#ifndef EXA_SIMENG_GPU_MEAS_BUILDER_H
#define EXA_SIMENG_GPU_MEAS_BUILDER_H

#include "gpu_meas.h"

/** @ingroup GpuMeas
  @{
*/

/** @brief Keeps a local & global count of the number of device meas cells in a device meas window */
class cDEVICE_MEAS_CELL_COUNTER
{
public:
  cDEVICE_MEAS_CELL_COUNTER(const sMEAS_WINDOW* w) : m_parent(w), m_local_host_meas_cells{}, m_n_local_meas_cells{0} {}

  void reset_local_counts();

  virtual std::pair<STP_MEAS_CELL_INDEX, sINT8> push_host_meas_cells(MEAS_CELL_VAR* mcv, uINT32 count)=0;
  virtual STP_MEAS_CELL_INDEX n_device_meas_cells() const =0;
  virtual bool needs_extra_reduction() const =0;
  virtual STP_MEAS_CELL_INDEX* reduction_idxs() const =0;
  virtual sDEVICE_REDUCE_METADATA* reduction_metadata(STP_MEAS_CELL_INDEX* reduction_idxs) const =0;
  virtual sSTD_CELL_VAR* megashob_std_cells() const = 0;
  virtual ~cDEVICE_MEAS_CELL_COUNTER()=0;

protected:
  std::pair<sINT8, bool> get_local_index(MEAS_CELL_VAR* mcv);

  const sMEAS_WINDOW* m_parent;
  std::array<MEAS_CELL_VAR*, N_VOXELS_64> m_local_host_meas_cells;
  size_t m_n_local_meas_cells;
};

/** @brief Makes sure that each megashob has its own device meas cell for repeatability.

  It is possible that multiple megashobs contribute to the same meas cell. Because each
  megashob is run separately on the GPU as its own thread block, we can't guarantee ordering
  of the summation if they use the same meas cell. So if we want repeatability, we have to
  give each megashob their own meas cell to work with, and then combine them later in a
  separate step. 

  This class provides the necessary reduction information for this extra step.
*/
class cDEVICE_MEAS_CELL_COUNTER_REPEATABLE: public cDEVICE_MEAS_CELL_COUNTER
{
public:
  cDEVICE_MEAS_CELL_COUNTER_REPEATABLE(const sMEAS_WINDOW* parent) : cDEVICE_MEAS_CELL_COUNTER(parent), 
  m_host_window_meas_cells{} {}
  std::pair<STP_MEAS_CELL_INDEX, sINT8> push_host_meas_cells(MEAS_CELL_VAR* mcv, uINT32 count) override;
protected:
  STP_MEAS_CELL_INDEX n_device_meas_cells() const override final;
  STP_MEAS_CELL_INDEX* reduction_idxs() const override final;
  sDEVICE_REDUCE_METADATA* reduction_metadata(STP_MEAS_CELL_INDEX* reduction_idxs) const override final;
  virtual sSTD_CELL_VAR* megashob_std_cells() const override final;
  bool needs_extra_reduction() const override final;

private:
  std::vector<STP_MEAS_CELL_INDEX> m_host_window_meas_cells;
  std::array<STP_MEAS_CELL_INDEX, N_VOXELS_64> m_local_device_meas_cells;
};

/** @brief Computes local reductions while ignoring global repeatability.

  This version computes local reductions within a megashob (for performance),
  while ignoring the possibility of multiple megashobs contributing to the
  same meas cell. No extra reduction step is needed for this window.
*/
class cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE : public cDEVICE_MEAS_CELL_COUNTER
{
public:
  cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE(const sMEAS_WINDOW* parent, bool create_stddev);

  std::pair<STP_MEAS_CELL_INDEX, sINT8> push_host_meas_cells(MEAS_CELL_VAR* mcv, uINT32 count) override;
protected:
  STP_MEAS_CELL_INDEX n_device_meas_cells() const override final;
  bool needs_extra_reduction() const override final;
  STP_MEAS_CELL_INDEX* reduction_idxs() const override final;
  sDEVICE_REDUCE_METADATA* reduction_metadata(STP_MEAS_CELL_INDEX* reduction_idxs) const override final;
  virtual sSTD_CELL_VAR* megashob_std_cells() const override final;
private:
  std::vector<sSTD_CELL_VAR> m_megashob_std_cells;
  bool m_create_stddev;
};

/** @brief Builds a standard fluid device window. 

  Can be built in repeatable or not repeatable mode, depending on the provided cDEVICE_MEAS_CELL_COUNTER.
*/
class cFLUID_DEVICE_MEAS_WINDOW_BUILDER : public cDEVICE_MEAS_WINDOW_BUILDER
{
public:
  cFLUID_DEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent, std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER>&& counter);

  int add_ublk_to_device_meas_cells(uINT8 child_ublk, SCALE scale, sUBLK_MEAS_CELL_PTR * const ublk_meas_cell_ptr, sUBLK_MEAS_CELL_PTR const * const end, bool mshob_is_moving) override;
  void append_mblk_meas_cells(std::vector<sMBLK_MEAS_CELL_PTR>& mcps) override;
  void compute_host_side_info() override;

private:
  std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER> m_counter;
  sMBLK_MEAS_CELL_PTR m_mcp;

  enum class eMBLK_TYPE
  {
    INVALID,
    VOXEL,
    UBLK
  };

  eMBLK_TYPE m_mblk_type;
  void add_voxel_device_meas_cell(uINT8 child_ublk, UBLK_MEAS_CELL_PTR ublk_meas_cell_ptr);
  void add_ublk_device_meas_cell(uINT8 child_ublk, UBLK_MEAS_CELL_PTR ublk_meas_cell_ptr);
};

/** @brief Builds a standard surface device window. 

  Can be built in repeatable or not repeatable mode, depending on the provided cDEVICE_MEAS_CELL_COUNTER.
*/
class cSURFACE_DEVICE_MEAS_WINDOW_BUILDER : public cDEVICE_MEAS_WINDOW_BUILDER
{
public:
  cSURFACE_DEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent, std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER>&& counter);

  int add_sfl_to_device_meas_cells(uINT8 child_sfl, sSURFEL_MEAS_CELL_PTR * const sfl_meas_cell_ptr, sSURFEL_MEAS_CELL_PTR const * const end, bool mshob_is_moving) override;
  void append_msfl_meas_cells(std::vector<sMSFL_MEAS_CELL_PTR>& mcps) override;
  void compute_host_side_info() override;
protected:
  std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER> m_counter;
  sMSFL_MEAS_CELL_PTR m_mcp;
};

/** @brief Builds a composite surface device window. 
  
  Composite windows would be too expensive to do in a repeatable fashion,
  because every meas cell would be duplicated for every megashob, so we
  just don't do it.
*/
class cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER : public cSURFACE_DEVICE_MEAS_WINDOW_BUILDER
{
public:
  cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER(sMEAS_WINDOW* parent);
};

/** @brief Builds a composite fluid device window. 
  
  Composite windows would be too expensive to do in a repeatable fashion,
  because every meas cell would be duplicated for every megashob, so we
  just don't do it.
*/
class cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER : public cFLUID_DEVICE_MEAS_WINDOW_BUILDER
{
public:
  cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER(sMEAS_WINDOW* parent);
};

/** @brief Builds a development surface device window. 

  Development windows would be far too expensive to do repeatably,
  (and almost impossible with lrfs spinning around too!), so we
  just don't do it.
*/
class cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER : public cDEVICE_MEAS_WINDOW_BUILDER
{
public:
  cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER(sMEAS_WINDOW* parent);

  int add_sfl_to_device_meas_cells(uINT8 child_sfl, sSURFEL_MEAS_CELL_PTR * const sfl_meas_cell_ptr, sSURFEL_MEAS_CELL_PTR const * const end, bool mshob_is_moving) override;
  void append_msfl_meas_cells(std::vector<sMSFL_MEAS_CELL_PTR>& mcps) override;
  void compute_host_side_info() override;
private:
  enum class eMSFL_IS_MOVING
  {
    INVALID,
    YES,
    NO
  };

  eMSFL_IS_MOVING m_msfl_is_moving;
  sMSFL_MEAS_CELL_PTR m_mcps[3];
  cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE m_counters[3];
  size_t m_num_mcps;
};

/** @brief Builds a development fluid device window. 

  Development windows would be far too expensive to do repeatably,
  (and almost impossible with lrfs spinning around too!), so we
  just don't do it.
*/
class cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER : public cDEVICE_MEAS_WINDOW_BUILDER
{
public:
  cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER(sMEAS_WINDOW* parent);

  int add_ublk_to_device_meas_cells(uINT8 child_ublk, 
                                    SCALE scale, 
                                    sUBLK_MEAS_CELL_PTR * const ublk_meas_cell_ptr,
                                    sUBLK_MEAS_CELL_PTR const * const end,
                                    bool mshob_is_moving) override;

  void append_mblk_meas_cells(std::vector<sMBLK_MEAS_CELL_PTR>& mcps) override;
  void compute_host_side_info() override;

private:
  enum class eMBLK_IS_MOVING
  {
    INVALID,
    YES,
    NO
  };

  eMBLK_IS_MOVING m_mblk_is_moving;
  std::vector<std::pair<uINT8,sUBLK_MEAS_CELL_PTR*>> m_ublk_mcps;
  std::vector<sMBLK_MEAS_CELL_PTR> m_mblk_mcps;
  std::vector<cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE> m_counters;
  std::vector<VOXEL_MASK_8> m_child_ublks_used;
};

/** @} */

#endif
