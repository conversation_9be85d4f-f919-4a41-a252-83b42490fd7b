--- HTML REPORTS -------------------------------------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/mrao/scarab/23678-ng6X-094-mrao-01/html/results_windows.html
  Linux:   file:///fs/qa/qmtest_rundirs/mrao/scarab/23678-ng6X-094-mrao-01/html/results_linux.html

--- INTERACTIVE HTML REPORT (Experimental) -----------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/mrao/scarab/23678-ng6X-094-mrao-01/html/exatest_report.html
  Linux:   file:///fs/qa/qmtest_rundirs/mrao/scarab/23678-ng6X-094-mrao-01/html/exatest_report.html

--- RUN DATA -----------------------------------------------------------------

  User ID:         mrao
  Host:            scarab
  Run Host(s):     scarab
  Start Time:      2019/04/01 15:31:30
  End Time:        2019/04/02 00:16:33
  Elapsed Time:    8 hour(s), 45 min(s), 2 sec(s)
  Distribution:    23678-ng6X-094-mrao-01
  Root Directory:  /fs/qa/tests/physics/collections/182-ng6X-03/basicPhysics/smokeTests
  Input Directory: /fs/qa/tests/physics/collections/182-ng6X-03/basicPhysics/smokeTests
  Run Directory:   /fs/qa/qmtest_rundirs/mrao/scarab/23678-ng6X-094-mrao-01
  Command:         exatest2 run -xk -i "main_cmd:-nprocs 4"

--- STATISTICS ---------------------------------------------------------------

     251        <USER> <GROUP>
       3 (  1%) tests ERROR
      28 ( 11%) tests FAIL
       8 (  3%) tests UNTESTED
     212 ( 84%) tests PASS

--- ERRORS AND FAILURES ------------------------------------------------------

  autostop/value_percent/ke_conf_ptot_conf      : FAIL    
    [main_cmd] Detected differences in 2 output file(s): monitor-1.dat,
    monitor-2.dat

  avg_fluid_ckpt/full_ckpt_resume/resume_on_diff_sps: ERROR   
    [middle_cmd] No such file or directory

  bit-for-bit/4P/thermalpassive_from_isotherm   : ERROR   
    [direct_sim] No such file or directory

  defrost/2d_inclined_osc                       : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s):
    water_vapor_mass_frac_history.lgraph, water_vapor_mass_frac_60.lgraph

  defrost/3d_acc_film_devel_w_defrost_time      : FAIL    
    [post_test_cmd] Detected differences in 9 output file(s):
    defrost_time.png, film_thickness.png, heat_flux.png, temperature.png,
    water_vapor_mass_flux.png, UDS-history_x0.15-y0.044-z0.05.lgraph,
    heat-flux-history_x0.1-y0.05-z0.05.lgraph,
    temperature-history_x0.1-y0.05-z0.05.lgraph,
    temperature-history_x0.15-y0.044-z0.05.lgraph

  defrost/coupled_compressible                  : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s):
    film_thickness.png, heat_flux.png, water_vapor_mass_flux.png,
    Film-thickness-history_x0.074-y0.036-z0.05.lgraph

  forceDev/nested_lrf                           : FAIL    
    [post_test_cmd] Detected differences in 3 output file(s): yvel_big.png,
    Force-dev_frame10.lgraph, Force-dev_frame20.lgraph

  measTests/checkpointResume/therm_cool         : FAIL    
    [post_test_cmd2] Detected differences in 1 output file(s): vmag.png

  measTests/checkpointResume/therm_cool_run_past_checkpoint: FAIL    
    [post_test_cmd2] Detected differences in 1 output file(s): vmag.png

  rotating_tire_tread/crooked                   : FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      vmag.png

  rotating_tire_tread/crooked_vr                : FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      vmag.png, x-vel.png

  rotating_tire_tread/resume_test               : FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

  rotating_tire_tread/tread_rotating            : FAIL    
    [main_cmd_tires] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 5 output file(s):
      density.png, pressure.png, temperature.png, vmag.png, x-vel.png

  rotating_tire_tread/tread_rotating_w_floor    : FAIL    
    [main_cmd_tires] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 6 output file(s):
      density.png, pressure.png, temperature.png, vmag.png, x-vel.png,
      tire.lgraph

  rotating_tire_tread/two_treads_floor_ramped_velocity: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 8 output file(s):
      density.png, pressure.png, temperature.png, total_pressure.png,
      vmag.png, x-vel.png, tire_front.lgraph, tread_front.lgraph

  seeding/rotate_seed_velocity                  : FAIL    
    [main_cmd] Command timed out.

  slidingMesh/globalRefFrame                    : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): den_x.png,
    den_y.png, tke_x.png, tke_z.png, xvel_x_body.png, xvel_y_body.png,
    zvel_x_ground.png, zvel_y_ground.png

  slidingMesh/nested_LRF/nested_not_align_not_colloc_noflow: FAIL    
    [main_cmd] Command timed out.

  slidingMesh/rotational_dynamics/noflow/diff_vr: FAIL    
    [main_cmd] Command timed out.

  slidingMesh/rotational_dynamics/noflow/external2: FAIL    
    [post_test_cmd] Detected differences in 3 output file(s): vmag2.png,
    z-vel2.png, z-vel1.png

  slidingMesh/rotational_dynamics/noflow/omega_1.0: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag_fan.png

  slidingMesh/solidXslrf                        : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    fluid-eddy.png, fluid-sij.png, fluid-tke.png, fluid-vmag.png,
    surf-htc.png, surf-vmag.png, fluid-eddy-xalign.lgraph,
    fluid-sij-xalign.lgraph, fluid-tke-xalign.lgraph, fluid-tke.lgraph,
    surf-cp-xalign.lgraph, surf-cp.lgraph, surf-vmag.lgraph

  snowconeV3                                    : FAIL    
    [post_test_cmd] Detected differences in 57 output file(s):
    surf_pt_f24_y.png, surf_pt_f64_z.png, surf_t_f24_x.png,
    surf_wheel_pt_f24_down.png, x-00-f64-p.png, x-00-f64-sij.png,
    x-00-f64-tdiss.png, x-00-f64-xvel.png, x-00-f88-t.png,
    x-00-f88-tdiss.png, x-00-f88-tke.png, x-1.6-f64-eddy.png,
    x-1.6-f64-pt.png, x-1.6-f64-tke.png, x-1.6-f88-sij.png, x-1.6-f88-t.png,
    x-1.6-f88-tdiss.png, x-1.6-f88-tke.png, x-1.6-f88-vmag.png,
    x-1.6-f88-xvel.png, y-045-f64-sij.png, y-045-f88-sij.png,
    y-045-f88-tdiss.png, y-045-f88-tke.png, y-045-f88-vmag.png,
    y-045-f88-zvel.png, y-m0125-f64-eddy.png, y-m0125-f88-eddy.png,
    y-m0125-f88-p.png, y-m0125-f88-pt.png, y-m0125-f88-sij.png,
    y-m0125-f88-t.png, y-m0125-f88-tdiss.png, y-m0125-f88-tke.png,
    y-m0125-f88-vmag.png, y-m0125-f88-xvel.png, y-m045-f64-pt.png,
    y-m045-f64-sij.png, y-m045-f64-tdiss.png, y-m045-f64-tke.png,
    y-m045-f64-vmag.png, y-m045-f64-xvel.png, y-m045-f88-pt.png,
    y-m045-f88-sij.png, y-m045-f88-tdiss.png, z-00-f24-sij.png,
    z-00-f24-tke.png, z-00-f64-pt.png, z-00-f64-sij.png, z-00-f64-t.png,
    z-00-f64-tdiss.png, z-00-f64-xvel.png, x-00-f64-tke.png,
    z-00-f24-vmag.png, z-00-f24-xvel.png, pt-tail.lgraph, xvel-tail.lgraph

  thermalBCs/2d_bump/accelerate_thermal_solver/always-on: FAIL    
    [post_test_cmd] Detected differences in 4 output file(s): density.png,
    static_p.png, temperature.png, total_p.png

  thermalBCs/2d_bump/accelerate_thermal_solver/on-off-on: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): static_p.png,
    total_p.png

  thermalBCs/2d_bump/baseline                   : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): static_p.png,
    total_p.png

  transonic/hybrid_transonic                    : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    total_pressure_lrf.png

  transonic/sliding_mesh/high_mach              : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s):
    static_pressure_lrf.png, static_pressure.png, x-vel.png, x-vel_lrf.png

  userDefinedSeeding/seed                       : ERROR   
    [main_cmd] No such file or directory

  variableCoupling/var_ptherm_coupling/infer_from_model: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): vmag_02.txt,
    vmag_03.txt

  vortSwirlLambda2/2D                           : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    pv-snap-fnc-vortz-y027.lgraph

--- TEST RESULTS -------------------------------------------------------------

  avg_fluid_ckpt/full_ckpt_resume/resume_on_diff_sps                                       : ERROR    (scarab)
  bit-for-bit/4P/thermalpassive_from_isotherm                                              : ERROR    (scarab)
  userDefinedSeeding/seed                                                                  : ERROR    (scarab)

  autostop/value_percent/ke_conf_ptot_conf                                                 : FAIL     (scarab)
  defrost/2d_inclined_osc                                                                  : FAIL     (scarab)
  defrost/3d_acc_film_devel_w_defrost_time                                                 : FAIL     (scarab)
  defrost/coupled_compressible                                                             : FAIL     (scarab)
  forceDev/nested_lrf                                                                      : FAIL     (scarab)
  measTests/checkpointResume/therm_cool                                                    : FAIL     (scarab)
  measTests/checkpointResume/therm_cool_run_past_checkpoint                                : FAIL     (scarab)
  rotating_tire_tread/crooked                                                              : FAIL     (scarab)
  rotating_tire_tread/crooked_vr                                                           : FAIL     (scarab)
  rotating_tire_tread/resume_test                                                          : FAIL     (scarab)
  rotating_tire_tread/tread_rotating                                                       : FAIL     (scarab)
  rotating_tire_tread/tread_rotating_w_floor                                               : FAIL     (scarab)
  rotating_tire_tread/two_treads_floor_ramped_velocity                                     : FAIL     (scarab)
  seeding/rotate_seed_velocity                                                             : FAIL     (scarab)
  slidingMesh/globalRefFrame                                                               : FAIL     (scarab)
  slidingMesh/nested_LRF/nested_not_align_not_colloc_noflow                                : FAIL     (scarab)
  slidingMesh/rotational_dynamics/noflow/diff_vr                                           : FAIL     (scarab)
  slidingMesh/rotational_dynamics/noflow/external2                                         : FAIL     (scarab)
  slidingMesh/rotational_dynamics/noflow/omega_1.0                                         : FAIL     (scarab)
  slidingMesh/solidXslrf                                                                   : FAIL     (scarab)
  snowconeV3                                                                               : FAIL     (scarab)
  thermalBCs/2d_bump/accelerate_thermal_solver/always-on                                   : FAIL     (scarab)
  thermalBCs/2d_bump/accelerate_thermal_solver/on-off-on                                   : FAIL     (scarab)
  thermalBCs/2d_bump/baseline                                                              : FAIL     (scarab)
  transonic/hybrid_transonic                                                               : FAIL     (scarab)
  transonic/sliding_mesh/high_mach                                                         : FAIL     (scarab)
  variableCoupling/var_ptherm_coupling/infer_from_model                                    : FAIL     (scarab)
  vortSwirlLambda2/2D                                                                      : FAIL     (scarab)

  2dAirfoils/11                                                                            : PASS     (scarab)
  2dAirfoils/2dGLCsurfVR                                                                   : PASS     (scarab)
  2dChannels/Re1_0e2-h40                                                                   : PASS     (scarab)
  2dTurbcyc                                                                                : PASS     (scarab)
  2dUbend                                                                                  : PASS     (scarab)
  APM/curvedAPM                                                                            : PASS     (scarab)
  APM/low_subsonic/mach_0.0                                                                : PASS     (scarab)
  APM/low_subsonic/mach_0.3                                                                : PASS     (scarab)
  APM/low_subsonic/mach_0.3_refl_damp                                                      : PASS     (scarab)
  APM/thermal_APM/t_ratio_1.2                                                              : PASS     (scarab)
  APM/thermal_APM/t_ratio_1.5                                                              : PASS     (scarab)
  LTT/NACA0012_AOA10_ltt                                                                   : PASS     (scarab)
  LTT/flat_plate_ltt                                                                       : PASS     (scarab)
  autostop/classic/cube_no_stop                                                            : PASS     (scarab)
  autostop/classic/cube_stop_0.005_s_char_time_1000                                        : PASS     (scarab)
  autostop/classic/cube_stop_0.005_s_char_time_2000                                        : PASS     (scarab)
  autostop/classic/cube_stop_0.005_subwin_6000                                             : PASS     (scarab)
  autostop/classic/cube_stop_0.01                                                          : PASS     (scarab)
  autostop/classic/cube_stop_var_grad_0.9                                                  : PASS     (scarab)
  autostop/classic/cube_two_monitors                                                       : PASS     (scarab)
  autostop/classic/x-vel_stop_later_it                                                     : PASS     (scarab)
  autostop/cool/four_mons                                                                  : PASS     (scarab)
  autostop/cool/heat_rejected                                                              : PASS     (scarab)
  autostop/cool/one_mon                                                                    : PASS     (scarab)
  autostop/mixed_monitor_types/surface_fluid_probe                                         : PASS     (scarab)
  autostop/percent/density/2d_baseline                                                     : PASS     (scarab)
  autostop/percent/density/simple_baseline                                                 : PASS     (scarab)
  autostop/percent/density/simple_symm_monitor                                             : PASS     (scarab)
  autostop/percent/density/simple_two_monitors                                             : PASS     (scarab)
  autostop/percent/pressure/simple_baseline                                                : PASS     (scarab)
  autostop/percent/pressure/simple_baseline_dont_stop                                      : PASS     (scarab)
  autostop/percent/x-vel/simple_baseline_2per_1stddev                                      : PASS     (scarab)
  autostop/percent/x-vel/simple_baseline_4per_2stddev                                      : PASS     (scarab)
  autostop/percent/x-vel/simple_stab_window_4000_sub_2000                                  : PASS     (scarab)
  autostop/percent/x-vel/simple_stop_on_second_monitor_only                                : PASS     (scarab)
  autostop/percent/x-vel/simple_two_monitors                                               : PASS     (scarab)
  autostop/porous/x-force                                                                  : PASS     (scarab)
  autostop/probes/cube_one_probe_one_regular                                               : PASS     (scarab)
  autostop/probes/cube_two_probes                                                          : PASS     (scarab)
  autostop/probes/surface_probes                                                           : PASS     (scarab)
  autostop/schemes/f1_lift_short_flow_pass                                                 : PASS     (scarab)
  autostop/surface/comp_by_face                                                            : PASS     (scarab)
  autostop/surface/mass_flux_by_face                                                       : PASS     (scarab)
  autostop/surface/plate_x-force_def_csys                                                  : PASS     (scarab)
  autostop/surface/plate_x-force_other_csys                                                : PASS     (scarab)
  autostop/therm/battery_cooling                                                           : PASS     (scarab)
  autostop/value/y-vel/chained_meas                                                        : PASS     (scarab)
  autostop/value/y-vel/conv_on_third_mon_only                                              : PASS     (scarab)
  autostop/value/y-vel/cube_two_monitors                                                   : PASS     (scarab)
  autostop/value/y-vel/cube_user_spec_it                                                   : PASS     (scarab)
  autostop/value/y-vel/stop_sim_it_plus_duration                                           : PASS     (scarab)
  autostop/value_percent/q_char_time_conf_pt_deficit_conf                                  : PASS     (scarab)
  autostop/value_percent/xvel_csys_vmag_flowpass                                           : PASS     (scarab)
  avg_fluid_ckpt/meas_options/meas_exclude                                                 : PASS     (scarab)
  avg_fluid_ckpt/meas_options/meas_include                                                 : PASS     (scarab)
  avg_fluid_ckpt/start_via_monitors/aligned_ckpt_period/full_and_mme_ckpt_at_end           : PASS     (scarab)
  avg_fluid_ckpt/start_via_monitors/aligned_ckpt_period/periodic_full_ckpt                 : PASS     (scarab)
  avg_fluid_ckpt/start_via_monitors/aligned_ckpt_period/periodic_mme_ckpt                  : PASS     (scarab)
  avg_fluid_ckpt/start_via_monitors/ckpt_after_DIT                                         : PASS     (scarab)
  avg_fluid_ckpt/start_via_monitors/ckpt_before_DIT                                        : PASS     (scarab)
  avg_fluid_ckpt/start_via_time/end_via_duration/VR                                        : PASS     (scarab)
  avg_fluid_ckpt/start_via_time/end_via_duration/noVR/period_greater_than_avg_int          : PASS     (scarab)
  avg_fluid_ckpt/start_via_time/end_via_duration/noVR/period_less_than_avg_int             : PASS     (scarab)
  avg_fluid_ckpt/start_via_time/end_via_specified                                          : PASS     (scarab)
  avg_fluid_ckpt/vel_freeze                                                                : PASS     (scarab)
  bumps/2D/attached                                                                        : PASS     (scarab)
  bumps/2D/separated                                                                       : PASS     (scarab)
  bumps/3D/attached                                                                        : PASS     (scarab)
  bumps/3D/separated                                                                       : PASS     (scarab)
  compression                                                                              : PASS     (scarab)
  couette                                                                                  : PASS     (scarab)
  defrost/2d_sliding                                                                       : PASS     (scarab)
  defrost/3d_no_acc                                                                        : PASS     (scarab)
  defrost/compressible                                                                     : PASS     (scarab)
  denso/encrypted_box                                                                      : PASS     (scarab)
  denso/invalid_encrypted_box                                                              : PASS     (scarab)
  denso/original_box                                                                       : PASS     (scarab)
  exa_signal_events/Exit                                                                   : PASS     (scarab)
  exa_signal_events/FullCkpt/ScheduledEarly                                                : PASS     (scarab)
  exa_signal_events/FullCkpt/ScheduledLate                                                 : PASS     (scarab)
  exa_signal_events/MMECkpt/ScheduledEarly                                                 : PASS     (scarab)
  exa_signal_events/MMECkpt/ScheduledLate                                                  : PASS     (scarab)
  exa_signal_events/SetMaxMinTemp                                                          : PASS     (scarab)
  exa_signal_events/SetMaxVel                                                              : PASS     (scarab)
  exa_signal_events/TableRead                                                              : PASS     (scarab)
  exa_signal_events/ThermalAcceleration/TurnOff                                            : PASS     (scarab)
  exa_signal_events/ThermalAcceleration/TurnOn                                             : PASS     (scarab)
  forceDev/slidingMeshPM/2D                                                                : PASS     (scarab)
  forceDev/slidingMeshPM/3D                                                                : PASS     (scarab)
  frozenMomentum/base_seed                                                                 : PASS     (scarab)
  frozenMomentum/base_seed_2d_channel                                                      : PASS     (scarab)
  frozenMomentum/thermal_timestep_ratio                                                    : PASS     (scarab)
  highSubsonic/2d-dns-nozzle/pback-8811                                                    : PASS     (scarab)
  highSubsonic/2d-dns-nozzle/pback-8859                                                    : PASS     (scarab)
  highSubsonic/2d-dns-nozzle/pback-89                                                      : PASS     (scarab)
  highSubsonic/2d-dns-nozzle/pback-92                                                      : PASS     (scarab)
  highSubsonic/2d-dns-nozzle/pback-9575                                                    : PASS     (scarab)
  highSubsonic/3d-dlr-f4                                                                   : PASS     (scarab)
  highSubsonic/total_temperature_SPS                                                       : PASS     (scarab)
  incompressibleSolver                                                                     : PASS     (scarab)
  lgi_to_fnc                                                                               : PASS     (scarab)
  mass_flow_and_flux/mass_flow_inlet/damping                                               : PASS     (scarab)
  mass_flow_and_flux/mass_flow_inlet/no_damping                                            : PASS     (scarab)
  mass_flow_and_flux/mass_flow_outlet/damping                                              : PASS     (scarab)
  mass_flow_and_flux/mass_flow_outlet/no_damping                                           : PASS     (scarab)
  mass_flow_and_flux/mass_flux_inlet/damping                                               : PASS     (scarab)
  mass_flow_and_flux/mass_flux_inlet/no_damping                                            : PASS     (scarab)
  mass_flow_and_flux/mass_flux_outlet/damping                                              : PASS     (scarab)
  mass_flow_and_flux/mass_flux_outlet/no_damping                                           : PASS     (scarab)
  mass_flow_and_flux/true_mass_flow_inlet/double                                           : PASS     (scarab)
  mass_flow_and_flux/true_mass_flow_inlet/single                                           : PASS     (scarab)
  mbcParticle                                                                              : PASS     (scarab)
  measTests/averagedMeasurement/autostop/cube_stop_0.005                                   : PASS     (scarab)
  measTests/averagedMeasurement/measavg                                                    : PASS     (scarab)
  measTests/averagedMeasurement/measavg_bysim                                              : PASS     (scarab)
  mrf/2dMrfTurb                                                                            : PASS     (scarab)
  mrf/3dCoupled                                                                            : PASS     (scarab)
  mrf/3dInclined                                                                           : PASS     (scarab)
  multiphase/droplet                                                                       : PASS     (scarab)
  multiphase/slug_between_plates                                                           : PASS     (scarab)
  non_air_fluid/ethyl_30                                                                   : PASS     (scarab)
  non_air_fluid/ethyl_50                                                                   : PASS     (scarab)
  non_air_fluid/fc77                                                                       : PASS     (scarab)
  non_air_fluid/user_defined                                                               : PASS     (scarab)
  non_air_fluid/water                                                                      : PASS     (scarab)
  normal_velocity_BC/constant                                                              : PASS     (scarab)
  normal_velocity_BC/oscillating_flow_rate_from_table                                      : PASS     (scarab)
  normal_velocity_BC/space_varying                                                         : PASS     (scarab)
  normal_velocity_BC/time_varying                                                          : PASS     (scarab)
  nu_over_t                                                                                : PASS     (scarab)
  particle_modelling/two_pmr_files_beam                                                    : PASS     (scarab)
  particle_modelling/two_pmr_files_gauss                                                   : PASS     (scarab)
  porous_media/3pm_closeSpace_nonAxisAligned                                               : PASS     (scarab)
  porous_media/curvedpm_adiabatic                                                          : PASS     (scarab)
  porous_media/curvedpm_prscrbd_temp                                                       : PASS     (scarab)
  porous_media/curvedpm_var_heat                                                           : PASS     (scarab)
  porous_media/curvedpm_wo_hTransfer                                                       : PASS     (scarab)
  porous_media/heat_exchanger_with_holes                                                   : PASS     (scarab)
  powercool/cac/singleHX_direct_Ynot_aligned_CAC_Sandwich_NoSuthCorr_with_heat_reject      : PASS     (scarab)
  powercool/cac/singleHX_turbulence_Xnot_aligned_CAC_Bilinear_YesSuthCorr_with_entry_temp  : PASS     (scarab)
  powercool/cac/singleHX_turbulence_Xnot_aligned_CAC_Sandwich_YesSuthCorr_with_heat_reject : PASS     (scarab)
  powercool/partition_tests/condenser                                                      : PASS     (scarab)
  powercool/partition_tests/radiator                                                       : PASS     (scarab)
  powercool/rad/singleHX_turbulence_111not_aligned_RAD_Sandwich_NoSuthCorr_with_entry_temp : PASS     (scarab)
  probes                                                                                   : PASS     (scarab)
  q_factor/q_factor_0.1                                                                    : PASS     (scarab)
  rotating_tire_tread/sq_cyl_annular                                                       : PASS     (scarab)
  rotating_tire_tread/stationary_sq_cyl                                                    : PASS     (scarab)
  seeding/mks_seeding/basic_seed                                                           : PASS     (scarab)
  seeding/mks_seeding/error_tests/diff_char_density_scaling                                : PASS     (scarab)
  seeding/mks_seeding/error_tests/diff_char_temp                                           : PASS     (scarab)
  seeding/mks_seeding/error_tests/high_subsonic                                            : PASS     (scarab)
  seeding/mks_seeding/error_tests/mach_number_set_by_user                                  : PASS     (scarab)
  seeding/mks_seeding/error_tests/max_expected_vel                                         : PASS     (scarab)
  seeding/mks_seeding/mks_seed                                                             : PASS     (scarab)
  seeding/seed_from_sample/inlets/mflux_inlet                                              : PASS     (scarab)
  seeding/seed_from_sample/inlets/p_inlet_fixed_dir                                        : PASS     (scarab)
  seeding/seed_from_sample/inlets/p_inlet_sampled_dir                                      : PASS     (scarab)
  seeding/seed_from_sample/inlets/p_trans_v_from_turb                                      : PASS     (scarab)
  seeding/seed_from_sample/inlets/p_v_inlet                                                : PASS     (scarab)
  seeding/seed_from_sample/inlets/pt_inlet_fixed_dir                                       : PASS     (scarab)
  seeding/seed_from_sample/inlets/pt_inlet_sampled_dir                                     : PASS     (scarab)
  seeding/seed_from_sample/inlets/trans_v_from_turb                                        : PASS     (scarab)
  seeding/seed_from_sample/inlets/v_inlet                                                  : PASS     (scarab)
  seeding/seed_from_sample/outlets/mflux_outlet                                            : PASS     (scarab)
  seeding/seed_from_sample/outlets/p_outlet_free                                           : PASS     (scarab)
  seeding/seed_from_sample/outlets/p_outlet_prescr_dir_fixed                               : PASS     (scarab)
  seeding/seed_from_sample/outlets/p_outlet_prescr_dir_sampled                             : PASS     (scarab)
  seeding/seed_from_sample/outlets/p_v_outlet                                              : PASS     (scarab)
  seeding/seed_from_sample/outlets/pt_outlet                                               : PASS     (scarab)
  seeding/seed_from_sample/outlets/v_outlet                                                : PASS     (scarab)
  sim_timing_tests/autostop_greatest_end_meas_time                                         : PASS     (scarab)
  sim_timing_tests/greatest_end_meas_time                                                  : PASS     (scarab)
  slidingMesh/annular_lrf                                                                  : PASS     (scarab)
  slidingMesh/nested_LRF/double_nested_lrf_noflow                                          : PASS     (scarab)
  slidingMesh/nested_LRF/grf_nested_lrf_noflow                                             : PASS     (scarab)
  slidingMesh/nested_LRF/nested_align_not_colloc_lrf_noflow                                : PASS     (scarab)
  slidingMesh/noVR_isothermal_05v_slrf                                                     : PASS     (scarab)
  slidingMesh/rotational_dynamics/base_test                                                : PASS     (scarab)
  slidingMesh/rotational_dynamics/base_timeaccurate                                        : PASS     (scarab)
  slidingMesh/rotational_dynamics/noflow/external_torque                                   : PASS     (scarab)
  slidingMesh/rotational_dynamics/noflow/stationary                                        : PASS     (scarab)
  synthetic_turbulence/differentBCs/PressVelDNS                                            : PASS     (scarab)
  synthetic_turbulence/differentBCs/PressVelDNSHT                                          : PASS     (scarab)
  synthetic_turbulence/differentBCs/TurbPressVel                                           : PASS     (scarab)
  synthetic_turbulence/differentBCs/TurbPressVelHT                                         : PASS     (scarab)
  synthetic_turbulence/differentBCs/TurbVel                                                : PASS     (scarab)
  synthetic_turbulence/differentBCs/TurbVelHT                                              : PASS     (scarab)
  synthetic_turbulence/differentBCs/VelDNS                                                 : PASS     (scarab)
  synthetic_turbulence/differentBCs/VelDNSHT                                               : PASS     (scarab)
  synthetic_turbulence/spectrum                                                            : PASS     (scarab)
  thermalBCs/q_channel                                                                     : PASS     (scarab)
  thermalBCs/t_channel                                                                     : PASS     (scarab)
  thermalBCs/water/0degLB                                                                  : PASS     (scarab)
  thermalBCs/water/0degPDE                                                                 : PASS     (scarab)
  thermalBCs/water/15degLB                                                                 : PASS     (scarab)
  thermalBCs/water/15degPDE                                                                : PASS     (scarab)
  thermalBCs/water/15degTA                                                                 : PASS     (scarab)
  transonic/3d-dlr                                                                         : PASS     (scarab)
  transonic/back_p_0.3                                                                     : PASS     (scarab)
  transonic/back_p_0.73                                                                    : PASS     (scarab)
  transonic/back_p_0.92                                                                    : PASS     (scarab)
  transonic/sliding_mesh/low_mach                                                          : PASS     (scarab)
  transonic/total_t_total_p_inflow                                                         : PASS     (scarab)
  transonic/total_temperature_SPS                                                          : PASS     (scarab)
  userDefinedSeeding/bseed_exclude                                                         : PASS     (scarab)
  userDefinedSeeding/bseed_include                                                         : PASS     (scarab)
  userDefinedSeeding/seed_exclude_region                                                   : PASS     (scarab)
  variableCoupling/var_ptherm_coupling/steady_import_delay                                 : PASS     (scarab)
  variableCoupling/var_ptherm_coupling/transient                                           : PASS     (scarab)
  velocityWallBC/2D                                                                        : PASS     (scarab)
  vortSwirlLambda2/3D                                                                      : PASS     (scarab)

  autostop/percent/x-vel/pseudo_monoton_stab_window_1000                                   : UNTESTED (scarab)
  autostop/percent/x-vel/pseudo_monoton_stab_window_1000_sub_500                           : UNTESTED (scarab)
  autostop/percent/x-vel/simple_monoton                                                    : UNTESTED (scarab)
  htcXRA/adiabatic                                                                         : UNTESTED (scarab)
  htcXRA/isothermal                                                                        : UNTESTED (scarab)
  q_factor/q_factor_0                                                                      : UNTESTED (scarab)
  seeding/seed_from_sample/inlets/input                                                    : UNTESTED (scarab)
  seeding/seed_from_sample/outlets/input                                                   : UNTESTED (scarab)
