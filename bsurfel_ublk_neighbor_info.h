#ifndef BSURFEL_UBLK_NEIGHBOR_INFO_H
#define BSURFEL_UBLK_NEIGHBOR_INFO_H

#include "ublk.h"

//-----------------------------------------------------------
// sBSURFEL_UBLK_NEIGHBOR_INFO STRUCT
//
// This struct holds the interpolation weights and distribution
// weights for a bsurfel/voxel pair. This should be a very
// lightweight object.
//-----------------------------------------------------------

struct sBSURFEL_UBLK_NEIGHBOR_INFO {
private:
  sUBLK *m_ublk;
  uINT8  m_voxel;
  dFLOAT m_interpolation_weight;
  dFLOAT m_distribution_weight;

public:

  friend std::ostream & operator << (std::ostream& os, sBSURFEL_UBLK_NEIGHBOR_INFO& b_info);

  // constructor
  sBSURFEL_UBLK_NEIGHBOR_INFO(UBLK ublk, uINT8 voxel, dFLOAT iweight, dFLOAT dweight) :
    m_ublk(ublk), m_voxel(voxel), m_interpolation_weight(iweight), m_distribution_weight(dweight) {}

  UBLK ublk() { return m_ublk; }

  uINT8 voxel() { return m_voxel; }

  dFLOAT interpolation_weight() { return m_interpolation_weight; }

  dFLOAT distribution_weight() { return m_distribution_weight; }

  void add_bsurfel_contribution_to_ublk(dFLOAT computed_body_force[3], dFLOAT computed_body_force_pfld[3], STP_DGEOM_VARIABLE area)
  {
    m_ublk->add_bsurfel_contribution(m_voxel, m_distribution_weight, computed_body_force, computed_body_force_pfld, area);
  }

  dFLOAT normalizing_weight() { return m_ublk->m_ib_bf->m_weight[m_voxel]; }

  dFLOAT voxel_ratio(asINT32 bsurfel_scale)
  {
    return static_cast<dFLOAT>(scale_to_voxel_size(m_ublk->scale())) / static_cast<dFLOAT>(scale_to_voxel_size(bsurfel_scale));
  }

  dFLOAT voxel_volume(dFLOAT _voxel_ratio)
  {
    return _voxel_ratio * _voxel_ratio * (sim.is_2d() ? 1.0F : _voxel_ratio);
  }

  sdFLOAT lb_next_density()
  {
    asINT32 lb_prior_index = g_timescale.m_lb_tm.prior_timestep_index(m_ublk->scale());
    asINT32 lb_next_index = lb_prior_index ^ 1;
    return m_ublk->lb_data()->m_lb_data[lb_next_index].density[m_voxel];
  }

  dFLOAT pfluid() { return m_ublk->is_near_surface() ? m_ublk->surf_geom_data()->pfluids[m_voxel] : 1.0; }

  struct sINTERPOLATE_FLUID_PROPERTIES_HELPER {
    asINT32 lb_prior_index;
    asINT32 lb_next_index;
    asINT32 ke_prior_index;
    asINT32 ke_next_index;
    asINT32 t_prior_index;
    asINT32 t_next_index;
    asINT32 uds_prior_index;
    asINT32 uds_next_index;

    sINTERPOLATE_FLUID_PROPERTIES_HELPER(sUBLK* ublk)
    {
      lb_prior_index = g_timescale.m_lb_tm.prior_timestep_index(ublk->scale());
      lb_next_index = lb_prior_index ^ 1 ;
      ke_prior_index = g_timescale.m_ke_pde_tm.prior_timestep_index(ublk->scale());
      ke_next_index = ke_prior_index ^ 1;
      t_prior_index = g_timescale.m_t_pde_tm.prior_timestep_index(ublk->scale());
      t_next_index = sim_next_index(t_prior_index);
      uds_prior_index = g_timescale.m_uds_pde_tm.prior_timestep_index(ublk->scale());
      uds_next_index = sim_next_index(uds_prior_index);
    }
  };

  sINTERPOLATE_FLUID_PROPERTIES_HELPER interpolate_fluid_properties_helper() { return sINTERPOLATE_FLUID_PROPERTIES_HELPER(m_ublk); }

  sdFLOAT lb_next_vel(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h, int axis)
  {
    return m_ublk->lb_data()->m_lb_data[h.lb_next_index].vel[axis][m_voxel];
  }

  sdFLOAT lb_prior_vel(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h, int axis)
  {
    return m_ublk->lb_data()->m_lb_data[h.lb_prior_index].vel[axis][m_voxel];
  }

  sdFLOAT lb_next_density(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->lb_data()->m_lb_data[h.lb_next_index].density[m_voxel];
  }

  sdFLOAT lb_prior_density(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->lb_data()->m_lb_data[h.lb_prior_index].density[m_voxel];
  }

  sdFLOAT t_next_fluid_temp(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->t_data()->fluid_temp[h.t_next_index][m_voxel];
  }

  sdFLOAT uds_next_uds_value(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h, int nth_uds)
  {
    return m_ublk->uds_data(nth_uds)->uds[h.uds_next_index][m_voxel];
  }

  sdFLOAT ke_prior_turb_ke(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->turb_data()->m_turb_data[h.ke_prior_index].turb_ke[m_voxel];
  }

  sdFLOAT ke_prior_turb_df(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->turb_data()->m_turb_data[h.ke_prior_index].turb_df[m_voxel];
  }

  sdFLOAT pf_next_vel(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h, int axis)
  {
    return m_ublk->pf_data()->m_pf_data[h.uds_next_index].vel_pfld[axis][m_voxel];
    //return m_ublk->pf_data()->m_pf_data[h.uds_prior_index].vel_pfld[axis][m_voxel];
  }
  sdFLOAT pf_prior_pressure_pfld(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h)
  {
    return m_ublk->pf_data()->m_pf_data[h.uds_prior_index].pressure_pfld[m_voxel];
  }

};

#endif
