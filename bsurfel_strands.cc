/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 
/*--------------------------------------------------------------------------*
 * Strands that operate on bsurfels
 *
 * Vinit Gupta, Exa Corporation
 * Created Fri Sep 21, 2017
 *--------------------------------------------------------------------------*/

#include "shob_groups.h"
#include "strand_mgr.h"
#include "strands.h"
#include "bsurfel_dyn_sp.h"
#include "bsurfel_comm.h"

VOID sFRINGE_BSURFELS_STRAND::run()
{
  if (sim.is_movb_sim()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    SCALE finest_scale = FINEST_SCALE;
    sBSURFEL_PROCESS_CONTROL bsurfel_process_control;
    for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
      bsurfel_process_control.init(scale);
      bsurfel_process_control.process_all_bsurfels(m_index, FRINGE_BSURFEL_GROUP_TYPE);
      if (scale != finest_scale) {
        g_strand_mgr.update_consumer_counts(scale+1);
      }
    }
    g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale);

    // These two loops MUST remain separated
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      request_ublk_ib_bf_sends(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}

VOID sFRINGE2_BSURFELS_STRAND::run()
{
  if (sim.is_movb_sim()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    sBSURFEL_PROCESS_CONTROL bsurfel_process_control;
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      bsurfel_process_control.init(scale);
      bsurfel_process_control.process_all_bsurfels(m_index, FRINGE2_BSURFEL_GROUP_TYPE);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}

VOID sINTERIOR_BSURFELS_STRAND::run()
{
  if (sim.is_movb_sim()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    sBSURFEL_PROCESS_CONTROL bsurfel_process_control;
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      bsurfel_process_control.init(scale);
      bsurfel_process_control.process_all_bsurfels(m_index, INTERIOR_BSURFEL_GROUP_TYPE);
    }
  }
  g_strand_mgr.complete_timestep();
}
