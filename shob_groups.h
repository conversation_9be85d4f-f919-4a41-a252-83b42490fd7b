/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef _SHOB_GROUPS_H_
#define _SHOB_GROUPS_H_

#include "fset.h"
#include "surfel.h"
#include "surfel_table.h"

#include VMEM_VECTOR_H

#define ADD_SHOB_IN_ID_ORDER 0
// forward declarations
struct sSP_SEND_GROUP_BASE;
// struct sSURFEL_SEND_GROUP;
// struct sWSURFEL_SEND_GROUP;

enum SP_TIMER_TYPE;

enum UBLK_GROUP_TYPE {
  INVALID_UBLK_GROUP_TYPE      = -1,

  FRINGE_FARBLK_GROUP_TYPE,
  FRINGE_NEARBLK_GROUP_TYPE,

  FRINGE2_FARBLK_GROUP_TYPE,
  FRINGE2_NEARBLK_GROUP_TYPE,

  SLIDING_NEARBLK_GROUP_TYPE,        // Near ublks that interact with mlrf surfels

  INTERIOR_FARBLK2_GROUP_TYPE,       // interior far ublks with 2 sets of LB states
  INTERIOR_FARBLK1_GROUP_TYPE,       // interior far ublks with 1 set of LB states
  INTERIOR_NEARBLK_GROUP_TYPE,       // interior near ublks

  VRFINE_FRINGE_FARBLK_GROUP_TYPE,   // VR fine far ublks on the fringe
  VRFINE_FRINGE_NEARBLK_GROUP_TYPE,  // VR fine near ublks on the fringe

  VRFINE_FRINGE2_FARBLK_GROUP_TYPE,  // VR fine far ublks in fringe2 layer
  VRFINE_FRINGE2_NEARBLK_GROUP_TYPE, // VR fine near ublks in fringe2 layer

  VRFINE_SLIDING_NEARBLK_GROUP_TYPE, // VR fine Near ublks that interact with mlrf surfels

  VRFINE_FARBLK_GROUP_TYPE,          // interior VR fine far ublks that use PDE ADVECT
  VRFINE_NEARBLK_GROUP_TYPE,         // interior VR fine near ublks that use PDE ADVECT
 
  MIRROR_UBLK_GROUP_TYPE,            // DO NOT NEED MIRROR GROUP

  VRFINE_GHOSTBLK_GROUP_TYPE,        // VR fine ublks underlying coarse ghost ublks

  GHOST_UBLK_GROUP_TYPE,
  N_UBLK_GROUP_TYPES
};

BOOLEAN _INLINE_ __HOST__DEVICE__ is_non_vr_nearblock_group_type(UBLK_GROUP_TYPE gtype) {
  return (gtype == FRINGE_NEARBLK_GROUP_TYPE) ||
	 (gtype == FRINGE2_NEARBLK_GROUP_TYPE) ||
	 (gtype == SLIDING_NEARBLK_GROUP_TYPE) ||
	 (gtype == INTERIOR_NEARBLK_GROUP_TYPE);
}

BOOLEAN _INLINE_ __HOST__DEVICE__ is_vrfine_nearblock_group_type(UBLK_GROUP_TYPE gtype) {
  return (gtype == VRFINE_NEARBLK_GROUP_TYPE) ||
	 (gtype == VRFINE_FRINGE_NEARBLK_GROUP_TYPE) ||
	 (gtype == VRFINE_FRINGE2_NEARBLK_GROUP_TYPE) ||
         (gtype == VRFINE_SLIDING_NEARBLK_GROUP_TYPE);
}

BOOLEAN _INLINE_ __HOST__DEVICE__ is_nearblock_group_type(UBLK_GROUP_TYPE gtype) {
  return is_non_vr_nearblock_group_type(gtype) ||
         is_vrfine_nearblock_group_type(gtype);
}

BOOLEAN _INLINE_ __HOST__DEVICE__ is_non_vr_farblock_group_type(UBLK_GROUP_TYPE gtype) {
  return (gtype == INTERIOR_FARBLK1_GROUP_TYPE) ||
	 (gtype == INTERIOR_FARBLK2_GROUP_TYPE) ||
         (gtype == FRINGE_FARBLK_GROUP_TYPE) ||
         (gtype == FRINGE2_FARBLK_GROUP_TYPE);  
}

BOOLEAN _INLINE_ __HOST__DEVICE__ is_vrfine_farblock_group_type(UBLK_GROUP_TYPE gtype) {
  return (gtype == VRFINE_FARBLK_GROUP_TYPE) ||
	 (gtype == VRFINE_FRINGE_FARBLK_GROUP_TYPE) ||
         (gtype == VRFINE_FRINGE2_FARBLK_GROUP_TYPE);
}

BOOLEAN _INLINE_ __HOST__DEVICE__ is_farblock_group_type(UBLK_GROUP_TYPE gtype) {
  return is_non_vr_farblock_group_type(gtype) ||
         is_vrfine_farblock_group_type(gtype);
}

extern const char* g_ublk_group_type_names[N_UBLK_GROUP_TYPES]; 

// Though it has "ghost" in the name, VRFINE_GHOST_UBLK_GROUP_TYPE is included in
// this, because VRFINE_GHOST_UBLK_GROUPs are organized in UBLK_FSETs

// #define N_NON_GHOST_UBLK_GROUP_TYPES (N_UBLK_GROUP_TYPES - 1)

// FRINGE2 surfels are now just FRINGE surfels with a dest_sp of DEST_SP_PROCESS_LAST

// Note that n_dest_sps must always be less than STP_MAX_PROCS, because the SP itself is not a possible destination

#define DEST_SP_PROCESS_LAST STP_MAX_PROC_ID

// Base groups clasifies surfels based on the location with respect to the fringe, independently of their actualy type
enum SURFEL_BASE_GROUP_TYPE {
  INVALID_SURFEL_BASE_GROUP_TYPE      = -1,
  FRINGE_SURFEL_BASE_GROUP_TYPE,
  FRINGE2_SURFEL_BASE_GROUP_TYPE,
  INTERIOR_SURFEL_BASE_GROUP_TYPE,

  N_SURFEL_BASE_GROUP_TYPES
};

// For each FSET type (one per group class definition), following enums indicate the group types they contain
enum SURFEL_GROUP_TYPE {
  INVALID_SURFEL_GROUP_TYPE      = -1,

  // These first 3 need to match SURFEL_BASE_GROUP_TYPE for the GPU
  FRINGE_FLOW_DYN_SURFEL_GROUP_TYPE,
  FRINGE2_FLOW_DYN_SURFEL_GROUP_TYPE,
  INTERIOR_FLOW_DYN_SURFEL_GROUP_TYPE,

  FRINGE_FLOW_WSURFEL_GROUP_TYPE,
  FRINGE2_FLOW_WSURFEL_GROUP_TYPE,
  INTERIOR_FLOW_WSURFEL_GROUP_TYPE,
  
  //Conduction
  FRINGE_CONDUCTION_DYN_SURFEL_GROUP_TYPE,
  FRINGE2_CONDUCTION_DYN_SURFEL_GROUP_TYPE,
  INTERIOR_CONDUCTION_DYN_SURFEL_GROUP_TYPE,

  FRINGE_CONDUCTION_WSURFEL_GROUP_TYPE,
  FRINGE2_CONDUCTION_WSURFEL_GROUP_TYPE,
  INTERIOR_CONDUCTION_WSURFEL_GROUP_TYPE,
  
  //Other
  MLRF_SURFEL_GROUP_TYPE,

  MIRROR_SURFEL_GROUP_TYPE,
  GHOST_SURFEL_GROUP_TYPE,
  N_SURFEL_GROUP_TYPES
};

extern const char* g_surfel_group_type_names[N_SURFEL_GROUP_TYPES]; 

#define N_NON_GHOST_SURFEL_GROUP_TYPES (N_SURFEL_GROUP_TYPES - 1)

enum CONTACT_SURFEL_GROUP_TYPE {
  INVALID_CONTACT_SURFEL_GLOBAL_GROUP_TYPE = -1,
  FRINGE_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
  FRINGE_CONTACT_SURFEL_LOCAL_GROUP_TYPE,
  FRINGE2_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
  FRINGE2_CONTACT_SURFEL_LOCAL_GROUP_TYPE,
  INTERIOR_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
  INTERIOR_CONTACT_SURFEL_LOCAL_GROUP_TYPE,
  N_CONTACT_SURFEL_GROUP_TYPES
};

enum SAMPLING_SURFEL_GROUP_TYPE {
  INVALID_SAMPLING_SURFEL_GROUP_TYPE      = -1,
  FRINGE_SAMPLING_SURFEL_GROUP_TYPE,
  FRINGE2_SAMPLING_SURFEL_GROUP_TYPE,
  INTERIOR_SAMPLING_SURFEL_GROUP_TYPE,
  N_SAMPLING_SURFEL_GROUP_TYPES
};

enum SURFEL_PAIR_GROUP_TYPE {
  INVALID_SURFEL_PAIR_GROUP_TYPE      = -1,

  FRINGE_APM_SURFEL_PAIR_GROUP_TYPE,
  FRINGE_SLRF_SURFEL_PAIR_GROUP_TYPE,
  
  FRINGE2_APM_SURFEL_PAIR_GROUP_TYPE,
  FRINGE2_SLRF_SURFEL_PAIR_GROUP_TYPE,
  
  INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE,
  INTERIOR_SLRF_SURFEL_PAIR_GROUP_TYPE,
  
  N_SURFEL_PAIR_GROUP_TYPES
};

enum BSURFEL_GROUP_TYPE {
  INVALID_BSURFEL_GROUP_TYPE = -1,
  FRINGE_BSURFEL_GROUP_TYPE,
  FRINGE2_BSURFEL_GROUP_TYPE,
  INTERIOR_BSURFEL_GROUP_TYPE,
  N_BSURFEL_GROUP_TYPES
};


// These are called supertypes because they are less specific than the surfel
// group types; for example, APM_SURFEL_PAIR_GROUP_SUPERTYPE incorporates FRINGE_APM_SURFEL_PAIR_GROUP_TYPE,
// FRINGE2_APM_SURFEL_PAIR_GROUP_TYPE, and INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE.

enum SURFEL_GROUP_SUPERTYPE {
  INVALID_SURFEL_GROUP_SUPERTYPE      = -1,
  //Flow
  FLOW_DYN_SURFEL_GROUP_SUPERTYPE,
  FLOW_WSURFEL_GROUP_SUPERTYPE,
  SLRF_SURFEL_PAIR_GROUP_SUPERTYPE,
  APM_SURFEL_PAIR_GROUP_SUPERTYPE,
  //Conduction
  CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE,
  CONDUCTION_WSURFEL_GROUP_SUPERTYPE,
  CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE,
  CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE,
  //Other
  MLRF_SURFEL_GROUP_SUPERTYPE,
  SAMPLING_SURFEL_GROUP_SUPERTYPE,
  N_SURFEL_GROUP_SUPERTYPES
};


namespace  nSHOB_CATEGORIES {
  // nvcc doesn't yet support template argument deduction,
  // so we have to use this function instead
  template<typename T, typename... U>
  constexpr auto make_array(T t, U&&... u) {
    return std::array<T,1+sizeof...(U)>{t, u...};
  }

  inline constexpr auto FARBLK_TYPES = make_array(
    FRINGE_FARBLK_GROUP_TYPE,
    FRINGE2_FARBLK_GROUP_TYPE,
    INTERIOR_FARBLK1_GROUP_TYPE,
    INTERIOR_FARBLK2_GROUP_TYPE
  );

  inline constexpr asINT32 N_FARBLK_TYPES = FARBLK_TYPES.size();

  inline constexpr auto NEARBLK_TYPES = make_array(
    FRINGE_NEARBLK_GROUP_TYPE,
    FRINGE2_NEARBLK_GROUP_TYPE,
    INTERIOR_NEARBLK_GROUP_TYPE,
    SLIDING_NEARBLK_GROUP_TYPE
  );

  inline constexpr asINT32 N_NEARBLK_TYPES = NEARBLK_TYPES.size();

  inline constexpr auto VRFINE_TYPES = make_array(
    VRFINE_FRINGE_FARBLK_GROUP_TYPE,
    VRFINE_FRINGE_NEARBLK_GROUP_TYPE,
    VRFINE_FRINGE2_NEARBLK_GROUP_TYPE,
    VRFINE_SLIDING_NEARBLK_GROUP_TYPE,
    VRFINE_FARBLK_GROUP_TYPE,
    VRFINE_NEARBLK_GROUP_TYPE
  );

  inline constexpr asINT32 N_VRFINE_TYPES = VRFINE_TYPES.size();

  inline constexpr auto VRFINE_NEAR_TYPES = make_array(
    VRFINE_FRINGE_NEARBLK_GROUP_TYPE,
    VRFINE_FRINGE2_NEARBLK_GROUP_TYPE,
    VRFINE_NEARBLK_GROUP_TYPE,
    VRFINE_SLIDING_NEARBLK_GROUP_TYPE
  );

  inline constexpr asINT32 N_VRFINE_NEAR_TYPES = VRFINE_NEAR_TYPES.size();

  inline constexpr auto VRFINE_FAR_TYPES = make_array(
    VRFINE_FRINGE_FARBLK_GROUP_TYPE,
    VRFINE_FARBLK_GROUP_TYPE
  );

  inline constexpr asINT32 N_VRFINE_FAR_TYPES = VRFINE_FAR_TYPES.size();

  inline constexpr auto VRFINE_GHOST_TYPES = make_array(
    VRFINE_GHOSTBLK_GROUP_TYPE
  );

  inline constexpr asINT32 N_VRFINE_GHOST_TYPES = VRFINE_GHOST_TYPES.size();

  inline constexpr auto VRFINE_NO_COMM_TYPES = make_array(
    VRFINE_FRINGE2_NEARBLK_GROUP_TYPE,
    VRFINE_SLIDING_NEARBLK_GROUP_TYPE,
    VRFINE_FARBLK_GROUP_TYPE,
    VRFINE_NEARBLK_GROUP_TYPE
  );

  inline constexpr auto DYN_SURFEL_FLOW_TYPES = make_array(
    FRINGE_FLOW_DYN_SURFEL_GROUP_TYPE,
    FRINGE2_FLOW_DYN_SURFEL_GROUP_TYPE,
    INTERIOR_FLOW_DYN_SURFEL_GROUP_TYPE
  );

  inline constexpr asINT32 N_DYN_SURFEL_FLOW_TYPES = DYN_SURFEL_FLOW_TYPES.size();
  
  inline constexpr auto DYN_SURFEL_CONDUCTION_TYPES = make_array(
    FRINGE_CONDUCTION_DYN_SURFEL_GROUP_TYPE,
    FRINGE2_CONDUCTION_DYN_SURFEL_GROUP_TYPE,
    INTERIOR_CONDUCTION_DYN_SURFEL_GROUP_TYPE
  );

  inline constexpr asINT32 N_DYN_SURFEL_CONDUCTION_TYPES = DYN_SURFEL_CONDUCTION_TYPES.size();
  
  inline constexpr auto SAMPLING_SURFEL_TYPES = make_array(
    FRINGE_SAMPLING_SURFEL_GROUP_TYPE,
    FRINGE2_SAMPLING_SURFEL_GROUP_TYPE,
    INTERIOR_SAMPLING_SURFEL_GROUP_TYPE
  );

  inline constexpr asINT32 N_SSURFEL_TYPES = SAMPLING_SURFEL_TYPES.size();
  
  // CONDUCTION-TODO: Change these to APM_SURFEL_PAIR_TYPES like above
  inline constexpr auto ISURFEL_PAIR_TYPES = make_array(
    FRINGE_APM_SURFEL_PAIR_GROUP_TYPE,
    FRINGE2_APM_SURFEL_PAIR_GROUP_TYPE,
    INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE
  );

  inline constexpr asINT32 N_ISURFEL_PAIR_TYPES = ISURFEL_PAIR_TYPES.size();

  inline constexpr auto WSURFEL_CONDUCTION_TYPES = make_array(
    FRINGE_CONDUCTION_WSURFEL_GROUP_TYPE,
    FRINGE2_CONDUCTION_WSURFEL_GROUP_TYPE,
    INTERIOR_CONDUCTION_WSURFEL_GROUP_TYPE
  );

  inline constexpr asINT32 N_WSURFEL_CONDUCTION_TYPES = WSURFEL_CONDUCTION_TYPES.size();
  
  inline constexpr auto WSURFEL_FLOW_TYPES = make_array(
    FRINGE_FLOW_WSURFEL_GROUP_TYPE,
    FRINGE2_FLOW_WSURFEL_GROUP_TYPE,
    INTERIOR_FLOW_WSURFEL_GROUP_TYPE
  );

  inline constexpr asINT32 N_WSURFEL_FLOW_TYPES = WSURFEL_FLOW_TYPES.size();
  
  // Only distinction between global and local contact surfels is done in
  // surface process control, since global are processed by wstrands while
  // local are processed in the regular surfel strands.  However, the groups
  // are distinguished by their supertype as the base group is traversed.  Rest
  // of locations where contact groups are traversed specifically using their
  // macros do not impose any distinction whether global or local, so lumped
  // here in a single list so we only need one macro to traverse all contact
  // surfels

  //Arranges FRINGE2 first so when looping through types in seeding, fringe2 are the first ones processed
  inline constexpr auto CONTACT_SURFEL_TYPES = make_array(
    FRINGE2_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
    FRINGE2_CONTACT_SURFEL_LOCAL_GROUP_TYPE,
    FRINGE_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
    FRINGE_CONTACT_SURFEL_LOCAL_GROUP_TYPE,
    INTERIOR_CONTACT_SURFEL_GLOBAL_GROUP_TYPE,
    INTERIOR_CONTACT_SURFEL_LOCAL_GROUP_TYPE
  );

  inline constexpr asINT32 N_CONTACT_SURFEL_TYPES = CONTACT_SURFEL_TYPES.size();

  inline constexpr auto SLRF_SURFEL_PAIR_TYPES = make_array(
    FRINGE_SLRF_SURFEL_PAIR_GROUP_TYPE,
    FRINGE2_SLRF_SURFEL_PAIR_GROUP_TYPE,
    INTERIOR_SLRF_SURFEL_PAIR_GROUP_TYPE
  );

  inline constexpr asINT32 N_SLRF_SURFEL_PAIR_TYPES = SLRF_SURFEL_PAIR_TYPES.size();

  // const static asINT32 N_MLRF_SURFEL_TYPES = 2;
  // asINT32 MLRF_SURFEL_TYPES[N_MLRF_SURFEL_TYPES];

  inline constexpr auto BSURFEL_TYPES = make_array(
    FRINGE_BSURFEL_GROUP_TYPE,
    FRINGE2_BSURFEL_GROUP_TYPE,
    INTERIOR_BSURFEL_GROUP_TYPE
  );

  // if I follow this pattern, the result is N_BSURFEL_TYPES,
  // which conflicts with a macro defined in stp.h
  inline constexpr asINT32 N_BSURFEL_TYPES_ = BSURFEL_TYPES.size();

  inline int vr_fine_type_index(UBLK_GROUP_TYPE g)
  {
    ccDOTIMES(i, N_VRFINE_TYPES) {
      if (g == VRFINE_TYPES[i]) {
        return i;
      }
    }

    msg_internal_error("Cannot find matching vr fine_group type for group %d",g);
    return -1;
  }

};

extern BOOLEAN g_ublk_type_has_ghosts[N_UBLK_GROUP_TYPES];

inline BOOLEAN is_fringe(UBLK_GROUP_TYPE gtype) {
  switch (gtype) {
  case FRINGE_FARBLK_GROUP_TYPE:
  case FRINGE_NEARBLK_GROUP_TYPE:  
  case VRFINE_FRINGE_FARBLK_GROUP_TYPE:
  case VRFINE_FRINGE_NEARBLK_GROUP_TYPE:
    return TRUE;
  default:
    return FALSE;
  } 
  return FALSE; // to make the compiler shut up
}

inline BOOLEAN is_fringe(SURFEL_GROUP_TYPE gtype) {
  switch (gtype) {
  case FRINGE_FLOW_DYN_SURFEL_GROUP_TYPE:
  case FRINGE_CONDUCTION_DYN_SURFEL_GROUP_TYPE:
    return TRUE;
  default:
    return FALSE;
  }
  return FALSE; // to make the compiler shut up
}

template<typename sSHOB_TYPE>
struct tSHOB_GROUP_ITERATOR {

  using iterator_category = std::bidirectional_iterator_tag;
  using difference_type = std::ptrdiff_t;
  using value_type = sSHOB_TYPE;
  using pointer = sSHOB_TYPE*;
  using reference = sSHOB_TYPE&;

  //constructor
  tSHOB_GROUP_ITERATOR(sSHOB_TYPE* ptr):m_ptr(ptr) {}

  //operator overloads
  reference operator*() const {
    cassert(m_ptr);
    return *m_ptr;
  }
  pointer operator->() const { return m_ptr; }

  tSHOB_GROUP_ITERATOR& operator++() {
    m_ptr = m_ptr? m_ptr->m_next : nullptr;
    return *this;
  }

  tSHOB_GROUP_ITERATOR& operator--() {
    m_ptr = m_ptr? m_ptr->m_prev : nullptr;
    return *this;
  }

  tSHOB_GROUP_ITERATOR operator++(int) {
    auto tmp = *this;
    ++(*this);
    return tmp;
  }

  tSHOB_GROUP_ITERATOR operator--(int) {
    auto tmp = *this;
    --(*this);
    return tmp;
  }  

  //Allow cast to raw pointer
  operator sSHOB_TYPE*() const { return m_ptr; }
  operator sSHOB_TYPE*() { return m_ptr; }

  template<typename T>
  friend bool operator == (const tSHOB_GROUP_ITERATOR<T>& a,
                           const tSHOB_GROUP_ITERATOR<T>& b);

  template<typename T>
  friend bool operator != (const tSHOB_GROUP_ITERATOR<T>& a,
                           const tSHOB_GROUP_ITERATOR<T>& b);

private:
  pointer m_ptr;
};

template<typename sSHOB_TYPE>
INLINE bool operator == (const tSHOB_GROUP_ITERATOR<sSHOB_TYPE>& a,
                         const tSHOB_GROUP_ITERATOR<sSHOB_TYPE>& b) {
  return a.m_ptr == b.m_ptr;
}

template<typename sSHOB_TYPE>
INLINE bool operator != (const tSHOB_GROUP_ITERATOR<sSHOB_TYPE>& a,
                         const tSHOB_GROUP_ITERATOR<sSHOB_TYPE>& b) {
  return a.m_ptr != b.m_ptr;
}

template <typename sSHOB_TYPE, typename sBASE_CLASS = sGROUP >
struct tSHOB_GROUP : public sBASE_CLASS
{
 private:
  SHOB_ID m_shob_count;
 protected:
  sSHOB_TYPE* m_head;
  sSHOB_TYPE* m_tail;

 public:
  SHOB_ID   n_shob_ptrs()         { return m_shob_count; }
  sSHOB_TYPE *shob_ptrs()         { return m_head; }
  VOID set_head(sSHOB_TYPE* shob_ptr, sSHOB_TYPE*& head) { head = shob_ptr; if (shob_ptr) shob_ptr->m_prev = NULL; }
  VOID set_tail(sSHOB_TYPE* shob_ptr, sSHOB_TYPE*& tail) { tail = shob_ptr; if (shob_ptr) shob_ptr->m_next = NULL; }
  // VOID set_head(sSHOB_TYPE* head) { m_head = head; if (head) head->m_prev = NULL; }
  // VOID set_tail(sSHOB_TYPE* tail) { m_tail = tail; if (tail) tail->m_next = NULL; }
  VOID set_head(sSHOB_TYPE* head) { set_head(head, m_head); }
  VOID set_tail(sSHOB_TYPE* tail) { set_tail(tail, m_tail); }

  //Forward iterator support for mixing this non standard linked list with other stl
  //containers
  auto begin() { return tSHOB_GROUP_ITERATOR<sSHOB_TYPE>(m_head); }
  auto end() { return tSHOB_GROUP_ITERATOR<sSHOB_TYPE>(nullptr); }

  tSHOB_GROUP() {
    reset();
  }

 tSHOB_GROUP(SCALE _scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm):
 sBASE_CLASS(_scale, dest_sp, supertype, realm) {
    reset();
  }

  VOID reset() {
    m_head    = NULL;
    m_tail    = NULL;
    m_shob_count = 0;
  }

  VOID add_shob_to_group(sSHOB_TYPE *shob_ptr) {
    add_shob_to_group(shob_ptr, m_head, m_tail);
  }

  VOID remove_shob_from_group(sSHOB_TYPE *shob_ptr) {
    remove_shob_from_group(shob_ptr, m_head, m_tail);
  }
  
  VOID add_shob_to_group(sSHOB_TYPE* shob_ptr, sSHOB_TYPE*& head, sSHOB_TYPE*& tail) {
    if (head == NULL) {
      head = shob_ptr;
      tail = head;
    } else {
      tail->m_next = shob_ptr;
      shob_ptr->m_prev = tail;
      tail = shob_ptr;
    }
    m_shob_count++;
  }

  VOID remove_shob_from_group(sSHOB_TYPE* shob_ptr, sSHOB_TYPE*& head, sSHOB_TYPE*& tail) {
    if (shob_ptr == NULL)
      return;
    if (shob_ptr->m_prev == NULL && shob_ptr->m_next == NULL) { // single node in the group
      head = NULL;
      tail = NULL;
    } else if (shob_ptr->m_prev == NULL) { // node is head
      set_head(shob_ptr->m_next, head);
    } else if (shob_ptr->m_next == NULL) { // node is tail
      set_tail(shob_ptr->m_prev, tail);
    } else {
      shob_ptr->m_prev->m_next = shob_ptr->m_next;
      shob_ptr->m_next->m_prev = shob_ptr->m_prev;
    }
    shob_ptr->m_prev = NULL;
    shob_ptr->m_next = NULL;
    m_shob_count--;
  }
};

template<typename UBLK_TYPE>
struct tUBLK_GROUP: public tSHOB_GROUP<UBLK_TYPE>
{
  cNEIGHBOR_SP m_dest_sp;
  sSP_SEND_GROUP_BASE *m_send_group = nullptr;
  sSP_SEND_GROUP_BASE *m_mme_send_group = nullptr;
#if BUILD_5G_LATTICE
  sSP_SEND_GROUP_BASE *m_pore_send_group = nullptr;
#endif

  tUBLK_GROUP(SCALE scale, cNEIGHBOR_SP dest_sp) : m_dest_sp(dest_sp) {
    this->m_scale = scale;
  }

  SP_TIMER_TYPE get_fluid_dyn_timer();

};

typedef tUBLK_GROUP<sUBLK> sUBLK_GROUP, *UBLK_GROUP;


#if BUILD_GPU
struct sMBLK_GROUP : public tUBLK_GROUP<sHMBLK> {
  
  sMBLK_GROUP(SCALE scale, cNEIGHBOR_SP dest_sp):
    tUBLK_GROUP<sHMBLK>(scale, dest_sp) {
    m_group_has_special_fluid = false;
  }

  void set_group_has_mblks_with_special_fluid();
  bool group_has_mblks_with_special_fluid() const {
    return m_group_has_special_fluid;
  }
private:
    bool m_group_has_special_fluid;
};

using MBLK_GROUP = sMBLK_GROUP*;
#endif


// sSURFEL_GROUP_BASE is a more general type that includes both regular surfel groups
// and surfel pair groups for APM and SLRF. It serves both as a base class for those
// group types and as the group type for the SURFEL_GROUP_BASE fset, which allows
// iteration over each present supertype (regular, APM, SLRF) for each dest_sp.

typedef struct sSURFEL_GROUP_BASE : public sGROUP
{
  REALM m_realm;
  cNEIGHBOR_SP m_dest_sp;
  sSP_SEND_GROUP_BASE *m_send_group;
  SURFEL_GROUP_SUPERTYPE m_supertype;
  BOOLEAN m_is_interface;

  sSURFEL_GROUP_BASE(SCALE scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm) {
    m_scale = scale;
    m_realm = realm;
    m_dest_sp = dest_sp;
    m_supertype = supertype;
    m_send_group = NULL;
    m_is_interface = (m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE) || 
                     (m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) ||
                     (m_supertype == CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE);
    //NOTE:
    //Local contact surfel groups (m_supertype==CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE) are processed as regular surfels 
    //since do not depend on any conduction_interface comm. Therefore, they are not tagged with m_is_interface.
  }

  STP_PROC dest_nsp() const { return m_dest_sp.nsp(); }

} *SURFEL_GROUP_BASE;


template<typename SURFEL_TYPE>
struct tSURFEL_GROUP : public tSHOB_GROUP<SURFEL_TYPE, sSURFEL_GROUP_BASE>
{
  tSURFEL_GROUP(SCALE _scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm):
      tSHOB_GROUP<SURFEL_TYPE, sSURFEL_GROUP_BASE>(_scale, dest_sp, supertype, realm) {}
};

typedef tSURFEL_GROUP<sSURFEL> sSURFEL_GROUP, *SURFEL_GROUP;

#if BUILD_GPU
typedef tSURFEL_GROUP<sMSFL> sMSFL_GROUP, *MSFL_GROUP;
#endif

sSURFEL_PAIR* allocate_surfel_pair(sSURFEL *int_surfel, sSURFEL *ext_surfel,
#if BUILD_D39_LATTICE
                                   dFLOAT inv_total_pgram_volume_d39,
                                   dFLOAT inv_total_pgram_volume_d19,
#endif
                                   dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                                   SURFEL_PAIR_GROUP_TYPE surfel_pair_type,
                                   STP_PHYSTYPE_TYPE sim_phys_type);

typedef struct sSURFEL_PAIR_GROUP: public tSHOB_GROUP<sSURFEL_PAIR, sSURFEL_GROUP_BASE>
{
 sSURFEL_PAIR_GROUP(SCALE _scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm):
  tSHOB_GROUP<sSURFEL_PAIR, sSURFEL_GROUP_BASE>(_scale, dest_sp, supertype, realm) {}

  sSURFEL_PAIR* add_surfel_pair_to_group(sSURFEL *interior_surfel, sSURFEL *exterior_surfel,
#if BUILD_D39_LATTICE
                                         dFLOAT inv_total_pgram_volume_d39,
                                         dFLOAT inv_total_pgram_volume_d19,
#endif
                                         dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                                         SURFEL_PAIR_GROUP_TYPE surfel_pair_type,
                                         STP_PHYSTYPE_TYPE sim_phys_type) {
    sSURFEL_PAIR *surfel_pair_ptr = allocate_surfel_pair(interior_surfel, exterior_surfel,
#if BUILD_D39_LATTICE
                                                         inv_total_pgram_volume_d39,
                                                         inv_total_pgram_volume_d19,
#endif
                                                         sum_pgram_volumes, surfel_pair_type,
                                                         sim_phys_type);
    this->add_shob_to_group(surfel_pair_ptr);
    surfel_pair_ptr->set_group(this);
    // Always store the surfel pair ptr in the second surfel
    if (interior_surfel->id() < exterior_surfel->id())
      exterior_surfel->set_surfel_pair(surfel_pair_ptr);
    else
      interior_surfel->set_surfel_pair(surfel_pair_ptr);
    return surfel_pair_ptr;
  }
} *SURFEL_PAIR_GROUP;


//Specific group type for surfel maps, which distinguishes within it several subsets of surfels and keep track of the 
//head/tail of each subset. Additionally, it stores the map weights needed for the surfels coupling.
enum CONTACT_SURFEL_TYPE {
  CONTACT_WSURFEL,                    //widow surfel for contact across realms (solid-fluid)
  CONTACT_SURFEL_PAIRED,              //paired surfels (conformal contact so no map need, same realm)
  CONTACT_SURFEL_PRIMARY_AVERAGED,    //averaged interact among themselves through a face averaged value
  CONTACT_SURFEL_SECONDARY_AVERAGED,
  CONTACT_SURFEL_PRIMARY,             //surfels with map weights for surface coupling and with no dependencies
  CONTACT_SURFEL_PRIMARY_SECONDARY,   //surfels with map weights, but who also depend on other primary surfels
  CONTACT_SURFEL_SECONDARY,           //surfels involved in coupling, expecting to receive info from primary ones
  NUM_CONTACT_SURFEL_TYPES
};

struct sCONTACT_SURFEL_SECONDARY_INFO {
  sSURFEL* m_surfel;
  asINT32 m_accumulated_heat_flux_idx; 
};

struct sCONTACT_WEIGHT_ITEM {
  union {
    SHOB_ID m_opposite_surfel_id;
    asINT32 m_opposite_info_index;   //local index where opposite (secondary) item info is stored within the group
  };
  sFLOAT m_weight;
  sFLOAT m_opposite_weight;
};

struct sCONTACT_WEIGHT_SET {
  std::vector<sCONTACT_WEIGHT_ITEM> m_weights;
};

struct sCONTACT_SURFEL_WEIGHTS_INFO {
  uINT32 m_n_contact_weights;
};

// Global map used to store the weights during the initialization
extern std::unordered_map<SHOB_ID,sCONTACT_WEIGHT_SET> g_contact_info;

/** @brief Class that centralizes data and methods for accumulation needed for averaged contact 
 * 
 * Averaged contact involves two steps: accumulation & averaging. Accumulation done on a group basis per face.
*/
class sCONTACT_ACCUMULATION {
 public:
  sCONTACT_ACCUMULATION(BOOLEAN is_primary, size_t idx) { //used during initialization
    m_averager_encoded_index = (is_primary) ? 2*idx : 2*idx+1;
    m_curr_timestep = 0;
    reset();
  }

  //Accumulated values
  dFLOAT temp_accum;
  dFLOAT conductivity_accum;
  dFLOAT volume_accum;

  VOID reset() {
    temp_accum = 0.0;
    conductivity_accum = 0.0;
    volume_accum = 0.0;
  }

  size_t averager_index() const { return m_averager_encoded_index / 2; }
  
  struct sAVERAGER_INDEX_AND_FACE {
    size_t idx;
    bool is_primary;
  };
  
  sAVERAGER_INDEX_AND_FACE averager_index_and_face() const {
    size_t idx = averager_index();
    bool is_primary = (2*idx == m_averager_encoded_index);
    return sAVERAGER_INDEX_AND_FACE{idx, is_primary};
  }

  VOID accumulate(sSURFEL *surfel);
  
  VOID add(sCONTACT_ACCUMULATION* accum) {
    temp_accum += accum->temp_accum;
    conductivity_accum += accum->conductivity_accum;
    volume_accum += accum->volume_accum;
  }

  //Dangling accumulators, own by the averager item rather than the surfel groups, do not update the time_step since
  //they are only filled from global reduction. Use that element to tag them
  VOID set_dangling() { m_curr_timestep = BASETIME_LAST; }
  bool is_dangling() { return m_curr_timestep == BASETIME_LAST; }
  
 private:
  //Index in the global average contact vector, encoded so
  //- (idx % 2 = 0) : primary 
  //- (idx % 2 = 1) : secondary
  size_t m_averager_encoded_index;

  //Keeps track of the current time step to identify when needs to reset values
  BASETIME m_curr_timestep;
};

// Specific macros to loop through surfels in a contact group
#define DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, contact_surfel_group, contact_surfel_type)                   \
  if (contact_surfel_group->shob_head_ptr(contact_surfel_type) !=NULL)                                            \
    for (sSURFEL *shob_var  = contact_surfel_group->shob_head_ptr(contact_surfel_type),                           \
             *___(shob_end) = contact_surfel_group->shob_tail_ptr(contact_surfel_type)->m_next;                   \
          shob_var != ___(shob_end) ;                                                                             \
          shob_var = shob_var->m_next)

#define DO_CONTACT_SURFELS_OF_GROUP_AVERAGED(shob_var, contact_surfel_group, contact_surfel_type)                 \
  for (CONTACT_SURFEL_TYPE contact_surfel_type = CONTACT_SURFEL_PRIMARY_AVERAGED;                            \
       contact_surfel_type <= CONTACT_SURFEL_SECONDARY_AVERAGED;                                             \
       contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>((int)contact_surfel_type + 1))            \
    DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, contact_surfel_group, contact_surfel_type)


#define DO_CONTACT_SURFELS_OF_GROUP_PRIMARY(shob_var, contact_surfel_group)                                       \
  for (CONTACT_SURFEL_TYPE ___(contact_surfel_type) = CONTACT_SURFEL_PRIMARY;                                     \
       ___(contact_surfel_type) < CONTACT_SURFEL_SECONDARY;                                                       \
       ___(contact_surfel_type) = static_cast<CONTACT_SURFEL_TYPE>((int)___(contact_surfel_type) + 1))            \
    DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, contact_surfel_group, ___(contact_surfel_type))

#define DO_CONTACT_SURFELS_OF_GROUP_SECONDARY(shob_var, contact_surfel_group)                                     \
  for (CONTACT_SURFEL_TYPE ___(contact_surfel_type) = CONTACT_SURFEL_PRIMARY_SECONDARY;                           \
       ___(contact_surfel_type) <= CONTACT_SURFEL_SECONDARY;                                                      \
       ___(contact_surfel_type) = static_cast<CONTACT_SURFEL_TYPE>((int)___(contact_surfel_type) + 1))            \
    DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, contact_surfel_group, ___(contact_surfel_type))

#define DO_CONTACT_SURFELS_OF_GROUP(shob_var, contact_surfel_group, contact_surfel_type)                          \
  for (CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(0);                             \
       contact_surfel_type < NUM_CONTACT_SURFEL_TYPES;                                                            \
       contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>((int)contact_surfel_type + 1))                      \
    DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, contact_surfel_group, contact_surfel_type)

typedef struct sCONTACT_SURFEL_GROUP : public sSURFEL_GROUP
{
  sCONTACT_SURFEL_GROUP(SCALE _scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype):
  sSURFEL_GROUP(_scale, dest_sp, supertype, STP_COND_REALM) 
  { //Initializes the array with the head of each group
    for (int type=0; type<NUM_CONTACT_SURFEL_TYPES; type++) {
      m_heads[type] = NULL;
      m_tails[type] = NULL;
    }
  }

  //shob_ptrs() keeps base class behavior, adding now new functionality to enabling specifiying subset type
  sSURFEL* shob_head_ptr(CONTACT_SURFEL_TYPE type) { return m_heads[type]; } 
  sSURFEL* shob_tail_ptr(CONTACT_SURFEL_TYPE type) { return m_tails[type]; } 

  VOID get_averaged_head_tail_ptrs(sSURFEL* &head, sSURFEL* &tail) {
    head = m_heads[CONTACT_SURFEL_PRIMARY_AVERAGED];
    tail = m_tails[CONTACT_SURFEL_SECONDARY_AVERAGED];
    if (head == NULL && tail != NULL) {
      head = m_heads[CONTACT_SURFEL_SECONDARY_AVERAGED];
    } else if (head != NULL && tail == NULL) {
      tail = m_tails[CONTACT_SURFEL_PRIMARY_AVERAGED];
    }
  }
  
  //Add/remove shob methods require for this group to specify the type of subset they belong to
  //NOTE: add_surfel_to_groups(..) is the calling method that loads surfels in the group. It ensures when loading 
  //      paired surfels that they are added consecutively to the group, so no need to do additional work here.
  VOID add_surfel_to_group(sSURFEL *surfel, CONTACT_SURFEL_TYPE type) {
    add_shob_to_group(surfel, m_heads[type], m_tails[type]);
  }

  VOID remove_surfel_from_group(sSURFEL *surfel, CONTACT_SURFEL_TYPE type) {
    if (type == CONTACT_SURFEL_PAIRED) {
      remove_shob_from_group(surfel->m_next, m_heads[type], m_tails[type]);
      remove_shob_from_group(surfel, m_heads[type], m_tails[type]);
    }
    else {
      remove_shob_from_group(surfel, m_heads[type], m_tails[type]);
    }
  }
  
  //Accumulation done locally by each group
  std::vector<sCONTACT_ACCUMULATION> m_averaged_accumulation;
  
  //The subset of contact weights related info associated with this group 
  std::vector<sCONTACT_SURFEL_WEIGHTS_INFO> m_contact_info;     //# of contact weights per primary
  std::vector<sCONTACT_WEIGHT_ITEM> m_contact_weights;          //contact weights, pointing at secondary info
  std::vector<sCONTACT_SURFEL_SECONDARY_INFO> m_secondary_info; //secondary surfel & accumulator index
  std::vector<dFLOAT> m_secondary_accumulated_interface_heat;   //accumulator
  std::vector<dFLOAT*> m_ghost_accumulated_interface_heat_ptr;  //pointers to the accumulator on the send/recv groups for ghosts

  
  //NOTE: This method needs to be called after all surfels are correctly assigned to their groups during initialization,
  //but before subtypes are linked and accumulators allocated since we modify the order of the surfels within the group
  VOID check_even_odd_order();
  //NOTE: This method needs to be called after even/odd order has been checked
  VOID link_shob_types_and_fill_contact_weights(std::unordered_map<SHOB_ID, asINT32>& id_to_info_index_map,
                                                std::unordered_map<sCONTACT_SURFEL_GROUP*, std::vector<asINT32>>& deferred_info_map);

  VOID fill_deferred_secondaries(std::unordered_map<SHOB_ID, asINT32>& id_to_info_index_map,
                                 std::vector<asINT32>& secondary_indexes) {
    for (auto i : secondary_indexes) {
      SURFEL secondary_surfel = m_secondary_info[i].m_surfel;
      auto it = id_to_info_index_map.find(secondary_surfel->id());
      if (it == id_to_info_index_map.end()) {
        msg_internal_error("Deffered accumulation not found for surfel %d", secondary_surfel->id());
      } else {
        m_secondary_info[i].m_accumulated_heat_flux_idx = it->second;
      }
    }
  }

  VOID add_averaged_contact_index(size_t idx) { //used during initialization
    m_averaged_contact_idx.push_back(idx);
  }
  VOID reset_next_averaged_contact_index() {
    m_next_average_contact_index = 0;
  }
  sCONTACT_ACCUMULATION* next_accumulation() {
    size_t idx_accum = m_averaged_contact_idx[m_next_average_contact_index];
    m_next_average_contact_index++;
    return &m_averaged_accumulation[idx_accum];
  }
  size_t next_averaging_index() {
    return next_accumulation()->averager_index();
  }
  
  VOID reset_processed_contact_counters() {
    m_next_contact_set_index = 0;
    m_next_contact_weight_index = 0;
    m_next_accumulated_heat_index = 0;
    reset_next_averaged_contact_index();
  }
  
  VOID peek_weights_info(asINT32 &idx_begin, asINT32 &idx_end) {
    idx_begin = m_next_contact_weight_index;
    idx_end = idx_begin + m_contact_info[m_next_contact_set_index].m_n_contact_weights;
  }
  VOID get_weights_info(asINT32 &idx_begin, asINT32 &idx_end) {
    //last processed 
    peek_weights_info(idx_begin, idx_end);
    //update the counter to leave it ready for next set of weights
    m_next_contact_set_index++;
    m_next_contact_weight_index = idx_end;
  }
  asINT32 next_accumulation_index() {
    return m_next_accumulated_heat_index++;
  }
  VOID skip_accumulation() {
    m_next_accumulated_heat_index++;
  }
  VOID skip_contact_surfel(CONTACT_SURFEL_TYPE contact_surfel_type, sSURFEL* shob_ptr) {
    asINT32 num_accumulations = 1;
    if (shob_ptr->is_conduction_shell()) {
      num_accumulations += shob_ptr->shell_conduction_data()->num_layers();
    }
    if (contact_surfel_type == CONTACT_SURFEL_PRIMARY) {
      m_next_contact_weight_index += m_contact_info[m_next_contact_set_index].m_n_contact_weights;
      m_next_contact_set_index++;
    } else if (contact_surfel_type == CONTACT_SURFEL_SECONDARY) {
      m_next_accumulated_heat_index += num_accumulations;
    } else if (contact_surfel_type == CONTACT_SURFEL_PRIMARY_SECONDARY) {
      m_next_contact_weight_index += m_contact_info[m_next_contact_set_index].m_n_contact_weights;
      m_next_contact_set_index++;
      m_next_accumulated_heat_index += num_accumulations;
    }
  }
  
 private:
  sSURFEL *m_heads[(int)NUM_CONTACT_SURFEL_TYPES];
  sSURFEL *m_tails[(int)NUM_CONTACT_SURFEL_TYPES];

  //Set/weights indices of next elements to be processed to iterate over weights as surfels are being processed
  asINT32 m_next_contact_set_index;
  asINT32 m_next_contact_weight_index;
  //Accumulation index, to retrieve accumulated heat transfer as secondary surfels are being processed
  asINT32 m_next_accumulated_heat_index;
  
  //For averaged contact, store indexes to the global average contact vector. More over, the indexes are encoded so for
  //each entry in the global vector there are two possible indexes:
  //- one for primary   (idx % 2 = 0) 
  //- one for secondary (idx % 2 = 1)
  asINT32 m_next_average_contact_index;
  std::vector<size_t> m_averaged_contact_idx; //encoded: for each entry there are two indices one for primary (%2=0)

  inline VOID push_secondary_item(sSURFEL *shob_ptr, 
                                  asINT32 &n_accumulated) {
    sCONTACT_SURFEL_SECONDARY_INFO item;
    item.m_surfel = shob_ptr;
    item.m_accumulated_heat_flux_idx = n_accumulated;
    m_secondary_info.push_back(item);
    //update the number of elements that this secondary surfel will require
    n_accumulated += 1;
    if (shob_ptr->is_conduction_shell()) {
      n_accumulated += shob_ptr->shell_conduction_data()->num_layers();
    }
  };

  //ghost surfels do not accumulate, just need to store the pointer in the secondary info
  inline VOID push_secondary_item(sSURFEL *shob_ptr) {
    sCONTACT_SURFEL_SECONDARY_INFO item;
    item.m_surfel = shob_ptr;
    item.m_accumulated_heat_flux_idx = -1;
    m_secondary_info.push_back(item);
  };
  
} *CONTACT_SURFEL_GROUP;


// struct sBSURFEL;

typedef struct sBSURFEL_GROUP: public tSHOB_GROUP<sBSURFEL>
{
  sBSURFEL_GROUP(SCALE scale)
  {
    m_scale = scale;
  }

} *BSURFEL_GROUP;

typedef struct sSAMPLING_SURFEL_GROUP: public tSHOB_GROUP<sSAMPLING_SURFEL, sSURFEL_GROUP_BASE>
{
  sSAMPLING_SURFEL_GROUP(SCALE _scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm):
  tSHOB_GROUP<sSAMPLING_SURFEL, sSURFEL_GROUP_BASE>(_scale, dest_sp, supertype, realm) {}

} *SAMPLING_SURFEL_GROUP;


//------------------- Shob group fsets -----------------------------------------

struct sUBLK_GROUP_ORDER
{
  BOOLEAN operator () (UBLK_GROUP a, UBLK_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

#if BUILD_GPU
struct sMBLK_GROUP_ORDER
{
  BOOLEAN operator () (MBLK_GROUP a, MBLK_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

struct sMSFL_GROUP_ORDER
{
  BOOLEAN operator () (MSFL_GROUP a, MSFL_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};
#endif

struct sSURFEL_GROUP_ORDER
{
  BOOLEAN operator () (SURFEL_GROUP a, SURFEL_GROUP b) const
  {
    if(a->m_realm == b->m_realm) {
      return (a->m_dest_sp < b->m_dest_sp);
    } else {
      return (a->m_realm < b->m_realm);
    }
  }
};

struct sSAMPLING_SURFEL_GROUP_ORDER
{
  BOOLEAN operator () (SAMPLING_SURFEL_GROUP a, SAMPLING_SURFEL_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

struct sSURFEL_PAIR_GROUP_ORDER
{
  BOOLEAN operator () (SURFEL_PAIR_GROUP a, SURFEL_PAIR_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

struct sCONTACT_SURFEL_GROUP_ORDER
{
  BOOLEAN operator () (CONTACT_SURFEL_GROUP a, CONTACT_SURFEL_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

struct sBSURFEL_GROUP_ORDER
{
  BOOLEAN operator () (BSURFEL_GROUP a, BSURFEL_GROUP b) const
  {
    return (a->m_scale < b->m_scale);
  }
};

struct sSURFEL_GROUP_BASE_ORDER
{
  BOOLEAN operator () (SURFEL_GROUP_BASE a, SURFEL_GROUP_BASE b) const
  {
    if(a->m_dest_sp !=  b->m_dest_sp)
      return (a->m_dest_sp < b->m_dest_sp);
    else
      return (a->m_supertype < b->m_supertype);
  }
};


typedef struct sUBLK_FSET : public tSP_FSET < UBLK_GROUP, sUBLK_GROUP_ORDER > {

  public:

  UBLK_GROUP create_group(asINT32 scale, cNEIGHBOR_SP dest_sp);

} *UBLK_FSET;

#if BUILD_GPU
typedef struct sMBLK_FSET : public tSP_FSET < MBLK_GROUP, sMBLK_GROUP_ORDER > {

  public:

  MBLK_GROUP create_group(asINT32 scale, cNEIGHBOR_SP dest_sp);

} *MBLK_FSET;
#endif

typedef struct sSURFEL_BASE_FSET : public tSP_FSET < SURFEL_GROUP_BASE, sSURFEL_GROUP_BASE_ORDER > {

  public:

  SURFEL_GROUP_BASE create_group(asINT32 scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm) {
    sSURFEL_GROUP_BASE signature(scale, dest_sp, supertype, realm);
    SURFEL_GROUP_BASE group = find_group(&signature);
    if (group == NULL) {
      switch (supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE:
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      case MLRF_SURFEL_GROUP_SUPERTYPE:
        group = xnew sSURFEL_GROUP(scale, dest_sp, supertype, realm);
        break;
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
        group = xnew sSURFEL_PAIR_GROUP(scale, dest_sp, supertype, realm);
        break;
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
        group = xnew sCONTACT_SURFEL_GROUP(scale, dest_sp, supertype);
        break;
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
        group = xnew sSAMPLING_SURFEL_GROUP(scale, dest_sp, supertype, realm);
        break;
      default:
        msg_internal_error("Surfel super type %d not defined", supertype);
      }
      add_group(group);
    }
    return group;
  }

} *SURFEL_BASE_FSET;

typedef struct sBSURFEL_FSET : public tSP_FSET < BSURFEL_GROUP, sBSURFEL_GROUP_ORDER > {

  BSURFEL_GROUP create_group(asINT32 scale)
  {
    sBSURFEL_GROUP signature(scale);
    BSURFEL_GROUP group = find_group(&signature);
    if (group == NULL) {
      group = xnew sBSURFEL_GROUP(scale);
      add_group(group);
    }
    return group;
  }
} *BSURFEL_FSET;



// These are only needed so we can iterate over these groups during initialization. The create_group methods
// are unnecessary. Groups could be added to these fsets in one of two ways: either immediately after they are
// added, or during the init phase when we iterate over the sSURFEL_BASE_FSETs adjusting the send group pointers.

typedef struct sSURFEL_FSET : public tSP_FSET < SURFEL_GROUP, sSURFEL_GROUP_ORDER > {
} *SURFEL_FSET;

#if BUILD_GPU
typedef struct sMSFL_FSET : public tSP_FSET < MSFL_GROUP, sMSFL_GROUP_ORDER > {

  public:

  MSFL_GROUP create_group(asINT32 scale, cNEIGHBOR_SP dest_sp, SURFEL_GROUP_SUPERTYPE supertype, REALM realm) {
    sMSFL_GROUP signature(scale, dest_sp, supertype, realm);
    MSFL_GROUP group = find_group(&signature);
    if (group == nullptr) {
      switch (supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      case MLRF_SURFEL_GROUP_SUPERTYPE:
        group = xnew sMSFL_GROUP(scale, dest_sp, supertype, realm);
        break;
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      default:
        msg_internal_error("Surfel super type %d not defined", supertype);
      }
      add_group(group);
    }
    return group;
  }

} *MSFL_FSET;
#endif

typedef struct sSAMPLING_SURFEL_FSET : public tSP_FSET < SAMPLING_SURFEL_GROUP, sSAMPLING_SURFEL_GROUP_ORDER > {
} *SAMPLING_SURFEL_FSET;

typedef struct sSURFEL_PAIR_FSET : public tSP_FSET < SURFEL_PAIR_GROUP, sSURFEL_PAIR_GROUP_ORDER > {
} *SURFEL_PAIR_FSET;

typedef struct sCONTACT_SURFEL_FSET : public tSP_FSET < CONTACT_SURFEL_GROUP, sCONTACT_SURFEL_GROUP_ORDER > {
} *CONTACT_SURFEL_FSET;

extern UBLK_FSET g_ublk_groups[N_UBLK_GROUP_TYPES];
#if BUILD_GPU
extern MBLK_FSET g_mblk_groups[N_UBLK_GROUP_TYPES];
extern MSFL_FSET g_msfl_groups[N_SURFEL_GROUP_TYPES];
#endif
extern SURFEL_BASE_FSET g_surfel_base_groups[N_SURFEL_BASE_GROUP_TYPES]; // 3 fsets; fringe, fringe2 and interior
extern SURFEL_FSET g_surfel_groups[N_SURFEL_GROUP_TYPES];
extern SAMPLING_SURFEL_FSET g_sampling_surfel_groups[N_SAMPLING_SURFEL_GROUP_TYPES];  // fringe and interior
extern SURFEL_PAIR_FSET g_surfel_pair_groups[N_SURFEL_PAIR_GROUP_TYPES];
extern CONTACT_SURFEL_FSET g_contact_surfel_groups[N_CONTACT_SURFEL_GROUP_TYPES];
extern BSURFEL_FSET g_bsurfel_groups[N_BSURFEL_GROUP_TYPES]; // 3 fsets; fringe, fringe2, and interior
extern UBLK_FSET g_solid_vr_fine_group;

VOID initialize_shob_groups();
VOID initialize_surfel_group_fsets();
VOID disable_surfel_send_requests_by_surfel_groups();

VOID print_shob_group_info();

VOID allocate_contact_send_recv_accumulators();

VOID update_shob_group_types();

VOID move_ublk_to_nearblk_group(UBLK ublk, UBLK_GROUP_TYPE group_type);

VOID assign_dangling_send_groups_to_surfel_groups(STP_REALM realm);

//----------------------------------------------------------------------------
// LOOP MACROS
//----------------------------------------------------------------------------

#define DO_UBLK_GROUPS_OF_SCALE_TYPE(group_var, scale, type) \
  UBLK_FSET ___(ublk_fset) = g_ublk_groups[type];           \
  FSET_PTR_FOREACH_GROUP_OF_SCALE(sUBLK_FSET, ___(ublk_fset), group_var, scale) 

#if BUILD_GPU
#define DO_MBLK_GROUPS_OF_SCALE_TYPE(group_var, scale, type)                                            \
  MBLK_FSET ___(ublk_fset) = g_mblk_groups[type];     \
  FSET_PTR_FOREACH_GROUP_OF_SCALE(sMBLK_FSET, ___(ublk_fset), group_var, scale) 
#endif

#define DO_UBLKS_OF_GROUP(shob_var, ublk_group)                                                                  \
  for(auto shob_var = ublk_group->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_FARBLKS_OF_SCALE(shob_var, scale)                                                                     \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::FARBLK_TYPES[0]];                       \
       ___(igroup) < nSHOB_CATEGORIES::N_FARBLK_TYPES;                                                           \
       ++___(igroup),                                                                                            \
       ___(ublk_fset) = ___(igroup) < nSHOB_CATEGORIES::N_FARBLK_TYPES ?                                         \
       g_ublk_groups[nSHOB_CATEGORIES::FARBLK_TYPES[___(igroup)]] : NULL)                                        \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)



#define DO_NEARBLKS_OF_SCALE(shob_var, scale)                                                                    \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::NEARBLK_TYPES[0]];                      \
       ___(igroup) < nSHOB_CATEGORIES::N_NEARBLK_TYPES;                                                          \
       ++___(igroup),                                                                                            \
       ___(ublk_fset) =  ___(igroup) < nSHOB_CATEGORIES::N_NEARBLK_TYPES ?                                       \
       g_ublk_groups[nSHOB_CATEGORIES::NEARBLK_TYPES[___(igroup)]] : NULL)                                       \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(),                                             \
           ___(end) = ___(ublk_fset)->groups_of_scale(scale).end();                                              \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_VRBLKS_OF_SCALE(shob_var, scale)                                                                      \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::VRFINE_TYPES[0]];                       \
       ___(igroup) < nSHOB_CATEGORIES::N_VRFINE_TYPES;                                                           \
       ++___(igroup),                                                                                            \
       ___(ublk_fset) = ___(igroup) < nSHOB_CATEGORIES::N_VRFINE_TYPES ?                                         \
       g_ublk_groups[nSHOB_CATEGORIES::VRFINE_TYPES[___(igroup)]] : NULL)                                        \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_SOLID_VRBLKS_OF_SCALE(shob_var, scale)                                                                \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset) = g_solid_vr_fine_group;                                                              \
  UBLK_GROUP ___(group);                                                                                         \
  if(___(ublk_fset)->groups_per_scale())                                                                         \
    for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
         ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
      for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_VRFINE_NEARBLKS_OF_SCALE(shob_var, scale)                                                             \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::VRFINE_NEAR_TYPES[0]];                  \
       ___(igroup) < nSHOB_CATEGORIES::N_VRFINE_NEAR_TYPES;                                                      \
       ++___(igroup),                                                                                            \
       ___(ublk_fset) = ___(igroup) < nSHOB_CATEGORIES::N_VRFINE_NEAR_TYPES ?                                    \
       g_ublk_groups[nSHOB_CATEGORIES::VRFINE_NEAR_TYPES[___(igroup)]] : NULL)                                   \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_MIRROR_UBLKS_OF_SCALE(shob_var, scale)                                                                \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::MIRROR_UBLK_TYPES[0]];                  \
       ___(igroup) < nSHOB_CATEGORIES::N_MIRROR_UBLK_TYPES;                                                      \
       ++___(igroup), ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::MIRROR_UBLK_TYPES[___(igroup)]])          \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_VRFINE_GHOST_UBLKS_OF_SCALE(shob_var, scale)                                                          \
  sUBLK *shob_var; asINT32 ___(igroup);                                                                          \
  sUBLK_FSET::ITERATOR ___(it);                                                                                  \
  sUBLK_FSET::ITERATOR ___(end);                                                                                 \
  UBLK_FSET ___(ublk_fset);                                                                                      \
  UBLK_GROUP ___(group);                                                                                         \
  for (___(igroup) = 0, ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::VRFINE_GHOST_TYPES[0]];                 \
       ___(igroup) < nSHOB_CATEGORIES::N_VRFINE_GHOST_TYPES;                                                     \
       ++___(igroup), ___(ublk_fset) = g_ublk_groups[nSHOB_CATEGORIES::VRFINE_GHOST_TYPES[___(igroup)]])         \
    if(___(ublk_fset)->groups_per_scale())                                                                       \
      for (___(it) = ___(ublk_fset)->groups_of_scale(scale).begin(), ___(end) = ___(ublk_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

//Macro to loop through all base groups expected to contain surfels 
#define DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group_var, scale, type)                                               \
  sSURFEL_BASE_FSET ___(surfel_base_fset) = *g_surfel_base_groups[type];                                          \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_BASE_FSET, ___(surfel_base_fset), group_var, scale)

#if BUILD_GPU
#define DO_MSFL_GROUPS_OF_SCALE_TYPE(group_var, scale, type)                                             \
  sMSFL_FSET ___(msfl_fset) = *g_msfl_groups[type];	\
  FSET_FOREACH_GROUP_OF_SCALE(sMSFL_FSET, ___(msfl_fset), group_var, scale) 
#endif

//NOTE: Consider sending this macro to fset.h
#define FSET_CATEGORY_FOREACH_GROUP_OF_SCALE(FSET_TYPE, _fsets, _category_types, _n_category_types, group_var, _scale) \
  asINT32 ___(icategory_type);                                                                                    \
  FSET_TYPE *___(fset);                                                                                           \
  FSET_TYPE::GROUP group_var;                                                                                     \
  for (___(icategory_type) = 0, ___(fset) = _fsets[_category_types[0]];                                           \
       ___(icategory_type) < _n_category_types;                                                                   \
       ++___(icategory_type),                                                                                     \
       ___(fset) = (___(icategory_type)<_n_category_types) ? _fsets[_category_types[___(icategory_type)]] : NULL) \
    if (___(fset)->groups_per_scale())                                                                            \
      for (FSET_TYPE::ITERATOR ___(it) = ___(fset)->groups_of_scale(_scale).begin(),                              \
           ___(end) = ___(fset)->groups_of_scale(_scale).end();                                                   \
           ___(it) != ___(end) && (group_var = *___(it), TRUE);    ___(it)++)

#define DO_CONTACT_SURFELS_GROUPS_OF_SCALE(group_var, scale)                                                      \
  FSET_CATEGORY_FOREACH_GROUP_OF_SCALE(sCONTACT_SURFEL_FSET,                                                      \
                                       g_contact_surfel_groups,                                                   \
                                       nSHOB_CATEGORIES::CONTACT_SURFEL_TYPES,                                    \
                                       nSHOB_CATEGORIES::N_CONTACT_SURFEL_TYPES,                                  \
                                       group_var, scale)

#define DO_CONTACT_SURFELS_GROUPS_OF_SCALE_TYPE(group_var, scale, type)                                           \
  sCONTACT_SURFEL_FSET ___(contact_surfel_fset) = *g_contact_surfel_groups[type];                                 \
  FSET_FOREACH_GROUP_OF_SCALE(sCONTACT_SURFEL_FSET, ___(contact_surfel_fset), group_var, scale)

#define DO_CONTACT_SURFELS_GROUPS_OF_SCALE_TYPE_REVERSE(group_var, scale, type)                                   \
  sCONTACT_SURFEL_FSET ___(contact_surfel_fset) = *g_contact_surfel_groups[type];                                 \
  FSET_FOREACH_GROUP_OF_SCALE_REVERSE(sCONTACT_SURFEL_FSET, ___(contact_surfel_fset), group_var, scale)

#define DO_SURFELS_OF_GROUP(shob_var, surfel_group)                                                              \
  for(sSURFEL* shob_var = surfel_group->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_SAMPLING_SURFELS_OF_GROUP(shob_var, sampling_surfel_group)                                                              \
  for(sSAMPLING_SURFEL* shob_var = sampling_surfel_group->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_SURFEL_PAIRS_OF_GROUP(shob_var, surfel_group)                                                         \
  for(sSURFEL_PAIR* shob_var = surfel_group->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define FSET_CATEGORY_FOREACH_SURFEL_OF_SCALE(SURFEL_TYPE, FSET_TYPE, _fsets, _category_types, _n_category_types,     \
                                              shob_var, scale)                                                        \
  FSET_CATEGORY_FOREACH_GROUP_OF_SCALE(FSET_TYPE, _fsets, _category_types, _n_category_types, ___(group), scale)      \
    for(SURFEL_TYPE shob_var = ___(group)->shob_ptrs();                                                               \
        shob_var != NULL;                                                                                             \
        shob_var = shob_var ? shob_var->m_next : NULL)                                                                \

#define DO_DYN_SURFELS_FLOW_OF_SCALE(shob_var, scale)                                                            \
  sSURFEL *shob_var; asINT32 ___(igroup);                                                                        \
  sSURFEL_FSET::ITERATOR ___(it);                                                                                \
  sSURFEL_FSET::ITERATOR ___(end);                                                                               \
  SURFEL_FSET ___(surfel_fset);                                                                                  \
  SURFEL_GROUP ___(group);                                                                                       \
  for (___(igroup) = 0, ___(surfel_fset) = g_surfel_groups[nSHOB_CATEGORIES::DYN_SURFEL_FLOW_TYPES[0]];               \
       ___(igroup) < nSHOB_CATEGORIES::N_DYN_SURFEL_FLOW_TYPES;                                                       \
       ++___(igroup),                                                                                            \
       ___(surfel_fset) = ___(igroup) < nSHOB_CATEGORIES::N_DYN_SURFEL_FLOW_TYPES ?                                   \
       g_surfel_groups[nSHOB_CATEGORIES::DYN_SURFEL_FLOW_TYPES[___(igroup)]] : NULL)                                  \
    if(___(surfel_fset)->groups_per_scale())                                                                     \
      for (___(it) = ___(surfel_fset)->groups_of_scale(scale).begin(), ___(end) = ___(surfel_fset)->groups_of_scale(scale).end(); \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                   \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

//DFG-TODO: Once verified the FSET_CATEGORY_FOREACH_SURFEL_OF_SCALE, use it in the rest
#define DO_DYN_SURFELS_CONDUCTION_OF_SCALE(shob_var, scale)                                                       \
  FSET_CATEGORY_FOREACH_SURFEL_OF_SCALE(SURFEL, sSURFEL_FSET, g_surfel_groups,                                    \
                                        nSHOB_CATEGORIES::DYN_SURFEL_CONDUCTION_TYPES,                            \
                                        nSHOB_CATEGORIES::N_DYN_SURFEL_CONDUCTION_TYPES, shob_var, scale)


#define DO_SLRF_SURFEL_PAIRS_OF_SCALE(shob_var, scale)                                                             \
  sSURFEL_PAIR *shob_var; asINT32 ___(igroup);                                                                     \
  sSURFEL_PAIR_FSET::ITERATOR ___(it);                                                                             \
  sSURFEL_PAIR_FSET::ITERATOR ___(end);                                                                            \
  SURFEL_PAIR_FSET ___(surfel_pair_fset);                                                                          \
  SURFEL_PAIR_GROUP ___(group);                                                                                    \
  for (___(igroup) = 0, ___(surfel_pair_fset) = g_surfel_pair_groups[nSHOB_CATEGORIES::SLRF_SURFEL_PAIR_TYPES[0]]; \
       ___(igroup) < nSHOB_CATEGORIES::N_SLRF_SURFEL_PAIR_TYPES;                                                   \
       ++___(igroup),                                                                                              \
       ___(surfel_pair_fset) = ___(igroup) < nSHOB_CATEGORIES::N_SLRF_SURFEL_PAIR_TYPES ?                          \
       g_surfel_pair_groups[nSHOB_CATEGORIES::SLRF_SURFEL_PAIR_TYPES[___(igroup)]] : NULL)                         \
    if(___(surfel_pair_fset)->groups_per_scale())                                                                  \
      for (___(it) = ___(surfel_pair_fset)->groups_of_scale(scale).begin(),                                        \
           ___(end) = ___(surfel_pair_fset)->groups_of_scale(scale).end();                                         \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                     \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_ISURFEL_PAIRS_OF_SCALE(shob_var, scale)                                                             \
  sSURFEL_PAIR *shob_var; asINT32 ___(igroup);                                                                 \
  sSURFEL_PAIR_FSET::ITERATOR ___(it);                                                                         \
  sSURFEL_PAIR_FSET::ITERATOR ___(end);                                                                        \
  SURFEL_PAIR_FSET ___(surfel_pair_fset);                                                                      \
  SURFEL_PAIR_GROUP ___(group);                                                                                \
  for (___(igroup) = 0, ___(surfel_pair_fset) = g_surfel_pair_groups[nSHOB_CATEGORIES::ISURFEL_PAIR_TYPES[0]]; \
       ___(igroup) < nSHOB_CATEGORIES::N_ISURFEL_PAIR_TYPES;                                                   \
       ++___(igroup),                                                                                          \
       ___(surfel_pair_fset) = ___(igroup) < nSHOB_CATEGORIES::N_ISURFEL_PAIR_TYPES ?                          \
       g_surfel_pair_groups[nSHOB_CATEGORIES::ISURFEL_PAIR_TYPES[___(igroup)]] : NULL)                         \
    if(___(surfel_pair_fset)->groups_per_scale())                                                              \
      for (___(it) = ___(surfel_pair_fset)->groups_of_scale(scale).begin(),                                    \
           ___(end) = ___(surfel_pair_fset)->groups_of_scale(scale).end();                                     \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                 \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_WSURFELS_CONDUCTION_OF_SCALE(shob_var, scale)                                                        \
  sSURFEL *shob_var; asINT32 ___(igroup);                                                                       \
  sSURFEL_FSET::ITERATOR ___(it);                                                                               \
  sSURFEL_FSET::ITERATOR ___(end);                                                                              \
  SURFEL_FSET ___(surfel_fset);                                                                                 \
  SURFEL_GROUP ___(group);                                                                                      \
  for (___(igroup) = 0, ___(surfel_fset) = g_surfel_groups[nSHOB_CATEGORIES::WSURFEL_CONDUCTION_TYPES[0]];\
       ___(igroup) < nSHOB_CATEGORIES::N_WSURFEL_CONDUCTION_TYPES;                                              \
       ++___(igroup),                                                                                           \
       ___(surfel_fset) = ___(igroup) < nSHOB_CATEGORIES::N_WSURFEL_CONDUCTION_TYPES ?                          \
       g_surfel_groups[nSHOB_CATEGORIES::WSURFEL_CONDUCTION_TYPES[___(igroup)]] : NULL)                         \
    if(___(surfel_fset)->groups_per_scale())                                                                    \
      for (___(it) = ___(surfel_fset)->groups_of_scale(scale).begin(),                                          \
           ___(end) = ___(surfel_fset)->groups_of_scale(scale).end();                                           \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                  \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var->m_next)

#define DO_WSURFELS_FLOW_OF_SCALE(shob_var, scale)                                                              \
  sSURFEL *shob_var; asINT32 ___(igroup);                                                                       \
  sSURFEL_FSET::ITERATOR ___(it);                                                                               \
  sSURFEL_FSET::ITERATOR ___(end);                                                                              \
  SURFEL_FSET ___(surfel_fset);                                                                                 \
  SURFEL_GROUP ___(group);                                                                                      \
  for (___(igroup) = 0, ___(surfel_fset) = g_surfel_groups[nSHOB_CATEGORIES::WSURFEL_FLOW_TYPES[0]];            \
       ___(igroup) < nSHOB_CATEGORIES::N_WSURFEL_FLOW_TYPES;                                                    \
       ++___(igroup),                                                                                           \
       ___(surfel_fset) = ___(igroup) < nSHOB_CATEGORIES::N_WSURFEL_FLOW_TYPES ?                                \
       g_surfel_groups[nSHOB_CATEGORIES::WSURFEL_FLOW_TYPES[___(igroup)]] : NULL)                               \
    if(___(surfel_fset)->groups_per_scale())                                                                    \
      for (___(it) = ___(surfel_fset)->groups_of_scale(scale).begin(),                                          \
           ___(end) = ___(surfel_fset)->groups_of_scale(scale).end();                                           \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                                  \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var->m_next)

#define DO_SAMPLING_SURFELS_OF_SCALE(shob_var, scale)                                                        \
  sSAMPLING_SURFEL *shob_var; asINT32 ___(igroup);                                                           \
  sSAMPLING_SURFEL_FSET::ITERATOR ___(it);                                                                   \
  sSAMPLING_SURFEL_FSET::ITERATOR ___(end);                                                                  \
  SAMPLING_SURFEL_FSET ___(ss_fset);                                                                         \
  SAMPLING_SURFEL_GROUP ___(group);                                                                          \
  for (___(igroup) = 0, ___(ss_fset) = g_sampling_surfel_groups[nSHOB_CATEGORIES::SAMPLING_SURFEL_TYPES[0]]; \
       ___(igroup) < nSHOB_CATEGORIES::N_SSURFEL_TYPES;                                                      \
       ++___(igroup),                                                                                        \
       ___(ss_fset) = ___(igroup) < nSHOB_CATEGORIES::N_SSURFEL_TYPES ?                                      \
       g_sampling_surfel_groups[nSHOB_CATEGORIES::SAMPLING_SURFEL_TYPES[___(igroup)]] : NULL)                \
    if(___(ss_fset)->groups_per_scale())                                                                     \
      for (___(it) = ___(ss_fset)->groups_of_scale(scale).begin(),                                           \
           ___(end) = ___(ss_fset)->groups_of_scale(scale).end();                                            \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                               \
        for(shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)

#define DO_BSURFELS_OF_SCALE(shob_var, scale)                                                              \
  sBSURFEL *shob_var; asINT32 ___(n_shob_ptrs); asINT32 ___(ifset); asINT32 ___(i);                        \
  sBSURFEL_FSET::ITERATOR ___(it);                                                                         \
  sBSURFEL_FSET::ITERATOR ___(end);                                                                        \
  BSURFEL_FSET ___(bsurfel_fset);                                                                          \
  BSURFEL_GROUP ___(group);                                                                                \
  asINT32 ___(nfsets) = nSHOB_CATEGORIES::N_BSURFEL_TYPES_;                                                \
  asINT32 ___(btype);                                                                                      \
  for (___(ifset) = 0, ___(btype) = nSHOB_CATEGORIES::BSURFEL_TYPES[0],                                    \
      ___(bsurfel_fset) = g_bsurfel_groups[___(btype)];                                                    \
       ___(ifset) < ___(nfsets);                                                                           \
       ++___(ifset), ___(btype) = (___(ifset) < ___(nfsets)) ? nSHOB_CATEGORIES::BSURFEL_TYPES[___(ifset)] \
           : nSHOB_CATEGORIES::BSURFEL_TYPES[0],                                                           \
       ___(bsurfel_fset) = g_bsurfel_groups[___(btype)])                                                   \
    if(___(bsurfel_fset)->groups_per_scale())                                                              \
      for (___(it) = ___(bsurfel_fset)->groups_of_scale(scale).begin(),                                    \
           ___(end) = ___(bsurfel_fset)->groups_of_scale(scale).end();                                     \
           ___(it) != ___(end) && (___(group) = *___(it), TRUE);    ___(it)++)                             \
        for (shob_var = ___(group)->shob_ptrs(); shob_var != NULL; shob_var = shob_var ? shob_var->m_next : NULL)


#define DO_CONTACT_SURFELS_OF_SCALE_TYPES(shob_var, contact_surfel_type, scale)                            \
  DO_CONTACT_SURFELS_GROUPS_OF_SCALE(___(group_var), scale)                                                     \
    DO_CONTACT_SURFELS_OF_GROUP(shob_var, ___(group_var), contact_surfel_type)

#define DO_CONTACT_SURFELS_OF_SCALE(shob_var, scale)                                                       \
  DO_CONTACT_SURFELS_OF_SCALE_TYPES(shob_var, ___(contact_surfel_type), scale)

//Within each group/scale, loops through the contact_surfel type subset of contact_surfels within
#define DO_CONTACT_SURFELS_OF_SCALE_SUBSET(shob_var, scale, subset_type)                                   \
  DO_CONTACT_SURFELS_GROUPS_OF_SCALE(___(group_var), scale)                                                     \
    DO_CONTACT_SURFELS_OF_GROUP_SUBSET(shob_var, ___(group_var), subset_type)


#if BUILD_GPU
namespace GPU {

struct MSFL_GROUP_KEY {
  
  SURFEL_GROUP_TYPE group_type;
  int scale;
  int dest_sp;
  STP_EVEN_ODD even_odd_mask;
  LRF_PHYSICS_DESCRIPTOR lrf_desc;

  friend bool operator<(const MSFL_GROUP_KEY& key1, const MSFL_GROUP_KEY& key2) {
    return \
      std::tie(key1.group_type, key1.scale, key1.dest_sp, key1.even_odd_mask, key1.lrf_desc) <
      std::tie(key2.group_type, key2.scale, key2.dest_sp, key2.even_odd_mask, key2.lrf_desc);
  }
};

struct RANGE_DESCRIPTOR {
  RANGE_DESCRIPTOR(size_t _offset, size_t _count):
    offset(_offset), count(_count) {}
  const uint32_t offset;
  const uint32_t count;
  std::array<size_t, 2> range() const {
    return {offset, offset + count};
  }
};

}//namespace GPU

#endif

#endif // _SHOB_GROUPS_H_
