/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "bsurfel.h"
#include "shob.h"
#include "sim.h"
#include "ublk.h"
#include "lb_solver_data.h"
#include "eqns.h"
#include "sp_timers.h"
#include "ublk_table.h"
#include "algorithm"
#include "ckpt.h"
#include "meas.h"
#include "vr.h"
#include "bsurfel_util.h"
#include "bsurfel_dyn_meas.h"
#include "bsurfel_comm.h"
#include "comm_utils.h"

// Destroys the smart_ptrs, which returns the meas_cells to the
// window if necessary. Then stores the smart pointers in
// the appropriate local memory pool.
sBSURFEL::~sBSURFEL()
{
  if (m_n_meas_cell_refs > 0) {

    ccDOTIMES(n, m_n_meas_cell_refs) {
      m_meas_cell_refs[n].~sMOVING_MEAS_CELL_SMART_PTR();
    }

    sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL &mempool = get_moving_meas_cell_smart_ptr_mem_pool(m_n_meas_cell_refs);
    mempool.push(m_meas_cell_refs);

    m_n_meas_cell_refs = 0;
    m_meas_cell_refs = NULL;
  }

  if (is_deforming()) {
    for (int i = 0; i < 3; i++) {
      m_vertices[i].~sTIRE_VERTEX_SMART_PTR();
    }
  }

  m_id = INVALID_SHOB_ID;
}

VOID sBSURFEL::init(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom,
                    sUBLK_TABLE& ublk_table,
                    dFLOAT char_density,
                    sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs,
                    sMOVB_PHYSICS_DESCRIPTOR* movb_physics_descs,
                    sMEAS_WINDOW_COLLECTION& meas_windows)
{
  m_id = bsurfel_desc->bs.bsurfel_id;
  m_scale = bsurfel_desc->bs.bsurfel_scale;
  m_is_active = true;
  ccDOTIMES(axis, 3) {
    m_centroid[axis] = bsurfel_geom->centroid[axis];
    m_normal[axis] = bsurfel_geom->normal[axis];
  }

  m_ref_frame_index = bsurfel_desc->bs.lrf_index;
  m_lrf_physics_desc = (m_ref_frame_index >= 0) ? &lrf_physics_descs[m_ref_frame_index] : NULL;
  asINT32 physics_desc_index = bsurfel_geom->phys_desc_index;
  m_movb_physics_desc =  &movb_physics_descs[physics_desc_index];

  if (m_movb_physics_desc->is_deforming()) {
    ccDOTIMES(i, 3) {
      m_vertices[i].nullify(); // initialize the union object properly
      m_vertices[i] = m_movb_physics_desc->tire->get_vertex(bsurfel_desc->vertices[i].vertex_index);
    }
  }
  else {
    m_init_centroid = sBG_POINT3d(m_centroid[0], m_centroid[1], m_centroid[2]);
    m_init_normal = sBG_VECTOR3d(m_normal[0], m_normal[1], m_normal[2]);
  }

  m_area = bsurfel_geom->area;
  m_home_ublk = ublk_table.ublk_from_id(bsurfel_desc->bs.ublk_id);
  if (!m_home_ublk) {
    msg_internal_error("Unable to locate home ublk (ID %d) for bsurfel (ID %d)",
                       bsurfel_desc->bs.ublk_id,  m_id);
  }
  m_home_voxel = bsurfel_desc->bs.voxel;
  m_n_meas_cell_refs = bsurfel_desc->bs.num_meas_bsurfel_refs;
  m_face_index = bsurfel_desc->bs.face_index;
  m_sampled_fluid_density = char_density;

  m_next = nullptr;
  m_prev = nullptr;

  deactivate_if_in_solid_voxel();

  m_meas_cell_refs = nullptr;
  if (m_n_meas_cell_refs > 0) {
    sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL &mempool = get_moving_meas_cell_smart_ptr_mem_pool(m_n_meas_cell_refs);

    m_meas_cell_refs = mempool.pop();

    asINT32 n = 0;
    DO_STD_VECTOR(cDGF_BSURFEL_MEAS_SURFEL_REFERENCE, meas_surfel_ref, bsurfel_desc->meas_bsurfel_refs) {
      STP_MEAS_WINDOW_INDEX meas_window_id = meas_surfel_ref.meas_window_index;
      STP_MEAS_CELL_INDEX global_meas_cell_index = meas_surfel_ref.meas_surfel_index;
      MEAS_WINDOW window = meas_windows[meas_surfel_ref.meas_window_index];
      if (!window->is_development && window->n_global_moving_meas_cells == 0) {
        msg_internal_error("Bsurfel %d references window %d which has no moving measurement cells", id(), window->index);
      }
      new (&m_meas_cell_refs[n]) sMOVING_MEAS_CELL_SMART_PTR(); // construct the meas_cell_smart_ptr
      m_meas_cell_refs[n] = window->get_moving_meas_cell(global_meas_cell_index, m_face_index, TRUE);
      n++;
    }
  }

}

VOID sBSURFEL::init(std::vector<unsigned char>::iterator &recv_buffer,
                    sUBLK_TABLE& ublk_table,
                    dFLOAT char_density,
                    sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs,
                    sMOVB_PHYSICS_DESCRIPTOR* movb_physics_descs,
                    sMEAS_WINDOW_COLLECTION& meas_windows)
{
  using LRF_INDEX_TYPE  = decltype(m_lrf_physics_desc->index);
  using MOVB_INDEX_TYPE = decltype(m_movb_physics_desc->index);
  LRF_INDEX_TYPE lrf_index;
  MOVB_INDEX_TYPE movb_index;
  uint32_t vertex_gid[3];
  // These match sSHOB::id
  SHOB_ID home_ublk_id;

  m_prev = NULL;
  m_next = NULL;

  unpack(recv_buffer, &m_id);
  unpack(recv_buffer, &m_scale);
  unpack(recv_buffer, &  lrf_index);
  unpack(recv_buffer, &  movb_index);
  unpack(recv_buffer, &m_face_index);
  unpack(recv_buffer, &  home_ublk_id);
  unpack(recv_buffer, &m_home_voxel);
  unpack(recv_buffer, &m_area);
  unpack(recv_buffer, &m_is_active);

  unpack(recv_buffer,  m_centroid, 3);
  unpack(recv_buffer,  m_normal, 3);
  unpack(recv_buffer,  m_vel, 3);
  unpack(recv_buffer,  m_next_vel, 3);
#if BUILD_D19_LATTICE
  unpack(recv_buffer,  m_centrifugal_force, 3);  //PF
#endif
  unpack(recv_buffer, &m_n_meas_cell_refs);

  m_lrf_physics_desc  = lrf_index >= 0 ? &lrf_physics_descs[lrf_index] : NULL;
  m_movb_physics_desc = &movb_physics_descs[movb_index];
  m_home_ublk = ublk_table.ublk_from_id(home_ublk_id);

  if (!m_is_active) {
    sHOME_UBLK_LOCATOR finder(m_home_ublk, m_home_voxel, m_centroid, true);
    finder.locate_unpacked_bsurfel();
    
    if (finder.ublk() != NULL) {
      m_home_ublk = finder.ublk();
      m_home_voxel = finder.voxel();
      deactivate_if_in_solid_voxel();
    }
  }

  if (!m_home_ublk) {
    msg_internal_error("Unable to locate home ublk (ID %d) for bsurfel (ID %d)", home_ublk_id, m_id);
  }

  // It is possible for the vertex to be uninitialized at this point
  // but that is okay, because the new position for this bsurfel was computed
  // before it was sent. The vertex values are contained later on in this buffer
  if (is_deforming()) {
    for (int i = 0; i < 3; i++) {
      unpack(recv_buffer, &vertex_gid[i]);
    }

    sSIM_TIRE* tire = m_movb_physics_desc->tire;
    for (int i = 0; i < 3; i++) {
      this->m_vertices[i].nullify(); // initialize the union object properly
      this->m_vertices[i] = tire->get_vertex(vertex_gid[i]);
    }
  }
  else {
    unpack(recv_buffer, &m_init_centroid);
    unpack(recv_buffer, &m_init_normal);
  }

  m_meas_cell_refs = nullptr;
  if (m_n_meas_cell_refs > 0) {
    sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL &mempool = get_moving_meas_cell_smart_ptr_mem_pool(m_n_meas_cell_refs);

    m_meas_cell_refs = mempool.pop();

    ccDOTIMES(n, m_n_meas_cell_refs) {
      STP_MEAS_WINDOW_INDEX meas_window_id;
      STP_MEAS_CELL_INDEX global_meas_cell_index;
      uINT32 n_variables;
      unpack(recv_buffer, &meas_window_id);
      unpack(recv_buffer, &global_meas_cell_index);
      unpack(recv_buffer, &n_variables);
      MEAS_WINDOW window = meas_windows[meas_window_id];
      new (&m_meas_cell_refs[n]) sMOVING_MEAS_CELL_SMART_PTR(); // reset the meas_cell_smart_ptr
      m_meas_cell_refs[n] = window->get_moving_meas_cell(global_meas_cell_index, m_face_index, FALSE);

      // if n_variables > 0, the moving meas cell has piggybacked on this bsurfel,
      // so add those values to the moving meas cell on this process
      if (n_variables > 0) {
        MEAS_CELL_VAR * meas_cell_vars = m_meas_cell_refs[n].variables();
        meas_cell_vars++; // skip the header
        ccDOTIMES(i, n_variables) {
          MEAS_CELL_VAR tmp;
          unpack(recv_buffer, &tmp);
          *meas_cell_vars += tmp;
          meas_cell_vars++;
        }
      }
    }
  }
}

VOID sBSURFEL::fill_send_buffer(cNEIGHBOR_SP nsp, std::vector<unsigned char> &send_buffer)
{
  using LRF_INDEX_TYPE = decltype(m_lrf_physics_desc->index);
  SHOB_ID home_ublk_id = m_home_ublk->id();
  LRF_INDEX_TYPE lrf_index = m_lrf_physics_desc ? m_lrf_physics_desc->index : -1;
  // Note: The following order must be maintained in the corresponding init() method
  pack(send_buffer, &m_id);
  pack(send_buffer, &m_scale);
  pack(send_buffer, &lrf_index);
  pack(send_buffer, &m_movb_physics_desc->index);
  pack(send_buffer, &m_face_index);
  pack(send_buffer, &  home_ublk_id);
  pack(send_buffer, &m_home_voxel);
  pack(send_buffer, &m_area);
  pack(send_buffer, &m_is_active);

  pack(send_buffer,  m_centroid, 3);
  pack(send_buffer,  m_normal, 3);
  pack(send_buffer,  m_vel, 3);
  pack(send_buffer,  m_next_vel, 3);
#if BUILD_D19_LATTICE
  pack(send_buffer,  m_centrifugal_force, 3);
#endif
  pack(send_buffer, &m_n_meas_cell_refs);

  if (is_deforming()) {
    sSIM_TIRE * tire = m_movb_physics_desc->tire;
    for (int i = 0; i < 3; i++) {
      pack(send_buffer, &(m_vertices[i]->id));
      tire->vertex_needs_packing(nsp, std::move(m_vertices[i])); // we don't need this smart pointer anymore
    }
  }
  else {
    pack(send_buffer, &m_init_centroid);
    pack(send_buffer, &m_init_normal);
  }

  ccDOTIMES(n, m_n_meas_cell_refs) {
    STP_MEAS_WINDOW_INDEX window_id = m_meas_cell_refs[n].window_id();
    STP_MEAS_CELL_INDEX global_meas_cell_index = m_meas_cell_refs[n].global_meas_cell_index();
    MEAS_WINDOW window = g_meas_windows[window_id];
    uINT32 n_variables = window->n_variables;

    if (window->is_development || !m_meas_cell_refs[n].is_last_ref()) {
      n_variables = 0;
    }

    pack(send_buffer, &window_id);
    pack(send_buffer, &global_meas_cell_index);
    pack(send_buffer, &n_variables);

    if (n_variables > 0) {
      MEAS_CELL_VAR * meas_cell_vars = m_meas_cell_refs[n].variables();
      meas_cell_vars++; // skip the header information
      pack(send_buffer, meas_cell_vars, n_variables);
    }
  }
}

VOID sBSURFEL::update_position(std::vector<sBSURFEL::sSEND_LIST_QUANTUM> &send_list,
                               std::vector<sBSURFEL::sMOVE_LIST_QUANTUM> &move_list)
{
  ROTATING_TIRE_PARAMETERS parms = m_movb_physics_desc->parameters();

  STP_REF_FRAME_INDEX vel_ref_frame_index = parms->ref_frame.value;
  STP_REF_FRAME_INDEX group_ref_frame_index = m_lrf_physics_desc ? m_lrf_physics_desc->index : -1;

  // NOTE: The default ref frame index (-1) in the physics descriptor is
  // interpreted to be the "containing" ref frame instead of the global
  // body-fixed reference frame
  if ((group_ref_frame_index >= 0) && (vel_ref_frame_index == group_ref_frame_index)) {
    // bsurfel relative angular velocity is zero, so bsurfel positions are
    // unchanged in its current ref frame. The home ublk/voxel is unchanged.
    // See NOTE: in the dynamics function
    return;
  }

  // update bsurfel normal and centroid due to rotation
  sBG_POINT3d new_centroid(0, 0, 0);
  sBG_VECTOR3d new_normal(0, 0, 0);
  if (!is_deforming()) {
    new_centroid = m_movb_physics_desc->motion_xform.Transform(m_init_centroid);
    new_normal = m_movb_physics_desc->motion_xform.Transform(m_init_normal);
    ccDOTIMES(axis, 3) {
      m_centroid[axis] = new_centroid[axis];
      m_normal[axis] = new_normal[axis];

    }
  }
  else {

    // For a deforming tire, we assume the points have already been rotated
    // into their new position. We then compute the new centroid and normal
    sBG_POINT3d v0 = m_vertices[0]->xyz;
    sBG_POINT3d v1 = m_vertices[1]->xyz;
    sBG_POINT3d v2 = m_vertices[2]->xyz;

    sBG_VECTOR3d d0 = v1 - v0;
    sBG_VECTOR3d d1 = v2 - v0;

    new_centroid += v0;
    new_centroid += v1;
    new_centroid += v2;
    new_centroid = new_centroid / 3.0;
    new_normal = Tire::cross(d0, d1);
    dFLOAT new_area = Tire::norm(new_normal);

    // A deforming bsurfel can become degenerate
    // We flush the area to 0 if this is the case
    // and deactivate the bsurfel later if this is true
    if ( Tire::almost_equal(new_area,0.0,4) ) {
      new_area = 0.0;
    }
    else {
      new_normal /= new_area;
    }
    new_area *= 0.5;

    LOG_MSG_IF((this->id() == 14828), "BSURFEL_COMM", LOG_TS, "bsurfel", this->id()).format("Update position\n"
        "vertices {} {} {}\n"
        "old centroid {} {} {}\n"
        "new centroid {} {} {}\n",
        m_vertices[0]->id,
        m_vertices[1]->id,
        m_vertices[2]->id,
        m_centroid[0],
        m_centroid[1],
        m_centroid[2],
        new_centroid[0],
        new_centroid[1],
        new_centroid[2]);

    ccDOTIMES(axis, 3) {
      m_next_vel[axis] = (new_centroid[axis] - m_centroid[axis])/1.0; // we have to divide by the timestep, which for now is always 1
      m_centroid[axis] = new_centroid[axis];
      m_normal[axis] = new_normal[axis];
    }
    m_area = new_area;
  }


  STP_PROC new_home_sp = locate_new_home_ublk_and_voxel();
  
  LOG_MSG_IF(this->id() == 14828, "BSURFEL_COMM", LOG_TS, "bsurfel", id()).format("Home ublk {} voxel {} location {} {} {}",m_home_ublk->id(),m_home_voxel,m_home_ublk->location(0),m_home_ublk->location(1), m_home_ublk->location(2));

  if (m_home_ublk->is_ghost()) {
    add_self_to_send_list(send_list);
  }  else if (new_home_sp !=my_proc_id && new_home_sp > -1) {
    add_self_to_send_list_from_box(send_list, new_home_sp);
  }  else if (m_is_active) {
    BSURFEL_GROUP_TYPE new_group = activate_bsurfel_neighbor_ublks();
    maybe_add_self_to_move_list(new_group, move_list);
  }

}

VOID sBSURFEL::add_self_to_send_list(std::vector<sBSURFEL::sSEND_LIST_QUANTUM> &send_list)
{
  sBSURFEL_SEND_GROUP signature(g_strand_mgr.m_neighbor_sp_map.get_nsp(m_home_ublk->home_sp()), FINEST_SCALE);
  BSURFEL_SEND_GROUP send_group = g_bsurfel_comm_send_fset.find_group(&signature);
  cassert(send_group);
  send_list.emplace_back(this, send_group);
}

VOID sBSURFEL::add_self_to_send_list_from_box(std::vector<sBSURFEL::sSEND_LIST_QUANTUM> &send_list, STP_PROC dest_rank)
{
  sBSURFEL_SEND_GROUP signature(g_strand_mgr.m_neighbor_sp_map.get_nsp(dest_rank), FINEST_SCALE);
  BSURFEL_SEND_GROUP send_group = g_bsurfel_comm_send_fset.find_group(&signature);
  cassert(send_group);
  send_list.emplace_back(this, send_group);
}

VOID sBSURFEL::maybe_add_self_to_move_list(BSURFEL_GROUP_TYPE new_group_type, std::vector<sBSURFEL::sMOVE_LIST_QUANTUM> &move_list)
{
  BSURFEL_FSET new_fset = g_bsurfel_groups[new_group_type];
  sBSURFEL_GROUP * new_group = new_fset->create_group(scale());
  if (new_group != m_group) {
    move_list.emplace_back(this, new_group);
  }
}

VOID sBSURFEL::move_self_to_new_group(BSURFEL_GROUP_TYPE new_group_type)
{
  BSURFEL_FSET new_fset = g_bsurfel_groups[new_group_type];
  sBSURFEL_GROUP * new_group = new_fset->create_group(scale());
  if (new_group != m_group) {
    sMOVE_LIST_QUANTUM q(this, new_group);
    q.execute();
  }
}

BSURFEL_GROUP_TYPE sBSURFEL::activate_bsurfel_neighbor_ublks(bool check_all_ublks_are_fringe)
{
  sSET_VALID_BSURFEL_NEIGHBOR_UBLKS setter(m_home_voxel, check_all_ublks_are_fringe);
  setter.set_bsurfel_interacting_on_neighbor_ublks(m_home_ublk);
  return setter.group_type();
}

VOID sBSURFEL::deactivate_if_in_solid_voxel()
{
  if (!m_home_ublk->fluid_like_voxel_mask.test(m_home_voxel)) {
    m_is_active = FALSE;
  }
  else {
    if ( m_area == 0.0 ) {
      m_is_active = FALSE;
    }
    else {
      m_is_active = TRUE;
    }
  }
}

STP_PROC sBSURFEL::locate_new_home_ublk_and_voxel()
{
  STP_PROC new_home_sp = my_proc_id;
  try {
    sHOME_UBLK_LOCATOR finder(m_home_ublk, m_home_voxel, m_centroid, true);
    new_home_sp = finder.locate();

    if (finder.ublk()==NULL) {
      m_is_active = FALSE;
    } else {
      m_home_ublk = finder.ublk();
      m_home_voxel = finder.voxel();
      // LOG_ON_IF(id() == 40267,"BSURFEL",LOG_TS,LOG_ATTR(m_home_ublk->id()),LOG_ATTR(m_home_voxel));

      if (m_home_ublk->is_mirror()) {
        throw sHOME_UBLK_LOCATOR::SearchError(fmt::format("Bsurfel crossed a symmetry plane! Ublk {}", m_home_ublk->id()));
      }

      deactivate_if_in_solid_voxel();
      LOG_MSG_IF(id() == 0, "BSURFEL_INIT", LOG_TS, LOG_FUNC).format("Centroid: ({} {} {})", m_centroid[0], m_centroid[1], m_centroid[2]);
    }
  }
  catch (sHOME_UBLK_LOCATOR::SearchError& msg) {
    // msg << "=== sBSURFEL::locate_new_home_ublk_and_voxel() ===\n";
    msg << "==========\n";
    msg << "Bsurfel Id: " << this->id() << '\n';
    msg_internal_error("%s",msg.what());
  }
  return new_home_sp;
}

VOID sBSURFEL::calculate_ref_frame_vel_and_pt(dFLOAT ref_frame_vel[3], dFLOAT ref_pt[3])
{

  ROTATING_TIRE_PARAMETERS parms = m_movb_physics_desc->parameters();

  ref_pt[0] = parms->axis_point[0].value;
  ref_pt[1] = parms->axis_point[1].value;
  ref_pt[2] = parms->axis_point[2].value;

  STP_REF_FRAME_INDEX vel_ref_frame_index = parms->ref_frame.value;
  STP_REF_FRAME_INDEX bsurfel_ref_frame_index = m_lrf_physics_desc ? m_lrf_physics_desc->index : SRI_GLOBAL_REF_FRAME_INDEX;

  // NOTE: The default ref frame index (-1) in the physics descriptor is
  // interpreted to be the "containing" ref frame instead of the global
  // body-fixed reference frame
  // TODO: If the rotation is defined via a ref frame, then the relative
  // angular velocity of the bsurfels in the specified ref frame is zero since
  // they share the same ref pt and angular velocity as the ref frame.  During
  // general motion, for example for wipers, bsurfels may transit through this
  // ref frame, so the check below needs to be augmented.
  if ((bsurfel_ref_frame_index >= 0) && (vel_ref_frame_index == bsurfel_ref_frame_index)) {
    // bsurfel relative angular velocity is zero
    ccDOTIMES(axis, 3)  {
      ref_frame_vel[axis] = 0.0F;
    }
  }
  else {
    ccDOTIMES(axis, 3) {
      ref_frame_vel[axis] = parms->axis_unit_vec[axis].value * parms->angular_vel.value;
    }
  }
}

sBSURFEL::sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL &
sBSURFEL::get_moving_meas_cell_smart_ptr_mem_pool(sINT16 n_meas_cell_refs)
{
  static std::map < sINT16, sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL* > meas_cell_refs_pools;
  sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL *mempool = NULL;
  std::map < sINT16, sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL* >::iterator pool = meas_cell_refs_pools.find(n_meas_cell_refs);

  if (pool == meas_cell_refs_pools.end()) {
    mempool = new sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL(n_meas_cell_refs, 100);
    meas_cell_refs_pools.insert(std::make_pair(n_meas_cell_refs, mempool));
  }
  else {
    mempool = pool->second;
  }

  return *mempool;
}

