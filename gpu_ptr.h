#ifndef EXA_SIMENG_GPU_PTR_H
#define EXA_SIMENG_GPU_PTR_H
#if BUILD_GPU

namespace GPU {

// GPU::Ptr behaves, in almost all respects, like a normal pointer type,
// including pointer arithmetic, conversions, comparisons, etc.  It is a
// non-owning pointer (a dumb pointer), and points to memory on the GPU. Memory
// is allocated using the GPU::malloc<T>() function and freed using the
// GPU::free() function.
//
// Casting the pointer is possible with static_ptr_cast<U>().
// Don't add a dynamic_ptr_cast() function! This won't work because the GPU::Ptr
// is handling memory on the GPU, not the CPU. In a similar manner, virtual
// inheritance will not work through GPU::Ptr.
//
// Memory can be copied between the host and device using the GPU::memcpy_h2d()
// and GPU::memcpy_d2h(), although you should prefer GPU::copy_h2d<T>() and
// GPU::copy_d2h<T>(), which are convenience wrappers for computing the sizes
// of type T.
//
// It is possible to have a GPU::Ptr that refers to another GPU::Ptr. This
// is a handle to a pointer that lives on the device, and is equivalent to a 
// handle to a T*:
//
// GPU::Ptr< GPU::Ptr<T> > is equivalent to GPU::Ptr< T* >. 
// This relationship is recursive.

namespace detail {
template<typename T> struct is_gpu_ptr { static constexpr bool value = false; };
template<typename T> struct is_gpu_ptr<Ptr<T>>{ static constexpr bool value = true; };
template<typename T> struct gpu_ptr_inner_type { using type = typename std::conditional<is_gpu_ptr<T>::value, T, T*>::type; };
template<typename T> struct gpu_ptr_raw_type { using type = T*; };
template<typename T> struct gpu_ptr_raw_type<Ptr<T>> { using type = typename gpu_ptr_raw_type<T>::type*; };
}

template<typename T>
class Ptr
{

public:
  using Type    = typename detail::gpu_ptr_inner_type<T>::type;
  using RawType = typename detail::gpu_ptr_raw_type<T>::type;

  __HOST__DEVICE__ Ptr() { _ptr = nullptr; };
  __HOST__DEVICE__ Ptr(std::nullptr_t) { _ptr = nullptr; }
  __HOST__DEVICE__ explicit Ptr(Type ptr) : _ptr(ptr) {}

  // implicit conversion from another type of pointer
  template<typename U> friend class Ptr;

  template<typename U>
    Ptr(Ptr<U> other)
    {
      _ptr = other.get();
    }

  template<typename U>
    Ptr& operator = (Ptr<U> other)
    {
      _ptr = other._ptr;
      return *this;
    }

  Ptr(const Ptr&) = default;
  Ptr(Ptr&&) = default;
  Ptr& operator = (const Ptr&) = default;
  Ptr& operator = (Ptr&&) = default;
  ~Ptr() = default;

  // conversion to bool
  __HOST__DEVICE__
    explicit operator bool() const { return bool(_ptr); }

  // set to null
  __HOST__DEVICE__
    Ptr& operator = (std::nullptr_t) {
      _ptr = nullptr;
      return *this;
    }

  // A nicer cast syntax
  template<typename U>
    __HOST__DEVICE__
    Ptr<U> static_ptr_cast() 
    { 
      return Ptr<U>(static_cast<U*>(_ptr)); 
    }

  // A nicer cast syntax
  template<typename U>
    __HOST__DEVICE__
    Ptr<U> reinterpret_ptr_cast() 
    { 
      return Ptr<U>(reinterpret_cast<U*>(_ptr)); 
    }

  __HOST__DEVICE__
    Ptr<const T> add_const() const
    { 
      return Ptr<const T>(_ptr); 
    }

  // Device stuff
  __DEVICE__
    const T& operator * () const
    {
      return *_ptr;
    }

  __DEVICE__
    T& operator * ()

    {
      return *_ptr;
    }

  __DEVICE__
    const T& operator [] (size_t i) const
    {
      return _ptr[i];
    }

  __DEVICE__
    T& operator [] (size_t i) 
    {
      return _ptr[i];
    }

  // Pointer arithmetic. In the case of a double device pointer, we need to
  // operate on the inner most raw pointer to get the correct size
  
  template<typename I>
    __HOST__DEVICE__
    friend Ptr<T> operator + (Ptr<T> ptr, I N)
    {
      return Ptr<T>(ptr.get() + N);
    }

  template<typename I>
    __HOST__DEVICE__
    friend Ptr<T> operator + (I N, Ptr<T> ptr)
    {
      return Ptr<T>(ptr.get() + N);
    }

  template<typename I>
    __HOST__DEVICE__
    friend Ptr<T> operator - (Ptr<T> ptr, I N)
    {
      return Ptr<T>(ptr.get() - N);
    }

  __HOST__DEVICE__
    friend ptrdiff_t operator - (Ptr<T> ptr1, Ptr<T> ptr2)
    {
      return ptr1.get() - ptr2.get();
    }

  __HOST__DEVICE__
    friend Ptr<T>& operator ++ (Ptr<T>& ptr)
    {
      ptr = ptr + 1;
      return ptr;
    }

  __HOST__DEVICE__
    friend Ptr<T> operator ++ (Ptr<T> ptr, int)
    {
      Ptr<T> tmp(ptr);
      ptr = ptr + 1;
      return ptr;
    }

  __HOST__DEVICE__ RawType get()
  {
    RawType ptr;
    static_assert(sizeof(ptr) == sizeof(_ptr));
    std::memcpy(&ptr, &_ptr, sizeof(_ptr));
    return ptr;
  }

  __HOST__DEVICE__
    friend bool operator < (Ptr<T> a, Ptr<T> b)
    {
      return a.get() < b.get();
    }

  __HOST__DEVICE__
    friend bool operator <= (Ptr<T> a, Ptr<T> b)
    {
      return a.get() <= b.get();
    }

  __HOST__DEVICE__
    friend bool operator > (Ptr<T> a, Ptr<T> b)
    {
      return a.get() > b.get();
    }

  __HOST__DEVICE__
    friend bool operator >= (Ptr<T> a, Ptr<T> b)
    {
      return a.get() >= b.get();
    }

  __HOST__DEVICE__
    friend bool operator == (Ptr<T> a, Ptr<T> b)
    {
      return a.get() == b.get();
    }

  __HOST__DEVICE__
    friend bool operator != (Ptr<T> a, Ptr<T> b)
    {
      return a.get() != b.get();
    }

private:

  Type _ptr;

};

// Const Void pointer specialization
template<>
class Ptr<const void>
{
  const void * _ptr;

public:
  __HOST__DEVICE__ Ptr() { _ptr = nullptr; };
  __HOST__DEVICE__ Ptr(std::nullptr_t) { _ptr = nullptr; }
  __HOST__DEVICE__ explicit Ptr(const void* ptr) : _ptr(ptr) {}

  // implicit conversion from another type of pointer
  template<typename U> friend class Ptr;
  template<typename U>
    Ptr(Ptr<U> other)
    {
      _ptr = other.get();
    }

  template<typename U>
    Ptr& operator = (Ptr<U> other)
    {
      _ptr = other._ptr;
      return *this;
    }

  Ptr(const Ptr&) = default;
  Ptr(Ptr&&) = default;
  Ptr& operator = (const Ptr&) = default;
  Ptr& operator = (Ptr&&) = default;
  ~Ptr() = default;

  __HOST__DEVICE__
    explicit operator bool() const { return bool(_ptr); }

  __HOST__DEVICE__
    Ptr& operator = (std::nullptr_t) {
      _ptr = nullptr;
      return *this;
    }

  __HOST__DEVICE__
    friend bool operator < (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() < b.get();
    }

  __HOST__DEVICE__
    friend bool operator <= (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() <= b.get();
    }

  __HOST__DEVICE__
    friend bool operator > (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() > b.get();
    }

  __HOST__DEVICE__
    friend bool operator >= (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() >= b.get();
    }

  __HOST__DEVICE__
    friend bool operator == (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() == b.get();
    }

  __HOST__DEVICE__
    friend bool operator != (Ptr<const void> a, Ptr<const void> b)
    {
      return a.get() != b.get();
    }

  // A nicer cast syntax
  template<typename U>
    __HOST__DEVICE__
    Ptr<U> static_ptr_cast() 
    { 
      return Ptr<U>(static_cast<U*>(_ptr)); 
    }

  __HOST__DEVICE__
    Ptr<const void> add_const() const
    { 
      return *this;
    }

  __HOST__DEVICE__ const void * get() { return _ptr; }
};


// Void Pointer Specialization
template<>
class Ptr<void>
{
  void * _ptr;

public:
  __HOST__DEVICE__ Ptr() { _ptr = nullptr; };
  __HOST__DEVICE__ Ptr(std::nullptr_t) { _ptr = nullptr; }
  __HOST__DEVICE__ explicit Ptr(void* ptr) : _ptr(ptr) {}

  // implicit conversion from another type of pointer
  template<typename U> friend class Ptr;
  template<typename U>
    Ptr(Ptr<U> other)
    {
      _ptr = other.get();
    }

  template<typename U>
    Ptr& operator = (Ptr<U> other)
    {
      _ptr = other._ptr;
      return *this;
    }

  Ptr(const Ptr&) = default;
  Ptr(Ptr&&) = default;
  Ptr& operator = (const Ptr&) = default;
  Ptr& operator = (Ptr&&) = default;
  ~Ptr() = default;

  __HOST__DEVICE__
    explicit operator bool() const { return bool(_ptr); }

  __HOST__DEVICE__
    Ptr& operator = (std::nullptr_t) {
      _ptr = nullptr;
      return *this;
    }

  __HOST__DEVICE__
    friend bool operator < (Ptr<void> a, Ptr<void> b)
    {
      return a.get() < b.get();
    }

  __HOST__DEVICE__
    friend bool operator <= (Ptr<void> a, Ptr<void> b)
    {
      return a.get() <= b.get();
    }

  __HOST__DEVICE__
    friend bool operator > (Ptr<void> a, Ptr<void> b)
    {
      return a.get() > b.get();
    }

  __HOST__DEVICE__
    friend bool operator >= (Ptr<void> a, Ptr<void> b)
    {
      return a.get() >= b.get();
    }

  __HOST__DEVICE__
    friend bool operator == (Ptr<void> a, Ptr<void> b)
    {
      return a.get() == b.get();
    }

  __HOST__DEVICE__
    friend bool operator != (Ptr<void> a, Ptr<void> b)
    {
      return a.get() != b.get();
    }

  // A nicer cast syntax
  template<typename U>
    __HOST__DEVICE__
    Ptr<U> static_ptr_cast() 
    { 
      return Ptr<U>(static_cast<U*>(_ptr)); 
    }

  __HOST__DEVICE__
    Ptr<const void> add_const() const
    { 
      return Ptr<const void>(_ptr); 
    }

  __HOST__DEVICE__ void * get() { return _ptr; }
};

using sDEV_PTR = GPU::Ptr<void>;
using sDEV_PTR_TO_DEV_PTR = GPU::Ptr<GPU::Ptr<void>>;

// Allocates device memory with cudaMalloc().
//
// Warning: cudaMalloc() implicitly synchronizes the host thread and the
// device, and all attached peer devices, so can have a surprising performance
// cost, which is quadratic with the number of peer devices. We will be working
// on an API to eliminate this bottleneck.
//
// @param nbytes Number of bytes to allocate.
// @param tag String to be written to logfile
  
Ptr<void> malloc(size_t nbytes, const std::string& tag = "");

// Type-safe device malloc. Automatically computes the size of the object,
// allocates memory, and returns a GPU::Ptr to the resulting allocation.
//
// @param count The number of objects of type T to allocate.  
//              Warning: This is not the number of bytes!
// @param tag String to be written to logfile

template<typename T>
Ptr<T> malloc(size_t count, const std::string& tag = "")
{
  return GPU::malloc(sizeof(T)*count, tag).static_ptr_cast<T>();
}

// Frees GPU memory with cudaFree().
//
// The pointer is set to nullptr after memory is successfully freed.
//
// Warning: cudaFree() implicitly synchronizes the host thread and the device,
// and all attached peer devices, so can have a surprising peformance cost,
// which is quadratic with the number of peer devices. We will be working on an
// API to eliminate this bottleneck.

void free(Ptr<void>& ptr, const std::string& tag);

template<typename T>
void free(Ptr<T>& ptr, const std::string& tag) {
  Ptr<void> p(ptr);
  GPU::free(p, tag);
  ptr = nullptr;
}

// cudaMemcpy() from the device to the host using the default cuda stream 
//
// Warning: This will not sync with the compute stream (GPU::g_stream) or the
// comm stream (GPU::g_comm_stream). The copy operation could overlap with
// running kernels and give you garbage data! 
//
// If you need to sync with these streams, use cudaStreamSynchronize(), which
// will sync with a single stream, or use cudaDeviceSynchronize(), which will
// wait for all streams to finish their scheduled operations.  

void memcpy_d2h(void * h, Ptr<const void> d, size_t nbytes);

// cudaMemcpy() from the host to the device using the default cuda stream
//
// Warning: This will not sync with the compute stream (GPU::g_stream) or the
// comm stream (GPU::g_comm_stream). The copy operation could overlap with
// running kernels and give you garbage data!
//
// If you need to sync with these streams, use cudaStreamSynchronize(), which
// will sync with a single stream, or use cudaDeviceSynchronize(), which will
// wait for all streams to finish their scheduled operations.  

void memcpy_h2d(Ptr<void> d, const void * h, size_t nbytes);

// cudaMemcpy() from the host to the device using the default cuda stream
//
// Warning: This will not sync with the compute stream (GPU::g_stream) or the
// comm stream (GPU::g_comm_stream). The copy operation could overlap with
// running kernels and give you garbage data!
//
// If you need to sync with these streams, use cudaStreamSynchronize(), which
// will sync with a single stream, or use cudaDeviceSynchronize(), which will
// wait for all streams to finish their scheduled operations.  

void memcpy_d2d(Ptr<void> dst, Ptr<const void> src, size_t nbytes);

// Convenience GPU::memcpy_h2d(). Automatically computes the number of
// bytes to be copied from T and N.
//
// @param N Number of objects to be copied. Not the number of bytes!
//
// Warning: This does not check to see if T is trivially_copyable.
//          Warnings from GPU::memcpy_h2d(Ptr<void>, const void *, size_t)
//          regarding streams still apply.
template<typename T>
void copy_h2d(Ptr<T> d, const T* h, size_t N)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_h2d() cannot be used with void pointers.\n"
                                             "Use GPU::memcpy_h2d() instead\n\n");
  memcpy_h2d(d, h, sizeof(T)*N);
}

// Convenience GPU::memcpy_d2h(). Automatically computes the number of
// bytes to be copied.
//
// @param N Number of objects to be copied. Not the number of bytes!
//
// Warning: This does not check to see if T is trivially_copyable.
//          Warnings from GPU::memcpy_d2h(void *, Ptr<const T>, size_t)
//          regarding streams still apply.
template<typename T>
void copy_d2h(T* h, Ptr<const T> d, size_t N)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_d2h() cannot be used with void pointers.\n"
                                              "Use GPU::memcpy_h2d() instead\n\n");
  memcpy_d2h(h, d, sizeof(T)*N);
}

// Convenience GPU::memcpy_d2d(). Automatically computes the number of
// bytes to be copied.
template<typename T>
void copy_d2d(Ptr<T> dst, Ptr<const T> src, size_t N)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_d2d() cannot be used with void pointers.\n"
                                              "Use GPU::memcpy_d2d() instead\n\n");
  memcpy_d2d(dst, src, sizeof(T)*N);
}

// Wrapper around cudaMemset().
//
// Warning: This will not sync with the compute stream (GPU::g_stream) or the
// comm stream (GPU::g_comm_stream). The memset operation could overlap with
// running kernels and give you garbage data! 
//
// If you need to sync with these streams, use cudaStreamSynchronize(), which
// will sync with a single stream, or use cudaDeviceSynchronize(), which will
// wait for all streams to finish their scheduled operations.  
//
// You can also use the memset_async() functions below.
void memset(Ptr<void> d, int value, size_t nbytes);

// Convienence method to zero out a block of memory of type T, 
// Wrapper around GPU::memset();
//
// @param N Number of objects to be zeros. Not the number of bytes!
//
// Warnings from GPU::memset() apply.
template<typename T>
void zero(Ptr<T> d, size_t N) {
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::zero() cannot be used with void pointers.\n"
                                              "Use GPU::memset() instead\n\n");
  GPU::memset(d, 0, sizeof(T)*N);
}

// We keep track of the total amount of memory allocated on the GPU.
// This will output the current memory usage.
void output_mem_usage();

// Wrapper around cudaMallocHost().
void * malloc_host_page_locked(size_t nbytes);

// Convenience method to allocate page-locked host memory for objects of type T.
//
// @param N Number of objects to allocate. Not the number of bytes!
template<typename T>
T* malloc_host_page_locked(size_t N) {
  return static_cast<T*>(malloc_host_page_locked(sizeof(T)*N));
}

#if GPU_COMPILER
#include <cuda.h>
#include <cuda_runtime.h>

// Wrapper around cudaMemsetAsync().
void memset_async(Ptr<void> d, int value, size_t nbytes, cudaStream_t stream);

// Convienence wrapper around cudaMemsetAsync() for zeroing out a block of memory.
//
// @param N Number of objects to be zeros. Not the number of bytes!
template<typename T>
void zero_async(Ptr<T> d, size_t N, cudaStream_t stream) {
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::zero_async() cannot be used with void pointers.\n"
                                              "Use GPU::memset_async() instead\n\n");
  GPU::memset_async(d, 0, sizeof(T)*N, stream);
}

// cudaMemcpyAsync() from the device to the host using the given cuda stream

void memcpy_d2h_async(void * h, Ptr<const void> d, size_t nbytes, cudaStream_t stream);

// cudaMemcpy() from the host to the device using the given cuda stream

void memcpy_h2d_async(Ptr<void> d, const void * h, size_t nbytes, cudaStream_t stream);

// cudaMemcpy() from the device to the device using the given cuda stream

void memcpy_d2d_async(Ptr<void> dst, Ptr<const void> src, size_t nbytes, cudaStream_t stream);

// Convenience GPU::memcpy_h2d_async(). Automatically computes the number of
// bytes to be copied from T and N.
//
// @param N Number of objects to be copied. Not the number of bytes!
//
// Warning: This does not check to see if T is trivially_copyable.
template<typename T>
void copy_h2d_async(Ptr<T> d, const T* h, size_t N, cudaStream_t stream)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_h2d_async() cannot be used with void pointers.\n"
                                             "Use GPU::memcpy_h2d_async() instead\n\n");
  memcpy_h2d_async(d, h, sizeof(T)*N, stream);
}

// Convenience GPU::memcpy_d2h_async(). Automatically computes the number of
// bytes to be copied.
//
// @param N Number of objects to be copied. Not the number of bytes!
//
// Warning: This does not check to see if T is trivially_copyable.
template<typename T>
void copy_d2h_async(T* h, Ptr<const T> d, size_t N, cudaStream_t stream)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_d2h_async() cannot be used with void pointers.\n"
                                              "Use GPU::memcpy_h2d_async instead\n\n");
  memcpy_d2h_async(h, d, sizeof(T)*N, stream);
}

template<typename T>
void copy_d2d_async(Ptr<T> dst, Ptr<const T> src, size_t N, cudaStream_t stream)
{
  static_assert(!std::is_same<T,void>::value, "\n\nGPU::copy_d2d_async() cannot be used with void pointers.\n"
                                              "Use GPU::memcpy_d2d_async instead\n\n");
  memcpy_d2d_async(dst, src, sizeof(T)*N, stream);
}

#endif
}
#endif
#endif
