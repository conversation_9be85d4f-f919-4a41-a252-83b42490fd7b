/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Swap advect API
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Mukul Rao, Exa Corporation                      Created Mon, May 2018
//----------------------------------------------------------------------------
//
#ifndef SWAP_ADVECT_H_
#define SWAP_ADVECT_H_
#include "box_advect.h"

inline namespace ADVECT_VECTORIZED {
template <BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_TWO_COPY_NBR>
VOID swap_advect(sUBLK *dest_ublk,
                 VOXEL_MASK_8 voxel_mask,
		 UBLK (&active_nbrs) [N_MOVING_STATES],
                 BOOLEAN is_timestep_even,
                 SOLVER_INDEX_MASK prior_solver_index_mask,
                 ACTIVE_SOLVER_MASK active_solver_mask);
}

inline namespace ADVECT_SCALAR {
template <BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_TWO_COPY_NBR>
VOID swap_advect(sUBLK *dest_ublk,
                 VOXEL_MASK_8 voxel_mask,
		 UBLK (&active_nbrs) [N_MOVING_STATES],
                 BOOLEAN is_timestep_even,
                 SOLVER_INDEX_MASK prior_solver_index_mask,
                 ACTIVE_SOLVER_MASK active_solver_mask);
}

// swap the states
template<BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
VOID swap_along_latvec_pairs(VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],
			     VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS]){
  VOXEL_STATE tmp[8];
  ccDOTIMES(latvec_pair, N_LATTICE_VECTOR_PAIRS) {
    ccDOTIMES(v, ubFLOAT::N_VOXELS) {
      tmp[v] = states[latvec_pair * 2][v];
      states[latvec_pair * 2][v] = states[latvec_pair * 2 + 1][v];
      states[latvec_pair * 2 + 1][v] = tmp[v];
    }
  }

  if (ADVECT_TEMP) {
    ccDOTIMES(latvec_pair, N_LATTICE_VECTOR_PAIRS) {
      ccDOTIMES(v, ubFLOAT::N_VOXELS) {
        tmp[v] = states_t[latvec_pair * 2][v];
        states_t[latvec_pair * 2][v] = states_t[latvec_pair * 2 + 1][v];
        states_t[latvec_pair * 2 + 1][v] = tmp[v];
      }
    }
  }
  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      ccDOTIMES(latvec_pair, N_LATTICE_VECTOR_PAIRS) {
	ccDOTIMES(v, ubFLOAT::N_VOXELS) {
	  tmp[v] = states_uds[nth_uds][latvec_pair * 2][v];
	  states_uds[nth_uds][latvec_pair * 2][v] = states_uds[nth_uds][latvec_pair * 2 + 1][v];
	  states_uds[nth_uds][latvec_pair * 2 + 1][v] = tmp[v];
	}
      }
    }
  }

#if BUILD_5G_LATTICE
  if (g_is_multi_component) {
    ccDOTIMES(latvec_pair, N_LATTICE_VECTOR_PAIRS) {
      ccDOTIMES(v, ubFLOAT::N_VOXELS) {
        tmp[v] = states_mc[latvec_pair * 2][v];
        states_mc[latvec_pair * 2][v] = states_mc[latvec_pair * 2 + 1][v];
        states_mc[latvec_pair * 2 + 1][v] = tmp[v];
      }
    }
  }
#endif
}

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
#if DEBUG
static
#else
static INLINE
#endif
VOID push_states_onto_neighbor_with_two_copies(const asINT32 n_src_voxels,
                                               const asINT32 dest_voxels[],
                                               const asINT32 src_voxels[],
                                               VOXEL_STATE (*neighbor_states)[ubFLOAT::N_VOXELS],
                                               VOXEL_STATE (*neighbor_states_t)[ubFLOAT::N_VOXELS],
                                               VOXEL_STATE (*neighbor_states_mc)[ubFLOAT::N_VOXELS],
					       VOXEL_STATE (*neighbor_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                               VOXEL_STATE (*advecting_block_states)[ubFLOAT::N_VOXELS],
                                               VOXEL_STATE (*advecting_block_states_t)[ubFLOAT::N_VOXELS],
                                               VOXEL_STATE (*advecting_block_states_mc)[ubFLOAT::N_VOXELS],
					       VOXEL_STATE (*advecting_block_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                               asINT32 latvec) {
asINT32 parity = state_parity(latvec);
#if defined(INTEL_COMPILER)
#pragma unroll(4)
#endif
for (asINT32 v=0; v < n_src_voxels; v++) {
  neighbor_states[parity][dest_voxels[v]] = advecting_block_states[parity][src_voxels[v]];
#if BUILD_5G_LATTICE
  if (g_is_multi_component) {
    neighbor_states_mc[parity][dest_voxels[v]] = advecting_block_states_mc[parity][src_voxels[v]];
  }
#endif
  if (ADVECT_TEMP) {
    neighbor_states_t[parity][dest_voxels[v]] = advecting_block_states_t[parity][src_voxels[v]];    
  }
  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
      neighbor_states_uds[nth_uds][parity][dest_voxels[v]] = advecting_block_states_uds[nth_uds][parity][src_voxels[v]]; 
  }
}
}


template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
#if DEBUG
static
#else
static INLINE
#endif
VOID swap_voxel_states(const asINT32 n_src_voxels,
                       const asINT32 dest_voxels[],
                       const asINT32 src_voxels[],
                       VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                       VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],
                       VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],
		       VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                       VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                       VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS],
                       VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS],
		       VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                       asINT32 latvec,
                       BOOLEAN src_block_has_two_copies) {
  VOXEL_STATE tmp;
  asINT32 parity = state_parity(latvec);
#if defined(INTEL_COMPILER)
#pragma unroll(4)
#endif
  for (asINT32 v=0; v < n_src_voxels; v++) {
    tmp = src_states[latvec][src_voxels[v]];
    //src states are for prev timestep when block has 2 states
    //We want to overwrite the correct element in the current lb_states
    //This is done in a separate function
    if (!src_block_has_two_copies) {
      src_states[latvec][src_voxels[v]] = states[parity][dest_voxels[v]];
    }
    states[parity][dest_voxels[v]] = tmp;
#if BUILD_5G_LATTICE
    if (g_is_multi_component) {
      tmp = src_states_mc[latvec][src_voxels[v]];
      if (!src_block_has_two_copies) {
        src_states_mc[latvec][src_voxels[v]] = states_mc[parity][dest_voxels[v]];
      }
      states_mc[parity][dest_voxels[v]] = tmp;
    }
#endif
    if (ADVECT_TEMP) {
      tmp = src_states_t[latvec][src_voxels[v]];
      if (!src_block_has_two_copies) {
        src_states_t[latvec][src_voxels[v]] = states_t[parity][dest_voxels[v]];
      }
      states_t[parity][dest_voxels[v]] = tmp;
    }
    if (ADVECT_UDS) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
	tmp = src_states_uds[nth_uds][latvec][src_voxels[v]];
	if (!src_block_has_two_copies) {
	  src_states_uds[nth_uds][latvec][src_voxels[v]] = states_uds[nth_uds][parity][dest_voxels[v]];
	}
	states_uds[nth_uds][parity][dest_voxels[v]] = tmp;
      }
    }
  }
}

#if BUILD_AVX2

/*=================================================================================================
 * @fcn swap_voxel_states_v
 *
 * This is a vectorized version of the swap routine. It relies on permute and blend AVX2 instructions.
 * The strategy is broadly three parts :
 *
 * a) Use permutation indices to move data from source voxels to required destination voxels, for the
 *    source states
 * b) Blend the original destination states, with the permuted source states to get the final destination
 *    states. The blend is used because only the destination voxel values are modified, and the remaining
 *    destination state voxel values matches the original.
 *
 * c) Perform operations (a) and (b) from destination to source, to swap with the source UBLK
 *
 *    Ex : Advect along latvec 1, for UBLK offset {1, 0 , 0} or +X neighbor
 *         Source voxels : {0, 1, 2, 3}
 *         Destination voxels : {4, 5, 6, 7}
 *
 *         * Source permutation indices : (3, 2, 1, 0, 3, 2, 1, 0) , read right to left
 *           The permutation indices imply that voxels 0 - 3 remain as is, but voxels 4 - 7
 *           are replaced by values from 0 - 3 respectively
 *
 *         * Permute(src_states, src_perm_ind) will therefore replace values in voxel 4-7, with
 *           those from voxels 0-3
 *
 *           permuted_src_states = PERM(src_states, src_perm_ind)
 *
 *         * Now that the source states are permuted, we can overwrite the destination states
 *
 *           dst_states = BLEND<dst_voxel_mask>(dst_states, permuted_src_states)
 *
 *           Where dst_voxel mask is (0b11110000), i.e., voxels 4 - 7 will be modified
 *
 *           The blend operation takes only active voxels worth of data from the permuted src_states
 *           and keeps the data at the other voxels as is.
 *
 *           Therefore dst_states will have voxels  4 - 7 match the permuted src values or voxels 0 -3
 *           of the src_states, and voxels 0-3 of the dst_states will remain as is.
 *
*================================================================================================*/
template<asINT32 latvec, auINT8 src_voxel_mask, auINT8 dst_voxel_mask,BOOLEAN ADVECT_TEMP,BOOLEAN ADVECT_UDS,  BOOLEAN HAS_TWO_COPY_NBR>
static INLINE VOID swap_voxel_states_v(UBLK src_ublk,
                                       UBLK dst_ublk,
                                       const __m256i& src_perm_ind,
                                       const __m256i& dst_perm_ind,
                                       asINT32 voxor,
                                       asINT32 prev_lb_index,
                                       asINT32 prev_t_index,
				       asINT32 prev_uds_index,
                                       BOOLEAN src_ublk_has_two_copies
                                       ) {

  asINT32 src_prev_lb_index = HAS_TWO_COPY_NBR? (src_ublk_has_two_copies? prev_lb_index : ONLY_ONE_COPY) : ONLY_ONE_COPY;
  asINT32 src_prev_t_index  = HAS_TWO_COPY_NBR? (src_ublk_has_two_copies? prev_t_index : ONLY_ONE_COPY) : ONLY_ONE_COPY;
  asINT32 src_prev_uds_index  = HAS_TWO_COPY_NBR? (src_ublk_has_two_copies? prev_uds_index : ONLY_ONE_COPY) : ONLY_ONE_COPY;

  auto src_ublk_ubfloat = src_ublk->to_ubfloat_ublk();
  auto dst_ublk_ubfloat = dst_ublk->to_ubfloat_ublk();

  constexpr asINT32 parity = state_parity(latvec);

  //LB STATES
  {
    vxFLOAT_BASE& src_states = src_ublk_ubfloat->lb_states(src_prev_lb_index)->m_states[latvec][voxor];
    vxFLOAT_BASE& dst_states = dst_ublk_ubfloat->lb_states(ONLY_ONE_COPY)->m_states[parity][voxor];
    vxFLOAT tmp = permutevar(src_states, src_perm_ind); //tmp for swapping
    //If src ublk has two copies, we overwrite the parity values
    if (HAS_TWO_COPY_NBR && src_ublk_has_two_copies) {
      vxFLOAT_BASE& curr_src_states = src_ublk_ubfloat->lb_states(src_prev_lb_index^1)->m_states[parity][voxor];
      curr_src_states = blend_v<src_voxel_mask>(curr_src_states, permutevar(dst_states, dst_perm_ind));
    } else {
      src_states = blend_v<src_voxel_mask>(src_states, permutevar(dst_states, dst_perm_ind));
    }
    dst_states = blend_v<dst_voxel_mask>(dst_states, tmp);
  }

  //TEMPERATURE
  if constexpr(ADVECT_TEMP) {
    vxFLOAT_BASE& src_states_t = src_ublk_ubfloat->t_data()->lb_t_data(src_prev_t_index)->m_states_t[latvec][voxor];
    vxFLOAT_BASE& dst_states_t = dst_ublk_ubfloat->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t[parity][voxor];
    vxFLOAT tmp = permutevar(src_states_t, src_perm_ind); //tmp for swapping
    //If src ublk has two copies, we overwrite the parity values
    if (HAS_TWO_COPY_NBR && src_ublk_has_two_copies) {
      vxFLOAT_BASE& curr_src_states_t = src_ublk_ubfloat->t_data()->lb_t_data(prev_t_index^1)->m_states_t[parity][voxor];
      curr_src_states_t = blend_v<src_voxel_mask>(curr_src_states_t, permutevar(dst_states_t, dst_perm_ind));
    } else {
      src_states_t = blend_v<src_voxel_mask>(src_states_t, permutevar(dst_states_t, dst_perm_ind));
    }
    dst_states_t = blend_v<dst_voxel_mask>(dst_states_t, tmp);
  }

  //UDS STATES
  if constexpr(ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      vxFLOAT_BASE& src_states_uds = src_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(src_prev_uds_index)->m_states_uds[latvec][voxor];
      vxFLOAT_BASE& dst_states_uds = dst_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds[parity][voxor];
      vxFLOAT tmp = permutevar(src_states_uds, src_perm_ind); //tmp for swapping
      //If src ublk has two copies, we overwrite the parity values
      if (HAS_TWO_COPY_NBR && src_ublk_has_two_copies) {
	vxFLOAT_BASE& curr_src_states_uds = src_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(prev_uds_index^1)->m_states_uds[parity][voxor];
	curr_src_states_uds = blend_v<src_voxel_mask>(curr_src_states_uds, permutevar(dst_states_uds, dst_perm_ind));
      } else {
	src_states_uds = blend_v<src_voxel_mask>(src_states_uds, permutevar(dst_states_uds, dst_perm_ind));
      }
      dst_states_uds = blend_v<dst_voxel_mask>(dst_states_uds, tmp);
    }
  }

  //MC STATES
#if BUILD_5G_LATTICE
  if (g_is_multi_component){
    vxFLOAT_BASE& src_states_mc = src_ublk_ubfloat->mc_data()->mc_states_data(src_prev_lb_index)->m_states_mc[latvec][voxor];
    vxFLOAT_BASE& dst_states_mc = dst_ublk_ubfloat->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc[parity][voxor];
    vxFLOAT tmp = permutevar(src_states_mc, src_perm_ind); //tmp for swapping
    //If src ublk has two copies, we overwrite the parity values
    if(src_ublk->has_two_copies()){
      vxFLOAT_BASE& curr_src_states_mc = src_ublk_ubfloat->mc_data()->mc_states_data(src_prev_lb_index^1)->m_states_mc[parity][voxor];
      curr_src_states_mc = blend_v<src_voxel_mask>(curr_src_states_mc, permutevar(dst_states_mc, dst_perm_ind));
    } else {
      src_states_mc = blend_v<src_voxel_mask>(src_states_mc, permutevar(dst_states_mc, dst_perm_ind));
    }
    dst_states_mc = blend_v<dst_voxel_mask>(dst_states_mc, tmp);
  }
#endif
}

#endif


#endif
