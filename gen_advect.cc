/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 


/**** This program generates the bodies of the three advection loops in
***** advect.c.  This is done so that we can unroll the loops with respect
***** to state numbers; i.e., we don't need to look up state numbers in
***** an advection table at run time.  This program is run whenever the
***** definitions of the state numbers (in PHYSTYPES_H) change.
*****
***** The top-level of this program is called with an axis (x, y, or z)
***** for which code is to be generated, and a filename.  It writes into
***** this file a snippet of code for each state which advects along the
***** given axis.  Each snippet is generated by a function, and the functions
***** are called via a table which maps (a,v) to a function, where a is the
***** axis and v is the velocity of the state along that axis (-2, -1, 0, 1, 2).
***** 
***** The code in each snippet will in general refer to variables defined in
***** the main body of advect.c.
*****
***** In the current implementations of the snippets, the same function
***** is used for v=-2 and v=2 for all axes, whereas the v=-1 and v=1 functions
***** are different for each axis.
*/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#ifdef  __cplusplus
extern "C" {
#endif

#include SCALAR_H
#include LOOP_H
#include MSGERR_H
#ifdef  __cplusplus
}
#endif

#include PHYSTYPES_H

#include "lattice.h"
