/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * BSurfel dynamics (functional sets and groups)
 *
 * Nath Gopalaswamy, Exa Corporation
 * Created Wed Nov 3, 2015
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
// #include "quantum.h"
// #include "group.h"
// #include "shob_dyn.h"
#include "bsurfel_dyn_meas.h"
// #include "sim.h"
// #include "sp_timers.h"
// #include PHYSICS_H

sMOVING_MEAS_CELL_SMART_PTR::~sMOVING_MEAS_CELL_SMART_PTR()
{
  if (!m_is_development && variables() != NULL) {
    sMOVING_MEAS_CELL_ID_AND_REF* header = get_header();
    header->ref_count--;
    if (header->ref_count == 0) {
      m_parent->delete_moving_meas_cell(variables());
    }
  }
  reset();
}

VOID do_all_bsurfel_measurements()
{
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  sBSURFEL_PROCESS_CONTROL bsurfel_process_control;
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    bsurfel_process_control.init(scale);
    bsurfel_process_control.do_bsurfel_measurements(FRINGE_BSURFEL_GROUP_TYPE);
    bsurfel_process_control.do_bsurfel_measurements(FRINGE2_BSURFEL_GROUP_TYPE);
    bsurfel_process_control.do_bsurfel_measurements(INTERIOR_BSURFEL_GROUP_TYPE);
  }
}

VOID update_bsurfel_meas_windows(sBSURFEL* bsurfel,
                                 LRF_PHYSICS_DESCRIPTOR lrf,
                                 ACTIVE_SOLVER_MASK active_solver_mask,
                                 STP_GEOM_VARIABLE meas_scale_factor,
                                 dFLOAT force[3],
                                 dFLOAT mass_flux)
{
#define transform_centroid()                                                                \
   {                                                                                         \
     dFLOAT centroid_input[3];                                                               \
     dFLOAT centroid_output[3];                                                              \
     vcopy(centroid_input,bsurfel->m_centroid);                                                \
     lrf->transform_point_to_containing(centroid_input,centroid_output);                     \
     sINT32 containing_lrf_index = lrf->containing_lrf_index;                                \
     while (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {                            \
       LRF_PHYSICS_DESCRIPTOR containing_lrf = &sim.lrf_physics_descs[containing_lrf_index]; \
       vcopy(centroid_input, centroid_output);                                               \
       containing_lrf->transform_point_to_containing(centroid_input,centroid_output);        \
       containing_lrf_index = containing_lrf->containing_lrf_index;                          \
     }                                                                                       \
     vcopy(centroid_xformed,centroid_output);                                                \
   }

  BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ? TRUE : FALSE;
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE) ? TRUE : FALSE;

  dFLOAT area = bsurfel->m_area;
  STP_DGEOM_VARIABLE *normal = bsurfel->m_normal;

  dFLOAT mass = bsurfel->m_sampled_fluid_density;
  dFLOAT mom_density;
  dFLOAT vel_sqrd;  
  dFLOAT vel[3];

#if BUILD_D19_LATTICE
  if(sim.is_pf_model){
    dFLOAT phi = bsurfel->m_sampled_uds_value[0];
    mass = phi2rho_capped(phi);

    ccDOTIMES(axis, 3) {
      vel[axis] = bsurfel->m_sampled_fluid_velocity_pfld[axis];
    }
  } else 
#endif
  {      
    ccDOTIMES(axis, 3) {
      vel[axis] = bsurfel->m_sampled_fluid_velocity[axis];
    }    
  }

  if (sim.is_incompressible_solver)
    mom_density = sim.char_density_f;
  else
    mom_density = mass;

  if (lrf != NULL) {
    // Transform velocities in local ref frame back to global ref frame
    dFLOAT  global_vel[3];
    dFLOAT centroid[3] = {bsurfel->m_centroid[0], bsurfel->m_centroid[1], bsurfel->m_centroid[2]};
    convert_vel_from_local_to_global_frame(vel, centroid, global_vel, lrf);
    ccDOTIMES(axis, 3) vel[axis] = global_vel[axis];
  }

  vel[0] *= area;
  vel[1] *= area;
  vel[2] *= area;
  force[0] *= area;
  force[1] *= area;
  force[2] *= area;
  vel_sqrd = vel[0] * vel[0] + vel[1] * vel[1] + vel[2] * vel[2];

  // These vectors hold vectors transformed from local ref frame coord system to global ref frame coord system
  dFLOAT vel_xformed[3];
  dFLOAT f_xformed[3];
  dFLOAT force_xformed[3]; // kinetic force
  dFLOAT centroid_xformed[3];
  dFLOAT r_cross_f[3];
  auINT32 calc_mask = 0; // 1 bit for each vector
  const auINT32 VEL_XFORMED             = 1;
  const auINT32 F_XFORMED               = 2;
  const auINT32 KINETIC_FORCE_XFORMED   = 4;
  const auINT32 R_CROSS_F_COMPUTED      = 16;
  const auINT32 F_AND_CENTROID_FOR_TORQUE_COMPUTED = 32;

  dFLOAT passive_scalar_temp = area * sim.char_temp;
  dFLOAT wall_temp = sim.char_temp;

  if (sim.is_heat_transfer) {
    passive_scalar_temp = area * bsurfel->m_sampled_fluid_temp;
    wall_temp = bsurfel->m_sampled_fluid_temp;
  }

  dFLOAT uds_wall[MAX_N_USER_DEFINED_SCALARS] = {sim.char_uds};

  if (sim.is_scalar_model) {
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
      uds_wall[nth_uds] = bsurfel->m_sampled_uds_value[nth_uds];
  }

  // bsurfels are adiabatic. So heat transfer is zero
  dFLOAT htc = 0.0F;
  dFLOAT htc_XRA = 0.0F;
  dFLOAT heatflux = 0.0F;
  dFLOAT uds_flux = 0.0F;

  dFLOAT nf_dot = (force[0] * normal[0] + force[1] * normal[1] + force[2] * normal[2]);

  dFLOAT pressure;
  if (sim.thermal_feedback_is_on)
    pressure = bsurfel->m_sampled_fluid_temp * mass * sim.lattice_gas_const;
  else
    pressure = g_lb_temp_constant * mass * sim.lattice_gas_const;

  dFLOAT pressure_char = sim.char_pressure_f;
#if BUILD_D19_LATTICE
  if(sim.is_pf_model) {
    pressure_char /= g_density_scale_factor;
    //pressure = g_lb_temp_constant * mass * sim.lattice_gas_const;
    pressure = bsurfel->m_sampled_fluid_pressure_pfld;
  }
#endif

  dFLOAT a_pressure = pressure * area;

  dFLOAT a_pressure_minus_nf_dot = (a_pressure - area * pressure_char) - nf_dot;

  dFLOAT f[3], tf[3];
  dFLOAT *f_for_torque;

  // forces modified so that the pressure is equal to the normal force
  // TODO: Should this modification be done for immersed boundaries, since
  // they are not one-sided? Which normal should be used, inward or outward?
  f[0] = force[0]; // + normal[0] * a_pressure_minus_nf_dot;
  f[1] = force[1]; // + normal[1] * a_pressure_minus_nf_dot;
  f[2] = force[2]; // + normal[2] * a_pressure_minus_nf_dot;

  // TODO: We should attempt to compute the tangential force from the sampled
  // turbulent shear stress along the wall for turbulent flows
  // compute tangential force from the modified force
  dFLOAT nforce = f[0] * normal[0] + f[1] * normal[1] + f[2] * normal[2];
  tf[0] = f[0] - normal[0] * nforce;
  tf[1] = f[1] - normal[1] * nforce;
  tf[2] = f[1] - normal[2] * nforce;

  // isfinite is only defined in the std:: namepsace for the Cray compiler on audrey
#if defined(__AMD64_LINUX_CRAYXE6__) || defined(_EXA_CLANG)
  BOOLEAN finite_forces_p = (std::isfinite(f[0]) && std::isfinite(f[1]) && std::isfinite(f[2]));
#else
  BOOLEAN finite_forces_p = (isfinite(f[0]) && isfinite(f[1]) && isfinite(f[2]));
#endif
  BOOLEAN is_moving_ref_frame = lrf != NULL && lrf->is_mlrf_on;

  ccDOTIMES(nth_window, bsurfel->m_n_meas_cell_refs) {
    MEAS_WINDOW window = g_meas_windows[bsurfel->m_meas_cell_refs[nth_window].window_id()];

    ACTIVE_SOLVER_MASK solver_mask = window->solver_mask;
    dFLOAT acous_switch_scale = 1.0F;

    //only accumulating vars when all associated windows are active
    if (solver_mask != 0) {
      MEAS_CELL_VAR *meas_cell;
      BOOLEAN assign_value_p = TRUE;

      if ( !bsurfel->is_active() ) {
        assign_value_p = FALSE;
      }

      BOOLEAN should_rotate_vector_to_grf = (is_moving_ref_frame && !window->is_output_in_local_csys);

      if (window->is_composite || window->is_development) {
        calc_mask &= ~R_CROSS_F_COMPUTED;  // every window has a different ref point requiring new calculation
        if (a_pressure > (area * window->max_pressure)
            || a_pressure < (area * window->min_pressure)
            || !finite_forces_p) {
          assign_value_p = FALSE;
        }
      }

      // For development windows with bsurfels we adjust the meas cells
      if (window->is_development) {
        // meas_cell_ptr has been left as an index so that axis could also be encoded in meas_cell_ptr
        MEAS_CELL_PTR *meas_cell_ptr = &(bsurfel->m_meas_cell_refs[nth_window]);
        asINT32 axis = meas_cell_ptr->axis();
        STP_MEAS_CELL_INDEX meas_cell_index = window->m_meas_cell_index_map[meas_cell_ptr->index()].m_new_sp_cell_index;
        STP_MEAS_CELL_INDEX iextent;
        if (should_rotate_vector_to_grf) {
          // @@@ We need a flag in calc_mask so we don't transform the centroid multiple times
          transform_centroid();
          iextent = window->calculate_dev_win_extent_index(centroid_xformed, axis);
        }
        else {
          iextent = window->calculate_dev_win_extent_index(bsurfel->m_centroid, axis);
        }
        asINT32 face_index = meas_cell_ptr->face();
        iextent -= window->entity_first_segment[axis][face_index];
        meas_cell = window->meas_cell(meas_cell_index + iextent);
#if DEBUG
        uINT64 min_ptr = (uINT64)window->meas_cells();
        uINT64 max_ptr =  min_ptr + sizeof(MEAS_CELL_VAR) * window->n_stationary_meas_cells * window->n_variables;
        uINT64 curr_ptr = (uINT64) meas_cell;
        if ((curr_ptr >= max_ptr) || (curr_ptr < min_ptr)) {
          msg_internal_error("Bsurfel %d force development window meas cell %ld out of range[%ld %ld]",
                             bsurfel->id(), curr_ptr,
                             min_ptr, max_ptr);
        }
        if (iextent >= window->entity_n_segments[axis][face_index] || (iextent < 0))
          msg_internal_error("Bsurfel %d force development window segment index out of range iextent %d face %d nsegs %d",
                             bsurfel->id(), iextent, face_index,
                             window->entity_n_segments[axis][face_index]);
#endif
      }
      else {
        meas_cell = bsurfel->m_meas_cell_refs[nth_window].variables();
        meas_cell++; // skip over the first variable which is the meas cell index/ref_count
      }

// NOTE: Standard deviation meas variables on immersed boundaries are not supported in PowerFLOW 5.4.
//       Until we decide to support them, the following macro always accumulates zero.
//       When needed, replace the line
//         var_accum   += 0;
//       with
//         var_accum   += (scale_factor) * (var);
#define std_dev_var(scale_factor, var)                                                                          \
       if (assign_value_p) {                                                                                     \
         dFLOAT var_accum = *(meas_cell + 1);                                                                    \
         var_accum   += 0;                                                                                       \
         \
         /* M2 is the variance (scaled by the pfluid sum). */                                                  \
         dFLOAT mean      = *(meas_cell + 2);                                                                  \
         dFLOAT M2        = *meas_cell;                                                                        \
         /* n/delta_t is the Nth sample because we only allow voxel and ublk measurements of variance */       \
         dFLOAT one_over_n= window->one_over_n_timesteps_since_clear();                                        \
         dFLOAT x         = var_accum;                                                                         \
         dFLOAT delta     = x - mean;                                                                          \
         /* @@@ these 2 uses of delta_t should be replaced by variables stored in some sort of bsurfel dyn dcache */\
         mean             = mean + scale_to_delta_t(bsurfel->scale()) * one_over_n * delta;                     \
         M2               = M2 + delta * (x - mean) / scale_to_delta_t(bsurfel->scale());                       \
         \
         *meas_cell = M2;                                                                                      \
         *(meas_cell + 1) = 0;                                                                                 \
         *(meas_cell + 2) = mean;                                                                              \
       }                                                                                                         \
       meas_cell += 3;                                                                                           \
       var_index += 2;


#define compute_r_cross_f()                                                          \
       /* Note that R_CROSS_F_COMPUTED is reset for each composite meas window */     \
       if ((calc_mask & R_CROSS_F_COMPUTED) == 0) {                                   \
         calc_mask |= R_CROSS_F_COMPUTED;                                             \
         if ((calc_mask & F_AND_CENTROID_FOR_TORQUE_COMPUTED) == 0) {                 \
           calc_mask |= F_AND_CENTROID_FOR_TORQUE_COMPUTED;                           \
           /* Torque (i.e. r_cross_f) is only computed for composite windows.  */     \
           /* Thus we follow the same branch of this conditional for all windows. */  \
           if (should_rotate_vector_to_grf) {                                         \
             transform_f();                                                           \
             transform_centroid();                                                    \
             f_for_torque = f_xformed;                                                \
           } else {                                                                   \
             f_for_torque = f;                                                        \
             centroid_xformed[0] = bsurfel->m_centroid[0];                            \
             centroid_xformed[1] = bsurfel->m_centroid[1];                            \
             centroid_xformed[2] = bsurfel->m_centroid[2];                            \
           }                                                                          \
         }                                                                            \
         dFLOAT r[3];                                                                 \
         vsub(r, centroid_xformed, window->reference_point);                          \
         /* reverse the sign of the torque since force will be reversed in the */     \
         /* meas file */                                                              \
         vcross(r_cross_f, f_for_torque, r);                                          \
       }

#define transform_vel()                                                         \
       if ((calc_mask & VEL_XFORMED) == 0) {                                     \
         calc_mask |= VEL_XFORMED;                                               \
         rotate_vector(vel, vel_xformed, lrf->local_to_global_rotation_matrix);  \
       }

#define transform_f()                                                           \
       if ((calc_mask & F_XFORMED) == 0) {                                       \
         calc_mask |= F_XFORMED;                                                 \
         rotate_vector(f, f_xformed, lrf->local_to_global_rotation_matrix);      \
       }

#define transform_kinetic_force()                                                       \
       if ((calc_mask & KINETIC_FORCE_XFORMED) == 0) {                                   \
         calc_mask |= KINETIC_FORCE_XFORMED;                                             \
         rotate_vector(force, force_xformed, lrf->local_to_global_rotation_matrix);      \
       }

      asINT32 n_vars = window->n_variables;

      if (solver_mask == (solver_mask & active_solver_mask)) {
        for (asINT32 var_index = 0; var_index < n_vars; var_index++) {
          SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
          switch (var_type) {
          case SRI_VARIABLE_DENSITY:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * area * mom_density : 0.0;
            break;
          // NOTE: Standard deviation meas variables on immersed boundaries are not supported in PowerFLOW 5.4.
          //       Until we decide to support them, the macro std_dev_var always accumulates zero.
          case SRI_VARIABLE_STD_DEV_XVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              std_dev_var(meas_scale_factor, vel_xformed[0]);
            }
            else {
              std_dev_var(meas_scale_factor, vel[0]);
            }
            break;
          case SRI_VARIABLE_STD_DEV_YVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              std_dev_var(meas_scale_factor, vel_xformed[1]);
            }
            else {
              std_dev_var(meas_scale_factor, vel[1]);
            }
            break;
          case SRI_VARIABLE_STD_DEV_ZVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              std_dev_var(meas_scale_factor, vel_xformed[2]);
            }
            else {
              std_dev_var(meas_scale_factor, vel[2]);
            }
            break;
          case SRI_VARIABLE_STD_DEV_VEL_MAG:
            std_dev_var(meas_scale_factor, sqrt(vel_sqrd));
            break;
          case SRI_VARIABLE_STD_DEV_PRESSURE:
            std_dev_var(meas_scale_factor * area, pressure);
            break;
          case SRI_VARIABLE_STD_DEV_TEMP:
            std_dev_var(meas_scale_factor * area, wall_temp);
            break;
          case SRI_VARIABLE_STD_DEV_DENSITY:
            std_dev_var(meas_scale_factor * area, mom_density);
            break;
          case SRI_VARIABLE_XVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel_xformed[0] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel[0] : 0.0;
            }
            break;
          case SRI_VARIABLE_YVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel_xformed[1] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel[1] : 0.0;
            }
            break;
          case SRI_VARIABLE_ZVEL:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel_xformed[2] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * vel[2] : 0.0;
            }
            break;
          case SRI_VARIABLE_TEMP:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * area * wall_temp : 0.0;
            break;
          case SRI_VARIABLE_XFORCE:
            // sign of x,y,z surface force components reversed for vizualization
            if (should_rotate_vector_to_grf) {
              transform_f();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f_xformed[0] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f[0] : 0.0;
            }
            break;
          case SRI_VARIABLE_YFORCE:
            if (should_rotate_vector_to_grf) {
              transform_f();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f_xformed[1] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f[1] : 0.0;
            }
            break;
          case SRI_VARIABLE_ZFORCE:
            if (should_rotate_vector_to_grf) {
              transform_f();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f_xformed[2] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * f[2] : 0.0;
            }
            break;
          case SRI_VARIABLE_KINETIC_XFORCE:
            if (should_rotate_vector_to_grf) {
              transform_kinetic_force();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force_xformed[0] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force[0] : 0.0;
            }
            break;
          case SRI_VARIABLE_KINETIC_YFORCE:
            if (should_rotate_vector_to_grf) {
              transform_kinetic_force();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force_xformed[1] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force[1] : 0.0;
            }
            break;
          case SRI_VARIABLE_KINETIC_ZFORCE:
            if (should_rotate_vector_to_grf) {
              transform_kinetic_force();
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force_xformed[2] : 0.0;
            }
            else {
              *(meas_cell++) -= assign_value_p ? meas_scale_factor * force[2] : 0.0;
            }
            break;
          case SRI_VARIABLE_TFORCE_MAG:
            // TODO: Should be possible to replace this with the wall shear
            // stress calculated from the sampled strain rate tensor from
            // the neighboring ublks
          {
            *(meas_cell++) += assign_value_p ? meas_scale_factor * sqrt(tf[0] * tf[0] +
                              tf[1] * tf[1] +
                              tf[2] * tf[2]) : 0.0;
          }
          break;
          case SRI_VARIABLE_XTORQUE:
            compute_r_cross_f();
            *(meas_cell++) += assign_value_p ? meas_scale_factor * r_cross_f[0] : 0.0;
            break;
          case SRI_VARIABLE_YTORQUE:
            compute_r_cross_f();
            *(meas_cell++) += assign_value_p ? meas_scale_factor * r_cross_f[1] : 0.0;
            break;
          case SRI_VARIABLE_ZTORQUE:
            compute_r_cross_f();
            *(meas_cell++) += assign_value_p ? meas_scale_factor * r_cross_f[2] : 0.0;
            break;
          case SRI_VARIABLE_VEL_MAG:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * (sqrt(vel_sqrd)) : 0.0;
            break;
          case SRI_VARIABLE_ENERGY:
            if (sim.is_heat_transfer) {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * (passive_scalar_temp * LATTICE_SPECIFIC_HEAT_CV_THERMAL +
                                0.5 * vel_sqrd / area) : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * (area * g_lb_temp_constant * LATTICE_SPECIFIC_HEAT_CV_THERMAL +
                                0.5 * vel_sqrd / area) : 0.0;
            }
            break;
          case SRI_VARIABLE_PRESSURE:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * a_pressure : 0.0;
            break;
          // Pressure gradient calculation not implemented for bsurfels
          case SRI_VARIABLE_XPRESSURE_GRADIENT:
          case SRI_VARIABLE_YPRESSURE_GRADIENT:
          case SRI_VARIABLE_ZPRESSURE_GRADIENT:
          case SRI_VARIABLE_PRESSURE_GRADIENT_MAG:
            *(meas_cell++) += 0.0;
            break;
          case SRI_VARIABLE_USTAR:
            // Implement the calculation in SRI
            // Also see note above for the TFORCE_MAG calculation
          {
            dFLOAT ustar_out = sqrt((tf[0] * tf[0] + tf[1] * tf[1] + tf[2] * tf[2]) / mom_density);
            *(meas_cell++) += assign_value_p ? meas_scale_factor * ustar_out : 0.0;
          }
          break;
          case SRI_VARIABLE_YPLUS:
          case SRI_VARIABLE_THERMAL_YPLUS:
            // Implement the calculation in SRI
            // Also see note above for the TFORCE_MAG calculation
          {
            dFLOAT ustar_out = sqrt((tf[0] * tf[0] + tf[1] * tf[1] + tf[2] * tf[2]) / mom_density);
            dFLOAT yplus_out = ustar_out * 0.5F * dFLOAT(1 << bsurfel->scale()) * mom_density / g_nu_molecular;
            *(meas_cell++) += assign_value_p ? meas_scale_factor * yplus_out : 0.0;
          }
          break;
          case SRI_VARIABLE_INTERNAL_ENERGY:
            if (sim.is_heat_transfer) {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * passive_scalar_temp * LATTICE_SPECIFIC_HEAT_CV_THERMAL : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * area * g_lb_temp_constant * LATTICE_SPECIFIC_HEAT_CV_THERMAL : 0.0;
            }
            break;
          case SRI_VARIABLE_TURB_KINETIC_ENERGY:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * area * bsurfel->m_sampled_tke : 0.0;
            break;
          case SRI_VARIABLE_TURB_DISSIPATION:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * area * bsurfel->m_sampled_eps : 0.0;
            break;
          case SRI_VARIABLE_KINETIC_ENERGY:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * 0.5 * vel_sqrd / area : 0.0;
            break;
          case SRI_VARIABLE_ENTHALPY:
            if (sim.is_heat_transfer) {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * passive_scalar_temp * LATTICE_SPECIFIC_HEAT_CP_THERMAL : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * area * g_lb_temp_constant * LATTICE_SPECIFIC_HEAT_CP_THERMAL : 0.0;
            }
            break;
          case SRI_VARIABLE_FORCE_MAG:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * sqrt(f[0] * f[0] + f[1] * f[1] + f[2] * f[2]) : 0.0;
            break;
          case SRI_VARIABLE_KINETIC_FORCE_MAG:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * sqrt(force[0] * force[0] + force[1] * force[1] + force[2] * force[2]) : 0.0;
            break;
          case SRI_VARIABLE_N_SCREENED_SURFELS:
            // shob_to_scale is terribly slow, but we expect to have to call it extremely rarely
            *(meas_cell++) += assign_value_p ? 0 : sim_scale_fine_timesteps_per_timestep(bsurfel->scale()) * acous_switch_scale;
            break;
          case SRI_VARIABLE_SCREENED_AREA:
            *(meas_cell++) += assign_value_p ? 0.0 : meas_scale_factor * area ;
            break;
          case SRI_VARIABLE_AREA:
            // for bsurfels, we always include the area to get the correct value
            // in the denominator
            // *(meas_cell++) += assign_value_p ? meas_scale_factor * area : 0.0 ;
            *(meas_cell++) += meas_scale_factor * area;
            break;
          case SRI_VARIABLE_TORQUE_MAG:
            compute_r_cross_f();
            *(meas_cell++) += assign_value_p ? meas_scale_factor * area *
                              sqrt(r_cross_f[0] * r_cross_f[0] +
                                   r_cross_f[1] * r_cross_f[1] +
                                   r_cross_f[2] * r_cross_f[2]) : 0.0;
            break;
          case SRI_VARIABLE_XMOMENTUM:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel_xformed[0] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel[0] : 0.0;
            }
            break;
          case SRI_VARIABLE_YMOMENTUM:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel_xformed[1] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel[1] : 0.0;
            }
            break;
          case SRI_VARIABLE_ZMOMENTUM:
            if (should_rotate_vector_to_grf) {
              transform_vel();
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel_xformed[2] : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * vel[2] : 0.0;
            }
            break;
          case SRI_VARIABLE_MOMENTUM_MAG:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * mom_density * sqrt(vel_sqrd) : 0.0;
            break;
          case SRI_VARIABLE_HEAT_FLUX:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * heatflux : 0.0;
            break;
          case SRI_VARIABLE_MASS_FLUX:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * mass_flux : 0.0;
            break;
          case SRI_VARIABLE_NEAR_WALL_TEMP:
            if (sim.is_heat_transfer) {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * passive_scalar_temp : 0.0;
            }
            else {
              *(meas_cell++) += assign_value_p ? meas_scale_factor * area * g_lb_temp_constant : 0.0;
            }
            break;
          case SRI_VARIABLE_HTC_CHAR_TEMP:
            if (sim.is_heat_transfer && !sim.is_high_subsonic_HT_off) {
              dFLOAT temp_diff = wall_temp - sim.char_temp_f;
              *(meas_cell++) += assign_value_p ? (temp_diff == 0 ? 0 : meas_scale_factor * heatflux / temp_diff) : 0;
            }
            else {  //isothermal
              *(meas_cell++) += assign_value_p ? meas_scale_factor * htc : 0.0;
            }
            break;
          case SRI_VARIABLE_HTC_NEAR_WALL_TEMP:
            *(meas_cell++) += assign_value_p ? meas_scale_factor * htc : 0.0;
            break;
	  case SRI_VARIABLE_WATER_VAPOR_MFRAC:
	    *(meas_cell++) += assign_value_p? meas_scale_factor * uds_wall[0] * area : 0.0;
            break;
          case SRI_VARIABLE_WATER_VAPOR_MFLUX:
            *(meas_cell++) += assign_value_p? meas_scale_factor * uds_flux * sim.char_density : 0.0; 
	    break;
          case SRI_VARIABLE_WATER_FILM_THICKNESS:
          case SRI_VARIABLE_DEFROST_TIME:
          case SRI_VARIABLE_FILM_THICKNESS:
          case SRI_VARIABLE_FILM_XVEL:
          case SRI_VARIABLE_FILM_ZVEL:
          case SRI_VARIABLE_FILM_YVEL:
          case SRI_VARIABLE_FILM_VEL_MAG:
          case SRI_VARIABLE_FILM_STRESS:
          case SRI_VARIABLE_PRTCL_XFORCE:
          case SRI_VARIABLE_PRTCL_YFORCE:
          case SRI_VARIABLE_PRTCL_ZFORCE:
          case SRI_VARIABLE_FILM_PERSISTENCE:
            meas_cell++;
            break;
#if 1
          case SRI_VARIABLE_PRTCL_XIMPULSE:
          case SRI_VARIABLE_PRTCL_YIMPULSE:
          case SRI_VARIABLE_PRTCL_ZIMPULSE:
          case SRI_VARIABLE_ICE_THICKNESS:
          case SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND:
          case SRI_VARIABLE_PRTCL_MEAN_DIAM_OUTBOUND:
          case SRI_VARIABLE_PRTCL_MEAN_DENS_INBOUND:
          case SRI_VARIABLE_PRTCL_MEAN_DENS_OUTBOUND:
          case SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND:
          case SRI_VARIABLE_PRTCL_MASS_RATE_OUTBOUND:
          case SRI_VARIABLE_PRTCL_RATE_INBOUND:
          case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
            meas_cell++;
            break;
            
#else 
          case SRI_VARIABLE_PRTCL_XIMPULSE:
            *(meas_cell++) += composite_scale_factor * bsurfel->p_data()->sum_particle_impulse[0]  * area;
            break;
          case SRI_VARIABLE_PRTCL_YIMPULSE:
            *(meas_cell++) += composite_scale_factor * bsurfel->p_data()->sum_particle_impulse[1]  * area;
            break;
          case SRI_VARIABLE_PRTCL_ZIMPULSE:
            *(meas_cell++) += composite_scale_factor * bsurfel->p_data()->sum_particle_impulse[2]  * area;
            break;
          case SRI_VARIABLE_ICE_THICKNESS:
            *(meas_cell++) += bsurfel->p_data()->accretion_volume;
            break;
          case SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND:
            *(meas_cell++) += bsurfel->p_data()->sum_diameter_inbound();
            break;
          case SRI_VARIABLE_PRTCL_MEAN_DIAM_OUTBOUND:
            *(meas_cell++) += bsurfel->p_data()->sum_diameter_outbound();
            break;
          case SRI_VARIABLE_PRTCL_MEAN_DENS_INBOUND:
            *(meas_cell++) += bsurfel->p_data()->sum_density_inbound();
            break;
          case SRI_VARIABLE_PRTCL_MEAN_DENS_OUTBOUND:
            *(meas_cell++) += bsurfel->p_data()->sum_density_outbound();
            break;
          case SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND:
            *(meas_cell++) += bsurfel->p_data()->mass_inbound();
            break;
          case SRI_VARIABLE_PRTCL_MASS_RATE_OUTBOUND:
            *(meas_cell++) += bsurfel->p_data()->mass_outbound();
            break;
        //these two serve as helper variables when mean inbound and outbound particle property variables are requested
        //they can also be requested explicitly by the user
          case SRI_VARIABLE_PRTCL_RATE_INBOUND:
            *(meas_cell++) += bsurfel->p_data()->num_inbound();
            break;
          case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
            *(meas_cell++) += bsurfel->p_data()->num_outbound();
            break;
#endif
          default:
            msg_internal_error("Unrecognized measurement variable in bsurfel dynamics");
            break;
          }//switch

        }// var_index

      }
#undef std_dev_var
#undef compute_r_cross_f
#undef transform_mom
#undef transform_f
#undef transform_kinetic_force
    }
  }
}
