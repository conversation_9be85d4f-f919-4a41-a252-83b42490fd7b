/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                      Created Tue, Mar 3, 2009
//----------------------------------------------------------------------------
#include "common_sp.h"
#include "sim.h"
#include "box_advect.h"
#include "vr.h"
#include "strand_mgr.h"

#if BUILD_D39_LATTICE
template<typename UBLK_TYPE>
VOID copy_z_only_states(UBLK_TYPE* ublk, asINT32 timestep_index, ACTIVE_SOLVER_MASK active_solver_mask) {

  VOXEL_STATE (*curr_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(    timestep_index)->m_states;
  VOXEL_STATE (*prev_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(1 ^ timestep_index)->m_states;
  ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
    curr_states[D_0_0_P1_D39][voxel] = prev_states[D_0_0_P1_D39][voxel];
    curr_states[D_0_0_N1_D39][voxel] = prev_states[D_0_0_N1_D39][voxel];
    curr_states[D_0_0_P2_D39][voxel] = prev_states[D_0_0_P2_D39][voxel];
    curr_states[D_0_0_N2_D39][voxel] = prev_states[D_0_0_N2_D39][voxel];
  }

  if ((active_solver_mask & T_PDE_ACTIVE) && sim.is_T_S_solver_type_lb()) {
    VOXEL_STATE (*curr_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(    timestep_index)->m_states_t;
    VOXEL_STATE (*prev_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(1 ^ timestep_index)->m_states_t;
    ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
      curr_states_t[D_0_0_P1_D39][voxel] = prev_states_t[D_0_0_P1_D39][voxel];
      curr_states_t[D_0_0_N1_D39][voxel] = prev_states_t[D_0_0_N1_D39][voxel];
      curr_states_t[D_0_0_P2_D39][voxel] = prev_states_t[D_0_0_P2_D39][voxel];
      curr_states_t[D_0_0_N2_D39][voxel] = prev_states_t[D_0_0_N2_D39][voxel];
    }
  }

  if ((active_solver_mask & UDS_PDE_ACTIVE) && (sim.uds_solver_type == LB_UDS)) {
    //DO_UBLK_UDS_DATA(nth_uds, sim.n_user_defined_scalars,ublk,ublk_uds_data){
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
     auto ublk_uds_data = ublk->uds_data(nth_uds);
     VOXEL_STATE (*curr_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(    timestep_index)->m_states_uds;
     VOXEL_STATE (*prev_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(1 ^ timestep_index)->m_states_uds;
     ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
       curr_states_uds[D_0_0_P1_D39][voxel] = prev_states_uds[D_0_0_P1_D39][voxel];
       curr_states_uds[D_0_0_N1_D39][voxel] = prev_states_uds[D_0_0_N1_D39][voxel];
       curr_states_uds[D_0_0_P2_D39][voxel] = prev_states_uds[D_0_0_P2_D39][voxel];
       curr_states_uds[D_0_0_N2_D39][voxel] = prev_states_uds[D_0_0_N2_D39][voxel];
     }
    }
  }
}

template<typename UBLK_TYPE>
VOID scale_up_z_only_states(UBLK_TYPE* ublk, dFLOAT fraction, asINT32 timestep_index,
                            BOOLEAN has_two_copies, ACTIVE_SOLVER_MASK active_solver_mask) {
  VOXEL_STATE (*curr_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(timestep_index)->m_states;
  ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
    curr_states[D_0_0_P1_D39][voxel] *= fraction;
    curr_states[D_0_0_N1_D39][voxel] *= fraction;
    curr_states[D_0_0_P2_D39][voxel] *= fraction;
    curr_states[D_0_0_N2_D39][voxel] *= fraction;
  }

  if (has_two_copies && (active_solver_mask & T_PDE_ACTIVE) && sim.is_T_S_solver_type_lb()) {
    VOXEL_STATE (*curr_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(    timestep_index)->m_states_t;
    VOXEL_STATE (*prev_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(1 ^ timestep_index)->m_states_t;
    ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
      curr_states_t[D_0_0_P1_D39][voxel] = prev_states_t[D_0_0_P1_D39][voxel];
      curr_states_t[D_0_0_N1_D39][voxel] = prev_states_t[D_0_0_N1_D39][voxel];
      curr_states_t[D_0_0_P2_D39][voxel] = prev_states_t[D_0_0_P2_D39][voxel];
      curr_states_t[D_0_0_N2_D39][voxel] = prev_states_t[D_0_0_N2_D39][voxel];
    }
  }
  if (has_two_copies && (active_solver_mask & UDS_PDE_ACTIVE) && (sim.uds_solver_type == LB_UDS)) {
    //DO_UBLK_UDS_DATA(nth_uds, sim.n_user_defined_scalars,ublk,ublk_uds_data){
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      auto ublk_uds_data = ublk->uds_data(nth_uds);
      VOXEL_STATE (*curr_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(    timestep_index)->m_states_uds;
      VOXEL_STATE (*prev_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(1 ^ timestep_index)->m_states_uds;
      ccDOTIMES(voxel, UBLK_TYPE::N_VOXELS) {
	curr_states_uds[D_0_0_P1_D39][voxel] = prev_states_uds[D_0_0_P1_D39][voxel];
	curr_states_uds[D_0_0_N1_D39][voxel] = prev_states_uds[D_0_0_N1_D39][voxel];
	curr_states_uds[D_0_0_P2_D39][voxel] = prev_states_uds[D_0_0_P2_D39][voxel];
	curr_states_uds[D_0_0_N2_D39][voxel] = prev_states_uds[D_0_0_N2_D39][voxel];
      }
    }
  }
}
#elif BUILD_D19_LATTICE || BUILD_5G_LATTICE

template<typename UBLK_TYPE>
__HOST__DEVICE__ VOID copy_z_only_states(UBLK_TYPE* ublk, asINT32 timestep_index,
                                         ACTIVE_SOLVER_MASK active_solver_mask) {
  auto& simc = get_simc_ref();
  VOXEL_STATE (*curr_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(    timestep_index)->m_states;
  VOXEL_STATE (*prev_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(1 ^ timestep_index)->m_states;
  ccDO_UBLK_VOXELS(voxel){
    curr_states[D_0_0_P1_D19][voxel] = prev_states[D_0_0_P1_D19][voxel];
    curr_states[D_0_0_N1_D19][voxel] = prev_states[D_0_0_N1_D19][voxel];
  }
  if ((active_solver_mask & T_PDE_ACTIVE) && simc.is_T_S_solver_type_lb()) {
   VOXEL_STATE (*curr_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(    timestep_index)->m_states_t;
   VOXEL_STATE (*prev_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(1 ^ timestep_index)->m_states_t;
    ccDO_UBLK_VOXELS(voxel){
     curr_states_t[D_0_0_P1_D19][voxel] = prev_states_t[D_0_0_P1_D19][voxel];
     curr_states_t[D_0_0_N1_D19][voxel] = prev_states_t[D_0_0_P1_D19][voxel];
   }
  }
  if ((active_solver_mask & UDS_PDE_ACTIVE) && (simc.uds_solver_type == LB_UDS)) {
    //DO_UBLK_UDS_DATA(nth_uds, simc.n_user_defined_scalars,ublk,ublk_uds_data){
    ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
      auto ublk_uds_data = ublk->uds_data(nth_uds);
      VOXEL_STATE (*curr_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(    timestep_index)->m_states_uds;
      VOXEL_STATE (*prev_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(1 ^ timestep_index)->m_states_uds;
      ccDO_UBLK_VOXELS(voxel){
	curr_states_uds[D_0_0_P1_D19][voxel] = prev_states_uds[D_0_0_P1_D19][voxel];
	curr_states_uds[D_0_0_N1_D19][voxel] = prev_states_uds[D_0_0_P1_D19][voxel];
      }
    }
  }
}

template<typename UBLK_TYPE>
__HOST__DEVICE__ VOID scale_up_z_only_states(UBLK_TYPE* ublk, dFLOAT fraction,
                                             asINT32 timestep_index,
                                             BOOLEAN has_two_copies,
                                             ACTIVE_SOLVER_MASK active_solver_mask) {
  VOXEL_STATE (*curr_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(    timestep_index)->m_states;
  VOXEL_STATE (*prev_states)[UBLK_TYPE::N_VOXELS] = ublk->lb_states(1 ^ timestep_index)->m_states;
  auto& simc = get_simc_ref();
  ccDO_UBLK_VOXELS(voxel){
    curr_states[D_0_0_P1_D19][voxel] = prev_states[D_0_0_P1_D19][voxel] * fraction;
    curr_states[D_0_0_N1_D19][voxel] = prev_states[D_0_0_N1_D19][voxel] * fraction;
  }
  if (has_two_copies && (active_solver_mask & T_PDE_ACTIVE) && simc.is_T_S_solver_type_lb()) {
    VOXEL_STATE (*curr_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(    timestep_index)->m_states_t;
    VOXEL_STATE (*prev_states_t)[UBLK_TYPE::N_VOXELS] = ublk->t_data()->lb_t_data(1 ^ timestep_index)->m_states_t;
    ccDO_UBLK_VOXELS(voxel){
      curr_states_t[D_0_0_P1_D19][voxel] = prev_states_t[D_0_0_P1_D19][voxel];
      curr_states_t[D_0_0_N1_D19][voxel] = prev_states_t[D_0_0_P1_D19][voxel];
    }
  }
  if (has_two_copies && (active_solver_mask & UDS_PDE_ACTIVE) && (simc.uds_solver_type == LB_UDS)) {
    //DO_UBLK_UDS_DATA(nth_uds, simc.n_user_defined_scalars,ublk,ublk_uds_data){
    ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
      auto ublk_uds_data = ublk->uds_data(nth_uds);
      VOXEL_STATE (*curr_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(    timestep_index)->m_states_uds;
      VOXEL_STATE (*prev_states_uds)[UBLK_TYPE::N_VOXELS] = ublk_uds_data->lb_uds_data(1 ^ timestep_index)->m_states_uds;
      ccDO_UBLK_VOXELS(voxel){
	curr_states_uds[D_0_0_P1_D19][voxel] = prev_states_uds[D_0_0_P1_D19][voxel];
	curr_states_uds[D_0_0_N1_D19][voxel] = prev_states_uds[D_0_0_P1_D19][voxel];
      }
    }
  }
}
#endif

template
VOID scale_up_z_only_states(sUBLK* ublk, dFLOAT fraction, asINT32 timestep_index,
                            BOOLEAN has_two_copies, ACTIVE_SOLVER_MASK active_solver_mask);


template
VOID copy_z_only_states(sUBLK* ublk, asINT32 timestep_index, ACTIVE_SOLVER_MASK active_solver_mask);

#if GPU_COMPILER
template
__HOST__DEVICE__ VOID scale_up_z_only_states(sMBLK* ublk, dFLOAT fraction, asINT32 timestep_index,
                                             BOOLEAN has_two_copies, ACTIVE_SOLVER_MASK active_solver_mask);


template
__HOST__DEVICE__ VOID copy_z_only_states(sMBLK* ublk, asINT32 timestep_index,
                                         ACTIVE_SOLVER_MASK active_solver_mask);
#endif
