/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "sim.h"
#include "surfel_vertices.h"
#include "shob_groups.h"
#include "sampling_surfel.h"
#include "film_comm.h"
#include "mlrf.h"
#include PHYSICS_H

cSURFEL_VERTICES_INFO g_surfel_vertices_info;

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
template <typename SURFEL_TYPE>
VOID cSURFEL_VERTICES_INFO::remap_surfel_vertex_indices_for_old_stencil_construction(SURFEL_TYPE surfel) {

  if(surfel->is_even())
    return;

  asINT32 n_vertices = surfel->p_data()->n_vertices;
  DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->p_data()->first_vertex_index;
  DGF_VERTEX_INDEX vertex_index_table_index = first_vertex_index_table_index;
  ccDOTIMES(i, n_vertices) {
    // The tail_vertex is the tail of a half edge.
    DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[vertex_index_table_index];
    DGF_VERTEX_INDEX vertex_first_surfel_index = m_vertex_first_surfel_indices[tail_vertex];
    DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[ (i == n_vertices - 1
                                                             ? first_vertex_index_table_index
                                                             : vertex_index_table_index + 1) ];
    DGF_VERTEX_INDEX opposite_head_vertex = m_surfel_vertex_indices[ (i == 0
                                                                      ? first_vertex_index_table_index + n_vertices - 1
                                                                      : vertex_index_table_index - 1) ];
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].surfel = surfel;
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].head_vertex = head_vertex;
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].opposite_head_vertex = opposite_head_vertex;
#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    if (tail_vertex == 1082) {
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("regular surfel %d", surfel->id());
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("i %d vertex_first_surfel_index %d n_s %d",i, vertex_first_surfel_index, m_vertex_n_surfels[tail_vertex]);
    }
#endif
    ++m_vertex_n_surfels[tail_vertex];
    ++vertex_index_table_index;
  }
}
#endif

template <typename SURFEL_TYPE>
VOID cSURFEL_VERTICES_INFO::remap_surfel_vertex_indices(SURFEL_TYPE surfel) {

  if(surfel->is_even())
    return;

  asINT32 n_vertices = surfel->stencil()->n_vertices;
  DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->stencil()->first_vertex_index;
  DGF_VERTEX_INDEX vertex_index_table_index = first_vertex_index_table_index;

  ccDOTIMES(i, n_vertices) {
    // The tail_vertex is the tail of a half edge.
    DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[vertex_index_table_index];
    DGF_VERTEX_INDEX vertex_first_surfel_index = m_vertex_first_surfel_indices[tail_vertex];
    DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[ (i == n_vertices - 1
                                                             ? first_vertex_index_table_index
                                                             : vertex_index_table_index + 1) ];
    DGF_VERTEX_INDEX opposite_head_vertex = m_surfel_vertex_indices[ (i == 0
                                                                      ? first_vertex_index_table_index + n_vertices - 1
                                                                      : vertex_index_table_index - 1) ];
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].surfel = surfel;
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].head_vertex = head_vertex;
    m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].opposite_head_vertex = opposite_head_vertex;
#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    if (tail_vertex == 1082) {
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("regular surfel %d", surfel->id());
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("i %d vertex_first_surfel_index %d n_s %d",i, vertex_first_surfel_index, m_vertex_n_surfels[tail_vertex]);
    }
#endif
    ++m_vertex_n_surfels[tail_vertex];
    ++vertex_index_table_index;
  }
}

BOOLEAN edge_is_shared_and_could_connect(
                            SURFACE_SHOB surfel, 
                            asINT32 surfel_tail_vertex, 
                            SURFACE_SHOB candidate_surfel, 
                            asINT32 candidate_surfel_head_vertex)
{
  // For shell conduction surfels, connect only to other shell conduction surfels
  //if (surfel->is_conduction_shell() && !candidate_surfel->is_conduction_shell())
  //  return FALSE;

  BOOLEAN surfels_share_an_edge_and_the_normals_are_oriented_consistently = 
    sim.is_2d() || surfel_tail_vertex == candidate_surfel_head_vertex;
  //The above check assumes both surfels have the same vertex ordering (either both right hand or both left hand).  
  //Also we only need to check for a match for one vertex becasue we don't call this function if the other end of the 
  //edge isn't already matching. For 2D, the head and tail vertex are the same for an edge, so return above as TRUE.
  
  //If the surfels don't share and edge or the normals are oriented inconsistenly, they should obviosly not be connected.
  if(!surfels_share_an_edge_and_the_normals_are_oriented_consistently)
    return FALSE;

  BOOLEAN surfels_are_from_same_face = surfel->face_index() == candidate_surfel->face_index();
  
  //If the surfels are from the same face, then only connect them along an edges if both surfels are generated from the
  //same side of the open shell.
  if(surfels_are_from_same_face) {
    BOOLEAN surfels_are_generated_from_same_side_of_thin_shell = 
      candidate_surfel->is_backside() == surfel->is_backside();
    return surfels_are_generated_from_same_side_of_thin_shell;
  } else {
    //Otherwise, If the surfels are from different faces, then we don't care if they are generated from the same side of of an open shell.
   //Instead, we only care of the normals are oriented consistently around the edge
    return TRUE;
  }
  return FALSE;
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
BOOLEAN edge_is_shared_and_should_connect(
                            SURFACE_SHOB surfel, 
                            asINT32 surfel_tail_vertex, 
                            SURFACE_SHOB opposite_surfel, 
                            asINT32 opposite_surfel_head_vertex)
{
  BOOLEAN surfels_share_an_edge_and_the_normals_are_oriented_consistently = 
    surfel_tail_vertex == opposite_surfel_head_vertex; 
  //The above check assumes both surfels have the same vertex ordering (either both right hand or both left hand).  
  //Also we only need to check for a match for one vertex becasue we don't call this function if the other end of the 
  //edge isn't already matching.
  
  //If the surfels don't share and edge or the normals are oriented inconsistenly, they should obviosly not be connected.
  if(!surfels_share_an_edge_and_the_normals_are_oriented_consistently)
    return FALSE;

  BOOLEAN surfels_are_from_same_face = surfel->face_index() == opposite_surfel->face_index();
  
  //If the surfels are from the same face, then only connect them along an edges if both surfels are generated from the
  //same side of the open shell.
  if(surfels_are_from_same_face) {
    BOOLEAN surfels_are_generated_from_same_side_of_thin_shell = 
      opposite_surfel->is_backside() == surfel->is_backside();
    return surfels_are_generated_from_same_side_of_thin_shell;
  } else {
    //Otherwise, If the surfels are from different faces, then we don't care if they are generated from the same side of of an open shell.
   //Instead, we only care of the normals are oriented consistently around the edge
    return TRUE;
  }
  return FALSE;
}
#endif

SURFEL_HALF_EDGE cSURFEL_VERTICES_INFO::get_edge_from_surfel_and_tail_vertex(const SURFACE_SHOB surfel, const SIM_VERTEX tail_vertex)
{
  SURFEL_HALF_EDGE edge = NULL;
  asINT32 n_edges = surfel->stencil()->n_vertices;
  sSURFEL_HE_VERTEX_INFO this_edge(surfel);

  ccDOTIMES(edge_num, n_edges) {
    this_edge.update_from_edge_num(edge_num);

    if (this_edge.tail_vertex() == tail_vertex) {
      edge = this_edge.get_ptr();
      break;
    }
  }

  return edge;
}

template <typename SURFEL_TYPE>
VOID cSURFEL_VERTICES_INFO::compute_half_edge_info(SURFEL_TYPE surfel)
{
  asINT32 n_vertices = surfel->stencil()->n_vertices;
  if (!n_vertices)
    return;

  sSURFEL_HE_VERTEX_INFO surfel_edge(surfel);

  // Exit if this surfel's half-edges have already been computed
  if (surfel_edge.get_ptr()->edge_length != -1)
    return;

  ccDOTIMES(vertex_num, n_vertices) {
    surfel_edge.update_from_vertex_num(vertex_num);

    const DGF_VERTEX_INDEX head_vertex_index = surfel_edge.head_vertex_index();
    const DGF_VERTEX_INDEX tail_vertex_index = surfel_edge.tail_vertex_index();
    const SIM_VERTEX head_vertex = surfel_edge.head_vertex();
    const SIM_VERTEX tail_vertex = surfel_edge.tail_vertex();

    /* In 2D, the definition of an edge or half-edge requires some clarification:
     * Here, the edge/half-edge is really a point, and the "tail_vertex" is the location of this
     * edge point whereas the "head_vertex" will refer the other end of this surfel.
     * When getting the coordinates of the head and tail of an edge, we should be careful for 2D cases
     * in which head and tail refers to the two end locations of the surfels
     * and hence, for 2D cases, we use an imaginary unit z-vector instead (so that
     * other calculations can proceed without any modifications).
     */

    DGF_VERTEX_INDEX first_vertex_surfel_index;
    if(sim.is_3d()){
      first_vertex_surfel_index = vertex_first_surfel_index(head_vertex_index);
    }else{
      first_vertex_surfel_index = vertex_first_surfel_index(tail_vertex_index);
    }

    dFLOAT vector_tail_to_surfel_centroid[3];
    vsub(vector_tail_to_surfel_centroid, surfel->centroid, tail_vertex->coord); 

    dFLOAT vector_tail_to_head[3];

    if (sim.is_3d()) {
      vsub(vector_tail_to_head, head_vertex->coord, tail_vertex->coord);
    } else {
      // For 2D cases, the tail to head vector is either the +Z or -Z axis
      // This can be determined by a cross of (edge to surfel centroid) and (surfel normal)
      // Further, for 2D cases, tail vertex is the same as the edge since edge is a point
      dFLOAT unit_vector_edge_to_surfel_centroid[3];
      vcopy(unit_vector_edge_to_surfel_centroid, vector_tail_to_surfel_centroid);
      vunitize(unit_vector_edge_to_surfel_centroid);
      vcross(vector_tail_to_head, unit_vector_edge_to_surfel_centroid, surfel->normal);
    }

    dFLOAT unit_vector_tail_to_head[3];
    vcopy(unit_vector_tail_to_head, vector_tail_to_head);
    vunitize(unit_vector_tail_to_head);

    const dFLOAT pi = acos(-1.0);
    SURFACE_SHOB smallest_angle_surfel = NULL;
    dFLOAT smallest_angle = 2.0 * pi;

    dFLOAT vector_tail_to_projection[3];
    dFLOAT scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_surfel_centroid);
    vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

    dFLOAT vector_edge_to_surfel_centroid[3];
    vsub(vector_edge_to_surfel_centroid, vector_tail_to_surfel_centroid, vector_tail_to_projection);

    dFLOAT unit_vector_edge_to_surfel_centroid[3];
    vcopy(unit_vector_edge_to_surfel_centroid, vector_edge_to_surfel_centroid);
    vunitize(unit_vector_edge_to_surfel_centroid);

    //SURFACE_SHOB neighbor_surfel = NULL;
    //BOOLEAN candidate_is_found = FALSE;
    //Iterate over all the other surfels that share this edge's head vertex
    asINT32 num_surfels_at_vertex = 0; 
    if(sim.is_3d()){
      num_surfels_at_vertex = m_vertex_n_surfels[head_vertex_index];   
    }else{
      num_surfels_at_vertex = m_vertex_n_surfels[tail_vertex_index];   
    }

    BOOLEAN found_shell_neighbor_on_face = FALSE;

    ccDOTIMES(s, num_surfels_at_vertex) {
      sSURFEL_VERTEX_PAIR *vertex_surfel = &(m_vertex_surfels[first_vertex_surfel_index + s]);
      SURFACE_SHOB candidate_surfel = vertex_surfel->surfel;

      DGF_VERTEX_INDEX candidate_surfel_head_vertex_index = vertex_surfel->head_vertex;
      DGF_VERTEX_INDEX candidate_surfel_opposite_head_vertex_index = vertex_surfel->opposite_head_vertex;

#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    if (surfel->id() == 346 && head_vertex_index == 242) {
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("compute_half_edge_info surfel %d", surfel->id());
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("s %d first_vertex_surfel_index %d n_s %d",s, first_vertex_surfel_index, m_vertex_n_surfels[head_vertex_index]);
      if(candidate_surfel)
        LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("candidate_surfel %d", candidate_surfel->id());
    }
#endif
      // Sampling surfels should be connected only to sampling surfels and regular only to regular
      if (surfel->is_sampling_surfel() != candidate_surfel->is_sampling_surfel()) //This happened that one time...and again in PR55835.
        continue;
    
      // At a VR transition, connect only to odd surfels
      if ((surfel == candidate_surfel) || candidate_surfel->is_even())
        continue;

      // shell surfels should only be matched with conduction surfels (either another shell or a conduction surface)
      if (surfel->is_conduction_shell() &&  !(candidate_surfel->is_conduction_surfel()))
        continue;

      /* Current surfel's head vertex matching with shob_surfel's head/opposite head vertex works
       * only for 3D cases. For 2D, any surfel (other than itself) that shares the vertex is a candidate
       */
      if (edge_is_shared_and_could_connect(surfel, tail_vertex_index, candidate_surfel, candidate_surfel_head_vertex_index)) {

        dFLOAT angle = 2.0 * pi;

        // check if the candidate surfel is a coupled surfel	
        // this prevents fluid wsurfel to be added to the neighbor in open shells
        BOOLEAN coupled_cand_surf = FALSE;
        if (candidate_surfel->is_conduction_interface()) {
          REALM realm = (candidate_surfel->is_conduction_surfel()) ? STP_COND_REALM : STP_FLOW_REALM;
          SURFEL cand_surfel = regular_surfel_from_id(candidate_surfel->id(), realm);
          SURFEL coupled_surfel = cand_surfel->conduction_interface_base_data()->m_coupled_surfel;
          if (coupled_surfel != NULL) {
            coupled_cand_surf = (coupled_surfel->id() == surfel->id());
          }
        }

        // If candidate surfel is the opposite counterpart, use default value of
        // angle = 2pi. This is necessary since otherwise the angle determination
        // may end up giving angle = 0 due to numerical precision errors.
        if ( (surfel->m_opposite_surfel != candidate_surfel) && (!coupled_cand_surf) ) {
          dFLOAT vector_tail_to_candidate_surfel_centroid[3];
          vsub(vector_tail_to_candidate_surfel_centroid, candidate_surfel->centroid, tail_vertex->coord);

          scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_candidate_surfel_centroid);
          vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

          dFLOAT unit_vector_edge_to_candidate_surfel_centroid[3];
          vsub(unit_vector_edge_to_candidate_surfel_centroid, vector_tail_to_candidate_surfel_centroid, vector_tail_to_projection);
          vunitize(unit_vector_edge_to_candidate_surfel_centroid);

          // Use temporary edge vectors to eliminate non-feasible surfels
          dFLOAT surfel_edge_vector[3];
          vcross(surfel_edge_vector, unit_vector_edge_to_surfel_centroid, surfel->normal);
          dFLOAT candidate_surfel_edge_vector[3];
          vcross(candidate_surfel_edge_vector, unit_vector_edge_to_candidate_surfel_centroid, candidate_surfel->normal);

          // All valid candidates will have opposite directed edge vectors
          if (vdot(surfel_edge_vector, candidate_surfel_edge_vector) < -0.95) {
            // Cross product of the edge to centroid vectors tells us if the angle is in the
            // first or second half. If the cross product is same direction as the edge
            // vector, it is 1/2 quadrants, if not, it is 3/4 quadrants
            dFLOAT cross_edge_to_centroid_vectors[3];
            vcross(cross_edge_to_centroid_vectors, unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);

            // The dot product of the two edge to centroid vectors is used to compute the angle
            dFLOAT dot_edge_to_centroid_vectors = vdot(unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);

            // protection for numerical precision
            dot_edge_to_centroid_vectors = std::min(dot_edge_to_centroid_vectors, 1.0);
            dot_edge_to_centroid_vectors = std::max(dot_edge_to_centroid_vectors, -1.0);

            if (vdot(cross_edge_to_centroid_vectors, unit_vector_tail_to_head) > 0.0) { // 1/2 quadrants
              angle = acos(dot_edge_to_centroid_vectors);
            } else { // 3/4 quadrants
              angle = 2.0 * pi - acos(dot_edge_to_centroid_vectors);
            }

            // Consistency check to help catch any issues with angle calculation
            cassert(!std::isnan(angle));
          }
        }

        if (surfel->is_conduction_shell() && candidate_surfel->is_conduction_shell()
            && (surfel->m_face_index == candidate_surfel->m_face_index)) {
          smallest_angle = angle;
          smallest_angle_surfel = candidate_surfel;
          found_shell_neighbor_on_face = TRUE;
        }
        else if (angle < smallest_angle && !found_shell_neighbor_on_face) {
          smallest_angle = angle;
          smallest_angle_surfel = candidate_surfel;
        }
      }
    } // loop over all surfels at vertex

    SURFEL_HALF_EDGE edge = surfel_edge.get_ptr();

    /** Load edge properties (except for neighbor information) */
    edge->edge_length = vlength(vector_tail_to_head);
    edge->normal_dist = vlength(vector_edge_to_surfel_centroid);

    ccDOTIMES (i, 3) {
      /* We want edge normals to be directed away from the surfels */
      edge->normal[i] = -1.0 * unit_vector_edge_to_surfel_centroid[i];
      edge->edge_dir[i] = unit_vector_tail_to_head[i];
    }

    /** Load neighbor information, if it exists */
    if (smallest_angle_surfel != NULL 
        &&(smallest_angle_surfel->is_backside() == surfel->is_backside() 
        || surfel->m_opposite_surfel == smallest_angle_surfel) ) { //&& !shob_is_sampling_surfel(smallest_angle_surfel)  //TO_BE_DONE
      edge->neighbor_surfel = smallest_angle_surfel;
      // Head vertex on current edge is neighbor edge's tail vertex
      // For a 2D case, since edge is a point, head vertex = tail vertex
      edge->neighbor_edge = get_edge_from_surfel_and_tail_vertex(smallest_angle_surfel, sim.is_3d() ? head_vertex : tail_vertex);
    } else {
      edge->neighbor_surfel = NULL;
      edge->neighbor_edge = NULL;
    }
#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    if (surfel->id() != -346 || surfel->id() == 5345) {
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("Surfel %d, ver %d: edge: %p, len: %.17g, dir: %.17g %.17g %.17g, n_s: %d, n_e: %p",
          surfel->id(), vertex_num, (void *)NULL/*edge*/,
          edge->edge_length, edge->edge_dir[0], edge->edge_dir[1], edge->edge_dir[2],
          smallest_angle_surfel != NULL ? smallest_angle_surfel->id() : -1,
          (void *)NULL/*edge->neighbor_edge*/);
    }
#endif

  }
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
template <typename SURFEL_TYPE>
VOID cSURFEL_VERTICES_INFO::assign_half_edge_neighbors(SURFEL_TYPE surfel) {
  asINT32 n_vertices = surfel->p_data()->n_vertices;
  if (!n_vertices)
    return;

  DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->p_data()->first_vertex_index;
  asINT32 tail_v = n_vertices - 1;
  DGF_VERTEX_INDEX local_tail_vertex_indices = m_surfel_vertex_indices[first_vertex_index_table_index + tail_v];

  ccDOTIMES(head_v, n_vertices) {
    DGF_VERTEX_INDEX local_head_vertex_indices = m_surfel_vertex_indices[first_vertex_index_table_index + head_v];

    SIM_VERTEX head_vertex = surfel_vertex_from_global_index(head_v == n_vertices - 1 ? first_vertex_index_table_index : first_vertex_index_table_index + head_v + 1);
    SIM_VERTEX tail_vertex = surfel_vertex_from_global_index(first_vertex_index_table_index + head_v);

    dFLOAT vector_tail_to_surfel_centroid[3];
    vsub(vector_tail_to_surfel_centroid, surfel->centroid, tail_vertex->coord); 

    dFLOAT vector_tail_to_head[3]; 
    vsub(vector_tail_to_head, head_vertex->coord, tail_vertex->coord);

    dFLOAT unit_vector_tail_to_head[3];
    vcopy(unit_vector_tail_to_head, vector_tail_to_head);
    vunitize(unit_vector_tail_to_head);

    dFLOAT vector_tail_to_projection[3];
    dFLOAT scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_surfel_centroid);
    vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

    dFLOAT vector_edge_to_surfel_centroid[3];
    vsub(vector_edge_to_surfel_centroid, vector_tail_to_surfel_centroid, vector_tail_to_projection);

    dFLOAT unit_vector_edge_to_surfel_centroid[3];
    vcopy(unit_vector_edge_to_surfel_centroid, vector_edge_to_surfel_centroid);
    vunitize(unit_vector_edge_to_surfel_centroid);

    SURFACE_SHOB smallest_angle_surfel = NULL;
    dFLOAT pi = acos(-1.0);
    dFLOAT smallest_angle = 2.0 * pi;

    SURFACE_SHOB neighbor_surfel = NULL;
    // Iterate over the surfels registered with head_vertex until we find a surfel
    // whose head vertex is the same as tail_vertex.
    DGF_VERTEX_INDEX first_vertex_surfel_index = m_vertex_first_surfel_indices[local_head_vertex_indices];
    ccDOTIMES(s, m_vertex_n_surfels[local_head_vertex_indices]) {
      // CONDUCTION-TODO: Should rename opposite_surfel to (maybe) edge_surfel since
      // opposite terminology is reserved for double-sided surfel pairing
      SURFACE_SHOB opposite_surfel = m_vertex_surfels[ first_vertex_surfel_index + s ].surfel;

      if(surfel->is_sampling_surfel() != opposite_surfel->is_sampling_surfel()) //This happened that one time...
        continue;
    
      if ((surfel == opposite_surfel) || (surfel->m_opposite_surfel == opposite_surfel) ||
          opposite_surfel->is_even()) //Only connect to odd surfels if at a VR transition.
        continue;

      DGF_VERTEX_INDEX opposite_head_vertex  = m_vertex_surfels[ first_vertex_surfel_index + s ].head_vertex;

      if (edge_is_shared_and_should_connect(surfel, local_tail_vertex_indices, opposite_surfel,
                                            opposite_head_vertex)) {

        dFLOAT angle = 2 * pi;

        dFLOAT vector_tail_to_candidate_surfel_centroid[3];
        vsub(vector_tail_to_candidate_surfel_centroid, opposite_surfel->centroid, tail_vertex->coord);

        scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_candidate_surfel_centroid);
        vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

        dFLOAT unit_vector_edge_to_candidate_surfel_centroid[3];
        vsub(unit_vector_edge_to_candidate_surfel_centroid, vector_tail_to_candidate_surfel_centroid, vector_tail_to_projection);
        vunitize(unit_vector_edge_to_candidate_surfel_centroid);

        // Use temporary edge vectors to eliminate non-feasible surfels
        dFLOAT surfel_edge_vector[3];
        vcross(surfel_edge_vector, unit_vector_edge_to_surfel_centroid, surfel->normal);
        dFLOAT candidate_surfel_edge_vector[3];
        vcross(candidate_surfel_edge_vector, unit_vector_edge_to_candidate_surfel_centroid, opposite_surfel->normal);

        // All valid candidates will have opposite directed edge vectors
        if (vdot(surfel_edge_vector, candidate_surfel_edge_vector) < -0.95) {
          // Cross product of the edge to centroid vectors tells us if the angle is in the
          // first or second half. If the cross product is same direction as the edge
          // vector, it is 1/2 quadrants, if not, it is 3/4 quadrants
          dFLOAT cross_edge_to_centroid_vectors[3];
          vcross(cross_edge_to_centroid_vectors, unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);

          // The dot product of the two edge to centroid vectors is used to compute the angle
          dFLOAT dot_edge_to_centroid_vectors = vdot(unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);
          // protection for numerical precision
          dot_edge_to_centroid_vectors = std::min(dot_edge_to_centroid_vectors, 1.0);
          dot_edge_to_centroid_vectors = std::max(dot_edge_to_centroid_vectors, -1.0);

          if (vdot(cross_edge_to_centroid_vectors, unit_vector_tail_to_head) > 0.0) { // 1/2 quadrants
            angle = acos(dot_edge_to_centroid_vectors);
          } else { // 3/4 quadrants
            angle = 2.0 * pi - acos(dot_edge_to_centroid_vectors);
          }
        }

        if (angle <= smallest_angle){
          smallest_angle = angle;
          neighbor_surfel = opposite_surfel;
        }
      }
    }

    if (neighbor_surfel == nullptr && surfel->m_opposite_surfel)
      m_surfel_half_edges[first_vertex_index_table_index + tail_v].neighbor_surfel = surfel->m_opposite_surfel;
    else
      m_surfel_half_edges[first_vertex_index_table_index + tail_v].neighbor_surfel = neighbor_surfel;
    
    tail_v = head_v;
    local_tail_vertex_indices = local_head_vertex_indices;
  }
}

// This must called after the surfel descriptors are read, but should be called before the ublk descs
// are read, because it allocates and frees a large chunk of tmp storage, and we would prefer
// that it not raise the peak size of the SP.

/*
VOID cSURFEL_VERTICES_INFO::remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges() {
  DGF_VERTEX_INDEX local_index = 0;
  DGF_VERTEX_INDEX *global_to_local_map = xnew DGF_VERTEX_INDEX[m_n_global_surfel_vertices];
  ccDOTIMES(i, m_n_global_surfel_vertices) {
    if ((*m_is_surfel_vertex_used)[i])
      global_to_local_map[i] = local_index++;
  }
  m_n_surfel_vertices = local_index;
  m_vertex_first_surfel_indices = cnew DGF_VERTEX_INDEX [ m_n_surfel_vertices ]; // zero
  m_surfel_vertex_indices.resize(m_surfel_vertex_global_indices.size());
  ccDOTIMES(i, m_surfel_vertex_global_indices.size()) {
    // Map vertex index from global to local.
    m_surfel_vertex_indices[i] = global_to_local_map[ m_surfel_vertex_global_indices[i] ];
    // Initially fill m_vertex_first_surfel_indices with a simple surfel count per vertex.
    ++m_vertex_first_surfel_indices[ m_surfel_vertex_indices[i] ];
  }
  delete[] global_to_local_map;

  // Now convert m_vertex_first_surfel_indices to a cumulative count for all prior surfels.
  DGF_VERTEX_INDEX cumulative_vertex_refs = 0;
  ccDOTIMES(i, m_n_surfel_vertices) {
    DGF_VERTEX_INDEX vertex_n_refs = m_vertex_first_surfel_indices[i];
    m_vertex_first_surfel_indices[i] = cumulative_vertex_refs;
    cumulative_vertex_refs += vertex_n_refs;
  }

  m_vertex_surfels = xnew sSURFEL_VERTEX_PAIR[ m_surfel_vertex_indices.size() ];
  m_vertex_n_surfels = cnew uINT16[ m_n_surfel_vertices ]; // zero

  // Iterate over regular surfels and sampling surfels. The type of surfel is SURFACE_SHOB
  // because that is the common base class of regular surfels and sampling surfels.
  // And for each surfel, this does something....
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      remap_surfel_vertex_indices_for_old_stencil_construction(surfel);
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      remap_surfel_vertex_indices_for_old_stencil_construction(isurfel_pair->m_interior_surfel);
      remap_surfel_vertex_indices_for_old_stencil_construction(isurfel_pair->m_exterior_surfel);
    }

    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      remap_surfel_vertex_indices_for_old_stencil_construction(sampling_surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      remap_surfel_vertex_indices_for_old_stencil_construction(slrf_surfel_pair->m_exterior_surfel);
      remap_surfel_vertex_indices_for_old_stencil_construction(slrf_surfel_pair->m_interior_surfel);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        asINT32 n_vertices = surfel->p_data()->n_vertices;
        if (!n_vertices)
          continue;
        DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->p_data()->first_vertex_index;
        DGF_VERTEX_INDEX vertex_index_table_index = first_vertex_index_table_index;
        ccDOTIMES(i, n_vertices) {
          // tail_vertex is the tail of a half edge
          DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[vertex_index_table_index];
          DGF_VERTEX_INDEX vertex_first_surfel_index = m_vertex_first_surfel_indices[tail_vertex];
          DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[ (i == n_vertices - 1
              ? first_vertex_index_table_index
                  : vertex_index_table_index + 1) ];
          DGF_VERTEX_INDEX opposite_head_vertex = m_surfel_vertex_indices[ (i == 0
              ? first_vertex_index_table_index + n_vertices - 1
                  : vertex_index_table_index - 1) ];
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].surfel = surfel;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].head_vertex = head_vertex;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].opposite_head_vertex = opposite_head_vertex;
          ++m_vertex_n_surfels[tail_vertex];
          ++vertex_index_table_index;
        }
      }
    }
  }

  size_t n_half_edges = m_surfel_vertex_indices.size();
  m_surfel_half_edges = xnew sSURFEL_HALF_EDGE [ n_half_edges ];
  ccDOTIMES(he, n_half_edges) {
    m_surfel_half_edges[he].edge_length = -1;
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      assign_half_edge_neighbors(surfel);
    }

    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      assign_half_edge_neighbors(sampling_surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      assign_half_edge_neighbors(slrf_surfel_pair->m_exterior_surfel);
      assign_half_edge_neighbors(slrf_surfel_pair->m_interior_surfel);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        assign_half_edge_neighbors(surfel);
      }
    }
  }
}
*/
#endif

// This must called after the surfel descriptors are read, but should be called before the ublk descs
// are read, because it allocates and frees a large chunk of tmp storage, and we would prefer
// that it not raise the peak size of the SP.
VOID cSURFEL_VERTICES_INFO::remap_surfel_vertex_indices_from_global_to_local_new() {
  DGF_VERTEX_INDEX local_index = 0;
  DGF_VERTEX_INDEX *global_to_local_map = xnew DGF_VERTEX_INDEX[m_n_global_surfel_vertices];

  // If g_pfc_output_shell_mesh_info != 0, we need to store a mapping of the local to global vertex indices
  // for outputting the shell mesh info to be reconstructed as a vtk file.
  if (g_pfc_output_shell_mesh_info || g_pfc_output_shell_stencil_details) {
    m_local_to_global_map = xnew std::vector<DGF_VERTEX_INDEX>();
    m_local_to_global_map->reserve(m_n_global_surfel_vertices);
    ccDOTIMES(i, m_n_global_surfel_vertices) {
      if ((*m_is_surfel_vertex_used)[i]) {
        global_to_local_map[i] = local_index++;
        m_local_to_global_map->push_back(i);
      }
    }
    m_local_to_global_map->shrink_to_fit();
  } else {
    ccDOTIMES(i, m_n_global_surfel_vertices) {
      if ((*m_is_surfel_vertex_used)[i])
        global_to_local_map[i] = local_index++;
    }
  }

  m_n_surfel_vertices = local_index;
  m_vertex_first_surfel_indices = cnew DGF_VERTEX_INDEX [ m_n_surfel_vertices ]; // zero
  m_surfel_vertex_indices.resize(m_surfel_vertex_global_indices.size());
  ccDOTIMES(i, m_surfel_vertex_global_indices.size()) {
    // Map vertex index from global to local.
    m_surfel_vertex_indices[i] = global_to_local_map[ m_surfel_vertex_global_indices[i] ];
    // Initially fill m_vertex_first_surfel_indices with a simple surfel count per vertex.
    ++m_vertex_first_surfel_indices[ m_surfel_vertex_indices[i] ];
  }
  delete[] global_to_local_map;

  // Now convert m_vertex_first_surfel_indices to a cumulative count for all prior surfels.
  DGF_VERTEX_INDEX cumulative_vertex_refs = 0;
  ccDOTIMES(i, m_n_surfel_vertices) {
    DGF_VERTEX_INDEX vertex_n_refs = m_vertex_first_surfel_indices[i];
    m_vertex_first_surfel_indices[i] = cumulative_vertex_refs;
    cumulative_vertex_refs += vertex_n_refs;
  }

  m_vertex_surfels = xnew sSURFEL_VERTEX_PAIR[ m_surfel_vertex_indices.size() ];
  m_vertex_n_surfels = cnew uINT16[ m_n_surfel_vertices ]; // zero

  // Iterate over regular surfels and sampling surfels. The type of surfel is SURFACE_SHOB
  // because that is the common base class of regular surfels and sampling surfels.
  // And for each surfel, this does something....
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      remap_surfel_vertex_indices(surfel);
    }
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      remap_surfel_vertex_indices(surfel);
    }
    DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
      remap_surfel_vertex_indices(wsurfel_flow);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      remap_surfel_vertex_indices(wsurfel_conduction);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      remap_surfel_vertex_indices(contact_surfel);
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      remap_surfel_vertex_indices(isurfel_pair->m_interior_surfel);
      remap_surfel_vertex_indices(isurfel_pair->m_exterior_surfel);
    }

    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      remap_surfel_vertex_indices(sampling_surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      remap_surfel_vertex_indices(slrf_surfel_pair->m_exterior_surfel);
      remap_surfel_vertex_indices(slrf_surfel_pair->m_interior_surfel);
    }

    DO_GHOST_SURFELS_OF_SCALE(quantum, scale) {
      remap_surfel_vertex_indices(quantum.m_surfel);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        asINT32 n_vertices = surfel->stencil()->n_vertices;
        if (!n_vertices)
          continue;
        DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->stencil()->first_vertex_index;
        DGF_VERTEX_INDEX vertex_index_table_index = first_vertex_index_table_index;
        ccDOTIMES(i, n_vertices) {
          // tail_vertex is the tail of a half edge
          DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[vertex_index_table_index];
          DGF_VERTEX_INDEX vertex_first_surfel_index = m_vertex_first_surfel_indices[tail_vertex];
          DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[ (i == n_vertices - 1
              ? first_vertex_index_table_index
                  : vertex_index_table_index + 1) ];
          DGF_VERTEX_INDEX opposite_head_vertex = m_surfel_vertex_indices[ (i == 0
              ? first_vertex_index_table_index + n_vertices - 1
                  : vertex_index_table_index - 1) ];
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].surfel = surfel;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].head_vertex = head_vertex;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].opposite_head_vertex = opposite_head_vertex;
#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
      if (tail_vertex == 1082) {
	LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("ghost surfel %d", surfel->id());
	LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("i %d vertex_first_surfel_index %d n_s %d",i, vertex_first_surfel_index, m_vertex_n_surfels[tail_vertex]);
      }
#endif
          ++m_vertex_n_surfels[tail_vertex];
          ++vertex_index_table_index;
        }
      }
    }
  }
}

VOID cSURFEL_VERTICES_INFO::create_surfel_half_edges()
{
  size_t n_half_edges = m_surfel_vertex_indices.size();
  m_surfel_half_edges = xnew sSURFEL_HALF_EDGE [ n_half_edges ];
  ccDOTIMES(he, n_half_edges) {
    m_surfel_half_edges[he].edge_length = -1;
  }

  // SS-CHECK: We need to have a check on which surfels to include
  // depending on which build_connectivity_map call this is?
  // For example, for the first four calls, shell conduction surfels
  // need not be included in the following
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      compute_half_edge_info(surfel);
    }
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      compute_half_edge_info(surfel);
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      compute_half_edge_info(sampling_surfel);
    }
    DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SAMPLING_SURFEL sampling_surfel = quantum.m_surfel;
        compute_half_edge_info(sampling_surfel);
      }
    }
    DO_SURFEL_FILM_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_half_edge_info(surfel);
      }
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        compute_half_edge_info(surfel);
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      compute_half_edge_info(slrf_surfel_pair->m_exterior_surfel);
      compute_half_edge_info(slrf_surfel_pair->m_interior_surfel);
    }
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_half_edge_info(surfel);
      }
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_half_edge_info(surfel);
      }
    }
#if !BUILD_5G_LATTICE
    // DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
    //   compute_half_edge_info(wsurfel_flow);
    // }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      if (wsurfel_conduction->is_conduction_shell()) {
        compute_half_edge_info(wsurfel_conduction);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      if (contact_surfel->is_conduction_shell()) {
        compute_half_edge_info(contact_surfel);
      }
    }
#endif
  }
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
VOID cSURFEL_VERTICES_INFO::remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction() {
  DGF_VERTEX_INDEX local_index = 0;
  DGF_VERTEX_INDEX *global_to_local_map = xnew DGF_VERTEX_INDEX[m_n_global_surfel_vertices];
  ccDOTIMES(i, m_n_global_surfel_vertices) {
    if ((*m_is_surfel_vertex_used)[i])
      global_to_local_map[i] = local_index++;
  }
  m_n_surfel_vertices = local_index;
  m_vertex_first_surfel_indices = cnew DGF_VERTEX_INDEX [ m_n_surfel_vertices ]; // zero
  m_surfel_vertex_indices.resize(m_surfel_vertex_global_indices.size());
  ccDOTIMES(i, m_surfel_vertex_global_indices.size()) {
    // Map vertex index from global to local.
    m_surfel_vertex_indices[i] = global_to_local_map[ m_surfel_vertex_global_indices[i] ];
    // Initially fill m_vertex_first_surfel_indices with a simple surfel count per vertex.
    ++m_vertex_first_surfel_indices[ m_surfel_vertex_indices[i] ];
  }
  delete[] global_to_local_map;

  // Now convert m_vertex_first_surfel_indices to a cumulative count for all prior surfels.
  DGF_VERTEX_INDEX cumulative_vertex_refs = 0;
  ccDOTIMES(i, m_n_surfel_vertices) {
    DGF_VERTEX_INDEX vertex_n_refs = m_vertex_first_surfel_indices[i];
    m_vertex_first_surfel_indices[i] = cumulative_vertex_refs;
    cumulative_vertex_refs += vertex_n_refs;
  }

  m_vertex_surfels = xnew sSURFEL_VERTEX_PAIR[ m_surfel_vertex_indices.size() ];
  m_vertex_n_surfels = cnew uINT16[ m_n_surfel_vertices ]; // zero

  // Iterate over regular surfels and sampling surfels. The type of surfel is SURFACE_SHOB
  // because that is the common base class of regular surfels and sampling surfels.
  // And for each surfel, this does something....
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      remap_surfel_vertex_indices(surfel);
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      remap_surfel_vertex_indices(isurfel_pair->m_interior_surfel);
      remap_surfel_vertex_indices(isurfel_pair->m_exterior_surfel);
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      remap_surfel_vertex_indices(sampling_surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      remap_surfel_vertex_indices(slrf_surfel_pair->m_exterior_surfel);
      remap_surfel_vertex_indices(slrf_surfel_pair->m_interior_surfel);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        asINT32 n_vertices = surfel->p_data()->n_vertices;
        if (!n_vertices)
          continue;
        DGF_VERTEX_INDEX first_vertex_index_table_index = surfel->p_data()->first_vertex_index;
        DGF_VERTEX_INDEX vertex_index_table_index = first_vertex_index_table_index;
        ccDOTIMES(i, n_vertices) {
          // tail_vertex is the tail of a half edge
          DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[vertex_index_table_index];
          DGF_VERTEX_INDEX vertex_first_surfel_index = m_vertex_first_surfel_indices[tail_vertex];
          DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[ (i == n_vertices - 1
              ? first_vertex_index_table_index
                  : vertex_index_table_index + 1) ];
          DGF_VERTEX_INDEX opposite_head_vertex = m_surfel_vertex_indices[ (i == 0
              ? first_vertex_index_table_index + n_vertices - 1
                  : vertex_index_table_index - 1) ];
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].surfel = surfel;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].head_vertex = head_vertex;
          m_vertex_surfels[ vertex_first_surfel_index + m_vertex_n_surfels[tail_vertex] ].opposite_head_vertex = opposite_head_vertex;
          ++m_vertex_n_surfels[tail_vertex];
          ++vertex_index_table_index;
        }
      }
    }
  }
}


VOID cSURFEL_VERTICES_INFO::create_half_edges_for_old_stencil_construction() {
  size_t n_half_edges = m_surfel_vertex_indices.size();
  m_surfel_half_edges = xnew sSURFEL_HALF_EDGE [ n_half_edges ];
  ccDOTIMES(he, n_half_edges) {
    m_surfel_half_edges[he].edge_length = -1;
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      assign_half_edge_neighbors(surfel);
    }

    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      assign_half_edge_neighbors(sampling_surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      assign_half_edge_neighbors(slrf_surfel_pair->m_exterior_surfel);
      assign_half_edge_neighbors(slrf_surfel_pair->m_interior_surfel);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        assign_half_edge_neighbors(surfel);
      }
    }
  }
}


VOID cSURFEL_VERTICES_INFO::compute_surfel_half_edge_length(SURFEL_PARTICLE_DATA_BASE p_data,
                                                            bool is_right_hand_rule_ordered=true) {
  if (p_data->n_vertices <= 0)
    return;
  asINT32 n_vertices = p_data->n_vertices;
  DGF_VERTEX_INDEX first_vertex_index_table_index = p_data->first_vertex_index;
  asINT32 tail_v = n_vertices - 1;
  DGF_VERTEX_INDEX tail_vertex = m_surfel_vertex_indices[first_vertex_index_table_index + tail_v];
  ccDOTIMES(head_v, n_vertices) {
    DGF_VERTEX_INDEX head_vertex = m_surfel_vertex_indices[first_vertex_index_table_index + head_v];
    sSIM_VERTEX &head = m_surfel_vertices[head_vertex];
    sSIM_VERTEX &tail = m_surfel_vertices[tail_vertex];
    sSIM_VERTEX dist_vector;
    vsub(dist_vector.coord, head.coord, tail.coord);
    dFLOAT edge_length = sqrt(vdot(dist_vector.coord, dist_vector.coord));
    m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_length = edge_length;
    //Ignore degenerate edges or those close to it.
    //This value was arbitrarily chosen based on one case
    if (edge_length > 0.0001) { 
      ccDOTIMES(i,3) {
        if (is_right_hand_rule_ordered) {
          m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[i] = 
                  -dist_vector.coord[i] / edge_length;
        } else {
          m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[i] = 
                  dist_vector.coord[i] / edge_length;
        }
      }
    } else {
      ccDOTIMES(i,3) {
        m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[i] = 0.0;
      }
    }

#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    if (surfel->id() != -346 || surfel->id() == 5345) {
      sSURFEL* smallest_angle_surfel = m_surfel_half_edges[first_vertex_index_table_index + tail_v].neighbor_regular_surfel();
      LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("Surfel %d, ver %d: edge: %p, len: %.17g, dir: %.17g %.17g %.17g, n_s: %d, n_e: %p",
          surfel->id(), head_v, (void *)NULL/*edge*/,
          m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_length,
          -m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[0],
          -m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[1],
          -m_surfel_half_edges[first_vertex_index_table_index + tail_v].edge_dir[2],
          smallest_angle_surfel != NULL ? smallest_angle_surfel->id() : -1,
          (void *)NULL/*edge->neighbor_edge*/);
    }
#endif
    tail_v = head_v;
    tail_vertex = head_vertex;
  }
}
#endif

template <typename SURFEL_TYPE>
VOID compute_off_plane_tolerance(SURFEL_TYPE surfel) {
  dFLOAT tolerance = 0;
  surfel->m_off_plane_tolerance = 0.0;
#if 1
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  asINT32 n_vertices = surfel->p_data()->n_vertices;
  asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;
  asINT32 first_half_edge_index = surfel->p_data()->first_half_edge_index();
#else
  asINT32 n_vertices = surfel->stencil()->n_vertices;
  asINT32 first_vertex_index = surfel->stencil()->first_vertex_index;
  asINT32 first_half_edge_index = surfel->stencil()->first_half_edge_index();
#endif
  BOOLEAN is_right_hand_rule_ordered = !surfel->is_lrf();

  ccDOTIMES(edge_num, n_vertices) {
    SIM_VERTEX vertex = surfel_vertex_from_global_index(first_vertex_index + edge_num);
    dFLOAT dist = surfel->compute_normal_distance_to_centroid(vertex->coord);
    surfel->m_off_plane_tolerance = MIN(0.1, MAX(surfel->off_plane_tolerance(), std::abs(dist)));
  }
  //msg_print("For surfel %d, veretx dist %e Normal %e", surfel->id(), surfel->m_off_plane_tolerance,
  //          tolerance);
#endif
}

template <typename SURFEL_TYPE>
VOID compute_edge_in_plane_normal(SURFEL_TYPE surfel,
                                  dFLOAT edge[N_SPACE_DIMS], 
                                  dFLOAT triangle_normal[N_SPACE_DIMS]){
  dFLOAT edge_in_plane_normal[N_SPACE_DIMS];
  vcross(edge_in_plane_normal, edge, triangle_normal);
  vunitize(edge_in_plane_normal);
  ccDOTIMES(axis, N_SPACE_DIMS){
    surfel->p_data()->triangle_edge_in_plane_normals.push_back(edge_in_plane_normal[axis]);
  }
}

template <typename SURFEL_TYPE>
VOID compute_edge_in_plane_normal(SURFEL_TYPE surfel) {
  asINT32 n_vertices = surfel->p_data()->n_vertices;
  surfel->p_data()->m_n_triangles = n_vertices - 2;
  asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;
  SIM_VERTEX first_vertex = surfel_vertex_from_global_index(first_vertex_index);
  ccDOTIMES(nth_triangle, surfel->p_data()->m_n_triangles){
    SIM_VERTEX second_vertex = surfel_vertex_from_global_index(first_vertex_index + nth_triangle + 1);
    SIM_VERTEX third_vertex  = surfel_vertex_from_global_index(first_vertex_index + nth_triangle + 2);
    dFLOAT edge_1[N_SPACE_DIMS];
    dFLOAT edge_2[N_SPACE_DIMS];
    vsub(edge_1, second_vertex->coord, first_vertex->coord);   
    vsub(edge_2, third_vertex->coord, second_vertex->coord);   
    dFLOAT triangle_normal[N_SPACE_DIMS];
    vcross(triangle_normal, edge_1, edge_2);
    if(vdot(triangle_normal, triangle_normal) < 10 * DFLOAT_EPSILON){
      vzero(triangle_normal);
      ccDOTIMES(axis,3 * N_SPACE_DIMS){
        // just push zeros to the edge normal
        surfel->p_data()->triangle_edge_in_plane_normals.push_back(triangle_normal[0]);
      }
    } else{
      vunitize(triangle_normal);

      compute_edge_in_plane_normal(surfel, edge_1, triangle_normal);
      compute_edge_in_plane_normal(surfel, edge_2, triangle_normal);
      dFLOAT edge_3[N_SPACE_DIMS];
      vsub(edge_3, first_vertex->coord,  third_vertex->coord);   
      compute_edge_in_plane_normal(surfel, edge_3, triangle_normal);
    }

    ccDOTIMES(axis, N_SPACE_DIMS){
      surfel->p_data()->triangle_normals.push_back(triangle_normal[axis]);
    }
  }
}

VOID compute_off_plane_tolerances() {
  // This function is only called for particle model
  cassert(sim.is_particle_model);

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      compute_off_plane_tolerance(surfel);
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      compute_off_plane_tolerance(sampling_surfel);
    }
    DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SAMPLING_SURFEL surfel = quantum.m_surfel;
        compute_off_plane_tolerance(surfel);
      }
    }
    DO_SURFEL_FILM_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_off_plane_tolerance(surfel);
      }
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        compute_off_plane_tolerance(surfel);
      }
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      compute_off_plane_tolerance(slrf_surfel_pair->m_exterior_surfel);
      compute_off_plane_tolerance(slrf_surfel_pair->m_interior_surfel);
    }


    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_off_plane_tolerance(quantum.m_surfel);
      }
    }
  }
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
VOID  cSURFEL_VERTICES_INFO::compute_surfel_half_edge_lengths() {

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      compute_surfel_half_edge_length(surfel->p_data());
      compute_off_plane_tolerance(surfel);
      compute_edge_in_plane_normal(surfel);
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      compute_surfel_half_edge_length(sampling_surfel->p_data());
      compute_off_plane_tolerance(sampling_surfel);
      compute_edge_in_plane_normal(sampling_surfel);
    }
    DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SAMPLING_SURFEL surfel = quantum.m_surfel;
        compute_surfel_half_edge_length(surfel->p_data());
        compute_off_plane_tolerance(surfel);
        compute_edge_in_plane_normal(surfel);
      }
    }
    DO_SURFEL_FILM_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_surfel_half_edge_length(surfel->p_data());
        compute_off_plane_tolerance(surfel);
        compute_edge_in_plane_normal(surfel);
      }
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        compute_surfel_half_edge_length(surfel->p_data());
        compute_off_plane_tolerance(surfel);
        compute_edge_in_plane_normal(surfel);
      }
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      compute_surfel_half_edge_length(slrf_surfel_pair->m_exterior_surfel->p_data());
      compute_off_plane_tolerance(slrf_surfel_pair->m_exterior_surfel);
      compute_edge_in_plane_normal(slrf_surfel_pair->m_exterior_surfel);
      compute_surfel_half_edge_length(slrf_surfel_pair->m_interior_surfel->p_data());
      compute_off_plane_tolerance(slrf_surfel_pair->m_interior_surfel);
      compute_edge_in_plane_normal(slrf_surfel_pair->m_interior_surfel);
    }


    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        compute_surfel_half_edge_length(surfel->p_data());
        compute_off_plane_tolerance(quantum.m_surfel);
        compute_edge_in_plane_normal(quantum.m_surfel);
      }
    }

    // CONDUCTION-TODO: conduction-isurfels should be added here
  }
}
#endif

VOID cSURFEL_VERTICES_INFO::read_surfel_vertices() {
  m_surfel_vertices = xnew sSIM_VERTEX[ m_n_surfel_vertices ];
  asINT32 num_received = 0;
  sSIM_VERTEX vertices[N_VERTICES_PER_BROADCAST];
  ccDO_FROM_BELOW_BY(i, 0, m_n_global_surfel_vertices, N_VERTICES_PER_BROADCAST) {
    DGF_VERTEX_INDEX n_to_read = MIN(N_VERTICES_PER_BROADCAST, m_n_global_surfel_vertices - i);
    MPI_Bcast(vertices, 3 * n_to_read, eMPI_dFLOAT, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);

    ccDOTIMES(j, n_to_read) {
      if ((*m_is_surfel_vertex_used)[i + j])
        m_surfel_vertices[num_received++] = vertices[j];
    }
  }
}

VOID cSURFEL_VERTICES_INFO::delete_vertex_surfels_arrays() {
  if(m_vertex_n_surfels != NULL) {
    delete[] m_vertex_n_surfels;
    m_vertex_n_surfels = NULL;
  }
  if(m_vertex_surfels != NULL) {
    delete[] m_vertex_surfels;
    m_vertex_surfels = NULL;
  }
  if(m_vertex_first_surfel_indices != NULL) {
    delete[] m_vertex_first_surfel_indices;
    m_vertex_first_surfel_indices = NULL;
  }
  if (m_local_to_global_map != NULL) {
    delete m_local_to_global_map;
    m_local_to_global_map = NULL;
  }
}
