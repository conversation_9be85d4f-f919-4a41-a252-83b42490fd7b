#ifndef UBLK_SEED_H
#define UBLK_SEED_H
#include PHYSICS_H
#include "ublk.h"

template<typename UBLK_TYPE_TAG>
__HOST__DEVICE__ inline
VOID tUBLK<UBLK_TYPE_TAG>::reset_states(VOXEL_MASK voxel_mask) {
  auto& simc = get_simc_ref();
  auto full_voxel_mask = default_voxel_mask<VOXEL_MASK>(simc.is_2d());
  if (voxel_mask != full_voxel_mask) {
    DO_ACTIVE_VOXELS_IN_MASK(v, voxel_mask) {
      ccDOTIMES(l, N_MOVING_STATES) {
        lb_states(0)->m_states[l][v] = 0.0;
        if (has_two_copies()) {
          lb_states(1)->m_states[l][v] = 0.0;
        }
      } 
    }
  } else {
    //size_t states_size = has_two_copies() ? sizeof(sUBLK_STATES_DATA) * 2 : sizeof(sUBLK_STATES_DATA) * 1;
    //memset((char *)lb_states(0), 0, states_size);
    auto ublk_ubfloat = this->to_ubfloat_ublk();
    hd_zero(ublk_ubfloat->lb_states(0)->m_states);
    if (has_two_copies()) {
      hd_zero(ublk_ubfloat->lb_states(1)->m_states);
    }
  }
}

template<typename UBLK_TYPE_TAG>
__HOST__DEVICE__ inline
VOID tUBLK<UBLK_TYPE_TAG>::seed(BOOLEAN is_smart_seed, SOLVER_INDEX_MASK seed_solver_index_mask) {
  auto& simc = get_simc_ref();
  // Sometimes the states for solid voxels end up being nan and pollute the
  // states for neighboring voxel during advection, even though
  // post_advect_factor is 0
  auto reset_voxel_mask = default_voxel_mask<VOXEL_MASK>(simc.is_2d());
  if (is_smart_seed) {
    reset_voxel_mask &= ~fluid_like_voxel_mask;
  }

  reset_states(reset_voxel_mask);

  auto ublk_dyn_data = dynamics_data();
  VOXEL_MASK dynamics_voxel_mask = fluid_like_voxel_mask;
  int child_ublk = get_child_ublk_index(get_dyn_voxor());
  if (basic_fluid_voxel_mask.any()) {
    //There is a check for voxel_mask in seed_ublk, it should be fine calling
    //this for all threads in GPU sim
    if (child_ublk_mask(basic_fluid_voxel_mask, child_ublk).any())
       ublk_dyn_data->seed_ublk(this, basic_fluid_voxel_mask, seed_solver_index_mask, is_smart_seed);
    dynamics_voxel_mask &= ~basic_fluid_voxel_mask;
    ublk_dyn_data = (sUBLK_DYNAMICS_DATA*) ublk_dyn_data->next();
  }

  
  while (child_ublk_mask(dynamics_voxel_mask, child_ublk).any()) {
    auINT32 fluid_type = ublk_dyn_data->fluid_type();
    switch (fluid_type) {
    case POROUS_FLUID_TYPE :
    case CURVED_POROUS_FLUID_TYPE :
    case CURVED_HX_POROUS_FLUID_TYPE :
      {
        // reference frame index in physics parameters is at different locations
        auto dyn_data = (sPOROUS_UBLK_DYNAMICS_DATA*) ublk_dyn_data;
        auto special_fluid_voxel_mask = dyn_data->voxel_mask();
        BOOLEAN is_curved_pm = fluid_type != POROUS_FLUID_TYPE;
        //There is a check for voxel_mask in seed_ublk, it should be fine calling
        //this for all threads in GPU sim
        dyn_data->seed_ublk(this, special_fluid_voxel_mask, seed_solver_index_mask, is_smart_seed, TRUE, is_curved_pm);
        dynamics_voxel_mask &= ~special_fluid_voxel_mask;
        ublk_dyn_data = (sUBLK_DYNAMICS_DATA*) ublk_dyn_data->next();
        break;
      }
    case FAN_FLUID_TYPE:
    case TABLE_FAN_FLUID_TYPE:
      {
#if !GPU_COMPILER        
        FAN_UBLK_DYNAMICS_DATA dyn_data = (FAN_UBLK_DYNAMICS_DATA) ublk_dyn_data;
        VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
        dyn_data->seed_ublk(this, special_fluid_voxel_mask, seed_solver_index_mask, is_smart_seed, FALSE, FALSE);
        dynamics_voxel_mask &= ~special_fluid_voxel_mask;
        ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next() ;
        break;
#else
        assert(false);
#endif        
      }
    case CONDUCTION_SOLID_TYPE:
      {
#if !GPU_COMPILER
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
        if (basic_fluid_voxel_mask != VOXEL_MASK_8(0) || dynamics_voxel_mask != fluid_like_voxel_mask)
          msg_internal_error("Conduction UBLKs (ID %d) should be exclusive and cannot have voxels of different types, but basic_fluid_voxel_mask is %d ", this->id(), basic_fluid_voxel_mask.get());
#endif
        CONDUCTION_UBLK_DYNAMICS_DATA dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) ublk_dyn_data;
        VOXEL_MASK_8 conduction_voxel_mask = dyn_data->voxel_mask();
        dyn_data->seed_ublk(this, conduction_voxel_mask, seed_solver_index_mask, is_smart_seed, TRUE);
        dynamics_voxel_mask &= ~conduction_voxel_mask;
        ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
        break;
#else
        assert(false);
#endif
      }
    default:
#if !GPU_COMPILER
      msg_internal_error("Unknown fluid type!");
#else
      assert(false);
#endif
    }
  }
}

#if GPU_COMPILER
// This specialization is put in place to avoid trigger template expansion of seeding code
// on host. This solves a lot of compilaton related issues with host_device 
template<>  
__HOST__DEVICE__ inline
VOID tUBLK<UBLK_SDFLOAT_TYPE_TAG>::seed(BOOLEAN is_smart_seed, SOLVER_INDEX_MASK seed_solver_index_mask) {
  assert(false); //Seeding cant be done on GPU
}
#endif
#endif  
