/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_THREAD_MEAS_H
#define _SIMENG_THREAD_MEAS_H

#include "meas.h"

// class cMEAS_SEND_QUEUE {
//
//  public:
//
//   cMEAS_SEND_QUEUE(asINT32 capacity) : m_queue(capacity) {}
//
//   private:
//
//
//   cSPSC_QUEUE<MEAS_WINDOW> m_queue;
//
//  public:
//
//   // Note that is_empty() accesses both the head and tail indices, which violates the principle that the head
//   // is accessed only by the comm thread and the tail only by the sim thread. This is OK because the worst that
//   // can happen in the case of a race is that the comm thread will incompletely drain the queue. 
//
//   VOID add_entry(sMEAS_WINDOW *window);
//   VOID drain();                         // Called only by comm thread. Post each ready send in the queue.
//
// };
//
//
// // The completion queue records sends of windows that have been posted but not completed. The
// // comm thread will periodically attempt to complete the requests at the head of the queue, and will set
// // the flag window->m_send_buffer_ready when it is succesful.
//
// class cMEAS_COMPLETION_QUEUE {
//   
//  public:
//
//   sMEAS_WINDOW *m_head;
//   sMEAS_WINDOW *m_tail;
//
//   BOOLEAN is_empty() { return (m_head == m_tail); }
//   VOID process();
// };
//
// // This is a queue of reduction window sends for which the child SP contributions are 
// // not yet available.
//
// class cMEAS_PENDING_SEND_QUEUE {
//
//  public:
//
//   sMEAS_WINDOW *m_head;
//   sMEAS_WINDOW *m_tail;
//
//   VOID process();
// };
#endif /* #ifndef _SIMENG_THREAD_MEAS_H */
