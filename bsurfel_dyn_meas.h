/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Bsurfel dynamics (functional sets and groups)
 *
 * Dalon Work, Exa Corporation
 * Created Oct 16, 2017
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_BSURFEL_DYN_MEAS_H
#define _SIMENG_BSURFEL_DYN_MEAS_H

#include "common_sp.h"
#include "meas_cell.h"
#include THASH_H
#include "memory_pool.h"
#include "bsurfel.h"
#include PHYSICS_H


// forward declarations
struct sMOVING_MEAS_CELL_MGR;

/*
 * Manages the lifetime of a moving measurement cell in a window using an
 * intrusive reference counting scheme. The measurement cell itself is managed
 * by MEAS_CELL_PTR. This level of indirection is necessary because an index is
 * used instead of a pointer if the window is a development window.  The smart
 * pointer does not do any reference counting if the window is a development
 * window. A measurement cell consists of several double floating-point values,
 * or variables. The first value is actually used to store the
 * global_meas_index and the reference count. The length of the array is known
 * by the window and the bsurfel which owns the smart_ptr. It is not known by
 * the pointer itself.
 *
 * When this pointer is destructed, the reference count is decremented. When
 * the count reaches 0, the pointer notifies the sMOVING_MEAS_CELL_MGR that the
 * measurement cell has no more references. The measurement cell is removed from
 * the global map and is returned to the memory pool.
 */

struct sMOVING_MEAS_CELL_SMART_PTR : public MEAS_CELL_PTR {

private:
  sINT16 m_window_id;
  STP_MEAS_CELL_INDEX m_global_meas_cell_index;
  BOOLEAN m_is_development;
  sMOVING_MEAS_CELL_MGR * m_parent;

public:

  sMOVING_MEAS_CELL_SMART_PTR() : m_window_id(-1),
    m_global_meas_cell_index(-1),
    m_is_development(0),
    m_parent(NULL) {};

  // constructor for non-development windows. The pointer is in a
  // valid state when this constructor finishes. If a global id is given,
  // then it is assumed that this is the first reference to the meas_cell variables.
  // The global_meas_index and ref_count will be overwritten.
  sMOVING_MEAS_CELL_SMART_PTR(STP_MEAS_WINDOW_INDEX window_id, sMOVING_MEAS_CELL_MGR* parent, MEAS_CELL_VAR *meas_cell_vars, STP_MEAS_CELL_INDEX gid = -1)
    : m_window_id(window_id),
      m_parent(parent),
      m_is_development(FALSE)
  {
    set_variables(meas_cell_vars);
    sMOVING_MEAS_CELL_ID_AND_REF* header = get_header();
    if (gid != -1) {
      header->global_meas_index = gid;
      header->ref_count = 1;
      m_global_meas_cell_index = gid;
    }
    else {
      m_global_meas_cell_index = header->global_meas_index;
      header->ref_count++;
    }
  }

  // constructor for development windows. Note the ptr is not in
  // a valid state, and must be further initialized by the window
  sMOVING_MEAS_CELL_SMART_PTR(STP_MEAS_WINDOW_INDEX window_id, STP_MEAS_CELL_INDEX gid)
    : m_window_id(window_id),
      m_global_meas_cell_index(gid),
      m_parent(NULL),
      m_is_development(TRUE)
  {
    reset();
  }

  // not copyable, but it is movable
  sMOVING_MEAS_CELL_SMART_PTR(const sMOVING_MEAS_CELL_SMART_PTR&) = delete;
  sMOVING_MEAS_CELL_SMART_PTR & operator = (const sMOVING_MEAS_CELL_SMART_PTR&) = delete;

  sMOVING_MEAS_CELL_SMART_PTR(sMOVING_MEAS_CELL_SMART_PTR&& other)
    : MEAS_CELL_PTR(other),
      m_window_id(other.m_window_id),
      m_global_meas_cell_index(other.m_global_meas_cell_index),
      m_parent(other.m_parent),
      m_is_development(other.m_is_development)
  {
    other.reset();
  }


  sMOVING_MEAS_CELL_SMART_PTR & operator = (sMOVING_MEAS_CELL_SMART_PTR&& other)
  {
    this->~sMOVING_MEAS_CELL_SMART_PTR(); // cleanup the old values, not exception safe, but we don't care
    *static_cast<MEAS_CELL_PTR*>(this) = static_cast<const MEAS_CELL_PTR&>(other);
    m_window_id = other.m_window_id;
    m_global_meas_cell_index = other.m_global_meas_cell_index;
    m_parent = other.m_parent;
    m_is_development = other.m_is_development;
    other.reset();

    return *this;

  };

  // destructor must be defined in bsurfel_dyn_meas.cc to actually use m_parent
  ~sMOVING_MEAS_CELL_SMART_PTR();


  STP_MEAS_WINDOW_INDEX window_id()
  {
    return m_window_id;
  }

  STP_MEAS_CELL_INDEX global_meas_cell_index()
  {
    return m_global_meas_cell_index;
  }

  bool is_last_ref()
  {
    if (!m_is_development && variables() != NULL) {
      sMOVING_MEAS_CELL_ID_AND_REF* header = get_header();
      return header->ref_count == 1;
    }
    else {
      return true;
    }
  }



private:
  sMOVING_MEAS_CELL_ID_AND_REF* get_header()
  {
    return reinterpret_cast<sMOVING_MEAS_CELL_ID_AND_REF*>(variables());
  }

};

struct sGLOBAL_TO_MOVING_MEAS_CELL_TRAITS {
  STP_MEAS_CELL_INDEX HashFcn(const STP_MEAS_CELL_INDEX &global_meas_cell_index) const
  { return global_meas_cell_index; }

  BOOLEAN EqualKeys(const STP_MEAS_CELL_INDEX &key1,
                    const STP_MEAS_CELL_INDEX &key2) const
  { return key1 == key2; }
};

typedef tHASH_TABLE< STP_MEAS_CELL_INDEX, MOVING_MEAS_CELL_PTR, sGLOBAL_TO_MOVING_MEAS_CELL_TRAITS>  sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE;

struct sMOVING_MEAS_CELL_MGR {

  // constructor
  sMOVING_MEAS_CELL_MGR(STP_MEAS_WINDOW_INDEX window_id, sINT16 n_variables)
    : m_window_id(window_id),
      m_moving_meas_cell_vars_pool(n_variables + 1, 100), // add 1 for the header variable
      m_global_index_to_moving_meas_cell_map(128) {};

private:
  STP_MEAS_WINDOW_INDEX m_window_id;
  tMEMORY_POOL<MEAS_CELL_VAR> m_moving_meas_cell_vars_pool;
  sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE m_global_index_to_moving_meas_cell_map;
  std::vector<sMOVING_MEAS_CELL_SMART_PTR> m_orphaned_meas_cells;

public:
  // This function checks the hash table. If it doesn't exist, then the
  // measurement variables are pulled from the memory pool.
  // The result is then wrapped in a reference-counted pointer object.
  sMOVING_MEAS_CELL_SMART_PTR get_moving_meas_cell(STP_MEAS_CELL_INDEX gid)
  {
    MOVING_MEAS_CELL_PTR *moving_meas_cell_ptr = m_global_index_to_moving_meas_cell_map.Get(gid);
    if (moving_meas_cell_ptr == NULL) {
      MOVING_MEAS_CELL_PTR moving_meas_cell = m_moving_meas_cell_vars_pool.pop();
      memset(moving_meas_cell, 0, m_moving_meas_cell_vars_pool.n_bytes_per_array());
      m_global_index_to_moving_meas_cell_map.Put(gid, moving_meas_cell);
      return sMOVING_MEAS_CELL_SMART_PTR(m_window_id, this, moving_meas_cell, gid);
    }
    else {
      return sMOVING_MEAS_CELL_SMART_PTR(m_window_id, this, *moving_meas_cell_ptr);
    }
  };

  bool has_moving_meas_cell(STP_MEAS_CELL_INDEX gid)
  {
    return m_global_index_to_moving_meas_cell_map.IsKeyPresent(gid);
  }

  uINT64 n_moving_meas_cells() const
  {
    return m_global_index_to_moving_meas_cell_map.NumEntries();
  }

  void write_ckpt()
  {
    asINT32 sp_meas_cell_size = m_moving_meas_cell_vars_pool.n_bytes_per_array();

    write_ckpt_lgi(sp_meas_cell_size);
    tTHASH_DO(keyVar, valueVar, &m_global_index_to_moving_meas_cell_map, sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE) {

      // overwrite the first element with the global_meas_index,
      // we don't need to ckpt the reference count.
      // After writing, restore the header
      sMOVING_MEAS_CELL_ID_AND_REF* header = reinterpret_cast<sMOVING_MEAS_CELL_ID_AND_REF*>(valueVar);
      sMOVING_MEAS_CELL_ID_AND_REF copy = *header;
      reinterpret_cast<int64_t&>(*valueVar) = (int64_t) header->global_meas_index;
      ::write_ckpt_lgi(valueVar, sp_meas_cell_size);
      *header = copy;
    }
  }

  size_t ckpt_len() const
  {
    return m_moving_meas_cell_vars_pool.n_bytes_per_array() * n_moving_meas_cells();
  }

  size_t n_bytes_per_cell() const
  {
    return m_moving_meas_cell_vars_pool.n_bytes_per_array();
  }

  void write_ckpt(sCKPT_BUFFER& pio_ckpt_buff, THASH_INDEX& first_idx)
  {
    const asINT32 sp_meas_cell_size = m_moving_meas_cell_vars_pool.n_bytes_per_array();
    const THASH_INDEX size = m_global_index_to_moving_meas_cell_map.Capacity();
    const std::vector<THASH_INDEX> & table_indexes = m_global_index_to_moving_meas_cell_map._KvpIndexTable();
    const std::vector<typename sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE::cKEY_VALUE_PAIR> & pairs =
      m_global_index_to_moving_meas_cell_map.m_keyValuePairs;
    for (; first_idx < size; first_idx++)
    {
      const THASH_INDEX kvp_index = table_indexes[first_idx];
      if (sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE::IsKvpIndexValid(kvp_index))
      {
        MOVING_MEAS_CELL_PTR val = pairs[kvp_index].Value();
        sMOVING_MEAS_CELL_ID_AND_REF* const header = reinterpret_cast<sMOVING_MEAS_CELL_ID_AND_REF*>(val);
        sMOVING_MEAS_CELL_ID_AND_REF copy = *header;
        reinterpret_cast<int64_t&>(*val) = (int64_t) header->global_meas_index;
        pio_ckpt_buff.write(val, sp_meas_cell_size);
        *header = copy;

        // Break loop if remaining capacity in the buffer is not enough to add next data
        if (pio_ckpt_buff.m_capacity - pio_ckpt_buff.m_offset < sp_meas_cell_size)
          break;
      }
    }
  }

  // called by the smart pointers when the reference count falls to zero
  void delete_moving_meas_cell(MOVING_MEAS_CELL_PTR moving_meas_cell)
  {
    auto header = reinterpret_cast<sMOVING_MEAS_CELL_ID_AND_REF*>(moving_meas_cell);
    auto gid = header->global_meas_index;
    cassert(header->ref_count == 0); // If this is not zero, something is wrong

    // This could be a performance problem, because of constant freeing and
    // allocating.  We could just zero it out and leave it, and not return it
    // to the pool...but that could induce a different performance problem,
    // where every SP ends up with every gid in the map. This is extra memory.
    // We could mitigate this by setting the pointer to nullptr and leaving
    // just the key hanging around, but recycle the actual pointers in the
    // pool. We would have to add a nullptr check when getting a key from the
    // map. Also n_moving_meas_cells() would have to change. Constantly
    // deleting and creating in the tHASH_MAP could lead to memory
    // fragmentation as well?  Probably the best way to handle that particular
    // issue is switching to std::unordered_map with a custom allocator that
    // manages a pool.
    //
    // We will leave it this way for now, since logically this is correct.

    bool success = m_global_index_to_moving_meas_cell_map.Delete(gid);
    cassert(success); // If the index doesn't exist, then we have a problem somewhere
    m_moving_meas_cell_vars_pool.push(moving_meas_cell);
  }

  void add_orphaned_moving_meas_cell(sMOVING_MEAS_CELL_SMART_PTR mcp)
  {
    m_orphaned_meas_cells.emplace_back(std::move(mcp));
  }

  void clear_moving_meas_cells()
  {
    tTHASH_DO(keyVar, valueVar, &m_global_index_to_moving_meas_cell_map, sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE) {
      // leave the first value alone since it is the meas cell index and ref count
      valueVar++;
      ccDOTIMES(j, m_moving_meas_cell_vars_pool.array_size() - 1) {
        *valueVar++ = 0.0F;
      }
    }
    m_orphaned_meas_cells.clear();
  }

  sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE & hash_table()
  {
    return m_global_index_to_moving_meas_cell_map;
  }

};

VOID do_all_bsurfel_measurements();

VOID update_bsurfel_meas_windows(sBSURFEL* bsurfel,
                                 LRF_PHYSICS_DESCRIPTOR lrf,
                                 ACTIVE_SOLVER_MASK active_solver_mask,
                                 STP_GEOM_VARIABLE meas_scale_factor,
                                 dFLOAT input_force[3],
                                 dFLOAT mass_flux);

#endif // _SIMENG_BSURFEL_DYN_MEAS_H
