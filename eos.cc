/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Equation of state
 *--------------------------------------------------------------------------*/
#include "eos.h"

class cEOS : public iEOS
{
public:
  virtual sdFLOAT pressure(sdFLOAT density, sdFLOAT temperature)  {
    sdFLOAT  psi = potential(density);
    
    return density * temperature - .5 * psi * psi;
  }
};

class cEOS_IDEAL_GAS : public cEOS
{
 public:

  cEOS_IDEAL_GAS(const sEOS_PARAMS& params) {}

  STP_PHYS_VARIABLE potential(sdFLOAT density)  {
    return 0.;
  }

  sdFLOAT pressure (sdFLOAT density, sdFLOAT temperature)  {
    return density * temperature;
  }

  EOS_TYPE type() {
    return EOS_IDEAL_GAS;
  }
};

class cEOS_MC : public cEOS
{
public:

  cEOS_MC(sEOS_PARAMS& params) {}

  sdFLOAT potential(sdFLOAT density) {
    return density;
  }

  EOS_TYPE type() {
    return EOS_MC;
  }
};

class cEOS_SC93 : public cEOS
{
  dFLOAT m_alpha;

public:

  cEOS_SC93(sEOS_PARAMS& params) {
    dassert(params.temperature >= 0.);
    
    m_alpha = 2. * sqrt(1. / params.temperature / log(2.));
  }
  
  sdFLOAT potential(sdFLOAT density)  {
    return (1. - pow(2., -density)) * m_alpha;
  }

  EOS_TYPE type() {
    return EOS_SHAN_CHEN_93;
  }
};

class cEOS_SC94 : public cEOS
{
  dFLOAT m_alpha;

public:

  cEOS_SC94( sEOS_PARAMS& params) {
    dassert(params.temperature >= 0.);
    
    m_alpha = sqrt(1. / params.temperature);
  }

  sdFLOAT potential (sdFLOAT density)  {
    return exp(1. - 1. / density) * m_alpha;
  }

  EOS_TYPE type() {
    return EOS_SHAN_CHEN_94;
  }
};

class cEOS_PR : public cEOS
{
  CONST static dFLOAT a0 = 1.4874221936691127516;
  CONST static dFLOAT b0 = 0.2530765865415994623;
  //static const double zeta = 0.3074;

  dFLOAT m_critical_volume;
  dFLOAT m_sound_speed_squared;
  dFLOAT m_sound_speed_bound;
  dFLOAT m_accentric_factor;
  dFLOAT m_temperature;
  dFLOAT m_max_density;
  dFLOAT m_alpha;
  dFLOAT speed_sound_exact_bound;
  dFLOAT pressure_exact_bound;

public:
  
  cEOS_PR(const sEOS_PARAMS& params) : 
    m_critical_volume(1.), 
    m_accentric_factor(0.344) {
    
    dassert(params.temperature > 0.);
  
    m_temperature = params.temperature;
    m_sound_speed_squared = params.sound_speed_squared;
    m_sound_speed_bound = params.sound_speed_bound;

    dFLOAT  kappa = 0.37464 + (1.54226 - 0.26992 * m_accentric_factor) * m_accentric_factor;

    m_alpha = a0 * pow(1. + kappa * (1. - sqrt(m_temperature)), 2.);

    dFLOAT m_alpha2 = 2.0 * m_alpha;

    dFLOAT A_rou = m_sound_speed_squared * 45.9678 * m_temperature;
    dFLOAT B_rou = m_sound_speed_squared * -(355.99 * m_temperature + m_alpha2 * 0.0782462);
    dFLOAT C_rou = m_sound_speed_squared * (932.192 * m_temperature - m_alpha2 * 0.0221696);
    dFLOAT D_rou = m_sound_speed_squared * -(816.529 * m_temperature + m_alpha2 * 0.628903) - m_sound_speed_bound * m_sound_speed_bound;

    dFLOAT Px = B_rou / A_rou;
    dFLOAT Qx = C_rou / A_rou;
    dFLOAT Rx = D_rou / A_rou;

    dFLOAT au = Qx - Px * Px / 3.0;
    dFLOAT bu = Rx - Px * Qx / 3.0 + 2.0 * Px * Px * Px / 27.0;

    dFLOAT term_abu = bu * bu / 4.0 + au * au * au / 27.0;

    dassert(term_abu >= 0.0);

    dFLOAT term_u = pow(term_abu, 0.5);

    dFLOAT term_AB1 = -0.5 * bu + term_u;
    dFLOAT term_AB2 = -0.5 * bu - term_u;
    
    dFLOAT term_A = copysignf(1.0, term_AB1) * pow(fabs(term_AB1), 1 / 3.0);
    dFLOAT term_B = copysignf(1.0, term_AB2) * pow(fabs(term_AB2), 1 / 3.0);
      
    dFLOAT root_rou = term_A + term_B - Px / 3.0;

    dFLOAT pr_eos_term1 = 1.0 - b0 * root_rou;
    dFLOAT pr_eos_term2 = 2.0 - pow(pr_eos_term1, 2.0);

    dFLOAT dpr_drour_term1 = m_temperature / pow(pr_eos_term1, 2.0);
    dFLOAT dpr_drour_term2 = -2.0 * m_alpha * (root_rou + b0 * root_rou * root_rou) / pow(pr_eos_term2, 2.0);

    speed_sound_exact_bound = sqrt(m_sound_speed_squared * (dpr_drour_term1 + dpr_drour_term2));
    pressure_exact_bound =  m_sound_speed_squared * root_rou * (m_temperature / pr_eos_term1 - m_alpha * root_rou / pr_eos_term2);
    m_max_density = root_rou;
  }

  sdFLOAT potential(sdFLOAT density)  {
    dFLOAT difference;
    dFLOAT bn  = b0 * density;

    if (density >= m_max_density) {
      difference = density - (pressure_exact_bound + (density - m_max_density) * speed_sound_exact_bound * speed_sound_exact_bound);
    } else {
      difference = density - m_sound_speed_squared * density * (m_temperature / (1. - bn) - density * m_alpha / (1. + (2. - bn) * bn));
    }

    if (difference < 0.)
      return 0.;

    return sqrt(2. * difference);
  }

  EOS_TYPE type() {
    return EOS_PENG_ROBINSON;
  }
};

class cEOS_VDW : public cEOS
{
  CONST static dFLOAT a0 = 1.125;
  CONST static dFLOAT b0 = 1. / 3.;

  dFLOAT m_critical_volume;
  dFLOAT m_sound_speed_squared;
  dFLOAT m_temperature;
  dFLOAT m_max_density;

public:

  cEOS_VDW( sEOS_PARAMS& params) :
    m_critical_volume(1.) {
    assert(params.temperature > 0.);

    m_temperature = params.temperature;
    m_sound_speed_squared = params.sound_speed_squared;
    
    m_max_density = 2.9;
  }

  sdFLOAT potential(sdFLOAT density)  {
    if (density >= m_max_density)
      density = m_max_density;

    dFLOAT bn  = b0 * density;
    dFLOAT difference = density - m_sound_speed_squared * density *
      (m_temperature / (1. - bn) - a0 * density);

    if (difference < 0.)
      return 0.;

    return sqrt(2. * difference);
  }

  EOS_TYPE type() {
    return EOS_VAN_DER_WAALS;
  }
};

class cEOS_SOAVE : public cEOS
{
  CONST static dFLOAT a0 = 1.4874221936691127516;
  CONST static dFLOAT b0 = 0.2530765865415994623;
    
  dFLOAT m_critical_volume;
  dFLOAT m_sound_speed_squared;
  dFLOAT m_accentric_factor;
  dFLOAT m_temperature;
  dFLOAT m_max_density;
  dFLOAT m_alpha;

public:

  cEOS_SOAVE(sEOS_PARAMS& params) :
    m_critical_volume(1.),
    m_accentric_factor(0.344) {
    dassert(params.temperature > 0.);

    m_temperature = params.temperature;
    m_sound_speed_squared = params.sound_speed_squared;
    
    dFLOAT  kappa = 0.37464 + (1.54226 - 0.26992 * m_accentric_factor) * m_accentric_factor;

    m_alpha = a0 * pow(1. + kappa * (1. - sqrt(m_temperature)), 2.);
    m_max_density = 3.5;
  }

  sdFLOAT potential(sdFLOAT density)  {
    if (density >= m_max_density)
      density = m_max_density;

    dFLOAT bn  = b0 * density;
    dFLOAT difference = density - m_sound_speed_squared * density *
      (m_temperature / (1. - bn) - density * m_alpha / (1. + bn));

    if (difference < 0.)
      return 0.;

    return sqrt(2. * difference);
  }

  EOS_TYPE type() {
    return EOS_SOAVE;
  }
};

class cEOS_RHO_SQUARE : public cEOS
{
public:

  cEOS_RHO_SQUARE( sEOS_PARAMS& params) { }

  sdFLOAT potential(sdFLOAT density)  {
    return density*density;
  }

  EOS_TYPE type() {
    return EOS_RHO_SQUARE;
  }
};


iEOS* iEOS::create( EOS_TYPE type,  sEOS_PARAMS& params)
{
    switch (type) {
    case EOS_IDEAL_GAS:
        return new cEOS_IDEAL_GAS(params);
    case EOS_SHAN_CHEN_93:
        return new cEOS_SC93(params);
    case EOS_SHAN_CHEN_94:
        return new cEOS_SC94(params);
    case EOS_PENG_ROBINSON:
        return new cEOS_PR(params);
    case EOS_VAN_DER_WAALS:
        return new cEOS_VDW(params);
    case EOS_SOAVE:
        return new cEOS_SOAVE(params);
    case EOS_MC:
        return new cEOS_MC(params);
    case EOS_RHO_SQUARE:
        return new cEOS_RHO_SQUARE(params);
    default:
        break;
    }

    return NULL;
}
