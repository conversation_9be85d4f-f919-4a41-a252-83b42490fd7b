/*
 * turb.h
 *
 *  Created on: Jan 13, 2016
 *      Author: chona
 */

#ifndef TURB_H_
#define TURB_H_

#include PHYSTYPES_H
#include TURB_SYNTH_SIM_H
#include "ckpt.h"

typedef struct sTURB_SYNTH_INFO {
  uINT32 m_checkpointer;
  BOOLEAN m_fromCheckpoint;
  std::vector<uINT32> m_nvel;
  std::vector<sdFLOAT *> m_v;
  std::vector<cTURB_SYNTH_SIM *> m_vturb_synth;

  VOID ckpt_turb_synth_info_state();
  VOID ckpt_turb_synth_info_state(sCKPT_BUFFER& pio_ckpt_buff);
  size_t ckpt_len();
  
  VOID read_ckpt_turb_synth_info_state();

  sTURB_SYNTH_INFO()
  {
    m_checkpointer = 1 << 30;
    m_fromCheckpoint = FALSE;
  }

} *TURB_SYNTH_INFO;

extern sTURB_SYNTH_INFO g_turb_info;

#endif /* TURB_H_ */
