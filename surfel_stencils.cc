#include "sim.h"
#include "shob_groups.h"
#include "surfel_stencils.h"
#include "surfel_vertices.h"
#include "conduction_solver_stencils.h"
// SS-CHECK: Since these are really specific to the solver, we should
// probably define corresponding functions in <solver>_stencils.cc
// to keep this file limited to only "common" operations. This would
// mean creating moving the shell conduction related stuff to
// conduction_solver_stencils.cc
#include "film_solver_stencils.h"
#include "particle_sim_info.h"
#include "film_comm.h"
#include "comm_groups.h"
#include PHYSICS_H

#define USE_FILM_STENCIL_CUTPOINTS
#define FILM_STENCIL_CUTPOINT_ANGLE 60.0  //degrees
#define DO_SECOND_ROUND_FO_GHOST_CREATION

std::vector<sSTENCIL_SIM_ERROR_DATA> g_stencil_sim_error_buffer_new;

INLINE bool sFILM_STENCIL_INIT_INFO_PREDICATE::operator()(sSURFEL_STENCIL_INIT_INFO const &a, sSURFEL_STENCIL_INIT_INFO const &b) const {
  sPARTICLE_VAR r[N_SPACE_DIMS];

  vsub(r, a.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_a = vdot(r, r);

  vsub(r, b.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_b = vdot(r, r);

  return dist_sqr_a < dist_sqr_b;
}

asINT32 sSURFEL_HALF_EDGE::get_edge_num(SURFEL surfel) const {
  const asINT32 n_edges = surfel->stencil()->n_edges();

  const asINT32 first_half_edge_index = surfel->stencil()->first_half_edge_index();

  asINT32 edge_num = -1;
  for (edge_num = 0; edge_num < n_edges; edge_num++) {
    SURFEL_HALF_EDGE temp_edge = surfel_half_edge(first_half_edge_index + edge_num);
    if (this == temp_edge)
      break;
  }

  return edge_num;
}

sSURFEL_HE_VERTEX_INFO::sSURFEL_HE_VERTEX_INFO (sSURFACE_SHOB* s) {
  _surfel = s;
  _first_vertex_index = s->stencil()->first_vertex_index;
  _edge_num = 0;
}

asINT32 sSURFEL_HE_VERTEX_INFO::edge_num_from_vertex_num (const asINT32 vertex_num) {
#ifdef CONDUCTION_STENCILS_REPLICATE_OLD_ORDERING
  // In the old implementation, the half edge compute for vertex 0 has
  // vertex 0 at its head.
  return (vertex_num + _surfel->stencil()->n_vertices - 1) % _surfel->stencil()->n_vertices;
#else
  /* Simplest to assume vertex_num and edge_num are identical.
   * Whether the vertex is the head or the tail can be implemented
   * in update_from_edge_num */
  return vertex_num;
#endif
}

VOID sSURFEL_HE_VERTEX_INFO::update_from_edge_num(const asINT32 edge_num) {
  _edge_num = edge_num;

  const asINT32 n_edges = _surfel->stencil()->n_vertices;

  /* First vertex index is edge 0's tail */
  const asINT32 head_vertex_num = _first_vertex_index + (edge_num + 1) % n_edges;
  const asINT32 tail_vertex_num = _first_vertex_index + edge_num;

  _head_vertex_index = vertex_local_index_from_global(head_vertex_num);
  _tail_vertex_index = vertex_local_index_from_global(tail_vertex_num);
}

SURFEL_HALF_EDGE sSURFEL_HE_VERTEX_INFO::get_ptr () const {
  return &(g_surfel_vertices_info.m_surfel_half_edges[_first_vertex_index + _edge_num]);
}

SIM_VERTEX sSURFEL_HE_VERTEX_INFO::head_vertex () const {
  cassert(g_surfel_vertices_info.m_surfel_vertices);
  return &(g_surfel_vertices_info.m_surfel_vertices[_head_vertex_index]);
}

SIM_VERTEX sSURFEL_HE_VERTEX_INFO::tail_vertex () const {
  cassert(g_surfel_vertices_info.m_surfel_vertices);
  return &(g_surfel_vertices_info.m_surfel_vertices[_tail_vertex_index]);
}

VOID sSURFEL_HALF_EDGE::get_tail_and_head_vertex(SIM_VERTEX &tail_vertex, SIM_VERTEX &head_vertex,
    SURFEL surfel, asINT32 edge_num) const {
  // Compute edge_num if not provided
  if (edge_num == -1) {
    edge_num = get_edge_num(surfel);
  }

  sSURFEL_HE_VERTEX_INFO edge(surfel);
  edge.update_from_edge_num(edge_num);

  head_vertex = edge.head_vertex();
  tail_vertex = edge.tail_vertex();
}

VOID sSURFEL_HALF_EDGE::get_centroid(dFLOAT (&edge_centroid)[3], sSURFEL* surfel,
    asINT32 edge_num) const {

  SIM_VERTEX tail_vertex;
  SIM_VERTEX head_vertex;
  get_tail_and_head_vertex(tail_vertex, head_vertex, surfel, edge_num);

  if (sim.is_2d()) {
    // For 2D cases, "edge" is really a point in space
    vcopy(edge_centroid, tail_vertex->coord);
  } else {
    vadd(edge_centroid, tail_vertex->coord, head_vertex->coord);
    vmul(edge_centroid, 0.5);
  }
}

VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::reset_last_added_tags()
{
  // All surfels which must be included in connectivity determination
  // are to be reset here
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      last_added_tags_map[surfel->id()] = -1;
    }
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      last_added_tags_map[surfel->id()] = -1;
    }
    DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
      last_added_tags_map[wsurfel_flow->id()] = -1;
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      last_added_tags_map[wsurfel_conduction->id()] = -1;
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      last_added_tags_map[contact_surfel->id()] = -1;
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      last_added_tags_map[isurfel_pair->m_interior_surfel->id()] = -1;
      last_added_tags_map[isurfel_pair->m_exterior_surfel->id()] = -1;
    }
    DO_GHOST_SURFELS_OF_SCALE(quantum, scale) {
      last_added_tags_map[quantum.m_surfel->id()] = -1;
    }
#ifndef CONDUCTION_DISABLE_MULTIPROC_SHELL_CONDUCTION
    msg_internal_error("Ghost surfels have not been created for shell conduction stencil");
#endif
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL fo_surfel = quantum.m_surfel;
        last_added_tags_map[fo_surfel->id()] = -1;
      }
    }
  }
}

VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::add_surfel(SURFEL surfel, SURFEL_HALF_EDGE shared_half_edge, asINT32 neighbor_order)
{
if (shared_half_edge == NULL) {
    sSURFEL_STENCIL_INIT_INFO new_record(surfel, neighbor_order);
    v.push_back(new_record);
  } else {
    sSURFEL_STENCIL_INIT_INFO new_record(surfel, shared_half_edge, neighbor_order);
    v.push_back(new_record);
  }
  tag_stencil_addition(surfel);
}

VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::add_surfel(SURFEL surfel, asINT32 edges_to_center)
{
  //SS-CHECK edge_count
  sSURFEL_STENCIL_INIT_INFO new_record(surfel, edges_to_center);
  v.push_back(new_record);
  tag_stencil_addition(surfel);
}

BOOLEAN sSURFEL_STENCIL_INIT_INFO_VECTOR::is_candidate_surfel_within_stencil_radius(SURFEL candidate_surfel) const
{
  // CONDUCTION-TODO: This distance checking needs to be improved to account for surfaces with curvature
  // and especially ones with large curvatures.
  dFLOAT r[3];
  vsub(r, candidate_surfel->centroid, home_surfel()->centroid);
  sdFLOAT dist_sqrd = vdot(r, r);
  sdFLOAT max_radius_sqrd = max_radius() * max_radius();
  return dist_sqrd < max_radius_sqrd;
}

BOOLEAN sSURFEL_STENCIL_INIT_INFO_VECTOR::is_candidate_surfel_already_added(SURFEL candidate_surfel) const {
  return last_added_tags_map.at(candidate_surfel->id()) == home_surfel()->id();
}

BOOLEAN sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR::build_stencil_for_surfel(SURFEL surfel)
{ 
  // Any other criteria?
  return even_odd_filter(surfel);
}

STP_DGEOM_VARIABLE sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR::max_radius() const
{
  cassert(v.size() > 0); /* surfel vector should consist of at least the home surfel */
  return g_particle_sim_info.max_radius_of_film_stencil; 
}

BOOLEAN sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR::even_odd_filter(SURFEL surfel) const {
  return !surfel->is_even();
}

//SS-CHECK
BOOLEAN sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR::should_surfel_be_added_to_stencil(SURFEL neighbor_surfel,
    const asINT32 neighbor_order) const {
  SURFEL root_surfel = this->home_surfel();

#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
  LOG_MSG_IF(root_surfel->id() == 3087 && neighbor_surfel->id() == 3059, "SURFEL_STENCILS", LOG_TS).
    printf("%d %d %d %d %d %d %d %d",
        root_surfel->id() != neighbor_surfel->id() ? 1 : 0,
        !neighbor_surfel->is_inlet_or_outlet() ? 1 : 0,
        even_odd_filter(neighbor_surfel) ? 1 : 0,
        neighbor_surfel->is_wall() ? 1 : 0,
        root_surfel->is_backside() == neighbor_surfel->is_backside() ? 1 : 0,
        root_surfel->m_face_index != neighbor_surfel->m_face_index ? 1 : 0,
        !is_candidate_surfel_already_added(neighbor_surfel) ? 1 : 0, 
        is_candidate_surfel_within_stencil_radius(neighbor_surfel) ? 1 : 0);
#endif

  return root_surfel->id() != neighbor_surfel->id()
    && !neighbor_surfel->is_inlet_or_outlet()
    && even_odd_filter(neighbor_surfel)
    && neighbor_surfel->is_wall()
    && (root_surfel->is_backside() == neighbor_surfel->is_backside()
        || root_surfel->m_face_index != neighbor_surfel->m_face_index)
    && !is_candidate_surfel_already_added(neighbor_surfel)
    && is_candidate_surfel_within_stencil_radius(neighbor_surfel);
}

SURFEL_STENCIL_VECTOR sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR::get_home_surfel_stencil_vector_ptr() const
{
  return (SURFEL_STENCIL_VECTOR)(home_surfel()->p_data()->nearby_surfels);
}

VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::tag_stencil_addition(SURFEL surfel)
{
  auto got = last_added_tags_map.find(surfel->id());
  cassert(got != last_added_tags_map.end());
  SURFEL home_surfel = v[0].surfel;
  last_added_tags_map[surfel->id()] = home_surfel->id();
}

/*******************************************************************************
 * @brief Default finalize for surfel stencils (copy entire vector)
 *
 * @details
 *  As a default implementation for finalize(), this simply creates
 *  a copy of the entire stencil vector on the surfel. Any special solver
 *  requirement can be implemented by constexpr IFs (simple, ideally one-line
 *  conditions) or template specialization (more complex ones).
 ******************************************************************************/
template <typename SURFEL_TYPE, typename STENCIL_VECTOR_TYPE>
VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::finalize()
{
  STENCIL_VECTOR_TYPE nearby_surfels = (STENCIL_VECTOR_TYPE)get_home_surfel_stencil_vector_ptr();

  for (auto const& s_init_info : v) {
    SURFEL stencil_surfel = s_init_info.surfel;

    SURFEL_TYPE surfel_to_add = SURFEL_TYPE(stencil_surfel);
    // if(stencil_surfel->is_even()) {
    //   surfel_to_add = stencil_surfel->clone_surfel();
    // }

    // if(surfel_to_add == NULL)
    //   continue;

    nearby_surfels->push_back(surfel_to_add);
  }
}

template<>
VOID sSURFEL_STENCIL_INIT_INFO_VECTOR::finalize<sSTENCIL_SURFEL, FILM_STENCIL_SURFEL_VECTOR>()
{
  SURFEL surfel = home_surfel();
  surfel->p_data()->nearby_surfels->resize(0); //discard any old stencils
  surfel->p_data()->nearby_areas->resize(0);
  
  //if (!shob_is_sampling_surfel(surfel))  //TO_BE_DONE
  SCALE min_scale = FINEST_SCALE, max_scale = COARSEST_SCALE;

  FILM_STENCIL_SURFEL_VECTOR nearby_surfels = (FILM_STENCIL_SURFEL_VECTOR)get_home_surfel_stencil_vector_ptr();

  //Sort the init info records by distance to centroid so that closest surfels are first.
  std::sort(v.begin(), v.end(), sFILM_STENCIL_INIT_INFO_PREDICATE(surfel));

  //Find the max of sharp_edge_count_to_center for any of the surfels available for this surfel's stencil.
  asINT32 max_edge_transists_to_center = 0;
  //ccDOTIMES(index, v.size()) {
  for (auto const& init_info : v) {
    //sFILM_STENCIL_INIT_INFO &init_info = v[index];
    max_edge_transists_to_center = MAX(max_edge_transists_to_center, init_info.sharp_edge_count_to_center);

    SURFEL stencil_surfel = init_info.surfel;
    *(stencil_surfel->p_data()->s.was_area_accumulated()) = 0;

#if 1
    SURFEL clone_surfel = stencil_surfel->clone_surfel();

    if (clone_surfel != NULL)
      *(clone_surfel->p_data()->s.was_area_accumulated()) = 0;
#endif
  }

  //Now make several passes through the available surfels, each pass accepting a greater number
  //of sharp_edge_count_to_centers, and add the closest surfels first (they've previously
  //been sorted by distance from center).  Stop when cummulative_area grows to a sufficent amount
  sPARTICLE_VAR cummulative_area = 0;
  for(asINT32 number_of_compromises = 0;
      number_of_compromises < max_edge_transists_to_center + 1;
      number_of_compromises++){

    sPARTICLE_VAR min_allowed_area =  number_of_compromises!=0  ?  0.5 * max_radius() * max_radius() * M_PI : 1e6; //add everything in the stencil that is smoothly connected and then up to a certain area if not smoothly connected

    //for(asINT32 index = 0 ; (index < v.size()) && (cummulative_area < min_allowed_area) ; index++) {
    for (auto const& init_info : v ) {
      //sFILM_STENCIL_INIT_INFO &init_info = v[index];
      if (cummulative_area >= min_allowed_area)
        break;
      
      SURFEL stencil_surfel = init_info.surfel;
      //        if (init_info.sharp_edge_count_to_center <= number_of_compromises)
      if (init_info.sharp_edge_count_to_center <= number_of_compromises && *(stencil_surfel->p_data()->s.was_area_accumulated()) == 0) {
        
        SURFEL surfel_to_add = stencil_surfel;
        if(stencil_surfel->is_even()) {
          surfel_to_add = stencil_surfel->clone_surfel();
        }

        if(surfel_to_add == NULL)
          continue;
         
        //if the stencil surfel is not even
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
        nearby_surfels->push_back(surfel_to_add);
#else
	sSTENCIL_SURFEL stencil_surfel_to_add = sSTENCIL_SURFEL(surfel_to_add);
        nearby_surfels->push_back(stencil_surfel_to_add);
#ifdef DEBUG_NEW_SURFEL_STENCIL
    if (surfel_to_add->id()==1447){
      msg_print("T%ld S %d pushing back s1447",g_timescale.m_time, surfel->id());
    }
#endif
#endif
        min_scale = std::min(min_scale, (SCALE)surfel_to_add->scale());
        max_scale = std::max(max_scale, (SCALE)surfel_to_add->scale());
        
        asINT32 log2_voxel_size = (sim.num_dims - 1) * scale_to_log2_voxel_size(surfel_to_add->scale());
        sPARTICLE_VAR surfel_area = surfel_to_add->area * (sPARTICLE_VAR)( 1 << log2_voxel_size);
        cummulative_area += surfel_area;
        surfel->p_data()->nearby_areas->push_back(cummulative_area);
        
        *(surfel_to_add->p_data()->s.was_area_accumulated()) = 1;
       

# if 0          
          SURFEL clone_surfel = stencil_surfel->clone_surfel();
          
          if ( clone_surfel != NULL) {
            //if (*(clone_surfel->p_data()->s.was_added_to_stencil()) != surfel->id()) { //surfels that have already been used have this flag set to -1
            //*(clone_surfel->p_data()->s.was_added_to_stencil()) = surfel->id();
            
            surfel->p_data()->nearby_surfels->push_back(clone_surfel);
            min_scale = std::min(min_scale, (SCALE)clone_surfel->scale());
            max_scale = std::max(max_scale, (SCALE)clone_surfel->scale());
            
            surfel->p_data()->nearby_areas->push_back(cummulative_area);
            
            *(clone_surfel->p_data()->s.was_area_accumulated()) = 1;
            //}
          }
#endif
      }
      if (cummulative_area >= min_allowed_area)
        break;
    }
  }

  //ccDOTIMES(index, v.size()) {
  for (auto const& init_info : v) {  
    //sFILM_STENCIL_INIT_INFO &init_info = v[index];

    SURFEL stencil_surfel = init_info.surfel;
    *(stencil_surfel->p_data()->s.was_area_accumulated()) = 0;

#if 1
    SURFEL clone_surfel = stencil_surfel->clone_surfel();

    if (clone_surfel != NULL)
      *(clone_surfel->p_data()->s.was_area_accumulated()) = 0;
#endif
  }

  surfel->p_data()->scale_range[0] = min_scale;
  surfel->p_data()->scale_range[1] = max_scale;
  //}

}


INLINE VOID initialize_surfel_stencil_vector(SURFEL surfel, SURFEL_STENCIL_INIT_INFO_VECTOR stencil_vector)
{
  stencil_vector->v.clear();
  stencil_vector->add_surfel(surfel, (SURFEL_HALF_EDGE)NULL, 0);
  stencil_vector->v[0].sharp_edge_count_to_center = 0;  
}

INLINE BOOLEAN surfel_is_stencil_cutpoint(SURFEL home_surfel,SURFEL surfel_to_check) {
#ifdef USE_FILM_STENCIL_CUTPOINTS
  //Check if the angle between two surfels is extreme.
  sPARTICLE_VAR cos_angle_between_surfels;
  cos_angle_between_surfels = vdot(home_surfel->normal, surfel_to_check->normal);
  BOOLEAN is_cutpoint = cos_angle_between_surfels < cos(M_PI/180.0 * FILM_STENCIL_CUTPOINT_ANGLE);
  return is_cutpoint;
#else
  return FALSE;
#endif
}

template<typename STENCIL_VECTOR_TYPE>
VOID compile_nearby_surfels(SURFEL_STENCIL_INIT_INFO_VECTOR stencil_vector)
{
  cassert(stencil_vector->v.size() != 0);

  const STP_DGEOM_VARIABLE max_radius = stencil_vector->max_radius();
  SURFEL home_surfel = stencil_vector->home_surfel();

  /* For each surfel in the vector, starting from the beginning iterate over its neighbors...
   *   if the neighbor is already in the list, skip it
   *   otherwise, if its centroid is within max_radius of the centroid of the home surfel
   *     then add the surfel to the vector of surfels.
   *  continue until the end of the vector is reached which will eventually happen when no new surfels can be added
   */
  for (asINT32 i = 0; i < stencil_vector->v.size(); i++) { //size() is expected to increase as new surfels are added

    SURFEL surfel = stencil_vector->v[i].surfel; //This cast should be legal because no sampling surfels should appear in the stencil
    asINT32 edges_to_center_from_parent = stencil_vector->v[i].sharp_edge_count_to_center;
    if (!stencil_vector->build_stencil_for_surfel(surfel))
      continue;

    /* Iterate over all the edges of this surfel.
     * Since compute_half_edge_info already computed the neighbor surfel that
     * is associated with every edge on this surfel, we simply use that information
     * and decide whether or not to add the neighbor surfels to the stencil */
    sSURFEL_HE_VERTEX_INFO edge_info(surfel);
    const asINT32 n_edges = surfel->stencil()->n_edges();

    ccDOTIMES(edge_num, n_edges) {
      edge_info.update_from_edge_num(edge_num);
      SURFEL_HALF_EDGE edge = edge_info.get_ptr();
      SURFEL neighbor_surfel = edge->neighbor_regular_surfel();
      /** Some solvers might require wrap-around connectivity at
       *  boundaries of double-sided surfaces. If this half-edge
       *  has a NULL neighbor_surfel, it implies that edge is at
       *  the boundary. */
      // CONDUCTION-TODO: Convert should_wrap_around_at_interface_boundaries as a constexpr
      if (neighbor_surfel == surfel->m_opposite_surfel
          && !stencil_vector->should_wrap_around_at_interface_boundaries()) {
        neighbor_surfel = (SURFEL)NULL;
      }

      if (neighbor_surfel != NULL) {
        if (stencil_vector->should_surfel_be_added_to_stencil(neighbor_surfel,edges_to_center_from_parent+1)) {
          if constexpr (std::is_same<STENCIL_VECTOR_TYPE, FILM_STENCIL_SURFEL_VECTOR>::value) {
              asINT32 edges_from_neighbor_to_center = edges_to_center_from_parent + (surfel_is_stencil_cutpoint(surfel, neighbor_surfel) ? 1 : 0);
              stencil_vector->add_surfel(neighbor_surfel, edges_from_neighbor_to_center);
          }
        }
      }
      // Dont allow stencil to grow beyond max allowable size
      if (stencil_vector->v.size() >= (stencil_vector->max_surfels_allowed()-1)) { break; }
    }
  } // for i

#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
  LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("%d -> %d", home_surfel->id(), stencil_vector->v.size());
  // for (asINT32 i = 0; i < stencil_vector->v.size(); i++) {
  //   SURFEL surfel = stencil_vector->v[i].surfel;
  //   LOG_MSG_IF(home_surfel->id() == 1976, "SURFEL_STENCILS", LOG_TS).printf("%d[%d] -> %d",
  //       home_surfel->id(), i, surfel->id());
  // }
#endif

#ifndef CONDUCTION_DISABLE_STENCIL_SIM_ERRORS
  maybe_throw_surfel_stencil_errors();
#endif
}

template <typename SURFEL_TYPE, typename STENCIL_VECTOR_TYPE>
VOID populate_surfel_stencil_vector(SURFEL surfel, SURFEL_STENCIL_INIT_INFO_VECTOR stencil_vector)
{
  if (stencil_vector->build_stencil_for_surfel(surfel)) {
    initialize_surfel_stencil_vector(surfel, stencil_vector);
    compile_nearby_surfels<STENCIL_VECTOR_TYPE>(stencil_vector);
    stencil_vector->finalize<SURFEL_TYPE, STENCIL_VECTOR_TYPE>();
  }
}

VOID populate_all_surfel_stencil_vectors(const asINT32 build_stencils_for_solvers_mask)
{
  sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR film_solver_stencil_vector; //stencil_init_info

  const BOOLEAN build_particle_solver_stencils =
    (build_stencils_for_solvers_mask & (1 << PARTICLE_SOLVER)) != 0;

  DO_SCALES_FINE_TO_COARSE(scale) {
#ifndef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
    if (build_particle_solver_stencils) {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        populate_surfel_stencil_vector<sSTENCIL_SURFEL, FILM_STENCIL_SURFEL_VECTOR>(surfel, &film_solver_stencil_vector);
      }
      DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
        populate_surfel_stencil_vector<sSTENCIL_SURFEL, FILM_STENCIL_SURFEL_VECTOR>(isurfel_pair->m_interior_surfel, &film_solver_stencil_vector);
        populate_surfel_stencil_vector<sSTENCIL_SURFEL, FILM_STENCIL_SURFEL_VECTOR>(isurfel_pair->m_exterior_surfel, &film_solver_stencil_vector);
      }
      DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
          SURFEL fo_surfel = quantum->m_surfel;
          populate_surfel_stencil_vector<sSTENCIL_SURFEL, FILM_STENCIL_SURFEL_VECTOR>(fo_surfel, &film_solver_stencil_vector);
        }
      }
    }
#endif
  }
}

static VOID assign_home_sp_for_ghost_surfels()
{
  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, 0) {
    DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      asINT32 sp = group->m_source_sp.rank();
      for (auto& quantum: group->quantums()) {
        assert(quantum.m_surfel->m_home_sp == sp);
        //quantum->m_surfel->m_home_sp = sp;
      }
    }
  }
}

static VOID build_connectivity_map(const asINT32 build_stencils_for_solvers_mask)
{
  // stencil_solver_mask indicates which solvers to build stencil for
  g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_new();
  g_surfel_vertices_info.read_surfel_vertices();

  // Shell stencils use a separate stencil creation process, so need to check if the
  // CONDUCTION_SOLVER bit is set and use the appropriate stencil creation process
  const BOOLEAN build_shell_solver_stencils =
    (build_stencils_for_solvers_mask & (1 << CONDUCTION_SOLVER)) != 0;
  if (build_shell_solver_stencils) {
    g_shell_stencils_info = xnew cSHELL_STENCIL_INFO();
    g_shell_stencils_info->build_shell_stencils();
    delete g_shell_stencils_info;
    g_shell_stencils_info = NULL;
  }
  else {
    g_surfel_vertices_info.create_surfel_half_edges();
    populate_all_surfel_stencil_vectors(build_stencils_for_solvers_mask);
  }
}

VOID compute_surfel_stencils_and_create_stencil_only_ghosts()
{
#ifdef CONDUCTION_ENFORCE_SURFEL_STENCILS_INITIALIZE_DUE_TO_HARDCODE_IN_CP
  // In CP hacked setup, to make life easy, we are assuming shell conduction
  // and particle model cannot be ON simulataneously.
  if (!sim.is_shell_conduction_model) {
    // Read and throw away vertices once since CP is hacked to think
    // shell conduction is ON (even if it isn't) and broadcasts vertices once.
    g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_new();
    g_surfel_vertices_info.read_surfel_vertices();
    delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
    g_surfel_vertices_info.delete_vertex_surfels_arrays();

#ifndef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
    if (sim.is_particle_model) {

      const asINT32 particle_solver_mask = 1 << PARTICLE_SOLVER;

      // Read and throw away vertices once more since CP has an additional
      // call for old stencil construction (added by Long for r28910 stencils
      // bug fix)
      g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_new();
      g_surfel_vertices_info.read_surfel_vertices();
      delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
      g_surfel_vertices_info.delete_vertex_surfels_arrays();

      g_stencil_sim_error_buffer_new.reserve(MAX_STENCIL_ERROR_LIMIT);
      assign_home_sp_for_ghost_surfels();

      if (sim.is_film_solver) {

        //Build film solver stencils used for film scalar field accumulation.
        build_connectivity_map(particle_solver_mask);

        create_surfel_film_send_and_receive_groups();
        //The surfels near SP boundaries have their stencils truncated
        //so some extra ghost surfels that penetrate further into the remote
        //sp need to be generated. Only the film solver uses these new ghost surfels.
        retrieve_ghost_side_interface_stencils(0);
        retrieve_dynamics_side_interface_stencils(0);

        //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
        g_surfel_vertices_info.m_surfel_vertex_indices.clear();
        delete[] g_surfel_vertices_info.m_surfel_half_edges;
        g_surfel_vertices_info.delete_vertex_surfels_arrays();
        delete[] g_surfel_vertices_info.m_surfel_vertices;
        g_surfel_vertices_info.m_surfel_vertices = nullptr;
        build_connectivity_map(particle_solver_mask);

#ifdef DO_SECOND_ROUND_FO_GHOST_CREATION
        //Do another round of reconciling stencils that cross domain boundaries.
        retrieve_ghost_side_interface_stencils(1);
        //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
        g_surfel_vertices_info.m_surfel_vertex_indices.clear();
        delete[] g_surfel_vertices_info.m_surfel_half_edges;
        g_surfel_vertices_info.delete_vertex_surfels_arrays();
        delete[] g_surfel_vertices_info.m_surfel_vertices;
        g_surfel_vertices_info.m_surfel_vertices = nullptr;
        build_connectivity_map(particle_solver_mask);

        retrieve_dynamics_side_interface_stencils(1);

        //Delete the old connectivity map and build a new one that including the newly created film only ghost surfels.
        g_surfel_vertices_info.m_surfel_vertex_indices.clear();
        delete[] g_surfel_vertices_info.m_surfel_half_edges;
        g_surfel_vertices_info.delete_vertex_surfels_arrays();
        delete[] g_surfel_vertices_info.m_surfel_vertices;
        g_surfel_vertices_info.m_surfel_vertices = nullptr;
        build_connectivity_map(particle_solver_mask);
        g_surfel_vertices_info.m_surfel_vertex_global_indices.clear(); //only need the local id's at this point now that the above has remapped all IDs.

        delete g_surfel_vertices_info.m_is_surfel_vertex_used;
#endif

        g_surfel_vertices_info.delete_vertex_surfels_arrays();

      } else {
        create_surfel_film_send_and_receive_groups();
        // No need to build connectivity for only particle solver
        const asINT32 no_solver_mask = 0;
        build_connectivity_map(no_solver_mask);
    
        g_surfel_vertices_info.delete_vertex_surfels_arrays();
      }
    }
#endif
  } else {
    if (sim.is_particle_model)
      msg_internal_error("Shell conduction and particle model stencils not implemented in hacked setup");

    const asINT32 shell_conduction_solver_mask = 1 << CONDUCTION_SOLVER;
    build_connectivity_map(shell_conduction_solver_mask);
    g_surfel_vertices_info.delete_vertex_surfels_arrays();
  }
#else
  cassert(sim.is_shell_conduction_model || sim.is_particle_model);

  g_stencil_sim_error_buffer_new.reserve(MAX_STENCIL_ERROR_LIMIT);
  assign_home_sp_for_ghost_surfels();

  // If film solver is enabled, we need multiple passes. First three passes
  // are for film solver only. The last pass is for shell conduction or
  // particle model (without film solver).
  if (sim.is_film_solver) {

    const asINT32 particle_solver_mask = 1 << PARTICLE_SOLVER;

    //Build film solver stencils used for film scalar field accumulation.
    build_connectivity_map(particle_solver_mask);

    create_surfel_film_send_and_receive_groups();
    //The surfels near SP boundaries have their stencils truncated
    //so some extra ghost surfels that penetrate further into the remote
    //sp need to be generated. Only the film solver uses these new ghost surfels.
    retrieve_ghost_side_interface_stencils(0);
    retrieve_dynamics_side_interface_stencils(0);

    //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
    g_surfel_vertices_info.m_surfel_vertex_indices.clear();
    delete[] g_surfel_vertices_info.m_surfel_half_edges;
    g_surfel_vertices_info.delete_vertex_surfels_arrays();
    delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
    build_connectivity_map(particle_solver_mask);

#ifdef DO_SECOND_ROUND_FO_GHOST_CREATION
    //Do another round of reconciling stencils that cross domain boundaries.
    retrieve_ghost_side_interface_stencils(1);
    //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
    g_surfel_vertices_info.m_surfel_vertex_indices.clear();
    delete[] g_surfel_vertices_info.m_surfel_half_edges;
    g_surfel_vertices_info.delete_vertex_surfels_arrays();
    delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
    build_connectivity_map(particle_solver_mask);

    retrieve_dynamics_side_interface_stencils(1);

    //Delete the old connectivity map and build a new one that including the newly created film only ghost surfels.
    g_surfel_vertices_info.m_surfel_vertex_indices.clear();
    delete[] g_surfel_vertices_info.m_surfel_half_edges;
    g_surfel_vertices_info.delete_vertex_surfels_arrays();
    delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
#endif
  }

  const asINT32 stencils_for_solvers_mask = 0;
  stencils_for_solvers_mask |= sim.is_particle_model && sim.is_film_solver ? (1 << PARTICLE_SOLVER) : 0;
  stencils_for_solvers_mask |= sim.is_shell_conduction_model ? (1 << CONDUCTION_SOLVER) : 0;

  build_connectivity_map(stencils_for_solvers_mask);

  g_surfel_vertices_info.m_surfel_vertex_global_indices.clear(); //only need the local id's at this point now that the above has remapped all IDs.
  delete g_surfel_vertices_info.m_is_surfel_vertex_used;
  g_surfel_vertices_info.delete_vertex_surfels_arrays();

#endif

//  if (sim.is_particle_model)
//    compute_off_plane_tolerances();
}


VOID surfel_stencils_initialize()
{
  compute_surfel_stencils_and_create_stencil_only_ghosts();
  
#ifdef CONDUCTION_DISABLE_MULTIPROC_SHELL_CONDUCTION
#ifndef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  if (!sim.is_shell_conduction_model)
    init_surfel_film_send_and_recv_groups();
#endif
#else
    init_surfel_stencils_send_and_recv_groups(); //SS-TODO
#endif
}
