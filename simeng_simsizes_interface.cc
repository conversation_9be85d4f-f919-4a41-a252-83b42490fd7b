/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 


#include "export_simsizes.h"
#include "surfel.h"
#include "ublk.h"

#include "parse_shob_descs_factory.h"
#include SIMSIZES_SHARED_H

/*=================================================================================================
 * @fcn get_temperature_solver_type
 * Logic roughly mimics sSIM_INFO::set_T_solver_attributes()
 *================================================================================================*/
static T_SOLVER_TYPE get_temperature_solver_type(const SIM_SIZES::SIM_OPTIONS* opts) {

  static_assert(T_SOLVER_TYPE::N_T_SOLVER_TYPES == 6,
		"If you add a new T_solver, do not forget to make changes here.");
    
  T_SOLVER_TYPE t_solver_type = T_SOLVER_TYPE::INVALID;

  if (opts->is_high_subsonic_mach_regime) {
#if BUILD_6X_SOLVER    
    if (opts->use_lb_energy_solver) {
      t_solver_type = T_SOLVER_TYPE::LB_ENERGY;
    } else if (opts->use_hs_lb_entropy_solver) {
      t_solver_type = T_SOLVER_TYPE::LB_ENTROPY;
    } else
#endif
    {
      t_solver_type = T_SOLVER_TYPE::PDE_ENTROPY;
    }
  } else if (opts->is_heat_transfer) {
    if (opts->is_momentum_freeze) {
      t_solver_type = T_SOLVER_TYPE::LB_TEMPERATURE;
    } else if (opts->is_thermal_accel_active){
      //Switches from LB to PDE but is ultimately PDE
      t_solver_type = T_SOLVER_TYPE::PDE_TEMPERATURE;
    } else {
      t_solver_type = T_SOLVER_TYPE::LB_TEMPERATURE;
    }
  }
  return t_solver_type;
}

inline namespace SIMULATOR_NAMESPACE
{
  using SIM_SIZES::sSHOB_SIZES;

  /*=================================================================================================
   * @fcn SIMENG_SIZES_INTERFACE_IMPL
   *================================================================================================*/

  SIMENG_SIZES_INTERFACE_IMPL::SIMENG_SIZES_INTERFACE_IMPL(const SIM_SIZES::SIM_OPTIONS& opts) : m_sim_opts(&opts) {
    initialize_surfel_data_offset_table<sSURFEL>(DATA_OFFSET_TABLE_OPTS(opts));
    initialize_ublk_data_offset_table<sUBLK>(DATA_OFFSET_TABLE_OPTS(opts));
    set_hs_and_ts_solver_types(const_cast<SIM_SIZES::SIM_OPTIONS&>(opts));
    //save_surfel_table_to_file(TRUE);
    //save_ublk_table_to_file(TRUE);
  }

  SIMENG_SIZES_INTERFACE_IMPL::~SIMENG_SIZES_INTERFACE_IMPL() {
    // TODO Auto-generated destructor stub
  }

  size_t SIMENG_SIZES_INTERFACE_IMPL::size_of_sSURFEL() const {
    return sizeof(sSURFEL);
  }

  size_t SIMENG_SIZES_INTERFACE_IMPL::size_of_sUBLK() const {
    return sizeof(sUBLK);
  }

  /*=================================================================================================
   * @fcn append_t_send_field_size
   *================================================================================================*/
  static
  VOID append_t_send_field_size(size_t& send_field_size,
                                const SIM_SIZES::SIM_OPTIONS* opts,
                                SIM_SIZES::SHOB_TYPE shob_type){

    static_assert(T_SOLVER_TYPE::N_T_SOLVER_TYPES == 6,
		  "If you add a new T_solver, do not forget to make changes here.");
    
    size_t sizeof_t_scalar_send_field = 0;
    size_t sizeof_t_pde_send_field    = 0;
    size_t sizeof_t_entropy_field     = 0;
#if BUILD_6X_SOLVER
    size_t sizeof_t_lb_entropy_field  = 0;
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    size_t sizeof_t_lb_energy_field  = 0;
#endif

    if ( shob_type == SIM_SIZES::SHOB_TYPE::UBLK ) {
      sizeof_t_scalar_send_field = sizeof(SIMULATOR_NAMESPACE::tUBLK_T_SCALAR_SEND_FIELD<8>);
      sizeof_t_pde_send_field    = sizeof(SIMULATOR_NAMESPACE::tUBLK_T_PDE_SEND_FIELD<8>);
      sizeof_t_entropy_field     = sizeof(SIMULATOR_NAMESPACE::tUBLK_ENTROPY_SEND_FIELD<8>);
#if BUILD_6X_SOLVER
      sizeof_t_lb_entropy_field  = sizeof(SIMULATOR_NAMESPACE::tUBLK_LB_ENTROPY_SEND_FIELD<8>);
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      sizeof_t_lb_energy_field  = sizeof(SIMULATOR_NAMESPACE::tUBLK_LB_ENERGY_SEND_FIELD<8>);
#endif
    } else if ( shob_type == SIM_SIZES::SHOB_TYPE::SURFEL ) {
      sizeof_t_scalar_send_field = sizeof(SIMULATOR_NAMESPACE::sSURFEL_T_SCALAR_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::sSURFEL_V2S_T_SCALAR_SEND_FIELD);
      sizeof_t_pde_send_field    = sizeof(SIMULATOR_NAMESPACE::SURFEL_T_PDE_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::SURFEL_V2S_T_PDE_SEND_FIELD);
      sizeof_t_entropy_field     = sizeof(SIMULATOR_NAMESPACE::SURFEL_ENTROPY_SEND_FIELD) +
	                           sizeof(SIMULATOR_NAMESPACE::SURFEL_V2S_T_PDE_SEND_FIELD);
#if BUILD_6X_SOLVER
      sizeof_t_lb_entropy_field  = sizeof(SIMULATOR_NAMESPACE::sSURFEL_LB_ENTROPY_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::sSURFEL_V2S_LB_ENTROPY_SEND_FIELD);
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      sizeof_t_lb_energy_field   = sizeof(SIMULATOR_NAMESPACE::sSURFEL_LB_ENERGY_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::sSURFEL_V2S_LB_ENERGY_SEND_FIELD);
#endif
    }

    T_SOLVER_TYPE t_solver_type = get_temperature_solver_type(opts);
    if (t_solver_type == T_SOLVER_TYPE::PDE_ENTROPY) {
      send_field_size += sizeof_t_entropy_field;
    }
    else if (t_solver_type == T_SOLVER_TYPE::LB_TEMPERATURE) {
      send_field_size += sizeof_t_scalar_send_field;
    } else if (t_solver_type == T_SOLVER_TYPE::LB_ENTROPY) {
#if BUILD_6X_SOLVER
      send_field_size += sizeof_t_lb_entropy_field;
#else
      msg_error("LB_ENTROPY solver active for non 6X Solver build");
#endif
    } else if (t_solver_type == T_SOLVER_TYPE::LB_ENERGY) {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      send_field_size += sizeof_t_lb_energy_field;
#else
      msg_error("LB_ENERGY solver not active for 5X or 5G Solver build");
#endif
    } else if (t_solver_type == T_SOLVER_TYPE::PDE_TEMPERATURE) {
      send_field_size += std::max(sizeof_t_pde_send_field,sizeof_t_scalar_send_field);
    }

  }
  
  /*=================================================================================================
   * @fcn append_uds_send_field_size
   *================================================================================================*/
  static
  VOID append_uds_send_field_size(size_t& send_field_size,
                                const SIM_SIZES::SIM_OPTIONS* opts,
                                SIM_SIZES::SHOB_TYPE shob_type){
    
    size_t sizeof_uds_send_field = 0;
    size_t sizeof_uds_pde_send_field    = 0;

    if ( shob_type == SIM_SIZES::SHOB_TYPE::UBLK ) {
      sizeof_uds_send_field = sizeof(SIMULATOR_NAMESPACE::tUBLK_UDS_SEND_FIELD<8>);
      sizeof_uds_pde_send_field    = sizeof(SIMULATOR_NAMESPACE::tUBLK_UDS_PDE_SEND_FIELD<8>);
    } else if ( shob_type == SIM_SIZES::SHOB_TYPE::SURFEL ) {
      sizeof_uds_send_field = sizeof(SIMULATOR_NAMESPACE::sSURFEL_UDS_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::sSURFEL_V2S_UDS_SEND_FIELD);
      sizeof_uds_pde_send_field    = sizeof(SIMULATOR_NAMESPACE::sSURFEL_UDS_PDE_SEND_FIELD) +
                                   sizeof(SIMULATOR_NAMESPACE::sSURFEL_V2S_UDS_PDE_SEND_FIELD);
    }

    if (opts->use_uds_lb_solver)
      send_field_size += opts->n_user_defined_scalars * sizeof_uds_send_field;
    else
      send_field_size += opts->n_user_defined_scalars * sizeof_uds_pde_send_field;
  }

  /*=================================================================================================
   * @fcn fill_solver_vars_generic_unsplit_neighbors
   *================================================================================================*/
  size_t SIMENG_SIZES_INTERFACE_IMPL::size_of_nearblk_send_field() const {

    size_t send_field_size = size_of_farblk_send_field();

    if (m_sim_opts->is_lb_model){
      send_field_size += sizeof(tNEARBLK_LB_SEND_FIELD<8>);
    }

    if (m_sim_opts->is_turb_model){
      send_field_size += sizeof(tNEARBLK_TURB_SEND_FIELD<8>);
    }

    return send_field_size;
  }

  /*=================================================================================================
   * @fcn size_of_farblk_send_field
   *================================================================================================*/
  size_t SIMENG_SIZES_INTERFACE_IMPL::size_of_farblk_send_field() const {

    size_t send_field_size = 0;

    if (m_sim_opts->is_lb_model){
      send_field_size += sizeof(tUBLK_LB_SEND_FIELD<8>);
      //Even though states are compressed, assume we allocate storage for
      //all states
      send_field_size += sizeof(sUBLK_STATES_DATA);
    }

    if (m_sim_opts->is_turb_model){
      send_field_size += sizeof(tUBLK_TURB_SEND_FIELD<8>);
    }

    if(m_sim_opts->is_heat_transfer) {
      append_t_send_field_size(send_field_size,m_sim_opts,SIM_SIZES::SHOB_TYPE::UBLK);
    }

    if (m_sim_opts->is_multi_component && m_sim_opts->is_lb_model){
      send_field_size += sizeof(tUBLK_MC_SEND_FIELD<8>);
    }

    if (m_sim_opts->n_user_defined_scalars > 0 && m_sim_opts->is_scalar_model){
      append_uds_send_field_size(send_field_size,m_sim_opts,SIM_SIZES::SHOB_TYPE::UBLK);
    }

    if (m_sim_opts->is_particle_sim){
      //Ignore particle comm for now
    }

    return send_field_size;

  }

  /*=================================================================================================
   * @fcn size_of_surfel_send_field
   *================================================================================================*/
  size_t SIMENG_SIZES_INTERFACE_IMPL::size_of_surfel_send_field() const {

    size_t send_field_size = 0;

    if (m_sim_opts->is_lb_model) {
      send_field_size += sizeof(sSURFEL_LB_SEND_FIELD);
      send_field_size += sizeof(sSURFEL_V2S_LB_SEND_FIELD);
    }

    if (m_sim_opts->is_turb_model) {
      send_field_size += sizeof(sSURFEL_TURB_SEND_FIELD);
      send_field_size += sizeof(sSURFEL_V2S_TURB_SEND_FIELD);
    }

    if(m_sim_opts->is_heat_transfer) {
      append_t_send_field_size(send_field_size,m_sim_opts,SIM_SIZES::SHOB_TYPE::SURFEL);
    }

    if (m_sim_opts->is_multi_component && m_sim_opts->is_lb_model) {
      send_field_size += sizeof(sSURFEL_MC_SEND_FIELD);
    }
    if (m_sim_opts->n_user_defined_scalars > 0 && m_sim_opts->is_scalar_model) {
      append_uds_send_field_size(send_field_size,m_sim_opts,SIM_SIZES::SHOB_TYPE::SURFEL);
    }
#if BUILD_D19_LATTICE
    if (m_sim_opts->is_momentum_freeze && m_sim_opts->is_lb_model) {
      send_field_size += sizeof(sSURFEL_FROZEN_SEND_FIELD);
    }
#endif
//    if (this->is_lrf())
//    {
//      sSURFEL_LRF_DATA::add_send_size(send_size, tpde_send_size);
//    }
return send_field_size;
  }

  
  /*=================================================================================================
   * @fcn fill_surfel_sizes
   *================================================================================================*/
  static  VOID fill_surfel_sizes(DGF_SURFEL_DESC desc,
				 sSHOB_SIZES& s,
				 const SIM_SIZES::SHOB_INFO& shob_info) {
    
    std::vector<cDGF_SURFEL_PROC_GHOST> ghost_info;
    uINT16 ghost_flags = 0;

    //Weights are not allocated as part of the surfel, hence they must
    //be accounted for separately
    asINT32 surfel_weight_set_size = get_surfel_weight_set_size(desc);

    //Simple surfel type
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
    cBASE_SURFEL_DESCS_PARSER* parser =
      cSURFEL_DESC_PARSER_FACTORY::create_surfel_desc_parser(STP_FLOW_REALM,
                                                             desc,
                                                             my_proc_id,
                                                             RP_PROC_INVALID,
                                                             RP_PROC_INVALID,
                                                             ghost_flags,
                                                             ghost_info);
#else
#error Conduction shob sizes not yet implemented
#endif
    
    parser->set_phys_type((STP_PHYSTYPE_TYPE)shob_info.phys_type);
      
    s.simple_shob_size = parser->SIZE() + surfel_weight_set_size;


    //ghost surfel type
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
    parser =
      cSURFEL_DESC_PARSER_FACTORY::create_surfel_desc_parser(STP_FLOW_REALM,
                                                             desc,
                                                             my_proc_id + 1, //Some random home sp
                                                             RP_PROC_INVALID,
                                                             RP_PROC_INVALID,
                                                             ghost_flags,
                                                             ghost_info);
#else
#error Conduction shob sizes not yet implemented
#endif

    parser->set_phys_type((STP_PHYSTYPE_TYPE)shob_info.phys_type);
      
    s.ghost_shob_size = parser->SIZE() + surfel_weight_set_size;

    //surfel with ghosts
    cDGF_SURFEL_PROC_GHOST random_ghost;
    ghost_info.push_back(random_ghost); //Some random ghost info
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
     parser = cSURFEL_DESC_PARSER_FACTORY::create_surfel_desc_parser(STP_FLOW_REALM,
                                                                     desc,
                                                                     my_proc_id,
                                                                     RP_PROC_INVALID,
                                                                     RP_PROC_INVALID,
                                                                     ghost_flags,
                                                                     ghost_info);
#else
#error Conduction shob sizes not yet implemented
#endif
    
    parser->set_phys_type((STP_PHYSTYPE_TYPE)shob_info.phys_type);
      
    s.shob_with_ghosts_size = parser->SIZE() + surfel_weight_set_size;    
      
  }

  /*=================================================================================================
   * @fcn fill_ublk_sizes
   *================================================================================================*/
  static  VOID fill_ublk_sizes(DGF_UBLK_BASE_DESC desc,
			       sSHOB_SIZES& s,
			       const SIM_SIZES::SHOB_INFO& shob_info) {
    
    std::vector<cDGF_GHOST_INFO> ghost_info;
    //decomp flags do not matter when it comes to size calculations, they;re
    //only used to determine group type
    uINT8 ublk_decomp_flags = 0;

    //Simple surfel type
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
    cBASE_UBLK_DESCS_PARSER* parser =
      cUBLK_DESC_PARSER_FACTORY::create_ublk_desc_parser(STP_FLOW_REALM,
               desc,
							 ublk_decomp_flags,
							 my_proc_id,
							 ghost_info,
							 nullptr);
#else
#error Conduction shob sizes not yet implemented
#endif
    
    parser->set_parse_type(cBASE_UBLK_DESCS_PARSER::PARSE_WITHOUT_ALLOCATION);
    parser->parse();
    s.simple_shob_size = parser->SIZE();


    //ghost surfel type
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
    parser =
      cUBLK_DESC_PARSER_FACTORY::create_ublk_desc_parser(STP_FLOW_REALM,
               desc,
							 ublk_decomp_flags,
							 my_proc_id + 1, //Some random home sp
							 ghost_info,
							 nullptr);    
#else
#error Conduction shob sizes not yet implemented
#endif
    parser->parse();  
    s.ghost_shob_size = parser->SIZE();

    //surfel with ghosts
    ghost_info.push_back(cDGF_GHOST_INFO()); //Some random ghost info
#ifdef CONDUCTION_SIMSIZES_USE_FLOW_SIZES
    parser =
     cUBLK_DESC_PARSER_FACTORY::create_ublk_desc_parser(STP_FLOW_REALM,
              desc,
							ublk_decomp_flags,
							my_proc_id,
							ghost_info,
							nullptr);
#else
#error Conduction shob sizes not yet implemented
#endif
    
    parser->parse();     
    s.shob_with_ghosts_size = parser->SIZE();
      
  }
  
  /*=================================================================================================
   * @fcn fill_bsurfel_sizes
   *================================================================================================*/
  static  VOID fill_bsurfel_sizes(DGF_BSURFEL_DESC desc,
                                  sSHOB_SIZES& s,
                                  const SIM_SIZES::SHOB_INFO& shob_info) {
    //TO DO:: modify bsurfel parsing to use parser infrastructure and
    // abstract a size method that we can use here similar to SURFELs and UBLKs
    //For now we keep this simple
    s.ghost_shob_size = s.shob_with_ghosts_size
    = s.simple_shob_size = sizeof(sBSURFEL);
  }

  /*=================================================================================================
   * @fcn get_shob_size
   *================================================================================================*/
  sSHOB_SIZES SIMENG_SIZES_INTERFACE_IMPL::get_shob_size(const SIM_SIZES::SHOB_INFO& shob_info) const{

    sSHOB_SIZES shob_size;    
    DGF_SHOB_DESC desc = (DGF_SHOB_DESC) &shob_info.desc;
    switch (desc->type()){
    case DGF_SHOB_SURFEL:
      {      
	fill_surfel_sizes((DGF_SURFEL_DESC) desc,shob_size,shob_info);      
	break;
      }
    case DGF_SHOB_BSURFEL:
      fill_bsurfel_sizes((DGF_BSURFEL_DESC) desc,shob_size,shob_info);
      break;
    case DGF_SHOB_SIMPLE_UBLK:
    case DGF_SHOB_REAL_UBLK:
    case DGF_SHOB_MIRROR_UBLK:
      {
	fill_ublk_sizes((DGF_UBLK_BASE_DESC) desc,shob_size,shob_info);
	break;
      }
    default:
      break;
    };

    return shob_size;
  };

  VOID SIMENG_SIZES_INTERFACE_IMPL::set_ublk_static_info(const SIM_SIZES::sUBLK_STATIC_INFO& info){
    SIMULATOR_NAMESPACE::cBASE_UBLK_DESCS_PARSER::set_static_info_for_simsizes(info);
  }

} /* namespace SIMULATOR_NAMESPACE */

