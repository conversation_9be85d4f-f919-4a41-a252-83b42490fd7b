/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef SIMENG_VR_COALESCE_EXPLODE_H
#define SIMENG_VR_COALESCE_EXPLODE_H

#include PHYSICS_H

#include "vr.h"
#include "ublk.h"

template<typename UBLK_TYPE>
__DEVICE__ 
static bool vr_fine_ublk_is_invalid(const UBLK_TYPE* fine_ublk, int child_ublk) 
{
  return (fine_ublk == NULL) || (fine_ublk->is_solid(child_ublk));
}


template <bool IS_NEAR_SURFACE, bool IS_T_S_LB_SOLVER_ON, bool IS_UDS_LB_SOLVER_ON, typename UBLK_TYPE>
__DEVICE__ VOID execute_coalesce_rule_impl(UBLK_TYPE* coarse_ublk, 
                                SOLVER_INDEX_MASK prior_solver_index_mask,
                                ACTIVE_SOLVER_MASK active_solver_mask) 
{
  enum {
    N_VOXELS = UBLK_TYPE::N_VOXELS
  };
  auto& sim = get_sim_ref();
  auto& sim_c = *sim.c();
  BOOLEAN is_2d = sim_c.is_2d();
  asINT32 n_dims = sim_c.num_dims;
  sdFLOAT fractional_factor = (1.0 - g_adv_fraction) / g_adv_fraction;

  BOOLEAN is_lb_active   = (active_solver_mask & LB_ACTIVE);
  asINT32 coarse_scale = coarse_ublk->scale();
  asINT32 prev_coarse_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_coarse_lb_index = 1 ^ prev_coarse_lb_index;
  asINT32 prev_coarse_t_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 prev_coarse_uds_index = uds_index_from_mask(prior_solver_index_mask);

  asINT32 curr_fine_lb_index   = sUBLK::coalesce_fine_state_index();
  asINT32 curr_fine_lb_t_index = sUBLK::coalesce_fine_state_index();
  asINT32 curr_fine_lb_uds_index = sUBLK::coalesce_fine_state_index();
  const int child_ublk = get_cuda_thread_child_ublk();

  typename UBLK_TYPE::sVR_COARSE_INTERFACE_DATA* vr_coarse_data =  coarse_ublk->vr_coarse_data();
  VR_CONFIG config = vr_coarse_data->vr_config(child_ublk);

  // Find the vr coarse ublk dynamics type by using the first voxel, then increment the corresponding vrc voxel counter
  // This only makes sense when all the voxels within a vrc ublk are of the same type. This is true for cases with only
  // one physics type (fluid, porous medai or fan). Therefore the vrc voxel counters only make sense for these special 
  // cases. Eventually we will use the initialization counters to replace the in-simulation counters, and it is put here 
  // temporarily for checking if the init counters and in-simu counters are consistent.

#if ! DEVICE_COMPILATION_MODE
  auINT32 fluid_type = coarse_ublk->dynamics_data()->fluid_type();
  BOOLEAN is_split = coarse_ublk->is_split();
  asINT32 ublk_timer = ublk_dyn_timer_from_fluid_type(TRUE, is_split, IS_NEAR_SURFACE, fluid_type);
#endif


  /* Coalesce everything from the VR fine ublks into coarse states */
  {

    UBLK_STATE *prev_coarse_states = coarse_ublk->lb_states(prev_coarse_lb_index)->m_states;
    UBLK_STATE *curr_coarse_states = coarse_ublk->lb_states(curr_coarse_lb_index)->m_states;

    /* In 2D, there are only 4 underlying VR fine blks for each coarse ublk.
     * Hence (via coalesce) each underlying VR fine blk defines 2 coarse voxels.
     */
    ccDO_UBLK_VOXELS(coarse_voxel) {
      asINT32 fine_blk_idx = coarse_voxel;
      tVR_UBLK_PTR<UBLK_TYPE> vr_fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx);
      UBLK_TYPE* fine_ublk = vr_fine_ublk.ublk();
      const int fine_mblk_offset = vr_fine_ublk.offset();
      const int fine_mblk_start  = fine_mblk_offset * N_VOXELS_8;
      const int fine_mblk_end    = fine_mblk_start  + N_VOXELS_8;
      if (vr_fine_ublk_is_invalid(fine_ublk, fine_mblk_offset))
        continue;

#if ENABLE_SIM_COUNTERS
      if (!coarse_ublk->is_fringe_or_fringe2()) {
        if (IS_NEAR_SURFACE)
          timer_accum_counters(ublk_timer, 3, 1);
        else  
          timer_accum_counters(ublk_timer, 3, 1);
      }
#endif
      //CONDUCTION-TODO: The conduction solid should not have lb active anyway - so when implemented, the
      //is_conduction_solid is not needed
      if (!coarse_ublk->is_conduction_solid() && is_lb_active) {
        cVR_STATES& states = config->coalesce_states[fine_blk_idx % N_VOXELS_8];
        auto *fine_states = fine_ublk->lb_states(curr_fine_lb_index)->m_states;
        asINT32 voxel_inc = 1 + (3 - n_dims);

        dFLOAT pfluid_sum = 0.0;
        if (states.any()) {
          if (!IS_NEAR_SURFACE) {
            pfluid_sum = 1 << n_dims;
          } else {
            sdFLOAT *fine_pfluids = fine_ublk->surf_geom_data()->pfluids;

            ccDO_FROM_BELOW_BY(fine_voxel, fine_mblk_start, fine_mblk_end, voxel_inc) {
              if (fine_pfluids[fine_voxel] > 0.0)
                pfluid_sum += fine_pfluids[fine_voxel];
            }
          }
        }

        /* Loop over states */
        if (pfluid_sum > 0.0) {
          sdFLOAT inv_pfluid_sum = 1.0/pfluid_sum;
          ccDO_VR_STATES(state_index, states) {
            dFLOAT dtotal = 0;
            if (!IS_NEAR_SURFACE) {
              ccDO_FROM_BELOW_BY(fine_voxel, fine_mblk_start, fine_mblk_end, voxel_inc) {
                dtotal += fine_states[state_index][fine_voxel];
              }
            } else {
              /* We do not need to multiply by pas_factor because this is
               * already done while V2V on the fine ublks */
              // ASSUMPTION Summation_over_vrf (pas_i(x) * pf(x) ) =
              // summation_over_vrf(pf(x)) * pas_i(VRC)
              // Surfels advect to  VR coarse during OS2V where as pas_factors
              // from VR fine are used to scale V2V contribution. They are
              // assumed to sum up to 1.
              sdFLOAT *fine_pfluids = fine_ublk->surf_geom_data()->pfluids;
              ccDO_FROM_BELOW_BY(fine_voxel, fine_mblk_start, fine_mblk_end, voxel_inc) {
                if (fine_pfluids[fine_voxel] > 0.0)  {
                  dtotal += fine_states[state_index][fine_voxel] *
                    fine_pfluids[fine_voxel];
#ifdef DEBUG_CONDUCTION_SOLVER
	if(coarse_ublk->id()==42&&coarse_voxel==6&&state_index==19){
	  msg_print("fine_ublk %d fine_voxel %d fine_state %g", fine_ublk->id(), fine_voxel, fine_states[state_index][fine_voxel]);
	  msg_print("dtotal %g", dtotal);
	}
#endif
                }
              }
            }

            dtotal = dtotal * inv_pfluid_sum;
            /* Save state */
            // There is no S2V contribution for farblks, so we can remove the V2V contribution
            // from neighboring coarse voxels for states to be coalesced.
            // However for nearblks, we should set the pas factor to 0 for states
            // from neighboring coarse voxels which will be coalesced.
            if (!IS_NEAR_SURFACE) {
              curr_coarse_states[state_index][coarse_voxel] = dtotal +
                fractional_factor * prev_coarse_states[state_index][coarse_voxel];
            } else {
              // coarse_vr_states should have S2V and stay_state contribution already.
              // V2V contribution should be zero for states to be coalesced.
              curr_coarse_states[state_index][coarse_voxel] += dtotal;              
            }
          } /* state loop */
        }

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
        if (IS_T_S_LB_SOLVER_ON) {
          STP_GEOM_VARIABLE (*pas_factors)[N_VOXELS] = NULL;
          if (IS_NEAR_SURFACE) {
            pas_factors = (STP_GEOM_VARIABLE (*)[N_VOXELS])
              fine_ublk->surf_lb_data()->post_advect_scale_factors;
          }
          UBLK_STATE *fine_states_t = fine_ublk->t_data()->lb_t_data(curr_fine_lb_t_index)->m_states_t;
          if (pfluid_sum > 0.0) {
            ccDO_VR_STATES(state_index, states) {
              VOXEL_STATE *coarse_vr_states_t =
                (VOXEL_STATE *)coarse_ublk->t_data()->lb_t_data(curr_coarse_lb_index)->m_states_t[state_index];

              dFLOAT dtotal_t = 0;
              dFLOAT fine_pas_scale_up_factor = 0;

              ccDO_FROM_BELOW_BY(fine_voxel, fine_mblk_start, fine_mblk_end, voxel_inc) {
                if (!IS_NEAR_SURFACE) {
                  dtotal_t += fine_states_t[state_index][fine_voxel];
                } else {
                  if (fine_ublk->surf_geom_data()->pfluids[fine_voxel] > 0.0)  {
                    /* We do not need to multiply by pas_factor because this is
                     * already done while V2V on the fine ublks */
                    dtotal_t += fine_states_t[state_index][fine_voxel] *
                      fine_ublk->surf_geom_data()->pfluids[fine_voxel];
                    // ASSUMPTION Summation_over_vrf (pas_i(x) * pf(x) ) =
                    // summation_over_vrf(pf(x)) * pas_i(VRC)
                    // VR coarse pas_factor is used to scale S2V contribution,
                    // where as pas_factors from VR fine are used to scale V2V
                    // contribution. They are assumed to sum up to 1.
                    //fine_pas_scale_up_factor += pas_factors[state_index][fine_voxel] *
                    //                fine_nearblk->surf_geom_data()->pfluids[fine_voxel];
                  }
                }
              }

              dtotal_t = dtotal_t / pfluid_sum;

              if (IS_NEAR_SURFACE) {
                coarse_vr_states_t[coarse_voxel] += dtotal_t;
              } else {
                // no fractional advection for temperature states
                coarse_vr_states_t[coarse_voxel] = dtotal_t;
              }
              // This was to done to avoid discrepancy between post_advect_scale
              // factors between VR coarse and VR fine ublks?
              //if (IS_NEAR_SURFACE) {
              //NEARBLK coarse_nearblk = (NEARBLK)coarse_ublk;
              //coarse_nearblk->surf_lb_data()->post_advect_scale_factors[state_index][coarse_voxel] = fine_pas_scale_up_factor / pfluid_sum;
              //}
            }
          }
        }
#endif
	if (IS_UDS_LB_SOLVER_ON) {
          STP_GEOM_VARIABLE (*pas_factors)[N_VOXELS] = NULL;
          if (IS_NEAR_SURFACE) {
            pas_factors = (STP_GEOM_VARIABLE (*)[N_VOXELS])
                          fine_ublk->surf_lb_data()->post_advect_scale_factors;
          }
	  ccDOTIMES(nth_uds, sim_c.n_user_defined_scalars){
	    UBLK_STATE *fine_states_uds = fine_ublk->uds_data(nth_uds)->lb_uds_data(curr_fine_lb_uds_index)->m_states_uds;
	    if (pfluid_sum > 0.0) {
	      ccDO_VR_STATES(state_index, states) {
		VOXEL_STATE *coarse_vr_states_uds =
		    (VOXEL_STATE *)coarse_ublk->uds_data(nth_uds)->lb_uds_data(curr_coarse_lb_index)->m_states_uds[state_index];

		dFLOAT dtotal_uds = 0;
		dFLOAT fine_pas_scale_up_factor = 0;

		ccDO_FROM_BELOW_BY(fine_voxel, fine_mblk_start, fine_mblk_end, voxel_inc) {
		  if (!IS_NEAR_SURFACE) {
		    dtotal_uds += fine_states_uds[state_index][fine_voxel];
		  } else {
		    if (fine_ublk->surf_geom_data()->pfluids[fine_voxel] > 0.0)  {
		     /* We do not need to multiply by pas_factor because this is
		      * already done while V2V on the fine ublks */
		      dtotal_uds += fine_states_uds[state_index][fine_voxel] *
				  fine_ublk->surf_geom_data()->pfluids[fine_voxel];
		      // ASSUMPTION Summation_over_vrf (pas_i(x) * pf(x) ) =
		      // summation_over_vrf(pf(x)) * pas_i(VRC)
		      // VR coarse pas_factor is used to scale S2V contribution,
		      // where as pas_factors from VR fine are used to scale V2V
		      // contribution. They are assumed to sum up to 1.
		      //fine_pas_scale_up_factor += pas_factors[state_index][fine_voxel] *
		      //                fine_nearblk->surf_geom_data()->pfluids[fine_voxel];
		    }
		  }
		}

		dtotal_uds = dtotal_uds / pfluid_sum;

		if (IS_NEAR_SURFACE) {
		  coarse_vr_states_uds[coarse_voxel] += dtotal_uds;
		} else {
		  // no fractional advection for temperature states
		  coarse_vr_states_uds[coarse_voxel] = dtotal_uds;
		}
		// This was to done to avoid discrepancy between post_advect_scale
		// factors between VR coarse and VR fine ublks?
		//if (IS_NEAR_SURFACE) {
		 //NEARBLK coarse_nearblk = (NEARBLK)coarse_ublk;
		 //coarse_nearblk->surf_lb_data()->post_advect_scale_factors[state_index][fine_voxel_num] = fine_pas_scale_up_factor / pfluid_sum;
		//}
	      }
	    }
	  }
        }//uds


      }
#if !DEVICE_COMPILATION_MODE
      fine_ublk->unset_exploded();
#endif
    } /* Fine microblock loop */
  }
}

__DEVICE__ static bool is_temp_active(ACTIVE_SOLVER_MASK active_solver_mask)
{
  return ((active_solver_mask & T_PDE_ACTIVE) != 0);
}

__DEVICE__ static bool is_uds_active(ACTIVE_SOLVER_MASK active_solver_mask)
{
  return ((active_solver_mask & UDS_PDE_ACTIVE) != 0);
}

template <typename UBLK_TYPE>
__DEVICE__ VOID execute_coalesce_rule(UBLK_TYPE* coarse_ublk, 
                          SOLVER_INDEX_MASK prior_solver_index_mask,
                          ACTIVE_SOLVER_MASK active_solver_mask) 
{
  auto& sim = get_sim_ref();
  auto& sim_c = *sim.c();
  asINT32 voxor = get_dyn_voxor();
  asINT32 child_ublk_index = get_child_ublk_index(voxor);
  bool is_near_regular_surface = coarse_ublk->is_near_regular_surface(child_ublk_index);
  BOOLEAN is_T_S_lb_solver_on = is_temp_active(active_solver_mask) && sim_c.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active(active_solver_mask) && (sim_c.uds_solver_type == LB_UDS);
  if (is_near_regular_surface) {
    if (is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<true, true, false>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else if (is_T_S_lb_solver_on && is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<true, true, true>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else if (!is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<true, false, false>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else {
      execute_coalesce_rule_impl<true, false, true>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
  }
  else  {
    if (is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<false, true, false>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else if (is_T_S_lb_solver_on && is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<false, true, true>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else if (!is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      execute_coalesce_rule_impl<false, false, false>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
    else {
      execute_coalesce_rule_impl<false, false, true>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
    }
  }
}

#if BUILD_GPU
namespace GPU {
  VOID vr_coarse_coalesce(UBLK_GROUP_TYPE group_type,
			   const size_t range[2],
			   SOLVER_INDEX_MASK prior_solver_index_mask,
			   ACTIVE_SOLVER_MASK active_solver_mask);
}
#endif

#endif
