/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Surfel advection 
 *
 * James Hoch, Exa Corporation 
 * Created Tues Dec 16, 1997
 *--------------------------------------------------------------------------*/


#ifndef _SIMENG_SURFEL_ADVECT_H
#define _SIMENG_SURFEL_ADVECT_H

#include <vector>
#include VMEM_VECTOR_H

#include "common_sp.h"
#include "lattice.h"
#include "shob.h"
#include "ublk.h"
#include "surfel.h"
#include "sampling_surfel.h"
#include "vr.h"
#include "fset.h"
#include "ublk_table.h"
#include "surfel_table.h"

/*--------------------------------------------------------------------------*
 * Tagged S2S Surfels
 *--------------------------------------------------------------------------*/

#define TAGGED_S2S_SURFEL_LRF_SRC_TAG   4
#define TAGGED_S2S_SURFEL_EVEN_ODD_TAG  3

#define TAGGED_S2S_SURFEL_N_TAG_BITS    3
#define TAGGED_S2S_SURFEL_TAG_MASK      7

template <typename SURFEL_TYPE>
union tTAGGED_S2S_SURFEL {

private:
  SURFEL_TYPE        tagged_ptr;
  POINTER_SIZE_INT   tagged_id;  // An ID may live here temporarily until we can convert it into a pointer

public:
  BOOLEAN is_lrf_src() { return (BOOLEAN)scalar_mask_pointer_to_int(tagged_ptr, TAGGED_S2S_SURFEL_LRF_SRC_TAG); }

  VOID mark_lrf_src()
  { tagged_ptr = (SURFEL_TYPE)scalar_or_pointer(tagged_ptr, TAGGED_S2S_SURFEL_LRF_SRC_TAG); }

  VOID unmark_lrf_src()
  { tagged_ptr = (SURFEL_TYPE)scalar_mask_pointer(tagged_ptr, ~TAGGED_S2S_SURFEL_LRF_SRC_TAG); }

  // The tagged_surfel even_odd flag is only set to something other than
  // STP_PROCESS_ON_ALL_TIMES for a surfel at the same scale as the coarse ublks.

  auINT32 even_odd() { return (auINT32)scalar_mask_pointer_to_int(tagged_ptr, TAGGED_S2S_SURFEL_EVEN_ODD_TAG); }

  VOID mark_even_odd(auINT32 even_odd) 
  { tagged_ptr = (SURFEL_TYPE)scalar_or_pointer(scalar_mask_pointer(tagged_ptr, ~TAGGED_S2S_SURFEL_EVEN_ODD_TAG), 
                                                even_odd & TAGGED_S2S_SURFEL_EVEN_ODD_TAG); }

  SURFEL_TYPE ptr() { return (SURFEL_TYPE)scalar_mask_pointer(tagged_ptr, ~TAGGED_S2S_SURFEL_TAG_MASK); }

  VOID init(SURFEL_TYPE surfel)           { tagged_ptr = surfel; }

  /* Constructors */
  tTAGGED_S2S_SURFEL(SURFEL_TYPE surfel)  { init(surfel); }
  
  tTAGGED_S2S_SURFEL(SURFEL_ID id)        { tagged_id = (POINTER_SIZE_INT)id << TAGGED_S2S_SURFEL_N_TAG_BITS; }

  SURFEL_ID id()                          { return tagged_id >> TAGGED_S2S_SURFEL_N_TAG_BITS; }
};

/* Tagged S2S surfels are meant to be passed by value. Hence only the struct type
 * is defined. 
 */
typedef tTAGGED_S2S_SURFEL<SURFEL> TAGGED_S2S_SURFEL;

inline asINT32 active_latvecs(STP_LATVEC_MASK latvec_mask) {
  asINT32 n_latvecs = 0;
  while (latvec_mask) {
    latvec_mask &= (latvec_mask - 1);
    n_latvecs++;
  }
  return n_latvecs;
}
/*--------------------------------------------------------------------------*
 * Surfel to surfel advection
 *--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------*
 * S2S quantums
 *--------------------------------------------------------------------------*/

/*typedef struct sS2S_ADVECT_DATA {
  uINT16                    m_n_src_surfels; // Number of surfels that contribute to DEST_SURFEL
  cBOOLEAN                  some_lrf_src;
  SURFEL_SURFEL_INTERACTION m_surfel_interactions;
} *S2S_ADVECT_DATA;*/

template < typename DEST_SURFEL_TYPE >
struct tS2S_QUANTUM {
  DEST_SURFEL_TYPE dest_surfel;	
  sINT32           n_src_surfels; // Number of surfels that contribute to DEST_SURFEL
  cBOOLEAN         some_lrf_src;

  tS2S_QUANTUM(DEST_SURFEL_TYPE _dest_surfel) :
    dest_surfel(_dest_surfel), 
    n_src_surfels(0),
    some_lrf_src(FALSE)
  {  }
};

/*--------------------------------------------------------------------------*
 * S2S Operations
 *--------------------------------------------------------------------------*/
VOID do_surfel_to_surfel_advect(SCALE scale,
				ACTIVE_SOLVER_MASK active_solver_mask,
				ACTIVE_SOLVER_MASK even_active_solver_mask,
				ACTIVE_SOLVER_MASK odd_active_solver_mask);

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/
VOID accumulate_all_v2s_and_s2s_weights();
asINT32 ls_count_voxel_surfel_interactions();

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/

VOID do_dyn_v2s_advect(SCALE scale, ACTIVE_SOLVER_MASK active_solver_masks[2],
		       ACTIVE_SOLVER_MASK even_active_solver_masks[2],
		       ACTIVE_SOLVER_MASK odd_active_solver_masks[2]);
VOID do_ghost_v2s_advect(SCALE scale,ACTIVE_SOLVER_MASK active_solver_mask,
			 ACTIVE_SOLVER_MASK even_active_solver_mask,
			 ACTIVE_SOLVER_MASK odd_active_solver_mask);
VOID do_dyn_s2v_advect(SCALE scale,ACTIVE_SOLVER_MASK active_solver_masks[2],
		       ACTIVE_SOLVER_MASK even_active_solver_masks[2],
		       ACTIVE_SOLVER_MASK odd_active_solver_masks[2]);

VOID finish_init_of_s2s_groups();
VOID finish_init_of_v2s_groups();

VOID surfel_adjust_v2s_dist(SURFEL surfel);
VOID surfel_accumulate_v2s_weights(SURFEL surfel);
VOID surfel_accumulate_v2s_sampling_weights(SAMPLING_SURFEL surfel);
VOID surfel_v2s_advect_for_seed(SURFEL surfel,
                                ACTIVE_SOLVER_MASK active_solver_mask,
                                sSURFEL_V2S_DATA *surfel_v2s_data,
                                SOLVER_INDEX_MASK seed_solver_index_mask);
VOID surfel_v2s_meas(SAMPLING_SURFEL surfel,SOLVER_PHASE_MASKS solver_phase_masks,
                       ACTIVE_SOLVER_MASK active_solver_mask);

template <BOOLEAN LB_ACTIVE, BOOLEAN LB_TEMP_SOLVER_ON,
          BOOLEAN LB_UDS_SOLVER_ON, BOOLEAN NO_VR_INTERACTION,
          BOOLEAN IS_LRF_SURFEL, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID surfel_v2s_advect(SURFEL surfel, SOLVER_PHASE_MASKS phase_masks,
                       ACTIVE_SOLVER_MASK active_solver_mask,
                       uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask,
                       sSURFEL_V2S_DATA *surfel_v2s_data);
                       
template <BOOLEAN NO_VR_INTERACTION, BOOLEAN IS_LRF_SURFEL>
VOID conduction_surfel_v2s_sample(SURFEL surfel, asINT32 phase_mask, 
                                  ACTIVE_SOLVER_MASK active_solver_mask,
                                  uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask);

template <BOOLEAN LB_ACTIVE, BOOLEAN LB_TEMP_SOLVER_ON,
          BOOLEAN LB_UDS_SOLVER_ON, BOOLEAN NO_VR_INTERACTION,
          BOOLEAN IS_LRF_SURFEL, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID surfel_s2v_advect(SURFEL surfel, SOLVER_PHASE_MASKS phase_masks,
                       ACTIVE_SOLVER_MASK active_solver_mask,
                       uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask,
                       sSURFEL_V2S_DATA *surfel_v2s_data);

template <BOOLEAN NO_VR_INTERACTION, BOOLEAN IS_LRF_SURFEL>
VOID conduction_surfel_s2v_distribute(SURFEL surfel, asINT32 phase_mask, 
                                  ACTIVE_SOLVER_MASK active_solver_mask,
                                  uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask);
                                  
//Provide default implementation that simply errors out for GPU builds
#if BUILD_GPU
template <BOOLEAN LB_ACTIVE, BOOLEAN LB_TEMP_SOLVER_ON,
          BOOLEAN LB_UDS_SOLVER_ON, BOOLEAN NO_VR_INTERACTION,
          BOOLEAN IS_LRF_SURFEL, BOOLEAN IS_ALL_SOLVER_TS_SAME>
INLINE VOID surfel_v2s_advect(SURFEL surfel, SOLVER_PHASE_MASKS phase_masks,
                              ACTIVE_SOLVER_MASK active_solver_mask,
                              uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask,
                              sSURFEL_V2S_DATA *surfel_v2s_data) {
  msg_error("CPU surfel advection should not be called for GPU solver");
}

template <BOOLEAN LB_ACTIVE, BOOLEAN LB_TEMP_SOLVER_ON,
          BOOLEAN LB_UDS_SOLVER_ON, BOOLEAN NO_VR_INTERACTION,
          BOOLEAN IS_LRF_SURFEL, BOOLEAN IS_ALL_SOLVER_TS_SAME>
INLINE VOID surfel_s2v_advect(SURFEL surfel, SOLVER_PHASE_MASKS phase_masks,
                              ACTIVE_SOLVER_MASK active_solver_mask,
                              uINT32 allowed_scale_diff, SOLVER_INDEX_MASK prior_solver_index_mask,
                              sSURFEL_V2S_DATA *surfel_v2s_data) {
  msg_error("CPU surfel advection should not be called for GPU solver");  
}
#endif

VOID surfel_s2s_advect(SURFEL dest_surfel, ACTIVE_SOLVER_MASK active_solver_mask,
                       sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK odd_active_solver_mask);
VOID conduction_surfel_s2s_sample(SURFEL dest_surfel, ACTIVE_SOLVER_MASK active_solver_mask,
                                  BOOLEAN is_odd_time);
VOID surfel_s2s_meas(SAMPLING_SURFEL dest_surfel, BOOLEAN is_time_step_odd,
                     auINT32 even_odd_mask);
#if BUILD_5G_LATTICE
VOID surfel_accumulate_srf_potential(SURFEL surfel) ;
#endif


#endif /* __SURFEL_ADVECT_H */

