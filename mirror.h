/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Support for symmetry planes 
 *
 * Sam Watson, Exa Corporation 
 * Created Tuesday July 23 2002
 *
 * Possible mottos:
 *
 *  "Objects in the mirror are exactly where they appear"
 *
 *  "Pay no attention to the man behind the curtain"
 *
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MIRROR_H
#define _SIMENG_MIRROR_H

#include "common_sp.h"
#include "shob.h"
#include "group.h"
#include "surfel.h"
#include "ublk.h"
#include "fset.h"
#include "surfel_advect_sp.h"

typedef struct sMIRROR_STATE_PAIR 
{

  STP_STATE_INDEX source_state;
  STP_STATE_INDEX mirror_state;

} *MIRROR_STATE_PAIR;


typedef struct sUBLK_MIRROR_CONFIG
{
  std::vector<sMIRROR_STATE_PAIR>  state_pair_table;
  VOXEL_MASK_8                     voxels_to_mirror_mask;
  VOXEL_NUM                        voxel_mirror_table[ubFLOAT::N_VOXELS];
  // store the sign (+1,-1) to flip the appropriate velocity vector components for each state 
  STP_PHYS_VARIABLE                velocity_mirror_sign_factor[N_AXES];
  STP_STATE_INDEX                  m_state_index;

  VOID init(STP_STATE_INDEX state_index) {
    m_state_index = state_index;

    bool is_2d = sim.is_2d();
    voxels_to_mirror_mask = VOXEL_MASK_8{0};

    STP_STATE_VEL vel[N_AXES] = { state_vx(state_index),
                                  state_vy(state_index),
                                  state_vz(state_index) };

    ccDOTIMES(axis, N_AXES) {
      velocity_mirror_sign_factor[axis] = (vel[axis] == 0)? 1.0 : -1.0;
    }

    ccDOTIMES(voxel_index, ubFLOAT::N_VOXELS) {
      voxel_mirror_table[voxel_index] =
        stp_voxel_index(num_to_voxel_offset(voxel_index,0) ^ (vel[STP_X_AXIS] != 0 ? 1 : 0),
                        num_to_voxel_offset(voxel_index,1) ^ (vel[STP_Y_AXIS] != 0 ? 1 : 0),
                        num_to_voxel_offset(voxel_index,2) ^ (vel[STP_Z_AXIS] != 0 ? 1 : 0));
    }

    ccDOTIMES(search_state_index, N_MOVING_STATES) {
      STP_STATE_VEL search_vel[N_AXES] = { state_vx(search_state_index),
                                           state_vy(search_state_index),
                                           state_vz(search_state_index) };

      bool state_already_added = false;
      ccDOTIMES(voxel_index, ubFLOAT::N_VOXELS) {
        // add voxel offset to search for valid states to reflect in each voxel in the source ublk
        asINT32 voxel_loc[N_AXES] =  {num_to_voxel_x(voxel_index),
                                      num_to_voxel_y(voxel_index),
                                      num_to_voxel_z(voxel_index)};

        bool check[N_AXES];
        if (is_2d && num_to_voxel_z(voxel_index) > 0)
          continue;

        ccDOTIMES(axis, N_AXES) {
          if (vel[axis] >= 0) {
            check[axis] = (search_vel[axis] - vel[axis]) >= voxel_loc[axis];
          }
          else {
            check[axis] = (search_vel[axis] - vel[axis]) <= (voxel_loc[axis] - 1);
          }
        }
        if (((vel[STP_X_AXIS] == 0) || check[STP_X_AXIS])
            && ((vel[STP_Y_AXIS] == 0) || check[STP_Y_AXIS])
            && ((vel[STP_Z_AXIS] == 0) || check[STP_Z_AXIS])) {
          // valid state to reflect for this voxel
          voxels_to_mirror_mask.set(voxel_index);
          if (!state_already_added) {
            STP_STATE_VEL mirror_vel[N_AXES] =
                    {static_cast<STP_STATE_VEL>(search_vel[STP_X_AXIS] * velocity_mirror_sign_factor[STP_X_AXIS]),
                     static_cast<STP_STATE_VEL>(search_vel[STP_Y_AXIS] * velocity_mirror_sign_factor[STP_Y_AXIS]),
                     static_cast<STP_STATE_VEL>(search_vel[STP_Z_AXIS] * velocity_mirror_sign_factor[STP_Z_AXIS])};
            sMIRROR_STATE_PAIR state_pair = {static_cast<STP_STATE_INDEX>(search_state_index),
                                             convert_state_vel_to_latvec_index(mirror_vel)};
            state_pair_table.push_back(state_pair);
            state_already_added = true;
          }
        }
      } // ubFLOAT::N_VOXELS
    } // N_MOVING_STATES
  }

} *UBLK_MIRROR_CONFIG;

// A real ublk can have up to 7 mirror ublks (however 3 realistically)
inline namespace SIMULATOR_NAMESPACE {
template<typename UBLK_TYPE_TAG>
struct tUBLK_MIRROR_DATA
{
  tUBLK<UBLK_TYPE_TAG> *m_mirror_ublk;
  UBLK_MIRROR_CONFIG m_ublk_mirror_config;
  tUBLK_MIRROR_DATA  *m_next_mirror_data;
};
typedef tUBLK_MIRROR_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_MIRROR_DATA, *UBLK_MIRROR_DATA;
}

typedef struct sSURFEL_MIRROR_CONFIG
{
  STP_DIRECTION         m_direction;
  // There will be an implicit padding of 3 bytes in single precision
  // There will be an implicit padding of 7 bytes in double precision
  STP_PHYS_VARIABLE     m_velocity_mirror_sign_factor[N_AXES];
  STP_STATE_INDEX       m_reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES];  //new


  VOID init(STP_DIRECTION direction) {
    m_direction = direction;

    static asINT32 direction_vector[STP_DIRECTION_MASK_N_DIRECTIONS][N_AXES] =
        STP_DIRECTION_MASK_DIRECTION_VECTORS;

    asINT32 dirs[N_AXES] = {direction_vector[m_direction][STP_X_AXIS],
                            direction_vector[m_direction][STP_Y_AXIS],
                            direction_vector[m_direction][STP_Z_AXIS]};
    ccDOTIMES(axis, N_AXES) {
      m_velocity_mirror_sign_factor[axis] = dirs[axis] == 0? 1.0 : -1.0;
    }
    ccDOTIMES(state_index, N_MOVING_STATES) {
      STP_STATE_VEL mirror_vel[N_AXES];
      ccDOTIMES(axis, N_AXES) {
        mirror_vel[axis] = g_state_table[state_index].vel[axis]* m_velocity_mirror_sign_factor[axis];
      }
      STP_STATE_INDEX reflected_state = convert_state_vel_to_latvec_index(mirror_vel);
      if (reflected_state >= N_MOVING_STATES)
        msg_internal_error("Invalid state velocity computed for direction %d for surfel mirror group", m_direction);
      m_reflected_latvec_pair[state_latvec_pair(state_index)] = state_latvec_pair(reflected_state); //new
    }
  }
} *SURFEL_MIRROR_CONFIG;

inline namespace SIMULATOR_NAMESPACE {
typedef struct sSURFEL_MIRROR_DATA
{
  SURFEL               m_mirror_surfel;
  STP_LATVEC_MASK      m_latvec_state_mask;
  SURFEL_MIRROR_CONFIG m_surfel_mirror_config;
  sSURFEL_MIRROR_DATA  *m_next_mirror_data;
} *SURFEL_MIRROR_DATA;
} //SIMULATOR_NAMESPACE


VOID reflect_states_from_mirror_voxels_to_vrblk_voxels(SCALE scale, BOOLEAN is_near_surface);

VOID reflect_solver_data_from_ghosted_voxels_to_mirror_voxels(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask);
VOID reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask);
VOID reset_mirror_states_for_vrblk_voxels(SCALE scale, BOOLEAN is_near_surface);

VOID copy_voxel_masks_and_pfluids_for_all_ublk_mirror_groups();
VOID copy_voxel_masks_and_pfluids_to_mirror_ublks();

VOID reflect_curr_states_to_mirror_ublk(UBLK source_ublk, SOLVER_INDEX_MASK prior_solver_index_mask);
VOID reflect_prev_states_to_mirror_ublk(sUBLK *source_ublk, SOLVER_INDEX_MASK prior_solver_index_mask );
VOID reflect_solver_data_to_mirror_ublk(sUBLK *source_ublk, ACTIVE_SOLVER_MASK active_solver_mask);
VOID reflect_mme_solver_data_to_mirror_ublk(sUBLK *source_ublk, ACTIVE_SOLVER_MASK active_solver_mask);
VOID swap_states_for_mirror_ublks(SCALE scale);

#if BUILD_5G_LATTICE
VOID reflect_adv_states_to_mirror_ublk(UBLK source_ublk, asINT32 time_index);

VOID reflect_full_states_to_mirror_ublk(UBLK source_ublk);
VOID reflect_full_states_from_dynblk_voxels_to_mirror_voxels(SCALE scale);
#endif

UBLK_MIRROR_CONFIG find_mirror_ublk_config(STP_STATE_INDEX state_index);
SURFEL_MIRROR_CONFIG find_mirror_surfel_config(STP_DIRECTION direction);
VOID reflect_mirror_surfel_weights_to_real_surfel(SURFEL source_surfel, SURFEL mirror_surfel,
                                                  STP_LATVEC_MASK latvec_mask,
                                                  STP_STATE_INDEX reflected_states[N_STATES]);
VOID reflect_real_surfel_weights_to_mirror_surfel(SURFEL source_surfel, SURFEL mirror_surfel,
                                                  STP_LATVEC_MASK latvec_mask,
                                                  STP_STATE_INDEX reflected_states[N_STATES]);

VOID mirror_to_real_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask,
                                   sSURFEL_V2S_DATA *real_surfel_v2s_data,
                                   sSURFEL_V2S_DATA *mirror_surfel_v2s_data);
VOID real_to_mirror_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask,
                                   sSURFEL_V2S_DATA *real_surfel_v2s_data,
                                   sSURFEL_V2S_DATA *mirror_surfel_v2s_data);
VOID conduction_mirror_to_real_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask);
VOID conduction_real_to_mirror_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask);

#endif /* #ifndef _SIMENG_MIRROR_H */
