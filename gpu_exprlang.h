#ifndef GPU_EXPRLANG_H
#define GPU_EXPRLANG_H
#include "ublk.h"

#include "exprlang_utilities.h"



#if BUILD_GPU
namespace GPU {

class EXPRLANG_DEVICE_DATA;

//@global g_exprlang_device_data
//Global handle to device exprlang results. The device buffer allocated to hold
//exprlang results is reused by several groups and is overwritten at the beginning
//of a particular group's operation - ex ublk seeding
extern __DEVICE__ EXPRLANG_DEVICE_DATA* g_exprlang_device_data;

/*==============================================================================
 * @class EXPRLANG_DEVICE_DATA
 *============================================================================*/
class EXPRLANG_DEVICE_DATA {

public: 

  //Global device buffer overwritten each time a shob group is evaluated
  __HOST__DEVICE__ static EXPRLANG_DEVICE_DATA* get_data() {
#if DEVICE_COMPILATION_MODE
    return GPU::g_exprlang_device_data;
#else
    return m_ptr_to_global_host_data;
#endif
  }

  VOID set_no_space_or_time_varying_data();

  __DEVICE__ BOOLEAN has_space_or_time_varying_var_data() const {
    return m_current_var_array_size > 0;
  }
  
  VOID set_device_data(const std::vector<uINT32>& var_offset_from_group_index,
                       const std::vector<dFLOAT>& vars,
                       const std::vector<EXPRLANG_VAR_MASK>& var_masks);

  __DEVICE__ uINT32 var_start_index(size_t group_id) {
    cassert(group_id < m_current_group_size);
    return m_var_offset_from_group_index.get()[group_id];
  }
  __DEVICE__ dFLOAT var_value(uINT32 var_index) {
    cassert(var_index < m_current_var_array_size);
    return m_vars_d.get()[var_index];
  }
  __DEVICE__ BOOLEAN all_constant(size_t group_id) {
    cassert(group_id < m_current_group_size);
    return m_exprlang_var_mask.get()[group_id].none();
  }
  __DEVICE__ auto exprlang_var_mask(size_t group_id) {
    cassert(group_id < m_current_group_size);
    return m_exprlang_var_mask.get()[group_id];
  }

  static VOID configure(size_t max_group_size,
                        size_t max_vars_size) {
    m_ptr_to_global_host_data = new EXPRLANG_DEVICE_DATA(max_group_size,
                                                         max_vars_size);
  }
  
private:
  
  EXPRLANG_DEVICE_DATA(size_t max_group_size,
                       size_t max_vars_size);
  
private:

  //offset or index into the m_vars_d array stored per group id
  GPU::Ptr<uINT32> m_var_offset_from_group_index;

  //Flat array of exprlang variables at a mega shob level. Even if some child
  //shobs dont have space/time varying descs, this buffer holds data for them
  GPU::Ptr<dFLOAT> m_vars_d;

  //Conveience mask representing which variables have to be read from the m_vars_d
  //array at a the mega shob level. Note that the physics descriptor only tells you
  //this information at the child shob level
  GPU::Ptr<EXPRLANG_VAR_MASK> m_exprlang_var_mask; 
  
  //m_current_group_size and m_current_var_array_size represent
  //current utilization of buffer space
  size_t m_current_group_size;
  public:
  size_t debugGet_m_current_group_size() const { return m_current_group_size; }
  private:
  size_t m_current_var_array_size;

  //m_max_group_size and m_max_var_array_size
  //represent capacity fixed on construction
  const size_t m_max_group_size;
  const size_t m_max_var_array_size;

private:
  static inline GPU::Ptr<EXPRLANG_DEVICE_DATA> m_ptr_to_global_device_data;
  static inline EXPRLANG_DEVICE_DATA* m_ptr_to_global_host_data;  
};

/*==============================================================================
 * @class tEXPRLANG_HOST_DATA
 * Template for copying information to tEXPRLANG_DEVICE_DATA. Derived classes
 * must implement the fill and init_var_offsets_and_masks functions
 * This host data is stored on a per group, per scale and per operation basis.
 * All instances of tEXPRLANG_HOST_DATA<SHOB_GROUP_TYPE> shared the same host
 * buffer (m_shared_host_buf) for filling variables since it is temporary slot
 * for filling data.
 *============================================================================*/
template<typename SHOB_GROUP_TYPE>
class tEXPRLANG_HOST_DATA {

protected:

  //Fill operation populates the m_shared_host_buf vector
  //The format order is (mshob-variable-voxel/sfl), that is data must be filled
  //per mega shob, in variable order, and fastest varying dimension be the voxel or
  //sfl data for mblk or msfl respectively
  //Only those variables that have their bits active in the m_exprlang_var_mask
  //must be written to the buffer
  virtual void fill(SHOB_GROUP_TYPE* g, sPHYSICS_DESCRIPTOR const*const updated_pd=nullptr) = 0;

  //Initialization of m_var_offset_from_group_index and m_exprlang_var_mask
  //Both vectors of of size g->n_shob_pts()
  //Also intializes m_var_size which is the expected size of device memory needed
  //to hold the exprlang variable data
  virtual void init_var_offsets_and_masks(SHOB_GROUP_TYPE* g) = 0;
  
public:

  size_t var_array_len() const { return m_vars_size; }
  
  BOOLEAN has_space_or_time_varying_var_data() {
    return m_vars_size > 0;
  }

  tEXPRLANG_HOST_DATA(SHOB_GROUP_TYPE* g): m_group(g), m_vars_size(0) {
    size_t group_size = g->n_shob_ptrs();
    m_var_offset_from_group_index.reserve(group_size);
    m_exprlang_var_mask.reserve(group_size);    
  }

  void fill_and_port_to_device(sPHYSICS_DESCRIPTOR const*const pd = nullptr);
  
protected:

  SHOB_GROUP_TYPE* m_group;
  
  //@m_var_offset_from_group_index
  //start index into the m_vars array for every shob part of the group  
  std::vector<uINT32> m_var_offset_from_group_index;

  //per group mshob mask indicating which variables are to be read from exprlang
  //device buffer
  std::vector<EXPRLANG_VAR_MASK> m_exprlang_var_mask;

  //Precomputed number of vars needed to be stored for the group
  size_t m_vars_size;

  //@m_shared_host_buf
  static inline std::vector<dFLOAT> m_shared_host_buf;
};

/*==============================================================================
 * @class EXPRLANG_MBLK_IC_DATA
 *============================================================================*/
class EXPRLANG_MBLK_IC_DATA : public tEXPRLANG_HOST_DATA<sMBLK_GROUP> {

  using VOXEL_VAR_ARRAY = std::array<dFLOAT, N_VOXELS_64>;
  using VOXEL_IC_DATA = std::vector<VOXEL_VAR_ARRAY>;
  using PD_INDEX_MAP = std::unordered_map<sPHYSICS_DESCRIPTOR*, asINT32>;

  //Dynamics data corresponding to mblk headers are overwritten with device info
  //and deleted after initialization. Store unique descriptors assoicated with a group
  //here and m_pd_index per group shob that indexes into it
  std::vector<uint8_t> m_pd_index; //same size as group
  std::vector<sPHYSICS_DESCRIPTOR*> m_pds; //size equal to unique PDs in group
  std::vector<EXPRLANG_VAR_MASK> m_pd_var_mask;//same size as m_pds

public:
  EXPRLANG_MBLK_IC_DATA(sMBLK_GROUP* g): tEXPRLANG_HOST_DATA<sMBLK_GROUP>(g)
  {
    size_t n_shobs = m_group->n_shob_ptrs();
    m_pd_index.reserve(n_shobs);    
    init_var_offsets_and_masks(m_group);
  }

private:
  void fill_data_for_child_ublk(sHMBLK* ublk,
                                int child_ublk,
                                sPHYSICS_DESCRIPTOR* pd,
                                EXPRLANG_VAR_MASK is_ic_space_varying,
                                VOXEL_IC_DATA& voxel_ic_data,
                                bool debug_centroids = false);

  EXPRLANG_VAR_MASK get_exprlang_var_mask_for_mblk(sHMBLK* h_ublk,
                                                   PD_INDEX_MAP& pd_index_map);

  virtual void init_var_offsets_and_masks(sMBLK_GROUP* g) override;  
  virtual void fill(sMBLK_GROUP* g, sPHYSICS_DESCRIPTOR const*const updated_pd=nullptr) override;
};

/*==============================================================================
 * @class EXPRLANG_MSFL_DATA
 *============================================================================*/
class EXPRLANG_MSFL_DATA : public tEXPRLANG_HOST_DATA<sMSFL_GROUP> {
  
  using MSURFEL_VAR_ARRAY = std::array<dFLOAT, N_SFLS_PER_MSFL>;
  using MSURFEL_DATA = std::vector<MSURFEL_VAR_ARRAY>;
  using PD_INDEX_MAP = std::unordered_map<sPHYSICS_DESCRIPTOR*, asINT32>;

  //Store unique descriptors associcated with a group
  //here and m_pd_index per group shob that indexes into it
  std::vector<uint8_t> m_pd_index; //same size as group
  std::vector<sPHYSICS_DESCRIPTOR*> m_pds; //size equal to unique PDs in group
  std::vector<EXPRLANG_VAR_MASK> m_pd_var_mask;//same size as m_pds

public:
  EXPRLANG_MSFL_DATA(sMSFL_GROUP* g): tEXPRLANG_HOST_DATA<sMSFL_GROUP>(g)
  {
    size_t n_shobs = m_group->n_shob_ptrs();
    m_pd_index.reserve(n_shobs);    
    init_var_offsets_and_masks(m_group);
  }
  enum PARAM_TYPE{
    ALL_SHARABLE,
    SPACE_AND_TABLE,
    TIME_AND_SPACE,
    TIME_ONLY,
    SPACE_ONLY
  }m_param_type;
  
  void set_param_type(PARAM_TYPE type) { m_param_type = type; }
private:
  void fill_data_for_child_sfl(sMSFL const *const msfl,
                               int child_sfl,
                               sPHYSICS_DESCRIPTOR* pd,
                               const EXPRLANG_VAR_MASK& is_msfl_data_from_eqn,
                               MSURFEL_DATA& msurfel_data);
  EXPRLANG_VAR_MASK get_exprlang_var_mask_for_msfl(sMSFL* msfl,
                                                   PD_INDEX_MAP& pd_index_map);

  virtual void init_var_offsets_and_masks(sMSFL_GROUP* g) override;  
  virtual void fill(sMSFL_GROUP* g,sPHYSICS_DESCRIPTOR const*const updated_pd) override;
};
/*==============================================================================
 * @struct EXPRLANG_MANAGER
 * Holds all instances of tEXPRLANG_HOST_DATA and provides a convenient way to
 * access their functionality
 *============================================================================*/
struct EXPRLANG_MANAGER {
  struct sUBLK_KEY
  {
    UBLK_GROUP_TYPE group_type;
    int scale;
    int dest_sp;
    friend bool operator<(const sUBLK_KEY& key1, const sUBLK_KEY& key2) {
      return std::tie(key1.group_type, key1.scale, key1.dest_sp) < std::tie(key2.group_type, key2.scale, key2.dest_sp);
    }
  };


  void init_space_varying_initial_conditions(const sUBLK_KEY& key) {
    const auto& ic_data = m_ic_data.at(key);
    if (ic_data) {
      ic_data->fill_and_port_to_device();
    }
  }
  void init_space_varying_initial_conditions(UBLK_GROUP_TYPE group_type,
                                             STP_SCALE scale,
                                             int dest_sp = 0) {
    init_space_varying_initial_conditions({group_type, scale, dest_sp});
  }

  struct sSURFEL_KEY
  {
    SURFEL_GROUP_TYPE group_type;
    int scale;
    int dest_sp;
    friend bool operator<(const sSURFEL_KEY& key1, const sSURFEL_KEY& key2) {
      return std::tie(key1.group_type, key1.scale, key1.dest_sp) < std::tie(key2.group_type, key2.scale, key2.dest_sp);
    }
  };
  void fill_surfel_varying_data(const sSURFEL_KEY& key, sPHYSICS_DESCRIPTOR const*const pd, EXPRLANG_MSFL_DATA::PARAM_TYPE type) {
#ifndef DEBUG
    const auto& sfl_data = m_sfl_data.at(key);
    if (sfl_data) {
      sfl_data->set_param_type(type);
      sfl_data->fill_and_port_to_device(pd);
    }
#else
    if (auto search = m_sfl_data.find(key); search != m_sfl_data.end()) {
      const auto& sfl_data = search->second;
      sfl_data->set_param_type(type);
      sfl_data->fill_and_port_to_device(pd);
    }
    else
    {
      assert(false && "map does not contain requested information\n");
      // printf("SP-%d map DOES NOT contain key group_t %d scale %d dest_sp %d\n", my_proc_id, key.group_type, key.scale, key.dest_sp);
    }
#endif
  }
  void fill_surfel_varying_data(sPHYSICS_DESCRIPTOR const*const pd,
                                SURFEL_GROUP_TYPE group_type,
                                STP_SCALE scale,
                                EXPRLANG_MSFL_DATA::PARAM_TYPE type,
                                int dest_sp = 0
                                ) {
    fill_surfel_varying_data({group_type, scale, dest_sp}, pd, type);
  }
  
  void init();
  
private:
  std::map<sUBLK_KEY, std::unique_ptr<EXPRLANG_MBLK_IC_DATA>> m_ic_data;
  std::map<sSURFEL_KEY, std::unique_ptr<EXPRLANG_MSFL_DATA>> m_sfl_data;
};

extern EXPRLANG_MANAGER g_exprlang_mgr;

#define COMPILE_DEBUG_EXPRLANG_DEVICE_METHODS 0
#ifdef COMPILE_DEBUG_EXPRLANG_DEVICE_METHODS
VOID launch_check_exprlang_device_var_mask(unsigned int msfl_id, int trace_id);
VOID launch_check_exprlang_device_var_value(unsigned int msfl_id, int trace_id);
#if GPU_COMPILER
#include <cuda.h>
#include <cuda_runtime.h>
__global__ VOID
check_exprlang_device_var_mask(unsigned int msfl_id, int trace_id);
__device__ VOID
debug_exprlang_device_var_mask(unsigned int msfl_id, int trace_id);
__global__ VOID 
check_exprlang_device_var_value(unsigned int msfl_id, int trace_id);
__device__ VOID
debug_exprlang_device_var_value(unsigned int msfl_id, int trace_id);
#endif
#endif
}//namespace GPU

#endif //BUILD_GPU
#endif
