/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "status.h"
#include "strands.h"
#include "ublk_process_control.h"
#include "bsurfel_dyn_meas.h"
#include "bsurfel_comm.h"
#include "trajectory_meas.h"
#include "fan.h"
#include "particle_slrf.h"
#include "implicit_shell_solver.h"
#include "implicit_solid_solver.h"
#include "conduction_contact_averaging.h"
#include PHYSICS_H

#define DEBUG_ASM 0

#include "sp_timers.h"
extern VOID read_time_depended_input(const char* input_file);

static VOID solver_switch_seeding(SCALE last_coarsest_active_scale) {
  if(sim.T_solver_type == PDE_TEMPERATURE)
    seed_dyn_surfel_T_lb_scalar_solver();
  asINT32 coarsest_active_scale = last_coarsest_active_scale;
  // INFA1 has only one set of lb_states. Hence the pde or lb solver cannot be seeded just after completing the dynamics
  // for each ublk in INFA1 strand as it will overwrite the data needed for the present TS.

  for (asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK1_GROUP_TYPE) {
      if (group->m_send_group != NULL) {
        group->m_send_group->wait_for_copying_of_send_data();
      }
      DO_UBLKS_OF_GROUP(ublk, group) {
        if (sim.T_solver_type == PDE_TEMPERATURE)
          ublk->seed_T_scalar_solver();
        else
          ublk->seed_T_pde_solver();
        if (ublk->has_mirror()) {
          ACTIVE_SOLVER_MASK active_solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale];
          ACTIVE_SOLVER_MASK prior_solver_index_mask = compute_solver_index_mask(scale);
          reflect_curr_states_to_mirror_ublk(ublk, prior_solver_index_mask);
        }
      }
    }
  }

  g_timescale.switch_T_solver(sim.T_solver_type);
  if (sim.is_prescribed_T_solver_switch && my_proc_id == 0)
    msg_print_no_prefix("Temperature solver is successfully switched to lb scalar solver at timestep %ld.\n",(g_timescale.m_time + 1));
}

static VOID calculate_tbs_interp_index() {
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0)
      sim.transient_boundary_seeding[i].calculate_interpolation_index();
  }
}

static VOID update_powertherm_time()
{
  if (sim.m_pt_time_info.pf_start_time == 0) {// before first coupling, use the first powertherm start time for evaluating equations in terms of t_ptherm
    //g_timescale.m_powertherm_time = sim.m_new_pt_time_info.pt_start_time;
    // If ratio is 0, then we use the pt_start_time; if ratio < 0, then we will extrapolate backward to get the time before pf_start_time which is the first PT solver start time
    if (sim.m_new_pt_time_info.pt_pf_time_ratio < 0) {
      g_timescale.m_powertherm_time = sim.m_new_pt_time_info.pt_start_time - (g_timescale.time_flow() - sim.m_new_pt_time_info.pf_start_time) * sim.m_new_pt_time_info.pt_pf_time_ratio;
    } else
      g_timescale.m_powertherm_time = sim.m_new_pt_time_info.pt_start_time;
  } else {
    g_timescale.m_powertherm_time = sim.m_pt_time_info.pt_start_time 
                                  + (g_timescale.time_flow() - sim.m_pt_time_info.pf_start_time) * sim.m_pt_time_info.pt_pf_time_ratio;
  }
#if DEBUG_T_PTHERM_EQNS
  if (my_proc_id == 0)
    msg_print("g_timescale.m_time %ld pf_start_time %d powertherm_time %f", g_timescale.m_time, sim.m_pt_time_info.pf_start_time, g_timescale.m_powertherm_time);
#endif

  // If delay == 0, then will receive new pt_time_info at the coupling timestep,
  // so should update pt time info one timestep ealier.
  // If delay > 0, then will reeive new pt_time_info after the coupling timestep,
  // and it is OK to update pt time info at the coupling timestep.
  if (g_timescale.time_flow() >= (sim.m_new_pt_time_info.pf_start_time - 1)) {
    sim.m_pt_time_info.pf_start_time = sim.m_new_pt_time_info.pf_start_time;
    sim.m_pt_time_info.pt_start_time = sim.m_new_pt_time_info.pt_start_time;
    sim.m_pt_time_info.pt_pf_time_ratio = fabs(sim.m_new_pt_time_info.pt_pf_time_ratio);  // If the ratio is negative (for steady state), make it positive for the next coupling
  }
}

VOID sTIMESTEP_UPDATE_STRAND::run() {

#if BUILD_5G_LATTICE
  char input_file[2048];
  sprintf(input_file, "user_input.init.%ld", g_timescale.m_time);

  int file_is_not_there = access(input_file, F_OK);
  if (!file_is_not_there)
    read_time_depended_input(input_file);
#endif

  // do bsurfel measurements before sending meas windows and before updating timestep
  if ( sim.is_movb_sim() ) {
    do_all_bsurfel_measurements();
  }

  if (sim.is_particle_model) {
    if (has_trajectory_window()) {
      if (g_trajectory_id_map.remap_at_end_of_this_timestep(g_timescale.next_time_flow())) {
        g_trajectory_id_map.mark_trajectory_global_id_comm_ready();
        g_trajectory_id_map.wait_for_trajectory_global_id_comm_done();
      }
    }
    
    // moves parcels from ublks to surfels
    particle_sim.surface_collision();
    particle_sim.move_parcels_to_new_container();
  }

#if !GPU_COMPILER && !BUILD_GPU
  if (sim.is_conduction_sp && sim.is_shell_conduction_model && sim.use_implicit_shell_solver) {
    start_and_wait_for_implicit_shell_solver(); // This is called by the compute thread
  }
#if !BUILD_5G_LATTICE
  if (sim.is_conduction_sp && sim.is_conduction_model && sim.use_implicit_solid_solver) {
    start_and_wait_for_implicit_solid_solver(); // This is called by the compute thread
    //implicit_solid_solver_update_rhs_vector(); 
  }
#endif
#endif

  // This will update the solver masks to be used during the next to next time step
  if(sim_run_info.run_options & (SIM_REPORT_THREADTIME_VERBOSE)) {
    struct timespec main_thread_timestep_stop_timespec;
    clock_gettime(CLOCK_THREAD_CPUTIME_ID, &main_thread_timestep_stop_timespec);
    asINT64 this_timestep_main_thread_stop_ns = main_thread_timestep_stop_timespec.tv_nsec + main_thread_timestep_stop_timespec.tv_sec * NSEC_PER_SEC;
    
    asINT64 main_thread_ns =  this_timestep_main_thread_stop_ns - g_strand_mgr.last_timestep_main_thread_stop_ns;
    g_strand_mgr.last_timestep_main_thread_stop_ns = this_timestep_main_thread_stop_ns;

    //msg_print("Start %lld, stop %lld, dif %lld",  main_thread_start_ns, main_thread_stop_ns, main_thread_ns);
    dFLOAT main_thread_cputime = dFLOAT(main_thread_ns) / dFLOAT(NSEC_PER_SEC);
    msg_print("Timestep %ld   sim thread time: %g", g_timescale.m_time, main_thread_cputime);
  }

  

  SCALE last_coarsest_active_scale = g_timescale.coarsest_active_scale();
  SCALE coarsest_active_scale = g_timescale.compute_next_coarsest_active_scale(FINEST_SCALE);
  sim.update_ref_frames(last_coarsest_active_scale, coarsest_active_scale);
  
#if BUILD_D19_LATTICE
  if(sim.is_pf_model && last_coarsest_active_scale == 0){
    ccDOTIMES(ctype, N_COLL_TYPES) {
      g_strand_mgr.add_collective_to_queue((COLLECTIVE_TYPE)(ctype + N_RECV_TYPES));
    }
  }
#endif

  if ( sim.is_movb_sim() ) {
    update_all_time_varying_bsurfel_physics_parameters(g_timescale.next_time());
    sim.update_movb_xforms();
  }  
  
  // Done before timestep is updated, the simulation is done executing m_time+1 timesteps.
  // Surface measurements
  meas_do_output(TRUE,g_timescale.next_solver_time(sim.is_conduction_sp));
  meas_do_clear(TRUE,g_timescale.next_solver_time(sim.is_conduction_sp));

  // Fluid measurements
  meas_do_output(FALSE, g_timescale.next_solver_time(sim.is_conduction_sp));
  meas_do_clear(FALSE, g_timescale.next_solver_time(sim.is_conduction_sp));

  if (has_trajectory_window() && sim.is_particle_model) {
    sp_trajectory_startpoint_manager.mark_trajectory_data_ready_to_send();
    sp_trajectory_vertex_manager.mark_trajectory_data_ready_to_send();
    sp_trajectory_hitpoint_manager.mark_trajectory_data_ready_to_send();
    
    sp_trajectory_startpoint_manager.wait_for_previous_data_send();
    sp_trajectory_vertex_manager.wait_for_previous_data_send();
    sp_trajectory_hitpoint_manager.wait_for_previous_data_send();
  }

  // fan comm
  if (n_global_fan_descs > 0) {
    // Should receive data from all SPs before we proceed to the next timestep
    g_strand_mgr.m_fan_comm.request_and_wait_fan_data();
  }

  // If any averaged contact is active in this time-step, it should have completed global reduction and be ready to
  // compute average values. Note that should be done before processing async events so correct averaged heat fluxes go
  // into the checkpoint if requested.
  if (g_thermal_averaged_contacts.is_active()) {
    g_thermal_averaged_contacts.compute_averages();
  }

#if BUILD_D19_LATTICE
  if(sim.thermal_accel.do_T_solver_switch)
    solver_switch_seeding(last_coarsest_active_scale);

  sim.thermal_accel.do_T_solver_switch = FALSE;
#endif
  // This is where the simulation time a.k.a. g_timescale.m_time is advanced. 
  // Note that the timestep counters are still not advanced until complete_timestep is called later.
  g_timescale.timestep_update(FINEST_SCALE);

// Update the index of transient seed data.
  calculate_tbs_interp_index();
  if(sim.prepare_calibration_run) {
    sim.calibration_params.iteration_number++;
    if(sim.is_full_checkpoint_restore)
      sim.is_full_checkpoint_restore = FALSE;
   g_seed_calibration_iteration.m_do_calib_seed_comm = TRUE;
   g_seed_calibration_iteration.process();
   sim.prepare_calibration_run = FALSE;
  }
  g_sync_threads.wake_up_comm_thread();

  sp_timer_stop(SP_TOTAL_RUN_TIMER);

  if (sim.async_event_flags.timers_on_p) {

    g_per_timestep_timer_counters.clear();

    for (asINT32 scale = FINEST_SCALE; scale >= last_coarsest_active_scale; --scale) {
      // Calculate the counters for this timestep using the complete set of counters
      auINT32 odd_timestep_mask = (1 << (sim.num_scales - scale)) - 1;
      BOOLEAN was_timestep_even = ((g_timescale.m_timestep & odd_timestep_mask) != odd_timestep_mask);
      g_per_timestep_timer_counters.calculate(scale, was_timestep_even, g_timer_counters);
    }
#if !ENABLE_SIM_COUNTERS
    // Copy counters from initial counters
    g_per_timestep_timer_counters.copy_to_sp_timer();
#else
    g_per_timestep_timer_counters.print();
    g_per_timestep_timer_counters.validate();
#endif

    // Accumulated timer by summing over several timers. The time is less than the total time of the timestep.
    accum_timer();

    msg_print("Timestep = %ld", g_timescale.m_time);
    sp_timer_report_all_verbose(TIMER_UNITS_US);

    // Reset the timers
    sp_timer_clear_all();

    sp_timer_start(SP_TOTAL_RUN_TIMER);

  }

  g_async_event_queue.process_queue(g_timescale.m_time);

  if (sim.is_ptherm_time_input_to_programs)
    update_powertherm_time();
  
  // this is set only by the compute thread, so we don't need atomics here
  if (!g_strand_mgr.m_exit) {
    update_all_time_varying_surface_physics_parameters();
    update_all_time_varying_shell_physics_parameters();
    update_all_time_varying_fluid_physics_parameters();
    update_all_time_varying_body_force_parameters();

    // bsurfel movement must occur after meas window output to prevent duplication
    if (sim.is_movb_sim()) {
      update_all_bsurfel_positions();
      post_bsurfel_sends();
    }

    if (sim.is_particle_model) {
      // These operations should be skipped this timestep because full
      // checkpoint forces these operations.
      if (!g_strand_mgr.m_full_ckpt_done) {
        particle_sim.queue_parcel_sends();
        particle_sim.wait_for_parcels_tobe_copied_to_send_buffer();
      } else {
        g_strand_mgr.m_full_ckpt_done = false;
      }
      // These operations are carried out during checkpoint resume
      g_particle_subcycling_info.set_particle_solver_mask(g_timescale.time_flow(), -1);
      reset_parcel_index_in_timestep();
#if DEBUG_PARTICLE_SUMMARY
      particle_sim.log_debug_summary();
#endif
      WITH_TIMER(SP_PARTICLE_EMISSION_TIMER) {
        particle_sim.release_parcels();
      }
      WITH_TIMER(SP_PARTICLE_SLRF_EMISSION_TIMER) {
        process_slrf_parcels();
      }
      particle_sim.update_all_virtual_wiper_positions();
    }    
  }

  for (asINT32 scale = FINEST_SCALE; scale >= last_coarsest_active_scale; --scale) {
    g_strand_mgr.update_consumer_counts(scale); // for bsurfel migration
  }


#if BUILD_D19_LATTICE
  if(sim.is_pf_model && last_coarsest_active_scale == 0){
    g_strand_mgr.update_collective_consumer_counts();
  }
#endif
  if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
    g_thermal_averaged_contacts.reset_count();
  }

  //This is where the SP timestep & cross-realm comm counter, a.k.a g_timescale.m_timestep and
  //g_timescale.m_wsurfel_comm_cntr are incremented
  g_strand_mgr.complete_timestep(g_timescale.m_active_solver_masks[g_timescale.time_parity()][FINEST_SCALE]);
  
  SCALE wsurfel_coarsest_active_scale = g_timescale.compute_wsurfel_comm_coarsest_active_scale(FINEST_SCALE);
  for (asINT32 scale = FINEST_SCALE; scale >= wsurfel_coarsest_active_scale; --scale) {
    g_strand_mgr.update_wsurfel_consumer_counts(scale, g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale]);
  }

#ifdef BUILD_GPU
  /*PR - 55485
   * Relocating update of GPU timescale from TIMESCALE::timestep_update() to here
   * Also moving the update of device windows here since it depends on the update of GPU::g_timescale
   *
   * With changes on cond_sm, a new m_timestep member was added to the TIMESCALE
   * struct and we update g_timescale.m_time in TIMESCALE::timestep_update()
   * but update g_timescale.m_timestep in STRAND_MGR::complete_timestep()
   */
  GPU::update_timescale();  
  GPU::update_on_device_windows();
#endif  
}
