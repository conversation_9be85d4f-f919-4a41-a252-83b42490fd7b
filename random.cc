/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("physics.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */ 
#include "common_sp.h"
#include "random.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"


sRANDOM_NUM_GENERATOR g_random_num_generator;

VOID sRANDOM_NUM_GENERATOR::ckpt()
{
  LGI_RANDOM_SEED_REC record;
  asINT32 record_length = sizeof(record) + this->state_size();
  lgi_write_init_tag (&record, LGI_CKPT_RANDOM_SEED_TAG, record_length);
  record.n_bytes_rand_state = this->state_size();
  write_ckpt_lgi_head(record);
  write_ckpt_lgi(this->read_state(), this->state_size());
}

VOID sRANDOM_NUM_GENERATOR::ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t size_rand_state = this->state_size() + sizeof(size_t);
  pio_ckpt_buff.write(&size_rand_state);
  pio_ckpt_buff.write(this->read_state(), this->state_size());
}

VOID sRANDOM_NUM_GENERATOR::read_ckpt()
{
  LGI_RANDOM_SEED_REC record;
  read_lgi_head(record);
  if (record.n_bytes_rand_state != this->state_size()) {
    msg_internal_error("Size of checkpointed random number generator (%i) state is different"
		       "than expected (%lu).", record.n_bytes_rand_state, this->state_size());
  }
  char* state_buffer = xnew char[this->state_size()];
  read_lgi(state_buffer, this->state_size());
  this->restore_state(state_buffer);
  delete[] state_buffer;
}
