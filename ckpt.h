/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Checkpoints
 *--------------------------------------------------------------------------*/

#ifndef __CKPT_H
#define __CKPT_H

#include "common_sp.h"
#include "group.h"
#include "event_queue.h"
#include "shob.h"

// Writing and reading checkpoint files
//
// When writing a checkpoint, the SP must send all data for its lowest numbered ublk,
// followed by all data for its second lowest numbered ublk, etc. The scheme to achieve
// this relies on the fact that ublks appear in every group in order. This is
// guaranteed because we read ublks from the DGF file in ID order and always push a new 
// quantum on the back of a group.
// 
// The data structure that guides writing of a checkpoint is a queue of the groups that
// contain checkpointable data, sorted by the ublk IDs of the next quantum to be checkpointed 
// in each group. Groups with checkpointable data must be derived from sCKPT_GROUP and
// must implement the following methods:
//
//    virtual BOOLEAN ckpt_p()         // for checkpoint at sim.time, will group write any data  
//    virtual SHOB_ID ckpt_next_shob() // returns ID of next shob in the group
//    virtual asINT32 shob_ckpt_len()  // may depend on sim.time, but not vary shob to shob
//
// In addition, groups with checkpointable data must be added to the queue of groups during
// initialization by calling sCKPT_GROUP::insert_in_ublk_ckpt_queue.
//
// The story is similar for surfels.

// g_ckpt_lgi_stream is only exposed for use by write_ckpt_lgi
extern LGI_STREAM g_ckpt_lgi_stream;
extern cBOOLEAN g_is_equilibrium_ckpt;

VOID determine_if_equilibrium_ckpt();         // sets g_is_equilibrium_ckpt

template< typename TYPE >
inline VOID write_ckpt_lgi_head(TYPE &object)
{
  lgi_write_next_head(g_ckpt_lgi_stream, &object, sizeof(object));
}

template< typename TYPE >
inline VOID write_ckpt_lgi_head(TYPE *buf, size_t n_bytes)
{
  lgi_write_next_head(g_ckpt_lgi_stream, buf, n_bytes);
}

template< typename TYPE >
inline VOID write_ckpt_lgi(TYPE &object)
{
  lgi_write(g_ckpt_lgi_stream, &object, sizeof(object));
}

template< typename TYPE >
inline VOID write_ckpt_lgi(TYPE *buf, size_t n_bytes)
{
  lgi_write(g_ckpt_lgi_stream, buf, n_bytes);
}

inline VOID write_ckpt_shob_header(SHOB_ID shob_id, DGF_SHOB_CKPT_LEN len)
{
  cDGF_CKPT_SHOB_HEADER header;
  header.shob_id = shob_id;
  header.len = len;
  header.write(g_ckpt_lgi_stream);
}

// The order of these entries must correspond to the order that ckpt data needs to be read
// in parse_shob_descs.cc during initialization from the DGF file.
enum CKPT_GROUP_PRECEDENCE {
  SHOB_CKPT_PRECEDENCE,
  SHOB_DYN_CKPT_PRECEDENCE,
  V2S_CKPT_PRECEDENCE,
  VR_INTERFACE_CKPT_PRECEDENCE
};

struct sSAMPLING_SURFEL;

typedef struct sCKPT_GROUP : public sGROUP {

  std::vector <sUBLK *>   m_ckpt_ublks[STP_N_REALMS];
  std::vector <sSURFEL *> m_ckpt_surfels[STP_N_REALMS];
  static sCKPT_GROUP *bsurfel_ckpt_group_queue;
  static sCKPT_GROUP *next_bsurfel_ckpt_group_queue; // queue for next ckpt - groups moved here when exhausted

  DGF_SHOB_CKPT_LEN m_shob_ckpt_len; // cache of most recent call to shob_ckpt_len()

  VOID add(sUBLK *ublk, REALM realm);

  VOID add(sSURFEL *surfel, REALM realm);

  VOID add(sSAMPLING_SURFEL *surfel, REALM realm);

  BOOLEAN is_sampling_surfel(sINT32 ns, REALM realm) {
    return ((uINT64) m_ckpt_surfels[realm][ns] & 1);
  }
  sSAMPLING_SURFEL *get_sampling_surfel(sINT32 ns, REALM realm) {
    return (sSAMPLING_SURFEL*) ((uINT64)m_ckpt_surfels[realm][ns] & ~1);
  }

} *CKPT_GROUP;

extern sCKPT_GROUP g_ckpt_group;

VOID sim_checkpoint(BOOLEAN full, BOOLEAN is_avg_mme);

VOID clear_avg_mme_checkpoint_data();

VOID add_initial_checkpoints_to_event_queue();

#endif /* __CKPT_H */

