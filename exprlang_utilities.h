#ifndef EXPRLANG_UTILITIES_H
#define EXPRLANG_UTILITIES_H

//@var N_MAX_EXPRLANG_VARS
//Maximum number of allowed bits to represent which variables associated with a
//physics descriptor are required to be evaluated with EXPRLANG as captured by
//the mask EXPRLANG_VAR_MASK.
constexpr size_t N_MAX_EXPRLANG_VARS = 64;

//Per mega shob variable mask. A conveient way to figure out if a variable should
//be read from the exprlang device buffer or from the physics descriptor
//If a child shob in a mega shob does not have a space or time varying physics
//descriptor, but others do, we still read from the exprlang device buffer which
//is basically an all or none concept at this point
template<size_t N, typename INTERNAL_TYPE>
class tBITSET;
using EXPRLANG_VAR_MASK = tBITSET<N_MAX_EXPRLANG_VARS>;

#endif