/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * All exported definitions from simulation engine
 *
 * Jim Salem, Exa Corporation 
 * Created Mon Feb 14 1994
 *--------------------------------------------------------------------------*/

#ifndef __SIMENG_SIMENG_H
#define __SIMENG_SIMENG_H

#include "common_sp.h"

#if GPU_COMPILER
#include "gpu_shobs.hcu"
#include "gpu_globals.hcu"
#include "gpu_gather_advect.hcu"
#endif

#include "lattice.h"
#include "shob.h"
#include "ublk.h"
#include "surfel.h"
#include "bsurfel.h"
#include "sampling_surfel.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"
#include "bsurfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "surfel_advect_sp.h"
#include "sp_timers.h"
#include "random.h"
#include "seed_sp.h"
#include "fan.h"
#include "boltz_diags.h"
#include "gradient.h"
#include "phys_type_map.h"
#include "meas.h"
#include "gpu_meas.h"
#include "dgf_reader_sp.h"
#include "box_advect.h"
#include "mirror.h"
#include "sim.h"
#include "logging.h"
#include "particle_sim.h"
#include "bsurfel_util.h"
#include "particle_meas_dcache.h"
#include "mme_ckpt.h"
#include "conduction_contact_averaging.h"
#include "implicit_shell_solver.h"

#endif /* __SIMENG_SIMENG_H */

