/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Zachary Mills, Dassault Systemes
 * Created September 1, 2023
 *--------------------------------------------------------------------------*/

#include "sim.h"
#include "surfel.h"
#include "shob_groups.h"
#include "conduction_solver_stencils.h"
#include "surfel_vertices.h"
#include "conduction_data.h"
#include "comm_groups.h"
#include "phys_type_map.h"
#include "voxel_dyn_sp.h"
#include "mlrf.h"
#include "common_sp.h"
#include "implicit_shell_solver.h"
#include <algorithm>
#include PHYSICS_H


inline namespace SIMULATOR_NAMESPACE {
cSHELL_CONDUCTION_STENCIL_INFO g_shell_conduction_edges;
}


template <typename sPHYSICS_DESCRIPTOR_TYPE>
cEDGE_BOUNDARY_DYNAMICS_DATA<sPHYSICS_DESCRIPTOR_TYPE>::cEDGE_BOUNDARY_DYNAMICS_DATA(sSURFEL* owner_surfel, 
                                                                                     sSURFEL* nbr_surfel,
                                                                                     const sdFLOAT scaled_norm_dist,
                                                                                     const sdFLOAT* norm_vec_local) {
  
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data = nbr_surfel->dynamics_data();
  m_edge_physics_desc = (sPHYSICS_DESCRIPTOR_TYPE*)(dyn_data->physics_descriptor());

  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = owner_surfel->shell_conduction_data(); 
  surfel_conduction_data->rotate_to_global_coordinates(m_edge_normal, norm_vec_local);
  // shouldnt need to unitize, but doing so just in case precision issues cause any scaling
  vunitize(m_edge_normal);

  sdFLOAT unscaled_norm_dist = scaled_norm_dist * scale_to_voxel_size(owner_surfel->scale());
  sdFLOAT vec_to_edge_local[3];
  vscale(vec_to_edge_local, unscaled_norm_dist, m_edge_normal);
  vcopy(m_edge_center, owner_surfel->centroid);
  vinc(m_edge_center, vec_to_edge_local);
  // Initializes physics parameters for this edge location if it is space varying
  init_physics_params();
}

template <typename sPHYSICS_DESCRIPTOR_TYPE>
BOOLEAN cEDGE_BOUNDARY_DYNAMICS_DATA<sPHYSICS_DESCRIPTOR_TYPE>::update_varying_physics_params() {
  return update_time_and_space_varying_surfel_physics_parms(m_edge_center, m_edge_normal,m_edge_physics_desc); 
}

template <typename sPHYSICS_DESCRIPTOR_TYPE>
VOID cEDGE_BOUNDARY_DYNAMICS_DATA<sPHYSICS_DESCRIPTOR_TYPE>::init_physics_params() {
  m_edge_physics_desc->eval_space_and_table_varying_parameter_program(m_edge_center, m_edge_normal,
                                                                      boundary_eqn_error_handler); 
}

template class cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR>;
template class cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR>;
template class cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR>;

/**
 @brief   Constructor for cCONDUCTION_EDGE_BASE.

 @details The geometric information is from the cSHELL_MESH::HALF_EDGE corresponding
          to this surfel in the edge ring and is already scaled and rotated to the surfels 2D csys.
*/
cCONDUCTION_EDGE_BASE::cCONDUCTION_EDGE_BASE(sSURFEL* owner_surfel, dFLOAT e_length,
  sdFLOAT norm_dist, const sdFLOAT* norm_vec) : m_owner_surfel(owner_surfel),
    m_length(e_length), m_normal_dist(norm_dist), m_last_update(-1) {

  vcopy2(m_normal, norm_vec);

  // defaults to owner's scale, but will be updated if necessary in derived classes constructors
  set_update_scale(m_owner_surfel->scale());
}

/**
 @brief   Returns unscaled length of the edge
*/
dFLOAT cCONDUCTION_EDGE_BASE::unscaled_length() {
  return m_length * scale_to_voxel_size(m_owner_surfel->scale());
}

/**
 @brief   Returns unscaled length normal distance from surfel centroid to the edge
*/
sdFLOAT cCONDUCTION_EDGE_BASE::unscaled_normal_distance() {
  return m_normal_dist * scale_to_voxel_size(m_owner_surfel->scale());
}

/**
 @brief   Constructor for cCONDUCTION_EDGE_INTERNAL.
*/
cCONDUCTION_EDGE_INTERNAL::cCONDUCTION_EDGE_INTERNAL(sSURFEL* owner_surfel, dFLOAT e_length,
  sdFLOAT norm_dist, const sdFLOAT* norm_vec, sSURFEL* nbr_surfel, BOOLEAN is_switched)
    : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec), m_nbr_surfel(nbr_surfel),
      m_is_switched(is_switched) {
  // Update scale needs to be the finest of non-ghost surfels since a ghost surfel will not be calling
  // compute_face_fluxes
  asINT32 own_scale = (m_owner_surfel->is_ghost()) ? -1 : m_owner_surfel->scale();
  asINT32 nbr_scale = (m_nbr_surfel->is_ghost()) ? -1 : m_nbr_surfel->scale();
  set_update_scale(MAX(own_scale, nbr_scale));

  cassert(m_owner_surfel->id() < m_nbr_surfel->id());

  cassert(m_owner_surfel->shell_conduction_data()->num_layers()
             ==  m_nbr_surfel->shell_conduction_data()->num_layers());
}

/**
 @brief   Sets the unfolded vector between centroids and rotation angle.

 @details Because the internal edge classes can be initialized when creating the neighbor surfel stencils,
          the rotation andle and unfolded vector are provided separate from the constructor to ensure that
          the owner surfel sets these values.
*/
VOID cCONDUCTION_EDGE_INTERNAL::set_unfolded_vec_and_rotation(const dFLOAT* owner_unfold_centroid,
                                                              const dFLOAT rotation_angle) {
  vcopy2(m_unfolded_centroid, owner_unfold_centroid);
  m_rotation_angle = rotation_angle;

  // Rotate vector from owner to nbr. The m_rotation_angle gives the angle needed to rotate a vector from the nbr to the
  // owner. So -m_rotation_angle will give a rotation from the owner to the nbr.
  vrotate2(m_nbr_normal, -rotation_angle, m_normal); 
  vneg2(m_nbr_normal, m_nbr_normal);

  vrotate2(m_nbr_unfolded_centroid, -rotation_angle, m_unfolded_centroid);
  vneg2(m_nbr_unfolded_centroid, m_nbr_unfolded_centroid);
}


/**
 @brief Calculates surface normal gradient at face
 @details Computes and returns the surface normal gradient at face given by:
          
          \f$\nabla_{n} T_f=\mathbf{\nabla T_{f,corr}} \cdot \mathbf{\hat{n}_{f}}\f$

          Here, \f$\mathbf{\hat{n}_{f}}\f$ is the face unit normal and \f$\mathbf{\nabla T_{f,corr}}\f$ is the 
          gradient at the face with a limited orthogonal correction applied:

          \f$\mathbf{\nabla T_{f,corr}}=\mathbf{\nabla T_{f}} + \Phi_{OC} \frac{\mathbf{ds}}{\mathbf{ds} 
          \cdot \mathbf{ds}}\left[ \left( T_{nbr} - T_{own} \right)- \mathbf{\nabla T_{f}} \cdot \mathbf{ds}\right]\f$

          Here, \f$T_{own}\f$ is the owner cell temperature, \f$T_{nbr}\f$ is the neighbor cell temperature, 
          \f$\mathbf{\nabla T_{f}}\f$ is the gradient at the face interpolated from the cell centers, \f$\mathbf{ds}\f$
          is the vector from the cell center to the neighbor cell center, and \f$\Phi_{OC}\f$ is a limiter to
          limit the contribution of the non-orthogonal term. This limiter, which has a value between 0 and 1
          is based on the area of the smaller cell and is given by:

          \f$\Phi_{OC} = \phi_{OC} \frac{\min\left(A_{own},A_{nbr}\right) - A_{lim,min}} {A_{lim,max} - A_{lim,min}}\f$

          Here, \f$phi_{OC}\f$ is the default value of the limiter set using pfc_shell_orthogonal_correction_limiter
          (defaults to 1.0), \f$A_{own}\f$ and \f$A_{nbr}\f$ are the areas of the owner and neighbor cells
          (scaled to their respective VR levels), \f$A_{lim,min}\f$ is the minimum area below which no
          orthogonal correction is applied set using pfc_shell_orthogonal_correction_limiter_min_area (defaults
          to 0.0), and \f$A_{lim,max}\f$ is the maximum area above which the full value of the correction term is
          applied set using pfc_shell_orthogonal_correction_limiter_max_area (defaults to 1.0). The reason for
          including the area into the limiting calculation is due to the low accuracy associated with the
          passthrough implementation. As the area of the cells decreases and the reliance on passthrough increases,
          the orthogonal correction becomes unreliable. Therefore, limiting its contribution as the
          cell sizes decrease is necessary to maintain accuracy and avoid stability issues arising from the
          large variations in cell temperatures that can occur when using the passthrough method.

          Note: The area of the cells used in the calculation of \f$Phi_{OC}\f$ are scaled to each cell's
          VR level rather than the VR level of the owner cell for two reasons:
            1) This ensures that the same value of \f$Phi_{OC}\f$ is used when the edge is split across SPs
            and avoids a loss of conservation.
            2) The need to limit the orthogonal correction is based on the effects of the passthrough method and
            the use of the passthrough method is directly related to the scaled area of the cells. 
*/
sdFLOAT cCONDUCTION_EDGE_INTERNAL::calculate_sn_grad(const sdFLOAT grad_t_edge[2], const sdFLOAT T_owner,
    const sdFLOAT T_nbr) {

  sdFLOAT min_area = MIN(m_owner_surfel->area, m_nbr_surfel->area);
  sdFLOAT area_ratio = (min_area - g_pfc_shell_orthogonal_correction_limiter_min_area)
      / (g_pfc_shell_orthogonal_correction_limiter_max_area - g_pfc_shell_orthogonal_correction_limiter_min_area + SDFLOAT_EPSILON);
  sdFLOAT ortho_corr_limiter = (MIN(MAX(area_ratio, 0.0), 1.0))*g_pfc_shell_orthogonal_correction_limiter;

  const sdFLOAT* ds = this->get_unfolded_centroid();
  const sdFLOAT* n_hat = this->local_normal();
  sdFLOAT ds_dot_nhat_div_ds2 = vdot2(ds, n_hat)/vlensqrd2(ds);
  
  sdFLOAT ortho_corrector = ds_dot_nhat_div_ds2 * ((T_nbr - T_owner) - (vdot2(grad_t_edge, ds)));

  sdFLOAT gradT_dot_nhat = vdot2(grad_t_edge,n_hat);

  return gradT_dot_nhat + ortho_corr_limiter * ortho_corrector;
}

/**
 @brief Calculates flux across edge

 @details Computes flux at edge, \f$J_{face}\f$, given by:
          
          \f$J_{face}=\left(\mathbf{\overline{\overline{\kappa_{}}}}_{face}\cdot\mathbf{\nabla}T_{face}\right)\cdot
            \mathbf{A}_{face}\f$

          Here, \f$\mathbf{\overline{\overline{\kappa_{}}}}_{face}\f$ is the thermal conductivity,
          \f$\mathbf{\nabla}T_{face}\f$ is the thermal gradient, and \f$\mathbf{A}_{face}\f$ is the face area vector.
          To make use of orthogonal correction and limiting, the normal and parallel contributions of the conductivity
          tensor are calculated separately. The normal contribution to the flux, \f$J_{\bot}\f$, is calculated
          according to:

          \f$J_{\bot}=\left(\left(\mathbf{A}_{face}\cdot\mathbf{\overline{\overline{\kappa_{}}}}_{face}\right)
              \cdot\mathbf{\hat{n}}_{face}\right)\nabla_{n} T_{face}\f$

          Where \f$\mathbf{\hat{n}}_{face}\f$ is the face unit normal, and \f$\nabla_{n} T_{face}\f$ is the
          surface normal gradient.

          The parallel contribution to the flux, \f$J_{\parallel}\f$, is calculated with:

          \f$J_{\parallel}=\left(\left(\mathbf{A}_{face}\cdot\mathbf{\overline{\overline{\kappa_{}}}}_{face}\right) -
            \left(\left(\mathbf{A}_{face}\cdot\mathbf{\overline{\overline{\kappa_{}}}}_{face}\right)
          \cdot\mathbf{\hat{n}}_{face}\right)\mathbf{\hat{n}}_{face}\right)\cdot\mathbf{\nabla} T_{face}\f$
          
          Here, \f$\mathbf{\nabla} T_{face}\f$ is the gradient interpolated to the face from the edge centers.

          The function returns the sum of the normal and parallel contributions.
*/
template <asINT32 SCALE_DIFF>
sdFLOAT cCONDUCTION_EDGE_INTERNAL::compute_layer_edge_flux(SHELL_LAYER_DATA owner_layer, SHELL_LAYER_DATA nbr_layer,
    const sdFLOAT *kappa_edge, const sdFLOAT nbr_area, const asINT32 time_index, const asINT32 nbr_time_index) {
  sdFLOAT grad_t_edge[2];
  sdFLOAT grad_t_nbr[2];
  nbr_layer->rotated_scaled_grad_t<SCALE_DIFF>(grad_t_nbr, m_rotation_angle, nbr_time_index);

  ccDOTIMES(i, 2) {
    grad_t_edge[i] = compute_arithmetic_mean<sdFLOAT>(owner_layer->grad_t[time_index][i], grad_t_nbr[i],
                                                      nbr_area, m_owner_surfel->area);
  }

  sdFLOAT T_owner = owner_layer->temp(time_index);
  sdFLOAT T_nbr = nbr_layer->temp(nbr_time_index);
  sdFLOAT surface_normal_grad = calculate_sn_grad(grad_t_edge, T_owner, T_nbr);
  
  const sdFLOAT edge_area = owner_layer->scaled_interfacial_area<SCALE_DIFF>(length(), nbr_layer,
      m_owner_surfel->scale());

  const sdFLOAT* edge_normal = this->local_normal();

  // Get kappa normal and parallel to face
  sdFLOAT area_dot_kappa[2] = {edge_area*(edge_normal[0]*kappa_edge[0]+edge_normal[1]*kappa_edge[1]),
                               edge_area*(edge_normal[0]*kappa_edge[1]+edge_normal[1]*kappa_edge[2])};

  sdFLOAT area_dot_kappa_normal = vdot2(area_dot_kappa,edge_normal);

  sdFLOAT area_dot_kappa_parallel[2] = {area_dot_kappa[0] - area_dot_kappa_normal*edge_normal[0],
                                        area_dot_kappa[1] - area_dot_kappa_normal*edge_normal[1]};

  sdFLOAT flux = area_dot_kappa_normal*surface_normal_grad;
  sdFLOAT flux_correction = vdot2(area_dot_kappa_parallel,grad_t_edge);

  return flux + flux_correction;
}

#if !GPU_COMPILER && !BUILD_GPU
sdFLOAT cCONDUCTION_EDGE_INTERNAL::compute_implicit_solver_layer_edge_flux(SHELL_LAYER_DATA root_layer_data, SHELL_LAYER_DATA nbr_layer_data, const sdFLOAT *kappa_edge, 
    const sdFLOAT root_surfel_area, const sdFLOAT nbr_surfel_area, const asINT32 time_index, const asINT32 nbr_time_index, STATE_VECTOR_INDEX column_index, 
    asINT32 root_surfel_scale, BOOLEAN do_nbr_layer, sdFLOAT flux_correction_coeff, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT rotation_angle, 
    sdFLOAT root_scale_factor, sdFLOAT nbr_scale_factor, sdFLOAT edge_length_finest_scale) {

  // Get the stencil gradient coefficients for the root surfel
  sdFLOAT grad_t_coeff[2];
  if (do_nbr_layer) {
    nbr_layer_data->rotated_grad_t(grad_t_coeff, rotation_angle, time_index, column_index);
  } else {
    ccDOTIMES(i, 2) {
      grad_t_coeff[i] = root_layer_data->layer_matrix_data.stencil_coeffs[i][column_index];
    }
  }

  sdFLOAT sum_of_areas = root_surfel_area + nbr_surfel_area;
  sdFLOAT grad_coeff_avg_weight = nbr_surfel_area;
  if (do_nbr_layer) {
    grad_coeff_avg_weight = root_surfel_area;
  }
  grad_coeff_avg_weight = grad_coeff_avg_weight / sum_of_areas;

  sdFLOAT grad_t_edge_coeff[2];
  ccDOTIMES(i, 2) {
    grad_t_edge_coeff[i] = grad_coeff_avg_weight * grad_t_coeff[i];
  }
  
  const sdFLOAT edge_area = root_layer_data->scaled_interfacial_area(edge_length_finest_scale, nbr_layer_data, root_scale_factor, nbr_scale_factor);

  sdFLOAT area_vector[2];
  vscale2(area_vector, edge_area, edge_normal);

  // Over-relaxed or orthogonal correction
  sdFLOAT grad_dot_ds = vdot2(grad_t_edge_coeff, unfolded_centroid_finest_scale);
  sdFLOAT correction_to_grad_t = flux_correction_coeff * grad_dot_ds;

  sdFLOAT kappa_dot_grad_t[2];
  stdotv2(kappa_dot_grad_t, kappa_edge, grad_t_edge_coeff);

  sdFLOAT flux;
  flux = vdot2(kappa_dot_grad_t, area_vector); 

  flux = -flux + correction_to_grad_t; // J = -KG*A + (Kp*A)(G*ds) 

  return flux;
}

sdFLOAT cCONDUCTION_EDGE_INTERNAL::implicit_solver_flux_correction_coeff(sSURFEL* root_surfel, sSURFEL* nbr_surfel, SHELL_LAYER_DATA root_layer, SHELL_LAYER_DATA nbr_layer, 
    const sdFLOAT *kappa_edge, asINT32 root_surfel_scale, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT root_scale_factor, sdFLOAT nbr_scale_factor, 
    sdFLOAT edge_length_finest_scale, bool cell_is_bad, bool cell_has_anti_diffusion, bool cell_is_tiny) {

  const sdFLOAT edge_area = root_layer->scaled_interfacial_area(edge_length_finest_scale, nbr_layer, root_scale_factor, nbr_scale_factor);

  sdFLOAT area_vector[2];
  vscale2(area_vector, edge_area, edge_normal);

  sdFLOAT kappa_dot_area[2];
  stdotv2(kappa_dot_area, kappa_edge, area_vector);

  sdFLOAT normal_kappa_area = vdot2(edge_normal, kappa_dot_area);

  sdFLOAT corrector_OR, corrector_OC;

  corrector_OR = vdot2(area_vector, edge_normal) / (vdot2(unfolded_centroid_finest_scale, area_vector) + SDFLOAT_EPSILON);
  corrector_OC = vdot2(unfolded_centroid_finest_scale, edge_normal) / (vdot2(unfolded_centroid_finest_scale, unfolded_centroid_finest_scale) + SDFLOAT_EPSILON);

  sdFLOAT corrector = corrector_OR;

  if (cell_is_bad && g_shell_implicit_solver_use_protectors) {
      corrector = SDFLOAT_EPSILON;
  }

  sdFLOAT flux_correction_coeff;
  flux_correction_coeff = corrector * normal_kappa_area;

  return flux_correction_coeff; // no signs, these will be incorporated at assembly time
}

sdFLOAT cCONDUCTION_EDGE_INTERNAL::implicit_solver_assemble_rhs_from_grad_stencil(SHELL_LAYER_DATA root_layer_data, SHELL_LAYER_DATA nbr_layer_data, SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil, 
    const sdFLOAT *kappa_edge, const sdFLOAT root_surfel_area, const sdFLOAT nbr_surfel_area, const asINT32 time_index, const asINT32 nbr_time_index, 
    asINT32 root_surfel_scale, BOOLEAN do_nbr_layer, sdFLOAT flux_correction_coeff, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT rotation_angle, 
    sdFLOAT root_scale_factor, sdFLOAT nbr_scale_factor, sdFLOAT edge_length_finest_scale) {

  const sdFLOAT* stencil_surfel_lsq_coeff = nbr_stencil->get_lsq_coeff();

  // Scale the stencil coefficients. As passed in, the scale factors are for length scales, but the stencil coefficients
  // have units of 1/length so we need to invert the original scale factors.
  sdFLOAT gradient_scale_factor = 1.0 / root_scale_factor;
  if (do_nbr_layer) {
    gradient_scale_factor = 1.0 / nbr_scale_factor;
  }

  sdFLOAT grad_t_coeff[2];
  ccDOTIMES(i, 2) {
    grad_t_coeff[i] = stencil_surfel_lsq_coeff[i] * gradient_scale_factor;
  }

  if (do_nbr_layer) {
    nbr_layer_data->rotated_grad_t(grad_t_coeff, rotation_angle);
  }

  sdFLOAT sum_of_areas = root_surfel_area + nbr_surfel_area;
  sdFLOAT grad_coeff_avg_weight = nbr_surfel_area;
  if (do_nbr_layer) {
    grad_coeff_avg_weight = root_surfel_area;
  }
  grad_coeff_avg_weight = grad_coeff_avg_weight / sum_of_areas;

  sdFLOAT grad_t_edge_coeff[2];
  ccDOTIMES(i, 2) {
    grad_t_edge_coeff[i] = grad_coeff_avg_weight * grad_t_coeff[i];
  }

  const sdFLOAT edge_area = root_layer_data->scaled_interfacial_area(edge_length_finest_scale, nbr_layer_data, root_scale_factor, nbr_scale_factor);

  sdFLOAT area_vector[2];
  vscale2(area_vector, edge_area, edge_normal);

  sdFLOAT grad_dot_ds = vdot2(grad_t_edge_coeff, unfolded_centroid_finest_scale);
  sdFLOAT correction_to_grad_t = flux_correction_coeff * grad_dot_ds;

  sdFLOAT kappa_dot_grad_t[2];
  stdotv2(kappa_dot_grad_t, kappa_edge, grad_t_edge_coeff);

  sdFLOAT flux;
  flux = vdot2(kappa_dot_grad_t, area_vector);

  flux = -flux + correction_to_grad_t; // J = -KG*A + (Kp*A)(G*ds) 

  // The following term is either a temperature or a flux term depending on the type of BC. It multiplies the secondary
  // gradient contribution.
  sdFLOAT boundary_contribution;
  if (do_nbr_layer) {
    boundary_contribution = nbr_stencil->implicit_solver_get_boundary_contribution(nbr_layer_data, nbr_time_index, edge_normal, nbr_scale_factor);
  } else {
    boundary_contribution = nbr_stencil->implicit_solver_get_boundary_contribution(root_layer_data, time_index, edge_normal, root_scale_factor);
  }
  return -boundary_contribution * flux; // negative b/c it has to go on the RHS
}

#endif //!GPU_COMPILER && !BUILD_GPU

/**
 @brief   returns the layer data from the neighbor shell surfel taking into account if the layers are flipped
*/
SHELL_LAYER_DATA cCONDUCTION_EDGE_INTERNAL::get_nbr_layer(const asINT32 nth_layer) {
  return m_nbr_surfel->shell_conduction_data()->layer(this->neighbor_layer_index(nth_layer));
}

/**
 @brief   Returns the index of the neighboring shell layer based on the value of m_is_switched
*/
asINT32 cCONDUCTION_EDGE_INTERNAL::neighbor_layer_index(const asINT32 nth_layer) {
  if (m_is_switched) {
    return m_owner_surfel->shell_conduction_data()->num_layers() - nth_layer - 1;
  } else {
    return nth_layer;
  }
}

/**
 @brief Applies passed-through energy to each cell sharing the edge

 @details Calculates the energy passing through the edge based on the excess energy calculated
          in the previous timestep using the passthrough method. The amount of energy passed by each cells
          is given by:
          
          \f$ dE_{cell} = D_{opp,edge}\Delta E_{opp}\f$

          where \f$\dE_{cell}\f$ is energy being passed across the edge from the opposite cell,
          \f$\Delta E_{opp}\f$ is the total amount of excess energy in the opposite cell calculated in the
          previous time step, and \f$\D_{opp,edge}\f$ is the distribution factor applied to the opposite
          cell at this edge.
          
          The distribution factor can be calculated two different ways by setting the value of
          pfc_shell_passthrough_distribution to 3 or 4. With pfc_shell_passthrough_distribution = 3 it
          is calculated according to:
          
          \f$ D_{opp,edge} = \frac{\kappa_{n,edge} A_{opp}} {\sum_{e}^{edges} \left(\kappa_{n,e} A_{eNbr}\right)}\f$

          Here, \f$\kappa_{n,edge}f$ is the cell's conductivity normal to this edge, \f$A_{opp}\f$ is the area
          of the opposite cell at this edge, \f$\kappa_{n,edge}f$ is the cell's conductivity normal 
          edge \f$e\f$ and \f$A_{eNbr}\f$ is the area of the neighboring cell at edge \f$e\f$.

          With pfc_shell_passthrough_distribution = 4, the distribution factor is calculated according to:
          
          \f$ D_{opp,edge} = \frac{A_{opp}}{\sum_{e}^{edges} A_{eNbr}}\f$

          The energy being passed by each cell can be average before being accumulated in each. This is enabled
          using pfc_shell_passthrough_averaging_on. When enable, if the energies have differing signs, their 
          sum is accumulated in the cell with the smaller absolute value. If the energies have the same sign, 
          their sum will be distributed according to the relative areas of each cell. If either is zero, the
          corresponding cell will receive all the energy from its neighbor.
*/
VOID cCONDUCTION_EDGE_INTERNAL::apply_passthrough(SHELL_LAYER_DATA owner_layer, SHELL_LAYER_DATA nbr_layer,
    const sdFLOAT *conductivity_at, const sdFLOAT *conductivity_nbr, const asINT32 time_index) {
  // dont do anything if passthrough is turned off
  if (!g_pfc_shell_passthrough_on) { return; }
  
  const sdFLOAT* edge_normal = this->local_normal();

  // calculate value passing from the nbr to the owner layer
  // conductivity_nbr has already been rotated to the owner's local csys, so edge_normal is in the correct direction
  sdFLOAT nbr_to_owner = m_owner_surfel->area;
  if (g_pfc_shell_passthrough_distribution == 4) {
    nbr_to_owner *= symmTensorDoubleDotVec2(conductivity_nbr, edge_normal);
  }
  nbr_to_owner *= nbr_layer->passthrough_source[time_index];

  // calculate value passing from the owner to the nbr
  sdFLOAT owner_to_nbr = m_nbr_surfel->area;
  if (g_pfc_shell_passthrough_distribution == 4) {
    owner_to_nbr *= symmTensorDoubleDotVec2(conductivity_at, edge_normal);
  }
  owner_to_nbr *= owner_layer->passthrough_source[time_index];

  // Calculate average if flag enabled
  if (g_pfc_shell_passthrough_averaging_on) {
    sdFLOAT prod_flux = nbr_to_owner * owner_to_nbr;
    if (prod_flux < 0.0) {
      sdFLOAT sum_flux = nbr_to_owner + owner_to_nbr;
      // we can either just add the smaller magnitude flux to the larger one, passing the result
      // in the same direction as the large magnitude value
      if ((nbr_to_owner * sum_flux) > 0.0) {
        owner_layer->accumulate_dE(sum_flux);
      } else {
        nbr_layer->accumulate_dE(sum_flux);
      }
      // or we can proportion it according to area
      // return m_owner_surfel->area * sum_flux / (m_owner_surfel->area+m_nbr_surfel->area);
    } else if (prod_flux > 0.0) {
      sdFLOAT sum_flux = nbr_to_owner + owner_to_nbr;
      sdFLOAT area_ratio = m_owner_surfel->area / (m_owner_surfel->area+m_nbr_surfel->area);
      owner_layer->accumulate_dE(area_ratio*sum_flux);
      nbr_layer->accumulate_dE((1. - area_ratio) * sum_flux);
    } else {
      owner_layer->accumulate_dE(nbr_to_owner);
      nbr_layer->accumulate_dE(owner_to_nbr);
    }
  } else {
  // Otherwise just accumulate in each
    owner_layer->accumulate_dE(nbr_to_owner);
    nbr_layer->accumulate_dE(owner_to_nbr);
  }
}

/**
 @brief   Interpolates conductivities at cell centers to edge.

 @details If CONDUCTION_SHELL_HARMONIC_MEAN_FOR_CONDUCTIVITY is defined, will use the get_face_avg_value
          function, which computes the harmonic mean when both values have the same sign and arithmetic mean
          when the signs are opposite. A distance of 0.5 is used for both cells in this interpolation, regardless
          of their actual distances to the edge. This is appropriate for isotropic conductivities, but should not
          be used when the conductivity is anisotropic.

          If CONDUCTION_SHELL_HARMONIC_MEAN_FOR_CONDUCTIVITY is not defined, will perform arithmetic mean interpolation
          using the cell sizes as weight factors. This is the appropriate method for anisotropic conductivity
          as harmonic mean interpolation should never be used for vectors.

          Later implementations should use distinguish between anisotropic and isotropic conductivity and
          use the correct implemntation based on this information rather than relying on a macro that applies
          the same method to all calculations regardless of conductivity properties.
*/
VOID cCONDUCTION_EDGE_INTERNAL::compute_edge_conductivity(sdFLOAT kappa_edge[3], const sdFLOAT *kappa_at,
    const sdFLOAT* kappa_nbr, const sdFLOAT nbr_area) {
#ifdef CONDUCTION_SHELL_HARMONIC_MEAN_FOR_CONDUCTIVITY
  // Compute edge-average conductivity
  const sdFLOAT dist_to_edge = 0.5; // equal weights to both for conductivity face averaging
  ccDOTIMES(i,3) {
    kappa_edge[i] = get_face_avg_value(kappa_at[i], kappa_nbr[i], dist_to_edge, dist_to_edge);
  }
#else
  sdFLOAT interp_coeff = m_owner_surfel->area / (m_owner_surfel->area + nbr_area);
  sdFLOAT nbr_multiplier = (1.0 - interp_coeff);
  ccDOTIMES(i,3) {
    kappa_edge[i] = kappa_at[i]*interp_coeff + kappa_nbr[i]*nbr_multiplier;
  }
#endif
}


#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_INTERNAL::compute_implicit_solver_edge_conductivity(sdFLOAT kappa_edge[3], const sdFLOAT *kappa_at,
    const sdFLOAT* kappa_nbr, const sdFLOAT owner_area, const sdFLOAT nbr_area) {
#ifdef CONDUCTION_SHELL_HARMONIC_MEAN_FOR_CONDUCTIVITY
  // Compute edge-average conductivity
  const sdFLOAT dist_to_edge = 0.5; // equal weights to both for conductivity face averaging
  ccDOTIMES(i,3) {
    kappa_edge[i] = get_face_avg_value(kappa_at[i], kappa_nbr[i], dist_to_edge, dist_to_edge);
  }
#else
  sdFLOAT interp_coeff = owner_area / (owner_area + nbr_area);
  sdFLOAT nbr_multiplier = (1.0 - interp_coeff);
  ccDOTIMES(i,3) {
    kappa_edge[i] = kappa_at[i]*interp_coeff + kappa_nbr[i]*nbr_multiplier;
  }
#endif
}
#endif

/**
 @brief   Returns the scaled area of the edge on the neighbor surfel's side.
*/
template <asINT32 SCALE_DIFF>
sdFLOAT cCONDUCTION_EDGE_INTERNAL::scaled_nbr_area() {
  return m_nbr_surfel->area * twoLambdaRatio<SCALE_DIFF>() * twoLambdaRatio<SCALE_DIFF>();
}

/**
 @brief   Computes face fluxes by calling the correct templated compute_face_fluxes_func.
*/
VOID cCONDUCTION_EDGE_INTERNAL::compute_face_fluxes(const asINT32 time_index) {
  if (this->is_updated()) { return; }
  const asINT32 scale_diff = m_nbr_surfel->scale() - m_owner_surfel->scale();
  switch (scale_diff) {
    case 0:
      compute_face_fluxes_func<0>(time_index);
      break;
    case -1:
      compute_face_fluxes_func<-1>(time_index);
      break;
    default:
      compute_face_fluxes_func<1>(time_index);
  }
  this->store_update_time();
}

#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_INTERNAL::compute_implicit_solver_face_fluxes(SURFEL root_surfel) {
  // This function doesn't really do anything anymore. Keeping it around for now for the sake of the structure of the
  // code. Will probably just get rid of it in the future.

  compute_implicit_solver_face_fluxes_func(root_surfel);

}
#endif

/**
 @brief   Returns the edge normal rotated to nbr_surfel's local csys

 @details Normal is in owner surfel's 2D csys and needs to be rotated for the nbr surfel.
*/
VOID cCONDUCTION_EDGE_INTERNAL::get_edge_normal_for_passthrough(sdFLOAT norm[2], sSURFEL* nbr_surfel) {
  if (nbr_surfel == m_nbr_surfel) {
    vcopy2(norm, this->local_normal());
  } else {
    vrotate2(norm, m_rotation_angle, this->local_normal());
  }
}

/**
 @brief   Calculates the flux through the edge and accumulates the change in energy for each cell.

 @details The rate of heat passing through the edge is calculated using:

          \f$ q_{edge} = \mathbf{\kappa_{edge}} \cdot \mathbf{\nabla T}_{edge} A_{face}\f$

          The edge conductivity is averaged using harmonic averaging. The edge temperature gradient
          is calculated using an arithmetic mean of the LS gradients at the centroids and then 
          corrected for non-orthogonality. The neighbor surfel LS centroid gradient is rotated to the
          root surfels coordinate system before averaging. The interface area is the product of the
          shell layer thickness and the edge length.

          The total change in energy for each cell is given by: \f$ dE{i} = q_{edge} \Delta t_{i}\f$. Here
          \f$\Delta t_i\f$ is the local timestep of the cell based on cell's scale its difference from that
          of the other cell. When the scales are equal, \f$\Delta t_i = 1\f$. When the scales are different,
          the coarser cell's timestep is 0.5 and the finer is 1.0, regardless if the owner cell is coarser
          or finer. This is because the frequency this function is called is based on the finest scale
          at the edge rather than the scale of the owner cell.

          The total energy passing through the edge is always accumulated in the owner cell. A negative value
          (which is scaled appropriatly) is accumulated in the neighbor cell as long as the surfel is
          not a ghost. In the event that the neighbor is a ghost, an equivalent edge exists on the neighbor's
          SP which will calculate an equal (again scaled appropriately) and opposite edge_dE to accumulate.

          The passthrough energy, if any, is only applied when the scale difference is 0. The amount applied
          is calculated in the `apply_passthrough` function.
 */
template <asINT32 SCALE_DIFF>
VOID cCONDUCTION_EDGE_INTERNAL::compute_face_fluxes_func(const asINT32 time_index) {
  const asINT32 nbr_time_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(m_nbr_surfel->scale());
  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = m_owner_surfel->shell_conduction_data();

  sdFLOAT nbr_area = this->scaled_nbr_area<SCALE_DIFF>();

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA owner_layer_data = surfel_conduction_data->layer(nth_layer);
    SHELL_LAYER_DATA nbr_layer_data = this->get_nbr_layer(nth_layer);

    // get conductivity at edge rescaled to current surfel's scale
    sdFLOAT conductivity_nbr[3], kappa_edge[3];
    const sdFLOAT* conductivity_at = owner_layer_data->local_transverse_conductivity(time_index);
    nbr_layer_data->rotated_scaled_conductivity<SCALE_DIFF>(conductivity_nbr, 
          m_rotation_angle, nbr_time_index);
    compute_edge_conductivity(kappa_edge, conductivity_at, conductivity_nbr, nbr_area);

    sdFLOAT edge_dEdt = compute_layer_edge_flux<SCALE_DIFF>(owner_layer_data, nbr_layer_data, kappa_edge,
        nbr_area, time_index, nbr_time_index);

    sdFLOAT edge_dE = edge_dEdt * g_timescale.conduction_delta_t();

    if constexpr(SCALE_DIFF == 0) {
      if (!m_owner_surfel->is_ghost()) {
        owner_layer_data->accumulate_dE(edge_dE);
      }
      if (!m_nbr_surfel->is_ghost()) {
        nbr_layer_data->accumulate_dE(-edge_dE);
      }
      this->apply_passthrough(owner_layer_data, nbr_layer_data, conductivity_at, conductivity_nbr, time_index);
    } else if constexpr(SCALE_DIFF == -1) {
      // if the owner (which is at a a finer scale) is ghosted, the nbr surfel will miss every other timestep, so 
      // compute_face_flux_from_fine_ghost is called to reconstruct the skipped step and accumulate
      // the energy in the nbr layers. If the owner is not ghosted, it will ensure the flux is calculated
      // and energy is accumulated in the nbr surfel at every timestep.
      if (m_owner_surfel->is_ghost()) {
        compute_face_flux_from_fine_ghost<-1>(time_index^1, nbr_time_index);
      } else {
        owner_layer_data->accumulate_dE(edge_dE);
      }

      // If the coarse neighbor is ghosted, no need to accumulate into it. In this case, there is also no need to
      // reconstruct a skipped step.
      if (!m_nbr_surfel->is_ghost()) {
        const sdFLOAT scaleFactor = -0.5 / ((sdFLOAT)(1 << (sim.num_dims-1)));
        nbr_layer_data->accumulate_dE(scaleFactor*edge_dE);
      }
    } else {
      // If the coarse owner is ghosted, no need to accumulate into it. In this case, there is also no need to
      // reconstruct a skipped step.
      if (!m_owner_surfel->is_ghost()) {
        owner_layer_data->accumulate_dE(edge_dE / 2.);
      }

      // if the neighbor (which is at a finer scale) is ghosted, the owner surfel will miss every other timestep, so 
      // compute_face_flux_from_fine_ghost is called to reconstruct the skipped step and accumulate
      // the energy in the owners layers. If the nbr is not ghosted, it will ensure the flux is calculated
      // and energy is accumulated in the nbr surfel at every timestep.
      if (m_nbr_surfel->is_ghost()) {
        compute_face_flux_from_fine_ghost<1>(time_index, nbr_time_index^1);
      } else {
        const sdFLOAT scaleFactor = -1. * ((sdFLOAT)(1 << (sim.num_dims-1)));
        nbr_layer_data->accumulate_dE(scaleFactor*edge_dE);
      }
    }
  }
}

sdFLOAT cCONDUCTION_EDGE_INTERNAL::get_rotation_angle(sSURFEL* root_surfel) {
  return (root_surfel == m_owner_surfel ? m_rotation_angle : -m_rotation_angle);
}


#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_INTERNAL::compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel) {

  SURFEL nbr_surfel = this->nbr_surfel(root_surfel);
  const sdFLOAT* root_normal = this->local_normal(root_surfel);
  sdFLOAT rotation_angle = this->get_rotation_angle(root_surfel); // how to rotate a vector from nbr to root surfel

  SURFEL_SHELL_CONDUCTION_DATA root_shell_conduction_data = root_surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_DATA nbr_shell_conduction_data = nbr_surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA root_shell_implicit_solver_data = root_surfel->shell_conduction_implicit_solver_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA nbr_shell_implicit_solver_data = nbr_surfel->shell_conduction_implicit_solver_data();

  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(root_surfel->scale());
  const asINT32 time_index = prior_index ^ 1;
  const asINT32 nbr_time_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(nbr_surfel->scale());

  // scale_to_log2_voxel_size gives the scale difference between the finest scale and the current scale: scale_to_log2_voxel_size = sim_num_scales() - (scale) - 1
  //NOTE: Negate the scale diff so that we have the "from" - "to". In our case, "from" = current scale and "to" =
  //FINEST_SCALE, but scale_to_log2_voxel_size gives the negative of that.
  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  asINT32 nbr_scale_diff = -scale_to_log2_voxel_size(nbr_surfel->scale());

  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
  sdFLOAT nbr_scale_factor = vr_length_scale_factor(nbr_scale_diff);

  sdFLOAT unfolded_centroid_finest_scale[2];
  this->get_unfolded_centroid_finest_scale(root_surfel, root_surfel->scale(), nbr_surfel->scale(), unfolded_centroid_finest_scale); // Returns the unfolded centroid at the finest scale

  sdFLOAT edge_length_finest_scale = get_edge_length_finest_scale(root_surfel, root_surfel->scale(), nbr_surfel->scale());

  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT nbr_surfel_area = nbr_surfel->area * nbr_scale_factor * nbr_scale_factor;

  sdFLOAT dt_over_area = g_timescale.conduction_delta_t() / root_surfel_area;

  ccDOTIMES(nth_layer, root_shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = root_shell_conduction_data->layer(nth_layer);
    SHELL_LAYER_DATA nbr_surfel_nth_layer = nbr_shell_conduction_data->layer(nth_layer);
    //DLS-TODO:  SHELL_LAYER_DATA nbr_layer_data = this->get_nbr_layer(nth_layer); May want to use this instead.

    // Get rid of this when we allow this to be time-dependent
    sdFLOAT heat_capacity = root_surfel_nth_layer->heat_capacity[prior_index];
    heat_capacity = heat_capacity * root_scale_factor;
    dt_over_area = dt_over_area / heat_capacity;

    // get conductivity at edge rescaled to current surfel's scale
    sdFLOAT conductivity_nbr[3], conductivity_root[3], kappa_edge[3];
    const sdFLOAT* conductivity_at = root_surfel_nth_layer->local_transverse_conductivity(time_index);
    vscale(conductivity_root, root_scale_factor, conductivity_at);
    nbr_surfel_nth_layer->rotated_conductivity(conductivity_nbr, rotation_angle, nbr_time_index);
    vmul(conductivity_nbr, nbr_scale_factor);
    compute_implicit_solver_edge_conductivity(kappa_edge, conductivity_root, conductivity_nbr, root_surfel_area, nbr_surfel_area);

    PetscInt root_column_index = root_shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
    PetscInt nbr_column_index = nbr_shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
    PetscInt global_row_idx = root_column_index;

    if (!root_surfel_nth_layer->layer_matrix_data.system_row_needs_to_be_updated) {
      root_surfel_nth_layer->layer_matrix_data.system_row_needs_to_be_updated = true;
    }

    PetscScalar face_flux_contribution;
    sdFLOAT flux_contribution;
    sdFLOAT flux_correction_coeff;

    // Calculate the gradient coefficient
    flux_correction_coeff = implicit_solver_flux_correction_coeff(root_surfel, nbr_surfel, root_surfel_nth_layer, nbr_surfel_nth_layer, 
                                                                  kappa_edge, root_surfel->scale(), root_normal, unfolded_centroid_finest_scale, 
                                                                  root_scale_factor, nbr_scale_factor, edge_length_finest_scale, 
                                                                  root_shell_implicit_solver_data->cell_is_bad, 
                                                                  root_shell_implicit_solver_data->cell_has_anti_diffusion, 
                                                                  root_shell_implicit_solver_data->cell_is_tiny);

    // Diagonal term
    PetscInt root_global_column_index = root_column_index;
    // First put the (T_nbr - T_c) term in the diagonal part of the matrix (that is just the T_c coefficient)
    face_flux_contribution = dt_over_area * flux_correction_coeff;

    root_surfel_nth_layer->layer_matrix_data.row_idx_val[root_global_column_index] += face_flux_contribution;

    // Incorporate neighbor surfel's diagonal term into root surfel's row
    PetscInt nbr_global_column_index = nbr_column_index;
    // First put the (T_nbr - T_c) term in the nbr part of the matrix (that is just the T_nbr coefficient)
    face_flux_contribution = -dt_over_area * flux_correction_coeff;

    root_surfel_nth_layer->layer_matrix_data.row_idx_val[nbr_global_column_index] += face_flux_contribution;

    bool use_secondary_gradients = true;
    if (g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
      use_secondary_gradients = false;
    }

    if (use_secondary_gradients) {
      // Now get the diagonal part with the gradients
      flux_contribution = compute_implicit_solver_layer_edge_flux(root_surfel_nth_layer, nbr_surfel_nth_layer, kappa_edge, root_surfel_area, 
          nbr_surfel_area, time_index, nbr_time_index, root_column_index, root_surfel->scale(), FALSE, flux_correction_coeff, root_normal, unfolded_centroid_finest_scale, rotation_angle,
          root_scale_factor, nbr_scale_factor, edge_length_finest_scale); // FALSE b/c getting values for the root surfel
      face_flux_contribution = dt_over_area * flux_contribution;

      root_surfel_nth_layer->layer_matrix_data.row_idx_val[root_global_column_index] += face_flux_contribution;

      // Now get the nbr part with the gradients
      flux_contribution = compute_implicit_solver_layer_edge_flux(root_surfel_nth_layer, nbr_surfel_nth_layer, kappa_edge, root_surfel_area, 
          nbr_surfel_area, time_index, nbr_time_index, nbr_column_index, root_surfel->scale(), TRUE, flux_correction_coeff, root_normal, unfolded_centroid_finest_scale, rotation_angle, 
          root_scale_factor, nbr_scale_factor, edge_length_finest_scale); // TRUE b/c doing the neighbors of the owner surfel's neighbor
      face_flux_contribution = dt_over_area * flux_contribution;

      root_surfel_nth_layer->layer_matrix_data.row_idx_val[nbr_global_column_index] += face_flux_contribution;

      // Loop over the neighbors of the root surfel to get their contribution to the root surfel's edge gradient.
      DO_STENCIL_NEIGHBORS(root_shell_conduction_data, nth_nbr, is_boundary_nbr) {
        if (is_boundary_nbr) {
          continue;
        }
        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_edge = g_shell_conduction_edges.get_nbr_stencil(nth_nbr);
        STATE_VECTOR_INDEX column_index = nbr_edge->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index + nth_layer;
        PetscInt global_column_index = column_index;

        // Contribution to the face flux of the neighbor surfel's neighbor's gradient
        sdFLOAT flux_contribution = compute_implicit_solver_layer_edge_flux(root_surfel_nth_layer, nbr_surfel_nth_layer, kappa_edge, root_surfel_area, 
            nbr_surfel_area, time_index, nbr_time_index, column_index, root_surfel->scale(), FALSE, flux_correction_coeff, root_normal, unfolded_centroid_finest_scale, rotation_angle, 
            root_scale_factor, nbr_scale_factor, edge_length_finest_scale);
        face_flux_contribution = dt_over_area * flux_contribution;

        root_surfel_nth_layer->layer_matrix_data.row_idx_val[global_column_index] += face_flux_contribution;
      }

      // Assemble the RHS with contributions from boundary terms
      DO_BOUNDARY_EDGE_STENCILS(root_shell_conduction_data, nth_stencil) {
        SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
        sdFLOAT rhs_contribution = implicit_solver_assemble_rhs_from_grad_stencil(root_surfel_nth_layer, 
                                                                                  nbr_surfel_nth_layer, 
                                                                                  bnd_nbr_stencil,
                                                                                  kappa_edge, 
                                                                                  root_surfel_area, 
                                                                                  nbr_surfel_area, 
                                                                                  time_index, 
                                                                                  nbr_time_index, 
                                                                                  root_surfel->scale(), 
                                                                                  FALSE, 
                                                                                  flux_correction_coeff, 
                                                                                  root_normal, 
                                                                                  unfolded_centroid_finest_scale, 
                                                                                  rotation_angle, 
                                                                                  root_scale_factor, 
                                                                                  nbr_scale_factor, 
                                                                                  edge_length_finest_scale);
        PetscScalar rhs_flux_contribution = dt_over_area * rhs_contribution;
        if (!root_surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated) {
          root_surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated = true;
        }
        root_surfel_nth_layer->layer_matrix_data.bc_vals += rhs_flux_contribution;
      }

      // Loop over the neighbors of the neighbor surfel to get their contribution to the root surfel's edge gradient.
      DO_STENCIL_NEIGHBORS(nbr_shell_conduction_data, nth_nbr, is_boundary_nbr) {
        if (is_boundary_nbr) {
          continue;
        }
        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_edge_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_nbr);
        STATE_VECTOR_INDEX column_index = nbr_edge_nbr->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index + nth_layer;
        PetscInt global_column_index = column_index;

        // Contribution to the face flux of the neighbor surfel's neighbor's gradient
        sdFLOAT flux_contribution = compute_implicit_solver_layer_edge_flux(root_surfel_nth_layer, nbr_surfel_nth_layer, kappa_edge, root_surfel_area, 
            nbr_surfel_area, time_index, nbr_time_index, column_index, root_surfel->scale(), TRUE, flux_correction_coeff, root_normal, unfolded_centroid_finest_scale, rotation_angle, 
            root_scale_factor, nbr_scale_factor, edge_length_finest_scale); // TRUE b/c doing the neighbors of the root surfel's neighbor
        face_flux_contribution = dt_over_area * flux_contribution;
        root_surfel_nth_layer->layer_matrix_data.row_idx_val[global_column_index] += face_flux_contribution;
      }

      // Assemble the RHS with contributions from boundary terms involving the neighbor surfel
      DO_BOUNDARY_EDGE_STENCILS(nbr_shell_conduction_data, nth_stencil) {
        SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
        sdFLOAT rhs_contribution = implicit_solver_assemble_rhs_from_grad_stencil(root_surfel_nth_layer, 
                                                                                  nbr_surfel_nth_layer, 
                                                                                  bnd_nbr_stencil,
                                                                                  kappa_edge, 
                                                                                  root_surfel_area, 
                                                                                  nbr_surfel_area, 
                                                                                  time_index, 
                                                                                  nbr_time_index, 
                                                                                  root_surfel->scale(), 
                                                                                  TRUE, 
                                                                                  flux_correction_coeff, 
                                                                                  root_normal, 
                                                                                  unfolded_centroid_finest_scale, 
                                                                                  rotation_angle, 
                                                                                  root_scale_factor, 
                                                                                  nbr_scale_factor,
                                                                                  edge_length_finest_scale);
        PetscScalar rhs_flux_contribution = dt_over_area * rhs_contribution;
        if (!root_surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated) {
          root_surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated = true;
        }
        root_surfel_nth_layer->layer_matrix_data.bc_vals += rhs_flux_contribution;
      }
    }
  }
}

VOID cCONDUCTION_EDGE_INTERNAL::implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {

  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA root_shell_implicit_solver_data = root_surfel->shell_conduction_implicit_solver_data();

  if (root_shell_implicit_solver_data->cell_is_bad) {
    return;
  }

  SURFEL nbr_surfel = this->nbr_surfel(root_surfel);
  const sdFLOAT* root_normal = this->local_normal(root_surfel);
  sdFLOAT rotation_angle = this->get_rotation_angle(root_surfel); // how to rotate a vector from nbr to root surfel

  SURFEL_SHELL_CONDUCTION_DATA root_shell_conduction_data = root_surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_DATA nbr_shell_conduction_data = nbr_surfel->shell_conduction_data();

  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  asINT32 nbr_scale_diff = -scale_to_log2_voxel_size(nbr_surfel->scale());

  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
  sdFLOAT nbr_scale_factor = vr_length_scale_factor(nbr_scale_diff);

  sdFLOAT unfolded_centroid_finest_scale[2];
  this->get_unfolded_centroid_finest_scale(root_surfel, root_surfel->scale(), nbr_surfel->scale(), unfolded_centroid_finest_scale); // Returns the unfolded centroid at the finest scale

  sdFLOAT edge_length_finest_scale = get_edge_length_finest_scale(root_surfel, root_surfel->scale(), nbr_surfel->scale());

  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT nbr_surfel_area = nbr_surfel->area * nbr_scale_factor * nbr_scale_factor;

  ccDOTIMES(nth_layer, root_shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = root_shell_conduction_data->layer(nth_layer);
    SHELL_LAYER_DATA nbr_surfel_nth_layer = nbr_shell_conduction_data->layer(nth_layer);
    //DLS-TODO:  SHELL_LAYER_DATA nbr_layer_data = this->get_nbr_layer(nth_layer); May want to use this instead.

    const sdFLOAT edge_area = root_surfel_nth_layer->scaled_interfacial_area(edge_length_finest_scale, nbr_surfel_nth_layer, root_scale_factor, nbr_scale_factor);
  
    sdFLOAT area_vector[2];
    vscale2(area_vector, edge_area, root_normal);
  
    sdFLOAT unfolded_centroid_dot_area_vector = vdot2(unfolded_centroid_finest_scale, area_vector);
    if (unfolded_centroid_dot_area_vector < SDFLOAT_EPSILON) {
      sdFLOAT centroid[3];
      vcopy(centroid, root_surfel->centroid);
      sIMPLICIT_SOLVER_STENCIL_ERROR anti_diffusion_error = {SP_EER_IMPLICIT_SHELL_SOLVER_ANTI_DIFFUSION,
                                                             root_surfel->scale(),
                                                             root_surfel->id(),
                                                             nbr_surfel->id(),
                                                             root_surfel->area,
                                                             nbr_surfel->area,
                                                             centroid[0],
                                                             centroid[1],
                                                             centroid[2],
                                                             nbr_surfel->centroid[0],
                                                             nbr_surfel->centroid[1],
                                                             nbr_surfel->centroid[2],
                                                             unfolded_centroid_dot_area_vector,
                                                             ""};
      g_implicit_shell_solver_simerrs.push_back(anti_diffusion_error);
      root_shell_implicit_solver_data->cell_is_bad = true;
      root_shell_implicit_solver_data->cell_has_anti_diffusion = true;
      if (!g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
        g_implicit_shell_solver_any_cell_bad = true;
      }
    }

    // Check how bad the orthogonality is
    sdFLOAT maximum_allowable_nonorthogonality = 0.0871557427476581; //1.4835298641951801 rad = 85 deg;
    //sdFLOAT maximum_allowable_nonorthogonality = 0.2588190451025207; // 75 deg
    sdFLOAT unfolded_centroid_mag = vlength2(unfolded_centroid_finest_scale);
    sdFLOAT area_vector_mag = vlength2(area_vector);
    sdFLOAT orthogonality = vdot2(unfolded_centroid_finest_scale, area_vector) / unfolded_centroid_mag / area_vector_mag;
    if (orthogonality < maximum_allowable_nonorthogonality) {
      sdFLOAT centroid[3];
      vcopy(centroid, root_surfel->centroid);
      sIMPLICIT_SOLVER_STENCIL_ERROR nonorthogonality_error = {SP_EER_IMPLICIT_SHELL_SOLVER_NONORTHOGONALITY,
                                                               root_surfel->scale(),
                                                               root_surfel->id(),
                                                               nbr_surfel->id(),
                                                               root_surfel->area,
                                                               nbr_surfel->area,
                                                               centroid[0],
                                                               centroid[1],
                                                               centroid[2],
                                                               nbr_surfel->centroid[0],
                                                               nbr_surfel->centroid[1],
                                                               nbr_surfel->centroid[2],
                                                               orthogonality,
                                                               ""};
      g_implicit_shell_solver_simerrs.push_back(nonorthogonality_error);
      root_shell_implicit_solver_data->cell_is_bad = true;
      if (!g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
        g_implicit_shell_solver_any_cell_bad = true;
      }
    }

    // Check if this surfel is really tiny
    sdFLOAT root_surfel_area = root_surfel->area;
    if (root_surfel_area < 2.0e-7) {
      sdFLOAT centroid[3];
      vcopy(centroid, root_surfel->centroid);
      sIMPLICIT_SOLVER_STENCIL_ERROR tiny_surfel_error = {SP_EER_IMPLICIT_SHELL_SOLVER_TINY_SURFEL,
                                                          root_surfel->scale(),
                                                          root_surfel->id(),
                                                          0,
                                                          root_surfel->area,
                                                          0,
                                                          centroid[0],
                                                          centroid[1],
                                                          centroid[2],
                                                          0.0,
                                                          0.0,
                                                          0.0,
                                                          -1,
                                                          ""};
      g_implicit_shell_solver_simerrs.push_back(tiny_surfel_error);
      root_shell_implicit_solver_data->cell_is_bad = true;
      root_shell_implicit_solver_data->cell_is_tiny = true;
      if (!g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
        g_implicit_shell_solver_any_cell_bad = true;
      }
    }

    // Check if the area ratio is extremely distorted
    sdFLOAT area_ratio = root_surfel_area / nbr_surfel->area;
    if (area_ratio < 0.01 || area_ratio > 100) {
      sdFLOAT centroid[3];
      vcopy(centroid, root_surfel->centroid);
      sIMPLICIT_SOLVER_STENCIL_ERROR area_ratio_error = {SP_EER_IMPLICIT_SHELL_SOLVER_BAD_AREA_RATIO,
                                                         root_surfel->scale(),
                                                         root_surfel->id(),
                                                         nbr_surfel->id(),
                                                         root_surfel->area,
                                                         nbr_surfel->area,
                                                         centroid[0],
                                                         centroid[1],
                                                         centroid[2],
                                                         nbr_surfel->centroid[0],
                                                         nbr_surfel->centroid[1],
                                                         nbr_surfel->centroid[2],
                                                         area_ratio,
                                                         ""};
      g_implicit_shell_solver_simerrs.push_back(area_ratio_error);
      root_shell_implicit_solver_data->cell_is_bad = true;
      root_shell_implicit_solver_data->cell_has_extreme_area_ratio = true;
      if (!g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
        g_implicit_shell_solver_any_cell_bad = true;
      }
    }

  }

  // If the protectors are turned off, then reset the surfel value if it was marked as bad. We're keeping all the logic
  // above so we can preserve simerr info.
  //if (!g_shell_implicit_solver_use_protectors) {
  //  root_shell_implicit_solver_data->cell_is_bad = false;
  //}

  return;
}

#endif

/**
 @brief   Performs the face flux calculation using the previous timestep's (n-1) values.

 @details Function is used to calculate the missed half timestep update when the finer surfel at a VR
          interface is ghosted and the coarser surfel misses every other timestep associated with the
          finer scale. All necessary data should be available in the previous timestep index
          (note: this is the opposite index of what is commonly referred to as the prior index).
*/
template <asINT32 SCALE_DIFF>
VOID cCONDUCTION_EDGE_INTERNAL::compute_face_flux_from_fine_ghost(const asINT32 time_index,
    const asINT32 nbr_time_index) {
  // const asINT32 nbr_time_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(m_nbr_surfel->scale())^1;
  
  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = m_owner_surfel->shell_conduction_data(); 
  sdFLOAT nbr_area = this->scaled_nbr_area<SCALE_DIFF>();

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA owner_layer_data = surfel_conduction_data->layer(nth_layer);
    SHELL_LAYER_DATA nbr_layer_data = this->get_nbr_layer(nth_layer);

    // get conductivity at edge rescaled to current surfel's scale
    sdFLOAT conductivity_nbr[3], kappa_edge[3];
    const sdFLOAT* conductivity_at = owner_layer_data->local_transverse_conductivity(time_index);
    nbr_layer_data->rotated_scaled_conductivity<SCALE_DIFF>(conductivity_nbr, m_rotation_angle, nbr_time_index);
    compute_edge_conductivity(kappa_edge, conductivity_at, conductivity_nbr, nbr_area);

    sdFLOAT edge_dEdt = compute_layer_edge_flux<SCALE_DIFF>(owner_layer_data, nbr_layer_data, kappa_edge,
        nbr_area, time_index, nbr_time_index);

    // This will always be a half time step, so we can go ahead and divide it by 2.
    // If the non-ghost surfel is the neighbor, it will still need to be scaled.
    sdFLOAT edge_dE = edge_dEdt * 0.5 * g_timescale.conduction_delta_t();

    if constexpr(SCALE_DIFF == -1) {
      // Accumulate the energy from the skipped step into the nbr surfel, making sure to scale correctly
      const sdFLOAT scaleFactor = -1.0 / ((sdFLOAT)(1 << (sim.num_dims-1)));
      nbr_layer_data->accumulate_dE(scaleFactor*edge_dE);
    } else {
      owner_layer_data->accumulate_dE(edge_dE);
    }
  }
}

/**
 @brief   Constructs a cCONDUCTION_EDGE_INTERSECTION object

 @details Must be passed 3 vectors containing the surfels in the intersection, the normal distances in the
          corresponding surfels VR scale, and the relative rotation between the corresponding surfel's csys
          and the owner surfel's csys. This information can be constructed from the cSHELL_MESH::HALF_EDGE's
          associated with this edge.
*/
cCONDUCTION_EDGE_INTERSECTION::cCONDUCTION_EDGE_INTERSECTION(sSURFEL* owner_surfel,
  dFLOAT e_length, sdFLOAT norm_dist, const sdFLOAT* norm_vec, std::vector<sSURFEL*>& nbr_surfels,
  std::vector<sdFLOAT>& nbr_dists, std::vector<sdFLOAT>& nbr_rotations)
    : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec) {

  m_nbr_surfels = xnew sEDGE_SURFEL_VECTOR();
  m_nbr_surfels->reserve(nbr_surfels.size());
  m_rot_angles = xnew sdFLOAT[nbr_surfels.size()];
  m_nbr_dists = xnew sdFLOAT[nbr_surfels.size()];

  // m_owner_surfel and its associated data are already included in nbr_surfels, nbr_dists and nbr_rotations
  asINT32 finest_scale = m_owner_surfel->scale();
  asINT32 coarsest_scale = m_owner_surfel->scale();
  asINT32 finest_scale_on_SP = finest_scale;

  ccDOTIMES(i, nbr_surfels.size()) {
    m_nbr_surfels->push_back(nbr_surfels[i]);
    SURFEL nbr_surfel = nbr_surfels[i];
    finest_scale = MAX(finest_scale, nbr_surfel->scale());
    coarsest_scale = MIN(coarsest_scale, nbr_surfel->scale());
    if (!nbr_surfel->is_ghost()) {
      finest_scale_on_SP = MAX(finest_scale_on_SP, nbr_surfel->scale());
    }
    // Neighbor normal distances will be stored in their surfel's VR scale.
    m_nbr_dists[i] = nbr_dists.at(i);
    m_rot_angles[i] = nbr_rotations.at(i);
  }

  // This implementation cannot handle intersection of surfels across more than 2 VRs
  // without losing energy conservation. It is not expected that this situation
  // can occur, but this check has been added to ensure it throws an error
  // in the event that it does occur.
  if (finest_scale > (coarsest_scale+1)) {
    msg_internal_error("Current edge implementation cannot handle scale differences > 2 at a single intersection.");
  }

  // for an intersection with surfels from two different VR regions, the update frequency
  // will be that of the finest surfel on this SP
  // The timestep of each update however will be based on the finest surfel at the intersection,
  // therefore this is stored as m_finest_surfel.
  this->set_update_scale(finest_scale_on_SP);
  m_finest_scale = finest_scale;
}

/**
 @brief   Calculates flux into the surfel specified by surfel_ind and accumulates it into each shell layer object.
*/
VOID cCONDUCTION_EDGE_INTERSECTION::update_shell_surfel(const asINT32 surfel_ind) {
  SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
  // no need to update ghost surfels. They will be updated on their SP.
  if (surfel->is_ghost()) { return; }

  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 

  const asINT32 time_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
  const sdFLOAT surfel_dt = this->local_timestep(surfel->scale());

  sdFLOAT normal_vec[2];
  this->nbr_normal_vec(normal_vec, surfel_ind);

  const asINT32 scale_diff = surfel->scale() - m_finest_scale;
  // scale_diff will always be 0 or -1
  const sdFLOAT twoLamRatio = 1 << -scale_diff;
  sdFLOAT scaled_edge_length = length()*twoLamRatio;

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
    sdFLOAT edge_grad_t = (m_temp_or_flux - layer_data->temp(time_index)) / m_nbr_dists[surfel_ind];
    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, normal_vec);
    sdFLOAT interface_area = scaled_edge_length * layer_data->thickness;
    sdFLOAT edge_dE = normal_conductivity * edge_grad_t * interface_area * surfel_dt;

    layer_data->accumulate_dE(edge_dE);
  }
}

/**
 @brief   Computes the temperature of the intersection edge.

 @details Temperature calculated using a thermal resistance network. A detailed description of the
          method will be provided in a specification document.
*/
VOID cCONDUCTION_EDGE_INTERSECTION::compute_edge_temperature() {
  sdFLOAT sum_numerator = 0.0;
  sdFLOAT sum_denominator = 0.0;

  ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
    SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
    SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 
    sdFLOAT normal_vec[2];
    this->nbr_normal_vec(normal_vec, surfel_ind);

    const asINT32 time_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
    const asINT32 scale_diff = surfel->scale() - m_finest_scale;
    // scale_diff will always be 0 or -1
    const sdFLOAT twoLamRatio = 1 << -scale_diff;

    ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
      SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
      sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, normal_vec);
      // m_nbr_dists and thickness are at scale of the corresponding surfel, so scale factors divide out
      sdFLOAT kappa_tau_div_len = normal_conductivity*layer_data->thickness/m_nbr_dists[surfel_ind];
      kappa_tau_div_len *= twoLamRatio;
      sum_numerator += kappa_tau_div_len * layer_data->temp(time_index);
      sum_denominator += kappa_tau_div_len;
    }
  }
  m_temp_or_flux = sum_numerator / sum_denominator;
}


/**
 @brief   Returns the local timestep to be used when calculating dE based on the surfel_scale and the
          scale of the surfels sharing this edge.
*/
sdFLOAT cCONDUCTION_EDGE_INTERSECTION::local_timestep(const asINT32 surfel_scale) {
  // for any given VR scale the timestep is the conduction timestep (n_conduction_pde_base_steps/n_lb_base_steps)
  // so for surfels at a coarser scale, which are updated every time the fine surfels at the intersection
  // are updated, the timestep is halved or dt = (conduction timestep)/(1 << (m_finest_scale - surfel_scale))
  return g_timescale.conduction_delta_t() / (1 << (m_finest_scale - surfel_scale));
}

/**
 @brief   Performs the face flux calculation using the previous timestep's (n-1) values for
          the surfel at index surfel_ind.

 @details Function is used to calculate the missed half timestep update when the finer surfels at sharing
          the edge are all ghosted from a separate SP. All necessary data should be available
          in the previous timestep index (note: this is the opposite index of what is commonly referred to
          as the prior index).
*/
VOID cCONDUCTION_EDGE_INTERSECTION::update_shell_surfel_missed_step(const asINT32 surfel_ind) {
  SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
  // no need to update ghost surfels. They will be updated on their SP.
  if (surfel->is_ghost()) { return; }

  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 

  // This function is called only for coarse surfels with all finer resolution surfels
  // being ghosts, so scale diff will be -1 for every surfel
  const sdFLOAT twoLamRatio = 2.;
  const sdFLOAT scaled_edge_length = length()*twoLamRatio;
  const sdFLOAT surfel_dt = 0.5 * g_timescale.conduction_delta_t();
  const asINT32 time_index = (g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale()));

  sdFLOAT normal_vec[2];
  this->nbr_normal_vec(normal_vec, surfel_ind);

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
    sdFLOAT edge_grad_t = (m_temp_or_flux - layer_data->temp(time_index)) / m_nbr_dists[surfel_ind];
    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, normal_vec);
    sdFLOAT interface_area = scaled_edge_length * layer_data->thickness;
    sdFLOAT edge_dE = normal_conductivity * edge_grad_t * interface_area * surfel_dt;
    layer_data->accumulate_dE(edge_dE);
  }
}

/**
 @brief   Computes the temperature of the intersection edge at the previous missed half step

 @details This will be used by the calls to `update_shell_surfel_missed_step` to calculate the flux
*/
VOID cCONDUCTION_EDGE_INTERSECTION::compute_prev_edge_temperature() {
  sdFLOAT sum_numerator = 0.0;
  sdFLOAT sum_denominator = 0.0;

  ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
    SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
    SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 
    sdFLOAT normal_vec[2];
    this->nbr_normal_vec(normal_vec, surfel_ind);

    const asINT32 scale_diff = surfel->scale() - m_finest_scale;
    // for fine surfels we want data from t = n-1 and for coarse surfels we want it from t = n
    // this will flip the time index for fine surfels and not for coarse surfels
    const asINT32 time_index = (g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale())) ^ (scale_diff+1);
    // scale_diff will always be 0 or -1
    const sdFLOAT twoLamRatio = 1 << -scale_diff;

    ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
      SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
      sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, normal_vec);
      // m_nbr_dists and thickness are at scale of the corresponding surfel, so scale factors divide out
      sdFLOAT kappa_tau_div_len = normal_conductivity*layer_data->thickness/m_nbr_dists[surfel_ind];
      kappa_tau_div_len *= twoLamRatio;
      sum_numerator += kappa_tau_div_len * layer_data->temp(time_index);
      sum_denominator += kappa_tau_div_len;
    }
  }
  m_temp_or_flux = sum_numerator / sum_denominator;
}

#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_INTERSECTION::implicit_solver_compute_edge_temperature() {
  // Computes the edge temperature at the previous timestep
  sdFLOAT sum_numerator = 0.0;
  sdFLOAT sum_denominator = 0.0;

  ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
    SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
    SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 
    
    sdFLOAT normal_vec[2];
    this->nbr_normal_vec(normal_vec, surfel_ind);

    // Get the surfel scale
    asINT32 surfel_scale_diff = -scale_to_log2_voxel_size(surfel->scale());
    sdFLOAT surfel_scale_factor = vr_length_scale_factor(surfel_scale_diff);

    const asINT32 prior_index = (g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale())) ^ 1;

    ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
      SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
      sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(prior_index, normal_vec);
      normal_conductivity = surfel_scale_factor * normal_conductivity;

      // m_nbr_dists and thickness are at scale of the corresponding surfel, so scale factors divide out
      sdFLOAT kappa_tau_div_len = normal_conductivity * layer_data->thickness / m_nbr_dists[surfel_ind];
      sum_numerator += kappa_tau_div_len * layer_data->temp(prior_index);
      sum_denominator += kappa_tau_div_len;
    }
  }
  m_temp_or_flux = sum_numerator / sum_denominator;
}

VOID cCONDUCTION_EDGE_INTERSECTION::implicit_solver_compute_edge_resistance() {
  sdFLOAT total_inverse_edge_resistance = 0.0;
  ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
    SURFEL surfel = m_nbr_surfels->operator[](surfel_ind);
    SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = surfel->shell_conduction_data(); 
    
    sdFLOAT normal_vec[2];
    this->nbr_normal_vec(normal_vec, surfel_ind);

    // Get the surfel scale
    asINT32 surfel_scale_diff = -scale_to_log2_voxel_size(surfel->scale());
    sdFLOAT surfel_scale_factor = vr_length_scale_factor(surfel_scale_diff);

    const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());

    ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
      SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
      sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(prior_index, normal_vec);
      normal_conductivity = surfel_scale_factor * normal_conductivity;

      // m_nbr_dists and thickness are at scale of the corresponding surfel, so scale factors divide out
      sdFLOAT kappa_tau_div_len = normal_conductivity * layer_data->thickness / m_nbr_dists[surfel_ind];
      total_inverse_edge_resistance += kappa_tau_div_len;
    }
  }
  m_total_edge_resistance = 1.0 / total_inverse_edge_resistance;
}

VOID cCONDUCTION_EDGE_INTERSECTION::assemble_rhs_func(sSURFEL* root_surfel) {
  sdFLOAT root_normal[2];
  this->root_local_normal(root_normal, root_surfel);

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = root_surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = root_surfel->shell_conduction_implicit_solver_data();

  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT edge_length_finest_scale = root_scale_factor * this->length();
  sdFLOAT dt_over_area = g_timescale.conduction_delta_t() / root_surfel_area;

  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(root_surfel->scale());

  auto root_surfel_iterator = std::find(m_nbr_surfels->begin(), m_nbr_surfels->end(), root_surfel);
  auto root_surfel_index = std::distance(m_nbr_surfels->begin(), root_surfel_iterator);
  sdFLOAT root_surfel_distance = m_nbr_dists[root_surfel_index];
  
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = shell_conduction_data->layer(nth_layer);

    if (!layer_data->layer_matrix_data.system_row_needs_to_be_updated) {
      layer_data->layer_matrix_data.system_row_needs_to_be_updated = true;
    }

    sdFLOAT root_normal_conductivity = layer_data->edge_normal_conductivity(prior_index, root_normal); // value of prior_index doesn't matter since assembly is happening before anything else 
    root_normal_conductivity = root_scale_factor * root_normal_conductivity;
    sdFLOAT scaled_normal_distance = root_scale_factor * root_surfel_distance; //this->normal_distance();
    sdFLOAT root_normal_conductivity_over_distance = root_normal_conductivity / scaled_normal_distance;
    sdFLOAT heat_capacity = root_scale_factor * layer_data->heat_capacity[prior_index];
    dt_over_area = dt_over_area / heat_capacity;
    sdFLOAT scaled_thickness = root_scale_factor * layer_data->thickness;
    sdFLOAT interface_area = edge_length_finest_scale * scaled_thickness;

    // This contributes to the diagonal terms of the flux matrix
    PetscReal edge_intersection_flux_matrix_diag_contribution = dt_over_area * interface_area * root_normal_conductivity_over_distance;
    PetscInt root_row_idx = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
    layer_data->layer_matrix_data.row_idx_val[root_row_idx] += edge_intersection_flux_matrix_diag_contribution;

    // Now for this root surfel's layer, loop over the neighbor's layers
    ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
      SURFEL nbr_surfel_of_this_edge = m_nbr_surfels->operator[](surfel_ind);

      // Get the surfel scale
      asINT32 nbr_surfel_scale_diff = -scale_to_log2_voxel_size(nbr_surfel_of_this_edge->scale());
      sdFLOAT nbr_surfel_scale_factor = vr_length_scale_factor(nbr_surfel_scale_diff);

      SURFEL_SHELL_CONDUCTION_DATA nbr_surfel_conduction_data = nbr_surfel_of_this_edge->shell_conduction_data(); 

      sdFLOAT nbr_surfel_area = nbr_surfel_of_this_edge->area * nbr_surfel_scale_factor * nbr_surfel_scale_factor;

      sdFLOAT nbr_normal_vec[2]; // normal vector of this nbr surfel
      this->nbr_normal_vec(nbr_normal_vec, surfel_ind);

      STATE_VECTOR_INDEX nbr_column_index = nbr_surfel_of_this_edge->shell_conduction_implicit_solver_data()->implicit_shell_state_index;

      ccDOTIMES(nbr_nth_layer, nbr_surfel_conduction_data->num_layers()) {
        SHELL_LAYER_DATA nbr_layer_data = nbr_surfel_conduction_data->layer(nbr_nth_layer);
        sdFLOAT nbr_normal_conductivity = nbr_layer_data->edge_normal_conductivity(prior_index, nbr_normal_vec);
        nbr_normal_conductivity = nbr_surfel_scale_factor * nbr_normal_conductivity;
        sdFLOAT edge_resistance = nbr_normal_conductivity * nbr_layer_data->thickness / m_nbr_dists[surfel_ind];
        PetscReal edge_intersection_flux_contribution = -edge_intersection_flux_matrix_diag_contribution * edge_resistance * m_total_edge_resistance;

        PetscInt col_idx = nbr_column_index + nbr_nth_layer;
        layer_data->layer_matrix_data.row_idx_val[col_idx] += edge_intersection_flux_contribution;
      }
    }
  }
}

std::vector<int> cCONDUCTION_EDGE_INTERSECTION::implicit_solver_get_edge_intersection_nbr_index() {
  std::vector<PetscInt> edge_intersection_nbrs;
  ccDOTIMES(surfel_ind, m_nbr_surfels->size()) {
    SURFEL nbr_surfel_of_this_edge = m_nbr_surfels->operator[](surfel_ind);
    SURFEL_SHELL_CONDUCTION_DATA nbr_surfel_conduction_data = nbr_surfel_of_this_edge->shell_conduction_data(); 
    STATE_VECTOR_INDEX nbr_column_index = nbr_surfel_of_this_edge->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
    ccDOTIMES(nbr_nth_layer, nbr_surfel_conduction_data->num_layers()) {
      PetscInt col_idx = nbr_column_index + nbr_nth_layer;
      edge_intersection_nbrs.push_back(col_idx);
    }
  }
  return edge_intersection_nbrs;
}

VOID cCONDUCTION_EDGE_INTERSECTION::assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {
  // Only doing the root surfel because there is no nbr surfel.
  ccDOTIMES(i, 2) {
    if (root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i].count(column_index) == 0) {
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] = 0.0;
    }
    root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] -= scale_factor * stencil_surfel_lsq_coeff[i];
  }
}

#endif

/**
 @brief   Returns the temperature on an adiabatic edge for the provided layer and time index.

 @details Returns the temperature at the centroid of the layer's cell.
*/
sdFLOAT cCONDUCTION_EDGE_ADIABATIC_BOUNDARY::boundary_temperature(SHELL_LAYER_DATA layer_data,
    const asINT32 time_index) {
  return layer_data->temp(time_index);
}

/**
 @brief   Constructs a cCONDUCTION_EDGE_DYN_DATA_BOUNDARY object

 @details Must be passed the neighboring non-shell conduction surfel that defines the flux BC.
*/
template<class EDGE_DYN_DATA_TYPE>
cCONDUCTION_EDGE_DYN_DATA_BOUNDARY<EDGE_DYN_DATA_TYPE>::cCONDUCTION_EDGE_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel,
                                                                                           dFLOAT e_length,
                                                                                           sdFLOAT norm_dist,
                                                                                           const sdFLOAT* norm_vec,
                                                                                           sSURFEL* bc_surfel)
    : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec), 
      m_dyn_data(owner_surfel, bc_surfel, norm_dist, norm_vec) {
}

/**
 @brief   Constructs a cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY object

 @details Must be passed the neighboring conduction surfel that contains the physics descriptor defining the flux bc
*/
cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel,
                                                                                 dFLOAT e_length,
                                                                                 sdFLOAT norm_dist,
                                                                                 const sdFLOAT* norm_vec,
                                                                                 sSURFEL* bc_surfel) 
    : cCONDUCTION_EDGE_DYN_DATA_BOUNDARY<cEDGE_FLUX_BOUNDARY_DYN_DATA>(owner_surfel, e_length, norm_dist,
                                                                       norm_vec, bc_surfel) {

  // Open shell surfels make contact with surface on the fluid side. Therefore, if they are abutting a
  // conduction_surface (thats not an open shell boundary), they get their flux information from
  // parameters->heat_flux_2 instead of parameters->heat_flux_1
  BOOLEAN nbr_is_conduction_surfel = bc_surfel->is_conduction_surface()
                                 && !bc_surfel->is_conduction_open_shell_boundary();
  m_uses_pd_flux1 = (owner_surfel->is_conduction_open_shell() && nbr_is_conduction_surfel) ? FALSE : TRUE;

  // Store initial flux in m_temp_or_flux in case it is fixed and all subsequent updates are skipped
  auto pd = m_dyn_data.physics_descriptor();
  if (m_uses_pd_flux1) {
    this->m_temp_or_flux = pd->parameters()->face_area.value > 0.0 ? (pd->parameters()->heat_flux_1.value / pd->parameters()->face_area.value) * g_density_scale_factor
                                                              : pd->parameters()->heat_flux_1.value * g_density_scale_factor;
  } else {
    this->m_temp_or_flux = pd->parameters()->face_area.value > 0.0 ? (pd->parameters()->heat_flux_2.value / pd->parameters()->face_area.value) * g_density_scale_factor
                                                              : pd->parameters()->heat_flux_2.value * g_density_scale_factor;
  }
}

/**
 @brief   Updates m_temp_or_flux with flux information stored in physics descriptor

 @details Checks if the flux term in the physics descriptor is fixed and if so, skips the unnecessary update.
*/
VOID cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::update_edge_flux() {
  if (m_dyn_data.update_varying_physics_params()) { 
    auto pd = m_dyn_data.physics_descriptor();
    if (m_uses_pd_flux1) {
      this->m_temp_or_flux = pd->parameters()->face_area.value > 0.0 ? (pd->parameters()->heat_flux_1.value / pd->parameters()->face_area.value) * g_density_scale_factor
                                                              : pd->parameters()->heat_flux_1.value * g_density_scale_factor;
    } else {
      this->m_temp_or_flux = pd->parameters()->face_area.value > 0.0 ? (pd->parameters()->heat_flux_2.value / pd->parameters()->face_area.value) * g_density_scale_factor
                                                              : pd->parameters()->heat_flux_2.value * g_density_scale_factor;
    }
  }
}

/**
 @brief   Accumulates the energy change in the layers of the owner surfel based on the value of m_temp_or_flux
*/
VOID cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::compute_face_fluxes_func(const asINT32 time_index) {
  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = m_owner_surfel->shell_conduction_data(); 

  const sdFLOAT surfel_dt = g_timescale.conduction_delta_t();

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
    sdFLOAT interface_area = this->length() * layer_data->thickness;
    sdFLOAT edge_dE = this->m_temp_or_flux * interface_area * surfel_dt;
    layer_data->accumulate_dE(edge_dE);
  }
}

#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::assemble_rhs_func(sSURFEL* root_surfel) {
  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = root_surfel->shell_conduction_data();
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(root_surfel->scale());

  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);

  sdFLOAT edge_length_finest_scale = root_scale_factor * this->length();
  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT dt_over_area = g_timescale.conduction_delta_t() / root_surfel_area;

  //STATE_VECTOR_INDEX column_index = root_surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = shell_conduction_data->layer(nth_layer);
    sdFLOAT heat_capacity = root_scale_factor * layer_data->heat_capacity[prior_index]; // Scale the heat capacity to the finest scale
    dt_over_area = dt_over_area / heat_capacity;
    sdFLOAT scaled_thickness = root_scale_factor * layer_data->thickness; // scale the thickness to the finest scale
    sdFLOAT interface_area = edge_length_finest_scale * scaled_thickness;

    PetscScalar boundary_flux = dt_over_area * interface_area * this->m_temp_or_flux;

    //PetscInt global_idx = column_index + nth_layer;
    if (!layer_data->layer_matrix_data.bc_needs_to_be_updated) {
      layer_data->layer_matrix_data.bc_needs_to_be_updated = true;
    }
    layer_data->layer_matrix_data.bc_vals += boundary_flux;
  }
}

sdFLOAT cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::implicit_solver_boundary_contribution_func(SHELL_LAYER_DATA layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
  // This accounts for the contribution to the LS gradient stencil

  sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, edge_normal);
  normal_conductivity = scale_factor * normal_conductivity;

  //sdFLOAT unscaled_normal_distance = this->unscaled_normal_distance();
  //sdFLOAT scaled_normal_distance = scale_factor * unscaled_normal_distance;
  sdFLOAT scaled_normal_distance = scale_factor * this->normal_distance();

  sdFLOAT boundary_contribution = -this->m_temp_or_flux * scaled_normal_distance / normal_conductivity;
  return boundary_contribution;
}
#endif //!GPU_COMPILER && !BUILD_GPU

/**
 @brief   Calculates and returns the corresponding layer's boundary temperature based on the flux information
*/
sdFLOAT cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::boundary_temperature(SHELL_LAYER_DATA layer_data, const asINT32 time_index) {
    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, this->local_normal());
    sdFLOAT effective_grad_t_at_edge = this->m_temp_or_flux / normal_conductivity;
    return layer_data->temp(time_index) + effective_grad_t_at_edge * this->normal_distance();
}

/**
 @brief   Constructs a cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY object

 @details Must be passed the neighboring conduction surfel that contains the physics descriptor defining the HTC bc
*/
cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel,
                                                                               dFLOAT e_length,
                                                                               sdFLOAT norm_dist,
                                                                               const sdFLOAT* norm_vec,
                                                                               sSURFEL* bc_surfel) 
    : cCONDUCTION_EDGE_DYN_DATA_BOUNDARY<cEDGE_HTC_BOUNDARY_DYN_DATA>(owner_surfel, e_length, norm_dist,
                                                                      norm_vec, bc_surfel) {
  // Update ambient temp and htc directly since calling update_htc_params will return without updating if they are
  // not varying
  auto pd = m_dyn_data.physics_descriptor();
  m_ambient_temp = pd->parameters()->ambient_temp.value;
  m_heat_xfer_coeff = pd->parameters()->ext_heat_xfer_coeff.value * g_density_scale_factor;
}

/**
 @brief   Updates variables storing ambient temperature and htc with information stored in physics descriptor

 @details Checks if the terms in the physics descriptor is fixed and if so, skips the unnecessary update.
*/
VOID cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::update_htc_params() {
  if (m_dyn_data.update_varying_physics_params()) { 
    auto pd = m_dyn_data.physics_descriptor();
    m_ambient_temp = pd->parameters()->ambient_temp.value;
    m_heat_xfer_coeff = pd->parameters()->ext_heat_xfer_coeff.value * g_density_scale_factor;
  }
}

/**
 @brief   Calculates change in energy produced by boundary edge and accumulates the result in the layer object
*/
VOID cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::compute_face_fluxes_func(const asINT32 time_index) {
  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = m_owner_surfel->shell_conduction_data(); 

  sdFLOAT group_voxel_size = sim_scale_to_voxel_size(m_owner_surfel->scale());
  sdFLOAT one_over_scaled_dist = 1./(group_voxel_size * this->normal_distance());
  const sdFLOAT surfel_dt = g_timescale.conduction_delta_t();

  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);

    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, this->local_normal());
    sdFLOAT K_elem = normal_conductivity * one_over_scaled_dist;

    // h_ext and wall_cond need to be correctly scaled when the surfel values are parsed
    sdFLOAT edge_temp = (K_elem * layer_data->temp(time_index) + m_heat_xfer_coeff * m_ambient_temp)
    / (K_elem + m_heat_xfer_coeff);

    sdFLOAT heat_flux = m_heat_xfer_coeff * (m_ambient_temp - edge_temp);

    sdFLOAT interface_area = this->length() * layer_data->thickness;
    sdFLOAT edge_dE = heat_flux * interface_area * surfel_dt;
    layer_data->accumulate_dE(edge_dE);
  }
}

/**
 @brief   Calculates and returns the corresponding layer's boundary temperature based on boundary info
*/
sdFLOAT cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::boundary_temperature(SHELL_LAYER_DATA layer_data, 
                                                                     const asINT32 time_index) {
  sdFLOAT group_voxel_size = sim_scale_to_voxel_size(m_owner_surfel->scale());
  sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, this->local_normal());
  sdFLOAT K_elem = normal_conductivity / group_voxel_size / this->normal_distance();

  // h_ext and wall_cond need to be correctly scaled when the surfel values are parsed
  return (K_elem * layer_data->temp(time_index) + m_heat_xfer_coeff * m_ambient_temp) / (K_elem + m_heat_xfer_coeff);
}

#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::assemble_rhs_func(sSURFEL* root_surfel) {
  const sdFLOAT* root_normal = this->local_normal(root_surfel);

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = root_surfel->shell_conduction_data();
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(root_surfel->scale());

  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = root_surfel->shell_conduction_implicit_solver_data();

  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT edge_length_finest_scale = root_scale_factor * this->length();
  sdFLOAT scaled_normal_distance = root_scale_factor * this->normal_distance();
  sdFLOAT dt_over_area = g_timescale.conduction_delta_t() / root_surfel_area;

  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = shell_conduction_data->layer(nth_layer);

    if (!layer_data->layer_matrix_data.system_row_needs_to_be_updated) {
      layer_data->layer_matrix_data.system_row_needs_to_be_updated = true;
    }

    sdFLOAT heat_capacity = root_scale_factor * layer_data->heat_capacity[prior_index];
    dt_over_area = dt_over_area / heat_capacity;
    sdFLOAT scaled_thickness = root_scale_factor * layer_data->thickness;
    sdFLOAT interface_area = edge_length_finest_scale * scaled_thickness;

    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(prior_index, root_normal);
    normal_conductivity = root_scale_factor * normal_conductivity;
    sdFLOAT K_elem = normal_conductivity /scaled_normal_distance;

    // Assemble contributions to the system. This BC contributes to both the flux matrix and the RHS
    PetscInt global_idx = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;

    // Diagonal contribution
    PetscReal diag_contribution = dt_over_area * interface_area * m_heat_xfer_coeff * K_elem / (K_elem + m_heat_xfer_coeff);
    layer_data->layer_matrix_data.row_idx_val[global_idx] += diag_contribution;

    // RHS contribution
    PetscReal rhs_contribution = dt_over_area * interface_area * m_heat_xfer_coeff * m_ambient_temp * (1.0 - m_heat_xfer_coeff / (K_elem + m_heat_xfer_coeff));

    if (!layer_data->layer_matrix_data.bc_needs_to_be_updated) {
      layer_data->layer_matrix_data.bc_needs_to_be_updated = true;
    }
    layer_data->layer_matrix_data.bc_vals += rhs_contribution;
  }
}
#endif


/**
 @brief   Constructs a cCONDUCTION_EDGE_TEMP_BOUNDARY object when using edge based LSQ implementation

 @details Must be passed a pointer to the non-shell surfel defining the boundary temperature
*/
cCONDUCTION_EDGE_TEMP_BOUNDARY::cCONDUCTION_EDGE_TEMP_BOUNDARY(sSURFEL* owner_surfel,
  dFLOAT e_length, sdFLOAT norm_dist, const sdFLOAT* norm_vec, sSURFEL* bc_surfel)
    : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec), m_boundary_surfel(bc_surfel) { }

/**
 @brief   Updates temperature stored in m_temp_or_flux from the nbr surfel's boundary info.
*/
VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::update_edge_temp() {
  cassert(m_boundary_surfel->conduction_data()->wall_temp > 0.);
  this->m_temp_or_flux = m_boundary_surfel->conduction_data()->wall_temp;
}

/**
 @brief   Adds contribution of a neighboring surfel to the sample temperature calculations.

 @details Updates the values for each layer in edge_temp_sample as well as the values of sum_weights
          and dist_sample.
*/
VOID add_layer_temps_to_sample(const sdFLOAT *vector_to_sample_loc, sSURFEL* surfel,
    SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data, sdFLOAT &sum_weights, sdFLOAT &dist_sample,
    std::vector<sdFLOAT>& edge_temp_sample) {

  sdFLOAT dist_to_sample_loc = vlength2(vector_to_sample_loc);
  sdFLOAT weight = approximate_pgram_dist_weighting(dist_to_sample_loc)
                 * approximate_pgram_volume_weighting(surfel->area);
  sum_weights += weight;
  dist_sample += weight * dist_to_sample_loc;

  ccDOTIMES (n, edge_temp_sample.size()) {
    edge_temp_sample[n] += weight * shell_conduction_data->get_layer_prior_temperature(n, surfel->scale());
  }
}

/**
@brief   Calculates sample temperature on the edge

@details The sampled temperature is calculated using:\n
         \f$ 
         T_{sample} = \frac{\sum_{ss} w_{ss} T_{ss}}{\sum_{ss} w_{ss}} 
         \f$\n
         where \f$ ss \f$ represents all surfels in the current surfel's stencil.
         The sampled distance is calculated in a similar manner, while the weight for each stencil surfel, 
	 \f$ w_{ss}\f$ is a product of the distance weighting and area weighting.
         The two weights are obtained using:\n
         \f$ w_d = 1.2 - 1.51 \delta x_{ss,scaled} 
                     - 1.32 \delta x_{ss,scaled}^2
                     + 2.58 \delta x_{ss,scaled}^3
                     - 0.95 \delta x_{ss,scaled}^4
         \f$\n
         and\n
         \f$ w_v = 0.53 A   + 0.93 A^2
                            - 0.68 A^3
                            + 0.40 A^4 
         \f$\n
         where \f$ \delta x_{ss,scaled} =\frac{ \delta x_{ss}}{1.65}\f$, \f$ \delta x_{ss} \f$ is the distance 
	 of the stencil surfel centroid from the edge and \f$A\f$ is the area of the stencil surfel.
*/
VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::get_sampled_temp_near_edge(std::vector<sdFLOAT>& edge_temp_sample,
    sdFLOAT& dist_sample) {

  // Compute edge sample values
  SURFEL_SHELL_CONDUCTION_DATA root_surfel_shell_conduction = m_owner_surfel->shell_conduction_data();
  asINT32 num_layers = root_surfel_shell_conduction->num_layers();

  // edge_to_stencil_surfel_vector = stencil_surfel->unfolded_centroid - edge_centroid_2d
  // since edge_centroid_2d was translated to coordinate system centered about root_surfel centroid
  // Both of these distances are at the local scale, so we dont require further rescaling
  sdFLOAT edge_centroid_2d[2];
  vscale2(edge_centroid_2d, this->normal_distance(), this->local_normal());
  sdFLOAT sum_weights = 0.0;

  // Using LSQ stencil of shell surfel to calculate a sampled temperature at the edge. We exclude
  // boundary information in this calculation and only use internal edge neighbors and vertex neighbors.
  // Since direct nbr is not in stencil, we add this first
  add_layer_temps_to_sample(edge_centroid_2d, m_owner_surfel, root_surfel_shell_conduction,
      sum_weights, dist_sample, edge_temp_sample);

  DO_INTERNAL_EDGE_STENCILS(root_surfel_shell_conduction, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    SURFEL nbr_surfel = nbr_stencil->surfel();
    SURFEL_SHELL_CONDUCTION_DATA stencil_surfel_shell_conduction = nbr_surfel->shell_conduction_data();
    const sdFLOAT* dist_btw_centroids = nbr_stencil->get_unfolded_centroid();
    sdFLOAT edge_to_stencil_surfel_vector[2];
    vsub2(edge_to_stencil_surfel_vector, dist_btw_centroids, edge_centroid_2d);
    add_layer_temps_to_sample(edge_to_stencil_surfel_vector, nbr_surfel, stencil_surfel_shell_conduction,
        sum_weights, dist_sample, edge_temp_sample);
  }

  DO_VERTEX_NEIGHBOR_STENCILS(root_surfel_shell_conduction, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR vert_nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    SURFEL nbr_surfel = vert_nbr_stencil->surfel();
    SURFEL_SHELL_CONDUCTION_DATA stencil_surfel_shell_conduction = nbr_surfel->shell_conduction_data();
    const sdFLOAT* dist_btw_centroids = vert_nbr_stencil->get_unfolded_centroid();
    sdFLOAT edge_to_stencil_surfel_vector[2];
    vsub2(edge_to_stencil_surfel_vector, dist_btw_centroids, edge_centroid_2d);
    add_layer_temps_to_sample(edge_to_stencil_surfel_vector, nbr_surfel, stencil_surfel_shell_conduction,
                              sum_weights, dist_sample, edge_temp_sample);
  }

  dist_sample = dist_sample / sum_weights;

  ccDOTIMES (n, num_layers) {
    edge_temp_sample[n] /= sum_weights;
  }
}

/**
 @brief   Calculates edge fluxes at the boundary edge and accumulates the dE in each shell layer.

 @details This implementation relies on sampling to estimate a temperature near the edge which is
          used to calculate the gradient. This sampling utilizes the unfolded centroid vectors from the
          stencils of the owner shell surfel with the vector from the edge to the owner surfel centroid
          added to obtain the vector between the edge and the stencil objects. These vectors are not
          guarenteed to represent the shortest path between the two locations and therefore can introduce
          error when the complexity of the surface increases.
*/
VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::compute_face_fluxes_func(const asINT32 time_index) {
  SURFEL_SHELL_CONDUCTION_DATA surfel_conduction_data = m_owner_surfel->shell_conduction_data(); 

  const sdFLOAT* local_normal = this->local_normal();
  const sdFLOAT surfel_dt = g_timescale.conduction_delta_t();

  std::vector<sdFLOAT> edge_temp_sample(surfel_conduction_data->num_layers(), 0.0);
  sdFLOAT dist_sample = 0.0;
  this->get_sampled_temp_near_edge(edge_temp_sample, dist_sample);
  ccDOTIMES(nth_layer, surfel_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = surfel_conduction_data->layer(nth_layer);
    // For prescribed temp BC, compute effective grad_t as a FD form
    // effective temp is directly the BC value
    sdFLOAT effective_grad_t_at_edge = (edge_temp_sample[nth_layer] - m_temp_or_flux) / dist_sample;
    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(time_index, local_normal);
    sdFLOAT interface_area = this->length() * layer_data->thickness;
    sdFLOAT edge_dE = -normal_conductivity * effective_grad_t_at_edge * interface_area * surfel_dt;
    layer_data->accumulate_dE(edge_dE);

    sdFLOAT heat_capacity = layer_data->heat_capacity[time_index];
    sdFLOAT dt_over_area = surfel_dt / m_owner_surfel->area / heat_capacity;
  }
}


#if !GPU_COMPILER && !BUILD_GPU
VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::assemble_rhs_func(sSURFEL* root_surfel) {
  const sdFLOAT* root_normal = this->local_normal(root_surfel);

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = root_surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = root_surfel->shell_conduction_implicit_solver_data();

  asINT32 root_scale_diff = -scale_to_log2_voxel_size(root_surfel->scale());
  sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
  sdFLOAT root_surfel_area = root_surfel->area * root_scale_factor * root_scale_factor;
  sdFLOAT edge_length_finest_scale = root_scale_factor * this->length();
  sdFLOAT dt_over_area = g_timescale.conduction_delta_t() / root_surfel_area;

  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(root_surfel->scale());
  
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA layer_data = shell_conduction_data->layer(nth_layer);

    if (!layer_data->layer_matrix_data.system_row_needs_to_be_updated) {
      layer_data->layer_matrix_data.system_row_needs_to_be_updated = true;
    }

    sdFLOAT normal_conductivity = layer_data->edge_normal_conductivity(prior_index, root_normal); // value of prior_index doesn't matter since assembly is happening before anything else 
    normal_conductivity = root_scale_factor * normal_conductivity;
    sdFLOAT scaled_normal_distance = root_scale_factor * this->normal_distance();
    sdFLOAT conductivity_over_distance = normal_conductivity / scaled_normal_distance;
    sdFLOAT heat_capacity = root_scale_factor * layer_data->heat_capacity[prior_index];
    dt_over_area = dt_over_area / heat_capacity;
    sdFLOAT scaled_thickness = root_scale_factor * layer_data->thickness;
    sdFLOAT interface_area = edge_length_finest_scale * scaled_thickness;

    // Assemble contributions to the system. This BC contributes to both the flux matrix and the RHS
    PetscInt global_idx = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;

    // This contributes to the diagonal terms of the flux matrix
    PetscScalar boundary_flux_matrix_contribution = dt_over_area * interface_area * conductivity_over_distance;
    layer_data->layer_matrix_data.row_idx_val[global_idx] += boundary_flux_matrix_contribution;

    // This is the RHS contribution. The sign is positive b/c the term has to be on the RHS
    // Note that this sign is the opposite of the previous contribution if it were on the same side of the equation.
    PetscScalar boundary_flux_rhs_contribution = boundary_flux_matrix_contribution * this->m_temp_or_flux;

    if (!layer_data->layer_matrix_data.bc_needs_to_be_updated) {
      layer_data->layer_matrix_data.bc_needs_to_be_updated = true;
    }
    layer_data->layer_matrix_data.bc_vals += boundary_flux_rhs_contribution;
  }
}

VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {
  // Only doing the root surfel because there is no nbr surfel.
  ccDOTIMES(i, 2) {
    if (root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i].count(column_index) == 0) {
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] = 0.0;
    }
    root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] -= scale_factor * stencil_surfel_lsq_coeff[i];
  }
}

VOID cCONDUCTION_EDGE_TEMP_BOUNDARY::compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel) {
  //Imlicit shell solver: This function isn't used with the current implementation. If the implementation used DO_EDGE_NEIGHBOR_STENCILS,
  //then this would take the role of the implicit_solver_assemble_rhs(). This should be updated when the implicit shell
  //solver code is reorganized.
}

#endif //!GPU_COMPILER && !BUILD_GPU


cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY::cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY(sSURFEL* owner_surfel,
  dFLOAT e_length, sdFLOAT norm_dist, const sdFLOAT* norm_vec, std::vector<sSURFEL*>& bc_surfels)
    : cCONDUCTION_EDGE_TEMP_BOUNDARY(owner_surfel, e_length, norm_dist, norm_vec) {
  cassert(bc_surfels.size() == 2);
  m_boundary_surfel_1 = bc_surfels[0];
  m_boundary_surfel_2 = bc_surfels[1];
}

VOID cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY::update_average_temp() {
  cassert(m_boundary_surfel_1->conduction_data()->wall_temp > 0.);
  cassert(m_boundary_surfel_2->conduction_data()->wall_temp > 0.);
  this->m_temp_or_flux = (m_boundary_surfel_1->conduction_data()->wall_temp
                        + m_boundary_surfel_2->conduction_data()->wall_temp) / 2.;
}

/**
 @brief   Constructs a cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY object

 @details Must be passed the neighboring conduction surfel that contains the physics descriptor defining the temp bc
*/
cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY::cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel,
                                                                                 dFLOAT e_length,
                                                                                 sdFLOAT norm_dist,
                                                                                 const sdFLOAT* norm_vec,
                                                                                 sSURFEL* bc_surfel)
    : cCONDUCTION_EDGE_TEMP_BOUNDARY(owner_surfel, e_length, norm_dist, norm_vec), 
      m_dyn_data(owner_surfel, bc_surfel, norm_dist, norm_vec), m_bnd_surfel(bc_surfel) {
  auto pd = m_dyn_data.physics_descriptor();
  this->m_temp_or_flux = pd->parameters()->temperature.value;
}

/**
 @brief   Updates temperature stored in m_temp_or_flux from the nbr surfel's boundary info.
*/
VOID cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY::update_dyn_data_temp() {
  if (m_dyn_data.update_varying_physics_params()) { 
    auto pd = m_dyn_data.physics_descriptor();
    this->m_temp_or_flux = pd->parameters()->temperature.value;
  }
}

asINT32 cSHELL_CONDUCTION_STENCIL_INFO::get_root_surfel_start_index(sSURFEL* root_surfel) {
  return root_surfel->shell_conduction_data()->get_stencil_start_index();
}

/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
///////////                                                                       ///////////
///////////           Functions to print edge information for debugging           ///////////
///////////                                                                       ///////////
/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////


asINT32 dyn_type_from_ghost_surfel_dynamics(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data) {
  CONDUCTION_PHYSICS_DESCRIPTOR phys_desc = (CONDUCTION_PHYSICS_DESCRIPTOR)(dyn_data->physics_descriptor());
  cSTRING name = phys_desc->name;
  CDI_PHYS_TYPE_DESCRIPTOR cdi_desc = phys_desc->phys_type_desc;
  switch (cdi_desc->cdi_physics_type) {
    case CDI_PHYS_TYPE_ADIABATIC:
      return CONDUCTION_ADIABATIC_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_TEMP:
      return CONDUCTION_FIXED_TEMP_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
      return CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
      return CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE;
    case CDI_PHYS_TYPE_CONTACT_RESISTANCE:
      return CONDUCTION_CONTACT_SURFEL_TYPE;
    case CDI_PHYS_TYPE_COUPLED_THERMAL:
      return CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE;
    default:
      return 0;
  }
}

asINT32 get_dynamics_type_int(sSURFEL* surfel) {
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data = surfel->dynamics_data();
  asINT32 dynamicsType = dyn_data->m_dynamics_type;
  if (dynamicsType == 0) {
    tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data = surfel->dynamics_data();
    dynamicsType = dyn_type_from_ghost_surfel_dynamics(dyn_data);
  }
  return dynamicsType;
}

std::string get_dynamics_type_name(sSURFEL* surfel) {
  asINT32 dynType = get_dynamics_type_int(surfel);
  switch (dynType) {
    case eDYN_SURFEL_TYPE::CONDUCTION_ADIABATIC_SURFEL_TYPE:
      return std::string("Adiabatic");
    case eDYN_SURFEL_TYPE::CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
      return std::string("FixedTemp");
    case eDYN_SURFEL_TYPE::CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
      return std::string("HTC");
    case eDYN_SURFEL_TYPE::CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
      return std::string("FixedFlux");
    case eDYN_SURFEL_TYPE::CONDUCTION_CONTACT_SURFEL_TYPE:
      return std::string("ThermalContact");
    case eDYN_SURFEL_TYPE::CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE:
      return std::string("CoupledThermal");
    default:
      msg_internal_error("dynamics type = %d unknown",dynType);
  }
  return std::string("");
}

std::ostream& print_basic_surfel_info(std::ostream &out, sSURFEL* surfel, std::string &&tabSize) {
  asINT32 dynTypeget_dynamics_type_int;
  std::string dynamicsTypeName = get_dynamics_type_name(surfel);
  if (surfel->is_conduction_open_shell()) {
    out << tabSize << "SurfelType OpenShell, Surfel " << surfel->id() << ", FaceID " << surfel->m_face_index
        << ", Layers " << (asINT32)(surfel->shell_conduction_data()->num_layers());
  } else if (surfel->is_conduction_shell()) {
    out << tabSize << "SurfelType ClosedShell, Surfel " << surfel->id() << ", FaceID " << surfel->m_face_index
        << ", Layers " << (asINT32)(surfel->shell_conduction_data()->num_layers());
  } else {
    out << tabSize << "SurfelType CondSurfel, Surfel " << surfel->id() << ", FaceID " << surfel->m_face_index 
        << ", DynType " << dynamicsTypeName;
  }
  return out;
}


std::ostream& cCONDUCTION_EDGE_INTERNAL::print_edge_info(std::ostream &out) {
  out << "    EdgeType Internal\n      OwnerSurfel:\n";
  print_basic_surfel_info(out, this->m_owner_surfel, "        "); 
  out << "\n      NbrSurfel:\n";
  print_basic_surfel_info(out, this->m_nbr_surfel, "        "); 
  out << "\n      RotationAngle " << m_rotation_angle << ", IsSwitched " << m_is_switched << ", ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}


std::ostream& cCONDUCTION_EDGE_INTERSECTION::print_edge_info(std::ostream &out) {
  out << "    EdgeType Intersection\n      Surfels " << m_nbr_surfels->size() << ":\n";
  ccDOTIMES(i, m_nbr_surfels->size()) {
    print_basic_surfel_info(out, m_nbr_surfels->operator[](i), "        ");
    out << ", Distance " << m_nbr_dists[i] << ", Rotation " << m_rot_angles[i] << "\n";
  }
  out << "      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_ADIABATIC_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType Adiabatic\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        ");
  out << "\n      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType FluxBoundary\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        ");
  out << "\n      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType HTCDynBoundary\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        ");
  out << "\n      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType TempDynBoundary\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        ");
  out << "\n      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_TEMP_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType TemperatureBoundary\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        ");
  out << "\n      BoundarySurfel:\n";
  print_basic_surfel_info(out,this->m_boundary_surfel, "        ");
  out << "\n      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY::print_edge_info(std::ostream &out) {
  out << "    EdgeType MultiTempBoundary\n      OwnerSurfel:\n";
  print_basic_surfel_info(out,this->m_owner_surfel, "        "); 
  out << "\n      BoundarySurfels 2:\n";
  print_basic_surfel_info(out,m_boundary_surfel_1, "        ");
  out << "\n";
  print_basic_surfel_info(out,m_boundary_surfel_2, "        ");
  out << "\n";
  out << "      ";
  return cCONDUCTION_EDGE_BASE::print_edge_info(out);
}

std::ostream& cSHELL_CONDUCTION_STENCIL_NEIGHBOR::print_edge_info(std::ostream &out) {
  if (m_edge == NULL) {
    out << "  VertexNeighbor:\n";
    print_basic_surfel_info(out, m_surfel, "    ");
    out << "\n    LSQCoefficient (" << m_lsq_coeff[0] << " " << m_lsq_coeff[1] << "), "
      << "UnfoldedCentroid (" << m_unfolded_centroid[0] << " " << m_unfolded_centroid[1] << ")";
  } else {
    out << "    LSQCoefficient (" << m_lsq_coeff[0] << " " << m_lsq_coeff[1] << "), "
        << "UnfoldedCentroid (" << m_unfolded_centroid[0] << " " << m_unfolded_centroid[1] << ")" << std::endl;
    out << m_edge;
  }
  return out;
}

sdFLOAT cCONDUCTION_EDGE_INTERNAL::cellSizeRatio() {
  const asINT32 scale_diff = m_nbr_surfel->scale() - m_owner_surfel->scale();
  sdFLOAT scaleVal = (scale_diff >= 0) ? (1.0 / (1 << scale_diff)) : (1 << -scale_diff);
  sdFLOAT nbr_area = m_nbr_surfel->area * scaleVal * scaleVal;
  sdFLOAT owner_area = m_owner_surfel->area;
  return MIN(nbr_area, owner_area) / (MAX(nbr_area,owner_area)+SDFLOAT_EPSILON);
}

sdFLOAT cCONDUCTION_EDGE_INTERNAL::nonOrthogonality() {
  // dFLOAT nonOrtho = fabs(atan2(vcross2(m_unfolded_centroid, this->local_normal()),
  //                          vdot2(m_unfolded_centroid, this->local_normal())));
  sdFLOAT nVec[2];
  sdFLOAT ds[2];
  vcopy2(nVec, this->local_normal());
  vcopy2(ds, m_unfolded_centroid);
  sdFLOAT nVecLen = vlength2(nVec);
  sdFLOAT dsLen = vlength2(ds);
  sdFLOAT dotProd = vdot2(nVec, ds);
  sdFLOAT cosTheta = dotProd / (nVecLen*dsLen+SDFLOAT_EPSILON);
  sdFLOAT nonOrtho = acos(MIN(cosTheta,1.0));
  return nonOrtho;
}
