/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "sync_threads.h"

sSYNC_THREADS::sSYNC_THREADS() {
  pthread_mutex_init(&m_comm_thread_mutex, NULL);
  pthread_cond_init(&m_comm_thread_cv, NULL);
  pthread_mutex_init(&m_compute_thread_mutex, NULL);
  pthread_cond_init(&m_compute_thread_cv, NULL);
  m_is_compute_thread_awake = TRUE;
}

VOID sSYNC_THREADS::wake_up_comm_thread() {
  pthread_mutex_lock(&m_comm_thread_mutex);
  pthread_cond_signal(&m_comm_thread_cv);
  pthread_mutex_unlock(&m_comm_thread_mutex);
}

VOID sSYNC_THREADS::wake_up_compute_thread() {
  pthread_mutex_lock(&m_compute_thread_mutex);
  m_is_compute_thread_awake = TRUE;
  pthread_cond_signal(&m_compute_thread_cv);
  pthread_mutex_unlock(&m_compute_thread_mutex);
}

timespec sSYNC_THREADS::set_timeout(SP_SLEEP_INDEX sleepIndex) {
  timespec timeout;
  struct timeval now;
  gettimeofday(&now, NULL);
  timeout.tv_sec = now.tv_sec;
  timeout.tv_nsec = now.tv_usec * 1000 + g_sp_sleep_ns[sleepIndex];
  if (timeout.tv_nsec >= 1000000000) {
    timeout.tv_nsec -= 1000000000;
    timeout.tv_sec++;
  }
  return timeout;
}

VOID sSYNC_THREADS::wait_for_compute_thread(SP_SLEEP_INDEX sleepIndex) {
  timespec timeout = set_timeout(sleepIndex);
  pthread_mutex_lock(&m_comm_thread_mutex);
  pthread_cond_timedwait(&m_comm_thread_cv, &m_comm_thread_mutex, &timeout);
  pthread_mutex_unlock(&m_comm_thread_mutex);
}

VOID sSYNC_THREADS::wait_for_comm_thread() {
  pthread_mutex_lock(&m_compute_thread_mutex);
  m_is_compute_thread_awake = FALSE;
  while (m_is_compute_thread_awake == FALSE) {
    pthread_cond_wait(&m_compute_thread_cv, &m_compute_thread_mutex);
  }
  pthread_mutex_unlock(&m_compute_thread_mutex);
}
