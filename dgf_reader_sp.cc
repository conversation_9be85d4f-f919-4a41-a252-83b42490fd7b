/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

//----------------------------------------------------------------------------
// David Hall                                             Dec 10, 2008
//----------------------------------------------------------------------------
#include "common_sp.h"
#include "dgf_reader_sp.h"
#include "parse_shob_descs.h"
#include "phys_type_map.h"
#include "sim.h"
#include "random.h"
#include "mlrf.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "fan.h"
#include "meas.h"
#include "shob_groups.h"
#include "ublk_table.h"
#include "surfel_table.h"
#include "bsurfel_table.h"
#include "strand_mgr.h"
#include "comm_groups.h"
#include "thread_run.h"
#include "box_advect.h"
#include "bsurfel.h"
// #include "conduction_contact_averaging.h"
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_sim.h"
#include "particle_sim_info.h"
#include "trajectory_meas.h"
#include "trajectory_window.h"
#include "read_pm_lgi_records.h"
extern sPARTICLE_SIM particle_sim;
extern std::vector<TRAJECTORY_WINDOW> g_trajectory_windows;
#include PHYSICS_H

//#include "hdf5_writer.h"

//#endif
sDGF_READER g_dgf_reader;
LGI_STREAM g_lgi_stream;

//----------------------------------------------------------------------------
// open_dgf_stream
//----------------------------------------------------------------------------
VOID sDGF_READER::open_dgf_stream() 
{
  g_lgi_stream = sp_lgi_open_cp_input_stream(SIMENG_LGI_BUFSIZE);
}

//----------------------------------------------------------------------------
// close_dgf_stream
//----------------------------------------------------------------------------
VOID sDGF_READER::close_dgf_stream() 
{
  lgi_close_stream(g_lgi_stream);
}

static VOID read_eqn_rand_state()
{
  asINT32 rand_state_size = exprlang_rand_state_size();
  
  LGI_RANDOM_SEED_REC record_head;
  lgi_read_next_head(g_lgi_stream, record_head);

  if (record_head.n_bytes_rand_state != rand_state_size)
    msg_internal_error("Size of EQN random number generator state disagrees"
                       " with the internal simulator state");
  
  char *rand_state = exprlang_rand_state();
  lgi_read(g_lgi_stream, rand_state, rand_state_size);

  char *randt_state = exprlang_randt_state();
  lgi_read(g_lgi_stream, randt_state, rand_state_size);
}

static VOID read_start_and_end_time()
{
  DGF_START_END_TIME_REC record;

  record.read(g_lgi_stream);

  /* Current simulation base time */
  /* If re-starting from a time ahead of g_timescale.init_time(), needs to loop through the time coupling phases to get
  the base time right, and determine the value of the counters keeping track of of how many timesteps each realm has
  completed till reaching current phase */
  if (record.start_time > g_timescale.m_base_time) {
    size_t idx_prev_phase = 0;
    BOOLEAN set_tm_reference = FALSE;
    BASETIME phase_time_0 = 0;
    TIMESTEP time_0_flow = 0, time_0_cond = 0, wsurfel_cntr_0=0;
    g_timescale.set_current_time(record.start_time);
    while (sim.time_coupling_info.maybe_update_time_coupling_phase()) {
      const sTIME_COUPLING_PHASE& phase = sim.time_coupling_info.active_phase();
      if (phase.time_coupling == eTIME_COUPLING_SCHEME::FreezeOne && 
          ((!sim.is_conduction_sp && phase.frozen_solver == eCOUPLED_SOLVER::FlowSolver) ||
          (sim.is_conduction_sp && phase.frozen_solver == eCOUPLED_SOLVER::ConductionSolver))) {
        //skip to next phase since realm handled by this SP is not active
        g_timescale.set_current_time(sim.time_coupling_info.next_phase_start(BASETIME_LAST));
      } 
      //update the number of timesteps that the flow or conduction have traversed in the previous phases
      size_t idx_this_phase = sim.time_coupling_info.idx_this_phase();
      for (size_t idx = idx_prev_phase; idx < idx_this_phase; idx++) {
        sTIME_COUPLING_PHASE& prev_phase = sim.time_coupling_info.m_phases[idx];
        BASETIME prev_phase_span = sim.time_coupling_info.m_phases[idx+1].start - prev_phase.start;
        BOOLEAN is_frozen = (prev_phase.time_coupling == eTIME_COUPLING_SCHEME::FreezeOne);
        TIMESTEP time_inc_flow = g_timescale.n_flow_base_steps(prev_phase.therm_time_ratio);
        TIMESTEP time_inc_cond = g_timescale.n_cond_base_steps(prev_phase.therm_time_ratio);
        //flow realm (if active)
        if (!is_frozen || prev_phase.frozen_solver != eCOUPLED_SOLVER::FlowSolver) {
          //phase spans adjusted by CP so are exact multiples of the flow and conduction, no need to cast to double
          time_0_flow += prev_phase_span / time_inc_flow; 
        }
        //conduction realm (if active)
        if (!is_frozen || prev_phase.frozen_solver != eCOUPLED_SOLVER::ConductionSolver) {
          //phase spans adjusted by CP so are exact multiples of the flow and conduction, no need to cast to double
          time_0_cond += prev_phase_span / time_inc_cond; 
        }
        //cross-realm comm, occuring at the pace of the solver with larger time-step when both solvers are active
        if (prev_phase.time_coupling == eTIME_COUPLING_SCHEME::SameRate ||
            prev_phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRate ||
            prev_phase.time_coupling == eTIME_COUPLING_SCHEME::DifferentRateConservative) {
          wsurfel_cntr_0 += prev_phase_span / std::max(time_inc_flow, time_inc_cond);
        }
        //if frozen, need to adjust the tm to account for the iterations skipped for the realm frozen
        set_tm_reference |= (prev_phase.time_coupling != eTIME_COUPLING_SCHEME::SameRate);
      }
      //set the new update ratio (internally updates m_base_time_inc & m_time_inc)
      BOOLEAN update_time_ratio = (std::abs(phase.therm_time_ratio - g_timescale.m_therm_time_ratio) > 1.0e-06);
      set_tm_reference |= update_time_ratio;
      if (update_time_ratio) g_timescale.set_therm_time_ratio(phase.therm_time_ratio, phase.radiation_update_ratio);
      phase_time_0 = phase.start;
    }
    //updates the timestep counters accordingly for the phase we are in
    TIMESTEP timestep_cntr_0 = sim.is_conduction_sp ? time_0_cond : time_0_flow;
    g_timescale.set_current_timestep_ctrs(sim.is_conduction_sp, phase_time_0, timestep_cntr_0, wsurfel_cntr_0);
    //once it has the correct time, stores it as start time for this SP
    g_timescale.m_start_time = g_timescale.m_time;
    //finally, initializes the reference for the different solver timemanagers if needed
    if (set_tm_reference) {
      g_timescale.set_solver_tm_time_reference(phase_time_0, time_0_flow, time_0_cond);
    }
  }

  //Sets also the end_time specified for the simulation
  g_timescale.m_end_time = record.end_time;
  
  // Compute which scales for this timestep will do dynamics. Initialize the timestep managers before launching the comm
  // thread, and before seeding, since the time-step manager should be initialized before reading checkpoint measurement
  // windows from the lgi stream and doing checkpoint restore. See the comment in seed_shobs() for more details.
  sim.set_acous_switch();
  if (sim.switch_acous_during_simulation) {
    g_timescale.init_solver_tm_before_lighthill_on(FINEST_SCALE);
  }
  else {
    g_timescale.init_solver_tm(FINEST_SCALE);
  }
  g_timescale.update_solver_status(false, false); //initializes solver status to report correct timesteps to CP

  if (sim.is_full_checkpoint_restore) {
    determine_if_equilibrium_ckpt();   // sim.time must be set
  }

  set_randt_initial_update_time(g_timescale.m_time);
}

static VOID read_momentum_freeze_params()
{
  DGF_MOMENTUM_FREEZE_REC record;

  record.read(g_lgi_stream);

  /* Params of momentum freeze solver */
  sim.m_freeze_momentum_parms.m_is_on      = record.freeze_momentum_field;
  sim.m_freeze_momentum_parms.m_start_time = record.momentum_freeze_start_time;//will be overridden by start_time
  sim.m_freeze_momentum_parms.m_thermal_timestep_ratio = record.thermal_timestep_ratio;

#if BUILD_D19_LATTICE
  if (sim.m_freeze_momentum_parms.m_is_on) {
    //enforce no farctional advection
    g_adv_fraction = g_adv_fraction_mf;
    g_one_over_adv_fraction = 1.0 / g_adv_fraction;

    if (g_adv_fraction <1.0 && !g_use_feq_for_frac_adv)
      g_is_frac_adv = 1;
    else
      g_is_frac_adv = 0;

    //modify global parameters for MF
    g_T_underrelax = g_T_underrelax_mf; //enforce to be 1.0 with freeze_momentum
    g_scatter_omega = g_scatter_omega_mf;
    g_u_scatter_n = g_u_scatter_n_mf;
    g_delta_temp_alpha = g_delta_temp_alpha_mf;
    g_T_over_T0_underrelax = g_T_over_T0_underrelax_mf;

    if (g_ke_timestep_coeff <= 0.)
      g_ke_timestep_coeff = 1.0;   //defaulted as 1.0 for numerical stability

    if (g_uds_timestep_coeff <= 0.)
      g_uds_timestep_coeff = sim.m_freeze_momentum_parms.m_thermal_timestep_ratio;

    if (g_physical_time_scale_override > 0 ){
      g_uds_timestep_coeff = g_physical_time_scale_override;
      sim.m_freeze_momentum_parms.m_thermal_timestep_ratio = g_physical_time_scale_override;
    }
    asINT32 large_int = 1 << 30;
    if (my_proc_id == 0)
      msg_print_no_prefix("thermal_timestep_ratio = %g\n", sim.m_freeze_momentum_parms.m_thermal_timestep_ratio);
  }
#else
  sim.m_freeze_momentum_parms.m_is_on = FALSE;
#endif
}

static VOID read_large_pore_params()
{
  DGF_LARGE_PORE_REC record;
  
  record.read(g_lgi_stream);

  /* 5G large pore */
  sim.is_large_pore = record.is_large_pore_sim;  //set FALSE for non 5G in CP

#if BUILD_5G_LATTICE
  if(!sim.is_large_pore || !g_mp_pm_table_input)
    g_mp_pm_table_input_contact_angle = 0;
  else if( !g_mp_pm_table_input_contact_angle && g_mp_pm_table_input )
    msg_warn("wettability in the PM region is set via the bin file or set as the default value (10 degree).");
#endif


  if (sim.is_large_pore) {
#if BUILD_5G_LATTICE
    dassert(g_adv_fraction == 1.0);

    /* if (g_is_multi_component) { */
      // //modify the following parameters for converting Pc from Psi to lattice unit
      // sdFLOAT PC_unit_factor = 6894.76 * 0.025 * sim.meters_per_cell /3.0E-2;  // Psi -> lattice_pressure
      // g_mp_pm_pc_coef *= PC_unit_factor;
      // g_mp_pm_pc_shft *= PC_unit_factor;
      // g_mp_pm_pc_branchcoefa *= PC_unit_factor;
      // g_mp_pm_pc_branchcoefc *= PC_unit_factor;
    /* } */ //conversion could be reverted if controller copies user_input.init to user_input.init.ts

    if(g_mp_pm_kr_simulation==1){
      g_mp_pm_wetbranch_op=3;
    }   
    /*if (my_proc_id == 0)
      msg_print_no_prefix("using large pore solver.\n");*/
#else
    msg_internal_error("Simulator does not support large pore case."); 
#endif
  }
}

static VOID read_avg_mme_params()
{
  DGF_AVG_MME_REC record;

  record.read(g_lgi_stream);

  g_full_ckpt_with_avg_mme = record.full_ckpt_with_avg_mme;
}

static VOID read_local_vel_freeze_params()
{
  DGF_LOCAL_VEL_FREEZE_REC record;

  record.read(g_lgi_stream);

  g_full_ckpt_with_frozen_vars = record.full_ckpt_with_frozen_vars;
}

static VOID read_vapor_transport_params()
{
  DGF_FILM_VAPOR_REC record;

  record.read(g_lgi_stream);

  /* water vapor transport */
  sim.is_water_vapor_transport           = record.is_water_vapor_transport;

  /* accelerate water vapor transport
   * if aftd is not found in the cdi file then film_acceleration_factor = 1 (no acceleration)
   * otherwise aftd is multiplier for the film acceleration
   * 
   * This relies on the fact that aftd information is sent LAST from the CP
   * This should probably be done automatically by powercase ( rather than here )
   * */
  dFLOAT film_accleration_multiplier = record.film_acceleration_factor;
  //sim.film_acceleration_factor = film_accleration_multiplier * sim.m_freeze_momentum_parms.m_thermal_timestep_ratio * adv_fraction;
  sim.film_acceleration_factor = film_accleration_multiplier * sim.m_freeze_momentum_parms.m_thermal_timestep_ratio;
  if (g_acceleration_multiplier > 0 ){
    sim.film_acceleration_factor = g_acceleration_multiplier * sim.m_freeze_momentum_parms.m_thermal_timestep_ratio;
  }
  if (g_physical_time_scale_override > 0 ){
    if (g_acceleration_multiplier > 0 )
      sim.film_acceleration_factor = g_acceleration_multiplier* g_physical_time_scale_override;
    else
      sim.film_acceleration_factor = film_accleration_multiplier * g_physical_time_scale_override;
  }


#if BUILD_D19_LATTICE
  if (sim.is_water_vapor_transport) {
    g_is_defogging = 1.0; //enforce to be 1.0 with freeze_momentum
  }
#else
  sim.is_water_vapor_transport= FALSE;
  sim.film_acceleration_factor= FALSE;
#endif
}
//----------------------------------------------------------------------------
// Realistic wind calibration params
//----------------------------------------------------------------------------

static VOID read_calibration_params()
{
  DGF_RW_CALIBRATION_PARAMETERS_REC record;
  record.read(g_lgi_stream);
  sim.calibration_params.init(record);
  sim.prepare_calibration_run = TRUE;
  g_timescale.m_end_time = record.clbr_end_time;
  sim.calibration_params.calib_period = g_timescale.m_end_time / sim.calibration_params.iterations;
  if(sim.is_full_checkpoint_restore) {
    sim.calibration_params.iteration_number = (g_timescale.m_time / sim.calibration_params.calib_period) + 1;
  }
}
//----------------------------------------------------------------------------
// read_file
//----------------------------------------------------------------------------
VOID sDGF_READER::read_records_from_stream()
{
  open_dgf_stream();

  // The LGI header should arrive first
  BOOLEAN is_wrong_endian_ok = FALSE;
  auINT32 version = lgi_read_header(g_lgi_stream, is_wrong_endian_ok);
  // @@@ validate_version(version);

  // Read records as they come. Identify them by their tags and send them off to be processed
  BOOLEAN stream_contains_data = TRUE;
  while(stream_contains_data) {
    LGI_TAG tag = lgi_peek_tag(g_lgi_stream);
    // msg_print("Reading tag %d : %s", tag.id, lgi_tag_namestring(tag.id));
    switch(tag.id)
    {
    case LGI_ERROR_TAG:
      msg_error("lgi error.");
      stream_contains_data = false; 
      break;

    case LGI_EOF_TAG:
      stream_contains_data = false;
      break;

    case DGF_GLOBAL_PART_NAMES_TAG:
      read_global_part_names();
      break;

    case DGF_GLOBAL_FACE_NAMES_TAG:
      read_global_face_names();
      break;

    case DGF_MEAS_WINDOW_TAG:
      read_meas_windows();
      break;

    case DGF_QUAD_MESH_HEADER_TAG:
      // quad mesh info for curved porous hxch inlet reference face
      read_quad_mesh_info();
      break;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      case DGF_VERTICES_TAG:
        read_vertices_header(); // only the record header is sent at this point
        break;
//#endif

    case DGF_COUPLING_MEAS_WINDOW_TAG:
      read_coupling_meas_window();
      break;

    case DGF_COUPLING_MODELS_TAG:
      read_coupling_models(g_lgi_stream);
      break;

    case DGF_ROTATIONAL_DYNAMICS_HEADER_TAG:
      read_rotational_dynamics_descs(g_lgi_stream);
      break;

    case DGF_CONTROL_TAG:
      read_control_record();
      break;

    case DGF_SEED_INFO_TAG:
      read_smart_seed_control_record();
      break;

    case DGF_SMART_SEED_LRF_INITIAL_ROTATION_TAG:
      read_smart_seed_initial_lrf_rotations();
      break;

    case DGF_SMART_SEED_MOVB_INITIAL_XFORM_TAG:
      read_smart_seed_initial_movb_xforms();
      break;

    case DGF_NEIGHBOR_MASK_TABLE_TAG:
      read_neighbor_mask_table();
      break;
      
    case DGF_FLOW_UBLK_TABLE_TAG:
      read_ublk_table<cDGF_FLOW_UBLK_PROC>(STP_FLOW_REALM);
      break;

    case DGF_COND_UBLK_TABLE_TAG:
      read_ublk_table<cDGF_COND_UBLK_PROC>(STP_COND_REALM);
     break;

    case DGF_DEFORMING_TIRE_TABLE_TAG:
      read_deforming_tire_table();
      break;

    case DGF_BSURFEL_TABLE_TAG:
      read_bsurfel_table();
      break;

    case LGI_MEAS_WINDOW_VARS_TAG:
      read_meas_vars_list();
      break;

    case LGI_MEAS_CELL_REFERENCE_FRAME_CONFLICTS_TAG : {
      read_meas_cell_reference_frame_conflicts();
      //
      // This section has been moved down from DGF_UBLK_TABLE_TAG
      // Now, the third bit in meas_cell_ptr is filled in resolve_meas_cell_ptrs
      // based on information received via read_reference frame conflicts
      //
      // A variety of meas window init must be finished after all ublks and surfels are read.
      DO_MEAS_WINDOWS(window) {
        if (window->n_stationary_meas_cells > 0)
          window->finish_init(); // allocates meas cells, etc
      }

      DO_SCALES_FINE_TO_COARSE(scale) {

        DO_NEARBLKS_OF_SCALE(nearblk, scale) {
          nearblk->resolve_meas_cell_ptrs();
        }
        DO_FARBLKS_OF_SCALE(farblk, scale) {
          farblk->resolve_meas_cell_ptrs();
        }
        DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
          surfel->resolve_meas_cell_ptrs();
        }
        DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
          surfel->resolve_meas_cell_ptrs();
        }
        { DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
            wsurfel_conduction->resolve_meas_cell_ptrs();
        } }
        { DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
            wsurfel_flow->resolve_meas_cell_ptrs();
        } }
        { DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
            contact_surfel->resolve_meas_cell_ptrs();
        } }
        {
          DO_ISURFEL_PAIRS_OF_SCALE(surfel_pair, scale) {
            surfel_pair->resolve_meas_cell_ptrs();
          }
        }
        DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
          sampling_surfel->resolve_meas_cell_ptrs();
        }
        // resolve meas cell ptrs for ghost sampling surfels
        // These are used in Particle modeling.
        DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
          for (const auto& quantum: surfel_recv_group->quantums()) {
            SAMPLING_SURFEL sampling_surfel = quantum.m_surfel;
            sampling_surfel->resolve_meas_cell_ptrs();
          }
        }
      }

      DO_MEAS_WINDOWS(window1)
        if (window1->n_stationary_meas_cells > 0) {
          if (!(window1->is_development && sim.n_movb_physics_descs > 0))
            window1->free_meas_cell_index_map();
          window1->meas_cell_output_in_local_csys.clear();
        }

      break;
    }

    case LGI_MEAS_WINDOW_MASTER_SP_TAG:
      read_meas_window_master_sps_and_output_times();
      break;
      
    case DGF_FLOW_SURFEL_TABLE_TAG:
      read_surfel_table(STP_FLOW_REALM);
      //some flow wsurfels in open shells might not have been added to a group yet
      add_dangling_flow_wsurfels_to_groups();
      //some send groups may not belong to any surfel group.
      assign_dangling_send_groups_to_surfel_groups(STP_FLOW_REALM);
      initialize_surfel_group_fsets();
      if (sim.n_seed_from_meas_descs > 0) {
        read_seed_from_meas_scale_factors();
      }
      break;

    case DGF_COND_SURFEL_TABLE_TAG:
      read_surfel_table(STP_COND_REALM);
      //ensures that all paired contact surfels are loaded correctly
      sanity_check_paired_contact_surfels(); 
      //some send groups may not belong to any surfel group.
      assign_dangling_send_groups_to_surfel_groups(STP_COND_REALM);
      initialize_surfel_group_fsets();
      if (sim.n_seed_from_meas_descs > 0) {
        read_seed_from_meas_scale_factors();
      }
      break;

    case DGF_GAP_CONTACT_TABLE_TAG:
        read_gap_contact_table();
        break;

    case DGF_AVERAGED_CONTACTS_HEADER_TAG:
      read_averaged_contacts();
      break;
    
    case DGF_LRF_CONTAINMENT_TAG:
      read_lrf_containment(g_lgi_stream);             // in mlrf.cc

      break;

    case DGF_FLOW_MLRF_RING_SET_TAG:
      read_mlrf_ring_set(g_lgi_stream, STP_FLOW_REALM);             // in mlrf.cc
      disable_surfel_send_requests_by_surfel_groups();              // See comment to add_surfel_groups_of_scale_to_send_queue in strand_mgr.cc 
      break;
      
    case DGF_COND_MLRF_RING_SET_TAG:
      read_mlrf_ring_set(g_lgi_stream, STP_COND_REALM);             // in mlrf.cc
      disable_surfel_send_requests_by_surfel_groups();              // See comment to add_surfel_groups_of_scale_to_send_queue in strand_mgr.cc 
      break;
      
    case LGI_CDI_GLOBAL_TAG: // Should be the first record
      read_cdi_global_info();
#if !BUILD_5G_LATTICE
      SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<sUBLK>(get_sim_ublk_surfel_offset_table_opts());
      SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table<sSURFEL>(get_sim_ublk_surfel_offset_table_opts());
      //SIMULATOR_NAMESPACE::save_ublk_table_to_file(FALSE);
      //SIMULATOR_NAMESPACE::save_surfel_table_to_file(FALSE);
#endif
      break;

    case LGI_TIME_COUPLING_PHASES_TAG:
      read_time_coupling_phases();
      break;


#if BUILD_5G_LATTICE
    case LGI_CMPS_TAG:  //5g component info
      read_multi_component();

      //call the below in read_large_pore_params
      //SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table(DATA_OFFSET_TABLE_OPTS(sim,g_is_multi_component));
      //SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table(DATA_OFFSET_TABLE_OPTS(sim,g_is_multi_component));
      break;
#endif

    case LGI_SCLS_TAG:
      read_scalar_material();
      break;
      
    case LGI_CP_TO_SP_INFO_TAG:
      read_cp_to_sp_info();
      break;

    case LGI_CASE_ORIGIN_TAG:
      read_case_origin(g_lgi_stream);                //in eqns.cc
      break;

    case LGI_UNITS_DB_TAG:
      read_units_database(g_lgi_stream);             //in eqns.cc
      break;

    case LGI_EQUATIONS_TAG:
      read_equations(g_lgi_stream);                  //in eqns.cc
      break;

    case LGI_TABLE_COUNT_TAG:
      read_tables(g_lgi_stream);                     //in eqns.cc
      break;

    case LGI_CSYS_TAG:
      read_csys(g_lgi_stream);                       //in eqns.cc
      break;

    case LGI_GRAVITY_BUOYANCY_TAG:
      read_gravity_buoyancy(g_lgi_stream);           //in eqns.cc
      break;

    case LGI_BODY_FORCE_DESCRIPTORS_TAG:
      read_body_force_descriptors(g_lgi_stream);     //in eqns.cc
      break;

    case LGI_FLUID_PHYSICS_DESCRIPTORS_TAG:
      read_fluid_physics_descriptors(g_lgi_stream);  //in eqns.cc
      initialize_global_fan_descriptors();           //must be called after physics descs but before ublks
      break;

    case LGI_SCAS_TAG:
      // CP sends this over before the hscd chunk so that we can build the material mapping
      read_scas_chunk(g_lgi_stream); // in eqns.cc
      break;
      
    case LGI_HCSD_PHYSICS_DESCRIPTORS_TAG:
      read_hcsd_physics_descriptors(g_lgi_stream);  //in eqns.cc
      break;

    case LGI_SHELL_CONFIG_PHYSICS_DESCRIPTORS_TAG:
      read_shell_config_physics_descriptors(g_lgi_stream);  //in eqns.cc
      break;
    case LGI_MOVB_PHYSICS_DESCRIPTORS_TAG:
      read_movb_physics_descriptors(g_lgi_stream);   //in eqns.cc
      break;

    case LGI_FLOW_SURFACE_PHYSICS_DESCRIPTORS_TAG:
      read_flow_surface_physics_descriptors(g_lgi_stream);//in eqns.cc
      break;
    case LGI_THERMAL_SURFACE_PHYSICS_DESCRIPTORS_TAG:
      read_thermal_surface_physics_descriptors(g_lgi_stream);//in eqns.cc
     // need to calculate shell layer face areas in case the total heat per face is specified
      calculate_shell_layer_face_areas(); // in eqns.cc
      break;
    case LGI_FACE_ID_TO_SURFACE_PHYSICS_TAG:
      read_face_id_to_flow_surface_physics(g_lgi_stream); // in eqns.cc
      break;
    case LGI_LRF_PHYSICS_DESCRIPTORS_TAG:
      read_lrf_physics_descriptors(g_lgi_stream);
      break;

    case LGI_TURB_SYNTH_VEL_TAG:
      read_bc_turb_vel(g_lgi_stream);
      break;

    case LGI_NON_INERTIAL_FRAME_TAG:
      read_global_ref_frame(g_lgi_stream);
      break;

    case DGF_START_END_TIME_TAG:
      read_start_and_end_time();
      // Temperature solver type should be set before allocating send and receive buffers.
      // Other wise the calculation of receive size will be wrong for ublks.

      // After reading in the cdi global info and momentum freeze params, T solver type can be determined.
      // Should be done here since the ublk ckpt reading needs to know the T solver type.
      sim.adjust_uds_solver_parameters();

#if BUILD_D19_LATTICE
      sim.set_T_solver_attributes();
#elif BUILD_D39_LATTICE
      if (sim.is_heat_transfer) {
	if (sim.use_lb_energy_solver) {
	  sim.T_solver_type = LB_ENERGY;
	  g_nuT_ceiling = g_nuT_ceiling_lb_energy;
	  g_uds_diffusivity_ceiling = g_uds_diffusivity_ceiling_lb_energy;
	  //keep g_fix_viscosity_TS = g_fix_viscosity; otherwise resconsider sim.one_over_gc
	  g_fix_viscosity_TS = g_fix_viscosity_lb_energy;
	  g_fix_viscosity = g_fix_viscosity_lb_energy;
	} else {
	  sim.T_solver_type = PDE_ENTROPY;
	}
      } else
        sim.T_solver_type = INVALID;
#elif BUILD_5G_LATTICE
      sim.T_solver_type = INVALID; //5G
#endif

#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      //adjust global parameters for lb_energy solver
      if(sim.is_heat_transfer && sim.use_lb_energy_solver) {
	g_alpha_stab_udotf = g_alpha_stab_udotf_lb_energy;
	g_tau_temp_base = g_tau_temp_base_lb_energy;
	g_slip_visc_heating = g_slip_visc_heating_lb_energy;
	g_turb_visc_heating = g_turb_visc_heating_lb_energy;
	g_mach_correction_alpha = g_mach_correction_alpha_lb_energy;
	g_mach_correction_alpha_low = g_mach_correction_alpha_low_lb_energy;
      }
#endif

#if !BUILD_5G_LATTICE
      if (sim.uds_solver_type == LB_UDS) {//LB_UDS only works with isothermal, LB_T or LB_E so far
	if (!(sim.T_solver_type==LB_TEMPERATURE || sim.T_solver_type==LB_ENERGY || sim.T_solver_type==PDE_TEMPERATURE || sim.T_solver_type==INVALID)) 
	  msg_internal_error("LB_based UDS solver does not support T_solver type %d.", sim.T_solver_type);
      }
#endif
      

      // Compute 1/gc used for computing the temperature dependent molecular
      // viscosity for active scalar cases
      {
	dFLOAT xc;
	dFLOAT gc;
#if BUILD_D19_LATTICE
	if (sim.is_heat_transfer && !sim.use_lb_energy_solver && g_fix_viscosity != 3)
	  msg_internal_error("fix_viscosity can only be 3 for 19s T_solver (%d).", sim.T_solver_type);
#endif

	if (g_fix_viscosity == 3){
	  xc = sim.char_temp * sim.one_over_300k_lat;
	  gc = (((0.0304206*xc - 0.332231)*xc + 1.8598778)*xc + 0.0013748)*xc + 0.0246372;
	} else if (g_fix_viscosity == 4){
	  xc = sim.char_temp * sim.one_over_273k_lat;
	  gc = (((-0.000835F*xc + 0.018466F)*xc - 0.166452F)*xc + 1.031723F)*xc + 0.118559F;
	} else {
	  gc = 1.0;
#if BUILD_D39_LATTICE
	  msg_internal_error("fix_viscosity can only be 3 or 4");
#endif
	}
	
	sim.one_over_gc               = 1.0/gc;
      }


      //do_debug -- need to remove
      /*if(my_proc_id == 0) {
	if (sim.T_solver_type == PDE_TEMPERATURE)
	  msg_print_no_prefix("Use pde_temperature solver.\n");
	else if (sim.T_solver_type == LB_TEMPERATURE)
	  msg_print_no_prefix("Use lb_temperature solver. \n");
	else if (sim.T_solver_type == PDE_ENTROPY)
	  msg_print_no_prefix("Use pde_entropy solver.\n");
	else if (sim.T_solver_type == LB_ENTROPY)
	  msg_print_no_prefix("Use lb_entropy solver.\n");
	else if (sim.T_solver_type == LB_ENERGY)
	  msg_print_no_prefix("Use lb_energy solver.\n");
	  }*/      

#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      if(my_proc_id == 0) {
	if (sim.T_solver_type == LB_ENERGY)
	  msg_print_no_prefix("Using total energy solver.\n");
#if BUILD_6X_SOLVER
	else if (sim.T_solver_type == LB_ENTROPY)
	  msg_print_no_prefix("Using legacy entropy solver.\n");
#else
	else if (sim.T_solver_type == PDE_ENTROPY)
	  msg_print_no_prefix("Using legacy entropy solver.\n");
#endif
#if BUILD_6X_SOLVER
	if (sim.is_pf_model)
	  msg_print_no_prefix("Using phase field solver. \n");
#endif	
      }
#endif     

      break;

    case DGF_MOMENTUM_FREEZE_TAG:
      read_momentum_freeze_params();

      break;

    case DGF_LARGE_PORE_TAG:
      read_large_pore_params();
#if BUILD_5G_LATTICE     
      SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<sUBLK>(get_sim_ublk_surfel_offset_table_opts());
      SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table<sSURFEL>(get_sim_ublk_surfel_offset_table_opts());
#endif
      break;

    case DGF_FILM_VAPOR_TAG:
      read_vapor_transport_params();
      break;

    case DGF_RW_CALIBRATION_PARAMETERS_TAG:
      read_calibration_params();
      break;

    case DGF_AVG_MME_TAG:
      read_avg_mme_params();
      break;

    case DGF_LOCAL_VEL_FREEZE_TAG:
      read_local_vel_freeze_params();
      break;

    case LGI_CKPT_FAN_TAG:
      read_ckpt_fan_descs();
      break;

    case LGI_CKPT_AVERAGED_CONTACTS_TAG:
      g_thermal_averaged_contacts.read_ckpt_data();
      break;

    case DGF_CKPT_MEAS_WINDOW_TAG:
      read_meas_window_ckpt_data();
      break;

    case LGI_TRAJECTORY_ID_MAP_TAG: // only received on ckpt resume if number of SPs has changed
      read_trajectory_id_map();
      break;

    case LGI_CKPT_RANDOM_SEED_TAG:
      g_random_num_generator.read_ckpt();
      break;

    case LGI_CKPT_EQN_RANDOM_SEED_TAG:
      read_eqn_rand_state();
      break;

    case LGI_SYNCHRONIZATION_TAG: 
      {
        // sync record introduces ckpt data - allows SPs to exchange fan info without deadlock
        lgi_skip_record(g_lgi_stream);

        static cBOOLEAN fans_initialized = FALSE;
        if (!fans_initialized) {
          fans_initialized = TRUE;
          // Finish fan initialization before restoring fan ckpt info
          initialize_local_fan_descriptors();
          exchange_fan_info();
        }
      }
      break;

    case LGI_GLOBAL_NIRF_STATE_TAG:
      sim.read_ckpt_grf_state();
      break;

    case LGI_LRF_STATE_TAG:
      sim.read_ckpt_lrf_state();
      break;

    case LGI_TURB_SYNTH_INFO_STATE_TAG:
      g_turb_info.read_ckpt_turb_synth_info_state();
      break;

    case LGI_CKPT_RADIATION_TM_TAG:
#if !BUILD_GPU
      g_timescale.m_radiation_tm.read_ckpt(g_timescale.m_lb_tm, g_timescale.m_conduction_pde_tm);
#endif
      break;

    case LGI_MOVB_STATE_TAG:
      sim.read_ckpt_movb_state();
      break;

    case LGI_THERMAL_ACCEL_STATE_TAG:
      sim.read_ckpt_thermal_accel_state();
      break;

    case LGI_POROUS_ROCK_COUNT_TAG:
      read_porous_rock_tables(g_lgi_stream);
      break;

    case LGI_ICE_ACCRETION_TAG:
        read_ice_accretion_parameters();
        break;
    case LGI_PARTICLE_GLOB_TAG:
        read_particle_globals();
        break;
    case LGI_PARTICLE_MATERIAL_TAG:
        read_particle_materials();
        break;
    case LGI_CONDUCTION_SOLID_MATERIAL_TAG:
        read_conduction_solid_materials(g_lgi_stream);
        break;
    case LGI_RADIATION_SURFACE_CONDITION_TAG:
        read_radiation_surface_conditions(g_lgi_stream);
        break;
    case LGI_DATA_CURVES_TAG:
        read_data_curves();
        break;
    case LGI_PART_AXIS_TAG:
        read_anisotropic_part_axis();
        break;
    case LGI_PARTICLE_EMITTER_CONFIGURATION_TAG:
        read_particle_emitter_configurations();
        break;
    case LGI_PARTICLE_EMITTER_TAG:
        read_particle_emitters();
        break;
    case LGI_PARTICLE_SURFACE_MATERIAL_TAG:
        read_particle_surface_interaction_parameters();
        break;
    case LGI_PARTICLE_SCREENS_TAG:
        read_particle_screens();
        break;
    case LGI_VIRTUAL_WIPER_TAG:
        read_virtual_wipers();
        break;
    case LGI_CKPT_PARTICLE_EMITTERS_STATE_TAG:
        read_particle_emitters_state_ckpt();
        break;
    case LGI_CKPT_PARTICLE_EMITTERS_TAG:
        read_particle_emitter_ckpt();
        break;
    case LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG:
        read_particle_property_rng_state();
        break;
    case LGI_SURFACE_EMITTER_GEOMETRY_TAG:
        read_surface_emitter_geometry();
        break;
    case LGI_IMPLICIT_SOLVER_DATA_TAG:
        read_implicit_solver_data();
        break;
    default:
      msg_internal_error("Unrecognized LGI tag %d: %s",tag.id, lgi_tag_namestring(tag.id));
      lgi_skip_record(g_lgi_stream);
      break;
    }
  }

  close_dgf_stream();
}

//----------------------------------------------------------------------------
// validate_version
//----------------------------------------------------------------------------
VOID sDGF_READER::validate_version(asINT32 version)
{
  if (version < LGI_DGF_VERSION)
    msg_error("LGI version %d not supported by this version of the PowerFLOW simulator.", (int) version);

  if (version > LGI_DGF_VERSION)
    msg_error("The version of the LGI data being read (%u) "
              "is more recent than the version of this LGI reader (%u)",
              (unsigned) version, (unsigned) LGI_VERSION);
}

//----------------------------------------------------------------------------
// read_global_part_names
//----------------------------------------------------------------------------
VOID sDGF_READER::read_global_part_names()
{
  DGF_GLOBAL_PART_NAMES_REC record;
  record.read(g_lgi_stream);

  g_cdi_volume_physics_desc_index_from_part_index = xnew sINT32[record.num_regions];
  g_num_parts = record.num_regions;
  asINT32 max_physics_desc_index = -1;
  g_part_total_volumes = xnew dFLOAT[record.num_regions];
  ccDOTIMES(i, record.num_regions) {
    DGF_GLOBAL_PART_NAMES_SUBREC subrec;
    subrec.read(g_lgi_stream);
    g_cdi_volume_physics_desc_index_from_part_index[i] = subrec.cdi_physics_index;
    g_part_total_volumes[i] = subrec.part_simulated_volume;
    if (subrec.cdi_physics_index > max_physics_desc_index)
      max_physics_desc_index = subrec.cdi_physics_index;
  }
  sim.n_volume_physics_descs = max_physics_desc_index + 1;
}

//----------------------------------------------------------------------------
// read_global_face_names
//----------------------------------------------------------------------------
VOID sDGF_READER::read_global_face_names()
{
  DGF_GLOBAL_FACE_NAMES_REC record;
  record.read(g_lgi_stream);

  g_num_faces = record.num_faces;
  g_face_index_to_part_index = xnew sINT32[record.num_faces];
  g_face_index_to_opposite_face_index = xnew sINT32[record.num_faces];
  g_face_index_is_front_only = xnew sINT8[record.num_faces];
  g_cdi_front_flow_surface_physics_desc_index_from_face_index    = xnew sINT32[record.num_faces];
  g_cdi_front_thermal_surface_physics_desc_index_from_face_index = xnew sINT32[record.num_faces];
  g_cdi_back_flow_surface_physics_desc_index_from_face_index     = xnew sINT32[record.num_faces];
  g_cdi_back_thermal_surface_physics_desc_index_from_face_index  = xnew sINT32[record.num_faces];
  g_flow_bc_total_areas = xnew dFLOAT[record.num_faces];
  
  ccDOTIMES(i, record.num_faces) {
    DGF_GLOBAL_FACE_NAMES_SUBREC subrec;
    subrec.read(g_lgi_stream);

    g_face_index_to_part_index[i] = subrec.face_parent_index;
    g_face_index_to_opposite_face_index[i] = subrec.opposite_face_cdi_id;
    g_face_index_is_front_only[i] = subrec.is_front_only;
    g_cdi_front_flow_surface_physics_desc_index_from_face_index[i] = subrec.cdi_front_flow_physics_index;
    g_cdi_front_thermal_surface_physics_desc_index_from_face_index[i] = subrec.cdi_front_thermal_physics_index;
    g_cdi_back_flow_surface_physics_desc_index_from_face_index[i] = subrec.cdi_back_flow_physics_index;
    g_cdi_back_thermal_surface_physics_desc_index_from_face_index[i] = subrec.cdi_back_thermal_physics_index;
    g_flow_bc_total_areas[i] = subrec.surfel_area;
  }

  // Old CDI files did not include a table of thermal surface physics descriptors (THPT chunk)
  // so DGF_GLOBAL_FACE_NAMES_SUBREC::cdi_thermal_physics_index will be -1.
  if (sim.cdi_major_version < 9 || (sim.cdi_major_version == 9 && sim.cdi_minor_version <= 4)) {
    memcpy(g_cdi_front_thermal_surface_physics_desc_index_from_face_index, 
           g_cdi_front_flow_surface_physics_desc_index_from_face_index,
           record.num_faces * sizeof(sINT32));
    memcpy(g_cdi_back_thermal_surface_physics_desc_index_from_face_index, 
           g_cdi_back_flow_surface_physics_desc_index_from_face_index,
           record.num_faces * sizeof(sINT32));
  }

  // Copy face -> area to a temporary vector
  std::vector<dFLOAT> faceArea(g_flow_bc_total_areas, g_flow_bc_total_areas + g_num_faces);

  // Sum up total area of part segment for each face that comprise that part segment
  ccDOTIMES(i, g_num_faces) {
    BOOLEAN is_back_side = is_back_side_only_from_face_index(i);
    sINT32 myPdIndex = flow_phys_desc_index_from_face_index(i, is_back_side);
    if (myPdIndex != -1) {
      ccDOTIMES(ii, g_num_faces) {
        if (i != ii) {
          BOOLEAN ii_is_back_side = is_back_side_only_from_face_index(ii);
          sINT32 iiPdIndex = flow_phys_desc_index_from_face_index(ii, ii_is_back_side);
          if (iiPdIndex == myPdIndex)
            g_flow_bc_total_areas[i] += faceArea[ii];
        }
      }
    }
  }
}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
VOID sDGF_READER::read_vertices_header()
{
  cDGF_VERTICES record;
  record.read(g_lgi_stream);

  g_surfel_vertices_info.m_n_global_surfel_vertices = record.num_surfel_vertices;
  g_surfel_vertices_info.allocate_is_surfel_vertex_used(record.num_surfel_vertices);
  g_surfel_vertices_info.reserve_surfel_vertex_global_indices(MIN(record.num_surfel_vertices,
                                             10 * record.num_surfel_vertices / total_sps));
  if (sim.is_particle_model) {
    g_face_polygon_info.m_n_fpoly_vertices = record.num_fpoly_vertices;
    ccDOTIMES(pvertex, record.num_fpoly_vertices) {
      cDGF_VERTEX_2D_POINT p;
      p.read(g_lgi_stream);
      g_face_polygon_info.add_fpoly_vertex(p);
    }
  }
}
//#endif

VOID sDGF_READER::read_meas_windows()
{
  asINT32 n_meas_windows = sim.control_record.num_meas_windows;

  ccDOTIMES(i, n_meas_windows) {
    cDGF_MEAS_WINDOW dgf_window;
    dgf_window.read(g_lgi_stream);

    MEAS_WINDOW meas_window = g_meas_windows.add_meas_window(&dgf_window);

    if (meas_window->is_average_mme) {
      continue;
    }

    if(meas_window->m_is_particle_trajectory_window) {
      //Read the additional data for the trajectory window parameters.
      LGI_TRAJECTORY_WINDOW_OPTIONS_HEADER header;
      lgi_read_next_head(g_lgi_stream, header);

      LGI_TRAJECTORY_WINDOW_OPTIONS_REC record;
      lgi_read(g_lgi_stream, &record, sizeof(record));

      TRAJECTORY_WINDOW trajectory_window = static_cast<TRAJECTORY_WINDOW>(meas_window);
      trajectory_window->set_options(record);
    }

    if (meas_window->is_development &&  dgf_window.num_meas_cells > 0 ) {
      cDGF_DEV_WINDOW_LINE_SEGMENTS dgf_segment;
      dgf_segment.read(g_lgi_stream);

      ccDOTIMES(icoord,3) {
        meas_window->start_pt[0][icoord] = dgf_segment.start_pt_x[icoord];
        meas_window->start_pt[1][icoord] = dgf_segment.start_pt_y[icoord];
        meas_window->start_pt[2][icoord] = dgf_segment.start_pt_z[icoord];
        meas_window->dev_line_orientation[0][icoord] =  dgf_segment.end_pt_x[icoord] - dgf_segment.start_pt_x[icoord]; 
        meas_window->dev_line_orientation[1][icoord] =  dgf_segment.end_pt_y[icoord] - dgf_segment.start_pt_y[icoord]; 
        meas_window->dev_line_orientation[2][icoord] =  dgf_segment.end_pt_z[icoord] - dgf_segment.start_pt_z[icoord];
      }
      ccDOTIMES(dev_axis,3) {
        dFLOAT dev_line_length = sqrt(meas_window->dev_line_orientation[dev_axis][0]*meas_window->dev_line_orientation[dev_axis][0] + 
                                      meas_window->dev_line_orientation[dev_axis][1]*meas_window->dev_line_orientation[dev_axis][1] + 
                                      meas_window->dev_line_orientation[dev_axis][2]*meas_window->dev_line_orientation[dev_axis][2]);
        ccDOTIMES(icoord,3) 
          meas_window->dev_line_orientation[dev_axis][icoord] /= dev_line_length;
        if (dev_axis == 0)
          meas_window->one_over_dev_win_segment = dgf_segment.num_segments_x /dev_line_length ;
      }
      STP_MEAS_CELL_INDEX meas_cell_index = 0;
   
      cDGF_MEAS_WIN_SUBRECS meas_win_subrecs;
      meas_win_subrecs.read(g_lgi_stream);

      if ((meas_window->meas_window_type == LGI_FLUID_WINDOW) || (meas_window->meas_window_type == LGI_POROUS_WINDOW)) {
        ccDOTIMES(j, dgf_window.num_subrecs) {
          cDGF_FLUID_DEV_WINDOW_PART_EXTENT  extent;
          extent.read(g_lgi_stream);
          uINT8 axis = extent.dev_axis;
          meas_window->entity_first_meas_cell[axis][extent.part_index] = meas_cell_index;
          meas_window->entity_n_segments[axis][extent.part_index] = extent.num_part_segments;
          meas_window->entity_first_segment[axis][extent.part_index] = extent.first_segment_index;
          meas_cell_index += extent.num_part_segments;
        }
      } else if (meas_window->meas_window_type == LGI_SURFACE_WINDOW) {
        ccDOTIMES(j, dgf_window.num_subrecs) {
          cDGF_SURFACE_DEV_WINDOW_FACE_EXTENT  extent;
          extent.read(g_lgi_stream);
          uINT8 axis = extent.dev_axis;
          meas_window->entity_first_meas_cell[axis][extent.face_index] = meas_cell_index;
          meas_window->entity_n_segments[axis][extent.face_index] = extent.num_face_segments;
          meas_window->entity_first_segment[axis][extent.face_index] = extent.first_segment_index;
          meas_cell_index += extent.num_face_segments;
        }
      } else {
        msg_error("LGI_SAMPLING_SURFACE_WINDOW not supported by development window.");
      }

    }        
  }

#if BUILD_GPU
  // build_device_meas_windows(g_meas_windows);
#endif
}

VOID sDGF_READER::read_meas_vars_list() {

  asINT32 n_meas_windows = sim.control_record.num_meas_windows;
  ccDOTIMES(i, n_meas_windows) {
    MEAS_WINDOW meas_window = g_meas_windows[i];
    if (meas_window->is_average_mme)
      continue;
    if ((meas_window->n_global_stationary_meas_cells + meas_window->n_global_moving_meas_cells ) > 0)
      meas_window->read_variables(g_lgi_stream);
  }
}


// quad mesh info for curved porous hxch inlet reference face
VOID sDGF_READER::read_quad_mesh_info()
{
  cDGF_QUAD_MESH_HEADER quad_mesh_header;

  quad_mesh_header.read(g_lgi_stream);

  g_meas_windows[quad_mesh_header.window_index]->quad_mesh = xnew sQUAD_MESH;
  QUAD_MESH q = g_meas_windows[quad_mesh_header.window_index]->quad_mesh; 

  q->facet_offset = quad_mesh_header.facet_offset;
  q->num_facets = quad_mesh_header.num_facets;

  asINT32 n_vertex_refs = q->num_facets * 4;

  q->vertex_indices = xnew DGF_VERTEX_INDEX[n_vertex_refs];
  lgi_read(g_lgi_stream, q->vertex_indices, n_vertex_refs * sizeof(DGF_VERTEX_INDEX));

  cDGF_NUM_VERTICES num_vertices;
  num_vertices.read(g_lgi_stream);
  q->num_vertices = num_vertices.n;

  q->vertex_coords = xnew STP_SGEOM_VARIABLE[3 * q->num_vertices];
  lgi_read(g_lgi_stream, q->vertex_coords, 3 * q->num_vertices * sizeof(STP_SGEOM_VARIABLE));

}

VOID sDGF_READER::read_coupling_meas_window()
{
  cDGF_MEAS_WINDOW dgf_window;
  dgf_window.read(g_lgi_stream);

  if (dgf_window.num_meas_cells == 0 || sim.is_conduction_sp) 
    return;

  MEAS_WINDOW meas_window = g_meas_windows.add_meas_window(&dgf_window);

  // now read the surfel ids and meas cell references. At this point, we are
  // assuming that the surfel descriptors have already been read. Check for
  // a sentinel surfel id of -1 to end reading this record.
  ccDOTIMES (i, (dgf_window.num_meas_cells + 1)) {
    cDGF_SURFEL_IDS_MEAS_INDEX surfel_meas_cell;
    surfel_meas_cell.read(g_lgi_stream);
    if (surfel_meas_cell.surfel_id  == INVALID_SHOB_ID)
      break; // done reading surfels 
    //REALM_ASSUMPTION: Surfel is FLOW
    SURFEL surfel = regular_surfel_from_id(surfel_meas_cell.surfel_id);
    if (NULL == surfel) {
      msg_internal_error("Cannot find surface coupling surfel ID %d", 
                         surfel_meas_cell.surfel_id);
    }
    // surfel_meas_cell.meas_index is the global measurement surfel index
    STP_MEAS_CELL_INDEX local_meas_index = meas_window->create_surfel_meas_cell(surfel_meas_cell.meas_index);

    // store a couple of related parameters in the surfel to be used during
    // surfel dyn group split operations
    surfel->lb_data()->m_attribs.set_is_coupling_meas_surfel();
    surfel->coupling_meas_index = local_meas_index;

    sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptrs =
        surfel->add_meas_cell_ptr(surfel->dynamics_data());
    surfel_meas_cell_ptrs->set_window_index(g_meas_windows.n_meas_windows() - 1);
    surfel_meas_cell_ptrs->set_index(local_meas_index);

    // does the surfel have an even-odd partner? If so, add that also
    if (surfel_meas_cell.clone_id != INVALID_SHOB_ID) {
      //REALM_ASSUMPTION: Surfel is FLOW
      SURFEL clone_surfel = regular_surfel_from_id(surfel_meas_cell.clone_id);
      if (NULL == clone_surfel) {
        msg_internal_error("Cannot find surface coupling surfel ID %d", 
                           surfel_meas_cell.clone_id);
      }
      clone_surfel->lb_data()->m_attribs.set_is_coupling_meas_surfel();
      clone_surfel->coupling_meas_index = local_meas_index;
      sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptrs =
          clone_surfel->add_meas_cell_ptr(clone_surfel->dynamics_data());
      surfel_meas_cell_ptrs->set_window_index(g_meas_windows.n_meas_windows() - 1);
      surfel_meas_cell_ptrs->set_index(local_meas_index);
    }
  } // for all meas surfels

  meas_window->read_variables(g_lgi_stream);
}

VOID sDGF_READER::read_meas_window_master_sps_and_output_times()
{
  LGI_MEAS_WINDOW_MASTER_SP_HEADER record;
  record.read(g_lgi_stream);

  DO_MEAS_WINDOWS(window) {
    LGI_MEAS_WINDOW_MASTER_SP subrec;
    subrec.read(g_lgi_stream);
    if (subrec.master_sp != -1) {
      window->initialize_master_sp(subrec.master_sp);
      window->m_time_desc = subrec.time_desc;
      window->current_update_time.clear_time  = subrec.initial_clear_time;
      window->current_update_time.output_time = subrec.initial_output_time;
      window->next_update_time.clear_time     = subrec.next_clear_time;
      window->next_update_time.output_time    = subrec.next_output_time;
    }

    // Average mme meas window is not a real meas window
    if (window->is_average_mme) {
      BOOLEAN is_cond_window = (subrec.time_rel_realm == STP_COND_REALM);
      if (is_cond_window != sim.is_conduction_sp) {
        continue; //defers setting variables till receive the avg_mme_window linked to this realm
      }
#if DEBUG_AVG_MME
      msg_print("Receive average mme dgf window subrec reset %d mme ckpt %d repeat %d", subrec.initial_clear_time, subrec.initial_output_time, subrec.time_desc.repeat);
#endif
      TIMESTEP period = subrec.time_desc.period;
      sim.avg_mme_period = period;
      sim.avg_mme_interval = subrec.initial_output_time - subrec.initial_clear_time;
      if (sim.avg_mme_interval < 0 || sim.avg_mme_interval > period)
        sim.avg_mme_interval = sim.avg_mme_period;
      // convert them to base steps, since unlike true measurement windows, avg mme checkpoints expects both realms to
      // cover the same span (this limitation implies that avg mme checkpoints are only valid for advance solvers at
      // same rate, with CP throwing an error if is not the case)
      STP_REALM realm;
      if (sim.is_conduction_sp) {
        realm = STP_COND_REALM;
        sim.avg_mme_period   *= g_timescale.m_n_conduction_pde_base_steps / g_timescale.m_n_base_timesteps_per_timestep;
        sim.avg_mme_interval *= g_timescale.m_n_conduction_pde_base_steps / g_timescale.m_n_base_timesteps_per_timestep;
      } else {
        realm = STP_FLOW_REALM;
        sim.avg_mme_period   *= g_timescale.m_n_lb_base_steps / g_timescale.m_n_base_timesteps_per_timestep;
        sim.avg_mme_interval *= g_timescale.m_n_lb_base_steps / g_timescale.m_n_base_timesteps_per_timestep;
      }

      if (subrec.initial_output_time > 0)
        g_async_event_queue.add_entry(new_event(EVENT_ID_AVG_MME_CKPT, 0, subrec.initial_output_time, period, TIMESTEP_LAST, realm));
      // If the full ckpt is between the clear and output times, then the next clear event should be scheduled here
      if (subrec.initial_clear_time >= 0) {
        if (subrec.initial_clear_time < g_timescale.m_start_time)
          subrec.initial_clear_time += period;
        g_async_event_queue.add_entry(new_event(EVENT_ID_CLEAR_AVG_MME_DATA, 0, subrec.initial_clear_time, period, TIMESTEP_LAST, realm));
      }

      if (subrec.time_desc.repeat > 0) {// end time known from the beginning
        double e = subrec.time_desc.start + subrec.time_desc.repeat * subrec.time_desc.period;
        BASETIME end_time = MIN(sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(e), BASETIME_MAX);
        if (end_time >= g_timescale.m_start_time) {
#if DEBUG_AVG_MME
          msg_print("Register the stop mme ckpt event at timestep %d", end_time);
#endif
          g_async_event_queue.add_entry(new_event(EVENT_ID_STOP_AVG_MME_CKPT, 0, end_time, 0, BASETIME_LAST));
        }
      } else {  // end now unknown at the beginning, use end_time as the duration
        sim.avg_mme_duration = sim.realm_phase_time_info[realm].realm_to_global_timestep(subrec.end_time);
      }
      continue;
    } else {
      // insert in output and clear queues based on current_update_time
      g_meas_windows.insert_in_output_and_clear_queues_if_non_empty(window);
    }
    // For supporting variable PowerTherm coupling periods
    window->end_time = subrec.end_time;

    if (!sim.is_conduction_sp) {
      ccDOTIMES(i, subrec.n_meas_phases)
      {
        cDGF_MEAS_PHASE phase;
        phase.read(g_lgi_stream);
        sMEAS_PHASE_TIME_DESC_SP meas_phase_entry;
        meas_phase_entry.m_output_time = phase.next_output_time;
        meas_phase_entry.m_period = phase.period;
        meas_phase_entry.m_interval = phase.interval;
        meas_phase_entry.m_repeat = phase.repeat;
        window->m_meas_phase_descs.push_back(meas_phase_entry);
      }
    }
  }
}

VOID sDGF_READER::read_meas_cell_reference_frame_conflicts()
{

  LGI_MEAS_CELL_REFERENCE_FRAME_CONFLICTS conflicts;
  conflicts.read(g_lgi_stream);
  if (sim.n_lrf_physics_descs <= 0) return;
  
  DO_MEAS_WINDOWS(window) {
    if (!window->is_composite) continue;
    if (!window->is_output_in_local_csys) continue;
    if (!window->meas_cell_output_in_local_csys.size())
      window->meas_cell_output_in_local_csys.resize(window->n_stationary_meas_cells);

    const sINT8 N_MEAS_CELLS_PER_ROUND = 64;
    uINT64 meas_cells_span_ref_frames = 0;
    sINT32 nrounds = (window->n_stationary_meas_cells + N_MEAS_CELLS_PER_ROUND - 1) /
                     N_MEAS_CELLS_PER_ROUND;
    ccDOTIMES(irounds, nrounds) {
      lgi_read(g_lgi_stream,&meas_cells_span_ref_frames,sizeof(uINT64));
      STP_MEAS_CELL_INDEX meas_cells_processed = irounds*N_MEAS_CELLS_PER_ROUND;
      STP_MEAS_CELL_INDEX n_meas_cells_to_process = 
          MIN(window->n_stationary_meas_cells - meas_cells_processed,N_MEAS_CELLS_PER_ROUND);
      
      ccDOTIMES(mci,n_meas_cells_to_process) {
        window->meas_cell_output_in_local_csys[mci+meas_cells_processed]
            = !(meas_cells_span_ref_frames&(1<<mci));
      }
    }
  }
}

VOID sDGF_READER::read_meas_window_ckpt_data()
{       
  LGI_TAG tag;
  read_lgi_head(tag);

  DO_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;
    window->read_ckpt();
  }
}

VOID sDGF_READER::read_trajectory_id_map()
{
  LGI_TAG tag;
  read_lgi_head(tag); 

  asINT32 total_ckpt_sps;
  read_lgi(total_ckpt_sps);

  // remap all parcel trajectory IDs from local to global
  sSP_TRAJECTORY_ID_MAP trajectory_id_map;
  trajectory_id_map.initialize(total_ckpt_sps, true);
  std::vector<TRAJECTORY_ID> &base_global_ids = trajectory_id_map.base_global_ids();
  read_lgi(base_global_ids.data(), sizeof(TRAJECTORY_ID) * base_global_ids.size());
  trajectory_id_map.remap_parcel_trajectory_ids();
}

VOID sDGF_READER::read_smart_seed_control_record()
{

  cDGF_SEED_INFO control_info;
  control_info.read(g_lgi_stream);
  sINT8 n_seed_controllers = control_info.n_seed_controllers;

  for (int controller_index=0; controller_index < n_seed_controllers; controller_index++) {
    cDGF_SMART_SEED_CONTROL control;
    control.read(g_lgi_stream);

    sSEED_CONTROL seed_control;
    seed_control.init(control);
    seed_control.is_var_seeded         = cnew cBOOLEAN[DGF_N_SEED_VARS];
    seed_control.active_seed_var_index = cnew asINT32[DGF_N_SEED_VARS];
    //Set all seed var indices to -1
    std::fill(seed_control.active_seed_var_index,
              seed_control.active_seed_var_index + DGF_N_SEED_VARS,
              -1);
    
    int active_seed_var_index = 0;
    if (control.n_variables > 0) {
      seed_control.smart_seed_var_types  = xnew DGF_SEED_VAR_TYPE[control.n_variables];
      ccDOTIMES(i, control.n_variables) {
        uINT8 var_type;
        lgi_read(g_lgi_stream, var_type);
        seed_control.smart_seed_var_types[i] = (DGF_SEED_VAR_TYPE)var_type;
        seed_control.is_var_seeded[var_type] = TRUE;
        seed_control.active_seed_var_index[var_type] = active_seed_var_index++;
      }
    } else {
      seed_control.smart_seed_var_types  = NULL;
    }

    if (active_seed_var_index > N_AVAILABLE_DGF_SEED_SLOTS) {
      msg_internal_error("Number of seed variables exceeds the cap of %lu, required to "
                         "overlap smart seed data with min(sizeof(LB_STATES), "
                         "sizeof(NEAR_LB_DATA::post_advect_scale_factors))",
                         N_AVAILABLE_DGF_SEED_SLOTS);
    }

    //UDS
    if (control.n_uds_variables > 0) {
      seed_control.smart_seed_uds_var_indices  = xnew sINT16[control.n_uds_variables];  
      ccDOTIMES(i, control.n_uds_variables) {
        uINT8 uds_var_index;
        lgi_read(g_lgi_stream, uds_var_index);
        seed_control.smart_seed_uds_var_indices[i] = uds_var_index;
      }
    } else {
      seed_control.smart_seed_uds_var_indices  = NULL;
    }
  
    ccDOTIMES(i, seed_control.n_fluid_seed_var_specs) {
      uINT8 n_spec_vars;
      uINT8 n_uds_vars;
      lgi_read(g_lgi_stream, &n_spec_vars, sizeof(uINT8));
      lgi_read(g_lgi_stream, &n_uds_vars, sizeof(uINT8));
      sFLUID_SEED_VAR_SPEC seed_var_spec(seed_control, n_spec_vars, n_uds_vars);

      seed_var_spec.seed_controller = controller_index;

      ccDOTIMES(v, seed_var_spec.n_vars) {
        uINT8 seed_var_type;
        lgi_read(g_lgi_stream, seed_var_type);
        seed_var_spec.seed_var_types[v] = (DGF_SEED_VAR_TYPE)seed_var_type;
        seed_var_spec.is_var_seeded[seed_var_type] = TRUE;
      }
  
      ccDOTIMES(v, seed_var_spec.n_uds_vars) {
        uINT8 seed_uds_var_index;
        lgi_read(g_lgi_stream, seed_uds_var_index);
        seed_var_spec.seed_uds_var_indices[v] = seed_uds_var_index;
        asINT32 index_in_enum;  //in DGF_SEED_UDS_VAR_TYPE
        asINT32 nth_uds;
        find_uds_indices(seed_uds_var_index, index_in_enum, nth_uds);
        seed_var_spec.is_uds_var_seeded[nth_uds][index_in_enum] = TRUE;
      }
      g_fluid_seed_var_specs.push_back(seed_var_spec);
    }

    ccDOTIMES(i, seed_control.n_boundary_seed_var_specs) {
      uINT8 n_spec_vars;
      uINT8 n_uds_vars;
      lgi_read(g_lgi_stream, &n_spec_vars, sizeof(uINT8));
      lgi_read(g_lgi_stream, &n_uds_vars, sizeof(uINT8));
      sBOUNDARY_SEED_VAR_SPEC seed_var_spec(seed_control, n_spec_vars, n_uds_vars);

      seed_var_spec.seed_controller = controller_index;

      ccDOTIMES(v, seed_var_spec.n_vars) {
        uINT8 seed_var_type;
        lgi_read(g_lgi_stream, seed_var_type);
        seed_var_spec.seed_var_types[v] = (DGF_BOUNDARY_SEED_VAR_TYPE)seed_var_type;
        seed_var_spec.is_var_seeded[seed_var_type] = TRUE;
      }
  
      ccDOTIMES(v, seed_var_spec.n_uds_vars) {
        uINT8 seed_uds_var_index;
        lgi_read(g_lgi_stream, seed_uds_var_index);
        seed_var_spec.seed_uds_var_indices[v] = seed_uds_var_index;
        asINT32 index_in_enum;  //in DGF_SEED_UDS_VAR_TYPE
        asINT32 nth_uds;
        find_uds_indices(seed_uds_var_index, index_in_enum, nth_uds);
        seed_var_spec.is_uds_var_seeded[nth_uds][index_in_enum] = TRUE;
      }
      
      g_boundary_seed_var_specs.push_back(seed_var_spec);
    }

    sim.m_seed_control.push_back(seed_control);

  }

}

VOID sDGF_READER::read_smart_seed_initial_lrf_rotations()
{
  cDGF_SMART_SEED_LRF_INITIAL_ROTATIONS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  asINT32 n_lrfs = sim.n_lrf_physics_descs;

  ccDOTIMES(i, n_lrfs) {
    cDGF_SMART_SEED_LRF_INITIAL_ROTATION subrec;
    subrec.read(g_lgi_stream);
    LRF_PHYSICS_DESCRIPTOR lrf = sim.ref_frame_index_to_lrf(i);
    lrf->initial_angle_rotated = subrec.rotation;
    lrf->initial_n_revolutions = subrec.n_revolutions;
    lrf->angle_rotated         = subrec.rotation;
    lrf->n_revolutions         = subrec.n_revolutions;
  }

  // update local reference frames information
  // pass TRUE as the last parameter because this is the initial update after smart seed
  cBOOLEAN IS_INITIAL_UPDATE = TRUE;
  sim_update_local_ref_frames(COARSEST_SCALE,
                              FINEST_SCALE,
                              TRUE /*single pass*/,
                              1 /*time_incr*/,
                              1.0 /*time rate factor*/,
                              IS_INITIAL_UPDATE);
}

VOID sDGF_READER::read_smart_seed_initial_movb_xforms()
{
  // This transformation must be performed on the CP since the bsurfels need
  // to identify their home ublk on specific SPs at initialization
  cDGF_SMART_SEED_MOVB_INITIAL_XFORMS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  uINT16 n_movbs_cdi = header.n_movbs_cdi;

  ccDOTIMES(i, n_movbs_cdi) {
    cDGF_SMART_SEED_MOVB_INITIAL_XFORMS subrec;
    subrec.read(g_lgi_stream);
    sINT32 idx = subrec.xform_index;
    if ( idx >= 0) {
      MOVB_PHYSICS_DESCRIPTOR movb = &sim.movb_physics_descs[idx];
      movb->initial_angle_rotated = subrec.xform[3][0];
      movb->angle_rotated = subrec.xform[3][0];
      sBG_TRANSFORM3d bg_xform(subrec.xform[0][0], subrec.xform[0][1], subrec.xform[0][2], subrec.xform[0][3],
                               subrec.xform[1][0], subrec.xform[1][1], subrec.xform[1][2], subrec.xform[1][3],
                               subrec.xform[2][0], subrec.xform[2][1], subrec.xform[2][2], subrec.xform[2][3]);
      movb->motion_xform = bg_xform;
    }
  }
}

//----------------------------------------------------------------------------
// read_control_record
//----------------------------------------------------------------------------
VOID sDGF_READER::read_control_record()
{
  cDGF_CONTROL *control_record = &sim.control_record;
  control_record->read(g_lgi_stream);
  sim.init(control_record->num_scales, control_record->simv_size);
  if (control_record->num_shob_types != N_SHOB_TYPES) 
    msg_internal_error("Unexpected number of shob types");

  sim.init_lattice();

#if BUILD_5G_LATTICE
  //advection_path_type: 0 = standard advection paths, 1 = diagonal advection paths
  if ((g_is_frac_adv && (control_record->advection_path_type==0)) || (!g_is_frac_adv && (control_record->advection_path_type==1)))
    msg_error("LGI version not compatible with this version of the PowerFLOW simulator.");

  //must after state_table is initialized
  g_gradient_operator = iGRADIENT_OP::create (sim.num_dims, g_mp_gradient_level, g_mp_surface_tension);
#endif

  // build_recipes cannot be called before init_lattice, because it access the state_table,
  // which init_lattice updates. However it must be called before read_ublk_table,
  // because the sizes it computes are used in adding quantums to send groups.

  g_comm_compression_info.build_recipes();
  
  g_strand_mgr.m_neighbor_sp_map.init_rank_map(total_sps);

  ccDOTIMES(realm, STP_N_REALMS) {
    SHOB_ID n_ublks = 0;
    SHOB_ID n_surfels = 0;
    ccDOTIMES(scale, sim.num_scales) {
      cDGF_SHOB_COUNTS shob_counts;
      shob_counts.read(g_lgi_stream);
      ccDOTIMES(type, N_SHOB_TYPES) {
        if (shob_type_is_ublk(type))
          n_ublks += shob_counts.num_shobs[type];
        else if (shob_type_is_surfel(type))
          n_surfels += shob_counts.num_shobs[type];
      }
    }
    sim.n_total_ublks[realm] = n_ublks;
    sim.n_total_surfels[realm] = n_surfels;

    g_ublk_table[realm].reserve(n_ublks);
    g_surfel_table[realm].reserve(n_surfels);
  }

  initialize_shob_groups();

}

//----------------------------------------------------------------------------
// read_neighbor_mask_table
//----------------------------------------------------------------------------
void sDGF_READER::read_neighbor_mask_table()
{
  cDGF_NEIGHBOR_MASK_TABLE nmask_table_rec;
  nmask_table_rec.read(g_lgi_stream);
  g_comm_compression_info.m_num_masks = nmask_table_rec.num_masks;
  g_comm_compression_info.m_masks = xnew STP_UBLK_NEIGHBOR_MASK[g_comm_compression_info.m_num_masks];

  ccDOTIMES(i, g_comm_compression_info.m_num_masks) {
    cDGF_NEIGHBOR_MASK nmask_rec;
    nmask_rec.read(g_lgi_stream);
    g_comm_compression_info.m_masks[i] =  nmask_rec.neighbor_mask;
  }
}


//----------------------------------------------------------------------------
// read_ublk_table
//----------------------------------------------------------------------------
template <typename UBLK_PROC>
void sDGF_READER::read_ublk_table(STP_REALM realm)
{
  
  g_ublk_table[realm].last_added_ublk_id = -1;

#if BUILD_GPU  
  init_gpu_host_ublk_data_managers();
#endif  
  cDGF_UBLK_TABLE table_header;
  table_header.read(g_lgi_stream);

  std::vector<cDGF_GHOST_INFO> ghost_info;

  // Declare these outside the loop so that each successive read into the object
  // reuses storage previously allocated. 
  cDGF_SIMPLE_UBLK_DESC simple_desc;
  cDGF_REAL_UBLK_DESC real_desc;
  cDGF_MIRROR_UBLK_DESC mirror_desc;

  // ignore the global ublk count in the table header, since it is across all
  // SPs, and could refer to compacted ublks. Use the per-sp total ublk count
  // already calculated when reserving space for the ublk table. Sentinel ublk
  // needed to account for compacted ublks
  asINT32 ublk_id_success = 0;
  ccDOTIMES64(i, sim.n_total_ublks[realm] + 1) {
    cDGF_UBLK_BASE ublk_base;
    STP_PROC home_proc_id = 0; // This is initialized for the total_sps == 1 case; otherwise it will be reassigned
    NEIGHBOR_MASK_INDEX nmi = 0;

    ublk_base.read(g_lgi_stream);
    if (ublk_base.ublk_type == DGF_UBLK_INVALID) {
      return;
    }
    UBLK_PROC ublk_proc;

    // Default values for the total_sps == 1 case
    ublk_proc.home_sp = 0;
    ublk_proc.num_ghost_sps = 0;
    ublk_proc.ublk_decomp_flags = 0;

    // compacted simple blocks are processed differently, and the ghost
    // information is sent in a different order by CP. In that case the ublk_proc argument to the parse
    // functions called in the switch statement below wil be unused.

    if (total_sps > 1 && ublk_base.cube_factor == 0) {
      // We use the proc_id field of a  cDGF_PROC_ID record to communicate the number of ghosts, rather
      // than defining a separate record
      //cDGF_PROC_ID home_proc, proc_n_ghosts;
      cDGF_GHOST_INFO ghost_info_rec;
      asINT32 n_ghosts = 0;
      ublk_proc.read(g_lgi_stream);
      home_proc_id = ublk_proc.home_sp;
      ghost_info.clear();
      if (home_proc_id == my_proc_id) {
        //proc_n_ghosts.read(g_lgi_stream);
        n_ghosts = ublk_proc.num_ghost_sps;
        ccDOTIMES(j, n_ghosts) {
          ghost_info_rec.read(g_lgi_stream);
          g_strand_mgr.m_neighbor_sp_map.add_nsp(ghost_info_rec.proc_id);
          ghost_info.push_back(ghost_info_rec);
        }
      } else {
        // Need to pass the NMI to the parser when the ublk is a ghostblk, in which case n_ghosts is 0
        // This means the parser must interperet the ghost_info vector differently for ghostblks and
        // for ublks with ghosts
        ghost_info_rec.read(g_lgi_stream);
        g_strand_mgr.m_neighbor_sp_map.add_nsp(ghost_info_rec.proc_id);
        ghost_info.push_back(ghost_info_rec);
      }
    }

    switch(ublk_base.ublk_type) {
    case DGF_UBLK_SIMPLE: 
      simple_desc.b = ublk_base;
      simple_desc.read(g_lgi_stream);
      parse_ublk_desc(realm, &simple_desc,ublk_proc.ublk_decomp_flags,  home_proc_id, ghost_info, g_lgi_stream);
      break;
    case DGF_UBLK_REAL: 
      real_desc.b = ublk_base;
      real_desc.read(g_lgi_stream);
      parse_ublk_desc(realm, &real_desc, ublk_proc.ublk_decomp_flags, home_proc_id, ghost_info, g_lgi_stream);
      break;
    case DGF_UBLK_MIRROR:
      mirror_desc.b = ublk_base;
      mirror_desc.read(g_lgi_stream);
      parse_ublk_desc(realm, &mirror_desc, ublk_proc.ublk_decomp_flags, home_proc_id, ghost_info, g_lgi_stream);
      break;
    default:
      msg_internal_error("Unknown microblock type %d after reading ublk %d", ublk_base.ublk_type, ublk_id_success);
    }
    ublk_id_success = ublk_base.ublk_id;
  }
}

//----------------------------------------------------------------------------
// read_bsurfels
//----------------------------------------------------------------------------

void sDGF_READER::read_deforming_tire_table()
{
  LOG_MSG("BSURFEL_INIT",LOG_FUNC) << "start";
  cDGF_DEFORMING_TIRE_TABLE table_header;
  table_header.read(g_lgi_stream);

  sim.n_tires = table_header.num_tires;
  sim.tires = new sSIM_TIRE[sim.n_tires];

  std::vector<double> u_knots;
  std::vector<double> v_knots;
  std::vector<double> coeff_carcass;
  std::vector<double> coeff_wrap;

  ccDOTIMES(i, sim.n_tires) {
     u_knots.clear();
     v_knots.clear();
     coeff_carcass.clear();
     coeff_wrap.clear();

    cDGF_DEFORMING_TIRE dgf_tire;
    dgf_tire.read(g_lgi_stream);
    auINT32 nu = dgf_tire.n_knots[0];
    auINT32 nv = dgf_tire.n_knots[1];
    auINT32 ncoeff = dierckx_surface_coeff_array_size(3, nu, nv);
    u_knots.reserve(nu);
    v_knots.reserve(nv);
    coeff_carcass.reserve( ncoeff );

    if ( !dgf_tire.grooved_tire ) {
      coeff_wrap.reserve( ncoeff );
    }

    ccDOTIMES(j,nu) {
      cDGF_DEFORMING_TIRE_VALUE val;
      val.read(g_lgi_stream);
      u_knots.push_back(val.value);
    }

    ccDOTIMES(j,nv) {
      cDGF_DEFORMING_TIRE_VALUE val;
      val.read(g_lgi_stream);
      v_knots.push_back(val.value);
    }

    ccDOTIMES(j,ncoeff) {
      cDGF_DEFORMING_TIRE_VALUE val;
      val.read(g_lgi_stream);
      coeff_carcass.push_back(val.value);
    }

    if ( !dgf_tire.grooved_tire ) {
      ccDOTIMES(j,ncoeff) {
        cDGF_DEFORMING_TIRE_VALUE val;
        val.read(g_lgi_stream);
        coeff_wrap.push_back(val.value);
      }
    }

    new (&sim.tires[i]) sSIM_TIRE(dgf_tire,u_knots,v_knots,coeff_wrap,coeff_carcass);
  }
}

//----------------------------------------------------------------------------
// read_bsurfel_table
//----------------------------------------------------------------------------
void sDGF_READER::read_bsurfel_table()
{
  LOG_MSG("BSURFEL_INIT",LOG_FUNC) << "start";
  cDGF_BSURFEL_TABLE table_header;
  table_header.read(g_lgi_stream);
  LOG_MSG("BSURFEL_INIT").format("table header tag: {} nbsurfel {}",table_header.tag.id, table_header.num_bsurfels);

  cDGF_BSURFEL_DESC bsurfel_desc;
  cDGF_BSURFEL_GEOM bsurfel_geom;
  sim.n_total_bsurfels = 0; 
  STP_BSURFEL_ID n_bsurfels_to_read = table_header.num_bsurfels + 1;

  // sentinel bsurfel indicates end of bsurfel table for this SP
  ccDOTIMES(i, n_bsurfels_to_read) {
    bsurfel_desc.read(g_lgi_stream);
    LOG_MSG("BSURFEL_INIT") << "read bsurfel_desc " << bsurfel_desc.bs.bsurfel_id;
    if (bsurfel_desc.bs.bsurfel_id == STP_INVALID_BSURFEL_ID)
      break;

    bsurfel_geom.read(g_lgi_stream);
    LOG_MSG("BSURFEL_INIT") << "read bsurfel_geom";
    parse_bsurfel_desc(&bsurfel_desc, &bsurfel_geom);
    sim.n_total_bsurfels++;
  }

  if ( sim.n_tires > 0 ) {
    cDGF_DEFORMING_TIRE_VERTEX dvertex;
    while(true) {
      dvertex.read(g_lgi_stream);
      if ( dvertex.vertex_index == STP_INVALID_BSURFEL_ID ) break;
      sINT16 tire_index = dvertex.deforming_tire_index;
      cassert( tire_index >= 0 );
      cassert( tire_index < sim.n_tires );

      sSIM_TIRE * tire = &sim.tires[tire_index];

      sBG_POINT3d uvw( dvertex.uvw[0], dvertex.uvw[1], dvertex.uvw[2] );
      sBG_POINT3d xyz( dvertex.xyz[0], dvertex.xyz[1], dvertex.xyz[2] );

      tire->init_vertex(dvertex.vertex_index, uvw, xyz);
      //tire->init_vertex(dvertex.vertex_index, uvw);

      if ( !dvertex.location_is_accurate ) {
        STP_GEOM_VARIABLE xyz[3];
        ccDOTIMES(i,3) {
          xyz[i] = dvertex.xyz[i];
        }
        simerr_report_error_code(SP_EER_DEFORMING_TIRE_VERTEX_ACCURACY, 0, xyz, 
                                 tire_index, 
                                 dvertex.vertex_index, 
                                 dvertex.uvw[0], 
                                 dvertex.uvw[1], 
                                 dvertex.uvw[2]);
      }
    }
  }

  // For a moving boundary with arbitrary motion, every bsurfel could potentially
  // contribute to a development meas cell depending on its location during
  // each time step
  // Hence, we allocate meas cells on every SP for every face that has bsurfels
  DO_MEAS_WINDOWS(window) {
    if (window->is_development && window->meas_window_type == LGI_SURFACE_WINDOW) {
      cDGF_MOVB_FACES movb_faces;
      movb_faces.read(g_lgi_stream);
      LOG_MSG("BSURFEL_INIT") << "read movb faces";
      ccDOTIMES(axis, 3) {
        ccDOTIMES(i, movb_faces.size()) {
          asINT32 face = movb_faces[i];
          asINT32 n_segments = window->entity_n_segments[axis][face];
          for (asINT32 j=0; j < n_segments; j++) {
            window->create_surfel_meas_cell(window->entity_first_meas_cell[axis][face] + j);
          }
        }
      }
    }
  }

}
//----------------------------------------------------------------------------
// read_surfel_table
//----------------------------------------------------------------------------

void sDGF_READER::read_surfel_table(STP_REALM realm)
{

  g_surfel_table[realm].last_added_surfel_id = -1;
  check_consistency_and_validate_surface_physics_descriptors();
  
  //cDGF_PROC_ID proc,proc_n_ghosts;
  cDGF_SURFEL_PROC proc;
  cDGF_PROC_ID radiation_proc;
  cDGF_PROC_ID backside_radiation_proc;
  cDGF_SURFEL_PROC_GHOST ghost_proc;
  std::vector<cDGF_SURFEL_PROC_GHOST> ghost_procs;
  
  cDGF_SURFEL_TABLE table_header;
  table_header.read(g_lgi_stream);
  cDGF_SEED_FROM_MEAS_INFO seed_from_meas_info;
  seed_from_meas_info.read(g_lgi_stream);
  sim.n_seed_from_meas_descs = seed_from_meas_info.n_seed_from_meas_descs;
  sim.seed_from_meas_scale_factors = new sFLOAT*[sim.n_seed_from_meas_descs];
  sim.m_seed_from_meas_controls = cnew sSEED_CONTROL[sim.n_seed_from_meas_descs];
  sim.transient_boundary_seeding.resize(sim.n_seed_from_meas_descs);
  ccDOTIMES(imeas, sim.n_seed_from_meas_descs) {
    cDGF_SMART_SEED_CONTROL seed_from_meas_control;
    lgi_read(g_lgi_stream, seed_from_meas_control);
    sim.m_seed_from_meas_controls[imeas].init(seed_from_meas_control);
    sim.transient_boundary_seeding[imeas].set_params(seed_from_meas_control);
    if(!sim.is_mean_vel_added)
      sim.is_mean_vel_added = seed_from_meas_control.is_mean_vel_added;
  }

  // Declare this outside the loop so that each successive read into the object
  // reuses storage previously allocated. 
  cDGF_SURFEL_DESC surfel_desc;

  // no sentinel surfel needed since the surfels are not compacted

  for (uINT64 i = 0; i < sim.n_total_surfels[realm]; i++) {
    asINT32 n_ghosts = 0;
    uINT8 ghost_flags = 0;
    cDGF_TBS tbs;
    tbs.transient_boundary_seeding = FALSE;
    sim.surfel_tbs_info.meas_index = -1;
    ghost_procs.clear();
    proc.read(g_lgi_stream);
    radiation_proc.proc_id = PROC_INVALID;
    backside_radiation_proc.proc_id = PROC_INVALID;

    if (proc.home_sp == my_proc_id) {
      n_ghosts = proc.num_ghost_sps;
      tbs.read(g_lgi_stream);
      sim.surfel_tbs_info.is_tbs = tbs.transient_boundary_seeding;
      if(tbs.transient_boundary_seeding) {
        sim.surfel_tbs_info.meas_index = tbs.meas_index;
        sim.transient_boundary_seeding[tbs.meas_index].set_var_mask(tbs);
      }

    } else {
      // Ghost surfel; home_sp is source proc, num_ghost_sps is ghost_flags
      g_strand_mgr.m_neighbor_sp_map.add_nsp(proc.home_sp);
    }
    
    // Home SP only; n_ghosts will be zero otherwise
    ccDOTIMES(j, n_ghosts) {
      ghost_proc.read(g_lgi_stream);
      g_strand_mgr.m_neighbor_sp_map.add_nsp(ghost_proc.ghost_sp);
      ghost_procs.push_back(ghost_proc);
    }
    //msg_print("Reading surfel desc; realm = %d, i = %d",realm,i);
    surfel_desc.read(g_lgi_stream);
    // Note: if this is a ghost surfel, proc.num_ghost_sps is actually ghost_flags,
    // and is passed as such, The ghost_flags argument to parse_surfel_desc is only for ghost
    // surfels.

    if (sim.is_radiation_model && proc.home_sp == my_proc_id && surfel_desc.s.radiation_patch_id >= 0) {
      radiation_proc.read(g_lgi_stream);
      backside_radiation_proc.read(g_lgi_stream);
    }

    parse_surfel_desc(&surfel_desc, 
                      realm, 
                      proc.home_sp, 
                      RP_PROC(radiation_proc.proc_id), 
                      RP_PROC(backside_radiation_proc.proc_id),
                      proc.num_ghost_sps, 
                      ghost_procs);

    if(tbs.transient_boundary_seeding)
      sim.transient_boundary_seeding[tbs.meas_index].update_tbs_var_count(tbs);
  }

}

//----------------------------------------------------------------------------
// read_gap_contact_table
//----------------------------------------------------------------------------
void sDGF_READER::read_gap_contact_table() {
  cDGF_GAP_CONTACT_TABLE t;
  t.read(g_lgi_stream);
  
  //Clears the map and loads data into it as we receive it
  g_contact_info.clear();
  cDGF_GAP_CONTACT_AREA_SET stream_cas;
  sCONTACT_WEIGHT_SET weight_set;
  sCONTACT_WEIGHT_ITEM weight_item;
  ccDOTIMES(iws, t.num_contact_area_sets) {
    //Weight_set is read with number of expected weight items
    stream_cas.read(g_lgi_stream);
    weight_set.m_weights.clear();
    //Then, all the weight_items are received. 
    ccDOTIMES(iwi, stream_cas.num_contact_area_items) {
      lgi_read(g_lgi_stream, weight_item.m_opposite_surfel_id);
      lgi_read(g_lgi_stream, weight_item.m_weight);
      lgi_read(g_lgi_stream, weight_item.m_opposite_weight);
      weight_set.m_weights.push_back(weight_item);
      // msg_print("contact %d %d %f %f", stream_cas.primary_surfel_id, weight_item.m_opposite_surfel_id,
      //     weight_item.m_weight, weight_item.m_opposite_weight);
    }
    //Now that the info is complete, loads it in the map
    //Ensures also that no weightless element is received, since those correspond to secondary surfels and not part of
    //the map and should not have been commed by the CP. In this way, we can distinguish later primary_secondary from
    //secondary by checking if they are present or not in the map.
    cassert(stream_cas.num_contact_area_items > 0);
    g_contact_info[stream_cas.primary_surfel_id] = weight_set;
  }
}

//----------------------------------------------------------------------------
// read_averaged_contacts
//----------------------------------------------------------------------------
VOID sDGF_READER::read_averaged_contacts()
{
  LGI_TAG tag = lgi_peek_tag(g_lgi_stream);
  if (tag.id != DGF_AVERAGED_CONTACTS_HEADER_TAG) {
    return;
  }
  cDGF_AVERAGED_CONTACTS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  if (header.num_averaged_contacts <= 0) {
    return;
  }

  g_thermal_averaged_contacts.init(header.num_averaged_contacts);
  asINT32 pd_index, num_faces, face_id;
  ccDOTIMES(avg_contact_idx, header.num_averaged_contacts) {
    //read the physics descriptor
    lgi_read(g_lgi_stream, pd_index);
    g_thermal_averaged_contacts.add_physics_descriptor(pd_index);
    //reads the faces involved in this averaged contact
    lgi_read(g_lgi_stream, num_faces);
    ccDOTIMES(i, num_faces) {
      lgi_read(g_lgi_stream, face_id);
      g_thermal_averaged_contacts.add_face(face_id, avg_contact_idx);
    }
  }
}

//----------------------------------------------------------------------------
// read_seed_from_meas_scale_factors
//----------------------------------------------------------------------------
void sDGF_READER::read_seed_from_meas_scale_factors()
{
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    size_t n_pts_total;
    lgi_read(g_lgi_stream, &n_pts_total, sizeof(n_pts_total));
    sim.seed_from_meas_scale_factors[i] = new sFLOAT[n_pts_total];
    lgi_read(g_lgi_stream, &(sim.seed_from_meas_scale_factors[i][0]), n_pts_total*sizeof(sim.seed_from_meas_scale_factors[i][0]));
  }

  DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      surfel->rescale_seed_from_meas_data();
    }
  }

  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if (sim.seed_from_meas_scale_factors[i] != NULL)
      delete [] sim.seed_from_meas_scale_factors[i];
  }
  if (sim.seed_from_meas_scale_factors != NULL)
    delete [] sim.seed_from_meas_scale_factors;
}

//----------------------------------------------------------------------------
// read_cdi_global_info
//----------------------------------------------------------------------------

static VOID parse_cdi_global_header_simv_flags(sINT32 flags)
{
  sim.is_lb_model          = TRUE;
  sim.is_flow              = (flags & CDI_ENABLE_FLOW_SOLVER)    != 0;
  sim.is_turb_model        = (flags & CDI_IS_TURB_MODEL)         != 0;
  sim.is_heat_transfer     = (flags & CDI_IS_HEAT_TRANSFER)      != 0;
  sim.is_internal_flow     = (flags & CDI_IS_INTERNAL_FLOW)      != 0;
  sim.is_incompressible_solver     = (flags & CDI_TRUE_INCOMPRESSIBLE) != 0;
  sim.is_high_subsonic_mach_regime = (flags & CDI_HIGH_SUBSONIC_MACH_REGIME) != 0;
  sim.is_HSExt_solver 		   = (flags & CDI_HIGH_SUBSONIC_EXTENDED_MACH_REGIME) != 0;
  sim.is_hydrogen_eos              = (flags & CDI_HYDROGEN_EOS) != 0;
  sim.is_high_subsonic_HT_off = FALSE;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  sim.is_particle_model    = (flags & CDI_PARTICLE_MODELING_ENABLED)  != 0;
//#endif
  sim.is_5g_sim            = (flags & CDI_IS_MULTI_PHASE) != 0;

  sim.is_liquid            = (flags & CDI_IS_LIQUID) != 0;

  sim.use_melting_solver    = (flags & CDI_EUTECTIC) != 0;

  sim.is_conduction_model  = (flags & CDI_ENABLE_CONDUCTION_SOLVER) != 0;
  sim.is_radiation_model   = (flags & CDI_ENABLE_RADIATION) != 0;

  if ((flags & CDI_USER_DEFINED_SCALAR_TRANSPORT) != 0) { 
    sim.uds_solver_type = LB_UDS;
    sim.is_scalar_model  = TRUE;
  } else if ((flags & CDI_WATER_VAPOR_TRANSPORT) != 0) {
    sim.uds_solver_type = PDE_UDS;
    sim.is_scalar_model  = TRUE;
  }  
  else {
    sim.uds_solver_type = INVALID_UDS;
    sim.is_scalar_model  = FALSE;
  }

  //#if BUILD_6X_SOLVER
  //sim.use_hs_lb_entropy_solver = sim.is_high_subsonic_mach_regime && ((flags & CDI_ENABLE_LB_ENTROPY_SOLVER) != 0);
  //else
  //sim.use_hs_lb_entropy_solver = FALSE;
  //endif

  sim.is_legacy_energy_solver  = FALSE;
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE  
    sim.is_legacy_energy_solver  = (flags & CDI_LEGACY_ENERGY_SOLVER) != 0;
#endif


#if BUILD_D39_LATTICE
  sim.use_hybrid_ts_hs_solver = (flags & CDI_TRANSONIC_FLOW_ONLY_IN_RRF) != 0;
#else
  sim.use_hybrid_ts_hs_solver = FALSE;
#endif

//  sim.use_lb_energy_solver = FALSE;
//#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  //@@@ need to modify after lb_energy_solver become an option of PowerCase
//#if BUILD_6X_SOLVER
//  if (sim.is_high_subsonic_mach_regime)
//#endif
//  sim.use_lb_energy_solver = g_use_lb_energy; //input from exa_turb file
//#endif


  /* For high subsonic flow, heat transfer should be always on in simulator. */
  if (sim.is_high_subsonic_mach_regime && !sim.is_heat_transfer) {
    sim.is_heat_transfer = TRUE;
    sim.is_high_subsonic_HT_off = TRUE;
  }

  sim.num_dims             = flags & CDI_IS_2D ? 2 : 3;

  if (sim.num_dims == 2)
    cDGF_GLOBAL_STATE::is_2d = TRUE;

#if BUILD_5G_LATTICE
  if (!sim.is_5g_sim || sim.is_turb_model || sim.is_heat_transfer)//test, may not be necessary
    msg_error("CDI file error: not 5g version.");
#endif

#if BUILD_D39_LATTICE
  cDGF_GLOBAL_STATE::lattice_type = STP_LATTICE_D39;
  cDGF_GLOBAL_STATE::latvec_mask_n_bytes = 4;
  sim.is_transonic_sim = TRUE;
#else
  sim.is_transonic_sim = FALSE;
#endif

#if BUILD_D19_LATTICE
  if (my_proc_id == 0) {
    if (sim.is_high_subsonic_mach_regime)
      msg_print_no_prefix("Simulation is using high subsonic solver");
    else
      msg_print_no_prefix("Simulation is using low subsonic solver");
  }
#endif
}

static VOID parse_cdi_global_header_cmdline_flags(sINT32 flags)
{
  sim.do_smart_seed        = (flags & LGI_DO_SMART_SEED)         != 0;
  sim.extrapolate_seed     = (flags & LGI_EXTRAPOLATE_SEED)      != 0;
  sim.smart_seed_boundaries= (flags & LGI_SMART_SEED_BOUNDARIES) != 0;
  sim.is_full_checkpoint_restore = (flags & LGI_FULL_CKPT_RESTORE) != 0;
  sim.is_mme_checkpoint_restore = (flags & LGI_MME_CKPT_RESTORE) != 0;

  sim.fixed_temp_walls        = (flags & LGI_FIXED_TEMP_WALLS   ) != 0;
  sim.use_local_vel_fan_model = (flags & LGI_LOCAL_VEL_FAN_MODEL) != 0;
  sim.no_swirl_model          = (flags & LGI_NO_SWIRL_MODEL     ) != 0;
  sim.is_fluid_turb_solver_1  = (flags & LGI_FLUID_TURB_SOLVER_1) != 0;
  g_no_reserve_addr_space     = (flags & LGI_NO_RESERVE_ADDR_SPACE) != 0;
  g_short_mpi_thread_sleep    = (flags & LGI_SHORT_MPI_THREAD_SLEEP) != 0;

  sim.smart_seed_contact_angle = (flags & LGI_SMART_SEED_CONTACT_ANGLE) != 0;

  sim.enable_film_trajectory_measurement = (flags & LGI_ENABLE_FILM_TRAJECTORY_MEASUREMENTS) !=0; //PR41964

  sim.disable_scalar_diffusivity_bound = (flags & LGI_DISABLE_SCALAR_DIFFUSIVITY_BOUND)  !=0;
}

VOID sDGF_READER::read_cdi_global_info()
{
  LGI_CDI_GLOBAL_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  parse_cdi_global_header_simv_flags(header.simv_flags);
  parse_cdi_global_header_cmdline_flags(header.cmdline_flags);

#if BUILD_5G_LATTICE
  if (sim.do_smart_seed && g_mp_meas_seed_pres)
    msg_warn("Only seed body force!\n");
#endif

  sim.cdi_major_version           = header.cdi_major_version;
  sim.cdi_minor_version           = header.cdi_minor_version;

  sim.thermal_accel.start         = header.thermal_accel.start;
  sim.thermal_accel.repeat        = header.thermal_accel.repeat;
  sim.thermal_accel.period        = header.thermal_accel.period;
  sim.thermal_accel.interval      = header.thermal_accel.interval;

  sim.mean_temp                   = FIXED_TEMP;
  sim.thermal_feedback_is_on      = FALSE;
  sim.lattice_gas_const           = header.lattice_gas_const;
  sim.one_over_lattice_gas_const  = 1.0 / header.lattice_gas_const;
  sim.one_minus_lattice_gas_const = 1.0F - header.lattice_gas_const;
  sim.lattice_gamma               = header.lattice_gamma;

  set_hs_and_ts_solver_types(sim);

  sim.min_bc_pressure = sim.is_high_subsonic_mach_regime? MIN_BC_PRESSURE_HIGH_SUBSONIC : MIN_BC_PRESSURE;
#if BUILD_D39_LATTICE
  sim.min_bc_pressure =  MIN_BC_PRESSURE_TRANSONIC;
#endif 

  if (header.custom_vars.numParticleEmitters >=0) {
    sim.cvid_helper = new sSRI_CUSTOM_VAR_ID_HELPER(header.custom_vars.numParticleEmitters,
						    header.custom_vars.numParticleMaterials,
						    header.custom_vars.numUDScalars,
						    header.custom_vars.num5gFluidComponents,
						    header.custom_vars.numParticleModelingVars,
						    header.custom_vars.numUDSVars,
						    header.custom_vars.num5gVars);
  } else {
    sim.cvid_helper =  new sSRI_CUSTOM_VAR_ID_HELPER(0,0,0,0);
  }

  sim.is_temp_dependent_liquid = FALSE;
  sim.is_mean_vel_added        = FALSE;

#if BUILD_D19_LATTICE
  if(sim.is_high_subsonic_mach_regime) {
    sim.C_v = sim.lattice_gas_const /(sim.lattice_gamma - 1.0);
  } else {
    sdFLOAT gamma_pass = 1.5;
    sim.C_v = sim.lattice_gas_const /(gamma_pass-1.0);
  }

  //liquid
  CDI_LIQUID_MATERIAL liquid_type = (CDI_LIQUID_MATERIAL)header.liquid_type;
  if(sim.is_liquid && (liquid_type != CDI_LM_USER_DEFINED) && !sim.use_melting_solver)
    sim.is_temp_dependent_liquid = TRUE;

  if (sim.is_temp_dependent_liquid) {
    g_lpms = xnew sCDI_LIQUID_PARAMS;
    g_lpms->init(liquid_type);
  }

#elif BUILD_D39_LATTICE
    sim.C_v = sim.lattice_gas_const /(sim.lattice_gamma - 1.0);

    g_scatter_Mach_threshold2 = g_scatter_Mach_threshold * g_scatter_Mach_threshold * sim.lattice_gamma * sim.lattice_gas_const;
#elif BUILD_5G_LATTICE
  sdFLOAT gamma_pass = 1.5;
  sim.C_v = sim.lattice_gas_const /(gamma_pass-1.0);
#endif

  sim.C_p = sim.lattice_gas_const + sim.C_v;
  sim.one_over_C_p = 1.0F/sim.C_p;

  if (header.mean_temp > 0 && sim.is_heat_transfer) {
    sim.mean_temp = header.mean_temp;
    sim.thermal_feedback_is_on = TRUE;
  } else if (sim.is_high_subsonic_mach_regime) {
    sim.thermal_feedback_is_on = TRUE;
  } 
  sim.one_over_mean_temp = 1.0 / sim.mean_temp;

#if BUILD_5X_SOLVER
  if (!sim.is_turb_model) {
    g_adv_fraction = 1.0;
    g_use_filter_noneq = 1;
  }

  if (sim.is_incompressible_solver)
    g_use_filter_noneq = 1;
#elif BUILD_5G_LATTICE
  g_adv_fraction = g_mp_fraction_advect;

  if (g_adv_fraction != 1.0)
    msg_error("Fractional advection is not allowed, please set mp_fraction_advect to 1.");

    // sf_theta_threshold_alpha depends on mp_sample_potential_factor, 10:382, 20:432, 30:450, 40:460, 50:464
  sdFLOAT sf_theta_threshold_alpha;
  if (g_mp_sample_potential_factor == 10.0)
    sf_theta_threshold_alpha = 382.0;
  else if (g_mp_sample_potential_factor == 20.0)
    sf_theta_threshold_alpha = 432.0;
  else if (g_mp_sample_potential_factor == 30.0)
    sf_theta_threshold_alpha = 450.0;
  else if (g_mp_sample_potential_factor == 40.0)
    sf_theta_threshold_alpha = 459.0;
  else
    sf_theta_threshold_alpha = 464.0;

  if (g_sf_theta_threshold < 90.0)
    g_sf_rho_threshold = (90.0 - g_sf_theta_threshold) / sf_theta_threshold_alpha;
  else
    g_sf_rho_threshold = (g_sf_theta_threshold - 90.0) / sf_theta_threshold_alpha;

#endif

  //modify g_eq_3rd_order_d19 for lb_energy solver
  if (sim.use_lb_energy_solver) {
    g_eq_3rd_order_d19 = g_eq_3rd_order_d19_lb_energy;
#if BUILD_D39_LATTICE
    g_u_scatter = g_u_scatter_ts_lb_energy;
#endif
  }

  //modify flags required for entropy solver here
  if(sim.is_high_subsonic_mach_regime){
    g_adv_fraction = g_adv_fraction_hs;
    g_Mach_cap = 0.95;
    g_use_filter_noneq =1;
    //g_T_lb_solver_start_time=999999999;
    g_alpha_vel_sqrd_correction=0.0;
    g_eq_3rd_order_d19 = 0.7396;
    if (g_adv_fraction < 1.0)
      g_use_feq_for_frac_adv = 1;
    else 
      g_use_feq_for_frac_adv = 0;
    g_T_over_T0_underrelax = g_T_over_T0_underrelax_hs;
    g_nuT_ceiling = g_nuT_ceiling_hs;
    g_uds_diffusivity_ceiling = g_uds_diffusivity_ceiling_hs;
    g_ablm_floor = g_ablm_floor_hs;
    g_k_floor = g_k_floor_hs;
    g_swirl_factor = g_swirl_factor_hs;
    g_nu_base_eps_floor = g_nu_base_eps_floor_hs;
  } 
#if BUILD_D19_LATTICE
  if (sim.is_HSExt_solver){
    g_alpha_stab_udotf = g_HSExt_alpha_stab_udotf;
    g_alpha_stab_press = g_HSExt_alpha_stab_press;
    g_max_lattice_vel_sqrd = g_HSExt_max_lattice_vel_sqrd; 
    //g_filter_1st_order_alpha = g_HSExt_filter_1st_order_alpha;
    g_Mach_cap = g_HSExt_Mach_cap;
  }
  if(sim.is_hydrogen_eos){
    g_alpha_stab_udotf = g_alpha_stab_udotf_h2;
    g_alpha_stab_press = g_alpha_stab_press_h2;
  }

#if BUILD_6X_SOLVER
  if(!header.use_PF6_VLES_model && sim.is_high_subsonic_mach_regime && (!sim.is_legacy_energy_solver))
    g_RT_gamma_swirl = 1;
  if (g_RT_gamma_swirl) {
    g_swirl_cutoff_pow = 0.0F;
    g_u_scatter = 0.98;
  }
  if (header.use_PF6_VLES_model && sim.is_high_subsonic_mach_regime) {
    if (my_proc_id == 0)
      msg_print_no_prefix("Using standard PowerFLOW VLES model. \n");
  }
#endif
#endif

#if BUILD_6X_SOLVER
  if(header.checkpoint_body_force) {
    g_meas_write_pres_d19 = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Output conformal body force. \n");
  }
  if(header.seed_body_force) {
    g_meas_seed_pres_d19 = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Seed conformal body force. Only body force and pressure are seeded. \n");
  }
  if(header.laplace) {
    g_laplace_solver_d19 = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Laplace solver. \n");
  }
  if(header.seed_body_force_scale > 0){
    g_seed_body_force_scale = header.seed_body_force_scale;
    if (my_proc_id == 0)
      msg_print_no_prefix("Scale conformal body force. Scale factor: %f \n", g_seed_body_force_scale);
  }
#endif

#if BUILD_5G_LATTICE
  if(header.checkpoint_body_force) {
    g_mp_meas_write_pres = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Output conformal body force. \n");
  }
  if(header.seed_body_force) {
    g_mp_meas_seed_pres = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Seed conformal body force. \n");
  }
  if(header.laplace) {
    g_mp_laplace_solver = 1;
    if (my_proc_id == 0)
      msg_print_no_prefix("Laplace solver. \n");
  }
  if(header.seed_body_force_scale > 0){
    g_mp_seed_grad_p_scale = header.seed_body_force_scale;
    if (my_proc_id == 0)
      msg_print_no_prefix("Scale conformal body force. Scale factor: %f \n", g_mp_seed_grad_p_scale);
  }
#endif


#if BUILD_D39_LATTICE
   g_max_lattice_vel_sqrd_hs_region = g_hybExt_max_lattice_vel_sqrd; 
   g_Mach_cap_hs_region = g_hybExt_Mach_cap;
  g_Mach_cap = g_Mach_cap  * sqrt(0.25/sim.lattice_gas_const);
  if (header.use_PF6_VLES_model || sim.is_legacy_energy_solver) {
    g_RT_gamma_swirl = 0;
    //g_kepsilon_ur = 1.0F;
    if (my_proc_id == 0)
      msg_print_no_prefix("Using standard PowerFLOW VLES model. \n");
  }
  if (g_RT_gamma_swirl) {
    g_swirl_cutoff_pow = 0.0F;
    //g_u_scatter = 0.98;
    g_scatter_Mach_alpha = 0.0F;
  }
#endif 

  g_one_over_adv_fraction = 1.0 / g_adv_fraction;

  if (g_adv_fraction != 1.0) {
    g_z_states_scaling_fraction = 1.0 / (1.0 - g_adv_fraction);
  } else {
    g_z_states_scaling_fraction = 0.0;
  }
  g_adv_fractional_factor     = (1.0 - g_adv_fraction) * g_one_over_adv_fraction;

  if (g_adv_fraction < 1.0 && !g_use_feq_for_frac_adv)
    g_is_frac_adv = 1;
  else
    g_is_frac_adv = 0;

  sim.accum_energy_meas_in_t_solver = sim.is_t_timestep_smaller_than_lb_timestep && sim.thermal_feedback_is_on;

  sim.char_temp                 = header.char_temp;
  sim.char_temp_f               = header.char_temp;
  sim.one_over_char_temp        = 1.0 / header.char_temp;
  sim.char_density              = header.char_density * g_density_scale_factor;

//  sim.char_density				= 300000;
  sim.char_density_f            = sim.char_density;
  sim.char_pressure             = sim.char_temp * sim.char_density * sim.lattice_gas_const;
  sim.char_pressure_f           = sim.char_pressure;
  sim.char_vel                  = header.char_vel;
  sim.one_over_char_vel         = 1.0 / header.char_vel;
  sim.sqrt_4_char_vel           = sqrt(4.0 * header.char_vel);
  sim.char_length               = header.char_length;
  sim.reynolds_number           = header.reynolds_number;
  sim.char_vel_over_char_len    = header.char_vel / header.char_length;
  sim.fluid_Prandtl_number      = header.fluid_Prandtl_number;
  sim.turb_Prandtl_number       = header.turb_Prandtl_number;
  g_nu_molecular                = header.nu_molecular;

  g_timescale.m_n_lb_base_steps       = header.n_lb_base_steps;
  g_timescale.m_n_t_pde_base_steps    = header.n_t_base_steps;
  g_timescale.m_n_ke_pde_base_steps   = header.n_ke_base_steps;
  g_timescale.m_n_uds_pde_base_steps  = header.n_uds_base_steps;
  g_timescale.m_n_conduction_pde_base_steps = header.n_conduction_base_steps;
  g_timescale.m_n_radiation_base_steps = header.n_radiation_base_steps;
  
  g_timescale.m_n_particle_base_steps = header.n_particle_base_steps;
  g_timescale.m_acous_start_time      = header.acous_start_time;

#if BUILD_D19_LATTICE
  sim.is_ke_super_cycling = (!sim.is_high_subsonic_mach_regime) && sim.is_turb_model && (g_timescale.m_n_lb_base_steps < g_timescale.m_n_ke_pde_base_steps);
#else
  sim.is_ke_super_cycling = FALSE;
#endif
  if (sim.is_ke_super_cycling) {
    sdFLOAT lb_ke_step_ratio = g_timescale.m_n_ke_pde_base_steps / g_timescale.m_n_lb_base_steps;
    if (g_ke_super_cycling_alpha > lb_ke_step_ratio)
      g_ke_super_cycling_alpha = lb_ke_step_ratio;
  }

  sim.use_temp_dep_gamma        = header.variable_gamma;

  //cooling air leakage model
  eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum cooling_air_opt = static_cast<eCDI_COOLING_AIR_LEAKAGE_OPTION::Enum>(header.cooling_air_opt);
  if (cooling_air_opt == eCDI_COOLING_AIR_LEAKAGE_OPTION::Off)
    sim.pm_force_factor = g_pm_force_off;
  else if (cooling_air_opt == eCDI_COOLING_AIR_LEAKAGE_OPTION::Low)
    sim.pm_force_factor = g_pm_force_low;
  else if (cooling_air_opt == eCDI_COOLING_AIR_LEAKAGE_OPTION::Medium)
    sim.pm_force_factor = g_pm_force_medium;
  else if (cooling_air_opt == eCDI_COOLING_AIR_LEAKAGE_OPTION::High)
    sim.pm_force_factor = g_pm_force_high;
  else
    msg_internal_error("Unknown cooling air option %d", header.cooling_air_opt);

  if(sim.lattice_gamma != 1.0)
    sim.char_entropy            =  sim.C_v * log(sim.char_temp/pow(sim.char_density,sim.lattice_gamma -1.0));
  
  sim.is_pf_model               = FALSE;
  sim.n_user_defined_scalars    = header.n_user_defined_scalars; //1 for water_vapor_transport
  sim.has_avg_mme_window        = header.has_average_mme_window;
  sim.local_vel_freeze          = header.local_vel_freeze;
  sim.need_to_scale_viscosity   = header.need_to_scale_viscosity;

  sim.store_frozen_vars = (sim.local_vel_freeze || sim.has_avg_mme_window);

#if BUILD_5G_LATTICE
  if (sim.n_user_defined_scalars > 0) { //read "n_user_defined_scalars" from user_input.init in CP/cp_cdi_reader.cc until Case support for 5G is available
    sim.uds_solver_type = LB_UDS;
    sim.is_scalar_model  = TRUE;
  }
#endif

  if (sim.is_scalar_model) {
    if ((sim.n_user_defined_scalars <= 0 || sim.n_user_defined_scalars> MAX_N_USER_DEFINED_SCALARS)  && my_proc_id==0)   //already check in cp
       msg_error("Invalid number of user defined scalars.");

    if (g_is_pf_model) {
      if (sim.is_5g_sim && my_proc_id==0)
	msg_error("Phasefield is not implemented in 5G.");
      else if ((sim.is_high_subsonic_mach_regime || sim.is_transonic_sim) && my_proc_id==0)
	msg_error("Phasefield is not implemented in HS or TS.");
      else if ((g_timescale.m_n_lb_base_steps!=g_timescale.m_n_uds_pde_base_steps) && my_proc_id==0)
	msg_error("Phasefield requires n_lb_base_steps(%ld) equal to n_uds_base_steps(%ld).\n",
                  g_timescale.m_n_lb_base_steps,g_timescale.m_n_uds_pde_base_steps);
    }

#if BUILD_D19_LATTICE
    if ((sim.uds_solver_type == LB_UDS) && g_is_pf_model) {
      sim.is_pf_model = TRUE;      
      if (sim.n_user_defined_scalars != 2)
	msg_error("Phasefield solver requires two scalars.");
    }
#endif

    { //@@@ do we really need them?
      sim.char_uds = g_user_char_UDS;
      sim.char_uds_f = g_user_char_UDS;
    }   
  }

  if (sim.is_heat_transfer) {
    // CONDUCTION-CHECK: do we really need a separate flag for shell conduction? Alternatively,
    // we can check is_conduction_model and then let the shell surfel loops (which may or may not
    // be empty) decide whether to perform any shell conduction calculations?
    // One of the benefits is the use of this flag to avoid surfel vertex info initialization?
    if (sim.is_conduction_model) {
      if (g_pfc_enable_shell_conduction == 1) {
        sim.is_shell_conduction_model = TRUE;
      } else {
        sim.is_shell_conduction_model = FALSE;
      }
    }
  }

#ifndef CONDUCTION_USE_DEFAULT_FOR_ALL_PARAMETERS
  /* If temp_bc uses q_bc like LS contribution, we are forced to use conventional temp BC formulation */
  if (g_pfc_temp_bc_ls_contribution_like_q_bc == 1 && g_pfc_use_conventional_temp_bc == 0) {
    if (my_proc_id == 0) msg_warn("Changing pfc_use_conventional_temp_bc to 1 due to exa_turb parameter");
    g_pfc_use_conventional_temp_bc = 1;
  }
#endif

  //melting solver
  sim.is_temp_dependent_AdBlue = FALSE;

#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
  const BOOLEAN all_solvers_have_same_timestep = 
    ((!sim.is_heat_transfer || (g_timescale.m_n_lb_base_steps == g_timescale.m_n_t_pde_base_steps))
     && (!sim.is_turb_model || (g_timescale.m_n_lb_base_steps == g_timescale.m_n_ke_pde_base_steps))
     && (!sim.is_scalar_model || (g_timescale.m_n_lb_base_steps == g_timescale.m_n_uds_pde_base_steps))
     && (!sim.is_conduction_model || (g_timescale.m_n_lb_base_steps == g_timescale.m_n_conduction_pde_base_steps))
     && (!sim.is_radiation_model || g_timescale.m_n_lb_base_steps == g_timescale.m_n_radiation_base_steps));

  if (!all_solvers_have_same_timestep) {
    if (my_proc_id == 0) {
      std::string radiation_base_steps_string;

      if (sim.is_radiation_model) {
        radiation_base_steps_string = ", RAD (" + std::to_string(g_timescale.m_n_radiation_base_steps) + ")";
      }

      msg_print_no_prefix("Base steps: LB (%ld), T (%ld), KE (%ld), UDS (%ld), COND (%ld)%s",
          g_timescale.m_n_lb_base_steps, g_timescale.m_n_t_pde_base_steps,
          g_timescale.m_n_ke_pde_base_steps, g_timescale.m_n_uds_pde_base_steps,
          g_timescale.m_n_conduction_pde_base_steps,
          radiation_base_steps_string.c_str());
    }
  }
#endif

#if BUILD_D19_LATTICE
  if (sim.is_heat_transfer) {
    sim.thermal_accel.is_hacked = FALSE;
    sim.thermal_accel.hacked_start = -1;
    sim.thermal_accel.hacked_period = -1;
    sim.thermal_accel.hacked_stop = -1;
    sim.thermal_accel.T_solver_switch_time_for_surfel_recv_group  = -1;
    sim.thermal_accel.T_solver_switch_time_for_ublk_recv_group  = -1;
    sim.T_lb_scalar_solver_start_time = g_T_lb_solver_start_time;

    if (sim.use_melting_solver) {
      sim.cp_liq_over_cp_sol = header.specific_heat_ratio_liquid_to_solid;
      sim.cp_sol_over_cp_liq = 1.0 / header.specific_heat_ratio_liquid_to_solid;
      //sim.A_coef_melt = header.thermal_conductivity_solid * sim.C_p / (header.thermal_conductivity_liquid * header.specific_heat_solid);
      sim.A_coef_melt = header.thermal_conductivity_ratio_solid_to_liquid * header.specific_heat_ratio_liquid_to_solid;

      sim.T_melt = header.melting_temp; //g_Tmelt * one_deg;
      sim.T_melt_up = header.latent_heat_of_fusion / sim.C_p; //g_lat_heat_melt * one_deg / g_cpl;
      sim.T_boil = header.boiling_temp;

      sim.is_temp_dependent_AdBlue = TRUE;
      if (g_AB_td <= 0.0)  //backdoor to internal test
        sim.is_temp_dependent_AdBlue = FALSE;
      sim.T_lb_scalar_solver_start_time = g_T_lb_scalar_solver_start_time; // use pde t solver 
    }
  }
#endif

#if BUILD_5G_LATTICE
  g_inverse_surface_tension = (g_mp_surface_tension == 0.) ? 0 : 1.0 / g_mp_surface_tension;
  g_inverse_rock_cs_area = (g_mp_rock_cs_area == 0.) ? 0 : 1.0 / g_mp_rock_cs_area;

  if (g_is_multi_component) {
    sdFLOAT mp_G_mag = fabs(g_mp_G[0][1]);   // Assume mp_G is axis-symmetric
    if (mp_G_mag < g_mp_G_mag_slip_threshold)
      g_sf_slip_contraction_factor = mp_G_mag / g_mp_G_mag_slip_threshold;
  }

#endif

  // Initialize some simulation units from this header
  sim.meters_per_cell           = header.meters_per_cell;

  if (sim.meters_per_cell == 0)
    sim.meters_per_cell = 1;

  sim.one_mps_lattice           = header.seconds_per_timestep / sim.meters_per_cell;
  sim.one_over_300k_lat         = header.kelvins_per_lattice_temp / 300.0;
  sim.one_over_273k_lat         = header.kelvins_per_lattice_temp / 273.0;
  sim.one_over_1k_lat           = sim.one_over_300k_lat * 300.0;
  sim.one_k_lat                 = 1.0 / header.kelvins_per_lattice_temp;
  sim.rho_kg_m3                 = 1.176829 /(sim.char_temp * sim.one_over_300k_lat);
  sim.one_over_rho_water        = 1.0 / 919.0;

  g_state_cap                     = g_state_cap_coeff * sim.char_density;

#if BUILD_5G_LATTICE
  g_mp_den_cap                    = g_mp_den_cap_coeff * 0.22;
  g_mp_den_floor                  = g_mp_den_floor_coeff * 0.22;
#endif
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  sim.kilos_per_particle        = header.kilos_per_particle;
//#endif


  g_B_turb = g_yplus_ht_crit * (header.fluid_Prandtl_number - g_turb_Prandtl_wall) / g_turb_Prandtl_wall + g_B_zero;

  dFLOAT one_th = 1.0 / 3.0;

  if (sim.is_heat_transfer) {
    dFLOAT abv = sim.fluid_Prandtl_number;
    dFLOAT abn = 3.85 * pow(abv, one_th) - 1.3;
    sim.B_new_twf = abn * abn + 1.3;  
    sim.lb_char_temp = g_lb_temp_constant;
    dFLOAT b_temp = abv - 0.707;
    if (b_temp < 0.0) b_temp = 0.0; 
    sim.c_temp = b_temp / (1.0 + b_temp);
  } else {
    dFLOAT abv = g_cold_fluid_Prandtl_number;
    dFLOAT abn = 3.85 * pow(abv, one_th) - 1.3;
    sim.c_temp = 0.0;
    sim.B_new_twf = abn * abn + 1.3; 
    sim.lb_char_temp = sim.char_temp;
  }

  if (sim.thermal_feedback_is_on)
    g_Qpatch_under_relaxation *= g_adv_fraction * sqrt(sim.char_temp / sim.mean_temp * sim.lattice_gas_const * sim.lattice_gamma);

#if BUILD_D19_LATTICE
  // update u_scatter based on simulation Mach number
  //dFLOAT Mach_sim = header.char_vel / sqrt(g_lb_temp_constant);
  //dFLOAT Mach_scale = Mach_sim / Mach_limit;
  //if (Mach_scale > 1.0) Mach_scale = 1.0;
  //u_scatter = scatter_omega - u_scatter_alpha * 0.38 * Mach_scale;

  if (sim.is_heat_transfer) {
    sdFLOAT nu_aix = sim.char_vel * sim.char_length / sim.reynolds_number;
    sdFLOAT nu_aix1 = nu_aix;

    if (sim.is_temp_dependent_liquid) {
      get_temp_dep_nu_pr(sim.char_temp, &nu_aix, NULL);
      
      sdFLOAT temp_300k = 300.0F * sim.one_k_lat;
      get_temp_dep_nu_pr(temp_300k, &nu_aix1, NULL);
    } else if (sim.is_temp_dependent_AdBlue) {
      sdFLOAT temp_298k = 298.15F * sim.one_k_lat; 
      get_temp_dep_ab(temp_298k, &nu_aix, NULL);
      
      sdFLOAT temp_300k = 300.0F * sim.one_k_lat;
      get_temp_dep_ab(temp_300k, &nu_aix1, NULL);
    }

    // kinematic viscosity in mks units = sim.nu_0 * 10^-6 m^2/s
    sim.nu_0 = nu_aix;
    sim.nu_300 = nu_aix1; 

    if (sim.use_lb_energy_solver && g_use_surfel_filter_collision == 0)
      g_eq_3rd_order_d19 = 1.0;
  }

#endif

  // set the user's notion of time as sim.time
  g_timescale.m_n_base_timesteps_per_timestep = header.n_user_base_steps;
  // simulation time-span set to max span, with the actual start and end timesteps sent as separate record later once
  // the time-coupling phases are read
  g_timescale.m_start_time = 0;
  g_timescale.m_end_time = BASETIME_MAX;
  
  sim.init_solver_mask = header.initial_solver_mask;  

  if (sim.is_radiation_model) {
    g_radiation_patches.init(total_rps);
  }

  sim.use_implicit_shell_solver = header.use_implicit_shell_solver;
  sim.use_implicit_solid_solver = header.use_implicit_solid_solver;
}

VOID sDGF_READER::read_time_coupling_phases() {
  LGI_TIME_COUPLING_PHASES_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  //Initializes the data structure
  sim.time_coupling_info.init(header.n_time_coupling_phases, header.coarsest_coupled_scale);

  // Fills timing coupling phases
  if (g_pfc_time_coupling_num_phases>0) {
    //COND_TODO: Hacked version, already filled the phases. Remove once not used
    msg_warn("Replacing time-coupling phases with g_pfc values");
    //Clears the lgi stream reading the phases sent, but does not load them
    LGI_TIME_COUPLING_PHASE_REC record;
    ccDOTIMES(i, header.n_time_coupling_phases) {
      lgi_read(g_lgi_stream, record);
    }
  } else {
    LGI_TIME_COUPLING_PHASE_REC record;
    ccDOTIMES(i, header.n_time_coupling_phases) {
      lgi_read(g_lgi_stream, record);
      sTIME_COUPLING_PHASE phase;
      phase.start                   = record.start;
      phase.time_coupling           = (eTIME_COUPLING_SCHEME::Enum)(record.time_coupling);
      phase.frozen_solver           = (eCOUPLED_SOLVER::Enum)(record.frozen_solver);
      phase.therm_time_ratio        = record.therm_time_ratio;
      phase.radiation_update_ratio  = record.radiation_update_ratio;
      phase.flow_duration           = record.flow_duration;
      phase.flow_avg_interval       = record.flow_avg_interval;
      phase.conduction_duration     = record.conduction_duration;
      phase.conduction_avg_interval = record.conduction_avg_interval;
      sim.time_coupling_info.m_phases[i] = phase;
    }
  }
  //Once all phases are loaded, can cash the phase flags for the initial phase
  sim.time_coupling_info.set_phase_flags();
  //It can also load each realm specific phase time info to be able to convert between global and realm timesteps
  sREALMS_PHASE_TIME_INFO_LOADER realms_phase_time_info_loader(sim.realm_phase_time_info, 
                                                               g_timescale.m_n_base_timesteps_per_timestep, 
                                                               g_timescale.m_n_lb_base_steps, 
                                                               g_timescale.m_n_conduction_pde_base_steps);
  ccDOTIMES(i, header.n_time_coupling_phases) {
    realms_phase_time_info_loader.load_phase(sim.time_coupling_info.m_phases[i]);
  }
  
  //We can account now for potential different rate phases to determine the timestep increments
  //(note that set_therm_time_ratio internally sets base_time, time, and wsurfel_comm_cntr increments)
  sTIME_COUPLING_PHASE &first_phase = sim.time_coupling_info.m_phases[0];
  g_timescale.set_therm_time_ratio(first_phase.therm_time_ratio, first_phase.radiation_update_ratio);
  
  // Process time (needs to be loaded after setting m_time_inc so it is properly aligned as it is loaded)
  g_timescale.init_time();

  //No need to call maybe_update_time_coupling_phase() since active and next phase indexes set to 0 and 1, respectively,
  //when initializing time_coupling_info above.
  sim.time_coupling_info.update_phase_averaging_start_end();
}

#if BUILD_5G_LATTICE
//----------------------------------------------------------------------------
// read_multi_component
//----------------------------------------------------------------------------
VOID sDGF_READER::read_multi_component()
{
  

  LGI_CMPS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  sim.num_components = header.n_components;

  if (sim.num_components < 1 || sim.num_components > MAXIMUM_NUM_COMPONENTS)
    msg_error("Invalid num_components (%d).\n", sim.num_components);

  if (sim.num_components > 1)
    g_is_multi_component = TRUE;
  else
    g_is_multi_component = FALSE;

  if ( !g_is_multi_component &&  !(g_mp_mss_z % 2) )
    msg_error("Mass sink and source model in single component case \n");

  g_mc_types = xnew sFLUID_PROPERTIES[sim.num_components];
  for (asINT32 c = 0; c < sim.num_components; ++c) {
    LGI_FCMP fcmp;
    lgi_read(g_lgi_stream, &fcmp, sizeof(LGI_FCMP));

    EOS_TYPE type = EOS_IDEAL_GAS;
    if (fcmp.eos == SRI_IDEAL_GAS_EOS)
      type = EOS_MC;
    else if (fcmp.eos == SRI_PENG_ROBINSON_EOS)
      type = EOS_PENG_ROBINSON;
    else
      msg_internal_error("Unrecognized eos type %d", fcmp.eos);

    if (sim.smart_seed_contact_angle && type != EOS_MC)
       msg_error("Smart seeding with option of seed_contact_angle only supports ideal eos type.");

    g_mc_types[c].equation_of_state = NULL;
    g_mc_types[c].initialize (c, type, fcmp.viscosity, fcmp.molecular_weight);
  }

  if (sim.is_scalar_model) { //always LB_UDS
    if (g_is_multi_component) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
        if (g_mp_uds_carrier[nth_uds]>=0 && g_mp_uds_carrier[nth_uds]<MAXIMUM_NUM_UDS_CARRIERS)
	  sim.uds_carriers[nth_uds] = (UDS_CARRIER)g_mp_uds_carrier[nth_uds];
        else
	  msg_error("Invalid uds(%d) carrier index (%d).\n", nth_uds, g_mp_uds_carrier[nth_uds]);
      }
    } else {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
	if (g_mp_uds_carrier[nth_uds]>=0 && g_mp_uds_carrier[nth_uds] < sim.num_components)
          sim.uds_carriers[nth_uds] = (UDS_CARRIER)g_mp_uds_carrier[nth_uds];
        else                    
          msg_error("Invalid uds(%d) carrier index (%d) for a single component case.\n", nth_uds, g_mp_uds_carrier[nth_uds]);
      }
    }
  }  
}

#endif

//----------------------------------------------------------------------------
// read_scalar_material
//----------------------------------------------------------------------------
VOID sDGF_READER::read_scalar_material()
{
  LGI_SCLS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  asINT32 n_scalars = header.n_scalars;

  if (n_scalars < 1 || n_scalars > MAX_N_USER_DEFINED_SCALARS)
    msg_error("Invalid num_scalars (%d).\n", n_scalars);

  g_scalar_materials = xnew sSCALAR_MATERIAL[n_scalars];

  for (asINT32 i = 0; i < n_scalars; i++) {
    LGI_SCLR sclr;
    lgi_read(g_lgi_stream, &sclr, sizeof(LGI_SCLR));

    g_scalar_materials[i].diffusivity_mol = sclr.diffusion_coefficient;
    g_scalar_materials[i].turb_schmidt_number = sclr.scalar_turb_schmidt_number;
    g_scalar_materials[i].uds_max_value = sclr.maximum_value;
    g_scalar_materials[i].uds_min_value = sclr.minimum_value;
    g_scalar_materials[i].allow_negative_values = sclr.allow_negative_values;  //if uds_min_value id determined, this var may not be mecessary
    strncpy(g_scalar_materials[i].name, sclr.scalar_name, LGI_SCALAR_NAME_LEN);
  }
}

//----------------------------------------------------------------------------
// read_cp_to_sp_info
//----------------------------------------------------------------------------
VOID sDGF_READER:: read_cp_to_sp_info()
{
  LGI_CP_TO_SP_INFO_REC record;
  lgi_read_next_head(g_lgi_stream, record);

  sim.ignore_vel_n_timesteps  = record.ignore_vel_n_timesteps < 0 ? BASETIME_LAST : record.ignore_vel_n_timesteps;
  sim.ignore_temp_n_timesteps = record.ignore_temp_n_timesteps < 0 ? BASETIME_LAST : record.ignore_temp_n_timesteps;
  sim.ignore_uds_n_timesteps  = record.ignore_uds_n_timesteps < 0 ? BASETIME_LAST : record.ignore_uds_n_timesteps;
  sim.timers_on_timestep =   record.timers_on_timestep;
  sim.timers_off_timestep =   record.timers_off_timestep;
  sim.stefan_boltzmann_const = record.stefan_boltzmann_const;

  LOG_MSG("RAD").printf("sfc %.18e", sim.stefan_boltzmann_const);

  if (record.max_temp <= 0)
    sim.user_max_temp = sdFLOAT_MAX;
  else
    sim.user_max_temp = record.max_temp;

  if (record.min_temp <= 0)
    sim.user_min_temp = 0;
  else
    sim.user_min_temp = record.min_temp;

  if (record.max_vel < 0) {
    if (sim.is_turb_model && !sim.thermal_feedback_is_on) {
      sim.default_max_vel = DEFAULT_MAX_VEL_AERO;
    }
    sim.set_user_max_vel(sim.default_max_vel, TRUE);
  } else {
    sim.set_user_max_vel(record.max_vel, FALSE);
    g_Mach_cap = -1.0;       /* Use user specified max_vel */
  }

  if (record.max_vel < 0) {
    if (sim.is_turb_model && !sim.thermal_feedback_is_on) {
      sim.default_max_vel = DEFAULT_MAX_VEL_AERO;
    }
    sim.set_user_max_vel(sim.default_max_vel, TRUE);
  } else {
    sim.set_user_max_vel(record.max_vel, FALSE);
    g_Mach_cap = -1.0;       /* Use user specified max_vel */
  }

  if (sim.thermal_feedback_is_on) {
    dFLOAT one_and_half_temp_mean = 1.5 * sim.mean_temp;
    dFLOAT half_temp_char = 0.5 * sim.char_temp;
    if (sim.user_max_temp < 0.5 * sdFLOAT_MAX) {
      if (sim.user_max_temp > one_and_half_temp_mean) {
        sim.sim_max_temp = one_and_half_temp_mean;
      } else {
        sim.sim_max_temp = sim.user_max_temp;
      }
    } else { 
      sim.sim_max_temp = one_and_half_temp_mean;
    }
    
    if (sim.user_min_temp > 0) {
      if (sim.user_min_temp < half_temp_char) {
        sim.sim_min_temp = half_temp_char;
      } else {
        sim.sim_min_temp = sim.user_min_temp;
      }
    } else {
      sim.sim_min_temp = half_temp_char;
    }
  } else {
    sim.sim_max_temp = sim.user_max_temp;
    sim.sim_min_temp = sim.user_min_temp;
  }

#if BUILD_5G_LATTICE  
  sim.sim_max_uds = g_user_max_uds;  //only for 5G LB_UDS
  sim.sim_min_uds = g_user_min_uds;  //only for 5G LB_UDS
#endif  

  sim.enable_tangential_shell_conduction = record.enable_tangential_shell_conduction;
  sim.constant_implicit_shell_solver_matrix = record.constant_implicit_shell_solver_matrix;

}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
VOID sDGF_READER::read_ice_accretion_parameters() {
  LGI_ICE_ACCRETION_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  sPARTICLE_SIM_INFO& parameters = g_particle_sim_info;
  lgi_read(g_lgi_stream, parameters.ice_accretion_parameters);
  ccDOTIMES(i, header.num_surface_ids) {
    asINT32 surface_id;
    lgi_read(g_lgi_stream, surface_id);
    parameters.accretion_surface_ids.push_back(surface_id);
  }
  sim.is_accretion_simulation = true;
}

VOID sDGF_READER::read_particle_globals() {
  LGI_PARTICLE_GLOB_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);


  LGI_PARTICLE_GLOB_REC record;
  lgi_read(g_lgi_stream, record);

  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->gravity_csys_id = 0; //transform to the default csys //record.gravity_csys_id;
  parameters->gravity[0] = record.gravity[0];
  parameters->gravity[1] = record.gravity[1];
  parameters->gravity[2] = record.gravity[2];
  if (record.gravity_csys_id > -1 && record.gravity_csys_id < sim.n_csys)
    xform_vector(parameters->gravity, &sim.csys_table[record.gravity_csys_id]);

  parameters->feedback_enabled = record.coupled_momentum_solver != 0;
  sim.is_particle_feedback = parameters->feedback_enabled;

  //disable the film solver until a fluid type particle material is found
  sim.is_film_solver = false;

  //retreive some hidden model parameters
  parameters->reentrainment_diameter = record.reentrainment_diameter;
  parameters->reentrainment_diameter_range = record.reentrainment_diameter_range;
  parameters->surface_tension_factor_at_splash = record.surface_tension_factor_at_splash;
  parameters->shear_stress_factor_for_film = record.shear_stress_factor_for_film;
  parameters->max_radius_of_film_stencil = record.max_radius_of_film_stencil;

  parameters->film_surface_tension_model_enabled = record.film_surface_tension_model_enabled;
  parameters->advancing_contact_angle = record.advancing_contact_angle;
  parameters->receding_contact_angle = record.receding_contact_angle;

  parameters->film_surface_tension_model_enabled = g_surface_tension_model_enabled;
  parameters->advancing_contact_angle = g_surface_tension_advancing_angle;
  parameters->receding_contact_angle = g_surface_tension_receding_angle;

  memcpy(&parameters->dispersion_box, &record.dispersion_box, sizeof(parameters->dispersion_box));
//  printf("disp  box csys  %d\n", record.dispersion_box.csys_index);

  char* name = xnew char [record.dispersion_box.name_length+1];
  lgi_read(g_lgi_stream,name,record.dispersion_box.name_length);
  name[record.dispersion_box.name_length]='\0';
  parameters->dispersion_box_name=name;

  //sort the corners by min and max
  ccDOTIMES(dim,3) {
    if ( parameters->dispersion_box.corners[0][dim] > parameters->dispersion_box.corners[1][dim] ) {
      dFLOAT temp = parameters->dispersion_box.corners[0][dim];
      parameters->dispersion_box.corners[0][dim] = parameters->dispersion_box.corners[1][dim];
      parameters->dispersion_box.corners[1][dim] = temp;
    }
  }


}

VOID sDGF_READER::read_particle_materials() {
  LGI_PARTICLE_MATERIAL_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->ice_accretion_parameters.accretion_material_index = -1;
  parameters->num_materials = 0;
  ccDOTIMES(material_index, header.n_materials) {
    LGI_PARTICLE_MATERIAL_REC record;

    lgi_read(g_lgi_stream, record);
    std::string name;
    lgi_read(g_lgi_stream, name, record.name_length);

    PARTICLE_MATERIAL new_material = xnew sPARTICLE_MATERIAL(name);

    if (record.density_distribution == LGI::DISTRIBUTION_GAMMA  && record.density_range == 0) {
      msg_error("Particle material trying to use gamma density distribution with zero stddev.\n"); //powercase should warn about this then set the distribution to none
    }

    new_material->m_name[record.name_length] = '\0';
    new_material->m_density_mean = record.mean_density;
    new_material->m_density_dist_param2 = record.density_range;
    new_material->m_density_distribution = lgi_to_pdfs_distribution(record.density_distribution);
    new_material->m_dynamic_viscosity = record.dynamic_viscosity;
    new_material->m_surface_tension = record.surface_tension;
    new_material->m_material_type = (sMATERIAL_TYPES) record.material_type;
    if(new_material->m_density_mean == 0.0 && new_material->m_material_type == SOLID)
      new_material->m_material_type = MASSLESS_TRACER;
    new_material->m_breakup_allowed = record.breakup_allowed;
    if(new_material->m_material_type == MASSLESS_TRACER)
      new_material->m_breakup_allowed = FALSE;

    new_material->m_id = parameters->num_materials;
    parameters->particle_materials.push_back(new_material);
    parameters->num_materials++;

  }
}

VOID sDGF_READER::read_anisotropic_part_axis() {
  LGI_PART_AXIS_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  CONDUCTION_SIM_INFO parameters = &g_conduction_sim_info;
  parameters->num_axes = 0;
  ccDOTIMES(axes_index, header.n_axes) {
    LGI_PART_AXIS part_axis;

    lgi_read(g_lgi_stream, part_axis);
    CONDUCTION_PART_AXIS new_part_axis = xnew sCONDUCTION_PART_AXIS;
    new_part_axis->part_index = part_axis.part_index;
    ccDOTIMES(i,3) {
      new_part_axis->axis_dir[i] = part_axis.axis_dir[i];
    }

    parameters->conduction_part_axes.push_back(new_part_axis);
    parameters->num_axes++;
  }
}

VOID sDGF_READER::read_particle_emitter_configurations() {

  LGI_PARTICLE_EMITTER_CONFIGURATION_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->num_emitter_configurations = 0;

  ccDOTIMES(emitter_config_index, header.n_configurations) {
    //sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER emitter_config_record(); "Most vexing parse!"
    sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER emitter_config_record;
    //Read all the fixed size data first then read the varying number of strings
    //once the emitter configuration type is known.
    emitter_config_record.read();

    //Read in the strings common to all config types.
    std::string emitter_config_name;
    std::string emission_rate_variable_name;
    emitter_config_record.read_common_strings(emitter_config_name, emission_rate_variable_name);

    //Read in the variable name strings that are specific to the type of emitter.
    std::string wind_velocity_x_variable_name;
    std::string wind_velocity_y_variable_name;
    std::string wind_velocity_z_variable_name;


    //Read in the variable name strings that are specific to the type of emitter.
    std::string spatial_emission_dist_parameter_variable_name; //The following two are only used when reading tire emitter records.
    std::string spatial_emission_dist_ratio_variable_name;

    switch(emitter_config_record.configuration_type) {
    case NOZZLE:
      emitter_config_record.read_nozzle_strings();
      break;
    case RAIN:
      emitter_config_record.read_rain_strings(
                                              wind_velocity_x_variable_name,
                                              wind_velocity_y_variable_name,
                                              wind_velocity_z_variable_name);
      break;
    case TIRE:
      emitter_config_record.read_tire_strings(spatial_emission_dist_parameter_variable_name,
                                              spatial_emission_dist_ratio_variable_name);

      break;
    case UNKNOWN:
      msg_error("Unknown emitter configuration type forund in LGI stream.");
    default:
      msg_error("Unknown emitter configuration type forund in LGI stream.");
    }

    //Now read the nozzle data (which is seperated from the above because tire emitters
    //may have more than one set of nozzle-like parameters).

    std::vector<sLGI_NOZZLE_CONFIGURATION_REC_READER> nozzle_records(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> mean_diameter_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> diameter_range_variable_names(emitter_config_record.num_nozzle_configs);

    //Strings for full cone nozzle config:
    std::vector<std::string> mean_velocity_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> velocity_range_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> cone_half_angle_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> outer_half_angle_limit_variable_names(emitter_config_record.num_nozzle_configs);  //added to support CDI/513 changes

    //Strings for hollow cone nozzle config:
    //std::vector<std::string> mean_velocity_variable_names(emitter_config_record.num_nozzle_configs);
    //std::vector<std::string> velocity_range_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> mean_angle_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> angle_stddev_variable_names(emitter_config_record.num_nozzle_configs);
    //std::vector<std::string> outer_half_angle_limit_variable_names(emitter_config_record.num_nozzle_configs); //added to support CDI/513 changes
    std::vector<std::string> inner_half_angle_limit_variable_names(emitter_config_record.num_nozzle_configs);

    //Strings for elliptical cone nozzle config:
    //std::vector<std::string> mean_velocity_variable_names(emitter_config_record.num_nozzle_configs);
    //std::vector<std::string> velocity_range_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> major_cone_half_angle_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> major_cone_half_angle_stddev_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> minor_cone_half_angle_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> minor_cone_half_angle_stddev_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> major_half_angle_limit_variable_names(emitter_config_record.num_nozzle_configs);  //added to support CDI/513 changes 
    std::vector<std::string> minor_half_angle_limit_variable_names(emitter_config_record.num_nozzle_configs);

    //Strings for rain emitters configs
    //No additional strings are needed.

    //Strings for tire emiter configs:
    //std::vector<std::string> mean_velocity_variable_names(emitter_config_record.num_nozzle_configs);
    //std::vector<std::string> velocity_range_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> tire_arc_position_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> emission_offset_angle_variable_names(emitter_config_record.num_nozzle_configs);
    //std::vector<std::string> cone_half_angle_variable_names(emitter_config_record.num_nozzle_configs);
    std::vector<std::string> stretch_factor_variable_names(emitter_config_record.num_nozzle_configs);

    ccDOTIMES(nozzle_config_index, emitter_config_record.num_nozzle_configs)  {

      //Read in the fixed size nozzle data.
      nozzle_records[nozzle_config_index].read();

      //Read in the strings common to all nozzle types.
      nozzle_records[nozzle_config_index].read_common_strings(mean_diameter_variable_names[nozzle_config_index],
                                                              diameter_range_variable_names[nozzle_config_index]);

      //Read in the variable name strings for the given emitter configuraton type.


      switch(nozzle_records[nozzle_config_index].nozzle_type) {
      case FULL_CONE_NOZZLE:
        nozzle_records[nozzle_config_index].read_full_cone_strings(mean_velocity_variable_names[nozzle_config_index],
                                                                   velocity_range_variable_names[nozzle_config_index],
                                                                   cone_half_angle_variable_names[nozzle_config_index],
                                                                   outer_half_angle_limit_variable_names[nozzle_config_index]);
        break;
      case HOLLOW_CONE_NOZZLE:
        nozzle_records[nozzle_config_index].read_hollow_cone_strings(mean_velocity_variable_names[nozzle_config_index],
                                                                     velocity_range_variable_names[nozzle_config_index],
                                                                     mean_angle_variable_names[nozzle_config_index],
                                                                     angle_stddev_variable_names[nozzle_config_index],
                                                                     outer_half_angle_limit_variable_names[nozzle_config_index],
                                                                     inner_half_angle_limit_variable_names[nozzle_config_index]);
        break;
      case ELLIPTICAL_CONE_NOZZLE:
        nozzle_records[nozzle_config_index].read_elliptical_cone_strings(mean_velocity_variable_names[nozzle_config_index],
                                                                         velocity_range_variable_names[nozzle_config_index],
                                                                         major_cone_half_angle_variable_names[nozzle_config_index],
                                                                         major_cone_half_angle_stddev_variable_names[nozzle_config_index],
                                                                         minor_cone_half_angle_variable_names[nozzle_config_index],
                                                                         minor_cone_half_angle_stddev_variable_names[nozzle_config_index],
                                                                         major_half_angle_limit_variable_names[nozzle_config_index],
                                                                         minor_half_angle_limit_variable_names[nozzle_config_index]);
        break;
      case RAIN_DATA:
        nozzle_records[nozzle_config_index].read_rain_strings();
        break;
      case TIRE_NOZZLE:
        nozzle_records[nozzle_config_index].read_tire_strings(mean_velocity_variable_names[nozzle_config_index],
                                                              velocity_range_variable_names[nozzle_config_index],
                                                              tire_arc_position_variable_names[nozzle_config_index],
                                                              emission_offset_angle_variable_names[nozzle_config_index],
                                                              cone_half_angle_variable_names[nozzle_config_index],
                                                              stretch_factor_variable_names[nozzle_config_index]);
        break;
      case UNDEFINED_NOZZLE:
      default:
        msg_error("Unknown nozzle parameters found in LGI stream.");
      }

    }

    //Now that everything has been read from the LGI stream for this emitter configuration,
    //do some consistency checks and then construct an object for the emitter configuration:

    //Ensure that only tire emitters have more than one nozzle parameter set
    if(emitter_config_record.configuration_type != TIRE)
      if(emitter_config_record.num_nozzle_configs != 1)
        msg_error("Too many nozzle parameter sets found for a nozzle emitter configuration.");

    //Ensure that there is at least one set of nozzle parameters
    if(emitter_config_record.num_nozzle_configs <=0 )
      msg_error("Too few nozzle parameter sets found for a nozzle emitter configuration.");

    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = NULL;
    PARTICLE_MATERIAL material = g_particle_sim_info.particle_materials[emitter_config_record.material_index];
    switch(emitter_config_record.configuration_type) {
    case NOZZLE:
      switch(nozzle_records[0].nozzle_type) {
      case FULL_CONE_NOZZLE:

        emitter_configuration = xnew sFULL_CONE_NOZZLE_CONFIGURATION(emitter_config_record,
                                                                     nozzle_records[0],
                                                                     emitter_config_name,
                                                                     material,
                                                                     emission_rate_variable_name,
                                                                     mean_diameter_variable_names[0],
                                                                     diameter_range_variable_names[0],
                                                                     mean_velocity_variable_names[0],
                                                                     velocity_range_variable_names[0],
                                                                     cone_half_angle_variable_names[0],
                                                                     outer_half_angle_limit_variable_names[0]);
        break;
      case HOLLOW_CONE_NOZZLE:

        emitter_configuration = xnew sHOLLOW_CONE_NOZZLE_CONFIGURATION(emitter_config_record,
                                                                       nozzle_records[0],
                                                                       emitter_config_name,
                                                                       material,
                                                                       emission_rate_variable_name,
                                                                       mean_diameter_variable_names[0],
                                                                       diameter_range_variable_names[0],
                                                                       mean_velocity_variable_names[0],
                                                                       velocity_range_variable_names[0],
                                                                       mean_angle_variable_names[0],
                                                                       angle_stddev_variable_names[0],
                                                                       outer_half_angle_limit_variable_names[0],
                                                                       inner_half_angle_limit_variable_names[0]);

        break;
      case ELLIPTICAL_CONE_NOZZLE:
        emitter_configuration = xnew sELLIPTICAL_CONE_NOZZLE_CONFIGURATION(emitter_config_record,
                                                                           nozzle_records[0],
                                                                           emitter_config_name,
                                                                           material,
                                                                           emission_rate_variable_name,
                                                                           mean_diameter_variable_names[0],
                                                                           diameter_range_variable_names[0],
                                                                           mean_velocity_variable_names[0],
                                                                           velocity_range_variable_names[0],
                                                                           major_cone_half_angle_variable_names[0],
                                                                           major_cone_half_angle_stddev_variable_names[0],
                                                                           minor_cone_half_angle_variable_names[0],
                                                                           minor_cone_half_angle_stddev_variable_names[0],
                                                                           major_half_angle_limit_variable_names[0],
                                                                           minor_half_angle_limit_variable_names[0]);
        break;
      default:
        msg_error("Wrong type of nozzle parameters found for NOZZLE emitter configuration.");
      }
      break;
    case RAIN:  emitter_configuration = xnew sRAIN_EMITTER_CONFIGURATION(emitter_config_record,
                                                               nozzle_records[0],
                                                               emitter_config_name,
                                                               material,
                                                               emission_rate_variable_name,
                                                               mean_diameter_variable_names[0],
                                                               diameter_range_variable_names[0],
                                                               wind_velocity_x_variable_name,
                                                               wind_velocity_y_variable_name,
                                                               wind_velocity_z_variable_name);
      break;
    case TIRE:
      emitter_configuration = xnew sTIRE_EMITTER_CONFIGURATION(emitter_config_record,
                                                               nozzle_records,
                                                               emitter_config_name,
                                                               material,
                                                               emission_rate_variable_name,
                                                               spatial_emission_dist_parameter_variable_name,
                                                               spatial_emission_dist_ratio_variable_name,
                                                               mean_diameter_variable_names,
                                                               diameter_range_variable_names,
                                                               mean_velocity_variable_names,
                                                               velocity_range_variable_names,
                                                               tire_arc_position_variable_names,
                                                               emission_offset_angle_variable_names,
                                                               cone_half_angle_variable_names,
                                                               stretch_factor_variable_names );
      break;
    default:
      msg_error("Unknown emitter configuration type forund in LGI stream.");
    }

    parameters->emitter_configurations.push_back(emitter_configuration);
  }
}

VOID sDGF_READER::read_particle_emitters() {

  LGI_PARTICLE_EMITTER_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->num_cdi_emitters = 0;

  //Create surface emitters.
  ccDOTIMES(surface_emitter_index, header.num_surface_emitters) {
    sLGI_SURFACE_EMITTER_REC_READER record;
    std::string name;
    std::vector<int> face_list;
    std::vector<std::string> sprayer_direction_variable_names;
    record.read(name, face_list, sprayer_direction_variable_names);
    
    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = g_particle_sim_info.emitter_configurations[record.emitter_configuration_index];
    BOOLEAN full_cone_not_hollow = emitter_configuration->emitter_configuration_type() == sPARTICLE_EMITTER_CONFIGURATION_BASE::FULL_CONE;
   
    ccDOTIMES(face_index, face_list.size()) {
    
      sEMISSION_SURFACE emission_surface( face_list[face_index] );

      EMISSION_SURFACE emission_surface_ref = parameters->emission_surfaces.add_if_unique(emission_surface);

      PARTICLE_EMITTER_BASE surface_emitter = NULL;
      if(full_cone_not_hollow) {
        if(record.fixed_release_points) {
          surface_emitter = xnew sFULL_CONE_FIXED_POINT_SURFACE_EMITTER(record,
                                                                        parameters->num_cdi_emitters,
                                                                        name,
                                                                        emission_surface_ref,
                                                                        emitter_configuration,
                                                                        sprayer_direction_variable_names);
        } else {
          surface_emitter = xnew sFULL_CONE_RANDOM_POINT_SURFACE_EMITTER(record,
                                                                         parameters->num_cdi_emitters,
                                                                         name,
                                                                         emission_surface_ref,
                                                                         emitter_configuration,
                                                                         sprayer_direction_variable_names);
        }
      } else {
        if(record.fixed_release_points) {
          surface_emitter = xnew sHOLLOW_CONE_FIXED_POINT_SURFACE_EMITTER(record,
                                                                          parameters->num_cdi_emitters,
                                                                          name,
                                                                          emission_surface_ref,
                                                                          emitter_configuration,
                                                                          sprayer_direction_variable_names);
        } else {
          surface_emitter = xnew sHOLLOW_CONE_RANDOM_POINT_SURFACE_EMITTER(record,
                                                                           parameters->num_cdi_emitters,
                                                                           name,
                                                                           emission_surface_ref,
                                                                           emitter_configuration,
                                                                           sprayer_direction_variable_names);
        }
      }
      parameters->emitters.push_back(surface_emitter);
    }
    
    parameters->num_cdi_emitters++;
  }
  
  //Create volume emitters.
  ccDOTIMES(volume_emitter_index, header.num_volume_emitters) {
    sLGI_VOLUME_EMITTER_REC_READER record;
    std::string name;
    std::vector<std::string> sprayer_direction_variable_names;
    std::vector<sLGI_CYLINDER_REC> cylinders;
    std::vector<std::string> cylinder_names;
    std::vector<sLGI_BOX_REC> boxes;
    std::vector<std::string> box_names;
    
    record.read(name, sprayer_direction_variable_names, cylinders, cylinder_names, boxes, box_names);
    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = g_particle_sim_info.emitter_configurations[record.emitter_configuration_index];
    BOOLEAN full_cone_not_hollow = emitter_configuration->emitter_configuration_type() == sPARTICLE_EMITTER_CONFIGURATION_BASE::FULL_CONE;
   
    //For PR41693, compute the total volume of all parts of this emitter so the fractional release rate of each part 
    //can be computed.
    sPARTICLE_VAR total_emitter_volume = 0.0;
    ccDOTIMES(cylinder_index, cylinders.size()) {
      sEMISSION_CYLINDER emission_cylinder(cylinders[cylinder_index], cylinder_names[cylinder_index]);
      total_emitter_volume += emission_cylinder.volume();
    }
    ccDOTIMES(box_index, boxes.size()) {
      sEMISSION_BOX emission_box(boxes[box_index], box_names[box_index]);
      total_emitter_volume += emission_box.volume();
    }
    

    ccDOTIMES(cylinder_index, cylinders.size()) {
      
      sEMISSION_CYLINDER emission_cylinder(cylinders[cylinder_index], cylinder_names[cylinder_index]);
      if(record.fixed_release_points) {
        sFIXED_EMISSION_POINTS fep(record.release_spacing, record.spray_direction_csys_id);
	emission_cylinder.set_emission_points(fep);
      }
      
      EMISSION_CYLINDER emission_cylinder_ref =
        parameters->emission_cylinders.add_if_unique(emission_cylinder);
      
      sPARTICLE_VAR emission_fraction = 1.0;
      if((sim.cdi_major_version == 5 && sim.cdi_minor_version > 6) ||
         sim.cdi_major_version > 5) {
        emission_fraction = emission_cylinder.volume() / total_emitter_volume;
      }
      
      PARTICLE_EMITTER_BASE cylinder_emitter = NULL;
      if(full_cone_not_hollow) {
        if(record.fixed_release_points) {
          cylinder_emitter = xnew sFULL_CONE_FIXED_POINT_CYLINDER_EMITTER(record,
                                                                          parameters->num_cdi_emitters,
                                                                          name,
                                                                          sprayer_direction_variable_names,
                                                                          emission_cylinder_ref,
                                                                          emission_fraction,
                                                                          emitter_configuration);
        } else{
          cylinder_emitter = xnew sFULL_CONE_RANDOM_POINT_CYLINDER_EMITTER(record,
                                                                           parameters->num_cdi_emitters,
                                                                           name,
                                                                           sprayer_direction_variable_names,
                                                                           emission_cylinder_ref,
                                                                           emission_fraction,
                                                                           emitter_configuration);
        }
      } else {
        if(record.fixed_release_points) {
          cylinder_emitter = xnew sHOLLOW_CONE_FIXED_POINT_CYLINDER_EMITTER(record,
                                                                            parameters->num_cdi_emitters,
                                                                            name,
                                                                            sprayer_direction_variable_names,
                                                                            emission_cylinder_ref,
                                                                            emission_fraction,
                                                                            emitter_configuration);
        } else{
          cylinder_emitter = xnew sHOLLOW_CONE_RANDOM_POINT_CYLINDER_EMITTER(record,
                                                                             parameters->num_cdi_emitters,
                                                                             name,
                                                                             sprayer_direction_variable_names,
                                                                             emission_cylinder_ref,
                                                                             emission_fraction,
                                                                             emitter_configuration);
        }
        
      }
      parameters->emitters.push_back(cylinder_emitter);
    }
    
    ccDOTIMES(box_index, boxes.size()) {

      sEMISSION_BOX emission_box(boxes[box_index], box_names[box_index]);

      if(record.fixed_release_points) {
        sFIXED_EMISSION_POINTS fep(record.release_spacing, record.spray_direction_csys_id);
        emission_box.set_emission_points(fep);
      }

      EMISSION_BOX emission_box_ref =
        parameters->emission_boxes.add_if_unique(emission_box);

      sPARTICLE_VAR emission_fraction = 1.0;
      if((sim.cdi_major_version == 5 && sim.cdi_minor_version > 6) ||
         sim.cdi_major_version > 5) {
        emission_fraction = emission_box.volume() / total_emitter_volume;
      }   

      PARTICLE_EMITTER_BASE box_emitter = NULL;
      if(full_cone_not_hollow) {
        if(record.fixed_release_points) {
          box_emitter = xnew sFULL_CONE_FIXED_POINT_BOX_EMITTER(record,
                                                                parameters->num_cdi_emitters,
                                                                name,
                                                                sprayer_direction_variable_names,
                                                                emission_box_ref,
                                                                emission_fraction,
                                                                emitter_configuration);
        } else {
          box_emitter = xnew sFULL_CONE_RANDOM_POINT_BOX_EMITTER(record,
                                                                 parameters->num_cdi_emitters,
                                                                 name,
                                                                 sprayer_direction_variable_names,
                                                                 emission_box_ref,
                                                                 emission_fraction,
                                                                 emitter_configuration);
        } 
      } else  {
        if(record.fixed_release_points) {
          box_emitter = xnew sHOLLOW_CONE_FIXED_POINT_BOX_EMITTER(record,
                                                                  parameters->num_cdi_emitters,
                                                                  name,
                                                                  sprayer_direction_variable_names,
                                                                  emission_box_ref,
                                                                  emission_fraction,
                                                                  emitter_configuration);
        } else {
          box_emitter = xnew sHOLLOW_CONE_RANDOM_POINT_BOX_EMITTER(record,
                                                                   parameters->num_cdi_emitters,
                                                                   name,
                                                                   sprayer_direction_variable_names,
                                                                   emission_box_ref,
                                                                   emission_fraction,
                                                                   emitter_configuration);
        } 
      }
      parameters->emitters.push_back(box_emitter);
    }
    parameters->num_cdi_emitters++;
  }
  
  //Create point emitters.
  ccDOTIMES(point_emitter_index, header.num_point_emitters) {
    sLGI_POINT_EMITTER_REC_READER record;
    std::string name;
    std::vector<std::string> sprayer_direction_variable_names;
    std::vector<dFLOAT> points;
    record.read(name, sprayer_direction_variable_names, points);
  
    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = g_particle_sim_info.emitter_configurations[record.emitter_configuration_index];
    BOOLEAN full_cone_not_hollow = emitter_configuration->emitter_configuration_type() == sPARTICLE_EMITTER_CONFIGURATION_BASE::FULL_CONE;
  
    sEMISSION_POINTS emission_points(name + "unnamed_point_set", points);
    EMISSION_POINTS emission_points_ref = parameters->emission_points.add_if_unique(emission_points);
    PARTICLE_EMITTER_BASE point_emitter = NULL;
    if(full_cone_not_hollow) {
      point_emitter = xnew sFULL_CONE_POINT_EMITTER(record,
                                                    parameters->num_cdi_emitters,
                                                    name,
                                                    sprayer_direction_variable_names,
                                                    emission_points_ref,
                                                    emitter_configuration);
    } else {
      point_emitter = xnew sHOLLOW_CONE_POINT_EMITTER(record,
                                                      parameters->num_cdi_emitters,
                                                      name,
                                                      sprayer_direction_variable_names,
                                                      emission_points_ref,
                                                      emitter_configuration);
    }
    parameters->emitters.push_back(point_emitter);
    parameters->num_cdi_emitters++;
  }
  
  //Create tire emitters
  ccDOTIMES(tire_emitter_index, header.num_tire_emitters) {
    sLGI_TIRE_EMITTER_REC_READER record;
    std::string name;
    std::vector<std::string> cylinder_names;
    std::vector<sLGI_CYLINDER_REC> cylinders;
    
    record.read(name, cylinder_names, cylinders);
    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = g_particle_sim_info.emitter_configurations[record.emitter_configuration_index];
  
    sPARTICLE_VAR emission_fraction = 1.0;

    ccDOTIMES(cylinder_index, cylinders.size()) {
      
      sEMISSION_CYLINDER emission_cylinder(cylinders[cylinder_index], cylinder_names[cylinder_index]);
      EMISSION_CYLINDER emission_cylinder_ref = parameters->emission_cylinders.add_if_unique(emission_cylinder);
      
      TIRE_EMITTER tire_emitter = xnew sTIRE_EMITTER(record,
                                                     parameters->num_cdi_emitters,
                                                     name,
                                                     emission_cylinder_ref,
                                                     emission_fraction,
                                                     emitter_configuration);
      parameters->emitters.push_back((PARTICLE_EMITTER_BASE)tire_emitter);
    }
    parameters->num_cdi_emitters++;
  }
  
  //Create rain emitters
  ccDOTIMES(rain_emitter_index, header.num_rain_emitters) {
    sLGI_RAIN_EMITTER_REC_READER record;
    std::string name;
    std::vector<sLGI_CYLINDER_REC> cylinders;
    std::vector<std::string> cylinder_names;
    std::vector<sLGI_BOX_REC> boxes;
    std::vector<std::string> box_names;
    
    record.read(name, cylinders, cylinder_names, boxes, box_names);
    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration = g_particle_sim_info.emitter_configurations[record.emitter_configuration_index];
  
    sPARTICLE_VAR emission_fraction = 1.0;

    ccDOTIMES(cylinder_index, record.num_cylinders) {
      
      sEMISSION_CYLINDER emission_cylinder(cylinders[cylinder_index], cylinder_names[cylinder_index]);
      EMISSION_CYLINDER emission_cylinder_ref = parameters->emission_cylinders.add_if_unique(emission_cylinder);
      
      RAIN_CYLINDER_EMITTER rain_emitter = xnew sRAIN_CYLINDER_EMITTER(record,
                                                                       parameters->num_cdi_emitters,
                                                                       name,
                                                                       emission_cylinder_ref,
                                                                       emission_fraction,
                                                                       emitter_configuration);
      parameters->emitters.push_back((PARTICLE_EMITTER_BASE)rain_emitter);
    }
    
    ccDOTIMES(box_index, record.num_boxes) {
      sEMISSION_BOX emission_box(boxes[box_index], box_names[box_index]);
      EMISSION_BOX emission_box_ref = parameters->emission_boxes.add_if_unique(emission_box);

      RAIN_BOX_EMITTER rain_emitter = xnew sRAIN_BOX_EMITTER(record,
                                                             parameters->num_cdi_emitters,
                                                             name,
                                                             emission_box_ref,
                                                             emission_fraction,
                                                             emitter_configuration);
      parameters->emitters.push_back((PARTICLE_EMITTER_BASE)rain_emitter);
    }
    parameters->num_cdi_emitters++;
  }
}


VOID sDGF_READER::read_particle_surface_interaction_parameters() {
  LGI_PARTICLE_SURFACE_MATERIAL_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  std::vector<SURFACE_INTERACTION_PROPERTY> surface_props;
  ccDOTIMES(i,header.n_surface_materials) {
    LGI_PARTICLE_SURFACE_MATERIAL_REC record;
    lgi_read(g_lgi_stream, record);
    char * name = xnew char[record.name_length+1];
    lgi_read(g_lgi_stream, name, record.name_length);
    name[record.name_length] = '\0';
    SURFACE_INTERACTION_PROPERTY new_surface_prop = xnew sSURFACE_INTERACTION_PROPERTY (record.name_length, record, name);
    surface_props.push_back(new_surface_prop);
  }
  //organize the properties so they can be indexed by surface and particle material ids
  asINT32 max_surface_material_id_referenced = -1;
  asINT32 max_particle_material_id_referenced = -1;
  ccDOTIMES(prop_num,surface_props.size()) {
    if(surface_props[prop_num]->m_surface_material_id > max_surface_material_id_referenced)
      max_surface_material_id_referenced = surface_props[prop_num]->m_surface_material_id;
    if(surface_props[prop_num]->m_particle_material_id > max_particle_material_id_referenced)
      max_particle_material_id_referenced = surface_props[prop_num]->m_particle_material_id;
  }
  parameters->surface_interaction_matrix.clear();
  ccDOTIMES(surface_type,max_surface_material_id_referenced+1) {
    std::vector<SURFACE_INTERACTION_PROPERTY> empty_vect (parameters->particle_materials.size(),(SURFACE_INTERACTION_PROPERTY)NULL);
    parameters->surface_interaction_matrix.push_back(empty_vect);
  }
  ccDOTIMES(prop_num,surface_props.size()) {
    asINT32 surface_material_type = surface_props[prop_num]->m_surface_material_id;
    asINT32 particle_material_type = surface_props[prop_num]->m_particle_material_id;
    parameters->surface_interaction_matrix[surface_material_type][particle_material_type] = surface_props[prop_num];
  }
  ccDOTIMES(surface_type,max_surface_material_id_referenced+1) {
    ccDOTIMES(particle_type,max_particle_material_id_referenced+1) {
      if(parameters->surface_interaction_matrix[surface_type][particle_type]  ==  NULL) {
        msg_error("Some combinations of surface material and particle material types are missing.");
      }
    }
  }
}

VOID sDGF_READER::read_particle_screens() {
  LGI_PARTICLE_SCREEN_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->num_screens = 0;

  ccDOTIMES(i, header.n_screens) {
    LGI_PARTICLE_SCREEN_REC record;

    lgi_read(g_lgi_stream, record);

    std::string name;
    lgi_read(g_lgi_stream, name, record.name_length);

    ccDOTIMES(face_index, record.num_faces) {
      asINT32 face_id;
      lgi_read(g_lgi_stream, face_id);

      if(parameters->screens.find(face_id) != parameters->screens.end())
        msg_error("one face appears in two particle screen windows");

      PARTICLE_SCREEN screen = xnew sPARTICLE_SCREEN;

      screen->m_name = name;
      screen->opening_size = record.opening_size;
      screen->pass_thru_fraction = record.pass_thru_fraction;
      screen->surface_material = record.surface_material;
      screen->measured_fraction = record.measured_fraction;

      parameters->screens[face_id] = screen;
    }

    parameters->num_screens++;
  }
}


VOID sDGF_READER::read_virtual_wipers() {

  LGI_VIRTUAL_WIPER_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  parameters->num_virtual_wipers = header.n_wipers;

  ccDOTIMES(wiper_num, header.n_wipers) {
    sVIRTUAL_WIPER* new_wiper = xnew sVIRTUAL_WIPER;
    LGI_VIRTUAL_WIPER_REC record;
    lgi_read(g_lgi_stream, record);
    lgi_read(g_lgi_stream, new_wiper->m_name, sizeof(char) * record.name_length);
    memcpy(&new_wiper->m_parameters, &record, sizeof(LGI_VIRTUAL_WIPER_REC));
    parameters->wipers.push_back(new_wiper);

    new_wiper->wiped_surfaces.resize(record.num_wiped_surfaces);
    lgi_read(g_lgi_stream, new_wiper->wiped_surfaces.data(), sizeof(asINT32) * record.num_wiped_surfaces);

    new_wiper->wiper_parts.resize(record.num_wiper_blade_parts);
    lgi_read(g_lgi_stream, new_wiper->wiper_parts.data(), sizeof(asINT32) * record.num_wiper_blade_parts);

    new_wiper->monitors.resize(record.num_monitors);
    lgi_read(g_lgi_stream, new_wiper->monitors.data(), sizeof(asINT32) * record.num_monitors);

    new_wiper->initialize();
  }
}


VOID sDGF_READER::read_data_curves() {

  LGI_DATA_CURVES_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  CONDUCTION_SIM_INFO parameters = &g_conduction_sim_info;
  parameters->num_data_curves = header.n_data_curves;

  ccDOTIMES(data_curve_num, header.n_data_curves) {
    std::unique_ptr<sDATA_CURVES> data_curve = std::make_unique<sDATA_CURVES>();
    LGI_DATA_CURVES_REC record;
    lgi_read(g_lgi_stream, record);
    std::string curve_name;
    lgi_read(g_lgi_stream, curve_name, sizeof(char) * record.name_length);

    // Find all materials that reference this curve
    BOOLEAN has_materials = FALSE;
    for (auto& material : parameters->conduction_materials) {
      for (int prop = 0; prop < NUM_MT_DATA; prop++) {
        if (material->property_curve_map[prop].first == curve_name) {
          has_materials = TRUE;
        }
      }
    }

    if (!has_materials) {
      if (my_proc_id == 0) {
        msg_warn("Data curve \"%s\" is not referenced by any solid conduction material", curve_name.c_str());
      }
    }


    // The first column in each datacurve is Temperature (x)
    std::vector<double> x_data(record.num_data_points);
    lgi_read(g_lgi_stream, x_data.data(), sizeof(double) * record.num_data_points);

    // Now read the other properties y=f(T)
    ccDOTIMES(ydata_index, record.num_ydata) {
      std::string ydata_name;
      LGI_YDATA_REC yrecord;
      lgi_read(g_lgi_stream, yrecord);
      lgi_read(g_lgi_stream, ydata_name, sizeof(char) * yrecord.name_length);

      std::vector<double> y_data(yrecord.num_ydata_points);
      lgi_read(g_lgi_stream, y_data.data(), sizeof(double) * yrecord.num_ydata_points);

      data_curve->mt_data[ydata_name].clear();
      for(int i = 0; i < yrecord.num_ydata_points; i++) {
        data_curve->mt_data[ydata_name].push_back(std::make_pair(x_data[i], y_data[i]));
      }
      std::sort(data_curve->mt_data[ydata_name].begin(), data_curve->mt_data[ydata_name].end());
    }
    // Finished reading in the data curve, add it in the g_conduction_sim_info struct
    parameters->material_data_curves[curve_name] = std::move(*data_curve);
  }

#if 0
  // Check if each material is referencing the correct data curves

  for (auto& material : parameters->conduction_materials) {
    printf("Material name %s\n", material->m_name.c_str());
    for (int prop = 0; prop < NUM_MT_DATA; prop++) {
      const std::string& curveName = material->property_curve_map[prop].first;
      const std::string& dataName = material->property_curve_map[prop].second;
      printf("PropNum %d Data curve %s\n", prop, curveName.c_str());
      std::vector<std::pair<double, double>> &data = parameters->material_data_curves[curveName].mt_data[dataName];
      for (int j = 0; j < data.size(); j++) printf("%g %g\n", data[j].first, data[j].second);
    }
  }
#endif

}



VOID sDGF_READER::read_particle_property_rng_state() {
  g_random_particle_properties->read_ckpt();
}

VOID sDGF_READER::read_particle_emitters_state_ckpt() {

  asINT32 n_emitters; // only emitters started after initial transient
  LGI_CKPT_PARTICLE_EMITTERS_STATE_HEADER header;
  read_lgi_head(header);
  n_emitters = header.n_emitters;

  LGI_CKPT_PARTICLE_EMITTERS_STATE_REC record;
  ccDOTIMES(i, n_emitters) {
    lgi_read(g_lgi_stream, record);
#if DEBUG_START_EMITTERS
    msg_print("read emitter %d requested to start %d scheduled to start %d requested start time %d start time %d",
              record.sim_emitter_id,
              record.is_requested_to_start,
              record.is_scheduled_to_start,
              record.requested_start_time,
              record.start_time);
#endif
    if (record.is_scheduled_to_start)
      g_particle_sim_info.emitters[record.sim_emitter_id]->set_start_time(record.start_time);
  }
}

VOID sDGF_READER::read_particle_emitter_ckpt() {
  LGI_CKPT_PARTICLE_EMITTERS_HEADER record_head;
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  read_lgi_head(record_head);
  if (record_head.n_emitters !=  parameters->emitters.size()) {
    msg_internal_error("Extra or missing emitter checkpoint records. Expected %zu but received %d",parameters->emitters.size(),record_head.n_emitters);
  }
  ccDOTIMES(i, parameters->emitters.size()) {
    LGI_CKPT_PARTICLE_EMITTER_REC subrec;
    read_lgi(subrec);
    parameters->emitters[i]->reset_parcel_id_counter(subrec.parcel_id_counter);
  }
}

VOID sDGF_READER::read_surface_emitter_geometry() {

  LGI_SURFACE_EMITTER_GEOMETRY_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);

  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;

  ccDOTIMES(surface_num, header.n_faces) {
    LGI_SURFACE_EMITTER_GEOMETRY_RECORD record;
    memset(&record,0,sizeof(record));
    size_t bytes_read = lgi_read(g_lgi_stream, &record, sizeof(record));
    if(bytes_read !=  sizeof(record)) {
      msg_error("Incomplete surface emitter geometry record read from LGI stream.\n");
    }
    asINT32 num_facets = record.num_facets;

    EMISSION_SURFACE emission_surface = parameters->emission_surfaces.get_existing_record(record.face_index);
    if(emission_surface == NULL)
      msg_error("Unexpected emission surface record found.\n");

    std::string emission_surface_name;
    lgi_read(g_lgi_stream, emission_surface_name, record.name_length);
    emission_surface->set_additional_info(emission_surface_name, record.csys_id, num_facets);

    ccDOTIMES(facet_index, num_facets) {
      asINT32 num_vertices;
      lgi_read(g_lgi_stream, num_vertices);

      std::vector<sG3_POINT> &m_vertices = emission_surface->facets()[facet_index].m_vertices;
      m_vertices.resize(num_vertices);

      ccDOTIMES(vertex_index, num_vertices) {
        lgi_read(g_lgi_stream, m_vertices[vertex_index].pcoord, 3 * sizeof(dFLOAT));
      }
    }
  }
}

VOID sDGF_READER::read_implicit_solver_data() {
  LGI_IMPLICIT_SOLVER_DATA record;

  lgi_read_next_head(g_lgi_stream, record);

  sim.num_implicit_shell_solver_states = record.num_implicit_shell_solver_states;
  sim.num_implicit_solid_solver_states = record.num_implicit_solid_solver_states;

  std::vector<int> implicit_shell_solver_state_index_offset(total_sps, 0);
  lgi_read(g_lgi_stream, implicit_shell_solver_state_index_offset.data(), total_sps * sizeof(int));

  sim.implicit_shell_solver_state_index_offset = implicit_shell_solver_state_index_offset;
  
  std::vector<int> implicit_solid_solver_state_index_offset(total_sps, 0);
  lgi_read(g_lgi_stream, implicit_solid_solver_state_index_offset.data(), total_sps * sizeof(int));

  sim.implicit_solid_solver_state_index_offset = implicit_solid_solver_state_index_offset;

}

//#endif
