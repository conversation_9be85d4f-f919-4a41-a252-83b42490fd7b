/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Support for symmetry planes 
 *
 * Sam Watson, Exa Corporation 
 * Created Wednesday July 24 2002
 *
 * Possible mottoes:
 *
 *  "Objects in the mirror are exactly where they appear"
 *
 *  "Pay no attention to the man behind the curtain"
 *
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "mirror.h"
#include "sim.h"
#include "sp_timers.h"
#include "ublk_table.h"
#include "surfel.h"
#include "surfel_table.h"
#include "strand_mgr.h"

VOID reflect_mirror_surfel_weights_to_real_surfel(SURFEL source_surfel, SURFEL mirror_surfel,
                                                  STP_LATVEC_MASK latvec_mask,
                                                  STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {

  source_surfel->mme_weight += mirror_surfel->mme_weight;
  source_surfel->s2s_sampling_weight += mirror_surfel->s2s_sampling_weight;
  
  //is_conduction_surface means interacting with volume conduction
  //only those surfels have a conduction_data
  if (sim.is_conduction_model && source_surfel->is_conduction_surface()){ 
    source_surfel->conduction_data()->sampling_weight_without_interface_s2s_inv += mirror_surfel->conduction_data()->sampling_weight_without_interface_s2s_inv;
    source_surfel->y_sample += mirror_surfel->y_sample;
    source_surfel->conduction_data()->sampling_distance_with_interface_s2s += mirror_surfel->conduction_data()->sampling_distance_with_interface_s2s;
  }
  
  ccDOTIMES(state_index, N_MOVING_STATES) {
    if ((latvec_mask >> state_index) & 1) {
      STP_STATE_INDEX source_state_index = state_index;
      STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
      STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

      source_surfel->in_state_scale_factors[source_latvec_pair_index] +=
        mirror_surfel->in_state_scale_factors[mirror_latvec_pair_index];
      source_surfel->in_states_voxel_weight[source_latvec_pair_index] +=
        mirror_surfel->in_states_voxel_weight[mirror_latvec_pair_index];
      source_surfel->in_states_voxel_weight2[source_latvec_pair_index] +=
        mirror_surfel->in_states_voxel_weight2[mirror_latvec_pair_index];
    }
  }
}

VOID reflect_real_surfel_weights_to_mirror_surfel(SURFEL source_surfel, SURFEL mirror_surfel,
                                                  STP_LATVEC_MASK latvec_mask,
                                                  STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {

  mirror_surfel->mme_weight = source_surfel->mme_weight;
  mirror_surfel->s2s_sampling_weight = source_surfel->s2s_sampling_weight;
  
  if (sim.is_conduction_model && source_surfel->is_conduction_surface()){
    mirror_surfel->conduction_data()->sampling_weight_without_interface_s2s_inv = source_surfel->conduction_data()->sampling_weight_without_interface_s2s_inv;
    mirror_surfel->y_sample = source_surfel->y_sample;
    mirror_surfel->conduction_data()->sampling_distance_with_interface_s2s = source_surfel->conduction_data()->sampling_distance_with_interface_s2s;
  }
  
  ccDOTIMES(pvol, N_SURFEL_PGRAM_VOLUMES) {
    mirror_surfel->in_states_voxel_weight[pvol] = source_surfel->in_states_voxel_weight[pvol];
    mirror_surfel->in_states_voxel_weight2[pvol] = source_surfel->in_states_voxel_weight2[pvol];
  }
  ccDOTIMES(state_index, N_MOVING_STATES) {
    if ((latvec_mask >> state_index) & 1) {
      STP_STATE_INDEX source_state_index = state_index;
      STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
      STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

      mirror_surfel->in_states_voxel_weight[mirror_latvec_pair_index] =
        source_surfel->in_states_voxel_weight[source_latvec_pair_index];
      mirror_surfel->in_states_voxel_weight2[mirror_latvec_pair_index] =
        source_surfel->in_states_voxel_weight2[source_latvec_pair_index];
    }
  }
}
// functions for mirror ublks

static tEARRAY< sUBLK_MIRROR_CONFIG > mirror_ublk_configs(0);
static tEARRAY< sSURFEL_MIRROR_CONFIG > mirror_surfel_configs(0);

static inline UBLK_MIRROR_CONFIG alloc_mirror_config()
{
  return mirror_ublk_configs.append();
}

UBLK_MIRROR_CONFIG find_mirror_ublk_config(STP_STATE_INDEX state_index)
{
  /* If a configuration exists with the same attributes, return it. */
  TEARRAY_DOTIMES(sUBLK_MIRROR_CONFIG, config, i, mirror_ublk_configs) {
    if (config->m_state_index == state_index)
      return config;
  }

  /* Otherwise, create a new configuration. */
  UBLK_MIRROR_CONFIG mirror_config = alloc_mirror_config();
  mirror_config->init(state_index);
  return mirror_config;
}

SURFEL_MIRROR_CONFIG find_mirror_surfel_config(STP_DIRECTION direction)
{
  /* If a configuration exists with the same attributes, return it. */
  TEARRAY_DOTIMES(sSURFEL_MIRROR_CONFIG, config, i, mirror_surfel_configs) {
    if (config->m_direction == direction)
      return config;
  }

  /* Otherwise, create a new configuration. */
  SURFEL_MIRROR_CONFIG mirror_config = mirror_surfel_configs.append();
  mirror_config->init(direction);
  return mirror_config;
}

static VOID copy_voxel_masks_and_pfluids_to_mirror_ublk(UBLK source_ublk)
{
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
#if BUILD_5G_LATTICE
  BOOLEAN copy_real_porosity = sim.is_large_pore && g_is_multi_component;
#endif
  while (mirror_data) {

    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
    // This should already have been done while parsing the mirror ublk
    mirror_ublk->set_ref_frame_index(source_ublk->ref_frame_index());
    VOXEL_MASK_8 fluid_like_voxel_mask{0};
    VOXEL_MASK_8 basic_fluid_voxel_mask{0};

    if (mirror_config->state_pair_table.size() > 0) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        asINT32 mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        if (source_ublk->fluid_like_voxel_mask.test(voxel)) {
          fluid_like_voxel_mask.set(mirror_voxel);
        }
        if (source_ublk->basic_fluid_voxel_mask.test(voxel)) {
          basic_fluid_voxel_mask.set(mirror_voxel);
        }
        if (source_ublk->is_near_surface()) {
          mirror_ublk->surf_geom_data()->pfluids[mirror_voxel] =
            source_ublk->surf_geom_data()->pfluids[voxel];
        }
#if BUILD_5G_LATTICE
	mirror_ublk->porosity[mirror_voxel] = source_ublk->porosity[voxel]; //large_pore
	if (copy_real_porosity) {
	  mirror_ublk->pore_lb_data()->porosity_input[mirror_voxel] = source_ublk->pore_lb_data()->porosity_input[voxel];
	  mirror_ublk->pore_lb_data()->wall_potential[mirror_voxel] = source_ublk->pore_lb_data()->wall_potential[voxel];
	  mirror_ublk->pore_mc_data()->wall_potential[mirror_voxel] = source_ublk->pore_mc_data()->wall_potential[voxel];
	}
#endif
      }
    }
    //Perhaps only the voxels that store mirrored states are needed
    if (source_ublk->is_vr_fine()) {
      mirror_ublk->basic_fluid_voxel_mask = basic_fluid_voxel_mask;
    }
    mirror_ublk->fluid_like_voxel_mask = fluid_like_voxel_mask;
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

VOID copy_voxel_masks_and_pfluids_to_mirror_ublks()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (farblk->has_mirror())
        copy_voxel_masks_and_pfluids_to_mirror_ublk(farblk);
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->has_mirror())
        copy_voxel_masks_and_pfluids_to_mirror_ublk(nearblk);
    }
    DO_VRBLKS_OF_SCALE(vrblk, scale) {
      if (vrblk->has_mirror())
        copy_voxel_masks_and_pfluids_to_mirror_ublk(vrblk);
      
    auto * vr_fine_data =  vrblk->vr_fine_data();
    UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();

      // ghost vr coarse ublks are exploded, and their mirrors
      // need to be initialized properly. Of course, sometimes the
      // mirror isn't ghosted on the SP, so gotta check for that.
      if ( coarse_ublk->is_ghost() && coarse_ublk->has_mirror() ) {
	sUBLK_MIRROR_DATA * mirror_data = coarse_ublk->mirror_data();
	if (mirror_data->m_mirror_ublk) {
	  copy_voxel_masks_and_pfluids_to_mirror_ublk(coarse_ublk);
	}
      } 
    }
    //PR53887
    DO_GHOST_NEARBLKS_OF_SCALE(quantum, scale) {
      if (quantum.m_ublk->has_mirror()) {
        sUBLK_MIRROR_DATA *mirror_data = quantum.m_ublk->mirror_data();
        if (mirror_data->m_mirror_ublk && mirror_data->m_ublk_mirror_config){
	  copy_voxel_masks_and_pfluids_to_mirror_ublk(quantum.m_ublk);
        }
      }
    }
  }
}

VOID reflect_curr_states_to_mirror_ublk(UBLK source_ublk, SOLVER_INDEX_MASK prior_solver_index_mask) {
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = source_ublk->has_two_copies() ? (1 ^ prev_lb_index) : ONLY_ONE_COPY;
  asINT32 prev_t_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_t_index = source_ublk->has_two_copies() ? (1 ^ prev_t_index) : ONLY_ONE_COPY;
  asINT32 prev_uds_index = uds_index_from_mask(prior_solver_index_mask);
  asINT32 curr_uds_index = source_ublk->has_two_copies() ? (1 ^ prev_uds_index) : ONLY_ONE_COPY;

  while (mirror_data) {

    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;

    UBLK_STATE *mirror_states = (UBLK_STATE *) mirror_ublk->lb_states(curr_lb_index)->m_states;
    UBLK_STATE *source_states = (UBLK_STATE *) source_ublk->lb_states(curr_lb_index)->m_states;
    UBLK_STATE *mirror_states_t = NULL;
    UBLK_STATE *source_states_t = NULL;
    UBLK_STATE *mirror_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};
    UBLK_STATE *source_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};

    BOOLEAN is_T_S_lb_solver_on = FALSE;
    BOOLEAN is_UDS_lb_solver_on = FALSE;
    if(sim.thermal_accel.do_T_solver_switch) {
      if(sim.T_solver_type == PDE_TEMPERATURE)
        is_T_S_lb_solver_on = TRUE;
    } else
      is_T_S_lb_solver_on = sim.is_T_S_solver_type_lb();

    if (is_T_S_lb_solver_on) {
      mirror_states_t = (UBLK_STATE *) mirror_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t;
      source_states_t = (UBLK_STATE *) source_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t;
    }

    if (sim.uds_solver_type == LB_UDS)  {
      is_UDS_lb_solver_on = TRUE;
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	mirror_states_uds[nth_uds] = (UBLK_STATE *) mirror_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
	source_states_uds[nth_uds] = (UBLK_STATE *) source_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
      } 
    } 

#if BUILD_5G_LATTICE
    UBLK_STATE *mirror_states_mc = NULL;
    UBLK_STATE *source_states_mc = NULL;
    if (g_is_multi_component) {
      UBLK_STATE *mirror_states_mc = (UBLK_STATE *) mirror_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc;
      UBLK_STATE *source_states_mc = (UBLK_STATE *) source_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc;
    }
#endif  

    ccDOTIMES(voxel, N_VOXELS_8) {
      if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        {
          ccDOTIMES(i_state, N_MOVING_STATES) {
            mirror_states[i_state][mirror_voxel] = source_states[i_state][voxel];
            if (is_T_S_lb_solver_on) {
              mirror_states_t[i_state][mirror_voxel] = source_states_t[i_state][voxel];
            }
	    if (is_UDS_lb_solver_on) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
                mirror_states_uds[nth_uds][i_state][mirror_voxel] = source_states_uds[nth_uds][i_state][voxel];
              }
	    }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      mirror_states_mc[i_state][mirror_voxel] = source_states_mc[i_state][voxel];
	    }
#endif
          }
          ccDOTIMES(state_pair, mirror_config->state_pair_table.size()) {
            STP_STATE_INDEX source_state_index = mirror_config->state_pair_table[state_pair].source_state;
            STP_STATE_INDEX mirror_state_index = mirror_config->state_pair_table[state_pair].mirror_state;
            mirror_states[source_state_index][mirror_voxel] = source_states[mirror_state_index][voxel];
            mirror_states[mirror_state_index][mirror_voxel] = source_states[source_state_index][voxel];
            if (is_T_S_lb_solver_on) {
              mirror_states_t[source_state_index][mirror_voxel] = source_states_t[mirror_state_index][voxel];
              mirror_states_t[mirror_state_index][mirror_voxel] = source_states_t[source_state_index][voxel];
            }
	    if (is_UDS_lb_solver_on) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
                mirror_states_uds[nth_uds][source_state_index][mirror_voxel] = source_states_uds[nth_uds][mirror_state_index][voxel];
                mirror_states_uds[nth_uds][mirror_state_index][mirror_voxel] = source_states_uds[nth_uds][source_state_index][voxel];
              }
	    }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      mirror_states_mc[source_state_index][mirror_voxel] = source_states_mc[mirror_state_index][voxel];
              mirror_states_mc[mirror_state_index][mirror_voxel] = source_states_mc[source_state_index][voxel];
	    }
#endif	   
          }
        }
      }
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

VOID reflect_prev_states_to_mirror_ublk(UBLK source_ublk, SOLVER_INDEX_MASK prior_solver_index_mask) {
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = 1 ^ prev_lb_index;
  asINT32 prev_t_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_t_index = 1 ^ prev_t_index;
  asINT32 prev_uds_index = uds_index_from_mask(prior_solver_index_mask);
  asINT32 curr_uds_index = 1 ^ prev_uds_index;
  while (mirror_data) {

    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;


    UBLK_STATE *mirror_states = NULL;
    UBLK_STATE *source_states = NULL;
    UBLK_STATE *mirror_states_t = NULL;
    UBLK_STATE *source_states_t = NULL;
    UBLK_STATE *mirror_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};
    UBLK_STATE *source_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};

    if (source_ublk->has_two_copies()) {
      source_states = (UBLK_STATE *) source_ublk->lb_states(prev_lb_index)->m_states;
      mirror_states = (UBLK_STATE *) mirror_ublk->lb_states(prev_lb_index)->m_states;
    } else {
      mirror_states = (UBLK_STATE *) mirror_ublk->lb_states(ONLY_ONE_COPY)->m_states;
      source_states = (UBLK_STATE *) source_ublk->lb_states(ONLY_ONE_COPY)->m_states;

    }

    BOOLEAN is_T_S_lb_solver_on = sim.is_T_S_solver_type_lb();
    BOOLEAN is_UDS_lb_solver_on = (sim.uds_solver_type == LB_UDS) ? TRUE : FALSE;
    if (is_T_S_lb_solver_on ) {
      if (source_ublk->has_two_copies()) {
        mirror_states_t = (UBLK_STATE *) mirror_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t;
        source_states_t = (UBLK_STATE *) source_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t;
      } else {
        mirror_states_t = (UBLK_STATE *) mirror_ublk->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t;
        source_states_t = (UBLK_STATE *) source_ublk->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t;
      }
    }

    if (is_UDS_lb_solver_on ) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	if (source_ublk->has_two_copies()) {
	  mirror_states_uds[nth_uds] = (UBLK_STATE *) mirror_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds;
	  source_states_uds[nth_uds] = (UBLK_STATE *) source_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds;
	} else {
	  mirror_states_uds[nth_uds] = (UBLK_STATE *) mirror_ublk->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds;
	  source_states_uds[nth_uds] = (UBLK_STATE *) source_ublk->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds;
	}
      }
    }

#if BUILD_5G_LATTICE
    UBLK_STATE *mirror_states_mc = NULL;
    UBLK_STATE *source_states_mc = NULL;
    if (g_is_multi_component) {
      if (source_ublk->has_two_copies()) {
        mirror_states_mc = (UBLK_STATE *) mirror_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc;
        source_states_mc = (UBLK_STATE *) source_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc;
      } else {
        mirror_states_mc = (UBLK_STATE *) mirror_ublk->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc;
        source_states_mc = (UBLK_STATE *) source_ublk->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc;
      }
    }
#endif

    ccDOTIMES(voxel, N_VOXELS_8) {
      if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        {
          ccDOTIMES(i_state, N_MOVING_STATES) {
            mirror_states[i_state][mirror_voxel] = source_states[i_state][voxel];
            if (is_T_S_lb_solver_on ) {
              mirror_states_t[i_state][mirror_voxel] = source_states_t[i_state][voxel];
            }
	    if (is_UDS_lb_solver_on ) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
                mirror_states_uds[nth_uds][i_state][mirror_voxel] = source_states_uds[nth_uds][i_state][voxel];
            }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      mirror_states_mc[i_state][mirror_voxel] = source_states_mc[i_state][voxel];
	    }
#endif
          }
          ccDOTIMES(state_pair, mirror_config->state_pair_table.size()) {
            STP_STATE_INDEX source_state_index = mirror_config->state_pair_table[state_pair].source_state;
            STP_STATE_INDEX mirror_state_index = mirror_config->state_pair_table[state_pair].mirror_state;
            mirror_states[source_state_index][mirror_voxel] = source_states[mirror_state_index][voxel];
            mirror_states[mirror_state_index][mirror_voxel] = source_states[source_state_index][voxel];
            if (is_T_S_lb_solver_on ) {
              mirror_states_t[source_state_index][mirror_voxel] = source_states_t[mirror_state_index][voxel];
              mirror_states_t[mirror_state_index][mirror_voxel] = source_states_t[source_state_index][voxel];
            }
	    if (is_UDS_lb_solver_on ) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
                mirror_states_uds[nth_uds][source_state_index][mirror_voxel] = source_states_uds[nth_uds][mirror_state_index][voxel];
                mirror_states_uds[nth_uds][mirror_state_index][mirror_voxel] = source_states_uds[nth_uds][source_state_index][voxel];
              }
	    }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      mirror_states_mc[source_state_index][mirror_voxel] = source_states_mc[mirror_state_index][voxel];
              mirror_states_mc[mirror_state_index][mirror_voxel] = source_states_mc[source_state_index][voxel];
	    }
#endif
          }
          // TODO Should the states be zeroed out for mirror ublks?
          if (source_ublk->has_two_copies()) {
            ccDOTIMES(i_state, N_MOVING_STATES) {
              mirror_ublk->lb_states(curr_lb_index)->m_states[i_state][mirror_voxel] = 0.0;
              if (is_T_S_lb_solver_on) {
                mirror_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t[i_state][mirror_voxel] = 0.0;
              }
	      if (is_UDS_lb_solver_on) {
		ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
                  mirror_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds[i_state][mirror_voxel] = 0.0;
              }
#if BUILD_5G_LATTICE
	      if (g_is_multi_component){
		mirror_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc[i_state][mirror_voxel] = 0.0;
	      }
#endif
            }
          }
        }
      }
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

#if BUILD_5G_LATTICE
VOID reflect_adv_states_to_mirror_ublk(UBLK source_ublk, asINT32 time_index) {
  //source_ublk is dynblk, it cannot be a ghost
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  
  while (mirror_data) {
    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;

    UBLK_STATE *mirror_states = (UBLK_STATE *) mirror_ublk->lb_states(time_index)->m_states;
    UBLK_STATE *source_states = (UBLK_STATE *) source_ublk->lb_states(time_index)->m_states;

    UBLK_STATE *mirror_states_mc = NULL;
    UBLK_STATE *source_states_mc = NULL;

    if (g_is_multi_component){
      mirror_states_mc = (UBLK_STATE *) mirror_ublk->mc_data()->mc_states_data(time_index)->m_states_mc;
      source_states_mc = (UBLK_STATE *) mirror_ublk->mc_data()->mc_states_data(time_index)->m_states_mc;
    }
    
    ccDO_UBLK_VOXELS(voxel) {
      if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        {
          ccDOTIMES(i_state, N_MOVING_STATES) {
            mirror_states[i_state][mirror_voxel] = source_states[i_state][voxel];
            if (g_is_multi_component ) {
              mirror_states_mc[i_state][mirror_voxel] = source_states_mc[i_state][voxel];
	    }
	  }

	  ccDOTIMES(state_pair, mirror_config->state_pair_table.size()) {
            STP_STATE_INDEX source_state_index = mirror_config->state_pair_table[state_pair].source_state;
            STP_STATE_INDEX mirror_state_index = mirror_config->state_pair_table[state_pair].mirror_state;
            mirror_states[source_state_index][mirror_voxel] = source_states[mirror_state_index][voxel];
	    mirror_states[mirror_state_index][mirror_voxel] = source_states[source_state_index][voxel];
	    if (g_is_multi_component ) {
              mirror_states_mc[source_state_index][mirror_voxel] = source_states_mc[mirror_state_index][voxel];
              mirror_states_mc[mirror_state_index][mirror_voxel] = source_states_mc[source_state_index][voxel];
            }
	  }
	}
      }
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

VOID reflect_full_states_to_mirror_ublk(UBLK source_ublk) {
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  
  while (mirror_data) {
    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;

    UBLK_STATE *mirror_states = (UBLK_STATE *) mirror_ublk->pore_lb_data()->full_states;
    UBLK_STATE *source_states = (UBLK_STATE *) source_ublk->pore_lb_data()->full_states;;

    UBLK_STATE *mirror_states_mc = NULL;
    UBLK_STATE *source_states_mc = NULL;

    UBLK_STATE *mirror_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};
    UBLK_STATE *source_states_uds[MAX_N_USER_DEFINED_SCALARS] = {NULL};

    if (g_is_multi_component){
      mirror_states_mc = (UBLK_STATE *) mirror_ublk->pore_mc_data()->full_states;
      source_states_mc = (UBLK_STATE *) source_ublk->pore_mc_data()->full_states;
    }

    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
	mirror_states_uds[nth_uds] = (UBLK_STATE *) mirror_ublk->pore_uds_data(nth_uds)->full_states;
	source_states_uds[nth_uds] = (UBLK_STATE *) source_ublk->pore_uds_data(nth_uds)->full_states;
      }
    }
    
    ccDO_UBLK_VOXELS(voxel) {
      if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        {
          ccDOTIMES(i_state, N_MOVING_STATES) {
            mirror_states[i_state][mirror_voxel] = source_states[i_state][voxel];
            if (g_is_multi_component ) {
              mirror_states_mc[i_state][mirror_voxel] = source_states_mc[i_state][voxel];
	    }
	    if (sim.is_scalar_model) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
		mirror_states_uds[nth_uds][i_state][mirror_voxel] = source_states_uds[nth_uds][i_state][voxel];
	      }
	    }
	  }

	  ccDOTIMES(state_pair, mirror_config->state_pair_table.size()) {
            STP_STATE_INDEX source_state_index = mirror_config->state_pair_table[state_pair].source_state;
            STP_STATE_INDEX mirror_state_index = mirror_config->state_pair_table[state_pair].mirror_state;
            mirror_states[source_state_index][mirror_voxel] = source_states[mirror_state_index][voxel];
	    mirror_states[mirror_state_index][mirror_voxel] = source_states[source_state_index][voxel];
	    if (g_is_multi_component ) {
              mirror_states_mc[source_state_index][mirror_voxel] = source_states_mc[mirror_state_index][voxel];
              mirror_states_mc[mirror_state_index][mirror_voxel] = source_states_mc[source_state_index][voxel];
            }
	    if (sim.is_scalar_model) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
		mirror_states_uds[nth_uds][source_state_index][mirror_voxel] = source_states_uds[nth_uds][mirror_state_index][voxel];
		mirror_states_uds[nth_uds][mirror_state_index][mirror_voxel] = source_states_uds[nth_uds][source_state_index][voxel];
	      }
	    }
	  }
	}
      }
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}
#endif

#ifdef MIRROR_COMM
VOID reflect_solver_data_from_ghosted_voxels_to_mirror_voxels(SCALE scale, 
                                                              ACTIVE_SOLVER_MASK active_solver_mask)
{
  DO_GHOSTED_DYNBLK_MIRROR_GROUPS_OF_SCALE(group1, scale) {
    WITH_ITEM_TIMER(SP_UBLK_MIRROR_COPY_TIMER, group1->n_quantums()) {
      group1->reflect_solver_data_from_real_voxels_to_mirror_voxels(active_solver_mask);
    }
  }
  DO_GHOSTED_VRBLK_MIRROR_GROUPS_OF_SCALE(group2, scale) {
    WITH_ITEM_TIMER(SP_UBLK_MIRROR_COPY_TIMER, group2->n_quantums()) {
      group2->reflect_solver_data_from_real_voxels_to_mirror_voxels(active_solver_mask);
    }
  }
}
#endif

//Called from seed.cc and ublk_d19.cc
VOID reflect_solver_data_to_mirror_ublk(UBLK source_ublk, ACTIVE_SOLVER_MASK active_solver_mask) {
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  while (mirror_data) {

    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      //if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        STP_PHYS_VARIABLE *mirror_sign_factors = mirror_config->velocity_mirror_sign_factor;
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        source_ublk->reflect_solver_data_from_real_voxel_to_mirror_voxel(mirror_ublk, mirror_voxel,
                                                                         voxel, mirror_sign_factors,
                                                                         active_solver_mask);
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        if (source_ublk->id()==2645){
          LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("su %d(%d) mu %d(%d),temp %g, %g", 
            source_ublk->id(), voxel, mirror_ublk->id(), mirror_voxel,
            source_ublk->conduction_data()->solid_temp[0][voxel],
            mirror_ublk->conduction_data()->solid_temp[0][mirror_voxel]);
        }
#endif
      //}
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

VOID reflect_mme_solver_data_to_mirror_ublk(UBLK source_ublk, ACTIVE_SOLVER_MASK active_solver_mask) {
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  while (mirror_data) {

    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      //if (mirror_config->voxels_to_mirror_mask.test(voxel)) {
        STP_PHYS_VARIABLE *mirror_sign_factors = mirror_config->velocity_mirror_sign_factor;
        VOXEL_NUM mirror_voxel = mirror_config->voxel_mirror_table[voxel];
        source_ublk->reflect_mme_solver_data_from_real_voxel_to_mirror_voxel(mirror_ublk, mirror_voxel,
                                                                         voxel, mirror_sign_factors,
                                                                         active_solver_mask);
      //}
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
}

//Called from seed.cc
VOID reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(SCALE scale,
                                                                  ACTIVE_SOLVER_MASK active_solver_mask)
{
  DO_FARBLKS_OF_SCALE(farblk, scale) {
    if (farblk->has_mirror()) {
      reflect_solver_data_to_mirror_ublk(farblk, active_solver_mask);
    }
  }
  DO_NEARBLKS_OF_SCALE(nearblk, scale) {
    if (nearblk->has_mirror()) {
      reflect_solver_data_to_mirror_ublk(nearblk, active_solver_mask);
    }
  }
  DO_VRBLKS_OF_SCALE(vrblk, scale) {
    if (vrblk->has_mirror()) {
     reflect_solver_data_to_mirror_ublk(vrblk, active_solver_mask);
    }
  }
}

#if BUILD_5G_LATTICE
VOID reflect_full_states_from_dynblk_voxels_to_mirror_voxels(SCALE scale)
{
  DO_FARBLKS_OF_SCALE(farblk, scale) {
    if (farblk->has_mirror()) {
      reflect_full_states_to_mirror_ublk(farblk);
    }
  }
  DO_NEARBLKS_OF_SCALE(nearblk, scale) {
    if (nearblk->has_mirror()) {
      reflect_full_states_to_mirror_ublk(nearblk);
    }
  }
  //no vrblks in 5G
}
#endif
