#include <stdlib.h>

#include LGI_H
#include MSGERR_H

#include "particle_random_properties.h"

#define OK 0
#define ERROR 1

LGI_STREAM g_ckpt_lgi_stream = NULL;
LGI_STREAM g_cond_ckpt_lgi_stream = NULL;
LGI_STREAM g_lgi_stream = NULL;

const int num_dist_parameters[] = {2,2,2,2,2,2,2,2,2,1};

void print_usage(char* exec_name);

int my_proc_id = 0;

int main(int argc, char *argv[]) 
{

  if (getenv("EXA_WAIT_ON_STARTUP")) {
    printf("CP>  pid %d Hit return to continue.", getpid());
    getchar();
  }

  printf("\n");

  if( argc < 3) {
    print_usage(argv[0]); 
    return(ERROR);
  }

  sPDF::ePDF_TYPE pdf_type = (sPDF::ePDF_TYPE)atoi(argv[1]);
  if(pdf_type > sPDF::NUM_DISTRIBUTION_TYPES) {
    printf("Unsupported PDF type index %d.\n", pdf_type);
    print_usage(argv[0]);
    exit(ERROR);
  }

  if(argc < (3 + num_dist_parameters[pdf_type]) ) {
    printf("Expected %d parameters for the %s distribution.\n",
           num_dist_parameters[ pdf_type ],
           sPDF::pdfs_distribution_names[ pdf_type ].c_str());
    print_usage(argv[0]);
    return(ERROR);
  }

  int num_samples = atoi(argv[2]);
  sPARTICLE_VAR mean = atof(argv[3]);

  sPARTICLE_VAR dist_param2 = 0.0;
  if(num_dist_parameters[pdf_type] > 1)
    dist_param2 = atof(argv[4]);


  g_random_particle_properties = cnew sRANDOM_PARTICLE_PROPERTIES;

  //Open a file to store random samples in for futher analysis.
  FILE* data_file = fopen("random_samples.dat","w");
  if(data_file == NULL)
    return(ERROR);

  //Print a header to the datafile and a message to stdout
  if(pdf_type == sPDF::POISSON_DISTRIBUTION) {
    printf("Computing %d samples consistent with a %s probability distribution function (mean %g).\n",
           num_samples, sPDF::pdfs_distribution_names[pdf_type].c_str(), mean);
    fprintf(data_file, "#PDF: %s\n#num_samples: %d\n#mean: %g\n",
            sPDF::pdfs_distribution_names[pdf_type].c_str(), num_samples, mean);
 
  } else {
    printf("Computing %d samples consistent with a %s probability distribution function (mean %g param %g).\n",
           num_samples, sPDF::pdfs_distribution_names[pdf_type].c_str(), mean, dist_param2);
    fprintf(data_file, "#PDF: %s\n#num_samples: %d\n#mean: %g\n#dist_param2: %g",
            sPDF::pdfs_distribution_names[pdf_type].c_str(), num_samples, mean, dist_param2);
  }
  

  //Compute many samples
  std::vector<sPARTICLE_VAR> samples;
  samples.reserve(num_samples);
  for(int i = 0; i < num_samples ; i++) {
    samples.push_back(g_random_particle_properties->random_number(mean, dist_param2, pdf_type));
    fprintf(data_file,"%g\n",samples.back());
  }
  
  //Then measure their mean.
  sPARTICLE_VAR measured_mean = 0;
  for(int i = 0; i < num_samples ; i++) {
    measured_mean += samples[i];
  }
  measured_mean /= (sPARTICLE_VAR)num_samples;

  //And measure their standard deviation.
  sPARTICLE_VAR measured_stddev = 0;
  for(int i = 0; i < num_samples ; i++) {
    measured_stddev += pow(samples[i] - measured_mean, 2);
  }
  measured_stddev /= (sPARTICLE_VAR)num_samples;
  measured_stddev = std::sqrt(measured_stddev);

  //Print the results.
  sPARTICLE_VAR expected_stddev = g_random_particle_properties->expected_stddev(mean, dist_param2, pdf_type);
  sPARTICLE_VAR expected_mean = g_random_particle_properties->expected_mean(mean, dist_param2, pdf_type);;
  printf("With %d samples, mean is %g (%g %%error) and stddev is %g( %g %%error). Maximum expected error for this sample size is %g%%\n",
         num_samples, measured_mean, 
         100.0 * fabs(measured_mean - expected_mean) / expected_mean, 
         measured_stddev, 
         100.0 * fabs(measured_stddev - expected_stddev) / expected_stddev,
         100.0 / std::sqrt(num_samples));

  printf("\n");

  return(OK);
}

void print_usage(char* exec_name) {
  printf("Expected at least 3 arguments:\n"
         "%s <pdf_type> <num_samples> [dist parameters]...\n", exec_name);

  printf("Valid pdf types are:\n");
  for(int i = 0 ; i < sPDF::NUM_DISTRIBUTION_TYPES; i++) {
    printf("%d - %s\n", i, sPDF::pdfs_distribution_names[i].c_str());
  }

}
