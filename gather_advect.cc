/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Vinit Gupta, Exa Corporation                      Created Fri, Dec 14, 2012
//----------------------------------------------------------------------------
//
#include "box_advect.h"
#include "gather_advect.h"
#include "comm_groups.h"

#if BUILD_AVX2 && !(BUILD_DOUBLE_PRECISION)
#include BOX_ADVECT_NEIGHBORS_AVX_2D
#include BOX_ADVECT_NEIGHBORS_AVX_3D
#else
#include BOX_ADVECT_NEIGHBORS_2D
#include BOX_ADVECT_NEIGHBORS_3D
#endif

#include BOX_SPLIT_ADVECT_NEIGHBORS_2D
#include BOX_SPLIT_ADVECT_NEIGHBORS_3D

 
#if BUILD_D39_LATTICE
static ALIGN_VECTOR STP_GEOM_VARIABLE unity_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS] =
    {
     //0
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //1
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //2
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //3
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //4
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //5
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //6
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //7
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
    };
#else
static ALIGN_VECTOR  STP_GEOM_VARIABLE unity_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS] =
    {
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
    };
#endif

inline namespace ADVECT_NAMESPACE {

  // This function doesn't even USE voxel mask!
template <BOOLEAN IS_VR_FINE, BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
VOID gather_advect_for_unsplit_neighbors(sUBLK *pde_advect_ublk,
                                         VOXEL_MASK_8 voxel_mask,
                                         UBLK (&active_nbrs) [N_MOVING_STATES],
                                         BOOLEAN is_timestep_even,
                                         SOLVER_INDEX_MASK prior_solver_index_mask,
                                         ACTIVE_SOLVER_MASK active_solver_mask)
{

  const auto& box_access = pde_advect_ublk->m_box_access;

  STP_GEOM_VARIABLE (*pas_factors)[ubFLOAT::N_VOXELS];
  if (pde_advect_ublk->is_near_surface()) {
    pas_factors = (STP_GEOM_VARIABLE (*)[ubFLOAT::N_VOXELS])
                  pde_advect_ublk->surf_lb_data()->post_advect_scale_factors;
  } else {
    pas_factors = unity_pas_factors;
  }

  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = (1 ^ prev_lb_index);

  asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_t_index = (1 ^ prev_t_index);

  asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);
  asINT32 curr_uds_index = (1 ^ prev_uds_index);

  VOXEL_STATE (*states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(curr_lb_index)->m_states);
  VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(prev_lb_index)->m_states);
  VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  if (ADVECT_TEMP) {
    states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t);
    p_states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t);
  }
  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds);
      p_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds);
    }
  }

  VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  if (g_is_multi_component) {
    states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc);
    p_states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc);
  }

  ALIGN_VECTOR STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS];
  BOOLEAN use_vrf_pas = FALSE;
  if (IS_VR_FINE && pde_advect_ublk->is_near_surface()) {
    // TODO This has to be written correctly during initialization.
    // If a VRFINE voxel does not interact with any surfel during ES2V
    // phase, the pas factors for that voxel should be 1 during even
    // time steps. This will work even if pas factor is 0 because the
    // fluid connectivity check should not allow contributions from
    // such neighbors.
    if (is_timestep_even) {
      sVR_FINE_INTERFACE_DATA *vr_fine_data = pde_advect_ublk->vr_fine_data();
      ccDOTIMES(latvec, N_MOVING_STATES) {
        // If there is no contribution during ES2V for VR fine ublk along a
        // particular latvec, the pas_factor should be forced to 1 on even
        // time steps.
        // However pas_factor should not be forced to 1 on odd time steps,
        // since the VR coarse coalesced states get the S2V contribution from
        // the surfels and V2V contribution via coalescing of VR fine voxels
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          sdFLOAT pas_factor = pas_factors[latvec][voxel];
          BOOLEAN no_even_s2v = !vr_fine_data->m_even_s2v_weights_mask[latvec].test(voxel);
          if (!((states[latvec][voxel] == 0.0) && (pas_factor > 0))) {
            vrf_pas_factors[latvec][voxel] = pas_factor;
          } else {
            vrf_pas_factors[latvec][voxel] = 1.0;
#if ENABLE_CONSISTENCY_CHECKS
            //TODO This check should go away after regression tests run successfully.
            if ((states[latvec][voxel] > 0) || (pas_factor == 0)) {
              msg_internal_error("VR fine %d states should be 0 l %d v %d state %e",
                                 pde_advect_ublk->id(), latvec, voxel, states[latvec][voxel]);
            }
#endif
          }
        }
      }
      use_vrf_pas = TRUE;
    }
  }
  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();

  VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};


#if BUILD_D39_LATTICE
#if BUILD_AVX2  && !(BUILD_DOUBLE_PRECISION)
  if ( is_2D ) {
    d39_avx_collect_advect_states_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,
										is_timestep_even,
										states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
										box_access,
										prior_solver_index_mask,
										active_solver_mask,vrf_pas_factors,
										pas_factors,use_vrf_pas);
  } else {
    d39_avx_collect_advect_states_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,is_timestep_even,
										states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
										box_access,
										prior_solver_index_mask,
										active_solver_mask,vrf_pas_factors,
										pas_factors,use_vrf_pas);
  }
#else
  if ( is_2D ) {
    d39_collect_advect_states_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,
									    is_timestep_even,
									    states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
									    box_access,
									    prior_solver_index_mask,
									    active_solver_mask,vrf_pas_factors,
									    pas_factors,use_vrf_pas);
  } else {
    d39_collect_advect_states_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,is_timestep_even,
									    states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
									    box_access,
									    prior_solver_index_mask,
									    active_solver_mask,vrf_pas_factors,
									    pas_factors,use_vrf_pas);
  }
#endif //BUILD_AVX2
#else //BUILD_D19_LATTICE
#if BUILD_AVX2 && !(BUILD_DOUBLE_PRECISION)
  if ( is_2D ) {
    d19_avx_collect_advect_states_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,
										is_timestep_even,
										states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
										box_access,
										prior_solver_index_mask,
										active_solver_mask,vrf_pas_factors,
										pas_factors,use_vrf_pas);
  } else {
    d19_avx_collect_advect_states_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,is_timestep_even,
										states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
										box_access,
										prior_solver_index_mask,
										active_solver_mask,vrf_pas_factors,
										pas_factors,use_vrf_pas);
  }
#else
  if ( is_2D ) {
    d19_collect_advect_states_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,
									    is_timestep_even,
									    states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
									    box_access,
									    prior_solver_index_mask,
									    active_solver_mask,vrf_pas_factors,
									    pas_factors,use_vrf_pas);
  } else {
    d19_collect_advect_states_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,HAS_S2V>(pde_advect_ublk,active_nbrs,is_timestep_even,
									    states,states_t,states_mc,states_uds,p_states,p_states_t,p_states_mc,p_states_uds,
									    box_access,
									    prior_solver_index_mask,
									    active_solver_mask,vrf_pas_factors,
									    pas_factors,use_vrf_pas);
  }
#endif
#endif
  // The following is only done for farblks since states_clear is set for nearblks in S2V
  if (!pde_advect_ublk->are_states_clear()) {
    ccDOTIMES(v, ubFLOAT::N_VOXELS) {
      states[V_0_0_0][v] = p_states[V_0_0_0][v];
      if (ADVECT_TEMP) {
        states_t[V_0_0_0][v] = p_states_t[V_0_0_0][v];
      }
      if (ADVECT_UDS) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
	  states_uds[nth_uds][V_0_0_0][v] = p_states_uds[nth_uds][V_0_0_0][v];
	}
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        states_mc[V_0_0_0][v] = p_states_mc[V_0_0_0][v];
      }
#endif
    }
    pde_advect_ublk->set_states_clear();  // Actually not needed since it will be unset after dynamics and the flag is never used 
                                          // before that.
  }  
  pde_advect_ublk->toggle_advected();
}

//instantiate gather_advect functions
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , TRUE , TRUE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , TRUE , TRUE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , TRUE, FALSE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , TRUE , FALSE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, TRUE , TRUE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, TRUE , TRUE, TRUE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, TRUE , FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, TRUE , FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , FALSE, TRUE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , FALSE, TRUE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , FALSE, FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , FALSE, FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, FALSE, TRUE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, FALSE, TRUE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, FALSE, FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, FALSE, FALSE, TRUE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

//--------------
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , TRUE , TRUE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , TRUE , TRUE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , TRUE, FALSE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , TRUE , FALSE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, TRUE , TRUE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, TRUE , TRUE, FALSE >(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, TRUE , FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, TRUE , FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , FALSE, TRUE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , FALSE, TRUE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , TRUE , FALSE, FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, TRUE , FALSE, FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, FALSE, TRUE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, FALSE, TRUE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<TRUE , FALSE, FALSE, FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_for_unsplit_neighbors<FALSE, FALSE, FALSE, FALSE, FALSE>(
                                                 sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 UBLK (&active_nbrs) [N_MOVING_STATES],
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
	 
}//ADVECT_NAMESPACE

//For now no need to build this function for AVX2
#if !BUILD_AVX2
template <BOOLEAN IS_VR_FINE, BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
VOID gather_advect(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
                   BOOLEAN is_timestep_even,
                   SOLVER_INDEX_MASK prior_solver_index_mask,
                   ACTIVE_SOLVER_MASK active_solver_mask)
{

  const auto& box_access = pde_advect_ublk->m_box_access;
  const sSPLIT_ADVECT_FACTORS* split_advect_factors = pde_advect_ublk->get_split_advect_factors();

  STP_GEOM_VARIABLE (*pas_factors)[ubFLOAT::N_VOXELS];
  if (pde_advect_ublk->is_near_surface()) {
    pas_factors = (STP_GEOM_VARIABLE (*)[ubFLOAT::N_VOXELS])
                   pde_advect_ublk->surf_lb_data()->post_advect_scale_factors;
  } else {
    pas_factors = unity_pas_factors;
  }

  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = (1 ^ prev_lb_index);

  asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_t_index = (1 ^ prev_t_index);

  asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);
  asINT32 curr_uds_index = (1 ^ prev_uds_index);

  VOXEL_STATE (*states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(curr_lb_index)->m_states);
  VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(prev_lb_index)->m_states);

  VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS] = NULL;

  VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};

  if (ADVECT_TEMP) {
    states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t);
    p_states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t);
  }

  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds);
      p_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds);
    }
  }

  VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS] = NULL;
#if BUILD_5G_LATTICE
  if (g_is_multi_component) {
    states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc);
    p_states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc);
  }
#endif

  STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS];
  BOOLEAN use_vrf_pas = FALSE;
  if (IS_VR_FINE && pde_advect_ublk->is_near_surface()) {
    // TODO This has to be written correctly during initialization.
    // If a VRFINE voxel does not interact with any surfel during ES2V
    // phase, the pas factors for that voxel should be 1 during even
    // time steps. This will work even if pas factor is 0 because the
    // fluid connectivity check should not allow contributions from
    // such neighbors.
    if (is_timestep_even) {
      sVR_FINE_INTERFACE_DATA *vr_fine_data = pde_advect_ublk->vr_fine_data();
      ccDOTIMES(latvec, N_MOVING_STATES) {
        // If there is no contribution during ES2V for VR fine ublk along a
        // particular latvec, the pas_factor should be forced to 1 on even
        // time steps.
        // However pas_factor should not be forced to 1 on odd time steps,
        // since the VR coarse coalesced states get the S2V contribution from
        // the surfels and V2V contribution via coalescing of VR fine voxels
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          sdFLOAT pas_factor = pas_factors[latvec][voxel];
          BOOLEAN no_even_s2v = !vr_fine_data->m_even_s2v_weights_mask[latvec].test(voxel);
          if (!((no_even_s2v) && (pas_factor > 0))) {
            vrf_pas_factors[latvec][voxel] = pas_factor;
          } else {
            vrf_pas_factors[latvec][voxel] = 1.0;
#if ENABLE_CONSISTENCY_CHECKS
            //TODO This check should go away after regression tests run successfully.
            if ((states[latvec][voxel] > 0) || (pas_factor == 0)) {
              msg_internal_error("VR fine %d states should be 0 l %d v %d state %e",
                                 pde_advect_ublk->id(), latvec, voxel, states[latvec][voxel]);
            }
#endif
          }
        }
      }
      use_vrf_pas = TRUE;
    }
  }

  BOOLEAN HAS_S2V = pde_advect_ublk->are_states_clear();

#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
  if (is_2D) {
    if (HAS_S2V) {
      d19_split_advect_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,TRUE>(pde_advect_ublk, split_advect_factors,
                                                       states,states_t,states_mc,states_uds,
                                                       p_states,p_states_t,p_states_mc,p_states_uds,
                                                       box_access,
                                                       prior_solver_index_mask, active_solver_mask,
                                                       vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    } else {
      d19_split_advect_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,FALSE>(pde_advect_ublk, split_advect_factors,
                                                        states,states_t,states_mc,states_uds,
                                                        p_states,p_states_t,p_states_mc,p_states_uds,
                                                        box_access,
                                                        prior_solver_index_mask, active_solver_mask,
                                                        vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    }
  } else {
    if (HAS_S2V) {
      d19_split_advect_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,TRUE>(pde_advect_ublk, split_advect_factors,
                                                       states,states_t,states_mc,states_uds,
                                                       p_states,p_states_t,p_states_mc,p_states_uds,
                                                       box_access,
                                                       prior_solver_index_mask, active_solver_mask,
                                                       vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    } else {
      d19_split_advect_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,FALSE>(pde_advect_ublk, split_advect_factors,
                                                        states,states_t,states_mc,states_uds,
                                                        p_states,p_states_t,p_states_mc,p_states_uds,
                                                        box_access,
                                                        prior_solver_index_mask, active_solver_mask,
                                                        vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    }
  }
#else
  if (is_2D) {
    if (HAS_S2V) {
      d39_split_advect_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,TRUE>(pde_advect_ublk, split_advect_factors,
                                                       states,states_t,states_mc,states_uds,
                                                       p_states,p_states_t,p_states_mc,p_states_uds,
                                                       box_access,
                                                       prior_solver_index_mask, active_solver_mask,
                                                       vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    } else {
      d39_split_advect_2D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,FALSE>(pde_advect_ublk, split_advect_factors,
                                                        states,states_t,states_mc,states_uds,
                                                        p_states,p_states_t,p_states_mc,p_states_uds,
                                                        box_access,
                                                        prior_solver_index_mask, active_solver_mask,
                                                        vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    }
  } else {
    if (HAS_S2V) {
      d39_split_advect_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,TRUE>(pde_advect_ublk, split_advect_factors,
                                                       states,states_t,states_mc,states_uds,
                                                       p_states,p_states_t,p_states_mc,p_states_uds,
                                                       box_access,
                                                       prior_solver_index_mask, active_solver_mask,
                                                       vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    } else {
      d39_split_advect_3D<IS_VR_FINE,ADVECT_TEMP,ADVECT_UDS,FALSE>(pde_advect_ublk, split_advect_factors,
                                                        states,states_t,states_mc,states_uds,
                                                        p_states,p_states_t,p_states_mc,p_states_uds,
                                                        box_access,
                                                        prior_solver_index_mask, active_solver_mask,
                                                        vrf_pas_factors, pas_factors, use_vrf_pas, is_timestep_even);
    }
  }
#endif
  if (!pde_advect_ublk->are_states_clear()) {
    ccDOTIMES(v, ubFLOAT::N_VOXELS) {
      states[V_0_0_0][v] = p_states[V_0_0_0][v];
      if (ADVECT_TEMP) {
        states_t[V_0_0_0][v] = p_states_t[V_0_0_0][v];
      }
      if (ADVECT_UDS) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  states_uds[nth_uds][V_0_0_0][v] = p_states_uds[nth_uds][V_0_0_0][v];
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        states_mc[V_0_0_0][v] = p_states_mc[V_0_0_0][v];
      }
#endif
    }
    pde_advect_ublk->set_states_clear();  // Actually not needed since it will be unset after dynamics and the flag is never used 
                                          // before that.
  }

  pde_advect_ublk->toggle_advected();
}

template VOID gather_advect<TRUE , TRUE , TRUE, TRUE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
	 
template VOID gather_advect<FALSE, TRUE , TRUE, TRUE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , FALSE, FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, FALSE, FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , TRUE , FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, TRUE , FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , FALSE, TRUE, TRUE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, FALSE, TRUE, TRUE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

//-----

template VOID gather_advect<TRUE , TRUE , TRUE, FALSE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
	 
template VOID gather_advect<FALSE, TRUE , TRUE, FALSE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , FALSE, FALSE, FALSE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, FALSE, FALSE, FALSE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , TRUE , FALSE, FALSE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, TRUE , FALSE, FALSE>(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

template VOID gather_advect<TRUE , FALSE, TRUE, FALSE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect<FALSE, FALSE, TRUE, FALSE >(sUBLK *pde_advect_ublk,
                                                 VOXEL_MASK_8 voxel_mask,
                                                 BOOLEAN is_timestep_even,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask);

#endif //!BUILD_AVX2
