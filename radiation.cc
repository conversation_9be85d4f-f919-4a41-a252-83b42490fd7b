
#include "radiation_comm.h"
#include "radiation.h"
#include "sim.h"
#include "surfel.h"

#include PHYSICS_H

#include <map>

cRADIATION_PATCHES g_radiation_patches;

class sRADIATION_PATCH_FSET;

class cRADIATION_PATCHES_IMPL 
{
private:

  friend class cRADIATION_PATCHES;

  struct sPROTO_PATCH
  {
    std::vector<sRAD_PATCH_SURFEL_PTR> surfels;
    bool emissivity_is_time_varying = false;
    sPROTO_PATCH() = default;

    sRADIATION_PATCH_GROUP::eREALM realm() const
    {
      if (surfels.size() == 0) {
        msg_internal_error("Called on patch with 0 surfels!");
      }

      if (surfels[0].ptr()->is_conduction_surfel()) {
        return sRADIATION_PATCH_GROUP::eREALM::CONDUCTION;
      } else {
        return sRADIATION_PATCH_GROUP::eREALM::FLOW;
      }
    }
  };

  cRADIATION_PATCHES_IMPL(STP_PROC num_rps) : m_num_rps(num_rps), 
                                              m_neighbor_rp_map( new STP_PROC[num_rps] )
  {
    for (int i=0; i<num_rps; i++) {
      m_neighbor_rp_map[i] = PROC_INVALID;
    }
  }

  void init();

  void register_surfel(sSURFEL* surfel, SHOB_ID patch_id, RP_PROC rp, bool backside);

  void do_initial_rp_comm();

  void build_radiation_fset(sRADIATION_PATCH_FSET& send_fset);

  const STP_PROC m_num_rps; 
  std::unique_ptr<STP_PROC[]> m_neighbor_rp_map;

  // using std::map so the patches are iterated in order
  // RP -> scales -> patches -> surfels
  std::vector<std::vector<std::map<SHOB_ID,sPROTO_PATCH>>> m_patch_to_surfel;

};

// necessary for the pimpl idiom & unique_ptr
cRADIATION_PATCHES::cRADIATION_PATCHES() = default;
cRADIATION_PATCHES::~cRADIATION_PATCHES() = default;

void cRADIATION_PATCHES_IMPL::register_surfel(sSURFEL* surfel, SHOB_ID patch_id, RP_PROC rp, bool backside)
{
  cassert(rp != RP_PROC_INVALID);

  auto& rp_map = [&]() -> std::vector<std::map<SHOB_ID, sPROTO_PATCH>>& {
    STP_PROC& map_idx = m_neighbor_rp_map[static_cast<STP_PROC>(rp)];
    if (map_idx == PROC_INVALID) {
      map_idx = m_patch_to_surfel.size();
      return m_patch_to_surfel.emplace_back(sim.num_scales); // add a new RP vector with num scales
    } else {
      return m_patch_to_surfel.at(map_idx);
    }
  }();

  sRAD_PATCH_SURFEL_PTR tagged_ptr(surfel);

  if (backside) {
    tagged_ptr.set<sRAD_PATCH_SURFEL_PTR_TRAITS::BACKSIDE>(true);
  }

  rp_map[surfel->scale()][patch_id].surfels.push_back(tagged_ptr);

}

// This bunch of code detects if the parameters has a member called "radiation_surface_condition"
// using SFINAE
template<typename P> 
using has_radiation_surface_condition_t = decltype(std::declval<P>().radiation_surface_condition);

template <typename P, typename = std::void_t<>>
struct has_radiation_surface_condition : std::false_type{};

template <typename P>
struct has_radiation_surface_condition<P, std::void_t<has_radiation_surface_condition_t<P>>> : std::true_type{};

template <typename P>
static constexpr bool has_radiation_surface_condition_v = has_radiation_surface_condition<P>::value;

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_EMISSIVITY_IS_TIME_VARYING : public tSURFEL_DYN_TYPE_TRAITS_BASE<E,SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data,
                   bool& emissivity_is_time_varying) {
    // we can't use the radiation_data()->surface_condition pointer because it hasn't been set yet (happens in seeding)
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    [[maybe_unused]] auto *parms = derived_dynamics_data->physics_descriptor()->parameters();
    if constexpr( has_radiation_surface_condition_v<decltype(*parms)> ) {
      emissivity_is_time_varying = parms->radiation_surface_condition.is_time_varying;
    } else if ( is_inlet_outlet_type<E>() ) {
      emissivity_is_time_varying = false;
    } else {
      msg_internal_error("This was called on a non-radiation surfel type!");
    }
  }
};

void cRADIATION_PATCHES_IMPL::do_initial_rp_comm()
{
#if !BUILD_FOR_SIMSIZES

  std::vector<uint64_t> patches;
  std::vector<uint64_t> scale_send_buffer(sim.num_scales,0);

  // send patch counts for each rp & scale
  for (STP_PROC rp = 0; rp < m_num_rps; rp++) {
    STP_PROC rp_idx = m_neighbor_rp_map[rp];
    std::fill(scale_send_buffer.begin(), scale_send_buffer.end(), 0);

    if (rp_idx != PROC_INVALID) {
      for (int scale = 0; scale < sim.num_scales; scale++) {
        scale_send_buffer[scale] = m_patch_to_surfel[rp_idx][scale].size(); // how many patches to this RP for this scale
      }
    }

    // use a sync send to prevent overwhelming MPI with lots of small messages
    MPI_Ssend(scale_send_buffer.data(), sim.num_scales, MPI_UINT64_T, rp_proc_to_stp_proc(RP_PROC(rp)), eMPI_SP_TO_RP_INIT_TAG, MPI_COMM_WORLD);
  }

  {
    int val = 0;
    MPI_Type_size(MPI_INT, &val);
    if (sizeof(STP_SHOB_ID) != val) {
      msg_internal_error("The sizes do not match!");
    }
  }

  std::vector<STP_SHOB_ID> patch_ids;
  std::vector<STP_SHOB_ID> time_varying_patches;
  // send patch ids for each rp & scale
  for (STP_PROC rp = 0; rp < m_num_rps; rp++) {
    STP_PROC rp_idx = m_neighbor_rp_map[rp];

    if (rp_idx != PROC_INVALID) {
      for (int scale = 0; scale < sim.num_scales; scale++) {
        uint64_t num_patches = m_patch_to_surfel[rp_idx][scale].size();
        if (num_patches == 0) {
          continue;
        }

        int realm = [&]() {
          if ( m_patch_to_surfel[rp_idx][scale].begin()->second.realm() == sRADIATION_PATCH_GROUP::eREALM::CONDUCTION ) {
            return STP_COND_REALM;
          } else {
            return STP_FLOW_REALM;
          }
        }();

        MPI_Ssend(&realm, 1, MPI_INT, rp_proc_to_stp_proc(RP_PROC(rp)), eMPI_SP_TO_RP_INIT_TAG, MPI_COMM_WORLD);

        patch_ids.clear();
        patch_ids.reserve(num_patches);
        time_varying_patches.clear();

        for ( auto& [patch_id, proto_patch] : m_patch_to_surfel[rp_idx][scale] ) {
          patch_ids.push_back(patch_id);

          // This works because all surfels in a patch should have the same surface condition
          // How to adapt this for front/back differences?
          auto * dyn_data = proto_patch.surfels.at(0).ptr()->dynamics_data();

          exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_EMISSIVITY_IS_TIME_VARYING>(dyn_data, proto_patch.emissivity_is_time_varying);

          if (proto_patch.emissivity_is_time_varying) {
            time_varying_patches.push_back(patch_id);
          }
        }

        MPI_Ssend(patch_ids.data(), patch_ids.size(), MPI_INT, rp_proc_to_stp_proc(RP_PROC(rp)), eMPI_SP_TO_RP_INIT_TAG, MPI_COMM_WORLD);
        MPI_Ssend(time_varying_patches.data(), time_varying_patches.size(), MPI_INT, rp_proc_to_stp_proc(RP_PROC(rp)), eMPI_SP_TO_RP_INIT_TAG, MPI_COMM_WORLD);
      }
    }
  }
#endif
}

void cRADIATION_PATCHES_IMPL::build_radiation_fset(sRADIATION_PATCH_FSET& send_fset)
{

  // This dummy call will cause send_fset to allocate the scale array, just in case
  send_fset.get_groups_of_scale(0);

  for (STP_PROC rp = 0; rp < m_num_rps; rp++) {
    STP_PROC rp_idx = m_neighbor_rp_map[rp];

    if (rp_idx != PROC_INVALID) {
      for (SCALE scale=0; scale < sim.num_scales; scale++) {
        auto& map = m_patch_to_surfel[rp_idx][scale];
        if (map.size() == 0) {
          continue;
        }

        for (auto& [patch_id, proto_patch]: map) {
          auto realm = proto_patch.realm();
          sRADIATION_PATCH_GROUP * patch_group = send_fset.create_group(scale, RP_PROC(rp), realm);

          // move the surfels out, because we don't need them in the map anymore
          patch_group->add_patch(std::move(proto_patch.surfels), proto_patch.emissivity_is_time_varying); 
        }
      }
    }
  }
}

void cRADIATION_PATCHES::init(STP_PROC num_rps)
{
  m_pimpl = std::unique_ptr<cRADIATION_PATCHES_IMPL> (new cRADIATION_PATCHES_IMPL(num_rps) );
}

void cRADIATION_PATCHES::register_surfel(sSURFEL* surfel, SHOB_ID patch_id, RP_PROC rp, bool backside)
{
  cassert(m_pimpl);
  m_pimpl->register_surfel(surfel, patch_id, rp, backside);
}

void cRADIATION_PATCHES::do_initial_rp_comm()
{
  if (sim.is_radiation_model) {
    cassert(m_pimpl);
    m_pimpl->do_initial_rp_comm();
  }
}

void cRADIATION_PATCHES::build_radiation_fset(sRADIATION_PATCH_FSET& send_fset)
{
  if (sim.is_radiation_model) {
    cassert(m_pimpl);
    m_pimpl->build_radiation_fset(send_fset);
    m_pimpl.reset(); // throw away everything else, we are done
  }
}



