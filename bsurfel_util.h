/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Various utility classes/functions for working with bsurfels
 *
 * Dalon Work, Exa Corporation
 * Created Sep 28, 2017
 *--------------------------------------------------------------------------*/
#ifndef BSURFEL_UTIL_H_
#define BSURFEL_UTIL_H_

#include "common_sp.h"
#include "bsurfel.h"
#include "ublk.h"
#include "ublk_box_tree.h"

#define BSURFEL_MAINLINE


//-----------------------------------------------------------
// sBUSRFEL_UTIL_BASE STRUCT
//
// This struct contains functions common between the util classes
//-----------------------------------------------------------

struct sBSURFEL_UTIL_BASE {

  int depth;

  sBSURFEL_UTIL_BASE() : depth(0) {};

  // internal RAII counter to limit recursion depth
  struct sDEPTH_COUNTER {
    sBSURFEL_UTIL_BASE * m_base;

    sDEPTH_COUNTER(sBSURFEL_UTIL_BASE *base) : m_base(base)
    {
      m_base->depth++;
      if (m_base->depth == 10) {
        msg_internal_error("Bsurfel search algorithm max depth reached!");
      }
    }

    // allow moving, but no copying
    sDEPTH_COUNTER(const sDEPTH_COUNTER&) = delete;
    sDEPTH_COUNTER& operator = (const sDEPTH_COUNTER&) = delete;
    sDEPTH_COUNTER(sDEPTH_COUNTER&&) = default;
    sDEPTH_COUNTER& operator = (sDEPTH_COUNTER&&) = default;

    ~sDEPTH_COUNTER()
    {
      m_base->depth--;
    }
  };
};

/** Implements the algorithm for determining which ublk & voxel a bsurfel
 * centroid or a particle sim parcel centroid lies in. Both are points in
 * nature, And both are free to move irrespective of the underlying grid.
 * Bsurfels are allowed to move through solid ublks, but parcels are not.
 *
 * The algorithm works by computing the distance between the ublk centroid and
 * the bsurfel centroid, in lattice coords, and using this information to walk
 * the grid. The algorithm requires the old home ublk and the new location of
 * its centroid.  The algorithm then walks the grid, accounting for the
 * different types of ublks that can be encountered, until a satisfactory ublk
 * and voxel is encountered.
 */

struct sHOME_UBLK_LOCATOR : public sBSURFEL_UTIL_BASE {
private:
  STP_DGEOM_VARIABLE m_location[3];
  STP_DGEOM_VARIABLE m_p_location[3];
  UBLK m_home_ublk;
  sINT8 m_home_voxel;
  bool  m_for_bsurfels;
  bool  m_parcel_location_modified;
  bool m_error_detected;
  // previous_location only used by parcels

public:
  sHOME_UBLK_LOCATOR(UBLK ublk, sINT8 home_voxel, STP_DGEOM_VARIABLE centroid[3], bool for_bsurfels,
                     STP_DGEOM_VARIABLE p_location[3] = nullptr);

  INLINE
  UBLK ublk() { return m_home_ublk; }

  INLINE
  uINT8 voxel()
  {
    return m_home_voxel;
  }

  static uINT8 locate_voxel(const STP_DGEOM_VARIABLE ublk_offset[3]);
  static bool calc_centroid_offset_from_ublk(UBLK ublk, const dFLOAT loc[3], dFLOAT ublk_offset[3]);
  bool bsurfel_is_inside_ublk_box(sUBLK_BOX_HEADER_BASE* ublk_box_header, const dFLOAT loc[3]);
  bool bsurfel_is_inside_ublk_in_box(sUBLK_BOX_HEADER_BASE* ublk_box_header, const dFLOAT loc[3]);

  INLINE
  bool parcel_location_modified() { return m_parcel_location_modified;}

  bool error_detected() { return m_error_detected;}

  STP_DGEOM_VARIABLE * location()
  {
    return m_location;
  }

  STP_DGEOM_VARIABLE * p_location()
  {
    return m_p_location;
  }

  void locate_unpacked_bsurfel();
  STP_PROC locate();

  /** If something goes wrong, throw this */
  struct SearchError;


  private:

  uINT8 locate_voxel_and_check_inside(UBLK ublk);

  STP_PROC locate_home_box_sp_for_void_bsurfel();
  TAGGED_UBLK locate_home_ublk_in_same_sp();
  TAGGED_UBLK check_t_ublk_for_scale_interface(TAGGED_UBLK t_ublk);
  std::pair<UBLK, uINT8> locate_new_home_ublk_from_ublk(UBLK ublk);
  std::pair<UBLK, uINT8> locate_new_home_ublk_in_split_ublk(sUBLK_VECTOR * split_ublk_vector, sINT8 voxel);
  UBLK locate_new_home_ublk_in_split_ublk_for_parcels(sUBLK_VECTOR * split_ublk_vector);
  void truncate_ublk_offset(const dFLOAT ublk_offset[3], sINT64 trunc_offset[3]);
  STP_LATVEC_INDEX latvec_from_displacement();
  UBLK locate_new_home_ublk_from_multiple_split_ublks(sINT32 n_hops);
  void process_periodic_boundaries(UBLK ublk);
  void process_periodic_boundaries(UBLK ublk, int crossed_face);
};

struct sHOME_UBLK_LOCATOR::SearchError : public std::exception
{
  std::string m_msg;
  SearchError(const std::string& msg) {
    m_msg = msg;
  }

  virtual const char * what() const noexcept
  {
    return m_msg.c_str();
  }
};

template<typename T>
inline sHOME_UBLK_LOCATOR::SearchError& operator << (sHOME_UBLK_LOCATOR::SearchError &err, T const& val)
{
  std::ostringstream s;
  s << val;
  err.m_msg += s.str();
  return err;
}

inline sHOME_UBLK_LOCATOR::SearchError& operator << (sHOME_UBLK_LOCATOR::SearchError &err, const char *T)
{
  err.m_msg += T;
  return err;
}

inline sHOME_UBLK_LOCATOR::SearchError& operator << (sHOME_UBLK_LOCATOR::SearchError &err, char T)
{
  err.m_msg.push_back(T);
  return err;
}


//-----------------------------------------------------------
// sSET_VALID_BSURFEL_NEIGHBOR_UBLK STRUCT
//
// This struct takes the newly found home ublk and voxel
// for a bsurfel centroid, and proceeds to turn on all
// valid neighboring ublks for the following timestep. Doing
// this limits the number of ublks that get processed in the
// A and B strands, while providing a correct numerical method.
//
// It is possible that not all of the neighbors that are turned
// on by this algorithm will actually get used by the bsurfels,
// but it is the maximum set of ublks that could possibly be
// touched by the bsurfels for a given timestep.
//
// The actual neighbors are set by sBSURFEL_NEIGHBOR_UBLK_LOCATOR.
//
// This algorithm also computes and stores the group that the bsurfel
// should be located in this timestep.
//-----------------------------------------------------------

struct sSET_VALID_BSURFEL_NEIGHBOR_UBLKS : public sBSURFEL_UTIL_BASE {

  static const int MAX_NEIGHBORS = 8;

private:
  uINT8 m_home_voxel;
  BSURFEL_GROUP_TYPE m_group;
  bool m_check_all_ublks_are_fringe;

public:

  sSET_VALID_BSURFEL_NEIGHBOR_UBLKS(uINT8 home_voxel, bool check_all_ublks_are_fringe = false) :
    m_home_voxel(home_voxel),
    m_group(N_BSURFEL_GROUP_TYPES),
    m_check_all_ublks_are_fringe(check_all_ublks_are_fringe) { }

  // sINT16 * get_offset(uINT8 index) {
  //   dassert(index < MAX_NEIGHBORS);
  //   return m_valid_ublks[index];
  // }

  void set_bsurfel_interacting_on_neighbor_ublks(UBLK home_ublk);

  BSURFEL_GROUP_TYPE group_type()
  {
    dassert(m_group != N_BSURFEL_GROUP_TYPES);
    return m_group;
  }

private:

  void set_bsurfel_interacting_on_tagged_ublk(TAGGED_UBLK t_ublk, VOXEL_MASK_8 mask);
  void set_bsurfel_interacting_on_ublk(UBLK ublk);
  void set_bsurfel_interacting_on_split_ublk(sUBLK_VECTOR * split_ublk_vector);
  void set_bsurfel_interacting_on_scale_interface(sSCALE_BOX_INTERFACE *interface, VOXEL_MASK_8 mask);
  void set_bsurfel_group_type(UBLK ublk);

};


//-----------------------------------------------------------
// sBSURFEL_NEIGHBOR_UBLK_LOCATOR STRUCT
//
// This struct contains the algorithm used in finding neighbor
// ublks for a given bsurfel. It takes a reference to a
// vector of sBSURFEL_UBLK_NEIGHBOR_INFO structs, which is assumed
// empty at the start of the run. As the algorithm finds neighbors
// and determines the info for them, they are added to the vector.
//
//-----------------------------------------------------------

struct sNEIGHBOR_UBLK_LOCATOR : public sBSURFEL_UTIL_BASE {

protected:
  UBLK m_home_ublk;
  uINT8 m_home_voxel;

public:
  // skip tridiagonal neighbors like mainline for bsurfels
  bool  m_skip_triple_diagonal;

  sNEIGHBOR_UBLK_LOCATOR(UBLK ublk, uINT8 voxel): m_home_ublk(ublk), m_home_voxel(voxel) {}
  virtual void add_neighboring_ublks();
  void add_neighbor_ublks_from_scale_interface(sSCALE_BOX_INTERFACE *interface, VOXEL_MASK_8 mask);
  void add_neighbor_ublks_from_tagged_ublk(TAGGED_UBLK t_ublk, VOXEL_MASK_8 mask);
  void add_neighbor_ublks_from_split_ublk(sUBLK_VECTOR * split_ublk_vector, VOXEL_MASK_8 mask);
  virtual void add_neighbor_ublk(UBLK ublk, VOXEL_MASK_8 mask) = 0;
};

template <typename SURFEL_TYPE>
struct sSURFEL_NEIGHBOR_UBLK_LOCATOR : public sNEIGHBOR_UBLK_LOCATOR {

  SURFEL_TYPE m_surfel;
 sSURFEL_NEIGHBOR_UBLK_LOCATOR(SURFEL_TYPE surfel, UBLK ublk, uINT8 voxel) :
  m_surfel(surfel),
    sNEIGHBOR_UBLK_LOCATOR(ublk, voxel)
  {
    m_skip_triple_diagonal = false;
  }

  void add_neighboring_ublks() final;
  void add_neighbor_ublks_internal(UBLK ublk, int voxel);
  void add_neighbor_ublk(UBLK ublk, VOXEL_MASK_8 voxel_mask) final {
    if (ublk->is_mirror() || ublk->is_solid())
      return;
    if (ublk->is_vr_fine()) {
      sVR_FINE_INTERFACE_DATA * vr_data = ublk->vr_fine_data();
      ublk = vr_data->vr_coarse_ublk().ublk();
      voxel_mask = VOXEL_MASK_8(0xFF);
      auto location = std::find(m_surfel->p_data()->neighbor_ublks.begin(), 
                                m_surfel->p_data()->neighbor_ublks.end(), ublk);
      
      if( location ==  m_surfel->p_data()->neighbor_ublks.end()) {
        m_surfel->p_data()->neighbor_ublks.push_back(ublk);
        m_surfel->p_data()->neighbor_voxel_masks.push_back(voxel_mask & ublk->fluid_like_voxel_mask);
      } else {
        int index = std::distance(m_surfel->p_data()->neighbor_ublks.begin() , location);
        m_surfel->p_data()->neighbor_voxel_masks[index] |= voxel_mask & ublk->fluid_like_voxel_mask;
      }
    } else {
      auto location = std::find(m_surfel->p_data()->neighbor_ublks.begin(), 
                                m_surfel->p_data()->neighbor_ublks.end(), ublk);
      
      if( location ==  m_surfel->p_data()->neighbor_ublks.end()) {
        m_surfel->p_data()->neighbor_ublks.push_back(ublk);
        m_surfel->p_data()->neighbor_voxel_masks.push_back(voxel_mask & ublk->fluid_like_voxel_mask);
      } else {
        int index = std::distance(m_surfel->p_data()->neighbor_ublks.begin() , location);
        m_surfel->p_data()->neighbor_voxel_masks[index] |= voxel_mask & ublk->fluid_like_voxel_mask;
      }
    }
  }
};

struct sBSURFEL_NEIGHBOR_UBLK_LOCATOR : public sNEIGHBOR_UBLK_LOCATOR {
private:
  sBSURFEL &m_bsurfel;
  std::vector<sBSURFEL_UBLK_NEIGHBOR_INFO> & m_neighbor_info;
  dFLOAT m_resolution;
  dFLOAT m_resolution_sqrd;
  dFLOAT m_one_over_resolution_sqrd;
  dFLOAT m_one_over_resolution_cubed;
  bool   m_set_ublk_interior; // when doing measurements, don't touch the ublks

public:
  sBSURFEL_NEIGHBOR_UBLK_LOCATOR(sBSURFEL &bsurfel,
                                 std::vector<sBSURFEL_UBLK_NEIGHBOR_INFO> & b_info, bool set_ublk_interior = true) :
    m_bsurfel(bsurfel), m_neighbor_info(b_info), m_set_ublk_interior(set_ublk_interior),
    sNEIGHBOR_UBLK_LOCATOR(bsurfel.home_ublk(), bsurfel.home_voxel())
  {
    recompute_resolution(m_bsurfel.home_ublk()->scale());
    m_skip_triple_diagonal = true;
  }

private:

  void recompute_resolution(sINT8 ublk_scale)
  {
    m_resolution = sim_scale_to_voxel_size(ublk_scale);
    m_resolution_sqrd = m_resolution * m_resolution;
    m_one_over_resolution_sqrd = 1.0F / m_resolution_sqrd;
    m_one_over_resolution_cubed = 1.0F / m_resolution_sqrd / (sim.is_2d() ? 1.0F : m_resolution);
  }
  virtual void add_neighbor_ublk(UBLK ublk, VOXEL_MASK_8 mask) final;
};

#endif
