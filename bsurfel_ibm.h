#ifndef BSURFEL_IBM_H
#define BSURFEL_IBM_H
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*/

#include "bsurfel.h"

// Computes the body force on the neighboring voxels
template<typename INFO>
VOID sBSURFEL::distribute_body_force(std::vector<INFO> & b_info)
{
  // now compute the body force distributed to the voxels
  for (auto& buni : b_info) {
    // LOG_MSG_IF(buni.ublk()->id() == 28681,"BSURFEL_CKPT",
    //           "ublk",buni.ublk()->id(),
    //           "bsurfel",id(),
    //           "voxel",buni.voxel(),
    //           LOG_TS).format("DISTRIBUTE\n"
    //                          "bf: {} {} {}\n",
    //                          m_computed_body_force[0],
    //                          m_computed_body_force[1],
    //                          m_computed_body_force[2]);
    
#if BUILD_D19_LATTICE
    dFLOAT computed_body_force_pfld[3] = {m_computed_body_force_pfld[0], m_computed_body_force_pfld[1],m_computed_body_force_pfld[2]}; 
#else
    dFLOAT computed_body_force_pfld[3] = {0};
#endif
    
    buni.add_bsurfel_contribution_to_ublk(m_computed_body_force, computed_body_force_pfld, m_area);
  }
}

// Computes the force on the bsurfel itself.
template<typename INFO>
VOID sBSURFEL::normalize_distributed_body_force(std::vector<INFO> &b_info, dFLOAT sampled_force_per_area[3])
{

  for (auto& buni : b_info) {
    dFLOAT normalizing_weight = buni.normalizing_weight();
    if (normalizing_weight != 0.0F) {
      normalizing_weight = 1.0 / normalizing_weight;
      dFLOAT voxel_ratio = buni.voxel_ratio(this->scale());
      dFLOAT voxel_volume = buni.voxel_volume(voxel_ratio);
      dFLOAT scale_diff = 1.0 / voxel_ratio;
      dFLOAT weight = buni.distribution_weight();
      dFLOAT pfluid = buni.pfluid();
#if BUILD_D19_LATTICE
      if (sim.is_pf_model){
	auto helper = buni.interpolate_fluid_properties_helper();
	dFLOAT phi = buni.uds_next_uds_value(helper, 0);
	if(phi<0.0) 
	  phi = 0.0;
	else if(phi>1.0) 
	  phi = 1.0;
	dFLOAT density = g_Dens_light + phi*(g_Dens_heavy - g_Dens_light);
	ccDOTIMES(axis, 3) 
	  sampled_force_per_area[axis] += weight * weight * m_computed_body_force_pfld[axis] * m_area * pfluid * voxel_volume *
	                                  density * scale_diff * normalizing_weight;
      } else
#endif
      {	
	ccDOTIMES(axis, 3) {
	  sampled_force_per_area[axis] += weight * weight * m_computed_body_force[axis] * pfluid * voxel_volume *
                                          buni.lb_next_density() * scale_diff * normalizing_weight;
	}
      }
    }

    // UBLK ublk = buni.ublk();
    // auto voxel = buni.voxel();
    // auto h = buni.interpolate_fluid_properties_helper();
    // LOG_MSG_IF(ublk->id() == 13771, "BSURFEL_COMM",LOG_FUNC,LOG_TS,"bsurfel",id(),"Ublk",ublk->id(),LOG_ATTR(voxel),"ghost",ublk->is_ghost())
    //   .format("density {} wvb {} Wv {} pfluid {} computed_body_force {} {} {}",
    //             ublk->lb_data()->m_lb_data[h.lb_next_index].density[voxel],
    //             buni.distribution_weight(),
    //             normalizing_weight,
    //             buni.pfluid(),
    //             m_computed_body_force[0],
    //             m_computed_body_force[1],
    //             m_computed_body_force[2]
    //             );

    // LOG_MSG_IF( ublk->id() == 5686 , "BSURFEL_COMM",LOG_TS,LOG_FUNC,"bsurfel",id(),"voxel",voxel,"nweight",normalizing_weight);
  }

  // area of bsurfels is in finest units, scaling it to be in bsurfel scale
  dFLOAT inv_scaled_area = ((uINT64) 1 << ((sim.num_dims - 1) * (sim.num_scales - this->scale() - 1))) / m_area;
  ccDOTIMES(axis, 3) {
    sampled_force_per_area[axis] *= inv_scaled_area;
  }

  // LOG_MSG_IF( id() == 3632, "BSURFEL_COMM",LOG_FUNC,LOG_TS).printf(
  //             "sampled_force %f %f %f inv_scaled_area %f",
  //             sampled_force_per_area[0],
  //             sampled_force_per_area[1],
  //             sampled_force_per_area[2],
  //             inv_scaled_area);

}

template<typename INFO>
VOID sBSURFEL::interpolate_fluid_properties(std::vector<INFO> &b_info)
{
  dFLOAT weight_sum = 0.0;

  m_sampled_fluid_velocity[0] = m_sampled_fluid_velocity[1] = m_sampled_fluid_velocity[2] = 0.0F;  
  m_sampled_fluid_density = m_sampled_fluid_temp = m_sampled_tke = m_sampled_eps = 0.0F;
#if BUILD_D19_LATTICE
  m_sampled_fluid_velocity_pfld[0] = m_sampled_fluid_velocity_pfld[1] = m_sampled_fluid_velocity_pfld[2] = 0.0F;
  m_sampled_fluid_pressure_pfld = 0.0F;
#endif
  ccDOTIMES(nth_uds, sim.n_user_defined_scalars) m_sampled_uds_value[nth_uds] = 0.0F;

  for (auto& buni : b_info) {
    UBLK ublk = buni.ublk();
    auto helper = buni.interpolate_fluid_properties_helper();
    sdFLOAT weight = buni.interpolation_weight();
    weight_sum += weight;

    LOG_MSG_IF( ublk->id() == 13771,"BSURFEL_COMM",
              "ublk",buni.ublk()->id(),
              "voxel",buni.voxel(),
              "ghost",buni.ublk()->is_ghost(),
              "bsurfel",this->id(),
              LOG_TS).format("INTERPOLATE\n"
              "lb_next_index {}\n"
              "lb_next_vel {} {} {}\n"
              "lb_prior_vel {} {} {}\n"
              "lb_next_density {}\n"
              "lb_prior_density {}\n"
              // "t_next_fluid_temp {}\n"
              "ke_prior_turb_ke {}\n"
              "ke_prior_turb_df {}\n",helper.lb_next_index,
                                      buni.lb_next_vel(helper,0),
                                      buni.lb_next_vel(helper,1),
                                      buni.lb_next_vel(helper,2),
                                      buni.lb_prior_vel(helper,0),
                                      buni.lb_prior_vel(helper,1),
                                      buni.lb_prior_vel(helper,2),
                                      buni.lb_next_density(helper),
                                      buni.lb_prior_density(helper),
                                      // buni.t_next_fluid_temp(helper),
                                      buni.ke_prior_turb_ke(helper),
                                      buni.ke_prior_turb_df(helper));

    // LOG_MSG_IF(ublk->id() == 1471 && voxel == 1,"BSURFEL_COMM",LOG_FUNC,"TS",g_timescale.m_time,"B", id()).printf(
    //             "density %f vel %f %f %f",
    //             ublk->lb_data()->m_lb_data[lb_next_index].density[voxel],
    //             ublk->lb_data()->m_lb_data[lb_next_index].vel[0][voxel],
    //             ublk->lb_data()->m_lb_data[lb_next_index].vel[1][voxel],
    //             ublk->lb_data()->m_lb_data[lb_next_index].vel[2][voxel]);

    ccDOTIMES(axis, 3) {
      m_sampled_fluid_velocity[axis] += buni.lb_next_vel(helper, axis) * weight;
    }
    m_sampled_fluid_density += buni.lb_next_density(helper) * weight;
    if (sim.is_heat_transfer) {
      m_sampled_fluid_temp += buni.t_next_fluid_temp(helper) * weight;
    }
    if (sim.is_turb_model) {
      m_sampled_tke += buni.ke_prior_turb_ke(helper) * weight;
      m_sampled_eps += buni.ke_prior_turb_df(helper) * weight;
    }
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
        m_sampled_uds_value[nth_uds] += buni.uds_next_uds_value(helper, nth_uds) * weight;
#if BUILD_D19_LATTICE
      if(sim.is_pf_model) {
	m_sampled_fluid_pressure_pfld += buni.pf_prior_pressure_pfld(helper) * weight;
	ccDOTIMES(axis, 3)
	  m_sampled_fluid_velocity_pfld[axis] += buni.pf_next_vel(helper, axis) * weight;
      }
#endif
    }
  }

  if (weight_sum > 0.0) {
    dFLOAT weight_sum_inv = 1.0 / weight_sum;
    ccDOTIMES(axis, 3) {
      m_sampled_fluid_velocity[axis] *= weight_sum_inv;
    }
    m_sampled_fluid_density *= weight_sum_inv;
    if (sim.is_heat_transfer) {
      m_sampled_fluid_temp *= weight_sum_inv;
    }
    if (sim.is_turb_model) {
      m_sampled_tke *= weight_sum_inv;
      m_sampled_eps *= weight_sum_inv;
    }
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
        m_sampled_uds_value[nth_uds] *= weight_sum_inv;
#if BUILD_D19_LATTICE
      if(sim.is_pf_model) {
	m_sampled_fluid_pressure_pfld *= weight_sum_inv;
	ccDOTIMES(axis, 3) 
	  m_sampled_fluid_velocity_pfld[axis] *= weight_sum_inv;
      }
#endif
    }
  }
}

VOID sBSURFEL::compute_body_force()
{
  const bool is_slip = sim.is_turb_model || (g_pf_ibm_slip && sim.is_pf_model);
  // apply the specified BC type (free-slip or no-slip)
  // normal body force is  2.0 * (body velocity - interpolated normal fluid velocity)
  if (is_slip) { // for frictionless walls, keep only the normal velocity component
    dFLOAT fluid_norm_vel_mag, bsurfel_norm_vel_mag;
    fluid_norm_vel_mag = vdot(m_sampled_fluid_velocity, m_normal);
    bsurfel_norm_vel_mag = vdot(m_vel, m_normal);
    ccDOTIMES(axis, 3) {
      m_computed_body_force[axis] = m_normal[axis] * (2.0f * (bsurfel_norm_vel_mag - fluid_norm_vel_mag));
    }
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      dFLOAT fluid_norm_vel_mag_pfld = vdot(m_sampled_fluid_velocity_pfld, m_normal);
      ccDOTIMES(axis, 3)
	m_computed_body_force_pfld[axis] = m_normal[axis] * (2.0f * (bsurfel_norm_vel_mag - fluid_norm_vel_mag_pfld));
    }
#endif
    // LOG_MSG_IF(id() == 3632,"BSURFEL_COMM",LOG_FUNC,LOG_TS).printf("C %e %e %e BF %e %e %e",
    //           m_centroid[0], m_centroid[1], m_centroid[2],
    //           m_computed_body_force[0],
    //           m_computed_body_force[1],
    //           m_computed_body_force[2]);
  }
  else {
    ccDOTIMES(axis, 3) {
      m_computed_body_force[axis] = 2.0f * (m_vel[axis] - m_sampled_fluid_velocity[axis]);
    }
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      ccDOTIMES(axis, 3)
	m_computed_body_force_pfld[axis] = 2.0f * (m_vel[axis] - m_sampled_fluid_velocity_pfld[axis]);
    }
#endif
  }
#if BUILD_D19_LATTICE
  if(sim.is_pf_model && g_pf_ibm_phi_bbk == 7){   
    vadd(m_computed_body_force_pfld, m_computed_body_force_pfld, m_centrifugal_force);
  }
#endif
}
#endif
