#ifndef EXA_SIMENG_STRAND_ENUM_H
#define EXA_SIMENG_STRAND_ENUM_H

enum STRAND {
  NO_STRAND           = -1,
  SLIDING_SURF_A_STRAND,            // 0
  SLIDING_SURF_B_STRAND,            // 1
  INTERIOR_FILM_ACCUMULATION_STRAND,// 2
  INTERIOR_FILM_KINEMATICS_STRAND,  // 3
  FRINGE_WSURF_SAMPLING_STRAND,     // 4
  INTERIOR_WSURF_SAMPLING_STRAND,   // 5
  FRINGE2_SURF_STRAND,              // 6
  FRINGE2_WSURF_HFC_STRAND,         // 7
  FRINGE_SURF_STRAND,               // 8
  SLIDING_SURF_C_STRAND,            // 9
  FRINGE_NEARBLK_A_STRAND,          // 10
  FRINGE_FARBLK_A_STRAND,           // 11
  SLIDING_NEARBLK_A_STRAND,         // 12
  FRINGE2_NEARBLK_A_STRAND,         // 13
  INTERIOR_NS_A_STRAND,             // 14
  INTERIOR_FARBLK2_A_STRAND,        // 15
  INTERIOR_FARBLK1_A_STRAND,        // 16
  FRINGE2_BSURFELS_STRAND,          // 17
  FRINGE_BSURFELS_STRAND,           // 18
  INTERIOR_BSURFELS_STRAND,         // 19
  FRINGE_NEARBLK_B_STRAND,          // 20
  FRINGE_FARBLK_B_STRAND,           // 21
  SLIDING_NEARBLK_B_STRAND,         // 22
  FRINGE2_NEARBLK_B_STRAND,         // 23
  INTERIOR_NS_B_STRAND,             // 24
  INTERIOR_FARBLK2_B_STRAND,        // 25
  INTERIOR_FARBLK1_B_STRAND,        // 26
#if BUILD_5G_LATTICE
  FRINGE_NEARBLK_C_STRAND,          // 27
  FRINGE_FARBLK_C_STRAND,           // 28
  SLIDING_NEARBLK_C_STRAND,         // 29
  FRINGE2_NEARBLK_C_STRAND,         // 30
  INTERIOR_NS_C_STRAND,             // 31
  INTERIOR_FARBLK2_C_STRAND,        // 32
  INTERIOR_FARBLK1_C_STRAND,        // 33
#endif
  RADIATION_STRAND,                 // 34
  TIMESTEP_UPDATE_STRAND,           // 35
  N_STRANDS                         // 36
};

using STRAND_MASK = tBITSET<N_STRANDS>;
using ATOMIC_STRAND_MASK = tATOMIC_BITSET<N_STRANDS>;

inline
const char * print_strand_name(asINT32 strand)
{
#define CASE_RETURN_STRING(s) case s: {return #s; break;}
  switch((STRAND) strand) {
    CASE_RETURN_STRING(NO_STRAND)
    CASE_RETURN_STRING(SLIDING_SURF_A_STRAND)
    CASE_RETURN_STRING(SLIDING_SURF_B_STRAND)
    CASE_RETURN_STRING(INTERIOR_FILM_ACCUMULATION_STRAND)
    CASE_RETURN_STRING(INTERIOR_FILM_KINEMATICS_STRAND)
    CASE_RETURN_STRING(FRINGE_WSURF_SAMPLING_STRAND)
    CASE_RETURN_STRING(INTERIOR_WSURF_SAMPLING_STRAND)
    CASE_RETURN_STRING(RADIATION_STRAND)
    CASE_RETURN_STRING(FRINGE2_SURF_STRAND)
    CASE_RETURN_STRING(FRINGE2_WSURF_HFC_STRAND)
    CASE_RETURN_STRING(FRINGE_SURF_STRAND)
    CASE_RETURN_STRING(SLIDING_SURF_C_STRAND)
    CASE_RETURN_STRING(FRINGE_NEARBLK_A_STRAND)
    CASE_RETURN_STRING(FRINGE_FARBLK_A_STRAND)
    CASE_RETURN_STRING(SLIDING_NEARBLK_A_STRAND)
    CASE_RETURN_STRING(FRINGE2_NEARBLK_A_STRAND)
    CASE_RETURN_STRING(INTERIOR_NS_A_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK2_A_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK1_A_STRAND)
    CASE_RETURN_STRING(FRINGE2_BSURFELS_STRAND)
    CASE_RETURN_STRING(FRINGE_BSURFELS_STRAND)
    CASE_RETURN_STRING(INTERIOR_BSURFELS_STRAND)
    CASE_RETURN_STRING(FRINGE_NEARBLK_B_STRAND)
    CASE_RETURN_STRING(FRINGE_FARBLK_B_STRAND)
    CASE_RETURN_STRING(SLIDING_NEARBLK_B_STRAND)
    CASE_RETURN_STRING(FRINGE2_NEARBLK_B_STRAND)
    CASE_RETURN_STRING(INTERIOR_NS_B_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK2_B_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK1_B_STRAND)
    CASE_RETURN_STRING(TIMESTEP_UPDATE_STRAND)
#if BUILD_5G_LATTICE
    CASE_RETURN_STRING(FRINGE_NEARBLK_C_STRAND)
    CASE_RETURN_STRING(FRINGE_FARBLK_C_STRAND)
    CASE_RETURN_STRING(SLIDING_NEARBLK_C_STRAND)
    CASE_RETURN_STRING(FRINGE2_NEARBLK_C_STRAND)
    CASE_RETURN_STRING(INTERIOR_NS_C_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK2_C_STRAND)
    CASE_RETURN_STRING(INTERIOR_FARBLK1_C_STRAND)
#endif
    CASE_RETURN_STRING(N_STRANDS)
    default:
    msg_internal_error("Strand not recognized!");
  }
  return "";
#undef CASE_RETURN_STRING
}

#endif
