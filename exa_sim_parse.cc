
// This is not used anymore, but is left here to serve as a template for how to add
// support for additional environment variables.
sdFLOAT pressure_bc_omega_u_n = 0.01;

int g_measure_conduction_passthrough = 0;
int g_shell_tangential_measurements = 0;

typedef struct sSIM_CONSTANT_OVERRIDE_VIA_ENV_VAR {
  char env_var_name[256];
  sdFLOAT *parameter;
  char echo_string[256];
} *SIM_CONSTANT_OVERRIDE_VIA_ENV_VAR;

static sSIM_CONSTANT_OVERRIDE_VIA_ENV_VAR constant_override_table[] = {
 { "EXA_PRESSURE_BC_OMEGA_U_N",  &pressure_bc_omega_u_n,  "Pressure BC underrelaxation"       },
};

#define N_CONSTANT_OVERRIDES (sizeof(constant_override_table) / sizeof(sSIM_CONSTANT_OVERRIDE_VIA_ENV_VAR))

VOID init_sim_constant_overrides()
{
  ccDOTIMES(i, N_CONSTANT_OVERRIDES) {
    STRING env_var_str = getenv(constant_override_table[i].env_var_name);

    if (env_var_str) {
      *constant_override_table[i].parameter = atof(env_var_str);	
      msg_print("%s set to %g", 
		constant_override_table[i].echo_string, 
		*constant_override_table[i].parameter);
    }
  }
}


/* This routine allows the user to skip over the rest of the line, after 
 * reading a number. The file pointer moves beyond the next newline 
 * character. */
VOID skip_2_nl (FILE *dfp) {
  char ch;
  while ( (ch=fgetc(dfp)) != '\n') {
    ;
  }
  return;
}

typedef enum {
  PARM_INT,
  PARM_FLOAT
} PHYSICS_PARM_TYPE;

typedef struct sPHYSICS_PARM {
  cSTRING name;
  PHYSICS_PARM_TYPE type;
  void *addr;
  asINT32 n_elems;
} *PHYSICS_PARM;

#include "shob.h"
#define set_parm(name, addr, type) { #name, PARM_ ## type, (void *)&addr, 1}
#define set_parm_v(name, addr, type, n_elems) { #name, PARM_ ## type, (void *)&addr, n_elems}

static sPHYSICS_PARM physics_parms[] = {
#if BUILD_D19_LATTICE
   set_parm(relaxation_D_finest, g_relaxation_D_finest, FLOAT),
#endif

   set_parm(lrf_exterior_kick, g_lrf_exterior_kick, FLOAT),
   set_parm(lrf_interior_kick, g_lrf_interior_kick, FLOAT),
   set_parm(lrf_visc_factor, g_lrf_visc_factor, FLOAT),
   set_parm(lrf_skin_factor, g_lrf_skin_factor, FLOAT),
   set_parm(use_rng_model, g_use_rng_model, INT),
   set_parm(NEW_PM_TURB_MODEL, g_NEW_PM_TURB_MODEL, INT),
   set_parm(td_p, g_td_p, FLOAT),
   set_parm(T_in_fluid, g_T_in_fluid, FLOAT),
   set_parm(atf_0, g_atf_0, FLOAT),
   set_parm(atf_1_pass, g_atf_1_pass, FLOAT),
   set_parm(atf_1, g_atf_1, FLOAT),
   set_parm(PM_TURB_MODEL_CONK, g_PM_TURB_MODEL_CONK, INT),
   set_parm(k_floor, g_k_floor, FLOAT),
   set_parm(k_floor_hs, g_k_floor_hs, FLOAT),
   set_parm(e_floor, g_e_floor, FLOAT),
   set_parm(k_ceiling, g_k_ceiling, FLOAT),
   set_parm(e_ceiling, g_e_ceiling, FLOAT),
   set_parm(nu_tur_ceil, g_nu_tur_ceil, FLOAT),
   set_parm(mterm_ablm, g_mterm_ablm, FLOAT),
   set_parm(ablm_cap, g_ablm_cap, FLOAT),
   set_parm(Cmu_min, g_Cmu_min, FLOAT),
   set_parm(Cmu_max, g_Cmu_max, FLOAT),
   set_parm(A_Cmu, g_A_Cmu, FLOAT),
   set_parm(eta_crit, g_eta_crit, FLOAT),
   set_parm(Cmu_factor, g_Cmu_factor, FLOAT),
   set_parm(delu_factor, g_delu_factor, FLOAT),
   set_parm(less_factor, g_less_factor, FLOAT),
   set_parm(dist_pde_cutoff, g_dist_pde_cutoff, FLOAT),
   set_parm(count_nan_max, g_count_nan_max, INT),
   set_parm(ycell_omega, g_ycell_omega, FLOAT),
   set_parm(y_cell_cap, g_y_cell_cap, FLOAT),
   set_parm(swirl_factor, g_swirl_factor, FLOAT),
   set_parm(swirl_factor_hs, g_swirl_factor_hs, FLOAT),
   set_parm(Nusselt_power, g_Nusselt_power, FLOAT),
   set_parm(helicity_factor, g_helicity_factor, FLOAT),
   set_parm(helicity_relax, g_helicity_relax, FLOAT),
   set_parm(hel_denom_factor, g_hel_denom_factor, FLOAT),
   set_parm(surface_temp_alpha, g_surface_temp_alpha, FLOAT),
   set_parm(sliding_surf_temp_alpha, g_sliding_surf_temp_alpha, FLOAT),
   set_parm(rotating_surf_temp_alpha, g_rotating_surf_temp_alpha, FLOAT),
   set_parm(nuT_ceiling, g_nuT_ceiling, FLOAT),
   set_parm(nuT_ceiling_hs, g_nuT_ceiling_hs, FLOAT),
   set_parm(nuT_ceiling_hs_lb_entropy, g_nuT_ceiling_hs_lb_entropy, FLOAT),
   set_parm(nuT_floor, g_nuT_floor, FLOAT),
   set_parm(h_t_alpha, g_h_t_alpha, FLOAT),
   set_parm(tke_surfel_beta, g_tke_surfel_beta, FLOAT),
   set_parm(yplus_ht_crit, g_yplus_ht_crit, FLOAT),
   set_parm(yplus_crit, g_yplus_crit, FLOAT),
   set_parm(yplus_wall_crit, g_yplus_wall_crit, FLOAT),
   set_parm(yplus_turb_dist, g_yplus_turb_dist, FLOAT),
   set_parm(yplus_A, g_yplus_A, FLOAT),
   set_parm(yplus_B, g_yplus_B, FLOAT),
   set_parm(A_yp, g_A_yp, FLOAT),
   set_parm(coef_thickness, g_coef_thickness, FLOAT),
   set_parm(is_lat_heat, g_is_lat_heat, INT),
   set_parm(is_Schmidt_dif, g_is_Schmidt_dif, INT),
   set_parm(turb_schmidt_number, g_turb_schmidt_number, FLOAT),
   set_parm(schmidt_number, g_schmidt_number, FLOAT),
   //set_parm(rho_kg_m3, g_rho_kg_m3, FLOAT),
   set_parm(coef_thf, g_coef_thf, FLOAT),
   set_parm(AB_coef, g_AB_coef, FLOAT),
   set_parm(AB_coef_m, g_AB_coef_m, FLOAT),
   set_parm(AMP_coef, g_AMP_coef, FLOAT),
   set_parm(AA_yp, g_AA_yp, FLOAT),
   set_parm(A_liq, g_A_liq, FLOAT),
   set_parm(B_liq, g_B_liq, FLOAT),
   set_parm(A_ypl, g_A_ypl, FLOAT),
   set_parm(mt_coef, g_mt_coef, FLOAT),
   set_parm(Tpr, g_Tpr, FLOAT),
   set_parm(is_test_wv, g_is_test_wv, INT),
   set_parm(A_coef_th, g_A_coef_th, FLOAT),
   set_parm(y_con, g_y_con, FLOAT),
   set_parm(y_con2, g_y_con2, FLOAT),
   set_parm(ustar_A, g_ustar_A, FLOAT),
   set_parm(ustar_B, g_ustar_B, FLOAT),
   set_parm(E_dist, g_E_dist, FLOAT),
   set_parm(T_underrelax, g_T_underrelax, FLOAT),
   set_parm(T_underrelax_mf, g_T_underrelax_mf, FLOAT),
   set_parm(swirl_beta, g_swirl_beta, FLOAT),
   set_parm(upwind_factor, g_upwind_factor, FLOAT),
   set_parm(upwind_bound, g_upwind_bound, FLOAT),
   set_parm(is_noneq_div_u, g_is_noneq_div_u, INT),
   set_parm(is_ra_model, g_is_ra_model, INT),
   set_parm(is_defogging, g_is_defogging, INT),
   set_parm(turb_Prandtl_wall, g_turb_Prandtl_wall, FLOAT),
   set_parm(turb_Schmidt_wall, g_turb_Schmidt_wall, FLOAT),
   set_parm(turb_Schmidt_wall_min, g_turb_Schmidt_wall_min, FLOAT),
   set_parm(thr_m, g_thr_m, FLOAT),
   set_parm(cold_fluid_Prandtl_number, g_cold_fluid_Prandtl_number, FLOAT),
   set_parm(ablm_power, g_ablm_power, INT),
   set_parm(scatter_omega, g_scatter_omega, FLOAT),
   set_parm(scatter_omega_dns, g_scatter_omega_dns, FLOAT),
   set_parm(scatter_omega_mf, g_scatter_omega_mf, FLOAT),
   set_parm(kspan_alpha, g_kspan_alpha, FLOAT),
   set_parm(base_visc_alpha, g_base_visc_alpha, FLOAT),
   set_parm(surfel_smoothing, g_surfel_smoothing, INT),
   set_parm(eta_swirl_crit, g_eta_swirl_crit, FLOAT),
   set_parm(eta_swirl_alpha, g_eta_swirl_alpha, FLOAT),
   set_parm(ablm_tau_factor, g_ablm_tau_factor, FLOAT),
   set_parm(ablm_ustar_floor, g_ablm_ustar_floor, FLOAT),
   set_parm(exa_swirl_type, g_exa_swirl_type, INT),
   set_parm(ablm_floor, g_ablm_floor, FLOAT),
   set_parm(ablm_floor_hs, g_ablm_floor_hs, FLOAT),
   set_parm(vr_factor, g_vr_factor, FLOAT),
   set_parm(vr_factor_2, g_vr_factor_2, FLOAT),
   set_parm(tke_pde_alpha, g_tke_pde_alpha, FLOAT),
   set_parm(yplus_ht0_crit, g_yplus_ht0_crit, FLOAT),
   set_parm(B_zero, g_B_zero, FLOAT),
   set_parm(tke_buoy_factor, g_tke_buoy_factor, FLOAT),
   set_parm(eps_buoy_factor, g_eps_buoy_factor, FLOAT),
   set_parm(vel_buoy_max, g_vel_buoy_max, FLOAT),
   set_parm(coef_pm, g_coef_pm, FLOAT),
   set_parm(rough_beta, g_rough_beta, FLOAT),
   set_parm(dns_surface_meas, g_dns_surface_meas, INT),
   set_parm(eddy_tran_alpha, g_eddy_tran_alpha, FLOAT),
   set_parm(eddy_tran_y, g_eddy_tran_y, FLOAT),
   set_parm(first_tran_y, g_first_tran_y, FLOAT),
   set_parm(skfr_ceiling, g_skfr_ceiling, FLOAT),
   set_parm(nu_threshold, g_nu_threshold, FLOAT),
   set_parm(yplus_lam_crit, g_yplus_lam_crit, FLOAT),
   set_parm(tran_eddy_factor, g_tran_eddy_factor, FLOAT),
   set_parm(yplus_ceiling, g_yplus_ceiling, FLOAT),
   set_parm(S_B_0, g_S_B_0, FLOAT),
   set_parm(PRES_CORR_INLET, g_PRES_CORR_INLET, INT),
   set_parm(domain_length, g_domain_length, FLOAT),
   set_parm(pr_rel, g_pr_rel, FLOAT),
   set_parm(al_tur_c, g_al_tur_c, FLOAT),
   set_parm(Eta_c, g_Eta_c, FLOAT),
   set_parm(Pr_tur_min, g_Pr_tur_min, FLOAT),
   set_parm(Pr_tur_max, g_Pr_tur_max, FLOAT),
   set_parm(pm_int_lev, g_pm_int_lev, FLOAT),
   set_parm(pm_eps, g_pm_eps, FLOAT),
   set_parm(C_s, g_C_s, FLOAT),
   set_parm(nu_over_t_base, g_nu_over_t_base, FLOAT),
   set_parm(u_scatter, g_u_scatter, FLOAT),
   set_parm(u_scatter_n, g_u_scatter_n, FLOAT),
   set_parm(u_scatter_n_TS, g_u_scatter_n_TS, FLOAT),
   set_parm(u_scatter_n_mf, g_u_scatter_n_mf, FLOAT),
   set_parm(dpdb_factor, g_dpdb_factor, FLOAT),
   set_parm(y_scatter_min, g_y_scatter_min, FLOAT),
   set_parm(y_scatter_max, g_y_scatter_max, FLOAT),
   set_parm(pfluid_crit, g_pfluid_crit, FLOAT),
   set_parm(scatter_span, g_scatter_span, FLOAT),
   set_parm(kplus_alpha, g_kplus_alpha, FLOAT),
   set_parm(kpde_factor, g_kpde_factor, FLOAT),
   set_parm(yplus_ablm_crit, g_yplus_ablm_crit, FLOAT),
   set_parm(tau_ustar_factor, g_tau_ustar_factor, FLOAT),
   set_parm(trans_a, g_trans_a, FLOAT),
   set_parm(trans_b, g_trans_b, FLOAT),
   set_parm(coef_conv, g_coef_conv, FLOAT),
   set_parm(coef_diff, g_coef_diff, FLOAT),
   set_parm(coef_conv_fb, g_coef_conv_fb, FLOAT),
   set_parm(coef_diff_fb, g_coef_diff_fb, FLOAT),
   set_parm(weight_crit, g_weight_crit, FLOAT),
   set_parm(total_weight_floor, g_total_weight_floor, FLOAT),
   set_parm(scatter_coeff_factor, g_scatter_coeff_factor, FLOAT),
   set_parm(temp_fac_alpha, g_temp_fac_alpha, FLOAT),
   set_parm(temp_fac_beta, g_temp_fac_beta, FLOAT),
   set_parm(Mach_limit, g_Mach_limit, FLOAT),
   set_parm(adv_fraction, g_adv_fraction, FLOAT),
   set_parm(adv_fraction_hs, g_adv_fraction_hs, FLOAT),
   set_parm(adv_fraction_mf, g_adv_fraction_mf, FLOAT),
   set_parm(frac_visc_factor, g_frac_visc_factor, FLOAT),
   set_parm(frac_vr_visc_factor, g_frac_vr_visc_factor, FLOAT),
   set_parm(frac_visc_factor_wall, g_frac_visc_factor_wall, FLOAT),
   set_parm(frac_visc_factor_wall_max, g_frac_visc_factor_wall_max, FLOAT),
   set_parm(frac_visc_yplus_alpha, g_frac_visc_yplus_alpha, FLOAT),
   set_parm(pfluid_threshold, g_pfluid_threshold, FLOAT),
   set_parm(pfluid_bottom, g_pfluid_bottom, FLOAT),
   set_parm(left_bound, g_left_bound, FLOAT),
   set_parm(right_bound, g_right_bound, FLOAT),
   set_parm(yplus_hyper_switch, g_yplus_hyper_switch, FLOAT),
   set_parm(surfel_k_res_mag, g_surfel_k_res_mag, FLOAT),
   set_parm(tau_across_mrf_one, g_tau_across_mrf_one, INT),
   set_parm(scatter_omega_lrf, g_scatter_omega_lrf, FLOAT),
   set_parm(scatter_omega_lrf_n, g_scatter_omega_lrf_n, FLOAT),
   set_parm(scatter_omega_lrf_dns, g_scatter_omega_lrf_dns, FLOAT),
   set_parm(scatter_omega_lrf_n_dns, g_scatter_omega_lrf_n_dns, FLOAT),
   set_parm(lrf_ke_wall_correction, g_lrf_ke_wall_correction, INT),
   set_parm(noneq_sij_across_lrf, g_noneq_sij_across_lrf, INT),
   set_parm(state_cap_coeff, g_state_cap_coeff, FLOAT),
   set_parm(norm_factor_off, g_norm_factor_off, INT),
   set_parm(use_noneq_sij, g_use_noneq_sij, INT),
   set_parm(eddy_visc_over_t_const, g_eddy_visc_over_t_const, FLOAT),
   set_parm(base_visc_over_t_cross_mrf, g_base_visc_over_t_cross_mrf, FLOAT),
   set_parm(is_ring_surfel_for_MRF, g_is_ring_surfel_for_MRF, INT),
   set_parm(scatter_omega_lrf_cap_floor, g_scatter_omega_lrf_cap_floor, FLOAT),
   set_parm(lrf_s2s_scatter_transit, g_lrf_s2s_scatter_transit, FLOAT),
   set_parm(Mach_cap, g_Mach_cap, FLOAT),
   set_parm(q_gch, g_q_gch, FLOAT),
   //for hybrid solver
   set_parm(T_floor, g_T_floor, FLOAT),
   set_parm(helicity_floor, g_helicity_floor, FLOAT),
   set_parm(helicity_floor_hs, g_helicity_floor_hs, FLOAT),
   set_parm(helicity_floor_hs_wall, g_helicity_floor_hs_wall, FLOAT),
   set_parm(helicity_floor_wall, g_helicity_floor_wall, FLOAT),
   set_parm(Q_factor, g_Q_factor, FLOAT),
   set_parm(vr_factor_pde, g_vr_factor_pde, FLOAT),
   set_parm(u_scatter_alpha, g_u_scatter_alpha, FLOAT),
   set_parm(standard_rng, g_standard_rng, INT),
   set_parm(menter_pk_limiter, g_menter_pk_limiter, INT),
   set_parm(improved_laminar_patches, g_improved_laminar_patches, INT),
   set_parm(fpg_model, g_fpg_model, INT),
   set_parm(low_re_model, g_low_re_model, INT),
   set_parm(product_k_factor, g_product_k_factor, FLOAT),
   //set_parm(flux_limiter, g_flux_limiter, INT),
   set_parm(use_delta_mass_in_skin, g_use_delta_mass_in_skin, INT),
   set_parm(tau_temp_base, g_tau_temp_base, FLOAT),
   set_parm(delta_temp_alpha, g_delta_temp_alpha, FLOAT),
   set_parm(delta_temp_alpha_mf, g_delta_temp_alpha_mf, FLOAT),
   //set_parm(use_t_filter, g_use_t_filter, FLOAT),
   set_parm(use_t_filter_protection, g_use_t_filter_protection, FLOAT),
   set_parm(protect_everywhere, g_protect_everywhere, INT),
   set_parm(ke_lbm_protection_cutoff, g_ke_lbm_protection_cutoff, FLOAT),
   set_parm(temp_protection_alpha, g_temp_protection_alpha, FLOAT),
   set_parm(in_state_scale_up_on, g_in_state_scale_up_on, INT),
   set_parm(s2v_states_protection, g_s2v_states_protection, INT),
   set_parm(pre_advect_protection, g_pre_advect_protection, INT),
   set_parm(surfel_dyn_protection, g_surfel_dyn_protection, INT),
   set_parm(N_in_out_ratio, g_N_in_out_ratio, FLOAT),
   set_parm(delta_T_dyn_correction, g_delta_T_dyn_correction, INT),
   set_parm(thermal_diff_accel_factor, g_thermal_diff_accel_factor, FLOAT),
   //set_parm(srf_scatter_adv, g_srf_scatter_adv, INT),
   set_parm(scale_up_surface_q, g_scale_up_surface_q, INT),
   set_parm(S_trace_factor, g_S_trace_factor, FLOAT),
   set_parm(S_trace_factor_TS, g_S_trace_factor_TS, FLOAT),
   //set_parm(use_pfluid_in_grad_vel, g_use_pfluid_in_grad_vel, INT),
   set_parm(is_dyn_base_visc_on, g_is_dyn_base_visc_on, INT),
   set_parm(y_cell_floor, g_y_cell_floor, FLOAT),
   set_parm(y_cell_floor_eps, g_y_cell_floor_eps, FLOAT),
   set_parm(ustar0_in_prod_k, g_ustar0_in_prod_k, INT),
   set_parm(use_old_eps_wall, g_use_old_eps_wall, INT),
   set_parm(cross_grad_upwind, g_cross_grad_upwind, INT),
   //set_parm(central_across_vr, g_central_across_vr, INT),
   set_parm(flux_limiter_across_vr, g_flux_limiter_across_vr, INT),
   //set_parm(cross_term_across_vr, g_cross_term_across_vr, INT),
   set_parm(pwal_overlap_alpha, g_pwal_overlap_alpha, FLOAT),
   set_parm(add_tan_vel_component, g_add_tan_vel_component, INT),
#if BUILD_D39_LATTICE
   set_parm(isotropic_delta_mass, g_isotropic_delta_mass, INT),
   //set_parm(combined_solver_option, g_combined_solver_option, INT),
   set_parm(use_d3q27_mme, g_use_d3q27_mme, INT),
#endif
   //set_parm(use_new_mass_flux_bc, g_use_new_mass_flux_bc, INT),
   //set_parm(use_tan_vel_component_only, g_use_tan_vel_component_only, INT),
   set_parm(scale_up_feq, g_scale_up_feq, INT),
   //set_parm(use_second_order_feq, g_use_second_order_feq, INT),
   //set_parm(use_positive_mass_flux, g_use_positive_mass_flux, INT),
   //set_parm(new_mass_flux_measurement, g_new_mass_flux_measurement, INT),
   set_parm(nu_over_t_input, g_nu_over_t_input, FLOAT),
   set_parm(underrelax_E, g_underrelax_E, INT),
   set_parm(use_mom_hyper_visc_correct, g_use_mom_hyper_visc_correct, INT),
   set_parm(nu_eddy_prod_alpha, g_nu_eddy_prod_alpha, FLOAT),
   set_parm(RT_floor, g_RT_floor, FLOAT),
   set_parm(RT_beta, g_RT_beta, FLOAT),
   set_parm(RT_beta_wall, g_RT_beta_wall, FLOAT),
   set_parm(RT_fact, g_RT_fact, FLOAT),
   set_parm(use_e_correct_wall, g_use_e_correct_wall, INT),
   set_parm(T_scatter_threshhold, g_T_scatter_threshhold, FLOAT),
   set_parm(T_lb_solver_start_time, g_T_lb_solver_start_time, INT),
   set_parm(nu_rat_m, g_nu_rat_m, FLOAT),
   set_parm(use_directional_grad, g_use_directional_grad, INT),
   set_parm(DG_STAB, g_DG_STAB, INT),
   set_parm(use_prev_time_force, g_use_prev_time_force, INT),
   set_parm(use_prior_u_in_pde, g_use_prior_u_in_pde, INT),
   set_parm(use_s2v_protection, g_use_s2v_protection, INT),
   set_parm(T_over_T0_underrelax, g_T_over_T0_underrelax, FLOAT),
   set_parm(T_over_T0_underrelax_mf, g_T_over_T0_underrelax_mf, FLOAT),
   set_parm(tang_vel_at_outlet, g_tang_vel_at_outlet, INT),
   set_parm(press_outlet_omega, g_press_outlet_omega, FLOAT),
   set_parm(use_ustar_only_prod_k, g_use_ustar_only_prod_k, INT),
   set_parm(eta_swirl_2d, g_eta_swirl_2d, FLOAT),
   set_parm(eta_swirl_2d_cap, g_eta_swirl_2d_cap, FLOAT),
   set_parm(Mach_max, g_Mach_max, FLOAT),
   set_parm(use_laminar_ustar0, g_use_laminar_ustar0, INT),
   set_parm(ustar_turb_beta, g_ustar_turb_beta, FLOAT),
   set_parm(skfr_theta_factor, g_skfr_theta_factor, FLOAT),
   set_parm(ustar0_theta_factor, g_ustar0_theta_factor, FLOAT),
   set_parm(skfr_gamma_factor, g_skfr_gamma_factor, FLOAT),
   set_parm(use_ablm_new_skfr, g_use_ablm_new_skfr, INT),
   set_parm(use_ustar_k_alpha, g_use_ustar_k_alpha, INT),
   set_parm(skfr_dpds_power, g_skfr_dpds_power, FLOAT),
   set_parm(skfr_theta_delta, g_skfr_theta_delta, FLOAT),
   set_parm(turn_on_visht, g_turn_on_visht, INT),
   set_parm(alpha_vel_sqrd_correction, g_alpha_vel_sqrd_correction, FLOAT),
   set_parm(use_filter_noneq, g_use_filter_noneq, INT),
   set_parm(use_feq_for_frac_adv, g_use_feq_for_frac_adv, INT),
   set_parm(beta_L_to_T, g_beta_L_to_T, FLOAT),
   set_parm(beta_T_to_L, g_beta_T_to_L, FLOAT),
   set_parm(ltt_on_eddy_visc, g_ltt_on_eddy_visc, INT),
   set_parm(ltt_on_eddy_visc_ke, g_ltt_on_eddy_visc_ke, INT),
   set_parm(pitch_motion_delta_mass, g_pitch_motion_delta_mass, INT),
   //set_parm(output_ablm_skfr, g_output_ablm_skfr, INT),
   set_parm(dpds_factor, g_dpds_factor, FLOAT),
   set_parm(APM_wall_fact, g_APM_wall_fact, FLOAT),
   set_parm(APM_phi_crit, g_APM_phi_crit, FLOAT),
   set_parm(APM_scatter_factor, g_APM_scatter_factor, FLOAT),
   set_parm(APM_OMEGA_RHO, g_APM_OMEGA_RHO, FLOAT),
   set_parm(APM_OMEGA_DU_N, g_APM_OMEGA_DU_N, FLOAT),
   set_parm(APM_OMEGA_DU_N1, g_APM_OMEGA_DU_N1, FLOAT),
   set_parm(APM_input_porosity, g_APM_input_porosity, FLOAT),
   set_parm(APM_input_surface_porosity, g_APM_input_surface_porosity, FLOAT),
   set_parm(APM_input_tortuosity, g_APM_input_tortuosity, FLOAT),
   set_parm(APM_input_resistivity_x, g_APM_input_resistivity_x, FLOAT),
   set_parm(APM_input_resistivity_y, g_APM_input_resistivity_y, FLOAT),
   set_parm(APM_input_resistivity_z, g_APM_input_resistivity_z, FLOAT),
   set_parm(APM_skin_fact, g_APM_skin_fact, FLOAT),
   set_parm(APM_fluid_fric_fact, g_APM_fluid_fric_fact, FLOAT),
   set_parm(APM_fluid_fric_weight, g_APM_fluid_fric_weight, FLOAT),
   set_parm(APM_nu_over_t_base, g_APM_nu_over_t_base, FLOAT),
   set_parm(APM_srf_A, g_APM_srf_A, FLOAT),
   set_parm(APM_srf_B, g_APM_srf_B, FLOAT),
   set_parm(APM_srf_C, g_APM_srf_C, FLOAT),
   set_parm(APM_srf_nx, g_APM_srf_nx, FLOAT),
   set_parm(APM_srf_ny, g_APM_srf_ny, FLOAT),
   set_parm(APM_srf_nz, g_APM_srf_nz, FLOAT),
   set_parm(APM_use_ABC_dp, g_APM_use_ABC_dp, INT),
   set_parm(APM_linear_x0, g_APM_linear_x0, FLOAT),
   set_parm(APM_linear_x1, g_APM_linear_x1, FLOAT),
   set_parm(APM_delta_mass_scale_up, g_APM_delta_mass_scale_up, INT),
   set_parm(APM_tang_mom_exchange, g_APM_tang_mom_exchange, INT),
   set_parm(APM_interface_dyn, g_APM_interface_dyn, INT),
   set_parm(APM_free_slip, g_APM_free_slip, INT),
   set_parm(APM_use_cell_u, g_APM_use_cell_u, INT),
   set_parm(APM_use_mean_temp, g_APM_use_mean_temp, INT),
   set_parm(is_no_flow_case, g_is_no_flow_case, INT),
   set_parm(visc_mach_alpha_dns, g_visc_mach_alpha_dns, FLOAT),
   set_parm(feq_3_beta, g_feq_3_beta, FLOAT),
   set_parm(turn_off_hyb_force, g_turn_off_hyb_force, INT),
   set_parm(use_filter_noneq, g_use_filter_noneq, INT),
   set_parm(use_diag_grad_p, g_use_diag_grad_p, INT),
   set_parm(flux_limiter_cross, g_flux_limiter_cross, INT),
   set_parm(turn_on_visht, g_turn_on_visht, INT),
   set_parm(use_N_eq_cleanup_rule, g_use_N_eq_cleanup_rule, INT),
   set_parm(nu_bulk, g_nu_bulk, FLOAT),
   set_parm(nu_bulk_bgk, g_nu_bulk_bgk, FLOAT),
   set_parm(nu_bulk_diss, g_nu_bulk_diss, FLOAT),
   set_parm(eq_5th_order_d39, g_eq_5th_order_d39, FLOAT),
   set_parm(eq_4th_order_d39, g_eq_4th_order_d39, FLOAT),
   set_parm(eq_3th_order_d39, g_eq_3th_order_d39, FLOAT),
   set_parm(eq_5th_order_init_d39, g_eq_5th_order_init_d39, FLOAT),
   set_parm(alpha_eq_4th_order, g_alpha_eq_4th_order, FLOAT),
   set_parm(alpha_tau_bulk, g_alpha_tau_bulk, FLOAT),
   set_parm(alpha_eq_5th_order, g_alpha_eq_5th_order, FLOAT),
   set_parm(alpha_stab_udotf, g_alpha_stab_udotf, FLOAT),
   set_parm(alpha_stab_press, g_alpha_stab_press, FLOAT),
   set_parm(alpha_stab_udotf_h2, g_alpha_stab_udotf_h2, FLOAT),
   set_parm(alpha_stab_press_h2, g_alpha_stab_press_h2, FLOAT),
   set_parm(filter_1st_order_alpha, g_filter_1st_order_alpha, FLOAT),
   set_parm(max_lattice_vel_sqrd, g_max_lattice_vel_sqrd, FLOAT),
#if BUILD_D19_LATTICE
  //Following are related to 19state based Transonic version 
   set_parm(HSExt_alpha_stab_udotf, g_HSExt_alpha_stab_udotf, FLOAT),
   set_parm(HSExt_alpha_stab_press, g_HSExt_alpha_stab_press, FLOAT),
   set_parm(HSExt_alpha_uuu_mom, g_HSExt_alpha_uuu_mom, FLOAT),
   set_parm(HSExt_uuu_mom_cap, g_HSExt_uuu_mom_cap, FLOAT),
   set_parm(HSExt_stab_range_end, g_HSExt_stab_range_end, FLOAT),
   set_parm(HSExt_stab_range_start, g_HSExt_stab_range_start, FLOAT),
   set_parm(HSExt_stab_correction_cap, g_HSExt_stab_correction_cap, FLOAT),
   set_parm(HSExt_max_lattice_vel_sqrd, g_HSExt_max_lattice_vel_sqrd, FLOAT),
   set_parm(HSExt_filter_1st_order_alpha, g_HSExt_filter_1st_order_alpha, FLOAT),
   set_parm(HSExt_Mach_cap, g_HSExt_Mach_cap, FLOAT),
   set_parm(simplified_mass_flux_bc, g_simplified_mass_flux_bc, INT),
#elif BUILD_D39_LATTICE 
   set_parm(hybExt_alpha_stab_udotf, g_hybExt_alpha_stab_udotf, FLOAT),
   set_parm(hybExt_alpha_stab_press, g_hybExt_alpha_stab_press, FLOAT),
   set_parm(hybExt_alpha_uuu_mom, g_hybExt_alpha_uuu_mom, FLOAT),
   set_parm(hybExt_uuu_mom_cap, g_hybExt_uuu_mom_cap, FLOAT),
   set_parm(hybExt_stab_range_end, g_hybExt_stab_range_end, FLOAT),
   set_parm(hybExt_stab_range_start, g_hybExt_stab_range_start, FLOAT),
   set_parm(hybExt_max_lattice_vel_sqrd, g_hybExt_max_lattice_vel_sqrd, FLOAT),
   set_parm(hybExt_lapp_mach_dep, g_hybExt_lapp_mach_dep, INT),
   set_parm(hybExt_filter_1st_order_alpha, g_hybExt_filter_1st_order_alpha, FLOAT),
   set_parm(hybExt_Mach_cap, g_hybExt_Mach_cap, FLOAT),
   set_parm(hybExt_unified_bulk_visc_model, g_hybExt_unified_bulk_visc_model, FLOAT),
   set_parm(hybExt_nu_bulk_unified_alpha, g_hybExt_nu_bulk_unified_alpha, FLOAT),
   set_parm(hybExt_nu_bulk_over_t_base, g_hybExt_nu_bulk_over_t_base, FLOAT),
   set_parm(nu_bulk_over_t_hs_region, g_nu_bulk_over_t_hs_region, FLOAT),
   set_parm(hybExt_bulk_temp_dep, g_hybExt_bulk_temp_dep, FLOAT),
   set_parm(hybExt_bulk_temp_offset, g_hybExt_bulk_temp_offset, FLOAT),
   set_parm(Mach_cap_hs_region, g_Mach_cap_hs_region, FLOAT),
   set_parm(max_lattice_vel_sqrd_hs_region, g_max_lattice_vel_sqrd_hs_region, FLOAT),
   set_parm(simplified_mass_flux_bc, g_simplified_mass_flux_bc, INT),
#endif 
   set_parm(delta_mass_flux_cap, g_delta_mass_flux_cap, FLOAT),
   set_parm(freeze_temp, g_freeze_temp, INT),
   set_parm(STAG_TEMP, g_STAG_TEMP, INT),
   set_parm(SS_INLET, g_SS_INLET, INT),
   set_parm(SS_STAT_PRES, g_SS_STAT_PRES, FLOAT),
   set_parm(ma0, g_ma0, FLOAT),
   set_parm(y_sample_new, g_y_sample_new, INT),
   set_parm(new_v2s_sampling, g_new_v2s_sampling, INT),
   set_parm(vr_acoustic_correction, g_vr_acoustic_correction, INT),
   set_parm(filter_2nd_order_alpha, g_filter_2nd_order_alpha, FLOAT),
   set_parm(filter_3rd_order_alpha, g_filter_3rd_order_alpha, FLOAT),
   set_parm(filter_noneq_alpha, g_filter_noneq_alpha, FLOAT),
   set_parm(no_surfel_scatter, g_no_surfel_scatter, INT),
   set_parm(use_traceless_form, g_use_traceless_form, INT),
   set_parm(bulk_visc_factor, g_bulk_visc_factor, FLOAT),
   set_parm(eq_3rd_order_d19, g_eq_3rd_order_d19, FLOAT),
   set_parm(alpha_deltaT, g_alpha_deltaT, FLOAT),
   set_parm(A_sam, g_A_sam, FLOAT),
   set_parm(ustar_lam_beta, g_ustar_lam_beta, FLOAT),
   set_parm(LTT_yplus_lam, g_LTT_yplus_lam, FLOAT),
   set_parm(LTT_yplus_turb, g_LTT_yplus_turb, FLOAT),
   set_parm(LTT_index_cutoff, g_LTT_index_cutoff, INT),
   set_parm(use_ke_damping, g_use_ke_damping, INT),
   set_parm(output_ltt_index, g_output_ltt_index, INT),
   set_parm(outlet_noneq_alpha, g_outlet_noneq_alpha, FLOAT),
   set_parm(scale_outlet_vel, g_scale_outlet_vel, INT),
   set_parm(omega_swirl_cutoff, g_omega_swirl_cutoff, FLOAT),
   set_parm(omega_swirl_cutoff_wall, g_omega_swirl_cutoff_wall, FLOAT),
   set_parm(swirl_cutoff_pow, g_swirl_cutoff_pow, FLOAT),
   set_parm(nu_bulk_over_t, g_nu_bulk_over_t, FLOAT),
   set_parm(nu_bulk_over_t_wall, g_nu_bulk_over_t_wall, FLOAT),
   set_parm(nu_bulk_over_t_input, g_nu_bulk_over_t_input, FLOAT),
   set_parm(comp_uds_to_temp, g_comp_uds_to_temp, INT),   // comp UDS solve to temp by using kappa as UDS diff coef
   //set_parm(init_ht_time, g_init_ht_time, INT),  // for momentum freeze
   set_parm(init_ht_option, g_init_ht_option, INT),// for momentum freeze
   set_parm(alpha_ht, g_alpha_ht, FLOAT),           // for momentum freeze
   set_parm(uds_timestep_coeff, g_uds_timestep_coeff, FLOAT),           // for momentum freeze and scalar solver
   set_parm(min_temp_coeff, g_min_temp_coeff, FLOAT),     // for passive momentum freeze
   set_parm(max_temp_coeff, g_max_temp_coeff, FLOAT),     // for passive momentum freeze
   set_parm(explode_uniformly, g_explode_uniformly, INT),
   set_parm(mass_flux_correction, g_mass_flux_correction,  INT),
   set_parm(beta_mf, g_beta_mf, FLOAT),
   set_parm(film_thick_timestep_coeff, g_film_thick_timestep_coeff, FLOAT),
   set_parm(nu_bulk_alpha, g_nu_bulk_alpha, FLOAT),
   set_parm(nu_bulk_alpha_lb_entropy, g_nu_bulk_alpha_lb_entropy, FLOAT),
   set_parm(mach_correction_alpha, g_mach_correction_alpha, FLOAT),
   set_parm(mach_correction_alpha_low, g_mach_correction_alpha_low, FLOAT),
   set_parm(fraction_ustar_sqrd_tbl, g_fraction_ustar_sqrd_tbl, FLOAT),
#if BUILD_D39_LATTICE
   set_parm(fix_viscosity, g_fix_viscosity, INT),
#endif
   set_parm(fix_viscosity_TS, g_fix_viscosity_TS, INT),
   set_parm(slip_visc_heating, g_slip_visc_heating, INT),
   set_parm(h_plus_lam, g_h_plus_lam, FLOAT),
   set_parm(use_new_nu_bulk, g_use_new_nu_bulk, INT),
   set_parm(nu_bulk_over_t_base, g_nu_bulk_over_t_base, FLOAT),
   set_parm(nu_bulk_over_t_base_lb_entropy, g_nu_bulk_over_t_base_lb_entropy, FLOAT),
   set_parm(nu_bulk_over_t_cap, g_nu_bulk_over_t_cap, FLOAT),
   set_parm(use_fd_divu_bulk, g_use_fd_divu_bulk, INT),
   set_parm(omega_dns_surface, g_omega_dns_surface, FLOAT),
   set_parm(scale_up_outflux_upbound, g_scale_up_outflux_upbound, FLOAT),
   set_parm(use_T_cap_T0_over_R, g_use_T_cap_T0_over_R, INT),
   set_parm(mass_flux_delta_alpha, g_mass_flux_delta_alpha, FLOAT),
   set_parm(mass_flux_delta_beta, g_mass_flux_delta_beta, FLOAT),
   set_parm(no_skfr_smooth, g_no_skfr_smooth, INT),
   set_parm(ablm_Mach_alpha, g_ablm_Mach_alpha, FLOAT),
   set_parm(no_vr_gradp, g_no_vr_gradp, INT),
   set_parm(scatter_Mach_threshold, g_scatter_Mach_threshold, FLOAT),
   set_parm(scatter_Mach_alpha, g_scatter_Mach_alpha, FLOAT),
   set_parm(dir_depend_dpds, g_dir_depend_dpds, INT),
   set_parm(zeroth_scatter_coeff, g_zeroth_scatter_coeff, FLOAT),
   set_parm(v2s_scatter_option, g_v2s_scatter_option, INT),
   set_parm(v2s_scatter_stagnation_scaledown, g_v2s_scatter_stagnation_scaledown, INT),
   set_parm(LTT_dpds_beta, g_LTT_dpds_beta, FLOAT),
   set_parm(pm_force_Ma_alpha, g_pm_force_Ma_alpha, FLOAT),
   set_parm(add_PM_resistence_cap, g_add_PM_resistence_cap, INT),
   set_parm(PM_force_iter_num, g_PM_force_iter_num, INT),
   set_parm(PM_force_iter_std, g_PM_force_iter_std, INT),
   set_parm(PM_force_iter_alpha, g_PM_force_iter_alpha, FLOAT),
   set_parm(Qpatch_under_relaxation, g_Qpatch_under_relaxation, FLOAT),
   set_parm(fan_force_iter_num, g_fan_force_iter_num, INT),
   set_parm(fan_force_iter_alpha, g_fan_force_iter_alpha, FLOAT),
   set_parm(tke_source_gamma, g_tke_source_gamma, FLOAT),
   set_parm(eps_source_gamma, g_eps_source_gamma, FLOAT),
   set_parm(yplus_mach_correction_max, g_yplus_mach_correction_max, FLOAT),
   set_parm(yplus_mach_correction_min, g_yplus_mach_correction_min, FLOAT),
   set_parm(den_wall_scale_factor, g_den_wall_scale_factor, FLOAT),
   set_parm(den_wall_scale_delta, g_den_wall_scale_delta, FLOAT),
   set_parm(turb_visc_heating, g_turb_visc_heating, INT),
   set_parm(trace_in_turb_visht, g_trace_in_turb_visht, INT),
   set_parm(nu_base_eps_floor, g_nu_base_eps_floor, FLOAT),
   set_parm(nu_base_eps_floor_hs, g_nu_base_eps_floor_hs, FLOAT),
   set_parm(turb_visc_heating_alpha, g_turb_visc_heating_alpha, FLOAT),
   set_parm(turb_visc_heating_alpha_wall, g_turb_visc_heating_alpha_wall, FLOAT),
   set_parm(RT_y_plus_threshold_HS, g_RT_y_plus_threshold_HS, FLOAT),
   set_parm(RT_y_plus_threshold_TS, g_RT_y_plus_threshold_TS, FLOAT),
   set_parm(SW_y_plus_threshold_HS, g_SW_y_plus_threshold_HS, FLOAT),
   set_parm(RT_gamma_swirl, g_RT_gamma_swirl, INT),
   set_parm(gamma_swirl_floor_yplus, g_gamma_swirl_floor_yplus, INT),
   set_parm(kepsilon_ur, g_kepsilon_ur, FLOAT),
   set_parm(rat_rho_sp, g_rat_rho_sp, FLOAT),
   set_parm(coef_u, g_coef_u, FLOAT),
   set_parm(coef_gs, g_coef_gs, FLOAT),
   set_parm(is_RT_new, g_is_RT_new, INT),
   set_parm(is_thr_hel, g_is_thr_hel, INT),
   set_parm(T_over_T0_underrelax_wall, g_T_over_T0_underrelax_wall, FLOAT),
   set_parm(T_over_T0_underrelax_hs, g_T_over_T0_underrelax_hs, FLOAT),
   set_parm(temp_sample_relax, g_temp_sample_relax, FLOAT),
   set_parm(temp_sample_overrelax, g_temp_sample_overrelax, INT),
   set_parm(dns_slip_model, g_dns_slip_model, INT),
   set_parm(k_f_vssp_cap, g_k_f_vssp_cap, FLOAT),
   set_parm(nu_over_t_factor_pow, g_nu_over_t_factor_pow, FLOAT),
   set_parm(DNS_blending_opt, g_DNS_blending_opt, INT),
   set_parm(slip_s2s_factor, g_slip_s2s_factor, FLOAT),
   set_parm(slip_s2s_pow, g_slip_s2s_pow, FLOAT),
   set_parm(slip_nu_over_t_pow, g_slip_nu_over_t_pow, FLOAT),
   set_parm(roughness_height_factor, g_roughness_height_factor, FLOAT),
   set_parm(ke_super_cycling_alpha, g_ke_super_cycling_alpha, FLOAT),
   set_parm(vel_super_cycling_cap, g_vel_super_cycling_cap, FLOAT),

   set_parm(track_lost_particle_mass, g_track_lost_particle_mass, INT),
   set_parm(report_stencil_memory, g_report_stencil_memory, INT),
   set_parm(use_particle_evaporation_model, g_use_particle_evaporation_model, INT),
   set_parm(enable_particle_evaporation_scalar_solver_coupling, g_enable_particle_evaporation_scalar_solver_coupling, INT),
   set_parm(use_explicit_particle_thermal_solver, g_use_explicit_particle_thermal_solver, INT),
   set_parm(track_evaporated_particles, g_track_evaporated_particles, INT),

   set_parm(particle_Prandtl_number, g_particle_Prandtl_number, FLOAT),
   set_parm(particle_Nusselt_number, g_particle_Nusselt_number, FLOAT),
   set_parm(particle_Sherwood_number, g_particle_Sherwood_number, FLOAT),

   set_parm(initial_particle_temperature, g_initial_particle_temperature, FLOAT),
   set_parm_v(initial_particle_mass_fractions, g_initial_particle_mass_fractions, FLOAT, NUM_PARTICLE_MATERIAL_SPECIES),
   set_parm(particle_cp, g_particle_cp, FLOAT),
   set_parm(particle_evaporation_limit, g_particle_evaporation_limit, FLOAT),
   set_parm(particle_min_diameter, g_particle_min_diameter, FLOAT),
   set_parm(particle_evaporation_relative_humidity, g_particle_evaporation_relative_humidity, FLOAT),
   set_parm(particle_water_activity_slope, g_particle_water_activity_slope, FLOAT),
   set_parm(particle_vapor_diffusivity, g_particle_vapor_diffusivity, FLOAT),
   set_parm(particle_specific_heat_of_vaporization, g_particle_specific_heat_of_vaporization, FLOAT),
   set_parm(particle_vapor_gas_constant , g_particle_vapor_gas_constant, FLOAT),

   set_parm(do_accretion_filtering_with_film_stencil, g_do_accretion_filtering_with_film_stencil, INT),
   set_parm(use_dynamic_restitution_coefficients, g_use_dynamic_restitution_coefficients, INT),
   set_parm(is_erosion_simulation, g_is_erosion_simulation, INT),
   set_parm(erosion_start_time, g_erosion_start_time, FLOAT),
   set_parm(erosion_surface_material_density, g_erosion_surface_material_density, FLOAT),
   set_parm(erosion_F_s, g_erosion_F_s, FLOAT),
   set_parm(erosion_C_c, g_erosion_C_c, FLOAT),
   set_parm(erosion_C_d, g_erosion_C_d, FLOAT),
   set_parm(erosion_K, g_erosion_K, FLOAT),
   set_parm(erosion_U_tsh, g_erosion_U_tsh, FLOAT),
   set_parm(include_brownian_diffusion, g_include_brownian_diffusion, INT),
   set_parm(reentrain_everything_always, g_reentrain_everything_always, INT),
   set_parm(collision_detection_perimeter_tolerance, g_collision_detection_perimeter_tolerance, FLOAT),
   set_parm(skip_dynamics_if_just_reentrianed, g_skip_dynamics_if_just_reentrianed, INT),
   set_parm(fast_film_accumulation, g_fast_film_accumulation, INT),
   set_parm(enable_sample_surface_film_meas, g_enable_sample_surface_film_meas, INT),
   set_parm(enable_film_mlrf, g_enable_film_mlrf, INT),
   set_parm(use_deterministic_release_rate, g_use_deterministic_release_rate, INT),
   set_parm(override_emitter_on_time, g_override_emitter_on_time, INT),
   set_parm(override_emitter_off_time, g_override_emitter_off_time, INT),
   set_parm(override_emitter_diameter_limit, g_override_emitter_diameter_limit, INT),
   set_parm(wiper_range_of_influence, g_wiper_range_of_influence, FLOAT),
   set_parm(constant_shear_stress_value, g_constant_shear_stress_value, FLOAT),
   set_parm(constant_shear_stress_direction, g_constant_shear_stress_direction, INT),
   set_parm(film_thickness_threshold_mm, g_film_thickness_threshold_mm, FLOAT),
   set_parm(surface_tension_alpha, g_surface_tension_alpha, FLOAT),
   set_parm(surface_tension_beta, g_surface_tension_beta, FLOAT),
   set_parm(Fuchs_number_threshold, g_reference_film_thickness_h0, FLOAT),
   set_parm(Fuchs_number_threshold, g_Fuchs_number_threshold, FLOAT),
   set_parm(surface_tension_f_receding_force, g_surface_tension_f_receding_force, FLOAT),

   set_parm(max_surfels_in_a_film_solver_stencil, g_max_surfels_in_a_film_solver_stencil, INT),
   set_parm(interpolate_air_vel_for_particles, g_interpolate_air_vel_for_particles, INT),
   set_parm(enable_spalding_near_wall_interpolation, g_enable_spalding_near_wall_interpolation, INT),
   set_parm(characteristic_particle_time_threshold, g_characteristic_particle_time_threshold, FLOAT),
   set_parm(use_explicit_integrator_for_particle_dynamics, g_use_explicit_integrator_for_particle_dynamics, INT),
   set_parm(use_particle_pressure_force, g_use_particle_pressure_force, INT),
   set_parm(ublk_averaged_momentum_coupling, g_ublk_averaged_momentum_coupling, INT),
   set_parm(max_particle_subcycles, g_max_particle_subcycles, INT),
   set_parm(max_fluid_particle_coupling_accel_sqrd, g_max_fluid_particle_coupling_accel_sqrd, FLOAT),
   set_parm(particles_in_vacuum, g_particles_in_vacuum, INT),
   set_parm(model_unresolved_tke_for_particle, g_model_unresolved_tke_for_particle, FLOAT),
   set_parm(particle_ode_fastest_allowed_timescale, g_particle_ode_fastest_allowed_timescale, FLOAT),
   set_parm(saffman_lift_force_enabled, g_saffman_lift_force_enabled, INT),
   set_parm(saffman_strain_rate_mag_max, g_saffman_strain_rate_mag_max, FLOAT),
   set_parm(saffman_strain_rate_mag_min, g_saffman_strain_rate_mag_min, FLOAT),
   set_parm(use_u_star_ht_based_shear_force_for_film, g_use_u_star_ht_based_shear_force_for_film, INT),
   set_parm(no_trajectory_measurement, g_no_trajectory_measurement, INT),
   set_parm(trajectory_use_blocking_synchronous_send, g_trajectory_use_blocking_synchronous_send, INT),
   set_parm(n_film_solver_base_steps, g_n_film_solver_base_steps, INT),
   set_parm(use_agglomeration, g_use_agglomeration, INT),
   set_parm(agglomeration_frequency, g_agglomeration_frequency, INT),
   set_parm(maximum_number_of_parcels, g_maximum_number_of_parcels, INT),
   set_parm(minimum_number_of_parcels, g_minimum_number_of_parcels, INT),
   set_parm(n_accumulation_base_steps, g_n_accumulation_base_steps, INT),

   set_parm(max_splash_child_parcels, g_max_splash_child_parcels, INT),
   set_parm(max_breakup_child_parcels, g_max_breakup_child_parcels, INT),
   set_parm(wiper_reentrainment_factor, g_wiper_reentrainment_factor, FLOAT),

   set_parm(surface_tension_advancing_angle, g_surface_tension_advancing_angle, FLOAT),
   set_parm(surface_tension_receding_angle, g_surface_tension_receding_angle, FLOAT),
   set_parm(surface_tension_model_enabled, g_surface_tension_model_enabled, INT),

   set_parm(dynamic_reentrainment_model_enabled, g_dynamic_reentrainment_model_enabled, INT),
   set_parm(dynamic_reentrainment_model_curvature_enabled, g_dynamic_reentrainment_model_curvature_enabled, INT),
   set_parm(dynamic_reentrainment_model_gravity_enabled, g_dynamic_reentrainment_model_gravity_enabled, INT),
   set_parm(dynamic_reentrainment_model_curvature_radius_meter, g_dynamic_reentrainment_model_curvature_radius_meter, FLOAT),
   set_parm(dynamic_reentrainment_model_contact_angle, g_dynamic_reentrainment_model_contact_angle, FLOAT),
   set_parm(dynamic_reentrainment_model_capillary_length_coeff, g_dynamic_reentrainment_model_capillary_length_coeff, FLOAT),

   set_parm(static_trajectory_meas_decimation_rate, g_static_trajectory_meas_decimation_rate, INT),

   //set_parm(internal_film_viscosity_coeff, g_internal_film_viscosity_coeff, FLOAT), //Not allowed to change this as of 7/1/16
   set_parm(corona_vel_angle, g_corona_vel_angle, FLOAT),
   set_parm(normal_splash_restitution_coefficient, g_normal_splash_restitution_coefficient, FLOAT),
   set_parm(transverse_splash_restitution_coefficient, g_transverse_splash_restitution_coefficient, FLOAT),
   set_parm(particle_no_drag_delay, g_particle_no_drag_delay, FLOAT),
   set_parm(film_reentrainment_normal_vel_coefficient, g_film_reentrainment_normal_vel_coefficient, FLOAT),

   //set_parm(MF_energy_correction, g_MF_energy_correction, INT),  //for momentum freeze, not use
   set_parm(convert_to_incompressible, g_convert_to_incompressible, INT), //for momentum freeze, not use
   set_parm(ke_timestep_coeff, g_ke_timestep_coeff, FLOAT),  //for momentum freeze

   // for LB entropy 
   //set_parm(use_lb_entropy, g_use_lb_entropy, INT),
   set_parm(s_visc_heating_factor, g_s_visc_heating_factor, FLOAT),
   set_parm(s_temp_diff_factor, g_s_temp_diff_factor, FLOAT),
   set_parm(s_const_kappa, g_s_const_kappa, FLOAT),
   set_parm(s_remove_fraction, g_s_remove_fraction, FLOAT),
   set_parm(s_fluctuation_factor, g_s_fluctuation_factor, FLOAT),
   set_parm(s_noneq_factor, g_s_noneq_factor, FLOAT),
   set_parm(alpha_laplace_remove_s, g_alpha_laplace_remove_s, FLOAT),
   set_parm(use_cap_for_s_noneq, g_use_cap_for_s_noneq, INT),
   set_parm(strict_stop_state_conserve, g_strict_stop_state_conserve, INT),
   set_parm(lb_entropy_solver_type, g_lb_entropy_solver_type, INT),
   set_parm(s_stability_factor, g_s_stability_factor, FLOAT),

   // for LB_energy
   set_parm(scatter_Mach_factor, g_scatter_Mach_factor, FLOAT),
   //set_parm(use_surfel_filter_collision, g_use_surfel_filter_collision, INT),
   set_parm(filter_norm_theta, g_filter_norm_theta,FLOAT),
   //set_parm(scale_up_surface_pressure, g_scale_up_surface_pressure, INT),
   set_parm(delta_mass_relax_theta, g_delta_mass_relax_theta, FLOAT),
   //set_parm(correct_mtflux_option, g_correct_mtflux_option, INT),
   set_parm(mtflux_delta, g_mtflux_delta, FLOAT),
   //set_parm(is_tke_in_TE, g_is_tke_in_TE, INT),
   set_parm(u_scatter_ts_lb_energy, g_u_scatter_ts_lb_energy, FLOAT),
   set_parm(eq_3rd_order_d19_lb_energy, g_eq_3rd_order_d19_lb_energy, FLOAT),

   //set_parm(use_lb_energy, g_use_lb_energy, INT),
   //set_parm(use_const_tau_bulk, g_use_const_tau_bulk, INT),
   set_parm(nuT_ceiling_hs_lb_energy, g_nuT_ceiling_hs_lb_energy, FLOAT),
   set_parm(nu_bulk_alpha_lb_energy, g_nu_bulk_alpha_lb_energy, FLOAT),
   set_parm(nu_bulk_over_t_base_lb_energy, g_nu_bulk_over_t_base_lb_energy, FLOAT),
   set_parm(energy_stab_factor, g_energy_stab_factor, FLOAT),
   set_parm(energy_vr_factor_input, g_energy_vr_factor_input, FLOAT),
   set_parm(eff_Pr_floor, g_eff_Pr_floor, FLOAT),
   set_parm(eff_Pr_ceiling, g_eff_Pr_ceiling, FLOAT),
   //set_parm(cap_remove_noneq, g_cap_remove_noneq, INT),
   set_parm(beta_mach_dep_kappa, g_beta_mach_dep_kappa, FLOAT),
   set_parm(nuT_ceiling_lb_energy, g_nuT_ceiling_lb_energy, FLOAT),
   set_parm(alpha_stab_udotf_lb_energy, g_alpha_stab_udotf_lb_energy, FLOAT),
   set_parm(remove_neq_TE_factor, g_remove_neq_TE_factor, FLOAT),
   set_parm(cap_ddt_TE_factor, g_cap_ddt_TE_factor, FLOAT),
   set_parm(cap_uu_TE_factor, g_cap_uu_TE_factor, FLOAT),
   set_parm(tau_temp_base_lb_energy, g_tau_temp_base_lb_energy, FLOAT),
   set_parm(slip_visc_heating_lb_energy, g_slip_visc_heating_lb_energy, INT),
   set_parm(turb_visc_heating_lb_energy, g_turb_visc_heating_lb_energy,INT),
   set_parm(fix_viscosity_lb_energy, g_fix_viscosity_lb_energy, INT),
   set_parm(near_wall_swirl_alpha, g_near_wall_swirl_alpha, FLOAT),
   set_parm(mach_correction_alpha, g_mach_correction_alpha_lb_energy, FLOAT),
   set_parm(mach_correction_alpha_low, g_mach_correction_alpha_low_lb_energy, FLOAT),

   //for local vel freeze
   set_parm(beta_u_feq, g_beta_u_feq, FLOAT),
   set_parm(alpha_tau_mf, g_alpha_tau_mf, FLOAT),

   //for new TWM of 6X MF
   set_parm(pwn, g_pwn, INT),
   set_parm(as_mag, g_as_mag, FLOAT),
   set_parm(A_alp, g_A_alp, FLOAT),
   //set_parm(AA_a, g_AA_a, FLOAT),
   //set_parm(AMF, g_AMF, FLOAT),

   //for melting solver
   set_parm(min_T_pde_value, g_min_T_pde_value, FLOAT),
   set_parm(AB_td, g_AB_td, FLOAT),
   set_parm(A_alph, g_A_alph, FLOAT),
   set_parm(A_adt, g_A_adt, FLOAT),
   set_parm(A_addt, g_A_addt, FLOAT),
   set_parm(dns_melt, g_dns_melt, INT),
   set_parm(vr_factor_pde_T, g_vr_factor_pde_T, FLOAT),

   /* for LB based UDS solver */
   set_parm(uds_diffusivity_ceiling, g_uds_diffusivity_ceiling, FLOAT),   
   set_parm(uds_diffusivity_floor, g_uds_diffusivity_floor, FLOAT),
   set_parm(Nin_uds_sum_threshold, g_Nin_uds_sum_threshold, FLOAT),
   set_parm(uds_threshold, g_uds_threshold, FLOAT),
#if BUILD_5G_LATTICE  
   set_parm(meas_uds,g_meas_uds, INT),
   set_parm(use_special_uds_source, g_use_special_uds_source, INT),
   set_parm(specify_uds_values, g_specify_uds_values, INT),
   set_parm_v(uds_volume_source_polynomial_coefs, g_uds_volume_source_polynomial_coefs, FLOAT, 5),
   set_parm_v(uds_volume_source_term,g_uds_volume_source_term, FLOAT, MAX_N_USER_DEFINED_SCALARS),
   set_parm_v(uds_inlet_value, g_uds_inlet_value, FLOAT, MAX_N_USER_DEFINED_SCALARS),
   set_parm_v(uds_flux_bc_value, g_uds_flux_bc_value, FLOAT, MAX_N_USER_DEFINED_SCALARS),
   set_parm_v(uds_prescribed_bc, g_uds_prescribed_bc, FLOAT, MAX_N_USER_DEFINED_SCALARS),
   set_parm_v(uds_initial_value, g_uds_initial_value, FLOAT, MAX_N_USER_DEFINED_SCALARS),
#endif
   set_parm(n_user_defined_scalars, g_n_user_defined_scalars, INT),                   
   //set_parm(use_old_water_turb_model, g_use_old_water_turb_model, INT), 
   //set_parm(comp_uds_to_temp, g_comp_uds_to_temp, INT),   // comp UDS solver to temp by using kappa as UDS diff coef    
   //set_parm(use_uds_lb_solver, g_use_uds_lb_solver, INT),

   //phase field
   set_parm(is_pf_model, g_is_pf_model, INT),
   set_parm(mp_filter_flag, g_mp_filter_flag, INT),   
   set_parm(mp_turb_sles, g_mp_turb_sles, INT),   
   set_parm(pf_iwettability, g_pf_iwettability, INT),
   set_parm(pf_contact_angle, g_pf_contact_angle, FLOAT),
   set_parm(pf_wettability_orderpw, g_pf_wettability_orderpw, FLOAT),   
   set_parm(mp_test_velocity_time, g_mp_test_velocity_time, INT),
   set_parm(mp_test_velocity, g_mp_test_velocity, FLOAT),
   set_parm(Mobility, g_Mobility, FLOAT),
   set_parm(Intrfc_thickness, g_Intrfc_thickness, FLOAT),
   set_parm(Surface_tension, g_Surface_tension, FLOAT),
   set_parm(Surface_tension_mks_kg_per_s2, g_Surface_tension_mks_kg_per_s2, FLOAT),
   set_parm(Dens_heavy, g_Dens_heavy, FLOAT),
   set_parm(Dens_light, g_Dens_light, FLOAT),   
   set_parm(pf_nu_over_T_air_floor, g_pf_nu_over_T_air_floor, FLOAT),
   set_parm(pf_nu_over_T_water_floor, g_pf_nu_over_T_water_floor, FLOAT),
   set_parm(pf_nu_air_mks_m2_per_s, g_pf_nu_air_mks_m2_per_s, FLOAT),
   set_parm(pf_nu_over_T_air, g_pf_nu_over_T_air, FLOAT),
   set_parm(pf_nu_over_T_water, g_pf_nu_over_T_water, FLOAT),
   set_parm(pf_nu_w2a_ratio, g_pf_nu_w2a_ratio, FLOAT),
   set_parm(pf_accommodation_air, g_pf_accommodation_air, FLOAT),
   set_parm(pf_accommodation_water, g_pf_accommodation_water, FLOAT),   
   set_parm(pf_vel_output, g_pf_vel_output, INT),   
   set_parm(pf_CSF, g_pf_CSF, INT),   
   set_parm(pf_bc_ur, g_pf_bc_ur, INT),
   // end phase field
 
#if BUILD_5G_LATTICE 
   set_parm(scatter_omega_rho, g_scatter_omega_rho, FLOAT),
   //set_parm(mp_num_components, g_mp_num_components, INT),
   // hysteresis model parameters
   //set_parm(mp_wetting_model, g_mp_wetting_model, INT),
   set_parm(mp_wetting_fluctuation, g_mp_wetting_fluctuation, FLOAT),
   set_parm(mp_defect_width, g_mp_defect_width, FLOAT),
   set_parm(mp_defect_period, g_mp_defect_period, INT),
   // stokes model parameters
   set_parm(mp_solver_type, g_mp_solver_type, INT),

   //set_parm_v(mp_eos, g_mp_eos, INT, MAXIMUM_NUM_COMPONENTS),
   set_parm_v(mp_G, g_mp_G, FLOAT, MAXIMUM_NUM_COMPONENTS * MAXIMUM_NUM_COMPONENTS),
   //set_parm_v(mp_wall_potential, g_mp_wall_potential, FLOAT, MAXIMUM_NUM_COMPONENTS),
   //set_parm_v(mp_wall_potential2, g_mp_wall_potential2, FLOAT, MAXIMUM_NUM_COMPONENTS),
   set_parm_v(mp_viscosity, g_mp_viscosity, FLOAT, MAXIMUM_NUM_COMPONENTS),
   set_parm_v(mp_molecular_mass, g_mp_molecular_mass, FLOAT, MAXIMUM_NUM_COMPONENTS),
   set_parm(mp_temperature, g_mp_temperature, FLOAT),
   set_parm(mp_sound_speed_squared, g_mp_sound_speed_squared, FLOAT),
   set_parm_v(mp_wall_friction, g_mp_wall_friction, FLOAT, MAXIMUM_NUM_COMPONENTS),
   set_parm_v(mp_wall_friction2, g_mp_wall_friction2, FLOAT, MAXIMUM_NUM_COMPONENTS),
   
   set_parm(mp_surface_tension, g_mp_surface_tension, FLOAT),
   set_parm(mp_gradient_level, g_mp_gradient_level, INT),
   set_parm(mp_sound_speed_bound, g_mp_sound_speed_bound, FLOAT),
   
   set_parm(mp_has_gradp, g_mp_has_gradp, INT),
   set_parm(mp_gradp_x, g_mp_gradp_x, FLOAT),
   set_parm(mp_gradp_y, g_mp_gradp_y, FLOAT),
   set_parm(mp_gradp_z, g_mp_gradp_z, FLOAT),
   
   set_parm(mp_has_field, g_mp_has_field, INT),
   set_parm_v(mp_field_x, g_mp_field_x, FLOAT, 2),
   set_parm_v(mp_field_y, g_mp_field_y, FLOAT, 2),
   set_parm_v(mp_field_z, g_mp_field_z, FLOAT, 2),
   set_parm_v(mp_molecular_charge, g_mp_molecular_charge, FLOAT, MAXIMUM_NUM_COMPONENTS),
   
   set_parm(mp_improved_guo, g_mp_improved_guo, FLOAT),
   set_parm(mp_omega_mix, g_mp_omega_mix, FLOAT),
   
   set_parm(mp_seed_grad_p_scale, g_mp_seed_grad_p_scale, FLOAT),
   set_parm(mp_seed_grad_p_time, g_mp_seed_grad_p_time, INT),
   set_parm(mp_meas_seed_pres, g_mp_meas_seed_pres, INT),
   set_parm(mp_meas_test_dp, g_mp_meas_test_dp, INT),
   set_parm(mp_meas_write_pres, g_mp_meas_write_pres, INT),
   set_parm(mp_laplace_solver, g_mp_laplace_solver, INT),

   set_parm(mp_sample_potential, g_mp_sample_potential, INT),
   set_parm(mp_sample_potential_factor, g_mp_sample_potential_factor, FLOAT),
   
   set_parm(mp_fraction_advect, g_mp_fraction_advect, FLOAT),
   set_parm(mp_relax_protection, g_mp_relax_protection, INT),

   set_parm(mp_multiphase_type, g_mp_multiphase_type, INT),
   set_parm(mp_interface_A, g_mp_interface_A, FLOAT),
   set_parm(mp_omega_color, g_mp_omega_color, FLOAT),
   set_parm(mp_protection_Ma, g_mp_protection_Ma, FLOAT),
   set_parm(mp_feq3rd_coef, g_mp_feq3rd_coef, FLOAT),
   set_parm(mp_mix_omega_type, g_mp_mix_omega_type, INT),
   set_parm(mp_traceless_H, g_mp_traceless_H, INT),
   set_parm(mp_use_vel_force_H, g_mp_use_vel_force_H, INT),
    set_parm(mp_scale_comp_vel, g_mp_scale_comp_vel, INT),
   
   set_parm(mp_sink_alpha, g_mp_sink_alpha, FLOAT),
   set_parm_v(mp_sink_v, g_mp_sink_v, FLOAT, 2),
   set_parm_v(mp_sink_A, g_mp_sink_A, FLOAT, 2),
   set_parm_v(mp_corner_low, g_mp_corner_low, FLOAT, 3),
   set_parm_v(mp_corner_high, g_mp_corner_high, FLOAT, 3),

   set_parm(mp_u_scatter_cap, g_mp_u_scatter_cap, FLOAT),
   set_parm(mp_scale_srf_scatter, g_mp_scale_srf_scatter, INT),
   set_parm(mp_atwood_scatter, g_mp_atwood_scatter, FLOAT),
   set_parm(mp_scatter_coeff, g_mp_scatter_coeff, FLOAT),

   set_parm(mp_protection_Ma_stokes, g_mp_protection_Ma_stokes, FLOAT),

   set_parm(mp_normalize_g, g_mp_normalize_g, INT),
   set_parm(mp_rock_cs_area, g_mp_rock_cs_area, FLOAT),

   set_parm(dns_slip_option, g_dns_slip_option, INT),
   set_parm(dns_slip_factor, g_dns_slip_factor, FLOAT),
   set_parm(near_wall_visc_alpha, g_near_wall_visc_alpha, FLOAT),
   set_parm(sf_rho_threshold, g_sf_rho_threshold, FLOAT),
   set_parm(sf_theta_threshold, g_sf_theta_threshold, FLOAT),
   //set_parm(k_f_vssp_cap, g_k_f_vssp_cap, FLOAT),
   //set_parm(nu_over_t_factor_pow, g_nu_over_t_factor_pow, FLOAT),
   //set_parm(slip_s2s_factor, g_slip_s2s_factor, FLOAT),
   //set_parm(slip_s2s_pow, g_slip_s2s_pow, FLOAT),
   //set_parm(slip_nu_over_t_pow, g_slip_nu_over_t_pow, FLOAT),
   set_parm(do_DNS_cf_smooth, g_do_DNS_cf_smooth, INT),
   set_parm(wetting_power, g_wetting_power, FLOAT),
   set_parm(AT_wood_wet_max, g_AT_wood_wet_max, FLOAT),
   set_parm(AT_wood_wet_min, g_AT_wood_wet_min, FLOAT),
   set_parm(AT_wood_slip_max, g_AT_wood_slip_max, FLOAT),
   set_parm(AT_wood_slip_min, g_AT_wood_slip_min, FLOAT),
   set_parm(AT_wood_film_max, g_AT_wood_film_max, FLOAT),
   set_parm(AT_wood_film_min, g_AT_wood_film_min, FLOAT),
   set_parm(AT_alpha_floor, g_AT_alpha_floor, FLOAT),
   set_parm(AT_alpha_floor_wet, g_AT_alpha_floor_wet, FLOAT),
   set_parm(mp_potential_threshold, g_mp_potential_threshold, FLOAT),
   set_parm(mp_c0wet_crt_At, g_mp_c0wet_crt_At, FLOAT),
   set_parm(mp_c1wet_crt_At, g_mp_c1wet_crt_At, FLOAT),
   set_parm(mp_ceiling_potential, g_mp_ceiling_potential, FLOAT),
   set_parm(mp_ceiling_potential_beta, g_mp_ceiling_potential_beta, FLOAT),
   set_parm(mp_G_mag_slip_threshold, g_mp_G_mag_slip_threshold, FLOAT),

   //from merge of 22584-5.5a_digital_rock-130
   set_parm(mp_pbc_noleak_atwood_threshold, g_mp_pbc_noleak_atwood_threshold, FLOAT),
   set_parm(component_mass_residual, g_component_mass_residual, FLOAT),
   set_parm(component_mass_residual2, g_component_mass_residual2, FLOAT),

   set_parm_v(mp_emss_sink_A, g_mp_emss_sink_A, FLOAT, 2),
   set_parm_v(mp_emss_sink_alpha, g_mp_emss_sink_alpha, FLOAT, 2),
   set_parm(mp_mss_water_inlet, g_mp_mss_water_inlet, FLOAT),
   set_parm(mp_mss_z, g_mp_mss_z, INT),
   set_parm(mp_mss_scale_mass, g_mp_mss_scale_mass, INT),
   set_parm(mp_mss_porosity_switch, g_mp_mss_porosity_switch, INT),
   set_parm(mp_mss_porosity_switch_rock_type, g_mp_mss_porosity_switch_rock_type, INT),
   set_parm(mp_mss_porosity_switch_tanh, g_mp_mss_porosity_switch_tanh, FLOAT),
   set_parm(mp_mss_porosity_switch_porosity, g_mp_mss_porosity_switch_porosity, FLOAT),
   set_parm(mp_mss_pore_dsm_factor, g_mp_mss_pore_dsm_factor, FLOAT),
   set_parm(mp_mss_pore_dsm_factor2, g_mp_mss_pore_dsm_factor2, FLOAT),
   set_parm(iSw_exp_simplified, g_iSw_exp_simplified, INT),
   set_parm(mp_mss_residual_switch, g_mp_mss_residual_switch, INT),
   set_parm(mp_mss_residualwo_model, g_mp_mss_residualwo_model, INT),
   set_parm(mp_mss_residualwo_model_mxSw, g_mp_mss_residualwo_model_mxSw, FLOAT),
   set_parm(idsm_individual_rocktype, g_idsm_individual_rocktype, INT),

   set_parm(mp_NuTrationeq1_factor, g_mp_NuTrationeq1_factor, FLOAT),
   set_parm(mp_mixedtau_param, g_mp_mixedtau_param, FLOAT),

   set_parm(ms_porosity_input_floor,g_ms_porosity_input_floor, FLOAT),
   set_parm(ms_input_ref_charlength,g_ms_input_ref_charlength, FLOAT),
   set_parm(ms_input_read_charlength,g_ms_input_read_charlength, FLOAT),
   set_parm(ms_length_scale_change,g_ms_length_scale_change, INT),
   set_parm(mp_csf_kappa_pm,g_mp_csf_kappa_pm, FLOAT),
   set_parm(mp_csf_kappa_pore,g_mp_csf_kappa_pore, FLOAT),
   set_parm(mp_csf_kappa_porosity_switch,g_mp_csf_kappa_porosity_switch, FLOAT),
   set_parm(mp_csf_kappa_porosity_width,g_mp_csf_kappa_porosity_width, FLOAT),

   //large pore  (porous media region)
   set_parm(scale_pm_resistance, g_scale_pm_resistance, INT),
   set_parm(output_times_porosity, g_output_times_porosity, INT),

   //large pore multi-phase
   set_parm(mp_pc_model_atcrt, g_mp_pc_model_atcrt, FLOAT),
   set_parm(mp_pm_k0model, g_mp_pm_k0model, INT),
   set_parm(mp_pm_kr_simulation, g_mp_pm_kr_simulation, INT),
   set_parm(mp_pm_krmodel, g_mp_pm_krmodel, INT),
   set_parm(mp_pm_intfc_detect_op, g_mp_pm_intfc_detect_op, INT),
   set_parm(mp_pm_detect_Swderiv, g_mp_pm_detect_Swderiv, FLOAT),
   set_parm(mp_relperm_Swd, g_mp_relperm_Swd, FLOAT),
   set_parm(mp_pm_relperm_krfloor, g_mp_pm_relperm_krfloor, FLOAT),
   set_parm(mp_pm_krmpweight, g_mp_pm_krmpweight, INT),
   set_parm(mp_pm_cnt_screening_tanhcf, g_mp_pm_cnt_screening_tanhcf, FLOAT),
   set_parm(mp_pm_cnt_screening_switch, g_mp_pm_cnt_screening_switch, FLOAT),
   set_parm(mp_pm_cnt_screening_tanhcf_pp, g_mp_pm_cnt_screening_tanhcf_pp, FLOAT),
   set_parm(mp_pm_cnt_screening_switch_pp, g_mp_pm_cnt_screening_switch_pp, FLOAT),
   set_parm(mp_pm_cnt_screening_zero, g_mp_pm_cnt_screening_zero, FLOAT),
   set_parm(mp_pm_accom_switch_porosity, g_mp_pm_accom_switch_porosity, FLOAT),
   set_parm(mp_pm_accom_switch_width, g_mp_pm_accom_switch_width, FLOAT),
   set_parm(mp_pm_cnt_model_off_Swd, g_mp_pm_cnt_model_off_Swd, FLOAT),
   set_parm(mp_pm_detect_Swmax, g_mp_pm_detect_Swmax, FLOAT),
   set_parm(mp_pm_detect_Swmin, g_mp_pm_detect_Swmin, FLOAT),
   set_parm(mp_pm_pcbranch_op, g_mp_pm_pcbranch_op, INT),
   set_parm(mp_pm_pc_branchSw, g_mp_pm_pc_branchSw, FLOAT),
   set_parm(mp_pm_pc_branchcoefa, g_mp_pm_pc_branchcoefa, FLOAT),
   set_parm(mp_pm_pc_branchcoefb, g_mp_pm_pc_branchcoefb, FLOAT),
   set_parm(mp_pm_pc_branchcoefc, g_mp_pm_pc_branchcoefc, FLOAT),
   set_parm(mp_pm_cnt_model, g_mp_pm_cnt_model, INT),
   set_parm(mp_pm_cnt_model_tfilm, g_mp_pm_cnt_model_tfilm, INT),
   set_parm(mp_typpotential_c0, g_mp_typpotential_c0, FLOAT),
   set_parm(mp_typpotential_c1, g_mp_typpotential_c1, FLOAT),
   set_parm(mp_pm_pcresidual, g_mp_pm_pcresidual, INT),
   set_parm(mp_pm_pcresidual_oil, g_mp_pm_pcresidual_oil, INT),
   set_parm(mp_pm_pcresidual_mss, g_mp_pm_pcresidual_mss, INT),
   set_parm(mp_pm_pcresidual_release, g_mp_pm_pcresidual_release, INT),
   set_parm(mp_pm_pc_rsdlmd_Sw, g_mp_pm_pc_rsdlmd_Sw, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_imb_Sw, g_mp_pm_pc_rsdlmd_imb_Sw, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_watresolved_Sw, g_mp_pm_pc_rsdlmd_watresolved_Sw, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_oilresolved_Sw, g_mp_pm_pc_rsdlmd_oilresolved_Sw, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_Swd , g_mp_pm_pc_rsdlmd_Swd, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_critP_upper, g_mp_pm_pc_rsdlmd_critP_upper, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_critP_lower, g_mp_pm_pc_rsdlmd_critP_lower, FLOAT),
   set_parm(mp_pm_pc_rsdlmd_Sw_detect, g_mp_pm_pc_rsdlmd_Sw_detect, INT),
   set_parm(mp_relperm_Swres_used, g_mp_relperm_Swres_used, INT),



   set_parm(mp_pm_typ_inletprs, g_mp_pm_typ_inletprs, FLOAT),
   set_parm(mp_pm_typ_outletprs, g_mp_pm_typ_outletprs, FLOAT),
   set_parm(mp_rock_size, g_mp_rock_size, FLOAT),
   set_parm(mp_mx_released_ratio, g_mp_mx_released_ratio, FLOAT),
   set_parm(mp_pm_pc_rsdmarker, g_mp_pm_pc_rsdmarker, FLOAT),
   set_parm(mp_pm_resolution_um, g_mp_pm_resolution_um, FLOAT),
   set_parm(mp_pm_surftens_lat, g_mp_pm_surftens_lat, FLOAT),
   set_parm(mp_pm_surftens_dyncm_sim, g_mp_pm_surftens_dyncm_sim, FLOAT),
   set_parm(mp_pm_surftens_dyncm_inputpc, g_mp_pm_surftens_dyncm_inputpc, FLOAT),
   set_parm(mp_pm_contactangle_inputpc, g_mp_pm_contactangle_inputpc, FLOAT),
   set_parm(mp_pm_k0_d, g_mp_pm_k0_d, FLOAT),
   set_parm(mp_pm_k0_phip, g_mp_pm_k0_phip, FLOAT),
   set_parm(mp_pm_k0_tau, g_mp_pm_k0_tau, FLOAT),
   set_parm(mp_pm_table_input, g_mp_pm_table_input, INT),
   set_parm(mp_pm_pc_lowlimitSw, g_mp_pm_pc_lowlimitSw, FLOAT),
   set_parm(mp_pm_pc_highlimitSw, g_mp_pm_pc_highlimitSw, FLOAT),
   set_parm(mp_pm_pc_coef, g_mp_pm_pc_coef, FLOAT),
   set_parm(mp_pm_pc_coef_exp, g_mp_pm_pc_coef_exp, FLOAT),
   set_parm(mp_pm_pc_coef_expln, g_mp_pm_pc_coef_expln, FLOAT),
   set_parm(mp_pm_pc_smpld_prsty, g_mp_pm_pc_smpld_prsty, FLOAT),
   set_parm(mp_pm_pc_shft, g_mp_pm_pc_shft, FLOAT),
   set_parm(mp_pm_pc_cap, g_mp_pm_pc_cap, FLOAT),
   set_parm(mp_pm_kr_resw, g_mp_pm_kr_resw, FLOAT),
   set_parm(mp_pm_kr_resa, g_mp_pm_kr_resa, FLOAT),
   set_parm(mp_pm_krw_power, g_mp_pm_krw_power, FLOAT),
   set_parm(mp_pm_kra_power, g_mp_pm_kra_power, FLOAT),
   set_parm(mp_pm_krw0, g_mp_pm_krw0, FLOAT),
   set_parm(mp_pm_kra0, g_mp_pm_kra0, FLOAT),
   set_parm(mp_pm_resistance_cap, g_mp_pm_resistance_cap, FLOAT),
   set_parm(mp_pm_scale_por_srften_cntangle, g_mp_pm_scale_por_srften_cntangle, INT),
   set_parm(mp_pm_rsdlmd_iniskip, g_mp_pm_rsdlmd_iniskip, INT),
   set_parm(mp_pm_wetbranch_op, g_mp_pm_wetbranch_op, INT),
   set_parm(mp_pm_pcmap_cnt, g_mp_pm_pcmap_cnt, INT),
   set_parm(mp_pm_pcmap_surf, g_mp_pm_pcmap_surf, INT),

   set_parm(mp_pm_tensor_k0, g_mp_pm_tensor_k0, INT),
   set_parm(mp_pm_tensor_k0_simplified, g_mp_pm_tensor_k0_simplified, INT),
   set_parm(mp_pm_tensor_k0_eig_method, g_mp_pm_tensor_k0_eig_method, INT),
   //LB_UDS
   set_parm_v(mp_uds_carrier, g_mp_uds_carrier, INT, MAX_N_USER_DEFINED_SCALARS), 

#endif

   //make compiler happy
   set_parm(mp_pm_table_input_contact_angle, g_mp_pm_table_input_contact_angle, INT),
   set_parm(pfc_enable_shell_conduction, g_pfc_enable_shell_conduction, INT),
   set_parm(pfc_disable_thermal_coupling, g_pfc_disable_thermal_coupling, INT),
   set_parm(pfc_cfl_violation_warning_only, g_pfc_cfl_violation_warning_only, INT),
   set_parm(pfc_time_coupling_num_phases, g_pfc_time_coupling_num_phases, INT),
   set_parm(pfc_staggered_coupling_flow_duration_in_ts, g_pfc_staggered_coupling_flow_duration_in_ts, INT),
   set_parm(pfc_staggered_coupling_flow_avg_period_in_ts, g_pfc_staggered_coupling_flow_avg_period_in_ts, INT),
   set_parm(pfc_staggered_coupling_conduction_duration_in_ts, g_pfc_staggered_coupling_conduction_duration_in_ts, INT),
   set_parm(pfc_staggered_coupling_conduction_avg_period_in_ts, g_pfc_staggered_coupling_conduction_avg_period_in_ts, INT),
   
   set_parm(pfc_temp_sample_underrelaxation_factor, g_pfc_temp_sample_underrelaxation_factor, FLOAT),
   set_parm(pfc_heat_flux_underrelaxation_factor, g_pfc_heat_flux_underrelaxation_factor, FLOAT),
   set_parm(pfc_interface_flux_underrelaxation_factor, g_pfc_interface_flux_underrelaxation_factor, FLOAT),
   set_parm(pfc_use_explicit_temp_bc, g_pfc_use_explicit_temp_bc, INT),

//Conduction solver parameters:
#ifdef DISABLE_EXA_TURB_INIT_CONTROLS_FOR_CONDUCTION_SOLVER_PARAMETERS
#else
   set_parm(pfc_surface_adiabatic_treatment, g_pfc_surface_adiabatic_treatment, INT),
   set_parm(pfc_use_conventional_temp_bc, g_pfc_use_conventional_temp_bc, INT),
   set_parm(pfc_temp_bc_ls_contribution_like_q_bc, g_pfc_temp_bc_ls_contribution_like_q_bc, INT),
   set_parm(pfc_passthrough_along_grad_t, g_pfc_passthrough_along_grad_t, INT),
   set_parm(pfc_use_approximate_weights, g_pfc_use_approximate_weights, INT),
   set_parm(pfc_temp_bc_surfel_grad_t_form, g_pfc_temp_bc_surfel_grad_t_form, INT),
   set_parm(pfc_max_surfels_allowed_in_stencil, g_pfc_max_surfels_allowed_in_stencil, INT),
   set_parm(pfc_interface_ls_contribution_like_q_bc, g_pfc_interface_ls_contribution_like_q_bc, INT),
   set_parm(pfc_lrf_ls_contribution_like_q_bc, g_pfc_lrf_ls_contribution_like_q_bc, INT),
   set_parm(pfc_use_actual_sampling_distance_for_temp_bc, g_pfc_use_actual_sampling_distance_for_temp_bc, INT),
   set_parm(pfc_temp_caps_option, g_pfc_temp_caps_option, INT),
   set_parm(pfc_use_sampling_across_materials_for_temp, g_pfc_use_sampling_across_materials_for_temp, INT),
   set_parm(pfc_passthrough_terms_option, g_pfc_passthrough_terms_option, INT),
   set_parm(pfc_nonparallel_s2s_source_scaledown, g_pfc_nonparallel_s2s_source_scaledown, INT),
   set_parm(pfc_quasi_1d_source_form, g_pfc_quasi_1d_source_form, INT),
   set_parm(pfc_quasi_1d_source_use_s2s, g_pfc_quasi_1d_source_use_s2s, INT),
   set_parm(pfc_even_odd_surfel_v2s_use_vr_fine, g_pfc_even_odd_surfel_v2s_use_vr_fine, INT),
   set_parm(pfc_regular_surfel_v2s_use_vr_fine, g_pfc_regular_surfel_v2s_use_vr_fine, INT),
   set_parm(pfc_quasi_1d_solve_steady_state, g_pfc_quasi_1d_solve_steady_state, INT),
   set_parm(matrix_determinant_threshold, g_matrix_determinant_threshold, FLOAT),
   set_parm(pfc_kappa_floor, g_pfc_kappa_floor, FLOAT),
   set_parm(pfc_kappa_ceiling, g_pfc_kappa_ceiling, FLOAT),
   set_parm(pfc_T_underrelax, g_pfc_T_underrelax, FLOAT),
   set_parm(pfc_ls_dist_inner, g_pfc_ls_dist_inner, FLOAT),
   set_parm(pfc_ls_dist_outer, g_pfc_ls_dist_outer, FLOAT),
   set_parm(pfc_shell_ls_dist_inner, g_pfc_ls_dist_inner, FLOAT),
   set_parm(pfc_shell_ls_dist_outer, g_pfc_ls_dist_outer, FLOAT),
   set_parm(pfc_damping_correction, g_pfc_damping_correction, FLOAT),
   set_parm(pfc_damping_pfrat_threshold, g_pfc_damping_pfrat_threshold, FLOAT),
   set_parm(pfc_damping_pfluid_threshold, g_pfc_damping_pfluid_threshold, FLOAT),
   set_parm(pfc_passthrough_allowed_deviation_from_avg, g_pfc_passthrough_allowed_deviation_from_avg, FLOAT),
   set_parm(pfc_temp_caps_opt1_tiny_pfluid_threshold, g_pfc_temp_caps_opt1_tiny_pfluid_threshold, FLOAT),
   set_parm(pfc_temp_caps_opt1_overall_pfluid_threshold, g_pfc_temp_caps_opt1_overall_pfluid_threshold, FLOAT),
   set_parm(pfc_temp_caps_opt2_tiny_pfluid_threshold, g_pfc_temp_caps_opt2_tiny_pfluid_threshold, FLOAT),
   set_parm(pfc_temp_caps_opt2_overall_pfluid_threshold, g_pfc_temp_caps_opt2_overall_pfluid_threshold, FLOAT),
   set_parm(pfc_approximate_pgram_dist_max, g_pfc_approximate_pgram_dist_max, FLOAT),
   set_parm(pfc_shell_conduction_stencil_size, g_pfc_shell_conduction_stencil_size, FLOAT),
   set_parm(pfc_ls_surfel_info_dirichlet_factor, g_pfc_ls_surfel_info_dirichlet_factor, FLOAT),
   //moved below out for testing
   //set_parm(pfc_temp_sample_underrelaxation_factor, g_pfc_temp_sample_underrelaxation_factor, FLOAT),
   //set_parm(pfc_heat_flux_underrelaxation_factor, g_pfc_heat_flux_underrelaxation_factor, FLOAT),
   //set_parm(pfc_interface_flux_underrelaxation_factor, g_pfc_interface_flux_underrelaxation_factor, FLOAT),
   set_parm(pfc_grad_t_dot_n_underrelaxation_factor, g_pfc_grad_t_dot_n_underrelaxation_factor, FLOAT),
   set_parm(pfc_sampling_distance_without_s2s_floor, g_pfc_sampling_distance_without_s2s_floor, FLOAT),
   set_parm(pfc_sampling_distance_with_s2s_floor, g_pfc_sampling_distance_with_s2s_floor, FLOAT),
   set_parm(pfc_shell_conduction_coplanar_threshold, g_pfc_shell_conduction_coplanar_threshold, FLOAT),
   set_parm(pfc_ls_neumann_weight_temp_bc_surfel_factor, g_pfc_ls_neumann_weight_temp_bc_surfel_factor, FLOAT),
   set_parm(pfc_ls_neumann_weight_flux_bc_surfel_factor, g_pfc_ls_neumann_weight_flux_bc_surfel_factor, FLOAT),
   set_parm(pfc_ls_neumann_weight_thermal_resistance_bc_surfel_factor, g_pfc_ls_neumann_weight_thermal_resistance_bc_surfel_factor, FLOAT),
   set_parm(pfc_ls_neumann_weight_interface_surfel_factor, g_pfc_ls_neumann_weight_interface_surfel_factor, FLOAT),
   set_parm(pfc_ls_neumann_weight_lrf_surfel_factor, g_pfc_ls_neumann_weight_lrf_surfel_factor, FLOAT),
   set_parm(pfc_s2s_weight_scaling_factor, g_pfc_s2s_weight_scaling_factor, FLOAT),
#endif
   set_parm(measure_conduction_passthrough, g_measure_conduction_passthrough, INT),
   set_parm(shell_tangential_measurements, g_shell_tangential_measurements, INT),
   set_parm(pfc_shell_passthrough_range_scale_value, g_pfc_shell_passthrough_range_scale_value, FLOAT),
   set_parm(pfc_shell_passthrough_range_area_cutoff, g_pfc_shell_passthrough_range_area_cutoff, FLOAT),
   set_parm(pfc_shell_orthogonal_correction_limiter, g_pfc_shell_orthogonal_correction_limiter, FLOAT),
   set_parm(pfc_shell_orthogonal_correction_limiter_max_area, g_pfc_shell_orthogonal_correction_limiter_max_area, FLOAT),
   set_parm(pfc_shell_orthogonal_correction_limiter_min_area, g_pfc_shell_orthogonal_correction_limiter_min_area, FLOAT),
   set_parm(pfc_orthogonal_correction_limiter, g_pfc_orthogonal_correction_limiter, FLOAT),
   set_parm(pfc_orthogonal_correction_limiter_max_volume, g_pfc_orthogonal_correction_limiter_max_volume, FLOAT),
   set_parm(pfc_orthogonal_correction_limiter_min_volume, g_pfc_orthogonal_correction_limiter_min_volume, FLOAT),
   set_parm(pfc_shell_LSQ_stencil_max_neighbor_order, g_pfc_shell_LSQ_stencil_max_neighbor_order, INT),
   set_parm(pfc_shell_passthrough_distribution, g_pfc_shell_passthrough_distribution, INT),
   set_parm(pfc_shell_passthrough_on, g_pfc_shell_passthrough_on, INT),
   set_parm(pfc_shell_passthrough_averaging_on, g_pfc_shell_passthrough_averaging_on, INT),
   set_parm(pfc_print_shell_mesh_statistics, g_pfc_print_shell_mesh_statistics, INT),
   set_parm(pfc_print_shell_mesh_connectivity, g_pfc_print_shell_mesh_connectivity, INT),
   set_parm(pfc_output_shell_mesh_info, g_pfc_output_shell_mesh_info, INT),
   set_parm(pfc_shell_non_orthogonality_threshold, g_pfc_shell_non_orthogonality_threshold, FLOAT),
   set_parm(pfc_shell_skewness_threshold, g_pfc_shell_skewness_threshold, FLOAT),
   set_parm(pfc_output_shell_stencil_details, g_pfc_output_shell_stencil_details, INT),
   set_parm(shell_implicit_solver, g_shell_implicit_solver, INT),
   set_parm(shell_implicit_solver_relative_tolerance, g_shell_implicit_solver_relative_tolerance, FLOAT),
   set_parm(shell_implicit_solver_verbosity, g_shell_implicit_solver_verbosity, INT),
   set_parm(shell_implicit_solver_use_protectors, g_shell_implicit_solver_use_protectors, INT),
   set_parm(shell_implicit_solver_guard_secondary_gradients, g_shell_implicit_solver_guard_secondary_gradients, INT),
   set_parm(shell_implicit_solver_make_edge_htc_bc_adiabatic, g_shell_implicit_solver_make_edge_htc_bc_adiabatic, INT),
   set_parm(solid_implicit_solver, g_solid_implicit_solver, INT),
   set_parm(solid_implicit_solver_relative_tolerance, g_solid_implicit_solver_relative_tolerance, FLOAT),
   set_parm(solid_implicit_solver_verbosity, g_solid_implicit_solver_verbosity, INT)
};

#define N_PHYSICS_PARMS (sizeof(physics_parms) / sizeof(sPHYSICS_PARM))
#define PARM_TYPE_INT 0
#define PARM_TYPE_FLOAT 1

/* This routine reads the optional input turbulence parameters. */
static VOID read_turb_parm_init_internal (const char * input_file) 
{
  const asINT32 LINE_BUFFER_SIZE = 160;
  char in_line[LINE_BUFFER_SIZE];
  const char *delim = " ,\t\r\n";

  FILE *dfp = fopen(input_file, "r");

#if BUILD_5G_LATTICE
  if (!strcmp(input_file, "user_input.init")) {
    ccDOTIMES(itry, 10) {
      if (dfp != NULL)
        break;
      else {
        sleep(5);
        msg_warn("%s not found try %d of 10", input_file, itry+1);
        dfp = fopen(input_file, "r");
      }
    }
    if (dfp == NULL)
      msg_internal_error("%s not found after 10 tries", input_file);
  }
#endif

  if (dfp != NULL) {
#if BUILD_5G_LATTICE
    msg_warn ("Initializing user input parameters from file %s. \n", input_file);
#else
    msg_warn ("Initializing turbulence parameters from file %s. \n", input_file);
#endif

    while (fgets(in_line, LINE_BUFFER_SIZE-1, dfp) != NULL) {
      if (in_line[0] == '#' ) {
        continue;
      } else if (in_line[0] == '$') {
        printf("A $ sign is found, closing the %s file.\n", input_file);
        fclose (dfp);
        return;
      } else {
        //sscanf(in_line, "%s %s %s", parm_type, parm_name, parm_value);
	char *parm_type = strtok(in_line, delim);
	if (parm_type == NULL) //empty line
	  continue;

	char *parm_name = strtok(NULL, delim);

        PHYSICS_PARM physics_parm = (PHYSICS_PARM)&physics_parms;
        BOOLEAN get_string;
        for (asINT32 nth_parm = 0; nth_parm < N_PHYSICS_PARMS; nth_parm++) {
          if (strcmp(parm_name, physics_parm->name) != 0) {
            get_string = FALSE;
            physics_parm ++;
          } else {
            get_string = TRUE;
            break;
          }
        }
        if (get_string) {
          if (!((strcmp(parm_type, "int") == 0 || strcmp(parm_type, "INT") == 0) && (physics_parm->type == PARM_INT)) && !((strcmp(parm_type, "float") == 0 || strcmp(parm_type, "FLOAT") == 0) && (physics_parm->type == PARM_FLOAT))) {
            msg_error ("Parameter type mismatch %s %s\n", parm_name, parm_type);
          }
          
          char *parm_value;
          sdFLOAT *fvalue = (sdFLOAT*)physics_parm->addr;
          int     *ivalue = (int*)physics_parm->addr;
          asINT32 n_elems = physics_parm->n_elems;
        
          for (asINT32 i=0; (parm_value = strtok(NULL, delim)) != NULL; i++) {
            if (i == n_elems){
              msg_warn ("Excessive values of variable %s ignored", parm_name);
              break;
                  }
        
            switch (physics_parm->type) {
            case PARM_FLOAT:
              fvalue[i] = atof(parm_value);
              printf("%s = %f \n", parm_name, fvalue[i]);
              break;
            case PARM_INT:
              ivalue[i] = atol(parm_value);
              printf("%s = %d \n", parm_name, ivalue[i]);
              break;
            }
          }
        } else {
          printf("Unknown string and value is found:-\n%s\n", parm_name);
          fclose (dfp);
          printf("Terminated during initialization.\n");
          exit(1);
        }
      }
    }
    printf("End of file reached, closing the %s file.\n",  input_file);
    fclose (dfp);
  }

  if (dfp != NULL) {
    g_one_over_pfluid_crit = 1.0 / (dFLOAT)g_pfluid_crit;
    g_one_over_eta_crit = 1.0 / (dFLOAT)g_eta_crit;
    g_one_over_eta_swirl_alpha = 1.0 / (dFLOAT)g_eta_swirl_alpha;
    g_one_over_turb_Prandtl_wall = 1.0 / g_turb_Prandtl_wall;

#if BUILD_5G_LATTICE
    if (g_mp_emss_sink_alpha[0]>1.0F || g_mp_emss_sink_alpha[0]<0.0F)
      msg_error("mp_emss_sink_alpha = %f is out of range. Acceptable range is [0,1).", g_mp_emss_sink_alpha[0]);
    if (g_mp_emss_sink_alpha[1]>1.0F || g_mp_emss_sink_alpha[1]<0.0F)
      msg_error("mp_emss_sink_alpha = %f is out of range. Acceptable range is [0,1).", g_mp_emss_sink_alpha[1]);
#endif  
  }
  return;

}


#if BUILD_5G_LATTICE
static VOID expand_user_input_internal ()
{
  switch (g_mp_multiphase_type) {
    case IDEAL_IDEAL:
      //mp_num_components = 2;
      //mp_eos[0]         = 6;
      //mp_eos[1]         = 6;
      g_mp_G[0][0] = 0.;
      g_mp_G[0][1] = -10.;
      g_mp_G[1][0] = -10.;
      g_mp_G[1][1] = 0.;
      break;
    case PR_PR:
      //mp_num_components = 2;
      //mp_eos[0]         = 3;
      //mp_eos[1]         = 3;
      g_mp_G[0][0] = 1.;
      g_mp_G[0][1] = -0.;
      g_mp_G[1][0] = -0.;
      g_mp_G[1][1] = 1.;
      break;
    case IDEAL:
      //mp_num_components = 1;
      //mp_eos[0]         = 6;
      g_mp_G[0][0] = 0.;
      break;
    case PR:
      //mp_num_components = 1;
      //mp_eos[0]         = 3;
      g_mp_G[0][0] = 1.;
      break;
    case IDEAL_PR:
      //mp_num_components = 2;
      //mp_eos[0]         = 6;
      //mp_eos[1]         = 3;
      g_mp_G[0][0] = 0.;
      g_mp_G[0][1] = -10.;
      g_mp_G[1][0] = -10.;
      g_mp_G[1][1] = 1.;
      break;
    default:
      msg_error("Unknown mp_multiphase_type \n "); 
  } 
  return;
}

static VOID expand_turb_parm_internal ()
{
  switch (g_mp_multiphase_type) {
    case IDEAL_IDEAL:
      //mp_num_components = 2;
      /*mp_eos[0]         = 6;
      mp_eos[1]         = 6;
      mp_G[0][0] = 0.;
      mp_G[0][1] = -10.;
      mp_G[1][0] = -10.;
      mp_G[1][1] = 0.;*/
      break;
    case PR_PR:
      //sim.num_components  = 2;
      /*mp_eos[0]         = 3;
      mp_eos[1]         = 3;
      mp_G[0][0] = 1.;
      mp_G[0][1] = -0.;
      mp_G[1][0] = -0.;
      mp_G[1][1] = 1.;*/
      break;
    case IDEAL:
      //sim.num_components  = 1;
      /*mp_eos[0]         = 6;
        mp_G[0][0] = 0.;*/
      break;
    case PR:
      //sim.num_components  = 1;
      /*mp_eos[0]         = 3;
        mp_G[0][0] = 1.;*/
      break;
    case IDEAL_PR:
      //sim.num_components  = 2;
      /*mp_eos[0]         = 6;
      mp_eos[1]         = 3;
      mp_G[0][0] = 0.;
      mp_G[0][1] = -10.;
      mp_G[1][0] = -10.;
      mp_G[1][1] = 1.;*/
      break;
    default:
      msg_error("Unknown mp_multiphase_type \n "); 
  }

  if (g_mp_solver_type == STOKES_SOLVER_INDEX) {
    g_mp_protection_Ma = g_mp_protection_Ma_stokes;
    printf("Stokes solver parameter mp_protection_Ma_stokes = %f \n", g_mp_protection_Ma_stokes);
  } 
  return;
}

extern VOID read_time_depended_input (const char * input_file)
{
  read_turb_parm_init_internal (input_file);
  // time depended input file should not change components and G
  //expand_user_input_internal ();
  if (g_mc_types != NULL) {
    for (asINT32 c = 0; c < sim.num_components; ++c) {
      g_mc_types[c].update (c);
    }
  }

  return;
}
#endif

VOID read_turb_parm_init ()
{
#if BUILD_GPU  
  //Initialize turb globals explicitly with an init method before it's used downstream.
  //Using a constructor leads to undefined behavior of initialization of dependent struct members
  //such as g_density_scale_factor - PR 54731
  ::g.init();
#endif
  
#if BUILD_5G_LATTICE
  read_turb_parm_init_internal ("user_input.init");
  expand_user_input_internal ();

  read_turb_parm_init_internal ("exa_turb_5g.init");
  expand_turb_parm_internal ();
#elif BUILD_D19_LATTICE
  read_turb_parm_init_internal ("exa_turb_d19.init");
#elif BUILD_D39_LATTICE
  read_turb_parm_init_internal ("exa_turb_d39.init");
#elif  BUILD_D34_LATTICE
  read_turb_parm_init_internal ("exa_turb_d34.init");
#endif

  return;
}

