#include "gpu_exprlang.h"
#include "surfel_dyn_sp.h"

#if DEVICE_COMPILATION_MODE

__DEVICE__ float get_exprlang_dev_data_value(GPU::EXPRLANG_DEVICE_DATA* d, uINT32 var_index)
{
  return d->var_value(var_index);
}

__DEVICE__ bool test_exprlang_mask(EXPRLANG_VAR_MASK& mask, int i)
{
  return  mask.test(i);
}

template < typename PARAMS_T>
__DEVICE__ VOID sSURFEL_BC_VALUES<PARAMS_T>::read_exprlang_device_data(size_t msfl_id) {
  auto soxor = get_dyn_soxor();
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  cassert(d->has_space_or_time_varying_var_data());
  auto mask = d->exprlang_var_mask(blockIdx.x);
  uINT32 msfl_var_start_index = d->var_start_index(blockIdx.x);
  uINT32 soxor_var_index = msfl_var_start_index + soxor;
  int n_exprlang_bits = 0;
  int i=0;
  set_var_recursive(i, soxor_var_index, n_exprlang_bits, d, mask);
}

template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_SLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_SLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sVEL_SLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sNOSLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sNOSLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sNOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sNOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_NOSLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_NOSLIP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<IO_UDS_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sMASS_FLUX_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sMASS_FLOW_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sFIXED_VEL_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSOURCE_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sPASS_THRU_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sPRESSURE_FREE_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSTATIC_PRESSURE_FIXED_DIR_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sSOURCE_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sMASS_FLUX_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sNOSLIP_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sANGULAR_NOSLIP_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);
template
__DEVICE__ VOID sSURFEL_BC_VALUES<sLINEAR_NOSLIP_5G_SURFEL_PARAMETERS*>::read_exprlang_device_data(size_t msfl_id);

#endif
