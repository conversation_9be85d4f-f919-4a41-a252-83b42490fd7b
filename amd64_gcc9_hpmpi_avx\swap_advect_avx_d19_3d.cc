#include "../common_sp.h"
#include "../swap_advect.h"
#include "../box_advect_gen_headers.h"
template <SWAP_ADVECT_TEMPLATE_PARAMETERS>
static INLINE 
 VOID d19_avx_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { 

const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);
constexpr asINT32 voxor = 0;
//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,0}; 
    { //SELF
      sUBLK*  src_ublk = pde_advect_ublk;
      constexpr BOOLEAN src_tagged_ublk_has_two_copies = FALSE;
        {
          constexpr asINT32 latvec = 0;
          constexpr auINT8 src_voxel_mask = 0b00001111;
          const     __m256i src_perm_ind = PERM_IND_PS(3, 2, 1, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 7, 6, 5, 4);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 2;
          constexpr auINT8 src_voxel_mask = 0b00110011;
          const     __m256i src_perm_ind = PERM_IND_PS(5, 4, 5, 4, 1, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11001100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 7, 6, 3, 2, 3, 2);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 4;
          constexpr auINT8 src_voxel_mask = 0b01010101;
          const     __m256i src_perm_ind = PERM_IND_PS(6, 6, 4, 4, 2, 2, 0, 0);
          constexpr auINT8 dst_voxel_mask = 0b10101010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 7, 5, 5, 3, 3, 1, 1);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00001100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00000011;
          const     __m256i src_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b10100000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          constexpr auINT8 dst_voxel_mask = 0b00100010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10001000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[0] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[0];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 1;
          constexpr auINT8 src_voxel_mask = 0b00001111;
          const     __m256i src_perm_ind = PERM_IND_PS(3, 2, 1, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 7, 6, 5, 4);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b00000011;
          const     __m256i src_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00001100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10100000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b00001010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[1] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[1];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 0;
          constexpr auINT8 src_voxel_mask = 0b11110000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 7, 6, 5, 4);
          constexpr auINT8 dst_voxel_mask = 0b00001111;
          const     __m256i dst_perm_ind = PERM_IND_PS(3, 2, 1, 0, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b11000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000011;
          const     __m256i dst_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00110000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr auINT8 src_voxel_mask = 0b10100000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[2] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[2];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 3;
          constexpr auINT8 src_voxel_mask = 0b00110011;
          const     __m256i src_perm_ind = PERM_IND_PS(5, 4, 5, 4, 1, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11001100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 7, 6, 3, 2, 3, 2);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00000011;
          const     __m256i src_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00110000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10001000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr auINT8 src_voxel_mask = 0b00100010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[3] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[3];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 2;
          constexpr auINT8 src_voxel_mask = 0b11001100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 7, 6, 3, 2, 3, 2);
          constexpr auINT8 dst_voxel_mask = 0b00110011;
          const     __m256i dst_perm_ind = PERM_IND_PS(5, 4, 5, 4, 1, 0, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b11000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000011;
          const     __m256i dst_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00001100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr auINT8 src_voxel_mask = 0b10001000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          constexpr auINT8 dst_voxel_mask = 0b00100010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[4] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[4];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 5;
          constexpr auINT8 src_voxel_mask = 0b01010101;
          const     __m256i src_perm_ind = PERM_IND_PS(6, 6, 4, 4, 2, 2, 0, 0);
          constexpr auINT8 dst_voxel_mask = 0b10101010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 7, 5, 5, 3, 3, 1, 1);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10100000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10001000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          constexpr auINT8 dst_voxel_mask = 0b00100010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,-1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[5] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[5];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 4;
          constexpr auINT8 src_voxel_mask = 0b10101010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 7, 5, 5, 3, 3, 1, 1);
          constexpr auINT8 dst_voxel_mask = 0b01010101;
          const     __m256i dst_perm_ind = PERM_IND_PS(6, 6, 4, 4, 2, 2, 0, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b10100000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr auINT8 src_voxel_mask = 0b00001010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b10001000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b00100010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[6] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[6];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b00001100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00110000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[7] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[7];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00110000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 5, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 3, 2, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[8] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[8];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00000011;
          const     __m256i src_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b11000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[9] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[9];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b11000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 7, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000011;
          const     __m256i dst_perm_ind = PERM_IND_PS(1, 0, 5, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[10] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[10];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 11;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          constexpr auINT8 dst_voxel_mask = 0b00001010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,-1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[11] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[11];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b00001010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 3, 5, 1, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 6, 2, 4, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,-1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[12] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[12];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 13;
          constexpr auINT8 src_voxel_mask = 0b10100000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[13] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[13];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(2, 6, 0, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10100000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 7, 1, 5);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[14] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[14];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 15;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          constexpr auINT8 dst_voxel_mask = 0b00100010;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,-1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[15] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[15];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b00100010;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 5, 5, 4, 3, 1, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 6, 4, 3, 2, 2, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[16] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[16];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 17;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b10001000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,-1}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[17] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[17];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b10001000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 7, 3, 2, 1, 3);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(4, 6, 5, 4, 0, 2, 1, 0);
          SWAP_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,-1}; 

swap_along_latvec_pairs<ADVECT_TEMP, ADVECT_UDS>(states,states_t,states_mc, states_uds);
}

