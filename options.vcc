SOURCE_ARCHIVABLE

GROUP_TARGET_USER exa_sim_sp exa_sim_sp_*mpi
GROUP_SOURCE_SWDEV master.mak

RELEASE_TARGET_SAVE Makefile exa_sim_sp* simulator_simsizes_lib* simsizes_define_once.a print_pmodel .exa_defines.h .vmake_status

RELEASE_TARGET_REMOVE exa_sim_sp.o *.json

RELEASE_SOURCE_REMOVE *~ .git .tags .tags_sorted_by_file .gitignore *.orig unittests/*/* compile_commands.json

# Tests live in this component
EXATEST_INFO Simulator smoketests
EXATEST_DISTRIBUTIONS powerflow
EXATEST_TESTCOMP physics_coll_tests/basicPhysics

