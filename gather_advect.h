/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Gather advect API
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Mukul Rao, Exa Corporation                      Created Mon, May 2018
//----------------------------------------------------------------------------
//
#ifndef GATHER_ADVECT_H_
#define GATHER_ADVECT_H_

#include "box_advect.h"

inline namespace ADVECT_VECTORIZED {
template <BOOLEAN IS_VR_FINE, BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
VOID gather_advect_for_unsplit_neighbors(sUBLK *pde_advect_ublk,
                                         VOXEL_MASK_8 voxel_mask,
                                         UBLK (&active_nbrs) [N_MOVING_STATES],
                                         BOOLEAN is_timestep_even,
                                         SOLVER_INDEX_MASK prior_solver_index_mask,
                                         ACTIVE_SOLVER_MASK solver_mask);

}

inline namespace ADVECT_SCALAR {
template <BOOLEAN IS_VR_FINE, BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
VOID gather_advect_for_unsplit_neighbors(sUBLK *pde_advect_ublk,
                                         VOXEL_MASK_8 voxel_mask,
                                         UBLK (&active_nbrs) [N_MOVING_STATES],
                                         BOOLEAN is_timestep_even,
                                         SOLVER_INDEX_MASK prior_solver_index_mask,
                                         ACTIVE_SOLVER_MASK solver_mask);

}

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
VOID gather_advect_split(sUBLK *pde_advect_ublk,
                         VOXEL_MASK_8 voxel_mask,
                         asINT32 split_instance_index,
                         BOOLEAN is_timestep_even,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         ACTIVE_SOLVER_MASK active_solver_mask);


template <BOOLEAN IS_VR_FINE, BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
VOID gather_advect(sUBLK *pde_advect_ublk,
                   VOXEL_MASK_8 voxel_mask,
                   BOOLEAN is_timestep_even,
                   SOLVER_INDEX_MASK prior_solver_index_mask,
                   ACTIVE_SOLVER_MASK active_solver_mask);

INLINE
static BOOLEAN is_ublk_valid_for_split_advect(TAGGED_UBLK t_ublk, bool is_timestep_even)
{
  return (!t_ublk.is_ublk_scale_interface() && !t_ublk.is_null() &&
          !t_ublk.is_ublk_coarse() &&
          (t_ublk.is_ublk_split() || 
           (t_ublk.ublk()->fluid_like_voxel_mask.any() &&
            t_ublk.ublk()->need_to_push_states(is_timestep_even)))
         );
}

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
#if DEBUG
static
#else
static INLINE
#endif
VOID push_states_to_swap_advect_neighbor(const asINT32 n_src_voxels,
                                         const asINT32 dest_voxels[],
                                         const asINT32 src_voxels[],
                                         VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],
                                         VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS],
                                         VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS],
					 VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                         VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                                         VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS],
                                         VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS],
					 VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                         asINT32 latvec)
{
  asINT32 parity = state_parity(latvec);
  for (asINT32 v=0; v < n_src_voxels; v++) {
    src_states[latvec][src_voxels[v]] = p_states[parity][dest_voxels[v]];
    if (g_is_multi_component) {
      src_states_mc[latvec][src_voxels[v]] = p_states_mc[parity][dest_voxels[v]];
    }
    if (ADVECT_TEMP) {
      src_states_t[latvec][src_voxels[v]] = p_states_t[parity][dest_voxels[v]];
    }
    if (ADVECT_UDS) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	src_states_uds[nth_uds][latvec][src_voxels[v]] = p_states_uds[nth_uds][parity][dest_voxels[v]];
    }
  }
}

template <BOOLEAN IS_VR_FINE, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
#if DEBUG
static
#else
static INLINE
#endif
VOID append_v2v_contribution(const asINT32 n_src_voxels, UBLK pde_advect_ublk,
                             const asINT32 dest_voxels[],
                             const asINT32 src_voxels[],
                             VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],
			     VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS],
			     VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],
                             STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],
                             STP_GEOM_VARIABLE pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],
                             asINT32 latvec, BOOLEAN use_vrf_pas)
{
  sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
  if (IS_VR_FINE && use_vrf_pas) {
    if (!HAS_S2V) {
      // XDU: This function is called with HAS_S2V=1 for All VR fine ublks.
      msg_internal_error("This is never called!");
      for (asINT32 v=0; v < n_src_voxels; v++) {
        states[latvec][dest_voxels[v]]  = src_states[latvec][src_voxels[v]];
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]]  = src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
          states_t[latvec][dest_voxels[v]]  = src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]]  = src_states_uds[nth_uds][latvec][src_voxels[v]];
	}
      }
    } else {
      for (asINT32 v=0; v < n_src_voxels; v++) {
#ifdef DEBUG_CONDUCTION_SOLVER
	if (pde_advect_ublk->id()==49&&dest_voxels[v]==0&&latvec==19){
	  msg_print("T %ld before:", g_timescale.m_time); 
	  msg_print("states %g", states[latvec][dest_voxels[v]]);
	  msg_print("vrf_pas %g, src_states %g",vrf_pas_factors[latvec][dest_voxels[v]] ,src_states[latvec][src_voxels[v]]);
	}
#endif
        states[latvec][dest_voxels[v]] += vrf_pas_factors[latvec][dest_voxels[v]] * src_states[latvec][src_voxels[v]];
#ifdef DEBUG_CONDUCTION_SOLVER
	if (pde_advect_ublk->id()==49&&dest_voxels[v]==0&&latvec==19){
	  msg_print("after: states %g", states[latvec][dest_voxels[v]]);
	}
#endif
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]] += vrf_pas_factors[latvec][dest_voxels[v]] * src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
          states_t[latvec][dest_voxels[v]] += vrf_pas_factors[latvec][dest_voxels[v]] * src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]] += vrf_pas_factors[latvec][dest_voxels[v]] * src_states_uds[nth_uds][latvec][src_voxels[v]];
	}
      }
    }
  } else {

    if (!HAS_S2V) {
      for (asINT32 v=0; v < n_src_voxels; v++) {
        if (IS_VR_FINE) {
          states[latvec][dest_voxels[v]]  = src_states[latvec][src_voxels[v]];
        } else {
#if BUILD_5X_SOLVER
          states[latvec][dest_voxels[v]]  = fractional_factor * p_states[latvec][dest_voxels[v]] +
                                            src_states[latvec][src_voxels[v]];
#else
          cassert(g_adv_fraction == 1.0f);
          states[latvec][dest_voxels[v]]  = src_states[latvec][src_voxels[v]];
#endif

        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]]  = src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
          states_t[latvec][dest_voxels[v]]  = src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]]  = src_states_uds[nth_uds][latvec][src_voxels[v]];
	}
      }
    } else {
      for (asINT32 v=0; v < n_src_voxels; v++) {
#ifdef DEBUG_CONDUCTION_SOLVER
	if (((pde_advect_ublk->id()==49&&dest_voxels[v]==0)||(pde_advect_ublk->id()==2034&&dest_voxels[v]==5))&&latvec==19){
	  msg_print("T %ld U %d before:", g_timescale.m_time, pde_advect_ublk->id()); 
	  msg_print("dest_voxel %d, states %g", dest_voxels[v], states[latvec][dest_voxels[v]]);
	  msg_print("src_voxel %d, src_states %g",src_voxels[v] ,src_states[latvec][src_voxels[v]]);
	}
#endif
        states[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states[latvec][src_voxels[v]];
#ifdef DEBUG_CONDUCTION_SOLVER
	if (((pde_advect_ublk->id()==49&&dest_voxels[v]==0)||(pde_advect_ublk->id()==2034&&dest_voxels[v]==5))&&latvec==19){
	  msg_print("after: dest_states %g", states[latvec][dest_voxels[v]]);
	}
#endif
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
          states_t[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_uds[nth_uds][latvec][src_voxels[v]];
	}
      }
    }
  }
}


template <BOOLEAN IS_VR_FINE>
#ifdef DEBUG
static
#else
static INLINE
#endif
VOID update_states_t(UBLK pde_advect_ublk, sdFLOAT pas_factor,
                     asINT32 latvec, asINT32 voxel,
                     VOXEL_STATE in_states_t,
                     VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS]) {
  // the exploded and coalesced states can be 0 so this check is
  // disabled for VR coarse and VR fine voxels.
  // Entropy states are always negative.
  if (!IS_VR_FINE && g_s2v_states_protection && !pde_advect_ublk->is_vr_coarse() &&
      (sim.T_solver_type == LB_TEMPERATURE)) {
    sdFLOAT inverse_pas;
    if (in_states_t < sim.sim_min_temp || in_states_t > sim.sim_max_temp) {
      if (pas_factor < 0.999) {
        inverse_pas = 1.0 / (1.0 - pas_factor);
        states_t[latvec][voxel] *= inverse_pas;
#if PERFORMANCE_TEST
        if (!pde_advect_ublk->are_any_neighbors_split(voxel)) {
          printf("temperature out of bounds pas %e l %d V %d U %d in_state %e\n",
              pas_factor, latvec, voxel, pde_advect_ublk->id(), in_states_t);
        }
#endif
      } else {
        states_t[latvec][voxel] += sim.char_temp * pas_factor;
      }
    } else {
      states_t[latvec][voxel] += pas_factor * in_states_t;
    }
  } else {
    states_t[latvec][voxel] += pas_factor * in_states_t;
  }
  if (!IS_VR_FINE && !pde_advect_ublk->is_vr_coarse() &&
       (sim.T_solver_type == LB_TEMPERATURE)) {
    if (states_t[latvec][voxel] < sim.sim_min_temp)
      states_t[latvec][voxel] = sim.sim_min_temp;
    else if (states_t[latvec][voxel] > sim.sim_max_temp)
      states_t[latvec][voxel] = sim.sim_max_temp;
  }
}

template <BOOLEAN IS_VR_FINE>
#ifdef DEBUG
static
#else
static INLINE
#endif
VOID update_states_uds(UBLK pde_advect_ublk, sdFLOAT pas_factor,
		       asINT32 latvec, asINT32 voxel, asINT32 nth_uds,
		       VOXEL_STATE in_states_uds,
		       VOXEL_STATE (*states_uds)[ubFLOAT::N_VOXELS]) {
  // the exploded and coalesced states can be 0 so this check is
  // disabled for VR coarse and VR fine voxels.
  // Entropy states are always negative.
  cassert (sim.uds_solver_type == LB_UDS);
#if !GPU_COMPILER  
  sdFLOAT max_uds = g_scalar_materials[nth_uds].uds_max_value;
  sdFLOAT min_uds = g_scalar_materials[nth_uds].uds_min_value;
#endif
  if (!IS_VR_FINE && !sim.is_pf_model && g_s2v_states_protection && !pde_advect_ublk->is_vr_coarse()) {
    sdFLOAT inverse_pas;
#if !GPU_COMPILER    
    if (in_states_uds < (min_uds - SFLOAT_EPSILON) || in_states_uds > max_uds) {
      if (pas_factor < (1 - SFLOAT_EPSILON)) {
        inverse_pas = 1.0 / (1.0 - pas_factor);
        states_uds[latvec][voxel] *= inverse_pas;
#if PERFORMANCE_TEST
        if (!pde_advect_ublk->are_any_neighbors_split(voxel)) {
          printf("UDS out of bounds pas %e l %d V %d U %d in_state %e\n",
              pas_factor, latvec, voxel, pde_advect_ublk->id(), in_states_uds);
        }
#endif
      } else {
	if (in_states_uds > max_uds) {
          states_uds[latvec][voxel] += max_uds * pas_factor;
#if defined(DEBUG)
	  msg_warn("Ublk %d Voxel %d latvec %d UDS out of bounds updated states with max_uds value %g\n", pde_advect_ublk->id(), voxel, latvec, max_uds); 
#endif
        } else {
	  states_uds[latvec][voxel] += min_uds * pas_factor;
#if defined(DEBUG)
	  msg_warn("Ublk %d Voxel %d latvec %d UDS out of bounds updated states with min_uds value %g\n", pde_advect_ublk->id(), voxel, latvec, min_uds); 
#endif
        }
      }
    } else
#endif
    {
      states_uds[latvec][voxel] += pas_factor * in_states_uds;
    }
  } else {
    states_uds[latvec][voxel] += pas_factor * in_states_uds;
  }
#if !GPU_COMPILER  
  if (!IS_VR_FINE && !sim.is_pf_model && !pde_advect_ublk->is_vr_coarse()) {
    if (states_uds[latvec][voxel] < min_uds)
      states_uds[latvec][voxel] = min_uds;
    else if (states_uds[latvec][voxel] > max_uds)
      states_uds[latvec][voxel] = max_uds;
  }
#endif
}


template <BOOLEAN IS_VR_FINE, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
#if DEBUG
static
#else
static INLINE
#endif
VOID append_v2v_split_contribution(UBLK pde_advect_ublk,
                                   TAGGED_UBLK neighbor_tagged_ublk,
                                   const sSPLIT_ADVECT_FACTORS* split_advect_factors,
                                   const asINT32 latvec,
                                   const asINT32 *dst_voxels,
                                   const asINT32 *src_voxels,
                                   asINT32 n_src_voxels,
                                   SOLVER_INDEX_MASK prior_solver_index_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask,
                                   BOOLEAN is_timestep_even,
                                   VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                                   VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],
                                   VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],
				   VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                   VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],
                                   STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],
                                   STP_GEOM_VARIABLE pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],
                                   BOOLEAN use_vrf_pas) {

  const sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
  VOXEL_STATE state_t_increment[64];
  VOXEL_STATE state_uds_increment[MAX_N_USER_DEFINED_SCALARS][64];

  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);

  sUBLK_VECTOR *split_ublks = neighbor_tagged_ublk.split_ublks();
  asINT32 parity = state_parity(latvec);

  dassert(!split_advect_factors->get<SPEED1>().is_empty());

  //Accumulate fractional states from instances
  ccDOTIMES(split_instance_index, split_ublks->num_split_ublks()) {

    TAGGED_UBLK tagged_neighbor = split_ublks->m_tagged_ublks[split_instance_index];
    UBLK neighbor_ublk = tagged_neighbor.ublk();
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    // explode is one on even timesteps
    explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(neighbor_ublk, is_timestep_even,
							 prior_solver_index_mask, active_solver_mask);
#endif

    for( asINT32 i = 0; i < n_src_voxels; i++) {

      asINT32 src_voxel = src_voxels[i];
      asINT32 dst_voxel = dst_voxels[i];

      sdFLOAT pas_factor = pas_factors[latvec][dst_voxel];

      //Initialize states
      if (split_instance_index == 0) {
        if (!HAS_S2V) {
          states[latvec][dst_voxel] = fractional_factor * p_states[latvec][dst_voxel];
          if (ADVECT_TEMP) {
            states_t[latvec][dst_voxel] = 0;
          }
	  if (ADVECT_UDS) {
	    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      states_uds[nth_uds][latvec][dst_voxel] = 0;
          }
#if BUILD_5G_LATTICE
          if (g_is_multi_component) {
            states_mc[latvec][dst_voxel] = 0;
          }
#endif
        } else {
          if (ADVECT_TEMP && HAS_S2V) {
            state_t_increment[i] = 0.0;
          }
	  if (ADVECT_UDS && HAS_S2V) {
	    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      state_uds_increment[nth_uds][i] = 0.0;
          }
        }
      }

#if DEBUG_SPLIT_ADVECT_FACTORS
      sdFLOAT split_fac = split_advect_factors->get<SPEED1>().next_factor(split_instance_index, latvec,
                                                                          src_voxel, dst_voxel);
#else
      sdFLOAT split_fac = split_advect_factors->get<SPEED1>().next_factor();
#endif


      //No need to pull neighbor states that are going to add zero contributions
      if (split_fac != 0.0F) {

        cassert((neighbor_ublk->fluid_like_voxel_mask.test(src_voxel)));
        if (use_vrf_pas) {
          pas_factor = vrf_pas_factors[latvec][dst_voxel];
        }

        asINT32 instance_lb_index = neighbor_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 instance_t_index  = neighbor_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 instance_uds_index  = neighbor_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;

        if (!HAS_S2V) {
          states[latvec][dst_voxel] += split_fac*neighbor_ublk->lb_states(instance_lb_index)->m_states[latvec][src_voxel];
#if BUILD_5G_LATTICE
          if (g_is_multi_component) {
            states_mc[latvec][dst_voxel] += split_fac*neighbor_ublk->mc_data()->mc_states_data(instance_lb_index)->m_states_mc[latvec][src_voxel];
          }
#endif
        } else {
          states[latvec][dst_voxel] += pas_factor*split_fac*neighbor_ublk->lb_states(instance_lb_index)->m_states[latvec][src_voxel];
#if BUILD_5G_LATTICE
          if (g_is_multi_component) {
            states_mc[latvec][dst_voxel] += pas_factor*split_fac*neighbor_ublk->mc_data()->mc_states_data(instance_lb_index)->m_states_mc[latvec][src_voxel];
          }
#endif
        }

        //PAS factors are accounted for in the call to update_states_t
        if (ADVECT_TEMP && pas_factor != 0) {
          if (HAS_S2V) {
            state_t_increment[i] += split_fac*neighbor_ublk->t_data()->lb_t_data(instance_t_index)->m_states_t[latvec][src_voxel];
          } else {
            states_t[latvec][dst_voxel] += split_fac*neighbor_ublk->t_data()->lb_t_data(instance_t_index)->m_states_t[latvec][src_voxel];
          }
        }
	if (ADVECT_UDS && pas_factor != 0) {
          if (HAS_S2V) {
	    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      state_uds_increment[nth_uds][i] += split_fac*neighbor_ublk->uds_data(nth_uds)->lb_uds_data(instance_uds_index)->m_states_uds[latvec][src_voxel];
          } else {
	    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      states_uds[nth_uds][latvec][dst_voxel] += split_fac*neighbor_ublk->uds_data(nth_uds)->lb_uds_data(instance_uds_index)->m_states_uds[latvec][src_voxel];
          }
        }
      } //if split_fac != 0;

      //Correct temperature if we're at the end of aggregating from all instances
      if (ADVECT_TEMP && HAS_S2V && 
          (split_instance_index == split_ublks->num_split_ublks() - 1)) {
        update_states_t<IS_VR_FINE>(pde_advect_ublk, pas_factor, latvec, dst_voxel, state_t_increment[i], states_t);
      }
      if (ADVECT_UDS && HAS_S2V && 
          (split_instance_index == split_ublks->num_split_ublks() - 1)) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  update_states_uds<IS_VR_FINE>(pde_advect_ublk, pas_factor, latvec, dst_voxel, nth_uds, state_uds_increment[nth_uds][i], states_uds[nth_uds]);
      }

    }

  }
}

#if BUILD_AVX2
/*=================================================================================================
 * @fcn append_voxel_states_v
 *
 * THis is the vectorized append variant. This function works very similar to swap_voxel_states_v
 * Please see swap_advect.h for a description on how the permute and blend operations are done.
 *
 *================================================================================================*/
template<asINT32 latvec, auINT8 src_voxel_mask, auINT8 dst_voxel_mask,BOOLEAN IS_VR_FINE, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
static INLINE VOID append_voxel_states_v(UBLK src_ublk,
                                         UBLK dst_ublk,
					 const __m256i& src_perm_ind,
					 const __m256i& dst_perm_ind,
					 asINT32 voxor,
					 asINT32 prev_lb_index,
					 asINT32 prev_t_index,
					 asINT32 prev_uds_index,
					 BOOLEAN src_ublk_has_two_copies,
					 BOOLEAN use_vrf_pas,
					 STP_GEOM_VARIABLE (vrf_pas_factors)[N_MOVING_STATES][ubFLOAT::N_VOXELS],
					 STP_GEOM_VARIABLE (pas_factors)[N_MOVING_STATES][ubFLOAT::N_VOXELS]) {

  
  asINT32 src_prev_lb_index = src_ublk_has_two_copies? prev_lb_index : ONLY_ONE_COPY;
  asINT32 src_prev_t_index  = src_ublk_has_two_copies? prev_t_index : ONLY_ONE_COPY;
  asINT32 src_prev_uds_index  = src_ublk_has_two_copies? prev_uds_index : ONLY_ONE_COPY;

  auto src_ublk_ubfloat = src_ublk->to_ubfloat_ublk();
  auto dst_ublk_ubfloat = dst_ublk->to_ubfloat_ublk();

  constexpr asINT32 parity = state_parity(latvec);

  sdFLOAT* pas_factors_latvec = use_vrf_pas ? vrf_pas_factors[latvec] : pas_factors[latvec];
  vxFLOAT  pas_factors_v = vxFLOAT(pas_factors_latvec);
  
  //STATES
  {
    vxFLOAT_BASE& src_states = src_ublk_ubfloat->lb_states(src_prev_lb_index)->m_states[latvec][voxor];
    vxFLOAT_BASE& dst_states = dst_ublk_ubfloat->lb_states(prev_lb_index^1)->m_states[latvec][voxor];
    vxFLOAT src_contrib = permutevar(src_states, src_perm_ind);
    if constexpr (!HAS_S2V) {
#if BUILD_5X_SOLVER    	
      sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
      if (!IS_VR_FINE) {
        vxFLOAT_BASE& p_dst_states = dst_ublk_ubfloat->lb_states(prev_lb_index)->m_states[latvec][voxor];
        vxFLOAT src_and_p_states  = fractional_factor * p_dst_states + src_contrib;
        dst_states = blend_v<dst_voxel_mask>(dst_states, src_and_p_states);
      } else {
        dst_states = blend_v<dst_voxel_mask>(dst_states, src_contrib);
      }
#else	
      dst_states = blend_v<dst_voxel_mask>(dst_states, src_contrib);
#endif   
    } else {
      dst_states = blend_v<dst_voxel_mask>(dst_states, dst_states + pas_factors_v * src_contrib);
    }
  }

  if constexpr(ADVECT_TEMP) {
    vxFLOAT_BASE& src_states_t = src_ublk_ubfloat->t_data()->lb_t_data(src_prev_t_index)->m_states_t[latvec][voxor];
    vxFLOAT_BASE& dst_states_t = dst_ublk_ubfloat->t_data()->lb_t_data(prev_t_index^1)->m_states_t[latvec][voxor];
    vxFLOAT src_contrib = permutevar(src_states_t, src_perm_ind);
    if constexpr (HAS_S2V) {
      dst_states_t = blend_v<dst_voxel_mask>(dst_states_t, dst_states_t + pas_factors_v * src_contrib);
    } else {
      dst_states_t = blend_v<dst_voxel_mask>(dst_states_t, src_contrib);
    }
  }

  if constexpr(ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
      vxFLOAT_BASE& src_states_uds = src_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(src_prev_uds_index)->m_states_uds[latvec][voxor];
      vxFLOAT_BASE& dst_states_uds = dst_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(prev_uds_index^1)->m_states_uds[latvec][voxor];
      vxFLOAT src_contrib = permutevar(src_states_uds, src_perm_ind);
      if constexpr (HAS_S2V) {
	  dst_states_uds = blend_v<dst_voxel_mask>(dst_states_uds, dst_states_uds + pas_factors_v * src_contrib);
	} else {
	dst_states_uds = blend_v<dst_voxel_mask>(dst_states_uds, src_contrib);
      }
    }
  }
  
#if BUILD_5G_LATTICE
  if (g_is_multi_component){
    vxFLOAT_BASE& src_states_mc = src_ublk_ubfloat->mc_data()->mc_states_data(src_prev_lb_index)->m_states_mc[latvec][voxor];
    vxFLOAT_BASE& dst_states_mc = dst_ublk_ubfloat->mc_data()->mc_states_data(prev_lb_index^1)->m_states_mc[latvec][voxor];
    vxFLOAT src_contrib = permutevar(src_states_mc, src_perm_ind);
    if constexpr (HAS_S2V) {
      dst_states_mc = blend_v<dst_voxel_mask>(dst_states_mc, dst_states_mc + pas_factors_v * src_contrib);
    } else {
      dst_states_mc = blend_v<dst_voxel_mask>(dst_states_mc, src_contrib);
    }    
  }
#endif
  
  //If src ublk has two copies, we overwrite parity values
    if (src_ublk->does_advect_through_swap()) {
     //LB STATES
      {
        vxFLOAT_BASE& p_dst_states = dst_ublk_ubfloat->lb_states(prev_lb_index)->m_states[parity][voxor];
        vxFLOAT_BASE& src_states = src_ublk_ubfloat->lb_states(ONLY_ONE_COPY)->m_states[latvec][voxor];
        src_states = blend_v<src_voxel_mask>(src_states, permutevar(p_dst_states, dst_perm_ind));
      }
     
     //TEMP
     if constexpr (ADVECT_TEMP) {

       vxFLOAT_BASE& p_dst_states_t =
           dst_ublk_ubfloat->t_data()->lb_t_data(prev_t_index)->m_states_t[parity][voxor];

       vxFLOAT_BASE& src_states_t =
           src_ublk_ubfloat->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t[latvec][voxor];

       src_states_t = blend_v<src_voxel_mask>(src_states_t, permutevar(p_dst_states_t, dst_perm_ind));
     }

     //UDS
     if constexpr (ADVECT_UDS) {

       ccDOTIMES(nth_uds, sim.n_user_defined_scalars){	 
	 vxFLOAT_BASE& p_dst_states_uds =
           dst_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds[parity][voxor];
	 
	 vxFLOAT_BASE& src_states_uds =
           src_ublk_ubfloat->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds[latvec][voxor];
	 
	 src_states_uds = blend_v<src_voxel_mask>(src_states_uds, permutevar(p_dst_states_uds, dst_perm_ind));
       }
     }

     //MC STATES
#if BUILD_5G_LATTICE
     if (g_is_multi_component){

       vxFLOAT_BASE& p_dst_states_mc =
	 dst_ublk_ubfloat->mc_data()->mc_states_data(prev_lb_index)->m_states_mc[parity][voxor];
       
       vxFLOAT_BASE& src_states_mc =
	 src_ublk_ubfloat->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc[latvec][voxor];

       src_states_mc = blend_v<src_voxel_mask>(src_states_mc, permutevar(p_dst_states_mc, dst_perm_ind));
     }
#endif
   } //src_ublk->does_advect_through_swap()
}

#endif 

#endif
