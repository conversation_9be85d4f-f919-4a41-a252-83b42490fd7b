/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Support for interprocessor ublk and surfel communication
 *
 * Samuel Watson, Exa Corporation 
 * Created Jan 12 2015
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_COMM_GROUPS_H
#define _SIMENG_COMM_GROUPS_H

#include "common_sp.h"
#include "shob.h"
#include "ublk.h"
#include "surfel.h"
#include "group.h"
#include "fset.h"
#include "thread_run.h"
#include "comm_compression.h"
#include "neighbor_sp.h"
#include "film_solver_stencils.h"
#include "atomic_ref.h"

enum RECV_TYPE {
  NO_RECV_TYPE = -1,
  NEARBLK_RECV_TYPE,
  FARBLK_RECV_TYPE,
  SURFEL_RECV_TYPE,
  NEARBLK_MME_RECV_TYPE,
  FARBLK_MME_RECV_TYPE,
  NEARBLK_IB_BF_RECV_TYPE,
  FARBLK_IB_BF_RECV_TYPE,
  NEARBLK_IB_BF_BCAST_RECV_TYPE,
  FARBLK_IB_BF_BCAST_RECV_TYPE,
  BSURFEL_RECV_TYPE,
  PARCEL_RECV_TYPE,
  FILM_RECV_TYPE,
  WSURFEL_RECV_TYPE,
  CONTACT_RECV_TYPE,
#if BUILD_5G_LATTICE
  NEARBLK_PORE_RECV_TYPE,
  FARBLK_PORE_RECV_TYPE,
#endif
  N_RECV_TYPES
};

enum COLLECTIVE_TYPE {
  NO_COLLECTIVE_TYPE = -1,
  PHASE_FIELD_PRESSURE_REDUCTION_TYPE = N_RECV_TYPES,
  N_COMM_TYPES
};

#define N_COLL_TYPES (N_COMM_TYPES - N_RECV_TYPES)

enum SEND_TYPE {
  NO_SEND_TYPE = -1,
  NEARBLK_SEND_TYPE,
  FARBLK_SEND_TYPE,
  SURFEL_SEND_TYPE,
  NEARBLK_MME_SEND_TYPE,
  FARBLK_MME_SEND_TYPE,
  NEARBLK_IB_BF_SEND_TYPE,
  FARBLK_IB_BF_SEND_TYPE,
  NEARBLK_IB_BF_BCAST_SEND_TYPE,
  FARBLK_IB_BF_BCAST_SEND_TYPE,
  BSURFEL_SEND_TYPE,
  PARCEL_SEND_TYPE,
  FILM_SEND_TYPE,
  WSURFEL_SEND_TYPE,
  CONTACT_SEND_TYPE,
#if BUILD_5G_LATTICE
  NEARBLK_PORE_SEND_TYPE,
  FARBLK_PORE_SEND_TYPE,
#endif
  N_SEND_TYPES
};

// This is modeled on the above types, but I don't see why separate send and receive
// types are needed, so for now there's just one type.

enum MLRF_COMM_TYPE {
  NO_MLRF_COMM_TYPE      =  -1,
  SID_MLRF_COMM_TYPE,
  MCD_MLRF_COMM_TYPE,
  N_MLRF_COMM_TYPES
};

inline uINT32 num_floats_from_num_bytes(uINT32 num_bytes) {
  asINT32 roundup = ((num_bytes % sizeof(sdFLOAT)) == 0) ? 0 : 1;
  return (num_bytes /  sizeof(sdFLOAT) +  roundup);
}
//-------------------------------------------------------------------
//
//      Send quantums, groups, and fsets
//
//-------------------------------------------------------------------

// Now that we're using a receive buffer, and eliminating MPI datatypes to do the unpacking, receive elements
// have no meaning. That doesn't preclude using send elements as a convenience in filling the send buffer.


//----------- Comm Quantums ---------------------------

typedef struct sUBLK_COMM_QUANTUM
{

  sUBLK *m_ublk;
  NEIGHBOR_MASK_INDEX m_nmi;
} *UBLK_COMM_QUANTUM;

template <class QUANTUM_TYPE>
struct tCOMM_QUANTUM
{
  QUANTUM_TYPE *m_surfel;

  tCOMM_QUANTUM(QUANTUM_TYPE *surfel) : m_surfel(surfel) {
  }

  QUANTUM_TYPE operator()() {
    return m_surfel;
  }

};

using sSURFEL_COMM_QUANTUM = tCOMM_QUANTUM<sSURFEL>;
using SURFEL_COMM_QUANTUM  = tCOMM_QUANTUM<sSURFEL> *;
using sSAMPLING_SURFEL_COMM_QUANTUM = tCOMM_QUANTUM<sSAMPLING_SURFEL>;
using SAMPLING_SURFEL_COMM_QUANTUM  = tCOMM_QUANTUM<sSAMPLING_SURFEL> *;

namespace
{

template<typename T> struct sFLOAT_TO_INT;

template <> struct sFLOAT_TO_INT<double> {
  typedef uINT64 type;
};

template <> struct sFLOAT_TO_INT<float> {
  typedef uINT32 type;
};

};
//----------- Send Groups ---------------------------

// A sSEND_GROUP organizes the shobs for which a particular sim thread is
// responsible according to scale and remote SP.
class sSEND_GROUP_BASE : public sGROUP
{
private:
  bool       m_send_data_copied;
public:

  sSEND_GROUP_BASE() {
    m_send_data_copied = true;
  }

  sSEND_GROUP_BASE(const sSEND_GROUP_BASE&) = default;

  void wait_for_copying_of_send_data() {
    while(false == tATOMIC_REF(m_send_data_copied).load(std::memory_order_acquire)) {
      sp_thread_sleep(MPI_SLEEP_SHORT);
    }
  }

  virtual void complete_send() = 0;

  virtual void send(ACTIVE_SOLVER_MASK active_solver_mask) = 0;

  virtual ~sSEND_GROUP_BASE() = default;

  // rather than using an atomic_bool, an tATOMIC_REF
  // Replace with std::atomic_ref when using C++20
  // I chose this because std::atomic_bool is not copyable, and I reason
  // it's less error prone to define these two functions rather than
  // make everyone aware of the need to update the copy constructor. sigh.
  void set_send_data_copied() { tATOMIC_REF(m_send_data_copied).store(true, std::memory_order_release); }

  // called by the compute thread. The comm thread doesn't care
  // about the actual value, or the data dependencies, as these
  // are taken care of by the queue
  void reset_send_data_copied() { tATOMIC_REF(m_send_data_copied).store(false, std::memory_order_relaxed); }
};


class sSP_SEND_GROUP_BASE : public sSEND_GROUP_BASE
{

public:
  SEND_TYPE     m_send_type;
  cNEIGHBOR_SP  m_dest_sp;
  asINT32       m_sendsize;          // Default messages size in sdFLOATs
  asINT32       m_tpde_sendsize;     // Message size for the PDE temperature solver in sdFLOATs
  T_SOLVER_TYPE t_solver_type;
  SOLVER_INDEX_MASK m_solver_timestep_index_mask;

  SEND_TYPE send_type() const { return m_send_type; }

  const cNEIGHBOR_SP& dest_sp() const { return m_dest_sp; }

  asINT32 sendsize() const { return m_sendsize; }

  void switch_T_solver() {
    if(t_solver_type == PDE_TEMPERATURE)
      t_solver_type = LB_TEMPERATURE;
    else
      t_solver_type = PDE_TEMPERATURE;
  }

  void set_t_solver_type(T_SOLVER_TYPE type) { t_solver_type = type; }

  sSP_SEND_GROUP_BASE() {
    m_tpde_sendsize  = 0;
    m_solver_timestep_index_mask = 0;
    m_sendsize = 0;
  }

  sSP_SEND_GROUP_BASE(const sSP_SEND_GROUP_BASE& group) :
    m_send_type(group.m_send_type),
    m_dest_sp(group.m_dest_sp),
    m_sendsize(group.m_sendsize),
    m_tpde_sendsize(group.m_tpde_sendsize),
    t_solver_type(group.t_solver_type),
    m_solver_timestep_index_mask(group.m_solver_timestep_index_mask)
  {
    m_scale = group.m_scale;
  }

  STP_PROC dest_nsp() const { return m_dest_sp.nsp(); }
  STP_PROC dest_rank() const { return m_dest_sp.rank(); }

  virtual ~sSP_SEND_GROUP_BASE() = default;
};

template<typename TMsg>
class tSP_SEND_GROUP : public sSP_SEND_GROUP_BASE
{

public:
  TMsg m_send_msg;

  tSP_SEND_GROUP() = default;

  template <typename T>
  tSP_SEND_GROUP(const tSP_SEND_GROUP<T>& group) : sSP_SEND_GROUP_BASE(group) {}

  STP_PROC dest_nsp() const { return m_dest_sp.nsp(); }
  STP_PROC dest_rank() const { return m_dest_sp.rank(); }

  virtual ~tSP_SEND_GROUP() = default;

  VOID complete_send() {
    complete_mpi_request_while_processing_cp_messages(m_send_msg, MPI_SLEEP_LONG);
    LOG_MSG("STRAND_SWITCHING").printf( "completing send: size %d to SP %d", m_sendsize, dest_rank());
  }

};

using sSP_SEND_GROUP = tSP_SEND_GROUP<cExaMsg<sdFLOAT>>;
using SP_SEND_GROUP = sSP_SEND_GROUP*;

//----------- collective ----------


typedef class sCOLLECTIVE_GROUP : public sGROUP
{

protected:
  bool          m_collective_called;     // Indicates that the group has been collective called.
                                // Cleared when the last group of a type
                                // has been called and the consumer counts are reset.

public:

  COLLECTIVE_TYPE m_collective_type;
  sCOLLECTIVE_GROUP *m_next_in_collective_list;

  sCOLLECTIVE_GROUP() {
    m_collective_type = NO_COLLECTIVE_TYPE;
    m_next_in_collective_list = NULL;
    m_collective_called = false;
  }

  virtual VOID collective_call() = 0;

  bool is_collective_called() { return m_collective_called; }
  void reset_collective_called() { m_collective_called = false; }
  virtual bool is_ready_to_be_called() { return !is_collective_called(); }

} *COLLECTIVE_GROUP;


typedef class sPHASE_FIELD_COLLECTIVE_GROUP : public sCOLLECTIVE_GROUP
{
public:

  sPHASE_FIELD_COLLECTIVE_GROUP() {
    m_collective_type = PHASE_FIELD_PRESSURE_REDUCTION_TYPE;
  }

  VOID collective_call();

} *PHASE_FIELD_COLLECTIVE_GROUP;


//----------- Ublk send groups ---------------------------

typedef class sUBLK_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>> {

public:
  mutable std::vector<sUBLK_COMM_QUANTUM> m_quantums;

  BOOLEAN       m_is_near_surface;
  BOOLEAN       m_is_mme_data;
#if BUILD_5G_LATTICE
  BOOLEAN       m_is_pore_data;
#endif

  std::vector<sUBLK_COMM_QUANTUM>& quantums() { return m_quantums;    }
  std::vector<sUBLK_COMM_QUANTUM>& quantums() const { return m_quantums;    }

  sUBLK_SEND_GROUP() {
    m_is_near_surface = FALSE;
    m_is_mme_data = FALSE;
#if BUILD_5G_LATTICE
    m_is_pore_data = FALSE;
#endif
  }

  VOID add_quantum(UBLK ublk, NEIGHBOR_MASK_INDEX nmi) {
    m_quantums.push_back({ublk, nmi});
    m_sendsize += g_comm_compression_info.m_sendsize[nmi];
  }

  virtual VOID allocate_send_buffer() {}
  virtual VOID send_init_info() {}
  virtual VOID send_conduction_init_info() {}
  // Have to define empty since we create an abstract instance for signature.
  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask) {}
  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) {}
#if BUILD_5G_LATTICE
  virtual VOID send_init_pore_data() {}
#endif

} *UBLK_SEND_GROUP;

typedef class sNEARBLK_SEND_GROUP :  public sUBLK_SEND_GROUP {
public:
  

  sNEARBLK_SEND_GROUP() {
    m_is_near_surface = TRUE;
    m_send_type = NEARBLK_SEND_TYPE;
  }

  VOID allocate_send_buffer() {

    // This should be called after all the quantums have been added, at which point m_sendsize
    // will already include the size of the states

    // solver data size per quantum; this is basically m_n_quantums
    // times the sum of the sizes of the active solver fields.
    // T solver comes in two sizes; pick the largest.
    
    asINT32 per_quantum_sendsize = 0;
    asINT32 per_tpde_quantum_sendsize = 0;
    sUBLK::add_send_size_for_nearblk(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_tpde_sendsize = per_tpde_quantum_sendsize * m_quantums.size();
    asINT32 max_runtime_sendsize =  MAX(m_sendsize, m_tpde_sendsize);

    asINT32 bytesize = sizeof(sNEARBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 ublk_init_info_sendsize = num_floats_from_num_bytes(bytesize) ;

    asINT32 allocated_sendsize = MAX(max_runtime_sendsize, ublk_init_info_sendsize); 
#if BUILD_5G_LATTICE
    if (sim.is_large_pore) {
      asINT32 per_quantum_pore_sendsize = 0;
      sUBLK::add_pore_send_size(sim.init_solver_mask, per_quantum_pore_sendsize);
      asINT32 pore_sendsize = per_quantum_pore_sendsize * m_quantums.size();

      //do_debug
      /*if (pore_sendsize > allocated_sendsize)
	printf("In near_send_group: poer_sendsize(%d) is larger thean regular send size(%d)\n", pore_sendsize, allocated_sendsize);*/
      
      allocated_sendsize = MAX(allocated_sendsize, pore_sendsize);
    }
#endif  
    allocated_sendsize++;


    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for nearblks scale %d size %d to SP %d", m_scale, allocated_sendsize, dest_rank());

    m_send_msg.allocateBuffer(allocated_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  // during initialization
  VOID send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask);

  VOID send_init_info();
  VOID send_conduction_init_info();

#if BUILD_5G_LATTICE
  //borrow to send pore data during initialization
  VOID send_init_pore_data();
#endif

} *NEARBLK_SEND_GROUP;

typedef class sFARBLK_SEND_GROUP :  public sUBLK_SEND_GROUP {
public:
  
  sFARBLK_SEND_GROUP() {
    m_is_near_surface = FALSE;
    m_send_type = FARBLK_SEND_TYPE;
  }

  //TODO(multi-gpu) : move to .cpp
  VOID allocate_send_buffer() {

    // This should be called after all the quantums have been added, at which point m_sendsize
    // will already include the size of the states

    // solver data size per quantum; this is basically m_n_quantums
    // times the sum of the sizes of the active solver fields.
    // T solver comes in two sizes; pick the largest.
    
    asINT32 per_quantum_sendsize = 0;
    asINT32 per_tpde_quantum_sendsize = 0;

    sUBLK::add_send_size_for_farblk(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_tpde_sendsize = per_tpde_quantum_sendsize * m_quantums.size();
    asINT32 max_runtime_sendsize =  MAX(m_sendsize, m_tpde_sendsize);

    asINT32 bytesize = sizeof(sUBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 ublk_init_info_sendsize = num_floats_from_num_bytes(bytesize) ;

    asINT32 allocated_sendsize = MAX(max_runtime_sendsize, ublk_init_info_sendsize); 
#if BUILD_5G_LATTICE
    if (sim.is_large_pore) {
      asINT32 per_quantum_pore_sendsize = 0;
      sUBLK::add_pore_send_size(sim.init_solver_mask, per_quantum_pore_sendsize);
      asINT32 pore_sendsize = per_quantum_pore_sendsize * m_quantums.size();

      //do_debug
      /*if (pore_sendsize > allocated_sendsize)
	printf("In far_send_group: poer_sendsize(%d) is larger thean regular send size(%d)\n", pore_sendsize, allocated_sendsize);*/
	

      allocated_sendsize = MAX(allocated_sendsize, pore_sendsize);
    }
#endif
    allocated_sendsize++;

    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for farblks scale %d size %d to SP %d", m_scale, allocated_sendsize, dest_rank());

    m_send_msg.allocateBuffer(allocated_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  // during initialization
  VOID send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask);

  VOID send_init_info();

#if BUILD_5G_LATTICE
  //borrow to send pore data during initialization
  VOID send_init_pore_data();
#endif

} *FARBLK_SEND_GROUP;

typedef class sUBLK_MME_SEND_GROUP :  public sUBLK_SEND_GROUP {
public:

  sUBLK_MME_SEND_GROUP(sUBLK_SEND_GROUP* template_group) {
    *static_cast<sUBLK_SEND_GROUP*>(this) = *template_group;
    m_is_mme_data = TRUE;
    m_solver_timestep_index_mask = sim_prior_timestep_index_mask(m_scale,g_timescale.m_timestep);
//    m_solver_timestep_index_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][m_scale];
    m_solver_timestep_index_mask ^= sim.init_solver_mask;
  }

  virtual VOID allocate_send_buffer() {

    m_tpde_sendsize = 0;
    asINT32 per_quantum_sendsize = 0;
    sUBLK::add_mme_send_size(sim.init_solver_mask, per_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_sendsize++;
    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for mme ublks scale %d size %d to SP %d", m_scale, m_sendsize, dest_rank());

    m_send_msg.allocateBuffer(m_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) { }
} *UBLK_MME_SEND_GROUP;

#if BUILD_5G_LATTICE
typedef class sUBLK_PORE_SEND_GROUP :  public sUBLK_SEND_GROUP {
public:

  sUBLK_PORE_SEND_GROUP(sUBLK_SEND_GROUP* template_group) {
    *static_cast<sUBLK_SEND_GROUP*>(this) = *template_group;
    m_is_pore_data = TRUE;
    m_solver_timestep_index_mask = 0;  //m_solver_timestep_index_mask has no use here
  }

  virtual VOID allocate_send_buffer() {

    m_tpde_sendsize = 0;
    asINT32 per_quantum_sendsize = 0;
    sUBLK::add_pore_send_size(sim.init_solver_mask, per_quantum_sendsize);
    //no compressed states here
    //m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_sendsize = per_quantum_sendsize * m_quantums.size();

    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for pore ublks scale %d size %d to SP %d", m_scale, m_sendsize, dest_rank());	  

    m_send_msg.allocateBuffer(m_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) { }
} *UBLK_PORE_SEND_GROUP;
#endif

//----------- Surfel send groups ---------------------------


typedef class sSURFEL_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>> {

  protected:
    mutable std::vector<sSURFEL_COMM_QUANTUM> m_quantums;

    cExaMsg<uINT32, 1> m_send_neigbor_ublks_data_size;

  public:
 
    std::vector<sSURFEL_COMM_QUANTUM>& quantums() { return m_quantums; }
    std::vector<sSURFEL_COMM_QUANTUM>& quantums() const { return m_quantums; }

    sSURFEL_SEND_GROUP() {
      m_send_type = SURFEL_SEND_TYPE;
    }

    VOID allocate_send_buffer() {

      asINT32 per_quantum_sendsize = 0;
      asINT32 per_tpde_quantum_sendsize = 0;
      asINT32 send_count = m_quantums.size();
      for (const auto& quantum: m_quantums) {
        quantum.m_surfel->add_send_size(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
      }
      m_sendsize = per_quantum_sendsize ;
      m_tpde_sendsize = per_tpde_quantum_sendsize ;
      asINT32 max_runtime_sendsize =  MAX(m_sendsize, m_tpde_sendsize);
      
      asINT32 surfel_init_info_bytesize = sizeof(sSURFEL_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
      asINT32 surfel_init_info_sendsize = num_floats_from_num_bytes(surfel_init_info_bytesize) ;

      asINT32 allocated_sendsize = MAX(max_runtime_sendsize, surfel_init_info_sendsize); 

      LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for surfels scale %d size %d to SP %d", m_scale, allocated_sendsize, dest_rank());
      m_send_msg.allocateBuffer(allocated_sendsize);
    }

    virtual VOID add_quantum(SURFEL surfel)
    {
      sSURFEL_COMM_QUANTUM q(surfel);
      m_quantums.push_back(q);
    }

    VOID trim() { m_quantums.shrink_to_fit(); }

    VOID send_init_info(); /* Comm the pgram volumes, used for pbl */
    VOID send_surfel_type(); /* Comm surfel type, used for s2s and v2s weight accumulation */
    VOID send_bc_type(); /* Comm the pgram volumes, used for pbl */

    VOID pack_and_send_neighbor_ublks();
#if BUILD_5G_LATTICE
    VOID send_potential();
#endif

    // The asINT32 timestep_index is ignored for surfels
    virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask);


} *SURFEL_SEND_GROUP;

//----------- Sampling Surfel send groups ---------------------------


typedef class sSAMPLING_SURFEL_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>> {

  protected:
    std::vector<sSAMPLING_SURFEL_COMM_QUANTUM> m_quantums;

    cExaMsg<uINT32, 1> m_send_neigbor_ublks_data_size;
  public:
    std::vector<sSAMPLING_SURFEL_COMM_QUANTUM>& quantums() { return m_quantums; }

    VOID add_quantum(sSAMPLING_SURFEL *surfel) {
      sSAMPLING_SURFEL_COMM_QUANTUM q(surfel);
      m_quantums.push_back(q);
    }

    virtual VOID complete_send() {}
    virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask) {}
    VOID pack_and_send_neighbor_ublks();
    VOID allocate_send_buffer();

} *SAMPLING_SURFEL_SEND_GROUP;

//------------------- Send fsets -----------------------------------------


//------ Ublk send fsets -------------------------------------------------

struct sUBLK_SEND_GROUP_ORDER 
{
  // order groups by the dest SP, then far/near for same dest SP
  BOOLEAN operator()(UBLK_SEND_GROUP a, UBLK_SEND_GROUP b) const
  {
    cNEIGHBOR_SP dest_sp1 = a->m_dest_sp;
    cNEIGHBOR_SP dest_sp2 = b->m_dest_sp;

    if (dest_sp1 != dest_sp2) {
      return (dest_sp1 < dest_sp2);
    }

    return (a->m_is_near_surface < b->m_is_near_surface);
  }
};

class sUBLK_SEND_FSET : public tSP_FSET < UBLK_SEND_GROUP, sUBLK_SEND_GROUP_ORDER > 
{
  typedef tSP_FSET < UBLK_SEND_GROUP, sUBLK_SEND_GROUP_ORDER >  BASE_CLASS;
  public:
    UBLK_SEND_GROUP find_group (asINT32 scale, cNEIGHBOR_SP dest_sp, BOOLEAN is_near_surface) {
      // Reduce to two-value form for use in signature comparisons
      BOOLEAN is_near_surface_canonic = is_near_surface ? TRUE : FALSE;
      static sUBLK_SEND_GROUP signature;
      signature.m_scale = scale;
      signature.m_dest_sp = dest_sp;
      signature.m_is_near_surface = is_near_surface_canonic;
      UBLK_SEND_GROUP group = BASE_CLASS::find_group(&signature);
      return group;
    }

    UBLK_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp, BOOLEAN is_near_surface) {
      // Reduce to two-value form for use in signature comparisons
      BOOLEAN is_near_surface_canonic = is_near_surface ? TRUE : FALSE;
      static sUBLK_SEND_GROUP signature;
      signature.m_scale = scale;
      signature.m_dest_sp = dest_sp;
      signature.m_is_near_surface = is_near_surface_canonic;
      UBLK_SEND_GROUP group = BASE_CLASS::find_group(&signature);
      if (NULL == group) {
        //msg_print("creating new ublk send group scale %d dest_sp %d dest_sp_rank %d", scale, dest_sp, dest_sp_rank);
        if (is_near_surface)
          group  = xnew sNEARBLK_SEND_GROUP;
        else
          group = xnew sFARBLK_SEND_GROUP;
	
        group->m_scale = scale;
        group->m_solver_timestep_index_mask = 0;
        group->m_dest_sp = dest_sp;
        group->m_send_msg.init(my_proc_id, group->dest_rank());
        group->m_is_near_surface = is_near_surface_canonic;
        add_group(group);
      }
      return group;
    }
};

extern sUBLK_SEND_FSET g_ublk_send_fset;

#define DO_UBLK_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sUBLK_SEND_FSET, g_ublk_send_fset, group_var)

#define DO_UBLK_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_SEND_FSET, g_ublk_send_fset, group_var, scale)


//------ Surfel send fsets -------------------------------------------------

struct sSURFEL_SEND_GROUP_ORDER {
  BOOLEAN operator() (SP_SEND_GROUP a, SP_SEND_GROUP b) const
  {
    return a->m_dest_sp < b->m_dest_sp;
  }

};

typedef struct sSURFEL_SEND_FSET : 
  public tSP_FSET < SURFEL_SEND_GROUP, sSURFEL_SEND_GROUP_ORDER > {

  public:
  SURFEL_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp);
} *SURFEL_SEND_FSET;

extern sSURFEL_SEND_FSET g_surfel_send_fset;

#define DO_SURFEL_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sSURFEL_SEND_FSET, g_surfel_send_fset, group_var)

#define DO_SURFEL_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_SEND_FSET, g_surfel_send_fset, group_var, scale)

//------ Sampling Surfel send fsets -------------------------------------------------
typedef struct sSAMPLING_SURFEL_SEND_FSET :
  public tSP_FSET < SAMPLING_SURFEL_SEND_GROUP, sSURFEL_SEND_GROUP_ORDER > {

  public:
  SAMPLING_SURFEL_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp) {

    static sSAMPLING_SURFEL_SEND_GROUP signature;
    signature.m_scale = scale;
    signature.m_dest_sp = dest_sp;
    sSAMPLING_SURFEL_SEND_GROUP *group = find_group(&signature);
    if (NULL == group) {
      group = xnew sSAMPLING_SURFEL_SEND_GROUP;
      group->m_scale = scale;
      group->m_dest_sp = dest_sp;
      group->m_send_msg.init(my_proc_id, group->dest_rank());
      add_group(group);
    }
    return group;
  }
} *SAMPLING_SURFEL_SEND_FSET;

extern sSAMPLING_SURFEL_SEND_FSET g_sampling_surfel_send_fset;

#define DO_SAMPLING_SURFEL_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSAMPLING_SURFEL_SEND_FSET, g_sampling_surfel_send_fset, group_var, scale)

//-------------------------------------------------------------------
//
//      Receive groups and fsets
//
//-------------------------------------------------------------------


//--------- Recv groups ---------------------------------------------
class sRECV_GROUP_BASE : public sGROUP
{

private:
  bool          m_unpacked;     // Indicates that the group has been unpacked.
                                // Cleared when the last group of a type and scale
                                // has been unpacked and the consumer counts are reset.
public:
  sRECV_GROUP_BASE() {
    m_unpacked = false;
  }

  sRECV_GROUP_BASE(const sRECV_GROUP_BASE& group) : sGROUP(group), m_unpacked(group.m_unpacked) {}

  // called by comm thread
  virtual VOID post_recv() = 0;

  virtual VOID complete_recv() = 0; 
  
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) = 0;

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) = 0;

  virtual bool is_recv_request_null() = 0;

  virtual void cancel_pending_recv() = 0; 

  // called by recv channel
  virtual bool is_recv_ready() = 0; 

  bool is_unpacked() const { return m_unpacked; }

  void reset_unpacked() { m_unpacked = false; }

  void set_unpacked() { m_unpacked = true; }

  virtual bool is_ready_to_be_unpacked() { return !is_unpacked(); }

};

class sSP_RECV_GROUP_BASE : public sRECV_GROUP_BASE
{

public:

  sSP_RECV_GROUP_BASE* m_next_in_unpacking_list;

  RECV_TYPE     m_recv_type;
  cNEIGHBOR_SP  m_source_sp;
  asINT32       m_recvsize;       // Default message size in sdFLOATs;
  asINT32       m_tpde_recvsize;  // Message size in sdFLOATs with the PDE temp solver
  asINT32       m_init_recvsize;  // The size of the init message, in bytes.
  T_SOLVER_TYPE t_solver_type;
  SOLVER_INDEX_MASK m_solver_timestep_index_mask;

  sSP_RECV_GROUP_BASE() : sRECV_GROUP_BASE() {
    m_solver_timestep_index_mask = 0;
    m_recvsize = 0;
    m_tpde_recvsize = 0;
    m_init_recvsize = 0;
    m_recv_type = NO_RECV_TYPE;
    m_next_in_unpacking_list = NULL;
  }

  sSP_RECV_GROUP_BASE(const sSP_RECV_GROUP_BASE& group) : sRECV_GROUP_BASE(group), 
    m_recv_type(group.m_recv_type),
    m_source_sp(group.m_source_sp),
    m_recvsize(group.m_recvsize),
    m_tpde_recvsize(group.m_tpde_recvsize),
    m_init_recvsize(group.m_init_recvsize),
    t_solver_type(group.t_solver_type),
    m_solver_timestep_index_mask(group.m_solver_timestep_index_mask)
  {
    m_scale = group.m_scale;
  }

  RECV_TYPE get_recv_type() const { return m_recv_type; }

  STP_PROC source_nsp() const { return m_source_sp.nsp(); }

  STP_PROC source_rank() const { return m_source_sp.rank(); }

  STP_PROC source_sp() const { return m_source_sp.nsp(); }

};

using SP_RECV_GROUP_BASE = sSP_RECV_GROUP_BASE*;

template <typename TMsg >
class tSP_RECV_GROUP : public sSP_RECV_GROUP_BASE
{

public:

  TMsg m_recv_msg;

  tSP_RECV_GROUP() = default;

  template <typename T>
  tSP_RECV_GROUP(const tSP_RECV_GROUP<T>& group) : sSP_RECV_GROUP_BASE(group) {}

  // called by comm thread
  virtual VOID post_recv() override = 0;

  VOID complete_recv() override {
    complete_mpi_request_while_processing_cp_messages(m_recv_msg, MPI_SLEEP_LONG);
    LOG_MSG("STRAND_SWITCHING").printf( "completing recv: size %d from SP %d", m_recvsize, source_rank());
  }
    
#ifdef _EXA_MPI
  bool is_recv_request_null() override { 
    return m_recv_msg.m_request == MPI_REQUEST_NULL;
  }
#else
  bool is_recv_request_null() override;
#endif
    
  virtual void cancel_pending_recv() override {
    if (!is_recv_request_null()) {
      g_exa_sp_cp_comm.cancel(m_recv_msg);
    }
  }

  virtual bool is_recv_ready() override { 
    int flag;
    g_exa_sp_cp_comm.test(m_recv_msg);
    return m_recv_msg.m_flag;
  }

};

typedef tSP_RECV_GROUP<cExaMsg<sdFLOAT>> sSP_RECV_GROUP, *SP_RECV_GROUP;

typedef class sUBLK_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>> {

public:
  // This is a pointer to a recv group since this is shared between MME and non MME variants.
  std::vector<sUBLK_COMM_QUANTUM> m_quantums;

  bool       m_is_near_surface;
  bool       m_is_mme_data;
#if BUILD_5G_LATTICE
  bool       m_is_pore_data;
  asINT32       m_init_pore_recvsize; //The size of the pore_data message, in sdFLOATS
#endif

  sUBLK_RECV_GROUP() {
#if BUILD_5G_LATTICE
    m_init_pore_recvsize = 0;
#endif
  }

  std::vector<sUBLK_COMM_QUANTUM>& quantums() { return m_quantums;    }

  VOID post_init_info_recv() {
    m_recv_msg.set_nelems(m_init_recvsize);
    m_recv_msg.settag(eMPI_GHOSTBLK_INIT_TAG);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  virtual VOID post_conduction_init_info_recv() {}
  virtual VOID unpack_conduction_init_info() {}
#if BUILD_5G_LATTICE
  VOID post_init_pore_data_recv() {
    int tag = 0;
    if (m_is_near_surface) {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_PORE>(m_scale);
    }
    else {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_PORE>(m_scale);
    }

    asINT32 recvsize = m_init_pore_recvsize;

    m_recv_msg.set_nelems(recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }
#endif

  virtual VOID post_recv() override {
    BOOLEAN is_T_pde_on = (sim.T_solver_type == PDE_TEMPERATURE);
    int tag = 0;
    if (m_is_mme_data) {
      if (m_is_near_surface) {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_MME>(m_scale);
      }
      else {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_MME>(m_scale);
      }
    }
#if BUILD_5G_LATTICE
    else if (m_is_pore_data) {
      if (m_is_near_surface) {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_PORE>(m_scale);
      }
      else {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_PORE>(m_scale);
      }
    }
#endif
    else {
      if (m_is_near_surface) {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK>(m_scale);
      }
      else {
        tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK>(m_scale);
      }
    }

    asINT32 recvsize = m_recvsize;

    LOG_MSG("STRAND_SWITCHING").printf( "posting ublk recv type %d: mme %d source %d scale %d  tag %x, size %d", m_recv_type, m_is_mme_data, source_rank(), m_scale, tag,  recvsize);
    m_recv_msg.set_nelems(recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  VOID add_quantum(UBLK ublk, NEIGHBOR_MASK_INDEX nmi) {
    sUBLK_COMM_QUANTUM q;
    q.m_ublk = ublk;
    q.m_nmi = nmi;
    m_quantums.push_back(q);
    m_recvsize += g_comm_compression_info.m_sendsize[nmi];
  }
    
} *UBLK_RECV_GROUP;

typedef class sFARBLK_RECV_GROUP : public sUBLK_RECV_GROUP {


 public:

  // Note the the size of the
  // receive buffer should be identical to the size of the send buffer on the remote SP.

  VOID allocate_recv_buffer() {

    // This should be called after all the quantums have been added, at which point m_recvsize
    // will already include the size of the states
 
    m_tpde_recvsize = m_recvsize;
    asINT32 per_ghostblk_recvsize = 0;
    asINT32 per_ghostblk_tpde_recvsize = 0;
    sUBLK::add_send_size_for_farblk(sim.init_solver_mask, per_ghostblk_recvsize, per_ghostblk_tpde_recvsize);
    m_recvsize += per_ghostblk_recvsize * m_quantums.size();
    m_tpde_recvsize  += per_ghostblk_tpde_recvsize * m_quantums.size();
    asINT32 max_runtime_recvsize = MAX(m_recvsize, m_tpde_recvsize);

    // The init message is not composed of floats. We send it as raw bytes and thus store
    // the size as a byte size.

    asINT32 init_bytesize = sizeof(sUBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 init_floatsize = num_floats_from_num_bytes(init_bytesize) ;
    m_init_recvsize = init_floatsize;

    asINT32 allocated_recvsize = MAX(max_runtime_recvsize, init_floatsize);
#if BUILD_5G_LATTICE
    if (sim.is_large_pore) {
      asINT32 per_ghostblk_pore_recvsize = 0;
      sUBLK::add_pore_send_size(sim.init_solver_mask, per_ghostblk_pore_recvsize);
      asINT32 pore_recvsize = per_ghostblk_pore_recvsize * m_quantums.size();
      m_init_pore_recvsize = pore_recvsize;
      allocated_recvsize = MAX(allocated_recvsize, pore_recvsize);
    }
#endif
    allocated_recvsize++;
    m_recvsize++;

    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for farblks scale %d size %d to SP %d", m_scale, allocated_recvsize, source_rank());
    m_recv_msg.allocateBuffer(allocated_recvsize);

  }

  void unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
  VOID unpack_init_info();

#if BUILD_5G_LATTICE
  //borrow to comm pore data during initialization
  VOID unpack_init_pore_data();
#endif

} *FARBLK_RECV_GROUP;

typedef class sUBLK_MME_RECV_GROUP : public sUBLK_RECV_GROUP {

 public:

   sUBLK_MME_RECV_GROUP(sUBLK_RECV_GROUP* template_group)
   {
     *static_cast<sUBLK_RECV_GROUP*>(this) = *template_group;
     
     m_recv_type = m_is_near_surface ? NEARBLK_MME_RECV_TYPE : FARBLK_MME_RECV_TYPE; 
     m_is_mme_data = TRUE;
#if BUILD_5G_LATTICE
     m_is_pore_data = FALSE;
#endif
     m_solver_timestep_index_mask = sim_prior_timestep_index_mask(m_scale,g_timescale.m_timestep);
//     m_timestep_index = sim_prior_timestep_index(m_scale,g_timescale.m_time);
     m_solver_timestep_index_mask ^= sim.init_solver_mask;
   }

  // Note the the size of the
  // receive buffer should be identical to the size of the send buffer on the remote SP.
  virtual VOID allocate_recv_buffer() {

    m_tpde_recvsize = 0;
    asINT32 per_ghostblk_recvsize = 0;
    sUBLK::add_mme_send_size(sim.init_solver_mask, per_ghostblk_recvsize);
    m_recvsize += per_ghostblk_recvsize * m_quantums.size();
    m_recvsize++;
    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for mme ublks scale %d size %d to SP %d", m_scale, m_recvsize, source_rank());

    m_recv_msg.allocateBuffer(m_recvsize);
  }

  VOID process_quantum(const sUBLK_COMM_QUANTUM& quantum, sdFLOAT * &recv_buffer, ACTIVE_SOLVER_MASK active_solver_mask);

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) {}

} *UBLK_MME_RECV_GROUP;

#if BUILD_5G_LATTICE
typedef class sUBLK_PORE_RECV_GROUP : public sUBLK_RECV_GROUP {

 public:

   sUBLK_PORE_RECV_GROUP(sUBLK_RECV_GROUP* template_group)
   {
     *static_cast<sUBLK_RECV_GROUP*>(this) = *template_group;
     
     m_recv_type = m_is_near_surface ? NEARBLK_PORE_RECV_TYPE : FARBLK_PORE_RECV_TYPE; 
     m_is_pore_data = TRUE;
     m_is_mme_data = FALSE;
     m_solver_timestep_index_mask = 0;  //no real use
   }

  // Note the the size of the
  // receive buffer should be identical to the size of the send buffer on the remote SP.
  virtual VOID allocate_recv_buffer() {

    m_tpde_recvsize = 0;
    asINT32 per_ghostblk_recvsize = 0;
    sUBLK::add_pore_send_size(sim.init_solver_mask, per_ghostblk_recvsize);
    //no compressed states
    //m_recvsize += per_ghostblk_recvsize * m_quantums.size();
    m_recvsize = per_ghostblk_recvsize * m_quantums.size();
    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for pore ublks scale %d size %d to SP %d", m_scale, m_recvsize, source_rank());

    m_recv_msg.allocateBuffer(m_recvsize);
  }

  VOID process_quantum(const sUBLK_COMM_QUANTUM& quantum, sdFLOAT * &recv_buffer, ACTIVE_SOLVER_MASK active_solver_mask);

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) {}

} *UBLK_PORE_RECV_GROUP;
#endif

typedef class sNEARBLK_RECV_GROUP : public sUBLK_RECV_GROUP {

public:

  VOID allocate_recv_buffer() {

    // This should be called after all the quantums have been added, at which point m_recvsize
    // will already include the size of the states

    m_tpde_recvsize = m_recvsize;
    asINT32 per_ghostblk_recvsize = 0;
    asINT32 per_ghostblk_tpde_recvsize = 0;
    sUBLK::add_send_size_for_nearblk(sim.init_solver_mask, per_ghostblk_recvsize, per_ghostblk_tpde_recvsize);
    m_recvsize += per_ghostblk_recvsize * m_quantums.size();
    m_tpde_recvsize  += per_ghostblk_tpde_recvsize * m_quantums.size();
    asINT32 max_runtime_recvsize = MAX(m_recvsize, m_tpde_recvsize);

    asINT32 init_bytesize = sizeof(sNEARBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 init_floatsize = num_floats_from_num_bytes(init_bytesize);
    m_init_recvsize = init_floatsize;

    asINT32 allocated_recvsize = MAX(max_runtime_recvsize, init_floatsize);
#if BUILD_5G_LATTICE
    if (sim.is_large_pore) {
      asINT32 per_ghostblk_pore_recvsize = 0;
      sUBLK::add_pore_send_size(sim.init_solver_mask, per_ghostblk_pore_recvsize);
      asINT32 pore_recvsize = per_ghostblk_pore_recvsize * m_quantums.size();
      m_init_pore_recvsize = pore_recvsize;
      allocated_recvsize = MAX(allocated_recvsize, pore_recvsize);
  }
#endif
    allocated_recvsize++;
    m_recvsize++;

    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for nearblks scale %d size %d to SP %d",  m_scale, allocated_recvsize, source_rank());

    m_recv_msg.allocateBuffer(allocated_recvsize);

  }

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask); // This is the method that transfers the received message into the ghostblocks.
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask);
  VOID unpack_init_info();
  VOID unpack_conduction_init_info();

  VOID post_conduction_init_info_recv() {
    asINT32 cond_init_bytesize = sizeof(sCOND_NEARBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 cond_init_floatsize = num_floats_from_num_bytes(cond_init_bytesize);
    m_recv_msg.set_nelems(cond_init_floatsize);
    m_recv_msg.settag(eMPI_COND_GHOSTBLK_INIT_TAG);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

#if BUILD_5G_LATTICE
  //borrow to comm pore data during initialization
  VOID unpack_init_pore_data();
#endif

} *NEARBLK_RECV_GROUP;

typedef class sSURFEL_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>>
{
protected:

  mutable std::vector <sSURFEL_COMM_QUANTUM> m_quantums;

public:
  cExaMsg<uINT32, 1> m_recv_neigbor_ublks_data_size;
  asINT32       m_bc_type_recvsize;
  asINT32       m_potential_recvsize;

  sSURFEL_RECV_GROUP() {
    m_bc_type_recvsize = 0;
    m_potential_recvsize = 0;
  }

  std::vector<sSURFEL_COMM_QUANTUM>& quantums() { return m_quantums; }
  std::vector<sSURFEL_COMM_QUANTUM>& quantums() const { return m_quantums; }

  virtual VOID post_recv() {
    int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL>(m_scale);
    
    LOG_MSG("STRAND_SWITCHING").printf( "posting surfel recv: scale %d tag %x, size %d src %d buf %p", m_scale, tag, m_recvsize, source_rank(), fmt::ptr(m_recv_msg.buffer()));
    m_recv_msg.set_nelems(m_recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  VOID post_init_info_recv() {      // Used in pbl 
    LOG_MSG("STRAND_SWITCHING").printf( "posting surfel init recv: scale %d tag %x, size %d src %d buf %p", m_scale, eMPI_SURFEL_PRELIM_TAG, m_init_recvsize, source_rank(), fmt::ptr(m_recv_msg.buffer()));

    m_recv_msg.set_nelems(m_init_recvsize);
    m_recv_msg.settag(eMPI_SURFEL_PRELIM_TAG);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  VOID post_bc_type_recv() {
    LOG_MSG("STRAND_SWITCHING").printf( "posting surfel bc type recv: scale %d tag %x, size %d src %d buf %p", m_scale, eMPI_SURFEL_BCTYPE_TAG, m_bc_type_recvsize, source_rank(), fmt::ptr(m_recv_msg.buffer()));
    m_recv_msg.set_nelems(m_bc_type_recvsize);
    m_recv_msg.settag(eMPI_SURFEL_BCTYPE_TAG);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

#if BUILD_5G_LATTICE
  VOID post_potential_recv() {
    m_recv_msg.set_nelems(m_potential_recvsize);
    m_recv_msg.settag(eMPI_SURFEL_POTENTIAL_TAG);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }
  VOID unpack_potential();
#endif

  VOID allocate_recv_buffer() {

    asINT32 per_ghost_surfel_recvsize = 0;
    asINT32 per_ghost_surfel_tpde_recvsize = 0;
    asINT32 recv_count = m_quantums.size();
    for(const auto& quantum: m_quantums) {
      quantum.m_surfel->add_send_size(sim.init_solver_mask, per_ghost_surfel_recvsize, per_ghost_surfel_tpde_recvsize);
    }
    m_recvsize = per_ghost_surfel_recvsize ;
    m_tpde_recvsize = per_ghost_surfel_tpde_recvsize ;
    asINT32 max_runtime_recvsize = MAX(m_recvsize, m_tpde_recvsize);

    // The init message is not composed of floats. We send it as raw bytes and thus store
    // the size as a byte size.

    asINT32 init_bytesize = sizeof(sSURFEL_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
    asINT32 init_floatsize = num_floats_from_num_bytes(init_bytesize) ;
    m_init_recvsize = init_floatsize;

    asINT32 bctype_bytesize = sizeof(sSURFEL_BCTYPE_SEND_ELEMENT) * m_quantums.size();
    m_bc_type_recvsize = num_floats_from_num_bytes(bctype_bytesize) ;

#if BUILD_5G_LATTICE
    m_potential_recvsize = sizeof(sSURFEL_POTENTIAL_SEND_ELEMENT) * m_quantums.size();
#endif

    asINT32 allocated_recvsize = MAX(max_runtime_recvsize,init_floatsize ); 
    allocated_recvsize = MAX(allocated_recvsize, m_bc_type_recvsize); 

    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for surfels scale %d size %d to SP %d", m_scale, allocated_recvsize, source_rank());

    m_recv_msg.allocateBuffer(allocated_recvsize) ;
    
  }

  virtual VOID add_quantum(SURFEL surfel)
  {
    sSURFEL_COMM_QUANTUM q(surfel);
    m_quantums.push_back(q);
  }

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask); // This is the method that transfers the received message into the ghost surfels.
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask);  // For use during init. The index is not used.

  VOID unpack_init_info(); 
  VOID unpack_surfel_type();
  VOID unpack_bc_types(); 
  VOID recv_and_unpack_neighbor_ublks();

} *SURFEL_RECV_GROUP;

// Sampling surfel recv group only receives information during initialization.
typedef class sSAMPLING_SURFEL_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>>
{

protected:

  std::vector <sSAMPLING_SURFEL_COMM_QUANTUM> m_quantums;
  cExaMsg<uINT32, 1> m_recv_neighbor_ublks_data_size;

public:
  VOID add_quantum(sSAMPLING_SURFEL *surfel) {
    sSAMPLING_SURFEL_COMM_QUANTUM q(surfel);
    m_quantums.push_back(q);
  }
  std::vector<sSAMPLING_SURFEL_COMM_QUANTUM>& quantums() { return m_quantums; }

  virtual VOID post_recv() override {}
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) override {}
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override {}

  VOID recv_and_unpack_neighbor_ublks();
  VOID allocate_recv_buffer(uINT32 num_floats) {
    m_recv_msg.allocateBuffer(num_floats);
  }
} *SAMPLING_SURFEL_RECV_GROUP;

//--------- Recv fsets --------------------------------------------------

//------ Ublk recv fsets -------------------------------------------------


typedef struct sSP_RECV_GROUP_ORDER {
  BOOLEAN operator() (SP_RECV_GROUP a, SP_RECV_GROUP b) const
  {
    // just order by source processor number

    return (a->m_source_sp < b->m_source_sp);
  }

} *RECV_GROUP_ORDER;


typedef class sFARBLK_RECV_FSET : public tSP_FSET < FARBLK_RECV_GROUP, sSP_RECV_GROUP_ORDER>
{
  public:
  FARBLK_RECV_GROUP create_group(asINT32 scale, cNEIGHBOR_SP source_sp) {
    static sFARBLK_RECV_GROUP signature;
    signature.m_scale = scale;
    signature.m_source_sp = source_sp;

    FARBLK_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = xnew sFARBLK_RECV_GROUP;
      group->m_scale = scale;
      group->m_source_sp = source_sp;
      group->m_recv_msg.init(group->source_rank(), my_proc_id);
      group->m_recv_type = FARBLK_RECV_TYPE;
      group->m_is_near_surface = FALSE;
      group->m_is_mme_data = FALSE;
#if BUILD_5G_LATTICE
      group->m_is_pore_data = FALSE;
#endif
      add_group(group);
    }

    return group;
  }

} *FARBLK_RECV_FSET;

typedef class sNEARBLK_RECV_FSET : public tSP_FSET < NEARBLK_RECV_GROUP, sSP_RECV_GROUP_ORDER>
{
  public:
  NEARBLK_RECV_GROUP create_group(asINT32 scale, cNEIGHBOR_SP source_sp) 
  {
    static sNEARBLK_RECV_GROUP signature;
    signature.m_scale = scale;
    signature.m_source_sp = source_sp;

    NEARBLK_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = xnew sNEARBLK_RECV_GROUP;
      group->m_scale = scale;
      group->m_source_sp = source_sp;
      group->m_recv_msg.init(group->source_rank(), my_proc_id);
      group->m_recv_type = NEARBLK_RECV_TYPE;
      group->m_is_near_surface = TRUE;
      group->m_is_mme_data = FALSE;
#if BUILD_5G_LATTICE
      group->m_is_pore_data = FALSE;
#endif
      add_group(group);
    }

    return group;
  }

} *NEARBLK_RECV_FSET;

extern sFARBLK_RECV_FSET g_farblk_recv_fset;
extern sNEARBLK_RECV_FSET g_nearblk_recv_fset;

#define DO_FARBLK_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sFARBLK_RECV_FSET, g_farblk_recv_fset, group_var)

#define DO_NEARBLK_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sNEARBLK_RECV_FSET, g_nearblk_recv_fset, group_var)

#define DO_FARBLK_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sFARBLK_RECV_FSET, g_farblk_recv_fset, group_var, scale)

#define DO_NEARBLK_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sNEARBLK_RECV_FSET, g_nearblk_recv_fset, group_var, scale)





#define DO_GHOST_FARBLKS_OF_SCALE(quantum, scale)                                                                                              \
  FSET_FOREACH_GROUP_OF_SCALE(sFARBLK_RECV_FSET, g_farblk_recv_fset, group_var, scale)                                                          \
       for (auto& quantum: group_var->quantums())


#define DO_GHOST_NEARBLKS_OF_SCALE(quantum, scale)                                                                                             \
  FSET_FOREACH_GROUP_OF_SCALE(sNEARBLK_RECV_FSET, g_nearblk_recv_fset, group_var, scale)                                                        \
       for (auto& quantum: group_var->quantums())



//------ Surfel recv fsets -------------------------------------------------

typedef struct sSURFEL_RECV_FSET : 
        public tSP_FSET < SURFEL_RECV_GROUP, sSP_RECV_GROUP_ORDER>
{
  public:
  SURFEL_RECV_GROUP create_group (asINT32 scale, cNEIGHBOR_SP source_sp);
} *SURFEL_RECV_FSET;

extern sSURFEL_RECV_FSET g_surfel_recv_fset; 

#define DO_SURFEL_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sSURFEL_RECV_FSET, g_surfel_recv_fset, group_var)

#define DO_SURFEL_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_RECV_FSET, g_surfel_recv_fset, group_var, scale)

#define DO_GHOST_SURFELS_OF_SCALE(quantum, scale)                                                                                        \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_RECV_FSET, g_surfel_recv_fset, group_var, scale)                                                    \
    for (auto& quantum: group_var->quantums())

//------- Sampling Surfel recv fsets -----------------------------

typedef struct sSAMPLING_SURFEL_RECV_FSET :
        public tSP_FSET < SAMPLING_SURFEL_RECV_GROUP, sSP_RECV_GROUP_ORDER>
{
  public:
  sSAMPLING_SURFEL_RECV_GROUP *create_group (asINT32 scale, cNEIGHBOR_SP source_sp) {
    static sSAMPLING_SURFEL_RECV_GROUP signature;
    signature.m_scale = scale;
    signature.m_source_sp = source_sp;
    sSAMPLING_SURFEL_RECV_GROUP *group = find_group(&signature);
    if (NULL == group) {
      group = xnew sSAMPLING_SURFEL_RECV_GROUP;
      group->m_scale = scale;
      group->m_source_sp = source_sp;
      group->m_recv_msg.init(group->source_rank(), my_proc_id);
      //group->m_recv_type = SURFEL_RECV_TYPE;
      add_group(group);
    }
    return group;
  }
} *SAMPLING_SURFEL_RECV_FSET;

extern sSAMPLING_SURFEL_RECV_FSET g_sampling_surfel_recv_fset;

#define DO_SAMPLING_SURFEL_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sSAMPLING_SURFEL_RECV_FSET, g_sampling_surfel_recv_fset, group_var)

#define DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSAMPLING_SURFEL_RECV_FSET, g_sampling_surfel_recv_fset, group_var, scale)

//---------------- Miscellaneous functions ---------------------------

extern VOID *comm_thread_fcn(void * noarg);

ACTIVE_SOLVER_MASK prior_timestep_index_mask(asINT32 scale, asINT32 timestep, BOOLEAN is_seed);
VOID do_preliminary_ublk_comm(asINT32 timestep, BOOLEAN next_index, BOOLEAN is_seed = FALSE);
VOID do_calibration_ublk_comm(asINT32 timestep, BOOLEAN next_index);
VOID do_preliminary_surfel_comm();
VOID do_calibration_surfel_comm();
VOID do_preliminary_surfel_type_comm();
VOID do_surfel_potential_comm();
VOID do_send_surfel_bc_types();
VOID send_init_ublk_info();
VOID send_conduction_init_ublk_info();
VOID fixup_surfel_send_group_pointers();
size_t allocate_send_buffers();  // returns the number of groups
VOID allocate_recv_buffers();
VOID sim_finalize_shob_comm();
VOID comm_neighbor_ublks_for_surfels();
#if BUILD_5G_LATTICE
VOID do_preliminary_ublk_pore_comm();
#endif

#if !BUILD_GPU
INLINE auto& get_send_group_fset_by_build_type() {
  return g_surfel_send_fset;
}
#endif
#endif // _SIMENG_COMM_GROUPS_H
