#ifndef SCALAR_OR_ARRAY_H
#define SCALAR_OR_ARRAY_H

/*==============================================================================
 * @struct tSCALAR_OR_ARRAY
 * Holds an array of N elements of type T. When N = 1, the specialization avoids
 * the situation of defining an array of size 1, which is just a pointer to an element
 * of type T. Accessing this single element of type T via the pointer is not as 
 * performant as having just a single element of type T, since the pointer leads
 * to more instructions.
 *============================================================================*/
template<typename T, size_t N>
struct tSCALAR_OR_ARRAY {

  T m_v[N];

  tSCALAR_OR_ARRAY() = default;
  __HOST__ tSCALAR_OR_ARRAY(const T& v) { std::fill(m_v, m_v + N, v); }

  __HOST__ void copy(const tSCALAR_OR_ARRAY& other) { memcpy(m_v, other.m_v, sizeof(T) * N); }
  
  __HOST__ tSCALAR_OR_ARRAY(const tSCALAR_OR_ARRAY& other) { copy(other); }
  
  __HOST__ tSCALAR_OR_ARRAY& operator = (const tSCALAR_OR_ARRAY& other) {
    if (this != &other) {
      copy(other);
    }
    return *this;
  }
  
  tSCALAR_OR_ARRAY(tSCALAR_OR_ARRAY&& other) = delete;
  tSCALAR_OR_ARRAY& operator = (tSCALAR_OR_ARRAY&& other) = delete;

  //Support operator[] and operator () to provide an array like interface
  //Clients can use [] or ()  
  __HOST__DEVICE__ _ALWAYS_INLINE_ T& operator [] (int index) {
    cassert(index < N);
    return m_v[index];
  }
  __HOST__DEVICE__ _ALWAYS_INLINE_ const T& operator [] (int index) const {
    cassert(index < N);
    return m_v[index];
  }
  _ALWAYS_INLINE_ __HOST__DEVICE__ const T& operator()(int index) const {
    cassert(index < N);
    return m_v[index];
  }
  _ALWAYS_INLINE_ __HOST__DEVICE__ T& operator()(int index) {
    cassert(index < N);
    return m_v[index];
  }
};

/*==============================================================================
 * tSCALAR_OR_ARRAY specialization for scalars
 * Dummy element access operators [] and () allow for writing common code for
 * both arrays and scalars.
 * Implicit type conversion operators to transform tSCALAR_OR_ARRAY<T, 1> to 
 * T& and const T& greatly simplify compilation of T's and tSCALAR_OR_ARRAY<T, 1>'s
 * in arithmetic expressions (Ex in surfel or voxel dynamics)
 *============================================================================*/
template<typename T>
struct tSCALAR_OR_ARRAY<T, 1> {
  
  T m_v;

  tSCALAR_OR_ARRAY() = default;

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY(const U& v): m_v(v) {}

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY(tSCALAR_OR_ARRAY<U, 1>&& other) { m_v = other.m_v; }

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY& operator = (tSCALAR_OR_ARRAY<U, 1>&& other) {
    m_v = other.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY(tSCALAR_OR_ARRAY<U, 1>& other) { m_v = other.m_v; }

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY& operator = (const tSCALAR_OR_ARRAY<U, 1>& other) {
    m_v = other.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ tSCALAR_OR_ARRAY(const tSCALAR_OR_ARRAY<U, 1>& other) { m_v = other.m_v; }
  
  //Support operator[] and operator () to provide an array like interface
  //Clients can use [] or ()
  __HOST__DEVICE__ T& operator [] (int unused) {
    return m_v;
  }
  __HOST__DEVICE__ const T& operator [] (int unused) const {
    return m_v;
  }  
  __HOST__DEVICE__ const T& operator()(int unused = 0) const {
    return m_v;
  }
  __HOST__DEVICE__ T& operator()(int unused = 0)  {
    return m_v;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator+=(const tSCALAR_OR_ARRAY<U, 1>& s) {
    m_v += s.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator+=(U s) {
    m_v += s;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator-=(const tSCALAR_OR_ARRAY<U, 1>& s) {
    m_v -= s.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator-=(U s) {
    m_v -= s;
    return *this;
  }  

  template<typename U>
  __HOST__DEVICE__ auto& operator*=(const tSCALAR_OR_ARRAY<U, 1>& s) {
    m_v *= s.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator*=(U s) {
    m_v *= s;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator/=(const tSCALAR_OR_ARRAY<U, 1>& s) {
    m_v /= s.m_v;
    return *this;
  }

  template<typename U>
  __HOST__DEVICE__ auto& operator/=(U s) {
    m_v /= s;
    return *this;
  }
  
  //Cast operator
  __HOST__DEVICE__ operator T&() {
    return m_v;
  }  
  __HOST__DEVICE__ operator const T&() const {
    return m_v;
  }

  //Pointer dereference if applicable
  template<class Q = T>
  typename std::enable_if<std::is_pointer<Q>::value, Q>::type operator->()
  {
    return m_v;
  }

  template<class Q = T>
  typename std::enable_if<!std::is_pointer<Q>::value, Q>::type operator->() = delete;
};

// NOTE: The following operator overloads deviated from convention based on performance
// testing. Rather than return a tSCALAR_OR_ARRAY, these overloads return the 
// scalar type that is a result of a scalar operation between the first and second
// argument. The compiler can then chose to create a new tSCALAR_OR_ARRAY or optimize
// it out all together. This formulation is biased to avoid tSCALARY_OR_ARRAY temporaries
// and improves overall performance by 0.5 - 1%.
template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator+(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v + second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator+(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v + second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator+(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first + second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator-(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v - second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator-(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v - second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator-(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first - second.m_v;
}


template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator*(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v * second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator*(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v * second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator*(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first * second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator/(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v / second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator/(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v / second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator/(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first / second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator<(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v < second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator<(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v < second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator<(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first < second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator>(const tSCALAR_OR_ARRAY<T, 1>& first,
                                       const tSCALAR_OR_ARRAY<U, 1>& second) {
  return first.m_v > second.m_v;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator>(const tSCALAR_OR_ARRAY<T, 1>& first, U second) {
  return first.m_v > second;
}

template<typename T, typename U>
__HOST__DEVICE__ INLINE auto operator>(U first, const tSCALAR_OR_ARRAY<T, 1>& second) {
  return first > second.m_v;
}

template<typename T>
std::ostream& operator<<(std::ostream& os, const tSCALAR_OR_ARRAY<T, 1>& data) {
  os << T(data); return os;
}

/*==============================================================================
 * cast_as_regular_array
 * A HOST only function that allows temporarily converting tSFL_VAR<T, 1> array
 * type argument to a "regular" array of T. The primary use case is for functions 
 * that have not have been templatized on surfel count (or ported to GPU).
 * The idea is when these functions do get ported to GPUs, if the line code employing this
 * cast is not changed, the GPU compiler will issue a warning because this function cannot be
 * called from a device function. In this scenario, the developer must remove
 * this cast from the call site, and account how to deal with tSFL_VAR<T, N>  arrays,
 * where N can be 1 (regular surfel), or a mega surfel (N = 64 or larger)
 *============================================================================*/
template<typename T, size_t N>
/***DO NOT MAKE THIS HOST DEVICE!***/ __HOST__
inline auto cast_as_regular_array(tSCALAR_OR_ARRAY<T, 1> (&data) [N]) {
    return reinterpret_cast<T (&) [N]>(data);
}

template<typename T>
/***DO NOT MAKE THIS HOST DEVICE!***/ __HOST__
inline auto cast_as_regular_ptr(tSCALAR_OR_ARRAY<T, 1>* data) {
    return reinterpret_cast<T*>(data);
}

template<typename T, size_t N>
using tSFL_VAR = tSCALAR_OR_ARRAY<T, N>;

template<typename T, size_t N>
using tUBLK_VAR = tSCALAR_OR_ARRAY<T, N>;

namespace SCALAR_OR_ARRAY {
  template<typename A, typename B>
  struct LARGEST {
    using type = typename std::conditional<(sizeof(A) > sizeof(B)), A, B>::type;
  };
}

#if defined(_EXA_CLANG)
#define ARRAY_UNROLL _Pragma("clang loop unroll_count(3)")
#endif

//Support vector operations required in surfel/voxel dynamics that mimic old
//simulator macros vdot, vcopy etc...
template<typename DST, typename SRC, size_t N, size_t M>  
__HOST__DEVICE__ INLINE VOID v_copy(DST (&dst) [M],
                                    const tSCALAR_OR_ARRAY<SRC, N> (&src) [M],
                                    asINT32 index) {
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    dst[i] = src[i][index];
  }
}

//Signature changed here to avoid accidental use of incorrect overload
template<typename DST, typename SRC, size_t N, size_t M>  
__HOST__DEVICE__ INLINE VOID v_copy(tSCALAR_OR_ARRAY<DST, N> (&dst) [M],
                                    asINT32 index,
                                    const SRC (&src) [M]) {
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    dst[i][index] = src[i];
  }
}

template<typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ auto INLINE v_dot(tSCALAR_OR_ARRAY<A, N> (&a) [M],
                                   tSCALAR_OR_ARRAY<B, N> (&b) [M],
                                   asINT32 index) {
  using T = typename SCALAR_OR_ARRAY::LARGEST<A, B>::type;
  T sum = 0.0F;
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    sum += a[i][index] * b[i][index];
  }
  return sum;
}

template<typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ auto INLINE v_dot(A (&a) [M],
                                   tSCALAR_OR_ARRAY<B, N> (&b) [M],
                                   asINT32 index) {
  using T = typename SCALAR_OR_ARRAY::LARGEST<A, B>::type;
  T sum = 0.0F;
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    sum += a[i] * b[i][index];
  }
  return sum;
}

template<typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ auto INLINE v_dot(tSCALAR_OR_ARRAY<A, N> (&a) [M],
                                   B (&b) [M],
                                   asINT32 index) {
  using T = typename SCALAR_OR_ARRAY::LARGEST<A, B>::type;
  T sum = 0.0F;
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    sum += a[i][index] * b[i];
  }
  return sum;
}

template<typename R, typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ INLINE VOID v_sub(R (&out) [M],
                                   const tSCALAR_OR_ARRAY<A, N> (&a) [M],
                                   const tSCALAR_OR_ARRAY<B, N> (&b) [M],
                                   asINT32 index) {
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    out[i] = a[i][index] - b[i][index];
  }
}

template<typename R, typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ INLINE VOID v_sub(R (&out) [M],
                                   const tSCALAR_OR_ARRAY<A, N> (&a) [M],
                                   const B (&b) [M],
                                   asINT32 index) {
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    out[i] = a[i][index] - b[i];
  }
}

template<typename R, typename A, typename B, size_t N, size_t M>
__HOST__DEVICE__ INLINE VOID v_sub(R (&out) [M],
                                   const A (&a) [M],
                                   const tSCALAR_OR_ARRAY<B, N> (&b) [M],
                                   asINT32 index) {
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    out[i] = a[i] - b[i][index];
  }
}

template<typename T, size_t N, size_t M>
__HOST__DEVICE__ INLINE VOID v_unitize(tSCALAR_OR_ARRAY<T, N>  (&a) [M],
                                       asINT32 index)
{
  dFLOAT _mag = sqrt(v_dot(a,a, index));
  ARRAY_UNROLL
  for (int i = 0; i < M; i++) {
    a[i][index] /= _mag;    
  }
}

// template<typename T, size_t N, size_t M>
// __HOST__DEVICE__ T add(tSCALAR_OR_ARRAY<T, N> (&a) [M],
//                        tSCALAR_OR_ARRAY<T, N> (&b) [M],
//                        asINT32 index) {
//   T sum = 0.0F;
//   for (int i = 0; i < M; i++) {
//     sum += a[i][index] + b[i][index];
//   }
//   return sum;
// }

// template<typename T, size_t N, size_t M>
// __HOST__DEVICE__ VOID inc(tSCALAR_OR_ARRAY<T, N> (&a) [M],
//                           tSCALAR_OR_ARRAY<T, N> (&b) [M],
//                           asINT32 index) {
//   for (int i = 0; i < M; i++) {
//     a[i][index] += b[i][index];
//   }
// }

// template<typename T, size_t N, size_t M>
// __HOST__DEVICE__ VOID dec(tSCALAR_OR_ARRAY<T, N> (&a) [M],
//                           tSCALAR_OR_ARRAY<T, N> (&b) [M],
//                           asINT32 index) {
//   for (int i = 0; i < M; i++) {
//     a[i][index] -= b[i][index];
//   }
// }

// template<typename T, size_t N, size_t M>
// __HOST__DEVICE__ VOID mul(tSCALAR_OR_ARRAY<T, N> (&a) [M],
//                           T s,
//                           asINT32 index) {
//   for (int i = 0; i < M; i++) {
//     a[i][index] *= s;
//   }
// }

#if defined(_EXA_CLANG)
#undef ARRAY_UNROLL
#endif
#endif
