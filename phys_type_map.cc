/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "phys_type_map.h"
#include "sim.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"

// Global instance
sINT32 *g_cdi_volume_physics_desc_index_from_part_index = NULL;
sINT32 g_num_parts = 0;

BODY_FORCE_PHYSICS_DESCRIPTOR *g_cdi_body_force_desc_from_part_index = NULL;

sINT32 g_num_faces = 0;
sINT32 *g_face_index_to_part_index = NULL;
sINT32 *g_face_index_to_opposite_face_index = NULL;
sINT8  *g_face_index_is_front_only = NULL;
sINT32 *g_cdi_front_flow_surface_physics_desc_index_from_face_index = NULL;
sINT32 *g_cdi_front_thermal_surface_physics_desc_index_from_face_index = NULL;
sINT32 *g_cdi_back_flow_surface_physics_desc_index_from_face_index = NULL;
sINT32 *g_cdi_back_thermal_surface_physics_desc_index_from_face_index = NULL;
dFLOAT *g_flow_bc_total_areas = NULL;
dFLOAT *g_part_total_volumes = NULL;
sINT32 *g_cdi_tire_physics_desc_index_from_face_index = NULL;


//----------------------------------------------------------------------------
// is_cdi_physics_type functions
//----------------------------------------------------------------------------
BOOLEAN is_cdi_physics_type_basic_fluid(asINT32 cdi_physics_type)
{
  switch (cdi_physics_type) {
  case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
  case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
  case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
  case ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE:
    return FALSE;
  default:
    return TRUE;
  }
}

BOOLEAN is_cdi_physics_type_fluid_like(asINT32 cdi_physics_type)
{
  switch (cdi_physics_type) {
    case ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE:
    case CDI_PHYS_TYPE_SOLID_CONDUCTOR:
      return FALSE;
    default:
      return TRUE;
  }
}

BOOLEAN is_cdi_physics_type_apm(asINT32 cdi_physics_type)
{
  switch (cdi_physics_type) {
    case ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE:
      return TRUE;
    default:
      return FALSE;
  }
}

BOOLEAN is_cdi_physics_type_conduction_solid(asINT32 cdi_physics_type)
{
  switch (cdi_physics_type) {
    case CDI_PHYS_TYPE_SOLID_CONDUCTOR:
      return TRUE;
    default:
      return FALSE;
  }
}

BOOLEAN is_pd_fluid_like(PHYSICS_DESCRIPTOR pd)
{
  asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
  return is_cdi_physics_type_fluid_like(cdi_phys_type);
}

BOOLEAN is_pd_apm(PHYSICS_DESCRIPTOR pd)
{
  asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
  return is_cdi_physics_type_apm(cdi_phys_type);
}

BOOLEAN is_pd_conduction_solid(PHYSICS_DESCRIPTOR pd)
{
  asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
  return is_cdi_physics_type_conduction_solid(cdi_phys_type);
}

//----------------------------------------------------------------------------
// voxel_sim_type_from_cdi_type (method stolen from the discretizer)
//----------------------------------------------------------------------------
STP_PHYSTYPE_TYPE voxel_sim_type_from_cdi_type(PHYSICS_DESCRIPTOR fluid_phys_desc, BOOLEAN is_near_surf)
{
  // There is no difference between near-surface and far-from-surface
  // fan and porous media voxel dyn groups.

  asINT32 cdi_phys_type = fluid_phys_desc->phys_type_desc->cdi_physics_type;
//  if(cdi_phys_type == CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID)
//  cdi_phys_type = CDI_PHYS_TYPE_SOLID_CONDUCTOR;
  switch (cdi_phys_type) {
  case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
    return STP_FAN_VVFLUID_TYPE;
  case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
#if BUILD_GPU
    msg_error("Fans not supported on GPU");
#endif
    return STP_TABLE_FAN_VVFLUID_TYPE;
  case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE: {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    if (cdi_phys_type == CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL || cdi_phys_type == CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL) {
#if BUILD_GPU
      msg_error("Curved Heat Exchangers are not supported on GPU");
#endif
      return STP_CURVED_HX_POROUS_VVFLUID_TYPE;
    }
    else if (pd->parameters()->is_curved.value) {
#if BUILD_GPU
      msg_error("Curved Porous Media is not supported on GPU");
#endif
        return STP_CURVED_POROUS_VVFLUID_TYPE;
    }
    else {
      return STP_POROUS_VVFLUID_TYPE;
    }
  }
  case ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE:
  {
#if BUILD_GPU
      msg_error("Acoustic Porous Media is not supported on GPU");
#endif
    ACOUSTIC_POROUS_MEDIA_PHYSICS_DESCRIPTOR pd =
        (ACOUSTIC_POROUS_MEDIA_PHYSICS_DESCRIPTOR) fluid_phys_desc;
    if (pd->parameters()->is_curved.value)
      return STP_CURVED_POROUS_VVFLUID_TYPE;
    else
      return STP_POROUS_VVFLUID_TYPE;
  }
  case CDI_PHYS_TYPE_SOLID_CONDUCTOR:
    return STP_CONDUCTION_SOLID_TYPE;

  case CDI_PHYS_TYPE_SOLID_INSULATOR:
    return STP_INSULATOR_SOLID_TYPE;

  default:    
    if (is_near_surf)
      return STP_NEAR_SURFACE_VVFLUID_TYPE;
    else
      return STP_VVFLUID_TYPE;
  }

  return STP_INVALID_PHYSTYPE_TYPE;
}

STP_PHYSTYPE_TYPE voxel_sim_type_from_cdi_type(asINT32 cdi_phys_type, BOOLEAN is_near_surf)
{
  // There is no difference between near-surface and far-from-surface
  // fan and porous media voxel dyn groups.

  switch (cdi_phys_type) {
  case FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
#if BUILD_GPU
      msg_error("Fans are not supported on GPU");
#endif
    return STP_FAN_VVFLUID_TYPE;
  case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE:
#if BUILD_GPU
      msg_error("Fans are not supported on GPU");
#endif
    return STP_TABLE_FAN_VVFLUID_TYPE;
  case POROUS_CDI_FLUID_PHYSICS_TYPE_CASE: {
    if (cdi_phys_type == CDI_PHYS_TYPE_CURVED_HX_POROUS_VV_FLUID_THERMAL ||
        cdi_phys_type == CDI_PHYS_TYPE_CURVED_HX_POROUS_LES_VV_FLUID_THERMAL) {
#if BUILD_GPU
      msg_error("Curved HX are not supported on GPU");
#endif
      return STP_CURVED_HX_POROUS_VVFLUID_TYPE;
    }
    else {
      return STP_POROUS_VVFLUID_TYPE;
    }
  }
  case ACOUSTIC_POROUS_FLUID_PHYSICS_TYPE_CASE:
#if BUILD_GPU 
      msg_error("Acoustic Porous Media is not supported on GPU");
#endif
    return STP_POROUS_VVFLUID_TYPE;
  case CDI_PHYS_TYPE_SOLID_CONDUCTOR:
    return STP_CONDUCTION_SOLID_TYPE;
  case CDI_PHYS_TYPE_SOLID_INSULATOR:
    return STP_INSULATOR_SOLID_TYPE;

  default:    
    if (is_near_surf)
      return STP_NEAR_SURFACE_VVFLUID_TYPE;
    else
      return STP_VVFLUID_TYPE;
  }
  assert(false);
  return STP_INVALID_PHYSTYPE_TYPE;
}
//----------------------------------------------------------------------------
// surfel_sim_type_from_cdi_type (method stolen from the discretizer)
//----------------------------------------------------------------------------
STP_PHYSTYPE_TYPE surfel_sim_type_from_cdi_type(PHYSICS_DESCRIPTOR surface_phys_desc)
{
  asINT32 cdi_phys_type = surface_phys_desc->phys_type_desc->cdi_physics_type;

  switch (cdi_phys_type) {
  case CDI_PHYS_TYPE_SLIP95:
    return STP_SLIP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_TRUE_NOSLIP:
  case CDI_PHYS_TYPE_5G_DNS_WALL:
  case CDI_PHYS_TYPE_5G_DNS_FRICTIONLESS_WALL:
    return STP_NOSLIP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_SLIP95_FIXED_TEMP:
    return STP_SLIP_FIXED_TEMP_SURFEL_TYPE;
      
  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP:
    return STP_NOSLIP_FIXED_TEMP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX:
    return STP_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
      
  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX:
    return STP_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;

  case CDI_PHYS_TYPE_STATIC_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR_TFLOAT:
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR_TFLOAT:
  case CDI_PHYS_TYPE_5G_PRESS_FIXED_DIR_BC:
    return STP_STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE;

  case CDI_PHYS_TYPE_STAG_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR_TFLOAT:
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR_TFLOAT:
    return STP_STAG_PRESSURE_FREE_DIR_SURFEL_TYPE;

  case CDI_PHYS_TYPE_STAG_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR_TFLOAT:
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR:
  case CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR_TFLOAT:
    return STP_STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE;

  case CDI_PHYS_TYPE_STATIC_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR_TFLOAT:
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR:
  case CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR_TFLOAT:
  case CDI_PHYS_TYPE_5G_PRESS_BC:  
    return STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_SLIP:
    return STP_LINEAR_SLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_VEL_SLIP:
    return STP_VEL_SLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_SLIP:
    return STP_ANGULAR_SLIP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_NOSLIP:
  case CDI_PHYS_TYPE_5G_DNS_LINEAR_WALL:
    return STP_LINEAR_NOSLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
  case CDI_PHYS_TYPE_5G_DNS_ANGULAR_WALL:
    return STP_ANGULAR_NOSLIP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP:
    return STP_LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP:
    return STP_ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP:
    return STP_LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP:
    return STP_ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX:
    return STP_LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX:
    return STP_ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX:
    return STP_LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX:
    return STP_ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;

  case CDI_PHYS_TYPE_MASS_FLUX:
  case CDI_PHYS_TYPE_NEW_MASS_FLUX:
  case CDI_PHYS_TYPE_NEW_MASS_FLUX_TFLOAT:
  case CDI_PHYS_TYPE_LES_MASS_FLUX:
  case CDI_PHYS_TYPE_LES_MASS_FLUX_TFLOAT:
  case CDI_PHYS_TYPE_5G_MASS_FLUX_BC:
    return STP_MASS_FLUX_SURFEL_TYPE;

  case CDI_PHYS_TYPE_MASS_FLOW:
  case CDI_PHYS_TYPE_LES_MASS_FLOW:
    return STP_MASS_FLOW_SURFEL_TYPE;

  case CDI_PHYS_TYPE_FIXED_VEL:
  case CDI_PHYS_TYPE_FIXED_VEL_TFLOAT:
  case CDI_PHYS_TYPE_LES_FIXED_VEL:
  case CDI_PHYS_TYPE_LES_FIXED_VEL_TFLOAT:
  {
    FIXED_VEL_SURFEL_PARAMETERS par = (FIXED_VEL_SURFEL_PARAMETERS) surface_phys_desc->parameters();
    if (par->turb_synth_spec.table_id.value >= 0) {
      return STP_TURB_VEL_SURFEL_TYPE;
    } else {
      return STP_FIXED_VEL_SURFEL_TYPE;
    }
  }

  case CDI_PHYS_TYPE_SOURCE_SURFEL:
  case CDI_PHYS_TYPE_SOURCE_SURFEL_TFLOAT:
  case CDI_PHYS_TYPE_LES_SOURCE_SURFEL:
  case CDI_PHYS_TYPE_LES_SOURCE_SURFEL_TFLOAT:
  case CDI_PHYS_TYPE_5G_PRESS_VEL_BC:
  {
    SOURCE_SURFEL_PARAMETERS par = (SOURCE_SURFEL_PARAMETERS) surface_phys_desc->parameters();
    if (par->turb_synth_spec.table_id.value >= 0) {
      return STP_TURB_SOURCE_SURFEL_TYPE;
    } else {
      return STP_SOURCE_SURFEL_TYPE;
    }
  }

  case CDI_PHYS_TYPE_PASS_THRU:
    return STP_PASS_THRU_SURFEL_TYPE;

  case CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST:
    return STP_SLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST:
    return STP_NOSLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST:
    return STP_LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST:
    return STP_ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST:
    return STP_LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST:
    return STP_ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE;

  case CDI_PHYS_TYPE_APM_SLIP95:
    return STP_APM_SLIP_ISURFEL_TYPE;
  case CDI_PHYS_TYPE_APM_TRUE_NOSLIP:
    return STP_APM_NOSLIP_ISURFEL_TYPE;
  case CDI_PHYS_TYPE_APM_LINEAR_SLIP:
    return STP_APM_LINEAR_SLIP_ISURFEL_TYPE;
  case CDI_PHYS_TYPE_APM_LINEAR_NOSLIP:
    return STP_APM_LINEAR_NOSLIP_ISURFEL_TYPE;
  case CDI_PHYS_TYPE_APM_ANGULAR_SLIP:
    return STP_APM_ANGULAR_SLIP_ISURFEL_TYPE;
  case CDI_PHYS_TYPE_APM_ANGULAR_NOSLIP:
    return STP_APM_ANGULAR_NOSLIP_ISURFEL_TYPE;

  case CDI_PHYS_TYPE_ADIABATIC:
    return STP_CONDUCTION_ADIABATIC_SURFEL_TYPE;
  case CDI_PHYS_TYPE_FIXED_TEMP:
    return STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_THERMAL_RESIST: // For conduction surfels, this is the same BC as FIXED_HTC_AMBIENT_TEMP
  case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
    return STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_FIXED_HEAT_FLOW:
  case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
    return STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case CDI_PHYS_TYPE_COUPLED_THERMAL:
    return STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE;
  case CDI_PHYS_TYPE_CONTACT_RESISTANCE:
    return STP_CONDUCTION_CONTACT_SURFEL_TYPE;

  case CDI_PHYS_TYPE_SLIP95_COUPLED:
    return STP_SLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:
    return STP_NOSLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:
    return STP_LINEAR_SLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:
    return STP_LINEAR_NOSLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:
    return STP_ANGULAR_SLIP_SURFEL_TYPE;
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED:
    return STP_ANGULAR_NOSLIP_SURFEL_TYPE;

  default:
    return STP_INVALID_PHYSTYPE_TYPE;
  }
}

#include <unordered_map>

typedef struct sPHYS_DESC_PAIR {
  sINT32 m_wall_surface_phys_desc_index;
  sINT32 m_thermal_surface_phys_desc_index;
} *PHYS_DESC_PAIR;

struct sPHYS_DESC_PAIR_HASH {
  size_t operator() (const sPHYS_DESC_PAIR& pair) const {
    size_t hash = pair.m_wall_surface_phys_desc_index | (pair.m_thermal_surface_phys_desc_index << 16);
    return hash;
  }
};

struct sPHYS_DESC_PAIR_EQ {
  bool operator() (const sPHYS_DESC_PAIR& pair1, const sPHYS_DESC_PAIR& pair2) const {
    return (pair1.m_wall_surface_phys_desc_index == pair2.m_wall_surface_phys_desc_index 
            && pair1.m_thermal_surface_phys_desc_index == pair2.m_thermal_surface_phys_desc_index);
  }
};

typedef std::pair<sINT32, sINT32> sPD_PAIR;

static std::unordered_map<sPHYS_DESC_PAIR, sINT32, sPHYS_DESC_PAIR_HASH, sPHYS_DESC_PAIR_EQ> thermal_wall_phys_desc_map;

asINT32 find_merged_wall_and_thermal_surface_phys_desc(asINT32 wall_surface_phys_desc_index, asINT32 thermal_surface_phys_desc_index)
{
  sPHYS_DESC_PAIR pd_pair;
  pd_pair.m_wall_surface_phys_desc_index = wall_surface_phys_desc_index;
  pd_pair.m_thermal_surface_phys_desc_index = thermal_surface_phys_desc_index;

  auto merged_phys_desc_iterator = thermal_wall_phys_desc_map.find(pd_pair);
  if (merged_phys_desc_iterator != thermal_wall_phys_desc_map.end())
    return merged_phys_desc_iterator->second;
  return -1;
}

static void copy_rad_pd(PHYSICS_DESCRIPTOR wall, PHYSICS_DESCRIPTOR thermal)
{
  asINT32 wall_rad_idx = wall->phys_type_desc->continuous_dp_index(CDI_VAR_ID_RADIATION_SURFACE_COND);
  asINT32 thermal_rad_idx = thermal->phys_type_desc->continuous_dp_index(CDI_VAR_ID_RADIATION_SURFACE_COND);

  if (wall_rad_idx >= 0 && thermal_rad_idx >= 0) {
    wall->_parameters[wall_rad_idx] = thermal->_parameters[thermal_rad_idx];
  }

}

asINT32 register_thermal_wall_surface_phys_desc(PHYSICS_DESCRIPTOR wall_surface_phys_desc, 
                                                PHYSICS_DESCRIPTOR thermal_surface_phys_desc,
                                                asINT32 new_wall_cdi_phys_type,
                                                asINT32 n_new_parameters,
                                                PHYSICS_VARIABLE new_parameters, // vector of new parameters
                                                std::vector<sPHYSICS_DESCRIPTOR> &flow_surface_phys_descs)
{
  asINT32 wall_surface_phys_desc_index = wall_surface_phys_desc->index;
  asINT32 thermal_surface_phys_desc_index = thermal_surface_phys_desc->index;

  sPHYS_DESC_PAIR pd_pair;
  pd_pair.m_wall_surface_phys_desc_index = wall_surface_phys_desc_index;
  pd_pair.m_thermal_surface_phys_desc_index = thermal_surface_phys_desc_index;

  auto merged_phys_desc_iterator = thermal_wall_phys_desc_map.find(pd_pair);
  if (merged_phys_desc_iterator != thermal_wall_phys_desc_map.end())
    return merged_phys_desc_iterator->second;
  else {
    CDI_PHYS_TYPE_DESCRIPTOR new_phys_type_desc = cdi_lookup_physics(new_wall_cdi_phys_type);
    sPHYSICS_DESCRIPTOR merged_phys_desc;

    merged_phys_desc = *wall_surface_phys_desc;
    merged_phys_desc.phys_type_desc = new_phys_type_desc;
    // if (n_new_parameters > 0) {
      merged_phys_desc.n_parameters = new_phys_type_desc->n_continuous_dp; // more than wall_surface_phys_desc
      merged_phys_desc._parameters = xnew sPHYSICS_VARIABLE [ merged_phys_desc.n_parameters ];

      ccDOTIMES(i, wall_surface_phys_desc->n_parameters)
        merged_phys_desc._parameters[i] = wall_surface_phys_desc->_parameters[i];

      ccDOTIMES(i, n_new_parameters) {
        merged_phys_desc._parameters[ wall_surface_phys_desc->n_parameters + i ] = new_parameters[i];

        if (new_parameters[i].is_time_varying)
          merged_phys_desc.some_parameter_time_varying = TRUE;

        if (new_parameters[i].is_time_varying && new_parameters[i].is_space_varying)
          merged_phys_desc.some_parameter_time_and_space_varying = TRUE;

        if (new_parameters[i].is_time_varying && !new_parameters[i].is_space_varying)
          merged_phys_desc.some_parameter_time_varying_only = TRUE;

        if (!new_parameters[i].is_time_varying && !new_parameters[i].is_space_varying && new_parameters[i].is_eqn)
          merged_phys_desc.some_constant_parameter_in_need_of_eval = TRUE;

        if (!new_parameters[i].is_sharable())
          merged_phys_desc.all_parameters_sharable = FALSE;
      }
    // }
    // The following is needed to build the list of surfels later that are coupled to PowerTHERM
    merged_phys_desc.coupling_model_index = thermal_surface_phys_desc->coupling_model_index;
    merged_phys_desc.init_from_coupling_model_p = thermal_surface_phys_desc->init_from_coupling_model_p;

    merged_phys_desc.index = flow_surface_phys_descs.size();
    auto& m = flow_surface_phys_descs.emplace_back(merged_phys_desc);
    
    // put new phys desc in hash table
    thermal_wall_phys_desc_map.insert(std::make_pair(pd_pair, m.index));

    copy_rad_pd(&m, thermal_surface_phys_desc);

    return merged_phys_desc.index;
  }
}

struct sPHYS_DESC_HASH {
  size_t operator() (const sINT32 &pd) const {
    return pd;
  }
};

struct sPHYS_DESC_EQ {
  bool operator() (const sINT32 &pd1, const sINT32 &pd2) const {
    return pd1 == pd2;
  }
};

static std::unordered_map<sINT32, sINT32, sPHYS_DESC_HASH, sPHYS_DESC_EQ> heat_flux_phys_desc_map;

asINT32 find_phys_desc_with_swapped_in_out_heat_fluxes(asINT32 heat_flux_phys_desc_index)
{
  auto phys_desc_iterator = heat_flux_phys_desc_map.find(heat_flux_phys_desc_index);
  if (phys_desc_iterator != heat_flux_phys_desc_map.end())
    return phys_desc_iterator->second;
  return -1;
}

asINT32 register_phys_desc_with_swapped_in_out_heat_fluxes(PHYSICS_DESCRIPTOR _heat_flux_phys_desc, // type CDI_PHYS_TYPE_FIXED_HEAT_FLUX
                                                           std::vector<sPHYSICS_DESCRIPTOR> &thermal_surface_phys_descs)
{
  CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR heat_flux_phys_desc = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)_heat_flux_phys_desc;

  auto phys_desc_iterator = heat_flux_phys_desc_map.find(heat_flux_phys_desc->index);
  if (phys_desc_iterator != heat_flux_phys_desc_map.end())
    return phys_desc_iterator->second;
  else {
    sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR new_phys_desc;

    new_phys_desc = *(CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)heat_flux_phys_desc;
    // account for the additional (not in CDI) face_area and is_heat_flux parameters 
    // same code in init_physics_desc()
    new_phys_desc._parameters = cnew sPHYSICS_VARIABLE [ new_phys_desc.n_parameters + 2];
    ccDOTIMES(i, new_phys_desc.n_parameters + 2) {
      new_phys_desc._parameters[i] = heat_flux_phys_desc->_parameters[i];
    }

    // swap heat flux values
    new_phys_desc.parameters()->heat_flux_1 = heat_flux_phys_desc->parameters()->heat_flux_2;
    new_phys_desc.parameters()->heat_flux_2 = heat_flux_phys_desc->parameters()->heat_flux_1;
    
    auto heat_flux_phys_desc_index = heat_flux_phys_desc->index;
    new_phys_desc.index = thermal_surface_phys_descs.size();

    // this call invalidates the heat_flux_phys_desc variable!
    // Don't use _heat_flux_phys_desc variable after this line!
    thermal_surface_phys_descs.push_back(new_phys_desc);
    
    // put new phys desc in hash table
    heat_flux_phys_desc_map.insert(std::make_pair(heat_flux_phys_desc_index, new_phys_desc.index));

    return new_phys_desc.index;
  }
}







//----------------------------------------------------------------------------
// cdi_physics_type_for_region
//----------------------------------------------------------------------------
/*asINT32 sPHYS_TYPE_MAP::cdi_phys_type(uINT32 region_id)
{
  PHYSICS_DESCRIPTOR       pd            = physics_desc(region_id);
  CDI_PHYS_TYPE_DESCRIPTOR ptd           = pd ->phys_type_desc;
  asINT32                  cdi_phys_type = ptd->cdi_physics_type;
  return cdi_phys_type;
}
*/

//----------------------------------------------------------------------------
// physics_descriptor
//----------------------------------------------------------------------------
/*STP_PHYSTYPE_TYPE sPHYS_TYPE_MAP::sim_phys_type(uINT32 region_id, BOOLEAN is_near_surf)
{
  asINT32           cdi_type = cdi_phys_type( region_id );
  STP_PHYSTYPE_TYPE sim_phys_type = sim_type_from_cdi_type(cdi_type,is_near_surf);
  return sim_phys_type;
}*/

