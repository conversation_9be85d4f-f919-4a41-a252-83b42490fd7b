#include "gtest/gtest.h"
#include "../bsurfel.h"

class BsurfelTestFixture : public testing::Test {

protected:
  sBSURFEL m_bsurfel;
  sUBLK m_ublk;
  sUBLK_TABLE m_ublk_table;
  dFLOAT m_char_density = 1.0;
  std::unique_ptr<sLRF_PHYSICS_DESCRIPTOR[]> m_lrf_physics_descs;
  std::unique_ptr<sMOVB_PHYSICS_DESCRIPTOR[]> m_movb_physics_descs;
  sMEAS_WINDOW_COLLECTION m_meas_windows;

  virtual void SetUp() {
    cDGF_BSURFEL_DESC bsurfel_desc;
    cDGF_BSURFEL_GEOM bsurfel_geom;
    m_movb_physics_descs = std::unique_ptr<sMOVB_PHYSICS_DESCRIPTOR[]>( new sMOVB_PHYSICS_DESCRIPTOR[1] );
    m_char_density = 1.0;

    // Setup the bsurfel information
    bsurfel_desc.bs.bsurfel_id = 1900;
    bsurfel_desc.bs.bsurfel_scale = 5;
    bsurfel_desc.bs.ublk_id = 2249;
    bsurfel_desc.bs.voxel = 3;
    bsurfel_desc.bs.face_index = 1;

    // Not going to test measurement windows or lrfs
    // because this would involve more setup
    bsurfel_desc.bs.num_meas_bsurfel_refs = 0;
    bsurfel_desc.bs.lrf_index = -1;  

    bsurfel_desc.vertices[0].vertex_index = 0;
    bsurfel_desc.vertices[1].vertex_index = 1;
    bsurfel_desc.vertices[2].vertex_index = 2;

    bsurfel_geom.phys_desc_index = 0;
    bsurfel_geom.centroid[0] = 3.14159;
    bsurfel_geom.centroid[1] = 2.718;
    bsurfel_geom.centroid[2] = 1.618;

    bsurfel_geom.normal[0] = 3.0/std::sqrt(50.0);
    bsurfel_geom.normal[1] = 4.0/std::sqrt(50.0);
    bsurfel_geom.normal[2] = 5.0/std::sqrt(50.0);

    bsurfel_geom.area = std::sqrt(3);

    // Now setup the ublk_table with a dummy ublk
    m_ublk.set_id(2249);
    m_ublk_table.add(&m_ublk);

    m_bsurfel.init(&bsurfel_desc, 
                 &bsurfel_geom, 
                 m_ublk_table, 
                 m_char_density, 
                 m_lrf_physics_descs, 
                 m_movb_physics_descs, 
                 m_meas_windows);

    EXPECT_EQ(m_bsurfel.id(), 1900);
    EXPECT_EQ(&m_ublk, m_bsurfel.home_ublk());
    EXPECT_EQ(3,  m_bsurfel.home_voxel());
    EXPECT_TRUE(m_bsurfel.is_active());
    EXPECT_EQ(m_bsurfel.centroid(0), bsurfel_geom.centroid[0]);
    EXPECT_EQ(m_bsurfel.centroid(1), bsurfel_geom.centroid[1]);
    EXPECT_EQ(m_bsurfel.centroid(2), bsurfel_geom.centroid[2]);
    EXPECT_EQ(m_bsurfel.normal(0), bsurfel_geom.normal[0]);
    EXPECT_EQ(m_bsurfel.normal(1), bsurfel_geom.normal[1]);
    EXPECT_EQ(m_bsurfel.normal(2), bsurfel_geom.normal[2]);
    EXPECT_EQ(m_bsurfel.m_next, nullptr);
    EXPECT_EQ(m_bsurfel.m_prev, nullptr);
    EXPECT_EQ(m_bsurfel.m_group, nullptr);
    EXPECT_EQ(m_bsurfel.m_area, bsurfel_geom.area);
    EXPECT_EQ(m_bsurfel.m_init_centroid[0], bsurfel_geom.centroid[0]);
    EXPECT_EQ(m_bsurfel.m_init_centroid[1], bsurfel_geom.centroid[1]);
    EXPECT_EQ(m_bsurfel.m_init_centroid[2], bsurfel_geom.centroid[2]);
    EXPECT_EQ(m_bsurfel.m_meas_cell_refs, nullptr);
    EXPECT_EQ(m_bsurfel.m_lrf_physics_desc, nullptr);
    EXPECT_EQ(m_bsurfel.m_movb_physics_desc, &m_movb_physics_descs[0]);
  }

  virtual void TearDown() {}

};
