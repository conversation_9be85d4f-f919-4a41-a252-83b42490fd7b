/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "common_sp.h"
#include "shob.h"
#include "face_polygons.h"
#include "ublk.h"
#include "bsurfel_util.h"


struct HOME_VOXEL_UBLK {
  TAGGED_UBLK m_tagged_ublk;
  UBLK        m_home_ublk;
  uINT8       m_home_voxel;
  sINT8       m_split_ublk_instance;
  HOME_VOXEL_UBLK() {
    m_tagged_ublk = 0;
    m_home_ublk = nullptr;
    m_home_voxel = ubFLOAT::N_VOXELS;
    m_split_ublk_instance = -1;
  }
  bool is_valid() {
    return ((m_home_ublk != nullptr) && !m_home_ublk->is_solid() && !m_home_ublk->is_mirror() && (m_home_voxel < ubFLOAT::N_VOXELS));
  }
  bool operator == (const HOME_VOXEL_UBLK& other) {
    return ((m_home_ublk == other.m_home_ublk) && (m_home_voxel == other.m_home_voxel));
  }
};

// To encapsulate the ublk and voxel pairs across a face
struct HOP_ACROSS_FACE {
  HOME_VOXEL_UBLK m_start;
  HOME_VOXEL_UBLK m_end;
  uINT8 m_face;
  bool m_try_vr_fine;
  bool m_try_vr_coarse;

  // if a neighbor across this face is split will multiple polygons on the face,
  // this struct contains an invalid end ublk.
  // split_advect_info is null, ublk instances will not be set
  HOP_ACROSS_FACE(UBLK start_ublk, uINT8 start_voxel, uINT8 face,
                  bool skip_split_ublk_instance_assignment = false,
                  sSPLIT_ADVECT_INFO *split_advect_info = nullptr) {
    const auto& box_access = start_ublk->m_box_access;
    m_face = face;

    m_start.m_home_ublk = start_ublk;
    m_start.m_tagged_ublk = box_access.stagged_ublk();
    m_start.m_home_voxel = start_voxel;

    m_end.m_home_voxel = neighbor_voxel_along_axis(start_voxel, face_index_to_axis(face));
    m_try_vr_fine = false; 
    m_try_vr_coarse = false; 
    if (face_index_to_direction(face) < 0) { //backward face
      if (m_end.m_home_voxel < start_voxel) {
        // still within the same ublk
        m_end.m_tagged_ublk = m_start.m_tagged_ublk;
      } else {
        // crossed over to backward ublk
        m_end.m_tagged_ublk = box_access.backward_neighbor(face_index_to_axis(face));
      }
    } else { // forward face
      if (m_end.m_home_voxel > start_voxel) {
        // still within the same ublk
        m_end.m_tagged_ublk = m_start.m_tagged_ublk;
      } else {
        // crossed over to forward ublk
        m_end.m_tagged_ublk = box_access.forward_neighbor(face_index_to_axis(face));
      }
    }

    if (m_end.m_tagged_ublk.is_ublk_scale_interface()) {
      if (start_ublk->is_vr_coarse()){
        m_try_vr_fine = true;
      } else {
#if ENABLE_CONSISTENCY_CHECKS
        msg_print("Interface not implemented");
#endif
      }
    } else if (m_end.m_tagged_ublk.is_ublk_split()) {
      if (!skip_split_ublk_instance_assignment) {
        sSPLIT_NEIGHBOR_INFO *split_neighbor_info = start_ublk->split_neighbor_info();
        if (split_neighbor_info == nullptr) {
          msg_internal_error("split neighbor info should have been allocated for Ublk %d", start_ublk->id());
        }
        TAGGED_UBLK *split_ublks = m_end.m_tagged_ublk.split_ublks()->tagged_ublks();
        if (split_neighbor_info->any_polygons_on_face(m_start.m_home_voxel, m_face)) {
          // There are more than one candidate ublks across this face. This is an invalid option
        } else if (split_neighbor_info->is_ublk_instance_to_be_set(start_voxel, face)) {
          // This branch is only accessed during face connectivity
          sTAGGED_SPLIT_FACTOR split_factors[MAX_SPLIT_UBLKS];
          asINT32 n_splits =
              tagged_neighbor_from_split_ublk_site(m_end.m_tagged_ublk,
                                                   m_start.m_home_ublk->advect_from_split_mask(),
                                                   m_start.m_home_voxel, m_face,
                                                   split_advect_info,
                                                   m_start.m_home_ublk->m_split_tagged_instance.get(),
                                                   split_factors);
          cassert(n_splits == 1);
          cassert(split_factors[0].tagged_neighbor.ublk());
          m_end.m_home_ublk = split_factors[0].tagged_neighbor.ublk();
          ccDOTIMES(instance, m_end.m_tagged_ublk.split_ublks()->num_split_ublks()) {
            if (split_factors[0].tagged_neighbor.ublk() == split_ublks[instance].ublk()) {
              m_end.m_split_ublk_instance = instance;
              break;
            }
          }
        } else if (split_neighbor_info->is_ublk_instance_single(start_voxel, face)) {
          cassert(split_neighbor_info->ublk_instance(start_voxel, face) < m_end.m_tagged_ublk.split_ublks()->num_split_ublks());
          TAGGED_UBLK split_tagged_ublk = split_ublks[split_neighbor_info->ublk_instance(start_voxel, face)];
          m_end.m_home_ublk = split_tagged_ublk.ublk();
          m_end.m_split_ublk_instance = split_neighbor_info->ublk_instance(start_voxel, face);
        }
      }
    } else if (m_end.m_tagged_ublk.is_ublk()) {
      m_end.m_home_ublk = m_end.m_tagged_ublk.ublk();
      m_end.m_split_ublk_instance = 0;
    } else {
      if (start_ublk->is_vr_fine()){
        m_try_vr_coarse = true;
      }
      // This can happen during initialization
      //msg_internal_error("in a null ublk");
    }
  }
};

bool sCONVEX_VERTICES_LOOP::does_point_overlap(dFLOAT pt[2]) {
  dFLOAT diff[2], edge[2];
  ccDOTIMES(v, m_n_vertices) {
    sFPOLY_VERTEX &s_vertex = g_face_polygon_info.fpoly_vertex(m_vertex_indices[v]);
    sFPOLY_VERTEX &e_vertex = v == m_n_vertices-1 ? g_face_polygon_info.fpoly_vertex(m_vertex_indices[0]) :
                                                    g_face_polygon_info.fpoly_vertex(m_vertex_indices[v+1]);
    edge[0] = e_vertex.m_plane_coords[0] - s_vertex.m_plane_coords[0];
    edge[1] = e_vertex.m_plane_coords[1] - s_vertex.m_plane_coords[1];

    diff[0] = pt[0] - s_vertex.m_plane_coords[0];
    diff[1] = pt[1] - s_vertex.m_plane_coords[1];

    if ((edge[0]*diff[1] - edge[1]*diff[0]) < 0) {
      return false;
    }
  }
  return true;
}

cFACE_POLYGONS_INFO g_face_polygon_info;
sVOXEL_FACE_POLYGON::sVOXEL_FACE_POLYGON(uINT8 num_loops, uINT8 voxel,
                                           uINT8 voxel_face, uINT8 neighbor_instance) {
  m_voxel_face = voxel_face;
  m_voxel = voxel;
  m_num_loops = 0;
  m_ublk_instance = neighbor_instance;
  m_vertex_loops = new sCONVEX_VERTICES_LOOP[num_loops];
}

VOID sVOXEL_FACE_POLYGON::add_convex_loop(cDGF_VOXEL_FPOLYGON_DESC &polygon_desc) {
  m_vertex_loops[m_num_loops++] = sCONVEX_VERTICES_LOOP(polygon_desc);
}

bool sVOXEL_FACE_POLYGON::does_point_overlap(dFLOAT pt[2]) {

  ccDOTIMES(loop, m_num_loops) {
    if (m_vertex_loops[loop].does_point_overlap(pt))
      return true;
  }
  return false;
}

sVOXEL_FACE_POLYGON cFACE_POLYGONS_INFO::add_face_polygons(sINT32 num_loops, uINT8 voxel,
                                                            uINT8 voxel_face, uINT8 neighbor_instance) {
  return sVOXEL_FACE_POLYGON(num_loops, voxel, voxel_face, neighbor_instance);
}

VOID sSPLIT_NEIGHBOR_INFO::init(sUBLK *ublk) {
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDOTIMES(neighbor, N_3D_NEIGHBOR_VOXELS) {
      m_start_face_polygon_indices[voxel][neighbor] = 0;
      if (neighbor < FIRST_CORNER_NEIGHBOR) {
      STP_LATVEC_INDEX latvec_index = neighbor_to_latvec_index(neighbor);
        if (!ublk->is_voxel_path_connected_along_latvec(voxel, latvec_index)) {
          m_ublk_instances[voxel][neighbor] = NEIGHBORS_NONE;
        } else {
          m_ublk_instances[voxel][neighbor] = NEIGHBORS_SINGLE;
        }
      } else {
        m_ublk_instances[voxel][neighbor] = NEIGHBORS_MULTIPLE;
      }
    }
  }
}

VOID sSPLIT_NEIGHBOR_INFO::init_face_connectivity(std::vector<sVOXEL_FACE_POLYGON> &voxel_face_polygons) {
  uINT8 prev_voxel = -1, prev_voxel_face = -1;
  sVOXEL_FACE_POLYGON prev_polygon;
  bool more_than_one = false;
  for(sVOXEL_FACE_POLYGON polygon : voxel_face_polygons) {
    uINT8 voxel = polygon.m_voxel;
    uINT8 voxel_face = polygon.m_voxel_face;
    more_than_one = ((voxel_face == prev_voxel_face) && (prev_voxel == voxel));
    if (!more_than_one) {
      cassert(is_ublk_instance_to_be_set(voxel, voxel_face));
      m_ublk_instances[voxel][voxel_face] = polygon.m_ublk_instance;
    } else {
      cassert(is_valid_instance(m_ublk_instances[voxel][voxel_face]) ||
              are_ublk_instances_multiple(voxel, voxel_face));
      m_ublk_instances[voxel][voxel_face] = NEIGHBORS_MULTIPLE;
      // storing the count of polygons on each face initially.
      if (m_start_face_polygon_indices[voxel][voxel_face]) {
        m_start_face_polygon_indices[voxel][voxel_face]++;
        m_face_polygons.push_back(polygon);
      } else {
        m_start_face_polygon_indices[voxel][voxel_face] = 2;
        m_face_polygons.push_back(prev_polygon);
        m_face_polygons.push_back(polygon);
      }
    }
    prev_voxel = voxel;
    prev_voxel_face = voxel_face;
    prev_polygon = polygon;
  }

  // convert the count of polygons on each face to start indices.
  int count = 0, total_count = 0;
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDOTIMES(neighbor, N_3D_NEIGHBOR_VOXELS) {
      count = m_start_face_polygon_indices[voxel][neighbor];
      m_start_face_polygon_indices[voxel][neighbor] = total_count;
      total_count += count;
    }
  }
}

VOID sSPLIT_NEIGHBOR_INFO::update_face_connectivity(sUBLK *ublk) {
  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

  // set ublk_instances for voxels which are neighbors of whole voxels, but part of a split ublk
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDOTIMES(neighbor, N_FACE_NEIGHBORS) {
      if (is_ublk_instance_to_be_set(voxel, neighbor)) {
        HOP_ACROSS_FACE hop(ublk, voxel, neighbor, false, split_advect_info);
        if (hop.m_end.m_split_ublk_instance > -1) {
          m_ublk_instances[voxel][neighbor] = hop.m_end.m_split_ublk_instance;
        }
      }
    }
  }
}

VOID sSPLIT_NEIGHBOR_INFO::set_face_single_ublk_instance(sUBLK *ublk) {
  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDOTIMES(neighbor, N_FACE_NEIGHBORS) {
      if (is_ublk_instance_to_be_set(voxel, neighbor)) {
        HOP_ACROSS_FACE hop_face(ublk, voxel, neighbor, false, split_advect_info);
        if (hop_face.m_end.m_split_ublk_instance > -1)
          m_ublk_instances[voxel][neighbor] = hop_face.m_end.m_split_ublk_instance;
      }
    }
  }
}

// update the split ublk instance indices to be consistent with the simulator
VOID sSPLIT_NEIGHBOR_INFO::update_split_ublk_instances_across_faces(sUBLK *ublk) {
  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDOTIMES(neighbor, N_FACE_NEIGHBORS) {
      HOP_ACROSS_FACE hop(ublk, voxel, neighbor, true);
      if (hop.m_end.m_tagged_ublk.is_ublk_split()) {
        sUBLK_VECTOR *split_ublks = hop.m_end.m_tagged_ublk.split_ublks();
        if (is_ublk_instance_single(voxel, neighbor)) {

          cassert(is_ublk_instance_single(voxel, neighbor));
          uINT32 old_ublk_instance = ublk_instance(voxel, neighbor);
          uINT32 new_ublk_instance = new_split_ublk_instance(split_ublks, old_ublk_instance,
                                                             ublk, voxel, neighbor);
          cassert(new_ublk_instance < split_ublks->m_n_ublks);
          m_ublk_instances[voxel][neighbor] = new_ublk_instance;
        } else if (are_ublk_instances_multiple(voxel, neighbor)) {
          int start_index = m_start_face_polygon_indices[voxel][neighbor];
          int end_index = m_start_face_polygon_indices[voxel][neighbor+1];
          for (int ipolygon= start_index; ipolygon < end_index; ipolygon++) {
            sVOXEL_FACE_POLYGON &fpolygon = m_face_polygons[ipolygon];
            uINT32 old_ublk_instance = fpolygon.m_ublk_instance;
            uINT32 new_ublk_instance = new_split_ublk_instance(split_ublks, old_ublk_instance,
                                                               ublk, voxel, neighbor);
            cassert(new_ublk_instance < split_ublks->m_n_ublks);
            fpolygon.m_ublk_instance = new_ublk_instance;
          }
        }
      }
    }
  }
}

VOID sSPLIT_NEIGHBOR_INFO::update_edge_connectivity(sUBLK *ublk) {

  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info1 = ublk->get_split_advect_info();  

  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    ccDO_FROM_BELOW(neighbor, FIRST_EDGE_NEIGHBOR, FIRST_EDGE_NEIGHBOR + N_EDGE_NEIGHBORS) {
      STP_LATVEC_INDEX latvec_index = neighbor_to_latvec_index(neighbor);
      if (!ublk->is_voxel_path_connected_along_latvec(voxel, latvec_index))
        continue;
      dFLOAT corner1[3];
      int face_indices[2];
      corner1[0] = state_vel(latvec_index, 0);
      corner1[1] = state_vel(latvec_index, 1);
      corner1[2] = state_vel(latvec_index, 2);
      if (corner1[0] == 0) {
        face_indices[0] = ((1 - (int)(corner1[STP_Y_AXIS])) >> 1) + STP_POS_Y_FACE;
        face_indices[1] = ((1 - (int)(corner1[STP_Z_AXIS])) >> 1) + STP_POS_Z_FACE;
      }
      if (corner1[1] == 0) {
        face_indices[0] = ((1 - (int)(corner1[STP_X_AXIS])) >> 1) + STP_POS_X_FACE;
        face_indices[1] = ((1 - (int)(corner1[STP_Z_AXIS])) >> 1) + STP_POS_Z_FACE;
      }
      if (corner1[2] == 0) {
        face_indices[0] = ((1 - (int)(corner1[STP_X_AXIS])) >> 1) + STP_POS_X_FACE;
        face_indices[1] = ((1 - (int)(corner1[STP_Y_AXIS])) >> 1) + STP_POS_Y_FACE;
      }

      if (!is_ublk_instance_to_be_set(voxel, neighbor)) {
        msg_internal_error("U %d V %d neighbor %d instance %d", ublk->id(), voxel, neighbor,
                           m_ublk_instances[voxel][neighbor]);
      }
      bool found_single_instance = false;
      { // explore first path
        HOP_ACROSS_FACE hop1(ublk, voxel, face_indices[0]);
        if (hop1.m_end.is_valid()) {
	  auto& box_access = hop1.m_end.m_home_ublk->m_box_access;
	  SPLIT_ADVECT_INFO split_advect_info2 = hop1.m_end.m_home_ublk->get_split_advect_info();
          HOP_ACROSS_FACE hop2(hop1.m_end.m_home_ublk, hop1.m_end.m_home_voxel, face_indices[1]);
          if (hop2.m_end.is_valid()) {
            m_ublk_instances[voxel][neighbor] = hop2.m_end.m_split_ublk_instance;
            found_single_instance = true;
          }
        }
      }
      if (found_single_instance) { // explore second path
        HOP_ACROSS_FACE hop1(ublk, voxel, face_indices[1]);
        if (hop1.m_end.is_valid()) {
	  auto& box_access = hop1.m_end.m_home_ublk->m_box_access;
	  SPLIT_ADVECT_INFO split_advect_info2 = hop1.m_end.m_home_ublk->get_split_advect_info();

          HOP_ACROSS_FACE hop2(hop1.m_end.m_home_ublk, hop1.m_end.m_home_voxel, face_indices[0]);
          if (hop2.m_end.is_valid() && (m_ublk_instances[voxel][neighbor] == hop2.m_end.m_split_ublk_instance)) {
            found_single_instance = true;
          } else {
            found_single_instance = false;
          }
        } else {
          found_single_instance = false;
        }
      }
      if (!found_single_instance) {
        m_ublk_instances[voxel][neighbor] = NEIGHBORS_MULTIPLE;
      }
    }
  }
}

bool compute_intersection_on_face(uINT8 face, dFLOAT start_pt[3], dFLOAT end_pt[3], dFLOAT voxel_dx,
                                  dFLOAT intersecting_pt[3]) {
  STP_AXIS axis = stp_face_to_axis(face);
  dFLOAT plane_loc = MAX(lattice_location_from_position(start_pt[axis], voxel_dx), lattice_location_from_position(end_pt[axis], voxel_dx));
  
  dFLOAT interpolate_factor = 0.0;
    if (end_pt[axis] - start_pt[axis] != 0.0)
      interpolate_factor = (plane_loc - start_pt[axis]) / (end_pt[axis] - start_pt[axis]);

  if(!(interpolate_factor <= 1.0)) {
#if 0
    msg_print("interpolate_factor %g is out of range for segment between %g %g %g and %g %g %g.",
              interpolate_factor,
              start_pt[0],
              start_pt[1],
              start_pt[2],
              end_pt[0],
              end_pt[1],
              end_pt[2]);
#endif
    throw sHOME_UBLK_LOCATOR::SearchError("expected intersection with face polygon not found");
  }

  //cassert(interpolate_factor <= 1.0);

  ccDOTIMES(a, N_AXES) {
    if (a == axis)
      intersecting_pt[a] = plane_loc;
    else {
      intersecting_pt[a] = interpolate_factor * (end_pt[a] - start_pt[a]) + start_pt[a];
    }
  }
  return true;
}

UBLK sSPLIT_NEIGHBOR_INFO::find_split_ublk_across_face(dFLOAT pt[3], sUBLK *ublk, uINT8 voxel, uINT8 face_index) {

  HOP_ACROSS_FACE hop_face(ublk, voxel, face_index);
  bool vr_coarse_tested = false;
  if (hop_face.m_try_vr_fine){
    vr_coarse_tested = true;
    sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
    if (vr_coarse_data->vr_fine_ublk(voxel))
      ublk = vr_coarse_data->vr_fine_ublk(voxel).ublk();
    
    dFLOAT ublk_offset[3] = {0, 0, 0};
    bool inside = sHOME_UBLK_LOCATOR::calc_centroid_offset_from_ublk(ublk, pt, ublk_offset);    
    if (!inside){
#if ENABLE_CONSISTENCY_CHECKS
      msg_print("intersecting point not inside fine ublk");
#endif
      return nullptr;
    }
    voxel = sHOME_UBLK_LOCATOR::locate_voxel(ublk_offset); 
    HOP_ACROSS_FACE hop_face2(ublk, voxel, face_index);
    hop_face = hop_face2;
  }

  if (hop_face.m_try_vr_coarse && !vr_coarse_tested){
    sVR_FINE_INTERFACE_DATA * vr_fine_data = ublk->vr_fine_data();
    ublk = vr_fine_data->vr_coarse_ublk().ublk();

    dFLOAT ublk_offset[3] = {0, 0, 0};
    bool inside = sHOME_UBLK_LOCATOR::calc_centroid_offset_from_ublk(ublk, pt, ublk_offset);    
    if (!inside){
#if ENABLE_CONSISTENCY_CHECKS
      msg_print("intersecting point not inside coarse ublk");
#endif
      return nullptr;
    }
    voxel = sHOME_UBLK_LOCATOR::locate_voxel(ublk_offset); 
    HOP_ACROSS_FACE hop_face2(ublk, voxel, face_index);
    hop_face = hop_face2;
  }

  sINT32 instance_index  = -1;
  if (hop_face.m_end.is_valid()) {
    instance_index = hop_face.m_end.m_split_ublk_instance;
  } else {
    dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
    instance_index  = find_overlapping_fpolygon(pt, voxel_dx, voxel, face_index);
  }
  if(instance_index > -1) {
    if (hop_face.m_end.m_tagged_ublk.is_ublk_split()) {
      cassert(instance_index < hop_face.m_end.m_tagged_ublk.split_ublks()->num_split_ublks());
      return hop_face.m_end.m_tagged_ublk.split_ublks()->tagged_ublks()[instance_index].ublk();
    } else if (hop_face.m_end.m_tagged_ublk.is_ublk_scale_interface()) {
#if ENABLE_CONSISTENCY_CHECKS
      msg_print("Cannot be an interface");
#endif
      return nullptr;
    } else if (hop_face.m_end.m_tagged_ublk.is_ublk()) {
      cassert(instance_index == 0);
      return hop_face.m_end.m_tagged_ublk.ublk();
    } else {
#if ENABLE_CONSISTENCY_CHECKS
      msg_print("Cannot be a null ublk");
#endif
      return nullptr;
    }
  } else {
    if (hop_face.m_end.m_home_ublk != nullptr && hop_face.m_end.m_home_ublk->is_mirror()){
      return hop_face.m_end.m_home_ublk;
    } else {
      //msg_print("instance index is less than 0"); //Enough already!
      return nullptr;
    }
  }
}

int sSPLIT_NEIGHBOR_INFO::find_overlapping_fpolygon(dFLOAT pt[3], dFLOAT voxel_dx, uINT8 voxel, uINT8 face_index) {
  //cassert(pt[face_index_to_axis(face_index)] == dFLOAT(face_index_to_direction(face_index)));
  int start_index = m_start_face_polygon_indices[voxel][face_index];
  int end_index = m_start_face_polygon_indices[voxel][face_index+1];
  dFLOAT pt_2d[2];
  convert_to_2D_fpolygon_coord(pt, voxel_dx, face_index, pt_2d);
  for (int ipolygon= start_index; ipolygon < end_index; ipolygon++) {
    sVOXEL_FACE_POLYGON &fpolygon = m_face_polygons[ipolygon];
    if (fpolygon.does_point_overlap(pt_2d))
      return fpolygon.m_ublk_instance;
  }
  return -1;
}

/*
 * This function converts a 3D coord to 2D on the given plate
 * (x, y, z) to (y, z) in x plate
 *           to (z, x) in y plate
 *           to (x, y) in z plate 
 */
void sSPLIT_NEIGHBOR_INFO::convert_to_2D_fpolygon_coord(dFLOAT pt[3], dFLOAT voxel_dx, uINT8 face_index, dFLOAT pt_2d[2]) {
  dFLOAT ivdx = 1.0/voxel_dx;
  if(face_index_to_axis(face_index) != 1){
    int a = 0;
    ccDOTIMES(axis, N_AXES) {
      if (face_index_to_axis(face_index) != axis) {
        pt_2d[a++] = (pt[axis] - lattice_location_from_position(pt[axis], voxel_dx))*ivdx;
      }
    }
  } else {
    int a = 1;
    ccDOTIMES(axis, N_AXES) {
      if (face_index_to_axis(face_index) != axis) {
        pt_2d[a--] = (pt[axis] - lattice_location_from_position(pt[axis], voxel_dx))*ivdx;
      }
    }
  }
}

STP_LATVEC_INDEX sSPLIT_NEIGHBOR_INFO::neighbor_to_latvec_index(uINT8 neighbor) {
  //This function maps a neighbor index from one of the 26 surrounding
  //cubes to the corresponding lattice vector index (if it exists) for
  //whichever lattice is being compiled.

  //This is being used in order to test path connectivity with a
  //ublk's m_path_connect_masks, the indexing into which is dependent
  //on the lattice type, given a 3d neighbor index that can be used in
  //something like this class' m_ublk_instances array.
  
  STP_LATVEC_INDEX latvec_index = N_LATTICE_VECTORS; //used as an invalid value
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
  //For the D19 and 5G lattice, the mapping is 1 to 1 up until the
  //tri-diagonal neighbors which dont have corresponsing lattice
  //vectors.
  if(neighbor < FIRST_CORNER_NEIGHBOR) {
    latvec_index = neighbor; 
  } else {
    //Triple diagonal neighbors on the D19 lattice are handled as a
    //special condition.  Care must be taken to not test the path
    //connectivity in these neighbor directions.
    latvec_index = N_LATTICE_VECTORS;
  }
#elif BUILD_D39_LATTICE
  if(is_face_neighbor(neighbor)) {
    //The mapping is 1 to 1 for the first 6 face neighbors.
    latvec_index = neighbor;
  } else {
    //For edge or corner neighbors, skip over the 6 latvecs for the speed 2 face neighbors.
    latvec_index = neighbor + 6;
  }
 
#else
#error "face_polygons.c needs to be updated to support a new lattice type"
  //Determine a mapping for a new lattice by inspecting the lattice
  //vectors in the phystypes component and comparing to the D19 and
  //D39 lattices.  If there are less lattice vectors than D19, a new
  //way to test path connectivity will need to be devised.
#endif  
  return latvec_index;
}
