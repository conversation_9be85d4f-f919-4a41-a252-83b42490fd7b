/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include <typeinfo>
#include "mlrf.h"
#include "particle_mlrf.h"
#include "particle_sim.h"
#include "particle_sim_info.h"
#include PF_LOG_H

#if ENABLE_LOGGING
#if DEBUG_MLRF_PARCEL_COMM
#define PRINT_TO_LOG
#endif
#endif

#if 1 //Use the stock MLRF_DO loop macros
#define PARCEL_MLRF_DO_LOCAL_SEGMENTS(segment_length, send_buffer_offset, remote_proc, ring, rotation_offset) \
  MLRF_DO_SEND_MESSAGES(segment_length, send_buffer_offset, remote_proc, ring, rotation_offset)
#define PARCEL_MLRF_DO_REMOTE_SEGMENTS(segment_length, receive_buffer_offset, remote_proc, ring, rotation_offset) \
  MLRF_DO_RECV_MESSAGES(segment_length, receive_buffer_offset, remote_proc, ring, rotation_offset)
#else //But eventually, we'd prefer to ignore the overlap once everything else is working.
#define PARCEL_MLRF_DO_LOCAL_SEGMENTS(segment_length, send_buffer_offset, remote_proc, ring, rotation_offset) \
  MLRF_DO_LOCAL_SEGMENTS_WITH_NO_OVERLAP(segment_length, send_buffer_offset, remote_proc, ring, rotation_offset)
#define PARCEL_MLRF_DO_REMOTE_SEGMENTS(segment_length, receive_buffer_offset, remote_proc, ring, rotation_offset) \
  MLRF_DO_REMOTE_SEGMENTS_WITH_NO_OVERLAP(segment_length, receive_buffer_offset, remote_proc, ring, rotation_offset)
#endif

//#define DISABLE_PARTICLE_MLRF

//Set filters for debugging messages.
#if 0
#define MLRF_PARCEL_OF_INTEREST(parcel) ((parcel->id) == -8)
#define TIME_OF_INTEREST(time) ((time) < 256)
#define DEBUG_RING_OF_INTEREST(ring_index) ((ring_index) == 3 )
#define DEBUG_DIRECTION_OF_INTEREST(direction) ((direction) == sMLRF_PARCEL_HALF_RING::EXTERNAL_TO_INTERNAL)
#else //or debug everything
#define MLRF_PARCEL_OF_INTEREST(parcel) (TRUE)
#define TIME_OF_INTEREST(time) (TRUE)
#define DEBUG_RING_OF_INTEREST(ring_index) (TRUE)
#define DEBUG_DIRECTION_OF_INTEREST(direction) (TRUE)
#endif

#ifdef PRINT_TO_LOG
#define  MLRF_PARCEL_SEND_DEBUG_CONDITION(ring_index)    ((my_proc_id == 0) && TIME_OF_INTEREST(g_timescale.time_flow()) && DEBUG_RING_OF_INTEREST(ring_index))
#define  MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(ring_index)   ((my_proc_id == 0) && TIME_OF_INTEREST(g_timescale.time_flow()) && DEBUG_RING_OF_INTEREST(ring_index))
#else
#define  MLRF_PARCEL_SEND_DEBUG_CONDITION(ring_index)    (FALSE)
#define  MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(ring_index)   (FALSE)
#endif

const char* sMLRF_PARCEL_HALF_RING::m_interface_direction_to_string[BOTH_DIRECTIONS] =
  {"EXTERNAL",
      "INTERNAL"};

template<>
VOID sMLRF_SURFEL_GROUP::count_parcels_and_fill_send_buffers()  //In all segments of all rings:
{
  //For each ring's segments, count and fill a send buffer with parcel states.
  TAGGED_MLRF_SURFEL* first_surfel_in_ring = this->quantum(0); //first surfel of all rings in group
  ccDOTIMES(nth_ring, this->n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    dFLOAT angle_rotated = m_lrf_physics_desc->angle_rotated;
    BOOLEAN debug = MLRF_PARCEL_SEND_DEBUG_CONDITION(nth_ring);

    ring->count_parcels_and_fill_send_buffers(first_surfel_in_ring, angle_rotated, debug);
    first_surfel_in_ring += 2 * ring->m_n_surfels_per_frame_on_proc;
  }
  //At this point, the send buffer for each ring's segments should be populated and the parcel count for each segment computed.
  //Also, the parcels have all been removed from the LRF surfels' lists.
}


VOID sMLRF_PARCEL_RING::count_parcels_and_fill_send_buffers( //In both frames of a ring's segments:
                                                            TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                                            dFLOAT angle_rotated,
                                                            asINT32 debug)
{
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    this->half_ring(direction)->count_parcels_and_fill_send_buffers(
                                                                    first_surfel_in_ring,
                                                                    angle_rotated,
                                                                    this,
                                                                    debug && DEBUG_DIRECTION_OF_INTEREST(direction));
  }
}


VOID calculate_mlrf_rotating_surfel_position(dFLOAT angle_rotated,
                                             asINT32 n_surfels_in_ring,
                                             BOOLEAN is_exterior_surfel,
                                             dFLOAT surfel_overlapping_ratio[2]);

VOID sMLRF_PARCEL_HALF_RING::count_parcels_and_fill_send_buffers(  //In one frame of a ring's segments:
                                                                 TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                                                 dFLOAT angle_rotated,
                                                                 MLRF_RING ring,
                                                                 asINT32 debug)
{
  m_num_active_local_segments = 0;
  std::fill(m_segment_send_counts.begin(), m_segment_send_counts.end(), 0); //Clear the parcel count of all segments.
  dFLOAT angle_in_surfels = angle_rotated * ring->m_one_over_angle_per_surfel;
  asINT32 offset = rotation_offset_for_frame(angle_in_surfels);
  TAGGED_MLRF_SURFEL* first_surfel_in_frame = this->first_surfel_in_frame(first_surfel_in_ring);

#if 0
  dFLOAT surfel_overlap_ratio[2];
  calculate_mlrf_rotating_surfel_position(angle_rotated,
                                          m_n_surfels_per_frame_in_ring,
                                          m_direction == sMLRF_PARCEL_HALF_RING::EXTERNAL_TO_INTERNAL,
                                          surfel_overlap_ratio);
#endif

  PARCEL_MLRF_DO_LOCAL_SEGMENTS(segment_length, send_buffer_offset, remote_proc, ring, offset) {

    //Cache what SP this segment communicates with for when the sends are posted.
    m_segment_destination_proc[m_num_active_local_segments] = remote_proc;
    //Count the parcels in each mlrf surfel.
    ccDOTIMES(nth_surfel_in_segment, segment_length) {
      asINT32 surfel_index = (nth_surfel_in_segment + send_buffer_offset) % m_n_surfels_per_frame_on_proc;
      TAGGED_MLRF_SURFEL tagged_surfel = first_surfel_in_frame[surfel_index];
      if (tagged_surfel.is_surfel_weightless()) {
        // This is not a real surfel, so don't process it
        continue;
      }
      SURFEL surfel = tagged_surfel.mlrf_surfel();
      asINT32 num_parcels = surfel->p_data()->surface_parcel_list->size();
      m_segment_send_counts[m_num_active_local_segments] += num_parcels;
    }

    if(debug && m_segment_send_counts[m_num_active_local_segments] > 0) {
      LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
        printf("COUNTED SEGMENT PARCELS ts %ld ring %d %s segment %d offset %d of length %d for SP%d has send buffer offset %d and %d particles.",
               g_timescale.m_time,
               m_ring_index,
               this->direction_string(),
               m_num_active_local_segments,
               offset,
               segment_length,
               remote_proc,
               send_buffer_offset,
               m_segment_send_counts[m_num_active_local_segments]);
    }

    //Allocate a send buffer for this segment.
    ccDOTIMES(i, m_parcel_send_buffers[m_num_active_local_segments].size()) {
      //Flag the objects properly so the destructor doesn't complain when clear is called.
      m_parcel_send_buffers[m_num_active_local_segments][i].change_dynamics_type(ABOUT_TO_BE_DELETED);
    }

    m_parcel_send_buffers[m_num_active_local_segments].clear();
    if(m_parcel_send_buffers[m_num_active_local_segments].capacity() < m_segment_send_counts[m_num_active_local_segments]) {
      m_parcel_send_buffers[m_num_active_local_segments].reserve(m_segment_send_counts[m_num_active_local_segments]);
    }

    //Make a second pass over the surfels and fill the send buffer.
    ccDOTIMES(nth_surfel_in_segment, segment_length) {
      asINT32 surfel_index = (nth_surfel_in_segment + send_buffer_offset) % m_n_surfels_per_frame_on_proc;
      TAGGED_MLRF_SURFEL tagged_surfel = first_surfel_in_frame[surfel_index];
      if (tagged_surfel.is_surfel_weightless()) {
        // This is not a real surfel, so don't process it
        continue;
      }
      SURFEL surfel = tagged_surfel.mlrf_surfel();
      PARCEL_LIST parcel_list =  surfel->p_data()->surface_parcel_list;
      parcel_list->reset();
      while(!parcel_list->exhausted()) {
        PARCEL_STATE parcel = parcel_list->data();

        if(PARCEL_IS_INTERESTING(parcel)) {
          msg_print("parcel %d:%d with dynamics type %d:%s is being packed for sending off mlrf surfel %d (is ghost %d is weightless %d) at timestep %ld (ring %d segment %d).",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    surfel->id(),
                    surfel->is_ghost(),
                    surfel->is_weightless_mlrf(),
                    g_timescale.m_time,
                    m_ring_index,
                    m_num_active_local_segments);
        }

        //Hijack the home voxel field so we can match this particle to the
        //corresponding surfel on the opposite side of the frame.
          parcel->m_home_voxel = nth_surfel_in_segment ;

          parcel->lrf_index = surfel->ref_frame_index();
          m_parcel_send_buffers[m_num_active_local_segments].push_back(*(parcel));
          parcel_list->remove();
          particle_sim.destroy_parcel(surfel, parcel, TRUE, FALSE);

#ifdef PRINT_TO_LOG
        parcel = &(m_parcel_send_buffers[m_num_active_local_segments].back()); //use the parcel data from the send buffer from here on out since it has been destroyed
        asINT32 new_state_index = parcel->relative_timestep_to_state_index(0); //always update based on the newest completed state
        sPARTICLE_VAR vector_to_centroid[N_SPACE_DIMS];
        vsub(vector_to_centroid, parcel->x[new_state_index], surfel->centroid);
        sPARTICLE_VAR distance_to_centroid = std::sqrt(vdot(vector_to_centroid, vector_to_centroid));

        LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
          printf("COUNTED SEGMENT PARCELS ts %ld ring %d %s segment %d packing parcel %d at %g, %g, %g from surfel %d at (%g, %g, %g) with normal (%g, %g, %g)  with message index %d of %d. dist %g",
                 g_timescale.m_time,
                 m_ring_index,
                 this->direction_string(),
                 m_num_active_local_segments,
                 parcel->id,
                 parcel->x[new_state_index][0],
                 parcel->x[new_state_index][1],
                 parcel->x[new_state_index][2],
                 surfel->id(),
                 surfel->centroid[0],
                 surfel->centroid[1],
                 surfel->centroid[2],
                 surfel->normal[0],
                 surfel->normal[1],
                 surfel->normal[2],
                 parcel->m_home_voxel,
                 segment_length,
                 distance_to_centroid);
#endif
      }
    }
    m_num_active_local_segments++;
  }
}

template<>
VOID sMLRF_SURFEL_GROUP::post_parcel_sends()  //For both frames of each segment of all rings...
{
#ifdef DISABLE_PARTICLE_MLRF
  return;
#endif

  ccDOTIMES(nth_ring, this->n_rings()) {
    MLRF_PARCEL_RING current_ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    BOOLEAN debug = MLRF_PARCEL_SEND_DEBUG_CONDITION(nth_ring);
    current_ring->post_parcel_sends(debug);
  }
}

VOID sMLRF_PARCEL_RING::post_parcel_sends(BOOLEAN debug) {  //For both frames of each segment of one ring..
  ccDOTIMES(dir, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    BOOLEAN debug_this_direction = debug && DEBUG_DIRECTION_OF_INTEREST(dir);
    this->half_ring(dir)->post_parcel_sends(debug_this_direction);
  }
}

template <typename T>
VOID complete_message(cExaMsg<T>&message, const char* debug_str) {
  if(message.m_request == MPI_REQUEST_NULL)
    return;
  int flag = 0;
  MPI_Status status;
  MPI_Test(&message.m_request, &flag, &status);
  if(!flag) {
    msg_internal_error("Unexpected uncompleted MPI request for %s message in %s.", typeid(T).name(), debug_str);
  }
}

VOID sMLRF_PARCEL_HALF_RING::post_parcel_sends(BOOLEAN debug) { //For one frame of each segment of one ring...
  ccDOTIMES(nth_segment, m_num_active_local_segments) {
    if(debug) {
      LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
        printf("POST SEGMENT COUNT SEND ts %ld ring %d %s segment %d (%d parcels) to SP%d tag 0x%x.",
               g_timescale.m_time,
               m_ring_index,
               this->direction_string(),
               nth_segment,
               m_segment_send_counts[nth_segment],
               m_segment_destination_proc[nth_segment],
               parcel_count_send_mpi_tag());
    }
    //Post the parcel count message.
    cExaMsg<int> &count_send_message = m_count_send_msgs[nth_segment];
    complete_message(count_send_message, "parcel_count_send");
    count_send_message.setBuffer(m_segment_send_counts.data() + nth_segment);
    count_send_message.init(my_proc_id, m_segment_destination_proc[nth_segment]);
    count_send_message.set_nelems(1);
    count_send_message.settag(parcel_count_send_mpi_tag());
    g_exa_sp_cp_comm.isend(count_send_message);

    //Post the parcel states from the send buffer if there were any parcels.
    if(m_segment_send_counts[nth_segment] > 0 ) {
      if(debug) {
        LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
          printf("POST SEGMENT PARCEL SEND ts %ld ring %d segment %d (%d parcels) to SP%d tag 0x%x.",
                 g_timescale.m_time,
                 m_ring_index,
                 nth_segment,
                 m_segment_send_counts[nth_segment],
                 m_segment_destination_proc[nth_segment],
                 parcel_state_send_mpi_tag());
      }
      cExaMsg<sPARCEL_STATE> &state_send_message = m_state_send_msgs[nth_segment];
      std::stringstream debug_message;
      debug_message << "parcel_state_send " << this->direction_string() << " ring " << m_ring_index << " segment " << nth_segment;
      complete_message(state_send_message, debug_message.str().c_str());
      state_send_message.setBuffer(m_parcel_send_buffers[nth_segment].data());
      state_send_message.init(my_proc_id, m_segment_destination_proc[nth_segment]);
      state_send_message.set_nelems(m_segment_send_counts[nth_segment]);
      state_send_message.settag(parcel_state_send_mpi_tag());
      g_exa_sp_cp_comm.isend(state_send_message);
    }
  }
}

template<>
VOID sMLRF_SURFEL_GROUP::post_parcel_count_receives() { //For all rings...
#ifdef DISABLE_PARTICLE_MLRF
  return;
#endif
#ifdef PRINT_TO_LOG
  LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).printf("ts %ld", g_timescale.m_time);
#endif
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    dFLOAT angle_in_surfels = m_lrf_physics_desc->angle_rotated * ring->m_one_over_angle_per_surfel;
    BOOLEAN debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(nth_ring);
    ring->post_count_receives(angle_in_surfels, debug);
  }
}

#if BUILD_GPU
template<>
VOID sMLRF_MSFL_GROUP::post_parcel_count_receives() {
  msg_internal_error("post_parcel_count_receives not implemented for GPU");
}
#endif

VOID sMLRF_PARCEL_RING::post_count_receives(dFLOAT angle_in_surfels, BOOLEAN debug) { //For both frames of a ring...
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    asINT32 debug_this_direction = debug && !DEBUG_DIRECTION_OF_INTEREST(direction);
    this->half_ring(direction)->post_count_receives(angle_in_surfels, this, debug_this_direction);
  }
}


VOID sMLRF_PARCEL_HALF_RING::post_count_receives(dFLOAT angle_in_surfels, MLRF_RING ring,  BOOLEAN debug) { //For each segments of one frame of a ring...
  //Iterate over all the receiving segments, count the segments, and post recieve requests for the counts.
  asINT32 offset = rotation_offset_for_opposite_frame(angle_in_surfels);
  m_num_active_remote_segments = 0;
  PARCEL_MLRF_DO_REMOTE_SEGMENTS(segment_length, receive_buffer_offset, remote_proc, ring, offset) {
    if(debug) {
      LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
        printf("POST SEGMENT COUNT RECV ts %ld ring %d %s segment %d offset %d segment %d of length %d has receive_buffer_offset %d from SP%d tag 0x%x.",
               g_timescale.m_time,
               m_ring_index,
               this->direction_string(),
               m_num_active_remote_segments,
               offset,
               m_num_active_remote_segments,
               segment_length,
               receive_buffer_offset,
               remote_proc,
               parcel_count_receive_mpi_tag());
    }
    m_segment_source_proc[m_num_active_remote_segments] = remote_proc;
    cExaMsg<int> &count_receive_message = m_count_receive_msgs[m_num_active_remote_segments];
    count_receive_message.setBuffer(m_segment_receive_counts.data() + m_num_active_remote_segments);
    count_receive_message.init(remote_proc, my_proc_id);
    count_receive_message.set_nelems(1);
    count_receive_message.settag(parcel_count_receive_mpi_tag());
    g_exa_sp_cp_comm.irecv(count_receive_message);
    m_num_active_remote_segments++;
  }
}

template<>
VOID sMLRF_SURFEL_GROUP::post_parcel_state_receives()  //For all segments of both frames of all rings...
{
#ifdef DISABLE_PARTICLE_MLRF
  return;
#endif
  //LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).printf("POST SURFEL GROUP PARCEL RECV ts %ld", g_timescale.m_time);
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    dFLOAT angle_in_surfels = m_lrf_physics_desc->angle_rotated * ring->m_one_over_angle_per_surfel;
    BOOLEAN debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(nth_ring);
    ring->post_state_receives(angle_in_surfels, debug);
  }
}

VOID sMLRF_PARCEL_RING::post_state_receives(dFLOAT angle_in_surfels, BOOLEAN debug) { //For the segments of both frames of a ring...
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    asINT32 debug_this_direction = debug && !DEBUG_DIRECTION_OF_INTEREST(direction);
    this->half_ring(direction)->post_state_receives(angle_in_surfels, this, debug_this_direction);
  }
}

VOID sMLRF_PARCEL_HALF_RING::post_state_receives(dFLOAT angle_in_surfels, MLRF_RING ring, BOOLEAN debug) { //For the segments of one frame of a ring...
  ccDOTIMES(nth_segment, m_num_active_remote_segments) {
    asINT32 parcel_count = m_segment_receive_counts[nth_segment];
    if(!parcel_count) //Skip this segment if there are no parcels crossing it.
      continue;

    if(debug) {
      LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
        printf("POST SEGMENT PARCEL RECV ts %ld ring %d segment %d %s segment %d of length %d (%d parcels) from SP%d tag 0x%x.",
               g_timescale.m_time,
               m_ring_index,
               nth_segment,
               this->direction_string(),
               nth_segment,
               m_segment_receive_counts[nth_segment],
               parcel_count,
               m_segment_source_proc[m_num_active_remote_segments],
               parcel_state_receive_mpi_tag());
    }

    //Resize the receive buffer.
    std::vector<sPARCEL_STATE> &parcel_receive_buffer = m_state_receive_buffers[nth_segment];
    if(parcel_receive_buffer.capacity() < parcel_count) {
      //Using an stl::vector for a receive buffer is perhaps a bad choice since there is no way to size the vector after space is reserved
      //without calling a constructor for each element.   We should use a buffer that allows uninitialized data in it which the
      //standard library tries to prevent.
      parcel_receive_buffer.reserve(parcel_count);
      parcel_receive_buffer.resize(parcel_count);
    } else if(parcel_receive_buffer.capacity() > 2 * parcel_count) {
      //Is it worth downsizing the receive buffer if it's much larger than needed?
      parcel_receive_buffer.clear();
      std::vector<sPARCEL_STATE>(parcel_receive_buffer).swap(parcel_receive_buffer);
      parcel_receive_buffer.reserve(parcel_count);
      parcel_receive_buffer.resize(parcel_count);
    }
    //Post the receive request for the parcel states.
    asINT32 remote_proc = m_segment_source_proc[nth_segment];
    cExaMsg<sPARCEL_STATE> &parcel_receive_message = m_state_receive_msgs[nth_segment];
    parcel_receive_message.setBuffer(parcel_receive_buffer.data());
    parcel_receive_message.init(remote_proc, my_proc_id);
    parcel_receive_message.set_nelems(parcel_count);
    parcel_receive_message.settag(parcel_state_receive_mpi_tag());
    g_exa_sp_cp_comm.irecv(parcel_receive_message);
  }
}

template<>
BOOLEAN sMLRF_SURFEL_GROUP::complete_parcel_count_receives() {
#ifdef DISABLE_PARTICLE_MLRF
  return TRUE;
#endif
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    BOOLEAN debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(nth_ring);
    if(!ring->complete_count_receives(debug))
      return FALSE;
  }
  return TRUE;
}

BOOLEAN sMLRF_PARCEL_RING::complete_count_receives(BOOLEAN debug) {
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    BOOLEAN debug_this_direction = debug && !DEBUG_DIRECTION_OF_INTEREST(direction);
    if(!this->half_ring(direction)->complete_count_receives(debug_this_direction))
      return FALSE;
  }
  return TRUE;
}

BOOLEAN sMLRF_PARCEL_HALF_RING::complete_count_receives(BOOLEAN debug) {
  ccDOTIMES(nth_segment, m_num_active_remote_segments) {
    cExaMsg<int> &count_receive_message = m_count_receive_msgs[nth_segment];
    if(count_receive_message.m_request != MPI_REQUEST_NULL) {
      int flag = 0;
      MPI_Status status;
      MPI_Test(&count_receive_message.m_request, &flag, &status);
      if(!flag) {
        return FALSE;
      } else {
        if(debug) {
          LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
            printf("COMPLETE SEGMENT COUNT RECV ts %ld ring %d %s segment %d (%d parcels).",
                   g_timescale.m_time,
                   m_ring_index,
                   this->direction_string(),
                   nth_segment,
                   m_segment_receive_counts[nth_segment]);
        }
      }
    }
  }
  return TRUE;
}

template<>
BOOLEAN sMLRF_SURFEL_GROUP::complete_parcel_state_receives() {
#ifdef DISABLE_PARTICLE_MLRF
  return TRUE;
#endif
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    BOOLEAN debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(nth_ring);
    if(!ring->complete_state_receives(debug))
      return FALSE;
  }
  return TRUE;
}
BOOLEAN sMLRF_PARCEL_RING::complete_state_receives(BOOLEAN debug) {
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    BOOLEAN debug_this_direction = debug && !DEBUG_DIRECTION_OF_INTEREST(direction);
    if(! this->half_ring(direction)->complete_state_receives(debug_this_direction))
      return FALSE;
  }
  return TRUE;
}

BOOLEAN sMLRF_PARCEL_HALF_RING::complete_state_receives(BOOLEAN debug) {
  ccDOTIMES(nth_segment, m_num_active_remote_segments) {
    cExaMsg<sPARCEL_STATE> &state_receive_message = m_state_receive_msgs[nth_segment];
    if(state_receive_message.m_request != MPI_REQUEST_NULL) {
      int flag = 0;
      MPI_Status status;
      MPI_Test(&state_receive_message.m_request, &flag, &status);
      if(!flag) {
        return FALSE;
      } else {
        if(debug) {
          LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
            printf("COMPLETE SEGMENT PARCEL RECV ts %ld ring %d %s segment %d (%d parcels).",
                   g_timescale.m_time,
                   m_ring_index,
                   this->direction_string(),
                   nth_segment,
                   m_segment_receive_counts[nth_segment]);
        }
      }
    }
  }
  return TRUE;
}

template<>
VOID sMLRF_SURFEL_GROUP::emit_lrf_parcels() {
#ifdef DISABLE_PARTICLE_MLRF
  return;
#endif
  LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
  TAGGED_MLRF_SURFEL* first_surfel_in_ring = this->quantum(0);
  ccDOTIMES(nth_ring, this->n_rings()) {
    MLRF_PARCEL_RING ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    dFLOAT angle_rotated = m_lrf_physics_desc->angle_rotated;
    BOOLEAN debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(nth_ring);
    ring->emit_lrf_parcels(first_surfel_in_ring, angle_rotated, lrf, debug);
    first_surfel_in_ring += 2 * ring->m_n_surfels_per_frame_on_proc;
  }
}

VOID sMLRF_PARCEL_RING::emit_lrf_parcels(
                                         TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                         dFLOAT angle_rotated,
                                         LRF_PHYSICS_DESCRIPTOR lrf,
                                         BOOLEAN debug) {
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    this->half_ring(direction)->emit_lrf_parcels(first_surfel_in_ring, angle_rotated, this, lrf, debug && !DEBUG_DIRECTION_OF_INTEREST(direction));
  }
}

static VOID transform_parcel_state_to_new_reference_frame(
                                                          PARCEL_STATE parcel,
                                                          sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS direction,
                                                          LRF_PHYSICS_DESCRIPTOR lrf );

static BOOLEAN emit_lrf_surfel_parcel(
                                      SURFEL lrf_surfel,
                                      PARCEL_STATE parcel,
                                      sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS direction,
                                      LRF_PHYSICS_DESCRIPTOR lrf,
                                      asINT32 ring_index,
                                      asINT32 rotation_offset);


VOID sMLRF_PARCEL_HALF_RING::emit_lrf_parcels(
                                              TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                              dFLOAT angle_rotated,
                                              MLRF_RING ring,
                                              LRF_PHYSICS_DESCRIPTOR lrf,
                                              BOOLEAN debug) {
#if 0
  dFLOAT surfel_overlap_ratio[2];
  calculate_mlrf_rotating_surfel_position(angle_rotated,
                                          m_n_surfels_per_frame_in_ring,
                                          m_direction == sMLRF_PARCEL_HALF_RING::EXTERNAL_TO_INTERNAL,
                                          surfel_overlap_ratio);
#endif

  MLRF_RING_SEGMENT segments = ring->ring_segments();
  asINT32 total_num_surfels_in_ring  = ring->m_n_surfels_per_frame_in_ring;
  asINT32 n_ring_segments_on_proc = ring->n_ring_segments_on_proc();
  asINT32 highest_segment_number = ring->n_ring_segments() - 1;
  asINT32 *ring_segments_on_proc = ring->ring_segments_on_proc();



  asINT32 depot_segment_start[n_ring_segments_on_proc];
  asINT32 depot_segment_end[n_ring_segments_on_proc];
  ccDOTIMES(ith_segment_in_ring, n_ring_segments_on_proc){
    // each segment has one more depot than surfel, need to offset start and end
    asINT32 segment_number = ring_segments_on_proc[ith_segment_in_ring];
    depot_segment_start[ith_segment_in_ring] = ith_segment_in_ring == 0 ? 0: depot_segment_end[ith_segment_in_ring - 1] + 1; 
    depot_segment_end[ith_segment_in_ring] = segment_number < highest_segment_number ?
      depot_segment_start[ith_segment_in_ring] + segments[segment_number+1].offset - segments[segment_number].offset :
      depot_segment_start[ith_segment_in_ring] + total_num_surfels_in_ring - segments[segment_number].offset;
  }
  dFLOAT angle_in_surfels = angle_rotated * ring->m_one_over_angle_per_surfel;
  asINT32 offset = rotation_offset_for_opposite_frame(angle_in_surfels);
  TAGGED_MLRF_SURFEL* first_surfel_in_frame = this->first_surfel_in_frame(first_surfel_in_ring);
  asINT32 nth_segment = 0;
  PARCEL_MLRF_DO_REMOTE_SEGMENTS(segment_length, receive_buffer_offset, remote_proc, ring, offset) {
    std::vector<sPARCEL_STATE> &parcel_receive_buffer = m_state_receive_buffers[nth_segment];
    if(debug && m_segment_receive_counts[nth_segment]) {
      LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
        printf("EMIT SEGMENT PARCELS ts %ld ring %d %s segment %d (%d parcels)",
               g_timescale.m_time,
               m_ring_index,
               this->direction_string(),
               nth_segment,
               m_segment_receive_counts[nth_segment]);
    }

    timer_accum_counters(SP_PARTICLE_MLRF_EMISSION_TIMER, 0, m_segment_receive_counts[nth_segment]);
    ccDOTIMES(nth_parcel, m_segment_receive_counts[nth_segment]) {
      sPARCEL_STATE &parcel_buffer_element = parcel_receive_buffer[nth_parcel];
      //We need to reference the surfel correspoinding to the Nth depot of this message.
      //We know N as it was stored in the parcel state in the m_home_ublk field and set by the sender near line 117.
      asINT32 nth_depot_in_segment = parcel_buffer_element.m_home_voxel;  //The m_home_voxel field is used to indicate which surfel in the segment this parcel collided with.
      asINT32 nth_depot = nth_depot_in_segment + receive_buffer_offset;
    
      asINT32 depot_in_nth_segment = 0;
      ccDOTIMES(ith_segment_in_ring, n_ring_segments_on_proc){
        if(nth_depot >= depot_segment_start[ith_segment_in_ring] &&
           nth_depot <= depot_segment_end[ith_segment_in_ring]){
           depot_in_nth_segment = ith_segment_in_ring; 
           break;
        }
      }

      asINT32 num_candidate_surfel = 2; // each depot contribute to two surfel, unless depot is at start or end
      if (nth_depot == depot_segment_start[depot_in_nth_segment] || 
          nth_depot == depot_segment_end[depot_in_nth_segment]){
        num_candidate_surfel = 1;
      }
      
      asINT32 candidate_surfel_index[num_candidate_surfel];

      if(nth_depot == depot_segment_start[depot_in_nth_segment]){
        candidate_surfel_index[0] = nth_depot - depot_in_nth_segment;
      } else if(nth_depot == depot_segment_end[depot_in_nth_segment]){
        candidate_surfel_index[0] = nth_depot - depot_in_nth_segment -1;
      } else {
        candidate_surfel_index[0] = nth_depot - depot_in_nth_segment - 1;
        candidate_surfel_index[1] = nth_depot - depot_in_nth_segment;
      }
        
      BOOLEAN mlrf_surfel_is_weightless = true; 
      ccDOTIMES(ith_candidate_surfel, num_candidate_surfel){ 
        asINT32 surfel_index = candidate_surfel_index[ith_candidate_surfel];  
        mlrf_surfel_is_weightless = mlrf_surfel_is_weightless && (first_surfel_in_frame + surfel_index)->is_surfel_weightless();
      }
     
      if(mlrf_surfel_is_weightless) {
        if(sPARCEL_IS_INTERESTING(parcel_buffer_element)) {
          SURFEL mlrf_surfel = (first_surfel_in_frame + candidate_surfel_index[0])->mlrf_surfel();
          msg_print("parcel %d:%d with dynamics type %d:%s is being discarded from weightless mlrf surfel %d (is ghost %d, is weightless %d) at timestep %ld (ring %d segment %d).",
                    parcel_buffer_element.originating_sp,
                    parcel_buffer_element.id,
                    parcel_buffer_element.dynamics_type,
                    parcel_buffer_element.dynamics_name(),
                    mlrf_surfel->id(),
                    mlrf_surfel->is_ghost(),
                    mlrf_surfel->is_weightless_mlrf(),
                    g_timescale.m_time,
                    m_ring_index,
                    nth_segment);
        }
        continue; //Skip trying to emit into a solid and don't count this as lost mass.
      }
      PARCEL_STATE parcel = xnew sPARCEL_STATE(parcel_buffer_element); //Copy-construct a new parcel out of the receive buffer's element.

      BOOLEAN parcel_coord_is_transfered = false;
      BOOLEAN parcel_is_successfully_emitted = false;
      if(PARCEL_IS_INTERESTING(parcel)) {
        if(g_timescale.m_time == 66){
          printf("before emit");
        }
      }
      ccDOTIMES(ith_candidate_surfel, num_candidate_surfel){
        asINT32 surfel_index = candidate_surfel_index[ith_candidate_surfel];  
        if ((first_surfel_in_frame + surfel_index)->is_surfel_weightless()){
          continue;
        }
        SURFEL mlrf_surfel = (first_surfel_in_frame + surfel_index)->mlrf_surfel(); //Get the surfel in the new frame.
        parcel->lrf_index = mlrf_surfel->ref_frame_index();
        if(!parcel_coord_is_transfered){
          transform_parcel_state_to_new_reference_frame(parcel, m_direction, lrf); //transform coordinates and velocity vectors.
          parcel_coord_is_transfered = true;
        }
        //Try to emit the parcel.
        if(emit_lrf_surfel_parcel(mlrf_surfel, parcel, m_direction, lrf, m_ring_index, offset)){
          parcel_is_successfully_emitted = true;
          break;
        }
      }
      if(PARCEL_IS_INTERESTING(parcel)) {
        SURFEL mlrf_surfel = (first_surfel_in_frame + candidate_surfel_index[0])->mlrf_surfel();
        msg_print("parcel %d:%d with dynamics type %d:%s was recived through mlrf surfel %d (is ghost %d, is weightless %d) at timestep %ld (ring %d segment %d).",
                  parcel->originating_sp,
                  parcel->id,
                  parcel->dynamics_type,
                  parcel->dynamics_name(),
                  mlrf_surfel->id(),
                  mlrf_surfel->is_ghost(),
                  mlrf_surfel->is_weightless_mlrf(),
                  g_timescale.m_time,
                  m_ring_index,
                  nth_segment);
      }

      if (!parcel_is_successfully_emitted)  {
        //Print a relevant error message
        SURFEL mlrf_surfel = (first_surfel_in_frame + candidate_surfel_index[0])->mlrf_surfel(); //Get the surfel in the new frame.
        asINT32 new_state_index = parcel->relative_timestep_to_state_index(0); //always update based on the newest completed ode state
#ifdef PRINT_TO_LOG
        LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
          printf("No ublk found for mlrf parcel %d at %g %g %g, lrf surfel %d centroid was %g %g %g with normal %g %g %g ring %d.",
                 parcel->id,
                 parcel->x[new_state_index][0],
                 parcel->x[new_state_index][1],
                 parcel->x[new_state_index][2],
                 mlrf_surfel->id(),
                 mlrf_surfel->centroid[0],
                 mlrf_surfel->centroid[1],
                 mlrf_surfel->centroid[2],
                 mlrf_surfel->normal[0],
                 mlrf_surfel->normal[1],
                 mlrf_surfel->normal[2],
                 m_ring_index);
#endif
        std::ostringstream error_string;
        error_string << "sp " << my_proc_id;
        error_string << " parcel " << parcel->id;
        error_string << ":" << parcel->originating_sp;
        error_string << " mass " << parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;
        error_string << " ring " << m_ring_index;
        error_string << " to " << this->direction_string();
        error_string << " surfel " << mlrf_surfel->id();
        error_string << " is_gost " << mlrf_surfel->is_ghost();
        error_string << " is_fringe " << mlrf_surfel->is_fringe() || mlrf_surfel->is_fringe2();
        error_string << " rot_off " << offset;
        //error_string << " overlap " << surfel_overlap_ratio[0] << " " << surfel_overlap_ratio[1];
        error_string << " surf_index " << (int)parcel->m_home_voxel;
        error_string << " seg_length " << segment_length;
        error_string << " seg_offset " << receive_buffer_offset;
        error_string << " n_surfels " << m_n_surfels_per_frame_on_proc;
        sdFLOAT point[3];
        vcopy(point,    parcel->x[new_state_index]);
        simerr_report_error_code(SP_EER_PARCEL_CANT_BE_EMITTED_FROM_LRF_SURFEL,
                                 mlrf_surfel->scale(),
                                 point,
                                 error_string.str().c_str(),
                                 mlrf_surfel->centroid[0],
                                 mlrf_surfel->centroid[1],
                                 mlrf_surfel->centroid[2]);

        particle_sim.destroy_parcel(parcel, TRUE); //TRUE means count the mass as inexplicably lost.
      }
    }
    nth_segment++;
  }
}

static VOID transform_parcel_state_to_new_reference_frame(
                                                          PARCEL_STATE parcel,
                                                          sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS direction,
                                                          LRF_PHYSICS_DESCRIPTOR lrf ) {
  //Transform the parcels velocity vectors and position vectors into the new frame.
  //Both must be transformed for collision detection to work properly on the first timestep in the new frame.
  ccDOTIMES(state_index, NUM_PREVIOUS_STORED_TIMESTEPS) {
    sPARTICLE_VAR radius[N_SPACE_DIMS];
    sdFLOAT ref_frame_vel[3] = {0.0, 0.0, 0.0};
    sPARTICLE_VAR new_parcel_velocity[N_SPACE_DIMS];
    sPARTICLE_VAR new_position[N_SPACE_DIMS];
    vsub(radius, parcel->x[state_index], lrf->point);
    vcross(ref_frame_vel, radius, lrf->axis);
    vmul(ref_frame_vel, lrf->omega);
    switch(direction) {
    case sMLRF_PARCEL_HALF_RING::EXTERNAL_TO_INTERNAL:
      vsub(new_parcel_velocity, parcel->v[state_index], ref_frame_vel); //get the velocity relative to the new moving ref frame but in the outer csys
      rotate_vector(new_parcel_velocity, parcel->v[state_index], lrf->containing_to_local_rotation_matrix, TRUE); //transform the csys to the inner csys
      rotate_vector(radius, new_position, lrf->containing_to_local_rotation_matrix, TRUE);  //rotate the position relative to the rotation point to the outer csys
      vadd(parcel->x[state_index], new_position, lrf->point);
      break;
    case sMLRF_PARCEL_HALF_RING::INTERNAL_TO_EXTERNAL:
      vadd(new_parcel_velocity, parcel->v[state_index], ref_frame_vel);  //add the reference frame vel to the parcel vel to get the velocity in the outer ref frame but realtive to inner csys
      rotate_vector(new_parcel_velocity, parcel->v[state_index], lrf->containing_to_local_rotation_matrix, FALSE); //transform to the outer csys
      rotate_vector(radius, new_position, lrf->containing_to_local_rotation_matrix, FALSE);  //rotate the position relative to the rotation point to the inner csys
      vadd(parcel->x[state_index], new_position, lrf->point);
    default:
      break;
    }
  }
}

#define TOL_WITHIN_SURFEL_PERIMETER_PHY  0.02
static BOOLEAN emit_lrf_surfel_parcel(
                                      SURFEL lrf_surfel,
                                      PARCEL_STATE parcel,
                                      sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS direction,
                                      LRF_PHYSICS_DESCRIPTOR lrf,
                                      asINT32 ring_index,
                                      asINT32 rotation_offset ) {

  //This routine emits a parcel from the ring segment receive buffer
  //back into the fluid or film. It assumes the coordiantes and
  //velocities ahve already been transformed.
  //Returns true on success
  if (lrf_surfel->lb_data()->boundary_condition_type != BOUNDARY_CONDITION_LRF)
    return FALSE;

#ifdef PRINT_TO_LOG
  asINT32 state_index = parcel->relative_timestep_to_state_index(0); //always update based on the newest completed ode state
  sPARTICLE_VAR vector_to_centroid[N_SPACE_DIMS];
  vsub(vector_to_centroid, parcel->x[state_index], lrf_surfel->centroid);
  sPARTICLE_VAR distance_to_centroid = std::sqrt(vdot(vector_to_centroid, vector_to_centroid));
  LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).
    printf("EMIT SEGMENT PARCELS parcel %d at (%g, %g, %g) to surfel %d at (%g, %g, %g) with normal (%g, %g, %g) on %s side of ring. distance %g, angle rotated %g",
           parcel->id,
           parcel->x[state_index][0],
           parcel->x[state_index][1],
           parcel->x[state_index][2],
           lrf_surfel->id(),
           lrf_surfel->centroid[0],
           lrf_surfel->centroid[1],
           lrf_surfel->centroid[2],
           lrf_surfel->normal[0],
           lrf_surfel->normal[1],
           lrf_surfel->normal[2],
           sMLRF_PARCEL_HALF_RING::m_interface_direction_to_string[direction],
           distance_to_centroid,
           lrf->angle_rotated);
#endif

  //Numerically reentrain film that doesnt have a surface to advect
  //onto in the receiving reference frame (this can occur when the
  //ring is incomplete and weightless mlrf surfels are generated)
  if(lrf_surfel->p_data()->intersecting_surfels == nullptr &&
     parcel->dynamics_type == ON_SURFACE) {
#if 1
    return FALSE;
#else
    asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
    parcel->dynamics_type = JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT;
    parcel->particles_may_breakup = TRUE;
    parcel->y_breakup[new_state_index] = -0.266;    // Assumed the droplet was born "tear-shaped" or "snowcone-shaped"
    parcel->dydt[new_state_index] = 0.0;            // Assumed the parts of the droplet do not move relative each other at birth.
    
    parcel->first_measured_ts = -1;
    parcel->order_in_measured_ts = -1;
    parcel->parcel_is_measurable_for_trajectory_window = 0;
    parcel->visited_trajectory_windows = 0;
    parcel->aggl_applied = 0;

    //Don't apply drag immediatly to the parcels that are reentrained (same as for splash model).
    parcel->no_drag_steps_number = particle_no_drag_delay / TIME_TO_MKS_SCALE_FACTOR; //assume it takes .1ms for the spherical droplets to form from the mess of impact structures (wanderer 10/10/13)
    if (parcel->no_drag_steps_number == 0)
      parcel->no_drag_steps_number = 1;  //do at least one timestep in cases with very large timesteps.
    
    //Modify the diameter and number of particles in the parcel.
    if (g_particle_sim_info.reentrainment_diameter > 0.0) {
      sPARTICLE_VAR reentrained_diameter = g_particle_sim_info.reentrainment_diameter + (g_random_particle_properties->uniform()/2.0) * g_particle_sim_info.reentrainment_diameter_range;
      parcel->num_particles *= pow(parcel->d / reentrained_diameter,3);  //adjust the number of particles to conserve mass
      parcel->d = reentrained_diameter;
    }
#endif
  }


  //Either put the parcel in a ublk if it's a fluid parcel or on a surfel if it's a film parcel.
  if(parcel->dynamics_type != ON_SURFACE) {
    asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
    //Find the ublk this parcel belongs in
    int voxel;
    UBLK ublk = particle_sim.find_containing_neighbor_ublk(parcel->x[new_state_index], lrf_surfel, voxel);
    if(ublk == NULL){
      return FALSE;
    }

    if(ublk->p_data()->fluid_parcel_list[voxel] == NULL){
      return FALSE;
    }
    if(!ublk->fluid_like_voxel_mask.test(voxel)){
      return FALSE;
    }

    parcel->m_home_shob_id = ublk->id();
    parcel->m_home_voxel = voxel;
    if(ublk->is_ghost()){
      uINT32 home_sp = ublk->home_sp(); 
      add_parcel_to_send_group(parcel, home_sp);
    } else{
      ublk->p_data()->fluid_parcel_list[voxel]->add(parcel);
    }
    return TRUE;

  } else {     //For surface parcels, find the surfel this parcel belongs on.

    //Compute the point on the sliding mesh surfel this parcel came out of.
    asINT32 previous_state_index = parcel->relative_timestep_to_state_index(-1); //index for last timestep
    asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);

    sPARTICLE_VAR post_wall_norm_coord = particle_sim.compute_normal_distance_to_centroid(parcel->x[new_state_index], lrf_surfel);

    sPARTICLE_VAR v_norm = vdot(parcel->v[new_state_index], lrf_surfel->normal);
    sPARTICLE_VAR t_minus = -post_wall_norm_coord / v_norm;

    sPARTICLE_VAR crossing_point[N_SPACE_DIMS];
    parcel->extrapolate_trajectory(new_state_index, t_minus, crossing_point);

    sPARTICLE_VAR radius[N_SPACE_DIMS];
    vsub(radius, parcel->x[new_state_index], crossing_point);

    sPARTICLE_VAR radius_direction[N_SPACE_DIMS];
    vcopy(radius_direction, radius);
    vunitize(radius_direction);

    //For each film-carying surfel, rotate the segment of the particle
    //trajectory that extends outside the sliding mesh surfel into its
    //plane.  If that rotated position is within the surfel's
    //perimeter, then add the parcel to that surfel.
  
    assert(lrf_surfel->p_data()->intersecting_surfels);
    BOOLEAN found_a_surfel = FALSE;
    ccDOTIMES(nth_surfel, lrf_surfel->p_data()->intersecting_surfels->size()) {
      SURFEL surfel =  lrf_surfel->p_data()->intersecting_surfels->at(nth_surfel);
  
      sPARTICLE_VAR rotation_angle = acos(vdot(radius_direction, surfel->normal)) - M_PI/2.0;

      sPARTICLE_VAR axis_of_rotation[N_SPACE_DIMS];
      vcross(axis_of_rotation, radius_direction, surfel->normal);
      
      sPARTICLE_VAR rotated_radius[N_SPACE_DIMS];
      vscale(rotated_radius, cos(rotation_angle), radius);

      sPARTICLE_VAR axis_cross_radius[N_SPACE_DIMS];
      vcross(axis_cross_radius, axis_of_rotation, radius);
      vmac(rotated_radius, sin(rotation_angle), axis_cross_radius);

      sPARTICLE_VAR axis_dot_radius = vdot(axis_of_rotation, radius);
      vmac(rotated_radius, axis_dot_radius * (1- cos(rotation_angle)), axis_of_rotation);

      sPARTICLE_VAR new_position[N_SPACE_DIMS];
      vadd(new_position, rotated_radius, crossing_point);

      if(particle_sim.position_is_within_surfel_perimeter(surfel, new_position) + TOL_WITHIN_SURFEL_PERIMETER_PHY > 0.0) {
        found_a_surfel = TRUE;
     
        surfel->p_data()->surface_parcel_list->add(parcel);
        vcopy(parcel->x[new_state_index], new_position);
        //Also rotate the parcels velocity vector to lie in the plane of the new surfel
        sPARTICLE_VAR rotated_velocity[N_SPACE_DIMS];
        vscale(rotated_velocity, cos(rotation_angle), parcel->v[new_state_index]);
        
        sPARTICLE_VAR axis_cross_velocity[N_SPACE_DIMS];
        vcross(axis_cross_velocity, axis_of_rotation, parcel->v[new_state_index]);
        vmac(rotated_velocity, sin(rotation_angle), axis_cross_velocity);

        sPARTICLE_VAR axis_dot_velocity = vdot(axis_of_rotation, parcel->v[new_state_index]);
        vmac(rotated_velocity, axis_dot_velocity * (1- cos(rotation_angle)), axis_of_rotation);

        vcopy(parcel->v[new_state_index], rotated_velocity);
        
        break;

      }
    }
    return found_a_surfel;
  }
  return FALSE;
}




VOID sMLRF_PARCEL_RING::cancel_receives() {
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS) {
    asINT32 debug = MLRF_PARCEL_RECEIVE_DEBUG_CONDITION(m_ring_index) && !DEBUG_DIRECTION_OF_INTEREST(direction);
    this->half_ring(direction)->cancel_receives(debug);
  }
}

VOID sMLRF_PARCEL_HALF_RING::cancel_receives(BOOLEAN debug) {
  ccDOTIMES(nth_segment, m_num_active_remote_segments) {
    cExaMsg<int> &count_receive_message = m_count_receive_msgs[nth_segment];
    if(count_receive_message.m_request != MPI_REQUEST_NULL) {
      if(debug) {
        LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).printf("CANCEL COUNT RECEIVES ts %ld ring %d %s segment %d.",
                                                     g_timescale.m_time,
                                                     m_ring_index,
                                                     this->direction_string(),
                                                     nth_segment);
      }
      MPI_Cancel(&count_receive_message.m_request);
    }
    cExaMsg<sPARCEL_STATE> &state_receive_message = m_state_receive_msgs[nth_segment];
    if(state_receive_message.m_request != MPI_REQUEST_NULL) {
      if(debug) {
        LOG_MSG("PARCEL_MLRF_COMM", LOG_TIME).printf("CANCEL PARCEL RECEIVES t %ld ring %d %s segment %d.",
                                                     g_timescale.m_time,
                                                     m_ring_index,
                                                     this->direction_string(),
                                                     nth_segment);
      }
      MPI_Cancel(&state_receive_message.m_request);
    }
  }
}


template<>
VOID sMLRF_SURFEL_GROUP::initialize_for_parcels() {
  TAGGED_MLRF_SURFEL* first_surfel_in_ring = this->quantum(0); //first surfel of all rings in group
  ccDOTIMES(nth_ring, this->n_rings()) {
    MLRF_PARCEL_RING parcel_ring = static_cast<MLRF_PARCEL_RING>(this->ring(nth_ring));
    parcel_ring->initialize(nth_ring, first_surfel_in_ring);
    first_surfel_in_ring += 2 * parcel_ring->m_n_surfels_per_frame_on_proc;
  }
}

VOID sMLRF_PARCEL_RING::initialize(asINT32 ring_index, TAGGED_MLRF_SURFEL* first_surfel_in_ring) {
  m_ring_index  = ring_index; //stored for debug messages
  TAGGED_MLRF_SURFEL* first_surfel_in_frame = first_surfel_in_ring;
  ccDOTIMES(direction, (int)sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS)  {

    m_half_rings[direction].initialize(
                                       m_n_surfels_per_frame_in_ring,
                                       m_n_surfels_per_frame_on_proc,
                                       (sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS)direction,
                                       m_n_receive_requests,
                                       ring_index,
                                       first_surfel_in_frame);
    first_surfel_in_frame += m_n_surfels_per_frame_on_proc;
  }
}

VOID  sMLRF_PARCEL_HALF_RING::initialize(
                                         asINT32 num_surfels_per_frame_in_ring,
                                         asINT32 num_surfels_per_frame_on_proc,
                                         eINTERFACE_DIRECTIONS direction,
                                         asINT32 max_segments,
                                         asINT32 ring_index,
                                         TAGGED_MLRF_SURFEL* first_surfel_in_frame) {

  m_direction = direction;
  m_max_segments = max_segments;

  m_count_send_msgs.resize(max_segments);
  m_state_send_msgs.resize(max_segments);
  m_segment_send_counts.resize(max_segments);
  m_parcel_send_buffers.resize(max_segments);
  m_segment_destination_proc.resize(max_segments);

  m_num_active_remote_segments = 0;
  m_num_active_local_segments = 0;

#if 0
  cExaMsg<int> null_count_receive_msg;
  null_count_receive_msg.m_request = MPI_REQUEST_NULL;
  m_count_receive_msgs.resize(max_segments, null_count_receive_msg);
  cExaMsg<sPARCEL_STATE> null_state_receive_msg;
  null_state_receive_msg.m_request = MPI_REQUEST_NULL;
  m_state_receive_msgs.resize(max_segments, null_state_receive_msg);
#endif

  m_count_receive_msgs.resize(max_segments);
  m_state_receive_msgs.resize(max_segments);
  m_segment_receive_counts.resize(max_segments);
  m_state_receive_buffers.resize(max_segments);
  m_segment_source_proc.resize(max_segments);

  //for debugging, remove later:
  m_n_surfels_per_frame_in_ring = num_surfels_per_frame_in_ring;
  m_n_surfels_per_frame_on_proc = num_surfels_per_frame_on_proc;
  m_ring_index = ring_index;


#if 0
  //  if( m_ring_index == 3 && m_direction == sMLRF_PARCEL_HALF_RING::EXTERNAL_TO_INTERNAL) {
  ccDOTIMES(nth_surfel, m_n_surfels_per_frame_on_proc) {
    SURFEL surfel = (first_surfel_in_frame + nth_surfel)->mlrf_surfel();
    if(surfel->id() == 831 || surfel->id() == 1114 || surfel->id() == 837) {
      asINT32 n_vertices = surfel->p_data()->n_vertices;
      asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;
      asINT32 first_half_edge_index = surfel->p_data()->first_half_edge_index();
      ccDOTIMES(nth_vertex, n_vertices) {
        SIM_VERTEX vertex_1 = surfel_vertex_from_global_index(first_vertex_index + nth_vertex);
        SIM_VERTEX vertex_2 = surfel_vertex_from_global_index(first_vertex_index + ((nth_vertex + 1) % n_vertices)); //the last edge closes the loop
        std::ostringstream error_string;
        error_string << "mlrf ring " << m_ring_index;
        error_string << " surfel " << surfel->id();
        error_string << " vertex " << nth_vertex << " of " << n_vertices;
        error_string << " normal " << surfel->normal[0] << "," << surfel->normal[1] << "," << surfel->normal[2];
        sdFLOAT point[3];
        vcopy(point,vertex_1->coord);
        simerr_report_error_code(
                                 SP_EER_SURFEL_NEIGHBOR_MISSING,
                                 surfel->scale(),
                                 point,
                                 error_string.str().c_str(),
                                 surfel->centroid[0],
                                 surfel->centroid[1],
                                 surfel->centroid[2]);
      }
    }
  }
  //}
#endif


}
