/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Quantums
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_QUANTUM_H
#define _SIMENG_QUANTUM_H

#include "common_sp.h"
#include "ublk.h"
#include "surfel.h"
#include "bsurfel.h"
#include "sampling_surfel.h"

template <typename SHOB_TYPE>
struct tSHOB_QUANTUM
{
  SHOB_TYPE shob;
};

template <typename UBLK_TYPE>
struct tUBLK_QUANTUM
{
  UBLK_TYPE ublk;
};

template <typename SURFEL_TYPE>
struct tSURFEL_QUANTUM
{
  SURFEL_TYPE surfel;
  VOID check_parms_bounds(PHYSICS_DESCRIPTOR pd) {}	// does nothing for base surfels. Overridden for moving slip and noslip surfels
  	  	  	  	  	  	  							// and further overridden for fixed temperature wall surfels for checking velocity bounds.
  	  	  	  	  	  	  							// Can be further extended to check other parameters for other types of surfels.
  VOID seed_from_meas(PHYSICS_DESCRIPTOR pd,
                     SURFEL _surfel,
                     cDGF_SEED_FROM_MEAS_DATA *seed_from_meas_data = NULL) {}
};

typedef tSHOB_QUANTUM  <SHOB>   sSHOB_QUANTUM  , *SHOB_QUANTUM;
typedef tUBLK_QUANTUM  <UBLK>   sUBLK_QUANTUM  , *UBLK_QUANTUM;
typedef tSURFEL_QUANTUM<SURFEL> sSURFEL_DYN_QUANTUM,* SURFEL_DYN_QUANTUM;

#endif	/* #ifndef _SIMENG_QUANTUM_H */
