/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Functions to operate on the simulation lattice
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "lattice.h"

#include "sim.h"
#include "sp_timers.h"
#include "random.h"
#include "seed_sp.h"



/*--------------------------------------------------------------------------*
 * Voxel mask support 
 *--------------------------------------------------------------------------*/

STP_STATE_INDEX g_state_vel_to_index[NVELS][NVELS][NVELS];

VOID init_state_vel_map()
{
   // first init all entries to STP_INVALID_STATE_INDEX;
   memset(g_state_vel_to_index, STP_INVALID_STATE_INDEX, sizeof(g_state_vel_to_index));

   // now fill in the entries for the states that exist
   g_state_vel_to_index[MAX_STATE_COMPONENT_VEL]
                       [MAX_STATE_COMPONENT_VEL]
                       [MAX_STATE_COMPONENT_VEL] = V_0_0_0;
   ccDOTIMES(i, N_LATTICE_VECTORS) {
     g_state_vel_to_index[MAX_STATE_COMPONENT_VEL+state_vx(i)]
                         [MAX_STATE_COMPONENT_VEL+state_vy(i)]
                         [MAX_STATE_COMPONENT_VEL+state_vz(i)] = i;
   }

}

sNEIGHBOR_UBLK_VECTOR neighbor_ublk_vector_table[] = NEIGHBOR_UBLK_VECTOR_TABLE; 

STP_UBLK_NEIGHBOR_INDEX 
g_ublk_neighbor_vec_to_index[NVELS_UBLK_SCALE][NVELS_UBLK_SCALE][NVELS_UBLK_SCALE];

VOID init_ublk_neighbor_vector_index_map()
{
   // first init all entries to STP_INVALID_STATE_INDEX;
   memset(g_ublk_neighbor_vec_to_index, 
          STP_INVALID_UBLK_NEIGHBOR, 
          sizeof(g_ublk_neighbor_vec_to_index));

   // now fill in the entries for the states that exist
   const int n = MAX_STATE_COMPONENT_VEL_UBLK_SCALE;
   int n_neighbors = sizeof(neighbor_ublk_vector_table)/sizeof(sNEIGHBOR_UBLK_VECTOR); 
   ccDOTIMES(i, n_neighbors) {
     g_ublk_neighbor_vec_to_index[n + neighbor_ublk_vector_table[i].vec[0]]
                                 [n + neighbor_ublk_vector_table[i].vec[1]]
                                 [n + neighbor_ublk_vector_table[i].vec[2]] = i;
   }
}

/*--------------------------------------------------------------------------*
 * Lattice tables
 *--------------------------------------------------------------------------*/
sSTATE_TABLE_ENTRY state_table[N_STATES] = STATE_TABLE; 

// Use lattice_vector_pde_table which is actually state_table_d25 for d19 lattice in update_split_ublk_instance()
#if BUILD_D19_LATTICE
sSTATE_TABLE_ENTRY lattice_vector_pde_table[N_LATTICE_VECTORS_PDE] = LATTICE_VECTOR_PDE_TABLE; 
#endif
STP_STATE_INDEX mirror_state_order[N_STATES] = MIRROR_STATE_ORDER;


asINT8 _state_energy[N_STATES]; /* often used as array index */
SURFEL_STATE _float_state_vel[N_STATES][3];
SURFEL_STATE _float_state_energy[N_STATES];
SURFEL_STATE _latvec_pair_energy_one_weight[N_LATTICE_VECTOR_PAIRS];
SURFEL_STATE _latvec_pair_energy_one_float[N_LATTICE_VECTOR_PAIRS];
SURFEL_STATE _latvec_pair_first_layer_weight[N_LATTICE_VECTOR_PAIRS];
SURFEL_STATE _latvec_pair_first_layer_float[N_LATTICE_VECTOR_PAIRS];


/* SURFEL_STATE_WEIGHT contains 2 for dual states and 1 otherwise */
SURFEL_STATE _float_state_weight[N_STATES];


/* IS_STATE_DUAL indicates which states are dual states. Each entry is guaranteed 
 * to be 0 or 1 so that it may be used as a shift count.
*/
csINT8 is_state_dual[N_STATES];

/*--------------------------------------------------------------------------*
 * Lattice table initialization functions
 *--------------------------------------------------------------------------*/

static VOID verify_state_table_order()
{
  ccDOTIMES(i, N_STATES) {
    assert(state_table[i].state_index == i);
  } 
}

#if BUILD_D19_LATTICE
static VOID verify_lattice_vector_pde_table_order()
{
  ccDOTIMES(i, N_LATTICE_VECTORS_PDE) {
    assert(lattice_vector_pde_table[i].state_index == i);
  } 
}
#endif

static VOID init_misc_vel_and_energy_tables(VOID)
{
  ccDOTIMES(n, N_STATES) {
    asINT32 i = state_table[n].state_index;
    _float_state_vel[i][0] = state_table[n].vel[0];
    _float_state_vel[i][1] = state_table[n].vel[1];
    _float_state_vel[i][2] = state_table[n].vel[2];
    _state_energy[i] = state_table[n].energy;
    _float_state_energy[i] = state_table[n].energy;

    _float_state_weight[i] = state_table[n].weight;
 
    is_state_dual[i] = state_weight(i) == 2;
  }

  ccDOTIMES(lvp, N_LATTICE_VECTOR_PAIRS) {
    asINT32 i = latvec_pair_first_latvec(lvp);
    _latvec_pair_energy_one_weight[lvp] = _state_energy[i] == 1 ? state_weight(i) : 0;
    _latvec_pair_energy_one_float[lvp] = _state_energy[i] == 1 ? 1 : 0;
    _latvec_pair_first_layer_weight[lvp] = (_state_energy[i] < 4) ? state_weight(i) : 0;
    _latvec_pair_first_layer_float[lvp] = (_state_energy[i] < 4) ? 1 : 0;
  }
}

VOID init_lattice_tables()
{
  static BOOLEAN tables_initialized = FALSE;
  if (!tables_initialized) {
    tables_initialized = TRUE;

    verify_state_table_order();
#if BUILD_D19_LATTICE
    verify_lattice_vector_pde_table_order();
#endif
    init_misc_vel_and_energy_tables();

    init_state_vel_map();

    init_ublk_neighbor_vector_index_map();
  }
}

