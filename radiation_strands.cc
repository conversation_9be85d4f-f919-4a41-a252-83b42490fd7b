

#include "strands.h"
#include "radiation_comm.h"
#include "radiation_phy.h"
#include "radiation.h"
#include "lattice.h"
#include "strand_mgr.h"

#include PHYSICS_H

static uint64_t get_active_realm_mask(const sTIME_COUPLING_PHASE* time_phase)
{
#if !BUILD_GPU
  uint64_t active_realm_mask = 0;
  switch(time_phase->time_coupling) {
    case eTIME_COUPLING_SCHEME::SameRate:
    case eTIME_COUPLING_SCHEME::DifferentRate:
    case eTIME_COUPLING_SCHEME::DifferentRateConservative:
      active_realm_mask |= 1 << STP_FLOW_REALM;
      active_realm_mask |= 1 << STP_COND_REALM;
      break;
    case eTIME_COUPLING_SCHEME::FreezeOne:
      if (time_phase->frozen_solver == eCOUPLED_SOLVER::FlowSolver) {
        active_realm_mask |= 1 << STP_COND_REALM;
      } else if (time_phase->frozen_solver == eCOUPLED_SOLVER::ConductionSolver) {
        active_realm_mask |= 1 << STP_FLOW_REALM;
      } else {
        msg_internal_error("Unknown solver");
      }
      break;
    case eTIME_COUPLING_SCHEME::Stagger:
      msg_internal_error("Cannot handle staggered time phase in radiation");
      break;
    default:
      msg_internal_error("Unknown time coupling scheme");
  }
  return active_realm_mask;
#else
  return 0;
#endif
}

static void maybe_send_sp_rp_solve_info(uint64_t curr_active_realm_mask)
{
#if !BUILD_GPU
  uint64_t next_active_realm_mask = curr_active_realm_mask;

  // Logic: 
  // If both flow & cond are active, then SP 0 (the flow SP) will send the info.
  // If only flow is active, then SP 0 will send the info.
  // If only conduction is active, then the bottom most conduction SP will send the info

  bool send_message_to_rps = false;
  if (curr_active_realm_mask & (1 << STP_FLOW_REALM)) {
    if (my_proc_id == 0) {
      // If flow is active, have SP0 do the broadcast
      send_message_to_rps = true;
    }
  } else if (curr_active_realm_mask & (1 << STP_COND_REALM)) {
    if (my_csp_proc_id == 0) {
      send_message_to_rps = true;
    }
  }

  static sRP_SEND_SOLVE_INFO solve_info_group;

  solve_info_group.wait_for_copying_of_send_data();

  if (send_message_to_rps) {
    if (sim.time_coupling_info.next_phase_start(BASETIME_LAST) <= g_timescale.m_radiation_tm.next_recv()) {
      next_active_realm_mask = get_active_realm_mask(sim.time_coupling_info.next_phase());
    }

    LOG_MSG("RAD").format("SP {} sending start signal to RPs curr {} next {}", my_proc_id, curr_active_realm_mask, next_active_realm_mask);
    solve_info_group.values[sRP_SEND_SOLVE_INFO::CURR_ACTIVE_SOLVER_MASK] = curr_active_realm_mask;
    solve_info_group.values[sRP_SEND_SOLVE_INFO::NEXT_ACTIVE_SOLVER_MASK] = next_active_realm_mask;
    solve_info_group.values[sRP_SEND_SOLVE_INFO::FLOW_TIMESTEP] = g_timescale.m_lb_tm.m_time;
    solve_info_group.values[sRP_SEND_SOLVE_INFO::COND_TIMESTEP] = g_timescale.m_conduction_pde_tm.m_time;

    g_strand_mgr.m_send_queue->add_rp_entry(&solve_info_group);
  }
#endif
}

static void maybe_report_wait_to_cp(uint64_t curr_active_realm_mask, cRP_RECV_CHANNEL::Duration wait_time)
{
  if (wait_time > cRADIATION_WAIT_TIME::MIN_WAIT_TIME) {
    bool flow_active = curr_active_realm_mask & (1 << STP_FLOW_REALM);
    bool cond_active = curr_active_realm_mask & (1 << STP_COND_REALM);

    bool report_wait = false;

    if (flow_active) {
      if (my_proc_id == 0) {
        report_wait = true;
      }
    } else if (cond_active) {
      if (my_csp_proc_id == 0) {
        report_wait = true;
      }
    }

    if (report_wait) {
      g_radiation_wait_time.report_wait(g_timescale.m_lb_tm.m_time,
                                        g_timescale.m_conduction_pde_tm.m_time,
                                        std::chrono::duration<double>(wait_time).count());
    }
  }
}

void sRADIATION_STRAND::run()
{

#if !BUILD_GPU
  auto& timescale = get_timescale_ref();

  bool do_recvs = timescale.m_radiation_tm.do_recvs();
  bool do_sends = timescale.m_radiation_tm.do_sends();

  uint64_t curr_active_realm_mask = get_active_realm_mask(&sim.time_coupling_info.active_phase());

  if (do_recvs) {

    LOG_MSG("RAD","ts", timescale.m_base_time) << "doing recvs";

    cRP_RECV_CHANNEL::Duration wait_time = g_strand_mgr.m_rp_recv_channel.wait_for_receives(); 

    maybe_report_wait_to_cp(curr_active_realm_mask, wait_time);

  }

  if (do_sends) {
    LOG_MSG("RAD","ts", timescale.m_base_time) << "doing sends";
    maybe_send_sp_rp_solve_info(curr_active_realm_mask);
  }

  if (sim.is_lb_model) {
    timescale.m_lb_tm.update_scale_base_time(FINEST_SCALE, timescale.m_base_time);
  }

  if (sim.is_conduction_model) {
    timescale.m_conduction_pde_tm.update_scale_base_time(FINEST_SCALE, timescale.m_base_time);
  }

  if (do_recvs || do_sends) {
    // msg_print("do rad base_time %d recv %d send %d", timescale.m_base_time, do_recvs, do_sends);
    asINT32 prior_rad_index = timescale.m_radiation_tm.prior_timestep_index();
    asINT32 next_rad_index = prior_rad_index ^ 1;
    BASETIME prior_send_base_time = timescale.m_radiation_tm.prior_send_base_time();

    DO_SCALES_COARSE_TO_FINE(scale) {

      for ( sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
        BASETIME scale_prior_base_time = get_solver_tm(group->realm()).scale_prior_base_time(scale);
        BASETIME dt_scale = timescale.m_base_time - scale_prior_base_time;
        BASETIME total_send_interval = timescale.m_base_time - prior_send_base_time;
        BASETIME send_integration_interval = std::min(dt_scale, total_send_interval);

        LOG_MSG("RAD").format("SCALE {} prior_base_time {} dt_scale {} integration_interval {} total_send_interval {}", scale, scale_prior_base_time, dt_scale, send_integration_interval, total_send_interval);
        do_radiation_patch_group(group, prior_rad_index, do_recvs, do_sends, send_integration_interval, total_send_interval);

        if (do_sends) {
          g_strand_mgr.m_send_queue->add_rp_entry(group);
        }
      }
    }

    if (do_recvs) {
      g_strand_mgr.m_rp_recv_channel.set_post_receives();
      timescale.m_radiation_tm.finish_recvs(timescale.m_base_time);
    }

    if (do_sends) {
      timescale.m_radiation_tm.finish_sends(timescale.m_base_time);
    }
  }

  g_strand_mgr.complete_timestep();
#endif
}

// void sRADIATION_SEND_STRAND::run()
// {
// #if !BUILD_GPU
//
//   auto& timescale = get_timescale_ref();
//   if (timescale.m_radiation_tm.do_sends()) {
//     LOG_MSG("RAD","ts", timescale.m_base_time) << "doing sends";
//
//     asINT32 prior_rad_index = timescale.m_radiation_tm.prior_timestep_index();
//     BASETIME prior_send_base_time = timescale.m_radiation_tm.prior_send_base_time();
//     BASETIME total_send_interval = timescale.m_base_time - prior_send_base_time;
//
//     DO_SCALES_COARSE_TO_FINE(scale) {
//       for (sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
//         BASETIME scale_prior_base_time = get_solver_tm(group->solver()).scale_prior_base_time(scale);
//         BASETIME dt_scale = timescale.m_base_time - scale_prior_base_time;
//         BASETIME integration_interval = std::min(dt_scale, total_send_interval);
//
//
//         if (total_send_interval == 0) {
//           total_send_interval = 1;
//           integration_interval = 1;
//         }
//
//         LOG_MSG("RAD").format("SCALE {} prior_base_time {} dt_scale {} integration_interval {} total_send_interval {}", scale, scale_prior_base_time, dt_scale, integration_interval, total_send_interval);
//
//         accumulate_radiation_patch_blackbody_power(group,
//                                                    integration_interval,
//                                                    total_send_interval,
//                                                    prior_rad_index);
//
//         g_strand_mgr.m_send_queue->add_rp_entry(group);
//       }
//     }
//
//     timescale.m_radiation_tm.finish_sends(timescale.m_base_time);
//   }
//
//   if (sim.is_lb_model) {
//     timescale.m_lb_tm.update_scale_base_time(FINEST_SCALE, timescale.m_base_time);
//   }
//
//   if (sim.is_conduction_model) {
//     timescale.m_conduction_pde_tm.update_scale_base_time(FINEST_SCALE, timescale.m_base_time);
//   }
//
//
//   g_strand_mgr.complete_timestep();
//
// #endif
// }
