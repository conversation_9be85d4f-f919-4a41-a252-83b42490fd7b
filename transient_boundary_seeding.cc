/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Transient boundary seeding data
 *
 * Mohit Jain, Dassault Systemes Simulia Corp.
 *  * Created Friday Oct 25, 2019
 *--------------------------------------------------------------------------*/
#include "sim.h"
//Data for transient boundary seeding is double buffered. This allows us
//to hide the communication cost.In this function, the data is copied from
//the receive buffer to the seed data buffer. The measurement file
//contains the data as a 3D array of [frame][variable][measurement surfel].
//The seed data contains the data as a 3D array of [measurement surfel][variable][frame].
//This re-ordering of data while copying helps in optimal memory access while
//interpolation in time before surfel dynamics. Additional shuffling of data is
//performed because the last frame received during previous interpolation phase
//becomes the first frame for the next interpolation phase.

std::vector<std::string> sSEED_CONTROL::fluid_seed_var_names = {
  "static_pressure", 
  "x_velocity",
  "y_velocity",
  "z_velocity",
  "temp",
  "turb_kinetic_energy",
  "turb_dissipation",
  "density",
  "stress_tensor_mag",
  "water_vapor_mfrac",
  "comp0_density",
  "comp1_density",
  "contact_angle",
  "dynamic_scalar_multiplier",
  "ustar",
  "density_frozen"};

std::vector<std::string> sSEED_CONTROL::boundary_seed_var_names = {
  "pressure",
  "total_pressure",
  "x_velocity",
  "y_velocity",         
  "z_velocity",         
  "flow_dir_x",
  "flow_dir_y",
  "flow_dir_z",
  "temp",        
  "turb_kinetic_energy",
  "turb_dissipation",   
  "x_mass_flux",        
  "y_mass_flux",        
  "z_mass_flux",        
  "mass_flow"};

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::scale_seed_vars_via_mks_properties(asINT32 var, sFLOAT &var_value, sSEED_CONTROL &seed_control) {
  // We need to be able to seed cases from results which have different
  // characteristic and maximum expected velocities using MKS units. See PR 31186.
  dFLOAT pressure_seed;
  if (var == (DGF_BOUNDARY_SEED_VAR_PRESSURE) || (var == (DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE))) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * var_value;
    pressure_seed = seed_control.scale_seed_pressure(pressure_seed_orig);
    var_value = pressure_seed / g_density_scale_factor;
  }

  dFLOAT vel_scale = seed_control.smart_seed_mks_vel_scaling / sim.mks_vel_scaling;
  if (var == (DGF_BOUNDARY_SEED_VAR_XVEL) || (var == (DGF_BOUNDARY_SEED_VAR_YVEL)) || (var == (DGF_BOUNDARY_SEED_VAR_ZVEL)) )
    var_value *= vel_scale;

  if (var == (DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX) || (var == (DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX)) || (var == (DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX)))
    var_value *= vel_scale;

  dFLOAT char_vel_ratio_2 = vel_scale * vel_scale;
  if (var == (DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY)) {
    var_value *= char_vel_ratio_2;
  }
  if (var == (DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION)) {
    dFLOAT coord_scale = 1.0; // The domain should be scaled the same
    dFLOAT char_vel_ratio_3 = vel_scale * char_vel_ratio_2;
    var_value *= coord_scale * char_vel_ratio_3;
  }
}


VOID sSP_TRANSIENT_BOUNDARY_SEEDING::scale_seed_vars_via_dimless_properties(asINT32 var, sFLOAT &var_value, sSEED_CONTROL &seed_control) {
  // We have considered doing this by simply scaling through the
  // dimension less parameters (Cp and u/Vo), but scaling Cp and velocity
  // independently of one another violates continuity.  So following is
  // a simple procedure for satisfying both.
  // 
  // Primed "'" values are for the "new" simulation (seeded case),
  // unprimed values are from previous case.  _c denotes the
  // characteristic value.
  // 
  //  1) Scale pressure (density) through Cp:
  //  2) Use this new density when scaling velocity to maintain continuity,

  dFLOAT pressure_seed = 0.;
  if (var == (DGF_BOUNDARY_SEED_VAR_PRESSURE) || (var == (DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE))) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * var_value;
    pressure_seed = seed_control.scale_seed_pressure(pressure_seed_orig);
    var_value = pressure_seed / g_density_scale_factor;
  }

  dFLOAT vel_scale = seed_control.compute_seed_velocity_scaling(pressure_seed, sim.char_temp, sim.char_density, TRUE);
  if (var == (DGF_BOUNDARY_SEED_VAR_XVEL) || (var == (DGF_BOUNDARY_SEED_VAR_YVEL)) || (var == (DGF_BOUNDARY_SEED_VAR_ZVEL)) )
    var_value *= vel_scale;

  if (var == (DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX) || (var == (DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX)) || (var == (DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX)))
    var_value *= vel_scale;

  dFLOAT char_vel_ratio_2 = vel_scale * vel_scale;
  if (var == (DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY)) {
    var_value *= char_vel_ratio_2;
  }
  if (var == (DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION)) {
    dFLOAT coord_scale = 1.0; // The domain should be scaled the same
    dFLOAT char_vel_ratio_3 = vel_scale * char_vel_ratio_2;
    var_value *= coord_scale * char_vel_ratio_3;
  }
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::scale_seed_buffer_via_mks_properties(sSEED_CONTROL &seed_control) {
  if( m_params.n_total_vars_per_frame <= 0)
    return;
  while(!m_recvd_data) {
    sleep(MPI_SLEEP_SHORT);
  }
  ccDOTIMES(frame, m_params.n_frames_per_iter) {
    asINT32 var_index = 0;
    ccDOTIMES(var, DGF_N_BOUNDARY_SEED_VARS) {
      ccDOTIMES(i, m_params.n_surfels_per_var[var]) {
	scale_seed_vars_via_mks_properties(var, m_recv_buffer[var_index + i + m_params.n_total_vars_per_frame * frame], seed_control);
      }
      var_index += m_params.n_surfels_per_var[var];
    }
  }
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::scale_seed_buffer_via_dimless_properties(sSEED_CONTROL &seed_control) {
  if( m_params.n_total_vars_per_frame <= 0)
    return;
  while(!m_recvd_data) {
    sleep(MPI_SLEEP_SHORT);
  }
  ccDOTIMES(frame, m_params.n_frames_per_iter) {
    asINT32 var_index = 0;
    ccDOTIMES(var, DGF_N_BOUNDARY_SEED_VARS) {
      ccDOTIMES(i, m_params.n_surfels_per_var[var]) {
        scale_seed_vars_via_dimless_properties(var, m_recv_buffer[var_index + i + m_params.n_total_vars_per_frame * frame], seed_control);
      }
      var_index += m_params.n_surfels_per_var[var];
    }
  }
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::unpack()
{
  if( m_params.n_total_vars_per_frame <= 0)
    return;
  while(!m_recvd_data) {
    sleep(MPI_SLEEP_SHORT);
  }
  cassert(m_recvd_data == TRUE);
  m_recvd_data = FALSE;
  asINT32 seed_data_index = 0;
  asINT32 recv_buffer_index = 0;
  asINT32 index = 0;
  ccDOTIMES(i, m_params.n_vars_per_surfel.size()) {
    ccDOTIMES(var, m_params.n_vars_per_surfel[i]) {
        cassert(index < (m_params.n_total_vars_per_frame * (m_params.n_frames_per_iter + 1)));
      if(m_first_unpack == 0)
        m_seed_data[index] = m_recv_buffer[recv_buffer_index];
      else
        m_seed_data[index] = m_seed_data[index + (m_params.n_frames_per_iter)];
      index += m_params.n_frames_per_iter + 1;
      recv_buffer_index++;
    }
  }
  recv_buffer_index = 0;
  ccDOTIMES(i, m_params.n_vars_per_surfel.size()) {
    ccDOTIMES(var, m_params.n_vars_per_surfel[i]) {
      ccDOTIMES(frame, m_params.n_frames_per_iter + 1) {
        cassert(seed_data_index < (m_params.n_total_vars_per_frame * (m_params.n_frames_per_iter + 1)));
        if(frame != 0)
          m_seed_data[seed_data_index] = m_recv_buffer[recv_buffer_index + (frame -1) * m_params.n_total_vars_per_frame];
        seed_data_index++;
      }
      recv_buffer_index++;
    }
  }
  m_first_unpack++;
  m_ready_to_recv = TRUE;
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::allocate_buffers()
{
  m_recv_buffer = new sFLOAT[m_params.n_total_vars_per_frame * m_params.n_frames_per_iter];
  m_seed_data = new sFLOAT[m_params.n_total_vars_per_frame * (m_params.n_frames_per_iter + 1)];
  m_tbs_msg.set_nelems(m_params.n_total_vars_per_frame * m_params.n_frames_per_iter);
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::post_recv(asINT32 imeas)
{
  if( m_params.n_total_vars_per_frame <= 0)
    return;
  cassert(m_ready_to_recv == TRUE);
    int tag = make_mpi_tag<eMPI_MSG::TBS>(imeas);
    m_tbs_msg.settag(tag);
    m_tbs_msg.setBuffer(m_recv_buffer);
    g_exa_sp_cp_comm.irecv(m_tbs_msg);
  m_ready_to_recv = FALSE;
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::calculate_interpolation_index()
{
  asINT32 meas_data_time_span = m_params.n_frames_per_iter * m_params.interpolation_period;
  asINT32 first_comm_time_span = meas_data_time_span - m_params.interpolation_period + m_params.start_time;
  asINT32 timestep = 0;
  if(g_timescale.time_flow() >= first_comm_time_span) {
    timestep = (g_timescale.time_flow()  - first_comm_time_span) % meas_data_time_span;
  } else {
    timestep = (g_timescale.time_flow() > m_params.start_time) ? (g_timescale.time_flow() - m_params.start_time) % meas_data_time_span : 0;
  }
  m_interp_data_index = timestep / m_params.interpolation_period;
  if(g_timescale.time_flow() < first_comm_time_span && g_timescale.time_flow() > m_params.start_time)
    m_interp_data_index++;
  timestep %= m_params.interpolation_period;
  m_interp_weight = (sFLOAT)timestep / m_params.interpolation_period;
}

sFLOAT sSP_TRANSIENT_BOUNDARY_SEEDING::interpolate(asINT32 tbs_data_offset)
{
  asINT32 index = m_interp_data_index + tbs_data_offset;
  sFLOAT var_value = (1 - m_interp_weight) * m_seed_data[index]
                      + m_interp_weight * m_seed_data[index + 1];
  return var_value;
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::set_params(cDGF_SMART_SEED_CONTROL &seed_control)
{
  m_params.interpolation_period = seed_control.sim_ts_per_frame;
  m_params.start_time = seed_control.first_frame_ts;
  m_params.n_frames_per_iter = seed_control.num_frames;
  m_params.seed_via_mks = (seed_control.seed_scaling_type == DGF_MKS_SCALING) ? TRUE : FALSE;
  m_params.n_vars_per_surfel.reserve(1024);

  m_first_unpack = 0;
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::set_var_mask(cDGF_TBS &tbs)
{
  m_params.n_tbs_surfels++;
  m_params.var_mask = tbs.var_mask;
}

VOID sSP_TRANSIENT_BOUNDARY_SEEDING::update_tbs_var_count(cDGF_TBS &tbs) {
  asINT32 var_count = 0;
  if(tbs.transient_boundary_seeding) {
    ccDOTIMES(i, DGF_N_BOUNDARY_SEED_VARS) {
      if((tbs.var_mask & (1 << i)) && (i != DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX || i != DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX)) {
        var_count++;
        m_params.n_surfels_per_var[i]++;
      }
    }
    m_params.n_total_vars_per_frame += var_count;
    m_params.n_vars_per_surfel.push_back(var_count);
  }
}

asINT32 sSP_TRANSIENT_BOUNDARY_SEEDING::meas_data_period()
{
  return m_params.interpolation_period * m_params.n_frames_per_iter;
}

asINT32 sSP_TRANSIENT_BOUNDARY_SEEDING::meas_start_time()
{
  return m_params.interpolation_period * (m_params.n_frames_per_iter - 1) + m_params.start_time;
}

VOID add_transient_boundary_seed_data_comm_events() {
  ccDOTIMES(imeas, sim.n_seed_from_meas_descs) {
    asINT32 period = sim.transient_boundary_seeding[imeas].meas_data_period();
    asINT32 start_time = sim.transient_boundary_seeding[imeas].meas_start_time();
    if(period == 0)
      continue;
    if(start_time < g_timescale.time_flow()) {
      asINT32 num_period = (g_timescale.time_flow() - start_time) / period;
      start_time += (num_period + 1 ) *period;
    }
    BASETIME start_global_time = sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(start_time);
    g_async_event_queue.add_entry(new_event(EVENT_ID_UNPACK_TBS_DATA, imeas, start_global_time, period, g_timescale.m_end_time-1));
  }
}

// CP sends data twice for a full checkpoint resume because
// at the start of simulation first frame data is copied to
// zeroth and first index of seed data. But while resuming from
// a checkpoint the zeroth index should have data from last frame
// of previous interpolation phase. Hence comm is done twice
// to get the correct alignment of data for transient seeding.

VOID recv_initial_transient_boundary_seed_data() {
  asINT32 recv_count = 1;
  if(sim.is_full_checkpoint_restore)
    recv_count++;
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0) {
      sim.transient_boundary_seeding[i].m_first_unpack = 0;
      sim.transient_boundary_seeding[i].allocate_buffers();
    }
  }
  ccDOTIMES(ir, recv_count) {
    ccDOTIMES(i, sim.n_seed_from_meas_descs) {
      if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0) {
        sim.transient_boundary_seeding[i].post_recv(i);
        int flag = 0;
        MPI_Status status;
        while(!flag)
          MPI_Test(&sim.transient_boundary_seeding[i].m_tbs_msg.m_request, &flag, &status);
        sim.transient_boundary_seeding[i].m_recvd_data = TRUE;
        if(sim.transient_boundary_seeding[i].m_params.seed_via_mks)
          sim.transient_boundary_seeding[i].scale_seed_buffer_via_mks_properties(sim.m_seed_from_meas_controls[i]);
        else
          sim.transient_boundary_seeding[i].scale_seed_buffer_via_dimless_properties(sim.m_seed_from_meas_controls[i]);
        sim.transient_boundary_seeding[i].unpack();
      }
    }
  }
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0) {
      sim.transient_boundary_seeding[i].post_recv(i);
    }
  }
}
