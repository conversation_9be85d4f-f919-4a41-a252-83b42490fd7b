/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Measurement windows
 *
 * James Hoch, Exa Corporation 
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MEAS_H
#define _SIMENG_MEAS_H

#include <map>
#include <algorithm>

#include "common_sp.h"
#include "lattice.h"
#include "eqns.h"
#include "sim.h"
#include "timescale.h"
#include VMEM_VECTOR_H
#include THASH_H
#include "meas_cell.h"
#include <unordered_set>
#include <unordered_map>
#include "particle_solver_data.h"
#include "gpu_ptr.h"

class sMEAS_WINDOW;
struct sMOVING_MEAS_CELL_MGR;
struct sMOVING_MEAS_CELL_SMART_PTR;

#if BUILD_GPU
struct sDEVICE_REDUCE_METADATA;
struct sON_DEVICE_WINDOW;
class cDEVICE_MEAS_WINDOW_BUILDER;
#endif

__HOST__DEVICE__
inline bool lrf_is_rotating(LRF_PHYSICS_DESCRIPTOR lrf) {
  return lrf != nullptr && lrf->is_mlrf_on;
}

enum class write_or_count_t {WRITE, COUNT};

// A MEAS_WINDOW_PTR is a pointer to a meas window with the low bit used to encode
// whether this is a composite or per-voxel meas window. Remember that a meas window is only
// treated as a per-voxel window for a voxel dyn group if the voxels are no smaller than the
// meas window cell size.
class MEAS_WINDOW_PTR {
  private:
  sMEAS_WINDOW   *m_window;

  public:
  MEAS_WINDOW_PTR()                      { m_window = nullptr; }
  MEAS_WINDOW_PTR(sMEAS_WINDOW *window)  { m_window = window; }

  VOID set_window(sMEAS_WINDOW *window)  { m_window = window; }
  sMEAS_WINDOW *window()                 { return (sMEAS_WINDOW *)scalar_mask_pointer(m_window, ~1); }

  VOID mark_composite_or_per_voxel_or_development()     { m_window = (sMEAS_WINDOW *)scalar_or_pointer(m_window, 1); }
  VOID unmark_composite_or_per_voxel_or_development()   { m_window = (sMEAS_WINDOW *)scalar_mask_pointer(m_window, ~1); }
  BOOLEAN is_composite_or_per_voxel_or_development()    { return scalar_mask_pointer_to_int(m_window, 1); }
};


const SRI_VARIABLE_TYPE  SRI_VARIABLE_STD_DEV_HELPER =     SRI_VARIABLE_TYPE( SRI_VARIABLE_FIRST_NON_PREDEFINED + 1 );

// For defrost time support
const SRI_VARIABLE_TYPE  SRI_VARIABLE_DEFROST_TIME_HELPER = SRI_VARIABLE_TYPE( SRI_VARIABLE_FIRST_NON_PREDEFINED + 2 );

/*--------------------------------------------------------------------------*
 * Measurement windows
 *--------------------------------------------------------------------------*/

// Moving boundary condition info
typedef struct sMEAS_WINDOW_ROTATING_BC_INFO {
  dFLOAT            angular_vel;        // Accumulated over averaging interval
  sPHYSICS_VARIABLE *angular_vel_var;
#if DEBUG
  sINT32            surface_physics_desc_index;
  BOOLEAN           is_slip;            // as opposed to no-slip
#endif
  sMEAS_WINDOW_ROTATING_BC_INFO() {
    angular_vel = 0;
  }

  cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>& get_examsg(asINT32 index);

  // Get the real message which is being sent
  sMBC_MEAS_FRAME_SP_TO_CP_MSG *get_msg(asINT32 index) {
    return get_examsg(index).buffer();
  }

} *MEAS_WINDOW_ROTATING_BC_INFO;

typedef struct sMEAS_WINDOW_SLIDING_BC_INFO {
  dFLOAT            linear_vel[3];      // Accumulated over averaging interval */
  sPHYSICS_VARIABLE *linear_vel_vars;
#if DEBUG
  sINT32            surface_physics_desc_index;
  BOOLEAN           is_slip;            // as opposed to no-slip
#endif
  sMEAS_WINDOW_SLIDING_BC_INFO() {
    linear_vel[0] = 0;
    linear_vel[1] = 0;
    linear_vel[2] = 0;
  }

  cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>& get_examsg(asINT32 index);

  // Get the real message which is being sent
  sMBC_MEAS_FRAME_SP_TO_CP_MSG *get_msg(asINT32 index) {
    return get_examsg(index).buffer();
  }
} *MEAS_WINDOW_SLIDING_BC_INFO;

typedef struct sMEAS_WINDOW_LRF_INFO {
  dFLOAT angle_at_start;        /* Angle of rotation at beginning of averaging interval */
  dFLOAT angular_vel;           /* Accumulated over averaging interval */
  sINT32 n_revolutions_at_start;/* Number of revolutions at beginning of the averaging interval */
  sMEAS_WINDOW_LRF_INFO() {
	  angle_at_start = 0;
    angular_vel = 0;
    n_revolutions_at_start = 0;
  }

  cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1>& get_examsg(asINT32 index);

  // Get the real message which is being sent
  sLRF_MEAS_FRAME_SP_TO_CP_MSG *get_msg(asINT32 index) {
    return get_examsg(index).buffer();
  }
} *MEAS_WINDOW_LRF_INFO;

typedef struct sMEAS_WINDOW_MOVB_INFO {
  // TODO: Currently, the following is specialized for rotating tires. Should
  // be restricted in the future to just the xform for general rigid body motion
  dFLOAT angle_at_start;        // Angle of rotation at beginning of averaging interval 
  dFLOAT angle_rotated;         // Change in angle accumulated over averaging interval */
  sMEAS_WINDOW_MOVB_INFO() {
	  angle_at_start = 0;
	  angle_rotated = 0;
  }

  cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1>& get_examsg(asINT32 index);

  // Get the real message which is being sent
  sMOVB_MEAS_FRAME_SP_TO_CP_MSG *get_msg(asINT32 index) {
    return get_examsg(index).buffer();
  }

} *MEAS_WINDOW_MOVB_INFO;

typedef struct sMEAS_WINDOW_GRF_INFO {
  /* We accumulate the global ref frame angular velocity in the meas window
   * so that we can compute an average value over the averaging interval.
   */
  dFLOAT angular_vel[3]; /* Accumulated over averaging interval */

  /* If the global ref frame axis of rotation is fixed over the averaging 
   * interval, the meas window reports the average rotation around the axis over 
   * the averaging interval. Otherwise it reports the effective axis and rotation
   * (taken from the associated quaternion) at the midpoint of the averaging 
   * interval.
   */
  sINT8 axis_change_mark_at_start;
  cBOOLEAN axis_changed;
  dFLOAT angle_at_start;        /* Angle of rotation at beginning of averaging interval */
  dFLOAT quaternion_at_midpt[4];

  /* If the global ref frame ref point velocity direction is fixed over the averaging 
   * interval, the meas window reports the average position of the ref point over 
   * the averaging interval. Otherwise it reports the ref point position at the midpoint 
   * of the averaging interval.
   */
  sINT8 ref_pt_vel_dir_change_mark_at_start;
  cBOOLEAN ref_pt_vel_dir_changed;
  dFLOAT ref_pt_vel[3];                /* Accumulated over averaging interval */
  dFLOAT ref_pt_vel_at_midpt[3];
  /* translation is relative to global frame and ground csys */
  dFLOAT translation[3];            /* Accumulated over averaging interval */
  dFLOAT translation_at_midpt[3];

  cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1>& get_examsg();

  // Get the real message which is being sent
  sGRF_MEAS_FRAME_SP_TO_CP_MSG *get_msg() {
    return get_examsg().buffer();
  }
} *MEAS_WINDOW_GRF_INFO;


// Meas cells on the SP must be sorted by global index order to support full checkpoints, where the
// SPs must write meas window cells in global index order to allow the simulation to be resumed on a
// different number of SPs. The treatment of dev windows also relies on meas cells on the SP being
// sorted by global index order. This ensures that the cells corresponding to a face are consecutive,
// which allows a moving surfel/voxel to easily calculate the cell it contributes to at a
// particular timestep.
//
// As a meas window is constructed, we build a vector of the global meas cell indices corresponding
// to each meas cell instantiated on the SP. Each global meas cell index is paired with its SP meas
// cell index (which simply counts consecutively from 0). After all meas cells have been instantiated,
// we sort this vector by global meas cell index and then replace the global meas cell indices with
// new SP cell indices indicating how the original SP cell indices have been remapped. Voxel and surfel
// dyn groups then convert their original SP meas cell indices into new meas cell indices and
// ultimately meas cell pointers.
struct sMEAS_CELL_INDEX_MAP_ENTRY {
  union {
    STP_MEAS_CELL_INDEX m_global_cell_index;
    STP_MEAS_CELL_INDEX m_new_sp_cell_index;
  };
  STP_MEAS_CELL_INDEX m_sp_cell_index;
};

inline bool meas_cell_index_cmp_fcn(const sMEAS_CELL_INDEX_MAP_ENTRY &entry1, const sMEAS_CELL_INDEX_MAP_ENTRY &entry2)
{ return entry1.m_global_cell_index < entry2.m_global_cell_index; }


// Store the quad mesh for the curved HX inlet meas window. 
typedef struct sQUAD_MESH {
  uINT32 num_facets;                      // M * N 
  uINT32 facet_offset;                    // to substract off each facet_id in the LGI file
  uINT32 num_vertices;                    // (M+1) * (N+1)
  sFLOAT *vertex_scales;                  // (num_vertices) scale factor for each vertex, 
                                          // inversely proportional to the number of times 
                                          // each vertex is visited

  STP_SGEOM_VARIABLE *vertex_coords;      // (M+1) * (N+1) * 3. Format x0 y0 z0 x1 y1 z1 ...
  DGF_VERTEX_INDEX   *vertex_indices;     // num_facets * 4.
} *QUAD_MESH;

class sUBLK_MEAS_CELL_PTR;
class sMBLK_MEAS_CELL_PTR;
class sMSFL_MEAS_CELL_PTR;

typedef class sMEAS_WINDOW {
  public:
  cBOOLEAN m_is_master_sp;
  cBOOLEAN is_ref_frame_master_sp; // Does this SP send ref frame info to the CP
  sINT8    meas_cell_scale;
  BOOLEAN8 per_voxel_meas_p;
  BOOLEAN8 is_composite;
  BOOLEAN8 is_development;
  BOOLEAN8 contains_std_dev_vars;
  BOOLEAN8 is_probe;
  BOOLEAN8 is_output_in_local_csys;
  BOOLEAN8 is_meas_vars_output_dp;
  BOOLEAN8 is_average_mme;
  BOOLEAN8 calc_htc_for_adb_walls;
  BOOLEAN8 calc_lambda2;          // user specifies SRI_VARIABLE_LAMBDA2
  STP_MEAS_WINDOW_INDEX index; // window's global index
  std::vector<bool> meas_cell_output_in_local_csys;
  private:
  MEAS_CELL_VAR *_meas_cells;
  TIMESTEP      m_n_timesteps_since_clear_cache_timestep;
  MEAS_CELL_VAR m_one_over_n_timesteps_since_clear;
  public:
  MEAS_CELL_VAR one_over_n_timesteps_since_clear() {
    TIMESTEP realm_time = g_timescale.solver_time(sim.is_conduction_sp);
    if (m_n_timesteps_since_clear_cache_timestep != realm_time) {
      m_n_timesteps_since_clear_cache_timestep = realm_time;
      m_one_over_n_timesteps_since_clear = 1.0 / (realm_time - current_update_time.clear_time + 1);
    }
    return m_one_over_n_timesteps_since_clear;
  }

  // sMEAS_WINDOW *m_next_in_output_queue; // next window in MEAS_WINDOW_COLLECTION output queue
  // sMEAS_WINDOW *m_next_in_clear_queue;  // next window in MEAS_WINDOW_COLLECTION clear queue
  sINT16 n_variables;
  sINT16 n_cp_variables; /* there may be variables not transferred to CP */
  ACTIVE_SOLVER_MASK solver_mask;
  sINT32 acous_switch_scale;  /* only used in Lighthill switch */
  SRI_VARIABLE_TYPE *var_types;
  BOOLEAN8 m_is_particle_trajectory_window; // Flag this window if it is for trajectory measurement.
  
  // An array of flags indicating if the var_type is of an average
  // over a particle population and should therefor be normalized by
  // the population size.
  BOOLEAN8 *m_normalize_var_by_particle_count;  

  //The following var component IDs distinguish between vars types
  //used for measurements that have a variable number of components
  //(like per-emitter or per-material particle measurements where the
  //number of components varies case by case).
  sINT16* m_var_component_ids; //one component index per element of var_types
  BOOLEAN8 m_per_emitter_particle_measurements;
  BOOLEAN8 m_per_material_particle_measurements;

#if BUILD_5G_LATTICE
  // Performing these meas vvariable ID transformations during initializations
  // and avoid them durint the simulation
  SRI_VARIABLE_TYPE *var_5g_types;
  sINT8 *comp_5g_index;
  sINT8 *phase_5g_index;
#endif

  //LB_UDS
  SRI_VARIABLE_TYPE *uds_var_base_type;
  sINT8 *uds_scalar_index;
  
  LGI_MEAS_WINDOW_TYPE meas_window_type;
  /* Bounds of user-defined filter for composite force meas window */
  sFLOAT min_pressure;
  sFLOAT max_pressure;

  /* Default reference point for composite moment meas window */
  sFLOAT reference_point[3];
  
  STP_MEAS_CELL_INDEX n_global_stationary_meas_cells; // across all SPs
  STP_MEAS_CELL_INDEX n_global_moving_meas_cells;     // across all SPs

  TIMESTEP midpt;
  TIMESTEP avg_interval;

  sMEAS_WINDOW_LRF_INFO *lrfs;
  sMEAS_WINDOW_MOVB_INFO *movbs;

  sMEAS_WINDOW_ROTATING_BC_INFO *rotating_bcs;
  sMEAS_WINDOW_SLIDING_BC_INFO  *sliding_bcs;

  sMEAS_WINDOW_GRF_INFO grf;

  sTIME_DESC m_time_desc;  // start, end, average, and period after rounding
  
  // The next time the window should be cleared and the next time the window 
  // should be sent to the CP.
  sMEAS_UPDATE_TIME current_update_time; 
  sMEAS_UPDATE_TIME next_update_time; 
  asINT32 nframes_sent;

  // local meas cell count to this SP
  STP_MEAS_CELL_INDEX n_stationary_meas_cells;
  STP_MEAS_CELL_INDEX n_send_moving_meas_cells;
  uINT64              n_moving_meas_cell_data;
  STP_MEAS_CELL_INDEX n_moving_meas_cells();

  sQUAD_MESH *quad_mesh; // Used for curved heat exchangers

  // development window data structs
  sdFLOAT  start_pt[3][3];  // first index is axis, second is coord
  sdFLOAT  dev_line_orientation[3][3];  //first index is axis, second is coord of a unit vector
  sdFLOAT  one_over_dev_win_segment;
  STP_MEAS_CELL_INDEX *entity_first_meas_cell[3]; // meas cell index of the first active meas cell for each face/part and each axis
  asINT32  *entity_n_segments[3];   // number of segments for each face/part and each axis
  asINT32  *entity_first_segment[3];   // starting segment index (relative to axis) for each face/part and each axis  

  /* Reduction tree for stationary meas window data */
  BOOLEAN use_tree_reduction() const { return is_composite || is_development; }
  BOOLEAN needs_moving_meas_cell_areas() const { return !is_development; }

  STP_PROC m_parent_rank;
  sINT8    m_n_child_sps;  // 0, 1, or 2
  STP_PROC m_n_child_sps_ready;
  STP_PROC m_child_sps[2]; // The SP number is also the MPI rank

  sMEAS_WINDOW *m_next_in_send_queue;  // next window in MEAS_WINDOW_COLLECTION send queue

  VOID reduce_meas_cells();

  /* Send queue and completion queue */

  VOID append_to_send_queue();
  VOID send();

  // m_send_buffer_ready is FALSE when the send has been placed on the send queue and has not been
  // completed. It is cleared only by the sim thread and set only by the comm thread. To avoid
  // a race, it should be cleared by the sim thread BEFORE the send is placed on the queue. Since
  // the comm thread does not read it, but only sets it, there is no race in the other direction.
  void wait_for_previous_send_to_complete();
  void set_send_buffers_ready();
  bool m_all_send_buffers_ready;         

  pthread_mutex_t m_previous_send_mutex;
  pthread_cond_t m_previous_send_cv;

  VOID append_to_pending_queue();
  VOID insert_in_completion_queue();

  sMEAS_WINDOW *m_next_in_pending_queue;
  sMEAS_WINDOW *m_next_in_completion_queue;

  STP_MEAS_CELL_INDEX *m_child_global_meas_cell_indices[2]; // binary tree
  STP_MEAS_CELL_INDEX *m_my_global_meas_cell_indices;
  STP_MEAS_CELL_INDEX m_n_child_meas_cells[2];
  STP_MEAS_CELL_INDEX m_n_reduced_meas_cells; // union across 2 children and myself

  CP_MEAS_CELL_VAR *m_send_buffer; // length is (n_cp_variables * m_n_reduced_meas_cells)
  MPI_Request m_send_request;

  CP_MEAS_CELL_VAR *m_moving_send_buffer; // length is ((n_cp_variables + 1) * n_moving_meas_cells)
  uINT64      m_current_moving_buffer_size;
  MPI_Request m_moving_send_count_request;
  MPI_Request m_moving_send_request;

  // Data to be sent from master SP of the window to CP
  // WARNING: Insertion of this struct caused malfunction in the pass.
  // Some of the components didn't compile this additinal member and as
  // consequence there was and offset when access member behind this.
  struct
  {
    struct
    {
      TIMESTEP num_clear_steps;
      asINT32 nframes_sent;
      sINT16 n_sp_variables;
      sINT16 header_len;
    } essential_data_send;

    struct
    {
      size_t map_size;
      size_t map_length;
      size_t last_map_idx;
    } essential_data_recv;

    struct
    {
      size_t num_chunks_mov_cells_per_sp;
    } essential_data_inter_send;

    std::vector<decltype(essential_data_inter_send)> essential_data_inter_recv;
  
    MPI_Request request_send  = MPI_REQUEST_NULL;
    MPI_Request request_recv  = MPI_REQUEST_NULL;
    MPI_Request request_inter = MPI_REQUEST_NULL;
  } cp_comm;

  void open_comm_with_cp(const TIMESTEP current_timestep);
  void open_inter_comm(const TIMESTEP current_timestep);
  void close_inter_comm(const TIMESTEP current_timestep);
  template <bool IS_BLOCKING = true> void open_comm_recv_map(void * const);
  bool avoid_ckpt() const;
  bool has_meas_cells() const;

#if BUILD_GPU
  uINT32 m_n_device_meas_cells;
  GPU::Ptr<MEAS_CELL_VAR> m_device_meas_cells;
  GPU::Ptr<MEAS_CELL_VAR> m_device_reduction_buffer;
  GPU::Ptr<STP_MEAS_CELL_INDEX> m_device_reduction_idxs;
  GPU::Ptr<sDEVICE_REDUCE_METADATA> m_device_reduction_metadata;

  MEAS_CELL_VAR* m_reduction_buffer;
  sSTD_CELL_VAR* m_megashob_std_cells;
  STP_MEAS_CELL_INDEX* m_reduction_idxs;
  sDEVICE_REDUCE_METADATA* m_reduction_metadata;

  bool check_reduction_metadata();
  void add_ublk_meas_cell_ptr_to_device_meas_cell(sINT32 child_ublk_idx, sUBLK * child_ublk, sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr);

  std::unique_ptr<sON_DEVICE_WINDOW> allocate_on_device_window();
  void resolve_device_meas_cell_ptr(SCALE ublk_scale, sMBLK_MEAS_CELL_PTR& mblk_meas_cell_ptr, bool lrf_is_rotating);
  void resolve_device_meas_cell_ptr(sMSFL_MEAS_CELL_PTR& msfl_meas_cell_ptr, bool lrf_is_rotating);

  GPU::Ptr<MEAS_CELL_VAR> device_meas_cell_ptr(std::uintptr_t index);

  size_t device_meas_cell_array_size() const 
  { 
    size_t n_vars = contains_std_dev_vars ? n_variables + 1 : n_variables;
    return m_n_device_meas_cells * n_vars;
  }

  size_t device_meas_cell_array_size_no_std_dev_cells() const 
  { 
    return m_n_device_meas_cells * n_variables;
  }

  size_t device_reduction_metadata_array_size() const { return m_n_device_meas_cells; }
  size_t device_reduction_idxs_array_size() const { return m_n_device_meas_cells; }

  void set_device_meas_cell_ptr(sMBLK_MEAS_CELL_PTR& mblk_meas_cell_ptr);
  void clear_stationary_meas_cells_on_gpu();
  void reduce_and_transpose_stationary_meas_cells_on_gpu();
  void copy_stationary_meas_cells_from_gpu();

  /** @brief Returns the correct device window builder based on the type of meas window
    @ingroup GpuMeas
  */
  std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER> create_device_window_builder();
#endif

  // Convert for CP should always place its results at the back of the send buffer. This
  // allows the reduction process across 2 child SPs and this SP to deposit results in
  // send buffer.
  CP_MEAS_CELL_VAR *convert_for_cp_buffer() 
  {
    char *cell_var = ((char *) m_send_buffer +
                      cp_meas_cell_size() * (m_n_reduced_meas_cells - n_stationary_meas_cells));
    return (CP_MEAS_CELL_VAR *) cell_var;
  }

  REDUCTION_MEAS_CELL_VAR *m_child_meas_cells[2];
  MPI_Request m_child_receive_requests[2];

  VOID post_tree_reduction_receives();

  VOID finish_init() 
  {
    // Try to free storage before allocating additional storage. 
    clear_global_to_local_cell_index_map(); // frees storage
    sort_meas_cells_by_global_index();

    if (use_tree_reduction()) {
      m_my_global_meas_cell_indices = new STP_MEAS_CELL_INDEX[ n_stationary_meas_cells ];

      ccDOTIMES(i, n_stationary_meas_cells)
        m_my_global_meas_cell_indices[i] = m_meas_cell_index_map[i].m_global_cell_index;
    }

    if (sim.n_movb_physics_descs > 0 && is_development) {
      m_global_to_sp_cell_index = xnew sGLOBAL_TO_SP_CELL_HASH_TABLE(n_stationary_meas_cells);
      ccDOTIMES(i, n_stationary_meas_cells) {
        m_global_to_sp_cell_index->Put(m_meas_cell_index_map[i].m_global_cell_index, m_meas_cell_index_map[i].m_sp_cell_index);
      }
    }

    build_map_from_old_to_new_sp_meas_cell_indices(); // overwrites m_meas_cell_index_map[i].m_global_cell_index

    allocate_meas_cells();
  }

  VOID setup_tree_reduction();

  MEAS_CELL_VAR *meas_cells() { return _meas_cells; }

  MEAS_CELL_VAR *meas_cell(STP_MEAS_CELL_INDEX i) {
    if(contains_std_dev_vars)
      return _meas_cells + (MEAS_CELL_OFFSET)i*(n_variables + 1);  // cast prevents overflow
    else
      return _meas_cells + (MEAS_CELL_OFFSET)i*n_variables ;  // cast prevents overflow
  }

  MEAS_CELL_VAR *std_dev_meas_cell(STP_MEAS_CELL_INDEX i) {
      return _meas_cells + (MEAS_CELL_OFFSET)(i*(n_variables + 1) + n_variables);  // cast prevents overflow
  }

  STP_MEAS_CELL_INDEX meas_cell_index(MEAS_CELL_VAR * test_meas_cell) const
  {
    size_t n_var;
    n_var = contains_std_dev_vars ? n_variables + 1 : n_variables;
    size_t idx = (test_meas_cell - _meas_cells) / n_var;
    cassert(idx < n_stationary_meas_cells);
    return idx;
  }

  STP_MEAS_CELL_INDEX meas_cell_var_index(MEAS_CELL_VAR * test_meas_cell) const
  {
    size_t n_var;
    n_var = contains_std_dev_vars ? n_variables + 1 : n_variables;
    return (test_meas_cell - _meas_cells) % n_var;
  }

  size_t ckpt_len(const TIMESTEP current_timestep);
  VOID write_ckpt(const TIMESTEP current_timestep);
  
#ifndef _EXA_HPMPI
  template <write_or_count_t WOC>
  size_t woc_header(sCKPT_BUFFER& pio_ckpt_buff);
  template <write_or_count_t WOC, bool IS_MASTER>
  size_t generate_ckpt_src_map(sCKPT_BUFFER& pio_ckpt_buff,
                               const TIMESTEP current_timestep,
                               void * const srcMap0,
                               int start_element_idx,
                               int end_element_idx);
  size_t ckpt_len_mapped(const TIMESTEP current_timestep,
                         void * const srcMap0);
  size_t write_ckpt_mapped(sCKPT_BUFFER& pio_ckpt_buff,
                           const TIMESTEP current_timestep,
                           void * const srcMap0,
                           int start_element_idx,
                           int end_element_idx);
  size_t write_ckpt_mapped_master(sCKPT_BUFFER& pio_ckpt_buff,
                                  const TIMESTEP current_timestep,
                                  int start_element_idx,
                                  int end_element_idx);
  size_t write_ckpt_mapped_ordinary(sCKPT_BUFFER& pio_ckpt_buff,
                                    const TIMESTEP current_timestep,
                                    int start_element_idx,
                                    int end_element_idx);
#endif
  VOID read_ckpt();

  asINT32 meas_cell_size() { return n_variables * sizeof(MEAS_CELL_VAR); }

  asINT32 cp_meas_cell_size() 
  { 
    if (use_tree_reduction()) {
      return n_cp_variables * sizeof(REDUCTION_MEAS_CELL_VAR);
    } else if (is_meas_vars_output_dp) {
      return n_cp_variables * sizeof(dFLOAT);
    } else {
      return n_cp_variables * sizeof(sFLOAT);
    }
  }

  /* CONVERT_FOR_CP converts the meas cells from their SP format to their CP
   * format (i.e., single float). The transformation takes place within the
   * vector of measurement cells themselves (i.e., no new storage is
   * allocated). This assumes that the CP type of the meas cell is the same
   * size or smaller than the SP type for the meas cell. */
  VOID convert_for_cp();

  VOID convert_moving_data_for_cp();

  /* INCLUDES_FLOATS indicates  whether the meas cells contain floats or
   * doubles. In that case, we make sure to clear the meas cells after
   * transfer to the CP to guard against floating point exceptions.
   */
  BOOLEAN includes_floats() { return TRUE; }

  VOID initialize_master_sp(asINT32 master_sp);

  // During a full checkpoint, the SPs must write meas window data in global index
  // order so that the simulation can be resumed on a different number of SPs. To
  // facilitate this, we sort the local meas cells by global index order. Dev windows
  // also rely on this order. See comment above.

  VMEM_VECTOR < sMEAS_CELL_INDEX_MAP_ENTRY >  m_meas_cell_index_map;

 private:

  VOID push_back_global_meas_cell_index(STP_MEAS_CELL_INDEX global_meas_cell_index,
                                        STP_MEAS_CELL_INDEX sp_cell_index)
  { 
#if DEBUG
    if (sp_cell_index != m_meas_cell_index_map.size())
      msg_internal_error("SP meas window index skipped");
#endif
    sMEAS_CELL_INDEX_MAP_ENTRY entry;
    entry.m_global_cell_index = global_meas_cell_index;
    entry.m_sp_cell_index     = m_meas_cell_index_map.size();
    m_meas_cell_index_map.push_back(entry); 
  }

 public:
  VOID sort_meas_cells_by_global_index()
  {
#if DEBUG
    if (n_stationary_meas_cells != m_meas_cell_index_map.size())
      msg_internal_error("Missed global meas cell index");
#endif

    // sort by global index
    std::sort(&m_meas_cell_index_map[0],
              &m_meas_cell_index_map[0] + n_stationary_meas_cells,
              meas_cell_index_cmp_fcn);

#if DEBUG
    STP_MEAS_CELL_INDEX last_global_index = -1;
    ccDOTIMES(i, n_stationary_meas_cells) {
      if (m_meas_cell_index_map[i].m_global_cell_index <= last_global_index)
        msg_internal_error("Unordered or duplicate global meas cell indices for window %d, meas index %d", index, m_meas_cell_index_map[i].m_global_cell_index);
      last_global_index = m_meas_cell_index_map[i].m_global_cell_index;
    }
#endif
  }

  VOID build_map_from_old_to_new_sp_meas_cell_indices() {
    // build the map from old SP meas cell indices to new SP meas cell indices
    ccDOTIMES(i, n_stationary_meas_cells)
      m_meas_cell_index_map[m_meas_cell_index_map[i].m_sp_cell_index].m_new_sp_cell_index = i;
  }

  VOID free_meas_cell_index_map()
  {
    m_meas_cell_index_map.resize(0);
    m_meas_cell_index_map.trim(); // free all storage
  } 

  private:
  struct sGLOBAL_TO_SP_CELL_TRAITS {
    STP_MEAS_CELL_INDEX HashFcn(const STP_MEAS_CELL_INDEX &global_meas_cell_index) const
    { return global_meas_cell_index; }

    BOOLEAN EqualKeys(const STP_MEAS_CELL_INDEX &key1,
                      const STP_MEAS_CELL_INDEX &key2) const
    { return key1 == key2; }
  };

  typedef tHASH_TABLE< STP_MEAS_CELL_INDEX, STP_MEAS_CELL_INDEX, sGLOBAL_TO_SP_CELL_TRAITS >  sGLOBAL_TO_SP_CELL_HASH_TABLE;
  sGLOBAL_TO_SP_CELL_HASH_TABLE *m_global_to_sp_cell_index;

  // CREATE_MEAS_CELL returns the local index of the meas cell that corresponds
  // to the indicated global index.
  STP_MEAS_CELL_INDEX create_meas_cell(STP_MEAS_CELL_INDEX global_meas_cell_index)
  {
    if (m_global_to_sp_cell_index == NULL) {
      // Defer allocation of this hash table until we know this meas window is used on this SP.
      // For the initial capacity of the hash table, we recognize that for a non-composite fluid 
      // meas window, there will be few entries in the hash table because we exploit the octtree 
      // order of meas cell references in the DGF file (see create_ublk_meas_cell). 
      uINT32 n_local_cells = (uINT32) n_global_stationary_meas_cells / (uINT32) total_sps;
      THASH_INDEX initial_capacity = (is_composite || is_lgi_meas_window_type_surface(meas_window_type) 
                                      ? 4u * n_local_cells 
                                      : 0);
      m_global_to_sp_cell_index = new sGLOBAL_TO_SP_CELL_HASH_TABLE(initial_capacity);
    }

    STP_MEAS_CELL_INDEX *sp_meas_cell_index = m_global_to_sp_cell_index->Get(global_meas_cell_index);

    if (sp_meas_cell_index == NULL) {
      // Use next available cell index. This is a convention that must be common to both SP and CP.
      push_back_global_meas_cell_index(global_meas_cell_index, n_stationary_meas_cells);
      m_global_to_sp_cell_index->Put(global_meas_cell_index, n_stationary_meas_cells);
      return n_stationary_meas_cells++;
    } else {
      return *sp_meas_cell_index;
    }
  }

  public:

  // Used for development window meas cell index lookup
  STP_MEAS_CELL_INDEX find_meas_cell(STP_MEAS_CELL_INDEX global_meas_cell_index)
  {
    if (m_global_to_sp_cell_index != NULL) {
      STP_MEAS_CELL_INDEX *sp_meas_cell_index = m_global_to_sp_cell_index->Get(global_meas_cell_index);
      if (sp_meas_cell_index != NULL) 
        return *sp_meas_cell_index;
    }
    return -1;
  }

  sMOVING_MEAS_CELL_MGR *m_moving_meas_cell_mgr;
  VOID allocate_moving_send_buffer();
  VOID allocate_moving_send_buffer(uINT64 n);
  VOID clear_stationary_meas_cells();
  VOID clear_moving_meas_cells();
  sMOVING_MEAS_CELL_SMART_PTR get_moving_meas_cell(STP_MEAS_CELL_INDEX global_meas_cell_index, sINT16 face_index, bool init);

  sMOVING_MEAS_CELL_MGR* get_moving_meas_cell_mgr()
  {
    return m_moving_meas_cell_mgr;
  }

  VOID delete_moving_meas_cell(MOVING_MEAS_CELL_PTR moving_meas_cell);

  public:

  bool is_per_voxel_for_scale(asINT32 voxel_scale) const
  { return (per_voxel_meas_p
            && (sim_is_scale_same_or_finer(meas_cell_scale, voxel_scale)));
  }

private:
  // These are used within create_ublk_meas_cell
  STP_MEAS_CELL_INDEX last_global_meas_cell_index;
  STP_MEAS_CELL_INDEX last_sp_meas_cell_index;
  STP_COORD m_current_meas_cube_location[3]; // coordinates of meas cube housing current DGF ublk desc

  STP_MEAS_CELL_INDEX compute_reduced_global_meas_cell_indices(std::vector<STP_MEAS_CELL_INDEX> &reduced_meas_cell_indices) 
  {
    STP_MEAS_CELL_INDEX *meas_cell_indices0 = m_child_global_meas_cell_indices[0];
    STP_MEAS_CELL_INDEX *meas_cell_indices1 = m_child_global_meas_cell_indices[1];
    STP_MEAS_CELL_INDEX *meas_cell_indices2 = m_my_global_meas_cell_indices;

    STP_MEAS_CELL_INDEX *end_meas_cell_indices0 = meas_cell_indices0 + m_n_child_meas_cells[0];
    STP_MEAS_CELL_INDEX *end_meas_cell_indices1 = meas_cell_indices1 + m_n_child_meas_cells[1];
    STP_MEAS_CELL_INDEX *end_meas_cell_indices2 = meas_cell_indices2 + n_stationary_meas_cells;

    STP_MEAS_CELL_INDEX n = 0; // number of unique meas cell indices
    // loop advancing all 3 vectors of meas cell indices
    while (1) {
      // if one or more vectors is exhausted, set things up for next loop, and break
      if (meas_cell_indices2 >= end_meas_cell_indices2)
        break;
      if (meas_cell_indices1 >= end_meas_cell_indices1) {
        meas_cell_indices1 = meas_cell_indices2;
        end_meas_cell_indices1 = end_meas_cell_indices2;
        break;
      }
      if (meas_cell_indices0 >= end_meas_cell_indices0) {
        meas_cell_indices0 = meas_cell_indices2;
        end_meas_cell_indices0 = end_meas_cell_indices2;
        break;
      }

      n++;
      STP_MEAS_CELL_INDEX min = MIN(*meas_cell_indices0, MIN(*meas_cell_indices1, *meas_cell_indices2));
      reduced_meas_cell_indices.push_back(min);
      if (*meas_cell_indices0 == min)
        meas_cell_indices0++;
      if (*meas_cell_indices1 == min)
        meas_cell_indices1++;
      if (*meas_cell_indices2 == min)
        meas_cell_indices2++;
    }
      
    // loop advancing 2 vectors of meas cell indices
    while (1) {
      // if one or more vectors is exhausted, set things up for next loop, and break
      if (meas_cell_indices0 >= end_meas_cell_indices0) {
        meas_cell_indices0 = meas_cell_indices1;
        end_meas_cell_indices0 = end_meas_cell_indices1;
        break;
      }
      if (meas_cell_indices1 >= end_meas_cell_indices1)
        break;
        
      n++;
      STP_MEAS_CELL_INDEX min = MIN(*meas_cell_indices0, *meas_cell_indices1);
      reduced_meas_cell_indices.push_back(min);
      if (*meas_cell_indices0 == min)
        meas_cell_indices0++;
      if (*meas_cell_indices1 == min)
        meas_cell_indices1++;
    }

    // loop advancing last vector of meas cell indices
    while (meas_cell_indices0 < end_meas_cell_indices0) {
      n++;
      reduced_meas_cell_indices.push_back(*meas_cell_indices0++);
    }

    return n; // this becomes m_n_reduced_meas_cells
  }
public:

  template <typename FLOAT_TYPE>
  asINT32 calculate_dev_win_extent_index(FLOAT_TYPE centroid[3], uINT8 dev_axis)
  {
    FLOAT_TYPE rlocal[3];
    rlocal[0] = centroid[0] - start_pt[dev_axis][0];
    rlocal[1] = centroid[1] - start_pt[dev_axis][1];
    rlocal[2] = centroid[2] - start_pt[dev_axis][2];
    return ((asINT32) (one_over_dev_win_segment * vdot(rlocal,dev_line_orientation[dev_axis])));
  }

  size_t stationary_meas_cell_array_size() const
  {
    size_t n_var = contains_std_dev_vars ? n_variables+1 : n_variables;
    return n_stationary_meas_cells * n_var;
  }

  VOID allocate_meas_cells()
  {
    sINT64 num_meas_cells = n_stationary_meas_cells;
    if (contains_std_dev_vars) {
      _meas_cells = new MEAS_CELL_VAR[num_meas_cells * (n_variables + 1)];
      memset(_meas_cells, 0, sizeof(MEAS_CELL_VAR)*(num_meas_cells*(n_variables + 1) ) );
    }
    else {
      _meas_cells = new MEAS_CELL_VAR[num_meas_cells * n_variables ];
      memset(_meas_cells, 0, sizeof(MEAS_CELL_VAR)*(num_meas_cells*(n_variables) ) );
    }
  }

  sINT64 n_total_vars_to_cp() { return (sINT64)n_cp_variables * m_n_reduced_meas_cells; }

  VOID allocate_send_buffer() {
    if (use_tree_reduction()) {
      m_send_buffer = (CP_MEAS_CELL_VAR *) xnew REDUCTION_MEAS_CELL_VAR[ n_total_vars_to_cp() ];
    }  else if (is_meas_vars_output_dp) {
      m_send_buffer = (CP_MEAS_CELL_VAR *) xnew dFLOAT[ n_total_vars_to_cp() ];
    } else {
      m_send_buffer = (CP_MEAS_CELL_VAR *) xnew sFLOAT[ n_total_vars_to_cp() ];
    }
  }

  STP_MEAS_CELL_INDEX create_surfel_meas_cell(STP_MEAS_CELL_INDEX global_meas_cell_index)
  {
    return create_meas_cell(global_meas_cell_index); 
  }

  STP_MEAS_CELL_INDEX create_ublk_meas_cell(DGF_UBLK_BASE_DESC ublk_desc,
                                            STP_MEAS_CELL_INDEX global_meas_cell_index,
                                            asINT32 voxel_scale,
                                            BOOLEAN is_split_ublk,
                                            auINT32 meas_cell_ref_voxel_mask);

  VOID clear_global_to_local_cell_index_map()
  { 
    // free hash table's memory
    if (m_global_to_sp_cell_index != NULL) {
      delete m_global_to_sp_cell_index;
      m_global_to_sp_cell_index = NULL;
    }
  }

  // For supporting variable PowerTherm coupling
  std::vector<sMEAS_PHASE_TIME_DESC_SP> m_meas_phase_descs;
  asINT32 end_time;

  asINT32 meas_cell_var_inc() const {
    return 1;
  }

  sMEAS_WINDOW() {
    m_global_to_sp_cell_index = NULL;
    n_stationary_meas_cells = 0;
    n_send_moving_meas_cells = 0;
    nframes_sent = 0;
    n_moving_meas_cell_data = 0;
    m_n_timesteps_since_clear_cache_timestep = -1;
    m_one_over_n_timesteps_since_clear = 0;
    is_output_in_local_csys = 0;

    _meas_cells = NULL;

    last_global_meas_cell_index     = -1;
    last_sp_meas_cell_index         = -1;
    m_current_meas_cube_location[0] = -1;
    m_current_meas_cube_location[1] = -1;
    m_current_meas_cube_location[2] = -1;

    avg_interval = 0;
    midpt        = 0;

    m_is_master_sp = FALSE;
    is_ref_frame_master_sp = FALSE;
    lrfs = NULL;
    rotating_bcs = NULL;
    sliding_bcs = NULL;

    entity_first_meas_cell[0] = NULL;
    entity_first_meas_cell[1] = NULL;
    entity_first_meas_cell[2] = NULL;
    entity_n_segments[0] = NULL;
    entity_n_segments[1] = NULL;
    entity_n_segments[2] = NULL;
    entity_first_segment[0] = NULL;
    entity_first_segment[1] = NULL;
    entity_first_segment[2] = NULL;

    // Clear time and CP info
    current_update_time.clear_time  = TIMESTEP_INVALID;
    current_update_time.output_time = TIMESTEP_INVALID;
    next_update_time.clear_time     = TIMESTEP_INVALID;
    next_update_time.output_time    = TIMESTEP_INVALID;

    m_parent_rank = eMPI_sp_cp_rank();
    m_n_child_sps = 0;
    m_n_child_sps_ready = 0;
    m_child_sps[0] = m_child_sps[1] = 0;
    m_child_global_meas_cell_indices[0] = m_child_global_meas_cell_indices[1] = NULL;
    m_my_global_meas_cell_indices = NULL;
    m_n_child_meas_cells[0] = m_n_child_meas_cells[1] = 0;
    m_n_reduced_meas_cells = 0;
    
    m_send_buffer = NULL;
    m_send_request = MPI_REQUEST_NULL;

    m_moving_send_count_request = MPI_REQUEST_NULL;
    m_moving_send_buffer = NULL;
    m_moving_send_count_request = MPI_REQUEST_NULL;
    m_moving_send_request = MPI_REQUEST_NULL;
    m_current_moving_buffer_size = 0;

    pthread_mutex_init(&m_previous_send_mutex,NULL);
    pthread_cond_init(&m_previous_send_cv,NULL);
    m_all_send_buffers_ready = true;

    m_child_receive_requests[0] = m_child_receive_requests[1] = MPI_REQUEST_NULL;
    m_moving_meas_cell_mgr = NULL;
#if BUILD_GPU
    m_n_device_meas_cells = 0;
    m_device_meas_cells = nullptr;
#endif
  }

  BOOLEAN read_variables(LGI_STREAM stream);
private:
  VOID validate_vars_supported();

} *MEAS_WINDOW;

#define DOTIMES_MEAS_WINDOW_CELLS(meas_cell_var, index_var, meas_window)		\
  MEAS_CELL_VAR *meas_cell_var = (meas_window)->meas_cells();				\
  asINT32 ___(_sizeof_meas_cell) = (meas_window)->meas_cell_size();			\
  asINT32 ___(_n_meas_cells) = (meas_window)->n_stationary_meas_cells;			\
  asINT32 index_var;									\
  for (index_var = 0;									\
       index_var < ___(_n_meas_cells);							\
       index_var++,									\
	 meas_cell_var = (MEAS_CELL_VAR *)((char *)meas_cell_var + ___(_sizeof_meas_cell)))

#define DO_MEAS_WINDOW_CELLS(meas_cell_var, meas_window) \
  DOTIMES_MEAS_WINDOW_MEAS_CELLS(meas_cell_var, ___(_nth_meas_cell), meas_window)

// According to the documentation of std::priority_queue: (from cppreference.com)
//
// Note that the Compare parameter is defined such that it returns true if its
// first argument comes before its second argument in a weak ordering. But
// because the priority queue outputs largest elements first, the elements that
// "come before" are actually output last. That is, the front of the queue
// contains the "last" element according to the weak ordering imposed by
// Compare.
//
// Thus, the eval function below will return a bool parameter to state whether
// lhs < rhs, and the actual functor operator will negate this value. This will
// place the windows in the priority queue in the proper order.

struct sMEAS_WINDOW_OUTPUT_TIME_COMPARE
{
private:
  bool eval(const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const;
public:
  bool operator() (const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const
  {
    return !eval(lhs,rhs);
  }
};

struct sMEAS_WINDOW_CLEAR_TIME_COMPARE
{
private:
  bool eval(const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const;
public:
  bool operator() (const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const
  {
    return !eval(lhs,rhs);
  }
};

typedef class sMEAS_WINDOW_COLLECTION {
private:
  std::vector<MEAS_WINDOW> m_meas_window_array;
  sINT16                   m_n_meas_windows;

  using cOUTPUT_QUEUE = std::priority_queue<MEAS_WINDOW, std::vector<MEAS_WINDOW>, sMEAS_WINDOW_OUTPUT_TIME_COMPARE>;
  using cCLEAR_QUEUE = std::priority_queue<MEAS_WINDOW, std::vector<MEAS_WINDOW>, sMEAS_WINDOW_CLEAR_TIME_COMPARE>;

  cOUTPUT_QUEUE m_output_queue;
  cCLEAR_QUEUE m_clear_queue;
public:

  MEAS_WINDOW next_output_window(bool do_surface_windows, TIMESTEP num_timesteps_done);
  MEAS_WINDOW next_clear_window(bool do_surface_windows, TIMESTEP num_timesteps_done);

  // The send queue contains meas windows that are ready to perform a
  // "reduction" of this SP's meas window data with that of its child
  // SPs. Meas windows are appended to this queue so that they are popped
  // off the queue in the order that they are added to the queue.
  MEAS_WINDOW m_send_queue;
  MEAS_WINDOW m_send_queue_tail;
  
  void insert_in_output_queue(MEAS_WINDOW window);
  void insert_in_clear_queue(MEAS_WINDOW window);
  void insert_in_output_and_clear_queues_if_non_empty(MEAS_WINDOW window);

  asINT32 n_meas_windows() const { return m_n_meas_windows; }

  sMEAS_WINDOW_COLLECTION() 
  { 

    m_send_queue      = NULL;
    m_send_queue_tail = NULL;

    m_n_meas_windows = 0;
  }

  MEAS_WINDOW operator[](asINT32 index)
  { 
    // To accommodate trajectory windows, m_meas_window_array was redefined to contain pointers
    //to window objects instead of the window objects themselves.
    
    //According to the comment in the other branch of this ifdef something might 
    //expect to be able to use an out of bounds array index.  Catch such instances here.
    //if ( index >= m_n_meas_windows )
    //  msg_error("Measurement window index is out of bounds.");

    //The way the DO_MEAS_WINDOWS macro is designed results in an out of bounds index being used at the end of the loop.
    //Return a NULL pointer when that happens instead of the above msg_error
    if ( index >= m_n_meas_windows )
      return(NULL);

    //return *(m_meas_window_array.getElt(index));
    return(m_meas_window_array[index]);
  }

  const sMEAS_WINDOW * operator[](asINT32 index) const
  { 
    cassert(index < m_meas_window_array.size());
    cassert(index >= 0);
    return m_meas_window_array[index]; 
  }

  std::vector<MEAS_WINDOW>::iterator begin() { return m_meas_window_array.begin(); }
  std::vector<MEAS_WINDOW>::iterator end() { return m_meas_window_array.end(); }

  std::vector<MEAS_WINDOW>::const_iterator begin() const { return m_meas_window_array.begin(); }
  std::vector<MEAS_WINDOW>::const_iterator end() const { return m_meas_window_array.end(); }

  MEAS_WINDOW add_meas_window(cDGF_MEAS_WINDOW *dgf_window); 

} *MEAS_WINDOW_COLLECTION;

extern sMEAS_WINDOW_COLLECTION  g_meas_windows;
inline asINT32                  g_n_meas_windows() { return g_meas_windows.n_meas_windows(); }

#if !GPU_COMPILER
_ALWAYS_INLINE_ sMEAS_WINDOW_COLLECTION&  get_meas_window_coll_ref()
{
  return g_meas_windows;
}
#endif

#define DO_MEAS_WINDOWS(window_var)                                                    \
  for ( MEAS_WINDOW window_var : g_meas_windows )

#define DO_SURFEL_MEAS_WINDOWS(window_var)                                             \
  asINT32 ___(n_meas_windows) = g_n_meas_windows();                                    \
  asINT32 ___(i)              = 0;                                                     \
  MEAS_WINDOW window_var      = g_n_meas_windows() > 0? g_meas_windows[0] : NULL;      \
  for ( ; ___(i) < ___(n_meas_windows); window_var = g_meas_windows[ ++ ___(i) ])      \
    if (is_lgi_meas_window_type_surface(window_var->meas_window_type))

#define DO_UBLK_MEAS_WINDOWS(window_var)                                               \
  asINT32 ___(n_meas_windows) = g_n_meas_windows();                                    \
  asINT32 ___(i)              = 0;                                                     \
  MEAS_WINDOW window_var      = g_n_meas_windows() > 0? g_meas_windows[0] : NULL;      \
  for ( ; ___(i) < ___(n_meas_windows); window_var = g_meas_windows[ ++ ___(i) ])      \
    if (!is_lgi_meas_window_type_surface(window_var->meas_window_type))

typedef class sUBLK_MEAS_CELL_PTR : public MEAS_CELL_PTR {
private:
  STP_MEAS_WINDOW_INDEX m_meas_window_index;
  VOXEL_MASK_8   m_meas_voxel_mask; // memory is being wasted here
public:

  VOID set_window_index(STP_MEAS_WINDOW_INDEX window_index)   {
    m_meas_window_index = window_index;
  }
  VOID set_voxel_mask(STP_VOXEL_MASK voxel_mask)   {
    m_meas_voxel_mask = VOXEL_MASK_8{voxel_mask};
  }

  VOXEL_MASK_8 voxel_mask() const { return m_meas_voxel_mask; }

  STP_MEAS_WINDOW_INDEX window_index() const {
    return m_meas_window_index;
  }

  MEAS_CELL_PTR meas_cell_ptr(uINT8) { return *this; }

} *UBLK_MEAS_CELL_PTR;

typedef class sSURFEL_MEAS_CELL_PTR : public MEAS_CELL_PTR {
private:
  STP_MEAS_WINDOW_INDEX        m_meas_window_index;

public:
  VOID set_window_index(STP_MEAS_WINDOW_INDEX window_index)   {
    m_meas_window_index = window_index;
  }

  STP_MEAS_WINDOW_INDEX window_index() const {
    return m_meas_window_index;
  }

  MEAS_CELL_PTR meas_cell_ptr(uINT8) { return *this; }

} *SURFEL_MEAS_CELL_PTR;

typedef class sSURFEL_MEAS_DATA {
public:
  asINT32              m_n_meas_cell_ptrs;
  union {
    SURFEL_MEAS_CELL_PTR m_surfel_meas_cell_ptrs;
    sMSFL_MEAS_CELL_PTR* m_msfl_meas_cell_ptrs;
  };

  sSURFEL_MEAS_DATA() {
    m_n_meas_cell_ptrs      = 0;
    m_surfel_meas_cell_ptrs = nullptr;
  }
} *SURFEL_MEAS_DATA;

extern MPI_Datatype eMPI_GRF_MEAS_FRAME_SP_TO_CP_MSG;
extern MPI_Datatype eMPI_LRF_MEAS_FRAME_SP_TO_CP_MSG;
extern MPI_Datatype eMPI_MBC_MEAS_FRAME_SP_TO_CP_MSG;
extern MPI_Datatype eMPI_MOVB_MEAS_FRAME_SP_TO_CP_MSG;
extern asINT32 g_n_time_varying_lrfs ;
extern asINT32 g_n_time_varying_rotating_bcs;
extern asINT32 g_n_time_varying_sliding_bcs;
extern asINT32 g_n_time_varying_mbcs;

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

BOOLEAN read_meas_window_vars(LGI_STREAM stream);

VOID initialize_meas_reduction_trees();

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/

void get_window_ready_for_sending(MEAS_WINDOW window);

/* Clears measurements as necessary */
VOID meas_do_clear(const BOOLEAN do_surface_windows, const TIMESTEP strand_time);

/* Sends all measurements to the CP as necessary */
VOID meas_do_output(const BOOLEAN do_surface_windows, const TIMESTEP strand_time);

VOID sim_finalize_meas();

VOID prepare_meas_window_ref_frame_info(MEAS_WINDOW window);

// currently local reference frame surfel groups do not participate in measurements, 
#define REF_FRAME_SURFELS_COLLECT_MEASUREMENTS 0

//The following handles maping a set of window ids for every possible
//combination of windows a ublk might be in to a combination index.
//The index is used to indicate what set of meas vars need to be
//computed before measurement.
class  sMEAS_WINDOW_COMBINATION : public std::unordered_set<int> {};

struct sMEAS_WINDOW_COMBINATION_HASH {
 size_t operator()( const sMEAS_WINDOW_COMBINATION& combination) const {
    size_t hash = 0;
    for(auto i = combination.begin(); i != combination.end(); ++i) {
      hash ^= std::hash<int>()(*i); //this combining of hash values must be independent of the order in the set.
    }
    return hash;
  }
};

class sMEAS_WINDOW_COMBINATIONS : public std::unordered_map<sMEAS_WINDOW_COMBINATION, int, sMEAS_WINDOW_COMBINATION_HASH> {
  std::vector<sPARTICLE_MEAS_VARTYPE_BITSET> m_meas_var_bitsets;
 public:
  sPARTICLE_MEAS_VARTYPE_BITSET meas_var_bitset(int combination_index) {return m_meas_var_bitsets[combination_index];}
  int index_of(sMEAS_WINDOW_COMBINATION window_ids) {
    std::unordered_map<sMEAS_WINDOW_COMBINATION, int>::iterator element = find(window_ids);
    if(element != end())
      return element->second;     //Return the id already assigned to this combination.
    int next_index  = size();  //Assign the next index to this combination.
    insert(std::make_pair(sMEAS_WINDOW_COMBINATION(window_ids), next_index));
    return next_index;
  }
  sMEAS_WINDOW_COMBINATION nth_combination(int combination_index) { 
    for( auto i = begin(); i != end(); ++i) {
      if(i->second == combination_index) {
        return i->first;
      }
    }
    msg_internal_error( "The sMEAS_WINDOW_COMBINATIONS map doesn't contain the combination with the requested index.");
  }
  void compute_bitsets() {
    m_meas_var_bitsets.resize(size());
    ccDOTIMES(combo_index, size()) {
      sMEAS_WINDOW_COMBINATION window_ids = nth_combination(combo_index);
      for(auto w = window_ids.begin(); w != window_ids.end(); ++w) {
        sMEAS_WINDOW *window = g_meas_windows[*w];
        m_meas_var_bitsets[combo_index].unmask_vartypes(window->n_variables, window->var_types);
      }
    }
  }
};

extern sMEAS_WINDOW_COMBINATIONS g_meas_window_combinations;

#if BUILD_GPU
inline void debug_check_device_reduction_metadata(const char * tag)
{
  MEAS_WINDOW window = g_meas_windows[0];

  bool ok = window->check_reduction_metadata();

  if (!ok) {
    msg_error("Metadata has been corrupted at timestep %ld. tag '%s'", g_timescale.m_time, tag);
  }

}
#endif




#endif /* _SIMENG_MEAS_H */

