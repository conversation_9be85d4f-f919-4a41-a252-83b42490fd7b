# ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "78")
##############################################################################
### PowerFLOW Simulator Simulation Process                                 ###
###                                                                        ###
### Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.         ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239;                     ###
###        6,089,744; 7,558,714                                            ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Dassault Systemes Americas Corp. ###
### and contains its confidential trade secrets.  Use, examination, copying, ###
### transfer and disclosure to others, in whole or in part, are prohibited ###
### except with the express prior written consent of Dassault Systemes     ###
### Americas Corp.                                                         ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78")

VMAKE_COMPILER=clang

SIM_LINKER  = $(CCC)
SIM_SYSLIBS = -lpthread

include ../master.mak


