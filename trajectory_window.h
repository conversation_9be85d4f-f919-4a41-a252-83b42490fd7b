#ifndef TRAJECTORY_WINDOW_H_
#define TRAJECTORY_WINDOW_H_

#include "particle_solver_data.h"
#include "meas.h"
#include LGI_H

#include "particle_emitters.h"

VOID assign_parcel_a_remapable_id(PARCEL_STATE parcel,
                                  asINT32 trajectory_window);  // name space that only include trajectory windows


typedef class sTRAJECTORY_WINDOW : public sMEAS_WINDOW {

 private:
  std::string m_name;
  
  asINT32 m_window_index;
  static asINT32 trajectory_window_count;
  asINT32 m_start_time;
  asINT32 m_end_time;
  
  BOOLEAN m_measure_trajectory_vertices;
  BOOLEAN m_measure_velocities;
  BOOLEAN m_use_dynamic_decimation;
  sPARTICLE_VAR m_dynamic_decimation_tolerance;
  asINT32 m_static_decimation_rate;
  HITPOINT_OPTIONS m_hitpoint_options;
  sPARTICLE_VAR m_fraction_eligible_for_measurement; //added 2/28/17 to support changes for cdi 3.2 (wanderer).
  asINT32 m_trajectory_window_index;
 

  BOOLEAN m_measure_hitpoints_only_from_adhering_particles;
  BOOLEAN m_measure_impulse;

 public:
  sTRAJECTORY_WINDOW(asINT32 window_index, cDGF_MEAS_WINDOW* lgi_record, std::string name);
  ~sTRAJECTORY_WINDOW(){}
 

  HITPOINT_OPTIONS hitpoint_options() {return m_hitpoint_options;}
  sPARTICLE_VAR fraction_eligible_for_measurement() {return m_fraction_eligible_for_measurement;}
  asINT32 trajectory_window_index() {return m_trajectory_window_index;}
  BOOLEAN use_dynamic_decimation() {return m_use_dynamic_decimation;}
  sPARTICLE_VAR dynamic_decimation_tolerance_sqr() {return m_dynamic_decimation_tolerance * m_dynamic_decimation_tolerance;}
  asINT32 static_decimation_rate() {return m_static_decimation_rate;}
  BOOLEAN include_vertices() {return m_measure_trajectory_vertices;}

  BOOLEAN is_active() {
    if((g_timescale.time_flow() >= m_start_time && g_timescale.time_flow() <= m_end_time) ||
       (g_timescale.time_flow() >= m_start_time && m_end_time < 0)) {
      return TRUE;
    }
    return FALSE;
  }

  BOOLEAN is_active(asINT32 time) {
    if((time >= m_start_time && time <= m_end_time) ||
       (time >= m_start_time && m_end_time < 0)) {
      return TRUE;
    }
    return FALSE;
  }

  BOOLEAN is_active(asINT32 time, PARTICLE_EVENT_TYPE event_type) {
    if(event_type == EVENT_EMITTED)
      return is_active(g_timescale.time_flow());
    return is_active(g_timescale.time_flow() + 1); 
  }

  VOID set_options(LGI_TRAJECTORY_WINDOW_OPTIONS_REC &record) {
    m_start_time = record.start_time;
    m_end_time = record.end_time;
    m_measure_trajectory_vertices = record.record_trajectories;
    m_measure_velocities = record.record_velocities;
    m_use_dynamic_decimation = record.use_dynamic_decimation;
    m_dynamic_decimation_tolerance = record.decimation_tolerance;
    m_static_decimation_rate = record.static_decimation_rate;
    m_hitpoint_options = record.hitpoint_options;
    m_measure_hitpoints_only_from_adhering_particles = record.record_only_adhering_hitpoints;
    m_measure_impulse = record.record_normal_impulse;
    m_fraction_eligible_for_measurement = record.fraction_eligible_for_measurement;

    if (g_static_trajectory_meas_decimation_rate > 0) {
      m_use_dynamic_decimation = FALSE;
      m_static_decimation_rate = g_static_trajectory_meas_decimation_rate;
    }

  }


  VOID record_hitpoint(PARCEL_STATE parcel,
                       sPARTICLE_VAR position[N_SPACE_DIMS],
                       sPARTICLE_VAR fractional_time_of_impact,
                       asINT32 surface_index,
                       sPARTICLE_VAR inbound_velocity[N_SPACE_DIMS],
                       sPARTICLE_VAR outpund_velocity[N_SPACE_DIMS],
                       STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                       PARTICLE_EVENT_TYPE event_type);

  //Parent parcel can be NULL in the following if the parcel has no parent.
  VOID record_startpoint(PARCEL_STATE parcel, PARTICLE_EVENT_TYPE event_type, PARCEL_STATE parent_parcel);

  VOID record_vertex(PARCEL_STATE parcel,
                     sPARTICLE_VAR position[N_SPACE_DIMS],
                     sPARTICLE_VAR velocity[N_SPACE_DIMS],
                     PARTICLE_EVENT_TYPE event_type);

  VOID flag_record_for_parent_child_data(sTRAJECTORY_PARENT_CHILD_DATA_BASE &control_record,
                                         PARCEL_STATE parcel,
                                         asINT32 num_subrecords,
                                         asINT32 source_emitter_id);


  //This is intended to be used for splash events where a hitpoint is generated and there are multiple children parcels.
  VOID record_hitpoint_with_children(PARCEL_STATE parent_parcel,
                                     sPARTICLE_VAR position[N_SPACE_DIMS],
                                     sPARTICLE_VAR fractional_time_of_impact,
                                     asINT32 surface_index,
                                     sPARTICLE_VAR inbound_velocity[N_SPACE_DIMS],
                                     sPARTICLE_VAR outbound_velocity[N_SPACE_DIMS],
                                     STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                     PARTICLE_EVENT_TYPE event_type,
                                     std::vector<PARCEL_STATE> &child_parcels,
                                     std::vector<PARTICLE_EVENT_TYPE> &child_source_flags);

  //This is intended for breakup and split events where no hitpoint is generated.
  VOID record_multiple_children(PARCEL_STATE parent_parcel,
                                PARTICLE_EVENT_TYPE event_type,
                                std::vector<PARCEL_STATE> &child_parcels,
                                std::vector<PARTICLE_EVENT_TYPE> &child_source_flags);

  //This method is intended for merge events.
  VOID record_multiple_parents(PARCEL_STATE child_parcel,
                               PARTICLE_EVENT_TYPE event_type,
                               std::vector<PARCEL_STATE> &parent_parcels);
                               


}* TRAJECTORY_WINDOW;

extern std::vector<TRAJECTORY_WINDOW> g_trajectory_windows;
extern BOOLEAN g_has_trajectory_windows;

VOID reset_parcel_index_in_timestep();
auINT32 abstract_emitter_id(PARCEL_STATE parcel);

#endif
