/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Surfel advection 
 *
 * James Hoch, Exa Corporation 
 * Created Tues Dec 16, 1997
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "surfel_advect_sp.h"
#include "surfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "sp_timers.h"
#include "shob_groups.h"
#include "mirror.h"
#include "lattice.h"
#include "sim.h"
#include "comm_groups.h"

/*--------------------------------------------------------------------------*
 * Operations (S2S)
 *--------------------------------------------------------------------------*/

VOID sS2S_ADVECT_DATA::create_surfel_interactions(DGF_SURFEL_DESC surfel_desc) {

  DGF_SURFEL_SURFEL_WEIGHT_SET_DESC weight_set = &surfel_desc->weight_sets[0];
  asINT32 total_size = 0;
  for (asINT32 w = 0; w < surfel_desc->s.num_surfel_weight_sets; w++, weight_set++) {
    STP_LATVEC_MASK          latvec_mask      = weight_set->w.latvec_mask;

    asINT32 n_weights = active_latvecs(latvec_mask);
    //if (n_weights > 0) {
    total_size += sSURFEL_SURFEL_INTERACTION::size(n_weights);
    //}
  }
  m_n_src_surfels = surfel_desc->s.num_surfel_weight_sets;
  m_surfel_interactions = (SURFEL_SURFEL_INTERACTION) malloc(total_size);

  weight_set = &surfel_desc->weight_sets[0];
  SURFEL_SURFEL_INTERACTION surfel_interaction = m_surfel_interactions;
  for (asINT32 w = 0; w < surfel_desc->s.num_surfel_weight_sets; w++, weight_set++) {
    // will be converted to pointer later
    surfel_interaction->m_tagged_src_surfel.set_surfel_id(weight_set->w.src_surfel_id);
    surfel_interaction->m_even_odd_mask = weight_set->w.even_odd;

    STP_LATVEC_MASK latvec_mask = weight_set->w.latvec_mask;
    asINT32 n_weights = active_latvecs(latvec_mask);
    surfel_interaction->m_n_weights = n_weights;

    cDGF_SURFEL_WEIGHT_VALUE *weights = &weight_set->weights[0];
    uINT8 *surfel_latvec             = surfel_interaction->latvecs();
    STP_SURFEL_WEIGHT *surfel_weight = surfel_interaction->weights();
    ccDOTIMES(latvec, N_LATTICE_VECTORS) {
      if ((latvec_mask >> latvec) & 1) {
        *surfel_latvec = latvec;
        surfel_latvec++;
        // This is currently sFLOAT in dgf file
        *surfel_weight = (STP_SURFEL_WEIGHT)weights->weight;
        surfel_weight++;
        weights++;
      }
    }
    surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
  }
}

VOID surfel_accumulate_s2s_weights(SURFEL dest_surfel) {

  if (!dest_surfel->is_s2s_destination()) {
    msg_internal_error("Surfel %d should participate in S2S", dest_surfel->id());
  }

  S2S_ADVECT_DATA s2s_advect_data = dest_surfel->s2s_advect_data();
  asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

  STP_GEOM_VARIABLE *pgram_volumes = (STP_GEOM_VARIABLE *) dest_surfel->lb_data()->pgram_volumes;

  dFLOAT dest_pgvm_sum = 0;
  ccDOTIMES(i, N_LATTICE_VECTOR_PAIRS) {
    dest_pgvm_sum += pgram_volumes[i];
  }

  SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;

  dFLOAT lrf_sampling_weight = 0;
  BOOLEAN is_there_any_even_odd_src_surfel = FALSE;
  /* Accumulate the surfel to surfel contributions */
  ccDOTIMES(nth_src_surfel, n_src_surfels) {

    asINT32 n_weights = surfel_interaction->m_n_weights;
    SURFEL src_surfel = surfel_interaction->m_tagged_src_surfel.src_surfel();

    auINT32 interaction_even_odd_mask = surfel_interaction->m_even_odd_mask;

    dFLOAT overlap_area = 0;
    dFLOAT src_pgvm_sum = 0;
    ccDOTIMES(i, N_LATTICE_VECTOR_PAIRS) {
      src_pgvm_sum += src_surfel->lb_data()->pgram_volumes[i];
    }

    /* If there is any even_odd surfel in s2s advection, the lrf_s2s_sampling is turned off. */
    if (src_surfel->even_odd_mask() != STP_PROCESS_ON_ALL_TIMES)
      is_there_any_even_odd_src_surfel = TRUE;

    STP_SURFEL_WEIGHT *weights = surfel_interaction->weights();
    auINT8            *latvecs = surfel_interaction->latvecs();

    ccDOTIMES(nth_weight, n_weights) {
      STP_SURFEL_WEIGHT in_weight = *weights++;
      asINT32 latvec = *latvecs++;                  //inwards to dest surfel
      asINT32 latvec_pair = latvec_to_latvec_pair(latvec);

      /* src_surfel has to be slip type of surfel. */
      if (src_surfel->is_wall()) {
        overlap_area += in_weight * src_surfel->lb_data()->pgram_volumes[latvec_pair] * dest_surfel->lb_data()->pgram_volumes[latvec_pair] * src_surfel->area;
      }

      if ((src_surfel->is_lrf()) &&
          (src_surfel->even_odd_mask() == STP_PROCESS_ON_ALL_TIMES))
        lrf_sampling_weight += in_weight * dest_surfel->lb_data()->pgram_volumes[latvec_pair];

    }
#if BUILD_5G_LATTICE || BUILD_6X_SOLVER || BUILD_D39_LATTICE
    if (src_surfel->is_inlet_or_outlet())
      dest_surfel->lb_data()->m_attribs.set_is_interacting_io_surfel();
#endif

    /* Only consider lrf surfel */
    if (dest_surfel->is_lrf()) {
      dest_surfel->lrf_data()->lrf_s2s_factor += overlap_area / (src_pgvm_sum * dest_surfel->area);
    }

    /* For conduction surfels */
      /* Even/odd source surfels would result in every weight being counted
       * twice - once for the odd and once for the even surfel. For the
       * following accumulation, we avoid even src surfels to avoid this
       * double counting */
    if (dest_surfel->is_conduction_surface()
        && !src_surfel->is_even()) {
      // Conduction surfels should only interact with other conduction surfels */
      //cassert(src_surfel->is_conduction_surface()); 
#if ENABLE_CONSISTENCY_CHECKS
    if (!src_surfel->is_conduction_surface()) 
        msg_internal_error("Conduction surfel %d at location %g %g %g "
                           "should not be interacting with "
                           "non-conduction surfel %d at location %g %g %g.",
                           dest_surfel->id(), 
                           (float)dest_surfel->centroid[0],
                           (float)dest_surfel->centroid[1],
                           (float)dest_surfel->centroid[2],
                           src_surfel->id(),
                           (float)src_surfel->centroid[0],
                           (float)src_surfel->centroid[1],
                           (float)src_surfel->centroid[2]);
#endif

#ifdef CONDUCTION_COMPUTE_S2S_WEIGHTS_ON_THE_FLY
      sdFLOAT npgram_centroid[3];
      sdFLOAT npgram_length = scale_to_voxel_size(src_surfel->scale());
      sdFLOAT npgram_half_length = 0.5 * npgram_length;

      vcopy(npgram_centroid, src_surfel->centroid);
      vmac(npgram_centroid, -npgram_half_length, src_surfel->normal);
      vdec(npgram_centroid, dest_surfel->centroid);

      sdFLOAT dist = vlength(npgram_centroid);
      sdFLOAT npgram_vol = src_surfel->area * npgram_length;
      sdFLOAT used_weight = g_pfc_s2s_weight_scaling_factor
                            * approximate_pgram_dist_weighting(dist)
                            * approximate_pgram_volume_weighting(npgram_vol);

#else
#error Incomplete implementation: s2s weights should be computed and stored
#endif

      dest_surfel->conduction_data()->sampling_weight_with_interface_s2s_inv += used_weight;
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      LOG_MSG_IF(dest_surfel->id() == 87 || dest_surfel->id() == 69,
          "CONDUCTION_SOLVER", LOG_TS).
        printf("d<-src(lv): %d<-%d, wt: %.17g",
            dest_surfel->id(), src_surfel->id(), used_weight);
#endif

#ifdef CONDUCTION_SET_LOWER_LIMIT_FOR_SAMPLING_DISTANCE_IN_S2S_SITUATIONS
      // nothing to add
#else
#error Have to use the correct sampling distance from source surfel here
      // msg_internal_error("Check if the sampling distance addition for s2s has the values loaded");
      // CONDUCTION-TODO: have to add the right distance here
#endif
    }

    surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
  }

  // This code that initializes surfel->s2s_sampling_weight must be kept in sync with the related code
  // in tS2S_GROUP::resolve_src_surfel_pointers.
  if (!is_there_any_even_odd_src_surfel && (dest_surfel->is_wall()))
    dest_surfel->s2s_sampling_weight = lrf_sampling_weight;
  if (dest_surfel->lrf_data()->lrf_s2s_factor > 1.0) {
    dest_surfel->lrf_data()->lrf_s2s_factor = 1.0;
  }
}


static void copy_ckpt_lrf_data(SURFEL surfel) GNU_FUNC_ATTR(no_sanitize("address"))
{
  memcpy(surfel->lrf_data(), &surfel->m_lrf_solver_data_from_ckpt, sizeof(surfel->m_lrf_solver_data_from_ckpt));
   
  //In double precision, lrf_solver_data and m_lrf_solver_data_from_ckpt
  //overlap in memory since a union is used. This code only zeros out the
  //portion that does not overlap
  char *lrf_solver_data_end =  (char *)surfel->lrf_data() +  sizeof (surfel->m_lrf_solver_data_from_ckpt);
  sINT32 overlap = (char *) &surfel->m_lrf_solver_data_from_ckpt - lrf_solver_data_end;
  if (overlap > 0) {
    memset(&surfel->m_lrf_solver_data_from_ckpt, 0, sizeof(surfel->m_lrf_solver_data_from_ckpt));
  } else {
    memset(lrf_solver_data_end, 0, (sizeof(surfel->m_lrf_solver_data_from_ckpt)+overlap));
  }
}

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/
    
// ACCUMULATE_ALL_V2S_WEIGHTS requires that pgrams are defined for both 
// dynamics and ghost surfels. 
VOID accumulate_all_v2s_and_s2s_weights()
{
  // Temporary double precision pgram volumes (used only during the computation of post-advect
  // scale factors) overlap surfel->in_states_voxel_weight and surfel->in_states_voxel_weight2,
  // which are accumulated in this function in the calls to accumulate_v2s_surfel_weights.

  // Memset m_dp_pgram_volumes first and copy lrf ckpt data to lrf_data(), since otherwise lrf_data() will be cleared when accumulating weights later in this function
  DO_SCALES_FINE_TO_COARSE(scale) {
    {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        memset(surfel->m_dp_pgram_volumes, 0, sizeof(surfel->m_dp_pgram_volumes));
        if (sim.is_full_checkpoint_restore) {
          copy_ckpt_lrf_data(surfel);
        }
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            memset(mirror_surfel->m_dp_pgram_volumes, 0, sizeof(mirror_surfel->m_dp_pgram_volumes));
            if (sim.is_full_checkpoint_restore) {
              copy_ckpt_lrf_data(mirror_surfel);
            }
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      SURFEL ext_surfel = slrf_surfel_pair->m_exterior_surfel;
      memset(ext_surfel->m_dp_pgram_volumes, 0, sizeof(ext_surfel->m_dp_pgram_volumes));
      if (sim.is_full_checkpoint_restore) {
        copy_ckpt_lrf_data(ext_surfel);
          // Need to reset lrf_v2s_scale_diff and lrf_s2s_factor here since it will be accumulated in accumulate_v2s(s2s)_weight
          ext_surfel->lrf_data()->lrf_v2s_scale_diff = 0;
          ext_surfel->lrf_data()->lrf_s2s_factor = 0;
          ext_surfel->lrf_data()->lrf_v2s_dist = 0;
      }

      SURFEL int_surfel = slrf_surfel_pair->m_interior_surfel;
      memset(int_surfel->m_dp_pgram_volumes, 0, sizeof(int_surfel->m_dp_pgram_volumes));
      if (sim.is_full_checkpoint_restore) {
        copy_ckpt_lrf_data(int_surfel);
        // Need to reset lrf_v2s_scale_diff and lrf_s2s_factor here since it will be accumulated in accumulate_v2s(s2s)_weight
        int_surfel->lrf_data()->lrf_v2s_scale_diff = 0;
        int_surfel->lrf_data()->lrf_s2s_factor = 0;
        int_surfel->lrf_data()->lrf_v2s_dist = 0;
      }
    }

    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
      memset(ext_surfel->m_dp_pgram_volumes, 0, sizeof(ext_surfel->m_dp_pgram_volumes));
      if (sim.is_full_checkpoint_restore) {
        copy_ckpt_lrf_data(ext_surfel);
      }
      if (ext_surfel->has_mirror()) {
	sSURFEL_MIRROR_DATA *mirror_data = ext_surfel->mirror_data();
	while (mirror_data) {
	  SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	  memset(mirror_surfel->m_dp_pgram_volumes, 0, sizeof(mirror_surfel->m_dp_pgram_volumes));
	  if (sim.is_full_checkpoint_restore) {
	    copy_ckpt_lrf_data(mirror_surfel);
	  }
	  mirror_data = mirror_data->m_next_mirror_data;
	}
      }

      SURFEL int_surfel = isurfel_pair->m_interior_surfel;
      memset(int_surfel->m_dp_pgram_volumes, 0, sizeof(int_surfel->m_dp_pgram_volumes));
      
      if (sim.is_full_checkpoint_restore) {
        copy_ckpt_lrf_data(int_surfel);
      }
      if (int_surfel->has_mirror()) {
	sSURFEL_MIRROR_DATA *mirror_data = int_surfel->mirror_data();
	while (mirror_data) {
	  SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	  memset(mirror_surfel->m_dp_pgram_volumes, 0, sizeof(mirror_surfel->m_dp_pgram_volumes));
	  if (sim.is_full_checkpoint_restore) {
	    copy_ckpt_lrf_data(mirror_surfel);
	  }
	  mirror_data = mirror_data->m_next_mirror_data;
	}
      }
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_surfel_group, scale) {
      DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_surfel_group) {
        if (!tagged_mlrf_surfel.is_surfel_weightless()) {
          SURFEL surfel = tagged_mlrf_surfel.mlrf_surfel();
          memset(surfel->m_dp_pgram_volumes, 0, sizeof(surfel->m_dp_pgram_volumes));

          if (sim.is_full_checkpoint_restore) {
            copy_ckpt_lrf_data(surfel);
            // Need to reset lrf_v2s_scale_diff and lrf_s2s_factor here since it will be accumulated in accumulate_v2s(s2s)_weight
            surfel->lrf_data()->lrf_v2s_scale_diff = 0;
            surfel->lrf_data()->lrf_s2s_factor = 0;
            surfel->lrf_data()->lrf_v2s_dist = 0;
          }
        }
      }
    }
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(surfel);
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        if (surfel->is_s2s_destination())
          surfel_accumulate_s2s_weights(surfel);
      }
    }
    { 
      DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
        memset(surfel->m_dp_pgram_volumes, 0, sizeof(surfel->m_dp_pgram_volumes));
        surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(surfel);
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        if (surfel->is_s2s_destination())
          surfel_accumulate_s2s_weights(surfel);
      } 
    }
    { 
      DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
        memset(wsurfel_flow->m_dp_pgram_volumes, 0, sizeof(wsurfel_flow->m_dp_pgram_volumes));
        wsurfel_flow->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(wsurfel_flow);
        if (wsurfel_flow->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = wsurfel_flow->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(wsurfel_flow, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(wsurfel_flow, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        if (wsurfel_flow->is_s2s_destination())
          surfel_accumulate_s2s_weights(wsurfel_flow);
      } 
    }
    { 
      DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
        memset(wsurfel_conduction->m_dp_pgram_volumes, 0, sizeof(wsurfel_conduction->m_dp_pgram_volumes));
        wsurfel_conduction->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(wsurfel_conduction);
        if (wsurfel_conduction->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = wsurfel_conduction->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(wsurfel_conduction, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(wsurfel_conduction, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        if (wsurfel_conduction->is_s2s_destination())
          surfel_accumulate_s2s_weights(wsurfel_conduction);
      } 
    }
    { 
      DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
        memset(contact_surfel->m_dp_pgram_volumes, 0, sizeof(contact_surfel->m_dp_pgram_volumes));
        contact_surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(contact_surfel);
        if (contact_surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = contact_surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(contact_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(contact_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        if (contact_surfel->is_s2s_destination())
          surfel_accumulate_s2s_weights(contact_surfel);
      } 
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      {
        SURFEL ext_surfel = slrf_surfel_pair->m_exterior_surfel;
        ext_surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(ext_surfel);
        if (ext_surfel->is_s2s_destination()) {
          surfel_accumulate_s2s_weights(ext_surfel);
        }
      }
      {
        SURFEL int_surfel = slrf_surfel_pair->m_interior_surfel;
        int_surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(int_surfel);
        if (int_surfel->is_s2s_destination()) {
          surfel_accumulate_s2s_weights(int_surfel);
        }
      }
      surfel_pair_exchange_slrf_s2s_and_v2s_factors(slrf_surfel_pair);
    }

    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      {
        SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
        ext_surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(ext_surfel);

        if (ext_surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = ext_surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(ext_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(ext_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }

        if (ext_surfel->is_s2s_destination())
          surfel_accumulate_s2s_weights(ext_surfel);
      }
      {
        SURFEL int_surfel = isurfel_pair->m_interior_surfel;
        int_surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
        surfel_accumulate_v2s_weights(int_surfel);

        if (int_surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = int_surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
            STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
            surfel_accumulate_v2s_weights(mirror_surfel);
            reflect_mirror_surfel_weights_to_real_surfel(int_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            reflect_real_surfel_weights_to_mirror_surfel(int_surfel, mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }

        if (int_surfel->is_s2s_destination())
          surfel_accumulate_s2s_weights(int_surfel);
      }
    }

    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_surfel_group, scale) {
      DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_surfel_group) {
        if (!tagged_mlrf_surfel.is_surfel_weightless()) {
          SURFEL surfel = tagged_mlrf_surfel.mlrf_surfel();
          surfel->voxel_surfel_weight_total = 0; // Since this is overloaded with m_surfel_group_dest_sp
          surfel_accumulate_v2s_weights(surfel);
          if (surfel->is_s2s_destination())
            surfel_accumulate_s2s_weights(surfel);
        }
      }
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      // sampling surfels do not have pgram volumes
      surfel_accumulate_v2s_sampling_weights(sampling_surfel);
    }
  }

  // loop over surfels to print
  int PRINT_WEIGHTS = 0;
  if (PRINT_WEIGHTS) {
    SCALE scale = 0;
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel2, scale) {
      printf("V2SW %d \n",surfel2->id());
      ccDOTIMES(is, N_LATTICE_VECTORS/2) {
        printf("%e ",((STP_SURFEL_WEIGHT *) surfel2->in_state_scale_factors)[is]);
        if ((is+1)%6==0) printf("\n");
      }
      if ((N_LATTICE_VECTORS/2)%6!=0) printf("\n");
    }
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel1, scale) {
      printf("V2SSW %d swt %e pv2s %e mme %e \n",surfel1->id(),
	     float(surfel1->voxel_surfel_weight_total),
             float(surfel1->percent_v2s),
             float(surfel1->mme_weight));
      ccDOTIMES(is, N_LATTICE_VECTORS/2) {
        printf("%e ",float(surfel1->in_states_voxel_weight[is]));
        if ((is+1)%6==0) printf("\n");
      }
      if ((N_LATTICE_VECTORS/2)%6!=0) printf("\n");
      ccDOTIMES(is, N_LATTICE_VECTORS/2) {
        printf("%e ",float(surfel1->in_states_voxel_weight2[is]));
        if ((is+1)%6==0) printf("\n");
      }
      if ((N_LATTICE_VECTORS/2)%6!=0) printf("\n");
    }
  }
}

// Count number of voxel-surfel interactions on this processor
asINT32 ls_count_voxel_surfel_interactions()
{
  //CONDUCTION-CHECK - does this loop over ghost surfels as well? We do need the ghost surfels,
  // but we do not need the ghost UBLKs
  asINT32 interaction_count = 0;

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      if (surfel->is_conduction_surface()) {
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
        if (surfel->is_ghost())
          msg_internal_error("We should not be looping over ghost SURFELs");
#endif
        SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
        asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

        for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
          interaction_count += (asINT32)ublk_interaction->m_n_weight_sets;
        }
      }
    }
  }

  return interaction_count;
}

VOID finish_init_of_v2s_groups()
{
  // Think about add to chkpt queue later
}

/* Dynamics functions */

/*--------------------------------------------------------------------------*
 * do_dyn_v2s_advect and do_ghost_v2s_advect
 *
 * Handles voxel to surfel advection. For each v2s advect group, calls the 
 * v2s_advect method. 
 *--------------------------------------------------------------------------*/

VOID do_ghost_v2s_advect(SCALE scale,ACTIVE_SOLVER_MASK active_solver_mask,
			 ACTIVE_SOLVER_MASK even_active_solver_mask,
			 ACTIVE_SOLVER_MASK odd_active_solver_mask)
{
}
