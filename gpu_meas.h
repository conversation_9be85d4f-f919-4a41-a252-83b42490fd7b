#ifndef SIMENG_GPU_MEAS_H
#define SIMENG_GPU_MEAS_H

#if BUILD_GPU

#include "common_sp.h"
#include "lattice.h"
#include "meas_cell.h"
#include "gpu_ptr.h"
#include "timescale.h"

struct sMEAS_WINDOW_COLLECTION;

/** @defgroup GpuMeas Measurements on the GPU

  Measurements on the GPU require special attention for maximum performance.
  The measurements are collected on the device, and are only transferred to the
  CPU when it is time to output a frame. This is because the GPU-CPU
  interconnect between is quite slow, so we only want to do this when
  absolutely necessary. We try to mitigate this cost by using a separate
  comm thread stream as well.

  Each ublk and surfel has a set of measurement cells that they contribute too.
  (Or you can invert this thinking. The measurement cells are 'observers' of
  the ublks/surfels, if you want to think about it using the Observer pattern.)

  When ublks/surfels get merged into mblks and msfls, we have to merge the
  measurement cells too. As much as possible, we want measurements on the GPU
  to be repeatable and performant. This means avoiding atomic operations, which
  are not repeatable and can cause contention.

  sMSFL_MEAS_CELL_PTR and sMBLK_MEAS_CELL_PTR facilitate reduction operations
  that occur within a mblk/msfl.

  @{
*/

/** @brief Stores meas cell ptrs for each surfel in a megasurfel.

  In general, MEAS_CELL_PTRs are not stored in a 1-to-1 mapping of child surfel -> meas_cell_ptr.
*/
typedef struct sMSFL_MEAS_CELL_PTR
{
private:

  /** @brief The meas cell pointers used by this megasurfel.

    These are stored in a compact form. If multiple child surfels use the same
    meas cell, they share the same location in the array.
  */
  MEAS_CELL_PTR m_ptrs[N_SFLS_PER_MSFL];

  /** @brief The meas window the meas cell ptrs are associated with. */
  STP_MEAS_WINDOW_INDEX m_meas_window_index;

  /** @brief The mapping of child surfels to meas cell ptrs.

    If a child surfel does not contribute to a meas cell in this window, == -1.
  */
  sINT8 m_child_sfl_to_meas_cell[N_SFLS_PER_MSFL];

  /** @brief The number of meas cell ptrs - 1.

    The meas cell ptrs are stored between m_ptrs[0] and m_ptrs[m_max_meas_cell] (inclusive)
  */
  sINT8 m_max_meas_cell;

public:

  __HOST__ sMSFL_MEAS_CELL_PTR() {};

  __HOST__ sMSFL_MEAS_CELL_PTR(STP_MEAS_WINDOW_INDEX window_index) :
    m_ptrs{},
    m_meas_window_index(window_index),
    m_max_meas_cell{-1}
  {
    ccDOTIMES(i, N_SFLS_PER_MSFL) {
      m_child_sfl_to_meas_cell[i] = -1;
    }
  }

  __HOST__DEVICE__ STP_MEAS_WINDOW_INDEX window_index() const { return m_meas_window_index; }

  __HOST__DEVICE__ MEAS_CELL_PTR meas_cell_ptr(uINT8 child_sfl) const
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    return m_ptrs[child_sfl];
  }

  __HOST__DEVICE__ sINT8 child_sfl_to_meas_cell(uINT8 child_sfl) const
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    return m_child_sfl_to_meas_cell[child_sfl];
  }

  __HOST__ void set_child_sfl_to_meas_cell(sINT8 child_sfl, sINT8 idx)
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    m_child_sfl_to_meas_cell[child_sfl] = idx;
  }

  __HOST__ void set_meas_cell_idx(uINT8 child_sfl, STP_MEAS_CELL_INDEX meas_cell_idx)
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    m_ptrs[child_sfl].set_index(meas_cell_idx);
  }

  /** @brief This is only used for development windows */
  __HOST__ void set_meas_cell_ptr(uINT8 child_sfl, MEAS_CELL_PTR meas_cell_ptr)
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    m_ptrs[child_sfl] = meas_cell_ptr;
  }

  __HOST__ STP_MEAS_CELL_INDEX meas_cell_idx(uINT8 child_sfl) const
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    return m_ptrs[child_sfl].index();
  }

  __HOST__ void set_variables(uINT8 child_sfl, GPU::Ptr<MEAS_CELL_VAR> meas_cell_ptr)
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    m_ptrs[child_sfl].set_variables(meas_cell_ptr.get());
  }

  __HOST__ void mark_should_rotate_vector_to_grf(uINT8 child_sfl)
  {
    cassert(child_sfl < N_SFLS_PER_MSFL);
    m_ptrs[child_sfl].mark_should_rotate_vector_to_grf();
  }

  __HOST__DEVICE__ sINT8 max_meas_cell() const { return m_max_meas_cell; }

  __HOST__ void set_max_meas_cell();

  /** @brief Compacts the meas cell pointers down to get ready for the reduction */
  __HOST__ void sort_meas_cell_ptrs_for_reduction();

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sMSFL_MEAS_CELL_PTR* h_ptr,
              int n_meas_windows,
              bool lrf_is_rotating,
              const sMEAS_WINDOW_COLLECTION& windows);

} *MSFL_MEAS_CELL_PTR;

inline bool operator < (const sMSFL_MEAS_CELL_PTR& a, const sMSFL_MEAS_CELL_PTR& b)
{
  return a.window_index() < b.window_index();
}

/** @brief Stores meas cell ptrs for each ublk in a megaublk.

  In general, MEAS_CELL_PTRs are not stored in a 1-to-1 mapping of child ublk -> meas_cell_ptr.
  Here we work on the level of ublks, not voxels, which is different from sMSFL_MEAS_CELL_PTR.
*/
typedef struct sMBLK_MEAS_CELL_PTR
{
private:
  /** @brief The meas cell pointers used by the megablk 

    These are stored in a compact form. If multiple child ublks
    use the same meas cell, they share the same location in the array.
  */
  MEAS_CELL_PTR m_ptrs[N_VOXELS_8];

  /** @brief A voxel mask indicating which voxels to measure. */
  MBLK_VOXEL_MASK m_meas_voxel_mask;

  /** @brief The meas window the meas cell ptrs are associated with. */
  STP_MEAS_WINDOW_INDEX m_meas_window_index;


  /** @brief The mapping of child ublks to meas cell ptrs. 

    If a child ublk does not contribute to a meas cell in this window, == -1

  */
  sINT8 m_child_ublk_to_meas_cell[N_VOXELS_8];

  /** @brief The number of meas cell ptrs - 1.

    The meas cell ptrs are stored between m_ptrs[0] and m_ptrs[m_max_meas_cell] (inclusive)
  */
  sINT8 m_max_meas_cell;

public:

  __HOST__ sMBLK_MEAS_CELL_PTR() {};

  __HOST__ sMBLK_MEAS_CELL_PTR(STP_MEAS_WINDOW_INDEX window_index) :
    m_ptrs{},
    m_meas_voxel_mask{0},
    m_meas_window_index(window_index),
    m_max_meas_cell{-1}
  {
    ccDOTIMES(i,N_VOXELS_8) {
      m_child_ublk_to_meas_cell[i] = -1;
    }
  }

  __HOST__DEVICE__ STP_MEAS_WINDOW_INDEX window_index() const { return m_meas_window_index; }

  __HOST__DEVICE__ MBLK_VOXEL_MASK voxel_mask() const { return m_meas_voxel_mask; }

  __HOST__DEVICE__ VOXEL_MASK_8 voxel_mask(uINT8 child_ublk) const { return child_ublk_mask(m_meas_voxel_mask, child_ublk); }

  __HOST__DEVICE__ MEAS_CELL_PTR meas_cell_ptr(uINT8 child_ublk) const
  {
    cassert(child_ublk < N_VOXELS_8);
    return m_ptrs[child_ublk];
  }

  __HOST__DEVICE__ sINT8 child_ublk_to_meas_cell(sINT8 child_ublk) const
  {
    cassert(child_ublk < N_VOXELS_8);
    return m_child_ublk_to_meas_cell[child_ublk];
  }

  __HOST__ void set_child_ublk_to_meas_cell(sINT8 child_ublk,sINT8 idx)
  {
    cassert(child_ublk < N_VOXELS_8);
    m_child_ublk_to_meas_cell[child_ublk] = idx;
  }

  __HOST__ void set_voxel_mask(sINT32 child_ublk, VOXEL_MASK_8 child_voxel_mask);

  __HOST__ void set_meas_cell_idx(uINT8 child_ublk, STP_MEAS_CELL_INDEX meas_cell_idx)
  {
    cassert(child_ublk < N_VOXELS_8);
    m_ptrs[child_ublk].set_index(meas_cell_idx);
  }

  __HOST__ STP_MEAS_CELL_INDEX meas_cell_idx(uINT8 child_ublk) const
  {
    cassert(child_ublk < N_VOXELS_8);
    return m_ptrs[child_ublk].index();
  }

  __HOST__ void set_variables(uINT8 child_ublk, GPU::Ptr<MEAS_CELL_VAR> meas_cell_ptr)
  {
    cassert(child_ublk < N_VOXELS_8);
    m_ptrs[child_ublk].set_variables(meas_cell_ptr.get());
  }

  /** @brief This is only used for development windows */
  __HOST__ void set_meas_cell_ptr(uINT8 child_ublk, MEAS_CELL_PTR meas_cell_ptr)
  {
    cassert(child_ublk < N_VOXELS_8);
    m_ptrs[child_ublk] = meas_cell_ptr;
  }

  __HOST__ void mark_should_rotate_vector_to_grf(uINT8 child_ublk)
  {
    cassert(child_ublk < N_VOXELS_8);
    m_ptrs[child_ublk].mark_should_rotate_vector_to_grf();
  }

  __HOST__ void set_max_meas_cell();

  __HOST__DEVICE__ sINT8 max_meas_cell() const { return m_max_meas_cell; }

  /** @brief Compacts the meas cell ptrs down to get ready for reduction */
  __HOST__ void sort_meas_cell_ptrs_for_reduction();

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sMBLK_MEAS_CELL_PTR* h_ptr,
              SCALE scale,
              int n_meas_windows,
              bool lrf_is_rotating,
              const sMEAS_WINDOW_COLLECTION& windows);

} *MBLK_MEAS_CELL_PTR;

inline bool operator < (const sMBLK_MEAS_CELL_PTR& a, const sMBLK_MEAS_CELL_PTR& b)
{
  return a.window_index() < b.window_index();
}

namespace GPU {
  /** @brief The maximum number of standard deviation variables a window could possibly have.  */
  constexpr size_t N_STD_DEV_VARIABLES = 8;
}

/** @brief Stores only the necessary data from the window on the GPU.

  @note Perhaps most of this should be a base class for both meas window and this one?
        That would help prevent silly errors in the future.
*/
struct sON_DEVICE_WINDOW
{
  ACTIVE_SOLVER_MASK solver_mask;
  sINT8    meas_cell_scale;
  SRI_VARIABLE_TYPE * var_types;
  bool per_voxel_meas_p;
  bool is_composite;
  bool is_development;
  bool contains_std_dev_vars;
  bool calc_lambda2;
  sINT16 n_variables;
  uINT32 n_device_meas_cells;
  bool calc_htc_for_adb_walls;
  sFLOAT reference_point[3];
  MEAS_CELL_VAR * device_meas_cells;
  MEAS_CELL_VAR m_one_over_n_timesteps_since_clear;
  sMEAS_UPDATE_TIME current_update_time; 

  uINT32 std_dev_vars[GPU::N_STD_DEV_VARIABLES]; 
  uINT32 n_std_dev_vars;
 
  // development window info
  sdFLOAT  start_pt[3][3];  // first index is axis, second is coord
  sdFLOAT  dev_line_orientation[3][3];  // first index is axis, second is coord of a unit vector
  sdFLOAT  one_over_dev_win_segment;

  sFLOAT min_pressure;
  sFLOAT max_pressure;

  __DEVICE__ BOOLEAN is_per_voxel_for_scale(asINT32 voxel_scale)
  {
    return per_voxel_meas_p && (meas_cell_scale >= voxel_scale);
  }

  /** On the GPU, the measurement cells are transposed from the CPU */
  __DEVICE__ asINT32 meas_cell_var_inc() const 
  {
    return n_device_meas_cells;
  }

  __DEVICE__ MEAS_CELL_VAR one_over_n_timesteps_since_clear() 
  {
    return m_one_over_n_timesteps_since_clear;
  }

  __DEVICE__ asINT32 calculate_dev_win_extent_index(sdFLOAT centroid[3], uINT8 dev_axis)
  {
    sdFLOAT rlocal[3];
    rlocal[0] = centroid[0] - start_pt[dev_axis][0];
    rlocal[1] = centroid[1] - start_pt[dev_axis][1];
    rlocal[2] = centroid[2] - start_pt[dev_axis][2];
    return ((asINT32) (one_over_dev_win_segment * vdot(rlocal,dev_line_orientation[dev_axis])));
  }

  __DEVICE__ MEAS_CELL_VAR* meas_cell(STP_MEAS_CELL_INDEX i) 
  {
    return device_meas_cells + i;
  }

};

namespace GPU {
  void update_on_device_windows();
}

class sUBLK_MEAS_CELL_PTR;
class sSURFEL_MEAS_CELL_PTR;
class sMEAS_WINDOW;

/** @brief Builds megablock and megasurfel meas cell ptrs, while building up a GPU meas window for a given CPU meas window.

  Building a GPU window is done incrementally, as the ublks/surfels are processed.
  Each window constructs a derived class to be a proxy for itself.

  When building a mblk/msfl, each child ublk/child surfel should call
  add_ublk_to_device_meas_cells()/add_sfl_to_device_meas_cells(), respectively.

  Once all the children have been processed, calling append_mblk_meas_cells()/append_msfl_meas_cells()
  will add the created mblk/msfl meas cell ptrs to the provided vector. 

  Once all mblks & msfls have been processed, compute_host_side_info() will compute the complete picture
  of the GPU window.

  The base class covers both Fluid and Surface windows, so calling the wrong function for a given derived
  will result in an error.
*/

class cDEVICE_MEAS_WINDOW_BUILDER
{
public:
  cDEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent);
  virtual ~cDEVICE_MEAS_WINDOW_BUILDER() = 0;
  cDEVICE_MEAS_WINDOW_BUILDER(const cDEVICE_MEAS_WINDOW_BUILDER&) = delete;
  cDEVICE_MEAS_WINDOW_BUILDER(cDEVICE_MEAS_WINDOW_BUILDER&&) = delete;

  /** @brief Add a child ublk to the device window */
  [[nodiscard]] virtual int add_ublk_to_device_meas_cells(uINT8 child_ublk, 
                                                          SCALE scale, 
                                                          sUBLK_MEAS_CELL_PTR * const ublk_meas_cell_ptr, 
                                                          sUBLK_MEAS_CELL_PTR const * const end, 
                                                          bool lrf_is_rotating) 
  { 
    msg_internal_error("Cannot add ublk meas cell ptr"); 
  }
  
  /** @brief Add a child surfel to the device window */
  [[nodiscard]] virtual int add_sfl_to_device_meas_cells(uINT8 child_sfl, 
                                                         sSURFEL_MEAS_CELL_PTR * const sfl_meas_cell_ptr, 
                                                         sSURFEL_MEAS_CELL_PTR const * const end, 
                                                         bool lrf_is_rotating) 
  { 
    msg_internal_error("Cannot add surfel meas cell ptr"); 
  }

  /** @brief Once all child ublks in a megablk are processed, call this to get the created sMBLK_MEAS_CELL_PTRs */
  virtual void append_mblk_meas_cells(std::vector<sMBLK_MEAS_CELL_PTR>& mcps) { msg_internal_error("Cannot append mblk"); };

  /** @brief Once all child ublks in a megasurfel are processed, call this to get the created sMFSL_MEAS_CELL_PTRs */
  virtual void append_msfl_meas_cells(std::vector<sMSFL_MEAS_CELL_PTR>& mcps) { msg_internal_error("Cannot append msfl"); };

  /** @brief Compute the final host side info after all mblks/mfls are processeed */
  virtual void compute_host_side_info() = 0;

protected:
  sMEAS_WINDOW* m_parent;
};

using cDEVICE_MEAS_WINDOW_BUILDER_VEC = std::vector<std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER>>;


void set_msfl_meas_cell_ptrs(sMSFL& mega, const std::vector<sMSFL_MEAS_CELL_PTR>& msfl_meas_cell_ptrs);

class sH2D_EXPORT;
namespace GPU
{
  void init_device_windows(const sH2D_EXPORT& h_data);
}

/** @} */

#endif
#endif
