/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("physics.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */ 
#ifndef _RANDOM_PARTICLE_PROPERTIES_H
#define _RANDOM_PARTICLE_PROPERTIES_H

#include PDFS_H
#include "common_sp.h"

//static asINT32 ping_counter = 0;

//This is just a wrapper class for the RNG implementation in the PDFS component.
typedef class sRANDOM_PARTICLE_PROPERTIES {

 private:
  tPDF<sPARTICLE_VAR>* m_generators[sPDF::NUM_DISTRIBUTION_TYPES];
  
 public:
  ~sRANDOM_PARTICLE_PROPERTIES(){}
  sRANDOM_PARTICLE_PROPERTIES(){
    //Allocate one sPDF object for each distribution types.
    asINT32 num_dist_types = sPDF::NUM_DISTRIBUTION_TYPES;
    ccDOTIMES(dist_type_index, num_dist_types) {
      sPDF::ePDF_TYPE dist_type = (sPDF::ePDF_TYPE)dist_type_index;
      m_generators[dist_type_index] = new tPDF<sPARTICLE_VAR>(dist_type);
      m_generators[dist_type_index]->seed(my_proc_id + 1);       //Seed using SP index plus one so a seed of zero is not used.
    }
  }

  sPARTICLE_VAR random_number(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->random());
    // sPARTICLE_VAR val = m_generators[distribution]->random();
    // LOG_MSG("SURFEL_STENCILS").printf("Counter: %d, dist: %d, val: %.17g", ++ping_counter, distribution, val);
    // return val;
  }

  sPARTICLE_VAR random_number(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPARTICLE_VAR param3, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2, param3);
    return(m_generators[distribution]->random());
  }

  sPARTICLE_VAR pdf(sPARTICLE_VAR x, sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->pdf(x));
  }

  sPARTICLE_VAR pdf(sPARTICLE_VAR x, sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPARTICLE_VAR param3, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2, param3);
    return(m_generators[distribution]->pdf(x));
  }

  sPARTICLE_VAR cdf(sPARTICLE_VAR x, sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->cdf(x));
  }

  sPARTICLE_VAR third_order_moment(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->third_order_moment());
  }

  sPARTICLE_VAR truncated_third_order_moment(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPARTICLE_VAR param3, sPARTICLE_VAR param4, sPDF::ePDF_TYPE distribution) {
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->truncated_third_order_moment(param3, param4));
  }

  sPARTICLE_VAR expected_mean(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution){
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->expected_mean());
  }
  
  sPARTICLE_VAR expected_stddev(sPARTICLE_VAR param1, sPARTICLE_VAR param2, sPDF::ePDF_TYPE distribution){
    m_generators[distribution]->set_params(param1, param2);
    return(m_generators[distribution]->expected_stddev());
  }
  
  VOID ckpt();
  VOID ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  size_t ckpt_len();
  VOID read_ckpt();

  //some commonly used random nubmer sources:
  sPARTICLE_VAR uniform() {
    sPARTICLE_VAR range = 2.0;
    return(random_number(0.0, range, sPDF::UNIFORM_DISTRIBUTION )); //uniform on [-1,1]
  }

  sPARTICLE_VAR gaussian(sPARTICLE_VAR mean, sPARTICLE_VAR stddev) {
    m_generators[sPDF::GAUSSIAN_DISTRIBUTION]->set_params(mean, stddev);
    return(random_number(mean, stddev, sPDF::GAUSSIAN_DISTRIBUTION ));
  }

  //uniform integer [0, num-1]
  asINT32 uniform_integer(asINT32 num) {
    sPARTICLE_VAR float_result = random_number(0.5, 1.0, sPDF::UNIFORM_DISTRIBUTION);
    return std::min((asINT32)(float_result*num), num-1);
  }

} *RANDOM_PARTICLE_PROPERTIES;

extern RANDOM_PARTICLE_PROPERTIES g_random_particle_properties;



//Function to convert a LGI::eLGI_DISTRIBUTION_TYPE to an sPDF:ePDF_TYPE.
sPDF::ePDF_TYPE lgi_to_pdfs_distribution(LGI::eLGI_DISTRIBUTION_TYPE lgi_type);


#endif
