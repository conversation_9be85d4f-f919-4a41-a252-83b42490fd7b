/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_emitters.h"
#include "trajectory_meas.h"
#include "particle_sim_info.h"
#include "trajectory_window.h"

sSP_TRAJECTORY_ID_MAP g_trajectory_id_map;

sSP_TRAJECTORY_VERTEX_MANAGER sp_trajectory_vertex_manager;
sSP_TRAJECTORY_HITPOINT_MANAGER sp_trajectory_hitpoint_manager;
sSP_TRAJECTORY_STARTPOINT_MANAGER sp_trajectory_startpoint_manager;

BOOLEAN g_has_trajectory_windows = FALSE;

asINT32 sSP_TRAJECTORY_ID_MAP::count_total_pri_emitters()
{
  //If an emitter defined in the CDI file uses multiple geometries, the simulator actually creates one
  //independent emitter object (each with their own simualtor emitter id) for each peice of geometry.
  //This function finds the number of unique cdi emitter IDs in use in the simualtor so the
  //simulator's ID can be mapped back to the CDI's ID.

  const std::vector<PARTICLE_EMITTER_BASE> &emitters = g_particle_sim_info.emitters;
  asINT32 num_sim_emitters = emitters.size();
  if(num_sim_emitters == 0)
    return(1);   //Include onlt the ficticious "merged" emitter.

  std::vector<BOOLEAN8> is_emitter_id_used(num_sim_emitters, FALSE);
  asINT32 max_emitter_id = 0;
  asINT32 num_cdi_emitters = 0;

  ccDOTIMES(i, num_sim_emitters) {
    asINT32 emitter_cdi_id = emitters[i]->get_emitter_id();
    max_emitter_id = std::max(max_emitter_id, emitter_cdi_id);
    if (!is_emitter_id_used[emitter_cdi_id]) {
      is_emitter_id_used[emitter_cdi_id] = TRUE;
      num_cdi_emitters++;
    }
  }

  if (max_emitter_id + 1 != num_cdi_emitters)
    msg_internal_error("Emitter IDs are not contiguous.");

  //PRI requires an additional ficticious emitter to account for agglomerated
  //particles which could have parents from different real emitters.

  asINT32 num_pri_emitters = num_cdi_emitters + 1;
  return num_pri_emitters;
}

inline VOID sSP_TRAJECTORY_ID_MAP::remap_parcel_trajectory_id(PARCEL_STATE parcel)
{

   if(PARCEL_IS_INTERESTING(parcel)) {
     msg_print("parcel %d:%d with dynamics type %d is having its ID remapped at timestep %ld.",
               parcel->originating_sp,
               parcel->id,
               parcel->dynamics_type,
               g_timescale.m_time);
      }



  asINT32 meas_ts = parcel->first_measured_ts;
  asINT32 emi_id = abstract_emitter_id(parcel);
  asINT32 sp_id = parcel->first_measured_sp;

  if (meas_ts >= 0) {
    asINT32 trajectory_window = ffsll(parcel->visited_trajectory_windows) - 1; // ffsll returns the position of the first bit set
    if (trajectory_window >= 0
        && ((parcel->parcel_is_measurable_for_trajectory_window >> trajectory_window) & 1)) {
      if (is_timestep_in_map_range(meas_ts)) {
        parcel->order_in_measured_ts += global_base_id(meas_ts, sp_id, trajectory_window, emi_id);
        parcel->first_measured_ts = -1;
      }
      else if (is_timestep_before_map_range(meas_ts)) {
#if 0
        msg_internal_error("Failed to convert parcel trajectory ID from local to global: %ld %d %d",
                           g_timescale.m_time, meas_ts, current_start_timestep());
#else
        msg_warn("Failed to convert parcel trajectory ID from local (orig_sp:id %d:%d, window %d last_measured_ts %g order_in_measured_ts %d) to global: %ld %d %d",
                 parcel->originating_sp, parcel->id, trajectory_window, parcel->last_measured_t, parcel->order_in_measured_ts,
                 g_timescale.m_time, meas_ts, current_start_timestep());
#endif
        parcel->parcel_is_measurable_for_trajectory_window = 0;
      }
    }
  }
}

inline VOID sSP_TRAJECTORY_ID_MAP::remap_voxel_trajectory_ids(UBLK ublk, asINT32 voxel)
{
  if (ublk->fluid_like_voxel_mask.test(voxel)) {
    PARCEL_LIST fluid_parcel_list = ublk->p_data()->fluid_parcel_list[voxel];
    fluid_parcel_list->reset();
    while(!fluid_parcel_list->exhausted()) {
      PARCEL_STATE parcel = fluid_parcel_list->data();
      remap_parcel_trajectory_id(parcel);
      fluid_parcel_list->next();
    }
  }
}

inline VOID sSP_TRAJECTORY_ID_MAP::remap_surfel_trajectory_ids(SURFEL surfel)
{
  PARCEL_LIST parcel_list = surfel->p_data()->surface_parcel_list;
  parcel_list->reset();
  while(!parcel_list->exhausted()) {
    PARCEL_STATE parcel = parcel_list->data();
    remap_parcel_trajectory_id(parcel);
    parcel_list->next();
  }
}

VOID sSP_TRAJECTORY_ID_MAP::remap_parcels_on_surfels_of_type(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          if(surfel->is_even())
            continue;
          remap_surfel_trajectory_ids(surfel);
        }
        break;
      }
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected surfel group type %d in surfel parcel id remapping.",group->m_supertype);
      break;
    }
  }
}


// This must be called after receive_base_global_ids()
VOID sSP_TRAJECTORY_ID_MAP::remap_parcel_trajectory_ids() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        if (farblk->fluid_like_voxel_mask.test(voxel))
          remap_voxel_trajectory_ids(farblk, voxel);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      ccDOTIMES(voxel,ubFLOAT::N_VOXELS ) {
        if (nearblk->fluid_like_voxel_mask.test(voxel))
          remap_voxel_trajectory_ids(nearblk, voxel);
      }
    }

#if 0 //Remember to remap film parcels too if trajectory measurements for film are ever enabled.
    remap_parcels_on_surfels_of_type(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
    remap_parcels_on_surfels_of_type(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
    remap_parcels_on_surfels_of_type(INTERIOR_SURFEL_BASE_GROUP_TYPE, scale);
#endif
  }

  //Also process the parcels on MLRF surfels but only for scales
  //coarser than the coarsest active scale since the parcel lists of
  //these surfels may contain particles that collided the last time
  //those scales were active but have not yet been re-emitted into the
  //ublks from the matching MLRF surfel on the opposite frame.

  //Collision detection for MLRF surfels of the active scales has not
  //occurred yet this timestep so no parcels exist on those surfels
  //and they can be skipped.

  //Also, scales coarse enough that their previous active time scale
  //was before the last remapping can also be skipped since they have
  //also not collected any parcels since the last remapping but this
  //optimization is not implemented here.

  //This remapping must occur before the posting of mlrf_pre_dyn send
  //requests because, at that point, the particles have been packed
  //into their send buffers and deallocated.  They would arrive at the
  //destination without their global IDs and trigger
  //dropped-measurement errors the next time their trajectory is
  //measured.

  SCALE finest_inactive_scale = std::max(COARSEST_SCALE, g_timescale.coarsest_active_scale() - 1); //This relies on a coarser scale being smaller than a finer scale.
#if 0
  if(!my_proc_id)
    msg_print("Remapping parcels on MLRF surfels of scales from coarsest inactive scale %d up to finest inactive scale %d at timestep %ld (finest scale is %d).",
              COARSEST_SCALE,
              finest_inactive_scale,
              g_timescale.m_time,
              FINEST_SCALE);
#endif
  DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_inactive_scale, COARSEST_SCALE) {
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL mlrf_surfel = group->quantum(nth_surfel)->mlrf_surfel();
        remap_surfel_trajectory_ids(mlrf_surfel);
      }
    }
  }
  m_current_start_timestep += TRAJECTORY_ID_MAP_SEND_PERIOD;
}
