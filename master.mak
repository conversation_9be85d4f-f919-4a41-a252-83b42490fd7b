# ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "78")
##############################################################################
### PowerFLOW Simulator Simulation Process                                 ###
###                                                                        ###
### Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.         ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239;                     ###
###        6,089,744; 7,558,714                                            ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Dassault Systemes Americas Corp. ###
### and contains its confidential trade secrets.  Use, examination, copying, ###
### transfer and disclosure to others, in whole or in part, are prohibited ###
### except with the express prior written consent of Dassault Systemes     ###
### Americas Corp.                                                         ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78")
#--------------------------------------------------
# Included files and libraries
#--------------------------------------------------

include $R/registry.mak

VMAKE_WARNLEVEL=low
INLINE_DEPS = 1
include $(MAKEINCLUDE_DIR)/$T/makeinclude.mak
include $(MAKEINCLUDE_DIR)/simulator_flags.mak

export CCACHE_BASEDIR=$(SIMENG_DIR)
export CCACHE_CONFIGPATH=/opt/ccache-3.7.9/ccache.conf

#disable this warning: "warning The entire set of C++ language bindings have been deprecated in mpi 2.2"
#DO NOT USE ffast-math OPTION, IT LEADS TO SIM ERRORS FOR SOME SIMULATIONS
CC_OPT += -Wno-deprecated -fdenormal-fp-math=positive-zero -Wuninitialized -Wsometimes-uninitialized #-Wno-parentheses

CC_DBG += -O0 -Wno-deprecated -Wuninitialized -Wsometimes-uninitialized -fdenormal-fp-math=positive-zero #-Wno-parentheses

ifdef USE_NVCC

NVCC_CLANG_OPTS += -Wno-invalid-offsetof

# It's easy to turn off warnings in host compilation
# but for device compilation its a little tricky
# This method uses undocumented NVCC flags, use
#
# -Xcudafe="--display_error_number"
#
# to show numbers for different device compilation warnings
# 1427 - Winvalid-offset

NVCC_COMPILE_FLAGS +=  --fmad=true --ftz=true
NVCC_COMPILE_FLAGS += -Xcudafe "--diag_suppress=1427" -Xcudafe "--diag_suppress=177"  -Xcudafe "--diag_suppress=550"
# NVCC_COMPILE_FLAGS += -lineinfo

ifdef NVCC_REAL_ARCH
NVCC_COMPILE_FLAGS += -Xptxas=-v
endif

endif

include $(PLATFORM_DIR)/export.mak
include $(TPI_DIR)/export.mak
include $(SCALAR_DIR)/export.mak
include $(MSGERR_DIR)/export.mak
include $(MALLOC_DIR)/export.mak
include $(LOOP_DIR)/export.mak
include $(DEBUG_DIR)/export.mak
include $(VHASH_DIR)/export.mak
include $(THASH_DIR)/export.mak
include $(SORT_DIR)/export.mak
include $(AUDIT_DIR)/export.mak
include $(XARRAY_DIR)/export.mak
include $(EARRAY_DIR)/export.mak
include $(TEARRAY_DIR)/export.mak
include $(TXARRAY_DIR)/export.mak
include $(JOBCTL_DIR)/export.mak
include $(EXATIME_DIR)/export.mak
include $(EXALIC_DIR)/export.mak
include $(EXPRLANG_DIR)/export.mak
include $(UNITS_DIR)/export.mak
include $(ESTRING_DIR)/export.mak
include $(BG_DIR)/export.mak
include $(G1_DIR)/export.mak
include $(G2_DIR)/export.mak
include $(G3_DIR)/export.mak
include $(FANTABLE_DIR)/export.mak
include $(LGI_DIR)/export.mak
include $(SRI_DIR)/export.mak
include $(SIMUTILS_DIR)/export.mak
include $(CDI_DIR)/export.mak
include $(PRI_DIR)/export.mak
include $(HDF5_DIR)/export.mak
include $(CIO_DIR)/export.mak
include $(GRID_DIR)/export.mak
include $(PHYSTYPES_DIR)/export.mak
include $(CP_SP_LIB_DIR)/export.mak
include $(PF_COMM_DIR)/export.mak
include $(PF_LOG_DIR)/export.mak
include $(VMEM_DIR)/export.mak
include $(VMEM_VECTOR_DIR)/export.mak
include $(XNEW_DIR)/export.mak
include $(CCUTILS_DIR)/export.mak
include $(FILE_ADAPTER_DIR)/export.mak
include $(MKL_DIR)/export.mak
include $(XRAND_DIR)/export.mak
include $(CIPHER_DIR)/export.mak
include $(TURB_SYNTH_DIR)/export.mak
include $(FMT_DIR)/export.mak
include $(DO_ACROSS_DIR)/export.mak
include $(BREP_DIR)/export.mak
include $(BAGS_DIR)/export.mak
include $(PQ_DIR)/export.mak
include $(NERO_CGAL_DIR)/export.mak
include $(INTERP_DIR)/export.mak
include $(POOLS_DIR)/export.mak
include $(BITMAP_DIR)/export.mak
include $(FGEOM_DIR)/export.mak
include $(DLIST_DIR)/export.mak
include $(THASH_DIR)/export.mak

include $(PHYSICS_DIR)/export.mak
include $(SIMENG_DIR)/export.mak
include $(INTERP_DIR)/export.mak
include $(PAGED_BITMAP_DIR)/export.mak
include $(XRAND_DIR)/export.mak
include $(PDFS_DIR)/export.mak
include $(SIMSIZES_DIR)/export.mak
include $(DENSOLIB_DIR)/export.mak
include $(SMEM_DIR)/export.mak
include $(TIRE_DIR)/export.mak
include $(PARALLEL_IO_DIR)/export.mak
include $(GTUTIL_DIR)/export.mak
include $(EIGEN_DIR)/export.mak
ifndef USE_NVCC
include $(PETSC_DIR)/export.mak
endif

ifdef BUILD_D19_LATTICE
ADVECT_D_FLAGS = -DBOX_ADVECT_NEIGHBORS_2D=\"$T/box_advect_d19_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_3D=\"$T/box_advect_d19_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_2D=\"$T/swap_advect_d19_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_3D=\"$T/swap_advect_d19_3d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_2D=\"$T/box_advect_avx_d19_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_3D=\"$T/box_advect_avx_d19_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_2D=\"$T/swap_advect_avx_d19_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_3D=\"$T/swap_advect_avx_d19_3d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_2D=\"$T/box_split_advect_d19_2d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_3D=\"$T/box_split_advect_d19_3d.cc\"                  
endif

ifdef BUILD_5G_LATTICE
ADVECT_D_FLAGS = -DBOX_ADVECT_NEIGHBORS_2D=\"$T/box_advect_d19_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_3D=\"$T/box_advect_d19_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_2D=\"$T/swap_advect_d19_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_3D=\"$T/swap_advect_d19_3d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_2D=\"$T/box_advect_avx_d19_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_3D=\"$T/box_advect_avx_d19_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_2D=\"$T/swap_advect_avx_d19_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_3D=\"$T/swap_advect_avx_d19_3d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_2D=\"$T/box_split_advect_d19_2d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_3D=\"$T/box_split_advect_d19_3d.cc\"
endif

ifdef BUILD_D39_LATTICE
ADVECT_D_FLAGS = -DBOX_ADVECT_NEIGHBORS_2D=\"$T/box_advect_d39_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_3D=\"$T/box_advect_d39_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_2D=\"$T/swap_advect_d39_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_3D=\"$T/swap_advect_d39_3d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_2D=\"$T/box_advect_avx_d39_2d.cc\" \
                 -DBOX_ADVECT_NEIGHBORS_AVX_3D=\"$T/box_advect_avx_d39_3d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_2D=\"$T/swap_advect_avx_d39_2d.cc\" \
                 -DBOX_SWAP_NEIGHBORS_AVX_3D=\"$T/swap_advect_avx_d39_3d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_2D=\"$T/box_split_advect_d39_2d.cc\" \
                 -DBOX_SPLIT_ADVECT_NEIGHBORS_3D=\"$T/box_split_advect_d39_3d.cc\"
endif

GENERIC_D_FLAGS = $(GRID_D) $(SRI_D) $(PLATFORM_D) $(TPI_COMMON_D) $(SCALAR_D) \
	          $(MSGERR_D) $(AUDIT_D) $(LOOP_D) $(XNEW_D) $(MALLOC_D) $(DEBUG_D) \
		  $(LGI_D) $(CIO_D) $(SIMUTILS_D) $(CDI_D) $(PRI_D) \
		  $(SIMENG_D) $(INTERP_D) $(VHASH_D) $(THASH_D) $(SORT_D) \
		  $(XARRAY_D) $(EARRAY_D) $(TEARRAY_D) $(PHYSTYPES_D) $(PHYSTYPES_GLOBALS_D) $(SP_D) \
		  $(DISC_INTERCOMM_D) $(PHYSICS_EXPORTS_D) $(JOBCTL_SERVER_D) \
		  $(EXALIC_D) $(EXATIME_D) $(CCUTILS_D) $(CRAYULIB_D) \
		  $(CRAYOLA_D) $(MDU_OBJECT_D)  $(PHYSICS_D) $(EXPRLANG_D) \
		  $(PAGED_BITMAP_D) $(MKL_FFTW_D) $(CIPHER_D) $(XRAND_D) \
		  $(TURB_SYNTH_D) $(UNITS_D) $(ESTRING_D) $(BG_D) $(G3_D) \
		  $(G2_D) $(G1_D) $(FANTABLE_D) $(VMEM_VECTOR_D) $(VMEM_D) \
		  $(SIMSIZES_SHARED_D) $(FILE_ADAPTER_D) $(DENSOLIB_D) \
	          -DSOLVER_VERSION=$(SOLVER_VERSION) \
	          -DSP_VERSION=\"$(COMPONENT_VERSION)\" \
		  -DPHYSICS_VERSION=\"$(PHYSICS_VERSION)\"\
		  -DPRODUCT_VERSION=\"$(PRODUCT_VERSION)\" \
		  $(PDFS_D) $(SMEM_D) $(FMT_D) $(PF_LOG_D) $(TIRE_D) $(DIERCKX_D)

SHELL_STENCIL_D_FLAGS = $(BREP_D) $(BREP_CC_D) $(BAGS_D) $(BAGS_CC_D) $(PQ_D) $(NERO_CGAL_D) \
                        $(FGEOM_D) $(POOLS_D) $(BITMAP_D) $(DLIST_D) $(TXARRAY_D) $(DO_ACROSS_D)
                        
I_FLAGS = $(HDF5_I) $(PARALLEL_IO_I)
I_FLAGS_NOMPI = $(HDF5_NOMPI_I)

# gpu files do not use parallel_io yet.
ifdef USE_NVCC
NVCC_INCLUDE +=	$(HDF5_NOMPI_I)
endif

ifdef SGI_MPI
L_FLAGS_GLIBC = -L/opt/glibc-2.28/lib
else
L_FLAGS_GLIBC =
endif

#--------------------------------------------------
# File definitions
#--------------------------------------------------

COPYRIGHT_FILE = simeng.copyright
CREATION_YEAR = 1993

OTHER_SOURCES = gen_advect.cc \
                box_advect_gen_headers.cc

GENERIC_INC_FILES =	sim.h                   	\
			simeng.h                	\
			common_sp.h                	\
			lattice.h               	\
			seed_sp.h                  	\
			mirror.h                	\
			voxel_dyn_sp.h             	\
			quantum.h               	\
			shob.h                  	\
      surface_shob.h\
			ublk.h                  	\
			surfel.h                	\
			sampling_surfel.h       	\
			ublk_neighbors.h          	\
			group.h                 	\
			shob_dyn.h              	\
			surfel_dyn_sp.h            	\
			bsurfel.h			\
			bsurfel_body_force.h		\
			bsurfel_comm.h			\
			bsurfel_dyn_meas.h		\
			bsurfel_dyn_sp.h		\
			bsurfel_table.h			\
			bsurfel_ublk_neighbor_info.h	\
			bsurfel_util.h           	\
			bsurfel_tire.h           	\
			v.h                        	\
			random.h                	\
			advect.h 			\
			surfel_advect_sp.h         	\
			thread_run.h                   	\
			ckpt.h                  	\
			sp_timers.h             	\
			boltz_diags.h           	\
			event_queue_event.h     	\
			async_events.h          	\
			event_queue.h           	\
			eqns.h                  	\
			fan.h                   	\
			fan_comm.h								\
			face_polygons.h                 \
			slrf.h                  	\
			mlrf.h                  	\
			mlrf_comm.h                  	\
			mlrf_depots.h           	\
			vectorization_support.h 	\
			forward_declarations.h  	\
			simerr.h                	\
			fset.h                  	\
			phys_type_map.h         	\
			dgf_reader_sp.h            	\
			shob_groups.h      	        \
			ublk_table.h            	\
			surfel_table.h          	\
			parse_shob_descs.h      	\
			meas.h 				\
			comm_compression.h              \
			lb_solver_data.h 		\
			t_solver_data.h 		\
			conduction_data.h 		\
			conduction_contact_averaging.h  \
			turb_solver_data.h 		\
			isurfel_dyn_sp.h 		\
			box_advect.h          		\
			timescale.h           		\
			status.h           		\
			sleep.h           		\
			comm_groups.h                  	\
			strands.h			\
			strand_mgr.h			\
			surfel_process_control.h	\
      parcel_list.h                   \
			surfel_vertices.h		\
      particle_solver_data.h          \
      film_solver_stencils.h          \
      film_comm.h  		        \
      particle_emitter_configurations.h \
      particle_emitters.h             \
      particle_comm.h   	        \
      particle_materials.h            \
      particle_emitter_geometry.h     \
      emitter_geometry.h              \
      virtual_wipers.h                \
      particle_sim.h                  \
      particle_sim_info.h             \
      trajectory_meas.h               \
      trajectory_window.h             \
			read_pm_lgi_records.h		\
			particle_random_properties.h	\
      shob_octree.h                   \
      mc_data_5g.h                    \
      eos.h                           \
			gradient_5g.h                   \
			turb_synth_info.h               \
			logging.h			\
      particle_random_properties.h    \
			ublk_surfel_offset_table.h \
      particle_mlrf.h \
      particle_slrf.h \
      transient_boundary_seeding.h \
      particle_meas_dcache.h \
      ublk_solver_data_layout.h \
			ublk_box_tree.h \
			ublk_tree.h \
			exprlang_utilities.h \
			gpu_surfel_bc_values.h \
      wsurfel_comm.h \
      ublk_solver_data_layout.h \
      radiation.h \
      radiation_comm.h \
      radiation_phy.h \
      implicit_shell_solver.h \
      implicit_solid_solver.h


GENERIC_CC_FILES =	exa_sim_sp.cc           \
			sim.cc                  \
			common_sp.cc            \
			lattice.cc              \
			seed_sp.cc              \
			mirror.cc               \
			voxel_dyn_sp.cc         \
			fset.cc                 \
			shob.cc                 \
			ublk_neighbors.cc       \
			group.cc                \
			random.cc               \
			surfel_dyn_sp.cc        \
			bsurfel.cc		\
			bsurfel_comm.cc		\
			bsurfel_dyn_meas.cc	\
			bsurfel_dyn_sp.cc	\
			bsurfel_strands.cc	\
			bsurfel_table.cc	\
			bsurfel_util.cc		\
			bsurfel_tire.cc		\
			vr.cc                   \
			surfel_advect_sp.cc     \
			ckpt.cc                 \
			sp_timers.cc            \
			sp_timer_counters.cc    \
			boltz_diags.cc          \
			async_events.cc         \
			event_queue.cc          \
			eqns.cc                 \
			fan.cc                  \
			face_polygons.cc        \
			slrf.cc                 \
			mlrf.cc                 \
			mlrf_comm.cc            \
			vectorization_support.cc\
			simerr.cc               \
			phys_type_map.cc        \
			dgf_reader_sp.cc        \
			shob_groups.cc          \
			ublk_table.cc           \
			surfel_table.cc         \
			parse_shob_descs.cc     \
			meas.cc                 \
			comm_compression.cc     \
			sampling_surfel.cc      \
			lb_solver_data.cc	\
                        t_solver_data.cc        \
                        conduction_data.cc      \
												conduction_contact_averaging.cc  \
                        turb_solver_data.cc     \
			isurfel_dyn_sp.cc	\
      surfel.cc               \
      ublk.cc                 \
			advect.cc               \
      scalar_data.cc          \
      mc_data_5g.cc           \
      eos.cc                  \
      gradient_5g.cc          \
      turb_synth_info.cc      \
			box_advect.cc       	\
			box_advect_split.cc     \
			swap_advect.cc       	\
			gather_advect.cc       	\
			timescale.cc        	\
			status.cc        	\
			sleep.cc        	\
			run_threads.cc        	\
			sync_threads.cc        \
			comm_utils.cc       \
			comm_thread.cc      \
      comm_groups.cc          \
      thread_meas.cc          \
			strand_mgr.cc		\
			far_shobs_strands.cc    \
			particle_model_strands.cc \
			time_update_strand.cc    \
			sliding_mesh_strands.cc \
			surfel_process_control.cc \
			gpu_surfel_process_control.cc \
			ublk_process_control.cc \
			near_shobs_strands.cc   \
			gpu_surfel_bc_values.cc \
			surfel_vertices.cc \
      particle_solver_data.cc \
      film_solver_stencils.cc \
      film_comm.cc	        \
      particle_emitter_configurations.cc \
      particle_emitters.cc    \
      particle_materials.cc   \
      particle_emitter_geometry.cc \
      virtual_wipers.cc       \
      particle_sim.cc         \
      particle_comm.cc        \
      particle_meas.cc        \
      particle_sim_info.cc    \
      trajectory_meas.cc      \
      trajectory_window.cc    \
      gradient_5g.cc          \
			logging.cc		\
      read_pm_lgi_records.cc  \
      particle_random_properties.cc\
      ublk_surfel_offset_table.cc \
      data_offset_table_opts.cc \
      box_advect_gen_headers.cc \
      particle_mlrf.cc \
      particle_slrf.cc \
      surfel_stencils.cc \
	  conduction_solver_stencils.cc \
	  conduction_shell_edge.cc \
      transient_boundary_seeding.cc \
      particle_meas_dcache.cc     \
      porous_rock.cc \
      wsurfel_comm.cc \
      radiation.cc \
      radiation_comm.cc \
      radiation_phy.cc \
      implicit_shell_solver.cc \
      implicit_solid_solver.cc \
      gpu_host_init.cc \
      gpu_surfel_interactions.cc \
      gpu_split.cc \
      particle_meas_dcache.cc \
			gpu_advect_interactions.cu \
			gpu_comm_groups.cu \
			gpu_copy_to_device.cu \
			gpu_globals.cu \
			gpu_init_shobs.cu \
			gpu_meas.cu \
			gpu_ptr.cu \
			gpu_swap_advect.cu \
			gpu_ublk_dynamics.cu \
			gpu_advect_interactions.hcu \
			gpu_globals.hcu \
			gpu_shobs.hcu \
			gpu_sim.hcu \
			gpu_swap_advect.hcu \
			ublk_box_tree.cc

CC_FILES = $(GENERIC_CC_FILES)

GENERIC_OBJ_FILES =	exa_sim_sp.o            \
			common_sp.o             \
			lattice.o               \
			mirror.o                \
			voxel_dyn_sp.o          \
			fset.o                  \
			shob.o                  \
			ublk_neighbors.o        \
			group.o                 \
			random.o                \
			surfel_dyn_sp.o         \
			bsurfel.o		\
			bsurfel_comm.o		\
			bsurfel_dyn_meas.o	\
			bsurfel_dyn_sp.o	\
			bsurfel_strands.o	\
			bsurfel_table.o		\
			bsurfel_util.o		\
			bsurfel_tire.o		\
			vr.o                    \
			surfel_advect_sp.o      \
			mme_ckpt.o              \
			ckpt.o                  \
			sp_timers.o             \
			sp_timer_counters.o     \
			boltz_diags.o           \
			async_events.o          \
			event_queue.o           \
			eqns.o                  \
			fan.o                   \
			face_polygons.o         \
			slrf.o                  \
			mlrf_comm.o             \
			vectorization_support.o \
			simerr.o                \
			phys_type_map.o         \
			dgf_reader_sp.o         \
			shob_groups.o     	\
			ublk_table.o            \
			surfel_table.o          \
			parse_shob_descs.o      \
      meas.o 			\
      comm_compression.o      \
			sampling_surfel.o  	\
			lb_solver_data.o 	\
			t_solver_data.o		\
			conduction_data.o	\
			conduction_contact_averaging.o \
			turb_solver_data.o	\
			isurfel_dyn_sp.o	\
			box_advect.o		\
			box_advect_split.o      \
			swap_advect.o		\
			gather_advect.o		\
			surfel.o 		\
			ublk.o 			\
      scalar_data.o           \
		  mc_data_5g.o            \
			surfel_vertices.o       \
      particle_solver_data.o  \
      film_solver_stencils.o  \
      film_comm.o  		\
      particle_emitter_configurations.o \
      particle_emitters.o     \
      particle_materials.o    \
      particle_emitter_geometry.o \
      virtual_wipers.o        \
      particle_sim.o          \
      particle_comm.o         \
      particle_meas.o         \
      particle_sim_info.o     \
      trajectory_meas.o       \
      trajectory_window.o     \
      eos.o                   \
      gradient_5g.o           \
      turb_synth_info.o	\
      read_pm_lgi_records.o   \
      particle_random_properties.o \
			timescale.o		 \
			status.o                 \
			sleep.o                  \
			run_threads.o            \
			sync_threads.o     	 \
			comm_utils.o        	 \
			comm_thread.o        	 \
			comm_groups.o		 \
			thread_meas.o		 \
			strand_mgr.o	 	 \
			far_shobs_strands.o      \
			particle_model_strands.o \
			time_update_strand.o	 \
			surfel_process_control.o \
			ublk_process_control.o   \
			sliding_mesh_strands.o   \
			near_shobs_strands.o	 \
			logging.o \
			ublk_surfel_offset_table.o \
			data_offset_table_opts.o \
			box_advect_gen_headers.o \
      particle_mlrf.o \
      particle_slrf.o \
      surfel_stencils.o \
      conduction_solver_stencils.o \
      conduction_shell_edge.o \
      transient_boundary_seeding.o \
      particle_meas_dcache.o     \
      porous_rock.o \
      ublk_box_tree.o \
      wsurfel_comm.o \
      radiation.o \
      radiation_comm.o \
      radiation_strands.o \
      radiation_phy.o \
      implicit_shell_solver.o \
      implicit_solid_solver.o

ifdef BUILD_AVX
ifndef BUILD_DOUBLE_PRECISION
GENERIC_OBJ_FILES := $(GENERIC_OBJ_FILES) \
		  swap_advect_avx2.o \
		  gather_advect_avx2.o
endif
endif

#--------------------------------------------------
# Some object files are common to both GPU and non GPU
# builds. If its a non GPU build, we just add the list
# of object files to the generic list
#--------------------------------------------------

NVCC_CPP_OBJ_FILES = seed_sp.o \
                     ublk_solver_data_layout.o \
                     advect.o \
                     sim.o \
                     mlrf.o

ifdef USE_NVCC

GENERIC_OBJ_FILES += gpu_host_init.o \
		     						 gpu_surfel_interactions.o \
                     gpu_split.o \
                     gpu_print_mega_shobs.o \
		     						 gpu_surfel_process_control.o 

$(NVCC_CPP_OBJ_FILES) : %.o : ../%.cc
	$(CCJSON) $(NVCC) $(NVCC_COMPILE_FLAGS) -dc $(abspath $<) -o $@

NVCC_ARCHIVES = $(PHYSICS_D19_NVCC_A) $(G3_A) $(G2_A) $(G1_A)

else

GENERIC_OBJ_FILES += $(NVCC_CPP_OBJ_FILES)

endif
#--------------------------------------------------

I_FLAGS += -I$(EIGEN_I) $(PETSC_I)

RNG_TESTS_OBJ_FILES = rng_tests.o particle_random_properties.o
RNG_TESTS_LIBS_A = $(XRAND_A) $(PDFS_A) $(MSGERR_A) $(LGI_A) $(XNEW_A) $(MALLOC_A) $(PLATFORM_A)

# Libraries for brep used by shell stencils
SHELL_STENCIL_LIBS = $(BAGS_A) $(NERO_CGAL_A) $(BREP_A) $(POOLS_A) $(BITMAP_A) \
            $(FGEOM_A) $(DLIST_A) $(THASH_A)

GENERIC_LIBS_A =  $(INTERP_A) $(LGI_A) $(CDI_A) $(EXPRLANG_A) $(UNITS_A) \
      $(ESTRING_A) $(VHASH_A) $(THASH_A) $(JOBCTL_SERVER_A) $(CCUTILS_A) \
      $(EXATIME_A) $(SHELL_STENCIL_LIBS) $(VMEM_A) $(TURB_SYNTH_A) $(CIPHER_A) \
		  $(XRAND_A) $(FANTABLE_A) $(BG_A) $(G3_A) $(G2_A) $(G1_A) \
		  $(XARRAY_A) $(EARRAY_A) $(SORT_A) $(EXALIC_A) $(SMEM_A) $(PLATFORM_A) $(MALLOC_A) \
		  $(MSGERR_A) $(XNEW_A) $(PAGED_BITMAP_A) $(XRAND_A) $(PDFS_A) \
		  $(PHYSTYPES_A) $(SIMUTILS_A) $(FMT_A) $(TIRE_A) $(PETSC_A)

ifdef BUILD_D19_LATTICE
  LATTICE_D_FLAGS=-DBUILD_D19_LATTICE=1
  LIBS = $(PHYSICS_D19_A) $(GENERIC_LIBS_A)
endif
ifdef BUILD_5G_LATTICE
  LATTICE_D_FLAGS=-DBUILD_5G_LATTICE=1
  LIBS = $(PHYSICS_5G_A) $(GENERIC_LIBS_A)
endif
ifdef BUILD_D39_LATTICE
  LATTICE_D_FLAGS=-DBUILD_D39_LATTICE=1
  LIBS = $(PHYSICS_D39_A) $(GENERIC_LIBS_A)
endif

LATTICE_DP_FLAGS = -DBUILD_DOUBLE_PRECISION=0
ifdef BUILD_DOUBLE_PRECISION
    LATTICE_DP_FLAGS = -DBUILD_DOUBLE_PRECISION=1
endif

ifdef USE_NVCC
$(CUDA_DEVICE_LINKED_OBJ_FILES) : $(NVCC_ARCHIVES)
endif

INTEL_LIBS=-L$(INTEL_LIB_DIR)/amd64_gcc9

#CP_SP_LIB (which declares SP_A) depends on DEBUG_A and PLATFORM_A
SIM_EXTRA_LIBS_A = $(DEBUG_A) $(PLATFORM_A) $(PARALLEL_IO_A) $(HDF5_SO)
SIM_EXTRA_LIBS = $(subst _dp,,$(subst _d34,,$(subst _d39,,$(subst _5g,,$(SP_A))))) $(SIM_EXTRA_LIBS_A)
SIM_LINKOPTS = $(INTEL_LIBS) $(MKL_SHARED_LIBS) $(TARGET_LIBS) -lm -lpthread  -lrt -lsvml -lirc -lrt -limf

#--------------------------------------------------
# Programs
#--------------------------------------------------

D_FLAGS           = $(GENERIC_D_FLAGS) $(SHELL_STENCIL_D_FLAGS) $(ADVECT_D_FLAGS) $(TARGET_D_FLAGS)


#--------------------------------------------------
# Derived variables
#--------------------------------------------------

SRC_FILES = master.mak export.mak $(INC_FILES) \
	    $(OTHER_SOURCES) $(CC_FILES)

#--------------------------------------------------
# Sim Sizes Lib
# These libraries contain functions to determine sizes
# of various components of a simulation as far as the
# Simulator is concerned. They are linked against by
# the Decomposer and Discretizer.
#--------------------------------------------------
ifdef BUILD_DOUBLE_PRECISION
SIMENG_SIMSIZES_LIB_D19 = simulator_simsizes_lib_d19_dp.a
SIMENG_SIMSIZES_LIB_D39 = simulator_simsizes_lib_d39_dp.a
SIMENG_SIMSIZES_LIB_D5g = simulator_simsizes_lib_d5g_dp.a
else
SIMENG_SIMSIZES_LIB_D19 = simulator_simsizes_lib_d19_sp.a
SIMENG_SIMSIZES_LIB_D39 = simulator_simsizes_lib_d39_sp.a
SIMENG_SIMSIZES_LIB_D5g = simulator_simsizes_lib_d5g_sp.a
endif

ifdef BUILD_D19_LATTICE
    SIMENG_SIMSIZES_LIB = $(SIMENG_SIMSIZES_LIB_D19)
endif
ifdef BUILD_D39_LATTICE
    SIMENG_SIMSIZES_LIB = $(SIMENG_SIMSIZES_LIB_D39)
endif
ifdef BUILD_5G_LATTICE
    SIMENG_SIMSIZES_LIB = $(SIMENG_SIMSIZES_LIB_D5g)
endif

#--------------------------------------------------
# Compilation Info
#--------------------------------------------------
ALL = exa_sim_sp

ifndef USE_NVCC
ALL += $(SIMENG_SIMSIZES_LIB) test_sim_sizes
endif

all:	$(ALL)

clean:
	'rm' -fR `'ls' | egrep -v "Makefile"`
	
#------------------------------------------------------------------------------
# Special dependencies
#------------------------------------------------------------------------------

# This must be recompiled when the registry changes to get the correct registry
# number.
exa_sim_sp.o: $(R)/registry.mak

# These are used for particle modeling tests.
rng_tests:	$(RNG_TESTS_OBJ_FILES)
		$(CCC_WITHOUT_MPI) -o rng_tests $(STD_FLAG) $(CCFLAGS) $(RNG_TESTS_OBJ_FILES) $(RNG_TESTS_LIBS_A) -lm

rng_tests.o:	../rng_tests.cc
		$(CCC) -c $(STD_FLAG) $(CCFLAGS) ../rng_tests.cc -o rng_tests.o 

# USER_CCFLAGS+=-fsanitize=memory -fno-omit-frame-pointer -g -fsanitize-memory-track-origins=2 -fsanitize-blacklist=../blacklist.txt -mllvm -msan-keep-going=1
# USER_CCFLAGS+=-fsanitize=memory -fno-omit-frame-pointer -fsanitize-blacklist=../blacklist.txt -mllvm -msan-keep-going=1
#USER_CCFLAGS+=-fsanitize=float-divide-by-zero
# USER_CCFLAGS+=-fsanitize=undefined -fno-omit-frame-pointer -g
#USER_CCFLAGS+=-fsanitize=safe-stack
# USER_CCFLAGS+=-fsanitize=address -fno-omit-frame-pointer -g

# ## to run the address sanitizer without stopping at an error
# USER_CCFLAGS+=-fsanitize=address -fno-omit-frame-pointer -fsanitize-recover=address -g

# USER_CCFLAGS += -fsanitize=thread -fno-omit-frame-pointer -g -fsanitize-blacklist=../tsan.suppressions
# USER_CCFLAGS += -g -O0 -fsanitize=address -fno-omit-frame-pointer
# USER_CCFLAGS += -Og #-fsanitize=address -fno-omit-frame-pointer

ifndef OVERRIDE_SIM_BUILD_RULES
exa_sim_sp:	$(GENERIC_OBJ_FILES) $(SIM_EXTRA_LIBS) $(LIBS) $(CUDA_DEVICE_LINKED_OBJ_FILES)
	$(SIM_LINKER) $(HOST_LINKER_NVCC_OBJS) $(NVCC_ARCHIVES) $(HOST_LINKER_NVCC_LIBS) $(L_FLAGS_GLIBC) $(ccLDFLAGS) $(LINKMTMPI) $(STD_FLAG) $(USER_CCFLAGS) -o exa_sim_sp $(GENERIC_OBJ_FILES) $(LIBS) $(SIM_EXTRA_LIBS) $(SIM_LINKOPTS)
endif

#------------------------------------------------------------------------------
# SIMSIZES
#------------------------------------------------------------------------------
#By building this executable (test_sim_sizes), we ensure that SIMENG_SIZES_LIB
#is self-sufficient and all global symbols are resolved within
#the archive.This ensures that the build of dependent clients
#such as the decomposer and discretizer do not fail during the
#linking phase.
DFLAGS_SIMSIZES=-DBUILD_FOR_SIMSIZES=1
test_sim_sizes : export_simsizes.o $(SIMENG_SIMSIZES_LIB) simsizes_define_once.a
	$(CCC_WITHOUT_MPI) $(ccLDFLAGS) -o test_sim_sizes $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) export_simsizes.o $(SIMENG_SIMSIZES_LIB) simsizes_define_once.a $(LIBS) $(SIM_LINKOPTS)

# This library should only contain code that is NOT target specific!! 
# No variations between solver types are allowed, or you will get strange results!!
simsizes_define_once.a : simsizes_define_once.o data_offset_table_opts_no_mpi.o

$(SIMENG_SIMSIZES_LIB) : simeng_simsizes_interface.o ublk_surfel_offset_table_no_mpi.o phys_type_map_no_mpi.o parse_shob_descs_simsizes.o

###
# I_FLAGS_NOMPI is used to avoid including parallel HDF5 into these files because they MUST be built without MPI.
# After discussing this with JMN9 we concluded that this trick may be avoided by creating a supporting component (TODO). AAR7.
### 
#Use ABI option to allow components like the discretizer that are compiled with older GCC versions with this library
simeng_simsizes_interface.o : I_FLAGS = $(I_FLAGS_NOMPI)
simeng_simsizes_interface.o : ../simeng_simsizes_interface.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../simeng_simsizes_interface.cc -o simeng_simsizes_interface.o

parse_shob_descs_simsizes.o : I_FLAGS = $(I_FLAGS_NOMPI)
parse_shob_descs_simsizes.o : ../parse_shob_descs.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../parse_shob_descs.cc -o parse_shob_descs_simsizes.o

ublk_surfel_offset_table_no_mpi.o : I_FLAGS = $(I_FLAGS_NOMPI)
ublk_surfel_offset_table_no_mpi.o : ../ublk_surfel_offset_table.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../ublk_surfel_offset_table.cc -o ublk_surfel_offset_table_no_mpi.o

data_offset_table_opts_no_mpi.o : I_FLAGS = $(I_FLAGS_NOMPI)
data_offset_table_opts_no_mpi.o : ../data_offset_table_opts.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../data_offset_table_opts.cc -o data_offset_table_opts_no_mpi.o

phys_type_map_no_mpi.o : I_FLAGS = $(I_FLAGS_NOMPI)
phys_type_map_no_mpi.o : ../phys_type_map.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../phys_type_map.cc -o phys_type_map_no_mpi.o

lattice_no_mpi.o : I_FLAGS = $(I_FLAGS_NOMPI)
lattice_no_mpi.o : ../lattice.cc
		$(CCC_WITHOUT_MPI) -c $(STD_FLAG) $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) $(I_FLAGS) ../lattice.cc -o lattice_no_mpi.o

export_simsizes.o : ../export_simsizes.cc
		$(CCC_WITHOUT_MPI) -c $(CCFLAGS) $(STD_FLAG) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../export_simsizes.cc -o export_simsizes.o -I export_simsizes.h

simsizes_define_once.o : ../simsizes_define_once.cc
		$(CCC_WITHOUT_MPI) -c $(CCFLAGS) $(STD_FLAG) $(USER_CCFLAGS) $(DFLAGS_SIMSIZES) ../simsizes_define_once.cc -o simsizes_define_once.o


#------------------------------------------------------------------------------
# Special dependencies for advection
#------------------------------------------------------------------------------

SPECIAL_ADVECT_SRCS = swap_advect.o gather_advect.o swap_advect_avx2.o gather_advect_avx2.o

GEN_FILES_DUMMY = gen_files_dummy.txt

$(SPECIAL_ADVECT_SRCS) : $(GEN_FILES_DUMMY)

$(GEN_FILES_DUMMY): I_FLAGS = $(I_FLAGS_NOMPI)
$(GEN_FILES_DUMMY): ../box_advect_gen_headers.cc lattice_no_mpi.o ../lattice.h
	$(CCC_WITHOUT_MPI) -DBOX_ADVECT_GEN_FILES $(CCFLAGS) $(USER_CCFLAGS) -mno-avx \
	../box_advect_gen_headers.cc lattice_no_mpi.o -o box_advect_gen_headers $(GENERIC_LIBS_A)
	./box_advect_gen_headers
	touch $(GEN_FILES_DUMMY)

#AVX2 builds for swap and pde advection
DFLAGS_AVX2=-DBUILD_AVX2=1
swap_advect_avx2.o :
	$(CCC) -c $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_AVX2) -mavx2 ../swap_advect.cc -o $@

gather_advect_avx2.o :
	$(CCC) -c $(CCFLAGS) $(USER_CCFLAGS) $(DFLAGS_AVX2) -mavx2 ../gather_advect.cc -o $@
		 
#------------------------------------------------------------------------------
# Compile database
#------------------------------------------------------------------------------
ifdef USE_CLANG
.PHONY: compile_database

#GENERIC_CDB_FILES=$(addsuffix .cdb,$(addprefix ../,$(basename $(GENERIC_OBJ_FILES))))
# how to make a cdb from a cc
GENERIC_CDB_FILES = $(GENERIC_OBJ_FILES:.o=.cdb)

# this just runs the preprocess to generate these commands
%.cdb: ../%.cc
	$(COMPILE.cc) -E -MJ $(@) $(abspath $<) -Wunused-command-line-argument > /dev/null

compile_database:  $(GENERIC_CDB_FILES)
	$(shell FILES=`cat *.cdb`; echo "[$$FILES]" > compile_commands.json)
	@rm *.cdb
	ln compile_commands.json ../compile_commands.json 

GENERIC_CGF_FILES = $(GENERIC_OBJ_FILES:.o=.ll)
# rule to convert cc to llvm ir
%.ll: ../%.cc
	$(COMPILE.cc) -S -emit-llvm -S $(abspath $<) > /dev/null

exa_sim_spll:  $(GENERIC_CGF_FILES)
	$(CLANG_BIN)/llvm-link -S -v -o exa_sim_sp.ll $(GENERIC_CGF_FILES)
	$(CLANG_BIN)/opt -S -O3 -aa -basicaaa -tbaa -licm exa_sim_sp.ll -o optimized.ll
	$(CLANG_BIN)/opt -analyze -std-link-opts -dot-callgraph optimized.ll
#cat callgraph.dot | c++filt | sed 's,>,\\>,g; s,-\\>,->,g; s,<,\\<,g' | gawk '/external node/{id=$1} $1 != id' | dot -Tsvg -ofoo.svg
#cat callgraph.dot | c++filt | sed 's,>,\\>,g; s,-\\>,->,g; s,<,\\<,g' | gawk '/external node/{id=$1} $1 != id' | dot -Tdot -ofoo.dot

endif

