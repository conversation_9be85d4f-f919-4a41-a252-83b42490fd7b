#pragma once

#if !GPU_COMPILER && !BUILD_GPU
#if !BUILD_FOR_SIMSIZES


#include "petscksp.h"
#include "petscvec.h"
#include "petscmat.h"
#include "petscpc.h"
#include "petscerror.h"
#include "petscviewer.h"
#include "simulator_namespace.h"
#include "ublk_process_control.h"
#include "surfel_dyn_sp.h" // Needed for MLRF_SURFEL_GROUPS_OF_SCALE
#include "shob_groups.h" // Needed for SLRF_SURFEL_PAIRS_OF_SCALE
#include "comm_groups.h" // Needed for SURFEL_RECV_GROUPS_OF_SCALE
#include <filesystem>

#include PHYSICS_H

inline namespace SIMULATOR_NAMESPACE {
extern Mat g_solid_conduction_coeff_mat;
extern Vec g_solid_conduction_rhs_vec;
extern Vec g_solid_conduction_rhs_bc_vec;
extern Vec g_solid_conduction_solution_vec;
extern Vec g_solid_conduction_rho_cp_vec;
//extern Vec g_solid_conduction_source_vec;
extern KSP g_solid_ksp;
}

// NOTE: These guys can probably be put into a class. Maybe follow the way Dalon did it for his radiation stuff in
// radiation_comm.cc
extern bool g_implicit_solid_solver_active;
extern pthread_mutex_t g_implicit_solid_solver_mutex;
extern pthread_cond_t g_implicit_solid_solver_cond;

extern bool g_implicit_solid_solver_setup_active;
extern bool g_implicit_solid_solver_setup_complete;
extern pthread_mutex_t g_implicit_solid_solver_setup_mutex;
extern pthread_cond_t g_implicit_solid_solver_setup_cond;

//These macros are necessary because the PetscCall function tries to return
//an int, but since our functions are pretty much just all void the compiler
//gets upset.
//static void check_throw(PetscErrorCode err, size_t lineno) {
//  // These pointers do not need to be freed
//  const char * text = nullptr;
//  char * specific = nullptr;
//  PetscErrorMessage(err, &text, &specific); 
//  std::string msg(text);
//  msg += "\n";
//  msg += specific;
//  msg += "\n";
//  msg += "line ";
//  msg += std::to_string(lineno);
//  throw std::runtime_error(msg);
//}

//#define check(...) \
//  do { \
//    PetscErrorCode ierr =  __VA_ARGS__; \
//    if (ierr != 0) check_throw(ierr, __LINE__); \
//  } while(0)

VOID setup_implicit_solid_solver();
int set_solid_solver_state_indices_on_sps();
int set_state_indices_on_sps_internal(UBLK ublk);
VOID assemble_surfel_contributions();
VOID accumulate_surfel_contributions(SURFEL surfel);
VOID assemble_voxel_coefficients();
VOID create_linear_system_for_solid_solver(int num_states);
VOID initialize_linear_system_for_solid_solver(int num_states); // was STATE_VECTOR_INDEX
VOID preallocate_matrix_for_solid_solver();
VOID preallocate_matrix_internal(UBLK ublk, 
                                 PetscInt first_local_row_global_index, 
                                 PetscInt last_local_row_global_index, 
                                 std::vector<PetscInt> &num_nonzeros_in_diagonal_block, 
                                 std::vector<PetscInt> &num_nonzeros_in_offdiagonal_block);
VOID assemble_linear_system_for_solid_solver();
VOID assemble_linear_system_internal(UBLK ublk);
VOID finalize_assembly_for_solid_solver();
VOID create_solver_objects_for_solid_solver();
VOID implicit_solid_solver_evolve();
VOID solid_solver_solve_linear_system();
VOID write_solid_linear_system_to_txt();
VOID implicit_solid_solver_destroy_solver_objects();
VOID start_and_wait_for_implicit_solid_solver();
VOID start_and_wait_for_implicit_solid_solver_setup();
VOID implicit_solid_solver_update_voxel_temperature();
VOID update_voxel_temperature_internal(UBLK ublk);
//VOID implicit_solid_solver_update_rhs_vector();
VOID update_rhs_surfel_and_volumetric_source_contributions();
VOID update_rhs_surfel_and_volumetric_source_contributions_internal(UBLK ublk);

template<BOOLEAN FIRST_SOLVE>
VOID assemble_linear_system_for_solid_solver();
template<BOOLEAN FIRST_SOLVE>
VOID assemble_linear_system_internal(UBLK ublk);

template<BOOLEAN IS_FIRST_SOLVE>
VOID assemble_voxel_coefficients();

template<BOOLEAN FIRST_SOLVE>
VOID finalize_assembly_for_solid_solver();

PetscErrorCode MySolidKSPMonitor(KSP ksp, PetscInt it, PetscReal rnorm, void *ctx);
#endif
#endif
