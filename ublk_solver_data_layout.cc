#if BUILD_GPU

#include "ublk.h"
#include "gpu_ptr.h"
#include "gpu_globals.hcu"

typename HOST_UBLK_SOLVER_BLOCKS::MANAGER_TYPE g_host_ublk_solver_blocks;
typename HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE g_host_mblk_solver_blocks;

template<typename T>
tDEVICE_VECTOR<T>::tDEVICE_VECTOR(const std::vector<T>& h_vector):
  m_data(nullptr), m_size(h_vector.size()){
  this->reserve(h_vector.size());
  GPU::memcpy_h2d_async(GPU::Ptr<void>(this->data()),
                        h_vector.data(),
                        sizeof(T) * h_vector.size(),
                        GPU::g_stream);
}

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE ... DATA_TYPES>
struct COPY_SOLVER_BLOCKS_H2D;

template<UBLK_DATA_TYPE DATA_TYPE, typename SOLVER_BLOCKS_MGR>
VOID copy_h2d_impl(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr,
                   SOLVER_BLOCKS_MGR& h_solver_blocks_mgr) {
  
  auto& d_data = d_mblk_solver_blocks_mgr.template ref_to_data<DATA_TYPE>();
  auto& h_data = h_solver_blocks_mgr.template ref_to_data<DATA_TYPE>();

  if (h_data->size() > 0) {
    using SOLVER_BLOCK_DATA_TYPE = typename tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>::template tBLOCK_TYPE<N_VOXELS_64>;  
    using DEVICE_VECTOR = tDEVICE_VECTOR<SOLVER_BLOCK_DATA_TYPE>;
    DEVICE_VECTOR* d_vector_to_copy = new DEVICE_VECTOR(h_data.ref_to_vector());
    
    GPU::Ptr<DEVICE_VECTOR> d_vector = GPU::malloc<DEVICE_VECTOR>(1, "gpu_init_solver_blocks");
    GPU::copy_h2d_async(d_vector, d_vector_to_copy, 1, GPU::g_stream);
    d_data.swap_ptr(d_vector.get());
  } else {
    d_data.swap_ptr(nullptr);
  }
}

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE DATA_TYPE>
struct COPY_SOLVER_BLOCKS_H2D<SOLVER_BLOCKS_MGR, DATA_TYPE> {
  static VOID copy_h2d(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr,
                       SOLVER_BLOCKS_MGR& h_solver_blocks_mgr) {
    copy_h2d_impl<DATA_TYPE>(d_mblk_solver_blocks_mgr, h_solver_blocks_mgr);
  }
};

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE DATA_TYPE, UBLK_DATA_TYPE ... REST>
struct COPY_SOLVER_BLOCKS_H2D<SOLVER_BLOCKS_MGR, DATA_TYPE, REST...> {
  static VOID copy_h2d(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr,
                       SOLVER_BLOCKS_MGR& h_solver_blocks_mgr) {
    copy_h2d_impl<DATA_TYPE>(d_mblk_solver_blocks_mgr, h_solver_blocks_mgr);
    COPY_SOLVER_BLOCKS_H2D<SOLVER_BLOCKS_MGR, REST...>::copy_h2d(d_mblk_solver_blocks_mgr,
                                                                 h_solver_blocks_mgr);
  }
};

template<UBLK_DATA_TYPE ... DATA_TYPES>
tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...> copy_mblk_solver_blocks_h2d(tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& h_solver_blocks_mgr) {
  
  using MANAGER_TYPE = tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>;
  MANAGER_TYPE d_mblk_solver_blocks_mgr_to_copy(h_solver_blocks_mgr);

  //Initiate recursive solver block copy
  COPY_SOLVER_BLOCKS_H2D<MANAGER_TYPE, DATA_TYPES...>::copy_h2d(d_mblk_solver_blocks_mgr_to_copy, h_solver_blocks_mgr);
  
  GPU::Ptr<MANAGER_TYPE> d_mblk_solver_blocks_mgr = GPU::malloc<MANAGER_TYPE>(1, "gpu_init_solver_blocks");
  GPU::copy_h2d_async(d_mblk_solver_blocks_mgr, &d_mblk_solver_blocks_mgr_to_copy, 1, GPU::g_stream);
  GPU::set_global_solver_blocks_mgr(d_mblk_solver_blocks_mgr.get());
  return d_mblk_solver_blocks_mgr_to_copy;
}

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE ... DATA_TYPES>
struct FREE_DEVICE_SOLVER_BLOCKS;

template<UBLK_DATA_TYPE DATA_TYPE, typename SOLVER_BLOCKS_MGR>
VOID free_device_impl(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr) {
  auto& d_data = d_mblk_solver_blocks_mgr.template ref_to_data<DATA_TYPE>();
  using SOLVER_BLOCK_DATA_TYPE = typename tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>::template tBLOCK_TYPE<N_VOXELS_64>;
  using DEVICE_VECTOR = tDEVICE_VECTOR<SOLVER_BLOCK_DATA_TYPE>;
  auto d_vector = GPU::Ptr<const DEVICE_VECTOR>((const DEVICE_VECTOR*) d_data.get_ptr());
  if (d_vector) {
    DEVICE_VECTOR h_vector;
    GPU::copy_d2h(&h_vector, d_vector, 1);
    if (h_vector.size() > 0) {
      auto to_free = GPU::Ptr<SOLVER_BLOCK_DATA_TYPE>(h_vector.data());
      GPU::free(to_free, "gpu_init_solver_blocks");
    }
  }
}

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE DATA_TYPE>
struct FREE_DEVICE_SOLVER_BLOCKS<SOLVER_BLOCKS_MGR, DATA_TYPE> {
  static VOID free(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr) {
    free_device_impl<DATA_TYPE>(d_mblk_solver_blocks_mgr);
  }
};

template<typename SOLVER_BLOCKS_MGR, UBLK_DATA_TYPE DATA_TYPE, UBLK_DATA_TYPE ... REST>
struct FREE_DEVICE_SOLVER_BLOCKS<SOLVER_BLOCKS_MGR, DATA_TYPE, REST...> {
  static VOID free(SOLVER_BLOCKS_MGR& d_mblk_solver_blocks_mgr) {
    free_device_impl<DATA_TYPE>(d_mblk_solver_blocks_mgr);
    FREE_DEVICE_SOLVER_BLOCKS<SOLVER_BLOCKS_MGR, REST...>::free(d_mblk_solver_blocks_mgr);
  }
};

template<UBLK_DATA_TYPE ... DATA_TYPES>
void free_mblk_solver_blocks(tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& h_solver_blocks_mgr,
                              tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& d_mblk_solver_blocks_mgr) {
  h_solver_blocks_mgr.clear_all_data();
  using MANAGER_TYPE = tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>;
  FREE_DEVICE_SOLVER_BLOCKS<MANAGER_TYPE, DATA_TYPES...>::free(d_mblk_solver_blocks_mgr);
}

template HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE 
copy_mblk_solver_blocks_h2d(HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE& h_solver_blocks_mgr);

template void free_mblk_solver_blocks(HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE& h_solver_blocks_mgr,
                                      HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE& d_mblk_solver_blocks_mgr);
#endif
