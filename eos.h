/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Equation of state
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_EOS_H
#define _SIMENG_EOS_H

#include "common_sp.h"

enum EOS_TYPE {
  EOS_IDEAL_GAS,
  EOS_SHAN_CHEN_93,
  EOS_SHAN_CHEN_94,
  EOS_PENG_ROBINSON,
  EOS_VAN_DER_WAALS,
  EOS_SOAVE,
  EOS_MC,
  EOS_RHO_SQUARE,
  
  N_EOS_TYPES
};

typedef struct sEOS_PARAMS {
  STP_PHYS_VARIABLE temperature;
  STP_PHYS_VARIABLE sound_speed_squared;
  STP_PHYS_VARIABLE sound_speed_bound;
} *EOS_PARAMS;

class iEOS
{

 public:

  virtual sdFLOAT potential(STP_PHYS_VARIABLE density) = 0;  

  static iEOS* create(EOS_TYPE type, sEOS_PARAMS& params);

  virtual sdFLOAT pressure(sdFLOAT density, sdFLOAT temperature) = 0;

  virtual EOS_TYPE type() = 0;
};


#endif
