
#if !BUILD_GPU
#include PHYSICS_H

#include "radiation_phy.h"
#include "radiation_comm.h"

static void accumulate_radiation_surfel_net_rad_flux(sRADIATION_PATCH_GROUP* group, 
                                                     sSURFEL* surfel, 
                                                     sSURFEL::sSURFEL_RADIATION_DATA* rad_data, 
                                                     double patch_h_power, 
                                                     TIMESTEP prior_rad_index, 
                                                     BASETIME integration_interval)
{
  auto& timescale = get_timescale_ref();
  rad_data->net_rad_flux_accum += rad_data->net_rad_flux_prev * integration_interval;
  rad_data->irradiation_accum  += rad_data->irradiation_prev * integration_interval;

  cassert(rad_data->blackbody_power[prior_rad_index] != 0.0);

  dFLOAT surfel_emissivity = rad_data->emissivity_power[prior_rad_index] / rad_data->blackbody_power[prior_rad_index];
  dFLOAT q_new = surfel_emissivity*(rad_data->blackbody_power[prior_rad_index] - patch_h_power);

  //if ((group->realm() == sRADIATION_PATCH_GROUP::eREALM::CONDUCTION) && (surfel->id() == 1663)) {
    // printf("ACCUM RAD FLUX ts %d surfel %d e %.18e patch h %.18e q_new %.18e bb %.18e prior %d q_prev %.18e int %d scale %d\n", timescale.m_time, surfel->id(), surfel_emissivity, patch_h_power, q_new * g_density_scale_factor, rad_data->blackbody_power[prior_rad_index], prior_rad_index, rad_data->net_rad_flux_prev, integration_interval, group->scale());
  //}

  // We have to scale by g_density_scale_factor for some units reason,
  // I'm not really sure why that is
  rad_data->net_rad_flux_prev = q_new * g_density_scale_factor;
  rad_data->irradiation_prev = patch_h_power * g_density_scale_factor;

  // if ((group->solver() == sRADIATION_PATCH_GROUP::eSOLVER::FLOW) && surfel->id() == 0) {
  //   LOG_MSG("RAD").printf("recv ts %d surfel %d g %.18e patch q %.18e q_c %.18e bb %.18e q_prev %.18e h_prev %.18e int %d scale %d", timescale.m_time, g_density_scale_factor, patch_h_power, q_corrected, rad_data->blackbody_power[prior_rad_index], rad_data->net_rad_flux_prev, rad_data->irradiation_prev, integration_interval, group->scale());
  // }
}

static void accumulate_radiation_surfel_blackbody_power(sRADIATION_PATCH_GROUP* group,
                                                        sSURFEL* surfel, 
                                                        sSURFEL::sSURFEL_RADIATION_DATA* rad_data, 
                                                        TIMESTEP prior_rad_index,
                                                        TIMESTEP next_rad_index, 
                                                        BASETIME integration_interval, 
                                                        bool patch_sends_emissive_power,
                                                        asINT32 area_scaling,
                                                        double bb_scale_factor,
                                                        double * patch_bb_power,
                                                        double * patch_emissive_power)
{

  auto& timescale = get_timescale_ref();
  dFLOAT area = surfel->area * area_scaling;

  rad_data->blackbody_power[next_rad_index] += radiation_t_fourth_power(rad_data->temp_prev) * integration_interval;
  rad_data->emissivity_power[next_rad_index] += rad_data->emissivity * radiation_t_fourth_power(rad_data->temp_prev) * integration_interval;

  rad_data->blackbody_power[next_rad_index] *= bb_scale_factor;
  rad_data->emissivity_power[next_rad_index] *= bb_scale_factor;

  *patch_bb_power += rad_data->blackbody_power[next_rad_index] * area;
  
  if( rad_data->blackbody_power[next_rad_index] == 0.0 || !std::isfinite(rad_data->blackbody_power[next_rad_index]) ) {
    simerr_report_error_code(SP_EER_BAD_BB_POWER,
                             surfel->scale(),
                             cast_as_regular_array(surfel->centroid),
                             surfel->id(),
                             surfel->area,
                             rad_data->temp_prev,
                             rad_data->emissivity,
                             "");
    rad_data->blackbody_power[next_rad_index]  =  sim.stefan_boltzmann_const * radiation_t_fourth_power(sim.char_temp);
  }
  //cassert(rad_data->blackbody_power[next_rad_index] != 0.0);

  if (patch_sends_emissive_power) {
    *patch_emissive_power += rad_data->emissivity_power[next_rad_index] * area;
  }

  rad_data->blackbody_power[prior_rad_index] = 0.0;
  rad_data->emissivity_power[prior_rad_index] = 0.0;

  // std::vector<SHOB_ID> surfels_to_check({564,647,648,651,897,899,901,1466,1467,1468,1469,1470,1472,1474,1475,1478});

  // if ((group->realm() == sRADIATION_PATCH_GROUP::eREALM::CONDUCTION) && (std::find(surfels_to_check.begin(), surfels_to_check.end(), surfel->id()) != surfels_to_check.end())) {
    // printf("ACCUM BB ts %d surfel %d eo %d bb [%.8e,%.8e] prior %d t_prev %.18e int %d scale %d\n", timescale.m_time, surfel->id(), !surfel->is_regular(), rad_data->blackbody_power[0], rad_data->blackbody_power[1], prior_rad_index, rad_data->temp_prev, integration_interval, group->scale());
  // }
  // if ((group->solver() == sRADIATION_PATCH_GROUP::eSOLVER::FLOW) && surfel->id() == 0) {
  //   LOG_MSG("RAD").printf("recv ts %d surfel %d patch q %.18e q_c %.18e bb %.18e q_prev %.18e h_prev %.18e int %d scale %d", timescale.m_time, surfel->id(), patch_h_power, q_corrected, rad_data->blackbody_power[prior_rad_index], rad_data->net_rad_flux_prev, rad_data->irradiation_prev, integration_interval, group->scale());
  // }

}

static void accumulate_radiation_surfel_blackbody_power_ckpt_restore(sRADIATION_PATCH_GROUP* group,
                                                                     sSURFEL* surfel, 
                                                                     sSURFEL::sSURFEL_RADIATION_DATA* rad_data, 
                                                                     TIMESTEP prior_rad_index,
                                                                     bool patch_sends_emissive_power,
                                                                     asINT32 area_scaling,
                                                                     double * patch_bb_power,
                                                                     double * patch_emissive_power)
{
  auto& timescale = get_timescale_ref();
  dFLOAT area = surfel->area * area_scaling;

  *patch_bb_power += rad_data->blackbody_power[prior_rad_index] * area;

  // std::vector<SHOB_ID> surfels_to_check({564,647,648,651,897,899,901,1466,1467,1468,1469,1470,1472,1474,1475,1478});

  // if ((group->realm() == sRADIATION_PATCH_GROUP::eREALM::CONDUCTION) && (std::find(surfels_to_check.begin(), surfels_to_check.end(), surfel->id()) != surfels_to_check.end())) {
    // printf("RESUME ACCUM BB ts %d surfel %d eo %d bb [%.8e,%.8e] prior %d t_prev %.18e scale %d\n", timescale.m_time, surfel->id(), !surfel->is_regular(), rad_data->blackbody_power[0], rad_data->blackbody_power[1], prior_rad_index, rad_data->temp_prev, group->scale());
  // }

  if (patch_sends_emissive_power) {
    *patch_emissive_power += rad_data->emissivity_power[prior_rad_index] * area;
  }
}

void do_radiation_patch_group(sRADIATION_PATCH_GROUP* group,
                              TIMESTEP prior_rad_index,
                              bool do_recvs,
                              bool do_sends,
                              BASETIME send_integration_interval,
                              BASETIME total_send_interval,
                              bool restart_for_ckpt_restore)
{
  if (do_sends) {
    group->wait_for_copying_of_send_data();
  }

  auto& timescale = get_timescale_ref();
  SCALE scale = group->scale();
  BASETIME scale_prior_base_time = get_solver_tm(group->realm()).scale_prior_base_time(group->scale());
  BASETIME prior_recv_base_time = timescale.m_radiation_tm.prior_recv_base_time();
  BASETIME dt_scale = timescale.m_base_time - scale_prior_base_time;
  BASETIME recv_integration_interval = std::min(dt_scale, timescale.m_base_time - prior_recv_base_time);

  auto surfel_it = group->surfels().begin();
  const dFLOAT * patch_h_power = group->patch_h_power();
  dFLOAT * patch_bb_power = group->patch_bb_power();
  dFLOAT * patch_emissive_power = group->patch_emissive_power();

  asINT32 next_rad_index = prior_rad_index ^ 1;
  dFLOAT scale_factor = sim.stefan_boltzmann_const / total_send_interval;
  dFLOAT area_scaling = sim_scale_to_voxel_size(scale);
  area_scaling *= area_scaling;

  size_t count = 0;
  for (auto patch_surfel_count: group->patch_surfel_counts()) {
    double local_patch_bb_power = 0.0;
    double local_patch_emissive_power = 0.0;

    // All the surfels in this patch should use the same surface condition,
    // So we only have to check the first one
    sRAD_PATCH_SURFEL_PTR first_surfel = *surfel_it;
    auto * first_rad_data = first_surfel.ptr()->radiation_data();

    if (first_surfel.get<sRAD_PATCH_SURFEL_PTR_TRAITS::BACKSIDE>()) {
      first_rad_data++;
    }

    bool patch_sends_emissive_power = first_rad_data->surface_condition->m_parameter_time_varying;

    for (size_t i=0; i<patch_surfel_count; i++) {
      sRAD_PATCH_SURFEL_PTR surfel = *surfel_it; 
      ++surfel_it;
      cassert( surfel.ptr()->is_radiation() );
      cassert(surfel.ptr()->scale() == group->scale());
      auto * rad_data = surfel.ptr()->radiation_data();

      if (surfel.get<sRAD_PATCH_SURFEL_PTR_TRAITS::BACKSIDE>()) {
        rad_data++;
      }

      if (do_recvs) {
        accumulate_radiation_surfel_net_rad_flux(group, 
                                                 surfel.ptr(), 
                                                 rad_data, 
                                                 *patch_h_power, 
                                                 prior_rad_index, 
                                                 recv_integration_interval);
      }

      if (do_sends) {

        if (restart_for_ckpt_restore) {
          accumulate_radiation_surfel_blackbody_power_ckpt_restore(group,
                                                                   surfel.ptr(),
                                                                   rad_data,
                                                                   next_rad_index,
                                                                   patch_sends_emissive_power, 
                                                                   area_scaling,
                                                                   &local_patch_bb_power,
                                                                   &local_patch_emissive_power);
        } else {
          accumulate_radiation_surfel_blackbody_power(group, 
                                                      surfel.ptr(), 
                                                      rad_data, 
                                                      prior_rad_index, 
                                                      next_rad_index, 
                                                      send_integration_interval, 
                                                      patch_sends_emissive_power, 
                                                      area_scaling, 
                                                      scale_factor, 
                                                      &local_patch_bb_power, 
                                                      &local_patch_emissive_power);
          if (!std::isfinite(local_patch_bb_power)) {
            msg_error("bad bb power!");
          }
        }
      }
    }

    if (do_recvs) {
      // printf("PATCH Q ts %ld %.18e\n", g_timescale.m_time, *patch_h_power);
      patch_h_power++;
    }

    if (do_sends) {
      if (!std::isfinite(local_patch_bb_power)) {
        msg_error("bad bb power!");
      }

      *patch_bb_power = local_patch_bb_power;
      // printf("PATCH BB ts %ld %.18e\n", g_timescale.m_time, *patch_bb_power);
      patch_bb_power++;
      if (patch_sends_emissive_power) {
        *patch_emissive_power = local_patch_emissive_power;
        patch_emissive_power++;
      }
    }
    
  }
}
#endif


