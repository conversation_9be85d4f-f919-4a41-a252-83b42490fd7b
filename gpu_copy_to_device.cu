/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include <cuda.h>
#include <iostream>
#include "helper_cuda.h"
#include <cassert>
#include "common_sp.h"
#include "gpu_globals.hcu"
#include "gpu_host_include.h"
#include "eqns.h"
#include "ublk.h"
#include "meas.h"
#include "gpu_meas.h"
#include "surfel.h"
#include "gpu_surfel_interactions.h"
#include "gpu_copy_to_device.h"

#include PHYSICS_H

using GPU::sDEV_PTR;
using GPU::sDEV_PTR_TO_DEV_PTR;
using GPU::cCOPY_TO_DEVICE;
using GPU::IS_NOT_INTEGRAL_TYPE;
using GPU::g_phys_desc_h2d_map;

void sHD_SLAB_ALLOCATOR::copy_all_slabs_h2d() {
  for (int s = 0; s < m_host_slabs.size(); s++) {    
    GPU::memcpy_h2d_async(GPU::Ptr<void>(m_device_slabs[s]->data()), m_host_slabs[s]->data(), SLAB_SIZE, GPU::g_stream);
  }
}

static GPU::TAGGED_UBLK translate_tagged_ublk_H2D(sHMBLK::sTAGGED_UBLK h_tagged_ublk)
{
  GPU::TAGGED_UBLK for_copy;
  sHMBLK* h_ublk = h_tagged_ublk.ublk();
  uint8_t child_ublk_offset = h_tagged_ublk.get_offset_in_mega_block();
  GPU::UBLK d_ublk = GPU::g_ublk_table.map_H2D(h_ublk);
  for_copy.set_ublk(d_ublk);
  for_copy.set_offset_in_mega_block(child_ublk_offset);
  if (h_ublk->has_two_copies()) {
    for_copy.set_ublk_has_two_copies();
  }

  if (h_tagged_ublk.is_ublk_last()) {
    for_copy.set_ublk_as_last();
  }  
  return for_copy;
}

/*=================================================================================================
 * @func sUBLK_VECTOR_64::copy_to_device
 *================================================================================================*/
void change_ptrs_h2d(sMBLK::sTAGGED_UBLK& d_tagged_ublk,
                     const sHMBLK::sTAGGED_UBLK& h_tagged_ublk);

VOID change_ptrs_h2d(sMBLK::sUBLK_VECTOR& d_split_vector,
                     const sHMBLK::sUBLK_VECTOR& h_split_vector) {

  constexpr auto hd_alloc_type = HD_SLAB_ALLOCATOR_TYPES::MBLK_BOX;
  
  const sHMBLK::sTAGGED_UBLK* h_tagged_ublks = h_split_vector.tagged_ublks();
  uINT8 num_split_ublks = h_split_vector.num_split_ublks();
  size_t n_bytes = sizeof(GPU::TAGGED_UBLK) * num_split_ublks;
  auto split_tagged_ublks_to_copy = (sMBLK::sTAGGED_UBLK*) hd_slab_malloc(hd_alloc_type,
                                                                          n_bytes);

  for (int t = 0; t < num_split_ublks; t++) {
    change_ptrs_h2d(split_tagged_ublks_to_copy[t],
                    h_tagged_ublks[t]);
  }

  auto d_split_tagged_ublks = sHD_SLAB_ALLOCATOR::get_device_ptr(split_tagged_ublks_to_copy);
  d_split_vector.m_n_ublks  = num_split_ublks;
  d_split_vector.m_tagged_ublks = d_split_tagged_ublks.get();
}

/*=================================================================================================
 * @func sTAGGED_UBLK::copy_to_device
 *================================================================================================*/
void change_ptrs_h2d(sMBLK::sTAGGED_UBLK& d_tagged_ublk,
                     const sHMBLK::sTAGGED_UBLK& h_tagged_ublk) {

  constexpr auto hd_alloc_type = HD_SLAB_ALLOCATOR_TYPES::MBLK_BOX;
  
  if (h_tagged_ublk.is_null()) {
    d_tagged_ublk.set_ublk(nullptr);
  }
  else if (h_tagged_ublk.is_ublk_scale_interface()) {
    
    const sHMBLK::sSCALE_BOX_INTERFACE * h_scale_interface = h_tagged_ublk.interface();
    auto& interface_for_copy = *hd_slab_malloc<GPU::sSCALE_BOX_INTERFACE>(hd_alloc_type);

    ccDOTIMES(tagged_fine_ublk, N_VOXELS_8) {
      sHMBLK::sTAGGED_UBLK h_tagged_fine_ublk = h_scale_interface->m_fine_ublks[tagged_fine_ublk];
      if ( h_tagged_fine_ublk.is_null() ) {
        continue;
      }
      else if (h_tagged_fine_ublk.is_ublk_split()) {
        GPU::TAGGED_UBLK d_tagged_fine_ublk;
        sHMBLK::sUBLK_VECTOR* h_split_vector = h_tagged_fine_ublk.split_ublks();
        auto& split_vector_for_copy = *hd_slab_malloc<sMBLK::sUBLK_VECTOR>(hd_alloc_type);
        change_ptrs_h2d(split_vector_for_copy, *h_split_vector);
        auto d_split_vector = sHD_SLAB_ALLOCATOR::get_device_ptr(&split_vector_for_copy);
        d_tagged_fine_ublk.set_ublk_as_split(d_split_vector.get());
        interface_for_copy.m_fine_ublks[tagged_fine_ublk] = d_tagged_fine_ublk;
      }
      else {
        interface_for_copy.m_fine_ublks[tagged_fine_ublk] = translate_tagged_ublk_H2D(h_tagged_fine_ublk);
      }
    }
    auto d_scale_interface_ptr = sHD_SLAB_ALLOCATOR::get_device_ptr(&interface_for_copy);
    d_tagged_ublk.set_scale_interface(d_scale_interface_ptr.get());
  }
  else if (h_tagged_ublk.is_ublk_split()) {
    auto h_split_vector = ((sHMBLK::sTAGGED_UBLK&) h_tagged_ublk).split_ublks();
    auto& split_vector_for_copy = *hd_slab_malloc<sMBLK::sUBLK_VECTOR>(hd_alloc_type);
    change_ptrs_h2d(split_vector_for_copy, *h_split_vector);
    auto d_split_vector = sHD_SLAB_ALLOCATOR::get_device_ptr(&split_vector_for_copy);
    d_tagged_ublk.set_ublk_as_split(d_split_vector.get());
  }
  else {
    d_tagged_ublk = translate_tagged_ublk_H2D(h_tagged_ublk);
  }
}

/*=================================================================================================
 * @func sUBLK_BOX::copy_to_device
 * Allocate all the TAGGED UBLKs in a UBLK_BOX and called their copy methods
 *================================================================================================*/
VOID change_ptrs_h2d(sMBLK::sUBLK_BOX& d_box,
                     const sHMBLK::sUBLK_BOX& h_box) { 
  size_t n_ublks = h_box.n_ublks();
  for (int i = 0; i < n_ublks; i++) {
    change_ptrs_h2d(d_box.m_ublks[i],
                    h_box.m_ublks[i]);
  }
  
  GPU::Ptr<sMBLK::sTAGGED_UBLK> tagged_ublks_for_copy = GPU::malloc<sMBLK::sTAGGED_UBLK>(n_ublks, "ublk box");
  GPU::copy_h2d_async(tagged_ublks_for_copy, d_box.m_ublks, n_ublks, GPU::g_stream);
  d_box.m_ublks = tagged_ublks_for_copy.get();
}

/*=================================================================================================
 * @func sCSYS::copy_to_device
 *================================================================================================*/
VOID sCSYS::copy_to_device(sDEV_PTR& d_ptr_to_ptr,
                           size_t n_bytes,
                           const sCSYS* h_ptr) {

  sCSYS* d_csys = static_cast<sCSYS*>(d_ptr_to_ptr.get());
  size_t n_csys = n_bytes / sizeof(sCSYS);

  for (int i = 0; i < n_csys; i++) {

    sCSYS* d_csys_i = d_csys + i;
    const sCSYS* h_csys_i = h_ptr + i;

    {//The struct itself      
      sDEV_PTR d_csys_i_wrap(d_csys_i);
      cCOPY_TO_DEVICE::trivial_copy(d_csys_i_wrap,
                                    sizeof(sCSYS),
                                    h_csys_i);      
    }

    //name
    if (h_csys_i->name) {
      sDEV_PTR_TO_DEV_PTR  ptr_to_d_csys_i_name( get_class_member_ptr(d_csys_i, name) );
      cCOPY_TO_DEVICE::allocate(ptr_to_d_csys_i_name, "csys",
                                strlen(h_csys_i->name) + 1, /*One for null char*/
                                h_csys_i->name);
    }    
  }

}

/*=================================================================================================
 * @func sCALIBRATION_PARAMS_INFO::copy_to_device
 *================================================================================================*/
VOID sCALIBRATION_PARAMS_INFO::copy_to_device(sDEV_PTR& d_ptr,
                                              size_t n_bytes,
                                              const sCALIBRATION_PARAMS_INFO* h_ptr) {
  /* cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr); */
}


/*=================================================================================================
 * @func sSIM_INFO::copy_to_device
 *================================================================================================*/
VOID sTHERMAL_ACCEL_INFO::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                         size_t n_bytes,
                                         const sTHERMAL_ACCEL_INFO* h_ptr) {
  /* cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr); */
}

/*=================================================================================================
 * @func sPHYSICS_VARIABLE::copy_to_device
 *================================================================================================*/
VOID sPHYSICS_VARIABLE::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                       size_t n_bytes,
                                       const sPHYSICS_VARIABLE* h_ptr,
                                       size_t n_vars) {

  sPHYSICS_VARIABLE* d_phy_var = static_cast<sPHYSICS_VARIABLE*>(d_ptr.get());

  for (int i = 0; i < n_vars; i++) {

    sPHYSICS_VARIABLE* d_phy_var_i = d_phy_var + i;
    const sPHYSICS_VARIABLE* h_phy_var_i = h_ptr + i;
    {//The struct itself
      sDEV_PTR d_phy_var_i_wrap(d_phy_var_i);
      cCOPY_TO_DEVICE::trivial_copy(d_phy_var_i_wrap,
                                    sizeof(sPHYSICS_VARIABLE),
                                    h_phy_var_i);
    }

    //name
    if (h_phy_var_i->name) {
      sDEV_PTR_TO_DEV_PTR ptr_to_var_name( get_class_member_ptr(d_phy_var_i, name) );
      cCOPY_TO_DEVICE::allocate(ptr_to_var_name, "physics var",
                                strlen(h_phy_var_i->name) + 1,
                                h_phy_var_i->name);
    }

  }
}

/*=================================================================================================
 * @func copy_physics_descriptor_h2d
 *================================================================================================*/
VOID copy_physics_descriptor_h2d(const sPHYSICS_DESCRIPTOR* h_phy_desc,
                                 sPHYSICS_DESCRIPTOR* d_phy_desc) {
  
  {//The struct itself      
    sDEV_PTR d_phy_desc_wrap(d_phy_desc);
    cCOPY_TO_DEVICE::trivial_copy(d_phy_desc_wrap,
                                  sizeof(sPHYSICS_DESCRIPTOR),
                                  h_phy_desc);      
  }

  if (h_phy_desc->name) {//name
    sDEV_PTR_TO_DEV_PTR ptr_to_desc_name( get_class_member_ptr(d_phy_desc, name) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_name, "physics desc",
                              strlen(h_phy_desc->name) + 1,
                              h_phy_desc->name);
  }

  //data_table_string
  if (h_phy_desc->data_table_string){
    sDEV_PTR_TO_DEV_PTR  ptr_to_desc_i_data_table_string( get_class_member_ptr(d_phy_desc, data_table_string) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_i_data_table_string, "physics desc",
                              strlen(h_phy_desc->data_table_string) + 1, /*One for null char*/
                              h_phy_desc->data_table_string);
  }

  //_parameters
  if (h_phy_desc->n_parameters){
    sDEV_PTR_TO_DEV_PTR ptr_to_desc_parameters( get_class_member_ptr(d_phy_desc, _parameters) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_parameters, "physics desc",
                              sizeof(sPHYSICS_VARIABLE) * h_phy_desc->n_parameters,
                              h_phy_desc->_parameters,
                              h_phy_desc->n_parameters);
  }

  //initial_conditions
  if(h_phy_desc->n_initial_conditions) {
    sDEV_PTR_TO_DEV_PTR ptr_to_desc_initial_conditions( get_class_member_ptr(d_phy_desc, _initial_conditions) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_initial_conditions, "physics desc",
                              sizeof(sPHYSICS_VARIABLE) * h_phy_desc->n_initial_conditions,
                              h_phy_desc->_initial_conditions,
                              h_phy_desc->n_initial_conditions);
  }

  //uds_parameters
  if (h_phy_desc->n_uds_parameters){
    sDEV_PTR_TO_DEV_PTR ptr_to_desc_parameters( get_class_member_ptr(d_phy_desc, _uds_parameters) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_parameters, "physics desc",
                              sizeof(sPHYSICS_VARIABLE) * h_phy_desc->n_uds_parameters,
                              h_phy_desc->_uds_parameters,
                              h_phy_desc->n_uds_parameters);
  }
  					
  //uds_initial_conditions
  if(h_phy_desc->n_uds_initial_conditions) {
    sDEV_PTR_TO_DEV_PTR ptr_to_desc_initial_conditions( get_class_member_ptr(d_phy_desc, _uds_initial_conditions) );
    cCOPY_TO_DEVICE::allocate(ptr_to_desc_initial_conditions, "physics desc",
                              sizeof(sPHYSICS_VARIABLE) * h_phy_desc->n_uds_initial_conditions,
                              h_phy_desc->_uds_initial_conditions,
                              h_phy_desc->n_uds_initial_conditions);
  }	
}

/*=================================================================================================
 * @func sPHYSICS_DESCRIPTOR::copy_to_device
 *================================================================================================*/
VOID sPHYSICS_DESCRIPTOR::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                         size_t n_bytes,
                                         const sPHYSICS_DESCRIPTOR* h_ptr,
                                         size_t n_physics_desc) {

  sPHYSICS_DESCRIPTOR* d_phy_desc = static_cast<sPHYSICS_DESCRIPTOR*>(d_ptr.get());

  for (int i = 0; i < n_physics_desc; i++) {

    sPHYSICS_DESCRIPTOR* d_phy_desc_i = d_phy_desc + i;
    const sPHYSICS_DESCRIPTOR* h_phy_desc_i = h_ptr + i;
    copy_physics_descriptor_h2d(h_phy_desc_i, d_phy_desc_i);
  }
}



/*=================================================================================================
 * @func sLRF_PHYSICS_DESCRIPTOR::copy_to_device
 *================================================================================================*/
VOID sLRF_PHYSICS_DESCRIPTOR::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                             size_t n_bytes,
                                             const sLRF_PHYSICS_DESCRIPTOR* h_ptr,
                                             size_t n_physics_desc) {

  auto d_phy_desc = static_cast<sLRF_PHYSICS_DESCRIPTOR*>(d_ptr.get());

  for (int i = 0; i < n_physics_desc; i++) {
    sLRF_PHYSICS_DESCRIPTOR* d_phy_desc_i = d_phy_desc + i;
    const sLRF_PHYSICS_DESCRIPTOR* h_phy_desc_i = h_ptr + i;

    {//The struct itself      
      sDEV_PTR d_phy_desc_i_wrap(d_phy_desc_i);
      cCOPY_TO_DEVICE::trivial_copy(d_phy_desc_i_wrap,
                                    sizeof(sLRF_PHYSICS_DESCRIPTOR),
                                    h_phy_desc_i);      
    }

    //Copy base physics descriptor class
    copy_physics_descriptor_h2d(h_phy_desc_i, d_phy_desc_i);
  }

}

/*=================================================================================================
 * @func sFREEZE_MOMENTUM_PARMS::copy_to_device
 *================================================================================================*/
VOID sFREEZE_MOMENTUM_PARMS::copy_to_device(sDEV_PTR& d_ptr, size_t n_bytes,
                                            const sFREEZE_MOMENTUM_PARMS* h_ptr) {
  cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
}


/*=================================================================================================
 * @func sGRF::copy_to_device
 *================================================================================================*/
VOID sGRF::copy_to_device(sDEV_PTR& d_ptr, size_t n_bytes,
                          const sGRF* h_ptr) {

  cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);

  //TO DO and copy for sBG_TRANSFORM3d ground_to_global_xform
}

/*=================================================================================================
 * @func sSEED_CONTROL::copy_to_device
 *================================================================================================*/
__HOST__ VOID sSEED_CONTROL::copy_to_device(sDEV_PTR& d_ptr_to_ptr,
                                            size_t n_bytes,
                                            const sSEED_CONTROL* h_ptr) {
  int n_seed_controllers = n_bytes / sizeof(sSEED_CONTROL);

  //GPU::sSIM_INFO* d_sim_info;
  //checkCudaErrors( cudaMemcpyFromSymbol(&d_sim_info,
  //                                      GPU::g_sim_info,
  //                                      sizeof(GPU::sSIM_INFO*), 0,
  //                                      cudaMemcpyDeviceToHost) );

  //auto ptr_n_seed_controllers = get_class_member_ptr(d_sim_info, n_seed_controllers);
  //checkCudaErrors( cudaMemcpy(ptr_n_seed_controllers.get(),
  //                            &n_seed_controllers,
  //                            sizeof(int),
  //                            cudaMemcpyHostToDevice) );

  sSEED_CONTROL* d_seed_ctrl = static_cast<sSEED_CONTROL*>(d_ptr_to_ptr.get());

  for (int i = 0; i < n_seed_controllers; i++) {

    sSEED_CONTROL* d_seed_ctrl_i = d_seed_ctrl + i;
    const sSEED_CONTROL* h_seed_ctrl_i = h_ptr + i;

    {//The struct itself      
      sDEV_PTR d_seed_ctrl_i_wrap(d_seed_ctrl_i);
      cCOPY_TO_DEVICE::trivial_copy(d_seed_ctrl_i_wrap,
                                    sizeof(sSEED_CONTROL),
                                    h_seed_ctrl_i);      
    }


    if (h_seed_ctrl_i->smart_seed_var_types)
    {
      sDEV_PTR_TO_DEV_PTR ptr_to_smart_seed_var_types( get_class_member_ptr(d_seed_ctrl_i, smart_seed_var_types) );
      cCOPY_TO_DEVICE::allocate(ptr_to_smart_seed_var_types, "smart seed",
                                h_seed_ctrl_i->n_smart_seed_vars * sizeof(DGF_SEED_VAR_TYPE),
                                (int*) h_seed_ctrl_i->smart_seed_var_types);
    }

    if (h_seed_ctrl_i->is_var_seeded)
    {
      sDEV_PTR_TO_DEV_PTR ptr_to_is_var_seeded( get_class_member_ptr(d_seed_ctrl_i, is_var_seeded) );
      cCOPY_TO_DEVICE::allocate(ptr_to_is_var_seeded, "smart seed",
                                h_seed_ctrl_i->n_smart_seed_vars * sizeof(cBOOLEAN),
                                h_seed_ctrl_i->is_var_seeded);
    }

    if (h_seed_ctrl_i->active_seed_var_index)
    {
      sDEV_PTR_TO_DEV_PTR ptr_to_active_seed_var_index( get_class_member_ptr(d_seed_ctrl_i, active_seed_var_index) );
      cCOPY_TO_DEVICE::allocate(ptr_to_active_seed_var_index, "smart seed",
                                h_seed_ctrl_i->n_smart_seed_vars * sizeof(asINT32),
                                h_seed_ctrl_i->active_seed_var_index);
    }

  }
}
/*=================================================================================================
 * @func sSIM_INFO::copy_to_device
 *================================================================================================*/
VOID ::sSIM_INFO::copy_to_device(sDEV_PTR& dev_ptr,
                                 size_t n_bytes,
                                 const ::sSIM_INFO* h_ptr) {

  GPU::sSIM_INFO* d_sim_info = static_cast<GPU::sSIM_INFO*>(dev_ptr.get());
  GPU::zero(GPU::Ptr<GPU::sSIM_INFO>(d_sim_info), 1);
  //sSEED_CONTROL
  {
    sDEV_PTR_TO_DEV_PTR ptr_to_seed_ptr( get_class_member_ptr(d_sim_info, m_seed_control) );
    size_t n_controllers = h_ptr->m_seed_control.size();
    cCOPY_TO_DEVICE::allocate(ptr_to_seed_ptr, "smart seed",
                              n_controllers*sizeof(sSEED_CONTROL), 
                              h_ptr->m_seed_control.data());
  }
  
  {//sCSYS
    sDEV_PTR_TO_DEV_PTR ptr_to_csys_ptr( get_class_member_ptr(d_sim_info, csys_table) );
    cCOPY_TO_DEVICE::allocate(ptr_to_csys_ptr, "csys",
                              h_ptr->n_csys*sizeof(sCSYS), 
                              h_ptr->csys_table);
  }

  {//CALIBRATION
    sDEV_PTR ptr_to_calibration_info( get_class_member_ptr(d_sim_info, calibration_params) );
    cCOPY_TO_DEVICE::copy_to_device(IS_NOT_INTEGRAL_TYPE(), ptr_to_calibration_info,
                                    sizeof(sCALIBRATION_PARAMS_INFO), &h_ptr->calibration_params);
  }

  {//THERMAL ACCEL
    sDEV_PTR ptr_to_taccel_info( get_class_member_ptr(d_sim_info, thermal_accel) );
    cCOPY_TO_DEVICE::copy_to_device(IS_NOT_INTEGRAL_TYPE(), ptr_to_taccel_info,
                                    sizeof(sTHERMAL_ACCEL_INFO), &h_ptr->thermal_accel);
  }

  //FLUID PHYSICS DESC
  if (h_ptr->n_volume_physics_descs) {
    sDEV_PTR_TO_DEV_PTR ptr_to_fluid_physics_descs( get_class_member_ptr(d_sim_info, fluid_physics_descs) );
    cCOPY_TO_DEVICE::allocate(ptr_to_fluid_physics_descs, "physics desc",
                              sizeof(sPHYSICS_DESCRIPTOR) * h_ptr->n_volume_physics_descs,
                              h_ptr->volume_physics_descs, h_ptr->n_volume_physics_descs);

    sPHYSICS_DESCRIPTOR* d_ptr_addr;
    checkCudaErrors( cudaMemcpy(&d_ptr_addr, ptr_to_fluid_physics_descs.get(),
                                sizeof(sPHYSICS_DESCRIPTOR*), cudaMemcpyDeviceToHost) );
    for (int i = 0; i < h_ptr->n_volume_physics_descs; i ++) {
      g_phys_desc_h2d_map.insert(h_ptr->volume_physics_descs + i,
                                  d_ptr_addr + i);
    }
  }

  //SURFACE PHYSICS DESC
  if (h_ptr->n_flow_surface_physics_descs) {
      sDEV_PTR_TO_DEV_PTR ptr_to_surf_physics_descs( get_class_member_ptr(d_sim_info, flow_surface_physics_descs) );

    cCOPY_TO_DEVICE::allocate(ptr_to_surf_physics_descs, "physics desc",
                                sizeof(sPHYSICS_DESCRIPTOR) * h_ptr->n_flow_surface_physics_descs,
                                h_ptr->flow_surface_physics_descs, h_ptr->n_flow_surface_physics_descs);

    sPHYSICS_DESCRIPTOR* d_ptr_addr;
    checkCudaErrors( cudaMemcpy(&d_ptr_addr, ptr_to_surf_physics_descs.get(),
                                sizeof(sPHYSICS_DESCRIPTOR*), cudaMemcpyDeviceToHost) );
    for (int i = 0; i < h_ptr->n_flow_surface_physics_descs; i ++) {
      g_phys_desc_h2d_map.insert(h_ptr->flow_surface_physics_descs + i,
                                  d_ptr_addr + i);
    }
  }

  //LRF PHYSICS DESC
  if (h_ptr->n_lrf_physics_descs) {
    
    sDEV_PTR_TO_DEV_PTR ptr_to_lrf_physics_descs( get_class_member_ptr(d_sim_info, lrf_physics_descs) );

    cCOPY_TO_DEVICE::allocate(ptr_to_lrf_physics_descs, "physics desc",
                              sizeof(sLRF_PHYSICS_DESCRIPTOR) * h_ptr->n_lrf_physics_descs,
                              h_ptr->lrf_physics_descs, h_ptr->n_lrf_physics_descs);

    sLRF_PHYSICS_DESCRIPTOR* d_ptr_addr;
    checkCudaErrors( cudaMemcpy(&d_ptr_addr, ptr_to_lrf_physics_descs.get(),
                                sizeof(sLRF_PHYSICS_DESCRIPTOR*), cudaMemcpyDeviceToHost) );
    
    for (int i = 0; i < h_ptr->n_lrf_physics_descs; i ++) {
      g_phys_desc_h2d_map.insert(h_ptr->lrf_physics_descs + i,
                                  d_ptr_addr + i);
    }
  }  

  {//MOMENTUM FREEZE
    sDEV_PTR ptr_to_momentum_freeze_parms = get_class_member_ptr(d_sim_info, m_freeze_momentum_parms);
    cCOPY_TO_DEVICE::copy_to_device(IS_NOT_INTEGRAL_TYPE(), ptr_to_momentum_freeze_parms,
                                    sizeof(sFREEZE_MOMENTUM_PARMS), &h_ptr->m_freeze_momentum_parms);
  }

  {//GRF
    sDEV_PTR ptr_to_grf = get_class_member_ptr(d_sim_info, grf);
    cCOPY_TO_DEVICE::copy_to_device(IS_NOT_INTEGRAL_TYPE(), ptr_to_grf,
                                    sizeof(sGRF), &h_ptr->grf);    
  }
}

static void copy_msfl_meas_data(GPU::SURFEL d_ptr, ::sMSFL* h_ptr, const sMEAS_WINDOW_COLLECTION& windows) {
  auto dyn_data = const_cast<::sMSFL*>(h_ptr)->dynamics_data();  
  sMSFL_MEAS_CELL_PTR * const msfl_meas_cell_ptr = dyn_data->msfl_meas_cell_ptrs();
  sINT32 n_meas_windows = dyn_data->n_meas_cell_ptrs();
  if (n_meas_windows) {
    sDEV_PTR_TO_DEV_PTR dev_msfl_ptr( sDEV_PTR( (char*)d_ptr + ((char*)dyn_data->address_of_msfl_meas_cell_ptrs() - (char*)h_ptr)) );

    LRF_PHYSICS_DESCRIPTOR lrf = h_ptr->ref_frame_index() >= 0 ? &sim.lrf_physics_descs[h_ptr->ref_frame_index()] : nullptr;

    cCOPY_TO_DEVICE::allocate(dev_msfl_ptr, "surfel meas ptr",
                              sizeof(sMSFL_MEAS_CELL_PTR) * n_meas_windows,
                              msfl_meas_cell_ptr,
                              n_meas_windows,
                              ::lrf_is_rotating(lrf),
                              windows);
  }
}

__host__ static sVR_CONFIG * translate_vr_config_h2d(sVR_CONFIG* h_vr_config)
{
  if( h_vr_config ) {
    size_t idx = get_vr_config_idx(*h_vr_config);
    return g_device_vr_config_array + idx;
  }
  else {
    return nullptr;
  }
}

static VOID change_ptrs_h2d_vr_coarse_data(sHMBLK& h_ublk)
{
  tVR_UBLK_PTR<GPU::sMBLK> d_vr_fine_ublks[N_VOXELS_64];
  sVR_CONFIG* d_vr_configs[N_VOXELS_8];
  
  auto* h_vr_coarse_data = h_ublk.vr_coarse_data();

  ccDOTIMES(child_ublk, N_VOXELS_8) {
    d_vr_configs[child_ublk] = translate_vr_config_h2d(h_vr_coarse_data->vr_config(child_ublk));
  }

  ccDOTIMES(voxel, N_VOXELS_64) {
    auto h_vr_fine_ublk = h_vr_coarse_data->vr_fine_ublk(voxel);
    if (h_vr_fine_ublk) {
      d_vr_fine_ublks[voxel] = GPU::g_ublk_table.translate_vr_ublk_H2D(h_vr_fine_ublk);
    }
    else {
      d_vr_fine_ublks[voxel] = nullptr;
    }
  }

  //Overwrite host data since this will be eventually copied to the device and never used again  
  memcpy(h_vr_coarse_data->all_vr_configs(), &d_vr_configs, sizeof(sVR_CONFIG*)*N_VOXELS_8);
  memcpy(h_vr_coarse_data->all_vr_fine_ublks(), &d_vr_fine_ublks, sizeof(tVR_UBLK_PTR<GPU::sMBLK>)*N_VOXELS_64);
}

static VOID change_ptrs_h2d_vr_fine_data(sHMBLK& h_ublk) {

  auto* h_vr_fine_data = h_ublk.vr_fine_data();

  tVR_UBLK_PTR<GPU::sMBLK> d_vr_coarse_ublks[N_VOXELS_8];
  sVR_CONFIG* d_vr_configs[N_VOXELS_8];

  ccDOTIMES(child_ublk, N_VOXELS_8) {
    d_vr_configs[child_ublk] = translate_vr_config_h2d(h_vr_fine_data->vr_config(child_ublk));
  }

  ccDOTIMES(voxel, N_VOXELS_8) {
    auto h_vr_coarse_ublk = h_vr_fine_data->vr_coarse_ublk(voxel);
    if (h_vr_coarse_ublk) {
      d_vr_coarse_ublks[voxel] = GPU::g_ublk_table.translate_vr_ublk_H2D(h_vr_coarse_ublk);
    }
    else {
      d_vr_coarse_ublks[voxel] = nullptr;
    }
  }

  //Overwrite host data since this will be eventually copied to the device and never used again
  memcpy(h_vr_fine_data->all_vr_configs(), &d_vr_configs, sizeof(sVR_CONFIG*)*N_VOXELS_8);
  memcpy(h_vr_fine_data->all_vr_coarse_ublks(), &d_vr_coarse_ublks, sizeof(tVR_UBLK_PTR<GPU::sMBLK>)*N_VOXELS_8); 
}

static VOID change_ptrs_h2d_nearblock_data(sHMBLK& d_ptr) 
{
  auto surf_geom_data = d_ptr.surf_geom_data();
  if (surf_geom_data->beta_factors_present.any()) {
    surf_geom_data->beta_factors = sHD_SLAB_ALLOCATOR::get_device_ptr(surf_geom_data->beta_factors).get();
  }

  if (surf_geom_data->pbl_factors_2_present.any()) {
    surf_geom_data->pbl_factors_2 = sHD_SLAB_ALLOCATOR::get_device_ptr(surf_geom_data->pbl_factors_2).get();
  }

  if (surf_geom_data->pde_scale_factors_present.any()) {
    surf_geom_data->pde_scale_factors = sHD_SLAB_ALLOCATOR::get_device_ptr(surf_geom_data->pde_scale_factors).get();
  }

  //tagged surfels
  {
    for (int v = 0; v < sHMBLK::N_VOXELS; v++) {
      auto& h_tagged_sfl = surf_geom_data->closest_surfels[v];
      sMSFL* h_sfl = h_tagged_sfl.get_surfel();      
      if (h_sfl) {
        sMSFL* d_sfl = GPU::g_surfel_table.map_H2D(h_sfl);
        auto offset = h_tagged_sfl.get_offset();
        //Now we can swap in place
        h_tagged_sfl.clear();
        h_tagged_sfl.set_surfel(d_sfl);
        h_tagged_sfl.set_offset(offset);
      }
    }
  }
}

template<typename sPHYSICS_DESCRIPTOR_TYPE>
static void change_ptrs_h2d_base_dynamics_data(tUBLK_DYNAMICS_DATA<sPHYSICS_DESCRIPTOR_TYPE,
                                               HMBLK_SDFLOAT_TYPE_TAG>& dyn_data) {

  int n_child_ublks = dyn_data.is_mixed_type()? N_UBLKS_PER_MBLK : 1;
  
  for (int child_ublk = 0; child_ublk < n_child_ublks; child_ublk++) {
    //Physics desc
    {
      auto src_desc = dyn_data.physics_descriptor(child_ublk);
      auto device_desc = src_desc? (sPHYSICS_DESCRIPTOR_TYPE*) (g_phys_desc_h2d_map.map_H2D(src_desc)) : nullptr;
      dyn_data.set_physics_descriptor(child_ublk, device_desc);
    }
  }

}

static void change_ptrs_h2d_porous_dynamics_data(sHMBLK::sPOROUS_UBLK_DYNAMICS_DATA& dyn_data) {
  tPOROUS_UBLK_DYNAMICS_DATA<sPOROUS_MEDIA_PHYSICS_DESCRIPTOR, HMBLK_SDFLOAT_TYPE_TAG>& dyn_base_data = dyn_data;
  change_ptrs_h2d_base_dynamics_data(dyn_base_data);
  for (int i = 0; i < N_VOXELS_64; i++) {
    auto spec = dyn_data.porous_media_specs[i];
    if (dyn_data.porous_media_specs[i]) {
      dyn_data.porous_media_specs[i] = sHD_SLAB_ALLOCATOR::get_device_ptr(spec).get();
    }
  }
}

static void change_ptrs_h2d_meas_data_impl(sHMBLK& h_ublk, 
                                           sHMBLK::sUBLK_DYNAMICS_DATA* dyn_data,
                                           const sMEAS_WINDOW_COLLECTION& windows) 
{
  sINT32 n_meas_windows = dyn_data->n_meas_windows();  
  sMBLK_MEAS_CELL_PTR* h_mblk_meas_cell_ptr = dyn_data->meas_cell_ptrs();
  if (n_meas_windows) {
    LRF_PHYSICS_DESCRIPTOR lrf = h_ublk.ref_frame_index() >= 0 ? &sim.lrf_physics_descs[h_ublk.ref_frame_index()] : nullptr;
    BOOLEAN lrf_is_rotating = ::lrf_is_rotating(lrf);
    STP_SCALE scale = h_ublk.scale();
    ccDOTIMES(i, n_meas_windows) {
      asINT32 index = h_mblk_meas_cell_ptr[i].window_index();
      const sMEAS_WINDOW * window = windows[index];
      const_cast<MEAS_WINDOW>(window)->resolve_device_meas_cell_ptr( scale, h_mblk_meas_cell_ptr[i], lrf_is_rotating );
    }
  }

  GPU::Ptr<sMBLK_MEAS_CELL_PTR> d_mblk_meas_cell_ptr = GPU::malloc<sMBLK_MEAS_CELL_PTR>(n_meas_windows, "ublk_meas_ptr");
  GPU::copy_h2d(d_mblk_meas_cell_ptr, h_mblk_meas_cell_ptr, n_meas_windows);

  *dyn_data->address_of_mblk_meas_cell_ptrs() = d_mblk_meas_cell_ptr.get();
}

static void change_ptrs_h2d_meas_data(sHMBLK& h_ublk, const sMEAS_WINDOW_COLLECTION& windows)
{
  auto dyn_data = h_ublk.dynamics_data();
  MBLK_VOXEL_MASK dynamics_voxel_mask = h_ublk.fluid_like_voxel_mask;

  if (h_ublk.basic_fluid_voxel_mask.any()) {
    change_ptrs_h2d_meas_data_impl(h_ublk, dyn_data, windows);
  }

  if (h_ublk.basic_fluid_voxel_mask.any()) {
    dynamics_voxel_mask &= ~h_ublk.basic_fluid_voxel_mask;
    dyn_data = (sHMBLK::sUBLK_DYNAMICS_DATA*) dyn_data->next();
  }

  while(dynamics_voxel_mask.any()) {
    auto special_dyn_data = (sHMBLK::sSPECIAL_UBLK_DYNAMICS_DATA*) dyn_data;
    MBLK_VOXEL_MASK special_fluid_voxel_mask = special_dyn_data->voxel_mask();

    change_ptrs_h2d_meas_data_impl(h_ublk, dyn_data, windows);

    dynamics_voxel_mask &= ~special_fluid_voxel_mask;
    dyn_data = (sHMBLK::sUBLK_DYNAMICS_DATA*) dyn_data->next();
  }

}

static void change_ptrs_h2d_dynamics_data(sHMBLK& h_ptr,
                                          const sMEAS_WINDOW_COLLECTION& windows)
{  
  MBLK_VOXEL_MASK host_dynamics_voxel_mask = h_ptr.fluid_like_voxel_mask;
  MBLK_VOXEL_MASK host_basic_fluid_voxel_mask = h_ptr.basic_fluid_voxel_mask;

  {//Scoped since host_dyn_data is overwritten and it can be easily used downstream
    auto host_dyn_data = h_ptr.dynamics_data();
    if (host_basic_fluid_voxel_mask.any()) {
      change_ptrs_h2d_base_dynamics_data(*host_dyn_data);
      host_dyn_data = static_cast<sHMBLK::sUBLK_DYNAMICS_DATA*> (host_dyn_data->next());
      host_dynamics_voxel_mask &= ~host_basic_fluid_voxel_mask;
    }

    while (host_dynamics_voxel_mask.any()){
      auINT32 fluid_type = host_dyn_data->fluid_type();
      auto special_dyn_data = static_cast<sHMBLK::sSPECIAL_UBLK_DYNAMICS_DATA*>(host_dyn_data);
      auto special_fluid_voxel_mask = special_dyn_data->voxel_mask();

      switch (fluid_type) {
      case POROUS_FLUID_TYPE:
        {
          change_ptrs_h2d_porous_dynamics_data((sHMBLK::sPOROUS_UBLK_DYNAMICS_DATA&) *special_dyn_data);
          break;
        }
      default:
        msg_internal_error("Cannot handle special dyn data on the GPU yet. fluid_type %d", fluid_type);
      }
      host_dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      host_dyn_data = static_cast<sHMBLK::sUBLK_DYNAMICS_DATA*> (host_dyn_data->next());
    }
  }
  
  change_ptrs_h2d_meas_data(h_ptr, windows);

  //Finally swap the pointer to the dynamics data with the device pointer
  auto host_dyn_data = h_ptr.dynamics_data();
  sMBLK::sUBLK_DYNAMICS_DATA** ptr_to_dyn_data_ptr = h_ptr.get_data<UBLK_DYN_DATA_TYPE>();
  *ptr_to_dyn_data_ptr = (sMBLK::sUBLK_DYNAMICS_DATA*) sHD_SLAB_ALLOCATOR::get_device_ptr(host_dyn_data).get();
}

VOID change_ptrs_h2d(sSPLIT_INFO_64*& d_ptr,
                     sSPLIT_INFO_64* h_ptr) {
  d_ptr = sHD_SLAB_ALLOCATOR::get_device_ptr(h_ptr).get();
}

VOID change_ptrs_h2d(sHMBLK& d_ptr,
                     const sHMBLK& h_ptr,
                     const std::unordered_map<sHMBLK::UBLK_BOX, sMBLK::UBLK_BOX>& hd_boxes_map,
                     const sMEAS_WINDOW_COLLECTION& windows) {

  for (int i = 0; i < sHMBLK::N_UBLKS; i++) {
    if (!h_ptr.is_solid(i)) {
      auto& d_box_access = d_ptr.box_access(i);
      auto& h_box_access = ((sHMBLK&) h_ptr).box_access(i);
      d_box_access.m_ublk_box = (sHMBLK::UBLK_BOX) hd_boxes_map.at(h_box_access.m_ublk_box);
    }
  }

  if (!h_ptr.has_empty_split_info()) {
    change_ptrs_h2d(d_ptr.m_split_info, h_ptr.m_split_info);
  }
  
  if ( (h_ptr.is_solid() || h_ptr.is_ghost()) && !(h_ptr.is_vr_fine() || h_ptr.is_vr_coarse()) ) {
    return;
  }

  //Both the host and device headers point to the same host solver block data
  //swapping the contents of one changes it for the other
  {
    if (h_ptr.is_near_surface()) {
      change_ptrs_h2d_nearblock_data(d_ptr);
    }

    if (h_ptr.is_vr_fine()) {
      change_ptrs_h2d_vr_fine_data(d_ptr);
    } else if (h_ptr.is_vr_coarse()) {
      change_ptrs_h2d_vr_coarse_data(d_ptr);
    }

    if ( h_ptr.is_ghost() && h_ptr.is_vr_coarse())
    {
      return;
    }

    if (!h_ptr.is_vr_fine() && h_ptr.fluid_like_voxel_mask.any()) {
      change_ptrs_h2d_dynamics_data(d_ptr, windows);
    }
  }
}

VOID ::sMSFL_MEAS_CELL_PTR::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                           size_t n_bytes,
                                           const sMSFL_MEAS_CELL_PTR* h_ptr,
                                           int n_meas_windows,
                                           bool lrf_is_rotating,
                                           const sMEAS_WINDOW_COLLECTION& windows)
{
  sMSFL_MEAS_CELL_PTR * h_msfl_meas_cell_ptr = const_cast<sMSFL_MEAS_CELL_PTR*>(h_ptr);
  ccDOTIMES(i, n_meas_windows) {
    asINT32 index = h_msfl_meas_cell_ptr[i].window_index();
    const sMEAS_WINDOW * window = windows[index];
    const_cast<MEAS_WINDOW>(window)->resolve_device_meas_cell_ptr( h_msfl_meas_cell_ptr[i], lrf_is_rotating );
  }

  auto d_msfl_meas_cell_ptr = static_cast<sMSFL_MEAS_CELL_PTR*>(d_ptr.get());

  // trivial copy here
  cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);  
}


/*=================================================================================================
 * @func sUBLK_GROUP::copy_to_device
 *================================================================================================*/
__host__ VOID GPU::sUBLK_GROUP::copy_to_device(sDEV_PTR& d_ptr,
                                               size_t n_bytes,
                                               const sUBLK_GROUP* h_ptr) {

  GPU::sUBLK_GROUP* d_ublk_group = static_cast<GPU::sUBLK_GROUP*>(d_ptr.get());

  //Copy struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
  }

  //Copy the UBLK pointers in the group
  if (h_ptr->m_count != 0) {

    sDEV_PTR_TO_DEV_PTR ptr_to_ublk_ptr( get_class_member_ptr(d_ublk_group, m_ublks) );

    cCOPY_TO_DEVICE::allocate(ptr_to_ublk_ptr, "ublk",
                              h_ptr->m_count * sizeof(GPU::UBLK),
                              h_ptr->m_ublks);
  }

}


__HOST__  VOID sMSFL_INTERACTIONS::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                  size_t n_bytes,
                                                  const sMSFL_INTERACTIONS* h_ptr) {

  auto d_interactions = static_cast<sMSFL_INTERACTIONS*>(d_ptr.get());
  //Copy the struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
  }

  std::vector<GPU::UBLK> d_mblks;
  unsigned n_interacting_mblks = h_ptr->get_n_elements<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>();
  auto h_mblks = h_ptr->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>();
  for (unsigned i = 0; i < n_interacting_mblks; i++) {
    d_mblks.push_back(GPU::g_ublk_table.map_H2D((sHMBLK*) h_mblks[i]));
  }

  {
    size_t offset_to_interactions = h_ptr->offset<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>();
    sDEV_PTR ptr_to_interactions = sDEV_PTR( (void*) (reinterpret_cast<char*>(d_interactions) + offset_to_interactions));
    cCOPY_TO_DEVICE::trivial_copy(ptr_to_interactions,				    
                                  sizeof(GPU::UBLK) * n_interacting_mblks,
                                  &d_mblks[0]);
  }
}

__HOST__  VOID sMSFL_sMSFL_INTERACTIONS::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                        size_t n_bytes,
                                                        const sMSFL_sMSFL_INTERACTIONS* h_ptr) {

  auto d_interactions = static_cast<sMSFL_sMSFL_INTERACTIONS*>(d_ptr.get());
  //Copy the struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
  }

  std::vector<GPU::SURFEL> d_msfls;
  unsigned n_interacting_msfls = h_ptr->get_n_elements<MSFL_MSFL_INT_COMPONENTS::UNIQUE_SFLS>();
  const ::sMSFL* const * h_msfls = h_ptr->get<MSFL_MSFL_INT_COMPONENTS::UNIQUE_SFLS>();
  for (unsigned i = 0; i < n_interacting_msfls; i++) {
    d_msfls.push_back(GPU::g_surfel_table.map_H2D((::sMSFL*) h_msfls[i]));    
  }

  {
    size_t offset_to_interactions = h_ptr->offset<MSFL_MSFL_INT_COMPONENTS::UNIQUE_SFLS>();
    sDEV_PTR ptr_to_interactions = sDEV_PTR((reinterpret_cast<char*>(d_interactions) + offset_to_interactions));
    cCOPY_TO_DEVICE::trivial_copy(ptr_to_interactions,				    
                                  sizeof(GPU::SURFEL) * n_interacting_msfls,
                                  &d_msfls[0]);
  }
}


__HOST__ VOID ::sMSFL_S2S_ADVECT_DATA::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                      size_t n_bytes,
                                                      const ::sMSFL_S2S_ADVECT_DATA* h_ptr) {

  auto d_s2s_advect_data = reinterpret_cast<::sMSFL_S2S_ADVECT_DATA*>(d_ptr.get());

  //copy struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
  }

  //copy the interactions
  {
    sDEV_PTR_TO_DEV_PTR ptr_to_interactions( get_class_member_ptr(d_s2s_advect_data, m_surfel_interactions) );
    size_t n_bytes = h_ptr->m_surfel_interactions->size();
    cCOPY_TO_DEVICE::allocate(ptr_to_interactions, "surfel interaction",
                              n_bytes,
                              h_ptr->m_surfel_interactions);
  }

}

__HOST__ VOID tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG>::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                                           size_t n_bytes,
                                                                           const tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG>* hc_ptr) {
  auto h_ptr = const_cast<tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG>*>(hc_ptr);
  tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG> for_copy;
  for (int s = 0; s < 64; s++) {
    if (!h_ptr->m_clone_surfel[s].is_empty()) {
      auto& h_clone_surfel = h_ptr->m_clone_surfel[s];
      auto device_clone_surfel = GPU::g_surfel_table.map_H2D(h_clone_surfel.clone_surfel());
      for_copy.m_clone_surfel[s].set_clone_surfel(device_clone_surfel);
      for_copy.m_clone_surfel[s].set_clone_surfel_offset(h_clone_surfel.get_offset_in_msfl());
      if (h_clone_surfel.has_no_v2s_weights()) {
        for_copy.m_clone_surfel[s].set_has_no_v2s_weights();
      }
    }
  }


  size_t clone_offset = reinterpret_cast<char*>(&h_ptr->m_clone_surfel[0]) - reinterpret_cast<char*>(h_ptr);
  sDEV_PTR d_ptr_to_clones(reinterpret_cast<char*>(d_ptr.get()) + clone_offset);
  cCOPY_TO_DEVICE::trivial_copy(d_ptr_to_clones, sizeof(for_copy), &for_copy);
}

template<>
__HOST__  VOID ::sMSFL::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                       size_t n_bytes,
                                       const ::sMSFL* hc_ptr,
                                       const sMEAS_WINDOW_COLLECTION& windows) {

  ::sMSFL* h_ptr = const_cast<::sMSFL*>(hc_ptr);
  GPU::SURFEL d_sfl = static_cast<GPU::SURFEL>(d_ptr.get());

  cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);

  if(!h_ptr->is_ghost() && h_ptr->has_dynamics_data())
  {
    auto surface_physics_desc = h_ptr->dynamics_data()->m_surface_physics_desc;
    size_t offset = reinterpret_cast<char*>(&h_ptr->dynamics_data()->m_surface_physics_desc) -
      reinterpret_cast<char*>(h_ptr);
    sDEV_PTR_TO_DEV_PTR ptr_to_phys_desc( sDEV_PTR(((void*) (reinterpret_cast<char*>(d_sfl) + offset))) );
    sPHYSICS_DESCRIPTOR* d_ptr_on_host = g_phys_desc_h2d_map.map_H2D(surface_physics_desc);
    cCOPY_TO_DEVICE::trivial_copy(ptr_to_phys_desc, d_ptr_on_host);

    copy_msfl_meas_data((sMSFL*)d_ptr.get(), h_ptr, windows);
  }

  //S2S and V2S/S2V interaction data is allocated at a later stage to reduce device peak memory usage
}

/*=================================================================================================
 * @func sUBLK_GROUP::copy_to_device
 *================================================================================================*/
__host__ VOID GPU::sSURFEL_GROUP::copy_to_device(sDEV_PTR& d_ptr,
                                                 size_t n_bytes,
                                                 const sSURFEL_GROUP* h_ptr) {

  GPU::sSURFEL_GROUP* d_surfel_group = static_cast<GPU::sSURFEL_GROUP*>(d_ptr.get());

  //Copy struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);
  }

  //Copy the UBLK pointers in the group
  if (h_ptr->m_surfels) {

    sDEV_PTR_TO_DEV_PTR ptr_to_surfel_ptr( get_class_member_ptr(d_surfel_group, m_surfels) );

    cCOPY_TO_DEVICE::allocate(ptr_to_surfel_ptr, "surfel ptrs",
                              h_ptr->m_count * sizeof(GPU::SURFEL),
                              h_ptr->m_surfels);
  }

}

/*=================================================================================================
 * @func tSEED_VAR_SPEC::copy_on_device
 * We can use the same copy method for fluid and boundary seed var specs
 *================================================================================================*/
template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS>
__host__ VOID tSEED_VAR_SPEC<SEED_VAR_TYPE, N_SEED_VARS>::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                                         size_t n_bytes,
                                                                         const tSEED_VAR_SPEC *h_ptr,
                                                                         size_t n_seed_specs) {
  auto d_seed_var_spec= static_cast<tSEED_VAR_SPEC*>(d_ptr.get());
  //copy struct itself
  {
    cCOPY_TO_DEVICE::trivial_copy(d_ptr, n_bytes, h_ptr);    
  }

  static_assert(sizeof(DGF_SEED_VAR_TYPE) == 4);
  //copy seed_var_types
  for (int s = 0; s < n_seed_specs; s++)
    {
      sINT32 n_vars = h_ptr->n_vars;
      sDEV_PTR_TO_DEV_PTR ptr_to_dev_seed_var_types(get_class_member_ptr(d_seed_var_spec, seed_var_types));    
      cCOPY_TO_DEVICE::allocate(ptr_to_dev_seed_var_types, "fluid seed var specs",
                                n_vars * sizeof(asINT32),
                                (asINT32*) h_ptr->seed_var_types);
      h_ptr++;
    }
}

template
__host__ VOID sFLUID_SEED_VAR_SPEC::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                   size_t n_bytes,
                                                   const sFLUID_SEED_VAR_SPEC *h_ptr,
                                                   size_t n_seed_specs);

template
__host__ VOID sBOUNDARY_SEED_VAR_SPEC::copy_to_device(GPU::sDEV_PTR& d_ptr,
                                                      size_t n_bytes,
                                                      const sBOUNDARY_SEED_VAR_SPEC *h_ptr,
                                                      size_t n_seed_specs);

VOID copy_boundary_seeding_counts_d2h() {
  GPU::sSIM_INFO* d_sim_info;
  checkCudaErrors( cudaMemcpyFromSymbol(&d_sim_info,
                                        GPU::g_sim_info,
                                        sizeof(GPU::sSIM_INFO*), 0,
                                        cudaMemcpyDeviceToHost) );
      
  auto ptr_n_boundary_sfls = get_class_member_ptr(d_sim_info, n_boundary_surfels);
  checkCudaErrors( cudaMemcpy(&sim.n_boundary_surfels,
                              ptr_n_boundary_sfls.get(),
                              sizeof(SHOB_ID),
                              cudaMemcpyDeviceToHost) );

  auto ptr_n_seeded_boundary_surfels = get_class_member_ptr(d_sim_info, n_seeded_boundary_surfels);
  checkCudaErrors( cudaMemcpy(&sim.n_seeded_boundary_surfels,
                              ptr_n_seeded_boundary_surfels.get(),
                              sizeof(SHOB_ID),
                              cudaMemcpyDeviceToHost) );  
}
