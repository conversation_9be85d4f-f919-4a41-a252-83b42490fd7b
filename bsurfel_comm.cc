/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "sleep.h"
#include "thread_run.h"
#include "bsurfel_comm.h"

sBSURFEL_COMM_SEND_FSET g_bsurfel_comm_send_fset;
sBSURFEL_COMM_RECV_FSET g_bsurfel_comm_recv_fset;

sUBLK_IB_BF_SEND_FSET g_ublk_ib_bf_send_fset;
sUBLK_IB_BF_RECV_FSET g_ublk_ib_bf_recv_fset;

sUBLK_IB_BF_BCAST_SEND_FSET g_ublk_ib_bf_bcast_send_fset;
sUBLK_IB_BF_BCAST_RECV_FSET g_ublk_ib_bf_bcast_recv_fset;

void request_ublk_ib_bf_sends(SCALE scale)
{
  DO_UBLK_IB_BF_SEND_GROUPS_OF_SCALE(group, scale) {
    g_strand_mgr.m_send_queue->add_sp_entry(group, 0, FALSE);
  }
}

void request_ublk_ib_bf_bcast_sends(SCALE scale, BOOLEAN do_near_surface)
{
  DO_UBLK_IB_BF_BCAST_SEND_GROUPS_OF_SCALE(group, scale) {
    if (group->m_is_near_surface == do_near_surface) {
      g_strand_mgr.m_send_queue->add_sp_entry(group, 0, FALSE);
    }
  }
}

void post_bsurfel_sends()
{
  DO_BSURFEL_SEND_GROUPS(group) {
    g_strand_mgr.m_send_queue->add_sp_entry(group, 0, FALSE);
  }
}

asINT32 create_bsurfel_send_groups()
{
  asINT32 n_send_buffers = 0;
  if (sim.is_movb_sim()) {
    for (cNEIGHBOR_SP nsp : g_strand_mgr.m_neighbor_sp_map) {
      sBSURFEL_SEND_GROUP* bsurfel_send_group = g_bsurfel_comm_send_fset.find_or_create_group(nsp);
      n_send_buffers++;
    }
  }

  return n_send_buffers;
}

asINT32 create_ublk_ib_bf_send_groups()
{
  asINT32 n_send_buffers = 0;
  if (sim.is_movb_sim()) {
    ccDOTIMES(scale, sim.num_scales) {
      DO_NEARBLK_RECV_GROUPS_OF_SCALE(ngroup, scale) {
        sUBLK_IB_BF_SEND_GROUP * ublk_ib_bf_send_group = g_ublk_ib_bf_send_fset.find_or_create_group(ngroup);
        ublk_ib_bf_send_group->allocate_send_buffer();
        n_send_buffers++;
      }
      DO_FARBLK_RECV_GROUPS_OF_SCALE(fgroup, scale) {
        sUBLK_IB_BF_SEND_GROUP * ublk_ib_bf_send_group = g_ublk_ib_bf_send_fset.find_or_create_group(fgroup);
        ublk_ib_bf_send_group->allocate_send_buffer();
        n_send_buffers++;
      }

      // BCAST
      DO_UBLK_SEND_GROUPS_OF_SCALE(group, scale) {
        sUBLK_IB_BF_BCAST_SEND_GROUP * ublk_ib_bf_bcast_send_group = g_ublk_ib_bf_bcast_send_fset.find_or_create_group(group);
        ublk_ib_bf_bcast_send_group->allocate_send_buffer();
        n_send_buffers++;
      }
    }
  }

  return n_send_buffers;
}

VOID sBSURFEL_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  LOG_MSG("BSURFEL_COMM", LOG_FUNC) << "Send Bsurfels to " << dest_rank();
  // make sure the send buffer and the count is available
  complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);
  complete_mpi_request_while_processing_cp_messages(&m_second_send_request, MPI_SLEEP_LONG);

  m_send_buffer.clear();

  int n_bsurfels = m_bsurfels_to_send.size();
  if (n_bsurfels > 0) {
    pack(m_send_buffer, &n_bsurfels);
    ccDOTIMES(i, n_bsurfels) {
      sBSURFEL * bsurfel = m_bsurfels_to_send[i];
      LOG_MSG_IF(bsurfel->id() == 14828, "BSURFEL_COMM", "TS", g_timescale.m_time, "From", my_proc_id, "To", dest_rank()).format("Sending bsurfel {}", bsurfel->id());
      bsurfel->fill_send_buffer(m_dest_sp, m_send_buffer);
      delete bsurfel;
    }

    for (int i = 0; i < sim.n_tires; i++) {
      sim.tires[i].pack_vertices(m_dest_sp, m_send_buffer);
    }
  }

  m_bsurfels_to_send.clear();
  m_n_send_bytes = m_send_buffer.size();

  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::BSURFEL_COUNT>(0);
#if defined(_EXA_IMPI)
  MPI_Issend(&m_n_send_bytes, 1, MPI_INT, dest_rank(), tag,
            eMPI_sp_cp_comm, &m_send_request);
#else
  MPI_Isend(&m_n_send_bytes, 1, MPI_INT, dest_rank(), tag,
            eMPI_sp_cp_comm, &m_send_request);
#endif

  LOG_MSG("STRAND_SWITCHING", "n_bsurfels", n_bsurfels, LOG_ATTR(m_n_send_bytes), "tag", fmt::hex(tag), "dest rank", dest_rank()) << "Sending bsurfel count";

  if (n_bsurfels > 0) {
    int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::BSURFEL>(0);
#if defined(_EXA_IMPI)
    MPI_Issend(&m_send_buffer[0], m_n_send_bytes, MPI_BYTE, dest_rank(),
              tag, eMPI_sp_cp_comm, &m_second_send_request);
#else
    MPI_Isend(&m_send_buffer[0], m_n_send_bytes, MPI_BYTE, dest_rank(),
              tag, eMPI_sp_cp_comm, &m_second_send_request);
#endif
    LOG_MSG("STRAND_SWITCHING", "size", m_n_send_bytes, "tag", fmt::hex(tag), "dest rank", dest_rank()) << "Sending bsurfels";
  }
  LOG_MSG("BSURFEL_COMM", LOG_FUNC) << "Send Bsurfels to " << dest_rank() << " ended";
}

std::unique_ptr<STP_PROC[]> sBSURFEL_RECV_GROUP::m_total_nsps_for_scale;
std::unique_ptr<STP_PROC[]> sBSURFEL_RECV_GROUP::m_current_unpacking_nsp_for_scale;

VOID sBSURFEL_RECV_GROUP::post_recv()
{
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::BSURFEL_COUNT>(0);
  xMPI_Irecv(&m_recvsize, 1, MPI_INT, source_rank(), tag, eMPI_sp_cp_comm, &m_recv_request);
  LOG_MSG("STRAND_SWITCHING", "tag", fmt::hex(tag), "source rank", source_rank()) << "Posting bsurfel count recv";
}

VOID sBSURFEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  unpack(active_solver_mask);
}

bool sBSURFEL_RECV_GROUP::is_ready_to_be_unpacked()
{
  if (m_current_unpacking_nsp_for_scale[m_scale] == m_order_id) {
    int flag = 0;
    MPI_Test(&m_recv_msg.m_request, &flag, MPI_STATUS_IGNORE);
    if (flag) {
      m_current_unpacking_nsp_for_scale[m_scale]++;
      if (m_current_unpacking_nsp_for_scale[m_scale] == m_total_nsps_for_scale[m_scale]) {
        m_current_unpacking_nsp_for_scale[m_scale] = 0;
      }
      return true;
    }
  }

  return false;
}


VOID sBSURFEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  if (m_recvsize > 0) {
    LOG_MSG("BSURFEL_COMM", "source rank", source_rank(), "size", m_recvsize) << "Unpacking bsurfel recv";
    std::vector<unsigned char>::iterator it = m_recv_buffer.begin();
    int n_bsurfels;
    ::unpack(it, &n_bsurfels);
    for (int i = 0; i < n_bsurfels; i++) {
      sBSURFEL * bsurfel = new sBSURFEL();
      bsurfel->init(it, g_ublk_table[STP_FLOW_REALM], sim.char_density, sim.lrf_physics_descs,
                    sim.movb_physics_descs, g_meas_windows);
      LOG_MSG_IF(bsurfel->id() == 14305, "BSURFEL_COMM", LOG_TS, "From", source_rank(), "To", my_proc_id).format("Unpacking bsurfel {}", bsurfel->id());
      if (bsurfel->is_active()) {
        BSURFEL_GROUP_TYPE new_group_type = bsurfel->activate_bsurfel_neighbor_ublks(true);
        bsurfel->move_self_to_new_group(new_group_type);
      } else {
        bsurfel->move_self_to_new_group(FRINGE_BSURFEL_GROUP_TYPE);
      }
    }

    LOG_MSG("BSURFEL_COMM", LOG_FUNC) << "Unpacking vertices";
    for (int i = 0; i < sim.n_tires; i++) {
      sim.tires[i].unpack_vertices(it);
    }

  LOG_MSG("BSURFEL_COMM", LOG_FUNC) << "Finished unpacking bsurfels";
  }

  set_unpacked();
}

bool sBSURFEL_RECV_GROUP::is_recv_request_null()
{
  if (!m_count_received) {
    return m_recv_request == MPI_REQUEST_NULL;
  }
  else {
    bool second_recv_finished = m_second_recv_request == MPI_REQUEST_NULL;
    if (second_recv_finished) {
      m_count_received = false;
    }
    return second_recv_finished;
  }
}

bool sBSURFEL_RECV_GROUP::is_recv_ready()
{
  int flag;
  if (!m_count_received) {
    MPI_Test(&m_recv_request, &flag, MPI_STATUS_IGNORE);
    if (flag) { // count has been received
      LOG_MSG("STRAND_SWITCHING", "source_rank", source_rank(), "size", m_recvsize) << "Recv bsurfel count";
      if (m_recvsize > 0) {   // post the data receive
        LOG_MSG("STRAND_SWITCHING") << "Posting second bsurfel recv";
        m_recv_buffer.resize(m_recvsize);
        int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::BSURFEL>(0);
        MPI_Irecv(&m_recv_buffer[0], m_recvsize, MPI_BYTE, source_rank(),
                  tag, eMPI_sp_cp_comm, &m_second_recv_request);
        m_count_received = true;
        return false; // tell the world that we aren't ready for unpacking
      }
      else {
        LOG_MSG("STRAND_SWITCHING") << "bsurfel recv complete, 0 bsurfels";
        return true; // nothing to unpack, but signal we are done
      }
    }
    else {
      return false; // count has not been received yet
    }
  }
  else { // check if the data is here yet
    MPI_Test(&m_second_recv_request, &flag, MPI_STATUS_IGNORE);
    if (flag) { // data has arrived
      return true; // ready for unpacking
    }
    else { // data has not arrived yet, not ready for unpacking
      return false;
    }
  }
}

//namespace
//{
//
//template<typename T> struct sFLOAT_TO_INT;
//
//template <> struct sFLOAT_TO_INT<double> {
//  typedef uINT64 type;
//};
//
//template <> struct sFLOAT_TO_INT<float> {
//  typedef uINT32 type;
//};
//
//};


VOID sUBLK_DYNAMIC_MME_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  BOOLEAN is_2d = sim.is_2d();
  INT_TYPE total_quantum_count = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
  recv_buffer++;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(recv_buffer);
  m_solver_timestep_index_mask = *solver_index_mask;
  recv_buffer++;
//  recv_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  // LOG_MSG("BSURFEL_COMM", "scale", m_scale, "n_ublks", total_quantum_count, "source rank", source_rank(), "near", m_is_near_surface) << "unpacking ublks dynamic mme";

  for (int i = 0; i < total_quantum_count; i++) {
    INT_TYPE next_quantum = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
    recv_buffer++;
    auto& quantum = m_quantums[next_quantum];

    process_quantum(quantum, recv_buffer, active_solver_mask);

    // This happens here because it is the first time in a timestep
    // that all ghost bsurfel_interacting ublks are touched
    quantum.m_ublk->reset_ib_body_force();
  }

  set_unpacked();
  // LOG_MSG("BSURFEL_COMM", "scale", m_scale, "n_ublks", total_quantum_count, "source rank", source_rank(), "near", m_is_near_surface) << "finished unpacking ublks dynamic mme";
}

VOID sUBLK_DYNAMIC_MME_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;

  INT_TYPE * quantum_count = nullptr;
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = 0;
  if (m_is_near_surface) {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_MME>(m_scale);
  }
  else {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_MME>(m_scale);
  }

  // complete the previous send if any
  complete_mpi_request_while_processing_cp_messages(&m_send_msg.m_request, MPI_SLEEP_LONG);

  // store the count at the top
  quantum_count = reinterpret_cast<INT_TYPE*>(send_buffer);
  *quantum_count = 0;
  send_buffer++;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(send_buffer);
  *solver_index_mask = m_solver_timestep_index_mask;
  send_buffer++;

//  send_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  // LOG_MSG("BSURFEL_COMM",  LOG_ATTR(m_scale), "tag", fmt::hex(tag), LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "sending ublks dynamic_mme";

  INT_TYPE nth_quantum = 0;
  for(auto& quantum: m_quantums) {
    UBLK ublk = quantum.m_ublk;
    if (ublk->is_bsurfel_interacting()) {
      // sending the quantum index
      *(reinterpret_cast<INT_TYPE*>(send_buffer)) =  nth_quantum;
      send_buffer++;

      ublk->fill_mme_send_buffer(sim.init_solver_mask, send_buffer, m_solver_timestep_index_mask);
      (*quantum_count)++;
    }
    nth_quantum++;
  }


  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();

  LOG_MSG("STRAND_SWITCHING",  LOG_ATTR(m_scale), LOG_ATTR(true_sendsize), "tag", fmt::hex(tag), LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "finished sending ublks dynamic_mme";

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
  m_solver_timestep_index_mask ^= active_solver_mask;
}

VOID sUBLK_IB_BF_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;

  INT_TYPE * quantum_count = NULL;
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = 0;
  if (m_is_near_surface) {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_IB_BF>(m_scale);
  }
  else {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_IB_BF>(m_scale);
  }

  // complete the previous send if any
  complete_mpi_request_while_processing_cp_messages(&m_send_msg.m_request, MPI_SLEEP_LONG);

  quantum_count = reinterpret_cast<INT_TYPE*>(send_buffer);
  *quantum_count = 0;
  send_buffer++;

  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), "tag", fmt::hex(tag), "n_ublks", *quantum_count, LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "sending ublks ib_bf";

  INT_TYPE nth_quantum = 0;
  for(auto& quantum: m_quantums) {
    UBLK ublk = quantum.m_ublk;
    if (ublk->is_bsurfel_interacting()) {
      *(reinterpret_cast<INT_TYPE*>(send_buffer)) = nth_quantum;
      send_buffer++;
      // LOG_MSG_IF(ublk->id() == 14828, "BSURFEL_COMM", LOG_TS, LOG_FUNC) << "filling send buffer";
      ublk->fill_ib_bf_send_buffer(send_buffer);
      LOG_MSG("STRAND_SWITCHING", LOG_ATTR(m_scale), "tag", fmt::hex(tag), "n_ublks", *quantum_count, LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "sending ublks ib_bf";

      // TODO: get rid of this by eliminating the bcast
      // no dynamics happens on ghosts, so reset it here. The Bcast will
      // fill the ghost back in with the reduced amount
      ublk->reset_ib_body_force();
      (*quantum_count)++;
    }
    nth_quantum++;
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
  m_solver_timestep_index_mask ^= active_solver_mask;
  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(true_sendsize), "tag", fmt::hex(tag), "n_ublks", *quantum_count, LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "finished sending ublks ib_bf";
}

std::unique_ptr<STP_PROC[]> sUBLK_IB_BF_RECV_GROUP::m_total_nsps_for_scale_and_type;
std::unique_ptr<STP_PROC[]> sUBLK_IB_BF_RECV_GROUP::m_current_unpacking_nsp_for_scale_and_type;

VOID sUBLK_IB_BF_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  sdFLOAT *recv_buffer =  m_recv_msg.buffer();
  INT_TYPE total_quantum_count = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
  recv_buffer++;

  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(m_recvsize), LOG_ATTR(total_quantum_count), LOG_ATTR(m_is_near_surface)).format("unpacking ublks ib_bf from {}", m_source_sp);

  for (int i = 0; i <  total_quantum_count; i++) {
    INT_TYPE next_quantum = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
    recv_buffer++;
    m_quantums[next_quantum].m_ublk->expand_ib_bf_recv_buffer(recv_buffer);
  }

  set_unpacked();
  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(m_recvsize), LOG_ATTR(total_quantum_count), LOG_ATTR(m_is_near_surface)).format("finished unpacking ublks ib_bf from {}", m_source_sp);
}

VOID sUBLK_IB_BF_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  unpack(active_solver_mask);
}

bool sUBLK_IB_BF_RECV_GROUP::is_ready_to_be_unpacked()
{
  // LOG_MSG("BSURFEL_COMM",LOG_FUNC,LOG_ATTR(m_scale),LOG_ATTR(source_nsp())).format("idx {} o {} c {} t {}",m_index, m_order_id, m_current_unpacking_nsp_for_scale_and_type[m_index], m_total_nsps_for_scale_and_type[m_index]);
  if (m_current_unpacking_nsp_for_scale_and_type[m_index] == m_order_id) {
    int flag = 0;
    MPI_Test(&m_recv_msg.m_request, &flag, MPI_STATUS_IGNORE);
    if (flag) {
      m_current_unpacking_nsp_for_scale_and_type[m_index]++;
      if (m_current_unpacking_nsp_for_scale_and_type[m_index] == m_total_nsps_for_scale_and_type[m_index]) {
        m_current_unpacking_nsp_for_scale_and_type[m_index] = 0;
      }
      return true;
    }
  }

  return false;
}

/******************************************************************************
 * BCAST FUNCTIONS
 *****************************************************************************/

VOID sUBLK_IB_BF_BCAST_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;

  INT_TYPE * quantum_count = nullptr;
  sdFLOAT *send_buffer =  m_send_msg.buffer();
  sdFLOAT *beg         =  m_send_msg.buffer();
  int tag = 0;
  if (m_is_near_surface) {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_IB_BF_BCAST>(m_scale);
  }
  else {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_IB_BF_BCAST>(m_scale);
  }


  // complete the previous send if any
  complete_mpi_request_while_processing_cp_messages(&m_send_msg.m_request, MPI_SLEEP_LONG);

  quantum_count = reinterpret_cast<INT_TYPE*>(send_buffer);
  *quantum_count = 0;
  send_buffer++;

  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), "tag", fmt::hex(tag), "n_ublks", *quantum_count, LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "sending ublks ib_bf bcast";
  INT_TYPE nth_quantum = 0;
  for(auto& quantum: m_quantums) {
    UBLK ublk = quantum.m_ublk;
    if (ublk->needs_to_send_ib_bf_data()) {
      *(reinterpret_cast<INT_TYPE*>(send_buffer)) = nth_quantum;
      send_buffer++;
      // LOG_MSG_IF( ublk->id() == 5686,"BSURFEL_COMM",LOG_TS,LOG_FUNC ) << "bcast fill send buffer";
      ublk->fill_ib_bf_send_buffer(send_buffer);
      (*quantum_count)++;
      ublk->unset_send_ib_bf_data();
    }
    nth_quantum++;
  }

  asINT32 true_sendsize = send_buffer - beg;

  m_send_msg.settag(tag);
  m_send_msg.set_nelems(true_sendsize);


  g_exa_sp_cp_comm.isend(m_send_msg);
  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(true_sendsize), "tag", fmt::hex(tag), "n_ublks", *quantum_count, LOG_ATTR(dest_rank()), LOG_ATTR(m_is_near_surface)) << "finished sending ublks ib_bf bcast";

  m_solver_timestep_index_mask ^= active_solver_mask;
}

VOID sUBLK_IB_BF_BCAST_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  sdFLOAT *recv_buffer =  m_recv_msg.buffer();
  INT_TYPE total_quantum_count = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
  recv_buffer++;

  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(m_recvsize), LOG_ATTR(total_quantum_count), LOG_ATTR(source_rank()), LOG_ATTR(m_is_near_surface)) << "unpacking ublks ib_bf bcast";

  for (int i = 0; i <  total_quantum_count; i++) {
    INT_TYPE next_quantum = *(reinterpret_cast<INT_TYPE*>(recv_buffer));
    recv_buffer++;
    m_quantums[next_quantum].m_ublk->expand_ib_bf_recv_buffer(recv_buffer);
    // LOG_MSG_IF( ublk->id() == 5686,"BSURFEL_COMM",LOG_TS,LOG_FUNC ) << "bcast expand recv buffer";
  }
  // LOG_MSG("BSURFEL_COMM", LOG_ATTR(m_scale), LOG_ATTR(m_recvsize), LOG_ATTR(total_quantum_count), LOG_ATTR(source_rank()), LOG_ATTR(m_is_near_surface)) << "finished unpacking ublks ib_bf bcast";

  set_unpacked();
}

VOID sUBLK_IB_BF_BCAST_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  unpack(active_solver_mask);
}

void do_preliminary_bsurfel_comm()
{

  DO_BSURFEL_SEND_GROUPS(send_group) {
    send_group->send(sim.init_solver_mask);
  }

  DO_BSURFEL_RECV_GROUPS(recv_group) {
    recv_group->post_recv();
    while (!recv_group->is_recv_ready()) {
      sp_thread_sleep(MPI_SLEEP_SHORT);
    }

    recv_group->unpack(sim.init_solver_mask);
    recv_group->reset_unpacked();
  }

}


