/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Surfel dynamics (functional sets and groups)
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "surfel_dyn_sp.h"
#include "sim.h"
#include "sp_timers.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "mirror.h"
#include "gpu_exprlang.h"
#include "gpu_host_include.h"
#include "phys_type_map.h"
#include "wsurfel_comm.h"
#include TPI_COMMON_H
#include PHYSICS_H

extern asINT32 SIMULATOR_NAMESPACE::g_surfel_dynamics_data_sizes[N_MAX_SURFEL_PHYSICS_TYPES];
using SIMULATOR_NAMESPACE::g_surfel_dynamics_data_sizes;

VOID initialize_dynamics_data(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dynamics_data,
                              STP_PHYSTYPE_TYPE sim_phys_type, SURFEL surfel,
                              sPHYSICS_DESCRIPTOR *surface_phys_desc,
                              dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                              cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data,
                              bool invert_normal)
{

  if (!surface_phys_desc->all_parameters_sharable ||
       surface_phys_desc->some_constant_parameter_in_need_of_eval) {
    
    sdFLOAT *normal = cast_as_regular_array(surfel->normal);
    sdFLOAT inverted_normal[3];
    if (invert_normal) {
      inverted_normal[0] = -surfel->normal[0];
      inverted_normal[1] = -surfel->normal[1];
      inverted_normal[2] = -surfel->normal[2];
      normal = inverted_normal;
    }
    surface_phys_desc->eval_space_and_table_varying_parameter_program(cast_as_regular_array(surfel->centroid),
                                                                      normal,
                                                                      boundary_eqn_error_handler);
  }

    switch (sim_phys_type) {
#if BUILD_5G_LATTICE
      case STP_NOSLIP_SURFEL_TYPE_ID:
      {
        sNOSLIP_5G_SURFEL_DATA *dyn_data = (sNOSLIP_5G_SURFEL_DATA *) dynamics_data;
        sNOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *pd = (sNOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *)surface_phys_desc;

        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sNOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sNOSLIP_5G_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sNOSLIP_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sNOSLIP_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sNOSLIP_5G_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_ANGULAR_NOSLIP_SURFEL_TYPE_ID:
      {
        sANGULAR_NOSLIP_5G_SURFEL_DATA *dyn_data =
          (sANGULAR_NOSLIP_5G_SURFEL_DATA *) dynamics_data;
        sANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_NOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sANGULAR_NOSLIP_5G_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sANGULAR_NOSLIP_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_NOSLIP_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_NOSLIP_5G_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_SLIP_SURFEL_TYPE_ID:
      {
        sLINEAR_NOSLIP_5G_SURFEL_DATA *dyn_data =
          (sLINEAR_NOSLIP_5G_SURFEL_DATA *) dynamics_data;
        sLINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_NOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sLINEAR_NOSLIP_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_NOSLIP_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_NOSLIP_5G_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
      {
        sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA *dyn_data =
          (sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA *) dynamics_data;
        sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_SOURCE_SURFEL_TYPE_ID:
      {
        sSOURCE_5G_SURFEL_DATA *dyn_data = (sSOURCE_5G_SURFEL_DATA *) dynamics_data;
        sSOURCE_5G_SURFEL_PHYSICS_DESCRIPTOR *pd = (sSOURCE_5G_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSOURCE_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSOURCE_5G_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSOURCE_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSOURCE_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_MASS_FLUX_SURFEL_TYPE_ID:
      {
        sMASS_FLUX_5G_SURFEL_DATA *dyn_data = (sMASS_FLUX_5G_SURFEL_DATA *) dynamics_data;
        sMASS_FLUX_5G_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sMASS_FLUX_5G_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sMASS_FLUX_5G_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sMASS_FLUX_5G_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sMASS_FLUX_5G_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sMASS_FLUX_5G_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
#else
      case STP_SLIP_SURFEL_TYPE_ID:
      {
        sSLIP_SURFEL_DATA *dyn_data = (sSLIP_SURFEL_DATA *) dynamics_data;
        sSLIP_SURFEL_PHYSICS_DESCRIPTOR *pd = (sSLIP_SURFEL_PHYSICS_DESCRIPTOR *)surface_phys_desc;
        // is_validated is set to 1 in init()

        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sSLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sSLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_NOSLIP_SURFEL_TYPE_ID:
      {
        sNOSLIP_SURFEL_DATA *dyn_data = (sNOSLIP_SURFEL_DATA *) dynamics_data;
        sNOSLIP_SURFEL_PHYSICS_DESCRIPTOR *pd = (sNOSLIP_SURFEL_PHYSICS_DESCRIPTOR *)surface_phys_desc;

        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sNOSLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sNOSLIP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sNOSLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sNOSLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sNOSLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_ANGULAR_SLIP_SURFEL_TYPE_ID:
      {
        sANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;
        sANGULAR_SLIP_SURFEL_DATA *dyn_data =
          (sANGULAR_SLIP_SURFEL_DATA *) dynamics_data;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_SLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sANGULAR_SLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_SLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_SLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_ANGULAR_NOSLIP_SURFEL_TYPE_ID:
      {
        sANGULAR_NOSLIP_SURFEL_DATA *dyn_data =
          (sANGULAR_NOSLIP_SURFEL_DATA *) dynamics_data;
        sANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_NOSLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sANGULAR_NOSLIP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sANGULAR_NOSLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_NOSLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_NOSLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_SLIP_SURFEL_TYPE_ID:
      {
        sLINEAR_SLIP_SURFEL_DATA *dyn_data =
          (sLINEAR_SLIP_SURFEL_DATA *) dynamics_data;
        sLINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_SLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sLINEAR_SLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_SLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_SLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_NOSLIP_SURFEL_TYPE_ID:
      {
        sLINEAR_NOSLIP_SURFEL_DATA *dyn_data =
          (sLINEAR_NOSLIP_SURFEL_DATA *) dynamics_data;
        sLINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_NOSLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sLINEAR_NOSLIP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sLINEAR_NOSLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_NOSLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_NOSLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_VEL_SLIP_SURFEL_TYPE_ID:
      {
        sVEL_SLIP_SURFEL_DATA *dyn_data =
          (sVEL_SLIP_SURFEL_DATA *) dynamics_data;
        sVEL_SLIP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sVEL_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sVEL_SLIP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sVEL_SLIP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sVEL_SLIP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sVEL_SLIP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }

      case STP_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sSLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sSLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sSLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSLIP_THERMAL_RESIST_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sSLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sNOSLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sNOSLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sNOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sNOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sNOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sNOSLIP_THERMAL_RESIST_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sNOSLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->lb_data()->boundary_condition_type = sNOSLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sANGULAR_SLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_SLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sLINEAR_SLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_SLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
      {
        sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA *dyn_data =
          (sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA *) dynamics_data;
        sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
        surfel->lb_data()->boundary_condition_type = sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::bc_type();
        surfel->set_not_free_slip_wall(sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }

      case STP_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sSLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sSLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->lb_data()->boundary_condition_type = sSLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sSLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sNOSLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sNOSLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sNOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sNOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sNOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->lb_data()->boundary_condition_type = sNOSLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_not_free_slip_wall(sNOSLIP_FIXED_TEMP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sNOSLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sNOSLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sANGULAR_SLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_SLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sLINEAR_SLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_SLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA *dyn_data =
          (sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }

      case STP_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sNOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sNOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(dyn_data->is_viscous_wall());
        surfel->set_wall(sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(TRUE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(TRUE);
        break;
      }
      case STP_LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data =
          (sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_wall());
        surfel->set_inlet_or_outlet(sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        surfel->lb_data()->boundary_condition_type = sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(TRUE);
        surfel->set_moving(TRUE);
        break;
      }


      case STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
      {
        sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA *dyn_data =
          (sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA *) dynamics_data;
        sSTATIC_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSTATIC_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        //dyn_data->check_parm_bounds(pd, surfel, &seed_from_meas_data);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
      {
        sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA *dyn_data =
          (sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA *) dynamics_data;
        sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_STAG_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
      {
        sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA *dyn_data =
          (sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA *) dynamics_data;
        sSTAG_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSTAG_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
      {
        sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA *dyn_data =
          (sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA *) dynamics_data;
        sSTAG_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sSTAG_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }



      case STP_MASS_FLUX_SURFEL_TYPE_ID:
      {
        sMASS_FLUX_SURFEL_DATA *dyn_data =
          (sMASS_FLUX_SURFEL_DATA *) dynamics_data;
        sMASS_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sMASS_FLUX_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sMASS_FLUX_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sMASS_FLUX_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sMASS_FLUX_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sMASS_FLUX_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sMASS_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_MASS_FLOW_SURFEL_TYPE_ID:
      {
        sMASS_FLOW_SURFEL_DATA *dyn_data =
          (sMASS_FLOW_SURFEL_DATA *) dynamics_data;
        sMASS_FLOW_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sMASS_FLOW_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sMASS_FLOW_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data); // does not do anything
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sMASS_FLOW_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sMASS_FLOW_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sMASS_FLOW_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sMASS_FLUX_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }

      case STP_FIXED_VEL_SURFEL_TYPE_ID:
      {
        sFIXED_VEL_SURFEL_DATA *dyn_data =
          (sFIXED_VEL_SURFEL_DATA *) dynamics_data;
        sFIXED_VEL_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sFIXED_VEL_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sFIXED_VEL_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sFIXED_VEL_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sFIXED_VEL_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sFIXED_VEL_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sFIXED_VEL_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_TURB_VEL_SURFEL_TYPE_ID:
      {
        sTURB_VEL_SURFEL_DATA *dyn_data = (sTURB_VEL_SURFEL_DATA *) dynamics_data;
        sFIXED_VEL_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sFIXED_VEL_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sTURB_VEL_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sTURB_VEL_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sTURB_VEL_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sTURB_VEL_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sTURB_VEL_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }

      case STP_SOURCE_SURFEL_TYPE_ID:
      {
        sSOURCE_SURFEL_DATA *dyn_data = (sSOURCE_SURFEL_DATA *) dynamics_data;
        sSOURCE_SURFEL_PHYSICS_DESCRIPTOR *pd = (sSOURCE_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc;

        surfel->set_inlet_or_outlet(sSOURCE_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sSOURCE_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sSOURCE_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sSOURCE_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSOURCE_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
      case STP_TURB_SOURCE_SURFEL_TYPE_ID:
      {
        sTURB_SOURCE_SURFEL_DATA *dyn_data = (sTURB_SOURCE_SURFEL_DATA *) dynamics_data;
        sSOURCE_SURFEL_PHYSICS_DESCRIPTOR *pd = (sSOURCE_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc;

        surfel->set_inlet_or_outlet(sTURB_SOURCE_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data);
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sTURB_SOURCE_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sTURB_SOURCE_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sTURB_SOURCE_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sSOURCE_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }

      case STP_PASS_THRU_SURFEL_TYPE_ID:
      {
        sPASS_THRU_SURFEL_DATA *dyn_data =
          (sPASS_THRU_SURFEL_DATA *) dynamics_data;
        sPASS_THRU_SURFEL_PHYSICS_DESCRIPTOR *pd =
          (sPASS_THRU_SURFEL_PHYSICS_DESCRIPTOR *) surface_phys_desc ;

        surfel->set_inlet_or_outlet(sPASS_THRU_SURFEL_DATA::is_inlet_or_outlet());
        dyn_data->init( pd, surfel, sum_pgram_volumes);
        dyn_data->seed_from_meas(pd, surfel, &seed_from_meas_data); // does not do anything
        dyn_data->m_surface_physics_desc = pd;
        dyn_data->m_dynamics_type = sPASS_THRU_SURFEL_DATA::dyn_surfel_type();
        surfel->set_not_free_slip_wall(sPASS_THRU_SURFEL_DATA::is_viscous_wall());
        surfel->set_wall(sPASS_THRU_SURFEL_DATA::is_wall());
        //surfel->lb_data()->boundary_condition_type = sPASS_THRU_SURFEL_DATA::bc_type();
        surfel->set_slip_surfel(FALSE);
        surfel->set_noslip_surfel(FALSE);
        surfel->set_moving(FALSE);
        break;
      }
#endif
#if !BUILD_5G_LATTICE
      case STP_CONDUCTION_ADIABATIC_SURFEL_TYPE_ID:
      {
        sCONDUCTION_ADIABATIC_SURFEL_DATA *dyn_data = (CONDUCTION_ADIABATIC_SURFEL_DATA) dynamics_data;
        CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_ADIABATIC_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
      case STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE_ID:
      {
        sCONDUCTION_FIXED_TEMP_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_TEMP_SURFEL_DATA *) dynamics_data;
        CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
      case STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE_ID:
      {
        sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA *) dynamics_data;
        CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
      case STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
      {
        sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA *) dynamics_data;
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
      case STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE_ID:
      {
        sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA *dyn_data = (sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA *) dynamics_data;
        CONDUCTION_COUPLED_THERMAL_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_COUPLED_THERMAL_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
      case STP_CONDUCTION_CONTACT_SURFEL_TYPE_ID:
      {
        sCONDUCTION_CONTACT_SURFEL_DATA *dyn_data = (sCONDUCTION_CONTACT_SURFEL_DATA *) dynamics_data;
        CONDUCTION_CONTACT_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_CONTACT_SURFEL_PHYSICS_DESCRIPTOR)surface_phys_desc;
        dyn_data->init(pd, surfel, sum_pgram_volumes);
        dyn_data->m_dynamics_type = sCONDUCTION_CONTACT_SURFEL_DATA::dyn_surfel_type();
        // CONDUCTION-CHECK: do we need to set the boundary condition type?
        break;
      }
#endif
     default:
        msg_internal_error("Invalid surfel dynamics type (%d)", sim_phys_type);
        break;
    }
}

#if 0
static BOOLEAN does_any_surface_pd_match(PHYSICS_DESCRIPTOR pd) {
  asINT32 n_spds = sim.n_flow_surface_physics_descs;
  PHYSICS_DESCRIPTOR pds = sim.flow_surface_physics_descs;

  for (int i=0; i<2; i++, n_spds = sim.n_thermal_surface_physics_descs, pds = sim.thermal_surface_physics_descs) {
    ccDOTIMES(pd_index, n_spds) {
      if (pd == pds)
        return TRUE;
      pds++;
    }
  }
  return FALSE;
}
#endif

uINT32 num_dependent_coupling_surfels(PHYSICS_DESCRIPTOR pd) {
  //Coupling skips odd and non-flow surfels and must be skipped also here when counting the pd dependencies
  uINT32 n_dependent_surfels = 0;
  if (pd->is_surface_physics_descriptor()) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        BOOLEAN is_odd_surfel = (surfel->is_even_or_odd() && !surfel->is_even());
        if (!is_odd_surfel && pd == ((PHYSICS_DESCRIPTOR) surfel->dynamics_data()->physics_descriptor())) {
          n_dependent_surfels++;
        }
      }
    }
  }
  return n_dependent_surfels;
}

VOID assign_coupling_temp_to_dependent_surfels(COUPLING_MODEL coupling_model,
                                               sFLOAT *coupling_temp_data)
{
  SURFEL clone_surfel;
  ccDOTIMES(var,coupling_model->n_vars) {
    for (DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = coupling_model->dependent_physics_descs;
        dependent_pd != NULL; dependent_pd = dependent_pd->next) {
      PHYSICS_DESCRIPTOR pd = dependent_pd->physics_desc;
      DO_SCALES_FINE_TO_COARSE(scale) {
        DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
          // coupling info only available to regular & even surfels
          if (surfel->dynamics_data()->physics_descriptor() != pd || (surfel->is_even_or_odd() && !surfel->is_even())) {
            continue;
          }
          // if it is a VR region, clone_surfel is a valid pointer to the odd clone and we can copy data to it
          clone_surfel = surfel->clone_surfel();
          // if only some of the coupling faces are to be initialized from the
          // foreign model, the data for those faces is skipped. This is a
          // limitation of the fixed coupling data size allocated initially on the CP
          // as well as the SP side.
          if (0 == g_timescale.m_time && !pd->init_from_coupling_model_p) {
            coupling_temp_data++;
            continue;
          }
          // @@@ We probably should ultimately consider packaging this loop into a virtual
          // function associated with the surfel dyn group. Otherwise as we add support
          // for other types of coupling this will get ugly, large, and difficult to
          // maintain.
          switch (coupling_model->var_types[var]) {
          case TPI_VAR_TYPE_SURFACE_TEMPERATURE: {
            // update surfel temp_bc field
            sFLOAT coup_temp = *coupling_temp_data++;
            surfel->t_data()->temp_bc = coup_temp;
            if (clone_surfel != nullptr) { 
              clone_surfel->t_data()->temp_bc = coup_temp; 
            }
            break; 
          }
          default:
            msg_internal_error("Surface coupling computations for variable type %d not implemented",
                               coupling_model->var_types[var]);
            break;
          }
        }
      }
    }
  }
}

VOID append_dependent_coupling_surfel_ids(PHYSICS_DESCRIPTOR pd, STP_SURFEL_ID **ids)
{
  //Coupling skips odd and non-flow surfels and must be skipped also here when assigning ids
  if (pd->is_surface_physics_descriptor()) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        BOOLEAN is_odd_surfel = (surfel->is_even_or_odd() && !surfel->is_even());
        if (!is_odd_surfel && pd == ((PHYSICS_DESCRIPTOR) surfel->dynamics_data()->physics_descriptor())) {
          **ids = surfel->id();
          (*ids)++;
        }
      }
    }
  }
}

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_REINIT : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data,
                   PHYSICS_DESCRIPTOR pd,
                   sSURFEL* surfel) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->reinit(pd, surfel);
  }
};

static VOID reinitialize_dependent_surfel_data(sSURFEL *surfel, sSURFEL_DYNAMICS_DATA *dynamics_data, PHYSICS_DESCRIPTOR pd) {

  if (!pd->all_parameters_sharable) {
    pd->eval_space_and_table_varying_parameter_program(cast_as_regular_array(surfel->centroid),
                                                       cast_as_regular_array(surfel->normal),
                                                       boundary_eqn_error_handler);
  }

  if (surfel->is_conduction_shell()) {
    auto ptd = surfel->shell_conduction_data()->shell_config_pd();
    if (!ptd->all_parameters_sharable) {
      ptd->eval_space_and_table_varying_parameter_program(cast_as_regular_array(surfel->centroid), 
                                                          cast_as_regular_array( surfel->normal),
                                                          boundary_eqn_error_handler);
    }
  }

  exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_REINIT>(dynamics_data, pd, surfel);
}

VOID reinitialize_dynamics_data_for_dependent_surfels(PHYSICS_DESCRIPTOR pd) 
{
#ifdef BUILD_GPU
  if (pd->is_surface_physics_descriptor()) {
    GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE params_type = (pd->all_parameters_sharable ? GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE::ALL_SHARABLE :
                                                                                     GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE::SPACE_AND_TABLE);
    for (SURFEL_GROUP_TYPE group_type : nSHOB_CATEGORIES::DYN_SURFEL_FLOW_TYPES) {
      MSFL_FSET msfl_group_fset = g_msfl_groups[group_type];
      if (msfl_group_fset && msfl_group_fset->n_groups()) {
        DO_SCALES_FINE_TO_COARSE(scale) {
          DO_MSFL_GROUPS_OF_SCALE_TYPE(msfl_group, scale, group_type) {
            GPU::g_exprlang_mgr.fill_surfel_varying_data(pd, group_type, scale, params_type,msfl_group->m_dest_sp.nsp());
            const auto range = GPU::get_surfel_group_range((SURFEL_GROUP_TYPE)group_type, scale, msfl_group->m_dest_sp.nsp());
            GPU::reinitialize_dependent_surfel_data((SURFEL_GROUP_TYPE)group_type,range.data());
          }
        }
      }
    }
  }
#else
  if (pd->is_surface_physics_descriptor() || pd->is_shell_physics_descriptor()) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        sSURFEL_DYNAMICS_DATA *dynamics_data = surfel->dynamics_data();
        if (pd == dynamics_data->physics_descriptor()) {
          reinitialize_dependent_surfel_data(surfel, dynamics_data, pd);
        }
      }
      DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
        sSURFEL_DYNAMICS_DATA *front_dynamics_data = surfel->front_dynamics_data();
        if (pd == front_dynamics_data->physics_descriptor()) {
          reinitialize_dependent_surfel_data(surfel, front_dynamics_data, pd);
        }
        if (surfel->has_distinct_back_bc()) {
          sSURFEL_DYNAMICS_DATA *back_dynamics_data = front_dynamics_data->next_dynamics_data();
          if (pd == back_dynamics_data->physics_descriptor()) {
            reinitialize_dependent_surfel_data(surfel, back_dynamics_data, pd);
          }
        }
      }
    }
  }
#endif
}

//Only certain surfel types are subject to validation and consistency checks
asINT32 dyn_surfel_type_from_stp_phy_type_for_validation(STP_PHYSTYPE_TYPE sim_phys_type) {
  switch (sim_phys_type) {
#if BUILD_5G_LATTICE
    case STP_NOSLIP_SURFEL_TYPE_ID: 
      return sNOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
    case STP_ANGULAR_NOSLIP_SURFEL_TYPE_ID: 
      return sANGULAR_NOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
    case STP_LINEAR_SLIP_SURFEL_TYPE_ID: 
      return sLINEAR_NOSLIP_5G_SURFEL_DATA::dyn_surfel_type();
    case STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_ID: 
      return sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::dyn_surfel_type();
    case STP_SOURCE_SURFEL_TYPE_ID: 
      return sSOURCE_5G_SURFEL_DATA::dyn_surfel_type();
    case STP_MASS_FLUX_SURFEL_TYPE_ID: 
      return sMASS_FLUX_5G_SURFEL_DATA::dyn_surfel_type();
#else
case STP_SLIP_SURFEL_TYPE_ID: 
    return sSLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_NOSLIP_SURFEL_TYPE_ID:
    return sNOSLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_SLIP_SURFEL_TYPE_ID:
    return sANGULAR_SLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_SLIP_SURFEL_TYPE_ID:
    return sLINEAR_SLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_VEL_SLIP_SURFEL_TYPE_ID:
    return sVEL_SLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_NOSLIP_SURFEL_TYPE_ID:
    return sANGULAR_NOSLIP_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_NOSLIP_SURFEL_TYPE_ID:
    return sLINEAR_NOSLIP_SURFEL_DATA::dyn_surfel_type();

  case STP_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
  case STP_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sNOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA::dyn_surfel_type();

  case STP_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sNOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();

  case STP_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();

  case STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
    return sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::dyn_surfel_type();
  case STP_STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
    return sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::dyn_surfel_type();
  case STP_STAG_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
    return sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::dyn_surfel_type();
  case STP_STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
    return sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::dyn_surfel_type();
  case STP_MASS_FLUX_SURFEL_TYPE_ID:
    return sMASS_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_FIXED_VEL_SURFEL_TYPE_ID:
    return sFIXED_VEL_SURFEL_DATA::dyn_surfel_type();
  case STP_SOURCE_SURFEL_TYPE_ID:
    return sSOURCE_SURFEL_DATA::dyn_surfel_type();
  case STP_PASS_THRU_SURFEL_TYPE_ID:
    return sPASS_THRU_SURFEL_DATA::dyn_surfel_type();
  case STP_MASS_FLOW_SURFEL_TYPE_ID:
    return sMASS_FLOW_SURFEL_DATA::dyn_surfel_type();
  case STP_TURB_VEL_SURFEL_TYPE_ID:
    return sTURB_VEL_SURFEL_DATA::dyn_surfel_type();
  case STP_TURB_SOURCE_SURFEL_TYPE_ID:
    return sTURB_SOURCE_SURFEL_DATA::dyn_surfel_type();
#endif
  case STP_CONDUCTION_ADIABATIC_SURFEL_TYPE_ID:
    return sCONDUCTION_ADIABATIC_SURFEL_DATA::dyn_surfel_type();
  case STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE_ID:
    return sCONDUCTION_FIXED_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE_ID:
    return sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA::dyn_surfel_type();
  case STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA::dyn_surfel_type();
  case STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE_ID:
    return sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA::dyn_surfel_type();
  case STP_CONDUCTION_CONTACT_SURFEL_TYPE_ID:
    return sCONDUCTION_CONTACT_SURFEL_DATA::dyn_surfel_type();

    default:
      return INVALID_SURFEL_TYPE;
      break;
  }
}


/*--------------------------------------------------------------------------*
 * Surfel dynamics groups: DGF file processing
 *--------------------------------------------------------------------------*/


VOID boundary_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                                cSTRING cdi_desc_var_name, vFLOAT value,
                                vFLOAT aux_value,		// min or max
                                STP_GEOM_VARIABLE surfel_centroid[3])
{
  CHARACTER err_buffer[128];

  cSTRING face_name = "unknown";
  cSTRING bc_name   = "unknown";
  cSTRING case_var = physics_var->name;
  asINT32 scale = -1;

  switch (type) {
  case EQN_ERROR_FPE:
    {
      simerr_report_error_code(SP_EER_FPE_FACE, scale, surfel_centroid, bc_name,
                               face_name, cdi_desc_var_name, case_var, value);
      break;
    }

  case EQN_ERROR_TOO_LARGE:
    {
      simerr_report_error_code(SP_EER_TOO_BIG_FACE, scale, surfel_centroid, bc_name,
                               face_name, cdi_desc_var_name, case_var, value, aux_value);
      break;
    }
  case EQN_ERROR_TOO_SMALL:
    {
      simerr_report_error_code(SP_EER_TOO_SMALL_FACE, scale, surfel_centroid, bc_name,
                               face_name, cdi_desc_var_name, case_var, value, aux_value);
      break;
    }
  default:
    msg_internal_error("Unrecognized eqn error type.");
    break;
  }
}

/*--------------------------------------------------------------------------*
 * Surfel dynamics groups: Operations
 *--------------------------------------------------------------------------*/

VOID mirror_to_real_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask,
                                   sSURFEL_V2S_DATA *real_surfel_v2s_data,
                                   sSURFEL_V2S_DATA *mirror_surfel_v2s_data)
{
  if (real_surfel->has_v2s_data()) {
    if (active_solver_mask & LB_ACTIVE) {
      sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data->v2s_lb_data();
      real_surfel->v2s_lb_data()->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
                                                         mirror_config->m_velocity_mirror_sign_factor,
                                                         mirror_config->m_reflected_latvec_pair);
    }
    if (active_solver_mask & KE_PDE_ACTIVE) {
      sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data->v2s_turb_data();
      real_surfel->v2s_turb_data()->reflect_to_real_surfel(mirror_v2s_turb_data);
    }
    if (active_solver_mask & T_PDE_ACTIVE) {
      sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data->v2s_t_data();
      real_surfel->v2s_t_data()->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                                        mirror_config->m_velocity_mirror_sign_factor,
                                                        mirror_config->m_reflected_latvec_pair,
                                                        sim.is_T_S_solver_type_lb());
    }
    if (active_solver_mask & UDS_PDE_ACTIVE) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data->v2s_uds_data(nth_uds);
	real_surfel->v2s_uds_data(nth_uds)->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
							  mirror_config->m_velocity_mirror_sign_factor,
							  mirror_config->m_reflected_latvec_pair,
							  (sim.uds_solver_type == LB_UDS));
      }
#if BUILD_D19_LATTICE
      if (sim.is_pf_model) {
        sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data->v2s_pf_data();
        real_surfel->v2s_pf_data()->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                          mirror_config->m_velocity_mirror_sign_factor,
                                                          mirror_config->m_reflected_latvec_pair);
      }
#endif
    }
  } else {
    if (active_solver_mask & LB_ACTIVE) {
      sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data->v2s_lb_data();
      sSURFEL_V2S_LB_DATA *real_v2s_lb_data = real_surfel_v2s_data->v2s_lb_data();
      real_v2s_lb_data->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
                                               mirror_config->m_velocity_mirror_sign_factor,
                                               mirror_config->m_reflected_latvec_pair);
    }
    if (active_solver_mask & KE_PDE_ACTIVE) {
      sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data->v2s_turb_data();
      sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = real_surfel_v2s_data->v2s_turb_data();
      real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
    }
    if (active_solver_mask & T_PDE_ACTIVE) {
      sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data->v2s_t_data();
      sSURFEL_V2S_T_DATA *real_v2s_t_data = real_surfel_v2s_data->v2s_t_data();
      real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                              mirror_config->m_velocity_mirror_sign_factor,
                                              mirror_config->m_reflected_latvec_pair,
                                              sim.is_T_S_solver_type_lb());
    }
    if (active_solver_mask & UDS_PDE_ACTIVE) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data->v2s_uds_data(nth_uds);
	sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = real_surfel_v2s_data->v2s_uds_data(nth_uds);
	real_v2s_uds_data->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
						mirror_config->m_velocity_mirror_sign_factor,
						mirror_config->m_reflected_latvec_pair,
						(sim.uds_solver_type == LB_UDS));
      }
#if BUILD_D19_LATTICE
      if (sim.is_pf_model) {
        sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data->v2s_pf_data();
        sSURFEL_V2S_PF_DATA *real_v2s_pf_data = real_surfel_v2s_data->v2s_pf_data();
        real_v2s_pf_data->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                mirror_config->m_velocity_mirror_sign_factor,
                                                mirror_config->m_reflected_latvec_pair);
      }
#endif
    }
  }
  real_surfel->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair,
                                                         mirror_config->m_velocity_mirror_sign_factor,
                                                         active_solver_mask);
}

VOID conduction_mirror_to_real_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask)
{
  real_surfel->conduction_reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                         mirror_config->m_reflected_latvec_pair,
                                                         mirror_config->m_velocity_mirror_sign_factor,
                                                         active_solver_mask);
}

VOID real_to_mirror_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask,
                                   sSURFEL_V2S_DATA *real_surfel_v2s_data,
                                   sSURFEL_V2S_DATA *mirror_surfel_v2s_data)
{
  if (real_surfel->has_v2s_data()) {
    if (active_solver_mask & LB_ACTIVE) {
      sSURFEL_V2S_LB_DATA *real_v2s_lb_data = real_surfel->v2s_lb_data();
      sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data->v2s_lb_data();
      mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                   mirror_config->m_reflected_latvec_pair);
    }
    if (active_solver_mask & KE_PDE_ACTIVE) {
      sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = real_surfel->v2s_turb_data();
      sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data->v2s_turb_data();
      mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
    }
    if (active_solver_mask & T_PDE_ACTIVE) {
      sSURFEL_V2S_T_DATA *real_v2s_t_data = real_surfel->v2s_t_data();
      sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data->v2s_t_data();
      mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                  mirror_config->m_reflected_latvec_pair,
                                                  sim.is_T_S_solver_type_lb());
    }
    if (active_solver_mask & UDS_PDE_ACTIVE) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = real_surfel->v2s_uds_data(nth_uds);
	sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data->v2s_uds_data(nth_uds);
	mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
						    mirror_config->m_velocity_mirror_sign_factor,
						    mirror_config->m_reflected_latvec_pair,
						    (sim.uds_solver_type == LB_UDS));
      }
#if BUILD_D19_LATTICE
      if (sim.is_pf_model) {
        sSURFEL_V2S_PF_DATA *real_v2s_pf_data = real_surfel->v2s_pf_data();
        sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data->v2s_pf_data();
        mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                    mirror_config->m_velocity_mirror_sign_factor,
                                                    mirror_config->m_reflected_latvec_pair);
      }
#endif  
    }

  } else {
    if (active_solver_mask & LB_ACTIVE) {
      sSURFEL_V2S_LB_DATA *real_v2s_lb_data = real_surfel_v2s_data->v2s_lb_data();
      sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data->v2s_lb_data();
      mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                   mirror_config->m_reflected_latvec_pair);
    }
    if (active_solver_mask & KE_PDE_ACTIVE) {
      sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = real_surfel_v2s_data->v2s_turb_data();
      sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data->v2s_turb_data();
      mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
    }
    if (active_solver_mask & T_PDE_ACTIVE) {
      sSURFEL_V2S_T_DATA *real_v2s_t_data = real_surfel_v2s_data->v2s_t_data();
      sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data->v2s_t_data();
      mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                  mirror_config->m_reflected_latvec_pair,
                                                  sim.is_T_S_solver_type_lb());
    }
    if (active_solver_mask & UDS_PDE_ACTIVE) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = real_surfel_v2s_data->v2s_uds_data(nth_uds);
	sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data->v2s_uds_data(nth_uds);
	mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
						    mirror_config->m_velocity_mirror_sign_factor,
						    mirror_config->m_reflected_latvec_pair,
						    (sim.uds_solver_type == LB_UDS));
      }
#if BUILD_D19_LATTICE
      if (sim.is_pf_model) {
        sSURFEL_V2S_PF_DATA *real_v2s_pf_data = real_surfel_v2s_data->v2s_pf_data();
        sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data->v2s_pf_data();
        mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                    mirror_config->m_velocity_mirror_sign_factor,
                                                    mirror_config->m_reflected_latvec_pair);
      }
#endif
    }
  }
  mirror_surfel->reflect_from_real_surfel_to_mirror_surfel(real_surfel, latvec_mask,
                                                           mirror_config->m_reflected_latvec_pair,
                                                           mirror_config->m_velocity_mirror_sign_factor,
                                                           active_solver_mask);
}

VOID conduction_real_to_mirror_surfel_reflect(SURFEL real_surfel,
                                   SURFEL mirror_surfel,
                                   SURFEL_MIRROR_CONFIG mirror_config,
                                   STP_LATVEC_MASK latvec_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask)
{
  mirror_surfel->conduction_reflect_from_real_surfel_to_mirror_surfel(real_surfel, latvec_mask,
                                                           mirror_config->m_reflected_latvec_pair,
                                                           mirror_config->m_velocity_mirror_sign_factor,
                                                           active_solver_mask);
}

static VOID print_surfel_in_states_t(char *print_string, sSURFEL_V2S_T_DATA *surfel_t_data, SURFEL surfel) {
  printf("%s T %ld S %d IN %e %e %e %e \n%e %e %e %e %e\n", print_string, g_timescale.m_time, surfel->id(),
         float(surfel_t_data->m_in_states_t[0]), float(surfel_t_data->m_in_states_t[1]),
         float(surfel_t_data->m_in_states_t[2]), float(surfel_t_data->m_in_states_t[3]),
         float(surfel_t_data->m_in_states_t[4]), float(surfel_t_data->m_in_states_t[5]),
         float(surfel_t_data->m_in_states_t[6]), float(surfel_t_data->m_in_states_t[7]),
         float(surfel_t_data->m_in_states_t[8]));
}
static VOID print_surfel_out_flux_t(sSURFEL_V2S_T_DATA *surfel_t_data, SURFEL surfel) {
  printf("T %ld S %d IN %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
         float(surfel_t_data->m_out_flux_t[0]), float(surfel_t_data->m_out_flux_t[1]),
         float(surfel_t_data->m_out_flux_t[2]), float(surfel_t_data->m_out_flux_t[3]),
         float(surfel_t_data->m_out_flux_t[4]), float(surfel_t_data->m_out_flux_t[5]),
         float(surfel_t_data->m_out_flux_t[6]), float(surfel_t_data->m_out_flux_t[7]),
         float(surfel_t_data->m_out_flux_t[8]));
}
VOID print_surfel_in_states(char *print_string, sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("%s time %ld S %d\n", print_string, g_timescale.m_time, surfel->id());
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  printf("density %14.9e momentum %14.9e %14.9e %14.9e cfn %14.9e delp_factor %14.9e \n",
         float(v2s_lb_data->m_density),
         float(v2s_lb_data->m_momentum[0]),
         float(v2s_lb_data->m_momentum[1]),
         float(v2s_lb_data->m_momentum[2]),
         float(v2s_lb_data->m_cf_n),
	 float(v2s_lb_data->m_delp_factor));
#else
  printf("density %14.9e momentum %14.9e %14.9e %14.9e cfn %14.9e \n",
         float(v2s_lb_data->m_density),
         float(v2s_lb_data->m_momentum[0]),
         float(v2s_lb_data->m_momentum[1]),
         float(v2s_lb_data->m_momentum[2]),
         float(v2s_lb_data->m_cf_n));
#endif

#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
  printf("%14.9e %14.9e %14.9e %14.9e %14.9e %14.9e\n",
         float(v2s_lb_data->m_u_bar[0]),
         float(v2s_lb_data->m_u_bar[1]),
         float(v2s_lb_data->m_u_bar[2]),
         float(v2s_lb_data->m_grads.gradp[0]),
         float(v2s_lb_data->m_grads.gradp[1]),
         float(v2s_lb_data->m_grads.gradp[2]));
#else
  printf("%14.9e %14.9e %14.9e %14.9e %14.9e %14.9e\n",
         float(v2s_lb_data->m_u_bar[0][0]),
         float(v2s_lb_data->m_u_bar[0][1]),
         float(v2s_lb_data->m_u_bar[0][2]),
         float(v2s_lb_data->m_grads.gradp[0]),
         float(v2s_lb_data->m_grads.gradp[1]),
         float(v2s_lb_data->m_grads.gradp[2]));
#endif
  printf("%e %e %e %e %e %e \n%e %e %e\n",
         float(v2s_lb_data->m_in_states[0]), float(v2s_lb_data->m_in_states[1]),
         float(v2s_lb_data->m_in_states[2]), float(v2s_lb_data->m_in_states[3]),
         float(v2s_lb_data->m_in_states[4]), float(v2s_lb_data->m_in_states[5]),
         float(v2s_lb_data->m_in_states[6]), float(v2s_lb_data->m_in_states[7]),
         float(v2s_lb_data->m_in_states[8]));
}
VOID print_surfel_out_flux(sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("T %ld S %d OUT %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
         float(v2s_lb_data->m_out_flux[0]), float(v2s_lb_data->m_out_flux[1]),
         float(v2s_lb_data->m_out_flux[2]), float(v2s_lb_data->m_out_flux[3]),
         float(v2s_lb_data->m_out_flux[4]), float(v2s_lb_data->m_out_flux[5]),
         float(v2s_lb_data->m_out_flux[6]), float(v2s_lb_data->m_out_flux[7]),
         float(v2s_lb_data->m_out_flux[8]));
}
sdFLOAT dummy_surfel_states[9] =
{
  2.111155270e+04, 2.111155270e+04, 2.111155270e+04,
  1.055577635e+04, 1.055577635e+04, 1.055577635e+04, 1.055577635e+04, 1.055577635e+04, 1.055577635e+04
};


static BOOLEAN check_bc_surfel_turb_spec(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                         asINT32 &i) { // note reference to i

  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_VIA)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LENGTH_SCALE)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_KE)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_DISSIPATION)){
    return FALSE;
  }
  else{
    return TRUE;
  }
}


asINT32 sMASS_FLUX_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                        cSTRING name, 
                                                        BOOLEAN is_called_from_derived_class) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a mass flux boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_MASS_FLUX 
                           : CDI_PHYS_TYPE_NEW_MASS_FLUX, 
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_START_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESPONSE_TIME)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NEW_MASS_FLUX_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NEW_MASS_FLUX_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NEW_MASS_FLUX_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_USE_REFL_DAMPING)
      || (!is_called_from_derived_class
          && sim.is_turb_model
          && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sMASS_FLOW_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                        cSTRING name,
                                                        BOOLEAN is_called_from_derived_class) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a mass flow boundary",
                           sim.is_turb_model
                           ? CDI_PHYS_TYPE_LES_MASS_FLOW
                           : CDI_PHYS_TYPE_MASS_FLOW,
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_START_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESPONSE_TIME)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MASS_FLOW_RATE)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_USE_REFL_DAMPING)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_INLET_AREA)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REVERSE_MASS_FLOW)
      || ((!is_called_from_derived_class
      && sim.is_turb_model
      && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i)) {
    msg_internal_error("%s is not consistent with CDI physics type %d.",
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sFIXED_VEL_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                        cSTRING name,
                                                        BOOLEAN is_called_from_derived_class) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a velocity boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_FIXED_VEL 
                           : CDI_PHYS_TYPE_FIXED_VEL, 
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_START_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESPONSE_TIME)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_N)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_IS_VEL_N)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_IMPORT_FLUCTUATIONS)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_FREQ_MIN)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_FREQ_MAX)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_DELTA_T)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TABULAR_DATA_ID)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_INLET_AREA)
      || (!is_called_from_derived_class
          && sim.is_turb_model
          && (!check_bc_surfel_turb_spec(ptd, i )))
      ||  ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.",
                        name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sSOURCE_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                     cSTRING name,
                                                     BOOLEAN is_called_from_derived_class ) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a pressure and velocity boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_SOURCE_SURFEL 
                           : CDI_PHYS_TYPE_SOURCE_SURFEL, 
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_START_FRAME_NUM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NEW_PRESSURE)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_N)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_IS_VEL_N)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FAR_FIELD_BC)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_IMPORT_FLUCTUATIONS)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_INTENSITY_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_LEN_SCALE_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_FREQ_MIN)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_FREQ_MAX)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TURB_DELTA_T)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TABULAR_DATA_ID)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_INLET_AREA)
      || (!is_called_from_derived_class
          && sim.is_turb_model
          && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
		       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sPRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                                cSTRING name,
                                                                BOOLEAN is_called_from_derived_class ) {
  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if (
#if !BUILD_5G_LATTICE
      (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_FRAME_NUM) ||
      (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_START_FRAME_NUM) ||
#endif
      (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
#if !BUILD_5G_LATTICE      
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
#endif
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESPONSE_TIME)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NEW_PRESSURE)
      || ptd->n_continuous_dp < i){
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                        name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sPASS_THRU_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                        cSTRING name,
                                                        BOOLEAN is_called_from_derived_class ) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a pass through boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_PASS_THRU 
                           : CDI_PHYS_TYPE_PASS_THRU, 
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RELATIVE_HUMIDITY)
      || (!is_called_from_derived_class
          && sim.is_turb_model
          && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sSTATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                                       cSTRING name,
                                                                       BOOLEAN is_called_from_derived_class) {
  if (is_called_from_derived_class)
    do_check_cdi_phys_type("a free direction static pressure boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FREE_DIR 
                           : CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FREE_DIR, 
                           ptd->cdi_physics_type);

  asINT32 i = sPRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, is_called_from_derived_class);

  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DIST_TO_REFL_SURFACE)
      || (!is_called_from_derived_class
          && sim.is_turb_model 
          && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }

  return i;
}


asINT32 sSTAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                                     cSTRING name,
                                                                     BOOLEAN is_called_from_derived_class ) {
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a free direction total pressure boundary",
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_STAG_PRESSURE_FREE_DIR 
                           : CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FREE_DIR, 
                           ptd->cdi_physics_type);

  asINT32 i = sPRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, is_called_from_derived_class);
  if ((!is_called_from_derived_class
       && sim.is_turb_model
       && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name, 
                                                                 BOOLEAN is_called_from_derived_class) {
  asINT32 i = sPRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, is_called_from_derived_class);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                                        cSTRING name,
                                                                        BOOLEAN is_called_from_derived_class ) {
  asINT32 i=  sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, is_called_from_derived_class);
  if (is_called_from_derived_class)
    do_check_cdi_phys_type("a fixed direction static pressure boundary", 
                           sim.is_turb_model 
                           ? CDI_PHYS_TYPE_LES_STATIC_PRESSURE_FIXED_DIR 
                            : CDI_PHYS_TYPE_NEW_STATIC_PRESSURE_FIXED_DIR, 
                           ptd->cdi_physics_type);
  if ((is_called_from_derived_class 
       && sim.is_turb_model 
       && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }

  return i;
}


asINT32 sSTAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                                                                      cSTRING name,
                                                                      BOOLEAN is_called_from_derived_class ) {
  asINT32 i=  sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, is_called_from_derived_class);
  if (is_called_from_derived_class)
    do_check_cdi_phys_type("a fixed direction total pressure boundary",
                           sim.is_turb_model
                           ? CDI_PHYS_TYPE_LES_STAG_PRESSURE_FIXED_DIR 
                           : CDI_PHYS_TYPE_NEW_STAG_PRESSURE_FIXED_DIR, 
                           ptd->cdi_physics_type);
  if ((ptd->continuous_dp_var[i++]->id  != CDI_VAR_ID_IS_TOTAL_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_IS_SUPERSONIC_INLET)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SS_INLET_PRESSURE)){
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                         name, ptd->cdi_physics_type);
   }

  if ((is_called_from_derived_class 
       && sim.is_turb_model 
       && (!check_bc_surfel_turb_spec(ptd, i )))
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", 
                       name, ptd->cdi_physics_type);
  }
  return i;
}

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_CKPT_LEN : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data,
                   uINT64 &len) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->ckpt_len(len);
  }
};

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_READ_CKPT : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->read_ckpt();
  }
};

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_WRITE_CKPT : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->write_ckpt();
  }
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data, sCKPT_BUFFER& pio_ckpt_buff) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->write_ckpt(pio_ckpt_buff);
  }
};

uINT64 surfel_dyn_ckpt_len(sSURFEL_DYNAMICS_DATA *sdyn_data) {
  uINT64 dyn_len = 0;
  exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_CKPT_LEN>(sdyn_data, dyn_len);
  return dyn_len;
}

VOID surfel_dyn_read_ckpt(sSURFEL_DYNAMICS_DATA *sdyn_data) {  
  exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_READ_CKPT>(sdyn_data);
}

VOID surfel_dyn_write_ckpt(sSURFEL_DYNAMICS_DATA *sdyn_data) {
  exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_WRITE_CKPT>(sdyn_data);
}

VOID surfel_dyn_write_ckpt(sCKPT_BUFFER& pio_ckpt_buff,sSURFEL_DYNAMICS_DATA *sdyn_data) {
  exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_WRITE_CKPT>(sdyn_data, pio_ckpt_buff);
}

static VOID conduction_surfel_filter_voxel_interactions(SURFEL surfel) {
  // Applicable to sufels interacting with conduction volumes, checked before calling
  // cassert(surfel->is_conduction_surface());

  /* For now, we do not filter any interactions - this function is just
   * a place holder. We implicitly eliminate VRCoarse voxels which
   * have underlying VRFine voxels by setting their approximate
   * weights to 0 - thus any sample/distribute that uses approximate
   * weights automatically takes care of this.
   * For heat flux distribution, we use vsurfel_ratio - thus as long
   * as vsurfel_ratio's add up to 1 among all dynamic voxels, there
   * is no need to eliminate VRFine voxels (which are NOT dynamic
   * but may have vsurfel_ratio > 0). */

  STP_LATVEC_MASK incoming_latvec_mask = surfel->incoming_latvec_mask;
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  SURFEL_UBLK_INTERACTION active_ublk_interaction = ublk_interaction;

  asINT32 n_active_ublks = 0;
  uINT8 surfel_vr_mask = 0;

  ccDOTIMES (iu, surfel->m_n_ublk_interactions) {
    UBLK ublk = ublk_from_id(ublk_interaction->ublk_id(), STP_COND_REALM);
    ublk_interaction->set_ublk(ublk);
    // update scale_diff
    sUBLK::sNEARBLK_GEOM_DATA* surf_geom = NULL;
    uINT8 ublk_phase_mask = 0;
    // skip mirror ublks, they do not interact with surfels
    if (!ublk->is_mirror()) {
      surf_geom = ublk->surf_geom_data();
    }

    uINT8 *voxel_info = ublk_interaction->voxel_info();
    STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();

    BOOLEAN is_vr_fine = ublk->is_vr_fine();
    uINT32  active_weight_set_mask = 0;
    asINT32 n_active_weight_sets = 0;
    asINT32 n_active_total_weights = 0;
    if (is_vr_fine || ublk->is_vr_coarse()) {
      surfel->set_interacts_with_vr_ublks();
    }
    ccDOTIMES(iv, ublk_interaction->m_n_weight_sets) {
      SURFEL_VOXEL_INTERACTION s_v_interaction = 
                        (SURFEL_VOXEL_INTERACTION) voxel_info;
      asINT32 voxel = s_v_interaction->voxel_id();
      asINT32 phase_mask = s_v_interaction->phase_mask() ;
      asINT32 n_voxel_weights  = s_v_interaction->n_voxel_weights();
      asINT32 n_weights_pde_only = s_v_interaction->n_voxel_weights_pde_only();
      asINT32 scale_diff = surfel->scale() - ublk->scale();
      s_v_interaction->set_scale_diff(scale_diff);

      // This is because the phase_mask is based on the scale of the ublk
      // however in the surfel centric code, these interactions happen at the
      // surfel scale.
      // Only for closely packed VR. This is essentially a vr fine voxel
      // behaving like a vr coarse voxel which means it is interacting with a
      // surfel at a finer scale, so phase from the surfel perspective is
      // reversed.
      if ((scale_diff == 1) && is_vr_fine) {
        if (phase_mask == STP_EVEN_S2V_PHASE_MASK) {
          phase_mask = STP_ODD_S2V_PHASE_MASK;
        } else if (phase_mask == STP_ODD_V2S_PHASE_MASK) {
          phase_mask = STP_EVEN_V2S_PHASE_MASK;
        }
        s_v_interaction->set_voxel_id_phase_mask(voxel, phase_mask);
      } 

      ublk_phase_mask |= (phase_mask & FULL_PHASE_MASK);

      active_weight_set_mask |= (1 << iv);
      n_active_weight_sets++; 
      n_active_total_weights += n_voxel_weights;

      voxel_info += sSURFEL_VOXEL_INTERACTION::SIZE;

      voxel_info += n_voxel_weights;
      weights    += n_voxel_weights;
    }

    size_t ublk_interaction_size = ublk_interaction->size();
    size_t active_ublk_interaction_size = 
      sSURFEL_UBLK_INTERACTION::size(n_active_total_weights, n_active_weight_sets);
    // Either ublk_interaction is ahead of active_ublk_interaction or same as
    // active_ublk_interaction
    if ((n_active_weight_sets != 0) && (!ublk->is_mirror())) {
      // Removing bogus weights at this point?
      surfel_vr_mask |= ublk_phase_mask;
      active_ublk_interaction->set_vr_phase_mask(ublk_phase_mask);
      n_active_ublks++;
      active_ublk_interaction = (SURFEL_UBLK_INTERACTION) 
                                ((uINT8 *) active_ublk_interaction + active_ublk_interaction_size);
    } 
    ublk_interaction = (SURFEL_UBLK_INTERACTION) 
                       ((uINT8 *) ublk_interaction + ublk_interaction_size);
  }
  surfel->m_n_ublk_interactions = n_active_ublks;
  surfel->m_vr_phase_mask = surfel_vr_mask;
}

static VOID surfel_accumulate_s2v_weights(SURFEL surfel) 
{
  // cassert(!surfel->is_conduction_surfel()); //applicable to fluid surfels, checked before calling

  BOOLEAN is_lrf_surfel = surfel->is_lrf();
  //             (surfel->lb_data()->boundary_condition_type == BOUNDARY_CONDITION_LRF);
  dFLOAT *dp_pgram_volumes = surfel->dp_pgram_volumes();
  STP_LATVEC_MASK incoming_latvec_mask = surfel->incoming_latvec_mask;
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  SURFEL_UBLK_INTERACTION active_ublk_interaction = ublk_interaction;
#if BUILD_5G_LATTICE
  BOOLEAN is_multi_component = g_is_multi_component;
  BOOLEAN calc_potential_overlap = !sim.smart_seed_contact_angle &&
                                   !sim.is_full_checkpoint_restore &&
                                   !g_mp_sample_potential;
#endif

  asINT32 n_active_ublks = 0;
  uINT8 surfel_vr_mask = 0;
  ccDOTIMES (iu, surfel->m_n_ublk_interactions) {
    //REALM_ASSUMPTION: Ublk is FLOW
    UBLK ublk = ublk_from_id(ublk_interaction->ublk_id());
    ublk_interaction->set_ublk(ublk);
    // update scale_diff
    NEAR_UBLK_GEOM_DATA surf_geom = NULL;
    NEAR_UBLK_LB_DATA surf_lb     = NULL;
    uINT8 ublk_phase_mask = 0;
    // skip mirror ublks, they do not interact with surfels
    if (!ublk->is_mirror()) {
      surf_geom = ublk->surf_geom_data();
      surf_lb   = ublk->surf_lb_data();
    }

    uINT8 *voxel_info = ublk_interaction->voxel_info();
    STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();

#if BUILD_5G_LATTICE
      asINT32 voxel_size = scale_to_voxel_size(ublk->scale());
#endif

    dFLOAT (*dp_pas_factors)[ubFLOAT::N_VOXELS] =
        (dFLOAT (*)[ubFLOAT::N_VOXELS]) ublk->double_precision_pas_factors();
    BOOLEAN is_vr_fine = ublk->is_vr_fine();
    uINT32  active_weight_set_mask = 0;
    asINT32 n_active_weight_sets = 0;
    asINT32 n_active_total_weights = 0;
    if (is_vr_fine || ublk->is_vr_coarse()) {
      surfel->set_interacts_with_vr_ublks();
    }
    ccDOTIMES(iv, ublk_interaction->m_n_weight_sets) {
      SURFEL_VOXEL_INTERACTION s_v_interaction = 
                        (SURFEL_VOXEL_INTERACTION) voxel_info;
      asINT32 voxel = s_v_interaction->voxel_id();
      asINT32 phase_mask = s_v_interaction->phase_mask() ;
      asINT32 n_voxel_weights  = s_v_interaction->n_voxel_weights();
      asINT32 n_weights_pde_only = s_v_interaction->n_voxel_weights_pde_only();
      asINT32 scale_diff = surfel->scale() - ublk->scale();

      // compute this here, because the phase_mask can change later on
      bool is_vr_fine_even_s2v_interaction = is_vr_fine && (n_voxel_weights > n_weights_pde_only) && (phase_mask & (1 << STP_EVEN_S2V_PHASE)); 
      s_v_interaction->set_scale_diff(scale_diff);

#if BUILD_5G_LATTICE
      if (g_is_multi_component && ublk->is_mss_layer()) {
          STP_LOCATION voxel_location;
          ublk->get_voxel_location(voxel, voxel_location);

          if (surfel->is_home_voxel(voxel_location, voxel_size)) {
              sdFLOAT  gz = g_mc_types[0].molecular_charge * (g_mp_field_z[0] + g_timescale.m_time * g_mp_field_z[1]);
              BOOLEAN outlet_layer = (gz < 0.0f) ? (voxel % 2 ) : !(voxel % 2 ); // gz is not 0.0f

              surfel->mc_data()->mss_home_voxel_surfel = outlet_layer ? 2 : 3 ; // mark out surfel in mss region , 2 outlet layer , 3 inlet layer
          }
      }
#endif      

      // This is because the phase_mask is based on the scale of the ublk
      // however in the surfel centric code, these interactions happen at the
      // surfel scale.
      // Only for closely packed VR. This is essentially a vr fine voxel
      // behaving like a vr coarse voxel which means it is interacting with a
      // surfel at a finer scale, so phase from the surfel perspective is
      // reversed.
      if ((scale_diff == 1) && is_vr_fine) {
        if (phase_mask == STP_EVEN_S2V_PHASE_MASK) {
          phase_mask = STP_ODD_S2V_PHASE_MASK;
        } else if (phase_mask == STP_ODD_V2S_PHASE_MASK) {
          phase_mask = STP_EVEN_V2S_PHASE_MASK;
        }
        s_v_interaction->set_voxel_id_phase_mask(voxel, phase_mask);
      } 
      if (!((scale_diff == 0) && is_vr_fine && (phase_mask == (1 << STP_EVEN_V2S_PHASE))))
        ublk_phase_mask |= (phase_mask & FULL_PHASE_MASK);

      // We can skip weights for odd ball states which have V2S phase
      auINT32 pas_phase_mask;
      if ((scale_diff == 0) && !is_vr_fine) {
        pas_phase_mask = 1 << STP_ODD_S2V_PHASE;
        active_weight_set_mask |= (1 << iv);
        n_active_weight_sets++; 
        n_active_total_weights += n_voxel_weights;
      } else if ((scale_diff == 0) && is_vr_fine) {
        // bogus weights
        pas_phase_mask = 1 << STP_EVEN_V2S_PHASE;
      } else { // scale_diff != 0
        pas_phase_mask = (1 << STP_EVEN_S2V_PHASE) | (1 << STP_ODD_S2V_PHASE);
        active_weight_set_mask |= (1 << iv);
        n_active_weight_sets++; 
        n_active_total_weights += n_voxel_weights;
#if !BUILD_D39_LATTICE        
        if (!surfel->is_sliding_rf() && is_vr_fine && (n_voxel_weights > n_weights_pde_only)) {
          msg_internal_error("VR fine ublks and interacting surfels should be at the same scale %d voxel_weights %d pde_weights %d",
                             scale_diff, n_voxel_weights, n_weights_pde_only);
        }
#endif
      }
      /*if (surfel->id() == 1354053 || surfel->id() == 1354051) {
        msg_print("S %d E %d O %d U %d V %d Sscale %d uscale %d vrc %d vrf %d phase_mask %x", surfel->id(), surfel->is_even(), surfel->is_odd(),
                  ublk->id(), voxel, surfel->scale(),
                  ublk->scale(), ublk->is_vr_coarse(), ublk->is_vr_fine(), phase_mask);
      }*/
      if (n_voxel_weights > n_weights_pde_only) {
        if (is_lrf_surfel)
          ublk->lrf_surfels_interaction_mask.set(voxel);
        else
          ublk->non_lrf_surfels_interaction_mask.set(voxel);
      }

      voxel_info += sSURFEL_VOXEL_INTERACTION::SIZE;

      //using pde_2_interaction_voxel_mask to store the voxels for which
      //post_advect_scale_factors need to be calculated for vr fine ublks
      // skipping pde_2 weights for vr fine
      if (is_vr_fine && (n_voxel_weights > n_weights_pde_only)) {
        if (phase_mask != pas_phase_mask) {
          ublk->lb_interaction_voxel_mask.set(voxel);
          if (scale_diff == 0) {
            active_weight_set_mask |= (1 << iv);
            n_active_weight_sets++; 
            n_active_total_weights += n_voxel_weights;
          } else
            ublk->pde_2_interaction_voxel_mask.set(voxel);
        } else {
          ublk->pde_2_interaction_voxel_mask.set(voxel);
        }
      }

      if (phase_mask & pas_phase_mask) {
        // There are weights interacting via lb masks
        if ((n_voxel_weights > n_weights_pde_only) && (!is_vr_fine)) {
          ublk->lb_interaction_voxel_mask.set(voxel);
        }
        if ((n_weights_pde_only > 0) && (!is_vr_fine)) {
          ublk->pde_2_interaction_voxel_mask.set(voxel);
        }
        STP_SURFEL_WEIGHT fluid_weight = 0;
        ccDOTIMES (iw, n_voxel_weights-n_weights_pde_only) {
          STP_LATVEC_INDEX latvec = *voxel_info++;
          STP_SURFEL_WEIGHT weight = *weights++;
          STP_LATVEC_INDEX latvec_pair = latvec_to_latvec_pair(latvec);
          STP_LATVEC_INDEX iparity = state_parity(latvec);

          if (is_vr_fine_even_s2v_interaction) {
            sVR_FINE_INTERFACE_DATA *vr_fine_data = (sVR_FINE_INTERFACE_DATA *) ublk->vr_data();
            // pas_phase will be EVEN_V2S_phase for vr_fine voxel lb interactions
            // hence, this branch will always be executed for EVEN_S2V_PHASE
            // be interactions.
            // Marking the the latvecs and voxels which have interactions in
            // EVEN_S2V_PHASE
            vr_fine_data->m_even_s2v_weights_mask[iparity].set(voxel);
          }

          // XDU TODO: BUILD_D19_LATTICE and BUILD_IB?
#if BUILD_D39_LATTICE
          // Since n_weights_pde_only is 0 for D39 lattice, this loop will go over SPEED 2 latvecs.
          // pbl_2 factors should be calculated here.
          if (latvec >= D_P2_0_0_D39 && latvec <= D_0_0_N2_D39) {
            if (!is_vr_fine)
              ublk->pde_2_interaction_voxel_mask.set(voxel);
            
            STP_LATVEC_INDEX latvec_1 = SPEED_2_TO_SPEED_1(latvec);  // sizeof pbl_2 is 6
            STP_LATVEC_INDEX iparity_1 = state_parity(latvec_1);
            if (!sim.is_full_checkpoint_restore && !ublk->is_ghost()) {
              if (!is_vr_fine && !ublk->is_mirror()) {
                if (stp_is_state_inward(incoming_latvec_mask, latvec_1)) {
                  surf_geom->pbl_2[iparity_1][voxel] += weight * dp_pgram_volumes[latvec_pair];
                } else if (stp_is_state_outward(incoming_latvec_mask, latvec_1)) {
                  surf_geom->pbl_2[latvec_1][voxel] += weight * dp_pgram_volumes[latvec_pair];
                }
              }
            }
          }
#endif
          // Even though the weight and pgram volume may be single floats,
          // we perform the calculation of the post advect scale factor
          // using doubles to ensure maximum accuracy.
          if (!ublk->is_mirror()  && !ublk->is_ghost()) {
            dFLOAT v_i_alpha_x = (dFLOAT) weight * dp_pgram_volumes[latvec_pair];
            if (!sim.is_full_checkpoint_restore) {
              if (stp_is_state_inward(incoming_latvec_mask, latvec)) {
                dp_pas_factors[iparity][voxel] += v_i_alpha_x;

#ifdef DEBUG_COND
	  if (ublk->id() == 2881 && voxel == 6) {
	    msg_print("inward U %d V %d S %d i %d ", ublk->id(), voxel, surfel->id(), iparity);
	    msg_print("v_i_alpha_x %g = w %g * pgram_v %g", v_i_alpha_x, weight, dp_pgram_volumes[latvec_pair]);
	    msg_print("pas_factor %g", dp_pas_factors[iparity][voxel]);
	  }
#endif       
                if ((iparity < N_CARTESIAN_SPEED1_LATVECS) && (!is_lrf_surfel))
                  surf_lb->overlaps_non_lrf[iparity][voxel] += v_i_alpha_x;
              } else if (stp_is_state_outward(incoming_latvec_mask, latvec)) {
                dp_pas_factors[latvec][voxel] += v_i_alpha_x;

#ifdef DEBUG_COND
	  if (ublk->id() == 2881 && voxel == 6) {
	    msg_print("outward U %d V %d S %d i %d ", ublk->id(), voxel, surfel->id(),latvec);
	    msg_print("v_i_alpha_x %g = w %g * pgram_v %g", v_i_alpha_x, weight, dp_pgram_volumes[latvec_pair]);
	    msg_print("pas_factor %g", dp_pas_factors[latvec][voxel]);
	  }
#endif       
                if ((latvec < N_CARTESIAN_SPEED1_LATVECS) && (!is_lrf_surfel))
                  surf_lb->overlaps_non_lrf[latvec][voxel] += v_i_alpha_x;
              }
            }

#if BUILD_D39_LATTICE
            fluid_weight += latvec_pair_first_layer_weight(latvec_pair) * weight * dp_pgram_volumes[latvec_pair];
#else
            fluid_weight += v_i_alpha_x;
#endif
#if BUILD_5G_LATTICE
            if (calc_potential_overlap) {
              ublk->surf_lb_data()->srf_potential[iparity][voxel] += weight * dp_pgram_volumes[latvec_pair] * surfel->lb_data()->potential;
              if (is_multi_component)
                ublk->surf_mc_data()->srf_potential[iparity][voxel] += weight * dp_pgram_volumes[latvec_pair] * surfel->mc_data()->potential;
            }
#elif BUILD_D19_LATTICE
	     if(sim.is_pf_model && !sim.is_full_checkpoint_restore)
              ublk->surf_pf_data()->srf_potential[iparity][voxel] += weight * dp_pgram_volumes[latvec_pair] * surfel->uds_data(0)->uds_value;	    
#endif
          }
        }
#if BUILD_D39_LATTICE
        // The voxel samples the surfel normals to be used later in the
        // hybrid force vector calculation.
        if(!sim.is_full_checkpoint_restore && surfel->is_wall()) {
           ccDOTIMES(axis, N_AXES)
              ublk->surf_geom_data()->normal[axis][voxel] += fluid_weight * surfel->normal[axis];
//           printf("S %d \n", surfel->id());
        }
#endif

        if (!sim.is_full_checkpoint_restore && !is_vr_fine && !ublk->is_mirror() && !ublk->is_ghost()) {
          // This is used in post_advect_init
          surf_lb->s2v_weight[voxel] += fluid_weight;
          if (is_lrf_surfel) {
            // This is used in post_advect_init
            surf_lb->lrf_s2v_weight[voxel] += fluid_weight;
          }
        }
      } else if (is_vr_fine_even_s2v_interaction) {
        sVR_FINE_INTERFACE_DATA *vr_fine_data = ublk->vr_fine_data();
        // pas_phase will be EVEN_V2S_phase for vr_fine voxel lb interactions
        // hence, this branch will always be executed for EVEN_S2V_PHASE
        // be interactions.
        // Marking the the latvecs and voxels which have interactions in
        // EVEN_S2V_PHASE
        ccDOTIMES (iw, n_voxel_weights-n_weights_pde_only) {
          STP_LATVEC_INDEX latvec = *voxel_info++;
          STP_SURFEL_WEIGHT weight = *weights++;
          STP_LATVEC_INDEX iparity = state_parity(latvec);
          vr_fine_data->m_even_s2v_weights_mask[iparity].set(voxel);
        }
      } else {
        voxel_info += (n_voxel_weights - n_weights_pde_only);
        weights    += (n_voxel_weights - n_weights_pde_only);
      }

      // pbl_2 does not belong to VR fine ublks since no dynamics is done
      ccDOTIMES (iw, n_weights_pde_only) {
        STP_LATVEC_INDEX latvec = *voxel_info++;
        STP_SURFEL_WEIGHT weight = *weights++;
        if (!sim.is_full_checkpoint_restore && !ublk->is_ghost()) {
          // Assumed V_P2_0_0_D19 - V_P1_0_0_D19 = N_LATTICE_VECTORS
          asINT32 latvec_2 = SPEED_2_TO_SPEED_1(latvec);
          asINT32 iparity_2 = state_parity(latvec_2);
          if (!is_vr_fine && !ublk->is_mirror() && (phase_mask & pas_phase_mask)
              && (latvec_2 < N_CARTESIAN_SPEED1_LATVECS)) {
            if (stp_is_state_inward(incoming_latvec_mask, latvec_2)) {
              surf_geom->pbl_2[iparity_2][voxel] += weight;
            } else if (stp_is_state_outward(incoming_latvec_mask, latvec_2)) {
              surf_geom->pbl_2[latvec_2][voxel] += weight;
            }
          }
        }
      }
    }
    size_t ublk_interaction_size = ublk_interaction->size();
    size_t active_ublk_interaction_size = 
      sSURFEL_UBLK_INTERACTION::size(n_active_total_weights, n_active_weight_sets);
    // Either ublk_interaction is ahead of active_ublk_interaction or same as
    // active_ublk_interaction
    if ((n_active_weight_sets != 0) && (!ublk->is_mirror())) {
      // Removing bogus weights at this point?
      if (ublk_interaction_size > active_ublk_interaction_size) {
#ifdef USE_MEMORY_OVERLAP_MEMCPY
        memcpy(active_ublk_interaction, ublk_interaction, sizeof(sSURFEL_UBLK_INTERACTION));
#else
        memmove(active_ublk_interaction, ublk_interaction, sizeof(sSURFEL_UBLK_INTERACTION));
#endif

        uINT8 *voxel_info = ublk_interaction->voxel_info(active_ublk_interaction->m_total_weights);

        STP_SURFEL_WEIGHT *active_weights = active_ublk_interaction->weights();
        STP_SURFEL_WEIGHT *       weights =        ublk_interaction->weights();

        // Copy the weights first because active_ublk_interaction and
        // ublk_interaction may overlap in memory.
        ccDOTIMES(iv, active_ublk_interaction->m_n_weight_sets) {
          SURFEL_VOXEL_INTERACTION s_v_interaction = 
                            (SURFEL_VOXEL_INTERACTION) voxel_info;
          asINT32 n_voxel_weights  = s_v_interaction->n_voxel_weights();
          if ((active_weight_set_mask >> iv) & 1) {
#ifdef USE_MEMORY_OVERLAP_MEMCPY
            memcpy(active_weights, weights, (n_voxel_weights)*sizeof(STP_SURFEL_WEIGHT));
#else
            memmove(active_weights, weights, (n_voxel_weights)*sizeof(STP_SURFEL_WEIGHT));
#endif
            active_weights    += n_voxel_weights;
          }
          weights    += n_voxel_weights;
          voxel_info += sSURFEL_VOXEL_INTERACTION::SIZE + n_voxel_weights;
        }

        voxel_info = ublk_interaction->voxel_info(active_ublk_interaction->m_total_weights);
        uINT8 *active_voxel_info = active_ublk_interaction->voxel_info(n_active_total_weights);
        ccDOTIMES(iv, active_ublk_interaction->m_n_weight_sets) {
          SURFEL_VOXEL_INTERACTION s_v_interaction = 
                            (SURFEL_VOXEL_INTERACTION) voxel_info;
          asINT32 n_voxel_weights  = s_v_interaction->n_voxel_weights();
          if ((active_weight_set_mask >> iv) & 1) {
#ifdef USE_MEMORY_OVERLAP_MEMCPY
            memcpy(active_voxel_info, voxel_info, (n_voxel_weights + sSURFEL_VOXEL_INTERACTION::SIZE));
#else
            memmove(active_voxel_info, voxel_info, (n_voxel_weights + sSURFEL_VOXEL_INTERACTION::SIZE));
#endif
            active_voxel_info += sSURFEL_VOXEL_INTERACTION::SIZE + n_voxel_weights;
          }
          voxel_info += sSURFEL_VOXEL_INTERACTION::SIZE + n_voxel_weights;
        }

        active_ublk_interaction->m_n_weight_sets = n_active_weight_sets;
        active_ublk_interaction->m_total_weights = n_active_total_weights;
        active_ublk_interaction->m_voxels_advect_to_surfel_mask = 
          ublk->lb_interaction_voxel_mask;

      } else if ((uINT64)ublk_interaction > (uINT64) active_ublk_interaction) {
#ifdef USE_MEMORY_OVERLAP_MEMCPY
        // If the memory chunk between source and destination overlap, memory
        // should only be copied in chunks of non overlap bytes
        //
        uINT64 n_bytes_gap  = (uINT64)ublk_interaction - (uINT64)active_ublk_interaction;
        char *src_location  = (char *) ublk_interaction;
        char *dest_location = (char *) active_ublk_interaction;
        char *end_location = (char *) ublk_interaction + ublk_interaction_size;
        uINT64 n_bytes_left = (uINT64) ublk_interaction_size;
        while (n_bytes_left > n_bytes_gap) {
          memcpy(dest_location, src_location, n_bytes_gap);
          src_location  += n_bytes_gap;
          dest_location += n_bytes_gap;
          n_bytes_left  -= n_bytes_gap;
        }
        memcpy(dest_location, src_location, n_bytes_left);
#else
        // memmove allows for memory chunks to overlap since it creates a
        // local buffer. This may be more expensive than memcpy.
        memmove(active_ublk_interaction, ublk_interaction, ublk_interaction_size);
#endif

      }
      surfel_vr_mask |= ublk_phase_mask;
      active_ublk_interaction->set_vr_phase_mask(ublk_phase_mask);
      n_active_ublks++;
      active_ublk_interaction = (SURFEL_UBLK_INTERACTION) 
                                ((uINT8 *) active_ublk_interaction + active_ublk_interaction_size);
    } 
    ublk_interaction = (SURFEL_UBLK_INTERACTION) 
                       ((uINT8 *) ublk_interaction + ublk_interaction_size);
  }
  surfel->m_n_ublk_interactions = n_active_ublks;
  surfel->m_vr_phase_mask = surfel_vr_mask;
}

static VOID translate_ublk_id_to_ublk_ptr(SAMPLING_SURFEL surfel) {

  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  STP_REALM realm = surfel->is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  ccDOTIMES (iu, surfel->m_n_ublk_interactions) {
    UBLK ublk = ublk_from_id(ublk_interaction->ublk_id(), realm);
    ublk_interaction->set_ublk(ublk);
    ublk_interaction = (SURFEL_UBLK_INTERACTION) 
                       ((uINT8 *) ublk_interaction + ublk_interaction->size());
  }
}

static VOID update_surfel_surfel_interactions(SAMPLING_SURFEL dest_surfel) {

  STP_REALM realm = dest_surfel->is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  S2S_ADVECT_DATA s2s_advect_data = dest_surfel->s2s_advect_data();
  asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;
  SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
  ccDOTIMES(nth_src_surfel, n_src_surfels) {
    SURFEL src_surfel = regular_surfel_from_id(surfel_interaction->m_tagged_src_surfel.src_surfel_id(), realm);
    asINT32 n_weights = surfel_interaction->m_n_weights;
    surfel_interaction->m_tagged_src_surfel.set_src_surfel(src_surfel);
    // Weights in the DGF file are of the form: V_i_dest(src) / V_i_dest. It is more convenient
    // and efficient in S2S advection if the weights include an additional factor of 1 / V_i_src.
    STP_SURFEL_WEIGHT *weights = surfel_interaction->weights();
    auINT8            *latvecs = surfel_interaction->latvecs();
    ccDOTIMES(nth_weight, n_weights) {
      asINT32 latvec = *latvecs++;                  //inwards to dest surfel
      asINT32 latvec_pair = latvec_to_latvec_pair(latvec);
      *weights++ /= src_surfel->dp_pgram_volumes()[latvec_pair];
    }

    surfel_interaction = (SURFEL_SURFEL_INTERACTION)
                         ((char *) surfel_interaction + surfel_interaction->size());
  }
}

static VOID update_surfel_surfel_interactions(SURFEL dest_surfel) {

  S2S_ADVECT_DATA s2s_advect_data = dest_surfel->s2s_advect_data();
  asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;
  SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;

  asINT32 dest_scale = dest_surfel->scale();
  BOOLEAN is_there_any_even_odd_src_surfel = FALSE;
  BOOLEAN is_there_any_valid_lrf_src_surfel = FALSE;
  ccDOTIMES(nth_src_surfel, n_src_surfels) {
    asINT32 n_weights = surfel_interaction->m_n_weights;
    sTAGGED_S2S_SRC_SURFEL *tagged_s2s_src_surfel = &surfel_interaction->m_tagged_src_surfel;
    SURFEL src_surfel = regular_surfel_from_id(tagged_s2s_src_surfel->src_surfel_id(), dest_surfel->realm());
    surfel_interaction->m_tagged_src_surfel.set_src_surfel(src_surfel);

    if (src_surfel->even_odd_mask() != STP_PROCESS_ON_ALL_TIMES)
      is_there_any_even_odd_src_surfel = TRUE;

    // The source surfel may be one scale finer or one scale coarser than the dest surfel.
    asINT32 src_scale = src_surfel->scale();
    if ((src_scale != dest_scale)
        && (src_scale != finen_scale(dest_scale))
        && (src_scale != coarsen_scale(dest_scale)) )
      msg_internal_error("Surfel %d (scale %d) contains an S2S source surfel (ID %d) at scale %d.",
                         dest_surfel->id(), dest_scale, src_surfel->id(), src_scale);

    // In S2S, we only collect mass, mom, etc from LRF surfels that are not even or odd, and only
    // if the dest surfel is a wall, and only if the dest surfel talks to no even or odd surfels.
    // This last condition is handled below.
    //
    // This code must be kept in sync with the code in surfel_advect.cc:accumulate_s2s_weights
    // that initializes surfel->s2s_sampling_weight
    if (src_surfel->is_lrf() && dest_surfel->is_wall() && !src_surfel->is_even_or_odd()) {
      tagged_s2s_src_surfel->mark_as_lrf_src();
      is_there_any_valid_lrf_src_surfel = TRUE;
    }

    // Weights in the DGF file are of the form: V_i_dest(src) / V_i_dest. It is more convenient
    // and efficient in S2S advection if the weights include an additional factor of 1 / V_i_src.
    STP_SURFEL_WEIGHT *weights = surfel_interaction->weights();
    auINT8            *latvecs = surfel_interaction->latvecs();
    ccDOTIMES(nth_weight, n_weights) {
      asINT32 latvec = *latvecs++;                  //inwards to dest surfel
      asINT32 latvec_pair = latvec_to_latvec_pair(latvec);
      if (src_surfel->dp_pgram_volumes()[latvec_pair] == 0)
        msg_internal_error("Src surfel %d has zero pgram volume in latvec_pair %d, it should not have S2S weight in latvec %d with dest surfel %d", src_surfel->id(), latvec_pair, latvec, dest_surfel->id());
      *weights++ /= src_surfel->dp_pgram_volumes()[latvec_pair];
    }

    surfel_interaction = (SURFEL_SURFEL_INTERACTION)
                         ((char *) surfel_interaction + surfel_interaction->size());
  }

  if (is_there_any_even_odd_src_surfel  && is_there_any_valid_lrf_src_surfel ) {
    surfel_interaction = s2s_advect_data->m_surfel_interactions;
    ccDOTIMES(nth_src_surfel, n_src_surfels) {
      asINT32 n_weights = surfel_interaction->m_n_weights;
      sTAGGED_S2S_SRC_SURFEL *tagged_s2s_src_surfel = &surfel_interaction->m_tagged_src_surfel;
      tagged_s2s_src_surfel->unmark_as_lrf_src();
      surfel_interaction = (SURFEL_SURFEL_INTERACTION)
                           ((char *) surfel_interaction + surfel_interaction->size());
    }
  }
}

static VOID translate_opposite_surfel_id_to_surfel_ptr(SURFEL opposite_surfel)
{
  STP_REALM realm = opposite_surfel->is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  if (reinterpret_cast<uINT64>(opposite_surfel->opposite_surfel()) == INVALID_SHOB_ID)
    opposite_surfel->m_opposite_surfel = nullptr;
  else {
    SURFEL surfel = regular_surfel_from_id(reinterpret_cast<uINT64>(opposite_surfel->opposite_surfel()), realm);
    opposite_surfel->m_opposite_surfel = surfel;
  }
}

// Coupled surfels are in the opposite realm
static VOID translate_coupled_surfel_id_to_surfel_ptr(SURFEL surfel)
{
  STP_REALM opposite_realm = surfel->is_conduction_surfel() ? STP_FLOW_REALM : STP_COND_REALM;
  CONDUCTION_INTERFACE_BASE_DATA interface_data = surfel->conduction_interface_base_data();
  if (interface_data->m_coupled_surfel_id == INVALID_SHOB_ID) {
    interface_data->m_coupled_surfel = nullptr;
  } else {
    SURFEL coupled_surfel = regular_surfel_from_id(interface_data->m_coupled_surfel_id, opposite_realm);
    //For conduction open shells only coupled to the back surfel, the front ghost fluid surfel was allocated during
    //parsing but not added to any group. Checks here if that is the case, and if so, translates the opposite surfel
    //pointer so it is available for the quasi 1D to retrieve the back surfel of the triplet.
    if (!surfel->is_ghost() && surfel->is_conduction_open_shell()) {
      if (coupled_surfel == nullptr) {
        msg_internal_error("Conduction open shell surfel %d missing front flow coupled surfel %d", 
                            surfel->id(), interface_data->m_coupled_surfel_id);
      } else if (coupled_surfel->m_group == nullptr) {
        translate_opposite_surfel_id_to_surfel_ptr(coupled_surfel);
      }
    }
    interface_data->m_coupled_surfel = coupled_surfel;
  }
}

static VOID translate_clone_surfel_id_to_surfel_ptr(SURFEL even_odd_surfel)
{
  STP_REALM realm = even_odd_surfel->is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  SURFEL clone_surfel = regular_surfel_from_id(even_odd_surfel->even_odd_data()->m_clone_surfel.clone_surfel_id(), realm);
  even_odd_surfel->even_odd_data()->m_clone_surfel.set_clone_surfel(clone_surfel);
}

static VOID translate_clone_surfel_id_to_sampling_surfel_ptr(SAMPLING_SURFEL even_odd_surfel)
{
  STP_REALM realm = even_odd_surfel->is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  SAMPLING_SURFEL clone_surfel = sampling_surfel_from_id(even_odd_surfel->m_clone_surfel_id, realm);
  even_odd_surfel->m_clone_surfel = clone_surfel;
}

static VOID surfel_s2s_s2v_interactions(SURFEL surfel)
{

  // surfel is translated to surfel pointer using the surfel_table

  // REALM_CHANGE: 
  // Formerly this was conditional on
  // surfel->is_conduction_surface(), which meant that 
  // conducting open shell surfels were processed by
  // surfel_accumulate_s2v_weights. Since both that function
  // and conduction_surfel_filter_voxel_interactions do almost
  // nothing (and do the same thing) when the surfel has no
  // ublk interactions, it does not really matter which one we 
  // call for conducting open shell surfels, which have no such 
  // interactions. It is convenient, and less counterintuitive,
  // to have them processed by the function that is specifically
  // for conduction surfels.

  if (surfel->is_conduction_surfel()) {
    conduction_surfel_filter_voxel_interactions(surfel);
  } else if (!surfel->is_conduction_surfel()) {
    surfel_accumulate_s2v_weights(surfel);
  }
  if (surfel->is_s2s_destination()) {
    // surfel is translated to surfel pointer using the surfel_table
    update_surfel_surfel_interactions(surfel);
  }
  if (surfel->is_even_or_odd()) {
    translate_clone_surfel_id_to_surfel_ptr(surfel);
  }
  translate_opposite_surfel_id_to_surfel_ptr(surfel);
  if (surfel->is_conduction_interface()) {
    translate_coupled_surfel_id_to_surfel_ptr(surfel);
  }
}

static inline VOID update_surfel_s2s_s2v_interactions(SURFEL surfel) {
  surfel_s2s_s2v_interactions(surfel);
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      surfel_s2s_s2v_interactions(mirror_data->m_mirror_surfel);
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

// SURFEL CENTRIC ublk update
VOID update_surfel_ublk_interactions() {

  if (!sim.is_full_checkpoint_restore) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {
        nearblk->init_surfel_interaction_data();
        nearblk->finalize_distance_info();
      }
      DO_VRFINE_NEARBLKS_OF_SCALE(vrblk, scale) {
        vrblk->init_surfel_interaction_data();
      }
    }
  } else {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {
        nearblk->finalize_distance_info();
      }
    }
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    //NOTE: Alternatively, we could use the macro DO_SURFEL_BASE_GROUPS_OF_SCALE(group, scale) to traverse all
    //      all base groups in a similar way as done when promoting interior to fringe for example
    //extra braces limit the scope of surfel
    { DO_DYN_SURFELS_FLOW_OF_SCALE(dyn_surfel, scale) {
        // ublk is translated to ublk pointer using the ublk_table
        //msg_print("Dyn COND %d, ID %d",dyn_surfel->is_conduction_surfel(),dyn_surfel->id());
        update_surfel_s2s_s2v_interactions(dyn_surfel);
    } }
    { DO_DYN_SURFELS_CONDUCTION_OF_SCALE(dyn_surfel, scale) {
        // ublk is translated to ublk pointer using the ublk_table
        update_surfel_s2s_s2v_interactions(dyn_surfel);
        //msg_print("WSC COND %d, ID %d",wsurfel_conduction->is_conduction_surfel(),wsurfel_conduction->id());
    } }
    { DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
        update_surfel_s2s_s2v_interactions(wsurfel_flow);
        //msg_print("WSF COND %d, ID %d",wsurfel_flow->is_conduction_surfel(),wsurfel_flow->id());
    } }
    { DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
        update_surfel_s2s_s2v_interactions(wsurfel_conduction);
    } }
    { DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
        update_surfel_s2s_s2v_interactions(contact_surfel);
    } }
    {
      DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
          SURFEL surfel = quantum.m_surfel;
          surfel_s2s_s2v_interactions(surfel);
          //msg_print("Ghost COND %d, ID %d",surfel->is_conduction_surfel(),surfel->id());
        }
      }
    }
    {
      DO_WSURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
        SURFEL *ws = group->surfels();
        asINT32 num_wsurfels = group->n_surfels();
        ccDOTIMES(nth_wsurfel, num_wsurfels) {
          SURFEL wsurfel = *ws;
          // If the ghost surfel receives same-realm comm, it will already have been processed above.
          if (reinterpret_cast<WSURFEL_RECV_GROUP>(wsurfel->m_group) == group) {
            surfel_s2s_s2v_interactions(wsurfel);
          }
          ws++;
        }
      }
    }
    {
      DO_CONTACT_RECV_GROUPS_OF_SCALE(group, scale) {
        SURFEL *cs = group->surfels();
        asINT32 num_csurfels = group->n_surfels();
        ccDOTIMES(nth_csurfel, num_csurfels) {
          SURFEL csurfel = *cs;
          if (reinterpret_cast<CONTACT_RECV_GROUP>(csurfel->m_group) == group) {
            surfel_s2s_s2v_interactions(csurfel);
          }
          cs++;
        }
      }
    }
    {
      DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
        SURFEL ext_surfel = slrf_surfel_pair->m_exterior_surfel;
        SURFEL int_surfel = slrf_surfel_pair->m_interior_surfel;
        surfel_s2s_s2v_interactions(ext_surfel);
        surfel_s2s_s2v_interactions(int_surfel);
        if (ext_surfel->interacts_with_vr_ublks() ^
            int_surfel->interacts_with_vr_ublks()) {
          ext_surfel->set_interacts_with_vr_ublks();
          int_surfel->set_interacts_with_vr_ublks();
        }
      }
    }
    {
      DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
        SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
        SURFEL int_surfel = isurfel_pair->m_interior_surfel;
        surfel_s2s_s2v_interactions(ext_surfel);
        surfel_s2s_s2v_interactions(int_surfel);

        if (ext_surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = ext_surfel->mirror_data();
          while (mirror_data) {
            surfel_s2s_s2v_interactions(mirror_data->m_mirror_surfel);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }//add for mirror isurfel_pair
        if (int_surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = int_surfel->mirror_data();
          while (mirror_data) {
            surfel_s2s_s2v_interactions(mirror_data->m_mirror_surfel);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }//add for mirror isurfel_pair

        if (ext_surfel->interacts_with_vr_ublks() ^
            int_surfel->interacts_with_vr_ublks()) {
          ext_surfel->set_interacts_with_vr_ublks();
          int_surfel->set_interacts_with_vr_ublks();
        }
      }
    }
    {
      DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_surfel_group, scale) {
        DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_surfel_group) {
          if (!tagged_mlrf_surfel.is_surfel_weightless()) {
            auto surfel = tagged_mlrf_surfel.mlrf_surfel();
            surfel_s2s_s2v_interactions(surfel);
          }
        }
      }
    }
    {
      DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
        if (sampling_surfel->is_s2s_destination()) {
          update_surfel_surfel_interactions(sampling_surfel);
        }
        translate_ublk_id_to_ublk_ptr(sampling_surfel);
        if (sampling_surfel->is_even_or_odd()) {
          translate_clone_surfel_id_to_sampling_surfel_ptr(sampling_surfel);
        }
      }
    }
    DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        translate_ublk_id_to_ublk_ptr(quantum.m_surfel);
      }
    }
  }  

  if (!sim.is_full_checkpoint_restore) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk1, scale) {
        nearblk1->process_surfel_interaction_data();
      }
      DO_VRFINE_NEARBLKS_OF_SCALE(vrblk1, scale) {
        vrblk1->process_surfel_interaction_data();
      }
    }
    // DO NOT COMBINE THESE TWO SCALE LOOPS. Double precision post advect scale
    // factors from VR fine voxels are accumulated in the calculation of beta
    // factors for parent VR coarse voxels.
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk2, scale) {
        nearblk2->convert_dp_post_advect_scale_factors_to_sp();
      }
      DO_VRFINE_NEARBLKS_OF_SCALE(vrblk2, scale) {
        vrblk2->convert_dp_post_advect_scale_factors_to_sp();
      }
    }
  }
}

#if BUILD_5G_LATTICE
//for 5G
asINT32 sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                                   cSTRING name,
                                                                   BOOLEAN is_called_from_derived_class)
{
  asINT32 i = sPRESSURE_FREE_DIR_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP0_VOL_FRAC)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP1_VOL_FRAC)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_STATE_OF_MATTER)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_BC_TYPE)
      || ptd->n_continuous_dp < i) {

    msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
  }
  return i;
}


asINT32 sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                                          cSTRING name,
                                                                          BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a 5G free direction static pressure boundary",
                           CDI_PHYS_TYPE_5G_PRESS_BC,
                           ptd->cdi_physics_type);

  asINT32 i = sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

  return i;
}

asINT32 sSTATIC_PRESSURE_FIXED_DIR_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                                           cSTRING name,
                                                                           BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a 5G fixed direction static pressure boundary",
                           CDI_PHYS_TYPE_5G_PRESS_FIXED_DIR_BC,
                           ptd->cdi_physics_type);

  asINT32 i = sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.",
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sSOURCE_5G_SURFEL_PARAMETERS:: check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                         cSTRING name,
                                                         BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a 5G pressure and velocity boundary",
                           CDI_PHYS_TYPE_5G_PRESS_VEL_BC,
                           ptd->cdi_physics_type);

  asINT32 i = sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.",
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sMASS_FLUX_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                           cSTRING name,
                                                           BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a 5G mass flux boundary",
                           CDI_PHYS_TYPE_5G_MASS_FLUX_BC,
                           ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESPONSE_TIME)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP0_MASS_FLUX)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP1_MASS_FLUX)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_STATE_OF_MATTER)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_BC_TYPE)
      || ptd->n_continuous_dp < i) {
     msg_internal_error("%s is not consistent with CDI physics type %d.",
                       name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sNOSLIP_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                        cSTRING name,
                                                        BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a 5G dns wall", CDI_PHYS_TYPE_5G_DNS_WALL, ptd->cdi_physics_type);

  asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
  if((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP0_FRICTION_FACTOR)
     || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP0_WETTING_DENS)
     || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP1_FRICTION_FACTOR)
     || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COMP1_WETTING_DENS)
     || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
  }
  return i;
}


asINT32 sANGULAR_NOSLIP_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                                cSTRING name,
                                                                BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a 5G rotating dns wall",CDI_PHYS_TYPE_5G_DNS_ANGULAR_WALL , ptd->cdi_physics_type);

  asINT32 i = sNOSLIP_5G_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ROTATION_VIA_REF_FRAME)
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
  }
  return i;
}

asINT32 sLINEAR_NOSLIP_5G_SURFEL_PARAMETERS::check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                                                               cSTRING name,
                                                               BOOLEAN is_called_from_derived_class)
{
  if (!is_called_from_derived_class)
    do_check_cdi_phys_type("a 5G sliding dns wall", CDI_PHYS_TYPE_5G_DNS_LINEAR_WALL, ptd->cdi_physics_type);

  asINT32 i = sNOSLIP_5G_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
      || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
      || ptd->n_continuous_dp < i) {
    msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
  }
  return i;
}
#endif

template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_RESCALE_MEAS_DATA : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data,
                   SURFEL surfel) {
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);
    derived_dynamics_data->rescale_seed_from_meas_data(surfel);
  }
};

template<>
VOID sSURFEL::rescale_seed_from_meas_data() {
  SURFEL surfel = this;
  sSURFEL_DYNAMICS_DATA *dynamics_data = surfel->dynamics_data();
  if (surfel->is_inlet_or_outlet()) {
    exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_RESCALE_MEAS_DATA>(dynamics_data, surfel);
  }
}

template <typename PHYSICS_DESC, typename PHYSICS_PARMS>
static VOID update_time_varying_attribute_for_mass_flux(PHYSICS_DESC pd, PHYSICS_PARMS parms) {
  // Implement check on time-varying velocity components; i.e., if one is, then make
  // everything time-varying. If one is both space-and-time-varying, then make all components
  // so.
  cBOOLEAN t_varying = false;
  cBOOLEAN s_varying = false;
  ccDOTIMES (i, 3) {
    t_varying = t_varying || parms->mass_flux[i].is_time_varying;
  }
  if (t_varying) {
    ccDOTIMES (i, 3) {
      if (!parms->mass_flux[i].is_time_varying) {
        parms->mass_flux[i].is_time_varying = t_varying;
        // This is done such that correct value is returned after temporal
        // evaluation of parameters.
        if (!parms->mass_flux[i].is_space_varying)
          parms->mass_flux[i].conversion_offset = parms->mass_flux[i].value;
      }
      parms->mass_flux[i].is_eqn = parms->mass_flux[i].is_time_varying
        || parms->mass_flux[i].is_space_varying;
      pd->some_parameter_time_and_space_varying = pd->some_parameter_time_and_space_varying
        || (parms->mass_flux[i].is_space_varying && parms->mass_flux[i].is_time_varying);
    }
  }
}

static VOID filter_boundary_response_time(PHYSICS_VARIABLE response_time,
                                          PHYSICS_VARIABLE boundary_variables,
                                          asINT32 n_boundary_vars)
{
  if (response_time->is_space_varying) {
    msg_internal_error("Boundary response time is not allowed to be space varying.");
  }

  BOOLEAN is_boundary_time_varying = FALSE;
  ccDOTIMES(i, n_boundary_vars) {
    if (boundary_variables[i].is_time_varying) {
      is_boundary_time_varying = TRUE;
      break;
    }
  }

  // For time-varying inlets/outlets, the default response time is 1 timestep
  if (is_boundary_time_varying
      && !response_time->is_time_varying
      && (response_time->value < 0)) {
    response_time->value = 1;
  }
}

#if BUILD_5G_LATTICE
VOID sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA::validate(sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  {
    PRESSURE_FREE_DIR_SURFEL_PARAMETERS pressure_parms = (PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
    filter_boundary_response_time(&pressure_parms->response_time, &pressure_parms->pressure, 1);
  }

  STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS parms = (STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
  parms->dist_to_reflect.cvalue(); // Pd checks that dist_to_reflect is constant

  if (parms->dist_to_reflect.value >= 0) {
    // Non-reflecting BC not allowed with a time-varying condition
    if (pd->some_parameter_time_varying)
      msg_internal_error("Static pressure boundary \"%s\" has a time-dependent parameter."
                         " Non-reflecting boundaries are not allowed to vary in time.",
                         pd->name);
  }
}

#else
template<>
VOID sSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA::validate(sSTATIC_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  {
    PRESSURE_FREE_DIR_SURFEL_PARAMETERS pressure_parms = (PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
    filter_boundary_response_time(&pressure_parms->response_time, &pressure_parms->pressure, 1);
  }

  STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS parms = (STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
  parms->dist_to_reflect.cvalue(); // This checks that dist_to_reflect is constant

  if (parms->dist_to_reflect.value >= 0) {
    // Non-reflecting BC not allowed with a time-varying condition
    if (pd->some_parameter_time_varying)
      msg_internal_error("Static pressure boundary \"%s\" has a time-dependent parameter."
                         " Non-reflecting boundaries are not allowed to vary in time.",
                         pd->name);
  }
}

template<>
VOID sSTAG_PRESSURE_FREE_DIR_SURFEL_DATA::validate(sSTAG_PRESSURE_FREE_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  PRESSURE_FREE_DIR_SURFEL_PARAMETERS pressure_parms = (PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
  filter_boundary_response_time(&pressure_parms->response_time, &pressure_parms->pressure, 1);
}

template<>
VOID sSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA::validate(sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  PRESSURE_FREE_DIR_SURFEL_PARAMETERS pressure_parms = (PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
  filter_boundary_response_time(&pressure_parms->response_time, &pressure_parms->pressure, 1);
}

template<>
VOID sSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA::validate(sSTAG_PRESSURE_FIXED_DIR_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  PRESSURE_FREE_DIR_SURFEL_PARAMETERS pressure_parms = (PRESSURE_FREE_DIR_SURFEL_PARAMETERS)pd->parameters();
  filter_boundary_response_time(&pressure_parms->response_time, &pressure_parms->pressure, 1);  
}

template<>
VOID sMASS_FLUX_SURFEL_DATA::validate(sMASS_FLUX_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  MASS_FLUX_SURFEL_PARAMETERS parms = pd->parameters();
  filter_boundary_response_time(&pd->parameters()->response_time, pd->parameters()->mass_flux, 3);
  update_time_varying_attribute_for_mass_flux(pd, parms);
}

template<>
VOID sMASS_FLOW_SURFEL_DATA::validate(sMASS_FLOW_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  MASS_FLOW_SURFEL_PARAMETERS parms = pd->parameters();
  parms->inlet_area.value = g_flow_bc_total_areas[face_index];
}

template <typename PHYSICS_DESC, typename PHYSICS_PARMS>
static VOID update_time_varying_attribute_for_vel(PHYSICS_DESC pd, PHYSICS_PARMS parms) {
  // Implement check on time-varying velocity components; i.e., if one is, then make
  // everything time-varying. If one is both space-and-time-varying, then make all components
  // so.
  cBOOLEAN t_varying = false;
  cBOOLEAN s_varying = false;
  ccDOTIMES (i, 3) {
    t_varying = t_varying || parms->velocity[i].is_time_varying;
  }
  if (t_varying) {
    ccDOTIMES (i, 3) {
      if (!parms->velocity[i].is_time_varying) {
        parms->velocity[i].is_time_varying = t_varying;
        // This is done such that correct value is returned after temporal
        // evaluation of parameters.
        if (!parms->velocity[i].is_space_varying)
          parms->velocity[i].conversion_offset = parms->velocity[i].value;
      }
      parms->velocity[i].is_eqn = parms->velocity[i].is_time_varying ||
                                  parms->velocity[i].is_space_varying;
      pd->some_parameter_time_and_space_varying = pd->some_parameter_time_and_space_varying ||
                                                  (parms->velocity[i].is_space_varying &&
                                                   parms->velocity[i].is_time_varying);
    }
  }
}

  // Create and initialize cTURB_SYNTH_SIM object and store in global array
static VOID create_turb_synth_object(const sBC_SURFEL_TURB_SYNTH_SPEC *par)
{
  asINT32 tID = (asINT32) par->table_id.value;
  if (tID < 0) {
    return; //source surfel descriptor has no turb synth information
  }
  // This SP has turb_vel surfels; is a candidate for checkpointing turb_synth object
  g_turb_info.m_checkpointer = my_proc_id;

  sFLOAT freqRange[2], turbIntens[3], lenScales[3];
  freqRange[0] = par->freq_range[0].value;
  freqRange[1] = par->freq_range[1].value;
  sdFLOAT deltaT = par->turb_delta_t.value;
  // Turbulent intensity parameters are in percentage, so need to divide by 100
  turbIntens[0] = par->turb_intens[0].value * 0.01;
  turbIntens[1] = par->turb_intens[1].value * 0.01;
  turbIntens[2] = par->turb_intens[2].value * 0.01;
  lenScales[0] = par->length_scales[0].value;
  lenScales[1] = par->length_scales[1].value;
  lenScales[2] = par->length_scales[2].value;
  sdFLOAT meanVelMag = sim.char_vel;
  if (! sim.is_full_checkpoint_restore) {
    if (g_turb_info.m_vturb_synth.size() > tID)
      return;
    cTURB_SYNTH_SIM *turb_synth = new cTURB_SYNTH_SIM();
    uINT32 nvel = g_turb_info.m_nvel[tID];

    sdFLOAT charLength = meanVelMag * (sFLOAT) nvel * deltaT;
    uINT64 seed = tID;
    turb_synth->init(nvel, deltaT, g_turb_info.m_v[tID], g_turb_info.m_v[tID] + nvel,
                     g_turb_info.m_v[tID] + 2 * nvel, charLength, freqRange, turbIntens, lenScales,
                     meanVelMag, seed);

    uINT8 nTables = (uINT8)par->table_id.value + 1;
    if (g_turb_info.m_vturb_synth.size() < nTables)
      g_turb_info.m_vturb_synth.resize(nTables);
    g_turb_info.m_vturb_synth[tID] = turb_synth;
  } else {
    // From checkpoint
    cTURB_SYNTH_SIM *turb_synth = g_turb_info.m_vturb_synth[tID];
    turb_synth->SetPrescribedFrequencyRange(freqRange);
    turb_synth->SetPrescribedTurbIntensities(turbIntens);
    turb_synth->SetPrescribedTurbLengthScales(lenScales);
    turb_synth->SetMeanVelMag(meanVelMag);
    turb_synth->UpdatePeriodAndTimestep();
  }
}

template<>
VOID sFIXED_VEL_SURFEL_DATA::validate(sFIXED_VEL_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  FIXED_VEL_SURFEL_PARAMETERS parms = pd->parameters();
  if (parms->is_n_vel.value)
    filter_boundary_response_time(&parms->response_time, &parms->n_vel, 1);
  else
    filter_boundary_response_time(&parms->response_time, parms->velocity, 3);
  update_time_varying_attribute_for_vel(pd, parms);
  
  {
    FIXED_VEL_SURFEL_PARAMETERS fv_pars = (FIXED_VEL_SURFEL_PARAMETERS) pd->parameters();
    create_turb_synth_object(&fv_pars->turb_synth_spec);
  }
}

template<>
VOID sSOURCE_SURFEL_DATA::validate(sSOURCE_SURFEL_PHYSICS_DESCRIPTOR *pd, asINT32 face_index) {
  SOURCE_SURFEL_PARAMETERS parms = pd->parameters();
  update_time_varying_attribute_for_vel(pd, parms);
  {
    SOURCE_SURFEL_PARAMETERS source_pars = (SOURCE_SURFEL_PARAMETERS) pd->parameters();
    create_turb_synth_object(&source_pars->turb_synth_spec);
  }
}
#endif //BUILD_5G_LATTICE

template<eDYN_SURFEL_TYPE E>
struct CHECK_AND_VALIDATE_SURFACE_PHYSICS_DESCS : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  static VOID exec(sPHYSICS_DESCRIPTOR *surface_desc, asINT32 face_index) {
    auto derived_descriptor = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_descriptor_type(surface_desc);
    derived_descriptor->check_consistency();
    using DERIVED_DYN_DATA_TYPE = typename tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::DYN_DATA_TYPE;
    DERIVED_DYN_DATA_TYPE::validate(derived_descriptor, face_index);
  }
};

VOID check_consistency_and_validate_surface_physics_descriptors() {
  for (asINT32 face_index = 0; face_index < g_num_faces; face_index++) {
    for (int is_backside = 0; is_backside < 2; is_backside++) {
      if ((!is_backside && is_back_side_only_from_face_index(face_index)) || (is_backside && is_front_side_from_face_index(face_index))) {
        continue; //face not associated with this particular side, skip check
      }
      for (int is_conduction_surfel = 0; is_conduction_surfel < 2; is_conduction_surfel++) {
        PHYSICS_DESCRIPTOR surface_phys_desc = surface_physics_desc_from_face_index(face_index, is_backside, is_conduction_surfel);
        //Not all faces have surface physics descriptors associated with them
        if (surface_phys_desc) {
          STP_PHYSTYPE_TYPE sim_phys_type = surfel_sim_type_from_cdi_type(surface_phys_desc);
          eDYN_SURFEL_TYPE dynamics_type = static_cast<eDYN_SURFEL_TYPE>(dyn_surfel_type_from_stp_phy_type_for_validation(sim_phys_type));
          if (dynamics_type != eDYN_SURFEL_TYPE::INVALID_SURFEL_TYPE) {
            exec_by_surfel_dynamics_type<CHECK_AND_VALIDATE_SURFACE_PHYSICS_DESCS>(dynamics_type,
                                                                                  surface_phys_desc,
                                                                                  face_index);
          }
        }
      }
    }
  }
}
