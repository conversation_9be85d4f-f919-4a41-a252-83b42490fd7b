/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Interface definitions (functional sets and groups)
 *
 * Created Mon Jan 23, 2012
 *--------------------------------------------------------------------------*/

#if !BUILD_5G_LATTICE
#include "common_sp.h"
#include "sim.h"
#include "isurfel_dyn_sp.h"
#include "sp_timers.h"
#include PHYSICS_H

size_t apm_surfel_pair_size(STP_PHYSTYPE_TYPE sim_phys_type) {
  switch (sim_phys_type) {
  case STP_APM_SLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_SLIP_ISURFEL_DATA);
  case STP_APM_LINEAR_SLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_LINEAR_SLIP_ISURFEL_DATA);
  case STP_APM_ANGULAR_SLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_ANGULAR_SLIP_ISURFEL_DATA);
  case STP_APM_NOSLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_NOSLIP_ISURFEL_DATA);
  case STP_APM_LINEAR_NOSLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_LINEAR_NOSLIP_ISURFEL_DATA);
  case STP_APM_ANGULAR_NOSLIP_ISURFEL_TYPE_ID:
    return sizeof(sAPM_ANGULAR_NOSLIP_ISURFEL_DATA);
  default:
    msg_internal_error("Invalid LGI apm surfel dynamics type (%d)", sim_phys_type);
    return 0;
  }
}

VOID initialize_dynamics_data (asINT32 fluid_region_indices[2],
                               STP_PHYSTYPE_TYPE sim_phys_type, // for conduction, the pair type, not the type of the individual surfels
                               PHYSICS_DESCRIPTOR physics_desc,
                               sSURFEL_PAIR *surfel_pair) {
  if (!physics_desc->all_parameters_sharable) {
    physics_desc->eval_space_varying_only_parameter_program(cast_as_regular_array(surfel_pair->m_exterior_surfel->centroid),
                                                            cast_as_regular_array(surfel_pair->m_exterior_surfel->normal),
                                                            boundary_eqn_error_handler);
  }
  switch (sim_phys_type) {
  case STP_APM_SLIP_ISURFEL_TYPE_ID:
  {
    sAPM_SLIP_ISURFEL_DATA *apm_surfel_pair =
        (sAPM_SLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_SLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc =
        (sAPM_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_SLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(surface_phys_desc, fluid_region_indices);
    break;
  }
  case STP_APM_LINEAR_SLIP_ISURFEL_TYPE_ID:
  {
    sAPM_LINEAR_SLIP_ISURFEL_DATA *apm_surfel_pair =
        (sAPM_LINEAR_SLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc =
        (sAPM_LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_LINEAR_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_LINEAR_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_LINEAR_SLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(surface_phys_desc, fluid_region_indices);
    break;
  }
  case STP_APM_ANGULAR_SLIP_ISURFEL_TYPE_ID:
  {
    sAPM_ANGULAR_SLIP_ISURFEL_DATA *apm_surfel_pair =
        (sAPM_ANGULAR_SLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc =
        (sAPM_ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_ANGULAR_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_ANGULAR_SLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_ANGULAR_SLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(surface_phys_desc, fluid_region_indices);
    break;
  }
  case STP_APM_NOSLIP_ISURFEL_TYPE_ID:
  {
    sAPM_NOSLIP_ISURFEL_DATA *apm_surfel_pair =
        (sAPM_NOSLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc =
        (sAPM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_NOSLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(fluid_region_indices);
    break;
  }
  case STP_APM_LINEAR_NOSLIP_ISURFEL_TYPE_ID:
  {
    sAPM_LINEAR_NOSLIP_ISURFEL_DATA *apm_surfel_pair =
        (sAPM_LINEAR_NOSLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc =
        (sAPM_LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_LINEAR_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_LINEAR_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_LINEAR_NOSLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(surface_phys_desc, fluid_region_indices);
    break;
  }
  case STP_APM_ANGULAR_NOSLIP_ISURFEL_TYPE_ID:
  {
    sAPM_ANGULAR_NOSLIP_ISURFEL_DATA *apm_surfel_pair = (sAPM_ANGULAR_NOSLIP_ISURFEL_DATA *) surfel_pair;
    sAPM_ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *surface_phys_desc = (sAPM_ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR *) physics_desc;
    surfel_pair->m_interior_surfel->lb_data()->boundary_condition_type = sAPM_ANGULAR_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_exterior_surfel->lb_data()->boundary_condition_type = sAPM_ANGULAR_NOSLIP_ISURFEL_DATA::bc_type();
    surfel_pair->m_dynamics_type = sAPM_ANGULAR_NOSLIP_ISURFEL_DATA::dyn_apm_isurfel_type();
    apm_surfel_pair->init(surface_phys_desc, fluid_region_indices);
    break;
  }
  default:
    msg_internal_error("Invalid LGI apm surfel dynamics type (%d)", sim_phys_type);
  }
}

// CONDUCTION-TODO: Change surfel_temp_data to surfel_pair_v2s_data
VOID isurfel_dynamics(sSURFEL_PAIR *surfel_pair,
                      ACTIVE_SOLVER_MASK active_solver_mask,
                      ACTIVE_SOLVER_MASK full_active_solver_mask,
                      SFL_V2S_DATA_PAIR_PTR surfel_temp_data,
                      const BOOLEAN is_seeding_call) {

  SURFEL surfels[2];
  surfels[ISURFEL_INTERIOR] = surfel_pair->m_interior_surfel;
  surfels[ISURFEL_EXTERIOR] = surfel_pair->m_exterior_surfel;

  asINT32 n_windows[2];
  SURFEL_MEAS_CELL_PTR meas_cells[2];
  ccDOTIMES (ns, 2) {
    n_windows[ns] = surfels[ns]->dynamics_data()->m_surfel_meas_data.m_n_meas_cell_ptrs;
    meas_cells[ns] = surfels[ns]->dynamics_data()->m_surfel_meas_data.m_surfel_meas_cell_ptrs;
  }
  SCALE surfel_scale = surfel_pair->m_interior_surfel->scale();
  STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(surfel_scale);
  STP_GEOM_VARIABLE meas_scale_factor = (STP_GEOM_VARIABLE)
                                        ((sINT64)1 << (sim.num_dims * (sim.num_scales - surfel_scale - 1)));
  sdFLOAT one_over_nu_molecular = group_voxel_size / g_nu_molecular;/* in local voxels */
  asINT32 ref_frame_index = (asINT32) surfels[ISURFEL_FLUID]->ref_frame_index();
  LRF_PHYSICS_DESCRIPTOR lrf = (is_ref_frame_local(ref_frame_index)
                                ? &sim.lrf_physics_descs[ref_frame_index] : NULL);

  STP_PHYS_VARIABLE vel[3];
  BOOLEAN is_moving_slip_surfel = FALSE;
  BOOLEAN is_vel_tangential = FALSE;

  CALL_SURFEL_PAIR_DYN_METHOD(surfel_pair, dynamics, (n_windows, meas_cells, vel, group_voxel_size, meas_scale_factor,
                              one_over_nu_molecular, lrf, is_moving_slip_surfel, is_vel_tangential, active_solver_mask,
                              full_active_solver_mask, surfel_temp_data, is_seeding_call));
}

size_t sSURFEL_PAIR::size(asINT32 surfel_type,
                          STP_PHYSTYPE_TYPE sim_phys_type) {
  SURFEL_PAIR_GROUP_TYPE surfel_pair_type = (SURFEL_PAIR_GROUP_TYPE) surfel_type;
  switch (surfel_pair_type) {
  case FRINGE_APM_SURFEL_PAIR_GROUP_TYPE:
  case INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE:
    return apm_surfel_pair_size(sim_phys_type);

  case FRINGE_SLRF_SURFEL_PAIR_GROUP_TYPE:
  case INTERIOR_SLRF_SURFEL_PAIR_GROUP_TYPE:
    return sizeof(sSURFEL_PAIR);
  default:
    msg_internal_error("Invalid surfel pair type %d\n", surfel_pair_type);
    return 0;
  }
}

#endif
