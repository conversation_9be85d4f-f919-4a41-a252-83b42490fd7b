/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// Sam Watson, Exa Corporation                      Created Wed, Jan 23, 2013
//----------------------------------------------------------------------------

#include "comm_compression.h"
#include "ublk.h"

cCOMM_COMPRESSION_INFO g_comm_compression_info;

VOID cCOMM_COMPRESSION_INFO::build_recipes()
{

  BOOLEAN state_required[N_STATES][ubFLOAT::N_VOXELS];
  sNEIGHBOR_UBLK_VECTOR neighbor_ublk_vector_table[] =  NEIGHBOR_UBLK_VECTOR_TABLE;

  m_recipes = xnew std::vector<asINT32>[m_num_masks];
  m_counts = xnew asINT32[m_num_masks];
  m_sendsize = xnew asINT32[m_num_masks + 1];

  m_sendsize[m_num_masks] = 0; // For COND ublks

  // The vectors will live forever but will only be extended during initialization.
  ccDOTIMES(nmi, m_num_masks) {

    STP_UBLK_NEIGHBOR_MASK nmask = m_masks[nmi];
#if DISABLE_COMM_STATE_COMPRESSION
    nmask = STP_SPECIAL_UBLK_NEIGHBORS_MASK;
#endif

    if(nmask == STP_SPECIAL_UBLK_NEIGHBORS_MASK) {
      // Handle special mask value
      ccDOTIMES(state, N_MOVING_STATES) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          state_required[state][voxel] = TRUE;
        }
      }

      ccDO_FROM_BELOW(stopped_state, N_MOVING_STATES, N_STATES) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          state_required[stopped_state][voxel] = FALSE;
#if DISABLE_COMM_STATE_COMPRESSION
          state_required[stopped_state][voxel] = TRUE;
#endif
        }
      }
    } else {

      // Determine voxel states required according to neighbor mask
      ccDOTIMES(state, N_STATES) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          state_required[state][voxel] = FALSE;
        }
      }

      ccDOTIMES(state, N_MOVING_STATES) {
        STP_STATE_VEL svx =  state_vx(state);
        STP_STATE_VEL svy =  state_vy(state);
        STP_STATE_VEL svz =  state_vz(state);
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          STP_STATE_VEL vox_x = num_to_voxel_x(voxel);
          STP_STATE_VEL vox_y = num_to_voxel_y(voxel);
          STP_STATE_VEL vox_z = num_to_voxel_z(voxel);
          STP_STATE_VEL ublkX = (vox_x + svx) >> 1;
          STP_STATE_VEL ublkY = (vox_y + svy) >> 1;
          STP_STATE_VEL ublkZ = (vox_z + svz) >> 1;

          if( ublkX == 0 && ublkY == 0 && ublkZ == 0) continue;
          BOOLEAN found = FALSE;
          ccDOTIMES(ublk_neighbor, N_UBLK_NEIGHBORS) {
            sNEIGHBOR_UBLK_VECTOR nvec = neighbor_ublk_vector_table[ublk_neighbor];
            if((ublkX ==  nvec.vec[0]) && (ublkY ==  nvec.vec[1]) && (ublkZ ==  nvec.vec[2])) {
              found = TRUE;
              if((0x1 << ublk_neighbor) & nmask) {
                state_required[state][voxel] = TRUE;
              } else {
                state_required[state][voxel] = FALSE;
              }
              break;
            }
          }
          if(!found) {
            msg_internal_error("No ublk neighbor found corresponding to state %d voxel %d",state,voxel); 
          }
        }
      }
    }
    
#if 0
    if(my_proc_id == 0) {
      msg_print("NMI %d: mask = %x", nmi,  nmask);
      ccDOTIMES(state, N_MOVING_STATES) {
        msg_print("  State %2d: %d %d %d %d %d %d %d %d",state,
                  state_required[state][7],state_required[state][6], state_required[state][5],
                  state_required[state][4], state_required[state][3],state_required[state][2],
                  state_required[state][1], state_required[state][0]);
      }
    } 
#endif

    // Generate recipe. This is a vector of counts of consecutive TRUE or FALSE values of the
    // voxel-state-required predicate, starting with a TRUE count.


    BOOLEAN current_test = TRUE;
    if(state_required[0][0] == FALSE) {
      m_recipes[nmi].push_back(0);
      current_test = FALSE;
    }

    asINT32 count = 0;
    asINT32 sendsize = 0;
    //ccDOTIMES(state, N_MOVING_STATES) {
    ccDOTIMES(state, N_STATES) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        if(state_required[state][voxel] != current_test) {
          current_test = ! current_test;
          m_recipes[nmi].push_back(count);
          if(current_test == TRUE) sendsize ++;
          count = 1;
        } else {
          count++;
          if(current_test == TRUE) sendsize ++;
        }
      }
    }
    m_recipes[nmi].push_back(count);
    m_counts[nmi] = (m_recipes[nmi][0] == 0) ?  ((m_recipes[nmi].size() - 1)/2) :  ((m_recipes[nmi].size() + 1)/2);
    m_sendsize[nmi] = sendsize;
#if 0
    ccDOTIMES(i,m_recipes[nmi].size()) {
      msg_print("nmi %d %d: count = %d", nmi, i, m_recipes[nmi][i]);
    }
#endif
  }
}    
    
VOID cCOMM_COMPRESSION_INFO::fill_by_recipe(sdFLOAT* &out, VOXEL_STATE *in, NEIGHBOR_MASK_INDEX nmi)
{
  if(is_no_mask_nmi(nmi)) return;
  
  // std::vector<asINT32> recipe = m_recipes[nmi];
  asINT32 n_steps = m_recipes[nmi].size();
  asINT32 count = m_counts[nmi];
  const BOOLEAN INCLUDE = TRUE;
  const BOOLEAN EXCLUDE = FALSE;

  BOOLEAN recipe_state = INCLUDE;
  asINT32 first_step = 0;
  if(m_recipes[nmi][0] == 0) {
    recipe_state = EXCLUDE;
    first_step = 1;
  }
  
  ccDO_FROM_TO(step, first_step, n_steps - 1) {
    if(recipe_state == INCLUDE) {
      memcpy(out, in, sizeof(sdFLOAT) * m_recipes[nmi][step]);
      out += m_recipes[nmi][step];
      in  += m_recipes[nmi][step];
    } else {
      in  += m_recipes[nmi][step];
    }        
    recipe_state = !recipe_state;
  }
}

VOID cCOMM_COMPRESSION_INFO::expand_by_recipe(sdFLOAT* &in, VOXEL_STATE *out, NEIGHBOR_MASK_INDEX nmi)
{
  
  if(is_no_mask_nmi(nmi)) return;

  asINT32 n_steps = m_recipes[nmi].size();
  asINT32 count = m_counts[nmi];
  const BOOLEAN INCLUDE = TRUE;
  const BOOLEAN EXCLUDE = FALSE;

  BOOLEAN recipe_state = INCLUDE;
  asINT32 first_step = 0;
  if(m_recipes[nmi][0] == 0) {
    recipe_state = EXCLUDE;
    first_step = 1;
  }
  
  ccDO_FROM_TO(step, first_step, n_steps - 1) {
    if(recipe_state == INCLUDE) {
      memcpy(out, in, sizeof(sdFLOAT) * m_recipes[nmi][step]);
      out += m_recipes[nmi][step];
      in  += m_recipes[nmi][step];
    } else {
      out  += m_recipes[nmi][step];
    }        
    recipe_state = !recipe_state;
  }
}
