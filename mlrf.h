/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Sam Watson, Exa Corporation 
 * Created March 26, 2009
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_MLRF_H
#define _SIMENG_MLRF_H
#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "mlrf_depots.h"
#include "surfel_process_control.h"
#include "tagged_ptr.h"

enum MLRF_ORIENTATION {
  MLRF_INTERNAL = 0,
  MLRF_EXTERNAL,
  MLRF_N_ORIENTATIONS
};

enum MLRF_PHASE {
  MLRF_PREDYN = 0,
  MLRF_POSTDYN,
  MLRF_N_PHASES
};


inline namespace SIMULATOR_NAMESPACE {

template<typename SFL_TYPE_TAG> struct tSURFEL_MLRF_DATA;
	 
#if BUILD_GPU

typedef tSURFEL_MLRF_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_MLRF_DATA, *MSFL_MLRF_DATA;

#endif
}
// Debug

extern asINT32 g_total_predyn_receives_posted;
extern asINT32 g_total_postdyn_receives_posted;
extern asINT32 g_total_predyn_receives_completed;
extern asINT32 g_total_postdyn_receives_completed;

extern asINT32 g_total_predyn_sends_posted;
extern asINT32 g_total_postdyn_sends_posted;
extern asINT32 g_total_predyn_sends_completed;
extern asINT32 g_total_postdyn_sends_completed;

#if DEBUG
extern VMEM_VECTOR <SURFEL> g_mlrf_surfel_vector;
#endif

inline namespace SIMULATOR_NAMESPACE {

template<typename SFL_TYPE_TAG>
struct tSURFEL_MLRF_DATA_BASE {
  EXTRACT_SURFEL_TRAITS
  // Inverse of the sum of the parallelogram volumes
  tSFL_VAR<STP_GEOM_VARIABLE, N> m_inverse_sum_volumes[N_NONZERO_ENERGIES];
#if BUILD_D39_LATTICE
  tSFL_VAR<STP_GEOM_VARIABLE, N> m_inverse_total_volume_d39;
  tSFL_VAR<STP_GEOM_VARIABLE, N> m_inverse_total_volume_d19;
#endif
  tSFL_VAR<sINT32, N> m_index_in_ring; 
};

template<typename SFL_TYPE_TAG>
struct tSURFEL_MLRF_DATA;

template<>
struct tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>:
    public tSURFEL_MLRF_DATA_BASE<SFL_SDFLOAT_TYPE_TAG>
{
  VOID print_surfel_data(std::ostream& os);

  VOID init(asINT32 index_in_ring,
#if BUILD_D39_LATTICE
            dFLOAT inverse_total_volume_d39,
            dFLOAT inverse_total_volume_d19,
#endif
            dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES],
            BOOLEAN is_weightless);
};

template<>
struct tSURFEL_MLRF_DATA<MSFL_SDFLOAT_TYPE_TAG> :
public tSURFEL_MLRF_DATA_BASE<MSFL_SDFLOAT_TYPE_TAG> {
  // Using ring set index, and index in ring, we should be able to get depot
  // index via the rings since they know their depot start indices
  tSFL_VAR<uINT32, N_SFLS_PER_MSFL>  m_ring_set_index;
  tSFL_VAR<uINT32, N_SFLS_PER_MSFL>  m_recv_depot_index;
  VOID init(tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>* other,
            uINT32 ring_set_index,
            asINT32 soxor,
            BOOLEAN is_weightless,
            uINT32 nth_surfel_in_ring_on_proc,
            uINT32 recv_depot_index);
};
  
} //namespace SIMULATOR_NAMESPACE

using sSURFEL_MLRF_DATA = tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_MLRF_DATA = sSURFEL_MLRF_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_MLRF_DATA);
#endif

/* The decomposition of a ring is represented in a table of ring segments called the ring segment table.
 * Each entry defines a sequence of consecutive ring surfels on a single processor by a processor number
 * and a base index (the offset) for the segment. Since no gaps in the ring are allowed, the length of
 * a segment can be determined by subtracting its offset from that of the next segment, or from
 * n_surfels_in_ring if it is the last segment.
 */ 

typedef struct sMLRF_RING_SEGMENT {
  STP_PROC proc;
  sINT32 offset;
} *MLRF_RING_SEGMENT;

class sMLRF_RING {

protected:

  std::vector <sMLRF_RING_SEGMENT>  m_ring_segments;
  std::vector <sINT32>              m_ring_segments_on_proc; // on this proc

public:
  
  /* Send and receive buffers are of the same type. Since receive buffers are accessed
   * directly during surfel dynamics they are not referred to as buffers but as depots.
   */

  /* We could have just one send buffer for each depot type but it would
   * have to be large enough to accommodate both frames. There is no storage
   * penalty to having a buffer for each, and it seems less error-prone.
   */

  MLRF_PRE_DYNAMICS_DEPOT m_pre_dynamics_send_buffer[MLRF_N_ORIENTATIONS];
  MLRF_POST_DYNAMICS_DEPOT m_post_dynamics_send_buffer[MLRF_N_ORIENTATIONS];

  MLRF_PRE_DYNAMICS_DEPOT m_pre_dynamics_recv_buffer[MLRF_N_ORIENTATIONS];
  MLRF_POST_DYNAMICS_DEPOT m_post_dynamics_recv_buffer[MLRF_N_ORIENTATIONS];  

  cExaMsg<sMLRF_PRE_DYNAMICS_DEPOT> *m_pre_dynamics_send_msg[MLRF_N_ORIENTATIONS];
  cExaMsg<sMLRF_POST_DYNAMICS_DEPOT> *m_post_dynamics_send_msg[MLRF_N_ORIENTATIONS];

  cExaMsg<sMLRF_PRE_DYNAMICS_DEPOT> *m_pre_dynamics_recv_msg[MLRF_N_ORIENTATIONS];
  cExaMsg<sMLRF_POST_DYNAMICS_DEPOT> *m_post_dynamics_recv_msg[MLRF_N_ORIENTATIONS];

  asINT32 m_n_send_requests;
  asINT32 m_n_receive_requests;

  /* The number of messages, which is needed for message completion, is constant between  
   * phases and between frames, but changes between time steps. To allow for freedom in
   * ordering posting and completion of messages it is preferable to maintain a separate 
   * count for each phase.
   */
  asINT32 m_n_messages[MLRF_N_PHASES][MLRF_N_ORIENTATIONS];
  asINT32 m_n_messages_received[MLRF_N_PHASES][MLRF_N_ORIENTATIONS];

  BOOLEAN m_all_messages_received[MLRF_N_PHASES];

  asINT32 m_n_surfels_per_frame_in_ring; // across all procs 
  asINT32 m_n_surfels_per_frame_on_proc; // per reference frame (interior/exterior) 
                                                  // on this proc 
  dFLOAT m_one_over_angle_per_surfel;   // m_n_surfels_per_frame_in_ring / (2.0 * PI)

public:
  sMLRF_PRE_DYNAMICS_DEPOT* pre_dynamics_send_buff(MLRF_ORIENTATION orientation) {
    return m_pre_dynamics_send_buffer[orientation];
  }
  sMLRF_PRE_DYNAMICS_DEPOT* pre_dynamics_recv_buff(MLRF_ORIENTATION orientation) {
    return m_pre_dynamics_recv_buffer[orientation];
  }
  sMLRF_POST_DYNAMICS_DEPOT* post_dynamics_send_buff(MLRF_ORIENTATION orientation) {
    return m_post_dynamics_send_buffer[orientation];
  }
  sMLRF_POST_DYNAMICS_DEPOT* post_dynamics_recv_buff(MLRF_ORIENTATION orientation) {
    return m_post_dynamics_recv_buffer[orientation];
  }
  
  // constructor, initialize counts
  sMLRF_RING() {
    ccDOTIMES(phase,MLRF_N_PHASES) {
      ccDOTIMES(orientation,MLRF_N_ORIENTATIONS) {
        m_pre_dynamics_send_buffer[orientation] = nullptr;
        m_post_dynamics_send_buffer[orientation] = nullptr;
        m_pre_dynamics_recv_buffer[orientation] = nullptr;
        m_post_dynamics_recv_buffer[orientation] = nullptr;        
        m_n_messages[phase][orientation] = 0;
        m_n_messages_received[phase][orientation] = 0;
      }
      m_all_messages_received[phase] = FALSE;
    }
    m_n_surfels_per_frame_in_ring = 0;
    m_n_surfels_per_frame_on_proc = 0;
  }

  VOID max_messages(asINT32& max_send_messages, asINT32& max_recv_messages);
  // across all procs
  MLRF_RING_SEGMENT ring_segment(asINT32 i)  { return &m_ring_segments[i];    }
  MLRF_RING_SEGMENT ring_segments()          { return ring_segment(0);        }
  asINT32 n_ring_segments()                  { return m_ring_segments.size(); } 

  // on this proc
  sINT32 *ring_segment_on_proc(asINT32 i)    { return &m_ring_segments_on_proc[i];    }
  asINT32 *ring_segments_on_proc()           { return ring_segment_on_proc(0);        }
  asINT32 n_ring_segments_on_proc() const { return m_ring_segments_on_proc.size(); }

  MLRF_RING_SEGMENT add_ring_segment(sMLRF_RING_SEGMENT ring_segment) { 
    m_ring_segments.push_back(ring_segment);
    return &m_ring_segments.back();
  }

  VOID add_ring_segment_on_proc(sINT32 nth_segment) {
    m_ring_segments_on_proc.push_back(nth_segment);
  }

  asINT32 n_recv_depots_per_frame() const {
    return this->m_n_surfels_per_frame_on_proc + this->n_ring_segments_on_proc();
  }

  asINT32 n_send_depots_per_frame() const {
    return this->m_n_surfels_per_frame_on_proc + 1;
  }

  template<typename SFL_TYPE_TAG>
  BOOLEAN complete_receives(MLRF_PHASE phase); // Returns TRUE if all receives have been completed

  virtual BOOLEAN particles_enabled() const {return FALSE;}  
};

using MLRF_RING = sMLRF_RING*;

#if BUILD_GPU
class cDEVICE_MLRF_RING {
  
public:
  cDEVICE_MLRF_RING(const sMLRF_RING* ring):
    m_n_surfels_per_frame_in_ring(ring->m_n_surfels_per_frame_in_ring),
    m_n_surfels_per_frame_on_proc(ring->m_n_surfels_per_frame_on_proc),
    m_n_ring_segments_on_proc(ring->n_ring_segments_on_proc()),
    m_one_over_angle_per_surfel(ring->m_one_over_angle_per_surfel) {}

  VOID set_pre_dynamics_recv_buffer(GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> buff, asINT32 orientation) {
    m_pre_dynamics_recv_buffer[orientation] = buff;
  }
  VOID set_post_dynamics_recv_buffer(GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> buff, asINT32 orientation) {
    m_post_dynamics_recv_buffer[orientation] = buff;
  }
  VOID set_pre_dynamics_send_buffer(GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> buff, asINT32 orientation) {
    m_pre_dynamics_send_buffer[orientation] = buff;
  }
  VOID set_post_dynamics_send_buffer(GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> buff, asINT32 orientation) {
    m_post_dynamics_send_buffer[orientation] = buff;
  }

  __DEVICE__ sMLRF_PRE_DYNAMICS_DEPOT* pre_dynamics_send_buff(MLRF_ORIENTATION orientation) {
    return m_pre_dynamics_send_buffer[orientation].get();
  }
  __DEVICE__ sMLRF_PRE_DYNAMICS_DEPOT* pre_dynamics_recv_buff(MLRF_ORIENTATION orientation) {
    return m_pre_dynamics_recv_buffer[orientation].get();
  }
  __DEVICE__ sMLRF_POST_DYNAMICS_DEPOT* post_dynamics_send_buff(MLRF_ORIENTATION orientation) {
    return m_post_dynamics_send_buffer[orientation].get();
  }
  __DEVICE__ sMLRF_POST_DYNAMICS_DEPOT* post_dynamics_recv_buff(MLRF_ORIENTATION orientation) {
    return m_post_dynamics_recv_buffer[orientation].get();
  }

  __DEVICE__ asINT32 n_surfels_per_frame_in_ring() const { return m_n_surfels_per_frame_in_ring; }
  __DEVICE__ asINT32 n_surfels_per_frame_on_proc() const { return m_n_surfels_per_frame_on_proc; }
  __DEVICE__ asINT32 n_ring_segments_on_proc() const { return m_n_ring_segments_on_proc; }
  __DEVICE__ asINT32 one_over_angle_per_surfel() const { return m_one_over_angle_per_surfel; }

  __DEVICE__ asINT32 n_recv_depots_per_frame() const {
    return this->m_n_surfels_per_frame_on_proc + this->m_n_ring_segments_on_proc;
  }

  __DEVICE__ asINT32 n_send_depots_per_frame() const {
    return this->m_n_surfels_per_frame_on_proc + 1;
  }  
private:
  GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> m_pre_dynamics_send_buffer[MLRF_N_ORIENTATIONS];
  GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> m_post_dynamics_send_buffer[MLRF_N_ORIENTATIONS];
  GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> m_pre_dynamics_recv_buffer[MLRF_N_ORIENTATIONS];
  GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> m_post_dynamics_recv_buffer[MLRF_N_ORIENTATIONS];  
  asINT32 m_n_surfels_per_frame_in_ring; // across all procs 
  asINT32 m_n_surfels_per_frame_on_proc; // per reference frame (interior/exterior) // on this proc
  asINT32 m_n_ring_segments_on_proc;
  dFLOAT m_one_over_angle_per_surfel;   // m_n_surfels_per_frame_in_ring / (2.0 * PI)
};
#endif

/* Neither a Ring Modulator or a Ring Modulus, but a baby mod operator for use with rings.
 * No nasty divide, but doesn't work if a >= 2 * modulus or a < 0. Both of these conditions 
 * are satisfied by ring indices.
 */

inline asINT32 ringmod(asINT32 a, asINT32 modulus) {
  if(a < modulus) return a;
  else return a - modulus;
}

inline asINT32 ringnegmod(asINT32 a, asINT32 modulus) {
  if(a >= 0) 
    return a;
  else 
    return a + modulus;
}


template<typename SFL_TYPE_TAG>
struct tTAGGED_MLRF_SURFEL_TRAITS {
  
  constexpr static auto ALIGNMENT = tSURFEL<SFL_TYPE_TAG>::ALIGNMENT;
  using PTR = tSURFEL<SFL_TYPE_TAG>*;
  
  enum ATTRIBUTES {
    IS_WEIGHTLESS = 0,
    IS_SENTINEL = 1,
    CHILD_SFL = 2,
    N
  };

  __HOST__DEVICE__ constexpr static size_t num_bits_for_attrib(ATTRIBUTES a) {
    switch(a) {
    case IS_WEIGHTLESS:
      return 1;
    case IS_SENTINEL:
      return SFL_TYPE_TAG::is_msfl()? 1 : 0;
    case CHILD_SFL:
      return SFL_TYPE_TAG::is_msfl()? 6 : 0;      
    default:
      return 0;
    }
  }
};

// TAGGED_MLRF_SURFEL is tagged with a boolean is_surfel_weightless.
// The MLRF GROUP contains  vector of TAGGED_MLRF_SURFELS.
// A surfel may be repeated in a ring and marked as weightless, that is why
// the boolean has to be in the tagged_mlrf_surfel.
template<typename SFL_TYPE_TAG>
class tTAGGED_MLRF_SURFEL {

private:
  using PTR_TRAITS = tTAGGED_MLRF_SURFEL_TRAITS<SFL_TYPE_TAG>;
  tTAGGED_PTR<PTR_TRAITS> m_tagged_mlrf_surfel;

public:
  tTAGGED_MLRF_SURFEL(tSURFEL<SFL_TYPE_TAG> *surfel,
                      BOOLEAN is_weightless,
                      asINT32 soxor) {
    m_tagged_mlrf_surfel.set_ptr(surfel);
    if (is_weightless) {
      mark_surfel_weightless(soxor);
    }
    set_child_sfl(soxor);
  }
  __HOST__DEVICE__ tSURFEL<SFL_TYPE_TAG> *mlrf_surfel() {
    return m_tagged_mlrf_surfel.ptr();
  }

  __HOST__DEVICE__ const tSURFEL<SFL_TYPE_TAG> *mlrf_surfel() const {
    return m_tagged_mlrf_surfel.ptr();
  }

  //Conenience cast
  operator tSURFEL<SFL_TYPE_TAG>*() { return this->mlrf_surfel(); }
  operator const tSURFEL<SFL_TYPE_TAG>*() const { return this->mlrf_surfel(); }
  
  VOID mark_surfel_weightless(asINT32 soxor) {
    m_tagged_mlrf_surfel.template set<PTR_TRAITS::IS_WEIGHTLESS>(1);
  }

  __HOST__DEVICE__ BOOLEAN is_surfel_weightless() const {
    return m_tagged_mlrf_surfel.template get<PTR_TRAITS::IS_WEIGHTLESS>();
  }

  VOID set_child_sfl(int child_sfl) {
    m_tagged_mlrf_surfel.template set<PTR_TRAITS::CHILD_SFL>(child_sfl);
  }

  __HOST__DEVICE__ int child_sfl() const {
    return m_tagged_mlrf_surfel.template get<PTR_TRAITS::CHILD_SFL>();
  }

  VOID mark_as_sentinel() {
    m_tagged_mlrf_surfel.template set<PTR_TRAITS::IS_SENTINEL>(1);
  }

  __HOST__DEVICE__ BOOLEAN is_sentinel() const {
    return m_tagged_mlrf_surfel.template get<PTR_TRAITS::IS_SENTINEL>();
  }
};

using TAGGED_MLRF_SURFEL = tTAGGED_MLRF_SURFEL<SFL_SDFLOAT_TYPE_TAG>;
#if BUILD_GPU
using TAGGED_MLRF_MSFL = tTAGGED_MLRF_SURFEL<MSFL_SDFLOAT_TYPE_TAG>;
#endif

/*--------------------------------------------------------------------------*
 * SYNC_COMM_THREAD_AN_RUN_THREAD_SINGLE_GPU_COMM
 * Temporary helper class to artifically sync the compute and comm threads
 * for handling single gpu comm. This should go away when we have CUDA aware
 * MPI since MPI_Test will make this class redundant
 *--------------------------------------------------------------------------*/
class SYNC_COMM_THREAD_AN_RUN_THREAD_SINGLE_GPU_COMM {
public:
  SYNC_COMM_THREAD_AN_RUN_THREAD_SINGLE_GPU_COMM():
    m_dynamics_sends_posted{0} {};

  BOOLEAN are_sends_ready(MLRF_PHASE phase) const {
    return m_dynamics_sends_posted[phase];
  }

  VOID unmark_sends_as_posted(MLRF_PHASE phase) {
    m_dynamics_sends_posted[phase] = FALSE;
  }

  VOID mark_sends_as_posted(MLRF_PHASE phase) {
    m_dynamics_sends_posted[phase] = TRUE;
  }

private:
  BOOLEAN m_dynamics_sends_posted[MLRF_N_PHASES];
};

/*--------------------------------------------------------------------------*
 * tMLRF_SURFEL_GROUP
 *--------------------------------------------------------------------------*/
template<typename SFL_TYPE_TAG>
class tMLRF_SURFEL_GROUP {

  EXTRACT_SURFEL_TRAITS
  using TAGGED_MLRF_SURFEL = tTAGGED_MLRF_SURFEL<SFL_TYPE_TAG>;
  
private:
  std::vector<TAGGED_MLRF_SURFEL> m_surfels;
  std::vector <sMLRF_RING*> m_rings;
#if BUILD_GPU
  GPU::Ptr<cDEVICE_MLRF_RING> m_device_rings;
  GPU::Ptr<TAGGED_MLRF_SURFEL> m_device_tagged_mlrf_surfels;
#endif
#if BUILD_GPU
  SYNC_COMM_THREAD_AN_RUN_THREAD_SINGLE_GPU_COMM m_sync_single_gpu_comm;
#endif  
  BOOLEAN m_has_weightless_tagged_sfls;
  static constexpr ssize_t N_QUANTUMS_INITIAL_RESERVE = 1024;

public:
  
#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
  std::vector< MEAS_WINDOW > m_meas_windows;
#endif

  STP_SCALE m_scale;
  STP_REALM m_realm;
  STP_EVEN_ODD m_even_odd; /* Process on even or odd timesteps (or both) ? */
  LRF_PHYSICS_DESCRIPTOR m_lrf_physics_desc;
  // mlrf pre and post dynamics sends are posted in SSA and SSB strands
  // respectively. And the number of sends and receives depend on angle
  // rotated. Angle rotated is updated in time update strand but there could
  // be synchronization issues between comm and compute thread due to which
  // previous time step sends are posted for an updated angle_rotated due to
  // which there is a mismatch in number of sends and receives and
  // simulations hang.
  dFLOAT pre_dyn_send_angle_rotated;
  dFLOAT post_dyn_send_angle_rotated;
  BOOLEAN m_all_rings_complete[MLRF_N_PHASES];
  asINT32 m_n_rings_complete[MLRF_N_PHASES];
  
public:
  // constructor
  tMLRF_SURFEL_GROUP():
    m_has_weightless_tagged_sfls(FALSE) {
    m_surfels.reserve(N_QUANTUMS_INITIAL_RESERVE);
  }
  sINT32 n_quantums() const {
    return m_surfels.size();
  }
  TAGGED_MLRF_SURFEL *quantum(asINT32  i) {
    return &m_surfels[i];
  }
  auto& quantums() {
    return m_surfels;
  }  
  asINT32 quantum_size() {
    return sizeof(SURFEL);
  }
  auto begin() {
    return m_surfels.begin();
  }
  auto end() {
    return m_surfels.end();
  }
  VOID add_tagged_sfl(TAGGED_MLRF_SURFEL s) {
    m_has_weightless_tagged_sfls =
      m_has_weightless_tagged_sfls || s.is_surfel_weightless();
    m_surfels.push_back(s);
  }
  
  VOID add_sentinel_tagged_sfl() {
    assert(this->n_quantums());
    TAGGED_MLRF_SURFEL last_tagged_ptr = m_surfels.back();
    last_tagged_ptr.mark_as_sentinel();
    m_surfels.push_back(last_tagged_ptr);
  }
  
  sINT32 n_rings() const {
    return m_rings.size();
  }  
  sMLRF_RING *ring(asINT32 i) {
    return m_rings[i];
  }
  sMLRF_RING *rings() {
    return ring(0);
  }

  std::vector<sMLRF_RING*>& ref_to_rings() {
    return m_rings;
  }
  BOOLEAN has_weightless_surfels() const {
    return m_has_weightless_tagged_sfls;
  }  
#if BUILD_GPU
  VOID set_device_rings(GPU::Ptr<cDEVICE_MLRF_RING> d_rings) {
    m_device_rings = d_rings;
  }

  GPU::Ptr<cDEVICE_MLRF_RING> get_device_rings() const {
    return m_device_rings;
  }

  VOID set_device_tagged_mlrf_surfels(GPU::Ptr<TAGGED_MLRF_SURFEL> d_tagged_mlrf_surfels) {
    m_device_tagged_mlrf_surfels = d_tagged_mlrf_surfels;
  }

  auto get_device_tagged_mlrf_surfels() const {
    return m_device_tagged_mlrf_surfels;
  }

  //For GPU builds host MLRF MSFL groups hold pointers to device memory
  //Memory is also ordered with external orientation first
  auto get_start_of_gpu_pre_dyn_sbuff() {
    sMLRF_PRE_DYNAMICS_DEPOT* sbuff = this->rings()->m_pre_dynamics_send_buffer[MLRF_EXTERNAL];
    return GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT>(sbuff);
  }
  
  auto get_start_of_gpu_post_dyn_sbuff() {
    sMLRF_POST_DYNAMICS_DEPOT* sbuff = this->rings()->m_post_dynamics_send_buffer[MLRF_EXTERNAL];
    return GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT>(sbuff);
  }
#endif

  asINT32 n_total_ring_segments_on_proc() const {
    asINT32 n_tot = 0;
    ccDOTIMES (i, m_rings.size()) {
      n_tot += m_rings[i]->n_ring_segments_on_proc();
    }
    return n_tot;
  }
  size_t no_of_recv_depots() const {
    size_t n_depots = 0;
    ccDOTIMES(i, this->n_rings()) {
      n_depots += 2 * (m_rings[i]->m_n_surfels_per_frame_on_proc + m_rings[i]->n_ring_segments_on_proc());
    }
    return n_depots;
  }
  size_t no_of_send_depots() const {
    size_t n_depots = 0;
    ccDOTIMES(i, this->n_rings()) {
      n_depots += 2 * (m_rings[i]->m_n_surfels_per_frame_on_proc + 1);
    }
    return n_depots;
  }
  VOID print_depot_data_for_ring(asINT32 ring_index);
  VOID dynamics_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                     SOLVER_INDEX_MASK time_step_index_mask, BOOLEAN is_seed = 0);
  VOID dynamics_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control, BOOLEAN is_seed = 0);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID reset_s2s_v2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control);

  VOID reset_s2s_v2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID s2v_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control);
  
  VOID s2v_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control);

  VOID surfel_dyn_delta_mass(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                             SOLVER_INDEX_MASK out_flux_index_mask, BOOLEAN is_seed = 0);

  VOID pre_advect_init(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID pre_seed_init(BOOLEAN is_full_checkpoint_restore, SOLVER_INDEX_MASK seed_solver_index_mask);
  VOID seed_T_scalar_solver();
  /* Communication methods */

  VOID copy_to_pre_dynamics_send_buffers(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID post_pre_dynamics_sends(BOOLEAN is_seeding = FALSE);
  VOID post_pre_dynamics_receives();
  
  BOOLEAN complete_receives(MLRF_PHASE phase);
  VOID post_post_dynamics_sends(BOOLEAN is_seeding = FALSE);
  VOID post_post_dynamics_receives();

  VOID cancel_receives();

  VOID initialize_for_parcels();
  VOID count_parcels_and_fill_send_buffers();
  VOID post_parcel_sends(); //Post send requests for both the parcel count and parcel state messages.
  VOID post_parcel_count_receives(); //Post receive requests for just the parcel count messages.
  VOID post_parcel_state_receives(); //Post receive requests for just the parcel state messages.
  BOOLEAN complete_parcel_count_receives();
  BOOLEAN complete_parcel_state_receives();
  VOID emit_lrf_parcels();

  bool is_timestep_valid(bool is_timestep_odd, bool is_timestep_even) {
    BOOLEAN group_even_and_odd = (m_even_odd == STP_PROCESS_ON_ALL_TIMES);
    BOOLEAN group_only_odd     = (m_even_odd == STP_PROCESS_ON_ODD_TIMES);
    BOOLEAN group_only_even    = (m_even_odd == STP_PROCESS_ON_EVEN_TIMES);
    if (group_even_and_odd)
      return true;
    else if ( is_timestep_odd && group_only_odd)
      return true;
    else if ( is_timestep_even && group_only_even)
      return true;
    else
      return false;
  }
  ACTIVE_SOLVER_MASK set_active_solver_mask(sSURFEL_PROCESS_CONTROL *surfel_process_control) {
    ACTIVE_SOLVER_MASK active_solver_mask;
    if(m_even_odd == STP_PROCESS_ON_ALL_TIMES)
      active_solver_mask = surfel_process_control->m_active_solver_mask;
    else if(m_even_odd == STP_PROCESS_ON_ODD_TIMES)
      active_solver_mask = surfel_process_control->m_odd_active_solver_mask;
    else if(m_even_odd == STP_PROCESS_ON_EVEN_TIMES)
      active_solver_mask = surfel_process_control->m_even_active_solver_mask;
    else
      active_solver_mask = 0;
    return active_solver_mask;
  }

  asINT32 get_lrf_index() const;  
  VOID promote_interacting_ublks_to_sliding_nearblks();
  VOID sanity_check_for_sliding_surfels();
  MLRF_RING add_ring();
};

using sMLRF_SURFEL_GROUP = tMLRF_SURFEL_GROUP<SFL_SDFLOAT_TYPE_TAG>;
using MLRF_SURFEL_GROUP = sMLRF_SURFEL_GROUP*;

VOID add_mlrf_surfel(tMLRF_SURFEL_GROUP<SFL_SDFLOAT_TYPE_TAG>* group,
                     tSURFEL<SFL_SDFLOAT_TYPE_TAG>* surfel,
                     asINT32 index_in_ring,
                     BOOLEAN is_weightless,
#if BUILD_D39_LATTICE
                     dFLOAT inverse_total_volume_d39,
                     dFLOAT inverse_total_volume_d19,
#endif
                     dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES]);

#if BUILD_GPU
using sMLRF_MSFL_GROUP = tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>;
VOID add_mlrf_surfel(tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>* group,
                     tSURFEL<MSFL_SDFLOAT_TYPE_TAG>* msfl,
                     tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>* mlrf_sfl_data,
                     BOOLEAN is_weightless,
                     uINT32 ring_set_index,
                     asINT32 soxor,
                     uINT32 nth_surfel_in_ring_on_proc,
                     uINT32 recv_depot_index);
#endif

#define DO_MLRF_SURFEL_GROUP_SURFELS(quantum_var, group) \
  for(auto quantum_var : (group)->quantums())

/*--------------------------------------------------------------------------*
 * MLRF_SURFEL_FSET
 *--------------------------------------------------------------------------*/
template<typename SFL_TYPE_TAG>
struct tMLRF_SURFEL_GROUP_ORDER
{
  BOOLEAN operator()(tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>* a,
                     tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>* b) const
  {
    if (a->m_realm != b->m_realm)
      return (a->m_realm < b->m_realm);
    
    if (a->m_scale != b->m_scale)
      return a->m_scale < b->m_scale;

    if (a->m_lrf_physics_desc != b->m_lrf_physics_desc)
      return a->m_lrf_physics_desc < b->m_lrf_physics_desc;

    if (a->m_even_odd != b->m_even_odd)
      return a->m_even_odd < b->m_even_odd;

#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
    asINT32 n_windows1 = a->n_meas_windows();
    asINT32 n_windows2 = b->n_meas_windows();

    if (n_windows1 != n_windows2)
      return n_windows1 < n_windows2;

    MEAS_WINDOW *w1 = a->meas_windows();
    MEAS_WINDOW *w2 = b->meas_windows();

    for (asINT32 i = 0; i < n_windows1; i++) {
      if (*w1 != *w2)
        return *w1 < *w2; // just compare pointers
      w1++;
      w2++;
    }
#endif
    return FALSE;
  }
};

template<typename SFL_TYPE_TAG>
class tMLRF_SURFEL_FSET : public tSP_FSET <tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>*,
                                           tMLRF_SURFEL_GROUP_ORDER<SFL_TYPE_TAG>> {
  public:
  tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>* create_group(asINT32 scale,
                                                 asINT32 lrf_index,
                                                 STP_EVEN_ODD even_odd,
                                                 STP_REALM realm
#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
                                                 ,std::vector< MEAS_WINDOW > &meas_windows
#endif
                                                 );
};

using sMLRF_SURFEL_FSET = tMLRF_SURFEL_FSET<SFL_SDFLOAT_TYPE_TAG>;
using MLRF_SURFEL_FSET = sMLRF_SURFEL_FSET*;
extern sMLRF_SURFEL_FSET g_mlrf_surfel_fset;

#if BUILD_GPU
using sMLRF_MSFL_FSET = tMLRF_SURFEL_FSET<MSFL_SDFLOAT_TYPE_TAG>;
using MLRF_MSFL_FSET = sMLRF_MSFL_FSET*;
extern sMLRF_MSFL_FSET g_mlrf_msfl_fset;
#endif

inline BOOLEAN is_sliding_mesh_present() {
  return (g_mlrf_surfel_fset.n_groups() > 0);
}

VOID read_mlrf_ring_set(LGI_STREAM stream, STP_REALM realm);
VOID read_lrf_containment(LGI_STREAM stream);
VOID finish_init_of_mlrf_surfel_groups();

/*--------------------------------------------------------------------------*
 * Iterating over all the MLRF surfel groups
 *--------------------------------------------------------------------------*/

#define DO_MLRF_SURFEL_GROUPS(group_var)	                        \
  FSET_FOREACH_GROUP(sMLRF_SURFEL_FSET, g_mlrf_surfel_fset, group_var)

#define DO_MLRF_SURFEL_GROUPS_OF_SCALE(group_var, scale)	        \
  FSET_FOREACH_GROUP_OF_SCALE(sMLRF_SURFEL_FSET, g_mlrf_surfel_fset, group_var, scale)

#if BUILD_GPU
#define DO_MLRF_MSFL_GROUPS(group_var)	                        \
  FSET_FOREACH_GROUP(sMLRF_MSFL_FSET, g_mlrf_msfl_fset, group_var)

#define DO_MLRF_MSFL_GROUPS_OF_SCALE(group_var, scale)	        \
  FSET_FOREACH_GROUP_OF_SCALE(sMLRF_MSFL_FSET, g_mlrf_msfl_fset, group_var, scale)
#endif

#if !BUILD_GPU
#define DO_MLRF_SFL_OR_MSFL_GROUPS(group_var) \
  DO_MLRF_SURFEL_GROUPS(group_var)
#else
#define DO_MLRF_SFL_OR_MSFL_GROUPS(group_var) \
  DO_MLRF_MSFL_GROUPS(group_var)
#endif

#if !BUILD_GPU
#define DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group_var, scale) \
 DO_MLRF_SURFEL_GROUPS_OF_SCALE(group_var, scale)
#else
#define DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group_var, scale) \
 DO_MLRF_MSFL_GROUPS_OF_SCALE(group_var, scale)
#endif

inline asINT32 find_segment_from_surfel_offset(MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, 
                                               asINT32 surfel_offset)
{
  // @@@ This should be coded as a binary search
  for (asINT32 i = 1; i < n_ring_segments; i++) {
    if (ring_segments[i].offset > surfel_offset)
      return i-1;
  }
  return n_ring_segments-1;
}

inline BOOLEAN next_send_message(asINT32 &msg_length, asINT32 &msg_depot_offset_inc,
                                 asINT32 &remote_proc, BOOLEAN &send_one_to_prior_remote_segment,
                                 asINT32 &local_segment_index,
                                 asINT32 &local_segment, asINT32 &remote_segment,
                                 asINT32 &local_offset,  asINT32 &remote_offset,
                                 asINT32 surfel_offset, sINT32 *local_ring_segments, asINT32 n_local_ring_segments,
                                 asINT32 n_local_ring_segments_to_process, asINT32 &local_segments_so_far,
                                 MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, asINT32 n_surfels_in_ring)
{
  if (send_one_to_prior_remote_segment) {
    send_one_to_prior_remote_segment = FALSE;
    msg_length = 1;
    msg_depot_offset_inc = 0;
    asINT32 segment_before_remote_segment = ringnegmod(remote_segment - 1, n_ring_segments);
    remote_proc = ring_segments[segment_before_remote_segment].proc;
    return local_segment != 0; // stop if we just cycled back around to the first segment
  }
    
  remote_proc = ring_segments[remote_segment].proc;

  asINT32 segment_after_local_segment = ringmod(local_segment + 1, n_ring_segments);
  asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);

  asINT32 segment_after_local_segment_offset = ((segment_after_local_segment == 0)
                                                ? n_surfels_in_ring 
                                                : ring_segments[segment_after_local_segment].offset);
  asINT32 segment_after_remote_segment_offset = ((segment_after_remote_segment == 0) 
                                                 ? n_surfels_in_ring 
                                                 : ring_segments[segment_after_remote_segment].offset);

  asINT32 local_segment_remaining_length = segment_after_local_segment_offset - local_offset;
  asINT32 remote_segment_remaining_length = segment_after_remote_segment_offset - remote_offset;

  if (local_segment_remaining_length <= remote_segment_remaining_length) {
    msg_length = local_segment_remaining_length;
    msg_depot_offset_inc = local_segment_remaining_length;
    local_segments_so_far++;
    if (local_segments_so_far < n_local_ring_segments_to_process) {
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringmod(local_offset + surfel_offset, n_surfels_in_ring);
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);

      if (ring_segments[remote_segment].offset == remote_offset)
        send_one_to_prior_remote_segment = TRUE;
      return TRUE;  // keep going
    } else {
      return FALSE; // stop
    }

  } else {
    msg_length = remote_segment_remaining_length + 1;
    msg_depot_offset_inc = remote_segment_remaining_length;
    local_offset = local_offset + remote_segment_remaining_length; // mod not needed since we stay in same segment
    remote_offset = ring_segments[segment_after_remote_segment].offset;
    remote_segment = segment_after_remote_segment;
    return TRUE;  // keep going
  }
}  


// @@@ All local vars defined in MLRF_DO_SEND_MESSAGES and MLRF_DO_RECV_MESSAGES should be wrapped in the __(x) macro
// @@@ to avoid name conflicts if these macros are used more than once in a block.

#if DEBUG_MLRF_DO_MESSAGES
#define __(x) x
#else
#define __(x) ___(x)
#endif
  
// msg_length ranges from 1 to n_surfels_in_ring.
// msg_depot_offset ranges from 0 to ring->n_local_surfels - 1,
// since there is one send depot per local surfel in the ring.
// _surfel_offset must be positive. If this is set to S
// for an exterior ring E sending to an interior ring I,
// it should be set to ring->n_surfels_in_ring - S for I 
// sending to E. 
#define MLRF_DO_SEND_MESSAGES(msg_length, msg_depot_offset, remote_proc,  /* Bound by macro */                          \
                              _ring, _surfel_offset)                      /* Inputs to macro */                         \
  MLRF_RING __(ring) = (_ring);                                                                                         \
  asINT32 n_surfels_in_ring       = __(ring)->m_n_surfels_per_frame_in_ring;                                            \
  asINT32 surfel_offset = ringmod(n_surfels_in_ring - _surfel_offset, n_surfels_in_ring);                               \
  /* surfel_offset used here is the index in the receiving ring that matches surfel zero in the sending ring */         \
  MLRF_RING_SEGMENT ring_segments = __(ring)->ring_segments();                                                          \
  asINT32 n_ring_segments         = __(ring)->n_ring_segments();                                                        \
  sINT32  *local_ring_segments    = __(ring)->ring_segments_on_proc(); /* indices of segments owned by this proc */     \
  asINT32 n_local_ring_segments   = __(ring)->n_ring_segments_on_proc();                                                \
  asINT32 msg_length;                                                                                                   \
  asINT32 msg_depot_offset = 0; /* The offset into the local array of depots */                                         \
  asINT32 remote_proc;                                                                                                  \
  asINT32 local_segment_index = 0;                                                                                      \
  asINT32 local_segment  = local_ring_segments[local_segment_index];                                                    \
  asINT32 local_offset   = ring_segments[local_segment].offset;                                                         \
  asINT32 remote_offset  = ringmod(local_offset + surfel_offset, n_surfels_in_ring);                                    \
  asINT32 remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);              \
  asINT32 msg_depot_offset_inc;                                                                                         \
  BOOLEAN send_one_to_prior_remote_segment = FALSE;                                                                     \
  asINT32 n_local_ring_segments_to_process;                                                                             \
  asINT32 local_segments_so_far = 0;                                                                                    \
                                                                                                                        \
  if (local_segment == 0) {                                                                                             \
    if (ring_segments[remote_segment].offset == remote_offset)                                                          \
      /* The first segment is owned by this proc and there is a segment boundary in the */                              \
      /* rotated ring at offset 0. Thus we need an extra message at the very end that   */                              \
      /* contains just the first element of the first segment.                          */                              \
      n_local_ring_segments_to_process = n_local_ring_segments + 1;                                                     \
    else                                                                                                                \
      n_local_ring_segments_to_process = n_local_ring_segments;                                                         \
  } else {                                                                                                              \
    n_local_ring_segments_to_process = n_local_ring_segments;                                                           \
    if (ring_segments[remote_segment].offset == remote_offset)                                                          \
      /* The first segment owned by this proc is not segment 0 but is aligned with a segment in the    */               \
      /* rotated ring. The first message to send is a one element message to the prior remote segment. */               \
      send_one_to_prior_remote_segment = TRUE;                                                                          \
  }                                                                                                                     \
                                                                                                                        \
  BOOLEAN keep_going = TRUE;                                                                                            \
  for ( ; keep_going && ((keep_going = next_send_message(msg_length, msg_depot_offset_inc,                              \
                                                         remote_proc, send_one_to_prior_remote_segment,                 \
                                                         local_segment_index,                                           \
                                                         local_segment, remote_segment,                                 \
                                                         local_offset,  remote_offset,                                  \
                                                         surfel_offset, local_ring_segments, n_local_ring_segments,     \
                                                         n_local_ring_segments_to_process, local_segments_so_far,       \
                                                         ring_segments, n_ring_segments, n_surfels_in_ring)), TRUE);    \
       msg_depot_offset = ringmod(msg_depot_offset + msg_depot_offset_inc, __(ring)->m_n_surfels_per_frame_on_proc))


inline BOOLEAN next_recv_message(asINT32 &msg_length, asINT32 &msg_depot_offset_inc,
                                 asINT32 &remote_proc, BOOLEAN &recv_one_from_next_remote_segment,
                                 asINT32 &local_segment_index,
                                 asINT32 &local_segment, asINT32 &remote_segment,
                                 asINT32 &local_offset,  asINT32 &remote_offset,
                                 asINT32 surfel_offset, sINT32 *local_ring_segments, asINT32 n_local_ring_segments,
                                 asINT32 n_local_ring_segments_to_process, asINT32 &local_segments_so_far,
                                 asINT32 offset_after_front_half_of_first_segment,
                                 MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, asINT32 n_surfels_in_ring)
{
  if (recv_one_from_next_remote_segment) {
    recv_one_from_next_remote_segment = FALSE;
    msg_length = 1;
    msg_depot_offset_inc = 1;
    asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);
    remote_proc = ring_segments[segment_after_remote_segment].proc;

    if (local_segments_so_far < n_local_ring_segments_to_process) {
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring);
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);
      return TRUE;
    }

    return FALSE;
  }
    
  remote_proc = ring_segments[remote_segment].proc;

  asINT32 segment_after_local_segment = ringmod(local_segment + 1, n_ring_segments);
  asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);

  asINT32 segment_after_local_segment_offset = ((local_segments_so_far == n_local_ring_segments)
                                                ? offset_after_front_half_of_first_segment
                                                : ((segment_after_local_segment == 0) 
                                                   ? n_surfels_in_ring 
                                                   : ring_segments[segment_after_local_segment].offset));
  asINT32 segment_after_remote_segment_offset = ((segment_after_remote_segment == 0) 
                                                 ? n_surfels_in_ring 
                                                 : ring_segments[segment_after_remote_segment].offset);

  asINT32 local_segment_remaining_length = segment_after_local_segment_offset - local_offset;
  asINT32 remote_segment_remaining_length = segment_after_remote_segment_offset - remote_offset;

  if (local_segment_remaining_length < remote_segment_remaining_length) {
    local_segments_so_far++;
    if (local_segments_so_far < n_local_ring_segments_to_process) {
      msg_length = local_segment_remaining_length + 1;
      msg_depot_offset_inc = local_segment_remaining_length + 1;
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring); // ?????
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);
      return TRUE;  // keep going
    } else if (local_segments_so_far == n_local_ring_segments) {
      msg_length = local_segment_remaining_length + 1;
      return FALSE; // stop
    } else {
      // If we reach here, we're processing the front half of the first segment and we don't want to send
      // one extra element
      msg_length = local_segment_remaining_length;
      return FALSE; // stop
    }

  } else if (local_segment_remaining_length == remote_segment_remaining_length) {
    msg_length = remote_segment_remaining_length;
    msg_depot_offset_inc = remote_segment_remaining_length;

    local_segments_so_far++;
    if (local_segments_so_far <= n_local_ring_segments) {
      recv_one_from_next_remote_segment = TRUE;
      return TRUE;
    } else {
      // If we reach here, we're processing the front half of the first segment and we don't want to send
      // one extra element
      return FALSE;
    }
  } else {
    msg_length = remote_segment_remaining_length;
    msg_depot_offset_inc = remote_segment_remaining_length;
    local_offset = local_offset + remote_segment_remaining_length; // mod not needed since we stay in same segment
    remote_offset = ring_segments[segment_after_remote_segment].offset;
    remote_segment = segment_after_remote_segment;
    return TRUE;  // keep going
  }
}  

// msg_length ranges from 1 to n_surfels_in_ring.
// msg_depot_offset ranges from 0 to ring->n_local_surfels
// + ring->n_local_ring_segments - 1 (i.e. the total number of recv depots). 
// _surfel_offset must be positive. If this is set to S
// for an exterior ring E receiving from an interior ring I,
// it should be set to ring->n_surfels_in_ring - S for I 
// receiving to E. 
#define MLRF_DO_RECV_MESSAGES(msg_length, msg_depot_offset, remote_proc,  /* Bound by macro */                          \
                              _ring, _surfel_offset)                      /* Inputs to macro */                         \
  MLRF_RING __(ring) = (_ring);                                                                                         \
  asINT32 n_surfels_in_ring       = __(ring)->m_n_surfels_per_frame_in_ring;                                            \
  asINT32 surfel_offset = ringmod(n_surfels_in_ring - _surfel_offset, n_surfels_in_ring);                               \
  /* surfel_offset used here is the index in the receiving ring that matches surfel zero in the sending ring */         \
  MLRF_RING_SEGMENT ring_segments = __(ring)->ring_segments();                                                          \
  asINT32 n_ring_segments         = __(ring)->n_ring_segments();                                                        \
                                                                                                                        \
  sINT32  *local_ring_segments    = __(ring)->ring_segments_on_proc(); /* indices of segments owned by this proc */     \
  asINT32 n_local_ring_segments   = __(ring)->n_ring_segments_on_proc();                                                \
  asINT32 msg_length;                                                                                                   \
                                                                                                                        \
  asINT32 remote_proc;                                                                                                  \
  /* We have to recv messages in the same order they are sent by remote procs. Thus we start at */                      \
  /* the first segment owned by this proc that is after surfel_offset.                          */                      \
  asINT32 local_depots_before_surfel_offset;                                                                            \
  asINT32 local_offset;                                                                                                 \
  asINT32 n_local_depots = __(ring)->m_n_surfels_per_frame_on_proc + n_local_ring_segments;                             \
  asINT32 local_segment_index = find_local_segment_at_surfel_offset(local_ring_segments, n_local_ring_segments,         \
                                                                    ring_segments, n_ring_segments, surfel_offset,      \
                                                                    n_surfels_in_ring,                                  \
                                                                    local_offset, local_depots_before_surfel_offset);   \
  asINT32 msg_depot_offset = ringmod(local_depots_before_surfel_offset, n_local_depots);                                \
  asINT32 local_segment  = local_ring_segments[local_segment_index];                                                    \
  asINT32 remote_offset  = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring);                                 \
  asINT32 remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);              \
  asINT32 msg_depot_offset_inc;                                                                                         \
  BOOLEAN recv_one_from_next_remote_segment = FALSE;                                                                    \
  asINT32 n_local_ring_segments_to_process;                                                                             \
  asINT32 local_segments_so_far = 0;                                                                                    \
  asINT32 offset_after_front_half_of_first_segment;                                                                     \
                                                                                                                        \
  if (ring_segments[local_segment].offset == local_offset) {                                                            \
    offset_after_front_half_of_first_segment = 0; /* not actually used */                                               \
    n_local_ring_segments_to_process = n_local_ring_segments;                                                           \
  } else {                                                                                                              \
    /* The first local segment is processed in 2 pieces. The back end is processed first */                             \
    /* and the front end is processed last (i.e. as the last segment in the ring).       */                             \
    offset_after_front_half_of_first_segment = local_offset;                                                            \
    n_local_ring_segments_to_process = n_local_ring_segments + 1;                                                       \
  }                                                                                                                     \
                                                                                                                        \
  BOOLEAN keep_going = TRUE;                                                                                            \
  for ( ; keep_going                                                                                                    \
          && ((keep_going = next_recv_message(msg_length, msg_depot_offset_inc,                                         \
                                              remote_proc, recv_one_from_next_remote_segment,                           \
                                              local_segment_index,                                                      \
                                              local_segment, remote_segment,                                            \
                                              local_offset,  remote_offset,                                             \
                                              surfel_offset, local_ring_segments, n_local_ring_segments,                \
                                              n_local_ring_segments_to_process, local_segments_so_far,                  \
                                              offset_after_front_half_of_first_segment,                                 \
                                              ring_segments, n_ring_segments, n_surfels_in_ring)),                      \
              TRUE);                                                                                                    \
        msg_depot_offset = ringmod(msg_depot_offset + msg_depot_offset_inc, n_local_depots))          


inline asINT32 find_local_segment_at_surfel_offset(sINT32 *local_ring_segments, asINT32 n_local_ring_segments,
                                                   MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, 
                                                   asINT32 surfel_offset, asINT32 n_surfels_in_ring, asINT32 &local_offset,
                                                   asINT32 &local_depots_before_surfel_offset)
{
  local_depots_before_surfel_offset = 0;
  for (asINT32 i=0; i<n_local_ring_segments; i++) {
    asINT32 segment = local_ring_segments[i];
    asINT32 segment_offset = ring_segments[segment].offset;
    if (segment_offset >= surfel_offset) {
      // surfel offset falls in front of segment
      local_offset = segment_offset;
      return i;
    }
    asINT32 next_segment_offset = ((segment == n_ring_segments - 1) 
                                   ? n_surfels_in_ring 
                                   : ring_segments[segment + 1].offset);
    if (next_segment_offset > surfel_offset) {
      // surfel_offset falls inside segment
      local_depots_before_surfel_offset += surfel_offset - segment_offset;
      local_offset = surfel_offset;
      return i;
    }
    local_depots_before_surfel_offset += next_segment_offset - segment_offset + 1;
  }

  // surfel_offset falls after the last local segment, so return local segment 0.
  // local_depots_before_surfel_offset is the sum of all local segment lengths.
  local_offset = ring_segments[local_ring_segments[0]].offset;
  return 0; 
}

#if DEBUG
VOID check_if_all_sliding_mesh_surfels_in_rings();
#endif

#endif //  _SIMENG_MLRF_H
