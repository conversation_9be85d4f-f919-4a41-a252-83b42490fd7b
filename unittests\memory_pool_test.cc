
#include "gtest/gtest.h"
#include "../memory_pool.h"
#include <vector>
#include <random>
#include <chrono>
#include <algorithm>

template<typename T>
class MemoryPoolFixture : public ::testing::Test {
public:

};

typedef ::testing::Types< tMEMORY_POOL<double,13,256> ,
                          tMEMORY_POOL<double,13,-1>  ,
                          tMEMORY_POOL<double,-1,256> ,
                          tMEMORY_POOL<double,-1,-1> > MemoryPoolTypes;

TYPED_TEST_CASE(MemoryPoolFixture, MemoryPoolTypes);

TYPED_TEST(MemoryPoolFixture,BasicInformation)
{
  TypeParam pool(13,256);

  EXPECT_EQ(pool.n_bytes_per_array(),104);
  EXPECT_EQ(pool.array_size(),13);
  EXPECT_EQ(pool.block_size(),256);
}

TYPED_TEST(MemoryPoolFixture,PopAndPush)
{
  TypeParam pool(13,256);

  std::vector<double*> values, new_values;

  for(int i=0;i < 10000; i++) {
    values.push_back(pool.pop());
    EXPECT_EQ( pool.n_blocks() , (i / 256)+1 );
  }

  EXPECT_EQ( pool.count_free_objects() , 240 );

  // now shuffle up the pointers and start giving them back to the pool
  unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
  std::shuffle(values.begin(), values.end(), std::default_random_engine(seed));

  size_t free_count = 240;
  size_t i = 0;
  for(int i=0; i<10000; i++) {
    pool.push(values[i]);
    ++free_count;
    if ( (i % 1000) == 0 ) {
      EXPECT_EQ( pool.count_free_objects() , free_count ) << "free_count: " << free_count;
      new_values.push_back(pool.pop());
      --free_count;
    }
  }

  EXPECT_EQ( new_values.size(), 10 );

  for(auto p : new_values) {
    pool.push(p);
  }

  EXPECT_EQ( pool.count_free_objects(), 10240 );

}




