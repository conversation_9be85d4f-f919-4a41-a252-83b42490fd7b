/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "sim.h"
#include "fan.h"
#include "fan_comm.h"
#include "thread_run.h"
#include "voxel_dyn_sp.h"
#include "comm_groups.h"
#include "strand_mgr.h"

VOID eMPI_fan_comm_descriptor_type_init(MPI_Datatype *eMPI_type)
{
  begin_mpi_type() {
    mpi_t(sFAN_COMM_DESCRIPTOR,       sINT16,         physics_descriptor_index);
    mpi_t(sFAN_COMM_DESCRIPTOR,       sINT32,         max_voxel_delta_t_this_sp);
    mpi_t(sFAN_COMM_DESCRIPTOR,       dFLOAT,         n_finest_voxels_this_sp);
  } end_mpi_type(sFAN_COMM_DESCRIPTOR, eMPI_type);
} 

static MPI_Datatype eMPI_sFAN_COMM_DESCRIPTOR;
asINT32 n_global_fan_descs = 0;
static sFAN_DESCRIPTOR *global_fan_descs;
static asINT32 n_local_fan_descs = 0;
static sFAN_DESCRIPTOR **local_fan_descs;
static asINT32 *n_remote_fan_descs;
static sFAN_COMM_DESCRIPTOR **remote_fan_descs;

/* n_local_fan_descs is the number of fans in which this SP owns voxels.
 * local_fan_descs is an array of pointers to sFAN_DESCRIPTORs, one for
 * each of these fans.
 * 
 * remote_fan_descs is an array of arrays (1 per SP) of sFAN_COMM_DESCRIPTORs.
 * n_remote_fan_descs is an array of ints (1 per SP) giving the size of each 
 * entry in remote_fan_descs.
 *
 * n_remote_fan_descs[P] is equal to the value of n_local_fan_descs on SP P.
 *
 * Thus n_remote_fan_descs and remote_fan_descs tell this SP the fans that
 * are of interest to the remote SPs (i.e., for each remote SP, the fans in
 * which it owns voxels). On each timestep, this SP iterates over the fans
 * in which it owns voxels, and if it is time to average axial velocity for 
 * a fan F, for each remote SP that also owns voxels of F, this SP sends its 
 * contribution to the overall accumulated fan axial velocity.
 */

static VOID exchange_n_fan_descriptors() 
{
  n_remote_fan_descs = cnew asINT32 [ total_sps ];
  MPI_Request *request_array = xnew MPI_Request [ total_sps ];
  MPI_Status status;

  n_remote_fan_descs[my_proc_id] = n_local_fan_descs;

  ccDOTIMES(recv_sp, total_sps) {
    if (my_proc_id != recv_sp) {
      MPI_Irecv( &n_remote_fan_descs[recv_sp], 1, eMPI_asINT32, recv_sp, eMPI_N_FAN_DESCRIPTORS_TAG, eMPI_sp_cp_comm, &request_array[recv_sp]);
    }
  }

  ccDOTIMES(send_sp, total_sps) {
    if (my_proc_id != send_sp) {
      MPI_Send(&n_local_fan_descs, 1, eMPI_asINT32, send_sp, eMPI_N_FAN_DESCRIPTORS_TAG, eMPI_sp_cp_comm);
    }
  }

  ccDOTIMES(wait_sp, total_sps) {
    if (my_proc_id != wait_sp) {
      MPI_Wait(&request_array[wait_sp], &status);
    }
  }

  delete[] request_array;
}

static VOID exchange_fan_comm_descriptors() 
{
  remote_fan_descs = xnew FAN_COMM_DESCRIPTOR [ total_sps ];

  ccDOTIMES(allocate_sp, total_sps) {
    if (n_remote_fan_descs[allocate_sp] > 0) {
      remote_fan_descs[allocate_sp] = xnew sFAN_COMM_DESCRIPTOR [ n_remote_fan_descs[allocate_sp] ];
    } else {
      remote_fan_descs[allocate_sp] = nullptr;
    }
  }

  typedef MPI_Request * eMPI_Request_ptr;
  eMPI_fan_comm_descriptor_type_init(&eMPI_sFAN_COMM_DESCRIPTOR);
  MPI_Status status;
    
  ccDOTIMES(recv_sp, total_sps) {
    ccDOTIMES(desc_num, n_remote_fan_descs[recv_sp]) {
      if (my_proc_id != recv_sp) {
        MPI_Irecv(&remote_fan_descs[recv_sp][desc_num], 1, eMPI_sFAN_COMM_DESCRIPTOR, recv_sp, eMPI_FAN_DESCRIPTOR_TAG, eMPI_sp_cp_comm, &remote_fan_descs[recv_sp][desc_num].recv_request);
      }
      remote_fan_descs[recv_sp][desc_num].send_request = MPI_REQUEST_NULL;
    }
  }

  ccDOTIMES(send_desc, n_local_fan_descs) {
    FAN_DESCRIPTOR desc = local_fan_descs[send_desc];

    sFAN_COMM_DESCRIPTOR desc_sp;
    desc_sp.physics_descriptor_index = desc->physics_descriptor_index;
    desc_sp.max_voxel_delta_t_this_sp = desc->max_voxel_delta_t;
    desc_sp.n_finest_voxels_this_sp = desc->n_finest_voxels;

    desc->n_finest_voxels = 0;
    desc->max_voxel_delta_t = 0;

    remote_fan_descs[my_proc_id][send_desc].physics_descriptor_index = desc_sp.physics_descriptor_index;
    remote_fan_descs[my_proc_id][send_desc].max_voxel_delta_t_this_sp = desc_sp.max_voxel_delta_t_this_sp;
    remote_fan_descs[my_proc_id][send_desc].n_finest_voxels_this_sp = desc_sp.n_finest_voxels_this_sp;
 
    ccDOTIMES(send_sp, total_sps) {
      if (my_proc_id != send_sp) {
        MPI_Send(&desc_sp, 1, eMPI_sFAN_COMM_DESCRIPTOR, send_sp, 
                 eMPI_FAN_DESCRIPTOR_TAG, eMPI_sp_cp_comm);
      }
//      else {
//        remote_fan_descs[my_proc_id][send_desc].physics_descriptor_index = desc_sp.physics_descriptor_index;
//        remote_fan_descs[my_proc_id][send_desc].max_voxel_delta_t_this_sp = desc_sp.max_voxel_delta_t_this_sp;
//        remote_fan_descs[my_proc_id][send_desc].n_finest_voxels_this_sp = desc_sp.n_finest_voxels_this_sp;
//      }
    }
  }

  ccDOTIMES(wait_sp, total_sps) {
    if (my_proc_id != wait_sp) {
      ccDOTIMES(wait_desc_num, n_remote_fan_descs[wait_sp]) {
        MPI_Wait(&remote_fan_descs[wait_sp][wait_desc_num].recv_request, &status);
      }
    }
  }
}

inline VOID project_point_onto_plane(dFLOAT point[3], dFLOAT normal[3], // must be unit normal
				     dFLOAT d)
{
  // To project point P onto plane, observe that equation for line from P to plane is:
  //   
  //   r(t) = P + t * normal
  //
  // and equation for plane is:
  //
  //   Ax + By + Cz - D = 0
  //
  // where (A, B, C) is the normal. More concisely:
  //
  //   vdot(normal, point) - D = 0
  //
  // Plugging r(t) into the equation for the plane yields:
  //
  //   t = D - vdot(P, normal)

  dFLOAT t = d - vdot(point, normal);
  point[0] = point[0] + t * normal[0];
  point[1] = point[1] + t * normal[1];
  point[2] = point[2] + t * normal[2];
}

VOID filter_coord_sys_axes(cSTRING region_name, dFLOAT raxis[3], dFLOAT gaxis[3])
{
  BOOLEAN is_2d = sim.is_2d();
  if (is_2d) {
    raxis[2] = gaxis[2] = 0;
  }

  vunitize(raxis);

  if (is_2d) {
    // In 2d, the G axis is always 90 degrees counter-clockwise from the R axis,
    // or algebraically:  R = (rx,ry,0); G = Z cross R = (-ry,rx,0)
    gaxis[0] = -raxis[1];
    gaxis[1] = raxis[0];
    gaxis[2] = 0;
  }
  else {
    // Project G axis onto plane orthogonal to R axis that passes thru the origin (i.e., a plane 
    // whose normal is the R axis)
    project_point_onto_plane(gaxis, raxis, 0);
  }

  if ((gaxis[0] == 0) && (gaxis[1] == 0) && (gaxis[2] == 0)) {
    msg_internal_error("Inconsistent local coordinate system for region '%s'.", region_name);
  }

  vunitize(gaxis);
}


VOID sFAN_DESCRIPTOR::initialize()
{
  if (!is_initialized) {
    is_initialized = TRUE;

    PHYSICS_DESCRIPTOR pd = &sim.volume_physics_descs[physics_descriptor_index];
    
    BASE_FAN_PARAMETERS parms = (BASE_FAN_PARAMETERS)pd->parameters();
    asINT32 csys_index = parms->csys_index.cvalue();

    sG3_POINT _origin = g3_point_make(0, 0, 0); 
    sG3_VEC _raxis = g3_vec_make(1,0,0);
    sG3_VEC _gaxis = g3_vec_make(0,1,0);
    if (is_csys_index_non_default(csys_index)) {
      CSYS csys = sim.csys_table + csys_index;

      _origin = g3_xform_point(_origin, csys->l_to_g_xform);
      _raxis = g3_xform_vec(_raxis, csys->l_to_g_xform);
      _gaxis = g3_xform_vec(_gaxis, csys->l_to_g_xform);

      _origin.pcoord[0] += sim.case_origin[0];
      _origin.pcoord[1] += sim.case_origin[1];
      _origin.pcoord[2] += sim.case_origin[2];
    }
    else if (csys_index < 0 // no csys in old CDI file, so CP sets index to -1
	     && (pd->data_table_string == NULL)) { // make sure its a polynomial fan
      FAN_PARAMETERS p = (FAN_PARAMETERS)parms;
      if ((p->raxis[0].value != 0) || (p->raxis[1].value != 0) || (p->raxis[2].value != 0)) {
        _origin.pcoord[0] = p->origin[0].cvalue();
        _origin.pcoord[1] = p->origin[1].cvalue();
        _origin.pcoord[2] = p->origin[2].cvalue();
        _raxis.vcoord[0]  = p->raxis[0].cvalue();
        _raxis.vcoord[1]  = p->raxis[1].cvalue();
        _raxis.vcoord[2]  = p->raxis[2].cvalue();
        _gaxis.vcoord[0]  = p->gaxis[0].cvalue();
        _gaxis.vcoord[1]  = p->gaxis[1].cvalue();
        _gaxis.vcoord[2]  = p->gaxis[2].cvalue();
      }
    }
    else {
      _origin.pcoord[0] = sim.case_origin[0];
      _origin.pcoord[1] = sim.case_origin[1];
      _origin.pcoord[2] = sim.case_origin[2];
    }
	
    origin[0] = g3_point_x(_origin);
    origin[1] = g3_point_y(_origin);
    origin[2] = g3_point_z(_origin);

    raxis[0] = g3_vec_x(_raxis);
    raxis[1] = g3_vec_y(_raxis);
    raxis[2] = g3_vec_z(_raxis);

    gaxis[0] = g3_vec_x(_gaxis);
    gaxis[1] = g3_vec_y(_gaxis);
    gaxis[2] = g3_vec_z(_gaxis);
    
    filter_coord_sys_axes(pd->name, raxis, gaxis);

    if (pd->data_table_string) {
      fantable = xnew cFANTABLE(pd->data_table_string);
      if (!fantable->IsValid())
        msg_internal_error("Failure to initialize data table for fan \"%s\"", pd->name);

      dFLOAT slope, offset;
      find_units_conversion_coefficients(fantable->GetVelocityUnit(), "LatticeVelocity", &slope, &offset);
      fantable->ConvertVelocityUnit("LatticeVelocity", slope, offset);

      find_units_conversion_coefficients(fantable->GetDataUnit(FT_DATA_PRESS_CHANGE), "LatticeDynamicPressure", 
					 &slope, &offset);
      fantable->ConvertDataUnit(FT_DATA_PRESS_CHANGE, "LatticeDynamicPressure", slope, offset);
      
      find_units_conversion_coefficients(fantable->GetDataUnit(FT_DATA_THRUST), "LatticeForce", 
					 &slope, &offset);
      fantable->ConvertDataUnit(FT_DATA_THRUST, "LatticeForce", slope, offset);
      

      TABLE_FAN_PARAMETERS p = (TABLE_FAN_PARAMETERS)parms;
      one_over_density_times_length = 1.0 / (p->mean_density.cvalue() * p->fan_length.cvalue());
      dFLOAT fan_area = PI * (p->fan_radius.cvalue() * p->fan_radius.cvalue()
			      - p->hub_radius.cvalue() * p->hub_radius.cvalue());
      density_radius_area = p->mean_density.cvalue() * p->fan_radius.cvalue() * fan_area;

      is_user_defined_tang_vel_model = p->tang_vel_model.cvalue() == CDI_TANG_VEL_MODEL_USER_DEFINED;
      one_over_fan_radius = 1.0 / p->fan_radius.cvalue();

      axial_force = 0;
      thrust = 0;
    } else {
      fantable = NULL;
    }
  }
}

//dFLOAT sFAN_DESCRIPTOR::n_finest_voxels {
//  return *(( dFLOAT*)(msg.buffer()));
//}

VOID initialize_global_fan_descriptors()
{

  ccDOTIMES(phys_desc_num, sim.n_fluid_physics_descs) {
    PHYSICS_DESCRIPTOR phys_desc = &sim.volume_physics_descs[phys_desc_num];
    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = phys_desc->phys_type_desc;
    BOOLEAN is_fan = CDI_PHYS_TYPE_IS_FAN(phys_type_desc->cdi_physics_type);
    if(is_fan) n_global_fan_descs++;
  }
  global_fan_descs = cnew sFAN_DESCRIPTOR [ n_global_fan_descs ]; 
  asINT32 global_fan_desc_num = 0;
  ccDOTIMES(phys_desc_num_2,  sim.n_fluid_physics_descs) {
    PHYSICS_DESCRIPTOR phys_desc = &sim.volume_physics_descs[phys_desc_num_2];
    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = phys_desc->phys_type_desc;
    BOOLEAN is_fan = CDI_PHYS_TYPE_IS_FAN(phys_type_desc->cdi_physics_type);
    if(is_fan) {
      FAN_DESCRIPTOR this_desc = &(global_fan_descs[global_fan_desc_num]);
      this_desc->physics_descriptor_index = phys_desc->index;
      this_desc->max_participating_sp = -1;

      global_fan_desc_num++;
    }
  }
}
 
FAN_DESCRIPTOR find_fan_desc(asINT32 fluid_physics_desc_index)
{
  ccDOTIMES(i, n_global_fan_descs) {
    FAN_DESCRIPTOR fan_desc = &global_fan_descs[i];
    if (fan_desc->physics_descriptor_index == fluid_physics_desc_index) {
      return fan_desc;
    }
  }

  return NULL;
}

VOID initialize_local_fan_descriptors()
{

  ccDOTIMES(i, n_global_fan_descs) {
    if (global_fan_descs[i].n_finest_voxels > 0)
      n_local_fan_descs++;
  }

  // Allocate the fan reduction descriptors
  if(n_local_fan_descs > 0) {
    local_fan_descs = xnew FAN_DESCRIPTOR [ n_local_fan_descs ];
  }
  else {
    local_fan_descs = NULL;
  }

  if (local_fan_descs) {
    asINT32 local_fan_desc_num = 0;
    ccDOTIMES(j, n_global_fan_descs) {
      if (global_fan_descs[j].n_finest_voxels > 0) {
        local_fan_descs[local_fan_desc_num] = &global_fan_descs[j];
        local_fan_desc_num++;
      }
    }
  }
}

VOID exchange_fan_info()
{
  if (n_global_fan_descs > 0) {
    exchange_n_fan_descriptors();
    exchange_fan_comm_descriptors();

    ccDOTIMES(sp, total_sps) {
      ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
        FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];

        ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
          FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];

          local_desc->n_finest_voxels = 0; // See conditional in avg_u_axial
          if(remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
            local_desc->max_voxel_delta_t = MAX(local_desc->max_voxel_delta_t,
                                                remote_desc->max_voxel_delta_t_this_sp);
            local_desc->time_of_next_avg = local_desc->max_voxel_delta_t - 1;
            local_desc->max_participating_sp = MAX(local_desc->max_participating_sp, sp);
          }
        }
      }
    }

    if (g_timescale.m_time > 0) {
      // update time_of_next_avg after ckpt restore
      ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
        FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
        local_desc->time_of_next_avg = (g_timescale.m_time - g_timescale.m_time % local_desc->max_voxel_delta_t
                                        + local_desc->max_voxel_delta_t - 1);
      }
    }
  } // n_global_fan_descs > 0
}

VOID sFAN_DESCRIPTOR::avg_u_axial()
{
  STP_PHYS_VARIABLE u_tilda_avg;
  STP_PHYS_VARIABLE factor;
  if (n_finest_voxels > 0) {
    u_axial_avg = u_axial_accum / n_finest_voxels;
#if DEBUG_INLINE_FAN
    msg_print("T %ld avg_u_axial u_axial_accum %f n_finest_voxels %f", g_timescale.m_time, u_axial_accum, n_finest_voxels);
#endif
    u_tilda_avg = u_tilda_accum / n_finest_voxels;
    factor = factor_accum / n_finest_voxels;
  } else {
    u_axial_avg = 0;
    u_tilda_avg = 0;
    factor = 1;
  }

  u_axial_accum = 0;
  u_tilda_accum = 0;
  factor_accum = 0;

  // At t=0, u_axial_avg is always 0 because no data has been sent
  if (fantable && (g_timescale.m_time > 0)) {
    if (max_participating_sp == my_proc_id) {
      asINT32 np = fantable->NumDataPoints(FT_DATA_PRESS_CHANGE);
      if (u_axial_avg < fantable->DataPointVelocity(FT_DATA_PRESS_CHANGE, 0)
          || u_axial_avg > fantable->DataPointVelocity(FT_DATA_PRESS_CHANGE, np - 1)) {
        STP_GEOM_VARIABLE fan_loc[3];
        fan_loc[0] = origin[0];
        fan_loc[1] = origin[1];
        fan_loc[2] = origin[2];
        simerr_report_error_code(SP_EER_FAN_AXIAL_VEL_DELTA_P, 0, fan_loc, u_axial_avg);
      }
      asINT32 nt = fantable->NumDataPoints(FT_DATA_THRUST);
      if (u_axial_avg < fantable->DataPointVelocity(FT_DATA_THRUST, 0)
          || u_axial_avg > fantable->DataPointVelocity(FT_DATA_THRUST, nt - 1)) {
        STP_GEOM_VARIABLE fan_loc[3];
        fan_loc[0] = origin[0];
        fan_loc[1] = origin[1];
        fan_loc[2] = origin[2];
        simerr_report_error_code(SP_EER_FAN_AXIAL_VEL_THRUST, 0, fan_loc, u_axial_avg);
      }
    }

    axial_force = (fantable->LookupDataValue(FT_DATA_PRESS_CHANGE, u_axial_avg)
                  * one_over_density_times_length);
    thrust = fantable->LookupDataValue(FT_DATA_THRUST, u_axial_avg);
    
    //PR44689
    sdFLOAT one_minus_iter_alpha = 1.0 - g_fan_force_iter_alpha;
    STP_PHYS_VARIABLE utemp = u_axial_avg;
    STP_PHYS_VARIABLE axial_force_0 = axial_force;
    utemp = u_tilda_avg + 0.5F * factor * axial_force_0 * max_voxel_delta_t;
    STP_PHYS_VARIABLE axial_force_1 = (fantable->LookupDataValue(FT_DATA_PRESS_CHANGE, utemp)
    				      * one_over_density_times_length);
    if (g_fan_force_iter_num > 0) {
      //msg_print("ts %d",sim.time);
      ccDOTIMES(i, g_fan_force_iter_num){
        //msg_print("iter %d: u_axial_avg = %g, axial_force = %g",i, utemp, axial_force_temp);
	utemp = 0.5F * factor * (one_minus_iter_alpha * axial_force_0 + g_fan_force_iter_alpha * axial_force_1) * max_voxel_delta_t + u_tilda_avg;
        axial_force_0 = axial_force_1;
	axial_force_1 = (fantable->LookupDataValue(FT_DATA_PRESS_CHANGE, utemp)
		           * one_over_density_times_length);
      }
      axial_force = axial_force_1;
      //msg_print("axial_force = %g", axial_force);
      thrust = fantable->LookupDataValue(FT_DATA_THRUST, utemp);
    }
  }
}

VOID cFAN_COMM::process_requests_impl() {

  // PR 52981 - Because fan comm is a blocking operation, we have to drain the
  // send queue to make sure the other SPs can reach this point as well.

  g_strand_mgr.m_send_queue->drain();

  do_fan_remote_comm_send(m_timestep);
  do_fan_remote_comm_recv(m_timestep);
}

/* The sends are done a timestep before the receives. When
 * max_voxel_delta_t is 1, there is a receive in timestep
 * 0, so there must be a preceding send, effectively in
 * timestep -1.
 */

VOID do_fan_remote_comm_post_initial_send()
{
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
    local_desc->send_buffer[FANS::U_AXIAL]         = local_desc->u_axial_accum;
    local_desc->send_buffer[FANS::N_FINEST_VOXELS] = local_desc->n_finest_voxels;
    local_desc->send_buffer[FANS::U_TILDA]         = local_desc->u_tilda_accum;
    local_desc->send_buffer[FANS::FACTOR]          = local_desc->factor_accum;
    ccDOTIMES(sp, total_sps) {
      ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
        FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
        if(remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
          int fan_tag = make_mpi_tag<eMPI_MSG::FANS>(local_desc->physics_descriptor_index);
          eMPI::isend(&local_desc->send_buffer[0], FANS::N_FAN_DATA, eMPI_dFLOAT, sp, fan_tag, eMPI_sp_cp_comm, &remote_desc->send_request);
        }
      }
    }
  }
}

VOID do_fan_remote_comm_send(BASETIME requested_timestep)
{

  MPI_Status status;

  
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
    if (local_desc->time_of_next_avg == requested_timestep) {
      ccDOTIMES(sp, total_sps) {
        ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
          FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
          if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
            complete_mpi_request_while_processing_cp_messages(&remote_desc->send_request, MPI_SLEEP_LONG);
          }
        }
      }
    }
  }


  ccDOTIMES(fan_desc_num_local_2, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local_2];
    if (local_desc->time_of_next_avg == requested_timestep) {
      local_desc->send_buffer[FANS::U_AXIAL]         = local_desc->u_axial_accum;
      local_desc->send_buffer[FANS::N_FINEST_VOXELS] = local_desc->n_finest_voxels;
      local_desc->send_buffer[FANS::U_TILDA]         = local_desc->u_tilda_accum;
      local_desc->send_buffer[FANS::FACTOR]          = local_desc->factor_accum;
      ccDOTIMES(sp, total_sps) {
        ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
          FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
          if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
            int fan_tag = make_mpi_tag<eMPI_MSG::FANS>(local_desc->physics_descriptor_index);
            eMPI::isend(&local_desc->send_buffer[0], FANS::N_FAN_DATA, eMPI_dFLOAT, sp, fan_tag, eMPI_sp_cp_comm, &remote_desc->send_request);
          }
        }
      }
    }
  }
}

VOID do_fan_remote_comm_post_initial_recv()
{
  MPI_Status status;
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
    ccDOTIMES(sp, total_sps) {
      ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
        FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
        if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
          int fan_tag = make_mpi_tag<eMPI_MSG::FANS>(local_desc->physics_descriptor_index);
          eMPI::irecv(&remote_desc->this_sp_buffer[0], FANS::N_FAN_DATA ,eMPI_dFLOAT, sp, fan_tag, eMPI_sp_cp_comm, &remote_desc->recv_request);
        }
      }
    }
  }
}


VOID wait_for_fan_remote_comm_initial_recvs_complete()
{
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
    local_desc->u_axial_accum = 0;
    local_desc->n_finest_voxels = 0;
    local_desc->u_tilda_accum = 0;
    local_desc->factor_accum = 0;

    ccDOTIMES(sp, total_sps) {
      ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
        FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
        if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
          complete_mpi_request_while_processing_cp_messages(&remote_desc->recv_request, MPI_SLEEP_LONG);
          local_desc->u_axial_accum   += remote_desc->this_sp_buffer[FANS::U_AXIAL];
          local_desc->n_finest_voxels += remote_desc->this_sp_buffer[FANS::N_FINEST_VOXELS];
          local_desc->u_tilda_accum   += remote_desc->this_sp_buffer[FANS::U_TILDA];
          local_desc->factor_accum    += remote_desc->this_sp_buffer[FANS::FACTOR];
        }
      }
    }

    local_desc->avg_u_axial();
    local_desc->n_finest_voxels = 0;
  }
}


VOID cancel_pending_fan_recvs()
{
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];

    ccDOTIMES(sp, total_sps) {
      ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
        FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
        if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
          if (remote_desc->recv_request != MPI_REQUEST_NULL) {
            int flag;
            MPI_Status status;
            MPI_Request_get_status(remote_desc->recv_request, &flag, &status );
            if (!flag) {
              MPI_Cancel(&remote_desc->recv_request);
            }
          }
        }
      }
    }
  }
}

VOID do_fan_remote_comm_recv(BASETIME requested_timestep)
{
  MPI_Status status;
  ccDOTIMES(fan_desc_num_local, n_local_fan_descs) {
    FAN_DESCRIPTOR local_desc = local_fan_descs[fan_desc_num_local];
    if (local_desc->time_of_next_avg == requested_timestep) {
      local_desc->u_axial_accum = 0;
      local_desc->n_finest_voxels = 0;
      local_desc->u_tilda_accum = 0;
      local_desc->factor_accum = 0;
      ccDOTIMES(sp, total_sps) {
        ccDOTIMES(fan_desc_num_remote, n_remote_fan_descs[sp]) {
          FAN_COMM_DESCRIPTOR remote_desc = &remote_fan_descs[sp][fan_desc_num_remote];
          if (remote_desc->physics_descriptor_index == local_desc->physics_descriptor_index) {
            complete_mpi_request_while_processing_cp_messages(&remote_desc->recv_request, MPI_SLEEP_LONG);
            local_desc->u_axial_accum   += remote_desc->this_sp_buffer[FANS::U_AXIAL];
            local_desc->n_finest_voxels += remote_desc->this_sp_buffer[FANS::N_FINEST_VOXELS];
            local_desc->u_tilda_accum   += remote_desc->this_sp_buffer[FANS::U_TILDA];
            local_desc->factor_accum    += remote_desc->this_sp_buffer[FANS::FACTOR];

            int fan_tag = make_mpi_tag<eMPI_MSG::FANS>(local_desc->physics_descriptor_index);

            eMPI::irecv(&remote_desc->this_sp_buffer[0], FANS::N_FAN_DATA, eMPI_dFLOAT, sp, fan_tag, eMPI_sp_cp_comm, &remote_desc->recv_request);
          }
        }
      }

      local_desc->avg_u_axial();
      local_desc->n_finest_voxels = 0;
      local_desc->time_of_next_avg += local_desc->max_voxel_delta_t;
    }
  }
}

size_t ckpt_fan_descs_len()
{
  return  sizeof(size_t) + (n_global_fan_descs < 0 ? 0 : n_global_fan_descs) * sizeof(LGI_CKPT_FAN_SUBREC);
}

VOID ckpt_fan_descs()
{
  if (n_global_fan_descs > 0) {
    LGI_CKPT_FAN_REC record;
    asINT32 record_length = sizeof(record) + n_global_fan_descs * sizeof(LGI_CKPT_FAN_SUBREC);

    lgi_write_init_tag (&record, LGI_CKPT_FAN_TAG, record_length);

    record.n_global_fan_descs = n_global_fan_descs;

    write_ckpt_lgi_head(record);

    ccDOTIMES(i, n_global_fan_descs) {
      FAN_DESCRIPTOR desc = &global_fan_descs[i];
      LGI_CKPT_FAN_SUBREC subrec;
      subrec.u_axial_accum   = desc->send_buffer[FANS::U_AXIAL];
      subrec.n_finest_voxels = desc->send_buffer[FANS::N_FINEST_VOXELS];
      subrec.u_tilda_accum   = desc->send_buffer[FANS::U_TILDA];
      subrec.factor_accum    = desc->send_buffer[FANS::FACTOR];
      write_ckpt_lgi(subrec);
    }
  }
}

VOID ckpt_fan_descs(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t len = ckpt_fan_descs_len();
  pio_ckpt_buff.write(&len);

  ccDOTIMES(i, n_global_fan_descs) {
    FAN_DESCRIPTOR desc = &global_fan_descs[i];
    pio_ckpt_buff.write(&desc->send_buffer);
  }
}

VOID read_ckpt_fan_descs()
{
  LGI_CKPT_FAN_REC record_head;
  read_lgi_head(record_head);

  if (record_head.n_global_fan_descs != n_global_fan_descs) {
    msg_internal_error("Number of fan reduction descriptors in checkpoint file disagrees"    
                       " with the internal simulator state");
  }
  
  ccDOTIMES(i, n_global_fan_descs) {
    FAN_DESCRIPTOR desc = &global_fan_descs[i];
    LGI_CKPT_FAN_SUBREC subrec;
    read_lgi(subrec);
    desc->u_axial_accum   = subrec.u_axial_accum;
    desc->n_finest_voxels = subrec.n_finest_voxels;
    desc->u_tilda_accum   = subrec.u_tilda_accum;
    desc->factor_accum = subrec.factor_accum;
  }      
}
