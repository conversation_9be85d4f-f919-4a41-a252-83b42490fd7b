/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
#ifndef _SIMENG_NEIGHBOR_SP_H
#define _SIMENG_NEIGHBOR_SP_H

#include "common_sp.h"

/*
 * cNEIGHBOR_SP represents a local mapping from the global MPI_Rank to the an
 * SP that the current SP actually communicates with. The mapping from MPI_Rank
 * to neighbor sp is maintained by cNEIGHBOR_SP_MAP.  cNEIGHBOR_SP is a small
 * wrapper around a neighbor sp index and its associated MPI rank. A
 * cNEIGHBOR_SP is considered to be "valid" when both the local nsp index and
 * the mpi rank are defined. A valid cNEIGHBOR_SP object can only be created by
 * cNEIGHBOR_SP_MAP. It is possible to create an "invalid" cNEIGHBOR_SP object,
 * which specifies just the nsp index and not the MPI rank. This is because the
 * cNEIGHBOR_SP class is used as a key in the SHOB FSETS, where the nsp index
 * is used to sort the corresponding groups, and certain magic values are used
 * to denote specific types of SHOBS. The surfel groups are especially guilty.
 */

class cNEIGHBOR_SP
{
  STP_PROC m_nsp;
  STP_PROC m_rank;

  // a valid neighbor_sp can only be created by cNEIGHBOR_SP_MAP;
  constexpr explicit cNEIGHBOR_SP(STP_PROC nsp, STP_PROC rank) : m_nsp(nsp), m_rank(rank) {};
  friend class cNEIGHBOR_SP_MAP;

  public:

  constexpr cNEIGHBOR_SP() : m_nsp(STP_PROC_INVALID), m_rank(STP_PROC_INVALID) {};
  constexpr explicit cNEIGHBOR_SP(STP_PROC nsp) : m_nsp(nsp), m_rank(STP_PROC_INVALID) {};

  constexpr STP_PROC nsp() const { return m_nsp; }
  constexpr STP_PROC rank() const { return m_rank; }
  constexpr bool is_valid() const { return m_nsp != STP_PROC_INVALID && m_rank != STP_PROC_INVALID; }

  constexpr bool operator < (const cNEIGHBOR_SP & b) const
  {
    return this->m_nsp < b.m_nsp;
  }

  constexpr bool operator != (const cNEIGHBOR_SP & b) const
  {
    return this->m_nsp != b.m_nsp;
  }

  constexpr bool operator == (const cNEIGHBOR_SP & b) const
  {
    return m_nsp == b.m_nsp;
  }

  constexpr static cNEIGHBOR_SP max() { return cNEIGHBOR_SP(STP_MAX_PROC_ID); }

};

inline std::ostream& operator << (std::ostream& os, const cNEIGHBOR_SP& nsp)
{
  os << "(n:" << nsp.nsp() << ",r:" << nsp.rank() << ")";
  return os;
}
  
// This iterator skips over invalid cNEIGHBOR_SPs in cNEIGHBOR_SP_MAP
class cNEIGHBOR_SP_MAP_ITERATOR
{
  cNEIGHBOR_SP * m_cur;
  cNEIGHBOR_SP const * const m_end;

public:

  cNEIGHBOR_SP_MAP_ITERATOR& operator++ ()
  {
    m_cur++;
    while ( (m_cur != m_end) && (!m_cur->is_valid())) {
      m_cur++;
    }
    return *this;
  }


  // only cNEIGHBOR_SP_MAP can create this iterator
  private:

  explicit cNEIGHBOR_SP_MAP_ITERATOR(cNEIGHBOR_SP *cur, cNEIGHBOR_SP *end) : m_cur(cur), m_end(end) 
  {
    if ( (m_cur != m_end) && (!m_cur->is_valid()))
    {
      ++(*this);
    }
  }

  friend class cNEIGHBOR_SP_MAP;

  public:

  bool operator != (const cNEIGHBOR_SP_MAP_ITERATOR& b)
  {
    return this->m_cur != b.m_cur;
  }

  const cNEIGHBOR_SP & operator * ()
  {
    return *m_cur;
  }

};

class cNEIGHBOR_SP_MAP
{
  using ITERATOR = cNEIGHBOR_SP_MAP_ITERATOR;
  cNEIGHBOR_SP * m_rank_to_nsp;
  // STP_PROC *     m_nsp_to_rank;
  STP_PROC m_n_neighbor_sps;
  STP_PROC m_total_sps;

  public: 

  cNEIGHBOR_SP_MAP() : m_n_neighbor_sps(0), 
                       m_rank_to_nsp(nullptr),
                       m_total_sps(0) {};

  ~cNEIGHBOR_SP_MAP() {
    if (m_rank_to_nsp) {
      delete [] m_rank_to_nsp;
    }
  }

  // no copying, no moving
  cNEIGHBOR_SP_MAP(const cNEIGHBOR_SP_MAP&) = delete;
  cNEIGHBOR_SP_MAP(cNEIGHBOR_SP_MAP&) = delete;
  cNEIGHBOR_SP_MAP& operator = (const cNEIGHBOR_SP_MAP&) = delete;
  cNEIGHBOR_SP_MAP& operator = (cNEIGHBOR_SP_MAP&&) = delete;

  void init_rank_map(STP_PROC nprocs)
  {
    m_total_sps = nprocs;
    m_rank_to_nsp = new cNEIGHBOR_SP[nprocs]{};
  }

  bool is_rank_present(STP_PROC rank) const
  {
    cassert(m_rank_to_nsp);
    cassert(rank >= 0);
    cassert(rank < m_total_sps);
    cNEIGHBOR_SP& nsp = m_rank_to_nsp[rank];
    return (nsp.is_valid());
  }

  cNEIGHBOR_SP get_nsp(STP_PROC rank) const
  {
    cassert(m_rank_to_nsp);
    cassert(rank >= 0);
    cassert(rank < m_total_sps);
    cNEIGHBOR_SP& nsp = m_rank_to_nsp[rank];
    assert(nsp.is_valid());
    return nsp;
  }

  void add_nsp(STP_PROC rank)
  {
    cassert(m_rank_to_nsp);
    cassert(rank >= 0);
    cassert(rank < m_total_sps);
    cNEIGHBOR_SP& nsp = m_rank_to_nsp[rank];
    if ( !nsp.is_valid() ) {
      nsp = cNEIGHBOR_SP(m_n_neighbor_sps,rank);
      m_n_neighbor_sps++;
    }
  }

  STP_PROC get_n_neighbor_sps() const { return m_n_neighbor_sps; }

  ITERATOR begin() const { 
    cassert(m_rank_to_nsp != nullptr);
    return ITERATOR(m_rank_to_nsp, m_rank_to_nsp + m_total_sps);
  }

  ITERATOR end() const { 
    cassert(m_rank_to_nsp != nullptr);
    return ITERATOR(m_rank_to_nsp + m_total_sps, m_rank_to_nsp + m_total_sps);
  }

};

#endif
