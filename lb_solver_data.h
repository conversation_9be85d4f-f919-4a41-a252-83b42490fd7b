/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_LB_SOLVER_DATA_H
#define _SIMENG_LB_SOLVER_DATA_H

#include "common_sp.h"
#include "shob.h"
#include "vectorization_support.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "debug_print_spec.h"
#include "meas_cell.h"
#include "face_polygons.h"
#include "gpu_host_init.h"
#include "mme_ckpt.h"


inline namespace SIMULATOR_NAMESPACE { 

// forward declarations specific to this solver data-block
template <typename UBLK_TYPE_TAG> class tLB_DATA; 
template <typename UBLK_TYPE_TAG> class tUBLK_LB_DATA; 
template <typename UBLK_TYPE_TAG> class tNEARBLK_LB_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_FROZEN_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_GEOM_DATA;

template <typename SFL_TYPE_TAG> struct tSURFEL_LB_DATA;
template <typename SFL_TYPE_TAG> struct tSURFEL_S2S_LB_DATA;
template<typename SFL_TYPE_TAG> struct tSURFEL_FROZEN_DATA;
template<typename SFL_TYPE_TAG> struct tSURFEL_V2S_LB_DATA;
typedef tLB_DATA<UBLK_SDFLOAT_TYPE_TAG>  sLB_DATA,         *LB_DATA;
typedef tLB_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBFLOAT_LB_DATA, *UBFLOAT_LB_DATA;

typedef tUBLK_LB_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_LB_DATA,         *UBLK_LB_DATA;
typedef tUBLK_LB_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_LB_DATA, *UBLK_UBFLOAT_LB_DATA;

typedef tNEARBLK_LB_DATA<UBLK_SDFLOAT_TYPE_TAG> sNEAR_UBLK_LB_DATA, *NEAR_UBLK_LB_DATA;
typedef tNEARBLK_LB_DATA<UBLK_UBFLOAT_TYPE_TAG> sNEAR_UBLK_UBFLOAT_LB_DATA, *NEAR_UBLK_UBFLOAT_LB_DATA;

typedef tNEARBLK_FROZEN_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_FROZEN_DATA, *NEAR_UBLK_FROZEN_DATA;

typedef tNEARBLK_GEOM_DATA<UBLK_SDFLOAT_TYPE_TAG> sNEAR_UBLK_GEOM_DATA,         *NEAR_UBLK_GEOM_DATA;
typedef tNEARBLK_GEOM_DATA<UBLK_UBFLOAT_TYPE_TAG> sNEAR_UBLK_UBFLOAT_GEOM_DATA, *NEAR_UBLK_UBFLOAT_GEOM_DATA;

template <typename UBLK_TYPE_TAG> class tUBLK_PORE_LB_DATA;
typedef tUBLK_PORE_LB_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_PORE_LB_DATA,         *UBLK_PORE_LB_DATA;
typedef tUBLK_PORE_LB_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_PORE_LB_DATA, *UBLK_UBFLOAT_PORE_LB_DATA;

template <typename UBLK_TYPE_TAG> class tUBLK_CBF_LB_DATA;
typedef tUBLK_CBF_LB_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_CBF_LB_DATA,         *UBLK_CBF_LB_DATA;
typedef tUBLK_CBF_LB_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_CBF_LB_DATA, *UBLK_UBFLOAT_CBF_LB_DATA;

#ifdef BUILD_GPU
typedef tUBLK_LB_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_LB_DATA,         *MBLK_LB_DATA;
typedef tUBLK_LB_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_LB_DATA, *MBLK_UBFLOAT_LB_DATA;

typedef tNEARBLK_LB_DATA<MBLK_SDFLOAT_TYPE_TAG> sNEAR_MBLK_LB_DATA, *NEAR_MBLK_LB_DATA;
typedef tNEARBLK_LB_DATA<MBLK_UBFLOAT_TYPE_TAG> sNEAR_MBLK_UBFLOAT_LB_DATA, *NEAR_MBLK_UBFLOAT_LB_DATA;

typedef tNEARBLK_FROZEN_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_FROZEN_DATA,            *NEAR_MBLK_FROZEN_DATA;

typedef tNEARBLK_GEOM_DATA<MBLK_SDFLOAT_TYPE_TAG> sNEAR_MBLK_GEOM_DATA,         *NEAR_MBLK_GEOM_DATA;
typedef tNEARBLK_GEOM_DATA<MBLK_UBFLOAT_TYPE_TAG> sNEAR_MBLK_UBFLOAT_GEOM_DATA, *NEAR_MBLK_UBFLOAT_GEOM_DATA;

typedef tSURFEL_LB_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_LB_DATA, *MSFL_LB_DATA;
typedef tSURFEL_S2S_LB_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_S2S_LB_DATA, *MSFL_S2S_LB_DATA;
typedef tSURFEL_FROZEN_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_FROZEN_DATA, *MSFL_FROZEN_DATA;
typedef tSURFEL_V2S_LB_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_V2S_LB_DATA, *MSFL_V2S_LB_DATA;	 
#endif

template<size_t N_VOXELS>
struct tUBLK_LB_SEND_FIELD
{
#if DISABLE_COMM_SOLVER_DATA_COMPRESSION
  STP_PHYS_VARIABLE m_vel[N_TIME_INDICES][3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_TIME_INDICES][N_VOXELS];
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE m_potential[N_TIME_INDICES][N_VOXELS];
#else
  STP_PHYS_VARIABLE m_visc_sij[N_VOXELS];
  STP_PHYS_VARIABLE m_pressure[N_TIME_INDICES][N_VOXELS];
#endif
#else
  STP_PHYS_VARIABLE m_vel[3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_VOXELS];
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE m_potential[N_VOXELS];
  STP_PHYS_VARIABLE m_grad_potential[3][N_VOXELS];
#else
  STP_PHYS_VARIABLE m_visc_sij[N_VOXELS];
  STP_PHYS_VARIABLE m_pressure[N_VOXELS];
#endif
#endif
}; 

template<size_t N_VOXELS>
struct tUBLK_LB_MME_SEND_FIELD
{
  STP_PHYS_VARIABLE m_vel[3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_VOXELS];
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE m_potential[N_VOXELS];
#endif //BUILD_5G_LATTICE
};

template<size_t N_VOXELS>
struct tUBLK_PORE_LB_SEND_FIELD
{ //for 5G large_pore
  STP_PHYS_VARIABLE m_porosity_input[N_VOXELS];
  STP_PHYS_VARIABLE m_wall_potential[N_VOXELS];
  VOXEL_STATE       m_full_states[N_STATES][N_VOXELS];
};


template<size_t N_VOXELS>
struct tNEARBLK_LB_SEND_FIELD
{
  sdFLOAT m_v2s_dist[N_VOXELS];
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  sdFLOAT m_pre_cf_n[N_VOXELS];
  sdFLOAT m_pre_delp_factor[N_VOXELS];  //lb_energy
#endif
};

template<size_t N_VOXELS>
struct tNEARBLK_FROZEN_SEND_FIELD
{
  sdFLOAT m_ustar_lb[N_VOXELS];
  sdFLOAT m_density_frozen[N_VOXELS];
  sdFLOAT m_vel_frozen[3][N_VOXELS];
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tLB_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT density;
  ALIGNED_UBFLOAT pressure;
  ALIGNED_UBFLOAT vel[3];
#if BUILD_5G_LATTICE
  ALIGNED_UBFLOAT potential;
#endif
  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){
      using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print<3, N_VOXELS>(os, "velocity", vel, loop_limits(0, 2), print_voxel);
    sim_print<N_VOXELS>(os, "density", density, print_voxel);
    sim_print<N_VOXELS>(os, "pressure", pressure, print_voxel);
#if BUILD_5G_LATTICE
    sim_print<N_VOXELS>(os, "potential", potential, print_voxel);
#endif
  }

#undef ALIGNED_UBFLOAT
};
         
//------------------------------------------------------------------------------
// sUBLK_LB_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_LB_DATA
{
  EXTRACT_UBLK_TRAITS
  using sLB_DATA = tLB_DATA<UBLK_TYPE_TAG>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  sLB_DATA m_lb_data[N_TIME_INDICES];
  ALIGNED_UBFLOAT visc_sij;

#if BUILD_5G_LATTICE
  ALIGNED_UBFLOAT grad_potential[2][3];
#endif

#if BUILD_D39_LATTICE
  ALIGNED_UBFLOAT force[3];
  uINT8 vr_norm_mask[N_VOXELS];
#endif

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel) {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_LB_DATA");
    sim_print<N_VOXELS>(os, "visc_sij", visc_sij, print_voxel);
    sim_print_vs(os);
#if BUILD_5G_LATTICE
    sim_print<2,3,N_VOXELS>(os, "grad_potential", grad_potential,loop_limits(0,1),loop_limits(0,2),print_voxel);
#endif
#if BUILD_D39_LATTICE
    sim_print<3,N_VOXELS>(os, "force", force,loop_limits(0,2),print_voxel);
    sim_print<N_VOXELS>(os, "vr_norm_mask", vr_norm_mask,print_voxel);
#endif
    if (g_is_multi_component) {
      sim_print_loop_header(os, "LB_DATA", 0);
      m_lb_data[0].print_voxel_data(os, print_voxel);
      sim_print_vs(os);
    } else {
      ccDOTIMES(t_step,N_TIME_INDICES)
      {
        sim_print_loop_header(os, "LB_DATA", t_step);
        m_lb_data[t_step].print_voxel_data(os, print_voxel);
        sim_print_vs(os);
      }
    }
  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_LB_DATA *dest_ublk_lb_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_LB_DATA* d = dest_ublk_lb_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDOTIMES(axis, N_AXES) {
        d->m_lb_data[timestep].vel[axis][dest_voxel]  = m_lb_data[timestep].vel[axis][source_voxel];
      }
      d->m_lb_data[timestep].density [dest_voxel]     = m_lb_data[timestep].density [source_voxel];
#if BUILD_5G_LATTICE
      d->m_lb_data[timestep].potential[dest_voxel]    = m_lb_data[timestep].potential[source_voxel];
#else
      d->m_lb_data[timestep].pressure[dest_voxel]     = m_lb_data[timestep].pressure[source_voxel];
#endif
    }
#if BUILD_5G_LATTICE
    ccDOTIMES(timestep, 2) {
      ccDOTIMES(axis, N_AXES) {
          d->grad_potential[timestep][axis][dest_voxel]   = grad_potential[timestep][axis][source_voxel];
      }
    }
#endif
  }

  __DEVICE__
  VOID explode_voxel(tUBLK_LB_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_lb_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    asINT32 prior_timestep = (prior_solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    // static_assert( std::is_same<T,sdFLOAT[N_VOXELS]>::value, "Coarse Ublk must be sdFLOAT" );
    auto* d = fine_ublk_lb_data;

    // The compiler isn't smart enough to pull the vxFLOAT loads out of the
    // loops, so we have to preload all of the coarse ublk data before setting
    // the values in the vr fine ublk.

    vxFLOAT coarse_density = m_lb_data[prior_timestep].density[voxel_to_explode];
    vxFLOAT coarse_vel0 = m_lb_data[prior_timestep].vel[0][voxel_to_explode];
    vxFLOAT coarse_vel1 = m_lb_data[prior_timestep].vel[1][voxel_to_explode];
    vxFLOAT coarse_vel2 = m_lb_data[prior_timestep].vel[2][voxel_to_explode];
#if BUILD_5G_LATTICE
    vxFLOAT coarse_potential = m_lb_data[prior_timestep].potential[voxel_to_explode];
#else
    vxFLOAT coarse_pressure = m_lb_data[prior_timestep].pressure[voxel_to_explode];
#endif
    vxFLOAT coarse_visc_sij = this->visc_sij[voxel_to_explode];

    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
        d->m_lb_data[timestep].density[voxor] = coarse_density;
        d->m_lb_data[timestep].vel[0][voxor] = coarse_vel0;
        d->m_lb_data[timestep].vel[1][voxor] = coarse_vel1;
        d->m_lb_data[timestep].vel[2][voxor] = coarse_vel2;
#if BUILD_5G_LATTICE
        d->m_lb_data[timestep].potential[voxor] = coarse_potential;
#else
        d->m_lb_data[timestep].pressure[voxor] = coarse_pressure;
#endif
      }
    }

    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      d->visc_sij[voxor]              = coarse_visc_sij;
    }
#if BUILD_5G_LATTICE
    ccDOTIMES(timestep, 2) {
      ccDOTIMES(voxel, N_VOXELS) {
        ccDOTIMES(axis, N_AXES) {
          d->grad_potential[timestep][axis][voxel]   = grad_potential[prior_timestep][axis][voxel_to_explode];
        }
      }
    }
#endif

  }


  VOID copy_voxel_mme_data(tUBLK_LB_DATA *dest_ublk_lb_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_LB_DATA *d = dest_ublk_lb_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
    ccDOTIMES(axis, 3) {
      d->m_lb_data[timestep].vel[axis][dest_voxel] = m_lb_data[timestep].vel[axis][source_voxel];
    }
    d->m_lb_data[timestep].density[dest_voxel] = m_lb_data[timestep].density[source_voxel];
#if BUILD_5G_LATTICE
    d->m_lb_data[timestep].potential[dest_voxel] = m_lb_data[timestep].potential[source_voxel];
#endif
    }
  }

  VOID explode_voxel_mme(tUBLK_LB_DATA<tUBLK_UBFLOAT_TYPE_TAG<UBLK_TYPE_TAG::N_VOXELS>> *fine_ublk_lb_data,
                         asINT32 voxel_to_explode,
                         asINT32 solver_ts_index_mask)
  {
    asINT32 coarse_timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    auto d = fine_ublk_lb_data;

    asINT32 fine_timestep_index = 0; // explode_voxel_mme happens on odd fine timesteps which is always 0
    vxFLOAT coarse_vel0 = m_lb_data[coarse_timestep_index].vel[0][voxel_to_explode];
    vxFLOAT coarse_vel1 = m_lb_data[coarse_timestep_index].vel[1][voxel_to_explode];
    vxFLOAT coarse_vel2 = m_lb_data[coarse_timestep_index].vel[2][voxel_to_explode];
    vxFLOAT coarse_den = m_lb_data[coarse_timestep_index].density[voxel_to_explode];
#if BUILD_5G_LATTICE
    vxFLOAT coarse_potential = m_lb_data[coarse_timestep_index].potential[voxel_to_explode];
#endif
    ccDO_UBLK(voxor) {
      d->m_lb_data[fine_timestep_index].density[voxor] = coarse_den;
      d->m_lb_data[fine_timestep_index].vel[0][voxor]  = coarse_vel0;
      d->m_lb_data[fine_timestep_index].vel[1][voxor]  = coarse_vel1;
      d->m_lb_data[fine_timestep_index].vel[2][voxor]  = coarse_vel2;
#if BUILD_5G_LATTICE
      d->m_lb_data[fine_timestep_index].potential[voxor]   = coarse_potential;
#endif
    }
  }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas)
  {
    // writing density and velocity is handled at the ublk level, not here
  }    
  
  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size      += (sizeof(tUBLK_LB_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(tUBLK_LB_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }

  static VOID add_mme_send_size(asINT32 &send_size) {
    send_size      += (sizeof(tUBLK_LB_MME_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    auto field = reinterpret_cast<tUBLK_LB_SEND_FIELD<N_VOXELS>*>(send_data_info.send_buffer);
#if BUILD_5G_LATTICE
    memcpy(field->m_grad_potential, grad_potential[timestep_index], sizeof(field->m_grad_potential));
    //grad_potential has 2 time indices
    timestep_index = 0;
#endif
    memcpy(field->m_vel,       m_lb_data[timestep_index].vel,       sizeof(field->m_vel));
    memcpy(field->m_density,   m_lb_data[timestep_index].density,   sizeof(field->m_density));
#if BUILD_5G_LATTICE
    memcpy(field->m_potential, m_lb_data[timestep_index].potential, sizeof(field->m_potential));
#else
    memcpy(field->m_visc_sij,  visc_sij,                            sizeof(field->m_visc_sij));
    memcpy(field->m_pressure,  m_lb_data[timestep_index].pressure,  sizeof(field->m_pressure));
#endif

    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    auto field = reinterpret_cast<tUBLK_LB_SEND_FIELD<N_VOXELS>*>(recv_data_info.recv_buffer);
#if BUILD_5G_LATTICE
    memcpy(grad_potential[timestep_index], field->m_grad_potential, sizeof(field->m_grad_potential));
    //grad_potential has 2 time indice
    timestep_index = 0;
#endif
    memcpy(m_lb_data[timestep_index].vel,       field->m_vel,       sizeof(field->m_vel));
    memcpy(m_lb_data[timestep_index].density,   field->m_density,   sizeof(field->m_density));
#if BUILD_5G_LATTICE
    memcpy(m_lb_data[timestep_index].potential, field->m_potential, sizeof(field->m_potential));
#else
    memcpy(visc_sij,                            field->m_visc_sij,  sizeof(field->m_visc_sij));
    memcpy(m_lb_data[timestep_index].pressure,  field->m_pressure,  sizeof(field->m_pressure));
#endif
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
#if BUILD_5G_LATTICE
    timestep_index = 0;
#endif
    auto field = reinterpret_cast<tUBLK_LB_MME_SEND_FIELD<N_VOXELS>*>(send_buffer);
    memcpy(field->m_vel,       m_lb_data[timestep_index].vel,       sizeof(field->m_vel));
    memcpy(field->m_density,   m_lb_data[timestep_index].density,   sizeof(field->m_density));
#if BUILD_5G_LATTICE
    memcpy(field->m_potential, m_lb_data[timestep_index].potential, sizeof(field->m_potential));
#endif
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
#if BUILD_5G_LATTICE
    timestep_index = 0;
#endif
    auto field = reinterpret_cast<tUBLK_LB_MME_SEND_FIELD<N_VOXELS>*>(recv_buffer);
    memcpy(m_lb_data[timestep_index].vel,       field->m_vel,       sizeof(field->m_vel));
    memcpy(m_lb_data[timestep_index].density,   field->m_density,   sizeof(field->m_density));
#if BUILD_5G_LATTICE
    memcpy(m_lb_data[timestep_index].potential, field->m_potential, sizeof(field->m_potential));
#endif
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID reflect_velocity(STP_PHYS_VARIABLE mirror_sign_factor[N_AXES], VOXEL_NUM voxel) {
    ccDOTIMES(scheme, N_TIME_INDICES) {
      ccDOTIMES(axis, N_AXES) {
        m_lb_data[scheme].vel[axis][voxel] *= mirror_sign_factor[axis];
      }
    }
  }
#undef ALIGNED_UBFLOAT
};

#ifdef BUILD_GPU

INIT_MBLK(LB_DATA)
{
  COPY_UBLK_TO_MBLK(density);
  COPY_UBLK_TO_MBLK(pressure);
  VEC_COPY_UBLK_TO_MBLK(vel,3);
#if BUILD_5G_LATTICE
  COPY_UBLK_TO_MBLK(potential);
#endif
}

INIT_MBLK(UBLK_LB_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(m_lb_data,N_TIME_INDICES);
  COPY_UBLK_TO_MBLK(visc_sij);
#if BUILD_D39_LATTICE
  VEC_COPY_UBLK_TO_MBLK(force,3);
#endif
}

#endif

//------------------------------------------------------------------------------
// tNEARBLK_DP_PASS_FACTOR_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_DP_PASS_FACTORS_DATA {
  EXTRACT_UBLK_TRAITS
  dFLOAT dp_pas_factors[N_STATES][N_VOXELS];
};

//------------------------------------------------------------------------------
// sNEARBLK_LB_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_LB_DATA
{
  EXTRACT_UBLK_TRAITS
  using sNEARBLK_LB_SEND_FIELD = tNEARBLK_LB_SEND_FIELD<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  // pas_i(x) = 1 - p_into_surf_i*(x) / pfluid(x)
  //
  // p_into_surf_i(x) = sum_over_alpha { V_i_alpha(x) }
  ALIGNED_UBFLOAT post_advect_scale_factors[N_MOVING_STATES];

  // VINIT: Wasting one slot here. Visit later
  union {
    dFLOAT overlaps_non_lrf[N_CARTESIAN_SPEED1_LATVECS][N_VOXELS];
    struct {
      ALIGNED_UBFLOAT pre_cf_n;
      ALIGNED_UBFLOAT post_cf_n;
      ALIGNED_UBFLOAT lrf_s2s_factor;
      ALIGNED_UBFLOAT ustar_0;
      ALIGNED_UBFLOAT ustar_0_pair;
      ALIGNED_UBFLOAT pre_delp_factor;   //lb_energy
      ALIGNED_UBFLOAT post_delp_factor;  //lb_energy
    };
  };
  ALIGNED_UBFLOAT lrf_s2v_weight; // new addition
  ALIGNED_UBFLOAT s2v_weight;     // new addition
  ALIGNED_UBFLOAT v2s_dist;
  ALIGNED_UBFLOAT lrf_v2s_dist;
#if BUILD_5G_LATTICE
  ALIGNED_UBFLOAT srf_potential[N_MOVING_STATES];
  ALIGNED_UBFLOAT srf_pressure[N_MOVING_STATES];
#endif

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_SURF_LB_DATA");
    sim_print<N_VOXELS>(os, "pre_cf_n", pre_cf_n, print_voxel);
    sim_print<N_VOXELS>(os, "post_cf_n", post_cf_n, print_voxel);
    sim_print<N_VOXELS>(os, "lrf_s2s_factor", lrf_s2s_factor, print_voxel);
    sim_print<N_VOXELS>(os, "ustar_0", ustar_0, print_voxel);
    sim_print<N_VOXELS>(os, "ustar_0_pair", ustar_0_pair, print_voxel);
    sim_print<N_VOXELS>(os, "lrf_s2v_weight", lrf_s2v_weight, print_voxel);
    sim_print<N_VOXELS>(os, "s2v_weight", s2v_weight, print_voxel);
    sim_print<N_VOXELS>(os, "v2s_dist", v2s_dist, print_voxel);
    sim_print<N_VOXELS>(os, "lrf_v2s_dist", lrf_v2s_dist, print_voxel);
    sim_print_vs(os);
  }

  __DEVICE__
  VOID explode_voxel(tNEARBLK_LB_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_lb_data, 
                     asINT32 voxel_to_explode,
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  { 
    auto d = fine_ublk_lb_data;
    vxFLOAT coarse_post_cf_n = post_cf_n[voxel_to_explode];
    vxFLOAT coarse_pre_cf_n = pre_cf_n[voxel_to_explode];
    vxFLOAT coarse_post_delp_factor = post_delp_factor[voxel_to_explode];
    vxFLOAT coarse_pre_delp_factor = pre_delp_factor[voxel_to_explode];
    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      d->post_cf_n[voxor] = coarse_post_cf_n;
      d->pre_cf_n[voxor] = coarse_pre_cf_n;
      d->post_delp_factor[voxor] = coarse_post_delp_factor;
      d->pre_delp_factor[voxor] = coarse_pre_delp_factor;
    }
  }

  // @@@ Do not ckpt or comm the static portion of this object
 
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sNEARBLK_LB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sNEARBLK_LB_SEND_FIELD) / sizeof(sdFLOAT));
  }


  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index) {

    asINT32 n_dims = sim.num_dims;

    sNEARBLK_LB_SEND_FIELD* field = reinterpret_cast<sNEARBLK_LB_SEND_FIELD*>(send_data_info.send_buffer);
    memcpy(field->m_v2s_dist, v2s_dist, sizeof(field->m_v2s_dist));
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    memcpy(field->m_pre_cf_n, pre_cf_n, sizeof(field->m_pre_cf_n));
    memcpy(field->m_pre_delp_factor, pre_delp_factor, sizeof(field->m_pre_delp_factor)); //lb_energy
#endif

    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
    
  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 timestep_index) {

    asINT32 n_dims = sim.num_dims;

    sNEARBLK_LB_SEND_FIELD* field = reinterpret_cast<sNEARBLK_LB_SEND_FIELD*>(recv_data_info.recv_buffer);
    memcpy(v2s_dist, field->m_v2s_dist, sizeof(field->m_v2s_dist));
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    memcpy(pre_cf_n, field->m_pre_cf_n, sizeof(field->m_pre_cf_n));
    memcpy(pre_delp_factor, field->m_pre_delp_factor, sizeof(field->m_pre_delp_factor));  //lb_energy
#endif

    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
    

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_LB_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(post_advect_scale_factors,N_MOVING_STATES);
  COPY_UBLK_TO_MBLK(pre_cf_n);
  COPY_UBLK_TO_MBLK(post_cf_n);
  COPY_UBLK_TO_MBLK(lrf_s2s_factor);
  COPY_UBLK_TO_MBLK(ustar_0);
  COPY_UBLK_TO_MBLK(ustar_0_pair);
  COPY_UBLK_TO_MBLK(lrf_s2v_weight); // new addition
  COPY_UBLK_TO_MBLK(s2v_weight);     // new addition
  COPY_UBLK_TO_MBLK(v2s_dist);
  COPY_UBLK_TO_MBLK(lrf_v2s_dist);
#if BUILD_5G_LATTICE
  VEC_COPY_UBLK_TO_MBLK(srf_potential,N_MOVING_STATES);
  VEC_COPY_UBLK_TO_MBLK(srf_pressure,N_MOVING_STATES);
#endif
}
#endif

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MME_DNS_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT vel[3];
  ALIGNED_UBFLOAT density;
  uINT64 ckpt_len() {  return sizeof(*this); }
  VOID write_ckpt() {  write_ckpt_lgi(*this); }
  VOID read_ckpt() {
    static tUBLK_MME_DNS_DATA avg_mme_dns_data;
    read_lgi(avg_mme_dns_data);
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_dns_data, sizeof(avg_mme_dns_data));
  }
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MME_T_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT fluid_temp;

  uINT64 ckpt_len() {  return sizeof(*this); }

  VOID write_ckpt() {  write_ckpt_lgi(*this); }

  VOID read_ckpt() {
    static tUBLK_MME_T_DATA avg_mme_t_data;
    read_lgi(avg_mme_t_data);
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_t_data, sizeof(avg_mme_t_data));
  }
  VOID read_ckpt(u_char* buff, size_t& readSz) {
    static tUBLK_MME_T_DATA avg_mme_t_data;
    size_t size = sizeof(avg_mme_t_data);
    std::memcpy(&avg_mme_t_data, buff+readSz, size);
    readSz += size;
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_t_data, sizeof(avg_mme_t_data));
  }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, STP_PHYS_VARIABLE one_over_avg_mme_interval, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {
    auto& simc = get_simc_ref();
    auto& timescale = get_timescale_ref();
    asINT32 T_prior_index = timescale.m_t_pde_tm.prior_timestep_index(scale);
    sdFLOAT time_scale = (sdFLOAT)(1 << (simc.num_scales - scale -1)) * one_over_avg_mme_interval;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = fluid_temp[voxel] * time_scale;
    }
    meas++;
  }

//  VOID read_mme_ckpt() { read_ckpt(); }
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MME_CONDUCTION_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT solid_temp;

  uINT64 ckpt_len() {  return sizeof(*this); }

  VOID write_ckpt() {  write_ckpt_lgi(*this); }

  VOID read_ckpt() {
    static tUBLK_MME_CONDUCTION_DATA avg_mme_conduction_data;
    read_lgi(avg_mme_conduction_data);
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_conduction_data, sizeof(avg_mme_conduction_data));
  }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, STP_PHYS_VARIABLE one_over_avg_mme_interval, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {
    auto& simc = get_simc_ref();
    auto& timescale = get_timescale_ref();
    asINT32 conduction_prior_index = timescale.m_conduction_pde_tm.prior_timestep_index(scale);
    sdFLOAT time_scale = (sdFLOAT)(1 << (simc.num_scales - scale -1)) * one_over_avg_mme_interval;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = solid_temp[voxel] * time_scale;
    }
    meas++;
  }

//  VOID read_mme_ckpt() { read_ckpt(); }
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MME_TURB_DATA: public tUBLK_MME_DNS_DATA<UBLK_TYPE_TAG>
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT turb_ke;
  ALIGNED_UBFLOAT turb_df;
  ALIGNED_UBFLOAT turb_str_mag;

  uINT64 ckpt_len() {  return sizeof(*this); }
  VOID write_ckpt() {  write_ckpt_lgi(*this); }
  VOID read_ckpt() {
    static tUBLK_MME_TURB_DATA avg_mme_turb_data;
    read_lgi(avg_mme_turb_data);
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_turb_data, sizeof(avg_mme_turb_data));
  }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, STP_PHYS_VARIABLE one_over_avg_mme_interval, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {
    auto& simc = get_simc_ref();
    auto& timescale = get_timescale_ref();
    asINT32 prior_index = timescale.m_ke_pde_tm.prior_timestep_index(scale);
    sdFLOAT time_scale = (sdFLOAT)(1 << (simc.num_scales - scale -1)) * one_over_avg_mme_interval;
    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = turb_ke[voxel] * time_scale;
    }
    meas++;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
        meas->var[voxel] = turb_df[voxel] * time_scale;
    }
    meas++;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      // Stress tensor mag is not scaled by mass in meas files
      meas->var[voxel] = turb_str_mag[voxel] * time_scale;
    }
    meas++;
  }

//  VOID read_mme_ckpt() { read_ckpt(); }
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MME_UDS_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT uds;

  uINT64 ckpt_len() {  return sizeof(*this); }

  VOID write_ckpt() {  write_ckpt_lgi(*this); }

  VOID read_ckpt() {
    static tUBLK_MME_UDS_DATA avg_mme_uds_data;
    read_lgi(avg_mme_uds_data);
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_uds_data, sizeof(avg_mme_uds_data));
  }
  VOID read_ckpt(u_char* buff, size_t& readSz) {
    static tUBLK_MME_UDS_DATA avg_mme_uds_data;
    size_t size = sizeof(avg_mme_uds_data);
    std::memcpy(&avg_mme_uds_data, buff+readSz, size);
    readSz += size;
    if (sim.has_avg_mme_window)
      memcpy(this, &avg_mme_uds_data, sizeof(avg_mme_uds_data));
  }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, STP_PHYS_VARIABLE one_over_avg_mme_interval, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {
    auto& simc = get_simc_ref();
    auto& timescale = get_timescale_ref();
    //asINT32 UDS_prior_index = timescale.m_uds_pde_tm.prior_timestep_index(scale);
    sdFLOAT time_scale = (sdFLOAT)(1 << (simc.num_scales - scale -1)) * one_over_avg_mme_interval;
    /*#if BUILD_5G_LATTICE
    UDS_prior_index = 0;
    #endif*/
    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = uds[voxel] * time_scale;
    }
    meas++;
  }

//  VOID read_mme_ckpt() { read_ckpt(); }
};

typedef tUBLK_MME_DNS_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_MME_DNS_DATA,         *UBLK_MME_DNS_DATA;
typedef tUBLK_MME_DNS_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MME_DNS_DATA,            *UBLK_UBFLOAT_MME_DNS_DATA;

typedef tUBLK_MME_T_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_MME_T_DATA,         *UBLK_MME_T_DATA;
typedef tUBLK_MME_T_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MME_T_DATA,            *UBLK_UBFLOAT_MME_T_DATA;

typedef tUBLK_MME_CONDUCTION_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_MME_CONDUCTION_DATA,         *UBLK_MME_CONDUCTION_DATA;
typedef tUBLK_MME_CONDUCTION_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MME_CONDUCTION_DATA,            *UBLK_UBFLOAT_MME_CONDUCTION_DATA;

typedef tUBLK_MME_TURB_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_MME_TURB_DATA,        *UBLK_MME_TURB_DATA;
typedef tUBLK_MME_TURB_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MME_TURB_DATA,          *UBLK_UBFLOAT_MME_TURB_DATA;

typedef tUBLK_MME_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_MME_UDS_DATA,         *UBLK_MME_UDS_DATA;
typedef tUBLK_MME_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MME_UDS_DATA,            *UBLK_UBFLOAT_MME_UDS_DATA;

// XDU: NEARBLK_MME_DATA corresponds to UBLK_SURF_MME_DATA_TYPE. Maybe we should use the same name?
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_MME_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT ustar_lb;
  ALIGNED_UBFLOAT vel_frozen[3];
  ALIGNED_UBFLOAT density_frozen;

  uINT64 ckpt_len() {  return sizeof(*this); }
  VOID write_ckpt() {  write_ckpt_lgi(*this); }
  VOID read_ckpt() {
    read_lgi(*this);
  }
};

typedef tNEARBLK_MME_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_MME_DATA,         *NEAR_UBLK_MME_DATA;
typedef tNEARBLK_MME_DATA<UBLK_UBFLOAT_TYPE_TAG> sNEAR_UBLK_UBFLOAT_MME_DATA,            *NEAR_UBLK_UBFLOAT_MME_DATA;

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_FROZEN_DATA
{
  EXTRACT_UBLK_TRAITS
  using sNEARBLK_FROZEN_SEND_FIELD = tNEARBLK_FROZEN_SEND_FIELD<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT ustar_lb;
  ALIGNED_UBFLOAT density_frozen;
  ALIGNED_UBFLOAT vel_frozen[3];
  ALIGNED_UBFLOAT den_seed;

  uINT64 ckpt_len() { return sizeof(*this); }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }

  static VOID define_send_mpi_type(MPI_Datatype *eMPI_type);
  static VOID define_recv_mpi_type(MPI_Datatype *eMPI_type);

  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size) {
    send_size += (sizeof(sNEARBLK_FROZEN_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 solver_ts_index_mask) {
    sNEARBLK_FROZEN_SEND_FIELD* field = reinterpret_cast<sNEARBLK_FROZEN_SEND_FIELD*>(send_data_info.send_buffer);
    memcpy(&field->m_ustar_lb, &ustar_lb, sizeof(field->m_ustar_lb));
    memcpy(&field->m_density_frozen, &density_frozen, sizeof(field->m_density_frozen));
    memcpy(field->m_vel_frozen, vel_frozen, sizeof(field->m_vel_frozen));

    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 solver_ts_index_mask) {
    sNEARBLK_FROZEN_SEND_FIELD* field = reinterpret_cast<sNEARBLK_FROZEN_SEND_FIELD*>(recv_data_info.recv_buffer);
    memcpy(&ustar_lb, &field->m_ustar_lb, sizeof(field->m_ustar_lb));
    memcpy(&density_frozen, &field->m_density_frozen, sizeof(field->m_density_frozen));
    memcpy(vel_frozen, field->m_vel_frozen, sizeof(field->m_vel_frozen));

    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }


#undef ALIGNED_UBFLOAT  
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_FROZEN_DATA)
{
  COPY_UBLK_TO_MBLK(ustar_lb);
  COPY_UBLK_TO_MBLK(density_frozen);
  VEC_COPY_UBLK_TO_MBLK(vel_frozen,3);
  COPY_UBLK_TO_MBLK(den_seed);
}
#endif

//------------------------------------------------------------------------------
// sUBLK_PORE_LB_DATA for 5G large_pore
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_PORE_LB_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  using sUBLK_PORE_LB_SEND_FIELD = tUBLK_PORE_LB_SEND_FIELD<N_VOXELS>;

  ALIGNED_UBFLOAT porosity_input;  //for measurement output
  ALIGNED_UBFLOAT resistance[3];
  ALIGNED_UBFLOAT pm_residual;
  ALIGNED_UBFLOAT k0;        //absolute permeability
  ALIGNED_UBFLOAT sampled_k0;
  ALIGNED_UBFLOAT k_ij[6];
  ALIGNED_UBFLOAT contact_angle;  //in degree unit
  ALIGNED_UBFLOAT wall_potential; 
  ALIGNED_UBFLOAT full_states[N_STATES];
  sINT32          rock_type[N_VOXELS];
  ALIGNED_UBFLOAT pm_pc_residual_model_sw_limit[2];

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

  VOID init() {  //looks like no use
    memset(this, 0, sizeof(*this));
  }

  static VOID add_pore_send_size(asINT32 &send_size) {
     send_size      += (sizeof(sUBLK_PORE_LB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_pore_send_buffer(sdFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sUBLK_PORE_LB_SEND_FIELD*>(send_buffer);
    memcpy(field->m_porosity_input, porosity_input, sizeof(field->m_porosity_input));
    memcpy(field->m_wall_potential, wall_potential, sizeof(field->m_wall_potential));
    memcpy(field->m_full_states, full_states, sizeof(field->m_full_states));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_pore_recv_buffer(sdFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sUBLK_PORE_LB_SEND_FIELD*>(recv_buffer);
    memcpy(porosity_input, field->m_porosity_input, sizeof(field->m_porosity_input));
    memcpy(wall_potential, field->m_wall_potential, sizeof(field->m_wall_potential));
    memcpy(full_states, field->m_full_states, sizeof(field->m_full_states));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }


#undef ALIGNED_UBFLOAT
};


//------------------------------------------------------------------------------
// sUBLK_CBF_LB_DATA for conformal body force
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_CBF_LB_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
  ALIGNED_UBFLOAT grad_p[3];
  ALIGNED_UBFLOAT pressure_from_laplace;
#endif

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

#undef ALIGNED_UBFLOAT
};


typedef struct sSURFEL_LB_SEND_FIELD
{
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE  m_rho_bar_ratio;
#endif
#if BUILD_D39_LATTICE || BUILD_6X_SOLVER
  STP_PHYS_VARIABLE  m_u_bar_ratio[4];
#else
  STP_PHYS_VARIABLE  m_u_bar_ratio[2];
#endif
  STP_PHYS_VARIABLE  m_ustar_0;
  STP_PHYS_VARIABLE  m_ustar_0_pair;

} *SURFEL_LB_SEND_FIELD;

typedef struct sSURFEL_V2S_LB_SEND_FIELD
{

  SURFEL_STATE       m_out_flux[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE  m_cf_n;
#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
  STP_PHYS_VARIABLE  m_u_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE  m_density;
#else
  STP_PHYS_VARIABLE  m_u_bar[N_SURFEL_PGRAM_VOLUMES][3];
#endif
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE  m_rho_bar[N_SURFEL_PGRAM_VOLUMES];
#else
  STP_PHYS_VARIABLE  m_delp_factor;   //lb_energy
#endif

} *SURFEL_V2S_LB_SEND_FIELD;

//------------------------------------------------------------------------------
// sSURFEL_LB_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_V2S_LB_DATA
{
  const static size_t OUTFLUX_SIZE = sizeof(STP_PHYS_VARIABLE)*N_SURFEL_PGRAM_VOLUMES;
  EXTRACT_SURFEL_TRAITS
  // incoming stuff from either s2s surfels or voxels
  tVOXEL_GRADS<N>  m_grads;   // V2S
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_density; // incoming LRF S2S and V2S
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_momentum[3]; // incoming LRF S2S and V2S
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_in_state_scale_factors_lb[N_SURFEL_PGRAM_VOLUMES]; // incoming S2S and V2S //for 5G large_pore, it will store corrected pgram with considering porosity
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_in_states[N_SURFEL_PGRAM_VOLUMES]; // incoming S2S and V2S
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_visc;  // V2S

  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_cf_n; // V2S and S2V
#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_u_bar[N_SURFEL_PGRAM_VOLUMES]; // V2S and S2V
#else
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_u_bar[N_SURFEL_PGRAM_VOLUMES][3]; // V2S and S2V
#endif
#if BUILD_5G_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_rho_bar[N_SURFEL_PGRAM_VOLUMES];
#else
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_delp_factor;
#endif

//#if BUILD_D39_LATTICE
//  // TODO this is all temporary for compilation of the discretizer via
//  // simsizes
//  STP_PHYS_VARIABLE m_div_vel;
//  STP_PHYS_VARIABLE m_eta_art;
//  STP_PHYS_VARIABLE m_hyb_force[3];
//#endif
  // outflux should be last. look at reset_except_outflux implementation
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_out_flux[N_SURFEL_PGRAM_VOLUMES]; // outgoing E/O S2S and S2V

  
  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_V2S_LB_DATA");
    sim_print<3>(os, "pressure grad", m_grads.gradp);
    sim_print(os, "density", m_density);
    sim_print<3>(os, "momentum", m_momentum);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "in_state_scale_factors_lb", m_in_state_scale_factors_lb);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "in_states", m_in_states);
    sim_print_vs(os);
    sim_print(os, "visc", m_visc);
    sim_print(os, "cf_n", m_cf_n);
#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "m_u_bar", m_u_bar);
    sim_print_vs(os);
#else
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES,3>(os, "u_bar", m_u_bar);
#endif
#if BUILD_5G_LATTICE
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "m_rho_bar", m_rho_bar);
#else
    sim_print(os, "delp_factor", m_delp_factor);
#endif

//#if BUILD_D39_LATTICE
//    sim_print_vs(os);
//    sim_print(os, "div_vel", m_div_vel);
//    sim_print(os, "eta_art", m_eta_art);
//    sim_print<3>(os, "hyb_force", m_hyb_force);
//#endif
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "out_flux", m_out_flux);
  }

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID read_ckpt()  { read_lgi(*this);       }
  VOID write_ckpt() { write_ckpt_lgi(*this); }

  __HOST__DEVICE__
  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_V2S_LB_DATA));
  }
  
  VOID reset_except_outflux() {
    memset(this, 0, (sizeof(tSURFEL_V2S_LB_DATA) - tSURFEL_V2S_LB_DATA::OUTFLUX_SIZE));
  }

  __DEVICE__ VOID reset(int soxor,
                        BOOLEAN is_even_or_odd,
                        BOOLEAN is_odd,
                        BOOLEAN is_timestep_even) {
    
    if (!is_even_or_odd || is_timestep_even) {
      m_grads.reset(soxor);   // V2S
      m_density[soxor] = 0.0F;
      ccDOTIMES(i, 3) {
        m_momentum[i][soxor] = 0.0F;
      }

      ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
        m_in_state_scale_factors_lb[i][soxor] = 0.0F; // incoming S2S and V2S
        m_in_states[i][soxor] = 0.0F;
      }
  
      m_visc[soxor] = 0.0F;  // V2S
      m_cf_n[soxor] = 0.0F; // V2S and S2V

      ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
        m_u_bar[i][soxor] = 0.0F; // V2S and S2V
#else
        ccDOTIMES(j, 3) {
          m_u_bar[i][j][soxor] = 0.0F; // V2S and S2V
        }
#endif
#if BUILD_5G_LATTICE
        m_rho_bar[i][soxor] = 0.0F;
#endif
      }

#if !BUILD_5G_LATTICE
      m_delp_factor[soxor] = 0.0F;
#endif

      if (!is_odd) {
        ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
          m_out_flux[i][soxor] = 0.0F;
        }
      }
    }
  }

  VOID reflect_to_real_surfel(tSURFEL_V2S_LB_DATA *mirror_v2s_lb_data,
                              STP_LATVEC_MASK latvec_mask,
                              STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                              STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {
    m_cf_n += mirror_v2s_lb_data->m_cf_n;
    m_density += mirror_v2s_lb_data->m_density;
    m_visc += mirror_v2s_lb_data->m_visc;//mme_weight of real surfel contain part from mirror surfel, hongli's suggestion

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    m_delp_factor += mirror_v2s_lb_data->m_delp_factor;
#endif    

    ccDOTIMES(axis, N_AXES) {
      m_grads.gradp[axis] += mirror_v2s_lb_data->m_grads.gradp[axis] * velocity_mirror_sign_factor[axis];
      m_momentum[axis] += mirror_v2s_lb_data->m_momentum[axis] * velocity_mirror_sign_factor[axis];
    }

    ccDOTIMES(state_index, N_MOVING_STATES) {
      if (((latvec_mask >> state_index) & 1) == 0)
        continue;
      STP_STATE_INDEX source_state_index = state_index;
      STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
      STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

      m_in_states[source_latvec_pair_index] += mirror_v2s_lb_data->m_in_states[mirror_latvec_pair_index];

      m_in_state_scale_factors_lb[source_latvec_pair_index] +=
        mirror_v2s_lb_data->m_in_state_scale_factors_lb[mirror_latvec_pair_index];

#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
      m_u_bar[source_latvec_pair_index] += mirror_v2s_lb_data->m_u_bar[mirror_latvec_pair_index];
#else
      ccDOTIMES(axis1, N_AXES) {
        m_u_bar[source_latvec_pair_index][axis1] += mirror_v2s_lb_data->m_u_bar[mirror_latvec_pair_index][axis1]
                                                  * velocity_mirror_sign_factor[axis1];
      }
#endif
#if BUILD_5G_LATTICE
      m_rho_bar[source_latvec_pair_index] += mirror_v2s_lb_data->m_rho_bar[mirror_latvec_pair_index];
#endif
    }
  }
  VOID reflect_to_mirror_surfel(tSURFEL_V2S_LB_DATA *real_v2s_lb_data,
                                STP_LATVEC_MASK latvec_mask,
                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                                STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {
    m_cf_n = real_v2s_lb_data->m_cf_n;
#if BUILD_5G_LATTICE
    m_density = real_v2s_lb_data->m_density;
#else
    m_delp_factor = real_v2s_lb_data->m_delp_factor;  //lb_energy
#endif
    ccDOTIMES(state_index, N_MOVING_STATES) {
      if (((latvec_mask >> state_index) & 1) == 0)
        continue;
      STP_STATE_INDEX mirror_state_index        = state_index;

      STP_STATE_INDEX mirror_latvec_pair_index  = state_latvec_pair(mirror_state_index);
      STP_STATE_INDEX source_latvec_pair_index  = reflected_latvec_pair[mirror_latvec_pair_index];

      m_out_flux[mirror_latvec_pair_index] = real_v2s_lb_data->m_out_flux[source_latvec_pair_index];

#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
      m_u_bar[mirror_latvec_pair_index] = real_v2s_lb_data->m_u_bar[source_latvec_pair_index];
#else
      ccDOTIMES(axis1, N_AXES) {
        m_u_bar[mirror_latvec_pair_index][axis1] = real_v2s_lb_data->m_u_bar[source_latvec_pair_index][axis1]
                                                 * velocity_mirror_sign_factor[axis1];
      }
#endif
#if BUILD_5G_LATTICE
      m_rho_bar[mirror_latvec_pair_index] = real_v2s_lb_data->m_rho_bar[source_latvec_pair_index];
#endif
    }
  }

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_V2S_LB_DATA *even_v2s_lb_data,
                             asINT32 esoxor, asINT32 osoxor) {
    ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES){
      m_in_states[i][osoxor] = even_v2s_lb_data->m_in_states[i][esoxor];
    }
  }

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_LB_DATA *even_v2s_lb_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
                                        asINT32 osoxor, asINT32 esoxor)
  {
    m_momentum[0][osoxor]    = even_v2s_lb_data->m_momentum[0][esoxor];
    m_momentum[1][osoxor]    = even_v2s_lb_data->m_momentum[1][esoxor];
    m_momentum[2][osoxor]    = even_v2s_lb_data->m_momentum[2][esoxor];
    m_density[osoxor]        = even_density;
    m_cf_n[osoxor]           = even_v2s_lb_data->m_cf_n[esoxor] * mme_weight_inverse;
    m_grads.gradp[0][osoxor] = even_v2s_lb_data->m_grads.gradp[0][esoxor] * mme_weight_inverse;
    m_grads.gradp[1][osoxor] = even_v2s_lb_data->m_grads.gradp[1][esoxor] * mme_weight_inverse;
    m_grads.gradp[2][osoxor] = even_v2s_lb_data->m_grads.gradp[2][esoxor] * mme_weight_inverse;
    // visc is only used for LRF surfels. Perhaps we should have a separate type of surfel.
    m_visc[osoxor]           = even_v2s_lb_data->m_visc[esoxor];
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    m_delp_factor[osoxor]    = even_v2s_lb_data->m_delp_factor[esoxor] * mme_weight_inverse;
#endif
  }

  __HOST__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_LB_DATA *even_v2s_lb_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_V2S_LB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_V2S_LB_SEND_FIELD) / sizeof(sdFLOAT));
  }


  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_V2S_LB_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(field->m_out_flux, m_out_flux, sizeof(field->m_out_flux));
    field->m_cf_n = m_cf_n;
    memcpy(field->m_u_bar, m_u_bar, sizeof(field->m_u_bar));
#if BUILD_5G_LATTICE
    field->m_density = m_density;
    memcpy(field->m_rho_bar, m_rho_bar, sizeof(field->m_rho_bar));
#else
    field->m_delp_factor = m_delp_factor;
#endif
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_V2S_LB_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(m_out_flux, &field->m_out_flux, sizeof(field->m_out_flux));
    m_cf_n = field->m_cf_n;
    memcpy(m_u_bar, field->m_u_bar, sizeof(field->m_u_bar));
#if BUILD_5G_LATTICE
    m_density = field->m_density;
    memcpy(m_rho_bar, field->m_rho_bar, sizeof(field->m_rho_bar));
#else
     m_delp_factor = field->m_delp_factor;
#endif
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};

using sSURFEL_V2S_LB_DATA = tSURFEL_V2S_LB_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_LB_DATA = sSURFEL_V2S_LB_DATA*;

template<>
__HOST__ INLINE
VOID tSURFEL_V2S_LB_DATA<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(tSURFEL_V2S_LB_DATA<SFL_SDFLOAT_TYPE_TAG> *even_v2s_lb_data,
                                                              STP_PHYS_VARIABLE even_density,
                                                              STP_SURFEL_WEIGHT mme_weight_inverse,
                                                              STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) {
  this->pre_advect_init_copy_even_to_odd(even_v2s_lb_data, even_density, mme_weight_inverse, s2s_sampling_weight_inverse, 0, 0);
}

#if BUILD_GPU
INIT_MSFL(tSURFEL_V2S_LB_DATA);
#endif

typedef struct sSURFEL_FROZEN_SEND_FIELD
{
  STP_PHYS_VARIABLE  m_ustar_lb;
  STP_PHYS_VARIABLE  m_density_frozen;
  STP_PHYS_VARIABLE  m_vel_frozen[3];
} *SURFEL_FROZEN_SEND_FIELD;

template<size_t N> struct tLB_DATA_ATTRS;

template<>
struct tLB_DATA_ATTRS<N_SFLS_PER_MSFL> {

public:
  
  VOID set_home_voxel_requires_free_slip(size_t n) {
    m_home_voxel_requires_free_slip.set(n);
  }

  VOID set_is_mlrf_sufel_in_ring(size_t n) {
    m_is_mlrf_surfel_in_ring.set(n);
  }

  VOID set_is_coupling_meas_surfel(size_t n) {
    m_is_coupling_meas_surfel.set(n);
  }

  VOID set_is_interacting_io_surfel(size_t n) {
    m_is_interacting_io_surfel.set(n);
  }
  
public:
  __HOST__DEVICE__ BOOLEAN get_home_voxel_requires_free_slip(size_t n) const {
    return m_home_voxel_requires_free_slip.test(n);
  }

  __HOST__DEVICE__ BOOLEAN get_is_mlrf_sufel_in_ring(size_t n) const {
    return m_is_mlrf_surfel_in_ring.test(n);
  }

  __HOST__DEVICE__ BOOLEAN get_is_coupling_meas_surfel(size_t n) const {
    return m_is_coupling_meas_surfel.test(n);
  }

  __HOST__DEVICE__ BOOLEAN get_is_interacting_io_surfel(size_t n) const {
    return m_is_interacting_io_surfel.test(n);
  }
  
public:  
  MSFL_MASK m_home_voxel_requires_free_slip;
  MSFL_MASK m_is_mlrf_surfel_in_ring;
  MSFL_MASK m_is_coupling_meas_surfel;
  MSFL_MASK m_is_interacting_io_surfel;
  
};

template<>
struct tLB_DATA_ATTRS<1> {
public:
  
  VOID set_home_voxel_requires_free_slip(size_t n = 0) {
    m_home_voxel_requires_free_slip = 1;
  }

  VOID set_is_mlrf_surfel_in_ring(size_t n = 0) {
    m_is_mlrf_surfel_in_ring = 1;
  }

  VOID set_is_coupling_meas_surfel(size_t n = 0) {
    m_is_coupling_meas_surfel = 1;
  }

  VOID set_is_interacting_io_surfel(size_t n = 0) {
    m_is_interacting_io_surfel = 1;
  }

public:
  __HOST__DEVICE__ BOOLEAN get_home_voxel_requires_free_slip(size_t n = 0) const {
    return m_home_voxel_requires_free_slip;
  }

  __HOST__DEVICE__ BOOLEAN get_is_mlrf_surfel_in_ring(size_t n = 0) const {
    return m_is_mlrf_surfel_in_ring;
  }

  __HOST__DEVICE__ BOOLEAN get_is_coupling_meas_surfel(size_t n = 0) const {
    return m_is_coupling_meas_surfel;
  }

  __HOST__DEVICE__ BOOLEAN get_is_interacting_io_surfel(size_t n = 0) const {
    return m_is_interacting_io_surfel;
  }
public:
  uINT8 m_home_voxel_requires_free_slip : 1;
  // the following two fields are initialized to 0
  uINT8  m_is_mlrf_surfel_in_ring : 1;
  uINT8  m_is_coupling_meas_surfel : 1;
  uINT8  m_is_interacting_io_surfel : 1;
};
  
template<typename SFL_TYPE_TAG>
struct tSURFEL_LB_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N>  density_prime;

#if BUILD_D39_LATTICE || BUILD_6X_SOLVER
  tSFL_VAR<STP_PHYS_VARIABLE, N>  u_bar_ratio[4];  
#else
  tSFL_VAR<STP_PHYS_VARIABLE, N>  u_bar_ratio[2];  //5g surfel scatter will only use u_bar_ratio[0]
#endif

#if BUILD_5G_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N>  potential;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  rho_bar_ratio;
  STP_PHYS_VARIABLE  porosity;  //for 5G large_pore
  STP_PHYS_VARIABLE  porosity_input;  //for 5G large_pore
#endif
  tSFL_VAR<STP_PHYS_VARIABLE, N>  pgram_volumes[N_SURFEL_PGRAM_VOLUMES];

  tSFL_VAR<STP_PHYS_VARIABLE, N>   ustar_0;
  tSFL_VAR<STP_PHYS_VARIABLE, N>   ustar_0_pair;
  //SURFEL_STATE        out_flux[2][N_SURFEL_PGRAM_VOLUMES];

  tSFL_VAR<STP_GEOM_VARIABLE, N> inverse_tot_pgvm;
  tSFL_VAR<sINT8, N>  boundary_condition_type;
  //sINT8  ref_frame_index;
  sINT8  unused8;

  tLB_DATA_ATTRS<N> m_attribs;
  // Used to calculate dp/dt and write it to measurements.
  tSFL_VAR<STP_PHYS_VARIABLE, N> prev_pressure;
  
  // The following fields are cleared prior to V2S and S2S by the pre_advect_init
  // method below with a single memset. Hence they need to be grouped together.
  // Note that pre_advect_init below assumes a certain order to these fields.
//#if BUILD_D39_LATTICE
//  STP_PHYS_VARIABLE  hyb_force[3]; // hybrid force sampled from the voxels
//#endif // BUILD_D39_LATTICE
  /*#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE  rho_bar[N_SURFEL_PGRAM_VOLUMES];
  #endif*/  

  VOID print_surfel_data(std::ostream& os) {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_LB_DATA");
    sim_print(os, "density_prime", density_prime);
    sim_print<2>(os, "u_bar_ratio", u_bar_ratio);

#if BUILD_5G_LATTICE
    sim_print(os,"potential", potential);
    sim_print(os,"rho_bar_ratio", rho_bar_ratio);
#endif
    sim_print_vs(os);
    sim_print<N_NONZERO_ENERGIES>(os, "pgram_volumes", pgram_volumes);
    sim_print_vs(os);

//#if BUILD_D39_LATTICE
//    sim_print<3>(os,"hyb_force",hyb_force);
//#endif

#if 0
    sim_print<N_NONZERO_ENERGIES>(os, "s2s_delp_mass_flux", s2s_delp_mass_flux);
#endif
  }

  VOID pre_advect_init() {
    msg_internal_error("Never called");
  }  

  VOID pre_advect_init_copy_even_to_odd(tSURFEL_LB_DATA *even_surfel_lb_data, 
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_LB_DATA *even_surfel_lb_data,
                             asINT32 esoxor, asINT32 osoxor) { }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_LB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_LB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_LB_SEND_FIELD field = reinterpret_cast<SURFEL_LB_SEND_FIELD>(send_data_info.send_buffer);
#if BUILD_5G_LATTICE
    memcpy(&field->m_rho_bar_ratio, &rho_bar_ratio, sizeof(field->m_rho_bar_ratio));
#endif
    memcpy(field->m_u_bar_ratio, u_bar_ratio, sizeof(field->m_u_bar_ratio));
    field->m_ustar_0      = ustar_0;
    field->m_ustar_0_pair = ustar_0_pair;
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_LB_SEND_FIELD field = reinterpret_cast<SURFEL_LB_SEND_FIELD>(recv_data_info.recv_buffer);
#if BUILD_5G_LATTICE
    memcpy(&rho_bar_ratio, &field->m_rho_bar_ratio, sizeof(field->m_rho_bar_ratio));
#endif
    memcpy(u_bar_ratio, field->m_u_bar_ratio, sizeof(field->m_u_bar_ratio));
    ustar_0      = field->m_ustar_0;
    ustar_0_pair = field->m_ustar_0_pair;
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
  
  //sINT8 padding_5 : 5;


  //uINT16 m_face_index;

  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
    if ((surfel_desc->s.home_voxel_type == LGI_FAN_VOXEL_TYPE) ||
	(surfel_desc->s.home_voxel_type == LGI_POROUS_MEDIA_VOXEL_TYPE)) {
      m_attribs.set_home_voxel_requires_free_slip();
    }
  }

  // Write everything except pgram_volumes, and hyb_force if equilibrium
  // timestep
  #define ckpt_size_before_pgram_volumes struct_field_disp(tSURFEL_LB_DATA*, pgram_volumes)
  #define ckpt_size_after_pgram_volumes  (sizeof(tSURFEL_LB_DATA)                               \
                                          - struct_field_disp(tSURFEL_LB_DATA*, pgram_volumes)  \
                                          - sizeof(pgram_volumes))

  GNU_FUNC_ATTR(no_sanitize("undefined"))
  VOID write_ckpt() 
  { 
    write_ckpt_lgi(this, ckpt_size_before_pgram_volumes);
    write_ckpt_lgi(((char *)pgram_volumes + sizeof(pgram_volumes)),
                   ckpt_size_after_pgram_volumes);
  }

  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff) 
  { 
    pio_ckpt_buff.write(this, ckpt_size_before_pgram_volumes);
    pio_ckpt_buff.write(((char *)pgram_volumes + sizeof(pgram_volumes)),
                   ckpt_size_after_pgram_volumes);
  }

  GNU_FUNC_ATTR(no_sanitize("undefined"))
  uINT64 ckpt_len() {
    uINT64 len = ckpt_size_before_pgram_volumes;
    len += ckpt_size_after_pgram_volumes;
    return len;
  }

  GNU_FUNC_ATTR(no_sanitize("undefined"))
  VOID read_ckpt() 
  { 
    read_lgi(this, ckpt_size_before_pgram_volumes);
    read_lgi(((char *)pgram_volumes + sizeof(pgram_volumes)),
             ckpt_size_after_pgram_volumes);
  }


  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_LB_DATA *mirror_lb_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 //STP_STATE_INDEX reflected_states[N_STATES],
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_LB_DATA *source_lb_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 //STP_STATE_INDEX reflected_states[N_STATES],
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  #undef ckpt_size_before_pgram_volumes
  #undef ckpt_size_after_pgram_volumes

};

using sSURFEL_LB_DATA = tSURFEL_LB_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_LB_DATA = sSURFEL_LB_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_LB_DATA);
#endif

template<typename SFL_TYPE_TAG>
struct tSURFEL_S2S_LB_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<SURFEL_STATE, N>       m_out_flux[N_S2S_TIME_INDICES][N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<STP_PHYS_VARIABLE, N>   ustar_0[N_S2S_TIME_INDICES];
  using OUTFLUX_PTR = typename std::conditional<N==1, SURFEL_STATE*, tSFL_VAR<SURFEL_STATE, N>*>::type;

  uINT64 ckpt_len() { return sizeof(*this); }
  VOID read_ckpt()  { read_lgi(*this);      }
  VOID write_ckpt() { write_ckpt_lgi(*this);}    
};

using sSURFEL_S2S_LB_DATA = tSURFEL_S2S_LB_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_S2S_LB_DATA = sSURFEL_S2S_LB_DATA*;

#if BUILD_GPU

INIT_MSFL(tSURFEL_S2S_LB_DATA);

#endif

static const asINT32 SURFEL_LB_DATA_CKPT_SIZE = (sizeof(sSURFEL_LB_DATA) - struct_field_size(SURFEL_LB_DATA, pgram_volumes));

template<typename SFL_TYPE_TAG>
struct tSURFEL_FROZEN_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N>   ustar_lb;
  tSFL_VAR<STP_PHYS_VARIABLE, N>   density_frozen;
  tSFL_VAR<STP_PHYS_VARIABLE, N>   vel_frozen[3];

  static VOID define_send_mpi_type(MPI_Datatype *eMPI_type);
  static VOID define_recv_mpi_type(MPI_Datatype *eMPI_type);
  
  
  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_FROZEN_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_FROZEN_SEND_FIELD) / sizeof(sdFLOAT));
  }


  //VOID fill_send_buffer(sSURFEL_FROZEN_SEND_FIELD *field, asINT32 timestep_index) 
  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_FROZEN_SEND_FIELD field = reinterpret_cast<SURFEL_FROZEN_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(&field->m_ustar_lb, &ustar_lb, sizeof(field->m_ustar_lb));
    memcpy(&field->m_density_frozen, &density_frozen, sizeof(field->m_density_frozen));
    memcpy(field->m_vel_frozen, vel_frozen, sizeof(field->m_vel_frozen));;
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_FROZEN_SEND_FIELD field = reinterpret_cast<SURFEL_FROZEN_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(&ustar_lb, &field->m_ustar_lb, sizeof(field->m_ustar_lb));
    memcpy(&density_frozen, &field->m_density_frozen, sizeof(field->m_density_frozen));
    memcpy(vel_frozen, field->m_vel_frozen, sizeof(field->m_vel_frozen));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
  }

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_FROZEN_DATA *even_surfel_frozen_data,
                             asINT32 esoxor, asINT32 osoxor) {
    ustar_lb[osoxor] = even_surfel_frozen_data->ustar_lb[esoxor];
    density_frozen[osoxor] = even_surfel_frozen_data->density_frozen[esoxor];
    vel_frozen[0][osoxor] = even_surfel_frozen_data->vel_frozen[0][esoxor];
    vel_frozen[1][osoxor] = even_surfel_frozen_data->vel_frozen[1][esoxor];
    vel_frozen[2][osoxor] = even_surfel_frozen_data->vel_frozen[2][esoxor];    
  }

  uINT64 ckpt_len() 
  { 
    return sizeof(*this); 
  }

  VOID write_ckpt() 
  { 
    write_ckpt_lgi(*this);
  }

  VOID read_ckpt() 
  { 
    read_lgi(*this);
  }

  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_FROZEN_DATA *source_frozen_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
};

using sSURFEL_FROZEN_DATA = tSURFEL_FROZEN_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_FROZEN_DATA = sSURFEL_FROZEN_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_FROZEN_DATA);
#endif


static const asINT32 SURFEL_FROZEN_DATA_CKPT_SIZE = sizeof(sSURFEL_FROZEN_DATA);


//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_LB_DATA
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_LB_DATA
{
  STP_PHYS_VARIABLE  density;
  STP_PHYS_VARIABLE  vel[3];
  STP_PHYS_VARIABLE  mass_flux;
  STP_PHYS_VARIABLE  prev_pressure{};
  VOID pre_advect_init() {
    density = 0.0f;
    std::fill_n(vel, 3, 0.0f);
    mass_flux = 0.0f;
//    memset(this, 0, sizeof(*this));
  }
  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_LB_DATA *even_surfel_lb_data);
  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

} *SAMPLING_SURFEL_LB_DATA;

// beta_factor_i(x) = ( 1 - pas_i(x) ) / p_into_surf_i*(x)
//
// This reduces to 1 / pfluid(x) unless we force pas_i(x) to zero, which happens if either:
// a) according to its definition, pas_i(x) is negative, or
// b) x is not connected in the i* direction.
template<size_t N_VOXELS>
struct tV2S_BETA_FACTORS {
  VOXEL_STATE beta[N_VOXELS][N_LATTICE_VECTORS];
  tV2S_BETA_FACTORS() { memset(beta, 0, sizeof(beta)); }
  static const size_t ALIGNMENT = 32;
};

template<size_t N_VOXELS>
struct tV2S_PBL_FACTORS {  
  STP_GEOM_VARIABLE alpha[N_VOXELS][N_FACES];
  static const size_t ALIGNMENT = 32;
};


// SCALE FACTORS are used to scale up all PDE S2V contributions to VR Coarse voxels
// which are not connected in any of the neighbor directions. Weights during ES2V are for
// VR fine voxels and the contributions to VR coarse voxel for LB data is taken care during OS2V
// But for PDE data the weigts need to be scaled to get the correct contributions.

template<size_t N_VOXELS>
struct tVR_PDE_S2V_SCALE_FACTORS {  
  STP_GEOM_VARIABLE alpha[N_VOXELS][N_FACES];
  static const size_t ALIGNMENT = 32;
};

template<typename SFL_TYPE_TAG>
class tSURFEL;
  
using sSURFEL = tSURFEL<SFL_SDFLOAT_TYPE_TAG>;

//------------------------------------------------------------------------------
// tTAGGED_SURFEL
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
class tTAGGED_SURFEL {
  
public:

  tTAGGED_SURFEL(tSURFEL<SFL_TYPE_TAG>* surfel): m_surfel(surfel) {};

  VOID clear() { m_surfel = nullptr; }

  VOID* get_ptr() { return m_surfel; }

  BOOLEAN is_valid() {
    //Sometimes tagged surfels are constructed with invalid IDs, and they
    //are not swapped with a nullptr
    return m_surfel && ((uintptr_t) m_surfel != STP_INVALID_SURFEL_ID);
  }

  VOID set_surfel(tSURFEL<SFL_TYPE_TAG>* surfel) {
    m_surfel = (tSURFEL<SFL_TYPE_TAG>*) (scalar_or_pointer(m_surfel, (uintptr_t) surfel));
  }
      
  VOID set_offset(uintptr_t offset) {
    assert(offset < tSURFEL<SFL_TYPE_TAG>::ALIGNMENT);
    m_surfel = (tSURFEL<SFL_TYPE_TAG>*) (scalar_or_pointer(m_surfel, offset));
  }
  
  __HOST__DEVICE__ tSURFEL<SFL_TYPE_TAG>* get_surfel() {
    return (tSURFEL<SFL_TYPE_TAG>*) scalar_mask_pointer(m_surfel, ~offset_mask());
  }

  __HOST__DEVICE__ uintptr_t get_offset() {
    return (uintptr_t) scalar_mask_pointer(m_surfel, offset_mask());
  }

private:
  _INLINE_ __HOST__DEVICE__ constexpr static uintptr_t offset_mask() { return tSURFEL<SFL_TYPE_TAG>::ALIGNMENT - 1; }
  tSURFEL<SFL_TYPE_TAG>* m_surfel;
};
  
template<typename UBLK_TYPE_TAG>
struct tCLOSEST_SURFEL_TYPE {
  using type = typename std::conditional<UBLK_TYPE_TAG::N_VOXELS == N_VOXELS_8,
                                         tTAGGED_SURFEL<SFL_SDFLOAT_TYPE_TAG>,
                                         tTAGGED_SURFEL<MSFL_SDFLOAT_TYPE_TAG>>::type;
};

constexpr static size_t N_SUPPORTED_VR_DIFF_LEVELS = 3;

template<size_t N_VOXELS>
struct tS2V_CLEAR_DATA;

template<>
struct tS2V_CLEAR_DATA<N_VOXELS_8>{};
  
template<>
struct tS2V_CLEAR_DATA<N_VOXELS_64> {

  tBITSET<8> m_surfel_scale_diff_masks[N_SUPPORTED_VR_DIFF_LEVELS];
  __ALIGN__(8) uint8_t m_are_states_cleared_for_S2V[8];
  
  __HOST__ VOID set_clear_bit_for_scale_diff(uint8_t child_ublk, int scale_diff) {
    cassert(scale_diff < N_SUPPORTED_VR_DIFF_LEVELS);
    m_surfel_scale_diff_masks[scale_diff].set(child_ublk);
  }

  __HOST__DEVICE__ tBITSET<8> get_clear_masks_for_scale_diff(int scale_diff) const {
    cassert(scale_diff < N_SUPPORTED_VR_DIFF_LEVELS);
    return m_surfel_scale_diff_masks[scale_diff];
  }

  __HOST__DEVICE__ BOOLEAN are_states_clear(int child_ublk) const {
    return m_are_states_cleared_for_S2V[child_ublk];
  }

  __HOST__DEVICE__ VOID set_states_clear(int child_ublk) {
    m_are_states_cleared_for_S2V[child_ublk] = 1;
  }

  __HOST__DEVICE__ VOID unset_states_clear()  {
    memset(&m_are_states_cleared_for_S2V[0], 0, sizeof(uint8_t) * 8);
  }

  __HOST__DEVICE__ BOOLEAN are_all_child_ublk_states_clear() {
    constexpr uINT64 all_states_cleared_mask = 0x0101010101010101;
    return (*(reinterpret_cast<uINT64*>(&m_are_states_cleared_for_S2V[0]))) == all_states_cleared_mask;
  }
};

//------------------------------------------------------------------------------
// sNEARBLK_GEOM_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_GEOM_DATA
{

  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR UBFLOAT_DATA_TYPE

  using sV2S_BETA_FACTORS = tV2S_BETA_FACTORS<N_VOXELS>;
  using sV2S_PBL_FACTORS = tV2S_PBL_FACTORS<N_VOXELS>;
  using sVR_PDE_S2V_SCALE_FACTORS = tVR_PDE_S2V_SCALE_FACTORS<N_VOXELS>;
  using SCALE_FACTOR_MASK = typename std::conditional<N_UBLKS == 1, uINT8, tBITSET<N_UBLKS>>::type;

  ALIGNED_UBFLOAT percent_boundary_layer;
  ALIGNED_UBFLOAT percent_boundary_layer_overlap;

  // speed 2 overlap fraction
  ALIGNED_UBFLOAT  pbl_2[N_CARTESIAN_SPEED1_LATVECS];

  VOXEL_MASK       only_lrf_surfels_mask[N_CARTESIAN_SPEED1_LATVECS];
  SCALE_FACTOR_MASK beta_factors_present; // for checkpointing
  SCALE_FACTOR_MASK pbl_factors_2_present; // for checkpointing
  SCALE_FACTOR_MASK pde_scale_factors_present; // for checkpointing
  
  sV2S_BETA_FACTORS *beta_factors;
  sV2S_PBL_FACTORS  *pbl_factors_2;
  sVR_PDE_S2V_SCALE_FACTORS *pde_scale_factors;

  // data below is not checkpointed
  union {
    typename tCLOSEST_SURFEL_TYPE<UBLK_TYPE_TAG>::type closest_surfels[N_VOXELS];
    POINTER_SIZE_INT closest_surfel_ids[N_VOXELS];
  };
  STP_GEOM_VARIABLE dist_to_surface[N_VOXELS];
  ALIGNED_UBFLOAT pfluids;
  ALIGNED_UBFLOAT inverse_pfluids;
  ALIGNED_UBFLOAT pfluids_s2v;

#if BUILD_D39_LATTICE
  ALIGNED_UBFLOAT  normal[3]; // sampled first layer surface normals
#endif
  tS2V_CLEAR_DATA<UBLK_TYPE_TAG::N_VOXELS> s2v_clear_data;
  
  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_SURF_GEOM_DATA");
    sim_print<N_VOXELS>(os, "percent_boundary_layer", percent_boundary_layer, print_voxel);
    sim_print<N_VOXELS>(os, "percent_boundary_layer_overlap", percent_boundary_layer_overlap, print_voxel);
    sim_print<N_VOXELS>(os, "dist_to_surface", dist_to_surface, print_voxel);
    sim_print<N_VOXELS>(os, "pfluids", pfluids, print_voxel);
    sim_print<N_VOXELS>(os, "inverse_pfluids", inverse_pfluids, print_voxel);
#if BUILD_D39_LATTICE
    sim_print<ubFLOAT::N_VOXELS>(os, "normal", normal, loop_limits(0,2), print_voxel);
#endif
    sim_print_vs(os);
  }

  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  tNEARBLK_GEOM_DATA() { init(); }

  VOID read_ckpt() {
    read_lgi(percent_boundary_layer);
    read_lgi(percent_boundary_layer_overlap);
    read_lgi(pbl_2);
    read_lgi(only_lrf_surfels_mask);
    read_lgi(beta_factors_present);
    read_lgi(pbl_factors_2_present);
    read_lgi(pde_scale_factors_present);
    if (beta_factors_present) {
      beta_factors = xnew sV2S_BETA_FACTORS;
      read_lgi(beta_factors, sizeof(sV2S_BETA_FACTORS));
    }
    if (pbl_factors_2_present) {
      pbl_factors_2 = xnew sV2S_PBL_FACTORS;
      read_lgi(pbl_factors_2, sizeof(sV2S_PBL_FACTORS));
    }
    if (pde_scale_factors_present) {
      pde_scale_factors = xnew sVR_PDE_S2V_SCALE_FACTORS;
      read_lgi(pde_scale_factors, sizeof(sVR_PDE_S2V_SCALE_FACTORS));
    }

#if BUILD_D39_LATTICE
    read_lgi(normal);
#endif
  }
  VOID read_ckpt(u_char* buff, size_t& readSz) {
      auto readFromBuff = [&buff, &readSz](auto& obj)
      {
        //reads obj from buffer and advances offset
        size_t size = sizeof(decltype(obj));
        std::memcpy(&obj, buff+readSz, size);
        readSz+=size;
      };
      auto readFromBuffSize = [&buff, &readSz](auto& obj, size_t size)
      {
        //reads obj from buffer and advances offset
        std::memcpy(&obj, buff+readSz, size);
        readSz +=size;
      };
    readFromBuff(percent_boundary_layer);
    readFromBuff(percent_boundary_layer_overlap);
    readFromBuff(pbl_2);
    readFromBuff(only_lrf_surfels_mask);
    readFromBuff(beta_factors_present);
    readFromBuff(pbl_factors_2_present);
    readFromBuff(pde_scale_factors_present);
    if (beta_factors_present) {
      beta_factors = xnew sV2S_BETA_FACTORS;
      readFromBuffSize(beta_factors, sizeof(sV2S_BETA_FACTORS));
    }
    if (pbl_factors_2_present) {
      pbl_factors_2 = xnew sV2S_PBL_FACTORS;
      readFromBuffSize(pbl_factors_2, sizeof(sV2S_PBL_FACTORS));
    }
    if (pde_scale_factors_present) {
      pde_scale_factors = xnew sVR_PDE_S2V_SCALE_FACTORS;
      readFromBuffSize(pde_scale_factors, sizeof(sVR_PDE_S2V_SCALE_FACTORS));
    }

#if BUILD_D39_LATTICE
    readFromBuff(normal);
#endif
  }
  VOID write_ckpt() {
    write_ckpt_lgi(percent_boundary_layer);
    write_ckpt_lgi(percent_boundary_layer_overlap);
    write_ckpt_lgi(pbl_2);
    /*if (beta_factors)
      beta_factors_present = 1;
    if (pbl_factors_2)
      pbl_factors_2_present = 1;*/
    write_ckpt_lgi(only_lrf_surfels_mask);
    write_ckpt_lgi(beta_factors_present);
    write_ckpt_lgi(pbl_factors_2_present);
    write_ckpt_lgi(pde_scale_factors_present);

    if (beta_factors_present)
      write_ckpt_lgi(beta_factors, sizeof(sV2S_BETA_FACTORS));
    if (pbl_factors_2_present)
      write_ckpt_lgi(pbl_factors_2, sizeof(sV2S_PBL_FACTORS));
    if (pde_scale_factors_present)
      write_ckpt_lgi(pde_scale_factors, sizeof(sVR_PDE_S2V_SCALE_FACTORS));

#if BUILD_D39_LATTICE
    write_ckpt_lgi(normal);
#endif
  }
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff) {
    
    pio_ckpt_buff.write(&percent_boundary_layer);
    pio_ckpt_buff.write(&percent_boundary_layer_overlap);
    pio_ckpt_buff.write(&pbl_2);
    /*if (beta_factors)
      beta_factors_present = 1;
    if (pbl_factors_2)
      pbl_factors_2_present = 1;*/
    pio_ckpt_buff.write(&only_lrf_surfels_mask);
    pio_ckpt_buff.write(&beta_factors_present);
    pio_ckpt_buff.write(&pbl_factors_2_present);
    pio_ckpt_buff.write(&pde_scale_factors_present);

    if (beta_factors_present)
      pio_ckpt_buff.write(beta_factors, sizeof(sV2S_BETA_FACTORS));
    if (pbl_factors_2_present)
      pio_ckpt_buff.write(pbl_factors_2, sizeof(sV2S_PBL_FACTORS));
    if (pde_scale_factors_present)
      pio_ckpt_buff.write(pde_scale_factors, sizeof(sVR_PDE_S2V_SCALE_FACTORS));

#if BUILD_D39_LATTICE
    pio_ckpt_buff.write(&normal);
#endif
  }
  uINT64 ckpt_len() {
    uINT64 len = 0;
    len += sizeof(percent_boundary_layer);
    len += sizeof(percent_boundary_layer_overlap);
    len += sizeof(pbl_2);
    len += sizeof(only_lrf_surfels_mask);
    len += sizeof(beta_factors_present);
    len += sizeof(pbl_factors_2_present);
    len += sizeof(pde_scale_factors_present);
    if (beta_factors_present)
      len += sizeof(sV2S_BETA_FACTORS);
    if (pbl_factors_2_present)
      len += sizeof(sV2S_PBL_FACTORS);
    if (pde_scale_factors_present)
      len += sizeof(sVR_PDE_S2V_SCALE_FACTORS);
#if BUILD_D39_LATTICE
    len += sizeof(normal);
#endif
    return len;
  }

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU

VOID copy_beta_pbl_pde_factors(sNEAR_UBLK_GEOM_DATA& ublk_data,
			       tNEARBLK_GEOM_DATA<HMBLK_SDFLOAT_TYPE_TAG>& mblk_data,
			       int child_ublk);

INIT_MBLK(NEARBLK_GEOM_DATA) 
{

  COPY_UBLK_TO_MBLK(percent_boundary_layer);
  COPY_UBLK_TO_MBLK(percent_boundary_layer_overlap);
  VEC_COPY_UBLK_TO_MBLK(pbl_2,N_CARTESIAN_SPEED1_LATVECS);
  VEC_COPY_UBLK_TO_MBLK(only_lrf_surfels_mask, N_CARTESIAN_SPEED1_LATVECS);

  // @fcn copy_beta_pbl_pde_factors
  // uINT8            beta_factors_present; // for checkpointing
  // uINT8            pbl_factors_2_present; // for checkpointing
  // uINT8            pde_scale_factors_present; // for checkpointing
  // sV2S_BETA_FACTORS *beta_factors;
  // sV2S_PBL_FACTORS  *pbl_factors_2;
  // sVR_PDE_S2V_SCALE_FACTORS *pde_scale_factors;
  copy_beta_pbl_pde_factors(ublk, mega, child_ublk); 

  //Temporarily stash child surfel pointer in MSFL pointer slot
  //We will resolve it later
  for (int v = 0, mv = child_ublk * N_VOXELS_8;
       v < N_VOXELS_8; v++, mv++) {    
    if (ublk.closest_surfels[v].is_valid()) {
      sSURFEL* s = ublk.closest_surfels[v].get_surfel();
      mega.closest_surfels[mv].set_surfel((sMSFL*) s);
    }
  }
  
  COPY_UBLK_TO_MBLK(dist_to_surface);
  COPY_UBLK_TO_MBLK(pfluids);
  COPY_UBLK_TO_MBLK(inverse_pfluids);
  COPY_UBLK_TO_MBLK(pfluids_s2v);

#if BUILD_D39_LATTICE
  VEC_COPY_UBLK_TO_MBLK(normal,3);
#endif
}
#endif


} //inline SIMULATOR_NAMESPACE
#endif //_SIMENG_LB_SOLVER_DATA_H
