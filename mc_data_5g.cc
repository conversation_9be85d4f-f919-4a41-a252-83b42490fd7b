/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#include "mc_data_5g.h"
#include "ublk.h"
#include "sim.h"
#include "surfel.h"
#include "sampling_surfel.h"

//------------------------------------------------------------------------------
// define_mpi_type
//------------------------------------------------------------------------------
template<>
VOID sSURFEL_MC_DATA::pre_advect_init_copy_even_to_odd(SURFEL_MC_DATA even_surfel_mc_data, 
                                                       STP_PHYS_VARIABLE even_density,
                                                       STP_SURFEL_WEIGHT mme_weight_inverse, 
                                                       STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) 
{
  if (g_use_lrf_s2s_sampling_mme) {
    density        = even_density * s2s_sampling_weight_inverse;
  } else {
    density        = even_density * mme_weight_inverse;
  }

  momentum[0]    = even_surfel_mc_data->momentum[0];
  momentum[1]    = even_surfel_mc_data->momentum[1];
  momentum[2]    = even_surfel_mc_data->momentum[2];

  potential      = even_surfel_mc_data->potential;
}

template<>
VOID sSURFEL_MC_DATA::define_send_mpi_type(MPI_Datatype *eMPI_type)
{
  begin_mpi_type() {
    mpi_t(sSURFEL_MC_SEND_FIELD, SURFEL_STATE     , m_out_flux);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_density_pair);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_momentum_pair);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_rho_bar);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_rho_bar_ratio);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_u_bar_ratio);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_density);
    mpi_t(sSURFEL_MC_SEND_FIELD, STP_PHYS_VARIABLE, m_potential);
  }
  end_mpi_type(sSURFEL_MC_SEND_FIELD, eMPI_type);
}

template<>
VOID sSURFEL_MC_DATA::define_recv_mpi_type(MPI_Datatype *eMPI_type)
{
  begin_mpi_type() {
    mpi_t(sSURFEL_MC_DATA, SURFEL_STATE     , out_flux);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, density_pair);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, momentum_pair);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, rho_bar);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, rho_bar_ratio);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, u_bar_ratio);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, density);
    mpi_t(sSURFEL_MC_DATA, STP_PHYS_VARIABLE, potential);
  } 
  end_mpi_type(sSURFEL_MC_DATA, eMPI_type);
}

template<>
VOID sSURFEL_MC_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_MC_DATA mirror_mc_data, 
                                                                STP_LATVEC_MASK latvec_state_mask,
								STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES], //new
                                                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  density += mirror_mc_data->density;

  /*
  //in swd seeding, surfel sampling is determined by its home voxel, no sampling any more
  if (sim.smart_seed_contact_angle)
    potential += mirror_mc_data->potential;
  */

  ccDOTIMES(axis, N_AXES) {
    momentum[axis] += mirror_mc_data->momentum[axis] * velocity_mirror_sign_factor[axis];
  }

  ccDOTIMES(state_index, N_MOVING_STATES) {
    if (((latvec_state_mask >> state_index) & 1) == 0)
      continue; 
    STP_STATE_INDEX source_state_index = state_index;
    STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
    STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

    in_states[source_latvec_pair_index] += mirror_mc_data->in_states[mirror_latvec_pair_index];

    rho_bar[source_latvec_pair_index] += mirror_mc_data->rho_bar[mirror_latvec_pair_index];

  }
}

template<>
VOID sSURFEL_MC_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_MC_DATA source_mc_data, 
                                                                STP_LATVEC_MASK latvec_state_mask,
								STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES], //new
                                                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  rho_bar_ratio = source_mc_data->rho_bar_ratio;
  u_bar_ratio = source_mc_data->u_bar_ratio;

  //potential = source_mc_data->potential;
  density = source_mc_data->density;
  density_pair = source_mc_data->density_pair;
  ccDOTIMES(axis, N_AXES) {
    momentum_pair[axis] = source_mc_data->momentum_pair[axis] * velocity_mirror_sign_factor[axis];
  } 
  ccDOTIMES(state_index, N_MOVING_STATES) {
    if (((latvec_state_mask >> state_index) & 1) == 0)
      continue; 
    STP_STATE_INDEX mirror_state_index        = state_index;
    STP_STATE_INDEX mirror_latvec_pair_index  = state_latvec_pair(mirror_state_index);
    STP_STATE_INDEX source_latvec_pair_index  = reflected_latvec_pair[mirror_latvec_pair_index];

    out_flux[mirror_latvec_pair_index]          = source_mc_data->out_flux[source_latvec_pair_index];
    rho_bar[mirror_latvec_pair_index] = source_mc_data->rho_bar[source_latvec_pair_index];
  }
}

VOID sSAMPLING_SURFEL_MC_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_MC_DATA even_surfel_mc_data)
{
  memcpy(this, even_surfel_mc_data, sizeof(*this));
}

/* undone for surfel data */

#if BUILD_GPU
inline namespace SIMULATOR_NAMESPACE {
INIT_MSFL(tSURFEL_MC_DATA) {
  COPY_SFL_TO_MSFL(potential);
  COPY_SFL_TO_MSFL(density_pair);
  VEC_COPY_SFL_TO_MSFL(momentum_pair, 3);
  COPY_SFL_TO_MSFL(rho_bar_ratio);
  COPY_SFL_TO_MSFL(u_bar_ratio);  
  VEC_COPY_SFL_TO_MSFL(out_flux, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(in_states, N_SURFEL_PGRAM_VOLUMES);
  COPY_SFL_TO_MSFL(density);
  VEC_COPY_SFL_TO_MSFL(momentum, 3);
  VEC_COPY_SFL_TO_MSFL(rho_bar, N_SURFEL_PGRAM_VOLUMES);
}
}
#endif
