#ifndef PARTICLE_SOLVER_DATA_H_
#define PARTICLE_SOLVER_DATA_H_

#include "common_sp.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "sim.h"
#include "parcel_list.h"
#include "comm_compression.h"
#include "gpu_host_init.h"
#include <vector>
#include "bitset.h"
#include "mme_ckpt.h"

#ifndef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
#include "surfel_stencils.h"
#endif

#define FIRST_TIMESTEP_OF_INTEREST  (0)
#define PARCEL_ID_OF_INTEREST (17865306)
#define PARCEL_ORIGINATING_SP_OF_INTEREST (16)
#define FILM_SOLVER_SURFEL_OF_INTEREST (1438)

#define PARCEL_IS_INTERESTING(parcel) (FALSE)
//#define PARCEL_IS_INTERESTING(parcel) (TRUE)
//#define PARCEL_IS_INTERESTING(parcel) (((parcel)->id == (PARCEL_ID_OF_INTEREST)) && ((parcel)->originating_sp == (PARCEL_ORIGINATING_SP_OF_INTEREST)) && (g_timescale.time_flow() >= (FIRST_TIMESTEP_OF_INTEREST)))//
#define sPARCEL_IS_INTERESTING(parcel) (PARCEL_IS_INTERESTING(&(parcel)))

//#define FILM_SURFEL_IS_INTERESTING(surfel) (surfel->id() == FILM_SOLVER_SURFEL_OF_INTEREST)
#define FILM_SURFEL_IS_INTERESTING(surfel) (FALSE)


#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
typedef std::vector<sSURFEL*> sFILM_STENCIL_SURFEL_VECTOR;
typedef sFILM_STENCIL_SURFEL_VECTOR* FILM_STENCIL_SURFEL_VECTOR;
typedef std::vector<sSURFEL*>::iterator sFILM_STENCIL_SURFEL_VECTOR_ITERATOR;
#else
typedef std::vector<sSTENCIL_SURFEL> sFILM_STENCIL_SURFEL_VECTOR;
typedef sFILM_STENCIL_SURFEL_VECTOR* FILM_STENCIL_SURFEL_VECTOR;
typedef std::vector<sSTENCIL_SURFEL>::iterator sFILM_STENCIL_SURFEL_VECTOR_ITERATOR;
#endif

//Vector of cummulative weights for surfels in a surfel's stencil:
typedef std::vector<sPARTICLE_VAR> sFILM_STENCIL_WEIGHTS_VECTOR;
typedef sFILM_STENCIL_WEIGHTS_VECTOR* FILM_STENCIL_WEIGHTS_VECTOR;

extern sSIM_INFO sim;
typedef sPARTICLE_VAR* PARTICLE_VAR;
typedef enum {IN_FLUID,
              ON_SURFACE,
              REENTRAINED_INTO_GHOST_UBLK,
              JUST_EMITTED_MAYBE_IN_FLUID,
              JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT,
              COLLIDED_WITH_GHOST_MLRF_FROM_FLUID,
              COLLIDED_WITH_GHOST_MLRF_FROM_SURFACE,
              MASSLESS_TRACER_PARTICLE,
              ABOUT_TO_BE_DELETED,
              NUM_DYNAMICS_TYPES} eDYNAMICS_TYPE;
extern const char* parcel_dynamics_type_names[NUM_DYNAMICS_TYPES];
#define N_SPACE_DIMS 3
#define NUM_PREVIOUS_STORED_TIMESTEPS 4 //space for state of previous and next timestep.  This must be small enough that the particle state is less than 168 bytes (limited by sp to sp RPC calls) in shared memory mode

typedef struct sPARTICLE_SUBCYCLING {
  BOOLEAN is_active;
  asINT32 n_base_steps;

  VOID  set_particle_solver_mask(asINT32 time, SCALE scale) {
    n_base_steps = g_n_film_solver_base_steps;
    asINT32 n_allowed_steps = 5, allowed_steps[] = {1, 2, 4, 8, 16};
    BOOLEAN found = FALSE;
    ccDOTIMES(i, n_allowed_steps) {
      if(allowed_steps[i] == n_base_steps) {
        found = TRUE;
        break;
      }
    }
    if(!found)
      msg_error("n_film_solver_base_steps must be a power of 2");
    is_active = FALSE;
    if(n_base_steps == 1)
      is_active = TRUE;
    if((time + 1)%n_base_steps == 0)
      is_active = TRUE;
  }
}*PARTICLE_SUBCYCLING;

extern sPARTICLE_SUBCYCLING g_particle_subcycling_info;

typedef class sPARCEL_STATE {
 public:

  //eDYNAMICS_TYPE dynamics_type;
  uINT32 dynamics_type;
  uINT32 last_buffer_index;
  sPARTICLE_VAR x[NUM_PREVIOUS_STORED_TIMESTEPS][N_SPACE_DIMS];
  sPARTICLE_VAR v[NUM_PREVIOUS_STORED_TIMESTEPS][N_SPACE_DIMS];

  //states added for dynamics trajectory decimation
  sPARTICLE_VAR last_measured_v[N_SPACE_DIMS];
  sPARTICLE_VAR last_measured_x[N_SPACE_DIMS];
  sPARTICLE_VAR last_measured_t;
  auINT64 parcel_is_measurable_for_trajectory_window;  //This bitflag is determined when a particle enters a window for the first time,
                                                       //if the corresponding bit in visited trajectory_windows is not set, then the bitflag value is undefined
  auINT64 visited_trajectory_windows;  //Bitflags of what windows have been visited
  sINT32 first_measured_sp;
  TIMESTEP first_measured_ts;
  sINT32 order_in_measured_ts;

  asINT32 lrf_index;
  LRF_PHYSICS_DESCRIPTOR lrf() {
    return this->lrf_index == -1 ? NULL : &(sim.lrf_physics_descs[this->lrf_index]);
  }

  //States added for surface tension model.
#ifdef COMPILE_SURFACE_TENSION_MODEL
  //sPARTICLE_VAR r_st[NUM_PREVIOUS_STORED_TIMESTEPS][N_SPACE_DIMS]; //displacement due to surface tension forces
  //sPARTICLE_VAR v_st[NUM_PREVIOUS_STORED_TIMESTEPS][N_SPACE_DIMS]; //velocity resulting from surface tension forces
  //sPARTICLE_VAR time_st[NUM_PREVIOUS_STORED_TIMESTEPS];            //period of time when parcel feels surface tension forces
  //BOOLEAN surface_tension_on;  // Relates to the parcel; will become FALSE when shift due to surface tension r_st increase has to be stopped and v_st becomes zero
#endif

  sPARTICLE_VAR d;
  sPARTICLE_VAR rho;
  sPARTICLE_VAR C_d;
  sPARTICLE_VAR surface_tension; //This is the material constant used in the splash and breakup models.
  sPARTICLE_VAR kinematic_viscosity;
  sPARTICLE_VAR temperature;
  sPARTICLE_VAR m_mass_fractions[NUM_PARTICLE_MATERIAL_SPECIES - 1];
  sPARTICLE_VAR mass_fraction(int species_index);
 
  sPARTICLE_VAR parcel_mass;
  sPARTICLE_VAR unevaporated_mass;
  sPARTICLE_VAR num_particles;
  //sPARTICLE_VAR terminal_velocity_mag;
  auINT32 reflection_count;

  BOOLEAN particles_may_breakup;
  sPARTICLE_VAR y_breakup[NUM_PREVIOUS_STORED_TIMESTEPS];
  sPARTICLE_VAR dydt[NUM_PREVIOUS_STORED_TIMESTEPS];
  sINT32 no_drag_steps_number;

  uINT32 creation_timestep;
  sINT32 last_update_timestep;

  asINT32 id;
  auINT32 originating_sp;
  auINT32 emitter_id;
  SHOB_ID m_home_shob_id;
  asINT32 m_home_voxel; //this is also used to flag the offset in a mlrf ring segment so needs to be a full int.
  // Container should only be updated once per time step
  bool    m_updated_container_this_timestep;
  bool    m_collision_done_this_timestep;

  //flag the parcel during the resample algorithm
  asINT32 resample;
  asINT32 aggl_applied;

  sPARCEL_STATE() {
    memset(this, 0, sizeof(sPARCEL_STATE));
    m_home_shob_id = INVALID_SHOB_ID;
    m_home_voxel = -1;
  }

  //We would like to devise some scheme that prevents a developer from calling delete on a parcel directly.
  //However if we delete the delete operator, then that also disables the new operator.
  //VOID operator delete(void* parcel) = delete;

  ~sPARCEL_STATE() {
    if(dynamics_type != ABOUT_TO_BE_DELETED) {
      //Use one of the destory_parcel methods instead of calling delete directly so that lost mass can be accounted for accurately.
      //msg_error("Unexpected call to a sPARCEL_STATE's destructor.\n");
    }
  }


  sPARCEL_STATE(sINT32 new_id,
                auINT32 new_creation_timestep,
                auINT32 new_originating_sp,
                auINT32 new_emitter_id,
                sPARTICLE_VAR new_num_particles,
                auINT32 new_reflection_count,
                eDYNAMICS_TYPE new_dynamics_type,
                BOOLEAN new_particles_may_breakup,
                sPARTICLE_VAR new_y_breakup,
                sPARTICLE_VAR new_dydt,
                auINT32 new_no_drag_steps_number,
                sPARTICLE_VAR new_density,
                sPARTICLE_VAR new_kinematic_viscosity,
                sPARTICLE_VAR new_surface_tension,
                sPARTICLE_VAR new_diameter,
                sPARTICLE_VAR new_position[N_SPACE_DIMS],
                sPARTICLE_VAR new_velocity[N_SPACE_DIMS],
                asINT32 new_lrf_index);

  VOID init(std::vector<unsigned char>::iterator &recv_buffer);
  sPARCEL_STATE (const sPARCEL_STATE &old_parcel);
  const char* dynamics_name() {return(parcel_dynamics_type_names[dynamics_type]);}
  VOID combine_parcels(PARCEL_STATE other_parcel);
 
  static uINT64 ckpt_len() {
    return sizeof(sPARCEL_STATE);
  }
  VOID write_ckpt();
  VOID read_ckpt();
  VOID print_state(FILE* fid,asINT32 phase);

  inline BOOLEAN is_in_fluid() { return  
      this->dynamics_type == IN_FLUID || 
      this->dynamics_type == MASSLESS_TRACER_PARTICLE|| 
      this->dynamics_type == JUST_EMITTED_MAYBE_IN_FLUID ||
      this->dynamics_type == JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT;}

  inline BOOLEAN is_on_surface() { return !is_in_fluid();}

  inline BOOLEAN is_massless_tracer() { return this->dynamics_type == MASSLESS_TRACER_PARTICLE;}
  inline VOID set_massless_tracer(BOOLEAN is_massless) { if(is_massless) this->dynamics_type = MASSLESS_TRACER_PARTICLE;}

  inline auINT32 age() {return g_timescale.time_flow() - creation_timestep;}
  inline auINT32 time_since_update(){return(g_timescale.time_flow() - last_update_timestep);}
  inline VOID mark_as_updated() {
    last_update_timestep = g_timescale.time_flow();
    last_buffer_index++;
    last_buffer_index %= NUM_PREVIOUS_STORED_TIMESTEPS;
  }
  inline auINT32 relative_timestep_to_state_index(asINT32 relative_step) const {
    asINT32 buffer_index = (last_buffer_index + relative_step + NUM_PREVIOUS_STORED_TIMESTEPS ) % NUM_PREVIOUS_STORED_TIMESTEPS;
    return(buffer_index);
  }

  VOID extrapolate_trajectory(asINT32 timestep_index, sPARTICLE_VAR delta_t,
                            sPARTICLE_VAR new_pos[N_SPACE_DIMS]) {
    new_pos[0] = x[timestep_index][0] + v[timestep_index][0] * delta_t;
    new_pos[1] = x[timestep_index][1] + v[timestep_index][1] * delta_t;
    new_pos[2] = x[timestep_index][2] + v[timestep_index][2] * delta_t;
  }

  VOID fill_send_buffer(std::vector<unsigned char> &send_buffer);

  VOID change_dynamics_type(eDYNAMICS_TYPE new_dynamics_type){ 
    if(this->dynamics_type == ON_SURFACE && 
       (new_dynamics_type == REENTRAINED_INTO_GHOST_UBLK || 
        new_dynamics_type == JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT)){
      this->first_measured_ts = -1;
      this->order_in_measured_ts = -1; 
      this->parcel_is_measurable_for_trajectory_window = 0;
      this->visited_trajectory_windows = 0;
      this->aggl_applied = 0; 
    }
    this->dynamics_type = new_dynamics_type;
  };

} *PARCEL_STATE;

struct sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES {
enum eSUPPORTED_VAR_TYPES {
    XFORCE, //The components of any vector types must be consecutive.
    YFORCE,
    ZFORCE,
    EVAPORATION_RATE,
    NUMBER,
    MEAN_DENSITY,
    MEAN_DIAMETER,
    MEAN_SURFACE_AREA,
    MEAN_VOLUME,
    MEAN_MASS,
    MEAN_XVEL,
    MEAN_YVEL,
    MEAN_ZVEL,
    MEAN_VEL_MAG,
    MEAN_TEMPERATURE,
    MEAN_COMPOSITION_1,
    MEAN_COMPOSITION_2,
    MEAN_COMPOSITION_3,
    NUM_SUPPORTED_VARTYPES
  };
  static const SRI_VARIABLE_TYPE m_all_supported_sri_fluid_vartypes[NUM_SUPPORTED_VARTYPES];
}; 


class sPARTICLE_MEAS_VARTYPE_BITSET : public sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES, public  tBITSET<sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES::NUM_SUPPORTED_VARTYPES> {
 public:
  void unmask_vartypes(int n_vars, SRI_VARIABLE_TYPE* var_types) {
    ccDOTIMES(var_index, n_vars) {
      SRI_VARIABLE_TYPE var_type = var_types[var_index];
      ccDOTIMES(i, NUM_SUPPORTED_VARTYPES) {
        if(m_all_supported_sri_fluid_vartypes[i] == var_type) {
          set(i);
          break;
        }
      }
    }
  }
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_PARTICLE_DATA 
{
  EXTRACT_UBLK_TRAITS

  sPARCEL_LIST* fluid_parcel_list[N_VOXELS];

  //ublk reference that the particle emitters have access to when emitting a parcel.
  //This variable is set during initialization using the ublk_dyn quantums.
  struct sSTATIC_PDATA {
    uINT64 window_mask; //flags which particle trajectory windows this ublk belongs to.
    asINT32 num_parcels[N_VOXELS];  //This count of many parcels are contained in each voxel
                                    //is used for reading the variable sized records from a checkpoint stream.
    sPARTICLE_VAR particle_force[3][N_VOXELS]; //This is the sum of all drag forces for particles in this ublk's voxels and must be stored to apply as a body force to LB solver when momentum copupling is enabled.
    sPARTICLE_VAR particle_evaporation_rate[N_VOXELS];
    sPARTICLE_VAR wetbulb_temp[N_VOXELS];
    //sPARTICLE_VAR Sij[N_VOXELS][6]; //Strain rate tensor of the continuum.
    sPARTICLE_VAR grad_u[N_VOXELS][3][3];
    vxFLOAT grad_p[3][N_VOXORS];

    sPARTICLE_VAR Sij(asINT32 voxel, asINT32 component) {
      if(component < 3)
        return(grad_u[voxel][component][component]);
      if(component == 3)
        return 0.5 * ( grad_u[voxel][0][1] + grad_u[voxel][1][0] );
      if(component == 4)
        return 0.5 * ( grad_u[voxel][0][2] + grad_u[voxel][2][0] );
      if(component == 5)
        return 0.5 * ( grad_u[voxel][1][2] + grad_u[voxel][2][1] );

      return(0);
    }

    sPARTICLE_VAR Sij(asINT32 voxel, asINT32 i, asINT32 j) {
      return 0.5 * ( grad_u[voxel][i][j] + grad_u[voxel][j][i] );
    }

    // Use struct instead of union to avoid ckpt resume issues. See PR48632
    struct {
      sINT16 m_window_combination_index;
      sPARTICLE_MEAS_VARTYPE_BITSET m_required_meas_variables;
    } m_window_data;
    
  }s;

  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, asINT32 timestep_index) {}
  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 timestep_index) {}
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, asINT32 timestep_index) {}
  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index) {}

  static VOID add_send_size(asINT32 &send_size) {
    //send_size += (sizeof(sSTATIC_PDATA) / sizeof(sdFLOAT));
  }

  VOID init() {}
  VOID init2(VOXEL_MASK_8 voxel_mask) {
    for(asINT32 voxel = 0; voxel < N_VOXELS ; voxel++) {
      if (voxel_mask.test(voxel)) {
        if( fluid_parcel_list[voxel] == NULL) { //do not initialize voxels that already have allocated lists
          fluid_parcel_list[voxel] = xnew sPARCEL_LIST;
          s.particle_force[0][voxel] = 0.0;
          s.particle_force[1][voxel] = 0.0;
          s.particle_force[2][voxel] = 0.0;
          s.wetbulb_temp[voxel] = -1; //sim.char_temp;
        }
      }
    }
  }
  uINT64 ckpt_len();
  VOID write_ckpt();
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();
  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {}
};

#if 0
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_PARTICLE_DATA {
  EXTRACT_UBLK_TRAITS

  SCALE scale_range_of_stencils[N_VOXELS][2];

  static VOID add_send_size(asINT32 &send_size) {
    //send_size += (sizeof(sSTATIC_SURF_PDATA) / sizeof(sdFLOAT));
  }
  VOID fill_send_buffer(sdFLOAT* &send_buffer, asINT32 timestep_index, NEIGHBOR_MASK_INDEX nmi) {}

  VOID init() {}
  VOID init2(VOXEL_MASK_8 voxel_mask) {
    for(asINT32 voxel = 0;voxel < N_VOXELS;voxel++) {
      scale_range_of_stencils[voxel][0] = COARSEST_SCALE;
      scale_range_of_stencils[voxel][1] = FINEST_SCALE;
    }
  }
  VOID write_ckpt() {}
  VOID read_ckpt() {}
  VOID write_mme_ckpt(VOXEL_MASK_8 voxel_mask, asINT32 scale) {}
};
#endif

typedef tUBLK_PARTICLE_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_PARTICLE_DATA, *UBLK_PARTICLE_DATA;
//typedef tNEARBLK_PARTICLE_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_PARTICLE_DATA, *NEAR_UBLK_PARTICLE_DATA;

#ifdef BUILD_GPU
typedef tUBLK_PARTICLE_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_PARTICLE_DATA, *MBLK_PARTICLE_DATA;
//typedef tNEARBLK_PARTICLE_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_PARTICLE_DATA, *NEAR_MBLK_PARTICLE_DATA;
#endif

#ifdef BUILD_GPU
INIT_MBLK(UBLK_PARTICLE_DATA) 
{
  msg_internal_error("init_mblk for tUBLK_PARTICLE_DATA is incomplete");
}
#endif


// Base class for regular surfels and sampling surfels
typedef struct sSURFEL_PARTICLE_DATA_BASE {
  // Vertices are sorted according to the right-hand-rule, counter-clockwise
  // around the surfel. Half edges are sorted similarly, with half edge N
  // using vertices N and (N + 1) % n_vertices.

  sINT16 n_vertices;
  DGF_VERTEX_INDEX first_vertex_index; // index in g_surfel_vertex_indices

  asINT32 n_edges() { return n_vertices; }
  // first_half_edge_index() is an index in g_surfel_half_edges
  DGF_VERTEX_INDEX first_half_edge_index() { return first_vertex_index; }


} *SURFEL_PARTICLE_DATA_BASE;


enum ePARCEL_STATE {
  HIT,
  SPLASHED,
  REENTRAINED,
  ACCUMULATED,
  REFLECTED,
  NUM_PARCEL_STATES
};

typedef struct sSURFEL_PARTICLE_DATA : public sSURFEL_PARTICLE_DATA_BASE {

  PARCEL_LIST surface_parcel_list;
  std::vector<sSURFEL*> *intersecting_surfels; //This is only set for mlrf surfels and is needed to detect film flowing across a sliding mesh interface.

  struct sFIXED_SIZE_SURFEL_PARTICLE_DATA {

    //These variables are used for accumulating particle statistics on a surface.
    uINT64 num[NUM_PARCEL_STATES];
    sPARTICLE_VAR mass[NUM_PARCEL_STATES];

    sPARTICLE_VAR sum_density[NUM_PARCEL_STATES];
    //sPARTICLE_VAR sum_density_flux;
    sPARTICLE_VAR sum_diameter[NUM_PARCEL_STATES];

    sPARTICLE_VAR sum_particle_impulse[N_SPACE_DIMS];

    //Variables for the film solver.
    sPARTICLE_VAR surface_force[N_SPACE_DIMS];
    sPARTICLE_VAR film_thickness;
    sPARTICLE_VAR accretion_volume;
    sPARTICLE_VAR reentrained_film_thickness;
    sPARTICLE_VAR surfel_curvature[9];
    sPARTICLE_VAR film_momentum[N_SPACE_DIMS];
    sPARTICLE_VAR film_acceleration[N_SPACE_DIMS]; //used in the experimental relaxation scheme
    sPARTICLE_VAR film_dynamics_timescale;

    sPARTICLE_VAR film_mass;
    sPARTICLE_VAR is_wetted;
    sPARTICLE_VAR film_viscosity;

    sINT32 num_parcels; //this is repurposed at initialization for stencil creation.
    sINT32* was_added_to_stencil() {return &num_parcels;}

    //For contact line surface tension model.
    sPARTICLE_VAR Fuchs_number;
    sPARTICLE_VAR the_rivulet_edge_is_here;  //--/  This will be either zero, or sqrt(surfel-area) == "effective diameter", as if the surfel was round
    sPARTICLE_VAR Heaviside_function;        //--/  This will be either zero, or 1. It is Heaviside step-function
    sPARTICLE_VAR surface_tension[N_SPACE_DIMS];
    sPARTICLE_VAR corrected_surface_tension[N_SPACE_DIMS];
    sPARTICLE_VAR effective_capillari_length;  // So far, it is calculated for the full gravity force, sqrt[sigma/(rho * g)], not in-plane component. As such, could be made a global variable.
    sPARTICLE_VAR deGennes_length           ;  // Surfel-specific, depends on advancing/receding type and combinations, calculated using effective_capillari_length above.
    sPARTICLE_VAR t_star;      // "Capillary time" = characteristic period of time when parcel feels surface tension forces (I. Staroselsky, Private Comm., Dec 10, 2014)
    sPARTICLE_VAR t_st_life;   // Max time allowed to a surface parcel to move due to the edge (contact line) surface tension.

    asINT32 surface_material_id;



    uINT64* was_area_accumulated() {return &num[HIT];}

    sPARTICLE_VAR num_outbound() {
      return ( num[SPLASHED] + num[REENTRAINED] + num[REFLECTED]);
    }
    sPARTICLE_VAR mass_outbound() {
      return( mass[SPLASHED] + mass[REENTRAINED] + mass[REFLECTED] );
    }
    sPARTICLE_VAR sum_density_outbound() {
      return( sum_density[SPLASHED] + sum_density[REENTRAINED] + sum_density[REFLECTED] );
    }
    sPARTICLE_VAR sum_diameter_outbound() {
      return( sum_diameter[SPLASHED] + sum_diameter[REENTRAINED] + sum_diameter[REFLECTED] );
    }

    sPARTICLE_VAR num_inbound()  { return (num[HIT]);  }
    sPARTICLE_VAR mass_inbound() { return (mass[HIT]); }
    sPARTICLE_VAR sum_density_inbound() { return (sum_density[HIT]); }
    sPARTICLE_VAR sum_diameter_inbound() { return (sum_diameter[HIT]); }

    VOID clear_reentrainment_meas_accumulators() {
      //This is called per surfel at the beginning of the
      //reentrainment model execution.
      num[REENTRAINED] = 0;
      mass[REENTRAINED] = 0.0;
      sum_density[REENTRAINED] = 0.0;
      sum_diameter[REENTRAINED] = 0.0;
    }

    VOID clear_meas_accumulators(bool force = false);

    VOID clear_film_accumulators() {
      film_thickness = 0.0;

      ccDOTIMES(i, 3) {
        film_momentum[i] = 0.0;
        film_acceleration[i] = 0.0;
      }
      film_dynamics_timescale = DBL_MAX;
      film_mass = 0.0;
      is_wetted = 0.0;
      film_viscosity = 0.0;

      reentrained_film_thickness = 0.0;
      Fuchs_number = 0.0;
      Heaviside_function = 0.0;
      the_rivulet_edge_is_here = 0.0;

      ccDOTIMES(i, N_SPACE_DIMS) {
        corrected_surface_tension[i] = 0.0;
      }
    }

  } s;

  sINT16 m_lrf_index;
  BOOLEAN m_surfel_is_wipeable;
  VOID mark_surfel_stencil_subject_to_simple_wiper_model() {
    m_surfel_is_wipeable = TRUE;
  }
  
  BOOLEAN surfel_stencil_is_subject_to_simple_wiper_model() {
    return m_surfel_is_wipeable;
  }

  SCALE scale_range[2];  //min and max scale of the surfels in the stencil
  FILM_STENCIL_SURFEL_VECTOR nearby_surfels;
  FILM_STENCIL_WEIGHTS_VECTOR nearby_areas;

  // Parcels in these ublks may intersect with this surfel
  std::vector<UBLK> neighbor_ublks;
  std::vector<VOXEL_MASK_8> neighbor_voxel_masks;

  //trianglization of surfel for collision detection
  DGF_VERTEX_INDEX  m_n_triangles;
  std::vector<sPARTICLE_VAR> triangle_edge_in_plane_normals;
  std::vector<sPARTICLE_VAR> triangle_normals;
  
  VOID init(DGF_SURFEL_DESC surfel_desc);
  uINT64 ckpt_len();
  VOID write_ckpt();
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();
  VOID write_mme_ckpt(VOXEL_MASK_8 voxel_mask, asINT32 scale) {}
  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_buffer) { }
  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) { }
  VOID fill_film_data(sdFLOAT * &send_buffer);
  VOID fill_solid_parcel_data(sdFLOAT * &send_buffer);
  VOID accumulate_film_data(sdFLOAT * &recv_buffer);
  VOID accumulate_solid_parcel_data(sdFLOAT * &recv_buffer);
}*SURFEL_PARTICLE_DATA;

typedef  sSURFEL_PARTICLE_DATA::sFIXED_SIZE_SURFEL_PARTICLE_DATA sSURFEL_P_DATA_SEND_FIELD;
static const asINT32 SURFEL_P_DATA_CKPT_SIZE = sizeof(sSURFEL_PARTICLE_DATA::sFIXED_SIZE_SURFEL_PARTICLE_DATA);


typedef struct sSAMPLING_SURFEL_PARTICLE_DATA : public sSURFEL_PARTICLE_DATA_BASE {

  std::vector<sSURFEL*> *intersecting_surfels;

  enum eMEAS_DIR{
    IN,
    OUT
  };

  struct sFIXED_SIZE_SAMPLING_SURFEL_PARTICLE_DATA {
    asINT32 number[2];
    sPARTICLE_VAR sum_density[2];
    sPARTICLE_VAR sum_diameter[2];
    sPARTICLE_VAR sum_area[2];
    sPARTICLE_VAR sum_volume[2];
    sPARTICLE_VAR sum_particle_vel[2][3];
    sPARTICLE_VAR sum_particle_vel_mag[2];
    sPARTICLE_VAR sum_mass[2];

    asINT32 num_pass_through() {
      return(number[IN]+ number[OUT]);
    }
    sPARTICLE_VAR sum_density_pass_through() {
      return(sum_density[OUT] + sum_density[IN]);
    }
    sPARTICLE_VAR sum_diameter_pass_through() {
      return(sum_diameter[OUT] + sum_diameter[IN]);
    }
    sPARTICLE_VAR sum_area_pass_through() {
      return(sum_area[OUT] + sum_area[IN]);
    }
    sPARTICLE_VAR sum_volume_pass_through() {
      return(sum_volume[OUT] + sum_volume[IN]);
    }
    sPARTICLE_VAR sum_particle_vel_pass_through(asINT32 dim){
      return(sum_particle_vel[OUT][dim] + sum_particle_vel[IN][dim]);
    }

    sPARTICLE_VAR sum_particle_vel_mag_pass_through(){
      return(sum_particle_vel_mag[OUT] + sum_particle_vel_mag[IN]);
    }
    sPARTICLE_VAR sum_mass_gross() {
      return(sum_mass[OUT] + sum_mass[IN]);
    }

    sPARTICLE_VAR sum_mass_net() {
      return(sum_mass[OUT] - sum_mass[IN]);
    }

    //variables that sample nearby fluid properties
    //asINT32 num_particles;
    //sPARTICLE_VAR sum_density;
    //sPARTICLE_VAR sum_diameter;
    //sPARTICLE_VAR sum_area;
    //sPARTICLE_VAR sum_volume;
    //sPARTICLE_VAR sum_particle_vel[3];

    VOID clear_meas_accumulators() {

      number[IN] = 0;
      sum_density[IN] = 0;
      sum_diameter[IN] = 0;
      sum_area[IN] = 0;
      sum_volume[IN] = 0;
      vzero(sum_particle_vel[IN]);
      sum_particle_vel_mag[IN] = 0;
      sum_mass[IN] = 0;

      number[OUT] = 0;
      sum_density[OUT] = 0;
      sum_diameter[OUT] = 0;
      sum_area[OUT] = 0;
      sum_volume[OUT] = 0;
      vzero(sum_particle_vel[OUT]);
      sum_particle_vel_mag[OUT] = 0;
      sum_mass[OUT] = 0;


      //num_particles = 0;
      //sum_density = 0;
      //sum_diameter = 0;
      //sum_area = 0;
      //sum_volume = 0;
      //vzero(sum_particle_vel);

    }

    asINT32 surface_material_id;

  } s;
  // Parcels in these ublks may intersect with this surfel
  std::vector<UBLK> neighbor_ublks;
  std::vector<VOXEL_MASK_8> neighbor_voxel_masks;

  VOID init() {
    intersecting_surfels = nullptr;
    s.clear_meas_accumulators();
  }
  VOID write_ckpt(){write_ckpt_lgi(this->s);}
  VOID read_ckpt(){ read_lgi(this->s);}
  VOID write_mme_ckpt(VOXEL_MASK_8 voxel_mask, asINT32 scale) {}

  VOID pack_particle_meas_data(sFIXED_SIZE_SAMPLING_SURFEL_PARTICLE_DATA *element) {
    memcpy(element, &this->s, sizeof(sFIXED_SIZE_SAMPLING_SURFEL_PARTICLE_DATA));
  }

  //trianglization of surfel for collision detection
  DGF_VERTEX_INDEX  m_n_triangles;
  std::vector<sPARTICLE_VAR> triangle_edge_in_plane_normals;
  std::vector<sPARTICLE_VAR> triangle_normals;
  VOID accumulate_particle_meas_data(sFIXED_SIZE_SAMPLING_SURFEL_PARTICLE_DATA *element) {
    s.number[IN]               += element->number[IN];
    s.sum_density[IN]          += element->sum_density[IN];
    s.sum_diameter[IN]         += element-> sum_diameter[IN];
    s.sum_area[IN]             += element->sum_area[IN];
    s.sum_volume[IN]           += element->sum_volume[IN];
    s.sum_particle_vel[IN][0]  += element->sum_particle_vel[IN][0];
    s.sum_particle_vel[IN][1]  += element->sum_particle_vel[IN][1];
    s.sum_particle_vel[IN][2]  += element->sum_particle_vel[IN][2];
    s.sum_particle_vel_mag[IN] += element->sum_particle_vel_mag[IN];
    s.sum_mass[IN]             += element->sum_mass[IN];

    s.number[OUT]               += element->number[OUT];
    s.sum_density[OUT]          += element->sum_density[OUT];
    s.sum_diameter[OUT]         += element-> sum_diameter[OUT];
    s.sum_area[OUT]             += element->sum_area[OUT];
    s.sum_volume[OUT]           += element->sum_volume[OUT];
    s.sum_particle_vel[OUT][0]  += element->sum_particle_vel[OUT][0];
    s.sum_particle_vel[OUT][1]  += element->sum_particle_vel[OUT][1];
    s.sum_particle_vel[OUT][2]  += element->sum_particle_vel[OUT][2];
    s.sum_particle_vel_mag[OUT] += element->sum_particle_vel_mag[OUT];
    s.sum_mass[OUT]             += element->sum_mass[OUT];
  }

} *SAMPLING_SURFEL_PARTICLE_DATA;

typedef struct sSURFEL_FILM_COMM_ELEMENT {
  uINT64 num[NUM_PARCEL_STATES];

  sPARTICLE_VAR mass[NUM_PARCEL_STATES];

  sPARTICLE_VAR sum_density[NUM_PARCEL_STATES];

  sPARTICLE_VAR sum_diameter[NUM_PARCEL_STATES];

  sPARTICLE_VAR film_thickness;
  sPARTICLE_VAR accretion_volume;
  sPARTICLE_VAR reentrained_film_thickness;
  sPARTICLE_VAR film_momentum[3];
  sPARTICLE_VAR film_acceleration[3];
  sPARTICLE_VAR film_dynamics_timescale;
  sPARTICLE_VAR sum_particle_impulse[3];
  sPARTICLE_VAR film_mass;
  sPARTICLE_VAR is_wetted;
  sPARTICLE_VAR film_viscosity;

  //These variables are needed for film surface tension model.
  sINT32 num_parcels;
  sPARTICLE_VAR Fuchs_number;
  sPARTICLE_VAR the_rivulet_edge_is_here;  //--/  This will be either zero, or sqrt(surfel-area) == "effective diameter", as if the surfel was round
  sPARTICLE_VAR Heaviside_function;        //--/  This will be either zero, or 1. It is Heaviside step-function
  sPARTICLE_VAR surface_tension[N_SPACE_DIMS];
  sPARTICLE_VAR corrected_surface_tension[N_SPACE_DIMS];
  sPARTICLE_VAR effective_capillari_length;  // So far, it is calculated for the full gravity force, sqrt[sigma/(rho * g)], not in-plane component. As such, could be made a global variable.
  sPARTICLE_VAR deGennes_length;             // Surfel-specific, depends on advancing/receding type and combinations, calculated using effective_capillari_length above.
  sPARTICLE_VAR t_star;                      // "Capillary time" = characteristic period of time when parcel feels surface tension forces (I. Staroselsky, Private Comm., Dec 10, 2014)
  sPARTICLE_VAR t_st_life;                   // Max time allowed to a surface parcel to move due to the edge (contact line) surface tension.

} *SURFEL_FILM_COMM_ELEMENT;

typedef struct sSURFEL_SOLID_PARCEL_COMM_ELEMENT {
  uINT64 num[NUM_PARCEL_STATES];
  sPARTICLE_VAR mass[NUM_PARCEL_STATES];
  sPARTICLE_VAR sum_density[NUM_PARCEL_STATES];
  sPARTICLE_VAR sum_diameter[NUM_PARCEL_STATES];
  sPARTICLE_VAR sum_particle_impulse[3];
  sPARTICLE_VAR accretion_volume;
} *SURFEL_SOLID_PARCEL_COMM_ELEMENT;

//define parameters for the Runge-Kutta time integration method used to integrate particle trajectories
//these coefficients and constants define the standard (RK4) 4th order, explicit, numerical integrator.
#define RUNGE_KUTTA_ORDER 4
#define RUNGE_KUTTA_FRACTIONAL_STEPS {0,0.5,0.5,1}
#define RUNGE_KUTTA_STEP_WEIGHTS {{0.5, 0.0, 0.0, 0.0}, {0.0, 0.5, 0.0, 0.0}, {0.0, 0.0, 1.0, 0.0}, {1/6.0, 1/3.0, 1/3.0, 1/6.0}}
//RK4 coeffecients (same as above but written as 2d matrix).  products for entries with j>i (elements above diagonal) are
//not actually performed as this is an explicit scheme...therfore non-zero entries above the diagonal will be ignored
//RUNGE_KUTTA_STEP_WEIGHTS =       { {0.5  , 0.0  , 0.0  , 0.0},
//                                   {0.0  , 0.5  , 0.0  , 0.0},
//                                   {0.0  , 0.0  , 1.0  , 0.0},
//                                   {1/6.0, 1/3.0, 1/3.0, 1/6.0}}
//

#define SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR    ( 0.22 / (sPARTICLE_VAR)sim.char_density_f )
#define SIMULATOR_PRESSURE_TO_LATTICE_PRESSURE_SCALE_FACTOR  ( 1.0 / sim.char_pressure / 13.5 )

//These are available for debugging message output but should be avoided in solver computations.
#define LENGTH_TO_MKS_SCALE_FACTOR                           ( sim.meters_per_cell )
#define TEMPERATURE_TO_MKS_SCALE_FACTOR                      ( 300.0 / sim.one_over_300k_lat )
#define LATTICE_DENSITY_TO_MKS_SCALE_FACTOR                  ( sim.kilos_per_particle / g_lattice_scale_factor / ( sim.meters_per_cell * sim.meters_per_cell * sim.meters_per_cell) * sim.char_density / 0.22 )
#define SIMULATOR_DENSITY_TO_MKS_SCALE_FACTOR                ( SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR * LATTICE_DENSITY_TO_MKS_SCALE_FACTOR )
#define TIME_TO_MKS_SCALE_FACTOR                             ( sim.one_mps_lattice*sim.meters_per_cell )
#define VELOCITY_TO_MKS_SCALE_FACTOR                         ( 1.0 / sim.one_mps_lattice )
#define ACCELERATION_TO_MKS_SCALE_FACTOR                     ( 1.0 / sim.one_mps_lattice / TIME_TO_MKS_SCALE_FACTOR )
#define KINEMATIC_VISCOSITY_TO_MKS_SCALE_FACTOR              ( sim.meters_per_cell / sim.one_mps_lattice )
#define DYNAMIC_VISCOSITY_TO_MKS_SCALE_FACTOR                ( KINEMATIC_VISCOSITY_TO_MKS_SCALE_FACTOR * LATTICE_DENSITY_TO_MKS_SCALE_FACTOR )
#define MASS_TO_MKS_SCALE_FACTOR                             ( LATTICE_DENSITY_TO_MKS_SCALE_FACTOR * ( LENGTH_TO_MKS_SCALE_FACTOR * LENGTH_TO_MKS_SCALE_FACTOR * LENGTH_TO_MKS_SCALE_FACTOR))
#define GRAVITY_TO_MKS_SCALE_FACTOR                          ( VELOCITY_TO_MKS_SCALE_FACTOR / TIME_TO_MKS_SCALE_FACTOR)
#define LATTICE_PRESSURE_TO_MKS_SCALE_FACTOR                 ( LATTICE_DENSITY_TO_MKS_SCALE_FACTOR * VELOCITY_TO_MKS_SCALE_FACTOR * VELOCITY_TO_MKS_SCALE_FACTOR)
#define SIMULATOR_PRESSURE_TO_MKS_SCALE_FACTOR               ( SIMULATOR_PRESSURE_TO_LATTICE_PRESSURE_SCALE_FACTOR * LATTICE_PRESSURE_TO_MKS_SCALE_FACTOR)
#define ENERGY_TO_MKS_SCALE_FACTOR                           ( MASS_TO_MKS_SCALE_FACTOR * VELOCITY_TO_MKS_SCALE_FACTOR * VELOCITY_TO_MKS_SCALE_FACTOR)
#define SURFACE_TENSION_TO_MKS_SCALE_FACTOR                  ( MASS_TO_MKS_SCALE_FACTOR / TIME_TO_MKS_SCALE_FACTOR / TIME_TO_MKS_SCALE_FACTOR)


#define R_IS_OUTSIDE_UBLK_AXIS_MIN(axis,r,ublk_min,ublk_max)    (  (r)[axis] < (ublk_min) )
#define R_IS_OUTSIDE_UBLK_AXIS_MAX(axis,r,ublk_min,ublk_max)    (  (r)[axis] > (ublk_max) )
#define R_IS_OUTSIDE_UBLK_AXIS_BOUNDS(axis,r,ublk_min,ublk_max) (  R_IS_OUTSIDE_UBLK_AXIS_MIN(axis,r,ublk_min,ublk_max) || R_IS_OUTSIDE_UBLK_AXIS_MAX(axis,r,ublk_min,ublk_max) )
#define R_IS_OUTSIDE_UBLK_X_MIN(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MIN(0,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_X_MAX(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MAX(0,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_X_BOUNDS(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_BOUNDS(0,r,ublk_min,ublk_max) )
#define R_IS_OUTSIDE_UBLK_Y_MIN(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MIN(1,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_Y_MAX(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MAX(1,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_Y_BOUNDS(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_BOUNDS(1,r,ublk_min,ublk_max) )
#define R_IS_OUTSIDE_UBLK_Z_MIN(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MIN(2,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_Z_MAX(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_MAX(2,r,ublk_min,ublk_max))
#define R_IS_OUTSIDE_UBLK_Z_BOUNDS(r,ublk_min,ublk_max) (R_IS_OUTSIDE_UBLK_AXIS_BOUNDS(2,r,ublk_min,ublk_max) )

#define R_IS_OUTSIDE_UBLK(r,ublk_min,ublk_max) ( R_IS_OUTSIDE_UBLK_Z_BOUNDS(r,ublk_min,ublk_max) || R_IS_OUTSIDE_UBLK_Y_BOUNDS(r,ublk_min,ublk_max) || R_IS_OUTSIDE_UBLK_X_BOUNDS(r,ublk_min,ublk_max)  )


#define R_IS_OUTSIDE_VOXEL_AXIS_MIN(axis,r,voxel_min,voxel_max)    (  (r)[axis] < (voxel_min) )
#define R_IS_OUTSIDE_VOXEL_AXIS_MAX(axis,r,voxel_min,voxel_max)    (  (r)[axis] > (voxel_max) )
#define R_IS_OUTSIDE_VOXEL_AXIS_BOUNDS(axis,r,voxel_min,voxel_max) (  R_IS_OUTSIDE_VOXEL_AXIS_MIN(axis,r,voxel_min,voxel_max) || R_IS_OUTSIDE_VOXEL_AXIS_MAX(axis,r,voxel_min,voxel_max) )

#define R_IS_OUTSIDE_VOXEL_X_MIN(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MIN(0,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_X_MAX(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MAX(0,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_X_BOUNDS(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_BOUNDS(0,r,voxel_min,voxel_max) )
#define R_IS_OUTSIDE_VOXEL_Y_MIN(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MIN(1,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_Y_MAX(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MAX(1,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_Y_BOUNDS(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_BOUNDS(1,r,voxel_min,voxel_max) )
#define R_IS_OUTSIDE_VOXEL_Z_MIN(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MIN(2,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_Z_MAX(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_MAX(2,r,voxel_min,voxel_max))
#define R_IS_OUTSIDE_VOXEL_Z_BOUNDS(r,voxel_min,voxel_max) (R_IS_OUTSIDE_VOXEL_AXIS_BOUNDS(2,r,voxel_min,voxel_max) )

#define R_IS_OUTSIDE_VOXEL(r,voxel_min,voxel_max) ( R_IS_OUTSIDE_VOXEL_Z_BOUNDS(r,voxel_min,voxel_max) || R_IS_OUTSIDE_VOXEL_Y_BOUNDS(r,voxel_min,voxel_max) || R_IS_OUTSIDE_VOXEL_X_BOUNDS(r,voxel_min,voxel_max)  )

#endif

