/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Strands that operate on surfels and nearblks
 *
 * Vinit Gupta, Exa Corporation
 * Created Fri Sep 4, 2014
 *--------------------------------------------------------------------------*/

#include "shob_groups.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "strands.h"
#include "sleep.h"
#include "bsurfel_comm.h"
#include "surfel_process_control.h"
#include "ublk_process_control.h"
#include PHYSICS_H

VOID sFRINGE2_SURFELS_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
#if !BUILD_GPU
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_A(m_index, m_surfel_base_group_type);
  }
#endif
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_surfels(m_index, m_surfel_base_group_type);
    ACTIVE_SOLVER_MASK solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale];
    g_strand_mgr.update_consumer_counts(scale, solver_mask);
  }
  g_strand_mgr.complete_timestep();
}

VOID sFRINGE_SURFELS_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  SCALE finest_scale = FINEST_SCALE;
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
// #if !BUILD_GPU
//   for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
//     m_surfel_process_control.init(scale);
//     m_surfel_process_control.process_all_wsurfels_and_contact_surfels_B(m_index, m_surfel_base_group_type);
//   }
// #endif
//   for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
//     m_surfel_process_control.init(scale);
//     m_surfel_process_control.process_all_surfels(m_index, m_surfel_base_group_type);

//     // Need to update consumer counts for all scales separately since a fine surfel may interact with
//     // coarse voxels. If the consumer counts is updated for finer scale, it is possible that fine ghost
//     // surfel data is unpacked earlier than expected.
//     if (scale != finest_scale ) {
//       g_strand_mgr.update_consumer_counts(scale+1);
//     }
//   }

  // Commented code above was causing send groups containing regular surfels (non-wsurfels) to send data
  // during the first loop only updating wsurfels instead of after the regular surfel was updated in the second
  // loop. To alleviate issue, new function, process_all_surfels_wsurfels_and_contact_surfels_B was added
  // to sSURFEL_PROCESS_CONTROL to allow for both wsurfels and regular surfels to be updated in their
  // surfel group order using a single loop, resolving the issue.  
  for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
#if !BUILD_GPU
    m_surfel_process_control.process_all_surfels_wsurfels_and_contact_surfels_B(m_index, m_surfel_base_group_type);
#else
    m_surfel_process_control.process_all_surfels(m_index, m_surfel_base_group_type);
#endif

    // Need to update consumer counts for all scales separately since a fine surfel may interact with
    // coarse voxels. If the consumer counts is updated for finer scale, it is possible that fine ghost
    // surfel data is unpacked earlier than expected.
    if (scale != finest_scale ) {
      ACTIVE_SOLVER_MASK solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale + 1];
      g_strand_mgr.update_consumer_counts(scale+1, solver_mask);
    }
  }
  g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale,  m_surfel_process_control.m_active_solver_mask);

  g_strand_mgr.complete_timestep();
}


VOID sFRINGE2_WSURFELS_HFC_STRAND::run() {
#if !BUILD_GPU
  //Process all groups in the strand
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_B(m_index, m_surfel_base_group_type);
    g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
  }
  g_strand_mgr.complete_timestep();
#endif
}
VOID sFRINGE_WSURFELS_SAMPLING_STRAND::run() {
#if !BUILD_GPU
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
  SCALE finest_scale = FINEST_SCALE;
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: Just before loop coarsest_active_scale %d"
      , g_timescale.m_time, g_running_strand, strand_coarsest_active_scale);
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: Inside loop scale %d, m_index %d", g_timescale.m_time, g_running_strand, scale, m_index);
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_A(m_index, m_surfel_base_group_type);
    if (scale != finest_scale ) {
    ACTIVE_SOLVER_MASK solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale + 1];
      g_strand_mgr.update_consumer_counts(scale+1, solver_mask);
    }
  }
  g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale,  m_surfel_process_control.m_active_solver_mask);
  g_strand_mgr.complete_timestep();
#endif
}

VOID sINTERIOR_WSURFELS_SAMPLING_STRAND::run() {
#if !BUILD_GPU
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_A(m_index, m_surfel_base_group_type);
    g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
  }
  g_strand_mgr.complete_timestep();
#endif
}

VOID sINTERIOR_WSURFELS_HFC_STRAND::run() {
#if !BUILD_GPU
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_B(m_index, m_surfel_base_group_type);
    g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
  }
  g_strand_mgr.complete_timestep();
#endif
}


VOID sFRINGE2_NEARBLKS_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_ublk_process_control.init(scale);
    m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_FRINGE2_NEARBLK_GROUP_TYPE);
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
  }
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
  }
  g_strand_mgr.complete_timestep();
}

VOID sFRINGE2_NEARBLKS_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
    }
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sFRINGE2_NEARBLKS_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif

VOID sFRINGE_NEARBLKS_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  SCALE finest_scale = FINEST_SCALE;
  
  for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.do_all_ghost_surfels_s2v(FRINGE_NEARBLK_A_STRAND);
    consider_surrender(g_running_strand);
  }

  for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
    m_ublk_process_control.init(scale);
    m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_FRINGE_NEARBLK_GROUP_TYPE);
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
    if (scale != finest_scale) {
    ACTIVE_SOLVER_MASK solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale+1];
      g_strand_mgr.update_consumer_counts(scale+1, solver_mask);
    }
  }
  g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale,  m_surfel_process_control.m_active_solver_mask);
  g_strand_mgr.complete_timestep();
}

VOID sFRINGE_NEARBLKS_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    SCALE finest_scale = FINEST_SCALE;

    for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
      if (scale != finest_scale) {
        ACTIVE_SOLVER_MASK solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][scale + 1];
        g_strand_mgr.update_consumer_counts(scale+1,  solver_mask);
      }
    }
    g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale,  m_surfel_process_control.m_active_solver_mask);

    if (sim.is_movb_sim()) {
      for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
        request_ublk_ib_bf_bcast_sends(scale,true);
      }
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sFRINGE_NEARBLKS_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    SCALE finest_scale = FINEST_SCALE;

    for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif

VOID sINTERIOR_NEAR_SHOBS_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
    strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
#if !BUILD_GPU
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_wsurfels_and_contact_surfels_B(m_index, m_surfel_base_group_type);
  }
#endif

  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_surfel_process_control.init(scale);
    m_surfel_process_control.process_all_surfels(m_index, m_surfel_base_group_type);
  }

  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_ublk_process_control.init(scale);
    WITH_TIMER(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER) {
      m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_NEARBLK_GROUP_TYPE);
    }
#ifdef DEBUG_CONDUCTION_SOLVER
      UBLK mirror_ublk = ublk_from_id(2034);
      msg_print("T %ld scale %d After advect all vr fine mirrorU %d", g_timescale.m_time, scale, mirror_ublk->id());
      msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
      msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
    
#endif
    
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
#ifdef DEBUG_CONDUCTION_SOLVER
      msg_print("After process all ublks");
      msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
      msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
    
#endif
    g_strand_mgr.update_consumer_counts(scale,  m_ublk_process_control.m_active_solver_mask);
  }
  g_strand_mgr.complete_timestep();
}

VOID sINTERIOR_NEAR_SHOBS_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale,  m_surfel_process_control.m_active_solver_mask);
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sINTERIOR_NEAR_SHOBS_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}

#endif
