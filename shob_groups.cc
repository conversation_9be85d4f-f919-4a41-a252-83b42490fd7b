/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "sim.h"
#include "ublk.h"
#include "surfel.h"
#include "sampling_surfel.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "vr.h"
#include "mlrf.h"
#include <memory>
#include "debug_print_spec.h"
#include "mirror.h"
#include "wsurfel_comm.h"
#include "conduction_contact_averaging.h"


UBLK_FSET g_ublk_groups[N_UBLK_GROUP_TYPES];
//Surfels groups initially assigned to base group fsets, distinguishing only fringe, fringe2, and interior.
SURFEL_BASE_FSET g_surfel_base_groups[N_SURFEL_BASE_GROUP_TYPES]; // 3 fsets; fringe fringe2 and interior
//After doing all promotions so each surfel fall in the right category, use the group supertype info store in 
//the base groups to assign each surfel group to the particular type fsets below, so macros that loop through
//particular types can loop through them directly
SURFEL_FSET g_surfel_groups[N_SURFEL_GROUP_TYPES];
SAMPLING_SURFEL_FSET g_sampling_surfel_groups[N_SAMPLING_SURFEL_GROUP_TYPES];
SURFEL_PAIR_FSET g_surfel_pair_groups[N_SURFEL_PAIR_GROUP_TYPES];
CONTACT_SURFEL_FSET g_contact_surfel_groups[N_CONTACT_SURFEL_GROUP_TYPES];
BSURFEL_FSET g_bsurfel_groups[N_BSURFEL_GROUP_TYPES];
//NOTE: Since wsurfels & conduction_surfels are processed in separate strands that enable splitting dynamics in two
//      phases connected through the interface data block, might be useful to create an iterator to loop through them 
//      only, or create two base fsets, one for each. 
//      However, during intialization there is not really distinction between them so having multiple base fsets 
//      leads to duplicating code. Chosen to add a boolean flag in the base fset to mark those involved in 
//      interface, so the iterator can identify quickly those involved in interface.

#if BUILD_GPU
MBLK_FSET g_mblk_groups[N_UBLK_GROUP_TYPES];
MSFL_FSET g_msfl_groups[N_SURFEL_GROUP_TYPES];
#endif

/** PR45714 -- Solid VR Fine ublks need to be stored somewhere so they can be
 * accessed by the ublk boxes to build scale interfaces. See
 * post_process_solid_ublk(), assign_scale_interface(), and the
 * DO_SOLID_VRBLKS_OF_SCALE macro.
 */
UBLK_FSET g_solid_vr_fine_group;

BOOLEAN g_ublk_type_has_ghosts[N_UBLK_GROUP_TYPES]; //NOT NECESSARY

UBLK_GROUP sUBLK_FSET::create_group(asINT32 scale, cNEIGHBOR_SP dest_sp) {
  sUBLK_GROUP signature(scale, dest_sp);
  UBLK_GROUP group = find_group(&signature);
  if (group == NULL) {
    group = xnew sUBLK_GROUP(scale, dest_sp);
    add_group(group);
  }
  return group;
}

#if BUILD_GPU
void sMBLK_GROUP::set_group_has_mblks_with_special_fluid() {
  //unsigned long count = 0;
  for (auto& hmblk : *this) {
    if (hmblk.has_special_fluid()) {
      m_group_has_special_fluid = true;
      break;
      //count++;
    }
  }
  // printf("Group scale %d, has_porous_fluid %d, num MBLKs %u, num porous-MBLKs %lu\n",
  //        this->m_scale, m_group_has_special_fluid, m_shob_count, count);
}

MBLK_GROUP sMBLK_FSET::create_group(asINT32 scale, cNEIGHBOR_SP dest_sp) {
  sMBLK_GROUP signature(scale, dest_sp);
  MBLK_GROUP group = find_group(&signature);
  if (group == NULL) {
    group = xnew sMBLK_GROUP(scale, dest_sp);
    add_group(group);
  }
  return group;
}
#endif

// Upon initialization, we store in a global map the weights info from the LGI. Once all surfels are processed, 
// each group type store in a local array the weights it needs and we can clear up the global table.
std::unordered_map<SHOB_ID,sCONTACT_WEIGHT_SET> g_contact_info;
// Additionally, we keep track during initialization of the accumulators that need to be exchanged with other SPs,
// with the data arranged as follows:
// - vector index 1: nsp, neighbor SP it is sending info to
// - vector index 2: finer scale
// - map key: coarser surfel id (it is the one that needs accumulated data)
// - map valule: -1 for now, will be filled later with the index in the accumulator
std::vector<std::vector<std::map<SHOB_ID,asINT32>>> g_contact_comm_accumulator;

// Averaged contact involves entire faces (or equivalent contact PD) that can span multiple groups. To satisfy
// repeatibility, each group does its own accumulation, and averaging is done in a separate step after
VOID sCONTACT_ACCUMULATION::accumulate(sSURFEL *surfel) {
  //checks first is this is the first call and needs to take care of reset
  if (g_timescale.m_time != m_curr_timestep) {
    reset();
    m_curr_timestep = g_timescale.m_time;
  }
  //time to add data to the accumulated values
  sdFLOAT area = surfel->area();
  if (surfel->is_conduction_shell()) {
    //accumulate outer layer
    SHELL_LAYER_DATA layer_data = surfel->shell_conduction_data()->layer(0);
    asINT32 conduction_prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
    dFLOAT volume = layer_data->thickness * area;
    temp_accum += layer_data->temp(conduction_prior_index) * volume;
    conductivity_accum += layer_data->normal_conductivity(conduction_prior_index) * volume;
    volume_accum += volume;
  } else {
    //accumulate sampled data
    CONDUCTION_INTERFACE_SOLID_DATA interface_data = surfel->conduction_interface_solid_data();
    dFLOAT volume = interface_data->thickness * area;
    temp_accum += interface_data->temp * volume;
    conductivity_accum += interface_data->conductivity * volume;
    //The sampled distance remains constant so technically should not be needed to be accumulated. However, simpler for
    //the MPI reduction  to keep the same accumulation scheme needed for layers than for layers who might have variable
    //thickness. Thus, for now, accumulate and average always the thickness
    volume_accum += volume;
  }
}

VOID sCONTACT_SURFEL_GROUP::check_even_odd_order() {
  //Contact surfel groups use contact weights to adjust the order of primary, primary_secondary, and secondary surfels
  //within the group during promotions and when completing their initialization. As a result, odd surfels might end up
  //placed ahead of the even, which will cause errors during dynamics since odd surfels copy their properties from the
  //even ones based on the assumption that even have been processed before. Thus, we do a final sweep to check the order
  //and adjust it if detected that is wrong
  std::unordered_set<SHOB_ID> dangling_even_map;
  for (int type_int=CONTACT_SURFEL_PRIMARY; type_int<NUM_CONTACT_SURFEL_TYPES; type_int++) {
    CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(type_int);
    if (m_heads[type_int]!=NULL) {
      for(sSURFEL *surfel = m_heads[type_int], *last_surfel = m_tails[type_int]->m_next; surfel != last_surfel; ) {
        sSURFEL* next_surfel = surfel->m_next;
        if (surfel->is_even()) {
          cassert(surfel->clone_surfel() != nullptr);
          if (surfel->clone_surfel() == next_surfel) {
            next_surfel = next_surfel->m_next; //correctly ordered, moves beyond this even/odd pair
          } else {
            dangling_even_map.insert(surfel->id());
          }
        } else if (surfel->is_odd()) {
          cassert(surfel->clone_surfel() != nullptr);
          auto it = dangling_even_map.find(surfel->clone_surfel()->id());
          if (it == dangling_even_map.end()) {
            //the odd was moved ahead of the even, restores the order here by removing this odd surfel and re-inserting
            //it at the end of its type, which should place it after the even
            remove_surfel_from_group(surfel, contact_surfel_type);
            add_surfel_to_group(surfel, contact_surfel_type);
            // msg_print("shifted odd surfel %d to be after even %d", surfel->id(), surfel->clone_surfel()->id());
          } else {
            //remove it from the map since the odd has been found after the even
            dangling_even_map.erase(it);
          }
        }
        surfel = next_surfel;
      }
    }
  }
  if (dangling_even_map.size() > 0) {
    //No even found its odd counterpart afterwards within the group, throw an error since this should not happen
    msg_internal_error("Found even contact surfels missing odd clones within the contact surfel type");
  }
} 

VOID sCONTACT_SURFEL_GROUP::link_shob_types_and_fill_contact_weights(std::unordered_map<SHOB_ID, asINT32>& id_to_info_index_map,
                                                                     std::unordered_map<sCONTACT_SURFEL_GROUP*, std::vector<asINT32>>& deferred_info_map) 
{
  //First, links the subsets so all surfels can be traversed sequentially enabling contact groups to be processed as 
  //regular surfel groups if needed. Empty subsets are identified with null head/tail.
  for (int type=0; type<NUM_CONTACT_SURFEL_TYPES; type++) {
    if (m_heads[type]!=NULL) { 
      if (m_head==NULL) { //found first surfel in the group, store its head & tail
        m_head = m_heads[type]; 
        m_tail = m_tails[type];
      } 
      // searches the next non-empty group to connect this tail with next head
      for (int type_next=type+1; type_next<NUM_CONTACT_SURFEL_TYPES; type_next++) {
        if (m_heads[type_next]!=NULL) {
          m_tails[type]->m_next = m_heads[type_next];
          m_tail = m_tails[type_next];
          break;
        }
      }
    }
  }
  //Next, extracts from the global map the weights associated with this group and stores them within the group,
  //arranged following the surfels linklist order to it can be traversed sequentially as we loop through surfels
  m_contact_info.clear();
  m_contact_weights.clear();
  sCONTACT_SURFEL_WEIGHTS_INFO info;
  DO_CONTACT_SURFELS_OF_GROUP_PRIMARY(shob_ptr, this) {
    auto it = g_contact_info.find(shob_ptr->id());
    if (it==g_contact_info.end() || (it->second).m_weights.empty()) {
      msg_internal_error("Surfel %d loaded as primary but has no weights info", shob_ptr->id());
    }
    info.m_n_contact_weights = (it->second).m_weights.size();
    m_contact_info.push_back(info);
    m_contact_weights.insert(m_contact_weights.end(), (it->second).m_weights.begin(), (it->second).m_weights.end());
  }
  //Only primary_secondary and secondary need to accumulate fluxes computed as the primaries are traversed.
  //To do so efficiently, accumulation is done within the group in a local array, which also contains the pointers 
  //to the secondaries. Contact weights store the index to the secondary item, so can be retrieved directly when 
  //traversing the weights.
  m_secondary_info.clear();
  sCONTACT_SURFEL_SECONDARY_INFO secondary_item;
  asINT32 n_accumulated = 0;
  DO_CONTACT_SURFELS_OF_GROUP_SECONDARY(shob_ptr, this) {
    id_to_info_index_map[shob_ptr->id()] = m_secondary_info.size();
    push_secondary_item(shob_ptr, n_accumulated);
  }
  //secondary surfels being ghost do not need any accumulation, so can resize the accumulation heat flux vector now
  m_secondary_accumulated_interface_heat.resize(n_accumulated, 0.0);
  //Now that we have loaded the relevant weight_items and secondary_items, loop through the local array of weights 
  //to replace the opposite surfel ids with local secondary array index, which contain the pointer to the surfel, 
  //to access information directly.
  size_t idx_begin = 0, idx_end=0, idx_info=0;
  DO_CONTACT_SURFELS_OF_GROUP_PRIMARY(shob_ptr, this) {
    info = m_contact_info[idx_info++];
    idx_begin = idx_end;
    idx_end += info.m_n_contact_weights;
    for (size_t idx = idx_begin; idx < idx_end; idx++) {
      sCONTACT_WEIGHT_ITEM& item = m_contact_weights[idx];
      SURFEL opposite_surfel = regular_surfel_from_id(item.m_opposite_surfel_id, STP_COND_REALM);
      auto it = id_to_info_index_map.find(item.m_opposite_surfel_id);
      if (it == id_to_info_index_map.end()) {
        if (opposite_surfel->is_ghost()) {
          //pointer to a ghost surfel that lives in another SP, pushed it to the end of secondary items array
          item.m_opposite_info_index = m_secondary_info.size();
          push_secondary_item(opposite_surfel);
          //if ghost resides in a different scale, there should be some accumulation commed
          asINT32 this_scale = this->scale();
          asINT32 ghost_scale = opposite_surfel->scale();
          if (this_scale != ghost_scale) {
            //we can use the group containing the ghost to determine the neighbor SP, but keep in mind that it does not
            //necessary point a the contact comm groups, which needs to be found from the fsets
            sSP_RECV_GROUP_BASE* recv_group = reinterpret_cast<sSP_RECV_GROUP_BASE*>(opposite_surfel->m_group);
            STP_PROC nsp = recv_group->m_source_sp.nsp();
            cNEIGHBOR_SP neighbor_sp(nsp);
            //accumulation gets commed through the finer scale send/recv comm, since SP processing finest scale sends and
            //SP with coarser scale receives, with the key in the global accumulation map being the coarser surfel
            if (this_scale > ghost_scale) {
              //surfel in this SP in a finer scale, computes flux and sends to coarser scale
              auto& accum_map = g_contact_comm_accumulator[nsp][this_scale];
              auto it_accum = accum_map.find(opposite_surfel->id());
              if (it_accum == accum_map.end()) {
                msg_internal_error("Missing accumulator for %d (%d) - %d (%d) contact", 
                                  shob_ptr->id(), this_scale, opposite_surfel->id(), ghost_scale);
              }
              //stores in the contact group the pointer to the accumulator in the send group
              sCONTACT_SEND_GROUP signature;
              signature.m_dest_sp = neighbor_sp;
              signature.m_scale = this_scale;
              sCONTACT_SEND_GROUP* send_group = g_contact_send_fset.find_group(&signature);
              dFLOAT* accum_ptr = &send_group->m_secondary_accumulated_interface_heat[it_accum->second];
              m_secondary_info.back().m_accumulated_heat_flux_idx = m_ghost_accumulated_interface_heat_ptr.size();
              m_ghost_accumulated_interface_heat_ptr.push_back(accum_ptr);
            } else {
              //surfel in this SP in a coarser scale, receives flux from SP processing finer scale
              auto& accum_map = g_contact_comm_accumulator[nsp][ghost_scale];
              auto it_accum = accum_map.find(shob_ptr->id());
              if (it_accum == accum_map.end()) {
                msg_internal_error("Missing accumulator from %d (%d) - %d (%d) contact", 
                                  shob_ptr->id(), this_scale, opposite_surfel->id(), ghost_scale);
              }
              //stores in the recv group the pointer to the accumulator here
              sCONTACT_RECV_GROUP signature;
              signature.m_source_sp = neighbor_sp;
              signature.m_scale = ghost_scale;
              sCONTACT_RECV_GROUP* recv_group = g_contact_recv_fset.find_group(&signature);
              auto it_this = id_to_info_index_map.find(shob_ptr->id());
              if (it_this == id_to_info_index_map.end()) {
                msg_internal_error("Primary interacting with ghost surfel not tagged as primary_secondary");
              }
              dFLOAT* accum_ptr = &this->m_secondary_accumulated_interface_heat[it_this->second];
              recv_group->m_secondary_accumulated_interface_heat_ptr[it_accum->second] = accum_ptr;
              if (shob_ptr->is_conduction_shell()) { 
                //adds pointers to the shell accumulators, so recv groups do not need to know any surfel info, just move
                //data to the acumulator
                asINT32 ii_last = it_accum->second + shob_ptr->shell_conduction_data()->num_layers();
                for (size_t ii = it_accum->second + 1; ii <= ii_last; ii++) {
                  accum_ptr++;
                  recv_group->m_secondary_accumulated_interface_heat_ptr[ii] = accum_ptr;
                }
              }
            }
          }
        } else if (opposite_surfel->is_fringe2() && opposite_surfel->m_group != this) {
          //It might happen that after promotions to fringe 2, the primary is in a coarser scale than the secondary, in
          //which case we kept the original weight and added the flipped one. This ensures that the promoted secondary
          //is processed in the time timesteps when the primary scale is inactive, despite duplicating the interaction.
          item.m_opposite_info_index = m_secondary_info.size();
          sCONTACT_SURFEL_SECONDARY_INFO secondary_info_item;
          secondary_info_item.m_surfel = opposite_surfel;
          //Since the secondary has not been loaded yet, we don't know the accumulated index yet. We defer filling it to
          //later by storing the index within the secondaries in the group_to_deferred_info_map
          m_secondary_info.push_back(secondary_info_item);
          deferred_info_map[this].push_back(item.m_opposite_info_index);
        } else {
          msg_internal_error("Contact with surfel %d in same SP not loaded yet", item.m_opposite_surfel_id);
        }
      } else if (opposite_surfel->m_group != this) {
        //it is a surfel in the same SP, but in a different scale, creates a secondary item within the group to retrieve its info
        //properly (pointer, opposite info index)
        item.m_opposite_info_index = m_secondary_info.size();
        sCONTACT_SURFEL_SECONDARY_INFO secondary_info_item;
        secondary_info_item.m_surfel = opposite_surfel;
        //actual secondary info is in the surfel group that contains the surfel, so store here just the index of the secondary item within that group
        secondary_info_item.m_accumulated_heat_flux_idx = it->second;
        m_secondary_info.push_back(secondary_info_item);
      } else {
        item.m_opposite_info_index = it->second;
      }
    }
  }
}

VOID initialize_shob_groups()
{

  // "has ghosts" here  really means "is commed", so vrfine fringe types are designated as not having ghosts
  g_ublk_type_has_ghosts[FRINGE_FARBLK_GROUP_TYPE] = TRUE;
  g_ublk_type_has_ghosts[FRINGE_NEARBLK_GROUP_TYPE] = TRUE;
  g_ublk_type_has_ghosts[FRINGE2_FARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[FRINGE2_NEARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[INTERIOR_FARBLK2_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[INTERIOR_FARBLK1_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[INTERIOR_NEARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[VRFINE_FRINGE_NEARBLK_GROUP_TYPE] = TRUE;
  g_ublk_type_has_ghosts[VRFINE_FRINGE_FARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[VRFINE_FARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[VRFINE_NEARBLK_GROUP_TYPE] = FALSE;
  g_ublk_type_has_ghosts[MIRROR_UBLK_GROUP_TYPE] = FALSE;

  ccDOTIMES(ublk_group_type, N_UBLK_GROUP_TYPES) {
    g_ublk_groups[ublk_group_type] = xnew sUBLK_FSET;
  }

#if BUILD_GPU
  ccDOTIMES(ublk_group_type, N_UBLK_GROUP_TYPES) {
    g_mblk_groups[ublk_group_type] = xnew sMBLK_FSET;
  }
#endif

  ccDOTIMES(surfel_base_group_type, N_SURFEL_BASE_GROUP_TYPES) {
    g_surfel_base_groups[surfel_base_group_type] = xnew sSURFEL_BASE_FSET;
  }

  ccDOTIMES(surfel_group_type, N_SURFEL_GROUP_TYPES) {
    g_surfel_groups[surfel_group_type] = xnew sSURFEL_FSET;
  }

#if BUILD_GPU
  ccDOTIMES(surfel_group_type, N_SURFEL_GROUP_TYPES) {
    g_msfl_groups[surfel_group_type] = xnew sMSFL_FSET;
  }
#endif

  ccDOTIMES(sampling_surfel_group_type, N_SAMPLING_SURFEL_GROUP_TYPES) {
    g_sampling_surfel_groups[sampling_surfel_group_type] = xnew sSAMPLING_SURFEL_FSET;
  }

  ccDOTIMES(surfel_pair_group_type, N_SURFEL_PAIR_GROUP_TYPES) {
    g_surfel_pair_groups[surfel_pair_group_type] = xnew sSURFEL_PAIR_FSET;
  }

  ccDOTIMES(contact_surfel_group_type, N_CONTACT_SURFEL_GROUP_TYPES) {
    g_contact_surfel_groups[contact_surfel_group_type] = xnew sCONTACT_SURFEL_FSET;
  }

  ccDOTIMES(bsurfel_group_type, N_BSURFEL_GROUP_TYPES) {
    g_bsurfel_groups[bsurfel_group_type] = xnew sBSURFEL_FSET;
  }

  g_solid_vr_fine_group = xnew sUBLK_FSET;

}

VOID assign_dangling_send_groups_to_surfel_groups(STP_REALM realm) {
  //Since a surfel can have multiple ghosts, it can be linked to multiple send groups. However, the surfel group
  //owning the surfel only keeps a pointer to one of the send groups, leading to the possibility of leaving dangling
  //send groups. Those dangling send groups are assigned here to empty dyn surfel groups. The goal of these empty 
  //groups goal is to let the send group know that the surfel has been processed, so their supertype is not relevant 
  //as long as it is processed. Therefore, they are assigned to <REALM>_DYN_SURFEL_GROUP_SUPERTYPE as the base supertype
  ccDOTIMES(scale,sim.num_scales) {
    FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_SEND_FSET, g_surfel_send_fset, send_group, scale) {
      BOOLEAN found_send_group = FALSE;
      DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group,scale, FRINGE_SURFEL_BASE_GROUP_TYPE) {
        if (group->m_send_group == send_group) {
          found_send_group = TRUE;
          break;
        }
      }
      if (!found_send_group) {
        cNEIGHBOR_SP dest_sp = send_group->m_dest_sp;
        SURFEL_BASE_FSET surfel_fset = g_surfel_base_groups[FRINGE_SURFEL_BASE_GROUP_TYPE];
        SURFEL_GROUP_SUPERTYPE supertype = (realm == STP_FLOW_REALM) 
                                           ? FLOW_DYN_SURFEL_GROUP_SUPERTYPE 
                                           : CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE;
        SURFEL_GROUP surfel_group = static_cast<SURFEL_GROUP>(surfel_fset->create_group(scale, dest_sp, supertype, realm));
        surfel_group->m_send_group = send_group;
      }
    }
  }
}

VOID initialize_surfel_group_fsets()
{
  //As we loop through the types and scales, some base groups can be removed if empty. However, to avoid 
  //conflicts with the iterator, store the groups to be removed in a local vector to remove them later.
  std::vector<sSURFEL_BASE_FSET::GROUP> empty_groups;
  empty_groups.clear();
  ccDOTIMES(base_type, N_SURFEL_BASE_GROUP_TYPES) {
    BOOLEAN is_fringe = (base_type == FRINGE_SURFEL_BASE_GROUP_TYPE);
    BOOLEAN is_fringe2 = (base_type == FRINGE2_SURFEL_BASE_GROUP_TYPE);
    ccDOTIMES(scale,sim.num_scales) {
      DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group,scale, base_type) {
        //NOTE: 
        //If the group has no surfels neither a valid send_group, it must have been an interior group filled 
        //in the first call to this method after parsing and whose surfels have been promoted. Therefore, it is
        //removed here rather than adding it since it is not used any more.
        //NOTE2: 
        //m_send_group pointers are removed after parsing in initialize_send_channel(),
        //so we skip removal of any FRINGE group even if empty, as nothing in the fringe should be moved elsewhere
        switch(group->m_supertype) {
        case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
        case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE:
          {
            BOOLEAN is_flow = (group->m_supertype == FLOW_DYN_SURFEL_GROUP_SUPERTYPE);
            SURFEL_GROUP_TYPE gtype;
            if (is_fringe)
              gtype = (is_flow) ? FRINGE_FLOW_DYN_SURFEL_GROUP_TYPE : FRINGE_CONDUCTION_DYN_SURFEL_GROUP_TYPE;
            else if (is_fringe2)
              gtype = (is_flow) ? FRINGE2_FLOW_DYN_SURFEL_GROUP_TYPE : FRINGE2_CONDUCTION_DYN_SURFEL_GROUP_TYPE;
            else
              gtype = (is_flow) ? INTERIOR_FLOW_DYN_SURFEL_GROUP_TYPE : INTERIOR_CONDUCTION_DYN_SURFEL_GROUP_TYPE;

            SURFEL_FSET surfel_fset = g_surfel_groups[gtype];
            SURFEL_GROUP surfel_group = static_cast<SURFEL_GROUP>(group);
            surfel_fset->add_group(surfel_group);

            break;
          }
        case SAMPLING_SURFEL_GROUP_SUPERTYPE:
          {
            SAMPLING_SURFEL_GROUP_TYPE sgtype;
            if (is_fringe)
              sgtype = FRINGE_SAMPLING_SURFEL_GROUP_TYPE;
            else if (is_fringe2)
              sgtype = FRINGE2_SAMPLING_SURFEL_GROUP_TYPE; 
            else
              sgtype = INTERIOR_SAMPLING_SURFEL_GROUP_TYPE;

            SAMPLING_SURFEL_FSET sampling_surfel_fset = g_sampling_surfel_groups[sgtype]; 
            SAMPLING_SURFEL_GROUP sampling_surfel_group = static_cast<SAMPLING_SURFEL_GROUP>(group);
            if (sampling_surfel_group->n_shob_ptrs() > 0 || is_fringe) {
              sampling_surfel_fset->add_group(sampling_surfel_group);
            } else {
              sampling_surfel_fset->delete_group(sampling_surfel_group);
              empty_groups.push_back(group);
            }
            break;
          }
        case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
          {
            SURFEL_PAIR_GROUP_TYPE slrf_pgtype;
            if (is_fringe)
              slrf_pgtype = FRINGE_SLRF_SURFEL_PAIR_GROUP_TYPE;
            else if (is_fringe2)
              slrf_pgtype = FRINGE2_SLRF_SURFEL_PAIR_GROUP_TYPE;
            else
              slrf_pgtype = INTERIOR_SLRF_SURFEL_PAIR_GROUP_TYPE;

            SURFEL_PAIR_FSET slrf_surfel_pair_fset = g_surfel_pair_groups[slrf_pgtype];
            SURFEL_PAIR_GROUP slrf_surfel_pair_group = static_cast<SURFEL_PAIR_GROUP>(group);
            if (slrf_surfel_pair_group->n_shob_ptrs() > 0 || is_fringe) {
              slrf_surfel_pair_fset->add_group(slrf_surfel_pair_group);
            } else {
              slrf_surfel_pair_fset->delete_group(slrf_surfel_pair_group);
              empty_groups.push_back(group);
            }
            break;
          }
        case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
          {
            SURFEL_PAIR_GROUP_TYPE apm_pgtype;
            if (is_fringe)
              apm_pgtype = FRINGE_APM_SURFEL_PAIR_GROUP_TYPE;
            else if (is_fringe2)
              apm_pgtype = FRINGE2_APM_SURFEL_PAIR_GROUP_TYPE;
            else
              apm_pgtype = INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE;

            SURFEL_PAIR_FSET apm_surfel_pair_fset = g_surfel_pair_groups[apm_pgtype];
            SURFEL_PAIR_GROUP apm_surfel_pair_group = static_cast<SURFEL_PAIR_GROUP>(group);
            if (apm_surfel_pair_group->n_shob_ptrs() > 0 || is_fringe) {
              apm_surfel_pair_fset->add_group(apm_surfel_pair_group);
            } else {
              apm_surfel_pair_fset->delete_group(apm_surfel_pair_group);
              empty_groups.push_back(group);
            }
            break;
          }
        case FLOW_WSURFEL_GROUP_SUPERTYPE:
        case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
          {
            BOOLEAN is_flow = (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE);
            SURFEL_GROUP_TYPE gtype;
            if (is_fringe)
              gtype = (is_flow) ? FRINGE_FLOW_WSURFEL_GROUP_TYPE: FRINGE_CONDUCTION_WSURFEL_GROUP_TYPE;
            else if (is_fringe2)
              gtype = (is_flow) ? FRINGE2_FLOW_WSURFEL_GROUP_TYPE : FRINGE2_CONDUCTION_WSURFEL_GROUP_TYPE;
            else
              gtype = (is_flow) ? INTERIOR_FLOW_WSURFEL_GROUP_TYPE : INTERIOR_CONDUCTION_WSURFEL_GROUP_TYPE;

            SURFEL_FSET wsurfel_fset = g_surfel_groups[gtype];
            SURFEL_GROUP wsurfel_group = static_cast<SURFEL_GROUP>(group);
            if (wsurfel_group->n_shob_ptrs() > 0 || is_fringe) {
              wsurfel_fset->add_group(wsurfel_group);
            } else {
              wsurfel_fset->delete_group(wsurfel_group);
              empty_groups.push_back(group);
            }
            break;
          }
        case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
        case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
          {
            BOOLEAN is_global = (group->m_supertype == CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE);
            CONTACT_SURFEL_GROUP_TYPE cs_pgtype;
            if (is_fringe)
              cs_pgtype = (is_global) ? FRINGE_CONTACT_SURFEL_GLOBAL_GROUP_TYPE : FRINGE_CONTACT_SURFEL_LOCAL_GROUP_TYPE;
            else if (is_fringe2)
              cs_pgtype = (is_global) ? FRINGE2_CONTACT_SURFEL_GLOBAL_GROUP_TYPE : FRINGE2_CONTACT_SURFEL_LOCAL_GROUP_TYPE;
            else
              cs_pgtype = (is_global) ? INTERIOR_CONTACT_SURFEL_GLOBAL_GROUP_TYPE : INTERIOR_CONTACT_SURFEL_LOCAL_GROUP_TYPE;
            CONTACT_SURFEL_FSET contact_surfel_fset = g_contact_surfel_groups[cs_pgtype];
            CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
            if (contact_surfel_group->n_shob_ptrs() > 0 || is_fringe) {
              contact_surfel_fset->add_group(contact_surfel_group);
            } else {
              contact_surfel_fset->delete_group(contact_surfel_group);
              empty_groups.push_back(group);
            }
            break;
          }
        case MLRF_SURFEL_GROUP_SUPERTYPE:
          break;
        default:
          msg_internal_error("Unexpected base surfel group type %d",group->m_supertype);
          break;
        }
      }
    }
    //removes all the empty groups of this scale and clears the vector to leave it ready for next base_type
    for (auto& group : empty_groups) {
      g_surfel_base_groups[base_type]->delete_group(group);
    }
    empty_groups.clear();
  }
}

// If sliding mesh surfels are present, request for sending surfel to the
// neighboring SPs must be initiating by the sliding mesh strand (mass
// correction). Hence, the send groups for all other surfel groups should
// be set to NULL.

// See comment to add_surfel_groups_of_scale_to_send_queue in strand_mgr.cc
// for further discussion of this procedure.

VOID disable_surfel_send_requests_by_surfel_groups() {

  if (is_sliding_mesh_present()) {
    ccDOTIMES(base_type, N_SURFEL_BASE_GROUP_TYPES) {
      ccDOTIMES(scale,sim.num_scales) {
        DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group,scale, base_type) {
          group->m_send_group = NULL;
        }
      }
    }
  }
}

template<>
SP_TIMER_TYPE sUBLK_GROUP::get_fluid_dyn_timer()
{
  UBLK ublk = this->shob_ptrs();
  if (ublk)
    return (ublk->get_fluid_dyn_timer()); 
  else
    return SP_INVALID_TIMER;
}

sSURFEL_PAIR* allocate_surfel_pair(sSURFEL *int_surfel, sSURFEL *ext_surfel,
#if BUILD_D39_LATTICE
                                   dFLOAT inv_total_pgram_volume_d39,
                                   dFLOAT inv_total_pgram_volume_d19,
#endif
                                   dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                                   SURFEL_PAIR_GROUP_TYPE surfel_pair_type,
                                   STP_PHYSTYPE_TYPE sim_phys_type) {
#if !BUILD_5G_LATTICE
  size_t n_bytes = sSURFEL_PAIR::size(surfel_pair_type, sim_phys_type) + 16;
  sSURFEL_PAIR *surfel_pair_ptr = (sSURFEL_PAIR *) malloc(n_bytes);
  surfel_pair_ptr = (sSURFEL_PAIR *) ((((size_t) surfel_pair_ptr) + 15) & ~15);
  memset(surfel_pair_ptr, 0, n_bytes);
  //surfel_ptr->m_surfel_type = surfel_type;
  surfel_pair_ptr->init(int_surfel, ext_surfel,
#if BUILD_D39_LATTICE
                        inv_total_pgram_volume_d39,
                        inv_total_pgram_volume_d19,
#endif
                        sum_pgram_volumes);
  return surfel_pair_ptr;
#else
  return NULL;
#endif
}

VOID print_shob_group_info() {

  msg_print("Ublk groups");
  sUBLK_FSET::ITERATOR fset_it;
  sUBLK_FSET::ITERATOR fset_end;
  ccDOTIMES(utype, N_UBLK_GROUP_TYPES) {
    UBLK_FSET ufset = g_ublk_groups[utype];
    msg_print("Type %d: n_groups = %d", utype, ufset->n_groups());
    if(!(ufset->groups_per_scale())) {
      //msg_print(" No groups of type %d", utype);
    } else {
      ccDOTIMES(scale, sim.num_scales) {
        asINT32 n_groups = ufset->n_groups_of_scale(scale);
        msg_print("  scale %d: n_groups = %d", scale, n_groups);
        fset_it = ufset->groups_of_scale(scale).begin();
        fset_end =  ufset->groups_of_scale(scale).end();
        asINT32 groupnum = 0;
        while(fset_it != fset_end) {
          UBLK_GROUP group = *fset_it;
          msg_print("    group %d: n_shobs = %d", groupnum, group->n_shob_ptrs());
          groupnum++;
          fset_it++;
        }
      }
    }
  }
}

//#define DEBUG_SHOB_CLASSIFICATION
static void mark_fringe_vr_fine_neighbor_ublks_to_be_fringe(UBLK ublk)
{
  // Find neighbors of fringe vr fine ublks
  const auto& box_access = ublk->m_box_access;
  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();

  ccDOTIMES (latvec, N_MOVING_STATES) {
     sINT16 u_offsets[3] = {0};
     u_offsets[0] = state_vx(latvec) == 0 ? 0 : (state_vx(latvec) < 0 ? -1 : 1);
     u_offsets[1] = state_vy(latvec) == 0 ? 0 : (state_vy(latvec) < 0 ? -1 : 1);
     u_offsets[2] = state_vz(latvec) == 0 ? 0 : (state_vz(latvec) < 0 ? -1 : 1);
     
     if (sim.num_dims == 2 && u_offsets[2] != 0)
       continue;

     TAGGED_UBLK neighbor_tagged_ublk = box_access.neighbor_ublk(u_offsets);
     if (!neighbor_tagged_ublk.is_ublk())
       continue;
     if (neighbor_tagged_ublk.is_ublk_scale_interface()) {
       SCALE_BOX_INTERFACE interface = neighbor_tagged_ublk.interface();
       ccDOTIMES(neighbor_voxel, ubFLOAT::N_VOXELS) {
         TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[neighbor_voxel];
         if (fine_tagged_ublk.is_ublk_split()) {
           sUBLK_VECTOR *fine_split_ublks = fine_tagged_ublk.split_ublks();
           ccDOTIMES(i, fine_split_ublks->num_split_ublks()) {
             UBLK fine_neighbor_split_ublk = fine_split_ublks->m_tagged_ublks[i].ublk();
             if (fine_neighbor_split_ublk == NULL)
               continue;
             if (!fine_neighbor_split_ublk->is_fringe() && !fine_neighbor_split_ublk->is_ghost() && !fine_neighbor_split_ublk->is_mirror()) {
               fine_neighbor_split_ublk->set_tobefringe();
             }
           }
         } else if (fine_tagged_ublk.is_ublk_scale_interface()) {
#if !BUILD_D39_LATTICE
           msg_internal_error("VR fine ublk %d neighbor offset %d %d %d should not have nesting scale interface.", ublk->id(),
                              u_offsets[0], u_offsets[1], u_offsets[2]);
#endif
         } else if (fine_tagged_ublk.is_ublk()) {
           UBLK fine_ublk = fine_tagged_ublk.ublk();
           if (!fine_ublk->is_fringe() && !fine_ublk->is_ghost() && !fine_ublk->is_mirror()) {
               fine_ublk->set_tobefringe();
           }
         }
       }
     }
     if (neighbor_tagged_ublk.is_ublk_split()) {
       // Find all split instances
       sUBLK_VECTOR *split_ublks = neighbor_tagged_ublk.split_ublks();
       ccDOTIMES(i, split_ublks->num_split_ublks()) {
         UBLK neighbor_split_ublk = split_ublks->m_tagged_ublks[i].ublk();
         if (!neighbor_split_ublk->is_fringe() && !neighbor_split_ublk->is_ghost() && !neighbor_split_ublk->is_mirror()) {
           neighbor_split_ublk->set_tobefringe();
           // If this neighbor ublk is vr fine, mark the vr coarse and all vr fine ublks to be fringe
           if (neighbor_split_ublk->is_vr_fine()) {
             sVR_FINE_INTERFACE_DATA *vr_fine_data = neighbor_split_ublk->vr_fine_data();
             UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
             coarse_ublk->set_tobefringe();
             sVR_COARSE_INTERFACE_DATA *vr_coarse_data = coarse_ublk->vr_coarse_data();
             ccDOTIMES(i, ubFLOAT::N_VOXELS) {
               UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
               if (fine_ublk == NULL)
                 continue;
               fine_ublk->set_tobefringe();
             }
           }
           // If this neighbor ublk is vr coarse, mark all vr fine ublks to be fringe
           if (neighbor_split_ublk->is_vr_coarse()) {
             sVR_COARSE_INTERFACE_DATA *vr_coarse_data = neighbor_split_ublk->vr_coarse_data();
             ccDOTIMES(i, ubFLOAT::N_VOXELS) {
               UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
               if (fine_ublk == NULL)
                 continue;
               fine_ublk->set_tobefringe();
             }
           }
         }
       }
     } else {
       UBLK neighbor_ublk = neighbor_tagged_ublk.ublk();
       if (!neighbor_ublk->is_solid() && !neighbor_ublk->is_fringe() && !neighbor_ublk->is_ghost() && !neighbor_ublk->is_mirror()) {
         neighbor_ublk->set_tobefringe();
         // If this neighbor ublk is vr fine, mark the vr coarse and all vr fine ublks to be fringe
         if (neighbor_ublk->is_vr_fine()) {
           sVR_FINE_INTERFACE_DATA *vr_fine_data = neighbor_ublk->vr_fine_data();
           UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
           coarse_ublk->set_tobefringe();
           sVR_COARSE_INTERFACE_DATA *vr_coarse_data = coarse_ublk->vr_coarse_data();
           ccDOTIMES(i, ubFLOAT::N_VOXELS) {
             UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
             if (fine_ublk == NULL)
               continue;
             fine_ublk->set_tobefringe();
           }
         } else if (neighbor_ublk->is_vr_coarse()) {
           sVR_COARSE_INTERFACE_DATA *vr_coarse_data = neighbor_ublk->vr_coarse_data();
           ccDOTIMES(i, ubFLOAT::N_VOXELS) {
             UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
             if (fine_ublk == NULL)
               continue;
             fine_ublk->set_tobefringe();
           }
         }
       }
     }
  }
}

template <typename sSHOB_TYPE>
static void remove_shob_from_group(sSHOB_TYPE* shob)
{
  shob->m_group->remove_shob_from_group(shob);
}

VOID move_ublk_to_nearblk_group(UBLK ublk, UBLK_GROUP_TYPE group_type)
{
  // Some ublks (solid ones) do not belong to any group
  if (!ublk->m_group)
    return;

  if (!ublk->is_fringe() && !ublk->is_ghost()) {
    // Change the ublk to fringe2
    ublk->m_group->remove_shob_from_group(ublk);

    UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];
    // There is only one group per scale for fringe2 and sliding nearblks
    UBLK_GROUP ugroup = ublk_group_fset->create_group(ublk->scale(),cNEIGHBOR_SP(0));
#ifdef DEBUG_SHOB_CLASSIFICATION
    msg_print("Move ublk %d to nearblk group %s", ublk->id(), group_type == FRINGE2_NEARBLK_GROUP_TYPE? "fringe2" : 
                                                                 group_type == SLIDING_NEARBLK_GROUP_TYPE? "sliding" : "unknown");
#endif
    ugroup->add_shob_to_group(ublk);
    ublk->set_group(ugroup);
  }
}

// Helper functions for check_ublk_neighbors()
static BOOLEAN is_ublk_ghost(UBLK ublk)
{
  return ublk->is_ghost();
}

static BOOLEAN is_ublk_fringe(UBLK ublk)
{
  return ublk->is_fringe();
}

// NOTE that this function will check all neighbors no matter whether they are 
// connected or not. This is because gather_advect_for_unsplit_neighbors() does
// not check connectivity, and we need to make the logic consistent when promoting
// shobs, otherwise some interior ublks may not be promoted properly. When processing
// those interior ublks, on-demand explode will be triggered for some fringe vr fine 
// ublks. However the explode will depend on the ghost ublk data which may or may not 
// arrived, leading to repeatability issues. Thus we need to promote those interior ublks
// to fringe even if they are not connected to the fringe vr fine.
static BOOLEAN check_if_any_ublk_neighbor_satisfies_func(UBLK ublk, BOOLEAN (*func)(UBLK))
{
  const auto& box_access = ublk->m_box_access;
  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();
  
  VOXEL_MASK_8 voxel_mask = ublk->fluid_like_voxel_mask;
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    if (!voxel_mask.test(voxel)) {
      continue;
    }
    // find all the neighbors
    TAGGED_UBLK neighbors[N_MOVING_STATES]; // constructor sets m_ublk to NULL
    if (sim.num_dims == 2)
      voxel_neighbors_along_state_directions<TRUE, TRUE>(ublk, voxel, neighbors, box_access, tagged_ublk, FALSE);
    else
      voxel_neighbors_along_state_directions<TRUE, FALSE>(ublk, voxel, neighbors, box_access, tagged_ublk, FALSE);

    ccDOTIMES(latvec, N_MOVING_STATES) {
      if (neighbors[latvec].is_ublk_split()) {
        // Find all split instances
        sUBLK_VECTOR *split_ublks = neighbors[latvec].split_ublks();
        ccDOTIMES(i, split_ublks->num_split_ublks()) {
          UBLK neighbor_split_ublk = split_ublks->m_tagged_ublks[i].ublk();
          if ((*func)(neighbor_split_ublk)) {
            //msg_print("split neighbor ublk %d is fringe", neighbor_split_ublk->id());
            return TRUE;
          }
        }
      } else if (neighbors[latvec].is_ublk_scale_interface()) {
        asINT32 neighbor_voxel = neighbor_voxel_along_state_direction(voxel, latvec);
        SCALE_BOX_INTERFACE interface = neighbors[latvec].interface();
        TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[neighbor_voxel];
        if (fine_tagged_ublk.is_ublk_split()) {
          sUBLK_VECTOR *fine_split_ublks = fine_tagged_ublk.split_ublks();
          ccDOTIMES(i, fine_split_ublks->num_split_ublks()) {
            UBLK fine_neighbor_split_ublk = fine_split_ublks->m_tagged_ublks[i].ublk();
            if ((*func)(fine_neighbor_split_ublk)) {
              //msg_print("fine neighbor split ublk %d is fringe", fine_neighbor_split_ublk->id());
              return TRUE;
            }
          }
        } else if (fine_tagged_ublk.is_ublk_scale_interface()) {
#if BUILD_D39_LATTICE
          return FALSE;
#else
          msg_internal_error("Ublk cannot be an interface");
#endif
        } else if (fine_tagged_ublk.is_ublk()) {
          if ((*func)(fine_tagged_ublk.ublk())) {
            //msg_print("neighbor fine ublk %d is fringe", fine_tagged_ublk.ublk()->id());
            return TRUE;
          }
        }
      } else if (neighbors[latvec].is_ublk()) {
        UBLK neighbor_ublk = neighbors[latvec].ublk();
        if ((*func)(neighbor_ublk)) {
          //msg_print("neighbor ublk %d is fringe", neighbor_ublk->id());
          return TRUE;
        }
      }
    }
  }
  return FALSE;
}

static BOOLEAN is_ublk_valid_for_interior_farblk_1(UBLK ublk)
{
  //We have a stringent requirement for a block two be two copied
  //because of following reasons
  // a) The strand dependencies dictate that certain neighbors cannot be modified by swap advection
  // b) Some blocks require additional work and add overhead to simple swapping, example :
  //    - vr fine would require computing vrf_pas_factors
  //    - near blocks have S2V contributions
  // c) Some blocks like split blocks expect their tagged neighbors to have two copied states

  auto test_nbr = [](sUBLK* ublk)->BOOLEAN {return  ( ublk->is_ghost() ||
                                                      ublk->is_vr_fine()||
                                                      ublk->is_vr_coarse()||             
                                                      ublk->is_split() ||
                                                      ublk->is_near_surface())
                                                      ;};

  auto test_ublk = [&](sUBLK* ublk)->BOOLEAN {return test_nbr(ublk) ||
					             ublk->is_fringe() ||					             
					             ublk->m_are_any_neighbors_split.any() ||
					             ublk->has_mirror() ||
					             ublk->advect_from_split_ublk()
#if BUILD_D39_LATTICE     // For D39, neighbors of sliding nearblks cannot be interior_1 ublks.   
                                                      || ublk->is_mlrf_surfel_interacting()                                                      
#endif					      
					             ;};
  
  return !test_ublk(ublk) && !check_if_any_ublk_neighbor_satisfies_func(ublk,test_nbr);
}

static BOOLEAN is_ublk_neighbor_ghost(UBLK ublk)
{
  return check_if_any_ublk_neighbor_satisfies_func(ublk, is_ublk_ghost);
}

static BOOLEAN does_surfel_interact_with_fringe_or_fringe2(SURFEL surfel)
{
  BOOLEAN is_fringe2 = FALSE;
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  // Check if interacting with fringe or fringe2 ublks
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (ublk->is_fringe() || ublk->is_fringe2() || ublk->is_mlrf_surfel_interacting()) {
      return TRUE;
    }
  }

  // Check if interacting with fringe surfels
  if (surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    for (asINT32 is = 0; is < n_src_surfels; is++) {
      sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
      SURFEL src_surfel = tagged_src_surfel.src_surfel();
      if (src_surfel->is_fringe()) {
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Interior surfel %d is promoted to be fringe2 since it interacts with fringe surfels", surfel->id());
#endif
        return TRUE;
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
    }
  }
  return is_fringe2;
}

template <typename SURFEL_TYPE>
static BOOLEAN does_surfel_interact_with_ghost(SURFEL_TYPE surfel)
{
  BOOLEAN is_fringe = FALSE;
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  // Check if interacting with ghost ublks
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (ublk->is_ghost()) {
      return TRUE;
    }
  }

  // Check if interacting with ghost surfels
  if (surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    for (asINT32 is = 0; is < n_src_surfels; is++) {
      sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
      SURFEL src_surfel = tagged_src_surfel.src_surfel();
      if (src_surfel->is_ghost()) {
        return TRUE;
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
    }
  }
  return is_fringe;
}

static BOOLEAN check_if_surfel_satisfies_func(SURFEL surfel, BOOLEAN (*func)(SURFEL))
{
  BOOLEAN is_satisfied = func(surfel);
  // Need to check mirror surfels since if the real surfel does not but the mirror surfel does satisfy the conditions, we need to promote the real surfel
  if (!is_satisfied && surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      if (func(mirror_surfel))
        is_satisfied = TRUE;
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
  return is_satisfied;
}

static BOOLEAN does_sampling_surfel_interact_with_fringe_or_fringe2(SAMPLING_SURFEL sampling_surfel)
{
  BOOLEAN is_fringe2 = FALSE;
  SURFEL_UBLK_INTERACTION ublk_interaction = sampling_surfel->m_ublk_interactions;
  asINT32 n_ublks                          = sampling_surfel->m_n_ublk_interactions;

  // Check if interacting with fringe or fringe2 ublks
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (ublk->is_fringe() || ublk->is_fringe2() || ublk->is_mlrf_surfel_interacting()) {
      return TRUE;
    }
  }
  // Check if interacting with fringe surfels
  if (sampling_surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = sampling_surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    for (asINT32 is = 0; is < n_src_surfels; is++) {
      sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
      SURFEL src_surfel = tagged_src_surfel.src_surfel();
      if (src_surfel->is_fringe()) {
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Interior surfel %d is promoted to be fringe2 since it interacts with fringe surfels", src_surfel->id());
#endif
        return TRUE;
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
    }
  }

  return is_fringe2;
}

static BOOLEAN check_if_sampling_surfel_satisfies_func(SAMPLING_SURFEL sampling_surfel, BOOLEAN (*func)(SAMPLING_SURFEL))
{
  BOOLEAN is_satisfied = func(sampling_surfel);
  /* sampling surfels do not have mirror data
  // Need to check mirror surfels since if the real surfel does not but the mirror surfel does satisfy the conditions, we need to promote the real surfel
  if (!is_satisfied && sampling_surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = sampling_surfel->mirror_data();
    while (mirror_data) {
      SAMPLING_SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      if (func(mirror_surfel))
        is_satisfied = TRUE;
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
  */
  return is_satisfied;
}

static BOOLEAN check_if_surfel_pair_satisfies_func(SURFEL_PAIR surfel_pair, BOOLEAN (*func)(SURFEL))
{
  BOOLEAN is_satisfied = FALSE;
  ccDOTIMES(s, 2) {
    SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
    if (func(surfel)) {
      is_satisfied = TRUE;
      break;
    }
    if (surfel->has_mirror()) {
      sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
      while (mirror_data) {
        SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
        if (func(mirror_surfel)) {
          is_satisfied = TRUE;
          break;
        }
        mirror_data = mirror_data->m_next_mirror_data;
      }
    }
    if (is_satisfied)
      break;
  }
  return is_satisfied;
}

VOID promote_fringe_vr_fine_neighbor_ublks_to_fringe()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        mark_fringe_vr_fine_neighbor_ublks_to_be_fringe(ublk);
      }
    }

    // Neighbors of VR fine Fringe ublks are promoted to be fringe ublks. Because explode on
    // demand can be invoked by neighboring ublks and explode of fringe VR ublks needs ghost data.
    // Also, VR coarse parent and VR fine siblings are promoted to be fringe, since they should 
    // be processed by the same strand. 
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE_FARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        mark_fringe_vr_fine_neighbor_ublks_to_be_fringe(ublk);
      }
    }
  }
  ccDOTIMES(realm, STP_N_REALMS) {
    ccDOTIMES(i, g_ublk_table[realm].n_ublks()) {
      UBLK ublk = g_ublk_table[realm].ublk(i);
      // Some ublks (e.g. solid ones) don't belong to any group, ignore them here
      if (ublk->is_solid())
        continue;
      if (ublk->is_tobefringe()) {
        ublk->m_group->remove_shob_from_group(ublk);
        UBLK_GROUP_TYPE group_type;
        if (ublk->is_vr_fine()) {
          if (ublk->is_near_surface())
            group_type = VRFINE_FRINGE_NEARBLK_GROUP_TYPE;
          else
            group_type = VRFINE_FRINGE_FARBLK_GROUP_TYPE;
        } else {
          if (ublk->is_near_surface())
            group_type = FRINGE_NEARBLK_GROUP_TYPE;
          else
            group_type = FRINGE_FARBLK_GROUP_TYPE;
        }
        UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];
        // Note that these promoted fringe ublks are not commed and thus they are not in any send/receive groups

        // Based on the following logic:
        //
        // A non-ghosted ublk of a ghosted type goes in the last group of that type and scale,
        // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted ublk of a non-ghosted goes in the only
        // group of that type and scale, with dest_sp = 0.
        // STP_PROC dest_sp = (g_ublk_type_has_ghosts[group_type]) ? DEST_SP_PROCESS_LAST : 0;
        //
        // dest_sp should be DEST_SP_PROCESS_LAST here
        UBLK_GROUP ugroup = ublk_group_fset->create_group(ublk->scale(), cNEIGHBOR_SP(DEST_SP_PROCESS_LAST));
        ugroup->add_shob_to_group(ublk);
        ublk->set_group(ugroup);
        ublk->set_fringe(); // the fringe flag is used later when setting fringe2 nearblks
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Move ublk %d to fringe group %d", ublk->id(), group_type);
#endif
      }
    }

    ccDOTIMES(i, g_ublk_table[realm].n_ublks()) {
      UBLK ublk = g_ublk_table[realm].ublk(i);
      if (ublk->is_tobefringe())
        ublk->unset_tobefringe();
    }
  }
}


template <SURFEL_BASE_GROUP_TYPE BASE_GROUP_TYPE>
inline void move_contact_surfel(SURFEL surfel,
                                SURFEL& next_surfel,
                                CONTACT_SURFEL_GROUP contact_surfel_group, 
                                CONTACT_SURFEL_GROUP new_contact_surfel_group, 
                                CONTACT_SURFEL_TYPE contact_surfel_type) {
  //determines the opp surfela and next surfel before removing surfels from group and invalidate the m_next pointers
  SURFEL opp_surfel;
  if (contact_surfel_type == CONTACT_SURFEL_PAIRED) {
    opp_surfel = surfel->m_next;
    if (next_surfel == opp_surfel || next_surfel == surfel) next_surfel = opp_surfel->m_next;
  } else {
    opp_surfel = nullptr;
    if (next_surfel == surfel) next_surfel = surfel->m_next;
  }
  //remove surfel(s) from group
  contact_surfel_group->remove_surfel_from_group(surfel, contact_surfel_type);
  //adds surfel to the new group
  new_contact_surfel_group->add_surfel_to_group(surfel, contact_surfel_type);
  surfel->set_group(new_contact_surfel_group);
  if constexpr (BASE_GROUP_TYPE == FRINGE_SURFEL_BASE_GROUP_TYPE) {
    surfel->set_fringe(TRUE);
  } else if constexpr ((BASE_GROUP_TYPE == FRINGE2_SURFEL_BASE_GROUP_TYPE)) {
    surfel->set_fringe2(TRUE);
  }
  if (opp_surfel != nullptr) { 
    //add opp surfel to the new group
    //(remove_surfel_from_group removes both paired surfels, no need to remove it again here)
    new_contact_surfel_group->add_surfel_to_group(opp_surfel, contact_surfel_type);
    opp_surfel->set_group(new_contact_surfel_group);
    if constexpr (BASE_GROUP_TYPE == FRINGE_SURFEL_BASE_GROUP_TYPE) {
      opp_surfel->set_fringe(TRUE);
    } else if constexpr ((BASE_GROUP_TYPE == FRINGE2_SURFEL_BASE_GROUP_TYPE)) {
      opp_surfel->set_fringe2(TRUE);
    }
  }
}

// Promote interior surfel(pairs) to :
// - fringe2 if they interact with fringe surfels(pairs), fringe or fringe2 ublks
// - fringe if they interact with ghosts()
template <SURFEL_BASE_GROUP_TYPE BASE_GROUP_TYPE>
inline SURFEL promote_interior_contact_surfel(SURFEL this_surfel,
                                              SURFEL_GROUP_SUPERTYPE supertype,
                                              CONTACT_SURFEL_GROUP contact_surfel_group, 
                                              CONTACT_SURFEL_TYPE contact_surfel_type) {
  SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[BASE_GROUP_TYPE];
  STP_PROC nsp;
  if constexpr (BASE_GROUP_TYPE == FRINGE_SURFEL_BASE_GROUP_TYPE) {
    nsp = DEST_SP_PROCESS_LAST;
  } else if constexpr (BASE_GROUP_TYPE == FRINGE2_SURFEL_BASE_GROUP_TYPE) {
    nsp = 0;
  }
  SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(this_surfel->scale(), cNEIGHBOR_SP(nsp), supertype, STP_COND_REALM);
  CONTACT_SURFEL_GROUP new_contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(base_group);
  //We need to ensure that oddd/even surfels are in the same group, so if we promote one, we promote the other
  BOOLEAN promote_clone_after_this = FALSE;
  SURFEL clone_surfel = this_surfel->clone_surfel();
  SURFEL next_surfel = this_surfel->m_next;
  if (clone_surfel != nullptr) {
    BOOLEAN move_clone;
    if constexpr (BASE_GROUP_TYPE == FRINGE_SURFEL_BASE_GROUP_TYPE) {
      move_clone = !clone_surfel->is_fringe();
    } else if constexpr (BASE_GROUP_TYPE == FRINGE2_SURFEL_BASE_GROUP_TYPE) {
      move_clone = !clone_surfel->is_fringe2();
    }
    if (move_clone) {
      if (clone_surfel->m_group->m_supertype != supertype) {
        msg_internal_error("Cannot promote odd/even conduction surfels %d and %d to same group, because they have incompatible supertypes %d and %d",
                            this_surfel->id(),clone_surfel->id(), supertype, clone_surfel->m_group->m_supertype);
      }
      //we need to promote the this surfel and its clone, preserving also the order so the even is ahead of the odd within the group
      if (clone_surfel->is_odd()) {
        promote_clone_after_this = TRUE;
      } else {
        cassert(this_surfel->m_group == clone_surfel->m_group);
        move_contact_surfel<BASE_GROUP_TYPE>(clone_surfel, next_surfel, contact_surfel_group, new_contact_surfel_group, contact_surfel_type);
      }
    } else if (clone_surfel->is_odd() || clone_surfel->m_group != base_group) {
      // In the event that the odd surfel already falls in the target group, we need to move the even to the same group
      // and move the odd afterwards to be after the even
      promote_clone_after_this = TRUE;
      new_contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(clone_surfel->m_group);
    }
  }
  //Time to promote this surfel
  move_contact_surfel<BASE_GROUP_TYPE>(this_surfel, next_surfel, contact_surfel_group, new_contact_surfel_group, contact_surfel_type);
  if (promote_clone_after_this) {
    CONTACT_SURFEL_GROUP clone_contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(clone_surfel->m_group);
    move_contact_surfel<BASE_GROUP_TYPE>(clone_surfel, next_surfel, clone_contact_surfel_group, new_contact_surfel_group, contact_surfel_type);    
  }
  return next_surfel;
}

// 02/16/2018: Promote some interior surfels to fringe due to bugs with the discretizer:
// Sometimes, a surfel interacts with another surfel, but not vice versa in the lgi file. 
// This causes the simulator to think one surfel is an interior surfel while it should be fringe.
// It is possible that this interior surfel (or fringe2 if promoted) is interacting with other ghost 
// surfels or ublks, which should not happen. Otherwise it will cause repeatability issues. As a 
// workaround, We promote these surfels to fringe during simulation initialization. 
// NOTE: these surfels are not comm'ed. Whether such a surfel is promoted to fringe depends on the 
// actual decomposition.
// TODO: This step should be removed once the disc bug is fixed. 
static void promote_interior_surfels_to_fringe_for_fixing_disc()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSURFEL* surfel = sgroup->shob_ptrs(); surfel != NULL;) {
          sSURFEL* next_surfel = surfel? surfel->m_next : NULL;
          
          if (does_surfel_interact_with_ghost<SURFEL>(surfel)) {
            sgroup->remove_shob_from_group(surfel);
            
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            //
            // Revision for conduction: for WSURFEL groups, the dest SP of the new FRINGE group must be the same as 
            // that of the original INTERIOR one, which will be a valid cross-realm dest SP for wsurfels.
  
            SURFEL_GROUP_BASE base_group;
            if(group->m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE || group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
              base_group = surfel_group_fset->create_group(scale, group->m_dest_sp, group->m_supertype, group_realm);
            } else {
              base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(DEST_SP_PROCESS_LAST), group->m_supertype, group_realm);
            }

            SURFEL_GROUP new_sgroup = static_cast<SURFEL_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel %d to fringe surfel group with head surfel id %d", surfel->id(), new_sgroup->shob_ptrs()? new_sgroup->shob_ptrs()->id() : -1);
#endif

            // For conduction, we need to ensure that oddd/even surfels are in the same group, so if we promote one, we promote them both.
            BOOLEAN clone_requires_promotion = FALSE;
            if(surfel->is_conduction_surfel() && surfel->clone_surfel() != NULL) {
              SURFEL cloneSurfel = surfel->clone_surfel();
              sSURFEL_GROUP* cloneGroup = cloneSurfel->m_group;
              if(!cloneSurfel->is_fringe()) {
                if(cloneGroup->m_supertype != group->m_supertype) {
                  msg_internal_error("Cannot promote odd/even conduction surfels %d and %d to same group, because they have incompatible supertypes %d and %d",
                                     surfel->id(),cloneSurfel->id(),group->m_supertype,cloneGroup->m_supertype);
                }
                if(next_surfel == cloneSurfel) {
                  clone_requires_promotion = TRUE;
                } else {
                  // In the event that the odd surfel interacts with a ghost, but the even doesnt, we need to promote the even
                  // surfel before promoting the odd one so that they remain in the correct order.
                  cloneGroup->remove_shob_from_group(cloneSurfel);
                  new_sgroup->add_shob_to_group(cloneSurfel);
                  cloneSurfel->set_group(new_sgroup);
                  cloneSurfel->set_fringe(TRUE);
                }
              }
            }

            new_sgroup->add_shob_to_group(surfel);
            surfel->set_group(new_sgroup);
            surfel->set_fringe(TRUE);
 
            if (clone_requires_promotion) {
              SURFEL cloneSurfel = surfel->clone_surfel();
              sSURFEL_GROUP* cloneGroup = cloneSurfel->m_group;
              next_surfel = cloneSurfel->m_next;
              cloneGroup->remove_shob_from_group(cloneSurfel);
              new_sgroup->add_shob_to_group(cloneSurfel);
              cloneSurfel->set_group(new_sgroup);
              cloneSurfel->set_fringe(TRUE);
            }
          }
          surfel = next_surfel;
        }
        break;
      }
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
        sSURFEL* opp_surfel;
        for (int type_int=0; type_int<NUM_CONTACT_SURFEL_TYPES; type_int++) {
          CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(type_int);
          //At this point, subsets are not linked so traverses each subset individually
          if (contact_surfel_group->shob_head_ptr(contact_surfel_type)!=NULL) {
            for(sSURFEL *surfel = contact_surfel_group->shob_head_ptr(contact_surfel_type), 
                        *last_surfel = contact_surfel_group->shob_tail_ptr(contact_surfel_type)->m_next; 
                        surfel != last_surfel; ) {
              sSURFEL* next_surfel = surfel->m_next;
              BOOLEAN is_fringe = does_surfel_interact_with_ghost<SURFEL>(surfel);
              if (contact_surfel_type == CONTACT_SURFEL_PAIRED) { //both ordered consecutively
                is_fringe = (is_fringe || does_surfel_interact_with_ghost<SURFEL>(next_surfel));
                next_surfel = next_surfel->m_next;
              } 
              if (is_fringe) {
#ifdef DEBUG_SHOB_CLASSIFICATION
                  msg_print("Move surfel %d to fringe surfel group for fixing disc", surfel->id());
#endif
                next_surfel = promote_interior_contact_surfel<FRINGE_SURFEL_BASE_GROUP_TYPE>(surfel, 
                                                                                             group->m_supertype, 
                                                                                             contact_surfel_group, 
                                                                                             contact_surfel_type);
              }
              surfel = next_surfel;
            }
          }
        }
        break;
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
          for(sSURFEL_PAIR* surfel_pair = sgroup->shob_ptrs(); surfel_pair != NULL;) {
          sSURFEL_PAIR* next_surfel_pair = surfel_pair? surfel_pair->m_next : NULL;

          BOOLEAN is_surfel_pair_fringe = FALSE;
          ccDOTIMES(s, 2) {
            SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
            if (does_surfel_interact_with_ghost<SURFEL>(surfel)) {
              is_surfel_pair_fringe = TRUE;
              break;
            }
          }
          if (is_surfel_pair_fringe) {
            sgroup->remove_shob_from_group(surfel_pair);
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            // 
            // dest_sp should be DEST_SP_PROCESS_LAST here 
            SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(DEST_SP_PROCESS_LAST), group->m_supertype, group_realm);
            SURFEL_PAIR_GROUP new_sgroup = static_cast<SURFEL_PAIR_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel pair %d to fringe surfel pair group", surfel_pair->id());
#endif
            new_sgroup->add_shob_to_group(surfel_pair);
            surfel_pair->set_group(new_sgroup);
            surfel_pair->set_fringe();
          }
          surfel_pair = next_surfel_pair;
        }
        break;
      }
      default:
        break;
      }
    }
  }
}

// XDU: If the mirror surfels satisfy the criteria to be promoted to fringe, promote the real surfels to fringe.
// Promote interior surfel(pairs) to fringe if they interact with ghost surfels(pairs) or ghost ublks
static void promote_interior_surfels_to_fringe()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSURFEL* surfel = sgroup->shob_ptrs(); surfel != NULL;) {
          sSURFEL* next_surfel = surfel? surfel->m_next : NULL;
          BOOLEAN to_be_fringe = check_if_surfel_satisfies_func(surfel, does_surfel_interact_with_ghost<SURFEL>);
          if (to_be_fringe) {
            sgroup->remove_shob_from_group(surfel);
            
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            //
             // Revision for conduction: for WSURFEL groups, the dest SP of the new FRINGE group must be the same as 
            // that of the original INTERIOR one, which will be a valid cross-realm dest SP for wsurfels.

            SURFEL_GROUP_BASE base_group;
            if(group->m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE || group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
              base_group = surfel_group_fset->create_group(scale, group->m_dest_sp, group->m_supertype, group_realm);
            } else {
              base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(0), group->m_supertype, group_realm);
            }

            SURFEL_GROUP new_sgroup = static_cast<SURFEL_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel %d to fringe surfel group with head surfel id %d", surfel->id(), new_sgroup->shob_ptrs()? new_sgroup->shob_ptrs()->id() : -1);
#endif

// For conduction, we need to ensure that oddd/even surfels are in the same group, so if we promote one, we promote them both.
            BOOLEAN clone_requires_promotion = FALSE;
            if(surfel->is_conduction_surfel() && surfel->clone_surfel() != NULL) {
              SURFEL cloneSurfel = surfel->clone_surfel();
              sSURFEL_GROUP* cloneGroup = cloneSurfel->m_group;
              if(!cloneSurfel->is_fringe()) {
                if(cloneGroup->m_supertype != group->m_supertype) {
                  msg_internal_error("Cannot promote odd/even conduction surfels %d and %d to same group, because they have incompatible supertypes %d and %d",
                                     surfel->id(),cloneSurfel->id(),group->m_supertype,cloneGroup->m_supertype);
                }

                if(next_surfel == cloneSurfel) {
                  clone_requires_promotion = TRUE;
                } else {
                  // In the event that the odd surfel interacts with a ghost, but the even doesnt, we need to promote the even
                  // surfel before promoting the odd one so that they remain in the correct order.
                  cloneGroup->remove_shob_from_group(cloneSurfel);
                  new_sgroup->add_shob_to_group(cloneSurfel);
                  cloneSurfel->set_group(new_sgroup);
                  cloneSurfel->set_fringe(TRUE);
                }
              }
            }

            new_sgroup->add_shob_to_group(surfel);
            surfel->set_group(new_sgroup);
            surfel->set_fringe(TRUE);
 
            if (clone_requires_promotion) {
              SURFEL cloneSurfel = surfel->clone_surfel();
              sSURFEL_GROUP* cloneGroup = cloneSurfel->m_group;
              next_surfel = cloneSurfel->m_next;
              cloneGroup->remove_shob_from_group(cloneSurfel);
              new_sgroup->add_shob_to_group(cloneSurfel);
              cloneSurfel->set_group(new_sgroup);
              cloneSurfel->set_fringe(TRUE);
            }
          }

          surfel = next_surfel;
        }
        break;
      }
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
        sSURFEL* opp_surfel;
        for (int type_int=0; type_int<NUM_CONTACT_SURFEL_TYPES; type_int++) {
          CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(type_int);
          //At this point, subsets are not linked so traverses each subset individually 
          if (contact_surfel_group->shob_head_ptr(contact_surfel_type)!=NULL) {
            for(sSURFEL *surfel = contact_surfel_group->shob_head_ptr(contact_surfel_type), 
                        *last_surfel = contact_surfel_group->shob_tail_ptr(contact_surfel_type)->m_next; 
                        surfel != last_surfel; ) {
              sSURFEL* next_surfel = surfel->m_next;
              BOOLEAN to_be_fringe = check_if_surfel_satisfies_func(surfel, does_surfel_interact_with_ghost<SURFEL>);
              if (contact_surfel_type == CONTACT_SURFEL_PAIRED) { //both ordered consecutively
                to_be_fringe = (to_be_fringe || check_if_surfel_satisfies_func(next_surfel, does_surfel_interact_with_ghost<SURFEL>));
                next_surfel = next_surfel->m_next;
              }
              if (to_be_fringe) {
#ifdef DEBUG_SHOB_CLASSIFICATION
                  msg_print("Move surfel %d to fringe contact surfel group", surfel->id());
#endif
                next_surfel = promote_interior_contact_surfel<FRINGE_SURFEL_BASE_GROUP_TYPE>(surfel, 
                                                                                             group->m_supertype, 
                                                                                             contact_surfel_group, 
                                                                                             contact_surfel_type);
              }
              surfel = next_surfel;
            }
          }
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {  
        break;  // sampling surfels only participate in v2s and s2s and they have no impact on fringe2 surfels
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSURFEL_PAIR* surfel_pair = sgroup->shob_ptrs(); surfel_pair != NULL;) {
          sSURFEL_PAIR* next_surfel_pair = surfel_pair? surfel_pair->m_next : NULL;
          BOOLEAN is_surfel_pair_fringe = check_if_surfel_pair_satisfies_func(surfel_pair, does_surfel_interact_with_ghost<SURFEL>);
          if (is_surfel_pair_fringe) {
            sgroup->remove_shob_from_group(surfel_pair);
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            // 
            // dest_sp should be 0 here 
            SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(0), group->m_supertype, group_realm);
            SURFEL_PAIR_GROUP new_sgroup = static_cast<SURFEL_PAIR_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel pair %d to fringe surfel pair group", surfel_pair->id());
#endif
            new_sgroup->add_shob_to_group(surfel_pair);
            surfel_pair->set_group(new_sgroup);
            surfel_pair->set_fringe();
          }
          surfel_pair = next_surfel_pair;
        }
        break;
      }
      default:
        break;
      }
    }
  }
}

static void promote_interior_sampling_surfels_to_fringe()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_SURFEL_BASE_GROUP_TYPE) {
      if (group->m_supertype == SAMPLING_SURFEL_GROUP_SUPERTYPE) {  
        // If sampling surfel interacts with ghost ublks, change it to the fringe fset
        SAMPLING_SURFEL_GROUP sgroup = static_cast<SAMPLING_SURFEL_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSAMPLING_SURFEL* sampling_surfel = sgroup->shob_ptrs(); sampling_surfel != NULL;) {
          sSAMPLING_SURFEL* next_sampling_surfel = sampling_surfel? sampling_surfel->m_next : NULL;
          if (does_surfel_interact_with_ghost<SAMPLING_SURFEL>(sampling_surfel)) {
            sgroup->remove_shob_from_group(sampling_surfel);
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            //
            // dest_sp should be DEST_SP_PROCESS_LAST here 
            SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(DEST_SP_PROCESS_LAST), group->m_supertype, group_realm);
            SAMPLING_SURFEL_GROUP new_sgroup = static_cast<SAMPLING_SURFEL_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move sampling surfel %d to fringe sampling surfel group with head surfel id %d", sampling_surfel->id(), new_sgroup->shob_ptrs()? new_sgroup->shob_ptrs()->id() : -1);
#endif
            new_sgroup->add_shob_to_group(sampling_surfel);
            sampling_surfel->set_group(new_sgroup);
            sampling_surfel->set_fringe(true);
          }
          sampling_surfel = next_sampling_surfel;
        }
      }
    }
  }
}

static void promote_interacting_nearblks_to_fringe2(SURFEL surfel)
{
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    // Skip solid ublks, mirror ublks, ghost ublks and fringe ublks
    if (ublk->is_solid() || ublk->is_mirror() || ublk->is_ghost() ||
        ublk->is_fringe() || ublk->is_fringe2())
      continue;
    if (ublk->is_vr_fine()) { // The vr coarse and all vr fine ublks must be fringe2
      sVR_FINE_INTERFACE_DATA *vr_fine_data = ublk->vr_fine_data();
      UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data = coarse_ublk->vr_coarse_data();
      ccDOTIMES(i, ubFLOAT::N_VOXELS) {
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
        if (fine_ublk == NULL || fine_ublk->is_solid())
          continue;
        move_ublk_to_nearblk_group(fine_ublk, VRFINE_FRINGE2_NEARBLK_GROUP_TYPE);

#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Move interior near vrfine ublk %d to vrfine fringe2 nearblk group", fine_ublk->id());
#endif 
        fine_ublk->set_fringe2();
      }
    } else if (ublk->is_vr_coarse()) {  // All the vr fine ublks must be fringe2
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
      ccDOTIMES(i, ubFLOAT::N_VOXELS) {
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
        if (fine_ublk == NULL || fine_ublk->is_solid()) 
          continue;
        move_ublk_to_nearblk_group(fine_ublk, VRFINE_FRINGE2_NEARBLK_GROUP_TYPE);
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Move interior near vrfine ublk %d to vrfine fringe2 nearblk group", fine_ublk->id());
#endif 
        fine_ublk->set_fringe2();
      }
      // Move the vr coarse ublk to fringe2
      move_ublk_to_nearblk_group(ublk, FRINGE2_NEARBLK_GROUP_TYPE);
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Move interior vrcoarse ublk %d to fringe2 nearblk group", ublk->id());
#endif 

      ublk->set_fringe2();
    } else {
      move_ublk_to_nearblk_group(ublk, FRINGE2_NEARBLK_GROUP_TYPE);
#ifdef DEBUG_SHOB_CLASSIFICATION
        msg_print("Move interior near ublk %d to fringe2 nearblk group", ublk->id());
#endif 
      ublk->set_fringe2();
    }
  }
}
 

// Promote interior nearblks to fringe2 if they interact with fringe surfels
static void promote_interior_nearblks_to_fringe2()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE:
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          promote_interacting_nearblks_to_fringe2(surfel);
          // Promote mirror surfel interacting nearblks to fringe2
          if (surfel->has_mirror()) {
            sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              promote_interacting_nearblks_to_fringe2(mirror_surfel);
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
        break;
      }
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        //Similar to regular surfels, but contact_surfel groups distinguishes subsets within which at this point 
        //might not have been linked depending on when this method is called. Safer to use specific macro
        //to ensure we loop through all the subsets consecutively
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
        DO_CONTACT_SURFELS_OF_GROUP(surfel, contact_surfel_group, contact_surfel_type) {
          promote_interacting_nearblks_to_fringe2(surfel);
          // Promote mirror surfel interacting nearblks to fringe2
          if (surfel->has_mirror()) {
            sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              promote_interacting_nearblks_to_fringe2(mirror_surfel);
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {
        break;  // sampling surfels only participate in v2s, not s2v, thus they have no impact on fringe2 nearblks
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        DO_SURFEL_PAIRS_OF_GROUP(surfel_pair, sgroup) {
          ccDOTIMES(s, 2) {
            SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
            promote_interacting_nearblks_to_fringe2(surfel);

            // Promote mirror surfel interacting nearblks to fringe2
            if (surfel->has_mirror()) {
            sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              promote_interacting_nearblks_to_fringe2(mirror_surfel);
              mirror_data = mirror_data->m_next_mirror_data;
            }
            }
          }
        }
        break;
      }
      default:
        break;
      }
    }
  }
}
 
static void promote_ublks_to_sliding_nearblks()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_group, scale) {
      mlrf_group->promote_interacting_ublks_to_sliding_nearblks();
    }
  }
}



static void promote_interior_surfels_to_fringe2()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSURFEL* surfel = sgroup->shob_ptrs(); surfel != NULL;) {
          sSURFEL* next_surfel = surfel? surfel->m_next : NULL;
          // It is possible that the mirror surfel is interacting with fringe surfels, fringe or fringe2 ublks, but the real surfel does not.
          // We should check the mirror surfel here too

          BOOLEAN to_be_fringe2 = check_if_surfel_satisfies_func(surfel, does_surfel_interact_with_fringe_or_fringe2);
          if (to_be_fringe2) {
            sgroup->remove_shob_from_group(surfel);
            
            // XDU: Decrease the group count by 1. This is not needed if we eliminate the group shob counts from the surfel loop
            // surfel->m_group->m_shob_count--;
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE2_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            //
            // Revision for conduction: for WSURFEL groups, the dest SP of the new FRINGE2 group must be the same as 
            // that of the original INTERIOR one, which will be a valid cross-realm dest SP for wsurfels.
 
            SURFEL_GROUP_BASE base_group;
            if(group->m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE || group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
              base_group = surfel_group_fset->create_group(scale, group->m_dest_sp, group->m_supertype, group_realm);
            } else {
              base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(0), group->m_supertype, group_realm);
            }

            SURFEL_GROUP new_sgroup = static_cast<SURFEL_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel %d to fringe2 surfel group with head surfel id %d", surfel->id(), new_sgroup->shob_ptrs()? new_sgroup->shob_ptrs()->id() : -1);
#endif
            new_sgroup->add_shob_to_group(surfel);
            surfel->set_group(new_sgroup);
            surfel->set_fringe2(TRUE);
            // For conduction, we need to ensure that oddd/even surfels are in the same group, so if we promote one, we promote them both
            if(surfel->is_conduction_surfel() && surfel->clone_surfel() != NULL) {
              SURFEL cloneSurfel = surfel->clone_surfel();
              sSURFEL_GROUP* cloneGroup = cloneSurfel->m_group;
              if(!cloneSurfel->is_fringe2()) { // It's not necessary to check if the clone is fringe, because if it was, the surfel itself would be fringe
                if(cloneGroup->m_supertype != group->m_supertype) {
                  msg_internal_error("Cannot promote odd/even conduction surfels %d and %d to same group, because they have incompatible supertypes %d and %d",
                                     surfel->id(),cloneSurfel->id(),group->m_supertype,cloneGroup->m_supertype);
                }
                if(next_surfel == cloneSurfel) {
                  next_surfel = cloneSurfel->m_next;
                }
                cloneGroup->remove_shob_from_group(cloneSurfel);
                new_sgroup->add_shob_to_group(cloneSurfel);
                cloneSurfel->set_group(new_sgroup);
                cloneSurfel->set_fringe2(TRUE);
              }
            }

          }
          surfel = next_surfel;
        }
        break;
      }
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
        sSURFEL* opp_surfel;
        for (int type_int=0; type_int<NUM_CONTACT_SURFEL_TYPES; type_int++) {
          CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(type_int);
          //At this point, subsets are not linked so traverses each subset individually 
          if (contact_surfel_group->shob_head_ptr(contact_surfel_type)!=NULL) {
            for(sSURFEL *surfel = contact_surfel_group->shob_head_ptr(contact_surfel_type), 
                        *last_surfel = contact_surfel_group->shob_tail_ptr(contact_surfel_type)->m_next; 
                        surfel != last_surfel; ) {
              sSURFEL* next_surfel = surfel->m_next;
              // It is possible that the mirror surfel is interacting with fringe surfels, fringe or fringe2 ublks, 
              // but the real surfel does not. We should check the mirror surfel here too
              BOOLEAN to_be_fringe2 = check_if_surfel_satisfies_func(surfel, does_surfel_interact_with_fringe_or_fringe2);
              if (contact_surfel_type == CONTACT_SURFEL_PAIRED) { //both ordered consecutively
                to_be_fringe2 = (to_be_fringe2 || check_if_surfel_satisfies_func(next_surfel, does_surfel_interact_with_fringe_or_fringe2));
                next_surfel = next_surfel->m_next;
              }
              if (to_be_fringe2) {
#ifdef DEBUG_SHOB_CLASSIFICATION
                msg_print("Move surfel %d to fringe2 contact surfel group", surfel->id());
#endif
                next_surfel = promote_interior_contact_surfel<FRINGE2_SURFEL_BASE_GROUP_TYPE>(surfel, 
                                                                                              group->m_supertype, 
                                                                                              contact_surfel_group, 
                                                                                              contact_surfel_type);
              }
              surfel = next_surfel;
            }
          }
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {  
        SAMPLING_SURFEL_GROUP sgroup = static_cast<SAMPLING_SURFEL_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSAMPLING_SURFEL* sampling_surfel = sgroup->shob_ptrs(); sampling_surfel != NULL;) {
          sSAMPLING_SURFEL* next_sampling_surfel = sampling_surfel? sampling_surfel->m_next : NULL;

          BOOLEAN to_be_fringe2 = check_if_sampling_surfel_satisfies_func(sampling_surfel, does_sampling_surfel_interact_with_fringe_or_fringe2);
          if (to_be_fringe2) {
            sgroup->remove_shob_from_group(sampling_surfel);
            
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE2_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            //
            // dest_sp should be 0 here 
            SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(0), group->m_supertype, group_realm);
            SAMPLING_SURFEL_GROUP new_sgroup = static_cast<SAMPLING_SURFEL_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move sampling surfel %d to fringe2 surfel group with head surfel id %d", sampling_surfel->id(), new_sgroup->shob_ptrs()? new_sgroup->shob_ptrs()->id() : -1);
#endif
            new_sgroup->add_shob_to_group(sampling_surfel);
            sampling_surfel->set_group(new_sgroup);
            sampling_surfel->set_fringe2(TRUE);
          }

          sampling_surfel = next_sampling_surfel;
        }
        break;  // sampling surfels only participate in v2s and s2s and they have no impact on fringe2 surfels
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        REALM group_realm = sgroup->m_realm;
        for(sSURFEL_PAIR* surfel_pair = sgroup->shob_ptrs(); surfel_pair != NULL;) {
          sSURFEL_PAIR* next_surfel_pair = surfel_pair? surfel_pair->m_next : NULL;

          BOOLEAN is_surfel_pair_fringe2 = check_if_surfel_pair_satisfies_func(surfel_pair, does_surfel_interact_with_fringe_or_fringe2);
          if (is_surfel_pair_fringe2) {
            sgroup->remove_shob_from_group(surfel_pair);
            SURFEL_BASE_GROUP_TYPE base_group_type = FRINGE2_SURFEL_BASE_GROUP_TYPE;
            SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
            // Based on the following logic:
            //
            // A non-ghosted surfel of a ghosted type goes in the last group of that type and scale,
            // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted surfel of a non-ghosted type goes in the
            // only group of that type and scale, with dest_sp = 0.
            // BOOLEAN type_has_ghosts = (base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? TRUE : FALSE;
            // STP_PROC dest_sp = type_has_ghosts ? DEST_SP_PROCESS_LAST : 0;
            // 
            // dest_sp should be 0 here 
            SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, cNEIGHBOR_SP(0), group->m_supertype, group_realm);
            SURFEL_PAIR_GROUP new_sgroup = static_cast<SURFEL_PAIR_GROUP>(base_group);
#ifdef DEBUG_SHOB_CLASSIFICATION
            msg_print("Move surfel pair %d to fringe2 surfel pair group", surfel_pair->id());
#endif
            new_sgroup->add_shob_to_group(surfel_pair);
            surfel_pair->set_group(new_sgroup);
            surfel_pair->set_fringe2();
          }
          surfel_pair = next_surfel_pair;
        }
        break;
      }
      default:
        break;
      }
    }
  }

  // Additional consideration for non-conformal contact surfels, which can be apart an arbitrary distance:
  // - We traverse the contact weights to identify each primary-secondary interaction and evaluate if they imply a
  //   fringe<->interior interaction, in which case the interior needs to be promoted to fringe2. 
  // - Additionally, we do a second adjustment in the contact weights before they are loaded into the groups to account
  //   for the fact that fringe2 is processed before. Thus, for all interactions that involve different groups, mark the
  //   one in the fringe2 as primary to ensure that it will be processed before
  // - Non-conformal are loaded as global by default, except when only 1SP is present in which case is routed directly
  //   to local. Thus, we can determine their supertype before looping through the weights.  
  SURFEL_GROUP_SUPERTYPE non_conformal_supertype = (total_sps == 1) ? CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE : CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE;
  std::vector<SHOB_ID> new_secondary_ids;
  std::vector<sCONTACT_WEIGHT_ITEM> new_weight_items;
  for (auto & [primary_id, contact_weight_set] : g_contact_info) {
    SURFEL primary_surfel = regular_surfel_from_id(primary_id, STP_COND_REALM);
    BOOLEAN is_primary_not_fringe2 = !primary_surfel->is_fringe2();
    BOOLEAN is_primary_fringe = primary_surfel->is_fringe();
    BOOLEAN is_primary_interior = is_primary_not_fringe2 && !is_primary_fringe;
    size_t i = 0, num_weights = contact_weight_set.m_weights.size();
    while (i<num_weights) {
      sCONTACT_WEIGHT_ITEM *weight = &(contact_weight_set.m_weights[i]);
      SHOB_ID secondary_id = weight->m_opposite_surfel_id;
      SURFEL secondary_surfel = regular_surfel_from_id(secondary_id, STP_COND_REALM);
      if (secondary_surfel->is_ghost()) {
        i++; //no further checks needed, moves to next weight
        continue;
      }
      //Time to check the subgroups to which the surfels belong
      BOOLEAN is_secondary_fringe = secondary_surfel->is_fringe();
      BOOLEAN is_secondary_fringe2 = secondary_surfel->is_fringe2();
      BOOLEAN is_secondary_interior = !(is_secondary_fringe || is_secondary_fringe2);
      if (is_secondary_fringe && is_primary_interior) {
        //primary needs to be promoted to fringe2
        //(negative weight imply is a reversed entry with secondaries owning the weights, so flips contact type)
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(primary_surfel->m_group);
        CONTACT_SURFEL_TYPE type = (weight->m_weight > 0.0) ? CONTACT_SURFEL_PRIMARY : CONTACT_SURFEL_SECONDARY;
        promote_interior_contact_surfel<FRINGE2_SURFEL_BASE_GROUP_TYPE>(primary_surfel, non_conformal_supertype, 
                                                                        contact_surfel_group, type);
        is_primary_not_fringe2 = FALSE;
        is_primary_interior = FALSE;
      } else if (is_primary_fringe && is_secondary_interior) {
        //Secondary needs to be promoted to fringe2
        //(negative weight imply is a reversed entry with secondaries owning the weights, so flips contact type)
        CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(secondary_surfel->m_group);
        CONTACT_SURFEL_TYPE type = (weight->m_weight > 0.0) ? CONTACT_SURFEL_SECONDARY : CONTACT_SURFEL_PRIMARY;
        promote_interior_contact_surfel<FRINGE2_SURFEL_BASE_GROUP_TYPE>(secondary_surfel, non_conformal_supertype, 
                                                                        contact_surfel_group, type);
        is_secondary_fringe2 = TRUE;
        is_secondary_interior = FALSE;
      }
      //Finally, if secondary surfel will be processed before due to the order groups, flips primary and secondary
      //assignments by shuffling weights in the global map to be consistent with this behavior
      BOOLEAN flip_order;
      if (is_primary_not_fringe2 && is_secondary_fringe2) {
        //If secondary is in the fringe 2 but primary is not, it will be processed before so it flips order
        flip_order = TRUE;
      } else if (is_primary_fringe && is_secondary_fringe && (primary_surfel->scale() == secondary_surfel->scale())) {
        //both should have valid destination neighbor SPs, checks the order to determine which one should be primary
        const STP_PROC primary_nsp = primary_surfel->m_group->m_send_group->dest_sp().nsp();
        const STP_PROC secondary_nsp = secondary_surfel->m_group->m_send_group->dest_sp().nsp();
        flip_order = (secondary_nsp < primary_nsp);
      } else {
        flip_order = FALSE;
      }
      if (flip_order) {
        //Add the new weights to temporary arrays so we don't invalidate the iterators, and to be added later once we
        //are done traversing the map
        new_secondary_ids.push_back(secondary_id);
        sCONTACT_WEIGHT_ITEM new_weight;
        new_weight.m_opposite_surfel_id = primary_id;
        new_weight.m_weight = -weight->m_opposite_weight;
        new_weight.m_opposite_weight = -weight->m_weight;
        new_weight_items.push_back(new_weight);
        //CP arranges the weights so primaries are in a finer or same scale than secondaries. Thus, after flipping the
        //order, the new primary may belong to a coarser scale than the new secondary. Thus, it may not be processed
        //in all the steps that the new secondary would, so we duplicate the weight item rather than flipping it. This
        //will lead to the new secondary to be promoted to primary_secondary after, and guarantee that it is traversed
        //always, needing to skip the interaction when the scale where the new primary resides is active.  
        if (secondary_surfel->scale() >= primary_surfel->scale()) { 
          //Secondary is in a finer (higher value) or equal scale than primary.
          //Moves the last weight that has not been processed yet to the location being processed
          num_weights--;
          *weight = contact_weight_set.m_weights[num_weights];
        } else {
          i++;
        }
      } else {
        i++;
      }
    }
    //Removes the weights moved to the secondary. Note that even if we end up with no weights, we don't remove the entry
    //from the contact info map so later we detect that it is a primary and shuffle the surfels within the group
    //properly when completing the initialization of contact surfel groups
    contact_weight_set.m_weights.resize(num_weights);
  }
  //Adds the new secondaries moved to act as primary to the map
  for (size_t i=0; i<new_secondary_ids.size(); i++) {
    SHOB_ID secondary_id = new_secondary_ids[i];
    auto it_secondary = g_contact_info.find(new_secondary_ids[i]);
    if (it_secondary == g_contact_info.end()) {
      //needs new entry in the map
      sCONTACT_WEIGHT_SET new_weight_set;
      new_weight_set.m_weights.clear();
      it_secondary = g_contact_info.insert(it_secondary, {new_secondary_ids[i], new_weight_set});
    }
    //Add the new weights to the iterator
    (it_secondary->second).m_weights.push_back(new_weight_items[i]);
  }
}

// Separate interior_farblk1 and interior_farblk2 ublks: mark ublks with fringe ublk neighbors interior_farblk2
// and others interior_farblk1. For the moment all interior farblks are interior_farblk2, so we just need to
// move farblk1 ublks.
static void divide_interior1_interior2_ublks()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK2_GROUP_TYPE) {
      for(sUBLK* ublk = group->shob_ptrs(); ublk != NULL;) {
        UBLK next_ublk = ublk? ublk->m_next : NULL;
        if (is_ublk_valid_for_interior_farblk_1(ublk)) {
          ublk->m_group->remove_shob_from_group(ublk);
          UBLK_GROUP_TYPE group_type = INTERIOR_FARBLK1_GROUP_TYPE;
          UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];
          UBLK_GROUP ugroup = ublk_group_fset->create_group(ublk->scale(), cNEIGHBOR_SP(DEST_SP_PROCESS_LAST));
#if BUILD_5G_LATTICE
	        if (!sim.is_large_pore)
#endif
	        {
            if (g_adv_fraction == 1 && ublk->has_two_copies()) {
              reduce_to_one_copy(ublk);
            }
          }
          ugroup->add_shob_to_group(ublk);
          ublk->set_group(ugroup);
#ifdef DEBUG_SHOB_CLASSIFICATION
          msg_print("Move ublk %d to interior farblk1 group %d", ublk->id(), group_type);
#endif
        }
        ublk = next_ublk;
      } 
    }
  }
}

VOID allocate_contact_send_recv_accumulators() {
  if (g_contact_info.empty()) { //no non-conformal contact defined, no accumulators needed
    return;
  }
  //Allocate g_contact_comm_accumulator to be able to retrive pointers to the accumulators k
  g_contact_comm_accumulator.resize(g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps());
  ccDOTIMES(i, g_contact_comm_accumulator.size()) {
    g_contact_comm_accumulator[i].resize(sim_num_scales());
  }
  //Traverse the map to identify the size requirements
  for (auto& [primary_id, contact_weight_set] : g_contact_info) {
    SURFEL primary_surfel = regular_surfel_from_id(primary_id, STP_COND_REALM);
    for (auto& weight : contact_weight_set.m_weights) {
      SHOB_ID secondary_id = weight.m_opposite_surfel_id;
      SURFEL secondary_surfel = regular_surfel_from_id(secondary_id, STP_COND_REALM);
      if (secondary_surfel->is_ghost()) {
        asINT32 primary_scale = primary_surfel->scale();
        asINT32 secondary_scale = secondary_surfel->scale();
        if (primary_scale != secondary_scale) {
          //If secondary is a ghost in a different scale, accumulated heat is computed by the finer scale and sent to
          //the SP processing the coarser scale.  //Add here the coarser surfel to g_contact_comm_accumulator to keep
          //track of it later. We send the info through the send/recv groups of the finer scale, with the acumulators
          //within the group identified by the coarser surfel id
          sSP_RECV_GROUP_BASE* recv_group = reinterpret_cast<sSP_RECV_GROUP_BASE*>(secondary_surfel->m_group);
          STP_PROC nsp = recv_group->m_source_sp.nsp(); 
          if (primary_scale > secondary_scale) {
            //surfel in this SP in a finer scale, accumulates and sends to the SP containing the ghost
            g_contact_comm_accumulator[nsp][primary_scale].insert({secondary_id, -1});
            // msg_print("send nsp %d scale %d : %d (%d) - %d (%d)",nsp,primary_scale,primary_id,primary_scale,secondary_id,secondary_scale);
          } else {
            //surfel in this SP in a coarser scale, received accumulated heat from the SP with the ghost
            g_contact_comm_accumulator[nsp][secondary_scale].insert({primary_id, -1});
            // msg_print("recv nsp %d scale %d : %d (%d) - %d (%d)",nsp,primary_scale,primary_id,primary_scale,secondary_id,secondary_scale);
          }
        }
      }
    }
  }
  //Traverse the send & recv groups and allocate storage for commed accumulated data
  ccDOTIMES(i, g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps()) {
    ccDOTIMES(j, sim_num_scales()) {
      asINT32 num_send = 0;
      asINT32 num_recv = 0;
      auto& accum_map = g_contact_comm_accumulator[i][j];
      for (auto it = accum_map.begin(); it != accum_map.end(); it++) {
        SURFEL coarser_surfel = regular_surfel_from_id(it->first, STP_COND_REALM);
        if (coarser_surfel->is_ghost()) {
          // surfel in this sp in a finer scale, accumulates and sends data
          it->second = num_send; //replaces the surfel_id with the index within the accumulation vector
          num_send++;
          if (coarser_surfel->is_conduction_shell()) num_send += coarser_surfel->shell_conduction_data()->num_layers();
        } else {
          // surfel in this SP in a coarser scale, receives accumulated data
          it->second = num_recv; //replaces the surfel_id with the index within the accumulation vector
          num_recv++;
          if (coarser_surfel->is_conduction_shell()) num_recv += coarser_surfel->shell_conduction_data()->num_layers();
        }
      }
      if (num_send==0 && num_recv==0) continue;
      //allocate accumulator storage for the send group (if needed)
      cNEIGHBOR_SP neighbor_sp(i);
      if (num_send > 0) {
        sCONTACT_SEND_GROUP signature;
        signature.m_dest_sp = neighbor_sp;
        signature.m_scale = j;
        sCONTACT_SEND_GROUP* send_group = g_contact_send_fset.find_group(&signature);
        send_group->m_secondary_accumulated_interface_heat.resize(num_send, 0.0);
      }
      //allocate accumulator pointers for the recv group (if needed)
      if (num_recv > 0) {
        sCONTACT_RECV_GROUP signature;
        signature.m_source_sp = neighbor_sp;
        signature.m_scale = j;
        sCONTACT_RECV_GROUP* recv_group = g_contact_recv_fset.find_group(&signature);
        recv_group->m_secondary_accumulated_interface_heat_ptr.resize(num_recv, nullptr);
      }
    }
  }
}

VOID reset_primary_as_global(SHOB_ID primary_id, 
                             std::unordered_map<SHOB_ID,std::vector<SHOB_ID>> &secondary_to_primary_map,
                             std::unordered_map<SHOB_ID,BOOLEAN> &promoting_map) 
{
  //First call enters in the loop because the primary triggering it is promoted by default so starts as TRUE
  if (promoting_map[primary_id]) {
    promoting_map[primary_id] = FALSE;
    //loops through the secondaries, setting to false the primaries already processed and sharing the secondary
    for (sCONTACT_WEIGHT_ITEM &weight : g_contact_info[primary_id].m_weights) {
      auto it_secondary_to_primary = secondary_to_primary_map.find(weight.m_opposite_surfel_id);
      if (it_secondary_to_primary != secondary_to_primary_map.end()) {
        for (SHOB_ID id : it_secondary_to_primary->second) {
          reset_primary_as_global(id, secondary_to_primary_map, promoting_map);
        }
        //removes the primaries that have already been set to false, so we don't even need to check them again when 
        //running into the secondary in a later primary
        it_secondary_to_primary->second.clear();
      }
    }
  }
  return;
}

static void promote_global_contact_surfels_to_local()
{
  //creates two maps to track back the primary surfels linked to a given secondary, 
  //and to flag the primaries that can be promoted to secondary
  std::unordered_map<SHOB_ID,std::vector<SHOB_ID>> secondary_to_primary_map;
  std::unordered_map<SHOB_ID,BOOLEAN> promoting_map;
  for (int type_int=0; type_int<N_SURFEL_BASE_GROUP_TYPES; type_int++) {
    SURFEL_BASE_GROUP_TYPE surfel_base_group_type = static_cast<SURFEL_BASE_GROUP_TYPE>(type_int);
    STP_PROC dest_sp = (surfel_base_group_type == FRINGE_SURFEL_BASE_GROUP_TYPE) ? DEST_SP_PROCESS_LAST : 0;
    for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
      DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_base_group_type) {
        if (group->m_supertype == CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE) {
          CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(group);
          //STEP 1: 
          //does a first pass to determine which ones can be promoted to local (note that secondaries might contain
          //weights as well, so we loop through all contact surfels)
          secondary_to_primary_map.clear();
          promoting_map.clear();
          DO_CONTACT_SURFELS_OF_GROUP(surfel, contact_surfel_group, contact_surfel_type) {
            SHOB_ID primary_id = surfel->id();
            auto it_primary = g_contact_info.find(primary_id);
            if (it_primary != g_contact_info.end() && !it_primary->second.m_weights.empty()) {
              //assumes it can be promoted to start with
              promoting_map[primary_id] = TRUE;
              for (sCONTACT_WEIGHT_ITEM &weight : it_primary->second.m_weights) {
                SHOB_ID secondary_id = weight.m_opposite_surfel_id;
                sSURFEL* secondary_surfel = regular_surfel_from_id(secondary_id, STP_COND_REALM);
                //Strictly speaking, we could loop through the secondary weights if present to determine whether they imply
                //contact with a ghost or with a primary in a coarser scale but in the same SP (will not require comm).
                //Nevertheless, for simplicity and to avoid possible issues with future implementations, we adopt a more
                //restrictive approach and rule out promotion if secondaries are not in the group or have any interaction
                //beyond the group
                if (secondary_surfel->is_ghost() || 
                    secondary_surfel->m_group != group ||
                    g_contact_info.find(secondary_id) != g_contact_info.end()) { //cannot be promoted
                  reset_primary_as_global(primary_id, secondary_to_primary_map, promoting_map);
                  break;
                }
                else {
                  secondary_to_primary_map[secondary_id].push_back(primary_id);
                }
              }
            }
          }
          //STEP 2: 
          //now that we have a clear picture, time to promote those marked for promotion 
          for (CONTACT_SURFEL_TYPE primary_type = CONTACT_SURFEL_PRIMARY; primary_type <= CONTACT_SURFEL_SECONDARY; ) {
            if (contact_surfel_group->shob_head_ptr(primary_type)!=NULL) {
#if ENABLE_CONSISTENCY_CHECKS
              if (primary_type == CONTACT_SURFEL_PRIMARY_SECONDARY) {
                msg_internal_error("No primary_secondary should be defined at this stage");
              }
#endif
              CONTACT_SURFEL_TYPE secondary_type = 
                  (primary_type==CONTACT_SURFEL_PRIMARY) ? CONTACT_SURFEL_SECONDARY : CONTACT_SURFEL_PRIMARY;
              for (sSURFEL *surfel = contact_surfel_group->shob_head_ptr(primary_type), 
                           *last_surfel = contact_surfel_group->shob_tail_ptr(primary_type)->m_next; 
                           surfel != last_surfel; ) {
                sSURFEL* next_surfel = surfel->m_next;
                auto it_promoting = promoting_map.find(surfel->id());
                if (it_promoting != promoting_map.end() && it_promoting->second) {
                  SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[surfel_base_group_type];
                  SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, 
                                                                                cNEIGHBOR_SP(dest_sp), 
                                                                                CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE,
                                                                                STP_COND_REALM);
                  CONTACT_SURFEL_GROUP new_contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(base_group);
                  //promotes primary
                  // msg_print("global->local primary %d", surfel->id());
                  contact_surfel_group->remove_surfel_from_group(surfel, primary_type);
                  new_contact_surfel_group->add_surfel_to_group(surfel, primary_type);
                  surfel->set_group(new_contact_surfel_group);
                  //promotes all associated secondary that have not been reasigned yet, i.e. still in the same group
                  for (sCONTACT_WEIGHT_ITEM &weight : g_contact_info[surfel->id()].m_weights) {
                    SHOB_ID secondary_id = weight.m_opposite_surfel_id;
                    sSURFEL* secondary_surfel = regular_surfel_from_id(secondary_id, STP_COND_REALM);
                    if (secondary_surfel->m_group == group) {
                      // msg_print("global->local secondary %d %d", surfel->id(), secondary_id);
                      contact_surfel_group->remove_surfel_from_group(secondary_surfel, secondary_type);
                      new_contact_surfel_group->add_surfel_to_group(secondary_surfel, secondary_type);
                      secondary_surfel->set_group(new_contact_surfel_group);
                    }
                  }
                }
                surfel = next_surfel;
              }
            }
            primary_type = static_cast<CONTACT_SURFEL_TYPE>((int)primary_type + 1);
          }
        }
      }
    }
  }
}

/** @brief Completes the initialization of contact surfel groups, which distinguishes subtypes within

Once all surfels are in the correct contact groups after fsets are initialized:
 - move within the group the surfels to their correct subtypes and link all subtypes to be traversed 
 - load contact weights and allocate storage memory for accumulation of heat fluxes
 - allocate storaged memory and assign accumulation indices for averaged contact
 - clears up global variables no longer needed
*/
VOID complete_initialization_contact_surfel_groups() {
  // STEP 1:
  // Creates a set of secondaries that interact with primaries, to distinguish later which ones should be promote to
  // primary_seconday and which ones to primary.
  // Additionally, evaluate the contact area ratio between primary and secondary for each contact item. If the ratio
  // exceeds a given threshold, then we limit the contact to the smaller area in a similar way as done for the averaged
  // contact area calculation. This threshold could be a configurable parameter, but we pick for now the same value used
  // for averaged contact area: 1.15.
  // (Note: if we enable extending the contact to the whole areas, then we should skip the weights adjustment)
  std::unordered_set<SHOB_ID> secondaries;
  sdFLOAT area_ratio_threshold = 1.15;
  for (auto & [primary_id, contact_weight_set] : g_contact_info) {
    for (auto& weight : contact_weight_set.m_weights) {
      secondaries.insert(weight.m_opposite_surfel_id);
      //Negative weights are passed by CP to identify reversed contacts where contact is processed by the secondaries.
      //Time now to set them to positive as we traverse the contact table and right before we will move them to the 
      //correct type in STEP 2 below. 
      if (weight.m_weight < 0.0) {
        weight.m_weight = -weight.m_weight;
        weight.m_opposite_weight = -weight.m_opposite_weight;
      }
      //Cap weights if contact areas differ significantly between primary & secondary
      SURFEL primary_surfel = regular_surfel_from_id(primary_id, STP_COND_REALM);
      SHOB_ID secondary_id = weight.m_opposite_surfel_id;
      SURFEL secondary_surfel = regular_surfel_from_id(secondary_id, STP_COND_REALM);
      sdFLOAT secondary_surfel_area = secondary_surfel->area();
      if (secondary_surfel->scale() != primary_surfel->scale()) { //scales secondary to be consistent with the primary
        asINT32 scale_diff = secondary_surfel->scale() - primary_surfel->scale(); //scale_from - scale_to
        sdFLOAT length_scaling_factor = vr_length_scale_factor(scale_diff);
        secondary_surfel_area *= length_scaling_factor * length_scaling_factor;
        //additionally, should recv accumulated data from ghost when the ghost is in a finer scale
        if (secondary_surfel->is_ghost() && (secondary_surfel->scale() > primary_surfel->scale())) {
          secondaries.insert(primary_id);
        }
      }
      sdFLOAT primary_area = primary_surfel->area() * weight.m_weight;
      sdFLOAT secondary_area = secondary_surfel_area * weight.m_opposite_weight;
      if ((primary_area > secondary_area) && (primary_area / secondary_area > area_ratio_threshold)) {
        weight.m_weight = secondary_area / primary_surfel->area();
      } else if ((secondary_area > primary_area) && (secondary_area / primary_area > area_ratio_threshold)) {
        weight.m_opposite_weight = primary_area / secondary_surfel_area;
      }
      // Enable for debugging to print the coordinates of the surfel interactions
      // msg_print("%d %d", primary_id, secondary_id);
      // msg_print("%d %d %d %d %.4g %.4g %.4g : %d %d %d %d %.4g %.4g %.4g", 
      //            primary_id, primary_surfel->scale(), primary_surfel->is_fringe(), primary_surfel->is_ghost(),
      //            primary_surfel->centroid[0](),  primary_surfel->centroid[1](),  primary_surfel->centroid[2](),
      //            secondary_id, secondary_surfel->scale(), secondary_surfel->is_fringe(), secondary_surfel->is_ghost(),
      //            secondary_surfel->centroid[0](),  secondary_surfel->centroid[1](),  secondary_surfel->centroid[2]());
    }
  }
  // STEP 2:
  // Move primaries and secondaries to primary_secondary subset if needed
  std::unordered_set<SHOB_ID> moved_primaries;
  ccDOTIMES(scale, sim.num_scales) {
    DO_CONTACT_SURFELS_GROUPS_OF_SCALE(contact_surfel_group, scale) {
      // Surfels loaded in contact surfel groups as primary/secondary based on the surfel flags. However, the order
      // might have been reversed based on the groups processing order. As a result, some primary surfels should be
      // treated as secondary if:
      // - cp reversed the order when parsing the contact table, so the primary surfel is not even in g_contact_info
      // - all its interactions were flipped to be handled by secondaries when promoting interior to fringe2, so the primary
      //   has an entry in g_contact_info with no weights
      if (contact_surfel_group->shob_head_ptr(CONTACT_SURFEL_PRIMARY)!=NULL) {
        for (sSURFEL *surfel = contact_surfel_group->shob_head_ptr(CONTACT_SURFEL_PRIMARY), 
                    *last_surfel = contact_surfel_group->shob_tail_ptr(CONTACT_SURFEL_PRIMARY)->m_next; 
                    surfel != last_surfel; ) {
          sSURFEL* next_surfel = surfel->m_next;
          auto it_primary = g_contact_info.find(surfel->id());
          if (it_primary == g_contact_info.end() || it_primary->second.m_weights.empty()) {
            contact_surfel_group->remove_surfel_from_group(surfel, CONTACT_SURFEL_PRIMARY);
            contact_surfel_group->add_surfel_to_group(surfel, CONTACT_SURFEL_SECONDARY);
            moved_primaries.insert(surfel->id());
          } else if (secondaries.find(surfel->id()) != secondaries.end()) {
            //this primary was tagged as secondary because should receive accumulation data from ghosts, so we
            //categorized it as primary_secondary to allocate accumulation storage
            contact_surfel_group->remove_surfel_from_group(surfel, CONTACT_SURFEL_PRIMARY);
            contact_surfel_group->add_surfel_to_group(surfel, CONTACT_SURFEL_PRIMARY_SECONDARY);
            moved_primaries.insert(surfel->id());
          }
          surfel = next_surfel;
        }
      }
      //If secondary has weights, needs to be moved to primary_secondary (note that if it does not have weights, it was
      //a primary whose all interactions were reversed so became a secondary and should not be touched)
      //Additionally, those secondaries that do not interact with other primaries are moved directly to primary category
      //to avoid allocating permanent accumulation storage in the contact surfel group
      if (contact_surfel_group->shob_head_ptr(CONTACT_SURFEL_SECONDARY)!=NULL) {
        for(sSURFEL *surfel = contact_surfel_group->shob_head_ptr(CONTACT_SURFEL_SECONDARY),
                    *last_surfel = contact_surfel_group->shob_tail_ptr(CONTACT_SURFEL_SECONDARY)->m_next;
                    surfel != last_surfel; ) {
          sSURFEL* next_surfel = surfel->m_next;
          auto it_secondary = g_contact_info.find(surfel->id());
          if (it_secondary != g_contact_info.end() && !it_secondary->second.m_weights.empty()) {
            contact_surfel_group->remove_surfel_from_group(surfel, CONTACT_SURFEL_SECONDARY);
            contact_surfel_group->add_surfel_to_group(surfel, (secondaries.find(surfel->id()) == secondaries.end())
                                                              ? CONTACT_SURFEL_PRIMARY
                                                              : CONTACT_SURFEL_PRIMARY_SECONDARY);
            //checks its weights to see if it needs to move some primary to primary_secondary
            for (auto &weight : it_secondary->second.m_weights) {
              SHOB_ID primary_id = weight.m_opposite_surfel_id;
              if (moved_primaries.find(primary_id) == moved_primaries.end()) {
                SURFEL primary_surfel = regular_surfel_from_id(primary_id, STP_COND_REALM);
                if (!primary_surfel->is_ghost()) {
                  CONTACT_SURFEL_GROUP primary_cs_group = static_cast<CONTACT_SURFEL_GROUP>(primary_surfel->m_group);
                  if (primary_surfel->is_odd()) {
                    //we need to move first its clone (even), which should be in the same group than the odd one, to
                    //respect the requirement that the even is processed before the odd during seeding
                    SURFEL even_primary_surfel = primary_surfel->clone_surfel();
                    cassert(primary_surfel->m_group == even_primary_surfel->m_group);
                    SHOB_ID even_primary_id = even_primary_surfel->id();
                    if (moved_primaries.find(even_primary_id) == moved_primaries.end()) {
                      auto it_even_primary = g_contact_info.find(even_primary_id);
                      primary_cs_group->remove_surfel_from_group(even_primary_surfel, CONTACT_SURFEL_PRIMARY);
                      //
                      //if it has no weights, it effectively should be handled as secondary (note that primary_cs_group
                      //might not have been traversed yet, so primaries with no weights are still tagged as primary)
                      if (it_even_primary == g_contact_info.end() || it_even_primary->second.m_weights.empty()) {
                        primary_cs_group->add_surfel_to_group(even_primary_surfel, CONTACT_SURFEL_SECONDARY);
                      } else {
                        primary_cs_group->add_surfel_to_group(even_primary_surfel, CONTACT_SURFEL_PRIMARY_SECONDARY);
                      }
                      moved_primaries.insert(even_primary_id);
                    }
                  }
                  primary_cs_group->remove_surfel_from_group(primary_surfel, CONTACT_SURFEL_PRIMARY);
                  auto it_primary = g_contact_info.find(primary_id);
                  if (it_primary == g_contact_info.end() || it_primary->second.m_weights.empty()) {
                    primary_cs_group->add_surfel_to_group(primary_surfel, CONTACT_SURFEL_SECONDARY);
                  } else {
                    primary_cs_group->add_surfel_to_group(primary_surfel, CONTACT_SURFEL_PRIMARY_SECONDARY);
                  }
                }
                //Tags primary as moved even if it is a ghost, so it is not checked again later
                moved_primaries.insert(primary_id);
              }
            }
          }
          surfel = next_surfel;
        }
      }
    }
  }
  // STEP 3: 
  // Link subsets within the groups and fill contact weights in groups from global map
  // - Since FRINGE2 surfels are processed before, we link first the surfels in FRINGE and INTERIOR groups to allocate space 
  //   for accumulation for secondaries, so when linking the FRINGE2 we can point at this accumulation storage.
  // - Similarly, since finer scales are processed before, we follow here the reverse order where we loop from coarse to fine.
  // - Within each scale, multiple groups can be present, so needs to traverse them in reverse order as well
  // - We create locally a map that provides the index within each map of the contact info
  std::unordered_map<SHOB_ID, asINT32> id_to_info_index_map; //this map provides the index 
  std::unordered_map<sCONTACT_SURFEL_GROUP*, std::vector<asINT32>> deferred_info_map;
  for (asINT32 type =  nSHOB_CATEGORIES::N_CONTACT_SURFEL_TYPES-1; type >= 0; type--) {
    asINT32 contact_type =  nSHOB_CATEGORIES::CONTACT_SURFEL_TYPES[type];
    for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
      DO_CONTACT_SURFELS_GROUPS_OF_SCALE_TYPE_REVERSE(contact_surfel_group, scale, contact_type) {
        contact_surfel_group->check_even_odd_order();
        contact_surfel_group->link_shob_types_and_fill_contact_weights(id_to_info_index_map, deferred_info_map);
      }
    }
  }
  for (auto& [contact_surfel_group, secondaries] : deferred_info_map) {
    contact_surfel_group->fill_deferred_secondaries(id_to_info_index_map, secondaries);
  }
  //STEP 5:
  //Now that all surfels have been linked, we can use the macros to traverse the surfels involved in averaged contact
  //and fill the related variables needed for accumulation, done by the surfel groups as the first step for averaging
  std::unordered_map<uINT32, uINT32> global_to_local_average_index_map; //to get global averaging index
  ccDOTIMES(scale, sim.num_scales) {
    DO_CONTACT_SURFELS_GROUPS_OF_SCALE(contact_surfel_group, scale) {
      std::unordered_map<uINT16, asINT32> face_to_accum_index_map; //to get local accumulation index within the group
      DO_CONTACT_SURFELS_OF_GROUP_AVERAGED(surfel, contact_surfel_group, contact_surfel_type) {
        uINT16 face_index = surfel->face_index();
        //Get global average index
        uINT32 idx_avg_global, idx_avg_local;
        idx_avg_global = g_thermal_averaged_contacts.get_global_index(face_index);
        auto it_avg = global_to_local_average_index_map.find(idx_avg_global);
        if (it_avg == global_to_local_average_index_map.end()) {//we need to create a new entry for this averaged contact
          idx_avg_local = g_thermal_averaged_contacts.size_local();
          g_thermal_averaged_contacts.add_contact(idx_avg_global);
          global_to_local_average_index_map.insert({idx_avg_global, idx_avg_local});
        } else {
          idx_avg_local = it_avg->second;
        }
        //Gets accumulation index
        size_t idx_accum;
        auto it_accum = face_to_accum_index_map.find(face_index);
        if (it_accum == face_to_accum_index_map.end()) { //adds the new accumulation
          BOOLEAN is_primary = (contact_surfel_type == CONTACT_SURFEL_PRIMARY_AVERAGED);
          std::vector<sCONTACT_ACCUMULATION> &accumulations = contact_surfel_group->m_averaged_accumulation;
          idx_accum = accumulations.size();
          accumulations.emplace_back(is_primary, idx_avg_local);
          face_to_accum_index_map.insert({face_index, idx_accum});
        } else {
          idx_accum = it_accum->second;
        }
        //Stores index to be used as we traverse the surfels
        contact_surfel_group->add_averaged_contact_index(idx_accum);
      }
      //As accumulators are emplaced into m_averaged_accumulation above, the vector might need to do some reallocation
      //invalidating the pointers. Thus, we can only link each accumulator to the global averaging data structure now,
      //once m_averaged_accumulation is not modified anymore
      for (auto &accumulation : contact_surfel_group->m_averaged_accumulation) {
        sCONTACT_ACCUMULATION::sAVERAGER_INDEX_AND_FACE avg = accumulation.averager_index_and_face();
        g_thermal_averaged_contacts[avg.idx].maybe_load_accumulation(avg.is_primary, &accumulation, scale);
      }
    }
  }
  // STEP 4: 
  // All contact weights info is now loaded into the group, we can clear memory allocated to the global set of weights.
  // g_contact_info.clear() does not release memory, use swap trick instead
  std::unordered_map<SHOB_ID,sCONTACT_WEIGHT_SET>().swap(g_contact_info);
  std::vector<std::vector<std::map<SHOB_ID,asINT32>>>().swap(g_contact_comm_accumulator);
}

VOID complete_initialization_averaged_contacts() {
  // Once all the contacts have been added to g_thermal_averaged_contacts
  // in complete_initialization_contact_surfel_groups(), the communicators can be created
  g_thermal_averaged_contacts.create_contact_averaging_communicators();
  // Do a global reduction across SPs to determine the coarsest scale for each face
  if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
    g_thermal_averaged_contacts.complete_initialization();
  }
  // Clears also the contact averaged physics descriptors list and face ids map since are no longer used
  g_thermal_averaged_contacts.clear_unused();
}
 
// Enable sanity check of various shob types and make sure everything is consistent
#define SANITY_CHECKS         1

// VR coarse and vr fine ublks should be processed by the same strand
static void sanity_check_for_vr_ublks()
{
  ccDOTIMES(realm, STP_N_REALMS) {
    ccDOTIMES(i, g_ublk_table[realm].n_ublks()) {
      UBLK ublk = g_ublk_table[realm].ublk(i);
      // Some ublks (e.g. solid ones) don't belong to any group, ignore them here
      if (ublk->is_solid() || ublk->is_ghost())
        continue;
      if (ublk->is_vr_coarse()) {
        sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
        ccDOTIMES(i, ubFLOAT::N_VOXELS) {
          UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
          if (fine_ublk == NULL || fine_ublk->is_solid()) 
            continue;
          if (ublk->is_fringe() && !fine_ublk->is_fringe())
            msg_internal_error("VR coarse ublk %d is processed by the fringe strand while vr fine ublk %d is not", ublk->id(), fine_ublk->id());
          if (!ublk->is_fringe() && fine_ublk->is_fringe())
            msg_internal_error("VR fine ublk %d is processed by the fringe strand while vr coarse ublk %d is not", fine_ublk->id(), ublk->id());
          if (ublk->is_fringe2() && !fine_ublk->is_fringe2())
            msg_internal_error("VR coarse ublk %d is processed by the fringe2 strand while vr fine ublk %d is not", ublk->id(), fine_ublk->id());
          if (!ublk->is_fringe2() && fine_ublk->is_fringe2())
            msg_internal_error("VR fine ublk %d is processed by the fringe2 strand while vr coarse ublk %d is not", fine_ublk->id(), ublk->id());
        }
      }
    }
  }
}

// Ghost surfel should not interact with fringe2 or interior ublks
static void sanity_check_for_ghost_surfel(SURFEL surfel)
{
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (!ublk->is_fringe()) {
      msg_internal_error("Ghost surfel %d should not interact with fringe2 or interior ublk %d", surfel->id(), ublk->id()); 
    }
  }
}

static void sanity_check_for_ghost_shobs()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    // Ghost surfels should not interact with fringe2 nearblks or interior ublks
    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        sanity_check_for_ghost_surfel(surfel);
      }
    }

    // Ghost ublks should not interact with fringe2 nearblks or interior ublks
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK2_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        if (ublk->is_vr_fine() && is_ublk_neighbor_ghost(ublk)) {
          msg_internal_error("Interior farblk2 %d should not have ghost ublk neighbor.", ublk->id());
        }
      }
    }
  }
}

static VOID verify_all_neighbors_exist_for_infblk1(UBLK ublk) {

#if BUILD_5G_LATTICE
  if (!sim.is_large_pore)
#endif
    assert(!ublk->has_two_copies());
  
  const auto& box_access = ublk->m_box_access;

  for (int latvec = 0; latvec < N_MOVING_STATES; latvec++) {
    const sINT16 offset[3] = { state_vx(latvec), state_vy(latvec), state_vz(latvec) };

    bool ignore_z_component = sim.is_2d() && offset[2];
    
    bool ignore_speed_2 = (abs(offset[0]) == 2) ||
                          (abs(offset[1]) == 2) ||
                          (abs(offset[2]) == 2);
    
    if (ignore_z_component || ignore_speed_2) { continue; }

    auto tagged_neighbor = box_access.neighbor_ublk(offset);
    if (!tagged_neighbor.is_ublk()) {
      msg_internal_error("Interior UBLK %d at location (%d, %d, %d), has empty neighbor for lattice vector %d\n",
			 ublk->id(), ublk->location(0), ublk->location(1), ublk->location(2),
			 latvec);
    }
  }
}

// Interior ublks should not have any ghost neighbor ublks
static void sanity_check_for_interior_ublks()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK1_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        if (ublk->is_vr_fine() && is_ublk_neighbor_ghost(ublk)) {
          msg_internal_error("Interior farblk1 %d should not have ghost ublk neighbor.", ublk->id());
        }

        verify_all_neighbors_exist_for_infblk1(ublk);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        if (ublk->is_vr_fine() && is_ublk_neighbor_ghost(ublk)) {
          msg_internal_error("Interior nearblk %d should not have ghost ublk neighbor.", ublk->id());
        }
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE2_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(ublk, group) {
        if (is_ublk_neighbor_ghost(ublk)) {
          msg_internal_error("Fringe2 nearblk %d should not have ghost ublk neighbor.", ublk->id());
        }
      }
    }
  }
}

static void sanity_check_for_interior_surfel(SURFEL surfel)
{
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (ublk->is_ghost()) { 
      msg_internal_error("Interior surfel %d interacts with ghost ublk %d", surfel->id(), ublk->id()); 
    }
    if (ublk->is_fringe()) { 
      msg_internal_error("Interior surfel %d interacts with fringe nearblk %d", surfel->id(), ublk->id()); 
    }
    if (ublk->is_fringe2()) { 
      msg_internal_error("Interior surfel %d interacts with fringe2 nearblk %d", surfel->id(), ublk->id()); 
    }
  }
  if (surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    for (asINT32 is = 0; is < n_src_surfels; is++) {
      sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
      SURFEL src_surfel = tagged_src_surfel.src_surfel();
      if (src_surfel->is_sliding_rf())
        //msg_print("interior surfel interacting with mlrf surfel %d", src_surfel->id());
      if (src_surfel->is_ghost()) {
        msg_internal_error("Interior surfel %d interacts with ghost surfel %d", surfel->id(), src_surfel->id());
      }
      if (src_surfel->is_fringe()) {
        msg_internal_error("Interior surfel %d interacts with fringe surfel %d", surfel->id(), src_surfel->id());
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
    }
  } 
}
    
static VOID surfel_sanity_check(SURFEL surfel, VOID (*func)(SURFEL))
{
  func(surfel);
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      func(mirror_surfel);
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

static VOID surfel_pair_sanity_check(SURFEL_PAIR surfel_pair, VOID (*func)(SURFEL))
{
  ccDOTIMES(s, 2) {
    SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
    func(surfel);
    if (surfel->has_mirror()) {
      sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
      while (mirror_data) {
        SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
        func(mirror_surfel);
        mirror_data = mirror_data->m_next_mirror_data;
      }
    }
  }
}
// Interior surfels should not interact with fringe nearblks, fringe2 nearblks, fringe surfels, ghost surfels, ghost ublks
// Mirror surfels v2s and s2v is done together with the real surfels, so they behaves the same like real surfels and should be checked too.
static void sanity_check_for_interior_surfels()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          surfel_sanity_check(surfel, sanity_check_for_interior_surfel);
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      { 
        break;  // sampling surfels only participate in v2s, not s2v, thus they have no impact on fringe2 nearblks
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        DO_SURFEL_PAIRS_OF_GROUP(surfel_pair, sgroup) {
          surfel_pair_sanity_check(surfel_pair, sanity_check_for_interior_surfel);
        }
        break;
      }
      default:
        break;
      }
    }
  }
}

// Fringe2 surfel should not interact with ghost surfels or ghost ublks
static void sanity_check_for_fringe2_surfel(SURFEL surfel)
{
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (ublk->is_ghost()) { 
      msg_internal_error("Fringe2 surfel %d interacts with ghost ublk %d", surfel->id(), ublk->id()); 
    }
  }
  if (surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    for (asINT32 is = 0; is < n_src_surfels; is++) {
      sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
      SURFEL src_surfel = tagged_src_surfel.src_surfel();
      if (src_surfel->is_ghost()) {
        msg_internal_error("Fringe2 surfel %d interacts with ghost surfel %d", surfel->id(), src_surfel->id());
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());
    }
  } 
}


// Fringe2 surfels should not interact with any ghost surfels or ghost ublks
static void sanity_check_for_fringe2_surfels()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE2_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          surfel_sanity_check(surfel, sanity_check_for_fringe2_surfel);
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {  
        break;  // sampling surfels only participate in v2s, not s2v, thus they have no impact on fringe2 nearblks
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        DO_SURFEL_PAIRS_OF_GROUP(surfel_pair, sgroup) {
          surfel_pair_sanity_check(surfel_pair, sanity_check_for_fringe2_surfel);
        }
        break;
      }
      default:
        break;
      }
    }
  }
}

// Fringe surfel should not interact with interior ublks
static void sanity_check_for_fringe_surfel(SURFEL surfel)
{
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (!ublk->is_fringe() && !ublk->is_fringe2() && !ublk->is_solid() && !ublk->is_ghost()) {   // It is OK to interact with interior solid ublks or interior ghost ublks
      msg_internal_error("Fringe surfel %d interacts with interior ublk %d", surfel->id(), ublk->id());
    }
  }
}
  
// Fringe surfels should not interact with any interior ublks
static void sanity_check_for_fringe_surfels()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE_SURFEL_BASE_GROUP_TYPE) {
      switch(group->m_supertype) {
      case FLOW_DYN_SURFEL_GROUP_SUPERTYPE: 
      case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE: 
      case FLOW_WSURFEL_GROUP_SUPERTYPE:
      case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
      case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          surfel_sanity_check(surfel, sanity_check_for_fringe_surfel);
        }
        break;
      }
      case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {  
        break;  // sampling surfels only participate in v2s, not s2v, thus they have no impact on fringe2 nearblks
      }
      case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP sgroup = static_cast<SURFEL_PAIR_GROUP>(group);
        DO_SURFEL_PAIRS_OF_GROUP(surfel_pair, sgroup) {
          surfel_pair_sanity_check(surfel_pair, sanity_check_for_fringe_surfel);
        }
        break;
      }
      default:
        break;
      }
    }
  }
}

static void sanity_check_for_sliding_surfels()
{
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_group, scale) {
      mlrf_group->sanity_check_for_sliding_surfels();
    }
  }
}

/* @fcn verify_interior1_ublks
 * Verify that UBLKs NOT marked as PDE_ADVECT by the discretizer satisfy
 * the requirements of the simulator. This is only called when ENABLE_CONSISTENCY_CHECKS
 * is set to true
 */
VOID verify_interior1_ublks() {
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK1_GROUP_TYPE) {
      for(sUBLK* ublk = group->shob_ptrs(); ublk != NULL;)
      {
        UBLK next_ublk = ublk? ublk->m_next : NULL;
        if(!is_ublk_valid_for_interior_farblk_1(ublk)){
	  msg_error("UBLK %d, allocated with single copy of states "
	            "does not meet the Simulator requirements to be part of INFBLK1 strand",
		    ublk->id());
	}
	ublk = next_ublk;
      }      
    }
  }
}

/* @fcn mark_interior1_ublks_with_two_copy_nbrs
 * Marks INFBLK1 ublks as having two copied neighbors or not. This allows
 * taking an optimized path in SWAP advection
 */
VOID mark_interior1_ublks_with_two_copy_nbrs() {
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK1_GROUP_TYPE) {
      for(sUBLK* ublk = group->shob_ptrs(); ublk != NULL; )
      {
        UBLK next_ublk = ublk? ublk->m_next : NULL;
	auto test_nbr = [](sUBLK* ublk)->BOOLEAN {return ublk->has_two_copies();};
	BOOLEAN has_nbr_with_two_copies = check_if_any_ublk_neighbor_satisfies_func(ublk,test_nbr);
	if (has_nbr_with_two_copies) {
	  ublk->set_ublk_has_two_copy_neighbor();
	} 
	ublk = next_ublk;
      }      
    }
  }
}

VOID update_shob_group_types()
{
  asINT32 scale;
  promote_fringe_vr_fine_neighbor_ublks_to_fringe();
  
  promote_interior_surfels_to_fringe_for_fixing_disc();
  
  //Promote interior surfels to fringe if the mirror surfel satisfy the criteria for being promoted to fringe
  promote_interior_surfels_to_fringe();

  promote_interior_sampling_surfels_to_fringe();

  promote_interior_nearblks_to_fringe2();

  divide_interior1_interior2_ublks();

#if ENABLE_CONSISTENCY_CHECKS
  verify_interior1_ublks();
#endif
  
  mark_interior1_ublks_with_two_copy_nbrs();
  
  //To mark sliding mesh nearblks
  if (is_sliding_mesh_present())
    promote_ublks_to_sliding_nearblks();

  promote_interior_surfels_to_fringe2();

  //contact_surfel fsets initialize all contact_surfels as global by default, now it is time to check if some
  //of them can be promoted to local, 
  promote_global_contact_surfels_to_local();
  
  //Update all surfel groups using surfel base groups AGAIN since the surfel base groups have been changed. 
  //Note that surfel groups are only used for initialization. 
  initialize_surfel_group_fsets();

  //Contact surfel groups distinguish several subtypes that require additional work.
  complete_initialization_contact_surfel_groups();

  if (g_thermal_averaged_contacts.is_enabled()) {
    complete_initialization_averaged_contacts();
  }

#if SANITY_CHECKS

  sanity_check_for_vr_ublks();
  sanity_check_for_ghost_shobs();
  sanity_check_for_interior_ublks();

  sanity_check_for_interior_surfels();
  sanity_check_for_fringe2_surfels();
  sanity_check_for_fringe_surfels();

  sanity_check_for_sliding_surfels();
#endif
}

const char* g_ublk_group_type_names[N_UBLK_GROUP_TYPES] = {
  //"INVALID_UBLK_GROUP_TYPE",
  "FRINGE_FARBLK_GROUP_TYPE",
  "FRINGE_NEARBLK_GROUP_TYPE",
  "FRINGE2_FARBLK_GROUP_TYPE",
  "FRINGE2_NEARBLK_GROUP_TYPE",
  "SLIDING_NEARBLK_GROUP_TYPE",        // Near ublks that interact with mlrf surfels
  "INTERIOR_FARBLK2_GROUP_TYPE",       // interior far ublks with 2 sets of LB states
  "INTERIOR_FARBLK1_GROUP_TYPE",       // interior far ublks with 1 set of LB states
  "INTERIOR_NEARBLK_GROUP_TYPE",       // interior near ublks
  "VRFINE_FRINGE_FARBLK_GROUP_TYPE",   // VR fine far ublks on the fringe
  "VRFINE_FRINGE_NEARBLK_GROUP_TYPE",  // VR fine near ublks on the fringe
  "VRFINE_FRINGE2_NEARBLK_GROUP_TYPE", // VR fine near ublks in fringe2 layer
  "VRFINE_SLIDING_NEARBLK_GROUP_TYPE", // VR fine Near ublks that interact with mlrf surfels
  "VRFINE_FARBLK_GROUP_TYPE",          // interior VR fine far ublks that use PDE ADVECT
  "VRFINE_NEARBLK_GROUP_TYPE",         // interior VR fine near ublks that use PDE ADVECT
   "MIRROR_UBLK_GROUP_TYPE",            // DO NOT NEED MIRROR GROUP
  "VRFINE_GHOSTBLK_GROUP_TYPE",        // VR fine ublks underlying coarse ghost ublks
  "GHOST_UBLK_GROUP_TYPE"};



const char* g_surfel_group_type_names[N_SURFEL_GROUP_TYPES] = {
  "FRINGE_FLOW_DYN_SURFEL_GROUP_TYPE",
  "FRINGE_CONDUCTION_DYN_SURFEL_GROUP_TYPE",
  "FRINGE_FLOW_WSURFEL_GROUP_TYPE",
  "FRINGE_CONDUCTION_WSURFEL_GROUP_TYPE",
  "FRINGE2_FLOW_DYN_SURFEL_GROUP_TYPE",
  "FRINGE2_FLOW_WSURFEL_GROUP_TYPE",
  "FRINGE2_CONDUCTION_WSURFEL_GROUP_TYPE",
  "INTERIOR_FLOW_DYN_SURFEL_GROUP_TYPE",
  "INTERIOR_FLOW_WSURFEL_GROUP_TYPE",
  "INTERIOR_CONDUCTION_WSURFEL_GROUP_TYPE",
  "MLRF_SURFEL_GROUP_TYPE",
  "MIRROR_SURFEL_GROUP_TYPE",
  "GHOST_SURFEL_GROUP_TYPE"};
