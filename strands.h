/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_STRANDS_H
#define _SIMENG_STRANDS_H

#include "common_sp.h"
#include "shob_groups.h"
#include "surfel_process_control.h"
#include "ublk_process_control.h"
#include "strand_enum.h"

class sSTRAND {
public:
  sUBLK_PROCESS_CONTROL   m_ublk_process_control;
  sSURFEL_PROCESS_CONTROL m_surfel_process_control;
  SURFEL_BASE_GROUP_TYPE  m_surfel_base_group_type;
  UBLK_GROUP_TYPE         m_ublk_group_type;
  STRAND                  m_index;

  sSTRAND(STRAND strand_index, UBLK_GROUP_TYPE ublk_group_type,
          SURFEL_BASE_GROUP_TYPE surfel_base_group_type)  {
    m_index = strand_index;
    m_surfel_base_group_type = surfel_base_group_type;
    m_ublk_group_type = ublk_group_type;
  }
  virtual ~sSTRAND() {

  }
  virtual VOID run() = 0;
};

class sFRINGE_WSURFELS_SAMPLING_STRAND : public sSTRAND {
public:
  sFRINGE_WSURFELS_SAMPLING_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE_WSURFELS_HFC_STRAND : public sSTRAND {
public:
  sFRINGE_WSURFELS_HFC_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE2_WSURFELS_SAMPLING_STRAND : public sSTRAND {
public:
  sFRINGE2_WSURFELS_SAMPLING_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE2_WSURFELS_HFC_STRAND : public sSTRAND {
public:
  sFRINGE2_WSURFELS_HFC_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sINTERIOR_WSURFELS_SAMPLING_STRAND : public sSTRAND {
public:
  sINTERIOR_WSURFELS_SAMPLING_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sINTERIOR_WSURFELS_HFC_STRAND : public sSTRAND {
public:
  sINTERIOR_WSURFELS_HFC_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE_SURFELS_STRAND : public sSTRAND {
public:
  sFRINGE_SURFELS_STRAND(STRAND strand_index,
                         SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                           sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE2_SURFELS_STRAND : public sSTRAND {
public:
  sFRINGE2_SURFELS_STRAND(STRAND strand_index,
                          SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                            sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFRINGE_FARBLKS_A_STRAND : public sSTRAND {
public:
  sFRINGE_FARBLKS_A_STRAND(STRAND strand_index,
                           UBLK_GROUP_TYPE ublk_group_type):
                             sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_FARBLKS_B_STRAND : public sSTRAND {
public:
  sFRINGE_FARBLKS_B_STRAND(STRAND strand_index,
                           UBLK_GROUP_TYPE ublk_group_type):
                             sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_NEARBLKS_A_STRAND : public sSTRAND {
public:
  sFRINGE_NEARBLKS_A_STRAND(STRAND strand_index,
                            UBLK_GROUP_TYPE ublk_group_type):
                              sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_NEARBLKS_B_STRAND : public sSTRAND {
public:
  sFRINGE_NEARBLKS_B_STRAND(STRAND strand_index,
                            UBLK_GROUP_TYPE ublk_group_type):
                              sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_SURFELS_A_STRAND : public sSTRAND {
public:
  sSLIDING_SURFELS_A_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_SURFELS_B_STRAND : public sSTRAND {
public:
  sSLIDING_SURFELS_B_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_SURFELS_C_STRAND : public sSTRAND {
public:
  sSLIDING_SURFELS_C_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE2_NEARBLKS_A_STRAND : public sSTRAND {
public:
  sFRINGE2_NEARBLKS_A_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE2_NEARBLKS_B_STRAND : public sSTRAND {
public:
  sFRINGE2_NEARBLKS_B_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_NEARBLKS_A_STRAND : public sSTRAND {
public:
  sSLIDING_NEARBLKS_A_STRAND(STRAND strand_index, UBLK_GROUP_TYPE ublk_group_type):
    sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_NEARBLKS_B_STRAND : public sSTRAND {
public:
  sSLIDING_NEARBLKS_B_STRAND(STRAND strand_index, UBLK_GROUP_TYPE ublk_group_type):
    sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_NEAR_SHOBS_A_STRAND : public sSTRAND {
public:
  sINTERIOR_NEAR_SHOBS_A_STRAND(STRAND strand_index,
                                UBLK_GROUP_TYPE ublk_group_type,
                                SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                                  sSTRAND(strand_index, ublk_group_type, surfel_base_group_type) {}
  VOID run();
};

class sINTERIOR_NEAR_SHOBS_B_STRAND : public sSTRAND {
public:
  sINTERIOR_NEAR_SHOBS_B_STRAND(STRAND strand_index,
                                UBLK_GROUP_TYPE ublk_group_type,
                                SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                                  sSTRAND(strand_index, ublk_group_type, surfel_base_group_type) {}
  VOID run();
};

class sRADIATION_STRAND : public sSTRAND {
public:
  bool m_reported_wait;
  sRADIATION_STRAND():
    sSTRAND(RADIATION_STRAND, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE),
    m_reported_wait(false) {}
  VOID run();
};

class sINTERIOR_FARBLKS2_A_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS2_A_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_FARBLKS2_B_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS2_B_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_FARBLKS1_A_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS1_A_STRAND(STRAND strand_index,
                              UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_FARBLKS1_B_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS1_B_STRAND(STRAND strand_index,
                              UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_BSURFELS_STRAND : public sSTRAND {
public:
  sFRINGE_BSURFELS_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE2_BSURFELS_STRAND : public sSTRAND {
public:
  sFRINGE2_BSURFELS_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_BSURFELS_STRAND : public sSTRAND {
public:
  sINTERIOR_BSURFELS_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

// Particle modeling strands
class sFILM_ACCUMULATION_STRAND: public sSTRAND {
public:
  sFILM_ACCUMULATION_STRAND(STRAND strand_index,
                            SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sFILM_KINEMATICS_STRAND: public sSTRAND {
public:
  sFILM_KINEMATICS_STRAND(STRAND strand_index,
                          SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, surfel_base_group_type) {}
  VOID run();
};

class sPARCEL_DYNAMICS_STRAND: public sSTRAND {
public:
  sPARCEL_DYNAMICS_STRAND(STRAND strand_index,
                          UBLK_GROUP_TYPE ublk_group_type):
    sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sTIMESTEP_UPDATE_STRAND : public sSTRAND {
public:
  sTIMESTEP_UPDATE_STRAND(STRAND strand_index):
    sSTRAND(strand_index, INVALID_UBLK_GROUP_TYPE, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

#if BUILD_5G_LATTICE
class sFRINGE2_NEARBLKS_C_STRAND : public sSTRAND {
public:
  sFRINGE2_NEARBLKS_C_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sSLIDING_NEARBLKS_C_STRAND : public sSTRAND {
public:
  sSLIDING_NEARBLKS_C_STRAND(STRAND strand_index, UBLK_GROUP_TYPE ublk_group_type):
    sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_NEAR_SHOBS_C_STRAND : public sSTRAND {
public:
  sINTERIOR_NEAR_SHOBS_C_STRAND(STRAND strand_index,
                                UBLK_GROUP_TYPE ublk_group_type,
                                SURFEL_BASE_GROUP_TYPE surfel_base_group_type):
                                  sSTRAND(strand_index, ublk_group_type, surfel_base_group_type) {}
  VOID run();
};

class sINTERIOR_FARBLKS2_C_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS2_C_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sINTERIOR_FARBLKS1_C_STRAND : public sSTRAND {
public:
  sINTERIOR_FARBLKS1_C_STRAND(STRAND strand_index,
                             UBLK_GROUP_TYPE ublk_group_type):
                               sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_FARBLKS_C_STRAND : public sSTRAND {
public:
  sFRINGE_FARBLKS_C_STRAND(STRAND strand_index,
                           UBLK_GROUP_TYPE ublk_group_type):
                             sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};

class sFRINGE_NEARBLKS_C_STRAND : public sSTRAND {
public:
  sFRINGE_NEARBLKS_C_STRAND(STRAND strand_index,
                            UBLK_GROUP_TYPE ublk_group_type):
                              sSTRAND(strand_index, ublk_group_type, INVALID_SURFEL_BASE_GROUP_TYPE) {}
  VOID run();
};
#endif

#endif
