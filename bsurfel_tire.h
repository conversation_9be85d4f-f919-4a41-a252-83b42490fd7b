/* ~~~CO<PERSON>Y<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Deforming Tires Implementation
 *
 * Dalon Work
 * Created Jul 29, 2019
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_BSURFEL_TIRE_H
#define _SIMENG_BSURFEL_TIRE_H

#include TIRE_H
#include THASH_H

#include "common_sp.h"
#include "memory_pool.h"
#include "neighbor_sp.h"

struct sSIM_TIRE;

struct sTIRE_VERTEX {
  sBG_POINT3d uvw;
  sBG_POINT3d xyz;
  uINT32 id;
  uINT32 ref_count;
  bool initialized; 
};

struct sTIRE_VERTEX_SMART_PTR {
private:
  sSIM_TIRE * m_parent;
  sTIRE_VERTEX * m_vertex;
public:

  sTIRE_VERTEX_SMART_PTR() : m_parent(nullptr), m_vertex(nullptr) {}

  sTIRE_VERTEX_SMART_PTR(sSIM_TIRE * parent, sTIRE_VERTEX * vertex) :
    m_parent(parent),
    m_vertex(vertex)
  {
    m_vertex->ref_count++;
  }

  sTIRE_VERTEX_SMART_PTR(const sTIRE_VERTEX_SMART_PTR&) = delete;
  sTIRE_VERTEX_SMART_PTR& operator = (const sTIRE_VERTEX_SMART_PTR&) = delete;

  sTIRE_VERTEX_SMART_PTR(sTIRE_VERTEX_SMART_PTR&& b)
  {
    this->m_parent = b.m_parent;
    this->m_vertex = b.m_vertex;
    b.nullify();
  }

  sTIRE_VERTEX_SMART_PTR& operator = (sTIRE_VERTEX_SMART_PTR&& b)
  {
    if (this != &b) {
      if (this->m_vertex) this->~sTIRE_VERTEX_SMART_PTR();
      this->m_parent = b.m_parent;
      this->m_vertex = b.m_vertex;
      b.nullify();
    }
    return *this;
  }

  void nullify()
  {
    m_parent = nullptr;
    m_vertex = nullptr;
  }

  ~sTIRE_VERTEX_SMART_PTR();


  sTIRE_VERTEX& operator * () { cassert(m_vertex); return *m_vertex; };
  sTIRE_VERTEX * operator -> () { cassert(m_vertex); return m_vertex; };

  friend bool operator < (const sTIRE_VERTEX_SMART_PTR& a, const sTIRE_VERTEX_SMART_PTR& b);
  friend bool operator == (const sTIRE_VERTEX_SMART_PTR& a, const sTIRE_VERTEX_SMART_PTR& b);

};

inline
bool operator < (const sTIRE_VERTEX_SMART_PTR& a, const sTIRE_VERTEX_SMART_PTR& b)
{
  return a.m_vertex < b.m_vertex;
}

inline
bool operator == (const sTIRE_VERTEX_SMART_PTR& a, const sTIRE_VERTEX_SMART_PTR& b)
{
  return a.m_vertex == b.m_vertex;
}

struct sGLOBAL_TO_TIRE_VERTEX_TRAITS {
  DGF_VERTEX_INDEX HashFcn(const DGF_VERTEX_INDEX &global_vertex_index) const
  { return global_vertex_index; }

  BOOLEAN EqualKeys(const DGF_VERTEX_INDEX &key1,
                    const DGF_VERTEX_INDEX &key2) const
  { return key1 == key2; }
};

struct cDGF_DEFORMING_TIRE;

struct sSIM_TIRE {
private:
  using sTIRE_VERTEX_HASH_TABLE = tHASH_TABLE<DGF_VERTEX_INDEX, sTIRE_VERTEX*, sGLOBAL_TO_TIRE_VERTEX_TRAITS>;
  Tire::cDEFORMING_TIRE m_dtire;
  tMEMORY_POOL<sTIRE_VERTEX, 1, 1000> m_vertex_pool;
  sTIRE_VERTEX_HASH_TABLE m_vertex_map;
  std::vector< std::vector< sTIRE_VERTEX_SMART_PTR > > m_vertices_to_comm;
  Dierckx::cSURFACE::sEVAL_DATA m_ed;
  std::unique_ptr<Tire::cSURFACE_POINT_FINDER> m_carcass_finder;


public:

  sSIM_TIRE() = default;
  ~sSIM_TIRE() = default;
  sSIM_TIRE(const sSIM_TIRE&) = delete;
  sSIM_TIRE(sSIM_TIRE&&) = delete;
  sSIM_TIRE& operator = (const sSIM_TIRE&) = delete;
  sSIM_TIRE& operator = (sSIM_TIRE&&) = delete;

  sSIM_TIRE(const cDGF_DEFORMING_TIRE& dgf_tire,
            const std::vector<double>& u_knots,
            const std::vector<double>& v_knots,
            const std::vector<double>& coeff_carcass,
            const std::vector<double>& coeff_wrap);

  void init_comm_buffers(STP_PROC n_neighbor_sps)
  {
    m_vertices_to_comm.resize(n_neighbor_sps);
  }

  void delete_vertex(sTIRE_VERTEX* vertex)
  {
    bool success = m_vertex_map.Delete(vertex->id);
    m_vertex_pool.push(vertex);
  }

  void vertex_needs_packing(cNEIGHBOR_SP nsp, sTIRE_VERTEX_SMART_PTR&& v)
  {
    m_vertices_to_comm[nsp.nsp()].push_back(std::move(v));
  }

  void pack_vertices(cNEIGHBOR_SP nsp, std::vector<unsigned char> &send_buffer);

  void unpack_vertices(std::vector<unsigned char>::iterator &recv_buffer);

  sTIRE_VERTEX_SMART_PTR get_vertex(DGF_VERTEX_INDEX gid)
  {
    sTIRE_VERTEX ** vertex_ptr = m_vertex_map.Get(gid);
    if (vertex_ptr == NULL) {
      sTIRE_VERTEX * new_vertex_ptr = m_vertex_pool.pop();
      memset(new_vertex_ptr, 0, m_vertex_pool.n_bytes_per_array());
      new_vertex_ptr->id = gid;
      new_vertex_ptr->initialized = false;
      m_vertex_map.Put(gid, new_vertex_ptr);

      return sTIRE_VERTEX_SMART_PTR(this, new_vertex_ptr);
    }
    return sTIRE_VERTEX_SMART_PTR(this, *vertex_ptr);
  }

  // This version useful for debugging
  void init_vertex(DGF_VERTEX_INDEX gid, 
                   const sBG_POINT3d& uvw_loc,
                   const sBG_POINT3d& xyz_loc)
  {
    sTIRE_VERTEX ** vertex_ptr = m_vertex_map.Get(gid);
    cassert(vertex_ptr != NULL);
    (*vertex_ptr)->uvw = uvw_loc;
    (*vertex_ptr)->xyz = xyz_loc;
    (*vertex_ptr)->initialized = true;
  }

  // void init_vertex(DGF_VERTEX_INDEX gid, 
  //                  const sBG_POINT3d& uvw_loc)
  // {
  //   sTIRE_VERTEX ** vertex_ptr = m_vertex_map.Get(gid);
  //   cassert(vertex_ptr != NULL);
  //   (*vertex_ptr)->uvw = uvw_loc;
  //   (*vertex_ptr)->initialized = true;
  // }

  void rotate_vertices(double rot_angle);

  auINT32 n_vertices() const { return m_vertex_map.NumEntries(); }

  Tire::cVECTOR carcass_u_deriv(Tire::cPOINT xyz) const { return m_dtire.carcass_u_deriv(xyz,*m_carcass_finder); }

};

#endif
