#ifndef EXA_SIMENG_SPSC_QUEUE_H
#define EXA_SIMENG_SPSC_QUEUE_H

#include <atomic>
#include <new> // std::hardware_destructive_interference_size

// Simplified from https://github.com/rigtorp/SPSCQueue (MIT license). 
// Everything in here is pretty self-evident though.
//
// Single Producer (main thread) Single Consumer (Comm Thread) Wait-Free Queue.
// Must be initialized with the max capacity. Does not do any memory
// allocation. As such, a push() operation is always O(1). The entry type T
// must be a trivial type.
//
// The compute thread push()es entries onto the queue. The comm thread should
// call drain() with a functor argument. This will automatically run through the queue,
// applying the functor to each element in the queue until it is empty.

template<typename T>
class cSPSC_QUEUE
{
public:
  explicit cSPSC_QUEUE(asINT32 capacity) : 
    m_capacity(capacity+1),
    m_head(0),
    m_tail_cached(0),
    m_tail(0) 
  {
    // It'll be a lot more work to make this work for non-trivial types
    static_assert( std::is_trivial<T>::value, "T is not a trivial type" );

    // Adding padding prevents false-sharing with other allocations
    m_entries = new T[m_capacity + 2 * ENTRY_PADDING];
  }

  ~cSPSC_QUEUE()
  {
    delete [] m_entries;
  }

  cSPSC_QUEUE(const cSPSC_QUEUE&) = delete;
  cSPSC_QUEUE(cSPSC_QUEUE&&) = delete;

  void push(const T& entry) noexcept 
  {
    cassert(size() != capacity()); // We are full, we can't push anything!
    const size_t tail = m_tail.load(std::memory_order_relaxed);
    size_t next = tail + 1;
    if (next == m_capacity) {
      next = 0;
    }
    m_entries[ tail + ENTRY_PADDING ] = entry;
    m_tail.store(next, std::memory_order_release);
  }

  T * front() noexcept
  {
    size_t head = m_head.load(std::memory_order_relaxed);
    if (head == m_tail_cached) {
      m_tail_cached = m_tail.load(std::memory_order_acquire);
      if (m_tail_cached == head) {
        return nullptr;
      }
    }
    return &m_entries[head + ENTRY_PADDING];
  }

  void pop() noexcept
  {
    size_t head = m_head.load(std::memory_order_relaxed);
    size_t next = head+1;
    if (next == m_capacity) {
      next = 0;
    }
    m_head.store(next, std::memory_order_release);
  }

  template<typename FUNC>
  void drain(FUNC&& f) noexcept
  {
    while (true) {
      T * entry = front();
      if (entry == nullptr) {
        break;
      }
      f(*entry);
      pop();
    }
  }

  size_t size() const noexcept 
  {
    std::ptrdiff_t diff = m_tail.load(std::memory_order_acquire) - m_head.load(std::memory_order_acquire);

    if (diff < 0) {
      diff += m_capacity;
    }
    return static_cast<size_t>(diff);
  }

  bool empty() const noexcept 
  {
    return m_tail.load(std::memory_order_acquire) == m_head.load(std::memory_order_acquire);
  }

  size_t capacity() const noexcept { return m_capacity-1; }

private:
#ifdef __cpp_lib_hardware_interference_size
  static constexpr size_t CACHE_LINE_SIZE = std::hardware_destructive_interference_size;
#else
  // newer intel processors actually try to fetch 2 cache lines! (starting with SandyBridge)
  static constexpr size_t CACHE_LINE_SIZE = 128;
#endif

  // padding in the entries array prevents false-sharing with adjacent allocations
  static constexpr size_t ENTRY_PADDING = ((CACHE_LINE_SIZE-1)/sizeof(T))+1;

  size_t m_capacity;
  T * m_entries;

  // Conventional wisdom says that each atomic variable should be in it's own
  // cache line to avoid false-sharing. Since our two threads are bound to the
  // same core, we get a slight performance improvement by keeping everything
  // in the same cache line.
  alignas(CACHE_LINE_SIZE) std::atomic<size_t> m_head;
  size_t m_tail_cached;
  std::atomic<size_t> m_tail;

  // padding avoids adjacent allocations false-sharing m_tail
  char padding[CACHE_LINE_SIZE-sizeof(m_tail)];
  
};

#endif
