/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include LGI_H

#include "particle_random_properties.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"

RANDOM_PARTICLE_PROPERTIES g_random_particle_properties;


VOID sRANDOM_PARTICLE_PROPERTIES::ckpt()
{

  LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER header;
  size_t total_rng_state_size = 0;
  ccDOTIMES(dist_type_index, sPDF::NUM_DISTRIBUTION_TYPES) {
    total_rng_state_size += m_generators[dist_type_index]->rng_state_size(); //PDFS RNGs can have state vectors of different sizes.
  }
  size_t record_length = sizeof(header) + total_rng_state_size * sizeof(char);
  lgi_write_init_tag(&header, LGI_CKPT_PARTICLE_RANDOM_PROPERTY_TAG, record_length);
  header.n_random_number_generators = sPDF::NUM_DISTRIBUTION_TYPES;
  write_ckpt_lgi_head(header);

  ccDOTIMES(dist_type_index, sPDF::NUM_DISTRIBUTION_TYPES) {
    size_t rng_state_size = m_generators[dist_type_index]->rng_state_size();
    char * rng_state = new char[rng_state_size];
    m_generators[dist_type_index]->get_rng_state_data(rng_state);
    write_ckpt_lgi(rng_state, rng_state_size);
    delete[] rng_state;
  }
}

VOID sRANDOM_PARTICLE_PROPERTIES::ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{

  size_t len = ckpt_len();
  pio_ckpt_buff.write(&len);
  asINT32 n_random_number_generators = sPDF::NUM_DISTRIBUTION_TYPES;
  pio_ckpt_buff.write(&n_random_number_generators);
  ccDOTIMES(dist_type_index, sPDF::NUM_DISTRIBUTION_TYPES) {
    size_t rng_state_size = m_generators[dist_type_index]->rng_state_size();
    char * rng_state = new char[rng_state_size];
    m_generators[dist_type_index]->get_rng_state_data(rng_state);
    pio_ckpt_buff.write(rng_state, rng_state_size);
    delete[] rng_state;
  }
}

size_t sRANDOM_PARTICLE_PROPERTIES::ckpt_len()
{
  size_t len = sizeof(size_t) + sizeof(asINT32);
  size_t total_rng_state_size = 0;
  ccDOTIMES(dist_type_index, sPDF::NUM_DISTRIBUTION_TYPES) {
    total_rng_state_size += m_generators[dist_type_index]->rng_state_size(); //PDFS RNGs can have state vectors of different sizes.
  }
  len += total_rng_state_size * sizeof(char);
  return len;
}

VOID sRANDOM_PARTICLE_PROPERTIES::read_ckpt()
{

  LGI_CKPT_PARTICLE_RANDOM_PROPERTY_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  if(header.n_random_number_generators != sPDF::NUM_DISTRIBUTION_TYPES)
    msg_internal_error("The checkpoint file contains an incorrect number of random number generator "
                       "states needed for generating random particle properties.");

  ccDOTIMES(dist_type_index, sPDF::NUM_DISTRIBUTION_TYPES) {//sPDF::NUM_DISTRIBUTION_TYPES) {
    asINT32 rng_state_size = m_generators[dist_type_index]->rng_state_size();
    char* rng_state = new char[rng_state_size];
    read_lgi(rng_state, rng_state_size);
    m_generators[dist_type_index]->set_rng_state_data(rng_state);
    delete [] rng_state;
  }
}


//Function to convert a LGI::eLGI_DISTRIBUTION_TYPE to an ePDF_TYPE.
sPDF::ePDF_TYPE lgi_to_pdfs_distribution(LGI::eLGI_DISTRIBUTION_TYPE lgi_type)
{
  sPDF::ePDF_TYPE pdfs_type;
  switch(lgi_type) {
  case LGI::DISTRIBUTION_UNIFORM:
    pdfs_type = sPDF::UNIFORM_DISTRIBUTION ;
    break;
  case LGI::DISTRIBUTION_GAUSSIAN:
    pdfs_type = sPDF::GAUSSIAN_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_TRUNCATED_GAUSSIAN:
    pdfs_type = sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_GAMMA:
    pdfs_type = sPDF::GAMMA_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_NONE:
    pdfs_type = sPDF::CONSTANT_NUMBER;
    break;
  case LGI::DISTRIBUTION_ROSIN_RAMMLER:
    pdfs_type = sPDF::ROSIN_RAMMLER_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_ROSIN_RAMMLER_VOLUME_FRACTION:
    pdfs_type = sPDF::ROSIN_RAMMLER_VOLUME_FRACTION_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_LOG_NORMAL:
    pdfs_type = sPDF::LOG_NORMAL_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_LINEAR:
    pdfs_type = sPDF::LINEAR_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_1MX_POW_N:
    pdfs_type = sPDF::EXPONENTIAL_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_HALF_COSINE:
    pdfs_type = sPDF::HALF_COSINE_DISTRIBUTION;
    break;
  case LGI::DISTRIBUTION_POISSON:
    pdfs_type = sPDF::POISSON_DISTRIBUTION;
    break;
  default:
    pdfs_type = sPDF::CONSTANT_NUMBER; // To turn off uninitialized variable warning
    msg_error("Unrecognized distribution type in LGI stream.\n");
  }
  return(pdfs_type);
}


