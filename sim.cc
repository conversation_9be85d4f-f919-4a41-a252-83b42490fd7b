/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * SIM data structure
 *--------------------------------------------------------------------------*/

#include "sim.h"
#include "ublk.h"
#include "ckpt.h"
#include "advect.h"
#include "strand_mgr.h"
#include "timescale.h"
#include "data_offset_table_opts.h"
#include PHYSICS_H

sSIM_INFO sim;
sTURB_SYNTH_INFO g_turb_info;

#if BUILD_D19_LATTICE
sCDI_LIQUID_PARAMS *g_lpms = NULL; //liquid parameters, determined by liquid type
#endif

sSCALAR_MATERIAL *g_scalar_materials = NULL;  //for LB_UDS

static VOID add_thermal_acc_events() {
  //Note that timesteps below are within flow realm, need to be specified when creating the new event
  TIMESTEP acc_start = sim.thermal_accel.start;
  TIMESTEP acc_int = sim.thermal_accel.interval;
  TIMESTEP acc_period = sim.thermal_accel.period;
  TIMESTEP acc_end = sim.thermal_accel.end_time();
  //PDE solver events
  if(acc_start == 0) {
    sim.thermal_accel.acc_on = TRUE;
    if(acc_period != acc_end) {
      g_async_event_queue.add_entry(new_event(EVENT_ID_T_PDE_SOLVER_ON, 0, acc_start + acc_period - 1, acc_period, acc_end - acc_period - 1, STP_FLOW_REALM));
    }
  } else {
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_PDE_SOLVER_ON, 0, acc_start - 1, acc_period, acc_end - acc_period - 1, STP_FLOW_REALM));
  }
  //LB solver events
  if(acc_int < acc_period) {
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_LB_SOLVER_ON, 0, acc_start + acc_int - 1, acc_period, acc_end, STP_FLOW_REALM));
  } else {
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_LB_SOLVER_ON, 0, acc_end - 1, 0, TIMESTEP_LAST, STP_FLOW_REALM));
  }
}

#ifdef CONDUCTION_HACKED_STAGGERED_COUPLING_FRAMEWORK
VOID round_up_to_nearest_valid_ts(asINT32& duration) {
  const asINT32 base_factor = 1 << (sim.num_scales - 1);

  duration += (base_factor - (duration % base_factor)) % base_factor;
}

VOID round_up_user_parameters() {
  round_up_to_nearest_valid_ts(g_pfc_staggered_coupling_flow_duration_in_ts);
  round_up_to_nearest_valid_ts(g_pfc_staggered_coupling_flow_avg_period_in_ts);
  round_up_to_nearest_valid_ts(g_pfc_staggered_coupling_conduction_duration_in_ts);
  round_up_to_nearest_valid_ts(g_pfc_staggered_coupling_conduction_avg_period_in_ts);

  // In this hacked setup, assume duration and averaging periods are always equal.
  // Use only the user specified averaging period
  g_pfc_staggered_coupling_flow_duration_in_ts = g_pfc_staggered_coupling_flow_avg_period_in_ts;
  g_pfc_staggered_coupling_conduction_duration_in_ts = g_pfc_staggered_coupling_conduction_avg_period_in_ts;

  if (my_proc_id == 0) {
    msg_print_no_prefix("Flow duration: %d, averaging period: %d",
        g_pfc_staggered_coupling_flow_duration_in_ts,
        g_pfc_staggered_coupling_flow_avg_period_in_ts);
    msg_print_no_prefix("Conduction duration: %d, averaging period: %d",
        g_pfc_staggered_coupling_conduction_duration_in_ts,
        g_pfc_staggered_coupling_conduction_avg_period_in_ts);
  }
}

VOID sTIME_COUPLING_INFO::set_phase_flags() {
  //Needs to be invoked every time m_idx_active_phase is changed, to ensure consistency
  m_is_staggered = is_staggered(m_idx_active_phase);
  m_is_one_solver_frozen = is_one_solver_frozen(m_idx_active_phase);
  m_is_flow_solver_frozen = is_flow_solver_frozen(m_idx_active_phase);
  m_is_advancing_at_different_rate = is_advancing_at_different_rate(m_idx_active_phase);
  m_is_conservative_across_realms = is_conservative_across_realms(m_idx_active_phase);
}

VOID sTIME_COUPLING_INFO::init(uINT32 num_phases, STP_SCALE coarsest_coupled_scale) {
  if (g_pfc_time_coupling_num_phases > 0) {
    //Hacked version for staggered scheme with flow & conduction times decoupled, set by editing 
    //global variables and used to override the phase info read from powercase.
    //COND-TODO: remove this once we enable proper staggering phases!
    round_up_user_parameters();

    if (g_pfc_time_coupling_num_phases > 1)
      msg_internal_error("Hacked coupling parameters cannot work with multiple phases");

    m_phases.resize(g_pfc_time_coupling_num_phases);

    ccDOTIMES (i, g_pfc_time_coupling_num_phases) {
      BASETIME next_phase_start_time = BASETIME_LAST;

      if (i < g_pfc_time_coupling_num_phases) {
        // next_phase_start_time = start time from next phase
      }

      m_phases.at(i).start = 0;
      // Assume the only phase is staggered solver now
      m_phases.at(i).time_coupling = eTIME_COUPLING_SCHEME::Stagger;

      if (m_phases.at(i).time_coupling == eTIME_COUPLING_SCHEME::Stagger) {
        m_phases.at(i).time_rel_solver = eCOUPLED_SOLVER::FlowSolver;
        m_phases.at(i).flow_duration = g_pfc_staggered_coupling_flow_duration_in_ts;
        m_phases.at(i).flow_avg_interval = g_pfc_staggered_coupling_flow_avg_period_in_ts;
        m_phases.at(i).conduction_duration = g_pfc_staggered_coupling_conduction_duration_in_ts;
        m_phases.at(i).conduction_avg_interval = g_pfc_staggered_coupling_conduction_avg_period_in_ts;
      }
    }
  } else {
    if (num_phases<=0) msg_internal_error("Missing time phases");
    m_phases.resize(num_phases);
  }
  
  //Initialized to track first phase
  m_idx_active_phase = 0;
  m_idx_next_phase = 1;

  m_coarsest_coupled_scale = coarsest_coupled_scale;
}

sTIME_COUPLING_PHASE* sTIME_COUPLING_INFO::next_unfrozen_phase() {
  for (size_t idx = m_idx_next_phase; idx < m_phases.size(); idx++) {
    if (m_phases[idx].time_coupling != eTIME_COUPLING_SCHEME::FreezeOne) {
      return &m_phases[idx];
    }
  }
  return nullptr;
}

cBOOLEAN sTIME_COUPLING_INFO::maybe_update_time_coupling_phase() {
  //Base time here is the value once the time_update moves to the next expected time. Since the freeze occurs at the start, 
  //any value equal or higher than start should lead to a phase change
  size_t num_phases = m_phases.size();
  if (m_idx_next_phase == num_phases || g_timescale.m_base_time < m_phases[m_idx_next_phase].start) {
    return FALSE;
  } else {
    //If new phase is advancing at different rate, the time increment used to increment g_timescale.m_base_time might be
    //higher than what is supposed to be and will be corrected backwards. Thus, we only increase the index by one so it
    //is checked again after g_timescale.m_base_time is corrected
    m_idx_active_phase++;
    m_idx_next_phase++;
    set_phase_flags();
  }
  return TRUE;
}

BASETIME sTIME_COUPLING_INFO::next_phase_start(BASETIME timestep_last) {
  if (m_idx_next_phase == m_phases.size()) {
    return timestep_last;
  } else {
    return m_phases[m_idx_next_phase].start;
  }
}

VOID sTIME_COUPLING_INFO::update_phase_averaging_start_end() {
  if (is_staggered()) {
    //multiple averaging windows during the phase, starting from the beginning
    update_staggered_averaging_start_end();
  } else {
    TIMESTEP avg_interval = (sim.is_conduction_sp) 
                            ? m_phases[m_idx_active_phase].conduction_avg_interval
                            : m_phases[m_idx_active_phase].flow_avg_interval;
    if (m_idx_next_phase == m_phases.size() || avg_interval <= 0) { 
      //last phase or phase with no averaging enabled, set start/end to last timestep so is never reached
      m_averaging_start_basetime = BASETIME_LAST;
      m_averaging_end_basetime = BASETIME_LAST;
    } else {
      //single averaging window at the end of the phase
      m_averaging_start_basetime = m_phases[m_idx_next_phase].start - avg_interval * g_timescale.m_base_time_inc;
      m_averaging_end_basetime   = m_phases[m_idx_next_phase].start - 1;
    }
  }
}

VOID sTIME_COUPLING_INFO::update_staggered_averaging_start_end() {
  TIMESTEP avg_interval = (sim.is_conduction_sp) 
                          ? m_phases[m_idx_active_phase].conduction_avg_interval
                          : m_phases[m_idx_active_phase].flow_avg_interval;
  //scales it to base_time
  avg_interval *= g_timescale.m_base_time_inc;
  //accumulated data is checkpointed, so can start from the middle of an averaging interval 
  m_averaging_start_basetime = g_timescale.m_base_time 
                               - (g_timescale.m_base_time - m_phases[m_idx_active_phase].start) % avg_interval;
  m_averaging_end_basetime = m_averaging_start_basetime + avg_interval - 1;
}

cBOOLEAN sTIME_COUPLING_INFO::compute_average(const asINT32 scale) const {
  //average time coupling parameters if the next time this scale is updated falls beyond the averaging end time
  //(note that for even/odd surfels updated every two timesteps, this might not be at the end of the timestep)
  BASETIME next_update = g_timescale.m_base_time + (1 << (sim.num_scales - scale - 1)) * g_timescale.m_base_time_inc;
  return (next_update > m_averaging_end_basetime);
}
#endif

sSIM_INFO::sSIM_INFO() 
{
  sBG_TRANSFORM3d xform = grf.ground_to_global_xform;
  memset(this, 0, sizeof(*this));
  grf.ground_to_global_xform = xform; // restore after the memset @@@ makes no sense

  // Initial state of quaternion is 0 radians rotated around the Z axis.
  // We choose the Z axis because it is meaningful for 2D and 3D cases.
  grf.quaternion[0]         = 1; // cos(0)
  grf.quaternion_inverse[0] = 1; // cos(0)
  //grf.quaternion[3] = 1;
  //grf.quaternion_inverse[3] = -1;
  g_timescale.m_base_time_inc = 1;
  g_timescale.m_n_base_timesteps_per_timestep = 1;
  
  is_t_timestep_smaller_than_lb_timestep = FALSE;

#if BUILD_DOUBLE_PRECISION
  is_double_precision = TRUE;
#else
  is_double_precision = FALSE;
#endif

  is_meas_prescribed = (getenv("EXA_MEAS_PRESCRIBED") != NULL)? TRUE : FALSE;
  is_rwnc_calibration_test_run = (getenv("EXA_TEST_RWNC_CALIBRATION") != NULL) ? TRUE : FALSE;
  sim.calibration_params.iteration_number = -1;
  sim.calibration_params.calib_period = 1;
  enable_dcache_pm_storage = FALSE;
  is_accretion_simulation = FALSE;
  enable_tangential_shell_conduction = FALSE;
  use_implicit_shell_solver = FALSE;
  constant_implicit_shell_solver_matrix = FALSE;
  use_implicit_solid_solver = FALSE;

}

VOID sSIM_INFO::init_lattice()
{
  init_lattice_tables();

  /* Init SIM structure entries */
  if (sim.char_vel == 0) {
    sim.one_over_mach_0_sqrd = 1e8;
  } else {
    sim.one_over_mach_0_sqrd = g_lb_temp_constant / (sim.char_vel * sim.char_vel);
  }

#if BUILD_D39_LATTICE
  sim.max_surfel_bc_vel_default = MAX_SURFEL_BC_VEL;
  sim.max_surfel_bc_vel_default_sqrd = MAX_SURFEL_BC_VEL * MAX_SURFEL_BC_VEL;
#else
    if(sim.is_high_subsonic_mach_regime){
      sim.max_surfel_bc_vel_default = MAX_SURFEL_BC_VEL_HS;
      sim.max_surfel_bc_vel_default_sqrd = MAX_SURFEL_BC_VEL_HS * MAX_SURFEL_BC_VEL_HS;
    } else{
      sim.max_surfel_bc_vel_default = MAX_SURFEL_BC_VEL;
      sim.max_surfel_bc_vel_default_sqrd = MAX_SURFEL_BC_VEL * MAX_SURFEL_BC_VEL;
    }

#endif

  sim.default_max_vel = DEFAULT_MAX_VEL;
  sim.max_bc_mass_flux = MAX_BC_MASS_FLUX;
  sim.max_bc_mass_flux_sqrd = MAX_BC_MASS_FLUX * MAX_BC_MASS_FLUX;

  if(sim.is_high_subsonic_mach_regime)
  {
    sim.max_moving_surface_vel = g_Mach_cap * sqrt(sim.lattice_gamma * sim.lattice_gas_const * sim.mean_temp); //sim.mean_temp should always be 1/3
    sim.max_moving_surface_vel_sqrd = sim.max_moving_surface_vel * sim.max_moving_surface_vel;
  }
  else
  {
    sim.max_moving_surface_vel = MAX_MOVING_SURFACE_VEL;
    sim.max_moving_surface_vel_sqrd = MAX_MOVING_SURFACE_VEL * MAX_MOVING_SURFACE_VEL;
  }

  sim.one_over_mach_0_4th = sim.one_over_mach_0_sqrd * sim.one_over_mach_0_sqrd;
}

VOID sSEED_CONTROL::init(cDGF_SMART_SEED_CONTROL &control) {
  if (control.seed_scaling_type == DGF_DIMLESS_SCALING) {
    seed_via_dimless_properties = TRUE;
    seed_via_mks = FALSE;
  } else if (control.seed_scaling_type == DGF_MKS_SCALING) {
    seed_via_dimless_properties = FALSE;
    seed_via_mks = TRUE;
  } else {
    seed_via_dimless_properties = FALSE;
    seed_via_mks = FALSE;
  }

#if BUILD_5G_LATTICE
   if (seed_via_dimless_properties || seed_via_mks)
     //these two are enforced to be false in CP
     msg_error("5G does not support dimless seeding or mks seeding.");
#endif

  seed_sim_was_incompressible  = control.seed_sim_was_incompressible;
  smart_seed_char_density      = control.seed_data_char_density * g_density_scale_factor;
  smart_seed_char_vel          = control.seed_data_char_vel;
  smart_seed_char_temp         = control.seed_data_char_temp;
  smart_seed_lattice_gas_const = control.seed_data_lattice_gas_const;
  smart_seed_mks_vel_scaling   = control.seed_data_mks_vel_scaling;
  n_smart_seed_vars            = control.n_variables;
  n_smart_seed_uds_vars        = control.n_uds_variables;
  n_fluid_seed_var_specs       = control.n_fluid_seed_var_specs;
  n_boundary_seed_var_specs    = control.n_boundary_seed_var_specs;
  seed_rotate_vel_csys_index   = control.seed_rotate_vel_csys_index;
  seed_rotate_vel_axis         = control.seed_rotate_vel_axis;
  seed_rotate_vel_angle        = control.seed_rotate_vel_angle;
}

__HOST__DEVICE__
VOID sim_initialize_lrf_velocities(LRF_PHYSICS_DESCRIPTOR lrf)
{
  memset(lrf->linear_vel, 0, 3 * sizeof(lrf->linear_vel[0]));
  memset(lrf->linear_accel, 0, 3 * sizeof(lrf->linear_accel[0]));

  vcopy(lrf->omega_total, lrf->axis); vscale(lrf->omega_total, lrf->omega, lrf->omega_total);
  vcopy(lrf->omega_total1, lrf->axis); vscale(lrf->omega_total1, lrf->omega1, lrf->omega_total1);

  auto& simc = get_simc_ref();
  
  if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
    ccDOTIMES(i, simc.num_scales) {
      if (lrf->domega_total) {
        dFLOAT *domega_total = &lrf->domega_total[3 * i];
        vcopy(domega_total, lrf->axis);
        vscale(domega_total, lrf->domega[i], domega_total);
      } else {
        HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT("domega_total is NULL but should have been allocated.");
      }
      if (lrf->domega_linear_accel)
        memset(&lrf->domega_linear_accel[3 * i], 0, 3 * sizeof(lrf->domega_linear_accel[0]));
      else
        HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT("domega_linear_accel is NULL but should have been allocated.");
    }
  }
}

//  sim_compute_lrf_angular_vel_terms calculates the following LRF quantities:
//      omega_total  : total omega at time t in local frame expressed in local CSYS
//      omega_total1 : total omega at time t + 1 in local frame expressed in local CSYS
//      domega_total : total derivative at time t in local frame expressed in local CSYS
//      linear_vel, linear_accel: velocity and acceleration vectors due to axis point offset from parent
//                                LRF's axis point, in local frame expressed in local CSYS
//      domega_linear_accel: additional acceleration terms arising from sum_i w_dot x (c_parent_i - c_i),
//                           in local frame expressed in local CSYS
__HOST__DEVICE__
VOID sim_compute_lrf_nested_terms(LRF_PHYSICS_DESCRIPTOR lrf, const BASETIME &time)
{
  dFLOAT cumulative_rotation_matrix[3][3], cumulative_rotation_matrix1[3][3];

  // Initialize for current time
  memcpy(&cumulative_rotation_matrix[0][0],
         &lrf->containing_to_local_rotation_matrix[0][0],
         sizeof(cumulative_rotation_matrix));
  
  // Initialize for next time step
  memcpy(&cumulative_rotation_matrix1[0][0],
         &lrf->containing_to_local_rotation_matrix1[0][0],
         sizeof(cumulative_rotation_matrix1));

  auto& sim = get_sim_ref();
  auto& simc = get_simc_ref();
  
  // Three LRFs figure in these calculations:
  //    ith_lrf        : i-th LRF ancestor
  //    containing LRF : i-th LRF's parent
  //    lrf            : LRF for which quantities are calculated
  sINT32 containing_lrf_index = lrf->containing_lrf_index;
  LRF_PHYSICS_DESCRIPTOR ith_lrf = lrf;
  LRF_PHYSICS_DESCRIPTOR containing_lrf = &sim.lrf_physics_descs[containing_lrf_index];
  while (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {
    BOOLEAN transpose = TRUE;
    // Current time:
    // Sum up omega and express in local coordinates
    dFLOAT omega_i[3], omega_il[3];
    vcopy(omega_i, containing_lrf->axis);
    vscale(omega_i, containing_lrf->omega, omega_i);
    if (lrf->is_mlrf_on)
      rotate_vector(omega_i, omega_il, cumulative_rotation_matrix);
    else
      vcopy(omega_il, omega_i);
    vinc(lrf->omega_total, omega_il);

    if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
      // Sum up domega and express in local coordinates
      ccDOTIMES(i, simc.num_scales) {
        if (containing_lrf->is_angular_vel_time_varying || containing_lrf->is_rotational_dynamics_on) {
          dFLOAT domega_i[3];
          vcopy(domega_i, containing_lrf->axis);
          vscale(domega_i, containing_lrf->domega[i], domega_i);
          if (lrf->is_mlrf_on)
            rotate_vector(domega_i, domega_i, cumulative_rotation_matrix);
          dFLOAT *domega_total = &lrf->domega_total[3 * i];
          vinc(domega_total, domega_i);
        }
      }
    }

    // Update rotation matrix that transforms from ancestor to this LRF
    if (lrf->is_mlrf_on) {
      calculate_matrix_matrix_product(cumulative_rotation_matrix, containing_lrf->containing_to_local_rotation_matrix,
                                      cumulative_rotation_matrix);
    }

    // Next time step. No need to calculate ref_accel_for_lrf or ref_vel_for_lrf or the domega accel terms
    dFLOAT omega_i1[3], omega_il1[3];
    vcopy(omega_i1, containing_lrf->axis);
    vscale(omega_i1, containing_lrf->omega1, omega_i1);
    if (lrf->is_mlrf_on) {
      rotate_vector(omega_i1, omega_il1, cumulative_rotation_matrix1);
      calculate_matrix_matrix_product(cumulative_rotation_matrix1, containing_lrf->containing_to_local_rotation_matrix1,
                                      cumulative_rotation_matrix1);
    } else {
      vcopy(omega_il1, omega_i1);
    }
    vinc(lrf->omega_total1, omega_il1);

    // Calculate axis point difference between i-th LRF and its immediate parent, in parent's CSYS
    dFLOAT ctr_diff[3];
    vsub(ctr_diff, ith_lrf->point, containing_lrf->point); // in parent LRF's CSYS

    dFLOAT ctr_diff_mag = sqrt(vdot(ctr_diff, ctr_diff));
    if (ctr_diff_mag > std::numeric_limits<dFLOAT>::epsilon()) { // axes are not collocated
      // Transform axis point offsets and omega to global
      dFLOAT ctr_diff_g[3], omega_ig[3];
      if (lrf->is_mlrf_on) {
        rotate_vector(ctr_diff, ctr_diff_g, containing_lrf->local_to_global_rotation_matrix);
        rotate_vector(omega_i, omega_ig, containing_lrf->local_to_global_rotation_matrix);
      } else {
        vcopy(ctr_diff_g, ctr_diff);
        vcopy(omega_ig, omega_i);
      }

      // Perform cross product in global CSYS
      dFLOAT w_x_ctr_diff_g[3], w_x_ctr_diff[3];
      vcross(w_x_ctr_diff_g, omega_ig, ctr_diff_g);

      // Transform back to LRF's local CSYS before adding velocity term
      if (lrf->is_mlrf_on)
        rotate_vector(w_x_ctr_diff_g, w_x_ctr_diff, lrf->local_to_global_rotation_matrix, transpose);
      else
        vcopy(w_x_ctr_diff, w_x_ctr_diff_g);
      vinc(lrf->linear_vel, w_x_ctr_diff);

      // Calculate linear acceleration terms due to axis center offsets
      dFLOAT w_x_w_x_ctr_diff[3], w_x_w_x_ctr_diff_g[3];
      vcross(w_x_w_x_ctr_diff_g, omega_ig, w_x_ctr_diff_g); // in global CSYS

      // Convert back to local csys by first converting to global
      if (lrf->is_mlrf_on)
        rotate_vector(w_x_w_x_ctr_diff_g, w_x_w_x_ctr_diff, lrf->local_to_global_rotation_matrix,
                      transpose); // back to LRF's CSYS
      else
        vcopy(w_x_w_x_ctr_diff, w_x_w_x_ctr_diff_g);
      vinc(lrf->linear_accel, w_x_w_x_ctr_diff);

      // Add wdot x (r_LRF - r_i) if time-dependent angular velocity
      // Calculate additional acceleration from domega x center_diffs
      if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
        ccDOTIMES(i, simc.num_scales) {
          dFLOAT domega_i[3], domega_ig[3];
          vcopy(domega_i, lrf->axis);
          vscale(domega_i, lrf->domega[i], domega_i);
          // Transform domega to global
          if (lrf->is_mlrf_on)
            rotate_vector(domega_i, domega_ig, containing_lrf->local_to_global_rotation_matrix);
          else
            vcopy(domega_ig, domega_i);

          dFLOAT wdot_x_ctr_diff[3], wdot_x_ctr_diff_g[3];
          vcross(wdot_x_ctr_diff_g, domega_ig, ctr_diff_g);

          // Transform cross product back to LRF's local CSYS
          if (lrf->is_mlrf_on)
            rotate_vector(wdot_x_ctr_diff_g, wdot_x_ctr_diff, lrf->local_to_global_rotation_matrix,
                          transpose); // from global CSYS to LRF local CSYS
          else
            vcopy(wdot_x_ctr_diff, wdot_x_ctr_diff_g);
          dFLOAT *domega_linear_accel = &lrf->domega_linear_accel[3 * i];
          vinc(domega_linear_accel, wdot_x_ctr_diff);
        }
      }
    }

    ith_lrf = containing_lrf;
    containing_lrf_index = containing_lrf->containing_lrf_index;
    if (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
      containing_lrf = &sim.lrf_physics_descs[containing_lrf_index];
    if (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
      vsub(ctr_diff, ith_lrf->point, containing_lrf->point);
  }
}

VOID sim_initialize_nested_lrfs()
{
  // Record all containing frames, immediate parent first to outermost
  sINT32 n_lrf = sim.n_lrf_physics_descs;
  ccDOTIMES (i, n_lrf) {
    LRF_PHYSICS_DESCRIPTOR lrf = &sim.lrf_physics_descs[i];
    asINT32 containing_lrf_index =  lrf->containing_lrf_index;

    lrf->lrf_index = i;  //needed for fix to PR41630.

    if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
      if (lrf->n_scales == 0)
        lrf->n_scales = sim.num_scales;
      if (lrf->domega_total == NULL) {
        lrf->domega_total = xnew dFLOAT [3 * sim.num_scales];
      }
      if (lrf->domega_linear_accel == NULL) {
        lrf->domega_linear_accel = xnew dFLOAT [3 * sim.num_scales];
      }
    }
    sim_initialize_lrf_velocities(lrf);
    if (lrf->containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
      sim_compute_lrf_nested_terms(lrf, g_timescale.m_time);
  }
}


__HOST__DEVICE__ static
VOID update_rotation_matrix_for_reference_frame(LRF_PHYSICS_DESCRIPTOR lrf) {
  auto& sim = get_sim_ref();
  if (lrf->is_mlrf_on) {
    sINT32 containing_lrf_index = lrf->containing_lrf_index;
    dFLOAT local_to_global_matrix[3][3];
    memcpy(local_to_global_matrix, lrf->local_to_containing_rotation_matrix, 9 * sizeof(dFLOAT));
    auto& sim = get_sim_ref();
    while (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {
      LRF_PHYSICS_DESCRIPTOR containing_lrf = &sim.lrf_physics_descs[containing_lrf_index];
      calculate_matrix_matrix_product(local_to_global_matrix, containing_lrf->local_to_containing_rotation_matrix,
                                      local_to_global_matrix);
      containing_lrf_index = containing_lrf->containing_lrf_index;
    }

    // velocity/force transform requires a local_to_global_rotation matrix
    memcpy(lrf->local_to_global_rotation_matrix, local_to_global_matrix, 9 * sizeof(dFLOAT));

  }
  // Update axis for all containing LRFs.
  if (lrf->containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
    sim_compute_lrf_nested_terms(lrf, get_timescale_ref().m_time);
}

__HOST__DEVICE__ static
VOID rotate_reference_frame(LRF_PHYSICS_DESCRIPTOR lrf,
                            dFLOAT lat_time_inc_per_timestep,
                            asINT32 last_coarsest_active_scale,
                            asINT32 coarsest_active_scale,
                            cBOOLEAN is_initial_update_after_smart_seed,
                            asINT32 time_inc) {
  /* MLRF. For convenience we enforce 0 <= angle_rotated < 2*PI. */
  dFLOAT timestep_per_lat_time_inc = 1.0 / lat_time_inc_per_timestep;
  if (lrf->is_mlrf_on) {
    // The term lrf->omega in the right side of the next line is shorthand for:
    //
    //    (N rad/LatticeTimeInc) * (1 timestep)
    //
    // Hence the need to multiply by lat_time_inc_per_timestep to yield an angle
    // in radians.
    //
    if (!is_initial_update_after_smart_seed) {
      lrf->angle_rotated += lrf->omega * lat_time_inc_per_timestep;
    }
    lrf->angle_rotated1 = lrf->angle_rotated + lrf->omega * lat_time_inc_per_timestep;
    
    // Note that the rotations might be exceed multiples than +-2*PI for supercyled solvers, so keeps
    // decreasing/increasing it till is within bounds. It should be rare that is needed to be done more than once, so
    // chose the iterative way rather than compute it analytically to skip the division
    while (lrf->angle_rotated < 0) {
      lrf->angle_rotated += 2.0 * PI;
      lrf->n_revolutions--;
    }
    while (lrf->angle_rotated >= 2.0 * PI) {
      lrf->angle_rotated -= 2.0 * PI;
      lrf->n_revolutions++;
    }

    while (lrf->angle_rotated1 < 0) {
      lrf->angle_rotated1 += 2.0 * PI;
    }
    while (lrf->angle_rotated1 >= 2.0 * PI) {
      lrf->angle_rotated1 -= 2.0 * PI;
    }


    {
      get_rotation_matrix_from_axis_angle(lrf->axis, lrf->angle_rotated, lrf->local_to_containing_rotation_matrix);
      ccDOTIMES(j,3) {
        ccDOTIMES(k,3) {
          lrf->containing_to_local_rotation_matrix[j][k] = lrf->local_to_containing_rotation_matrix[k][j];
        }
      }

      // repeat calculation for one timestep in the future
      get_rotation_matrix_from_axis_angle(lrf->axis, lrf->angle_rotated1, lrf->local_to_containing_rotation_matrix1);
      ccDOTIMES(j,3) {
        ccDOTIMES(k,3) {
          lrf->containing_to_local_rotation_matrix1[j][k] = lrf->local_to_containing_rotation_matrix1[k][j];
        }
      }
    }

  }

  if ((lrf->is_angular_vel_time_varying && lrf->is_active) || lrf->is_rotational_dynamics_on) {
    if (!is_initial_update_after_smart_seed) {
      dFLOAT omega_m1 = lrf->omega;
      lrf->omega = lrf->omega1;

      if (lrf->is_rotational_dynamics_on) {
        lrf->omega1 = lrf->omega + lrf->angular_acceleration*lat_time_inc_per_timestep;
      }
      else {
        LRF_PARAMETERS parms = lrf->parameters();
#if !GPU_COMPILER        
        lrf->eval_time_varying_only_parameter_program(g_timescale.m_time + time_inc, g_timescale.m_powertherm_time, global_eqn_error_handler);
#else
        assert(false);
#endif        
        lrf->omega1 = parms->angular_vel.value;
      }

      vscale(lrf->angular_vel, lrf->omega, lrf->axis);

      // The subtract in the next line is shorthand for:
      //
      //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     rad/LatticeTimeInc^2
      //
      dFLOAT domega = 0.5 * timestep_per_lat_time_inc * (lrf->omega1 - omega_m1);

      DO_SCALE_FROM_FINE_TO_COARSE(scale1, FINEST_SCALE, last_coarsest_active_scale) {
        lrf->domega[scale1] = 0;
      }

      DO_SCALES_FINE_TO_COARSE(scale2) {
        lrf->domega[scale2] += domega;
      }

      // Convert accumlated values to averages for scales which will be subject
      // to dynamics. COARSEST_ACTIVE_SCALE becomes LAST_COARSEST_ACTIVE_SCALE
      // for the next call to this fcn, so these values will be zeroed.
      DO_SCALE_FROM_FINE_TO_COARSE(scale3, coarsen_scale(FINEST_SCALE), coarsest_active_scale) {
        dFLOAT scale_factor = 1.0 / scale_to_delta_t(scale3);
        lrf->domega[scale3] *= scale_factor;
      }
    }
    // the else case is already taken care of in read_lrf_physics_descriptors
  }
}

#if BUILD_GPU
namespace GPU {
__GLOBAL__ VOID sim_update_device_lrfs(asINT32 last_coarsest_active_scale,
                                       asINT32 coarsest_active_scale,
                                       cBOOLEAN is_initial_update_after_smart_seed,
                                       dFLOAT lat_time_inc_per_timestep,
                                       asINT32 n_time_inc) {

  auto& sim = get_sim_ref();
  auto lrf = sim.lrf_physics_descs + threadIdx.x;
  
  for (int time_inc = 1; time_inc <= n_time_inc; time_inc++) {
    rotate_reference_frame(lrf, lat_time_inc_per_timestep,
                          last_coarsest_active_scale,
                          coarsest_active_scale,
                          is_initial_update_after_smart_seed,
                          time_inc);
  }

  __syncthreads();
    
  sim_initialize_lrf_velocities(lrf);

  __syncthreads();
  
  update_rotation_matrix_for_reference_frame(lrf);    
}
}
#endif

VOID sim_update_local_ref_frames(asINT32 last_coarsest_active_scale,
                                 asINT32 coarsest_active_scale,
                                 BOOLEAN single_pass,
                                 asINT32 n_time_inc,
                                 dFLOAT time_rate_factor,
                                 cBOOLEAN is_initial_update_after_smart_seed)
{

  dFLOAT lat_time_inc_per_timestep = g_adv_fraction * time_rate_factor;
  if (single_pass) {
    lat_time_inc_per_timestep *= n_time_inc;
    n_time_inc = 1;
  }

#if BUILD_GPU
  if (!is_initial_update_after_smart_seed) {
    GPU::sim_update_device_lrfs
      <<<1 /*one block*/, sim.n_lrf_physics_descs /*n_threads*/,
      GPU::NO_DYN_SHMEM, GPU::g_stream>>>(last_coarsest_active_scale,
                                          coarsest_active_scale,
                                          is_initial_update_after_smart_seed,
                                          lat_time_inc_per_timestep,
                                          n_time_inc);
  }
#endif

  LRF_PHYSICS_DESCRIPTOR lrf = sim.lrf_physics_descs;
  ccDOTIMES(i, sim.n_lrf_physics_descs) {
    for (int time_inc = 1; time_inc <= n_time_inc; time_inc++) {
      rotate_reference_frame(lrf, lat_time_inc_per_timestep,
                            last_coarsest_active_scale,
                            coarsest_active_scale,
                            is_initial_update_after_smart_seed,
                            time_inc);
    }
    lrf++;
  }

  ccDOTIMES(nth_lrf_index, sim.n_lrf_physics_descs) {
    LRF_PHYSICS_DESCRIPTOR lrf = &sim.lrf_physics_descs[nth_lrf_index];
    sim_initialize_lrf_velocities(lrf);
    update_rotation_matrix_for_reference_frame(lrf);
  }

#if BUILD_GPU
  if (!is_initial_update_after_smart_seed) {
    checkCudaErrors( cudaPeekAtLastError() );
    checkCudaErrors(cudaStreamSynchronize(GPU::g_stream));
  }
#endif
}

// This determines the "effective axis" of the global reference frame by calculating
// a point on that axis. axis must be angular_vel unitized.
__HOST__DEVICE__
VOID calculate_point_on_rotation_axis(dFLOAT ref_pt[3],
                                      dFLOAT ref_pt_vel[3],
                                      dFLOAT axis[3],
                                      dFLOAT angular_vel[3],
                                      dFLOAT point[3],          // result
                                      dFLOAT ref_pt_vel_par[3]) // result (component of ref_pt_vel parallel to angular vel)
{
  // Calculate a point p on the axis of rotation
  dFLOAT dot = vdot(ref_pt_vel, axis);

  vscale(ref_pt_vel_par, dot, axis);

  dFLOAT ref_pt_vel_orth[3];             // component of ref point vel orthogonal to angular vel
  vsub(ref_pt_vel_orth, ref_pt_vel, ref_pt_vel_par);

  // Find a point p on the axis of rotation using this relation:
  //
  //   u_ref_orth = ang_vel X (x_ref - p)
  //
  // Expanding the cross product:
  //
  //   u_ref_orth[0] = ang_vel[1] * (x_ref - p)[2] - ang_vel[2] * (x_ref - p)[1]
  //   u_ref_orth[1] = ang_vel[2] * (x_ref - p)[0] - ang_vel[0] * (x_ref - p)[2]
  //   u_ref_orth[2] = ang_vel[0] * (x_ref - p)[1] - ang_vel[1] * (x_ref - p)[0]
  //
  // But there are an infinite number of points p that lie on the axis. Assuming that the 
  // Z component of axis is largest, we set p[2] to xref[2], which yields a reduced set of
  // equations:
  //
  //   u_ref_orth[0] = - ang_vel[2] * (x_ref - p)[1]
  //   u_ref_orth[1] = ang_vel[2] * (x_ref - p)[0] 
  //
  // that allow us to directly solve for p[0] and p[1].

  if (ref_pt_vel_orth[0] == 0
      && ref_pt_vel_orth[1] == 0
      && ref_pt_vel_orth[2] == 0) {
    vcopy(point, ref_pt);
  } 
  else {
    asINT32 largest_axis_component = 0;
    if (fabs(axis[1]) > fabs(axis[0]))
      largest_axis_component = 1;
    if (fabs(axis[2]) > fabs(axis[largest_axis_component]))
      largest_axis_component = 2;

    if (largest_axis_component == 2) {
      point[2] = ref_pt[2];
      point[1] = (ref_pt_vel_orth[0] + angular_vel[2] * ref_pt[1]) / angular_vel[2];
      point[0] = (angular_vel[2] * ref_pt[0] - ref_pt_vel_orth[1]) / angular_vel[2];
    } 
    else if (largest_axis_component == 1) {
      point[1] = ref_pt[1];
      point[0] = (ref_pt_vel_orth[2] + angular_vel[1] * ref_pt[0]) / angular_vel[1];
      point[2] = (angular_vel[1] * ref_pt[2] - ref_pt_vel_orth[0]) / angular_vel[1];
    } 
    else {
      point[0] = ref_pt[0];
      point[2] = (ref_pt_vel_orth[1] + angular_vel[0] * ref_pt[2]) / angular_vel[0];
      point[1] = (angular_vel[0] * ref_pt[1] - ref_pt_vel_orth[2]) / angular_vel[0];
    }
  }
}

// Should use g_timescale.m_time+1 since now it is called before incrementing the timestep in time update strand
VOID sSIM_INFO::update_ref_frames(asINT32 last_coarsest_active_scale, asINT32 coarsest_active_scale) {
  /* Called once at end of each timestep */
  if (grf.is_defined){
#if !BUILD_GPU    
    BASETIME sim_time = g_timescale.next_time();
    sim_update_global_ref_frame(last_coarsest_active_scale, coarsest_active_scale, &grf, sim_time);
    sim_predict_global_ref_frame(last_coarsest_active_scale, coarsest_active_scale, sim_time);
#else
    msg_internal_error("Global reference frame update not implemented for GPUs");
#endif
  }
  if (n_lrf_physics_descs > 0) {
    // LRFs are only rotate when the flow solver is active, since really conduction is not affected by the rotation.
    // Therefore, first need to determine the number of time increments where the flow solver is not frozen. 
    dFLOAT flow_time_rate;  //note that if tightly coupled, it depends on the phase
    asINT32 n_time_inc;
    sTIME_COUPLING_PHASE* next_phase = sim.time_coupling_info.next_phase();
    if (next_phase == nullptr ||  g_timescale.next_base_time() < next_phase->start) {
      //Only needed to check this phase
      flow_time_rate = (g_timescale.is_flow_supercycled()) ? 1.0 / g_timescale.m_therm_time_ratio : 1.0;
      n_time_inc = (sim.time_coupling_info.is_flow_solver_frozen()) ? 0 : g_timescale.m_time_inc;
    } else {
      //Traverses two phases, so needs to check each of them. 
      //Moreover, this consideration only relevant for conduction SPs (CSPs), since are the only ones that might be active
      //when the flow is frozen. Flow SPs (FSPs) will jump over the frozen phase and continue after being unfrozen as if
      //nothing happened. 
      double base_steps_inc;
      if (sim.is_conduction_sp) {
        //CONDUCTION SPs
        //However, phases are aligned to start after both realms are active, so if reaching a new phase, all base steps
        //advanced in this timestep belong to the new phase and is enough to check the new phase properties.
#if ENABLE_CONSISTENCY_CHECKS
        //Throw an error if the above assumption is not true and we need to update the logic to split the base steps
        //advanced between this and next phase
        if (next_phase->start -  g_timescale.m_base_time > g_timescale.m_n_base_timesteps_per_timestep) {
          msg_internal_error("Unexpected phase alignment found to update ref frames");
        }
        //Previous code that considered both phases read as below, while assumed lrf remain static when flow or conduction
        // were frozen, not just flow, and does not support advance solvers at different rate
        // n_time_inc = 0;
        // if (!sim.time_coupling_info.is_flow_solver_frozen()) {
        //   n_time_inc += (next_phase->start - g_timescale.m_base_time) / g_timescale.m_n_base_timesteps_per_timestep - 1;
        // }
        // if ((next_phase->time_coupling != eTIME_COUPLING_SCHEME::FreezeOne) ||
        //     ((next_phase->frozen_solver == eCOUPLED_SOLVER::ConductionSolver) == sim.is_conduction_sp)) {
        //   //If freezing the realm processed by this SP, it actually moves to the next phase so we still do the update
        //   n_time_inc += (g_timescale.next_base_time() - next_phase->start) / g_timescale.m_n_base_timesteps_per_timestep + 1;
        // }
#endif
        double n_cond_base_steps_ref = g_timescale.m_n_conduction_pde_base_steps;
        if (next_phase->time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) {
          if (next_phase->frozen_solver == eCOUPLED_SOLVER::ConductionSolver) {
            //CONDUCTION REALM FROZEN
            //Conduction SPs jump ahead of the frozen phase, but lrf has been spinning during the frozen phase since flow realm was
            //active, so we need to rotate it here accordingly to reach the correct angular position.
            //In addition, the advance ratio on the flow might differ betwen phases, so the flow_time_rate is the
            //weighted averaged of both, to retrieve the same position than the flow:
            //flow_time_rate = (ratio_frozen_phase * span_frozen_phase + ratio_next_phase * base_timesteps_next_phase)
            //                 / (span_frozen_phase + base_timesteps_next_phase)
            BASETIME frozen_start = next_phase->start; //start of frozen phase(s);
            next_phase = sim.time_coupling_info.next_unfrozen_phase();
            if (next_phase == nullptr) {
              //Conduction is never unfrozen again, implying that it has reached its end of simulation and will start
              //closing, not moving to the next step. No need to do any update, set time inc to zero
              base_steps_inc = 0.0;
              flow_time_rate = 1.0; //arbitrary since we don't advance any steps
            } else {
              //this phase contribution
              base_steps_inc = (next_phase->start - frozen_start);
              flow_time_rate = (g_timescale.is_flow_supercycled()) 
                              ? (double)base_steps_inc / g_timescale.m_therm_time_ratio
                              : base_steps_inc;
              //next phase contribution
              double next_phase_therm_time_ratio = next_phase->therm_time_ratio;
              if (g_timescale.is_conduction_supercycled(next_phase_therm_time_ratio)) {
                BASETIME next_phase_base_steps_inc = std::round((double)n_cond_base_steps_ref * next_phase_therm_time_ratio);
                base_steps_inc += next_phase_base_steps_inc;
                flow_time_rate += next_phase_base_steps_inc;
              } else {
                base_steps_inc += n_cond_base_steps_ref;
                flow_time_rate += (g_timescale.is_flow_supercycled(next_phase_therm_time_ratio)) 
                                  ? (double)n_cond_base_steps_ref / next_phase_therm_time_ratio
                                  : n_cond_base_steps_ref;
              }
              //final re-normalization to get the correct factor
              flow_time_rate /= (double)base_steps_inc;
            }
          } else {
            //FLOW REALM FROZEN
            //lrfs are not rotated as the conduction SPs traverse the frozen phase
            base_steps_inc = 0.0;
            flow_time_rate = 1.0; //arbitrary since we don't advance any steps
          }
        } else {
          //FLOW & CONDUCTION REALMS ACTIVE
          double next_phase_therm_time_ratio = next_phase->therm_time_ratio;
          if (g_timescale.is_flow_supercycled(next_phase_therm_time_ratio)) { //flow supercycled
            flow_time_rate = 1.0 / next_phase_therm_time_ratio;
            base_steps_inc = n_cond_base_steps_ref;
          } else { //conduction supercycled
            flow_time_rate = 1.0;
            base_steps_inc = n_cond_base_steps_ref * next_phase_therm_time_ratio;
          }
        }
        n_time_inc = base_steps_inc / g_timescale.m_n_base_timesteps_per_timestep;
      } else {
        //FLOW SPs
        double next_phase_therm_time_ratio;
        double n_flow_base_steps_ref = g_timescale.m_n_lb_base_steps;
        if (next_phase->time_coupling == eTIME_COUPLING_SCHEME::FreezeOne &&
            next_phase->frozen_solver == eCOUPLED_SOLVER::FlowSolver) {
          //Flow solver jumps over the frozen phase till the next unfrozen phase (if present)
          next_phase = sim.time_coupling_info.next_unfrozen_phase();
          if (next_phase == nullptr) {
            //Flow is never unfrozen again, implying that it has reached its end of simulation and will start
            //closing, not moving to the next step. No need to do any update, set time inc to zero
            n_flow_base_steps_ref = 0.0;
            next_phase_therm_time_ratio = 1.0; //arbitrary since we don't advance any steps
          } else {
            next_phase_therm_time_ratio = next_phase->therm_time_ratio;
          }
        } else {
          next_phase_therm_time_ratio = g_timescale.m_therm_time_ratio;
        }
        if (g_timescale.is_flow_supercycled(next_phase_therm_time_ratio)) { //flow supercycled
          flow_time_rate = 1.0 / next_phase_therm_time_ratio;
          base_steps_inc = n_flow_base_steps_ref * flow_time_rate;
        } else {
          flow_time_rate = 1.0;
          base_steps_inc = n_flow_base_steps_ref;
        }
        n_time_inc = base_steps_inc / g_timescale.m_n_base_timesteps_per_timestep;
      }
    }
    //Updates the local reference frames only n_time_inc > 0
    if (n_time_inc == 1) {
        sim_update_local_ref_frames(last_coarsest_active_scale, coarsest_active_scale, 
                                    TRUE, n_time_inc, flow_time_rate, FALSE);
    } else if (n_time_inc > 1) {
      //Determine if update can be done in a single pass, or needs to be broken into multiple iterations in the case
      //of a supercycled realm to align with the other realm, so both reach to the same value
      BOOLEAN single_pass = TRUE;
      LRF_PHYSICS_DESCRIPTOR lrf = sim.lrf_physics_descs;
      ccDOTIMES(i, n_lrf_physics_descs) {
        if (lrf->is_angular_vel_time_varying && lrf->is_active) {
          //Instead of a single pass with a larger increment to determine the angular position, needs to update the angular
          //position multiple times as done by the other realm to reach the same angular position
          single_pass = FALSE;
          break;
        }
        lrf++;
      }
      sim_update_local_ref_frames(last_coarsest_active_scale, coarsest_active_scale, 
                                  single_pass, n_time_inc, flow_time_rate, FALSE);
    }
  }
}

// sim_update_global_ref_frame is called just after g_timescale.m_time is incremented.
// last_coarsest_active_scale is the coarsest scale on which dynamics was just
// performed, i.e., coarsest_active_scale(g_timescale.m_time - 1).
VOID sim_update_global_ref_frame(asINT32 last_coarsest_active_scale,
                                 asINT32 coarsest_active_scale,
                                 GRF grf_var,
                                 BASETIME sim_time)
{
  GRF_PARAMETERS parms = sim.grf_physics_desc->parameters();
  asINT32 csys_index = parms->coord_sys.value;
  CSYS csys = NULL;
  if (is_csys_index_non_default(csys_index))
    csys = &sim.csys_table[csys_index];

  dFLOAT timestep_per_lat_time_inc = 1.0 / g_adv_fraction;
  dFLOAT lat_time_inc_per_timestep = g_adv_fraction;

  if (sim.grf_physics_desc->is_motion_via_accel) {
    // The term grf_var->linear_accel in the next 3 lines is actually shorthand for:
    //
    //    (N cell/LatticeTimeInc^2) * (1 timestep)
    //
    // while the left side has units of cell/LatticeTimeInc. Hence the need to multiply
    // by lat_time_inc_per_timestep.
    //
    grf_var->ref_pt_vel[0] += lat_time_inc_per_timestep * grf_var->linear_accel[0];
    grf_var->ref_pt_vel[1] += lat_time_inc_per_timestep * grf_var->linear_accel[1];
    grf_var->ref_pt_vel[2] += lat_time_inc_per_timestep * grf_var->linear_accel[2];
  }

  dFLOAT neg_grf_ref_pt_vel[3];
  neg_grf_ref_pt_vel[0] = -grf_var->ref_pt_vel[0];
  neg_grf_ref_pt_vel[1] = -grf_var->ref_pt_vel[1];
  neg_grf_ref_pt_vel[2] = -grf_var->ref_pt_vel[2];
    
  if (grf_var->is_rotation) {
    dFLOAT a[3];	// axis of rotation thru origin (unit vector)
    a[0] = grf_var->angular_vel[0];
    a[1] = grf_var->angular_vel[1];
    a[2] = grf_var->angular_vel[2];

    // |a| * delta_t is angle of rotation.
    dFLOAT omega = sqrt(vdot(a,a)); // units of omega are rad/LatticeTimeInc

    // If the global ref frame angular vel or ref point vel vary in time, we have to track
    // grf_var->ground_to_global_translation since it is used in meas windows.
    if (omega == 0) {
      // no rotation in the last timestep, but rotation happens at some point
      
      if (grf_var->is_time_varying) {
        // neg_grf_ref_pt_vel in the next line is shorthand for:
        //
        //   (N cells/LatticeTimeInc) * (1 timestep)
        //
        // The translation vector needs to have units of cells. Hence the need to
        // multiply by lat_time_inc_per_timestep.
        //
        sBG_VECTOR3d translation_vec( lat_time_inc_per_timestep * neg_grf_ref_pt_vel[0], 
                                      lat_time_inc_per_timestep * neg_grf_ref_pt_vel[1], 
                                      lat_time_inc_per_timestep * neg_grf_ref_pt_vel[2] );
        sBG_TRANSFORM3d xform( BG_TRANSLATION, translation_vec );

        grf_var->ground_to_global_xform = xform * grf_var->ground_to_global_xform;

        grf_var->ground_to_global_translation[0] = grf_var->ground_to_global_xform.M(0,3); // row 0, column 3
        grf_var->ground_to_global_translation[1] = grf_var->ground_to_global_xform.M(1,3); // row 1, column 3
        grf_var->ground_to_global_translation[2] = grf_var->ground_to_global_xform.M(2,3); // row 2, column 3
      }

    } else {
      vscale(a, 1/omega, a);	// unitize

      omega *= lat_time_inc_per_timestep;    // convert omega from rad/LatticeTimeInc to rad/timestep

      omega = -omega; 		// rotation of ground frame, not body frame

      if (grf_var->is_time_varying) {
        // find effective axis for angular vel
        dFLOAT pt_on_axis[3];     // point on axis of rotation
        dFLOAT ref_pt_vel_par[3]; // component of ref pt vel parallel to angular vel
	calculate_point_on_rotation_axis(grf_var->ref_point, grf_var->ref_pt_vel, a, grf_var->angular_vel, pt_on_axis, ref_pt_vel_par);

        // incorporate rotation about effective axis into global to ground xform
        sBG_VECTOR3d bg_pt_on_axis( pt_on_axis[0], pt_on_axis[1], pt_on_axis[2] );
        sBG_TRANSFORM3d xform_to_origin( BG_TRANSLATION, -bg_pt_on_axis );
        sBG_VECTOR3d axis( a[0], a[1], a[2] );
        sBG_TRANSFORM3d xform_rotate( BG_ROTATION, axis, omega );
        sBG_TRANSFORM3d xform_from_origin( BG_TRANSLATION, bg_pt_on_axis );
        grf_var->ground_to_global_xform = (( xform_from_origin * xform_rotate ) * xform_to_origin ) * grf_var->ground_to_global_xform;
        
        // Incorporate component of ref point vel parallel to angular vel into global to ground xform.
        // ref_pt_vel_par in the next line is shorthand for:
        //
        //   (N cells/LatticeTimeInc) * (1 timestep)
        //
        // The translation vector needs to have units of cells. Hence the need to
        // multiply by lat_time_inc_per_timestep.
        //
        sBG_VECTOR3d ref_pt_translation( lat_time_inc_per_timestep * ref_pt_vel_par[0], 
                                         lat_time_inc_per_timestep * ref_pt_vel_par[1], 
                                         lat_time_inc_per_timestep * ref_pt_vel_par[2] );
        sBG_TRANSFORM3d xform( BG_TRANSLATION, ref_pt_translation );
        grf_var->ground_to_global_xform = xform * grf_var->ground_to_global_xform;

        grf_var->ground_to_global_translation[0] = grf_var->ground_to_global_xform.M(0,3); // row 0, column 3
        grf_var->ground_to_global_translation[1] = grf_var->ground_to_global_xform.M(1,3); // row 1, column 3
        grf_var->ground_to_global_translation[2] = grf_var->ground_to_global_xform.M(2,3); // row 2, column 3

        if (fabs(a[0] - grf_var->last_axis[0]) > 1e-6
            || fabs(a[1] - grf_var->last_axis[1]) > 1e-6
            || fabs(a[2] - grf_var->last_axis[2]) > 1e-6
            || fabs(pt_on_axis[0] - grf_var->last_pt_on_axis[0]) > 1e-6
            || fabs(pt_on_axis[1] - grf_var->last_pt_on_axis[1]) > 1e-6
            || fabs(pt_on_axis[2] - grf_var->last_pt_on_axis[2]) > 1e-6) {
          grf_var->axis_change_mark ^= 1;
          vcopy(grf_var->last_axis, a);
          vcopy(grf_var->last_pt_on_axis, pt_on_axis);
        }
      }

      // This rotation about unit vector a is described by normalized quaternion q:
      //
      //    q = cos(omega/2) + sin(omega/2)a
      // 
      // We can rotate a point p thru the angle omega about rotation axis a (yielding p')
      // via the quaternion multiplication:
      //
      //   P' = q P q_inverse
      //
      // where P is the quaternion representing p (the scalar part is zero, and the 
      // vector part is p). We extract p' from P'.
      // 
      // The inverse of a normalized quaternion q is the conjugate q*:
      //
      //   q* = cos(omega/2) - sin(omega/2)a
      //
      // The product of 2 normalized quaternions is also normalized.

      dFLOAT q[4];
      q[0] = cos(omega/2);
      dFLOAT sin_omega2 = sin(omega/2);
      vscale(q + 1, sin_omega2, a);

      dFLOAT q1[4];
      quaternion_multiply(q1, q, grf_var->quaternion);
      grf_var->quaternion[0] = q1[0];
      grf_var->quaternion[1] = q1[1];
      grf_var->quaternion[2] = q1[2];
      grf_var->quaternion[3] = q1[3];

      grf_var->quaternion_inverse[0] = q1[0];
      grf_var->quaternion_inverse[1] = -q1[1];
      grf_var->quaternion_inverse[2] = -q1[2];
      grf_var->quaternion_inverse[3] = -q1[3];

      // grf_var->angle_rotated is only used if the axis of rotation is fixed for 
      // the duration of a meas window frame.
      grf_var->angle_rotated = 2 * acos(grf_var->quaternion_inverse[0]);
      if (grf_var->angle_rotated < 0)
        grf_var->angle_rotated += 2.0 * PI;
      else if (grf_var->angle_rotated >= 2.0 * PI)
        grf_var->angle_rotated -= 2.0 * PI;
    }
  } else {
    // no rotation at anytime in the simulation
    if (grf_var->is_time_varying) {
      // neg_grf_ref_pt_vel in the next line is shorthand for:
      //
      //   (N cells/LatticeTimeInc) * (1 timestep)
      //
      // The translation vector needs to have units of cells. Hence the need to
      // multiply by lat_time_inc_per_timestep.
      //
      dFLOAT x[3];
      vscale(x, lat_time_inc_per_timestep, neg_grf_ref_pt_vel);
      vinc(grf_var->ground_to_global_translation, x);
    }
  }

  if (sim.grf_physics_desc->is_ref_vel_time_varying
      || sim.grf_physics_desc->is_ref_accel_time_varying) {
    dFLOAT d[3];	// velocity direction - unit vector
    vcopy(d, grf_var->ref_pt_vel);
    dFLOAT vmag = sqrt(vdot(d,d));
    if (vmag != 0) {
      vscale(d, 1/vmag, d);
      if (fabs(d[0] - grf_var->last_ref_vel_dir[0]) > 1e-6
          || fabs(d[1] - grf_var->last_ref_vel_dir[1]) > 1e-6
          || fabs(d[2] - grf_var->last_ref_vel_dir[2]) > 1e-6) {
        grf_var->ref_pt_vel_dir_change_mark ^= 1;
        vcopy(grf_var->last_ref_vel_dir, d);
      }
    }
  }
      
  if (sim.grf_physics_desc->some_parameter_time_varying) {
    dFLOAT grf_domega[3];

    sim.grf_physics_desc->eval_time_varying_only_parameter_program(sim_time + 1, g_timescale.m_powertherm_time, global_eqn_error_handler);

    dFLOAT angular_vel_m1[3]; // angular velocity at t-1
    angular_vel_m1[0] = grf_var->angular_vel[0];
    angular_vel_m1[1] = grf_var->angular_vel[1];
    angular_vel_m1[2] = grf_var->angular_vel[2];

    grf_var->angular_vel[0] = grf_var->angular_vel1[0];
    grf_var->angular_vel[1] = grf_var->angular_vel1[1];
    grf_var->angular_vel[2] = grf_var->angular_vel1[2];

    if (!parms->angular_vel[0].is_constant() || !parms->angular_vel[1].is_constant() || !parms->angular_vel[2].is_constant()) {
      grf_var->angular_vel1[0] = parms->angular_vel[0].value;
      grf_var->angular_vel1[1] = parms->angular_vel[1].value;
      grf_var->angular_vel1[2] = parms->angular_vel[2].value;
    } else {
      grf_var->angular_vel1[0] = parms->angular_vel[0].value;
      grf_var->angular_vel1[1] = parms->angular_vel[1].value;
      grf_var->angular_vel1[2] = parms->angular_vel[2].value;
    }
    if (csys != NULL) xform_vector(grf_var->angular_vel1, csys);

    // The subtract in the next line is shorthand for:
    //
    //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
    //
    // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
    //
    //     rad/LatticeTimeInc^2
    //
    grf_domega[0] = 0.5 * timestep_per_lat_time_inc * (grf_var->angular_vel1[0] - angular_vel_m1[0]);
    grf_domega[1] = 0.5 * timestep_per_lat_time_inc * (grf_var->angular_vel1[1] - angular_vel_m1[1]);
    grf_domega[2] = 0.5 * timestep_per_lat_time_inc * (grf_var->angular_vel1[2] - angular_vel_m1[2]);

    dFLOAT du[3];		// dU/dt
    if (sim.grf_physics_desc->is_motion_via_accel) {
      du[0] = grf_var->linear_accel1[0];
      du[1] = grf_var->linear_accel1[1];
      du[2] = grf_var->linear_accel1[2];

      grf_var->linear_accel1[0] = parms->ref_accel[0].value;
      grf_var->linear_accel1[1] = parms->ref_accel[1].value;
      grf_var->linear_accel1[2] = parms->ref_accel[2].value;
      if (csys != NULL) xform_vector(grf_var->linear_accel1, csys);
    }
    else {
      if (!sim.grf_physics_desc->is_ref_vel_time_varying) {
	du[0] = du[1] = du[2] = 0;
      } else {
	dFLOAT u_m1[3]; // velocity at t-1
	u_m1[0] = grf_var->ref_pt_vel[0];
	u_m1[1] = grf_var->ref_pt_vel[1];
	u_m1[2] = grf_var->ref_pt_vel[2];

	grf_var->ref_pt_vel[0] = grf_var->ref_pt_vel1[0];
	grf_var->ref_pt_vel[1] = grf_var->ref_pt_vel1[1];
	grf_var->ref_pt_vel[2] = grf_var->ref_pt_vel1[2];

	grf_var->ref_pt_vel1[0] = parms->ref_vel[0].value;
	grf_var->ref_pt_vel1[1] = parms->ref_vel[1].value;
	grf_var->ref_pt_vel1[2] = parms->ref_vel[2].value;
	if (csys != NULL) xform_vector(grf_var->ref_pt_vel1, csys);

        // The subtract in the next line is shorthand for:
        //
        //     (U cell/LatticeTimeInc - V cell/LatticeTimeInc) / (1 timestep)
        //
        // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
        //
        //     cell/LatticeTimeInc^2
        //
	du[0] = 0.5 * timestep_per_lat_time_inc * (grf_var->ref_pt_vel1[0] - u_m1[0]);
	du[1] = 0.5 * timestep_per_lat_time_inc * (grf_var->ref_pt_vel1[1] - u_m1[1]);
	du[2] = 0.5 * timestep_per_lat_time_inc * (grf_var->ref_pt_vel1[2] - u_m1[2]);
      }
    }

    // A = dU/dt - U x omega
    // (U x omega) has units of LatticeVelocity/timestep (just like du)
    vcopy(grf_var->linear_accel_m1, grf_var->linear_accel);
    dFLOAT cross[3]; vcross(cross, grf_var->ref_pt_vel, grf_var->angular_vel);
    grf_var->linear_accel[0] = du[0] - cross[0];
    grf_var->linear_accel[1] = du[1] - cross[1];
    grf_var->linear_accel[2] = du[2] - cross[2];

    DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, last_coarsest_active_scale) {
      grf_var->domega[scale][0] = 0;
      grf_var->domega[scale][1] = 0;
      grf_var->domega[scale][2] = 0;
    }

    {
      DO_SCALES_FINE_TO_COARSE(scale) {
	grf_var->domega[scale][0] += grf_domega[0];
	grf_var->domega[scale][1] += grf_domega[1];
	grf_var->domega[scale][2] += grf_domega[2];
      }
    }

    // Convert accumlated values to averages for scales which will be subject
    // to dynamics. COARSEST_ACTIVE_SCALE becomes LAST_COARSEST_ACTIVE_SCALE
    // for the next call to this fcn, so these values will be zeroed.
    {
      DO_SCALE_FROM_FINE_TO_COARSE(scale, coarsen_scale(FINEST_SCALE), coarsest_active_scale) {
	dFLOAT scale_factor = 1.0 / scale_to_delta_t(scale);
	grf_var->domega[scale][0] *= scale_factor;
	grf_var->domega[scale][1] *= scale_factor;
	grf_var->domega[scale][2] *= scale_factor;
      }
    }
  }
}

VOID sim_predict_global_ref_frame(asINT32 last_coarsest_active_scale,
                                 asINT32 coarsest_active_scale,
                                 BASETIME sim_time)
{
  //Predict velocity increment of scale for next timestep
  //"delta_u_of_scale":      vel increment of scale per timestep 
  //"mean_delta_u_of_scale": averaged vel increment of scale per timestep.

  sGRF grf_var = sim.grf;
  dFLOAT du[3] = {0.0};
  dFLOAT du_grf[3] = {0.0};
  dFLOAT du_from_mean_accel[3] = {0.0};
  dFLOAT du_sum[3] = {0.0};
  dFLOAT mean_accel[3] = {0.0};
  asINT32 NT = scale_to_delta_t(coarsest_active_scale);
  ///////////////// initialize finest scale
  asINT32 scale = FINEST_SCALE;
  vcopy(sim.grf.delta_u_of_scale_prior[scale], grf_var.linear_accel);
  vscale(sim.grf.mean_delta_u_of_scale_prior[scale], 0.5, grf_var.linear_accel);
  sim_time ++;
  sim_update_global_ref_frame(last_coarsest_active_scale, coarsest_active_scale, &grf_var, sim_time);
  vinc(du_grf, grf_var.linear_accel);
  vscale(mean_accel, 0.5, grf_var.linear_accel);
  vcopy(sim.grf.delta_u_of_scale[scale], du_grf);
  vcopy(sim.grf.mean_delta_u_of_scale[scale], mean_accel);
  ////////////////
  vinc(du, mean_accel);
  vinc(du_sum, du);
  ////////////////
  scale = coarsen_scale(scale);
  asINT32 N_step = scale_to_delta_t(scale);
  dFLOAT scale_factor = 1.0 / N_step;
  for (asINT32 i=2; i<=NT; i++) {
    sim_time ++;
    sim_update_global_ref_frame(last_coarsest_active_scale, coarsest_active_scale, &grf_var, sim_time);
    mean_accel[0] = 0.5 * (grf_var.linear_accel_m1[0] + grf_var.linear_accel[0]);
    mean_accel[1] = 0.5 * (grf_var.linear_accel_m1[1] + grf_var.linear_accel[1]);
    mean_accel[2] = 0.5 * (grf_var.linear_accel_m1[2] + grf_var.linear_accel[2]);
    vinc(du_grf, grf_var.linear_accel);
    vinc(du, mean_accel);
    vinc(du_sum, du);
    if(i == N_step){ //scale update
      vcopy(sim.grf.delta_u_of_scale_prior[scale], sim.grf.delta_u_of_scale[scale]);
      vscale(sim.grf.delta_u_of_scale[scale], scale_factor, du_grf);
      vsub(sim.grf.mean_delta_u_of_scale_prior[scale], sim.grf.delta_u_of_scale_prior[scale], sim.grf.mean_delta_u_of_scale[scale]);
      vscale(sim.grf.mean_delta_u_of_scale[scale], scale_factor*scale_factor, du_sum);
      scale = coarsen_scale(scale);
      N_step = scale_to_delta_t(scale);
      scale_factor = 1.0 / N_step;
    }
  }
}


size_t sSIM_INFO::ckpt_lrf_state_len()
{ 
  size_t len = 0;
  if (n_lrf_physics_descs > 0) {
    asINT32 i;
    LRF_PHYSICS_DESCRIPTOR lrf = lrf_physics_descs;
    for (i=0; i<n_lrf_physics_descs; i++, lrf++) {
      if (lrf->domega)
        len += sim.num_scales * sizeof(lrf->domega[0]);
      if (lrf->is_mlrf_on) {
        len += 3 * sizeof(lrf->angular_vel[0]);
        len += sizeof(lrf->omega);
        len += sizeof(lrf->omega1);
        len += sizeof(lrf->initial_angle_rotated);
        len += sizeof(lrf->initial_n_revolutions);
        len += sizeof(lrf->angle_rotated);
        len += sizeof(lrf->angle_rotated1);
        len += sizeof(lrf->n_revolutions);
        len += sizeof(lrf->local_to_global_rotation_matrix);
        len += sizeof(lrf->local_to_containing_rotation_matrix);
        len += sizeof(lrf->containing_to_local_rotation_matrix);
        len += sizeof(lrf->local_to_containing_rotation_matrix1);
        len += sizeof(lrf->containing_to_local_rotation_matrix1);
        len += 3 * sizeof(lrf->omega_total[0]);
        len += 3 * sizeof(lrf->omega_total1[0]);
        len += 3 * sizeof(lrf->linear_vel[0]);
        len += 3 * sizeof(lrf->linear_accel[0]);
      }
      // Only checkpoint domega quantities if there is angular time variation
      if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
        if (lrf->domega_total)
          len += sim.num_scales * 3 * sizeof(lrf->domega_total[0]);
        else
          msg_internal_error("domega_total is NULL but should have been allocated.");
        if (lrf->domega_linear_accel)
          len += sim.num_scales * 3 * sizeof(lrf->domega_linear_accel[0]);
        else
          msg_internal_error("domega_linear_accel is NULL but should have been allocated.");
      }

    }
  }
  return len;
}  
  
VOID sSIM_INFO::ckpt_lrf_state()
{ 
  if (n_lrf_physics_descs > 0) {
    LGI_NIRF_STATE record;
    
    record.n_bytes_nirf_state = 0;

    asINT32 i;
    LRF_PHYSICS_DESCRIPTOR lrf = lrf_physics_descs;
    for (i=0; i<n_lrf_physics_descs; i++, lrf++) {
      if (lrf->domega)
        record.n_bytes_nirf_state += sim.num_scales * sizeof(lrf->domega[0]);
      if (lrf->is_mlrf_on) {
        record.n_bytes_nirf_state += 3 * sizeof(lrf->angular_vel[0]);
        record.n_bytes_nirf_state += sizeof(lrf->omega);
        record.n_bytes_nirf_state += sizeof(lrf->omega1);
        record.n_bytes_nirf_state += sizeof(lrf->initial_angle_rotated);
        record.n_bytes_nirf_state += sizeof(lrf->initial_n_revolutions);
        record.n_bytes_nirf_state += sizeof(lrf->angle_rotated);
        record.n_bytes_nirf_state += sizeof(lrf->angle_rotated1);
        record.n_bytes_nirf_state += sizeof(lrf->n_revolutions);
        record.n_bytes_nirf_state += sizeof(lrf->local_to_global_rotation_matrix);
        record.n_bytes_nirf_state += sizeof(lrf->local_to_containing_rotation_matrix);
        record.n_bytes_nirf_state += sizeof(lrf->containing_to_local_rotation_matrix);
        record.n_bytes_nirf_state += sizeof(lrf->local_to_containing_rotation_matrix1);
        record.n_bytes_nirf_state += sizeof(lrf->containing_to_local_rotation_matrix1);
        record.n_bytes_nirf_state += 3 * sizeof(lrf->omega_total[0]);
        record.n_bytes_nirf_state += 3 * sizeof(lrf->omega_total1[0]);
        record.n_bytes_nirf_state += 3 * sizeof(lrf->linear_vel[0]);
        record.n_bytes_nirf_state += 3 * sizeof(lrf->linear_accel[0]);
      }
      // Only checkpoint domega quantities if there is angular time variation
      if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
        if (lrf->domega_total)
          record.n_bytes_nirf_state += sim.num_scales * 3 * sizeof(lrf->domega_total[0]);
        else
          msg_internal_error("domega_total is NULL but should have been allocated.");
        if (lrf->domega_linear_accel)
          record.n_bytes_nirf_state += sim.num_scales * 3 * sizeof(lrf->domega_linear_accel[0]);
        else
          msg_internal_error("domega_linear_accel is NULL but should have been allocated.");
      }

    }

    if (record.n_bytes_nirf_state > 0) {
      asINT32 record_length = sizeof(record) + record.n_bytes_nirf_state;
      lgi_write_init_tag (&record, LGI_LRF_STATE_TAG, record_length);

      write_ckpt_lgi_head(record);
      lrf = lrf_physics_descs;
      for (i=0; i<n_lrf_physics_descs; i++, lrf++) {
      	if (lrf->domega)
          write_ckpt_lgi(lrf->domega, sim.num_scales * sizeof(lrf->domega[0]));
        if (lrf->is_mlrf_on) {
          write_ckpt_lgi(&lrf->angular_vel[0], 3 * sizeof(lrf->angular_vel[0]));
          write_ckpt_lgi(lrf->omega);
          write_ckpt_lgi(lrf->omega1);
          write_ckpt_lgi(lrf->initial_angle_rotated);
          write_ckpt_lgi(lrf->initial_n_revolutions);
          write_ckpt_lgi(lrf->angle_rotated);
          write_ckpt_lgi(lrf->angle_rotated1);
          write_ckpt_lgi(lrf->n_revolutions);
          write_ckpt_lgi(lrf->local_to_global_rotation_matrix, sizeof(lrf->local_to_global_rotation_matrix));
          write_ckpt_lgi(lrf->local_to_containing_rotation_matrix, sizeof(lrf->local_to_containing_rotation_matrix));
          write_ckpt_lgi(lrf->containing_to_local_rotation_matrix, sizeof(lrf->containing_to_local_rotation_matrix));
          write_ckpt_lgi(lrf->local_to_containing_rotation_matrix1, sizeof(lrf->local_to_containing_rotation_matrix1));
          write_ckpt_lgi(lrf->containing_to_local_rotation_matrix1, sizeof(lrf->containing_to_local_rotation_matrix1));
          write_ckpt_lgi(&lrf->omega_total[0], 3 * sizeof(lrf->omega_total[0]));
          write_ckpt_lgi(&lrf->omega_total1[0], 3 * sizeof(lrf->omega_total1[0]));
          write_ckpt_lgi(&lrf->linear_vel[0], 3 * sizeof(lrf->linear_vel[0]));
          write_ckpt_lgi(&lrf->linear_accel[0], 3 * sizeof(lrf->linear_accel[0]));
        }
        if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
          write_ckpt_lgi(lrf->domega_total, 3 * sim.num_scales * sizeof(lrf->domega_total[0]));
          write_ckpt_lgi(lrf->domega_linear_accel, 3 * sim.num_scales * sizeof(lrf->domega_linear_accel[0]));
        }
      }
    }
  }
}

VOID sSIM_INFO::ckpt_lrf_state(sCKPT_BUFFER& pio_ckpt_buff)
{ 
  size_t len = ckpt_state_len<sLRF_PHYSICS_DESCRIPTOR>();
  pio_ckpt_buff.write(&len);
  if (n_lrf_physics_descs > 0) {

    if (len > 0) {
      LRF_PHYSICS_DESCRIPTOR lrf = lrf_physics_descs;
      for (asINT32 i=0; i<n_lrf_physics_descs; i++, lrf++) {
      	if (lrf->domega)
          pio_ckpt_buff.write(lrf->domega, sim.num_scales * sizeof(lrf->domega[0]));
        if (lrf->is_mlrf_on) {
          pio_ckpt_buff.write(&lrf->angular_vel);
          pio_ckpt_buff.write(&lrf->omega);
          pio_ckpt_buff.write(&lrf->omega1);
          pio_ckpt_buff.write(&lrf->initial_angle_rotated);
          pio_ckpt_buff.write(&lrf->initial_n_revolutions);
          pio_ckpt_buff.write(&lrf->angle_rotated);
          pio_ckpt_buff.write(&lrf->angle_rotated1);
          pio_ckpt_buff.write(&lrf->n_revolutions);
          pio_ckpt_buff.write(&lrf->local_to_global_rotation_matrix);
          pio_ckpt_buff.write(&lrf->local_to_containing_rotation_matrix);
          pio_ckpt_buff.write(&lrf->containing_to_local_rotation_matrix);
          pio_ckpt_buff.write(&lrf->local_to_containing_rotation_matrix1);
          pio_ckpt_buff.write(&lrf->containing_to_local_rotation_matrix1);
          pio_ckpt_buff.write(&lrf->omega_total);
          pio_ckpt_buff.write(&lrf->omega_total1);
          pio_ckpt_buff.write(&lrf->linear_vel);
          pio_ckpt_buff.write(&lrf->linear_accel);
        }
        if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
         pio_ckpt_buff.write(lrf->domega_total, 3 * sim.num_scales * sizeof(lrf->domega_total[0]));
         pio_ckpt_buff.write(lrf->domega_linear_accel, 3 * sim.num_scales * sizeof(lrf->domega_linear_accel[0]));
        }
      }
    }
  }
}

VOID sSIM_INFO::read_ckpt_lrf_state()
{ 
  if (n_lrf_physics_descs <= 0) {
    msg_internal_error("Checkpoint file includes local reference frames"
                       " but no local reference frames are present in this simulation.");
  } else {
    LGI_NIRF_STATE record;

    read_lgi_head(record);

    asINT32 n_bytes_nirf_state = 0;

    asINT32 i;
    LRF_PHYSICS_DESCRIPTOR lrf = lrf_physics_descs;
    for (i=0; i<n_lrf_physics_descs; i++, lrf++) {
      if (lrf->domega)
        n_bytes_nirf_state += sim.num_scales * sizeof(lrf->domega[0]);
      if (lrf->is_mlrf_on) {
        n_bytes_nirf_state += 3 * sizeof(lrf->angular_vel[0]);
        n_bytes_nirf_state += sizeof(lrf->omega);
        n_bytes_nirf_state += sizeof(lrf->omega1);
        n_bytes_nirf_state += sizeof(lrf->initial_angle_rotated);
        n_bytes_nirf_state += sizeof(lrf->initial_n_revolutions);
        n_bytes_nirf_state += sizeof(lrf->angle_rotated);
        n_bytes_nirf_state += sizeof(lrf->angle_rotated1);
        n_bytes_nirf_state += sizeof(lrf->n_revolutions);
        n_bytes_nirf_state += sizeof(lrf->local_to_global_rotation_matrix);
        n_bytes_nirf_state += sizeof(lrf->local_to_containing_rotation_matrix);
        n_bytes_nirf_state += sizeof(lrf->containing_to_local_rotation_matrix);
        n_bytes_nirf_state += sizeof(lrf->local_to_containing_rotation_matrix1);
        n_bytes_nirf_state += sizeof(lrf->containing_to_local_rotation_matrix1);
        n_bytes_nirf_state += 3 * sizeof(lrf->omega_total[0]);
        n_bytes_nirf_state += 3 * sizeof(lrf->omega_total1[0]);
        n_bytes_nirf_state += 3 * sizeof(lrf->linear_vel[0]);
        n_bytes_nirf_state += 3 * sizeof(lrf->linear_accel[0]);
      }
      if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
        // domega_for_lrf and domega_accel_terms need to be read in
        // NOTE: Make sure to put in the correct FP type here
        n_bytes_nirf_state += 3 * sim.num_scales * sizeof(dFLOAT);
        n_bytes_nirf_state += 3 * sim.num_scales * sizeof(dFLOAT);
      }
    }

    if (n_bytes_nirf_state > 0) {
      if (record.n_bytes_nirf_state != n_bytes_nirf_state)
        msg_internal_error("Size of LGI local reference frame state disagrees"
                           " with the internal simulator state");

      lrf = lrf_physics_descs;
      for (i=0; i<n_lrf_physics_descs; i++, lrf++) {
    	if (lrf->domega)
          read_lgi(lrf->domega, sim.num_scales * sizeof(lrf->domega[0]));
        if (lrf->is_mlrf_on) {
          read_lgi(&lrf->angular_vel[0], 3 * sizeof(lrf->angular_vel[0]));
          read_lgi(lrf->omega);
          read_lgi(lrf->omega1);
          read_lgi(lrf->initial_angle_rotated);
          read_lgi(lrf->initial_n_revolutions);
          read_lgi(lrf->angle_rotated);
          read_lgi(lrf->angle_rotated1);
          read_lgi(lrf->n_revolutions);
          read_lgi(lrf->local_to_global_rotation_matrix, sizeof(lrf->local_to_global_rotation_matrix));
          read_lgi(lrf->local_to_containing_rotation_matrix, sizeof(lrf->local_to_containing_rotation_matrix));
          read_lgi(lrf->containing_to_local_rotation_matrix, sizeof(lrf->containing_to_local_rotation_matrix));
          read_lgi(lrf->local_to_containing_rotation_matrix1, sizeof(lrf->local_to_containing_rotation_matrix1));
          read_lgi(lrf->containing_to_local_rotation_matrix1, sizeof(lrf->containing_to_local_rotation_matrix1));

          read_lgi(&lrf->omega_total[0], 3 * sizeof(lrf->omega_total[0]));
          read_lgi(&lrf->omega_total1[0], 3 * sizeof(lrf->omega_total1[0]));
          read_lgi(&lrf->linear_vel[0], 3 * sizeof(lrf->linear_vel[0]));
          read_lgi(&lrf->linear_accel[0], 3 * sizeof(lrf->linear_accel[0]));
        }
        if (lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on) {
          SCALE sz = sim.num_scales;
          if (lrf->domega_total)
            read_lgi(lrf->domega_total, sz * 3 * sizeof(lrf->domega_total[0]));
          else
            msg_internal_error("domega_total is NULL but should have been allocated.");
          if (lrf->domega_linear_accel)
            read_lgi(lrf->domega_linear_accel, sz * 3 * sizeof(lrf->domega_linear_accel[0]));
          else
            msg_internal_error("domega_linear_accel is NULL but should have been allocated.");
        }
      }
    }
  }
}

VOID sSIM_INFO::ckpt_movb_state()
{
  if (n_movb_physics_descs > 0) {
    LGI_MOVB_STATE record;
    record.n_bytes_movb_state = 0;
    MOVB_PHYSICS_DESCRIPTOR movb = movb_physics_descs;
    ccDOTIMES(i, n_movb_physics_descs) {
      record.n_bytes_movb_state += sizeof(movb->initial_angle_rotated);
      record.n_bytes_movb_state += sizeof(movb->angle_rotated);
      record.n_bytes_movb_state += sizeof(movb->motion_xform);
      movb++;
    }
    asINT32 record_length = sizeof(record) + record.n_bytes_movb_state;
    lgi_write_init_tag (&record, LGI_MOVB_STATE_TAG, record_length);
    write_ckpt_lgi_head(record);
    movb = movb_physics_descs;
    ccDOTIMES(i, n_movb_physics_descs) {
      write_ckpt_lgi(movb->initial_angle_rotated);
      write_ckpt_lgi(movb->angle_rotated);
      write_ckpt_lgi(movb->motion_xform);
      movb++;
    }
  }
}

VOID sSIM_INFO::ckpt_movb_state(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t len = ckpt_state_len<MOVB_PHYSICS_DESCRIPTOR>();
  pio_ckpt_buff.write(&len);
  if (n_movb_physics_descs > 0) {
    MOVB_PHYSICS_DESCRIPTOR movb = movb_physics_descs;
    ccDOTIMES(i, n_movb_physics_descs) {
      pio_ckpt_buff.write(&movb->initial_angle_rotated);
      pio_ckpt_buff.write(&movb->angle_rotated);
      pio_ckpt_buff.write(&movb->motion_xform);
      movb++;
    }
  }
}

VOID sSIM_INFO::read_ckpt_movb_state()
{ 
  if (sim.n_movb_physics_descs == 0) {
    msg_internal_error("Checkpoint file includes moving boundaries but no moving"
                       " moving boundaries ar defined for this simulation.");
  } 
  LGI_MOVB_STATE record;
  read_lgi_head(record);
  asINT32 n_bytes_movb_state = 0;
  MOVB_PHYSICS_DESCRIPTOR movb = movb_physics_descs;
  ccDOTIMES(i, n_movb_physics_descs) {
    n_bytes_movb_state += sizeof(movb->initial_angle_rotated);
    n_bytes_movb_state += sizeof(movb->angle_rotated);
    n_bytes_movb_state += sizeof(movb->motion_xform);
    movb++;
  }
  if (n_bytes_movb_state != record.n_bytes_movb_state) {
    msg_internal_error("Size of LGI moving boundaries state disagrees with the internal simulator state");
  }
  movb = movb_physics_descs;
  ccDOTIMES(i, n_movb_physics_descs) {
    read_lgi(movb->initial_angle_rotated);
    read_lgi(movb->angle_rotated);
    read_lgi(movb->motion_xform);
    movb++;
  }
}

VOID sSIM_INFO::ckpt_grf_state()
{ 
  if (grf.is_defined) {
    LGI_NIRF_STATE record;
    record.n_bytes_nirf_state = sizeof(sGRF);
    asINT32 record_length = sizeof(record) + record.n_bytes_nirf_state;
    lgi_write_init_tag (&record, LGI_GLOBAL_NIRF_STATE_TAG, record_length);
    write_ckpt_lgi_head(record);
    write_ckpt_lgi(grf);
  }
}

VOID sSIM_INFO::ckpt_grf_state(sCKPT_BUFFER& pio_ckpt_buff)
{ 
  size_t len = ckpt_state_len<sGRF>();
  pio_ckpt_buff.write(&len);
  if(grf.is_defined)
  {
    pio_ckpt_buff.write(&grf);
  }
}

VOID sSIM_INFO::read_ckpt_grf_state()
{ 
  if (!grf.is_defined) {
    msg_internal_error("Checkpoint file includes global reference frame but no global"
                       " reference frame defined for this simulation.");
  } else {
    LGI_NIRF_STATE record;
    read_lgi_head(record);
    // Allow is_time_varying and is_rotation to be changed across checkpoint restore
    BOOLEAN is_time_varying = grf.is_time_varying;
    BOOLEAN is_rotation     = grf.is_rotation;
    read_lgi(grf);
    grf.is_time_varying = is_time_varying;
    grf.is_rotation     = is_rotation;
  }
}


VOID sSIM_INFO::ckpt_thermal_accel_state()
{ 
  if (is_heat_transfer) {
    LGI_THERMAL_ACCEL_STATE record;
    LGI_TAG_ID tag_id = LGI_THERMAL_ACCEL_STATE_TAG;
    record.n_bytes_thermal_accel_state = sizeof(sTHERMAL_ACCEL_INFO);
    asINT32 record_length = sizeof(record) + record.n_bytes_thermal_accel_state;
    lgi_write_init_tag (&record, tag_id, record_length);
    write_ckpt_lgi_head(record);
    write_ckpt_lgi(thermal_accel);
  }
}

VOID sSIM_INFO::ckpt_thermal_accel_state(sCKPT_BUFFER& pio_ckpt_buff)
{ 
  size_t len = ckpt_state_len<sTHERMAL_ACCEL_INFO>();
  pio_ckpt_buff.write(&len);
  if(is_heat_transfer)
  {
    pio_ckpt_buff.write(&thermal_accel);
  }
}

VOID sSIM_INFO::read_ckpt_thermal_accel_state()
{ 
  if (!is_heat_transfer) {
    msg_internal_error("Checkpoint file includes thermal data but"
                       " isothermal in this simulation.");
  } else {
    LGI_THERMAL_ACCEL_STATE record;
    read_lgi_head(record);
    read_lgi(thermal_accel);
  }
}

VOID sSIM_INFO::set_acous_switch()
{
  if (g_timescale.m_acous_start_time > g_timescale.m_start_time) {   /* must be a thermal case */
    if(is_turb_model && (g_timescale.m_n_lb_base_steps != g_timescale.m_n_ke_pde_base_steps))
      msg_error("Lighthill switch (-acous_start_time) requires identical n_lb_base_steps(%ld) and n_ke_base_steps(%ld)!\n",
                g_timescale.m_n_lb_base_steps, g_timescale.m_n_ke_pde_base_steps);

    if(!thermal_feedback_is_on) {
      if(my_proc_id == 0)
        msg_print_no_prefix("Lighthill solver is scheduled to turn on at timestep %ld.\n", g_timescale.m_acous_start_time);
      switch_acous_during_simulation = TRUE;

      // Adjust periodicity of solvers
      g_timescale.set_acous_switch();
      g_timescale.m_acous_switch.m_init_solver_mask = init_solver_mask;
      
      // Disables heat transfer, to be enabled when doing the switch
      is_heat_transfer = FALSE;
      init_solver_mask &= (~T_PDE_ACTIVE);
      return;
    } else {
      msg_error("Lighthill switch (-acous_start_time) only supports passive scalar case! \n");
    }

  } else if (g_timescale.m_acous_start_time == g_timescale.m_start_time) {  /* must be a thermal case */
    if(!thermal_feedback_is_on) { //lighthill starts at start time */
      if(my_proc_id == 0)
        msg_print_no_prefix("Lighthill solver will start at start_time %ld. \n", g_timescale.m_start_time);
    }  else {
      msg_error("Lighthill switch (-acous_start_time) only supports passive scalar case! \n");
    }
  }

  switch_acous_during_simulation = FALSE;
  return;
}

#if BUILD_D19_LATTICE

VOID sSIM_INFO::set_T_solver_attributes()
{
  BOOLEAN use_only_pde_solver_for_temp = FALSE;
  thermal_accel.do_T_solver_switch = FALSE;
  if (is_high_subsonic_mach_regime){
#if BUILD_6X_SOLVER
    if (use_lb_energy_solver) {
      T_solver_type = LB_ENERGY;
      g_nuT_ceiling = g_nuT_ceiling_hs_lb_energy;
      g_fix_viscosity = g_fix_viscosity_lb_energy;
    } else if (use_hs_lb_entropy_solver) {
      T_solver_type = LB_ENTROPY;
      g_nuT_ceiling = g_nuT_ceiling_hs_lb_entropy;
    } else 
#endif
      T_solver_type = PDE_ENTROPY;
    is_prescribed_T_solver_switch = FALSE;
    use_only_pde_solver_for_temp = (T_solver_type == PDE_ENTROPY);
  } else if (is_heat_transfer) {
    if (m_freeze_momentum_parms.m_is_on) {
      is_prescribed_T_solver_switch = FALSE;  //for mom freeze
      if (my_proc_id == 0) {
        msg_print_no_prefix("Using LB solver for heat transfer.\n");
      }
      T_solver_type = LB_TEMPERATURE;

      m_freeze_momentum_parms.m_start_time = g_timescale.m_start_time;
      if (my_proc_id == 0) {
        msg_print_no_prefix("Velocity freeze solver starts at timestep %ld.\n",
            m_freeze_momentum_parms.m_start_time);
      }
      if (!thermal_feedback_is_on) {
        if (user_min_temp < 1.0E-6)
          sim_min_temp = g_min_temp_coeff * char_temp;

        if (user_max_temp > 0.5 * sdFLOAT_MAX)
          sim_max_temp = g_max_temp_coeff * char_temp;
      }
    } else {
      if (T_lb_scalar_solver_start_time > 0) {
        
        if (T_lb_scalar_solver_start_time == TIMESTEP_INVALID)
          msg_internal_error("Synchronous T_lb_scalar_solver_start_time(%d) is greater than LARGEST_TIMESTEP(%d). \n",T_lb_scalar_solver_start_time, LARGEST_TIMESTEP);
        
        is_prescribed_T_solver_switch = TRUE;
        
        // ensures that solver switch occurs at a time-step when all flow solvers are active
        BASETIME coarse_steps = 1 << (num_scales -1);
        BASETIME lcm_steps;
        if (is_turb_model) {
          lcm_steps = lcm(g_timescale.m_n_lb_base_steps,
                          g_timescale.m_n_t_pde_base_steps,
                          g_timescale.m_n_ke_pde_base_steps,
                          g_timescale.m_n_uds_pde_base_steps); //in base steps
        } else { //DNS case
          lcm_steps = lcm(g_timescale.m_n_lb_base_steps,
                          g_timescale.m_n_t_pde_base_steps,
                          g_timescale.m_n_uds_pde_base_steps);  //in base steps
          
        }
        lcm_steps *= coarse_steps / g_timescale.m_n_lb_base_steps; //in realm steps
        T_lb_scalar_solver_start_time = round_up_time(T_lb_scalar_solver_start_time, lcm_steps);
        
        // sets the correct solver based on the starting timestep, and schedule the event to switch solvers if needed
        BASETIME T_lb_scalar_solver_start_base_time = sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(T_lb_scalar_solver_start_time);
        if (T_lb_scalar_solver_start_base_time > g_timescale.m_start_time) {
          
          if (my_proc_id == 0 && !sim.use_melting_solver) // eutectic solver is always using PDE T solver, do not print this message (PR47523)
            msg_print_no_prefix("Temperature solver will be switched from pde solver to lb scalar solver at a synchronous timestep %d.\n", T_lb_scalar_solver_start_time);

          T_solver_type = PDE_TEMPERATURE;
          g_async_event_queue.add_entry(new_event(EVENT_ID_T_LB_SOLVER_ON, 0, T_lb_scalar_solver_start_base_time - 1, 0, BASETIME_LAST));
        } else { //T_lb_scalar_solver_start_base_time <= g_timescale.m_start_time
          T_solver_type = LB_TEMPERATURE;
        }
        
        // if switch to flow solver is scheduled beyond simulation end time, it implies that only the pde solver is used
        use_only_pde_solver_for_temp = (T_lb_scalar_solver_start_base_time > g_timescale.m_end_time);

      } else { //!is_prescribed_T_solver_switch
        T_solver_type = LB_TEMPERATURE;
        is_prescribed_T_solver_switch = FALSE;

        // During initialization the two thermal_accel structs are identical. One is chosen arbitrarily.
        if (thermal_accel.start >= 0) {
          add_thermal_acc_events();
          asINT32 acc_stime = thermal_accel.start;
          dFLOAT acc_etime = thermal_accel.end_time();
          TIMESTEP realm_start_time = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(g_timescale.m_start_time);
          if (realm_start_time >= acc_stime && realm_start_time < acc_etime) {
            asINT32 acc_mtime = (realm_start_time - acc_stime) % thermal_accel.period;
            if (acc_mtime < thermal_accel.interval) {
              T_solver_type = PDE_TEMPERATURE;
            }
          }
        }
      }
    }
  } else { //!is_heat_transfer
    T_solver_type = INVALID;
    is_prescribed_T_solver_switch = FALSE;
  }
  // If PDE solver is not used for temperatures, LB and T base steps should be same
  if (is_heat_transfer && !use_only_pde_solver_for_temp && (g_timescale.m_n_lb_base_steps != g_timescale.m_n_t_pde_base_steps)) {
    msg_error("LB temp solver requires n_lb_base_steps(%ld) equal to n_t_base_steps(%ld).\n",
              g_timescale.m_n_lb_base_steps, g_timescale.m_n_t_pde_base_steps);
  }
  return;
}
#endif

VOID sSIM_INFO::adjust_uds_solver_parameters()
{
  if (is_scalar_model) {
    if (is_high_subsonic_mach_regime){
#if BUILD_6X_SOLVER
      if (use_lb_energy_solver) {
	g_uds_diffusivity_ceiling = g_uds_diffusivity_ceiling_hs_lb_energy;
      } else if (use_hs_lb_entropy_solver) {
	g_uds_diffusivity_ceiling = g_uds_diffusivity_ceiling_hs_lb_entropy;
      } 
#endif
    }
    if (disable_scalar_diffusivity_bound)
      g_tau_uds_base = 0.5;
  }
  return;
}


__HOST__DEVICE__
inline VOID convert_vel_from_ground_to_global_frame(STP_VEC_FLOAT input_vel[3],
						    STP_VEC_FLOAT surfel_centroid[3],
						    STP_VEC_FLOAT output_vel[3])
{
  // Transform INPUT_VEL to non-inertial (body-fixed) frame

  // INPUT_VEL is defined relative to the ground, and expressed in the ground
  // coord sys.

  // First rotate according to current rotation of the ground-fixed coord system
  // with respect to the global body-fixed coord system.
  auto& sim = get_sim_ref();
  dFLOAT input[3];
  if (sim.grf.is_rotation
      && (input_vel[0] != 0 || input_vel[1] != 0 || input_vel[2] != 0)) {
    rotate_vector_from_ground_to_global_coord_sys(input_vel, input);
  } else {
    input[0] = input_vel[0];
    input[1] = input_vel[1];
    input[2] = input_vel[2];
  }

  // INPUT is still defined relative to the ground, but now expressed in the global 
  // body-fixed coord sys.

  // Now adjust according to difference in velocity of global body-fixed frame.
  dFLOAT r[3];  vsub(r, surfel_centroid, sim.grf.ref_point);
  vscale(r, g_lattice_constant, r);
  dFLOAT cross[3]; vcross(cross, sim.grf.angular_vel, r);
  output_vel[0] = input[0] - sim.grf.ref_pt_vel[0] - cross[0];
  output_vel[1] = input[1] - sim.grf.ref_pt_vel[1] - cross[1];
  output_vel[2] = input[2] - sim.grf.ref_pt_vel[2] - cross[2];

  // OUTPUT_VEL is now defined relative to the global body-fixed frame, and expressed
  // in the global body-fixed coord sys.
}

template<typename FLOAT_TYPE>
__HOST__DEVICE__
inline VOID convert_vel_from_containing_to_local_frame(FLOAT_TYPE input_vel[3], 
						   FLOAT_TYPE surfel_centroid[3], 
						   FLOAT_TYPE output_vel[3],
						   LRF_PHYSICS_DESCRIPTOR lrf)
{
  dFLOAT r[3];       vsub(r, surfel_centroid, lrf->point); 
  vscale(r, g_lattice_constant, r);
  dFLOAT u_delta[3]; vcross(u_delta, lrf->angular_vel, r);
  vsub(output_vel, input_vel, u_delta);
}

template<typename FLOAT_TYPE>  
inline VOID convert_vel_from_local_to_containing_frame(FLOAT_TYPE input_vel[3], 
						   FLOAT_TYPE surfel_centroid[3], 
						   FLOAT_TYPE output_vel[3],
						   LRF_PHYSICS_DESCRIPTOR lrf)
{
  dFLOAT r[3];       vsub(r, surfel_centroid, lrf->point); 
  vscale(r, g_lattice_constant, r);
  dFLOAT u_delta[3]; vcross(u_delta, lrf->angular_vel, r);
  vadd(output_vel, input_vel, u_delta);
}

VOID construct_path_to_global_ref_frame(LRF_PHYSICS_DESCRIPTOR lrf, asINT32 path[], asINT32 *n_steps)
{
  asINT32 containing_lrf_index =  lrf->containing_lrf_index;;
  *n_steps = 0;
  path[(*n_steps)++] = containing_lrf_index;
  while (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {
    asINT32 current_lrf_index = containing_lrf_index;
    containing_lrf_index = sim.lrf_physics_descs[current_lrf_index].containing_lrf_index;
    path[(*n_steps)++] = containing_lrf_index;
  }
}

template<typename FLOAT_TYPE>
VOID rotate_velocity_between_csys(FLOAT_TYPE input_vel[3], 
                                  FLOAT_TYPE output_vel[3], 
                                  LRF_PHYSICS_DESCRIPTOR lrf,
                                  BOOLEAN is_inverse)
{
  LRF_PHYSICS_DESCRIPTOR current_lrf = lrf; 
  asINT32 containing_lrf_index = current_lrf->containing_lrf_index;
  vcopy(output_vel,input_vel);
  if (is_inverse) {
    asINT32 path_to_global[STP_MAX_REF_FRAME_NESTED_LEVELS];
    asINT32 n_steps_to_global = 0;
    construct_path_to_global_ref_frame(lrf, path_to_global,&n_steps_to_global);
    for (asINT32 i = n_steps_to_global - 2; i >= 0; i--) {
      asINT32 current_ref_frame_index =  path_to_global[i];
      current_lrf = &sim.lrf_physics_descs[current_ref_frame_index];
      rotate_vector(output_vel,output_vel, current_lrf->containing_to_local_rotation_matrix);
    }
  } else {
    while (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {
      rotate_vector(output_vel,output_vel,current_lrf->local_to_containing_rotation_matrix);
      current_lrf = &sim.lrf_physics_descs[containing_lrf_index];
      containing_lrf_index = current_lrf->containing_lrf_index;
    }
  }
}

// Given a velocity in the local ref frame and local csys, we transform for it to
// the global ref frame in the the local csys.  Consider the example of ref frame/csys
// L < C < B < G, where "<" denotes containment (or subset).  Letting a superscript denote
// Csys and subscript denote ref frame, we have:
//
//v_C^L = v_L^L +  omega_LC^L x  r^L
//v_C^C = R_LC v_C^L
//
//v_B^C = v_C^C +  omega_CB^C x  r^C
//v_B^B = R_CB v_B^C
//
//v_G^B = v_B^B + omega_BG^B  r^B
//v_G^L = R_BL  v_G^B = R_CL R_BC v_G^B

// For details, see /proj/spec/powerflow/nested_mlrf/001/multiple_nested_vel_transforms.pdf

// 1. This function expects local_vel to be in the local CSYS
// 2. It returns global_vel in the local CSYS
template<typename FLOAT_TYPE>
__HOST__DEVICE__
VOID convert_vel_from_local_to_global_frame(FLOAT_TYPE local_vel[3],
                                            FLOAT_TYPE surfel_centroid[3],
                                            FLOAT_TYPE global_vel[3],
                                            LRF_PHYSICS_DESCRIPTOR lrf) {

  FLOAT_TYPE pt[3]; vcopy(pt, lrf->point);
  FLOAT_TYPE omega_for_lrf[3]; vcopy(omega_for_lrf, lrf->omega_total);
  FLOAT_TYPE ref_vel_for_lrf[3]; vcopy(ref_vel_for_lrf, lrf->linear_vel);

  dFLOAT r[3], vel_global[3];
  vsub(r, surfel_centroid, pt);
  vcross(vel_global, omega_for_lrf, r);
  vinc(vel_global, ref_vel_for_lrf);
  vinc(vel_global, local_vel);
  for (int i = 0; i < 3; i++) {
    global_vel[i] = vel_global[i];
  }
}

// 1. The last input parameter (is_global_vel_in_local_csys) tells the function if global_vel
//    is specified in the global CSYS or local CSYS. It defaults to FALSE (i.e. the function
//    expects global_vel to be in the global CSYS by default)
// 2. This function returns the local_vel in the local CSYS
template<typename FLOAT_TYPE>
__HOST__DEVICE__
VOID convert_vel_from_global_to_local_frame(FLOAT_TYPE global_vel[3],
                                            FLOAT_TYPE surfel_centroid[3],
                                            FLOAT_TYPE local_vel[3],
                                            LRF_PHYSICS_DESCRIPTOR lrf,
                                            cBOOLEAN is_global_vel_in_local_csys)
{
  sINT32 containing_lrf_index = lrf->containing_lrf_index;
  BOOLEAN transpose = TRUE;
  if (containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {  // nested LRF
    dFLOAT r[3], w_x_r[3];
    vsub(r, surfel_centroid, lrf->point);
    vcross(w_x_r, lrf->omega_total, r); // in local CSYS, because omega_total and r are in the local CSYS
    if (!is_global_vel_in_local_csys && lrf->is_mlrf_on) {
      // convert from global CSYS to local CSYS
      rotate_vector(global_vel, global_vel, lrf->local_to_global_rotation_matrix, transpose);
    }
    vsub(local_vel, global_vel, w_x_r); // subtract velocities in local CSYS
    vsub(local_vel, local_vel, lrf->linear_vel); // if axes are not collocated, then subtract
                                                 // velocity of frame itself
  } else { // not nested
    convert_vel_from_containing_to_local_frame(global_vel,surfel_centroid,
                                               local_vel,lrf);
  }
}

__HOST__DEVICE__
inline VOID convert_vel_from_global_to_ground_frame(STP_VEC_FLOAT input_vel[3], 
						    STP_VEC_FLOAT surfel_centroid[3], 
						    STP_VEC_FLOAT output_vel[3])
{
  // Transform input_vel to inertial (ground-fixed) frame

  // INPUT_VEL is defined relative to the global body-fixed frame, and expressed in 
  // the global body-fixed coord sys.

  // First adjust according to difference in velocity of body-fixed frame.
  auto& sim = get_sim_ref();
  dFLOAT r[3];  vsub(r, surfel_centroid, sim.grf.ref_point);
  vscale(r, g_lattice_constant, r);
  dFLOAT cross[3]; vcross(cross, sim.grf.angular_vel, r);
  output_vel[0] = input_vel[0] + sim.grf.ref_pt_vel[0] + cross[0];
  output_vel[1] = input_vel[1] + sim.grf.ref_pt_vel[1] + cross[1];
  output_vel[2] = input_vel[2] + sim.grf.ref_pt_vel[2] + cross[2];

}

// CONVERT_VEL_BETWEEN_REF_FRAMES assumes the "from" and "to" ref frames are not identical.
VOID vconvert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3], 
                                    subV3_8& centroid, 
                                    asINT32 voxel,
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index) 
{
  STP_VEC_FLOAT cen[3] = {centroid(voxel,0), centroid(voxel,1), centroid(voxel,2)};
  convert_vel_between_ref_frames(input_vel, cen, output_vel, from_ref_frame_index, to_ref_frame_index);
}

__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    STP_VEC_FLOAT surfel_centroid[3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    cBOOLEAN is_input_vel_in_local_csys)
{
  auto& sim = get_sim_ref();
  if (from_ref_frame_index == SRI_GROUND_REF_FRAME_INDEX) {
    convert_vel_from_ground_to_global_frame(input_vel, surfel_centroid, output_vel);
    if (to_ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) {
      // Transform velocity from global body-fixed frame to local body-fixed frame
      convert_vel_from_global_to_local_frame(output_vel, surfel_centroid, output_vel,
					     &sim.lrf_physics_descs[to_ref_frame_index]);
    }
  }
  else if (from_ref_frame_index == SRI_GLOBAL_REF_FRAME_INDEX) {
    if (to_ref_frame_index == SRI_GROUND_REF_FRAME_INDEX) {
      convert_vel_from_global_to_ground_frame(input_vel, surfel_centroid, output_vel);
    } else {
      // Transform velocity from global body-fixed frame to local body-fixed frame
      convert_vel_from_global_to_local_frame(input_vel, surfel_centroid, output_vel,
                                             &sim.lrf_physics_descs[to_ref_frame_index], is_input_vel_in_local_csys);
    }      
  } 
  else {
    // from_ref_frame is a local body-fixed ref frame.
    convert_vel_from_local_to_global_frame(input_vel, surfel_centroid, output_vel,
                                           &sim.lrf_physics_descs[from_ref_frame_index]);

    if (to_ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) {
      if (to_ref_frame_index == SRI_GROUND_REF_FRAME_INDEX) {
        convert_vel_from_global_to_ground_frame(output_vel, surfel_centroid, output_vel);
      }
      else {
        convert_vel_from_global_to_local_frame(output_vel, surfel_centroid, output_vel,
                                               &sim.lrf_physics_descs[to_ref_frame_index]);
      }
    }
  }
}

template<size_t N>
__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    tSFL_VAR<STP_VEC_FLOAT, N> (&point) [3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    asINT32 soxor,
                                    cBOOLEAN is_input_vel_in_local_csys) {
  STP_VEC_FLOAT point_for_soxor[3];
  v_copy(point_for_soxor, point, soxor);
  convert_vel_between_ref_frames(input_vel,
                                 point_for_soxor,
                                 output_vel,
                                 from_ref_frame_index,
                                 to_ref_frame_index,
                                 is_input_vel_in_local_csys);
}

template
__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    tSFL_VAR<STP_VEC_FLOAT, 1> (&point) [3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    asINT32 soxor,
                                    cBOOLEAN is_input_vel_in_local_csys);

#if BUILD_GPU
template
__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    tSFL_VAR<STP_VEC_FLOAT, N_SFLS_PER_MSFL> (&point) [3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    asINT32 soxor,
                                    cBOOLEAN is_input_vel_in_local_csys);
#endif

template <typename FLOAT_TYPE1, typename FLOAT_TYPE2, typename FLOAT_TYPE3>
__HOST__DEVICE__
VOID get_rotation_matrix_from_axis_angle(FLOAT_TYPE1 axis[3], FLOAT_TYPE2 angle_rotated, FLOAT_TYPE3 trans_matrix[3][3])
{
  dFLOAT omega_01 = axis[0] * axis[1];
  dFLOAT omega_02 = axis[0] * axis[2];
  dFLOAT omega_12 = axis[1] * axis[2];
  dFLOAT sin_theta = sin(angle_rotated);
  dFLOAT cos_theta = cos(angle_rotated);
  dFLOAT one_minus_cos_theta = 1.0 - cos_theta;

  trans_matrix[0][0] = axis[0] * axis[0] * one_minus_cos_theta + cos_theta;
  trans_matrix[1][1] = axis[1] * axis[1] * one_minus_cos_theta + cos_theta;
  trans_matrix[2][2] = axis[2] * axis[2] * one_minus_cos_theta + cos_theta;
  trans_matrix[0][1] = omega_01 * one_minus_cos_theta - axis[2] * sin_theta;
  trans_matrix[1][0] = omega_01 * one_minus_cos_theta + axis[2] * sin_theta;
  trans_matrix[0][2] = omega_02 * one_minus_cos_theta + axis[1] * sin_theta;
  trans_matrix[2][0] = omega_02 * one_minus_cos_theta - axis[1] * sin_theta;
  trans_matrix[1][2] = omega_12 * one_minus_cos_theta - axis[0] * sin_theta;
  trans_matrix[2][1] = omega_12 * one_minus_cos_theta + axis[0] * sin_theta;
}

#if BUILD_5G_LATTICE
sFLUID_PROPERTIES *g_mc_types = NULL;
iGRADIENT_OP     *g_gradient_operator = NULL;

VOID sFLUID_PROPERTIES::initialize (asINT32 c, EOS_TYPE type, sdFLOAT visc, sdFLOAT mol_weight)
{
  sEOS_PARAMS params;
  params.temperature = g_mp_temperature;
  params.sound_speed_squared = g_mp_sound_speed_squared;
  params.sound_speed_bound = g_mp_sound_speed_bound;
  equation_of_state = iEOS::create (type, params);
	
  asINT32 s;
  for (s = 0; s < sim.num_components; ++s) {
    if (g_mp_G[c][s] != 0.)
      break;
  }

  if (s < sim.num_components) {
    interaction_strengthes = g_mp_G[c];
    sim.is_non_ideal_gas = true;
  } else {
    interaction_strengthes = NULL;
  }

  molecular_mass = mol_weight;
  viscosity = visc;
  molecular_charge = g_mp_molecular_charge[c];

  /* if (mp_wetting_model == 0) {

    wall_potential[0] = equation_of_state->potential (mp_wall_potential[c]);
    wall_potential[1] = equation_of_state->potential (mp_wall_potential2[c]);

  } else if (mp_wetting_model == 1) {

    wall_potential[0] = mp_wall_potential[c];
    wall_potential[1] = mp_wall_potential2[c];
    } */
}

VOID sFLUID_PROPERTIES::update (asINT32 c)
{
  molecular_mass = g_mp_molecular_mass[c];
  viscosity = g_mp_viscosity[c];
  molecular_charge = g_mp_molecular_charge[c];
}


sdFLOAT mc_pressure(const sdFLOAT densities[], sdFLOAT temperature)
{
  sdFLOAT potential[MAXIMUM_NUM_COMPONENTS] = {0.};
  sdFLOAT pressure = 0.;

  for (asINT32 c = 0; c < sim.num_components; ++c) {
    potential[c] = g_mc_types[c].equation_of_state->potential (densities[c]);
  }

  for (asINT32 c = 0; c < sim.num_components; ++c) {

    pressure += densities[c] * temperature;

    sdFLOAT  *G = g_mc_types[c].interaction_strengthes;
    if (G) {
      for (asINT32 s = 0; s < sim.num_components; ++s) {
	pressure -= .5 * potential[c] * potential[s] * G[s];
      }
    }
  }

  return pressure;
}



static dFLOAT bisect (sdFLOAT rho_min,
		      sdFLOAT rho_max,
		      sdFLOAT comp0_percentage,
		      sdFLOAT p
		      )
{
  static const dFLOAT tolerance = 1.e-5;

  while (rho_max - rho_min > tolerance) {
    sdFLOAT densities[2];
    sdFLOAT rho = .5 * (rho_min + rho_max);

    densities[0] = comp0_percentage * rho;
    densities[1] = (1. - comp0_percentage) * rho;

    sdFLOAT p1 = mc_pressure (densities, g_lb_temp_constant);

    if (p1 == p)
      return rho;

    if (p1 < p)
      rho_min = rho;
    else
      rho_max = rho;
  }

  return .5 * (rho_min + rho_max);
}

VOID compute_mc_densities_from_pressure (sdFLOAT densities[2],
					 sdFLOAT p,
					 sdFLOAT comp0_percentage,
					 sdFLOAT phase_index
					 )
{
  static const int num_points = 400;
  static const sdFLOAT rho_max = 15.95;
  static const sdFLOAT gas_phase = 0.;
  static const sdFLOAT liquid_phase = 1.;
  static const sdFLOAT delta_rho = rho_max / num_points;

  static double last_p = - 1000;
  static double last_comp0_percentage = - 1000;
  static double last_phase_index = - 1000;
  static double last_comp0 = - 1000;
  static double last_comp1 = - 1000;
  static int solved_last = 0;

  sdFLOAT p_values[num_points];
  sdFLOAT rho;

  //comp0_percentage *= .01;
  comp0_percentage = (comp0_percentage > 1.0) ? 1.0 : comp0_percentage; // protect the component volume ratio when it is used to mark outlet type 

  if (solved_last && (last_p == p) && (last_phase_index == phase_index) &&
      (last_comp0_percentage == comp0_percentage)) {
    densities[0] = last_comp0;
    densities[1] = last_comp1; 
    return;
  }

  for (asINT32 i = 0; i < num_points; ++i) {
    rho = i * delta_rho;

    densities[0] = comp0_percentage * rho;
    densities[1] = (1. - comp0_percentage) * rho;

    p_values[i] = mc_pressure (densities, g_lb_temp_constant);
  }

  int index;
    
  for (index = 0; index < num_points && p_values[index] < p; ++index)

  dassert (index < num_points);

  rho = bisect ((index-1) * delta_rho, index * delta_rho, comp0_percentage, p);
	
  densities[0] = comp0_percentage * rho;
  densities[1] = (1. - comp0_percentage) * rho;

  if ((phase_index == gas_phase) ||
      (phase_index == liquid_phase)) {

    solved_last = 1;
    last_p = p;
    last_phase_index = phase_index; 
    last_comp0_percentage = comp0_percentage; 
    last_comp0 = densities[0]; 
    last_comp1 = densities[1]; 

    return;
  }

  if (phase_index == gas_phase) {
    msg_error("The equation of state does not have a gas phase at this temperature and pressure %f rho %f .", p, rho);
  }

  for (; index < num_points && p_values[index] >= p; ++index);

  if (index == num_points) {
    msg_error("The equation of state does not have a liquid phase at this temperature and pressure %f rho %f.", p, rho);
    return;
  }

  for (; index < num_points && p_values[index] < p; ++index)

  dassert (index < num_points);

  rho = bisect ((index-1) * delta_rho, index * delta_rho, comp0_percentage, p);

  densities[0] = comp0_percentage * rho;
  densities[1] = (1. - comp0_percentage) * rho;

  solved_last = 1;
  last_p = p;
  last_phase_index = phase_index; 
  last_comp0_percentage = comp0_percentage; 
  last_comp0 = densities[0]; 
  last_comp1 = densities[1]; 

  return;
}

sdFLOAT contact_angle_to_potential(sdFLOAT angle, sdFLOAT nu_over_t)
{
  sdFLOAT a0;
  if (nu_over_t <= 0.5)
    a0 = 118.55 * nu_over_t * nu_over_t -185.33 * nu_over_t-13.649;
  else if (nu_over_t <= 5.0 & nu_over_t > 0.5 )
    a0 = -0.2619 * nu_over_t -76.6751;
  else
    a0 = -0.2619 * 5.0 -76.6751;
  sdFLOAT a0_sqrd = a0 * a0;
  sdFLOAT a1 = -65.5;
  sdFLOAT a1_sqrd = a1 * a1;
  sdFLOAT a2 = 90.0 - angle;
  sdFLOAT a2_sqrd = a2 * a2;

  sdFLOAT delta = -4.0 * a0 * a1_sqrd * a1 - 27.0 * a0_sqrd * a2_sqrd;
  sdFLOAT delta_0 = -3.0 * a0 * a1;
  sdFLOAT delta_1 = 27.0 * a0_sqrd * a2;

  sdFLOAT term = 0.5 * (delta_1 + sqrt(-27.0 * a0_sqrd * delta));
  term = pow(term, 1.0/3.0);
  sdFLOAT potential = -(term + delta_0/term)/(3.0*a0);

  return potential;
}

#endif

// This must be called after
// update_all_time_varying_bsurfel_physics_parameters() in a given timestep

VOID sSIM_INFO::update_movb_xforms()
{
  MOVB_PHYSICS_DESCRIPTOR movb_physics_desc = movb_physics_descs;
  ccDOTIMES(n, n_movb_physics_descs) {
    dFLOAT angular_vel_mag = movb_physics_desc->parameters()->angular_vel.value; 
    dFLOAT ref_pt[3], unit_vec[3];
    ccDOTIMES(axis, 3) {
      ref_pt[axis] = movb_physics_desc->parameters()->axis_point[axis].value;
      unit_vec[axis] = movb_physics_desc->parameters()->axis_unit_vec[axis].value;
      movb_physics_desc->angular_vel_vec[axis] = angular_vel_mag*unit_vec[axis];
    }
    movb_physics_desc->angle_rotated += angular_vel_mag * g_adv_fraction;
    sBG_VECTOR3d bg_pt_on_axis( ref_pt[0], ref_pt[1], ref_pt[2] );
    sBG_TRANSFORM3d xform_to_origin( BG_TRANSLATION, -bg_pt_on_axis );
    sBG_TRANSFORM3d xform_from_origin( BG_TRANSLATION, bg_pt_on_axis );
    sBG_VECTOR3d axis_vec( unit_vec[0], unit_vec[1], unit_vec[2] );
    sBG_TRANSFORM3d xform_rotate( BG_ROTATION, axis_vec, movb_physics_desc->angle_rotated );
    movb_physics_desc->motion_xform = xform_from_origin * xform_rotate * xform_to_origin; 

    const sBG_TRANSFORM3d& x = movb_physics_desc->motion_xform;
    LOG_MSG("BSURFEL_INIT",LOG_TS,LOG_ATTR(n)).format(
                                                     "\n{} {} {} {}\n"
                                                       "{} {} {} {}\n"
                                                       "{} {} {} {}\n",x.M(0,0),x.M(0,1),x.M(0,2),x.M(0,3),
                                                                       x.M(1,0),x.M(1,1),x.M(1,2),x.M(1,3),
                                                                       x.M(2,0),x.M(2,1),x.M(2,2),x.M(2,3),
                                                                       x.M(3,0),x.M(3,1),x.M(3,2),x.M(3,3));
    if ( movb_physics_desc->tire ) {
      movb_physics_desc->tire->rotate_vertices(movb_physics_desc->angle_rotated);
    }

    // next movb descriptor
    movb_physics_desc++;
  }
}

// Instantiate these functions with template args "double" to keep compiler happy.
template
VOID convert_vel_from_global_to_local_frame<double>(double global_vel[3], 
                                                    double surfel_centroid[3], 
                                                    double local_vel[3],
                                                    LRF_PHYSICS_DESCRIPTOR lrf,
                                                    cBOOLEAN is_global_vel_in_local_csys);

template
VOID convert_vel_from_global_to_local_frame<float>(float global_vel[3], 
                                                   float surfel_centroid[3], 
                                                   float local_vel[3],
                                                   LRF_PHYSICS_DESCRIPTOR lrf,
                                                   cBOOLEAN is_global_vel_in_local_csys);

template
VOID convert_vel_from_local_to_global_frame<double>(double local_vel[3], 
                                                    double surfel_centroid[3], 
                                                    double global_vel[3],
                                                    LRF_PHYSICS_DESCRIPTOR lrf);

template
VOID convert_vel_from_local_to_global_frame<float>(float local_vel[3], 
                                                   float surfel_centroid[3], 
                                                   float global_vel[3],
                                                   LRF_PHYSICS_DESCRIPTOR lrf);

template
VOID get_rotation_matrix_from_axis_angle(double axis[3], double angle_rotated, double trans_matrix[3][3]);

template
VOID get_rotation_matrix_from_axis_angle(float axis[3], float angle_rotated, float trans_matrix[3][3]);

//Used during seeding
SOLVER_INDEX_MASK compute_seed_solver_index_mask(asINT32 scale, TIMESTEP timestep) {
  SOLVER_INDEX_MASK solver_index_mask = 0;
  if (sim.is_lb_model)
    solver_index_mask = (g_timescale.m_lb_tm.prior_timestep_index(scale, timestep) << LB_SOLVER);
  if (sim.is_turb_model)
    solver_index_mask |= (g_timescale.m_ke_pde_tm.prior_timestep_index(scale, timestep) << TURB_SOLVER);
  if (sim.is_heat_transfer)
    solver_index_mask |= (g_timescale.m_t_pde_tm.prior_timestep_index(scale, timestep) << T_SOLVER);
  if (sim.is_scalar_model)
    solver_index_mask |= (g_timescale.m_uds_pde_tm.prior_timestep_index(scale, timestep) << UDS_SOLVER);
  if (sim.is_conduction_model)
    solver_index_mask |= (g_timescale.m_conduction_pde_tm.prior_timestep_index(scale, timestep) << CONDUCTION_SOLVER);
  return solver_index_mask;
}

SOLVER_INDEX_MASK compute_solver_index_mask(asINT32 scale) {
  SOLVER_INDEX_MASK solver_index_mask = 0;
  solver_index_mask = (g_timescale.m_lb_tm.prior_timestep_index(scale) << LB_SOLVER);
  solver_index_mask |= (g_timescale.m_ke_pde_tm.prior_timestep_index(scale) << TURB_SOLVER);
  solver_index_mask |= (g_timescale.m_t_pde_tm.prior_timestep_index(scale) << T_SOLVER);
  solver_index_mask |= (g_timescale.m_uds_pde_tm.prior_timestep_index(scale) << UDS_SOLVER);
  solver_index_mask |= (g_timescale.m_conduction_pde_tm.prior_timestep_index(scale) << CONDUCTION_SOLVER);

  if (sim_is_scale_finer(scale, COARSEST_SCALE)) {
    SCALE coarser_scale = coarsen_scale(scale);
    solver_index_mask |= (g_timescale.m_lb_tm.prior_timestep_index(coarser_scale)
                          << (N_SOLVERS + LB_SOLVER));
    solver_index_mask |= (g_timescale.m_ke_pde_tm.prior_timestep_index(coarser_scale)
                          << (N_SOLVERS + TURB_SOLVER));
    solver_index_mask |= (g_timescale.m_t_pde_tm.prior_timestep_index(coarser_scale)
                          << (N_SOLVERS + T_SOLVER));
    solver_index_mask |= (g_timescale.m_uds_pde_tm.prior_timestep_index(coarser_scale)
                          << (N_SOLVERS + UDS_SOLVER));
    solver_index_mask |= (g_timescale.m_conduction_pde_tm.prior_timestep_index(coarser_scale)
                          << (N_SOLVERS + CONDUCTION_SOLVER));
  }
  return solver_index_mask;
}


DATA_OFFSET_TABLE_OPTS get_sim_ublk_surfel_offset_table_opts() {
#if BUILD_5G_LATTICE
  cBOOLEAN write_db_press = g_mp_meas_seed_pres || g_mp_meas_write_pres;
#elif  BUILD_D19_LATTICE
  cBOOLEAN write_db_press = g_meas_seed_pres_d19 || g_meas_write_pres_d19;
#else
  constexpr cBOOLEAN write_db_press = FALSE;
#endif
  return DATA_OFFSET_TABLE_OPTS(sim,
                                g_is_multi_component,
                                write_db_press);
}

BOOLEAN is_conduction_active_in_mask(const ACTIVE_SOLVER_MASK mask) {
  return (mask & CONDUCTION_PDE_ACTIVE) != 0;
}

BOOLEAN is_any_flow_solver_active_in_mask(const ACTIVE_SOLVER_MASK mask) {
  return (mask & LB_ACTIVE) != 0 || (mask & T_PDE_ACTIVE) != 0 || (mask & KE_PDE_ACTIVE) != 0 || (mask & UDS_PDE_ACTIVE) != 0;
}

