/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include <algorithm>

#include "bsurfel_util.h"
#include "vr.h"
#include "bsurfel_ublk_neighbor_info.h"
#include "surfel.h"
#include "sampling_surfel.h"
#include "ublk_box_tree.h"
#include "ublk_tree.h"

namespace
{
using VALID_UBLKS = std::array< std::array< sINT16, 3 >, ubFLOAT::N_VOXELS>;
using VALID_VOXELS = std::array<VOXEL_MASK_8, ubFLOAT::N_VOXELS>;

constexpr VALID_UBLKS l_get_valid_ublks(uint8_t voxel)
{
  int8_t x = voxel & 4 ? 1 : -1;
  int8_t y = voxel & 2 ? 1 : -1;
  int8_t z = voxel & 1 ? 1 : -1;

  VALID_UBLKS valid_ublks = {{
      { 0, 0, 0 },
      { x, 0, 0 },
      { 0, y, 0 },
      { 0, 0, z },
      { x, y, 0 },
      { x, 0, z },
      { 0, y, z },
      { x, y, z }
    }
  };

  return valid_ublks;
}

// We have to pass -d to put in the coordinate system expected by axis_delta_to_voxel_mask()
// For example, if we are looking up at the neighbor ublk, d == 1. We are referring now to the
// BOTTOM of the ublk above us, which is represented in axis_delta_to_voxel_mask() by -1.
constexpr VOXEL_MASK_8 l_x_voxels(sINT16 d) { return d != 0 ? axis_delta_to_voxel_mask(0, -d) : ~VOXEL_MASK_8(0);}
constexpr VOXEL_MASK_8 l_y_voxels(sINT16 d) { return d != 0 ? axis_delta_to_voxel_mask(1, -d) : ~VOXEL_MASK_8(0);}
constexpr VOXEL_MASK_8 l_z_voxels(sINT16 d) { return d != 0 ? axis_delta_to_voxel_mask(2, -d) : ~VOXEL_MASK_8(0);}
constexpr VOXEL_MASK_8 l_get_valid_voxel_mask(const std::array< sINT16, 3 >& ublk_offset)
{
  return l_x_voxels(ublk_offset[0]) & l_y_voxels(ublk_offset[1]) & l_z_voxels(ublk_offset[2]);
}

constexpr VALID_VOXELS l_get_all_valid_voxel_mask(const VALID_UBLKS& valid_ublks)
{
  VALID_VOXELS valid_voxels = {{
      l_get_valid_voxel_mask(valid_ublks[0]),
      l_get_valid_voxel_mask(valid_ublks[1]),
      l_get_valid_voxel_mask(valid_ublks[2]),
      l_get_valid_voxel_mask(valid_ublks[3]),
      l_get_valid_voxel_mask(valid_ublks[4]),
      l_get_valid_voxel_mask(valid_ublks[5]),
      l_get_valid_voxel_mask(valid_ublks[6]),
      l_get_valid_voxel_mask(valid_ublks[7])
    }
  };
  return valid_voxels;
}

// This array is a total of 2*192 bytes
alignas(64) constexpr
std::array<VALID_UBLKS, ubFLOAT::N_VOXELS> l_all_valid_ublks = {{
    l_get_valid_ublks(0),
    l_get_valid_ublks(1),
    l_get_valid_ublks(2),
    l_get_valid_ublks(3),
    l_get_valid_ublks(4),
    l_get_valid_ublks(5),
    l_get_valid_ublks(6),
    l_get_valid_ublks(7)
  }
};

// This array is a total of 64 bytes, so aligning on a 64-byte address places the whole array in a cache line
alignas(64) constexpr
std::array<VALID_VOXELS, ubFLOAT::N_VOXELS> l_all_ublk_voxel_masks = {{
    l_get_all_valid_voxel_mask(l_all_valid_ublks[0]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[1]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[2]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[3]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[4]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[5]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[6]),
    l_get_all_valid_voxel_mask(l_all_valid_ublks[7])
  }
};

}; // end anon namespace

#if 0
// This is the new method that only allows split instances that interact with a given surfel to be in that surfels neighbor ublks list.
template <typename SURFEL_TYPE>
void sSURFEL_NEIGHBOR_UBLK_LOCATOR<SURFEL_TYPE>::add_neighbor_ublks_internal(UBLK ublk, int voxel) {
  
  ccDOTIMES(i,3) {
    ccDOTIMES(j, 3) {
      ccDOTIMES(k, 3) {
        sINT16 offset[3];
        offset[0] = i - 1;
        offset[1] = j - 1;
        offset[2] = k - 1;
        
        if(offset[0]  == 0 && offset[1] == 0 && offset[2] ==0)
          continue;
        
        try{
          int voxel_size = sim_scale_to_voxel_size(ublk->scale());
          STP_DGEOM_VARIABLE centroid[3]; //compute the centroid of the voxel
          vcopy(centroid, ublk->location);
          centroid[0] += voxel_size * (0.5 + num_to_voxel_x(voxel));
          centroid[1] += voxel_size * (0.5 + num_to_voxel_y(voxel));
          centroid[2] += voxel_size * (0.5 + num_to_voxel_z(voxel));
          
          STP_DGEOM_VARIABLE neighbor_centroid[3];
          vcopy(neighbor_centroid, centroid);
          vmac(neighbor_centroid, voxel_size, offset);
          
          sHOME_UBLK_LOCATOR finder(ublk, voxel, neighbor_centroid, false, centroid);
          finder.locate();
          
          UBLK neighbor_ublk = finder.ublk();
#if 0
          if(m_surfel->id() == FILM_SOLVER_SURFEL_OF_INTEREST) {
            msg_print("found ublk %d voxel %d at %d %d %d in offset %d %d %d from ublk %d voxel %d.",
                      neighbor_ublk->id(),
                      finder.voxel(),
                      neighbor_ublk->location(0),
                      neighbor_ublk->location(1),
                      neighbor_ublk->location(2),
                      offset[0],
                      offset[1],
                      offset[2],
                      ublk->id(),
                      voxel);
          }
#endif
          
          add_neighbor_ublk(neighbor_ublk, 0x1 << finder.voxel());
        } catch (sHOME_UBLK_LOCATOR::SearchError& error_message) {
      
#if 0
          if(m_surfel->id() == FILM_SOLVER_SURFEL_OF_INTEREST) {
            msg_print("didnt find a ublk at offset %d %d %d from ublk %d voxel %d.",
                      offset[0],
                      offset[1],
                      offset[2],
                      ublk->id(),
                      voxel);
          }
#endif
        }
      }
    }
  }
}


template <typename SURFEL_TYPE>
void sSURFEL_NEIGHBOR_UBLK_LOCATOR<SURFEL_TYPE>::add_neighboring_ublks() {
  if(m_surfel->is_lrf() || m_surfel->is_sampling_surfel()) {
    
    sUBLK * home_ublk = m_home_ublk;
    uINT8 home_voxel = m_home_voxel;
    const auto& box_access = home_ublk->m_box_access;
    sINT16 offset[3];
    
    const auto& valid_ublks = l_all_valid_ublks[home_voxel];
    const auto& valid_ublks_voxel_masks = l_all_ublk_voxel_masks[home_voxel];
    for(int u=0; u < valid_ublks.size(); u++) {
      const auto& offset = valid_ublks[u];
      const auto& voxel_mask = valid_ublks_voxel_masks[u];
      
      //If this is a sliding mesh or sample surfel, include all split instances.
      TAGGED_UBLK t_ublk = box_access.neighbor_ublk(offset.data(), true);
      add_neighbor_ublks_from_tagged_ublk(t_ublk, voxel_mask);
    }
  } else {
    add_neighbor_ublks_internal(m_home_ublk, m_home_voxel);
    if(m_home_ublk->is_vr_coarse()) {
      UBLK vr_fine_ublk = nullptr;
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data =
        (sVR_COARSE_INTERFACE_DATA *) m_home_ublk->vr_data();
      vr_fine_ublk = vr_coarse_data->vr_fine_ublk(m_home_voxel);
      if(vr_fine_ublk) {
        ccDOTIMES(voxel, 8) {
          if(vr_fine_ublk->fluid_like_voxel_mask >> voxel & 0x1)
            add_neighbor_ublks_internal(vr_fine_ublk, voxel);
        }
      }
    }
  }
#if 0
  if(m_surfel->id() == FILM_SOLVER_SURFEL_OF_INTEREST) {
    ccDOTIMES(i, m_surfel->p_data()->neighbor_ublks.size()) {
      int home_vec[3];
      vsub(home_vec, m_surfel->p_data()->neighbor_ublks[i]->location, m_home_ublk->location);
      msg_print("new method: ublk %d scale %d with mask %d at %d %d %d in surfel %d's (scale %d) neighbor ublk vector. offset %d %d %d",
                m_surfel->p_data()->neighbor_ublks[i]->id(),
                m_surfel->p_data()->neighbor_ublks[i]->scale(),
                (int)m_surfel->p_data()->neighbor_voxel_masks[i],
                m_surfel->p_data()->neighbor_ublks[i]->location[0],
                m_surfel->p_data()->neighbor_ublks[i]->location[1],
                m_surfel->p_data()->neighbor_ublks[i]->location[2],
                m_surfel->id(),
                m_surfel->scale(),
                home_vec[0],
                home_vec[1],
                home_vec[2]);
    }
  }
#endif
}



#else
//This is the old method that may allow split voxels that don't interact with a given surfel to appear in that surfel's neighbor ublks vector. 
template <typename SURFEL_TYPE>
void sSURFEL_NEIGHBOR_UBLK_LOCATOR<SURFEL_TYPE>::add_neighboring_ublks() {
    sUBLK * home_ublk = m_home_ublk;
    uINT8 home_voxel = m_home_voxel;
    const auto& box_access = home_ublk->m_box_access;
    sINT16 offset[3];
    
    const auto& valid_ublks = l_all_valid_ublks[home_voxel];
    const auto& valid_ublks_voxel_masks = l_all_ublk_voxel_masks[home_voxel];
    for(int u=0; u < valid_ublks.size(); u++) {
      const auto& offset = valid_ublks[u];
      const auto& voxel_mask = valid_ublks_voxel_masks[u];

#if 0
      //Unless this is a sampling surfel or lrf surfel that should
      //interact with all instances, skip the 0,0,0 offset neighbors
      //since we'e already added the correct instance of the surfel's
      //home ublk
      if(!m_surfel->is_lrf() && !m_surfel->is_sampling_surfel()) 
        if( offset[0] == 0 && offset[1] == 0 && offset[2] == 0 )
          continue;
#endif
 
      TAGGED_UBLK t_ublk = box_access.neighbor_ublk(offset.data(), true);
      add_neighbor_ublks_from_tagged_ublk(t_ublk, voxel_mask);
    }
#if 0
    if(m_surfel->id() == FILM_SOLVER_SURFEL_OF_INTEREST) {
      ccDOTIMES(i, m_surfel->p_data()->neighbor_ublks.size()) {
        int home_vec[3];
        vsub(home_vec, m_surfel->p_data()->neighbor_ublks[i]->location, m_home_ublk->location);
        msg_print("old method: ublk %d scale %d with mask %d at %d %d %d in surfel %d's (scale %d) neighbor ublk vector. offset %d %d %d",
                  m_surfel->p_data()->neighbor_ublks[i]->id(),
                  m_surfel->p_data()->neighbor_ublks[i]->scale(),
                  (int)m_surfel->p_data()->neighbor_voxel_masks[i],
                  m_surfel->p_data()->neighbor_ublks[i]->location[0],
                  m_surfel->p_data()->neighbor_ublks[i]->location[1],
                  m_surfel->p_data()->neighbor_ublks[i]->location[2],
                  m_surfel->id(),
                  m_surfel->scale(),
                  home_vec[0],
                  home_vec[1],
                  home_vec[2]);
      }
    }
#endif
}
#endif

template void sSURFEL_NEIGHBOR_UBLK_LOCATOR<sSAMPLING_SURFEL*>::add_neighboring_ublks();
template void sSURFEL_NEIGHBOR_UBLK_LOCATOR<sSURFEL*>::add_neighboring_ublks();

//------------------------------------------
// sBSURFEL_UBLK_NEIGHBOR_INFO
//------------------------------------------

std::ostream & operator << (std::ostream& os, sBSURFEL_UBLK_NEIGHBOR_INFO& b_info)
{
  os << "Ublk: " << b_info.ublk()->id() << " Voxel: " << b_info.voxel() << " IW: " << b_info.interpolation_weight() << " DW: " << b_info.distribution_weight() << std::endl;
  return os;
}

//------------------------------------------
// HOME_UBLK_LOCATOR
//------------------------------------------

/** The entry point to the home ublk/voxel location algorithm.  Checks to see
 * if we are still inside the old home ublk.  If we are, then we can short-circuit
 * the generic algorithm.
 *
 * # Generic Algorithm
 *
 * Find the offset of the location, then pull the tagged neighbor ublk from the
 * box.  Each tagged ublk type requires different treatment. At the end, the
 * new home ublk will have been found, at which point the new home voxel can be
 * computed.
 *
 * ## Null
 * A null ublk is bad. That should never happen, throw an error if it does.
 *
 * ## Scale Interface Motion points will never live in a scale interface ublk.
 * If the point has moved into a scale interface, we back up. The previous home
 * ublk should have been a vr_coarse ublk, so we move the point to the vr_fine
 * scale, and use that as our starting point.  This has to be done because the
 * fpolygons that parcels use do not live in scale interface, but they do in vr
 * fine ublks.  The home voxel also needs to be recomputed in order for the
 * parcel split search algorithm to work properly. There is no extra cost to
 * bsurfels for doing it this way, because they would have to go through a similar
 * scale changing process anyway. Because of this, scale interface must be
 * checked first before anything else.
 *
 * ## Split
 * Bsurfels and parcels treat splits differently. Parcels *have to know* exactly
 * which split they ended up in. Bsurfels are more forgiving, due to the nature
 * of the immersed boundary method, resulting in a simpler implementation.
 */

sHOME_UBLK_LOCATOR::sHOME_UBLK_LOCATOR(UBLK ublk, sINT8 home_voxel, STP_DGEOM_VARIABLE centroid[3],
                                       bool for_bsurfels, STP_DGEOM_VARIABLE p_location[3])
{

  m_error_detected = FALSE;

  ccDOTIMES(axis, 3) {
    m_location[axis] = centroid[axis];
  }
  m_home_ublk = ublk;
  m_home_voxel = home_voxel;
  m_for_bsurfels = for_bsurfels;
  m_parcel_location_modified = false;
  if (p_location) {
    m_p_location[0] = p_location[0];
    m_p_location[1] = p_location[1];
    m_p_location[2] = p_location[2];

    //Check that the offset is not too far and that the previous location is in the specified home ublk and voxel
    dFLOAT ublk_offset[3] = {0, 0, 0};
    bool new_is_inside = calc_centroid_offset_from_ublk(m_home_ublk, centroid, ublk_offset);

    if(!for_bsurfels) {
      if(std::fabs(ublk_offset[0]) > 3 ||
         std::fabs(ublk_offset[1]) > 3 ||
         std::fabs(ublk_offset[2]) > 3) {
        auto error_message = SearchError("ublk offset ");
        error_message << ublk_offset[0] << " ";
        error_message << ublk_offset[1] << " ";
        error_message << ublk_offset[2];
        error_message << " is too long";
        throw error_message;
      }
    }

    bool previous_is_inside = calc_centroid_offset_from_ublk(m_home_ublk, m_p_location, ublk_offset);
    if(!new_is_inside && !previous_is_inside) {
      auto error_message = SearchError("previous location ");
      error_message << m_p_location[0] << " ";
      error_message << m_p_location[1] << " ";
      error_message << m_p_location[2];
      error_message << " is inconsistent with old home ublk";
      throw error_message;
    }
  }
}

static std::vector<UBLK>& get_ublks_with_voxel_from_split(TAGGED_UBLK * t_ublks, 
                                                          int n_split_ublks, 
                                                          sINT8 voxel) 
{
  static std::vector<UBLK> possible_ublks;
  possible_ublks.clear();
  for (int i = 0; i < n_split_ublks; i++) {
    UBLK ublk = t_ublks[i].ublk();
    if (ublk->fluid_like_voxel_mask.test(voxel)) {
      possible_ublks.push_back(ublk);
    }
  }

  return possible_ublks;
}

static UBLK get_fine_ublk_from_split_coarse_ublk(TAGGED_UBLK * t_ublks, 
                                                 int n_split_ublks, 
                                                 sINT8 voxel) 
{
  for (int i = 0; i < n_split_ublks; i++) {
    UBLK ublk = t_ublks[i].ublk();
    sVR_COARSE_INTERFACE_DATA * vr_coarse_data = ublk->vr_coarse_data();
    UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(voxel).ublk();
    if (fine_ublk) {
      return fine_ublk;
    }
  }

  return nullptr;
}

bool sHOME_UBLK_LOCATOR::bsurfel_is_inside_ublk_box(sUBLK_BOX_HEADER_BASE* ublk_box_header, const dFLOAT loc[3])
{
  bool inside = true;
  dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk_box_header->m_scale);
  dFLOAT ivdx = 1.0 / voxel_dx;
  dFLOAT ublk_distance = 2.0 * voxel_dx;
  ccDOTIMES(axis, 3) {
    dFLOAT dist_box_to_bsurfel = loc[axis] - ublk_box_header->m_location[axis] ;
    if ( (dist_box_to_bsurfel < 0) || ( dist_box_to_bsurfel > ublk_distance * ublk_box_header->m_size[axis] )  ) {
      inside = false; 
      break;
    }
  }
  return inside;
}

bool sHOME_UBLK_LOCATOR::bsurfel_is_inside_ublk_in_box(sUBLK_BOX_HEADER_BASE* ublk_box_header, const dFLOAT loc[3])
{
  dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk_box_header->m_scale);
  dFLOAT ivdx = 1.0 / voxel_dx;
  //have a nested loop here, dont like this
  //can loop over the m_n_ublks which will walk over succesively saved ublk info so thats good
  //but will need to see how to convert linear index to ublk_location
  uINT16 i,j,k;
  for (k = 0; k < ublk_box_header->m_size[2]; k++) {
    for (j = 0; j < ublk_box_header->m_size[1]; j++) {
      for (i = 0; i < ublk_box_header->m_size[0]; i++) {
        bool inside = true;
        uINT16 indices[3] = {i,j,k};
        int linear_index = ublk_box_header->get_linear_index(indices);
        uINT8* is_ublk_owned = (uINT8*) ublk_box_header ;
        is_ublk_owned += ublk_box_header->offset_dynData + linear_index;
        if (*is_ublk_owned) {
          STP_COORD ublk_location[3];
          ublk_box_header->get_ublk_location(indices, ublk_location);
          ccDOTIMES(axis, 3) {
            dFLOAT ublk_centroid = ublk_location[axis] + voxel_dx;
            dFLOAT ublk_offset = (loc[axis] - ublk_centroid) * ivdx;
            if (std::fabs(ublk_offset) > 1) {
              inside = false;
              break;
            }
          }
          if (inside)
            return true;
        }
      }
    }
  }
  return false;
}


extern std::vector<sUBLK_BOX_HEADER> g_ublk_box_header;

extern sBOX_TREE g_box_tree;
extern sUBLK_TREE g_ublk_tree;

STP_PROC sHOME_UBLK_LOCATOR::locate_home_box_sp_for_void_bsurfel()
{
  ////now search a ublk box where the bsurfel is within the size in all dir. 

  sBOX_TREE_NODE tree_node = *g_box_tree.find_internal(m_location, &g_box_tree.m_nodes.at(0));

  asINT32 n_boxes = tree_node.m_ublk_boxes.size();
  BOOLEAN found_inside = false;
  BOOLEAN found_inside_box = false;
  asINT32 i;
  for (i = 0; i < n_boxes; i++) {
    sUBLK_BOX_HEADER_BASE* ublk_box_header = tree_node.m_ublk_boxes.at(i);
    dFLOAT ublk_offset[3];
    found_inside_box = bsurfel_is_inside_ublk_box(ublk_box_header, m_location); 
    if (found_inside_box) {
      found_inside = bsurfel_is_inside_ublk_in_box(ublk_box_header, m_location); 
    }
    if (found_inside) 
      break;
    
  }
  //end loop over ublk_boxes  

  if (found_inside)
    return tree_node.m_ublk_boxes.at(i)->proc_id;
  else
    return -1;

}

TAGGED_UBLK sHOME_UBLK_LOCATOR::check_t_ublk_for_scale_interface(TAGGED_UBLK t_ublk) 
{

  if ( t_ublk.is_ublk_scale_interface()) {
    bool first_fine_ublk_found = false;
    int first_fine_ublk;
    ccDOTIMES(fine_blk, 8) {
      if ( !t_ublk.interface()->m_fine_ublks[fine_blk].is_null() ) {
        first_fine_ublk_found = true;
        first_fine_ublk = fine_blk;
        break;
      }
    }

    if (!first_fine_ublk_found)
      msg_internal_error("No fine ublk found for a scale interface while trying to locate bsurfel!");
      
    UBLK new_home_ublk = t_ublk.interface()->m_fine_ublks[first_fine_ublk].ublk();
    sBOX_ACCESS* box_access = &new_home_ublk->m_box_access;
    sINT64 trunc_offset[3] = {0};
    dFLOAT ublk_offset[3] = {0};
    bool inside_vrf = calc_centroid_offset_from_ublk(new_home_ublk, m_location, ublk_offset);
    if (inside_vrf) {
      t_ublk = t_ublk.interface()->m_fine_ublks[first_fine_ublk];
    } else {
      truncate_ublk_offset(ublk_offset, trunc_offset);
      t_ublk = box_access->neighbor_ublk(trunc_offset, true);
    }
  } 


  return t_ublk;
}

TAGGED_UBLK sHOME_UBLK_LOCATOR::locate_home_ublk_in_same_sp()
{
  ////now search a ublk where the offset is within 1 in all dir. 
  
  //return g_ublk_tree.find_ublk(m_location);
    
  UBLK new_ublk = g_ublk_tree.find_ublk(m_location);

  if (new_ublk == NULL) {
    msg_internal_error("No suitable ublk found in this sub domain while locating home ublk for bsurfel!");
    return nullptr;
  }

  sBOX_ACCESS* box_access = &new_ublk->m_box_access;
  TAGGED_UBLK t_ublk = *box_access->tagged_ublk();
  
  t_ublk = check_t_ublk_for_scale_interface(t_ublk);
  return t_ublk;

}

void sHOME_UBLK_LOCATOR::locate_unpacked_bsurfel()
{
  TAGGED_UBLK t_ublk_here = locate_home_ublk_in_same_sp();
  if (!t_ublk_here.is_null()) {
    if (t_ublk_here.is_ublk_split()) {
      //put the first split ublk as the home,
      //it will be moved later in the locate() call if it needs to
      m_home_ublk = t_ublk_here.split_ublks()->tagged_ublks()->ublk();
    } else {
      m_home_ublk = t_ublk_here.ublk();
    }
  }

  m_home_voxel = 0;

  STP_PROC new_home_sp = locate();

}

STP_PROC sHOME_UBLK_LOCATOR::locate()
{
  // determine where the centroid is located with respect to the home ublk
  sINT64 trunc_offset[3] = {0};
  dFLOAT ublk_offset[3] = {0};
  STP_PROC new_home_sp = my_proc_id;

  try {
    bool inside = calc_centroid_offset_from_ublk(m_home_ublk, m_location, ublk_offset);
    if (inside) {
      uINT8 voxel = locate_voxel(ublk_offset);
      if ((m_home_voxel != voxel) && m_home_ublk->is_split()) {
        auto& box_access = m_home_ublk->m_box_access;
        TAGGED_UBLK * t_ublk = box_access.tagged_ublk();
        if (!t_ublk->is_ublk_split())
          throw SearchError("ublk from same box as a split ublk is not also flagged as split");
        //cassert(t_ublk->is_ublk_split());
        std::tie(m_home_ublk, m_home_voxel) = locate_new_home_ublk_in_split_ublk(t_ublk->split_ublks(), voxel);
        if (m_home_ublk == nullptr)
          throw SearchError("Could not find ublk from a split location");
      }
      else {
        // at this point, either home_ublk is unsplit or the parcel/bsurfel is
        // in the same voxel.
        // collision detection should have transferred the parcel to a surfel
        // if it moved to a different voxel instance.
        // We already choose the ublk with a minimum ID for bsurfels so there is
        // no better ublk instance possible.
        m_home_voxel = voxel;
      }
    }
    else { // not inside
      truncate_ublk_offset(ublk_offset, trunc_offset);
      sBOX_ACCESS* box_access = &m_home_ublk->m_box_access;
      TAGGED_UBLK t_ublk = box_access->neighbor_ublk(trunc_offset, true);

      //if the bsurfel is outside the home ublks box, it will return a null pointer
      //we need to check if the bsurfel is inside void space (search through all ublks, if
      //not found then its in void space) or has entered another box (find the new home ublk)
      if (t_ublk.is_null() && m_for_bsurfels) {
        new_home_sp = locate_home_box_sp_for_void_bsurfel();
        if (new_home_sp == my_proc_id) {
          t_ublk = locate_home_ublk_in_same_sp();
        }
      }

      //Bsurfels can traverse more than one voxel in certain situations, in which case the
      //current m_home_ublk cannot be trusted in the later part of the algorithm when the
      //t_ublk is scale interface
      //The logic relies on the assumption that a bsurfel moves max one voxel in a TS
      //This can happen in two situations:
      //1. On resume: now that the home ublk locator responsibility is moved to the SP, 
      //the CP does not use the octree to find the new home ublk after rotating bsurfels
      //now we initialize the home ublks with what is provided in the LGI file, then locate
      //the correct home ublk here
      //2. On emergence from void space in a scale interface block
      //The check_t_tublk function will return a tagged fine ublk 
      //This should not affect the computation of bsurfels and particles in any other situation
      if ( m_for_bsurfels ) {
        if (abs(trunc_offset[0]) > 1 || 
            abs(trunc_offset[1]) > 1 ||
            abs(trunc_offset[2]) > 1 ) {
          t_ublk = check_t_ublk_for_scale_interface(t_ublk);
        }
      }
      
      if (t_ublk.is_ublk_scale_interface()) {
        if (m_home_ublk->is_vr_coarse()) {
          if (m_home_ublk->is_split()) { 
            // if split, make sure we have a valid coarse ublk
            // if none of them are fluid, then we just have to take our chances I guess
            sUBLK_VECTOR * splits = box_access->tagged_ublk()->split_ublks();
            m_home_ublk = get_fine_ublk_from_split_coarse_ublk(splits->tagged_ublks(),
                                                               splits->num_split_ublks(),
                                                               m_home_voxel);
          }
          else {
            sVR_COARSE_INTERFACE_DATA * vr_coarse_data = m_home_ublk->vr_coarse_data();
            m_home_ublk = vr_coarse_data->vr_fine_ublk(m_home_voxel).ublk();
          }

          if (m_home_ublk == NULL)
            throw SearchError("vr coarse voxel had a null reference to its fine ublk.");
          //cassert(m_home_ublk);

          if (!m_home_ublk->is_vr_fine())
            throw SearchError("vr coarse voxel had a reference to a fine ublk that isn't flaged as such.");
          //cassert(m_home_ublk->is_vr_fine());

          // recompute m_home_voxel
          if (!m_for_bsurfels) {
            dFLOAT vr_fine_ublk_offset[3] = {0};
            bool inside_vrf = calc_centroid_offset_from_ublk(m_home_ublk, m_p_location, vr_fine_ublk_offset);
            if (!inside_vrf)
              throw SearchError("location is not within expected vrfine voxel.");
            //cassert(inside_vrf);
            m_home_voxel = locate_voxel(vr_fine_ublk_offset);
          }

          // get vr_fine scale neighbor
          bool inside_vrf = calc_centroid_offset_from_ublk(m_home_ublk, m_location, ublk_offset);
          if (inside_vrf)
            throw SearchError("location is inside vrfine but it shouldn't be.");
          //cassert(!inside_vrf);
          truncate_ublk_offset(ublk_offset, trunc_offset);
          box_access = &m_home_ublk->m_box_access;
          t_ublk = box_access->neighbor_ublk(trunc_offset, true);

        }
        else {
          // test if the offset is going tri-diagonal direction
          if (trunc_offset[0] != 0 && trunc_offset[1] != 0 && trunc_offset[2] != 0) {
            //std::tie(m_home_ublk, m_home_voxel) = locate_new_home_ublk_in_tri_diagnoal_vr();
            STP_DGEOM_VARIABLE flip_trunc_offset[3];
            ccDOTIMES(axis, N_AXES) {
              flip_trunc_offset[axis] = - trunc_offset[axis];
            }
            uINT8 fine_ublk_index = locate_voxel(flip_trunc_offset);
            t_ublk = t_ublk.interface()->m_fine_ublks[fine_ublk_index];

          }
          else {
            throw SearchError("tagged ublk is a scale interface but home ublk is not a vr_coarse, and offset is not tri-diagnoal");
          }
        }
      }
      if (t_ublk.is_null()) {
        if (m_for_bsurfels) {
          m_home_ublk = nullptr;
          m_home_voxel = 0;
        } else {
          throw SearchError("tagged ublk is null");
        }
      }
      if (t_ublk.is_ublk_scale_interface()) {
        throw SearchError("tagged ublk is a scale interface");
      }
      else if (t_ublk.is_ublk_split()) {
        std::tie(m_home_ublk, m_home_voxel) = locate_new_home_ublk_in_split_ublk(t_ublk.split_ublks(), -1);
        if (m_home_ublk == nullptr)
          throw SearchError("Could not find ublk from a split location");
      }
      else {
        if (!t_ublk.is_null()) 
          std::tie(m_home_ublk, m_home_voxel) = locate_new_home_ublk_from_ublk(t_ublk.ublk());
      }
    }
  }
  catch (SearchError &msg) {
    if (m_for_bsurfels) {
      if (m_home_ublk) {
      msg << "\nOld Home Ublk ID: " << m_home_ublk->id() << "\n"
          << " VR Scale: " << m_home_ublk->scale() << "\n";
      msg << "\tLocation: (" << m_home_ublk->location(0) << ", "
          << m_home_ublk->location(1) << ", "
          << m_home_ublk->location(2) << ")\n";
      }
      else {
        msg << "Home ublk not found!" << "\n";
      }
      msg << "\tCentroid: (" << m_location[0] << ", "
          << m_location[1] << ", "
          << m_location[2] << ")\n";
      msg << "\tOffset  : (" << trunc_offset[0] << ", "
          << trunc_offset[1] << ", "
          << trunc_offset[2] << ")\n";
    }
    throw msg;
  }
  return new_home_sp;
}

/** This function modifies the m_location and m_p_location once a parcel cross the periodic boundary
 *  This function itself allows the parcel to cross the boundary through a face, an edge or a corner.
 *  However, because the split ublk/voxel algorithem, this function should always only cross through
 *  face when called from locate_new_home_ublk_from_multiple_split_ublks(),
 *  ie, only one axis of parcel_cross_periodic_boundary can be none zero.
 *  Function overloading, the following is for face not known.
 */

void sHOME_UBLK_LOCATOR::process_periodic_boundaries(UBLK ublk)
{

  if (m_for_bsurfels) return;

  typedef enum {
    CROSSED_THROUGH_NEGATIVE_FACE = -1,
    NO_CROSS = 0,
    CROSSED_THROUGH_POSITIVE_FACE = 1
  } ePOSSIBLE_SCENARIOS;

  if (ublk->does_ublk_abut_simvol()) {
    dFLOAT ublk_size = sim_scale_to_ublk_size(ublk->scale());
    ePOSSIBLE_SCENARIOS parcel_crossed_a_periodic_boundary[3] = {NO_CROSS, NO_CROSS, NO_CROSS};

    asINT32 disp[N_AXES];
    ccDOTIMES(axis, N_AXES) {
      disp[axis] = (lattice_location_from_position(m_location[axis], ublk_size) - lattice_location_from_position(m_p_location[axis], ublk_size));
    }

    ccDOTIMES(axis, N_AXES) {
      if (disp[axis] > 0 && ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(axis))) {
        parcel_crossed_a_periodic_boundary[axis] = CROSSED_THROUGH_POSITIVE_FACE;
        m_parcel_location_modified = true;
      }
      else if (disp[axis] < 0 && ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(axis))) {
        parcel_crossed_a_periodic_boundary[axis] = CROSSED_THROUGH_NEGATIVE_FACE;
        m_parcel_location_modified = true;
      }
    }

    ccDOTIMES(axis, N_AXES) {
      m_location[axis] -= parcel_crossed_a_periodic_boundary[axis] * sim.simvol_size[axis];
      m_p_location[axis] -= parcel_crossed_a_periodic_boundary[axis] * sim.simvol_size[axis];
    }
  }
}

/*  Function overloading, the following is for face already known. */
void sHOME_UBLK_LOCATOR::process_periodic_boundaries(UBLK ublk, int crossed_face)
{
  if (m_for_bsurfels) return;
  if (m_for_bsurfels) return;

  typedef enum {
    CROSSED_THROUGH_NEGATIVE_FACE = -1,
    NO_CROSS,
    CROSSED_THROUGH_POSITIVE_FACE
  } ePOSSIBLE_SCENARIOS;


  if (ublk->does_ublk_abut_simvol()) {
    ePOSSIBLE_SCENARIOS parcel_crossed_a_periodic_boundary[3] = {NO_CROSS, NO_CROSS, NO_CROSS};

    if (crossed_face < 0 || crossed_face >= 6)
      throw SearchError("Invalid index for which ublk face was crossed.");

    if (ublk->does_ublk_abut_simvol(crossed_face)) {
      if (stp_is_neg_face(crossed_face)) {
        parcel_crossed_a_periodic_boundary[stp_face_to_axis(crossed_face)] = CROSSED_THROUGH_NEGATIVE_FACE;
        m_parcel_location_modified = true;
      }
      else {
        parcel_crossed_a_periodic_boundary[stp_face_to_axis(crossed_face)] = CROSSED_THROUGH_POSITIVE_FACE;
        m_parcel_location_modified = true;
      }
    }

    ccDOTIMES(axis, N_AXES) {
      m_location[axis]   -= parcel_crossed_a_periodic_boundary[axis] * sim.simvol_size[axis];
      m_p_location[axis] -= parcel_crossed_a_periodic_boundary[axis] * sim.simvol_size[axis];
    }
  }
}

/** Locates the voxel (well, really the octant) that the offset indicates */
uINT8 sHOME_UBLK_LOCATOR::locate_voxel(const STP_DGEOM_VARIABLE ublk_offset[3])
{
  uINT8 voxel = 0;
  if (ublk_offset[0] >= 0) voxel |= 4;   // X
  if (ublk_offset[1] >= 0) voxel |= 2;   // Y
  if (ublk_offset[2] >= 0) voxel |= 1;   // Z
  return voxel;
}

/** Locates the voxel and checks that the ublk_offset is actually inside the ublk*/
uINT8 sHOME_UBLK_LOCATOR::locate_voxel_and_check_inside(UBLK ublk)
{
  dFLOAT ublk_offset[3] = {0};
  bool inside = calc_centroid_offset_from_ublk(ublk, m_location, ublk_offset);
  if (!inside) {
      if (m_for_bsurfels) {
        auto s = SearchError("point is outside of ublk\n");
        s << fmt::format("sHOME_UBLK_LOCATOR location {} {} {}\n"
                         "ublk_offset {} {} {}\n"
                         "ublk id {}\n",m_location[0],m_location[1],m_location[2],
                         ublk_offset[0], ublk_offset[1], ublk_offset[2],
                     ublk->id());
        throw s;
      } else {
        auto s = SearchError("point is outside of ublk");
        s << fmt::format(" (offset {} {} {})",
                         ublk_offset[0], 
                         ublk_offset[1], 
                         ublk_offset[2]);
        throw s;
      }
  }
  return locate_voxel(ublk_offset);
}

/** Chops the decimal value of ublk_offset to just an integer,
 *  in preparation for asking the ublk box for a neighbor
 */
INLINE
void sHOME_UBLK_LOCATOR::truncate_ublk_offset(const dFLOAT ublk_offset[3], sINT64 trunc_offset[3])
{
  ccDOTIMES(axis, 3) {
    trunc_offset[axis] = round(ublk_offset[axis]/2);
  }
}

/** Locate the new home ublk, doing any necessary movements between scales
 *
 * @pre point is inside the ublk
 *
 * Points never live in a vr_fine ublk, they only pass through them, so here
 * the home ublk is promoted to the vr_coarse_ublk().
 *
 */
std::pair<UBLK, uINT8> sHOME_UBLK_LOCATOR::locate_new_home_ublk_from_ublk(UBLK ublk)
{
  // bsurfels never live in a vr_fine region, so move to the coarse parent ublk
  if (!ublk->is_mirror() && ublk->is_vr_fine()) {
    sVR_FINE_INTERFACE_DATA * vr_data = ublk->vr_fine_data();
    if (vr_data == NULL)
      throw SearchError("VR fine ublk doesnt have a null reference to its vr data.");
    ublk = vr_data->vr_coarse_ublk().ublk();
    if (ublk == NULL)
      throw SearchError("VR fine ublk has vr_data with a null vr_coarse_ublk reference.");
  }
  process_periodic_boundaries(m_home_ublk);
  asINT32 voxel;
  try {
    voxel = locate_voxel_and_check_inside(ublk);
  }
  catch(SearchError& msg) {
    if (m_for_bsurfels) {
      msg << " when finding simple ublk " << ublk->id() << '\n'
          << "\tVR Scale: " << ublk->scale() << "\n";
      msg << "\tLocation: (" << ublk->location(0) << ", "
          << ublk->location(1) << ", "
          << ublk->location(2) << ")";
    } else { 
      //Don't use too many characters or any newlines in the error
      //message if it may be used as a note in a simerror when being
      //used for particle modeling.
      msg << " when finding simple ublk " << ublk->id();
      msg << " at (" << ublk->location(0) << ", "  << ublk->location(1) << ", "  << ublk->location(2) << ")";
    }
    throw msg;
  }
  return std::make_pair(ublk, voxel);
}

/** Locates a split ublk across an edge/corner jump */
UBLK sHOME_UBLK_LOCATOR::locate_new_home_ublk_from_multiple_split_ublks(sINT32 n_hops)
{
  UBLK curr_ublk = m_home_ublk;
  uINT8 curr_voxel = m_home_voxel;
  sSPLIT_NEIGHBOR_INFO *split_info = curr_ublk->split_neighbor_info();
  if (!split_info) {
    SearchError msg("ublk does not have split_neighbor_info");
    msg << curr_ublk->id();
    throw msg;
  }
  bool skip_axis[3] = {false, false, false}; // a face can only be crossed in one hop
  ccDOTIMES(hop, n_hops) {
    dFLOAT voxel_dx = sim_scale_to_voxel_size(curr_ublk->scale());
    ccDOTIMES(axis, N_AXES) {
      if (skip_axis[axis])
        continue;

      dFLOAT intersecting_pt[3];
      int crossed_face = -1;
      if (lattice_location_from_position(m_location[axis], voxel_dx) > lattice_location_from_position(m_p_location[axis], voxel_dx)) {
        crossed_face = stp_axis_to_pos_face(axis);
      }
      else if (lattice_location_from_position(m_location[axis], voxel_dx) < lattice_location_from_position(m_p_location[axis], voxel_dx)) {
        crossed_face = stp_axis_to_neg_face(axis);
      }
      if (crossed_face > -1) {
        try {
        compute_intersection_on_face(crossed_face, m_p_location, m_location, voxel_dx, intersecting_pt);
        }
        catch(SearchError& msg) {
          msg << " when entering a split voxel";
          throw msg;
        }
        int a1 = -1, a2 = -1;
        if (axis == 0) {
          a1 = 1; a2 = 2;
        }
        else if (axis == 1) {
          a1 = 0; a2 = 2;
        }
        else if (axis == 2) {
          a1 = 0; a2 = 1;
        }
        if ((skip_axis[a1] || (lattice_location_from_position(m_p_location[a1], voxel_dx) == lattice_location_from_position(intersecting_pt[a1], voxel_dx))) &&
            (skip_axis[a2] || (lattice_location_from_position(m_p_location[a2], voxel_dx) == lattice_location_from_position(intersecting_pt[a2], voxel_dx)))) {
          process_periodic_boundaries(curr_ublk, crossed_face);
          curr_ublk = split_info->find_split_ublk_across_face(intersecting_pt, curr_ublk, curr_voxel, crossed_face);
          curr_voxel = neighbor_voxel_along_axis(curr_voxel, axis);
          if (curr_ublk == nullptr || !curr_ublk->split_neighbor_info()) {
            SearchError msg("ublk is null or not near surface, but neighbor of split ublks"); 
            if (curr_ublk)
              msg << curr_ublk->id();
            throw msg;
          }
          if (curr_ublk->is_mirror()){
            // hoped into a mirror ublk, give up.
            break;
          }
          if (curr_ublk->is_vr_coarse()) {
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data = curr_ublk->vr_coarse_data();
            if (vr_coarse_data->vr_fine_ublk(curr_voxel))
              curr_ublk = vr_coarse_data->vr_fine_ublk(curr_voxel).ublk();
          }
          split_info = curr_ublk->split_neighbor_info();
          skip_axis[axis] = true;
          break;
        }
      }
    }
  }
  return curr_ublk;
}

/** Returns the home ublk for parcels among the split instances present at a location
 *
 * @pre the point is inside this split ublk
 */
UBLK sHOME_UBLK_LOCATOR::locate_new_home_ublk_in_split_ublk_for_parcels(sUBLK_VECTOR * split_ublk_vector)
{

  int n_split_ublks = split_ublk_vector->num_split_ublks();
  TAGGED_UBLK * t_ublks = split_ublk_vector->tagged_ublks();

  sSPLIT_NEIGHBOR_INFO *split_neighbor_info = m_home_ublk->split_neighbor_info();

  if (split_neighbor_info == nullptr) {
    SearchError msg("split neighbor info should have been allocated for ublk");
    msg << m_home_ublk->id();
    throw msg;
  }

  STP_LATVEC_INDEX latvec = latvec_from_displacement();
  if (split_neighbor_info->is_ublk_instance_single(m_home_voxel, latvec)) {
    uINT8 instance_index = split_neighbor_info->ublk_instance(m_home_voxel, latvec);
    if (instance_index >= n_split_ublks)
      throw SearchError("instance index exceeds range");
    process_periodic_boundaries(m_home_ublk);
    return t_ublks[instance_index].ublk();
  }
  else if (split_neighbor_info->are_ublk_instances_multiple(m_home_voxel, latvec)) {
    // follow the displacement vector
    if (split_neighbor_info->is_face_neighbor(latvec)) {

      process_periodic_boundaries(m_home_ublk);
      dFLOAT intersecting_pt[3];
      dFLOAT voxel_dx = scale_to_voxel_size(m_home_ublk->scale());
      try{
        compute_intersection_on_face(latvec, m_p_location, m_location, voxel_dx, intersecting_pt);
      }
      catch(SearchError& msg) {
        msg << " when entering a split voxel";
        throw msg;
      }
      int instance_index = split_neighbor_info->find_overlapping_fpolygon(intersecting_pt, voxel_dx, m_home_voxel, latvec);
      if (instance_index <= -1)
            throw SearchError("split instance index is negative");
      if (instance_index >= n_split_ublks)
        throw SearchError("split instance index exceeds range");
      return t_ublks[instance_index].ublk();
    }
    else if (split_neighbor_info->is_edge_neighbor(latvec)) {
      return locate_new_home_ublk_from_multiple_split_ublks(2);
    }
    else if (split_neighbor_info->is_corner_neighbor(latvec)) {
      return locate_new_home_ublk_from_multiple_split_ublks(3);
    } else {
      SearchError msg("invalid latvec index"); 
      msg << latvec;
      throw msg;
    }
  }
  else {
    SearchError msg("ublk ");
    msg << m_home_ublk->id() << " voxel " << (int)m_home_voxel 
        << " has invalid instance " << (int)split_neighbor_info->ublk_instance(m_home_voxel, latvec) 
        << " for latvec " << (int)latvec;
    throw msg;
  }
}


/**
 * This function can be called right at the beginning (if the bsurfel is
 * currently in a split ublk) or it can be called from a tagged ublk.
 *
 * @pre split_ublk_vectors only hold ublks, even though they are tagged_ublks
 * @pre Assumes the point is inside this split ublk
 *
 * First, get the voxel the point is located in. Then determine how many of the
 * split ublks have this voxel. If only 1, then return it.
 * At this point, bsurfels and parcels differ. Bsurfels are allowed to live in
 * solid voxels, so count == 0 is valid. Otherwise, return the ublk with the
 * lowest id, which comes from old code. Comments claim it is for checkpointing
 * purposes. I haven't checked this out though. It would be simpler to just
 * return the first ublk, because for bsurfels it doesn't really matter.
 *
 * In the end, we have to check if the ublk is a vr_fine ublk, and if it is,
 * demote it to the vr_coarse. We can do this directly, even for splits, because
 * there are an equal number of vr_coarse and vr_fine splits, with a 1-to-1 mapping
 * between them.
 */

std::pair<UBLK, uINT8> sHOME_UBLK_LOCATOR::locate_new_home_ublk_in_split_ublk(sUBLK_VECTOR * split_ublk_vector,
    sINT8 voxel)
{
  int n_split_ublks = split_ublk_vector->num_split_ublks();
  TAGGED_UBLK * const t_ublks = split_ublk_vector->tagged_ublks();
  if (!t_ublks->is_ublk())
    throw SearchError("split_ublk_vector contains a reference that is not a ublk");
  //cassert(t_ublks->is_ublk());

  UBLK ret_ublk = nullptr;

  if (voxel == -1) {
    UBLK first_split_ublk = t_ublks->ublk();
    try {
      voxel = locate_voxel_and_check_inside(first_split_ublk);
    }
    catch (SearchError& msg) {
      if (m_for_bsurfels) {
        msg << "Entering Split ublk";
        msg << "Ublk ID: " << first_split_ublk->id()
            << " VR Scale: " << first_split_ublk->scale() << "\n"
            << "\tLocation: (" << first_split_ublk->location(0) << ", "
            << first_split_ublk->location(1) << ", "
            << first_split_ublk->location(2) << ")\n";
      }
      else {
        msg << " when entering a split voxel";
      }
      throw msg;
    }
  }

  // determine how many of the split ublks hold this voxel
  auto& possible_ublks = get_ublks_with_voxel_from_split(t_ublks,
                                                         n_split_ublks,
                                                         voxel);
  int count = possible_ublks.size();

  if (count == 1) {
    ret_ublk =  possible_ublks[0];
    process_periodic_boundaries(m_home_ublk);
  }
  else {
    if (m_for_bsurfels) {
      if (count == 0) {
        // return the first ublk. Which one doesn't matter, because the bsurfel
        // will be deactivated anyway. We can't return NULL because we still
        // need to track the bsurfel as it moves.
        ret_ublk = t_ublks[0].ublk();
      }
      else {
        // if more than one candidate is found, use the one with lowest ID
        // and fluid content. This is necessary to make bit for bit for checkpointing work.
        // During full checkpoint restore the ublk with the lowest ID is picked.
        // This is because the CP doesn't have enough information to really know which
        // split the bsurfel is in, but for bsurfels, the actual split ublk doesn't matter.
        UBLK min_neighbor = NULL; // candidate with lowest ID
        SHOB_ID min_neighbor_id = UINT_MAX;
        ccDOTIMES(i, count) {
          UBLK neighbor = possible_ublks[i];
          if (neighbor->id() < min_neighbor_id) {
            min_neighbor = neighbor;
            min_neighbor_id = neighbor->id();
          }
        }
        ret_ublk = min_neighbor;
      }
    }
    else {
      ret_ublk = locate_new_home_ublk_in_split_ublk_for_parcels(split_ublk_vector);
    }
  }

  if (ret_ublk == nullptr) {
    msg_print("Returning null ublk voxel pair");
    return std::make_pair(ret_ublk, -1);
  }
  if (!ret_ublk->is_mirror() && ret_ublk->is_vr_fine()) {
    sVR_FINE_INTERFACE_DATA * vr_data = ret_ublk->vr_fine_data();
    if (vr_data == NULL)
      throw SearchError("ublk returned a null reference to its vr_data");
    //cassert(vr_data);
    ret_ublk = vr_data->vr_coarse_ublk().ublk();
    try {
      voxel = locate_voxel_and_check_inside(ret_ublk);
    }
    catch (SearchError& msg) {
      msg << " when finding vr coarse ublk";
      throw msg;
    }
  }
  return std::make_pair(ret_ublk, voxel);
}

#if 0
  { 1,  1,  1},
  { 1,  1, -1},
  { 1, -1,  1},
  { 1, -1, -1},
  {-1,  1,  1},
  {-1,  1, -1},
  {-1, -1,  1},
  {-1, -1, -1}
};
#endif

STP_LATVEC_INDEX triple_vel_latvec_index(sINT8 disp[3]) 
{
  STP_LATVEC_INDEX latvec_offset = sSPLIT_NEIGHBOR_INFO::n_face_neighbors() +  sSPLIT_NEIGHBOR_INFO::n_edge_neighbors();
  if(disp[0] > 0) latvec_offset += 4;
  if(disp[1] > 0) latvec_offset += 2;
  if(disp[2] > 0) latvec_offset++;
  return latvec_offset;
}

STP_LATVEC_INDEX sHOME_UBLK_LOCATOR::latvec_from_displacement()
{
  int voxel_size = sim_scale_to_voxel_size(m_home_ublk->scale());
  sINT8 disp[N_AXES];
  ccDOTIMES(axis, N_AXES) {
    int voxel_displacement = lattice_location_from_position(m_location[axis], voxel_size) - lattice_location_from_position(m_p_location[axis], voxel_size);
    disp[axis] = voxel_displacement / voxel_size;
  }

  if (!disp[0] || !disp[1] || !disp[2]) //if not a tripple diagonal:
    return convert_state_vel_to_latvec_index(disp);
  return triple_vel_latvec_index(disp);
 }

/** Calculates the distance of the motion point from the center of the provided ublk
 * in the scaled lattice coords.
 *
 * 1. Find the center of the ublk
 * 2. Compute the offset in lattice coordinates
 * 3. Scale to the scale of the ublk
 *
 * @param [in] ublk The base ublk
 * @param [in] loc The location to get the displacement vector from (lattice units)
 * @param [out] ublk_offset
 *   The distance of the point in voxels (at the ublk scale) from the center of the ublk
 *
 * @return Whether the point is inside the ublk or not
 *
 */
bool sHOME_UBLK_LOCATOR::calc_centroid_offset_from_ublk(UBLK ublk, const dFLOAT loc[3], dFLOAT ublk_offset[3])
{
  bool inside = true;
  dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
  dFLOAT ivdx = 1.0 / voxel_dx;
  ccDOTIMES(axis, 3) {
    dFLOAT ublk_centroid = ublk->location(axis) + voxel_dx;
    ublk_offset[axis] = (loc[axis] - ublk_centroid) * ivdx;
    if (std::fabs(ublk_offset[axis]) > 1) {
      inside = false; // Do not break, because we need to fill in the rest of ublk_offset
    }
  }
  return inside;
}

//------------------------------------------
// sSET_VALID_BSURFEL_NEIGHBOR_UBLKS
//------------------------------------------

void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_interacting_on_scale_interface(sSCALE_BOX_INTERFACE *interface, VOXEL_MASK_8 mask)
{
  auto d = sDEPTH_COUNTER(this);
  DO_VOXELS_IN_MASK(fine_ublk, mask) {
    TAGGED_UBLK t_ublk = interface->m_fine_ublks[fine_ublk];
    set_bsurfel_interacting_on_tagged_ublk(t_ublk, VOXEL_MASK_8{0xFF});
  }
};


INLINE
void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_interacting_on_tagged_ublk(TAGGED_UBLK t_ublk, VOXEL_MASK_8 mask)
{
  if (t_ublk.is_null()) {
    return;
  }
  else if (t_ublk.is_ublk_scale_interface()) {
    set_bsurfel_interacting_on_scale_interface(t_ublk.interface(), mask);
  }
  else if (t_ublk.is_ublk_split()) {
    set_bsurfel_interacting_on_split_ublk(t_ublk.split_ublks());
  }
  else {
    set_bsurfel_interacting_on_ublk(t_ublk.ublk());
  }
}

INLINE
void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_interacting_on_ublk(UBLK ublk)
{

  if (ublk->is_mirror()) {
    return;
  }

  auto d = sDEPTH_COUNTER(this);
  if (ublk->is_vr_fine()) {
    sVR_FINE_INTERFACE_DATA * vr_data = ublk->vr_fine_data();
    //if(vr_data == NULL)
    //  throw SearchError("vrfine ublk has null reference to its vr data");
    cassert(vr_data);
    ublk = vr_data->vr_coarse_ublk().ublk();
  }

  // ignore solid ublks
  if (!ublk->is_solid()) {
    ublk->set_bsurfel_interacting();
  }
  set_bsurfel_group_type(ublk);
}

INLINE
void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_interacting_on_split_ublk(sUBLK_VECTOR * split_ublk_vector)
{
  auto d = sDEPTH_COUNTER(this);
  int n_split_ublks = split_ublk_vector->num_split_ublks();
  TAGGED_UBLK * t_ublks = split_ublk_vector->tagged_ublks();

  ccDOTIMES(i, n_split_ublks) {
    set_bsurfel_interacting_on_ublk(t_ublks[i].ublk());
  }
}

/*
 * The entry point for this algorithm
 */
void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_interacting_on_neighbor_ublks(UBLK home_ublk)
{
  auto d = sDEPTH_COUNTER(this);
  const auto& box_access = home_ublk->box_access();

  const auto& valid_ublks = l_all_valid_ublks[m_home_voxel];
  const auto& valid_ublks_voxel_masks = l_all_ublk_voxel_masks[m_home_voxel];
  for (int u = 0; u < valid_ublks.size(); u++) {
    const auto& offset = valid_ublks[u];
    const auto& voxel_mask = valid_ublks_voxel_masks[u];
    TAGGED_UBLK t_ublk = box_access.neighbor_ublk(offset.data());
    set_bsurfel_interacting_on_tagged_ublk(t_ublk, voxel_mask);
  }
}

/**
 * This function dictates how the bsurfels are broken up into groups.
 * The methodology used follows that set by the surfels and ublks:
 * If a bsurfel interacts with a ghost ublk, then it is "fringe".
 * If a bsurfel interacts with a fringe ublk, then it is "fringe2".
 * Otherwise, it is "interior". This methodology is used over other
 * possibilities in order to maintain repeatability and for
 * performance reasons. This group definition dictates important
 * predecessor relationships between the strands.
 */
void sSET_VALID_BSURFEL_NEIGHBOR_UBLKS::set_bsurfel_group_type(UBLK ublk)
{
  // if any neighbor ublk is fringe, then bsurfel MUST be in fringe group
  if (ublk->is_ghost()) {
    m_group = std::min(m_group, FRINGE_BSURFEL_GROUP_TYPE);
  }
  else if (ublk->is_fringe()) {
    m_group = std::min(m_group, FRINGE2_BSURFEL_GROUP_TYPE);
  }
  else {
// #if ENABLE_CONSISTENCY_CHECKS
//     if (m_check_all_ublks_are_fringe) {
//       msg_internal_error("Fringe bsurfel is interacting with non-ghost/non-fringe ublk!");
//     }
// #endif
    m_group = std::min(m_group, INTERIOR_BSURFEL_GROUP_TYPE);
  }
}

//------------------------------------------
// NEIGHBOR_UBLK_LOCATOR
//------------------------------------------

void sNEIGHBOR_UBLK_LOCATOR::add_neighboring_ublks()
{
  sUBLK * home_ublk = m_home_ublk;
  uINT8 home_voxel = m_home_voxel;
  const auto& box_access = home_ublk->box_access();
  sINT16 offset[3];

  const auto& valid_ublks = l_all_valid_ublks[home_voxel];
  const auto& valid_ublks_voxel_masks = l_all_ublk_voxel_masks[home_voxel];
  for (int u = 0; u < valid_ublks.size(); u++) {
    const auto& offset = valid_ublks[u];
    const auto& voxel_mask = valid_ublks_voxel_masks[u];
    // skip tridiagonal neighbors like mainline for bsurfels
    if (m_skip_triple_diagonal) {
      int count = 0;
      ccDOTIMES(i, 3) {
        count += std::abs(offset[i]);
      }
      if (count == 3)
        continue;
    }

    TAGGED_UBLK t_ublk = box_access.neighbor_ublk(offset.data(), true);
    add_neighbor_ublks_from_tagged_ublk(t_ublk, voxel_mask);
  }
}

void sNEIGHBOR_UBLK_LOCATOR::add_neighbor_ublks_from_scale_interface(sSCALE_BOX_INTERFACE *interface, VOXEL_MASK_8 mask)
{
  auto d = sDEPTH_COUNTER(this);
  DO_VOXELS_IN_MASK(fine_ublk, mask) {
    TAGGED_UBLK t_ublk = interface->m_fine_ublks[fine_ublk];
    add_neighbor_ublks_from_tagged_ublk(t_ublk, VOXEL_MASK_8{0xFF});
  }
}

// split_ublk_vectors only hold ublks, even though they are tagged_ublks
INLINE
void sNEIGHBOR_UBLK_LOCATOR::add_neighbor_ublks_from_split_ublk(sUBLK_VECTOR* split_ublk_vector, VOXEL_MASK_8 mask)
{
  auto d = sDEPTH_COUNTER(this);
  int n_split_ublks = split_ublk_vector->num_split_ublks();
  TAGGED_UBLK * t_ublks = split_ublk_vector->tagged_ublks();
  ccDOTIMES(i, n_split_ublks) {
    add_neighbor_ublk(t_ublks[i].ublk(), mask);
  }
}

INLINE
void sNEIGHBOR_UBLK_LOCATOR::add_neighbor_ublks_from_tagged_ublk(TAGGED_UBLK t_ublk, VOXEL_MASK_8 mask)
{
  if (t_ublk.is_null()) {
    return;
  }
  else if (t_ublk.is_ublk_scale_interface()) {
    add_neighbor_ublks_from_scale_interface(t_ublk.interface(), mask);
  }
  else if (t_ublk.is_ublk_split()) {
    add_neighbor_ublks_from_split_ublk(t_ublk.split_ublks(), mask);
  }
  else {
    add_neighbor_ublk(t_ublk.ublk(), mask);
  }
}

void sBSURFEL_NEIGHBOR_UBLK_LOCATOR::add_neighbor_ublk(UBLK ublk, VOXEL_MASK_8 mask)
{

  if (ublk->is_solid() || ublk->is_mirror())
    return;
  auto d = sDEPTH_COUNTER(this);
  static const BOOLEAN is_2d = sim.is_2d();
  static const BOOLEAN is_linear_profile = !sim.is_turb_model || (sim.is_pf_model && (g_pf_ibm_linear_weight>0));
  static const dFLOAT local_voxel_dist_sqrd = is_2d ? 0.5F : 0.75F;
  BOOLEAN is_temp_pde_solver = sim.is_heat_transfer && (sim.T_solver_type == PDE_TEMPERATURE);

  if (ublk->is_vr_fine()) {
    sVR_FINE_INTERFACE_DATA * vr_data = ublk->vr_fine_data();
    //if(vr_data == NULL)
    //  throw SearchError("vrfine ublk has null reference to its vr data");
    cassert(vr_data);
    ublk = vr_data->vr_coarse_ublk().ublk();
    mask = VOXEL_MASK_8(0xFF);
    // recompute_resolution(ublk->scale());
  }

  dFLOAT rvec[3], rvec1[3];
  mask &= ublk->fluid_like_voxel_mask;

  DO_VOXELS_IN_MASK(voxel, mask) {
    rvec[0] = rvec1[0] = ublk->centroids(voxel, 0) - m_bsurfel.centroid(0);
    rvec[1] = rvec1[1] = ublk->centroids(voxel, 1) - m_bsurfel.centroid(1);
    rvec[2] = rvec1[2] = is_2d ? 0.0 : ublk->centroids(voxel, 2) - m_bsurfel.centroid(2);

    dFLOAT dist_sqrd = vdot(rvec, rvec);

    if (dist_sqrd >= m_resolution_sqrd) continue; // voxel is outside bsurfel influence

    // for tri/bi-linear interpolation of the fluid velocity from voxel to bsurfel
    rvec[0] = std::fabs(rvec[0]);
    rvec[1] = std::fabs(rvec[1]);
    rvec[2] = std::fabs(rvec[2]);

    dFLOAT interp_weight = m_one_over_resolution_cubed *
                           (m_resolution - rvec[0]) * (m_resolution - rvec[1]) *
                           (is_2d ? 1.0F : (m_resolution - rvec[2]));

    if (interp_weight <= 0.0) continue;   // negative/zero interp weight

    dFLOAT dist_weight, norm_dist_sqrd;
    if (is_linear_profile) {
      dist_weight = interp_weight;
    }
    else {
      norm_dist_sqrd = dist_sqrd * m_one_over_resolution_sqrd;
      //dist_weight = MAX(0.0, 1.0 - norm_dist_sqrd * norm_dist_sqrd);
      dist_weight = (norm_dist_sqrd <= local_voxel_dist_sqrd) ? 1.0F : 0.0F;
    }

    // mark the "interior" ublk neighbors of the bsurfel to be used in the turb solver
    dFLOAT vec_dot_product = vdot(rvec1, m_bsurfel.normal());
    if (m_set_ublk_interior && (vec_dot_product > 0.0F)) {
      ublk->set_ib_interior(voxel, is_temp_pde_solver);
    }

    m_neighbor_info.emplace_back(ublk, voxel, interp_weight, dist_weight);
  }


}



