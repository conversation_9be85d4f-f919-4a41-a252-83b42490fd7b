#include PHYSICS_H

#include "mme_ckpt.h"
#include "common_sp.h"
#include "ckpt.h"
#include "sim.h"
#include "ublk.h"

VOID cMME_CKPT::write_mme_ckpt_ref_frame_info(bool is_avg_mme)
{
  LGI_TAG tag;
  tag.id = is_avg_mme ? LGI_AVG_MME_CKPT_REF_FRAME_DATA_TAG : LGI_MME_CKPT_REF_FRAME_DATA_TAG;
  tag.length = 0;

  write_ckpt_lgi_head(tag);

  // Only send info for the global ref frame if it is defined and time-varying
  if (sim.grf.is_time_varying) {
    sGRF_MEAS_FRAME_SP_TO_CP_MSG msg;

    vcopy(msg.ref_point_velocity, sim.grf.ref_pt_vel);    
    vcopy(msg.translation, sim.grf.ground_to_global_translation);

    vzero(msg.point);
    if (sim.grf.is_rotation) {
      vcopy(msg.angular_vel, sim.grf.angular_vel);
      vcopy(msg.axis, sim.grf.last_axis);
      msg.angle_rotated = sim.grf.angle_rotated;
    } else {
      vzero(msg.angular_vel);
      vzero(msg.axis);
      msg.angle_rotated = 0;
    }
    
    write_ckpt_lgi(msg);
  }

  LRF_PHYSICS_DESCRIPTOR lrf_physics_desc = sim.lrf_physics_descs;
  ccDOTIMES(i, sim.n_lrf_physics_descs) {
    sLRF_MEAS_FRAME_SP_TO_CP_MSG msg;

    if (lrf_physics_desc->is_angular_vel_time_varying ||
        lrf_physics_desc->is_rotational_dynamics_on) {
      if (!lrf_physics_desc->is_mlrf_on) {
        msg.angle_rotated = 0;
        msg.n_revolutions = 0;
      } else {
        msg.angle_rotated = lrf_physics_desc->angle_rotated;
        msg.n_revolutions = lrf_physics_desc->n_revolutions;
      }
      msg.angular_vel_mag = lrf_physics_desc->omega;

      write_ckpt_lgi(msg);
    }
    lrf_physics_desc++;
  }

}

VOID cMME_CKPT::write_mme_ckpt_movb_info(bool is_avg_mme)
{
  LGI_TAG tag;
  tag.id = is_avg_mme ? LGI_AVG_MME_CKPT_MOVB_DATA_TAG : LGI_MME_CKPT_MOVB_DATA_TAG;
  tag.length = 0;
  write_ckpt_lgi_head(tag);

  MOVB_PHYSICS_DESCRIPTOR movb = sim.movb_physics_descs;
  ccDOTIMES(i, sim.n_movb_physics_descs) {
    sMOVB_MEAS_FRAME_SP_TO_CP_MSG msg;
    ccDOTIMES(i, 4) {
      ccDOTIMES(j, 4) {
        msg.xform[i][j] = movb->motion_xform.M(i,j);
      }
    }
    // Stuff the angle rotated into the unused (3,0) xform matrix entry
    msg.xform[3][0] = movb->angle_rotated;
    write_ckpt_lgi(msg);
    movb++;
  }
}

#if !BUILD_GPU
VOID cMME_CKPT::write_voxel_mme_ckpt_data(bool is_avg_mme)
{
  DO_REALMS(realm) {
    sINT32 num_ublks = g_ckpt_group.m_ckpt_ublks[realm].size();

    ccDOTIMES(nu, num_ublks) {
      UBLK ublk = g_ckpt_group.m_ckpt_ublks[realm][nu];
      if (realm == STP_FLOW_REALM) 
        write_flow_ublk_mme_ckpt(is_avg_mme, ublk, *this);
      else 
        write_conduction_ublk_mme_ckpt(is_avg_mme, ublk, *this);

      write_ckpt_lgi(mask);

      if (mask.fluid_like_voxel_mask > 0) {
        write_ckpt_lgi(header);

        if (header.is_near_surface) {
          write_ckpt_lgi(surface);
        }

        if (m_num_ckpt_vars > 0) {
          write_ckpt_lgi(meas, sizeof(cDGF_MME_CKPT_MEAS_VAR)*m_num_ckpt_vars);
        }
      }
    }
  }
}
#endif

void cMME_CKPT::write_mme_ckpt(bool is_avg_mme)
{
  if (my_proc_id == 0) {
    write_mme_ckpt_ref_frame_info(is_avg_mme);
    write_mme_ckpt_movb_info(is_avg_mme);
  }
  if (sim.is_flow && sim.is_conduction_model){
    if (my_proc_id == total_sps/2) {
      write_mme_ckpt_ref_frame_info(is_avg_mme);
      write_mme_ckpt_movb_info(is_avg_mme);
    }
  }

  LGI_TAG tag;
  tag.id = is_avg_mme ? LGI_AVG_MME_CKPT_VOXEL_DATA_TAG : LGI_MME_CKPT_VOXEL_DATA_TAG;
  tag.length = 0;
  write_ckpt_lgi_head(tag);

  write_voxel_mme_ckpt_data(is_avg_mme);

  LGI_EOF_REC eof_record;
  eof_record.tag.id = LGI_EOF_TAG;
  eof_record.tag.length = lgi_pad_and_encode_record_length(sizeof(eof_record));

    write_ckpt_lgi_head(eof_record);
}

