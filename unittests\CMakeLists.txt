cmake_minimum_required (VERSION 3.8)

#=======================================================
# Check that we're on ion.
# This should eventually go away
#=======================================================
cmake_host_system_information(RESULT EXA_BUILD_HOSTNAME QUERY HOSTNAME)
set(EXA_VALID_BUILD_HOSTNAME "vm-ion.exa.com")
if(NOT "${EXA_BUILD_HOSTNAME}" STREQUAL "${EXA_VALID_BUILD_HOSTNAME}")
  message(STATUS "Building on ${EXA_BUILD_HOSTNAME}")
  message(FATAL_ERROR "Must build on ${EXA_VALID_BUILD_HOSTNAME}")
endif()

#=======================================================
# First setup compiler
#=======================================================

include("simeng_utils.cmake" RESULT_VARIABLE VCC_FOUND)

# The compiler must be set before calling project!
set_component_dir_using_vccset(LLVM)
set (CMAKE_CXX_COMPILER "${LLVM_D}/bin/clang++")

project (simeng_unittests LANGUAGES CXX)

# at least 11...
set (CMAKE_CXX_STANDARD 11)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --gcc-toolchain=${LLVM_D}/gcc-5.1.0")
include_directories("${LLVM_D}/include")

set(CMAKE_EXPORT_COMPILE_COMMANDS 1)

#=======================================================
# Determine build type
#=======================================================

if (NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release CACHE STRING
    "Choose the type of build, options are: Debug Release"
    FORCE)
endif()

if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
  add_definitions(-DDEBUG)
  set(VMAKE_DEBUG_FLAG "DEBUG=1")
endif()

get_filename_component(SIMENG_TARGET "${CMAKE_BINARY_DIR}" NAME)

message("Target: ${SIMENG_TARGET}")
if("${SIMENG_TARGET}" MATCHES "_avx")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DBUILD_AVX -mavx")
  set(SIMENG_AVX)
  message(STATUS "Using AVX instructions")
endif()

if("${SIMENG_TARGET}" MATCHES "_dp")
  set(BUILD_DOUBLE_PRECISION_STRING "BUILD_DOUBLE_PRECISION")
  set(SIMENG_DP)
  message(STATUS "Using double precision")
endif()

if("${SIMENG_TARGET}" MATCHES "_5g")
  set(SIMENG_5G)
  add_definitions(-DBUILD_5G_LATTICE)
  message(STATUS "Compiling 5g solver")
elseif("${SIMENG_TARGET}" MATCHES "_d39")
  set(SIMENG_D39)
  add_definitions(-DBUILD_D39_LATTICE)
  message(STATUS "Compiling transonic solver")
else()
  set(SIMENG_D19)
  add_definitions(-DBUILD_D19_LATTICE)
  message(STATUS "Compiling D19 solver")
endif()

if("${SIMENG_TARGET}" MATCHES "_hpmpi")
  message(STATUS "Compiling with hpmpi")
  set(MPI_CXX_COMPILER "/opt/platform_mpi-09.01.03.01r/bin/mpiCC")
  set(MPI_CXX_HEADER_DIR "/opt/platform_mpi-09.01.03.01r/include")
  add_definitions(-D__AMD64_LINUX_HPMPI__ -D_EXA_HPMPI)
  set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
  set(CMAKE_INSTALL_RPATH "${EXA_REGISTRY}/serverdist/dist/x86_linux/server/x86_64/hpmpi/lib/linux_amd64")
elseif("${SIMENG_TARGET}" MATCHES "_ompi")
  message(STATUS "Compiling with openmpi")
  set(MPI_CXX_COMPILER "/fa/sw/openmpi/1.8.8-01/bin/mpic++")
  set(MPI_CXX_HEADER_DIR "/fa/sw/openmpi/1.8.8-01/include")
else()
  message(FATAL_ERROR "No MPI specified")
endif()

#=======================================================
# Setup our dummy MPI library
#=======================================================

message(STATUS "MPI: ${MPI_CXX_COMPILER}")

find_package(MPI REQUIRED)
if (MPI_FOUND)
  include_directories(${MPI_INCLUDE_PATH})
else (MPI_FOUND)
  message(SEND_ERROR "MPI Required")
endif (MPI_FOUND)

#=======================================================
# Setup dependencies on simeng src files
#=======================================================

set(SIMENG_OBJ_DIR "${SIMENG_SRC_DIR}/${SIMENG_TARGET}")

set(SIMENG_OBJ_FILES "")
foreach(S ${SIMENG_SRC_FILES})
  get_filename_component(F ${S} NAME)
  string(REPLACE ".cc" ".o" OBJ ${F})
  list(APPEND SIMENG_OBJ_FILES "${SIMENG_OBJ_DIR}/${OBJ}")
endforeach()

add_custom_target(vmake_simeng ALL
  COMMAND vmake ${SIMENG_TARGET}/simeng -j 4 ${VMAKE_DEBUG_FLAG} 
  COMMENT "Running vmake" VERBATIM)

# doing this ensures that vmake is run once everytime the
# tests are compiled. I can't find a nicer way around this
# add_custom_target(vmake_simeng DEPENDS ${SIMENG_OBJ_FILES})

#=======================================================
# Setup the gtest library
#=======================================================

set(GTEST_DIR "/fa/sw/gtest/1.8.0-01")
find_library(GTEST_LIB gtest PATHS "${GTEST_DIR}/lib" REQUIRED)
find_library(GTEST_MAIN_LIB gtest_main PATHS "${GTEST_DIR}/lib" REQUIRED)
include_directories("${GTEST_DIR}/include")

#=======================================================
# Setup pthreads
#=======================================================

set(THREADS_PREFER_PTHREAD_FLAG ON)
find_package(Threads REQUIRED)

#=======================================================
# Setup Intel Math libraries
#=======================================================

find_library(INTEL_RT
  NAMES rt
  DOC "Intel runtime library"
  REQUIRED)

find_library(INTEL_SVML
  NAMES libsvml.a
  DOC "Intel Short Vector Math Library"
  HINTS "/opt/intel/parallel_studio_xe_2015_update1/composer_xe_2015.1.133/compiler/lib/intel64"
  REQUIRED)

find_library(INTEL_IRC
  NAMES libirc.a
  DOC "Intel IRC"
  HINTS "/opt/intel/parallel_studio_xe_2015_update1/composer_xe_2015.1.133/compiler/lib/intel64"
  REQUIRED)

#=======================================================
# Setup all the necessary definitions, libraries, additional compiler options
#=======================================================

vcc_includes()

set(EXA_TARGET_DEFINITIONS 
 __LINUX__
 __AMD64_LINUX__
 _EXA_CLANG
 ${AVX_DEFINE_STRING}
 ${BUILD_DOUBLE_PRECISION_STRING}
 ${BUILD_XX_LATTICE_STRING}
 ${PHYSICS_VERSION_VAR}
 ${SP_VERSION_VAR}
 ${PRODUCT_VERSION_VAR}
 __AMD64_LINUX2_64__
 _EXA_MPI
 _FILE_OFFSET_BITS
 _LARGE_FILES
 _XOPEN_SOURCE
 __forceinline=inline
 EXA_CURRENT_YEAR=2018
 ${SIMENG_PHYSICS_DEFINES}
 BUILD_IB=0)

add_compile_options("-Wno-backslash-newline-escape"
  "-Wno-writable-strings"
  "-Wno-mismatched-new-delete"
  "-Wno-deprecated")

simulator_libs()

#=======================================================
# Now setup all the test cases
#=======================================================

enable_testing()

macro(add_simeng_test _test_name _exe_name _test_files)

  add_executable(${_exe_name} ${_test_files})
  target_compile_definitions(${_exe_name} PRIVATE ${EXA_TARGET_DEFINITIONS})
  add_dependencies(${_exe_name} vmake_simeng)
  target_link_libraries(${_exe_name}
      ${SIMENG_OBJ_FILES}
      ${MPI_CXX_LIBRARIES}
      ${PHYSICS_LIB}
      # this is an exception because you can't read the directory amd64_linux 
      ${EXALIC_D}/amd64_linux/libexalic.a
      ${SIMULATOR_LIB_LIST}
      ${INTEL_RT} 
      ${INTEL_SVML}
      ${INTEL_IRC}
      ${GTEST_LIB}
      ${GTEST_MAIN_LIB}
      ${CMAKE_THREAD_LIBS_INIT}
      "-Wl,--allow-multiple-definition")
  add_test(${_test_name} ${_exe_name})
endmacro()

add_simeng_test(NeighborSpTest neighbor_sp_test neighbor_sp_test.cc )
add_simeng_test(MemoryPoolTest memory_pool_test memory_pool_test.cc )
add_simeng_test(BsurfelTest bsurfel_test bsurfel_test.cc )

