/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1995-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */

/*--------------------------------------------------------------------------*
 * Tree for Ublk boxes and ublks 
 *
 * Abhishek Jain
 * Created Sep 30, 2024
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_UBLK_BOX_TREE_H_
#define _SIMENG_UBLK_BOX_TREE_H_


#include "box_advect.h"

// Base class to hold static data about ublk boxes
struct sUBLK_BOX_HEADER_BASE {
  sINT16                    proc_id;
  STP_SCALE                 m_scale; 
  STP_COORD                 m_size[3]; 
  STP_COORD                 m_location[3];
  int                       offset_dynData;

  sUBLK_BOX_HEADER_BASE() {};
  
  VOID get_ublk_location(uINT16 indices[3], STP_COORD ublk_location[3]) {
    ublk_location[0] = (indices[0] << (sim.num_scales - m_scale)) + m_location[0];
    ublk_location[1] = (indices[1] << (sim.num_scales - m_scale)) + m_location[1];
    ublk_location[2] = (indices[2] << (sim.num_scales - m_scale)) + m_location[2];
  };

  asINT32 get_linear_index(uINT16 indices[3]) {
    return (m_size[2] * (m_size[1] * indices[0] + indices[1]) + indices[2]);
  };
};

// Derived class to hold static + dynamic data about ublk boxes
struct sUBLK_BOX_HEADER : public sUBLK_BOX_HEADER_BASE {
  std::vector<uINT8>        ublk_bitset;

  sUBLK_BOX_HEADER(UBLK_BOX box) {
    proc_id = my_proc_id;
    m_scale = box->m_scale;
    m_size[0] = box->m_size[0];
    m_size[1] = box->m_size[1];
    m_size[2] = box->m_size[2];
    m_location[0] = box->m_location[0];
    m_location[1] = box->m_location[1];
    m_location[2] = box->m_location[2];

    int n_ublks = box->m_size[0] *  box->m_size[1] * box->m_size[2]; 
    for (int i = 0; i < n_ublks; i++) {
      bool is_ublk_owned = false; //slot not filled?
      TAGGED_UBLK t_ublk = box->m_ublks[i];
      //if ublk is owned by this sp?
      if (!t_ublk.is_null()) {
        if (t_ublk.is_ublk_scale_interface()) {
          ccDOTIMES(fine_blk, 8) {
            TAGGED_UBLK fine_ublk = t_ublk.interface()->m_fine_ublks[fine_blk];
            if (!fine_ublk.is_null()) {
              is_ublk_owned = !fine_ublk.is_ublk_ghost();
              continue;
            }
          }
        } else {
          is_ublk_owned = !t_ublk.is_ublk_ghost();
        }
      }
      //if (!box->m_ublks[i].is_null()) {
      //  is_ublk_owned = !box->m_ublks[i].is_ublk_ghost(); //box->get_tagged_ublk(i)->is_ghost()?
      
      ublk_bitset.emplace_back(is_ublk_owned);
    }
    offset_dynData = 0;
  };

  sUBLK_BOX_HEADER() {};
};

// Struct to build the SP map/tree
struct sBOX_SPMAP_BUILDER {
  //data members-------------------------------

  //intra-node comm sp -> master_sp 
  int master_rank = 0;               //rank of master sp in the node
  int n_sps_node;                    //number of sps in the node
  int n_mastersp;                    //number of master sps  
  int rank_on_node;                  //rank off sp in the node
  int nboxes_to_send_sp = 0;             //number of boxes to be sent by each SP to master sp
  int nublks_to_send_sp = 0;             //number of ublks to be sent by each SP to master sp

  int nboxes_on_master = 0;              //number of boxes on master
  int nublks_on_master = 0;              //number of ublks on master
                                     
  int tot_nboxes_dom = 0;              //total number of boxes in domain
  int tot_nublks_dom = 0;              //total number of ublks in domain
                                     
  //recv nboxes buffers --> will be malloc'ed, need to be free'd later
  int* recv_nboxes_sp = nullptr;               //number of boxes to be recv from each SP on master sp
  sUBLK_BOX_HEADER_BASE* recv_boxes_sp = nullptr;               //boxes to be recv from each SP on master sp
  int* displs_boxes_sp = nullptr;             //displacements recv boxes from each SP on master sp
  
  //recv ublks buffers --> will be malloc'ed, need to be free'd later
  int* recv_nublks_sp = nullptr;              //number of ublks to be recv from each SP on master sp
  uINT8* recv_ublks_sp = nullptr;               //ublkss to be recv from each SP on master sp
  int* displs_ublks_sp = nullptr;             //displacements recv ublks from each SP on master sp
  

  //inter-node comm master_sp <--> master_sp 
  
  //recv nboxes buffers --> will be malloc'ed, need to be free'd later
  int* recv_nboxes_master = nullptr;               //number of boxes to be recv from each master
  int* displs_boxes_master = nullptr;             //displacements recv boxes from each master
  
  //recv ublks buffers --> will be malloc'ed, need to be free'd later
  int* recv_nublks_master = nullptr;              //number of ublks to be recv from each master
  int* displs_ublks_master = nullptr;             //displacements recv ublks from each master
  
  bool is_master;                    //if i am the master sp in the node, rank==master_rank
                                     
  std::vector<sUBLK_BOX_HEADER> ublk_box_header_sp; //vector of ublk_box_headers

  //methods----------------------------------------
  
  //constructor
  sBOX_SPMAP_BUILDER();

  //destructor
  ~sBOX_SPMAP_BUILDER() {};

  //to create box headers on all SPs in a vector
  VOID create_box_info_headers();

  //gather all boxes from SPs to the master SP on the node
  VOID gather_to_master_sp();

  //allgather from master SPs to other master SPs
  VOID allgather_to_master_sps();

  //put all box header info on shared memory
  VOID put_box_headers_in_smem();

  //free all dynamically allocated memory
  VOID free_dyn_mem();

};


VOID build_box_spmap_for_bsurfels();
VOID add_all_procs_to_sp_map();

BOOLEAN check_if_box_is_inside_child_domain(STP_COORD box_min[3], 
                                            STP_COORD box_max[3], 
                                            STP_COORD child_min[3], 
                                            STP_COORD child_max[3]);
BOOLEAN is_interval_overlapping (STP_COORD box_size[2], STP_COORD domain_size[2]);
BOOLEAN is_point_inside_domain (dFLOAT loc[3], STP_COORD domain_min[3], STP_COORD domain_max[3]);

struct sBOX_TREE_NODE {
  STP_COORD                       m_min[3];       //the min location of the domain of this node in each direction                                    
  STP_COORD                       m_max[3];       //the max location of the domain of this node in each direction                                    
  uINT16                          m_index;        //index of the node in the tree node vector m_nodes
  uINT16                          m_children[8] = {0};  //an array of pointers to the children nodes (currently 8, will change according to 2^ndims later)
  uINT8                           m_level;        //level of this node in the tree 
  std::vector<sUBLK_BOX_HEADER_BASE*>   m_ublk_boxes;   //a vector of ublk_box_headers pointers
};

struct sBOX_TREE {
  sBOX_TREE_NODE*    root_node; //pointer to the root node aka the first node in the vector m_nodes
  std::vector<sBOX_TREE_NODE>   m_nodes;  //vector of all nodes

  sBOX_TREE_NODE get_root_node() { return m_nodes.at(0); };
                                       
  sBOX_TREE() {   //tree constructor
  };
  
  ~sBOX_TREE() {}; //destructor 

  VOID build_tree() {
    create_root_node();
    propogate_branch_until_leaf(get_root_node());
  };

  VOID create_root_node();
  VOID propogate_branch_until_leaf (sBOX_TREE_NODE current_node);
  sBOX_TREE_NODE* find_internal (dFLOAT loc[3], sBOX_TREE_NODE* current_node);
};

#endif

