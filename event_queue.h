/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#ifndef _SIMENG_EVENT_QUEUE_H
#define _SIMENG_EVENT_QUEUE_H

#include "common_sp.h"
#include "event_queue_event.h"
#include "async_events.h"

#include <forward_list>
#include <memory>

// This queue is used to communicate special events between the main and comm
// threads.  Each event has an associated timestep. This is a priority queue,
// kept in timestep order. Within a timestep, events are ordered by id number.
// BASETIME_LAST is a special reserved timestep, which indicates this event is
// to be processed after the simulation has exited. The queue is processed at
// the end of each timestep in time_update_strand.
//
// The CP will notify SPs of unanticipated events by sending an EVENT_REQ. This
// special event starts a negotiation process where each SP tells the CP when
// it is able to fulfill the request. The CP then sends the actual event to the
// SPs with an assigned timestep. 
//
// Sometimes an SP will have no ublks or surfels, and so will race ahead of all
// the other SPs, which has caused problems in the past. Hopefully the current
// implementation addresses this issue. After an SP exits, the main thread will
// enter a holding loop, where all it does is attempt to process events on the
// queue. However, it is not allowed to process BASETIME_LAST events until it
// gets the go-ahead from the CP. This forces the empty SP to continue
// participating in any unanticipated events. Both the main & comm threads wait
// for another message from the CP in order to finally close up shop.

typedef class sEVENT_QUEUE {
private:
  std::forward_list< std::unique_ptr<sEVENT_QUEUE_EVENT> > queue;
  BOOLEAN               reply_pending_p;
  // This mutex is locked while processing the queue so that comm thread
  // cannot add the reply event while the queue is processed.
  pthread_mutex_t       m_mutex;

  VOID lock() {
    pthread_mutex_lock(&m_mutex);
  }

  VOID unlock() {
    pthread_mutex_unlock(&m_mutex);
  }

public:
  sEVENT_QUEUE() {  // constructor
    reply_pending_p = FALSE;
    pthread_mutex_init(&m_mutex, NULL);
  }

  class cLOCK {
    sEVENT_QUEUE * m_p;
  public:
    cLOCK() : m_p{nullptr} {}
    cLOCK(sEVENT_QUEUE * p) : m_p{p} { m_p->lock(); }
    ~cLOCK() { if (m_p) m_p->unlock(); }
    cLOCK(const cLOCK&) = delete;
    cLOCK(cLOCK&& b) { m_p = b.m_p; b.m_p = nullptr; }
    cLOCK & operator = (cLOCK&& b) { this->~cLOCK(); m_p = b.m_p; b.m_p = nullptr; return *this; }
    bool is_locked() const  { return m_p; }
  };

  friend class cLOCK;

  VOID add_entry(std::unique_ptr<sEVENT_QUEUE_EVENT> entry, cLOCK l = cLOCK());
  std::unique_ptr<sEVENT_QUEUE_EVENT> pop_if_next_timestep(BASETIME next_timestep);
  EVENT_ID remove_recurring_event(EVENT_ID id);
  VOID process_queue(BASETIME strand_time);

  cLOCK get_lock() { return cLOCK(this); }

  VOID print_queue();
  void append_recurring_event(std::unique_ptr<sEVENT_QUEUE_EVENT> entry);
} *EVENT_QUEUE;


VOID process_event_replies();
VOID add_event(std::unique_ptr<sEVENT_QUEUE_EVENT> event,sEVENT_QUEUE::cLOCK l = sEVENT_QUEUE::cLOCK());

BOOLEAN receive_vtable(int mpi_tag); 
BOOLEAN receive_ptherm_time_info();
BOOLEAN receive_coupling_model(int mpi_tag); 
BOOLEAN receive_rotational_dynamics(int mpi_tag);
BOOLEAN maybe_receive_dsm(MPI_Status &probed_mpi_status);
BOOLEAN maybe_post_recv_for_tbs_data(asINT32 imeas);
VOID    test_posted_tbs_recvs();
#endif /* #ifndef _SIMENG_EVENT_QUEUE_H */

