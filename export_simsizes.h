#ifndef EXPORT_SIMSIZES_H_
#define EXPORT_SIMSIZES_H_
#include <stddef.h>
#include PHYSTYPES_H
#include "simulator_namespace.h"
#include <map>

//Forward decls
class cDGF_SHOB_DESC;

namespace SIM_SIZES {
 class SHOB_INFO;
 class SIM_OPTIONS;
 struct sSHOB_SIZES;
 struct sUBLK_STATIC_INFO;
}


/*=============================================================================
 * This class establishes an interface with which all size related information
 *  is exported by the simulator for various lattice builds. This class is only
 *  accessible through the SimSizes module that exposes its functionality in a more
 *  client friendly approach.
 *
 *  All classes are manufactured in $(SimSizes)/sim_sizes_factory.cpp
 */
class SIMENG_SIZES_INTERFACE {
public:
  SIMENG_SIZES_INTERFACE(){};
  virtual ~SIMENG_SIZES_INTERFACE(){};
  virtual size_t size_of_sSURFEL() const = 0;
  virtual size_t size_of_sUBLK() const = 0;
  virtual SIM_SIZES::sSHOB_SIZES get_shob_size(const SIM_SIZES::SHOB_INFO&) const = 0;
  virtual VOID set_ublk_static_info(const SIM_SIZES::sUBLK_STATIC_INFO& info) = 0;
  virtual size_t size_of_nearblk_send_field() const = 0;
  virtual size_t size_of_farblk_send_field() const = 0;
  virtual size_t size_of_surfel_send_field() const = 0;
};

#define EXPORT_SIMENG_SIZES_COMMON                                        \
class SIMENG_SIZES_INTERFACE_IMPL : public SIMENG_SIZES_INTERFACE         \
{                                                                         \
  public:                                                                 \
  SIMENG_SIZES_INTERFACE_IMPL(const SIM_SIZES::SIM_OPTIONS& opts);               \
  virtual ~SIMENG_SIZES_INTERFACE_IMPL();                                        \
  virtual size_t size_of_sSURFEL() const override;                        \
  virtual size_t size_of_sUBLK() const override;                          \
  virtual SIM_SIZES::sSHOB_SIZES get_shob_size(const SIM_SIZES::SHOB_INFO&) const override; \
  virtual VOID set_ublk_static_info(const SIM_SIZES::sUBLK_STATIC_INFO& info) override;\
  virtual size_t size_of_nearblk_send_field() const override;             \
  virtual size_t size_of_farblk_send_field() const override;              \
  virtual size_t size_of_surfel_send_field() const override;              \
						                          \
  private:                                                                \
  const SIM_SIZES::SIM_OPTIONS* m_sim_opts;                               \
}; 

//For now each type of simulator build has the same generic implementations for
//determining the sizes of various simeng simulation pieces. However this can
//be changed by having a new derived class that inherits from SIMENG_SIZES_INTERFACE
//and implements methods unique to a certain namespace.
inline namespace Simulator_D19_SP {
EXPORT_SIMENG_SIZES_COMMON
}

inline namespace Simulator_D19_DP {
EXPORT_SIMENG_SIZES_COMMON
}

inline namespace Simulator_D39_SP {
EXPORT_SIMENG_SIZES_COMMON
}

inline namespace Simulator_D39_DP {
EXPORT_SIMENG_SIZES_COMMON
}

inline namespace Simulator_D5G_SP {
EXPORT_SIMENG_SIZES_COMMON
}

inline namespace Simulator_D5G_DP {
EXPORT_SIMENG_SIZES_COMMON
}


#endif
