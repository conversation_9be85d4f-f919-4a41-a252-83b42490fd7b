/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
#ifndef CONDUCTION_SHELL_MESH_H_INCLUDED
#define CONDUCTION_SHELL_MESH_H_INCLUDED


#include "common_sp.h"
#include <vector>
#include BG_H
#include BREP_H
#include BAGS_H
#include TEARRAY_H
#include FGEOM_H

// Expands sdFLOAT arrays, sBG_POINT3 and sBG_VECTOR3
#define EXPAND_FLOAT_VEC3(vec) (vec)[0],(vec)[1],(vec)[2]

// Expands sdFLOAT arrays, sBG_POINT2 and sBG_VECTOR2
#define EXPAND_FLOAT_VEC2(vec) (vec)[0],(vec)[1]

#define DEBUG_BREP_STENCIL

#ifdef DEBUG_BREP_STENCIL

#define stencil_assert(expr)                            \
  if (!(expr)) {                                        \
    assert_handler(__STRING(expr), __FILE__, __LINE__); \
  }
#else
#define stencil_assert(expr)
#endif


// Forward declaration of cSHELL_CONDUCTION_STENCIL_NEIGHBOR and cCONDUCTION_EDGE_BASE Objects
inline namespace SIMULATOR_NAMESPACE {
 class cSHELL_CONDUCTION_STENCIL_NEIGHBOR;
 class cCONDUCTION_EDGE_BASE;
}
typedef SIMULATOR_NAMESPACE::cSHELL_CONDUCTION_STENCIL_NEIGHBOR *SHELL_CONDUCTION_STENCIL_NEIGHBOR;
typedef SIMULATOR_NAMESPACE::cCONDUCTION_EDGE_BASE *CONDUCTION_EDGE_BASE;

/**
 @brief Flags used to for storing information about facets needed during stencil building process
 */
typedef enum : uINT32 {
  UNSET_FLAG = 0x0,
  DISCARD_FLAG = 0x1,
  KEEP_FLAG = 0x2,
} FACET_FLAGS;

/**
 @brief A BREP Facet object representing a surfel
 
 @details This BREP Facet provides additional functions useful
          for building shell surfel stencils. Additionally,
          it stores a pointer to the surfel it represents.

          This facet also contains a uINT32 which can store information
          about the facet/surfel using the FACET_FLAGS enum. Currently
          it is only used to identify which facets should be kept during
          the simplification process implemented in cSHELL_STENCIL_INFO.
 */
BREP_DEFINE_FACET(SHELL_FACET, BREP_DEFAULT_FACET) {
  BREP_FACET_BASIC_DEFS;
  typedef BREP_FACET(BREP_DEFAULT_FACET) BASE;
  typedef BREP_FACET(SHELL_FACET) cSELF;
  typedef BREP_FACET_TYPENAME(SHELL_FACET)::FACET SHELL_FACET_TYPE;
  typedef BREP_FACET_TYPENAME(SHELL_FACET)::HALF_EDGE SHELL_HE_TYPE;
  typedef typename tBREP_GEOMETRY_TYPES<BG_KERNEL>::sBG_TRANSFORM3 sBG_TRANSFORM3;

  public:
  BREP_TYPE(SHELL_FACET)(iBREP_FACET facetIndex = BREP_INVALID_IFACET_INTERNAL,
                           iBREP_SHELL shellIndex = BREP_INVALID_ISHELL)
                           : BASE(facetIndex, shellIndex), m_flag(FACET_FLAGS::UNSET_FLAG) {}
  BREP_TYPE(SHELL_FACET)(const BASE &f) : BASE(f), m_flag(FACET_FLAGS::UNSET_FLAG) { }
  BREP_TYPE(SHELL_FACET)(const cSELF &f) : BASE(f), m_flag(FACET_FLAGS::UNSET_FLAG) {
    m_surfel = f.m_surfel;
  }

// For setting and getting associated surfel info
  VOID set_surfel(sSURFEL* surf) { m_surfel = surf; }
  sSURFEL* get_surfel() { return m_surfel; }

  // for setting, resetting and getting flag
  VOID add_flag(uINT32 flag_val) { m_flag |= flag_val; }

  VOID reset_flag() { m_flag = FACET_FLAGS::UNSET_FLAG; }

  VOID set_flag(const uINT32 fval) { m_flag = fval; }

  uINT32 flag_value() { return m_flag; }

  BOOLEAN marked_to_keep() {
    // stencil_assert(m_flag);
    return (m_flag & FACET_FLAGS::KEEP_FLAG);
  }

// Methods for obtain geometric, shell and surfel information
  BOOLEAN is_facet_shell(BOOLEAN exclude_ghosts=FALSE);

  BOOLEAN is_facet_shell_or_shell_neighbor(BOOLEAN exclude_ghosts=FALSE);

  BOOLEAN is_open_shell_facet();

  asINT32 surfel_face_id();

  asINT32 num_layers();

  asINT32 get_opposite_surfel_id();

  BOOLEAN is_boundary_facet_duplicate(SHELL_FACET_TYPE facet2);

  BOOLEAN has_edge_neighbors();

  VOID identify_facet_edge_types();

  SHELL_HE_TYPE half_edge_from_index(asINT32 ind);

  // Methods for obtaining edges of 2D facet
  SHELL_HE_TYPE get_2d_half_edge();

// Methods for printing facet info for debugging
  VOID dump_facet_info();

  VOID dump_edge_verification_info(std::vector<SHELL_HE_TYPE>& edges);

  VOID globalVertexIndices(std::vector<DGF_VERTEX_INDEX>& vertInds);

  sBG_TRANSFORM3& getTransform() {
    return m_global_to_local_transform;
  }
private:
  sSURFEL* m_surfel;
  uINT32 m_flag;

  sBG_TRANSFORM3 m_global_to_local_transform;
};

// Forward declaration of sGHOST_HALF_EDGE_INFO used by half edge class
struct sGHOST_HALF_EDGE_RING_INFO;


/**
 @brief A BREP Half Edge object for use in shell stencil building
 
  This BREP Half Edge provides additional functions useful for building shell surfel stencils.
  Additionally, it calculates and stores geometric information relative to the shell surfels'
  local csys and scale, which is later passed along to the resulting conduction edges. 

  A pointer to a cCONDUCTION_EDGE_BASE allows for conduction edges to be stored
  at half_edges so that subsequent shell surfels can access the pointer when it 
  is a neighbor surfel of that edge (specifically at internal and intersection
  edges which are shared by 2+ surfels).

  Ordering of layers and location of dynamics info differs depending on whether or not
  the shell is an open shell or attached to a conduction surface. Diagram below describes
  the ordering and dynamics data locations for open and closed shells. In both of these cases, the surfel
  is in the conduction realm.

  Open Shell: Can have separate front and back dynamics data. Ordering in opposite direction of normal direction.

               ____Front Dynamics Data____
              |___________________________| Shell Layer 0
              |___________________________| Shell Layer 1    ^
              |___________________________| Shell Layer 2    | normal direction
                    Back Dynamics Data

  Closed Shell: Dynamics data defines the back of the shell at which layer 0 is located. Front of shell
                is always coupled to the conduction volume.

               _(always coupled to solid)_
              |___________________________| Shell Layer 2
              |___________________________| Shell Layer 1    ^
              |___________________________| Shell Layer 0    | normal direction
                        Dynamics Data

  In this implementation, the terms 'front' and 'back' as defined by the normal direction will be used
  to describe the location of dynamics data. The location of the dynamics data for either surfel type will look like:
                 _____Front Dynamics Data___ 
                |___________________________| Shell Layer
                |___________________________| Shell Layer    ^
                |___________________________| Shell Layer    | normal direction
                      Back Dynamics Data
  The call to `get_back_dynamics_type` will return the dynamics data stored at the back of the surfel
  (as defined by the normal direction). For open shells, this is the data stored in
  surfel->dynamics_data()->next_dynamics_data() if surfel->has_distinct_back_bc() is TRUE. Otherwise
  the dynamics is assumed to be thermally coupled. For closed shell surfels, this is the dynamics
  data stored in surfel->dynamics_data(). The call to `get_front_dynamics_type` will return the dynamics
  stored at the front of the surfel. For open shells, this is the data at surfel->dynamics_data() while
  for closed shells surfels it will be at surfel->dynamics_data()->next_dynamics_data() if 
  surfel->has_distinct_back_bc() is TRUE. Currently, surfel->has_distinct_back_bc() will always return FALSE,
  but this will not remain the case after future changes. 

  Note: In current implementation, all information about the dynamics types at shell surfels
        is ignored since we are not able to read back dynamics of open shell ghost surfels. Therefore
        the call to `get_dynamics_type` should only be made for HALF_EDGEs associated with non-shell
        surfels and will always return the data stored in surfel->dynamics_data(). Future updates may
        add the ability to incorporate dynamics info of shell surfels in the edge determination process.
        If so, a separate get_front_dynamics function will be included that returns the main dynamics
        data of open shell surfels and the will always return a thermally coupled type for closed
        shell surfels.

  In the event a dynamics type is thermal contact with fixed 0 resistance, the type will be treated
  as thermally coupled

  Because the different orientations affect the alignment of layer numbers at internal edges, the function
  `are_layers_aligned` takes the shell type into account when determining alignment. This function is used
  to pass alignment information to the CONDUCTION_INTERNAL_EDGE which will access neighboring layers
  in the opposite order if they are reversed
  
  Note: The term "coupled surfel" is used to refer to surfels which are each other's opposite
        (i.e. the opposite_surfel() functions point to each other). Additionally, the "coupled edge"
        and "coupled facets" terms refer to edges associated with the coupled surfels. This can cause confusion
        due to the thermally coupled dynamics type. An attempt has been made to always refer to this dynamics
        type as thermally coupled rather than just coupled to avoid confusion.
 */
BREP_DEFINE_HALF_EDGE(SHELL_HALF_EDGE, BREP_DEFAULT_HALF_EDGE) {
  BREP_HALF_EDGE_BASIC_DEFS;
  typedef BREP_HALF_EDGE(BREP_DEFAULT_HALF_EDGE) BASE;
  typedef BREP_HALF_EDGE(SHELL_HALF_EDGE) SELF;
  typedef typename BG_KERNEL::NT NT;
  typedef BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE SHELL_HE_TYPE;
  typedef BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::FACET SHELL_FACET_TYPE;
  typedef std::pair<SHELL_HE_TYPE,NT> EDGE_ANGLE_PAIR;
  typedef std::pair<SHELL_HE_TYPE,asINT32> EDGE_INFO_PAIR;
  typedef std::vector<EDGE_ANGLE_PAIR> EDGE_ANGLE_PAIR_VECTOR;
  typedef std::vector<EDGE_INFO_PAIR> EDGE_INFO_PAIR_VECTOR;
  typedef std::vector<SHELL_HE_TYPE> HALF_EDGE_VECTOR;
  typedef std::pair<SHELL_HE_TYPE, uINT32> SPLIT_INFO_PAIR;
  typedef std::pair<SHELL_HE_TYPE, SHELL_HE_TYPE> FIXED_TEMP_NBR_PAIR;
  
  using INFO_FLAG_INDS = enum {
    HE_UNPROCESSED_IND,
    NBR_HE_INTERNAL_IND,
    NBR_HE_INTERSECTION_IND,
    NBR_HE_ADIABATIC_BND_IND,
    NBR_HE_DYN_DATA_BND_IND,
    NBR_HE_FIXED_TEMP_BND_IND,
    NBR_HE_IS_PAIR_IND,
    HE_IGNORED,
    NUM_INFO_FLAGS
  };

public:
  BREP_TYPE(SHELL_HALF_EDGE)() 
      : BASE(), m_conduction_edge(NULL), m_neighbor_he(NULL),
        m_int_edge_rotation(-1e10), m_int_edge_unfolded_vec{-1e10,-1e10} {
    m_info_flags.set(HE_UNPROCESSED_IND);
  }

  ~BREP_TYPE(SHELL_HALF_EDGE)() {
    // frees allocated memory when m_neighbor_pair is used to store neighbor info
    if (this->nbr_he_is_pair()) {
      this->delete_nbr_pair(); 
    }
  }

// Functions related to geometric data

  // this cannot be called until local csys info has been
  // calculated for each surfel and should only be used
  // with half_edges in the simplified mesh
  VOID compute_geometric_info();

  dFLOAT length() { return m_length; }

  sdFLOAT normal_distance() { return m_normal_dist; }

  const sdFLOAT* local_normal() { return m_normal; }

  dFLOAT unscaled_length();

  sdFLOAT unscaled_normal_distance();

// Functions for tracking conduction edges assigned to the edges
  CONDUCTION_EDGE_BASE get_conduction_edge();

  VOID set_conduction_edge(CONDUCTION_EDGE_BASE cond_edge);

// Functions for determining boundary info
  asINT32 get_back_dynamics_type();

  asINT32 check_back_dynamics_data();

// Functions providing information about edge ring and surfels connected to it
  asINT32 num_shell_edges(BOOLEAN exclude_ghosts=FALSE);

  asINT32 num_faceted_edges();

  BOOLEAN only_ghosts_in_ring();

  BOOLEAN is_shell_in_ring(BOOLEAN exclude_ghosts=FALSE);

  BOOLEAN is_edge_in_ring(SHELL_HE_TYPE half_edge);

  BOOLEAN is_2d_half_edge();

  asINT32 index_in_facet();

// Functions providing easy access to surfel information
  asINT32 num_layers_at_edge();

  BOOLEAN equal_number_of_layers(SHELL_HE_TYPE half_edge) {
    return (this->num_layers_at_edge() == half_edge->num_layers_at_edge());
  }

  asINT32 surfel_face_id();

  BOOLEAN is_shell_edge(BOOLEAN exclude_ghosts=FALSE);

  BOOLEAN is_open_shell_edge();

  BOOLEAN is_closed_shell_edge();

  BOOLEAN is_esurfel_edge();

  BOOLEAN is_coupled_cond_interface_edge();

  BOOLEAN is_contact_pair_edge();

  BOOLEAN is_boundary_edge();

  BOOLEAN is_edge_faceted();

  sSURFEL* get_surfel();

// Functions for iterating through edges by surfel type
  SHELL_HE_TYPE next_shell_edge();

  SHELL_HE_TYPE next_closed_shell_surfel_edge();

  SHELL_HE_TYPE next_open_shell_surfel_edge();

  // SHELL_HE_TYPE next_coupled_edge();

  SHELL_HE_TYPE next_boundary_edge();

  SHELL_HE_TYPE next_faceted_edge();

  SHELL_HE_TYPE get_opp_in_2d_facet();

  SHELL_HE_TYPE next_2d_half_edge();

// Functions used to determine alignment and to sort by angle around edge ring
  iBREP_VERTEX get_vertex_ind_for_alignment();

  BOOLEAN are_edges_aligned(SHELL_HE_TYPE half_edge);

  BOOLEAN are_layers_aligned(SHELL_HE_TYPE half_edge);

  NT angle_btw_edges(SHELL_HE_TYPE);

  VOID get_edge_vector(HALF_EDGE_VECTOR& hes, BOOLEAN exclude_root);

  asINT32 get_sorted_edges(HALF_EDGE_VECTOR& half_edges);

// Does surfel associated with edge have opposite with edge also in ring
  BOOLEAN is_edge_in_contact_pair();

  BOOLEAN are_edge_facets_opposites(SHELL_HE_TYPE half_edge);

  SHELL_HE_TYPE get_opposite_facet_edge();

  BOOLEAN is_edge_in_0_res_contact_pair(BOOLEAN test_opp = TRUE);

// Functions for identifying/specifying edge type and finding internal edges
  SHELL_HE_TYPE get_internal_edge_nbr();
  SHELL_HE_TYPE get_internal_edge_owner();

  SHELL_HE_TYPE get_boundary_or_intersection_edge_nbr();

  VOID set_as_adiabatic_boundary_edge();
  VOID reset_all_to_adiabatic_boundary_edges();
  VOID reset_to_adiabatic_boundary_edge();
  VOID set_edge_to_ignored() {
    m_info_flags.set(HE_IGNORED);
  };

  VOID set_internal_edge_nbr(SHELL_HE_TYPE edge_nbr);

  VOID set_intersection_edge_nbr(SHELL_HE_TYPE edge_nbr, BOOLEAN set_nbr_also = TRUE);
  VOID set_intersection_edge_nbrs(HALF_EDGE_VECTOR& edge_nbrs);

  VOID set_internal_or_intersection_edge_nbr(SHELL_HE_TYPE edge_nbr);

  VOID set_dyn_data_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr);

  VOID set_fixed_temp_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr);
  VOID set_fixed_temp_boundary_edge_nbrs(SHELL_HE_TYPE nbr_he1, SHELL_HE_TYPE nbr_he2);

  VOID set_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr);
  VOID set_as_boundary_edge_nbr_type(HALF_EDGE_VECTOR& shell_nbrs, uINT32 this_edge_type = 0x0);
  // VOID set_boundary_edge_nbrs(SHELL_HE_TYPE nbr1, SHELL_HE_TYPE nbr2);

  BOOLEAN nbr_he_is_pair() {
    return m_info_flags.test(NBR_HE_IS_PAIR_IND);
  }

  VOID delete_nbr_pair();

  SHELL_HE_TYPE get_first_edge_nbr_in_pair();

  SHELL_HE_TYPE get_second_edge_nbr_in_pair();

  BOOLEAN is_internal_edge() {
    return m_info_flags.test(NBR_HE_INTERNAL_IND);
  }

  BOOLEAN is_intersection_edge() {
    return m_info_flags.test(NBR_HE_INTERSECTION_IND);
  }

  BOOLEAN is_adiabatic_boundary() {
    return m_info_flags.test(NBR_HE_ADIABATIC_BND_IND);
  }

  BOOLEAN is_fixed_temp_boundary() {
    return m_info_flags.test(NBR_HE_FIXED_TEMP_BND_IND);
  }

  BOOLEAN is_dyn_data_boundary() {
    return m_info_flags.test(NBR_HE_DYN_DATA_BND_IND);
  }

  BOOLEAN is_edge_ignored() {
    return m_info_flags.test(HE_IGNORED); 
  }

  BOOLEAN edge_nbr_is_set() {
    constexpr uint8_t nbr_type_mask = tBITSET<NUM_INFO_FLAGS,uint8_t>().set(HE_UNPROCESSED_IND).flip_all().get();
    return (m_info_flags.get() & nbr_type_mask);
  }

  BOOLEAN is_processed() { return !(m_info_flags.test(HE_UNPROCESSED_IND)); }

  VOID set_processed(BOOLEAN all_in_ring=FALSE);

  VOID set_unprocessed() { m_info_flags.set(HE_UNPROCESSED_IND); }

  SHELL_HE_TYPE half_edge_in_ring_from_surf_id(SHOB_ID surfelId);

  VOID set_ring_types_from_ghost_info(sGHOST_HALF_EDGE_RING_INFO& gInfo);

  BOOLEAN can_edges_be_combined(SHELL_HE_TYPE edge2, BOOLEAN use_edge_type_info=FALSE);

  sdFLOAT boundarySkewness();

  sdFLOAT skewness(const dFLOAT unfolded_vec[2]);



// Functions used to write info to stdout when debugging. Can be deleted after
// thorough testing completed.
  VOID Print(FILE *stream = stdout);

  VOID global_vertex_indices(DGF_VERTEX_INDEX& vInd1, DGF_VERTEX_INDEX& vInd2);

  uint8_t get_info_flags() {
    return (uint8_t)(m_info_flags.get());
  }

  SHOB_ID get_nbr_he1_surfel_id();
  SHOB_ID get_nbr_he2_surfel_id();

  VOID set_int_edge_rotation(dFLOAT int_edge_rot) {
    cassert(int_edge_rot > -1e9);
    m_int_edge_rotation = int_edge_rot;
  }

  VOID set_int_edge_unfold_vec(const dFLOAT int_edge_vec[2]) {
    cassert(int_edge_vec[0] > -1e9);
    vcopy2(m_int_edge_unfolded_vec, int_edge_vec);
  }

  dFLOAT get_int_edge_rotation(BOOLEAN skip_assert = FALSE);

  dFLOAT* get_int_edge_unfold_vec(BOOLEAN skip_assert = FALSE);

#ifdef DEBUG_BREP_STENCIL
  std::string print_detailed_ring_info(BOOLEAN skipSimple=FALSE);
  std::string print_associated_surfel_info_with_angles(NT angle, iBREP_VERTEX tail_vertex);
#endif

private:
  // These distances are scaled to the correspond surfel's VR level
  dFLOAT m_length;
  sdFLOAT m_normal_dist;

  // this is the normal direction in the corresponding surfel's local coordinate system
  sdFLOAT m_normal[2];

  CONDUCTION_EDGE_BASE m_conduction_edge;

  // Stores neighbor edge associated with conduction edge type. Having a separate variable here to store a neighbor
  // edge avoids any need to reorder the half-edges in the ring and/or split it into separate rings.
  // For internal edges, for both half edges, m_neighbor_he will point to the other half_edge assoicated with the internal edge
  // For intersection edges, m_neighbor_he will each point to another edge in the intersection cyclically so that
  //      all edges in the intersection can be reached.
  // For adiabatic edges, this will be left blank
  // For dynamic data BC edges, m_neighbor_he will point to the half_edge of the surfel with dynamics data that
  //         defines the boundary condition
  // For fixed temp BCs with only one surfel defining the temperature, m_neighbor_he will also point to the half_edge
  //          of the surfel whose temperature will define the BC
  // For fixed temp BCs with two surfels defining the temperature: a pair containing the pointers to the two half_edges
  //          of the surfels defining the temp BC will be allocated and stored in m_neighbor_pair. In this case, 
  //          the flag at NBR_HE_IS_PAIR_IND in m_info_flags will be set to TRUE and will require that the
  //          allocated memory be deleted once no longer needed.
  //          This is done instead of creating circular between the surfel with layers' half_edge and the two boundary
  //          surfel half_edges, because there is a possibility that when the boundary surfels are closed edges with their
  //          own layers (when abutted by an open shell), they could be part of an intersection which includes
  //          different half_edges.
  union {
    SHELL_HE_TYPE m_neighbor_he; // for sending to a new SP
    FIXED_TEMP_NBR_PAIR* m_neighbor_pair;
  };

  dFLOAT m_int_edge_rotation;
  dFLOAT m_int_edge_unfolded_vec[2];

  // Stores information about the identified edge type
  tBITSET<NUM_INFO_FLAGS,uint8_t> m_info_flags;
};


/**
 @brief BREP object for entire shell surfel mesh

 @details provides methods to add surfels to the BREP mesh as well as adding surfels constructed from
          a subset of the original surfel vertices which excludes all unnecessary ones (creating co-linear
          edges all containing the same surfels in the ring).
 */
DEFINE_BREP_TEMPLATE(SHELL_MESH, BREP_DEFAULT_TEMPLATE) {
  BREP_BASIC_DEFS;

  typedef BREP_TEMPLATE(BREP_DEFAULT_TEMPLATE) cBASE;
  typedef BREP_TEMPLATE(SHELL_MESH) cSELF;
  typedef BREP_TEMPLATE_TYPENAME(SHELL_MESH)::BODY BODY_TYPE;
  typedef BREP_TEMPLATE_TYPENAME(SHELL_MESH)::SHELL SHELL_TYPE;
  typedef BREP_TEMPLATE_TYPENAME(SHELL_MESH)::FACET FACET_TYPE;
  typedef BREP_TEMPLATE_TYPENAME(SHELL_MESH)::VERTEX VERTEX_TYPE;
  typedef BREP_TEMPLATE_TYPENAME(SHELL_MESH)::HALF_EDGE HALF_EDGE_TYPE;

public:

  BREP_TYPE(SHELL_MESH)() : cBASE() {
    m_vertex_index_offset = 0;
    add_vertices();
  }

  ~BREP_TYPE(SHELL_MESH)() {}

  VOID add_vertices();

  VOID add_faces();

  VOID add_surfel(sSURFEL* surfel);

  VOID add_surfel(sSURFEL* surfel, std::vector<iBREP_VERTEX>& vertex_inds, iBREP_SHELL shell_index);

  asINT32 vertex_offset_2d() {
    return m_vertex_index_offset;
  }

  VOID identify_facet_edge_types();

  // VOID verify_facet_stencil(FACET_TYPE facet, std::vector<HALF_EDGE_TYPE>& edges);

  VOID dump_all_facet_info();

  VOID dump_edge_ring_info();

  FACET_TYPE get_facet_from_surfel_id(uINT32 sId) {
    return m_surfel_to_facet_map[sId];
  }

  VOID initialize_ghost_half_edge_info_vec(asINT32 nsps) {
    m_ghost_half_edge_sps.resize(nsps);
  }

  VOID perform_dummy_comms();

  std::vector<HALF_EDGE_TYPE>& get_ghost_half_edge_info_for_sp(asINT32 spNum) {
    return m_ghost_half_edge_sps[spNum];
  }

  VOID get_ghost_half_edge_ids(asINT32& num_ghost_half_edges_total,
                               asINT32& num_csp_being_sent_ghost_ids,
                               std::vector<asINT32>& num_ghost_ids_to_send_to_csp,
                               std::vector<std::vector<uINT32>>& ghost_ids_to_send_to_csp,
                               asINT32& num_csp_recv_ghost_ids_from,
                               std::vector<asINT32>& num_ghost_ids_to_recv_from_csp,
                               std::vector<std::vector<uINT32>>& ghost_ids_recv_from_csp);

  VOID comm_ghost_half_edge_ring_info();

  VOID comm_ghost_int_edge_geom_info();

private:
  std::vector<std::vector<HALF_EDGE_TYPE>> m_ghost_half_edge_sps;

  asINT32 m_vertex_index_offset;
  std::unordered_map<uINT32,FACET_TYPE> m_surfel_to_facet_map;
};


// Create cSHELL_MESH brep object
DEFINE_BREP_CLASS(cSHELL_MESH, SHELL_MESH, dFLOAT,
                  BREP_DEFAULT_BODY,
                  BREP_DEFAULT_SHELL,
                  SHELL_FACET,
                  SHELL_HALF_EDGE,
                  BREP_DEFAULT_VERTEX);

struct sGHOST_INT_EDGE_GEOM_INFO {
  struct sGHOST_INT_EDGE_GEOM_INFO_SEND_FIELD {
    SHOB_ID m_ghost_surfel_id;
    asINT32 m_half_edge_in_facet;
    dFLOAT m_rotation;
    dFLOAT m_unfolded_centroid_vec[2];
  };

  sGHOST_INT_EDGE_GEOM_INFO(uINT32 surfelId, uINT32 half_edge_index);

  sGHOST_INT_EDGE_GEOM_INFO() {}

  VOID add_send_size(asINT32 &tot_send_size) {
    tot_send_size += send_size();
  }

  asINT32 send_size() {
    return sizeof(sGHOST_INT_EDGE_GEOM_INFO_SEND_FIELD) / sizeof(sFLOAT);
  }

  VOID fill_send_buffer(sFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sGHOST_INT_EDGE_GEOM_INFO_SEND_FIELD*>(send_buffer);
    memcpy(&(field->m_ghost_surfel_id),          &ghost_surfel_id,       sizeof(field->m_ghost_surfel_id));
    memcpy(&(field->m_half_edge_in_facet),       &half_edge_in_facet,    sizeof(field->m_half_edge_in_facet));
    memcpy(&(field->m_rotation),                 &rotation,              sizeof(field->m_rotation));
    memcpy(field->m_unfolded_centroid_vec,       &unfolded_centroid_vec, sizeof(field->m_unfolded_centroid_vec));
    field++;
    send_buffer = reinterpret_cast<sFLOAT*>(field);
  }

  VOID expand_recv_buffer(sFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sGHOST_INT_EDGE_GEOM_INFO_SEND_FIELD*>(recv_buffer);
    memcpy(&ghost_surfel_id,       &(field->m_ghost_surfel_id),      sizeof(field->m_ghost_surfel_id));
    memcpy(&half_edge_in_facet,    &(field->m_half_edge_in_facet),   sizeof(field->m_half_edge_in_facet));
    memcpy(&rotation,              &(field->m_rotation),             sizeof(field->m_rotation));
    memcpy(&unfolded_centroid_vec, field->m_unfolded_centroid_vec,   sizeof(field->m_unfolded_centroid_vec));
    field++;
    recv_buffer = reinterpret_cast<sFLOAT*>(field);
  }

  SHOB_ID ghost_surfel_id;
  asINT32 half_edge_in_facet;
  dFLOAT rotation;
  dFLOAT unfolded_centroid_vec[2];
};


struct sGHOST_HALF_EDGE_RING_INFO {
  
  struct sRING_INFO_ELEMENT {
    struct sRING_INFO_ELEMENT_SEND_FIELD {
      uint8_t m_info_flags;
      SHOB_ID m_half_edge_surfel_id;
      SHOB_ID m_nbr_surfel_id_1;
      SHOB_ID m_nbr_surfel_id_2;
    };

    VOID add_send_size(asINT32 &tot_send_size) {
      tot_send_size += send_size();
    }
  
    sRING_INFO_ELEMENT(cSHELL_MESH::HALF_EDGE half_edge);
  
    sRING_INFO_ELEMENT() : info_flags(0x0), half_edge_surfel_id(STP_INVALID_SHOB_ID),
        nbr_surfel_id_1(STP_INVALID_SHOB_ID), nbr_surfel_id_2(STP_INVALID_SHOB_ID) {}
  
    asINT32 send_size() {
      return sizeof(sRING_INFO_ELEMENT_SEND_FIELD) / sizeof(sFLOAT);
    }
  
    VOID fill_send_buffer(sFLOAT* &send_buffer) {
      auto field = reinterpret_cast<sRING_INFO_ELEMENT_SEND_FIELD*>(send_buffer);
      memcpy(&(field->m_info_flags),          &info_flags,          sizeof(field->m_info_flags));
      memcpy(&(field->m_half_edge_surfel_id), &half_edge_surfel_id, sizeof(field->m_half_edge_surfel_id));
      memcpy(&(field->m_nbr_surfel_id_1),     &nbr_surfel_id_1,     sizeof(field->m_nbr_surfel_id_1));
      memcpy(&(field->m_nbr_surfel_id_2),     &nbr_surfel_id_2,     sizeof(field->m_nbr_surfel_id_2));
      field++;
      send_buffer = reinterpret_cast<sFLOAT*>(field);
    }
  
    VOID expand_recv_buffer(sFLOAT* &recv_buffer) {
      auto field = reinterpret_cast<sRING_INFO_ELEMENT_SEND_FIELD*>(recv_buffer);
      memcpy(&info_flags,           &(field->m_info_flags),           sizeof(field->m_info_flags));
      memcpy(&half_edge_surfel_id,  &(field->m_half_edge_surfel_id),  sizeof(field->m_half_edge_surfel_id));
      memcpy(&nbr_surfel_id_1,      &(field->m_nbr_surfel_id_1),      sizeof(field->m_nbr_surfel_id_1));
      memcpy(&nbr_surfel_id_2,      &(field->m_nbr_surfel_id_2),      sizeof(field->m_nbr_surfel_id_2));
      field++;
      recv_buffer = reinterpret_cast<sFLOAT*>(field);
    }

    uint8_t info_flags;
    SHOB_ID half_edge_surfel_id;
    SHOB_ID nbr_surfel_id_1;
    SHOB_ID nbr_surfel_id_2;
  };

  struct sGHOST_HALF_EDGE_RING_INFO_SEND_FIELD {
    SHOB_ID m_ghost_surfel_id;
    asINT32 m_half_edge_in_facet;
    asINT32 m_num_edges_in_ring;
  };

  sGHOST_HALF_EDGE_RING_INFO(uINT32 surfelId, uINT32 half_edge_index);

  sGHOST_HALF_EDGE_RING_INFO() {}

  VOID add_send_size(asINT32 &tot_send_size) {
    tot_send_size += send_size();
  }

  asINT32 send_size() {
    asINT32 this_send_size = sizeof(sGHOST_HALF_EDGE_RING_INFO_SEND_FIELD) / sizeof(sFLOAT);
    for (auto& he_info : half_edge_info) {
      he_info.add_send_size(this_send_size);
    }
    return this_send_size;
  }

  VOID fill_send_buffer(sFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sGHOST_HALF_EDGE_RING_INFO_SEND_FIELD*>(send_buffer);
    memcpy(&(field->m_ghost_surfel_id),          &ghost_surfel_id,      sizeof(field->m_ghost_surfel_id));
    memcpy(&(field->m_half_edge_in_facet),       &half_edge_in_facet,   sizeof(field->m_half_edge_in_facet));
    asINT32 num_edges_in_ring = half_edge_info.size();
    memcpy(&(field->m_num_edges_in_ring),       &num_edges_in_ring,     sizeof(field->m_num_edges_in_ring));
    field++;
    send_buffer = reinterpret_cast<sFLOAT*>(field);
    for (auto& he_info : half_edge_info) {
      he_info.fill_send_buffer(send_buffer);
    }
  }

  VOID expand_recv_buffer(sFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sGHOST_HALF_EDGE_RING_INFO_SEND_FIELD*>(recv_buffer);
    memcpy(&ghost_surfel_id,      &(field->m_ghost_surfel_id),      sizeof(field->m_ghost_surfel_id));
    memcpy(&half_edge_in_facet,   &(field->m_half_edge_in_facet),   sizeof(field->m_half_edge_in_facet));
    asINT32 num_edges_in_ring;
    memcpy(&num_edges_in_ring,    &(field->m_num_edges_in_ring),    sizeof(field->m_num_edges_in_ring));
    field++;
    recv_buffer = reinterpret_cast<sFLOAT*>(field);
    half_edge_info.resize(num_edges_in_ring);
    for (auto& he_info : half_edge_info) {
      he_info.expand_recv_buffer(recv_buffer);
    }
  }

  SHOB_ID ghost_surfel_id;
  asINT32 half_edge_in_facet;
  std::vector<sRING_INFO_ELEMENT> half_edge_info;
};

enum MALFORMED_EDGE_TYPES : uINT32 {
  NO_MALFORMATON = 0,
  PAIR_UNALIGNED_EDGES,
  SET_UNALIGNED_EDGES,
  INVALID_EDGE_TYPE,
  UNMATCHED_CLOSED_SHELL_EDGE
};

/**
 @brief Class to store a pair of half_edges associated with abutting surfels

 @details Stores two half edges associated with abutting surfels (i.e. aligned surfels associated with the same solid).
          This class is used by cEDGE_TYPE_IDENTIFIER to identify the edge types of each half_edge in an edge ring.
          
          Additionally, the m_prev_pair and m_next_pair variables can store pointers to the neighboring cHALF_EDGE_PAIRs
          (in the CCW and CW directions, respectively) if they meet the necessary conditions to be connected.

          The cOPEN_SHELL_SET class below derives from this class.
 */
typedef class cHALF_EDGE_PAIR {
public:
  typedef cSHELL_MESH::HALF_EDGE SHELL_EDGE;
  typedef std::vector<SHELL_EDGE> SHELL_EDGE_VECTOR;

  cHALF_EDGE_PAIR(SHELL_EDGE he1, SHELL_EDGE he2); 
  cHALF_EDGE_PAIR(SHELL_EDGE he1);

  void set_vector_index(asINT32 ind) { m_vector_index = ind; }

  asINT32 vector_index();
  
  virtual BOOLEAN contains_layers() {
    return (m_he1->is_shell_edge() || (m_he2 && m_he2->is_shell_edge()));
  }

  VOID add_closing_edge(SHELL_EDGE he2);

  virtual uINT32 check_if_malformed();

  virtual VOID add_open_shell(SHELL_EDGE openShell);

  virtual SHELL_EDGE open_shell(asINT32 index);

  virtual BOOLEAN is_open_shell_set() { return FALSE; }

  virtual VOID process_single_pair_or_set();

  virtual VOID add_surfel_with_layer_edges_to_vec(SHELL_EDGE_VECTOR& layer_he_vec);

  // This should only be called from cEDGE_TYPE_IDENTIFIER::check_for_simple_configs
  VOID set_includes_entire_ring_flag() {
    m_includes_entire_ring = TRUE;
  }
  
  SHELL_EDGE edge1() { return m_he1; }
  
  SHELL_EDGE edge2() { return m_he2; }
  
  virtual BOOLEAN check_connect_next_pair(cHALF_EDGE_PAIR* next_pair_tmp);

  VOID check_and_add_next_pair(cHALF_EDGE_PAIR* next_pair_tmp, BOOLEAN skip_connection_check = FALSE);

  cHALF_EDGE_PAIR* next_pair() { return m_next_pair; }
  
  BOOLEAN next_pair_connected() { return (m_next_pair != NULL); }
  
  virtual BOOLEAN check_connect_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp);

  VOID check_and_add_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp, BOOLEAN skip_connection_check = FALSE);

  cHALF_EDGE_PAIR* prev_pair() { return m_prev_pair; }
  
  BOOLEAN prev_pair_connected() { return (m_prev_pair != NULL); }
  
  BOOLEAN connected_both_dir() {
    return (this->next_pair_connected() && this->prev_pair_connected());
  }
  
  BOOLEAN is_group_circular();
  
protected:
  // Halfedges associated with abutting surfels
  SHELL_EDGE m_he1, m_he2;
  // Connected pair in CW direction (if it exists)
  cHALF_EDGE_PAIR* m_next_pair;
  // Connected pair in CCW direction (if it exists)
  cHALF_EDGE_PAIR* m_prev_pair;
  // Index of pair in cEDGE_TYPE_IDENTIFIER::m_edge_pairs
  asINT32 m_vector_index;
  // Flag used when processing OPEN_SHELL_SET to indicate that it should create a separate HALF_EDGE_PAIR for the
  // closed shell surfels and process them separately if necessary 
  BOOLEAN m_includes_entire_ring;
} *HALF_EDGE_PAIR;
  
    
/**
 @brief Class to store one or more open shell surfels abutting a pair surfels representing a surface.

 @details m_he1 and m_he2 in this class store the half-edges of the surface surfels. Open shell surfels are stored
          in m_he_vec. Remainder of functions are related to adding/accessing open shell edges, identifying that
          the object is an open shell set, adding the half-edge associated with the 2nd surface surfel.

          Open shell surfels are stored in CW order.

          Because only conduction surfels are stored in the BREP, an edge ring containing open shell surfels with
          layers in contact with more than 2 closed shell surfels (with or without layers), the half_edges in m_he1
          and m_he2 (which are coupled conduction surfels) are the same as those in the surrounding cHALF_EDGE_PAIR
          objects.
 */
typedef class cOPEN_SHELL_SET : public cHALF_EDGE_PAIR {
public:
  
  cOPEN_SHELL_SET(SHELL_EDGE he1) : cHALF_EDGE_PAIR(he1) {}
  
  virtual uINT32 check_if_malformed() override;

  virtual BOOLEAN contains_layers() override { return m_he_vec.size(); }
  
  virtual BOOLEAN is_open_shell_set() override { return TRUE; }
  
  // virtual VOID add_closing_edge(SHELL_EDGE he2) override;
  
  virtual VOID add_open_shell(SHELL_EDGE openShell) override;

  virtual VOID process_single_pair_or_set() override;

  virtual VOID add_surfel_with_layer_edges_to_vec(SHELL_EDGE_VECTOR& layer_he_vec) override;

  virtual BOOLEAN check_connect_next_pair(cHALF_EDGE_PAIR* next_pair_tmp) override;

  virtual BOOLEAN check_connect_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp) override;

/**
  @brief Returns open shell half_edge at index of m_he_vec.
*/
  virtual SHELL_EDGE open_shell(asINT32 index) override {
    return m_he_vec.at(index);
  }

  SHELL_EDGE_VECTOR& open_shell_vec() {
    return m_he_vec;
  }

  BOOLEAN closed_shells_contain_layers();
  
  BOOLEAN process_set_with_closed_shell_surfel_layers();

  VOID get_closed_shell_surfel_boundary_types(uINT32& edge_type1, uINT32& edge_type2);

  protected:
  SHELL_EDGE_VECTOR m_he_vec;
} *OPEN_SHELL_SET;
  
/**
 @brief Class implementing edge identification algorithm.

 @details This class will identify the edge types for all shell surfels in the edge ring associated with the provided
          half_edge. It does so by sorting the half_edges by position around the ring and splitting each pair of close
          shell surfel edges and open shell surfel sets into cHALF_EDGE_PAIR and cOPEN_SHELL_SET objects. Once
          split, the algorithm identifies which pairs/sets should be connected and identifies the edge type for the
          resulting connected and/or individual pairs/sets in the ring. Once identified, the neighbor and type information
          is stored in each half_edge for use in the final stencil building.

          Before splitting into pair/sets, the class will check for certain configurations that would either cause a
          failure in the algorithm (i.e. only one half-edge associated with a surfel in the ring), or the algorithm
          will fail to identify properly. This is performed in the check_for_simple_configs function.

          If any other specific configurations are not identified correctly by the algorithm, but can be identified
          based on the number and type of half-edges in the ring (but not positioning/order) they can be added to
          the check_for_simple_configs function.
 */
class cEDGE_TYPE_IDENTIFIER {
public:
  typedef cSHELL_MESH::HALF_EDGE SHELL_EDGE;
  typedef std::vector<SHELL_EDGE> SHELL_EDGE_VECTOR;
  typedef std::vector<HALF_EDGE_PAIR> EDGE_PAIR_VECTOR;

  cEDGE_TYPE_IDENTIFIER(SHELL_EDGE root_edge);

  ~cEDGE_TYPE_IDENTIFIER() {
    ccDOTIMES(i, m_edge_pairs.size()) {
      delete m_edge_pairs[i];
    }
  }

  // Provides cyclical indexing of increasing value for m_half_edges
  asINT32 nextIndex(asINT32 cur_ind) {
    return (cur_ind+1)%m_half_edges.size();
  }

  // Provides cyclical indexing of decreasing value for m_half_edges
  asINT32 prevIndex(asINT32 cur_ind) {
    return (cur_ind == 0) ? (m_half_edges.size() - 1) : (cur_ind - 1);
  }

  BOOLEAN check_for_simple_configs();

  BOOLEAN build_edge_pairs();

  VOID group_pairs();

  VOID process_pair_groups();

  asINT32 process_pair_group(asINT32 cur_ind, BOOLEAN single_group = FALSE);

  VOID process(BOOLEAN root_is_ghost);

  VOID get_malformed_string(std::string& str) {
    switch (m_malformed_error) {
      case (MALFORMED_EDGE_TYPES::PAIR_UNALIGNED_EDGES): {
        str = "Misaligned edges between neighboring surfels";
        break;
      }
      case (MALFORMED_EDGE_TYPES::SET_UNALIGNED_EDGES): {
        str = "Open shell surfel sharing edge with misaligned surfels";
        break;
      }
      case (MALFORMED_EDGE_TYPES::INVALID_EDGE_TYPE): {
        str = "Surfel associated with edge contains invalid dynamics type";
        break;
      }
      case (MALFORMED_EDGE_TYPES::UNMATCHED_CLOSED_SHELL_EDGE): {
        str = "Odd number of closed shell surfels share edge";
        break;
      }
      default: {
        str = "";
        break;
      }
    }
  }
  
private:
  uINT32 m_malformed_error;
  cSHELL_MESH::HALF_EDGE m_root_edge;
  SHELL_EDGE_VECTOR m_half_edges;
  EDGE_PAIR_VECTOR m_edge_pairs;
  // Index of root_edge provided to constructor in m_half_edges
  // The orientation of the root_edge defines the CCW and CW directions
  asINT32 m_root_index;
};
  

// For iterating through the 2 edges on either side of a 2D facet in 2D simulations.
#define BREP_FACET_DO_2D_HALF_EDGES(half_edge, facet, brep_type)          \
brep_type::HALF_EDGE half_edge = (facet)->get_2d_half_edge();             \
brep_type::HALF_EDGE ___(n_he) = half_edge->next_2d_half_edge();          \
brep_type::HALF_EDGE ___(f_he) = half_edge;				                        \
BOOLEAN ___(flag) = TRUE;                                                 \
for ( ; ___(flag) != FALSE;                                               \
      ___(flag) = (___(n_he) != ___(f_he) ? TRUE : FALSE),                \
      half_edge = ___(n_he), ___(n_he) = ___(n_he)->next_2d_half_edge())

      
#endif