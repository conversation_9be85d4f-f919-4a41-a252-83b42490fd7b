/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_emitters.h"
#include "particle_sim.h"
#include "particle_sim_info.h"
#include "trajectory_meas.h"

extern sPARTICLE_SIM particle_sim;
extern EXPRLANG_PROGRAM case_program;
extern sSP_TRAJECTORY_STARTPOINT_MANAGER sp_trajectory_startpoint_manager;

asINT32 sPARTICLE_EMITTER_BASE::m_parcel_id_counter = 1;

sEMISSION_DIRECTION::sEMISSION_DIRECTION(std::vector<std::string> &direction_variable_names,
                                         dFLOAT direction_coords[3],
                                         dFLOAT major_ellipse_direction[3],
                                         BOOLEAN elliptical_nozzle,
                                         asINT32 direction_csys_id,
                                         BOOLEAN needs_surface_normal) {

  vcopy(m_direction_variable_names, direction_variable_names);
  m_needs_surface_normal = needs_surface_normal;

  if(!m_needs_surface_normal) {
    dFLOAT local_direction[3];
    vcopy(local_direction, direction_coords);
    vunitize(local_direction);
    xform_vector(local_direction, &sim.csys_table[direction_csys_id]);
    vcopy(m_direction.vcoord, local_direction);
  } else {
    vzero(m_direction.vcoord);
  }

  if(elliptical_nozzle) {
    dFLOAT major_dir[3];
    m_elliptical_nozzle = TRUE;
    vcopy(major_dir, major_ellipse_direction);
    vunitize(major_dir);
    xform_vector(major_dir, &sim.csys_table[direction_csys_id]);
    vcopy(m_major_ellipse_direction.vcoord, major_dir);
  } else {
    m_elliptical_nozzle = FALSE;
    vzero(m_major_ellipse_direction.vcoord);
  }

  //If the emission direction is the same for all positions on the surface emitter, compute it once here.
  if(!m_needs_surface_normal)
    compute_csys_transformations();
}

VOID sEMISSION_DIRECTION::transform_velocity_from_cone_to_lattic_csys(sPARTICLE_VAR velocity_direction[3]){
  sG3_VEC velocity_in_cone_csys;
  vcopy(velocity_in_cone_csys.vcoord, velocity_direction);
  sG3_VEC transformed_velocity = g3_xform_vec(velocity_in_cone_csys, m_csys.l_to_g_xform);
  vcopy(velocity_direction, transformed_velocity.vcoord);
}

VOID sEMISSION_DIRECTION::compute_csys_transformations(){
  //The initial direction of particles from a nozzle are first computed in a
  //csys aligned to the major axis of the cone.  The direction is then transformed
  //into the lattice coordinate system.  This function initializes the transformation
  //matrix needed to perform this transformation

  if(m_elliptical_nozzle) {
    if(!construct_rotation_xform(m_csys, m_direction, m_major_ellipse_direction))
      msg_error("Major ellipse direction is parallel to mean emission direction.");
  }
  else
    construct_rotation_xform(m_csys, m_direction);
}


BOOLEAN construct_rotation_xform(sCSYS &m_csys, const sG3_VEC &m_direction)
{
  //Compute the transformation matrix that alignes the spray cone major axis with the major spray direction
  sG3_VEC sprayer_major_axis_in_sprayer_csys = {1.0 , 0.0, 0.0};  //By Convenction, e_1 is defined as the major axis of a spray cone
  //For an elliptical cone, e_2 is assuemd by convention to be the major axis of the ellipse.
  sG3_VEC Omega = g3_vec_cross(sprayer_major_axis_in_sprayer_csys, m_direction);

  sPARTICLE_VAR sin_omega = std::sqrt(g3_vec_dot(Omega, Omega));

  if(sin_omega > 1.0e-6)
    {
      //construct csys
      //sprayer_major_axis_in_sprayer_csys: x
      //Omega: z
      //yaxis: y

      Omega = g3_vec_divide(Omega , sin_omega); //Omega is now the unitized axis of rotation to rotate e_1 into the sprayer direction
      sG3_VEC yaxis = g3_vec_cross(Omega, sprayer_major_axis_in_sprayer_csys);

      sPARTICLE_VAR x = g3_vec_dot(m_direction, sprayer_major_axis_in_sprayer_csys);
      sPARTICLE_VAR y = g3_vec_dot(m_direction, yaxis);

      sPARTICLE_VAR omega = std::atan2(y, x); //is the number of radians between e_1 and the spray direction

      m_csys.l_to_g_xform = g3_xform_make_rotate(Omega, omega);
      m_csys.g_to_l_xform = g3_xform_transpose_rotation_matrix(m_csys.l_to_g_xform); //Because the inverse of the an orthonormal matrix is its transpose.
    }
  else
    {
      m_csys.l_to_g_xform = g3_xform_make_unit();
      m_csys.g_to_l_xform = g3_xform_make_unit();

      //parallel to (1, 0, 0) check if it is the reverse
      if(m_direction.vcoord[0] < 0.0)
        {
          m_csys.l_to_g_xform.xcoord[0][0] = -1.0;
          m_csys.g_to_l_xform.xcoord[0][0] = -1.0;
        }
    }
  return(TRUE);
}

//construct m_csys s.t.
//(1,0,0)l_to_g = e0, (0,1,0)l_to_g = e1, (0,0,1)l_to_g = e2
//note: m_dir0 and m_dir1 must be in lattice or default csys,
//m_dir0 is the mean emission direction, m_dir1 is the major ellipse direction
BOOLEAN construct_rotation_xform(sCSYS &m_csys, const sG3_VEC &m_dir0, const sG3_VEC &m_dir1)
{
  sG3_VEC e0 = m_dir0, e1, e2 = g3_vec_cross(m_dir0, m_dir1);

  sPARTICLE_VAR sin_omega = std::sqrt(g3_vec_dot(e2, e2));

  if(sin_omega > 1.0e-6) {
    e2 = g3_vec_divide(e2, sin_omega);
    e1 = g3_vec_cross(e2, e0);

    vcopy(m_csys.l_to_g_xform.xcoord[0], e0.vcoord);
    vcopy(m_csys.l_to_g_xform.xcoord[1], e1.vcoord);
    vcopy(m_csys.l_to_g_xform.xcoord[2], e2.vcoord);
    vzero(m_csys.l_to_g_xform.xcoord[3]);

    m_csys.g_to_l_xform = g3_xform_transpose_rotation_matrix(m_csys.l_to_g_xform); //because l_to_g is orthonormal
    return(TRUE);
  }

  //PR42413 - throw simerrs instead of halting when this happens, which will generally be for surface
  //emitters using the surface norm for the spray direction.  Use sim errors, becasue it may only be
  //a small section of the emitter surface that is degenerate.
  //msg_error("major ellipse direction is parallel to mean emission direction");
  return(FALSE);
}

asINT32 sPARTICLE_EMITTER_BASE::m_num_total_emitters = 0;
std::vector<asINT32> sPARTICLE_EMITTER_BASE::m_sim_to_cdi_emitter_id;

sPARTICLE_EMITTER_BASE::sPARTICLE_EMITTER_BASE(sLGI_EMITTER_BASE &lgi_record,
                                               asINT32 cdi_id,
                                               std::string &name,
                                               EMISSION_GEOMETRY emission_geometry,
                                               sPARTICLE_VAR emission_fraction,
                                               BOOLEAN fixed_release_points)
 {
  m_cdi_id = cdi_id;
  m_id = m_num_total_emitters++;
  m_sim_to_cdi_emitter_id.push_back(cdi_id);
  m_name = name;
  m_emission_geometry = emission_geometry;
  m_particles_per_parcel = lgi_record.particles_per_parcel;
  m_type = UNDEFINED_EMITTER_TYPE;
  m_is_rain_emitter = FALSE;
  m_fixed_release_points = fixed_release_points;

  m_subject_to_dispersion_box = lgi_record.subject_to_dispersion_box;
  m_subject_to_gravity = lgi_record.subject_to_gravity;

  m_start_time = lgi_record.start_time;
  if (g_override_emitter_on_time >= 0) {
    m_start_time = g_override_emitter_on_time;
  }
  m_end_time = lgi_record.end_time;
  if (g_override_emitter_off_time >= 0) {
    m_end_time = g_override_emitter_off_time;
  }
  m_duration = lgi_record.duration; //needs CP changes from registry 23395.
  m_emission_fraction = emission_fraction;
  m_max_allowed_particle_age = lgi_record.max_age;
  m_max_allowed_reflections = lgi_record.max_num_reflections;
  m_min_allowed_particle_velocity = lgi_record.min_particle_velocity;
  //m_fraction_eligible_for_measurement = lgi_record.fraction_eligible_for_measurement;
  //The following was changed from the above for PR38896.  The following quantity now evaluates to the fraction of parcels
  //eligible for measurement and the users request of fraction of particles eligible is respected.
  //m_fraction_eligible_for_measurement = lgi_record.fraction_eligible_for_measurement / (sPARTICLE_VAR)m_particles_per_parcel; //Removed on 2/28/17 to support CDI 3.2
  m_show_decorations = lgi_record.show_decorations;

  m_emission_deficit = 0;


}

VOID sPARTICLE_EMITTER_BASE::set_start_time(TIMESTEP start)
{
  m_start_time = start;
  if (m_duration > 0) {  // m_duration = 0 if the end time is via EndTime
    m_end_time = m_start_time + m_duration;
  }
}

sTIRE_EMITTER::sTIRE_EMITTER(sLGI_TIRE_EMITTER_REC &lgi_record,
                             asINT32 cdi_id,
                             std::string &name,
                             EMISSION_CYLINDER emission_cylinder,
                             sPARTICLE_VAR emission_fraction,
                             PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  sPARTICLE_EMITTER_BASE(lgi_record,
                         cdi_id,
                         name,
                         emission_cylinder,
                         emission_fraction,
                         FALSE)
{
  this->m_type = sPARTICLE_EMITTER_BASE::TIRE_EMITTER_TYPE;
  m_emitter_configuration = (sTIRE_EMITTER_CONFIGURATION*)emitter_configuration;
  vcopy(m_zero_angle_direction, lgi_record.zero_angle_direction);
  vunitize(m_zero_angle_direction);

  vcopy(m_rotation_axis, lgi_record.rotation_axis);
  vunitize(m_rotation_axis);

  m_zero_angle_csys_id = lgi_record.zero_angle_csys_index;
}


dFLOAT sPARTICLE_EMITTER_BASE::particle_emission_rate() {

  sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER simutils_helper(this->emitter_configuration()->simutils_emission_rate_type(),
                               this->emitter_configuration()->emission_rate(),
                               convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->diameter_distribution()),
                               this->emitter_configuration()->diameter_distribution_param1(),
                               this->emitter_configuration()->diameter_distribution_param2(),
                               this->emitter_configuration()->diameter_min(),
                               this->emitter_configuration()->diameter_max(),
                               convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->material()->density_distribution()),
                               this->emitter_configuration()->material()->density_distribution_param1(),
                               this->emitter_configuration()->material()->density_distribution_param2());

  return simutils_helper.particles_per_timestep();
}

dFLOAT sTIRE_EMITTER::particle_emission_rate() {
  sPARTICLE_VAR frequency_release_rate = 0;
  eEMISSION_RATE_TYPE emission_rate_type = m_emitter_configuration->emission_rate_type();

  switch(emission_rate_type) {
  case LGI::EMISSION_RATE_PARTICLE:
    frequency_release_rate = emission_rate();
    break;
  case LGI::EMISSION_RATE_MASSFLOW:
    frequency_release_rate = emission_rate()
      / expected_particle_volume() / expected_material_density();
    break;
  case LGI::EMISSION_RATE_VOLUMETRIC:
    frequency_release_rate = emission_rate() / expected_particle_volume();
    break;
  default:
    msg_error("Unrecognized release rate type found for tire emitter %s.", this->name().c_str());
  }

#define MAJOR_VERSION_OF_CDI_CHANGE 4
#define MINOR_VERSION_OF_CDI_CHANGE 19
  if((sim.cdi_major_version > MAJOR_VERSION_OF_CDI_CHANGE) ||
     ((sim.cdi_major_version == MAJOR_VERSION_OF_CDI_CHANGE) && (sim.cdi_minor_version >= MINOR_VERSION_OF_CDI_CHANGE))) {
    EMISSION_CYLINDER cylinder = (EMISSION_CYLINDER)m_emission_geometry;
    sPARTICLE_VAR tire_width = cylinder->length();
    return frequency_release_rate * tire_width;  //if the CDI file is new, PR38377 is fixed and the release rate is per tire width
  } else {
    return frequency_release_rate;
  }
#undef MAJOR_VERSION_OF_CDI_CHANGE
#undef MINOR_VERSION_OF_CDI_CHANGE
}

template <typename sEMITTER_CONFIG_TYPE>
dFLOAT tRAIN_EMITTER_BASE<sEMITTER_CONFIG_TYPE>::particle_emission_rate() { //Overridden for rain emitters
  return(m_rain_emission_rate); //This value was modified from the CDI at initialization (using SIMUTILS)
                                //to take into account the local air density and wind speed when using a depth rate.
}




VOID sPARTICLE_EMITTER_BASE::write_ckpt() {
  LGI_CKPT_PARTICLE_EMITTER_REC record;
  record.parcel_id_counter = m_parcel_id_counter;
  write_ckpt_lgi(record);
}

VOID sPARTICLE_EMITTER_BASE::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff) {
  pio_ckpt_buff.write(&m_parcel_id_counter);
}

template < typename sEMITTER_CONFIG_TYPE>
BOOLEAN tSURFACE_EMITTER_BASE<sEMITTER_CONFIG_TYPE>::pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) {

  //The velocity magnitude and spray direction are assumed to be statistically independend in this spray model.
  //So we pick a direction in the spray cone here and store it in the "velocity" variable.
  //The magnitude of this velocity is adjusted later according to the velocity magnitude statistics.
  this->m_emitter_configuration->sample_spray_direction(velocity);
  //The above sample is computed in a csys realtive to the axes of the sprayer cone.

  //Next, the direction is transformed according to the orientation of the sprayer cone here.
  if(m_emit_towards_surface_normal) {
    sCSYS m_csys;
    sG3_VEC *normal = (sG3_VEC*) this->m_emission_geometry->return_sample_position_info();

    if(normal == NULL) {
      m_emission_direction.transform_velocity_from_cone_to_lattic_csys(velocity);
    } else {


      if(m_emission_direction.m_elliptical_nozzle) {
        if(!construct_rotation_xform(m_csys, *normal, m_emission_direction.m_major_ellipse_direction))  {
          //If the major ellipse axis is parallel to the surface norm, don't emitt anything
          return(FALSE);
        }
      } else {
        construct_rotation_xform(m_csys, *normal);
      }


      sG3_VEC velocity_in_cone_csys;
      vcopy(velocity_in_cone_csys.vcoord, velocity);
      sG3_VEC transformed_velocity = g3_xform_vec(velocity_in_cone_csys, m_csys.l_to_g_xform);
      vcopy(velocity, transformed_velocity.vcoord);
    }
  } else {
    m_emission_direction.transform_velocity_from_cone_to_lattic_csys(velocity);
  }

  sPARTICLE_VAR velocity_magnitude = this->m_emitter_configuration->sample_spray_velocity_magnitude();
  vmul(velocity, velocity_magnitude);
  return(TRUE);
}

template <typename sEMITTER_CONFIG_TYPE>
tVOLUME_EMITTER_BASE<sEMITTER_CONFIG_TYPE>::tVOLUME_EMITTER_BASE(sLGI_VOLUME_EMITTER_REC &lgi_record,
                                                                 asINT32 cdi_id,
                                                                 std::string &name,
                                                                 EMISSION_VOLUME emission_volume,
                                                                 sPARTICLE_VAR emission_fraction,
                                                                 BOOLEAN fixed_release_points,
                                                                 PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration,
                                                                 std::vector<std::string> &spray_direction_variable_names) :
  tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                     cdi_id,
                                                     name,
                                                     emission_volume,
                                                     emission_fraction,
                                                     fixed_release_points,
                                                     emitter_configuration),
  m_emission_direction(spray_direction_variable_names,
                       lgi_record.spray_direction_constants,
                       lgi_record.major_ellipse_direction,
                       lgi_record.elliptical_nozzle,
                       lgi_record.spray_direction_csys_id)

{
  this->m_type = sPARTICLE_EMITTER_BASE::UNDEFINED_EMITTER_TYPE;
  vcopy(m_release_spacing, lgi_record.release_spacing);
}

//Rain emitter constructor
template <typename sEMITTER_CONFIG_TYPE>
tRAIN_EMITTER_BASE<sEMITTER_CONFIG_TYPE>::tRAIN_EMITTER_BASE(sLGI_RAIN_EMITTER_REC &lgi_record,
                                                             asINT32 cdi_id,
                                                             std::string &name,
                                                             EMISSION_VOLUME emission_volume,
                                                             sPARTICLE_VAR emission_fraction,
                                                             PARTICLE_EMITTER_CONFIGURATION_BASE emitter_config) :
  tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                     cdi_id,
                                                     name,
                                                     emission_volume,
                                                     emission_fraction,
                                                     FALSE,
                                                     emitter_config)
{
  this->m_type = sPARTICLE_EMITTER_BASE::UNDEFINED_EMITTER_TYPE;
  this->m_is_rain_emitter = TRUE;
  if (this->emission_geometry()->geometry_type() == BOX) {
    sPARTICLE_VAR air_velocity[3];
    this->emitter_configuration()->free_stream_velocity(air_velocity);
    EMISSION_BOX emitter_box = (EMISSION_BOX)this->m_emission_geometry;
    dFLOAT box_size[3];
    emitter_box->size(box_size);
    sCSYS &box_csys = sim.csys_table[emitter_box->csys_id()];
    m_emission_rate_calculator = xnew sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER(
                                                                                 (SIMUTILS::eEMISSION_RATE_TYPES)(this->emitter_configuration()->emission_rate_type()),
                                                                                 this->emitter_configuration()->emission_rate(),
                                                                                 convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->diameter_distribution()),
                                                                                 this->emitter_configuration()->diameter_distribution_param1(),
                                                                                 this->emitter_configuration()->diameter_distribution_param2(),
                                                                                 this->emitter_configuration()->diameter_min(),
                                                                                 this->emitter_configuration()->diameter_max(),
                                                                                 convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->material()->density_distribution()),
                                                                                 this->emitter_configuration()->material()->density_distribution_param1(),
                                                                                 this->emitter_configuration()->material()->density_distribution_param2(),
                                                                                 g_particle_sim_info.gravity,
                                                                                 air_velocity,
                                                                                 sim.char_density * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR,
                                                                                 g_nu_molecular, //not temperature adjusted
                                                                                 box_size,
                                                                                 (const double[3]) {0,0,0},(const double[3]) {0,0,0}, (const double[2]) {0,0}, //no inputs for cylinder
                                                                                 box_csys.g_to_l_xform.xcoord,
                                                                                 SIMUTILS::BOX
                                                                                 );

  } else {
    if(this->emission_geometry()->geometry_type() == CYLINDER) {
      sPARTICLE_VAR air_velocity[3];
      this->emitter_configuration()->free_stream_velocity(air_velocity);
      EMISSION_CYLINDER emitter_cylinder = (EMISSION_CYLINDER)this->m_emission_geometry;
      dFLOAT emitter_cylinder_center_one[3];
      dFLOAT emitter_cylinder_center_two[3];
      emitter_cylinder->center(emitter_cylinder_center_one, 0);
      emitter_cylinder->center(emitter_cylinder_center_two, 1);
      dFLOAT emitter_cylinder_radii[2];
      emitter_cylinder->radii(emitter_cylinder_radii);
      sCSYS &csys = sim.csys_table[emitter_cylinder->csys_id()];
      m_emission_rate_calculator = xnew sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER(
                                                                                   (SIMUTILS::eEMISSION_RATE_TYPES)(this->emitter_configuration()->emission_rate_type()),
                                                                                   this->emitter_configuration()->emission_rate(),
                                                                                   convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->diameter_distribution()),
                                                                                   this->emitter_configuration()->diameter_distribution_param1(),
                                                                                   this->emitter_configuration()->diameter_distribution_param2(),
                                                                                   this->emitter_configuration()->diameter_min(),
                                                                                   this->emitter_configuration()->diameter_max(),
                                                                                   convert_pdfs_pdf_type_to_simutils_pdf_type(this->emitter_configuration()->material()->density_distribution()),
                                                                                   this->emitter_configuration()->material()->density_distribution_param1(),
                                                                                   this->emitter_configuration()->material()->density_distribution_param2(),
                                                                                   g_particle_sim_info.gravity,
                                                                                   air_velocity,
                                                                                   sim.char_density * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR,
                                                                                   g_nu_molecular, //not temperature adjusted
                                                                                   (const double[3]) {0, 0, 0},// no inputs for box
                                                                                   emitter_cylinder_center_one,
                                                                                   emitter_cylinder_center_two,
                                                                                   emitter_cylinder_radii,
                                                                                   csys.g_to_l_xform.xcoord,
                                                                                   //this->emission_geometry()->geometry_type()
                                                                                   SIMUTILS::CYLINDER
                                                                                   );

    }else {
      msg_error("Problem encountered computing a rain emitter release rate: Rain emitter geometry is neither BOX nor CYLINDER \n");
    }
  }

  if(m_emission_rate_calculator->error_code()) {
 #ifdef WHY_WONT_THIS_LINK
    msg_error("Problem encountered computing a rain emitter release rate: %s. %s",
              m_emission_rate_calculator->error_code_string().c_str(),
              m_emission_rate_calculator->error_code_explanation().c_str());
#else
    msg_error("Problem encountered computing a rain emitter release rate.");
#endif
  }
}

template <typename sEMITTER_CONFIG_TYPE>
VOID tCONFIGURABLE_EMITTER_BASE<sEMITTER_CONFIG_TYPE>::emit_parcels() { //This method is overridden for tire emitters which utilize a unique emitter configuration type.
  asINT32 parcel_count = 0;
  if(  (g_timescale.m_time >= this->m_start_time)   &&   ( (g_timescale.m_time < this->m_end_time) || (this->m_end_time < 0) )  ) {
    sPARTICLE_VAR average_release_rate = this->m_emission_fraction * this->particle_emission_rate() / this->particles_per_parcel();
    if(this->material()->is_accretion_material(g_particle_sim_info.ice_accretion_parameters.accretion_material_index)){
      average_release_rate *= g_particle_sim_info.ice_accretion_parameters.accretion_acceleration_factor;
    }
    asINT32 release_rate;
    if (g_use_deterministic_release_rate) {
      m_emission_deficit += average_release_rate;
      release_rate = floor(m_emission_deficit);
      m_emission_deficit -= release_rate;
    } else {
      release_rate = (asINT32)g_random_particle_properties->random_number(average_release_rate, 0, lgi_to_pdfs_distribution(LGI::DISTRIBUTION_POISSON));
    }
    //LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("Release rate: %d", release_rate);

    for(asINT32 n = 0; n < release_rate; n++) {

      sPARTICLE_VAR position[N_SPACE_DIMS];
      UBLK init_ublk;
      sINT8 voxel;
      if(pick_a_position_on_this_sp(position, init_ublk, voxel)) {
        parcel_count++;
        sPARTICLE_VAR local_gravity[3];
        vcopy(local_gravity, g_particle_sim_info.gravity);
        sPARTICLE_VAR ref_frame_vel[3] = {0.0, 0.0, 0.0};
        LRF_PHYSICS_DESCRIPTOR lrf = init_ublk->lrf_physics_descriptor();
        dFLOAT lrf_rotation[2][3][3];
        //Compute the gravity in the local reference frame.
        //And compute the reference frame velocity.
        if(lrf != NULL) {
          //rotate gravity from the default csys to the internal lrf csys
          //calculate_rotation_matrix(lrf->axis, lrf->angle_rotated, lrf_rotation);
          get_rotation_matrix_from_axis_angle(lrf->axis, lrf->angle_rotated, lrf_rotation[1]);
          matrix_transpose(lrf_rotation[0], lrf_rotation[1]);

          rotate_vector(g_particle_sim_info.gravity, local_gravity, lrf_rotation[0]);

          sPARTICLE_VAR radius[3] = {0.0, 0.0, 0.0};
          vsub(radius, position, lrf->point); //r is the radius about the rotation point expressed in either the internal lattice coords or the external
          vcross(ref_frame_vel, lrf->axis, radius);
          vmul(ref_frame_vel, lrf->omega);

        }
        //Update the spatially varying sprayer and emitter model parameters at this parcel's position.
        STP_GEOM_VARIABLE point[3];
        STP_GEOM_VARIABLE normal[3];
        vcopy(point, position);
        vzero(normal);
        this->update_space_varying_parameters(point, normal, g_timescale.m_time, g_timescale.m_powertherm_time);

        //determine the parcel initial condition
        sPARTICLE_VAR density, kinematic_viscosity, surface_tension;
        this->material()->sample_material_properties(density, kinematic_viscosity, surface_tension);

        sPARTICLE_VAR diameter;
        diameter = this->emitter_configuration()->sample_spray_droplet_size();

        sPARTICLE_VAR velocity[N_SPACE_DIMS] = {0,0,0};

        //asINT32 prior_index = sim_prior_timestep_index(init_ublk->scale());
        asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(init_ublk->scale());
        sPARTICLE_VAR fluid_density = init_ublk->lb_data()->m_lb_data[prior_index].density[voxel] * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR; //voxel fluid density
#if BUILD_D19_LATTICE
	if(sim.is_pf_model) {
          sPARTICLE_VAR phi = init_ublk->uds_data(0)->uds[prior_index][voxel];
          fluid_density = phi2rho_capped(phi) * g_density_scale_factor * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR;
        }
#endif

        if(this->is_rain_emitter() || this->material()->is_massless_tracer()) {    //Use the local fluid velocity for massless tracers and the local fluid
                                                                                   //velcoity plus terminal velocity for rain emitters that don't emitt
                                                                                   //massless tracers.

          velocity[0] = init_ublk->lb_data()->m_lb_data[prior_index].vel[0][voxel];
          velocity[1] = init_ublk->lb_data()->m_lb_data[prior_index].vel[1][voxel];
          velocity[2] = init_ublk->lb_data()->m_lb_data[prior_index].vel[2][voxel];
#if BUILD_D19_LATTICE
	  if(sim.is_pf_model) {
            velocity[0] = init_ublk->pf_data()->m_pf_data[prior_index].vel_pfld[0][voxel];
            velocity[1] = init_ublk->pf_data()->m_pf_data[prior_index].vel_pfld[1][voxel];
            velocity[2] = init_ublk->pf_data()->m_pf_data[prior_index].vel_pfld[2][voxel];
          }
#endif   
          if (g_interpolate_air_vel_for_particles) {
            sPARTICLE_VAR displacement[N_SPACE_DIMS];
            sPARTICLE_VAR centroid[N_SPACE_DIMS];
            centroid[0] = init_ublk->centroids(voxel, 0);
            centroid[1] = init_ublk->centroids(voxel, 1);
            centroid[2] = init_ublk->centroids(voxel, 2);
            vsub(displacement, position, centroid);
            ccDOTIMES(i, N_SPACE_DIMS) {
              ccDOTIMES(j, N_SPACE_DIMS) {
                velocity[i] += displacement[j] * init_ublk->p_data()->s.grad_u[voxel][i][j];
              }
            }
          }
          vmul(velocity, particle_sim.lattice_time_correction());

          if(!this->material()->is_massless_tracer()) {
            dFLOAT terminal_velocity[3] = {0.0, 0.0, 0.0};

            //need to determine the local ref frame acceleration for this ublk.
            sPARTICLE_VAR ref_frame_accel[3] = {0.0, 0.0, 0.0};
#if 0
            particle_sim.compute_ref_frame_accel(lrf, init_ublk->scale(),
                                                 parcel->x[this_timestep_index],
                                                 parcel->v[this_timestep_index],
                                                 ref_frame_accel);
#endif
            vinc(ref_frame_accel, local_gravity);

            particle_sim.terminal_velocity(ref_frame_accel,
                                           fluid_density,
                                           g_nu_molecular, //air kinematic viscosity (not temperature corrected)
                                           density,
                                           diameter,
                                           terminal_velocity);
            vinc(velocity, terminal_velocity);
          }
        } else { //if not a rain emitter or an emitter emitting massless tracers, use the sprayer properties to pick the initial velcoity.
          if(!pick_a_velocity_vector(velocity)) {
            //If no velocity vector could be computed, don't emit from this location.
            //This was added for surface emiters set to emit from surface normals with
            //elliptical spray cones when the surface normal may be parallel to the major
            //ellipse direction for some locations on the surface.
            asINT32 unknown_scale = 0;
            sdFLOAT facet_position[N_SPACE_DIMS];
            vcopy(facet_position, position);
            simerr_report_error_code(SP_EER_DEGENERATE_SPRAYER_ELLIPSE, unknown_scale, facet_position);
            continue;
          }
        }

        //Allocate a new parcel with the above initial conditions
        sPARTICLE_VAR effective_gravity = std::sqrt(vdot(local_gravity, local_gravity)) * (1.0 - fluid_density / density); //Adjusted for bouyancy.
        PARCEL_STATE parcel = xnew sPARCEL_STATE(this->get_next_parcel_id(),
                                                 g_timescale.time_flow(),
                                                 my_proc_id,
                                                 (auINT32)this->m_id,
                                                 (sPARTICLE_VAR)this->particles_per_parcel(),
                                                 (auINT32)0,                                     //reflection count
                                                 JUST_EMITTED_MAYBE_IN_FLUID, //IN_FLUID,
                                                 (BOOLEAN)this->material()->breakup_allowed(),
                                                 (sPARTICLE_VAR)-0.266,                                //y_breakup: Assumed the droplet was born "tear-shaped" or "snowcone-shaped"
                                                 (sPARTICLE_VAR)0.0,                                   // dydt: Assumed the parts of the droplet do not move relative each other at birth (arguable!)
                                                 (auINT32)0,                                     //include drag from inception
                                                 density,
                                                 kinematic_viscosity,
                                                 surface_tension,
                                                 diameter,
                                                 position,
                                                 velocity,
                                                 lrf == NULL ? -1 : lrf->lrf_index);

        //printf("particle with material %d came from emitter id %d.\n", this->material()->id(), m_id);
        
#if 0
        //Transform the velocity into the local lrf frame from the global ref frame.
        if(lrf != NULL) {
          sPARTICLE_VAR vel[3] = {0.0, 0.0, 0.0};
          vsub(vel, parcel->v[this_timestep_index], ref_frame_vel);
          rotate_vector(vel, parcel->v[this_timestep_index], lrf_rotation[0]);
        }
#endif

        //Add the particle to the voxels parcel list.
        UBLK_PARTICLE_DATA p_data = init_ublk->p_data();
        p_data->fluid_parcel_list[voxel]->add(parcel);
        parcel->m_home_voxel = voxel;
        parcel->m_home_shob_id = init_ublk->id();
        parcel->m_updated_container_this_timestep = true;

        g_sp_emitted_particle_mass += parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;

        //Measure the starting vertex of the parcel.
        particle_sim.measure_trajectory_vertex(init_ublk, parcel, EVENT_EMITTED, TRUE);  //True means ignore the decimation options and always record the vertex.

        if(PARCEL_IS_INTERESTING(parcel)) {
          asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
          msg_print("parcel %d emitted at time %ld with position %g %g %g (%d) and velocity %g %g %g", 
                    parcel->id, 
                    g_timescale.m_time,
                    parcel->x[this_timestep_index][0],
                    parcel->x[this_timestep_index][1],
                    parcel->x[this_timestep_index][2],
                    this_timestep_index,
                    parcel->v[this_timestep_index][0],
                    parcel->v[this_timestep_index][1],
                    parcel->v[this_timestep_index][2]);
        }
        
#if DEBUG_PARTICLE_SUMMARY
        g_emitted_parcel_count++;
#endif

      }
    }
  }
  timer_accum_counters(SP_PARTICLE_EMISSION_TIMER, 0, parcel_count != 0); //emitter count (to distinguish timing info for emitters that never emit)
  timer_accum_counters(SP_PARTICLE_EMISSION_TIMER, 1, parcel_count); //particle count

}

VOID sTIRE_EMITTER::emit_parcels() {  //specialized for a tire emitter
  asINT32 parcel_count = 0;
  if(  (g_timescale.m_time >= this->m_start_time)   &&   ( (g_timescale.m_time < this->m_end_time) || (this->m_end_time < 0) )  ) {
    sPARTICLE_VAR average_release_rate = this->m_emission_fraction * this->particle_emission_rate() / this->particles_per_parcel();
    if(this->material()->is_accretion_material(g_particle_sim_info.ice_accretion_parameters.accretion_material_index)){
      average_release_rate *= g_particle_sim_info.ice_accretion_parameters.accretion_acceleration_factor;
    }
    asINT32 release_rate;

    if (g_use_deterministic_release_rate) {
      m_emission_deficit += average_release_rate;
      release_rate = floor(m_emission_deficit);
      m_emission_deficit -= release_rate;
    } else {
      release_rate = (asINT32)g_random_particle_properties->random_number(average_release_rate, 0, lgi_to_pdfs_distribution(LGI::DISTRIBUTION_POISSON));
    }

    for(asINT32 n = 0; n < release_rate; n++) {

      sPARTICLE_VAR position[N_SPACE_DIMS];
      UBLK init_ublk = NULL;
      sINT8 voxel = -1;

      //Pick a new initial position from the emission geometry.
      sdFLOAT ref_vec[3];
      vcross(ref_vec, m_rotation_axis, m_zero_angle_direction);
      vunitize(ref_vec);

      TIRE_EMITTER_CONFIGURATION emitter_configuration = (TIRE_EMITTER_CONFIGURATION)this->m_emitter_configuration;
      EMISSION_CYLINDER cylinder = (EMISSION_CYLINDER)this->m_emission_geometry;

      sPARTICLE_VAR angle = emitter_configuration->sample_arc_position();

      dFLOAT direction_vector[3];
      ccDOTIMES(i, 3) {
        direction_vector[i] = cos(angle)*m_zero_angle_direction[i] + sin(angle)*ref_vec[i];
      }

      //xform to default
      xform_vector(direction_vector, &(sim.csys_table[m_zero_angle_csys_id]));

      sPARTICLE_VAR vec[3], tire_csys[3][3];
      vcopy(vec, direction_vector);
      cylinder->sample_position_with_vector(position, tire_csys, vec);

      //cylinder axis is not in the same direction as the rotation axis, change tire tangential direction
      if(vdot(tire_csys[2], m_rotation_axis) > 0.0) {
        vneg(tire_csys[0], tire_csys[0]);
      }

      if(cylinder->find_voxel(position[0], position[1], position[2], init_ublk, voxel)) {

        parcel_count++;
        sPARTICLE_VAR local_gravity[3];
        vcopy(local_gravity, g_particle_sim_info.gravity);
        sPARTICLE_VAR ref_frame_vel[3] = {0.0, 0.0, 0.0};
        LRF_PHYSICS_DESCRIPTOR lrf = init_ublk->lrf_physics_descriptor();
        dFLOAT lrf_rotation[2][3][3];
        //Compute the gravity in the local reference frame.
        //And compute the reference frame velocity.
        if(lrf != NULL) {
          //rotate gravity from the default csys to the internal lrf csys
          //calculate_rotation_matrix(lrf->axis, lrf->angle_rotated, lrf_rotation);
          get_rotation_matrix_from_axis_angle(lrf->axis, lrf->angle_rotated, lrf_rotation[1]);

          rotate_vector(g_particle_sim_info.gravity, local_gravity, lrf_rotation[0]);

          sPARTICLE_VAR radius[3] = {0.0, 0.0, 0.0};
          vsub(radius, position, lrf->point); //r is the radius about the rotation point expressed in either the internal lattice coords or the external
          vcross(ref_frame_vel, lrf->axis, radius);
          vmul(ref_frame_vel, lrf->omega);
        }
        //Update the spatially varying sprayer and emitter model parameters at this parcel's position.
        STP_GEOM_VARIABLE point[3];
        STP_GEOM_VARIABLE normal[3];
        vcopy(point, position);
        vzero(normal);
        this->update_space_varying_parameters(point, normal, g_timescale.m_time, g_timescale.m_powertherm_time);

        //determine the parcel initial condition
        sPARTICLE_VAR density, kinematic_viscosity, surface_tension;
        this->material()->sample_material_properties(density, kinematic_viscosity, surface_tension);
        sPARTICLE_VAR diameter = emitter_configuration->sample_spray_droplet_size(angle);
        sPARTICLE_VAR velocity_dir[N_SPACE_DIMS] = {0,0,0};
        sPARTICLE_VAR velocity[N_SPACE_DIMS];


        emitter_configuration->sample_spray_direction(angle, velocity_dir);
        sPARTICLE_VAR offset_angle = emitter_configuration->offset_angle(angle);

        ccDOTIMES(i, N_SPACE_DIMS) {
          velocity[i] = velocity_dir[0] * (tire_csys[0][i]*cos(offset_angle) + tire_csys[1][i]*sin(offset_angle))
            + velocity_dir[1] * (-tire_csys[0][i]*sin(offset_angle) + tire_csys[1][i]*cos(offset_angle))
            + velocity_dir[2] * tire_csys[2][i];
        }

        sPARTICLE_VAR velocity_magnitude = emitter_configuration->sample_spray_velocity_magnitude(angle);
        vmul(velocity, velocity_magnitude);

        //Allocate a new parcel with the above initial conditions
        PARCEL_STATE parcel = xnew sPARCEL_STATE(this->get_next_parcel_id(),
                                                 g_timescale.time_flow(),
                                                 my_proc_id,
                                                 (auINT32)this->m_id,
                                                 (sPARTICLE_VAR)this->particles_per_parcel(),
                                                 (auINT32)0,                                     //reflection count
                                                 JUST_EMITTED_MAYBE_IN_FLUID, //IN_FLUID,
                                                 (BOOLEAN)this->material()->breakup_allowed(),
                                                 (sPARTICLE_VAR)-0.266,                                //y_breakup: Assumed the droplet was born "tear-shaped" or "snowcone-shaped"
                                                 (sPARTICLE_VAR)0.0,                                   // dydt: Assumed the parts of the droplet do not move relative each other at birth (arguable!)
                                                 (auINT32)0,                                     //include drag from inception
                                                 density,
                                                 kinematic_viscosity,
                                                 surface_tension,
                                                 diameter,
                                                 position,
                                                 velocity,
                                                 lrf == NULL ? -1 : lrf->lrf_index);

#if 0
        //Transform the velocity into the local lrf frame from the global ref frame.
        if(lrf != NULL) {
          sPARTICLE_VAR vel[3] = {0.0, 0.0, 0.0};
          vsub(vel, parcel->v[this_timestep_index], ref_frame_vel);
          rotate_vector(vel, parcel->v[this_timestep_index], lrf_rotation[0]);
        }
#endif
        //Add the particle to the voxels parcel list.
        UBLK_PARTICLE_DATA p_data = init_ublk->p_data();
        p_data->fluid_parcel_list[voxel]->add(parcel);
        parcel->m_home_voxel = voxel;

        g_sp_emitted_particle_mass += parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;

        //Measure the starting vertex of the parcel.
        particle_sim.measure_trajectory_vertex(init_ublk, parcel, EVENT_EMITTED, TRUE); //True means ignore the decimation options and always record the vertex.

        if(PARCEL_IS_INTERESTING(parcel)) {
          asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
          msg_print("parcel %d emitted at time %ld with position %g %g %g (%d) and velocity %g %g %g", 
                    parcel->id, 
                    g_timescale.m_time,
                    parcel->x[this_timestep_index][0],
                    parcel->x[this_timestep_index][1],
                    parcel->x[this_timestep_index][2],
                    this_timestep_index,
                    parcel->v[this_timestep_index][0],
                    parcel->v[this_timestep_index][1],
                    parcel->v[this_timestep_index][2]);
        }
        
#if DEBUG_PARTICLE_SUMMARY
        g_emitted_parcel_count++;
#endif
      }
    }
  }
  timer_accum_counters(SP_PARTICLE_EMISSION_TIMER, 0, parcel_count != 0); //emitter count (to distinguish timing info for emitters that never emit)
  timer_accum_counters(SP_PARTICLE_EMISSION_TIMER, 1, parcel_count); //particle count

}

// Explicitly instantiate the full set of supported emitter types.
// They are declared extern in particle_emitters.h
template class tCONFIGURABLE_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tCONFIGURABLE_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tCONFIGURABLE_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
template class tCONFIGURABLE_EMITTER_BASE<sRAIN_EMITTER_CONFIGURATION>;

template class tSURFACE_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tSURFACE_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tSURFACE_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

template class tFIXED_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tFIXED_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tFIXED_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
template class tRANDOM_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tRANDOM_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tRANDOM_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

template class tVOLUME_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tVOLUME_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tVOLUME_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

template class tCYLINDER_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tCYLINDER_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tCYLINDER_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
template class tBOX_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tBOX_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tBOX_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

template class tRAIN_EMITTER_BASE<sRAIN_EMITTER_CONFIGURATION>;
template class tRAIN_BOX_EMITTER<sRAIN_EMITTER_CONFIGURATION>;
template class tRAIN_CYLINDER_EMITTER<sRAIN_EMITTER_CONFIGURATION>;

template class tPOINT_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
template class tPOINT_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
template class tPOINT_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

