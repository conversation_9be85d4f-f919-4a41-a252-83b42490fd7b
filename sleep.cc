
/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Sleeping
 *--------------------------------------------------------------------------*/

#include "sleep.h"
#include "thread_run.h"

BOOLEAN g_short_mpi_thread_sleep = FALSE;
asINT32 g_sp_sleep_ns[4]; 


VOID sp_thread_sleep(SP_SLEEP_INDEX sIndex) {
  asINT32 sleepIndex = asINT32(sIndex);
  if(g_sp_sleep_ns[sleepIndex] != 0)
    platform_sleep_nanoseconds(g_sp_sleep_ns[sleepIndex]);
}

VOID initialize_sleep_times()
{
  cSTRING user_specified_sp_thread_sleep_ns = getenv("EXA_SP_THREAD_SLEEP_NS");

  if(user_specified_sp_thread_sleep_ns != NULL) {
    g_sp_sleep_ns[THREAD_SLEEP_LONG] = atoi(user_specified_sp_thread_sleep_ns);
    if(my_proc_id == 0)
      msg_print("Using user-specified thread sleep period of %d ns", g_sp_sleep_ns[THREAD_SLEEP_LONG]);
  } else {
    g_sp_sleep_ns[THREAD_SLEEP_LONG] = DEFAULT_SP_LONG_THREAD_SLEEP_NS;
  }

  cSTRING user_specified_sp_short_thread_sleep_ns = getenv("EXA_SP_SHORT_THREAD_SLEEP_NS");
  if(user_specified_sp_short_thread_sleep_ns != NULL) {
    g_sp_sleep_ns[THREAD_SLEEP_SHORT] = atoi(user_specified_sp_short_thread_sleep_ns);
    if(my_proc_id == 0)
      msg_print("Using user-specified short thread sleep period of %d ns", g_sp_sleep_ns[THREAD_SLEEP_SHORT]);
  } else {
    g_sp_sleep_ns[THREAD_SLEEP_SHORT] = DEFAULT_SP_SHORT_THREAD_SLEEP_NS;
  }

  cSTRING user_specified_sp_mpi_sleep_ns = getenv("EXA_SP_MPI_SLEEP_NS");

  if(user_specified_sp_mpi_sleep_ns != NULL) {
    g_sp_sleep_ns[MPI_SLEEP_LONG] = atoi(user_specified_sp_mpi_sleep_ns);
    if(my_proc_id == 0)
      msg_print("Using user-specified mpi sleep period of %d ns", g_sp_sleep_ns[MPI_SLEEP_LONG] );
  } else {
    g_sp_sleep_ns[MPI_SLEEP_LONG] = DEFAULT_SP_LONG_MPI_SLEEP_NS;
  }

  cSTRING user_specified_sp_short_mpi_sleep_ns = getenv("EXA_SP_SHORT_MPI_SLEEP_NS");

  if(user_specified_sp_short_mpi_sleep_ns != NULL) {
    g_sp_sleep_ns[MPI_SLEEP_SHORT] = atoi(user_specified_sp_short_mpi_sleep_ns);
    if(my_proc_id == 0)
      msg_print("Using user-specified short mpi sleep period of %d ns", g_sp_sleep_ns[MPI_SLEEP_SHORT] );
  } else {
    g_sp_sleep_ns[MPI_SLEEP_SHORT] = DEFAULT_SP_SHORT_MPI_SLEEP_NS;
  }
  /*if(my_proc_id == 0) {
    ccDOTIMES(si, 4) {
      msg_print_no_prefix("g_sp_sleep_ns[%d] is %d", si, g_sp_sleep_ns[si]);
    }
  }*/
}  

