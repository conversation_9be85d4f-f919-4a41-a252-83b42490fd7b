/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_meas_dcache.h"

#include "ublk.h"
#include "sim.h"
#include "particle_sim_info.h"

sPARTICLE_MEAS_DCACHE* g_particle_meas_dcache;

sPARTICLE_MEAS_COMPONENT::sPARTICLE_MEAS_COMPONENT(int emitter_index, int material_index) {
  m_emitter_index = emitter_index;
  m_material_index = material_index;
}

sPARTICLE_MEAS_COMPONENT::sPARTICLE_MEAS_COMPONENT(int composite_id) : sPARTICLE_MEAS_COMPONENT(-1, -1)  {
  if(composite_id == 0)
    return;
  int num_materials = g_particle_sim_info.num_materials;
  if(--composite_id < num_materials) {
    m_material_index = composite_id;
    return;
  }
  m_emitter_index = composite_id - num_materials;
  return;
}

int sPARTICLE_MEAS_COMPONENT::compute_id(int cdi_emitter_id, int material_id) {
  int num_materials = g_particle_sim_info.num_materials;
  //int num_emitters = g_particle_sim_info.num_cdi_emitters;
  return cdi_emitter_id * num_materials + material_id;
}

int sPARTICLE_MEAS_COMPONENT::compute_composite_id(int cdi_emitter_id, int material_id) {
  if(cdi_emitter_id != -1 && material_id != -1)
    msg_internal_error("Invalid composite id requested in sPARTICLE_MEAS_COMPONENT.");
  if(cdi_emitter_id == -1 && material_id == -1)
    return 0;
  if(cdi_emitter_id == -1)
    return material_id + 1;
  int num_materials = g_particle_sim_info.num_materials;
  return cdi_emitter_id + num_materials + 1;
}

template<typename MEAS_DATATYPE, typename ACCUM_DATATYPE, typename UBLK_TYPE>
tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::tPARTICLE_MEAS_DCACHE() {
  m_num_voxels = N_VOXELS_8; //(particle modeling does't supported 2D)
  m_num_var_types_per_voxel = NUM_SUPPORTED_VARTYPES;
  m_num_materials = g_particle_sim_info.num_materials;
  m_num_emitters = g_particle_sim_info.num_cdi_emitters;
  m_num_components = m_num_materials * m_num_emitters;
  m_num_composite_components =  1 + m_num_materials + m_num_emitters;

  ccDOTIMES(var_index, m_num_var_types_per_voxel) {
    SRI_VARIABLE_TYPE var_type = m_all_supported_sri_fluid_vartypes[var_index];
    switch(var_type) {
    case SRI_VARIABLE_PRTCL_NUMBER:
      m_var_type_index_map.insert(std::make_pair(var_type, var_index));
      break;
    case SRI_VARIABLE_PRTCL_XFORCE:
      m_vector_var_indices.push_back(var_index);
      m_vector_is_population_scaled.push_back(false);
      m_var_type_index_map.insert(std::make_pair(var_type, var_index));
      break;
    case SRI_VARIABLE_PRTCL_MEAN_XVEL:
      m_vector_var_indices.push_back(var_index);
      m_vector_is_population_scaled.push_back(true);
      m_var_type_index_map.insert(std::make_pair(var_type, var_index));
      break;
    case SRI_VARIABLE_PRTCL_YFORCE:
    case SRI_VARIABLE_PRTCL_ZFORCE:
    case SRI_VARIABLE_PRTCL_MEAN_DENSITY:
    case SRI_VARIABLE_PRTCL_MEAN_DIAMETER:
    case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA:
    case SRI_VARIABLE_PRTCL_MEAN_VOLUME:
    case SRI_VARIABLE_PRTCL_MEAN_MASS:
    case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
    case SRI_VARIABLE_PRTCL_MEAN_YVEL:
    case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
    case SRI_VARIABLE_PRTCL_MEAN_TEMP:
    case SRI_VARIABLE_PRTCL_COMPOSITION_1:
    case SRI_VARIABLE_PRTCL_COMPOSITION_2:
    case SRI_VARIABLE_PRTCL_COMPOSITION_3:
    case SRI_VARIABLE_PRTCL_EVAPORATION_RATE:
      m_var_type_index_map.insert(std::make_pair(var_type, var_index));
      break;
    default:;
      //Ignore non particle modeling var types.
    }
  }
  m_num_values = m_num_components * m_num_var_types_per_voxel * m_num_voxels;
  m_num_bytes = m_num_values * sizeof(ACCUM_DATATYPE);
  m_values = cnew ACCUM_DATATYPE[m_num_values];

  m_num_composite_values = m_num_composite_components * m_num_var_types_per_voxel * m_num_voxels;
  m_num_composite_bytes =  m_num_composite_values * sizeof(MEAS_DATATYPE);
  m_composite_values = cnew MEAS_DATATYPE[m_num_composite_values];

  m_non_grf_composite_values = cnew MEAS_DATATYPE[m_num_composite_values];
  m_grf_composite_values = cnew MEAS_DATATYPE[m_num_composite_values];

  //Initialize the var index lookup table for each window.
  m_num_windows = g_meas_windows.n_meas_windows();
  m_var_index_tables = cnew  sINT16*[m_num_windows];
  
  ccDOTIMES(window_index, m_num_windows) {
    MEAS_WINDOW window = g_meas_windows[window_index];
    m_var_index_tables[window_index] = cnew sINT16[window->n_variables];
    ccDOTIMES(window_var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[window_var_index];
      auto key_value = m_var_type_index_map.find(var_type);
      int dcache_var_index = -1;
      if(key_value != m_var_type_index_map.end())
        dcache_var_index = key_value->second;
      m_var_index_tables[window_index][window_var_index] = dcache_var_index;
    }
  }

  m_current_var_index_table = *m_var_index_tables;
  m_rotate_vectors_to_grf = false;
  m_grf_transformation_complete = false;
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
void tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::compute(UBLK_TYPE* ublk, VOXEL_MASK_8 voxel_mask) {

  m_rotate_vectors_to_grf = false;
  m_grf_transformation_complete = false;
  m_ublk = ublk;
  m_particle_population_scale_factor = 1 << (sim.num_scales - 1 - m_ublk->scale());
  m_voxel_mask = voxel_mask;
  m_required_meas_variables = &(ublk->p_data()->s.m_window_data.m_required_meas_variables);

  memset(m_values, 0 , m_num_bytes); //Reset all accumulators.
  memset(m_composite_values, 0 , m_num_composite_bytes);
  //memset(m_non_grf_composite_values, 0 , m_num_composite_bytes);
  //memset(m_grf_composite_values, 0 , m_num_composite_bytes);

  ccDOTIMES(voxel, m_num_voxels) {
    if(!m_voxel_mask.test(voxel))  //Skip over the voxels not in the mask.
      continue;
    if(m_required_meas_variables->test(XFORCE)) {
      int value_index = get_value_index(0, XFORCE, voxel);
      m_values[value_index] = ublk->p_data()->s.particle_force[0][voxel];
      value_index += m_num_voxels; //X, y, and z components should be at stride N_VOXELS.
      m_values[value_index] = ublk->p_data()->s.particle_force[1][voxel];
      value_index += m_num_voxels;
      m_values[value_index] =  ublk->p_data()->s.particle_force[2][voxel];
    }

    if(m_required_meas_variables->test(EVAPORATION_RATE)) {
      int value_index = get_value_index(0, EVAPORATION_RATE, voxel);
      m_values[value_index] = ublk->p_data()->s.particle_evaporation_rate[voxel];
    }

    PARCEL_LIST parcel_list = m_ublk->p_data()->fluid_parcel_list[voxel];
    parcel_list->reset();
    while(!parcel_list->exhausted()) {
      PARCEL_STATE parcel = parcel_list->data();
      asINT32 timestep_index = parcel->relative_timestep_to_state_index(0);
      asINT32 sim_emitter_id = parcel->emitter_id;
      asINT32 cdi_emitter_id = sPARTICLE_EMITTER_BASE::m_sim_to_cdi_emitter_id[sim_emitter_id];
      asINT32 material_id =  g_particle_sim_info.emitters[parcel->emitter_id]->material()->id();
      int component_index = sPARTICLE_MEAS_COMPONENT::compute_id(cdi_emitter_id, material_id);
#if 1
      int value_index = get_value_index(component_index, NUMBER, voxel);
      ccDO_FROM_BELOW(var_index, NUMBER, m_num_var_types_per_voxel) {
        if(!m_required_meas_variables->test(var_index)) {
          value_index += m_num_voxels;
          continue;
        }
#else
        //ccDO_BITSET(var_index, *m_required_meas_variables) {
        //value_index = get_value_index(component_index, var_index, voxel); //faster to check each bit than to recompute the value index from scratch each time?
#endif

        switch(var_index) {
        case NUMBER:
          m_values[value_index] += parcel->num_particles;
          break;
        case MEAN_DENSITY:
          m_values[value_index] += parcel->num_particles * parcel->rho;
          break;
        case MEAN_DIAMETER:
          m_values[value_index] += parcel->d * parcel->num_particles;
          break;
        case MEAN_SURFACE_AREA:
          m_values[value_index] += parcel->d * parcel->d * parcel->num_particles;
          break;
        case MEAN_VOLUME:
          m_values[value_index] += parcel->d * parcel->d * parcel->d * parcel->num_particles;
          break;
        case MEAN_MASS:
          m_values[value_index] += parcel->parcel_mass;
          break;
        case MEAN_XVEL:
          m_values[value_index] +=  parcel->v[timestep_index][0] * parcel->num_particles;
          break;
        case MEAN_YVEL:
          m_values[value_index] +=  parcel->v[timestep_index][1] * parcel->num_particles;
          break;
        case MEAN_ZVEL:
          m_values[value_index] +=  parcel->v[timestep_index][2] * parcel->num_particles;
          break;
        case MEAN_VEL_MAG:
          m_values[value_index] += std::sqrt(parcel->v[timestep_index][0] * parcel->v[timestep_index][0] +
                                             parcel->v[timestep_index][1] * parcel->v[timestep_index][1] +
                                             parcel->v[timestep_index][2] * parcel->v[timestep_index][2] ) * parcel->num_particles;
          break;
        case MEAN_TEMPERATURE:
          m_values[value_index] += parcel->temperature * parcel->num_particles;
          break;
        case MEAN_COMPOSITION_1:
          m_values[value_index] += parcel->mass_fraction(0) * parcel->num_particles;
          break;
        case MEAN_COMPOSITION_2:
          m_values[value_index] += parcel->mass_fraction(1) * parcel->num_particles;
          break;
        case MEAN_COMPOSITION_3:
          m_values[value_index] += parcel->mass_fraction(2) * parcel->num_particles;
          break;
        case EVAPORATION_RATE:
        case XFORCE:
        case YFORCE:
        case ZFORCE:
          value_index += m_num_voxels;
          continue;
        default:
          msg_internal_error("Unhandled particle measurement type.");
        }
        value_index += m_num_voxels;
      }
      parcel_list->next();
    }

    //Compute the sums needed for the composited components in this voxel
    ccDOTIMES(emitter_index, m_num_emitters) {
      int composite_component_index = sPARTICLE_MEAS_COMPONENT::compute_composite_id(emitter_index, -1);
      int composite_value_index = get_value_index(composite_component_index, 0, voxel);
      ccDOTIMES(var_index, m_num_var_types_per_voxel) {
        ccDOTIMES(material_index, m_num_materials) {
          int component_index = sPARTICLE_MEAS_COMPONENT::compute_id(emitter_index, material_index);
          int value_index = get_value_index(component_index, var_index, voxel);
          m_composite_values[composite_value_index] += m_values[value_index];
        }
        m_composite_values[composite_value_index]  *= m_particle_population_scale_factor;
        composite_value_index +=m_num_voxels;
      }
    }

    ccDOTIMES(material_index, m_num_materials) {
      int composite_component_index = sPARTICLE_MEAS_COMPONENT::compute_composite_id(-1, material_index);
      int composite_value_index = get_value_index(composite_component_index, 0, voxel);
      ccDOTIMES(var_index, m_num_var_types_per_voxel) {
        ccDOTIMES(emitter_index, m_num_emitters) {
          int component_index = sPARTICLE_MEAS_COMPONENT::compute_id(emitter_index, material_index);
          int value_index = get_value_index(component_index, var_index, voxel);
          m_composite_values[composite_value_index] += m_values[value_index];
        }
        m_composite_values[composite_value_index]  *= m_particle_population_scale_factor;
        composite_value_index +=m_num_voxels;
      }
    }

    int composite_component_index = sPARTICLE_MEAS_COMPONENT::compute_composite_id(-1, -1);
    int composite_value_index = get_value_index(composite_component_index, 0, voxel);
    ccDOTIMES(var_index, m_num_var_types_per_voxel) {
      ccDOTIMES(emitter_id, m_num_emitters) {
        int emitter_composite_component_index = sPARTICLE_MEAS_COMPONENT::compute_composite_id(emitter_id, -1);
        int emitter_composite_value_index = get_value_index(emitter_composite_component_index, var_index, voxel);
        m_composite_values[composite_value_index] += m_composite_values[emitter_composite_value_index];
      }
      composite_value_index += m_num_voxels;
    }
  }
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
void tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::setup_for_window(int window_index, bool should_transform_to_grf) {
  //Should only be called after the call to compute().
  m_current_var_index_table = m_var_index_tables[window_index]; //this array maps the nth variable in the window to the nth variable in this dcache.
  
  //Transform the vectors variables if needed for this window.  Only
  //the composited values (by emitter, by material, or total) are
  //currently supported for measure so only transform these values.
  if(should_transform_to_grf) {
    if(!m_grf_transformation_complete) {
      transform_vectors_to_grf();
      m_grf_transformation_complete = true;
    }
    
    //Copy the transformed values to the m_composite_values if they havent already been.
    if(!m_rotate_vectors_to_grf) {
      m_rotate_vectors_to_grf = true;
      copy_vector_values(m_composite_values, m_grf_composite_values);
    }
  } else {
    //Copy the untransformed values back to m_composite_values if needed
    if(m_rotate_vectors_to_grf) {
      m_rotate_vectors_to_grf = false;
      copy_vector_values(m_composite_values, m_non_grf_composite_values);
    }
  }
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
void tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::copy_vector_values(MEAS_DATATYPE* dest, MEAS_DATATYPE* source) {
  ccDOTIMES(nth_component, m_num_composite_components) {
    ccDOTIMES(nth_vector, m_vector_var_indices.size()) {
      int var_index = m_vector_var_indices[nth_vector];
      int value_index = get_value_index(nth_component, var_index, 0);
      int n_bytes_in_vector = m_num_voxels * 3 * sizeof(MEAS_DATATYPE);
      memcpy(dest + value_index, source + value_index, n_bytes_in_vector);
    }
  }
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
MEAS_DATATYPE* tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::get_voxel_values(int window_var_index, int composite_component_index)  {
  int vartype_dcache_index = m_current_var_index_table[window_var_index];
  int voxel = 0;
  int value_index = get_value_index(composite_component_index, vartype_dcache_index, voxel);
  return m_composite_values + value_index;
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
MEAS_DATATYPE tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::get_voxel_value(int voxel, int window_var_index, int composite_component_index)  {
  int vartype_dcache_index = m_current_var_index_table[window_var_index];
  int value_index = get_value_index(composite_component_index, vartype_dcache_index, voxel);
  return m_composite_values[value_index];
}

template<typename MEAS_DATATYPE,  typename ACCUM_DATATYPE, typename UBLK_TYPE>
void tPARTICLE_MEAS_DCACHE<MEAS_DATATYPE, ACCUM_DATATYPE, UBLK_TYPE>::transform_vectors_to_grf() {
  ccDOTIMES(nth_component, m_num_composite_components) {
    ccDOTIMES(nth_vector, m_vector_var_indices.size()) {
      int var_index = m_vector_var_indices[nth_vector];
      ccDOTIMES(voxel, m_num_voxels) {
        if(!m_voxel_mask.test(voxel))  //Skip over the voxels not in the mask.
          continue;
        
        MEAS_DATATYPE num_particles = m_composite_values[get_value_index(nth_component, NUMBER, voxel)];
        int value_index = get_value_index(nth_component, var_index, voxel);

        if(num_particles == 0) {
          m_non_grf_composite_values[value_index] = 0;
          m_non_grf_composite_values[value_index + m_num_voxels] = 0;
          m_non_grf_composite_values[value_index + (m_num_voxels << 1)] = 0;
          m_grf_composite_values[value_index] = 0;
          m_grf_composite_values[value_index + m_num_voxels] = 0;
          m_grf_composite_values[value_index + (m_num_voxels << 1)] = 0;
          continue;
        }
  
        MEAS_DATATYPE x[3] = {m_composite_values[value_index],
                              m_composite_values[value_index + m_num_voxels],
                              m_composite_values[value_index + (m_num_voxels << 1)]};

        //Save a copy of the unrotated vector;
        m_non_grf_composite_values[value_index] = x[0];
        m_non_grf_composite_values[value_index + m_num_voxels] = x[1];
        m_non_grf_composite_values[value_index + (m_num_voxels << 1)] = x[2];

        MEAS_DATATYPE centroid[3] = {m_ublk->centroids(voxel, 0),
                                     m_ublk->centroids(voxel, 1),
                                     m_ublk->centroids(voxel, 2)};

        if(m_vector_is_population_scaled[nth_vector]) {
          vmul(x, 1.0 / num_particles );
        }
        // Convert the local velocity from local ref frame to global ref frame.
        MEAS_DATATYPE x_grf[3] = {0};
        convert_vel_from_local_to_global_frame(x,
                                               centroid,
                                               x_grf,
                                               m_ublk->lrf_physics_descriptor());
        if(m_vector_is_population_scaled[nth_vector]) {
          vmul(x_grf, num_particles);
        }

        // Transform the global ref frame vel from the local csys to the global csys.
        rotate_vector(x_grf, x_grf, m_ublk->lrf_physics_descriptor()->local_to_global_rotation_matrix);

        //Save a copy of the transformed vectors.
        m_grf_composite_values[value_index] = x_grf[0];
        m_grf_composite_values[value_index + m_num_voxels] = x_grf[1];
        m_grf_composite_values[value_index + (m_num_voxels << 1)] = x_grf[2];
      }
    }
  }
}



template class tPARTICLE_MEAS_DCACHE<sdFLOAT, sPARTICLE_VAR, tUBLK<UBLK_UBFLOAT_TYPE_TAG> >;
