#ifndef MSFL_INTERACTIONS_H
#define MSFL_INTERACTIONS_H
#if BUILD_GPU
#include "offset_based_interface.h"
#include "simulator_namespace.h"
#include "vectorization_support.h"
#include "bitset.h"

__HOST__DEVICE__ _ALWAYS_INLINE_ BOOLEAN is_pde_2_interaction(uINT8 latvec) {
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
  return latvec >= N_LATTICE_VECTORS;
#else
  return (latvec >= D_P2_0_0_D39 && latvec <= D_0_0_N2_D39);
#endif
}

struct sMSFL_INTERACTION {
  STP_SURFEL_WEIGHT m_weight;
  uINT16 m_mblk_index;  
  uINT8  m_latvec;
  uINT8  m_phase_mask_scale_diff;
  uINT8  m_voxel;
  uINT8  m_dst_sfl_index;

  constexpr static size_t N_PHASE_MASK_BITS = 4;
  constexpr static size_t N_SCALE_DIFF_BITS = 2;
  
  __DEVICE__ sMSFL_INTERACTION():
    m_weight(0.0F),
    m_mblk_index(0),
    m_latvec(0),
    m_phase_mask_scale_diff(0),    
    m_voxel(0),
    m_dst_sfl_index(0) {
  }
  
  sMSFL_INTERACTION(uINT8 latvec,
			   uINT8 phase_mask,
         uINT8 scale_diff,
			   uINT16 ublk_index,
			   uINT8 voxel,
			   uINT8 dst_sfl_index,
			   STP_SURFEL_WEIGHT weight):
    m_weight(weight),
    m_mblk_index(ublk_index),    
    m_latvec(latvec),
    m_phase_mask_scale_diff(0),    
    m_voxel(voxel),
    m_dst_sfl_index(dst_sfl_index) {
    set_phase_mask(phase_mask);
    set_scale_diff(scale_diff);
  }

  sMSFL_INTERACTION(const sMSFL_INTERACTION&) = default;
  sMSFL_INTERACTION(sMSFL_INTERACTION&&) = default;
  sMSFL_INTERACTION& operator=(const sMSFL_INTERACTION&) = default;
  sMSFL_INTERACTION& operator=(sMSFL_INTERACTION&&) = default;

  VOID set_phase_mask(uINT8 phase_mask) {
    cassert(phase_mask <= phase_mask_mask());
    m_phase_mask_scale_diff |= phase_mask;
  }

  VOID set_scale_diff(uINT8 scale_diff) {
    cassert(scale_diff <= scale_diff_mask());
    m_phase_mask_scale_diff |= (scale_diff << N_PHASE_MASK_BITS);
  }

  __HOST__DEVICE__ VOID set_as_dummy() {
    this->m_weight = 0.0F;
  }
  
  __HOST__DEVICE__ constexpr static uINT8 phase_mask_mask() {
    return (0x1 << N_PHASE_MASK_BITS) - 1;
  }

  __HOST__DEVICE__ constexpr static uINT8 scale_diff_mask() {
    return (0x1 << N_SCALE_DIFF_BITS) - 1;
  }
  
  __HOST__DEVICE__ uINT8 phase_mask() const {    
    return m_phase_mask_scale_diff & phase_mask_mask();
  }

  __HOST__DEVICE__ uINT8 scale_diff() const {
    return (m_phase_mask_scale_diff >> N_PHASE_MASK_BITS);
  }  

  __HOST__DEVICE__ BOOLEAN is_pde_2_interaction() const {
    return ::is_pde_2_interaction(m_latvec);
  }

};

/*===================================================================================
 * @struct sMSFL_INTERACTIONS
 *==================================================================================*/

enum class MSFL_INT_COMPONENTS : int {
  UNIQUE_UBLKS = 0,
  START_INDICES = 1,
  END_INDICES = 2,
  PDE2_INDICES = 3,
  INTERACTIONS = 4,
  V2S_LP_PEERS = 5,
  V2S_PEERS = 6,
  S2V_LP_MASKS = 7,  
  S2V_MASKS = 8,
  N_COMPONENTS = 9
};

struct sMSFL_INTERACTIONS :
  public tOFFSET_BASED_INTERFACE<MSFL_INT_COMPONENTS, unsigned, sMSFL_INTERACTIONS> {

private:
  unsigned m_num_speed1_interactions;

public:
  VOID set_num_speed1_interactions(unsigned num_speed1_interactions) { m_num_speed1_interactions = num_speed1_interactions; }
  __HOST__DEVICE__ unsigned num_speed1_interactions() const { return m_num_speed1_interactions; }
  using S2V_MASK = unsigned long long;
  //sMBLK** unique_ublks[m_n_unique_ublks]
  //unsigned* start_indices[m_n_unique_ublks]
  //unsigned* end_indices[m_n_unique_ublks]
  //unsigned* pde2_indices[m_n_unique_ublks]
  //sMSFL_INTERACTION* interactions[m_n_interactions]
  //uINT8* v2s_lp_peers[m_n_interactions - n_pde2_interactions]
  //uINT32* v2s_peers[m_n_interactions - n_pde2_interactions]
  //S2V_MASK* s2v_lp_mask[m_n_interactions]
  //S2V_mask* s2v_mask[m_n_interactions]

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sMSFL_INTERACTIONS* h_ptr);

  __DEVICE__
  VOID set_start_end_indices(sMBLK* mblk,
                             int speed,
                             unsigned& start,
                             unsigned& end);
};

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::UNIQUE_UBLKS> { using type = sMBLK*; };

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::INTERACTIONS> { using type = sMSFL_INTERACTION; };

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::V2S_LP_PEERS> { using type = uINT8; };

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::V2S_PEERS> { using type = uINT64; };

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::S2V_LP_MASKS> { using type = sMSFL_INTERACTIONS::S2V_MASK; };

template<>
template<>
struct sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_INT_COMPONENTS::S2V_MASKS> { using type = sMSFL_INTERACTIONS::S2V_MASK; };


__DEVICE__ INLINE
VOID sMSFL_INTERACTIONS::set_start_end_indices(sMBLK* mblk,
                                               int speed,
                                               unsigned& start,
                                               unsigned& end) {
  start = end = 0;
  for (unsigned i = 0; i < this->get_n_elements<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>(); i++) {
    if (this->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>()[i] == mblk) {
      if (speed == 0) {
        start = this->get<MSFL_INT_COMPONENTS::START_INDICES>()[i];
        end = this->get<MSFL_INT_COMPONENTS::END_INDICES>()[i];
      } else {
        start = this->get<MSFL_INT_COMPONENTS::PDE2_INDICES>()[i];
        end = this->get<MSFL_INT_COMPONENTS::PDE2_INDICES>()[i + 1];              
      }
      break;
    }
  }
}

VOID init_msfl_interactions(sMSFL* msfl,
                            std::array<sSURFEL*,64>& sfls_to_pack,
                            size_t n_sfls_packed,
                            const std::vector<sHMBLK*>& mblks,
                            const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map);


/*===================================================================================
 *
 *==================================================================================*/
enum class S2V_COMPONENTS : int {
  UNIQUE_UBLKS = 0,
  INT_MSFL_BATCH_INDICES = 1,
  N_INT_MSFLS = 2,
  INT_MSFLS_START_INDEX = 3,
  N_UNIQUE_MBLKS_PER_BATCH = 4,
  N_COMPONENTS = 5
};

struct S2V_PER_GROUP_BATCH_INFO : public tOFFSET_BASED_INTERFACE<S2V_COMPONENTS, unsigned, S2V_PER_GROUP_BATCH_INFO> {
  
  //sMBLK* m_unique_ublks[m_n_unique_ublks]
  //unsigned* interacting_msfl_batch_indices[m_n_total_interacting_sfl_indices]
  //uINT16* num_interacting_msfls[m_n_unique_ublks]
  //unsigned* interacting_msfl_start_index[m_n_unique_ublks]
  //unsigned* m_n_unique_blocks_per_batch[m_n_batches]
  

  __HOST__ void init(const unsigned (&n_component_elements) [N_COMPONENTS],
		     const std::vector<const sHMBLK*>& unique_s2v_ublks_of_scale,
                     const std::vector<unsigned>& interacting_msfl_batch_indices,
		     const std::vector<uINT16>& num_interacting_msfls,
		     const std::vector<unsigned>& interacting_msfl_start_index,
		     const std::vector<unsigned>& unique_s2v_ublks_per_batch);
};

template<>
template<>
struct S2V_PER_GROUP_BATCH_INFO::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<S2V_COMPONENTS::UNIQUE_UBLKS> { using type = sHMBLK*; };

template<>
template<>
struct S2V_PER_GROUP_BATCH_INFO::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<S2V_COMPONENTS::N_INT_MSFLS> { using type = uINT16; };


/*===================================================================================
 * @struct cMSFL_S2V_BATCH_DATA
 * Given a MSFL group signature (GPU::sSURFEL_TABLE::KEY), this class retreive the
 * correct S2V_PER_GROUP_BATCH_INFO instance associated with that KEY
 *==================================================================================*/
namespace GPU {
struct MSFL_GROUP_KEY;
}

class MSFL_S2V_BATCH_DATA {

public:
  
  using KEY = GPU::MSFL_GROUP_KEY;

  //Singleton
  static MSFL_S2V_BATCH_DATA* get_instance();

  VOID add_group_h_s2v_data(const KEY& group_signature,
                            S2V_PER_GROUP_BATCH_INFO*);

  VOID add_group_d_s2v_data(const KEY& group_signature,
                            GPU::Ptr<S2V_PER_GROUP_BATCH_INFO>);

  const S2V_PER_GROUP_BATCH_INFO* get_group_h_s2v_data(const KEY& group_signature) const;
  GPU::Ptr<S2V_PER_GROUP_BATCH_INFO> get_group_d_s2v_data(const KEY& group_signature) const;

  VOID init_msfl_s2v_device_data_from_host();
  
private:
  std::map<KEY, S2V_PER_GROUP_BATCH_INFO*> h_s2v_per_group_batch_info;
  std::map<KEY, GPU::Ptr<S2V_PER_GROUP_BATCH_INFO>> d_s2v_per_group_batch_info;
};

VOID prepare_s2s_s2v_data_on_host(std::vector<sMSFL*> msfls,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                  size_t batch_size);

/*===================================================================================
 * @struct sMSFL_SURFEL_INTERACTIONS
 *==================================================================================*/

struct sMSFL_sMSFL_INTERACTION {

  uINT8 m_latvec;
  uINT8 m_even_odd_mask_lrf_src;
  uINT16 m_msfl_index;
  uINT8 m_src_sfl_index;
  uINT8 m_dst_sfl_index;
  STP_SURFEL_WEIGHT m_weight;

  constexpr static size_t N_EVEN_ODD_MASK_BITS = 4;

  __DEVICE__ sMSFL_sMSFL_INTERACTION():
    m_latvec(0),
    m_even_odd_mask_lrf_src(0),
    m_msfl_index(0),
    m_src_sfl_index(0),
    m_dst_sfl_index(0),
    m_weight(0) {}

  sMSFL_sMSFL_INTERACTION(uINT8 latvec,
                          uINT8 even_odd_mask,
                          bool is_lrf_src,
                          uINT16 src_msfl_index,
                          uINT8 src_sfl_index,
                          uINT8 dst_sfl_index,
                          STP_SURFEL_WEIGHT weight):
    m_latvec(latvec),
    m_even_odd_mask_lrf_src(even_odd_mask),
    m_msfl_index(src_msfl_index),
    m_src_sfl_index(src_sfl_index),
    m_dst_sfl_index(dst_sfl_index),
    m_weight(weight) {
    if (is_lrf_src) {
      m_even_odd_mask_lrf_src |= (0x1 << N_EVEN_ODD_MASK_BITS);
    }
  }

  sMSFL_sMSFL_INTERACTION(const sMSFL_sMSFL_INTERACTION&) = default;
  sMSFL_sMSFL_INTERACTION(sMSFL_sMSFL_INTERACTION&&) = default;
  sMSFL_sMSFL_INTERACTION& operator=(const sMSFL_sMSFL_INTERACTION&) = default;
  sMSFL_sMSFL_INTERACTION& operator=(sMSFL_sMSFL_INTERACTION&&) = default;

  __HOST__DEVICE__ constexpr static uINT8 even_odd_mask_mask() {
    return (0x1 << N_EVEN_ODD_MASK_BITS) - 1;
  }

  __HOST__DEVICE__ uINT8 even_odd_mask() const {
    return m_even_odd_mask_lrf_src & even_odd_mask_mask();
  }

  __HOST__DEVICE__ bool is_lrf_src() const {
    return m_even_odd_mask_lrf_src & (0x1 << N_EVEN_ODD_MASK_BITS);
  }

  __HOST__DEVICE__ VOID set_as_dummy() {
    this->m_weight = 0.0F;
  }

  __HOST__DEVICE__ BOOLEAN is_pde_2_interaction() const {
    return FALSE /*cannot have speed 2 interaction from a source sfl*/;
  }
};

enum class MSFL_MSFL_INT_COMPONENTS : int {
  UNIQUE_SFLS = 0,
  INTERACTIONS = 1,
  S2S_LP_PEERS = 2,
  S2S_PEERS = 3,
  N_COMPONENTS = 4
};

struct sMSFL_sMSFL_INTERACTIONS : public tOFFSET_BASED_INTERFACE<MSFL_MSFL_INT_COMPONENTS, unsigned, sMSFL_sMSFL_INTERACTIONS> {

public:

  using S2S_MASK = unsigned long long;
  //sMSFL** unique_sfls[m_n_unique_sfls]
  //sMSFL_sMSFL_INTERACTION* interactions[m_n_interactions]
  //uINT8* s2s_lp_peers[m_n_interactions]
  //S2V_mask* s2s_masks[m_n_interactions]

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sMSFL_sMSFL_INTERACTIONS* h_ptr);
  
};

template<>
template<>
struct sMSFL_sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_MSFL_INT_COMPONENTS::UNIQUE_SFLS> { using type = sMSFL*; };

template<>
template<>
struct sMSFL_sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_MSFL_INT_COMPONENTS::INTERACTIONS> { using type = sMSFL_sMSFL_INTERACTION; };

template<>
template<>
struct sMSFL_sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_MSFL_INT_COMPONENTS::S2S_LP_PEERS> { using type = uINT8; };

template<>
template<>
struct sMSFL_sMSFL_INTERACTIONS::OFFSET_INTERFACE_TYPE::COMPONENT_TRAITS<MSFL_MSFL_INT_COMPONENTS::S2S_PEERS> { using type = uINT64; };


extern std::vector<tBITSET<N_VOXELS_64>> g_mblk_adjust_v2s_dist_masks;
#endif
#endif
