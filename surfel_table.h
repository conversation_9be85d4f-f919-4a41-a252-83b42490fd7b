/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                      Created Tue, Mar 3, 2009
//----------------------------------------------------------------------------
#ifndef _SIMENG_SURFEL_TABLE_H_
#define _SIMENG_SURFEL_TABLE_H_

#include "common_sp.h"
#include "surfel.h"
#include "sampling_surfel.h"

//----------------------------------------------------------------------------
// SURFEL_TABLE
//----------------------------------------------------------------------------
typedef struct sSURFEL_TABLE 
{  
private:
public:
  SURFACE_SHOB    surfel_from_id(SHOB_ID id);
  SURFEL          regular_surfel_from_id(SHOB_ID id)   { return (SURFEL)surfel_from_id(id); }
  SAMPLING_SURFEL sampling_surfel_from_id(SHOB_ID id)  { return (SAMPLING_SURFEL)surfel_from_id(id); }
  SHOB_ID         n_surfels()                          { return m_surfels.size(); }
  SURFACE_SHOB    surfel(SHOB_ID index)                { return m_surfels[index]; }

  void    reserve(SHOB_ID n_surfels) { m_surfels.reserve(n_surfels); }
  void    trim()                     { m_surfels.shrink_to_fit(); }

  void add(SURFACE_SHOB surfel) 
  { 
    sINT64 id = surfel->id();
    // The surfels must be added to the table in ID order to allow for binary search
    // in surfel_from_id.
    if (id <= last_added_surfel_id)
      msg_internal_error("Surfel ID %ld not added to surfel table in ID order %ld", id, last_added_surfel_id);
    last_added_surfel_id = id;
    m_surfels.push_back(surfel);
  }

  sINT64  last_added_surfel_id;

protected:
  std::vector<SURFACE_SHOB> m_surfels;

}* SURFEL_TABLE;

extern sSURFEL_TABLE g_surfel_table[STP_N_REALMS];

inline SURFACE_SHOB    surfel_from_id (SHOB_ID id, REALM realm = STP_FLOW_REALM) { return g_surfel_table[realm].surfel_from_id(id);          }
inline SURFEL          regular_surfel_from_id (SHOB_ID id, REALM realm = STP_FLOW_REALM) { return g_surfel_table[realm].regular_surfel_from_id(id);  }
inline SAMPLING_SURFEL sampling_surfel_from_id(SHOB_ID id, REALM realm = STP_FLOW_REALM) { return g_surfel_table[realm].sampling_surfel_from_id(id); }

#ifdef BUILD_GPU
using sMSFL_SIZES_TABLE = tSHOB_SIZES_TABLE<sMSFL>;;
extern sMSFL_SIZES_TABLE g_msfl_sizes_table;
#endif

#endif /* _SIMENG_SURFEL_TABLE_H_ */
