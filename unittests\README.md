
# Simeng Unit Tests

Due to <PERSON>meng's complexity, there are quite a few hoops to jump through in
order to make them work. However, these initial growing pains will be rewarded
with code that is easier to maintain, easier to develop in, and has fewer bugs.

Unit testing forces developers to seriously consider the **contract** of a
single function or class. Given a set of inputs to a function, what are the
expected outputs? What are the expected side-effects? (This means how has the
global/class state changed due to the operation of the function!)

Unit testing provides an **entry-point** for new developers who want to
understand how a certain function/class is supposed to work. By looking at a
simplified example, it makes it easier to understand each piece and how they
are supposed to work together.

Unit testing allows for more confidence when modifying and maintaining the code
by letting the developer know that the contract of the function has not been
broken. If a developer fixes a bug in a function, the tests can be modified or
a new test added to reflect that change and ensure the bug stays squished.

Unit tests do not supercede the smoketests. They just provide a more focused
look at the code.

## Setup

1. Add the clang shared libraries to your LD_LIBRARY_PATH variable by placing
   the following line in your shell's startup script: (excluding the backticks)
   setenv LD_LIBRARY_PATH /fa/sw/llvm/5.0.1-01/gcc-5.1.0/lib64:$LD_LIBRARY_PATH
2. Or is you are using bash, use the following in your .bashrc:
   export LD_LIBRARY_PATH=/fa/sw/llvm/5.0.1-02/gcc-5.1.0/lib64:$LD_LIBRARY_PATH
3. Add cmake 3.10.3 to your PATH variable, or create an alias. This is done in
   a similar manner to the LD_LIBRARY_PATH above. The network location is:
   /fa/sw/cmake/3.10.3-01/linux-x86_64/bin

## Building the unit tests

Currently, due to some restrictions which are discussed below, all compilation
must take place on the `ion` build server.

In order to build the unittests:

1. ssh ion
2. setregistry
3. vcd simeng
4. cd unittests
5. Now make and cd into the appropriate build directory. These directories mimic
   the same pattern as the simeng component itself (e.g. amd64_linux_hpmpi_avx_dp).
6. cmake .. ; This runs cmake in the build directory. CMake then builds a Makefile
   which will build the unittests.
7. make
8. ctest ; This will execute all tests and report the results.

## Adding a unit test

In order to add a unit test, open the CMakeLists.txt file and add your test at 
the bottom using the add_simeng_test() macro. 

Note that in order to get a function or class under test, it may be necessary
to modify the function in order to remove dependencies on global state. Your
best tool is **dependency injection**, which is a technique that involves
eliminating all references to global state in the function by making them be
function arguments instead. As we slowly push back the tendrils of the global
state monster, it will enable refactorings and new ideas to create better
encapsulation and flexibility in the code.

These unit tests use GTest. More information can be found on their 
[Github repo](https://github.com/google/googletest). 

Each test source file is linked to gtest, which also provides the main function.
This main function runs all tests defines in the test source files and
reports the results. 

ctest is a "meta-tester" which maintains a list of all the tests and runs
all the separate executables.

Doing a VCC Release will erase all the unit testing build directories. This is
because each executable is very large (~10 MB). It is anticipated that
eventually there will be many executables, so it is considered prudent to erase
them at release time.

## Guidelines

* Test contracts, not implementations. Ideally, everything should have a public
  API that can be tested against, and any private functions can be ignored.
  This isn't possible in all cases, but it does make the test more fragile.
* Test a single **unit**. This is dependent on context. It could be a single
  function, or it could be an entire class. 
* Modify the actual source as little as possible. We want to test the code as
  if it is being run in a production environment.
* It will be necessary to refactor the code in order to get it to a state where
  it can actually be tested.
* If you fix a bug, write a test for it.
* If you want to refactor something, write a test for it, then refactor it.
* If you modify something, write a test for it.
* Run the tests often as you develop.
* Each test should only take a few milliseconds to run.

## Compiling restrictions

Currently, we have to build on ion because the Platform MPI library is located
on that machine, and because the Intel math libraries are located there.

If we can move these to the network, then we should be able to create fully
local builds.

