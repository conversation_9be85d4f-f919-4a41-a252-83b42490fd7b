/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Main Simulation Loop
 *
 * Vinit Gupta, Exa Corporation
 * Created Tue Sep 5 2017
 *--------------------------------------------------------------------------*/

#include "ublk_process_control.h"
#include "gpu_ublk_process_control.h"
#include "strand_mgr.h"
#include "mirror.h"
#include "gpu_host_include.h"
#include "vr_coalesce_explode.h"
#include <chrono>
#include <thread>
#include PHYSICS_H
#if BUILD_GPU
namespace GPU {
inline void explode_vr_groups_of_scale_templates(SCALE scale,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask,
                                                 UBLK_GROUP_TYPE group_type,
                                                 bool is_T_S_lb_solver_on,
                                                 bool is_uds_lb_solver_on,
                                                 bool process_ghost)
{

  if (is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
    GPU::explode_vr_groups_of_scale<TRUE, FALSE>(scale,
                                                 prior_solver_index_mask,
                                                 active_solver_mask,
                                                 group_type, process_ghost);
  } else if ( is_T_S_lb_solver_on && is_uds_lb_solver_on) {
    GPU::explode_vr_groups_of_scale<TRUE, TRUE>(scale,
                                                prior_solver_index_mask,
                                                active_solver_mask,
                                                group_type, process_ghost);
  } else if ( !is_T_S_lb_solver_on &&! is_uds_lb_solver_on) {
    GPU::explode_vr_groups_of_scale<FALSE, FALSE>(scale,
                                                  prior_solver_index_mask,
                                                  active_solver_mask, 
                                                  group_type, process_ghost);
  } else {
    GPU::explode_vr_groups_of_scale<FALSE, TRUE>(scale,
                                                 prior_solver_index_mask,
                                                 active_solver_mask,
                                                 group_type, process_ghost);       
  }
}

void explode_vr_groups_of_scale(SCALE scale,
                                SOLVER_INDEX_MASK prior_solver_index_mask,
                                ACTIVE_SOLVER_MASK active_solver_mask,
                                vr_fine_comm vr_fine_group_type,
                                bool is_T_S_lb_solver_on,
                                bool is_uds_lb_solver_on,
                                bool process_ghost) {
  switch(vr_fine_group_type) {
  case vr_fine_comm::no_comm:
    for(const auto group_type : nSHOB_CATEGORIES::VRFINE_NO_COMM_TYPES) {
      explode_vr_groups_of_scale_templates(scale, prior_solver_index_mask, active_solver_mask, group_type, is_T_S_lb_solver_on, is_uds_lb_solver_on, process_ghost);
    }
    break;
  case vr_fine_comm::near:
    explode_vr_groups_of_scale_templates(scale, prior_solver_index_mask, active_solver_mask, VRFINE_FRINGE_NEARBLK_GROUP_TYPE, is_T_S_lb_solver_on, is_uds_lb_solver_on, process_ghost);
    break;
  case vr_fine_comm::far:
    explode_vr_groups_of_scale_templates(scale, prior_solver_index_mask, active_solver_mask, VRFINE_FRINGE_FARBLK_GROUP_TYPE, is_T_S_lb_solver_on, is_uds_lb_solver_on, process_ghost);
    break;
  }
}

}

VOID cGPU_VR_EXPLODE_CONTROL::maybe_explode_vr_fine_groups_of_scale(STP_SCALE scale,
                                             SOLVER_INDEX_MASK prior_solver_index_mask,
                                                                    ACTIVE_SOLVER_MASK active_solver_mask,
                                                                    STRAND strand_type) {
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE);
  BOOLEAN is_T_S_lb_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE);
  BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);   

  // if(my_proc_id==1) printf("MPI(%d) : TS %d, maybe_explode_vr_fine_groups_of_scale(scale = %d, strand_type = %d)\n", my_proc_id, g_timescale.m_time, scale, strand_type);
  auto explode_once = [=](vr_fine_comm type, bool process_ghost) {
    if(!is_explode_done(scale, type, process_ghost))
      {
        // if(my_proc_id==1) printf("MPI(%d) : TS %d, maybe_explode_vr_fine_groups_of_scale() -> exploding vr type %d\n", my_proc_id, g_timescale.m_time, type);
        GPU::explode_vr_groups_of_scale(scale, prior_solver_index_mask, active_solver_mask,
                                        type, is_T_S_lb_solver_on, is_uds_lb_solver_on, process_ghost);
        mark_explode_done(scale, type, process_ghost);
      }
  };
  explode_once(vr_fine_comm::no_comm, false);
  explode_once(vr_fine_comm::far, false);
  explode_once(vr_fine_comm::near, false);

  if(
     strand_type == FRINGE_NEARBLK_A_STRAND ||
     strand_type == FRINGE_FARBLK_A_STRAND
     )
    {
      explode_once(vr_fine_comm::far, true);
    }

  if(
     strand_type == FRINGE_NEARBLK_A_STRAND ||
     strand_type == FRINGE_FARBLK_A_STRAND ||
     strand_type == SLIDING_NEARBLK_A_STRAND
     )
    {
      explode_once(vr_fine_comm::near, true);
    }
   
}
#endif
asINT32 get_timer_type_from_group(UBLK_GROUP group,
				  UBLK_GROUP_TYPE group_type) {

  asINT32 group_dyn_timer = group->get_fluid_dyn_timer();
  asINT32 fdyn_timer_type = -1;
  switch(group_type) {
  case INTERIOR_FARBLK1_GROUP_TYPE:
  case INTERIOR_NEARBLK_GROUP_TYPE:
  case SLIDING_NEARBLK_GROUP_TYPE:
    fdyn_timer_type = group_dyn_timer;
    break;
  case INTERIOR_FARBLK2_GROUP_TYPE:
    fdyn_timer_type = SP_FARBLK2_DYN_TIMER;
    break;
  case FRINGE_FARBLK_GROUP_TYPE:
    fdyn_timer_type = SP_FRINGE_FARBLK_DYN_TIMER;
    break;
  case FRINGE_NEARBLK_GROUP_TYPE:
    fdyn_timer_type = SP_FRINGE_NEARBLK_DYN_TIMER;
    break;
  case FRINGE2_NEARBLK_GROUP_TYPE:
    fdyn_timer_type = SP_FRINGE2_NEARBLK_DYN_TIMER;
    break;
  default:
    fdyn_timer_type = SP_INVALID_TIMER;
  }
  return fdyn_timer_type;
}

#if BUILD_GPU

cGPU_VR_EXPLODE_CONTROL g_vr_explode_control;

VOID sUBLK_PROCESS_CONTROL::process_all_ublks_in_group(MBLK_GROUP group,
                                                       UBLK_GROUP_TYPE group_type,
                                                       STRAND strand_type)
{

  if (group->n_shob_ptrs() == 0) {
    return;
  }

  if (sim.num_scales > 1 && group_type != INTERIOR_FARBLK1_GROUP_TYPE) {
    maybe_explode_vr_fine_groups_of_scale(strand_type);
  }
  
  BOOLEAN is_T_S_lb_solver_on = is_temp_active() && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);
  /*
  We set up the host dcache per scale and copy this information over to the GPU
  dcache. The kernel then sets up UBLK related dcache enteries.
  */
  FLUID_DYN_DCACHE h_dcache = ::sim_fluid_dyn_dcache;
  
  fluid_dyn_setup_per_scale(m_scale, h_dcache, m_prior_solver_index_mask, m_active_solver_mask);

#ifdef DEBUG_GPU

  int debug_mublk_id = 1957;
  auto debug_group_type = SLIDING_NEARBLK_GROUP_TYPE;
  int child_ublk_index = 4;
  int state_index = 0;
  sHMBLK* h_ublk = nullptr;
  if (my_proc_id == 0 && group_type == debug_group_type) {
    h_ublk = find_mblk_in_group(group, debug_mublk_id);
    //printf("h_ublk %d %s found in MPI(%d)\n", debug_mublk_id, (h_ublk? "":"NOT"), my_proc_id);
  }
#endif
  
#ifdef DEBUG_GPU
  if (group_type == debug_group_type) {
    //print_all_mblks_in_group(group, 0, "Before advect");
    //print_select_mblks_in_group(group, 0, "Before advect", {379, 380, 381, 382, 383});
  }
  if (h_ublk) {                                                                                                                                                                                                                                                                         
      std::ostringstream os;                                                                                                                                
      os << "\n";                                                                                                                                    
      print_mblk(h_ublk, child_ublk_index, state_index, "Before advect", std::cout); 
      LOG_MSG("GPU_GENERIC") << os.str();
      };                                                                                                         
      
#endif

  const auto range = GPU::get_ublk_group_range(group_type, m_scale, group->m_dest_sp.nsp());
 
  if (group_type == INTERIOR_FARBLK1_GROUP_TYPE) {
    cassert(sim.is_3d());
    GPU::swap_advect(group_type, is_T_S_lb_solver_on, is_uds_lb_solver_on, range.data(), m_prior_solver_index_mask);
  } else {
    GPU::gather_advect(group_type, is_T_S_lb_solver_on, is_uds_lb_solver_on,
                       range.data(), m_is_timestep_even,
                       m_prior_solver_index_mask,
                       m_active_solver_mask);
  }
    
#ifdef DEBUG_GPU
  if (group_type == debug_group_type) {
    //print_all_mblks_in_group(group, 0,"After advect");    
    //print_select_mblks_in_group(group, 0, "After advect", {379, 380, 381, 382, 383});    
  }  
  if (h_ublk) {                                                                                                                                                                                                                                                                                         
      std::ostringstream os;                                                                                                                                
      os << "\n";print_mblk(h_ublk, child_ublk_index, state_index, "After advect", std::cout); 
      LOG_MSG("GPU_GENERIC") << os.str(); 
      };
#endif

  if (m_scale != sim.finest_scale()) {
    GPU::vr_coarse_coalesce(group_type, range.data(), m_prior_solver_index_mask, m_active_solver_mask);
  }

#ifdef DEBUG_GPU  
  if (h_ublk) {                                                                                                                                                                                                                                                                 
      std::ostringstream os;                                                                                                                                
      os << "\n";
      print_mblk(h_ublk, child_ublk_index, state_index, "After coalesce", os); 
      LOG_MSG("GPU_GENERIC") << os.str(); };
#endif
  
  const bool do_measurements = GPU::ublk_group_has_measurements(group_type, m_scale, group->m_dest_sp.nsp());

  bool has_special_fluid = group->group_has_mblks_with_special_fluid();
  
  GPU::ublk_dynamics(group_type, m_scale, range.data(), h_dcache, do_measurements, has_special_fluid);

#ifdef DEBUG_GPU
  if (group_type == debug_group_type) {
    //print_all_mblks_in_group(group, 0, "After dynamics");
    //print_select_mblks_in_group(group, 0, "After dynamics", {379, 380, 381, 382, 383});
  }  
  if (h_ublk) {                                                                                                                                                                                                                                                                                 
      std::ostringstream os;                                                                                                                                
      os << "\n";
      print_mblk(h_ublk, child_ublk_index, state_index, "After dynamics", std::cout); 
      LOG_MSG("GPU_GENERIC") << os.str(); 
      };
#endif
  
  GPU::prepare_ublk_group_for_next_ts(group_type, range.data(),
				      h_dcache->c()->is_LB_active,
				      m_prior_solver_index_mask,
				      m_active_solver_mask);
}

#else

VOID sUBLK_PROCESS_CONTROL::process_all_ublks_in_group(UBLK_GROUP group, UBLK_GROUP_TYPE group_type,
						       STRAND strand_type)
{

  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  BOOLEAN is_T_S_lb_solver_on = is_temp_active() && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);
 
  DO_UBLKS_OF_GROUP(ublk, group) {
  if (!ublk->is_conduction_solid()) {
    if (m_is_2D && is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      ublk_adv_and_dyn< TRUE, TRUE, FALSE>(ublk, m_is_timestep_even,
				    m_prior_solver_index_mask,
				    m_active_solver_mask);
    } else if (m_is_2D && !is_T_S_lb_solver_on && !is_uds_lb_solver_on) {
      ublk_adv_and_dyn< TRUE, FALSE, FALSE>(ublk, m_is_timestep_even,
				     m_prior_solver_index_mask,
				     m_active_solver_mask);
    } else if (!m_is_2D && is_T_S_lb_solver_on  && !is_uds_lb_solver_on) {
      ublk_adv_and_dyn<FALSE, TRUE, FALSE>(ublk, m_is_timestep_even,
				    m_prior_solver_index_mask,
				    m_active_solver_mask);
    } else if (!m_is_2D && !is_T_S_lb_solver_on  && !is_uds_lb_solver_on) {
      ublk_adv_and_dyn<FALSE, FALSE, FALSE>(ublk, m_is_timestep_even,
				     m_prior_solver_index_mask,
				     m_active_solver_mask);
    } else if (m_is_2D && is_T_S_lb_solver_on && is_uds_lb_solver_on) {
      ublk_adv_and_dyn< TRUE, TRUE, TRUE>(ublk, m_is_timestep_even,
				    m_prior_solver_index_mask,
				    m_active_solver_mask);
    } else if (m_is_2D && !is_T_S_lb_solver_on && is_uds_lb_solver_on) {
      ublk_adv_and_dyn< TRUE, FALSE, TRUE>(ublk, m_is_timestep_even,
				     m_prior_solver_index_mask,
				     m_active_solver_mask);
    } else if (!m_is_2D && is_T_S_lb_solver_on  && is_uds_lb_solver_on) {
      ublk_adv_and_dyn<FALSE, TRUE, TRUE>(ublk, m_is_timestep_even,
				    m_prior_solver_index_mask,
				    m_active_solver_mask);
    } else if (!m_is_2D && !is_T_S_lb_solver_on  && is_uds_lb_solver_on) {
      ublk_adv_and_dyn<FALSE, FALSE, TRUE>(ublk, m_is_timestep_even,
				     m_prior_solver_index_mask,
				     m_active_solver_mask);
    }
  } else {
#if !BUILD_5G_LATTICE
  //DEBUG_IMPLICIT
  //msg_print("TS %d ublk dyns group_type %d", g_timescale.m_time, group_type);
    ublk_conduction_dyn(ublk,
                        m_prior_solver_index_mask,
                        m_active_solver_mask);
#endif
  }

    if (consider_surrender(strand_type)) {
      fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);
    }
  }  
}
#endif

#if !BUILD_GPU
VOID sUBLK_PROCESS_CONTROL::process_all_ublks(STRAND strand_type, UBLK_GROUP_TYPE group_type) {

  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);

  DO_UBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    
    if (group->m_send_group != NULL) {
      group->m_send_group->wait_for_copying_of_send_data();
    }

    if(sim.is_particle_model) {
      WITH_TIMER(SP_PARTICLE_VOXEL_DYN_TIMER) {
        DO_UBLKS_OF_GROUP(ublk, group) {
          particle_sim.particle_dynamics(ublk);
        }
      }
    }

    asINT32 fdyn_timer_type = get_timer_type_from_group(group, group_type);
    BOOLEAN time_group = (fdyn_timer_type == SP_INVALID_TIMER)? FALSE : TRUE;

    MAYBE_WITH_TIMER(time_group, fdyn_timer_type) {
      process_all_ublks_in_group(group, group_type, strand_type);
    }    

    if (group->m_send_group != NULL) {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);
      if ( sim.is_mme_comm_needed() ) {
        g_strand_mgr.m_send_queue->add_sp_entry(group->m_mme_send_group, m_active_solver_mask, false);
      }
      else {
        g_strand_mgr.m_send_queue->add_sp_entry(group->m_send_group, m_active_solver_mask, false);
      }
    }
  }
}

#else
VOID sUBLK_PROCESS_CONTROL::process_all_ublks(STRAND strand_type, UBLK_GROUP_TYPE group_type) {

  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);

  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "Goup type %d Strand %d", group_type, g_running_strand);

  DO_MBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    if (group->m_send_group) {
      group->m_send_group->wait_for_copying_of_send_data();
    }
    else
    {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "Send group NULL");
    }
    process_all_ublks_in_group(group, group_type, strand_type);
    if (group->m_send_group) {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);
      if ( sim.is_mme_comm_needed() ) {
        g_strand_mgr.m_send_queue->add_sp_entry(group->m_mme_send_group, m_active_solver_mask, FALSE);
      }
      else {
        g_strand_mgr.m_send_queue->add_sp_entry(group->m_send_group, m_active_solver_mask, FALSE);
      }
    }
  }
}
#endif

VOID sUBLK_PROCESS_CONTROL::process_all_ublks_dyn(STRAND strand_type, UBLK_GROUP_TYPE group_type) {

  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);
  DO_UBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {

    asINT32 group_dyn_timer = group->get_fluid_dyn_timer();
    asINT32 fdyn_timer_type = -1;
    BOOLEAN time_group = TRUE;

    switch(group_type) {
    case INTERIOR_FARBLK1_GROUP_TYPE:
    case INTERIOR_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = group_dyn_timer;
      break;
    case INTERIOR_FARBLK2_GROUP_TYPE:
      fdyn_timer_type = SP_FARBLK2_DYN_TIMER;
      break;
    case FRINGE_FARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE_FARBLK_DYN_TIMER;
      break;
    case FRINGE_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE_NEARBLK_DYN_TIMER;
      break;
    case FRINGE2_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE2_NEARBLK_DYN_TIMER;
      break;
    default:
      time_group = FALSE;
    }

    MAYBE_WITH_TIMER(time_group, fdyn_timer_type) {
      DO_UBLKS_OF_GROUP(farblk, group) {
        if (sim.is_movb_sim()) {	  
          if (!farblk->is_bsurfel_interacting()) {
#if BUILD_D19_LATTICE
	    if (!sim.is_pf_model)
#endif
	      continue; // skip ublks that do not interact with bsurfels
          }
          else {
            farblk->set_send_ib_bf_data();
          }
        }
        ublk_dyn_and_meas(farblk, m_is_timestep_even,
                          m_prior_solver_index_mask, m_active_solver_mask);
        if (consider_surrender(strand_type)) {
          fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);
        }
      }
    }

    if (group->m_send_group != NULL) {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);
#if BUILD_5G_LATTICE
      if (sim.is_large_pore) 
	g_strand_mgr.m_send_queue->add_sp_entry(group->m_pore_send_group, m_active_solver_mask, FALSE);
      else 
#endif  
	g_strand_mgr.m_send_queue->add_sp_entry(group->m_send_group, m_active_solver_mask, FALSE);
    }
  }
}

VOID sUBLK_PROCESS_CONTROL::advect_all_vr_fine_ublks(STRAND strand_type, UBLK_GROUP_TYPE group_type)
{
#if BUILD_GPU
  // Explode VR FINE groups of scale if it hasnt been already done on an even timestep


   //std::this_thread::sleep_for(std::chrono::milliseconds(500));
  // if(strand_type==INTERIOR_NS_A_STRAND && !m_is_timestep_even/*&& !g_vr_explode_control.are_vr_fine_groups_of_scale_exploded(m_scale)*/) {
  //   printf("MPI(%d) maybe_explode_vr_fine_groups_of_scale scale %d\n", my_proc_id, m_scale);
  //   while(g_vr_explode_control.is_recv_done(m_scale) == false) {}
  //   g_vr_explode_control.reset_recv_flag(m_scale);
  // }
  maybe_explode_vr_fine_groups_of_scale(strand_type);
  //Since previous step takes care of explode, no explode operation is performed in advection
  advect_vr_fine_ublks_internal<FALSE>(m_scale, m_active_solver_mask, m_prior_solver_index_mask,
                                       group_type, m_is_timestep_even);
#else  
  // explode is done on even timesteps
  if (m_is_timestep_even) {
    advect_vr_fine_ublks_internal<TRUE>(m_scale, m_active_solver_mask, m_prior_solver_index_mask,
                                        group_type, m_is_timestep_even);
  } else {
    advect_vr_fine_ublks_internal<FALSE>(m_scale, m_active_solver_mask, m_prior_solver_index_mask,
                                         group_type, m_is_timestep_even);
  }
#endif  
}

#if BUILD_5G_LATTICE
VOID sUBLK_PROCESS_CONTROL::construct_all_ublks_adv_states(STRAND strand_type, UBLK_GROUP_TYPE group_type)
{
  
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);

  asINT32 prev_lb_index = lb_index_from_mask(m_prior_solver_index_mask);
  asINT32 next_lb_index = 1 ^ prev_lb_index;

  DO_UBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {

    asINT32 group_dyn_timer = group->get_fluid_dyn_timer();
    asINT32 fdyn_timer_type = -1;
    BOOLEAN time_group = TRUE;

    switch(group_type) {
    case INTERIOR_FARBLK1_GROUP_TYPE:
    case INTERIOR_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = group_dyn_timer;
      break;
    case INTERIOR_FARBLK2_GROUP_TYPE:
      fdyn_timer_type = SP_FARBLK2_DYN_TIMER;
      break;
    case FRINGE_FARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE_FARBLK_DYN_TIMER;
      break;
    case FRINGE_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE_NEARBLK_DYN_TIMER;
      break;
    case FRINGE2_NEARBLK_GROUP_TYPE:
      fdyn_timer_type = SP_FRINGE2_NEARBLK_DYN_TIMER;
      break;
    default:
      time_group = FALSE;
    }

    MAYBE_WITH_TIMER(time_group, fdyn_timer_type) {      
      DO_UBLKS_OF_GROUP(farblk, group) {
	if (sim.is_scalar_model)
	  ublk_adv_states<TRUE>(farblk, next_lb_index);
	else
	  ublk_adv_states<FALSE>(farblk, next_lb_index);
	if (consider_surrender(strand_type)) {
          fluid_dyn_setup_per_scale(m_scale, dcache, m_prior_solver_index_mask, m_active_solver_mask);
        }
      }
    }
    
    if (group->m_send_group != NULL) {
       LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);
      g_strand_mgr.m_send_queue->add_sp_entry(group->m_send_group, m_active_solver_mask,
                                           sim.T_solver_type);
    }
  }

}
#endif
