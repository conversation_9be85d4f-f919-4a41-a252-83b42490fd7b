/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Bsurfel definition
 *
 * Nath Gopalaswamy, Exa Corporation
 * Created Tue Nov 3, 2015
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_BSURFEL_H
#define _SIMENG_BSURFEL_H

#include "common_sp.h"
#include "shob.h"
#include "eqns.h"
#include "meas.h"
#include "ublk.h"
#include "memory_pool.h"
#include "bsurfel_tire.h"
#include "particle_solver_data.h"

// forward declarations
struct sBSURFEL_UBLK_NEIGHBOR_INFO;
struct sMOVING_MEAS_CELL_SMART_PTR;
struct sBSURFEL_SEND_GROUP;
struct sBSURFEL_GROUP;

inline namespace SIMULATOR_NAMESPACE  //SIMULATOR NAMEPSACE, needed for simsizes
{

struct sBSURFEL : sSHOB {
  typedef tMEMORY_POOL<sBSURFEL, 1, 1000> sBSURFEL_MEM_POOL;
  typedef tMEMORY_POOL<sMOVING_MEAS_CELL_SMART_PTR> sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL;

  // These are used for storing where a bsurfel needs to be moved to.
  // See update_position() for more details.
  struct sSEND_LIST_QUANTUM {
    sBSURFEL * m_bsurfel;
    sBSURFEL_SEND_GROUP * m_send_group;
    void execute();
    sSEND_LIST_QUANTUM(sBSURFEL * bsurfel, sBSURFEL_SEND_GROUP * send_group) :
      m_bsurfel(bsurfel), m_send_group(send_group) {}
  };

  struct sMOVE_LIST_QUANTUM {
    sBSURFEL * m_bsurfel;
    sBSURFEL_GROUP * m_new_group;
    void execute();
    sMOVE_LIST_QUANTUM(sBSURFEL * bsurfel, sBSURFEL_GROUP * new_group) :
      m_bsurfel(bsurfel), m_new_group(new_group) {}
  };

  // NOTE: Adding any fields to the bsurfel would require modifications to the
  // init, and fill_send_buffer
  union {
    sTIRE_VERTEX_SMART_PTR m_vertices[3]; // 48 bytes
    struct {                              // 48 bytes
      sBG_POINT3d        m_init_centroid; // Corresponding to the initial position
      sBG_VECTOR3d       m_init_normal;   // Corresponding to the initial position
    };
  };
  STP_DGEOM_VARIABLE m_centroid[3];     // Varies with motion
  STP_DGEOM_VARIABLE m_normal[3];       // Varies with motion
  STP_DGEOM_VARIABLE m_area;
  sINT16             m_face_index;
  BOOLEAN8           m_is_active;

  sBSURFEL_GROUP*    m_group;
  sBSURFEL*          m_next;
  sBSURFEL*          m_prev;

  union {
    // used for looking up the home ublk during initialization and migration of bsurfels between SPs
    // converted from id to pointer after successful lookup
    STP_UBLK_ID m_home_ublk_id;
    sUBLK*      m_home_ublk;
  };

  STP_PHYS_VARIABLE m_vel[3];
  STP_PHYS_VARIABLE m_next_vel[3];
#if BUILD_D19_LATTICE
  STP_PHYS_VARIABLE m_centrifugal_force[3];  //PF
#endif
  uINT8             m_home_voxel;
  sINT16            m_n_meas_cell_refs;
  sMOVING_MEAS_CELL_SMART_PTR *m_meas_cell_refs;

  MOVB_PHYSICS_DESCRIPTOR m_movb_physics_desc;
  LRF_PHYSICS_DESCRIPTOR m_lrf_physics_desc;

  sBSURFEL()
  {
    m_group = nullptr;
    m_next = nullptr;
    m_prev = nullptr;
  };

  ~sBSURFEL();

  sBSURFEL(const sBSURFEL&) = delete;
  sBSURFEL(sBSURFEL&&) = delete;
  sBSURFEL& operator = (const sBSURFEL&) = delete;
  sBSURFEL& operator = (sBSURFEL&&) = delete;

  UBLK home_ublk() { return m_home_ublk; }
  uINT8 home_voxel() { return m_home_voxel; }

  STP_DGEOM_VARIABLE centroid(uINT8 axis) { return m_centroid[axis]; }
  STP_DGEOM_VARIABLE* centroid() {return m_centroid;}
  STP_DGEOM_VARIABLE normal(uINT8 axis) { return m_normal[axis]; }
  STP_DGEOM_VARIABLE * normal() { return m_normal; }

  // sampled quantities for measurements
  dFLOAT m_sampled_fluid_velocity[3];
  dFLOAT m_sampled_fluid_density;
  dFLOAT m_sampled_fluid_temp;
  dFLOAT m_sampled_uds_value[MAX_N_USER_DEFINED_SCALARS];
  dFLOAT m_sampled_tke;
  dFLOAT m_sampled_eps;
  dFLOAT m_computed_body_force[3];
#if BUILD_D19_LATTICE
  dFLOAT m_computed_body_force_pfld[3];     //PF
  dFLOAT m_sampled_fluid_velocity_pfld[3];  //PF
  dFLOAT m_sampled_fluid_pressure_pfld;     //PF
#endif

  bool is_active() { return m_is_active; }
  bool is_deforming() { return m_movb_physics_desc->is_deforming(); }

  void set_group(sBSURFEL_GROUP * group)
  {
    m_group = group;
  }

  VOID write_ckpt();

  VOID read_ckpt();

  VOID init(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom,
            sUBLK_TABLE& ublk_table,
            dFLOAT char_density,
            sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs,
            sMOVB_PHYSICS_DESCRIPTOR* movb_physics_descs,
            sMEAS_WINDOW_COLLECTION& meas_windows);

  VOID init(std::vector<unsigned char>::iterator &recv_buffer,
            sUBLK_TABLE& ublk_table,
            dFLOAT char_density,
            sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs,
            sMOVB_PHYSICS_DESCRIPTOR* movb_physics_descs,
            sMEAS_WINDOW_COLLECTION& meas_windows);

  VOID reset()
  {
    m_sampled_fluid_velocity[0] = 0;
    m_sampled_fluid_velocity[1] = 0;
    m_sampled_fluid_velocity[2] = 0;
    m_sampled_fluid_density = sim.char_density;
    m_sampled_fluid_temp = sim.char_temp;
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
      m_sampled_uds_value[nth_uds] = sim.char_uds;
    m_sampled_tke = 0;
    m_sampled_eps = 0;
    m_computed_body_force[0] = 0;
    m_computed_body_force[1] = 0;
    m_computed_body_force[2] = 0;
#if BUILD_D19_LATTICE
    m_computed_body_force_pfld[0] = 0;
    m_computed_body_force_pfld[1] = 0;
    m_computed_body_force_pfld[2] = 0;
    m_sampled_fluid_velocity_pfld[0] = 0;
    m_sampled_fluid_velocity_pfld[1] = 0;
    m_sampled_fluid_velocity_pfld[2] = 0;
    m_sampled_fluid_pressure_pfld = sim.char_pressure_f / g_density_scale_factor;
#endif
  }

  VOID reinit(PHYSICS_DESCRIPTOR pd) { }
  VOID check_parms_bounds(PHYSICS_DESCRIPTOR pd) { }

  VOID seed(BOOLEAN is_full_checkpoint_restore) { }

  BSURFEL_GROUP_TYPE activate_bsurfel_neighbor_ublks(bool check_all_ublks_are_fringe = false);

  VOID update_velocity(dFLOAT angular_vel_vec[3], dFLOAT ref_pt[3])
  {
    if (g_timescale.m_time != 0 && is_deforming()) {
      for(int i=0; i<3; i++) {
        m_vel[i] = m_next_vel[i];
#if BUILD_D19_LATTICE
	m_centrifugal_force[i] = 0.0;
#endif
      }
    }
    else {
      dFLOAT rvec[3];
      vsub(rvec, m_centroid, ref_pt);
      vcross(m_vel, angular_vel_vec, rvec);
#if BUILD_D19_LATTICE
      vcross(m_centrifugal_force, angular_vel_vec, m_vel);
#endif
    }
  }

  VOID calculate_ref_frame_vel_and_pt(dFLOAT ref_frame_vel[3], dFLOAT ref_pt[3]);

  VOID fill_send_buffer(cNEIGHBOR_SP nsp, std::vector<unsigned char> &send_buffer);

  VOID update_position(std::vector<sSEND_LIST_QUANTUM> &send_list,
                       std::vector<sMOVE_LIST_QUANTUM> &move_list);

  VOID validate_physics_desc()
  {
    m_movb_physics_desc->check_consistency();
  }

  static
  void* operator new (size_t size)
  {
    sBSURFEL_MEM_POOL & pool = get_bsurfel_mem_pool();
    return pool.pop();
  }

  static
  void operator delete (void * ptr)
  {
    sBSURFEL_MEM_POOL & pool = get_bsurfel_mem_pool();
    pool.push(ptr);
  }

  VOID maybe_add_self_to_move_list(BSURFEL_GROUP_TYPE new_group_type,
                                   std::vector<sMOVE_LIST_QUANTUM> &move_list);

  VOID move_self_to_new_group(BSURFEL_GROUP_TYPE new_group_type);

  // Immersed Boundary Functions
  VOID compute_body_force();

  template<typename INFO>
  VOID interpolate_fluid_properties(std::vector<INFO> &b_info);

  template<typename INFO>
  VOID distribute_body_force(std::vector<INFO> &b_info);

  template<typename INFO>
  VOID normalize_distributed_body_force(std::vector<INFO> &b_info, dFLOAT sampled_force_per_area[3]);

  VOID deactivate_if_in_solid_voxel();
#if 0
  //We will need to spend some memory on particle modeling variables to solve PR47746 regarding particle measurements on bsurfels.
  struct sBSURFEL_PARTICLE_DATA {
    //These variables are used for accumulating particle statistics on a bsurfel
    uINT64 num[NUM_PARCEL_STATES];
    sPARTICLE_VAR mass[NUM_PARCEL_STATES];
    sPARTICLE_VAR sum_density[NUM_PARCEL_STATES];
    //sPARTICLE_VAR sum_density_flux;
    sPARTICLE_VAR sum_diameter[NUM_PARCEL_STATES];
    sPARTICLE_VAR sum_particle_impulse[N_SPACE_DIMS];
  } m_particle_data;
  sBSURFEL_PARTICLE_DATA* p_data() {return &m_particle_data;}
#endif
private:

  STP_PROC locate_new_home_ublk_and_voxel();

  VOID add_self_to_send_list(std::vector<sSEND_LIST_QUANTUM> &send_list);
  VOID add_self_to_send_list_from_box(std::vector<sSEND_LIST_QUANTUM> &send_list, STP_PROC dest_rank);

  static sBSURFEL_MEM_POOL & get_bsurfel_mem_pool()
  {
    static sBSURFEL_MEM_POOL mem_pool;
    return mem_pool;
  }

  


  static sMOVING_MEAS_CELL_SMART_PTR_MEM_POOL & get_moving_meas_cell_smart_ptr_mem_pool(sINT16 n_meas_cell_refs);

  


};

} //SIMULATOR NAMEPSACE, needed for simsizes
#endif  /* _SIMENG_BSURFEL_H */

