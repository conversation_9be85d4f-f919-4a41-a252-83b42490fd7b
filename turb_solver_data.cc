/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#include "turb_solver_data.h"
#include "ublk.h"
#include "sim.h"
#include "surfel.h"
#include "sampling_surfel.h"

template<>
VOID sSURFEL_TURB_DATA::pre_advect_init_copy_even_to_odd(SURFEL_TURB_DATA even_surfel_turb_data, 
                                                         STP_PHYS_VARIABLE even_density,
                                                         STP_SURFEL_WEIGHT mme_weight_inverse, 
                                                         STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) 
{
}

template<>
VOID sSURFEL_TURB_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_TURB_DATA mirror_turb_data,  
                                                                  STP_LATVEC_MASK latvec_state_mask,
                                                                  //STP_STATE_INDEX reflected_states[N_STATES],
                                                                  STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                                  STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
#if BUILD_6X_SOLVER
  s_mag      += mirror_turb_data->s_mag;
#endif
  gamma_swirl += mirror_turb_data->gamma_swirl;
}

template<>
VOID sSURFEL_TURB_DATA::reflect_from_real_surfel_to_mirror_surfel(sSURFEL_TURB_DATA *source_turb_data, 
								  STP_LATVEC_MASK latvec_state_mask,
								  //STP_STATE_INDEX reflected_states[N_STATES],
								  STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
								  STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  ltt_index = source_turb_data->ltt_index;
}


VOID sSAMPLING_SURFEL_TURB_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_TURB_DATA even_surfel_turb_data)
{
  memcpy(this, even_surfel_turb_data, sizeof(*this));
}

#if BUILD_GPU
inline namespace SIMULATOR_NAMESPACE {
INIT_MSFL(tSURFEL_V2S_TURB_DATA) {
  COPY_SFL_TO_MSFL(m_nu);
  COPY_SFL_TO_MSFL(m_tke_pde);
  COPY_SFL_TO_MSFL(m_eps_pde);
  COPY_SFL_TO_MSFL(m_tke_sample);  
}

INIT_MSFL(tBC_TURB_DATA) {
  COPY_SFL_TO_MSFL(u.k.turb_kinetic_energy);
  COPY_SFL_TO_MSFL(u.k.turb_dissipation);
}

INIT_MSFL(tSURFEL_TURB_DATA)
{
  COPY_SFL_TO_MSFL(ltt_index);
  COPY_SFL_TO_MSFL(s_mag);
  COPY_SFL_TO_MSFL(gamma_swirl);
  COPY_SFL_TO_MSFL(dpds_ur);
  COPY_SFL_TO_MSFL(tke_sample_prime);
  COPY_SFL_TO_MSFL(m_bc_turb_data.u.k.turb_kinetic_energy);
  COPY_SFL_TO_MSFL(m_bc_turb_data.u.k.turb_dissipation);  
}
}
#endif
