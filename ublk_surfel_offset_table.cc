/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "surfel.h"
#include "ublk.h"
#include PHYSICS_H
#include <iostream>
#include <fstream>
#include SIMSIZES_SHARED_H

//Definition
asINT32 SIMULATOR_NAMESPACE::g_surfel_data_offset_table[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];
asINT32 SIMULATOR_NAMESPACE::g_surfel_dynamics_data_sizes[N_MAX_SURFEL_PHYSICS_TYPES];

uINT16 SIMULATOR_NAMESPACE::g_ublk_data_offset_table_8[N_UBLK_TYPES][N_UBLK_DATA_TYPES];

#ifdef BUILD_GPU
uINT16 SIMULATOR_NAMESPACE::g_ublk_data_offset_table_64[N_UBLK_TYPES][N_UBLK_DATA_TYPES];
asINT32 SIMULATOR_NAMESPACE::g_surfel_data_offset_table_64[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];
uINT16  SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_64[N_MAX_UBLK_PHYSICS_TYPES];
#endif

uINT16  SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_8[N_MAX_UBLK_PHYSICS_TYPES];

VOID SIMULATOR_NAMESPACE::save_surfel_table_to_file(BOOLEAN is_sim_sizes) {

  std::ofstream ofs;

  if(is_sim_sizes) {
    ofs.open("simsizes_sfl_table.txt");
  } else {
    ofs.open("simeng_sfl_table.txt");
  }

  if (!ofs){
    std::cerr << "Could not open file for writing table\n";
    return;
  }

  //Data offset table
  ofs << "SURFEL DATAOFSET TABLE\n";
  ccDOTIMES(stype,N_MAX_SURFEL_TYPES){
    ofs << "Surfel Type : " << stype << std::endl;    
    ccDOTIMES(dtype,N_MAX_SURFEL_DATA_TYPES){
      ofs << g_surfel_data_offset_table[stype][dtype] << ", ";
      if (dtype%6 == 0){
	ofs << std::endl;
      }
    }
    ofs << std::endl;
  }

  //Dynamcis table
  ofs << "\n\nSURFEL DYNAMCIS DATA SIZES\n";
  ccDOTIMES(ptype,N_MAX_SURFEL_PHYSICS_TYPES){
    ofs << g_surfel_dynamics_data_sizes[ptype] << ", ";
    if (ptype%6 == 0){
      ofs << std::endl;
    }    
  }
}

#ifdef GPU_WONT_COMPILE
VOID SIMULATOR_NAMESPACE::save_ublk_table_to_file(BOOLEAN is_sim_sizes) {

  std::ofstream ofs;

  if(is_sim_sizes) {
    ofs.open("simsizes_ublk_table.txt");
  } else {
    ofs.open("simeng_ublk_table.txt");
  }

  if (!ofs){
    std::cerr << "Could not open file for writing UBLK table\n";
    return;
  }

  //Debug
  ofs << "sizeof(sUBLK) " << sizeof(sUBLK) << std::endl;
  size_t ublk_size = 0;
  if ((0 >> TWO_STATE_COPIES_BIT) & 1) {
    size_t alignment = get_alignment_of<sUBLK_TWO_STATES_DATA>();
    ublk_size =  get_byte_aligned(ublk_size, alignment) + sizeof(sUBLK_TWO_STATES_DATA);
    ofs << "states2 " <<sizeof(sUBLK_TWO_STATES_DATA)<< std::endl;
    ofs << "align + 2states" << ublk_size << std::endl;
  } else {
    size_t alignment = get_alignment_of<sUBLK_STATES_DATA>();
    ublk_size = get_byte_aligned(ublk_size, alignment) + sizeof(sUBLK_STATES_DATA);
     ofs << "state1 " <<sizeof(sUBLK_STATES_DATA)<< std::endl;
    ofs << "align + 1states" << ublk_size << std::endl;
  }

    
  size_t alignment = get_alignment_of<sUBLK_TURB_DATA>();
  ublk_size = get_byte_aligned(ublk_size, alignment) + sizeof(sUBLK_TURB_DATA);
  ofs << "turb " << sizeof(sUBLK_TURB_DATA) << std::endl;
  ofs << "align + turb" << ublk_size << std::endl;

  
  //Data offset table
  ofs << "UBLK DATAOFSET TABLE\n";
  ccDOTIMES(utype,N_UBLK_TYPES){
    ofs << "Ublk Type : " << utype << std::endl;    
    ccDOTIMES(dtype,N_UBLK_DATA_TYPES){
      ofs << tUBLK::set_ublk_data_offset(utype,dtype) << ", ";
      if (dtype%6 == 0){
	ofs << std::endl;
      }
    }
    ofs << std::endl;
  }

  //Dynamcis table
  ofs << "\n\nUBLK DYNAMCIS DATA SIZES\n";
  ccDOTIMES(ptype,N_MAX_UBLK_PHYSICS_TYPES){
    ofs << g_ublk_dynamics_data_size[ptype] << ", ";
    if (ptype%6 == 0){
      ofs << std::endl;
    }    
  }
}
#endif

inline namespace SIMULATOR_NAMESPACE
{
template<typename tSURFEL>
static
VOID initialize_surfel_dynamics_data_sizes() {
  using SFL_TYPE_TAG = typename tSURFEL::TAG;
  static_assert(nSHOB_CATEGORIES::N_DYN_SURFEL_FLOW_TYPES <= N_MAX_SURFEL_PHYSICS_TYPES,
                "Number of surfel dynamics types is greater than allowed");

#if BUILD_5G_LATTICE
  // Adiabatic wall types
  g_surfel_dynamics_data_sizes[NOSLIP_SURFEL_TYPE]                         = sizeof(sNOSLIP_5G_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_NOSLIP_SURFEL_TYPE]                 = sizeof(sANGULAR_NOSLIP_5G_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_NOSLIP_SURFEL_TYPE]                  = sizeof(sLINEAR_NOSLIP_5G_SURFEL_DATA);

  // Pressure outlet
  g_surfel_dynamics_data_sizes[STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE]       = sizeof(sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_DATA);

  // inlets
  g_surfel_dynamics_data_sizes[SOURCE_SURFEL_TYPE]                         = sizeof(sSOURCE_5G_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[MASS_FLUX_SURFEL_TYPE]                      = sizeof(sMASS_FLUX_5G_SURFEL_DATA);

#else
  // Adiabatic wall types
  g_surfel_dynamics_data_sizes[SLIP_SURFEL_TYPE]                           = sizeof(tSLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[NOSLIP_SURFEL_TYPE]                         = sizeof(tNOSLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[ANGULAR_SLIP_SURFEL_TYPE]                   = sizeof(tANGULAR_SLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[LINEAR_SLIP_SURFEL_TYPE]                    = sizeof(tLINEAR_SLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[VEL_SLIP_SURFEL_TYPE]                       = sizeof(tVEL_SLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[ANGULAR_NOSLIP_SURFEL_TYPE]                 = sizeof(tANGULAR_NOSLIP_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[LINEAR_NOSLIP_SURFEL_TYPE]                  = sizeof(tLINEAR_NOSLIP_SURFEL_DATA<SFL_TYPE_TAG>);

  // thermal resist wall types
  g_surfel_dynamics_data_sizes[SLIP_THERMAL_RESIST_SURFEL_TYPE]            = sizeof(sSLIP_THERMAL_RESIST_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[NOSLIP_THERMAL_RESIST_SURFEL_TYPE]          = sizeof(sNOSLIP_THERMAL_RESIST_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE]    = sizeof(sANGULAR_SLIP_THERMAL_RESIST_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE]     = sizeof(sLINEAR_SLIP_THERMAL_RESIST_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE]  = sizeof(sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE]   = sizeof(sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_DATA);

  // isothermal (fixed temperature) wall types
  g_surfel_dynamics_data_sizes[SLIP_FIXED_TEMP_SURFEL_TYPE]                = sizeof(sSLIP_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[NOSLIP_FIXED_TEMP_SURFEL_TYPE]              = sizeof(sNOSLIP_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE]        = sizeof(sANGULAR_SLIP_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE]         = sizeof(sLINEAR_SLIP_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE]      = sizeof(sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE]       = sizeof(sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_DATA);

  // heat flux wall types
  g_surfel_dynamics_data_sizes[SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE]           = sizeof(sSLIP_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE]         = sizeof(sNOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE]   = sizeof(sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE]    = sizeof(sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE] = sizeof(sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE]  = sizeof(sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_DATA);

  // outlets pressure
  g_surfel_dynamics_data_sizes[STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE]       = sizeof(tSTATIC_PRESSURE_FREE_DIR_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE]      = sizeof(tSTATIC_PRESSURE_FIXED_DIR_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[STAG_PRESSURE_FREE_DIR_SURFEL_TYPE]         = sizeof(tSTAG_PRESSURE_FREE_DIR_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE]        = sizeof(tSTAG_PRESSURE_FIXED_DIR_SURFEL_DATA<SFL_TYPE_TAG>);

  // inlets (velocity, mass flux, mass flow)
  g_surfel_dynamics_data_sizes[MASS_FLUX_SURFEL_TYPE]                      = sizeof(tMASS_FLUX_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[MASS_FLOW_SURFEL_TYPE]                      = sizeof(tMASS_FLOW_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[FIXED_VEL_SURFEL_TYPE]                      = sizeof(tFIXED_VEL_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[TURB_VEL_SURFEL_TYPE]                       = sizeof(sTURB_VEL_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[SOURCE_SURFEL_TYPE]                         = sizeof(tSOURCE_SURFEL_DATA<SFL_TYPE_TAG>);
  g_surfel_dynamics_data_sizes[TURB_SOURCE_SURFEL_TYPE]                    = sizeof(sTURB_SOURCE_SURFEL_DATA);


  g_surfel_dynamics_data_sizes[PASS_THRU_SURFEL_TYPE]                      = sizeof(sPASS_THRU_SURFEL_DATA);

  // sampling surfels
  g_surfel_dynamics_data_sizes[SAMPLING_DYN_SURFEL_TYPE]                   = sizeof(sSURFEL_DYNAMICS_DATA);

  // LRF surfels
  g_surfel_dynamics_data_sizes[SLRF_SURFEL_TYPE]                           = sizeof(sSURFEL_DYNAMICS_DATA);

  // Conduction surfels
  g_surfel_dynamics_data_sizes[CONDUCTION_ADIABATIC_SURFEL_TYPE]           = sizeof(sCONDUCTION_ADIABATIC_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE] = sizeof(sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[CONDUCTION_FIXED_TEMP_SURFEL_TYPE]          = sizeof(sCONDUCTION_FIXED_TEMP_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE]     = sizeof(sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE]     = sizeof(sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA);
  g_surfel_dynamics_data_sizes[CONDUCTION_CONTACT_SURFEL_TYPE]             = sizeof(sCONDUCTION_CONTACT_SURFEL_DATA);

#endif
}
}

template<typename tSURFEL>
GNU_FUNC_ATTR(no_sanitize("undefined"))
VOID SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table(const DATA_OFFSET_TABLE_OPTS& opts) {

  initialize_surfel_dynamics_data_sizes<tSURFEL>();

  // To ensure that the size of the offset table is sufficiently large
  static_assert((1 << N_SURFEL_DATA_TYPE_BITS) <= N_MAX_SURFEL_TYPES,
                "Too many bits used to describe a surfel");
  
  static_assert(SURFEL_DYN_DATA_TYPE <= N_MAX_SURFEL_DATA_TYPES,
                "Number of surfel data blocks is more than allowed");

  for (asINT32 surfel_type = 0; surfel_type < N_MAX_SURFEL_TYPES; surfel_type++) {
    size_t surfel_size = sizeof(tSURFEL);
    if (opts.is_lb_model) {
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_LB_DATA_TYPE) = struct_field_disp(tSURFEL*, m_lb_data);
    }
    if (opts.is_turb_model) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_TURB_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_TURB_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_TURB_DATA_TYPE) +
	            sizeof(typename tSURFEL::sSURFEL_TURB_DATA);
    }
    if ((surfel_type >> TWO_SURFEL_STATES_DATA_BIT) & 1) {
      if (opts.is_lb_model) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_S2S_LB_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_OUTFLUX_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_OUTFLUX_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_S2S_LB_DATA);
      }
      if (opts.is_heat_transfer || opts.switch_acous_during_simulation) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_S2S_T_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_OUTFLUX_T_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_OUTFLUX_T_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_S2S_T_DATA);
      }
      if (opts.is_multi_component) {
	sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_S2S_MC_DATA>();
        g_surfel_data_offset_table[surfel_type][SURFEL_OUTFLUX_MC_DATA_TYPE] = get_byte_aligned(surfel_size, alignment);
        surfel_size = g_surfel_data_offset_table[surfel_type][SURFEL_OUTFLUX_MC_DATA_TYPE] + sizeof(typename tSURFEL::sSURFEL_S2S_MC_DATA);
      }
      if (opts.is_scalar_model && opts.use_uds_lb_solver){
        asINT32 n_scalars = opts.n_user_defined_scalars;
        sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_S2S_UDS_DATA>();
        g_surfel_data_offset_table[surfel_type][SURFEL_OUTFLUX_UDS_DATA_TYPE] = get_byte_aligned(surfel_size, alignment);
        surfel_size = g_surfel_data_offset_table[surfel_type][SURFEL_OUTFLUX_UDS_DATA_TYPE] + n_scalars * sizeof(typename tSURFEL::sSURFEL_S2S_UDS_DATA);
      }
    }
    if (opts.is_heat_transfer || opts.switch_acous_during_simulation) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_T_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_T_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_T_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_T_DATA);
    }
    if (opts.is_conduction_model) {
#if !BUILD_GPU
      if ((surfel_type >> SURFEL_CONDUCTION_DATA_BIT) & 1) {
        sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_CONDUCTION_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_CONDUCTION_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_CONDUCTION_DATA);
      }
      if ((surfel_type >> SURFEL_SHELL_CONDUCTION_DATA_BIT) & 1) {
        sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_SHELL_CONDUCTION_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_SHELL_CONDUCTION_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_SHELL_CONDUCTION_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_SHELL_CONDUCTION_DATA);
      }
      if (((surfel_type >> SURFEL_SHELL_CONDUCTION_DATA_BIT) & 1) && opts.use_implicit_shell_solver) {
        sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA);
      }
      if ((surfel_type >> SURFEL_CONDUCTION_INTERFACE_DATA_BIT) & 1) {
        if ((surfel_type >> SURFEL_CONDUCTION_DATA_BIT) & 1) { 
          //conduction closed shell
          sINT16 alignment = get_alignment_of<typename tSURFEL::sCONDUCTION_INTERFACE_SOLID_DATA>();
          tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
          surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE) + sizeof(typename tSURFEL::sCONDUCTION_INTERFACE_SOLID_DATA);
        } else if ((surfel_type >> SURFEL_SHELL_CONDUCTION_DATA_BIT) & 1) { 
          //conduction open shell
          sINT16 alignment = get_alignment_of<typename tSURFEL::sCONDUCTION_INTERFACE_OPEN_SHELL_DATA>();
          tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_SHELL_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
          surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_SHELL_DATA_TYPE) + sizeof(typename tSURFEL::sCONDUCTION_INTERFACE_OPEN_SHELL_DATA);
        } else { 
          //fluid
          sINT16 alignment = get_alignment_of<typename tSURFEL::sCONDUCTION_INTERFACE_FLUID_DATA>();
          tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_FLUID_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
          surfel_size =  tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_CONDUCTION_INTERFACE_FLUID_DATA_TYPE) + sizeof(typename tSURFEL::sCONDUCTION_INTERFACE_FLUID_DATA);
        }
      }
#endif
    }

    if (opts.is_radiation_model) {
#if !BUILD_GPU
      if ((surfel_type >> SURFEL_RADIATION_DATA_BIT) & 1) {
        sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_RADIATION_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_RADIATION_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type,SURFEL_RADIATION_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_RADIATION_DATA);

        // If this surfel is a shell and does not interact with a conduction voxel
        // Then it must be an open shell, and we need some more radiation data
        if ((surfel_type >> SURFEL_SHELL_CONDUCTION_DATA_BIT) & 1) {
          if (! ((surfel_type >> SURFEL_CONDUCTION_DATA_BIT) & 1)) {
            // conduction open shell, add back face of radiation data
            sINT16 alignment = get_alignment_of<typename tSURFEL::sSURFEL_RADIATION_DATA>();
            surfel_size = get_byte_aligned(surfel_size, alignment);
            surfel_size += sizeof(typename tSURFEL::sSURFEL_RADIATION_DATA);
          }
        }
      }
#endif
    }

    if (opts.is_scalar_model) {
      asINT32 n_scalars = opts.n_user_defined_scalars;
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_UDS_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_UDS_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_UDS_DATA_TYPE) + n_scalars * sizeof(typename tSURFEL::sSURFEL_UDS_DATA);
#if BUILD_D19_LATTICE
      if (opts.is_pf_model) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_PF_DATA>();
	tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_PF_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
	surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_PF_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_PF_DATA);
      }
#endif
    }
    if (opts.is_multi_component) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_MC_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MC_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MC_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_MC_DATA);
    }
#if BUILD_D19_LATTICE
    if(opts.store_frozen_vars) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_FROZEN_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_FROZEN_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_FROZEN_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_FROZEN_DATA);
    }
#endif
    if (opts.is_particle_model) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_PARTICLE_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_P_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_P_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_PARTICLE_DATA);
    }
    if ((surfel_type >> S2S_ADVECT_DATA_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tSURFEL::sS2S_ADVECT_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_S2S_ADVECT_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_S2S_ADVECT_DATA_TYPE) + sizeof(typename tSURFEL::sS2S_ADVECT_DATA);
    }
    if ((surfel_type >> SURFEL_MIRROR_DATA_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_MIRROR_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MIRROR_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MIRROR_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_MIRROR_DATA);
    }
    if ((surfel_type >> SURFEL_MLRF_DATA_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_MLRF_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MLRF_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_MLRF_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_MLRF_DATA);
    }
    if ((surfel_type >> SURFEL_EVEN_ODD_DATA_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_EVEN_ODD_DATA>();
      tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_EVEN_ODD_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
      surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_EVEN_ODD_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_EVEN_ODD_DATA);
    }
    if ((surfel_type >> SURFEL_V2S_DATA_BIT) & 1) {
      if (opts.is_lb_model) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_V2S_LB_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_LB_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_LB_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_V2S_LB_DATA);
      }
      if (opts.is_turb_model) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_V2S_TURB_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_TURB_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_TURB_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_V2S_TURB_DATA);
      }
      if (opts.is_heat_transfer || opts.switch_acous_during_simulation) {
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_V2S_T_DATA>();
        tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_T_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
        surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_T_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_V2S_T_DATA);
      }
      if (opts.is_scalar_model) {
        asINT32 n_scalars = opts.n_user_defined_scalars;
        size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_V2S_UDS_DATA>();
	tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_UDS_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
	surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_UDS_DATA_TYPE) + n_scalars * sizeof(typename tSURFEL::sSURFEL_V2S_UDS_DATA);
#if BUILD_D19_LATTICE
	if (opts.is_pf_model) {
          size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_V2S_PF_DATA>();
	  tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_PF_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
	  surfel_size = tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_V2S_PF_DATA_TYPE) + sizeof(typename tSURFEL::sSURFEL_V2S_PF_DATA);        
        }
#endif
      }

      /* PLEASE DO NOT DELETE THIS COMMENT!!!
       * If you add a new data block either to a UBLK or SURFEL, please make sure to use the
       * DATA_OFFSET_TABLE_OPTS opts instance to make a runtime decision rather than a simulator global
       * since this file is exported as part of the SIMSIZES library and will break the DISC and DECOMP
       * builds at the linking phase
       *
       * DO
       * 
       * if (opts.new_flag) {
       *
       * NOT
       *
       * if (g_new_flag) { <----
       *
       *   tSURFEL::set_surfel_data_offset(...);
       *   surfel_size = tSURFEL::set_surfel_data_offset(...);
       * }

       */      
    }
    size_t alignment = get_alignment_of<typename tSURFEL::sSURFEL_DYNAMICS_DATA> ();
    tSURFEL::set_surfel_data_offset(surfel_type, SURFEL_DYN_DATA_TYPE) = get_byte_aligned(surfel_size, alignment);
  }
}

template
VOID SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table<sSURFEL>(const DATA_OFFSET_TABLE_OPTS& opts);

#ifdef BUILD_GPU
template
VOID SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table<sMSFL>(const DATA_OFFSET_TABLE_OPTS& opts);
#endif

static
asINT32 surfel_dynamics_data_type_from_cdi_type(STP_PHYSTYPE_TYPE phys_type) {
  switch (phys_type) {
  // Adiabatic wall types
  case STP_SLIP_SURFEL_TYPE_ID:
    return SLIP_SURFEL_TYPE;
  case STP_NOSLIP_SURFEL_TYPE_ID:
    return NOSLIP_SURFEL_TYPE;
  case STP_ANGULAR_SLIP_SURFEL_TYPE_ID:
    return ANGULAR_SLIP_SURFEL_TYPE;
  case STP_LINEAR_SLIP_SURFEL_TYPE_ID:
    return LINEAR_SLIP_SURFEL_TYPE;
  case STP_VEL_SLIP_SURFEL_TYPE_ID:
    return VEL_SLIP_SURFEL_TYPE;
  case STP_ANGULAR_NOSLIP_SURFEL_TYPE_ID:
    return ANGULAR_NOSLIP_SURFEL_TYPE;
  case STP_LINEAR_NOSLIP_SURFEL_TYPE_ID:
    return LINEAR_NOSLIP_SURFEL_TYPE;

  // thermal resist wall types
  case STP_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return SLIP_THERMAL_RESIST_SURFEL_TYPE;
  case STP_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return NOSLIP_THERMAL_RESIST_SURFEL_TYPE;
  case STP_ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE;
  case STP_LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE;
  case STP_ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE;
  case STP_LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE_ID:
    return LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE;

  // isothermal (fixed temperature) wall types
  case STP_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return SLIP_FIXED_TEMP_SURFEL_TYPE;
  case STP_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return NOSLIP_FIXED_TEMP_SURFEL_TYPE;
  case STP_ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE;
  case STP_LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE;
  case STP_ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE;
  case STP_LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE_ID:
    return LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE;

  // heat flux wall types
  case STP_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE;

  // outlets pressure
  case STP_STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
    return STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE;
  case STP_STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
    return STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE;
  case STP_STAG_PRESSURE_FREE_DIR_SURFEL_TYPE_ID:
    return STAG_PRESSURE_FREE_DIR_SURFEL_TYPE;
  case STP_STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE_ID:
    return STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE;

  // inlets (velocity, mass flux, mass flow)
  case STP_MASS_FLUX_SURFEL_TYPE_ID:
    return MASS_FLUX_SURFEL_TYPE;
  case STP_MASS_FLOW_SURFEL_TYPE_ID:
    return MASS_FLOW_SURFEL_TYPE;
  case STP_FIXED_VEL_SURFEL_TYPE_ID:
    return FIXED_VEL_SURFEL_TYPE;
  case STP_TURB_VEL_SURFEL_TYPE_ID:
    return TURB_VEL_SURFEL_TYPE;
  case STP_SOURCE_SURFEL_TYPE_ID:
    return SOURCE_SURFEL_TYPE;
  case STP_TURB_SOURCE_SURFEL_TYPE_ID:
    return TURB_SOURCE_SURFEL_TYPE;


  case STP_PASS_THRU_SURFEL_TYPE_ID:
    return PASS_THRU_SURFEL_TYPE;

  // sampling surfels
  case STP_MEAS_ONLY_SURFEL_TYPE_ID:
    return SAMPLING_SURFEL_TYPE;

  // LRF surfels
  case STP_SLRF_SURFEL_TYPE_ID:
    return SLRF_SURFEL_TYPE;

  // APM surfels
  case STP_APM_SLIP_ISURFEL_TYPE_ID:
    return SLIP_SURFEL_TYPE;
  case STP_APM_LINEAR_SLIP_ISURFEL_TYPE_ID:
    return LINEAR_SLIP_SURFEL_TYPE;
  case STP_APM_ANGULAR_SLIP_ISURFEL_TYPE_ID:
    return ANGULAR_SLIP_SURFEL_TYPE;
  case STP_APM_NOSLIP_ISURFEL_TYPE_ID:
    return NOSLIP_SURFEL_TYPE;
  case STP_APM_LINEAR_NOSLIP_ISURFEL_TYPE_ID:
    return LINEAR_NOSLIP_SURFEL_TYPE;
  case STP_APM_ANGULAR_NOSLIP_ISURFEL_TYPE_ID:
    return ANGULAR_NOSLIP_SURFEL_TYPE;

  // Conduction surfels
  case STP_CONDUCTION_ADIABATIC_SURFEL_TYPE_ID:
    return CONDUCTION_ADIABATIC_SURFEL_TYPE;
  case STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE_ID:
    return CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE;
  case STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE_ID:
    return CONDUCTION_FIXED_TEMP_SURFEL_TYPE;
  case STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    return CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE;
  case STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE_ID:
    return CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE;
  case STP_CONDUCTION_CONTACT_SURFEL_TYPE_ID:
    return CONDUCTION_CONTACT_SURFEL_TYPE;
  default:
    msg_internal_error("Invalid LGI surfel dynamics type (%d)", phys_type);
    return INVALID_SURFEL_TYPE;
  }
}

size_t SIMULATOR_NAMESPACE::surfel_dynamics_data_size(STP_PHYSTYPE_TYPE phys_type) {
  asINT32 surfel_data_type = surfel_dynamics_data_type_from_cdi_type(phys_type);
  return g_surfel_dynamics_data_sizes[surfel_data_type];
}

inline namespace SIMULATOR_NAMESPACE
{
template<typename tUBLK>  
static VOID initialize_ublk_dynamics_data_size() {
  tUBLK::set_ublk_dynamics_data_size(MIRROR_TYPE) = 0;
  tUBLK::set_ublk_dynamics_data_size(BASIC_FLUID_TYPE) = tUBLK::sUBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(SPECIAL_FLUID_TYPE) = tUBLK::sSPECIAL_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(FAN_FLUID_TYPE) = sFAN_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(TABLE_FAN_FLUID_TYPE) = sTABLE_FAN_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(POROUS_FLUID_TYPE) = tUBLK::sPOROUS_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(CURVED_POROUS_FLUID_TYPE) = sCURVED_POROUS_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(CURVED_HX_POROUS_FLUID_TYPE) = sCURVED_HX_POROUS_UBLK_DYNAMICS_DATA::SIZE();
  tUBLK::set_ublk_dynamics_data_size(CONDUCTION_SOLID_TYPE) = sCONDUCTION_UBLK_DYNAMICS_DATA::SIZE();
}
}

template<typename tUBLK>
VOID SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table(const DATA_OFFSET_TABLE_OPTS& opts) 
{

  initialize_ublk_dynamics_data_size<tUBLK>();

  for (asINT32 ublk_type = 0; ublk_type < N_UBLK_TYPES; ublk_type++) {
    size_t ublk_size     = sizeof(tUBLK);
    if (opts.is_lb_model) {
      if ((ublk_type >> TWO_STATE_COPIES_BIT) & 1) {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_TWO_STATES_DATA>();
        ublk_size = get_byte_aligned(ublk_size, alignment) + sizeof(typename tUBLK::sUBLK_TWO_STATES_DATA);
      } else {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_STATES_DATA>();
        ublk_size = get_byte_aligned(ublk_size, alignment) + sizeof(typename tUBLK::sUBLK_STATES_DATA);
      }
    }
    if (opts.is_turb_model) {
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_TURB_DATA>();
      ublk_size = get_byte_aligned(ublk_size, alignment) + sizeof(typename tUBLK::sUBLK_TURB_DATA);
    }
    if (opts.is_heat_transfer || opts.switch_acous_during_simulation) {
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_T_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_T_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      BOOLEAN need_two_copies = ((ublk_type >> TWO_STATE_COPIES_BIT) & 1);
      //BOOLEAN is_lb_T_solver = (sim.T_lb_scalar_solver_start_time > 0);
      // We are wasting memory if the PDE solver is run, but we allow for active switching between
      // the two solvers so it is unavoidable
      BOOLEAN is_lb_T_solver = TRUE;
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_T_DATA_TYPE) +
                  tUBLK::sUBLK_T_DATA::SIZE(need_two_copies);
    }
#if !BUILD_GPU
    if (opts.is_conduction_model) {  
      if ((ublk_type >> IS_CONDUCTION_SOLID_BIT) & 1) {
        sINT16 alignment = get_alignment_of<typename tUBLK::sUBLK_CONDUCTION_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,UBLK_CONDUCTION_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_CONDUCTION_DATA_TYPE) + tUBLK::sUBLK_CONDUCTION_DATA::SIZE();
      }
      if (((ublk_type >> IS_CONDUCTION_SOLID_BIT) & 1) && opts.use_implicit_solid_solver) {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA);
      }
    }
#endif
    if (opts.is_scalar_model) {
      asINT32 n_scalars = opts.n_user_defined_scalars;
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_UDS_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_UDS_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      BOOLEAN need_two_copies = ((ublk_type >> TWO_STATE_COPIES_BIT) & 1);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_UDS_DATA_TYPE) + n_scalars * tUBLK::sUBLK_UDS_DATA::SIZE(need_two_copies, opts.use_uds_lb_solver);
#if BUILD_D19_LATTICE
      if (opts.is_pf_model) {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_PF_DATA>();
	tUBLK::set_ublk_data_offset(ublk_type,UBLK_PF_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
	ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_PF_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_PF_DATA);
      }
#endif
    }
    if (opts.is_multi_component) {
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_MC_DATA>();
      BOOLEAN need_two_copies = ((ublk_type >> TWO_STATE_COPIES_BIT) & 1);
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_MC_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_MC_DATA_TYPE) + tUBLK::sUBLK_MC_DATA::SIZE(need_two_copies);
    }

    if (opts.has_avg_mme_window) {
      size_t alignment;
      if (opts.is_turb_model)
        alignment = get_alignment_of<typename tUBLK::sUBLK_MME_TURB_DATA>();
      else 
        alignment = get_alignment_of<typename tUBLK::sUBLK_MME_DNS_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_MME_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_MME_DATA_TYPE) + (opts.is_turb_model ? sizeof(typename tUBLK::sUBLK_MME_TURB_DATA) : sizeof(typename tUBLK::sUBLK_MME_DNS_DATA));
    }
    if (opts.has_avg_mme_window && opts.is_heat_transfer) {
#if !BUILD_GPU
      if (opts.is_conduction_model && ((ublk_type >> IS_CONDUCTION_SOLID_BIT) & 1)) {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_MME_CONDUCTION_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,UBLK_MME_CONDUCTION_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::get_ublk_data_offset(ublk_type,UBLK_MME_CONDUCTION_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_MME_CONDUCTION_DATA);
      } else
#endif
      {
        size_t alignment = get_alignment_of<typename tUBLK::sUBLK_MME_T_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,UBLK_MME_T_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::get_ublk_data_offset(ublk_type,UBLK_MME_T_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_MME_T_DATA);
      }
    }

    if (opts.has_avg_mme_window && opts.is_scalar_model) {
      asINT32 n_scalars = opts.n_user_defined_scalars;
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_MME_UDS_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_MME_UDS_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::get_ublk_data_offset(ublk_type,UBLK_MME_UDS_DATA_TYPE) + n_scalars * sizeof(typename tUBLK::sUBLK_MME_UDS_DATA);
    }

#if BUILD_D19_LATTICE
    if(opts.store_frozen_vars) {
      if (opts.has_avg_mme_window) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_MME_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_MME_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::get_ublk_data_offset(ublk_type,NEARBLK_MME_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_MME_DATA);
      }

      size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_FROZEN_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_FROZEN_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::get_ublk_data_offset(ublk_type,NEARBLK_FROZEN_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_FROZEN_DATA);
    }
#endif

    if (opts.is_particle_model) {
      size_t alignment = get_alignment_of<typename tUBLK::sUBLK_PARTICLE_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_PARTICLE_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::get_ublk_data_offset(ublk_type,UBLK_PARTICLE_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_PARTICLE_DATA);
    }
    if ((ublk_type >> NEAR_SURFACE_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_GEOM_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_GEOM_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_GEOM_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_GEOM_DATA);
      if (opts.is_lb_model) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_LB_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_LB_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_LB_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_LB_DATA);
      }
      if (opts.is_turb_model) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_TURB_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_TURB_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_TURB_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_TURB_DATA);
      }
      if (opts.is_heat_transfer || opts.switch_acous_during_simulation) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_T_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_T_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_T_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_T_DATA);
      }
#if !BUILD_GPU
      if (opts.is_conduction_model && ((ublk_type >> IS_CONDUCTION_SOLID_BIT) & 1)) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_CONDUCTION_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_CONDUCTION_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_CONDUCTION_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_CONDUCTION_DATA);
      }
#endif
      if (opts.is_scalar_model) {
        asINT32 n_scalars = opts.n_user_defined_scalars;
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_UDS_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_UDS_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_UDS_DATA_TYPE) + n_scalars * tUBLK::sNEARBLK_UDS_DATA::SIZE(opts.use_uds_lb_solver);
#if BUILD_D19_LATTICE
	if (opts.is_pf_model) {
          size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_PF_DATA>();
	  tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_PF_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
	  ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_PF_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_PF_DATA);
        }
#endif
      }
      if (opts.is_multi_component) {
        size_t alignment = get_alignment_of<typename tUBLK::sNEARBLK_MC_DATA>();
        BOOLEAN need_two_copies = ((ublk_type >> TWO_STATE_COPIES_BIT) & 1);
        tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_MC_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
        ublk_size = tUBLK::set_ublk_data_offset(ublk_type,NEARBLK_MC_DATA_TYPE) + sizeof(typename tUBLK::sNEARBLK_MC_DATA);
      }
    }
    if ((ublk_type >> HAS_MIRROR_BIT) & 1) {
      size_t alignment = get_alignment_of<sUBLK_MIRROR_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_MIRROR_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_MIRROR_DATA_TYPE) + sizeof(sUBLK_MIRROR_DATA);
    }
    if ((ublk_type >> IS_VR_COARSE_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tUBLK::sVR_COARSE_INTERFACE_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_VR_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_VR_DATA_TYPE) + sizeof(typename tUBLK::sVR_COARSE_INTERFACE_DATA);
    } else if ((ublk_type >> IS_VR_FINE_BIT) & 1) {
      size_t alignment = get_alignment_of<typename tUBLK::sVR_FINE_INTERFACE_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_VR_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_VR_DATA_TYPE) + sizeof(typename tUBLK::sVR_FINE_INTERFACE_DATA);
    }

    //5G 
    if (opts.is_large_pore) {
      sINT16 alignment = get_alignment_of<typename tUBLK::sUBLK_PORE_LB_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_LB_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_LB_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_PORE_LB_DATA);

      if (opts.is_multi_component) {
	sINT16 alignment = get_alignment_of<typename tUBLK::sUBLK_PORE_MC_DATA>();
        tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_MC_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
	ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_MC_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_PORE_MC_DATA);
      }

      if (opts.is_scalar_model) {
	asINT32 n_scalars = opts.n_user_defined_scalars;
	sINT16 alignment = get_alignment_of<typename tUBLK::sUBLK_PORE_UDS_DATA>();
	tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_UDS_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
	ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_PORE_UDS_DATA_TYPE) + n_scalars * sizeof(typename tUBLK::sUBLK_PORE_UDS_DATA);
      }
    }

#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
    if(opts.meas_seed_or_write_pres_d19){
      sINT16 alignment = get_alignment_of<typename tUBLK::sUBLK_CBF_LB_DATA>();
      tUBLK::set_ublk_data_offset(ublk_type,UBLK_CBF_LB_DATA_TYPE) = get_byte_aligned(ublk_size, alignment);
      ublk_size = tUBLK::set_ublk_data_offset(ublk_type,UBLK_CBF_LB_DATA_TYPE) + sizeof(typename tUBLK::sUBLK_CBF_LB_DATA);
    }
#endif

    /* PLEASE DO NOT DELETE THIS COMMENT!!!
     * If you add a new data block either to a UBLK or SURFEL, please make sure to use the
     * DATA_OFFSET_TABLE_OPTS opts instance to make a runtime decision rather than a simulator global
     * since this file is exported as part of the SIMSIZES library and will break the DISC and DECOMP
     * builds at the linking phase
     *
     * DO
     * 
     * if (opts.new_flag) {
     *
     * NOT
     *
     * if (g_new_flag) { <----
     *
     *   tUBLK::set_ublk_data_offset(...);
     *   ublk_size = tUBLK::set_ublk_data_offset(...);
     * }
     *
     */
    size_t alignment = get_alignment_of<sUBLK_DYNAMICS_DATA> ();
    ublk_size = get_byte_aligned(ublk_size, alignment);
    tUBLK::set_ublk_data_offset(ublk_type,UBLK_DYN_DATA_TYPE) = ublk_size;
  }
}

#ifdef BUILD_GPU
template
VOID SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<tUBLK<MBLK_SDFLOAT_TYPE_TAG>>(const DATA_OFFSET_TABLE_OPTS& opts);

template<>
VOID SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<tUBLK<UBLK_SDFLOAT_TYPE_TAG>>(const DATA_OFFSET_TABLE_OPTS& opts) {
  initialize_ublk_dynamics_data_size<sUBLK>();
}
#else
template
VOID SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<tUBLK<UBLK_SDFLOAT_TYPE_TAG>>(const DATA_OFFSET_TABLE_OPTS& opts);
#endif

asINT32 SIMULATOR_NAMESPACE::dynamics_data_type_from_cdi_type(STP_PHYSTYPE_TYPE phys_type) {
  switch (phys_type) {
  case STP_UBLK_MIRROR_TYPE:
    return MIRROR_TYPE;
  case STP_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_VVFLUID_TYPE:
    return BASIC_FLUID_TYPE;
#if !BUILD_5G_LATTICE
  case STP_FAN_VVFLUID_TYPE:
    return  FAN_FLUID_TYPE;
  case STP_TABLE_FAN_VVFLUID_TYPE:
    return  TABLE_FAN_FLUID_TYPE;
  case STP_POROUS_VVFLUID_TYPE:
    return POROUS_FLUID_TYPE;
  case STP_CURVED_POROUS_VVFLUID_TYPE:
    return CURVED_POROUS_FLUID_TYPE;
  case STP_CURVED_HX_POROUS_VVFLUID_TYPE:
    return CURVED_HX_POROUS_FLUID_TYPE;
  case STP_CONDUCTION_SOLID_TYPE:
    return CONDUCTION_SOLID_TYPE;
  case STP_INSULATOR_SOLID_TYPE:
    return INSULATOR_SOLID_TYPE;
#endif
  default:
    msg_internal_error("undefined cdi_type %d", phys_type);
  }
  return -1;
}

// This is basically the inverse of dynamics_data_type_from_cdi_type
// The CDI type information for the voxels is lost after parsing. Therefore,
// what we return is a proxy type, which is good enough to translate back to
// the correct dynamics data type again downstream in the call stack
STP_PHYSTYPE_TYPE SIMULATOR_NAMESPACE::proxy_cdi_type_from_dynamics_data_type(asINT32 fluid_dynamics_type) {
  switch (fluid_dynamics_type) {
  case MIRROR_TYPE:
    return STP_UBLK_MIRROR_TYPE;
  case BASIC_FLUID_TYPE:
    return STP_VVFLUID_TYPE;
#if !BUILD_5G_LATTICE
  case FAN_FLUID_TYPE:
    return STP_FAN_VVFLUID_TYPE;
  case TABLE_FAN_FLUID_TYPE:
    return  STP_TABLE_FAN_VVFLUID_TYPE;
  case POROUS_FLUID_TYPE:
    return STP_POROUS_VVFLUID_TYPE;
  case CURVED_POROUS_FLUID_TYPE:
    return STP_CURVED_POROUS_VVFLUID_TYPE;
  case CURVED_HX_POROUS_FLUID_TYPE:
    return STP_CURVED_HX_POROUS_VVFLUID_TYPE;
#endif
  default:
    msg_internal_error("undefined fluid_dynamics_type %d", fluid_dynamics_type);
  }
  return STP_INVALID_PHYSTYPE_TYPE;
}

template<typename UBLK_TYPE_TAG, BOOLEAN IS_MIXED_TYPE>
size_t SIMULATOR_NAMESPACE::get_dynamic_attribute_size(STP_PHYSTYPE_TYPE sim_phys_type, sUBLK_DYNAMICS_ATTR_DATA *attribute_data)
{

  using sUBLK_DYNAMICS_ATTRIBUTES = tUBLK_DYNAMICS_ATTRIBUTES<UBLK_TYPE_TAG, IS_MIXED_TYPE>;
  
  PHYSICS_DESCRIPTOR fluid_phys_desc = attribute_data->fluid_phys_desc;
  if (fluid_phys_desc == nullptr)
    return 0;

  switch (sim_phys_type) {
  case STP_UBLK_MIRROR_TYPE:
    return 0;
  case STP_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_VVFLUID_TYPE:
  {
    FLUID_PHYSICS_DESCRIPTOR pd = (FLUID_PHYSICS_DESCRIPTOR) fluid_phys_desc;
    attribute_data->is_basic_fluid = TRUE;
    return sUBLK_DYNAMICS_ATTRIBUTES::template SIZE<sFLUID_PHYSICS_DESCRIPTOR>(attribute_data);
    break;
  }
  case STP_POROUS_VVFLUID_TYPE:
  case STP_CURVED_POROUS_VVFLUID_TYPE:
  case STP_CURVED_HX_POROUS_VVFLUID_TYPE:
  {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR)fluid_phys_desc;

    POROUS_MEDIA_PARAMETERS parms = pd->parameters();
    asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
    switch(cdi_phys_type)
    { 
      case ADIABATIC_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
        attribute_data->is_adiabatic_apm = TRUE;
    }

    attribute_data->is_basic_fluid = FALSE;
    return sUBLK_DYNAMICS_ATTRIBUTES::template SIZE<sPOROUS_MEDIA_PHYSICS_DESCRIPTOR>(attribute_data);
    break;
  }
  case STP_FAN_VVFLUID_TYPE:
  {
    FAN_PHYSICS_DESCRIPTOR pd = (FAN_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    attribute_data->is_basic_fluid = FALSE;
    return sUBLK_DYNAMICS_ATTRIBUTES::template SIZE<sFAN_PHYSICS_DESCRIPTOR>(attribute_data);
    break;
  }
  case STP_TABLE_FAN_VVFLUID_TYPE:
  {
    TABLE_FAN_PHYSICS_DESCRIPTOR pd = (TABLE_FAN_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    attribute_data->is_basic_fluid = FALSE;
    return sUBLK_DYNAMICS_ATTRIBUTES::template SIZE<sTABLE_FAN_PHYSICS_DESCRIPTOR>(attribute_data);
    break;
  }
  case STP_CONDUCTION_SOLID_TYPE:
  {
    CONDUCTION_PHYSICS_DESCRIPTOR pd = (CONDUCTION_PHYSICS_DESCRIPTOR)fluid_phys_desc;

    CONDUCTION_SOLID_PARAMETERS parms = pd->parameters();
    asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
    //attribute_data->is_conduction_solid = TRUE;
    attribute_data->is_basic_fluid = FALSE;
    return 0; // Conducting solids currently don't have any attributes
    // return sUBLK_DYNAMICS_ATTRIBUTES::template SIZE<sCONDUCTION_PHYSICS_DESCRIPTOR>(attribute_data);
    break;
  }
  case STP_INSULATOR_SOLID_TYPE:
    return 0;
    break;
  default:
  {
    msg_internal_error("Unknown dynamics data cdi type: %d", sim_phys_type);
  }
  }
  return 0;
}

template<>
size_t SIMULATOR_NAMESPACE::compute_ublk_dynamics_data_size<UBLK_SDFLOAT_TYPE_TAG>(asINT32 n_phys_types,
                                                                                     STP_PHYSTYPE_TYPE* phys_types,
                                                                                     sUBLK_DYNAMICS_ATTR_DATA* attribute_data) {
  size_t dynamics_data_size = 0;
  ccDOTIMES(n, n_phys_types) {
    asINT32 data_type = dynamics_data_type_from_cdi_type(phys_types[n]);
    dynamics_data_size += sUBLK::get_ublk_dynamics_data_size(data_type);
    dynamics_data_size += get_dynamic_attribute_size<UBLK_SDFLOAT_TYPE_TAG, FALSE>(phys_types[n],&attribute_data[n]);
  }
  return dynamics_data_size;
}

#if BUILD_GPU
/* @fcn compute_ublk_dynamics_data_size<MBLK_SDFLOAT_TYPE_TAG>
 * This is a specialization for computing size of Mega Ublks. Unlike regular UBLKs, the dynamics
 * data for MBLKs can be compressed when the composing child UBLKs have identical attributes.
 */
template<>
size_t SIMULATOR_NAMESPACE::compute_ublk_dynamics_data_size<MBLK_SDFLOAT_TYPE_TAG>(asINT32 n_sim_phys_types,
                                                                                      STP_PHYSTYPE_TYPE* sim_phys_types,
                                                                                      std::array<sUBLK_DYNAMICS_ATTR_DATA, 8>* attribute_data) {
  size_t dynamics_data_size = 0;
  ccDOTIMES(n, n_sim_phys_types) {
    asINT32 sim_fluid_type = dynamics_data_type_from_cdi_type(sim_phys_types[n]);
    auto& ublk_attributes = attribute_data[n];
    BOOLEAN is_uniform_type = is_uniform_attributes(ublk_attributes);
    dynamics_data_size += sMBLK::get_ublk_dynamics_data_size(sim_fluid_type) +
                          sMBLK::sUBLK_DYNAMICS_DATA::get_variable_dyn_data_size(!is_uniform_type);

    if (is_uniform_type) {
      auto first_non_default_attrib = std::find_if(ublk_attributes.begin(),
                                                   ublk_attributes.end(),
                                                   [&] (const sUBLK_DYNAMICS_ATTR_DATA& a) {
                                                     return !a.is_default();
                                                   });
      dynamics_data_size += get_dynamic_attribute_size<MBLK_SDFLOAT_TYPE_TAG, FALSE>(sim_phys_types[n], &(*first_non_default_attrib));
    } else {
      ccDOTIMES(u, 8) {
	dynamics_data_size += get_dynamic_attribute_size<MBLK_SDFLOAT_TYPE_TAG, TRUE>(sim_phys_types[n], &attribute_data[n][u]);
      }
    }
  }
  return dynamics_data_size;
}
#endif
