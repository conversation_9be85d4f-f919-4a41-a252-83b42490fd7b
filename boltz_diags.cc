/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Diagnostics for Boltzmann distribution
 *
 * James Hoch, Exa Corporation 
 * Created Wed Sept 17, 1997
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "boltz_diags.h"
#include "timescale.h"
#include "sim.h"

/**** Boltzmann related diagnostics */

#if SIM_ENABLE_BOLTZ_DIAGS

static asINT32 n_neg_or_state = 0;
static asINT32 n_neg_eq_state = 0;

static asINT32 n_overflow_or_state = 0;
static asINT32 n_overflow_eq_state = 0;

static asINT32 n_voxels_with_neg_or_state = 0;
static asINT32 n_voxels_with_neg_eq_state = 0;

static asINT32 n_voxels_with_overflow_or_state = 0;
static asINT32 n_voxels_with_overflow_eq_state = 0;

static asINT32 n_cleanup_rules = 0;

static asINT32 n_voxels_reverted_to_incremental_drive = 0;

VOID sim_reset_fluid_dyn_diagnostics(VOID)
{
  n_neg_or_state = 0;
  n_neg_eq_state = 0;
  n_overflow_or_state = 0;
  n_overflow_eq_state = 0;
  n_voxels_with_neg_or_state = 0;
  n_voxels_with_neg_eq_state = 0;
  n_voxels_with_overflow_or_state = 0;
  n_voxels_with_overflow_eq_state = 0;
  n_cleanup_rules = 0;
  n_voxels_reverted_to_incremental_drive = 0;
}

VOID sim_report_fluid_dyn_diagnostics(asINT32 avg_over_n_timesteps)
{
  if (n_voxels_with_neg_or_state || n_voxels_with_neg_eq_state 
      || n_voxels_with_overflow_or_state || n_voxels_with_overflow_eq_state) {
#define avg(x)	((double)(x)/avg_over_n_timesteps)
    msg_print_nf("Time %ld: Fluid diagnostics averaged over %d timesteps:\n"
		 "Voxels OR neg: %-6.4g  Voxels EQ neg: %-6.4g  States OR neg: %-6.4g  States EQ neg: %-6.4g\n"
		 "Voxels OR pos: %-6.4g  Voxels EQ pos: %-6.4g  States OR pos: %-6.4g  States EQ pos: %-6.4g\n"
		 "Cleanup rules: %-6.4g  Voxels requiring incremental process: %-6.4g\n",
		 g_timescale.m_time,
		 avg_over_n_timesteps,
		 avg(n_voxels_with_neg_or_state), avg(n_voxels_with_neg_eq_state),
		 avg(n_neg_or_state), avg(n_neg_eq_state),
		 avg(n_voxels_with_overflow_or_state), avg(n_voxels_with_overflow_eq_state),
		 avg(n_overflow_or_state), avg(n_overflow_eq_state),
		 avg(n_cleanup_rules), avg(n_voxels_reverted_to_incremental_drive));
#undef avg
    fflush(stdout);
  }
}

VOID sim_inc_n_neg_or_states(asINT32 n)
{
  n_neg_or_state += n;
}

VOID sim_inc_n_neg_eq_states(asINT32 n)
{
  n_neg_eq_state += n;
}

VOID sim_inc_n_overflow_or_states(asINT32 n)
{
  n_overflow_or_state += n;
}

VOID sim_inc_n_overflow_eq_states(asINT32 n)
{
  n_overflow_eq_state += n;
}

VOID sim_inc_n_voxels_with_neg_or_state(VOID)
{
  n_voxels_with_neg_or_state++;
}

VOID sim_inc_n_voxels_with_neg_eq_state(VOID)
{
  n_voxels_with_neg_eq_state++;
}

VOID sim_inc_n_voxels_with_overflow_or_state(VOID)
{
  n_voxels_with_overflow_or_state++;
}

VOID sim_inc_n_voxels_with_overflow_eq_state(VOID)
{
  n_voxels_with_overflow_eq_state++;
}

VOID sim_inc_n_cleanup_rules_executed(asINT32 n_rules_executed)
{
  n_cleanup_rules += n_rules_executed;
}

VOID sim_inc_n_voxels_reverted_to_incremental_drive(VOID)
{
  n_voxels_reverted_to_incremental_drive++;
}

#endif
