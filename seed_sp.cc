/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Seeding of ublks and surfels
 *
 * James Hoch, Exa Corporation
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "sp_timers.h"
#include "shob.h"
#include "seed_sp.h"
#include "sim.h"
#include "comm_groups.h"
#include "advect.h"
#include "vr.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "mirror.h"
#include "surfel_advect_sp.h"
#include "gradient.h"
#include "surfel_process_control.h"
#include "wsurfel_comm.h"
#include "radiation_comm.h"
#include "radiation_phy.h"
#include "implicit_shell_solver.h"
#include "implicit_solid_solver.h"
#include "conduction_contact_averaging.h"

#include PHYSICS_H
#include "ublk_seed.h"

std::vector<sFLUID_SEED_VAR_SPEC>    g_fluid_seed_var_specs; 
std::vector<sBOUNDARY_SEED_VAR_SPEC> g_boundary_seed_var_specs; 
cCALIBRATION_RUN_SEEDER g_seed_calibration_iteration;

std::vector<std::string> tSEED_VAR_SPEC_BASE::fluid_seed_var_names = {
  "static_pressure", 
  "x_velocity",
  "y_velocity",
  "z_velocity",
  "temp",
  "turb_kinetic_energy",
  "turb_dissipation",
  "density",
  "stress_tensor_mag",
  "water_vapor_mfrac",
  "comp0_density",
  "comp1_density",
  "contact_angle",
  "dynamic_scalar_multiplier",
  "ustar",
  "density_frozen"};

std::vector<std::string> tSEED_VAR_SPEC_BASE::boundary_seed_var_names = {
  "pressure",
  "total_pressure",
  "x_velocity",
  "y_velocity",         
  "z_velocity",         
  "flow_dir_x",
  "flow_dir_y",
  "flow_dir_z",
  "temp",        
  "turb_kinetic_energy",
  "turb_dissipation",   
  "x_mass_flux",        
  "y_mass_flux",        
  "z_mass_flux",        
  "mass_flow"};

static INLINE VOID unset_exploded_fine_ublks()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      fine_ublk->unset_exploded();
    }
  }
}

__HOST__DEVICE__
VOID convert_fluid_initial_conditions_to_seed_data(LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_condition_values,
						   VOXEL_LES_SEED_DATA seed_data,
						   asINT32 csys_index,
						   BOOLEAN is_turb, 
						   asINT32 vel_ref_frame_index,
						   asINT32 voxel_ref_frame_index,
						   STP_GEOM_VARIABLE point[3],
                                                   asINT32 scale,
                                                   LRF_PHYSICS_DESCRIPTOR lrf)
{
  const auto& simc = get_simc_ref();
  auto& sim = get_sim_ref();
  // initial_condition_values.velocity consists of the raw values specified in PowerCASE.
  // These may be in default or non-default CSYS, and with respect to the local or global reference frame.
  // At the end of this function, seed_data->velocity is transformed to the global reference frame and 
  // specified in the local CSYS
  seed_data->velocity[0] = initial_condition_values.velocity(0);
  seed_data->velocity[1] = initial_condition_values.velocity(1);
  seed_data->velocity[2] = initial_condition_values.velocity(2);

#if BUILD_5G_LATTICE
  sdFLOAT density[2];
  if (initial_condition_values.component_ratio_type() == CDI_COMP_RATIO_VIA_PRESS_AND_FRAC) {
    sdFLOAT pressure = initial_condition_values.pressure() * g_lattice_constant * g_lattice_constant;
    compute_mc_densities_from_pressure (density, pressure, initial_condition_values.comp0_vol_frac(), initial_condition_values.phase_index());
  } else if (initial_condition_values.component_ratio_type() == CDI_COMP_RATIO_VIA_DENS) {
    density[0] = initial_condition_values.comp0_density();
    density[1] = initial_condition_values.comp1_density();
  } else if (initial_condition_values.component_ratio_type() == CDI_COMP_RATIO_VIA_DENS_AND_FRAC) {
    density[0] = initial_condition_values.total_density() * initial_condition_values.comp0_vol_frac();
    density[1] = initial_condition_values.total_density() * initial_condition_values.comp1_vol_frac();
  } else {
    msg_internal_error("Unrecognized initial component ratio type %f.", initial_condition_values.component_ratio_type());
  }
  
  seed_data->density    = density[0] * g_density_scale_factor;
  seed_data->density_mc = density[1] * g_density_scale_factor;

  seed_data->passive_scalar_temp = 0;
  seed_data->lb_temp = g_lb_temp_constant;

  seed_data->velocity[0] *= g_lattice_constant;
  seed_data->velocity[1] *= g_lattice_constant;
  seed_data->velocity[2] *= g_lattice_constant;

  if (simc.smart_seed_contact_angle) {
    seed_data->potential[0] = seed_data->density;
    seed_data->potential[1] = seed_data->density_mc;
  }
  seed_data->dynamic_scalar_multiplier = 1.0;

  
  seed_data->porosity = 1.0; //with state gradient in state split, all voxels have to be counted as pm voxel to support gradient calculation for a large_pore case
  seed_data->pm_mode_flag = 1;  //default in large_pore
  seed_data->rock_type_index = 0;  //default in large pore, confine usage to mp_pm_table_input = TRUE

  if (sim.is_large_pore && g_is_multi_component) {
    //calculate wall potentail from default contact angle in PM region
    sdFLOAT contact_angle[MAXIMUM_NUM_COMPONENTS] = {g_mp_typpotential_c0, g_mp_typpotential_c1};
    for (asINT32 c = 0; c < sim.num_components; c++) {
      if (contact_angle[c] < 0.0 || contact_angle[c] > 90.0)
	contact_angle[c] = 90.0;
    }
 
    if (contact_angle[0] == 0.0 && contact_angle[1] < 90.0)
      contact_angle[0] = 90.0;
    if (contact_angle[1] == 0.0 && contact_angle[0] < 90.0)
      contact_angle[1] = 90.0;
    /* to enforce algorithm symmetry  */
    if (contact_angle[0] > contact_angle[1])
      contact_angle[0] = 90.0;
    else
      contact_angle[1] = 90.0;

    sdFLOAT contact_angle_degree=contact_angle[0];
    if (contact_angle[0]>contact_angle[1])
      contact_angle_degree = 180.0-contact_angle[1];
    seed_data->contact_angle = contact_angle_degree;

    sdFLOAT used_viscosity = MAX(g_mc_types[0].viscosity, g_mc_types[1].viscosity);
    if (contact_angle[0] > contact_angle[1] && g_mc_types[0].viscosity < g_mc_types[1].viscosity)
      used_viscosity = g_mc_types[0].viscosity * g_mp_NuTrationeq1_factor + g_mc_types[1].viscosity * (1.0 - g_mp_NuTrationeq1_factor);
    else if (contact_angle[0] < contact_angle[1] && g_mc_types[0].viscosity > g_mc_types[1].viscosity)
      used_viscosity = g_mc_types[1].viscosity * g_mp_NuTrationeq1_factor + g_mc_types[0].viscosity * (1.0 - g_mp_NuTrationeq1_factor);

    for (asINT32 c = 0; c < sim.num_components; c++) {
      sdFLOAT potential = 0.22 * contact_angle_to_potential(contact_angle[c], used_viscosity);
      if (potential < 0.0)
	potential = 0.0;
      seed_data->wall_potential[c] = potential;   //g_mc_types[c].equation_of_state->potential(potential);  ideal type EOS
    }
  }
  
#else
  if (simc.is_heat_transfer) {
    if (simc.is_high_subsonic_HT_off)
      seed_data->passive_scalar_temp = g_lb_temp_constant;
    else
      seed_data->passive_scalar_temp = initial_condition_values.temperature();
    seed_data->lb_temp = simc.lb_char_temp;
    if (simc.thermal_feedback_is_on) {
#if BUILD_D19_LATTICE
      if(simc.is_hydrogen_eos){
        seed_data->density = initial_condition_values.pressure() / (initial_condition_values.temperature() * simc.lattice_gas_const 
                                                                    + simc.hydrogen_beta_fac * initial_condition_values.pressure() * g_density_scale_factor);
      } else 
#endif      
      seed_data->density = initial_condition_values.pressure() / (initial_condition_values.temperature() * simc.lattice_gas_const);
    } else { 
      seed_data->density = initial_condition_values.pressure() / (simc.lb_char_temp * simc.lattice_gas_const);
    }    
  } else {
    seed_data->passive_scalar_temp = 0;
    if (IS_FIXED_TEMP_LATTICE)
      seed_data->lb_temp = g_lb_temp_constant;
    else
      seed_data->lb_temp = initial_condition_values.temperature();
    seed_data->density = initial_condition_values.pressure() / (initial_condition_values.temperature() * simc.lattice_gas_const);
  } 

  seed_data->density *= g_density_scale_factor;
#endif

  if(simc.is_scalar_model) {
    if (simc.uds_solver_type == LB_UDS) {
#if BUILD_5G_LATTICE
      if (g_specify_uds_values) {
	ccDOTIMES(nth_uds, simc.n_user_defined_scalars)	
	  seed_data->uds_value[nth_uds] = get_uds_initial_value(nth_uds); //g_uds_initial_value[nth_uds];
      }
      else {  //@@@ 5G powerCase does not support UDS, so we cannot put "uds_fluid_initial_condition" into initial_condition_var list
	ccDOTIMES(nth_uds, simc.n_user_defined_scalars)	
	  seed_data->uds_value[nth_uds] = 0.0;
      }
#else	
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars)
	seed_data->uds_value[nth_uds] = initial_condition_values.uds_value(nth_uds);
#endif
#if BUILD_D19_LATTICE
      if(simc.is_pf_model) {
	seed_data->density_pfld = initial_condition_values.pressure() / (g_lb_temp_constant * simc.lattice_gas_const);
	seed_data->uds_value[0] = initial_condition_values.uds_value(0);
      }
#endif
    } 
#if !BUILD_5G_LATTICE
    else { //PDE_UDS
      seed_data->water_vapor_mass_fraction = initial_condition_values.water_mass_fraction();
      seed_data->relative_humidity = initial_condition_values.relative_humidity();
    }
#endif
  }

  // transform seed_data->velocity to the default CSYS (at t=0, this is also the local CSYS for all reference frames)
  if (is_csys_index_non_default(csys_index)) {
    CSYS csys = &sim.csys_table[csys_index];

    sG3_VEC vel_vec;
    vel_vec.vcoord[0] = seed_data->velocity[0];
    vel_vec.vcoord[1] = seed_data->velocity[1];
    vel_vec.vcoord[2] = seed_data->velocity[2];

    vel_vec = g3_xform_vec(vel_vec, csys->l_to_g_xform);

    seed_data->velocity[0] = vel_vec.vcoord[0];
    seed_data->velocity[1] = vel_vec.vcoord[1];
    seed_data->velocity[2] = vel_vec.vcoord[2];
  }

  // when seeding from a file with an LRF region that has rotated from its t=0 position
  if (lrf != NULL && lrf->is_mlrf_on) {
      // transform to the local CSYS
      BOOLEAN transpose = TRUE;
      rotate_vector(seed_data->velocity, seed_data->velocity, lrf->local_to_global_rotation_matrix, transpose);
  }

  // Transform velocity to the global ref frame
  // Note: It may seem inefficient to transform to the global ref frame and then later transform back to
  //       the local ref frame when seeding voxels, but this is done to keep the output of this function 
  //       consistent with any smart seed velocities, which are always in the global ref frame. 
  if (vel_ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) {
    // the output of this function is in the local CSYS
    convert_vel_between_ref_frames(seed_data->velocity, point, seed_data->velocity, 
                                   vel_ref_frame_index, SRI_GLOBAL_REF_FRAME_INDEX);
  }

  if (is_turb) {
    seed_data->stress_ten_mag = 0;	// only non-zero for smart seed

    if (initial_condition_values.turb_profile_via() == CDI_TURB_VIA_INTENSITY) {
      // Turb intensity is defined in terms of velocity in the ground frame.
      // See discussion in PR 6725. If there is no global reference frame,
      // the ground and global body-fixed frames are one and the same.
      dFLOAT vel_sqrd;
      if (sim.grf.is_defined) {
        STP_PHYS_VARIABLE v[3];
        convert_vel_between_ref_frames(seed_data->velocity, point, v,
                                       SRI_GLOBAL_REF_FRAME_INDEX, SRI_GROUND_REF_FRAME_INDEX);
        vel_sqrd = vdot(v, v);
      }
      else {
        vel_sqrd = vdot(seed_data->velocity, seed_data->velocity);
      }

      dFLOAT turb_length_scale = initial_condition_values.turb_length_scale();
      dFLOAT turb_profile_via = initial_condition_values.turb_profile_via();

      if (turb_length_scale <= 0.0  && turb_profile_via != 1.0) {
#if !GPU_COMPILER        
        simerr_report_error_code(SP_EER_TURB_LENGTH_SCALE_NEG_VOXEL, scale, point, turb_length_scale);
#endif
        turb_length_scale = 1.0;
      }

      dFLOAT turb_intensity = initial_condition_values.turb_intensity();
      dFLOAT k = 1.5 * vel_sqrd * turb_intensity * turb_intensity;
      dFLOAT epsilon = pow(STP_C_MU, 0.75) * pow(k, 1.5) / turb_length_scale;
      seed_data->turb_kinetic_energy = k;
      seed_data->turb_dissipation = epsilon;
    }
    else {
      seed_data->turb_kinetic_energy = initial_condition_values.turb_kinetic_energy();
      seed_data->turb_dissipation = initial_condition_values.turb_dissipation();
    }
  }

#if BUILD_D19_LATTICE
  //new
  seed_data->ustar_lb = 0.0;
  seed_data->coarsen_region_identifier = -1.0;
  seed_data->density_frozen = 0.0;               
  seed_data->vel_frozen[0] = 0.0;
  seed_data->vel_frozen[1] = 0.0;
  seed_data->vel_frozen[2] = 0.0;
#endif
}

VOID convert_conduction_initial_conditions_to_seed_data(CONDUCTION_INITIAL_CONDITIONS initial_conditions,
						        VOXEL_CONDUCTION_SEED_DATA seed_data,
						        asINT32 csys_index,
						        asINT32 vel_ref_frame_index,
						        asINT32 voxel_ref_frame_index,
						        STP_GEOM_VARIABLE point[3],
                                                        asINT32 scale,
                                                        LRF_PHYSICS_DESCRIPTOR lrf)
{
  //TODO: Remove this later
  if (!sim.is_heat_transfer)
    msg_error("Entered conduction initial condition for non-HT case");
  seed_data->passive_scalar_temp = initial_conditions->temperature.value;

  // TODO, CHECK: Overwrite the density to be always char_density
  seed_data->density = sim.char_density;
}

template<typename SMART_SEED_DATA>
__HOST__DEVICE__ static INLINE
VOID scale_seed_vars_via_mks(SMART_SEED_DATA* seed_vars,
                             const asINT32 voxel,
                             const FLUID_SEED_VAR_SPEC seed_var_spec,
                             const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_condition_values)
{
  auto& sim = get_sim_ref();
  auto& simc = get_simc_ref();
  // We need to be able to seed cases from results which have different
  // characteristic and maximum expected velocities using MKS units. See PR 31186.
  sINT32 controller_index = seed_var_spec->seed_controller;
  dFLOAT pressure_seed;
  if (sim.m_seed_control[controller_index].is_var_seeded[DGF_SEED_VAR_PRESSURE]) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_vars->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index);
    pressure_seed = sim.m_seed_control[controller_index].scale_seed_pressure(pressure_seed_orig);
    seed_vars->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index) = pressure_seed / g_density_scale_factor;
  }

  dFLOAT temp_seed;
  if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) {
    temp_seed = seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index);
    seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) = temp_seed;
  } else {
    temp_seed = initial_condition_values.temperature();
  }
  dFLOAT vel_scale = sim.m_seed_control[controller_index].smart_seed_mks_vel_scaling / simc.mks_vel_scaling;

  for (int var = DGF_SEED_VAR_XVEL; var < DGF_SEED_VAR_ZVEL + 1; var++) {
    if (sim.m_seed_control[controller_index].is_var_seeded[var]) {
      seed_vars->vars(var, voxel, controller_index) *= vel_scale;
    }
  }
}

template<typename SMART_SEED_DATA>
  __HOST__DEVICE__ static INLINE
VOID scale_seed_vars_via_mks(SMART_SEED_DATA* seed_vars,
                                    const asINT32 voxel,
                                    const FLUID_SEED_VAR_SPEC seed_var_spec,
                                    const CONDUCTION_INITIAL_CONDITIONS initial_conditions)
{
  sINT32 controller_index = seed_var_spec->seed_controller;
  dFLOAT temp_seed;
  if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) {
    temp_seed = seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index);
    seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) = temp_seed;
  } else {
    temp_seed = initial_conditions->temperature.value;
  }
}

__HOST__DEVICE__
dFLOAT sSEED_CONTROL::compute_seed_velocity_scaling(dFLOAT pressure_seed, dFLOAT temp_seed,
                                                    dFLOAT density_seed, BOOLEAN based_on_vel_only) {
  //     scale momentum thru dimension less momentum.
  //
  //     Rather than doing  (V/V_c) = (V'/V_c') -> V' = V_c'*(V/V_c), instead
  //     do:
  //
  //     (rho*V)/(rho_c*V_c) = (rho'*V')/(rho_c'*V_c')
  //
  //     or:
  //
  //     V' = (rho_c'*V_c'/rho')*[(rho*V)/(rho_c*V_c)]
  //
  //     Where rho' comes from step 1.
  dFLOAT vel_scale;
  auto& simc = get_simc_ref();
  if (based_on_vel_only)
    vel_scale = simc.char_vel / smart_seed_char_vel;
  else {
    cassert(pressure_seed != 0.0);
    BOOLEAN is_ht_active = simc.thermal_feedback_is_on;
    dFLOAT density = pressure_seed / (is_ht_active ? simc.lattice_gas_const*temp_seed : simc.char_temp);
    dFLOAT density_scaled = g_density_scale_factor* density_seed;
    vel_scale = ((simc.char_density * simc.char_vel) / (smart_seed_char_density * smart_seed_char_vel)
                 * (density_scaled / density));
  }
  return vel_scale;
}

__HOST__DEVICE__
dFLOAT sSEED_CONTROL::scale_seed_pressure(dFLOAT seed_pressure) {
  //
  //     Cp' = Cp = (rho *T  - rho_c *T_c )/(0.5*rho_c *V_c^2 )
  //              = (rho'*T' - rho_c'*T_c')/(0.5*rho_c'*V_c'^2)
  //
  //     This is easily solved for the new density rho':
  //
  //     rho' = (1/T')*{[(rho_c'*V_c'^2)/(rho_c*V_c^2)]*(rho*T - rho_c*T_c) + rho_c'*T_c'}
  //
  auto& simc = get_simc_ref();  
  dFLOAT scaled_pressure = (((simc.char_density * simc.char_vel * simc.char_vel) /
                             (smart_seed_char_density * smart_seed_char_vel * smart_seed_char_vel)) *
                             (seed_pressure - smart_seed_lattice_gas_const * smart_seed_char_density * smart_seed_char_temp)
                             + simc.lattice_gas_const * simc.char_density * simc.char_temp);
  return scaled_pressure;
}

template<typename SMART_SEED_DATA>
__HOST__DEVICE__ static INLINE
VOID scale_seed_vars_via_dimless_properties(SMART_SEED_DATA* seed_vars,
                                            const asINT32 voxel,
                                            const FLUID_SEED_VAR_SPEC seed_var_spec,
                                            const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_conditions)
{
  auto& sim = get_sim_ref();
  const auto& simc = get_simc_ref();
  // We need to be able to seed cases from results which have different
  // velocity mappings. See PR 2933.
  //
  // We have considered doing this by simply scaling through the
  // dimension less parameters (Cp and u/Vo), but scaling Cp and velocity
  // independently of one another violates continuity.  So following is
  // a simple procedure for satisfying both.
  // 
  // Primed "'" values are for the "new" simulation (seeded case),
  // unprimed values are from previous case.  _c denotes the
  // characteristic value.
  // 
  //  1) Scale pressure (density) through Cp:
  //  2) Use this new density when scaling velocity to maintain continuity,

  sINT32 controller_index = seed_var_spec->seed_controller;
  dFLOAT pressure_seed = 0.0;
  if (sim.m_seed_control[controller_index].is_var_seeded[DGF_SEED_VAR_PRESSURE]) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_vars->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index);
    pressure_seed = sim.m_seed_control[controller_index].scale_seed_pressure(pressure_seed_orig);
    seed_vars->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index) = pressure_seed / g_density_scale_factor;
  }

  dFLOAT temp_seed;
  if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) {
    temp_seed = seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) * (simc.char_temp / sim.m_seed_control[controller_index].smart_seed_char_temp);
    seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) = temp_seed;
  } else {
    temp_seed = initial_conditions.temperature();
  }

  dFLOAT density_seed = 0.0;
  if (sim.m_seed_control[controller_index].is_var_seeded[DGF_SEED_VAR_DENSITY]) {
    density_seed = seed_vars->vars(DGF_SEED_VAR_DENSITY, voxel, controller_index);
  }

  // this must test whether the seed file was incompressible
  BOOLEAN based_on_vel_only = (sim.m_seed_control[controller_index].seed_sim_was_incompressible ||
                              !sim.m_seed_control[controller_index].is_var_seeded[DGF_SEED_VAR_PRESSURE] ||
                              !sim.m_seed_control[controller_index].is_var_seeded[DGF_SEED_VAR_DENSITY]);
  dFLOAT vel_scale = sim.m_seed_control[controller_index].compute_seed_velocity_scaling(pressure_seed, temp_seed,
                                                                            density_seed, based_on_vel_only);

  for (int var = DGF_SEED_VAR_XVEL; var < DGF_SEED_VAR_ZVEL + 1; var++) {
    if (sim.m_seed_control[controller_index].is_var_seeded[var]) {
      seed_vars->vars(var, voxel, controller_index) *= vel_scale;
    }
  }
}

template<typename SMART_SEED_DATA>
  __HOST__DEVICE__ static INLINE
VOID scale_seed_vars_via_dimless_properties(SMART_SEED_DATA* seed_vars,
                                            const asINT32 voxel,
                                            const FLUID_SEED_VAR_SPEC seed_var_spec,
                                            const CONDUCTION_INITIAL_CONDITIONS initial_conditions)
{
  auto& sim = get_sim_ref();
  const auto& simc = get_simc_ref();

  sINT32 controller_index = seed_var_spec->seed_controller;
  dFLOAT temp_seed;
  if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) {
    temp_seed = seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) * (simc.char_temp / sim.m_seed_control[controller_index].smart_seed_char_temp);
    seed_vars->vars(DGF_SEED_VAR_TEMP, voxel, controller_index) = temp_seed;
  } else {
    temp_seed = initial_conditions->temperature.value;
  }
}

VOID sSEED_CONTROL::scale_seed_vars_via_mks_properties(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data)
{
  // We need to be able to seed cases from results which have different
  // characteristic and maximum expected velocities using MKS units. See PR 31186.
  dFLOAT pressure_seed;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_PRESSURE)) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_PRESSURE];
    pressure_seed = scale_seed_pressure(pressure_seed_orig);
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_PRESSURE] = pressure_seed / g_density_scale_factor;
  }

  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE)) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE];
    pressure_seed = scale_seed_pressure(pressure_seed_orig);
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE] = pressure_seed / g_density_scale_factor;
  }

  dFLOAT temp_seed;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TEMP)) {
    temp_seed = seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TEMP];
  } else {
    temp_seed = sim.char_temp;
  }

  dFLOAT vel_scale = smart_seed_mks_vel_scaling / sim.mks_vel_scaling;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_XVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_YVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_ZVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL] *= vel_scale;

  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] *= vel_scale;

  dFLOAT char_vel_ratio_2 = vel_scale * vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY)) {
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY] *= char_vel_ratio_2;
  }
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION)) {
    dFLOAT coord_scale = 1.0; // The domain should be scaled the same
    dFLOAT char_vel_ratio_3 = vel_scale * char_vel_ratio_2;
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION] *= coord_scale * char_vel_ratio_3;
  }

  if ((seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X))) {
    // These are dimension less, so nothing should be done.
  }
}

//VOID sTBS_SEED_CONTROL::scale_seed_vars_via_dimless_properties(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data) {
VOID sSEED_CONTROL::scale_seed_vars_via_dimless_properties(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data) {

  dFLOAT pressure_seed;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_PRESSURE)) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_PRESSURE];
    pressure_seed = scale_seed_pressure(pressure_seed_orig);
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_PRESSURE] = pressure_seed / g_density_scale_factor;
  }

  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE)) {
    dFLOAT pressure_seed_orig = g_density_scale_factor * seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE];
    pressure_seed = scale_seed_pressure(pressure_seed_orig);
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TOTAL_PRESSURE] = pressure_seed / g_density_scale_factor;
  }

  dFLOAT temp_seed;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TEMP)) {
    temp_seed = seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TEMP] * (sim.char_temp / smart_seed_char_temp);
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TEMP] = temp_seed;
  } else {
    temp_seed = sim.char_temp; //unused
  }

  dFLOAT density_seed = sim.char_density; // unused
  dFLOAT vel_scale = compute_seed_velocity_scaling(pressure_seed, temp_seed, density_seed, TRUE);
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_XVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_XVEL] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_YVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_YVEL] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_ZVEL))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_ZVEL] *= vel_scale;

  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX] *= vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX))
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX] *= vel_scale;


  dFLOAT char_vel_ratio_2 = vel_scale * vel_scale;
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY)) {
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY] *= char_vel_ratio_2;
  }
  if (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION)) {
    dFLOAT coord_scale = 1.0; // The domain should be scaled the same
    dFLOAT char_vel_ratio_3 = vel_scale * char_vel_ratio_2;
    seed_from_meas_data.var_values[DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION] *= coord_scale * char_vel_ratio_3;
  }

  if ((seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X)) ||
      (seed_from_meas_data.mask & (1 << DGF_BOUNDARY_SEED_VAR_FLOW_DIR_X))) {
    // These are dimension less, so nothing should be done.
  }
}

static BOOLEAN invert_3x3_matrix(dFLOAT matrix[][3], dFLOAT new_matrix[][3]) 
{
  dFLOAT determinant = 
    matrix[0][0] * (matrix[1][1] * matrix[2][2] - matrix[1][2] * matrix[2][1]) -
    matrix[1][0] * (matrix[0][1] * matrix[2][2] - matrix[0][2] * matrix[2][1]) +
    matrix[2][0] * (matrix[0][1] * matrix[1][2] - matrix[0][2] * matrix[1][1]);

  if (fabs(determinant) < DFLOAT_MIN) 
    return FALSE;

  new_matrix[0][0] = (matrix[1][1] * matrix[2][2] - matrix[1][2] * matrix[2][1]) /determinant;
  new_matrix[0][1] = - (matrix[0][1] * matrix[2][2] - matrix[0][2] * matrix[2][1])/determinant;
  new_matrix[0][2] = (matrix[0][1] * matrix[1][2] - matrix[0][2] * matrix[1][1]) /determinant;
  new_matrix[1][0] = -(matrix[1][0] * matrix[2][2] - matrix[1][2] * matrix[2][0])/determinant;
  new_matrix[1][1] = (matrix[0][0] * matrix[2][2] - matrix[0][2] * matrix[2][0]) /determinant;
  new_matrix[1][2] = -(matrix[0][0] * matrix[1][2] - matrix[0][2] * matrix[1][0])/determinant;
  new_matrix[2][0] = (matrix[1][0] * matrix[2][1] - matrix[1][1] * matrix[2][0]) /determinant;
  new_matrix[2][1] = - (matrix[0][0] * matrix[2][1] - matrix[0][1] * matrix[2][0]) /determinant;
  new_matrix[2][2] = (matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0]) /determinant;
  return TRUE;
}

static VOID compute_orthogonal_axes(dFLOAT x[3],
                                    dFLOAT y[3],
                                    dFLOAT z[3])
{
  g3_vec_to_array(g3_vec_perpendicular(g3_vec_from_array(x)),y);
  vunitize(y);
  vcross(z,x,y);
  vunitize(z);

}

VOID compute_curved_axes(UBLK ublk,
                         asINT32 voxel,
                         asINT32 scale,
                         STP_GEOM_VARIABLE curved_x_axis[3],
                         BOOLEAN is_2d,
                         BOOLEAN compute_y_axis_p, 
                         BOOLEAN maybe_issue_warning_p,
                         STP_GEOM_VARIABLE porous_y_axis[3], 
                         STP_GEOM_VARIABLE curved_y_axis[3], 
                         STP_GEOM_VARIABLE curved_z_axis[3]) 
{
  dFLOAT x[3], y[3], z[3];
  sG3_VEC vel_vec;

  vcopy(x,curved_x_axis);
  if (vdot(x, x) == 0.0) {
    msg_internal_error("Normal of reference facet for curved PM for voxel %d in Ublk %d at [%d %d %d] is zero",
                       voxel, ublk->id(), ublk->location(0),
                       ublk->location(1), ublk->location(2));
  }
  vunitize(x);

  if (is_2d) {
    y[0] = -x[1]; y[1] = x[0]; y[2] = 0;
    z[0] = z[1] = 0; z[2] = 1;
  }
  else {
    if (compute_y_axis_p) {
      compute_orthogonal_axes(x,y,z);
    } 
    else { // take the Y axis from the supplied argument 
      vcopy(y, porous_y_axis);
      vunitize(y);
      vcross(z,x,y);
      if (sqrt(vdot(z,z)) <= 1.0e-7) {
        if (maybe_issue_warning_p) {
          report_collinear_curved_pm_axes(ublk, voxel, scale);
        }                                                                                           
        // make up non-linear collinear y and z axes
        compute_orthogonal_axes(x,y,z);
      }
      else {
        vunitize(z);
        vcross(y,z,x);
      }
    }
  }
  
  // cache the local voxel axes for use in dynamics
  vcopy(curved_x_axis, x);
  vcopy(curved_y_axis, y);
  vcopy(curved_z_axis, z);


}

VOID convert_voxel_vel_from_curved_coords_to_global_coords(LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_conditions, 
                                                           STP_GEOM_VARIABLE curved_x_axis[3], 
                                                           STP_GEOM_VARIABLE curved_y_axis[3], 
                                                           STP_GEOM_VARIABLE curved_z_axis[3]) 
{

  sG3_VEC local_vel = {initial_conditions.velocity(0),
                       initial_conditions.velocity(1),
                       initial_conditions.velocity(2)};

  dFLOAT Ainv[3][3];
  dFLOAT A[3][3] = {{curved_x_axis[0], curved_x_axis[1], curved_x_axis[2]},
                    {curved_y_axis[0], curved_y_axis[1], curved_y_axis[2]},
                    {curved_z_axis[0], curved_z_axis[1], curved_z_axis[2]}};

  // already checked for collinearity previously, so skip checking the inverse
  invert_3x3_matrix(A,Ainv);

  sG3_XFORM A_xform = g3_xform_make_unit();
  G3_DO_COORDS(i, {
      G3_DO_COORDS(j, {
        // g3 transform matrix is actually transpose of what one would
        // consider a regular transformation matrix
        A_xform.xcoord[i][j] = Ainv[j][i];
        });
      });

  sG3_VEC global_vel = g3_xform_vec(local_vel,A_xform);

  initial_conditions.velocity(0) = global_vel.vcoord[0];
  initial_conditions.velocity(1) = global_vel.vcoord[1];
  initial_conditions.velocity(2) = global_vel.vcoord[2];


}

__HOST__DEVICE__
BOOLEAN are_fluid_initial_conditions_constant(LES_FLUID_INITIAL_CONDITIONS initial_conditions,
					      FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions,
					      BOOLEAN is_turb)
{
  auto& simc = get_simc_ref();
  BOOLEAN all_constant = (!initial_conditions->m_velocity[0].is_space_varying
			  && !initial_conditions->m_velocity[1].is_space_varying
			  && !initial_conditions->m_velocity[2].is_space_varying
			  && !initial_conditions->m_temperature.is_space_varying);

#if BUILD_5G_LATTICE
  if (initial_conditions->m_component_ratio_type.value == CDI_COMP_RATIO_VIA_PRESS_AND_FRAC) {
    all_constant = all_constant && (!initial_conditions->m_pressure.is_space_varying
				    && !initial_conditions->m_comp0_vol_frac.is_space_varying
				    && !initial_conditions->m_phase_index.is_space_varying);
  } else if (initial_conditions->m_component_ratio_type.value == CDI_COMP_RATIO_VIA_DENS) {
    all_constant = all_constant && (!initial_conditions->m_comp0_density.is_space_varying
				    && (!g_is_multi_component || !initial_conditions->m_comp1_density.is_space_varying));
  } else if (initial_conditions->m_component_ratio_type.value == CDI_COMP_RATIO_VIA_DENS_AND_FRAC) {
    all_constant = all_constant && (!initial_conditions->m_total_density.is_space_varying
				    && !initial_conditions->m_comp0_vol_frac.is_space_varying);
  } else {
    msg_internal_error("Unregonized initial component ratio type %f.", initial_conditions->m_component_ratio_type.value);
  }
#else
  all_constant = all_constant && (!initial_conditions->m_pressure.is_space_varying);   
#endif

#if !BUILD_5G_LATTICE
  if (simc.is_scalar_model) {
    if (simc.uds_solver_type == LB_UDS) {
      assert(uds_initial_conditions != NULL); //do_debug
      
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
	all_constant = all_constant && (!uds_initial_conditions->m_uds_value.is_space_varying);
	uds_initial_conditions++;
      }
    } else {//PDE_UDS					
      all_constant = all_constant && (!initial_conditions->m_water_mass_fraction.is_space_varying);
    }
  }
  
#endif

  if (is_turb) {
    all_constant = all_constant && (!initial_conditions->m_turb_profile_via.is_space_varying
				    && !initial_conditions->m_turb_intensity.is_space_varying
				    && !initial_conditions->m_turb_length_scale.is_space_varying
				    && !initial_conditions->m_turb_kinetic_energy.is_space_varying
				    && !initial_conditions->m_turb_dissipation.is_space_varying);
  }
  
  return all_constant;
}


BOOLEAN are_conduction_initial_conditions_constant(CONDUCTION_INITIAL_CONDITIONS initial_conditions)
{
  return !initial_conditions->temperature.is_space_varying;
}

/*--------------------------------------------------------------------------*
 * Extrapolating seed data
 *--------------------------------------------------------------------------*/

const int MAX_N_ITERS = 20; //the max number of iterations per voxel

static UBLK seed_extrapolate_list = NULL;

VOID add_ublk_to_seed_extrapolate_list(UBLK ublk)
{
  UBLK_SMART_SEED_DATA smart_seed_data = ublk->smart_seed_data();
  
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    if (smart_seed_data->missing_seed_data(voxel))
      smart_seed_data->iter_count(voxel) = 0;
  }

  smart_seed_data->next() = seed_extrapolate_list;
  seed_extrapolate_list = ublk;
}

#define SEED_DATA_TOL 1e-3f
/* if both values are 0, return false */
static inline BOOLEAN value_changed(sdFLOAT v0, sdFLOAT v1)
{
  sdFLOAT d = fabs(v0-v1);
  sdFLOAT u0 = fabs(v0);
  sdFLOAT u1 = fabs(v1);
  return d > (SEED_DATA_TOL * (u0+u1));
}

// voxel_seed_var_spec is the seed_var_spec of the ublk of interest.
// neighbor_ublk is a neighbor of the ublk of interest 
static sdFLOAT accumulate_voxel_seed_vars(VOXEL_STATE *accumulated_seed_vars,
					  VOXEL_STATE *accumulated_seed_uds_vars,
                                          UBLK neighbor_ublk,
                                          asINT32 neighbor_voxel, sdFLOAT split_factor)
{
  UBLK_SMART_SEED_DATA smart_seed_data = neighbor_ublk->smart_seed_data();

  if ((smart_seed_data->missing_seed_data(neighbor_voxel) && smart_seed_data->iter_count(neighbor_voxel) == 0)
      || !smart_seed_data->needs_seed_data(neighbor_voxel))
    return 0.0; 
  
  // Extrapolate all variables
  uINT8 seed_var_spec_index = smart_seed_data->w[neighbor_voxel].seed_var_spec_index; 
  uINT8 controller_index = g_fluid_seed_var_specs[seed_var_spec_index].seed_controller;
  //sINT32 controller_index = smart_seed_data->w[neighbor_voxel].seed_controller;
  ccDOTIMES(i, sim.m_seed_control[controller_index].n_smart_seed_vars) {
    asINT32 v = sim.m_seed_control[controller_index].smart_seed_var_types[i];
    accumulated_seed_vars[v] += smart_seed_data->vars(v, neighbor_voxel, controller_index) * split_factor;
  }

  ccDOTIMES(i, sim.m_seed_control[controller_index].n_smart_seed_uds_vars) {
    asINT32 v = sim.m_seed_control[controller_index].smart_seed_uds_var_indices[i];
    asINT32 index_in_enum;
    asINT32 nth_uds;
    find_uds_indices(v, index_in_enum, nth_uds);
    accumulated_seed_uds_vars[v] += neighbor_ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum, neighbor_voxel) * split_factor;
  }
  
  return split_factor;
}

// EXTRAPOLATE_UBLK_DATA returns TRUE if some voxel initially missing seed data has yet to be iterated
// MAX_N_ITERS times. seed_data_changed is set to TRUE if the seed data for a voxel is changed. 
// seed_data_changed is never set to FALSE by this routine.
static BOOLEAN extrapolate_ublk_data(UBLK ublk, BOOLEAN &seed_data_changed)
{
  BOOLEAN rv = FALSE;
  asINT32 n_dims = sim.num_dims;
  VR_TOPOLOGY vr_topology = &ublk->vr_topology();
  UBLK_SMART_SEED_DATA smart_seed_data = ublk->smart_seed_data();
  auto& box_access = ublk->box_access();
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    uINT8 seed_var_spec_index = smart_seed_data->w[voxel].seed_var_spec_index; 
    uINT8 controller_index = g_fluid_seed_var_specs[seed_var_spec_index].seed_controller;
    asINT32 n_smart_seed_vars = sim.m_seed_control[controller_index].n_smart_seed_vars;
    DGF_SEED_VAR_TYPE *seed_var_types = sim.m_seed_control[controller_index].smart_seed_var_types;
    //UDS
    asINT32 n_smart_seed_uds_vars = sim.m_seed_control[controller_index].n_smart_seed_uds_vars;
    sINT16  *seed_uds_var_indices = sim.m_seed_control[controller_index].smart_seed_uds_var_indices;
    if (smart_seed_data->missing_seed_data(voxel)) {
      VOXEL_STATE neighbor_seed_vars[DGF_N_SEED_VARS];
      VOXEL_STATE neighbor_seed_uds_vars[MAX_N_SEED_UDS_VARS];
      asINT32 split_factor_sum = 0;
  
      ccDOTIMES(i, n_smart_seed_vars)
        neighbor_seed_vars[seed_var_types[i]] = 0;

      ccDOTIMES(i, n_smart_seed_uds_vars)
        neighbor_seed_uds_vars[seed_uds_var_indices[i]] = 0;
     
      ccDOTIMES(axis, sim.num_dims) {
        asINT32 neighbor_voxel;
        asINT32 forward_face, backward_face;
        TAGGED_UBLK forward_neighbor, backward_neighbor;
        BOOLEAN is_forward_neighbor_different_scale;
        BOOLEAN is_backward_neighbor_different_scale;
        BOOLEAN is_forward_neighbor_finer_scale;
        BOOLEAN is_backward_neighbor_finer_scale;
        voxel_neighbors_on_axis(axis, ublk, voxel, neighbor_voxel,
                                forward_face, backward_face,
                                forward_neighbor, backward_neighbor,
                                is_forward_neighbor_different_scale,
                                is_backward_neighbor_different_scale,
                                is_forward_neighbor_finer_scale,
                                is_backward_neighbor_finer_scale);

        if (is_forward_neighbor_finer_scale) {
          auINT32 fine_ublk_index = neighbor_voxel_along_axis(voxel, axis);
          sSCALE_BOX_INTERFACE *interface = forward_neighbor.interface();
          TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[fine_ublk_index];
          if (fine_tagged_ublk.is_ublk_split()) {
            printf("Do something special for this split neighbor %s %d\n", __FILE__, __LINE__);
            // vr_fine_blk is vr fine ublk under the coarse voxel
            asINT32 fine_blk_idx = voxel;
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
            UBLK vr_fine_blk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
            sTAGGED_SPLIT_FACTOR for_split_facs[MAX_SPLIT_UBLKS];
            asINT32 n_for_splits =
              collect_fine_split_neighbors(fine_tagged_ublk, vr_fine_blk, axis, 1,
                                           for_split_facs);
            // Add the contributions from all the neighboring split ublks
            // Quantities should be multiplied by pfluid while accumulation
            ccDOTIMES(nInt, n_for_splits) {
              sdFLOAT split_fac = for_split_facs[nInt].neighbor_split_factor;
              TAGGED_UBLK fine_neighbor = for_split_facs[nInt].tagged_neighbor;
              UBLK fine_ublk = fine_neighbor.ublk();
              DO_VOXELS_ON_UBLK_FACE(fine_voxel, backward_face) {
                split_factor_sum += accumulate_voxel_seed_vars(
                    neighbor_seed_vars, neighbor_seed_uds_vars, fine_ublk, fine_voxel, split_fac);
              }
            }
          } else if (fine_tagged_ublk.is_ublk()) {
            UBLK fine_ublk = interface->m_fine_ublks[fine_ublk_index].ublk();
            DO_VOXELS_ON_UBLK_FACE(fine_voxel, backward_face) {
              split_factor_sum += accumulate_voxel_seed_vars(neighbor_seed_vars,
							     neighbor_seed_uds_vars,
                                                             fine_ublk,
                                                             fine_voxel, 1.0);
            }
          }
        } else if (!forward_neighbor.is_ublk_split()) {
          // Same scale or coarser (but really looking at VR fine)
          // If we really we're directly connected to a coarse voxel, we should use
          // coarse_neighbor_voxel.
          // asINT32 coarse_neighbor_voxel =
          //   neighbor_voxel_along_axis(vr_topology->coarse_voxel(), axis);
          UBLK forward_ublk = forward_neighbor.ublk();
          if (forward_ublk != NULL)
            split_factor_sum += accumulate_voxel_seed_vars( neighbor_seed_vars,
							    neighbor_seed_uds_vars,
                                                            forward_ublk,
                                                            neighbor_voxel,
                                                            1.0);

        } else {  // ublk is split
          sTAGGED_SPLIT_FACTOR  forward_split_facs[MAX_SPLIT_UBLKS];
          asINT32 n_forward_splits =
            tagged_neighbor_from_split_ublk_site(forward_neighbor,
                                                 ublk->advect_from_split_mask(),
                                                 voxel, STATE_P(axis),
                                                 split_advect_info,
                                                 ublk->m_split_tagged_instance.get(),
                                                 forward_split_facs);

          if (n_forward_splits == 0)
            msg_internal_error("Ublk %d forward neighbor on axis %d should be split. However n_forward_splits = 0.", ublk->id(), axis);
          ccDOTIMES(nInt, n_forward_splits) {
            sdFLOAT split_fac = forward_split_facs[nInt].neighbor_split_factor;
            forward_neighbor = forward_split_facs[nInt].tagged_neighbor;
            UBLK forward_ublk = forward_neighbor.ublk();
            split_factor_sum += accumulate_voxel_seed_vars(neighbor_seed_vars,
							   neighbor_seed_uds_vars,
                                                           forward_ublk,
                                                           neighbor_voxel,
                                                           split_fac);
          }
        }

        if (is_backward_neighbor_finer_scale) {
          auINT32 fine_ublk_index = neighbor_voxel_along_axis(voxel, axis);
          sSCALE_BOX_INTERFACE *interface = backward_neighbor.interface();
          TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[fine_ublk_index];
          if (fine_tagged_ublk.is_ublk_split()) {
            printf("Do something special for this split neighbor %s %d\n",
                   __FILE__, __LINE__);
            // vr_fine_blk is vr fine ublk under the coarse voxel
            asINT32 fine_blk_idx = voxel;
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
            UBLK vr_fine_blk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
            sTAGGED_SPLIT_FACTOR bac_split_facs[MAX_SPLIT_UBLKS];
            asINT32 n_bac_splits = collect_fine_split_neighbors(fine_tagged_ublk,
                                                                vr_fine_blk,
                                                                axis, -1,
                                                                bac_split_facs);
            // Add the contributions from all the neighboring split ublks
            // Quantities should be multiplied by pfluid while accumulation
            ccDOTIMES(nInt, n_bac_splits)
            {
              sdFLOAT split_fac = bac_split_facs[nInt].neighbor_split_factor;
              TAGGED_UBLK fine_neighbor = bac_split_facs[nInt].tagged_neighbor;
              UBLK fine_ublk = fine_neighbor.ublk();
              DO_VOXELS_ON_UBLK_FACE(fine_voxel, forward_face)
              {
                split_factor_sum += accumulate_voxel_seed_vars(
                    neighbor_seed_vars, neighbor_seed_uds_vars, fine_ublk, fine_voxel, split_fac);
              }
            }
          } else if (fine_tagged_ublk.is_ublk()) {
            UBLK fine_ublk = interface->m_fine_ublks[fine_ublk_index].ublk();
            // If it is any consolation, you can rest assure that
            // FINE_UBLK is not VR fine
            DO_VOXELS_ON_UBLK_FACE(fine_voxel, forward_face)
            {
              split_factor_sum += accumulate_voxel_seed_vars(neighbor_seed_vars,
							     neighbor_seed_uds_vars,
                                                             fine_ublk,
                                                             fine_voxel, 1.0);
            }
          }
        } else if (!backward_neighbor.is_ublk_split()) {
          // Same scale or coarser (but really looking at VR fine)
          // If we really we're directly connected to a coarse voxel, we should use
          // coarse_neighbor_voxel.
          // asINT32 coarse_neighbor_voxel =
          //   neighbor_voxel_along_axis(vr_topology->coarse_voxel(), axis);
          UBLK backward_ublk = backward_neighbor.ublk();
          if (backward_ublk != NULL)
            split_factor_sum += accumulate_voxel_seed_vars( neighbor_seed_vars,
							    neighbor_seed_uds_vars,
                                                            backward_ublk,
                                                            neighbor_voxel,
                                                            1.0);
        } else { // back neighbor is split
          sTAGGED_SPLIT_FACTOR backward_split_facs[MAX_SPLIT_UBLKS];
          asINT32 n_backward_splits =
            tagged_neighbor_from_split_ublk_site(backward_neighbor,
                                                 ublk->advect_from_split_mask(),
                                                 voxel, STATE_N(axis),
                                                 split_advect_info,
                                                 ublk->m_split_tagged_instance.get(),
                                                 backward_split_facs);
          if (n_backward_splits == 0)
            msg_internal_error("Ublk %d backward neighbor on axis %d should be split. However n_backward_splits = 0.", ublk->id(), axis);
          ccDOTIMES(nInt, n_backward_splits) {
            sdFLOAT split_fac = backward_split_facs[nInt].neighbor_split_factor;
            backward_neighbor = backward_split_facs[nInt].tagged_neighbor;
            UBLK backward_ublk = backward_neighbor.ublk();
            split_factor_sum += accumulate_voxel_seed_vars(neighbor_seed_vars,
							   neighbor_seed_uds_vars,
                                                           backward_ublk,
                                                           neighbor_voxel,
                                                           split_fac);
          }
        }
      }
      if (split_factor_sum > 0) {
        sdFLOAT inv_split_factor_sum = 1.0 / split_factor_sum;
        if (smart_seed_data->iter_count(voxel) == 0)
          seed_data_changed = TRUE;
        ccDOTIMES(i, n_smart_seed_vars) {
          asINT32 v = seed_var_types[i];
          neighbor_seed_vars[v] *= inv_split_factor_sum;
          if (!seed_data_changed &&
              (smart_seed_data->iter_count(voxel) < MAX_N_ITERS) &&
              value_changed(smart_seed_data->vars(v, voxel, controller_index), neighbor_seed_vars[v])) {
            seed_data_changed = TRUE;
          }
          smart_seed_data->vars(v, voxel, controller_index) = neighbor_seed_vars[v];
        }

	//UDS
	ccDOTIMES(i, n_smart_seed_uds_vars) {
          asINT32 v = seed_uds_var_indices[i];
          neighbor_seed_uds_vars[v] *= inv_split_factor_sum;
	  asINT32 index_in_enum;  //in enum DGF_SEED_UDS_VAR_TYPE
	  asINT32 nth_uds;
	  find_uds_indices(v, index_in_enum, nth_uds);
          if (!seed_data_changed &&
              (smart_seed_data->iter_count(voxel) < MAX_N_ITERS) &&
              value_changed(ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum, voxel), neighbor_seed_uds_vars[v])) {
            seed_data_changed = TRUE;
          }
          ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum,voxel) = neighbor_seed_uds_vars[v];
        }

        if (smart_seed_data->iter_count(voxel) < MAX_N_ITERS) {
          smart_seed_data->iter_count(voxel) ++;
          rv = TRUE;
        }

        ublk->voxel_smart_seed_mask.set(voxel);
      } else
        rv = TRUE;
    }
  }
  return rv;
}

static inline VOID copy_voxel_seed_data(UBLK from_ublk, asINT32 from_voxel, UBLK to_ublk, asINT32 to_voxel)
{
  UBLK_SMART_SEED_DATA from_smart_seed_data = from_ublk->smart_seed_data();
  UBLK_SMART_SEED_DATA to_smart_seed_data   = to_ublk->smart_seed_data();

  to_smart_seed_data->missing_seed_data(to_voxel) = from_smart_seed_data->missing_seed_data(from_voxel);
  to_smart_seed_data->needs_seed_data(to_voxel)   = from_smart_seed_data->needs_seed_data(from_voxel);
  to_smart_seed_data->iter_count(to_voxel)        = from_smart_seed_data->iter_count(from_voxel);

  // Get the "from" controller index
  uINT8 from_seed_var_spec_index = from_smart_seed_data->w[from_voxel].seed_var_spec_index; 
  uINT8 from_controller_index = g_fluid_seed_var_specs[from_seed_var_spec_index].seed_controller;

  // Get the "to" controller index
  uINT8 to_seed_var_spec_index = to_smart_seed_data->w[to_voxel].seed_var_spec_index; 
  uINT8 to_controller_index = g_fluid_seed_var_specs[to_seed_var_spec_index].seed_controller;

  // Arbitrarily choosing to loop over the "from" controller.
  ccDOTIMES(i, sim.m_seed_control[from_controller_index].n_smart_seed_vars) {
    asINT32 v = sim.m_seed_control[from_controller_index].smart_seed_var_types[i];

    if (sim.m_seed_control[from_controller_index].active_seed_var_index[v] == -1) {
      // The "from" ublk doesn't have this variable so it can't copy it to the
      // "to" ublk.
      continue;
    }

    to_smart_seed_data->vars(v, to_voxel, to_controller_index) = from_smart_seed_data->vars(v, from_voxel, from_controller_index);

  }

  ccDOTIMES(i, sim.m_seed_control[from_controller_index].n_smart_seed_uds_vars) {
    asINT32 v = sim.m_seed_control[from_controller_index].smart_seed_uds_var_indices[i];
    asINT32 index_in_enum;
    asINT32 nth_uds;
    find_uds_indices(v, index_in_enum, nth_uds);
    to_ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum, to_voxel) = from_ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum, from_voxel);
  }
}

std::unordered_set<SHOB_ID> g_first_vr_fine_ublks;

static VOID explode_vr_interface_seed_data(BOOLEAN first_time)
{
  // The explode operation is done multiple times for a fine ublk
  asINT32 n_dims = sim.num_dims;
  asINT32 num_vr_fine = ubFLOAT::N_VOXELS;
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {    
      sVR_FINE_INTERFACE_DATA * vr_fine_data = fine_ublk->vr_fine_data();

      if (g_first_vr_fine_ublks.count(fine_ublk->id())) {
        UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
        ccDOTIMES(i, num_vr_fine) {
          sVR_COARSE_INTERFACE_DATA * vr_coarse_data = coarse_ublk->vr_coarse_data();
          UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
          if (fine_ublk != NULL) {
            asINT32 coarse_voxel = i;
            if (first_time || coarse_ublk->smart_seed_data()->missing_seed_data(coarse_voxel)) {
              ccDOTIMES(fine_voxel, 8) {
                copy_voxel_seed_data(coarse_ublk, coarse_voxel, fine_ublk, fine_voxel);
              }
            }
          }
        }
      }
    }
  }
}

static BOOLEAN some_sp_needs_more_extrapolation;

static VOID notify_sps_of_need_for_more_extrapolation(BOOLEAN more_iterations_needed,
                                                      BOOLEAN seed_data_changed) 
{
  int flags = (more_iterations_needed == TRUE) | ((seed_data_changed == TRUE) << 1);
  int recv_flags = 0;
  MPI_Allreduce(&flags, &recv_flags, 1, MPI_INT, MPI_BOR, eMPI_sp_comm);

  // We should extrapolate further if at least one SP has voxels yet to be iterated MAX_N_ITERS times,
  // and one SP saw a change in the seed data in the last iteration.
  some_sp_needs_more_extrapolation = (recv_flags & 1) && (recv_flags & 2);
}

static VOID extrapolate_seed_data()
{
  
  some_sp_needs_more_extrapolation = FALSE;
  notify_sps_of_need_for_more_extrapolation(seed_extrapolate_list != NULL, seed_extrapolate_list != NULL);

  BOOLEAN first_time_explode = TRUE;
  // This loop repeatedly walks over the list of unseeded ublks
  while (some_sp_needs_more_extrapolation) {
    some_sp_needs_more_extrapolation = FALSE;
    explode_vr_interface_seed_data(first_time_explode);
    first_time_explode = FALSE;

    // TODO: Only the seed_data which overlays the ublk states needs to be
    // comm'ed between the SPs during extrapolation. Regular ublk comm below
    // for dynblks and vrblks also includes the solver data-blocks, which is
    // not needed at this point. It would be good to optimize this.
    do_preliminary_ublk_comm(NEXT_TIMESTEP, TRUE, TRUE);

    BOOLEAN more_iterations_needed = FALSE;
    BOOLEAN seed_data_changed = FALSE;
    for (UBLK ublk = seed_extrapolate_list;
	 ublk != NULL;
	 ublk = ublk->smart_seed_data()->next()) {
      if (extrapolate_ublk_data(ublk, seed_data_changed))
        more_iterations_needed = TRUE;
    }

    notify_sps_of_need_for_more_extrapolation(more_iterations_needed, seed_data_changed);
  }

  UBLK ublk = seed_extrapolate_list;
  while (ublk != NULL) {
    UBLK next_ublk = ublk->smart_seed_data()->next();
    // zero the next ptr to ensure that advect-only voxels are left with zero'ed states
    ublk->smart_seed_data()->next() = 0;
    ublk = next_ublk;
  }
  seed_extrapolate_list = NULL;
}

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID convert_smart_seed_vars_to_seed_data(UBLK_TYPE* ublk,
                                          const asINT32 voxel, 
                                          const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_condition_values,
                                          VOXEL_LES_SEED_DATA voxel_seed_data, 
                                          const uINT8 seed_var_spec_index,
                                          LRF_PHYSICS_DESCRIPTOR lrf)
{
  auto smart_seed_data = ublk->smart_seed_data();
  FLUID_SEED_VAR_SPEC seed_var_spec = &get_fluid_seed_var_spec(seed_var_spec_index);
  auto& sim = get_sim_ref();
  const auto& simc = get_simc_ref();
  VOXEL_STATE pressure;
  VOXEL_STATE temp;

    // This updates the pressure, temp, and vel entries in smart_seed_data->vars
  sINT32 controller_index = seed_var_spec->seed_controller;

  if (sim.m_seed_control[controller_index].seed_via_dimless_properties)
    scale_seed_vars_via_dimless_properties(smart_seed_data, voxel, seed_var_spec, initial_condition_values);
  else if (sim.m_seed_control[controller_index].seed_via_mks)
    scale_seed_vars_via_mks(smart_seed_data, voxel, seed_var_spec, initial_condition_values);

#if BUILD_5G_LATTICE
  if (g_mp_meas_seed_pres) {
    // 5g uses velocity slots in smart_seed_data to store grad_p calculated in single component ideal gas case
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_XVEL]) 
      voxel_seed_data->grad_p[0] = smart_seed_data->vars(DGF_SEED_VAR_XVEL, voxel, controller_index);
    else
      voxel_seed_data->grad_p[0] = 0;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_YVEL]) 
      voxel_seed_data->grad_p[1] = smart_seed_data->vars(DGF_SEED_VAR_YVEL, voxel, controller_index);
    else
      voxel_seed_data->grad_p[1] = 0;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_ZVEL]) 
      voxel_seed_data->grad_p[2] = smart_seed_data->vars(DGF_SEED_VAR_ZVEL, voxel, controller_index);
    else
      voxel_seed_data->grad_p[2] = 0;
  }
  else {
    // 5g normal seeding
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_COMP0_DENSITY])
      voxel_seed_data->density = smart_seed_data->vars(DGF_SEED_VAR_COMP0_DENSITY, voxel, controller_index) * g_density_scale_factor;
    
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_COMP1_DENSITY])
      voxel_seed_data->density_mc = smart_seed_data->vars(DGF_SEED_VAR_COMP1_DENSITY, voxel, controller_index) * g_density_scale_factor;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_XVEL])
      voxel_seed_data->velocity[0] = smart_seed_data->vars(DGF_SEED_VAR_XVEL, voxel, controller_index) * g_lattice_constant;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_YVEL])
      voxel_seed_data->velocity[1] = smart_seed_data->vars(DGF_SEED_VAR_YVEL, voxel, controller_index) * g_lattice_constant;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_ZVEL])
      voxel_seed_data->velocity[2] = smart_seed_data->vars(DGF_SEED_VAR_ZVEL, voxel, controller_index) * g_lattice_constant;
    else if (simc.is_2d())
      voxel_seed_data->velocity[2] = initial_condition_values.velocity(2);  //keep as ZERO

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER])
      voxel_seed_data->dynamic_scalar_multiplier =
          smart_seed_data->vars(DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER, voxel, controller_index);

    if (sim.is_large_pore) {
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TURB_DISSIPATION])  //borrow
	voxel_seed_data->pm_mode_flag = smart_seed_data->vars(DGF_SEED_VAR_TURB_DISSIPATION, voxel, controller_index);

      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TURB_KINETIC_ENERGY])   //borrow
	voxel_seed_data->porosity = smart_seed_data->vars(DGF_SEED_VAR_TURB_KINETIC_ENERGY, voxel, controller_index);

      if (g_mp_pm_table_input && seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) //borrow
	voxel_seed_data->rock_type_index = smart_seed_data->vars(DGF_SEED_VAR_TEMP, voxel, controller_index);
    }

    if ((sim.is_large_pore && g_is_multi_component)  || (sim.smart_seed_contact_angle && ublk->is_near_surface())) {
      //what if the voxel has no seed_data? it will be seeded from the initial component density
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_CONTACT_ANGLE] || g_mp_pm_table_input_contact_angle) {
	  sdFLOAT comp0_angle;
	  sdFLOAT comp1_angle;
      if(g_mp_pm_table_input_contact_angle){ //take contact angle specified in column 2 of "info.txt", or default if "info.txt" not exist
        asINT32 rock_type_index = voxel_seed_data->rock_type_index;
        //printf("angle:%e \n",sim.rock_types[rock_type_index].get_contact_angle_Pc());
        comp0_angle = sim.rock_types[rock_type_index].get_contact_angle_Pc();
      }
      else{
	    comp0_angle = smart_seed_data->vars(DGF_SEED_VAR_CONTACT_ANGLE, voxel, controller_index);
      }
	  comp1_angle = 90.0F;

	if (comp0_angle < 0.0F || comp0_angle > 180.0F) {
	  msg_internal_error ("The seeded contact angle %f in voxel %d of ublk %d is out of the range [0, 180]",
                              comp0_angle, voxel, ublk->id());
        }
	else {
	  voxel_seed_data->contact_angle = comp0_angle;  //large pore
	  if (comp0_angle > 90.0F) {
	    comp1_angle = 180.F - comp0_angle;
	    comp0_angle = 90.0;
	  }
	}

	if (g_is_multi_component) {
	  sdFLOAT used_viscosity = MAX(g_mc_types[0].viscosity, g_mc_types[1].viscosity);
	  if (comp0_angle > comp1_angle && g_mc_types[0].viscosity < g_mc_types[1].viscosity)
	    used_viscosity = g_mc_types[0].viscosity * g_mp_NuTrationeq1_factor + g_mc_types[1].viscosity * (1.0 - g_mp_NuTrationeq1_factor);
	  else if (comp0_angle < comp1_angle && g_mc_types[0].viscosity > g_mc_types[1].viscosity)
	    used_viscosity = g_mc_types[1].viscosity * g_mp_NuTrationeq1_factor + g_mc_types[0].viscosity * (1.0 - g_mp_NuTrationeq1_factor);

	  voxel_seed_data->potential[0] = 0.22 * contact_angle_to_potential(comp0_angle, used_viscosity);  //ideal type EOS
	  voxel_seed_data->potential[1] = 0.22 * contact_angle_to_potential(comp1_angle, used_viscosity);  //ideal type EOS

	  if (voxel_seed_data->potential[0] < 0.0F)
	    voxel_seed_data->potential[0] = 0.0F;
	  if (voxel_seed_data->potential[1] < 0.0F)
	    voxel_seed_data->potential[1] = 0.0F;

	  if (sim.is_large_pore) {
	    voxel_seed_data->wall_potential[0] = voxel_seed_data->potential[0];
	    voxel_seed_data->wall_potential[1] = voxel_seed_data->potential[1];
	  }
	} else {
	  voxel_seed_data->potential[0] = 0.22 * contact_angle_to_potential(comp0_angle, g_mc_types[0].viscosity);
	  if (voxel_seed_data->potential[0] < 0.0F)
	    voxel_seed_data->potential[0] = 0.0F;
	}
      }
    }
  }

#else
#if BUILD_D19_LATTICE
  if (g_meas_seed_pres_d19) {
    // uses velocity slots in smart_seed_data to store grad_p calculated
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_XPRESSURE_GRADIENT]) 
      voxel_seed_data->grad_p[0] = smart_seed_data->vars(DGF_SEED_VAR_XPRESSURE_GRADIENT, voxel, controller_index);
    else
      voxel_seed_data->grad_p[0] = 0;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_YPRESSURE_GRADIENT]) 
      voxel_seed_data->grad_p[1] = smart_seed_data->vars(DGF_SEED_VAR_YPRESSURE_GRADIENT, voxel, controller_index);
    else
      voxel_seed_data->grad_p[1] = 0;

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_ZPRESSURE_GRADIENT]) 
      voxel_seed_data->grad_p[2] = smart_seed_data->vars(DGF_SEED_VAR_ZPRESSURE_GRADIENT, voxel, controller_index);
    else
      voxel_seed_data->grad_p[2] = 0;

    //seed pressure from laplace solver to the current simulation to recover the real pressure
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_PRESSURE])
      voxel_seed_data->pressure_from_laplace = smart_seed_data->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index);
    else
      voxel_seed_data->pressure_from_laplace = 0;
  } else
#endif
  { 
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_PRESSURE])
      pressure = smart_seed_data->vars(DGF_SEED_VAR_PRESSURE, voxel, controller_index);
    else
      pressure = initial_condition_values.pressure();

    // Note: The velocity in smart_seed_data is with respect to the global ref frame and the local CSYS.
    //       This was ensured in CP before sending over the data to the SPs.
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_XVEL])
      voxel_seed_data->velocity[0] = smart_seed_data->vars(DGF_SEED_VAR_XVEL, voxel, controller_index);

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_YVEL])
      voxel_seed_data->velocity[1] = smart_seed_data->vars(DGF_SEED_VAR_YVEL,voxel, controller_index);

    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_ZVEL])
      voxel_seed_data->velocity[2] = smart_seed_data->vars(DGF_SEED_VAR_ZVEL,voxel, controller_index);
    else if (simc.is_2d())
      voxel_seed_data->velocity[2] = initial_condition_values.velocity(2);  //keep as ZERO

    // Rotate the seed velocity about x,y,z-axis of any csys. 
    // Options specified using -seed_rotate_vel "<coord_system>,<x|y|z>,<angle>"
    // Do this only if all velocity components are seeded
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_XVEL]
	&& seed_var_spec->is_var_seeded[DGF_SEED_VAR_YVEL]
	&& (simc.is_2d() || seed_var_spec->is_var_seeded[DGF_SEED_VAR_ZVEL])
	&& seed_var_spec->rotation_angle != 0.0) {
      if (lrf!=NULL && lrf->is_mlrf_on) {
	// local CSYS to global CSYS
	rotate_vector(voxel_seed_data->velocity, voxel_seed_data->velocity, lrf->local_to_global_rotation_matrix);
      }

      // rotate about the specified axis
      sG3_VEC velocity_vec;
      velocity_vec.vcoord[0] = voxel_seed_data->velocity[0];
      velocity_vec.vcoord[1] = voxel_seed_data->velocity[1];
      velocity_vec.vcoord[2] = voxel_seed_data->velocity[2];
      velocity_vec = g3_xform_vec(velocity_vec, seed_var_spec->rotation_xform);
      voxel_seed_data->velocity[0] = velocity_vec.vcoord[0];
      voxel_seed_data->velocity[1] = velocity_vec.vcoord[1];
      voxel_seed_data->velocity[2] = velocity_vec.vcoord[2];

      if (lrf!=NULL && lrf->is_mlrf_on) {
	// global CSYS to local CSYS
	BOOLEAN transpose = TRUE;
	rotate_vector(voxel_seed_data->velocity, voxel_seed_data->velocity, lrf->local_to_global_rotation_matrix, transpose);
      }
    }

    if (simc.is_heat_transfer) {
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP]) {
        temp = voxel_seed_data->passive_scalar_temp = smart_seed_data->vars(DGF_SEED_VAR_TEMP, voxel, controller_index);
      } else {
        temp = voxel_seed_data->passive_scalar_temp = initial_condition_values.temperature();
      }
    }
    else {
      temp = simc.char_temp;
    }  

    if (simc.is_turb_model) {
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TURB_KINETIC_ENERGY])
        voxel_seed_data->turb_kinetic_energy = smart_seed_data->vars(DGF_SEED_VAR_TURB_KINETIC_ENERGY, voxel, controller_index);
      else
	voxel_seed_data->turb_kinetic_energy = initial_condition_values.turb_kinetic_energy();
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TURB_DISSIPATION])
        voxel_seed_data->turb_dissipation = smart_seed_data->vars(DGF_SEED_VAR_TURB_DISSIPATION, voxel, controller_index);
      else
	voxel_seed_data->turb_dissipation = initial_condition_values.turb_dissipation();
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_STRESS_TENSOR_MAG])
        voxel_seed_data->stress_ten_mag = smart_seed_data->vars(DGF_SEED_VAR_STRESS_TENSOR_MAG, voxel, controller_index);
      else
	voxel_seed_data->stress_ten_mag = 0;
    }

    if (!IS_FIXED_TEMP_LATTICE) {
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_DENSITY]) {
        dFLOAT density = smart_seed_data->vars(DGF_SEED_VAR_DENSITY, voxel, controller_index);
	voxel_seed_data->density = density;
	voxel_seed_data->lb_temp = pressure / (simc.lattice_gas_const * density);
      } 
      else {
	voxel_seed_data->density = pressure / (simc.lattice_gas_const * temp);
	voxel_seed_data->lb_temp = temp;
      }
    } 
    else {
      voxel_seed_data->density = pressure / (simc.lattice_gas_const * (simc.thermal_feedback_is_on? temp : g_lb_temp_constant));
      voxel_seed_data->lb_temp = g_lb_temp_constant;
    }
#if BUILD_D19_LATTICE
  if (simc.is_pf_model)
    voxel_seed_data->density_pfld = voxel_seed_data->density;
#endif
    voxel_seed_data->density *= g_density_scale_factor;

#if BUILD_D19_LATTICE
    if (simc.local_vel_freeze) {
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER])
        voxel_seed_data->coarsen_region_identifier = smart_seed_data->vars(DGF_SEED_VAR_DYNAMIC_SCALAR_MULTIPLIER, voxel, controller_index);
      else
	voxel_seed_data->coarsen_region_identifier = -1.0;

      if (ublk->is_near_surface()) {
	if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_USTAR])
          voxel_seed_data->ustar_lb = smart_seed_data->vars(DGF_SEED_VAR_USTAR, voxel, controller_index);
	else
	  voxel_seed_data->ustar_lb = 0.0;

	if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_DENSITY_FROZEN])
          voxel_seed_data->density_frozen = smart_seed_data->vars(DGF_SEED_VAR_DENSITY_FROZEN, voxel, controller_index) * g_density_scale_factor;
	else
	  voxel_seed_data->density_frozen = 0.0;

	if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_COMP0_DENSITY])    //borrow for xvel_frozen
          voxel_seed_data->vel_frozen[0] = smart_seed_data->vars(DGF_SEED_VAR_COMP0_DENSITY, voxel, controller_index);
	else
	  voxel_seed_data->vel_frozen[0] = 0.0;

	if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_COMP1_DENSITY])  //borrow for yvel frozen
          voxel_seed_data->vel_frozen[1] = smart_seed_data->vars(DGF_SEED_VAR_COMP1_DENSITY, voxel, controller_index);
	else
	  voxel_seed_data->vel_frozen[1] = 0.0;

	if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_CONTACT_ANGLE]) //borrow for zvel frozen
          voxel_seed_data->vel_frozen[2] = smart_seed_data->vars(DGF_SEED_VAR_CONTACT_ANGLE, voxel, controller_index);
	else
	  voxel_seed_data->vel_frozen[2] = 0.0;
      }
    } 
#endif  
  }
#endif
  //5G LB_UDS cannot seed uds_values until CASE support for it is available  
  if (simc.is_scalar_model) {
 #if !BUILD_5G_LATTICE       
    if (simc.uds_solver_type == LB_UDS) {
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
	if (seed_var_spec->is_uds_var_seeded[nth_uds][(int)DGF_SEED_UDS_VAR_SCALAR_VALUE]) 
	  voxel_seed_data->uds_value[nth_uds] = ublk->smart_seed_uds_data(nth_uds)->vars(DGF_SEED_UDS_VAR_SCALAR_VALUE, voxel);
	else
	  voxel_seed_data->uds_value[nth_uds] = initial_condition_values.uds_value(nth_uds);
      }
    } else { //PDE_UDS
      if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_WATER_VAPOR_MFRAC]) {
	voxel_seed_data->water_vapor_mass_fraction = smart_seed_data->vars(DGF_SEED_VAR_WATER_VAPOR_MFRAC, voxel, controller_index);
	// Force relative humidity to -1 so that in physics we always seed water_vapor_mass_fraction directly
	voxel_seed_data->relative_humidity = -1;
      } else {	 
	voxel_seed_data->water_vapor_mass_fraction = initial_condition_values.water_mass_fraction();
      }
    }
#endif
  }
}     
     

template
VOID convert_smart_seed_vars_to_seed_data<sUBLK>(sUBLK* ublk,
                                                 const asINT32 voxel, 
                                                 const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_condition_values,
                                                 VOXEL_LES_SEED_DATA voxel_seed_data, 
                                                 const uINT8 seed_var_spec_index,
                                                 LRF_PHYSICS_DESCRIPTOR lrf);

#if BUILD_GPU
template
VOID convert_smart_seed_vars_to_seed_data<sMBLK>(sMBLK* ublk,
                                                    const asINT32 voxel, 
                                                    const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_condition_values,
                                                    VOXEL_LES_SEED_DATA voxel_seed_data, 
                                                    const uINT8 seed_var_spec_index,
                                                    LRF_PHYSICS_DESCRIPTOR lrf);
#endif

VOID convert_smart_seed_vars_to_seed_data(const UBLK ublk,
                                          const asINT32 voxel, 
                                          const CONDUCTION_INITIAL_CONDITIONS initial_conditions,
                                          VOXEL_CONDUCTION_SEED_DATA voxel_seed_data, 
                                          const uINT8 seed_var_spec_index,
                                          LRF_PHYSICS_DESCRIPTOR lrf)
{
  UBLK_SMART_SEED_DATA smart_seed_data = ublk->smart_seed_data();
  FLUID_SEED_VAR_SPEC seed_var_spec = &g_fluid_seed_var_specs[seed_var_spec_index];

  sINT32 controller_index = seed_var_spec->seed_controller;
  //This updates the temp entry in smart_seed_data->vars
  if (sim.m_seed_control[controller_index].seed_via_dimless_properties)
    scale_seed_vars_via_dimless_properties(smart_seed_data, voxel, seed_var_spec, initial_conditions);
  else if (sim.m_seed_control[controller_index].seed_via_mks)
    scale_seed_vars_via_mks(smart_seed_data, voxel, seed_var_spec, initial_conditions);

  if (!sim.is_heat_transfer)
    msg_error("Entered conduction seeding for non-HT case");
  else {
    if (seed_var_spec->is_var_seeded[DGF_SEED_VAR_TEMP])
      voxel_seed_data->passive_scalar_temp = smart_seed_data->vars(DGF_SEED_VAR_TEMP, voxel, controller_index);
    else
      voxel_seed_data->passive_scalar_temp = initial_conditions->temperature.value;
  }

  // TODO, CHECK: Overwrite the density to be always char_density
  voxel_seed_data->density = sim.char_density;
}

/*** Seeding all ublks */

void cSHOBS_SEEDER::seed_all_dynblks()
{
  // For smart-seed, seed vars have been deposited at the head of each
  // ublk. It must be converted to states.
  DO_SCALES_FINE_TO_COARSE(scale) {
    if (g_timescale.m_timestep > 0) {
      TIMESTEP seed_timestep = g_timescale.m_timestep + (1 << (sim.num_scales - 1 - scale));
      m_seed_prior_solver_index_masks[scale] = compute_seed_solver_index_mask(scale, seed_timestep);
    }
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      farblk->seed(sim.do_smart_seed, m_seed_prior_solver_index_masks[scale]);
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      nearblk->seed(sim.do_smart_seed, m_seed_prior_solver_index_masks[scale]);
    }
  }
}

#if BUILD_5G_LATTICE
VOID construct_adv_states_for_all_dynblks_in_seed(asINT32 time_index) {
  DO_SCALES_FINE_TO_COARSE(scale) {
    if (sim.is_scalar_model) {
      DO_FARBLKS_OF_SCALE(farblk, scale) {      
	ublk_adv_states<TRUE>(farblk, time_index);      
      }
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {     
	ublk_adv_states<TRUE>(nearblk, time_index);     
      }
    } else {
      DO_FARBLKS_OF_SCALE(farblk, scale) {      
	ublk_adv_states<FALSE>(farblk, time_index);      
      }
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {     
	ublk_adv_states<FALSE>(nearblk, time_index);     
      }
    }
  }
}



VOID set_dynblk_states_clear_for_full_ckpt_restore() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      farblk->set_states_clear();
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      nearblk->set_states_clear();
    }
  }
}

VOID compute_gradient_porosity_for_all_dynblks() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      ublk_grad_porosity(farblk);
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      ublk_grad_porosity(nearblk);
    }
  }
}
#endif

#if 0
static VOID print_surfel_seed_states(char *print_string, sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("%s time %ld S %d\n", print_string, g_timescale.m_time, surfel->id());
  printf("density %14.9e momentum %14.9e %14.9e %14.9e cfn %14.9e \n",
         float(v2s_lb_data->m_density),
         float(v2s_lb_data->m_momentum[0]),
         float(v2s_lb_data->m_momentum[1]),
         float(v2s_lb_data->m_momentum[2]),
         float(v2s_lb_data->m_cf_n));
  printf("%14.9e %14.9e %14.9e %14.9e %14.9e %14.9e\n",
         float(v2s_lb_data->m_u_bar[0][0]),
         float(v2s_lb_data->m_u_bar[0][1]),
         float(v2s_lb_data->m_u_bar[0][2]),
         v2s_lb_data->m_grads.gradp[0],
         v2s_lb_data->m_grads.gradp[1],
         v2s_lb_data->m_grads.gradp[2]);
  printf("%e %e %e %e %e %e \n%e %e %e\n",
	 float(v2s_lb_data->m_in_states[0]), float(v2s_lb_data->m_in_states[1]),
	 float(v2s_lb_data->m_in_states[2]), float(v2s_lb_data->m_in_states[3]),
	 float(v2s_lb_data->m_in_states[4]), float(v2s_lb_data->m_in_states[5]),
	 float(v2s_lb_data->m_in_states[6]), float(v2s_lb_data->m_in_states[7]),
	 float(v2s_lb_data->m_in_states[8]));
}
static VOID print_surfel_seed_states_t(char *print_string, sSURFEL_V2S_T_DATA *v2s_t_data, SURFEL surfel) {

  printf("%s time %ld S %d\n", print_string, g_timescale.m_time, surfel->id());
  printf("%e %e %e %e %e %e \n%e %e %e\n",
	 float(v2s_t_data->m_in_states_t[0]), float(v2s_t_data->m_in_states_t[1]),
	 float(v2s_t_data->m_in_states_t[2]), float(v2s_t_data->m_in_states_t[3]),
	 float(v2s_t_data->m_in_states_t[4]), float(v2s_t_data->m_in_states_t[5]),
	 float(v2s_t_data->m_in_states_t[6]), float(v2s_t_data->m_in_states_t[7]),
	 float(v2s_t_data->m_in_states_t[8]));
}

static VOID print_surfel_seed_flux(sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("T %ld S %d OUT %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
               v2s_lb_data->m_out_flux[0], v2s_lb_data->m_out_flux[1],
               v2s_lb_data->m_out_flux[2], v2s_lb_data->m_out_flux[3],
               v2s_lb_data->m_out_flux[4], v2s_lb_data->m_out_flux[5],
               v2s_lb_data->m_out_flux[6], v2s_lb_data->m_out_flux[7],
               v2s_lb_data->m_out_flux[8]);
}

static VOID print_surfel_seed_flux_t(sSURFEL_V2S_T_DATA *v2s_t_data, SURFEL surfel) {
  printf("T %ld S %d OUT %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
               v2s_t_data->m_out_flux_t[0], v2s_t_data->m_out_flux_t[1],
               v2s_t_data->m_out_flux_t[2], v2s_t_data->m_out_flux_t[3],
               v2s_t_data->m_out_flux_t[4], v2s_t_data->m_out_flux_t[5],
               v2s_t_data->m_out_flux_t[6], v2s_t_data->m_out_flux_t[7],
               v2s_t_data->m_out_flux_t[8]);
}
#else
static VOID print_surfel_seed_states(char *print_string, sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("%s time %ld S %d\n", print_string, g_timescale.m_time, surfel->id());
  printf("density %12.3f momentum %12.3f %12.3f %12.3f cfn %12.3f \n",
         float(v2s_lb_data->m_density),
         float(v2s_lb_data->m_momentum[0]),
         float(v2s_lb_data->m_momentum[1]),
         float(v2s_lb_data->m_momentum[2]),
         float(v2s_lb_data->m_cf_n));
#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
  printf("%12.3f %12.3f %12.3f %12.3f %12.3f %12.3f\n",
         float(v2s_lb_data->m_u_bar[0]),
         float(v2s_lb_data->m_u_bar[1]),
         float(v2s_lb_data->m_u_bar[2]),
         float(v2s_lb_data->m_grads.gradp[0]),
         float(v2s_lb_data->m_grads.gradp[1]),
         float(v2s_lb_data->m_grads.gradp[2]));
#else
  printf("%12.3f %12.3f %12.3f %12.3f %12.3f %12.3f\n",
         float(v2s_lb_data->m_u_bar[0][0]),
         float(v2s_lb_data->m_u_bar[0][1]),
         float(v2s_lb_data->m_u_bar[0][2]),
         v2s_lb_data->m_grads.gradp[0],
         v2s_lb_data->m_grads.gradp[1],
         v2s_lb_data->m_grads.gradp[2]);
#endif
  printf("%12.3f %12.3f %12.3f %12.3f %12.3f %12.3f \n%12.3f %12.3f %12.3f\n",
	 float(v2s_lb_data->m_in_states[0]), float(v2s_lb_data->m_in_states[1]),
	 float(v2s_lb_data->m_in_states[2]), float(v2s_lb_data->m_in_states[3]),
	 float(v2s_lb_data->m_in_states[4]), float(v2s_lb_data->m_in_states[5]),
	 float(v2s_lb_data->m_in_states[6]), float(v2s_lb_data->m_in_states[7]),
	 float(v2s_lb_data->m_in_states[8]));
}
static VOID print_surfel_seed_states_t(char *print_string, sSURFEL_V2S_T_DATA *v2s_t_data, SURFEL surfel) {

  printf("%s time %ld S %d\n", print_string, g_timescale.m_time, surfel->id());
  printf("%12.3f %12.3f %12.3f %12.3f %12.3f %12.3f \n%12.3f %12.3f %12.3f\n",
	 float(v2s_t_data->m_in_states_t[0]), float(v2s_t_data->m_in_states_t[1]),
	 float(v2s_t_data->m_in_states_t[2]), float(v2s_t_data->m_in_states_t[3]),
	 float(v2s_t_data->m_in_states_t[4]), float(v2s_t_data->m_in_states_t[5]),
	 float(v2s_t_data->m_in_states_t[6]), float(v2s_t_data->m_in_states_t[7]),
	 float(v2s_t_data->m_in_states_t[8]));
}

static VOID print_surfel_seed_flux(sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel) {
  printf("T %ld S %d OUT %12.3f %12.3f %12.3f %12.3f \n%12.3f %12.3f %12.3f %12.3f %12.3f\n", g_timescale.m_time, surfel->id(),
	 float(v2s_lb_data->m_out_flux[0]), float(v2s_lb_data->m_out_flux[1]),
	 float(v2s_lb_data->m_out_flux[2]), float(v2s_lb_data->m_out_flux[3]),
	 float(v2s_lb_data->m_out_flux[4]), float(v2s_lb_data->m_out_flux[5]),
	 float(v2s_lb_data->m_out_flux[6]), float(v2s_lb_data->m_out_flux[7]),
	 float(v2s_lb_data->m_out_flux[8]));
}

static VOID print_surfel_seed_flux_t(sSURFEL_V2S_T_DATA *v2s_t_data, SURFEL surfel) {
  printf("T %ld S %d OUT %12.3f %12.3f %12.3f %12.3f \n%12.3f %12.3f %12.3f %12.3f %12.3f\n", g_timescale.m_time, surfel->id(),
	 float(v2s_t_data->m_out_flux_t[0]), float(v2s_t_data->m_out_flux_t[1]),
	 float(v2s_t_data->m_out_flux_t[2]), float(v2s_t_data->m_out_flux_t[3]),
	 float(v2s_t_data->m_out_flux_t[4]), float(v2s_t_data->m_out_flux_t[5]),
	 float(v2s_t_data->m_out_flux_t[6]), float(v2s_t_data->m_out_flux_t[7]),
	 float(v2s_t_data->m_out_flux_t[8]));
}
#endif

static BOOLEAN is_surfel_high_mach(SURFEL surfel) {
  BOOLEAN  is_high_mach = FALSE;
#if BUILD_D39_LATTICE
  asINT32 ref_frame_index = surfel->ref_frame_index();
  LRF_PHYSICS_DESCRIPTOR lrf = ref_frame_index >= 0 ? &sim.lrf_physics_descs[ref_frame_index] : NULL;
  if (sim.use_hybrid_ts_hs_solver)
    is_high_mach = (lrf != NULL && lrf->has_transonic_flow);
  else
  is_high_mach = TRUE;
#endif
  return is_high_mach;
}

/*** Seeding all surfels */
template<eDYN_SURFEL_TYPE E>
struct tSURFEL_TYPE_TRAITS_SEED : public tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG> {
  __HOST__DEVICE__
  static VOID exec(sSURFEL_DYNAMICS_DATA *dynamics_data,
                   SURFEL surfel,
                   sSURFEL_V2S_DATA *surfel_v2s_data,
                   BOOLEAN is_full_checkpoint_restore) {

#if !GPU_COMPILER    
    auto derived_dynamics_data = tSURFEL_DYN_TYPE_TRAITS_BASE<E, SFL_SDFLOAT_TYPE_TAG>::cast_to_derived_dynamics_type(dynamics_data);    
    derived_dynamics_data->seed(surfel, surfel_v2s_data, is_full_checkpoint_restore);
#endif    
  }
};

template<typename SFL_TYPE_TAG>
__HOST__DEVICE__ VOID copy_surfel_outflux_for_seed(tSURFEL<SFL_TYPE_TAG>* surfel,
                                                   tSURFEL_V2S_DATA<SFL_TYPE_TAG>* surfel_v2s_data)
{
  auto v2s_lb_data = surfel->has_v2s_data()? surfel->v2s_lb_data() : surfel_v2s_data->v2s_lb_data();
  asINT32 soxor = get_dyn_soxor();
  auto& simc = get_simc_ref();
  ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
    surfel->curr_outflux(0)[latvec_pair][soxor] = v2s_lb_data->m_out_flux[latvec_pair][soxor];
  }
  constexpr asINT32 TIME_INDEX_0 = 0;
  surfel->curr_ustar_0()[TIME_INDEX_0][soxor] = surfel->lb_data()->ustar_0[soxor];
  if (simc.is_T_S_solver_type_lb()) {
    auto v2s_t_data = surfel->has_v2s_data()? surfel->v2s_t_data() : surfel_v2s_data->v2s_t_data();
    ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
      surfel->curr_outflux_t(TIME_INDEX_0)[latvec_pair][soxor] = v2s_t_data->m_out_flux_t[latvec_pair][soxor];
    }
  }
  if (simc.uds_solver_type == LB_UDS) {
    ccDOTIMES(nth_uds,simc.n_user_defined_scalars){
      auto v2s_uds_data = surfel->has_v2s_data()? surfel->v2s_uds_data(nth_uds) : surfel_v2s_data->v2s_uds_data(nth_uds);
      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
	surfel->curr_outflux_uds(TIME_INDEX_0, nth_uds)[latvec_pair][soxor] = v2s_uds_data->m_out_flux_uds[latvec_pair][soxor];
      }
    }
  }
}


VOID seed_mlrf_surfels(SOLVER_INDEX_MASK seed_prior_solver_index_mask,
                       STP_SCALE scale,
                       BOOLEAN is_full_checkpoint_restore) {
  
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group0, scale) {
    mlrf_group0->pre_seed_init(is_full_checkpoint_restore, seed_prior_solver_index_mask);
  }

  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group1, scale) {
    mlrf_group1->post_pre_dynamics_receives();
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group2, scale) {
    mlrf_group2->copy_to_pre_dynamics_send_buffers(sim.init_solver_mask);
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group3, scale) {
    mlrf_group3->post_pre_dynamics_sends(TRUE);
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group4, scale) {
    while(!mlrf_group4->complete_receives(MLRF_PREDYN))
      sp_thread_sleep(MPI_SLEEP_SHORT);
  }
  if (!is_full_checkpoint_restore) {
    sSURFEL_PROCESS_CONTROL surfel_process_control;
    surfel_process_control.init(scale, g_timescale.time_parity());
    surfel_process_control.m_active_solver_mask = sim.init_solver_mask;
    surfel_process_control.m_even_active_solver_mask = sim.init_solver_mask;
    surfel_process_control.m_odd_active_solver_mask = sim.init_solver_mask;
    DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group5, scale) {
      if (mlrf_group5->m_realm == STP_FLOW_REALM) {
        mlrf_group5->dynamics_flow(&surfel_process_control, 0, 1);
      } else { //mlrf_group5->m_realm == STP_COND_REALM
        mlrf_group5->dynamics_conduction(&surfel_process_control, 1);
      }
    }
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group6, scale) {
    mlrf_group6->post_post_dynamics_receives();
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group7, scale) {
    mlrf_group7->post_post_dynamics_sends(TRUE);
  }
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group7, scale) {
    while(!mlrf_group7->complete_receives(MLRF_POSTDYN)) {
      sp_thread_sleep(MPI_SLEEP_SHORT);
    }
  }
  if (!is_full_checkpoint_restore) {
    sSURFEL_PROCESS_CONTROL surfel_process_control;
    surfel_process_control.init(scale, g_timescale.time_parity());
    surfel_process_control.m_active_solver_mask = sim.init_solver_mask;
    surfel_process_control.m_even_active_solver_mask = sim.init_solver_mask;
    surfel_process_control.m_odd_active_solver_mask = sim.init_solver_mask;
    DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group8, scale) {
      if (mlrf_group8->m_realm == STP_FLOW_REALM) {
        mlrf_group8->surfel_dyn_delta_mass(&surfel_process_control, 0, 1);
      }
    }
  }
}

#if BUILD_GPU
template VOID copy_surfel_outflux_for_seed(tSURFEL<MSFL_SDFLOAT_TYPE_TAG>* surfel,
                                           tSURFEL_V2S_DATA<MSFL_SDFLOAT_TYPE_TAG>* surfel_v2s_data);
#endif

static INLINE VOID seed_wsurfel_and_contact_surfel_A(BOOLEAN is_full_checkpoint_restore, 
                                                     SURFEL surfel, 
                                                     SURFEL_V2S_DATA surfel_v2s_data,
                                                     sCONTACT_ACCUMULATION* averaging_accumulation,
                                                     SOLVER_INDEX_MASK seed_prior_solver_index_mask,
                                                     const STP_GEOM_VARIABLE group_voxel_size) {
#if !BUILD_5G_LATTICE
  memset(surfel_v2s_data, 0, sizeof(sSURFEL_V2S_DATA));
  BOOLEAN is_flow_wsurfel = !surfel->is_conduction_surfel();
  if (!is_full_checkpoint_restore) {
    if (is_flow_wsurfel && surfel->has_v2s_data()) {
      surfel->v2s_lb_data()->reset();
    }
    if (sim.init_solver_mask & KE_PDE_ACTIVE) {
      surfel->turb_data()->s_mag = 0.0;
      surfel->turb_data()->gamma_swirl = 0.0;
    }
    if (sim.init_solver_mask & T_PDE_ACTIVE) {
      surfel->t_data()->temp_sample = 0;
    }
  
    surfel_v2s_advect_for_seed(surfel, sim.init_solver_mask, surfel_v2s_data, seed_prior_solver_index_mask);
    
    if (surfel->has_mirror()) {
      SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
      while (mirror_data) {
        SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
        SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
        STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
        LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
        auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
        mirror_surfel_v2s_data.clear();
        if (surfel->is_conduction_surface()) 
          mirror_surfel->conduction_data()->clear_sampled_quantities();
        surfel_v2s_advect_for_seed(mirror_surfel, sim.init_solver_mask,
                                   &mirror_surfel_v2s_data,
                                   seed_prior_solver_index_mask);
        if (is_flow_wsurfel && (sim.init_solver_mask & LB_ACTIVE)) {
          sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
          sSURFEL_V2S_LB_DATA *real_v2s_lb_data = (surfel->has_v2s_data()) 
                                                  ? surfel->v2s_lb_data()
                                                  : surfel_v2s_data->v2s_lb_data();
          real_v2s_lb_data->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                   mirror_config->m_reflected_latvec_pair);
        }
        if (sim.init_solver_mask & KE_PDE_ACTIVE) {
          sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
          sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = (surfel->has_v2s_data()) 
                                                      ? surfel->v2s_turb_data()
                                                      : surfel_v2s_data->v2s_turb_data();
          real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
        }
        if (sim.init_solver_mask & T_PDE_ACTIVE) {
          sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
          sSURFEL_V2S_T_DATA *real_v2s_t_data = (surfel->has_v2s_data()) 
                                                ? surfel->v2s_t_data()
                                                : surfel_v2s_data->v2s_t_data();
          real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                  mirror_config->m_reflected_latvec_pair,
                                                  sim.is_T_S_solver_type_lb());
        }
        if (is_flow_wsurfel)
          surfel->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                          mirror_config->m_reflected_latvec_pair,
                                                          mirror_config->m_velocity_mirror_sign_factor,
                                                          sim.init_solver_mask);
        else if (surfel->is_conduction_surface()) 
          surfel->conduction_reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                          mirror_config->m_reflected_latvec_pair,
                                                          mirror_config->m_velocity_mirror_sign_factor,
                                                          sim.init_solver_mask);
        mirror_data = mirror_data->m_next_mirror_data;
      }
    }
  }

  //BOOLEAN  is_high_mach = is_surfel_high_mach(surfel); //wsurfels do not handle high mach conditions (for now)
  pre_seed_init_surfel(surfel, is_full_checkpoint_restore, FALSE, surfel_v2s_data, TRUE);
  if (surfel->has_mirror()) {
    SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      // Only scale up states uses surfel_v2s_data, which is not done for mirror surfels
      pre_seed_init_surfel(mirror_surfel, is_full_checkpoint_restore, FALSE, nullptr, FALSE);
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }

  //Phase A only needs to seed the shells. However, at this points the shells have already been 
  //seeded and scaled and should not be seeded again since that will ruin the scaling.
  //DFG-TODO: for flow surfels, don't we need to seed them as well as flow_dyn_surfels?
  
  if (averaging_accumulation) {
    //During seeding we perform the area accumulation, since all groups are traversed always in the same order
    sCONTACT_ACCUMULATION::sAVERAGER_INDEX_AND_FACE avg = averaging_accumulation->averager_index_and_face();
    g_thermal_averaged_contacts[avg.idx].add_area(avg.is_primary, surfel->area);
  }
  
  if(!is_full_checkpoint_restore) {
    wsurfel_and_conduction_surfel_sampling_and_dynA(surfel, sim.init_solver_mask, surfel_v2s_data, 
                                                    averaging_accumulation, group_voxel_size, TRUE);
    //Cannot proceed with phase B until all wsurfels have completed phase A
  }
#endif
}

static INLINE VOID seed_wsurfel_and_contact_surfel_B(BOOLEAN is_full_checkpoint_restore, 
                                                     SURFEL surfel, 
                                                     CONTACT_SURFEL_TYPE contact_surfel_type,
                                                     SURFEL_V2S_DATA surfel_v2s_data,
                                                     const STP_GEOM_VARIABLE group_voxel_size,
                                                     const STP_GEOM_VARIABLE meas_scale_factor) {
#if !BUILD_5G_LATTICE
  //Once all wsurfels have completed phase A, phase B can be carried on to complete the seeding
  if(!is_full_checkpoint_restore) {
    wsurfel_and_contact_surfels_hfc_and_dynB(surfel, contact_surfel_type, sim.init_solver_mask, sim.init_solver_mask, 
                                             surfel_v2s_data, group_voxel_size, meas_scale_factor, TRUE);
    
    // VINIT The following comment does not make sense anymore
    // Mirror surfels are checkpointed, so we should not overwrite the
    // restored states. If not restoring from a full checkpoint, their data is
    // initialized from their real counterparts after they have been seeded.
    if (surfel->has_mirror()) {
      SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
      while (mirror_data) {
        SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
        // Mirror surfels cannot be sources in S2S
        // If at some point ghosted mirror surfels need two copies of outflux, check code for flow dyn surfels 
        // to handle them correctly
        if (mirror_surfel->has_two_copies_of_outflux()) {
          msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
        }
        SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
        STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
        // There is no point to setting a local copy of sSURFEL_TEMP_DATA
        // It should only be done for mirror surfels that participate in S2S
        if (!surfel->is_conduction_surfel())
          mirror_surfel->reflect_from_real_surfel_to_mirror_surfel(surfel, latvec_mask,
                                                                 mirror_config->m_reflected_latvec_pair,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 sim.init_solver_mask);
        else if (mirror_surfel->is_conduction_surface()) 
          mirror_surfel->conduction_reflect_from_real_surfel_to_mirror_surfel(surfel, latvec_mask,
                                                                 mirror_config->m_reflected_latvec_pair,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 sim.init_solver_mask);
        mirror_data = mirror_data->m_next_mirror_data;
      }
    }
    
    if (surfel->has_two_copies_of_outflux()) {
      if (!surfel->is_conduction_surfel()) {
        sSURFEL_V2S_LB_DATA *v2s_lb_data = (surfel->has_v2s_data())
                                          ? surfel->v2s_lb_data()
                                          : surfel_v2s_data->v2s_lb_data();
        ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
          surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
        }
        surfel->curr_ustar_0()[0] = surfel->lb_data()->ustar_0;
      }
      if (sim.is_T_S_solver_type_lb()) {
        sSURFEL_V2S_T_DATA *v2s_t_data = (surfel->has_v2s_data())
                                          ? surfel->v2s_t_data()
                                          : surfel_v2s_data->v2s_t_data();      
        ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
          surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
        }
      }
    }
  }
#endif
}

void cSHOBS_SEEDER::do_seed_all_surfels(BOOLEAN is_full_checkpoint_restore) {

  //Enable/disable averaged contacts, if present, based on if it is a full checkpoint restore
  if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
    g_thermal_averaged_contacts.init_count(is_full_checkpoint_restore);
  }

  DO_SCALES_FINE_TO_COARSE(scale) { /* seed */
    LOCAL_SFL_V2S_DATA local_v2s_data;
    auto& surfel_v2s_data = local_v2s_data.v2s_data();
    STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
    STP_GEOM_VARIABLE meas_scale_factor = (STP_GEOM_VARIABLE)
                      ((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1)));
    {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        surfel_v2s_data.clear();
        if(!is_full_checkpoint_restore && surfel->has_v2s_data())
          surfel->v2s_lb_data()->reset();
// XDU: do not reset these since the restored values are used for even/odd surfels        
//      Question: should we skip some other operations here for full ckpt restore, or even the whole function?
        if (!is_full_checkpoint_restore) {
          if (sim.init_solver_mask & KE_PDE_ACTIVE) {
            surfel->turb_data()->s_mag = 0.0;
            surfel->turb_data()->gamma_swirl = 0.0;
          }

          if (sim.init_solver_mask & T_PDE_ACTIVE) {
            surfel->t_data()->temp_sample = 0;
          }
        }
        if (!is_full_checkpoint_restore) {
          surfel_v2s_advect_for_seed(surfel, sim.init_solver_mask, &surfel_v2s_data,
                                     m_seed_prior_solver_index_masks[scale]);
          if (surfel->has_mirror()) {
            BOOLEAN is_high_mach = is_surfel_high_mach(surfel);
            SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
              STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
              surfel_v2s_advect_for_seed(mirror_surfel, sim.init_solver_mask,
                                         &mirror_surfel_v2s_data,
                                         m_seed_prior_solver_index_masks[scale]);
              if (surfel->has_v2s_data()) {
                if (sim.init_solver_mask & LB_ACTIVE) {
                  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
                  surfel->v2s_lb_data()->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
                                                                mirror_config->m_velocity_mirror_sign_factor,
                                                                mirror_config->m_reflected_latvec_pair);
                }
                if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
                  surfel->v2s_turb_data()->reflect_to_real_surfel(mirror_v2s_turb_data);
                }
                if (sim.init_solver_mask & T_PDE_ACTIVE) {
                  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                  surfel->v2s_t_data()->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                                          mirror_config->m_velocity_mirror_sign_factor,
                                                          mirror_config->m_reflected_latvec_pair,
                                                          sim.is_T_S_solver_type_lb());
                }
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
	          ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    surfel->v2s_uds_data(nth_uds)->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
							    mirror_config->m_velocity_mirror_sign_factor,
							    mirror_config->m_reflected_latvec_pair,
							    (sim.uds_solver_type == LB_UDS));
                  }
#if BUILD_D19_LATTICE
		  if(sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    surfel->v2s_pf_data()->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
              } else {
                if (sim.init_solver_mask & LB_ACTIVE) {
                  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
                  sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_v2s_data.v2s_lb_data();
                  real_v2s_lb_data->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
                                                                      mirror_config->m_velocity_mirror_sign_factor,
                                                                      mirror_config->m_reflected_latvec_pair);
                }
                if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
                  sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_v2s_data.v2s_turb_data();
                  real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
                }
                if (sim.init_solver_mask & T_PDE_ACTIVE) {
                  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                  sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_v2s_data.v2s_t_data();
                  real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                                          mirror_config->m_velocity_mirror_sign_factor,
                                                          mirror_config->m_reflected_latvec_pair,
                                                          sim.is_T_S_solver_type_lb());
                }
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
	          ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_v2s_data.v2s_uds_data(nth_uds);
		    real_v2s_uds_data->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
							    mirror_config->m_velocity_mirror_sign_factor,
							    mirror_config->m_reflected_latvec_pair,
							    (sim.uds_solver_type == LB_UDS));
                  }
#if BUILD_D19_LATTICE
		  if (sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_v2s_data.v2s_pf_data();
                    real_v2s_pf_data->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                            mirror_config->m_velocity_mirror_sign_factor,
                                                            mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
              }
              surfel->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                                mirror_config->m_reflected_latvec_pair,
                                                                mirror_config->m_velocity_mirror_sign_factor,
                                                                sim.init_solver_mask);
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
        BOOLEAN  is_high_mach = is_surfel_high_mach(surfel);
        pre_seed_init_surfel(surfel, is_full_checkpoint_restore, is_high_mach, &surfel_v2s_data, TRUE);
        if (surfel->has_mirror()) {
          SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            // Only scale up states uses surfel_v2s_data, which is not done
            // for mirror surfels
            pre_seed_init_surfel(mirror_surfel, is_full_checkpoint_restore, is_high_mach, nullptr, FALSE);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }

        sSURFEL_DYNAMICS_DATA *dynamics_data = surfel->dynamics_data();
        STP_GEOM_VARIABLE voxel_size = sim_scale_to_voxel_size(surfel->scale());

        exec_by_surfel_dynamics_type<tSURFEL_TYPE_TRAITS_SEED>(dynamics_data, surfel,
                                                               &surfel_v2s_data,
                                                               is_full_checkpoint_restore);

        if (!is_full_checkpoint_restore) {
          // We suppress simerrs in the dynamics routine on the assumption that
          // the only source of errors is the evaluation of time dependent parameter
          // programs. By design, the same errors will be generated at t=0. If this
          // assumption is violated in the future (for example, if we introduce an
          // error based on the surfel's sampled velocity being out-of-range),
          // this simple approach of suppressing all errors during this call to
          // dynamics will no longer work.
          simerr_suppress_errors = TRUE;
          flow_surfel_dynamics(surfel, sim.init_solver_mask, sim.init_solver_mask, group_voxel_size,
                               meas_scale_factor, &surfel_v2s_data, TRUE);
          simerr_suppress_errors = FALSE;
        }

        // VINIT The following comment does not make sense anymore
        // Mirror surfels are checkpointed, so we should not overwrite the
        // restored states. If not restoring from a full checkpoint, their data is
        // initialized from their real counterparts after they have been seeded.
        if (!is_full_checkpoint_restore) {
          if (surfel->has_mirror()) {
            SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
              STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              // There is no point to setting a local copy of sSURFEL_TEMP_DATA
              // It should only be done for mirror surfels that participate in S2S
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
              if (mirror_surfel->has_two_copies_of_outflux()) {
                msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
                if (surfel->has_v2s_data()) {
                  if (sim.init_solver_mask & LB_ACTIVE) {
                    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel->v2s_lb_data();
                    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
                    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 mirror_config->m_reflected_latvec_pair);
                  }
                  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel->v2s_turb_data();
                    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
                    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
                  }
                  if (sim.init_solver_mask & T_PDE_ACTIVE) {
                    sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel->v2s_t_data();
                    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
                                                                mirror_config->m_velocity_mirror_sign_factor,
                                                                mirror_config->m_reflected_latvec_pair,
                                                                sim.is_T_S_solver_type_lb());
                  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
                    ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel->v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
								  (sim.uds_solver_type == LB_UDS));
                    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel->v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                                  mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }
  
                } else {
                  if (sim.init_solver_mask & LB_ACTIVE) {
                    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_v2s_data.v2s_lb_data();
                    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
                    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
                                                                      mirror_config->m_velocity_mirror_sign_factor,
                                                                      mirror_config->m_reflected_latvec_pair);
                  }
                  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_v2s_data.v2s_turb_data();
                    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
                    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
                  }
                  if (sim.init_solver_mask & T_PDE_ACTIVE) {
                    sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_v2s_data.v2s_t_data();
                    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
                                                                mirror_config->m_velocity_mirror_sign_factor,
                                                                mirror_config->m_reflected_latvec_pair,
                                                                sim.is_T_S_solver_type_lb());
                  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
                    ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_v2s_data.v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
		    						  (sim.uds_solver_type == LB_UDS));
                    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_v2s_data.v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                                   mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }
                }
              }
              mirror_surfel->reflect_from_real_surfel_to_mirror_surfel(surfel, latvec_mask,
                                                                       mirror_config->m_reflected_latvec_pair,
                                                                       mirror_config->m_velocity_mirror_sign_factor,
                                                                       sim.init_solver_mask);
              // Mirror surfels cannot be sources in S2S
              // If at some point ghosted mirror surfels need two copies of outflux, this code will
              // become active
              if (mirror_surfel->has_two_copies_of_outflux()) {
                msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
                if (mirror_surfel->is_even_or_odd() || mirror_surfel->has_v2s_data()) {
                  if (sim.init_solver_mask & LB_ACTIVE) {
                    *mirror_surfel->v2s_lb_data() = *mirror_surfel_v2s_data.v2s_lb_data();
                  }
                  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                    *mirror_surfel->v2s_turb_data() = *mirror_surfel_v2s_data.v2s_turb_data();
                  }
                  if (sim.init_solver_mask & T_PDE_ACTIVE) {
                    *mirror_surfel->v2s_t_data() = *mirror_surfel_v2s_data.v2s_t_data();
                  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
                    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
		      *mirror_surfel->v2s_uds_data(nth_uds) = *mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      *mirror_surfel->v2s_pf_data() = *mirror_surfel_v2s_data.v2s_pf_data();
                    }
#endif
                  }
                } else {
                  sSURFEL_V2S_LB_DATA *v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
                  ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                    mirror_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
                  }
                  mirror_surfel->curr_ustar_0()[0] = mirror_surfel->lb_data()->ustar_0;
                  if (sim.is_T_S_solver_type_lb()) {
                    sSURFEL_V2S_T_DATA *v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                    ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                      mirror_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
                    }
                  }
		  if (sim.uds_solver_type == LB_UDS) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
		      sSURFEL_V2S_UDS_DATA *v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
			mirror_surfel->curr_outflux_uds(0,nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		      }
		    }
                  }
                }
              }
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        } // !is_full_checkpoint_restore
        if (0 && (surfel->id() == 816)) {
          printf("T %ld S %d OUT %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[0]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[1]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[2]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[3]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[4]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[5]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[6]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[7]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[8]));
        }
        if (0 && (surfel->id() == 832)) {
          printf("T %ld S %d OUT %e %e %e %e \n%e %e %e %e %e\n", g_timescale.m_time, surfel->id(),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[0]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[1]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[2]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[3]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[4]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[5]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[6]),
		 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[7]),
                 float(surfel_v2s_data.v2s_lb_data()->m_out_flux[8]));
        }
        if (!is_full_checkpoint_restore) {
          if (surfel->has_two_copies_of_outflux()) {
            copy_surfel_outflux_for_seed(surfel, &surfel_v2s_data);
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		surfel->curr_outflux_mc(0)[latvec_pair] = surfel->mc_data()->out_flux[latvec_pair];
	      }
	    }
#endif
          }
        }
	if (sim.is_mme_checkpoint_restore)
	  surfel->init_outflux_copied();
      }
    }
    {
      DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
#if !BUILD_5G_LATTICE
        surfel_v2s_data.clear();
         if (!is_full_checkpoint_restore) {
          if (sim.init_solver_mask & KE_PDE_ACTIVE) {
            surfel->turb_data()->s_mag = 0.0;
            surfel->turb_data()->gamma_swirl = 0.0;
          }

          if (sim.init_solver_mask & T_PDE_ACTIVE) {
            surfel->t_data()->temp_sample = 0;
          }
          surfel_v2s_advect_for_seed(surfel, sim.init_solver_mask, &surfel_v2s_data,
                                     m_seed_prior_solver_index_masks[scale]);
          
          if (surfel->has_mirror()) {
            SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
              STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
              if (surfel->is_conduction_surface()) 
                mirror_surfel->conduction_data()->clear_sampled_quantities();
              surfel_v2s_advect_for_seed(mirror_surfel, sim.init_solver_mask,
                                         &mirror_surfel_v2s_data,
                                         m_seed_prior_solver_index_masks[scale]);
              if (sim.init_solver_mask & KE_PDE_ACTIVE) {
                sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
                sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = (surfel->has_v2s_data()) 
                                                            ? surfel->v2s_turb_data()
                                                            : surfel_v2s_data.v2s_turb_data();
                real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
              }
              if (sim.init_solver_mask & T_PDE_ACTIVE) {
                sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
                sSURFEL_V2S_T_DATA *real_v2s_t_data = (surfel->has_v2s_data()) 
                                                      ? surfel->v2s_t_data()
                                                      : surfel_v2s_data.v2s_t_data();
                real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
                                                        mirror_config->m_velocity_mirror_sign_factor,
                                                        mirror_config->m_reflected_latvec_pair,
                                                        sim.is_T_S_solver_type_lb());
              }
              if (surfel->is_conduction_surface()) { 
                surfel->conduction_reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
                                                                mirror_config->m_reflected_latvec_pair,
                                                                mirror_config->m_velocity_mirror_sign_factor,
                                                                sim.init_solver_mask);
              }
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
        
        pre_seed_init_surfel(surfel, is_full_checkpoint_restore, FALSE, &surfel_v2s_data, TRUE);
        if (surfel->has_mirror()) {
          SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            // Only scale up states uses surfel_v2s_data, which is not done for mirror surfels
            pre_seed_init_surfel(mirror_surfel, is_full_checkpoint_restore, FALSE, nullptr, FALSE);
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
        
        //Conduction surfels needs to seed the shells. However, at this points the shells have already been 
        //seeded and scaled and should not be seeded again since that will ruin the scaling.
        
        if (!is_full_checkpoint_restore) {
          conduction_surfel_dynamics(surfel, sim.init_solver_mask, sim.init_solver_mask, &surfel_v2s_data, 
                                     group_voxel_size, meas_scale_factor, TRUE);
          
          // VINIT The following comment does not make sense anymore
          // Mirror surfels are checkpointed, so we should not overwrite the
          // restored states. If not restoring from a full checkpoint, their data is
          // initialized from their real counterparts after they have been seeded.
          if (surfel->has_mirror()) {
            SURFEL_MIRROR_DATA mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              // Mirror surfels cannot be sources in S2S
              // If at some point ghosted mirror surfels need two copies of outflux, check code for flow dyn surfels 
              // to handle them correctly
              if (mirror_surfel->has_two_copies_of_outflux()) {
                msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
              }
              SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
              STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              // There is no point to setting a local copy of sSURFEL_TEMP_DATA
              // It should only be done for mirror surfels that participate in S2S
              if (mirror_surfel->is_conduction_surface()) {
                mirror_surfel->conduction_reflect_from_real_surfel_to_mirror_surfel(surfel, latvec_mask,
                                                                       mirror_config->m_reflected_latvec_pair,
                                                                       mirror_config->m_velocity_mirror_sign_factor,
                                                                       sim.init_solver_mask);
              }
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
          
          if (surfel->has_two_copies_of_outflux()) {
            if (sim.T_solver_type == LB_TEMPERATURE) {
              sSURFEL_V2S_T_DATA *v2s_t_data = (surfel->has_v2s_data())
                                               ? surfel->v2s_t_data()
                                               : surfel_v2s_data.v2s_t_data();
              ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
              }
            }
          }
        }
#endif
	if (sim.is_mme_checkpoint_restore)
	  surfel->init_outflux_copied();
      }
    }

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    //Wsurfels require data from both realms to compute fluxes, so seeding needs to distinguish 
    //between phases A and B of dynamics unlike regular dyn surfels
    //Contact surfels when dealing with non-conformal contact also require split in phases A and B,
    //so for dynamic purposes are treated like wsurfels
    { //Phase A
      DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
        seed_wsurfel_and_contact_surfel_A(is_full_checkpoint_restore, wsurfel_flow, &surfel_v2s_data, 
                                          nullptr, m_seed_prior_solver_index_masks[scale], group_voxel_size);
	if (sim.is_mme_checkpoint_restore)
	  wsurfel_flow->init_outflux_copied();
      }
      DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_cond, scale) {
        seed_wsurfel_and_contact_surfel_A(is_full_checkpoint_restore, wsurfel_cond, &surfel_v2s_data, 
                                          nullptr, m_seed_prior_solver_index_masks[scale], group_voxel_size);
	if (sim.is_mme_checkpoint_restore)
	  wsurfel_cond->init_outflux_copied();
      }
      DO_CONTACT_SURFELS_GROUPS_OF_SCALE(contact_surfel_group, scale) {
        contact_surfel_group->reset_next_averaged_contact_index();
        DO_CONTACT_SURFELS_OF_GROUP(contact_surfel, contact_surfel_group, contact_surfel_type) {
          sCONTACT_ACCUMULATION* averaging_accumulation = (contact_surfel_type == CONTACT_SURFEL_PRIMARY_AVERAGED ||
                                                           contact_surfel_type == CONTACT_SURFEL_SECONDARY_AVERAGED)
                                                          ? contact_surfel_group->next_accumulation() : nullptr;
          seed_wsurfel_and_contact_surfel_A(is_full_checkpoint_restore, contact_surfel, &surfel_v2s_data, 
                                            averaging_accumulation, m_seed_prior_solver_index_masks[scale], group_voxel_size);
	  if (sim.is_mme_checkpoint_restore)
	    contact_surfel->init_outflux_copied();
        }
      }
    }
    //Phase B of wsurfels & contact surfels is done later, once all scales are traversed, since contact surfels are part
    //of the wsurfel comm and can involve interaction between surfels in separate scales

    // TODO should combine the two loops into one
    {
      BOOLEAN is_lb_active = (sim.init_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
      BOOLEAN is_temp_active = (sim.init_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
      BOOLEAN is_turb_active = (sim.init_solver_mask & KE_PDE_ACTIVE) ? TRUE: FALSE;
      BOOLEAN is_uds_active = (sim.init_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
      dFLOAT one_over_delta_t = 1.0F / scale_to_delta_t(scale);
      DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {

        SURFEL int_surfel = slrf_surfel_pair->m_interior_surfel;
        SURFEL ext_surfel = slrf_surfel_pair->m_exterior_surfel;
        LOCAL_SFL_V2S_DATA_PAIR local_surfel_pair_temp_data;
        auto surfel_pair_temp_data = local_surfel_pair_temp_data.v2s_data_pair();
        surfel_pair_temp_data[0].clear();
        surfel_pair_temp_data[1].clear();
        if (!is_full_checkpoint_restore) {
          if (sim.init_solver_mask & KE_PDE_ACTIVE) {
            int_surfel->turb_data()->s_mag = 0.0;
            int_surfel->turb_data()->gamma_swirl = 0.0;
            ext_surfel->turb_data()->s_mag = 0.0;
            ext_surfel->turb_data()->gamma_swirl = 0.0;
          }
          // stuff in t_data should also be reset to 0
          if (sim.init_solver_mask & T_PDE_ACTIVE) {
            int_surfel->t_data()->temp_sample = 0.0;
            ext_surfel->t_data()->temp_sample = 0.0;
          }
        }
        BOOLEAN is_ext_high_mach = is_surfel_high_mach(ext_surfel);
        if (!is_full_checkpoint_restore)
          surfel_v2s_advect_for_seed(ext_surfel, sim.init_solver_mask, &surfel_pair_temp_data[EXT_INDEX],
                                     m_seed_prior_solver_index_masks[scale]);
        pre_seed_init_surfel(ext_surfel, is_full_checkpoint_restore, is_ext_high_mach, &surfel_pair_temp_data[EXT_INDEX], TRUE);

        BOOLEAN is_int_high_mach = is_surfel_high_mach(int_surfel);
        if (!is_full_checkpoint_restore)
          surfel_v2s_advect_for_seed(int_surfel, sim.init_solver_mask, &surfel_pair_temp_data[INT_INDEX],
                                     m_seed_prior_solver_index_masks[scale]);
        pre_seed_init_surfel(int_surfel, is_full_checkpoint_restore, is_int_high_mach, &surfel_pair_temp_data[INT_INDEX], TRUE);

        asINT32 ref_frame_index = int_surfel->ref_frame_index();
        LRF_PHYSICS_DESCRIPTOR lrf = ref_frame_index >= 0 ? &sim.lrf_physics_descs[ref_frame_index] : NULL;
        // seeding only does dynamics for slrf surfel pairs
        if (!is_full_checkpoint_restore) {
          if (slrf_surfel_pair->m_interior_surfel->is_conduction_surface()) {
            slrf_surfel_dynamics_conduction(slrf_surfel_pair, lrf, scale, sim.init_solver_mask, TRUE);
          } else {
            slrf_surfel_dynamics_flow(slrf_surfel_pair, lrf, scale, one_over_delta_t,
                                 sim.init_solver_mask, surfel_pair_temp_data, 0);
          }

          if (ext_surfel->has_two_copies_of_outflux()) {
            sSURFEL_V2S_LB_DATA *v2s_lb_data = NULL;
            if (ext_surfel->has_v2s_data())
              v2s_lb_data = ext_surfel->v2s_lb_data();
            else
              v2s_lb_data = surfel_pair_temp_data[EXT_INDEX].v2s_lb_data();

            ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
              ext_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
            }
            ext_surfel->curr_ustar_0()[0] = ext_surfel->lb_data()->ustar_0;
            if (sim.is_T_S_solver_type_lb()) {
              sSURFEL_V2S_T_DATA *v2s_t_data;
              if (ext_surfel->has_v2s_data())
                v2s_t_data = ext_surfel->v2s_t_data();
              else
                v2s_t_data = surfel_pair_temp_data[EXT_INDEX].v2s_t_data();
              
              ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                ext_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
              }
            }
	    if (sim.uds_solver_type == LB_UDS) {
	      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
	        sSURFEL_V2S_UDS_DATA *v2s_uds_data;
		if (ext_surfel->has_v2s_data())
		  v2s_uds_data = ext_surfel->v2s_uds_data(nth_uds);
		else
		  v2s_uds_data = surfel_pair_temp_data[EXT_INDEX].v2s_uds_data(nth_uds);
		
		ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		  ext_surfel->curr_outflux_uds(0,nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		}
	      }
            }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		ext_surfel->curr_outflux_mc(0)[latvec_pair] = ext_surfel->mc_data()->out_flux[latvec_pair];
	      }
	    }
#endif
          }
          if (int_surfel->has_two_copies_of_outflux()) {
            sSURFEL_V2S_LB_DATA *v2s_lb_data = NULL;
            if (int_surfel->has_v2s_data())
              v2s_lb_data = int_surfel->v2s_lb_data();
            else
              v2s_lb_data = surfel_pair_temp_data[INT_INDEX].v2s_lb_data();

            ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
              int_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
            }
            int_surfel->curr_ustar_0()[0] = int_surfel->lb_data()->ustar_0;
            if (sim.is_T_S_solver_type_lb()) {
              sSURFEL_V2S_T_DATA *v2s_t_data;
              if (int_surfel->has_v2s_data())
                v2s_t_data = int_surfel->v2s_t_data();
              else
                v2s_t_data = surfel_pair_temp_data[INT_INDEX].v2s_t_data();         
              
              ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                int_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
              }
            }
	    if (sim.uds_solver_type == LB_UDS) {
	      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		sSURFEL_V2S_UDS_DATA *v2s_uds_data;
		if (int_surfel->has_v2s_data())
		  v2s_uds_data = int_surfel->v2s_uds_data(nth_uds);
		else
		  v2s_uds_data = surfel_pair_temp_data[INT_INDEX].v2s_uds_data(nth_uds);         
		
		ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		  int_surfel->curr_outflux_uds(0,nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		}
	      }
            }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		int_surfel->curr_outflux_mc(0)[latvec_pair] = int_surfel->mc_data()->out_flux[latvec_pair];
	      }
	    }
#endif
          }
        }
      }
    }

    {
      BOOLEAN is_lb_active = (sim.init_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
      BOOLEAN is_temp_active = (sim.init_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
      BOOLEAN is_turb_active = (sim.init_solver_mask & KE_PDE_ACTIVE) ? TRUE: FALSE;
      dFLOAT one_over_delta_t = 1.0F / scale_to_delta_t(scale);
      STP_GEOM_VARIABLE local_voxel_size = sim_scale_to_voxel_size(scale);
      DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {

        SURFEL int_surfel = isurfel_pair->m_interior_surfel;
        SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
        LOCAL_SFL_V2S_DATA_PAIR local_surfel_pair_temp_data;
        auto surfel_pair_temp_data = local_surfel_pair_temp_data.v2s_data_pair();
        surfel_pair_temp_data[0].clear();
        surfel_pair_temp_data[1].clear();
        if (sim.init_solver_mask & KE_PDE_ACTIVE) {
          int_surfel->turb_data()->s_mag = 0.0;
          int_surfel->turb_data()->gamma_swirl = 0.0;
          ext_surfel->turb_data()->s_mag = 0.0;
          ext_surfel->turb_data()->gamma_swirl = 0.0;
        }
        if (sim.init_solver_mask & T_PDE_ACTIVE) {
          int_surfel->t_data()->temp_sample = 0.0;
          ext_surfel->t_data()->temp_sample = 0.0;
        }

        BOOLEAN is_ext_high_mach = is_surfel_high_mach(ext_surfel);
	if (!is_full_checkpoint_restore){
	  surfel_v2s_advect_for_seed(ext_surfel, sim.init_solver_mask, &surfel_pair_temp_data[EXT_INDEX],
	      m_seed_prior_solver_index_masks[scale]);


	  if (ext_surfel->has_mirror()) {
	    BOOLEAN is_high_mach = is_surfel_high_mach(ext_surfel);
	    SURFEL_MIRROR_DATA mirror_data = ext_surfel->mirror_data();
	    while (mirror_data) {
	      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
	      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
	      surfel_v2s_advect_for_seed(mirror_surfel, sim.init_solver_mask,
		  &mirror_surfel_v2s_data,
		  m_seed_prior_solver_index_masks[scale]);
	      if (ext_surfel->has_v2s_data()) {
		if (sim.init_solver_mask & LB_ACTIVE) {
		  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  ext_surfel->v2s_lb_data()->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair);
		}
		if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		  ext_surfel->v2s_turb_data()->reflect_to_real_surfel(mirror_v2s_turb_data);
		}
		if (sim.init_solver_mask & T_PDE_ACTIVE) {
		  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		  ext_surfel->v2s_t_data()->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair,
		      sim.is_T_S_solver_type_lb());
		}
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		  ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    ext_surfel->v2s_uds_data(nth_uds)->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
									      mirror_config->m_velocity_mirror_sign_factor,
									      mirror_config->m_reflected_latvec_pair,
									      (sim.uds_solver_type == LB_UDS));
		  }
#if BUILD_D19_LATTICE
		  if(sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    ext_surfel->v2s_pf_data()->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
	      } else {
		if (sim.init_solver_mask & LB_ACTIVE) {
		  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_pair_temp_data[EXT_INDEX].v2s_lb_data();
		  real_v2s_lb_data->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair);
		}
		if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		  sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_pair_temp_data[EXT_INDEX].v2s_turb_data();
		  real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
		}
		if (sim.init_solver_mask & T_PDE_ACTIVE) {
		  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		  sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_pair_temp_data[EXT_INDEX].v2s_t_data();
		  real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair,
		      sim.is_T_S_solver_type_lb());
		}
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		  ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_pair_temp_data[EXT_INDEX].v2s_uds_data(nth_uds);
		    real_v2s_uds_data->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
							      mirror_config->m_velocity_mirror_sign_factor,
							      mirror_config->m_reflected_latvec_pair,
							      (sim.uds_solver_type == LB_UDS));
		  }
#if BUILD_D19_LATTICE
		  if (sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_pair_temp_data[EXT_INDEX].v2s_pf_data();
                    real_v2s_pf_data->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                            mirror_config->m_velocity_mirror_sign_factor,
                                                            mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
	      }
	      ext_surfel->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
		  mirror_config->m_reflected_latvec_pair,
		  mirror_config->m_velocity_mirror_sign_factor,
		  sim.init_solver_mask);
	      mirror_data = mirror_data->m_next_mirror_data;
	    }
	  }
	}

        pre_seed_init_surfel(ext_surfel, is_full_checkpoint_restore, is_ext_high_mach, &surfel_pair_temp_data[EXT_INDEX], TRUE);

	if (ext_surfel->has_mirror()) {
	  SURFEL_MIRROR_DATA mirror_data = ext_surfel->mirror_data();
	  while (mirror_data) {
	    SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	    // Only scale up states uses surfel_v2s_data, which is not done
	    // for mirror surfels
	    pre_seed_init_surfel(mirror_surfel, is_full_checkpoint_restore, is_ext_high_mach, nullptr, FALSE);
	    mirror_data = mirror_data->m_next_mirror_data;
	  }
	}



        BOOLEAN is_int_high_mach = is_surfel_high_mach(int_surfel);
        if (!is_full_checkpoint_restore){
          surfel_v2s_advect_for_seed(int_surfel, sim.init_solver_mask, &surfel_pair_temp_data[INT_INDEX],
                                     m_seed_prior_solver_index_masks[scale]);


	  if (int_surfel->has_mirror()) {
	    BOOLEAN is_high_mach = is_surfel_high_mach(int_surfel);
	    SURFEL_MIRROR_DATA mirror_data = int_surfel->mirror_data();
	    while (mirror_data) {
	      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
	      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
	      surfel_v2s_advect_for_seed(mirror_surfel, sim.init_solver_mask,
		  &mirror_surfel_v2s_data,
		  m_seed_prior_solver_index_masks[scale]);
	      if (int_surfel->has_v2s_data()) {
		if (sim.init_solver_mask & LB_ACTIVE) {
		  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  int_surfel->v2s_lb_data()->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair);
		}
		if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		  int_surfel->v2s_turb_data()->reflect_to_real_surfel(mirror_v2s_turb_data);
		}
		if (sim.init_solver_mask & T_PDE_ACTIVE) {
		  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		  int_surfel->v2s_t_data()->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair,
		      sim.is_T_S_solver_type_lb());
		}
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		  ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    int_surfel->v2s_uds_data(nth_uds)->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
									      mirror_config->m_velocity_mirror_sign_factor,
									      mirror_config->m_reflected_latvec_pair,
									      (sim.uds_solver_type == LB_UDS));
		  }
#if BUILD_D19_LATTICE
		  if(sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    int_surfel->v2s_pf_data()->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                                 mirror_config->m_velocity_mirror_sign_factor,
                                                                 mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
	      } else {
		if (sim.init_solver_mask & LB_ACTIVE) {
		  sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_pair_temp_data[INT_INDEX].v2s_lb_data();
		  real_v2s_lb_data->reflect_to_real_surfel(mirror_v2s_lb_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair);
		}
		if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		  sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		  sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_pair_temp_data[INT_INDEX].v2s_turb_data();
		  real_v2s_turb_data->reflect_to_real_surfel(mirror_v2s_turb_data);
		}
		if (sim.init_solver_mask & T_PDE_ACTIVE) {
		  sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		  sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_pair_temp_data[INT_INDEX].v2s_t_data();
		  real_v2s_t_data->reflect_to_real_surfel(mirror_v2s_t_data, latvec_mask,
		      mirror_config->m_velocity_mirror_sign_factor,
		      mirror_config->m_reflected_latvec_pair,
		      sim.is_T_S_solver_type_lb());
		}
		if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		  ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		    sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_pair_temp_data[INT_INDEX].v2s_uds_data(nth_uds);
		    real_v2s_uds_data->reflect_to_real_surfel(mirror_v2s_uds_data, latvec_mask,
							    mirror_config->m_velocity_mirror_sign_factor,
							    mirror_config->m_reflected_latvec_pair,
							    (sim.uds_solver_type==LB_UDS));
		  }
#if BUILD_D19_LATTICE
		  if (sim.is_pf_model) {
                    sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                    sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_pair_temp_data[INT_INDEX].v2s_pf_data();
                    real_v2s_pf_data->reflect_to_real_surfel(mirror_v2s_pf_data, latvec_mask,
                                                            mirror_config->m_velocity_mirror_sign_factor,
                                                            mirror_config->m_reflected_latvec_pair);
                  }
#endif
		}
	      }
	      int_surfel->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel, latvec_mask,
		  mirror_config->m_reflected_latvec_pair,
		  mirror_config->m_velocity_mirror_sign_factor,
		  sim.init_solver_mask);
	      mirror_data = mirror_data->m_next_mirror_data;
	    }
	  }
	}

        pre_seed_init_surfel(int_surfel, is_full_checkpoint_restore, is_int_high_mach, &surfel_pair_temp_data[INT_INDEX], TRUE);

	if (int_surfel->has_mirror()) {
	  SURFEL_MIRROR_DATA mirror_data = int_surfel->mirror_data();
	  while (mirror_data) {
	    SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	    // Only scale up states uses surfel_v2s_data, which is not done
	    // for mirror surfels
	    pre_seed_init_surfel(mirror_surfel, is_full_checkpoint_restore, is_int_high_mach, nullptr, FALSE);
	    mirror_data = mirror_data->m_next_mirror_data;
	  }
	}



        STP_EVEN_ODD even_odd = int_surfel->even_odd_mask();

#ifdef DEBUG_NEXTGEN
        if (int_surfel->id() == 1486) {
          if (int_surfel->has_v2s_data())
            print_surfel_seed_states("Before seed", int_surfel->v2s_lb_data(), int_surfel);                           
          else                      
            print_surfel_seed_states("Before seed", surfel_pair_temp_data[INT_INDEX].v2s_lb_data(), int_surfel);                           
        }
        if (ext_surfel->id() == 1486) {
          if (ext_surfel->has_v2s_data())
            print_surfel_seed_states("Before seed", ext_surfel->v2s_lb_data(), ext_surfel);                           
          else                      
            print_surfel_seed_states("Before seed", surfel_pair_temp_data[EXT_INDEX].v2s_lb_data(), ext_surfel);                           
        }
#endif

        CALL_SURFEL_PAIR_DYN_METHOD(isurfel_pair, seed, (is_full_checkpoint_restore, even_odd, local_voxel_size, surfel_pair_temp_data));
#ifdef DEBUG_NEXTGEN
        if (int_surfel->id() == 1486) {
          if (int_surfel->has_v2s_data())
            print_surfel_seed_states("After seed", int_surfel->v2s_lb_data(), int_surfel);                           
          else                      
            print_surfel_seed_states("After seed", surfel_pair_temp_data[INT_INDEX].v2s_lb_data(), int_surfel);                           
        }
        if (ext_surfel->id() == 1486) {
          if (ext_surfel->has_v2s_data())
            print_surfel_seed_states("After seed", ext_surfel->v2s_lb_data(), ext_surfel);                           
          else                      
            print_surfel_seed_states("After seed", surfel_pair_temp_data[EXT_INDEX].v2s_lb_data(), ext_surfel);                           
        }
#endif

        if (!is_full_checkpoint_restore) {
          isurfel_dynamics(isurfel_pair, sim.init_solver_mask, sim.init_solver_mask, surfel_pair_temp_data);



	  if (ext_surfel->has_mirror()) {
	    SURFEL_MIRROR_DATA mirror_data = ext_surfel->mirror_data();
	    while (mirror_data) {
	      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
	      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
	      // There is no point to setting a local copy of sSURFEL_TEMP_DATA
	      // It should only be done for mirror surfels that participate in S2S
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
	      if (mirror_surfel->has_two_copies_of_outflux()) {
		msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
		if (ext_surfel->has_v2s_data()) {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = ext_surfel->v2s_lb_data();
		    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair);
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = ext_surfel->v2s_turb_data();
		    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    sSURFEL_V2S_T_DATA *real_v2s_t_data = ext_surfel->v2s_t_data();
		    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair,
			sim.is_T_S_solver_type_lb());
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = ext_surfel->v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
								    (sim.uds_solver_type==LB_UDS));
		    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = ext_surfel->v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                                  mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }

		} else {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_pair_temp_data[EXT_INDEX].v2s_lb_data();
		    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair);
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_pair_temp_data[EXT_INDEX].v2s_turb_data();
		    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_pair_temp_data[EXT_INDEX].v2s_t_data();
		    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair,
			sim.is_T_S_solver_type_lb());
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_pair_temp_data[EXT_INDEX].v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
								    (sim.uds_solver_type==LB_UDS));
		    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_pair_temp_data[EXT_INDEX].v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                                   mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }
		}
	      }
	      mirror_surfel->reflect_from_real_surfel_to_mirror_surfel(ext_surfel, latvec_mask,
		  mirror_config->m_reflected_latvec_pair,
		  mirror_config->m_velocity_mirror_sign_factor,
		  sim.init_solver_mask);
	      // Mirror surfels cannot be sources in S2S
	      // If at some point ghosted mirror surfels need two copies of outflux, this code will
	      // become active
	      if (mirror_surfel->has_two_copies_of_outflux()) {
		msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
		if (mirror_surfel->is_even_or_odd() || mirror_surfel->has_v2s_data()) {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    *mirror_surfel->v2s_lb_data() = *mirror_surfel_v2s_data.v2s_lb_data();
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    *mirror_surfel->v2s_turb_data() = *mirror_surfel_v2s_data.v2s_turb_data();
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    *mirror_surfel->v2s_t_data() = *mirror_surfel_v2s_data.v2s_t_data();
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      *mirror_surfel->v2s_uds_data(nth_uds) = *mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    }
		  }
		} else {
		  sSURFEL_V2S_LB_DATA *v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		    mirror_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
		  }
		  mirror_surfel->curr_ustar_0()[0] = mirror_surfel->lb_data()->ustar_0;
		  if (sim.is_T_S_solver_type_lb()) {
		    sSURFEL_V2S_T_DATA *v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		      mirror_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
		    }
		  }
		  if (sim.uds_solver_type == LB_UDS) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
			mirror_surfel->curr_outflux_uds(0, nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		      }
		    }
		  }
		}
	      }
	      mirror_data = mirror_data->m_next_mirror_data;
	    }
	  }


	  if (int_surfel->has_mirror()) {
	    SURFEL_MIRROR_DATA mirror_data = int_surfel->mirror_data();
	    while (mirror_data) {
	      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
	      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
	      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
	      // There is no point to setting a local copy of sSURFEL_TEMP_DATA
	      // It should only be done for mirror surfels that participate in S2S
              LOCAL_SFL_V2S_DATA mirror_local_v2s_data;
              auto& mirror_surfel_v2s_data = mirror_local_v2s_data.v2s_data();
              mirror_surfel_v2s_data.clear();
	      if (mirror_surfel->has_two_copies_of_outflux()) {
		msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
		if (int_surfel->has_v2s_data()) {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = int_surfel->v2s_lb_data();
		    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair);
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = int_surfel->v2s_turb_data();
		    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    sSURFEL_V2S_T_DATA *real_v2s_t_data = int_surfel->v2s_t_data();
		    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair,
			sim.is_T_S_solver_type_lb());
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = int_surfel->v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
								    (sim.uds_solver_type == LB_UDS));
		    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = int_surfel->v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                  mirror_config->m_velocity_mirror_sign_factor,
                                                                  mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }

		} else {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    sSURFEL_V2S_LB_DATA *real_v2s_lb_data = surfel_pair_temp_data[INT_INDEX].v2s_lb_data();
		    sSURFEL_V2S_LB_DATA *mirror_v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		    mirror_v2s_lb_data->reflect_to_mirror_surfel(real_v2s_lb_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair);
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    sSURFEL_V2S_TURB_DATA *real_v2s_turb_data = surfel_pair_temp_data[INT_INDEX].v2s_turb_data();
		    sSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data = mirror_surfel_v2s_data.v2s_turb_data();
		    mirror_v2s_turb_data->reflect_to_mirror_surfel(real_v2s_turb_data);
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    sSURFEL_V2S_T_DATA *real_v2s_t_data = surfel_pair_temp_data[INT_INDEX].v2s_t_data();
		    sSURFEL_V2S_T_DATA *mirror_v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    mirror_v2s_t_data->reflect_to_mirror_surfel(real_v2s_t_data, latvec_mask,
			mirror_config->m_velocity_mirror_sign_factor,
			mirror_config->m_reflected_latvec_pair,
			sim.is_T_S_solver_type_lb());
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *real_v2s_uds_data = surfel_pair_temp_data[INT_INDEX].v2s_uds_data(nth_uds);
		      sSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      mirror_v2s_uds_data->reflect_to_mirror_surfel(real_v2s_uds_data, latvec_mask,
								  mirror_config->m_velocity_mirror_sign_factor,
								  mirror_config->m_reflected_latvec_pair,
								  (sim.uds_solver_type == LB_UDS));
		    }
#if BUILD_D19_LATTICE
		    if (sim.is_pf_model) {
                      sSURFEL_V2S_PF_DATA *real_v2s_pf_data = surfel_pair_temp_data[INT_INDEX].v2s_pf_data();
                      sSURFEL_V2S_PF_DATA *mirror_v2s_pf_data = mirror_surfel_v2s_data.v2s_pf_data();
                      mirror_v2s_pf_data->reflect_to_mirror_surfel(real_v2s_pf_data, latvec_mask,
                                                                   mirror_config->m_velocity_mirror_sign_factor,
                                                                   mirror_config->m_reflected_latvec_pair);
                    }
#endif
		  }
		}
	      }
	      mirror_surfel->reflect_from_real_surfel_to_mirror_surfel(int_surfel, latvec_mask,
		  mirror_config->m_reflected_latvec_pair,
		  mirror_config->m_velocity_mirror_sign_factor,
		  sim.init_solver_mask);
	      // Mirror surfels cannot be sources in S2S
	      // If at some point ghosted mirror surfels need two copies of outflux, this code will
	      // become active
	      if (mirror_surfel->has_two_copies_of_outflux()) {
		msg_internal_error("surfel %d is mirror and s2s\n", mirror_surfel->id());
		if (mirror_surfel->is_even_or_odd() || mirror_surfel->has_v2s_data()) {
		  if (sim.init_solver_mask & LB_ACTIVE) {
		    *mirror_surfel->v2s_lb_data() = *mirror_surfel_v2s_data.v2s_lb_data();
		  }
		  if (sim.init_solver_mask & KE_PDE_ACTIVE) {
		    *mirror_surfel->v2s_turb_data() = *mirror_surfel_v2s_data.v2s_turb_data();
		  }
		  if (sim.init_solver_mask & T_PDE_ACTIVE) {
		    *mirror_surfel->v2s_t_data() = *mirror_surfel_v2s_data.v2s_t_data();
		  }
		  if (sim.init_solver_mask & UDS_PDE_ACTIVE) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      *mirror_surfel->v2s_uds_data(nth_uds) = *mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		    }
		  }
		} else {
		  sSURFEL_V2S_LB_DATA *v2s_lb_data = mirror_surfel_v2s_data.v2s_lb_data();
		  ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		    mirror_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
		  }
		  mirror_surfel->curr_ustar_0()[0] = mirror_surfel->lb_data()->ustar_0;
		  if (sim.is_T_S_solver_type_lb()) {
		    sSURFEL_V2S_T_DATA *v2s_t_data = mirror_surfel_v2s_data.v2s_t_data();
		    ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		      mirror_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
		    }
		  }
		  if (sim.uds_solver_type == LB_UDS) {
		    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		      sSURFEL_V2S_UDS_DATA *v2s_uds_data = mirror_surfel_v2s_data.v2s_uds_data(nth_uds);
		      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
			mirror_surfel->curr_outflux_uds(0, nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		      }
		    }
		  }
		}
	      }
	      mirror_data = mirror_data->m_next_mirror_data;
	    }
	  }

          if (ext_surfel->has_two_copies_of_outflux()) {
            sSURFEL_V2S_LB_DATA *v2s_lb_data = NULL;
            if (ext_surfel->has_v2s_data())
              v2s_lb_data = ext_surfel->v2s_lb_data();
            else
              v2s_lb_data = surfel_pair_temp_data[EXT_INDEX].v2s_lb_data();

            ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
              ext_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
            }
            ext_surfel->curr_ustar_0()[0] = ext_surfel->lb_data()->ustar_0;
            if (sim.is_T_S_solver_type_lb()) {
              sSURFEL_V2S_T_DATA *v2s_t_data;
              if (ext_surfel->has_v2s_data())
                v2s_t_data = ext_surfel->v2s_t_data();
              else
                v2s_t_data = surfel_pair_temp_data[EXT_INDEX].v2s_t_data();
              
              ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                ext_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
              }
            }
	    if (sim.uds_solver_type == LB_UDS) {
	      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		sSURFEL_V2S_UDS_DATA *v2s_uds_data;
		if (ext_surfel->has_v2s_data())
		  v2s_uds_data = ext_surfel->v2s_uds_data(nth_uds);
		else
		  v2s_uds_data = surfel_pair_temp_data[EXT_INDEX].v2s_uds_data(nth_uds);
		
		ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		  ext_surfel->curr_outflux_uds(0,nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		}
	      }
            }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		ext_surfel->curr_outflux_mc(0)[latvec_pair] = ext_surfel->mc_data()->out_flux[latvec_pair];
	      }
	    }
#endif
          }
          if (int_surfel->has_two_copies_of_outflux()) {
            sSURFEL_V2S_LB_DATA *v2s_lb_data = NULL;
            if (int_surfel->has_v2s_data())
              v2s_lb_data = int_surfel->v2s_lb_data();
            else
              v2s_lb_data = surfel_pair_temp_data[INT_INDEX].v2s_lb_data();

            ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
              int_surfel->curr_outflux(0)[latvec_pair] = v2s_lb_data->m_out_flux[latvec_pair];
            }
            int_surfel->curr_ustar_0()[0] = int_surfel->lb_data()->ustar_0;
            if (sim.is_T_S_solver_type_lb()) {
              sSURFEL_V2S_T_DATA *v2s_t_data;
              if (int_surfel->has_v2s_data())
                v2s_t_data = int_surfel->v2s_t_data();
              else
                v2s_t_data = surfel_pair_temp_data[INT_INDEX].v2s_t_data();
              
              ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
                int_surfel->curr_outflux_t(0)[latvec_pair] = v2s_t_data->m_out_flux_t[latvec_pair];
              }
            }
	    if (sim.uds_solver_type == LB_UDS) {
	      ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
		sSURFEL_V2S_UDS_DATA *v2s_uds_data;
		if (int_surfel->has_v2s_data())
		  v2s_uds_data = int_surfel->v2s_uds_data(nth_uds);
		else
		  v2s_uds_data = surfel_pair_temp_data[INT_INDEX].v2s_uds_data(nth_uds);
		
		ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		  int_surfel->curr_outflux_uds(0,nth_uds)[latvec_pair] = v2s_uds_data->m_out_flux_uds[latvec_pair];
		}
	      }
            }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      ccDOTIMES(latvec_pair, N_SURFEL_PGRAM_VOLUMES) {
		int_surfel->curr_outflux_mc(0)[latvec_pair] = int_surfel->mc_data()->out_flux[latvec_pair];
	      }
	    }
#endif
          }
        }

#ifdef DEBUG_NEXTGEN
        if (int_surfel->id() == 1486) {
          if (int_surfel->has_v2s_data())
            print_surfel_seed_states("After seed dynamics", int_surfel->v2s_lb_data(), int_surfel);                           
          else                      
            print_surfel_seed_states("After seed dynamics", surfel_pair_temp_data[INT_INDEX].v2s_lb_data(), int_surfel);                           
        }
        if (ext_surfel->id() == 1486) {
          if (ext_surfel->has_v2s_data())
            print_surfel_seed_states("After seed dynamics", ext_surfel->v2s_lb_data(), ext_surfel);                           
          else                      
            print_surfel_seed_states("After seed dynamics", surfel_pair_temp_data[EXT_INDEX].v2s_lb_data(), ext_surfel);                           
        }
#endif
      }
    }
#endif
    {
      DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
        pre_seed_init_surfel(sampling_surfel);
      }
    }

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    if(sim.calibration_params.iteration_number < 2) {
      seed_mlrf_surfels(m_seed_prior_solver_index_masks[scale], scale,
                        is_full_checkpoint_restore);
    }
#endif
  }

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  // PHASE B:
  // -- cross-realm preliminary comm & contact averaging--
  do_preliminary_wsurfel_comm();
  if (g_thermal_averaged_contacts.is_enabled_in_sp()) { 
    g_thermal_averaged_contacts.seed_averaged_contacts();
  }
  // -- wsurfels --  
  DO_SCALES_FINE_TO_COARSE(scale) {
    LOCAL_SFL_V2S_DATA local_v2s_data;
    auto& surfel_v2s_data = local_v2s_data.v2s_data();
    STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
    STP_GEOM_VARIABLE meas_scale_factor = 
        (STP_GEOM_VARIABLE)((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1))); 
    DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
      seed_wsurfel_and_contact_surfel_B(is_full_checkpoint_restore, wsurfel_flow, CONTACT_WSURFEL, &surfel_v2s_data,
                                        group_voxel_size, meas_scale_factor);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_cond, scale) {
      seed_wsurfel_and_contact_surfel_B(is_full_checkpoint_restore, wsurfel_cond, CONTACT_WSURFEL, &surfel_v2s_data, 
                                        group_voxel_size, meas_scale_factor);
    }
  }
  // -- contact surfels --
  // Non-conformal contact can involve interaction between surfels in separate strands & scales, and rely on phase B 
  // of dynamics been computed for them in a particular order: 
  //    FRINGE2 (finer->coarser scales) -> INTERIOR/FRINGE (finer->coarser scales)
  // In this way, heat flux computed by primary surfels is available to secondary ones when the latter are processed. 
  // Seeding needs to respect this order, so done in a separate loop as regular surfels above just loop through scales.
  for (asINT32 type = 0; type < nSHOB_CATEGORIES::N_CONTACT_SURFEL_TYPES; type++) {
    asINT32 contact_type = nSHOB_CATEGORIES::CONTACT_SURFEL_TYPES[type];
    for (asINT32 scale = sim.num_scales-1; scale >=0; scale--) {
      LOCAL_SFL_V2S_DATA local_v2s_data;
      auto& surfel_v2s_data = local_v2s_data.v2s_data();
      STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
      STP_GEOM_VARIABLE meas_scale_factor = 
          (STP_GEOM_VARIABLE)((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1)));  
      DO_CONTACT_SURFELS_GROUPS_OF_SCALE_TYPE(contact_surfel_group, scale, contact_type) {
        contact_surfel_group->reset_processed_contact_counters();
        DO_CONTACT_SURFELS_OF_GROUP(contact_surfel, contact_surfel_group, contact_surfel_type) {
          seed_wsurfel_and_contact_surfel_B(is_full_checkpoint_restore, contact_surfel, contact_surfel_type, &surfel_v2s_data,
                                            group_voxel_size, meas_scale_factor);
        }
      }
    }
  }
  // For averaged thermal contact, either all or none related surfels are involved during seeding depending if data is checkpointed
  // Time now to get the actual count and leave it ready for the start of the simulation
  if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
    if (is_full_checkpoint_restore) {
      g_thermal_averaged_contacts.load_ckpt_data();
    } else {
      g_thermal_averaged_contacts.reset_accumulations();
    }
  }
#endif
}

static VOID init_voxel_masks_for_vr_fine_ublks()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {

      if (fine_ublk->is_conduction_solid()) {
        const asINT32 scale = !fine_ublk->is_vr_fine() ? fine_ublk->scale() : coarsen_scale(fine_ublk->scale());
        fine_ublk->conduction_data()->set_init_flux_accumulated_at_face(scale);
      }

    }
  }
}

static VOID finalize_conduction_nearblk_passthrough_weights()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        finalize_conduction_nearblk_passthrough_weights(nearblk);
      }
    }
  }
}

static VOID reset_flux_accumulation_masks_for_conduction_ublks()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (farblk->is_conduction_solid()) {
        farblk->conduction_data()->reset_flux_accumulated_at_face_masks();
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        nearblk->conduction_data()->reset_flux_accumulated_at_face_masks();
      }
    }
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        fine_ublk->conduction_data()->reset_flux_accumulated_at_face_masks();
      }
    }
  }
}

static VOID propagate_voxel_seed_mask_to_vr_fine_ublks()
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      sVR_FINE_INTERFACE_DATA * vr_fine_data = fine_ublk->vr_fine_data();

      if (g_first_vr_fine_ublks.count(fine_ublk->id())) {
        UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
        sVR_COARSE_INTERFACE_DATA * vr_coarse_data = coarse_ublk->vr_coarse_data();
        vr_coarse_data->propagate_smart_seed_mask_to_vr_fine_ublks(coarse_ublk) ;
      }
    }
  }
}

#if !BUILD_5G_LATTICE
static VOID compute_voxel_grad_t_init(UBLK ublk) {
  /* Have to use index = 1 here first because, that is the index that was loaded during the first prelim_ublk_comm */
  const asINT32 ublk_scale = ublk->scale();
  const SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(ublk_scale);
  SOLVER_INDEX_MASK conduction_index_mask = conduction_index_from_mask(prior_solver_index_mask);
  const SOLVER_INDEX_MASK active_solver_mask = g_timescale.m_active_solver_masks[g_timescale.time_parity()][ublk_scale];
  compute_voxel_grad_t(ublk, ublk->fluid_like_voxel_mask, 1, conduction_index_mask, active_solver_mask);
  DO_VOXELS_IN_MASK(voxel, ublk->fluid_like_voxel_mask) {
    ccDOTIMES (i, 3) {
      ublk->conduction_data()->grad_t[1][i][voxel] = ublk->conduction_data()->grad_t[0][i][voxel];
    }
  }
}

static VOID compute_surfel_grad_t_init(SURFEL surfel) {
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
  const asINT32 num_layers = surfel->shell_conduction_data()->num_layers();
  std::vector<sdFLOAT> tmin_dummy(num_layers,0.0);
  std::vector<sdFLOAT> tmax_dummy(num_layers,0.0);
  surfel->shell_conduction_data()->compute_grad_t(prior_index, tmin_dummy, tmax_dummy);
}
#endif

//----------------------------------------------------------------------------
// seed_shobs
//----------------------------------------------------------------------------
cSEEDER::cSEEDER(TIMESTEP start_time) {
  m_seed_time = start_time;
  ccDOTIMES(scale, sim.num_scales) {
    m_seed_prior_solver_index_masks[scale] = compute_seed_solver_index_mask(scale, m_seed_time - 1);
  }  
}

//This definition is only needed for GPU builds, when the HOST_SHOBS_SEEDER is different from
//cSHOBS_SEEDER
#if BUILD_GPU

VOID cHOST_SHOBS_SEEDER::determine_if_ublks_are_simple() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      farblk->determine_if_ublk_is_simple();
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      nearblk->determine_if_ublk_is_simple();
    }
  }  
}

VOID cHOST_SHOBS_SEEDER::process() {
  update_all_time_varying_parameters();
  propagate_voxel_seed_mask_to_vr_fine_ublks();
  send_init_ublk_info();
  do_preliminary_surfel_type_comm();
  accumulate_all_v2s_and_s2s_weights();
  determine_if_ublks_are_simple();
} 
#endif
 
cSHOBS_SEEDER::cSHOBS_SEEDER(TIMESTEP start_time): cSEEDER(start_time) {
}

//Called from seed_sp.cc
void cSHOBS_SEEDER::reflect_states_from_dynblk_to_mirror(SCALE scale)
{
  // During seeding, previous index = 1 and current index = 0 for all scales.
  // Since the first time a scale becomes active
  // previous index = 0 and current_index = 1 for all scales.
  DO_FARBLKS_OF_SCALE(farblk, scale) {
    if (farblk->has_mirror()) {
      reflect_curr_states_to_mirror_ublk(farblk, m_seed_prior_solver_index_masks[scale]);
    }
  }
  DO_NEARBLKS_OF_SCALE(nearblk, scale) {
    if (nearblk->has_mirror()) {
      reflect_curr_states_to_mirror_ublk(nearblk, m_seed_prior_solver_index_masks[scale]);
    }
  }
}

//Called from seed.cc
void cSHOBS_SEEDER::reflect_states_from_vrblk_to_mirror(SCALE scale)
{
  // During seeding, previous index = 1 and current index = 0 for all scales.
  // Since the first time a scale becomes active
  // previous index = 0 and current_index = 1 for all scales.

  DO_VRBLKS_OF_SCALE(vrblk, scale) {
    if (vrblk->has_mirror()) {
      reflect_prev_states_to_mirror_ublk(vrblk, m_seed_prior_solver_index_masks[scale]);
    }
  }
}

//Called from seed.cc
void cSHOBS_SEEDER::reflect_mme_solver_data_from_vrblk_to_mirror(SCALE scale)
{
  // During seeding, previous index = 1 and current index = 0 for all scales.
  // Since the first time a scale becomes active
  // previous index = 0 and current_index = 1 for all scales.

  DO_VRBLKS_OF_SCALE(vrblk, scale) {
    if (vrblk->has_mirror()) {
      reflect_mme_solver_data_to_mirror_ublk(vrblk, m_seed_prior_solver_index_masks[scale]);
    }
  }
}

void cSHOBS_SEEDER::do_dynblk_explode_init(SCALE fine_scale)
{

  /* explode pressure for far-from-surface vrfine, which will be used
   * in pres gradient calculation.
   * explode states and pressure for near_surface vrfine, which will
   * be used in pres gradient calculation and ghost v2s vrfine seeding.
   */
  DO_VRBLKS_OF_SCALE(fine_ublk, fine_scale) {
    sVR_FINE_INTERFACE_DATA *vr_fine_data = fine_ublk->vr_fine_data();
    UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
#if !GPU_COMPILER
    execute_explode_rule_init(coarse_ublk, m_seed_prior_solver_index_masks[coarsen_scale(fine_scale)]);
#else
    msg_internal_error("This version of explode init should not be called on GPU");
#endif
  }
}

VOID cSHOBS_SEEDER::update_all_time_varying_parameters() {
  update_all_time_varying_bsurfel_physics_parameters(g_timescale.m_time);
  update_all_time_varying_surface_physics_parameters();
  update_all_time_varying_fluid_physics_parameters();
  update_all_time_varying_body_force_parameters();  
  update_all_time_varying_shell_physics_parameters();
}

#if !BUILD_5G_LATTICE
/* Function copies temperature from index 1 to 0 on conduction solid ghost voxels
 * The index 1 on ghost voxels is loaded during comm. This function ensures both
 * indices have same value (index 0 is used for gradient calculation in seeding).
 */
void cSHOBS_SEEDER::copy_temperatures_on_conduction_solid_ghost_voxels() {
  {
    DO_SCALES_FINE_TO_COARSE(scale) {
      {
        DO_GHOST_FARBLKS_OF_SCALE(quantum, scale) {
          if (quantum.m_ublk->is_conduction_solid()) {
            quantum.m_ublk->conduction_data()->copy_voxel_temperatures();
          }
        }
      }
      {
        DO_GHOST_NEARBLKS_OF_SCALE(quantum, scale) {
          if (quantum.m_ublk->is_conduction_solid()) {
            quantum.m_ublk->conduction_data()->copy_voxel_temperatures();
          }
        }
      }
    }
  }
}

void cSHOBS_SEEDER::seed_conduction_solid_voxel_gradients() {
  // Function to compute the grad_t at all conduction solid voxels during seeding
  // Needs all voxel temperatures to have been seeded and comm'ed
  cassert(sim.is_conduction_model);
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        compute_voxel_grad_t_init(ublk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        compute_voxel_grad_t_init(nearblk);
      }
    }
  }
}

//DFG-TODO: This seems redundant, as it is done when seeding the wsurfel itself. Check in the debugger and confirm with Sam
void cSHOBS_SEEDER::seed_shell_conduction_surfels() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      if (surfel->is_conduction_shell()) {
        if(surfel->is_even())
          surfel->clone_surfel()->shell_conduction_data()->seed(surfel);
        else
          surfel->shell_conduction_data()->seed(surfel);
      }
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      if (wsurfel->is_conduction_shell()) {
        //DFG-TODO: Once added multilayer support, can all 
        if(wsurfel->is_even())
          wsurfel->clone_surfel()->shell_conduction_data()->seed(wsurfel);
        else
          wsurfel->shell_conduction_data()->seed(wsurfel);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      if (contact_surfel->is_conduction_shell()) {
        //DFG-TODO: Once added multilayer support, can all 
        if(contact_surfel->is_even())
          contact_surfel->clone_surfel()->shell_conduction_data()->seed(contact_surfel);
        else
          contact_surfel->shell_conduction_data()->seed(contact_surfel);
      }
    }

    DO_GHOST_SURFELS_OF_SCALE(ghost_quantum, scale) {
      if (ghost_quantum.m_surfel->is_conduction_shell()) {
        if(ghost_quantum.m_surfel->is_even())
         ghost_quantum.m_surfel->clone_surfel()->shell_conduction_data()->seed(ghost_quantum.m_surfel);
        else
          ghost_quantum.m_surfel->shell_conduction_data()->seed(ghost_quantum.m_surfel);
      }
    }
  }
}

void cSHOBS_SEEDER::seed_shell_conduction_surfel_gradients() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      if (surfel->is_conduction_shell() && !surfel->is_even()) {
        compute_surfel_grad_t_init(surfel);
      }
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      if (wsurfel->is_conduction_shell() && !wsurfel->is_even()) {
        compute_surfel_grad_t_init(wsurfel);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      if (contact_surfel->is_conduction_shell() && !contact_surfel->is_even()) {
        compute_surfel_grad_t_init(contact_surfel);
      }
    }
  }
}

VOID cSHOBS_SEEDER::copy_shell_conduction_surfel_gradients() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      if (surfel->is_conduction_shell()) {
        if(surfel->is_even())
          surfel->clone_surfel()->shell_conduction_data()->copy_seed_grad_t();
        else
          surfel->shell_conduction_data()->copy_seed_grad_t();
      }
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      if (wsurfel->is_conduction_shell()) {
        //DFG-TODO: Once added multilayer support, can all 
        if(wsurfel->is_even())
          wsurfel->clone_surfel()->shell_conduction_data()->copy_seed_grad_t();
        else
          wsurfel->shell_conduction_data()->copy_seed_grad_t();
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      if (contact_surfel->is_conduction_shell()) {
        //DFG-TODO: Once added multilayer support, can all 
        if(contact_surfel->is_even())
          contact_surfel->clone_surfel()->shell_conduction_data()->copy_seed_grad_t();
        else
          contact_surfel->shell_conduction_data()->copy_seed_grad_t();
      }
    }
  }
}

VOID cSHOBS_SEEDER::seed_s2s_all_conduction_surfels() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      conduction_surfel_s2s_sample(surfel, sim.init_solver_mask, FALSE);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      conduction_surfel_s2s_sample(wsurfel, sim.init_solver_mask, FALSE);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      conduction_surfel_s2s_sample(contact_surfel, sim.init_solver_mask, FALSE);
    }
  }
}

VOID cSHOBS_SEEDER::seed_v2s_all_conduction_surfels() {
#if !BUILD_GPU
  DO_SCALES_FINE_TO_COARSE(scale) {
    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      seed_conduction_v2s(surfel);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      seed_conduction_v2s(wsurfel);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      seed_conduction_v2s(contact_surfel);
    }
  }
#endif
}

VOID cSHOBS_SEEDER::seed_s2v_all_conduction_surfels() {
#if !BUILD_GPU
  DO_SCALES_FINE_TO_COARSE(scale) {

    // CONDUCTION-TODO: Replace with FSET based loop
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      seed_conduction_s2v(surfel);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel, scale) {
      seed_conduction_s2v(wsurfel);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      seed_conduction_s2v(contact_surfel);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_group, scale) {
      DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_group) {
        if (!tagged_mlrf_surfel.is_surfel_weightless()) {
          SURFEL mlrf_surfel = tagged_mlrf_surfel.mlrf_surfel();
          seed_conduction_s2v(mlrf_surfel);
        }
      }
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        if (surfel->is_conduction_surface()) {
          seed_conduction_s2v(surfel);
        }
      }
    }
  }
#endif
}
#endif

//Called from seed.cc
VOID cSHOBS_SEEDER::process()
{ 

  update_all_time_varying_parameters();
  
#if !BUILD_5G_LATTICE
  if (sim.is_conduction_model || sim.is_shell_conduction_model) {
    allocate_conduction_mcache();
  }
#endif

  if (!sim.is_full_checkpoint_restore) {
    if (sim.do_smart_seed && sim.extrapolate_seed) {
      // resolve the ao-ublk pointers and turn off consistency checks when
      // extrapolation is requested
      extrapolate_seed_data();
    }
    seed_all_dynblks();

    // The initialization of weights for volumetric conduction requires using the
    // flux accumulation masks in the conduction data, so they need to be reset
    // after the ublk seeding is performed
    reset_flux_accumulation_masks_for_conduction_ublks();

    // Correct pressure values are required in VR fine voxels in order for smart
    // seeding of surfels to work properly. It also helps for simulations starting 
    // from scratch.
    DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
      // Copy states into mirror ublks to facilitate a) state gradient
      // calculations in VR explode and b) fractional advection
#if BUILD_5G_LATTICE
      if (sim.is_large_pore)
	reflect_full_states_from_dynblk_voxels_to_mirror_voxels(scale);
      else
#endif
      {
        reflect_states_from_dynblk_to_mirror(scale);
        // do_dynblk_explode_init(scale);
        reflect_states_from_vrblk_to_mirror(scale);
      }
    }
  }

  if (sim.is_mme_checkpoint_restore) 
    init_voxel_masks_for_vr_fine_ublks();

  // For boundary seeding, the voxel seed mask has to be correctly initialized
  // for vr-fine ublks also (ghost and non-ghost) before the call to
  // initialize_surfels() below, which in turn calls
  // accumulate_all_s2v_and_v2s_weights(). This mask for ghostblks is comm'ed
  // during init ublk comm. If extrapolation of seed data is performed (in
  // seed_all_dynblks() above), then additional coarse ublks may be seeded,
  // and thus they need to propagate their voxel seed masks to their vr-fine
  // children.
  propagate_voxel_seed_mask_to_vr_fine_ublks();

  // copy over the pfluids and voxel masks from real to mirror ublks before send_init_ublk_info transmitts the info to ghosts.
  //copy_voxel_masks_and_pfluids_for_all_ublk_mirror_groups();
  copy_voxel_masks_and_pfluids_to_mirror_ublks();

  // Comm initial ublk info including voxel masks, vr topology, and 
  // trajectory window masks (if particle modeling is enabled).
  send_init_ublk_info();

  // Need to copy mirror ublks before communication of ublk states and data.
  // Also, mirror ublks are not checkpointed during a full checkpoint, so
  // their data is always initialized from their real counterparts.
  DO_SCALES_FINE_TO_COARSE(mirror_init_scale) {
    // eventually modify to these
    //reflect_states_to_mirror_voxels(ublk);
    //reflect_solver_data_to_mirror_voxels(ublk, active_solver_mask);
    // THESE WERE REFLECTED BEFORE EXPLODE_INIT, SO NO NEED TO REFLECT AGAIN.
    //reflect_states_from_dynblk_voxels_to_mirror_voxels(mirror_init_scale, TRUE); // for nearblks
    //reflect_states_from_dynblk_voxels_to_mirror_voxels(mirror_init_scale, FALSE);// for farblks
    //reflect_states_from_vrblk_voxels_to_mirror_voxels(mirror_init_scale, TRUE); // for nearblks
    //reflect_states_from_vrblk_voxels_to_mirror_voxels(mirror_init_scale, FALSE); // for farblks
    reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(mirror_init_scale,
                                                                 sim.init_solver_mask);
#ifdef MIRROR_COMM
    reflect_solver_data_from_ghosted_voxels_to_mirror_voxels(mirror_init_scale, sim.init_solver_mask);
    reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(mirror_init_scale, sim.init_solver_mask);
#endif

  }

  // for nearblks, check *all* surfels its voxels interact with, not just the
  // closest one, and zero out the v2s_dist value, if *any* of the surfels are
  // inlet/outlet type of surfels. The following must be called before ublk
  // comm.
  DO_SCALES_FINE_TO_COARSE(scale) {
    // Why do n't we do this for ghost surfels?
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      surfel_adjust_v2s_dist(surfel);
    }

    // v2s_dist should perhaps be modified for apm surfels too.
#if NOT_FOR_LRF_SURFELS
    DO_ISURFEL_DYN_GROUPS_OF_SCALE(igroup, scale) {
      igroup->adjust_v2s_dist();
    }
#endif
  }
  // Comm all ublks (including pressure values needed for smart seeding)
  
  do_preliminary_ublk_comm(g_timescale.m_timestep, TRUE, !sim.is_full_checkpoint_restore);
  // When restoring from full checkpoints, the other index should be communicated
  // as well, so ghost voxels have valid values in both indices. Ghost fine voxels at VR
  // interface in conduction solver will use both indices.
  // It appears that there is no other use case for both indices - so to optimize,
  // we really only need to comm the other index for conduction datablock on such
  // specified conduction voxels
  if (sim.is_full_checkpoint_restore)
    do_preliminary_ublk_comm(g_timescale.m_timestep, FALSE, !sim.is_full_checkpoint_restore);

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    do_preliminary_ublk_pore_comm();  //comm full_states to prepare for voxel state split, and full_states will be also used in v2s later
    if (sim.is_full_checkpoint_restore) {
      //avoid to clear curr_index states because they store bounce_back part of states
      set_dynblk_states_clear_for_full_ckpt_restore();
    } else {
      //no vr in 5G,  otherwise need to use m_seed_prior_solver_index_masks[scale]
      construct_adv_states_for_all_dynblks_in_seed((g_timescale.m_timestep) & 1); //use full_states to construct f_move and f_bb, and store f_move to prev_state and f_bb to next_state
      compute_gradient_porosity_for_all_dynblks();
    }
  }
#endif  
  
#if !BUILD_5G_LATTICE
  if (sim.is_conduction_model && !sim.is_full_checkpoint_restore) {
    copy_temperatures_on_conduction_solid_ghost_voxels();
  }
#endif

  // CONDUCTION SOLVER: We need to perform an explode here (after the UBLK comm)
  // so that all (including ghost) VRFine voxels get reasonable values
  // (even if interpolation will not happen because grad_t's
  // are not yet computed)
  if (!sim.is_full_checkpoint_restore) {
    DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
      do_dynblk_explode_init(scale);
    }
  }
  
  // The call to accumulate_all_v2s_and_s2v_weights inside
  // initialize_surfels() needs v2s_dist values in the ghostblks comm'ed
  // previously.  Some of the v2s_dist values may have been modified in the
  // call to "adjust_v2s_dist" above.  
  //

  // OLD IRRELEVANT COMMENT - retained temporarily as a reminder of
  // considerations for full checkpoint restore --
  // Call initialize_surfels before the second call to ublk comm which uses a
  // different time index for the voxel grads computation that occurs during
  // ublk comm in case of full checkpoint restore to prevent differences in
  // the solution with/without full checkpoint restore.

  do_preliminary_surfel_type_comm();
  accumulate_all_v2s_and_s2s_weights();

#if !BUILD_5G_LATTICE
  if (sim.is_conduction_model) {
    compute_ublk_ls_grad_coefficients();
    if(!sim.is_full_checkpoint_restore)
      seed_conduction_solid_voxel_gradients();
  }
#endif

  if (!sim.is_full_checkpoint_restore) {
    // If not a full checkpoint restore, finalize the calculation of pthru weights which at this point have
    // received all necessary information from neighboring voxels and surfels and can be inverted and comm'd
    if (sim.is_conduction_sp) {
      finalize_conduction_nearblk_passthrough_weights();
      send_conduction_init_ublk_info();
    }
    // Also, compute the pressure gradients
    if (sim.is_turb_model) {
      DO_SCALES_FINE_TO_COARSE(scale) {
        asINT32 next_timestep_index = sim_prior_timestep_index(scale, g_timescale.m_timestep) ^ 1;
        DO_NEARBLKS_OF_SCALE(nearblk, scale) {
          // CONDUCTION-TODO: Remove this conduction_solid check after FSETs are properly defined
          // and the appropriate new macros are defined
          if (!nearblk->is_conduction_solid()) {
            VOXEL_GRADS all_voxel_grads = nearblk->surf_turb_data()->voxel_grads;
            compute_pressure_gradients(nearblk, nearblk->lb_interaction_voxel_mask, all_voxel_grads, next_timestep_index);
          }
        }
      }
    }
  }


  do_preliminary_ublk_comm(g_timescale.m_timestep, FALSE, !sim.is_full_checkpoint_restore);

#if BUILD_5G_LATTICE
  if (sim.smart_seed_contact_angle)
    do_surfel_potential_comm();  //comm surfel potential

#endif
  if (!sim.is_full_checkpoint_restore) {
    DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
      do_dynblk_explode_init(scale);
#if BUILD_D39_LATTICE || BUILD_5G_LATTICE
      reflect_mme_solver_data_from_vrblk_to_mirror(scale);
#else // BUILD_D19_LATTICE
      if( sim.is_movb_sim() || sim.is_hydrogen_eos || sim.is_HSExt_solver ) {
        reflect_mme_solver_data_from_vrblk_to_mirror(scale);
      }
#endif
    }
  }
 
#if BUILD_5G_LATTICE
  if (sim.smart_seed_contact_angle) {
    do_surfel_potential_comm();  //comm surfel potential 
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "Done with prelim surfel comm for wetting");
  }
#endif
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0)
      sim.transient_boundary_seeding[i].calculate_interpolation_index();
  }

#if !BUILD_5G_LATTICE

  if (!sim.is_full_checkpoint_restore && sim.is_shell_conduction_model) {
    seed_shell_conduction_surfels();
  }

#endif

  if (sim.is_full_checkpoint_restore) { //restores accumulated_dE from past time-steps
    SIMULATOR_NAMESPACE::fill_ckpt_conduction_interface_accumulations();
    SIMULATOR_NAMESPACE::fill_ckpt_shell_layer_accumulations();
  }

  do_seed_all_surfels(sim.is_full_checkpoint_restore);

  if (sim.is_radiation_model) {
    do_initial_radiation_sends();

    // If restoring from a checkpoint, this is restarting the
    // run that was happening when the simulation was checkpointed
    if (!sim.is_full_checkpoint_restore) {
      do_initial_radiation_recvs();
    }
  }

  if (!sim.is_full_checkpoint_restore) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
        memset(fine_ublk->lb_states(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        if ((sim.init_solver_mask & T_PDE_ACTIVE) && sim.is_T_S_solver_type_lb()) {
          memset(fine_ublk->t_data()->lb_t_data(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        }
	if ((sim.init_solver_mask & UDS_PDE_ACTIVE) && (sim.uds_solver_type == LB_UDS)) {
	  ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
            memset(fine_ublk->uds_data(nth_uds)->lb_uds_data(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        }
      }
    }
  }

/*  DO_SCALES_FINE_TO_COARSE(mirror_init_scale) {
    // Mirror surfels are checkpointed, so we should not overwrite the
    // restored states. If not restoring from a full checkpoint, their data is
    // initialized from their real counterparts after they have been seeded.
    if (!sim.is_full_checkpoint_restore) {
      reflect_from_real_surfels_to_mirror_surfels(mirror_init_scale, sim.init_solver_mask);
    }
  }*/

  /* Computing surfel grad_t values happens in two calls:
   * Similar to UBLKs, the ghost surfels are expected to be seeded with correct values read from PDs.
   * The purpose of first call is to get the grad_t values based on temperatures and
   * BC edges. A communication after this is necessary to get these grad_t values on ghost surfels.
   * The second call made after the surfel comm takes care of computing the grad t values
   * which include BC surfel grad_t contribution.
   * To ensure BC surfel contribution is ignored in the first call, the copy of grad_t
   * values from index 1 to 0 is performed in a separate call.
   */
#if !BUILD_5G_LATTICE
  if (!sim.is_full_checkpoint_restore && sim.is_shell_conduction_model) {
    seed_shell_conduction_surfel_gradients();
    copy_shell_conduction_surfel_gradients();
  }
#endif

  // The ghost surfels feed into the initial surfel to surfel advection. Hence
  // the need to fill them prior to starting the main simulation loop.
  //
  // Also surfel comm must occur before voxel_dyn_groups_post_seed_init so
  // that ghost closest surfels can provide correct information for voxel
  // boundary seeding.  
  do_preliminary_surfel_comm();

#if !BUILD_5G_LATTICE
  /* Conduction solver related additions */
  if (!sim.is_full_checkpoint_restore && sim.is_conduction_model) {
    seed_s2v_all_conduction_surfels();

    seed_conduction_solid_voxel_gradients();

    // do_preliminary_conduction_ublk_comm();
    do_preliminary_ublk_comm(g_timescale.m_timestep, FALSE, !sim.is_full_checkpoint_restore);

    /* Perform S2S to improve surfel sources in the first TS in surfel dynamics */
    seed_s2s_all_conduction_surfels();

    /* Perform V2S to improve voxel sources in the first TS in surfel dynamics */
    seed_v2s_all_conduction_surfels();
  }
  
#endif

#if !BUILD_5G_LATTICE
  if (!sim.is_full_checkpoint_restore && sim.is_shell_conduction_model) {
    seed_shell_conduction_surfel_gradients();
    copy_shell_conduction_surfel_gradients();
  }
#endif

  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("Done with prelim surfel comm");

  post_seed_init_for_all_ublks();

  if (!sim.is_full_checkpoint_restore) {
    unset_exploded_fine_ublks(); 
  }
#if BUILD_5G_LATTICE
  if (g_specified_contact_angle == 0 && my_proc_id == 0) {
    msg_print_no_prefix("Warning :: Surface affinity (potential), instead of contact angle, is specified in this case. \n");
  }
#endif

  std::unordered_set<SHOB_ID>().swap(g_first_vr_fine_ublks);

#if !BUILD_5G_LATTICE
#if !GPU_COMPILER
  //if (sim.is_conduction_sp && sim.is_conduction_model && sim.use_implicit_solid_solver) {
  //  setup_implicit_solid_solver();
  //}
#endif
#endif
}

VOID cCALIBRATION_RUN_SEEDER::process() {

  update_all_time_varying_parameters();

  if (sim.do_smart_seed && sim.extrapolate_seed) {
    // resolve the ao-ublk pointers and turn off consistency checks when
    // extrapolation is requested
    extrapolate_seed_data();
  }
  seed_all_dynblks();
  // Correct pressure values are required in VR fine voxels in order for smart
  // seeding of surfels to work properly. It also helps for simulations starting
  // from scratch.
  DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
    // Copy states into mirror ublks to facilitate a) state gradient
    // calculations in VR explode and b) fractional advection
    reflect_states_from_dynblk_to_mirror(scale);
    // do_dynblk_explode_init(scale);
    reflect_states_from_vrblk_to_mirror(scale);
  }

  // For boundary seeding, the voxel seed mask has to be correctly initialized
  // for vr-fine ublks also (ghost and non-ghost) before the call to
  // initialize_surfels() below, which in turn calls
  // accumulate_all_s2v_and_v2s_weights(). This mask for ghostblks is comm'ed
  // during init ublk comm. If extrapolation of seed data is performed (in
  // seed_all_dynblks() above), then additional coarse ublks may be seeded,
  // and thus they need to propagate their voxel seed masks to their vr-fine
  // children.
  propagate_voxel_seed_mask_to_vr_fine_ublks();

  // copy over the pfluids and voxel masks from real to mirror ublks before send_init_ublk_info transmitts the info to ghosts.
  //copy_voxel_masks_and_pfluids_for_all_ublk_mirror_groups();
  copy_voxel_masks_and_pfluids_to_mirror_ublks();

  // Need to copy mirror ublks before communication of ublk states and data.
  // Also, mirror ublks are not checkpointed during a full checkpoint, so
  // their data is always initialized from their real counterparts.
  DO_SCALES_FINE_TO_COARSE(mirror_init_scale) {
    // eventually modify to these
    //reflect_states_to_mirror_voxels(ublk);
    //reflect_solver_data_to_mirror_voxels(ublk, active_solver_mask);
    // THESE WERE REFLECTED BEFORE EXPLODE_INIT, SO NO NEED TO REFLECT AGAIN.
    //reflect_states_from_dynblk_voxels_to_mirror_voxels(mirror_init_scale, TRUE); // for nearblks
    //reflect_states_from_dynblk_voxels_to_mirror_voxels(mirror_init_scale, FALSE);// for farblks
    //reflect_states_from_vrblk_voxels_to_mirror_voxels(mirror_init_scale, TRUE); // for nearblks
    //reflect_states_from_vrblk_voxels_to_mirror_voxels(mirror_init_scale, FALSE); // for farblks
    reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(mirror_init_scale,
                                                                 sim.init_solver_mask);
#ifdef MIRROR_COMM
    reflect_solver_data_from_ghosted_voxels_to_mirror_voxels(mirror_init_scale, sim.init_solver_mask);
    reflect_solver_data_from_non_ghosted_voxels_to_mirror_voxels(mirror_init_scale, sim.init_solver_mask);
#endif

  }

  // For full checkpoint restore, decrement the timestep used for computing
  // final voxel pressure gradients for sending to the ghostblks. This is
  // because the checkpoint time-step is incremented with respect to the
  // time-step used to compute the voxel gradients
  if (sim.is_turb_model) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {
        VOXEL_GRADS all_voxel_grads = nearblk->surf_turb_data()->voxel_grads;
        compute_pressure_gradients(nearblk, nearblk->lb_interaction_voxel_mask, all_voxel_grads, NEXT_TIMESTEP);
      }
    }
  }

  mark_ublk_comm_ready();
  wait_for_ublk_comm_done();
#if BUILD_5G_LATTICE
  if (sim.smart_seed_contact_angle)
    do_surfel_potential_comm();  //comm surfel potential

#endif
    DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
      do_dynblk_explode_init(scale);
#if BUILD_D39_LATTICE || BUILD_5G_LATTICE
      reflect_mme_solver_data_from_vrblk_to_mirror(scale);
#else // BUILD_D19_LATTICE
      if( sim.is_movb_sim() || sim.is_hydrogen_eos || sim.is_HSExt_solver ) {
        reflect_mme_solver_data_from_vrblk_to_mirror(scale);
      }
#endif
    }
#if BUILD_5G_LATTICE
  if (sim.smart_seed_contact_angle) {
    do_surfel_potential_comm();  //comm surfel potential
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "Done with prelim surfel comm for wetting");
  }
#endif
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_params.n_frames_per_iter > 0)
      sim.transient_boundary_seeding[i].calculate_interpolation_index();
  }

  do_seed_all_surfels(sim.is_full_checkpoint_restore);

    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
        memset(fine_ublk->lb_states(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        if ((sim.init_solver_mask & T_PDE_ACTIVE) && (sim.T_solver_type == LB_TEMPERATURE)) {
          memset(fine_ublk->t_data()->lb_t_data(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        }
	if ((sim.init_solver_mask & UDS_PDE_ACTIVE) && (sim.uds_solver_type == LB_UDS)) {
	  ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
	    memset(fine_ublk->uds_data(nth_uds)->lb_uds_data(0), 0 , sizeof(sUBLK_STATES_DATA)*2);
        }
      }
    }

  mark_surfel_comm_ready();
  wait_for_surfel_comm_done();
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("Done with prelim surfel comm");

  unset_exploded_fine_ublks();
#if BUILD_5G_LATTICE
  if (g_specified_contact_angle == 0 && my_proc_id == 0) {
    msg_print_no_prefix("Warning :: Surface affinity (potential), instead of contact angle, is specified in this case. \n");
  }
#endif
}


void cCALIBRATION_RUN_SEEDER::wait_for_ublk_comm_ready() {
  while(!m_ublk_comm_ready) {
    sleep(MPI_SLEEP_SHORT);
  }
  m_ublk_comm_ready = FALSE;
}

void cCALIBRATION_RUN_SEEDER::wait_for_ublk_comm_done() {
  while(!m_ublk_comm_done) {
    sleep(MPI_SLEEP_SHORT);
  }
  m_ublk_comm_done  = FALSE;
}

void cCALIBRATION_RUN_SEEDER::wait_for_surfel_comm_ready() {
  while(!m_surfel_comm_ready) {
    sleep(MPI_SLEEP_SHORT);
  }
  m_surfel_comm_ready = FALSE;
}
void cCALIBRATION_RUN_SEEDER::wait_for_surfel_comm_done() {
  while(!m_surfel_comm_done) {
    sleep(MPI_SLEEP_SHORT);
  }
  m_surfel_comm_done = FALSE;
}
void cCALIBRATION_RUN_SEEDER::mark_ublk_comm_ready(){m_ublk_comm_ready = TRUE;}
void cCALIBRATION_RUN_SEEDER::mark_ublk_comm_done(){m_ublk_comm_done = TRUE;}
void cCALIBRATION_RUN_SEEDER::mark_surfel_comm_ready(){m_surfel_comm_ready = TRUE;}
void cCALIBRATION_RUN_SEEDER::mark_surfel_comm_done(){m_surfel_comm_done = TRUE;}

VOID cCALIBRATION_RUN_SEEDER::comm_shobs() {
  wait_for_ublk_comm_ready();
// PR46310 : A barrier is needed at this place to make sure that the surfel sends are received
//  calibration_surfel_comm and not outside it. Surfel data is sent twice during the
//  calibration seeding timestep.
  sp_synchronize();
  do_calibration_ublk_comm(g_timescale.m_timestep, TRUE);
  mark_ublk_comm_done();
  wait_for_surfel_comm_ready();
  do_calibration_surfel_comm();
  sp_synchronize();
  mark_surfel_comm_done();
}

//----------------------------------------------------------------------------
// seed_shobs due to T_solver switch, ONLY for 19-s
//-----------------------------------------------------------------------


   
#if BUILD_D19_LATTICE
VOID seed_dyn_surfel_T_lb_scalar_solver()
{

  DO_SCALES_FINE_TO_COARSE(scale) {
    ccDOTIMES(group_type, N_SURFEL_BASE_GROUP_TYPES) {
      DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {

        if (group->m_send_group != NULL) {
          group->m_send_group->wait_for_copying_of_send_data();
        }
      }
    }
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      seed_surfel_out_flux_t(surfel);
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(surfel_pair, scale) {
      SURFEL int_surfel = surfel_pair->m_interior_surfel;
      SURFEL ext_surfel = surfel_pair->m_exterior_surfel;
      seed_surfel_out_flux_t(int_surfel);
      seed_surfel_out_flux_t(ext_surfel);
    }

    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_group, scale) {
      mlrf_group->seed_T_scalar_solver();
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        seed_surfel_out_flux_t(quantum.m_surfel);
      }
    }
  }
}

#endif

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID update_closest_surfel_BC_based_on_smart_seed_markers(UBLK_TYPE* ublk) {
  auto& simc = get_simc_ref();
#if BUILD_5G_LATTICE
  BOOLEAN perform_update = g_is_multi_component && ublk->is_near_surface();
#else
  BOOLEAN perform_update = simc.is_turb_model && ublk->is_near_surface();
#endif

  if (perform_update) {
    dFLOAT voxel_size = scale_to_voxel_size(ublk->scale());
    dFLOAT local_voxels_per_meter = 1.0F / (simc.meters_per_cell * voxel_size);
    dFLOAT l_rough = STP_L_ROUGH_CONSTANT * local_voxels_per_meter;

    BOOLEAN smart_seed_boundaries = (simc.do_smart_seed && simc.smart_seed_boundaries);

    auto surf_geom = ublk->surf_geom_data();
    auto fluid_like_voxel_mask = ublk->fluid_like_voxel_mask;
    DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {
      auto closest_surfel = surf_geom->closest_surfels[voxel].get_surfel();
      size_t surfel_offset = surf_geom->closest_surfels[voxel].get_offset();      

      // Adjust v2s dist from accumulate_v2s_surfel_weights()
      if (ublk->surf_lb_data()->v2s_dist[voxel] >= CLOSEST_SURFEL_IS_SLIP_SURFEL)
        ublk->surf_lb_data()->v2s_dist[voxel] -= CLOSEST_SURFEL_IS_SLIP_SURFEL;

      if (closest_surfel && !ublk->non_lrf_surfels_interaction_mask.test(voxel))
        ublk->surf_lb_data()->v2s_dist[voxel] = DIST_TO_SURFACE_LRF;

      if (closest_surfel) {
        STP_GEOM_VARIABLE dist = surf_geom->dist_to_surface[voxel];

        // TODO To ensure free slip goes here. It should be fixed during init of dynamics data
        if (closest_surfel->is_wall() && !closest_surfel->is_not_free_slip_wall(surfel_offset)) {
          dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_EXTRAPOLATE;
        } else {
          switch (closest_surfel->lb_data()->boundary_condition_type[surfel_offset]) {
          case BOUNDARY_CONDITION_LINEAR_SLIP_WALL:
            // A linear slip surfel is forced to be free slip if resolution is too coarse
            if (l_rough < (12.0 * STP_L_ROUGH_CONSTANT))
              dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_EXTRAPOLATE;
            break;
          case BOUNDARY_CONDITION_FREE_SLIP_WALL:
            dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_EXTRAPOLATE;
            break;
          case BOUNDARY_CONDITION_NOSLIP_WALL:
            dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_EXTRAPOLATE;
            break;
          case BOUNDARY_CONDITION_KE:
            dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_KE;
            break;
          case BOUNDARY_CONDITION_INTENSITY:
            dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_INTENSITY;
            break;
          case BOUNDARY_CONDITION_EXTRAPOLATED:
            dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_EXTRAPOLATE;
            break;
          default:
            break;
          }
        }

        // Negative values are used to encode special boundary conditions
        if ((dist > 0) && (dist < g_dist_pde_cutoff)) {
        }
#if !BUILD_5G_LATTICE
        else if ((dist == HD_NAMESPACE::DIST_TO_SURFACE_TURB_INTENSITY)
                 && smart_seed_boundaries
                 && (closest_surfel->turb_data()->s_mag[surfel_offset] < 0)) {
          //TODO have to use a different marker to signify smart seeding for these surfels

          // Inlet/outlet surfels whose boundaries have been smart-seeded are marked
          // with a negative tke_pde. In this case, they must have a k-epsilon
          // BC, not a turb intensity and length scale BC.
          dist = HD_NAMESPACE::DIST_TO_SURFACE_TURB_KE;
        }
#endif
        surf_geom->dist_to_surface[voxel] = dist;
      }
    }
  }
}

template
VOID update_closest_surfel_BC_based_on_smart_seed_markers<sUBLK>(sUBLK* ublk);

#if BUILD_GPU
template
VOID update_closest_surfel_BC_based_on_smart_seed_markers<sMBLK>(sMBLK* ublk);
#endif

#if BUILD_GPU
VOID init_mblk(tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG>& child_smart_seed_data,
               tUBLK_SMART_SEED_DATA<HMBLK_SDFLOAT_TYPE_TAG>& mblk_smart_seed_data,
               int child_ublk) {
  for (int v = 0, mv = N_VOXELS_8 * child_ublk; v < N_VOXELS_8; v++, mv++) {
    mblk_smart_seed_data.w[mv].seed_var_spec_index = child_smart_seed_data.w[v].seed_var_spec_index;
  }

  for (int var_index = 0; var_index < (int) DGF_N_SEED_VARS; var_index++) {
    for (int v = 0, mv = N_VOXELS_8 * child_ublk; v < N_VOXELS_8; v++, mv++) {
      uINT8 mblk_seed_var_spec_index = mblk_smart_seed_data.w[mv].seed_var_spec_index; 
      uINT8 mblk_controller_index = g_fluid_seed_var_specs[mblk_seed_var_spec_index].seed_controller;
      uINT8 child_seed_var_spec_index = child_smart_seed_data.w[v].seed_var_spec_index; 
      uINT8 child_controller_index = g_fluid_seed_var_specs[child_seed_var_spec_index].seed_controller;
      if (::sim.m_seed_control[child_controller_index].is_var_seeded[var_index]) {    
        mblk_smart_seed_data.vars(var_index, mv, mblk_controller_index) = \
          child_smart_seed_data.vars(var_index, v, child_controller_index);
      }
    }
  }
}
#endif
