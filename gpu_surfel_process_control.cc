/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#if BUILD_GPU

#include "gpu_host_include.h"
#include "surfel_process_control.h"
#include "strand_mgr.h"
#include "mirror.h"
#include "mlrf.h"
#include "gpu_exprlang.h"
#include PHYSICS_H

namespace GPU {

  template <BOOLEAN IS_LB_ACTIVE,
            BOOLEAN IS_UDS_LB_SOLVER_ON,
            BOOLEAN IS_T_LB_SOLVER_ON>
  void process_dyn_surfels(SURFEL_BASE_GROUP_TYPE gtype,
                           sSURFEL_PROCESS_CONTROL* surfel_process_control,
			                     const size_t range[2],
                           STP_PROC dest_sp);

  template <BOOLEAN IS_LB_ACTIVE,
            BOOLEAN IS_UDS_LB_SOLVER_ON,
            BOOLEAN IS_T_LB_SOLVER_ON>
  void process_dyn_surfels(SURFEL_GROUP_TYPE gtype,
                           sSURFEL_PROCESS_CONTROL* surfel_process_control,
			                     const size_t range[2],
                           STP_PROC dest_sp);
}


template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID sSURFEL_PROCESS_CONTROL::process_all_surfels_internal(SURFEL_BASE_GROUP_TYPE gtype,
							   sMSFL_FSET::GROUP group,
                 STRAND strand_type)
{
  //printf("MPI (%d): process_all_surfels_internal group type %d\n", my_proc_id, group_type);
  switch (group->m_supertype) {
  case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
  {
    const auto range = GPU::get_surfel_group_range((SURFEL_GROUP_TYPE)gtype, m_scale, group->m_dest_sp.nsp());
    LOG_MSG("GPU_GENERIC",LOG_FUNC).printf( "TS %ld group type %d dest sp %d range 0: %d range 1: %d", g_timescale.m_time, gtype, group->m_dest_sp.nsp(), range[0], range[1]);
    GPU::process_dyn_surfels<IS_LB_ACTIVE, IS_UDS_LB_SOLVER_ON, IS_T_LB_SOLVER_ON>(gtype, this, range.data(), group->m_dest_sp.nsp());
    break;
  }
  case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
  case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
  case SAMPLING_SURFEL_GROUP_SUPERTYPE:
  default:
    msg_internal_error("Unsupported surfel super type %d",group->m_supertype);
    break;
  }
}

VOID sSURFEL_PROCESS_CONTROL::process_all_surfels(STRAND strand_type, SURFEL_BASE_GROUP_TYPE gtype)
{

  BOOLEAN is_T_lb_solver_on = is_temp_active() &&  sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);
  
  DO_MSFL_GROUPS_OF_SCALE_TYPE(group, m_scale, gtype) {
    
    if (is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<TRUE, TRUE, TRUE>(group, strand_type);
    } else if (is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<TRUE, TRUE, FALSE>(group, strand_type);
    } else if (is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
      for(int dest_sp : GPU::get_surfel_group_dest_sp((SURFEL_GROUP_TYPE)gtype, m_scale)) {
        const auto range = GPU::get_surfel_group_range((SURFEL_GROUP_TYPE)gtype, m_scale, dest_sp);
        if (!GPU::is_empty_group(range)) {
          PHYSICS_DESCRIPTOR pd = sim.flow_surface_physics_descs;
          //This naive loop through all pds avoids developing new mechanism for finding MSFL's 
          //time and space varying pds. Inside fill_surfel_varying_data we filter MSFLs by 
          //comparing the loop PD with their PD.
          for(size_t i=0; i<sim.n_flow_surface_physics_descs; i++)
          {
            if (pd->is_surface_physics_descriptor() && pd->some_parameter_time_and_space_varying){
              MSFL_FSET msfl_group_fset = g_msfl_groups[gtype];

              if (msfl_group_fset && msfl_group_fset->n_groups()) {
                  GPU::g_exprlang_mgr.fill_surfel_varying_data(pd, (SURFEL_GROUP_TYPE)gtype, m_scale, GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE::TIME_AND_SPACE, dest_sp);//copy data to device  
              }
            }
            else if (pd->some_parameter_time_varying_only)
            {
               MSFL_FSET msfl_group_fset = g_msfl_groups[gtype];

              if (msfl_group_fset && msfl_group_fset->n_groups()) {
                  GPU::g_exprlang_mgr.fill_surfel_varying_data(pd, (SURFEL_GROUP_TYPE)gtype, m_scale, GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE::ALL_SHARABLE, dest_sp);//copy data to device  
              }
            }
            pd++;
          }
        }
      }
      process_all_surfels_internal<TRUE, FALSE, FALSE>(gtype, group, strand_type);
    } else if (is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      //process_all_surfels_internal<TRUE, FALSE, TRUE>(group_type, group, strand_type);
    } else if (!is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<FALSE, TRUE, FALSE>(group, strand_type);
    } else if (!is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<FALSE, TRUE, TRUE>(group, strand_type);
    } else if (!is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<FALSE, FALSE, FALSE>(group, strand_type);
    } else if (!is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
      msg_internal_error("This simulation type is not supported for mega surfels.");
      // process_all_surfels_internal<FALSE, FALSE, TRUE>(group, strand_type);
    }

    if (group->m_send_group != NULL) {

      LOG_MSG("GPU_GENERIC",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);

      g_strand_mgr.m_send_queue->add_sp_entry(group->m_send_group, m_active_solver_mask, TRUE);
    }
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON>
VOID sSURFEL_PROCESS_CONTROL::do_all_ghost_surfels_s2v_internal(sMSFL_FSET::GROUP group,
				                                                        STRAND strand_type)
{
  //printf("MPI (%d): do_all_ghost_surfels_s2v_internal group type %d\n", my_proc_id, GHOST_SURFEL_GROUP_TYPE);
  auto sgroup = static_cast<MSFL_GROUP>(group);
  const auto range = GPU::get_surfel_group_range(GHOST_SURFEL_GROUP_TYPE, m_scale, group->m_dest_sp.nsp());
  LOG_MSG("GPU_GENERIC",LOG_FUNC).printf( "TS %ld group type %d dest sp %d range 0: %d range 1: %d", g_timescale.m_time, GHOST_SURFEL_GROUP_TYPE, group->m_dest_sp.nsp(), range.data()[0], range.data()[1]);
  //GPU ghost surfels don't support UDS yet
  GPU::ghost_surfels_s2v<IS_LB_ACTIVE, FALSE, IS_T_LB_SOLVER_ON>(this, range.data(), group->m_dest_sp.nsp());
}

VOID sSURFEL_PROCESS_CONTROL::do_all_ghost_surfels_s2v(STRAND strand_type)
{
  BOOLEAN is_T_lb_solver_on = is_temp_active() && sim.is_T_S_solver_type_lb();

  const auto& surfel_64_groups = g_msfl_groups[GHOST_SURFEL_GROUP_TYPE];
  //for( SCALE scale = 0; scale <sim.num_scales; scale++) 
  {
    if(surfel_64_groups->n_groups_of_scale(m_scale)>0) 
    {
      const auto& set_of_sfl_64_groups = surfel_64_groups->get_groups_of_scale(m_scale);
      for (MSFL_GROUP sfl_64_group : set_of_sfl_64_groups) 
      {
        if (is_lb_active() && !is_T_lb_solver_on) {
            //m_scale = scale;
            do_all_ghost_surfels_s2v_internal<TRUE, FALSE>(sfl_64_group, strand_type);
      }
     }
   }
  }
}

#endif
