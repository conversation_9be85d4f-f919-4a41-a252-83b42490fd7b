/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1995-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */

/*--------------------------------------------------------------------------*
 * Tree for Ublk boxes and ublks 
 *
 * Abhishek Jain
 * Created Sep 30, 2024
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_UBLK_TREE_H_
#define _SIMENG_UBLK_TREE_H_

#include "ublk_box_tree.h"

struct sUBLK_TREE_NODE {
  uINT16                          m_index;        //index of the node in the tree node vector m_nodes
  uINT8                           m_level;        //level of this node in the tree 
  std::vector<STP_UBLK_ID>        m_ublks;        //a vector of ublk ids in the g_ublk_table
  STP_COORD                       m_min[3];       //the min location of the domain of this node in each direction                                    
  STP_COORD                       m_max[3];       //the max location of the domain of this node in each direction                                    
  uINT16                          m_children[8] = {0};  //an array of pointers to the children nodes (currently 8, will change according to 2^ndims later)
  
  UBLK ublk_from_index(STP_UBLK_ID index) {
    return g_ublk_table[STP_FLOW_REALM].ublk_from_id( m_ublks.at(index) );
  };

};

struct sUBLK_TREE {
  sUBLK_TREE_NODE*    root_node; //pointer to the root node aka the first node in the vector m_nodes
  std::vector<sUBLK_TREE_NODE>   m_nodes;  //vector of all nodes

  sUBLK_TREE_NODE get_root_node() { return m_nodes.at(0); };
                                       
  sUBLK_TREE() {   //tree constructor
  };
  
  ~sUBLK_TREE() {}; //destructor

  VOID build_tree() {
    create_root_node();
    propogate_branch_until_leaf(get_root_node());
  };

  VOID create_root_node() {
    //1. now fill the vector of ublk_ids.
    sUBLK_TREE_NODE first_node;
    first_node.m_index = 0;
    first_node.m_level = 0;
    asINT32 i;
    for (i = 0; i < g_ublk_table[STP_FLOW_REALM].n_ublks(); i++) {
      first_node.m_ublks.push_back( g_ublk_table[STP_FLOW_REALM].ublk(i)->id() );
    }
    
    ccDOTIMES(axis, 3) {
      first_node.m_min[axis] = std::numeric_limits<STP_COORD>::max() ;// 200000000; //2e8
      first_node.m_max[axis] = -std::numeric_limits<STP_COORD>::max() ;//-200000000;//-2e8
    }
    
    //TODO:DO this step in the for loop above
    //2. run through all ublk_ids, find out min and max
    ccDOTIMES(i, first_node.m_ublks.size() ) {
      UBLK ublk = first_node.ublk_from_index(i);
      dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
      dFLOAT ivdx = 1.0 / voxel_dx;
      dFLOAT ublk_distance = 2.0 * voxel_dx;

      ccDOTIMES(axis, 3) {
        STP_COORD ublk_min = ublk->location(axis);
        STP_COORD ublk_max = ublk->location(axis) + ublk_distance;
        first_node.m_min[axis] = std::min ( first_node.m_min[axis], ublk_min );
        first_node.m_max[axis] = std::max ( first_node.m_max[axis], ublk_max );
      }
    }

    //3. put root_node in the tree node vector, and update the pointer to the root_node
    m_nodes.push_back(first_node);
    root_node = &m_nodes.back();

  };

  VOID propogate_branch_until_leaf (sUBLK_TREE_NODE current_node) {
    if (current_node.m_ublks.size() <= 100 || current_node.m_level > 4)
      return;

    //1. divide up the domain into 8 and create 8 children nodes. put in the min, max, index. put the pointers of each children in the current_node->m_children 
    ccDOTIMES(z, 2) {
      ccDOTIMES(y, 2) {
        ccDOTIMES(x, 2) {
          uINT8 linear_index = z*4 + y*2 + x;
          sUBLK_TREE_NODE new_child_node;
          new_child_node.m_index = m_nodes.size();
          new_child_node.m_level = current_node.m_level + 1;

          new_child_node.m_min[0] = current_node.m_min[0] +     x * 0.5 * ( current_node.m_max[0] - current_node.m_min[0] );
          new_child_node.m_min[1] = current_node.m_min[1] +     y * 0.5 * ( current_node.m_max[1] - current_node.m_min[1] );
          new_child_node.m_min[2] = current_node.m_min[2] +     z * 0.5 * ( current_node.m_max[2] - current_node.m_min[2] );

          new_child_node.m_max[0] = current_node.m_max[0] - (1-x) * 0.5 * ( current_node.m_max[0] - current_node.m_min[0] );
          new_child_node.m_max[1] = current_node.m_max[1] - (1-y) * 0.5 * ( current_node.m_max[1] - current_node.m_min[1] );
          new_child_node.m_max[2] = current_node.m_max[2] - (1-z) * 0.5 * ( current_node.m_max[2] - current_node.m_min[2] );

          m_nodes.push_back(new_child_node);
          current_node.m_children[linear_index] = m_nodes.back().m_index;
          m_nodes.at(current_node.m_index).m_children[linear_index] = m_nodes.back().m_index;
        }
      }
    }

    //2. run through all ublk_ids in the vector current_node->m_ublks and copy them into the child node's m_ublks. At the end resize the current_node->m_ublks to zero.
    ccDOTIMES(i, current_node.m_ublks.size()) {
      UBLK ublk = current_node.ublk_from_index(i);
      dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
      dFLOAT ivdx = 1.0 / voxel_dx;
      dFLOAT ublk_distance = 2.0 * voxel_dx;

      STP_COORD ublk_min[3];
      STP_COORD ublk_max[3];
      ccDOTIMES(axis, 3) {
        ublk_min[axis] = ublk->location(axis);
        ublk_max[axis] = ublk->location(axis) + ublk_distance;
      }

      ccDOTIMES(child, 8) {
        sUBLK_TREE_NODE child_node = m_nodes.at(current_node.m_children[child]);
        STP_COORD child_min[3];
        STP_COORD child_max[3];
        ccDOTIMES(axis, 3) {
          child_min[axis] = child_node.m_min[axis];
          child_max[axis] = child_node.m_max[axis];
        }

        bool is_inside = check_if_box_is_inside_child_domain(ublk_min, ublk_max, child_min, child_max);
        
        if (is_inside)
          m_nodes.at(child_node.m_index).m_ublks.push_back(ublk->id());
      }
    }

    //resize the current nodes boxes vector to zero after copying them into the children nodes' vectors
    std::vector<STP_UBLK_ID>().swap(current_node.m_ublks); 
    std::vector<STP_UBLK_ID>().swap(m_nodes.at(current_node.m_index).m_ublks); 

    //3. loop over each child node and call this function using the pointer to a child node
    ccDOTIMES(child, 8) {
      propogate_branch_until_leaf( m_nodes.at(current_node.m_children[child]) );
    }
  };

  UBLK find_ublk (dFLOAT loc[3]) {
    //1. call an internal function giving it a starting pointer (root_node), it returns the leaf node.
    //the leaf node will have a less than or equal to 8 box headers
    sUBLK_TREE_NODE leaf_node =  find_internal(loc, get_root_node());

    //2. search through the leaf_node->m_ublk_boxes vector to find one where the loc is inside.
    ccDOTIMES(i, leaf_node.m_ublks.size()) {
      UBLK ublk = leaf_node.ublk_from_index(i);
      dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
      dFLOAT ivdx = 1.0 / voxel_dx;
      dFLOAT ublk_distance = 2.0 * voxel_dx;

      STP_COORD ublk_min[3];
      STP_COORD ublk_max[3];
      ccDOTIMES(axis, 3) {
        ublk_min[axis] = ublk->location(axis);
        ublk_max[axis] = ublk->location(axis) + 2.0 * voxel_dx;
      }
      bool found_inside = is_point_inside_domain (loc, ublk_min, ublk_max);
      if (found_inside)
        return ublk;
    }

    return NULL;
  }

  sUBLK_TREE_NODE find_internal (dFLOAT loc[3], sUBLK_TREE_NODE current_node) {
    //0. is the size > 0, then it is the leaf node. can also check m_children
    if (current_node.m_ublks.size() > 0)
      return current_node;

    sUBLK_TREE_NODE leaf_node;
    //1. search through the child_node domains and see where the loc is 
    ccDOTIMES(child, 8) {
      sUBLK_TREE_NODE child_node = m_nodes.at(current_node.m_children[child]);
      bool found_inside = is_point_inside_domain (loc, child_node.m_min, child_node.m_max);
      if (found_inside) {
        leaf_node = find_internal(loc, child_node);
        break;
      }
    }

    return leaf_node;
  }
};

#endif

