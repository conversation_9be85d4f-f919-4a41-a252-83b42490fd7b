/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_materials.h"
#include "surfel.h"
#include "surfel_vertices.h"

VOID sPARTICLE_MATERIAL::sample_material_properties(sPARTICLE_VAR &density, sPARTICLE_VAR &kinematic_viscosity, sPARTICLE_VAR &surface_tension ) {

  if(is_massless_tracer()) {
    density = 0;
    kinematic_viscosity = 0;
    surface_tension = 0;
  } else {
    //A material realization consists of a particular viscosity, surface tension, and density consistent with the requested statistics:
    do{ density = g_random_particle_properties->random_number(m_density_mean, m_density_dist_param2, m_density_distribution);
    } while(density < 0);
    kinematic_viscosity = m_dynamic_viscosity / density;
    surface_tension = m_surface_tension;
  }
}

BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization_internal(STP_GEOM_VARIABLE surfel_normal[3], 
                                                                             dFLOAT basis[3],
                                                                             PARTICLE_VAR normal_restitution_coefficent, 
                                                                             PARTICLE_VAR transverse_restitution_coefficient, 
                                                                             sPARTICLE_VAR scattered_normal[3]) {

  if(m_scatter_angle_distribution == sPDF::CONSTANT_NUMBER || m_scatter_angle_stddev == 0.0) {
    scattered_normal[0] = surfel_normal[0];
    scattered_normal[1] = surfel_normal[1];
    scattered_normal[2] = surfel_normal[2]; 
  } else {
    //compute a random vector in the plane of the surfel that can be used to rotate the surfel normal about to get a scattered surface normal
  
    sPARTICLE_VAR theta = M_PI * (g_random_particle_properties->uniform() + 1.0) / 2.0;
    sPARTICLE_VAR cos_theta = cos(theta);
    sPARTICLE_VAR sin_theta = sin(theta);
    sPARTICLE_VAR other_basis[N_SPACE_DIMS];
    vcross(other_basis, basis, surfel_normal);
    vmul(other_basis, sin_theta);
    sPARTICLE_VAR scatter_rotation_axis[N_SPACE_DIMS];
    vscale(scatter_rotation_axis, cos_theta, basis);
    vinc(scatter_rotation_axis, other_basis); //scatter rotation axis should be a random vector in the plane of the surfel.

    //Rotate the surface normal about the scatter_rotation_axis to realize an actual surface normal (for a rough surface for example)
    sPARTICLE_VAR phi =  g_random_particle_properties->random_number(0.0, m_scatter_angle_stddev, m_scatter_angle_distribution);
    //clamp phi to be +- 90 deg (phi is the angle between the realized normal and the surfel normal)
    if(phi > M_PI / 2.0) phi = M_PI / 2.0;
    else if(phi < -M_PI / 2.0) phi = -M_PI / 2.0;

    sPARTICLE_VAR cos_phi = cos(phi);
    sPARTICLE_VAR sin_phi = sin(phi);
    sPARTICLE_VAR perp_axis[N_SPACE_DIMS];
    vcross(perp_axis, scatter_rotation_axis, surfel_normal);
    vmul(perp_axis, sin_phi);
    vscale(scattered_normal, cos_phi, surfel_normal);
    vinc(scattered_normal, perp_axis);
  }

  *normal_restitution_coefficent = g_random_particle_properties->random_number(m_normal_restitution_coefficient_mean, 
                                                                               m_normal_restitution_coefficient_stddev, 
                                                                               m_normal_restitution_coefficient_distribution);
  *transverse_restitution_coefficient = g_random_particle_properties->random_number(m_tangential_restitution_coefficient_mean, 
                                                                                    m_tangential_restitution_coefficient_stddev, 
                                                                                    m_tangential_restitution_coefficient_distribution);
  return(FALSE);
}

template <typename SURFEL_TYPE>
BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization(SURFEL_TYPE surfel, PARTICLE_VAR normal_restitution_coefficent, PARTICLE_VAR transverse_restitution_coefficient, sPARTICLE_VAR scattered_normal[3]) {

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;
  asINT32 n_vertices = surfel->p_data()->n_vertices;
#else
  asINT32 first_vertex_index = surfel->stencil()->first_vertex_index;
  asINT32 n_vertices = surfel->stencil()->n_vertices;
#endif
  SIM_VERTEX vertex = surfel_vertex_from_global_index(first_vertex_index);
  
  sPARTICLE_VAR basis[N_SPACE_DIMS];
  vsub(basis, surfel->centroid, vertex->coord);
  vunitize(basis);
  
  return generate_surface_realization_internal((STP_GEOM_VARIABLE*) surfel->normal, 
                                               basis,
                                               normal_restitution_coefficent, 
                                               transverse_restitution_coefficient, 
                                               scattered_normal);


}

//This template specailization of the above is for bsurfels and should be eliminated once bsurfels' vertices are available.
template <>
BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization(sBSURFEL *surfel, PARTICLE_VAR normal_restitution_coefficent, PARTICLE_VAR transverse_restitution_coefficient, sPARTICLE_VAR scattered_normal[3]) {
  //The issue is that a basis vector in the plane of the bsurfel is needed to rotate the normal used for reflection calculations according to the scatter
  //angle parameters.  For other surfels this is obtained by subtracting a vertex from the centroid but for bsurfels, the verteices are not available 
  //and or not transformed to the current configuration.  

  //The alternative used here is to try the three cartesian basis vectors and pick the first one that is not very parallel to the surfel normal.
  sPARTICLE_VAR basis[N_SPACE_DIMS];
  sPARTICLE_VAR projection_dist;
  ccDOTIMES(i, N_SPACE_DIMS) {
    vzero(basis);
    basis[i] = 1.0;
    projection_dist = vdot(surfel->normal(), basis);
    if(projection_dist > 0.01)
      break;
  }

  vmul(basis, -projection_dist);
  vinc(basis, surfel->normal());
  vunitize(basis);

  STP_GEOM_VARIABLE surfel_normal[3];
  vcopy(surfel_normal, surfel->normal());  //bsurfels have double precision normals, other surfels use single.
  return generate_surface_realization_internal(surfel_normal, 
                                        basis,
                                        normal_restitution_coefficent, 
                                        transverse_restitution_coefficient, 
                                        scattered_normal);
}


template BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization<SURFEL>(SURFEL surfel, PARTICLE_VAR normal_restitution_coefficent, PARTICLE_VAR transverse_restitution_coefficient, sPARTICLE_VAR normal[3]);
template BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization<SAMPLING_SURFEL>(SAMPLING_SURFEL surfel, PARTICLE_VAR normal_restitution_coefficent, PARTICLE_VAR transverse_restitution_coefficient, sPARTICLE_VAR normal[3]);
//template BOOLEAN sSURFACE_INTERACTION_PROPERTY::generate_surface_realization<BSURFEL>(BSURFEL bsurfel, PARTICLE_VAR normal_restitution_coefficent, PARTICLE_VAR transverse_restitution_coefficient, sPARTICLE_VAR normal[3]);
