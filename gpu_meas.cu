/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "gpu_meas.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include "helper_cuda.h"
#include "gpu_globals.hcu"
#include "gpu_ptr.h"
#include "gpu_meas_builder.h"
#include "ublk.h"
#include PHYSICS_H

/** @ingroup GpuMeas
  @{
*/

struct sDEVICE_REDUCE_METADATA
{
  constexpr static size_t N_THREADS = 512;
  uINT16 sort_idx;
  uINT16 count;

  __HOST__DEVICE__ sDEVICE_REDUCE_METADATA() {
    // setting it to max is important, as it will trigger an assertion
    // if anything goes wrong.
    sort_idx = ~static_cast<uINT16>(0);
    count = 0;
  }

};

void __global__ voxel_based_meas_window_copy_kernel(asINT32 window_index, 
                                                    const MEAS_CELL_VAR * device_meas_cells, 
                                                    MEAS_CELL_VAR * device_copy_buffer, 
                                                    int n_device_meas_cells, 
                                                    int n_variables, 
                                                    sMEAS_UPDATE_TIME current_update_time)
{
  size_t idx = blockIdx.x * blockDim.x + threadIdx.x;
  size_t offset = gridDim.x * blockDim.x;
  size_t n_meas_cell_vars = n_device_meas_cells * n_variables;

  if (idx == 0) {
    auto& window_collection = get_meas_window_coll_ref();
    sON_DEVICE_WINDOW* odw = window_collection[window_index];
    odw->current_update_time = current_update_time;
  }

  for(size_t i=idx; i<n_meas_cell_vars; i += offset) {
    size_t var_i = i / n_device_meas_cells;
    size_t mc_i = i % n_device_meas_cells;
    size_t output_idx = mc_i * n_variables + var_i;
    device_copy_buffer[ output_idx ] = device_meas_cells[ i ];
  }
}

void __global__ reduction_meas_window_copy_kernel(asINT32 window_index,
                                                  const MEAS_CELL_VAR * device_meas_cells, 
                                                  MEAS_CELL_VAR * reduction_buffer, 
                                                  const sDEVICE_REDUCE_METADATA * metadata,
                                                  const STP_MEAS_CELL_INDEX * reduction_idxs,
                                                  int n_device_meas_cells, 
                                                  int n_variables,
                                                  sMEAS_UPDATE_TIME current_update_time)
{
  constexpr size_t N_THREADS = sDEVICE_REDUCE_METADATA::N_THREADS;
  constexpr size_t N_DEVICE_MEAS_CELLS = N_THREADS;
  cassert(N_THREADS == blockDim.x);

  __shared__ MEAS_CELL_VAR data[N_DEVICE_MEAS_CELLS];

  if (threadIdx.x == 0) {
    auto& window_collection = get_meas_window_coll_ref();
    sON_DEVICE_WINDOW* odw = window_collection[window_index];
    odw->current_update_time = current_update_time;
  }

  sDEVICE_REDUCE_METADATA my_metadata;
  MEAS_CELL_VAR my_data;

  size_t meas_cell_trips = (n_device_meas_cells / N_DEVICE_MEAS_CELLS) + (n_device_meas_cells % N_DEVICE_MEAS_CELLS > 0);

  ccDOTIMES(trip, meas_cell_trips) {
    size_t meas_cell_start = trip * N_DEVICE_MEAS_CELLS;
    size_t my_meas_cell_idx = meas_cell_start + threadIdx.x;
    size_t host_meas_cell;

    if (my_meas_cell_idx < n_device_meas_cells) {
      my_metadata = metadata[my_meas_cell_idx];
      host_meas_cell = reduction_idxs[my_meas_cell_idx];
    }
    else {
      my_metadata = sDEVICE_REDUCE_METADATA();
      host_meas_cell = ~static_cast<size_t>(0);
    }

    size_t my_sort_idx = my_metadata.sort_idx;
    size_t count = my_metadata.count;

    for(size_t var_idx = 0; var_idx < n_variables; var_idx++) {
      size_t my_data_idx = var_idx*n_device_meas_cells + my_meas_cell_idx;

      if ( my_meas_cell_idx < n_device_meas_cells ) {
        my_data = device_meas_cells[my_data_idx];
      }

      // Now sort the data
      if ( my_sort_idx < N_THREADS ) {
        data[my_sort_idx] = my_data;
      }

      GPU_SYNC_THREADS();

      my_data = 0.0;

      // Leaders sum up and output the reduced data
      for (size_t i=0; i<count; i++) {
        cassert(threadIdx.x + i < N_DEVICE_MEAS_CELLS);
        my_data += data[threadIdx.x + i];
      }

      if ( count > 0 ) {
        cassert(host_meas_cell != ~static_cast<size_t>(0));
        size_t output_meas_cell_var_idx = n_variables*host_meas_cell + var_idx;
        reduction_buffer[output_meas_cell_var_idx] += my_data;
      }

      GPU_SYNC_THREADS();
    }
  }
}

void __global__ update_on_device_windows_kernel()
{
  auto& timescale = get_timescale_ref();
  auto& window_collection = get_meas_window_coll_ref();
  sON_DEVICE_WINDOW* odw = window_collection[threadIdx.x];
  odw->m_one_over_n_timesteps_since_clear = 1.0 / (timescale.m_base_time - odw->current_update_time.clear_time + 1);

}

namespace GPU {
__host__ void update_on_device_windows()
{
  size_t n_threads = g_meas_windows.n_meas_windows();
  if (n_threads) {
    update_on_device_windows_kernel<<<1,n_threads, GPU::NO_DYN_SHMEM, GPU::g_stream>>>();
  }
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
}

}

bool sMEAS_WINDOW::check_reduction_metadata()
{
  std::vector<sDEVICE_REDUCE_METADATA> tmp(m_n_device_meas_cells);

  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
  GPU::copy_d2h_async(tmp.data(), m_device_reduction_metadata.add_const(), m_n_device_meas_cells, GPU::g_stream);
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );

  for (size_t i=0; i<m_n_device_meas_cells; i++) {
    sDEVICE_REDUCE_METADATA m = tmp[i];
    if ((tmp[i].sort_idx != m_reduction_metadata[i].sort_idx) ||
        (tmp[i].count != m_reduction_metadata[i].count)) {
      return false;
    }
  }
  return true;
}

void sMEAS_WINDOW::reduce_and_transpose_stationary_meas_cells_on_gpu()
{
  if ( m_device_reduction_idxs == nullptr ) {
    asINT32 n_vars = contains_std_dev_vars ? n_variables+1 : n_variables;
    voxel_based_meas_window_copy_kernel<<<1024,1024, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(index, m_device_meas_cells.get(), m_device_reduction_buffer.get(), m_n_device_meas_cells, n_vars, current_update_time);
  }
  else {
    // Even though this says "async", this will not overlap with the following kernel because they are on the same stream
    GPU::zero_async(m_device_reduction_buffer, stationary_meas_cell_array_size(), GPU::g_stream);

    reduction_meas_window_copy_kernel<<<1,sDEVICE_REDUCE_METADATA::N_THREADS, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(index, m_device_meas_cells.get(), m_device_reduction_buffer.get(), m_device_reduction_metadata.get(), m_device_reduction_idxs.get(), m_n_device_meas_cells, n_variables, current_update_time);
  }

  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
}

// This is run by the comm thread
void sMEAS_WINDOW::copy_stationary_meas_cells_from_gpu()
{
  GPU::copy_d2h_async<MEAS_CELL_VAR>(_meas_cells, m_device_reduction_buffer, stationary_meas_cell_array_size(), GPU::g_comm_thread_stream);
  checkCudaErrors( cudaStreamSynchronize(GPU::g_comm_thread_stream) );
}

void sMEAS_WINDOW::clear_stationary_meas_cells_on_gpu()
{
  GPU::zero_async(m_device_meas_cells, device_meas_cell_array_size_no_std_dev_cells(), GPU::g_stream);

  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
}


std::unique_ptr<sON_DEVICE_WINDOW> sMEAS_WINDOW::allocate_on_device_window()
{
  m_device_meas_cells = GPU::malloc<MEAS_CELL_VAR>(device_meas_cell_array_size(),"meas cells");

  GPU::zero(m_device_meas_cells, device_meas_cell_array_size());

  if ( m_megashob_std_cells ) {
    GPU::Ptr<sSTD_CELL_VAR> device_std_cells = (m_device_meas_cells + m_n_device_meas_cells * n_variables).reinterpret_ptr_cast<sSTD_CELL_VAR>();
    GPU::copy_h2d(device_std_cells, m_megashob_std_cells, m_n_device_meas_cells);
  }

  if ( m_reduction_idxs ) {
    m_device_reduction_idxs = GPU::malloc<STP_MEAS_CELL_INDEX>(device_reduction_idxs_array_size(),"meas reduction");
    GPU::copy_h2d(m_device_reduction_idxs, m_reduction_idxs, device_reduction_idxs_array_size());

    m_device_reduction_metadata = GPU::malloc<sDEVICE_REDUCE_METADATA>(device_reduction_metadata_array_size(),"meas reduction");
    GPU::copy_h2d(m_device_reduction_metadata, m_reduction_metadata, device_reduction_metadata_array_size());
  }

  m_device_reduction_buffer = GPU::malloc<MEAS_CELL_VAR>(stationary_meas_cell_array_size(),"meas reduction");

  delete [] _meas_cells;

  _meas_cells = GPU::malloc_host_page_locked<MEAS_CELL_VAR>(stationary_meas_cell_array_size());

  auto odw = std::make_unique<sON_DEVICE_WINDOW>();
  odw->is_composite = this->is_composite;
  odw->is_development = this->is_development;
  odw->n_variables = n_variables;
  odw->n_device_meas_cells = m_n_device_meas_cells;
  odw->device_meas_cells = m_device_meas_cells.get();
  odw->per_voxel_meas_p = this->per_voxel_meas_p;
  odw->contains_std_dev_vars = this->contains_std_dev_vars;
  odw->calc_lambda2 = this->calc_lambda2;
  odw->meas_cell_scale = this->meas_cell_scale;
  odw->solver_mask = this->solver_mask;
  odw->calc_htc_for_adb_walls = this->calc_htc_for_adb_walls;
  odw->min_pressure = this->min_pressure;
  odw->max_pressure = this->max_pressure;
  odw->current_update_time = this->current_update_time;
  odw->m_one_over_n_timesteps_since_clear = 1.0 / (g_timescale.m_base_time - current_update_time.clear_time + 1);

  ccDOTIMES(i,3) {
    odw->reference_point[i] = this->reference_point[i];
  }

  ccDOTIMES(i,3) {
    ccDOTIMES(j,3) {
      odw->start_pt[i][j] = this->start_pt[i][j];
      odw->dev_line_orientation[i][j] = this->dev_line_orientation[i][j];
    }
  }

  odw->n_std_dev_vars = 0;
  ccDOTIMES(var_index, this->n_variables) {
    switch(this->var_types[var_index]) {
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
        odw->std_dev_vars[odw->n_std_dev_vars] = var_index;
        odw->n_std_dev_vars++;
        if (odw->n_std_dev_vars > GPU::N_STD_DEV_VARIABLES) {
          msg_internal_error("Not enough room for all standard deviation variables");
        }
        break;
      default:
        break; // do nothing
    }
  }

  odw->one_over_dev_win_segment = this->one_over_dev_win_segment;

  auto vars = GPU::malloc<SRI_VARIABLE_TYPE>(odw->n_variables, "meas variables");

  GPU::copy_h2d(vars, this->var_types, odw->n_variables);
  odw->var_types = vars.get();

  return odw;
}


void sMBLK_MEAS_CELL_PTR::set_voxel_mask(sINT32 child_ublk, VOXEL_MASK_8 child_voxel_mask)
{
  m_meas_voxel_mask |= MBLK_VOXEL_MASK{ MBLK_VOXEL_MASK::TYPE(child_voxel_mask.get()) << 8*child_ublk };
}

void sMBLK_MEAS_CELL_PTR::set_max_meas_cell() 
{
  sINT8 m = -1;
  ccDOTIMES(i, N_VOXELS_8) {
    if ( m_child_ublk_to_meas_cell[i] > m ) {
      m = m_child_ublk_to_meas_cell[i];
    }
  }
  m_max_meas_cell = m;
}

void sMBLK_MEAS_CELL_PTR::sort_meas_cell_ptrs_for_reduction() 
{
  cassert(m_max_meas_cell >= 0);
  MEAS_CELL_PTR tmp[N_VOXELS_8];
  ccDOTIMES(i, N_VOXELS_8) {
    if ( m_child_ublk_to_meas_cell[i] >= 0 ) {
      tmp[ m_child_ublk_to_meas_cell[i] ] = m_ptrs[i];
    }
  }

  ccDOTIMES(i, N_VOXELS_8) {
    if (tmp[i].is_null()) {
      //Preserve tagged bits while setting meas_cell_var* to null
      m_ptrs[i].set_variables(nullptr);
    } else {
      m_ptrs[i] = tmp[i];
    }
  }

}

void sMSFL_MEAS_CELL_PTR::set_max_meas_cell() 
{
  sINT8 m = -1;
  ccDOTIMES(i, N_SFLS_PER_MSFL) {
    if ( m_child_sfl_to_meas_cell[i] > m ) {
      m = m_child_sfl_to_meas_cell[i];
    }
  }
  m_max_meas_cell = m;
}

void sMSFL_MEAS_CELL_PTR::sort_meas_cell_ptrs_for_reduction() 
{
  cassert(m_max_meas_cell >= 0);
  std::array<MEAS_CELL_PTR,N_SFLS_PER_MSFL> tmp{};
  ccDOTIMES(i, N_SFLS_PER_MSFL) {
    if ( m_child_sfl_to_meas_cell[i] >= 0 ) {
      tmp.at( m_child_sfl_to_meas_cell[i] ) = m_ptrs[i];
    }
  }

  ccDOTIMES(i, N_SFLS_PER_MSFL) {
    if (tmp[i].is_null()) {
      //Preserve tagged bits while setting meas_cell_var* to null
      m_ptrs[i].set_variables(nullptr);
    } else {
      m_ptrs[i] = tmp[i];
    }
  }
}

//==========================
// cDEVICE_MEAS_CELL_COUNTER
//==========================

cDEVICE_MEAS_CELL_COUNTER::~cDEVICE_MEAS_CELL_COUNTER() {};

void cDEVICE_MEAS_CELL_COUNTER::reset_local_counts() 
{
  std::fill(m_local_host_meas_cells.begin(),
            m_local_host_meas_cells.end(),
            nullptr);
  m_n_local_meas_cells = 0;
}

std::pair<sINT8, bool> cDEVICE_MEAS_CELL_COUNTER::get_local_index(MEAS_CELL_VAR* mcv) 
{

  bool is_new=false;
  sINT8 local_idx = -1;
  ccDOTIMES(i, m_n_local_meas_cells) {
    if ( m_local_host_meas_cells[i] == mcv ) {
      local_idx = i;
      break;
    }
  }

  if (local_idx == -1) {
    is_new = true;
    m_local_host_meas_cells.at(m_n_local_meas_cells) = mcv;
    local_idx = m_n_local_meas_cells;
    m_n_local_meas_cells++;
  }

  return std::make_pair(local_idx, is_new);
}

//=======================================
// cDEVICE_MEAS_CELL_COUNTER_REPEATABLE 
//=======================================


STP_MEAS_CELL_INDEX cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::n_device_meas_cells() const
{
  return m_host_window_meas_cells.size();
}

std::pair<STP_MEAS_CELL_INDEX, sINT8>
cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::push_host_meas_cells(MEAS_CELL_VAR* mcv, uINT32 count)
{
  STP_MEAS_CELL_INDEX h_mci = m_parent->meas_cell_index(mcv);
  auto [local_idx, is_new] = get_local_index(mcv);

  STP_MEAS_CELL_INDEX d_mci = -1;

  if (is_new) {
    d_mci = n_device_meas_cells();
    ccDOTIMES(i, count) {
      m_host_window_meas_cells.push_back(h_mci + i);
    }
    m_local_device_meas_cells.at(local_idx) = d_mci;
  } else {
    d_mci = m_local_device_meas_cells.at(local_idx);
  }

  return std::make_pair(d_mci, local_idx);
}

bool cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::needs_extra_reduction() const
{
  cassert(n_device_meas_cells() >= m_parent->n_stationary_meas_cells);

  if ( n_device_meas_cells() == m_parent->n_stationary_meas_cells ) {
    ccDOTIMES(i, n_device_meas_cells()) {
      if ( m_host_window_meas_cells[i] != i ) {
        return true;
      }
    }
    return false;
  }
  else {
    return true;
  }
}

STP_MEAS_CELL_INDEX* cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::reduction_idxs() const
{
  auto* idxs = new STP_MEAS_CELL_INDEX[m_host_window_meas_cells.size()];
  std::copy(m_host_window_meas_cells.begin(),
            m_host_window_meas_cells.end(),
            idxs);
  return idxs;
}

static uINT32 find_end_of_range(uINT32 key, uINT32 start, uINT32 end, const std::array<std::pair<uINT32, uINT32>, sDEVICE_REDUCE_METADATA::N_THREADS>& tmp)
{
  for(uINT32 i=start; i<end; i++) {
    if (tmp[i].first != key) {
      return i;
    }
  }
  return end;
}

static void compute_reduction_metadata_count(size_t n,
                                             const std::array<std::pair<uINT32, uINT32>, sDEVICE_REDUCE_METADATA::N_THREADS>& tmp,
                                             std::array<uINT32, sDEVICE_REDUCE_METADATA::N_THREADS>& reduce_count)
{
  uINT32 t=0;
  while (t < n ) {
    uINT32 key = tmp[t].first;
    uINT32 end = find_end_of_range(key, t, n, tmp);
    uINT32 n_elem = end - t;

    reduce_count[t] = n_elem;

    for (uINT32 i=t+1; i<end; i++) {
      reduce_count[i] = 0;
    }

    t = end;
  }

}

sDEVICE_REDUCE_METADATA* cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::reduction_metadata(STP_MEAS_CELL_INDEX * reduction_idxs) const
{
    auto num_dmc = n_device_meas_cells();
    auto metadata = new sDEVICE_REDUCE_METADATA[num_dmc];
    std::array<std::pair<uINT32,uINT32>, sDEVICE_REDUCE_METADATA::N_THREADS> tmp{};
    std::array<uINT32, sDEVICE_REDUCE_METADATA::N_THREADS> reduce_count{};

    size_t dmc = 0;
    while( dmc < num_dmc ) {
      size_t start = dmc;
      size_t t = 0;

      while ( t < sDEVICE_REDUCE_METADATA::N_THREADS && dmc < num_dmc ) {
        tmp[t] = std::make_pair( m_host_window_meas_cells[dmc], dmc );
        t++;
        dmc++;
      }

      std::stable_sort( &tmp[0], &tmp[t], [](const auto& a, const auto& b) { return a.first < b.first; } );

      compute_reduction_metadata_count(t, tmp, reduce_count);

      for(uINT32 i=0; i<t; i++) {
        uINT32 device_meas_cell = tmp[i].second;
        metadata[device_meas_cell].sort_idx = i;
        metadata[start + i].count = reduce_count[i];
        reduction_idxs[ start + i ] = tmp[i].first;
      }
    }
    return metadata;
}

sSTD_CELL_VAR * cDEVICE_MEAS_CELL_COUNTER_REPEATABLE::megashob_std_cells() const
{
  return nullptr;
}

//===========================================
// cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE 
//===========================================
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE(const sMEAS_WINDOW* parent, bool create_stddev) : 
  cDEVICE_MEAS_CELL_COUNTER(parent),
  m_megashob_std_cells(),
  m_create_stddev(create_stddev) 
{
  if (create_stddev) {
    m_megashob_std_cells.resize(parent->n_stationary_meas_cells);
  }
}

std::pair<STP_MEAS_CELL_INDEX,sINT8> 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::push_host_meas_cells(MEAS_CELL_VAR* mcv, uINT32 count) 
{
  STP_MEAS_CELL_INDEX h_mci = m_parent->meas_cell_index(mcv);
  auto [local_idx, is_new] = get_local_index(mcv);
  if (is_new && m_create_stddev) {
    ccDOTIMES(i, count) {
      m_megashob_std_cells.at(h_mci + i).add_shob();
    }
  }

  return {h_mci, local_idx};
}

STP_MEAS_CELL_INDEX 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::n_device_meas_cells() const
{
  return m_parent->n_stationary_meas_cells;
}

bool 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::needs_extra_reduction()  const
{ 
  return false; 
}

STP_MEAS_CELL_INDEX* 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::reduction_idxs()  const
{ 
  return nullptr; 
}

sDEVICE_REDUCE_METADATA* 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::reduction_metadata(STP_MEAS_CELL_INDEX*)  const
{ 
  return nullptr; 
}

sSTD_CELL_VAR * 
cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE::megashob_std_cells() const
{
  if (m_create_stddev) {
    sSTD_CELL_VAR * std_cells = new sSTD_CELL_VAR[m_megashob_std_cells.size()];
    std::copy(m_megashob_std_cells.begin(), m_megashob_std_cells.end(), std_cells);
    return std_cells;
  } else {
    return nullptr;
  }
}

//============================
// cDEVICE_MEAS_WINDOW_BUILDER
//============================

cDEVICE_MEAS_WINDOW_BUILDER::cDEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent) : m_parent(parent) {} 
cDEVICE_MEAS_WINDOW_BUILDER::~cDEVICE_MEAS_WINDOW_BUILDER() {};

//==================================
// cFLUID_DEVICE_MEAS_WINDOW_BUILDER
//==================================

cFLUID_DEVICE_MEAS_WINDOW_BUILDER::cFLUID_DEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent, std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER>&& c) : cDEVICE_MEAS_WINDOW_BUILDER(parent),
  m_counter(std::move(c)),
  m_mcp{parent->index},
  m_mblk_type{eMBLK_TYPE::INVALID}
{}

int cFLUID_DEVICE_MEAS_WINDOW_BUILDER::add_ublk_to_device_meas_cells(uINT8 child_ublk, 
                                                                     SCALE scale, 
                                                                     sUBLK_MEAS_CELL_PTR * const ublk_meas_cell_ptr, 
                                                                     sUBLK_MEAS_CELL_PTR const * const end,
                                                                     [[maybe_unused]] bool mshob_is_moving)
{
  cassert(child_ublk < N_VOXELS_8);
  if (m_mblk_type == eMBLK_TYPE::INVALID) {
    m_counter->reset_local_counts();
    if (m_parent->is_per_voxel_for_scale(scale)) {
      m_mblk_type = eMBLK_TYPE::VOXEL;
    } else {
      m_mblk_type = eMBLK_TYPE::UBLK;
    }
  }

  if (m_mblk_type == eMBLK_TYPE::VOXEL) {
    add_voxel_device_meas_cell(child_ublk, ublk_meas_cell_ptr);
  } else {
    add_ublk_device_meas_cell(child_ublk, ublk_meas_cell_ptr);
  }

  return 1;
}

void cFLUID_DEVICE_MEAS_WINDOW_BUILDER::append_mblk_meas_cells(std::vector<sMBLK_MEAS_CELL_PTR>& mcps)
{
  auto& mcp = mcps.emplace_back(m_mcp); // copy into the vector
  mcp.set_max_meas_cell();
  m_mcp = sMBLK_MEAS_CELL_PTR(m_parent->index);
  m_mblk_type = eMBLK_TYPE::INVALID;
}

static void compute_host_side_info(sMEAS_WINDOW* window, const cDEVICE_MEAS_CELL_COUNTER& counter) 
{
  window->m_n_device_meas_cells = counter.n_device_meas_cells();

  if( counter.n_device_meas_cells() == 0 ) //TODO: review with Dalon (issue with Sampling Surfels)
    return;

  if (counter.needs_extra_reduction()) {
    window->m_reduction_idxs = counter.reduction_idxs();
    window->m_reduction_metadata = counter.reduction_metadata(window->m_reduction_idxs);
  }
  else {
    window->m_reduction_idxs = nullptr;
    window->m_reduction_metadata = nullptr;
  }

  if (window->contains_std_dev_vars) {
    window->m_megashob_std_cells = counter.megashob_std_cells();
  } else {
    window->m_megashob_std_cells = nullptr;
  }
}

void cFLUID_DEVICE_MEAS_WINDOW_BUILDER::compute_host_side_info()
{
  ::compute_host_side_info(m_parent, *m_counter);
}

void cFLUID_DEVICE_MEAS_WINDOW_BUILDER::add_voxel_device_meas_cell(uINT8 child_ublk, 
                                                                   UBLK_MEAS_CELL_PTR ublk_meas_cell_ptr)
{ 
  cassert(child_ublk < N_VOXELS_8);
  VOXEL_MASK_8 mask = ublk_meas_cell_ptr->voxel_mask();
  m_mcp.set_voxel_mask(child_ublk, mask);

  MEAS_CELL_VAR * meas_cell = ublk_meas_cell_ptr->variables();

  auto [d_mci, d_local_mci] = m_counter->push_host_meas_cells(meas_cell, mask.count());

  m_mcp.set_meas_cell_idx(child_ublk, d_mci);
  m_mcp.set_child_ublk_to_meas_cell(child_ublk, d_local_mci);

  if (ublk_meas_cell_ptr->should_rotate_vector_to_grf()) {
    m_mcp.mark_should_rotate_vector_to_grf(child_ublk);
  }

}

static void add_ublk_to_device_meas_cell(uINT8 child_ublk, sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr, cDEVICE_MEAS_CELL_COUNTER& counter, sMBLK_MEAS_CELL_PTR& mcp)
{
  mcp.set_voxel_mask(child_ublk, ublk_meas_cell_ptr->voxel_mask());

  MEAS_CELL_VAR * meas_cell = ublk_meas_cell_ptr->variables();

  auto [d_mci, d_local_mci] = counter.push_host_meas_cells(meas_cell,1);

  mcp.set_meas_cell_idx(child_ublk, d_mci);
  mcp.set_child_ublk_to_meas_cell(child_ublk, d_local_mci);

  if (ublk_meas_cell_ptr->should_rotate_vector_to_grf()) {
    mcp.mark_should_rotate_vector_to_grf(child_ublk);
  }

}

void cFLUID_DEVICE_MEAS_WINDOW_BUILDER::add_ublk_device_meas_cell(uINT8 child_ublk, UBLK_MEAS_CELL_PTR ublk_meas_cell_ptr)
{ 
  cassert(child_ublk < N_VOXELS_8);
  ::add_ublk_to_device_meas_cell(child_ublk, ublk_meas_cell_ptr, *m_counter, m_mcp);
}

//==================================
// cSURFACE_DEVICE_MEAS_WINDOW_BUILDER
//==================================

cSURFACE_DEVICE_MEAS_WINDOW_BUILDER::cSURFACE_DEVICE_MEAS_WINDOW_BUILDER(sMEAS_WINDOW* parent, std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER>&& c) : cDEVICE_MEAS_WINDOW_BUILDER(parent),
  m_counter(std::move(c)),
  m_mcp{parent->index}
{}

static void add_sfl_to_device_meas_cell(uINT8 child_sfl, sSURFEL_MEAS_CELL_PTR *sfl_meas_cell_ptr, cDEVICE_MEAS_CELL_COUNTER& counter, sMSFL_MEAS_CELL_PTR& mcp)
{
  MEAS_CELL_VAR * meas_cell = sfl_meas_cell_ptr->variables();

  auto [d_mci, d_local_mci] = counter.push_host_meas_cells(meas_cell,1);

  mcp.set_meas_cell_idx(child_sfl, d_mci);
  mcp.set_child_sfl_to_meas_cell(child_sfl, d_local_mci);

  if (sfl_meas_cell_ptr->should_rotate_vector_to_grf()) {
    mcp.mark_should_rotate_vector_to_grf(child_sfl);
  }

}

int cSURFACE_DEVICE_MEAS_WINDOW_BUILDER::add_sfl_to_device_meas_cells(uINT8 child_sfl, sSURFEL_MEAS_CELL_PTR *const sfl_meas_cell_ptr, sSURFEL_MEAS_CELL_PTR const * const end, [[maybe_unused]] bool mshob_is_moving)
{
  cassert(child_sfl < N_SFLS_PER_MSFL);

  ::add_sfl_to_device_meas_cell(child_sfl, sfl_meas_cell_ptr, *m_counter, m_mcp);

  return 1;
}

void cSURFACE_DEVICE_MEAS_WINDOW_BUILDER::append_msfl_meas_cells(std::vector<sMSFL_MEAS_CELL_PTR>& mcps)
{
  auto& mcp = mcps.emplace_back(m_mcp);
  mcp.set_max_meas_cell();
  m_mcp = sMSFL_MEAS_CELL_PTR(m_parent->index);
  m_counter->reset_local_counts();
}

void cSURFACE_DEVICE_MEAS_WINDOW_BUILDER::compute_host_side_info()
{
  ::compute_host_side_info(m_parent, *m_counter);
}

//=========================================
// cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER
//=========================================

cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER::cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER(sMEAS_WINDOW* parent) : cSURFACE_DEVICE_MEAS_WINDOW_BUILDER(parent, std::make_unique<cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE>(parent, false)) {}

//=========================================
// cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER
//=========================================

cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER::cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER(sMEAS_WINDOW* parent) : cFLUID_DEVICE_MEAS_WINDOW_BUILDER(parent, std::make_unique<cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE>(parent, false)) {}

//===========================================
// cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER
//===========================================

cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER::cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER(sMEAS_WINDOW* parent) : 
  cDEVICE_MEAS_WINDOW_BUILDER(parent), 
  m_msfl_is_moving(eMSFL_IS_MOVING::INVALID),
  m_mcps{ parent->index, parent->index, parent->index },
  m_counters{ {parent, false}, {parent,false}, {parent,false} },
  m_num_mcps{0}
{}


int cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER::add_sfl_to_device_meas_cells(uINT8 child_sfl, sSURFEL_MEAS_CELL_PTR *const sfl_meas_cell_ptr, sSURFEL_MEAS_CELL_PTR const * const end, bool mshob_is_moving)
{
  if (m_msfl_is_moving == eMSFL_IS_MOVING::INVALID) {
    ccDOTIMES(axis, 3) {
      m_counters[axis].reset_local_counts();
    }
    m_num_mcps = 0;

    if (mshob_is_moving) {
      m_msfl_is_moving = eMSFL_IS_MOVING::YES;
    } else {
      m_msfl_is_moving = eMSFL_IS_MOVING::NO;
    }
  }

  sSURFEL_MEAS_CELL_PTR * smcp = sfl_meas_cell_ptr;
  size_t axis = 0;
  while ( smcp != end && smcp->window_index() == m_parent->index ) {
    assert(axis < 3);
    if (m_msfl_is_moving == eMSFL_IS_MOVING::NO) {
      ::add_sfl_to_device_meas_cell(child_sfl, smcp, m_counters[axis], m_mcps[axis]);
    } else {
      // For moving surfels, we can't precompute the reduction, so just set the meas cell pointer
      // so it has the correct information
      // Each child sfl has it's own meas cell ptr, so map them 1-to-1
      m_mcps[axis].set_meas_cell_ptr(child_sfl, *smcp);
      m_mcps[axis].set_child_sfl_to_meas_cell(child_sfl, child_sfl);

    }

    ++axis;
    ++smcp;
  }

  m_num_mcps = max(m_num_mcps, axis);

  return axis;
}

void cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER::append_msfl_meas_cells(std::vector<sMSFL_MEAS_CELL_PTR>& mcps)
{
  if (m_msfl_is_moving == eMSFL_IS_MOVING::NO) {
    ccDOTIMES(axis, m_num_mcps) {
      auto& mcp = mcps.emplace_back(m_mcps[axis]);
      mcp.set_max_meas_cell();
      m_mcps[axis] = sMSFL_MEAS_CELL_PTR( m_parent->index );
    }
  } else {
    ccDOTIMES(axis, m_num_mcps) {
      mcps.emplace_back(m_mcps[axis]);
    }

    ccDOTIMES(axis,3) {
      m_mcps[axis] = sMSFL_MEAS_CELL_PTR( m_parent->index );
    }
  }

  m_msfl_is_moving = eMSFL_IS_MOVING::INVALID;
}

void cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER::compute_host_side_info()
{
  ::compute_host_side_info(m_parent, m_counters[0]);
}

//===========================================
// cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER
//===========================================

cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER::cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER(sMEAS_WINDOW* parent) : 
  cDEVICE_MEAS_WINDOW_BUILDER(parent), 
  m_mblk_is_moving(eMBLK_IS_MOVING::INVALID)
{}

int cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER::add_ublk_to_device_meas_cells(uINT8 child_ublk, SCALE scale, sUBLK_MEAS_CELL_PTR * const ublk_meas_cell_ptr, sUBLK_MEAS_CELL_PTR const * const end, bool mshob_is_moving)
{
  if (m_mblk_is_moving == eMBLK_IS_MOVING::INVALID) {
    if (mshob_is_moving) {
      m_mblk_is_moving = eMBLK_IS_MOVING::YES;
    } else {
      m_mblk_is_moving = eMBLK_IS_MOVING::NO;
    }
  }

  sUBLK_MEAS_CELL_PTR * umcp = ublk_meas_cell_ptr;
  STP_MEAS_WINDOW_INDEX first_index = umcp->window_index();
  while (umcp != end && umcp->window_index() == first_index) {
    m_ublk_mcps.emplace_back(child_ublk, umcp);
    umcp++;
  }

  return umcp - ublk_meas_cell_ptr;
}

void cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER::append_mblk_meas_cells(std::vector<sMBLK_MEAS_CELL_PTR>& mblk_mcps)
{
  if (m_mblk_is_moving == eMBLK_IS_MOVING::NO) {
    // Puts all the mcps that point to the same location together
    std::sort(m_ublk_mcps.begin(), m_ublk_mcps.end(), 
              [](const auto& a, const auto&b) 
              {
                MEAS_CELL_VAR* amcv = a.second->variables();
                MEAS_CELL_VAR* bmcv = b.second->variables();
                if (amcv < bmcv) {
                  return true;
                } else if (amcv == bmcv) {
                  return a.first < b.first;
                } else {
                  return false;
                }
              });

    auto cur = m_ublk_mcps.begin();
    const auto end = m_ublk_mcps.end();

    // now compress successive umcps into mmcps
    while (cur != end) {
      auto& mcp = m_mblk_mcps.emplace_back(m_parent->index);
      auto& counter = m_counters.emplace_back(m_parent, false);
      auto& child_ublk_mask = m_child_ublks_used.emplace_back();
      MEAS_CELL_VAR* target_mcv = cur->second->variables();
      while (cur != end && cur->second->variables() == target_mcv) {
        uINT8 child_ublk = cur->first;
        assert(!child_ublk_mask.test(child_ublk));
        auto [d_mci, d_local_mci] = counter.push_host_meas_cells(target_mcv, 1);
        mcp.set_voxel_mask(child_ublk, cur->second->voxel_mask());
        mcp.set_meas_cell_idx(child_ublk, d_mci);
        mcp.set_child_ublk_to_meas_cell(child_ublk, d_local_mci);
        child_ublk_mask.set(child_ublk);
        ++cur;
      }
      mcp.set_max_meas_cell();
    }

    // {
    //   // now do the second round of compression
    //   std::vector<uint8_t> merged(m_mblk_mcps.size(),false);
    //   auto cur_mcp_it = m_mblk_mcps.begin();
    //   const auto mcp_end = m_mblk_mcps.end();
    //   auto cur_counter_it = m_counters.begin();
    //   auto cur_mask_it = m_child_ublks_used.begin();
    //   auto cur_merged_it = merged.begin();
    //
    //   while (cur_mcp_it != mcp_end) {
    //     if (cur_mask_it->all()) { // skip anything already full
    //       *cur_merged_it = true;
    //     }
    //
    //     if (*cur_merged_it == true) {
    //       ++cur_mcp_it;
    //       ++cur_counter_it;
    //       ++cur_mask_it;
    //       ++cur_merged_it;
    //       continue;
    //     }
    //
    //     auto next_mcp_it = cur_mcp_it+1;
    //     auto next_mask_it = cur_mask_it+1;
    //     auto next_merged_it = cur_merged_it+1;
    //     bool merge_found = false;
    //
    //     while (next_mcp_it != mcp_end) {
    //       if ( (*cur_mask_it & *next_mask_it).none() ) {
    //         // msg_print("\tFound a merge");
    //         // merge next into cur
    //
    //         *cur_mask_it &= *next_mask_it;
    //
    //         *next_merged_it = true;
    //         merge_found = true;
    //         break;
    //       } else {
    //         ++next_mcp_it;
    //         ++next_mask_it;
    //         ++next_merged_it;
    //       }
    //     }
    //
    //     if (!merge_found) {
    //       *cur_merged_it = true; // mark the current one finished
    //     }
    //   }
    //
    // }

  } else {

    std::vector<uint8_t> ublk_mcp_packed(m_ublk_mcps.size(), false);
    size_t num_ublk_mcp_packed = 0;
    const size_t num_ublk_mcps = m_ublk_mcps.size();

    while (num_ublk_mcp_packed != num_ublk_mcps) {
      auto& mcp = m_mblk_mcps.emplace_back(m_parent->index);

      for (size_t i=0; i < num_ublk_mcps; i++) {
        if (ublk_mcp_packed[i]) {
          continue;
        }

        auto [ child_ublk, umpc ] = m_ublk_mcps[i];

        if (mcp.child_ublk_to_meas_cell(child_ublk) > -1) {
          auto axis = mcp.meas_cell_ptr(child_ublk).axis();
          auto index = mcp.meas_cell_ptr(child_ublk).index();

          if (umpc->axis() == axis && umpc->index() == index) {
            VOXEL_MASK_8 old_voxel_mask = mcp.voxel_mask(child_ublk);
            mcp.set_voxel_mask(child_ublk, old_voxel_mask | umpc->voxel_mask());
            ublk_mcp_packed[i] = true;
            num_ublk_mcp_packed++;
          } else {
            continue;
          }
        } else {
          // First time we found this child ublk
          mcp.set_meas_cell_ptr(child_ublk, *umpc);
          mcp.set_voxel_mask(child_ublk, umpc->voxel_mask());
          mcp.set_child_ublk_to_meas_cell(child_ublk, child_ublk);
          ublk_mcp_packed[i] = true;
          num_ublk_mcp_packed++;
        }
      }

      for (int child_ublk = 0; child_ublk < sMBLK::N_UBLKS; child_ublk++) {
        mcp.set_child_ublk_to_meas_cell(child_ublk, child_ublk);
      }

      mcp.set_max_meas_cell();
    }

  }

  std::copy(m_mblk_mcps.begin(), m_mblk_mcps.end(), std::back_inserter(mblk_mcps));

  m_ublk_mcps.clear();
  m_mblk_mcps.clear();
  m_counters.clear();
  m_child_ublks_used.clear();

  m_mblk_is_moving = eMBLK_IS_MOVING::INVALID;
}

void cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER::compute_host_side_info()
{
  m_parent->m_n_device_meas_cells = m_parent->n_stationary_meas_cells;
  m_parent->m_reduction_idxs = nullptr;
  m_parent->m_reduction_metadata = nullptr;
  m_parent->m_megashob_std_cells = nullptr;
}

//===================
// sMEAS_WINDOW
//===================

GPU::Ptr<MEAS_CELL_VAR> sMEAS_WINDOW::device_meas_cell_ptr(std::uintptr_t index) {
  cassert( index < m_n_device_meas_cells );
  return m_device_meas_cells + index; 
}

void sMEAS_WINDOW::resolve_device_meas_cell_ptr(SCALE ublk_scale, sMBLK_MEAS_CELL_PTR& mblk_meas_cell_ptr, bool mshob_is_moving) 
{
  if (is_development && mshob_is_moving) {
    return;
  }

  ccDOTIMES(child_ublk, N_VOXELS_8) {
    if (mblk_meas_cell_ptr.child_ublk_to_meas_cell(child_ublk) >= 0) {
      mblk_meas_cell_ptr.set_variables(child_ublk, device_meas_cell_ptr(mblk_meas_cell_ptr.meas_cell_idx(child_ublk)));
    } else {
      mblk_meas_cell_ptr.set_variables(child_ublk, nullptr);
    }
  }

  if (!is_per_voxel_for_scale(ublk_scale)) {
    mblk_meas_cell_ptr.sort_meas_cell_ptrs_for_reduction();
  }
}

void sMEAS_WINDOW::resolve_device_meas_cell_ptr(sMSFL_MEAS_CELL_PTR& msfl_meas_cell_ptr, bool mshob_is_moving) 
{
  if (is_development && mshob_is_moving) {
    return;
  }

  ccDOTIMES(child_sfl, N_SFLS_PER_MSFL) {
    if (msfl_meas_cell_ptr.child_sfl_to_meas_cell(child_sfl) >= 0) {
      auto dptr = device_meas_cell_ptr(msfl_meas_cell_ptr.meas_cell_idx(child_sfl));
      msfl_meas_cell_ptr.set_variables( child_sfl, dptr);
    } else {
      msfl_meas_cell_ptr.set_variables(child_sfl, nullptr);
    }
  }

  msfl_meas_cell_ptr.sort_meas_cell_ptrs_for_reduction();
}

/** 
  - Development & Composite windows cannot be made repeatable, it would be too
    costly in terms of memory usage.  

  - Windows with standard deviation variables cannot be made repeatable, as we
    wouldn't be able to compute the standard deviations correctly.  (Maybe with
    true meas cells in the future we could do it for ublks)
*/

std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER> sMEAS_WINDOW::create_device_window_builder()
{
  // Can't return std::make_unique due to a clang bug
  std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER> tmp;
  if ( this->is_composite ) {
    if (is_lgi_meas_window_type_surface(meas_window_type)) {
      tmp = std::make_unique<cSURFACE_DEVICE_COMPOSITE_WINDOW_BUILDER>(this);
    } else {
      tmp = std::make_unique<cFLUID_DEVICE_COMPOSITE_WINDOW_BUILDER>(this);
    }
  }

  if (tmp) {
    return tmp;
  }

  if ( this->is_development ) {
    if (is_lgi_meas_window_type_surface(meas_window_type)) {
      tmp = std::make_unique<cSURFACE_DEVICE_DEVELOPMENT_WINDOW_BUILDER>(this);
    } else {
      tmp = std::make_unique<cFLUID_DEVICE_DEVELOPMENT_WINDOW_BUILDER>(this);
    }
  }

  if (tmp) { 
    return tmp;
  }

  std::unique_ptr<cDEVICE_MEAS_CELL_COUNTER> counter;
  if (GPU::g_use_repeatable_meas == GPU::REDUCTION_MODE::REPEATABLE) {
    if (this->contains_std_dev_vars) {
      counter = std::make_unique<cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE>(this, true);
    } else {
      counter = std::make_unique<cDEVICE_MEAS_CELL_COUNTER_REPEATABLE>(this);
    }
  } else {
    counter = std::make_unique<cDEVICE_MEAS_CELL_COUNTER_NOT_REPEATABLE>(this, this->contains_std_dev_vars);
  }

  if (is_lgi_meas_window_type_surface(meas_window_type)) {
    tmp = std::make_unique<cSURFACE_DEVICE_MEAS_WINDOW_BUILDER>(this, std::move(counter));
  } else {
    tmp = std::make_unique<cFLUID_DEVICE_MEAS_WINDOW_BUILDER>(this, std::move(counter));
  }
  return std::move(tmp);
}

//==========================
// Miscellaneous functions
//==========================

void set_msfl_meas_cell_ptrs(sMSFL& mega, const std::vector<sMSFL_MEAS_CELL_PTR>& msfl_meas_cell_ptrs)
{
  size_t n_msfl_meas_cell_ptrs = msfl_meas_cell_ptrs.size();
  auto dyn_data = mega.dynamics_data();
  if ( n_msfl_meas_cell_ptrs != 0 ) {
    sMSFL_MEAS_CELL_PTR *tmp = new sMSFL_MEAS_CELL_PTR[n_msfl_meas_cell_ptrs];
    ccDOTIMES(i, n_msfl_meas_cell_ptrs) {
      tmp[i] = msfl_meas_cell_ptrs[i];
    }
    dyn_data->set_msfl_meas_cell_ptrs(n_msfl_meas_cell_ptrs, tmp);
  }
  else {
    dyn_data->set_msfl_meas_cell_ptrs(0, nullptr);
  }
}


namespace GPU {

  __host__ VOID init_device_windows(const sH2D_EXPORT& h_data) {

    std::vector<std::unique_ptr<sON_DEVICE_WINDOW>> h_odws;

    for (MEAS_WINDOW window : *(h_data.m_meas_windows)) {
      h_odws.push_back( window->allocate_on_device_window() );
    }

    std::vector<sON_DEVICE_WINDOW *> d_odws;
    for( auto& h_odw : h_odws ) {
      d_odws.emplace_back(nullptr);
      d_odws.back() = GPU::malloc<sON_DEVICE_WINDOW>(1, "window").get();
      cudaMemcpy(d_odws.back(), h_odw.get(), sizeof(sON_DEVICE_WINDOW), cudaMemcpyHostToDevice);
    }

    sON_DEVICE_WINDOW ** odw_ptr;
    odw_ptr = GPU::malloc<sON_DEVICE_WINDOW*>(d_odws.size(),"window ptrs").get();
    cudaMemcpy(odw_ptr, d_odws.data(), sizeof(sON_DEVICE_WINDOW*)*d_odws.size(), cudaMemcpyHostToDevice);
    cudaMemcpyToSymbol(GPU::g_on_device_window_array, &odw_ptr, sizeof(void*), 0, cudaMemcpyHostToDevice);
  }
}

/** @} */
