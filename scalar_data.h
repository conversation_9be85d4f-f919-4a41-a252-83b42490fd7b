/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * James F. Kelly, Exa Corporation 
 * Created April 17, 2013
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_UDS_SOLVER_DATA_H
#define _SIMENG_UDS_SOLVER_DATA_H



#include "common_sp.h"
#include "vectorization_support.h"
#include "shob.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "sim.h"
#include "meas_cell.h"
#include "debug_print_spec.h"
#include "gpu_host_init.h"
#include "mme_ckpt.h"

inline namespace SIMULATOR_NAMESPACE {
  
// forward declarations specific to this solver data-block
template <typename UBLK_TYPE_TAG> class tUBLK_UDS_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_UDS_DATA;

template<typename SFL_TYPE_TAG> struct tSURFEL_UDS_DATA;	 

typedef tUBLK_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_UDS_DATA,         *UBLK_UDS_DATA;
typedef tUBLK_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_UDS_DATA, *UBLK_UBFLOAT_UDS_DATA;

typedef tNEARBLK_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_UDS_DATA,            *NEAR_UBLK_UDS_DATA;
typedef tNEARBLK_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEAR_UBLK_UBFLOAT_UDS_DATA,    *NEAR_UBLK_UBFLOAT_UDS_DATA;

template <typename UBLK_TYPE_TAG> class tUBLK_PORE_UDS_DATA;
typedef tUBLK_PORE_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_PORE_UDS_DATA,         *UBLK_PORE_UDS_DATA;
typedef tUBLK_PORE_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_PORE_UDS_DATA, *UBLK_UBFLOAT_PORE_UDS_DATA;

#ifdef BUILD_GPU
typedef tUBLK_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_UDS_DATA,         *MBLK_UDS_DATA;
typedef tUBLK_UDS_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_UDS_DATA, *MBLK_UBFLOAT_UDS_DATA;

typedef tNEARBLK_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_UDS_DATA,            *NEAR_MBLK_UDS_DATA;
typedef tNEARBLK_UDS_DATA<MBLK_UBFLOAT_TYPE_TAG>  sNEAR_MBLK_UBFLOAT_UDS_DATA,    *NEAR_MBLK_UBFLOAT_UDS_DATA;

typedef tUBLK_PORE_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_PORE_UDS_DATA,         *MBLK_PORE_UDS_DATA;
typedef tUBLK_PORE_UDS_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_PORE_UDS_DATA, *MBLK_UBFLOAT_PORE_UDS_DATA;

typedef tSURFEL_UDS_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_UDS_DATA, *MSFL_UDS_DATA;	 
#endif

template<size_t N_VOXELS>
struct tUBLK_UDS_SEND_FIELD
{
  STP_PHYS_VARIABLE m_uds_value[N_VOXELS];
  SURFEL_STATE	   m_states_uds[N_STATES][N_VOXELS];
};

template<size_t N_VOXELS>
struct tUBLK_UDS_PDE_SEND_FIELD
{
  STP_PHYS_VARIABLE m_uds_value[N_VOXELS];
  STP_PHYS_VARIABLE m_diffusion_coef[N_VOXELS];
  STP_PHYS_VARIABLE m_grad_uds_cross[3][N_VOXELS];
};

template<size_t N_VOXELS>
struct tUBLK_UDS_MME_SEND_FIELD
{
  STP_PHYS_VARIABLE m_uds_value[N_VOXELS];  //for phase field model
}; 

template<size_t N_VOXELS>
struct sNEARBLK_UDS_SEND_FIELD
{
  // Currently dont need to send anything
};

template<size_t N_VOXELS>
struct tUBLK_PORE_UDS_SEND_FIELD
{ //for 5G large_pore  
  VOXEL_STATE       m_full_states[N_STATES][N_VOXELS];
};

//------------------------------------------------------------------------------
// sUBLK_LB_SOLVER_UDS_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_LB_SOLVER_UDS_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT m_states_uds[N_STATES];

#undef ALIGNED_UBFLOAT
};
typedef tUBLK_LB_SOLVER_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_LB_SOLVER_UDS_DATA,	   *UBLK_LB_SOLVER_UDS_DATA;
typedef tUBLK_LB_SOLVER_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG>	 sUBLK_UBFLOAT_LB_SOLVER_UDS_DATA, *UBLK_UBFLOAT_LB_SOLVER_UDS_DATA;

#ifdef BUILD_GPU
typedef tUBLK_LB_SOLVER_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_LB_SOLVER_UDS_DATA,	   *MBLK_LB_SOLVER_UDS_DATA;
typedef tUBLK_LB_SOLVER_UDS_DATA<MBLK_UBFLOAT_TYPE_TAG>	 sMBLK_UBFLOAT_LB_SOLVER_UDS_DATA, *MBLK_UBFLOAT_LB_SOLVER_UDS_DATA;

INIT_MBLK(UBLK_LB_SOLVER_UDS_DATA) 
{
  VEC_COPY_UBLK_TO_MBLK(m_states_uds,N_STATES);
}
#endif


//------------------------------------------------------------------------------
// sUBLK_PDE_SOLVER_UDS_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_PDE_SOLVER_UDS_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT diffusion_coef[N_TIME_INDICES];
  ALIGNED_UBFLOAT grad_uds_cross[N_TIME_INDICES][3]; //used in anti-diffusion for cross-derivative term from time
#undef ALIGNED_UBFLOAT  
};

typedef tUBLK_PDE_SOLVER_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_PDE_SOLVER_UDS_DATA,         *UBLK_PDE_SOLVER_UDS_DATA;
typedef tUBLK_PDE_SOLVER_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_PDE_SOLVER_UDS_DATA, *UBLK_UBFLOAT_PDE_SOLVER_UDS_DATA;
#ifdef BUILD_GPU
typedef tUBLK_PDE_SOLVER_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_PDE_SOLVER_UDS_DATA,         *MBLK_PDE_SOLVER_UDS_DATA;
typedef tUBLK_PDE_SOLVER_UDS_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_PDE_SOLVER_UDS_DATA, *MBLK_UBFLOAT_PDE_SOLVER_UDS_DATA;

INIT_MBLK(UBLK_PDE_SOLVER_UDS_DATA) {
  VEC_COPY_UBLK_TO_MBLK(diffusion_coef, N_TIME_INDICES);
  VEC2_COPY_UBLK_TO_MBLK(grad_uds_cross,N_TIME_INDICES,3);
}
#endif

//------------------------------------------------------------------------------
// sNEARBLK_PDE_SOLVER_UDS_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_PDE_SOLVER_UDS_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT uds_pde_1[2][3];   //for flux limiter
  ALIGNED_UBFLOAT uds_pde_2[2][3];   //for flux limiter
#undef ALIGNED_UBFLOAT  
};

typedef tNEARBLK_PDE_SOLVER_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEARBLK_PDE_SOLVER_UDS_DATA,     *NEARBLK_PDE_SOLVER_UDS_DATA;
typedef tNEARBLK_PDE_SOLVER_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEARBLK_UBFLOAT_PDE_SOLVER_UDS_DATA, *NEARBLK_UBFLOAT_PDE_SOLVER_UDS_DATA;

#ifdef BUILD_GPU
typedef tNEARBLK_PDE_SOLVER_UDS_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_PDE_SOLVER_UDS_DATA,     *NEAR_MBLK_PDE_SOLVER_UDS_DATA;
typedef tNEARBLK_PDE_SOLVER_UDS_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEAR_MBLK_UBFLOAT_PDE_SOLVER_UDS_DATA, *NEAR_MBLK_UBFLOAT_PDE_SOLVER_UDS_DATA;

INIT_MBLK(NEARBLK_PDE_SOLVER_UDS_DATA) {
  VEC2_COPY_UBLK_TO_MBLK(uds_pde_1,2,3);
  VEC2_COPY_UBLK_TO_MBLK(uds_pde_2,2,3);
}
#endif


//------------------------------------------------------------------------------
// sUBLK_UDS_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_UDS_DATA
{
#if EXA_USE_SSE
  static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
  static const size_t ALIGNMENT = 32;
#endif

  EXTRACT_UBLK_TRAITS

  using sUBLK_UDS_SEND_FIELD = tUBLK_UDS_SEND_FIELD<N_VOXELS>;
  using sUBLK_UDS_PDE_SEND_FIELD = tUBLK_UDS_PDE_SEND_FIELD<N_VOXELS>;
  using sUBLK_UDS_MME_SEND_FIELD = tUBLK_UDS_MME_SEND_FIELD<N_VOXELS>;
  using sUBLK_LB_SOLVER_UDS_DATA = tUBLK_LB_SOLVER_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PDE_SOLVER_UDS_DATA = tUBLK_PDE_SOLVER_UDS_DATA<UBLK_TYPE_TAG>;  

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT uds[N_TIME_INDICES];  
  ALIGNED_UBFLOAT volume_source_term;
 
  ALIGNED_UBFLOAT grad_uds[3];
  ALIGNED_UBFLOAT uds_pre;   //new
  ALIGNED_UBFLOAT diffusivity_mol;  //LB_UDS

  static size_t SIZE(BOOLEAN has_two_copies, BOOLEAN use_uds_lb_solver) {
    if (use_uds_lb_solver) {
      if (has_two_copies)
	return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_LB_SOLVER_UDS_DATA)*2);
      else
	return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_LB_SOLVER_UDS_DATA));
    } else {  //PDE_UDS
      return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_PDE_SOLVER_UDS_DATA));
    }    
  }

   __HOST__DEVICE__ static size_t SIZE(BOOLEAN has_two_copies) {
    auto& sim = get_sim_ref();
    auto& simc = *sim.c();
    if (simc.uds_solver_type == LB_UDS) {
      if (has_two_copies)
	return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_LB_SOLVER_UDS_DATA)*2);
      else
	return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_LB_SOLVER_UDS_DATA));
    } else {  //PDE_UDS
      return (sizeof(tUBLK_UDS_DATA) + sizeof(sUBLK_PDE_SOLVER_UDS_DATA));
    }    
  }

  __HOST__DEVICE__ sUBLK_LB_SOLVER_UDS_DATA *lb_uds_data(asINT32 timestep_index) {
      return  ((sUBLK_LB_SOLVER_UDS_DATA *)
	       ((char *)this + timestep_index*sizeof(sUBLK_LB_SOLVER_UDS_DATA) + sizeof(tUBLK_UDS_DATA)));
  }

  __HOST__DEVICE__ sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data() {    
      return (sUBLK_PDE_SOLVER_UDS_DATA *)((char *)this + sizeof(tUBLK_UDS_DATA));
  }

  /*size_t diffusivity_send_size() {
    sUBLK_PDE_SOLVER_UDS_DATA  *pde_uds_data = this->pde_uds_data();
    return sizeof(pde_uds_data()->diffusion_coef[0]);
    }*/

  vxFLOAT_BASE& ub_diffusion_coef(asINT32 index, asINT32 voxor) {
    sUBLK_PDE_SOLVER_UDS_DATA  *pde_uds_data = this->pde_uds_data();
    return (pde_uds_data->diffusion_coef[index][voxor]);
  }

  __HOST__DEVICE__ sdFLOAT get_diffusion_coef(asINT32 index, asINT32 voxel) {
    sUBLK_PDE_SOLVER_UDS_DATA  *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->diffusion_coef[index][voxel];
  }

  sdFLOAT& set_diffusion_coef(asINT32 index, asINT32 voxel) {
    sUBLK_PDE_SOLVER_UDS_DATA  *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->diffusion_coef[index][voxel];
  }

  __DEVICE__
  vxFLOAT_BASE& set_diffusivity_voxor(asINT32 index, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,ubFLOAT>::value, "Function must be called on vector type" );

    sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->diffusion_coef[index][voxor];
  }

  
  size_t grad_uds_cross_send_size() {
    sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return sizeof(pde_uds_data->grad_uds_cross[0][0]);
  }
  
  __HOST__DEVICE__ sdFLOAT grad_uds_cross(asINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->grad_uds_cross[index][axis][voxel];
  }
  
  sdFLOAT& set_grad_uds_cross(asINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->grad_uds_cross[index][axis][voxel];
  }
     
  __DEVICE__
  vxFLOAT_BASE& set_grad_uds_cross_voxor(asINT32 index, asINT32 axis, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,ubFLOAT>::value, "Function must be called on vector type" );
    sUBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->grad_uds_cross[index][axis][voxor];
  }


  VOID copy_voxel_for_gradient_calculation(tUBLK_UDS_DATA *dest_ublk_uds_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);

    tUBLK_UDS_DATA* d = dest_ublk_uds_data;
    ccDOTIMES(index,  N_TIME_INDICES) {
      d->uds[index][dest_voxel] = uds[index][source_voxel];
    }
    
    if (is_uds_pde_on) {
      ccDOTIMES(timestep, N_TIME_INDICES) {
        d->set_diffusion_coef(timestep, dest_voxel) = get_diffusion_coef(timestep, source_voxel);
	d->set_grad_uds_cross(timestep, 0, dest_voxel) = grad_uds_cross(timestep, 0, source_voxel);
	d->set_grad_uds_cross(timestep, 1, dest_voxel) = grad_uds_cross(timestep, 1, source_voxel);
	d->set_grad_uds_cross(timestep, 2, dest_voxel) = grad_uds_cross(timestep, 2, source_voxel);
      }
    }
  }

  __DEVICE__
  VOID explode_voxel(tUBLK_UDS_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_uds_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    auto& sim = get_sim_ref();
    
    BOOLEAN is_uds_pde_on = (sim.c()->uds_solver_type == PDE_UDS);

    asINT32 prior_timestep_index = (prior_solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    auto f = fine_ublk_uds_data;
    vxFLOAT coarse_uds = uds[prior_timestep_index][voxel_to_explode];
    
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
        f->uds[timestep][voxor] = coarse_uds;
      }
    }
    
    if (is_uds_pde_on) {
      vxFLOAT coarse_diffusion_coeff = get_diffusion_coef(prior_timestep_index, voxel_to_explode);
      vxFLOAT coarse_grad_uds_cross0 = grad_uds_cross(prior_timestep_index, 0, voxel_to_explode);
      vxFLOAT coarse_grad_uds_cross1 = grad_uds_cross(prior_timestep_index, 1, voxel_to_explode);
      vxFLOAT coarse_grad_uds_cross2 = grad_uds_cross(prior_timestep_index, 2, voxel_to_explode);

      ccDOTIMES(timestep, N_TIME_INDICES) {
        ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
          f->set_diffusivity_voxor(timestep,       voxor)    = coarse_diffusion_coeff;
          f->set_grad_uds_cross_voxor(timestep, 0, voxor)    = coarse_grad_uds_cross0;
          f->set_grad_uds_cross_voxor(timestep, 1, voxor)    = coarse_grad_uds_cross1;
          f->set_grad_uds_cross_voxor(timestep, 2, voxor)    = coarse_grad_uds_cross2;
        }
      }
    }
  }

  VOID copy_voxel_mme_data(tUBLK_UDS_DATA *dest_ublk_uds_data, asINT32 dest_voxel, asINT32 source_voxel) {}

  VOID explode_voxel_mme(tUBLK_UDS_DATA<tUBLK_UBFLOAT_TYPE_TAG<UBLK_TYPE_TAG::N_VOXELS>> *fine_ublk_uds_data,
                         asINT32 voxel_to_explode, asINT32 solver_ts_index_mask)
  {
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      asINT32 coarse_timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
      asINT32 fine_timestep_index = 0; // explode_voxel_mme happens on odd fine timesteps which is always 0
      auto f = fine_ublk_uds_data;
      //phase field use on time uds.    
      vxFLOAT coarse_uds = uds[coarse_timestep_index][voxel_to_explode];

      ccDO_UBLK(voxor) {
	f->uds[fine_timestep_index][voxor]   = coarse_uds;
      }      
    }
#endif
  }

  VOID init() {
     memset(this, 0, sizeof(*this));
  }

   VOID init(asINT32 n_uds) {
      memset(this, 0, sizeof(*this));
   }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    asINT32 byte_size =  0;
    if (sim.uds_solver_type == LB_UDS)
      byte_size =  sizeof(sUBLK_UDS_SEND_FIELD);
    else
      byte_size =  sizeof(sUBLK_UDS_PDE_SEND_FIELD);
    send_size      += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
    tpde_send_size += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);
    if (is_uds_pde_on) {
      sUBLK_UDS_PDE_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_PDE_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_PDE_SOLVER_UDS_DATA* pde_uds_data = this->pde_uds_data();
      memcpy(field->m_uds_value,   &(uds[timestep_index]),                  sizeof(field->m_uds_value));
      memcpy(field->m_diffusion_coef, &(pde_uds_data->diffusion_coef[timestep_index]),     sizeof(field->m_diffusion_coef));
      memcpy(field->m_grad_uds_cross,   &(pde_uds_data->grad_uds_cross[timestep_index]),       sizeof(field->m_grad_uds_cross));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else {
      asINT32 uds_timestep_index = timestep_index;
#if BUILD_5G_LATTICE
      uds_timestep_index = 0;  //5G only allows LB_UDS
#endif
      sUBLK_UDS_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_LB_SOLVER_UDS_DATA* lb_uds_data = this->lb_uds_data(timestep_index);
      memcpy(field->m_uds_value, &(uds[uds_timestep_index]),       sizeof(field->m_uds_value));
      memcpy(field->m_states_uds,	    lb_uds_data->m_states_uds, sizeof(field->m_states_uds));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask, BOOLEAN is_vr_fine = FALSE) {
    asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;  
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);

    if (is_uds_pde_on) {
      sUBLK_UDS_PDE_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_PDE_SEND_FIELD*>(recv_data_info.recv_buffer);
      sUBLK_PDE_SOLVER_UDS_DATA* pde_uds_data =  this->pde_uds_data();
      memcpy(&(uds[timestep_index]),              field->m_uds_value,   sizeof(field->m_uds_value));
      memcpy(&(pde_uds_data->diffusion_coef[timestep_index]), field->m_diffusion_coef, sizeof(field->m_diffusion_coef));
      memcpy(&(pde_uds_data->grad_uds_cross[timestep_index]),   field->m_grad_uds_cross,   sizeof(field->m_grad_uds_cross));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else {
      asINT32 uds_timestep_index = timestep_index;
#if BUILD_5G_LATTICE
      uds_timestep_index = 0; //5G only allows LB_UDS
#endif  
      sUBLK_UDS_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_SEND_FIELD*>(recv_data_info.recv_buffer);
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
      sUBLK_LB_SOLVER_UDS_DATA* lb_uds_data = this->lb_uds_data(timestep_index);
#else      
      sUBLK_LB_SOLVER_UDS_DATA* lb_uds_data = this->lb_uds_data(ONLY_ONE_COPY); // Ghost ublks have one set of states
      if (is_vr_fine) // ghost vr fine ublks have two sets of states
	lb_uds_data =  this->lb_uds_data(timestep_index);
#endif
      memcpy(&(uds[uds_timestep_index]),       field->m_uds_value, sizeof(field->m_uds_value));
      memcpy(lb_uds_data->m_states_uds, field->m_states_uds,     sizeof(field->m_states_uds));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  static VOID add_mme_send_size(asINT32 &send_size) {
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) { 
      asINT32 byte_size =   sizeof(sUBLK_UDS_MME_SEND_FIELD);
      send_size += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
    }
#endif
  }
  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, SOLVER_INDEX_MASK solver_ts_index_mask)  {
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
      sUBLK_UDS_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_MME_SEND_FIELD*>(send_buffer);
      memcpy(field->m_uds_value, &(uds[timestep_index]),       sizeof(field->m_uds_value));
      field++;
      send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
  }
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
      sUBLK_UDS_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_UDS_MME_SEND_FIELD*>(recv_buffer);
      memcpy(&(uds[timestep_index]),       field->m_uds_value, sizeof(field->m_uds_value));
      field++;
      recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
  }
  
  VOID write_ckpt(BOOLEAN has_two_copies)                      { write_ckpt_lgi(this, SIZE(has_two_copies)); }
  VOID read_ckpt(BOOLEAN has_two_copies)                       { read_lgi(this, SIZE(has_two_copies));       }
  uINT64 ckpt_len(BOOLEAN has_two_copies)                      { return SIZE(has_two_copies);                }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas)
  {
    auto& timescale = get_timescale_ref();
    asINT32 prior_index = timescale.m_uds_pde_tm.prior_timestep_index(scale);
#if BUILD_5G_LATTICE
    prior_index = 0;
#endif
    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = uds[prior_index][voxel];
    }
    meas++;
  }

private:
  VOID print_lb_uds_solver_data_for_voxel(std::ostream& os,
					  asINT32 print_voxel,
					  asINT32 states_index)
  {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    os << "UDS_SOLVER_TYPE :: LB_UDS " << PRINT_OPTS::V2S;

    sim_print<N_LATTICE_VECTORS+1, N_VOXELS>(os, "lb_uds_states",
					     lb_uds_data(states_index)->m_states_uds,
					     loop_limits(0, N_LATTICE_VECTORS), print_voxel);

    sim_print_vs(os);

    ccDOTIMES(t_step, N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "uds_value", uds, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

  VOID print_pde_uds_solver_data_for_voxel(std::ostream& os,
					   asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    os << "UDS_SOLVER_TYPE :: PDE_UDS " << PRINT_OPTS::V2S;

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "uds_value", uds, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

public:
  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel,
			asINT32 states_index) {

    UBLK_SURFEL_PRINT_UTILS::sim_print_data_header(os,"UBLK_UDS_DATA");

    switch ( sim.uds_solver_type ) {

    case LB_UDS :
      print_lb_uds_solver_data_for_voxel(os,print_voxel,states_index);
      break;
      
    case PDE_UDS :
      print_pde_uds_solver_data_for_voxel(os,print_voxel);
      break;
      
    default :
      os << "Invalid UDS_SOLVER_TYPE " << (int) sim.uds_solver_type << '\n';
    }
  }

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(UBLK_UDS_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(uds,N_TIME_INDICES);  
  COPY_UBLK_TO_MBLK(volume_source_term);
  VEC_COPY_UBLK_TO_MBLK(grad_uds,3);
  COPY_UBLK_TO_MBLK(uds_pre);
  COPY_UBLK_TO_MBLK(diffusivity_mol);
}
#endif


//------------------------------------------------------------------------------
// sNEARBLK_UDS_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tNEARBLK_UDS_DATA
{
  EXTRACT_UBLK_TRAITS

    using sNEARBLK_PDE_SOLVER_UDS_DATA = tNEARBLK_PDE_SOLVER_UDS_DATA<UBLK_TYPE_TAG>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT surfel_source_term[N_TIME_INDICES]; // for capturing source term from fluid region
  ALIGNED_UBFLOAT uds_flux;  // analagous to heat_wall
  ALIGNED_UBFLOAT uds_value; // analagous to temp_wall

  //for sliding mesh
  ALIGNED_UBFLOAT uds_flux_wall;
  ALIGNED_UBFLOAT uds_lrf[2][3];
  
   __HOST__DEVICE__ static size_t SIZE() {
    auto& sim = get_sim_ref();
    auto& simc = *sim.c();
    if (simc.uds_solver_type == LB_UDS) {
      return (sizeof(tNEARBLK_UDS_DATA));
    } else {
      return (sizeof(tNEARBLK_UDS_DATA) + sizeof(sNEARBLK_PDE_SOLVER_UDS_DATA));
    }
  }

  static size_t SIZE(BOOLEAN use_uds_lb_solver) {
    if (use_uds_lb_solver) {
      return (sizeof(tNEARBLK_UDS_DATA));
    } else {
      return (sizeof(tNEARBLK_UDS_DATA) + sizeof(sNEARBLK_PDE_SOLVER_UDS_DATA));
    }
  }

  __HOST__DEVICE__  sNEARBLK_PDE_SOLVER_UDS_DATA* pde_uds_data() {    
    return  (( sNEARBLK_PDE_SOLVER_UDS_DATA *)
	     ((char *)this + sizeof(tNEARBLK_UDS_DATA)));
  }

  sdFLOAT uds_pde_1(sINT32 face, asINT32 axis, asINT32 voxel) {
    sNEARBLK_PDE_SOLVER_UDS_DATA* pde_uds_data = this->pde_uds_data();
    return pde_uds_data->uds_pde_1[face][axis][voxel];
  }

  sdFLOAT& set_uds_pde_1(sINT32 face, asINT32 axis, asINT32 voxel) {
    sNEARBLK_PDE_SOLVER_UDS_DATA* pde_uds_data = this->pde_uds_data();
    return (pde_uds_data->uds_pde_1[face][axis][voxel]);
  }

  sdFLOAT uds_pde_2(sINT32 face, asINT32 axis, asINT32 voxel) {
    sNEARBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return pde_uds_data->uds_pde_2[face][axis][voxel];
  }

  sdFLOAT& set_uds_pde_2(sINT32 face, asINT32 axis, asINT32 voxel) {
    sNEARBLK_PDE_SOLVER_UDS_DATA *pde_uds_data = this->pde_uds_data();
    return (pde_uds_data->uds_pde_2[face][axis][voxel]);
  }

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os,"UBLK_SURF_UDS_DATA");
    sim_print<N_VOXELS>(os,"uds_flux",uds_flux,print_voxel);
    sim_print<N_VOXELS>(os,"uds_value",uds_value,print_voxel);
    sim_print<N_VOXELS>(os,"uds_flux_wall",uds_flux_wall,print_voxel);
    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES) {
      sim_print_loop_header(os,"NRLK_UDS_DATA",t_step);
      sim_print<N_TIME_INDICES,N_VOXELS>(os,"surfel_source_term",surfel_source_term,t_step,print_voxel);
      sim_print<2,3,N_VOXELS>(os,"uds_lrf",uds_lrf,t_step,loop_limits(0,2),print_voxel);
      sim_print_vs(os);
    }
  }

  // All NEARBLK_UDS_DATA are used locally
  __DEVICE__
  VOID explode_voxel(tNEARBLK_UDS_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_uds_data, asINT32 voxel_to_explode, asINT32 scale, asINT32 gpu_fine_voxor) {}

  VOID explode_voxel_mme(tNEARBLK_UDS_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_uds_data, asINT32 voxel_to_explode, asINT32 scale) {}
  
  
  VOID init() {
    memset(this, 0, sizeof(*this)); 
  }

  VOID init(asINT32 n_uds) {
     memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    // Nothing to add
  }

  VOID fill_send_buffer(UBLK_SEND_DATA_INFO &send_buffer) {
    // Nothing to send
  }


  uINT64 ckpt_len() {return struct_field_size(tNEARBLK_UDS_DATA *, surfel_source_term);} 
  VOID write_ckpt() 
  {
    write_ckpt_lgi(surfel_source_term, sizeof(surfel_source_term));
  }
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff) 
  {
    size_t size = sizeof(surfel_source_term);
    pio_ckpt_buff.write(surfel_source_term, size);
  }
  VOID read_ckpt() 
  {
    read_lgi(surfel_source_term, sizeof(surfel_source_term));
  }
  VOID read_ckpt(u_char* buff, size_t& readSz) 
  {
    size_t size = sizeof(surfel_source_term);
    std::memcpy(surfel_source_term, buff+readSz, size);
    readSz+=size;
  }
  
 #undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_UDS_DATA) 
{
  VEC_COPY_UBLK_TO_MBLK(surfel_source_term,N_TIME_INDICES); // for capturing source term from fluid region
  COPY_UBLK_TO_MBLK(uds_flux);  // analagous to heat_wall
  COPY_UBLK_TO_MBLK(uds_value); // analagous to temp_wall

  //for sliding mesh
  COPY_UBLK_TO_MBLK(uds_flux_wall);
  VEC2_COPY_UBLK_TO_MBLK(uds_lrf,2,3);
}
#endif

//------------------------------------------------------------------------------
// sUBLK_PORE_UDS_DATA for 5G large_pore
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_PORE_UDS_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  using sUBLK_PORE_UDS_SEND_FIELD = tUBLK_PORE_UDS_SEND_FIELD<N_VOXELS>;
  
  ALIGNED_UBFLOAT full_states[N_STATES];  

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

  VOID init() {  //looks like no use
    memset(this, 0, sizeof(*this));
  }

  static VOID add_pore_send_size(asINT32 &send_size) {
    send_size      += (sizeof(sUBLK_PORE_UDS_SEND_FIELD) / sizeof(sdFLOAT)) * sim.n_user_defined_scalars;
  }

  VOID fill_pore_send_buffer(sdFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sUBLK_PORE_UDS_SEND_FIELD*>(send_buffer);    
    memcpy(field->m_full_states, full_states, sizeof(field->m_full_states));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_pore_recv_buffer(sdFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sUBLK_PORE_UDS_SEND_FIELD*>(recv_buffer);    
    memcpy(full_states, field->m_full_states, sizeof(field->m_full_states));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }


#undef ALIGNED_UBFLOAT
};

//------------------------------------------------------------------------------
// PHASE FIELD functions and ublk data
//------------------------------------------------------------------------------
template <typename FLOAT_TYPE>
__HOST__DEVICE__ inline sdFLOAT phi_capped(FLOAT_TYPE phi) {
  sdFLOAT phi_capped = phi;
  phi_capped = MIN(MAX(phi_capped, 0.0f), 1.0f);
  return phi_capped;
}

template <typename FLOAT_TYPE>
__HOST__DEVICE__ inline sdFLOAT phi2rho_capped(FLOAT_TYPE phi) {
  dFLOAT phi_capped = phi;
  phi_capped = MIN(MAX(phi_capped, 0.0f), 1.0f);
  sdFLOAT Phase_field_density = g_Dens_light + phi_capped * (g_Dens_heavy - g_Dens_light);
  return Phase_field_density;
}

template <typename UBLK_TYPE_TAG> class tUBLK_PF_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_PF_DATA;

typedef tUBLK_PF_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_PF_DATA,         *UBLK_PF_DATA;
typedef tUBLK_PF_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_PF_DATA, *UBLK_UBFLOAT_PF_DATA;

typedef tNEARBLK_PF_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_PF_DATA, *NEAR_UBLK_PF_DATA;
typedef tNEARBLK_PF_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEAR_UBLK_UBFLOAT_PF_DATA, *NEAR_UBLK_UBFLOAT_PF_DATA;

#ifdef BUILD_GPU
typedef tUBLK_PF_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_PF_DATA,         *MBLK_PF_DATA;
typedef tUBLK_PF_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_PF_DATA, *MBLK_UBFLOAT_PF_DATA;

typedef tNEARBLK_PF_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_PF_DATA,            *NEAR_MBLK_PF_DATA;
typedef tNEARBLK_PF_DATA<MBLK_UBFLOAT_TYPE_TAG>  sNEAR_MBLK_UBFLOAT_PF_DATA, *NEAR_MBLK_UBFLOAT_PF_DATA;
#endif

template<size_t N_VOXELS>
struct tUBLK_PF_SEND_FIELD
{  
  STP_PHYS_VARIABLE vel_pfld[3][N_VOXELS];
  STP_PHYS_VARIABLE pressure_pfld[N_VOXELS];
  STP_PHYS_VARIABLE m_force_chem_pfld[3][N_VOXELS];
  STP_PHYS_VARIABLE m_force_pfld[4][N_VOXELS];
};

template<size_t N_VOXELS>
struct tNEARBLK_PF_SEND_FIELD
{
  STP_PHYS_VARIABLE m_pre_cf_n[N_VOXELS];  
};

template<size_t N_VOXELS>
struct tUBLK_PF_MME_SEND_FIELD
{
  STP_PHYS_VARIABLE vel_pfld[3][N_VOXELS];
  STP_PHYS_VARIABLE pressure_pfld[N_VOXELS];
};

//------------------------------------------------------------------------------
// tPF_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tPF_DATA 
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT vel_pfld[3];
  ALIGNED_UBFLOAT pressure_pfld;

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print<N_VOXELS>(os, "pressure_pfld", pressure_pfld, print_voxel);
    sim_print<3, N_VOXELS>(os, "vel_pfld", vel_pfld, loop_limits(0, 2), print_voxel);
  }

#undef ALIGNED_UBFLOAT
};

typedef tPF_DATA<UBLK_SDFLOAT_TYPE_TAG> sPF_DATA,         *PF_DATA;
typedef tPF_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBFLOAT_PF_DATA, *UBFLOAT_PF_DATA;

#ifdef BUILD_GPU
INIT_MBLK(PF_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(vel_pfld, 3);  
  COPY_UBLK_TO_MBLK(pressure_pfld);  
}
#endif

//------------------------------------------------------------------------------
// tUBLK_PF_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_PF_DATA
{
  EXTRACT_UBLK_TRAITS
    
  using sPF_DATA = tPF_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PF_SEND_FIELD = tUBLK_PF_SEND_FIELD<N_VOXELS>;
  using sUBLK_PF_MME_SEND_FIELD = tUBLK_PF_MME_SEND_FIELD<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  sPF_DATA        m_pf_data[N_TIME_INDICES];
  ALIGNED_UBFLOAT force_chem_pfld[3];
  ALIGNED_UBFLOAT force_pfld[4];
  ALIGNED_UBFLOAT delta_v_p[3];
  ALIGNED_UBFLOAT delta_v_f[3];
  ALIGNED_UBFLOAT vel_m[3];
  ALIGNED_UBFLOAT phi_res;
  ALIGNED_UBFLOAT phi_state_res;
  ALIGNED_UBFLOAT eddy_vis_pfld;


  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;
    sim_print_data_header(os,"UBLK_PF_DATA");
    sim_print<3, N_VOXELS>(os, "force_chem_pfld", force_chem_pfld, loop_limits(0, 2), print_voxel);
    sim_print<4, N_VOXELS>(os, "force_pfld", force_pfld, loop_limits(0, 3), print_voxel);
    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "PF_DATA", t_step);
      m_pf_data[t_step].print_voxel_data(os, print_voxel);
      sim_print_vs(os);
    }

  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_PF_DATA *dest_ublk_pf_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_PF_DATA* d = dest_ublk_pf_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      d->m_pf_data[timestep].pressure_pfld[dest_voxel] = m_pf_data[timestep].pressure_pfld[source_voxel];
      for (asINT32 axis = 0; axis < N_AXES; axis++) {
        d->m_pf_data[timestep].vel_pfld[axis][dest_voxel] = m_pf_data[timestep].vel_pfld[axis][source_voxel];
      }
    }
    for (asINT32 axis = 0; axis < N_AXES; axis++) {
      d->force_chem_pfld[axis][dest_voxel] = force_chem_pfld[axis][source_voxel];
      d->force_pfld[axis][dest_voxel] = force_pfld[axis][source_voxel];
    }
    d->force_pfld[N_AXES][dest_voxel] = force_pfld[N_AXES][source_voxel];    
  }

  __DEVICE__
  VOID explode_voxel(tUBLK_PF_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_pf_data,
                     asINT32 voxel_to_explode,
		     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
		     [[maybe_unused]] asINT32 gpu_fine_voxor) {

    asINT32 prior_timestep_index = (prior_solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    auto d = fine_ublk_pf_data;
    vxFLOAT coarse_pressure_pfld = m_pf_data[prior_timestep_index].pressure_pfld[voxel_to_explode];
    vxFLOAT coarse_vel_pfld[N_AXES];
    vxFLOAT coarse_force_chem_pfld[N_AXES];
    vxFLOAT coarse_force_pfld[N_AXES+1];
    ccDOTIMES(axis, N_AXES) {
      coarse_vel_pfld[axis] = m_pf_data[prior_timestep_index].vel_pfld[axis][voxel_to_explode];
      coarse_force_chem_pfld[axis] = force_chem_pfld[axis][voxel_to_explode];
      coarse_force_pfld[axis] = force_pfld[axis][voxel_to_explode];
    }
    coarse_force_pfld[N_AXES] =  force_pfld[N_AXES][voxel_to_explode];
        
    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      ccDOTIMES(timestep, N_TIME_INDICES) {
        d->m_pf_data[timestep].pressure_pfld[voxor] = coarse_pressure_pfld;
        ccDOTIMES(axis, N_AXES) {
          d->m_pf_data[timestep].vel_pfld[axis][voxor] = coarse_vel_pfld[axis];
        }
      }
      for (asINT32 axis = 0; axis < N_AXES; axis++) {
	d->force_chem_pfld[axis][voxor] = coarse_force_chem_pfld[axis];
	d->force_pfld[axis][voxor] = coarse_force_pfld[axis];
      }
      d->force_pfld[N_AXES][voxor] = coarse_force_pfld[N_AXES];        
    }
  }

  VOID copy_voxel_mme_data(tUBLK_PF_DATA *dest_ublk_pf_data, asINT32 dest_voxel, asINT32 source_voxel) {
    tUBLK_PF_DATA *d = dest_ublk_pf_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      d->m_pf_data[timestep].pressure_pfld[dest_voxel] = m_pf_data[timestep].pressure_pfld[source_voxel];
      ccDOTIMES(axis, 3) {
        d->m_pf_data[timestep].vel_pfld[axis][dest_voxel] = m_pf_data[timestep].vel_pfld[axis][source_voxel];
      }
    }
    for (asINT32 axis = 0; axis < N_AXES; axis++) {
      d->force_chem_pfld[axis][dest_voxel] = force_chem_pfld[axis][source_voxel];
      d->force_pfld[axis][dest_voxel] = force_pfld[axis][source_voxel];
    }
    d->force_pfld[N_AXES][dest_voxel] = force_pfld[N_AXES][source_voxel];    
  }

  VOID explode_voxel_mme(tUBLK_PF_DATA<tUBLK_UBFLOAT_TYPE_TAG<UBLK_TYPE_TAG::N_VOXELS>> *fine_ublk_pf_data, 
			 asINT32 voxel_to_explode,
                         asINT32 solver_ts_index_mask) {

    asINT32 coarse_timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    auto d = fine_ublk_pf_data;

    asINT32 fine_timestep_index = 0; // explode_voxel_mme happens on odd fine timesteps which is always 0
    vxFLOAT coarse_pressure_pfld = m_pf_data[coarse_timestep_index].pressure_pfld[voxel_to_explode];
    vxFLOAT coarse_vel_pfld[N_AXES];
    vxFLOAT coarse_force_chem_pfld[N_AXES];
    vxFLOAT coarse_force_pfld[N_AXES+1];
    for (asINT32 axis = 0; axis < N_AXES; axis++) {
      coarse_vel_pfld[axis] = m_pf_data[coarse_timestep_index].vel_pfld[axis][voxel_to_explode];
      coarse_force_chem_pfld[axis] = force_chem_pfld[axis][voxel_to_explode];
      coarse_force_pfld[axis] = force_pfld[axis][voxel_to_explode];
    }
    coarse_force_pfld[N_AXES] = force_pfld[N_AXES][voxel_to_explode];
    
    ccDO_UBLK(voxor) {
      d->m_pf_data[fine_timestep_index].pressure_pfld[voxor] = coarse_pressure_pfld;
      ccDOTIMES(axis, N_AXES) {
	d->m_pf_data[fine_timestep_index].vel_pfld[axis][voxor] = coarse_vel_pfld[axis];
	d->force_chem_pfld[axis][voxor] = coarse_force_chem_pfld[axis];
	d->force_pfld[axis][voxor] = coarse_force_pfld[axis];
      }
      d->force_pfld[N_AXES][voxor] = coarse_force_pfld[N_AXES];        
    }
  }
 

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  VOID write_mme_ckpt(auINT32 voxel_mask, asINT32 scale)
  {
    /* asINT32 prior_index = g_timescale.m_uds_pde_tm.prior_timestep_index(scale); */
    /* CP_MEAS_CELL_VAR meas_vars[4 * 8]; // 3 vars for up to 8 voxels */
    /* asINT32 n_vars = 0; */
    /* DO_VOXELS_IN_MASK(voxel, voxel_mask) */
    /*   meas_vars[n_vars++] = m_pf_data[prior_index].pressure_pfld[voxel]; */
    /* DO_VOXELS_IN_MASK(voxel, voxel_mask) */
    /*   meas_vars[n_vars++] = m_pf_data[prior_index].vel_pfld[0][voxel]; */
    /* DO_VOXELS_IN_MASK(voxel, voxel_mask) */
    /*   meas_vars[n_vars++] = m_pf_data[prior_index].vel_pfld[1][voxel]; */
    /* DO_VOXELS_IN_MASK(voxel, voxel_mask) */
    /*   meas_vars[n_vars++] = m_pf_data[prior_index].vel_pfld[2][voxel]; */

    /* write_ckpt_lgi(meas_vars, n_vars * sizeof(CP_MEAS_CELL_VAR)); */
  }
  
  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sUBLK_PF_SEND_FIELD) / sizeof(sdFLOAT));
    //tpde_send_size += (sizeof(sUBLK_PF_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    sUBLK_PF_SEND_FIELD* field = reinterpret_cast<sUBLK_PF_SEND_FIELD*>(send_data_info.send_buffer);    
    memcpy(field->vel_pfld, m_pf_data[timestep_index].vel_pfld, sizeof(field->vel_pfld));
    memcpy(field->pressure_pfld, &(m_pf_data[timestep_index].pressure_pfld), sizeof(field->pressure_pfld));
    memcpy(field->m_force_chem_pfld, force_chem_pfld,      sizeof(field->m_force_chem_pfld));
    memcpy(field->m_force_pfld, force_pfld,      sizeof(field->m_force_pfld));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER; 
    sUBLK_PF_SEND_FIELD* field = reinterpret_cast<sUBLK_PF_SEND_FIELD*>(recv_data_info.recv_buffer);
    
    memcpy(m_pf_data[timestep_index].vel_pfld, field->vel_pfld, sizeof(field->vel_pfld));
    memcpy( &(m_pf_data[timestep_index].pressure_pfld), field->pressure_pfld, sizeof(field->pressure_pfld));
    memcpy(force_chem_pfld, field->m_force_chem_pfld, sizeof(field->m_force_chem_pfld));
    memcpy(force_pfld, field->m_force_pfld, sizeof(field->m_force_pfld));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  static VOID add_mme_send_size(asINT32 &send_size) {
    send_size      += ( sizeof(sUBLK_PF_MME_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
    sUBLK_PF_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_PF_MME_SEND_FIELD*>(send_buffer);
    
    memcpy(field->vel_pfld, m_pf_data[timestep_index].vel_pfld, sizeof(field->vel_pfld));
    memcpy(field->pressure_pfld, &(m_pf_data[timestep_index].pressure_pfld), sizeof(field->pressure_pfld));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    sUBLK_PF_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_PF_MME_SEND_FIELD*>(recv_buffer);
    
    memcpy(m_pf_data[timestep_index].vel_pfld, field->vel_pfld, sizeof(field->vel_pfld));
    memcpy(&(m_pf_data[timestep_index].pressure_pfld), field->pressure_pfld, sizeof(field->pressure_pfld));

    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID reflect_velocity(STP_PHYS_VARIABLE mirror_sign_factor[N_AXES], VOXEL_NUM voxel) {
    ccDOTIMES(axis, N_AXES) {
      ccDOTIMES(scheme, N_TIME_INDICES) {
        m_pf_data[scheme].vel_pfld[axis][voxel] *= mirror_sign_factor[axis];
      }
      force_pfld[axis][voxel] *= mirror_sign_factor[axis];
      force_chem_pfld[axis][voxel] *= mirror_sign_factor[axis];
    }
  }
#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(UBLK_PF_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(m_pf_data,N_TIME_INDICES);
  VEC_COPY_UBLK_TO_MBLK(force_chem_pfld,3);
  VEC_COPY_UBLK_TO_MBLK(force_pfld,4);
  VEC_COPY_UBLK_TO_MBLK(delta_v_p,3);
  VEC_COPY_UBLK_TO_MBLK(delta_v_f,3);
  VEC_COPY_UBLK_TO_MBLK(vel_m,3);
  COPY_UBLK_TO_MBLK(phi_res);
  COPY_UBLK_TO_MBLK(phi_state_res);
  COPY_UBLK_TO_MBLK( eddy_vis_pfld);
}
#endif

//------------------------------------------------------------------------------
// sNEARBLK_PF_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tNEARBLK_PF_DATA 
{
   EXTRACT_UBLK_TRAITS
   using sNEARBLK_PF_SEND_FIELD = tNEARBLK_PF_SEND_FIELD<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT srf_potential[N_MOVING_STATES];
  ALIGNED_UBFLOAT pre_cf_n;
  ALIGNED_UBFLOAT post_cf_n;  
  ALIGNED_UBFLOAT post_advect_scale_factors_pfld[N_MOVING_STATES];

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_SURF_PF_DATA");
    sim_print<N_MOVING_STATES, N_VOXELS>(os, "srf_potential", srf_potential, loop_limits(0, N_MOVING_STATES-1), print_voxel);
    sim_print_vs(os);
  }

  __DEVICE__
  VOID explode_voxel(tNEARBLK_PF_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_pf_data, 
		     asINT32 voxel_to_explode, 
		     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    // In the surfel centric world, surfel interacts with vr fine voxels and
    // cannot use any information from underlying coarse voxel
    auto *d = fine_ublk_pf_data;
    vxFLOAT coarse_post_cf_n = post_cf_n[voxel_to_explode];
    vxFLOAT coarse_pre_cf_n = pre_cf_n[voxel_to_explode];
    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      d->post_cf_n[voxor] = coarse_post_cf_n;
      d->pre_cf_n[voxor] = coarse_pre_cf_n;
    }    
  }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }
  
  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sNEARBLK_PF_SEND_FIELD) / sizeof(sdFLOAT));
    //tpde_send_size += (sizeof(sNEARBLK_PF_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index) {
    sNEARBLK_PF_SEND_FIELD* field = reinterpret_cast<sNEARBLK_PF_SEND_FIELD*>(send_data_info.send_buffer);
    memcpy(field->m_pre_cf_n, pre_cf_n, sizeof(field->m_pre_cf_n));
    //memcpy(field->m_post_cf_n, post_cf_n, sizeof(field->m_post_cf_n));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID  expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 timestep_index) {
    sNEARBLK_PF_SEND_FIELD* field = reinterpret_cast<sNEARBLK_PF_SEND_FIELD*>(recv_data_info.recv_buffer);
    memcpy(pre_cf_n, field->m_pre_cf_n, sizeof(field->m_pre_cf_n));
    //memcpy(post_cf_n, field->m_post_cf_n, sizeof(field->m_post_cf_n));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_PF_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(srf_potential,N_MOVING_STATES);
  COPY_UBLK_TO_MBLK(pre_cf_n);
  COPY_UBLK_TO_MBLK(post_cf_n);
  VEC_COPY_UBLK_TO_MBLK(post_advect_scale_factors_pfld,N_MOVING_STATES);
}
#endif
//end phase field ublk data



typedef struct sSURFEL_V2S_UDS_SEND_FIELD
{
  SURFEL_STATE		m_out_flux_uds[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE	m_uds_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE	m_uds_flux_cross_lrf;
} *SURFEL_V2S_UDS_SEND_FIELD;

typedef struct sSURFEL_UDS_SEND_FIELD
{
  STP_PHYS_VARIABLE m_uds_flux;          
  STP_PHYS_VARIABLE m_uds_value_bc;           
  STP_PHYS_VARIABLE m_uds_flux_bc;            
  STP_PHYS_VARIABLE m_uds_value;
  //STP_PHYS_VARIABLE m_uds_thickness;
  //STP_PHYS_VARIABLE m_defrost_time;
} *SURFEL_UDS_SEND_FIELD;

typedef struct sSURFEL_V2S_UDS_PDE_SEND_FIELD
{
  STP_PHYS_VARIABLE m_diffusion_coef;  
  STP_PHYS_VARIABLE m_uds0;
  STP_PHYS_VARIABLE m_uds_flux_cross_lrf;
} *SURFEL_V2S_UDS_PDE_SEND_FIELD;

typedef struct sSURFEL_UDS_PDE_SEND_FIELD
{
  STP_PHYS_VARIABLE m_uds_flux;          
  STP_PHYS_VARIABLE m_uds_value_bc;           
  STP_PHYS_VARIABLE m_uds_flux_bc;            
  STP_PHYS_VARIABLE m_uds_value;
  //STP_PHYS_VARIABLE m_uds_thickness;
  //STP_PHYS_VARIABLE m_defrost_time;
  //STP_PHYS_VARIABLE m_uds_value_pair; 
} *SURFEL_UDS_PDE_SEND_FIELD;


//------------------------------------------------------------------------------
// tSURFEL_V2S_UDS_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_V2S_UDS_DATA {
  EXTRACT_SURFEL_TRAITS

  tSFL_VAR<STP_PHYS_VARIABLE, N> m_uds_bar[N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<SURFEL_STATE, N>	 m_in_states_uds[N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_diffusion_coef; //used by PDE_UDS
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_uds0; 	      //used by PDE_UDS
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_uds_flux_cross_lrf;

  // Even though this is part of the LB_UDS solver, this is
  // put in the end to aid reset
  tSFL_VAR<SURFEL_STATE, N>	 m_out_flux_uds[N_SURFEL_PGRAM_VOLUMES];
  
  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_V2S_UDS_DATA");
    sim_print(os, "uds_flux_cross_lrf", m_uds_flux_cross_lrf);
    
    switch ( sim.uds_solver_type ) {
    case LB_UDS :
      sim_print_vs(os);
      sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "uds_bar", m_uds_bar);
      sim_print_vs(os);
      sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "in_states_uds", m_in_states_uds);
      sim_print_vs(os);
      sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "out_flux_uds", m_out_flux_uds);
      break;
    case PDE_UDS :
      sim_print_vs(os);
      sim_print(os, "diffusion_coef", m_diffusion_coef);
      sim_print(os, "uds0", m_uds0);
      break;
    default:
      ;
    }
  }
  
  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  
  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_V2S_UDS_DATA));
  }
  VOID reset_except_outflux_uds() {
    memset(this, 0, (sizeof(tSURFEL_V2S_UDS_DATA) - sizeof(STP_PHYS_VARIABLE)*N_SURFEL_PGRAM_VOLUMES));
  }
  
   __DEVICE__ VOID reset(int soxor,
                        BOOLEAN is_even_or_odd,
                        BOOLEAN is_odd,
                        BOOLEAN is_timestep_even) {
    if (!is_even_or_odd || is_timestep_even) {
      ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
        m_uds_bar[i][soxor] = 0.0F;
        m_in_states_uds[i][soxor] = 0.0F;
      } 
      m_diffusion_coef[soxor] = 0.0F;
      m_uds0[soxor] = 0.0F;
      
      m_uds_flux_cross_lrf[soxor] = 0.0F;
      
      if (!is_odd) {
        ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
          m_out_flux_uds[i][soxor] = 0.0F;
        }
      }
    }
  }
  

  VOID reflect_to_real_surfel(tSURFEL_V2S_UDS_DATA *mirror_v2s_uds_data,
                              STP_LATVEC_MASK latvec_mask,
                              STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                              STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                              BOOLEAN is_uds_lb_solver_on) {
    if (is_uds_lb_solver_on) {
      ccDOTIMES(state_index, N_MOVING_STATES) {
        if (((latvec_mask >> state_index) & 1) == 0)
          continue;
        STP_STATE_INDEX source_state_index = state_index;
        STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
        STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

        m_in_states_uds[source_latvec_pair_index] += mirror_v2s_uds_data->m_in_states_uds[mirror_latvec_pair_index];
        m_uds_bar[source_latvec_pair_index] += mirror_v2s_uds_data->m_uds_bar[mirror_latvec_pair_index];
      }
    } else {
      m_diffusion_coef += mirror_v2s_uds_data->m_diffusion_coef;
    }
  }
  VOID reflect_to_mirror_surfel(tSURFEL_V2S_UDS_DATA *real_v2s_uds_data,
                                STP_LATVEC_MASK latvec_mask,
                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                                STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                BOOLEAN is_uds_lb_solver_on) {
    if (is_uds_lb_solver_on) {
      ccDOTIMES(state_index, N_MOVING_STATES) {
        if (((latvec_mask >> state_index) & 1) == 0)
          continue;
        STP_STATE_INDEX mirror_state_index        = state_index;
        STP_STATE_INDEX mirror_latvec_pair_index  = state_latvec_pair(mirror_state_index);
        STP_STATE_INDEX source_latvec_pair_index  = reflected_latvec_pair[mirror_latvec_pair_index];
        m_out_flux_uds[mirror_latvec_pair_index] = real_v2s_uds_data->m_out_flux_uds[source_latvec_pair_index];
        m_uds_bar[mirror_latvec_pair_index] = real_v2s_uds_data->m_uds_bar[source_latvec_pair_index];
      }
    } else {
      m_diffusion_coef = real_v2s_uds_data->m_diffusion_coef;
      //uds0 = source_uds_data->uds0; //lrf surfel cannot be mirror surfel
    }
  }


  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_V2S_UDS_DATA *even_v2s_uds_data,
                             asINT32 esoxor, asINT32 osoxor) {
    auto& simc = get_simc_ref();
    if (simc.uds_solver_type == LB_UDS) {
      ccDOTIMES(j, N_SURFEL_PGRAM_VOLUMES) {
        m_in_states_uds[j][osoxor] = even_v2s_uds_data->m_in_states_uds[j][esoxor];
      }
    }    
  }

   __HOST__DEVICE__
   VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_UDS_DATA *even_v2s_uds_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
                                        asINT32 osoxor, asINT32 esoxor) {
    auto& simc = get_simc_ref();
    if (simc.uds_solver_type == PDE_UDS) {
      m_diffusion_coef[osoxor] = even_v2s_uds_data->m_diffusion_coef[esoxor] * mme_weight_inverse;
    }
  }


  __HOST__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_UDS_DATA *even_v2s_uds_data, 
					STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse, 
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse); 

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    asINT32 byte_size =  0;
    if (sim.uds_solver_type == LB_UDS)
      byte_size = sizeof(sSURFEL_V2S_UDS_SEND_FIELD);
    else
      byte_size = sizeof(sSURFEL_V2S_UDS_PDE_SEND_FIELD);

    send_size      += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
    tpde_send_size += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
  }
  
  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
      //BOOLEAN is_uds_pde_on = (send_data_info.uds_solver_type == PDE_UDS);
      BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);
    if (is_uds_pde_on) {
      SURFEL_V2S_UDS_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_UDS_PDE_SEND_FIELD>(send_data_info.send_buffer);
      field->m_diffusion_coef   = m_diffusion_coef;
      field->m_uds0               = m_uds0;
      field->m_uds_flux_cross_lrf = m_uds_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    } 
    else {
      SURFEL_V2S_UDS_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_UDS_SEND_FIELD>(send_data_info.send_buffer);
      memcpy(field->m_out_flux_uds, m_out_flux_uds, sizeof(field->m_out_flux_uds));
      memcpy(field->m_uds_bar,      m_uds_bar,      sizeof(field->m_uds_bar));
      field->m_uds_flux_cross_lrf = m_uds_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);  
    }
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
      //BOOLEAN is_uds_pde_on = (recv_data_info.uds_solver_type == PDE_UDS);
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);
    if (is_uds_pde_on) {
      SURFEL_V2S_UDS_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_UDS_PDE_SEND_FIELD>(recv_data_info.recv_buffer);
      m_diffusion_coef   = field->m_diffusion_coef;
      m_uds0               = field->m_uds0;
      m_uds_flux_cross_lrf = field->m_uds_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
    else {
      SURFEL_V2S_UDS_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_UDS_SEND_FIELD>(recv_data_info.recv_buffer);
      memcpy(m_out_flux_uds, field->m_out_flux_uds, sizeof(field->m_out_flux_uds));
      memcpy(m_uds_bar,      field->m_uds_bar,      sizeof(field->m_uds_bar));
      m_uds_flux_cross_lrf = field->m_uds_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);  
    }
  }

};

using sSURFEL_V2S_UDS_DATA = tSURFEL_V2S_UDS_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_UDS_DATA = sSURFEL_V2S_UDS_DATA*;

template<>
__HOST__ INLINE
VOID tSURFEL_V2S_UDS_DATA<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(tSURFEL_V2S_UDS_DATA<SFL_SDFLOAT_TYPE_TAG> *even_v2s_uds_data,
                                                             STP_PHYS_VARIABLE even_density,
                                                             STP_SURFEL_WEIGHT mme_weight_inverse,
                                                             STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) {
  this->pre_advect_init_copy_even_to_odd(even_v2s_uds_data, even_density, mme_weight_inverse, s2s_sampling_weight_inverse, 0, 0);
}

#if BUILD_GPU
INIT_MSFL(tSURFEL_V2S_UDS_DATA);
#endif


//------------------------------------------------------------------------------
// sSURFEL_UDS_DATA
//------------------------------------------------------------------------------
// Skeleton of Uds Surfel Data structure
template<typename SFL_TYPE_TAG>
struct tSURFEL_UDS_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<BOOLEAN, N> is_flux_bc;                 // false if prescribed value, true if prescribed flux
  tSFL_VAR<BOOLEAN, N> is_active_for_uds;       // is s2v on for this particular uds
  tSFL_VAR<BOOLEAN, N> is_outlet_bc;
  tSFL_VAR<BOOLEAN, N> is_condensable;

  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_value_bc;     // uds value on the surfel
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_flux_bc;      // flux of uds value on surfel
  
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_value;          // uds value boundary condition
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_value_prime;
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_thickness;      // uds value boundary condition
  tSFL_VAR<STP_PHYS_VARIABLE, N> defrost_time;     // defrost time of the physical surfel
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_flux;           // uds flux boundary condition 
  //tSFL_VAR<STP_PHYS_VARIABLE, N> diffusion_coef;   // unlike temperature, the diffusion coefficient is a variable
                                      // needed for the flux calculation

//  STP_PHYS_VARIABLE q_value_pair;
  //tSFL_VAR<STP_PHYS_VARIABLE, N> uds0;               // Currently unused simeng
  //tSFL_VAR<STP_PHYS_VARIABLE, N> uds_flux_cross_lrf;

  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_UDS_DATA");
    sim_print(os, "is_flux_bc", is_flux_bc);
    sim_print(os, "is_active_for_uds", is_active_for_uds);
    sim_print(os, "is_outlet_bc", is_outlet_bc);
    sim_print(os, "is_condensable", is_condensable);

    sim_print(os, "uds_value_bc", uds_value_bc);
    sim_print(os, "uds_flux_bc", uds_flux_bc);

    sim_print(os, "uds_value", uds_value);
    sim_print(os, "uds_value_prime", uds_value_prime);
    sim_print(os, "uds_thickness", uds_thickness);
    sim_print(os, "defrost_time", defrost_time);
    sim_print(os, "uds_flux", uds_flux);
  }

  VOID pre_advect_init();

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_UDS_DATA even_surfel_uds_data,
					STP_PHYS_VARIABLE even_density,
					STP_SURFEL_WEIGHT mme_weight_inverse,
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
					asINT32 osoxor, asINT32 esoxor)
  {
    auto& simc = get_simc_ref();
    uds_value[osoxor] = even_surfel_uds_data->uds_value[esoxor] *
      (g_use_lrf_s2s_sampling_uds ? s2s_sampling_weight_inverse : mme_weight_inverse);
    /*uds_thickness[osoxor] = even_surfel_uds_data->uds_thickness[esoxor] *
      (g_use_lrf_s2s_sampling_uds ? s2s_sampling_weight_inverse : mme_weight_inverse);*/
    if (simc.uds_solver_type == PDE_UDS){
#ifdef OLD_SURFEL_DATA_STRUCTURE      
      //diffusion_coef = even_surfel_uds_data->diffusion_coef * mme_weight_inverse;
      defrost_time[osoxor] = even_surfel_uds_data->defrost_time[esoxor];
#endif
    }
}

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_UDS_DATA *even_surfel_uds_data,
                             asINT32 esoxor, asINT32 osoxor) {
  }
  
  static VOID define_pde_send_mpi_type(MPI_Datatype *eMPI_type);

  static VOID define_pde_recv_mpi_type(MPI_Datatype *eMPI_type);
  //static VOID define_pde_recv_mpi_type0(MPI_Datatype *eMPI_type);
  //static VOID define_pde_recv_mpi_type1(MPI_Datatype *eMPI_type);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    asINT32 byte_size = 0;
    if (sim.uds_solver_type == LB_UDS)
      byte_size = sizeof(sSURFEL_UDS_SEND_FIELD);
    else
      byte_size = sizeof(sSURFEL_UDS_PDE_SEND_FIELD);

    send_size      += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
    tpde_send_size += sim.n_user_defined_scalars * (byte_size / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    //BOOLEAN is_uds_pde_on = (send_data_info.uds_solver_type == PDE_UDS);
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);
    //currently, PDE and LB based send fields comm the same conenst, but they may comm different vars in the future, so we separate them here
    if (is_uds_pde_on) {
      SURFEL_UDS_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_UDS_PDE_SEND_FIELD>(send_data_info.send_buffer);
      field->m_uds_flux       	= uds_flux;
      field->m_uds_value_bc       = uds_value_bc;
      field->m_uds_flux_bc        = uds_flux_bc;
      field->m_uds_value          = uds_value;
      //field->m_uds_thickness      = uds_thickness;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
    else {
      SURFEL_UDS_SEND_FIELD field = reinterpret_cast<SURFEL_UDS_SEND_FIELD>(send_data_info.send_buffer);
      field->m_uds_flux       	= uds_flux;
      field->m_uds_value_bc       = uds_value_bc;
      field->m_uds_flux_bc        = uds_flux_bc;
      field->m_uds_value          = uds_value;
      //field->m_uds_thickness      = uds_thickness;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    //BOOLEAN is_uds_pde_on = (recv_data_info.uds_solver_type == PDE_UDS);
    BOOLEAN is_uds_pde_on = (sim.uds_solver_type == PDE_UDS);
    if (is_uds_pde_on) {
      SURFEL_UDS_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_UDS_PDE_SEND_FIELD>(recv_data_info.recv_buffer);
      uds_flux	       = field->m_uds_flux;
      uds_value_bc       = field->m_uds_value_bc;
      uds_flux_bc        = field->m_uds_flux_bc;
      uds_value          = field->m_uds_value;
      //uds_thickness      = field->m_uds_thickness;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
    else {
      SURFEL_UDS_SEND_FIELD field = reinterpret_cast<SURFEL_UDS_SEND_FIELD>(recv_data_info.recv_buffer);
      uds_flux	       = field->m_uds_flux;
      uds_value_bc       = field->m_uds_value_bc;
      uds_flux_bc        = field->m_uds_flux_bc;
      uds_value          = field->m_uds_value;
      //uds_thickness      = field->m_uds_thickness;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_UDS_DATA *mirror_uds_data,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);

  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_UDS_DATA *source_uds_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);


};

using sSURFEL_UDS_DATA = tSURFEL_UDS_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_UDS_DATA = sSURFEL_UDS_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_UDS_DATA);
#endif

static const asINT32 SURFEL_UDS_DATA_CKPT_SIZE = sizeof(sSURFEL_UDS_DATA);

//------------------------------------------------------------------------------
// sSURFEL_S2S_UDS_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_S2S_UDS_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<SURFEL_STATE, N>	m_out_flux_uds[N_S2S_TIME_INDICES][N_SURFEL_PGRAM_VOLUMES];

  uINT64 ckpt_len() { return sizeof(*this); }
  VOID read_ckpt()  { read_lgi(*this);      }
  VOID write_ckpt() { write_ckpt_lgi(*this);}

};

using sSURFEL_S2S_UDS_DATA = tSURFEL_S2S_UDS_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_S2S_UDS_DATA = sSURFEL_S2S_UDS_DATA*;

#if BUILD_GPU

INIT_MSFL(tSURFEL_S2S_UDS_DATA);

#endif

//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_UDS_DATA
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_UDS_DATA
{
  STP_PHYS_VARIABLE uds_value;         
  
  VOID init() {
     memset(this, 0, sizeof(*this));
  };
  VOID pre_advect_init();
  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_UDS_DATA *even_surfel_uds_data);
 
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

 
} *SAMPLING_SURFEL_UDS_DATA;


//------------------------------------------------------------------------------
// PHASE FIELD SURFEL SEND FIELD
//------------------------------------------------------------------------------
typedef struct sSURFEL_PF_SEND_FIELD
{  
  STP_PHYS_VARIABLE  m_rho_bar_ratio_pfld[2];
  STP_PHYS_VARIABLE  m_u_bar_ratio_pfld[4];
  STP_PHYS_VARIABLE  m_pressure_pfld_ur;
} *SURFEL_PF_SEND_FIELD;

typedef struct sSURFEL_V2S_PF_SEND_FIELD
{
  STP_PHYS_VARIABLE  m_pressure_pfld;
  STP_PHYS_VARIABLE  m_cf_n;
  STP_PHYS_VARIABLE  m_grad_chem[3];
  STP_PHYS_VARIABLE  m_grad_orderp[4];
  STP_PHYS_VARIABLE  m_vel_pfld[3];
  STP_PHYS_VARIABLE  m_u_bar_pfld[N_SURFEL_PGRAM_VOLUMES];
} *SURFEL_V2S_PF_SEND_FIELD;

//------------------------------------------------------------------------------
// tSURFEL_V2S_PF_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_V2S_PF_DATA {

  EXTRACT_SURFEL_TRAITS
  
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_pressure_pfld;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_cf_n;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_grad_chem[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_grad_orderp[4];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_vel_pfld[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  m_u_bar_pfld[N_SURFEL_PGRAM_VOLUMES]; // V2S and S2V

  VOID print_surfel_data(std::ostream& os){
    using namespace UBLK_SURFEL_PRINT_UTILS;
    sim_print_data_header(os, "SURFEL_V2S_PF_DATA");
    sim_print(os, "m_cf_n", m_cf_n);
    sim_print(os, "m_pressure_pfld", m_pressure_pfld);
    sim_print<3>(os, "m_grad_chem", m_grad_chem);
    sim_print<4>(os, "m_grad_orderp", m_grad_orderp);
    sim_print<3>(os, "m_vel_pfld", m_vel_pfld);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "m_u_bar_pfld", m_u_bar_pfld);
  }

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID read_ckpt()  { read_lgi(*this);       }
  VOID write_ckpt() { write_ckpt_lgi(*this); }

  __HOST__DEVICE__
  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_V2S_PF_DATA));
  }

  VOID reflect_to_real_surfel(tSURFEL_V2S_PF_DATA *mirror_v2s_pf_data, 
                              STP_LATVEC_MASK latvec_mask,
                              STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                              STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {
    m_pressure_pfld += mirror_v2s_pf_data->m_pressure_pfld;
    m_cf_n    += mirror_v2s_pf_data->m_cf_n;
    ccDOTIMES(axis, 3) {
      m_grad_chem[axis] += mirror_v2s_pf_data->m_grad_chem[axis]  * velocity_mirror_sign_factor[axis];
      m_grad_orderp[axis] += mirror_v2s_pf_data->m_grad_orderp[axis]  * velocity_mirror_sign_factor[axis];
      m_vel_pfld[axis] += mirror_v2s_pf_data->m_vel_pfld[axis] * velocity_mirror_sign_factor[axis];
    }
    m_grad_orderp[3] += mirror_v2s_pf_data->m_grad_orderp[3] ;
    ccDOTIMES(state_index, N_MOVING_STATES) {
      if (((latvec_mask >> state_index) & 1) == 0)
        continue;
      STP_STATE_INDEX source_state_index = state_index;
      STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
      STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];
      m_u_bar_pfld[source_latvec_pair_index] += mirror_v2s_pf_data->m_u_bar_pfld[mirror_latvec_pair_index];
    }
  }

  VOID reflect_to_mirror_surfel(tSURFEL_V2S_PF_DATA *real_v2s_pf_data,
                                STP_LATVEC_MASK latvec_mask,
                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                                STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES]) {
    m_pressure_pfld      = real_v2s_pf_data->m_pressure_pfld;
    m_cf_n      = real_v2s_pf_data->m_cf_n;
    ccDOTIMES(axis, 3) {
      m_grad_chem[axis] = real_v2s_pf_data->m_grad_chem[axis]  * velocity_mirror_sign_factor[axis];
      m_grad_orderp[axis] = real_v2s_pf_data->m_grad_orderp[axis]  * velocity_mirror_sign_factor[axis];
      m_vel_pfld[axis] = real_v2s_pf_data->m_vel_pfld[axis] * velocity_mirror_sign_factor[axis];
    }
    m_grad_orderp[3] = real_v2s_pf_data->m_grad_orderp[3] ;
    ccDOTIMES(state_index, N_MOVING_STATES) {
      if (((latvec_mask >> state_index) & 1) == 0)
        continue;
      STP_STATE_INDEX mirror_state_index        = state_index;

      STP_STATE_INDEX mirror_latvec_pair_index  = state_latvec_pair(mirror_state_index);
      STP_STATE_INDEX source_latvec_pair_index  = reflected_latvec_pair[mirror_latvec_pair_index];
      m_u_bar_pfld[mirror_latvec_pair_index] = real_v2s_pf_data->m_u_bar_pfld[source_latvec_pair_index];
    }
  }

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_V2S_PF_DATA *even_v2s_pf_data) {
    return;
  }

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_PF_DATA *even_v2s_pf_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
					asINT32 osoxor, asINT32 esoxor)
  {
    m_pressure_pfld[osoxor]      = even_v2s_pf_data->m_pressure_pfld[esoxor] * mme_weight_inverse;
    m_cf_n[osoxor]      = even_v2s_pf_data->m_cf_n[esoxor] * mme_weight_inverse;
    ccDOTIMES(axis, 3) {
     m_grad_chem[axis][osoxor] = even_v2s_pf_data-> m_grad_chem[axis][esoxor] * mme_weight_inverse;
     m_grad_orderp[axis][osoxor] = even_v2s_pf_data-> m_grad_orderp[axis][esoxor] * mme_weight_inverse;
     m_vel_pfld[axis][osoxor]   = even_v2s_pf_data->m_vel_pfld[axis][esoxor] * mme_weight_inverse;
    }
    m_grad_orderp[3][osoxor]   = even_v2s_pf_data->m_grad_orderp[3][esoxor] * mme_weight_inverse;
  } 

  
  __HOST__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_PF_DATA *even_v2s_uds_data, 
					STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse, 
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse); 

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_V2S_PF_SEND_FIELD) / sizeof(sdFLOAT));
    //tpde_send_size += (sizeof(sSURFEL_V2S_PF_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_V2S_PF_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_PF_SEND_FIELD>(send_data_info.send_buffer);
    field->m_pressure_pfld = m_pressure_pfld;
    field->m_cf_n = m_cf_n;        
    memcpy(field->m_grad_chem, m_grad_chem, sizeof(field->m_grad_chem));
    memcpy(field->m_grad_orderp, m_grad_orderp, sizeof(field->m_grad_orderp));
    memcpy(field->m_vel_pfld, m_vel_pfld, sizeof(field->m_vel_pfld));
    memcpy(field->m_u_bar_pfld, m_u_bar_pfld, sizeof(field->m_u_bar_pfld));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_V2S_PF_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_PF_SEND_FIELD>(recv_data_info.recv_buffer);
    m_pressure_pfld = field->m_pressure_pfld;
    m_cf_n = field->m_cf_n;    
    memcpy(m_grad_chem, field->m_grad_chem, sizeof(field->m_grad_chem));
    memcpy(m_grad_orderp, field->m_grad_orderp, sizeof(field->m_grad_orderp));
    memcpy(m_vel_pfld, field->m_vel_pfld, sizeof(field->m_vel_pfld));
    memcpy(m_u_bar_pfld, field->m_u_bar_pfld, sizeof(field->m_u_bar_pfld));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};

using sSURFEL_V2S_PF_DATA = tSURFEL_V2S_PF_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_PF_DATA = sSURFEL_V2S_PF_DATA*;

template<>
__HOST__ INLINE
VOID tSURFEL_V2S_PF_DATA<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(tSURFEL_V2S_PF_DATA<SFL_SDFLOAT_TYPE_TAG> *even_v2s_pf_data,
										  STP_PHYS_VARIABLE even_density,
										  STP_SURFEL_WEIGHT mme_weight_inverse,
										  STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) 
{
  this->pre_advect_init_copy_even_to_odd(even_v2s_pf_data, even_density, mme_weight_inverse, s2s_sampling_weight_inverse, 0, 0);
}

#if BUILD_GPU
INIT_MSFL(tSURFEL_V2S_PF_DATA);
#endif

//------------------------------------------------------------------------------
// tSURFEL_PF_DATA
//------------------------------------------------------------------------------

template<typename SFL_TYPE_TAG>
struct tSURFEL_PF_QTM
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N> pressure_ur;
  tSFL_VAR<STP_PHYS_VARIABLE, N> u_n_ur[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N> u_avg[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N> u_pre[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N> ustar_n;
  tSFL_VAR<STP_PHYS_VARIABLE, N> ustar_exact_n;
  tSFL_VAR<STP_PHYS_VARIABLE, N> ustar_n_minus_1;
  tSFL_VAR<STP_PHYS_VARIABLE, N> flag_init;

  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_PF_QTM));
  }
};

using sSURFEL_PF_QTM = tSURFEL_PF_QTM<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_PF_QTM = sSURFEL_PF_QTM*;
#if BUILD_GPU
using sMSFL_PF_QTM = tSURFEL_PF_QTM<MSFL_SDFLOAT_TYPE_TAG>;
INIT_MSFL(tSURFEL_PF_QTM);
#endif

template<typename SFL_TYPE_TAG>
struct tSURFEL_PF_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N>   rho_bar_ratio_pfld[2];
  tSFL_VAR<STP_PHYS_VARIABLE, N>   u_bar_ratio_pfld[4]; 
  tSFL_VAR<STP_PHYS_VARIABLE, N>   pressure_pfld_ur;
  tSURFEL_PF_QTM<SFL_TYPE_TAG>     pf_qtm;

  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_PF_DATA");
    sim_print<2>(os, "rho_bar_ratio", rho_bar_ratio_pfld);
    sim_print<2>(os, "u_bar_ratio", u_bar_ratio_pfld);
  }

  VOID pre_advect_init() {
    msg_internal_error("Never should be called."); 
  }

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_PF_DATA *even_surfel_pf_data, 
					STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse, 
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
					asINT32 osoxor, asINT32 esoxor) 
  {
    rho_bar_ratio_pfld[0][osoxor] = even_surfel_pf_data->rho_bar_ratio_pfld[0][esoxor];
    rho_bar_ratio_pfld[1][osoxor] = even_surfel_pf_data->rho_bar_ratio_pfld[1][esoxor];
    u_bar_ratio_pfld[0][osoxor] = even_surfel_pf_data->u_bar_ratio_pfld[0][esoxor];
    u_bar_ratio_pfld[1][osoxor] = even_surfel_pf_data->u_bar_ratio_pfld[1][esoxor];
    u_bar_ratio_pfld[2][osoxor] = even_surfel_pf_data->u_bar_ratio_pfld[2][esoxor]; 
    u_bar_ratio_pfld[3][osoxor] = even_surfel_pf_data->u_bar_ratio_pfld[3][esoxor];
    pressure_pfld_ur[osoxor] = even_surfel_pf_data->pressure_pfld_ur[esoxor];
    pf_qtm[osoxor] = even_surfel_pf_data->pf_qtm[esoxor];
  }

   __HOST__DEVICE__
   VOID seed_copy_even_to_odd(tSURFEL_PF_DATA *even_surfel_pf_data,
			      asINT32 esoxor, asINT32 osoxor) { } 


  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
  } 

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }

  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_PF_DATA *mirror_pf_data,
                                                 STP_LATVEC_MASK latvec_state_mask,
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_PF_DATA *source_pf_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_PF_SEND_FIELD) / sizeof(sdFLOAT));
    //tpde_send_size += (sizeof(sSURFEL_PF_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_PF_SEND_FIELD field = reinterpret_cast<SURFEL_PF_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(field->m_rho_bar_ratio_pfld, rho_bar_ratio_pfld, sizeof(field->m_rho_bar_ratio_pfld));
    memcpy(field->m_u_bar_ratio_pfld, u_bar_ratio_pfld, sizeof(field->m_u_bar_ratio_pfld));
    field->m_pressure_pfld_ur = pressure_pfld_ur;
    //field->m_pf_qtm = pf_qtm;
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_PF_SEND_FIELD field = reinterpret_cast<SURFEL_PF_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(rho_bar_ratio_pfld, field->m_rho_bar_ratio_pfld, sizeof(field->m_rho_bar_ratio_pfld));
    memcpy(u_bar_ratio_pfld, field->m_u_bar_ratio_pfld, sizeof(field->m_u_bar_ratio_pfld));
    pressure_pfld_ur = field->m_pressure_pfld_ur;
    //pf_qtm = field->m_pf_qtm;
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};

using sSURFEL_PF_DATA = tSURFEL_PF_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_PF_DATA = sSURFEL_PF_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_PF_DATA);
#endif



#define DO_SURFEL_UDS_DATA(nth_uds,n_uds,surfel,surfel_uds_data) \
  asINT32 nth_uds;                                                         \
  auto surfel_uds_data = surfel->uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,surfel_uds_data++)

#define DO_SURFEL_V2S_UDS_DATA(nth_uds,n_uds,surfel, v2s_uds_data) \
  asINT32 nth_uds;                                                         \
  auto v2s_uds_data = surfel->v2s_uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,v2s_uds_data++)

#define DO_SURFEL_AND_V2S_UDS_DATA(nth_uds,n_uds,surfel,surfel_uds_data,      \
                             v2s_uds_data)	                              \
  asINT32 nth_uds;                                                         \
  auto surfel_uds_data = surfel->uds_data();                    \
  auto v2s_uds_data = surfel->v2s_uds_data();                  \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,surfel_uds_data++, v2s_uds_data++)

#define DO_SURFEL_S2S_UDS_DATA(nth_uds,n_uds,surfel, s2s_uds_data) \
  asINT32 nth_uds;                                                         \
  auto s2s_uds_data = surfel->s2s_uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,s2s_uds_data++)

#define DO_SAMPLING_SURFEL_UDS_DATA(nth_uds,n_uds,sampling_surfel,sampling_surfel_uds_data) \
  asINT32 nth_uds;                                                         \
  SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = sampling_surfel->uds_data(); \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,sampling_surfel_uds_data++)


// used in s2v to exlude scalars which do not participate in a scalar surfel
#define DO_ACTIVE_SURFEL_UDS_DATA(nth_uds,n_uds,surfel,surfel_uds_data) \
  asINT32 nth_uds;                                                         \
  auto surfel_uds_data = surfel->uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++, surfel_uds_data++)

#define DO_ACTIVE_SURFEL_V2S_UDS_DATA(nth_uds,n_uds,surfel,v2s_uds_data) \
  asINT32 nth_uds;                                                         \
  auto v2s_uds_data = surfel->v2s_uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++, v2s_uds_data++)

#define DO_ACTIVE_SURFEL_AND_V2S_UDS_DATA(nth_uds,n_uds,surfel,surfel_uds_data,      \
                             v2s_uds_data)	                              \
  asINT32 nth_uds;                                                         \
  SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data();                    \
  SURFEL_V2S_UDS_DATA v2s_uds_data = surfel->v2s_uds_data();                  \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,surfel_uds_data++, v2s_uds_data++)

#define DO_ACTIVE_SURFEL_S2S_UDS_DATA(nth_uds,n_uds,surfel,s2s_uds_data) \
  asINT32 nth_uds;                                                         \
  SURFEL_S2S_UDS_DATA s2s_uds_data = surfel->s2s_uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++, s2s_uds_data++)


// TO DO: use this in slrf_surfel_dynamics
#define DO_LRF_SURFEL_UDS_DATA(nth_uds,n_uds,surfels,surfels_uds_data)  \
  asINT32 nth_uds;                                                         \
  SURFEL_UDS_DATA surfels_uds_data[2];                                  \
  surfels_uds_data[0] = surfels[0]->uds_data();                         \
  surfels_uds_data[1] = surfels[1]->uds_data();                         \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,surfels_uds_data[0]++, surfels_uds_data[1]++) 


/*
#define DO_UBLK_UDS_DATA(nth_uds,n_uds,ublk,ublk_uds_data)       \
  asINT32 nth_uds;                                                         \
  auto ublk_uds_data = ublk->uds_data();                    \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,ublk_uds_data=ublk->uds_data(nth_uds)) 

#define DO_UBLK2_UDS_DATA(nth_uds,n_uds,ublk,ublk_uds_data,      \
                             ublk2,ubl2k_uds_data)                         \
  asINT32 nth_uds;                                                         \
  UBLK_UDS_DATA ublk_uds_data = ublk->uds_data();                    \
  UBLK_UDS_DATA ublk2_uds_data = ublk2->uds_data();                  \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,ublk_uds_data=ublk->uds_data(nth_uds), ublk2_uds_data=ublk2->uds_data(nth_uds))



#define DO_NEARBLK_UDS_DATA(nth_uds,n_uds,nearblk,nearblk_uds_data)       \
  asINT32 nth_uds;                                                         \
  auto nearblk_uds_data = nearblk->surf_uds_data();         \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,nearblk_uds_data=nearblk->surf_uds_data(nth_uds)) 

#define DO_UBLK_AND_SURFEL_UDS_DATA(nth_uds,n_uds,ublk,ublk_uds_data,surfel,surfel_uds_data) \
  asINT32 nth_uds;                                                         \
  auto ublk_uds_data = ublk->uds_data();                    \
  SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data();              \
  for (nth_uds = 0;                                                        \
       nth_uds < n_uds;                                                \
       nth_uds++,ublk_uds_data=ublk->uds_data(nth_uds),surfel_uds_data++)   

*/



/* # if DEBUG */
/* inline VOID print_ublk_scalar_value(asINT32 nth_scalar, UBLK ublk, asINT32 voxel)  */
/* { */
/*   UBLK_SCALAR_DATA ublk_scalar_data = get_ublk_scalar_data(nth_scalar, ublk); */
/*   printf("scalar_value: %f \n",ublk_scalar_data->q[0][voxel]); */
/* } */

/* inline VOID print_surfel_scalar_value(asINT32 nth_scalar, SURFEL surfel)  */
/* { */
/*   SURFEL_SCALAR_DATA surfel_scalar_data = get_surfel_scalar_data(nth_scalar, surfel);  */
/*   printf("scalar_value: %f \n",surfel_scalar_data->q_value); */
/* } */
/* #endif */


/* #define DO_SCALAR_DATA(nth_scalar,n_scalars,shob,shob_scalar_data,SHOB)     \ */
/*   asINT32 nth_scalar;                                                         \ */
/*   SHOB##_SCALAR_DATA shob_scalar_data = shob->scalar_data();                    \ */
/*   for (nth_scalar = 0;                                                        \ */
/*        nth_scalar < n_scalars;                                                \ */
/*        nth_scalar++,shob_scalar_data++)  */


} // inline namespace SIMULATOR_NAMESPACE

#endif//_SIMENG_T_SOLVER_DATA_H
