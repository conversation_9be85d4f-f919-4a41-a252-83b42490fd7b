/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 


#include <sys/stat.h>
#include "logging.h"

static pthread_t l_main_thread_id;

cLOG& get_log()
{
  pthread_t tid = pthread_self();
  if (tid == l_main_thread_id) {
    return main_log();
  }
  else {
    return comm_log();
  }
}

cLOG& main_log()
{
  static std::shared_ptr<cLOG> logger = cLOG_REGISTRY::getInstance().get_log("MAIN");
  return *logger;
};

cLOG& comm_log()
{
  static std::shared_ptr<cLOG> logger = cLOG_REGISTRY::getInstance().get_log("COMM");
  return *logger;
};

static
void add_sim_log_exceptions(std::shared_ptr<cLOG>& log)
{

  if(DEBUG_MLRF_PARCEL_COMM)
    log->add_exception("PARCEL_MLRF_COMM");
  
  if(DEBUG_PARTICLE_SUMMARY)
    log->add_exception("PARTICLE_SUMMARY");
  
  if (DEBUG_VARIABLE_POWERTHERM_COUPLING)
    log->add_exception("VARIABLE_POWERTHERM_COUPLING");

  if (DEBUG_BSURFEL_COMM)
    log->add_exception("BSURFEL_COMM");

  if (DEBUG_STRAND_SWITCHING)
    log->add_exception("STRAND_SWITCHING");

  if (DEBUG_STRANDMASTER)
    log->add_exception("STRANDMASTER");

  if (DEBUG_RECV_DEPENDENCIES)
    log->add_exception("RECV_DEPEND");

  if (DEBUG_BSURFEL_MEAS)
    log->add_exception("BSURFEL_MEAS");

  if (DEBUG_VTABLE)
    log->add_exception("VTABLE");

  if (DEBUG_BSURFEL_INIT)
    log->add_exception("BSURFEL_INIT");

  if (DEBUG_CONDUCTION)
    log->add_exception("CONDUCTION_SOLVER");

  if (DEBUG_SHELL_SURFEL_VERIFICATION)
    log->add_exception("SHELL_SURFEL_VERIFICATION");
  
  if (DEBUG_ASYNC_EVENTS)
    log->add_exception("ASYNC");

  if (DEBUG_GPU_MEM)
    log->add_exception("GPU_MEM");

  if (DEBUG_UDS)
    log->add_exception("LB_UDS");
    
  if (DEBUG_GPU_MEM)
    log->add_exception("GPU_INIT");

  if (DEBUG_GPU_GENERIC)
    log->add_exception("GPU_GENERIC");
    
 if (DEBUG_RADIATION)
    log->add_exception("RAD");
    
  if (DEBUG_PARALLEL_CKPT)
    log->add_exception("PCKPT");
}

// This function should only be called by the main thread,
// before creation of the comm thread
void initialize_loggers(int my_mpi_rank)
{
#if ENABLE_LOGGING
  l_main_thread_id = pthread_self();

  std::string sink_name("debug-" + std::to_string(my_mpi_rank) +".log");
  // std::string sink_name("stdout");
  std::string log_dir("./logs");

  std::string sys_cmd("mkdir -p "+log_dir);
  system(sys_cmd.c_str());

  cLOG_REGISTRY & log_registry = cLOG_REGISTRY::getInstance();
  log_registry.set_log_dir(log_dir);

  std::shared_ptr<cLOG> mlog = log_registry.get_log("MAIN",sink_name);
  mlog->turn_off();
  add_sim_log_exceptions(mlog);

  std::shared_ptr<cLOG> clog = log_registry.get_log("COMM",sink_name);
  clog->turn_off();
  add_sim_log_exceptions(clog);
#endif
}
