/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Voxel dynamics (functional sets and groups)
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "voxel_dyn_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "vr.h"
#include "sim.h"
#include "phys_type_map.h"
#include "comm_groups.h"
#include "common_math.h"

#include PHYSICS_H

sVOXEL_DYN_FSET g_pre_comm_voxel_dyn_fset;
sVOXEL_DYN_FSET g_post_comm_voxel_dyn_fset;

template<>
sFAN_SHARED_DATA_MAP sFAN_UBLK_DYNAMICS_DATA::c_fan_shared_data_map;
template<>
sTABLE_FAN_SHARED_DATA_MAP sTABLE_FAN_UBLK_DYNAMICS_DATA::c_table_fan_shared_data_map;
template<>
sPOROUS_SHARED_DATA_MAP sPOROUS_UBLK_DYNAMICS_DATA::c_porous_shared_data_map;
sCURVED_HX_POROUS_SHARED_DATA_MAP sCURVED_HX_POROUS_UBLK_DYNAMICS_DATA::c_curved_hx_porous_shared_data_map;
/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

// Converts all meas cell indices to pointers
VOID sVOXEL_DYN_GROUP::resolve_meas_cell_ptrs() 
{
  MEAS_CELL_PTR *meas_cell_ptr_vector = meas_cell_ptrs();
  asINT32 n_windows = n_meas_windows();
  BOOLEAN is_moving_ref_frame = lrf_is_rotating(lrf_physics_desc);
  ccDOTIMES(q, n_quantums()) {
    ccDOTIMES(w, n_windows) {
      MEAS_WINDOW window = meas_window_ptrs()[w].window();

#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
      if (!window->m_is_particle_trajectory_window) {
#endif

      DO_UBLK_MEAS_CELL_PTRS(meas_cell_ptr, meas_cell_ptr_vector) {
        STP_MEAS_CELL_INDEX meas_cell_index = window->m_meas_cell_index_map[meas_cell_ptr->index()].m_new_sp_cell_index;
        // for a dev window and a moving surfel, leave meas_cell_ptr as an index with an axis
        // also, mark for rotation into global ref frame
        if (window->is_development && is_moving_ref_frame) {
          uINT16 part_index = meas_cell_ptr->part();
          asINT32 axis = meas_cell_ptr->axis();
          meas_cell_ptr->set_index(meas_cell_index - window->entity_first_segment[axis][part_index]);
          meas_cell_ptr->mark_should_rotate_vector_to_grf();
        } else {
          MEAS_CELL_VAR *meas_cell = window->meas_cell(meas_cell_index);
          meas_cell_ptr->set_variables(meas_cell);
          if (window->contains_std_dev_vars) {
            sSTD_CELL_VAR *std_cell = meas_cell_ptr->std_cell_ptr(window->n_variables);
            std_cell->add_shob();
          }
          if (is_moving_ref_frame) {
            if (!window->is_output_in_local_csys) {
              meas_cell_ptr->mark_should_rotate_vector_to_grf();
            } else if (window->is_composite) {
              if (!window->meas_cell_output_in_local_csys[meas_cell_index])
                meas_cell_ptr->mark_should_rotate_vector_to_grf();
            }
          }
        }
      }
#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
        }
#endif
    }
  }
}

VOID reinitialize_dynamics_data_for_dependent_ublks(PHYSICS_DESCRIPTOR pd) {
  if (pd->is_body_physics_descriptor() || pd->is_fluid_physics_descriptor()) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {
        reinitialize_dependent_ublk_data(nearblk, pd);
      }
      DO_FARBLKS_OF_SCALE(farblk, scale) {
        reinitialize_dependent_ublk_data(farblk, pd);
      }
    }
  }
}

VOID post_seed_init_for_all_ublks(VOID)
{
  if ((g_timescale.m_time > 0) && (sCURVED_HX_POROUS_UBLK_DYNAMICS_DATA::c_curved_hx_porous_shared_data_map.size() > 0)) {

    std::vector<sCURVED_HX_POROUS_SHARED_DATA *> shared_datasets;
    sCURVED_HX_POROUS_UBLK_DYNAMICS_DATA::c_curved_hx_porous_shared_data_map.fill_shared_data_vector(shared_datasets);
    ccDOTIMES(n, shared_datasets.size()) {
      sCURVED_HX_POROUS_SHARED_DATA *shared_data = shared_datasets[n];
      shared_data->eval_T_and_H_at_vertices_for_curved_hx();
    }
  }
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      farblk->post_seed_init();
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      nearblk->post_seed_init();
    }
  }
}

#if !BUILD_5G_LATTICE
#if DEBUG
  constexpr asINT32 D_UBLK_ID = 13;
  constexpr asINT32 D_VOXEL = 6;
  constexpr asINT32 D_N_SCALES = 2;
#endif

/**
 @brief Compute the surfel contribution to matrix A 
 @details Let \f$ W_\alpha^D = W_{S,D}(\alpha), W_\alpha^N = W_{S,N}(\alpha) \f$, after the loop over the surfels in stencil, the following elements are added to the Matrix \f$ \textbf{A} \f$ previously calculated during voxel loop:
  \f[
  \textbf{A} += \begin{bmatrix}
  \sum_{\alpha} W_\alpha^D & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0) & \sum_{\alpha} W_\alpha^D(y_\alpha-y_0) & \sum_{\alpha} W_\alpha^D(z_\alpha-z_0) \\
  \sum_{\alpha} W_\alpha^D(x_\alpha-x_0) & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0)^2+W_\alpha^N\textbf{n}_{x\alpha}^2 & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0)(y_\alpha-y_0)+W_\alpha^N\textbf{n}_{x\alpha}\textbf{n}_{y\alpha} & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0)(z_\alpha-z_0)+W_\alpha^N\textbf{n}_{x\alpha}\textbf{n}_{z\alpha} \\
  \sum_{\alpha} W_\alpha^D(y_\alpha-y_0) & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0)(y_\alpha-y_0)+W_\alpha^N\textbf{n}_{x\alpha}\textbf{n}_{y\alpha} & \sum_{\alpha} W_\alpha^D(y_\alpha-y_0)^2+W_\alpha^N\textbf{n}_{y\alpha}^2 & \sum_{\alpha} W_\alpha^D(y_\alpha-y_0)(z_\alpha-z_0)+W_\alpha^N\textbf{n}_{z\alpha}\textbf{n}_{y\alpha} \\
  \sum_{\alpha} W_\alpha^D(z_\alpha-z_0) & \sum_{\alpha} W_\alpha^D(x_\alpha-x_0)(z_\alpha-z_0)+W_\alpha^N\textbf{n}_{x\alpha}\textbf{n}_{z\alpha} & \sum_{\alpha} W_\alpha^D(y_\alpha-y_0)(z_\alpha-z_0)+W_\alpha^N\textbf{n}_{y\alpha}\textbf{n}_{z\alpha} & \sum_{\alpha} W_\alpha^D(z_\alpha-z_0)^2+W_\alpha^N\textbf{n}_{z\alpha}^2 \\
  \end{bmatrix}
  \f]

*/
static
VOID ls_grad_load_surfel_info(SURFEL surfel,
                              const std::unordered_map<sINT32, sINT32>& ls_voxel_id_map,
                              MAT_A_4x4 * mat_a)
{
  const asINT32 n_dims = sim.num_dims;
  const asINT32 allowed_even_odd_phase_mask = STP_ODD_S2V_PHASE_MASK;

  if (surfel->is_conduction_surface()) {
    SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
    asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

    for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
      UBLK ublk                  = ublk_interaction->ublk();

      // We need not go over ghost UBLKs in the surfel loop
      if (ublk->is_ghost()) continue;

      uINT8 *voxel_info          = ublk_interaction->voxel_info();
      auINT32 n_weight_sets      = ublk_interaction->m_n_weight_sets;

      ccDOTIMES (iv, n_weight_sets) {

        // CONDUCTION-TODO: do we have to exclude those interactions that are only speed 2?
        sSURFEL_VOXEL_INTERACTION s_v_interaction;
        voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
        asINT32 n_voxel_weights = s_v_interaction.n_voxel_weights();
        asINT32 phase_mask = s_v_interaction.phase_mask();

        if ((phase_mask == STP_ALL_PHASE_MASK)
            || (!surfel->is_even() && (allowed_even_odd_phase_mask & phase_mask))) {

          asINT32 voxel = s_v_interaction.voxel_id();
          
#if CONDUCTION_ENABLE_PTHRU_TO_SURFELS
          // Surfel's effective volume in passthrough implementation is a the vsurfel_ratio * excluded_volume
          // where the excluded volume is (1 - pfluid)
          auto vsurfel_ratio = s_v_interaction.vsurfel_ratio();
          sdFLOAT voxel_pfluid = ublk->is_near_surface() ? ublk->surf_geom_data()->pfluids[voxel] : 1.0;
          sdFLOAT surfel_eff_vol = vsurfel_ratio*(1.0 - voxel_pfluid);
          auto* surfel_conduction = surfel->conduction_data();
          BOOLEAN surfel_accepts_pthru = FALSE;
#if CONDUCTION_ENABLE_PTHRU_TO_DIRICHLET_BOUNDARIES
          surfel_accepts_pthru |= surfel_conduction->is_prescribed_temp();
#endif
#if CONDUCTION_ENABLE_PTHRU_TO_COUPLED_BOUNDARIES
          surfel_accepts_pthru |= (surfel_conduction->bc_type == INTERFACE_BC);
#endif
#if CONDUCTION_ENABLE_PTHRU_ACROSS_LRF
          surfel_accepts_pthru |= surfel->is_lrf();
#endif
          if (surfel_accepts_pthru && !ublk->is_vr_fine()) {
            ublk->surf_conduction_data()->passthrough_summ_inv[voxel] += surfel_eff_vol;
          }
#endif

          asINT32 unique_id = unique_ublk_voxel_id(ublk->id(), voxel);
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
          auto got = ls_voxel_id_map.find(unique_id);
          if (got == ls_voxel_id_map.end())
            msg_internal_error("Surfel %d is looking for voxel %d(%d) which is not on map of size %lu", surfel->id(),
                ublk->id(), voxel, ls_voxel_id_map.size());
#endif
          asINT32 voxel_id = ls_voxel_id_map.at(unique_id);

          sdFLOAT weight_dirichlet;
          sdFLOAT weight_neumann;
          sdFLOAT term_dirichlet[4];
          sdFLOAT term_neumann[4];
          compute_ls_surfel_weights(ublk, voxel, surfel, weight_dirichlet, weight_neumann,
              term_dirichlet, term_neumann);

          ccDOTIMES (i, 4) {
            ccDOTIMES (j, 4)
              mat_a[voxel_id][i][j] += weight_dirichlet * term_dirichlet[j] * term_dirichlet[i]
                                       + weight_neumann * term_neumann[j] * term_neumann[i];
          }

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          {
            if (ublk->id() == D_UBLK_ID && voxel == D_VOXEL && sim.num_scales == D_N_SCALES) {
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("After surfel %d (%.4f,%.4f,%.4f)", surfel->id(),
                  surfel->centroid[0], surfel->centroid[1], surfel->centroid[2]);
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Matrix A:");
              // LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
              //     mat_a[voxel_id][0][0], mat_a[voxel_id][0][1], mat_a[voxel_id][0][2], mat_a[voxel_id][0][3]);
              // LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
              //     mat_a[voxel_id][1][0], mat_a[voxel_id][1][1], mat_a[voxel_id][1][2], mat_a[voxel_id][1][3]);
              // LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
              //     mat_a[voxel_id][2][0], mat_a[voxel_id][2][1], mat_a[voxel_id][2][2], mat_a[voxel_id][2][3]);
              // LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
              //     mat_a[voxel_id][3][0], mat_a[voxel_id][3][1], mat_a[voxel_id][3][2], mat_a[voxel_id][3][3]);
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
                  weight_dirichlet * term_dirichlet[0] * term_dirichlet[0] + weight_neumann * term_neumann[0] * term_neumann[0],
                  weight_dirichlet * term_dirichlet[1] * term_dirichlet[0] + weight_neumann * term_neumann[1] * term_neumann[0],
                  weight_dirichlet * term_dirichlet[2] * term_dirichlet[0] + weight_neumann * term_neumann[2] * term_neumann[0],
                  weight_dirichlet * term_dirichlet[3] * term_dirichlet[0] + weight_neumann * term_neumann[3] * term_neumann[0]);
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
                  weight_dirichlet * term_dirichlet[0] * term_dirichlet[1] + weight_neumann * term_neumann[0] * term_neumann[1],
                  weight_dirichlet * term_dirichlet[1] * term_dirichlet[1] + weight_neumann * term_neumann[1] * term_neumann[1],
                  weight_dirichlet * term_dirichlet[2] * term_dirichlet[1] + weight_neumann * term_neumann[2] * term_neumann[1],
                  weight_dirichlet * term_dirichlet[3] * term_dirichlet[1] + weight_neumann * term_neumann[3] * term_neumann[1]);
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
                  weight_dirichlet * term_dirichlet[0] * term_dirichlet[2] + weight_neumann * term_neumann[0] * term_neumann[2],
                  weight_dirichlet * term_dirichlet[1] * term_dirichlet[2] + weight_neumann * term_neumann[1] * term_neumann[2],
                  weight_dirichlet * term_dirichlet[2] * term_dirichlet[2] + weight_neumann * term_neumann[2] * term_neumann[2],
                  weight_dirichlet * term_dirichlet[3] * term_dirichlet[2] + weight_neumann * term_neumann[3] * term_neumann[2]);
              LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
                  weight_dirichlet * term_dirichlet[0] * term_dirichlet[3] + weight_neumann * term_neumann[0] * term_neumann[3],
                  weight_dirichlet * term_dirichlet[1] * term_dirichlet[3] + weight_neumann * term_neumann[1] * term_neumann[3],
                  weight_dirichlet * term_dirichlet[2] * term_dirichlet[3] + weight_neumann * term_neumann[2] * term_neumann[3],
                  weight_dirichlet * term_dirichlet[3] * term_dirichlet[3] + weight_neumann * term_neumann[3] * term_neumann[3]);
            }
          }
#endif
        }
        //Skip all the latvecs
        voxel_info += n_voxel_weights;
      }
    }
  }
}

// CONDUCTION-TODO: Rename all ls_grad_ functions for consistency
VOID compute_ublk_ls_grad_coefficients(VOID)
{
  /*****************************************************************************
   * 1. Count the number of near-surface voxels and voxel_surfel_interactions
   *    on current proc
   * 2. Allocate appropriate amount of space for matrices and offset
   *    information
   * 3. Load the matrices with voxel information and store voxel offsets
   * 4. Load the matrices with surfel information
   * 5. Compute least square coefficients and store on the UBLK
   * 6. Discard and free allocated memory.
   ****************************************************************************/
  const asINT32 n_dims = sim.num_dims;
  asINT32 voxel_count = 0;

  //CONDUCTION-CHECK - move this to a separate function?
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        ccDOTIMES (voxel, N_VOXELS_8)
          voxel_count += nearblk->fluid_like_voxel_mask.test(voxel) ? 1 : 0;
      }
    }
  }

  //CONDUCTION-TODO: Change the n_voxels from 27 to 19
  const asINT32 n_voxels = 27;
  asINT32 voxel_surfel_interactions_count = ls_count_voxel_surfel_interactions();

  /** Initial storage for Matrix A accumulation is defined double to avoid precision losses
   *  due to change in order of surfels (when running multi-proc) */
  auto mat_a = std::make_unique<MAT_A_4x4[]>(voxel_count);
  auto mat_b_voxel = std::make_unique<MAT_B_4x27[]>(voxel_count);

  std::unordered_map<sINT32, sINT32> ls_voxel_id_map;

  asINT32 voxel_id = 0;

  // Loading voxel information
  DO_SCALES_FINE_TO_COARSE(scale) {
    //CONDUCTION-CHECK - does this loop over ghost UBLKs as well? We do not need those.
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        nearblk_ls_coefficients(nearblk, scale, mat_a.get(), mat_b_voxel.get(), voxel_id, ls_voxel_id_map);
      }
    }
  }

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  {
    if (sim.num_scales == D_N_SCALES) {
      voxel_id = ls_voxel_id_map[unique_ublk_voxel_id(D_UBLK_ID, D_VOXEL)];

      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("After voxel loop:");
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Matrix A:");
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][0][0], mat_a[voxel_id][0][1], mat_a[voxel_id][0][2], mat_a[voxel_id][0][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][1][0], mat_a[voxel_id][1][1], mat_a[voxel_id][1][2], mat_a[voxel_id][1][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][2][0], mat_a[voxel_id][2][1], mat_a[voxel_id][2][2], mat_a[voxel_id][2][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][3][0], mat_a[voxel_id][3][1], mat_a[voxel_id][3][2], mat_a[voxel_id][3][3]);
    }
  }
#endif

  const asINT32 allowed_even_odd_phase_mask = STP_ODD_S2V_PHASE_MASK;

  // Loading surfel information
  DO_SCALES_FINE_TO_COARSE(scale) {
    {
      DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        ls_grad_load_surfel_info(surfel, ls_voxel_id_map, mat_a.get());
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Real surfel %d into LS grad", surfel->id());
#endif
        if (surfel->has_mirror()) {
          SURFEL mirror_surfel = surfel->mirror_data()->m_mirror_surfel;
          ls_grad_load_surfel_info(mirror_surfel, ls_voxel_id_map, mat_a.get());
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          LOG_MSG("CONDUCTION_SOLVER").printf("Mirror (dyn) surfel %d into LS grad", mirror_surfel->id());
#endif
        }
      }
    }
    {
      DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
        ls_grad_load_surfel_info(surfel, ls_voxel_id_map, mat_a.get());
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Real surfel %d into LS grad", surfel->id());
#endif
        if (surfel->has_mirror()) {
          SURFEL mirror_surfel = surfel->mirror_data()->m_mirror_surfel;
          ls_grad_load_surfel_info(mirror_surfel, ls_voxel_id_map, mat_a.get());
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          LOG_MSG("CONDUCTION_SOLVER").printf("Mirror (dyn) surfel %d into LS grad", mirror_surfel->id());
#endif
        }
      }
    }
    {
      DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
        ls_grad_load_surfel_info(wsurfel_flow, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Real surfel %d into LS grad", wsurfel_flow->id());
  #endif
        if (wsurfel_flow->has_mirror()) {
          SURFEL mirror_surfel = wsurfel_flow->mirror_data()->m_mirror_surfel;
          ls_grad_load_surfel_info(mirror_surfel, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          LOG_MSG("CONDUCTION_SOLVER").printf("Mirror (dyn) surfel %d into LS grad", mirror_surfel->id());
  #endif
        }
      }
    }
    {
      DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
        ls_grad_load_surfel_info(wsurfel_conduction, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Real surfel %d into LS grad", wsurfel_conduction->id());
  #endif
        if (wsurfel_conduction->has_mirror()) {
          SURFEL mirror_surfel = wsurfel_conduction->mirror_data()->m_mirror_surfel;
          ls_grad_load_surfel_info(mirror_surfel, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          LOG_MSG("CONDUCTION_SOLVER").printf("Mirror (dyn) surfel %d into LS grad", mirror_surfel->id());
  #endif
        }
      }
    }
    {
      DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
        ls_grad_load_surfel_info(contact_surfel, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Real surfel %d into LS grad", contact_surfel->id());
  #endif
        if (contact_surfel->has_mirror()) {
          SURFEL mirror_surfel = contact_surfel->mirror_data()->m_mirror_surfel;
          ls_grad_load_surfel_info(mirror_surfel, ls_voxel_id_map, mat_a.get());
  #if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          LOG_MSG("CONDUCTION_SOLVER").printf("Mirror (dyn) surfel %d into LS grad", mirror_surfel->id());
  #endif
        }
      }
    }
    {
      DO_GHOST_SURFELS_OF_SCALE(quantum, scale) {
        ls_grad_load_surfel_info(quantum.m_surfel, ls_voxel_id_map, mat_a.get());
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER").printf("Ghost surfel %d into LS grad", surfel->id());
#endif
        // Mirrors of ghost surfels, if any, would be part of ghost surfel
        // list itself and do not need following explicit code
      }
    }
    {
      DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_group, scale) {
	DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_group) {
	  if (!tagged_mlrf_surfel.is_surfel_weightless()) {
	    SURFEL mlrf_surfel = tagged_mlrf_surfel.mlrf_surfel();	
	    ls_grad_load_surfel_info(mlrf_surfel, ls_voxel_id_map, mat_a.get());
	  }
	}
      }
    }
  }
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  {
    if (sim.num_scales == D_N_SCALES) {
      voxel_id = ls_voxel_id_map[unique_ublk_voxel_id(D_UBLK_ID, D_VOXEL)];

      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("After surfel loop:");
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Matrix A:");
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][0][0], mat_a[voxel_id][0][1], mat_a[voxel_id][0][2], mat_a[voxel_id][0][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][1][0], mat_a[voxel_id][1][1], mat_a[voxel_id][1][2], mat_a[voxel_id][1][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][2][0], mat_a[voxel_id][2][1], mat_a[voxel_id][2][2], mat_a[voxel_id][2][3]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g",
          mat_a[voxel_id][3][0], mat_a[voxel_id][3][1], mat_a[voxel_id][3][2], mat_a[voxel_id][3][3]);
    }
  }
#endif

  asINT32 basis_size = sim.num_dims + 1;

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        DO_VOXELS_IN_MASK(voxel, nearblk->fluid_like_voxel_mask) {

          asINT32 voxel_id = ls_voxel_id_map[unique_ublk_voxel_id(nearblk->id(), voxel)];

          dFLOAT mat_a_temp[4][4] = { };
          for (asINT32 i = 0; i < 4; i++) {
            for (asINT32 j = 0; j < 4; j++) {
              mat_a_temp[i][j] = mat_a[voxel_id][i][j];
            }
          }

          dFLOAT mat_a_inv[4][4];
          std::pair<BOOLEAN, dFLOAT> is_invertible_and_determinant = basis_size == 4
                                     ? invert_partial_matrix<4, 4>(mat_a_temp, mat_a_inv)
                                     : invert_partial_matrix<4, 3>(mat_a_temp, mat_a_inv);
          BOOLEAN is_invertible = is_invertible_and_determinant.first;
          dFLOAT determinant = is_invertible_and_determinant.second;

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
          if (nearblk->id() == D_UBLK_ID && voxel == D_VOXEL) {
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Original stored matrix A:");
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a[voxel_id][0][0], mat_a[voxel_id][0][1], mat_a[voxel_id][0][2], mat_a[voxel_id][0][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a[voxel_id][1][0], mat_a[voxel_id][1][1], mat_a[voxel_id][1][2], mat_a[voxel_id][1][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a[voxel_id][2][0], mat_a[voxel_id][2][1], mat_a[voxel_id][2][2], mat_a[voxel_id][2][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a[voxel_id][3][0], mat_a[voxel_id][3][1], mat_a[voxel_id][3][2], mat_a[voxel_id][3][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Temporary matrix A:");
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_temp[0][0], mat_a_temp[0][1], mat_a_temp[0][2], mat_a_temp[0][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_temp[1][0], mat_a_temp[1][1], mat_a_temp[1][2], mat_a_temp[1][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_temp[2][0], mat_a_temp[2][1], mat_a_temp[2][2], mat_a_temp[2][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_temp[3][0], mat_a_temp[3][1], mat_a_temp[3][2], mat_a_temp[3][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Inverse of matrix A:");
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_inv[0][0], mat_a_inv[0][1], mat_a_inv[0][2], mat_a_inv[0][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_inv[1][0], mat_a_inv[1][1], mat_a_inv[1][2], mat_a_inv[1][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_inv[2][0], mat_a_inv[2][1], mat_a_inv[2][2], mat_a_inv[2][3]);
            LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%g %g %g %g", mat_a_inv[3][0], mat_a_inv[3][1], mat_a_inv[3][2], mat_a_inv[3][3]);
          }
#endif
          if (!is_invertible) {

            // Voxel location
            STP_COORD voxel_lattice_location[3];
            nearblk->get_voxel_location(voxel, voxel_lattice_location);
            STP_GEOM_VARIABLE voxel_location[3];
            vcopy(voxel_location, voxel_lattice_location);

            simerr_report_error_code(SP_EER_LEAST_SQUARES_INVERSION_FAILED, nearblk->scale(), voxel_location, 
                                     voxel_id, 
                                     nearblk->id(), 
                                     determinant,
                                     "" 
                                     );

            // Now set the matrix to zero. This effectively makes the gradient zero.
            sdFLOAT retransform_matrix[4][4] = {  {1, 0,        0,        0       },
                                                  {0, 0,0, 0},
                                                  {0, 0, 0, 0},
                                                  {0, 0, 0, 0} };

            sdFLOAT mat_a_inv_times_b_voxel[4][n_voxels];
            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < n_voxels; j++) {
                sdFLOAT sum = 0.0;
                for (asINT32 k = 0; k < 4; k++) {
                  sum += mat_a_inv[i][k] * mat_b_voxel[voxel_id][k][j];
                }
                mat_a_inv_times_b_voxel[i][j] = sum;
              }
            }

            sdFLOAT coeff_matrix[4][n_voxels];
            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < n_voxels; j++) {
                sdFLOAT sum = 0.0;
                for (asINT32 k = 0; k < 4; k++) {
                  sum += retransform_matrix[i][k] * mat_a_inv_times_b_voxel[k][j];
                }
                coeff_matrix[i][j] = sum;
              }
            }

            NEARBLK_CONDUCTION_DATA nearblk_conduction = nearblk->surf_conduction_data();

            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < 4; j++) {
                nearblk_conduction->ls_mat_a_inv[i][j][voxel] = retransform_matrix[i][j];
              }

              for (asINT32 s = 0; s < n_voxels; s++) {
                nearblk_conduction->ls_grad_coeff_matrix[i][s][voxel] = coeff_matrix[i][s];
              }
            }
 
          }
          else { // Inversion is successful, can go ahead with geting gradient coefficients
            // CONDUCTION-TODO: Retransform code is redundant - we do not use this any more
            // since the use of surfel info is sufficient to ensure mat_a is invertible
            sdFLOAT dir_1[3];
            sdFLOAT dir_2[3];
            sdFLOAT dir_3[3];

            dir_1[0] = 1.0; dir_1[1] = 0.0; dir_1[2] = 0.0;
            dir_2[0] = 0.0; dir_2[1] = 1.0; dir_2[2] = 0.0;
            dir_3[0] = 0.0; dir_3[1] = 0.0; dir_3[2] = 1.0;

            // Final coefficient matrix is: retransform_matrix * mat_a_inv * mat_b_voxel
            // This gives a matrix of size (4 x n_voxels)
            sdFLOAT retransform_matrix[4][4] = {  {1, 0,        0,        0       },
                                                  {0, dir_1[0], dir_2[0], dir_3[0]},
                                                  {0, dir_1[1], dir_2[1], dir_3[1]},
                                                  {0, dir_1[2], dir_2[2], dir_3[2]} };

            sdFLOAT mat_a_inv_times_b_voxel[4][n_voxels];
            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < n_voxels; j++) {
                sdFLOAT sum = 0.0;
                for (asINT32 k = 0; k < 4; k++) {
                  sum += mat_a_inv[i][k] * mat_b_voxel[voxel_id][k][j];
                }
                mat_a_inv_times_b_voxel[i][j] = sum;
              }
            }

            sdFLOAT coeff_matrix[4][n_voxels];
            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < n_voxels; j++) {
                sdFLOAT sum = 0.0;
                for (asINT32 k = 0; k < 4; k++) {
                  sum += retransform_matrix[i][k] * mat_a_inv_times_b_voxel[k][j];
                }
                coeff_matrix[i][j] = sum;
              }
            }

            NEARBLK_CONDUCTION_DATA nearblk_conduction = nearblk->surf_conduction_data();

            for (asINT32 i = 0; i < 4; i++) {
              for (asINT32 j = 0; j < 4; j++) {
                nearblk_conduction->ls_mat_a_inv[i][j][voxel] = mat_a_inv[i][j];
              }

              for (asINT32 s = 0; s < n_voxels; s++) {
                nearblk_conduction->ls_grad_coeff_matrix[i][s][voxel] = coeff_matrix[i][s];
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
                if (i == 0
                    && (nearblk->id() == D_UBLK_ID && voxel == D_VOXEL && sim.num_scales == D_N_SCALES)) {
                  LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%d %g %g %g", s, coeff_matrix[1][s], coeff_matrix[2][s], coeff_matrix[3][s]);
                }
#endif
              }
            }
 
          }
        }
      }
    }
  }

  ls_voxel_id_map.clear();
}
#endif
