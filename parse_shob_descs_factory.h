#ifndef PARSE_SHOB_DESCS_FACTORY_H
#define PARSE_SHOB_DESCS_FACTORY_H
#include "common_sp.h"
#include <exception>
#include <string>
#include PHYSICS_H

typedef sSAMPLING_SURFEL *SAMPLING_SURFEL;


namespace SIM_SIZES {
  struct sUBLK_STATIC_INFO;
}

//Basic custom exception
struct sSHOB_PARSER_EXCEPTION : public std::exception {

  const char* what() const noexcept {
    return m_except_msg;
  }
  
  //Attributes
  const static int BUFFER_CAPACITY = 1024;

  //One can write to this buffer conveniently with
  //sprintf before throwing the exception
  char m_except_msg[BUFFER_CAPACITY];
};

inline namespace SIMULATOR_NAMESPACE {
/*----------------------------------------------------------------------------
 * cBASE_DESCS_PARSER
 *
 * Abstract class that enforces the template pattern common to all descriptor
 * parsers.
 *----------------------------------------------------------------------------*/

class cBASE_SHOB_DESCS_PARSER {
  
 public:

  //For a client like SimSizes, only the descriptor mush be
  //read in order to determine the size of the SHOB
  //This enum allows for control over allocation
  //Default behavior is to parser with allocation
  
  enum PARSE_TYPE {PARSE_WITH_ALLOCATION, PARSE_WITHOUT_ALLOCATION};

  /*Template pattern that every derived class should
   *adhere to, to be consistent with the design
   *
   *To exit from the parsing routine at any stage
   * a sSHOB_PARSER_EXCEPTION can be thrown. This is
   * more flexible and cleaner than introducing booleans
   * which might have to be propogated across multiple
   * call levels before a decision can be made.
   */

  VOID parse() {
    try {
      read_descriptor();
      if (m_parse_type == PARSE_WITH_ALLOCATION) {
        sSHOB* shob  = allocate();
        post_process(shob);
      }
    }
    catch (std::exception& ex) {
      exception_handler(ex);
    }
  }

  VOID set_parse_type(PARSE_TYPE ptype) {
    m_parse_type = ptype;
  }
  
  virtual size_t SIZE() const = 0;

 protected:
 cBASE_SHOB_DESCS_PARSER():m_parse_type(PARSE_WITH_ALLOCATION){};
  virtual ~cBASE_SHOB_DESCS_PARSER(){};

  //For Debugging, use the ofs stream to write
  //shob sizes. The number of shobs written per
  //file is controlled by shobs_per_file
  static std::ofstream ofs;
  static size_t shob_count;
  static int file_count;
  const size_t shobs_per_file = 10000;
  VOID update_ofs();
  
 private:
  virtual VOID read_descriptor() = 0;
  virtual sSHOB* allocate() = 0;
  virtual VOID post_process(sSHOB* shob) = 0;

  PARSE_TYPE m_parse_type;

  VOID exception_handler(const std::exception& ex) {
    msg_error("%s", ex.what());
  }
};


/*----------------------------------------------------------------------------
 * sPGRAM_VOL
 * Struct for storing pgram related information. This struct is
 * populated in cPARSE_SURFEL_DESCS_BASE::compute_pgram_volumes
 *----------------------------------------------------------------------------*/
struct sPGRAM_VOL {
  dFLOAT inv_total_pgram_volume_d39;
  dFLOAT inv_total_pgram_volume_d19;
  dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES];

sPGRAM_VOL():inv_total_pgram_volume_d39(0),
             inv_total_pgram_volume_d19(0) {
}

};

/*----------------------------------------------------------------------------
* BASE SURFEL DESCRIPTOR PARSER
*
* An abstract class that implements most utility functions that are common
* to all derived surfel descriptor classes.
*
* Derived classes are forced to override the "post_process" method, since
* different surfel types are initialized and added to different groups
*----------------------------------------------------------------------------*/

class cBASE_SURFEL_DESCS_PARSER : public cBASE_SHOB_DESCS_PARSER {

 public:

  VOID set_surfel_desc(DGF_SURFEL_DESC sdesc) { m_surfel_desc = sdesc; };
  VOID set_ghost_sps(std::vector<cDGF_SURFEL_PROC_GHOST>& gsps) { m_ghost_sps = gsps; };
  VOID set_home_sp(STP_PROC hsp) { m_home_sp = hsp; };
  VOID set_ghost_flags(uINT16 ghost_flags) { m_ghost_flags = ghost_flags; }
  VOID set_rp(RP_PROC rp) { m_rp = rp; };
  VOID set_backside_rp(RP_PROC rp) { m_backside_rp = rp; };
  VOID set_phys_type(STP_PHYSTYPE_TYPE type, STP_PHYSTYPE_TYPE back_type = STP_INVALID_PHYSTYPE_TYPE) { 
    m_phys_type = type; 
    m_back_phys_type = back_type;
  };
  VOID set_realm(STP_REALM realm) { m_realm = realm; };

  //reset - resets the key properties of the parser object
  virtual VOID reset();

  //Returns the size of the surfel as defined in the descriptor
  //Other properties taken into account while computing size
  //include ghosting information and physics type of the surfel
  virtual size_t SIZE() const override;

  cBASE_SURFEL_DESCS_PARSER();
  virtual ~cBASE_SURFEL_DESCS_PARSER();

 protected:
  //Overrides the base class read_descriptor method
  //Used to extract useful features from the surface descriptor
  //and prepopulate data members
  virtual VOID read_descriptor() override;

  //Allocates memory for the surfel
  virtual sSHOB* allocate() override;

  //This method is pure virtual and is unique to the type
  //of surfel or derive class parsers
  virtual VOID post_process(sSHOB* shob) override = 0;

  //Populates the physics type of the surfel based on
  //the surface descriptor. This methods is called in the
  //read_descriptor method. Derived classes can customize
  //this behavior when necessary.
  virtual VOID compute_phys_type_from_surface_desc();
  
  //Helpers
  BOOLEAN is_sampling_surfel() const;
  BOOLEAN is_reference_frame() const;
  BOOLEAN is_mirror() const;
  BOOLEAN is_ghost() const;
  BOOLEAN is_sliding_mesh() const;
  BOOLEAN has_ghosts() const;
  BOOLEAN has_same_realm_ghosts() const;
  BOOLEAN is_mirror_of_fringe_surfel() const;
  STP_EVEN_ODD even_odd() const;
  BOOLEAN is_fringe() const;
  BOOLEAN is_S2S_destination() const;   
  BOOLEAN interacts_with_conduction_volume() const;
  BOOLEAN is_conduction_shell() const;
  BOOLEAN is_conduction_open_shell() const;
  BOOLEAN is_conduction_open_shell_boundary() const;
  BOOLEAN is_conduction_surfel() const;
  BOOLEAN is_conduction_interface() const;

  BOOLEAN is_radiation() const;
  
  //Helpers for read_descriptor
  VOID init_seed_from_meas_data();
  VOID convert_surfel_desc_area_to_local_units();

  //Helpers for post process
  VOID init_basic_surfel_props(SURFEL);
  VOID compute_pgram_volumes(SURFEL);
  VOID fill_even_odd_data(SURFEL);
  VOID add_S2S_advect_data(SURFEL);
  VOID fill_uds_data(SURFEL);
  VOID fill_conduction_data(SURFEL);
  VOID fill_conduction_shell_data(SURFEL);
  VOID fill_radiation_data(SURFEL);
  VOID fill_mirror_data(SURFEL surfel);
  VOID add_ublk_interactions(sSURFACE_SHOB* surfel);
  VOID maybe_init_surfel_stencil_info(SURFEL);
  VOID read_surfel_ckpt_data(SURFEL);
  VOID read_surfel_pair_ckpt_data(SURFEL); // Extra ckpt data for APM surfel pairs
  
  //PROPERTIES or DATA MEMBERS
  REALM m_realm;
  DGF_SURFEL_DESC m_surfel_desc;
  STP_PHYSTYPE_TYPE m_phys_type;
  STP_PHYSTYPE_TYPE m_back_phys_type;
  PHYSICS_DESCRIPTOR m_surface_phys_desc;
  PHYSICS_DESCRIPTOR m_face_surface_phys_desc;
  std::vector<cDGF_SURFEL_PROC_GHOST>& m_ghost_sps;
  PHYSICS_DESCRIPTOR m_back_face_surface_phys_desc;
  STP_PROC m_home_sp;
  uINT16 m_ghost_flags;
  RP_PROC m_rp;
  RP_PROC m_backside_rp;
  cDGF_SEED_FROM_MEAS_DATA m_seed_from_meas_data;
  sPGRAM_VOL m_pgram_info;

};

 asINT32 get_surfel_weight_set_size(DGF_SURFEL_DESC surfel_desc);
 asINT32 surfel_type_from_desc(BOOLEAN is_S2S_destination, 
                               STP_EVEN_ODD even_odd, 
                               auINT32 flags,  
                               BOOLEAN is_ghost, 
                               BOOLEAN has_ghosts,
                               BOOLEAN is_fringe,
                               BOOLEAN is_conduction_shell,
                               BOOLEAN is_conduction_interface,
                               BOOLEAN is_radiation);
 
//----------------------------------------------------------------------------
// PARSE REGULAR SURFEL DESCRIPTOR
//----------------------------------------------------------------------------

class cREGULAR_SURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {
  
  SURFEL_GROUP_SUPERTYPE surfel_group_supertype() const;
  virtual VOID post_process(sSHOB* shob) override;

 private:
  VOID fill_dynamics_data(SURFEL surfel);
  VOID fill_meas_windows(SURFEL surfel);

 public:  
  std::map <SURFEL_ID, sSURFEL*> m_opposite_parsing_maps[STP_N_REALMS];
};

//----------------------------------------------------------------------------
// MIRROR SURFEL DESCRIPTOR PARSER
//----------------------------------------------------------------------------

class cMIRROR_SURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {
  
  virtual VOID post_process(sSHOB* shob) override;
  virtual VOID compute_phys_type_from_surface_desc() override;

};

//----------------------------------------------------------------------------
// GHOST SURFEL DESCRIPTOR PARSER
//----------------------------------------------------------------------------

class cGHOST_SURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {
  
  virtual VOID post_process(sSHOB* shob) override;
  virtual VOID compute_phys_type_from_surface_desc() override;

 private:
  VOID add_ghost_surfel_quantum(SURFEL surfel);
  VOID add_ghost_wsurfel(SURFEL surfel, BOOLEAN is_main_recv_group);
  VOID add_ghost_contact(SURFEL surfel, BOOLEAN is_main_recv_group);
  VOID maybe_init_conduction_ghost_surfel_info(SURFEL surfel);

};

//----------------------------------------------------------------------------
// ISURFEL DESCRIPTOR PARSER
//----------------------------------------------------------------------------

class cISURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {
  
  virtual VOID post_process(sSHOB* shob) override;

 private:
  VOID update_isurfel_meas_windows_and_groups(SURFEL);
};


//----------------------------------------------------------------------------
// LRF SURFEL DESCRIPTOR PARSER
//----------------------------------------------------------------------------

class cLRF_SURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {

  virtual VOID post_process(sSHOB* shob) override;
  virtual VOID compute_phys_type_from_surface_desc() override;

 private:
  VOID update_lrf_surfel_groups(SURFEL);

};

//----------------------------------------------------------------------------
// SAMPLING SURFEL DESCRIPTOR PARSER
//----------------------------------------------------------------------------

class cSAMPLING_SURFEL_DESC_PARSER : public cBASE_SURFEL_DESCS_PARSER {

  virtual VOID post_process(sSHOB* shob) override;
  virtual VOID read_descriptor() override;
  virtual sSHOB* allocate() override;
  virtual size_t SIZE() const override;

 private:
  virtual VOID update_groups(SAMPLING_SURFEL);
 protected:
  VOID init_basic_surfel_props(SAMPLING_SURFEL);
  VOID fill_meas_windows(SAMPLING_SURFEL);
  VOID maybe_init_surfel_stencil_info(SAMPLING_SURFEL sampling_surfel);
  VOID read_surfel_ckpt_data(SAMPLING_SURFEL);
};

//----------------------------------------------------------------------------
// GHOST SAMPLING DESCRIPTOR PARSER
// Used by Particle Modeling
//----------------------------------------------------------------------------

class cGHOST_SAMPLING_SURFEL_DESC_PARSER : public cSAMPLING_SURFEL_DESC_PARSER {
  virtual VOID post_process(sSHOB* shob) override;
  VOID add_ghost_sampling_surfel_quantum(SAMPLING_SURFEL sampling_surfel);
};

//----------------------------------------------------------------------------
// SURFEL DESC PARSER FACTORY
// Manufactures all the surfel descriptor parser objects based on the
// surface descriptor and ghosting information
//----------------------------------------------------------------------------

class cSURFEL_DESC_PARSER_FACTORY {

 public:
  static cBASE_SURFEL_DESCS_PARSER* create_surfel_desc_parser(STP_REALM realm,
                                                              DGF_SURFEL_DESC surfel_desc,
                                                              STP_PROC home_sp,
                                                              RP_PROC rp,
                                                              RP_PROC backside_rp,
                                                              uINT16 ghost_flags,
                                                              std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps);

  VOID sanity_check_paired_contact_surfels_internal() {
    //This method should be called after all conduction surfels are parsed, when no paired contact surfel should remain
    //in the map.
    size_t map_size = regular_surfel_desc_parser.m_opposite_parsing_maps[STP_COND_REALM].size();
    if (map_size > 0) {
      //If reached here, it implies  that some surfels have not been added to the groups since paired surfels are 
      //loaded only after the second one of the pair is parsed, with the first one staying temporarily in the map. 
      //This should not happen, so throw an error.
#if DEBUG
      std::string msg = std::to_string(map_size) + " contact surfels missing paired surfel:";
      for (auto &it : regular_surfel_desc_parser.m_opposite_parsing_maps[STP_COND_REALM]) {
        SURFEL surfel = it.second;
        msg += " " + std::to_string(surfel->id()) + ",";
      }
      msg.pop_back(); //clean up last comma
      msg_internal_error("%s", msg.c_str());
#else
      std::string msg = std::to_string(map_size) + " contact surfels missing paired surfel:";
      size_t missing_surfel_count = 0;
      int missing_surfel_count_threshold = 10;
      for (auto &it : regular_surfel_desc_parser.m_opposite_parsing_maps[STP_COND_REALM]) {
        if (missing_surfel_count < missing_surfel_count_threshold) {
          SURFEL surfel = it.second;
          msg += " " + std::to_string(surfel->id()) + ",";
        } else {
          msg += " and" + std::to_string(map_size - missing_surfel_count_threshold) + " others.";
          break;
        }
        missing_surfel_count += 1;
      }
      if (missing_surfel_count < missing_surfel_count_threshold) {
        msg.pop_back(); // clean up last commma
      }
      msg_internal_error("%s", msg.c_str());
#endif
    }
  }

  VOID add_dangling_flow_wsurfels_to_groups_internal() {
    //This method should be called after all flow wsurfels are parsed to add the dangling ones to their corresponding
    //groups here.
    for (auto &it : regular_surfel_desc_parser.m_opposite_parsing_maps[STP_FLOW_REALM]) {
      //Flow wsurfels for open shells are loaded initially to the map rather than the group, so the two flow surfels are
      //added consecutively for optimization purposes. However, if only one side is coupled, the other side is not
      //processed as wsurfel and therefore the coupled flow surfel is still in the map.
      SURFEL surfel = it.second;
      surfel->m_group->add_shob_to_group(surfel);
    }
  }

 private:
  //We don't want to instantiate and delete an object every time a new descriptor is passed
  //We can resuse the same type of object allowing the client to reset key properties  
  static cREGULAR_SURFEL_DESC_PARSER regular_surfel_desc_parser;
  static cMIRROR_SURFEL_DESC_PARSER mirror_surfel_desc_parser;
  static cLRF_SURFEL_DESC_PARSER    lrf_surfel_desc_parser;
  static cISURFEL_DESC_PARSER isurfel_desc_parser;
  static cGHOST_SURFEL_DESC_PARSER  ghost_surfel_desc_parser;
  static cSAMPLING_SURFEL_DESC_PARSER sampling_surfel_desc_parser;
  static cGHOST_SAMPLING_SURFEL_DESC_PARSER ghost_sampling_surfel_desc_parser;  
};


/*----------------------------------------------------------------------------
* PARSE UBLK DESCRIPTOR BASE
*
* An abstract class that implements most utility functions that are common
* to all derived ublk descriptor classes.
*
* Derived classes are forced to override the "post_process" method, since
* different ublk types are initialized and added to different groups
*----------------------------------------------------------------------------*/

class cBASE_UBLK_DESCS_PARSER : public cBASE_SHOB_DESCS_PARSER {
  
 public:

  //This method is virtual to allow derived classes to override and also set the
  //derived descriptor pointer when the base descriptor is set
  VOID set_realm(REALM realm) { m_realm = realm; };
  virtual VOID set_ublk_desc(DGF_UBLK_BASE_DESC desc) { m_ublk_base_desc = desc; };
  VOID set_ghost_info(std::vector<cDGF_GHOST_INFO>& ginfo) { m_ghost_info = ginfo; };
  VOID set_home_sp(STP_PROC hsp) { m_home_sp = hsp; };
  VOID set_lgi_stream(LGI_STREAM lgistream) { m_istream = lgistream; };
  VOID set_ublk_decomp_flags (uINT8 flags) { m_ublk_decomp_flags = flags; };
  
  static VOID set_static_info_for_simsizes(const SIM_SIZES::sUBLK_STATIC_INFO& info);
  
  //reset - resets the key properties of the parser object
  virtual VOID reset();

  //Returns the size of the ublk as defined in the descriptor
  //Other properties taken into account while computing size
  //include m_n_phys_types, and attribute data
  virtual size_t SIZE() const override;

  size_t DYN_DATA_SIZE() const;
  
  cBASE_UBLK_DESCS_PARSER():
  m_realm(STP_INVALID_REALM),
  m_ublk_base_desc(nullptr),
  m_ghost_info(cBASE_UBLK_DESCS_PARSER::m_dummy_ghost_info_vec),
  m_home_sp(-1),
  m_istream(nullptr),
  m_ublk_decomp_flags(0),
  m_n_phys_types(0) {

  }
  
  virtual ~cBASE_UBLK_DESCS_PARSER(){};

 protected:
  
  //Overrides the base class read_descriptor method
  //This is a bare bones implementation most likely to
  //be override by derived classes
  virtual VOID read_descriptor() override;
  
  //Allocates memory for the ublk
  virtual sSHOB* allocate() override;

  //This method is pure virtual and is unique to the type
  //of ublk or derive class parsers
  virtual VOID post_process(sSHOB* shob) override = 0;

  //General Helpers
  BOOLEAN is_near_surf() const;
  BOOLEAN is_vr_fine() const;
  BOOLEAN is_vr_coarse() const;
  BOOLEAN is_vr_coarse_with_no_fine() const;
  BOOLEAN is_ghost() const;
  BOOLEAN is_ghost_vr_fine() const;
  BOOLEAN has_ghosts() const;

  // Ghosts of VR fine are a kind of hybrid; they are replicas of VR fine microblocks on another SP, and they are
  // commed for full ckpt resume repeatability. However they also have two sets of states and are populated via 
  // explode from VR coarse ghosts. Thus they are allocated as non-ghost ublks but they are in the recv groups.
  BOOLEAN is_allocated_as_ghost() const;
  BOOLEAN is_mirror() const;
  auINT32 flags() const;
  auINT32 vr_mask() const;
  BOOLEAN is_regular_surfel_interacting() const;
  BOOLEAN is_sampling_surfel_interacting() const;
  BOOLEAN is_pde_advect() const;
  BOOLEAN is_ublk_split() const;
  BOOLEAN should_read_ckpt_info() const;
  sUBLK::sUBLK_ATTRS get_ublk_attrs() const;
  auINT32 ublk_id() const;

  //Helpers for read_descriptor
  VOID  mark_vr_fine_as_near_surf_if_parent_is_near_surf();  
  VOID  set_phys_types_and_attributes_for_ghost_ublk();
  VOID  add_low_mach_info_to_attributes();
  asINT32  get_cdi_phys_type_from_map(asINT32 part_index);
  STP_PHYSTYPE_TYPE  get_voxel_sim_type_from_map(asINT32 part_index);
  
  virtual VOID compute_phys_types_and_attributes() = 0;
  asINT32 find_voxel_dominant_fluid_region(DGF_GENERAL_VOXEL_DESC voxel_desc, 
					   dFLOAT &total_pfluid);  
  
  //Helpers for post_process
  VOID read_ckpt_info(UBLK);
  VOID do_smart_seed_ublk(UBLK ublk);  
  VOID update_total_fluid_voxel_count(UBLK ublk);
  VOID set_vr_flags(UBLK ublk);
  VOID init_basic_ublk_properties(UBLK, BOOLEAN is_mirror_of_solid_ublk = FALSE);
  VOID update_non_ghost_ublk_common_properties(UBLK, BOOLEAN is_mirror_of_solid_ublk = FALSE);
  VOID update_ghost_ublk_common_properties(UBLK);
  VOID update_ghost_vr_fine_ublk_common_properties(UBLK);
  VOID add_vr_data_to_ublk(UBLK,STP_LATVEC_MASK);
  VOID add_vrfine_data_to_last_coarse_ublk(UBLK,VOXEL_MASK_8);
  VOID compute_offset_location( STP_COORD orig_ublk_location[3],
				asINT32 cube_offset, 
				asINT32 ublk_size);

  template<typename DERIVED_UBLK_DESC> VOID add_split_advect_info_to_ublks(UBLK ublk);
  template<typename DERIVED_UBLK_DESC> VOID compute_real_ublk_centroids(UBLK ublk);
  
  //Properties / Data Members
  REALM m_realm;
  DGF_UBLK_BASE_DESC m_ublk_base_desc;
  //Used for initializing reference
  static std::vector<cDGF_GHOST_INFO> m_dummy_ghost_info_vec;
  std::vector<cDGF_GHOST_INFO>& m_ghost_info;
  STP_PROC m_home_sp;
  LGI_STREAM m_istream;
  uINT8 m_ublk_decomp_flags;
  auINT32 m_n_phys_types;
  STP_PHYSTYPE_TYPE m_sim_phys_types[ubFLOAT::N_VOXELS];
  sUBLK_DYNAMICS_ATTR_DATA m_attribute_data[ubFLOAT::N_VOXELS];
  VOXEL_MASK_8 m_voxel_mask;

  //Common
  static UBLK g_last_vr_coarse_ublk;
  static asINT32 g_last_vr_fine_ublk_index;
  static SIM_SIZES::sUBLK_STATIC_INFO g_simsizes_info;
};


/*----------------------------------------------------------------------------
 * MIRROR UBLK DESCRIPTOR PARSER
 *
 * A concrete class that overrides necessary base class methods for parsing 
 * descriptors for mirror ublks
 *----------------------------------------------------------------------------*/
class cMIRROR_UBLK_DESC_PARSER : public cBASE_UBLK_DESCS_PARSER {

 public:
  virtual VOID set_ublk_desc(DGF_UBLK_BASE_DESC) override;

 private:

  virtual VOID read_descriptor() override;
  virtual VOID post_process(sSHOB*) override;
  virtual VOID compute_phys_types_and_attributes() override;
  
  //Helpers
  VOID init_ghost_mirror_of_solid_ublk(UBLK);
  VOID verify_and_update_descriptor();
  VOID add_mirror_comm_quantum(UBLK);
  STP_STATE_INDEX compute_state_index(UBLK);
  VOID fill_mirror_data(UBLK);
  VOID maybe_allow_copy_of_all_voxels(UBLK);
  UBLK m_real_ublk;
  BOOLEAN m_is_mirror_of_solid_ublk;
  DGF_MIRROR_UBLK_DESC m_ublk_desc;
};

/*----------------------------------------------------------------------------
 * SIMPLE UBLK DESCRIPTOR PARSER
 *
 * A concrete class that overrides necessary base class methods for parsing 
 * descriptors for simple ublks
 *----------------------------------------------------------------------------*/
class cSIMPLE_UBLK_DESC_PARSER : public cBASE_UBLK_DESCS_PARSER {

 public:
  virtual VOID set_ublk_desc(DGF_UBLK_BASE_DESC) override;
  VOID set_cube_offset(asINT32 offset) { m_cube_offset = offset;};
  
 private:

  virtual VOID read_descriptor() override;
  virtual VOID post_process(sSHOB*) override;
  virtual VOID compute_phys_types_and_attributes() override;

  //Helpers
  VOID compute_cube_offset();
  VOID debug_check();
  VOID add_vr_data(UBLK);
  VOID add_surf_data_if_vr_fine_and_near_surf(UBLK);
  VOID fill_dynamics_data(UBLK);
  VOID update_meas_info(UBLK);
  VOID fill_uds_data(UBLK);  //LB_UDS
  VOID maybe_store_voxel_face_areas(UBLK);
  VOID maybe_set_implicit_state_index(UBLK);
  
  //Attributes
 private:
  asINT32 m_cube_offset;
  DGF_SIMPLE_UBLK_DESC m_ublk_desc;
};

/*----------------------------------------------------------------------------
 * SIMPLE UBLK DESCRIPTOR PARSER DECORATOR
 *
 * A simple ublk descriptor can potentially allocate multiple simple ublks
 * This decorator, computes cube offset for each simple ublk and calls its
 * parse routine
 *----------------------------------------------------------------------------*/
class cSIMPLE_UBLK_DESC_PARSER_DECORATOR : public cBASE_UBLK_DESCS_PARSER {

 public:
  virtual VOID set_ublk_desc(DGF_UBLK_BASE_DESC) override;
  virtual size_t SIZE() const override { return m_total_simple_ublks_size; };
    
 private:
    //The read descriptor reads the simple ublk descriptor and then
  //calls the parse routine on the simple parser object contained
  //within the decorator as a private member
  virtual VOID read_descriptor() override;
  
  //These functions have no use for in the decorator
  virtual VOID compute_phys_types_and_attributes() override {};
  virtual sSHOB* allocate() override { return nullptr; };
  virtual VOID post_process(sSHOB*) override {};
  
  VOID read_nonzero_cube_factor_single_sp();
  VOID read_nonzero_cube_factor_nsps();
  VOID read_zero_cube_factor();
  VOID reset_simple_parser(REALM realm,
         DGF_SIMPLE_UBLK_DESC ublk_desc, 
			   uINT8 ublk_decomp_flags,
			   asINT32 cube_offset,
			   STP_PROC home_sp,
			   std::vector<cDGF_GHOST_INFO> &ghost_info,
			   LGI_STREAM istream);
  
 private:
  static std::vector<cDGF_GHOST_INFO> m_cube_ghost_info;
  asINT32 m_ublk_size;
  asINT32 m_n_ublks;
  STP_UBLK_ID m_cube_id;
  STP_COORD m_cube_location[3];
  size_t m_total_simple_ublks_size = 0;

  //Composition entails a parser oject
  cSIMPLE_UBLK_DESC_PARSER m_simple_parser;
  DGF_SIMPLE_UBLK_DESC m_ublk_desc;
};

/*----------------------------------------------------------------------------
 * REAL UBLK DESCRIPTOR PARSER
 *
 * A concrete class that overrides necessary base class methods for parsing 
 * descriptors for real ublks
 *----------------------------------------------------------------------------*/
class cREAL_UBLK_DESC_PARSER : public cBASE_UBLK_DESCS_PARSER {

 public:
  virtual VOID set_ublk_desc(DGF_UBLK_BASE_DESC) override;
  virtual size_t SIZE() const override;
  virtual VOID reset() override;

 private:
  virtual VOID read_descriptor() override;
  virtual VOID post_process(sSHOB*) override;
  virtual VOID compute_phys_types_and_attributes() override;  
    
 private:
  BOOLEAN is_solid_ublk() const;
  BOOLEAN is_solid_non_vr_ublk() const;
  VOID add_vr_data(UBLK);
  VOID update_dynamics_data_and_meas_info(UBLK);
  VOID post_process_solid_ublk(UBLK);
  VOID post_process_regular_real_ublk(UBLK);
  VOID fill_surf_geom_data(UBLK);
  VOID update_vr_fine_fluid_voxel_mask(UBLK);
  VOID maybe_store_voxel_face_areas(UBLK);
  VOID add_split_neighbor_info_to_ublks(UBLK);
  VOID maybe_set_implicit_state_index(UBLK);

 private:
  dFLOAT m_voxel_pfluids[ubFLOAT::N_VOXELS];
  VOXEL_MASK_8            m_voxel_masks[ubFLOAT::N_VOXELS];   // One per physics desc
  PHYSICS_DESCRIPTOR m_phys_descs[ubFLOAT::N_VOXELS];
  dFLOAT             m_total_pfluids[ubFLOAT::N_VOXELS];
  asINT32            m_fluid_region_indices[ubFLOAT::N_VOXELS];
  BODY_FORCE_PHYSICS_DESCRIPTOR m_body_force_descs[ubFLOAT::N_VOXELS];
  CONNECT_MASK m_fluid_conn_voxel_masks[ubFLOAT::N_VOXELS];
  CONNECT_MASK m_path_conn_voxel_masks[ubFLOAT::N_VOXELS];
  DGF_REAL_UBLK_DESC m_ublk_desc;
  std::vector<sVOXEL_FACE_POLYGON> m_voxel_face_polygons;
};


//----------------------------------------------------------------------------
// UBLK DESC PARSER FACTORY
// Manufactures all the UBLK descriptor parser objects based on the
// UBLK descriptor and ghosting information
//----------------------------------------------------------------------------

class cUBLK_DESC_PARSER_FACTORY {

 public:
  static  cBASE_UBLK_DESCS_PARSER* create_ublk_desc_parser(STP_REALM realm,
               DGF_UBLK_BASE_DESC descriptor,
							 uINT8 ublk_decomp_flags,
							 STP_PROC home_sp, 
							 std::vector<cDGF_GHOST_INFO> &ghost_info,
							 LGI_STREAM istream);

 private:
  //We don't want to instantiate and delete an object every time a new descriptor is passed
  //We can resuse the same type of object allowing the client to reset key properties  
  static cMIRROR_UBLK_DESC_PARSER mirror_ublk_desc_parser;
  static cSIMPLE_UBLK_DESC_PARSER_DECORATOR simple_ublk_desc_parser_decorator;
  static cSIMPLE_UBLK_DESC_PARSER simple_ublk_desc_parser;
  static cREAL_UBLK_DESC_PARSER real_ublk_desc_parser;
};

} //Namespace SIMULATOR_NAMESPACE
#endif
