/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "ublk_surfel_offset_table.h"
#include "sim.h"

#include SIMSIZES_SHARED_H

DATA_OFFSET_TABLE_OPTS::DATA_OFFSET_TABLE_OPTS(const SIM_SIZES::SIM_OPTIONS& opts){
  is_lb_model = opts.is_lb_model;
  is_turb_model = opts.is_turb_model;
  is_heat_transfer = opts.is_heat_transfer;
  is_conduction_model = opts.is_conduction_model;
  is_radiation_model = opts.is_radiation_model;
  is_scalar_model = opts.is_scalar_model;
  is_multi_component = opts.is_multi_component;
  is_particle_model = opts.is_particle_sim;
  switch_acous_during_simulation = opts.switch_acous_during_simulation;
  n_user_defined_scalars = opts.n_user_defined_scalars;
  has_avg_mme_window = opts.has_avg_mme_window;
  store_frozen_vars  = opts.store_frozen_vars;
  is_large_pore = opts.is_large_pore;
  use_uds_lb_solver = opts.use_uds_lb_solver;
  is_pf_model = opts.is_pf_model;
  meas_seed_or_write_pres_d19 = opts.meas_seed_or_write_pres_d19;
  use_implicit_shell_solver = opts.use_implicit_shell_solver;
  use_implicit_solid_solver = opts.use_implicit_solid_solver;
};

DATA_OFFSET_TABLE_OPTS::~DATA_OFFSET_TABLE_OPTS(){};

DATA_OFFSET_TABLE_OPTS::DATA_OFFSET_TABLE_OPTS(const sSIM_INFO& sim,
					       cBOOLEAN is_5g,
                                               cBOOLEAN write_cbf_pressure){
  is_lb_model = sim.is_lb_model;
  is_turb_model = sim.is_turb_model;
  is_heat_transfer = sim.is_heat_transfer;
  is_conduction_model = sim.is_conduction_model;
  is_radiation_model = sim.is_radiation_model;
  is_scalar_model = sim.is_scalar_model;
  is_multi_component = is_5g;
  is_particle_model = sim.is_particle_model;
  switch_acous_during_simulation = sim.switch_acous_during_simulation;
  n_user_defined_scalars = sim.n_user_defined_scalars;
  has_avg_mme_window = sim.has_avg_mme_window;
  store_frozen_vars  = sim.store_frozen_vars;
  is_large_pore = sim.is_large_pore;
  is_pf_model = sim.is_pf_model;
  use_uds_lb_solver = (sim.uds_solver_type == LB_UDS);
  meas_seed_or_write_pres_d19 = write_cbf_pressure;
  use_implicit_shell_solver = sim.use_implicit_shell_solver;
  use_implicit_solid_solver = sim.use_implicit_solid_solver;
};
