/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** ExaSIM Simulation Process                                             ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * DYN_INTERFACE definitions (functional sets and groups)
 *
 * Created Mon Jan 23, 2012 
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_DYN_INTERFACE_H
#define _SIMENG_DYN_INTERFACE_H

#include "common_sp.h"
#include "quantum.h"
#include "ckpt.h"
#include "group.h"
#include "shob_dyn.h"
#include "surfel_dyn_sp.h"
#include "sp_timers.h"

// For surfel index in an isurfel 
enum ISURFEL_SURFEL_INDEX {
  ISURFEL_EXTERIOR,
  ISURFEL_INTERIOR
};
// For surfel index in an APM isurfel 
// (apm side expected to be interior by apm isurfel dynamics who uses ISURFEL_FLUID and ISURFEL_APM extensively)
enum APM_ISURFEL_SURFEL_INDEX {
  ISURFEL_FLUID,
  ISURFEL_APM
};

enum eISURFEL_DYN_TYPE {
  INVALID_APM_ISURFEL_TYPE = -1,
  APM_SLIP_ISURFEL_TYPE,
  APM_NOSLIP_ISURFEL_TYPE,
  APM_LINEAR_SLIP_ISURFEL_TYPE,
  APM_LINEAR_NOSLIP_ISURFEL_TYPE,
  APM_ANGULAR_SLIP_ISURFEL_TYPE,
  APM_ANGULAR_NOSLIP_ISURFEL_TYPE,
  N_ISURFEL_DYN_TYPES
};

/*--------------------------------------------------------------------------*
 * ISURFEL_DYN_QUANTUM
 *--------------------------------------------------------------------------*/
typedef struct sISURFEL_DYN_QUANTUM {
  SURFEL exterior_surfel;
  SURFEL interior_surfel;
} *ISURFEL_DYN_QUANTUM;

// from surfel_dyn.h
struct sNOSLIP_SURFEL_PARAMETERS;
//
// APM SLIP SURFEL PARAMETERS have been overwritten to contain porosity for two
// sides. This consistency check is not required.
typedef struct sAPM_NOSLIP_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_NOSLIP_SURFEL_PARAMETERS") {
    return sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_NOSLIP_SURFEL_PARAMETERS;

typedef struct sAPM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
    APM_NOSLIP_SURFEL_PARAMETERS parameters()
    { return (APM_NOSLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR;

typedef struct sAPM_LINEAR_NOSLIP_SURFEL_PARAMETERS : public sLINEAR_NOSLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_LINEAR_NOSLIP_SURFEL_PARAMETERS") {
    return sLINEAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_LINEAR_NOSLIP_SURFEL_PARAMETERS;

typedef struct sAPM_LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR : public sAPM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR 
{
    APM_LINEAR_NOSLIP_SURFEL_PARAMETERS parameters()
    { return (APM_LINEAR_NOSLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR;

typedef struct sAPM_ANGULAR_NOSLIP_SURFEL_PARAMETERS : public sANGULAR_NOSLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_ANGULAR_NOSLIP_SURFEL_PARAMETERS") {
    return sANGULAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_ANGULAR_NOSLIP_SURFEL_PARAMETERS;

typedef struct sAPM_ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR : public sAPM_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR 
{
    APM_ANGULAR_NOSLIP_SURFEL_PARAMETERS parameters()
    { return (APM_ANGULAR_NOSLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR;

// from surfel_dyn.h
struct sSLIP_SURFEL_PARAMETERS;

typedef struct sAPM_SLIP_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_SLIP_SURFEL_PARAMETERS") {
    return sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_SLIP_SURFEL_PARAMETERS;

typedef struct sAPM_SLIP_SURFEL_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
    APM_SLIP_SURFEL_PARAMETERS parameters()
    { return (APM_SLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_SLIP_SURFEL_PHYSICS_DESCRIPTOR;

typedef struct sAPM_LINEAR_SLIP_SURFEL_PARAMETERS : public sLINEAR_SLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_LINEAR_SLIP_SURFEL_PARAMETERS") {
    return sLINEAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_LINEAR_SLIP_SURFEL_PARAMETERS;

typedef struct sAPM_LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR : public sAPM_SLIP_SURFEL_PHYSICS_DESCRIPTOR 
{
    APM_LINEAR_SLIP_SURFEL_PARAMETERS parameters()
    { return (APM_LINEAR_SLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR;

typedef struct sAPM_ANGULAR_SLIP_SURFEL_PARAMETERS : public sANGULAR_SLIP_SURFEL_PARAMETERS {
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "APM_ANGULAR_SLIP_SURFEL_PARAMETERS") {
    return sANGULAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
  }
} *APM_ANGULAR_SLIP_SURFEL_PARAMETERS;

typedef struct sAPM_ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR : public sAPM_SLIP_SURFEL_PHYSICS_DESCRIPTOR 
{
    APM_ANGULAR_SLIP_SURFEL_PARAMETERS parameters()
    { return (APM_ANGULAR_SLIP_SURFEL_PARAMETERS)_parameters; }

    VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }
} *APM_ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR;


VOID isurfel_dynamics(sSURFEL_PAIR *surfel_pair,
                      ACTIVE_SOLVER_MASK active_solver_mask,
                      ACTIVE_SOLVER_MASK full_active_solver_mask,
                      SFL_V2S_DATA_PAIR_PTR surfel_temp_data,
                      const BOOLEAN is_seeding_call = FALSE);
#endif // _SIMENG_DYN_INTERFACE_H
