/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#ifndef _SIMENG_LOGGING_H
#define _SIMENG_LOGGING_H

// If this is true, then logging is turned on at compile time.
#define ENABLE_LOGGING 0
// turn on specific groups at compile time
#define ENABLE_PRINT_STRANDS 0
#define ENABLE_PRINT_SENDQ 0
#define DEBUG_VARIABLE_POWERTHERM_COUPLING 0
#define DEBUG_T_PTHERM_EQNS 0
#define DEBUG_STRANDMASTER 0
#define DEBUG_STRAND_SWITCHING 0
#define DEBUG_RECV_DEPENDENCIES 0
#define DEBUG_BSURFEL_COMM 0
#define DEBUG_BSURFEL_MEAS 0
#define DEBUG_VTABLE 0
#define DEBUG_BSURFEL_INIT 0
#define DEBUG_MLRF_PARCEL_COMM 0
//#if DEBUG
#define DEBUG_CONDUCTION 0
//#else
//#define DEBUG_CONDUCTION 0
//#endif
#define DEBUG_SHELL_SURFEL_VERIFICATION 0
#define DEBUG_PARTICLE_SUMMARY 0
#define DEBUG_MPI_MESSAGES 0
#define DEBUG_ASYNC_EVENTS 0
#define DEBUG_GPU_INIT 0
#define DEBUG_GPU_GENERIC 0
#define DEBUG_GPU_MEM 0
#define DEBUG_UDS 0
#define DEBUG_PARALLEL_CKPT 0
#define DEBUG_RADIATION 0
#include PF_LOG_H

#define LOG_ON(group, ...) PF_LOG_ON(get_log(), group, ##__VA_ARGS__)
#define LOG_ON_IF(cond, group, ...) PF_LOG_ON_IF((cond), get_log(), group, ##__VA_ARGS__)
#define LOG_MSG(group, ...) PF_LOG_MSG(get_log(), group, ##__VA_ARGS__)
#define LOG_MSG_IF(cond, group, ...) PF_LOG_MSG_IF((cond), get_log(), group, ##__VA_ARGS__)

#define LOG_TS "TS", g_timescale.m_time
#define LOG_BASE_TS "TS", g_timescale.m_base_time
#define LOG_COND_TS "TS", g_timescale.m_conduction_pde_tm.m_time

cLOG& get_log();
cLOG& main_log();
cLOG& comm_log();
void initialize_loggers(int my_mpi_rank);

#endif
