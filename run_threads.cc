/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Main simulation loop
 *
 *--------------------------------------------------------------------------*/


#include <poll.h>
#include <time.h>
#include <sys/time.h>

#if !defined(_AIX)
#include <alloca.h>
#endif

#include "common_sp.h"
#include PHYSICS_H
#include "thread_run.h"
#include "advect.h"
#include "sp_timers.h"
#include "surfel_dyn_sp.h"
#include "ckpt.h"
#include "boltz_diags.h"
#include "event_queue.h"
#include "mlrf.h"
#include "ublk.h"
#include "mirror.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "comm_groups.h"
#include "status.h"
#include "particle_sim_info.h"
#include "trajectory_window.h"
#include "implicit_shell_solver.h"
#include "implicit_solid_solver.h"

#define ENABLE_TIMESTEP_SYNC    0        /* Hard sync between timesteps */

sEVENT_QUEUE g_async_event_queue;

/*--------------------------------------------------------------------------*
 * Main Loop
 *--------------------------------------------------------------------------*/

#define SIM_REPORT_OPTIONS \
  (SIM_REPORT_MME | SIM_REPORT_MME_VERBOSE | SIM_REPORT_OCCUPIED | \
   SIM_REPORT_MEMORY | SIM_REPORT_SIM | SIM_REPORT_DIAGNOSTICS)

static VOID handle_reports_process (SIM_OPTIONS options)
{
#if 0        /* No longer supported, but we may wish to reincarnate */
  /* Occupied */
  if (options & SIM_REPORT_OCCUPIED)
    sim_report_occupied();
#endif

  /* Memory */
  if (options & SIM_REPORT_MEMORY ) {
    if (!sim.debug_malloc_p) {
      msg_error("When using the -report_memory option the environment variable "
                "EXA_DEBUG_MALLOC_MODE must be set.");
    }
    else {
      msg_print("Timestep %5ld: Memory Usage ->", (long) g_timescale.m_time);
      exa_malloc_debug_report(FALSE);
    }
  }

#if SIM_ENABLE_BOLTZ_DIAGS
  if ((g_timescale.m_time % sim.async_event_flags.diag_avg) == 0) {
    sim_report_fluid_dyn_diagnostics(sim.async_event_flags.diag_avg);
    sim_reset_fluid_dyn_diagnostics();
  }
#endif

}
static VOID handle_reports(SIM_OPTIONS options)
{
  /* Return if nothing to report */
  if (! sim.async_event_flags.diags_on_p) return;

  ccDOTIMES(p, total_sps) {
    sp_synchronize();
    if (p == my_proc_id) {
      handle_reports_process(options);
    }
  }
  sp_synchronize();
}

static VOID determine_turb_synth_checkpointer() {

  if (total_sps > 1) {
    uINT32 checkpointer = g_turb_info.m_checkpointer;
    uINT32 new_checkpointer;
    MPI_Allreduce(&checkpointer, &new_checkpointer, 1, MPI_INT, MPI_MIN, eMPI_sp_cp_comm);
    if (g_turb_info.m_checkpointer != new_checkpointer)
      g_turb_info.m_checkpointer = new_checkpointer;
  }
}


const BOOLEAN READY = TRUE;
const BOOLEAN DONE = FALSE;

int full_ckpt_done = 0;
int full_ckpt_pending = 0;
int mme_ckpt_done = 0;
int mme_ckpt_pending = 0;
int avg_mme_ckpt_done = 0;
int avg_mme_ckpt_pending = 0;

static VOID wait_for_ckpt_comm_done(int *ckpt_pending, int *ckpt_done) {
  *ckpt_pending = 1;
  while(!(*ckpt_done)) {
    sp_thread_sleep(THREAD_SLEEP_LONG);
  }
  *ckpt_done = 0;
}

static BOOLEAN is_request_complete(MPI_Request *request) {
  int flag = 0;
  MPI_Status status;
  MPI_Test(request, &flag, &status);
  return (flag != 0);
}

static VOID ckpt_comm(int *ckpt_pending, int *ckpt_done, BOOLEAN is_full_ckpt, BOOLEAN is_avg_mme = FALSE)
{
  static MPI_Request full_ckpt_request = MPI_REQUEST_NULL;
  static MPI_Request mme_ckpt_request = MPI_REQUEST_NULL;
  static MPI_Request avg_mme_ckpt_request = MPI_REQUEST_NULL;

  MPI_Request *ckpt_request = is_full_ckpt ? &full_ckpt_request : (is_avg_mme? &avg_mme_ckpt_request : &mme_ckpt_request);

  // signal CP that checkpoint data is on its way
  if (*ckpt_request == MPI_REQUEST_NULL) {
    MPI_Issend(nullptr, 0, eMPI_BOOLEAN, eMPI_sp_cp_rank(), eMPI_ASYNC_CKPT_READY_TAG, eMPI_sp_cp_comm, ckpt_request);
  }
  if (!is_request_complete(ckpt_request))
    return;
  else {
    sim_checkpoint(is_full_ckpt, is_avg_mme);
    *ckpt_pending = 0;
    *ckpt_done = 1;
    *ckpt_request = MPI_REQUEST_NULL;
    return;
  }
}


VOID process_mme_ckpt_sim_thread() {
  wait_for_ckpt_comm_done(&mme_ckpt_pending, &mme_ckpt_done);
}

VOID process_avg_mme_ckpt_sim_thread() {
  wait_for_ckpt_comm_done(&avg_mme_ckpt_pending, &avg_mme_ckpt_done);
}

VOID process_full_ckpt_sim_thread() {
  g_strand_mgr.m_full_ckpt_done = true;
  wait_for_ckpt_comm_done(&full_ckpt_pending, &full_ckpt_done);
}

// All done by comm thread while sim threads wait.
VOID process_checkpoints_comm_thread() {
  if (mme_ckpt_pending || avg_mme_ckpt_pending || full_ckpt_pending) {
    // Even if the send queue is drained previously in the comm thread main loop, new entries could have 
    // been added between that call to this point as some strands may be called by the sim thread and new 
    // send entries are inserted in the queue. Need to drain the send queue again since the data 
    // (e.g. surfel data) may be needed by other SPs to reach the time update strand. Otherwise when the 
    // ckpt send to CP is stucked, those data will never reach other SPs leading to a hang. 
    // It is guaranteed that all the sends are drained here since full_ckpt_pending or mme_ckpt_pending 
    // flag is set by the time update strand while all the other strands are finished at this point.
    // Same logic applies to the meas send queue.
    g_strand_mgr.m_send_queue->drain(); 
    g_strand_mgr.m_meas_send_queue->drain(); 
  }

  if (mme_ckpt_pending) {
    ckpt_comm(&mme_ckpt_pending, &mme_ckpt_done, FALSE);
  }
  if (avg_mme_ckpt_pending) {
    ckpt_comm(&avg_mme_ckpt_pending, &avg_mme_ckpt_done, FALSE, TRUE);
  }
  if (full_ckpt_pending) {
    ckpt_comm(&full_ckpt_pending, &full_ckpt_done, TRUE);
  }
}

static void enable_run_diagnostics()
{
  if (getenv("EXA_DIAG_AVG")) {
    sim_run_info.run_options |= SIM_REPORT_DIAGNOSTICS;
    sim.async_event_flags.diags_on_p = TRUE;
    sim.async_event_flags.diag_avg = atoi(getenv("EXA_DIAG_AVG"));
    if (sim.async_event_flags.diag_avg < 1)
      sim.async_event_flags.diag_avg = 1;
  }

  if (sim_run_info.run_options & SIM_REPORT_OPTIONS) {
    sim.async_event_flags.diags_on_p = TRUE;
    msg_warn("Diagnostic reports enabled: this will cause hard synchronizations that invalidate timers.");
  }
}

static void reduce_fluid_voxels_count() {
  int voxel_count = sim.total_fluid_like_voxels;
  MPI_Reduce(&voxel_count, NULL, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
}

static void count_n_boundary_seeded_surfels()
{
  int bs[3];
  bs[0] = sim.n_boundary_surfels;
  bs[1] = sim.n_seeded_boundary_surfels;
  bs[2] = sim.n_transient_seeded_boundary_surfels;
  MPI_Reduce(&bs[0], NULL, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  MPI_Reduce(&bs[1], NULL, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  MPI_Reduce(&bs[2], NULL, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
}

//Definition in strang_mgr.cc
VOID set_running_strand(STRAND new_strand, BOOLEAN new_timestep);

sFLUID_DYN_DCACHE_PER_SCALE_DATA g_dcache_per_scale{};//zero-initialize

sFLUID_DYN_DCACHE* allocate_dcache() {
  void* dcache;
#if BUILD_GPU
  BOOLEAN is_entropy_on = (sim.T_solver_type == PDE_ENTROPY) ||
                          (sim.T_solver_type == LB_ENTROPY);

  BOOLEAN is_energy_on = (sim.T_solver_type == LB_ENERGY);;

  BOOLEAN is_uds_lb_on = (sim.uds_solver_type == LB_UDS);
  BOOLEAN is_pf_on = sim.is_pf_model;
  
  if (sim.enable_dcache_pm_storage) {
    if (!is_uds_lb_on) {
      if (is_entropy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENTROPY_PM>(dcache);
      } else if (is_energy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENERGY_PM>(dcache);
      } else if (sim.is_turb_model) {
	allocate_dcache<sFLUID_DYN_DCACHE_HT_TURB_PM>(dcache);
      } else {
	allocate_dcache<sFLUID_DYN_DCACHE_HT_DNS_PM>(dcache);
      }
    } else { //if (is_uds_lb_on)
      if (is_entropy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENTROPY_UDS_PM>(dcache);
      } else if (is_energy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENERGY_UDS_PM>(dcache);
      } else if (sim.is_turb_model) {
#if BUILD_D19_LATTICE
	if (is_pf_on)
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_TURB_PM_PF>(dcache);
	else
#endif
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_TURB_PM>(dcache);
      } else {
#if BUILD_D19_LATTICE
	if (is_pf_on)
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_DNS_PM_PF>(dcache);
	else
#endif
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_DNS_PM>(dcache);
      }
    }
  } else {
    if (!is_uds_lb_on) {
      if (is_entropy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENTROPY_NO_PM>(dcache);
      } else if (is_energy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENERGY_NO_PM>(dcache);      
      } else if (sim.is_turb_model) {           	          
	if (sim.is_heat_transfer) {
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_TURB>(dcache);
	} else {
	  allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_TURB>(dcache);
	}
      } else {
	if (sim.is_heat_transfer) {
	  allocate_dcache<sFLUID_DYN_DCACHE_HT_DNS>(dcache);
	} else {
	  allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_DNS>(dcache);
	}
      }
    } else { //if (is_uds_lb_on)
      if (is_entropy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENTROPY_UDS_NO_PM>(dcache);
      } else if (is_energy_on) {
	allocate_dcache<sFLUID_DYN_DCACHE_ENERGY_UDS_NO_PM>(dcache);      
      } else if (sim.is_turb_model) {           	          
	if (sim.is_heat_transfer) {
#if BUILD_D19_LATTICE
	  if (is_pf_on)
	    allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_TURB_PF>(dcache);
	  else
#endif
	    allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_TURB>(dcache);
	} else {
#if BUILD_D19_LATTICE
	  if (is_pf_on)
	    allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_UDS_TURB_PF>(dcache);
	  else
#endif
	    allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_UDS_TURB>(dcache);
	}
      } else {
	if (sim.is_heat_transfer) {
#if BUILD_D19_LATTICE
	  if (is_pf_on)
	    allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_DNS_PF>(dcache);
	  else
#endif
	    allocate_dcache<sFLUID_DYN_DCACHE_HT_UDS_DNS>(dcache);
	} else {
#if BUILD_D19_LATTICE
	  if (is_pf_on)
	    allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_UDS_DNS_PF>(dcache);
	  else
#endif
	    allocate_dcache<sFLUID_DYN_DCACHE_NO_HT_UDS_DNS>(dcache);
	}
      }
    }
  }
#else
  allocate_dcache(dcache);
#endif  
  return (FLUID_DYN_DCACHE) dcache;  
}

/*We need dcache initialized before data is sent to the GPU
 * This involves two pieces,
 * a) Initializing the global per scale dcache data, which is
 *    done in a CUDA file for a GPU build FLUID_DYN_DCACHE
 * b) The remaining UBLK specific data is stored in 
 */

VOID init_dcache() {
  sFLUID_DYN_DCACHE* dcache = allocate_dcache();
  memset(&g_dcache_per_scale, 0, sizeof(sFLUID_DYN_DCACHE_PER_SCALE_DATA));
  initialize_fluid_dyn_dcache(dcache);
}


VOID sim_run()
{
  enable_run_diagnostics();

  WITH_DBG_MODULE("sim_run") {
    SCALE finest_scale = FINEST_SCALE;
    SCALE scale;

#if 0
    MPI_Status debug_mpi_status;
    int debug_flag = 1;
    MPI_Iprobe(MPI_ANY_SOURCE, MPI_ANY_TAG, eMPI_sp_cp_comm, &debug_flag, &debug_mpi_status);
    msg_print("Debug Probe returned %d, %d, %d", debug_flag, debug_mpi_status.MPI_SOURCE, debug_mpi_status.MPI_TAG);
#endif

    // Send a special simerr to the CP which indicates to the CP that it has received all
    // init time simerrs, allowing it to safely enter the MPI_Reduce without risk of deadlock.
    STP_GEOM_VARIABLE dummy_point[3] = { 0 };
    simerr_report_error_code(SP_EER_SYNC, 0, dummy_point);
    // Flush the special simerr to the CP. This call is only legal here in the main thread
    // before lazy thread is launched.
    if (g_strand_mgr.m_comm_thread_started)
      msg_internal_error("Comm thread launched before flushing SYNC simerr");
    //simerr_mpi_process(); 
    while (simerr_mpi_process()) { } // repeat until no more errors left
    
    determine_turb_synth_checkpointer();

    count_n_boundary_seeded_surfels();
    // count number of fluid voxels needed for mme checkpoint
    reduce_fluid_voxels_count();

    handle_reports(sim_run_info.run_options);

    sim.initialization_complete_p = TRUE;

  // We used to think that the call to MPI_Reduce in count_n_boundary_seeded_surfels
  // was synchronizing, but this is not the case. The barrier below is necessary for precise 
  // benchmarking.
    MPI_Barrier(eMPI_sp_cp_comm);
    initialize_sleep_times();
    
    clock_gettime(CLOCK_PROCESS_CPUTIME_ID, &g_strand_mgr.sp_start_timespec);   // Start the all-threads timer

    /* Reset the timers */
    sp_timer_clear_all();

    void * temp = aligned_alloc(alignof(cSEND_QUEUE), sizeof(cSEND_QUEUE));
    g_strand_mgr.m_send_queue = new (temp) cSEND_QUEUE(g_strand_mgr.m_n_send_groups);

    void * temp_meas = aligned_alloc(alignof(cMEAS_SEND_QUEUE), sizeof(cMEAS_SEND_QUEUE));
    g_strand_mgr.m_meas_send_queue = new (temp_meas) cMEAS_SEND_QUEUE(g_meas_windows.n_meas_windows());

    if (sim.is_pf_model) {
      void * temp_coll = aligned_alloc(alignof(cCOLLECTIVE_QUEUE), sizeof(cCOLLECTIVE_QUEUE));
      g_strand_mgr.m_collective_queue = new (temp_coll) cCOLLECTIVE_QUEUE(N_COLL_TYPES);
    }
    if (g_timescale.m_all_solvers_have_same_timestep)
      g_timescale.init_common_coarsest_active_scales();
    if ( !(sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) || g_timescale.time_parity() == TIMESTEP_PARITY_ODD)
      g_timescale.init_coarsest_active_scales(finest_scale, TIMESTEP_PARITY_ODD, (sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) );
    if ( !(sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) || g_timescale.time_parity() == TIMESTEP_PARITY_EVEN)
      g_timescale.init_coarsest_active_scales(finest_scale, TIMESTEP_PARITY_EVEN, (sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) );
    if ( !(sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) )
      g_timescale.init_last_active_solver_masks();
    post_initial_receives();

    do_fan_remote_comm_post_initial_recv();
    do_fan_remote_comm_post_initial_send();
    wait_for_fan_remote_comm_initial_recvs_complete();

    // Post the first recv for the first timestep where we should exchange fan data (will be completed before posting the next recv)
    do_fan_remote_comm_post_initial_recv();

    // Reset the counters for averaged contact, now that the coarsest active scale is known
    if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
      g_thermal_averaged_contacts.reset_count();
    }

    meas_do_clear( TRUE, g_timescale.solver_time(sim.is_conduction_sp));   // Surface windows
    meas_do_clear(FALSE, g_timescale.solver_time(sim.is_conduction_sp));   // Fluid windows

    if (sim.is_particle_model) {
      g_particle_subcycling_info.set_particle_solver_mask(g_timescale.time_flow(), -1);
      reset_parcel_index_in_timestep();
      WITH_TIMER(SP_PARTICLE_EMISSION_TIMER) {
        particle_sim.release_parcels();
        particle_sim.update_all_virtual_wiper_positions();
      }
    }

    if(sim_run_info.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {
      clock_gettime(CLOCK_PROCESS_CPUTIME_ID, &g_strand_mgr.sp_start_timespec);   // Start the both-threads timer
    }

    pthread_t comm_thread;
    if(!(sim_run_info.run_options & SIM_NO_RUN)) {

      // Initialize strands

      // These are started in enum order, which is priority order, which is a nice sanity check,
      // but not necessary. If redundant conditionals, like the three is_conduction_model
      // conditionals, become sufficiently intrusive that the sanity-check benefit is lost,
      // combining them is allowable.

      g_strand_mgr.start_runstate(SLIDING_SURF_A_STRAND);
      g_strand_mgr.start_runstate(SLIDING_SURF_B_STRAND);
      if (sim.is_particle_model) {
        g_strand_mgr.start_runstate(INTERIOR_FILM_ACCUMULATION_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_FILM_KINEMATICS_STRAND);
      }
      if (sim.is_conduction_model) {
        g_strand_mgr.start_runstate(FRINGE_WSURF_SAMPLING_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_WSURF_SAMPLING_STRAND);
      }
      g_strand_mgr.start_runstate(FRINGE2_SURF_STRAND);
      if (sim.is_conduction_model) {
       g_strand_mgr.start_runstate(FRINGE2_WSURF_HFC_STRAND);
      }
      g_strand_mgr.start_runstate(FRINGE_SURF_STRAND);
      g_strand_mgr.start_runstate(SLIDING_SURF_C_STRAND);
      g_strand_mgr.start_runstate(FRINGE_NEARBLK_A_STRAND);
      g_strand_mgr.start_runstate(FRINGE_FARBLK_A_STRAND);
      g_strand_mgr.start_runstate(SLIDING_NEARBLK_A_STRAND);
      g_strand_mgr.start_runstate(FRINGE2_NEARBLK_A_STRAND);
      g_strand_mgr.start_runstate(INTERIOR_NS_A_STRAND);
      g_strand_mgr.start_runstate(INTERIOR_FARBLK2_A_STRAND);
      g_strand_mgr.start_runstate(INTERIOR_FARBLK1_A_STRAND);
      if (sim.is_movb_sim()) {
        g_strand_mgr.start_runstate(FRINGE_BSURFELS_STRAND);
        g_strand_mgr.start_runstate(FRINGE2_BSURFELS_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_BSURFELS_STRAND);
      }
      if (sim.is_mme_comm_needed()) {
        g_strand_mgr.start_runstate(FRINGE_NEARBLK_B_STRAND);
        g_strand_mgr.start_runstate(FRINGE_FARBLK_B_STRAND);
        g_strand_mgr.start_runstate(SLIDING_NEARBLK_B_STRAND);
        g_strand_mgr.start_runstate(FRINGE2_NEARBLK_B_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_NS_B_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_FARBLK2_B_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_FARBLK1_B_STRAND);
      }
#if BUILD_5G_LATTICE
      if (sim.is_large_pore) {
        g_strand_mgr.start_runstate(FRINGE_NEARBLK_C_STRAND);
        g_strand_mgr.start_runstate(FRINGE_FARBLK_C_STRAND);
        g_strand_mgr.start_runstate(SLIDING_NEARBLK_C_STRAND);
        g_strand_mgr.start_runstate(FRINGE2_NEARBLK_C_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_NS_C_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_FARBLK2_C_STRAND);
        g_strand_mgr.start_runstate(INTERIOR_FARBLK1_C_STRAND);
      }
#endif

      if (sim.is_radiation_model) {
        g_strand_mgr.start_runstate(RADIATION_STRAND);
      }

      g_strand_mgr.start_runstate(TIMESTEP_UPDATE_STRAND);

      asINT32 n_strands = N_STRANDS;

      // msg_print("ublks %d surfels %d", sim.n_total_ublks,sim.n_total_surfels);

      // Create the comm thread.

      if (sim_run_info.run_options & SIM_REPORT_CPU_AFFINITY) {
        std::string my_cpus = print_thread_affinity();
        msg_print("CPU Affinity: %s", my_cpus.c_str());
      }

      pthread_attr_t  attr;
      pthread_attr_init(&attr);
      pthread_attr_setscope(&attr, PTHREAD_SCOPE_PROCESS);
      int thread_create_status = pthread_create(&comm_thread, &attr, comm_thread_fcn, NULL);
      if(thread_create_status != 0)
        msg_error("Could not create comm thread; error was %s", strerror(thread_create_status));
      g_strand_mgr.m_comm_thread_started = TRUE;
      g_strand_mgr.m_timestep_last_events_processed = FALSE;

#if !GPU_COMPILER && !BUILD_GPU &&!BUILD_5G_LATTICE
      if (sim.is_conduction_sp && sim.is_shell_conduction_model && sim.use_implicit_shell_solver) {
        start_and_wait_for_implicit_shell_solver_setup();
#if ENABLE_CONSISTENCY_CHECKS
        while (g_implicit_shell_solver_simerrs.size()) {
          // Drain the simerrs on the compute thread to avoid hangs that were occurring when trying to drain from the
          // comm thread
          drain_implicit_solver_simerrs();
          usleep(1000); // wait 1ms so the simerr generation doesn't cause hangs when there are thousands of simerrs.
        }
#endif
      }
#endif

#if !GPU_COMPILER && !BUILD_GPU &&!BUILD_5G_LATTICE
      if (sim.is_conduction_sp && sim.is_conduction_model && sim.use_implicit_solid_solver) {
        start_and_wait_for_implicit_solid_solver_setup();
      }
#endif

      /* Initial checkpointing (timestep 0 operations) */
      g_async_event_queue.process_queue(g_timescale.m_time);

      /* Main loop */
      dbg_msg(2, "Starting main loop");

      // strand master loop
      LOG_MSG("STRANDMASTER").printf("Beginning main loop");
      // In each iteration of this loop we either execute the highest-priority ready (non-finished)
      // strand, or, if none are ready, wait for a wakeup to be signaled by the comm thread
      if(sim_run_info.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {
        struct timespec main_thread_begin_timespec;
        clock_gettime(CLOCK_THREAD_CPUTIME_ID, &main_thread_begin_timespec);
        g_strand_mgr.last_timestep_main_thread_stop_ns = main_thread_begin_timespec.tv_nsec + main_thread_begin_timespec.tv_sec * NSEC_PER_SEC;
        clock_gettime(CLOCK_THREAD_CPUTIME_ID, &g_strand_mgr.main_thread_start_timespec); // Start the main-thread timer
      }

      g_timescale.calculate_T_lb_pde_solver_switch_interval();

      while(!g_strand_mgr.m_exit) {
        // We want the compute thread to pick up new comm thread changes to m_ready_mask,
        // so use acquire to sync up with the comm thread
        if (( g_strand_mgr.m_ready_mask.load(std::memory_order_acquire) & 
              g_strand_mgr.m_runstate_mask.load(std::memory_order_relaxed) ).any()) {
          ccDOTIMES(strand_index, n_strands) {
            if (( g_strand_mgr.m_ready_mask.load(std::memory_order_acquire) &
                  g_strand_mgr.m_runstate_mask.load(std::memory_order_relaxed) ).test(strand_index)) {
              // This is the highest priority ready strand.
              // pthread_mutex_lock(&g_strand_mgr.m_dcntr_mutex);
              set_running_strand((STRAND)strand_index);
              // pthread_mutex_unlock(&g_strand_mgr.m_dcntr_mutex);
              // using relaxed here is fine, because we are only communicating 
              // the state of the compute thread to the comm thread
              g_strand_mgr.m_ready_mask.reset(strand_index, std::memory_order_relaxed);

	      //do_debug
	      //printf("Strandmaster: time=%d, calling %d: %s\n",g_running_strand_time, strand_index, print_strand_name(strand_index));

              LOG_MSG("STRANDMASTER").printf( "calling %d: %s",strand_index, print_strand_name(strand_index));
              LOG_MSG("RECV_DEPEND").printf("Starting Strand %s for T %d", print_strand_name(strand_index), g_timescale.m_time);
              g_strand_mgr.m_strands[strand_index]->run();
              LOG_MSG("STRANDMASTER").printf( "returned from %d: %s",strand_index,print_strand_name(strand_index));

              set_running_strand(NO_STRAND);
            }
          }
        } else {
          g_sync_threads.wake_up_comm_thread();
          g_sync_threads.wait_for_comm_thread();
        }
      }

      // Timestep loop finished, start closing up shop
      if (!g_strand_mgr.m_halt) {
        LOG_MSG("ASYNC") << "Holding Loop";
        while (!g_strand_mgr.m_received_sim_finished_msg) {
          g_async_event_queue.process_queue(BASETIME_LAST);
          sp_thread_sleep(THREAD_SLEEP_LONG);
        }
      }
      g_strand_mgr.m_timestep_last_events_processed = TRUE; // well, not actually processed if g_strand_mgr.m_halt
    }

    asINT64 main_thread_ns;
    asINT64 comm_thread_ns;
    if(sim_run_info.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {
      struct timespec main_thread_stop_timespec;
      clock_gettime(CLOCK_THREAD_CPUTIME_ID, &main_thread_stop_timespec); // Stop the main-thread timer
      asINT64 main_thread_start_ns = g_strand_mgr.main_thread_start_timespec.tv_nsec + g_strand_mgr.main_thread_start_timespec.tv_sec * 1000000000;
      asINT64 main_thread_stop_ns = main_thread_stop_timespec.tv_nsec + main_thread_stop_timespec.tv_sec * 1000000000;
      main_thread_ns =  main_thread_stop_ns -  main_thread_start_ns;
      asINT64 comm_thread_start_ns = g_strand_mgr.comm_thread_start_timespec.tv_nsec + g_strand_mgr.comm_thread_start_timespec.tv_sec * 1000000000;
      asINT64 comm_thread_stop_ns = g_strand_mgr.comm_thread_stop_timespec.tv_nsec + g_strand_mgr.comm_thread_stop_timespec.tv_sec * 1000000000;
      comm_thread_ns =  comm_thread_stop_ns -  comm_thread_start_ns;
     
    }
#if 1
    if(!(sim_run_info.run_options & SIM_NO_RUN)) {
      pthread_join(comm_thread, NULL);
    }
#endif

    if(sim_run_info.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {

      struct timespec sp_stop_timespec;
      clock_gettime(CLOCK_PROCESS_CPUTIME_ID, &sp_stop_timespec); // Stop the both-threads timer
      asINT64 sp_start_ns = g_strand_mgr.sp_start_timespec.tv_nsec + g_strand_mgr.sp_start_timespec.tv_sec * 1000000000;
      asINT64 sp_stop_ns = sp_stop_timespec.tv_nsec + sp_stop_timespec.tv_sec * 1000000000;
      asINT64 sp_ns = sp_stop_ns - sp_start_ns;
      dFLOAT sp_cputime = (dFLOAT)sp_ns / ((dFLOAT)1000000000);
      dFLOAT main_thread_cputime = (dFLOAT)main_thread_ns / ((dFLOAT)1000000000);
      dFLOAT comm_thread_cputime = (dFLOAT)comm_thread_ns / ((dFLOAT)1000000000);
      if(sim_run_info.run_options & (SIM_REPORT_THREADTIME | SIM_REPORT_THREADTIME_VERBOSE)) {
        msg_print("Comm thread simulation time:  %g seconds",  comm_thread_cputime);
        msg_print("Main thread simulation time:  %g seconds",  main_thread_cputime);
        msg_print("Both threads simulation time:  %g seconds",  sp_cputime);
        msg_print("");
      }

      typedef struct sVAL_RANK {
        dFLOAT m_val;
        int m_rank;
      } *VAL_RANK;

      sVAL_RANK comm_thread_ns_rank;
      comm_thread_ns_rank.m_val = comm_thread_ns;
      comm_thread_ns_rank.m_rank = my_proc_id;
      MPI_Reduce(&comm_thread_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&comm_thread_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      MPI_Reduce(&comm_thread_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 

      sVAL_RANK main_thread_ns_rank;
      main_thread_ns_rank.m_val = main_thread_ns;
      main_thread_ns_rank.m_rank = my_proc_id;
      MPI_Reduce(&main_thread_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&main_thread_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      MPI_Reduce(&main_thread_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 

      sVAL_RANK sp_ns_rank;
      sp_ns_rank.m_val = sp_ns;
      sp_ns_rank.m_rank = my_proc_id;
      MPI_Reduce(&sp_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&sp_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MAXLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 
      MPI_Reduce(&sp_ns_rank,NULL, 1, MPI_DOUBLE_INT, MPI_MINLOC, eMPI_sp_cp_rank(), eMPI_sp_cp_comm); 

#if 0
      MPI_Reduce(&comm_thread_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&comm_thread_ns, NULL, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&comm_thread_ns, NULL, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&main_thread_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&main_thread_ns, NULL, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&main_thread_ns, NULL, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&sp_ns, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&sp_ns, NULL, 1, MPI_LONG, MPI_MIN, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
      MPI_Reduce(&sp_ns, NULL, 1, MPI_LONG, MPI_MAX, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
#endif

    }

    // if( sim_run_info.run_options & SIM_REPORT_RP_TIME) {
    //   auto [time, count] = g_strand_mgr.m_rp_recv_channel.report_wait_time_in_seconds();
    //   msg_print("Waited %.3e seconds for %lu radiation results (%.3e sec/wait)", time, count, time/count);
    // }

  //Report the lost/emitted/evaporated particle mass/counts to the CP.
    if(sim.is_particle_model) {
      if(!g_track_lost_particle_mass) { //Send negative values to supress CP message output.
        g_sp_emitted_particle_mass = -1;
        g_sp_lost_particle_mass = -1;
      }

      MPI_Reduce(&g_sp_emitted_particle_mass, NULL, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(),  eMPI_sp_cp_comm);
      MPI_Reduce(&g_sp_lost_particle_mass, NULL, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(),  eMPI_sp_cp_comm);

      if(sim.is_thermal_particle_solver) {
        sPARTICLE_VAR evaporated_particle_mass_mks = particle_sim.convert_dimensional_parameter(
                                                                                                g_sp_evaporated_particle_mass,
                                                                                                "LatticeMass",
                                                                                                "kg" );
        MPI_Reduce(&evaporated_particle_mass_mks, NULL, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_cp_rank(),  eMPI_sp_cp_comm);
        MPI_Reduce(&g_sp_evaporated_particles, NULL, 1, MPI_INT, MPI_SUM, eMPI_sp_cp_rank(),  eMPI_sp_cp_comm);
      }

      long int stencil_bytes = -1;
      if (g_report_stencil_memory) {
        stencil_bytes = measure_film_solver_stencil_memory_usage();
      }
      MPI_Reduce(&stencil_bytes, NULL, 1, MPI_LONG, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);

    }

    sp_synchronize();
  }
  dbg_msg (1, "Total dynamically allocated bytes: %ld", total_malloced_bytes);

}

