/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "shob.h"
#include "ublk.h"
#include "surfel.h"
#include "bsurfel.h"
#include "sampling_surfel.h"

#include "advect.h"
#include "mirror.h"
#include "parse_shob_descs.h"
#include "dgf_reader_sp.h"
#include "shob_groups.h"
#include "fset.h"
#include "group.h"
#include "phys_type_map.h"
#include "sim.h"
#include "ublk_table.h"
#include "surfel_table.h"
#include "bsurfel_table.h"
#include "seed_sp.h"
#include "vr.h"
#include "strand_mgr.h"
#include "surfel_advect_sp.h"
#include "surfel_dyn_sp.h"
#include "bsurfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "voxel_dyn_sp.h"
#include "box_advect.h"
#include "shob_allocator.h"
#include "trajectory_window.h"
#include PHYSICS_H
#include "parse_shob_descs_factory.h"
#include SIMSIZES_SHARED_H
#include "face_polygons.h"
#include "wsurfel_comm.h"
#include "conduction_contact_averaging.h"


//=======================================================================================================
// SURFELS/BSURFELS/UBLKS COMMON UTILS : SIMENG SPECIFIC

#if !BUILD_FOR_SIMSIZES
//=======================================================================================================

inline VOID read_ckpt_shob_header(SHOB_ID &shob_id, DGF_SHOB_CKPT_LEN &len)
{
  cDGF_CKPT_SHOB_HEADER header;
  header.read(g_lgi_stream);
  shob_id = header.shob_id;
  len = header.len;
}

// CONDUCTION-TODO: Remove after unifying isurfel meas parsing
static BOOLEAN is_APM_fluid_region(asINT32 fluid_region_index) {
  if (fluid_region_index >= 0) {
    return is_pd_apm(volume_physics_desc_from_part_index(fluid_region_index));
  }
  return FALSE;
}

static BOOLEAN is_region_conduction_solid(asINT32 region_index) {
  if (region_index >= 0) {
    return is_pd_conduction_solid(volume_physics_desc_from_part_index(region_index));
  }
  return FALSE;
}



#endif //Utils

//=======================================================================================================
// PARSE BSURFELS : SIMENG SPECIFIC

#if !BUILD_FOR_SIMSIZES
//=======================================================================================================
static inline sBSURFEL* allocate_bsurfel(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom) 
{
  size_t n_bytes = sizeof(sBSURFEL);
  sBSURFEL *bsurfel_ptr = new sBSURFEL;
  memset(bsurfel_ptr, 0, n_bytes);
  bsurfel_ptr->init(bsurfel_desc, bsurfel_geom, g_ublk_table[STP_FLOW_REALM], sim.char_density, sim.lrf_physics_descs,
                    sim.movb_physics_descs, g_meas_windows);

  return bsurfel_ptr;
}

static inline sBSURFEL* allocate_non_ghost_bsurfel(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom)
{

  sBSURFEL *bsurfel = allocate_bsurfel(bsurfel_desc,bsurfel_geom);

  // place all bsurfels in the interior group. 
  // The actual groups will be set in finish_init_of_bsurfels()
  BSURFEL_GROUP_TYPE bsurfel_group_type = INTERIOR_BSURFEL_GROUP_TYPE;

  bsurfel->move_self_to_new_group(INTERIOR_BSURFEL_GROUP_TYPE);

  return bsurfel;
}

VOID parse_bsurfel_desc(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom) 
{
  // The cp shotguns bsurfels to all sps, so don't allocate bsurfels we don't own.
  UBLK home_ublk = ublk_from_id(bsurfel_desc->bs.ublk_id);
  sBSURFEL* bsurfel = allocate_non_ghost_bsurfel(bsurfel_desc, bsurfel_geom);

  // The cp sends the seeded/checkpointed position of this bsurfel
  // immediately rather than waiting for the seeding phase
  // This is because the bsurfels move and it is not known apriori 
  // which sp they will belong to.
  if (sim.do_smart_seed || sim.is_full_checkpoint_restore) {
    bsurfel_geom->read(g_lgi_stream);
    ccDOTIMES(axis, 3) {
      bsurfel->m_centroid[axis] = bsurfel_geom->centroid[axis];
      bsurfel->m_normal[axis] = bsurfel_geom->normal[axis];
    }
  }
  // printf("Bsurfel %d Centroid (%f,%f,%f) Home Ublk %d Home Voxel %d\n",bsurfel->id(), bsurfel->m_centroid[0], bsurfel->m_centroid[1], bsurfel->m_centroid[2], bsurfel->m_home_ublk->id(), bsurfel->m_home_voxel);
}

#endif //Bsurfels


//=======================================================================================================
// cBASE_SHOB_DESCS_PARSER
//=======================================================================================================

std::ofstream cBASE_SHOB_DESCS_PARSER::ofs;
size_t cBASE_SHOB_DESCS_PARSER::shob_count = 0;
int cBASE_SHOB_DESCS_PARSER::file_count = 0;

/* update_ofs - Utility function for debugging
 * This function is used for upating the output file stream.
 * Every call to update_ofs increments a counter. After a certain
 * counter limit is reached, a new file is opened for write. The
 * idea is to avoid writing large files that can hang tools like
 * kdiff3
 */
VOID cBASE_SHOB_DESCS_PARSER::update_ofs(){

  bool is_start_of_write = (shob_count == 0) && (file_count == 0);
  bool exceeded_file_limit = (shob_count > shobs_per_file);

  if ( exceeded_file_limit ) {
    file_count++;
  }
    
  if ( is_start_of_write || exceeded_file_limit){
    //Close any existing file handles if any are open
    if (ofs && ofs.is_open()) {
      ofs.close();
    }
    
    std::ostringstream os;

#if !BUILD_FOR_SIMSIZES
    os << "sp_" << my_proc_id <<"_simeng_sizes_" << file_count << ".out";
#else
 os << "sp_" << my_proc_id <<"_sim_sizes_" << file_count << ".out";    
#endif
    ofs.open(os.str(),std::ofstream::out);

    if ( !ofs ) {
      throw std::string("Simulator could not open file for writing sizes\n");
    }
          
  }

  if ( exceeded_file_limit ){
    shob_count = 0;
  } else {
    shob_count++;
  }
}

//=======================================================================================================
// PARSE SURFELS
//=======================================================================================================

#if !BUILD_FOR_SIMSIZES
VOID parse_surfel_desc(DGF_SURFEL_DESC surfel_desc, 
                       STP_REALM realm, 
                       STP_PROC home_sp, 
                       RP_PROC rp, 
                       RP_PROC backside_rp,
                       uINT16 ghost_flags, 
                       std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps)
{
  cBASE_SURFEL_DESCS_PARSER* parser = cSURFEL_DESC_PARSER_FACTORY::create_surfel_desc_parser(realm,
                                                                                             surfel_desc,
                                                                                             home_sp,
                                                                                             rp,
                                                                                             backside_rp,
                                                                                             ghost_flags,
                                                                                             ghost_sps);
  parser->parse();
}

VOID sanity_check_paired_contact_surfels() {
  cSURFEL_DESC_PARSER_FACTORY parser_factory;
  parser_factory.sanity_check_paired_contact_surfels_internal();
}

VOID add_dangling_flow_wsurfels_to_groups() {
  cSURFEL_DESC_PARSER_FACTORY parser_factory;
  parser_factory.add_dangling_flow_wsurfels_to_groups_internal();
}
#endif

// PARSE SURFELS UTILITIES
//=======================================================================================================

extern sSURFEL_TABLE g_surfel_table[STP_N_REALMS];
#if DEBUG
extern VMEM_VECTOR <SURFEL> g_mlrf_surfel_vector;
#endif

static VOID compute_surfel_pgram_volumes(DGF_SURFEL_DESC surfel_desc,
                                         dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                                         STP_GEOM_VARIABLE pgram_volumes[N_LATTICE_VECTOR_PAIRS],
                                         dFLOAT dp_pgram_volumes[N_LATTICE_VECTOR_PAIRS],
                                         STP_GEOM_VARIABLE &inverse_tot_pgvm
#if BUILD_D39_LATTICE
                                         , BOOLEAN compute_these_volumes,
                                         dFLOAT& inv_total_pgram_volume_d39,
                                         dFLOAT& inv_total_pgram_volume_d19
#endif
                                         )
{
#if BUILD_D39_LATTICE
  // Extending dimension to 4 to avoid compilation warning
  sdFLOAT weight_d19[4] = {2.0, 1.0, 0.0, 0.0};
  sdFLOAT weight_d39_dmass[N_NONZERO_ENERGIES] = {16.0, 8.0, 2.0, 1.0};
  inv_total_pgram_volume_d39 = 0;
  inv_total_pgram_volume_d19 = 0;
#endif
  ccDOTIMES(j, N_NONZERO_ENERGIES)
    sum_pgram_volumes[j] = 0;

  // The area and normal stored in the surfel descriptor are double precision to ensure
  // that the simulator can replicate the discretizer's accurate calculation of pgram
  // volumes and post advect scale factors.
  dFLOAT area = surfel_desc->s.area;
  dFLOAT normal[3];
  normal[0] = surfel_desc->s.normal[0];
  normal[1] = surfel_desc->s.normal[1];
  normal[2] = surfel_desc->s.normal[2];

  dFLOAT total_pgvm = 0.0;

#ifdef DEBUG_COND
  if (surfel_desc->s.surfel_id == 21934) {
    msg_print("area %g", area);
    msg_print("normal %g %g %g", normal[0], normal[1], normal[2]);
  }
#endif       

  // Compute the pgram volumes in double precision to ensure maximum accuracy
  FOREACH_MOVING_STATE_PAIR(i, cx, cy, cz, energy, weight, {
    // pgram volume is defined as (area * (ci dot normal))
    asINT32 latvec_pair = state_latvec_pair(i);
    dFLOAT dot = cx * normal[0] + cy * normal[1] + cz * normal[2];
    dFLOAT pgram_volume = fabs(area * dot);
    dp_pgram_volumes[latvec_pair] = pgram_volume;
    pgram_volumes[latvec_pair] = pgram_volume;
    dFLOAT volume = (dFLOAT)weight * pgram_volume;
    sum_pgram_volumes[energy - 1] += volume;
#if BUILD_D39_LATTICE
    total_pgvm += latvec_pair_first_layer_float(latvec_pair) * pgram_volume;

    if (compute_these_volumes) {
      inv_total_pgram_volume_d39 += weight_d39_dmass[energy-1] * volume;
      if (energy < 3) {
        inv_total_pgram_volume_d19 += (volume * weight_d19[energy-1]);
      }
    }
#else
    total_pgvm += pgram_volume;
#endif
  });
  inverse_tot_pgvm = total_pgvm == 0 ? 0 : 1.0 / total_pgvm;
#if BUILD_D39_LATTICE
  if (compute_these_volumes) {
    inv_total_pgram_volume_d39 = inv_total_pgram_volume_d39 == 0 ? 0 : 1.0 / inv_total_pgram_volume_d39;
    inv_total_pgram_volume_d19 = inv_total_pgram_volume_d19 == 0 ? 0 : 1.0 / inv_total_pgram_volume_d19;
  }
#endif
}

static inline BOOLEAN same_realm_comm_required(uINT16 ghost_flags, REALM realm) {
  BOOLEAN flow_realm_comm_required = (ghost_flags & DGF_SURFEL_GHOST_FLOW); 
  BOOLEAN cond_realm_comm_required = (ghost_flags & DGF_SURFEL_GHOST_COND); 
  if((realm == STP_FLOW_REALM && flow_realm_comm_required) 
     || (realm == STP_COND_REALM && cond_realm_comm_required)) {
    return TRUE;
  } else {
    return FALSE;
  }
}

static inline BOOLEAN cross_realm_comm_required(uINT16 ghost_flags, REALM realm) {
  BOOLEAN flow_realm_comm_required = (ghost_flags & DGF_SURFEL_GHOST_FLOW); 
  BOOLEAN cond_realm_comm_required = (ghost_flags & DGF_SURFEL_GHOST_COND); 
  if((realm == STP_FLOW_REALM && cond_realm_comm_required) 
     || (realm == STP_COND_REALM && flow_realm_comm_required)) {
    return TRUE;
  } else {
    return FALSE;
  }
}

static inline BOOLEAN contact_comm_required(uINT16 ghost_flags) {
  return (ghost_flags & DGF_SURFEL_GHOST_CONTACT);
}

static inline VOID add_surfel_to_send_groups(SURFEL surfel, std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps) {

  // Initialize to an invalid value
  surfel->m_surfel_group_dest_sp = cNEIGHBOR_SP();

  if (surfel->is_fringe() || surfel->is_conduction_interface()) {
    SCALE scale = surfel->scale();
    BOOLEAN is_conduction_surfel = surfel->is_conduction_surfel();
    BOOLEAN is_conduction_interface = surfel->is_conduction_interface();

    // If the surfel has multiple ghosts, it needs to be represented in multiple send groups, but in only one
    // surfel group. The surfel group should be the one with the lowest-numbered remapped destination SP, because
    // that is the one that will be processed first.

    REALM surfel_realm = is_conduction_surfel ? STP_COND_REALM : STP_FLOW_REALM;
    //cNEIGHBOR_SP min_surfel_group_dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( ghost_sps[0].ghost_sp );
    // Note: the maximum possible nsp is STP_MAX_PROC_ID - 1, so this is safe
    cNEIGHBOR_SP min_surfel_group_dest_sp = cNEIGHBOR_SP::max();
    ccDOTIMES(i, ghost_sps.size()) {
      cNEIGHBOR_SP dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( ghost_sps[i].ghost_sp);
      uINT16 ghost_flags = ghost_sps[i].ghost_flags;
      if(same_realm_comm_required(ghost_flags, surfel_realm)) {      
        min_surfel_group_dest_sp = MIN(g_strand_mgr.m_neighbor_sp_map.get_nsp( ghost_sps[i].ghost_sp), min_surfel_group_dest_sp);
      }
    }
    if(min_surfel_group_dest_sp != cNEIGHBOR_SP::max()) { // There may be cross-realm ghosts but no same-realm ghosts
      surfel->m_surfel_group_dest_sp = min_surfel_group_dest_sp;
    }

    ccDOTIMES(i, ghost_sps.size()) {
      cNEIGHBOR_SP dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( ghost_sps[i].ghost_sp);
      uINT16 ghost_flags = ghost_sps[i].ghost_flags;
      if(same_realm_comm_required(ghost_flags, surfel_realm)) {
        SURFEL_SEND_GROUP send_group = g_surfel_send_fset.create_group(scale, dest_sp);
        send_group->add_quantum(surfel);
      }
      if(is_conduction_interface) {
        if(cross_realm_comm_required(ghost_flags, surfel_realm)) {
          WSURFEL_SEND_GROUP send_group = g_wsurfel_send_fset.create_group(scale, dest_sp);
          send_group->add_surfel(surfel);
        }
        if(contact_comm_required(ghost_flags)) {
          CONTACT_SEND_GROUP send_group = g_contact_send_fset.create_group(scale, dest_sp);
          send_group->add_surfel(surfel);
        }
      }
    }
  }
}


static inline VOID init_conduction_interface_data(SURFEL surfel, DGF_SURFEL_DESC surfel_desc) {
  if (surfel->is_conduction_surface()) { //conduction closed shell
    surfel->conduction_interface_solid_data()->init(surfel, surfel_desc);
  } else if (surfel->is_conduction_shell()) { //conduction open shell
    surfel->conduction_interface_open_shell_data()->init(surfel, surfel_desc);
  } else { //fluid
    surfel->conduction_interface_fluid_data()->init(surfel, surfel_desc);
  }
}

static inline SURFEL add_surfel_to_groups(SURFEL surfel,
                                          REALM realm,
                                          DGF_SURFEL_DESC surfel_desc,
                                          SURFEL_GROUP_SUPERTYPE supertype,
                                          std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps, 
                                          std::map <SURFEL_ID, sSURFEL*> *opposite_parsing_maps) {
  //supertype provided as input rather than computed here from descriptor to catch better potential misuse of this 
  //function when parsing special type of surfels
  SCALE scale = surfel->scale();
  SURFEL_BASE_GROUP_TYPE base_group_type;

  cNEIGHBOR_SP dest_sp;
  if (surfel->is_fringe()) {
    base_group_type = FRINGE_SURFEL_BASE_GROUP_TYPE;
    dest_sp = surfel->m_surfel_group_dest_sp;  
  } else {
    base_group_type = INTERIOR_SURFEL_BASE_GROUP_TYPE;
    if(surfel->is_conduction_interface()) {
      dest_sp = surfel->m_surfel_group_dest_sp;  
    }
  }
  SURFEL_BASE_FSET surfel_group_fset = g_surfel_base_groups[base_group_type];
  SURFEL_GROUP_BASE base_group = surfel_group_fset->create_group(scale, dest_sp, supertype, realm);

  BOOLEAN surfel_send_group_required = FALSE;

  ccDOTIMES(i, ghost_sps.size()) {
    uINT16 ghost_flags = ghost_sps[i].ghost_flags;
    if(same_realm_comm_required(ghost_flags, realm))
      surfel_send_group_required = TRUE;
  }
    
  
  if (surfel_send_group_required) {
    sSURFEL_SEND_GROUP signature;
    signature.m_scale = scale;
    signature.m_dest_sp = dest_sp;
    base_group->m_send_group = g_surfel_send_fset.find_group(&signature);
  }

  //Loads surfel into the group, which depend on the specific supertype
  switch (supertype) {
  case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
  case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE:
  {
    SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(base_group);
    sgroup->add_shob_to_group(surfel);
    surfel->set_group(sgroup);
    break;
  }
  case FLOW_WSURFEL_GROUP_SUPERTYPE:
  case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
  {
    SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(base_group);
    if (surfel_desc->s.opposite_index >= 0) { //open shell
      /* Open shell:
       * - only the fluid wsurfel has an opposite index in an open shell, no need to check supertype
       * - allocated consecutively like paired contact surfels to optimize heat flux computation
       *
       * NOTE: if other side of open shell is not thermal coupled, the fluid wsurfel on the coupled side 
       *      remain in the map until all surfels are parsed and add_dangling_flow_wsurfels_to_groups is called,
       *      who adds the surfel to the groups */
      sINT32 opp_index  = surfel_desc->s.opposite_index;
      auto &map =  opposite_parsing_maps[STP_FLOW_REALM];
      auto it_opp_surfel = map.find(opp_index);
      if (it_opp_surfel == map.end()) {
        sINT32 this_index = surfel_desc->s.surfel_id;
        map[this_index] = surfel;
      } else {
        // Paired surfels can be ghosted on different SPs, so it is necessary to add the surfels to the
        // SURFEL_GROUP associated with the lesser of the two surfels' m_surfel_group_dest_sp variable. Otherwise,
        // variables may be commed before they are updated in wsurfel_and_contact_surfels_hfc_and_dynB.
        if (it_opp_surfel->second->m_surfel_group_dest_sp < surfel->m_surfel_group_dest_sp) {
          sgroup = static_cast<SURFEL_GROUP>(surfel_group_fset->create_group(scale,
              it_opp_surfel->second->m_surfel_group_dest_sp, supertype, realm));
        }
        sgroup->add_shob_to_group(it_opp_surfel->second);
        sgroup->add_shob_to_group(surfel);
        map.erase(it_opp_surfel);
      }
    } else { //close shell
      sgroup->add_shob_to_group(surfel);
    }
    surfel->set_group(sgroup);
#if !BUILD_5G_LATTICE    
    init_conduction_interface_data(surfel, surfel_desc);
#endif
    break;
  }
  case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
  case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
  {
    CONTACT_SURFEL_GROUP csgroup = static_cast<CONTACT_SURFEL_GROUP>(base_group);
    if (surfel_desc->s.opposite_index >= 0  && ((surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) == 0)) { 
      //Conformal contact, with two surfels paired:
      //When parsing first surfel, its pointer is stored in a map rather than added to the group so it can 
      //be retrieved later when parsing the second surfel of the pair, so both surfels are loaded in the 
      //group consecutively. 
      //Once both are loaded, the entry of the first one is removed from the map. Since the discretizer arrangges  
      //for an paired surfels to be near each other in the LGI file, the size of the map will never be very large. 
      //Additionally, no need to check for ghosts since paired surfels live on the same SP given that they belong 
      //to the same realm and are in the same location.
      sINT32 opp_index  = surfel_desc->s.opposite_index;
      auto &map =  opposite_parsing_maps[STP_COND_REALM];
      auto it_opp_surfel = map.find(opp_index);
      if (it_opp_surfel == map.end()) {
        sINT32 this_index = surfel_desc->s.surfel_id;
        map[this_index] = surfel;
        surfel->set_group(csgroup);
      } else {
        auto opp_surfel = it_opp_surfel->second;
        cNEIGHBOR_SP opp_dest_sp = opp_surfel->m_surfel_group_dest_sp;


        // Paired surfels can be ghosted on different SPs, so it is necessary to add the surfels to the
        // CONTACT_SURFEL_GROUP associated with the lesser of the two surfels' m_surfel_group_dest_sp variable.
        // Otherwise, variables may be commed before they are updated in wsurfel_and_contact_surfels_hfc_and_dynB.
        if (opp_dest_sp.is_valid()) {
          if (!dest_sp.is_valid() || (opp_dest_sp < dest_sp)) {
            base_group = opp_surfel->m_group;
            csgroup = static_cast<CONTACT_SURFEL_GROUP>(base_group);
            surfel->set_fringe(true);
          } 
        }

        //loaded so primary side goes first independently of the order in which are stored in LGI
        if (surfel_desc->s.surfel_flags & DGF_CONTACT_SURFEL_PRIMARY) {
          csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_PAIRED);
          csgroup->add_surfel_to_group(it_opp_surfel->second, CONTACT_SURFEL_PAIRED);
        } else {
          csgroup->add_surfel_to_group(it_opp_surfel->second, CONTACT_SURFEL_PAIRED);
          csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_PAIRED);
        }

        opp_surfel->set_group(csgroup);
        surfel->set_group(csgroup);

        map.erase(it_opp_surfel);
      }
    }
    else if (surfel_desc->s.surfel_flags & DGF_CONTACT_SURFEL_PRIMARY) {
      if (surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) {
        csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_PRIMARY_AVERAGED);
      } else {
        csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_PRIMARY);
      }
      surfel->set_group(csgroup);
    } 
    else { //if (surfel_desc->s.surfel_flags & DGF_CONTACT_SURFEL_SECONDARY) {
      if (surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) {
        csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_SECONDARY_AVERAGED);
      } else {
        csgroup->add_surfel_to_group(surfel, CONTACT_SURFEL_SECONDARY);
      }
      surfel->set_group(csgroup);
    }

#if !BUILD_5G_LATTICE    
    init_conduction_interface_data(surfel, surfel_desc);
#endif
    break;
  }
  default:
    msg_error("Cannot add super type %d to groups", supertype);
    break;
  }
  return surfel;
}

static inline BOOLEAN is_backside_face_surfel(auINT32 surfel_flags) {
  //For a surfel to be linked to the backside face, it should be
  //- not an APM, front and back surfels are created in an open-shell fashion, but only one PD is defined and linked to the front
  //- not a conduction surfel (conduction surfels always linked to the front)
  //- flagged as inverted open-shell
  if (surfel_flags & (DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE | DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) {
    return FALSE;
  } 
  return (surfel_flags & DGF_SURFEL_OPEN_SHELL_INVERTED);
}


static VOID fill_surfel_uds_data(SURFEL surfel, DGF_SURFEL_DESC surfel_desc) {
#ifdef MERGE_VINIT_NOTES
  // This code does not belong in this file.
  // It should be moved to physics
  // It does not support more than 1 uds
#endif
    asINT32 face_index = surfel_desc->s.face_index;
    bool is_conduction_surfel = surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION);
    bool is_volume_conduction = surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION;
    bool is_backside = is_backside_face_surfel(surfel_desc->s.surfel_flags);
    PHYSICS_DESCRIPTOR surface_phys_desc = surface_physics_desc_from_face_index(face_index, is_backside, is_conduction_surfel, is_volume_conduction);
    if (surface_phys_desc  == NULL)
      msg_error("Unknown physics description");

    CDI_PHYS_TYPE_DESCRIPTOR phys_type = surface_phys_desc->phys_type_desc;
    BOOLEAN is_uds_lb_solver_on = (sim.uds_solver_type == LB_UDS) ? TRUE : FALSE;  
    if (is_uds_lb_solver_on) {
#if BUILD_5G_LATTICE
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data(nth_uds);
	surfel_uds_data->is_flux_bc = TRUE;
	surfel_uds_data->uds_flux_bc = 0.0;
	surfel_uds_data->uds_value_bc = 0.0;
	surfel_uds_data->is_outlet_bc = FALSE;
      }
#else
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(phys_type->cdi_physics_type);      
      if (uds_phys_type_desc == NULL)
	msg_error("Unknown uds surafce physics description");
      
      if (uds_phys_type_desc->uds_physics_type == CDI_UDS_PHYS_WALL) {	
	WALL_UDS_PARAMETERS uds_parameters = (WALL_UDS_PARAMETERS)surface_phys_desc->uds_parameters();
	if (uds_parameters == NULL) 
	  msg_error("Wall surfel (%d) does not have uds parameters. is_mirror=%d", surfel->id(), surfel->is_mirror());
	
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data(nth_uds);
	  surfel_uds_data->is_outlet_bc = FALSE;
	  surfel_uds_data->uds_flux_bc = 0.0;
	  surfel_uds_data->uds_value_bc = 0.0;
	  
	  if (uds_parameters->wall_uds_boundary_type.value == CDI_WALL_SCALAR_VALUE_BC) {
	    surfel_uds_data->is_flux_bc = FALSE;
	    surfel_uds_data->uds_value_bc = uds_parameters->uds_value.value;
	  } else if (uds_parameters->wall_uds_boundary_type.value == CDI_WALL_SCALAR_FLUX_BC) {
	    surfel_uds_data->is_flux_bc = TRUE;
	    surfel_uds_data->uds_flux_bc = uds_parameters->uds_flux.value * g_density_scale_factor;
	  } else {
	    msg_error("Unknown wall scalar bounday type (%d).", (int)(uds_parameters->wall_uds_boundary_type.value));
	  }
	  
	  uds_parameters++;
	}
      } else if (uds_phys_type_desc->uds_physics_type == CDI_UDS_PHYS_INLET_OUTLET) {
	IO_UDS_PARAMETERS uds_parameters = (IO_UDS_PARAMETERS)surface_phys_desc->uds_parameters();
	if (uds_parameters == NULL) 
	  msg_error("IO surfel (%d) does not have uds parameters. is_mirror=%d", surfel->id(), surfel->is_mirror());
	
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data(nth_uds);
	  surfel_uds_data->is_flux_bc = FALSE;
	  surfel_uds_data->uds_flux_bc = 0.0;
	  surfel_uds_data->uds_value_bc = 0.0;
	  
	  if (uds_parameters->uds_value.value < SURFACE_Q_LOWER_BOUND) {//outlet 
	    surfel_uds_data->is_outlet_bc = TRUE;  // not use "reverse_flow_composition"
	  } else { //inlet
	    surfel_uds_data->is_outlet_bc = FALSE;
	    surfel_uds_data->uds_value_bc = uds_parameters->uds_value.value;  //not use  "no_uds_diffusion", "mean_value".
	  }

	  uds_parameters++;
	}	
      } else {
	msg_error("Unknown uds surafce physics tye (%d)",  uds_phys_type_desc->uds_physics_type);
      }
	
#endif
    } else {  //PDE solver
      PHYSICS_VARIABLE parameters = surface_phys_desc->parameters();
      // surfel can containe either a is_condensable/film_thickness phys_type_descriptor or
      // or CPVI_WATER_MASS_FRACTION, CPVI_RELATIVE_HUMIDITY  type bc's ( these are sliding wall/ inlet outlet)
      // 1. Check if either of these variable sets are present
      // 2. resolve if the bc has a value assigned to it
      // First check initial conditions - this will only loop on wall type surfels, inlets and outlets don't have
      // All surfaces don't have initial conditions
      cdiINT32 is_condensable_index = phys_type->continuous_dp_index(CDI_VAR_ID_CONDENSABLE_SURFACE);
      // Flux boundary conditions for inlet/outlet ( not initial conditions
      // Outlets should have only old physics values
      cdiINT32 vapor_mass_index = phys_type->continuous_dp_index(CDI_VAR_ID_WATER_MASS_FRACTION);
      cdiINT32 relative_humidity_index = phys_type->continuous_dp_index(CDI_VAR_ID_RELATIVE_HUMIDITY);            

      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data(nth_uds);
	surfel_uds_data->is_condensable = FALSE;
	surfel_uds_data->is_flux_bc = FALSE;
	surfel_uds_data->is_outlet_bc = FALSE;
	if (is_condensable_index > 0 ) {
	  if( parameters[is_condensable_index].value > 0 ){
	    surfel_uds_data->is_condensable = TRUE;
	    cdiINT32 film_index = phys_type->continuous_dp_index(CDI_VAR_ID_FILM_THICKNESS);
	    surfel_uds_data->uds_flux_bc = 0.0;
	    surfel_uds_data->uds_thickness = parameters[film_index].value;
	    surfel_uds_data->defrost_time = -1.0;
	  } else { // not a condensable surface - then its just a regular surfel	    
	    surfel_uds_data->is_flux_bc = TRUE;
	    surfel_uds_data->uds_flux_bc = 0.0;	   
	    surfel_uds_data->uds_thickness = 0;
	    surfel_uds_data->defrost_time = -1.0;
	  }
	} else if (vapor_mass_index > 0 ) { // This should always be true for inlet "surfaces" ( i.e. following could be 'else' not if )
	  // If either are set, then inlet
	  if( parameters[vapor_mass_index].value > 0 || parameters[relative_humidity_index].value ){
	    if(parameters[vapor_mass_index].value < 0 && parameters[relative_humidity_index].value < 0){ // Outlets don't have either of the new physics variables
	      surfel_uds_data->is_outlet_bc = TRUE;
	      surfel_uds_data->uds_value_bc = 0.0;
	    } else if(parameters[vapor_mass_index].value > 0){	      
	      surfel_uds_data->uds_value = parameters[vapor_mass_index].value;
	      surfel_uds_data->uds_value_bc = surfel_uds_data->uds_value;
	    } else if (parameters[relative_humidity_index].value > 0) {
	      surfel_uds_data->uds_value = convert_rh_to_mass_fraction(parameters[relative_humidity_index].value, (surfel->t_data())->temp_bc);
	      surfel_uds_data->uds_value_bc = surfel_uds_data->uds_value; // Conversion between relative humidity and mass fraction goes here
	    } else {
	      surfel_uds_data->uds_value_bc = 0;
	    }
	  }
	} else {	  
	  msg_error("Unknown surfel type");
	}      
      }
    }
}

static inline BOOLEAN is_interior_surfel( SURFEL surfel, SURFEL opposite_surfel) {


  asINT32 opposite_containing_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;
  asINT32 surfel_containing_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;
  asINT32 opposite_lrf_index = opposite_surfel->ref_frame_index();
  asINT32 surfel_lrf_index = surfel->ref_frame_index();
  if (opposite_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX)
    opposite_containing_lrf_index = sim.lrf_physics_descs[opposite_lrf_index].containing_lrf_index;
  else
    return TRUE;

  if (surfel_lrf_index !=  SRI_GLOBAL_REF_FRAME_INDEX)
    surfel_containing_lrf_index = sim.lrf_physics_descs[surfel_lrf_index].containing_lrf_index;
  else
    return FALSE;

  if (opposite_containing_lrf_index == surfel_lrf_index) 
    return FALSE;
  else if (surfel_containing_lrf_index == opposite_lrf_index) 
    return TRUE;
  else 
    msg_internal_error("Bad interior/exterior surfel pair for LRF surfel %d for surfel %d", 
                       surfel->id(),opposite_surfel->id());
  
  return FALSE;
}

// For nested LRF, we assign the innermost (contained) LRF to Surfel Quantum
static inline asINT32 get_lrf_index( SURFEL surfel, SURFEL opposite_surfel) {


  asINT32 lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;
  asINT32 opposite_containing_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;
  asINT32 surfel_containing_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;
  asINT32 opposite_lrf_index = opposite_surfel->ref_frame_index();
  asINT32 surfel_lrf_index = surfel->ref_frame_index();
  if (opposite_lrf_index > SRI_GLOBAL_REF_FRAME_INDEX)
    opposite_containing_lrf_index = sim.lrf_physics_descs[opposite_lrf_index].containing_lrf_index;
  if (surfel_lrf_index > SRI_GLOBAL_REF_FRAME_INDEX)
    surfel_containing_lrf_index = sim.lrf_physics_descs[surfel_lrf_index].containing_lrf_index;
  
  if (opposite_containing_lrf_index == surfel_lrf_index) {
    lrf_index = opposite_lrf_index;
  } else if (surfel_containing_lrf_index == opposite_lrf_index) {
    lrf_index = surfel_lrf_index;
  } else {
    lrf_index = opposite_surfel->ref_frame_index() >= 0?
                opposite_surfel->ref_frame_index() :
                surfel->ref_frame_index();
  }
 
  return lrf_index;
}

asINT32 SIMULATOR_NAMESPACE::surfel_type_from_desc(BOOLEAN is_S2S_destination, 
			      STP_EVEN_ODD even_odd, 
			      auINT32 flags,  
			      BOOLEAN is_ghost, 
			      BOOLEAN has_ghosts,
			      BOOLEAN is_fringe,
                              BOOLEAN is_conduction_shell,
                              BOOLEAN is_conduction_interface,
                              BOOLEAN is_radiation) {

  asINT32 surfel_type = 0;

  if (is_S2S_destination && !is_ghost) {
    surfel_type |= (1 << S2S_ADVECT_DATA_BIT);
  }
  if ((flags & DGF_SURFEL_IS_S2S_SOURCE) && !is_ghost) {
    surfel_type |= (1 << TWO_SURFEL_STATES_DATA_BIT);
  }
  // TODO The discretizer should not mark mirror surfels with has_mirror bit
  if ((flags & DGF_SURFEL_HAS_MIRROR) && !(flags & DGF_SURFEL_IS_MIRROR)) {
    surfel_type |= (1 << SURFEL_MIRROR_DATA_BIT);
    if (flags & DGF_SURFEL_REFERENCE_FRAME)
      msg_error("Mirror MLRF or SLRF surfels are not supported in nextgen.");
  } else if (flags & DGF_SURFEL_REFERENCE_FRAME) {
    if (flags & DGF_SURFEL_IS_SLIDING_MESH) {
      surfel_type |= (1 << SURFEL_MLRF_DATA_BIT);
      surfel_type |= (1 << SURFEL_V2S_DATA_BIT);
    } else {
      surfel_type |= (1 << SURFEL_SLRF_DATA_BIT);
    }
  }
 
  //can be both isurfel and has mirror
  if (flags & (DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE)) {
    surfel_type |= (1 << SURFEL_INTERFACE_DATA_BIT);
  }

  if (even_odd != STP_PROCESS_ON_ALL_TIMES) {
    surfel_type |= (1 << SURFEL_EVEN_ODD_DATA_BIT);
    if (!(flags & DGF_SURFEL_IS_MIRROR))
      surfel_type |= (1 << SURFEL_V2S_DATA_BIT);
  }

  if (has_ghosts || is_ghost || is_fringe) {
     surfel_type |= (1 << SURFEL_V2S_DATA_BIT);
  }
  
  if (flags & DGF_SURFEL_VOLUME_CONDUCTION) {
#if !BUILD_GPU
    surfel_type |= (1 << SURFEL_CONDUCTION_DATA_BIT);
    // CONDUCTION-TODO: Eventually, if these come from the disc, the following should be automatically taken care of.
    // For now, commenting out since we are not optimizing the memory yet.
    // If surfel is interacting with conduction solid, we can switch off TWO_SURFEL_STATES_DATA_BIT
    // and SURFEL_V2S_DATA_BIT

    // surfel_type &= ~(1 << TWO_SURFEL_STATES_DATA_BIT);
    // surfel_type &= ~(1 << SURFEL_V2S_DATA_BIT);
#endif
  }

  if (is_conduction_shell) {
#if !BUILD_GPU
    surfel_type |= (1 << SURFEL_SHELL_CONDUCTION_DATA_BIT);
#endif
  }

  if (is_conduction_interface) {
#if !BUILD_GPU
    surfel_type |= (1 << SURFEL_CONDUCTION_INTERFACE_DATA_BIT);
    if(!is_conduction_shell && !(flags & DGF_SURFEL_VOLUME_CONDUCTION)) {
      // Flow surfel in conduction interface
      surfel_type |= (1 << SURFEL_V2S_DATA_BIT);
    }     
#endif
  }
    
  if (is_radiation && !is_ghost) {
    // ghosts do not need radiation data blocks
#if !BUILD_GPU
    surfel_type |= (1 << SURFEL_RADIATION_DATA_BIT);
#endif
  }

  return surfel_type;
}

//=======================================================================================================
// PARSE SHOB SURFEL DESCRIPTOR BASE
//=======================================================================================================
//Used to initialize reference variable
static std::vector<cDGF_SURFEL_PROC_GHOST> dummy_ghost_vec = std::vector<cDGF_SURFEL_PROC_GHOST>();

cBASE_SURFEL_DESCS_PARSER::cBASE_SURFEL_DESCS_PARSER()
  :cBASE_SHOB_DESCS_PARSER(),
   m_realm(STP_INVALID_REALM),
   m_surfel_desc(nullptr),
   m_phys_type(STP_INVALID_PHYSTYPE_TYPE),
   m_ghost_sps(dummy_ghost_vec),
   m_home_sp(STP_PROC()),
   m_seed_from_meas_data(cDGF_SEED_FROM_MEAS_DATA())
{ 
}


cBASE_SURFEL_DESCS_PARSER::~cBASE_SURFEL_DESCS_PARSER(){
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_sampling_surfel() const {
  cassert(m_surfel_desc);
  return (m_surfel_desc->s.surfel_flags & DGF_SURFEL_SAMPLING);
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_reference_frame() const{
  cassert(m_surfel_desc);
  return (m_surfel_desc->s.surfel_flags & DGF_SURFEL_REFERENCE_FRAME);
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_mirror() const {
  cassert(m_surfel_desc);
  return (m_surfel_desc->s.surfel_flags & DGF_SURFEL_IS_MIRROR);
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_ghost() const {
  return m_home_sp != my_proc_id;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_sliding_mesh() const {
  cassert(m_surfel_desc);
  return m_surfel_desc->s.surfel_flags & DGF_SURFEL_IS_SLIDING_MESH;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::has_ghosts() const {
  return ( !is_ghost() ) && ( m_ghost_sps.size() > 0 );
}

#if 0
inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::has_flow_ghosts() const {
  if(!is_ghost() && (m_ghost_sps.size() > 0)) {
    ccDOTIMES(i, m_ghost_sps.size()) {
      uINT16 ghost_flags = m_ghost_sps[i].ghost_flags;
      if(ghost_flags & DGF_SURFEL_GHOST_FLOW) {
        return TRUE;
      }
    }
  }
  return FALSE;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::has_cond_ghosts() const {
  if(!is_ghost() && (m_ghost_sps.size() > 0)) {
    ccDOTIMES(i, m_gghost_sps.size()) {
      uINT16 ghost_flags = m_gghost_sps[i].ghost_flags;
      if(ghost_flags & (DGF_SURFEL_GHOST_COND | DGF_SURFEL_GHOST_CONTACT)) {
        return TRUE;
      }
    }
  }
  return FALSE;
}
#endif

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::has_same_realm_ghosts() const {
  if(!is_ghost() && (m_ghost_sps.size() > 0)) {
    ccDOTIMES(i, m_ghost_sps.size()) {
      uINT16 ghost_flags = m_ghost_sps[i].ghost_flags;
      BOOLEAN flow_realm_comm_required = ghost_flags & DGF_SURFEL_GHOST_FLOW; 
      BOOLEAN cond_realm_comm_required = ghost_flags & DGF_SURFEL_GHOST_COND; 
      if((flow_realm_comm_required && (m_realm == STP_FLOW_REALM)) ||
         (cond_realm_comm_required && (m_realm == STP_COND_REALM))) {
        return TRUE;
      }
    }
  }
  return FALSE;
}

inline
STP_EVEN_ODD cBASE_SURFEL_DESCS_PARSER::even_odd() const {
  cassert(m_surfel_desc);
  return (m_surfel_desc->s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK);
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_fringe() const {
  return has_same_realm_ghosts() || (!is_ghost() && is_mirror_of_fringe_surfel());
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_S2S_destination() const {
  cassert(m_surfel_desc);
  return m_surfel_desc->s.num_surfel_weight_sets > 0;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::interacts_with_conduction_volume() const {
  return m_surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_conduction_shell() const {
  return m_surfel_desc->s.surfel_flags & DGF_SURFEL_SHELL_CONDUCTION;
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_conduction_surfel() const {
  return interacts_with_conduction_volume() || is_conduction_shell();
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_conduction_open_shell() const {
  return !interacts_with_conduction_volume() && is_conduction_shell();
}

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_conduction_open_shell_boundary() const {
    return (interacts_with_conduction_volume() &&
        !is_conduction_shell() && (m_surfel_desc->s.num_interacting_ublks == 0));
  }

inline
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_conduction_interface() const {
  if (m_surfel_desc->s.surfel_flags & (DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY)) {
    return TRUE; //contact surfel
  } else if (m_surfel_desc->s.coupled_index >= 0) {
    return TRUE; //wsurfel
  } else {
    return FALSE;
  }
  //return FALSE;
}
      
  
BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_radiation() const {
  return m_surfel_desc->s.radiation_patch_id >= 0;
}

inline
size_t cBASE_SURFEL_DESCS_PARSER::SIZE() const {
  
  auINT32 flags = (auINT32)(m_surfel_desc->s.surfel_flags);

  //Only mirror surfels are allowed to have invalid phystype at this stage
  // assert( (m_phys_type != STP_INVALID_PHYSTYPE_TYPE) || is_mirror());
  if (!((m_phys_type != STP_INVALID_PHYSTYPE_TYPE) || is_mirror())) {
    auto& dgf_surfel = m_surfel_desc->s;
    msg_internal_error("cBASE_SURFEL_DESCS_PARSER::SIZE(). Surfel %u at (%1.4e, %1.4e, %1.4e) has an invalid phystype",
      dgf_surfel.surfel_id, dgf_surfel.centroid[0], dgf_surfel.centroid[1], dgf_surfel.centroid[2]);
  }

  BOOLEAN is_ci = is_conduction_interface();
  asINT32 surfel_type = surfel_type_from_desc(is_S2S_destination(),
                                              even_odd(),
                                              flags,
                                              is_ghost(),
                                              has_ghosts(),
                                              is_fringe(),
                                              is_conduction_shell(),
                                              is_ci,
                                              is_radiation());

#ifndef CONDUCTION_PERMIT_REDUNDANT_STORAGE_FOR_SHELLS
  // CONDUCTION-TODO: For now, both surfels of a double-sided pair get allocated with additional storage
  // for shell conduction. Only the surfel designated as "interior" goes through shell conduction calculations
  // and we should NOT allocate storage for "exterior" surfel
  // JEH This comment is totally wrong. Conduction surfels in contact will retain knowledge of their individual shell configurations.
  if (is_isurfel() && is_conduction_shell())
    msg_internal_error("Both surfels on ISurfels are being allocated extra storage for shell conduction");
#endif

  // Layers data is no longer stored on surfels. Hence no extra storage is
  // needed except SHELL_CONDUCTION_DATA block. 
  return sSURFEL::size(surfel_type, is_mirror(), m_phys_type, m_back_phys_type);
}

inline
VOID cBASE_SURFEL_DESCS_PARSER::reset(){
  m_realm       = STP_INVALID_REALM;
  m_surfel_desc = nullptr;
  
  m_phys_type = STP_INVALID_PHYSTYPE_TYPE;
  m_back_phys_type = STP_INVALID_PHYSTYPE_TYPE;
  m_surface_phys_desc = nullptr;
  m_face_surface_phys_desc = nullptr;
  m_back_face_surface_phys_desc = nullptr;
  
  m_ghost_sps = dummy_ghost_vec;
  m_home_sp   = 0;
  m_rp        = RP_PROC_INVALID;
  m_backside_rp = RP_PROC_INVALID;
}

//=======================================================================================================
// PARSE BASE SURFEL DESCRIPTOR : SPECIFIC TO SIMENG BUILD
//=======================================================================================================
#if !BUILD_FOR_SIMSIZES

VOID cBASE_SURFEL_DESCS_PARSER::init_seed_from_meas_data()
{
  if (sim.n_seed_from_meas_descs > 0 ) {
    this->m_seed_from_meas_data.read(g_lgi_stream);
    if ( this->m_seed_from_meas_data.descriptor_index >= 0 ) {
      cassert(this->m_seed_from_meas_data.descriptor_index < sim.n_seed_from_meas_descs);
      sSEED_CONTROL *seed_control = &sim.m_seed_from_meas_controls[this->m_seed_from_meas_data.descriptor_index];
      if (seed_control->seed_via_dimless_properties) {
        seed_control->scale_seed_vars_via_dimless_properties(this->m_seed_from_meas_data);
      } else if (seed_control->seed_via_mks) {
        seed_control->scale_seed_vars_via_mks_properties(this->m_seed_from_meas_data);
      }
    }
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::convert_surfel_desc_area_to_local_units(){
  // In DGF, all areas are defined in finest units, but in the simulator, we need the areas
  // defined in local units.
  asINT32 log2_voxel_size = (sim.num_dims - 1) * scale_to_log2_voxel_size(m_surfel_desc->s.surfel_scale);
  m_surfel_desc->s.area *= g_dfloat_inverse_power_of_two[log2_voxel_size];
}

VOID cBASE_SURFEL_DESCS_PARSER::init_basic_surfel_props(SURFEL surfel){
  BOOLEAN is_ci = is_conduction_interface();

  asINT32 surfel_type = surfel_type_from_desc(is_S2S_destination(),
                                              even_odd(),
                                              (auINT32)(m_surfel_desc->s.surfel_flags),
                                              is_ghost(),
                                              has_ghosts(),
                                              is_fringe(),
                                              is_conduction_shell(),
                                              is_ci,
                                              is_radiation());

  surfel->m_surfel_attributes.m_surfel_type = surfel_type;
  surfel->m_surfel_attributes.m_dynamic_bits = 0;
  surfel->set_conduction_interface(is_conduction_interface());
  surfel->set_ghost(is_ghost());
  surfel->set_fringe(is_fringe());
  surfel->set_mirror(is_mirror());
  surfel->init(m_surfel_desc);
  if (is_ghost()) {
    surfel->m_home_sp = m_home_sp;
  } else {
    surfel->m_home_sp = my_proc_id;
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::compute_pgram_volumes(SURFEL surfel){

#if BUILD_D39_LATTICE
  compute_surfel_pgram_volumes(m_surfel_desc,
			       m_pgram_info.sum_pgram_volumes,
			       (STP_GEOM_VARIABLE *) surfel->lb_data()->pgram_volumes,
                               surfel->m_dp_pgram_volumes,
			       surfel->lb_data()->inverse_tot_pgvm,
                               (surfel->is_stationary_rf() || surfel->is_isurfel()),
			       m_pgram_info.inv_total_pgram_volume_d39,
			       m_pgram_info.inv_total_pgram_volume_d19);
#else
  compute_surfel_pgram_volumes(m_surfel_desc,
			       m_pgram_info.sum_pgram_volumes,
			       (STP_GEOM_VARIABLE *)  surfel->lb_data()->pgram_volumes,
                               surfel->m_dp_pgram_volumes,
			       surfel->lb_data()->inverse_tot_pgvm);
#endif

}


VOID cBASE_SURFEL_DESCS_PARSER::add_ublk_interactions(sSURFACE_SHOB* surf_shob){

#ifdef ENABLE_CONSISTENCY_CHECKS
  if (m_surfel_desc->s.num_interacting_ublks == 0 && m_surfel_desc->s.num_surfel_weight_sets == 0) {
    // Except ghost, conduction open shells and conduction open shell edges,
    // rest of surfels expected to have interactions
    if (!is_ghost() && !is_conduction_open_shell() && !is_conduction_open_shell_boundary())
      msg_warn("Physics surfel %d has no UBLK/surfel interactions", surf_shob->id());
  }
#endif

  SURFEL_UBLK_INTERACTION ublk_interactions;
  uINT16 n_ublk_interactions;
  find_ublk_interactions(m_realm, m_surfel_desc, &n_ublk_interactions, &ublk_interactions, is_ghost());

  if (is_sampling_surfel()) {
    SAMPLING_SURFEL surfel = static_cast<SAMPLING_SURFEL> (surf_shob);
    if (surfel) {
      surfel->add_ublk_interactions(n_ublk_interactions, ublk_interactions);
    }
  } else {
    SURFEL surfel = static_cast<SURFEL>(surf_shob);
    if (surfel) {
      surfel->add_ublk_interactions(n_ublk_interactions, ublk_interactions);
    }
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::fill_even_odd_data(SURFEL surfel) {
  // Fill even odd data
  if (even_odd() != STP_PROCESS_ON_ALL_TIMES) {
    if (m_surfel_desc->s.clone_index < 0) {
      bool is_even = even_odd() == STP_PROCESS_ON_EVEN_TIMES;
      msg_internal_error("%s surfel %d does not have the corresponding %s surfel in lgi file.",
                         is_even ? "Even" : "Odd", surfel->id(),
                         is_even ? "odd"  : "even");
    } 
    surfel->set_clone_surfel_index(m_surfel_desc->s.clone_index);
  } 
}

VOID cBASE_SURFEL_DESCS_PARSER::add_S2S_advect_data(SURFEL surfel){
  // Add S2S advect data
  if (m_surfel_desc->s.num_surfel_weight_sets) {
    surfel->add_surfel_interactions(m_surfel_desc);
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::fill_uds_data(SURFEL surfel){
  if (sim.is_scalar_model &&
      !(m_surfel_desc->s.surfel_flags & DGF_SURFEL_REFERENCE_FRAME) && // always true
      !(m_surfel_desc->s.surfel_flags & DGF_SURFEL_IS_SLIDING_MESH) && // always true
      !sim.is_full_checkpoint_restore) {
    fill_surfel_uds_data(surfel, m_surfel_desc);
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::fill_conduction_data(SURFEL surfel){
  // Note that the use of surfel->is_conduction_surface() limits this operation to conduction surfels that talk to voxels.
  if (sim.is_conduction_model && surfel->is_conduction_surface()) {
    surfel->conduction_data()->set_bc_type_from_pd(surfel, m_surface_phys_desc, m_phys_type);
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::fill_conduction_shell_data(SURFEL surfel)
{
  // For a conduction surfel with shell conduction, fill in the shell config physics descriptor. This is
  // taken from the associated face's physics descriptor. The physics descriptor used by the surfel in simulation 
  // may be from an entry from the contact table, which will not have an appropriate shell configuration index.
  if (m_surfel_desc->s.surfel_flags & DGF_SURFEL_SHELL_CONDUCTION) {
    // CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR is an effective base class for all the various conduction
    // surfel physics descriptor types.
    // STP_PHYSTYPE_TYPE face_phys_type = surfel_sim_type_from_cdi_type(m_face_surface_phys_desc);
    CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR pd = (CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR)m_face_surface_phys_desc;
    asINT32 nth_shell_config = pd->parameters()->shell_configuration.value;
    cassert(nth_shell_config >=0 && nth_shell_config<sim.n_shell_config_physics_descs);
    SHELL_CONFIG_PHYSICS_DESCRIPTOR spd = &sim.shell_config_physics_descs[nth_shell_config];
    surfel->shell_conduction_data()->set_shell_config_pd(spd);
    surfel->shell_conduction_data()->set_shell_layer_data_index(g_surfels_shell_layer_data.n_shell_conduction_surfels_layers);
    g_surfels_shell_layer_data.n_shell_conduction_surfels_layers += surfel->shell_conduction_data()->num_layers();
    for (int i = 0; i < surfel->shell_conduction_data()->num_layers(); i++) {
      sSHELL_LAYER_DATA new_layer;
      new_layer.dT_prime = 0.0;
      new_layer.accumulated_dT = 0.0;
      new_layer.accumulated_dE = 0.0;
      new_layer.layer_rad_data_index = -1;
      if (spd->parameters(i)->layer_special_material.value != -1) {
        //air/vacuum layer, needs radiation data, even for insulation layer
        sLAYER_RAD_DATA rad_layer;
        if (spd->parameters(i)->front_rad_surf_cond.value == -1 ||
            spd->parameters(i)->back_rad_surf_cond.value == -1) {
          //no radiation if we dont have radiation surface conditions
          rad_layer.front_emissivity = -1;
          rad_layer.back_emissivity  = -1;
        } else {
          rad_layer.front_emissivity = sim.radiation_surface_conditions.at(spd->parameters(i)->front_rad_surf_cond.value).m_emissivity.value;//1.0;//
          rad_layer.back_emissivity  = sim.radiation_surface_conditions.at(spd->parameters(i)->back_rad_surf_cond.value).m_emissivity.value; //1.0;//
        }
        rad_layer.contact_area     = spd->parameters(i)->layer_contact_area.value;//1.0;//
        new_layer.layer_rad_data_index = g_surfels_shell_layer_data.layer_rad_data.size();
        g_surfels_shell_layer_data.layer_rad_data.push_back(rad_layer);
      }
      g_surfels_shell_layer_data.vec_layers_data.push_back(new_layer);
    }

    if (sim.use_implicit_shell_solver) {
      surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index = m_surfel_desc->s.implicit_shell_state_index;
    }

  }
}

static sPHYSICS_VARIABLE* get_pd_radiation_surface_condition(PHYSICS_DESCRIPTOR pd) {
  auto parms = pd->parameters();
  for (size_t i=0; i<pd->n_parameters; i++) {
    if (pd->phys_type_desc->continuous_dp_var[i]->id == CDI_VAR_ID_RADIATION_SURFACE_COND) {
      return &parms[i];
    }
  }
  return nullptr;
}

VOID cBASE_SURFEL_DESCS_PARSER::fill_radiation_data(SURFEL surfel){
  if (sim.is_radiation_model && this->is_radiation()) {
    g_radiation_patches.register_surfel(surfel, m_surfel_desc->s.radiation_patch_id, m_rp, false);
    auto* rad_data = surfel->radiation_data();

    auto surface_condition_var = get_pd_radiation_surface_condition(m_surface_phys_desc);

    // inlets and outlets don't have a surface condition var, but it should have already been set in the init() functions in physics
    if (surface_condition_var) {
      rad_data->surface_condition = &sim.radiation_surface_conditions.at( (int) surface_condition_var->value );
    }

    if (!rad_data->surface_condition->m_parameter_sharable || rad_data->surface_condition->m_constant_parameter_in_need_of_eval) {
      rad_data->surface_condition->eval_space_and_table_varying_parameter_program(cast_as_regular_ptr(surfel->centroid), cast_as_regular_ptr(surfel->normal), boundary_eqn_error_handler);
    }
    rad_data->emissivity = rad_data->surface_condition->m_emissivity.value;

    sINT32 backside_radiation_patch_id = m_surfel_desc->s.backside_radiation_patch_id; 

    if (backside_radiation_patch_id >= 0) {
      rad_data++;
      // If the open shell is not split into front and back, then m_back_face_surface_phys_desc should be null, and we use the front instead.
      auto backside_surface_condition_var = get_pd_radiation_surface_condition(m_back_face_surface_phys_desc ? m_back_face_surface_phys_desc : m_surface_phys_desc);

      if (backside_surface_condition_var == nullptr) {
        msg_internal_error("Unable to find radiation surface condition in physics descriptor");
      }

      rad_data->surface_condition = &sim.radiation_surface_conditions.at( (int) backside_surface_condition_var->value );

      if (!rad_data->surface_condition->m_parameter_sharable || rad_data->surface_condition->m_constant_parameter_in_need_of_eval) {
        rad_data->surface_condition->eval_space_and_table_varying_parameter_program(cast_as_regular_ptr(surfel->centroid), cast_as_regular_ptr(surfel->normal), boundary_eqn_error_handler);
      }
      rad_data->emissivity = rad_data->surface_condition->m_emissivity.value;
      g_radiation_patches.register_surfel(surfel, m_surfel_desc->s.backside_radiation_patch_id, m_backside_rp, true);
    }
  }
}


VOID cBASE_SURFEL_DESCS_PARSER::fill_mirror_data(SURFEL surfel){

  SURFEL real_surfel = regular_surfel_from_id(m_surfel_desc->m.mirror_index, m_realm);

  // It is OK for ghost mirror surfel to have null real_surfel ptr.
  if (!surfel->is_ghost() && real_surfel == nullptr){
    msg_internal_error("Real surfel is null for mirror surfel %d", m_surfel_desc->s.surfel_id);
  } else if (real_surfel != nullptr) {
    real_surfel->fill_mirror_data(surfel, m_surfel_desc->m.direction, m_surfel_desc->m.weight_mask);
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::maybe_init_surfel_stencil_info(SURFEL surfel) {
#ifndef CONDUCTION_DISABLE_FILM_SOLVER_WITH_SHELL_CONDUCTION
  if (sim.is_shell_conduction_model && sim.is_particle_model) {
    msg_internal_error("Shell conduction cannot work with particle solver yet");
  }
#endif
  if (sim.is_particle_model || sim.is_shell_conduction_model) {
    if (!is_sampling_surfel()) {
      STENCIL_INFO stencil_info = surfel->stencil();
      stencil_info->n_vertices = m_surfel_desc->vertices.size();
      stencil_info->first_vertex_index = g_surfel_vertices_info.n_surfel_vertex_global_indices();
      /** Copy information from stencil_info into p_data till we 
       *  are allowing particles to use old stencil construction code */
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
      if (sim.is_particle_model) {
        surfel->p_data()->n_vertices = stencil_info->n_vertices;
        surfel->p_data()->first_vertex_index = stencil_info->first_vertex_index;
      }
#endif

      DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, m_surfel_desc->vertices) {
        g_surfel_vertices_info.set_is_surfel_vertex_used(vi.vertex_index);
        g_surfel_vertices_info.add_vertex_global_index(vi.vertex_index);
      }
    } else {
      msg_internal_error("Sampling surfels should not call this version of maybe_init_surfel_stencil_info");
    }
  }
}

VOID cBASE_SURFEL_DESCS_PARSER::read_surfel_ckpt_data(SURFEL surfel) {
  if (sim.is_full_checkpoint_restore && surfel && !surfel->is_ghost()) {
    SHOB_ID ckpt_surfel_id;
    DGF_SHOB_CKPT_LEN ckpt_len;
    read_ckpt_shob_header(ckpt_surfel_id, ckpt_len);
    if (ckpt_surfel_id != m_surfel_desc->s.surfel_id)
      msg_internal_error("Inconsistency in surfel order for LGI and checkpoint files (%d vs %d).",
			 m_surfel_desc->s.surfel_id, ckpt_surfel_id);
    surfel->read_ckpt();
  }
}

BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_mirror_of_fringe_surfel() const {
  cassert(!is_ghost());
  if ( is_mirror() ) {
    SURFEL real_surfel = regular_surfel_from_id(m_surfel_desc->m.mirror_index,m_realm);
    if (real_surfel == nullptr){
      msg_internal_error("Real surfel is null for mirror surfel %d", m_surfel_desc->s.surfel_id);
    }
    if ( real_surfel->is_fringe() ){
      return TRUE;
    }
  }
  return FALSE;
}

VOID cBASE_SURFEL_DESCS_PARSER::read_descriptor(){
  if (STP_PROCESS_ON_NO_TIMES == even_odd()) {
    msg_internal_error("Surfel %d does not have its even-odd definition set.", 
                       m_surfel_desc->s.surfel_id);
  }

  init_seed_from_meas_data();
  convert_surfel_desc_area_to_local_units();
  compute_phys_type_from_surface_desc();
}

sSHOB* cBASE_SURFEL_DESCS_PARSER::allocate(){
  size_t n_bytes = SIZE();
  sSURFEL *surfel_ptr = sSHOB_ALLOCATOR<sSURFEL>::malloc(n_bytes);

  memset(surfel_ptr, 0, n_bytes);
  return surfel_ptr;
}

// JEH Should probably be moved to another file (perhaps eqns.cc)
static PHYSICS_DESCRIPTOR default_adiabatic_surface_physics_desc()
{
  static CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR pd = NULL;
  if (pd == NULL) {
    pd = cnew sCONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR;
    pd->all_parameters_sharable = TRUE;
    // pd->boundary_seed_var_spec_index = -1;
    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_ADIABATIC); // adiabatic
    pd->phys_type_desc = phys_type_desc;
    pd->n_parameters = phys_type_desc->n_continuous_dp;
    pd->coupling_model_index = -1;
  
    // parameters: ref_frame and shell configuration index
    CONDUCTION_ADIABATIC_SURFEL_PARAMETERS params = cnew sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS; // sPHYSICS_VARIABLE [ pd->n_parameters ];
    pd->_parameters = (PHYSICS_VARIABLE)params;
    ////params->ref_frame.value = CDI_REF_FRAME_GLOBAL; // thermal surface physics descriptors no longer have a ref_frame variable
    params->shell_configuration.value = -1;
    pd->check_consistency();
  }
  return pd;
}

// When the -cutdown disc option is used, the cdi file does not contain any physics information for the walls of the
// cutdown section. This face is therefore assigned a cdi physics index of -2. In this case, the face is treated as a slip
// surface for turbulence cases and a non-slip surface for DNS cases.
static PHYSICS_DESCRIPTOR cutdown_wall_default_surface_flow_physics_desc()
{
  if (sim.is_turb_model) {
    static SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = NULL;
    if (pd == NULL) {
      pd = cnew sSLIP_SURFEL_PHYSICS_DESCRIPTOR;
      pd->all_parameters_sharable = TRUE;
      CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_SLIP95_COUPLED);
      pd->phys_type_desc = phys_type_desc;
      pd->n_parameters = phys_type_desc->n_continuous_dp;
      pd->coupling_model_index = -1;

      SLIP_SURFEL_PARAMETERS params = cnew sSLIP_SURFEL_PARAMETERS;
      pd->_parameters = (PHYSICS_VARIABLE)params;
      params->ref_frame.value = SRI_GLOBAL_REF_FRAME_INDEX; 
      params->slip_factor.value = 1; 
      params->condensable_surface.value = 0; 
      params->film_thickness.value = 0; 
      params->shell_configuration.value = -1;
      pd->check_consistency();
    }
    return pd;
  } else {
    static NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = NULL;
    if (pd == NULL) {
      pd = cnew sNOSLIP_SURFEL_PHYSICS_DESCRIPTOR;
      pd->all_parameters_sharable = TRUE;
      CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED);
      pd->phys_type_desc = phys_type_desc;
      pd->n_parameters = phys_type_desc->n_continuous_dp;
      pd->coupling_model_index = -1;

      NOSLIP_SURFEL_PARAMETERS params = cnew sNOSLIP_SURFEL_PARAMETERS;
      pd->_parameters = (PHYSICS_VARIABLE)params;
      params->ref_frame.value = SRI_GLOBAL_REF_FRAME_INDEX; 
      params->condensable_surface.value = 0; 
      params->film_thickness.value = 0; 
      params->shell_configuration.value = -1;
      pd->check_consistency();
    }
    return pd;
  }
}

static bool is_contact_resistance_surface_physics_type(asINT32 cdi_phys_type)
{
  return cdi_phys_type == CDI_PHYS_TYPE_CONTACT_RESISTANCE;
}

static bool is_coupled_thermal_surface_physics_type(asINT32 cdi_phys_type)
{
  switch (cdi_phys_type) {
  case CDI_PHYS_TYPE_SLIP95_COUPLED:
  case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:
  case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:
  case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED:
  case CDI_PHYS_TYPE_COUPLED_THERMAL:
    return true;
  }

  return false;
}

VOID cBASE_SURFEL_DESCS_PARSER::compute_phys_type_from_surface_desc()
{
  // m_face_surface_phys_desc is the specific descriptor to the face that the surfel belongs to
  // (for example, if face in contact with another, the layer info is taken from the face BC, not the contact table)
  asINT32 face_index = m_surfel_desc->s.face_index;
  bool is_conduction_surfel = m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION);
  bool is_volume_conduction = m_surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION;
  bool is_backside = is_backside_face_surfel(m_surfel_desc->s.surfel_flags);

  m_face_surface_phys_desc  = surface_physics_desc_from_face_index(face_index, is_backside, is_conduction_surfel, is_volume_conduction);

  // m_back_face_surface_phys_desc and m_back_phys_type reset by default to NULL and STP_INVALID_PHYSTYPE_TYPE when creating 
  // the parser. Modified here only if it is an open shell with distinct front and back BCs
  if (is_conduction_surfel && !is_volume_conduction) {
    //conduction open shells only generate one surfel linked to the front face, so we can get the back physics
    //descriptor directly from it
    asINT32 back_phys_desc_index = thermal_phys_desc_index_from_face_index(face_index, true);
    if (m_face_surface_phys_desc->index != back_phys_desc_index) {
      m_back_face_surface_phys_desc = surface_physics_desc_from_face_index(face_index, true, is_conduction_surfel, is_volume_conduction);
      if (m_back_face_surface_phys_desc)
        this->m_back_phys_type = surfel_sim_type_from_cdi_type(m_back_face_surface_phys_desc);
    }
  }

  /* m_surface_phys_desc is the descriptor to the surface where the face is. Possible cases:
  - Surfel involved in averaged contact: preserve its primary/secondary flags but get descriptor from face
  - Pair or surfels in contact: lookup a surface physics desc based on the governing entry in the contact table.
  - Standalone surfel (includes both wsurfel and regular fluid surfel): get descriptor from face.
  */
  bool is_contact = m_surfel_desc->s.surfel_flags & (DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY);
  bool is_contact_not_averaged = is_contact && ((m_surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) == 0);
  if (is_conduction_surfel && is_contact_not_averaged) { 
    if (!is_volume_conduction) {
      // Open shell involved in contact, ensure we preserve the face physics descriptor in the back even if it has no
      // distinct front and back descriptors
      if (m_back_face_surface_phys_desc == NULL) {
        m_back_face_surface_phys_desc = m_face_surface_phys_desc;
        this->m_back_phys_type = surfel_sim_type_from_cdi_type(m_back_face_surface_phys_desc);
      }
      // Moreover, contact involving conduction open shells only supported if they are not coupled in the back, since we
      // don't support more than two surfels in contact for now
      if (is_coupled_thermal_surface_physics_type(m_back_face_surface_phys_desc->phys_type_desc->cdi_physics_type)) {
        msg_internal_error("Open shells with coupled back BC not supported for thermal contact, \
                            but found in surfel %d", m_surfel_desc->s.surfel_id);
      }
      // To avoid unnecessary conversions from ID to surfel later, just set the coupled index to -1 here
      m_surfel_desc->s.coupled_index = -1;
    }
    // The contact table is a tree with a root entry always present, there must be always a governing entry. 
    // Moreover, that root entry should be thought of as the default for conductor/conductor contact.
    PHYSICS_DESCRIPTOR contact_surface_phys_desc = surface_physics_desc_from_contact_index(m_surfel_desc->s.contact_physics_desc_index,
                                                                                           m_surfel_desc->s.surfel_flags & DGF_CONTACT_SURFEL_SECONDARY);
    if (contact_surface_phys_desc != NULL)
      m_surface_phys_desc = contact_surface_phys_desc;
    else {
      //Contact table should at least return the root entry that is not null. Throw an error if not to catch it here. 
      msg_internal_error("NULL physics descriptor from contact table found");
      m_surface_phys_desc = default_adiabatic_surface_physics_desc();
    }
    // If not coupled even if present in the contact table, each surfel can be processed independently as regular 
    // surfel, so we removes the PRIMARY/SECONDARY flags from here on
    if (!is_contact_resistance_surface_physics_type(m_surface_phys_desc->phys_type_desc->cdi_physics_type)) {
        m_surfel_desc->s.surfel_flags &= ~(DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY);
    }
  } else {
    // Note that in a flow + conduction simulation, a standalone conduction surfel only occurs when a conductor
    // part is in contact with a true insulator (insulator part without layers).
    // Additionally, a flow surfel involved in non-averaged contact should be treated as insulator independently of the
    // face surface descriptor, since no heat comes from the solid
    if (m_face_surface_phys_desc == NULL || is_contact_not_averaged) {
      if (is_conduction_surfel) {
        m_surface_phys_desc = default_adiabatic_surface_physics_desc();
      } else {
        m_surface_phys_desc = cutdown_wall_default_surface_flow_physics_desc();
      }
    } else {
      m_surface_phys_desc = m_face_surface_phys_desc;
    }
    // For flow surfels, remove contact related flags now that have the correct physics desc assigned
    // (note that conduction with averaged contact are routed here as well, but the flags are still meaningful)
    if (is_contact && !is_conduction_surfel) {
      m_surfel_desc->s.surfel_flags &= ~(DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY | DGF_CONTACT_AVERAGED);
    }
    // For wsurfels, check if they are actually coupling both realms. Otherwise, remove coupled index to 
    // treat them as regular surfels
    BOOLEAN is_coupled_physics_type = 
        is_coupled_thermal_surface_physics_type(m_surface_phys_desc->phys_type_desc->cdi_physics_type);
    if (m_back_face_surface_phys_desc) {
      is_coupled_physics_type |= 
          is_coupled_thermal_surface_physics_type(m_back_face_surface_phys_desc->phys_type_desc->cdi_physics_type);
    }
    if (m_surfel_desc->s.coupled_index >= 0 && !is_coupled_physics_type) {
      m_surfel_desc->s.coupled_index = -1;
    }
  }
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    if (m_surfel_desc->s.surfel_id == 5966) 
      msg_print("S %d cond? %d cdi_physics_type %d face %d", m_surfel_desc->s.surfel_id,is_conduction_surfel, m_surface_phys_desc->phys_type_desc->cdi_physics_type, face_index);
#endif
  this->m_phys_type = surfel_sim_type_from_cdi_type(m_surface_phys_desc);
}

#if !BUILD_5G_LATTICE
// CONDUCTION-TODO: Calling the following on dynamics data block may be problematic
// since ghost surfels may not have this. We can call this directly on a new function
// on conduction_data, but that may mean repetition of code to assign the bc values
// CONDUCTION-TODO: Following is lot of repeated code from initialize_dynamics_data
// and will need to be re-looked into.
VOID cGHOST_SURFEL_DESC_PARSER::maybe_init_conduction_ghost_surfel_info(SURFEL surfel) {
  if (surfel->is_conduction_surfel()) {
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
    if (surfel->dynamics_data() == NULL) {
      msg_internal_error("Conduction ghost surfels do not have dynamics data block");
    }
#endif
    // time/space varying paramters were not being evaluated on ghosted
    // esurfels, this was added to ensure that spatially varying parameters
    // are atleast calculated during initialization. Likely need to add code to 
    // ensure time varying parameters are updated properly in ghosted esurfels
    if (m_surface_phys_desc != nullptr && (!m_surface_phys_desc->all_parameters_sharable ||
                                      m_surface_phys_desc->some_constant_parameter_in_need_of_eval)) {
      sdFLOAT *normal = cast_as_regular_array(surfel->normal);
      sdFLOAT inverted_normal[3];
      if (m_surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION) {
        inverted_normal[0] = -surfel->normal[0];
        inverted_normal[1] = -surfel->normal[1];
        inverted_normal[2] = -surfel->normal[2];
        normal = inverted_normal;
      }
      m_surface_phys_desc->eval_space_and_table_varying_parameter_program(cast_as_regular_array(surfel->centroid),
                                                                          normal,
                                                                          boundary_eqn_error_handler);
    }

    dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES] = {};
    switch (m_phys_type) {
    case STP_CONDUCTION_ADIABATIC_SURFEL_TYPE_ID:
    case STP_SLRF_SURFEL_TYPE_ID:   //for ghost lrf surfel
    {
      sCONDUCTION_ADIABATIC_SURFEL_DATA *dyn_data = (sCONDUCTION_ADIABATIC_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    case STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE_ID:
    {
      sCONDUCTION_FIXED_TEMP_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_TEMP_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    case STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE_ID:
    {
      sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    case STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE_ID:
    {
      sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA *dyn_data = (sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    case STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE_ID:
    {
      sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA *dyn_data = (sCONDUCTION_COUPLED_THERMAL_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_COUPLED_THERMAL_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    case STP_CONDUCTION_CONTACT_SURFEL_TYPE_ID:
    {
      sCONDUCTION_CONTACT_SURFEL_DATA *dyn_data = (sCONDUCTION_CONTACT_SURFEL_DATA *) surfel->dynamics_data();
      dyn_data->init((CONDUCTION_CONTACT_SURFEL_PHYSICS_DESCRIPTOR)m_surface_phys_desc, surfel, sum_pgram_volumes);
      break;
    }
    default:
      msg_internal_error("Invalid conduction surfel dynamics type (%d)", m_phys_type);
      break;
    }
  }
}
#endif

//=======================================================================================================
// PARSE BASE SURFEL DESCRIPTOR : SPECIFIC TO SIMSIZES BUILD
//=======================================================================================================
#else

static asINT32 get_s2s_weight_set_size(DGF_SURFEL_DESC surfel_desc){
  DGF_SURFEL_SURFEL_WEIGHT_SET_DESC weight_set = &surfel_desc->weight_sets[0];
  asINT32 total_size = 0;
  for (asINT32 w = 0; w < surfel_desc->s.num_surfel_weight_sets; w++, weight_set++) {
    STP_LATVEC_MASK          latvec_mask      = weight_set->w.latvec_mask;

    asINT32 n_weights = active_latvecs(latvec_mask);
    //if (n_weights > 0) {
    total_size += sSURFEL_SURFEL_INTERACTION::size(n_weights);
    //}
  }
  return total_size;
}

static asINT32 get_v2s_weight_set_size(DGF_SURFEL_DESC surfel_desc){
  cDGF_SURFEL &surfel = surfel_desc->s;

  // For ghost surfels, the number of interacting ublks may be fewer than
  // indicated by the descriptor but for SIMSIZES, we simplify by accounting
  // these ghost ublks
  asINT32 n_interacting_ublks_from_desc = surfel.num_interacting_ublks;

  cDGF_SURFEL_UBLK_WEIGHT_SET_DESC *s_ublk_weight_set_desc =
              &surfel_desc->surfel_ublk_weight_sets[0];

  asINT32 n_bytes_for_ublk_interactions = 0;
  
  ccDOTIMES( iu, n_interacting_ublks_from_desc) {

    asINT32 total_ublk_weights = 0;
    asINT32 n_s_v_weight_sets = s_ublk_weight_set_desc->w.num_s_v_weight_sets;
    
    cDGF_SURFEL_VOXEL_WEIGHT_SET_DESC *s_v_weight_set =
      &s_ublk_weight_set_desc->surfel_voxel_weight_sets[0];

    ccDOTIMES (iv, n_s_v_weight_sets)
      {
	STP_LATVEC_MASK_WORD lv_mask = s_v_weight_set->w.latvec_mask;

	while (lv_mask)  {
	  total_ublk_weights += (lv_mask & 1);
	  lv_mask = lv_mask >> 1;
	}

	s_v_weight_set++;
      }
    
    n_bytes_for_ublk_interactions +=
      sSURFEL_UBLK_INTERACTION::size(total_ublk_weights, n_s_v_weight_sets);

    s_ublk_weight_set_desc++;
  }
  return n_bytes_for_ublk_interactions;
}

asINT32 SIMULATOR_NAMESPACE::get_surfel_weight_set_size(DGF_SURFEL_DESC surfel_desc){

  return get_s2s_weight_set_size(surfel_desc) +
    get_v2s_weight_set_size(surfel_desc);
}

BOOLEAN cBASE_SURFEL_DESCS_PARSER::is_mirror_of_fringe_surfel() const {
  return FALSE;
}

VOID cBASE_SURFEL_DESCS_PARSER::read_descriptor(){}
VOID cBASE_SURFEL_DESCS_PARSER::post_process(sSHOB* shob){}
sSHOB* cBASE_SURFEL_DESCS_PARSER::allocate(){return nullptr;}
VOID cBASE_SURFEL_DESCS_PARSER::compute_phys_type_from_surface_desc(){}


#endif

//=======================================================================================================
// PARSE BASE SURFEL DESCRIPTOR : SPECIFIC TO SIMSENG BUILD

#if !BUILD_FOR_SIMSIZES

//=======================================================================================================

SURFEL_GROUP_SUPERTYPE cREGULAR_SURFEL_DESC_PARSER::surfel_group_supertype() const {
  if (m_surfel_desc->s.surfel_flags & (DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY)) {
    // Contact within same realm:
    // - Averaged, if the contact averaged flag is set,
    //   loads as global since the computation of the averaged flux might involve multiple SPs
    // - Conformal if m_surfel_desc->s.opposite_index >= 0
    //   Since both are within the same SP, loads them directly to the LOCAL supertype
    // - Non-conformal (gap) if m_surfel_desc->s.opposite_index == 0
    //   Assumed GLOBAL by default, potentially being promoted to LOCAL later after loading all surfels if only 
    //   interacts with surfels in the same SP only
    if (m_surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) {
      return CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE;
    } else if (total_csps == 1 || m_surfel_desc->s.opposite_index >= 0) {
      return CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE;
    } else {
      return CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE;
    }
  } else if (m_surfel_desc->s.coupled_index >= 0) { 
    // Contact across realm:
    // - open-shell (fluid-solid-fluid triplet) if opposite_index of this surfel or opposite_index of the surfel 
    //   in contact on the other realm is non-zero
    // - close-shell (solid-fluid contact) otherwise
    // However, both scenarios are treated equally for purposes of assign the supertype.
    if (m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) {
      return CONDUCTION_WSURFEL_GROUP_SUPERTYPE;
    } else {
      return FLOW_WSURFEL_GROUP_SUPERTYPE;
    }
  } else {
    //Non-coupled flow/conduction surfel
    if (m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) {
      return CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE;
    } else {
      return FLOW_DYN_SURFEL_GROUP_SUPERTYPE;
    }
  }
}


VOID cREGULAR_SURFEL_DESC_PARSER::post_process(sSHOB* shob){
  SURFEL surfel = static_cast<SURFEL>(shob);
  
  init_basic_surfel_props(surfel);
  
  compute_pgram_volumes(surfel);
  
  add_ublk_interactions(surfel);

  add_surfel_to_send_groups(surfel, m_ghost_sps);

  add_surfel_to_groups(surfel, m_realm, m_surfel_desc, surfel_group_supertype(), m_ghost_sps, m_opposite_parsing_maps);
  g_surfel_table[m_realm].add(surfel);
  g_ckpt_group.add(surfel, m_realm);

  fill_dynamics_data(surfel);

  fill_even_odd_data(surfel);
  
  add_S2S_advect_data(surfel);
  
  fill_uds_data(surfel);

  fill_conduction_data(surfel);

  fill_radiation_data(surfel);

  fill_meas_windows(surfel);
  
  maybe_init_surfel_stencil_info(surfel);

  read_surfel_ckpt_data(surfel);
}

VOID cREGULAR_SURFEL_DESC_PARSER::fill_dynamics_data(SURFEL surfel) {
  if (m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) {
    fill_conduction_shell_data(surfel);

    tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *front_dyn_data = surfel->front_dynamics_data();
    // The surfel normal is the opposite of the user's notion of the normal, so we invert the normal
    // for use in the equations.
    bool invert_normal = m_surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION;
    initialize_dynamics_data(front_dyn_data, m_phys_type, surfel, m_surface_phys_desc,
                             m_pgram_info.sum_pgram_volumes, m_seed_from_meas_data, invert_normal);

    if (m_back_face_surface_phys_desc) {
      surfel->set_has_distinct_back_bc(true);
      tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *back_dyn_data = front_dyn_data->next_dynamics_data();
      initialize_dynamics_data(back_dyn_data, m_back_phys_type, surfel, m_back_face_surface_phys_desc,
                               m_pgram_info.sum_pgram_volumes, m_seed_from_meas_data, !invert_normal);
    }
  } else {
    // Fill dynamics data
    surfel->fill_dynamics_data(m_surfel_desc, m_phys_type,
                               m_surface_phys_desc,
                               m_pgram_info.sum_pgram_volumes,
                               m_seed_from_meas_data);
  }
}

VOID cREGULAR_SURFEL_DESC_PARSER::fill_meas_windows(SURFEL surfel)
{
  asINT32 n_meas_cells = m_surfel_desc->meas_surfel_refs.size();

  sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptr =
    surfel->create_meas_cell_ptrs(surfel->dynamics_data(), n_meas_cells);

  for (auto& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {

    MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];

    // Convert global meas cell index into local meas cell index
    if (window->is_development) {
      // The CP encodes the global meas surfel index and the axis into meas_surfel_ref.meas_surfel_index
      STP_MEAS_CELL_INDEX global_meas_surfel_index_plus_axis = meas_surfel_ref.meas_surfel_index;
      STP_MEAS_CELL_INDEX global_meas_surfel_index;
      uINT8 axis;
      lgi_meas_cell_index_dev_segment(global_meas_surfel_index_plus_axis, global_meas_surfel_index, axis);

      STP_MEAS_CELL_INDEX meas_cell_index = window->create_surfel_meas_cell(global_meas_surfel_index);
      surfel_meas_cell_ptr->set_window_index(meas_surfel_ref.meas_window_index);
      surfel_meas_cell_ptr->set_index(meas_cell_index);

      surfel_meas_cell_ptr->set_axis((asINT32)axis);
      surfel_meas_cell_ptr->set_face((asINT32)m_surfel_desc->s.face_index);

      if (surfel->is_surfel_moving()) {
        asINT32 n_segments = window->entity_n_segments[axis][m_surfel_desc->s.face_index];
        for (asINT32 j=1; j < n_segments; j++) {
          window->create_surfel_meas_cell(global_meas_surfel_index + j);
        }

      }
    } else {
      STP_MEAS_CELL_INDEX meas_cell_index = window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
      surfel_meas_cell_ptr->set_window_index(meas_surfel_ref.meas_window_index);
      surfel_meas_cell_ptr->set_index(meas_cell_index);

    }
    surfel_meas_cell_ptr++;

    if (window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW)
      msg_internal_error("Regular surfel %d contains sampling surfel meas window references.",
                         surfel->id());
  }
}
//=======================================================================================================
// PARSE BASE SURFEL DESCRIPTOR : SPECIFIC TO SIMSIZES BUILD
#else
//=======================================================================================================
VOID cREGULAR_SURFEL_DESC_PARSER::post_process(sSHOB* shob) {}
#endif

//=======================================================================================================
// PARSE MIRROR SURFEL DESCRIPTOR : SPECIFIC TO SIMENG BUILD

#if !BUILD_FOR_SIMSIZES

//=======================================================================================================

VOID cMIRROR_SURFEL_DESC_PARSER::post_process(sSHOB* shob){
  SURFEL surfel = static_cast<SURFEL>(shob);

  init_basic_surfel_props(surfel);
  
  add_ublk_interactions(surfel);

  compute_pgram_volumes(surfel);

  add_surfel_to_send_groups(surfel, m_ghost_sps);

  g_surfel_table[m_realm].add(surfel);
  g_ckpt_group.add(surfel, m_realm);

  surfel->m_face_index = m_surfel_desc->s.face_index;

  fill_even_odd_data(surfel);
  
  add_S2S_advect_data(surfel);
  
  fill_uds_data(surfel);
  
  // cMIRROR_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() sets m_phys_type for a mirror surfel 
  // to STP_INVALID_PHYS_TYPE_TYPE, which presumably is required post initialization, but fill_conduction_data
  // needs a proper phys_type.
  STP_PHYSTYPE_TYPE orig_phys_type = m_phys_type;
  m_phys_type = surfel_sim_type_from_cdi_type(m_surface_phys_desc);
  fill_conduction_data(surfel);
  fill_conduction_shell_data(surfel);
  m_phys_type = orig_phys_type;

  fill_mirror_data(surfel);

  maybe_init_surfel_stencil_info(surfel);

  read_surfel_ckpt_data(surfel);
}

VOID cMIRROR_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() {
  asINT32 face_index = m_surfel_desc->s.face_index;
  bool is_conduction_surfel = m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION);
  bool is_volume_conduction = m_surfel_desc->s.surfel_flags & DGF_SURFEL_VOLUME_CONDUCTION;
  bool is_backside = is_backside_face_surfel(m_surfel_desc->s.surfel_flags);
  m_surface_phys_desc = surface_physics_desc_from_face_index(face_index, is_backside, is_conduction_surfel, is_volume_conduction);
  m_face_surface_phys_desc =  m_surface_phys_desc;
  this->m_phys_type = STP_INVALID_PHYSTYPE_TYPE;
}
#else
VOID cMIRROR_SURFEL_DESC_PARSER::post_process(sSHOB* shob) {}

VOID cMIRROR_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() {
  this->m_phys_type = STP_INVALID_PHYSTYPE_TYPE;
}
#endif



//=======================================================================================================
// PARSE GHOST SURFEL DESCRIPTOR : SPECIFIC TO SIMENG BUILD

#if !BUILD_FOR_SIMSIZES

//=======================================================================================================

VOID cGHOST_SURFEL_DESC_PARSER::post_process(sSHOB* shob){
  SURFEL surfel = static_cast<SURFEL>(shob);

  init_basic_surfel_props(surfel);
  
  // In this version of code the coupled_index specified by the discretizer may have been overridden by the call to
  // compute_phys_type_from_surface_desc() in read_despriptor(), in which case cross-realm comm is not necessary.
  // However the ghost may have been specified by the decomposer only for the purpose of cross-realm comm. 
  //
  // Nevertheless, for conduction open shells, the quasi 1D solver uses the ghost fluid coupled surfel on the front to
  // get the pointer to the back surfel, even if the front surfel has a fixed BC and is not used for any computation.
  // Thus, we allocate here the ghost and add it to the surfel table, but leaves it unused and not added to any group. 
  //
  // This is quite a "waste" of memory, and would be best not to allocate the ghost surfel at all, but that would
  // require to provide a different way to retrieve the back surfel in the triplet, which might end up wasting even more
  // memory. Additionally, it will require special code in parse(), between read_descriptor() and allocate(), to break
  // out and avoid the allocation.

  if(!surfel->is_conduction_interface() && !same_realm_comm_required(m_ghost_flags,m_realm)) {
    g_surfel_table[m_realm].add(surfel);
    return;
  }

  add_ublk_interactions(surfel);

  compute_pgram_volumes(surfel);

  //Sets the distinct back bc attribute used by the quasi 1D solver to trigger checking 
  //which side of the open shell is coupled
  if (m_back_face_surface_phys_desc) {
    surfel->set_has_distinct_back_bc(true); 
  }

  fill_even_odd_data(surfel); 

  if ( is_mirror() ){
    fill_mirror_data(surfel);
  }

  if(is_conduction_interface()) {
    BOOLEAN same_realm_comm, cross_realm_comm, contact_comm;  
    if(is_conduction_surfel()) {
      same_realm_comm = (m_ghost_flags & DGF_SURFEL_GHOST_COND);
      cross_realm_comm = (m_ghost_flags & DGF_SURFEL_GHOST_FLOW);
      contact_comm = (m_ghost_flags & DGF_SURFEL_GHOST_CONTACT);
    } else {
      same_realm_comm = (m_ghost_flags & DGF_SURFEL_GHOST_FLOW);
      cross_realm_comm = (m_ghost_flags & DGF_SURFEL_GHOST_COND);
      contact_comm = FALSE;
    } 
    if(same_realm_comm) {
      add_ghost_surfel_quantum(surfel);
    }
    if(cross_realm_comm) {
      add_ghost_wsurfel(surfel, !same_realm_comm);
    }
    if(contact_comm) {
      add_ghost_contact(surfel, !(same_realm_comm || cross_realm_comm));
    }
    //Full initialization of conduction interface not needed, just load the coupled surfel id
    surfel->conduction_interface_base_data()->m_coupled_surfel_id = 
        (m_surfel_desc->s.coupled_index > -1) ? m_surfel_desc->s.coupled_index : INVALID_SHOB_ID;
  } else {
    add_ghost_surfel_quantum(surfel);
  }

  g_surfel_table[m_realm].add(surfel);

  fill_conduction_data(surfel);

  fill_conduction_shell_data(surfel);

  maybe_init_surfel_stencil_info(surfel);

#if !BUILD_5G_LATTICE
  maybe_init_conduction_ghost_surfel_info(surfel);
#endif
}

VOID cGHOST_SURFEL_DESC_PARSER::add_ghost_surfel_quantum(SURFEL surfel){
  STP_SCALE scale = m_surfel_desc->s.surfel_scale;
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  SURFEL_RECV_GROUP srgroup = g_surfel_recv_fset.create_group(scale, ssp);
  srgroup->add_quantum(surfel);
  /* Ghost surfels are not added to a surfel group since they don't participate in dynamics. 
   * However, they can belong to more than one recv group if participate in same-realm and cross-realm comms. 
   * Moreover, certain tasks need to be done only by one of the recv groups (like update_surfel_ublk_interactions() 
   * during initialization) so it is necessary to identify the main recv group who is in charge of these operations. 
   * To do so, we "re-use" the surfel->m_group field to store the pointer to the actual main recv group, even if it 
   * is a different type. By default, same-realm is the main recv group and stored if reached here */
  surfel->set_group(reinterpret_cast<SURFEL_GROUP>(srgroup));
}

VOID cGHOST_SURFEL_DESC_PARSER::add_ghost_wsurfel(SURFEL surfel, BOOLEAN is_main_recv_group){
  STP_SCALE scale = m_surfel_desc->s.surfel_scale;
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  WSURFEL_RECV_GROUP wsrgroup = g_wsurfel_recv_fset.create_group(scale, ssp);
  wsrgroup->add_surfel(surfel);
  //stores pointer to wsrgroup (needed if ghost surfel is involved only in cross-realm comm)
  if (is_main_recv_group) {
    surfel->set_group(reinterpret_cast<SURFEL_GROUP>(wsrgroup));
  }
}

VOID cGHOST_SURFEL_DESC_PARSER::add_ghost_contact(SURFEL surfel, BOOLEAN is_main_recv_group){
  STP_SCALE scale = m_surfel_desc->s.surfel_scale;
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  CONTACT_RECV_GROUP crgroup = g_contact_recv_fset.create_group(scale, ssp);
  crgroup->add_surfel(surfel);
  //stores pointer to crgroup (needed if ghost surfel is involved only in contact comm)
  if (is_main_recv_group) {
    surfel->set_group(reinterpret_cast<SURFEL_GROUP>(crgroup));
  }
}

VOID cGHOST_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc(){
  if ( is_reference_frame() ) {
    this->m_phys_type = STP_SLRF_SURFEL_TYPE;
  } else {
    // Call base class method
    cBASE_SURFEL_DESCS_PARSER::compute_phys_type_from_surface_desc();
  }

#if 0
  asINT32 face_index = m_surfel_desc->s.face_index;
  m_surface_phys_desc = surface_physics_desc_from_face_index(face_index);

  if ( is_reference_frame() ) {
    this->m_phys_type = STP_SLRF_SURFEL_TYPE;
  } else {
    //CONDUCTION-TODO: change interacts_with_APM to interacts_with_conduction_solid
    this->m_phys_type = surfel_sim_type_from_cdi_type(m_surface_phys_desc);
  }
#endif
}
//=======================================================================================================
// PARSE GHOST SURFEL DESCRIPTOR : SPECIFIC TO SIMSIZES BUILD
//=======================================================================================================
#else
  VOID cGHOST_SURFEL_DESC_PARSER::post_process(sSHOB* shob) {}
  VOID cGHOST_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() {}
#endif

//=======================================================================================================
// PARSE ISURFEL SURFEL DESCRIPTOR : SPECIFIC TO SIMENG BUILD
//=======================================================================================================
#if !BUILD_FOR_SIMSIZES

VOID cISURFEL_DESC_PARSER::post_process(sSHOB* shob){

  SURFEL surfel = static_cast<SURFEL>(shob);
  
  init_basic_surfel_props(surfel);
  
  add_ublk_interactions(surfel);

  compute_pgram_volumes(surfel);

  add_surfel_to_send_groups(surfel, m_ghost_sps);

  g_surfel_table[m_realm].add(surfel);
  g_ckpt_group.add(surfel, m_realm);

  surfel->m_face_index = m_surfel_desc->s.face_index;

  fill_even_odd_data(surfel);
  
  add_S2S_advect_data(surfel);
  
  fill_conduction_data(surfel);
  
  update_isurfel_meas_windows_and_groups(surfel);
 
  maybe_init_surfel_stencil_info(surfel);

  read_surfel_ckpt_data(surfel);
}

VOID cISURFEL_DESC_PARSER::update_isurfel_meas_windows_and_groups(SURFEL surfel)
{
  /* For now, only APM and SLRF paired sufels are processed as isurfel pairs. If extended to other types, it might 
   * be needed to expand this parser or split it in different methods based on the type */

  sINT32 surfel_index = m_surfel_desc->s.surfel_id;
  sINT32 opposite_index = m_surfel_desc->s.opposite_index;
#ifdef ENABLE_CONSISTENCY_CHECKS  
  if (surfel_index == opposite_index) 
    msg_internal_error("Pair of surfels that form the Isurfel cannot have the same ID %d\n",
		       surfel_index);

  // APM-conduction contact can only have adiabatic BCs, i.e. no conduction surfel should be involved in an isurfel
  BOOLEAN is_conduction_surfel = (m_surfel_desc->s.surfel_flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION));
  BOOLEAN is_across_realms = (m_surfel_desc->s.coupled_index >= 0);
  if (is_across_realms) {
    msg_internal_error("Conduction-flow paired surfels (%d-%d) should not be processed as isurfels",
                        surfel_index, opposite_index);
    
  } else if (is_conduction_surfel) {
     msg_internal_error("Conduction-conduction paired surfels (%d-%d) should not be processed as isurfels", 
                         surfel_index, opposite_index);
  }
#endif
  
  sINT32 fluid_region = m_surfel_desc->s.fluid_region_index;
  PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(fluid_region);
  BOOLEAN foundAPM_this_side = is_APM_fluid_region(fluid_region);

  // This is a map with the key as the ID of the surfel and value as an
  // array of indices of meas cells the surfel contributes to. When we 
  // encounter the first surfel of the quantum, we fill up the meas window
  // and meas cell indices vector from the meas cell references and add this
  // entry to the map and when we encounter the second surfel we retrieve
  // the indices of the measurement windows and cells and remove the entry
  // from the map. The discretizer arranges for an inside/outside pair of
  // isurfels to be near each other in the LGI file so the size of the map
  // will never be very large. 
    
  static std::map < SURFEL_ID, STP_MEAS_CELL_INDEX* >  surfel_to_meas_info_map;

  if (surfel_index < opposite_index) {
    // Accounting for each surfel's measurement cell references 
    sINT32 n_meas_cell_ref_counter = 2;
    // This array stores the following
    // Number of meas cells *2
    // fluid region index
    // meas_window_indices and meas_cell_indices
    STP_MEAS_CELL_INDEX *meas_info = 
      new STP_MEAS_CELL_INDEX [ 2 + 2*m_surfel_desc->meas_surfel_refs.size() ];
    meas_info[0] = 2 * m_surfel_desc->meas_surfel_refs.size(); // stash count in first entry 
    meas_info[1] = fluid_region; // stash fluid region index in the next
    for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
      meas_info[n_meas_cell_ref_counter++] = meas_surfel_ref.meas_window_index;
      MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
      if (window->meas_window_type == LGI_SAMPLING_SURFACE_WINDOW)
	msg_internal_error("Regular surfel %d contains sampling surfel meas window references.", surfel->id());
      // Convert global meas cell index into local meas cell index
      STP_MEAS_CELL_INDEX meas_cell_index = window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
      meas_info[n_meas_cell_ref_counter++] = meas_cell_index;
    }

    // Adding the entry to the map
    surfel_to_meas_info_map[surfel_index] = meas_info;

    if (foundAPM_this_side ) {
      if (meas_info[0] > 0) {
	msg_internal_error("APM isurfel %d should not contribute to measurements", surfel_index);
      }
    }
  } else {

    // find the opposite (exterior or interior) surfel which should already have
    // been created since it has a lower ID and add an isurfel quantum to
    // the isurfel group. Both surfels will be part of the same ISURFEL group.
    //REALM_ASSUMPTION: Surfel is FLOW
    SURFEL opposite_surfel = regular_surfel_from_id(opposite_index);

    if (NULL == opposite_surfel) {
      msg_internal_error("Cannot find opposite Interface surfel %d for surfel %d", 
			 opposite_index, surfel_index);
    }

    asINT32 face_index = m_surfel_desc->s.face_index;
    //isurfels use only one PD associated to the front, no need to check if is_backside
    PHYSICS_DESCRIPTOR surface_phys_desc = surface_physics_desc_from_face_index(face_index, FALSE, FALSE);
    STP_PHYSTYPE_TYPE  sim_phys_type     = surfel_sim_type_from_cdi_type(surface_phys_desc);

    // retrieve the entry cooresponding to the opposite_index
    std::map< SURFEL_ID, STP_MEAS_CELL_INDEX *>::iterator opp_meas_cell_index_surfel_id_iter =
      surfel_to_meas_info_map.find(opposite_index);
    STP_MEAS_CELL_INDEX *opp_meas_info = opp_meas_cell_index_surfel_id_iter->second; 

    static std::vector < MEAS_WINDOW > meas_windows;
    meas_windows.clear();
    sINT32 opposite_fluid_region = opp_meas_info[1];

    BOOLEAN foundAPM_opposite_side = is_APM_fluid_region(opposite_fluid_region);
    BOOLEAN isAPM = foundAPM_this_side  || foundAPM_opposite_side;

    if (foundAPM_this_side  && foundAPM_opposite_side) {
      msg_internal_error("Two APMs cannot be adjacent to each other");
    } else if (isAPM) {
      asINT32 n_params = surface_phys_desc->n_parameters -2;
      // Copying the parameters from apm side and then 
      // setting the porosity to 1 for the fluid side
      // should be set to constant if it is time varying??
      surface_phys_desc->_parameters[n_params + ISURFEL_FLUID] = 
	surface_phys_desc->_parameters[n_params + ISURFEL_APM];
      surface_phys_desc->_parameters[n_params + ISURFEL_FLUID].value = 1.0;
    } else {
      // This will go away once isurfels are used for physics other than APM
      // interface
      msg_internal_error("Atleast one side should be APM in this version");
    }


    // filling up meas_windows vector with the windows associated with the
    // fluid side apm isurfel. The APM side isurfel does not contribute to
    // any measurement window.
    //
    if (isAPM) {
      if (foundAPM_this_side ) {
	sINT32 n_meas_cell_ref_counter = 2;
	for (n_meas_cell_ref_counter = 2; n_meas_cell_ref_counter < opp_meas_info[0] + 2;
	     n_meas_cell_ref_counter=n_meas_cell_ref_counter + 2) {
	  sINT32 meas_window_index = opp_meas_info[n_meas_cell_ref_counter];
	  meas_windows.push_back(g_meas_windows[meas_window_index]);
	}
      }
      else {
	for(cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs)
	  meas_windows.push_back(g_meas_windows[meas_surfel_ref.meas_window_index]);
      }
    }
      

    SURFEL_PAIR_GROUP_TYPE surfel_pair_type;
    SURFEL_BASE_FSET surfel_base_fset;
    cNEIGHBOR_SP group_dest_sp(0);
    BOOLEAN is_any_surfel_fringe = (surfel->is_fringe() || opposite_surfel->is_fringe());
    if (is_any_surfel_fringe) {

      if (!surfel->m_surfel_group_dest_sp.is_valid()) {
        group_dest_sp = opposite_surfel->m_surfel_group_dest_sp;
      } else if  (!opposite_surfel->m_surfel_group_dest_sp.is_valid()) {
        group_dest_sp = surfel->m_surfel_group_dest_sp;
      } else {
        group_dest_sp = (surfel->m_surfel_group_dest_sp < opposite_surfel->m_surfel_group_dest_sp) ?
                         surfel->m_surfel_group_dest_sp : opposite_surfel->m_surfel_group_dest_sp;
      }
      surfel_base_fset = g_surfel_base_groups[FRINGE_SURFEL_BASE_GROUP_TYPE];
      surfel_pair_type = FRINGE_APM_SURFEL_PAIR_GROUP_TYPE;
    } else {
      surfel_base_fset = g_surfel_base_groups[INTERIOR_SURFEL_BASE_GROUP_TYPE];
      surfel_pair_type = INTERIOR_APM_SURFEL_PAIR_GROUP_TYPE;
    }
    SURFEL_PAIR_GROUP group = static_cast<SURFEL_PAIR_GROUP>(surfel_base_fset->create_group(surfel->scale(), group_dest_sp, APM_SURFEL_PAIR_GROUP_SUPERTYPE, m_realm));
    // Assign the send group to surfel pair group
    if (is_any_surfel_fringe) {
      sSURFEL_SEND_GROUP signature;
      signature.m_scale = surfel->scale();
      signature.m_dest_sp = group_dest_sp;
      group->m_send_group = g_surfel_send_fset.find_group(&signature);
    }
    if (isAPM) {
      if (foundAPM_this_side ) {
	sINT32 fluid_region_indices[2];
	fluid_region_indices[ISURFEL_APM]   = fluid_region;
	fluid_region_indices[ISURFEL_FLUID] = opposite_fluid_region;
	sSURFEL_PAIR *surfel_pair =
	  group->add_surfel_pair_to_group(surfel, opposite_surfel,
#if BUILD_D39_LATTICE
					  m_pgram_info.inv_total_pgram_volume_d39,
					  m_pgram_info.inv_total_pgram_volume_d19,
#endif
					  m_pgram_info.sum_pgram_volumes,
					  surfel_pair_type, sim_phys_type);
	surfel_pair->fill_interface_dynamics_data(fluid_region_indices, sim_phys_type, surface_phys_desc);
        
	// add meas cells to the opposite side fluid surfel
	sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptrs = opposite_surfel->create_meas_cell_ptrs(opposite_surfel->dynamics_data(), meas_windows.size());
	sINT32 n_meas_cell_ref_counter = 2;
	asINT32 iref = 0;
	for (n_meas_cell_ref_counter = 2; n_meas_cell_ref_counter < opp_meas_info[0]+2; n_meas_cell_ref_counter += 2) {
	  sINT32 meas_window_index = opp_meas_info[n_meas_cell_ref_counter];
	  MEAS_WINDOW window = g_meas_windows[meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = opp_meas_info[n_meas_cell_ref_counter+1];
	  surfel_meas_cell_ptrs[iref].set_window_index(meas_window_index);
	  surfel_meas_cell_ptrs[iref].set_index(meas_cell_index);
	  iref++; 
	}
      } else {
	sINT32 fluid_region_indices[2];
	fluid_region_indices[ISURFEL_APM]   = opposite_fluid_region;
	fluid_region_indices[ISURFEL_FLUID] = fluid_region;
	sSURFEL_PAIR *surfel_pair =
	  group->add_surfel_pair_to_group(opposite_surfel, surfel,
#if BUILD_D39_LATTICE
					  m_pgram_info.inv_total_pgram_volume_d39,
					  m_pgram_info.inv_total_pgram_volume_d19,
#endif
					  m_pgram_info.sum_pgram_volumes,
					  surfel_pair_type, sim_phys_type);
	sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptrs =
	  surfel->create_meas_cell_ptrs(surfel->dynamics_data(), meas_windows.size());
	asINT32 iref = 0;
	for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
         
	  MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
	  surfel_meas_cell_ptrs[iref].set_window_index(meas_surfel_ref.meas_window_index);
	  surfel_meas_cell_ptrs[iref].set_index(meas_cell_index);
	  iref++;
	}          
	surfel_pair->fill_interface_dynamics_data(fluid_region_indices, sim_phys_type, surface_phys_desc);
      }
    }
#ifdef ONLY_APM_ISURFELS_SUPPORTED
    else {
      // The code should never go in this branch
      BOOLEAN is_outside = (m_surfel_desc->s.surfel_flags & DGF_ISURFEL_OUTSIDE);
      // Measurement cells of each surfel are contiguous
      // Measurement cells of interior_surfel appear first
      if (is_outside) {
	sINT32 n_meas_cell_ref_counter = 1;
	for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
	  MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = opp_meas_info[n_meas_cell_ref_counter++];
	  group->add_meas_cell_index(meas_cell_index);
	}
	for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
	  MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = 
	    window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
	  group->add_meas_cell_index(meas_cell_index);
	}
      } else {
	for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
	  MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = 
	    window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
	  group->add_meas_cell_index(meas_cell_index);
	}
	sINT32 n_meas_cell_ref_counter = 1;
	for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
	  MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
	  STP_MEAS_CELL_INDEX meas_cell_index = opp_meas_info[n_meas_cell_ref_counter++];
	  group->add_meas_cell_index(meas_cell_index);
	}
      }

      if (is_outside) // opposite_surfel is the interior surfel
	group->add_quantum(opposite_surfel, surfel, sum_pgram_volumes);
      else // switch the interior/exterior surfels
        group->add_quantum(surfel, opposite_surfel, sum_pgram_volumes);
    }
#endif
    // Destroying the array with meas_info 
    delete opp_meas_info;
    // Removing the entry from the map
    surfel_to_meas_info_map.erase(opp_meas_cell_index_surfel_id_iter);
  }
}


//=======================================================================================================
// PARSE ISURFEL SURFEL DESCRIPTOR : SPECIFIC TO SIMSIZES BUILD
//=======================================================================================================
#else
  VOID cISURFEL_DESC_PARSER::post_process(sSHOB* shob) {}
#endif

//=======================================================================================================
// PARSE LRF SURFEL GHOST SURFEL DESCRIPTOR : SPECIFIC TO SIMENG BUILD
//=======================================================================================================
#if !BUILD_FOR_SIMSIZES

VOID cLRF_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() {
  if (is_sliding_mesh()) {
    m_phys_type = STP_MLRF_SURFEL_TYPE;
  } else {
    m_phys_type = STP_SLRF_SURFEL_TYPE;
  }
  m_surface_phys_desc = nullptr;
}

VOID cLRF_SURFEL_DESC_PARSER::post_process(sSHOB* shob){

  SURFEL surfel = static_cast<SURFEL> (shob);

  init_basic_surfel_props(surfel);
  
  add_ublk_interactions(surfel);

  add_surfel_to_send_groups(surfel, m_ghost_sps);

  surfel->m_face_index = m_surfel_desc->s.face_index;
  g_surfel_table[m_realm].add(surfel);
  g_ckpt_group.add(surfel, m_realm);

  compute_pgram_volumes(surfel);
  
  fill_even_odd_data(surfel);

  add_S2S_advect_data(surfel);

  fill_conduction_data(surfel);  //new
  
  read_surfel_ckpt_data(surfel);

  update_lrf_surfel_groups(surfel);

  maybe_init_surfel_stencil_info(surfel);
}

VOID cLRF_SURFEL_DESC_PARSER::update_lrf_surfel_groups(SURFEL surfel) {
  if (m_surfel_desc->s.surfel_id > m_surfel_desc->s.opposite_index) {

    // find the opposite (exterior or interior) surfel which should already have
    // been created since it has a lower ID
    SURFEL opposite_surfel = regular_surfel_from_id(m_surfel_desc->s.opposite_index, m_realm);

    if (NULL == opposite_surfel) {
      msg_internal_error("Cannot find opposite LRF surfel %d for surfel %d", 
                          m_surfel_desc->s.opposite_index, surfel->id());
    }

    asINT32 lrf_index = get_lrf_index(surfel, opposite_surfel);
    if (is_interior_surfel(surfel, opposite_surfel)) {
      surfel->set_interior_lrf(TRUE);
      opposite_surfel->set_interior_lrf(FALSE);
    } else {
      surfel->set_interior_lrf(FALSE);
      opposite_surfel->set_interior_lrf(TRUE);
    }

    if (lrf_index < 0) {
      msg_internal_error("Reference frame index for LRF surfel pair (ID %d, ID %d) is < 0",
                        surfel->id(), m_surfel_desc->s.opposite_index);
    }

#if !REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
    if (m_surfel_desc->meas_surfel_refs.size() > 0)
      msg_internal_error("LRF surfel %d is included in measurement windows", surfel->id());
#endif

    // skip creation of sSLRF group if a sliding mesh surfel
    if (sim.lrf_physics_descs[lrf_index].is_mlrf_on) {
#if DEBUG && !BUILD_GPU
      g_mlrf_surfel_vector.push_back(surfel);
      g_mlrf_surfel_vector.push_back(opposite_surfel);
      surfel->lb_data()->m_attribs.m_is_mlrf_surfel_in_ring = false;
      opposite_surfel->lb_data()->m_attribs.m_is_mlrf_surfel_in_ring = false;
#endif
    } else {
      BOOLEAN is_any_surfel_fringe = (surfel->is_fringe() || opposite_surfel->is_fringe());
      SURFEL_BASE_FSET surfel_base_fset;
      SURFEL_PAIR_GROUP_TYPE surfel_pair_group_type;
      cNEIGHBOR_SP group_dest_sp(0);
      if (is_any_surfel_fringe) {
        surfel_base_fset = g_surfel_base_groups[FRINGE_SURFEL_BASE_GROUP_TYPE];
        surfel_pair_group_type = FRINGE_SLRF_SURFEL_PAIR_GROUP_TYPE;
        if (!surfel->m_surfel_group_dest_sp.is_valid()) {
          group_dest_sp = opposite_surfel->m_surfel_group_dest_sp;
        } else if  (!opposite_surfel->m_surfel_group_dest_sp.is_valid()) {
          group_dest_sp = surfel->m_surfel_group_dest_sp;
        } else {
          group_dest_sp = (surfel->m_surfel_group_dest_sp < opposite_surfel->m_surfel_group_dest_sp) ?
	                   surfel->m_surfel_group_dest_sp : opposite_surfel->m_surfel_group_dest_sp;
        }
      } else {
        surfel_base_fset = g_surfel_base_groups[INTERIOR_SURFEL_BASE_GROUP_TYPE];
        surfel_pair_group_type =  INTERIOR_SLRF_SURFEL_PAIR_GROUP_TYPE;
      }

      SURFEL_PAIR_GROUP group = static_cast<SURFEL_PAIR_GROUP>(surfel_base_fset->create_group(m_surfel_desc->s.surfel_scale,
                                                                                              group_dest_sp, SLRF_SURFEL_PAIR_GROUP_SUPERTYPE, m_realm));

      // Assign the send group to surfel_pair_group
      if (is_any_surfel_fringe) {
        sSURFEL_SEND_GROUP signature;
        signature.m_scale = surfel->scale();
        signature.m_dest_sp = group_dest_sp;
        group->m_send_group = g_surfel_send_fset.find_group(&signature);
      }

      if (surfel->even_odd_mask() != opposite_surfel->even_odd_mask()) {
        msg_internal_error("Surfels %d %d in a pair do not have same even odd mask",
                           surfel->even_odd_mask(), opposite_surfel->even_odd_mask());
      }
      if (surfel->ref_frame_index() < 0) {// opposite_surfel is the interior surfel
        group->add_surfel_pair_to_group(opposite_surfel, surfel,
#if BUILD_D39_LATTICE
                                        m_pgram_info.inv_total_pgram_volume_d39,
                                        m_pgram_info.inv_total_pgram_volume_d19,
#endif
                                        m_pgram_info.sum_pgram_volumes,
                                        surfel_pair_group_type, STP_SLRF_SURFEL_TYPE);
      } else {
        group->add_surfel_pair_to_group(surfel, opposite_surfel,
#if BUILD_D39_LATTICE
                                        m_pgram_info.inv_total_pgram_volume_d39,
                                        m_pgram_info.inv_total_pgram_volume_d19,
#endif
                                        m_pgram_info.sum_pgram_volumes,
                                        surfel_pair_group_type, STP_SLRF_SURFEL_TYPE);
      }
    }
  } // if surfel_id > surfel opposite inde

}

//=======================================================================================================
// PARSE LRF SURFEL GHOST SURFEL DESCRIPTOR : SPECIFIC TO SIMSIMZES BUILD
//=======================================================================================================
#else
  VOID cLRF_SURFEL_DESC_PARSER::post_process(sSHOB* shob) {}
  VOID cLRF_SURFEL_DESC_PARSER::compute_phys_type_from_surface_desc() {}
#endif

//=======================================================================================================
// PARSE SAMPLING SURFEL DESC
//=======================================================================================================

VOID cSAMPLING_SURFEL_DESC_PARSER::read_descriptor(){
#if !BUILD_FOR_SIMSIZES
  init_seed_from_meas_data();
  if (!is_ghost() || sim.is_particle_model)
    convert_surfel_desc_area_to_local_units();
#endif
}

size_t cSAMPLING_SURFEL_DESC_PARSER::SIZE() const {
  return sizeof(sSAMPLING_SURFEL);
}

sSHOB* cSAMPLING_SURFEL_DESC_PARSER::allocate(){
#if !BUILD_FOR_SIMSIZES
  if (!is_ghost() || sim.is_particle_model)
    return xnew sSAMPLING_SURFEL;
  else
    return nullptr;
#else
  return nullptr;
#endif
  
}

#if !BUILD_FOR_SIMSIZES
VOID cSAMPLING_SURFEL_DESC_PARSER::init_basic_surfel_props(SAMPLING_SURFEL surfel){
  BOOLEAN is_ci = is_conduction_interface();

  asINT32 surfel_type = surfel_type_from_desc(is_S2S_destination(),
                                              even_odd(),
                                              (auINT32)(m_surfel_desc->s.surfel_flags),
                                              is_ghost(),
                                              has_ghosts(),
                                              is_fringe(),
                                              is_conduction_shell(),
                                              is_ci,
                                              is_radiation());

  surfel->m_surfel_attributes.m_surfel_type = surfel_type;
  surfel->m_surfel_attributes.m_dynamic_bits = 0;
  surfel->set_conduction_interface(is_conduction_interface());
  //The following m_is_sampling_surfel bit was set to 1 by the
  //constructor when the surfel was allocated but is hosed by the
  //value returned by surfel_type_from_desc leading to PR55835.  This
  //bug is also on the mainline but does not seem to affect sample
  //measurements.  It only seems relevant when the film solver is
  //active and edge connectivity is required.  The mainline can
  //tolerate this problem due to a difference in how edge connectivity
  //is computed (the PR has the full details as to why) but cond_sm cannot.
  surfel->m_surfel_attributes.m_is_sampling_surfel = 1; 
 
}

VOID cSAMPLING_SURFEL_DESC_PARSER::post_process(sSHOB* shob){

  SAMPLING_SURFEL sampling_surfel = static_cast<SAMPLING_SURFEL> (shob);

  init_basic_surfel_props(sampling_surfel);
  
  add_ublk_interactions(sampling_surfel);

  sampling_surfel->init(m_surfel_desc, false);

  update_groups(sampling_surfel);

  if (m_surfel_desc->s.num_surfel_weight_sets) {
    sampling_surfel->add_surfel_interactions(m_surfel_desc);
  }

  // Fill even odd data
  if (even_odd() != STP_PROCESS_ON_ALL_TIMES) {
    if (m_surfel_desc->s.clone_index < 0) {
      bool is_even = even_odd() == STP_PROCESS_ON_EVEN_TIMES;
      msg_internal_error("%s sampling surfel %d does not have the corresponding %s sampling surfel in lgi file.",
                         is_even ? "Even" : "Odd", sampling_surfel->id(),
                         is_even ? "odd"  : "even");
    } 
    sampling_surfel->set_clone_surfel_index(m_surfel_desc->s.clone_index);
  }
  
  fill_meas_windows(sampling_surfel);

  maybe_init_surfel_stencil_info(sampling_surfel);

  read_surfel_ckpt_data(sampling_surfel);
}

VOID cSAMPLING_SURFEL_DESC_PARSER::maybe_init_surfel_stencil_info(SAMPLING_SURFEL sampling_surfel) {
  if (sim.is_particle_model || sim.is_shell_conduction_model) {
    if (sampling_surfel != NULL ) {
      STENCIL_INFO stencil_info = sampling_surfel->stencil();
      stencil_info->n_vertices = m_surfel_desc->vertices.size();
      stencil_info->first_vertex_index = g_surfel_vertices_info.n_surfel_vertex_global_indices();
      /** Copy information from stencil_info into p_data till we 
       *  are allowing particles to use old stencil construction code */
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
      if (sim.is_particle_model) {
        sampling_surfel->p_data()->n_vertices = stencil_info->n_vertices;
        sampling_surfel->p_data()->first_vertex_index = stencil_info->first_vertex_index;
      }
#endif

      DO_STD_VECTOR(cDGF_SURFEL_VERTEX_INDEX, vi, m_surfel_desc->vertices) {
        g_surfel_vertices_info.set_is_surfel_vertex_used(vi.vertex_index);
        g_surfel_vertices_info.add_vertex_global_index(vi.vertex_index);
      }
    }
  }
}

VOID cSAMPLING_SURFEL_DESC_PARSER::read_surfel_ckpt_data(SAMPLING_SURFEL sampling_surfel) {
  if (sim.is_full_checkpoint_restore && sampling_surfel) {
    SHOB_ID ckpt_surfel_id;
    DGF_SHOB_CKPT_LEN ckpt_len;
    read_ckpt_shob_header(ckpt_surfel_id, ckpt_len);
    if (ckpt_surfel_id != m_surfel_desc->s.surfel_id)
      msg_internal_error("Inconsistency in sampling surfel order for LGI and checkpoint files (%d vs %d).",
			 m_surfel_desc->s.surfel_id, ckpt_surfel_id);
    sampling_surfel->read_ckpt();
  }
}

VOID cSAMPLING_SURFEL_DESC_PARSER::update_groups(SAMPLING_SURFEL sampling_surfel){
  SCALE scale = m_surfel_desc->s.surfel_scale;
  SURFEL_BASE_FSET surfel_base_fset =  g_surfel_base_groups[INTERIOR_SURFEL_BASE_GROUP_TYPE];
  if (m_ghost_sps.size() > 0) {
    surfel_base_fset =  g_surfel_base_groups[FRINGE_SURFEL_BASE_GROUP_TYPE];
    ccDOTIMES(i, m_ghost_sps.size()) {
      cNEIGHBOR_SP dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_ghost_sps[i].ghost_sp);
      SAMPLING_SURFEL_SEND_GROUP send_group = g_sampling_surfel_send_fset.create_group(scale, dest_sp);
      send_group->add_quantum(sampling_surfel);
      sampling_surfel->set_fringe(true);
    }
  }

  cNEIGHBOR_SP dest_sp(0);
  SURFEL_GROUP_BASE base_group = surfel_base_fset->create_group(scale, dest_sp, SAMPLING_SURFEL_GROUP_SUPERTYPE, m_realm);
  SAMPLING_SURFEL_GROUP sgroup = static_cast<SAMPLING_SURFEL_GROUP>(base_group);
  sgroup->add_shob_to_group(sampling_surfel);
  sampling_surfel->set_group(sgroup);
  // Add sampling surfel to surfel table
  //REALM_ASSUMPTION: Assumes m_realm is set for sampling surfel descriptors
  g_surfel_table[m_realm].add(sampling_surfel);
  g_ckpt_group.add(sampling_surfel, m_realm);

}

VOID cSAMPLING_SURFEL_DESC_PARSER::fill_meas_windows(SAMPLING_SURFEL sampling_surfel){
  static std::vector < MEAS_WINDOW > meas_windows;
  meas_windows.clear();
  for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs)
    meas_windows.push_back(g_meas_windows[meas_surfel_ref.meas_window_index]);

  sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptrs =
      sampling_surfel->create_meas_cell_ptrs(meas_windows.size());

  asINT32 iref = 0;
  for (cDGF_SURFEL_MEAS_SURFEL_REFERENCE& meas_surfel_ref: m_surfel_desc->meas_surfel_refs) {
    MEAS_WINDOW window = g_meas_windows[meas_surfel_ref.meas_window_index];
    // Convert global meas cell index into local meas cell index
    STP_MEAS_CELL_INDEX meas_cell_index = window->create_surfel_meas_cell(meas_surfel_ref.meas_surfel_index);
    surfel_meas_cell_ptrs[iref].set_window_index(meas_surfel_ref.meas_window_index);
    surfel_meas_cell_ptrs[iref].set_index(meas_cell_index);

    if (window->meas_window_type == LGI_SURFACE_WINDOW)
      msg_internal_error("Sampling surfel %d contains regular surfel meas window references.",
                         sampling_surfel->id());
    iref++;
  }
}
#else
VOID cSAMPLING_SURFEL_DESC_PARSER::post_process(sSHOB* shob){};
VOID cSAMPLING_SURFEL_DESC_PARSER::update_groups(SAMPLING_SURFEL sampling_surfel){};
#endif

//=======================================================================================================
// PARSE GHOST SAMPLING SURFEL DESC
//=======================================================================================================

VOID cGHOST_SAMPLING_SURFEL_DESC_PARSER::post_process(sSHOB* shob){

#if !BUILD_FOR_SIMSIZES
  if (!sim.is_particle_model)
    return;

  SAMPLING_SURFEL sampling_surfel = static_cast<SAMPLING_SURFEL> (shob);
  
  init_basic_surfel_props(sampling_surfel);

  add_ublk_interactions(sampling_surfel);

  sampling_surfel->init(m_surfel_desc, true);

  add_ghost_sampling_surfel_quantum(sampling_surfel);

  fill_meas_windows(sampling_surfel);

  // Fill even odd data
  //if (even_odd() != STP_PROCESS_ON_ALL_TIMES) {
  //  sampling_surfel->set_clone_surfel_index(m_surfel_desc->s.clone_index);
  //}

  maybe_init_surfel_stencil_info(sampling_surfel);
#endif
}

#if !BUILD_FOR_SIMSIZES
VOID cGHOST_SAMPLING_SURFEL_DESC_PARSER::add_ghost_sampling_surfel_quantum(SAMPLING_SURFEL sampling_surfel) {
  STP_SCALE scale = m_surfel_desc->s.surfel_scale;
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  SAMPLING_SURFEL_RECV_GROUP srgroup = g_sampling_surfel_recv_fset.create_group(scale, ssp);
  srgroup->add_quantum(sampling_surfel);
}
#endif

//=======================================================================================================
// SURFEL DESCRIPTOR PARSER FACTORY
//=======================================================================================================

cBASE_SURFEL_DESCS_PARSER* cSURFEL_DESC_PARSER_FACTORY::create_surfel_desc_parser(STP_REALM realm,
                                                                                  DGF_SURFEL_DESC surfel_desc,
                                                                                  STP_PROC home_sp,
                                                                                  RP_PROC rp,
                                                                                  RP_PROC backside_rp,
                                                                                  uINT16 ghost_flags,
                                                                                  std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps){
  cBASE_SURFEL_DESCS_PARSER* output_parser = nullptr;
  
  auINT32 flags              = surfel_desc->s.surfel_flags;
  BOOLEAN is_sampling_surfel = flags & DGF_SURFEL_SAMPLING;
  BOOLEAN is_reference_frame = flags & DGF_SURFEL_REFERENCE_FRAME;
  BOOLEAN is_mirror          = flags & DGF_SURFEL_IS_MIRROR;
  BOOLEAN is_ghost           = home_sp != my_proc_id;
  BOOLEAN is_isurfel         = flags & (DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE);
  STP_EVEN_ODD even_odd      = flags & DGF_SURFEL_EVEN_ODD_MASK;

#if !BUILD_FOR_SIMSIZES
  // JEH This is hopefully temporary. It would be easier if the discretizer clearly marked surfels that are coupled.
  // DFG-TODO: Modify the discretizer, so all conduction related surfels are not marked as isurfels

  // Mirror surfels do not participate in contact dynamics or cross-realm comm, so remove the flags and coupled index that
  // might have been assigned
  if (is_mirror) {
    surfel_desc->s.surfel_flags &= ~(DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY);
    surfel_desc->s.coupled_index = -1;
  }
  // For cond/cond and cond/flow pair of surfels, turn off isurfel flags since they are processed as wsurfels or contact_surfels.
  if (flags & (DGF_CONTACT_SURFEL_PRIMARY | DGF_CONTACT_SURFEL_SECONDARY)) { //contact surfel
    //For now all contact surfels are conduction. If contact extended to other scenarios, uncomment check fo conduction
    // if (flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) { //conduction 
      //Remove isurfel flags to be processed as a contact_surfel
      surfel_desc->s.surfel_flags &= ~(DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE);
      is_isurfel = FALSE;
#ifdef ENABLE_CONSISTENCY_CHECKS
      /* If tagged for averaged contact, there should be info available in the thermal averaged contacts */
      if ((surfel_desc->s.surfel_flags & DGF_CONTACT_AVERAGED) &&
          (!g_thermal_averaged_contacts.is_face_averaged((uINT16)surfel_desc->s.face_index))) {
        msg_warn("Surfel %d incorrectly tagged as averaged contact", surfel_desc->s.surfel_id);
        surfel_desc->s.surfel_flags &= ~DGF_CONTACT_AVERAGED;
      }
#endif
    // }
  } else if (surfel_desc->s.coupled_index >= 0) { //wsurfel
    surfel_desc->s.surfel_flags &= ~(DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE);
    is_isurfel = FALSE;
  } else if (flags & (DGF_SURFEL_VOLUME_CONDUCTION | DGF_SURFEL_SHELL_CONDUCTION)) { //standalone conduction surfel
    //Check for possible inconsistencies and throw warning if so
    //DFG-TODO: enable warnings once we release in cond_sm, commented for now to avoid triggering test failures
    if (surfel_desc->s.contact_physics_desc_index >=0) {
      // if (surfel_desc->s.opposite_index >= 0 && surfel_desc->s.surfel_id < surfel_desc->s.opposite_index) {
      //   msg_warn("Conduction surfels %d, %d missing primary/secondary flags",
      //             surfel_desc->s.surfel_id, surfel_desc->s.opposite_index);
      // } else {
      //   msg_warn("Conduction surfel %d missing primary/secondary flag", surfel_desc->s.surfel_id);
      // }
      //Assigns the correct flags that the discretizer should have assign
      if (flags & DGF_ISURFEL_INSIDE) {
        surfel_desc->s.surfel_flags |= DGF_CONTACT_SURFEL_PRIMARY;
      } else {
        surfel_desc->s.surfel_flags |= DGF_CONTACT_SURFEL_SECONDARY;
      }
    // } else if (surfel_desc->s.opposite_index >= 0 && surfel_desc->s.surfel_id < surfel_desc->s.opposite_index) {
    //   //This case should not occur if the discretizer process contact correctly. Process each surfel independently 
    //   //based on the face physics descriptor, but throw a warning to flag it.
    //   msg_warn("Conduction surfels %d, %d with no entry in contact table", 
    //             surfel_desc->s.surfel_id, surfel_desc->s.opposite_index);
    }
    //Should only occur when a conductor part is in contact with a true insulator (insulator part without layers).
    //Ensures that is not marked as a isurfel so it creates the correct parser type.
    surfel_desc->s.surfel_flags &= ~(DGF_ISURFEL_INSIDE | DGF_ISURFEL_OUTSIDE);
    is_isurfel = FALSE;
  }
#endif

  if (is_ghost) {

#if !BUILD_FOR_SIMSIZES
    if ( is_sampling_surfel && !sim.is_particle_model ) {
      msg_internal_error("Attempt to create ghost of sampling surfel %d", surfel_desc->s.surfel_id);
    }
#endif
    
    if ( is_sampling_surfel ) {
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::ghost_sampling_surfel_desc_parser;
    } else {
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::ghost_surfel_desc_parser;
    }
    
  }
  else { //Not ghost

    if (is_mirror){
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::mirror_surfel_desc_parser;
    }
    else if (is_reference_frame) {
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::lrf_surfel_desc_parser;
    }
    else if (is_isurfel) {
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::isurfel_desc_parser;
    }
    else if (is_sampling_surfel){
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::sampling_surfel_desc_parser;
    }
    else {
      //regular surfels, wsurfels, and contact_surfel share the same parser
      output_parser = &cSURFEL_DESC_PARSER_FACTORY::regular_surfel_desc_parser;
    } 
  } //if-else

  //Reset old values before reuse
  output_parser->reset();
  output_parser->set_surfel_desc(surfel_desc);
  output_parser->set_ghost_sps(ghost_sps);
  output_parser->set_home_sp(home_sp);
  output_parser->set_rp(rp);
  output_parser->set_backside_rp(backside_rp);
  output_parser->set_ghost_flags(ghost_flags);
  output_parser->set_realm(realm);
  
  return output_parser;
}

//Init static members
cREGULAR_SURFEL_DESC_PARSER  cSURFEL_DESC_PARSER_FACTORY::regular_surfel_desc_parser;
cMIRROR_SURFEL_DESC_PARSER cSURFEL_DESC_PARSER_FACTORY::mirror_surfel_desc_parser;
cLRF_SURFEL_DESC_PARSER  cSURFEL_DESC_PARSER_FACTORY::lrf_surfel_desc_parser;
cISURFEL_DESC_PARSER cSURFEL_DESC_PARSER_FACTORY::isurfel_desc_parser;
cGHOST_SURFEL_DESC_PARSER  cSURFEL_DESC_PARSER_FACTORY::ghost_surfel_desc_parser;
cSAMPLING_SURFEL_DESC_PARSER cSURFEL_DESC_PARSER_FACTORY::sampling_surfel_desc_parser;
cGHOST_SAMPLING_SURFEL_DESC_PARSER cSURFEL_DESC_PARSER_FACTORY::ghost_sampling_surfel_desc_parser;  

//======================================================================================================
// PARSE UBLK DESCRIPTOR
//======================================================================================================

#if !BUILD_FOR_SIMSIZES
VOID parse_ublk_desc(STP_REALM realm,
                     DGF_UBLK_BASE_DESC descriptor,
                     uINT8 ublk_decomp_flags,
                     STP_PROC home_sp, 
                     std::vector<cDGF_GHOST_INFO> &ghost_info,
                     LGI_STREAM istream){

  cBASE_UBLK_DESCS_PARSER* parser = cUBLK_DESC_PARSER_FACTORY::create_ublk_desc_parser(realm,descriptor,ublk_decomp_flags,
										       home_sp,ghost_info,istream);
  parser->parse();
}

//======================================================================================================
// PARSE UBLK UTILITIES
//======================================================================================================


static inline VOID set_sim_pm_dcache_allocation_flag(STP_PHYSTYPE_TYPE sim_phys_type) {
  switch (sim_phys_type) {  
  case STP_POROUS_VVFLUID_TYPE:
  case STP_PART_POROUS_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_POROUS_VVFLUID_TYPE:
  case STP_PART_CURVED_POROUS_VVFLUID_TYPE:
  case STP_CURVED_POROUS_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_CURVED_POROUS_VVFLUID_TYPE:
  case STP_CURVED_HX_POROUS_VVFLUID_TYPE:
  case STP_FAN_VVFLUID_TYPE:
  case STP_PART_FAN_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_FAN_VVFLUID_TYPE:
  case STP_TABLE_FAN_VVFLUID_TYPE:
  case STP_PART_TABLE_FAN_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_TABLE_FAN_VVFLUID_TYPE:
    {
      sim.enable_dcache_pm_storage = TRUE;
    }
  default:
    {
    }    
  }
}

static inline UBLK_GROUP_TYPE ublk_group_type_from_desc(auINT32 flags, 
                                                        uINT8 ublk_decomp_flags,
                                                        BOOLEAN is_mirror, 
                                                        BOOLEAN has_ghosts,
                                                        BOOLEAN last_coarse_is_fringe_or_ghost) {
  UBLK_GROUP_TYPE group_type = INVALID_UBLK_GROUP_TYPE;
  auINT32 vr_mask = flags & DGF_UBLK_VR_INTERFACE_MASK;
  BOOLEAN is_regular_surfel_interacting = (flags & DGF_UBLK_REGULAR_SURFEL_INTERACTING);
  BOOLEAN is_sampling_surfel_interacting = (flags & DGF_UBLK_SAMPLING_SURFEL_INTERACTING);
  BOOLEAN is_vr_fine = (vr_mask == DGF_UBLK_VR_FINE);
  BOOLEAN is_pde_advect = flags & DGF_UBLK_PDE_LIKE_ADVECTION;
  BOOLEAN is_neighbor_of_vrfine_ghost = ublk_decomp_flags &  DGF_UBLK_IS_NEIGHBOR_OF_VRFINE_GHOST;

  if (is_mirror) {
    group_type = MIRROR_UBLK_GROUP_TYPE;
  } else if (is_vr_fine) {
    if (is_regular_surfel_interacting || is_sampling_surfel_interacting) {
      if (has_ghosts || last_coarse_is_fringe_or_ghost || is_neighbor_of_vrfine_ghost) {
        group_type = VRFINE_FRINGE_NEARBLK_GROUP_TYPE;
      } else {
        group_type = VRFINE_NEARBLK_GROUP_TYPE;
      }
    } else {
      if (has_ghosts || last_coarse_is_fringe_or_ghost || is_neighbor_of_vrfine_ghost) {
        group_type = VRFINE_FRINGE_FARBLK_GROUP_TYPE;
      } else {
        group_type = VRFINE_FARBLK_GROUP_TYPE;
      }
    }
  } else {
    if (is_regular_surfel_interacting || is_sampling_surfel_interacting) {
      if (has_ghosts || is_neighbor_of_vrfine_ghost) {
        group_type = FRINGE_NEARBLK_GROUP_TYPE;
      } else {
        group_type = INTERIOR_NEARBLK_GROUP_TYPE;
      }
    } else if (is_pde_advect) {
      if (has_ghosts || is_neighbor_of_vrfine_ghost) {
        group_type = FRINGE_FARBLK_GROUP_TYPE;
      } else {
        group_type = INTERIOR_FARBLK2_GROUP_TYPE;
      }
    } else {
      if (has_ghosts || is_neighbor_of_vrfine_ghost) {
        group_type = FRINGE_FARBLK_GROUP_TYPE;
      } else {
        group_type = INTERIOR_FARBLK1_GROUP_TYPE;
      }
    }
  }
  return group_type;
}

asINT32 vr_fine_ublk_index_within_coarse_parent(UBLK vr_fine_ublk, UBLK coarse_ublk,
                                                bool report_error)
{
  // Sometimes, VR ublks can be decomposed such that the children are in one SP and the 
  // parent ublk is in another. In this case, coarse_ublk will be NULL. So we return -1
  // and do not set any parent SP.
  // In the future, we may want to change the decomposer so that both the parent and 
  // children ublks are on the same SP. This is useful when ghost bsurfels interact
  // with ghost ublks.
  if (coarse_ublk == NULL)
    return -1;
  
  STP_COORD *fine_loc = cast_as_regular_ptr(vr_fine_ublk->m_location);
  STP_COORD *coarse_loc = cast_as_regular_ptr(coarse_ublk->m_location);
  asINT32 coarse_voxel_size = sim_scale_to_voxel_size(coarse_ublk->scale());

  asINT32 delta[3];
  ccDOTIMES(i, 3) {
    asINT32 diff = fine_loc[i] - coarse_loc[i];
    if (diff == 0)
      delta[i] = 0;
    else if (diff == coarse_voxel_size)
      delta[i] = 1;
    else if (report_error) {
      msg_internal_error("Attempt to place VR fine ublk %d at location (%d, %d, %d)"
                         " within coarse interface ublk %d at location (%d, %d, %d)",
                         vr_fine_ublk->id(), fine_loc[0], fine_loc[1], fine_loc[2],
                         coarse_ublk->id(), coarse_loc[0], coarse_loc[1], coarse_loc[2]);
    } else {
      return -1;
    }
  }

  asINT32 coarse_voxel_num = voxel_to_num(delta[0],delta[1],delta[2]);
  asINT32 vr_fine_ublk_index = coarse_voxel_num;

  return vr_fine_ublk_index;
}
                                    
static VOID add_ublk_to_ublk_box(asINT32 part_index, STP_SCALE scale,
                                 STP_REF_FRAME_INDEX lrf_index, UBLK ublk) {
  STP_REALM realm = ublk->is_conduction_solid() ? STP_COND_REALM : STP_FLOW_REALM;
  // update the ublk_box
  asINT32 isolated_domain = -1;
  if (is_APM_fluid_region(part_index) || is_region_conduction_solid(part_index)) {
    isolated_domain = part_index;
  }
  UBLK_BOX ublk_box = g_ublk_box_fset.create_ublk_box(scale, realm, lrf_index,
                                                      isolated_domain, 0);
  ublk->set_ublk_box(ublk_box);
  // Do not include VR fine and ghost ublks, because we add an additional layer later
  ublk_box->update_box_dimensions (cast_as_regular_array(ublk->m_location));
}


static VOID fill_centroids_and_fluid_connect_mask(asINT32 scale, UBLK ublk,
                                                  asINT32 n_dims) {
  // Fill in centroids and connect masks
  STP_GEOM_VARIABLE half_voxel = 0.5f * scale_to_voxel_size(scale);
  DO_ACTIVE_VOXELS(v) { // Only visit voxels 0,2,4,6 in 2D
    STP_LOCATION voxel_location;
    ublk->get_voxel_location(v, voxel_location);
    ublk->centroids(v, 0) = (STP_GEOM_VARIABLE) (voxel_location[0] + half_voxel);
    ublk->centroids(v, 1) = (STP_GEOM_VARIABLE) (voxel_location[1] + half_voxel);
    if (n_dims == 3)
      ublk->centroids(v, 2) = (STP_GEOM_VARIABLE) (voxel_location[2] + half_voxel);

    CONNECT_MASK fluid_conn_voxel_mask = get_default_voxel_fluid_connect_mask();
    ublk->set_voxel_fluid_connect_mask(v, fluid_conn_voxel_mask);
    ublk->set_voxel_path_connect_mask(v, fluid_conn_voxel_mask);
  }
}

static inline asINT32 octree_index_to_3d_coord(auINT32 i)
{
  auINT32 result = 0;
  auINT32 mask   = 1;
  auINT32 shift  = 2;
  while (i > 0) {
    result |= i & mask;
    mask = mask << 1;   // move mask to next bit position
    i = i >> shift;     // move next bit into place to be masked
  }
  return result;
}

static inline asINT32 octree_index_to_2d_coord(asINT32 i)
{
  auINT32 result = 0;
  auINT32 mask   = 1;
  auINT32 shift  = 1;
  while (i > 0) {
    result |= i & mask;
    mask = mask << 1;   // move mask to next bit position
    i = i >> shift;     // move next bit into place to be masked
  }
  return result;
}

// Attach UBLK_UDS_DATA
static BOOLEAN mark_uds_as_active(DGF_GENERAL_VOXEL_DESC voxel_desc, asINT32 voxel, asINT32 nth_uds) 
{
  ccDOTIMES(r, voxel_desc->regions.size()) {
    cDGF_VOXEL_REGION  &region         = voxel_desc->regions[r];
    asINT32            fluid_region    = region.part_index;
    PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(fluid_region);
    
    BOOLEAN            is_fluid_region = FALSE;
    if (fluid_phys_desc)
      is_fluid_region = TRUE;

    if (is_fluid_region)
      return TRUE;
  }
  return FALSE;
}


static inline VOID filter_ublk_meas_cell_ref_windows (std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > &in_meas_cell_refs,
                                                      std::vector < MEAS_WINDOW_PTR > &out_meas_window_ptrs,
                                                      auINT32 dynamics_voxel_mask)
{
  out_meas_window_ptrs.clear();
  asINT32 last_meas_window_index = -1;
  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, in_meas_cell_refs) {
    if ((meas_cell_ref.voxel_mask & dynamics_voxel_mask)
        && (meas_cell_ref.meas_window_index != last_meas_window_index)) {
      out_meas_window_ptrs.push_back(g_meas_windows[meas_cell_ref.meas_window_index]);
      last_meas_window_index = meas_cell_ref.meas_window_index;
    }
  }
}

// filter_ublk_meas_cell_refs filters out meas cell refs where the meas voxel mask
// does not overlap the dynamics voxel mask.
static inline std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > 
&filter_ublk_meas_cell_refs (std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > &in_meas_cell_refs,
                             std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > &out_meas_cell_refs,
                             auINT32 dynamics_voxel_mask)
{
  BOOLEAN keep_all_meas_cell_refs = TRUE;

  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, in_meas_cell_refs) {
    if ((meas_cell_ref.voxel_mask & dynamics_voxel_mask) == 0) {
      keep_all_meas_cell_refs = FALSE;
      break;
    }
  }

  if (keep_all_meas_cell_refs)
    return in_meas_cell_refs;

  out_meas_cell_refs.clear();
  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, in_meas_cell_refs) {
    if (meas_cell_ref.voxel_mask & dynamics_voxel_mask)
      out_meas_cell_refs.push_back(meas_cell_ref);
  }
  return out_meas_cell_refs;
}

#endif //BUILD_FOR_SIMSIZES

static sUBLK::sUBLK_ATTRS ublk_type_from_flags(auINT32 flags) {
  sUBLK::sUBLK_ATTRS ublk_attrs;
  ublk_attrs.m_ublk_type = 0;
  ublk_attrs.m_dynamic_bits = 0;
  auINT32 vr_mask                       = flags & DGF_UBLK_VR_INTERFACE_MASK;
  BOOLEAN is_regular_surfel_interacting = (flags & DGF_UBLK_REGULAR_SURFEL_INTERACTING);
  BOOLEAN is_sampling_surfel_interacting = (flags & DGF_UBLK_SAMPLING_SURFEL_INTERACTING);
  BOOLEAN is_vr_fine                    = (vr_mask == DGF_UBLK_VR_FINE);
  BOOLEAN is_vr_coarse                  = ((vr_mask == DGF_UBLK_VR_COARSE_NO_FINE) ||
                                           (vr_mask == DGF_UBLK_VR_COARSE));
  BOOLEAN is_pde_advect                 = flags & DGF_UBLK_PDE_LIKE_ADVECTION;
  BOOLEAN has_mirror                    = (flags & DGF_UBLK_HAS_MIRROR_X) ||
    (flags & DGF_UBLK_HAS_MIRROR_Y) ||
    (flags & DGF_UBLK_HAS_MIRROR_Z);
  BOOLEAN is_bsurfel_interacting        = flags & DGF_UBLK_BSURFEL_INTERACTING;
  BOOLEAN is_conduction_solid           = flags & DGF_UBLK_CONDUCTION;

  ublk_attrs.m_has_two_copies_of_states = 1;
  if (is_regular_surfel_interacting || is_sampling_surfel_interacting) {
    ublk_attrs.m_does_interact_with_surface = 1;
  }
  if (is_regular_surfel_interacting)
    ublk_attrs.m_does_interact_with_regular_surface = 1; // Used in coalesce


#if ENABLE_FRACTIONAL_ADVECTION_SCHEME  // D19 5X solver
  // 5X_SOLVER. Force PDE advect now. TODO: Enable swap advection if the adv_fraction is 1.0 (MF, HS, etc.)
#else   // D39, 5G, D19 6X solver: adv_fraction = 1.0 by default, swap advection can be used.
  if (!is_pde_advect) {
    ublk_attrs.m_has_two_copies_of_states   = 0;
  }
#endif
  if (is_vr_fine) {
    ublk_attrs.m_is_vr_fine                 = 1;
  } else if (is_vr_coarse) {
    ublk_attrs.m_is_vr_coarse               = 1;
  }
  if (has_mirror) {
    ublk_attrs.m_has_mirror                 = 1;
  }

  ublk_attrs.m_are_states_advected = 0;
#if !BUILD_GPU
  if (is_conduction_solid) {
	  ublk_attrs.m_is_conduction_solid = 1;
  }
#endif
  return ublk_attrs;
}

STP_PHYSTYPE_TYPE  cBASE_UBLK_DESCS_PARSER::get_voxel_sim_type_from_map(asINT32 part_index){

  asINT32 cdi_phys_type = get_cdi_phys_type_from_map(part_index);
  if(cdi_phys_type != -1){
    return  voxel_sim_type_from_cdi_type(cdi_phys_type,is_near_surf());
  } else {
    return STP_INVALID_PHYSTYPE_TYPE;
  }
}

asINT32  cBASE_UBLK_DESCS_PARSER::get_cdi_phys_type_from_map(asINT32 part_index){
  assert(cBASE_UBLK_DESCS_PARSER::g_simsizes_info.part_index_to_phystype_map);
  auto item = cBASE_UBLK_DESCS_PARSER::g_simsizes_info.part_index_to_phystype_map->find(part_index);
  if (item !=  cBASE_UBLK_DESCS_PARSER::g_simsizes_info.part_index_to_phystype_map->end()) {
    return item->second;
  } else {
    return -1;
  }
}
  
// Returns a part index and fills in total_pfluid
asINT32 cBASE_UBLK_DESCS_PARSER::find_voxel_dominant_fluid_region(DGF_GENERAL_VOXEL_DESC voxel_desc, 
								  dFLOAT &total_pfluid)
{

  dFLOAT total_basic_fluid = 0;
  dFLOAT total_porous_media = 0;
  dFLOAT total_fan = 0;

  total_pfluid = 0;

  asINT32 thermal_porous_media_fluid_region = -1;
  asINT32 adiabatic_porous_media_fluid_region = -1;
  asINT32 fan_fluid_region;
  asINT32 basic_fluid_fluid_region;

  dFLOAT adiabatic_porous_media_largest_volume = 0.0;
  dFLOAT thermal_porous_media_largest_volume = 0.0;
  dFLOAT fan_largest_volume = 0.0;
  dFLOAT basic_fluid_largest_volume = 0.0;
  BOOLEAN some_solid_region = FALSE;
  
  // To be consistent with similar logic in the discretizer, when selecting a dominant
  // fluid region overlapping a voxel, in the event of a tie, the first region should
  // be the winner. Hence the presence of > operators below, rather than >=. This is
  // important if a voxel is overlapped by multiple curved porous media regions. The
  // discretizer needs to select a dominant fluid region in order to calculate the
  // voxel's reference surface normal. The simulator must pick the same dominant 
  // fluid region.

  ccDOTIMES(r, voxel_desc->regions.size()) {
    cDGF_VOXEL_REGION  &region         = voxel_desc->regions[r];
    asINT32            fluid_region    = region.part_index;
    STP_GEOM_VARIABLE  volume          = (STP_GEOM_VARIABLE) region.fraction; // fraction of full cube
    asINT32 cdi_phys_type;
    
#if !BUILD_FOR_SIMSIZES
    PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(fluid_region);
    if (fluid_phys_desc == NULL) { // NULL if a solid region 
      some_solid_region = TRUE;
      continue;
    }
    cdi_phys_type = fluid_phys_desc->phys_type_desc->cdi_physics_type;
    switch (cdi_phys_type) {
    case CONDUCTION_SOLID_PHYSICS_TYPE_CASE:
      some_solid_region = TRUE;
    }
#else
    cdi_phys_type = get_cdi_phys_type_from_map(fluid_region);
    if (cdi_phys_type == -1) {
      some_solid_region = TRUE;
      continue;
    }
  
#endif    
    switch (cdi_phys_type) {
    case THERMAL_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      if (volume > thermal_porous_media_largest_volume) {
	thermal_porous_media_fluid_region = fluid_region;
	thermal_porous_media_largest_volume = volume;
      }
      total_porous_media += volume;
      total_pfluid += volume;
      break;
    case ADIABATIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      if (volume > adiabatic_porous_media_largest_volume) {
	adiabatic_porous_media_fluid_region = fluid_region;
	adiabatic_porous_media_largest_volume = volume;
      }
      total_porous_media += volume;
      total_pfluid += volume;
      break;
    case FAN_CDI_FLUID_PHYSICS_TYPE_CASE :
    case TABLE_FAN_CDI_FLUID_PHYSICS_TYPE_CASE :
      if ((total_fan <= 0) || (volume > fan_largest_volume)) {
	fan_fluid_region = fluid_region;
	fan_largest_volume = volume;
      }
      total_fan += volume;
      total_pfluid += volume;
      break;

    default: // basic fluid
      if ((total_basic_fluid <= 0) || (volume > basic_fluid_largest_volume)) {
	basic_fluid_fluid_region = fluid_region;
	basic_fluid_largest_volume = volume;
      }
      total_basic_fluid += volume;
      total_pfluid += volume;
      break;
    }
  }     

  if (total_pfluid > 1.0
      || (!some_solid_region && (total_pfluid > (1.0 - 1e-6))))
    total_pfluid = 1.0;
  
  if (total_porous_media > 0.0) {
    if (total_porous_media > total_fan)
      return (thermal_porous_media_fluid_region >= 0)? thermal_porous_media_fluid_region:
	adiabatic_porous_media_fluid_region;
    else
      return fan_fluid_region;
  } else if (total_fan > 0.0) {
    return fan_fluid_region;
  } else if (total_basic_fluid > 0.0) {
    return basic_fluid_fluid_region;
  } else {
    return -1;
  }
}

//=======================================================================================================
// PARSE UBLK DESCRIPTOR BASE
// This abstract base class attepts to supply common methods to all derived UBLK descriptor parser types
// It is templated because the derived descriptors in LGI dont have a common virtual interface to access
// their contents. Therefore templating on the UBLK descriptors allows us to capture common code in this
// base class.
//=======================================================================================================

UBLK    cBASE_UBLK_DESCS_PARSER::g_last_vr_coarse_ublk = NULL;
asINT32 cBASE_UBLK_DESCS_PARSER::g_last_vr_fine_ublk_index = -1;
SIM_SIZES::sUBLK_STATIC_INFO cBASE_UBLK_DESCS_PARSER::g_simsizes_info;

//Used for initializing reference variable
std::vector<cDGF_GHOST_INFO> cBASE_UBLK_DESCS_PARSER::m_dummy_ghost_info_vec;

//General Helpers

VOID cBASE_UBLK_DESCS_PARSER::set_static_info_for_simsizes(const SIM_SIZES::sUBLK_STATIC_INFO& info) {
  assert(info.part_index_to_phystype_map);
  assert(info.sim_options);
  g_simsizes_info = info;
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_near_surf() const {
  return (flags() & DGF_UBLK_REGULAR_SURFEL_INTERACTING) ||
    (flags() & DGF_UBLK_SAMPLING_SURFEL_INTERACTING); 
}
  
BOOLEAN cBASE_UBLK_DESCS_PARSER::is_vr_fine() const{
  return (vr_mask() == DGF_UBLK_VR_FINE);
}
  
BOOLEAN cBASE_UBLK_DESCS_PARSER::is_vr_coarse() const {
  return (vr_mask() == DGF_UBLK_VR_COARSE_NO_FINE) ||
    (vr_mask() == DGF_UBLK_VR_COARSE);
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_vr_coarse_with_no_fine() const {
  return (vr_mask() == DGF_UBLK_VR_COARSE_NO_FINE);
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_ghost() const  {
  return m_home_sp != my_proc_id;
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::has_ghosts() const {
  return (m_ghost_info.size() > 0) && (!is_ghost());
}

// Ghosts of VR fine are a kind of hybrid; they are replicas of VR fine microblocks on another SP, but
// they are not copied (commed) directly; rather they are populated via explode from VR coarse ghosts.
// For some purposes they are treated as ghostblocks, but for most they are ordinary VR fine microblocks,  
BOOLEAN cBASE_UBLK_DESCS_PARSER::is_allocated_as_ghost() const {
  return (is_ghost() && !is_vr_fine());
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_ghost_vr_fine() const {
  return flags() & DGF_UBLK_IS_GHOST_VR_FINE;
}

  
BOOLEAN cBASE_UBLK_DESCS_PARSER::is_mirror() const {
  return (m_ublk_base_desc->b.ublk_type == DGF_UBLK_MIRROR);
}
  
auINT32 cBASE_UBLK_DESCS_PARSER::flags() const {
  return m_ublk_base_desc->b.ublk_flags;
}

//Helpful for debugging
auINT32 cBASE_UBLK_DESCS_PARSER::ublk_id() const {
  return m_ublk_base_desc->b.ublk_id;
}

auINT32 cBASE_UBLK_DESCS_PARSER::vr_mask() const  {
  return flags() & DGF_UBLK_VR_INTERFACE_MASK;
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_regular_surfel_interacting() const {
  return flags() & DGF_UBLK_REGULAR_SURFEL_INTERACTING;
}
  
BOOLEAN cBASE_UBLK_DESCS_PARSER::is_sampling_surfel_interacting() const {
  return flags() & DGF_UBLK_SAMPLING_SURFEL_INTERACTING;
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_pde_advect() const {
  return flags() & DGF_UBLK_PDE_LIKE_ADVECTION;
}

BOOLEAN cBASE_UBLK_DESCS_PARSER::is_ublk_split() const {
  return flags() & DGF_UBLK_IS_SPLIT;
}

//Helpers for read_descriptor

static VOID reset_attribute_data(sUBLK_DYNAMICS_ATTR_DATA& data) {
  data.is_adiabatic_apm = FALSE;
  data.is_low_mach = TRUE;
  data.is_basic_fluid = TRUE;
}

VOID  cBASE_UBLK_DESCS_PARSER::set_phys_types_and_attributes_for_ghost_ublk() {
  m_n_phys_types = 0;
  ccDOTIMES(voxel,ubFLOAT::N_VOXELS){
    m_sim_phys_types[voxel] = STP_INVALID_PHYSTYPE_TYPE;
    reset_attribute_data(m_attribute_data[voxel]);
  }
}

//Simple reset function that resets all the base UBLK descriptor parser
//properties, allowing for a clean reuse of an existing parser
//Derived classes can override this method to clean up new contents introduced
//in them
VOID cBASE_UBLK_DESCS_PARSER::reset() {
  m_n_phys_types = 0;
  ccDOTIMES(voxel,ubFLOAT::N_VOXELS){
    m_sim_phys_types[voxel] = STP_INVALID_PHYSTYPE_TYPE;
    reset_attribute_data(m_attribute_data[voxel]);
  }
  m_home_sp = -1;
  m_istream = nullptr;
  m_ublk_decomp_flags = 0;
  m_ublk_base_desc = nullptr;
  m_ghost_info = cBASE_UBLK_DESCS_PARSER::m_dummy_ghost_info_vec;
  m_voxel_mask.reset_all();
}

//Construct UBLK attributes from only the descriptor
sUBLK::sUBLK_ATTRS cBASE_UBLK_DESCS_PARSER::get_ublk_attrs() const {

  asINT32 uflags = this->flags();

  if (has_ghosts())
    uflags |= DGF_UBLK_PDE_LIKE_ADVECTION;
    
  sUBLK::sUBLK_ATTRS ublk_attrs = ublk_type_from_flags(uflags);

  // The attributes of a ghostblock are identical to the attributes of its original, except:
  // a. m_is_ghost is set
  // b. A ghostblock always has only one copy of states, unless it is a ghost of vrfine

  if(is_allocated_as_ghost()) {
    ublk_attrs.m_is_ghost = 1;
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
    ublk_attrs.m_has_two_copies_of_states = 1;
#else
    ublk_attrs.m_has_two_copies_of_states = 0;
#endif
  } else {  // Could be ghost vr fine or other non-ghost ublks
    if (is_ghost() && is_vr_fine() && is_near_surf()) { // Only near vr fine ublks are ghosted. Cannot use is_ghost_vr_fine() since DGF_UBLK_IS_GHOST_VR_FINE is not set in LGI
      ublk_attrs.m_is_ghost_vr_fine = 1; // ghost vr fine ublks are special: In some sense they are treated as non-ghost because
                                         // they have two sets of states. In some sense they are ghosts because they are commed.
    }
    ublk_attrs.m_is_ghost = 0;
  }

  if (is_mirror()) {
    ublk_attrs.m_is_mirror = 1;
    // Mirror ublks need a pointer to real ublk for particle modeling
    // This is stored in UBLK_MIRROR_DATA
    ublk_attrs.m_has_mirror = 1;
  }
    

  return ublk_attrs;
}

//Generic size method that should be called once the sim_phys_types and attribute_Data
//is computed
size_t cBASE_UBLK_DESCS_PARSER::SIZE() const {

  sUBLK::sUBLK_ATTRS ublk_attrs = get_ublk_attrs();

  size_t n_bytes = sUBLK::size(ublk_attrs, m_n_phys_types,
			       const_cast<STP_PHYSTYPE_TYPE*> (m_sim_phys_types),
			       const_cast<sUBLK_DYNAMICS_ATTR_DATA*> (m_attribute_data));
  return n_bytes;
  
}

size_t cBASE_UBLK_DESCS_PARSER::DYN_DATA_SIZE() const {

  sUBLK::sUBLK_ATTRS ublk_attrs = get_ublk_attrs();

  size_t n_bytes = sUBLK::size_of_dyn_data(ublk_attrs, m_n_phys_types,
                                           const_cast<STP_PHYSTYPE_TYPE*> (m_sim_phys_types),
                                           const_cast<sUBLK_DYNAMICS_ATTR_DATA*> (m_attribute_data));
  return n_bytes;
  
}

//Overrides the base class read_descriptor method
//This is a bare bones implementation most likely to
//be override by derived classes
VOID cBASE_UBLK_DESCS_PARSER::read_descriptor(){
  compute_phys_types_and_attributes();
}


//=======================================================================================================
// PARSE UBLK DESCRIPTOR BASE : SIMENG SPECIFIC BUILD
#if !BUILD_FOR_SIMSIZES
//=======================================================================================================

BOOLEAN cBASE_UBLK_DESCS_PARSER::should_read_ckpt_info() const {
  return sim.is_full_checkpoint_restore && (!is_ghost() || (is_ghost() && is_vr_fine()));
}

//Helpers for post_process
VOID cBASE_UBLK_DESCS_PARSER::update_total_fluid_voxel_count(UBLK ublk) {
  if (ublk && !ublk->is_ghost() && !ublk->is_vr_fine() && !ublk->is_mirror()) { 
    sim.total_fluid_like_voxels += ublk->fluid_like_voxel_mask.count();
  }
}

VOID cBASE_UBLK_DESCS_PARSER::set_vr_flags(UBLK ublk){
  if (is_vr_fine()) {
    ublk->set_vr_fine();
  } else if (is_vr_coarse()) {
    ublk->set_vr_coarse();
  }
}


//Used in real and simple ublk descriptor parsers
VOID cBASE_UBLK_DESCS_PARSER::mark_vr_fine_as_near_surf_if_parent_is_near_surf(){
  // If the VR coarse ublk is near surface, all its vr fine children should also be near surface
  if (is_vr_fine() && g_last_vr_coarse_ublk &&
      g_last_vr_coarse_ublk->is_near_surface()) {
    m_ublk_base_desc->b.ublk_flags |= DGF_UBLK_REGULAR_SURFEL_INTERACTING;
  }
}

sSHOB* cBASE_UBLK_DESCS_PARSER::allocate() {

  size_t n_bytes = SIZE();
  
  if (n_bytes == 0) {//Condition can be hit for solid ublks
    return nullptr;
  }
  
  UBLK ublk_ptr = sSHOB_ALLOCATOR<sUBLK>::malloc(n_bytes);
  memset(ublk_ptr, 0, n_bytes);
  return ublk_ptr;
}

VOID cBASE_UBLK_DESCS_PARSER::read_ckpt_info(UBLK ublk) {
  if ( should_read_ckpt_info() ) {
    SHOB_ID ckpt_ublk_id;
    DGF_SHOB_CKPT_LEN ckpt_len;
    read_ckpt_shob_header(ckpt_ublk_id, ckpt_len);
    if (ckpt_ublk_id != m_ublk_base_desc->b.ublk_id)
      msg_internal_error("Inconsistency in real ublk order for LGI and checkpoint files (lgi %d vs ckpt %d).",
                         m_ublk_base_desc->b.ublk_id, ckpt_ublk_id);
    if ( (ublk) && ublk->has_real_ckpt_data() )
    {
      ublk->read_ckpt();
    }
    else if (ckpt_len != 0) {
      // Ublk is solid, just consume the data
      char* temp_data = xnew char[ckpt_len];
      lgi_read(g_lgi_stream, temp_data, ckpt_len);
      delete[] temp_data;
    }
  }  
}


VOID cBASE_UBLK_DESCS_PARSER::do_smart_seed_ublk(UBLK ublk) {
  
  if (sim.do_smart_seed && !is_vr_fine() && !is_ghost()){

    cDGF_SMART_SEED_VOXEL_MASKS dgf_seed_mask;
    dgf_seed_mask.read(m_istream);

    if (ublk == nullptr) {return;};

    auINT32 needs_seed_data_voxel_mask = m_voxel_mask.get();
    if (dgf_seed_mask.needs_seed_data_voxel_mask != needs_seed_data_voxel_mask)
      msg_internal_error("For ublk %d, inconsistency identifying which voxels should be seeded (0x%02x vs 0x%02x).",
			 ublk->id(), dgf_seed_mask.needs_seed_data_voxel_mask, needs_seed_data_voxel_mask);

    UBLK_SMART_SEED_DATA smart_seed_data = ublk->smart_seed_data();
    auINT32 missing_seed_data_mask = ~dgf_seed_mask.has_seed_data_voxel_mask & needs_seed_data_voxel_mask;

    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      smart_seed_data->missing_seed_data(voxel) = is_voxel_in_mask(voxel, missing_seed_data_mask);
      smart_seed_data->needs_seed_data(voxel) = is_voxel_in_mask(voxel, needs_seed_data_voxel_mask);

      if ((dgf_seed_mask.has_seed_data_voxel_mask >> voxel) & 1) {
        uINT8 seed_var_spec_index;
        lgi_read(m_istream, seed_var_spec_index);
        smart_seed_data->w[voxel].seed_var_spec_index = seed_var_spec_index; 
        sINT32 controller_index = g_fluid_seed_var_specs[seed_var_spec_index].seed_controller;

        ccDOTIMES(var, sim.m_seed_control[controller_index].n_smart_seed_vars) {
          cDGF_SMART_SEED_VAR_VALUE subrec;
          subrec.read(m_istream);
          smart_seed_data->vars(sim.m_seed_control[controller_index].smart_seed_var_types[var], voxel, controller_index) = subrec.var_value;
        }

	ccDOTIMES(var, sim.m_seed_control[controller_index].n_smart_seed_uds_vars) {	    
	  cDGF_SMART_SEED_VAR_VALUE subrec;
	  subrec.read(m_istream);
	  asINT32 index_in_enum;  //in DGF_SEED_UDS_VAR_TYPES
	  asINT32 nth_uds;
	  find_uds_indices(sim.m_seed_control[controller_index].smart_seed_uds_var_indices[var], index_in_enum, nth_uds);
	  ublk->smart_seed_uds_data(nth_uds)->vars(index_in_enum, voxel) = subrec.var_value;	   
	}	
      }
    }

    // voxel_smart_seed_mask is augmented as seed_data extrapolation proceeds
    ublk->voxel_smart_seed_mask = VOXEL_MASK_8{dgf_seed_mask.has_seed_data_voxel_mask};

    if (missing_seed_data_mask != 0 && sim.extrapolate_seed)
      add_ublk_to_seed_extrapolate_list(ublk);
  }
}


// A ublk is ghosted on at least one other processor will have a valid dest_sp,
// and is assigned to a group associated with that SP. If it has mutiple ghosts
// it will be assigned to a group associated with the lowest-numbered remapped
// dest_sp encountered. If it has no ghosts it will be assigned it will be
// assigned either to group[DEST_SP_PROCESS_LAST] (if it belongs to a ghosted
// type), or to 0 (if it belongs to a type that is never ghosted.

extern sUBLK_TABLE g_ublk_table[STP_N_REALMS];

VOID cBASE_UBLK_DESCS_PARSER::update_non_ghost_ublk_common_properties(UBLK ublk, BOOLEAN is_mirror_of_solid_ublk){
  STP_SCALE scale = m_ublk_base_desc->b.voxel_scale;
  BOOLEAN last_coarse_is_fringe_or_ghost = FALSE;

  if(g_last_vr_coarse_ublk)  {
    last_coarse_is_fringe_or_ghost = g_last_vr_coarse_ublk->is_fringe() || g_last_vr_coarse_ublk->is_ghost();
  }

  // Promote fringe ublks to PDE advect

  // Use a temporary version of the flags so it can be modified without modifying the descriptor
  auINT32 uflags =  m_ublk_base_desc->b.ublk_flags;
  if (has_ghosts())
    uflags |= (DGF_UBLK_PDE_LIKE_ADVECTION);

  g_ublk_table[m_realm].add(ublk); // register in ublk table
  
  // ghosts of vr fine are not considered ghosts in nextgen simulator.
  // These ublks should not be checkpointed. We should restore them
  // from original ublks during full checkpoint restore.
  if (!is_ghost()) {
    g_ckpt_group.add(ublk, m_realm);
  }
 
  // mirror of solid ublks should be added to the table for ckpt, but they are not added to comm groups
  if (is_mirror_of_solid_ublk == TRUE) {
    return;
  }
  // For vr fine near ublks, we need to get the corresponding fine ublks group since they are inserted in the 
  // send group of the non-vr nearblk groups.
  UBLK_GROUP_TYPE non_vr_group_type = INVALID_UBLK_GROUP_TYPE;

  UBLK_GROUP_TYPE group_type = ublk_group_type_from_desc(uflags, m_ublk_decomp_flags, is_mirror(),
                                                         has_ghosts(), last_coarse_is_fringe_or_ghost);
  if (group_type == VRFINE_FRINGE_NEARBLK_GROUP_TYPE)
    non_vr_group_type = FRINGE_NEARBLK_GROUP_TYPE;
  if (group_type == MIRROR_UBLK_GROUP_TYPE)
    non_vr_group_type = MIRROR_UBLK_GROUP_TYPE;

  if (is_fringe(group_type)){
    ublk->set_fringe();
  }
  if (has_ghosts()) {
    
    ublk->set_has_ghosts();
    
    // If the ublk has multiple ghosts, it needs to be represented in multiple send groups, but in only one
    // ublk group. The ublk group should be the one with the lowest-numbered remapped destination SP, because
    // that is the one that will be processed first.

    // The proc_id field of the ghost_info record is equivalent to an MPI rank. Internally these ranks are remapped to two internal
    // representations, a source SP (ssp) for ghostblocks and a destination SP (dsp) for ublks that are ghosted on other SP's.
    // The two maps are built as the ublk table is read; when this function is called the ghost_info record should already have been processed
    // and the appropriate table updated as necessary.

    // Here we're trying to find the lowest dsp (not the lowest rank) ; that will determine the ublk group it is assigned to
    cNEIGHBOR_SP ublk_group_dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_ghost_info[0].proc_id );
    ccDO_FROM_BELOW(i,1, m_ghost_info.size()) {
      ublk_group_dest_sp = MIN(g_strand_mgr.m_neighbor_sp_map.get_nsp( m_ghost_info[i].proc_id ), ublk_group_dest_sp);
    }
    
    // Now create (or find) the ublk_groups and send groups.
    
    UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];
    UBLK_FSET non_vr_ublk_group_fset = nullptr;
    if (non_vr_group_type != INVALID_UBLK_GROUP_TYPE) {
      non_vr_ublk_group_fset = g_ublk_groups[non_vr_group_type];
    }
    ccDOTIMES(i,m_ghost_info.size()) {
      STP_PROC dest_sp_rank = m_ghost_info[i].proc_id;
      cNEIGHBOR_SP dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp( dest_sp_rank );
      UBLK_GROUP ugroup = ublk_group_fset->create_group(scale, dest_sp);
      UBLK_GROUP non_vr_ugroup = NULL;
      NEIGHBOR_MASK_INDEX nmi = (m_realm == STP_FLOW_REALM) ? m_ghost_info[i].nmi : g_comm_compression_info.no_mask_nmi();

      if (is_vr_fine() && is_near_surf() && non_vr_ublk_group_fset != nullptr)
        non_vr_ugroup = non_vr_ublk_group_fset->create_group(scale, dest_sp);
      //msg_print("Ublk %d is_fine %d dest_sp %d ublk_group_dest_sp %d group_type %d",ublk->id(),is_vr_fine,dest_sp,ublk_group_dest_sp, group_type);
      if (dest_sp == ublk_group_dest_sp) {
        ugroup->add_shob_to_group(ublk);
        ublk->set_group(ugroup);
      }
      // For ghosted vr fine near ublks, put them in the NEARBLK GROUP -> m_send_group instead of the VRFINE_NEARBLK_GROUP -> m_send_group
      // This is necessary because we only send m_send_group for nearblk groups after dynamics, but not for vrfine nearblk groups.
      if (non_vr_ugroup != NULL)  // non_vr_ugroup is valid only for vr fine near ublks
        non_vr_ugroup->m_send_group = g_ublk_send_fset.create_group(scale, dest_sp, is_near_surf());
      else if (!is_vr_fine())
        ugroup->m_send_group = g_ublk_send_fset.create_group(scale, dest_sp, is_near_surf());

      if(is_near_surf()) {
        NEARBLK_SEND_GROUP nearblk_send_group;
        if (!is_vr_fine())
          nearblk_send_group = static_cast<NEARBLK_SEND_GROUP>(ugroup->m_send_group);
        else  
          nearblk_send_group = static_cast<NEARBLK_SEND_GROUP>(non_vr_ugroup->m_send_group);
        nearblk_send_group->add_quantum(ublk, nmi);

      } else if (!is_vr_fine()) {  // Never comm vr fine far ublks so they are never in send groups.
        FARBLK_SEND_GROUP farblk_send_group = static_cast<FARBLK_SEND_GROUP>(ugroup->m_send_group);
        farblk_send_group->add_quantum(ublk, nmi);
      }
    }
  } else {
    UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];

    // A non-ghosted ublk of a ghosted type goes in the last group of that type and scale,
    // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted ublk of a non-ghosted goes in the only
    // group of that type and scale, with dest_sp = 0.

    cNEIGHBOR_SP dest_sp = cNEIGHBOR_SP( (g_ublk_type_has_ghosts[group_type]) ? DEST_SP_PROCESS_LAST : 0);
    UBLK_GROUP ugroup = ublk_group_fset->create_group(scale, dest_sp);
    ugroup->add_shob_to_group(ublk);
    ublk->set_group(ugroup);
  }  
}


VOID cBASE_UBLK_DESCS_PARSER::update_ghost_ublk_common_properties(UBLK gblk){

  // This should be called for ghosts of VR fine even if they are allocated with allocate_non_ghostblk.
  // Ghosts of VR fine are a kind of hybrid; they are replicas of VR fine microblocks on another SP, and
  // they are commed for full ckpt resume repeatability; However they also have two sets of states and 
  // are populated via explode from VR coarse ghosts.

  STP_SCALE scale = m_ublk_base_desc->b.voxel_scale;
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  NEIGHBOR_MASK_INDEX nmi = (m_realm == STP_FLOW_REALM) ? m_ghost_info[0].nmi : g_comm_compression_info.no_mask_nmi();
  
  if (is_near_surf()) {
    NEARBLK_RECV_GROUP nrgroup = g_nearblk_recv_fset.create_group(scale, ssp);
    nrgroup->add_quantum(gblk, nmi);
  } else {
    FARBLK_RECV_GROUP frgroup = g_farblk_recv_fset.create_group(scale, ssp);
    frgroup->add_quantum(gblk, nmi);
  }

  gblk->m_home_sp = m_home_sp;

  g_ublk_table[m_realm].add(gblk); // register in ublk table
}

VOID cBASE_UBLK_DESCS_PARSER::update_ghost_vr_fine_ublk_common_properties(UBLK ublk){
  STP_SCALE scale = m_ublk_base_desc->b.voxel_scale;
  BOOLEAN last_coarse_is_fringe_or_ghost = FALSE;
  
  if(g_last_vr_coarse_ublk)  {
    last_coarse_is_fringe_or_ghost = g_last_vr_coarse_ublk->is_fringe() || g_last_vr_coarse_ublk->is_ghost();
  }

  // Use a temporary version of the flags so it can be modified without modifying the descriptor
  auINT32 uflags =  m_ublk_base_desc->b.ublk_flags;
  UBLK_GROUP_TYPE group_type = ublk_group_type_from_desc(uflags, m_ublk_decomp_flags, is_mirror(),
                                                         has_ghosts(), last_coarse_is_fringe_or_ghost);

  g_ublk_table[m_realm].add(ublk); // register in ublk table
  // ghosts of vr fine are not checkpointed. We should restore them
  // from original ublks during full checkpoint restore.
  ublk->set_ghost_vr_fine();

  // ghost ublks are in the fringe group  
  ublk->set_fringe();

  UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];

  // A non-ghosted ublk of a ghosted type goes in the last group of that type and scale,
  // with dest_sp =  DEST_SP_PROCESS_LAST; a non-ghosted ublk of a non-ghosted goes in the only
  // group of that type and scale, with dest_sp = 0.
  cNEIGHBOR_SP dest_sp = cNEIGHBOR_SP( (g_ublk_type_has_ghosts[group_type]) ? DEST_SP_PROCESS_LAST : 0);
  UBLK_GROUP ugroup = ublk_group_fset->create_group(scale, dest_sp);
  ugroup->add_shob_to_group(ublk);
  ublk->set_group(ugroup);
  
  cNEIGHBOR_SP ssp = g_strand_mgr.m_neighbor_sp_map.get_nsp( m_home_sp );
  NEIGHBOR_MASK_INDEX nmi = (m_realm == STP_FLOW_REALM) ? m_ghost_info[0].nmi : g_comm_compression_info.no_mask_nmi();
  if (is_near_surf()) {
    NEARBLK_RECV_GROUP nrgroup = g_nearblk_recv_fset.create_group(scale, ssp);
    nrgroup->add_quantum(ublk, nmi);
  } else {
    msg_internal_error("VR fine farblks should not be ghosted.");
  }
  ublk->m_home_sp = m_home_sp;
}

VOID cBASE_UBLK_DESCS_PARSER::init_basic_ublk_properties(UBLK ublk, BOOLEAN is_mirror_of_solid_ublk){

  ublk->m_ublk_attributes = get_ublk_attrs();
  size_t dyn_data_size = DYN_DATA_SIZE();
  ublk->init(m_ublk_base_desc, dyn_data_size);
  
  // Solid ublks and ckpt only ublks do not have fluid like voxels
  if (m_n_phys_types == 0) {
    ublk->fluid_like_voxel_mask.reset_all();
  }

  //Add to standard ghost/non_ghost group
  if (is_ghost_vr_fine()) {
    update_ghost_vr_fine_ublk_common_properties(ublk);
  } else if (is_allocated_as_ghost()){
    update_ghost_ublk_common_properties(ublk);
  } else {
    update_non_ghost_ublk_common_properties(ublk, is_mirror_of_solid_ublk);
  }

  //Set split flag
  if (is_ublk_split()) {
    ublk->set_split();
  }

  //Set mirror flag
  if (is_mirror()) {
    ublk->set_is_mirror();
  }

  //Set VR flags
  if (is_vr_fine()) {
    ublk->set_vr_fine();
  } else if (is_vr_coarse()) {
    ublk->set_vr_coarse();
  }  
}

VOID cBASE_UBLK_DESCS_PARSER::add_vr_data_to_ublk(UBLK ublk,
						  STP_LATVEC_MASK fine_neighbor_mask)
{
  sVR_COARSE_INTERFACE_DATA *coarse_vr_data = ublk->vr_coarse_data();
  coarse_vr_data->init(find_vr_config(fine_neighbor_mask));

  if (is_vr_coarse_with_no_fine()) {
    ublk->set_vr_coarse_with_fine_children(0);
  } else {
    ublk->set_vr_coarse_with_fine_children(1);
  }
  g_last_vr_coarse_ublk = ublk;
  g_last_vr_fine_ublk_index = -1;
}

VOID cBASE_UBLK_DESCS_PARSER::add_vrfine_data_to_last_coarse_ublk(UBLK ublk,
								  VOXEL_MASK_8 fluid_voxel_mask) {
  asINT32 vr_fine_ublk_index =
    vr_fine_ublk_index_within_coarse_parent(ublk, g_last_vr_coarse_ublk);

  sVR_FINE_INTERFACE_DATA *fine_vr_data = ublk->vr_fine_data();
  fine_vr_data->init(g_last_vr_coarse_ublk);
  if (g_last_vr_fine_ublk_index == -1)
    g_first_vr_fine_ublks.insert(ublk->id());

  g_last_vr_fine_ublk_index = vr_fine_ublk_index;

  sVR_COARSE_INTERFACE_DATA *coarse_vr_data =  g_last_vr_coarse_ublk->vr_coarse_data();
  coarse_vr_data->add_vr_fine_ublk(ublk, vr_fine_ublk_index, sim.num_dims,
                                   g_last_vr_coarse_ublk, fluid_voxel_mask);
}

template <typename T>
VOID cBASE_UBLK_DESCS_PARSER::compute_real_ublk_centroids(UBLK ublk)
{
  T ublk_desc = static_cast<T>(m_ublk_base_desc);
  asINT32 scale = ublk_desc->b.voxel_scale;
  STP_GEOM_VARIABLE voxel_size = (STP_GEOM_VARIABLE) scale_to_voxel_size(scale);
  STP_GEOM_VARIABLE half_voxel = 0.5 * voxel_size;
  asINT32 n_dims = sim.num_dims;
  DO_ACTIVE_VOXELS(voxel) { // Only visit voxels 0,2,4,6 in 2D
    STP_LOCATION voxel_location;
    ublk->get_voxel_location(voxel, voxel_location);

    DGF_VOXEL_FLAGS voxel_flags = ublk_desc->voxel_flags[voxel].voxel_flags;
    if (voxel_flags & DGF_VOXEL_GENERAL) {
      DGF_GENERAL_VOXEL_DESC voxel_desc = &ublk_desc->general_voxels[voxel];
      // In the DGF file, voxel centroids are normalized from 0 to 1
      ublk->centroids(voxel, 0) = (STP_GEOM_VARIABLE) (voxel_location[0] + voxel_size * voxel_desc->v.centroid[0]);
      ublk->centroids(voxel, 1) = (STP_GEOM_VARIABLE) (voxel_location[1] + voxel_size * voxel_desc->v.centroid[1]);
      if (n_dims == 3)
        ublk->centroids(voxel, 2) = (STP_GEOM_VARIABLE) (voxel_location[2] + voxel_size * voxel_desc->v.centroid[2]);
    } else {

      ublk->centroids(voxel, 0) = (STP_GEOM_VARIABLE) (voxel_location[0] + half_voxel);
      ublk->centroids(voxel, 1) = (STP_GEOM_VARIABLE) (voxel_location[1] + half_voxel);
      if (n_dims == 3)
        ublk->centroids(voxel, 2) = (STP_GEOM_VARIABLE) (voxel_location[2] + half_voxel);
    }
  }
}
        
template <typename T>
VOID cBASE_UBLK_DESCS_PARSER::add_split_advect_info_to_ublks(UBLK ublk) {
  T ublk_desc = static_cast<T>(m_ublk_base_desc);
  asINT32 scale   = ublk_desc->b.voxel_scale;
  auINT32 vr_mask = ublk_desc->b.ublk_flags & DGF_UBLK_VR_INTERFACE_MASK;
  sSPLIT_ADVECT_INFO *split_advect_info = NULL;
  asINT32 total_advect_scales = 0;

  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    DGF_VOXEL_FLAGS voxel_flags = ublk_desc->voxel_flags[voxel].voxel_flags;
    if ((voxel_flags & DGF_VOXEL_SPLIT_ADVECT_DESTINATION) &&
        (voxel_flags & DGF_VOXEL_GENERAL)) {
      if (split_advect_info == NULL) 
        split_advect_info = new sSPLIT_ADVECT_INFO();

      DGF_GENERAL_VOXEL_DESC voxel_desc = &ublk_desc->general_voxels[voxel];

      // m_tot_advect_scale_factor will be translated to a cumulative count
      // later in assign_ublk_to_box.
      split_advect_info->m_tot_advect_scale_factors[voxel] = voxel_desc->advect_scale.a.num_advect_scale;
      total_advect_scales += voxel_desc->advect_scale.a.num_advect_scale;
      int prev_latvec = -1;
      split_advect_info->m_advect_one_instance_masks[voxel] = 0xFFFFFFFF;
      ccDOTIMES(l, N_LATTICE_VECTORS) {
        split_advect_info->m_src_ublk_instances[voxel][l] = 255;
      }
      ccDOTIMES(n, voxel_desc->advect_scale.a.num_advect_scale) {
        
        sADVECT_SCALE_FACTOR_INFO adv_factor_info;
        cDGF_SPLIT_ADVECT_SCALE_FACTOR factor = voxel_desc->advect_scale.factors[n];

        // Storing the split ublk instance in ublk instance. Should update it since
        // the ublk instance may change due to creation of ublk boxes. If two splt ublks
        // lie in different different boxes, they are not marked as split.
        adv_factor_info.src_ublk_instance   = factor.src_ublk_instance_index;
        adv_factor_info.latvec              = factor.latvec;
        adv_factor_info.advect_scale_factor = factor.advect_scale_factor;

        split_advect_info->m_advect_scale_factor_info.push_back(adv_factor_info);
        split_advect_info->m_src_ublk_instances[voxel][factor.latvec] =
	  factor.src_ublk_instance_index;
        if (prev_latvec == factor.latvec) {
          split_advect_info->m_advect_one_instance_masks[voxel] &= ~(1 << prev_latvec);
        }
        prev_latvec = factor.latvec;
      }
      ublk->set_advect_from_split_mask(voxel);
      ublk->set_advect_from_split_ublk();
    }
  }

  if (ublk->advect_from_split_ublk()) {

    split_advect_info->m_total_advect_scale_factors = total_advect_scales;

    ublk->m_split_info = new sSPLIT_INFO();
    ublk->m_split_info->m_split_advection_info = split_advect_info;

  }
} 

VOID cBASE_UBLK_DESCS_PARSER::compute_offset_location( STP_COORD orig_ublk_location[3],
						       asINT32 cube_offset, 
						       asINT32 ublk_size)
{
  if (sim.num_dims == 3) {
    m_ublk_base_desc->b.location[0] = orig_ublk_location[0] + 
      ublk_size * octree_index_to_3d_coord(cube_offset >> 2);
    m_ublk_base_desc->b.location[1] = orig_ublk_location[1] + 
      ublk_size * octree_index_to_3d_coord(cube_offset >> 1);
    m_ublk_base_desc->b.location[2] = orig_ublk_location[2] + 
      ublk_size * octree_index_to_3d_coord(cube_offset);

  } else {
    m_ublk_base_desc->b.location[2] = orig_ublk_location[2];
    m_ublk_base_desc->b.location[0] = orig_ublk_location[0] + 
      ublk_size * octree_index_to_2d_coord(cube_offset >> 1);
    m_ublk_base_desc->b.location[1] = orig_ublk_location[1] + 
      ublk_size * octree_index_to_2d_coord(cube_offset);

  }

}

VOID cBASE_UBLK_DESCS_PARSER::add_low_mach_info_to_attributes() {

  asINT32 lrf_index =  m_ublk_base_desc->b.lrf_index;
  LRF_PHYSICS_DESCRIPTOR lrf = nullptr;
  
  if (lrf_index >= 0) {
    lrf = &sim.lrf_physics_descs[lrf_index];    
  }

#if BUILD_D39_LATTICE
  ccDOTIMES(i, m_n_phys_types) {

    cassert( m_attribute_data[i].fluid_phys_desc != nullptr );
    
    sPHYSICS_VARIABLE &nu_over_t =
      static_cast<FLUID_PHYSICS_DESCRIPTOR>(m_attribute_data[i].fluid_phys_desc)->parameters()->nu_over_t;
    sPHYSICS_VARIABLE &viscous_damping_factor =
      static_cast<FLUID_PHYSICS_DESCRIPTOR>(m_attribute_data[i].fluid_phys_desc)->parameters()->viscous_damping_factor;
    
    if (nu_over_t.is_constant() && viscous_damping_factor.is_constant()){
      if (sim.use_hybrid_ts_hs_solver) {
	m_attribute_data[i].is_low_mach =!(lrf != nullptr && lrf->has_transonic_flow);
      }
      else {
	m_attribute_data[i].is_low_mach = FALSE;
      }
    }    
  }  
#endif
}


//=======================================================================================================
// PARSE UBLK DESCRIPTOR BASE : SIMSIZES SPECIFIC BUILD
#else
//=======================================================================================================
sSHOB* cBASE_UBLK_DESCS_PARSER::allocate() { return nullptr; }
VOID cBASE_UBLK_DESCS_PARSER::post_process(sSHOB* shob) {};
VOID cBASE_UBLK_DESCS_PARSER::add_low_mach_info_to_attributes() {};

#endif

//=======================================================================================================
// MIRROR UBLK DESC PARSER
//=======================================================================================================

VOID cMIRROR_UBLK_DESC_PARSER::set_ublk_desc(DGF_UBLK_BASE_DESC desc) {
  m_ublk_base_desc = desc;
#if !BUILD_FOR_SIMSIZES && BUILD_5G_LATTICE
  if (sim.is_large_pore)
    m_ublk_base_desc->b.ublk_flags |= (DGF_UBLK_PDE_LIKE_ADVECTION);
#endif
  m_ublk_desc = dynamic_cast<DGF_MIRROR_UBLK_DESC>(m_ublk_base_desc);
  if (!m_ublk_desc) {
    msg_internal_error("Incorrect descriptor type supplied to cMIRROR_UBLK_DESC_PARSER\n");
  }
}

VOID cMIRROR_UBLK_DESC_PARSER::compute_phys_types_and_attributes(){
  if (is_allocated_as_ghost()) {
    set_phys_types_and_attributes_for_ghost_ublk();
  } else {
    m_sim_phys_types[0] = STP_UBLK_MIRROR_TYPE;
    if (m_is_mirror_of_solid_ublk)
      m_n_phys_types = 0;
    else
      m_n_phys_types = 1;
  }
}

//=======================================================================================================
// MIRROR UBLK DESC PARSER : SIMENG SPECIFIC BUILD

#if !BUILD_FOR_SIMSIZES

//=======================================================================================================

VOID cMIRROR_UBLK_DESC_PARSER::verify_and_update_descriptor() {
  
  // The real ublk is in the same realm
  m_real_ublk = ublk_from_id(m_ublk_desc->u.real_ublk_id,m_realm);
  
  if (m_real_ublk == nullptr) {
    // This can happen on the ghost sp when the real ublk is a solid (ckpt only) ublk.
    if (is_ghost()) {
      m_is_mirror_of_solid_ublk = TRUE;
      return;
    } else {
      sSHOB_PARSER_EXCEPTION ex;
      sprintf(ex.m_except_msg, "No real ublk %d found for mirror ublk %d\n", 
              m_ublk_desc->u.real_ublk_id, m_ublk_desc->b.ublk_id);
      throw(ex);
    }
  }

  if (m_real_ublk->fluid_like_voxel_mask.none())
    m_is_mirror_of_solid_ublk = TRUE;
  else
    m_is_mirror_of_solid_ublk = FALSE;

  // The discretizer does not write the near surface flag for mirror ublks correctly.
  // Two copies of states are used for mirror ublks too which may not be necessary.
  if (m_real_ublk->is_near_surface()) {
    m_ublk_desc->b.ublk_flags |= DGF_UBLK_REGULAR_SURFEL_INTERACTING;
  }
  
  if (m_real_ublk->has_two_copies()) {
    m_ublk_desc->b.ublk_flags |= DGF_UBLK_PDE_LIKE_ADVECTION;
  }
}

VOID cMIRROR_UBLK_DESC_PARSER::read_descriptor(){
  verify_and_update_descriptor();  
  compute_phys_types_and_attributes();
}

VOID cMIRROR_UBLK_DESC_PARSER::add_mirror_comm_quantum(UBLK ublk){
#ifdef MIRROR_COMM
  if (is_vr_fine()) {
    ublk->is_ghost_or_mirror_of_vr_fine = TRUE;
  }
  if (m_ghost_info.size() > 0) {
    // add a comm group quantum for each ghost sp in the list
    ccDOTIMES (i, m_ghost_info.size()) {
      add_ublk_comm_quantum(ublk, is_vr_coarse(), is_near_surf(), m_ghost_info[i], 0x0);
    }
  }  
#endif
}

STP_STATE_INDEX cMIRROR_UBLK_DESC_PARSER::compute_state_index(UBLK ublk){
  // compute the latvec state index based on the mirror and real ublk
  // coordinates and the local scale

  // Full fluid mask so that it is added to the box
  ublk->fluid_like_voxel_mask.set_all();

  // TODO look at this code carefully for split mirror ublks
  UBLK_BOX real_ublk_box = m_real_ublk->get_ublk_box();
  ublk->set_ublk_box(real_ublk_box);
  real_ublk_box->update_box_dimensions(cast_as_regular_array(ublk->m_location));

  asINT32 shift_scale = scale_to_log2_voxel_size(coarsen_scale(m_ublk_desc->b.voxel_scale));

  STP_STATE_VEL vel[3] = {static_cast<STP_STATE_VEL>((m_real_ublk->location(0) - ublk->location(0)) >> shift_scale),
                          static_cast<STP_STATE_VEL>((m_real_ublk->location(1) - ublk->location(1)) >> shift_scale),
                          static_cast<STP_STATE_VEL>((m_real_ublk->location(2) - ublk->location(2)) >> shift_scale)};

  STP_STATE_INDEX state_index = convert_state_vel_to_latvec_index(vel);

  if (state_index == STP_INVALID_STATE_INDEX) {
    msg_internal_error("Invalid mirror ublk %d (%d, %d, %d) of real ublk %d (%d, %d, %d).",
                       m_ublk_desc->b.ublk_id, m_ublk_desc->b.location[0], m_ublk_desc->b.location[1], m_ublk_desc->b.location[2],
                       m_real_ublk->id(), m_real_ublk->location(0), m_real_ublk->location(1), m_real_ublk->location(2));
  }

  return state_index;

}

VOID cMIRROR_UBLK_DESC_PARSER::fill_mirror_data(UBLK ublk) {

  STP_STATE_INDEX state_index = compute_state_index(ublk);
 
  cassert(!m_is_mirror_of_solid_ublk);
 
  // If a mirror ublk is ghosted on another SP, this implies that the
  // associated ublk is also ghosted on that SP. Hence, add this (ublk,mirror)
  // pair to the ghosted mirror sub-group for a dynblk or vrblk mirror group.
  // Otherwise, to the non-ghosted mirror sub-group 
  if (is_vr_fine()) {
    //UBLK_MIRROR_GROUP group = g_vrblk_mirror_fset.create_group(mirror_desc->b.voxel_scale, state_index, ghost_info.size() > 0);
    m_real_ublk->fill_mirror_data(ublk, state_index);
    // for particle modeling, the mirror ublk needs to know the real ublk
    ublk->mirror_data()->m_mirror_ublk = m_real_ublk;
  }
  else {
    m_real_ublk->fill_mirror_data(ublk, state_index);
    // for particle modeling, the mirror ublk needs to know the real ublk
    ublk->mirror_data()->m_mirror_ublk = m_real_ublk;
  }
}

VOID cMIRROR_UBLK_DESC_PARSER::maybe_allow_copy_of_all_voxels(UBLK ublk) {
  if (ublk->is_conduction_solid()) {
    sUBLK_MIRROR_DATA *mirror_data = m_real_ublk->mirror_data();
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
    DO_ACTIVE_VOXELS_IN_MASK(voxel, m_real_ublk->fluid_like_voxel_mask) {
      mirror_config->voxels_to_mirror_mask.set(mirror_config->voxel_mirror_table[voxel]);
    }
  }
}

VOID cMIRROR_UBLK_DESC_PARSER::init_ghost_mirror_of_solid_ublk(UBLK ublk) {

  ublk->m_ublk_attributes = get_ublk_attrs();
  ublk->init(m_ublk_base_desc, 0);  // DWK2 TODO: Don't know if this is correct!
  ublk->fluid_like_voxel_mask.reset_all();
  g_ublk_table[m_realm].add(ublk);
  if (is_ublk_split()) { ublk->set_split(); }
  ublk->set_is_mirror();

  //Set VR flags
  if (is_vr_fine()) {
    ublk->set_vr_fine();
  } else if (is_vr_coarse()) {
    ublk->set_vr_coarse();
  }  
}

VOID cMIRROR_UBLK_DESC_PARSER::post_process(sSHOB* shob){

  cassert(shob);
  UBLK ublk = static_cast<UBLK> (shob);

  if (is_ghost() && m_is_mirror_of_solid_ublk == TRUE) {
    read_ckpt_info(ublk);
    //Solid mirror UBLKs have to be added to the UBLK table
    //else, the m_split_tagged_instance_in_lgi initialization
    //is incorrect - PR 49409    
    init_ghost_mirror_of_solid_ublk(ublk);
    return;
  }

  init_basic_ublk_properties(ublk, m_is_mirror_of_solid_ublk);

  if (m_is_mirror_of_solid_ublk == TRUE) 
    ublk->unset_real_ckpt_data(); 

  compute_real_ublk_centroids<DGF_MIRROR_UBLK_DESC>(ublk);

  add_split_advect_info_to_ublks<DGF_MIRROR_UBLK_DESC>(ublk);

  // mirror ublks are assigned ublk boxes later on to be the same as that of
  // the real counter part.
  // ckpt only ublks do NOT have mirror data
  if (!m_is_mirror_of_solid_ublk)
    fill_mirror_data(ublk);
  // mirror ublks are not written in mme ckpt file, thus the voxels should not be counted
  //update_total_fluid_voxel_count(m_real_ublk);

  // Some solvers (e.g., conduction solver) may require copy of all valid voxels rather than
  // just first layer
  maybe_allow_copy_of_all_voxels(ublk);

  read_ckpt_info(ublk);
}

//=======================================================================================================
// MIRROR UBLK DESC PARSER : SIMSIZES SPECIFIC BUILD

#else 
//=======================================================================================================

VOID cMIRROR_UBLK_DESC_PARSER::post_process(sSHOB*){}
VOID cMIRROR_UBLK_DESC_PARSER::read_descriptor() { cBASE_UBLK_DESCS_PARSER::read_descriptor(); }

#endif

//=======================================================================================================
// SIMPLE UBLK DESC PARSER
//=======================================================================================================

VOID cSIMPLE_UBLK_DESC_PARSER::set_ublk_desc(DGF_UBLK_BASE_DESC desc) {
  m_ublk_base_desc = desc;
#if !BUILD_FOR_SIMSIZES && BUILD_5G_LATTICE
  if (sim.is_large_pore)
    m_ublk_base_desc->b.ublk_flags |= (DGF_UBLK_PDE_LIKE_ADVECTION);
#endif
  m_ublk_desc = dynamic_cast<DGF_SIMPLE_UBLK_DESC>(m_ublk_base_desc);
  if (!m_ublk_desc) {
    msg_internal_error("Incorrect descriptor type supplied to cSIMPLE_UBLK_DESC_PARSER\n");
  }
}

//=======================================================================================================
// SIMPLE UBLK DESC PARSER : SIMENG SPECIFIC BUILD
#if !BUILD_FOR_SIMSIZES
//=======================================================================================================

VOID cSIMPLE_UBLK_DESC_PARSER::compute_phys_types_and_attributes(){
  if (is_allocated_as_ghost()){
    set_phys_types_and_attributes_for_ghost_ublk();
  }
  else {
    asINT32 part_index = m_ublk_desc->u.part_index;
    PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(part_index);
    BODY_FORCE_PHYSICS_DESCRIPTOR body_force_desc = body_force_desc_from_part_index(part_index);

    m_n_phys_types = 1;
    m_sim_phys_types[0] = voxel_sim_type_from_cdi_type(fluid_phys_desc, is_near_surf());
    m_attribute_data[0].fluid_phys_desc = fluid_phys_desc;
    m_attribute_data[0].body_force_desc = body_force_desc;
    add_low_mach_info_to_attributes();
  }
}

VOID cSIMPLE_UBLK_DESC_PARSER::debug_check(){
#if 0
  if(g_last_vr_coarse_ublk != NULL) {
    msg_print("psui: ID %d scale %d SP %d isg %d vrf %d vrc %d lc %d",
	      m_ublk_desc->b.ublk_id,   m_ublk_desc->b.voxel_scale,  m_home_sp,  is_ghost(), is_vr_fine(), is_vr_coarse(), g_last_vr_coarse_ublk->id());
  } else {
    msg_print("psui: ID %d scale %d SP %d isg %d vrf %d vrc %d",
              m_ublk_desc->b.ublk_id,   m_ublk_desc->b.voxel_scale,  m_home_sp,  is_ghost(), is_vr_fine(), is_vr_coarse());
  }

  if (is_regular_surfel_interacting() || is_sampling_surfel_interacting()) {
    msg_internal_error("Simple ublk %d has surfel interacting flag", m_ublk_desc->b.ublk_id);
  }
#endif
}

VOID cSIMPLE_UBLK_DESC_PARSER::read_descriptor(){
    
  debug_check();
  
  mark_vr_fine_as_near_surf_if_parent_is_near_surf();
  
  compute_phys_types_and_attributes();
}

VOID cSIMPLE_UBLK_DESC_PARSER::add_vr_data(UBLK ublk){
  if (is_vr_coarse()) {
    add_vr_data_to_ublk(ublk, m_ublk_desc->fine.fine_mask);
  }
  else if (is_vr_fine())
    {
      VOXEL_MASK_8 fluid_voxel_mask{0xff};
      add_vrfine_data_to_last_coarse_ublk(ublk, fluid_voxel_mask);
    }
}

VOID cSIMPLE_UBLK_DESC_PARSER::add_surf_data_if_vr_fine_and_near_surf(UBLK ublk){
  if (is_near_surf() && is_vr_fine()) {
    // VR fine ublks that are children of near surface VR coarse ublks are upgraded to near surface
    DO_ACTIVE_VOXELS(voxel){
      ublk->surf_geom_data()->pfluids[voxel] = 1.0;
      ublk->surf_geom_data()->inverse_pfluids[voxel] = 1.0;
      ublk->surf_lb_data()->v2s_dist[voxel] = 0.0;
    }
  }
}

VOID cSIMPLE_UBLK_DESC_PARSER::fill_dynamics_data(UBLK ublk){

  PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(m_ublk_desc->u.part_index);
  
  auto dynamics_data = ublk->dynamics_data();

#if !BUILD_FOR_SIMSIZES  
  if (!sim.enable_dcache_pm_storage) {
    set_sim_pm_dcache_allocation_flag(m_sim_phys_types[0]);
  }
#endif
  if (m_sim_phys_types[0] == STP_CONDUCTION_SOLID_TYPE){
    ublk->fill_dynamics_data(fluid_phys_desc, m_voxel_mask, m_sim_phys_types[0],
			   ublk, FALSE, m_ublk_desc,
			   dynamics_data, &m_attribute_data[0]);
  } else {
    ublk->fill_dynamics_data(fluid_phys_desc, m_voxel_mask, m_sim_phys_types[0],
			   ublk, FALSE, NULL,
			   dynamics_data, &m_attribute_data[0]);
  }
}

VOID cSIMPLE_UBLK_DESC_PARSER::fill_uds_data(UBLK ublk){    
  if (sim.is_scalar_model && sim.uds_solver_type == LB_UDS) {
    PHYSICS_DESCRIPTOR fluid_phys_desc = volume_physics_desc_from_part_index(m_ublk_desc->u.part_index);
    ublk->fill_uds_data(fluid_phys_desc, m_voxel_mask);
  }
}


VOID cSIMPLE_UBLK_DESC_PARSER::update_meas_info(UBLK ublk){

  // The meas cell refs must be sorted by meas window because the dynamics group lookup
  // routine relies on this order to find a group that has the same set of meas windows
  // as this ublk. The CP enforces this order.
  static std::vector<MEAS_WINDOW_PTR> meas_window_ptrs;
  meas_window_ptrs.clear();
  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, m_ublk_desc->meas_cell_refs) {
    meas_window_ptrs.push_back(g_meas_windows[meas_cell_ref.meas_window_index]);
  }

  asINT32 n_dims = sim.num_dims;
  asINT32 voxel_scale = m_ublk_desc->b.voxel_scale;
  asINT32 ublk_scale = coarsen_scale(voxel_scale);
  asINT32 two_to_n_dims = 1 << n_dims;
  asINT32 iref = 0;
  sMEAS_WINDOW_COMBINATION window_ids;
  
  sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs =
    ublk->create_meas_cell_ptrs(ublk->dynamics_data(), meas_window_ptrs.size());
  DO_STD_VECTOR(cDGF_UBLK_MEAS_CELL_REFERENCE, meas_cell_ref, m_ublk_desc->meas_cell_refs) {
    // The CP verifies that the meas cell indices are in range. For per voxel
    // meas windows, we've had trouble in the past with out-of-range meas cell
    //indices.

    MEAS_WINDOW window = g_meas_windows[meas_cell_ref.meas_window_index];

    // Particle trajectory measurement windows don't have meas cells but a ublk needs to know which trajectory windows it belongs too.
    // A bitmask is used to record this information in the ublks particle solver data block.
    if (window->m_is_particle_trajectory_window) {
      if (sim.is_particle_model) {
        uINT64 window_bitflag = 1 << ((TRAJECTORY_WINDOW)window)->trajectory_window_index();
        ublk->p_data()->s.window_mask |= window_bitflag;
      }
      ublk_meas_cell_ptrs[iref].set_window_index(meas_cell_ref.meas_window_index);
    } else {


    // In 2D, we only consider voxels on the min Z face. The discretizer enforces
    // this in the meas_cell_ref voxel mask.
    auINT32 meas_voxel_mask = meas_cell_ref.voxel_mask;

    // Convert global meas cell index into local meas cell index
    STP_MEAS_CELL_INDEX global_meas_cell_index;
    uINT8 axis;
    if (window->is_composite) {
      global_meas_cell_index = meas_cell_ref.meas_cell_index;
    } else if (window->is_development) {
      STP_MEAS_CELL_INDEX global_meas_cell_index_plus_axis;
      global_meas_cell_index_plus_axis = meas_cell_ref.meas_cell_index;
      lgi_meas_cell_index_dev_segment(global_meas_cell_index_plus_axis,
                                      global_meas_cell_index, axis);
    } else {
      asINT32 meas_cell_scale = window->meas_cell_scale;
      if (sim_is_scale_same_or_finer(meas_cell_scale, ublk_scale)) {
        if (window->is_per_voxel_for_scale(voxel_scale))
          global_meas_cell_index = meas_cell_ref.meas_cell_index
              + two_to_n_dims * m_cube_offset;
        else
          global_meas_cell_index = meas_cell_ref.meas_cell_index + m_cube_offset;
      } else {
        asINT32 log2_ublks_per_meas_cell = n_dims *
        sim_scale_diff(meas_cell_scale, ublk_scale);
        global_meas_cell_index = meas_cell_ref.meas_cell_index
            + (m_cube_offset >> log2_ublks_per_meas_cell);
      }
    }

    STP_MEAS_CELL_INDEX meas_cell_index = window->create_ublk_meas_cell(
        m_ublk_desc, global_meas_cell_index, voxel_scale, FALSE, // not split
        meas_voxel_mask);

    ublk_meas_cell_ptrs[iref].set_window_index(meas_cell_ref.meas_window_index);
    ublk_meas_cell_ptrs[iref].set_index(meas_cell_index);
    ublk_meas_cell_ptrs[iref].set_voxel_mask(meas_voxel_mask);
    if (window->is_development) {
      ublk_meas_cell_ptrs[iref].set_axis((asINT32) axis);
      ublk_meas_cell_ptrs[iref].set_part((asINT32) m_ublk_desc->u.part_index);
      if (ublk->is_ublk_moving()) {
        asINT32 n_segments = window->entity_n_segments[axis][m_ublk_desc->u.part_index];
        for (asINT32 j = 1; j < n_segments; j++) {
          window->create_ublk_meas_cell(m_ublk_desc, global_meas_cell_index + j,
                                        voxel_scale, FALSE, // not split
                                        meas_voxel_mask);
        }
      }
    }
    }

    if (sim.is_particle_model) {
      window_ids.insert(meas_cell_ref.meas_window_index);
    }
    iref++;
  }

  if (sim.is_particle_model) {
    ublk->p_data()->s.m_window_data.m_window_combination_index = g_meas_window_combinations.index_of(window_ids);
  }
  
}

VOID cSIMPLE_UBLK_DESC_PARSER::post_process(sSHOB* shob)
{
  UBLK ublk = static_cast<UBLK>(shob);
  init_basic_ublk_properties(ublk);

  m_voxel_mask =  sim.is_2d() ? VOXEL_MASK_8{0x55} : VOXEL_MASK_8{0xFF};

  add_ublk_to_ublk_box(m_ublk_desc->u.part_index,
		       m_ublk_desc->b.voxel_scale,
		       m_ublk_desc->b.lrf_index, ublk);
      
  fill_centroids_and_fluid_connect_mask(m_ublk_desc->b.voxel_scale, ublk, sim.num_dims);
    
  maybe_store_voxel_face_areas(ublk);


  if ( is_allocated_as_ghost() ) {
    ublk->fluid_like_voxel_mask = m_voxel_mask;
  }
  else {

    add_surf_data_if_vr_fine_and_near_surf(ublk);
      
    //vr fine does not participate in dynamics and measurements
    if ( !is_vr_fine() ) {

      ublk->fluid_like_voxel_mask = m_voxel_mask;
      
      fill_dynamics_data(ublk);

      fill_uds_data(ublk);  //LB_UDS
    
      update_total_fluid_voxel_count(ublk);

      ublk->set_distance_info(m_ublk_desc, FALSE, m_voxel_mask);

      update_meas_info(ublk);
      
      do_smart_seed_ublk(ublk);
    }
    ublk->init_p_data();

    read_ckpt_info(ublk);
  }
  
  add_vr_data(ublk);
  
  //DEBUG_IMPLICIT
  //if (ublk->id()==213)
  //  msg_print("Setting index to simple U %d ghost? %d home_sp: %d my proc %d", ublk->id(), is_ghost(), m_home_sp, my_proc_id);
  maybe_set_implicit_state_index(ublk);
}

//=======================================================================================================
// SIMPLE UBLK DESC PARSER : SIMSIZES SPECIFIC BUILD
#else
//=======================================================================================================

VOID cSIMPLE_UBLK_DESC_PARSER::read_descriptor(){ cBASE_UBLK_DESCS_PARSER::read_descriptor(); }

VOID cSIMPLE_UBLK_DESC_PARSER::post_process(sSHOB*){}

VOID cSIMPLE_UBLK_DESC_PARSER::compute_phys_types_and_attributes(){

  if (is_allocated_as_ghost()){
    set_phys_types_and_attributes_for_ghost_ublk();
  }
  else {
    
    asINT32 part_index = m_ublk_desc->u.part_index;

    m_n_phys_types = 1;
    m_sim_phys_types[0] = get_voxel_sim_type_from_map(part_index);

    //Its quite rare for ublks to have contributions from attribute data
    //for now we ignore it
    m_attribute_data[0].fluid_phys_desc = nullptr;
    m_attribute_data[0].body_force_desc = nullptr;
    
  }
}

#endif //BUILD_FOR_SIMSIZES

//=======================================================================================================
// SIMPLE UBLK DESC PARSER DECORATOR
// This decorator holds a cSIMPLE_UBLK_DESC_PARSER object. The read descriptor method of this class
// computes a cube_offset, and relevant ghosting information which it then passes along to the contained
// parser object.
//
// Parsing involves calling the parse method on the contained cSIMPLE_UBLK_DESC_PARSER object for different
// offsets and ghost information, and also capturing the size of the parsed UBLK for these combinations
//=======================================================================================================

std::vector<cDGF_GHOST_INFO> cSIMPLE_UBLK_DESC_PARSER_DECORATOR::m_cube_ghost_info;

VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::set_ublk_desc(DGF_UBLK_BASE_DESC desc) {
  m_ublk_base_desc = desc;
#if !BUILD_FOR_SIMSIZES && BUILD_5G_LATTICE
  if (sim.is_large_pore)
    m_ublk_base_desc->b.ublk_flags |= (DGF_UBLK_PDE_LIKE_ADVECTION);
#endif
  m_ublk_desc = dynamic_cast<DGF_SIMPLE_UBLK_DESC>(m_ublk_base_desc);
  if (!m_ublk_desc) {
    msg_internal_error("Incorrect descriptor type supplied to cSIMPLE_UBLK_DESC_PARSER_DECORATOR\n");
  }
}

//=======================================================================================================
// SIMPLE UBLK DESC PARSER DECORATOR
#if !BUILD_FOR_SIMSIZES
//=======================================================================================================
VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::reset_simple_parser(REALM realm,
                   DGF_SIMPLE_UBLK_DESC ublk_desc, 
							     uINT8 ublk_decomp_flags,
							     asINT32 cube_offset,
							     STP_PROC home_sp,
							     std::vector<cDGF_GHOST_INFO> &ghost_info,
							     LGI_STREAM istream){
  m_simple_parser.reset();
  m_simple_parser.set_realm(realm);
  m_simple_parser.set_ublk_desc(ublk_desc);
  m_simple_parser.set_ublk_decomp_flags(ublk_decomp_flags);
  m_simple_parser.set_cube_offset(cube_offset);
  m_simple_parser.set_home_sp(home_sp);
  m_simple_parser.set_ghost_info(ghost_info);
  m_simple_parser.set_lgi_stream(istream);
  
}

VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::read_zero_cube_factor(){
  asINT32 cube_offset = 0;
  reset_simple_parser(m_realm,m_ublk_desc,m_ublk_decomp_flags,cube_offset,m_home_sp,m_ghost_info,m_istream);
  //Parse and update size
  m_simple_parser.parse();
  m_total_simple_ublks_size = m_simple_parser.SIZE();
}

VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::read_nonzero_cube_factor_nsps(){
  // add 1 for the sentinel cube offset factor of -1
  ccDOTIMES64(n, m_n_ublks + 1) {
    m_cube_ghost_info.clear();
    STP_PROC ublk_home_sp; // The home_sp argument to the function is irrelevant for cube_factor > 0
    cDGF_GHOST_INFO ghost_info_rec;
    cDGF_CUBE_OFFSET c_offset;
    asINT32 n_ghosts = 0;

    c_offset.read(m_istream); 
    if (c_offset.cube_offset == -1 ) // done processing this descriptor
      break;
    if(m_realm == STP_FLOW_REALM) {
      cDGF_FLOW_UBLK_PROC ublk_proc;
      ublk_proc.read(m_istream);
      ublk_home_sp = ublk_proc.home_sp;
      n_ghosts = ublk_proc.num_ghost_sps;
    } else {
      cDGF_COND_UBLK_PROC ublk_proc;
      ublk_proc.read(m_istream);
      ublk_home_sp = ublk_proc.home_sp;
      n_ghosts = ublk_proc.num_ghost_sps;
    }
    m_cube_ghost_info.clear();
    if (ublk_home_sp == my_proc_id) {
      ccDOTIMES(i, n_ghosts) {
        ghost_info_rec.read(m_istream);
        g_strand_mgr.m_neighbor_sp_map.add_nsp(ghost_info_rec.proc_id);
        m_cube_ghost_info.push_back(ghost_info_rec);
      }
    } else {
      // Need to pass the NMI to the parser when the ublk is a ghostblk, in which case n_ghosts is 0/
      // This means the parser must interpret the ghost_info vector differently for ghostblks and
      // for ublks with ghosts
      ghost_info_rec.read(m_istream);
      g_strand_mgr.m_neighbor_sp_map.add_nsp(ghost_info_rec.proc_id);
      m_cube_ghost_info.push_back(ghost_info_rec);
    }

    m_ublk_desc->b.ublk_id = m_cube_id + c_offset.cube_offset;
    compute_offset_location(m_cube_location, c_offset.cube_offset, m_ublk_size);
    
    m_ublk_desc->b.implicit_solid_state_index = c_offset.implicit_solid_state_index;

    //msg_print("Reset ublk %d home_sp %d state_index %d", m_ublk_desc->b.ublk_id, ublk_home_sp, m_ublk_desc->b.implicit_solid_state_index);
    //SET PARSER PROPS AND PARSE
    reset_simple_parser(m_realm,m_ublk_desc,m_ublk_decomp_flags,c_offset.cube_offset,
			ublk_home_sp,m_cube_ghost_info,m_istream);
    //PARSE AND UPDATE SIZE
    m_simple_parser.parse();
    m_total_simple_ublks_size += m_simple_parser.SIZE();
  }
}

VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::read_nonzero_cube_factor_single_sp(){
  ccDOTIMES64(cube_offset, m_n_ublks) {
    m_ublk_desc->b.ublk_id = m_cube_id + cube_offset;
    compute_offset_location(m_cube_location, cube_offset, m_ublk_size);
    
    int voxels_per_ublk = sim.is_2d() ? N_VOXELS_8_2D : N_VOXELS_8;
    if (cube_offset != 0)
      m_ublk_desc->b.implicit_solid_state_index += voxels_per_ublk;

    //msg_print("Reset ublk %d state_index %d cube_offset %d n_ublks %d", m_ublk_desc->b.ublk_id, m_ublk_desc->b.implicit_solid_state_index, cube_offset, m_n_ublks);
    reset_simple_parser(m_realm,m_ublk_desc,m_ublk_decomp_flags,cube_offset,m_home_sp,m_ghost_info,m_istream);
    //PARSE AND UPDATE SIZE
    m_simple_parser.parse();
    m_total_simple_ublks_size += m_simple_parser.SIZE();
  }
}

//Populate m_cube_offset and can potentially modify
//ghost_info and home_sp depending on the value of
//cube factor
VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::read_descriptor(){
  m_ublk_desc = static_cast<DGF_SIMPLE_UBLK_DESC>(m_ublk_base_desc);
  asINT32 cube_factor = m_ublk_desc->b.cube_factor;
  m_total_simple_ublks_size = 0;
  
  if (cube_factor == 0) {
    read_zero_cube_factor();
  } else {
   
    m_ublk_size = sim_scale_to_cube_size(coarsen_scale(m_ublk_desc->b.voxel_scale));
    m_n_ublks = 1 << (sim.num_dims * cube_factor);
    m_cube_id = m_ublk_desc->b.ublk_id;
    m_cube_location[0] = m_ublk_desc->b.location[0];
    m_cube_location[1] = m_ublk_desc->b.location[1];
    m_cube_location[2] = m_ublk_desc->b.location[2];

    if (total_sps > 1) {
      read_nonzero_cube_factor_nsps();
    }
    else {
      read_nonzero_cube_factor_single_sp();
    }
  } // cube_factor > 0
}

VOID cSIMPLE_UBLK_DESC_PARSER::maybe_store_voxel_face_areas(UBLK ublk){
  if (ublk->is_conduction_solid() && is_near_surf()) {
    NEARBLK_CONDUCTION_DATA conduction_data = ublk->surf_conduction_data();

    DO_ACTIVE_VOXELS(voxel) {
      ccDOTIMES(f, 6) {
        conduction_data->face_areas[f][voxel] = 1.0;
      }
    } 
  }
}

VOID cSIMPLE_UBLK_DESC_PARSER::maybe_set_implicit_state_index(UBLK ublk){
  if (ublk->is_conduction_solid() && sim.use_implicit_solid_solver) {
    int count_voxel = 0; 
    DO_ACTIVE_VOXELS(voxel) {
      //int voxels_per_ublk = sim.is_2d() ? N_VOXELS_8_2D : N_VOXELS_8;
      ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] = m_ublk_desc->b.implicit_solid_state_index + count_voxel; 
      //DEBUG_IMPLICIT
      //if(ublk->id()==99 || ublk->id()==100)
      //  msg_print("cube factor %d U %d V %d base %d index %d",m_ublk_desc->b.cube_factor, ublk->id(), voxel, m_ublk_desc->b.implicit_solid_state_index, ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel]);
      count_voxel++;
    }
   
  }
}

//=======================================================================================================
// SIMPLE UBLK DESC PARSER DECORATOR : SIMSIZES BUILD
#else
//=======================================================================================================
VOID cSIMPLE_UBLK_DESC_PARSER_DECORATOR::read_descriptor() {};
#endif

//=======================================================================================================
// REAL UBLK DESC PARSER
//=======================================================================================================

VOID cREAL_UBLK_DESC_PARSER::set_ublk_desc(DGF_UBLK_BASE_DESC desc) {
  m_ublk_base_desc = desc;
#if !BUILD_FOR_SIMSIZES && BUILD_5G_LATTICE
  if (sim.is_large_pore)
    m_ublk_base_desc->b.ublk_flags |= (DGF_UBLK_PDE_LIKE_ADVECTION);
#endif
  m_ublk_desc = dynamic_cast<DGF_REAL_UBLK_DESC>(m_ublk_base_desc);
  if (!m_ublk_desc) {
    msg_internal_error("Incorrect descriptor type supplied to cREAL_UBLK_DESC_PARSER\n");
  }
}

VOID cREAL_UBLK_DESC_PARSER::reset() {

  cBASE_UBLK_DESCS_PARSER::reset();
  ccDOTIMES(voxel,ubFLOAT::N_VOXELS){
    m_voxel_masks[voxel].reset_all();
    m_voxel_pfluids[voxel] = 0;
    m_total_pfluids[voxel] = 0;
    m_fluid_region_indices[voxel] = 0;
    m_fluid_conn_voxel_masks[voxel].reset_all();
    m_path_conn_voxel_masks[voxel].reset_all();
  }
  m_voxel_face_polygons.clear();
}

/** The SIZE method is overriden here to accomodate solid
 *  ublks and solid_ublks which are not handled correctly
 *  in the base class SIZE method.
 *
 *  PR45714: Solid VR ublks must be fully allocated
 *  in order to accomodate the necessary VR data block.
 *  It would be nice to limit this size somehow, since
 *  we don't need ALL the data, just the geometry data.
 */
size_t cREAL_UBLK_DESC_PARSER::SIZE() const {

  size_t n_bytes;
  
  if ( is_solid_non_vr_ublk() ) {
    n_bytes = sizeof(sUBLK);
  }
  else {
    n_bytes = cBASE_UBLK_DESCS_PARSER::SIZE();
  }

  return n_bytes;
}

VOID cREAL_UBLK_DESC_PARSER::compute_phys_types_and_attributes(){

  m_n_phys_types = 1;

  // Fill in m_voxel_masks, m_phys_descs, and m_total_pfluids for all distinct physics descriptors found
  // across the 8 voxels of the ublk. m_n_phys_types tracks the number of distinct physics descriptors
  // that are found.
  // The first fluid region should correspond to basic fluid
  asINT32 BASIC_FLUID_INDEX = 0;
  m_voxel_masks[BASIC_FLUID_INDEX].reset_all();
  m_total_pfluids[BASIC_FLUID_INDEX] = 0;

#if DEBUG
STP_UBLK_ID ublk_id = m_ublk_desc->b.ublk_id; 
#endif


#if !BUILD_FOR_SIMSIZES
  asINT32 voxel_incr = 4 - sim.num_dims;
#else
  asINT32 voxel_incr = 4 - g_simsizes_info.sim_options->num_dimensions;
#endif
  for(asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; voxel += voxel_incr) {
    m_voxel_pfluids[voxel] = 0;
    asINT32 fluid_region_index;
    dFLOAT  pfluid;

    DGF_VOXEL_FLAGS voxel_flags = m_ublk_desc->voxel_flags[voxel].voxel_flags;
    if (voxel_flags & DGF_VOXEL_SIMPLE) {
      DGF_SIMPLE_VOXEL_DESC simple_voxel_desc = &m_ublk_desc->simple_voxels[voxel];
      fluid_region_index  = simple_voxel_desc->v.part_index;
      pfluid              = 1.0;
      // full mask
      m_fluid_conn_voxel_masks[voxel] = get_default_voxel_fluid_connect_mask();
      m_path_conn_voxel_masks[voxel] = get_default_voxel_fluid_connect_mask();
    } else if (voxel_flags & DGF_VOXEL_GENERAL) {
      DGF_GENERAL_VOXEL_DESC general_voxel_desc = &m_ublk_desc->general_voxels[voxel];
      fluid_region_index = find_voxel_dominant_fluid_region(general_voxel_desc, pfluid);
      m_fluid_conn_voxel_masks[voxel] = CONNECT_MASK(general_voxel_desc->v.fluid_connectivity_mask );
      m_path_conn_voxel_masks[voxel] = CONNECT_MASK( general_voxel_desc->v.path_connectivity_mask );

#if !BUILD_FOR_SIMSIZES
      if (sim.is_particle_model && (voxel_flags & DGF_VOXEL_SPLIT_NEIGHBOR)) {
        ccDOTIMES(f, 6) {
          if (general_voxel_desc->neighbors.v.num_neighbors[f] > 0) {
            //msg_print("U %d Voxel %d has %d split neighbors on Face %d", m_ublk_desc->b.ublk_id, voxel, general_voxel_desc->neighbors.v.num_neighbors[f], f);
            ccDOTIMES(n, general_voxel_desc->neighbors.v.num_neighbors[f]) {
              //msg_print("instance %d n_polygons %d",
              //          general_voxel_desc->neighbors.neighbors[f][n].v.neighbor_index,
              //          general_voxel_desc->neighbors.neighbors[f][n].v.num_fpolygons);
              uINT8 neighbor_instance = general_voxel_desc->neighbors.neighbors[f][n].v.neighbor_index;
              uINT8 num_loops = general_voxel_desc->neighbors.neighbors[f][n].v.num_fpolygons;
              if (num_loops > 0) {
                m_voxel_face_polygons.push_back(g_face_polygon_info.add_face_polygons(num_loops, voxel, f, neighbor_instance));
                ccDOTIMES(p, num_loops) {
                  cDGF_VOXEL_FPOLYGON_DESC fp_desc = general_voxel_desc->neighbors.neighbors[f][n].fpolygons[p];
                  m_voxel_face_polygons.back().add_convex_loop(fp_desc);
                }
              } else if (num_loops == 0) {
                m_voxel_face_polygons.push_back(sVOXEL_FACE_POLYGON(num_loops, voxel, f, neighbor_instance));
              }
            }
          }
        }
      }
#endif
    } else {
      fluid_region_index = -1;
    }

    if (fluid_region_index >= 0) { // otherwise, pfluid == 0

      m_voxel_pfluids[voxel] = pfluid;
      
#if !BUILD_FOR_SIMSIZES
      PHYSICS_DESCRIPTOR phys_desc = volume_physics_desc_from_part_index(fluid_region_index);
      BODY_FORCE_PHYSICS_DESCRIPTOR bf_desc = body_force_desc_from_part_index(fluid_region_index);     
      STP_PHYSTYPE_TYPE  sim_phys_type_loc = voxel_sim_type_from_cdi_type(phys_desc, is_near_surf());
#else
      PHYSICS_DESCRIPTOR phys_desc = nullptr;
      BODY_FORCE_PHYSICS_DESCRIPTOR bf_desc = nullptr;     
      STP_PHYSTYPE_TYPE  sim_phys_type_loc = get_voxel_sim_type_from_map(fluid_region_index);     
      
#endif
      BOOLEAN            is_basic_fluid  = (sim_phys_type_loc == STP_VVFLUID_TYPE) ||
                                           (sim_phys_type_loc == STP_NEAR_SURFACE_VVFLUID_TYPE);
      asINT32 j;
      if (is_basic_fluid) {
        if (m_voxel_masks[BASIC_FLUID_INDEX].none()) {
          j = BASIC_FLUID_INDEX;
        } else {
          for (j=0; j<m_n_phys_types; j++) {
            if (m_phys_descs[j] == phys_desc)
              break;
          }
        }
      } else {
        for (j=1; j<m_n_phys_types; j++) {
          if (m_phys_descs[j] == phys_desc)
            break;
        }
      }

      if (j >= m_n_phys_types) {
        m_voxel_masks[j].reset_all();
        m_total_pfluids[j] = 0;
        m_n_phys_types++;
      }

      m_sim_phys_types[j] = sim_phys_type_loc;
      m_voxel_masks[j].set(voxel);
      m_total_pfluids[j] += pfluid;
      m_phys_descs[j]     = phys_desc;
      m_body_force_descs[j]   = bf_desc;
      m_fluid_region_indices[j]  = fluid_region_index;
    }
  }
  // no voxels have basic fluid, so shift everything down
  // Access to nonexistent basic fluid block controlled
  // by basic_fluid_voxel_mask
  if (!m_voxel_masks[BASIC_FLUID_INDEX].any()) {
    asINT32 j;
    for (j=0; j<m_n_phys_types-1; j++) {
      m_sim_phys_types[j] = m_sim_phys_types[j+1];
      m_voxel_masks[j]    = m_voxel_masks[j+1];
      m_total_pfluids[j]  = m_total_pfluids[j+1]; // Why is there a += here?
      m_phys_descs[j]     = m_phys_descs[j+1];
      m_fluid_region_indices[j]  = m_fluid_region_indices[j+1];
      m_body_force_descs[j]  = m_body_force_descs[j+1];
    }
    m_n_phys_types--;
  }
  else { // check for multiple basic fluid types
    // The simulator only allows a single basic fluid type in a ublk, so we pick
    // the largest and let it claim all basic fluid voxels.
    asINT32 dominant_basic_fluid_index = -1;
    dFLOAT  dominant_basic_fluid_pfluid = -1;
    VOXEL_MASK_8 basic_fluid_voxel_mask{0};
    asINT32 n_basic_fluids = 0;

    ccDOTIMES(k, m_n_phys_types) {
      PHYSICS_DESCRIPTOR fluid_phys_desc = m_phys_descs[k];
      BOOLEAN            is_basic_fluid  = (m_sim_phys_types[k] == STP_VVFLUID_TYPE) ||
                                           (m_sim_phys_types[k] == STP_NEAR_SURFACE_VVFLUID_TYPE);

      if (is_basic_fluid) {
        if (m_total_pfluids[k] > dominant_basic_fluid_pfluid) {
          dominant_basic_fluid_pfluid = m_total_pfluids[k];
          dominant_basic_fluid_index = k;
        }

        basic_fluid_voxel_mask |= m_voxel_masks[k];
        m_voxel_masks[k].reset_all();
        n_basic_fluids++;
      }
    }

    m_voxel_masks[dominant_basic_fluid_index] = basic_fluid_voxel_mask;

    // place this information at the front
    if (dominant_basic_fluid_index != BASIC_FLUID_INDEX) {
      std::swap (m_sim_phys_types[BASIC_FLUID_INDEX], m_sim_phys_types[dominant_basic_fluid_index]);
      std::swap (m_voxel_masks[BASIC_FLUID_INDEX], m_voxel_masks[dominant_basic_fluid_index]);
      std::swap (m_total_pfluids[BASIC_FLUID_INDEX], m_total_pfluids[dominant_basic_fluid_index]);
      std::swap (m_phys_descs[BASIC_FLUID_INDEX], m_phys_descs[dominant_basic_fluid_index]);
      std::swap (m_fluid_region_indices[BASIC_FLUID_INDEX], m_fluid_region_indices[dominant_basic_fluid_index]);
      std::swap (m_body_force_descs[BASIC_FLUID_INDEX], m_body_force_descs[dominant_basic_fluid_index]);
    }

    // shift empty physics descriptors down
    for(int k = 0; k < m_n_phys_types; k++) {
      if (m_voxel_masks[k].none()) {
        for (int j=k; j<m_n_phys_types-1; j++) {
          m_sim_phys_types[j] = m_sim_phys_types[j+1];
          m_voxel_masks[j]    = m_voxel_masks[j+1];
          m_total_pfluids[j]  = m_total_pfluids[j+1];
          m_phys_descs[j]     = m_phys_descs[j+1];
          m_fluid_region_indices[j]  = m_fluid_region_indices[j+1];
          m_body_force_descs[j]  = m_body_force_descs[j+1];
        }
        k--; // shift k down in case another empty descriptor is next
        m_n_phys_types--;
      }
    }
  } // end merge basic fluid regions

  // fill in region attribute information
  for (int j=0; j < m_n_phys_types; j++) {
    m_attribute_data[j].fluid_phys_desc = m_phys_descs[j];
    m_attribute_data[j].body_force_desc = m_body_force_descs[j];
  }

  add_low_mach_info_to_attributes();
}

// solid ublks which are used for full ckpt only
BOOLEAN cREAL_UBLK_DESC_PARSER::is_solid_non_vr_ublk() const{
  return (m_n_phys_types == 0) && (!(is_vr_fine() || is_vr_coarse())); 
}

/* Per Peter Robert's comments (03-28-2025)
   The empty-voxel mask strictly means empty, in which case the voxel record in the ublk would be trivial (voxel flags == 0).
   Its possible for a voxel to have solid (insulator) content so its not empty in that sense. Such solid voxels can exist, e.g. to support locus of moving-boundary surfels
   Also ublks (voxels) can exist solely to support surfels on the voxel boundary; so pfluid=0 but ublk empty mask is non-empty
   So fluid_like_voxel_mask = ~empty_mask is not a correct assumption.
   Therefore, one cannot identify if a UBLK is solid in the conventional sense (all voxel with pfluid = 0) based on empty voxel mask
*/
BOOLEAN cREAL_UBLK_DESC_PARSER::is_solid_ublk() const{
  return (m_n_phys_types == 0); 
}

//=======================================================================================================
// REAL UBLK DESC PARSER : SIMENG SPECIFIC BUILD
#if !BUILD_FOR_SIMSIZES
//=======================================================================================================

VOID cREAL_UBLK_DESC_PARSER::read_descriptor(){

  m_ublk_desc = static_cast<DGF_REAL_UBLK_DESC>(m_ublk_base_desc);
  
  mark_vr_fine_as_near_surf_if_parent_is_near_surf();
  
  compute_phys_types_and_attributes();

}

/* @fcn check_lgi_consistency_of_vrfine_and_vrcoarse_ublk
 * This function was introduced as a result of PR 54643. Sometimes the disc can
 * generate a solid VR-fine UBLK underneath a coarse voxel that has tiny pfluid
 * The VR-fine block then gets a full fluid_like_voxel_mask when it gets associated
 * with the coarse voxel in fcn "add_vrfine_data_to_last_coarse_ublk"
 * This solid vr-fine UBLK gets added to g_solid_vr_fine_group even though its
 * fluid_like_voxel_mask gets overriden to be full. The GPU initialization code
 * never sees this UBLK since it's not solid based on masks, and not part of any conventional
 * VR-fine group
 */
static VOID
check_lgi_consistency_of_vrfine_and_vrcoarse_ublk(UBLK vr_fine_ublk,
                                                  UBLK vr_coarse_ublk,
                                                  bool vr_fine_ublk_is_solid) {
  asINT32 coarse_voxel =
    vr_fine_ublk_index_within_coarse_parent(vr_fine_ublk, vr_coarse_ublk);

    //Make it a hard error for GPU builds because initialization code fails to account
    //for this LGI inconsistency and adding a hack is nasty to get past the asserts
#if BUILD_GPU
    auto msg_fcn = msg_error;
#else
    auto msg_fcn = msg_warn;
#endif  
  if (vr_coarse_ublk->fluid_like_voxel_mask.test(coarse_voxel)) {
    if (vr_fine_ublk_is_solid) {
      msg_fcn("VR fine UBLK %d is solid, but containing coarse voxel %d of coarse UBLK %d, "
              "has non-zero pfluid associated with it.",
              vr_fine_ublk->id(), coarse_voxel,
              vr_coarse_ublk->id());
    }
  } else {
    if (!vr_fine_ublk_is_solid) {
      msg_warn("VR fine UBLK %d is not solid, but containing coarse voxel %d of coarse UBLK %d, "
               "has zero pfluid associated with it.",
               vr_fine_ublk->id(), coarse_voxel,
               vr_coarse_ublk->id());      
    }
  }
}

VOID cREAL_UBLK_DESC_PARSER::add_vr_data(UBLK ublk){
  
  if (ublk) {
    if (is_vr_coarse()) {
      add_vr_data_to_ublk(ublk, m_ublk_desc->fine.fine_mask);
    } else if (is_vr_fine()) {
      if (g_last_vr_coarse_ublk) {
        
	VOXEL_MASK_8 non_empty_voxel_mask = VOXEL_MASK_8(~(m_ublk_desc->b.empty_voxel_mask));
        
        //At this stage the vrfine UBLK has no fluid mask set, so can't test with ublk->is_solid()
        check_lgi_consistency_of_vrfine_and_vrcoarse_ublk(ublk, g_last_vr_coarse_ublk, is_solid_ublk());
        
        
	add_vrfine_data_to_last_coarse_ublk(ublk, non_empty_voxel_mask);
      }
    }//if-else-is_vr_coarse
    
  }
  else { //ublk == null
    if (is_vr_coarse()) {
      g_last_vr_coarse_ublk = NULL;
      g_last_vr_fine_ublk_index = -1;
    }
  } //if-else 
}

VOID cREAL_UBLK_DESC_PARSER::update_dynamics_data_and_meas_info(UBLK ublk){

  ccDOTIMES(j, m_n_phys_types) {
    VOXEL_MASK_8 voxel_mask = m_voxel_masks[j];
    if (voxel_mask.any()) {  
      ublk->fluid_like_voxel_mask |= voxel_mask;
    }
  }
  
  asINT32 voxel_scale = m_ublk_desc->b.voxel_scale;
  auto dynamics_data = ublk->dynamics_data();
  // Create up to 8 dynamics quantums
  ccDOTIMES(j, m_n_phys_types) {
    VOXEL_MASK_8 voxel_mask = m_voxel_masks[j];
    m_voxel_mask |= voxel_mask;
    if (voxel_mask.any()) {
      PHYSICS_DESCRIPTOR fluid_phys_desc = m_phys_descs[j];
      asINT32 fluid_region_index = m_fluid_region_indices[j];
#ifdef APM_DEBUG
      //STP_PHYSTYPE_TYPE  sim_phys_type   = voxel_sim_type_from_cdi_type(fluid_phys_desc, is_near_surf);
      if (sim_phys_type == STP_ACOUSTIC_POROUS_FLUID_TYPE_ID ) {
         printf(" Adding REAL ublk %d to sim_phys_type %d \n",m_ublk_desc->b.ublk_id, sim_phys_type);
      }
#endif

      // Extract a vector of meas window ptrs from the set of meas cell refs. Filter out the meas cell
      // refs that don't apply to this subset of voxels (because the dynamics voxel mask does not 
      // overlap the meas cell ref voxel mask). From the remaining meas cell refs, filter out duplicate
      // references to meas windows. Allocate the vector as static so that we don't have to repeatedly 
      // hit the heap.
      static std::vector < MEAS_WINDOW_PTR > tmp_meas_window_ptrs;
      filter_ublk_meas_cell_ref_windows(m_ublk_desc->meas_cell_refs, tmp_meas_window_ptrs, voxel_mask.get());

#if !BUILD_FOR_SIMSIZES  
      if (!sim.enable_dcache_pm_storage) {
	set_sim_pm_dcache_allocation_flag(m_sim_phys_types[j]);
      }
#endif
      
      auto last_dynamics_data = dynamics_data;
      ublk->fill_dynamics_data(fluid_phys_desc, voxel_mask, m_sim_phys_types[j], 
                               ublk, TRUE, m_ublk_desc, dynamics_data, &m_attribute_data[j]);

      if (sim.is_scalar_model && sim.uds_solver_type == LB_UDS) {
	ublk->fill_uds_data(fluid_phys_desc, voxel_mask);
      }
      
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();

      // Filter out the meas cell refs that don't apply to this subset of voxels (because the dynamics
      // voxel mask does not overlap the meas cell ref voxel mask). Allocate the vector as static so
      // that we don't have to repeatedly hit the heap.
      static std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > out_ublk_meas_cell_refs;
      std::vector < cDGF_UBLK_MEAS_CELL_REFERENCE > &tmp_ublk_meas_cell_refs = 
        filter_ublk_meas_cell_refs(m_ublk_desc->meas_cell_refs, out_ublk_meas_cell_refs, voxel_mask.get());

      asINT32 n_meas_cell_refs = tmp_ublk_meas_cell_refs.size();
      asINT32 last_meas_window_index = -1;
      asINT32 iref = 0;
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = ublk->create_meas_cell_ptrs(last_dynamics_data, n_meas_cell_refs);
      sMEAS_WINDOW_COMBINATION  window_ids;
      
      for(asINT32 i = 0; i < n_meas_cell_refs; i++) {
        cDGF_UBLK_MEAS_CELL_REFERENCE &meas_cell_ref = tmp_ublk_meas_cell_refs[i];
        asINT32 meas_window_index = meas_cell_ref.meas_window_index;
        MEAS_WINDOW window = g_meas_windows[meas_window_index];
        // Particle trajectory measurement windows don't have meas cells but a ublk needs to know which trajectory windows it belongs too.
        // A bitmask is used to record this information in the ublks particle solver data block.
        if (window->m_is_particle_trajectory_window) {
          if (sim.is_particle_model) {
            uINT64 window_bitflag = 1 << ((TRAJECTORY_WINDOW)window)->trajectory_window_index();
            ublk->p_data()->s.window_mask |= window_bitflag;
          }
          ublk_meas_cell_ptrs[iref].set_window_index(meas_window_index);
          last_meas_window_index = meas_window_index;
        } else {
          
          // In 2D, we only consider voxels on the min Z face. The discretizer enforces this in the meas
          // cell ref voxel mask.
          auINT32 meas_voxel_mask = meas_cell_ref.voxel_mask;
          
          STP_MEAS_CELL_INDEX global_meas_cell_index;
          uINT8 axis;
          if (window->is_development) {
            STP_MEAS_CELL_INDEX global_meas_cell_index_plus_axis = meas_cell_ref.meas_cell_index;
            lgi_meas_cell_index_dev_segment(global_meas_cell_index_plus_axis, global_meas_cell_index, axis);
          } else {
            global_meas_cell_index = meas_cell_ref.meas_cell_index;
          }
          
          // Convert global meas cell index into local meas cell index
          STP_MEAS_CELL_INDEX meas_cell_index = window->create_ublk_meas_cell(m_ublk_desc,
                                                                              global_meas_cell_index,
                                                                              voxel_scale,
                                                                              ublk->is_split(),
                                                                              meas_voxel_mask);
          if (meas_window_index == last_meas_window_index) {
            if (window->is_per_voxel_for_scale(voxel_scale))
              msg_internal_error("Ublk %d includes multiple references to per-voxel meas window (index %d)", 
                                 ublk->id(), meas_window_index);
          }
          last_meas_window_index = meas_window_index;
          
          ublk_meas_cell_ptrs[iref].set_window_index(meas_window_index);
          ublk_meas_cell_ptrs[iref].set_index(meas_cell_index);
          ublk_meas_cell_ptrs[iref].set_voxel_mask(meas_voxel_mask);
          if (window->is_development) {
            ublk_meas_cell_ptrs[iref].set_axis((asINT32) axis);
            ublk_meas_cell_ptrs[iref].set_part((asINT32) fluid_region_index);
            if (ublk->is_ublk_moving()) {
              asINT32 n_segments =
                window->entity_n_segments[axis][fluid_region_index];
              for (asINT32 j = 1; j < n_segments; j++) {
                window->create_ublk_meas_cell(m_ublk_desc,
                                              global_meas_cell_index + j,
                                              voxel_scale, FALSE, // not split
                                              meas_voxel_mask);
              }
            }
          }
          if (sim.is_particle_model) {
            window_ids.insert(meas_cell_ref.meas_window_index);
          }
        }
        iref++;
      }
      if (sim.is_particle_model) {
        ublk->p_data()->s.m_window_data.m_window_combination_index = g_meas_window_combinations.index_of(window_ids);
      }
    }
  }
}

VOID cREAL_UBLK_DESC_PARSER::fill_surf_geom_data(UBLK ublk){
  DO_ACTIVE_VOXELS(voxel) {
    if (ublk->is_near_surface()) {
      ublk->surf_geom_data()->pfluids[voxel] = m_voxel_pfluids[voxel];
      // SURFEL_CENTRIC
      if (m_voxel_pfluids[voxel] != 0) {
	ublk->surf_geom_data()->inverse_pfluids[voxel] = 1.0 / ((dFLOAT) m_voxel_pfluids[voxel]);
      } else {
	ublk->surf_geom_data()->inverse_pfluids[voxel] = 0.0;
      }
      if (!is_vr_fine()  &&
          ((!is_vr_coarse()) || is_vr_coarse_with_no_fine()) &&
          (m_voxel_pfluids[voxel] < g_pfluid_crit)) {
	sdFLOAT p_ratio = m_voxel_pfluids[voxel] * g_one_over_pfluid_crit;
	ublk->surf_geom_data()->pfluids_s2v[voxel] = p_ratio * p_ratio * (3.0 - 2.0 * p_ratio);
      } else {
	ublk->surf_geom_data()->pfluids_s2v[voxel] = 1.0;
      }
      ublk->surf_lb_data()->v2s_dist[voxel] = 0.0;
    }
    ublk->set_voxel_fluid_connect_mask(voxel, m_fluid_conn_voxel_masks[voxel]);
    ublk->set_voxel_path_connect_mask(voxel, m_path_conn_voxel_masks[voxel]);
  }
}

VOID cREAL_UBLK_DESC_PARSER::update_vr_fine_fluid_voxel_mask(UBLK ublk) {
  VOXEL_MASK_8 fluid_like_voxel_mask{0x00};
  DO_ACTIVE_VOXELS(voxel) {
    if (ublk->is_near_surface()) {
      if (ublk->surf_geom_data()->pfluids[voxel] > 0.0)
        fluid_like_voxel_mask.set(voxel);
    } else {
      fluid_like_voxel_mask.set(voxel);
    }
  }
  ublk->fluid_like_voxel_mask = fluid_like_voxel_mask;
}

//CONDUCTION-CHECK: Is this to be implemented for MIRROR_UBLK as well?
VOID cREAL_UBLK_DESC_PARSER::maybe_store_voxel_face_areas(UBLK ublk){
  if (ublk->is_conduction_solid() && is_near_surf()) {
    NEARBLK_CONDUCTION_DATA conduction_data = ublk->surf_conduction_data();
    
    DO_ACTIVE_VOXELS(voxel) {
      DGF_VOXEL_FLAGS voxel_flags = m_ublk_desc->voxel_flags[voxel].voxel_flags;

      //CONDUCTION-CHECK: Is it sufficient to check if the voxel is "general" as opposed to near-surface?
      //CONDUCTION-TODO: Remove the near-surface check after face areas are provided only for near-surface UBLKs
      if (voxel_flags & DGF_VOXEL_FACE_AREA) {
        DGF_GENERAL_VOXEL_DESC voxel_desc = &m_ublk_desc->general_voxels[voxel];
        ccDOTIMES(f, 6) {
          conduction_data->face_areas[f][voxel] = voxel_desc->face_areas.face_area[f];
        }
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
        LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%d(%d): %f %f %f %f %f %f", ublk->id(), voxel,
            voxel_desc->face_areas.face_area[0],
            voxel_desc->face_areas.face_area[1],
            voxel_desc->face_areas.face_area[2],
            voxel_desc->face_areas.face_area[3],
            voxel_desc->face_areas.face_area[4],
            voxel_desc->face_areas.face_area[5]);
#endif
      } else {
        ccDOTIMES(f, 6) {
          conduction_data->face_areas[f][voxel] = 1.0;
        }
      }
    } 
  }
}

VOID cREAL_UBLK_DESC_PARSER::maybe_set_implicit_state_index(UBLK ublk){
  if (ublk->is_conduction_solid() && sim.use_implicit_solid_solver) {
    asINT32 count_voxels_in_mask = 0;
    DO_ACTIVE_VOXELS(voxel) {
      if (is_voxel_in_mask(voxel, ublk->fluid_like_voxel_mask)){
        ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] = m_ublk_desc->b.implicit_solid_state_index + count_voxels_in_mask;
        //msg_print("V %d = %d count v %d", voxel, ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel], count_voxels_in_mask);
        count_voxels_in_mask++;
      } else {
        ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] = -1; 
        //msg_print("Setting -1 to U %d V %d", ublk->id(), voxel);
      }
    } 
  }
}

VOID cREAL_UBLK_DESC_PARSER::add_split_neighbor_info_to_ublks(UBLK ublk) {

  if (!m_voxel_face_polygons.size())
    return;
  sSPLIT_NEIGHBOR_INFO *split_neighbor_info = ublk->allocate_split_neighbor_info();
  split_neighbor_info->init(ublk);
  split_neighbor_info->init_face_connectivity(m_voxel_face_polygons);
}

VOID cREAL_UBLK_DESC_PARSER::post_process_regular_real_ublk(UBLK ublk){
  
  cassert(m_n_phys_types > 0);

  init_basic_ublk_properties(ublk);

  compute_real_ublk_centroids<DGF_REAL_UBLK_DESC>(ublk);
  
  maybe_store_voxel_face_areas(ublk);
  
  ccDOTIMES(j, m_n_phys_types) {
    VOXEL_MASK_8 voxel_mask = m_voxel_masks[j];
    if (voxel_mask.any()) {
      // update the ublk_box
      add_ublk_to_ublk_box(m_fluid_region_indices[j],
                           m_ublk_desc->b.voxel_scale,
                           m_ublk_desc->b.lrf_index, ublk);
    }
  }

  // ghost blocks do not participate in dynamics, and only need the voxel pfluids filled in
  // VR fine ublks do not participate in dynamics and measurements   
  if(is_ghost()) {
    VOXEL_MASK_8 voxel_mask{0};
    ccDOTIMES(i,m_n_phys_types) {
      voxel_mask |=  m_voxel_masks[i];
    }
    ublk->fluid_like_voxel_mask = voxel_mask;
  }

  if ( !(is_vr_fine() || is_ghost()) ){
    update_dynamics_data_and_meas_info(ublk);
    update_total_fluid_voxel_count(ublk);
  }

  fill_surf_geom_data(ublk);  
  ublk->set_distance_info(m_ublk_desc, TRUE, m_voxel_mask);
  ublk->init_p_data();

  add_split_advect_info_to_ublks<DGF_REAL_UBLK_DESC>(ublk);
  add_split_neighbor_info_to_ublks(ublk);
  add_vr_data(ublk);
  read_ckpt_info(ublk);
  do_smart_seed_ublk(ublk);

  if (is_vr_fine() && !is_ghost())
    update_vr_fine_fluid_voxel_mask(ublk);
  
  //msg_print("Setting index to real U %d ghost? %d home_sp: %d my proc %d", ublk->id(), is_ghost(), m_home_sp, my_proc_id);
  maybe_set_implicit_state_index(ublk);
}

VOID cREAL_UBLK_DESC_PARSER::post_process_solid_ublk(UBLK solid_ublk){
  
  sUBLK::sUBLK_ATTRS ublk_attrs = get_ublk_attrs();

  solid_ublk->m_ublk_attributes = ublk_attrs;
  constexpr size_t dyn_data_size = 0; //solid ublks have no dyn data
  solid_ublk->init(m_ublk_desc, dyn_data_size);

  if (m_n_phys_types == 0) {
    solid_ublk->fluid_like_voxel_mask.reset_all();
  }
  
  if (!is_ghost()) {
    g_ckpt_group.add(solid_ublk, m_realm);
    solid_ublk->unset_real_ckpt_data();
    if (has_ghosts()) {
      solid_ublk->set_fringe();
    }
  }
  else {
    solid_ublk->set_ghost();
    solid_ublk->m_home_sp = m_home_sp;
  }

  g_ublk_table[m_realm].add(solid_ublk); // register in ublk table
  
  if ((m_ublk_desc->b.ublk_flags & DGF_UBLK_IS_SPLIT)) {
    solid_ublk->set_split();
  }
  add_ublk_to_ublk_box(-1,
                       m_ublk_desc->b.voxel_scale,
                       m_ublk_desc->b.lrf_index, solid_ublk);

  add_vr_data(solid_ublk);

  if(is_vr_fine()) {
    // These solid vr_fine ublks are just placeholders, so the dest_sp doesn't matter
    cNEIGHBOR_SP dest_sp = cNEIGHBOR_SP(0);
    UBLK_GROUP ugroup = g_solid_vr_fine_group->create_group(solid_ublk->scale(), dest_sp);
    ugroup->add_shob_to_group(solid_ublk);
    solid_ublk->set_group(ugroup);
  }

  //This ublk does not read ckpt info
  read_ckpt_info(nullptr);
  do_smart_seed_ublk(nullptr);
}

VOID cREAL_UBLK_DESC_PARSER::post_process(sSHOB* shob){
  //The cast is more explicit to be compiler safe
  UBLK ublk = static_cast<UBLK> (shob);
  cassert(ublk);

  if (!is_solid_ublk()) {
    post_process_regular_real_ublk(ublk);
  }
  else {
    post_process_solid_ublk(ublk);
  }
}

//=======================================================================================================
// REAL UBLK DESC PARSER : SIMSIZES SPECIFIC BUILD
#else
//=======================================================================================================

VOID cREAL_UBLK_DESC_PARSER::read_descriptor(){ cBASE_UBLK_DESCS_PARSER::read_descriptor(); }
VOID cREAL_UBLK_DESC_PARSER::post_process(sSHOB* shob){}

#endif

//=======================================================================================================
// UBLK DESC PARSER FACTORY
// Manufactures all UBLK descriptor parser objects. 
//=======================================================================================================

cBASE_UBLK_DESCS_PARSER* cUBLK_DESC_PARSER_FACTORY::create_ublk_desc_parser(STP_REALM realm,
                     DGF_UBLK_BASE_DESC descriptor,
									   uINT8 ublk_decomp_flags,
									   STP_PROC home_sp, 
									   std::vector<cDGF_GHOST_INFO> &ghost_info,
									   LGI_STREAM istream){

  DGF_SHOB_DESC_TYPE desc_type = descriptor->type();
  cBASE_UBLK_DESCS_PARSER*   parser = nullptr;
  
  switch (desc_type) {
  case DGF_SHOB_SIMPLE_UBLK:
    {
#if !BUILD_FOR_SIMSIZES
      parser = &cUBLK_DESC_PARSER_FACTORY::simple_ublk_desc_parser_decorator;
#else      
      parser = &cUBLK_DESC_PARSER_FACTORY::simple_ublk_desc_parser;
#endif
      break;
    }
  case DGF_SHOB_REAL_UBLK:
    {
      parser = &cUBLK_DESC_PARSER_FACTORY::real_ublk_desc_parser;
      break;
    }
  case DGF_SHOB_MIRROR_UBLK:
    {
      parser = &cUBLK_DESC_PARSER_FACTORY::mirror_ublk_desc_parser;
      break;
    }
  default :
    {
      msg_internal_error("Invalid UBLK descriptor type");
      break;
    }
  }
  parser->reset();
  parser->set_realm(realm);
  parser->set_ublk_desc(descriptor);
  parser->set_ghost_info(ghost_info);
  parser->set_lgi_stream(istream);
  parser->set_home_sp(home_sp);
  parser->set_ublk_decomp_flags(ublk_decomp_flags);
  return parser;
}

//Init static members
cMIRROR_UBLK_DESC_PARSER cUBLK_DESC_PARSER_FACTORY::mirror_ublk_desc_parser;
cSIMPLE_UBLK_DESC_PARSER cUBLK_DESC_PARSER_FACTORY::simple_ublk_desc_parser;
cSIMPLE_UBLK_DESC_PARSER_DECORATOR cUBLK_DESC_PARSER_FACTORY::simple_ublk_desc_parser_decorator;
cREAL_UBLK_DESC_PARSER cUBLK_DESC_PARSER_FACTORY::real_ublk_desc_parser;
