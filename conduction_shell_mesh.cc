/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
#ifndef CONDUCTION_SHELL_MESH_CC_INCLUDED
#define CONDUCTION_SHELL_MESH_CC_INCLUDED


#include <execinfo.h>
#include "sim.h"
#include "surfel.h"
#include "conduction_shell_mesh.h"
#include "surfel_vertices.h"
#include "conduction_data.h"
#include "phys_type_map.h"
#include "common_math.h"
#include "conduction_solver_stencils.h"
#include PHYSICS_H
#include BREP_CC
#include BAGS_CC

// These provide additional info for identifying causes and creating cut down simulations
// while the implementation is still being debugged
#ifdef DEBUG_BREP_STENCIL
#define surfel_unfolder_stencil_assert(expr)                                                                            \
  if (!(expr)) {                                                                                                        \
    sdFLOAT scentroid[3];                                                                                               \
    vcopy(scentroid,this->m_root_facet->get_surfel()->centroid);                                                        \
    msg_print("Error in cSURFEL_UNFOLDER: root = %d, nbr = %d\n\tLocation = (%f, %f, %f)",                              \
        this->m_root_facet->get_surfel()->id(), this->m_nbr_facet->get_surfel()->id(),                                  \
        EXPAND_FLOAT_VEC3(scentroid));                                                                                  \
    msg_print("  CutPoints%s",getCutPointsString().c_str());                                                                       \
    msg_print("  CutplaneNormal = np.array([%1.10e, %1.10e, %1.10e])", EXPAND_FLOAT_VEC3(m_cut_plane.Normal()));        \
    msg_print("  VecToNbr = np.array([%1.10e, %1.10e, %1.10e])", EXPAND_FLOAT_VEC3(m_vec_to_nbr));                      \
    assert_handler(__STRING(expr), __FILE__, __LINE__);                                                                 \
  }

  #define half_edge_pair_stencil_assert(expr)                                                                           \
  if (!(expr)) {                                                                                                        \
    asINT32 nbrId = (this->m_he2 != NULL) ? this->m_he2->get_surfel()->id() : -1;                                       \
    std::string msg_to_print = "";                                                                                      \
    if (this->is_open_shell_set()) {                                                                                    \
      cOPEN_SHELL_SET* osSet = reinterpret_cast<cOPEN_SHELL_SET*>(this);                                                \
      const auto& osVec = osSet->open_shell_vec();                                                                      \
      std::string shellIds = (osVec.size()) ? fmt::format("[{}",osVec[0]->get_surfel()->id()) : "";                     \
      for (int ii = 1; ii < osVec.size(); ii++) {                                                                       \
        shellIds += fmt::format(", {}",osVec[ii]->get_surfel()->id());                                                  \
      }                                                                                                                 \
      shellIds += "]";                                                                                                  \
      msg_to_print += fmt::format("Error in cOPEN_SHELL_SET: he1 = {}, he2 = {}\n\tOpen Shells: {}",                    \
          m_he1->get_surfel()->id(), nbrId, shellIds.c_str());                                                          \
      msg_to_print += fmt::format("\tHasPrevPair {}, HasNextPair {}, IsCircular {}, EntireRing {}",                     \
        this->prev_pair_connected(), this->next_pair_connected(), this->is_group_circular(),                            \
        this->m_includes_entire_ring);                                                                                  \
    } else {                                                                                                            \
      msg_to_print += fmt::format("Error in cHALF_EDGE_PAIR: he1 = {}, he2 = {}", this->m_he1->get_surfel()->id(),      \
        nbrId);                                                                                                         \
      msg_to_print += fmt::format("\tHasPrevPair {}, HasNextPair {}, IsCircular {}", this->prev_pair_connected(),       \
        this->next_pair_connected(), this->is_group_circular());                                                        \
    }                                                                                                                   \
    msg_print("%s\n%s", msg_to_print.c_str(), this->m_he1->print_detailed_ring_info(FALSE).c_str());                    \
    assert_handler(__STRING(expr), __FILE__, __LINE__);                                                                 \
  }

#define half_edge_stencil_assert(expr)                                                              \
  if (!(expr)) {                                                                                    \
    cSHELL_MESH::sBG_POINT3 this_centroid = get_shell_mesh()->HalfEdgeMidPoint(this);               \
    msg_print("Error in SHELL_HALF_EDGE method:\n\tLocation = (%f, %f, %f)",                        \
      EXPAND_FLOAT_VEC3(this_centroid));                                                            \
    assert_handler(__STRING(expr), __FILE__, __LINE__);                                             \
  }

#define facet_stencil_assert(expr)                                                                  \
  if (!(expr)) {                                                                                    \
    cSHELL_MESH::sBG_POINT3 this_centroid = get_shell_mesh()->GetFacetCentroid(this->GetIndex());   \
    msg_print("Error in SHELL_FACET method:\n\tLocation = (%f, %f, %f)",                            \
      EXPAND_FLOAT_VEC3(this_centroid));                                                            \
    assert_handler(__STRING(expr), __FILE__, __LINE__);                                             \
  }
#else
#define surfel_unfolder_stencil_assert(expr)
#define half_edge_stencil_assert(expr)
#define facet_stencil_assert(expr)
#endif


static char g_assert_backtrace_path[64];
extern "C" void assert_handler(const char * assertion, const char * file, unsigned int line)
{
  //Attempt to log a backtrace.
  void* return_addresses[128];
  int stack_depth = backtrace(return_addresses, 128);
  int backtrace_log_fd = open(g_assert_backtrace_path, O_WRONLY | O_CREAT | O_TRUNC, 0660);
  dprintf(backtrace_log_fd, "The assertion %s on line %d of file %s failed in the shell stencil building process.\n The backtrace is:\n", assertion, line, file);
  backtrace_symbols_fd(return_addresses, stack_depth, backtrace_log_fd);
  dprintf(backtrace_log_fd, "Use \"addr2line -fCe <executable> <address>\" to retrieve function names and line numbers (when possible).\n"); 
  close(backtrace_log_fd);
  msg_print("A assertion failure was encountered on line %d of file %s during the shell stencil building.\nThe backtrace has been written to %s.", line, file, g_assert_backtrace_path);
  exit(1);
}

/**
 @brief Returns TRUE if surfel associated with facet is a conduction shell
 */
BREP_DEFINE_FACET_METHOD(BOOLEAN, SHELL_FACET)::is_facet_shell(BOOLEAN exclude_ghosts) {
  if (exclude_ghosts && this->get_surfel()->is_ghost()) {
    return FALSE;
  }
  return m_surfel->is_conduction_shell();
}

/**
 @brief Returns TRUE if surfel associated with facet shares an edge with a conduction shell

 @details Function used to identify facets to be kept during the simplification process
 */
BREP_DEFINE_FACET_METHOD(BOOLEAN, SHELL_FACET)::is_facet_shell_or_shell_neighbor(BOOLEAN exclude_ghosts) {
  // if facet surfel is a conduction shell return true
  if (this->is_facet_shell(exclude_ghosts)) { return TRUE; }

  // if it is a conduction surfel, loop through edges to determine if facet has neighboring edge
  if (this->get_surfel()->is_conduction_surfel()) {
    BREP_FACET_DO_HALF_EDGES(half_edge, this, cSHELL_MESH) {
      if (half_edge->is_shell_in_ring(exclude_ghosts)) {
        return TRUE;
      }
    }
  }
  // if no neighboring surfels are shell surfels, return false
  return FALSE;
}

/**
 @brief Returns TRUE if surfel associated with facet is an open shell facet

 @details Information needed to determine orientation of layers in internal edge
 */
BREP_DEFINE_FACET_METHOD(BOOLEAN, SHELL_FACET)::is_open_shell_facet() {
  return this->get_surfel()->is_conduction_open_shell();
}

/**
 @brief Returns face index of surfel associated with facet
 */
BREP_DEFINE_FACET_METHOD(asINT32, SHELL_FACET)::surfel_face_id() {
  cSHELL_MESH::FACET thisHe = (cSHELL_MESH::FACET)this;
  return this->get_surfel()->m_face_index;
}

/**
 @brief Returns number of shell layers in surfel associated with facet

 @details Must only be called for shell surfel facets or assert will catch it
 */
BREP_DEFINE_FACET_METHOD(asINT32, SHELL_FACET)::num_layers() {
  facet_stencil_assert(this->is_facet_shell());
  return m_surfel->shell_conduction_data()->num_layers();
}

/**
 @brief Returns the id of this facet's surfel's opposite surfel

 @details If facet's surfel does not have opposite, returns -1.
 */
BREP_DEFINE_FACET_METHOD(asINT32, SHELL_FACET)::get_opposite_surfel_id() {
  SURFEL opp_surfel = static_cast<SURFEL>(this->get_surfel()->opposite_surfel());
  if (opp_surfel != NULL) {
    return opp_surfel->id();
  }
  return -1;
}

/**
 @brief Returns TRUE if nbr_facet's surfel will always contain identical dynamics data as this facet's surfel
 
 @details Checks that the two surfels are located in the same face, and have either fixed temp, fixed flux,
          or adiabatic dynamics data. If the dynamics data is fixed temp/flux, verifies that the temp/flux
          is not spatially varying. If the two surfels meet these conditions, only one of them
          needs to be used to update boundary data in the conduction edge object representing their shared edge. 
 */
BREP_DEFINE_FACET_METHOD(BOOLEAN, SHELL_FACET)::is_boundary_facet_duplicate(SHELL_FACET_TYPE facet2) {
  SHELL_FACET_TYPE facet1 = (SHELL_FACET_TYPE)this;
  facet_stencil_assert(!(facet1->is_facet_shell() || facet2->is_facet_shell()));

  SURFEL surfel1 = facet1->get_surfel();
  SURFEL surfel2 = facet2->get_surfel();

  // must come from same face
  if (surfel1->m_face_index != surfel2->m_face_index) { return FALSE; }

  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data1 = surfel1->dynamics_data();
  uINT32 dynamicsType1 = dyn_data1->m_dynamics_type;

  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data2 = surfel2->dynamics_data();
  uINT32 dynamicsType2 = dyn_data2->m_dynamics_type;

  // if both surfels in the same face are fixed temperature or flux, they contain
  // identical information as long as they are not space varying. If both are adiabatic,
  // they will always contain identical info, so return True.
  if (dynamicsType1 == dynamicsType2) {
    if (dynamicsType1 == CONDUCTION_ADIABATIC_SURFEL_TYPE) {
      return TRUE;
    }

    if (dynamicsType1 == CONDUCTION_FIXED_TEMP_SURFEL_TYPE) {
      CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc1
        = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data1->physics_descriptor();
      CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc2
        = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data2->physics_descriptor();
      return (phys_desc1->parameters()->temperature.is_space_varying ||
              phys_desc2->parameters()->temperature.is_space_varying)
             ? FALSE : TRUE;
    }
    else if (dynamicsType1 == CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE) {
      CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR phys_desc1
        = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)dyn_data1->physics_descriptor();
      CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR phys_desc2
        = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)dyn_data2->physics_descriptor();
      return (phys_desc1->parameters()->heat_flux_1.is_space_varying ||
              phys_desc2->parameters()->heat_flux_1.is_space_varying)
             ? FALSE : TRUE;
    }
  }
  return FALSE;
}

/**
 @brief Returns TRUE if the facet contains one or more edge neighbors.
 */
BREP_DEFINE_FACET_METHOD(BOOLEAN, SHELL_FACET)::has_edge_neighbors() {
  BREP_FACET_DO_HALF_EDGES(half_edge, this, cSHELL_MESH) {
    if (half_edge->num_faceted_edges() > 1) {
      return TRUE;
    }
  }
  return FALSE;
}

/**
 @brief Returns the first (sharable) edge in the 2D facet. 

 @details Loops through edges in 2D facet and returns the first not located in the xy-plane. Should
          only be called in 2D simulations

          Used by the BREP_FACET_DO_2D_HALF_EDGES macro to begin the loop.
 */
BREP_DEFINE_FACET_METHOD(BREP_FACET_TYPENAME(SHELL_FACET)::HALF_EDGE, SHELL_FACET)::get_2d_half_edge() {
  facet_stencil_assert(sim.is_2d());
  cSHELL_MESH::HALF_EDGE thisHe = this->GetHalfEdge();
  if (thisHe->is_2d_half_edge()) {
    return thisHe;
  }
  return thisHe->next_2d_half_edge();
}

/**
 @brief Loops through half_edges in facet, identifies the type and stores it in the half_edge data structure
 
 @details All type identification and storing of information in half_edge data structure is handled
          by cEDGE_TYPE_IDENTIFIER class. The cEDGE_TYPE_IDENTIFIER class processes all half_edges in
          a ring. To avoid re-processing a ring in subsequent facets, a flag in the half_edge class is
          used to indicate that the processing has already been performed. This is checked using the is_processed()
          function.
 */
BREP_DEFINE_FACET_METHOD(VOID, SHELL_FACET)::identify_facet_edge_types() {
  cSHELL_MESH::FACET root_facet = (cSHELL_MESH::FACET)this;
  BOOLEAN isGhost = this->get_surfel()->is_ghost();
  if (sim.is_2d()) {
    BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }
  
      // Skip if already processed
      if (half_edge->is_processed()) { continue; }
  
      cEDGE_TYPE_IDENTIFIER typeIDer(half_edge);
      typeIDer.process(isGhost);

      // Make sure all edges are set as inactive so these edges are
      // skipped over during subsequent visits from other facets
      half_edge->set_processed(TRUE);
    }
  } else {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if already processed
      if (half_edge->is_processed()) { continue; }

      cEDGE_TYPE_IDENTIFIER typeIDer(half_edge);
      typeIDer.process(isGhost);
      
      // Make sure all edges are set as inactive so these edges are
      // skipped over during subsequent visits from other facets
      half_edge->set_processed(TRUE);
    }
  }
}

/**
 @brief Returns half edge of facet at index ind starting from facet->GetHalfEdge() as ind = 0.
 */
BREP_DEFINE_FACET_METHOD(BREP_FACET_TYPENAME(SHELL_FACET)::HALF_EDGE, SHELL_FACET)::half_edge_from_index(asINT32 ind) {
  cSHELL_MESH::HALF_EDGE half_edge = this->GetHalfEdge();
  ccDOTIMES(i, ind) {
    half_edge = half_edge->GetNext();
  }
  return half_edge;
}

/**
 @brief Prints facet information to stdout

 @details Prints geometric and properties of associated surfel and calls the BASE_FACET Print method.
          Useful for debugging.
 */
BREP_DEFINE_FACET_METHOD(VOID, SHELL_FACET)::dump_facet_info() {
  cSHELL_MESH::FACET thisFacet = (cSHELL_MESH::FACET)this;
  SURFEL surfel = thisFacet->get_surfel();
  auto scentroid = get_shell_mesh()->GetFacetCentroid(thisFacet->GetIndex());
  auto snormal = get_shell_mesh()->GetFacetUnitNormal(this->GetIndex());

  msg_print("Facet %d, Surfel %d: Centroid = (%.3f, %.3f, %.3f), Norm = (%.4f, %.4f, %.4f)"
    " shell = %d, isGhost = %d\n", thisFacet->GetIndex(), surfel->id(), EXPAND_FLOAT_VEC3(scentroid),
    EXPAND_FLOAT_VEC3(snormal), surfel->m_face_index, thisFacet->get_surfel()->is_ghost());
  thisFacet->Print();
  printf("\n");

  BREP_FACET_DO_HALF_EDGES(half_edge, thisFacet, cSHELL_MESH) {
    half_edge->Print();
    printf("\n");
  }
}

/**
 @brief Dumps facet information to stdout to be used for vertification tests

 @details Dumps edge information to be used for verification in python script that compares surfel info
          to ensure edge types match for neighboring surfels and to be produce plots for visual verification
          of the resulting edge types.

          This should only be called after stencils and conduction edges have been created for
          all shell surfels.

          Should be updated to use logging mechanism to improve analysis when using multiple SPs
 */
BREP_DEFINE_FACET_METHOD(VOID, SHELL_FACET)::dump_edge_verification_info(std::vector<SHELL_HE_TYPE>& edges) {
  cSHELL_MESH::FACET thisFacet = (cSHELL_MESH::FACET)this;

  SURFEL surfel = thisFacet->get_surfel();
  asINT32 start_int_edge_index = surfel->shell_conduction_data()->stencil_info_internal_edge_index;
  asINT32 start_boundary_edge_index = surfel->shell_conduction_data()->stencil_info_boundary_edge_index;
  asINT32 end_nbr_index = surfel->shell_conduction_data()->stencil_info_end_index;

  asINT32 start_vert_nbr_index = surfel->shell_conduction_data()->stencil_info_vertex_nbr_index;
  asINT32 n_int_edge_nbrs = start_boundary_edge_index - start_int_edge_index;
  asINT32 n_bnd_edge_nbrs = start_vert_nbr_index - start_boundary_edge_index;
  asINT32 n_vert_nbrs = end_nbr_index - start_vert_nbr_index;
  asINT32 n_edge_nbrs = n_bnd_edge_nbrs + n_int_edge_nbrs;

  LOG_MSG("SHELL_SURFEL_VERIFICATION", LOG_TS).printf("Facet %d, Surfel %d, FaceID %d",
      thisFacet->GetIndex(), surfel->id(), surfel->m_face_index);
  LOG_MSG("SHELL_SURFEL_VERIFICATION", LOG_TS).printf("  InternalEdges %d, BoundaryEdges %d, VertexNeighbors %d\n",
      n_int_edge_nbrs, n_bnd_edge_nbrs, n_vert_nbrs);

  for (int i = 0; i < n_edge_nbrs; i++) {
    LOG_MSG("SHELL_SURFEL_VERIFICATION", LOG_TS).printf("  Edge %d: TailVertex %d, HeadVertex %d",
        i, edges[i]->GetTailVertex()->GetIndex(), edges[i]->GetHeadVertex()->GetIndex());
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_nbr = g_shell_conduction_edges.get_nbr_stencil(i+start_int_edge_index);
    std::ostringstream oss;
    oss << stencil_nbr << std::endl;
    LOG_MSG("SHELL_SURFEL_VERIFICATION", LOG_TS).printf("%s", oss.str().c_str());
  }

  for (int i = 0; i < n_vert_nbrs; i++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_nbr = g_shell_conduction_edges.get_nbr_stencil(i+start_vert_nbr_index);
    std::ostringstream oss;
    oss << stencil_nbr << std::endl;
    LOG_MSG("SHELL_SURFEL_VERIFICATION", LOG_TS).printf("%s", oss.str().c_str());
  }
}

BREP_DEFINE_FACET_METHOD(VOID, SHELL_FACET)::globalVertexIndices(std::vector<DGF_VERTEX_INDEX>& vertInds) {
  vertInds.clear();
  cSHELL_MESH::FACET thisFacet = (cSHELL_MESH::FACET)this;
  BREP_FACET_DO_VERTICES(vertex, thisFacet, cSHELL_MESH) {
    DGF_VERTEX_INDEX vInd1 = g_surfel_vertices_info.map_local_to_global_index(vertex->GetIndex());
    vertInds.push_back(vInd1);
  }
}

/////////////////////////////////////////////////////////////////////////////////////
/////////////////////                                     ///////////////////////////
/////////////////////      HALF_EDGE IMPLEMENTATIONS      ///////////////////////////
/////////////////////                                     ///////////////////////////
/////////////////////////////////////////////////////////////////////////////////////


/**
 @brief Computes geometric information of the edge in the shell surfels local 2D csys.

 @details Calculates the edge normal vector, length and normal distance at the local
          2D csys of the shell surfel associated with the facet owning the edge. All lengths
          are also scaled to the surfel's scale.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID,SHELL_HALF_EDGE)::compute_geometric_info() {
  dFLOAT twoLam_inv = 1.0/scale_to_voxel_size(this->GetFacet()->get_surfel()->scale());

  SURFEL surfel = this->GetFacet()->get_surfel();
  cSHELL_MESH::sBG_VECTOR3 he_vec = get_shell_mesh()->HalfEdgeVec(this);

  // calculate length
  m_length = (sim.is_2d()) ? 1.0 : he_vec.Length() * twoLam_inv;

  // calculate normal vector
  cSHELL_MESH::sBG_VECTOR3 surf_normal = get_shell_mesh()->GetFacetUnitNormal(this->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 edge_normal = BgCross(he_vec, surf_normal);
  edge_normal.Normalize();

  // verify normal is pointing out of facet
  cSHELL_MESH::sBG_POINT3 surf_centroid = get_shell_mesh()->GetFacetCentroid(this->GetFacet()->GetIndex());
  if (sim.is_2d()) {
    surf_centroid[2] = 0.;
  }

  cSHELL_MESH::sBG_VECTOR3 centroid_to_tail_vec = this->GetVertex()->GetPoint() - surf_centroid;
  // if ((edge_normal * centroid_to_tail_vec) < 0.0) {
  //   edge_normal *= -1.0;
  // }

  // calculate normal distance to edge
  m_normal_dist = centroid_to_tail_vec * edge_normal * twoLam_inv;

  // rotate normal vector to local surfel's csys
  cSHELL_MESH::sBG_VECTOR3 local_normal = this->GetFacet()->getTransform().Transform(edge_normal);
  vcopy2(m_normal, local_normal);
  if (sim.is_2d()) {
    m_normal[1] = 0.;
  }
}


/**
 @brief Returns the unscaled length of the half_edge.
 */
BREP_DEFINE_HALF_EDGE_METHOD(dFLOAT,SHELL_HALF_EDGE)::unscaled_length() {
  return m_length * scale_to_voxel_size(this->GetFacet()->get_surfel()->scale());
}

/**
 @brief Returns the unscaled normal distance of the half_edge.
 */
BREP_DEFINE_HALF_EDGE_METHOD(sdFLOAT,SHELL_HALF_EDGE)::unscaled_normal_distance() {
  return m_normal_dist * scale_to_voxel_size(this->GetFacet()->get_surfel()->scale());
}

/**
 @brief Returns conduction edge associated with the half_edge.
 */
BREP_DEFINE_HALF_EDGE_METHOD(CONDUCTION_EDGE_BASE,SHELL_HALF_EDGE)::get_conduction_edge() {
  return m_conduction_edge;
}

/**
 @brief Stores a conduction edge associated with this half_edge and its neighbors.

 @details Allows shell surfels to identify and access conduction edges that have already been created
          at one of its edges by a neighboring shell surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID,SHELL_HALF_EDGE)::set_conduction_edge(CONDUCTION_EDGE_BASE cond_edge){
  m_conduction_edge = cond_edge;
}

/**
 @brief Returns the eDYN_SURFEL_TYPE value associated with the CDI_PHYS_TYPE_DESCRIPTOR in provided dynamics data

 @details The value of m_dynamics_type in the physics descriptor of ghost surfels is 0, so the boundary type
          of a conduction surfel must be obtained from the value of phys_type_desc which stores a
          CDI_PHYS_TYPE_DESCRIPTOR type. This cdi type is used to determine what the value of m_dynamics_type should
          be. 
 */
asINT32 convert_cdi_type_to_dyn_type(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data) {
  CONDUCTION_PHYSICS_DESCRIPTOR phys_desc = (CONDUCTION_PHYSICS_DESCRIPTOR)(dyn_data->physics_descriptor());
  CDI_PHYS_TYPE_DESCRIPTOR cdi_desc = phys_desc->phys_type_desc;
  switch (cdi_desc->cdi_physics_type) {
    case CDI_PHYS_TYPE_ADIABATIC:
      return CONDUCTION_ADIABATIC_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_TEMP:
      return CONDUCTION_FIXED_TEMP_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
      return CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE;
    case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
      return CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE;
    case CDI_PHYS_TYPE_CONTACT_RESISTANCE:
      return CONDUCTION_CONTACT_SURFEL_TYPE;
    case CDI_PHYS_TYPE_COUPLED_THERMAL:
      return CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE;
    default:
      msg_internal_error("Unknown cdi dynamics type %d",cdi_desc->cdi_physics_type);
      return 0;
  }
}


/**
 @brief Returns the eDYN_SURFEL_TYPE value found in the provided dynamics data

 @details If dyn_data->m_dynamics_type == 0 (which occurs in ghost surfels), the CDI_PHYS_TYPE_DESCRIPTOR
          value will be used to determine the dynamics type. If the dynamics type is CONDUCTION_CONTACT_SURFEL_TYPE,
          it will be updated to a CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE if the value of the resistance
          stored in the physics descriptor is fixed at 0.
 */
asINT32 get_dynamics_from_dyn_data(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data) {
  asINT32 dynamicsType = dyn_data->m_dynamics_type;
  if (dynamicsType == 0) {
    dynamicsType = convert_cdi_type_to_dyn_type(dyn_data);
  }
  return dynamicsType;
}

/**
 @brief Returns the eDYN_SURFEL_TYPE stored in the associated surfel's physics descriptor.

 @details For open shell surfels with surfel->has_distinct_back_bc() is true, it will return
          the dynamics type stored in dyn_data->next_dynamics_data(), if it's false, it will
          return INVALID_SURFEL_TYPE to indicate that the type has defaulted to coupled (but still
          allow it to be distinguished from an actual coupled dynamics type). For closed shell surfels,
          it will return the dynamics type stored in surfel->dynamics_data().
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::get_back_dynamics_type() {
  half_edge_stencil_assert(!(this->IsBorder()));
  SURFEL surfel = this->GetFacet()->get_surfel();
  if (surfel->is_conduction_open_shell()) {
    if (surfel->has_distinct_back_bc()) {
      tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data = surfel->dynamics_data();
      return get_dynamics_from_dyn_data(dyn_data->next_dynamics_data());
    } else {
      return INVALID_SURFEL_TYPE;
    }
  }
  return get_dynamics_from_dyn_data(surfel->dynamics_data());
}

/**
 @brief Returns the number of shell surfels located in the half_edge ring.

 @details Includes all shell surfels regardless of the number of layers.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::num_shell_edges(BOOLEAN exclude_ghosts) {
  asINT32 count_shells = 0;

  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;
  do {
    if (loopingHe->is_shell_edge(exclude_ghosts)) {
      count_shells++;
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);

  return count_shells;
}

/**
 @brief Returns the number of facets (i.e. surfels) in half_edge ring.

 @details By setting exclude_coupled to TRUE, any boundary surfels that have a shell surfel as their opposite
          will be excluded from the count.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::num_faceted_edges() {
  asINT32 count_faceted = 0;
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;

  do {
    if (!loopingHe->IsBorder()) {
      count_faceted++;
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);

  return count_faceted;
}

/**
 @brief Returns TRUE if all surfels associated with half edges in ring are ghosts.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::only_ghosts_in_ring() {
  cSHELL_MESH::HALF_EDGE thisHE = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHE = thisHE;
  do {
    if (!loopingHE->IsBorder()) {
      if (!loopingHE->get_surfel()->is_ghost()) {
        return FALSE;
      }
    }
    loopingHE = loopingHE->NextInRing();
  } while (loopingHE != thisHE);
  return TRUE;
}


/**
 @brief Returns TRUE if any facet in ring is associated with a shell surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_shell_in_ring(BOOLEAN exclude_ghosts) {
  return (this->num_shell_edges(exclude_ghosts) > 0);
}

/**
 @brief Returns TRUE if this HALF_EDGE edge in a 2D simulation can be shared between facets.

 @details The normalized vector of a 2D edge will always be (0,0,+-1)
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_2d_half_edge() {
  cSHELL_MESH::sBG_VECTOR3 edge_vec = get_shell_mesh()->HalfEdgeVec(this);
  edge_vec.Normalize();
  if (fabs(edge_vec[2]) > 0.5) {
    half_edge_stencil_assert((vlength2(edge_vec)) <= 1e-5);
    return TRUE;
  }
  half_edge_stencil_assert(fabs(edge_vec[2]) <= 1e-5);
  return FALSE;
}

/**
 @brief Returns index of half edge in facet with facet->GetHalfEdge() being index 0
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::index_in_facet() {
  cSHELL_MESH::FACET facet = this->GetFacet();
  cSHELL_MESH::HALF_EDGE thisHE = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE curHE = facet->GetHalfEdge();
  asINT32 index = 0;
  while (curHE != this) {
    index++;
    curHE = curHE->GetNext();
  }
  return index;
}

/**
 @brief Returns TRUE if half_edge is located in this edge's ring.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_edge_in_ring(SHELL_HE_TYPE half_edge) {
  half_edge_stencil_assert(half_edge != NULL);

  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;
  do {
    if (half_edge == loopingHe) { return TRUE; }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);

  return FALSE;
}

/**
 @brief Returns the number of layers in the shell surfel associated with this HALF_EDGE.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::num_layers_at_edge() {
  half_edge_stencil_assert(this->is_shell_edge());
  return this->GetFacet()->num_layers();
}

/**
 @brief Returns the face index of the surfel associated with this HALF_EDGE.

 @details The face indexes stored in the surfel data are used in place of the 
          BREP::SHELL indices. This is because some faces do not contain or interact with
          shell surfels and therefore are not added to the BREP mesh. As a result, the BREP::SHELL
          indices do not match the face indexes and sticking with the face indices allows for comparison
          with data contained in the LGI file.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::surfel_face_id() {
  half_edge_stencil_assert(!this->IsBorder());
  return this->get_surfel()->m_face_index;
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with shell surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_shell_edge(BOOLEAN exclude_ghosts) {
  if (this->IsBorder()) {
    return FALSE;
  }
  if (exclude_ghosts && this->get_surfel()->is_ghost()) {
    return FALSE;
  }
  return this->GetFacet()->is_facet_shell();
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with an open shell surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_open_shell_edge() {
  if (this->IsBorder()) {
    return FALSE;
  }
  return this->GetFacet()->is_open_shell_facet();
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with a closed shell surfel (with or without layers).
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_closed_shell_edge() {
  if (this->IsBorder()) {
    return FALSE;
  }
  return !this->GetFacet()->is_open_shell_facet();
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with an esurfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_esurfel_edge() {
  if (this->IsBorder()) {
    return FALSE;
  }
  return this->get_surfel()->is_conduction_open_shell_boundary();
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with a sampled conduction interface surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_coupled_cond_interface_edge() {
  if (this->IsBorder()) {
    return FALSE;
  }
  return this->get_surfel()->is_conduction_interface_sampled();

  // if (this->get_surfel()->is_conduction_surface()) {
  //   auto dyn_type = this->get_back_dynamics_type();
  //   return (dyn_type == CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE);
  // }
  // return FALSE;
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with non-shell conduction surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_boundary_edge() {
  if (this->IsBorder()) {
    return FALSE;
  }
  return !(this->GetFacet()->is_facet_shell());
}

/**
 @brief Returns TRUE if this HALF_EDGE is associated with a facet/surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN,SHELL_HALF_EDGE)::is_edge_faceted() {
  return (!(this->IsBorder()));
}

/**
 @brief Returns the surfel stored in the facet associated with this HALF_EDGE.
 */
BREP_DEFINE_HALF_EDGE_METHOD(SURFEL,SHELL_HALF_EDGE)::get_surfel() {
  half_edge_stencil_assert(!(this->IsBorder()));
  return this->GetFacet()->get_surfel();
}

#define DEFINE_FIND_NEXT_SHELL_OF_TYPE(_fname, _next_func, _test_func)              \
  BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, \
      SHELL_HALF_EDGE)::_fname() {                                                  \
    cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;                   \
    cSHELL_MESH::HALF_EDGE loopingHe = thisHe->_next_func;                          \
    do {                                                                            \
      if (loopingHe->_test_func)                                                    \
        return loopingHe;                                                           \
      loopingHe = loopingHe->_next_func;                                            \
    } while(loopingHe != thisHe);                                                   \
    return NULL;                                                                    \
  }

/**
 @brief Returns the next shell edge in the ring if one exists.

 @details If this HALF_EDGE is the only shell edge, or no shell edges exist, it will return NULL.
 */
DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_shell_edge, next_faceted_edge(), is_shell_edge());

/**
 @brief Returns the next closed shell surfel (with or without layers) edge in the ring if one exists.

 @details If this HALF_EDGE is the only closed shell surfel edge, or no closed shell surfel edges exist,
          it will return NULL.
 */
DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_closed_shell_surfel_edge, next_faceted_edge(), is_closed_shell_edge());

/**
 @brief Returns the next open shell surfel edge in the ring if one exists.

 @details If this HALF_EDGE is the only open shell surfel edge, or no open shell surfel edges exist,
          it will return NULL.
 */
DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_open_shell_surfel_edge, next_shell_edge(), is_open_shell_edge());

/**
 @brief Returns the next coupled edge in the ring if one exists.

 @details If this HALF_EDGE is the only coupled edge, or no coupled edges exist, it will return NULL.
          Note: This does not take into account if a HALF_EDGE is coupled with this HALF_EDGE, it just returns
          the next coupled HALF_EDGE in the ring.
 */
// DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_coupled_edge, next_faceted_edge(), is_edge_coupled());

/**
 @brief Returns the next boundary edge in the ring if one exists.

 @details If this HALF_EDGE is the only boundary edge, or no boundary edges exist, it will return NULL.
 */
DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_boundary_edge, next_faceted_edge(), is_boundary_edge());

/**
 @brief Returns the next edge associated with a surfel (i.e. non-border edge) in the ring if one exists.

 @details If this HALF_EDGE is the only faceted edge, or no faceted edges exist, it will return NULL.
 */
DEFINE_FIND_NEXT_SHELL_OF_TYPE(next_faceted_edge, NextInRing(), is_edge_faceted());


/**
 @brief Returns the next useful HALF_EDGE in a 2D Facet.

 @details This function can be called from any of the HALF_EDGEs in a 2D facet and will return the next
          useful edge as long as the simulation is 2D.

          Note: Unlike most of the other methods, this is not returning
          a HALF_EDGE on the same ring as this HALF_EDGE. Instead it is returning another HALF_EDGE in
          the associated FACET.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE,
      SHELL_HALF_EDGE)::next_2d_half_edge() {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe->GetNext();
  do {
    if (loopingHe->is_2d_half_edge())
      return loopingHe;
    loopingHe = loopingHe->GetNext();
  } while(loopingHe != thisHe);
  return NULL;
}


/**
 @brief Returns the other HALF_EDGE for a 2D Facet.

 @details This function should only be used in 2D simulations and called from one of the two HALF_EDGEs
          that can be shared between facets. 

          Note: Unlike most of the other methods, this is not returning
          a HALF_EDGE on the same ring as this HALF_EDGE. Instead it is returning another HALF_EDGE in
          the associated FACET.

 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE,
    SHELL_HALF_EDGE)::get_opp_in_2d_facet() {
  stencil_assert(sim.is_2d());
  stencil_assert(this->is_2d_half_edge());
  return this->next_2d_half_edge();
}



/**
 @brief Returns TRUE if this HALF_EDGE is in contact with another in the ring.
 
 @details Checks if the half_edge's associated surfel has an opposite, and if so, determines if that
          opposite surfel contains a half_edge in the same ring. Returns true only if both conditions
          are true.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_edge_in_contact_pair() {
  if (this->IsBorder()) { return FALSE; }
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  asINT32 opp_surfel_id = thisHe->GetFacet()->get_opposite_surfel_id();
  // if surfel does not have opposite, opposite id = -1
  if (opp_surfel_id == -1) { return FALSE; }

  // make sure that the coupled surfel is also in the ring before returning true
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe->NextInRing();
  do {
    if (!loopingHe->IsBorder()) {
      if (opp_surfel_id == loopingHe->get_surfel()->id())
        return TRUE;
    }
      loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);
  return FALSE;
}


/**
 @brief Returns TRUE if this HALF_EDGE is in contact with another in the ring and the contact resistance = 0.
 
 @details Returns true only if the following conditions are met for both this half_edge and the half_edge it is
          in contact with: 
            - is_edge_in_contact_pair() is true 
            - contact resistance defined in the surfel's physics descriptor is fixed at 0.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::is_edge_in_0_res_contact_pair(BOOLEAN test_opp) {
  if (!this->is_edge_in_contact_pair()) { return FALSE; }

  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data = this->get_surfel()->dynamics_data();
  asINT32 dynamicsType = dyn_data->m_dynamics_type;
  if (dynamicsType == 0) {
    dynamicsType = convert_cdi_type_to_dyn_type(dyn_data);
  }

  // check contact resistance is fixed at 0
  if(dynamicsType == eDYN_SURFEL_TYPE::CONDUCTION_CONTACT_SURFEL_TYPE) {
    CONDUCTION_CONTACT_SURFEL_PHYSICS_DESCRIPTOR pd = 
        (CONDUCTION_CONTACT_SURFEL_PHYSICS_DESCRIPTOR)(dyn_data->physics_descriptor());

    if (fabs(pd->parameters()->contact_resistance.value) <= SDFLOAT_EPSILON
        && pd->parameters()->contact_resistance.is_constant()) {
      if (test_opp) {
        return this->get_opposite_facet_edge()->is_edge_in_0_res_contact_pair(FALSE);
      } else {
        return TRUE;
      }
    }
  }
  return FALSE;
}

/**
 @brief Returns TRUE if this and half_edge are associated with coupled surfels.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN,SHELL_HALF_EDGE)::are_edge_facets_opposites(SHELL_HE_TYPE half_edge) {
  if (this->IsBorder() || half_edge->IsBorder()) { return FALSE; }

  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  asINT32 opp_surfel_id = thisHe->GetFacet()->get_opposite_surfel_id();
  asINT32 nbr_surfel_id = half_edge->get_surfel()->id();
  return ((opp_surfel_id == nbr_surfel_id) && (nbr_surfel_id != -1));
}

/**
 @brief Returns the HALF_EDGE associated with the surfel in contact with this HALF_EDGE's surfel.

 @details Will return NULL if this HALF_EDGE's surfel does not have an opposite surfel, or the opposite surfel
          does not have a half_edge in this ring.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE,SHELL_HALF_EDGE)
    ::get_opposite_facet_edge() {
  if (this->IsBorder()) { return NULL; }

  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  asINT32 opp_surfel_id = thisHe->GetFacet()->get_opposite_surfel_id();
  // if surfel does not have opposite, return Null
  if (opp_surfel_id == -1) { return NULL; }

  // find edge of opposite surfel
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe->next_faceted_edge();
  do {
    if (opp_surfel_id == loopingHe->get_surfel()->id()) { return loopingHe; }
    loopingHe = loopingHe->next_faceted_edge();
  } while(loopingHe != thisHe);
  return NULL;
}

/**
 @brief Returns TRUE is layers in half_edge's surfel are aligned with the layers in this HALF_EDGE's surfel.

 @details This function takes into acount the differing orientations of open and closed shell surfels. Will only
          return TRUE is the edges have the correct orientation based on the shell types and they both have
          the same number of layers.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::are_layers_aligned(SHELL_HE_TYPE half_edge) {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  half_edge_stencil_assert(thisHe->is_shell_edge() && half_edge->is_shell_edge());
  if (!thisHe->equal_number_of_layers(half_edge)) { return FALSE; }
  
  BOOLEAN this_open_shell = this->GetFacet()->is_open_shell_facet();
  BOOLEAN nbr_open_shell = half_edge->GetFacet()->is_open_shell_facet();
  BOOLEAN edges_aligned = this->are_edges_aligned(half_edge);
  return (edges_aligned == (this_open_shell == nbr_open_shell));
}

/**
 @brief Returns the head or tail vertex index of this HALF_EDGE to be for use in determining alignment between edges

 @details Normally, two HALF_EDGES are not aligned if they share the same tail vertex index.
          However, some facet orientations have been flipped during the BREP::SHELL building process
          and therefore should be using their head vertex index in this comparison.
 */
BREP_DEFINE_HALF_EDGE_METHOD(iBREP_VERTEX, SHELL_HALF_EDGE)::get_vertex_ind_for_alignment() {
  return (this->GetFacet()->IsFlipped()) ? this->GetHeadVertex()->GetIndex()
                                          : this->GetTailVertex()->GetIndex();
}

/**
 @brief Returns TRUE if two edges are correctly aligned.

 @details For HALF_EDGEs of non-flipped facets, this is TRUE when their tail vertex indices are different
          but both are in the same ring. Due to flipped facets, the get_vertex_ind_for_alignement function is
          used to obtain the correct index for comparison.
          Note: this is only looking at geometric orientation of facets and does not incorporate
          the types of shells in the comparison like the are_layers_aligned function does.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::are_edges_aligned(SHELL_HE_TYPE half_edge) {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  half_edge_stencil_assert(thisHe->is_edge_in_ring(half_edge));
  
  return (thisHe->get_vertex_ind_for_alignment()
       != half_edge->get_vertex_ind_for_alignment());
}

/**
 @brief Returns the angle between this HALF_EDGE and half_edge.

 @details half_edge must be in this HALF_EDGE's ring. The sign of the angle is defined by the orientation of this
          HALF_EDGE's facet with positive angles representing rotation in the direction of the facet's normal.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::NT,
    SHELL_HALF_EDGE)::angle_btw_edges(SHELL_HE_TYPE half_edge) {

  cSHELL_MESH::HALF_EDGE root_edge = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::sBG_VECTOR3 root_edge_vec = get_shell_mesh()->HalfEdgeVec(root_edge);
  if (root_edge->GetFacet()->IsFlipped()) {
    root_edge_vec *= -1.0;
  }
  root_edge_vec.Normalize();

  cSHELL_MESH::sBG_VECTOR3 root_surfel_normal = get_shell_mesh()->GetFacetUnitNormal(root_edge->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 root_normal = BgCross(root_surfel_normal, root_edge_vec);

  SURFEL nbr_surfel = half_edge->get_surfel();
  cSHELL_MESH::sBG_VECTOR3 nbr_surfel_normal = get_shell_mesh()->GetFacetUnitNormal(half_edge->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 nbr_edge_vec = get_shell_mesh()->HalfEdgeVec(half_edge);
  if (half_edge->GetFacet()->IsFlipped()) {
    nbr_edge_vec *= -1.0;
  }

  cSHELL_MESH::sBG_VECTOR3 nbr_normal = BgCross(nbr_surfel_normal, nbr_edge_vec);

  cSHELL_MESH::sBG_VECTOR3 nbr_cross_root = BgCross(nbr_normal, root_normal);
  cSHELL_MESH::NT nbr_cross_root_dot_n = nbr_cross_root * root_edge_vec;
  cSHELL_MESH::NT nbr_dot_root = nbr_normal * root_normal;
  
  return -1.0*atan2(nbr_cross_root_dot_n, nbr_dot_root);
}

/**
 @brief Sets flag indicating that the HALF_EDGE has been processed by type identification algorithm.

 @details If all_in_ring = TRUE, it loops through all half edges in the ring and resets the unprocessed flag.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID,SHELL_HALF_EDGE)::set_processed(BOOLEAN all_in_ring) {
  m_info_flags.reset(HE_UNPROCESSED_IND);
  if (all_in_ring) {
    cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
    cSHELL_MESH::HALF_EDGE loopingHe = thisHe->NextInRing();
    do {
      loopingHe->set_processed();
      loopingHe = loopingHe->NextInRing();
    } while(loopingHe != thisHe);
  }
}

BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE,SHELL_HALF_EDGE)
    ::half_edge_in_ring_from_surf_id(SHOB_ID surfelId) {
  half_edge_stencil_assert(surfelId != STP_INVALID_SHOB_ID);
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;
  do {
    if (!loopingHe->IsBorder()) {
      if (loopingHe->get_surfel()->id() == surfelId) {
        return loopingHe;
      }
    }
    loopingHe = loopingHe->NextInRing();
  } while (loopingHe != thisHe);
  msg_print("Could not find surfel %u in half_edge ring\n%s", surfelId, this->print_detailed_ring_info().c_str());
  half_edge_stencil_assert(FALSE);
  return NULL;
}

BREP_DEFINE_HALF_EDGE_METHOD(VOID,SHELL_HALF_EDGE)::set_ring_types_from_ghost_info(sGHOST_HALF_EDGE_RING_INFO& gInfo) {
  for (auto& he_info : gInfo.half_edge_info) {
    cSHELL_MESH::HALF_EDGE half_edge = this->half_edge_in_ring_from_surf_id(he_info.half_edge_surfel_id);
    half_edge->m_info_flags = tBITSET<NUM_INFO_FLAGS,uint8_t>(he_info.info_flags);
    if (half_edge->is_adiabatic_boundary()) {
      continue;
    }
    cSHELL_MESH::HALF_EDGE nbr1 = this->half_edge_in_ring_from_surf_id(he_info.nbr_surfel_id_1);
    if (half_edge->nbr_he_is_pair()) {
      cSHELL_MESH::HALF_EDGE nbr2 = this->half_edge_in_ring_from_surf_id(he_info.nbr_surfel_id_2);
      half_edge->set_fixed_temp_boundary_edge_nbrs(nbr1, nbr2);
    } else {
      half_edge->m_neighbor_he = nbr1;
    }
  }
}

typedef enum : uINT32 {
  EDGE_INVALID_BC             = 0x0,
  EDGE_IS_ADIABATIC_BC        = 0x1,
  EDGE_IS_DYN_DATA_BC         = 0x2,
  EDGE_IS_DEFINED_TEMP_BC     = 0x4,
} EDGE_SURFEL_BC_TYPES;

/**
 @brief Returns the boundary type for the half_edge based on its dynamics data type.

 @details Here, contact and coupled dynamics are treated as defined temp BC's. If the surfel
          has dynamics data defining a flux, temp or HTC, the type is specified as a dynamics
          data BC with the specific type of dynamics data determined later during conduction
          edge creation.
 */
uINT32 get_flag_from_dyn_data(asINT32 dyn_type) {
  switch (dyn_type) {
    case CONDUCTION_ADIABATIC_SURFEL_TYPE:
      return EDGE_IS_ADIABATIC_BC;
    case CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
    case CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
    case CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
      return EDGE_IS_DYN_DATA_BC;
    case CONDUCTION_CONTACT_SURFEL_TYPE:
    case CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE:
      return EDGE_IS_DEFINED_TEMP_BC;
    default:
      msg_internal_error("Unknown dynamics type %d", dyn_type);
  }
  return EDGE_INVALID_BC;
}

/**
 @brief Checks properties of the half_edge's associated surfel to determine the type of BC this half_edge represents

 @details First checks if the surfel is a lrf, mlrf, mirror or non-conformal contact surfel. If so, the edge is
          treated as a fixed temp BC. If none of those criteria are met, the BC type is determined from the surfel's
          dynamics data.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32,SHELL_HALF_EDGE)::check_back_dynamics_data() {
  SURFEL surfel = this->GetFacet()->get_surfel();

  // should never need dynamics data from open shell
  stencil_assert(!surfel->is_conduction_open_shell());

  // add test here for non-conformal contact surfel and any others that should always be handled like a fixed temp BC
  if (surfel->is_lrf()) {// || surfel->is_mirror()) {
    // These are treated as fixed temperature boundaries
    return EDGE_IS_DEFINED_TEMP_BC;
  }
  auto dyn_type = this->get_back_dynamics_type();
  if (dyn_type == CONDUCTION_CONTACT_SURFEL_TYPE) {
    half_edge_stencil_assert(this->is_edge_in_0_res_contact_pair(FALSE));
  }
  return get_flag_from_dyn_data(dyn_type);
}

/**
 @brief Functor used by std::sort to sort a vector of HALF_EDGES in by location in an edge ring.

 @details The functor is specifically used to sort a vector of std::pair<HALF_EDGE,sdFLOAT> where
          sdFLOAT is the angle between a root half edge and the half edge stored in the pair. Methods are
          include when the surfels of HALF_EDGEs are overlapping, which use the alignment of the edges
          to determine the sorted order (with the edge representing the surfel with normal directed toward
          the root surfel being positioned after the other surfel when the angle is negative and vice
          versa). This ensures the coupled surfels have normals oriented away from each other in the
          sorted vector.
 */
class sortCoupledEdgeFunctor {
  typedef std::pair<cSHELL_MESH::HALF_EDGE,cSHELL_MESH::NT> EDGE_ANGLE_PAIR;
public:
  cSHELL_MESH::HALF_EDGE root_edge;

  sortCoupledEdgeFunctor(cSHELL_MESH::HALF_EDGE edge) : root_edge(edge) {};

  BOOLEAN points_toward_root(EDGE_ANGLE_PAIR &edge_angle) const {
    return (edge_angle.first->get_vertex_ind_for_alignment()
        == root_edge->get_vertex_ind_for_alignment());
  }

  BOOLEAN is_root(EDGE_ANGLE_PAIR edge_angle) const {
    return (edge_angle.first == root_edge);
  }

  BOOLEAN sort_coupled(EDGE_ANGLE_PAIR &a, EDGE_ANGLE_PAIR &b) const {
    // if a is the root edge, return TRUE
    // if b is the root edge, return FALSE
    if (is_root(a)) {
      return FALSE;
    } else if (is_root(b)) {
      return TRUE;
    }

    // Otherwise, we need to check angles to determine which should come first
    // if < 0, smaller is edge associated with facet pointing away from root facet
    if (a.second < 0.0) {
      if (points_toward_root(a)) {
        stencil_assert(!(points_toward_root(b)));
        return FALSE;
      } else {
        stencil_assert(points_toward_root(b));
        return TRUE;
      }
    }

    if (points_toward_root(a)) {
      stencil_assert(!(points_toward_root(b)));
      return FALSE;
    }
    stencil_assert(points_toward_root(b));

    return TRUE;
  }

  BOOLEAN operator()(EDGE_ANGLE_PAIR &a, EDGE_ANGLE_PAIR &b) const {
    // if edges are coupled or angles are equal call sort_coupled.
    // if (a.first->are_edge_facets_opposites(b.first) || (a.second == b.second)) {
    if (a.first->are_edge_facets_opposites(b.first) || (fabs(a.second - b.second) < SDFLOAT_EPSILON)) {
        return sort_coupled(a,b);
    }
    // otherwise sort by angles
    return (a.second < b.second);
  }
};

/**
 @brief Fills the hes vector provided to the function with all half_edges.

 @details This function does not perform any sorting before adding the half_edges to the vector.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::get_edge_vector(HALF_EDGE_VECTOR& hes, BOOLEAN exclude_root) {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;
  hes.clear();
  if (exclude_root) {
    loopingHe = loopingHe->NextInRing();
    half_edge_stencil_assert(loopingHe != thisHe);
  }

  do {
    if (!loopingHe->IsBorder()) {
      hes.push_back(loopingHe);
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);
}

/**
 @brief Fills the provided vector with a sorted list of HALF_EDGES found in the edge ring, returning the index
        of this HALF_EDGE in the vector.

 @details Uses sortCoupledEdgeFunctor to sort the vector. See comments in sortCoupledEdgeFunctor for more information
          about the sorting criteria.
 */
BREP_DEFINE_HALF_EDGE_METHOD(asINT32, SHELL_HALF_EDGE)::get_sorted_edges(HALF_EDGE_VECTOR& half_edges) {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;

  EDGE_ANGLE_PAIR_VECTOR half_edges_and_angles;
  auto areEdgesOpposites = [&loopingHe](const EDGE_ANGLE_PAIR& x) {
    return loopingHe->are_edge_facets_opposites(x.first);
  };

  do {
    if (!loopingHe->IsBorder()) {
      const auto it = std::find_if(half_edges_and_angles.begin(), half_edges_and_angles.end(), areEdgesOpposites);
      if (it != half_edges_and_angles.end()) {
        half_edges_and_angles.push_back(EDGE_ANGLE_PAIR(loopingHe,it->second));
      } else {
        NT angle = thisHe->angle_btw_edges(loopingHe);
        half_edges_and_angles.push_back(EDGE_ANGLE_PAIR(loopingHe,angle));
      }
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);

  std::sort(half_edges_and_angles.begin(), half_edges_and_angles.end(), sortCoupledEdgeFunctor(thisHe));

  half_edges.resize(half_edges_and_angles.size());
  asINT32 root_index = -1;
  ccDOTIMES(i, half_edges_and_angles.size()) {
    half_edges[i] = half_edges_and_angles[i].first;
    if (half_edges[i] == thisHe) {
      root_index = i;
    }
  }
  if (root_index == -1) {
    msg_internal_error("root edge not found in sorted edge-angle vector");
  }
  return root_index;
}

/**
 @brief Returns the internal edge neighbor stored in m_neighbor_he.

 @details Throws assertion error if the half_edge has not been labeled as an internal edge.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, SHELL_HALF_EDGE)
    ::get_internal_edge_nbr() {
  stencil_assert(this->is_internal_edge());
  return m_neighbor_he;
}

/**
 @brief Returns either this half_edge or the neighbor stored in m_neighbor_he depending on the surfel indices.

 @details Throws assertion error if the half_edge has not been labeled as an internal edge.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, SHELL_HALF_EDGE)
    ::get_internal_edge_owner() {
  stencil_assert(this->is_internal_edge());
  if (this->get_surfel()->id() < m_neighbor_he->get_surfel()->id()) {
    return this;
  }
  return m_neighbor_he;
}

/**
 @brief Returns the boundary edge neighbor stored in m_neighbor_he.

 @details Throws assertion error if the half_edge has been labeled internal or if m_neighbor_he has not
          been set.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, SHELL_HALF_EDGE)
    ::get_boundary_or_intersection_edge_nbr() {
  half_edge_stencil_assert(this->is_fixed_temp_boundary() || this->is_dyn_data_boundary() 
                        || this->is_intersection_edge());
  if (this->nbr_he_is_pair()) {
    return this->get_first_edge_nbr_in_pair();
  }
  return m_neighbor_he;
}

/**
 @brief Returns the first boundary edge neighbor stored in m_neighbor_pair.

 @details Throws assertion error if the flag designating that the boundary neighbor info is stored as a pair
          is not set.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, SHELL_HALF_EDGE)
    ::get_first_edge_nbr_in_pair() {
  half_edge_stencil_assert(this->nbr_he_is_pair());
  return m_neighbor_pair->first;
}

/**
 @brief Returns the second boundary edge neighbor stored in m_neighbor_pair.

 @details Throws assertion error if the flag designating that the boundary neighbor info is stored as a pair
          is not set.
 */
BREP_DEFINE_HALF_EDGE_METHOD(BREP_HALF_EDGE_TYPENAME(SHELL_HALF_EDGE)::HALF_EDGE, SHELL_HALF_EDGE)
    ::get_second_edge_nbr_in_pair() {
  half_edge_stencil_assert(this->nbr_he_is_pair());
  return m_neighbor_pair->second;
}

/**
 @brief Returns the surfel id of the surfel associated with m_neighbor_he, or the first half_edge in m_neighbor_pair

 @details Returns STP_INVALID_SHOB_ID for adiabatic edges
 */
BREP_DEFINE_HALF_EDGE_METHOD(SHOB_ID, SHELL_HALF_EDGE)::get_nbr_he1_surfel_id() {
  if (this->nbr_he_is_pair()) {
    return this->get_first_edge_nbr_in_pair()->get_surfel()->id();
  } else if (this->is_adiabatic_boundary()) {
    return STP_INVALID_SHOB_ID;
  }
  return m_neighbor_he->get_surfel()->id();
}

/**
 @brief Returns the surfel id of the surfel associated with the second half_edge in m_neighbor_pair

 @details Returns STP_INVALID_SHOB_ID for any edge that does not store nbrs in pair
 */
BREP_DEFINE_HALF_EDGE_METHOD(SHOB_ID, SHELL_HALF_EDGE)::get_nbr_he2_surfel_id() {
  if (this->nbr_he_is_pair()) {
    return this->get_second_edge_nbr_in_pair()->get_surfel()->id();
  }
  return STP_INVALID_SHOB_ID;
}

BREP_DEFINE_HALF_EDGE_METHOD(dFLOAT, SHELL_HALF_EDGE)::get_int_edge_rotation(BOOLEAN skip_assert) {
  if (!skip_assert) {
    half_edge_stencil_assert(m_int_edge_rotation > -1e9);
  }
  return m_int_edge_rotation;
}

BREP_DEFINE_HALF_EDGE_METHOD(dFLOAT*, SHELL_HALF_EDGE)::get_int_edge_unfold_vec(BOOLEAN skip_assert) {
  if (!skip_assert) {
    half_edge_stencil_assert(m_int_edge_unfolded_vec[0] > -1e9);
  }
  return m_int_edge_unfolded_vec;
}


/**
 @brief Frees the memory allocated for m_neighbor_pair when it is no longer needed.

 @details Throws assertion error if the flag designating that the boundary neighbor info is stored as a pair
          is not set.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::delete_nbr_pair() {
  half_edge_stencil_assert(this->nbr_he_is_pair());
  m_info_flags.reset(NBR_HE_IS_PAIR_IND);
  delete m_neighbor_pair;
  m_neighbor_pair = NULL;
}

/**
 @brief Stores edge_nbr in m_neighbor_he variable and sets flags indicating this half edge is an internal edge.

 @details If edge has already been set as internal (which happens if edge_nbr was processed before this half_edge),
          the function will assert that the m_neighbor_he variables in both edges point to each other. 
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_internal_edge_nbr(SHELL_HE_TYPE edge_nbr) {
  if (this->get_surfel()->is_mirror() || edge_nbr->get_surfel()->is_mirror()) {
    this->set_as_adiabatic_boundary_edge();
    edge_nbr->set_as_adiabatic_boundary_edge();
    return;
  }
  if (this->edge_nbr_is_set()) {
    half_edge_stencil_assert(this->is_internal_edge());
    half_edge_stencil_assert(edge_nbr->is_internal_edge() && (edge_nbr == this->get_internal_edge_nbr()));
    return;
  }
  m_neighbor_he = edge_nbr;
  m_info_flags.set(NBR_HE_INTERNAL_IND);
  edge_nbr->set_internal_edge_nbr(this);
}

/**
 @brief Stores edge_nbr in m_neighbor_he variable and sets flags indicating this half edge is an intersection.

 @details If edge has already been set (which happens if edge_nbr was processed before this half_edge),
          the function will assert that the edge_nbr has already been flagged as an intersection.
          If set_nbr_also is TRUE, it does the same thing for edge_nbr. It is assumed that when this function
          is called directly, there are only two half_edges in the intersection. Therefore, set_nbr_also defaults
          to TRUE so that the m_neighbor_he variables point to each other. When called by set_intersection_edge_nbrs
          function (defined next) this variable is set to FALSE to allow for the m_neighbor_he
          variables in the half_edges to point to each other cyclically.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)
    ::set_intersection_edge_nbr(SHELL_HE_TYPE edge_nbr, BOOLEAN set_nbr_also) {
  if (this->edge_nbr_is_set()) {
    half_edge_stencil_assert(this->is_intersection_edge());
    half_edge_stencil_assert(edge_nbr->is_intersection_edge());
    return;
  }
  half_edge_stencil_assert(!(this->get_surfel()->is_mirror() || edge_nbr->get_surfel()->is_mirror()));
  m_neighbor_he = edge_nbr;
  m_info_flags.set(NBR_HE_INTERSECTION_IND);
  if (set_nbr_also) {
    edge_nbr->set_intersection_edge_nbr(this, FALSE);
  }
}

/**
 @brief Creates cyclical references to each half_edge in edge_nbrs vector by setting m_neighbor_he variables and
        sets the flags indicating they are an intersection.

 @details If an edge has already been set (which happens if edge_nbr was processed before this half_edge),
          the function will assert that this edge and edge_nbr both have already been flagged as intersections.
          Sets cyclical references by calling edge_nbrs[(i-1)%edge_nbrs.size()]->set_intersection_edge_nbr(edge_nbrs[i],FALSE)

          In the event that there are mirror surfels at the edge, they will be excluded from the intersection edge
          under the assumption that all surfels sharing this edge have a mirror surfel to be excluded.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_intersection_edge_nbrs(HALF_EDGE_VECTOR& edge_nbrs) {
  SHELL_HE_TYPE thisHe = (SHELL_HE_TYPE)this;

  // This may be called from a half edge that is also included in edge_nbrs. In that case, its possible that
  // this half edge should be treated as an internal edge and not an intersection.
  if (edge_nbrs.size() == 2) {
    if ((thisHe == edge_nbrs[0]) || (thisHe == edge_nbrs[1])) {
      return edge_nbrs[0]->set_internal_or_intersection_edge_nbr(edge_nbrs[1]);
    }
  }

  half_edge_stencil_assert(!this->get_surfel()->is_mirror());
  SHELL_HE_TYPE cur_edge = thisHe;
  for (auto edge_nbr : edge_nbrs) {
    if (edge_nbr == thisHe || edge_nbr->get_surfel()->is_mirror()) {
      continue;
    }
    cur_edge->set_intersection_edge_nbr(edge_nbr,FALSE);
    cur_edge = edge_nbr;
  }
  half_edge_stencil_assert(cur_edge != thisHe);
  cur_edge->set_intersection_edge_nbr(thisHe, FALSE);
}

/**
 @brief Sets the edge between this half_edge and edge_nbr based on the number of layers in each surfel
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_internal_or_intersection_edge_nbr(SHELL_HE_TYPE edge_nbr) {
  if (this->equal_number_of_layers(edge_nbr)) {
    this->set_internal_edge_nbr(edge_nbr);
  } else {
    this->set_intersection_edge_nbr(edge_nbr);
  }
}

/**
 @brief Sets flags indicating that the edge is adiabatic.

 @details Does not store a half_edge in m_neighbor_he.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_as_adiabatic_boundary_edge() {
  if (this->edge_nbr_is_set()) {
    stencil_assert(this->is_adiabatic_boundary());
    return;
  }
  m_info_flags.set(NBR_HE_ADIABATIC_BND_IND);
  m_neighbor_he = NULL;
}

/**
 @brief Resets all non border half edges to adiabatic.

 @details Function called in the event that the edge is malformed and edge types cannot be identified properly.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::reset_all_to_adiabatic_boundary_edges() {
  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;
  do {
    if (!loopingHe->IsBorder()) {
      loopingHe->reset_to_adiabatic_boundary_edge();
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);
}

/**
 @brief Resets all half edge to be an adiabatic type.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::reset_to_adiabatic_boundary_edge() {
  if (this->nbr_he_is_pair()) {
    this->delete_nbr_pair();
  } else {
    m_neighbor_he = NULL;
  }
  m_info_flags.reset_all();
  m_info_flags.set(NBR_HE_ADIABATIC_BND_IND);
}

/**
 @brief Stores edge_nbr in m_neighbor_he variable and sets flags indicating this half edge is a dyn data BC.

 @details This does not check that the edge has already been set since the neighbor is a non-shell edge and therefore
          the neighbor does not get set directly. (There is the condition where this edge and the neighbor are
          open and closed shell surfels with layers or vice versa, but in that case, the layers will not interact
          if the closed shell surfel has a BC defined on it).
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_dyn_data_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr) {
  m_neighbor_he = edge_nbr;
  m_info_flags.set(NBR_HE_DYN_DATA_BND_IND);
}

/**
 @brief Stores edge_nbr in m_neighbor_he variable and sets flags indicating this half edge is a Fixed Temp BC.

 @details This does not check that the edge has already been set since the neighbor is a non-shell edge and therefore
          the neighbor does not get set directly. (There is the condition where this edge and the neighbor are
          open and closed shell surfels with layers or vice versa, but in that case, the layers will not interact
          if the closed shell surfel has a BC defined on it).
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_fixed_temp_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr) {
  m_neighbor_he = edge_nbr;
  m_info_flags.set(NBR_HE_FIXED_TEMP_BND_IND);
}

/**
 @brief Allocates pair containing both nbr half_edges and stores in in m_neighbor_pair.

 @details Additionally, sets flags indicating that this is a fixed_temp bc edge and that there are two
          neighbors stored as a pair in m_neighbor_pair.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_fixed_temp_boundary_edge_nbrs(SHELL_HE_TYPE nbr_he1,
                                                                                       SHELL_HE_TYPE nbr_he2) {
  this->m_neighbor_pair = new FIXED_TEMP_NBR_PAIR(nbr_he1, nbr_he2);
  m_info_flags.set(NBR_HE_FIXED_TEMP_BND_IND);
  m_info_flags.set(NBR_HE_IS_PAIR_IND);
}

/**
 @brief Checks the type of surfel associated with edge_nbr and calls the appropriate set_XXX_boundary_edge_nbr function
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_boundary_edge_nbr(SHELL_HE_TYPE edge_nbr) {
  if (edge_nbr == NULL) {
    return this->set_as_adiabatic_boundary_edge();
  }
  uINT32 edge_nbr_type = edge_nbr->check_back_dynamics_data();
  
  half_edge_stencil_assert(edge_nbr_type != EDGE_INVALID_BC);

  switch (edge_nbr_type) {
    case EDGE_IS_ADIABATIC_BC:
      return this->set_as_adiabatic_boundary_edge();
    case EDGE_IS_DYN_DATA_BC:
      return this->set_dyn_data_boundary_edge_nbr(edge_nbr);
    case EDGE_IS_DEFINED_TEMP_BC:
      return this->set_fixed_temp_boundary_edge_nbr(edge_nbr);
    default:
      msg_internal_error("Unknown edge nbr type %u", edge_nbr_type);
  }
}


/**
 @brief Sets this half_edge as the boundary neighbor with type edge_type to all shell surfels in vector
*/
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::set_as_boundary_edge_nbr_type(HALF_EDGE_VECTOR& shell_nbrs,
                                                                                   uINT32 this_edge_type) {
  if (this_edge_type == EDGE_INVALID_BC) {
    this_edge_type = this->check_back_dynamics_data();
  }
  half_edge_stencil_assert(this_edge_type != EDGE_INVALID_BC);

  // Since shell_nbrs contains open shell surfels and this half_edge can belong to a closed surfel containing
  // shell layers, we pass set_nbr_also = FALSE to avoid setting the closed shell surfel as a boundary surfel when it will
  // have its own conduction edge type defined by its neighbors.
  for (auto shell_edge : shell_nbrs) {
    switch (this_edge_type) {
      case EDGE_IS_ADIABATIC_BC:
        return shell_edge->set_as_adiabatic_boundary_edge();
      case EDGE_IS_DYN_DATA_BC:
        return shell_edge->set_dyn_data_boundary_edge_nbr(this);
      case EDGE_IS_DEFINED_TEMP_BC:
        return shell_edge->set_fixed_temp_boundary_edge_nbr(this);
      default:
        msg_internal_error("Unknown edge nbr type %u", this_edge_type);
    }
  }
}

/**
 @brief Returns TRUE if edge2 and this HALF_EDGE can be combined into a single edge in the simplified mesh

 @details If edge2 and this HALF_EDGE are collinear, and have the same neighboring facets in their rings
          the edges can be combined into a single edge, so it returns TRUE. This function reduces can
          eliminate the additional unneeded vertices added to the facet during the discretization process.
          As a result, two neighboring facets should share at most 1 HALF_EDGE.

          An additional reduction in edges is made when generating the conduction edges, which combines any
          Colinear adiabatic edges or colinear dynamic data edges which obtain the physics descriptor from the same
          face. This comparison is performed by calling function with use_edge_type_info = TRUE (defaults to FALSE)
 */
BREP_DEFINE_HALF_EDGE_METHOD(BOOLEAN, SHELL_HALF_EDGE)::can_edges_be_combined(SHELL_HE_TYPE edge2,
                                                                              BOOLEAN use_edge_type_info) {
  // Check that they form a straight line
  cSHELL_MESH::sBG_VECTOR3 edge_vec1 = get_shell_mesh()->HalfEdgeVec((cSHELL_MESH::HALF_EDGE)this);
  cSHELL_MESH::sBG_VECTOR3 edge_vec2 = get_shell_mesh()->HalfEdgeVec(edge2);
  if (!edge_vec1.IsCollinear(edge_vec2,1e-6)) {
    return FALSE;
  }

  // We will loop through and make sure edges share the same facets regardless of order.
  // Any facets that will be excluded from final mesh (non-shell surfels not bordering a shell surfel)
  // will be excluded
  if (use_edge_type_info) {
    if (this->is_adiabatic_boundary() && edge2->is_adiabatic_boundary()) {
      return TRUE;      
    }
    if (this->is_dyn_data_boundary() && edge2->is_dyn_data_boundary()) {
      stencil_assert(!this->nbr_he_is_pair() && !edge2->nbr_he_is_pair())
      asINT32 thisNbrFace = this->get_boundary_or_intersection_edge_nbr()->surfel_face_id();
      asINT32 otherNbrFace = edge2->get_boundary_or_intersection_edge_nbr()->surfel_face_id();
      if (thisNbrFace == otherNbrFace) {
        return TRUE;
      }
    }
    return FALSE;
  }

  {
    BREP_EDGE_DO_INCIDENT_FACETS(facet, this, cSHELL_MESH) {
      if (facet->marked_to_keep()) {
        if (edge2->FindFacetInRing(facet->GetIndex()) == NULL) {
          return FALSE;
        }
      }
    }
  }

  {
    BREP_EDGE_DO_INCIDENT_FACETS(facet, edge2, cSHELL_MESH) {
      if (facet->marked_to_keep()) {
        if (this->FindFacetInRing(facet->GetIndex()) == NULL) {
          return FALSE;
        }
      }
    }
  }
  return TRUE;
}

/**
 @brief Returns the skewness of the half edge if it defines a boundary.

 @details Defined as the length of the vector from the edge center to the location a line drawn from the surfel
          centroid in the edge's normal direction intersects the edge normalized by half the length of the edge 
          The normalization factor is actually the distance from the edge center to its vertex closest to the
          intersection point, which will always be half the length of the edge. This should not be used to calculate the
          skewness of an internal edge. Instead, use the skewness function.
 */
BREP_DEFINE_HALF_EDGE_METHOD(sdFLOAT, SHELL_HALF_EDGE)::boundarySkewness() {
  SURFEL surfel = this->GetFacet()->get_surfel();
  cSHELL_MESH::sBG_VECTOR3 he_vec = get_shell_mesh()->HalfEdgeVec(this);

  // calculate normal vector
  cSHELL_MESH::sBG_VECTOR3 surf_normal = get_shell_mesh()->GetFacetUnitNormal(this->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 edge_normal = BgCross(he_vec, surf_normal);
  edge_normal.Normalize();

  // verify normal is pointing out of facet
  cSHELL_MESH::sBG_POINT3 surf_centroid = get_shell_mesh()->GetFacetCentroid(this->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 centroid_to_tail_vec = this->GetVertex()->GetPoint() - surf_centroid;
  if ((edge_normal * centroid_to_tail_vec) < 0.0) {
    edge_normal *= -1.0;
  }

  cSHELL_MESH::sBG_POINT3 edge_midpt = get_shell_mesh()->HalfEdgeMidPoint(this);
  cSHELL_MESH::sBG_VECTOR3 vec_to_edge_midpt = edge_midpt - surf_centroid;

  cSHELL_MESH::sBG_VECTOR3 dvec(edge_normal);
  dvec *= edge_normal * vec_to_edge_midpt;
  cSHELL_MESH::sBG_POINT3 intersection = surf_centroid + dvec;
  cSHELL_MESH::sBG_VECTOR3 centerToInt = intersection - edge_midpt;
  auto magCenterToInt = centerToInt.Length();
  return (sdFLOAT)(magCenterToInt / (he_vec.Length()+SDFLOAT_EPSILON));
}


/**
 @brief Returns the skewness of the half edge if it is internal.

 @details Defined as the length of the vector from the edge center to the intersection of the edge and the line
          between surfel centroids (which is represented by the unfolded centroid) normalized by half the length of the edge. 
          The normalization factor is actually the distance from the edge center to its vertex closest to the
          intersection point, which will always be half the length of the edge. This should not be used to calculate the
          skewness of a boundary edge. Instead, use boundarySkewness.
 */
BREP_DEFINE_HALF_EDGE_METHOD(sdFLOAT, SHELL_HALF_EDGE)::skewness(const dFLOAT unfolded_vec[2]) {
  SURFEL surfel = this->GetFacet()->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA surfel_shell_data = surfel->shell_conduction_data();
  dFLOAT global_unfolded[3];
  surfel_shell_data->rotate_to_global_coordinates(global_unfolded, unfolded_vec);
  cSHELL_MESH::sBG_VECTOR3 vecToNbr(global_unfolded[0],global_unfolded[1],global_unfolded[2]);

  // Get edge normal to be used to construct plane containing edge
  cSHELL_MESH::sBG_VECTOR3 he_vec = get_shell_mesh()->HalfEdgeVec(this);
  cSHELL_MESH::sBG_VECTOR3 surf_normal = get_shell_mesh()->GetFacetUnitNormal(this->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 edge_normal = BgCross(he_vec, surf_normal);
  edge_normal.Normalize();

  // construct plane containing half_edge (intersecting with a plane defining the edge rather than a segment or line
  // to avoid any precision issues that may result in no intersection found)
  cSHELL_MESH::sBG_POINT3 edge_midpt = get_shell_mesh()->HalfEdgeMidPoint(this);
  cSHELL_MESH::sBG_PLANE3 edge_plane(edge_midpt, edge_normal);

  cSHELL_MESH::sBG_POINT3 surf_centroid = get_shell_mesh()->GetFacetCentroid(this->GetFacet()->GetIndex());
  cSHELL_MESH::sBG_LINE3 lineToNbr(surf_centroid, vecToNbr);
  cSHELL_MESH::sBG_POINT3 intPoint;
  cSHELL_MESH::sBG_LINE3 intLine;

  eBG_RETURN_TYPE intType = BgIntersection(edge_plane, lineToNbr, intPoint, intLine);
  half_edge_stencil_assert(intType == BG_RT_POINT3);
  cSHELL_MESH::sBG_VECTOR3 edgeCenterToInt = intPoint - edge_midpt;
  cSHELL_MESH::NT distToInt = edgeCenterToInt.Length();

  cSHELL_MESH::NT edgeHalfLen = he_vec.Length()/2.;
  half_edge_stencil_assert(edgeHalfLen > DFLOAT_EPSILON);

  return (sdFLOAT)(distToInt/edgeHalfLen);
}


#ifdef DEBUG_BREP_STENCIL
std::string cdiTypeStringFromValue(asINT32 value) {
  switch (value) {
    case CDI_PHYS_TYPE_ADIABATIC:
      return "CDI_PHYS_TYPE_ADIABATIC";
    case CDI_PHYS_TYPE_FIXED_TEMP:
      return "CDI_PHYS_TYPE_FIXED_TEMP";
    case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
      return "CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP";
    case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
      return "CDI_PHYS_TYPE_FIXED_HEAT_FLUX";
    case CDI_PHYS_TYPE_CONTACT_RESISTANCE:
      return "CDI_PHYS_TYPE_CONTACT_RESISTANCE";
    case CDI_PHYS_TYPE_COUPLED_THERMAL:
      return "CDI_PHYS_TYPE_COUPLED_THERMAL";
    default:
      return fmt::format("UNKNOWN CDI TYPE {}",value);
  }
}

std::string dynTypeStringFromValue(uINT32 value) {
  switch (value) {
    case CONDUCTION_ADIABATIC_SURFEL_TYPE:
      return "CONDUCTION_ADIABATIC_SURFEL_TYPE";
    case CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
      return "CONDUCTION_FIXED_TEMP_SURFEL_TYPE";
    case CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
      return "CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE";
    case CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
      return "CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE";
    case CONDUCTION_CONTACT_SURFEL_TYPE:
      return "CONDUCTION_CONTACT_SURFEL_TYPE";
    case CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE:
      return "CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE";
    default:
      return fmt::format("UNKNOWN DYN TYPE {}",value);
  }
}

/**
 @brief Prints detailed information about surfels associated with half_edges in ring along with position information
        of half-edges around ring.

  @details This will likely be removed once the algorithm has been sufficiently tested.
 */
BREP_DEFINE_HALF_EDGE_METHOD(std::string, SHELL_HALF_EDGE)
    ::print_associated_surfel_info_with_angles(NT angle, iBREP_VERTEX tail_vertex) {

  cSHELL_MESH::HALF_EDGE root_edge = (cSHELL_MESH::HALF_EDGE)this;
  half_edge_stencil_assert(!(root_edge->IsBorder()));
  SURFEL root_surfel = root_edge->get_surfel();

  iBREP_SHELL shellInd = root_edge->surfel_face_id();
  asINT32 surfelInd = root_surfel->id();
  asINT32 nLayers = 0;

  BOOLEAN is_aligned = (tail_vertex == this->GetHeadVertex()->GetIndex());
  BOOLEAN isShell = root_edge->is_shell_edge();
  BOOLEAN isCoupled = root_edge->is_coupled_cond_interface_edge();
  BOOLEAN isESurfel = root_edge->is_esurfel_edge();
  BOOLEAN isContactPair = root_edge->is_edge_in_contact_pair();
  BOOLEAN isZeroResContactPair = root_edge->is_edge_in_0_res_contact_pair();
  BOOLEAN isOpenShell = root_edge->is_open_shell_edge(); 
  BOOLEAN isClosedShell = isShell && !isOpenShell;

  if (isOpenShell) {
    return fmt::format("  OpenShell: ID {}, FID {}, Align {}, nLayers {}, Angle {}\n", surfelInd, shellInd,
        (is_aligned) ? "T" : "H", root_edge->num_layers_at_edge(), angle);
  }
  
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data = root_surfel->dynamics_data();
  CONDUCTION_PHYSICS_DESCRIPTOR phys_desc = (CONDUCTION_PHYSICS_DESCRIPTOR)(dyn_data->physics_descriptor());
  asINT32 dyn_index = phys_desc->index;
  uINT32 dynamicsType = dyn_data->m_dynamics_type;
  CDI_PHYS_TYPE_DESCRIPTOR cdi_desc = phys_desc->phys_type_desc;
  
  std::string retStr;
  if (isShell) {
    retStr += fmt::format("  ClosedShell W/Layers: ID {}, FID {}, Align {}, nLayers {}, Angle {}\n", surfelInd, shellInd,
      (is_aligned) ? "T" : "H", root_edge->num_layers_at_edge(), angle);
  } else {
    retStr += fmt::format("  ClosedShell: ID {}, FID {}, Align {}, Angle {}\n", surfelInd, shellInd,
      (is_aligned) ? "T" : "H", angle);
  }
  retStr += fmt::format("    CondInt {}, Sampled {}, zeroResContact {}, OppId {}, dynType {}, dynInd {}, cdiID {}\n", root_surfel->is_conduction_interface(),
      isCoupled, isZeroResContactPair, (isZeroResContactPair) ? root_edge->GetFacet()->get_opposite_surfel_id() : -1, dynTypeStringFromValue(dynamicsType),
      dyn_index, cdiTypeStringFromValue(cdi_desc->cdi_physics_type));
  
  return retStr;
}


/**
 @brief Prints detailed information about half_edges in the ring and their associated surfels.
 
 @details This will likely be removed once the algorithm has been sufficiently tested.
 */
BREP_DEFINE_HALF_EDGE_METHOD(std::string, SHELL_HALF_EDGE)::print_detailed_ring_info(BOOLEAN skipSimple) {
  half_edge_stencil_assert(!(this->IsBorder()));

  cSHELL_MESH::HALF_EDGE thisHe = (cSHELL_MESH::HALF_EDGE)this;
  cSHELL_MESH::HALF_EDGE loopingHe = thisHe;

  EDGE_ANGLE_PAIR_VECTOR half_edges_and_angles;
  auto areEdgesOpposites = [&loopingHe](const EDGE_ANGLE_PAIR& x) {
    return loopingHe->are_edge_facets_opposites(x.first);
  };
 
  asINT32 firstFaceId = thisHe->surfel_face_id();
  BOOLEAN diffFaceIds = FALSE;
  do {
    if (!loopingHe->IsBorder()) {

      diffFaceIds |= (firstFaceId != loopingHe->surfel_face_id());

      const auto it = std::find_if(half_edges_and_angles.begin(), half_edges_and_angles.end(), areEdgesOpposites);
      if (it != half_edges_and_angles.end()) {
        half_edges_and_angles.push_back(EDGE_ANGLE_PAIR(loopingHe,it->second));
      } else {
        NT angle = thisHe->angle_btw_edges(loopingHe);
        half_edges_and_angles.push_back(EDGE_ANGLE_PAIR(loopingHe,angle));
      }
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != thisHe);

  // Rings with 1 or 2 edges which are associated with contact surfels and have the same face id are considered simple
  if (skipSimple && (this->num_faceted_edges() < 3) && !(diffFaceIds || this->is_edge_in_contact_pair())) {
    return "";
  }

  std::sort(half_edges_and_angles.begin(), half_edges_and_angles.end(), sortCoupledEdgeFunctor(thisHe));
  iBREP_VERTEX tail_vert = this->get_vertex_ind_for_alignment();


  asINT32 root_index = -1;
  std::vector<int> surfelsInRing(half_edges_and_angles.size());
  ccDOTIMES(i, half_edges_and_angles.size()) {
    cSHELL_MESH::HALF_EDGE curHalfEdge = half_edges_and_angles[i].first;
    surfelsInRing[i] = curHalfEdge->get_surfel()->id();
    if (curHalfEdge == thisHe) {
      root_index = i;
    }
  }
  
  DGF_VERTEX_INDEX vInd1, vInd2;
  this->global_vertex_indices(vInd1, vInd2);
  
  std::string printStr = fmt::format("HalfEdge: Vertices ({}, {}), nSurfels {}, Surfels [{}]\n",vInd1,vInd2,
      surfelsInRing.size(), fmt::join(surfelsInRing, ", "));
  ccDOTIMES(i, half_edges_and_angles.size()) {
    asINT32 curInd = (i+root_index) % half_edges_and_angles.size();
    EDGE_ANGLE_PAIR& curPair = half_edges_and_angles[curInd];
    printStr += curPair.first->print_associated_surfel_info_with_angles(curPair.second*180./M_PI, tail_vert);
  }
  return printStr + "\n\n";
}

#endif

/**
 @brief Overload of DEFAULT_HALF_EDGE Print method that prints additional info related to the surfel.
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::Print(FILE *stream) {
  BOOLEAN isBE = FALSE;
  BOOLEAN isMisoriented = FALSE;
  BOOLEAN isNM = FALSE;
  BOOLEAN isPHe = FALSE;

  if(this->IsBorder())
    isBE = TRUE;

  if(this->BelongsToRing())
    isMisoriented = TRUE;

  if(this->IsNonManifold())
    isNM = TRUE;

  if(this->BelongsToPartition())
    isPHe = TRUE;

  iBREP_VERTEX tailVIndex = this->GetTailVertex()->GetIndex();
  iBREP_VERTEX headVIndex = this->GetHeadVertex()->GetIndex();

  iBREP_FACET facetIndex = (isBE) ? -1 : this->GetFacet()->GetIndex();
  iBREP_SHELL shellInd = (isBE) ? -1 : this->surfel_face_id();
  asINT32 surfelInd = (isBE) ? -1 : this->GetFacet()->get_surfel()->id();

#ifdef BREP_DEBUG
  msg_print("h [ %lld ] | f[ %d ] : v[ %d ] v[ %d ] be %d, er %d, nm %d p %d",
	  GetIndex(), facetIndex, tailVIndex, headVIndex, isBE, isMisoriented, isNM, isPHe);
#else
  msg_print("h [ %p ] | f[ %d ] s[ %d (%d) ] : v[ %d ] v[ %d ] be %d, er %d, nm %d p %d",
	  this->GetOpposite()->GetOpposite(), facetIndex, surfelInd, shellInd, tailVIndex, headVIndex,
    isBE, isMisoriented, isNM, isPHe );
  msg_print("\tNormal (%f, %f), Length %f, Normal Distance %f", this->local_normal()[0],
    this->local_normal()[1], this->length(), this->normal_distance());
	
#endif
}

/**
 @brief Obtains the global indices for this half_edge's head and tail vertices.

 @details Used when writing BREP information to file in order to create vtk files of mesh. This
          function should only be called when g_pfc_output_shell_mesh_info = 1
 */
BREP_DEFINE_HALF_EDGE_METHOD(VOID, SHELL_HALF_EDGE)::global_vertex_indices(DGF_VERTEX_INDEX& vInd1,
                                                                           DGF_VERTEX_INDEX& vInd2) {
  vInd1 = g_surfel_vertices_info.map_local_to_global_index(this->GetTailVertex()->GetIndex());
  vInd2 = g_surfel_vertices_info.map_local_to_global_index(this->GetHeadVertex()->GetIndex());
}


/**
 @brief Adds surfel vertices to cSHELL_MESH BREP object.

 @details In 2D simulations a second set of vertices offset by 1 LatticeLength Unit in z direction
          are added as well.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::add_vertices() {
  ccDOTIMES(j, g_surfel_vertices_info.m_n_surfel_vertices) {
    SIM_VERTEX vertex = &g_surfel_vertices_info.m_surfel_vertices[j];
    this->AddVertex(vertex->coord[0], vertex->coord[1], vertex->coord[2]);
  }
  // Need to add extra vertices for 2d simulations to be able to create facets
  // shifted vertices will be at index + vertex_offset_2d()
  if (sim.is_2d()) {
    m_vertex_index_offset = this->NumVertices();
    ccDOTIMES(j, g_surfel_vertices_info.m_n_surfel_vertices) {
      SIM_VERTEX vertex = &g_surfel_vertices_info.m_surfel_vertices[j];
      this->AddVertex(vertex->coord[0], vertex->coord[1], vertex->coord[2]+1.0);
    }
  }
}

/**
 @brief Adds Shells to initial cSHELL_MESH object that correspond to the indices of faces in simulation.

 @details In simplified mesh, facets to be kept are added one face at a time (skipping any Shells not
          containing any facets marked to keep) and the Shell indices will no longer correspond to
          face indices as a result.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::add_faces() {
  iBREP_BODY ibody = this->NewBody();
  cSHELL_MESH::BODY m_body = this->GetBody(ibody);
  ccDOTIMES(face_index,g_num_faces) {
    iBREP_SHELL ishell = this->NewShell(BREP_INVALID_IFACET);
    cSHELL_MESH::SHELL shell = this->GetShell(ishell);
    m_body->AddShell(shell);
  }
}

/**
 @brief Adds Facet corresponding to a surfel to initial cSHELL_MESH.

 @details Facet is constructed from all vertices listed in the LGI. Once constructed, the surfel
          pointer is stored in the facet.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::add_surfel(sSURFEL* surfel) {
  // Skip over even surfels
  if (surfel->is_even()) { return; }

  // For debugging, can remove after testing completed
  if (!surfel->is_conduction_surfel()) {
    msg_internal_error("Attempting to add non-conduction surfel %d to brep", surfel->id());
  }

  asINT32 n_vertices = surfel->stencil()->n_vertices;

  if (!n_vertices)
    return;
  asINT32 first_vertex_ind = surfel->stencil()->first_vertex_index;

  // Create facet
  this->FacetBegin();
  ccDOTIMES(vertex_num, n_vertices) {
    asINT32 local_vertex_index = vertex_local_index_from_global(first_vertex_ind+vertex_num);
    this->FacetAddVertex((iBREP_VERTEX)local_vertex_index);
  }
  // need to add 2 more offset vertices for 2d simulations to allow for a facet
  if (sim.is_2d()) {
    for (asINT32 vnum = n_vertices-1; vnum >= 0; vnum--) {
      asINT32 local_vertex_index = vertex_local_index_from_global(first_vertex_ind+vnum);
      this->FacetAddVertex((iBREP_VERTEX)(local_vertex_index+vertex_offset_2d()));
    }
  }

  iBREP_FACET facet_index = this->FacetEnd();
  auto dynData = surfel->dynamics_data();
  // Check facet created correctly
  if (facet_index == BREP_INVALID_IFACET)
    msg_internal_error("Failed to add surfel ID %d to the stencil info mesh.",
                       surfel->id());

  // Set facet shell index to face index
  this->FacetSetShellIndex(facet_index, (iBREP_SHELL)(surfel->m_face_index));

  // store pointer to surfel in facet
  cSHELL_MESH::FACET facet = this->GetFacet(facet_index);
  facet->set_surfel(surfel);
}

/**
 @brief Adds Facet corresponding to a surfel to the simplified cSHELL_MESH using reduced set of vertex indices.

 @details After determining reduced set of vertex indices defining the surfel (by combining collinear edges),
          these vertex indices are used to construct the facet. If this is the first facet in a brep Shell
          (indicated using set_seed), this facet is set as the Shell seed. Additionally, the facet's new shell
          index is passed to the function since it will no longer correspond to the surfels face index.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::add_surfel(sSURFEL* surfel, std::vector<iBREP_VERTEX>& vertex_inds,
    iBREP_SHELL shell_index) {
  cassert(vertex_inds.size() > 0);

  // Create facet (no need to add additional vertices for 2d simulations
  // since these vertex indices came from facets with them already added)
  this->FacetBegin();
  for (auto & v_ind : vertex_inds) {
    this->FacetAddVertex(v_ind);
  }
  
  iBREP_FACET facet_index = this->FacetEnd();

  // Check facet created correctly
  if (facet_index == BREP_INVALID_IFACET) {
    msg_print("Failed to add surfel ID %d to the simplified stencil info mesh with vertices:",
                       surfel->id());
    for (auto & v_ind : vertex_inds) {
      const cSHELL_MESH::sBG_POINT3& vert_pt = this->GetVertex(v_ind)->GetPoint();
      msg_print("Vertex %d (%e, %e, %e)", v_ind, vert_pt[0], vert_pt[1], vert_pt[2]);
    }
    msg_internal_error("Failure constructing simplified mesh");
  }

  // Set facet shell index to face index
  this->FacetSetShellIndex(facet_index, shell_index);

  // store pointer to surfel in facet
  cSHELL_MESH::FACET facet = this->GetFacet(facet_index);
  facet->set_surfel(surfel);
  m_surfel_to_facet_map[surfel->id()] = facet;
}

/**
 @brief Loops through facets and identifies the edge types for each of the facet's half_edges
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::identify_facet_edge_types() {
  this->initialize_ghost_half_edge_info_vec(total_csps);

  BREP_DO_FACETS(facet, (this), cSHELL_MESH) {
    // only want to build stencils for shell surfels
    if (!facet->is_facet_shell()) { continue; }

    // If using explicit solver, we only want to identify edge types for non-ghosted shell surfels
    // If using implicit solver, we want to identify edge types if this surfel is not a ghost or has neighbors that are not
    // ghosted.
    BOOLEAN build_for_facet = (sim.use_implicit_shell_solver)
      ? facet->is_facet_shell_or_shell_neighbor(TRUE) : !facet->get_surfel()->is_ghost();

    if (build_for_facet) {
      facet->identify_facet_edge_types();
    }
  }

  // Only implicit solver needs to comm info about ghosted surfel edges
  if (sim.use_implicit_shell_solver) {
    comm_ghost_half_edge_ring_info();
  }
  ccDOTIMES(i, total_csps) {
    m_ghost_half_edge_sps[i].clear();
  }
}

DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::perform_dummy_comms() {
  this->initialize_ghost_half_edge_info_vec(total_csps);
  comm_ghost_half_edge_ring_info();
  comm_ghost_int_edge_geom_info();
}

/**
 @brief Writes all shell facet information to stdout.

 @details Currently uses msg_print to write to stdout, but due to the large amount of text being written
          over multiple lines, it can be difficult to determine which SP wrote which line. It would
          therefore be advantageous to utilize logging to write the data to a file.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::dump_all_facet_info() {
  msg_set_width_limit(180);
  BREP_DO_FACETS(facet, (this), cSHELL_MESH) {
    if (facet->is_facet_shell()) {
      facet->dump_facet_info();
    }
  }
}


/**
 @brief Writes detailed information about edge rings to shell_stencil_edge_ring_info.txt

 @details Must be called before edge type identification is performed since it utilizes the processed
          flag in the half edge class.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::dump_edge_ring_info() {
  std::string fileName = fmt::format("shell_stencil_edge_ring_info_CSP{}.txt", my_csp_proc_id);
  std::ofstream edgeRingFile(fileName.c_str());
  BOOLEAN openSuccess = edgeRingFile.is_open();
  if (!openSuccess) {
    msg_print("Error opening file %s for printing edge ring info", fileName.c_str());
    return;
  }

  {
    BREP_DO_FACETS(facet, (this), cSHELL_MESH) {
      if (facet->is_facet_shell()) {
        BREP_FACET_DO_HALF_EDGES(he, facet, cSHELL_MESH) {
          if (he->is_processed()) {
            continue;
          }
          edgeRingFile << he->print_detailed_ring_info(TRUE);
          he->set_processed(TRUE);
        }
      }
    }
  }

  // Need to set all edges back to unprocessed after completing it
  {
    BREP_DO_FACETS(facet, (this), cSHELL_MESH) {
      if (facet->is_facet_shell()) {
        BREP_FACET_DO_HALF_EDGES(he, facet, cSHELL_MESH) {
          he->set_unprocessed();
        }
      }
    }
  }
  edgeRingFile.close();
}

/**
 @brief Processes m_ghost_half_edge_sps and sends list of surfel ids and half edge index on this CSP to neighbor CSPs

 @details The surfel Id and the half edge index allow for the homeCSP to identify the correct half_edge since
          there is no way to directly reference the half_edge itself.
 */
DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::get_ghost_half_edge_ids(asINT32& num_ghost_half_edges_total,
    asINT32& num_csp_being_sent_ghost_ids, std::vector<asINT32>& num_ghost_ids_to_send_to_csp,
    std::vector<std::vector<uINT32>>& ghost_ids_to_send_to_csp, asINT32& num_csp_recv_ghost_ids_from,
    std::vector<asINT32>& num_ghost_ids_to_recv_from_csp, std::vector<std::vector<uINT32>>& ghost_ids_recv_from_csp) {

  num_csp_being_sent_ghost_ids = 0;  // number of different CSPs that this CSPs ghost surfels have homes at 
  num_ghost_half_edges_total = 0;       // total number of ghost surfels (total elements in m_ghost_half_edge_sps)
  num_ghost_ids_to_send_to_csp.resize(total_csps);
  num_ghost_ids_to_recv_from_csp.resize(total_csps);


  ccDOTIMES(i, total_csps) {
    asINT32 n_ghosts_from_csp = this->get_ghost_half_edge_info_for_sp(i).size();
    num_ghost_ids_to_send_to_csp[i] = n_ghosts_from_csp;
    num_ghost_half_edges_total += num_ghost_ids_to_send_to_csp[i];
    if (n_ghosts_from_csp) {
      num_csp_being_sent_ghost_ids++;
    }
  }

  if (num_csp_being_sent_ghost_ids > 0) {
    ghost_ids_to_send_to_csp.resize(num_csp_being_sent_ghost_ids);
  }

  asINT32 cur_ind = 0;
  ccDOTIMES(i, total_csps) {
    if (num_ghost_ids_to_send_to_csp[i]) {
      // home_csps_for_ghosts.push_back(i);
      ghost_ids_to_send_to_csp[cur_ind].reserve(num_ghost_ids_to_send_to_csp[i]*2);
      for (auto half_edge : this->get_ghost_half_edge_info_for_sp(i)) {
        uINT32 surfelId = half_edge->get_surfel()->id();
        uINT32 heIndex = half_edge->index_in_facet();
        ghost_ids_to_send_to_csp[cur_ind].push_back(surfelId);
        ghost_ids_to_send_to_csp[cur_ind].push_back(heIndex);
      }
      cur_ind++;
    }
  }

  num_csp_recv_ghost_ids_from = cSHELL_STENCIL_INFO::send_and_receive_counts_to_csps(
      num_ghost_ids_to_send_to_csp, num_ghost_ids_to_recv_from_csp);

  // Step 2:
  //    - Send/Receive ghost surfel ids from each CSP that needs stencil info about

  if (num_csp_recv_ghost_ids_from)
    ghost_ids_recv_from_csp.resize(num_csp_recv_ghost_ids_from);

  // For each remote SP that sent a non zero surfel count, post a receive request for the vector of surfel ids.
  std::vector<MPI_Request> surfel_ids_receive_requests(num_csp_recv_ghost_ids_from, MPI_REQUEST_NULL);
  
  cur_ind = 0;
  ccDOTIMES(remote_csp, total_csps) {
    if (remote_csp != my_csp_proc_id) {
      if (num_ghost_ids_to_recv_from_csp[remote_csp] > 0) {
        ghost_ids_recv_from_csp[cur_ind].resize(num_ghost_ids_to_recv_from_csp[remote_csp]*2);
        MPI_Irecv(&ghost_ids_recv_from_csp[cur_ind][0],
                  num_ghost_ids_to_recv_from_csp[remote_csp]*2,
                  eMPI_asINT32,
                  remote_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(surfel_ids_receive_requests[cur_ind]));
        cur_ind++;
      }
    }
  }

  std::vector<MPI_Request> surfel_ids_send_requests(num_csp_being_sent_ghost_ids, MPI_REQUEST_NULL); //Only some SPs get sent IDs
  cur_ind = 0;
  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id) {
      if (num_ghost_ids_to_send_to_csp[source_csp] > 0) {
#if defined(_EXA_IMPI)
        MPI_Issend(&ghost_ids_to_send_to_csp[cur_ind][0],
                   num_ghost_ids_to_send_to_csp[source_csp]*2,
                   eMPI_sINT32,
                   source_csp,
                   eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                   eMPI_csp_comm,
                   &(surfel_ids_send_requests[cur_ind]));
#else
        MPI_Isend(&ghost_ids_to_send_to_csp[cur_ind][0],
                  num_ghost_ids_to_send_to_csp[source_csp]*2,
                  eMPI_asINT32,
                  source_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(surfel_ids_send_requests[cur_ind]));
#endif
        cur_ind++;
      }
    }
  }

  //Complete the surfel ID send requests.
  ccDOTIMES(message_index, num_csp_being_sent_ghost_ids) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_send_requests[message_index]), MPI_SLEEP_LONG);
  }

  //Complete the surfel ID receive request.
  ccDOTIMES(message_index, num_csp_recv_ghost_ids_from) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_receive_requests[message_index]), MPI_SLEEP_LONG);
  }
}

template <typename GHOST_INFO_TYPE>
VOID comm_ghost_half_edge_info(std::vector<GHOST_INFO_TYPE>& ghost_half_edge_info) {
  asINT32 num_ghost_halfedges_total;
  asINT32 num_csp_to_recv_ghost_halfedge_info_from;
  asINT32 num_csp_to_send_ghost_halfedge_info_to;
  std::vector<asINT32> num_ghost_halfedge_info_to_recv_from_csp;
  std::vector<asINT32> num_ghost_halfedge_info_to_send_to_csp;
  std::vector<std::vector<uINT32>> ids_of_ghosted_halfedges_to_recv;
  std::vector<std::vector<uINT32>> ids_of_ghosted_halfedges_to_send;

  get_shell_mesh()->get_ghost_half_edge_ids(num_ghost_halfedges_total, num_csp_to_recv_ghost_halfedge_info_from, 
    num_ghost_halfedge_info_to_recv_from_csp, ids_of_ghosted_halfedges_to_recv,
    num_csp_to_send_ghost_halfedge_info_to, num_ghost_halfedge_info_to_send_to_csp,
    ids_of_ghosted_halfedges_to_send);

  std::vector<sFLOAT*> ghost_halfedge_info_to_send;
  if (num_csp_to_send_ghost_halfedge_info_to > 0)
    ghost_halfedge_info_to_send.resize(num_csp_to_send_ghost_halfedge_info_to,NULL);

  std::vector<asINT32> size_of_packet_to_send_to_csp(total_csps,0);

  asINT32 cur_ind = 0;
  ccDOTIMES(i, total_csps) {
    if (num_ghost_halfedge_info_to_send_to_csp[i]) {
      std::vector<GHOST_INFO_TYPE> ghost_he_info_tmp;
      ghost_he_info_tmp.reserve(num_ghost_halfedge_info_to_send_to_csp[i]);
      asINT32 total_size = 0;
      ccDOTIMES(tmp_index, num_ghost_halfedge_info_to_send_to_csp[i]) {
        uINT32 surfelIndTmp = ids_of_ghosted_halfedges_to_send[cur_ind][tmp_index*2];
        uINT32 heIndTmp = ids_of_ghosted_halfedges_to_send[cur_ind][tmp_index*2+1];
        ghost_he_info_tmp.emplace_back(surfelIndTmp, heIndTmp);
        ghost_he_info_tmp.back().add_send_size(total_size);
      } 
      size_of_packet_to_send_to_csp[i] = total_size;
      sFLOAT *ptrToData = new sFLOAT[total_size];
      ghost_halfedge_info_to_send[cur_ind++] = ptrToData;
      for (auto& ghost_info : ghost_he_info_tmp) {
        ghost_info.fill_send_buffer(ptrToData);
      }
    }
  }

  std::vector<asINT32> size_of_packet_to_recv_from_csp(total_csps,0);
  asINT32 num_csp_to_recv_ghost_he_info_from_check = cSHELL_STENCIL_INFO::send_and_receive_counts_to_csps(
      size_of_packet_to_send_to_csp, size_of_packet_to_recv_from_csp);

  stencil_assert(num_csp_to_recv_ghost_he_info_from_check == num_csp_to_recv_ghost_halfedge_info_from);

  std::vector<sFLOAT*> ghost_halfedge_recv_buffers;
  if (num_csp_to_recv_ghost_halfedge_info_from) {
    ghost_halfedge_recv_buffers.resize(num_csp_to_recv_ghost_halfedge_info_from, NULL);
  }

  // For each remote SP that sent a non zero surfel count, post a receive request for the vector of surfel ids.
  std::vector<MPI_Request> stencil_info_receive_requests(num_csp_to_recv_ghost_halfedge_info_from, MPI_REQUEST_NULL);

  cur_ind = 0;
  ccDOTIMES(remote_csp, total_csps) {
    if (remote_csp != my_csp_proc_id) {
      if (size_of_packet_to_recv_from_csp[remote_csp] > 0) {
        ghost_halfedge_recv_buffers[cur_ind] = new sFLOAT[size_of_packet_to_recv_from_csp[remote_csp]];
        MPI_Irecv(ghost_halfedge_recv_buffers[cur_ind],
                  size_of_packet_to_recv_from_csp[remote_csp],
                  eMPI_sFLOAT,
                  remote_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(stencil_info_receive_requests[cur_ind]));
        cur_ind++;
      }
    }
  }

  std::vector<MPI_Request> stencil_info_send_requests(num_csp_to_send_ghost_halfedge_info_to, MPI_REQUEST_NULL); 
  cur_ind = 0;
  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id) {
      if (size_of_packet_to_send_to_csp[source_csp] > 0) {
#if defined(_EXA_IMPI)
        MPI_Issend(ghost_halfedge_info_to_send[cur_ind],
                   size_of_packet_to_send_to_csp[source_csp],
                   eMPI_sFLOAT,
                   source_csp,
                   eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                   eMPI_csp_comm,
                   &(stencil_info_send_requests[cur_ind]));
#else
        MPI_Isend(ghost_halfedge_info_to_send[cur_ind],
                  size_of_packet_to_send_to_csp[source_csp],
                  eMPI_sFLOAT,
                  source_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(stencil_info_send_requests[cur_ind]));
#endif
        cur_ind++;
      }
    }
  }

  //Complete the surfel ID send requests.
  ccDOTIMES(message_index, num_csp_to_send_ghost_halfedge_info_to) {
    complete_mpi_request_while_processing_cp_messages(&(stencil_info_send_requests[message_index]), MPI_SLEEP_LONG);
  }

  //Complete the surfel ID receive request.
  ccDOTIMES(message_index, num_csp_to_recv_ghost_halfedge_info_from) {
    complete_mpi_request_while_processing_cp_messages(&(stencil_info_receive_requests[message_index]), MPI_SLEEP_LONG);
  }

  ccDOTIMES(i, num_csp_to_send_ghost_halfedge_info_to) {
    delete[] ghost_halfedge_info_to_send[i];
  }

  if (num_csp_to_recv_ghost_halfedge_info_from) {
    ghost_half_edge_info.resize(num_ghost_halfedges_total);
    asINT32 cur_stencil_ind = 0;
    cur_ind = 0;
    ccDOTIMES(cur_csp, total_csps) {
      if ((cur_csp != my_csp_proc_id) && num_ghost_halfedge_info_to_recv_from_csp[cur_csp]) {
        sFLOAT* curBufferLoc = ghost_halfedge_recv_buffers[cur_ind];
        ccDOTIMES(j, num_ghost_halfedge_info_to_recv_from_csp[cur_csp]) {
          ghost_half_edge_info[cur_stencil_ind++].expand_recv_buffer(curBufferLoc);
        }
        delete[] ghost_halfedge_recv_buffers[cur_ind];
        cur_ind++;
      }
    }
  }
}

DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::comm_ghost_half_edge_ring_info() {
  std::vector<sGHOST_HALF_EDGE_RING_INFO> ghost_half_edge_ring_info;
  comm_ghost_half_edge_info(ghost_half_edge_ring_info);
  for (auto& ghost_info : ghost_half_edge_ring_info) {
    cSHELL_MESH::FACET facet = this->get_facet_from_surfel_id(ghost_info.ghost_surfel_id);
    cSHELL_MESH::HALF_EDGE half_edge = facet->half_edge_from_index(ghost_info.half_edge_in_facet);
    half_edge->set_ring_types_from_ghost_info(ghost_info);
  }
}

DEFINE_BREP_TEMPLATE_METHOD(VOID, SHELL_MESH)::comm_ghost_int_edge_geom_info() {
  std::vector<sGHOST_INT_EDGE_GEOM_INFO> ghost_int_edge_geom_info;
  comm_ghost_half_edge_info(ghost_int_edge_geom_info);
  for (auto& ghost_info : ghost_int_edge_geom_info) {
    cSHELL_MESH::FACET facet = this->get_facet_from_surfel_id(ghost_info.ghost_surfel_id);
    cSHELL_MESH::HALF_EDGE half_edge = facet->half_edge_from_index(ghost_info.half_edge_in_facet);
    half_edge = half_edge->get_internal_edge_owner();
    half_edge->set_int_edge_rotation(ghost_info.rotation);
    half_edge->set_int_edge_unfold_vec(ghost_info.unfolded_centroid_vec);
  }
  m_ghost_half_edge_sps.clear();
}

sGHOST_INT_EDGE_GEOM_INFO::sGHOST_INT_EDGE_GEOM_INFO(uINT32 surfelId, uINT32 half_edge_index) {
  ghost_surfel_id = surfelId;
  half_edge_in_facet = half_edge_index;
  cSHELL_MESH::FACET facet = get_shell_mesh()->get_facet_from_surfel_id(surfelId);
  cSHELL_MESH::HALF_EDGE half_edge = facet->half_edge_from_index(half_edge_in_facet);
  half_edge = half_edge->get_internal_edge_owner();
  rotation = half_edge->get_int_edge_rotation();
  vcopy2(unfolded_centroid_vec, half_edge->get_int_edge_unfold_vec());
}

sGHOST_HALF_EDGE_RING_INFO::sRING_INFO_ELEMENT::sRING_INFO_ELEMENT(cSHELL_MESH::HALF_EDGE half_edge) {
  info_flags = half_edge->get_info_flags();
  half_edge_surfel_id = half_edge->get_surfel()->id();
  nbr_surfel_id_1 = half_edge->get_nbr_he1_surfel_id();
  nbr_surfel_id_2 = half_edge->get_nbr_he2_surfel_id();
}

sGHOST_HALF_EDGE_RING_INFO::sGHOST_HALF_EDGE_RING_INFO(uINT32 surfelId, uINT32 half_edge_index) {
  ghost_surfel_id = surfelId;
  half_edge_in_facet = half_edge_index;
  cSHELL_MESH::FACET facet = get_shell_mesh()->get_facet_from_surfel_id(surfelId);
  cSHELL_MESH::HALF_EDGE half_edge = facet->half_edge_from_index(half_edge_in_facet);

  cSHELL_MESH::HALF_EDGE loopingHE = half_edge;
  do {
    if (!loopingHE->IsBorder() && loopingHE->is_shell_edge()) {
      half_edge_info.emplace_back(loopingHE);
    }
    loopingHE = loopingHE->NextInRing();
  } while (loopingHE != half_edge);
}

VOID print_malformed_ring_simerr(cSHELL_MESH::HALF_EDGE half_edge, std::string& errString) {
  SURFEL surfel = half_edge->get_surfel();
  sdFLOAT centroid[3];
  vcopy(centroid, surfel->centroid);
  cSHELL_MESH::sBG_POINT3 edge_centroid = get_shell_mesh()->HalfEdgeMidPoint(half_edge);
  simerr_report_error_code(SP_EER_BAD_SHELL_HALF_EDGE_CONFIGURATION, surfel->scale(), centroid,
                           surfel->id(), edge_centroid[0], edge_centroid[1], edge_centroid[2],
                           errString.c_str());
}

/**
 @brief Constructor for cHALF_EDGE_PAIR

 @details Stores half edges in m_he1 and m_he2 and checks to ensure that neither half_edge is associated
          with an open shell surfel (since those should only be included in the vector of cOPEN_SHELL_SET)
*/
cHALF_EDGE_PAIR::cHALF_EDGE_PAIR(SHELL_EDGE he1, SHELL_EDGE he2)
    : m_he1(he1), m_he2(he2), m_next_pair(NULL), m_prev_pair(NULL), m_vector_index(-1), m_includes_entire_ring(FALSE) {
  half_edge_pair_stencil_assert(!he1->is_open_shell_edge());
  half_edge_pair_stencil_assert(!he2->is_open_shell_edge());
}
  
/**
 @brief Constructor for cHALF_EDGE_PAIR called by cOPEN_SHELL_SET constructor

 @details Should never be called directly. he1 should be associated with the surfel at the closed shell surface
          and not the open shell surfels to be added to m_he_vec using the add_open_shell function.
*/
cHALF_EDGE_PAIR::cHALF_EDGE_PAIR(SHELL_EDGE he1)
    : m_he1(he1), m_he2(NULL), m_next_pair(NULL), m_prev_pair(NULL), m_vector_index(-1), m_includes_entire_ring(FALSE) {
  half_edge_pair_stencil_assert(!he1->is_open_shell_edge());
}

uINT32 cHALF_EDGE_PAIR::check_if_malformed() {
  // Misalignment of edges indicates a malformation in the edge
  if (m_he1->are_edges_aligned(m_he2)) {
    return MALFORMED_EDGE_TYPES::NO_MALFORMATON;
  }
  return MALFORMED_EDGE_TYPES::PAIR_UNALIGNED_EDGES; 
}


/**
 @brief Stores the index of the object in cEDGE_TYPE_IDENTIFIER::m_edge_pairs

 @details Assert currently included to ensure that this is only called once when looping through half edges in ring.
*/
asINT32 cHALF_EDGE_PAIR::vector_index() {
  stencil_assert(m_vector_index != -1);
  return m_vector_index;
}

/**
 @brief Stores 2nd half_edge associated with surfel at the closed shell surface

 @details This is used by the OPEN_SHELL_SET class to set the closing edge which is unknown at the time of
          construction.

          It can also be used to set m_he2 when identifying pairs of aligned surfels on either side of a
          contact surface
*/
VOID cHALF_EDGE_PAIR::add_closing_edge(SHELL_EDGE he2) {
  half_edge_pair_stencil_assert(!he2->is_open_shell_edge());
  this->m_he2 = he2;
}

/**
 @brief Adds open shell surfel to m_he_vec.

 @details Shouldnt be called for pairs of half_edges, since they dont store any open shell surfel information.
*/
VOID cHALF_EDGE_PAIR::add_open_shell(SHELL_EDGE openShell) {
  stencil_assert(FALSE);
}

/**
 @brief Returns open shell half_edge at index of m_he_vec.

 @details Shouldnt be called for pairs of half_edges, since they dont store any open shell surfel information.
*/
cHALF_EDGE_PAIR::SHELL_EDGE cHALF_EDGE_PAIR::open_shell(asINT32 index) {
  stencil_assert(FALSE);
  return NULL;
}

VOID cHALF_EDGE_PAIR::add_surfel_with_layer_edges_to_vec(SHELL_EDGE_VECTOR& layer_he_vec) {
  if (m_he1->is_shell_edge()) {
    layer_he_vec.push_back(m_he1);
  }
  if (m_he2 && m_he2->is_shell_edge()) {
    layer_he_vec.push_back(m_he2);
  }
} 

/**
 @brief Checks if next_pair_tmp (next pair/set in CW direction) should be connect to this pair returning TRUE if it does
*/
BOOLEAN cHALF_EDGE_PAIR::check_connect_next_pair(cHALF_EDGE_PAIR* next_pair_tmp) {
  if (next_pair_tmp->is_open_shell_set()) {
    // OPEN_SHELL_SETs always have layers, so all we need to do is check if this pair contains layers
    // and if m_he2 is a coupled edge
    if (this->m_he2->is_coupled_cond_interface_edge() && this->contains_layers()) {
      return TRUE;
    }
  } else {
    if (this->m_he1->is_shell_edge() && this->m_he2->is_edge_in_0_res_contact_pair()) {
      if (next_pair_tmp->contains_layers()) {
        return TRUE;
      }
    }
  }
  return FALSE;
}

/**
 @brief Checks if prev_pair_tmp (next pair/set in CCW direction) should be connect to this pair returning TRUE if it does
*/
BOOLEAN cHALF_EDGE_PAIR::check_connect_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp) {
  if (prev_pair_tmp->is_open_shell_set()) {
    // OPEN_SHELL_SETs always have layers, so all we need to do is check if this pair contains layers
    // and if m_he1 is a coupled edge
    if (this->m_he1->is_coupled_cond_interface_edge() && this->contains_layers()) {
      return TRUE;
    }
  } else {
    if (this->m_he2->is_shell_edge() && this->m_he1->is_edge_in_0_res_contact_pair()) {
      if (prev_pair_tmp->contains_layers()) {
        return TRUE;
      }
    }
  }
  return FALSE;
}

/**
 @brief Stores pointer to next_pair_tmp (next pair/set in CW direction) in m_next_pair if it should be connected

 @details If already set, asserts that the new pair matches the old one.

          Sets m_prev_pair with this since they are connected to each other.

          Uses check_connect_next_pair function to determine if the pair/set should be connected, but will skip check
          if skip_connection_check is TRUE (defaults to FALSE)
*/
VOID cHALF_EDGE_PAIR::check_and_add_next_pair(cHALF_EDGE_PAIR* next_pair_tmp, BOOLEAN skip_connection_check) {
  if (m_next_pair) {
    half_edge_pair_stencil_assert(m_next_pair == next_pair_tmp);
    half_edge_pair_stencil_assert(next_pair_tmp->prev_pair() == this);
  } else if (skip_connection_check || this->check_connect_next_pair(next_pair_tmp)) {
    m_next_pair = next_pair_tmp;
    m_next_pair->check_and_add_prev_pair(this, TRUE);
  }
}

/**
 @brief Stores pointer to prev_pair_tmp (next pair/set in CCW direction) in m_prev_pair if it should be connected

 @details If already set, asserts that the new pair matches the old one.

          Sets m_next_pair with this since they are connected to each other.

          Uses check_connect_prev_pair function to determine if the pair/set should be connected, but will skip check
          if skip_connection_check is TRUE (defaults to FALSE)
*/
VOID cHALF_EDGE_PAIR::check_and_add_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp, BOOLEAN skip_connection_check) {
  if (m_prev_pair) {
    half_edge_pair_stencil_assert(m_prev_pair == prev_pair_tmp);
    half_edge_pair_stencil_assert(prev_pair_tmp->next_pair() == this);
  } else if (skip_connection_check || this->check_connect_prev_pair(prev_pair_tmp)) {
    m_prev_pair = prev_pair_tmp;
    m_prev_pair->check_and_add_next_pair(this, TRUE);
  }
}

/**
 @brief Walks through pairs stored in m_next_pair to check if references are circular.

 @details Returns TRUE if references are circular.
*/
BOOLEAN cHALF_EDGE_PAIR::is_group_circular() {
  cHALF_EDGE_PAIR* looping_pair = this;
  do {
    if (!looping_pair->connected_both_dir()) {
      return FALSE;
    }
    looping_pair = looping_pair->next_pair();
  } while (looping_pair != this);
  return TRUE;
}

VOID cHALF_EDGE_PAIR::process_single_pair_or_set() {
  if (!contains_layers()) {
    return;
  }
  SHELL_EDGE edge1 = this->edge1();
  SHELL_EDGE edge2 = this->edge2();
  if (edge1->is_shell_edge()) {
    if (edge2 && edge2->is_shell_edge()) {
      edge1->set_internal_or_intersection_edge_nbr(edge2);
    } else {
      edge1->set_boundary_edge_nbr(edge2);
    }
  } else if (edge2 && edge2->is_shell_edge()) {
    edge2->set_boundary_edge_nbr(edge1);
  }
}

/**
 @brief Adds open shell surfel to m_he_vec.

 @details Checks that half_edge is associated with an open shell surfel and throws assert if not.
*/
VOID cOPEN_SHELL_SET::add_open_shell(SHELL_EDGE openShell) {
  half_edge_pair_stencil_assert(openShell->is_open_shell_edge());
  m_he_vec.push_back(openShell);
}

uINT32 cOPEN_SHELL_SET::check_if_malformed() {
  // An invalid type for on of the edges indicates a malformation in the edge ring
  // if (m_he1->check_back_dynamics_data() == EDGE_INVALID_BC) {
  //   return MALFORMED_EDGE_TYPES::INVALID_EDGE_TYPE;
  // }

  if (m_he2 != NULL) {
    // if (m_he2->check_back_dynamics_data() == EDGE_INVALID_BC) {
    //   return MALFORMED_EDGE_TYPES::INVALID_EDGE_TYPE;
    // }
    if (!m_he1->are_edges_aligned(m_he2)) {
      return MALFORMED_EDGE_TYPES::SET_UNALIGNED_EDGES;
    }
  }
  return MALFORMED_EDGE_TYPES::NO_MALFORMATON; 
}

/**
 @brief Checks if either m_he1 or m_he2 contain layers
*/
BOOLEAN cOPEN_SHELL_SET::closed_shells_contain_layers() {
  BOOLEAN containsLayers = m_he1->is_shell_edge();
  if (m_he2) {
    containsLayers |= m_he2->is_shell_edge();
  }

  return containsLayers;
}


/**
 @brief Checks if next_pair_tmp (next pair/set in CW direction) should be connect to this pair returning TRUE if it does
*/
BOOLEAN cOPEN_SHELL_SET::check_connect_next_pair(cHALF_EDGE_PAIR* next_pair_tmp) {
  if (next_pair_tmp->is_open_shell_set()) {
    msg_internal_error("It should not be possible for an OPEN_SHELL_SET to have an OPEN_SHELL_SET next to it");
  } else {
    if (this->m_he2->is_coupled_cond_interface_edge() && next_pair_tmp->contains_layers()) {
      return TRUE;
    }
  }
  return FALSE;
}

/**
 @brief Checks if prev_pair_tmp (next pair/set in CCW direction) should be connect to this pair returning TRUE if it does
*/
BOOLEAN cOPEN_SHELL_SET::check_connect_prev_pair(cHALF_EDGE_PAIR* prev_pair_tmp) {
  if (prev_pair_tmp->is_open_shell_set()) {
    msg_internal_error("It should not be possible for an OPEN_SHELL_SET to have an OPEN_SHELL_SET next to it");
  } else {
    if (this->m_he1->is_coupled_cond_interface_edge() && prev_pair_tmp->contains_layers()) {
      return TRUE;
    }
  }
  return FALSE;
}

VOID cOPEN_SHELL_SET::add_surfel_with_layer_edges_to_vec(SHELL_EDGE_VECTOR& layer_he_vec) {
  for (auto shell_he : m_he_vec) {
    layer_he_vec.push_back(shell_he);
  }
} 

BOOLEAN are_parameters_fixed_and_equal(sPHYSICS_VARIABLE& param1, sPHYSICS_VARIABLE& param2) {
  if (param1.is_constant() && param2.is_constant()) {
    return (param1.value == param2.value);
  }
  return FALSE;
}

/**
 @brief Checks if either m_he1 or m_he2 contain layers
*/
VOID cOPEN_SHELL_SET::get_closed_shell_surfel_boundary_types(uINT32& edge_type1, uINT32& edge_type2) {
  edge_type1 = m_he1->check_back_dynamics_data();
  // Throw exception if edge type is EDGE_INVALID_BC
  if (m_he2) {
    edge_type2 = m_he2->check_back_dynamics_data();
    // Throw exception if edge type is EDGE_INVALID_BC
  } else {
    edge_type2 = EDGE_INVALID_BC;
  }

  // If m_he2 corresponds to an adiabatic surfel, set the edge type as invalid to indicate that only m_he1
  // is needed for edge type identification regardless of what it's type is
  if (edge_type2 == EDGE_IS_ADIABATIC_BC) {
    edge_type2 = EDGE_INVALID_BC;
  }
  // if edge_type2 is invalid (either because m_he2 is NULL or its related to an adiabatic surfel), return 
  if (edge_type2 == EDGE_INVALID_BC) {
    return;
  }

  // When this point is reached, m_he2 must not be NULL and edge_type2 != EDGE_IS_ADIABATIC_BC, so if
  // edge_type1 == EDGE_IS_ADIABATIC_BC, set it to EDGE_INVALID_BC and return to indicate that m_he2 defines the
  // edge type
  if (edge_type1 == EDGE_IS_ADIABATIC_BC) {
    edge_type1 = EDGE_INVALID_BC;
    return;
  }

  // At this point we know that m_he2 is not Null and neither edge_types are adiabatic, so check if this the edge
  // can still be defined from a single surfel

  // If either edge_type1 or edge_type2 == EDGE_IS_DEFINED_TEMP_BC, one of three possibilities can occur:
  //    Both are EDGE_IS_DEFINED_TEMP_BC and the edge will be a cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY using surfels
  //          associated with both m_he1 and m_he2
  //    edge_type1 is EDGE_IS_DEFINED_TEMP_BC and edge_type2 is EDGE_IS_DYN_DATA_BC or vice versa
  // In any of these three cases, we need to keep the value of edge_type1 and edge_type2 the same
  if ((edge_type1 == EDGE_IS_DEFINED_TEMP_BC) || (edge_type2 == EDGE_IS_DEFINED_TEMP_BC)) {
      return;
  }

  // At this point, edge_type1 and edge_type2 are both EDGE_IS_DYN_DATA_BC, so check if they can be defined with
  // just one of the surfels and if so, set edge_type2 to EDGE_INVALID_BC
  
  // If from the same face, they have the same physics descriptor
  if (m_he1->surfel_face_id() == m_he2->surfel_face_id()) {
    edge_type2 = EDGE_INVALID_BC;
    return;
  }

  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data1 = m_he1->get_surfel()->dynamics_data();
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR>* dyn_data2 = m_he2->get_surfel()->dynamics_data();
  
  // if different dynamics types, return without changing edge_type1 or edge_type2
  asINT32 dynType1 = convert_cdi_type_to_dyn_type(dyn_data1); 
  asINT32 dynType2 = convert_cdi_type_to_dyn_type(dyn_data2); 
  if (dynType1 != dynType2) {
    return;
  }

  // Could have the same physics descriptor even though they are from different faces, so check if pointers are equal
  if (dyn_data1->physics_descriptor() == dyn_data2->physics_descriptor()) {
    edge_type2 = EDGE_INVALID_BC;
    return;
  }

  BOOLEAN fixed_equal_params = TRUE;
  if (dynType1 == CONDUCTION_FIXED_TEMP_SURFEL_TYPE) {
    CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc1
      = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data1->physics_descriptor();
    CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc2
      = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data2->physics_descriptor();
    fixed_equal_params = are_parameters_fixed_and_equal(phys_desc1->parameters()->temperature,
                                                        phys_desc2->parameters()->temperature);
  }
  else if (dynType1 == CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE) {
    CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR phys_desc1
      = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)dyn_data1->physics_descriptor();
    CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR phys_desc2
      = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)dyn_data2->physics_descriptor();
    // We want flux into fluid side of wall, which is heat_flux_2 in physics descriptor
    fixed_equal_params = are_parameters_fixed_and_equal(phys_desc1->parameters()->heat_flux_2,
                                                        phys_desc2->parameters()->heat_flux_2);

  } else if (dynType1 == CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE) {
    CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc1
      = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data1->physics_descriptor();
    CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR phys_desc2
      = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)dyn_data2->physics_descriptor();
    fixed_equal_params = are_parameters_fixed_and_equal(phys_desc1->parameters()->ext_heat_xfer_coeff,
                                                        phys_desc2->parameters()->ext_heat_xfer_coeff);
    fixed_equal_params &= are_parameters_fixed_and_equal(phys_desc1->parameters()->ambient_temp,
                                                        phys_desc2->parameters()->ambient_temp);
  } else {
    msg_internal_error("dynType %d not recognized", dynType1);
  }
  
  // if fixed_equal_params is TRUE set edge_type2 to invalid
  if (fixed_equal_params) {
    edge_type2 = EDGE_INVALID_BC;
  }
}


BOOLEAN cOPEN_SHELL_SET::process_set_with_closed_shell_surfel_layers() {
  BOOLEAN he1_is_coupled = m_he1->is_coupled_cond_interface_edge();
  BOOLEAN he2_is_coupled = FALSE;
  if (m_he2) {
    he2_is_coupled = m_he2->is_coupled_cond_interface_edge();
  }
  BOOLEAN edges_are_coupled = he1_is_coupled || he2_is_coupled;
  
  if (edges_are_coupled) {
    // we can add m_he1 and/or m_he2 to the open shell vec here because it will no longer be needed for processing after this
    if (m_he1->is_shell_edge()) {
      m_he_vec.push_back(m_he1);
    }
    if (m_he2 && m_he2->is_shell_edge()) {
      m_he_vec.push_back(m_he2);
    }

    // If m_he_vec.size() == 2 these half_edges might need to be labeled as internal. If so, this will be handled
    // in set_intersection_edge_nbrs
    m_he_vec[0]->set_intersection_edge_nbrs(m_he_vec);
    // return TRUE to signify that processing is complete
    return TRUE;
  }

  // If we have reached this point, the closed shell surfel(s) is/are not coupled to the open shell surfels and can be
  // either treated as an adiabatic edge (if only one surfel) or processed as a separate pair (if two surfels).
  if (m_he2) {
    std::unique_ptr<cHALF_EDGE_PAIR> closed_shell_pair = std::make_unique<cHALF_EDGE_PAIR>(m_he1, m_he2);
    if (closed_shell_pair->check_if_malformed()) {
      std::string errMsg = "Open shell surfels abutting misaligned closed shell surfels";
      print_malformed_ring_simerr(m_he1, errMsg);
      m_he1->set_as_adiabatic_boundary_edge();
      m_he2->set_as_adiabatic_boundary_edge();
    } else {
      closed_shell_pair->process_single_pair_or_set();
    }
  } else {
    m_he1->set_as_adiabatic_boundary_edge();
  }

  // return false to indicate that the open shell surfels still need to be processed using boundary data stored in 
  // m_he1 and m_he2
  return FALSE;
}

/**
 @brief Processes the edges in this cOPEN_SHELL_SET that is the only pair/set in the ring

 @details This function processes a single cOPEN_SHELL_SET object which has the following rules:
            1) If the open shell surfels abut a single closed shell surfel that is:
              a) Adiabatic - its an intersection edge if there is > 1 open shell surfels in the set, otherwise its adiabatic
              b) A surfel with physics descriptor defining HTC/temp/flux BC - its a dynamics data boundary edge
              c) Otherwise its a fixed temperature boundary edge
            2) If they abut two closed shell surfels:
              a) And both closed shell surfels apply the same BC type - same treatment as above for a single surfel
                  Note: For dynamics data boundaries, both surfels must share the same phys descriptor or the same
                        type of phys descriptors with equal fixed (not time/space varying) parameter values
              b) One of the closed shell surfels is adiabatic - the other surfel applies the BC
              c) Otherwise each open shell surfel in set has its BC determined from the closest closed shell surfel (as
                  determined by the angle between surfels)

          This function should only be called under the following circumstances:
            1) This set represents the entire edge ring (i.e. the ring contains open shell surfels abutting one or
                two closed shell surfels with or without layers and nothing else). In this case m_includes_entire_ring
                is TRUE.
            2) Neither of the closed shell surfels defined by m_he1 and m_he2 do not contain any layers
            3) One or both of the closed shell surfels defined by m_he1 and m_he2 contain layers, but both
               surfels also specify a BC which prohibits the formation of an intersection edge between these layers
               and the layers in the open shell surfels.

          Therefore, we only need to worry about layers in the open shell surfels if m_includes_entire_ring
          Otherwise there are not layers in those surfels, or those surfels will be handled in a separate processing step
*/
VOID cOPEN_SHELL_SET::process_single_pair_or_set() {
  OPEN_SHELL_SET cur_set = this;
  SHELL_EDGE_VECTOR& shell_vec = cur_set->open_shell_vec();

  // Handle if either surfel has layers and this set represents the entire edge ring
  // If function returns true, the entire open shell set and it's closed shell surfel nbrs were processed
  // If the function returns false, only the closed shell surfel nbrs were processed
  if (m_includes_entire_ring && cur_set->closed_shells_contain_layers()) {
    if (cur_set->process_set_with_closed_shell_surfel_layers()) {
      return;
    }
  }

  uINT32 edge_type1, edge_type2;
  cur_set->get_closed_shell_surfel_boundary_types(edge_type1, edge_type2);
  // If edge_type2 == EDGE_INVALID_BC, edge1 defines the boundary type to apply
  if (edge_type2 == EDGE_INVALID_BC) {
    // if edge_type1 is adiabatic and there is > 1 open shell surfels, they form an intersection boundary.
    if ((edge_type1 == EDGE_IS_ADIABATIC_BC) && (shell_vec.size() > 1)) {
      shell_vec[0]->set_intersection_edge_nbrs(shell_vec);
    } else {
      // otherwise, each open shell surfel has a bc based on edge1
      cur_set->edge1()->set_as_boundary_edge_nbr_type(shell_vec, edge_type1);
    }
    return;
  } else if (edge_type1 == EDGE_INVALID_BC) {
    // if edge_type2 is adiabatic and there is > 1 open shell surfels, they form an intersection boundary.
    if ((edge_type2 == EDGE_IS_ADIABATIC_BC) && (shell_vec.size() > 1)) {
      shell_vec[0]->set_intersection_edge_nbrs(shell_vec);
    } else {
      // otherwise, each open shell surfel has a bc based on edge1
      cur_set->edge2()->set_as_boundary_edge_nbr_type(shell_vec, edge_type2);
    }
    return;
  } else if ((edge_type1 == EDGE_IS_DEFINED_TEMP_BC) && (edge_type2 == EDGE_IS_DEFINED_TEMP_BC)) {
    for (auto shell_edge : shell_vec) {
      shell_edge->set_fixed_temp_boundary_edge_nbrs(cur_set->edge1(), cur_set->edge2());
    }
    return;
  }

  // If reached this point, we need to assign edge nbrs based on angles between surfels
  for (auto shell_edge : shell_vec) {
    // using abs value of angle since we dont care about orientation
    dFLOAT ang1 = fabs(shell_edge->angle_btw_edges(cur_set->edge1()));
    dFLOAT ang2 = fabs(shell_edge->angle_btw_edges(cur_set->edge2()));
    if (ang1 <= ang2) {
      shell_edge->set_boundary_edge_nbr(cur_set->edge1());
    } else {
      shell_edge->set_boundary_edge_nbr(cur_set->edge2());
    }
  }
}

/**
 @brief Constructor for cEDGE_TYPE_IDENTIFIER

 @details Accepts a half_edge which will define the CCW and CW directions for sorting edges. This must be an
          edge associated with a shell surfel. 
*/
cEDGE_TYPE_IDENTIFIER::cEDGE_TYPE_IDENTIFIER(SHELL_EDGE root_edge)
   : m_malformed_error(MALFORMED_EDGE_TYPES::NO_MALFORMATON) {
  m_root_edge = root_edge;
}

/**
 @brief Checks if edge ring needs special handling when it contains two sets of opposite surfels

 @details Rings with two sets of opposite surfels have different configurations that need special handling:
       
  1) All four surfels have layers with one or both pairs of surfels on either side of the contact surface
     having the same number of layers:
              The pairs with matching number of layers will have an internal edge, unmatched numbers of layers
              will be an intersection edge.
  2) One pair of surfels on a side of the contact surface has a matching number of layers, the other side doesnt
     have any layers:
              Side with layers has internal edge
  3) If only one pair has layers but not the same number:
              The edges with layers will form an intersection, non shell edges will be ignored 
  4) 2 surfels with layers, but are on opposite sides of contact surface or 2 pairs of surfels with differing numbers
     of layers.
              This situation will result in the function returning false to have the identification algorithm
              handle the identification (along with the case where there are an odd number of surfels with layers in
              these two pairs).
  
  In the event that the ring meets condition 1,2 or 3 this function will return TRUE to indicate that the
  identification has been completed successfully
*/
BOOLEAN maybe_process_ring_with_layers_and_contact(cSHELL_MESH::HALF_EDGE root_edge, asINT32 nShellEdges) {
  // Using cHALF_EDGE_PAIR simplifies the code by utilizing its functions to assign half edge
  // type information. This function will perform some checks to ensure that the conditions descibed above
  // before calling the cHALF_EDGE_PAIR functions
  cHALF_EDGE_PAIR he_pair1(root_edge);
  cHALF_EDGE_PAIR he_pair2(root_edge->get_opposite_facet_edge());

  // the next edge in the ring is either one other set of opposite edges, or he_pair2.first. If it's
  // he_pair2.first, the next faceted edge is guarenteed to be one of the other set
  cSHELL_MESH::HALF_EDGE next_he = root_edge->next_faceted_edge();
  if (next_he == he_pair2.edge1()) {
    next_he = next_he->next_faceted_edge();
  }
  if (next_he->are_edges_aligned(he_pair1.edge1())) {
    he_pair1.add_closing_edge(next_he);
    next_he = next_he->get_opposite_facet_edge();
    
    // if for some reason either of the pairs are unaligned, the other pair are not aligned,
    // continue on to the main algorithm for identification
    if (next_he->are_edges_aligned(he_pair2.edge1())) {
      he_pair2.add_closing_edge(next_he);
    } else {
      return FALSE;
    }
  } else {
    he_pair2.add_closing_edge(next_he);
    next_he = next_he->get_opposite_facet_edge();

    if (next_he->are_edges_aligned(he_pair1.edge1())) {
      he_pair1.add_closing_edge(next_he);
    } else {
      return FALSE;
    }
  }

  // if either pair is malformed, return FALSE without building. The any malformation will be handled by the subsequent
  // attempt at identification
  if (he_pair1.check_if_malformed() || he_pair2.check_if_malformed()) {
    return FALSE;
  }

  // conditionals used before calling this function ensure that there are either 2 or 4 edges with layers.
  // Now, all we need to do is check to see if either have matching number of layers. If there are no surfels with
  // matching numbers of layers, we will go ahead and do the processing for the resulting intersection edge
  BOOLEAN pair1_has_layers = he_pair1.contains_layers();
  BOOLEAN pair2_has_layers = he_pair2.contains_layers();
  if (nShellEdges == 2) {
    // Both pairs only have one edge with layers, return FALSE (case 4)
    if (pair1_has_layers && pair2_has_layers) {
      return FALSE;
    } else if (pair1_has_layers) {
      // only he_pair1 has layers, so we can call he_pair1.process_single_pair_or_set() to handle the identification
      // if it is an internal edge (case 2) or intersection edge (case 3). This completes the processing for the ring,
      // so return TRUE
      he_pair1.process_single_pair_or_set();
    } else {
      // Same as above, but for he_pair2
      he_pair2.process_single_pair_or_set();
    }
    return TRUE;
  }

  // If not 2 shell edges, all 4 must contain layers, so handle appropriately for this falling under case 1 or case 4
  BOOLEAN pair1_layers_match = he_pair1.edge1()->equal_number_of_layers(he_pair1.edge2());
  BOOLEAN pair2_layers_match = he_pair2.edge1()->equal_number_of_layers(he_pair2.edge2());
  // Neither have matching number: have algorithm handle it since the existance of contact resistance may keep it from
  // forming an intersection between all 4 surfels
  if (!pair1_layers_match && !pair2_layers_match) {
    return FALSE;
  } 
  // Atleast one pair has matching number of layers, so have set_internal_or_intersection_edge_nbr function
  // handle assigning it as internal or intersection
  he_pair1.edge1()->set_internal_or_intersection_edge_nbr(he_pair1.edge2());
  he_pair2.edge1()->set_internal_or_intersection_edge_nbr(he_pair2.edge2());
  return TRUE;
}


/**
 @brief Checks for specific configurations, setting edge type information in the half-edges and returing TRUE if one
        is found.

 @details Starts by looping through the ring and counting the types of surfels associated with these edges.
          This information (and not any ordering/positioning info) is used to identify special configurations.
          This is the best place to add any additional configurations that the algorithm fails to identity correctly.
          
          Not all of the count variables are used to identify the edge type. They have been left here for now
          in case new additions to the function may need them.
*/
BOOLEAN cEDGE_TYPE_IDENTIFIER::check_for_simple_configs() {
  asINT32 nClosedShellEdges = 0;
  asINT32 nOpenShellEdges = 0;
  asINT32 nCoupledEdges = 0;
  asINT32 nCoupledShellEdges = 0;
  // Actually the number of contact pair surfels, not number of pairs (=2*number of pairs)
  asINT32 nZeroResContactPairs = 0;
  asINT32 nContactPairs = 0;
  asINT32 nTotalEdges = 0;
  asINT32 nTotalShellEdges = 0;
  asINT32 nNonShellEdges = 0;
  // asINT32 nESurfelEdges = 0;
  SHELL_EDGE loopingHe = m_root_edge;
  do {
    if (!loopingHe->IsBorder()) {
      nTotalEdges++;
      BOOLEAN isShell = loopingHe->is_shell_edge();
      BOOLEAN isCoupled = loopingHe->is_coupled_cond_interface_edge();
      // BOOLEAN isESurfel = loopingHe->is_esurfel_edge();
      BOOLEAN isContactPair = loopingHe->is_edge_in_contact_pair();
      BOOLEAN isZeroResContactPair = loopingHe->is_edge_in_0_res_contact_pair();

      if (isShell) {
        nTotalShellEdges++;
        if (loopingHe->is_open_shell_edge()) {
          nOpenShellEdges++;
        } else {
          nClosedShellEdges++;
          if (isCoupled) {
            nCoupledShellEdges++;
          }
        }
      } else {
        nNonShellEdges++;
      }
      if (isCoupled) {
        // assert(!isESurfel);
        nCoupledEdges++;
      }
      // if (isESurfel) {
      //   assert(!isCoupled);
      //   nESurfelEdges++;
      // }
      if (isContactPair) {
        nContactPairs++;
      }
      if (isZeroResContactPair) {
        nZeroResContactPairs++;
      }
    }
    loopingHe = loopingHe->NextInRing();
  } while(loopingHe != m_root_edge);

  // If this half_edge does not have another half edge in the ring, of if there is only one other half edge which is
  // associated with a surfel in contact with this half_edge's surfel, treat the edge as though its adiabatic
  if (nTotalEdges == 1 || (nTotalEdges == 2 && nContactPairs)) {
    m_root_edge->set_as_adiabatic_boundary_edge();
    if (nTotalShellEdges == 2) {
      m_root_edge->next_shell_edge()->set_as_adiabatic_boundary_edge();
    }
    return TRUE;
  }
  if (nTotalEdges == 2 && !nOpenShellEdges) {
    std::unique_ptr<cHALF_EDGE_PAIR> he_pair = std::make_unique<cHALF_EDGE_PAIR>(m_root_edge,
                                                                                 m_root_edge->next_faceted_edge());
    m_malformed_error = he_pair->check_if_malformed();
    if (m_malformed_error) {
      return FALSE;
    }
    he_pair->process_single_pair_or_set();
    return TRUE;
  }
  // if all are open shells, there is no need to worry about contact pairs.
  // ZMS1 TODO: Need to update to handle contact pairs of open shells
  if (nOpenShellEdges == nTotalEdges) {
    SHELL_EDGE_VECTOR shellVec;
    m_root_edge->get_edge_vector(shellVec, FALSE);
    m_root_edge->set_intersection_edge_nbrs(shellVec);
    return TRUE;
  }

  // Special handling of edges on contact surface. See comments for maybe_process_ring_with_layers_and_contact
  // function for more information.
  if (nTotalEdges == 4 && nContactPairs == 4 && !nOpenShellEdges && (nTotalShellEdges==2 || nTotalShellEdges==4)) {
    // dont return immediately if function returns FALSE since the ring may meet conditions processed below
    if (maybe_process_ring_with_layers_and_contact(m_root_edge, nTotalShellEdges)) {
      return TRUE;
    }
  }

  // If we can describe the entire ring using a single cOPEN_SHELL_SET, we can use process_single_pair_or_set to
  // identify and apply edge type information. This is possible if nOpenShellEdges > 0 and the number of closed
  // shell surfels (i.e. number of surfels that arent open shell surfels) is <= 2
  asINT32 nClosedShellSurfelEdges = nTotalEdges - nOpenShellEdges;
  if (nClosedShellSurfelEdges < 3 && nOpenShellEdges) {
    // easiest way to handle this is to create an OPEN_SHELL_SET and call process_single_pair_or_set
    SHELL_EDGE closed_shell_surfel_he1 = (m_root_edge->is_closed_shell_edge()) 
                                      ? m_root_edge : m_root_edge->next_closed_shell_surfel_edge();
    
    std::unique_ptr<cOPEN_SHELL_SET> open_shell_set = std::make_unique<cOPEN_SHELL_SET>(closed_shell_surfel_he1);
    SHELL_EDGE cur_open_shell_edge = (m_root_edge->is_open_shell_edge()) 
                                    ? m_root_edge : m_root_edge->next_open_shell_surfel_edge();
    open_shell_set->add_open_shell(cur_open_shell_edge);
    ccDOTIMES(i, nOpenShellEdges-1) {
      cur_open_shell_edge = cur_open_shell_edge->next_open_shell_surfel_edge();
      open_shell_set->add_open_shell(cur_open_shell_edge);
    }
    
    if (nClosedShellSurfelEdges == 2) {
      open_shell_set->add_closing_edge(closed_shell_surfel_he1->next_closed_shell_surfel_edge());
    }

    m_malformed_error = open_shell_set->check_if_malformed();
    if (m_malformed_error) {
      return FALSE;
    }

    // since this will be used to represent the entire edge ring, we indicate this by calling set_includes_entire_ring_flag
    open_shell_set->set_includes_entire_ring_flag();
    open_shell_set->process_single_pair_or_set();
    return TRUE;
  }

  return FALSE;
}
  
/**
 @brief Forms cHALF_EDGE_PAIR/cOPEN_SHELL_SET objects from ordered half_edges stored in m_half_edges.

 @details If m_root_index is associated with an open shell surfel, the function will walk in CCW direction
          until reaching a non open shell edge. It is assumed that this edge is associated with a closed shell 
          interface surfel as any rings with only open shell surfels will have been identified by 
          check_for_simple_configs. Once an appropriate starting position is found, the function walks through
          the sorted half_edges in the CW direction, creating cHALF_EDGE_PAIR/cOPEN_SHELL_SET objects for
          the pair/sets it passes.

          Note: Currently, the algorithm and the cHALF_EDGE_PAIR/cOPEN_SHELL_SET classes cannot handle an edge ring with
          an unpaired edge. Need to test to see if this situation can occur and add methods to handle if so.
*/
BOOLEAN cEDGE_TYPE_IDENTIFIER::build_edge_pairs() {
  // get vector of edges in ring sorted by angle
  m_root_index = m_root_edge->get_sorted_edges(m_half_edges);

  asINT32 nEdges = m_half_edges.size();
  BOOLEAN next_is_open_shell = FALSE;
  asINT32 cur_ind = m_root_index;
  asINT32 prev_ind = prevIndex(cur_ind);
  if (m_half_edges[cur_ind]->is_open_shell_edge()) {
    next_is_open_shell = TRUE;
    while(m_half_edges[prev_ind]->is_open_shell_edge()) {
      prev_ind = prevIndex(prev_ind);
    }
    cur_ind = nextIndex(prev_ind);
  } else {
    prev_ind = cur_ind;
    cur_ind = nextIndex(cur_ind);
  }

  asINT32 start_index = prev_ind;
  asINT32 numOpenShells = 0;
  asINT32 edgesProcessed = 0;
  do {
    if (m_half_edges[cur_ind]->is_open_shell_edge()) {
      cHALF_EDGE_PAIR* cur_pair = new cOPEN_SHELL_SET(m_half_edges[prev_ind]);
      cur_pair->set_vector_index(m_edge_pairs.size());
      m_edge_pairs.push_back(cur_pair);
      while(m_half_edges[cur_ind]->is_open_shell_edge()) {
        numOpenShells++;
        edgesProcessed++;
        cur_pair->add_open_shell(m_half_edges[cur_ind]);
        cur_ind = nextIndex(cur_ind);
      }
      cur_pair->add_closing_edge(m_half_edges[cur_ind]);
      m_malformed_error = cur_pair->check_if_malformed();
      if (m_malformed_error) {
        return FALSE;
      }
    } else {
      cHALF_EDGE_PAIR* cur_pair = new cHALF_EDGE_PAIR(m_half_edges[prev_ind], m_half_edges[cur_ind]);
      cur_pair->set_vector_index(m_edge_pairs.size());
      m_edge_pairs.push_back(cur_pair);
      
      m_malformed_error = cur_pair->check_if_malformed();
      if (m_malformed_error) {
        return FALSE;
      }

      // if the next surfel after the one at cur_ind is an open shell surfel, we need to set prev_ind = cur_ind
      // and increment cur_ind
      asINT32 next_ind = nextIndex(cur_ind);
      if(!m_half_edges[next_ind]->is_open_shell_edge()) {
        cur_ind = next_ind;
      }
      edgesProcessed += 2;
    }
    prev_ind = cur_ind;
    cur_ind = nextIndex(cur_ind);
  } while (edgesProcessed < nEdges);

  asINT32 numClosedSurfels = m_half_edges.size() - numOpenShells;
  // Number of closed shell surfels should always be even in a properly formed half edge ring, so throw exception
  // if it isnt
  if ((numClosedSurfels%2) == 1) {
    m_malformed_error = MALFORMED_EDGE_TYPES::UNMATCHED_CLOSED_SHELL_EDGE;
    return FALSE;
  }
  return TRUE;
}

/**
 @brief Groups pairs/sets in m_edge_pairs based on the dynamics data stored in each surfel and the location of layer
        containing surfels.

 @details Pair/sets are grouped by setting m_next_pair and m_prev_pair in the cHALF_EDGE_PAIR/cOPEN_SHELL_SET objects.
          This is done using the check_and_add_next_pair and check_and_add_prevpair member functions.

          Note: Currently does not handle intersection of open shell surfels and closed shell surfels with layers.
*/
VOID cEDGE_TYPE_IDENTIFIER::group_pairs() {
  asINT32 nPairs = m_edge_pairs.size();

  // TODO: also add support for grouping open shell surfels to closed shell surfels through coupled surfels
  ccDOTIMES(cur_ind, nPairs) {
    cHALF_EDGE_PAIR* cur_pair = m_edge_pairs[cur_ind];
    cHALF_EDGE_PAIR* next_pair = m_edge_pairs[(cur_ind + 1)%nPairs];
    cHALF_EDGE_PAIR* prev_pair = m_edge_pairs[(cur_ind == 0) ? (nPairs-1) : (cur_ind - 1)];

    cur_pair->check_and_add_next_pair(next_pair);
    cur_pair->check_and_add_prev_pair(prev_pair);
  }
}

/**
 @brief Loops through m_edge_pairs, which may contain one or more pairs/sets that grouped together 

 @details Loops through the vector, skipping any pairs/sets that have been grouped with their neighbors and therefore
          do not need to be processed separately.

          Has special handling for two situations:
            - All pair/sets in m_edge_pairs are grouped together (a circular group)
            - m_edge_pairs[0] is grouped with its CCW and CW neighbors.

*/
VOID cEDGE_TYPE_IDENTIFIER::process_pair_groups() {
  if (m_edge_pairs.size() == 1) {
    process_pair_group(0,TRUE);
    return;
  }

  group_pairs();
  
  // Call process_pair_group once for a circular group (all pairs/sets in vector are grouped).
  // Calling with single_group = TRUE to ensure that the function knows the references are circular
  if (m_edge_pairs[0]->is_group_circular()) {
    process_pair_group(0, TRUE);
    return;
  } else {
    
    // Want to begin on with a pair/set that isnt connect to its CCW neighbor to ensure simplify the looping process 
    asINT32 cur_ind = 0;
    while(m_edge_pairs[cur_ind]->prev_pair_connected()) {
      cur_ind++;
    }

    // Loop through elements in m_edge_pairs, using the number of pairs/sets in the group returned by process_pair_group
    // to skip any pairs/sets that were included in the last processing and to track how many groups remain that need
    // processing.
    asINT32 totalPairsProcessed = 0;
    do {
      totalPairsProcessed += process_pair_group(cur_ind+totalPairsProcessed, FALSE);
    } while (totalPairsProcessed != m_edge_pairs.size());
  }
}

/**
 @brief Processes a group of pairs/sets starting at cur_ind in m_edge_pairs 

 @details Processes all pairs/sets grouped with m_edge_pairs[cur_ind] and the number of pairs/sets processed.
          If single_group == TRUE, the function processes all pairs/sets in
          m_edge_pairs as a single group.
*/
asINT32 cEDGE_TYPE_IDENTIFIER::process_pair_group(asINT32 cur_ind, BOOLEAN single_group) {
  cHALF_EDGE_PAIR* cur_pair = m_edge_pairs[cur_ind];
  SHELL_EDGE_VECTOR shell_edges;

  asINT32 nPairsInGroup;
  if (single_group) {
    nPairsInGroup = m_edge_pairs.size();
    for (auto edge_pair : m_edge_pairs) {
      edge_pair->add_surfel_with_layer_edges_to_vec(shell_edges);
    }
  } else if (!cur_pair->next_pair_connected()) {
    cur_pair->process_single_pair_or_set();
    return 1;
  } else {
    nPairsInGroup = 1;
    cur_pair->add_surfel_with_layer_edges_to_vec(shell_edges);
    while(cur_pair->next_pair_connected()) {
      nPairsInGroup++;
      cur_pair = cur_pair->next_pair();
      cur_pair->add_surfel_with_layer_edges_to_vec(shell_edges);
    }
    stencil_assert(nPairsInGroup <= m_edge_pairs.size());
  }
  
  if (nPairsInGroup == 1) {
    cur_pair->process_single_pair_or_set();
  } else {
    shell_edges[0]->set_intersection_edge_nbrs(shell_edges);
  }
  return nPairsInGroup;
}


VOID cEDGE_TYPE_IDENTIFIER::process(BOOLEAN root_is_ghost) {
  // If this half_edge ring only contains ghosts, we will skip it here and update its type information
  // from that contained on this surfel's homeSP.
  if (root_is_ghost && m_root_edge->only_ghosts_in_ring()) {
    STP_PROC homeCSP = m_root_edge->get_surfel()->home_sp() - total_fsps;
    get_shell_mesh()->get_ghost_half_edge_info_for_sp(homeCSP).push_back(m_root_edge);
    return;
  }
  BOOLEAN ring_is_simple = check_for_simple_configs();
  if (!ring_is_simple && (m_malformed_error == MALFORMED_EDGE_TYPES::NO_MALFORMATON)) {
    BOOLEAN build_success = build_edge_pairs();
    if (build_success) {
      process_pair_groups();
    }
  }

  if (m_malformed_error) {
    std::string errString;
    get_malformed_string(errString);
    print_malformed_ring_simerr(m_root_edge, errString);
    m_root_edge->reset_all_to_adiabatic_boundary_edges();
  }
}


#endif