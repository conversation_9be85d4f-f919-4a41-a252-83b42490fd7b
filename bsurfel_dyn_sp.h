/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Bsurfel dynamics (functional sets and groups)
 *
 * Nath Gopalaswamy, Exa Corporation
 * Created Tue Nov 3, 2015
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_BSURFEL_DYN_H
#define _SIMENG_BSURFEL_DYN_H

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "bsurfel.h"
#include "eqns.h"
#include "strand_enum.h"

class sBSURFEL_PROCESS_CONTROL
{
  static std::vector<sBSURFEL_UBLK_NEIGHBOR_INFO> m_bsurfel_neighbor_info;
public:
  SCALE m_scale;
  ACTIVE_SOLVER_MASK m_active_solver_mask;


  VOID set_scale(SCALE scale)
  {
    m_scale = scale;
  }

  VOID set_active_solver_mask(SCALE scale, TIMESTEP_PARITY running_parity)
  {
    m_active_solver_mask = g_timescale.m_active_solver_masks[running_parity][scale];
  }

  VOID init(SCALE scale, TIMESTEP_PARITY running_parity = g_timescale.time_parity())
  {
    set_scale(scale);
    set_active_solver_mask(scale, running_parity);
  }

  VOID process_all_bsurfels(STRAND strand_type, BSURFEL_GROUP_TYPE group_type);
  VOID do_bsurfel_measurements(BSURFEL_GROUP_TYPE group);
  asINT32 surface_collision(BSURFEL_GROUP_TYPE group); //for particles

};


VOID bsurfel_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                               cSTRING cdi_desc_var_name, vFLOAT value,
                               vFLOAT aux_value,
                               STP_GEOM_VARIABLE bsurfel_centroid[3]);


/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/
VOID finish_init_of_bsurfels();
VOID seed_all_bsurfels(BOOLEAN is_full_checkpoint_restore);
VOID do_bsurfel_dynamics(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask);
VOID update_all_bsurfel_positions();
VOID mark_ublk_neighbors_of_all_bsurfels();
VOID reset_interior_and_ublk_neighbors_of_all_bsurfels();
VOID deactivate_all_bsurfels_in_solid_voxels();
VOID update_bsurfel_meas_windows(sBSURFEL* bsurfel,
                                 LRF_PHYSICS_DESCRIPTOR lrf,
                                 ACTIVE_SOLVER_MASK active_solver_mask,
                                 STP_GEOM_VARIABLE meas_scale_factor,
                                 dFLOAT force[3],
                                 dFLOAT mass_flux);



#endif // _SIMENG_BSURFEL_DYN_H
