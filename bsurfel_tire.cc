/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Deforming Tires Implementation
 *
 * Dalon Work
 * Created Jul 29, 2019
 *--------------------------------------------------------------------------*/

#include "bsurfel_tire.h"
#include "comm_utils.h"

#include LGI_H

sTIRE_VERTEX_SMART_PTR::~sTIRE_VERTEX_SMART_PTR()
{
  if (m_vertex) {
    m_vertex->ref_count--;
    if (m_vertex->ref_count == 0)  {
      m_parent->delete_vertex(m_vertex);
    }
    nullify();
  }
}

sSIM_TIRE::sSIM_TIRE(const cDGF_DEFORMING_TIRE& dgf_tire,
                     const std::vector<double>& u_knots,
                     const std::vector<double>& v_knots,
                     const std::vector<double>& coeff_wrap,
                     const std::vector<double>& coeff_carcass)
{
  double origin[] = { 0.0, 0.0, 0.0 };
  double rotaxis[] = { 1.0, 0.0, 0.0 };

  const double (*x)[4] = dgf_tire.xform;

  auto xform = sBG_TRANSFORM3d(x[0][0], x[0][1], x[0][2], x[0][3],
                               x[1][0], x[1][1], x[1][2], x[1][3],
                               x[2][0], x[2][1], x[2][2], x[2][3]);

  m_dtire = Tire::cDEFORMING_TIRE(origin,
                                  rotaxis,
                                  dgf_tire.grooved_tire,
                                  u_knots,
                                  v_knots,
                                  coeff_wrap,
                                  coeff_carcass,
                                  xform);
  m_carcass_finder = std::make_unique<Tire::cSURFACE_POINT_FINDER>(m_dtire.get_carcass_finder());
}

void sSIM_TIRE::pack_vertices(cNEIGHBOR_SP nsp, std::vector<unsigned char> &send_buffer)
{
  auto& vertices = m_vertices_to_comm[nsp.nsp()];
  std::sort(vertices.begin(), vertices.end());
  auto new_end = std::unique(vertices.begin(), vertices.end());
  uint32_t nvertices = std::distance(vertices.begin(), new_end);
  pack(send_buffer, &nvertices);
  for (auto it = vertices.begin(); it != new_end; ++it) {
    pack(send_buffer, &((*it)->id));
    pack(send_buffer, &((*it)->uvw));
    pack(send_buffer, &((*it)->xyz));
  }

  vertices.clear();
}

void sSIM_TIRE::unpack_vertices(std::vector<unsigned char>::iterator &recv_buffer)
{
  uint32_t nvertices;
  uINT32 gid;
  sBG_POINT3d uvw;
  sBG_POINT3d xyz;
  unpack(recv_buffer, &nvertices);
  for (int i = 0; i < nvertices; i++) {
    unpack(recv_buffer, &gid);
    unpack(recv_buffer, &uvw);
    unpack(recv_buffer, &xyz);
    init_vertex(gid, uvw, xyz);
  }
}

void sSIM_TIRE::rotate_vertices(double rot_angle)
{
  tTHASH_DO(keyVar, valueVar, &m_vertex_map, sTIRE_VERTEX_HASH_TABLE) {
    cassert(valueVar->initialized);
    auto xyz = m_dtire.rotate(rot_angle, valueVar->uvw, m_ed);
#if ENABLE_CONSISTENCY_CHECKS
    {
      auto d = xyz - valueVar->xyz;
      double diff = Tire::norm(d);
      if (diff > 1.0) {
        // sINT32 index = sim.get_deforming_tire_index(this);
        sINT32 index = -1;

        //Changed this internal error to sim error, lets the simulation go on but
        //reports the location of the supersonic vertices in the simerr file
        
        //msg_internal_error("Vertex %d traveled greater than a single voxel!\n" 
        //                   "Rotation angle: %f, Tire: %d\n"
        //                   "UVW: (%f, %f, %f)\n"
        //                   "Previous location: (%f, %f, %f)\n"
        //                   "New location: (%f, %f, %f)", keyVar, 
        //                   rot_angle, index,
        //                   valueVar->uvw[0],
        //                   valueVar->uvw[1],
        //                   valueVar->uvw[2],
        //                   valueVar->xyz[0],
        //                   valueVar->xyz[1],
        //                   valueVar->xyz[2],
        //                   xyz[0],
        //                   xyz[1],
        //                   xyz[2]
        //                   );

        STP_GEOM_VARIABLE xyz_old[3];
        ccDOTIMES(i,3) {
          xyz_old[i] = valueVar->xyz[i];
        }
        simerr_report_error_code(SP_EER_DEFORMING_TIRE_SUPERSONIC_VERTEX, 0, xyz_old, 
                                 index, 
                                 keyVar, 
                                 valueVar->uvw[0], 
                                 valueVar->uvw[1], 
                                 valueVar->uvw[2]);
      }
    }
#endif
    valueVar->xyz = xyz;
  }
}

