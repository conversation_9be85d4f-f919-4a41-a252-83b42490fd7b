/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * BSurfel dynamics (functional sets and groups)
 *
 * Nath Gopalaswamy, Exa Corporation
 * Created Wed Nov 3, 2015
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "bsurfel_dyn_sp.h"
#include "sim.h"
#include "sp_timers.h"
#include "bsurfel_util.h"
#include "bsurfel_comm.h"
#include "bsurfel_ibm.h"
#include "bsurfel_ublk_neighbor_info.h"
#include "particle_sim.h"

VOID bsurfel_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                               cSTRING cdi_desc_var_name, vFLOAT value,
                               vFLOAT aux_value,		// min or max
                               STP_GEOM_VARIABLE bsurfel_centroid[3])
{
  msg_internal_error("Bsurfel Eqn Error!");
  // CHARACTER err_buffer[128];
  //
  // cSTRING movb_name = (simerr_current_bsurfel_dyn_group
  // 	       ? simerr_current_bsurfel_dyn_group->physics_descriptor()->name
  // 	       : "unknown");
  // cSTRING bc_name   = (simerr_current_bsurfel_dyn_group
  //                      ? simerr_current_bsurfel_dyn_group->bc_name()
  //                      : "unknown");
  // cSTRING case_var = physics_var->name;
  // asINT32 scale = (simerr_current_bsurfel_dyn_group ? simerr_current_bsurfel_dyn_group->scale : -1);
  //
  // switch (type) {
  //   case EQN_ERROR_FPE:
  //     {
  // simerr_report_error_code(SP_EER_FPE_FACE, scale, bsurfel_centroid,
  // 			 bc_name, movb_name, cdi_desc_var_name, case_var, value);
  // break;
  //     }
  //
  //   case EQN_ERROR_TOO_LARGE:
  //     {
  // simerr_report_error_code(SP_EER_TOO_BIG_FACE, scale, bsurfel_centroid,
  // 			 bc_name,
  // 			 movb_name, cdi_desc_var_name, case_var, value, aux_value);
  // break;
  //     }
  //   case EQN_ERROR_TOO_SMALL:
  //     {
  // simerr_report_error_code(SP_EER_TOO_SMALL_FACE, scale, bsurfel_centroid,
  // 			 bc_name,
  // 			 movb_name, cdi_desc_var_name, case_var, value, aux_value);
  // break;
  //     }
  //   default:
  //     msg_internal_error("Unrecognized eqn error type.");
  //     break;
  // }
}

#ifdef BSURFEL_NOT_SUPPORTED
VOID reinitialize_shobs()
{
  WITH_SIMERR_BFACE(this) {
    sPHYSICS_DESCRIPTOR *pd = this->physics_descriptor();
    if (pd->all_parameters_sharable) {
      STP_GEOM_VARIABLE dummy[3] = { 1, 0, 0 };
      pd->eval_space_varying_only_parameter_program(dummy, dummy, bsurfel_eqn_error_handler);
    }
    DO_BSURFEL_DYN_GROUP_SHOBS(bsurfel, this) {
      if (!pd->all_parameters_sharable) {
        STP_GEOM_VARIABLE centroid[3] = {bsurfel->centroid[0], bsurfel->centroid[1], bsurfel->centroid[2]};
        STP_GEOM_VARIABLE normal[3] = {bsurfel->normal[0], bsurfel->normal[1], bsurfel->normal[2]};
        pd->eval_space_varying_only_parameter_program(centroid, normal, bsurfel_eqn_error_handler);
      }
      bsurfel->reinit(pd);
      bsurfel->check_parms_bounds(pd);
    }
  }
}
#endif

std::vector<sBSURFEL_UBLK_NEIGHBOR_INFO> sBSURFEL_PROCESS_CONTROL::m_bsurfel_neighbor_info;

VOID sBSURFEL_PROCESS_CONTROL::process_all_bsurfels(STRAND strand_type, BSURFEL_GROUP_TYPE group_type)
{

  WITH_TIMER(SP_BSURFEL_DYN_TIMER) {
    sBSURFEL_FSET &bsurfel_fset = *g_bsurfel_groups[group_type];
    FSET_FOREACH_GROUP_OF_SCALE(sBSURFEL_FSET, bsurfel_fset, group, m_scale) {
      for (sBSURFEL *bsurfel = group->shob_ptrs(); bsurfel != NULL; bsurfel = bsurfel ? bsurfel->m_next : NULL) {

        if (!bsurfel->is_active()) continue;

        increment_item_timer_count(SP_BSURFEL_DYN_TIMER);

        dFLOAT ref_frame_vel[3], ref_pt[3];

        bsurfel->calculate_ref_frame_vel_and_pt(ref_frame_vel, ref_pt);
        bsurfel->update_velocity(ref_frame_vel, ref_pt);

        sBSURFEL_NEIGHBOR_UBLK_LOCATOR finder(*bsurfel, m_bsurfel_neighbor_info);
        finder.add_neighboring_ublks();

        if (m_bsurfel_neighbor_info.size() > 0) {
          bsurfel->interpolate_fluid_properties(m_bsurfel_neighbor_info);
          bsurfel->compute_body_force();
          bsurfel->distribute_body_force(m_bsurfel_neighbor_info);
        }
        else {
          bsurfel->reset();
        }

        m_bsurfel_neighbor_info.clear(); 

      }
    } 
  }
}

VOID sBSURFEL_PROCESS_CONTROL::do_bsurfel_measurements(BSURFEL_GROUP_TYPE group_type)
{
  dFLOAT sampled_force_per_area[3] = {0};
  // Now that all ublks have been updated, we can compute the normalized
  // forces on the bsurfels for measurement purposes
  WITH_TIMER(SP_BSURFEL_DYN_TIMER) {
    sBSURFEL_FSET &bsurfel_fset = *g_bsurfel_groups[group_type];
    FSET_FOREACH_GROUP_OF_SCALE(sBSURFEL_FSET, bsurfel_fset, group, m_scale) {
      STP_GEOM_VARIABLE meas_scale_factor = STP_GEOM_VARIABLE((sINT64) 1 << (sim.num_scales - m_scale - 1));
      for (sBSURFEL* bsurfel = group->shob_ptrs(); bsurfel != NULL; bsurfel = bsurfel ? bsurfel->m_next : NULL) {

        if (bsurfel->is_active()) {
          sBSURFEL_NEIGHBOR_UBLK_LOCATOR finder(*bsurfel, m_bsurfel_neighbor_info, false);
          finder.add_neighboring_ublks();
          memset(sampled_force_per_area, 0, sizeof(sampled_force_per_area));

          if (m_bsurfel_neighbor_info.size() > 0) {
            bsurfel->normalize_distributed_body_force(m_bsurfel_neighbor_info, sampled_force_per_area);
          }
        }

        update_bsurfel_meas_windows(bsurfel, bsurfel->m_lrf_physics_desc, m_active_solver_mask,
                                    meas_scale_factor, sampled_force_per_area, 0);
        m_bsurfel_neighbor_info.clear(); 
        
      }
    }
  }
}

void sBSURFEL::sSEND_LIST_QUANTUM::execute()
{
  if (m_bsurfel->m_group != NULL) {
    m_bsurfel->m_group->remove_shob_from_group(m_bsurfel);
  }
  m_send_group->add_bsurfel(m_bsurfel);
  m_bsurfel->m_group = NULL;
}

void sBSURFEL::sMOVE_LIST_QUANTUM::execute()
{
  if (m_bsurfel->m_group != NULL) m_bsurfel->m_group->remove_shob_from_group(m_bsurfel);
  m_new_group->add_shob_to_group(m_bsurfel);
  m_bsurfel->m_group = m_new_group;
}


VOID finish_init_of_bsurfels()
{

  for (int i = 0; i < sim.n_tires; i++) {
    sim.tires[i].init_comm_buffers(g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps());
  }

  //move the bsurfel through the transform if checkpoint restored
  if (sim.is_full_checkpoint_restore) {
    sim.update_movb_xforms();
  }
  
  //bsurfel positions are required to be updated at initialization because 
  //home_ublk are needed to be located now.
  //For deforming tires and bsurfel seeding the home_ublk changes from the one provided 
  //in the LGI file.
  //CP no longer finds the current home_ublk
  update_all_bsurfel_positions();
  do_preliminary_bsurfel_comm();

  std::vector<sBSURFEL_UBLK_NEIGHBOR_INFO> bsurfel_neighbor_info;
  std::vector<sBSURFEL::sMOVE_LIST_QUANTUM> move_list;

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_BSURFELS_OF_SCALE(bsurfel, scale) {

      dFLOAT ref_frame_vel[3], ref_pt[3];
      bsurfel->calculate_ref_frame_vel_and_pt(ref_frame_vel, ref_pt);
      bsurfel->update_velocity(ref_frame_vel, ref_pt);

      // All ublks should default to OFF, so now turn ON possible neighbors
      // bsurfels all start in the interior group, so place them in the correct
      // locations here.
      if (bsurfel->is_active()) {
        BSURFEL_GROUP_TYPE new_group_type = bsurfel->activate_bsurfel_neighbor_ublks();
        bsurfel->maybe_add_self_to_move_list(new_group_type, move_list);

        // Now find the real neighbors
        sBSURFEL_NEIGHBOR_UBLK_LOCATOR finder(*bsurfel, bsurfel_neighbor_info);
        finder.add_neighboring_ublks(); // this marks neighbor ublks
        bsurfel_neighbor_info.clear();
      }
    }
  }

  for (auto& m : move_list) {
    m.execute();
  }
}

VOID update_all_bsurfel_positions()
{
  static std::vector<sBSURFEL::sSEND_LIST_QUANTUM> send_list;
  static std::vector<sBSURFEL::sMOVE_LIST_QUANTUM> move_list;

  send_list.clear();
  move_list.clear();
  WITH_TIMER(SP_BSURFEL_MOVE_TIMER) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_BSURFELS_OF_SCALE(bsurfel, scale) {
        increment_item_timer_count(SP_BSURFEL_MOVE_TIMER);
        bsurfel->update_position(send_list, move_list);
      }
    }

    for (auto& s: send_list) {
      s.execute();
    }

    for (auto& m: move_list) { 
      m.execute();
    }
  }

}

VOID seed_all_bsurfels(BOOLEAN is_full_checkpoint_restore)
{
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_BSURFELS_OF_SCALE(bsurfel, scale) {
      bsurfel->seed(is_full_checkpoint_restore);
    }
  }
}

// VOID bsurfel_particle_collision_internal(UBLK ublk, asINT32 voxel,BSURFEL bsurfel, PARTICLE_VAR angular_velocity); //defined in particle_sim.cc
asINT32 sBSURFEL_PROCESS_CONTROL::surface_collision(BSURFEL_GROUP_TYPE group_type)
{
  asINT32 surfel_count = 0;
  sBSURFEL_FSET &bsurfel_fset = *g_bsurfel_groups[group_type];
  FSET_FOREACH_GROUP_OF_SCALE(sBSURFEL_FSET, bsurfel_fset, group, m_scale) {
    for (sBSURFEL* bsurfel = group->shob_ptrs(); bsurfel != NULL; bsurfel = bsurfel ? bsurfel->m_next : NULL) {
      if (bsurfel->is_active()) {
        
        surfel_count++;

        sBSURFEL_NEIGHBOR_UBLK_LOCATOR finder(*bsurfel, m_bsurfel_neighbor_info, false);
        finder.add_neighboring_ublks();

        if (m_bsurfel_neighbor_info.size() > 0) {
          //Compute the bsurfel's velocity and pass it to the collision detection routine incase the splash or reflection
          //models need it (fix for PR40436)

          ROTATING_TIRE_PARAMETERS parms = bsurfel->m_movb_physics_desc->parameters();
          STP_REF_FRAME_INDEX vel_ref_frame_index = parms->ref_frame.value;
          STP_REF_FRAME_INDEX group_ref_frame_index = bsurfel->m_lrf_physics_desc ? bsurfel->m_lrf_physics_desc->index : -1;
          sPARTICLE_VAR bsurfel_velocity[N_SPACE_DIMS];
          if ((group_ref_frame_index >= 0) && (vel_ref_frame_index == group_ref_frame_index)) {
            vzero(bsurfel_velocity);
          }
          else {
            sPARTICLE_VAR radius[N_SPACE_DIMS];
            ccDOTIMES(i, N_SPACE_DIMS) {
              radius[i] = bsurfel->centroid(i) - parms->axis_point[i].value;
            }
            vcross(bsurfel_velocity, bsurfel->m_movb_physics_desc->angular_vel_vec, radius);
          }

          ccDOTIMES(neighbor_index, m_bsurfel_neighbor_info.size()) {
            asINT32 voxel = m_bsurfel_neighbor_info[neighbor_index].voxel();
            UBLK ublk = m_bsurfel_neighbor_info[neighbor_index].ublk();
            particle_sim.surface_collision(ublk, voxel, bsurfel, bsurfel_velocity);
          }
        }
        m_bsurfel_neighbor_info.clear();
      }
    }
  }
  return surfel_count;
}
