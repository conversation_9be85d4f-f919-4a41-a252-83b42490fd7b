/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Mukul Rao, Exa Corporation                      Created April, 2018
//----------------------------------------------------------------------------
#ifndef SIMENG_COMPILE_TIME_LOOP_H_
#define SIMENG_COMPILE_TIME_LOOP_H_

/*COMPILE_TIME_LOOP client structure
 *
 *  struct client {
 *
 *   template<int index>
 *   void eval(type1 arg1,type2 arg2,...) //can take in any number of arguments
 *   {
 *     //Instantiate some function that depends on <index>
 *   }
 *  }
 *
 * =========================================================
 *  Example
 *
 *  template<int N>
 *  print_power(int exponent){
 *    std::cout << "Number^exponent : " << pow(N,exponent) << "\n";
 *  }
 *
 *  struct evaluate_powers{
 *
 *  template<int index>
 *   void eval(int exponent){
 *     print_power<index>(exponent);
 *   }
 *  }
 *
 * In some cc file, statement below
 * adds assembly code to print 10 numbers that are powers of 2, the loop is unrolled at compile time
 *
 * COMPILE_TIME_LOOP<1,10,evaluate_powers>::iterate(2)
 *
 * The above statement is equivalent to
 * client::print_number<1>(2); //Prints  1^2
 * client::print_number<2>(2); //Prints  2^2
 * client::print_number<3>(2); //Prints  3^2
 * .... 10 times
 *
 * The compile time loop therefore helps eliminate repeating code
 */


//COMPILE_TIME_LOOP implements a compile time for loop by recursively
//instantiating different versions of the class template. The loop comes to
//an end when the template specialization below is called.
//Note that the number of recursions is limited by the compiler
template<int start_index,int end_index,typename struct_with_eval_method>
struct COMPILE_TIME_LOOP {
  template<typename... Args>
  __HOST__DEVICE__ static __attribute__((always_inline)) inline void iterate(Args&... args){
    static_assert(start_index < end_index,"Start index must be smaller than the end index");
    struct_with_eval_method::template eval<start_index>(args...);
    COMPILE_TIME_LOOP<start_index+1,end_index,struct_with_eval_method>::iterate(args...);
  }
};

//Template specialization that terminates the static for loop
template<int end_index,typename struct_with_eval_method>
struct COMPILE_TIME_LOOP<end_index,end_index,struct_with_eval_method> {
  template<typename... Args>
  __HOST__DEVICE__ static __attribute__((always_inline)) inline void iterate(Args&... args){};
};

#endif /* SIMENG_COMPILE_TIME_LOOP_H_ */
