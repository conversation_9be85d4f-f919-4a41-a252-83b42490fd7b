#ifndef UBLK_SOLVER_DATA_ACCESS_H
#define UBLK_SOLVER_DATA_ACCESS_H
#include "simulator_namespace.h"
#include "common_sp.h"
#include <memory>

enum UBLK_DATA_TYPE
{
  /* The offsets for the following data types are implicitly available
   * UBLK_LB_DATA_TYPE, UBLK_STATES_DATA_TYPE, UBLK_TURB_DATA_TYPE
   * with an INLINE_BLOCK_LAYOUT
   *
   * For the vector of solver blocks layout (used for GPU compressed layout),
   * we do not assume that any UBLK data is implicitly available as part of the UBLK.
   * We thus define additional enums to account for this lack of implicitity.
   */  
  UBLK_T_DATA_TYPE,
#if !BUILD_GPU
  UBLK_CONDUCTION_DATA_TYPE,
  UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE,
#endif
  UBLK_UDS_DATA_TYPE,
  UBLK_PF_DATA_TYPE,
  UBLK_MC_DATA_TYPE,
  UBLK_MME_DATA_TYPE,
  UBLK_MME_T_DATA_TYPE,
  UBLK_MME_UDS_DATA_TYPE,
#if !BUILD_GPU
  UBLK_MME_CONDUCTION_DATA_TYPE,
#endif
  UBLK_PARTICLE_DATA_TYPE,
  NEARBLK_GEOM_DATA_TYPE,
  NEARBLK_LB_DATA_TYPE,
  NEARBLK_TURB_DATA_TYPE,
  NEARBLK_T_DATA_TYPE,
#if !BUILD_GPU
  NEARBLK_CONDUCTION_DATA_TYPE,
#endif
  NEARBLK_UDS_DATA_TYPE,
  NEARBLK_PF_DATA_TYPE,
  NEARBLK_MC_DATA_TYPE,
  NEARBLK_MME_DATA_TYPE,
  NEARBLK_FROZEN_DATA_TYPE,
  NEARBLK_P_DATA_TYPE,
  UBLK_VR_DATA_TYPE,
#if !BUILD_GPU
  UBLK_VR_CONDUCTION_DATA_TYPE,
#endif
  UBLK_MIRROR_DATA_TYPE,
  UBLK_PORE_LB_DATA_TYPE,
  UBLK_PORE_MC_DATA_TYPE,  
  UBLK_PORE_UDS_DATA_TYPE,
  UBLK_CBF_LB_DATA_TYPE,
#if !BUILD_GPU  
  UBLK_DYN_DATA_TYPE
#else
  UBLK_DYN_DATA_TYPE,
  UBLK_LB_DATA_TYPE,
  UBLK_STATES_DATA_TYPE,
  UBLK_TURB_DATA_TYPE,
  UBLK_MME_TURB_DATA_TYPE,
  UBLK_MME_DNS_DATA_TYPE,
  NEARBLK_DP_PASS_FACTORS_DATA_TYPE,
  UBLK_SMART_SEED_DATA_TYPE,
  N_TYPES
#endif  
};

enum class SOLVER_LAYOUT_TYPE {
  INLINE_SOLVER_LAYOUT_TYPE = 0,
  ARRAY_OF_SOLVER_BLOCKS_LAYOUT_TYPE = 1
};

template<size_t N_VOXELS>
struct tINLINE_BLOCK_LAYOUT {

  using UBLK_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS, sdFLOAT_UBLK, tINLINE_BLOCK_LAYOUT>;
  using UBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using UBLK_ATTRS = tUBLK_ATTRS<N_VOXELS>;
  using sUBLK_STATES_DATA = tUBLK_STATES_DATA<UBLK_TYPE_TAG>;
  using sUBLK_UDS_DATA = tUBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_UDS_DATA = tNEARBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PORE_UDS_DATA = tUBLK_PORE_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PF_DATA = tUBLK_PF_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_PF_DATA = tNEARBLK_PF_DATA<UBLK_TYPE_TAG>;

  __HOST__DEVICE__ constexpr static auto layout_type() { return SOLVER_LAYOUT_TYPE::INLINE_SOLVER_LAYOUT_TYPE; }

  __HOST__ VOID init(UBLK_ATTRS u, size_t) {};
  
  template<typename ATTRIBUTES_SPEC>
  static uINT64 size(UBLK_ATTRS ublk_attrs,
                     asINT32 n_phys_types,
                     STP_PHYSTYPE_TYPE* phys_types,
                     ATTRIBUTES_SPEC attribute_data) {

    //At the moment size of dyn data is invariant of block layout type
    size_t dynamics_data_size = UBLK_TYPE::template size_of_dyn_data<ATTRIBUTES_SPEC>(ublk_attrs,
                                                                                      n_phys_types,
                                                                                      phys_types,
                                                                                      attribute_data);
    uINT8 ublk_type = ublk_attrs.ublk_type();
    // Ghostblocks have only one copy of states

#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
    if(ublk_attrs.m_is_ghost) ublk_type |= (1 << TWO_STATE_COPIES_BIT);
#else
    if(ublk_attrs.m_is_ghost) ublk_type &= (~(1 << TWO_STATE_COPIES_BIT));
#endif

    return (get_ublk_data_offset(ublk_type, UBLK_DYN_DATA_TYPE) + dynamics_data_size);
  }

  uINT64 size(void* u) const {
    UBLK_TYPE* ublk = to_sdfloat_ublk(u);
    size_t dynamics_data_size = (!ublk->is_vr_fine() && !ublk->is_ghost())? ublk->size_of_dyn_data() : 0;
    return (get_ublk_data_offset(ublk->ublk_type(), UBLK_DYN_DATA_TYPE) + dynamics_data_size);
  }

  template<UBLK_DATA_TYPE data_type>
  static __HOST__DEVICE__ bool does_ublk_have_data_of_type(void* u) {
    UBLK_TYPE* ublk = to_sdfloat_ublk(u);
    return (get_ublk_data_offset(ublk->ublk_type(), data_type) > 0);
  }  
  
  __HOST__DEVICE__ static uINT16 get_ublk_data_offset(asINT32 ublk_type, asINT32 data_type) {
    return ::get_ublk_data_offset<N_VOXELS>(ublk_type,data_type);
  }

  __HOST__ static uINT16& set_ublk_data_offset(asINT32 ublk_type, asINT32 data_type) {
    return ::set_ublk_data_offset<N_VOXELS>(ublk_type,data_type);
  }

  __HOST__DEVICE__ static UBLK_TYPE* to_sdfloat_ublk(void* ublk) {
    return reinterpret_cast<UBLK_TYPE*>(ublk);
  }
  
  static __HOST__DEVICE__ char* lb_data(void* ublk) {
    return (char *) &to_sdfloat_ublk(ublk)->m_lb_data;
  }
  static __HOST__DEVICE__ char* turb_data(void* ublk, BOOLEAN has_two_copies_of_states) {
    return  ((char*) ublk + sizeof(UBLK_TYPE) + (1+has_two_copies_of_states)*sizeof(sUBLK_STATES_DATA));
  }
  static __HOST__DEVICE__ char* turb_data(void* ublk) {
    return  ((char*) ublk + sizeof(UBLK_TYPE) + (1+to_sdfloat_ublk(ublk)->has_two_copies())*sizeof(sUBLK_STATES_DATA));
  }
  static __HOST__DEVICE__ char* t_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_T_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_T_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* conduction_data(void* ublk) {
#if !BUILD_GPU
#ifdef CONDUCTION_ENABLE_MESSAGE_FOR_DISC_DEBUGGING
    if(!does_ublk_have_data_of_type<UBLK_CONDUCTION_DATA_TYPE>(ublk)){
      UBLK_TYPE* debug_ublk = to_sdfloat_ublk(ublk);
      msg_internal_error("UBLK %d does not have conduction_data", debug_ublk->id());
    }
#endif
    cassert(does_ublk_have_data_of_type<UBLK_CONDUCTION_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_CONDUCTION_DATA_TYPE));
#else
    assert(false);
#endif
  }
  static __HOST__DEVICE__ char* conduction_implicit_solver_data(void* ublk) {
#if !BUILD_GPU
    cassert(does_ublk_have_data_of_type<UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE));
#else
    assert(false);
#endif
  }
  static __HOST__DEVICE__ char* uds_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_UDS_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_UDS_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* uds_data(void* ublk, asINT32 nth_uds) {
    return ((char *)ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_UDS_DATA_TYPE) + nth_uds * sUBLK_UDS_DATA::SIZE(to_sdfloat_ublk(ublk)->has_two_copies()));
  }  
  static __HOST__DEVICE__ char* pf_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_PF_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_PF_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* mc_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_MC_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MC_DATA_TYPE));
  }

  static __HOST__DEVICE__ char* avg_mme_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_MME_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MME_DATA_TYPE));
  }

  static __HOST__DEVICE__ char* avg_mme_t_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_MME_T_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MME_T_DATA_TYPE));
  }
  
  static __HOST__DEVICE__ char* avg_mme_conduction_data(void* ublk) {
#if !BUILD_GPU
    cassert(does_ublk_have_data_of_type<UBLK_MME_CONDUCTION_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MME_CONDUCTION_DATA_TYPE));
#else
    assert(false);
#endif
  }

  static __HOST__DEVICE__ char* avg_mme_uds_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_MME_UDS_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MME_UDS_DATA_TYPE));
  }

  static __HOST__DEVICE__ char* avg_mme_uds_data(void* ublk, asINT32 nth_uds) {
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MME_UDS_DATA_TYPE) + nth_uds * sizeof(sUBLK_MME_UDS_DATA));
  }
  
  //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  static __HOST__DEVICE__ char*  p_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_PARTICLE_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_PARTICLE_DATA_TYPE));
  }
  //#endif

  static __HOST__DEVICE__ char *surf_lb_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_LB_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_LB_DATA_TYPE));
  }
  static __HOST__DEVICE__ char *surf_turb_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_TURB_DATA_TYPE>(ublk));
    return((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_TURB_DATA_TYPE));
  }
  static __HOST__DEVICE__ char *surf_t_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_T_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_T_DATA_TYPE));
  }
  static __HOST__DEVICE__ char *surf_conduction_data(void* ublk) {
#if !BUILD_GPU
    cassert(does_ublk_have_data_of_type<NEARBLK_CONDUCTION_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_CONDUCTION_DATA_TYPE));
#else
    assert(false);
#endif
  }  
  static __HOST__DEVICE__ char* surf_uds_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_UDS_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_UDS_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* surf_uds_data(void* ublk, asINT32 nth_uds) {    
    return ((char *)ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_UDS_DATA_TYPE) + nth_uds * sNEARBLK_UDS_DATA::SIZE());
  }
  static __HOST__DEVICE__ char* surf_pf_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_PF_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_PF_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* surf_mc_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_MC_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_MC_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* surf_avg_mme_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_MME_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_MME_DATA_TYPE));
  }
  static __HOST__DEVICE__ char* surf_frozen_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_FROZEN_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_FROZEN_DATA_TYPE));
  }

  //#endif
  static __HOST__DEVICE__ char *surf_geom_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<NEARBLK_GEOM_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), NEARBLK_GEOM_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *vr_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_VR_DATA_TYPE>(ublk));
    return  ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_VR_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *vr_fine_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_VR_DATA_TYPE>(ublk));
    return  ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_VR_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *vr_coarse_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_VR_DATA_TYPE>(ublk));
    return  ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_VR_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *mirror_data(void* ublk) {
    cassert(does_ublk_have_data_of_type<UBLK_MIRROR_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_MIRROR_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *dynamics_data(void* ublk)  {
    cassert(does_ublk_have_data_of_type<UBLK_DYN_DATA_TYPE>(ublk));
    return ((char*) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_DYN_DATA_TYPE));
  }

  // Double precision post advect scale factors are overlaid on states data during initialization
  char* double_precision_pas_factors(void* ublk) {
    return ((char*) ublk + sizeof(UBLK_TYPE));
  }

  static __HOST__DEVICE__ char *lb_states(void* u, asINT32 timestep_toggle) {
#ifdef DEBUG
    UBLK_TYPE* ublk = to_sdfloat_ublk(u);
    if (!ublk->has_two_copies()) {
      if (timestep_toggle == 1) {
        HOST_MSG_ERROR_OR_DEVICE_ASSERT("ublk %d only has one set of states", ublk->id());
      }
      timestep_toggle = 0;
    }
#endif
    return ((char*) u + timestep_toggle*sizeof(sUBLK_STATES_DATA) + sizeof(UBLK_TYPE));
  }

  // Smart seed data overlays the states of the ublk.
  __HOST__DEVICE__ char* smart_seed_data(void* u) {
    return lb_states(u, ONLY_ONE_COPY);
  }

  static __HOST__DEVICE__ char *pore_lb_data(void* ublk) { //5G large_pore
    cassert(does_ublk_have_data_of_type<UBLK_PORE_LB_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_PORE_LB_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *pore_mc_data(void* ublk) { //5G large_pore
    cassert(does_ublk_have_data_of_type<UBLK_PORE_MC_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(),UBLK_PORE_MC_DATA_TYPE));
  }

  static __HOST__DEVICE__ char *pore_uds_data(void* ublk) { //5G large_pore
    cassert(does_ublk_have_data_of_type<UBLK_PORE_UDS_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(),UBLK_PORE_UDS_DATA_TYPE));
  }

  static __HOST__DEVICE__ char* pore_uds_data(void* ublk, asINT32 nth_uds) {
    return ((char *)ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_PORE_UDS_DATA_TYPE) + nth_uds * sizeof(sUBLK_PORE_UDS_DATA));
  }

  static __HOST__DEVICE__ char *cbf_lb_data(void* ublk) { //conformal body force
    cassert(does_ublk_have_data_of_type<UBLK_CBF_LB_DATA_TYPE>(ublk));
    return ((char *) ublk + get_ublk_data_offset(to_sdfloat_ublk(ublk)->ublk_type(), UBLK_CBF_LB_DATA_TYPE));
  }
};


#if BUILD_GPU

/*==============================================================================
 * @enum SOLVER_BLOCK_INDEX
 * This enum list maps UBLK_DATA_TYPE to the type of array index used to
 * keep track of a UBLK's position in solver data arrays. For example multiple 
 * NEARBLK_*DATA_TYPEs map to the same SOLVER_BLOCK_INDEX type NEAR, since arrays
 * of NEARBLK_*DATA_TYPEs are assumed to be of the same length.
 *============================================================================*/
enum class SOLVER_BLOCK_INDEX {
  INVALID = -1,
  COMMON,
  NEAR,
  VR,
  N_TYPES
};

/*==============================================================================
 * @struct tUBLK_DATA_TYPE_TRAITS
 * Traits structure to hold important attributes of each UBLK_DATA_TYPE
 * tBLOCK_TYPE - the type of UBLK data associated with UBLK_DATA_TYPE
 * is_near - is data block is associated with near UBLK
 * is_vr - is data block associated with VR
 *============================================================================*/
template<UBLK_DATA_TYPE> struct tUBLK_DATA_TYPE_TRAITS;

#define DEF_UBLK_DATA_TYPE_TRAITS_IMPL(DATA_TYPE, IS_NEAR, IS_VR)       \
  template<>                                                            \
  struct tUBLK_DATA_TYPE_TRAITS<DATA_TYPE##_TYPE> {                     \
    template<size_t N_VOXELS>                                           \
    using tBLOCK_TYPE = t##DATA_TYPE<tUBLK_SDFLOAT_TYPE_TAG<N_VOXELS>>;  \
    __HOST__DEVICE__ static constexpr BOOLEAN is_near_data() { return IS_NEAR; } \
    __HOST__DEVICE__ static constexpr BOOLEAN is_vr_data() { return IS_VR; }     \
    __HOST__DEVICE__ static constexpr BOOLEAN is_common_data() {                 \
      return !is_near_data() && !is_vr_data();                          \
    }                                                                  \
};

#define DEF_UBLK_DATA_TYPE_TRAITS(DATA_TYPE) \
  DEF_UBLK_DATA_TYPE_TRAITS_IMPL(DATA_TYPE, FALSE, FALSE)
  
#define DEF_NEARBLK_DATA_TYPE_TRAITS(DATA_TYPE) \
  DEF_UBLK_DATA_TYPE_TRAITS_IMPL(DATA_TYPE, TRUE, FALSE)

template<>
struct tUBLK_DATA_TYPE_TRAITS<UBLK_DYN_DATA_TYPE> {

  template<size_t N_VOXELS>
  using tBLOCK_TYPE = tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, tUBLK_SDFLOAT_TYPE_TAG<N_VOXELS>>*;
  
  __HOST__DEVICE__ static constexpr BOOLEAN is_near_data() { return FALSE; }
  __HOST__DEVICE__ static constexpr BOOLEAN is_vr_data() { return FALSE; }
  __HOST__DEVICE__ static constexpr BOOLEAN is_common_data() { return TRUE; }
};

template<size_t N_VOXELS>
union uSOLVER_VR_INTERFACE_DATA {
  uSOLVER_VR_INTERFACE_DATA() {
    memset(this, 0, sizeof(*this));
  }
  tVR_FINE_INTERFACE_DATA<tUBLK_SDFLOAT_TYPE_TAG<N_VOXELS>> m_vr_fine_data;
  tVR_COARSE_INTERFACE_DATA<tUBLK_SDFLOAT_TYPE_TAG<N_VOXELS>> m_vr_coarse_data;
};
  
template<>
struct tUBLK_DATA_TYPE_TRAITS<UBLK_VR_DATA_TYPE> {
  template<size_t N_VOXELS>
  using tBLOCK_TYPE = uSOLVER_VR_INTERFACE_DATA<N_VOXELS>;
  __HOST__DEVICE__ static constexpr BOOLEAN is_near_data() { return FALSE; }
  __HOST__DEVICE__ static constexpr BOOLEAN is_vr_data() { return TRUE; }
  __HOST__DEVICE__ static constexpr BOOLEAN is_common_data() { return FALSE; }
};

DEF_UBLK_DATA_TYPE_TRAITS(UBLK_LB_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_TURB_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_STATES_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_T_DATA)
#if !BUILD_GPU
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_CONDUCTION_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA)
#endif
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_UDS_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_PF_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MC_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_PORE_LB_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_PORE_MC_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_PORE_UDS_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_CBF_LB_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MME_TURB_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MME_DNS_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MME_T_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MME_UDS_DATA)
#if !BUILD_GPU
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MME_CONDUCTION_DATA)
#endif
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_PARTICLE_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_GEOM_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_LB_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_TURB_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_T_DATA)
#if !BUILD_GPU
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_CONDUCTION_DATA)
#endif
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_UDS_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_PF_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_MC_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_MME_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_FROZEN_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_MIRROR_DATA)
DEF_NEARBLK_DATA_TYPE_TRAITS(NEARBLK_DP_PASS_FACTORS_DATA)
DEF_UBLK_DATA_TYPE_TRAITS(UBLK_SMART_SEED_DATA)

template<typename T>
using tUBLK_DATA_TYPE_ARRAY = std::array<T, (int) UBLK_DATA_TYPE::N_TYPES>;

/*==============================================================================
 * @struct tDEVICE_VECTOR
 * Temporary implementation to support solver blocks functionality on GPUs
 * We can replace this later on with a more complete implementation of a device vector
 *==============================================================================*/
template<typename T>
struct tDEVICE_VECTOR {

  using value_type = T;
  using pointer = T*;
  using const_pointer = const T*;
  using reference = value_type&;
  using const_reference = const value_type&;
  using iterator = value_type*;
  using const_iterator = const value_type*;
  using reverse_iterator = std::reverse_iterator<iterator>;
  using const_reverse_iterator = std::reverse_iterator<const_iterator>;
  using size_type = std::size_t;
  using difference_type = std::ptrdiff_t;  

  tDEVICE_VECTOR() = default;
  __HOST__ tDEVICE_VECTOR(const std::vector<T>& h_vector);
  
  __HOST__DEVICE__ size_t size() const {
    return m_size;
  }
  
  //Element access  
  __HOST__DEVICE__ T* data() {
    return m_data;
  }

  __HOST__DEVICE__ T& operator[] (size_t index) {
    return m_data[index];
  }

  __HOST__DEVICE__ T& at(size_t index) {
    assert(index < m_size);
    return m_data[index];
  }

  __HOST__DEVICE__ T& front() {
    return m_data[0];
  }
  
  __HOST__DEVICE__ T& back() {
    return m_data[m_size - 1];
  }
  
  __HOST__DEVICE__ VOID push_back(const T& value) {
    assert(false);
  }
 
  __HOST__ VOID reserve(size_t count) {
    m_data = GPU::malloc<T>(count, "gpu_init_solver_blocks").get();
  }
  
  __HOST__DEVICE__ VOID clear() {
    m_size = 0;
    free(m_data);
  }
  __HOST__DEVICE__ VOID shrink_to_fit() {
  }
private:
  T* m_data;
  size_t m_size;
};

/*==============================================================================
 * @struct HD_VECTOR_PTR
 * Opaque pointer, that points to device or host implementation of a vector
 * All the solver blocks can be easily be ported over to the device by just
 * modifying where m_ptr points to
 *============================================================================*/
template<typename T, typename ... ARGS>
struct HD_VECTOR_PTR {
#if DEVICE_COMPILATION_MODE
  using VECTOR_TYPE = tDEVICE_VECTOR<T>;
#else
  using VECTOR_TYPE = typename std::vector<T, ARGS...>;
#endif  

  HD_VECTOR_PTR() {
    m_ptr = new VECTOR_TYPE();
  }

  HD_VECTOR_PTR(const HD_VECTOR_PTR&) = default;
  
  __HOST__ VOID swap_ptr(void* data) {
    m_ptr = data;
  }
  
  __HOST__DEVICE__ BOOLEAN is_empty() { return m_ptr == nullptr; }
  
  __HOST__DEVICE__ auto* operator->() {
    return ptr_to_vector();
  }
  __HOST__DEVICE__ const auto* operator->() const {
    return ptr_to_vector();
  }

  __HOST__DEVICE__ VECTOR_TYPE* ptr_to_vector() {
    return (VECTOR_TYPE*) m_ptr;
  }

  __HOST__DEVICE__ VECTOR_TYPE& ref_to_vector() {
    return *((VECTOR_TYPE*) m_ptr);
  }  

  __HOST__ void* get_ptr() { return m_ptr; }
private:
  void* m_ptr;
};

class BLOCK_ASSIGNMENT_ARGS {
public:

  BLOCK_ASSIGNMENT_ARGS(const tUBLK_DATA_TYPE_ARRAY<BOOLEAN>& is_runtime_active,
                        size_t ublk_dyn_data_size):
    m_is_runtime_active(is_runtime_active),
    m_ublk_dyn_data_size(ublk_dyn_data_size) {}
  
  size_t size_of_dyn_data() const { return m_ublk_dyn_data_size; }  
  BOOLEAN is_runtime_active(UBLK_DATA_TYPE data_type) const { return m_is_runtime_active[(int) data_type]; }
    
private:
  size_t m_ublk_dyn_data_size;
  const tUBLK_DATA_TYPE_ARRAY<BOOLEAN>& m_is_runtime_active;
};

/*==============================================================================
 * @struct tSOLVER_BLOCK_FACTORY
 * Since not all blocks are manufactured the same, this abstraction lets us
 * customize the behavior
 *============================================================================*/
template<size_t N_VOXELS, UBLK_DATA_TYPE DATA_TYPE>
struct tSOLVER_BLOCK_FACTORY {
  
  using BLOCK_TYPE = typename tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>::template tBLOCK_TYPE<N_VOXELS>;
  
  static void add_new_block(HD_VECTOR_PTR<BLOCK_TYPE>& v,
                            const BLOCK_ASSIGNMENT_ARGS& args) {
    v->push_back(BLOCK_TYPE());
    memset(&v->back(), 0, sizeof(BLOCK_TYPE));
  }
};

template<size_t N_VOXELS>
struct tSOLVER_BLOCK_FACTORY<N_VOXELS, UBLK_DYN_DATA_TYPE> {
  using BLOCK_TYPE = typename tUBLK_DATA_TYPE_TRAITS<UBLK_DYN_DATA_TYPE>::tBLOCK_TYPE<N_VOXELS>;
  
  static void add_new_block(HD_VECTOR_PTR<BLOCK_TYPE>& v,
                            const BLOCK_ASSIGNMENT_ARGS& args) {
    size_t n_bytes = args.size_of_dyn_data();
    if (n_bytes) {
      void* p;
      if constexpr (N_VOXELS == N_VOXELS_8) {
        p = malloc(n_bytes);
        memset(p, 0, n_bytes);
      } else {
        //This dynamics data is going to be copied over the inline device MBLK data, hence
        //it is temporary and can be disposed. hd_slab_malloc returns 0 initialized mem
        p = hd_slab_malloc(HD_SLAB_ALLOCATOR_TYPES::TMP_INIT_DATA, n_bytes, 32 /*alignment*/);
      }
      v->push_back((BLOCK_TYPE) p);
    } else {
      //Solid ublks or other special cases like VR_FINE
      v->push_back((BLOCK_TYPE) nullptr);
    }
  }
};

//Assume we have a million voxels associated with each solver block type
template<size_t N_VOXELS, size_t REDUCTION_FACTOR = 1>
constexpr size_t reserve_block_count() { return (1024 * 1024) / N_VOXELS / REDUCTION_FACTOR; }

template<size_t N_VOXELS, typename TRAITS, typename T>
INLINE VOID reserve_blocks(HD_VECTOR_PTR<T>& v) {
  if constexpr (TRAITS::is_common_data()) {    
    v->reserve(reserve_block_count<N_VOXELS>());
  } else if constexpr (TRAITS::is_near_data()) {
    v->reserve(reserve_block_count<N_VOXELS, 2>());
  } else if constexpr (TRAITS::is_vr_data()) {
    v->reserve(reserve_block_count<N_VOXELS, 4>());
  } else {
    msg_internal_error("Invalid traits type");
  }
}

/*==============================================================================
 * @struct tSOLVER_BLOCKS
 * Templated on UBLK_DATA_TYPES, each iteration of the tSOLVER_BLOCK type list
 * stores an underlying vector of data associated with each UBLK_DATA_TYPE
 *============================================================================*/
template<size_t N_VOXELS, UBLK_DATA_TYPE ... UBLK_DATA_TYPES>
class tSOLVER_BLOCKS;

//Specialization single data type
template<size_t N_VOXELS, UBLK_DATA_TYPE DATA_TYPE>
class tSOLVER_BLOCKS<N_VOXELS, DATA_TYPE> {

public:

  using SOLVER_BLOCK_DATA_TYPE = typename tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>::template tBLOCK_TYPE<N_VOXELS>;
  using TRAITS = tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>;
  using BLOCK_FACTORY = tSOLVER_BLOCK_FACTORY<N_VOXELS, DATA_TYPE>;
  
  template<UBLK_DATA_TYPE E>
  __HOST__DEVICE__ SOLVER_BLOCK_DATA_TYPE* get_data(size_t index) {
    if constexpr(E == DATA_TYPE) {
      cassert(index < m_data->size());
      return m_data->data() + index;
    } else {
      HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT("UBLK data type %d was not configured as part of solver blocks", (int) DATA_TYPE);
      return nullptr; //Pointless but to suppress missing return statement warning      
    }
  }  
  void reserve() {
    reserve_blocks<N_VOXELS, TRAITS>(m_data);
  }
  void assign_common_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if (TRAITS::is_common_data() && args.is_runtime_active(DATA_TYPE)) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
  };
  void assign_near_solver_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if (TRAITS::is_near_data() && args.is_runtime_active(DATA_TYPE)) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
  };
  void assign_vr_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if constexpr(TRAITS::is_vr_data()) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
  };
  
  template<UBLK_DATA_TYPE E>
  static constexpr BOOLEAN has_data_of_type() {
    return E == DATA_TYPE;
  }

  template<UBLK_DATA_TYPE E>
  VOID clear_data_of_type() {
    if constexpr(E == DATA_TYPE || E == N_TYPES){
      m_data->clear(); m_data->shrink_to_fit();
    }
  }

  template<UBLK_DATA_TYPE E>
  auto& ref_to_data() {
    return m_data;
  }
private:
  HD_VECTOR_PTR<SOLVER_BLOCK_DATA_TYPE> m_data;
};

//Generic implementation
template<size_t N_VOXELS,
         UBLK_DATA_TYPE DATA_TYPE,
         UBLK_DATA_TYPE ... REST>
class tSOLVER_BLOCKS<N_VOXELS, DATA_TYPE, REST...> : public tSOLVER_BLOCKS<N_VOXELS, REST...> {
  
public:

  using TRAITS = tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>;
  using BASE_TYPE = tSOLVER_BLOCKS<N_VOXELS, REST...>;
  using SOLVER_BLOCK_DATA_TYPE = typename TRAITS::template tBLOCK_TYPE<N_VOXELS>;
  using BLOCK_FACTORY = tSOLVER_BLOCK_FACTORY<N_VOXELS, DATA_TYPE>;
  
  template<UBLK_DATA_TYPE E>
  __HOST__DEVICE__ auto* get_data(size_t index) {
    if constexpr(E == DATA_TYPE) {
      cassert(index < m_data->size());
      return m_data->data() + index;
    } else {
      return static_cast<BASE_TYPE*>(this)->template get_data<E>(index);
    }
  }

  void reserve() {
    reserve_blocks<N_VOXELS, TRAITS>(m_data);
    static_cast<BASE_TYPE*>(this)->reserve();
  }
  
  void assign_common_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if (TRAITS::is_common_data() && args.is_runtime_active(DATA_TYPE)) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
    static_cast<BASE_TYPE*>(this)->assign_common_blocks(args);
  };
  
  void assign_near_solver_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if (TRAITS::is_near_data() && args.is_runtime_active(DATA_TYPE)) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
    static_cast<BASE_TYPE*>(this)->assign_near_solver_blocks(args);
  };

  void assign_vr_blocks(const BLOCK_ASSIGNMENT_ARGS& args) {
    if constexpr(TRAITS::is_vr_data()) {
      BLOCK_FACTORY::add_new_block(m_data, args);
    }
    static_cast<BASE_TYPE*>(this)->assign_vr_blocks(args);
  };

  template<UBLK_DATA_TYPE E>
  static constexpr BOOLEAN has_data_of_type() {
    return E == DATA_TYPE? TRUE : BASE_TYPE::template has_data_of_type<E>();
  }

  template<UBLK_DATA_TYPE E>
  void clear_data_of_type() {
    if constexpr(E == DATA_TYPE) {
      m_data->clear(); m_data->shrink_to_fit();
    }
    else {
      if (E == N_TYPES) { m_data->clear(); m_data->shrink_to_fit(); }
      static_cast<BASE_TYPE*>(this)->template clear_data_of_type<E>();
    }
  }

  template<UBLK_DATA_TYPE E>
  auto& ref_to_data() {
    if constexpr (E == DATA_TYPE) { return m_data; }
    else { return static_cast<BASE_TYPE*>(this)->template ref_to_data<E>(); }
  }
private:
  HD_VECTOR_PTR<SOLVER_BLOCK_DATA_TYPE> m_data;
};

/*==============================================================================
 * @struct tSOLVER_BLOCKS_MANAGER
 * A global instance of this class is used to keep track of arrays of solver
 * blocks for all UBLKs
 *============================================================================*/
template<size_t N_VOXELS, UBLK_DATA_TYPE ... DATA_TYPES>
class tSOLVER_BLOCKS_MANAGER {
  
public:
  using SOLVER_BLOCKS_TYPE = tSOLVER_BLOCKS<N_VOXELS, DATA_TYPES...>;

  tSOLVER_BLOCKS_MANAGER()=default;
  tSOLVER_BLOCKS_MANAGER(const tSOLVER_BLOCKS_MANAGER& other)=default;

  VOID init();
  
  INLINE size_t assign_common_data(const BLOCK_ASSIGNMENT_ARGS& args) {
    m_solver_blocks.assign_common_blocks(args);
    return m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::COMMON]++;
  }
  
  INLINE size_t assign_near_data(const BLOCK_ASSIGNMENT_ARGS& args) {
    m_solver_blocks.assign_near_solver_blocks(args);
    return m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::NEAR]++;
  }

  INLINE size_t assign_vr_data(const BLOCK_ASSIGNMENT_ARGS& args) {
    m_solver_blocks.assign_vr_blocks(args);
    return m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::VR]++;
  }

  template<UBLK_DATA_TYPE E>
  __HOST__DEVICE__ auto* get_data(size_t index) {
    return this->m_solver_blocks.template get_data<E>(index);
  }

  template<UBLK_DATA_TYPE E>
  static constexpr BOOLEAN has_data_of_type() {
    return SOLVER_BLOCKS_TYPE::template has_data_of_type<E>();
  }

  template<UBLK_DATA_TYPE E>
  VOID clear_data_of_type() {
    m_solver_blocks.template clear_data_of_type<E>();
  }

  VOID clear_all_data() {
    return m_solver_blocks.template clear_data_of_type<UBLK_DATA_TYPE::N_TYPES>();
  }  

  template<UBLK_DATA_TYPE E>
  auto& ref_to_data() {
    static_assert(has_data_of_type<E>(), "Reference to invalid data requested");
    return m_solver_blocks.template ref_to_data<E>();
  }
  
  const tUBLK_DATA_TYPE_ARRAY<BOOLEAN>& runtime_active_info() const {
    return m_is_solver_block_runtime_active;
  }
private:
  SOLVER_BLOCKS_TYPE m_solver_blocks;
  //Current running count of different array sizes
  std::array<std::size_t, (int) SOLVER_BLOCK_INDEX::N_TYPES> m_solver_block_indices;
  tUBLK_DATA_TYPE_ARRAY<BOOLEAN> m_is_solver_block_runtime_active;
};

template<size_t N_VOXELS, UBLK_DATA_TYPE ... DATA_TYPES>
INLINE VOID tSOLVER_BLOCKS_MANAGER<N_VOXELS, DATA_TYPES...>::init() {
  
  for (size_t& index : m_solver_block_indices) { index = 0; }

  m_solver_blocks.reserve();
  
  if (sim.is_lb_model) {
    m_is_solver_block_runtime_active[UBLK_LB_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[UBLK_STATES_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[NEARBLK_LB_DATA_TYPE] = TRUE;
  }
  
  if (sim.is_turb_model) {
     m_is_solver_block_runtime_active[UBLK_TURB_DATA_TYPE] = TRUE;
     m_is_solver_block_runtime_active[NEARBLK_TURB_DATA_TYPE] = TRUE;
  }

#if BUILD_D19_LATTICE  
  if (sim.has_avg_mme_window) {
    m_is_solver_block_runtime_active[UBLK_MME_TURB_DATA_TYPE] = sim.is_turb_model;
    m_is_solver_block_runtime_active[UBLK_MME_DNS_DATA_TYPE] = !sim.is_turb_model;
    m_is_solver_block_runtime_active[UBLK_MME_T_DATA_TYPE] = sim.is_heat_transfer;
    m_is_solver_block_runtime_active[UBLK_MME_UDS_DATA_TYPE] = sim.is_scalar_model;
#if !BUILD_GPU
    m_is_solver_block_runtime_active[UBLK_MME_CONDUCTION_DATA_TYPE] = sim.is_conduction_model;
#endif
  }

  if (sim.store_frozen_vars) {
    m_is_solver_block_runtime_active[NEARBLK_FROZEN_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[NEARBLK_MME_DATA_TYPE] = sim.has_avg_mme_window;
  }
#endif
  
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
    m_is_solver_block_runtime_active[UBLK_T_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[NEARBLK_T_DATA_TYPE] = TRUE;
  }
  
  if (sim.is_scalar_model) {
    m_is_solver_block_runtime_active[UBLK_UDS_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[NEARBLK_UDS_DATA_TYPE] = TRUE;
    if (sim.is_pf_model) {
      m_is_solver_block_runtime_active[UBLK_PF_DATA_TYPE] = TRUE;
      m_is_solver_block_runtime_active[NEARBLK_PF_DATA_TYPE] = TRUE;
    }
  }

#if BUILD_5G_LATTICE  
  if (sim.is_5g_sim) {
    m_is_solver_block_runtime_active[UBLK_MC_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[NEARBLK_MC_DATA_TYPE] = TRUE;
  }

  if (sim.is_large_pore) {
    m_is_solver_block_runtime_active[UBLK_PORE_LB_DATA_TYPE] = TRUE;
    m_is_solver_block_runtime_active[UBLK_PORE_MC_DATA_TYPE] = TRUE;
    if (sim.is_scalar_model) 
      m_is_solver_block_runtime_active[UBLK_PORE_UDS_DATA_TYPE] = TRUE;
  }

  if(g_mp_meas_seed_pres || g_mp_meas_write_pres)
    m_is_solver_block_runtime_active[UBLK_CBF_LB_DATA_TYPE] = TRUE;
#endif

#if BUILD_D19_LATTICE
  if(g_meas_seed_pres_d19 || g_meas_write_pres_d19){
    m_is_solver_block_runtime_active[UBLK_CBF_LB_DATA_TYPE] = TRUE;
  }
#endif
  
  if (sim.is_particle_model) {
    m_is_solver_block_runtime_active[UBLK_PARTICLE_DATA_TYPE] = TRUE;
  }

  if (sim.do_smart_seed) {
    m_is_solver_block_runtime_active[UBLK_SMART_SEED_DATA_TYPE] = TRUE;
  }
  
  //Solver independent
  m_is_solver_block_runtime_active[UBLK_DYN_DATA_TYPE] = TRUE;
  m_is_solver_block_runtime_active[UBLK_MIRROR_DATA_TYPE] = TRUE;
  m_is_solver_block_runtime_active[UBLK_VR_DATA_TYPE] = TRUE;
  m_is_solver_block_runtime_active[NEARBLK_GEOM_DATA_TYPE] = TRUE;
  m_is_solver_block_runtime_active[NEARBLK_DP_PASS_FACTORS_DATA_TYPE] = TRUE;
}

/*==============================================================================
 * @struct tSOLVER_BLOCKS_CLIENT
 *============================================================================*/

template<typename SOLVER_BLOCKS_MANAGER_TYPE>
__HOST__DEVICE__ SOLVER_BLOCKS_MANAGER_TYPE* get_solver_blocks_manager();

template<size_t N_VOXELS, UBLK_DATA_TYPE ... DATA_TYPES>
class tSOLVER_BLOCKS_CLIENT {
  
private:
  std::array<std::size_t, (size_t) SOLVER_BLOCK_INDEX::N_TYPES> m_solver_block_indices;
  
public:
  template<size_t N_VOXELS_> using _tSOLVER_BLOCKS_CLIENT_ = tSOLVER_BLOCKS_CLIENT<N_VOXELS, DATA_TYPES...>;
  //using UBLK_TYPE = tUBLK<tUBLK_TYPE_TAG<N_VOXELS, sdFLOAT_UBLK, _tSOLVER_BLOCKS_CLIENT_>>;
  using UBLK_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS, sdFLOAT_UBLK, _tSOLVER_BLOCKS_CLIENT_>;
  using UBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using sUBLK_UDS_DATA = tUBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_UDS_DATA = tNEARBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using UBLK_ATTRS = tUBLK_ATTRS<N_VOXELS>;
  using MANAGER_TYPE = tSOLVER_BLOCKS_MANAGER<N_VOXELS, DATA_TYPES...>;
  __HOST__DEVICE__ constexpr static auto layout_type() { return SOLVER_LAYOUT_TYPE::ARRAY_OF_SOLVER_BLOCKS_LAYOUT_TYPE; }

  __HOST__ VOID init(UBLK_ATTRS u,
                     size_t ublk_dyn_data_size) {
    auto args = BLOCK_ASSIGNMENT_ARGS(get_solver_blocks_manager<MANAGER_TYPE>()->runtime_active_info(),
                                      ublk_dyn_data_size);
    m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::COMMON] =
      get_solver_blocks_manager<MANAGER_TYPE>()->assign_common_data(args);
    
    if (u.m_does_interact_with_surface) {
      m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::NEAR] =
        get_solver_blocks_manager<MANAGER_TYPE>()->assign_near_data(args);
    }
    if (u.m_is_vr_fine || u.m_is_vr_coarse) {
      m_solver_block_indices[(int) SOLVER_BLOCK_INDEX::VR] =
        get_solver_blocks_manager<MANAGER_TYPE>()->assign_vr_data(args);
    }
  }
  
  __HOST__DEVICE__ static UBLK_TYPE* to_sdfloat_ublk(void* ublk) {
    return reinterpret_cast<UBLK_TYPE*>(ublk);
  }
  
  template<UBLK_DATA_TYPE T>
  __HOST__DEVICE__ auto get_data() {
    constexpr int index = get_solver_index<T>();
    int ublk_index_in_block_array = this->m_solver_block_indices[index];
    return get_solver_blocks_manager<MANAGER_TYPE>()->template get_data<T>(ublk_index_in_block_array);
  }

  __HOST__DEVICE__ char* lb_data(void* ublk) {
    return (char*) get_data<UBLK_LB_DATA_TYPE>();
  }
  
  __HOST__DEVICE__ char* turb_data(void* ublk, BOOLEAN has_two_copies_of_states) {
    assert(false); //Two copies is currently not supported
    return (char*) get_data<UBLK_TURB_DATA_TYPE>();
  }
  
  __HOST__DEVICE__ char* turb_data(void* ublk) {
    return (char*) get_data<UBLK_LB_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* t_data(void* ublk) {
    return (char*) get_data<UBLK_T_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* conduction_data(void* ublk) {
#if !BUILD_GPU
    return (char*) get_data<UBLK_CONDUCTION_DATA_TYPE>();
#else
    assert(false);
#endif
  }
  __HOST__DEVICE__ char* conduction_implicit_solver_data(void* ublk) {
#if !BUILD_GPU
    return (char*) get_data<UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE>();
#else
    assert(false);
#endif
  }
   __HOST__DEVICE__ char* uds_data(void* ublk) {
    return (char*) get_data<UBLK_UDS_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* uds_data(void* ublk, asINT32 nth_uds) {
    assert(false);  //N copies is currently not supported
    return (char*) get_data<UBLK_UDS_DATA_TYPE>();

  }
  __HOST__DEVICE__ char* pf_data(void* ublk) {
    return (char*) get_data<UBLK_PF_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* mc_data(void* ublk) {
    return (char*) get_data<UBLK_MC_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* avg_mme_data(void* ublk) {
    auto& simc = get_simc_ref();
    return simc.is_turb_model? (char*) get_data<UBLK_MME_TURB_DATA_TYPE>() : (char*) get_data<UBLK_MME_DNS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* avg_mme_t_data(void* ublk) {
    return (char*) get_data<UBLK_MME_T_DATA_TYPE>();
  }
  
  __HOST__DEVICE__ char* avg_mme_conduction_data(void* ublk) {
#if !BUILD_GPU
    return (char*) get_data<UBLK_MME_CONDUCTION_DATA_TYPE>();
#else
    assert(false);
#endif
  }

  __HOST__DEVICE__ char* avg_mme_uds_data(void* ublk) {
    return (char*) get_data<UBLK_MME_UDS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* avg_mme_uds_data(void* ublk, asINT32 nth_uds) {
    assert(false);  //N copies is currently not supported
    return (char*) get_data<UBLK_MME_UDS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char*  p_data(void* ublk) {
    return (char*) get_data<UBLK_PARTICLE_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *surf_turb_data(void* ublk) {
    return (char*) get_data<NEARBLK_TURB_DATA_TYPE>();
  }
  __HOST__DEVICE__ char *surf_t_data(void* ublk) {
    return (char*) get_data<NEARBLK_T_DATA_TYPE>();
  }
  __HOST__DEVICE__ char *surf_conduction_data(void* ublk) {
#if !BUILD_GPU
    return (char*) get_data<NEARBLK_CONDUCTION_DATA_TYPE>();
#else
    assert(false);
#endif
  }
  __HOST__DEVICE__ char* surf_uds_data(void* ublk) {
    return (char*) get_data<NEARBLK_UDS_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* surf_uds_data(void* ublk, asINT32 nth_uds) {
    assert(false); //N copies is currently not supported
    return (char*) get_data<NEARBLK_UDS_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* surf_pf_data(void* ublk) {
    return (char*) get_data<NEARBLK_PF_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* surf_mc_data(void* ublk) {
    return (char*) get_data<NEARBLK_MC_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* surf_avg_mme_data(void* ublk) {
    return (char*) get_data<NEARBLK_MME_DATA_TYPE>();
  }
  __HOST__DEVICE__ char* surf_frozen_data(void* ublk) {
    return (char*) get_data<NEARBLK_FROZEN_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *mirror_data(void* ublk) {
    return (char*) get_data<UBLK_MIRROR_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *lb_states(void* ublk, asINT32 timestep_toggle) {
    return (char*) get_data<UBLK_STATES_DATA_TYPE>();
  }
  
  __HOST__DEVICE__ char* vr_fine_data(void* ublk) {
    return (char*) get_data<UBLK_VR_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* vr_coarse_data(void* ublk) {
    return (char*) get_data<UBLK_VR_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *vr_data(void* ublk) {
    return (char*) get_data<UBLK_VR_DATA_TYPE>();
  }
  
  __HOST__DEVICE__ char* surf_geom_data(void* ublk) {
    return (char*) get_data<NEARBLK_GEOM_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* surf_lb_data(void* ublk) {
    return (char*) get_data<NEARBLK_LB_DATA_TYPE>();
  }  

  __HOST__DEVICE__ char* double_precision_pas_factors(void* ublk) {
    return (char*) get_data<NEARBLK_DP_PASS_FACTORS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* smart_seed_data(void* ublk) {
    return (char*) get_data<UBLK_SMART_SEED_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* dynamics_data(void* ublk)  {
    //get_data returns a DYN_DATA** since its stored as an array of pointers
    auto* dyn_data = *get_data<UBLK_DYN_DATA_TYPE>();
    cassert(dyn_data); //can be null for vr fine or solid ublks
    return (char*) (dyn_data);
  }

  __HOST__DEVICE__ char *pore_lb_data(void* ublk) { //5G large_pore
    return (char*) get_data<UBLK_PORE_LB_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *pore_mc_data(void* ublk) { //5G large_pore
    return (char*) get_data<UBLK_PORE_MC_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *pore_uds_data(void* ublk) { //5G large_pore
    return (char*) get_data<UBLK_PORE_UDS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char* pore_uds_data(void* ublk, asINT32 nth_uds) {
    assert(false);  //N copies is currently not supported
    return (char*) get_data<UBLK_PORE_UDS_DATA_TYPE>();
  }

  __HOST__DEVICE__ char *cbf_lb_data(void* ublk) { //conformal body force
    return (char*) get_data<UBLK_CBF_LB_DATA_TYPE>();
  }

  template<UBLK_DATA_TYPE data_type>
  static __HOST__DEVICE__ bool does_mgr_have_data_of_type() {
    constexpr bool is_compile_time_active = MANAGER_TYPE::template has_data_of_type<data_type>();    
    bool is_runtime_active = get_solver_blocks_manager<MANAGER_TYPE>()->runtime_active_info()[data_type];
    return is_compile_time_active && is_runtime_active;
  }
  
  template<UBLK_DATA_TYPE data_type>
  static __HOST__DEVICE__ bool does_ublk_have_data_of_type(void* u) {
    using TRAITS = tUBLK_DATA_TYPE_TRAITS<data_type>;
    UBLK_TYPE* ublk = to_sdfloat_ublk(u);
    return does_mgr_have_data_of_type<data_type>() &&
      (ublk->is_near_surface() && TRAITS::is_near_data()) ||
      ((ublk->is_vr_fine() || ublk->is_vr_coarse()) && TRAITS::is_vr_data()) ||
      (TRAITS::is_common_data());
  }

  template<typename ATTRIBUTES_SPEC>
  static uINT64 size(UBLK_ATTRS ublk_attrs,
                     asINT32 n_phys_types,
                     STP_PHYSTYPE_TYPE* phys_types,
                     ATTRIBUTES_SPEC attribute_data) {
    return sizeof(UBLK_TYPE);
  }
  
  uINT64 size(void* u) const {
    return sizeof(UBLK_TYPE);
  }
  
private:
  template<UBLK_DATA_TYPE DATA_TYPE>
  constexpr static int get_solver_index() {
    using TRAITS = tUBLK_DATA_TYPE_TRAITS<DATA_TYPE>;
    if constexpr (TRAITS::is_near_data()) {
      return (int) SOLVER_BLOCK_INDEX::NEAR;
    }
    else if constexpr (TRAITS::is_vr_data()) {
      return (int) SOLVER_BLOCK_INDEX::VR;
    }
    else {
      return (int) SOLVER_BLOCK_INDEX::COMMON;
    }
  }

};


using HOST_UBLK_SOLVER_BLOCKS = tSOLVER_BLOCKS_CLIENT<N_VOXELS_8,
                                                      UBLK_SMART_SEED_DATA_TYPE,
                                                      NEARBLK_GEOM_DATA_TYPE,
                                                      NEARBLK_LB_DATA_TYPE,
                                                      NEARBLK_DP_PASS_FACTORS_DATA_TYPE,
                                                      UBLK_VR_DATA_TYPE,                                                       
                                                      UBLK_DYN_DATA_TYPE>;

//Double precision pass factors only needed for microblock initialization
using HOST_MBLK_SOLVER_BLOCKS = tSOLVER_BLOCKS_CLIENT<N_VOXELS_64,
                                                      UBLK_SMART_SEED_DATA_TYPE,
                                                      NEARBLK_GEOM_DATA_TYPE,
                                                      NEARBLK_LB_DATA_TYPE,
                                                      UBLK_VR_DATA_TYPE,
                                                      UBLK_DYN_DATA_TYPE>;

extern typename HOST_UBLK_SOLVER_BLOCKS::MANAGER_TYPE g_host_ublk_solver_blocks;
extern typename HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE g_host_mblk_solver_blocks;

template<size_t N_VOXELS>
using tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT_IMPL = typename std::conditional<N_VOXELS == N_VOXELS_8,
                                                                            HOST_UBLK_SOLVER_BLOCKS,
                                                                            HOST_MBLK_SOLVER_BLOCKS>::type;

template<size_t N_VOXELS>
struct tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT : public tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT_IMPL<N_VOXELS> {};

namespace GPU {
//The use of g_device_solver_blocks is a misnomer, the solver blocks have to be ported to the device
//for the purposes of initialization, hence a device variation of what we see on the host
__DEVICE__ extern typename HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE* g_device_mblk_solver_blocks;
VOID set_global_solver_blocks_mgr(HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE* d_mblk_solver_blocks_mgr);
}

//Weirdly auto doesnt work for return type
template<> __HOST__DEVICE__ INLINE
decltype(g_host_ublk_solver_blocks)* get_solver_blocks_manager<decltype(g_host_ublk_solver_blocks)>() {
#if DEVICE_COMPILATION_MODE  
  assert(false); return nullptr;
#else  
  return &g_host_ublk_solver_blocks;
#endif
}

template<> __HOST__DEVICE__ INLINE
decltype(g_host_mblk_solver_blocks)* get_solver_blocks_manager<decltype(g_host_mblk_solver_blocks)>() {
#if DEVICE_COMPILATION_MODE  
  return GPU::g_device_mblk_solver_blocks;
#else  
  return &HD_NAMESPACE::g_host_mblk_solver_blocks;
#endif  
}

INLINE VOID init_gpu_host_ublk_data_managers() {
  get_solver_blocks_manager<decltype(g_host_ublk_solver_blocks)>()->init();
  get_solver_blocks_manager<decltype(g_host_mblk_solver_blocks)>()->init();
}

template<UBLK_DATA_TYPE ... DATA_TYPES>
tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...> copy_mblk_solver_blocks_h2d(tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& solver_blocks_mgr);

template<UBLK_DATA_TYPE ... DATA_TYPES>
void free_mblk_solver_blocks(tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& h_solver_blocks_mgr,
                             tSOLVER_BLOCKS_MANAGER<N_VOXELS_64, DATA_TYPES...>& d_mblk_solver_blocks_mgr);

#endif   //BUILD_GPU

template<typename SOLVER_BLOCK_LAYOUT>
INLINE constexpr BOOLEAN is_inline_layout() {
  return SOLVER_BLOCK_LAYOUT::layout_type() == SOLVER_LAYOUT_TYPE::INLINE_SOLVER_LAYOUT_TYPE;
}

#endif //UBLK_SOLVER_DATA_ACCESS_H
