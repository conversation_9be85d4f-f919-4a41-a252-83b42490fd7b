/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Surfel dynamics (functional sets and groups)
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SURFEL_DYN_H
#define _SIMENG_SURFEL_DYN_H

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "mlrf_depots.h"
#include TURB_SYNTH_SIM_H
#include "sp_timers.h"
#include "surfel_process_control.h"
#include "tagged_ptr.h"

#include "gpu_surfel_bc_values.h"

struct sSURFEL_RECV_GROUP;

/*--------------------------------------------------------------------------*
 * EVEN_ODD_SURFEL_QUANTUM
 *--------------------------------------------------------------------------*/
template<typename SFL_TYPE_TAG>
struct tCLONE_SURFEL_TRAITS {
  
  constexpr static auto ALIGNMENT = tSURFEL<SFL_TYPE_TAG>::ALIGNMENT;
  using PTR = void*;
  
  enum ATTRIBUTES {
    HAS_NO_V2S_WEIGHTS = 0,
    CHILD_SFL = 1,
    N
  };

  __HOST__DEVICE__ constexpr static size_t num_bits_for_attrib(ATTRIBUTES a) {
    switch(a) {
    case HAS_NO_V2S_WEIGHTS:
      return 1;
    case CHILD_SFL:
      return SFL_TYPE_TAG::is_msfl()? 6 : 0;
    default:
      return 0;
    }
  }
};

template<typename SFL_TYPE_TAG>
union tCLONE_SURFEL {
  
  EXTRACT_SURFEL_TRAITS
  
private:
  
  using TRAITS = tCLONE_SURFEL_TRAITS<SFL_TYPE_TAG>;
  using TAGGED_PTR = tTAGGED_PTR<TRAITS>;
  using sSURFEL = tSURFEL<SFL_TYPE_TAG>;
  
  STP_SURFEL_ID m_clone_surfel_id;  // An ID may live here temporarily until we can convert it into a pointer
  TAGGED_PTR m_ptr;
  
  public:
  
  __HOST__ tCLONE_SURFEL(): m_ptr(nullptr) {}

  __HOST__ tCLONE_SURFEL(void* const ptr): m_ptr(ptr) {}
  
  __HOST__ void* masked_ptr() const { return m_ptr.masked_ptr(); } 

  __HOST__ void set_masked_ptr(void* ptr) { m_ptr.set_masked_ptr(ptr); }

  __HOST__DEVICE__ BOOLEAN is_empty() { return m_ptr == nullptr; }
  
  __HOST__DEVICE__ auto clone_surfel() {
    return (sSURFEL *) m_ptr.ptr();
  }
  
  __HOST__DEVICE__ STP_SURFEL_ID clone_surfel_id() {
    return m_clone_surfel_id;
  }
  
  __HOST__DEVICE__ BOOLEAN has_no_v2s_weights() {
    return m_ptr.template get<TRAITS::HAS_NO_V2S_WEIGHTS>() != 0;
  }
  
  __HOST__DEVICE__ VOID set_has_no_v2s_weights() {
    m_ptr.template set<TRAITS::HAS_NO_V2S_WEIGHTS>(1);
  }
  
  __HOST__ VOID set_clone_surfel(sSURFEL *surfel) {
    m_ptr.clear();
    m_ptr.set_ptr((void*) surfel);
  }

  __HOST__ VOID set_clone_surfel_offset(uintptr_t offset) {
    m_ptr.template set<TRAITS::CHILD_SFL>(offset);
  }

  __HOST__DEVICE__ uint8_t get_offset_in_msfl() const {
    return m_ptr.template get<TRAITS::CHILD_SFL>();
  }  
  
  __HOST__ VOID set_clone_surfel_id(STP_SURFEL_ID surfel_id) {
    m_clone_surfel_id = surfel_id;
  }
};

inline namespace
SIMULATOR_NAMESPACE {
template<typename SFL_TYPE_TAG>
struct tSURFEL_EVEN_ODD_DATA;

template<>
struct tSURFEL_EVEN_ODD_DATA<SFL_SDFLOAT_TYPE_TAG> {
  
  tCLONE_SURFEL<SFL_SDFLOAT_TYPE_TAG>  m_clone_surfel;

  __HOST__DEVICE__
  tCLONE_SURFEL<SFL_SDFLOAT_TYPE_TAG>& clone_surfel(asINT32 unused) { return m_clone_surfel; }
};

template<>
struct tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG> {  
  tCLONE_SURFEL<MSFL_SDFLOAT_TYPE_TAG>  m_clone_surfel[N_SFLS_PER_MSFL];

  __HOST__DEVICE__
  tCLONE_SURFEL<MSFL_SDFLOAT_TYPE_TAG>& clone_surfel(asINT32 index) { return m_clone_surfel[index]; }
  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG>* h_ptr);
};
  
using sSURFEL_EVEN_ODD_DATA = tSURFEL_EVEN_ODD_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_EVEN_ODD_DATA = sSURFEL_EVEN_ODD_DATA*;  
}


// SLRF surfel group/quantum definitions
#include "slrf.h"
#include "mlrf.h"

using eDYN_SURFEL_TYPE = enum {
  INVALID_SURFEL_TYPE = -1,

  // adiabatic wall types
  SLIP_SURFEL_TYPE,
  NOSLIP_SURFEL_TYPE,
  ANGULAR_SLIP_SURFEL_TYPE,
  LINEAR_SLIP_SURFEL_TYPE,
  VEL_SLIP_SURFEL_TYPE,
  ANGULAR_NOSLIP_SURFEL_TYPE,
  LINEAR_NOSLIP_SURFEL_TYPE,

  // thermal resist wall types
  SLIP_THERMAL_RESIST_SURFEL_TYPE,
  NOSLIP_THERMAL_RESIST_SURFEL_TYPE,
  ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE,
  LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE,
  ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE,
  LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE,

  // isothermal (fixed temperature) wall types
  SLIP_FIXED_TEMP_SURFEL_TYPE,
  NOSLIP_FIXED_TEMP_SURFEL_TYPE,
  ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE,
  LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE,
  ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE,
  LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE,

  // heat flux wall types
  SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,
  NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,
  ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,
  LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,
  ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,
  LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE,

  // outlets pressure
  STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE,
  STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE,
  STAG_PRESSURE_FREE_DIR_SURFEL_TYPE,
  STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE,

  // inlets (velocity, mass flux, mass flow)
  MASS_FLUX_SURFEL_TYPE,
  MASS_FLOW_SURFEL_TYPE,
  FIXED_VEL_SURFEL_TYPE,
  TURB_VEL_SURFEL_TYPE,
  SOURCE_SURFEL_TYPE,
  TURB_SOURCE_SURFEL_TYPE,

  PASS_THRU_SURFEL_TYPE,

  // sampling surfels
  SAMPLING_DYN_SURFEL_TYPE,

  // LRF surfels
  SLRF_SURFEL_TYPE,

  // Conduction surfels (fixed BCs)
  CONDUCTION_ADIABATIC_SURFEL_TYPE,
  CONDUCTION_FIXED_TEMP_SURFEL_TYPE,
  CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE,
  CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE,
  
  // Conduction surfels (coupled BC)
  CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE,
  CONDUCTION_CONTACT_SURFEL_TYPE,

  N_SURFEL_DYN_TYPES
};

template<eDYN_SURFEL_TYPE E>
constexpr bool is_inlet_outlet_type() {
  return (E == STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE)
  || (E == STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE)
  || (E == STAG_PRESSURE_FREE_DIR_SURFEL_TYPE)
  || (E == STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE)
  || (E == MASS_FLUX_SURFEL_TYPE)
  || (E == MASS_FLOW_SURFEL_TYPE)
  || (E == FIXED_VEL_SURFEL_TYPE)
  || (E == TURB_VEL_SURFEL_TYPE)
  || (E == SOURCE_SURFEL_TYPE)
  || (E == TURB_SOURCE_SURFEL_TYPE);
}

//Flow & conduction have separate dynamic traits
template<eDYN_SURFEL_TYPE E>
constexpr BOOLEAN is_flow_dynamics_type() {
  return (E < CONDUCTION_ADIABATIC_SURFEL_TYPE);
}

template<eDYN_SURFEL_TYPE E>
constexpr BOOLEAN is_conduction_dynamics_type() {
  return (E >= CONDUCTION_ADIABATIC_SURFEL_TYPE);
}

template<eDYN_SURFEL_TYPE E>
constexpr BOOLEAN is_radiation_dynamics_type() {
  return E != CONDUCTION_CONTACT_SURFEL_TYPE;
}

// Subset of flow surfel dynamics types that only includes slip/noslip.
// This is used in conduction_interface_dyn.cc to call dynamics of flow wsurfels
template<eDYN_SURFEL_TYPE E>
constexpr BOOLEAN is_flow_sampling_dynamics_type() {
  return (E < STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE);
}

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/
VOID exchange_all_slrf_s2s_and_v2s_factors(VOID);

VOID do_normalize_surfel_dist(VOID);

#define MAX_SLIP_VELOCITY_COSINE_DEVIATION  0.043619387 /* cos(87.5 degrees)      */
#define DEFAULT_FIXED_VEL_BC_OMEGA_RHO .002
#define PASS_THRU_BC_OMEGA_RHO	0.01
#define PASS_THRU_BC_OMEGA_VEL	0.01
#define INVALID_FACE_INDEX (-1)

typedef struct sANGULAR_MOVING_SURFEL_SPEC {
  sPHYSICS_VARIABLE angular_velocity;
  sPHYSICS_VARIABLE unit_vec[3];
  sPHYSICS_VARIABLE point[3];
  sPHYSICS_VARIABLE with_respect_to_ref_frame;
} *ANGULAR_MOVING_SURFEL_SPEC;

// BC_SURFEL_TURB_SPEC is included at the end of all BC surfel parameter lists for
// cases run with turbulence modeling.
typedef struct sBC_SURFEL_TURB_SPEC {
  sPHYSICS_VARIABLE turb_profile_via;	// ICs specified either as intensity/length scale or k and epsilon
  sPHYSICS_VARIABLE turb_intensity;
  sPHYSICS_VARIABLE turb_length_scale;
  sPHYSICS_VARIABLE turb_kinetic_energy;
  sPHYSICS_VARIABLE turb_dissipation;  
  
} *BC_SURFEL_TURB_SPEC;

typedef struct sBC_SURFEL_TURB_SYNTH_SPEC {

  sPHYSICS_VARIABLE turb_intens[3];    // User-specified turbulent intensities
  sPHYSICS_VARIABLE length_scales[3];  // User-specified length scales
  sPHYSICS_VARIABLE freq_range[2];     // User-specified frequency range
  sPHYSICS_VARIABLE turb_delta_t;
  sPHYSICS_VARIABLE table_id;
  sPHYSICS_VARIABLE inlet_area;

} *BC_SURFEL_TURB_SYNTH_SPEC;

VOID boundary_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
				cSTRING cdi_desc_var_name, vFLOAT value,
				vFLOAT aux_value,		// min or max
				STP_GEOM_VARIABLE surfel_centroid[3]);


__HOST__DEVICE__ dFLOAT compute_one_over_roughness_char_height(dFLOAT char_height, dFLOAT one_over_voxel_size);

static asINT32 map_coupled_wall_surface_physics_type_to_base_type(asINT32 cdi_phys_type)
{
  switch (cdi_phys_type) {
  case CDI_PHYS_TYPE_SLIP95_COUPLED:
    return CDI_PHYS_TYPE_SLIP95;
  case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:
    return CDI_PHYS_TYPE_TRUE_NOSLIP;
  case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:
    return CDI_PHYS_TYPE_LINEAR_SLIP;
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:
    return CDI_PHYS_TYPE_LINEAR_NOSLIP;
  case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:
    return CDI_PHYS_TYPE_ANGULAR_SLIP;
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED:
    return CDI_PHYS_TYPE_ANGULAR_NOSLIP;
  }

  return cdi_phys_type;
}

inline VOID do_check_cdi_phys_type(cSTRING phys_name, asINT32 expected_cdi_type, asINT32 actual_cdi_type)
{
  asINT32 base_actual_cdi_type = map_coupled_wall_surface_physics_type_to_base_type(actual_cdi_type);
  if (base_actual_cdi_type != expected_cdi_type)
    msg_error("LGI and CDI file inconsistency: The LGI file expects %s"
	      " (CDI type %d), but the CDI file contains type %d. Most likely, you simply"
	      " need to discretize your CDI file.",
	      phys_name, expected_cdi_type, base_actual_cdi_type);
}


//----------------------------------------------------------------------------
// sWALL_UDS_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sWALL_UDS_PARAMETERS {
  sPHYSICS_VARIABLE wall_uds_boundary_type;
  sPHYSICS_VARIABLE uds_value;
  sPHYSICS_VARIABLE uds_flux;

  asINT32 check_consistency(asINT32 n_parameters, CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "WALL_UDS_PARAMETERS")  {
    if (n_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uptd = find_uds_physics(ptd->cdi_physics_type);
      asINT32 i = 0;
      if ((uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_WALL_SCALAR_BOUNDARY_TYPE)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_INLET_WALL_VALUE)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_WALL_SCALAR_FLUX)
	  || uptd->n_parameters < i)
	msg_internal_error("%s is not consistent with CDI UDS physics type %d.", name, uptd->uds_physics_type);
      else
	return i;
    } else
      return 0;
  }
        
} *WALL_UDS_PARAMETERS;

typedef struct sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE ref_frame;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    asINT32 i = 0;
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_FRAME
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }

} *SURFEL_PARAMETERS_BASE;

typedef struct sSLIP_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE slip_factor;
  sPHYSICS_VARIABLE roughness_char_height;
  sPHYSICS_VARIABLE char_len;
  sPHYSICS_VARIABLE surface_material;
  sPHYSICS_VARIABLE boundary_layer_type;
  sPHYSICS_VARIABLE condensable_surface;
  sPHYSICS_VARIABLE film_thickness;
  sPHYSICS_VARIABLE surface_acoustic_absorption_coeff;
  sPHYSICS_VARIABLE radiation_surface_condition;
  sPHYSICS_VARIABLE shell_configuration;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("wall", CDI_PHYS_TYPE_SLIP95, ptd->cdi_physics_type);
    
    asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SLIP_FAC
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SURFACE_ROUGHNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SURFACE_CHAR_LEN
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SURFACE_MATERIAL
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_BOUNDARY_LAYER_TYPE
	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDENSABLE_SURFACE
	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FILM_THICKNESS
	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SURFACE_ACOUSTIC_ABSORB
 	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_SURFACE_COND
 	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SHELL_CONFIGURATION
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }

} *SLIP_SURFEL_PARAMETERS;

typedef struct sSLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed temperature wall", CDI_PHYS_TYPE_SLIP95_FIXED_TEMP, ptd->cdi_physics_type);
    
    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *SLIP_FIXED_TEMP_SURFEL_PARAMETERS;


typedef struct sSLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a thermal resistance wall", CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST, ptd->cdi_physics_type);

    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *SLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed heat flux wall", CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX, ptd->cdi_physics_type);
    
    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;

typedef struct sANGULAR_SLIP_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  
  sANGULAR_MOVING_SURFEL_SPEC angular_spec;
  sPHYSICS_VARIABLE deforming_tire_index;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_SLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating wall", CDI_PHYS_TYPE_ANGULAR_SLIP, ptd->cdi_physics_type);
    
    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ROTATION_VIA_REF_FRAME)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DEFORMING_TIRE)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *ANGULAR_SLIP_SURFEL_PARAMETERS;

typedef struct sLINEAR_SLIP_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_SLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding wall", CDI_PHYS_TYPE_LINEAR_SLIP, ptd->cdi_physics_type);
    
    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_SLIP_SURFEL_PARAMETERS;

typedef struct sVEL_SLIP_SURFEL_PARAMETERS : public sSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ref_point_vel[3];
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE ref_point[3];
  sPHYSICS_VARIABLE angular_vel[3];
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "VEL_SLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a general moving wall", CDI_PHYS_TYPE_VEL_SLIP, ptd->cdi_physics_type);
    
    asINT32 i = sSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_SLIP_TYPE)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *VEL_SLIP_SURFEL_PARAMETERS;

typedef struct sANGULAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sANGULAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating fixed temperature wall", CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sANGULAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *ANGULAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS;

typedef struct sANGULAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sANGULAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating thermal resistance wall", CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST,
                             ptd->cdi_physics_type);

    asINT32 i = sANGULAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {

      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *ANGULAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sANGULAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                            cSTRING name = "ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating fixed heat flux wall", CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sANGULAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    
    return i;
  }
} *ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;

typedef struct sLINEAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sLINEAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding fixed temperature wall", CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sLINEAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *LINEAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS;


typedef struct sLINEAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sLINEAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding thermal_resistance wall", CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST,
                             ptd->cdi_physics_type);

    asINT32 i = sLINEAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

     if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sLINEAR_SLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                            cSTRING name = "LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding fixed heat flux wall", CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sLINEAR_SLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;

typedef struct sNOSLIP_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE surface_material;
  sPHYSICS_VARIABLE condensable_surface;
  sPHYSICS_VARIABLE film_thickness;
  sPHYSICS_VARIABLE radiation_surface_condition;
  sPHYSICS_VARIABLE shell_configuration;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "NOSLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a noslip wall", CDI_PHYS_TYPE_TRUE_NOSLIP, ptd->cdi_physics_type);

    asINT32 i = sSURFEL_PARAMETERS_BASE::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SURFACE_MATERIAL
	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDENSABLE_SURFACE
	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FILM_THICKNESS
 	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_SURFACE_COND
 	|| ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SHELL_CONFIGURATION
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *NOSLIP_SURFEL_PARAMETERS;


typedef struct sNOSLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed temperature wall", CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP, ptd->cdi_physics_type);
    
    asINT32 i = sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS;


typedef struct sNOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a thermal resistance wall", CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST, ptd->cdi_physics_type);

    asINT32 i = sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

     if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sNOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed heat flux wall", CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;


typedef struct sANGULAR_NOSLIP_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  sANGULAR_MOVING_SURFEL_SPEC angular_spec;
  sPHYSICS_VARIABLE deforming_tire_index;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_NOSLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating wall", CDI_PHYS_TYPE_ANGULAR_NOSLIP, ptd->cdi_physics_type);
    
    asINT32 i = sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ROTATION_VIA_REF_FRAME)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DEFORMING_TIRE)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *ANGULAR_NOSLIP_SURFEL_PARAMETERS;

typedef struct sLINEAR_NOSLIP_SURFEL_PARAMETERS : public sNOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_NOSLIP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding wall", CDI_PHYS_TYPE_LINEAR_NOSLIP, ptd->cdi_physics_type);
    
    asINT32 i = sNOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_NOSLIP_SURFEL_PARAMETERS;


typedef struct sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sANGULAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating fixed temperature wall", CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP, ptd->cdi_physics_type);
    
    asINT32 i = sANGULAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS;


typedef struct sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sANGULAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating thermal resistance wall", CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST, ptd->cdi_physics_type);

    asINT32 i = sANGULAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

     if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sANGULAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                            cSTRING name = "ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a rotating fixed heat flux wall", CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sANGULAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);
    }
    return i;
  }
} *ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;

typedef struct sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS : public sLINEAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding fixed temperature wall", CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP, 
			     ptd->cdi_physics_type);
    
    asINT32 i = sLINEAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);
    }
    return i;
  }

} *LINEAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS;

typedef struct sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS : public sLINEAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding thermal resistance wall", CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST,
                             ptd->cdi_physics_type);

    asINT32 i = sLINEAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

     if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS;


typedef struct sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sLINEAR_NOSLIP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, 
                            cSTRING name = "LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a sliding fixed heat flux wall", CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX, ptd->cdi_physics_type);

    asINT32 i = sLINEAR_NOSLIP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);
    }
    return i;
  }
} *LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;


//----------------------------------------------------------------------------
// sINLET_OUTLET_UDS_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sIO_UDS_PARAMETERS {
  sPHYSICS_VARIABLE inlet_via;                 //inlet
  sPHYSICS_VARIABLE uds_value;                 //inlet
  sPHYSICS_VARIABLE no_uds_diffusion;          //inlet
  sPHYSICS_VARIABLE mean_value;                //inlet
  sPHYSICS_VARIABLE reverse_flow_composition;  //outlet

  asINT32 check_consistency(asINT32 n_parameters, CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "IO_UDS_PARAMETERS")  {
    if (n_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uptd = find_uds_physics(ptd->cdi_physics_type);
      asINT32 i = 0;
      if ((uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_INLET_CONTENT_VIA)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_INLET_WALL_VALUE)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_INLET_NO_SCALAR_DIFFUSION)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_INLET_MEAN_VALUE)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_BC_OUTLET_REVERSE_FLOW_COMPOSITION)
	  || uptd->n_parameters < i)
	msg_internal_error("%s is not consistent with CDI UDS physics type %d.", name, uptd->uds_physics_type);
      else
	return i;
    } else
      return 0;
  }
        
} *IO_UDS_PARAMETERS;

typedef struct sMASS_FLUX_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE meas_frame_num;
  sPHYSICS_VARIABLE meas_frame_start_num;
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE radiation_temperature;
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
  sPHYSICS_VARIABLE response_time;       // density response time
  sPHYSICS_VARIABLE mass_flux[3];
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE flexible_massflux_bc;

  sBC_SURFEL_TURB_SPEC turb_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "MASS_FLUX_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE);
} *MASS_FLUX_SURFEL_PARAMETERS;

typedef struct sMASS_FLOW_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE meas_frame_num;
  sPHYSICS_VARIABLE meas_frame_start_num;
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE radiation_temperature;
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
  sPHYSICS_VARIABLE response_time;       // density response time
  sPHYSICS_VARIABLE mass_flow;
  sPHYSICS_VARIABLE flexible_massflux_bc;
  sPHYSICS_VARIABLE inlet_area;
  sPHYSICS_VARIABLE mass_flow_sign; // this is set in eqns.cc based on the value for CDI_VAR_ID_REVERSE_MASS_FLOW (173)
  sBC_SURFEL_TURB_SPEC turb_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "MASS_FLOW_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE);
} *MASS_FLOW_SURFEL_PARAMETERS;


typedef struct sFIXED_VEL_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE meas_frame_num;
  sPHYSICS_VARIABLE meas_frame_start_num;
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE radiation_temperature;
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
  sPHYSICS_VARIABLE response_time;      // density response time
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE n_vel;
  sPHYSICS_VARIABLE is_n_vel;
  sPHYSICS_VARIABLE import_fluctuations;
  sBC_SURFEL_TURB_SYNTH_SPEC turb_synth_spec;
  sBC_SURFEL_TURB_SPEC turb_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd,
                            cSTRING name = "FIXED_VEL_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE);

  static constexpr asINT32 num_of_vars() {
    return sizeof(sFIXED_VEL_SURFEL_PARAMETERS) / sizeof(sPHYSICS_VARIABLE);
  }  
} *FIXED_VEL_SURFEL_PARAMETERS;

typedef struct sSOURCE_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE meas_frame_num;
  sPHYSICS_VARIABLE meas_start_frame_num;
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE radiation_temperature;
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
  sPHYSICS_VARIABLE pressure;
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE n_vel;
  sPHYSICS_VARIABLE is_n_vel;
  sPHYSICS_VARIABLE is_far_field_bc;
  sPHYSICS_VARIABLE import_fluctuations;

  sBC_SURFEL_TURB_SYNTH_SPEC turb_synth_spec;

  sBC_SURFEL_TURB_SPEC turb_spec;


  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SOURCE_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
  static constexpr asINT32 num_of_vars() {
    return sizeof(sSOURCE_SURFEL_PARAMETERS) / sizeof(sPHYSICS_VARIABLE);
  } 
} *SOURCE_SURFEL_PARAMETERS;

typedef struct sPASS_THRU_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE {
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
  sBC_SURFEL_TURB_SPEC turb_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "PASS_THRU_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE);
} *PASS_THRU_SURFEL_PARAMETERS;

typedef struct sPRESSURE_FREE_DIR_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE { 
#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE meas_frame_num;
  sPHYSICS_VARIABLE meas_frame_start_num;
#endif
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE radiation_temperature;
#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE water_mass_fraction;
  sPHYSICS_VARIABLE relative_humidity;
#endif  
  sPHYSICS_VARIABLE response_time;      // normal velocity response time
  sPHYSICS_VARIABLE pressure;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "PRESSURE_FREE_DIR_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *PRESSURE_FREE_DIR_SURFEL_PARAMETERS;


typedef struct sSTATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE dist_to_reflect; // distance to reflecting surface
  sBC_SURFEL_TURB_SPEC turb_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS;

typedef struct sSTAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_SURFEL_PARAMETERS {
  sBC_SURFEL_TURB_SPEC turb_spec;
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS;


typedef struct sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS  : public sPRESSURE_FREE_DIR_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE unit_vec[3];
  sPHYSICS_VARIABLE coord_sys;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "PRESSURE_FIXED_DIR_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *PRESSURE_FIXED_DIR_SURFEL_PARAMETERS;

typedef struct sSTATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS : public sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS {
  
  sBC_SURFEL_TURB_SPEC turb_spec;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS;

typedef struct sSTAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS : public sPRESSURE_FIXED_DIR_SURFEL_PARAMETERS {

  sPHYSICS_VARIABLE is_total_temp;
  sPHYSICS_VARIABLE is_ss_inlet;
  sPHYSICS_VARIABLE ss_static_pressure;
  sBC_SURFEL_TURB_SPEC turb_spec;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS;

//for 5G
typedef struct sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_SURFEL_PARAMETERS
{
  sPHYSICS_VARIABLE comp0_vol_frac;
  sPHYSICS_VARIABLE comp1_vol_frac;
  sPHYSICS_VARIABLE phase_index;
  sPHYSICS_VARIABLE bc_type;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS;


typedef struct sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS
{
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS;

typedef struct sSTATIC_PRESSURE_FIXED_DIR_5G_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS
{
  sPHYSICS_VARIABLE unit_vec[3];
  sPHYSICS_VARIABLE coord_sys;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "STATIC_PRESSURE_FIXED_5G_DIR_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *STATIC_PRESSURE_FIXED_DIR_5G_SURFEL_PARAMETERS;

typedef struct sSOURCE_5G_SURFEL_PARAMETERS : public sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS
{
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SOURCE_5G_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
  
} *SOURCE_5G_SURFEL_PARAMETERS;


typedef struct sMASS_FLUX_5G_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE 
{
  sPHYSICS_VARIABLE temperature;
  sPHYSICS_VARIABLE response_time;       // density response time
  sPHYSICS_VARIABLE unit_vec[3];
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE comp0_mass_flux;
  sPHYSICS_VARIABLE comp1_mass_flux;
  sPHYSICS_VARIABLE phase_index;
  sPHYSICS_VARIABLE bc_type;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "MASS_FLUX_5G_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *MASS_FLUX_5G_SURFEL_PARAMETERS;


typedef struct sNOSLIP_5G_SURFEL_PARAMETERS : public sSURFEL_PARAMETERS_BASE 
{
  sPHYSICS_VARIABLE comp0_fric_factor;
  sPHYSICS_VARIABLE comp0_wetting_den;
  sPHYSICS_VARIABLE comp1_fric_factor;
  sPHYSICS_VARIABLE comp1_wetting_den;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "NOSLIP_5G_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *NOSLIP_5G_SURFEL_PARAMETERS;

typedef struct sANGULAR_NOSLIP_5G_SURFEL_PARAMETERS : public sNOSLIP_5G_SURFEL_PARAMETERS
{
  sANGULAR_MOVING_SURFEL_SPEC angular_spec;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ANGULAR_NOSLIP_5G_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *ANGULAR_NOSLIP_5G_SURFEL_PARAMETERS;

typedef struct sLINEAR_NOSLIP_5G_SURFEL_PARAMETERS : public sNOSLIP_5G_SURFEL_PARAMETERS
{
  sPHYSICS_VARIABLE velocity[3];
  sPHYSICS_VARIABLE coord_sys;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LINEAR_NOSLIP_5G_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) ;
} *LINEAR_NOSLIP_5G_SURFEL_PARAMETERS;
//end 5G

//Generic enum with all possible surfel variables.
//Having only one big enum allows to avoid common variables repeated in different particular enums.
//enum in lowercase to work better with MACRO defined later.
enum e_SBC : int {
  ref_frame,
  meas_frame_num,
  meas_frame_start_num,
  temperature,
  water_mass_fraction,
  relative_humidity,
  response_time,      // density response time
  velocity_x,
  velocity_y,
  velocity_z,
  coord_sys,
  n_vel,
  is_n_vel,
  import_fluctuations,
  // sbc_surfel_turb_synth_spec
  turb_intens_x,   
  turb_intens_y,   
  turb_intens_z,   
  length_scales_x, 
  length_scales_y, 
  length_scales_z, 
  freq_range_1,    
  freq_range_2,    
  turb_delta_t,
  table_id,
  inlet_area,
  //sbc_surfel_turb_spec
  turb_profile_via,
  turb_intensity,
  turb_length_scale,
  turb_kinetic_energy,
  turb_dissipation,
  //SOURCE_SURFEL
  meas_start_frame_num,
  pressure,
  is_far_field_bc,
  //SLIP SURFEL
  slip_factor,
  roughness_char_height,
  char_len,
  surface_material,
  boundary_layer_type,
  condensable_surface,
  film_thickness,
  surface_acoustic_absorption_coeff,
  //SLIP_FIXED_TEMP
  ambient_temp,
  wall_thickness,
  wall_conductivity,
  ext_heat_xfer_coeff,
  heat_flux,
  angular_velocity,
  unit_vec_x,
  unit_vec_y,
  unit_vec_z,
  point_x,
  point_y,
  point_z,
  with_respect_to_ref_frame,
  deforming_tire_index,
  ref_point_vel_x,
  ref_point_vel_y,
  ref_point_vel_z,
  ref_point_x,
  ref_point_y,
  ref_point_z,
  angular_vel_x,
  angular_vel_y,
  angular_vel_z,
  inlet_via,
  uds_value,      
  no_uds_diffusion,
  mean_value,
  reverse_flow_composition,
  mass_flux_x,
  mass_flux_y,
  mass_flux_z,
  flexible_massflux_bc,
  mass_flow,
  mass_flow_sign,
  dist_to_reflect,
  is_total_temp,
  is_ss_inlet,
  ss_static_pressure,
  comp0_vol_frac,
  comp1_vol_frac,
  phase_index,
  bc_type,
  comp0_mass_flux,
  comp1_mass_flux,
  comp0_fric_factor,
  comp0_wetting_den,
  comp1_fric_factor,
  comp1_wetting_den,
  wall_uds_boundary_type,
  uds_flux,
  unit_vector_x,
  unit_vector_y,
  unit_vector_z,
  mass_flow_inlet_area,
  radiation_surface_condition,
  shell_configuration,
  radiation_temperature,
  max_num_vars
};

namespace GPU{
class EXPRLANG_DEVICE_DATA;
}
#if DEVICE_COMPILATION_MODE
/* set_var_recursive will read device buffer if variable is varying based on associated mask.
   only exists in device code.*/ \
  #define DEVICE_METHODS \
  __DEVICE__ virtual void set_var_recursive(int i, int soxor_var_index, int n_exprlang_bits, GPU::EXPRLANG_DEVICE_DATA* d,  \
                                            EXPRLANG_VAR_MASK& mask) override \
  { \
    if (test_exprlang_mask(mask,i)) \
    { \
      uINT32 var_index = soxor_var_index + N_SFLS_PER_MSFL * n_exprlang_bits; \
      val = get_exprlang_dev_data_value(d,var_index); \
      n_exprlang_bits++; \
    } \
    i++; \
    if constexpr (Parent::num_vars() != 0)  /*we don't need to call this for the last element*/ \
      Parent::set_var_recursive(i, soxor_var_index, n_exprlang_bits, d, mask); \
  }
  /* DEVICE_CONSTRUCTOR_PHASE2 defines the extra step needed if this is device code to read varying variables
   at construction from device buffer. This only applies when constructed with surfel id*/ \
  #define DEVICE_CONSTRUCTOR_PHASE2 this->read_exprlang_device_data(msfl_id);
#else
#define DEVICE_METHODS
#define DEVICE_CONSTRUCTOR_PHASE2
#endif
/* This macro defines a handler for parameter's PHYSICS_VARIABLE values to pass information to CPU or GPU code in a common way.
This is the base BC_VALUES used for scarlars. It is defined by an ENUM_VALUE, the actual variable name which in turn is used as getter, 
and a PARAMS_ACCESSOR, the way the parameters class access its physics variables.*/
#define sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR(ENUM_VALUE, PARAMS_ACCESSOR)\
template <typename PARAMS_T, e_SBC...others> \
struct sSURFEL_BC_VALUES<PARAMS_T,e_SBC::ENUM_VALUE, others ...>: public sSURFEL_BC_VALUES<PARAMS_T,others ...> \
{ \
  using Parent = sSURFEL_BC_VALUES<PARAMS_T, others ...>; \
  \
  template <typename PARAMS_T1, e_SBC... E_others> \
  using Append_t = sSURFEL_BC_VALUES<PARAMS_T1, e_SBC::ENUM_VALUE, others..., E_others...>; \
  \
  dFLOAT val=0; \
  __HOST__DEVICE__ dFLOAT ENUM_VALUE() const{ return val;} \
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params):Parent(params) \
  {\
    val = params->PARAMS_ACCESSOR.value;\
  }\
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params, size_t msfl_id):sSURFEL_BC_VALUES( params) \
  {\
    /*The first parameter from the pack will call read_exprlang_device_data at then end to read from gpu buffer at construction*/ \
    DEVICE_CONSTRUCTOR_PHASE2 \
  }\
  __HOST__DEVICE__ constexpr static int num_vars(){ return 1 + Parent::num_vars(); } \
  \
  protected: \
  DEVICE_METHODS \
  \
  __HOST__DEVICE__ virtual VOID copy_vars_from_pd_recursive() override { \
      val = this->m_params->PARAMS_ACCESSOR.value; \
      /*printf("m_pd->n_parameters=%d i=%d m_vars[i]=%f \n",m_pd->n_parameters, i, m_vars[i]); */\
    if constexpr (Parent::num_vars() != 0)  /*we don't need to call this for the last element*/ \
      Parent::copy_vars_from_pd_recursive(); \
  } \
     \
};

/* This macro defines a special version for PHYSICS_VARIABLES that are 3D vectors. They define 2 getters, one for the first component, and another to return the 3D vector*/
#define sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D(ENUM_VALUE, PARAMS_ACCESSOR)\
template <typename PARAMS_T, e_SBC...others> \
struct sSURFEL_BC_VALUES<PARAMS_T,e_SBC::ENUM_VALUE ## _x, others ...>: public sSURFEL_BC_VALUES<PARAMS_T,others ...> \
{ \
  using Parent = sSURFEL_BC_VALUES<PARAMS_T, others ...>; \
  template <typename PARAMS_T1, e_SBC... E_others> \
  using Append_t = sSURFEL_BC_VALUES<PARAMS_T1, e_SBC::ENUM_VALUE ## _x, others..., E_others...>; \
  dFLOAT val=0; \
  __HOST__DEVICE__ dFLOAT ENUM_VALUE ## _x() const{ return val;} \
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params):Parent(params) \
  {\
    val = params->PARAMS_ACCESSOR[0].value;\
  }\
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params, size_t msfl_id):sSURFEL_BC_VALUES( params) \
  {\
    /*The first parameter from the pack will call read_exprlang_device_data at then end to read from gpu buffer at construction*/ \
    DEVICE_CONSTRUCTOR_PHASE2 \
  }\
  __HOST__DEVICE__ VOID get_ ## ENUM_VALUE (dFLOAT* out) const \
  { \
    out[0] = this->ENUM_VALUE ## _x(); \
    out[1] = this->ENUM_VALUE ## _y(); \
    out[2] = this->ENUM_VALUE ## _z(); \
  } \
  __HOST__DEVICE__ constexpr static int num_vars(){ return 1 + Parent::num_vars(); } \
  \
  protected: \
  DEVICE_METHODS \
  \
  __HOST__DEVICE__ virtual VOID copy_vars_from_pd_recursive() override { \
      val = this->m_params->PARAMS_ACCESSOR[0].value; \
    if constexpr (Parent::num_vars() != 0)  /*we don't need to call this for the last element*/ \
      Parent::copy_vars_from_pd_recursive(); \
  } \
     \
}; \
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR( ENUM_VALUE ## _y, PARAMS_ACCESSOR[1]) \
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR( ENUM_VALUE ## _z, PARAMS_ACCESSOR[2]) 

/* This macro defines a special version for PHYSICS_VARIABLES that are 2D vectors. They define 2 getters, one for the first component, and another to return the 2D vector*/
#define sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_2D(ENUM_VALUE, PARAMS_ACCESSOR)\
template <typename PARAMS_T, e_SBC...others> \
struct sSURFEL_BC_VALUES<PARAMS_T,e_SBC::ENUM_VALUE ## _1, others ...>: public sSURFEL_BC_VALUES<PARAMS_T,others ...> \
{ \
  using Parent = sSURFEL_BC_VALUES<PARAMS_T, others ...>; \
  template <typename PARAMS_T1, e_SBC... E_others> \
  using Append_t = sSURFEL_BC_VALUES<PARAMS_T1, e_SBC::ENUM_VALUE ## _1, others..., E_others...>; \
  dFLOAT val=0; \
  __HOST__DEVICE__ dFLOAT ENUM_VALUE ## _1(){ return val;} \
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params):Parent(params) \
  {\
    val = params->PARAMS_ACCESSOR[0].value;\
  }\
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params, size_t msfl_id):sSURFEL_BC_VALUES( params) \
  {\
    /*The first parameter from the pack will call read_exprlang_device_data at then end to read from gpu buffer at construction*/ \
    DEVICE_CONSTRUCTOR_PHASE2 \
  }\
  __HOST__DEVICE__ VOID get_ ## ENUM_VALUE (dFLOAT* out) const \
  { \
    out[0] = this->ENUMVAL ## _1(); \
    out[1] = this->ENUMVAL ## _2(); \
  } \
  __HOST__DEVICE__ constexpr static int num_vars(){ return 1 + Parent::num_vars(); } \
  \
  protected: \
  DEVICE_METHODS \
  \
  __HOST__DEVICE__ virtual VOID copy_vars_from_pd_recursive() override { \
      val = this->m_params->PARAMS_ACCESSOR[0].value; \
      /*printf("m_pd->n_parameters=%d i=%d m_vars[i]=%f \n",m_pd->n_parameters, i, m_vars[i]); */\
    if constexpr (Parent::num_vars() != 0)  /*we don't need to call this for the last element*/ \
      Parent::copy_vars_from_pd_recursive(); \
  } \
     \
}; \
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR( ENUM_VALUE ## _2, PARAMS_ACCESSOR[1]) \


/* When the variable name matches the way the parameter access its variable we can avoid copying two times the same with these macros*/
#define sSURFEL_BC_VALUES_SPECIALIZATION(ENUM_VALUE) sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR(ENUM_VALUE,ENUM_VALUE)
#define sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D(ENUM_VALUE) sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D(ENUM_VALUE,ENUM_VALUE)
#define sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_2D(ENUM_VALUE) sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D(ENUM_VALUE,ENUM_VALUE)

template< typename PARAMS_T, e_SBC... E > 
struct sSURFEL_BC_VALUES ;
/* Empty class to terminate variadic group*/
template < typename PARAMS_T>
struct sSURFEL_BC_VALUES<PARAMS_T>
{
  PARAMS_T m_params;
  __HOST__DEVICE__ sSURFEL_BC_VALUES(PARAMS_T params):m_params(params){}
  __HOST__DEVICE__ constexpr static int num_vars(){ return 0;}
  __HOST__DEVICE__ virtual VOID copy_vars_from_pd_recursive()=0;
#if DEVICE_COMPILATION_MODE

  __DEVICE__ virtual void set_var_recursive(int i, int soxor_var_index, 
                            int n_exprlang_bits, GPU::EXPRLANG_DEVICE_DATA* d, 
                            EXPRLANG_VAR_MASK& mask) = 0;

  __DEVICE__ VOID read_exprlang_device_data(size_t msfl_id);
#endif

  __HOST__DEVICE__ VOID read_exprlang_data( size_t msfl_id ) {
  #if DEVICE_COMPILATION_MODE
    this->read_exprlang_device_data(msfl_id);
  #else
    copy_vars_from_pd_recursive();
  #endif
  }
  
};

/* Use macros to define classes */
sSURFEL_BC_VALUES_SPECIALIZATION ( ref_frame );
sSURFEL_BC_VALUES_SPECIALIZATION ( meas_frame_num);
sSURFEL_BC_VALUES_SPECIALIZATION ( meas_frame_start_num);
sSURFEL_BC_VALUES_SPECIALIZATION ( temperature);
sSURFEL_BC_VALUES_SPECIALIZATION ( water_mass_fraction);
sSURFEL_BC_VALUES_SPECIALIZATION ( relative_humidity);
sSURFEL_BC_VALUES_SPECIALIZATION ( response_time);      // density response time
sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D ( velocity);
sSURFEL_BC_VALUES_SPECIALIZATION ( coord_sys);
sSURFEL_BC_VALUES_SPECIALIZATION ( n_vel);
sSURFEL_BC_VALUES_SPECIALIZATION ( is_n_vel);
sSURFEL_BC_VALUES_SPECIALIZATION ( import_fluctuations);
// MASSFLOW
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( mass_flow_inlet_area, inlet_area);
// sbc_surfel_turb_synth_spec
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D ( turb_intens, turb_synth_spec.turb_intens);   
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D ( length_scales, turb_synth_spec.length_scales); 
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_2D ( freq_range,turb_synth_spec.freq_range);    
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_delta_t, turb_synth_spec.turb_delta_t);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( table_id, turb_synth_spec.table_id);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( inlet_area, turb_synth_spec.inlet_area);
//sbc_surfel_turb_spec//sbc_surfel_turb_s
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_profile_via, turb_spec.turb_profile_via);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_intensity, turb_spec.turb_intensity);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_length_scale, turb_spec.turb_length_scale);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_kinetic_energy, turb_spec.turb_kinetic_energy);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( turb_dissipation, turb_spec.turb_dissipation);
//SOURCE
sSURFEL_BC_VALUES_SPECIALIZATION ( meas_start_frame_num);
sSURFEL_BC_VALUES_SPECIALIZATION ( pressure);
sSURFEL_BC_VALUES_SPECIALIZATION ( is_far_field_bc);
//SLIP
sSURFEL_BC_VALUES_SPECIALIZATION ( slip_factor);
sSURFEL_BC_VALUES_SPECIALIZATION ( roughness_char_height);
sSURFEL_BC_VALUES_SPECIALIZATION ( char_len);
sSURFEL_BC_VALUES_SPECIALIZATION ( surface_material);
sSURFEL_BC_VALUES_SPECIALIZATION ( boundary_layer_type);
sSURFEL_BC_VALUES_SPECIALIZATION ( condensable_surface);
sSURFEL_BC_VALUES_SPECIALIZATION ( film_thickness);
sSURFEL_BC_VALUES_SPECIALIZATION ( surface_acoustic_absorption_coeff);
// SLIP_THERMAL_RESIST_SURFEL
sSURFEL_BC_VALUES_SPECIALIZATION ( ambient_temp);
sSURFEL_BC_VALUES_SPECIALIZATION ( wall_thickness);
sSURFEL_BC_VALUES_SPECIALIZATION ( wall_conductivity);
sSURFEL_BC_VALUES_SPECIALIZATION ( ext_heat_xfer_coeff);
//SLIP_FIXED_HEAT_FLUX
sSURFEL_BC_VALUES_SPECIALIZATION ( heat_flux);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( angular_velocity,angular_spec.angular_velocity);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D ( unit_vec, angular_spec.unit_vec);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D ( point, angular_spec.point);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR ( with_respect_to_ref_frame, angular_spec.with_respect_to_ref_frame);
sSURFEL_BC_VALUES_SPECIALIZATION (deforming_tire_index);
sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D ( ref_point_vel );
sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D ( ref_point );
sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D ( angular_vel );
sSURFEL_BC_VALUES_SPECIALIZATION (inlet_via);
sSURFEL_BC_VALUES_SPECIALIZATION (uds_value);
sSURFEL_BC_VALUES_SPECIALIZATION (no_uds_diffusion);
sSURFEL_BC_VALUES_SPECIALIZATION (mean_value);
sSURFEL_BC_VALUES_SPECIALIZATION (reverse_flow_composition);
sSURFEL_BC_VALUES_SPECIALIZATION_ARRAY_3D ( mass_flux );
sSURFEL_BC_VALUES_SPECIALIZATION (flexible_massflux_bc);
sSURFEL_BC_VALUES_SPECIALIZATION (mass_flow);
sSURFEL_BC_VALUES_SPECIALIZATION (mass_flow_sign);
sSURFEL_BC_VALUES_SPECIALIZATION (dist_to_reflect);
sSURFEL_BC_VALUES_SPECIALIZATION (is_total_temp);
sSURFEL_BC_VALUES_SPECIALIZATION (is_ss_inlet);
sSURFEL_BC_VALUES_SPECIALIZATION (ss_static_pressure);
sSURFEL_BC_VALUES_SPECIALIZATION (comp0_vol_frac);
sSURFEL_BC_VALUES_SPECIALIZATION (comp1_vol_frac);
sSURFEL_BC_VALUES_SPECIALIZATION (phase_index);
sSURFEL_BC_VALUES_SPECIALIZATION (bc_type);
sSURFEL_BC_VALUES_SPECIALIZATION (comp0_mass_flux);
sSURFEL_BC_VALUES_SPECIALIZATION (comp1_mass_flux);
sSURFEL_BC_VALUES_SPECIALIZATION (comp0_fric_factor);
sSURFEL_BC_VALUES_SPECIALIZATION (comp0_wetting_den);
sSURFEL_BC_VALUES_SPECIALIZATION (comp1_fric_factor);
sSURFEL_BC_VALUES_SPECIALIZATION (comp1_wetting_den);
sSURFEL_BC_VALUES_SPECIALIZATION (wall_uds_boundary_type);
sSURFEL_BC_VALUES_SPECIALIZATION (uds_flux);
sSURFEL_BC_VALUES_SPECIALIZATION_WITH_ACCESSOR_ARRAY_3D (unit_vector, unit_vec);
sSURFEL_BC_VALUES_SPECIALIZATION (radiation_surface_condition);
sSURFEL_BC_VALUES_SPECIALIZATION (shell_configuration);
sSURFEL_BC_VALUES_SPECIALIZATION (radiation_temperature);

/* Wrapper to avoid passing functions each value individually.*/
struct sANGULAR_SPEC_VALUES{
  sANGULAR_SPEC_VALUES() = default;
  
  __HOST__DEVICE__ sANGULAR_SPEC_VALUES(dFLOAT vec[3], dFLOAT pt[3], dFLOAT ang_vel)
  {
    ccDOTIMES(i,3)
    {
      unit_vec[i] = vec[i];
      point[i] = pt[i];
    }
    angular_vel = ang_vel;
  }
  dFLOAT unit_vec[3], point[3];
  dFLOAT angular_vel;
};

#define BUILD_THERMAL_BC_VALUES 0
/* Define usings to help create more expresive and easier code to read.
All surfel BC_VALUEs are defined, GPU and CPU, even if only GPU supported surfels use them at this point.*/
using sWALL_UDS_BC_VALUES = sSURFEL_BC_VALUES<WALL_UDS_PARAMETERS,
                                            wall_uds_boundary_type,
                                            uds_value,
                                            uds_flux
                                            >;
using sSLIP_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<SLIP_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::slip_factor,
                                                      e_SBC::roughness_char_height,
                                                      e_SBC::char_len,
                                                      e_SBC::surface_material,
                                                      e_SBC::boundary_layer_type,
                                                      e_SBC::condensable_surface,
                                                      e_SBC::film_thickness,
                                                      e_SBC::surface_acoustic_absorption_coeff,
                                                      e_SBC::radiation_surface_condition,
                                                      e_SBC::shell_configuration
                                                      >;
#ifdef BUILD_THERMAL_BC_VALUES
using sSLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<SLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;
using sSLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<SLIP_THERMAL_RESIST_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >;
using sSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >;
#endif
using sANGULAR_SLIP_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_SLIP_SURFEL_PARAMETERS,
                                                      e_SBC::angular_velocity,
                                                      e_SBC::unit_vec_x,
                                                      e_SBC::unit_vec_y,
                                                      e_SBC::unit_vec_z,
                                                      e_SBC::point_x,
                                                      e_SBC::point_y,
                                                      e_SBC::point_z,
                                                      e_SBC::with_respect_to_ref_frame,
                                                      e_SBC::deforming_tire_index
                                                      >;
using sLINEAR_SLIP_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_SLIP_SURFEL_PARAMETERS,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys
                                                      >;
using sVEL_SLIP_SURFEL_BC_VALUES = typename sSLIP_SURFEL_BC_VALUES::template Append_t<VEL_SLIP_SURFEL_PARAMETERS,
                                                      e_SBC::ref_point_vel_x,
                                                      e_SBC::ref_point_vel_y,
                                                      e_SBC::ref_point_vel_z,
                                                      e_SBC::coord_sys,
                                                      e_SBC::ref_point_x,
                                                      e_SBC::ref_point_y,
                                                      e_SBC::ref_point_z,
                                                      e_SBC::angular_vel_x,
                                                      e_SBC::angular_vel_y,
                                                      e_SBC::angular_vel_z
                                                      >;
#ifdef BUILD_THERMAL_BC_VALUES
using sANGULAR_SLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sANGULAR_SLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;
using sANGULAR_SLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sANGULAR_SLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >;
using sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sANGULAR_SLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >;
using sLINEAR_SLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sLINEAR_SLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_SLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;
using sLINEAR_SLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sLINEAR_SLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_SLIP_THERMAL_RESIST_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >;
using sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sLINEAR_SLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >;
#endif
using sNOSLIP_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<NOSLIP_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::surface_material,
                                                      e_SBC::condensable_surface,
                                                      e_SBC::film_thickness,
                                                      e_SBC::radiation_surface_condition,
                                                      e_SBC::shell_configuration
                                                      >;
#ifdef BUILD_THERMAL_BC_VALUES
using sNOSLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sNOSLIP_SURFEL_BC_VALUES::template Append_t<NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;
using sNOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sNOSLIP_SURFEL_BC_VALUES::template Append_t<NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >; 
using sNOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sNOSLIP_SURFEL_BC_VALUES::template Append_t<NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >;
#endif
using sANGULAR_NOSLIP_SURFEL_BC_VALUES = typename sNOSLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_NOSLIP_SURFEL_PARAMETERS,
                                                      e_SBC::angular_velocity,
                                                      e_SBC::unit_vec_x,
                                                      e_SBC::unit_vec_y,
                                                      e_SBC::unit_vec_z,
                                                      e_SBC::point_x,
                                                      e_SBC::point_y,
                                                      e_SBC::point_z,
                                                      e_SBC::with_respect_to_ref_frame,
                                                      e_SBC::deforming_tire_index
                                                      >;
using sLINEAR_NOSLIP_SURFEL_BC_VALUES = typename sNOSLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_NOSLIP_SURFEL_PARAMETERS,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys
                                                      >;  
#ifdef BUILD_THERMAL_BC_VALUES
using sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sANGULAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;
using sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sANGULAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >;
using sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sANGULAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >;
using sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_BC_VALUES = typename sLINEAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_NOSLIP_FIXED_TEMP_SURFEL_PARAMETERS,
                                                      e_SBC::temperature
                                                      >;                                                                                                            
using sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES = typename sLINEAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_PARAMETERS,
                                                      e_SBC::ambient_temp,
                                                      e_SBC::ext_heat_xfer_coeff,
                                                      e_SBC::wall_thickness,
                                                      e_SBC::wall_conductivity
                                                      >;
using sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES = typename sLINEAR_NOSLIP_SURFEL_BC_VALUES::template Append_t<LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::heat_flux
                                                      >; 
#endif
using sIO_UDS_BC_VALUES = sSURFEL_BC_VALUES<IO_UDS_PARAMETERS,
                                                      e_SBC::inlet_via,
                                                      e_SBC::uds_value,      
                                                      e_SBC::no_uds_diffusion,
                                                      e_SBC::mean_value,
                                                      e_SBC::reverse_flow_composition
                                                      >;  
using sMASS_FLUX_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<MASS_FLUX_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::meas_frame_num,
                                                      e_SBC::meas_frame_start_num,
                                                      e_SBC::temperature,
                                                      e_SBC::radiation_temperature,
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                      e_SBC::response_time,
                                                      e_SBC::mass_flux_x,
                                                      e_SBC::mass_flux_y,
                                                      e_SBC::mass_flux_z,
                                                      e_SBC::coord_sys,
                                                      e_SBC::flexible_massflux_bc,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;                                                        
using sMASS_FLOW_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<MASS_FLOW_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::meas_frame_num,
                                                      e_SBC::meas_frame_start_num,
                                                      e_SBC::temperature,
                                                      e_SBC::radiation_temperature,
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                      e_SBC::response_time,
                                                      e_SBC::mass_flow,
                                                      e_SBC::flexible_massflux_bc,
                                                      e_SBC::mass_flow_inlet_area,
                                                      e_SBC::mass_flow_sign,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >; 
using sFIXED_VEL_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<FIXED_VEL_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::meas_frame_num,
                                                      e_SBC::meas_frame_start_num,
                                                      e_SBC::temperature,
                                                      e_SBC::radiation_temperature,
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                      e_SBC::response_time,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys,
                                                      e_SBC::n_vel,
                                                      e_SBC::is_n_vel,
                                                      e_SBC::import_fluctuations,
                                                      e_SBC::turb_intens_x,   
                                                      e_SBC::turb_intens_y,   
                                                      e_SBC::turb_intens_z,   
                                                      e_SBC::length_scales_x, 
                                                      e_SBC::length_scales_y, 
                                                      e_SBC::length_scales_z, 
                                                      e_SBC::freq_range_1,    
                                                      e_SBC::freq_range_2,    
                                                      e_SBC::turb_delta_t,
                                                      e_SBC::table_id,
                                                      e_SBC::inlet_area,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;

using sSOURCE_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<SOURCE_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::meas_frame_num,
                                                      e_SBC::meas_start_frame_num,
                                                      e_SBC::temperature,
                                                      e_SBC::radiation_temperature,
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                      e_SBC::pressure,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys,
                                                      e_SBC::n_vel,
                                                      e_SBC::is_n_vel,
                                                      e_SBC::is_far_field_bc,
                                                      e_SBC::import_fluctuations,
                                                      e_SBC::turb_intens_x,   
                                                      e_SBC::turb_intens_y,   
                                                      e_SBC::turb_intens_z,   
                                                      e_SBC::length_scales_x, 
                                                      e_SBC::length_scales_y, 
                                                      e_SBC::length_scales_z, 
                                                      e_SBC::freq_range_1,    
                                                      e_SBC::freq_range_2,    
                                                      e_SBC::turb_delta_t,
                                                      e_SBC::table_id,
                                                      e_SBC::inlet_area,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;
using sPASS_THRU_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<PASS_THRU_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::temperature,
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;
using sPRESSURE_FREE_DIR_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<PRESSURE_FREE_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                    #if !BUILD_5G_LATTICE
                                                      e_SBC::meas_frame_num,
                                                      e_SBC::meas_frame_start_num,
                                                    #endif
                                                      e_SBC::temperature,
                                                      e_SBC::radiation_temperature,
                                                      #if !BUILD_5G_LATTICE
                                                      e_SBC::water_mass_fraction,
                                                      e_SBC::relative_humidity,
                                                    #endif  
                                                      e_SBC::response_time,      // normal velocity response time
                                                      e_SBC::pressure
                                                      >;
using sSTATIC_PRESSURE_FREE_DIR_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_SURFEL_BC_VALUES::template Append_t<STATIC_PRESSURE_FREE_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::dist_to_reflect,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;
using sSTAG_PRESSURE_FREE_DIR_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_SURFEL_BC_VALUES::template Append_t<STAG_PRESSURE_FREE_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;
using sPRESSURE_FIXED_DIR_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_SURFEL_BC_VALUES::template Append_t<PRESSURE_FIXED_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::unit_vector_x,
                                                      e_SBC::unit_vector_y,
                                                      e_SBC::unit_vector_z,
                                                      e_SBC::coord_sys
                                                      >;

using sSTATIC_PRESSURE_FIXED_DIR_SURFEL_BC_VALUES = typename sPRESSURE_FIXED_DIR_SURFEL_BC_VALUES::template Append_t<STATIC_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::turb_profile_via,
                                                      e_SBC::turb_intensity,
                                                      e_SBC::turb_length_scale,
                                                      e_SBC::turb_kinetic_energy,
                                                      e_SBC::turb_dissipation
                                                      >;
using sSTAG_PRESSURE_FIXED_DIR_SURFEL_BC_VALUES = typename sSTATIC_PRESSURE_FIXED_DIR_SURFEL_BC_VALUES::template Append_t<STAG_PRESSURE_FIXED_DIR_SURFEL_PARAMETERS,
                                                      e_SBC::is_total_temp,
                                                      e_SBC::is_ss_inlet,
                                                      e_SBC::ss_static_pressure
                                                      >;
using sPRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_SURFEL_BC_VALUES::template Append_t<PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS,
                                                      e_SBC::comp0_vol_frac,
                                                      e_SBC::comp1_vol_frac,
                                                      e_SBC::phase_index,
                                                      e_SBC::bc_type
                                                      >;
using sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES::template Append_t<STATIC_PRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS
                                                      >;
using sSTATIC_PRESSURE_FIXED_DIR_5G_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES::template Append_t<STATIC_PRESSURE_FIXED_DIR_5G_SURFEL_PARAMETERS,
                                                      e_SBC::unit_vec_x,
                                                      e_SBC::unit_vec_y,
                                                      e_SBC::unit_vec_z,
                                                      e_SBC::coord_sys
                                                      >;
using sSOURCE_5G_SURFEL_BC_VALUES = typename sPRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES::template Append_t<sPRESSURE_FREE_DIR_5G_SURFEL_PARAMETERS,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys
                                                      >;
using sMASS_FLUX_5G_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<MASS_FLUX_5G_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::temperature,
                                                      e_SBC::response_time,
                                                      e_SBC::unit_vec_x,
                                                      e_SBC::unit_vec_y,
                                                      e_SBC::unit_vec_z,
                                                      e_SBC::coord_sys,
                                                      e_SBC::comp0_mass_flux,
                                                      e_SBC::comp1_mass_flux,
                                                      e_SBC::phase_index,
                                                      e_SBC::bc_type
                                                      >;
using sNOSLIP_5G_SURFEL_BC_VALUES = sSURFEL_BC_VALUES<NOSLIP_5G_SURFEL_PARAMETERS,
                                                      e_SBC::ref_frame,
                                                      e_SBC::comp0_fric_factor,
                                                      e_SBC::comp0_wetting_den,
                                                      e_SBC::comp1_fric_factor,
                                                      e_SBC::comp1_wetting_den
                                                      >;
using sANGULAR_NOSLIP_5G_SURFEL_BC_VALUES = typename sNOSLIP_5G_SURFEL_BC_VALUES::template Append_t<ANGULAR_NOSLIP_5G_SURFEL_PARAMETERS,
                                                      e_SBC::angular_velocity,
                                                      e_SBC::unit_vec_x,
                                                      e_SBC::unit_vec_y,
                                                      e_SBC::unit_vec_z,
                                                      e_SBC::point_x,
                                                      e_SBC::point_y,
                                                      e_SBC::point_z,
                                                      e_SBC::with_respect_to_ref_frame
                                                      >;
using sLINEAR_NOSLIP_5G_SURFEL_BC_VALUES = typename sNOSLIP_5G_SURFEL_BC_VALUES::template Append_t<LINEAR_NOSLIP_5G_SURFEL_PARAMETERS,
                                                      e_SBC::velocity_x,
                                                      e_SBC::velocity_y,
                                                      e_SBC::velocity_z,
                                                      e_SBC::coord_sys
                                                      >;

/* Compute maximum number of variables in any possible surfel to use it from gpu_exprlang as buffer array size*/
template <typename T0, typename... Ts>
constexpr int getMaxNumVars()
{
  constexpr int num_vars_0 = T0::num_vars();
  if constexpr (std::is_same_v<std::tuple<T0>, std::tuple<T0, Ts...>>)
    return num_vars_0;
  else
  {
    constexpr int max_vars_s = getMaxNumVars<Ts...>();
    return std::max(num_vars_0, max_vars_s);
  }
}

constexpr int BC_VALUES_MAX_NUM_VARS = getMaxNumVars<sWALL_UDS_BC_VALUES,
                              sSLIP_SURFEL_BC_VALUES,
                              sANGULAR_SLIP_SURFEL_BC_VALUES,
                              sLINEAR_SLIP_SURFEL_BC_VALUES,
                              sVEL_SLIP_SURFEL_BC_VALUES,
                              sNOSLIP_SURFEL_BC_VALUES,
                              sANGULAR_NOSLIP_SURFEL_BC_VALUES,
                              sLINEAR_NOSLIP_SURFEL_BC_VALUES,
                              sIO_UDS_BC_VALUES,
                              sMASS_FLUX_SURFEL_BC_VALUES,
                              sMASS_FLOW_SURFEL_BC_VALUES,
                              sFIXED_VEL_SURFEL_BC_VALUES,
                              sSOURCE_SURFEL_BC_VALUES,
                              sPASS_THRU_SURFEL_BC_VALUES,
                              sPRESSURE_FREE_DIR_SURFEL_BC_VALUES,
                              sSTATIC_PRESSURE_FREE_DIR_SURFEL_BC_VALUES,
                              sSTAG_PRESSURE_FREE_DIR_SURFEL_BC_VALUES,
                              sPRESSURE_FIXED_DIR_SURFEL_BC_VALUES,
                              sSTATIC_PRESSURE_FIXED_DIR_SURFEL_BC_VALUES,
                              sSTAG_PRESSURE_FIXED_DIR_SURFEL_BC_VALUES,
                              sPRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES,
                              sSTATIC_PRESSURE_FREE_DIR_5G_SURFEL_BC_VALUES,
                              sSTATIC_PRESSURE_FIXED_DIR_5G_SURFEL_BC_VALUES,
                              sSOURCE_5G_SURFEL_BC_VALUES,
                              sMASS_FLUX_5G_SURFEL_BC_VALUES,
                              sNOSLIP_5G_SURFEL_BC_VALUES,
                              sANGULAR_NOSLIP_5G_SURFEL_BC_VALUES,
                              sLINEAR_NOSLIP_5G_SURFEL_BC_VALUES,
#ifdef BUILD_THERMAL_BC_VALUES
                              sSLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sSLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
                              sANGULAR_SLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sANGULAR_SLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
                              sLINEAR_SLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sLINEAR_SLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sLINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
                              sNOSLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sNOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sNOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
                              sANGULAR_NOSLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
                              sLINEAR_NOSLIP_FIXED_TEMP_SURFEL_BC_VALUES,
                              sLINEAR_NOSLIP_THERMAL_RESIST_SURFEL_BC_VALUES,
                              sLINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_BC_VALUES,
#endif
                              sNOSLIP_5G_SURFEL_BC_VALUES
                              >();

/*templated free function that mimics num_of_vars() without copy-paste development*/
template < typename PARAMS_T>
static constexpr int parameters_num_of_vars()
{
  return sizeof(PARAMS_T) / sizeof(sPHYSICS_VARIABLE);
}
/* This linked types relate parameters to bc_values so that we can infer bc_vales from parameter type. 
    Useful to avoid extra template arguments*/
template <typename PARAMS_T> struct sLink_param_bcvalues_t {};
#define GENERATE_LINK_TYPE(SURFEL_NAME) \
template <> struct sLink_param_bcvalues_t<SURFEL_NAME ## _PARAMETERS> { \
  using BC_VALUES_T = s ## SURFEL_NAME ## _BC_VALUES; \
  /* If anyone adds or remove a variable from any paramter class its corresponding bc_value needs to be updated accordingly*/\
  static_assert( parameters_num_of_vars<typename std::remove_pointer<SURFEL_NAME ## _PARAMETERS>::type>() == BC_VALUES_T::num_vars(), \
  "The variable layout of PARAMETERS and BC_VALUES must be identical");\
};
                                                      
GENERATE_LINK_TYPE(SLIP_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_SLIP_SURFEL)
GENERATE_LINK_TYPE(LINEAR_SLIP_SURFEL)
GENERATE_LINK_TYPE(VEL_SLIP_SURFEL)
GENERATE_LINK_TYPE(NOSLIP_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_NOSLIP_SURFEL)
GENERATE_LINK_TYPE(LINEAR_NOSLIP_SURFEL)
GENERATE_LINK_TYPE(IO_UDS)
GENERATE_LINK_TYPE(MASS_FLUX_SURFEL)
GENERATE_LINK_TYPE(MASS_FLOW_SURFEL)
GENERATE_LINK_TYPE(FIXED_VEL_SURFEL)
GENERATE_LINK_TYPE(SOURCE_SURFEL)
GENERATE_LINK_TYPE(PASS_THRU_SURFEL)
GENERATE_LINK_TYPE(PRESSURE_FREE_DIR_SURFEL)
GENERATE_LINK_TYPE(STATIC_PRESSURE_FREE_DIR_SURFEL)
GENERATE_LINK_TYPE(STAG_PRESSURE_FREE_DIR_SURFEL)
GENERATE_LINK_TYPE(PRESSURE_FIXED_DIR_SURFEL)
GENERATE_LINK_TYPE(STATIC_PRESSURE_FIXED_DIR_SURFEL)
GENERATE_LINK_TYPE(STAG_PRESSURE_FIXED_DIR_SURFEL)
GENERATE_LINK_TYPE(PRESSURE_FREE_DIR_5G_SURFEL)
GENERATE_LINK_TYPE(STATIC_PRESSURE_FREE_DIR_5G_SURFEL)
GENERATE_LINK_TYPE(STATIC_PRESSURE_FIXED_DIR_5G_SURFEL)
GENERATE_LINK_TYPE(SOURCE_5G_SURFEL)
GENERATE_LINK_TYPE(MASS_FLUX_5G_SURFEL)
GENERATE_LINK_TYPE(NOSLIP_5G_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_NOSLIP_5G_SURFEL)
GENERATE_LINK_TYPE(LINEAR_NOSLIP_5G_SURFEL)
#ifdef BUILD_THERMAL_BC_VALUES                
GENERATE_LINK_TYPE(SLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(SLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(SLIP_FIXED_HEAT_FLUX_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_SLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_SLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL)
GENERATE_LINK_TYPE(LINEAR_SLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(LINEAR_SLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL)
GENERATE_LINK_TYPE(NOSLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(NOSLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(NOSLIP_FIXED_HEAT_FLUX_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_NOSLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL)
GENERATE_LINK_TYPE(LINEAR_NOSLIP_FIXED_TEMP_SURFEL)
GENERATE_LINK_TYPE(LINEAR_NOSLIP_THERMAL_RESIST_SURFEL)
GENERATE_LINK_TYPE(LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL)
#endif
#undef DEVICE_METHODS
#undef sSURFEL_BC_VALUES_SPECIALIZATION

//for conduction solver
typedef struct sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE shell_configuration;
  sPHYSICS_VARIABLE radiation_surface_condition;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_ADIABATIC_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a conduction wall", CDI_PHYS_TYPE_ADIABATIC, ptd->cdi_physics_type);

    asINT32 i = 0;
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SHELL_CONFIGURATION
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_SURFACE_COND
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *CONDUCTION_ADIABATIC_SURFEL_PARAMETERS;

typedef struct sCONDUCTION_COUPLED_THERMAL_SURFEL_PARAMETERS : public sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS { 

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_COUPLED_THERMAL_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a conduction coupled thermal wall", CDI_PHYS_TYPE_COUPLED_THERMAL, ptd->cdi_physics_type);

    asINT32 i = sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *CONDUCTION_COUPLED_THERMAL_SURFEL_PARAMETERS;

typedef struct sCONDUCTION_FIXED_TEMP_SURFEL_PARAMETERS : public sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE temperature;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_FIXED_TEMP_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed temperature conduction wall", CDI_PHYS_TYPE_FIXED_TEMP, ptd->cdi_physics_type);
    
    asINT32 i = sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *CONDUCTION_FIXED_TEMP_SURFEL_PARAMETERS;

typedef struct sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS : public sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS { 
  sPHYSICS_VARIABLE ambient_temp;
  sPHYSICS_VARIABLE ext_heat_xfer_coeff;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class) {
      asINT32 expected_cdi_type = CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP;
      // For conduction surfels, a THERMAL_RESIST BC is mapped to a FIXED_HTC_AMBIENT_TEMP BC
      if (ptd->cdi_physics_type == CDI_PHYS_TYPE_THERMAL_RESIST)
        expected_cdi_type = CDI_PHYS_TYPE_THERMAL_RESIST;
      do_check_cdi_phys_type("a thermal resistance wall", expected_cdi_type, ptd->cdi_physics_type);
    }

    asINT32 i = sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TEMP
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }
} *CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS;

typedef struct sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS : public sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE heat_flux_1;
  sPHYSICS_VARIABLE heat_flux_2;
  sPHYSICS_VARIABLE face_area; 
  sPHYSICS_VARIABLE is_heat_flow; // if > 0, this is a heat flow bc, else it is a fixed heat flux bc

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a fixed heat flux wall", CDI_PHYS_TYPE_FIXED_HEAT_FLUX, 
                             ptd->cdi_physics_type);
    
    asINT32 i = sCONDUCTION_ADIABATIC_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_HEAT_FLUX && ptd->continuous_dp_var[i]->id != CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_IN)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_HEAT_FLUX_2 && ptd->continuous_dp_var[i]->id != CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_OUT)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS;

typedef struct sCONDUCTION_CONTACT_SURFEL_PARAMETERS { 
  sPHYSICS_VARIABLE contact_resistance;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_CONTACT_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a conduction coupled thermal wall", CDI_PHYS_TYPE_CONTACT_RESISTANCE, ptd->cdi_physics_type);

    asINT32 i = 0;
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONTACT_RESISTANCE
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }

} *CONDUCTION_CONTACT_SURFEL_PARAMETERS;

// When this is used as a conduction surfel type, the last 2 parameters are unused (wall_thickness, wall_conductivity).
// The code sees a physics descriptor of type FIXED_HTC_AMBIENT_TEMP.
typedef struct sTHERMAL_RESIST_SURFEL_PARAMETERS  : public sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS {
  sPHYSICS_VARIABLE wall_thickness;
  sPHYSICS_VARIABLE wall_conductivity;
  
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "THERMAL_RESIST_SURFEL_PARAMETERS", 
                            BOOLEAN is_called_from_derived_class = FALSE) {
    if (!is_called_from_derived_class)
      do_check_cdi_phys_type("a thermal resistance wall", CDI_PHYS_TYPE_THERMAL_RESIST, 
                             ptd->cdi_physics_type);

    asINT32 i = sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS::check_consistency(ptd, name, TRUE);

    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WALL_CONDUCTIVITY
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    }
    return i;
  }
} *THERMAL_RESIST_SURFEL_PARAMETERS;
//end conduction solver

#define define_wall_base_surfel_physics_descriptor(NAME)			\
  typedef struct s ## NAME ## _SURFEL_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {	\
    									\
    __HOST__DEVICE__ NAME ## _SURFEL_PARAMETERS parameters()		\
    { return (NAME ## _SURFEL_PARAMETERS)_parameters; }			\
    									\
    __HOST__DEVICE__ WALL_UDS_PARAMETERS uds_parameters()               \
    { return (WALL_UDS_PARAMETERS)_uds_parameters; }			\
                                                                        \
    VOID check_consistency()						\
    { parameters()->check_consistency(phys_type_desc); 		        \
      uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);  \
    }                                                                         \
  } *NAME ## _SURFEL_PHYSICS_DESCRIPTOR

#define define_wall_surfel_physics_descriptor(NAME, BASE)			\
  typedef struct s ## NAME ## _SURFEL_PHYSICS_DESCRIPTOR : public s ## BASE ## _SURFEL_PHYSICS_DESCRIPTOR { \
    									\
    __HOST__DEVICE__  NAME ## _SURFEL_PARAMETERS parameters()		\
    { return (NAME ## _SURFEL_PARAMETERS)_parameters; }			\
    									\
    __HOST__DEVICE__ WALL_UDS_PARAMETERS uds_parameters()               \
    { return (WALL_UDS_PARAMETERS)_uds_parameters; }			\
                                                                        \
    VOID check_consistency()						\
    { parameters()->check_consistency(phys_type_desc);  		\
      uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);  \
    }                                                                         \
  } *NAME ## _SURFEL_PHYSICS_DESCRIPTOR



#define define_io_base_surfel_physics_descriptor(NAME)			\
  typedef struct s ## NAME ## _SURFEL_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {	\
    									\
    __HOST__DEVICE__ NAME ## _SURFEL_PARAMETERS parameters()		\
    { return (NAME ## _SURFEL_PARAMETERS)_parameters; }			\
    									\
    __HOST__DEVICE__ IO_UDS_PARAMETERS uds_parameters()                 \
    { return (IO_UDS_PARAMETERS)_uds_parameters; }			\
                                                                        \
    VOID check_consistency()						\
    { parameters()->check_consistency(phys_type_desc); 		        \
      uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);  \
    }                                                                         \
  } *NAME ## _SURFEL_PHYSICS_DESCRIPTOR

#define define_io_surfel_physics_descriptor(NAME, BASE)			\
  typedef struct s ## NAME ## _SURFEL_PHYSICS_DESCRIPTOR : public s ## BASE ## _SURFEL_PHYSICS_DESCRIPTOR { \
    									\
    __HOST__DEVICE__  NAME ## _SURFEL_PARAMETERS parameters()		\
    { return (NAME ## _SURFEL_PARAMETERS)_parameters; }			\
    									\
    __HOST__DEVICE__ IO_UDS_PARAMETERS uds_parameters()                 \
    { return (IO_UDS_PARAMETERS)_uds_parameters; }			\
                                                                        \
    VOID check_consistency()						\
    { parameters()->check_consistency(phys_type_desc); 		        \
      uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);  \
    }                                                                         \
  } *NAME ## _SURFEL_PHYSICS_DESCRIPTOR




//wall
define_wall_base_surfel_physics_descriptor(SLIP);
define_wall_surfel_physics_descriptor(SLIP_FIXED_TEMP, SLIP);
define_wall_surfel_physics_descriptor(SLIP_THERMAL_RESIST, SLIP);
define_wall_surfel_physics_descriptor(SLIP_FIXED_HEAT_FLUX, SLIP);
define_wall_surfel_physics_descriptor(ANGULAR_SLIP, SLIP);
define_wall_surfel_physics_descriptor(LINEAR_SLIP, SLIP);
define_wall_surfel_physics_descriptor(VEL_SLIP, SLIP);
define_wall_surfel_physics_descriptor(ANGULAR_SLIP_FIXED_TEMP, ANGULAR_SLIP);
define_wall_surfel_physics_descriptor(ANGULAR_SLIP_THERMAL_RESIST, ANGULAR_SLIP);
define_wall_surfel_physics_descriptor(ANGULAR_SLIP_FIXED_HEAT_FLUX, ANGULAR_SLIP);
define_wall_surfel_physics_descriptor(LINEAR_SLIP_FIXED_TEMP, LINEAR_SLIP);
define_wall_surfel_physics_descriptor(LINEAR_SLIP_THERMAL_RESIST, LINEAR_SLIP);
define_wall_surfel_physics_descriptor(LINEAR_SLIP_FIXED_HEAT_FLUX, LINEAR_SLIP);
define_wall_base_surfel_physics_descriptor(NOSLIP);
define_wall_surfel_physics_descriptor(NOSLIP_FIXED_TEMP, NOSLIP);
define_wall_surfel_physics_descriptor(NOSLIP_THERMAL_RESIST, NOSLIP);
define_wall_surfel_physics_descriptor(NOSLIP_FIXED_HEAT_FLUX, NOSLIP);
define_wall_surfel_physics_descriptor(ANGULAR_NOSLIP, NOSLIP);
define_wall_surfel_physics_descriptor(LINEAR_NOSLIP, NOSLIP);
define_wall_surfel_physics_descriptor(ANGULAR_NOSLIP_FIXED_TEMP, ANGULAR_NOSLIP);
define_wall_surfel_physics_descriptor(ANGULAR_NOSLIP_THERMAL_RESIST, ANGULAR_NOSLIP);
define_wall_surfel_physics_descriptor(ANGULAR_NOSLIP_FIXED_HEAT_FLUX, ANGULAR_NOSLIP);
define_wall_surfel_physics_descriptor(LINEAR_NOSLIP_FIXED_TEMP, LINEAR_NOSLIP);
define_wall_surfel_physics_descriptor(LINEAR_NOSLIP_THERMAL_RESIST, LINEAR_NOSLIP);
define_wall_surfel_physics_descriptor(LINEAR_NOSLIP_FIXED_HEAT_FLUX, LINEAR_NOSLIP);

//inlet/outlet
define_io_base_surfel_physics_descriptor(MASS_FLUX);
define_io_base_surfel_physics_descriptor(MASS_FLOW);
define_io_base_surfel_physics_descriptor(FIXED_VEL);
define_io_base_surfel_physics_descriptor(SOURCE);
define_io_base_surfel_physics_descriptor(PASS_THRU);
define_io_base_surfel_physics_descriptor(PRESSURE_FREE_DIR);
define_io_surfel_physics_descriptor(PRESSURE_FIXED_DIR, PRESSURE_FREE_DIR);
define_io_surfel_physics_descriptor(STAG_PRESSURE_FREE_DIR, PRESSURE_FREE_DIR);
define_io_surfel_physics_descriptor(STATIC_PRESSURE_FREE_DIR, PRESSURE_FREE_DIR);
define_io_surfel_physics_descriptor(STAG_PRESSURE_FIXED_DIR, PRESSURE_FIXED_DIR);
define_io_surfel_physics_descriptor(STATIC_PRESSURE_FIXED_DIR, PRESSURE_FIXED_DIR);

//for 5G
//io
define_io_base_surfel_physics_descriptor(PRESSURE_FREE_DIR_5G);
define_io_surfel_physics_descriptor(STATIC_PRESSURE_FREE_DIR_5G, PRESSURE_FREE_DIR_5G);
define_io_surfel_physics_descriptor(STATIC_PRESSURE_FIXED_DIR_5G, PRESSURE_FREE_DIR_5G);
define_io_surfel_physics_descriptor(SOURCE_5G, PRESSURE_FREE_DIR_5G);
define_io_base_surfel_physics_descriptor(MASS_FLUX_5G);
//wall
define_wall_base_surfel_physics_descriptor(NOSLIP_5G);
define_wall_surfel_physics_descriptor(ANGULAR_NOSLIP_5G, NOSLIP_5G);
define_wall_surfel_physics_descriptor(LINEAR_NOSLIP_5G, NOSLIP_5G);
// end 5G

//for conduction solver
define_wall_base_surfel_physics_descriptor(CONDUCTION_ADIABATIC);
define_wall_surfel_physics_descriptor(CONDUCTION_COUPLED_THERMAL, CONDUCTION_ADIABATIC);
define_wall_surfel_physics_descriptor(CONDUCTION_FIXED_TEMP, CONDUCTION_ADIABATIC);
define_wall_surfel_physics_descriptor(CONDUCTION_FIXED_HTC_AMBIENT_TEMP, CONDUCTION_ADIABATIC);
define_wall_surfel_physics_descriptor(CONDUCTION_FIXED_HEAT_FLUX, CONDUCTION_ADIABATIC);
define_wall_base_surfel_physics_descriptor(CONDUCTION_CONTACT);
define_wall_surfel_physics_descriptor(THERMAL_RESIST, CONDUCTION_FIXED_HTC_AMBIENT_TEMP); 
//end conduction solver

__HOST__DEVICE__
inline VOID calculate_mlrf_rotating_surfel_position(dFLOAT angle_rotated,
                                                    asINT32 n_surfels_in_ring,
                                                    BOOLEAN is_exterior_surfel,
                                                    dFLOAT surfel_overlapping_ratio[2])
{
  if (n_surfels_in_ring == 1) {
    surfel_overlapping_ratio[0] = 1.0;
    surfel_overlapping_ratio[1] = 0.0;
  } else { 
    asINT32 index_back;
    asINT32 index_forward;
    if(is_exterior_surfel) {
      index_back = 0;
      index_forward = 1;
    } else {
      index_back = 1;
      index_forward = 0;
    }

    dFLOAT half_angle_per_surfel = PI / n_surfels_in_ring;
    dFLOAT angle_per_surfel = 2.0 * half_angle_per_surfel;
    dFLOAT angle_ovelapped = angle_rotated - (asINT32)(angle_rotated / angle_per_surfel) * angle_per_surfel;
    surfel_overlapping_ratio[index_back] = 0.5 - 0.5 * (tan(half_angle_per_surfel - angle_ovelapped) / tan(half_angle_per_surfel));

    // on 32-bit linux in optimized mode, the above floating point calculation
    // can result in small negative numbers, which causes problems in assigning lrf surfel
    // solver data values in mlrf_surfel_dynamics
    if (surfel_overlapping_ratio[index_back] < 0.0) 
      surfel_overlapping_ratio[index_back] = 0.0;

    surfel_overlapping_ratio[index_forward] = 1.0 - surfel_overlapping_ratio[index_back];
  }
}


/* It seems that we can not use the sign of angle_rotated to
 * determine whether the rotation is clockwise or not because
 * rotation could be time dependent.
 */
inline VOID calculate_ring_offset_index(asINT32 ring_offset_index[2],
                                          asINT32 angle_offset,
                                          asINT32 index_in_ring,
                                          asINT32 n_surfels_in_ring,
                                          BOOLEAN is_interior_surfel)
{
  if (is_interior_surfel) {
    ring_offset_index[0] = index_in_ring + angle_offset;
    ring_offset_index[1] = ring_offset_index[0] + 1;
    /* Note: 0 <= angle_rotated < 2*PI */
    if (ring_offset_index[1] > n_surfels_in_ring - 1) {
      ring_offset_index[1] -= n_surfels_in_ring;
      if (ring_offset_index[0] > n_surfels_in_ring - 1)
        ring_offset_index[0] -= n_surfels_in_ring;
    }
  } else {
    ring_offset_index[0] = index_in_ring - angle_offset;
    ring_offset_index[1] = ring_offset_index[0] - 1;
    /* Note: 0 <= angle_rotated < 2*PI */
    if (ring_offset_index[1] < 0) {
      ring_offset_index[1] += n_surfels_in_ring;
      if (ring_offset_index[0] < 0)
        ring_offset_index[0] += n_surfels_in_ring;
    }
  }
}

__HOST__DEVICE__
inline VOID check_fixed_vel_velocity(PHYSICS_DESCRIPTOR pd,
                                     STP_GEOM_VARIABLE surfel_centroid[3],
                                     STP_PHYS_VARIABLE vel[3],
                                     SCALE scale)
{
  STP_PHYS_VARIABLE vmag_sqrd = vdot(vel, vel);
  auto& simc = get_simc_ref();
  if (vmag_sqrd > simc.max_surfel_bc_vel_sqrd) {
#if !GPU_COMPILER    
    if (!sim.is_user_max_vel_default && (vmag_sqrd > sim.user_max_vel_sqrd)) {
      simerr_report_error_code(SP_EER_VELOCITY_USER_MAX, scale, surfel_centroid,
			       pd->name, sqrt(vmag_sqrd), sim.user_max_vel);
    }
    else if (vmag_sqrd > (STP_PHYS_VARIABLE )sim.max_surfel_bc_vel_default_sqrd) {
      simerr_report_error_code(SP_EER_VELOCITY_MAX, scale, surfel_centroid,
			       pd->name, sqrt(vmag_sqrd), sim.max_surfel_bc_vel_default);
    }
#endif    
  }
}

inline VOID check_source_velocity(PHYSICS_DESCRIPTOR pd, STP_GEOM_VARIABLE surfel_centroid[3], STP_PHYS_VARIABLE vel[3],
                                  SCALE scale)
{
  STP_PHYS_VARIABLE vmag_sqrd = vdot(vel, vel);
#if BUILD_5G_LATTICE
  vmag_sqrd *= g_one_over_lattice_constant * g_one_over_lattice_constant;
#endif
  if (vmag_sqrd > sim.max_surfel_bc_vel_sqrd) {
    if (!sim.is_user_max_vel_default && (vmag_sqrd > sim.user_max_vel_sqrd)) {
      simerr_report_error_code(SP_EER_SOURCE_VEL_USER_MAX, scale, surfel_centroid,
			       pd->name, sqrt(vmag_sqrd), sim.user_max_vel);
    }
    else if (vmag_sqrd > (STP_PHYS_VARIABLE )sim.max_surfel_bc_vel_default_sqrd) {
      simerr_report_error_code(SP_EER_SOURCE_VEL_MAX, scale, surfel_centroid,
			       pd->name, sqrt(vmag_sqrd), sim.max_surfel_bc_vel_default);
    }
  }
}

inline VOID check_mass_flux(PHYSICS_DESCRIPTOR pd, STP_GEOM_VARIABLE surfel_centroid[3], STP_PHYS_VARIABLE mass_flux[3],
                            SCALE scale)
{
  // @@@ May be no need to check mass flux once we switch to floating point code
  STP_PHYS_VARIABLE mass_flux_sqrd = vdot(mass_flux, mass_flux);
  STP_PHYS_VARIABLE max_bc_mass_flux_sqrd_used = (STP_PHYS_VARIABLE)sim.max_bc_mass_flux_sqrd;
#if BUILD_D19_LATTICE
  if(sim.is_pf_model) {
    max_bc_mass_flux_sqrd_used *= g_Dens_heavy * g_Dens_heavy / (g_Dens_light * g_Dens_light);
  }
#endif

  if (mass_flux_sqrd > max_bc_mass_flux_sqrd_used) {
    simerr_report_error_code(SP_EER_MASS_FLUX_MAX, scale, surfel_centroid,
			     pd->name, sqrt(mass_flux_sqrd), sim.max_bc_mass_flux);
  }
}

__HOST__DEVICE__
inline VOID check_moving_wall_velocity(PHYSICS_DESCRIPTOR pd, STP_GEOM_VARIABLE surfel_centroid[3], 
                                       STP_PHYS_VARIABLE vel[3], BOOLEAN is_rotating_wall,
                                       sdFLOAT max_surfel_vel_sqrd, SCALE scale)
{
  STP_PHYS_VARIABLE vmag_sqrd = vdot(vel, vel);
#if BUILD_5G_LATTICE
  vmag_sqrd *= g_one_over_lattice_constant * g_one_over_lattice_constant;
#endif
  auto& simc = get_simc_ref();
  if (!simc.is_user_max_vel_default && (vmag_sqrd > simc.user_max_vel_sqrd)) {
#if !GPU_COMPILER
    simerr_report_error_code(is_rotating_wall ? SP_EER_ROTATING_WALL_VEL_USER_MAX : SP_EER_SLIDING_WALL_VEL_USER_MAX, 
                             scale, surfel_centroid, pd->name, sqrt(vmag_sqrd), sim.user_max_vel);
#endif    
  }
  else if (vmag_sqrd > max_surfel_vel_sqrd) {
#if !GPU_COMPILER    
    if (simc.is_high_subsonic_mach_regime && (g_Mach_cap > 0))
    {    
      simerr_report_error_code(is_rotating_wall ? SP_EER_ROTATING_WALL_MACH_MAX : SP_EER_SLIDING_WALL_MACH_MAX, 
                               scale, surfel_centroid, pd->name,
                               sqrt(vmag_sqrd/max_surfel_vel_sqrd) * g_Mach_cap, g_Mach_cap);
    }
    else
    {
      simerr_report_error_code(is_rotating_wall ? SP_EER_ROTATING_WALL_VEL_MAX : SP_EER_SLIDING_WALL_VEL_MAX, 
                               scale, surfel_centroid, pd->name, sqrt(vmag_sqrd), sqrt(max_surfel_vel_sqrd));
    }
#endif    
  }
}

inline VOID convert_sliding_vel_between_ref_frames(PHYSICS_DESCRIPTOR pd,
                                                   STP_PHYS_VARIABLE input_vel[3], 
                                                   STP_GEOM_VARIABLE surfel_centroid[3], 
                                                   STP_PHYS_VARIABLE output_vel[3],
                                                   asINT32 vel_ref_frame_index,
                                                   asINT32 surfel_ref_frame_index,
                                                   SCALE scale)
{
  convert_vel_between_ref_frames(input_vel, surfel_centroid, output_vel, vel_ref_frame_index, surfel_ref_frame_index);
  auto& simc = get_simc_ref();
  check_moving_wall_velocity(pd, surfel_centroid, output_vel, TRUE, simc.max_moving_surface_vel_sqrd, scale);
}

__HOST__DEVICE__
inline VOID convert_fixed_vel_between_ref_frames(PHYSICS_DESCRIPTOR pd,
                                                 STP_PHYS_VARIABLE input_vel[3], 
                                                 STP_GEOM_VARIABLE surfel_centroid[3], 
                                                 STP_PHYS_VARIABLE output_vel[3],
                                                 asINT32 vel_ref_frame_index,
                                                 asINT32 surfel_ref_frame_index,
                                                 SCALE scale)
{
  convert_vel_between_ref_frames(input_vel, surfel_centroid, output_vel, vel_ref_frame_index, surfel_ref_frame_index);
  check_fixed_vel_velocity(pd, surfel_centroid, output_vel, scale);
}

template<size_t N>
__HOST__DEVICE__
inline VOID convert_fixed_vel_between_ref_frames(PHYSICS_DESCRIPTOR pd,
                                                 tSFL_VAR<STP_PHYS_VARIABLE, N> (&quantum_vel)[3], 
                                                 tSFL_VAR<STP_GEOM_VARIABLE, N> (&surfel_centroid)[3],
                                                 STP_PHYS_VARIABLE output_vel[3],
                                                 asINT32 vel_ref_frame_index,
                                                 asINT32 surfel_ref_frame_index,
                                                 SCALE scale,
                                                 asINT32 soxor)
{
  STP_PHYS_VARIABLE input_vel[3];
  v_copy(input_vel, quantum_vel, soxor);
  STP_GEOM_VARIABLE centroid[3];
  v_copy(centroid, surfel_centroid, soxor);
  convert_fixed_vel_between_ref_frames(pd, input_vel, centroid,
                                       output_vel, vel_ref_frame_index,
                                       surfel_ref_frame_index, scale);
}

__HOST__DEVICE__
inline VOID convert_source_vel_between_ref_frames(PHYSICS_DESCRIPTOR pd,
                                                  STP_PHYS_VARIABLE input_vel[3], 
                                                  STP_GEOM_VARIABLE surfel_centroid[3], 
                                                  STP_PHYS_VARIABLE output_vel[3],
                                                  asINT32 vel_ref_frame_index,
                                                  asINT32 surfel_ref_frame_index,
                                                  SCALE scale)
{
  convert_vel_between_ref_frames(input_vel, surfel_centroid, output_vel, vel_ref_frame_index, surfel_ref_frame_index);
#if !GPU_COMPILER  
  check_source_velocity(pd, surfel_centroid, output_vel, scale);
#endif  
}

inline VOID convert_mass_flux_between_ref_frames(PHYSICS_DESCRIPTOR pd,
                                                 STP_PHYS_VARIABLE input_mass_flux[3], 
                                                 STP_GEOM_VARIABLE surfel_centroid[3], 
                                                 STP_PHYS_VARIABLE output_mass_flux[3], 
                                                 STP_PHYS_VARIABLE density,
                                                 asINT32 vel_ref_frame_index,
                                                 asINT32 surfel_ref_frame_index,
                                                 SCALE scale)
{
  convert_mflux_between_ref_frames(input_mass_flux, surfel_centroid, output_mass_flux, density,
				   vel_ref_frame_index, surfel_ref_frame_index);

  check_mass_flux(pd, surfel_centroid, output_mass_flux, scale);
}

VOID split_coupling_meas_window_surfel_groups(MEAS_WINDOW coupling_meas_window);
VOID update_surfel_ublk_interactions();

VOID collect_sp_timer_counters();

VOID reinitialize_dynamics_data_for_dependent_surfels(PHYSICS_DESCRIPTOR pd);
VOID append_dependent_coupling_surfel_ids(PHYSICS_DESCRIPTOR pd, STP_SURFEL_ID **ids);
uINT32 num_dependent_coupling_surfels(PHYSICS_DESCRIPTOR pd);
VOID assign_coupling_temp_to_dependent_surfels(COUPLING_MODEL coupling_model,
                                               sFLOAT *coupling_temp_data);

/*****************************************************************************
 *                      S2S
 ****************************************************************************/
template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID surfels_clear_and_s2s(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                           SURFEL_GROUP surfel_group,
                           sSURFEL_V2S_DATA surfel_v2s_datasets[]);

template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID do_surfel_pairs_s2s(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                         SURFEL_PAIR_GROUP surfel_pair_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

VOID do_sampling_surfels_s2s(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                             sSAMPLING_SURFEL_GROUP *surfel_group);

/*****************************************************************************
 *                      V2S
 ****************************************************************************/
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID do_surfels_v2s_flow(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                         SURFEL_GROUP surfel_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF>
VOID do_surfel_pairs_v2s(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                         SURFEL_PAIR_GROUP surfel_pair_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

VOID do_sampling_surfels_v2s(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                             sSAMPLING_SURFEL_GROUP *surfel_group);

/*****************************************************************************
 *                      DYNAMICS
 ****************************************************************************/
VOID do_surfels_dyn_flow(ACTIVE_SOLVER_MASK active_solver_masks[2],
                         ACTIVE_SOLVER_MASK even_active_solver_masks[2],
                         ACTIVE_SOLVER_MASK odd_active_solver_masks[2],
                         BOOLEAN is_time_step_odd,
                         STP_GEOM_VARIABLE group_voxel_size,
                         STP_GEOM_VARIABLE meas_scale_factor,
                         asINT32 first_index, asINT32 last_index,
                         SURFEL_GROUP surfel_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_SLRF>
VOID do_surfel_pairs_dyn(ACTIVE_SOLVER_MASK active_solver_masks[2],
                         BOOLEAN is_time_step_odd, SCALE scale,
                         dFLOAT one_over_delta_t,
                         asINT32 first_index, asINT32 last_index,
                         SURFEL_PAIR_GROUP surfel_pair_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

VOID do_sampling_surfels_dyn(ACTIVE_SOLVER_MASK active_solver_masks[2],
                             ACTIVE_SOLVER_MASK even_active_solver_masks[2],
                             ACTIVE_SOLVER_MASK odd_active_solver_masks[2],
                             BOOLEAN is_time_step_odd,
                             asINT32 first_index, asINT32 last_index,
                             sSAMPLING_SURFEL_GROUP *surfel_group);
/*****************************************************************************
 *                      S2V
 ****************************************************************************/
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID do_surfels_s2v_flow(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                         SURFEL_GROUP surfel_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID do_ghost_surfels_s2v(ACTIVE_SOLVER_MASK active_solver_masks[2],
                          BOOLEAN is_time_step_odd,
                          asINT32 coarse_time_step_index,
                          uINT32 allowed_s2v_scale_diff,
                          asINT32 allowed_s2v_phase_mask,
                          SOLVER_INDEX_MASK prior_solver_index_mask,
                          sSURFEL_RECV_GROUP *surfel_recv_group);

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF>
VOID do_surfel_pairs_s2v(sSURFEL_PROCESS_CONTROL &v2s_s2v_control,
                         SURFEL_PAIR_GROUP surfel_pair_group,
                         sSURFEL_V2S_DATA surfel_v2s_datasets[]);

VOID check_consistency_and_validate_surface_physics_descriptors();
asINT32 dyn_surfel_type_from_stp_physics_type(STP_PHYSTYPE_TYPE phys_type);
#endif	/* #ifndef _SIMENG_SURFEL_DYN_H */
