/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Shared object (voxel or surfel) dynamics groups
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SHOB_DYN_H
#define _SIMENG_SHOB_DYN_H

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "meas.h"
#include "eqns.h"
#include "fset.h"

/*--------------------------------------------------------------------------*
 * SHOB_DYN_GROUP (shared object dynamics group) abstract type definition
 *--------------------------------------------------------------------------*/
struct sSHOB_DYN_GROUP; // So can be referenced in sDEPENDENT_SHOB_DYN_GROUP

/* This is used to hang a list of shob dyn groups off a physics descriptor. */
typedef struct sDEPENDENT_SHOB_DYN_GROUP 
{
  sSHOB_DYN_GROUP *shob_dyn_group;
  sDEPENDENT_SHOB_DYN_GROUP *next;
} *DEPENDENT_SHOB_DYN_GROUP;

typedef class sSHOB_DYN_GROUP : public sCKPT_GROUP
{
public:
  STP_PHYSTYPE_TYPE  m_type;
  PHYSICS_DESCRIPTOR m_physics_descriptor;

  /* PHYSICS_DESC is intended to be specialized by each different shob 
   * dyn group, so that each group returns an appropriately typed pointer. */
  PHYSICS_DESCRIPTOR physics_descriptor() { return m_physics_descriptor; }
  STP_PHYSTYPE_TYPE  type()               { return m_type;               } 

  LRF_PHYSICS_DESCRIPTOR        lrf_physics_desc;
  BODY_FORCE_PHYSICS_DESCRIPTOR body_force_desc;
  
protected:  
  virtual SHOB_ID      _n_quantums() = 0;

  // M_MEAS_CELL_PTRS is a vector of pointers to meas cells. For a surfel dyn
  // group, each quantum owns N_MEAS_WINDOWS of these pointers. For a voxel dyn 
  // group, for each quantum, there are one or more of these pointers per meas 
  // window, because in special cases, a ublk can have multiple meas cell refs 
  // for one meas window.
  VMEM_VECTOR < MEAS_CELL_PTR >  m_meas_cell_ptrs;

public:

  SHOB_ID      n_quantums()             { return _n_quantums(); }
   
  virtual STP_EVEN_ODD even_odd_mask()  { return STP_PROCESS_ON_ALL_TIMES; }

  virtual asINT32 quantum_size() = 0;	/* Size of each shob quantum */
  
  virtual VOID validate_physics_desc() = 0;
  
  MEAS_CELL_PTR *meas_cell_ptrs()       { return &m_meas_cell_ptrs.front(); }
  asINT32       n_meas_cell_ptrs()      { return m_meas_cell_ptrs.size();   }

  virtual VOID trim()                   { m_meas_cell_ptrs.trim();          }
  
  MEAS_CELL_PTR *add_meas_cell_index(STP_MEAS_CELL_INDEX meas_cell_index) 
  {
    MEAS_CELL_PTR meas_cell_ptr(meas_cell_index); // index will be converted to pointer later
    m_meas_cell_ptrs.push_back(meas_cell_ptr);
    return &m_meas_cell_ptrs.back();
  }

  /* Called to update quantums after table read */
  virtual VOID reinitialize_quantums() = 0;

  sSHOB_DYN_GROUP()
  {
    m_physics_descriptor = NULL;
    lrf_physics_desc     = NULL;
    body_force_desc      = NULL;

    if (!g_no_reserve_addr_space)
      m_meas_cell_ptrs.reserve(128 * 1024);
  }

  virtual VOID add_to_ckpt_queue() = 0;

} *SHOB_DYN_GROUP;

extern "C" int shob_dyn_group_cmp(const VOID *arg1, const VOID *arg2, const VOID *free);

#define DOTIMES_SHOB_DYN_GROUP_QUANTUMS(quantum_var, i, group)                          \
  SHOB_QUANTUM quantum_var = (group)->quantums();                                       \
  asINT32 ___(_sizeof_quantum) = (group)->quantum_size();                               \
  SHOB_ID ___(_n_quantums) = (group)->n_quantums();                                     \
  SHOB_ID i;                                                                            \
  for (i = 0;                                                                           \
       i < ___(_n_quantums);                                                            \
       i++,                                                                             \
	 quantum_var = (SHOB_QUANTUM)((char *)quantum_var + ___(_sizeof_quantum)))

#define DO_SHOB_DYN_GROUP_QUANTUMS(quantum_var, group)		\
  DOTIMES_SHOB_DYN_GROUP_QUANTUMS(quantum_var, ___(i), group)


#if NOT_DGF
typedef struct sSHOB_DYN_FSET : public sFSET {
  VOID add_group(SHOB_DYN_GROUP group) {
    sFSET::add_group(group);
    PHYSICS_DESCRIPTOR physics_descriptor = group->physics_descriptor();
    // Sampling surfel dyn groups will not have a physics descriptor
    if (physics_descriptor != NULL) {
      DEPENDENT_SHOB_DYN_GROUP d = xnew sDEPENDENT_SHOB_DYN_GROUP;
      d->shob_dyn_group = group;
      d->next = physics_descriptor->dependent_shob_dyn_groups;
      physics_descriptor->dependent_shob_dyn_groups = d;
    }
  }
} *SHOB_DYN_FSET;


/*--------------------------------------------------------------------------*
 * Interating over all the shob dyn groups
 *--------------------------------------------------------------------------*/

#define DO_SHOB_DYN_GROUPS(group_var, fset)	\
  FSET_DO_GROUPS(SHOB_DYN_GROUP, group_var,	\
		 fset)

#define DOTIMES_SHOB_DYN_GROUPS(group_var, index_var, fset)	\
  FSET_DOTIMES_GROUPS(SHOB_DYN_GROUP, group_var, index_var,	\
		      fset)

#define DO_SHOB_DYN_GROUPS_OF_SCALE(group_var, scale, fset)	\
  FSET_DO_GROUPS_OF_SCALE(SHOB_DYN_GROUP, group_var,		\
			  fset, scale)

#define DOTIMES_SHOB_DYN_GROUPS_OF_SCALE(group_var, index_var, scale, fset)	\
  FSET_DOTIMES_GROUPS_OF_SCALE(SHOB_DYN_GROUP, group_var, index_var,		\
			       fset, scale)

#define DOREVERSE_SHOB_DYN_GROUPS_OF_SCALE(group_var, index_var, scale, fset)	\
  FSET_DOREVERSE_GROUPS_OF_SCALE(SHOB_DYN_GROUP, group_var, index_var,		\
			         fset, scale)
#endif



#endif	/* _SIMENG_SHOB_DYN_H */
