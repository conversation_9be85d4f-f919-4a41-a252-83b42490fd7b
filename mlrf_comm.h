/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Communication for mlrf surfels
 *
 * Vinit Gupta, Exa Corporation
 * Created Thu Nov 30, 2017
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MLRF_COMM_H
#define _SIMENG_MLRF_COMM_H


#include "common_sp.h"
#include "timescale.h"
#include "lattice.h"
#include "comm_groups.h"
#include "strand_global.h"
#include "strands.h"
#include "mlrf.h"

class cMLRF_COMM {
  // Requests - sim threads use these to request that the comm thread post sends or receives.
  // The timestep is needed so that the comm thread can compute parity and coarsest_active_scale.

  struct sMLRF_COMM_REQUEST {
    TIMESTEP m_timestep;
    std::atomic<bool> m_post_req_flag; // Set by sim thread to request posting of send or recv by comm thread
  };

  // They must be volatile to ensure that updates are seen by the other thread
  sMLRF_COMM_REQUEST m_pre_dyn_recv_req;
  sMLRF_COMM_REQUEST m_pre_dyn_send_req;
  sMLRF_COMM_REQUEST m_post_dyn_recv_req;
  sMLRF_COMM_REQUEST m_post_dyn_send_req;

  bool m_pre_dyn_recvs_completed;
  bool m_post_dyn_recvs_completed;

public:
  VOID process_posted_pre_dyn_recvs();
  VOID process_posted_post_dyn_recvs();

  // processed by the comm thread
  VOID process_requests();

  //Called by sim thread
  VOID request_pre_dyn_sends() {
    TIMESTEP timestep;
    if(sim.is_conduction_sp)
      timestep = g_timescale.m_conduction_pde_tm.m_time;
    else
      timestep = g_timescale.m_lb_tm.m_time;
    m_pre_dyn_send_req.m_timestep = timestep;
    cassert(!m_pre_dyn_send_req.m_post_req_flag.load(std::memory_order_relaxed));
    SCALE coarsest_active_scale = compute_coarsest_active_scale(timestep, FINEST_SCALE);
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale)
      {
          group->pre_dyn_send_angle_rotated = group->m_lrf_physics_desc->angle_rotated;
      }
    }
    m_pre_dyn_send_req.m_post_req_flag.store(true, std::memory_order_release);
    LOG_MSG("RECV_DEPEND").printf("Request pre_dyn_send_req timestep %d", m_pre_dyn_recv_req.m_timestep);
  }

  VOID request_post_dyn_sends() {
    TIMESTEP timestep;
    if(sim.is_conduction_sp)
      timestep = g_timescale.m_conduction_pde_tm.m_time;
    else
      timestep = g_timescale.m_lb_tm.m_time;

    m_post_dyn_send_req.m_timestep = timestep;
    cassert(!m_post_dyn_send_req.m_post_req_flag.load(std::memory_order_relaxed));
    SCALE coarsest_active_scale = compute_coarsest_active_scale(timestep, FINEST_SCALE);
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
          group->post_dyn_send_angle_rotated = group->m_lrf_physics_desc->angle_rotated;
      }
    }
    m_post_dyn_send_req.m_post_req_flag.store(true, std::memory_order_release);
    LOG_MSG("RECV_DEPEND").printf("Request post_dyn_send_req timestep %d", m_post_dyn_recv_req.m_timestep);
  }

  // Receives are posted for the next timestep
  VOID request_pre_dyn_recvs() {
    TIMESTEP timestep;
    if(sim.is_conduction_sp)
      timestep = g_timescale.m_conduction_pde_tm.m_time;
    else
      timestep = g_timescale.m_lb_tm.m_time;
    m_pre_dyn_recv_req.m_timestep = timestep;
    cassert(!m_pre_dyn_recv_req.m_post_req_flag.load(std::memory_order_relaxed));
    m_pre_dyn_recv_req.m_post_req_flag.store(true, std::memory_order_release);
    LOG_MSG("RECV_DEPEND").printf("Request pre_dyn_recv_req timestep %d", m_pre_dyn_recv_req.m_timestep);
  }
  // Receives are posted for the next timestep
  VOID request_post_dyn_recvs() {
    TIMESTEP timestep;
    if(sim.is_conduction_sp)
      timestep = g_timescale.m_conduction_pde_tm.m_time;
    else
      timestep = g_timescale.m_lb_tm.m_time;
    m_post_dyn_recv_req.m_timestep = timestep;
    cassert(!m_post_dyn_recv_req.m_post_req_flag.load(std::memory_order_relaxed));
    m_post_dyn_recv_req.m_post_req_flag.store(true, std::memory_order_release);
    LOG_MSG("RECV_DEPEND").printf("Request post_dyn_recv_req timestep %d", m_post_dyn_recv_req.m_timestep);
  }

  VOID init() {
    m_post_dyn_recvs_completed            = true;
    m_pre_dyn_recvs_completed             = true;
    m_pre_dyn_recv_req.m_post_req_flag    = false;
    m_post_dyn_recv_req.m_post_req_flag   = false;
    m_pre_dyn_send_req.m_post_req_flag    = false;
    m_post_dyn_send_req.m_post_req_flag   = false;
  }

  VOID init_pre_dyn_recv() {
    m_pre_dyn_recv_req.m_timestep = g_timescale.start_timestep_cntr();
    m_pre_dyn_recvs_completed  = false;
  }

  VOID init_post_dyn_recv() {
    m_post_dyn_recv_req.m_timestep = g_timescale.start_timestep_cntr();
    m_post_dyn_recvs_completed  = false;
  }

  STRAND recv_comm_type_to_dependent_strand(MLRF_COMM_TYPE type) {
    STRAND strand;
    switch(type) {
      case SID_MLRF_COMM_TYPE :
        strand = SLIDING_SURF_B_STRAND;
        break;
      case MCD_MLRF_COMM_TYPE :
        strand = SLIDING_SURF_C_STRAND;
        break;
      default:
        strand = NO_STRAND;
        msg_internal_error("mlrf_recv_update_dcntr: invalid MLRF_COMM_TYPE %d", type);
        break;
    }
    return strand;
  }

  STRAND send_comm_type_to_dependent_strand(MLRF_COMM_TYPE type) {
    if (type ==  SID_MLRF_COMM_TYPE) {
      STRAND strand = SLIDING_SURF_B_STRAND;
      return strand;
    } else {
      msg_internal_error("mlrf_send_update_dcntr: invalid MLRF_COMM_TYPE %d", type);
      return NO_STRAND;
    }
  }

};

#endif
