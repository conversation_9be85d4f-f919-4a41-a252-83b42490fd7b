/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Shobs (shared objects)
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/
#include "shob_groups.h"
#include "shob.h"
#include "sim.h"

//--------------------------------------------------------------------------
// Global variables
//--------------------------------------------------------------------------
#if BUILD_GPU
sGLOBALS g;
//We can't handle initialization of arrays with the auto-generated globals yet for GPU builds. 
//This is a temporary hack
STP_CONSTANT g_initial_particle_mass_fractions[NUM_PARTICLE_MATERIAL_SPECIES] = {1.0, 0.0, 0.0};
//STP_CONSTANT g_pf_field_x[2] = {0., 0.};
//STP_CONSTANT g_pf_field_y[2] = {0., 0.};
//STP_CONSTANT g_pf_field_z[2] = {0., 0.};
//STP_CONSTANT g_pf_grav_freq[3] = {0.0, 0.0, 0.0};
#endif

//for UDS lb solver
#if BUILD_5G_LATTICE
STP_CONSTANT g_uds_initial_value[MAX_N_USER_DEFINED_SCALARS] = {1./3.,1./3.,0.0};

#if BUILD_GPU
STP_CONSTANT g_uds_volume_source_polynomial_coefs[5] = {0.0,0.0,0.0,0.0,0.0};
STP_CONSTANT g_uds_volume_source_term[MAX_N_USER_DEFINED_SCALARS] = {0.0,0.0,0.0};
STP_CONSTANT g_uds_inlet_value[MAX_N_USER_DEFINED_SCALARS] = {1.1/3.,1.1/3.,0.0};
STP_CONSTANT g_uds_flux_bc_value[MAX_N_USER_DEFINED_SCALARS] = {0.0,0.0,0.0};
STP_CONSTANT g_uds_prescribed_bc[MAX_N_USER_DEFINED_SCALARS] = {0.0,0.0,0.0};
#endif
#endif

struct sSTRUCT_WITH_A_DFLOAT {
  dFLOAT x;
};

const char* g_shob_type_names[N_SHOB_TYPES] = { "FARBLK_TYPE", "PDE_ADVECT_FARBLK_TYPE", "NEARBLK_TYPE", "GHOSTBLK_TYPE",
                                                "VRBLK_TYPE", "MIRROR_UBLK_TYPE", "DYN_SURFEL_TYPE", "GHOST_SURFEL_TYPE",
                                                "MIRROR_SURFEL_TYPE", "SAMPLING_SURFEL_TYPE", "BSURFEL_TYPE",
                                                "GHOST_SAMPLING_SURFEL_TYPE", "FILM_ONLY_GHOST_SURFEL_TYPE" };
