/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include <cuda.h>
#include <cuda_runtime.h>
#include <iostream>
#include "helper_cuda.h"
#include <cassert>
#include "common_sp.h"
#include "gpu_globals.hcu"
#include "gpu_copy_to_device.h"
#include "ublk.h"
#include "shob.h"
#include "timescale.h"
#include PHYSICS_H
#include PHYSTYPES_H

namespace GPU {

  cSHOB_DYN_AND_MEAS g_shob_dyn_and_meas;
  cudaStream_t g_comm_thread_stream;
  cudaStream_t g_stream;

  //Global definitions
  __constant__ __device__ sGLOBALS g;
  __constant__ __device__ sSIM_INFO_CONSTANTS g_constant_sim_info;
  __constant__ __device__ sSIM_INFO* g_sim_info;
                          sUBLK_TABLE g_ublk_table;
                          sSURFEL_TABLE g_surfel_table;
  __constant__ __device__ UBLK* g_ublk_array;
  __constant__ __device__ SURFEL* g_surfel_array;
  __constant__ __device__ sUBLK_GROUP g_ublk_groups[N_UBLK_GROUP_TYPES];
  __constant__ __device__ sSURFEL_GROUP g_surfel_groups[N_SURFEL_GROUP_TYPES];
  __constant__ __device__ uINT16 g_ublk_data_offset_table_64[N_UBLK_TYPES][N_UBLK_DATA_TYPES];
  __constant__ __device__ asINT32 g_surfel_data_offset_table_64[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];  
  __constant__ __device__ uINT16 g_ublk_dynamics_data_size_64[N_MAX_UBLK_PHYSICS_TYPES];  
  __constant__ __device__ sFLUID_DYN_DCACHE_PER_SCALE_DATA g_dcache_per_scale;
  __constant__ __device__ sVR_CONFIG* g_vr_config_array;
  __constant__ __device__ UBLK* g_vr_coarse_explode_mblks;
  __constant__ __device__ sFLUID_DYN_MEAS_DCACHE* g_mcache;
  __constant__ __device__ sSURFEL_DYN_MEAS_DCACHE* g_surfel_mcache;
  __constant__ __device__ sON_DEVICE_WINDOW ** g_on_device_window_array;
  __constant__ __device__ sCDI_FACE_INDEX_TO_PHYSICS_INFO * g_cdi_face_index_to_flow_physics_info;
#if BUILD_D19_LATTICE
  __constant__ __device__ sSTATE_TABLE_ENTRY lattice_vector_pde_table[N_LATTICE_VECTORS_PDE] = LATTICE_VECTOR_PDE_TABLE; 
#endif

  __constant__ __device__ sSTATE_TABLE_ENTRY state_table[N_STATES] = STATE_TABLE;
  __constant__ __device__ sNEIGHBOR_UBLK_VECTOR neighbor_ublk_vector_table[] = NEIGHBOR_UBLK_VECTOR_TABLE;
  
  __constant__ __device__ asINT8 _state_energy[N_STATES];
  __constant__ __device__ SURFEL_STATE _float_state_vel[N_STATES][3];
  __constant__ __device__ SURFEL_STATE _float_state_energy[N_STATES];
  __constant__ __device__ SURFEL_STATE _latvec_pair_energy_one_weight[N_LATTICE_VECTOR_PAIRS];
  __constant__ __device__ SURFEL_STATE _latvec_pair_energy_one_float[N_LATTICE_VECTOR_PAIRS];
  __constant__ __device__ SURFEL_STATE _latvec_pair_first_layer_weight[N_LATTICE_VECTOR_PAIRS];
  __constant__ __device__ SURFEL_STATE _latvec_pair_first_layer_float[N_LATTICE_VECTOR_PAIRS];
  __constant__ __device__ SURFEL_STATE _float_state_weight[N_STATES];
  __constant__ __device__ STP_STATE_INDEX g_state_vel_to_index[NVELS][NVELS][NVELS];
  __constant__ __device__ sTIMESCALE_INFO g_timescale;
  __constant__ Inverse_power_of_two_array <2 * STP_MAX_SCALES -1> g_dfloat_inverse_power_of_two;
  __device__ sFLUID_SEED_VAR_SPEC*    g_fluid_seed_var_specs;    // fluid region seed var specs
  __device__ sBOUNDARY_SEED_VAR_SPEC* g_boundary_seed_var_specs; // boundary face seed var specs
  __device__ typename HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE* g_device_mblk_solver_blocks;
  __constant__ __device__ asINT32 g_v2s_data_offset_table_64[as_int(V2S_DATA_TYPES::N_TYPES) + 1];
  
  REDUCTION_MODE g_use_repeatable_reduction;
  REDUCTION_MODE g_use_repeatable_meas;
  uINT32 g_max_buffer_size;
  
  sPHYSICS_DESCRIPTOR_H2D_MAP g_phys_desc_h2d_map;

#if BUILD_5G_LATTICE
  __device__ STP_PHYS_VARIABLE g_uds_initial_value[MAX_N_USER_DEFINED_SCALARS];
#endif  
  
  __host__ static void init_device_vr_configs()
  {
    sVR_CONFIG * d_ptr = nullptr;
    const std::deque<sVR_CONFIG>& h_vr_configs = get_vr_configs();
    std::vector<sVR_CONFIG> h_contiguous_buffer(h_vr_configs.begin(), h_vr_configs.end());
    g_device_vr_config_array = GPU::malloc<sVR_CONFIG>(h_vr_configs.size(),"vr configs").get();
    checkCudaErrors( cudaMemcpy(g_device_vr_config_array, h_contiguous_buffer.data(), sizeof(sVR_CONFIG)*h_vr_configs.size(), cudaMemcpyHostToDevice) );
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_vr_config_array, &g_device_vr_config_array, sizeof(void*), 0, cudaMemcpyHostToDevice) );
  }

  __host__ VOID set_global_solver_blocks_mgr(HOST_MBLK_SOLVER_BLOCKS::MANAGER_TYPE* d_mblk_solver_blocks_mgr) {
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_device_mblk_solver_blocks,
                                        &d_mblk_solver_blocks_mgr, sizeof(void*), 0, cudaMemcpyHostToDevice) );      
  }

  __host__ VOID update_timescale() {
    checkCudaErrors( cudaMemcpyToSymbolAsync(GPU::g_timescale, &::g_timescale,
					sizeof(::g_timescale), 0, cudaMemcpyHostToDevice, GPU::g_stream) );
    checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
  }

  __host__ VOID init_seeding_globals() {
    {
      sDEV_PTR d_g_fluid_seed_vars(nullptr);
      // g_fluid_seed_var_specs size is 0 if initial conditions are constant across the domain
      if (::g_fluid_seed_var_specs.size()) {
        cCOPY_TO_DEVICE::allocate(d_g_fluid_seed_vars, "fluid seed var specs",
                                  sizeof(sFLUID_SEED_VAR_SPEC) * ::g_fluid_seed_var_specs.size(),
                                  &::g_fluid_seed_var_specs[0],
                                  ::g_fluid_seed_var_specs.size());
      }

      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_fluid_seed_var_specs,
                                          &d_g_fluid_seed_vars, sizeof(void*), 0, cudaMemcpyHostToDevice) );
    }
    {
      sDEV_PTR d_g_boundary_seed_vars(nullptr);
      // g_fluid_seed_var_specs size is 0 if initial conditions are constant across the domain
      if (::g_boundary_seed_var_specs.size()) {
        cCOPY_TO_DEVICE::allocate(d_g_boundary_seed_vars, "boundary seed var specs",
                                  sizeof(sBOUNDARY_SEED_VAR_SPEC) * ::g_boundary_seed_var_specs.size(),
                                  &::g_boundary_seed_var_specs[0],
                                  ::g_boundary_seed_var_specs.size());
      }

      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_boundary_seed_var_specs,
                                          &d_g_boundary_seed_vars, sizeof(void*), 0, cudaMemcpyHostToDevice) );      
    }
  }


  __host__ static void init_cdi_face_index_to_physics_info() 
  {
    size_t n = ::g_cdi_face_index_to_flow_physics_info.size();
    if (n > 0) {
      auto d_cdi_info = GPU::malloc<sCDI_FACE_INDEX_TO_PHYSICS_INFO>(n);
      GPU::copy_h2d(d_cdi_info, ::g_cdi_face_index_to_flow_physics_info.data(), n);
      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_cdi_face_index_to_flow_physics_info,
                                          &d_cdi_info,
                                          sizeof(void*),0,cudaMemcpyHostToDevice) );
    }
  }
  
  __host__ VOID init_device_globals(const sH2D_EXPORT& h_data) {

    //Copy attributes in sGLOBALS
    const ::sGLOBALS & h_g = static_cast<const sGLOBALS&> (*h_data.m_g);
    checkCudaErrors( cudaMemcpyToSymbol(g, &h_g,
                                        sizeof(sGLOBALS), 0, cudaMemcpyHostToDevice) );

    const ::sSIM_INFO& h_sim_info = *h_data.m_sim_info;
    const ::sSIM_INFO_CONSTANTS& h_sim_info_base = static_cast<const sSIM_INFO_CONSTANTS&>(h_sim_info);

    //Setup constant struct
    checkCudaErrors( cudaMemcpyToSymbol(g_constant_sim_info, &h_sim_info_base,
                                        sizeof(sSIM_INFO_CONSTANTS), 0, cudaMemcpyHostToDevice) );



    //Setup constant pointer to gpu_sim_info
    sDEV_PTR d_sim_info(nullptr);

    cCOPY_TO_DEVICE::allocate(d_sim_info, "sim info", sizeof(GPU::sSIM_INFO), &h_sim_info);

    checkCudaErrors( cudaMemcpyToSymbol(g_sim_info, &d_sim_info,
                                        sizeof(GPU::sSIM_INFO*), 0, cudaMemcpyHostToDevice) );


    //UBLK OFFSET TABLES
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_ublk_data_offset_table_64, h_data.m_ublk_data_offset_table,
                                        sH2D_EXPORT::size_of_ublk_data_offset_table, 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_ublk_dynamics_data_size_64, h_data.m_ublk_dynamics_data_size,
                                        sH2D_EXPORT::size_of_ublk_dynamics_data_table, 0, cudaMemcpyHostToDevice) );

    //SURFEL OFFSET TABLES
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_surfel_data_offset_table_64, h_data.m_surfel_data_offset_table,
                                        sH2D_EXPORT::size_of_surfel_data_offset_table, 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_v2s_data_offset_table_64, ::g_v2s_data_offset_table_64,
                                        sizeof(::g_v2s_data_offset_table_64), 0, cudaMemcpyHostToDevice) );
    
    //GLOBAL LATTICE TABLES
    checkCudaErrors( cudaMemcpyToSymbol(GPU::_state_energy, ::_state_energy,
					sizeof(::_state_energy), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_float_state_vel, ::_float_state_vel,
					sizeof(::_float_state_vel), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_float_state_energy, ::_float_state_energy,
					sizeof(::_float_state_energy), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_latvec_pair_energy_one_weight, ::_latvec_pair_energy_one_weight,
					sizeof(::_latvec_pair_energy_one_weight), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_latvec_pair_energy_one_float, ::_latvec_pair_energy_one_float,
					sizeof(::_latvec_pair_energy_one_float), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_latvec_pair_first_layer_weight, ::_latvec_pair_first_layer_weight,
					sizeof(::_latvec_pair_first_layer_weight), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_latvec_pair_first_layer_float, ::_latvec_pair_first_layer_float,
					sizeof(::_latvec_pair_first_layer_float), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::_float_state_weight, ::_float_state_weight,
					sizeof(::_float_state_weight), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_state_vel_to_index, ::g_state_vel_to_index,
					sizeof(::g_state_vel_to_index), 0, cudaMemcpyHostToDevice) );

    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_dfloat_inverse_power_of_two, &::g_dfloat_inverse_power_of_two,
                                        sizeof(::g_dfloat_inverse_power_of_two), 0, cudaMemcpyHostToDevice));

#if BUILD_5G_LATTICE
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_uds_initial_value, ::g_uds_initial_value,
                                        sizeof(STP_PHYS_VARIABLE) * MAX_N_USER_DEFINED_SCALARS, 0, cudaMemcpyHostToDevice));
#endif					

    init_cdi_face_index_to_physics_info();

    update_timescale();
    
    init_device_vr_configs();

    init_seeding_globals();

    init_exprlang_mgr();

  }

  VOID init_device_shobs(const sH2D_EXPORT& h_data);

  void cSHOB_DYN_AND_MEAS::init_batches(size_t max_mblks_per_group, size_t max_msfls_per_group)
  {
    size_t mblk_total_bytes = sizeof(sFLUID_DYN_MEAS_DCACHE);
    size_t msfl_total_bytes = sizeof(sSURFEL_DYN_MEAS_DCACHE) + sMSFL_V2S_DATA_MEM_POOL::size_of_allocated_elems(1);
    size_t requested_mblk_size = max_mblks_per_group * mblk_total_bytes;
    size_t requested_msfl_size = max_msfls_per_group * msfl_total_bytes;
    size_t requested_total = requested_mblk_size + requested_msfl_size;
    size_t max_buf = size_t(g_max_buffer_size) * 1e6;

    if (requested_total <= max_buf || max_buf == 0) {
      m_n_mblks_per_kernel_call = max_mblks_per_group;
      m_n_msfls_per_kernel_call = max_msfls_per_group;
    } else {
      dFLOAT mblk_share = (dFLOAT(requested_mblk_size) / dFLOAT(requested_total)) * max_buf;
      dFLOAT msfl_share = (dFLOAT(requested_msfl_size) / dFLOAT(requested_total)) * max_buf;

      m_n_mblks_per_kernel_call = mblk_share / mblk_total_bytes;
      m_n_msfls_per_kernel_call = msfl_share / msfl_total_bytes;
    }

    // override if the env variables are set
    if ( const char * env = getenv("EXA_GPU_N_MBLKS") ) {
      int tmp = std::atoi(env);
      m_n_mblks_per_kernel_call = tmp <= 0 ? max_mblks_per_group : tmp;

      if (my_proc_id == 0) {
        msg_print("EXA_GPU_N_MBLKS %lu", m_n_mblks_per_kernel_call);
      }
    }

    if ( const char * env = getenv("EXA_GPU_N_MEGASURFELS") ) {
      int tmp = std::atoi(env);
      m_n_msfls_per_kernel_call = tmp <= 0 ? max_msfls_per_group : tmp;

      if (my_proc_id == 0) {
        msg_print("EXA_GPU_N_MEGASURFELS %lu", m_n_msfls_per_kernel_call);
      }
    }

    m_n_mblks_per_kernel_call = std::min(m_n_mblks_per_kernel_call, max_mblks_per_group);
    m_n_msfls_per_kernel_call = std::min(m_n_msfls_per_kernel_call, max_msfls_per_group);

    m_device_mcache_size = m_n_mblks_per_kernel_call*sizeof(sFLUID_DYN_MEAS_DCACHE);
    m_device_mcache = GPU::malloc<sFLUID_DYN_MEAS_DCACHE>(m_n_mblks_per_kernel_call,"fluid meas buffer");
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_mcache, &m_device_mcache, sizeof(void*), 0, cudaMemcpyHostToDevice) );
    GPU::zero(m_device_mcache, m_n_mblks_per_kernel_call);

    // mega surfel allocation now
    if (max_msfls_per_group == 0) {
      m_n_msfls_per_kernel_call = 0;
      m_device_surfel_mcache_size = 0;
      m_n_msfls_per_kernel_call = 0;

      m_device_surfel_mcache = nullptr;
      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_surfel_mcache, &m_device_surfel_mcache, sizeof(void*), 0, cudaMemcpyHostToDevice) );

      return;
    }

    m_device_surfel_mcache_size = m_n_msfls_per_kernel_call*sizeof(sSURFEL_DYN_MEAS_DCACHE);
    m_device_surfel_mcache = GPU::malloc<sSURFEL_DYN_MEAS_DCACHE>(m_n_msfls_per_kernel_call,"surfel meas buffer");
    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_surfel_mcache, &m_device_surfel_mcache, sizeof(void*), 0, cudaMemcpyHostToDevice) );
    GPU::zero(m_device_surfel_mcache, m_n_msfls_per_kernel_call);

    auto& v2s_pool = sMSFL_V2S_DATA_MEM_POOL::get_instance();
    v2s_pool.allocate_pool(m_n_msfls_per_kernel_call);
    v2s_pool.clear(m_n_msfls_per_kernel_call);

  }

  void init_batches(size_t max_mblks_per_group, size_t max_msfls_per_group)
  {
    GPU::g_shob_dyn_and_meas.init_batches(max_mblks_per_group, max_msfls_per_group);
  }

  size_t msfl_batch_size() {
    return GPU::g_shob_dyn_and_meas.msfl_batch_size();
  }

  void cSHOB_DYN_AND_MEAS::print_mem_usage()
  {
    double device_mcache_size = static_cast<double>(m_device_mcache_size)/1'000'000.0;
    double device_surfel_mcache_size = static_cast<double>(m_device_surfel_mcache_size)/1'000'000.0;
    double device_v2s_size = static_cast<double>(sMSFL_V2S_DATA_MEM_POOL::size_of_allocated_elems(m_n_msfls_per_kernel_call))/1'000'000.0;
    double total_size = device_mcache_size + device_surfel_mcache_size + device_v2s_size;
    msg_print("\nGPU Work Buffer Sizes\n"
              "---------------------\n"
              "Mblk batch size : %lu\n"
              "Msfl batch size : %lu\n"
              "Mblk meas buffer: %.1f MB\n"
              "Msfl meas buffer: %.1f MB\n"
              "Msfl V2S buffer : %.1f MB\n"
              "Total           : %.1f MB\n",
              m_n_mblks_per_kernel_call,
              m_n_msfls_per_kernel_call,
              device_mcache_size,
              device_surfel_mcache_size,
              device_v2s_size,
              total_size);
  }

  sMSFL* map_H2D(sMSFL* h) {
    return g_surfel_table.map_H2D(h);
  }

  sMBLK* map_H2D(sHMBLK* h) {
    return g_ublk_table.map_H2D(h);
  }

  VOID initialize_msfl_group_ranges() {
    g_surfel_table.initialize_msfl_group_ranges();
  }
} // end namespace GPU

VOID export_host_data_to_device(const sH2D_EXPORT& h_data) {

  GPU::init_device_globals(h_data);
  GPU::init_device_windows(h_data);
  GPU::init_device_shobs(h_data);
  //GPU::output_mem_usage();
  if (getenv("EXA_GPU_INIT_VERBOSE") != nullptr) {
    GPU::g_shob_dyn_and_meas.print_mem_usage();
  }

}

namespace GPU {

int my_device_id = -1;
SHOB_ID print_ublk_parent_id = (SHOB_ID) -1;
SHOB_ID print_mblk_children_id = (SHOB_ID) -1;
SHOB_ID print_sfl_parent_id = (SHOB_ID) -1;
SHOB_ID print_msfl_children_id = (SHOB_ID) -1;
std::vector<sHMBLK*> g_host_mblks;

void set_device_comm_thread()
{
  if (my_device_id == -1) {
    msg_internal_error("Invalid device id");
  }
  checkCudaErrors(cudaSetDevice(my_device_id));
}

static
void lookup_print_ublk_parent_id()
{
  const char * env = getenv("EXA_GPU_PRINT_UBLK_PARENT_ID");
  if (env) {
    print_ublk_parent_id = atoi(env);
  }
}

static
void lookup_print_mblk_children_id()
{
  const char * env = getenv("EXA_GPU_PRINT_MBLK_CHILDREN_ID");
  if (env) {
    print_mblk_children_id = atoi(env);
  }
}

static
void lookup_print_sfl_parent_id()
{
  const char * env = getenv("EXA_GPU_PRINT_SFL_PARENT_ID");
  if (env) {
    print_sfl_parent_id = atoi(env);
  }
}

static
void lookup_print_msfl_children_id()
{
  const char * env = getenv("EXA_GPU_PRINT_MSFL_CHILDREN_ID");
  if (env) {
    print_msfl_children_id = atoi(env);
  }
}

static
int lookup_device_id()
{
  const char * env = getenv("EXA_DEVICE_ID");
  if (env) {
    return atoi(env);
  }
  return -2;

}

static bool is_empty_or_whitespace(const char * str)
{
  cassert(str);
  const char * p = str;
  while (*p != '\0') {
    if (*p != ' ' || *p != '\t' || *p != '\n') {
      return false;
    }
    ++p;
  }
  return true;
}

sSP_GPU_INFO init_device_and_context()
{

  /* Process ID of the current process in the intra-node communicator */
  const auto my_node_proc_id = my_proc_id; //TODO use correct value from cp_sp_lib/shared.cc
  int device_count=0;
  cudaError_t err = cudaGetDeviceCount(&device_count);

  bool verbose_output = (getenv("EXA_GPU_INIT_VERBOSE") != nullptr);

  if (err == cudaErrorNoDevice) {
    const char * cuda_visible_devices = getenv("CUDA_VISIBLE_DEVICES");
    if (cuda_visible_devices) {
      if (is_empty_or_whitespace(cuda_visible_devices)) {
        msg_error("No Nvidia GPUs detected. Environment variable CUDA_VISIBLE_DEVICES " 
                  "is set, but is empty, which will prevent PowerFLOW from detecting "
                  "devices. Unset CUDA_VISIBLE_DEVICES or set it to a valid "
                  "list of integers.");
      }
    }
    msg_error("No Nvidia GPUs detected");
  } else if (err != cudaSuccess) {
    checkCudaErrors(err);
  }

  // If set to a positive integer, try to use that 1 device.  If set to -1,
  // look for an available GPU.  Otherwise, just used 0. This is the default if
  // EXA_DEVICE_ID is not set, and will work nicely with queuing systems that
  // understand CUDA_VISIBLE_DEVICES.
  int specified_device = lookup_device_id();
  std::vector<int> devices_to_try;
  if (specified_device >= 0) {
    msg_print_no_prefix("EXA_DEVICE_ID set, forcing use of GPU %d",specified_device);
    devices_to_try.push_back(specified_device);
  } else if (specified_device == -1) {
    msg_print_no_prefix("EXA_DEVICE_ID set to -1, looking for available GPU");
    ccDOTIMES(i,device_count) {
      if(i+my_node_proc_id < device_count) //distribute GPUs amongst SPs
        devices_to_try.push_back(i+my_node_proc_id);
    } 
  } else {
    if(my_node_proc_id < device_count) //distribute GPUs amongst SPs
      devices_to_try.push_back(my_node_proc_id);
  }
  if(devices_to_try.empty())
    devices_to_try.push_back(0);

  // Loop through all devices and try each one in turn.
  // We could refactor this to use cudaSetValidDevices() instead,
  // which moves the decision to the cuda runtime. We just have to
  // give it a list of "good" devices (for some definition of "good".
  // See serverfault.com/questions/377005/using-cuda-visible-devices-with-sge
  // msg_print_no_prefix("\nInitializing Nvidia GPU");
  cudaDeviceProp dev_prop;
  for(int d: devices_to_try) {
    checkCudaErrors( cudaGetDeviceProperties(&dev_prop,d) );

    // The JITing process doesn't check for this, so we have to do it ourselves
    if (dev_prop.major < MIN_CUDA_ARCH) {
      msg_print("GPU %d supports CUDA %d.0, which is less than minumum required %d.0", d, dev_prop.major, MIN_CUDA_ARCH);
      continue;
    }

    // cudaSetDevice is a lightweight function. It does very little error-checking,
    // in particular, it doesn't check to see if the gpu is busy
    checkCudaErrors(cudaSetDevice(d));

    // this forces creation of the cuda context (and possible JITing of the code) 
    // on the GPU. If this fails, then this device is probably busy.
    cudaError_t err = cudaFree(nullptr); 

    if (err != cudaSuccess) {
      // resets the last error to cudaSuccess so we don't accidentally catch it later on
      cudaGetLastError(); 
      
      const char * error_str = cudaGetErrorString(err);
      msg_print("GPU %d initialization failed with error '%s'.", d, error_str);
      continue;
    }

    my_device_id = d;
    break;
  }

  if (my_device_id == -1) {
    msg_error("Unable to find a suitable GPU\n");
  } 

  sSP_GPU_INFO sp_gpu_info;
  memset(&sp_gpu_info, 0, sizeof(sp_gpu_info));
  static_assert(sizeof(sp_gpu_info.name) == sizeof(dev_prop.name), "GPU name buffers must be the same size");

  memcpy(sp_gpu_info.name, dev_prop.name, sizeof(dev_prop.name));

  sp_gpu_info.sms = dev_prop.multiProcessorCount;
  sp_gpu_info.total_global_mem = dev_prop.totalGlobalMem;
  sp_gpu_info.cuda_major = dev_prop.major;
  sp_gpu_info.cuda_minor = dev_prop.minor;
  sp_gpu_info.fp32_cores = _ConvertSMVer2Cores(dev_prop.major, dev_prop.minor) * dev_prop.multiProcessorCount;

  if (verbose_output) {
    const double giga = 1.0e9;
    const double mega = 1.0e6;
    const double kilo = 1.0e3;
    float global_mem = (double) dev_prop.totalGlobalMem / giga;
    float shared_mem_per_block = (double) dev_prop.sharedMemPerBlock / kilo;
    float const_mem = (double) dev_prop.totalConstMem / kilo;
    msg_print("Device %d: %s\n"
              "SMs         : %d\n"
              "Global Mem  : %.1f GB\n"
              "Const Mem: %.1f kB\n"
              "Shared Mem/Block: %.1f kB\n"
              "Max Threads Per Block: %d\n"
              "CUDA Version: %d.%d\n",
              my_device_id,
              dev_prop.name,
              dev_prop.multiProcessorCount,
              global_mem,
              const_mem,
              shared_mem_per_block,
              dev_prop.maxThreadsPerBlock,
              dev_prop.major, dev_prop.minor);
  }

  lookup_print_ublk_parent_id();
  lookup_print_mblk_children_id();
  lookup_print_sfl_parent_id();
  lookup_print_msfl_children_id();

  // Now create the two thread specific streams
  checkCudaErrors( cudaStreamCreateWithFlags( &g_comm_thread_stream, cudaStreamNonBlocking ) );
  checkCudaErrors( cudaStreamCreateWithFlags( &g_stream, cudaStreamNonBlocking ) );

  return sp_gpu_info;

}
}
