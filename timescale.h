/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#ifndef _SIMENG_TIMESCALE_H
#define _SIMENG_TIMESCALE_H

#include "common_sp.h"
#include "lattice.h"


typedef sINT16 TIMESTEP_PARITY;
const TIMESTEP_PARITY TIMESTEP_PARITY_INVALID  = -1;
const TIMESTEP_PARITY TIMESTEP_PARITY_EVEN = 0;
const TIMESTEP_PARITY TIMESTEP_PARITY_ODD = 1;

/** The coarsest active scale at a given timestep corresponds to the
    first zero bit in the timestep integer. The scales are counted down from
    the finest scale (max_scale). For a 4 scale case, the coarsest scale is 0,
    the finest scale is numbered 3. Every scale down to and including the
    coarsest active scale is processed on this timestep.

    Timestep | Bits | Coarsest Active Scale
    -------- | ---- | ---------------------
    0        | 0000 | 3
    1        | 0001 | 2
    2        | 0010 | 3
    3        | 0011 | 1
    4        | 0100 | 3
    5        | 0101 | 2
    6        | 0110 | 3
    7        | 0111 | 0
    8        | 1000 | 3
    9        | 1001 | 2
    10       | 1010 | 3

    Computing this is pretty easy. Given a timestep and the finest scale, we drop a zero
    in the timestep index as a blocker for the number of scales, then invert the result.
    We can then find the first set bit (then subtract 1 because of how the builtin numbers the bits)
    and determine the coarsest active scale by subtracting max_scale and the first set bit.
*/
__HOST__DEVICE__ inline SCALE compute_coarsest_active_scale(TIMESTEP t, SCALE max_scale)
{
  static_assert( sizeof(TIMESTEP) * CHAR_BIT <= STP_MAX_SCALES, "More scales than bits in TIMESTEP!" );
#if DEVICE_COMPILATION_MODE
  int s = __ffs(~(t & (~(1 << max_scale))))-1;
#else
  int s = __builtin_ffs(~(t & (~(1 << max_scale))))-1;
#endif

  return max_scale - s;
}

/// @brief Keeps track of the timing for a particular solver
typedef struct sTIMESTEP_MANAGER {
  // The current time for this solver. Units of solver time, not base time. Note also that solver with smaller timestep
  // on a larger timestep SP should progress at a faster rate to keep track of its time.
  TIMESTEP m_time;
  TIMESTEP m_time_inc; //for solvers active in another SP, can be multiple than one

  /// Next value of simulation base time when m_time should be stepped.
  BASETIME m_next_base_time;

  /// This solver runs every m_n_base_steps. Units of base time.
  BASETIME m_n_base_steps;

  /// Since m_n_base_steps can vary from phase to phase, keeps track of the starting point for each phase
  TIMESTEP m_time_0;
  BASETIME m_base_time_0;

  /// Scales are processed from finest (high numbers) to coarsest (low numbers).
  /// This denotes the lowest coarsest active scale to run this timestep.
  STP_SCALE m_coarsest_active_scale;

  /// Since all scale loops step from finest down to the coarsest_active_scale,
  /// setting m_coarsest_active_scale to a really large number causes all scale loops to 0-trip 
  static const STP_SCALE m_invalid_fine_scale = STP_MAX_SCALES + 1;

#if !BUILD_GPU
  /// Stores the last time a given scale was processed
  /// Most solvers do not need to track their scale prior_base_time,
  /// so store this on the heap to prevent an explosion in memory usage.
  /// We need this information for the interaction with the radiation solver.
  std::unique_ptr<BASETIME[]> m_scale_prior_base_time;
#endif

  enum class eTRACK_SCALE_PRIOR_BASE_TIME : bool {
    NO  = false,
    YES = true
  };
  
  /** Advances this solver in time if necessary
      @param base_time The current base time
      @param finest_scale The finest scale to compute from

      It is assumed that each time advance() is called, base_time will be >= to
      the the last time advance() was called.

      If m_next_base_time == base_time (which means that this solver will be
      active this timestep), compute m_coarsest_active_scale and the next base
      time that this solver will be active. 

      Otherwise, set m_coarsest_active_scale to m_invalid_fine_scale.

      The next time advance() is called, if m_coarsest_active_scale is valid, we increment
      the solver time. This means that the solver's timestep gets incremented after using
      the m_coarsest_active_scale that was set on the previous call to advance().
  */
  VOID advance(BASETIME base_time, asINT32 finest_scale) {
    if (is_coarsest_active_scale_valid()) {
      m_time += m_time_inc;
    }

    if (m_next_base_time == base_time) {
      m_coarsest_active_scale = compute_coarsest_active_scale(m_time, finest_scale);

      m_next_base_time += m_n_base_steps;
    } else {
      m_coarsest_active_scale = m_invalid_fine_scale;
    }
  }

  VOID init(BASETIME start_base_time, asINT32 finest_scale, eTRACK_SCALE_PRIOR_BASE_TIME track_prior_scale_base_time) {
    //m_time_inc & m_n_base_steps should have been set before when initializing time and setting the tm time reference
    //To get the nextbase time when will be active, needs to round up for intermediate timesteps, accounting for
    //possible values equal or prior to m_base_time_0 where it needs to round down
    m_next_base_time = (start_base_time > m_base_time_0) 
                       ? m_base_time_0 + ((start_base_time - m_base_time_0 - 1) / m_n_base_steps + 1) * m_n_base_steps
                       : m_base_time_0 + ((start_base_time - m_base_time_0) / m_n_base_steps) * m_n_base_steps;
    BASETIME last_next_base_time = m_next_base_time - m_n_base_steps;
    if (track_prior_scale_base_time == eTRACK_SCALE_PRIOR_BASE_TIME::YES) {
#if !BUILD_GPU
      // could have been allocated by ckpt restore
      if (!m_scale_prior_base_time) {
        m_scale_prior_base_time = std::make_unique<BASETIME[]>(finest_scale+1);
        for (int scale=0; scale<finest_scale+1; scale++) {
          m_scale_prior_base_time[scale] = -1;
        }
      }
#endif
    }
    
    if (last_next_base_time < 0) {
      /* normal start from t=0 */
      m_time = - 1;
      m_coarsest_active_scale = COARSEST_SCALE;
    } else {
      /* checkpoint restore */
      //last_coarsest_active_scale should be based on last_timestep
      m_time = m_time_0 + (last_next_base_time - m_base_time_0) / m_n_base_steps * m_time_inc;
      m_coarsest_active_scale = compute_coarsest_active_scale(m_time, finest_scale);
    }
    advance(start_base_time, finest_scale);
  }

  VOID set_phase_time_reference(BASETIME base_time_0, TIMESTEP time_0, BASETIME base_time_inc, BASETIME n_base_steps_ref) {
    if (base_time_inc < n_base_steps_ref) { //supercycled solver in subcycled realm
      //input parameters provided based on the subcycled realm and need to be adjusted since the supercycled solver will
      //start after
      base_time_0 += n_base_steps_ref - base_time_inc;
      base_time_inc = n_base_steps_ref;
      time_0++;
    }
    m_base_time_0 = base_time_0;
    m_time_0 = time_0;
    //Subcycled realm solvers within supercycled realm are advanced at the supercycled base time inc, i.e. each timestep
    //in this realm corresponds to m_time_inc timesteps in the subclyced realm. Since we set the phase time reference
    //during initialization based on the phase it falls, so we directly set the values without any correction needs
    m_n_base_steps = base_time_inc;
    m_time_inc = base_time_inc / n_base_steps_ref;
  }
  
  VOID reset_phase_time_reference(BASETIME base_time_0, TIMESTEP time_0, BASETIME base_time_inc, BASETIME n_base_steps_ref) {
    //Invoked during time-stepping to re-set the phase time reference. Similar to when being set, but does not need to care for
    //supercycled solver in a subcycled realm since data provided is already in a consistent state
    m_base_time_0 = base_time_0;
    m_time_0 = time_0;
    m_n_base_steps = base_time_inc;
    m_time_inc = base_time_inc / n_base_steps_ref;
  }
  
  VOID update_n_base_steps(BASETIME base_time_inc, BASETIME new_n_base_steps) {
    // Note that we reached here for:
    //  - Accelerated solver, i.e solver whose n_base_steps have changed (note that the ratio is updated in the solver
    //    that is supercycled, for which m_time_inc should be generally one since it is only traversed once in each
    //    timestep.
    //  - Inactive unscaled solver, kept in sync with the active solver in this realm
    //
    // Starts by determining the corresponding new time inc associated with new_n_base_steps
    BASETIME new_time_inc;
    if (base_time_inc > new_n_base_steps) {
      // Accelerated solver:
      //  - the acceleration ratio is large enough to flip which solver is supercycled, so time_inc is now larger than one in the realm
      //    originally supercycled
      // Inactive unscaled solver:
      //  - advanced multiple timesteps
      new_time_inc = base_time_inc / new_n_base_steps;
      new_n_base_steps = base_time_inc;
    } else {
      // Accelerated solver
      //  - There might have an scenario when both realms are tighly coupled (flow base steps == cond base steps), and we
      //    switch which one is accelerated. The solver that was previously subcycled and had m_time_inc > 1 becomes now
      //    supercycled, so we ensure here that m_time_inc is one for this one, since won't be synced after since it is
      //    no longer subcycled.
      // Inactive unscaled solver:
      //  - after adjusting ratio, supercycled solver became subcycled, so need to update inactive solver at its own pace
      new_time_inc = 1;  
    }
    // Stores as the origin of this new phase the current time when this solver was advanced last time, and corrects
    // next_base_time based on the base steps difference to get what should have been used when advanced before
    m_base_time_0 = m_next_base_time - m_n_base_steps;
    m_next_base_time += new_n_base_steps - m_n_base_steps;
    // Store also the corresponding m_time origin to the base steps origin above, and maybe correct m_time if the solver
    // was previously advanced with the incorrect m_time_increment, which would have happened if the solver is not
    // active in this time step
    if (is_coarsest_active_scale_valid()) {
      //active in this timestep and not advanced yet, m_time is valid (will be updated with correct time inc later)
      m_time_0 = m_time;
    } else {
      //advanced in a previous time step and m_time needs to be corrected
      m_time_0 = m_time - m_time_inc;
      m_time += new_time_inc - m_time_inc;
    }
    // Updates the value of m_n_base_steps and m_time_inc now that the old values are no longer needed
    m_n_base_steps = new_n_base_steps;
    m_time_inc = new_time_inc; 
  }

  VOID update_next_base_time_for_next_phase(BASETIME next_phase_start) {
    //Needs to freeze it till the next time we expect the solver to be active after being unfrozen
    m_next_base_time = (next_phase_start == BASETIME_LAST) 
                       ? next_phase_start //reached end of simulation, no need to round
                       : round_up_time(next_phase_start, m_n_base_steps, m_base_time_0);
  }

  VOID sync_cross_realm_for_next_phase(BASETIME next_phase_start) {
    //Upates next time
    if(next_phase_start != BASETIME_LAST) {
      update_next_base_time_for_next_phase(next_phase_start);
      //Adjust m_time accordingly, noting that it should correspond to the next time the solver is active, i.e. before m_next_base_time
      m_time = m_time_0 + (m_next_base_time - m_base_time_0) / m_n_base_steps * m_time_inc;
      if (is_coarsest_active_scale_valid()) {
        m_time -= m_time_inc;//It is being advanced in this time-step later, so needs to sync with the previous time it would have been active;
      }
    } else {
      m_time = REALM_STATUS_INACTIVE;
    }
  }

  BOOLEAN same_than_timescale(BASETIME _timescale_n_base_steps) {
    return ((m_time_inc == 1) && (m_n_base_steps == _timescale_n_base_steps));
  }

  // This is called by the radiation send strand. It must be updated
  // after the radiation send strand has finished.
  void update_scale_base_time(SCALE finest_scale, BASETIME base_time) {
#if !BUILD_GPU
    DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_coarsest_active_scale) {
      m_scale_prior_base_time[scale] = base_time;
    }
#endif
  }

  BOOLEAN is_coarsest_active_scale_valid() { return m_coarsest_active_scale != m_invalid_fine_scale; }

  __HOST__DEVICE__ BOOLEAN is_timestep_odd(asINT32 scale);

  /** Due to the explicit nature of PowerFLOW, many quantities have a 'prior' and
    a 'next' version. The quantities are stored as arrays of size 2.  The
    prior timestep index tells us which index is the 'old' data. The timestep index is not global across
    scales, because not every scale is processed every timestep. The index can
    only be 0 or 1. If you have prior timestep index, you can get the next
    timestep index by xor-ing the index with 1: next_index = prior_index ^ 1;
  */
  __HOST__DEVICE__ asINT32 prior_timestep_index(asINT32 scale) { return is_timestep_odd(scale) ? 1 : 0; }

  __HOST__DEVICE__ asINT32 prior_timestep_index(asINT32 scale, TIMESTEP timestep);

#if !BUILD_GPU
  BASETIME scale_prior_base_time(SCALE scale) const
  {
    return m_scale_prior_base_time[scale];
  }
#endif

} *TIMESTEP_MANAGER;

/** Manages timestepping for the radiation solver.
    
    The radiation solver is different from the other solvers in 2 ways.
    First, radiation solves are independent of scale, so there is no
    need to keep track of "Coarsest Active Scale." Second, there are 2
    fundamental operations that need to be tracked at separate times.

    The primary operation is when to receive new radiation heat flux
    values from the RPs. When this is to occur is computed exactly the
    same way as the base time of the other solvers. The secondary operation
    is when to accumulate and send blackbody power from the SPs to the RPs.
    This occurs sometime before the receives, and is offset by a lag value.

    Because the radiation solver is independent of scale, the solver index
    for the radiation solver is the same for all surfels at a given timestep.

    I have tried to mimic the interface of the sTIMESTEP_MANAGER, even if the
    terminology doesn't quite make sense. Since the recvs are the primary operation,
    all the base time variables refer to the recvs.

    Radiation doesn't need to end up in the ACTIVE_SOLVER_MASK (because we don't
    compute radiation in the SPs), but it does need to end up in the SOLVER_INDEX_MASK.
*/
struct sRADIATION_TIMESTEP_MANAGER {

  /** The radiation solver doesn't physically have a notion of 'time'. The 
      information propagation happens at the speed-of-light, so the radiation
      solver is _always_ at steady-state, at least for our use cases. So this
      variable is more like 'number of times radiation has been started'.
  */
  TIMESTEP m_time;

  /// Next time to do a radiation send. Units of base time.
  BASETIME m_next_send_base_time; 

  /// Next time to do a radiation recv. Units of base time.
  BASETIME m_next_recv_base_time; 

  /// The floor of m_time converted to base_units. Used
  /// for the time integration.
  BASETIME m_prior_send_base_time;

  /// The floor of m_time converted to base_units. Used
  /// for the time integration.
  BASETIME m_prior_recv_base_time;

  /// Radiation send/recv interval. Units of base time.
  BASETIME m_n_base_steps;

  /// Since m_n_base_steps can vary from phase to phase, keeps track of the starting point for each phase
  BASETIME m_base_time_0;

  bool m_do_radiation_recvs;
  bool m_do_radiation_sends;

  bool m_this_sp_is_currently_frozen;

  std::unique_ptr<BASETIME[]> m_this_sp_save_scale_prior_base_time_diff;

  /** Checks if it is time to do sends & recvs. We don't
      advance m_time in here. m_time is advanced when finish_recvs() is called
      by the radiation strand. The traditional solvers don't advance m_time
      until after the current timestep. Since recvs can happen in the current
      timestep or the following timesteps, we need a consistent notion of
      m_time for both the current timestep and the following timesteps.
      Incrementing m_time after the send provides this consistency.

      @param base_time The current base time
  */
  VOID advance(BASETIME base_time);

  VOID set_phase_time_reference(BASETIME base_time_0, BASETIME n_base_steps_ref);

  void start_new_time_coupling_phase(BASETIME _n_base_steps, 
                                     BASETIME rad_start_base_time, 
                                     BASETIME phase_start_base_time, 
                                     bool this_sp_is_frozen_this_phase, 
                                     BASETIME this_sp_scale_prior_base_time[]);

  __HOST__DEVICE__ void finish_sends(BASETIME base_time) 
  {
    m_time++;
    m_prior_send_base_time = base_time;
  }

  __HOST__DEVICE__ void finish_recvs(BASETIME base_time) 
  { 
    m_prior_recv_base_time = base_time; 
  }

  __HOST__DEVICE__ BASETIME prior_send_base_time() { return m_prior_send_base_time; }

  __HOST__DEVICE__ BASETIME prior_recv_base_time() { return m_prior_recv_base_time; }
  
  __HOST__DEVICE__ bool is_timestep_odd() { return m_time & 1; };

  __HOST__DEVICE__ asINT32 prior_timestep_index() { return is_timestep_odd(); }

  __HOST__DEVICE__ void init(BASETIME start_base_time); 

  __HOST__DEVICE__ bool do_recvs() const { return m_do_radiation_recvs; }

  __HOST__DEVICE__ bool do_sends() const { return m_do_radiation_sends; }

  __HOST__DEVICE__ BASETIME next_send() const { return m_next_send_base_time; }

  __HOST__DEVICE__ BASETIME next_recv() const { return m_next_recv_base_time; }

  void write_ckpt(const sTIMESTEP_MANAGER& lb_tm, const sTIMESTEP_MANAGER& cond_tm);
  void read_ckpt(sTIMESTEP_MANAGER& lb_tm, sTIMESTEP_MANAGER& cond_tm);

};

/* It is only for Olga's LightHill switch during the simulation.
 * Now LightHill hacks temp solver and only works in passive scalar mode.
 */ 
typedef struct sACOUS_SWITCH {
  /* the followings are solver timestep params when LightHill is on */
  ACTIVE_SOLVER_MASK m_init_solver_mask;
} *ACOUS_SWITCH;

typedef struct sTIMESCALE_INFO {
  /**** Time step information ****/
  /*  Original Starting Time for this simulation */
  BASETIME		m_start_time;
  /*  Last Time in the simulation. This is currently not updated
   *  when the user changes the number of timesteps. Thus our code is
   *  instead sensitive to the exit_p flag in async_event_flags. */
  BASETIME 		m_end_time;
  
  /*  Current Timesteps */
  sTIMESTEP_MANAGER	m_lb_tm;
  sTIMESTEP_MANAGER	m_t_pde_tm;
  sTIMESTEP_MANAGER	m_ke_pde_tm;
  sTIMESTEP_MANAGER	m_uds_pde_tm;
  sTIMESTEP_MANAGER	m_conduction_pde_tm; 
  sTIMESTEP_MANAGER m_particle_tm;
#if !BUILD_GPU
  sRADIATION_TIMESTEP_MANAGER m_radiation_tm;
#endif

  // Formerly global variables in shob.cc & shob.h
  BASETIME m_n_lb_base_steps;
  BASETIME m_n_t_pde_base_steps;
  BASETIME m_n_ke_pde_base_steps;
  BASETIME m_n_uds_pde_base_steps;
  BASETIME m_n_conduction_pde_base_steps;
  BASETIME m_n_particle_base_steps;
  BASETIME m_n_radiation_base_steps;

  //Periodicity at which wsurfel cross-realm comm occurs, characterized by the realm with the largest base_steps.
  BASETIME m_n_wsurfel_comm_base_steps;

  // Formerly in the global sim structure in sim.h
  BASETIME m_base_time;     // all above timesteps defined in terms of this
  BASETIME m_base_time_inc; // inc base_time by this each time thru loop 
  cBOOLEAN m_all_solvers_have_same_timestep;

  TIMESTEP_PARITY m_base_time_parity; // Toggles each time base_time is incremented

  //The user's notion of time may be different than the base time, and in fact, TIME
  // may advance more slowly than BASE_TIME. For instance, if the T solver is subcycled
  // by a factor of 4 for stability reasons, its timestep will be 1/4 the size of the LB 
  // solver timestep, but the user's notion of time will probably be the LB solver's timestep.

  BASETIME m_time; // user's notion of time
  BASETIME m_n_base_timesteps_per_timestep;
  BASETIME m_time_inc;

  sSIM_SOLVER_STATUS solver_status;
  // COND-TODO: This is quite confusing, since it adds an additional layer or time-scales that not sure is needed.
  // Wanting to keep the base time an integer and all larger is fine, but then m_time is not really an iteration.
  // Maybe we can change m_time name within the solvers.

  // Timestep iteration counters
  TIMESTEP m_timestep;            //Current strands iteration counter
  TIMESTEP m_wsurfel_comm_cntr;   //Current cross-realm wsurfel comm iteration counter
  
  // Time can advance different in each time-step by setting different base time increments that then realm basesteps.
  // We keep track here of the ratio being used, as well as the starting point for this ratio to be able to do the
  // transformation between base_time and time correctly
  BASETIME m_base_time_0;             //base time used to determine timestep counter
  BASETIME m_wsurfel_base_time_0;     //base time used to determine wsurfel comm counter
  TIMESTEP m_timestep_ctr_0;          //timestep corresponding to m_base_time_0
  TIMESTEP m_wsurfel_comm_ctr_0;      //wsurfel comm counter corresponding to m_wsurfel_base_time_0
  dFLOAT m_therm_time_ratio;          //current cond over flow base timesteps ratio

  //Helper functions to convert between base time and a given timestep counter.
  __HOST__DEVICE__ inline TIMESTEP round_basetime_to_timestep(BASETIME base_time, BASETIME n_base_timesteps_per_timestep) {
    //Rounds down for intermediate base_times
    return (base_time>=0) ? base_time/n_base_timesteps_per_timestep : (base_time+1)/n_base_timesteps_per_timestep - 1;
  }
  __HOST__DEVICE__ inline BASETIME timestep_cntr_to_base_time(TIMESTEP timestep_cntr, BASETIME n_base_timesteps_per_timestep) {
    return (timestep_cntr + 1) * n_base_timesteps_per_timestep - 1;
  }
  //Typically, it is needed to convert to
  // - time: user's notion of time (same for all realms) characterized by m_n_base_timesteps_per_timestep
  // - strands timestep counter: characterized by m_base_time_inc (realm specific depending on the base steps of its
  //   characteristic solver)
  BASETIME basetime_to_time(BASETIME base_time) {
    return round_basetime_to_timestep(base_time, m_n_base_timesteps_per_timestep);
  }
  BASETIME time_to_basetime(BASETIME time) {
    return timestep_cntr_to_base_time(time, m_n_base_timesteps_per_timestep);
  }
  TIMESTEP basetime_to_strands_timestep_cntr(BASETIME base_time) {
    return m_timestep_ctr_0 + round_basetime_to_timestep(base_time - m_base_time_0, m_base_time_inc);
  }
  TIMESTEP basetime_to_wsurfel_comm_cntr(BASETIME base_time) {
    //Wsurfel-comm is active only when both realms are active, and its counter remains the old value till then, starting
    //at -1 if the comm is not active in the first timestep. Thus, we round down in the conversion
    return m_wsurfel_comm_ctr_0 
           + round_basetime_to_timestep(base_time - m_wsurfel_base_time_0, m_n_wsurfel_comm_base_steps);
  }
  TIMESTEP basetime_to_next_wsurfel_comm_cntr(BASETIME base_time) {
    //Occasionally, like when initializing send/recv channels, we need the counter that will be active the "next" time
    //the wsurfel comm is active, which might happen at base_time or after. Thus, we round up in the conversion
    return m_wsurfel_comm_ctr_0
           + round_basetime_to_timestep(base_time - m_wsurfel_base_time_0 - 1, m_n_wsurfel_comm_base_steps) + 1;
  }
  // BASETIME strands_timestep_cntr_to_basetime(TIMESTEP timestep_cntr) {
  //   return m_base_time_0 + timestep_cntr_to_base_time(timestep_cntr - m_timestep_ctr_0, m_base_time_inc);
  // }
  BASETIME wsurfel_comm_cntr_to_basetime(TIMESTEP wsurfel_comm_cntr) {
    return m_wsurfel_base_time_0 + (wsurfel_comm_cntr - m_wsurfel_comm_ctr_0) * m_n_wsurfel_comm_base_steps;
  }
  //During intialization several methods use the start time in user units, so is computed here rather than stored
  //(note that m_timestep_ctr_0 can be updated in simulation, so these methods should not be called after initialization)
  BASETIME start_basetime() {
    return time_to_basetime(m_start_time);
  }
  TIMESTEP start_timestep_cntr() {
    return basetime_to_strands_timestep_cntr(start_basetime());
  }
  TIMESTEP start_next_wsurfel_comm_cntr() {
    return basetime_to_next_wsurfel_comm_cntr(start_basetime());
  }

  sACOUS_SWITCH         m_acous_switch;
  BASETIME              m_acous_start_time;

  // !! asINT32 m_last_coarsest_active_scale;
  asINT32 m_coarsest_active_scales[2];

  // These are indexed by scale and timestep parity. The order of the indices reflects
  // the observation that scales change more frequently than timesteps.

  ACTIVE_SOLVER_MASK m_active_solver_masks[2][STP_MAX_SCALES];
  ACTIVE_SOLVER_MASK m_last_active_solver_masks[STP_MAX_SCALES];

  // The union (logical OR) of these 2 yields coarsest_active_scale_masks
  ACTIVE_SOLVER_MASK m_even_active_solver_masks[2][STP_MAX_SCALES];
  ACTIVE_SOLVER_MASK m_odd_active_solver_masks[2][STP_MAX_SCALES];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
  ACTIVE_SOLVER_MASK m_last_odd_active_solver_masks[STP_MAX_SCALES];
#endif
  ACTIVE_SOLVER_MASK m_last_even_active_solver_masks[STP_MAX_SCALES];

  dFLOAT   m_powertherm_time;   // powertherm physical time in seconds

  VOID set_therm_time_ratio(double ratio, double rad_ratio);
  VOID set_solver_tm_time_reference(BASETIME base_time_0, TIMESTEP flow_time_0, TIMESTEP cond_time_0);
  VOID set_solver_tm_time(SCALE finest_scale, BASETIME base_time);  // (re)initialize the solvers' time mangers at a given time
  VOID sync_solvers_tm_with_time_reference();                       // adjusts solvers time reference based on current phase options
  VOID init_solver_tm(SCALE finest_scale);                          // initialize the solvers' time mangers during the intialization
  VOID init_solver_tm_before_lighthill_on(SCALE finest_scale);      // initialize the solver's tm during the lighthill sleep

  __HOST__DEVICE__ BOOLEAN is_timestep_odd(asINT32 scale);

  BASETIME time() { return m_time; }

  TIMESTEP time_flow() { return m_lb_tm.m_time; }
  TIMESTEP time_cond() { return m_conduction_pde_tm.m_time; }
  
  TIMESTEP solver_time(cBOOLEAN is_conduction_sp) {
    if (is_conduction_sp)
      return m_conduction_pde_tm.m_time;
    else
      return m_lb_tm.m_time;
  }

  BASETIME next_time() {
    return m_time + m_time_inc;
  }

  TIMESTEP next_time_flow() {
    return m_lb_tm.m_time + m_lb_tm.m_time_inc;
  }

  TIMESTEP next_time_cond() {
    return m_conduction_pde_tm.m_time + m_conduction_pde_tm.m_time_inc;
  }

  TIMESTEP next_solver_time(cBOOLEAN is_conduction_sp) {
    if(is_conduction_sp)
      return m_conduction_pde_tm.m_time + m_conduction_pde_tm.m_time_inc;
    else
      return m_lb_tm.m_time + m_lb_tm.m_time_inc;
  }

  BASETIME next_base_time() {
    return m_base_time + m_base_time_inc;
  }

  VOID init_time()
  {
    //ensures time is aligned with m_base_time_inc
    cassert(m_start_time == 0);
    //initialized to the first phase, safe to asign zero as initial value for counters
    m_timestep_ctr_0 = 0;
    m_wsurfel_comm_ctr_0 = 0;
    //all realms start at timestep 0 also
    m_timestep = 0;
    //however, base_time, time, and cross-realm comm time depends on the realm time increment that this SP handles, set
    //before the call to init_time when we set_therm_time_ratio If the realm is supercycled starts at the first base
    //time when is active
    m_base_time = round_up_time(time_to_basetime(m_start_time) + 1, m_base_time_inc) - 1;
    m_time = basetime_to_time(m_base_time);
    //now that we have the correct starting base time, store it as time reference
    m_base_time_0 = m_base_time;
    m_wsurfel_base_time_0 = m_n_wsurfel_comm_base_steps - 1; //first cross-realm is based on the wsurfel periodicity
    m_wsurfel_comm_cntr = basetime_to_wsurfel_comm_cntr(m_base_time);
    //initialize also all solver time managers time-reference, with initial phase starting at 0
    set_solver_tm_time_reference(0, 0, 0);
    //initializes remaining variables
    m_start_time = m_time;
    set_conduction_delta_t();
  }

  VOID set_current_time(BASETIME t) {
    m_base_time = round_up_time(time_to_basetime(t) + 1, m_base_time_inc) - 1;
    m_time = basetime_to_time(m_base_time);
  }

  VOID set_current_timestep_ctrs(cBOOLEAN is_conduction_sp, BASETIME phase_time_0, TIMESTEP timestep_ctr_0, TIMESTEP wsurfel_cntr_0) {
    //Depends on the previous phases, requires initial values for the counters to be provided 
    m_base_time_0 = phase_time_0;
    m_wsurfel_base_time_0 = phase_time_0;
    m_timestep_ctr_0 = timestep_ctr_0;
    m_wsurfel_comm_ctr_0 = wsurfel_cntr_0;
    //Based on the realm base steps, reference base starts at first time the realm was active within the phase
    m_base_time_0 += (is_conduction_sp ? n_cond_base_steps() : n_flow_base_steps()) - 1;
    m_wsurfel_base_time_0 += m_n_wsurfel_comm_base_steps - 1;
    //once the reference is properly set, internal methods to convert to timestep counters work
    m_timestep = basetime_to_strands_timestep_cntr(m_base_time);
    m_wsurfel_comm_cntr = basetime_to_wsurfel_comm_cntr(m_base_time);
    //Once timestep is properly set, we can set its parity
    if (m_timestep & 0x1) {
      m_base_time_parity = 1;
    } else {
      m_base_time_parity = 0;
    }
  }

  inline BOOLEAN is_flow_supercycled(double ratio) { 
    return (m_n_conduction_pde_base_steps < m_n_lb_base_steps || (m_n_conduction_pde_base_steps == m_n_lb_base_steps && ratio > 1.0)); 
  }
  inline BOOLEAN is_conduction_supercycled(double ratio) { 
    return (m_n_conduction_pde_base_steps > m_n_lb_base_steps || (m_n_conduction_pde_base_steps == m_n_lb_base_steps && ratio < 1.0)); 
  }
  inline BOOLEAN are_flow_base_steps_scaled(double ratio) {
    return (ratio != 1.0) && is_flow_supercycled(ratio);
  }
  inline BOOLEAN are_cond_base_steps_scaled(double ratio) {
    return (ratio != 1.0) && is_conduction_supercycled(ratio);
  }

  inline BASETIME n_flow_base_steps(double ratio) {
    if (are_flow_base_steps_scaled(ratio)) {
      //rounds rather than casting to avoid floating precission issues
      return std::round((double)m_n_lb_base_steps * ratio);
    } else { //cond_over_flow ratio applied to flow
      return m_n_lb_base_steps;
    }
  }
  inline BASETIME n_cond_base_steps(double ratio) {
    if (are_cond_base_steps_scaled(ratio)) {
      //rounds rather than casting to avoid floating precission issues
      return std::round((double)m_n_conduction_pde_base_steps / ratio);
    } else { //cond_over_flow ratio applied to conduction
      return m_n_conduction_pde_base_steps;
    }
  }
  inline BOOLEAN is_flow_supercycled() { return m_n_cond_base_steps_phase < m_n_flow_base_steps_phase; }
  inline BOOLEAN is_conduction_supercycled() { return m_n_cond_base_steps_phase > m_n_flow_base_steps_phase; }
  inline BASETIME n_flow_base_steps() { return m_n_flow_base_steps_phase; }
  inline BASETIME n_cond_base_steps() { return m_n_cond_base_steps_phase; }
  inline BASETIME n_rad_base_steps() { return m_n_rad_base_steps_phase; }

  inline BASETIME lcm_time_inc() {
    BASETIME lcm_base_steps = lcm(m_n_lb_base_steps, m_n_t_pde_base_steps, m_n_ke_pde_base_steps, m_n_uds_pde_base_steps, 
                             m_n_conduction_pde_base_steps);
    return lcm_base_steps / m_n_base_timesteps_per_timestep;
  }
  
  inline VOID set_conduction_delta_t() {
    //even if advancing at different rate, the physical time traversed by each solver in each timestep remains constant
    //so the delta_t in timesteps is computed based on the unscaled base steps ratio and can be computed here
    m_cond_delta_t = (sdFLOAT)(m_n_conduction_pde_base_steps)/(sdFLOAT)m_n_lb_base_steps; 
  }
  inline sdFLOAT conduction_delta_t() { return m_cond_delta_t; }

  bool is_cond_flow_ts_equal() {
    return (m_n_cond_base_steps_phase == m_n_flow_base_steps_phase);
  }

  bool is_this_sp_realm_subcycled(bool is_this_conduction_sp, bool is_cond_and_flow_sim) {
    // If subcycled, the realm handled by this SP (flow or conduction) has smaller base steps than 
    // its counterpart (conduction or flow)
    if(is_cond_and_flow_sim) {
      if (is_this_conduction_sp) {
        return (m_base_time_inc < m_n_flow_base_steps_phase);
      } else {
        return (m_base_time_inc < m_n_cond_base_steps_phase);
      }
    } else
      return FALSE;
  }

  VOID check_if_all_solvers_have_same_timestep();

  VOID init_common_coarsest_active_scales();
  VOID compute_coarsest_active_scales(asINT32 finest_scale, TIMESTEP timestep, TIMESTEP_PARITY parity, BOOLEAN is_full_ckpt);
  // compute_coarsest_active_scales is called either:
  // - during initialization, when we need to call it with both parity values
  // - during update_timestep, after advancing the parity and the solvers time managers but not m_timestep
  // Thus, create two methods tailored with the correct inputs for each case 
  inline VOID init_coarsest_active_scales(asINT32 finest_scale, TIMESTEP_PARITY parity, BOOLEAN is_full_ckpt) {
    return compute_coarsest_active_scales(finest_scale, m_timestep, parity, is_full_ckpt);
  }
  inline VOID compute_coarsest_active_scales(asINT32 finest_scale) {
    return compute_coarsest_active_scales(finest_scale, m_timestep + 1, m_base_time_parity, FALSE);
  }  
  VOID compute_last_active_solver_masks(asINT32 finest_scale);
  ACTIVE_SOLVER_MASK update_active_solver_masks_for_freeze_mom(ACTIVE_SOLVER_MASK in_mask,
                                                               TIMESTEP_PARITY parity);

  __HOST__DEVICE__ TIMESTEP_PARITY time_parity();

  __HOST__DEVICE__ SCALE coarsest_active_scale() {
    return m_coarsest_active_scales[time_parity()];
  }

  __HOST__DEVICE__ SCALE coarsest_active_scale(TIMESTEP_PARITY t_parity) {
    return m_coarsest_active_scales[t_parity];
  }

  __HOST__DEVICE__ SCALE compute_next_coarsest_active_scale(SCALE finest_scale) {
    return compute_coarsest_active_scale(m_timestep+1, finest_scale);
  }

  //__HOST__DEVICE__ SCALE compute_next_coarsest_active_scale(SCALE finest_scale, TIMESTEP next_solver_timestep) {
  __HOST__DEVICE__ SCALE compute_next_coarsest_active_scale(SCALE finest_scale, asINT32 next_solver_timestep) {
    return compute_coarsest_active_scale(m_timestep+next_solver_timestep, finest_scale);
  }

  __HOST__DEVICE__ SCALE compute_wsurfel_comm_coarsest_active_scale(SCALE finest_scale);
  
  VOID timestep_update(asINT32 finest_scale);

  VOID set_acous_switch();
  VOID do_acous_switch();
  VOID calculate_T_lb_pde_solver_switch_interval();
  VOID switch_T_solver(T_SOLVER_TYPE &T_solver_type);
  BOOLEAN switch_recv_group_solver_type(const BOOLEAN is_surfel_recv, const TIMESTEP prev_timestep, const SCALE scale);

  VOID update_last_timestep_info_for_solver(const sTIMESTEP_MANAGER& solver_tm,
                                            ACTIVE_SOLVER_MASK active_solver_mask, 
                                            const asINT32 parity_to_update);
  VOID init_last_active_solver_masks();
  VOID update_solver_status(const bool is_this_sp_frozen, const bool is_other_solver_sp_frozen);

 private:
  // To improve perfomance, cashed some common values scaled by the therm time ratio to avoid the need to multiply or
  // divide by it everytime they are requested
  BASETIME m_n_flow_base_steps_phase;
  BASETIME m_n_cond_base_steps_phase;
  BASETIME m_n_rad_base_steps_phase;

  // Similarly, stores values that are ratios between base timesteps to avoid continuously divide them when requessted
  sdFLOAT m_cond_delta_t;
  
} *TIMESCALE_INFO;

extern sTIMESCALE_INFO g_timescale;

#if GPU_COMPILER
namespace GPU {
  extern __CONSTANT__ __DEVICE__ sTIMESCALE_INFO g_timescale;  
}
#endif

#if BUILD_GPU
namespace GPU {
  VOID update_timescale();
}
#endif

static INLINE __HOST__DEVICE__ auto&  get_timescale_ref() {
#if DEVICE_COMPILATION_MODE
  return GPU::g_timescale;
#else
  return ::g_timescale;
#endif  
}

#endif
