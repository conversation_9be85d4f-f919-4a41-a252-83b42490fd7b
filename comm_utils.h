/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _STRAND_MGR_UTILS_HEADER_
#define _STRAND_MGR_UTILS_HEADER_

#include "common_sp.h"
#include "lattice.h"
#include "comm_groups.h"
#include "radiation_comm.h"
#include "timescale.h"
#include "strand_global.h"
#include "strands.h"
#include "mlrf_comm.h"
#include "fan_comm.h"
#include "neighbor_sp.h"
#include "sync_threads.h"
#include "spsc_queue.h"

// SEND QUEUE
// The send queue is implemented as a ring buffer of pointers to sSEND_GROUPs. This is a new type
// that includes both ublk and surfel send groups. In practice, send groups are distinguished by a triplet
// <type, scale, remote SP>, but the comm thread is not aware of these distinctions; it just manages
// a queue of objects that have send methods. When it comes to life, it drains the queue by calling
// the send method of the group at the head, removing it, and continuing to the next until the queue
// is empty.
//
// Since the comm thread has no concept of a current timestep, it cannot provide a timestep index
// when calling the send method. This index was formerly used to select between timestep t and t-1 values
// for various solver variables, and now will be need to select between t and t-1 copies of states.
// The send group itself (or the send method) must provide this; it may be as simple as toggling
// on every call.
// Responsibility of determining the ACTIVE_SOLVER_MASK, also required by the send method, is yet to be
// determined, but probably also must lie with the send group.
//
// The number of send groups is m_capacity; the size of the array of pointers is m_capacity + 1.
// The extra location avoids confusion between full and empty; more precisely, since there will
// never be more than m_n_groups entries, (m_head == m_tail) always indicates an empty queue.

class cSEND_QUEUE {
 public:

  cSEND_QUEUE(asINT32 capacity) : m_queue(capacity) {}

 private:

  struct sSEND_QUEUE_ENTRY {
    sSEND_GROUP_BASE        *m_group;
    ACTIVE_SOLVER_MASK m_active_solver_mask;
  };

  cSPSC_QUEUE<sSEND_QUEUE_ENTRY> m_queue;

 public:

  // Called only by sim threads. Adds entry to tail of queue.
  void add_sp_entry(sSP_SEND_GROUP_BASE* group, ACTIVE_SOLVER_MASK active_solver_mask, bool surfel_send_group);

  void add_rp_entry(sRP_SEND_GROUP *group);

  VOID drain();                         // Called only by comm thread. Post each send in the queue
                                        // until it is empty.
};

//-----------------collective queue--------------------


class cCOLLECTIVE_QUEUE {
 private:

  sCOLLECTIVE_GROUP ** m_entries;
  asINT32 m_capacity;                  // Actual number of entries
  asINT32 m_head;                      // head index
  asINT32 m_tail;                      // tail index

 public:

  cCOLLECTIVE_QUEUE(asINT32 capacity);

  // Note that is_empty() accesses both the head and tail indices, which violates the principle that the head
  // is accessed only by the comm thread and the tail only by the sim thread. This is OK because the worst that
  // can happen in the case of a race is that the comm thread will incompletely drain the queue.

  BOOLEAN is_empty() { return (m_head == m_tail); }

  asINT32 n_entries()  {
    if (m_tail >= m_head)
      return (m_tail - m_head);
    else
      return (m_capacity + 1  - m_head + m_tail);
  }

  // Called only by sim threads. Adds entry to tail of queue.
  VOID add_entry(sCOLLECTIVE_GROUP *group);

  // Called only by comm thread. Post each send in the queue
                                        // until it is empty.
  BOOLEAN drain();

};

//=============================================================================
// RECEIVE CHANNEL

// Messages representing shob data use a common base tag but incorporate the
// type and scale.

// A receive channel is an object used for collecting information about
// received messages of a a particular type; there is a one-to-one
// correspondence between receive channels and receive types; thus in the
// current design there will be three of them, for SURF_RECV_TYPE,
// NEARBLK_RECV_TYPE, and FARBLK_RECV_TYPE.

// This implementation depends on keeping certain information per source SP.
// However for most cases/decompositions the number of source SP's is small
// compared to the total number of SP's in a simulation. Thus we introduce the
// concept of a source SP number, or SSP, which is local. There is a fixed map
// from MPI rank (i.e. global SP number) to SSP which is built at
// initialization time.

class cRECV_CHANNEL {

 public:

  RECV_TYPE m_type;                  // The receive type of this channel

  uINT32    m_n_source_sps;          // number of source SPs for this receive channel

  sSP_RECV_GROUP_BASE ***m_recv_groups; // Pointers to the receive groups of this type. Indexed by [scale][ssp].

  uINT16 **m_n_expected_ssp;         // Count of expected receives per scale per source SP. This is
                                     // an array asINT32[m_n_source_sps][sim.num_scales].The counts
                                     // are determined at initialization and unchanged thereafter.
                                     // Counts are either 0 or 1, but they are used as counts, not truth
                                     // values, so are not typed as BOOLEAN.

  asINT32 *m_n_expected_scale;       // Expected receives per scale. Sum of the above across all source SP's.

  asINT32 m_n_expected;              // Total expected receives. Sum of the above across all scales.

  STP_SCALE m_finest_non_empty_scale; // Finest scale for which m_n_expected_scale > 0

  // There is a SSP timestep associated with each source SP and scale, a scale timestep associated with each scale,
  // and an aggregate channel timestep.
  // A channel's view of time concerns only completed receives. When the expected set for a particular SP and scale
  // have been completed, that timestep is incremented. When all the SSP timesteps for a particular scale have past
  // the current scale timestep, that timestep is incremented.
  //
  // The overall channel timestep is incremented when the scale timesteps for the finest scale and for
  // coarsen(coarsest_active_scale) (if that scale exists) have passed its current value.

  TIMESTEP *m_scale_timestep;          // Per-scale timestep. Advances at the increment appropriate for that scale, when
                                       // all messages from SSP's contributing to that scale have been received..

  TIMESTEP  **m_ssp_timestep;          // Per-SSP per-scale timestep. Increments by the increment appropriate for that scale
                                       // whenever a message of the channel's type for that SSP and scale is received.

  STP_PROC *m_n_advanced_ssps;         // Number of SSP's that have advanced past m_scale_timestep for each scale.

  STP_SCALE m_n_advanced_scales;       // Number of scales for which all SSP's have advanced past the channel timestep


  uINT16 **m_count;                    // Per-SSP per-scale received message counter. Initialized to n_expected for the given
                                       // SSP and scale.

  // These are used to sequence the receive-request completion loop.

  TIMESTEP m_scale_sequence_timestep;   // Used for determining scales for next set of receive completions.
  STP_SCALE *m_scale_sequence_scales;   // For surfels, alls scales, in reverse order; for ublks,
                                        // { FINEST_SCALE, coarsen_scale(coarsest_active_scale(m_scale_sequence_timestep, FINEST_SCALE)) }
  asINT32 m_scale_sequence_nscales;     // 1 if m_scale_sequence_cca < 0 (no such scale), 2 otherwise
  asINT32 m_scale_sequence_scale_index; // 0 = FINEST, 1 = CCA (if it exists)
  STP_PROC m_scale_sequence_ssp;        // SSP for next uncompleted recv
  TIMESTEP m_next_phase_timestep;       // Use detect a timephase change when determining scales
  size_t m_next_phase_idx;              // Index of next time coupling phase for the recv channel
  
public:
  VOID init();
  VOID init_m_scale_sequence_scales();
  void allocate_arrays(RECV_TYPE rtype, asINT32 num_scales, asINT32 n_source_sps);
  void update_allocations(asINT32 n_source_sps);
  void update_source_sps(asINT32 n_neighbor_sps);
  void add_recv_group(SP_RECV_GROUP_BASE group);
  BOOLEAN process_posted_receives();
  VOID progress_scale_sequence();
  VOID post_initial_recvs();
  VOID cancel_pending_recvs();
  TIMESTEP get_scale_time_step(asINT32 scale, TIMESTEP timestep_cntr, BOOLEAN get_last_active);
  TIMESTEP get_init_scale_time_step(asINT32 scale);
  BOOLEAN update(STP_PROC ssp, SCALE scale);
  VOID update_ssp_timestep_to_next_phase(BASETIME next_phase_start);
  VOID update_scale_sequence_to_next_phase();
  BOOLEAN is_prepare_calibration_ts(STP_SCALE scale, STP_PROC ssp, SP_RECV_GROUP_BASE group );
  VOID complete_pending_recvs();
};


//=============================================================================
// SEND CHANNEL

// We only need a send channel if it's possible to have multiple base groups
// with the same send group signature (base type, scale, remote SP). This
// is currently true only of FRINGE_SURFEL groups.

// The only service the channel provides is keeping a total of the number
// of "final contributors" to a send group, and a count of those that have 
// not yet contributed. Each final contributor decrements the count; when 
// it goes to zero, the send group is added to the send queue.

// It is necessary to distinguish final contributors from contributors. Many
// shobs are ghosted on multiple SP's, and are included in multiple send groups,
// but in only one shob group. The shob group will be the one with the lowest-numbered 
// remapped destination SP (see comment in add_surfel_to_send_groups). The send group
// associated with the shob group is the one with that destination SP, and that shob
// group is a final contributor to that send group. If there is only one final
// contributor, a send channel is not necessary.


class cSEND_CHANNEL {

  SEND_TYPE m_send_type;

  // Arrays indexed by [scale][dest_sp].
  asINT32 **m_total_send_contributors;      // Total number of contributors to sSURFEL_SEND_GROUP
  asINT32 **m_send_contributors_cntr;       // Contributors remaining before send can be added to queue
  SP_SEND_GROUP **m_send_groups;               // Pointers to the send groups
 public:

  VOID allocate(SEND_TYPE stype);
  VOID contribute_to_send_queue(STP_SCALE scale, STP_PROC dest_sp, ACTIVE_SOLVER_MASK active_solver_mask);
  VOID initialize_send_channel();

};

//=============================================================================
// MEASUREMENT SEND QUEUE
class cMEAS_SEND_QUEUE {

 public:

  cMEAS_SEND_QUEUE(asINT32 capacity) : m_queue(capacity) {}

  private:

  cSPSC_QUEUE<sMEAS_WINDOW*> m_queue;

 public:

  VOID add_entry(sMEAS_WINDOW *window);
  VOID drain();                         // Called only by comm thread. Post each ready send in the queue.

};

//=============================================================================
// MEASUREMENT COMPLETION QUEUE
// The completion queue records sends of windows that have been posted but not
// completed. The comm thread will periodically attempt to complete the
// requests at the head of the queue, and will set the flag
// window->m_send_buffer_ready when it is succesful.

class cMEAS_COMPLETION_QUEUE {

 public:

  sMEAS_WINDOW *m_head;
  sMEAS_WINDOW *m_tail;

  BOOLEAN is_empty() { return (m_head == m_tail); }
  VOID process();
};

//=============================================================================
// MEASUREMENT PENDING QUEUE
// This is a queue of reduction window sends for which the child SP
// contributions are not yet available.
class cMEAS_PENDING_SEND_QUEUE {

 public:

  sMEAS_WINDOW *m_head;
  sMEAS_WINDOW *m_tail;

  VOID process();
};

//=============================================================================
// UTILITY FUNCTIONS
BOOLEAN update_receive_channel(RECV_TYPE rtype, STP_PROC ssp, SCALE scale);


template<typename T>
void pack(std::vector<unsigned char>& m, const T* o, size_t n=1)
{
  const unsigned char * copy_me = reinterpret_cast<const unsigned char*>(o);
  m.insert(m.end(), copy_me, copy_me + sizeof(T)*n);
}

template<typename T>
void unpack(std::vector<unsigned char>::iterator & it, T* o, size_t n=1)
{
  unsigned char * copy_me = reinterpret_cast<unsigned char*>(o);
  std::copy(it, it + sizeof(T)*n, copy_me);
  it += sizeof(T)*n;
}

template<typename T>
void copy(T dest[], const T src[], size_t N) {

  ccDOTIMES(i, N) {
    dest[i] = src[i];
  }
}
template<typename T>
void copy(T *dest, const T *src) {

  *dest = *src;
}

template<typename T>
void pack_and_advance(uINT8 *&dest, const T src[], size_t N=1) {

  size_t data_size = N*sizeof(T);
  memcpy((void *) dest, (void *) src, data_size);
  dest += data_size;
}

template<typename T>
void unpack_and_advance(T *dest, uINT8 *&src, size_t N=1) {

  size_t data_size = N*sizeof(T);
  memcpy((void *) dest, (void *) src, data_size);
  src += data_size;
}

template<typename T>
void accumulate(T dest[], const T source[], const int N) {

  ccDOTIMES(i, N) {
    dest[i] += source[i];
  }
}

template<typename T>
void accumulate(T &dest, const T source) {

  dest += source;
}

#endif

