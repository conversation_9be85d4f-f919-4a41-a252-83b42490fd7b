/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Main Simulation Loop
 *
 * Vinit Gupta, Exa Corporation
 * Created Tue Sep 5 2017
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SURFEL_PROCESS_CONTROL_H
#define _SIMENG_SURFEL_PROCESS_CONTROL_H

#include "common_sp.h"
#include "lattice.h"
#include "timescale.h"
#include "shob_groups.h"
#include "strand_enum.h"

struct sSURFEL_RECV_GROUP;

template<typename SFL_TYPE_TAG> struct tSURFEL_V2S_DATA;

using sSURFEL_V2S_DATA = tSURFEL_V2S_DATA<SFL_SDFLOAT_TYPE_TAG>;
struct sSAMPLING_SURFEL;

typedef sSAMPLING_SURFEL *SAMPLING_SURFEL;

class sSURFEL_PROCESS_CONTROL {
  static constexpr int N_SURFELS_IN_A_PASS = sSURFEL_V2S_DATA_MEM_POOL::N_SURFELS_PER_PASS;
  static constexpr int N_SURFEL_PAIRS_IN_A_PASS = sSURFEL_V2S_DATA_MEM_POOL::N_SURFEL_PAIRS_PER_PASS;
public:
  SCALE m_scale;
  ACTIVE_SOLVER_MASK m_active_solver_mask;
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
  ACTIVE_SOLVER_MASK m_last_active_solver_mask;
#endif
  ACTIVE_SOLVER_MASK m_even_active_solver_mask;
  ACTIVE_SOLVER_MASK m_odd_active_solver_mask;
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
  ACTIVE_SOLVER_MASK m_last_odd_active_solver_mask;
#endif
  ACTIVE_SOLVER_MASK m_last_even_active_solver_mask;

  uINT32  m_allowed_v2s_scale_diff;
  uINT32  m_allowed_s2v_scale_diff;
  SOLVER_INDEX_MASK   m_prior_solver_index_mask;
  SOLVER_PHASE_MASKS  m_solver_phase_masks;
  SOLVER_PHASE_MASKS  m_full_phase_masks;

  SOLVER_INDEX_MASK m_coarse_time_step_index_mask;
  SOLVER_INDEX_MASK m_time_step_index_mask;

  union{
    SURFEL   m_first_surfel_this_pass;
    SAMPLING_SURFEL m_first_sampling_surfel_this_pass;
    SURFEL_PAIR   m_first_surfel_pair_this_pass;
  };
  union {
    asINT32  m_n_surfels_this_pass;
    asINT32  m_n_sampling_surfels_this_pass;
    asINT32  m_n_surfel_pairs_this_pass;
  };
  union {
    asINT32  m_n_surfels_left;
    asINT32  m_n_sampling_surfels_left;
    asINT32  m_n_surfel_pairs_left;
  };

  sSURFEL_PROCESS_CONTROL() {
    m_scale = -1;
    m_active_solver_mask      = 0;
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_active_solver_mask = 0;
#endif
    m_even_active_solver_mask = 0;
    m_odd_active_solver_mask  = 0;
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_odd_active_solver_mask = 0;
#endif
    m_last_even_active_solver_mask = 0;

    m_allowed_v2s_scale_diff = 0;
    m_allowed_s2v_scale_diff = 0;

    m_prior_solver_index_mask = 0;
    m_coarse_time_step_index_mask = ~SOLVER_INDEX_MASK(0);
    m_time_step_index_mask = ~SOLVER_INDEX_MASK(0);
    m_solver_phase_masks = 0;
    m_full_phase_masks = 0;
  }

  VOID init(SCALE scale, const TIMESTEP_PARITY running_parity = g_timescale.time_parity()) {
    set_scale(scale);
    set_active_solver_mask(scale, running_parity);
    set_phase_masks_and_scale_diffs();
    set_prior_solver_index_mask(scale);
    set_time_step_index_masks(scale);
  }

  VOID set_scale(SCALE scale) {
    m_scale = scale;
  }

  SOLVER_PHASE_MASKS compute_phase_masks() {
    SOLVER_PHASE_MASKS phase_masks = 0;
    SOLVER_PHASE_MASKS inclusiveor_phase_masks = 0;
    SOLVER_PHASE_MASKS lb_phase_masks = 0;
    SOLVER_PHASE_MASKS turb_phase_masks = 0;
    SOLVER_PHASE_MASKS temp_phase_masks = 0;
    SOLVER_PHASE_MASKS uds_phase_masks = 0;
    if(g_timescale.m_all_solvers_have_same_timestep) {
      if(!is_time_step_odd()) {
	if(is_lb_active())
	  lb_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
	if(is_turb_active())
	  turb_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
	if(is_temp_active())
	  temp_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
	if(is_uds_active())
	  uds_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
      } else {
	if(is_lb_active())
	  lb_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
	if(is_turb_active())
	  turb_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
	if(is_temp_active())
	  temp_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
	if(is_uds_active())
	  uds_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
      }
    } else {
      if(is_lb_time_step_even()) {
	lb_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
      } else if(is_lb_time_step_odd()){
	lb_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
      }
      if(is_turb_time_step_even()) {
	turb_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
     } else if(is_turb_time_step_odd()) {
	turb_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
     }
      if(is_temp_time_step_even()) {
	temp_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
     } else if(is_temp_time_step_odd()) {
	temp_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
     }
      if(is_uds_time_step_even()) {
	uds_phase_masks = STP_EVEN_V2S_PHASE_MASK | STP_EVEN_S2V_PHASE_MASK;
     } else if(is_uds_time_step_odd()) {
	uds_phase_masks = STP_ODD_V2S_PHASE_MASK | STP_ODD_S2V_PHASE_MASK;
     }
    }
    inclusiveor_phase_masks = lb_phase_masks | turb_phase_masks | temp_phase_masks | uds_phase_masks;
    phase_masks = (inclusiveor_phase_masks << N_SOLVERS * STP_N_PHASES) | (lb_phase_masks << (LB_SOLVER * STP_N_PHASES))
		  | (turb_phase_masks << (TURB_SOLVER * STP_N_PHASES))
		  | (temp_phase_masks << (T_SOLVER * STP_N_PHASES))
		  | (uds_phase_masks << (UDS_SOLVER * STP_N_PHASES));
    return phase_masks;
  }

  VOID  set_active_solver_mask(const SCALE scale,
                               const TIMESTEP_PARITY running_parity) {
    SCALE coarsest_active_scale = g_timescale.coarsest_active_scale(running_parity);
    m_active_solver_mask      = g_timescale.m_active_solver_masks[running_parity][scale];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_active_solver_mask = g_timescale.m_last_active_solver_masks[scale];
#endif
    m_even_active_solver_mask = g_timescale.m_even_active_solver_masks[running_parity][scale];
    m_odd_active_solver_mask  = g_timescale.m_odd_active_solver_masks[running_parity][scale];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_odd_active_solver_mask = g_timescale.m_last_odd_active_solver_masks[scale];
#endif
    m_last_even_active_solver_mask = g_timescale.m_last_even_active_solver_masks[scale];
  }

  VOID set_phase_masks_and_scale_diffs() {
    m_allowed_v2s_scale_diff = 0;
    m_allowed_s2v_scale_diff = 0;

    if (!is_time_step_odd()) {
      m_allowed_v2s_scale_diff = 1;
      // Sliding mesh surfels can do this
      m_allowed_v2s_scale_diff = 2;
      m_allowed_s2v_scale_diff = 0;
      // Sliding mesh surfels can do this
     // m_allowed_s2v_scale_diff = 1;
      // TODO this logic has to be changed to allow for scale difference of 2
      // These interactions do not occur in the D3Q25 lgi file written by the discretizer
      /*  if (even_active_solver_masks[1] != 0) {
       allowed_v2s_scale_diff = 2;
       } else if (odd_active_solver_masks[1] != 0) {
       allowed_v2s_scale_diff = 1;
       } */
    } else {
      m_allowed_v2s_scale_diff = 0;
      // Sliding mesh surfels can do this
      //m_allowed_v2s_scale_diff = 1;
      m_allowed_s2v_scale_diff = 1;
      // Sliding mesh surfels can do this
      m_allowed_s2v_scale_diff = 2;
      // TODO this logic has to be changed to allow for scale difference of 2
      // These interactions do not occur in the D3Q25 lgi file written by the discretizer
      /*    if (even_active_solver_masks[1] != 0) {
       allowed_s2v_scale_diff = 1;
       } else if (odd_active_solver_masks[1] != 0) {
       allowed_s2v_scale_diff = 2;
       }*/
    }
    m_solver_phase_masks = compute_phase_masks();
    m_full_phase_masks = full_phase_masks();
  }

  VOID set_prior_solver_index_mask(SCALE scale){
    m_prior_solver_index_mask = compute_solver_index_mask(scale);
  }

  VOID set_time_step_index_masks(SCALE scale) {
    asINT32 coarse_lb_index = (g_timescale.m_lb_tm.is_timestep_odd(coarsen_scale(scale)) == 0) << LB_SOLVER;
    asINT32 coarse_turb_index = (g_timescale.m_ke_pde_tm.is_timestep_odd(coarsen_scale(scale)) == 0) << TURB_SOLVER;
    asINT32 coarse_temp_index = (g_timescale.m_t_pde_tm.is_timestep_odd(coarsen_scale(scale)) == 0) << T_SOLVER;
    asINT32 coarse_uds_index = (g_timescale.m_uds_pde_tm.is_timestep_odd(coarsen_scale(scale)) == 0) << UDS_SOLVER;
    m_coarse_time_step_index_mask = coarse_lb_index | coarse_turb_index | coarse_temp_index | coarse_uds_index;
    m_time_step_index_mask = m_even_active_solver_mask;

  }

  BOOLEAN is_lb_active() {
    return ((m_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_turb_active() {
    return ((m_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_temp_active() {
    return ((m_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_conduction_active() {
    return ((m_active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0);
  }

  BOOLEAN is_uds_active() {
    return ((m_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }

  BOOLEAN is_any_flow_solver_active() {
    return is_lb_active() || is_temp_active() || is_turb_active() || is_uds_active();
  }

#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
  BOOLEAN is_last_ts_lb_active() {
    return ((m_last_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_turb_active() {
    return ((m_last_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_temp_active() {
    return ((m_last_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_conduction_active() {
    return ((m_last_active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_uds_active() {
    return ((m_last_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }

  BOOLEAN is_last_ts_odd_lb_active() {
    return ((m_last_odd_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_odd_turb_active() {
    return ((m_last_odd_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_odd_temp_active() {
    return ((m_last_odd_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_odd_conduction_active() {
    return ((m_last_odd_active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_last_ts_odd_uds_active() {
    return ((m_last_odd_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }
#endif

  BOOLEAN is_time_step_odd() {
    return (m_odd_active_solver_mask != 0);
  }
  BOOLEAN is_time_step_even() {
    return (m_even_active_solver_mask != 0);
  }
  BOOLEAN is_lb_time_step_odd() {
    return ((m_odd_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_temp_time_step_odd() {
    return ((m_odd_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_turb_time_step_odd() {
    return ((m_odd_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_uds_time_step_odd() {
    return ((m_odd_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_conduction_time_step_odd() {
    return ((m_odd_active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_lb_time_step_even() {
    return ((m_even_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_temp_time_step_even() {
    return ((m_even_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_turb_time_step_even() {
    return ((m_even_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_uds_time_step_even() {
    return ((m_even_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_conduction_time_step_even() {
    return ((m_even_active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0);
  }
  asINT32 conduction_coarse_time_step_index() {
    return g_timescale.m_conduction_pde_tm.is_timestep_odd(coarsen_scale(m_scale)) == 0;
  }

  SOLVER_PHASE_MASKS full_phase_masks() {
    SOLVER_PHASE_MASKS phase_masks = 0;
    SOLVER_PHASE_MASKS inclusiveor_phase_masks = 0;
    SOLVER_PHASE_MASKS lb_phase_masks = 0;
    SOLVER_PHASE_MASKS turb_phase_masks = 0;
    SOLVER_PHASE_MASKS temp_phase_masks = 0;
    SOLVER_PHASE_MASKS uds_phase_masks = 0;
    if(is_lb_active())
      lb_phase_masks = FULL_PHASE_MASK;
    if(is_turb_active())
      turb_phase_masks = FULL_PHASE_MASK;
    if(is_temp_active())
      temp_phase_masks = FULL_PHASE_MASK;
    if(is_uds_active())
      uds_phase_masks = FULL_PHASE_MASK;

    inclusiveor_phase_masks = lb_phase_masks | turb_phase_masks | temp_phase_masks | uds_phase_masks;
    phase_masks = (inclusiveor_phase_masks << N_SOLVERS * STP_N_PHASES) | (lb_phase_masks << (LB_SOLVER * STP_N_PHASES))
  		      | (turb_phase_masks << (TURB_SOLVER * STP_N_PHASES))
		      | (temp_phase_masks << (T_SOLVER * STP_N_PHASES))
		      | (uds_phase_masks << (UDS_SOLVER * STP_N_PHASES));
    return phase_masks;
  }
  VOID process_all_surfels(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type);
  VOID process_all_wsurfels_and_contact_surfels_A(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type);
  VOID process_all_wsurfels_and_contact_surfels_B(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type);
  VOID process_all_surfels_wsurfels_and_contact_surfels_B(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type);
  VOID do_all_ghost_surfels_s2v(STRAND strand_type);

protected:
  // Ghost surfels
  #if !BUILD_GPU
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID do_all_ghost_surfels_s2v_internal(sSURFEL_RECV_GROUP *surfel_recv_group);
  #else
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON>
  VOID do_all_ghost_surfels_s2v_internal(sMSFL_FSET::GROUP group,
                                         STRAND strand_type);
  #endif

  // Dyn surfels
  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
  VOID surfels_clear_and_s2s_flow(SURFEL_GROUP surfel_group,
                             sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
                             
  VOID surfels_clear_and_s2s_conduction(SURFEL_GROUP surfel_group,
                                        sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
  
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID do_surfels_v2s_flow(SURFEL_GROUP surfel_group,
                      sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
                      
  VOID do_surfels_v2s_conduction(SURFEL_GROUP surfel_group);
  
  VOID do_surfels_dyn_flow(STP_GEOM_VARIABLE group_voxel_size,
                           STP_GEOM_VARIABLE meas_scale_factor,
                           SURFEL_GROUP surfel_group,
                      sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
                      
  VOID do_surfels_dyn_conduction(STP_GEOM_VARIABLE group_voxel_size,
                                 STP_GEOM_VARIABLE meas_scale_factor,
                                 SURFEL_GROUP surfel_group,
                                 sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID do_surfels_s2v_flow(SURFEL_GROUP surfel_group,
                           sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                           SURFEL& first_surfel_next_pass);
                           
  VOID do_surfels_s2v_conduction(SURFEL_GROUP surfel_group,
                                 SURFEL& first_surfel_next_pass);
  
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID process_all_dyn_surfels_flow(SURFEL_GROUP surfel_group,
                                    STRAND strand_type);

  VOID process_all_dyn_surfels_conduction(SURFEL_GROUP surfel_group,
                                          STRAND strand_type);
  
  // Wsurfels & contact surfels
  struct sAVERAGING_LIMITS {
    sAVERAGING_LIMITS(SURFEL_GROUP surfel_group);
    BOOLEAN m_maybe_average;
    BOOLEAN m_fill_average;
    sSURFEL* m_begin;
    sSURFEL* m_end;
  };

  VOID do_wsurfels_and_contact_surfels_sampling_and_dynA(SURFEL_GROUP surfel_group,
                                                         STP_GEOM_VARIABLE group_voxel_size,
                                                         sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                                         SURFEL& next_surfel_to_process,
                                                         sAVERAGING_LIMITS& averaging);
  VOID do_update_averaged_contact_counters(CONTACT_SURFEL_GROUP contact_surfel_group);
  
  VOID do_wsurfels_hfc_and_dynB(STP_GEOM_VARIABLE group_voxel_size,
                                STP_GEOM_VARIABLE meas_scale_factor,
                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
  
  VOID do_contact_surfels_hfc_and_dynB(STP_GEOM_VARIABLE group_voxel_size,
                                       STP_GEOM_VARIABLE meas_scale_factor,
                                       CONTACT_SURFEL_GROUP contact_surfel_group,
                                       CONTACT_SURFEL_TYPE &contact_surfel_type,
                                       sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                       SURFEL &first_surfel_next_type);
  
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID process_all_wsurfels_and_contact_surfels_A_internal(sSURFEL_BASE_FSET::GROUP group, 
                                                  STRAND strand_type);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME, BOOLEAN IS_WSURFEL>
  VOID process_all_wsurfels_and_contact_surfels_B_internal(sSURFEL_BASE_FSET::GROUP group, 
                                                  STRAND strand_type);

  // Surfel pairs
  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
  VOID do_surfel_pairs_s2s_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
  VOID do_surfel_pairs_s2s_conduction(SURFEL_PAIR_GROUP surfel_pair_group);
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID do_surfel_pairs_v2s_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool);
  template <BOOLEAN IS_SLRF>
  VOID do_surfel_pairs_v2s_conduction(SURFEL_PAIR_GROUP surfel_pair_group);
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_SLRF>
  VOID do_surfel_pairs_dyn_flow(SCALE scale,
                                dFLOAT one_over_delta_t,
                                SURFEL_PAIR_GROUP surfel_pair_group,
                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                SP_TIMER_TYPE surfel_pair_timer);
  template <BOOLEAN IS_SLRF>
  VOID do_surfel_pairs_dyn_conduction(SCALE scale, SURFEL_PAIR_GROUP surfel_pair_group, SP_TIMER_TYPE surfel_pair_timer);
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID do_surfel_pairs_s2v_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                SURFEL_PAIR& first_surfel_pair_next_pass);
  template <BOOLEAN IS_SLRF>
  VOID do_surfel_pairs_s2v_conduction(SURFEL_PAIR_GROUP surfel_pair_group,
                                      SURFEL_PAIR& first_surfel_pair_next_pass);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID process_all_surfel_pairs_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                STRAND strand_type);
  template <BOOLEAN IS_SLRF>
  VOID process_all_surfel_pairs_conduction(SURFEL_PAIR_GROUP surfel_pair_group,
                                           STRAND strand_type);


  // Sampling surfels
  VOID do_sampling_surfels_s2s(sSAMPLING_SURFEL_GROUP *surfel_group);
  VOID do_sampling_surfels_v2s(sSAMPLING_SURFEL_GROUP *surfel_group);
  VOID process_all_sampling_surfels(SAMPLING_SURFEL_GROUP surfel_group,
                                    STRAND strand_type);
  VOID do_sampling_surfels_dyn(sSAMPLING_SURFEL_GROUP *surfel_group, SAMPLING_SURFEL& first_sampling_surfel_next_pass);

#if BUILD_GPU
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
  VOID process_all_surfels_internal(SURFEL_BASE_GROUP_TYPE gtype,
				    sMSFL_FSET::GROUP group,
				    STRAND strand_type);
#else
  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON,  BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID process_all_surfels_internal(sSURFEL_BASE_FSET::GROUP group,
                                   STRAND strand_type);

  template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON,  BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
  VOID process_all_dyn_surfels(SURFEL_GROUP surfel_group,
                               STRAND strand_type);  
#endif

};

template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID surfel_clear_and_s2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                               SURFEL surfel,
                               sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask);

VOID surfel_clear_and_s2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                     SURFEL surfel,
                                     ACTIVE_SOLVER_MASK s2s_active_solver_mask);

VOID seed_conduction_v2s(SURFEL surfel);
VOID seed_conduction_s2v(SURFEL surfel);

#if BUILD_GPU

template<typename T> struct tMLRF_SURFEL_GROUP;
using sMLRF_MSFL_GROUP = tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>;

namespace GPU {

VOID set_sfl_group_range_for_scale(size_t *range, SURFEL_GROUP_TYPE group_type, int scale);
VOID set_mlrf_msfl_group_range_for_scale(size_t *range, int scale,
                                         STP_EVEN_ODD even_odd_mask, LRF_PHYSICS_DESCRIPTOR lrf);
  
template <BOOLEAN IS_LB_ACTIVE,
          BOOLEAN IS_UDS_LB_SOLVER_ON,
          BOOLEAN IS_T_S_LB_SOLVER_ON>
void process_dyn_surfels(SURFEL_GROUP_TYPE group_type,
                         sSURFEL_PROCESS_CONTROL* surfel_process_control,
                         const size_t range[2]);

template <BOOLEAN IS_LB_ACTIVE,
          BOOLEAN IS_T_LB_SOLVER_ON>
void mlrf_reset_s2s_v2s_flow(sSURFEL_PROCESS_CONTROL* surfel_process_control,
                             const size_t range[2]);

VOID do_mlrf_surfel_dynamics_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                  ACTIVE_SOLVER_MASK active_solver_mask,
                                  SOLVER_INDEX_MASK out_flux_index_mask,
                                  LRF_PHYSICS_DESCRIPTOR lrf,
                                  BOOLEAN is_seed,
                                  STP_SCALE scale,
                                  sMLRF_MSFL_GROUP* mlrf_msfl_group,
                                  const size_t range[2]);

VOID mlrf_surfel_dyn_delta_mass(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                ACTIVE_SOLVER_MASK active_solver_mask,
                                SOLVER_INDEX_MASK out_flux_index_mask,
                                LRF_PHYSICS_DESCRIPTOR lrf,
                                BOOLEAN is_seed,
                                STP_SCALE scale,
                                sMLRF_MSFL_GROUP* mlrf_msfl_group,
                                const size_t range[2]);

template <BOOLEAN IS_LB_ACTIVE,
          BOOLEAN IS_T_LB_SOLVER_ON,
          BOOLEAN IS_ALL_SOLVER_TS_SAME>
void mlrf_surfel_s2v_advect_flow(sSURFEL_PROCESS_CONTROL* surfel_process_control,
                                 const size_t range[2],
                                 STP_EVEN_ODD even_odd_mask,
                                 sLRF_PHYSICS_DESCRIPTOR* lrf);


template <BOOLEAN IS_LB_ACTIVE,
          BOOLEAN IS_UDS_LB_SOLVER_ON,
          BOOLEAN IS_T_LB_SOLVER_ON>
void ghost_surfels_s2v(sSURFEL_PROCESS_CONTROL* surfel_process_control,
                       const size_t range[2],
                       STP_PROC dest_sp);


void seed_mlrf_surfels(const size_t range[2],
                       ACTIVE_SOLVER_MASK active_solver_mask,
                       SOLVER_INDEX_MASK seed_solver_index_mask,
                       BOOLEAN is_full_checkpoint_restore);
}
#endif
#endif /* #ifndef _SIMENG_STRAND_CONTROL_H*/
