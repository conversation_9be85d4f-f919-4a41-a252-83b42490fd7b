/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "timescale.h"
#include "sim.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "atomic_ref.h"

#include CCUTILS_H

sTIMESCALE_INFO g_timescale;

__HOST__DEVICE__ TIMESTEP_PARITY sTIMESCALE_INFO::time_parity() {
  auto& simc = get_simc_ref();
  if(simc.is_conduction_sp)
    return (m_conduction_pde_tm.m_time & 0x1);
  else
    return (m_lb_tm.m_time & 0x1);
}

VOID sTIMESCALE_INFO::update_last_timestep_info_for_solver(const sTIMESTEP_MANAGER& solver_tm,
    ACTIVE_SOLVER_MASK active_solver_mask, const asINT32 parity_to_update) {
  BASETIME solver_last_active_base_time = m_base_time / solver_tm.m_n_base_steps * solver_tm.m_n_base_steps - 1;

  TIMESTEP solver_last_time;
  asINT32 solver_last_coarsest_active_scale;
  BOOLEAN is_solver_last_timestep_odd[STP_MAX_SCALES];

  if (solver_last_active_base_time < 0) {
    solver_last_time = -1;
    solver_last_coarsest_active_scale = COARSEST_SCALE;
  } else {
    solver_last_time = solver_last_active_base_time / solver_tm.m_n_base_steps;

    if (solver_last_time == solver_last_active_base_time)
      solver_last_coarsest_active_scale = compute_coarsest_active_scale(solver_last_time, FINEST_SCALE);
    else
      solver_last_coarsest_active_scale = solver_tm.m_invalid_fine_scale;
  }

  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
    is_solver_last_timestep_odd[scale] = sim_is_timestep_odd(scale, solver_last_time);
  }

  m_coarsest_active_scales[parity_to_update] = coarsest_scale_of_pair(m_coarsest_active_scales[parity_to_update],
                                                                      solver_last_coarsest_active_scale);

  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
    m_active_solver_masks[parity_to_update][scale] |= active_solver_mask;
    if (is_solver_last_timestep_odd[scale])
      m_odd_active_solver_masks[parity_to_update][scale] |= active_solver_mask;
    else
      m_even_active_solver_masks[parity_to_update][scale] |= active_solver_mask;
  }
}

VOID sTIMESCALE_INFO::check_if_all_solvers_have_same_timestep() {
  //If all solvers shared the same time step:
  // - all realms at same even/odd timescale
  m_all_solvers_have_same_timestep = (m_lb_tm.is_timestep_odd(FINEST_SCALE) == m_conduction_pde_tm.is_timestep_odd(FINEST_SCALE));
  // - there are no frozen solvers
  // - not advancing at different rate (accelerating one realm might lead to both to be updated at same rate, but
  //   physically each advances with different time increments, and is important to mark since this will trigger
  //   conduction dynamics to scale delta_time by conduction_delta_t)
  // - all should share same time increments than the reference timescale, taken here as the lb n_base_steps
  m_all_solvers_have_same_timestep &= (!sim.time_coupling_info.is_one_solver_frozen()) &&
                                      (!sim.time_coupling_info.is_advancing_at_different_rate()) &&
                                      (m_lb_tm.same_than_timescale(m_base_time_inc)) &&
                                      (!sim.is_heat_transfer    || m_t_pde_tm.same_than_timescale(m_base_time_inc)) && 
                                      (!sim.is_turb_model       || m_ke_pde_tm.same_than_timescale(m_base_time_inc)) && 
                                      (!sim.is_scalar_model     || m_uds_pde_tm.same_than_timescale(m_base_time_inc)) && 
                                      (!sim.is_conduction_model || m_conduction_pde_tm.same_than_timescale(m_base_time_inc)) && 
                                      (!sim.is_particle_model   || m_particle_tm.same_than_timescale(m_base_time_inc));
#if !BUILD_GPU
  if (sim.is_radiation_model) {
    m_all_solvers_have_same_timestep &= (m_radiation_tm.m_n_base_steps == m_base_time_inc);
  }
#endif
}

VOID sTIMESCALE_INFO::init_common_coarsest_active_scales()
{
  // First, reset all masks to 0
  memset(m_active_solver_masks,      0, 2 * STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));
  memset(m_even_active_solver_masks, 0, 2 * STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));
  memset(m_odd_active_solver_masks,  0, 2 * STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));

  ACTIVE_SOLVER_MASK active_mask = sim.init_solver_mask;

#if BUILD_5X_SOLVER
  if (sim.m_freeze_momentum_parms.m_is_on) {
    //active_mask &= (~LB_ACTIVE);
    if (sim.is_turb_model  && !sim.is_scalar_model)
      active_mask &= (~KE_PDE_ACTIVE);
  }
#endif

  // When we are restoring from a checkpoint, we need to populate the
  // masks in index which will get accessed to fill up the last_* masks
  // in subsequent call to compute_coarsest_active_scales
  asINT32 parity_to_update = time_parity() ^ 1;

  if ( !(sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) ) {

    DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
      m_active_solver_masks[0][scale] = active_mask;
      m_active_solver_masks[1][scale] = active_mask;
      m_even_active_solver_masks[0][scale] = active_mask;
      m_even_active_solver_masks[1][scale] = active_mask;
      m_odd_active_solver_masks[0][scale] = active_mask;
      m_odd_active_solver_masks[1][scale] = active_mask;
    }

  } else {

    m_coarsest_active_scales[parity_to_update] = STP_MAX_SCALES + 1; // initialize with invalid scale

    if (m_all_solvers_have_same_timestep) {
      // When there is no sub-/super-cycling, we can use any solver here. 
      update_last_timestep_info_for_solver(m_lb_tm, active_mask, parity_to_update);

      // Since for checkpoint resumes, we only update one of parities,
      // m_active_solver_masks need to be populated with correct values
      // for the other parity.
      DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
        m_active_solver_masks[parity_to_update^1][scale] = active_mask;
        m_even_active_solver_masks[parity_to_update^1][scale] = active_mask;
        m_odd_active_solver_masks[parity_to_update^1][scale] = active_mask;
      }
    } else {

      update_last_timestep_info_for_solver(m_lb_tm, LB_ACTIVE, parity_to_update);

      if (sim.is_heat_transfer) {
        update_last_timestep_info_for_solver(m_t_pde_tm, T_PDE_ACTIVE, parity_to_update);
      }

      if (sim.is_turb_model) {
        update_last_timestep_info_for_solver(m_ke_pde_tm, KE_PDE_ACTIVE, parity_to_update);
      }

      if (sim.is_scalar_model) {
        update_last_timestep_info_for_solver(m_uds_pde_tm, UDS_PDE_ACTIVE, parity_to_update);
      }

      if (sim.is_conduction_model) {
        update_last_timestep_info_for_solver(m_conduction_pde_tm, CONDUCTION_PDE_ACTIVE, parity_to_update);
      }
    }
  }

  // Update last_* masks for all scales - this is necessary since in 
  // a checkpoint resume, not all scales may have been active during
  // the previous timestep and hence those last_*_masks[scale] would
  // contain incorrect values
  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
    m_last_active_solver_masks[scale] = m_active_solver_masks[parity_to_update][scale];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_odd_active_solver_masks[scale] = m_odd_active_solver_masks[parity_to_update][scale];
#endif
  }
}

/** @brief Sets thermal (cond over flow) time ratio 

Note that the timestep ratio is the inverse of time ratio, i.e. a ratio larger than one implies that time in conduction
advances faster than in flow so we traverse less flow timesteps for a given conduction timestep. In other words, for a
given flow time span, we traverse a larger number of conduction timesteps if we are accelerating conduction. 
*/
VOID sTIMESCALE_INFO::set_therm_time_ratio(double therm_ratio, double rad_ratio) {
  //Adjusts the flow and conduction base steps based on the new ratio
  m_n_flow_base_steps_phase = n_flow_base_steps(therm_ratio);
  m_n_cond_base_steps_phase = n_cond_base_steps(therm_ratio);
  if (sim.is_radiation_model) {
    if (rad_ratio >= 1.0) {//rounds ratio to the closest integer to get the correct multiple of cond base steps
      m_n_rad_base_steps_phase = (int)(rad_ratio + 0.5f) * m_n_cond_base_steps_phase;
    } else { //uses the default value
      m_n_rad_base_steps_phase = m_n_radiation_base_steps;
    }
    //if flow is supercycled, ensures that radiation occurs at timesteps when flow is also active
    if (m_n_flow_base_steps_phase > m_n_cond_base_steps_phase) {
      TIMESTEP lcm_base_rad = std::lcm(m_n_flow_base_steps_phase, m_n_cond_base_steps_phase);
      m_n_rad_base_steps_phase = std::round((double)m_n_rad_base_steps_phase / (double)lcm_base_rad) * lcm_base_rad;
      //just in case user default value is so small that will be rounded to zero, ensure it is a valid value
      if (m_n_rad_base_steps_phase <= 0) m_n_rad_base_steps_phase = lcm_base_rad;
    }
  }
  //Adjusts timestep increments
  m_base_time_inc = (sim.is_conduction_sp) ? m_n_cond_base_steps_phase : m_n_flow_base_steps_phase;
  m_time_inc = m_base_time_inc / m_n_base_timesteps_per_timestep;
  //wsurfel cross-realm comm occurs when both flow and conduction solver active
  m_n_wsurfel_comm_base_steps = std::lcm(m_n_flow_base_steps_phase, m_n_cond_base_steps_phase);
  //store current ratio to detect changes later
  m_therm_time_ratio = therm_ratio;
}

VOID sTIMESCALE_INFO::set_solver_tm_time_reference(BASETIME phase_time_0, TIMESTEP flow_time_0, TIMESTEP cond_time_0) {
  //Set before we initialize the solver time managers, so we need to calculate the steps advanced within the phase to
  //reach m_base_time.
  TIMESTEP flow_time = flow_time_0 + (m_base_time - phase_time_0 + 1) / n_flow_base_steps() - 1;
  TIMESTEP cond_time = cond_time_0 + (m_base_time - phase_time_0 + 1) / n_cond_base_steps() - 1;

  //Note that supercyclyed solver time managers in the subcycled realm are still not active at this phase_time_0, so they will
  //be trailing by one. This is corrected within the set_phase_time_reference below, so it is left unchanged here.

  //Phases are aligned to span exact multiples of all solvers, so results of the scales using the ratio between the
  //solver and the realm should yield exact integers
  m_lb_tm.set_phase_time_reference(m_base_time, flow_time, m_base_time_inc, n_flow_base_steps());
  if (sim.is_heat_transfer) {
    float t_base_steps_scale = (float)m_n_t_pde_base_steps / (float)m_n_lb_base_steps;
    TIMESTEP t_time = t_base_steps_scale * (float)flow_time;
    BASETIME t_base_steps_ref = t_base_steps_scale * (float)n_flow_base_steps();
    m_t_pde_tm.set_phase_time_reference(m_base_time, t_time, m_base_time_inc, t_base_steps_ref);
  }
  if (sim.is_turb_model) {
    float ke_base_steps_scale = (float)m_n_ke_pde_base_steps / (float)m_n_lb_base_steps;
    TIMESTEP ke_time = ke_base_steps_scale * (float)flow_time;
    BASETIME ke_base_steps_ref = ke_base_steps_scale * (float)n_flow_base_steps();
    m_ke_pde_tm.set_phase_time_reference(m_base_time, ke_time, m_base_time_inc, ke_base_steps_ref);
  }
  if (sim.is_scalar_model) {
    float uds_base_steps_scale = (float)m_n_uds_pde_base_steps / (float)m_n_lb_base_steps;
    TIMESTEP uds_time = uds_base_steps_scale * (float)flow_time;
    BASETIME uds_base_steps_ref = uds_base_steps_scale * (float)n_flow_base_steps();
    m_uds_pde_tm.set_phase_time_reference(m_base_time, uds_time, m_base_time_inc, uds_base_steps_ref);
  }
  if (sim.is_conduction_model) {
    m_conduction_pde_tm.set_phase_time_reference(m_base_time, cond_time, m_base_time_inc, n_cond_base_steps());
  }
  
  if(sim.is_particle_model) {
    float particle_base_steps_scale = (float)m_n_particle_base_steps / (float)m_n_lb_base_steps;
    TIMESTEP particle_time = particle_base_steps_scale * (float)flow_time;
    BASETIME particle_base_steps_ref = particle_base_steps_scale * (float)n_flow_base_steps();
    m_particle_tm.set_phase_time_reference(m_base_time, particle_time, m_base_time_inc, particle_base_steps_ref);
  }

#if !BUILD_GPU
  //radiation only tracks base_time to determine when needs to send/recv info to/from the RPs
  //for every phase traverse, and sets the first send to the first time in the phase when both 
  //solvers are active
  BASETIME rad_base_time_0 = std::max(m_lb_tm.m_base_time_0, m_conduction_pde_tm.m_base_time_0);
  m_radiation_tm.set_phase_time_reference(rad_base_time_0, n_rad_base_steps());
#endif
}

VOID sTIMESCALE_INFO::set_solver_tm_time(SCALE finest_scale, BASETIME base_time) {
  
  static constexpr auto YES = sTIMESTEP_MANAGER::eTRACK_SCALE_PRIOR_BASE_TIME::YES;
  static constexpr auto NO = sTIMESTEP_MANAGER::eTRACK_SCALE_PRIOR_BASE_TIME::NO;
  
  m_lb_tm.init(base_time, finest_scale, YES);

  if (sim.is_heat_transfer)
    m_t_pde_tm.init(base_time, finest_scale, NO);
  if (sim.is_turb_model)
    m_ke_pde_tm.init(base_time, finest_scale, NO);
  if (sim.is_scalar_model)
    m_uds_pde_tm.init(base_time, finest_scale, NO);
  if (sim.is_conduction_model)
    m_conduction_pde_tm.init(base_time, finest_scale, YES);
  
  if(sim.is_particle_model)
    m_particle_tm.init(base_time, finest_scale, NO);
  
#if !BUILD_GPU
  if (sim.is_radiation_model) {
    m_radiation_tm.init(base_time);
  }
#endif
}

VOID sTIMESCALE_INFO::sync_solvers_tm_with_time_reference() {
  //When changing the therm_time_ratio, needs to update the solvers similar to when we init.
  //Note that:
  //- Phases are aligned to span exact multiples of all solvers, so results of the scales using the ratio between the
  //  solver and the realm should yield exact integers
  //- Requires m_base_time_0 and m_therm_time_ratio to be set correctly before
  //- If flow and cond have the same timesteps, either of them might have been accelerated before and needs to be synced
  //  now. Otherwise, only the solver with larger timestep can be accelerated and needs to be synced
  //- Flow-related solvers are inactive in conduction SP and viceversa, but it is essential that we keep track which
  //  solvers are active in the other realm for the cross-real comm. Thus, the solver tm m_time should be consistent in
  //  both realms However, when supercycling flow or conduction, the realm with the larger time-step needs to adjust the
  //  base steps of the inactive solvers so they march at the same rate than this SP solvers 

  //FLOW RELATED TIME-MANAGERS
  if (m_n_lb_base_steps >= m_n_conduction_pde_base_steps) { 
    //flow might have been scaled
    m_lb_tm.update_n_base_steps(m_base_time_inc, n_flow_base_steps());
    if (sim.is_heat_transfer) {
      BASETIME n_t_pde_base_steps = (float)n_flow_base_steps() * (float)m_n_t_pde_base_steps / (float)m_n_lb_base_steps;
      m_t_pde_tm.update_n_base_steps(m_base_time_inc, n_t_pde_base_steps);
    }
    if (sim.is_turb_model) {
      BASETIME n_ke_pde_base_steps = (float)n_flow_base_steps() * (float)m_n_ke_pde_base_steps / (float)m_n_lb_base_steps;
      m_ke_pde_tm.update_n_base_steps(m_base_time_inc, n_ke_pde_base_steps);
    }
    if (sim.is_scalar_model) {
      BASETIME n_uds_pde_base_steps = (float)n_flow_base_steps() * (float)m_n_uds_pde_base_steps / (float)m_n_lb_base_steps;
      m_uds_pde_tm.update_n_base_steps(m_base_time_inc, n_uds_pde_base_steps);
    }
    if (sim.is_particle_model) {
      BASETIME n_particle_base_steps = (float)n_flow_base_steps() * (float)m_n_particle_base_steps / (float)m_n_lb_base_steps;
      m_particle_tm.update_n_base_steps(m_base_time_inc, n_particle_base_steps);
    }
  } else if (sim.is_conduction_sp) {
    //inactive unscaled flow solvers in this realm need to be syncronized with the conduction time update
    m_lb_tm.update_n_base_steps(m_base_time_inc, m_n_lb_base_steps);
    if (sim.is_heat_transfer)   m_t_pde_tm.update_n_base_steps(m_base_time_inc, m_n_t_pde_base_steps);
    if (sim.is_turb_model)      m_ke_pde_tm.update_n_base_steps(m_base_time_inc, m_n_ke_pde_base_steps);
    if (sim.is_scalar_model)    m_uds_pde_tm.update_n_base_steps(m_base_time_inc, m_n_uds_pde_base_steps);
    if (sim.is_particle_model)  m_particle_tm.update_n_base_steps(m_base_time_inc, m_n_particle_base_steps);
  }
  
  //CONDUCTION RELATED TIME-MANAGERS
  if (m_n_conduction_pde_base_steps >= m_n_lb_base_steps) { 
    //conduction might have been scaled
    if (sim.is_conduction_model) m_conduction_pde_tm.update_n_base_steps(m_base_time_inc, n_cond_base_steps());
  } else if (!sim.is_conduction_sp) {
    //inactive unscaled conduction solvers in this realm need to be syncronized with the flow time update
    if (sim.is_conduction_model) m_conduction_pde_tm.update_n_base_steps(m_base_time_inc, m_n_conduction_pde_base_steps);
  }

  //RADIATION TIME-MANAGER : Updated after the call to this method passing additional info, skipped here
}

VOID sTIMESCALE_INFO::init_solver_tm(SCALE finest_scale) {
  if( !(sim.is_mme_checkpoint_restore || sim.is_full_checkpoint_restore) ) {
    BASETIME prev_base_time = m_base_time - m_base_time_inc;
    set_solver_tm_time(finest_scale, prev_base_time);
    compute_last_active_solver_masks(finest_scale);
  }

  set_solver_tm_time(finest_scale, m_base_time);
  check_if_all_solvers_have_same_timestep();
}


VOID sTIMESCALE_INFO::init_solver_tm_before_lighthill_on(SCALE finest_scale) {
  static constexpr auto YES = sTIMESTEP_MANAGER::eTRACK_SCALE_PRIOR_BASE_TIME::YES;
  static constexpr auto NO = sTIMESTEP_MANAGER::eTRACK_SCALE_PRIOR_BASE_TIME::NO;
  
  m_lb_tm.init(m_base_time, finest_scale, YES);

  if(sim.is_turb_model)
    m_ke_pde_tm.init(m_base_time, finest_scale, NO);
  
  /* if(sim.is_heat_transfer) //disabled during the acoustic switch, no need to check
     m_t_pde_tm.init(m_base_time, finest_scale, NO);
  */

  if (sim.is_scalar_model || sim.is_conduction_model || sim.is_particle_model || sim.is_radiation_model) {
    msg_internal_error("Lighthill switch (-acous_start_time) only supports passive scalar and turbulence models! \n");
  }

  //all solvers base steps set to n_base_timesteps_per_timestep in set_acous_switch()
  m_all_solvers_have_same_timestep =  TRUE;
}

#define DEBUG_CCAS 0

ACTIVE_SOLVER_MASK
sTIMESCALE_INFO::update_active_solver_masks_for_freeze_mom(ACTIVE_SOLVER_MASK in_mask,
                                                           TIMESTEP_PARITY parity) {
  ACTIVE_SOLVER_MASK active_mask = in_mask;
#if BUILD_5X_SOLVER
  if (sim.m_freeze_momentum_parms.m_is_on) {
    //active_mask &= (~LB_ACTIVE);
    if (sim.is_turb_model  && !sim.is_scalar_model)
      active_mask &= (~KE_PDE_ACTIVE);
    DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
      m_active_solver_masks[parity][scale] = active_mask;
    }
  }
#endif
  return active_mask;
}

__HOST__DEVICE__ SCALE sTIMESCALE_INFO::compute_wsurfel_comm_coarsest_active_scale(SCALE finest_scale) {
    // If the freeze span is multiple of the coarsest coupled scale periodicity, then we can use the m_wsurfel_comm_cntr.
    // Otherwise, it would need to use the timestep counter based on the wsurfel comm base steps to determine the scales
    // TIMESTEP comm_timestep = basetime_to_wsurfel_comm_cntr(m_base_time);
    SCALE coarsest_scale = compute_coarsest_active_scale(m_wsurfel_comm_cntr, finest_scale);
    //upper bounds the scale by the coarsest scale being coupled, i.e the finest_scale_of_pair
    return std::max(coarsest_scale, sim.time_coupling_info.m_coarsest_coupled_scale);
  }

// compute_coarsest_active_scales should only be called after the
// timestep managers have been advanced. In fact, it should only
// be called by update_timestep, except during init.

VOID sTIMESCALE_INFO::compute_coarsest_active_scales(asINT32 finest_scale, 
                                                     TIMESTEP timestep, 
                                                     TIMESTEP_PARITY parity, 
                                                     BOOLEAN is_full_ckpt) 
{
  if(!is_full_ckpt) {
    DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_coarsest_active_scales[parity^1]) {
      m_last_active_solver_masks[scale] = m_active_solver_masks[parity^1][scale];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
      m_last_odd_active_solver_masks[scale] = m_odd_active_solver_masks[parity^1][scale];
#endif
      m_last_even_active_solver_masks[scale] = m_even_active_solver_masks[parity^1][scale];
    }
  }

  if (m_all_solvers_have_same_timestep) {
    
    m_coarsest_active_scales[parity] = compute_coarsest_active_scale(timestep, finest_scale);
    ACTIVE_SOLVER_MASK active_mask = sim.init_solver_mask;

    active_mask = update_active_solver_masks_for_freeze_mom(sim.init_solver_mask, parity);

    DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
      m_active_solver_masks[parity][scale] = active_mask;
      if (sim_is_timestep_odd(scale, timestep)){
        m_odd_active_solver_masks[parity][scale] = active_mask;
        m_even_active_solver_masks[parity][scale] = 0;
     } else {
        m_even_active_solver_masks[parity][scale] = active_mask;
        m_odd_active_solver_masks[parity][scale] = 0;
      }
    }
  } else {

    // For the other realm not being handled by this SP, only matters up to the coarsest scale being coupled
    // (even/odd timesteps might not aligned between realms for coarser scales after a frozen time coupling phase,
    // which would lead to wrong even/odd masks for the current realm handled by the SP)
    asINT32 num_scales = finest_scale + 1;
    STP_SCALE flow_coarsest_scale, conduction_coarsest_scale;
    if (sim.is_conduction_sp) {
      m_coarsest_active_scales[parity] = m_conduction_pde_tm.m_coarsest_active_scale;
      conduction_coarsest_scale = m_conduction_pde_tm.m_coarsest_active_scale;
      flow_coarsest_scale = std::max(m_lb_tm.m_coarsest_active_scale, 
                                     sim.time_coupling_info.m_coarsest_coupled_scale);
    } else {
      m_coarsest_active_scales[parity] = m_lb_tm.m_coarsest_active_scale;
      flow_coarsest_scale = m_lb_tm.m_coarsest_active_scale;
      conduction_coarsest_scale = std::max(m_conduction_pde_tm.m_coarsest_active_scale, 
                                           sim.time_coupling_info.m_coarsest_coupled_scale);
    }
    memset(m_active_solver_masks,      0, 2* STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));
    memset(m_even_active_solver_masks, 0, 2* STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));
    memset(m_odd_active_solver_masks,  0, 2* STP_MAX_SCALES * sizeof(ACTIVE_SOLVER_MASK));

    //Both flow and conduction SPs need to keep track of the other realm solver status to determine if the wsurfel comm
    //is active. Since it is enforced an odd timestep ratio, even/odd should be aligned between realms.
    DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, flow_coarsest_scale) {
      m_active_solver_masks[parity][scale] |= LB_ACTIVE;
      if (m_lb_tm.is_timestep_odd(scale))
        m_odd_active_solver_masks[parity][scale] |= LB_ACTIVE;
      else
        m_even_active_solver_masks[parity][scale] |= LB_ACTIVE;
    }
    if (sim.is_conduction_model) {
      DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, conduction_coarsest_scale) {
        m_active_solver_masks[parity][scale] |= CONDUCTION_PDE_ACTIVE;
        if (m_conduction_pde_tm.is_timestep_odd(scale))
          m_odd_active_solver_masks[parity][scale] |= CONDUCTION_PDE_ACTIVE;
        else
          m_even_active_solver_masks[parity][scale] |= CONDUCTION_PDE_ACTIVE;
      }
    }

    //Updates the rest of active solvers masks that depend on the realm handled by this SP
    if (!sim.is_conduction_sp) {
      if (sim.is_heat_transfer) {
        m_coarsest_active_scales[parity] = coarsest_scale_of_pair(m_coarsest_active_scales[parity],
                                                                  m_t_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_t_pde_tm.m_coarsest_active_scale) {
          m_active_solver_masks[parity][scale] |= T_PDE_ACTIVE;
          if (m_t_pde_tm.is_timestep_odd(scale))
            m_odd_active_solver_masks[parity][scale] |= T_PDE_ACTIVE;
          else
            m_even_active_solver_masks[parity][scale] |= T_PDE_ACTIVE;
        }
      }
      if (sim.is_turb_model) {
        m_coarsest_active_scales[parity] = coarsest_scale_of_pair(m_coarsest_active_scales[parity],
                                                                  m_ke_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_ke_pde_tm.m_coarsest_active_scale) {
          m_active_solver_masks[parity][scale] |= KE_PDE_ACTIVE;
          if (m_ke_pde_tm.is_timestep_odd(scale))
            m_odd_active_solver_masks[parity][scale] |= KE_PDE_ACTIVE;
          else
            m_even_active_solver_masks[parity][scale] |= KE_PDE_ACTIVE;
        }
      }
      if (sim.is_scalar_model) {
        m_coarsest_active_scales[parity] = coarsest_scale_of_pair(m_coarsest_active_scales[parity],
                                                                  m_uds_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_uds_pde_tm.m_coarsest_active_scale) {
          m_active_solver_masks[parity][scale] |= UDS_PDE_ACTIVE;
          if (m_uds_pde_tm.is_timestep_odd(scale))
            m_odd_active_solver_masks[parity][scale] |= UDS_PDE_ACTIVE;
          else
            m_even_active_solver_masks[parity][scale] |= UDS_PDE_ACTIVE;
        }
      }
      if (sim.is_particle_model) {
        m_coarsest_active_scales[parity] = coarsest_scale_of_pair(m_coarsest_active_scales[parity],
                                                                  m_particle_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_particle_tm.m_coarsest_active_scale) {
          m_active_solver_masks[parity][scale] |= PARTICLE_ACTIVE;
          if (m_particle_tm.is_timestep_odd(scale))
            m_odd_active_solver_masks[parity][scale] |= PARTICLE_ACTIVE;
          else
            m_even_active_solver_masks[parity][scale] |= PARTICLE_ACTIVE;
        }
      }
    }
  }
  if (m_coarsest_active_scales[parity] == (STP_MAX_SCALES + 1)) {
    m_coarsest_active_scales[parity] = compute_coarsest_active_scale(timestep, FINEST_SCALE);
  }
}

VOID sTIMESCALE_INFO::compute_last_active_solver_masks(asINT32 finest_scale)
{
  ACTIVE_SOLVER_MASK active_solver_masks[STP_MAX_SCALES] = { 0 };
  ACTIVE_SOLVER_MASK odd_active_solver_masks[STP_MAX_SCALES] = { 0 };
  ACTIVE_SOLVER_MASK even_active_solver_masks[STP_MAX_SCALES] = { 0 };
  asINT32 coarsest_active_scales = 0;
  TIMESTEP timestep = m_timestep - 1;

  if (m_all_solvers_have_same_timestep) {

    coarsest_active_scales = compute_coarsest_active_scale(timestep, finest_scale);
    ACTIVE_SOLVER_MASK active_mask = sim.init_solver_mask;

    DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
      active_solver_masks[scale] = active_mask;
      if (sim_is_timestep_odd(scale, timestep)){
        odd_active_solver_masks[scale] = active_mask;
        even_active_solver_masks[scale] = 0;
     } else {
        even_active_solver_masks[scale] = active_mask;
        odd_active_solver_masks[scale] = 0;
      }
    }
  } else {

    asINT32 num_scales = finest_scale + 1;
    STP_SCALE flow_coarsest_scale, conduction_coarsest_scale;
    if (sim.is_conduction_sp) {
      coarsest_active_scales = m_conduction_pde_tm.m_coarsest_active_scale;
      conduction_coarsest_scale = m_conduction_pde_tm.m_coarsest_active_scale;
      flow_coarsest_scale = std::max(m_lb_tm.m_coarsest_active_scale, 
                                     sim.time_coupling_info.m_coarsest_coupled_scale);
    } else {
      coarsest_active_scales = m_lb_tm.m_coarsest_active_scale;
      flow_coarsest_scale = m_lb_tm.m_coarsest_active_scale;
      conduction_coarsest_scale = std::max(m_conduction_pde_tm.m_coarsest_active_scale, 
                                           sim.time_coupling_info.m_coarsest_coupled_scale);
    }

    DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, flow_coarsest_scale) {
      active_solver_masks[scale] |= LB_ACTIVE;
      if (m_lb_tm.is_timestep_odd(scale))
        odd_active_solver_masks[scale] |= LB_ACTIVE;
      else
        even_active_solver_masks[scale] |= LB_ACTIVE;
    }
    if (sim.is_conduction_model) {
      DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, conduction_coarsest_scale) {
        active_solver_masks[scale] |= CONDUCTION_PDE_ACTIVE;
        if (m_conduction_pde_tm.is_timestep_odd(scale))
          odd_active_solver_masks[scale] |= CONDUCTION_PDE_ACTIVE;
        else
          even_active_solver_masks[scale] |= CONDUCTION_PDE_ACTIVE;        
      }
    }

    if(!sim.is_conduction_sp) {
      if (sim.is_heat_transfer) {
        coarsest_active_scales = coarsest_scale_of_pair(coarsest_active_scales,
                                                        m_t_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_t_pde_tm.m_coarsest_active_scale) {
          active_solver_masks[scale] |= T_PDE_ACTIVE;
          if (m_t_pde_tm.is_timestep_odd(scale))
            odd_active_solver_masks[scale] |= T_PDE_ACTIVE;
          else
            even_active_solver_masks[scale] |= T_PDE_ACTIVE;
        }
      }
      if (sim.is_turb_model) {
        coarsest_active_scales = coarsest_scale_of_pair(coarsest_active_scales,
                                                        m_ke_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_ke_pde_tm.m_coarsest_active_scale) {
          active_solver_masks[scale] |= KE_PDE_ACTIVE;
          if (m_ke_pde_tm.is_timestep_odd(scale))
            odd_active_solver_masks[scale] |= KE_PDE_ACTIVE;
          else
            even_active_solver_masks[scale] |= KE_PDE_ACTIVE;
        }
      }
      if (sim.is_scalar_model) {
        coarsest_active_scales = coarsest_scale_of_pair(coarsest_active_scales,
                                                        m_uds_pde_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_uds_pde_tm.m_coarsest_active_scale) {
          active_solver_masks[scale] |= UDS_PDE_ACTIVE;
          if (m_uds_pde_tm.is_timestep_odd(scale))
            odd_active_solver_masks[scale] |= UDS_PDE_ACTIVE;
          else
            even_active_solver_masks[scale] |= UDS_PDE_ACTIVE;
        }
      }
      if (sim.is_particle_model) {
        coarsest_active_scales = coarsest_scale_of_pair(coarsest_active_scales,
                                                        m_particle_tm.m_coarsest_active_scale);
        DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, m_particle_tm.m_coarsest_active_scale) {
          active_solver_masks[scale] |= PARTICLE_ACTIVE;
          if (m_particle_tm.is_timestep_odd(scale))
            odd_active_solver_masks[scale] |= PARTICLE_ACTIVE;
          else
            even_active_solver_masks[scale] |= PARTICLE_ACTIVE;
        }
      }
    }
  }
  DO_SCALE_FROM_FINE_TO_COARSE(scale, finest_scale, coarsest_active_scales) {
    m_last_active_solver_masks[scale] = active_solver_masks[scale];
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_odd_active_solver_masks[scale] = odd_active_solver_masks[scale];
#endif
    m_last_even_active_solver_masks[scale] = even_active_solver_masks[scale];
  }
}

VOID sTIMESCALE_INFO::set_acous_switch() 
{
  //Till reaching acoustic start time, heat transfer periodicity set to match the flow solver.
  //
  //Solvers time managers have not been initialized yet, so only thing needed at this point is to set the phase time
  //reference based on the flow state. This also implies that time is not set in the solver managers and we need to
  //compute it, though the acoustic switch happens during the first phase, so we know that phase_time_0 and flow_time_0
  //are zero
  TIMESTEP flow_time = (m_base_time + 1) / n_flow_base_steps() - 1;
  m_t_pde_tm.set_phase_time_reference(m_base_time, flow_time, m_base_time_inc, n_flow_base_steps());
}

VOID sTIMESCALE_INFO::do_acous_switch()
{
  if(m_time == m_acous_start_time) {
    //time to undo the adjustments done when we set_acous_switch during initialization
    sim.switch_acous_during_simulation = FALSE;
    
    //heat transfer solver advanced (if active) with same periodicity than flow till now, needs to restore the heat
    //transfer defined periodicity
    float t_base_steps_scale = (float)m_n_t_pde_base_steps / (float)m_n_lb_base_steps;
    TIMESTEP t_time = (sim.is_heat_transfer) ? time_flow() : 0;
    BASETIME t_base_steps_ref = t_base_steps_scale * (float)n_flow_base_steps();
    m_t_pde_tm.reset_phase_time_reference(m_base_time, t_time, m_base_time_inc, t_base_steps_ref);
    
    //Enables heat transfer
    sim.is_heat_transfer = TRUE;
    sim.init_solver_mask = m_acous_switch.m_init_solver_mask;
    
    //Call now the regular initialization of all solver time managers
    init_solver_tm(FINEST_SCALE);
    
    if (g_timescale.m_all_solvers_have_same_timestep) init_common_coarsest_active_scales();
    compute_coarsest_active_scales(FINEST_SCALE);

    if(my_proc_id == 0) {
      msg_print_no_prefix ("Lighthill is successfully turned on at timestep %ld.\n",m_time);
    }
  }
}

// #define DEBUG_TIMESTEP_UPDATE

VOID sTIMESCALE_INFO::timestep_update(asINT32 finest_scale)
{
#ifdef DEBUG_TIMESTEP_UPDATE
  msg_print("Before timestep_update; m_base_time %d", m_base_time);
  msg_print("m_time %d, m_base_time_parity = %d",
            m_time, m_base_time_parity);
  msg_print("m_coarsest_active_scales {%d, %d}",
            m_coarsest_active_scales[0],  m_coarsest_active_scales[1]);
  for(SCALE scale = FINEST_SCALE; scale >= m_coarsest_active_scales[0]; scale --) {
    msg_print("Scale %d regular odd even active_solver_masks[0] {%d, %d, %d}", scale,
        m_active_solver_masks[0][scale], m_odd_active_solver_masks[0][scale],
        m_even_active_solver_masks[0][scale]);
  }
  for(SCALE scale = FINEST_SCALE; scale >= m_coarsest_active_scales[1]; scale --) {
    msg_print("Scale %d regular odd even active_solver_masks[1] {%d, %d, %d}", scale,
        m_active_solver_masks[1][scale], m_odd_active_solver_masks[1][scale],
        m_even_active_solver_masks[1][scale]);
  }
  msg_print("");
#endif

  m_base_time += m_base_time_inc;
  // The comm thread doesn't need to know precisely what time it is, so we can store this
  // without a memory ordering constraint
  tATOMIC_REF(m_time).store(m_base_time / m_n_base_timesteps_per_timestep, std::memory_order_relaxed);
  bool this_sp_is_frozen = false;
  bool other_solver_sp_is_frozen = false;
  //Checks if we have reached the next time coupling phase, and update parameters accordingly. Additionally, we keep
  //iterating over the next phases until we reach a phase where we need to solve dynamics again (if the realm is frozen,
  //it is advanced to the next phase directly).
  BOOLEAN advanced_to_next_phase = FALSE;
  while (sim.time_coupling_info.maybe_update_time_coupling_phase()) {
    //reached the start of the next phase
    advanced_to_next_phase = TRUE;
    const sTIME_COUPLING_PHASE& phase = sim.time_coupling_info.active_phase();
    BASETIME next_phase_start = sim.time_coupling_info.next_phase_start(BASETIME_LAST);
    // msg_print("ts %d->%d, new phase up to %d", m_base_time-m_base_time_inc, m_base_time, next_phase_start);
    BASETIME * this_sp_scale_prior_base_time = nullptr;
#if !BUILD_GPU
    if (sim.is_radiation_model) {
      if (sim.is_conduction_sp) {
        this_sp_scale_prior_base_time = m_conduction_pde_tm.m_scale_prior_base_time.get();
      } else {
        this_sp_scale_prior_base_time = m_lb_tm.m_scale_prior_base_time.get();
      }
    }
#endif
    
    //Time to adjust the timing of the solvers / realm / recv channels  
    switch (phase.time_coupling) {
    case eTIME_COUPLING_SCHEME::FreezeOne: 
    {
      //We need to shift m_wsurfel_base_time_0 by the duration of the phase since by design it is a multiple of the
      //wsurfel comm periodicity but no comm occurs during the phase.
      m_wsurfel_base_time_0 += next_phase_start - phase.start;
      //In terms of the active solvers, treats the freeze as a single step for the solvers frozen. 
      // - First, advances next base time to when the solver will be active again to disable it from the
      //   active_solver_mask and make sure that when the solver is advanced after reaches the next base
      // - Second, if the solvers frozen are the ones active in the SP, jumps ahead in time to the next time-step where
      //   the solvers are active
      BASETIME base_time_before_freeze = m_base_time;
      if(phase.frozen_solver == eCOUPLED_SOLVER::FlowSolver) {
        m_lb_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (sim.is_heat_transfer) m_t_pde_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (sim.is_turb_model) m_ke_pde_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (sim.is_scalar_model) m_uds_pde_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (sim.is_particle_model) m_particle_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (!sim.is_conduction_sp) {
          //Nothing happens on the flow side during the freeze time, so directly jumps to the next phase after the freeze
          m_base_time = m_lb_tm.m_next_base_time;
          tATOMIC_REF(m_time).store(m_base_time / m_n_base_timesteps_per_timestep, std::memory_order_relaxed);
          //The other realm will keep marching forward during the frozen phase, so needs to sync its counters
          m_conduction_pde_tm.sync_cross_realm_for_next_phase(next_phase_start);
          //Once all Adjusts base_time_0 to account for the missing timesteps
          m_base_time_0 = m_base_time - m_base_time_inc;
          m_timestep_ctr_0 = m_timestep;
          this_sp_is_frozen = true;
        } else {
          other_solver_sp_is_frozen = true;
        }
      } else { //eCOUPLED_SOLVER::ConductionSolver
        m_conduction_pde_tm.update_next_base_time_for_next_phase(next_phase_start);
        if (sim.is_conduction_sp) {
          //jumps to next phase
          m_base_time = m_conduction_pde_tm.m_next_base_time;
          tATOMIC_REF(m_time).store(m_base_time / m_n_base_timesteps_per_timestep, std::memory_order_relaxed);
          //sync counters for solvers in the other realm
          m_lb_tm.sync_cross_realm_for_next_phase(next_phase_start);
          if (sim.is_heat_transfer) m_t_pde_tm.sync_cross_realm_for_next_phase(next_phase_start);
          if (sim.is_turb_model) m_ke_pde_tm.sync_cross_realm_for_next_phase(next_phase_start);
          if (sim.is_scalar_model) m_uds_pde_tm.sync_cross_realm_for_next_phase(next_phase_start);
          if (sim.is_particle_model) m_particle_tm.sync_cross_realm_for_next_phase(next_phase_start);
          //Adjusts base_time_0 to account for the missing timesteps
          m_base_time_0 = m_base_time - m_base_time_inc;
          m_timestep_ctr_0 = m_timestep;
          this_sp_is_frozen = true;
        } else {
          other_solver_sp_is_frozen = true;
        }
      }
#if !BUILD_GPU
      if (sim.is_radiation_model) {
        //adjusts the periodicity of the update to the realm handled by this SP
        BASETIME n_radiation_base_steps = n_rad_base_steps() / n_cond_base_steps() * m_base_time_inc;
        m_radiation_tm.start_new_time_coupling_phase(n_radiation_base_steps, 
                                                     base_time_before_freeze,
                                                     base_time_before_freeze, 
                                                     this_sp_is_frozen, 
                                                     this_sp_scale_prior_base_time);
      }
#endif
      break;
    }
    case eTIME_COUPLING_SCHEME::SameRate:
    case eTIME_COUPLING_SCHEME::DifferentRate:
    case eTIME_COUPLING_SCHEME::DifferentRateConservative: 
    {
      //Adjusts the time-stepping rate based on the phase type
      if (std::abs(phase.therm_time_ratio - m_therm_time_ratio) > 1.0e-06) { 
        //Since we are changing the periodicity of the supercycled realm needs to provide a new time reference point
        //both for the timestep counter and the wsurfel comm counter. Counters have not been updated yet, and the
        //wsurfel might not even be updated in the next timestep if we are in a subcyled realm, so we pick the previous
        //timestep as reference
        m_base_time_0 = m_base_time - m_base_time_inc;
        m_timestep_ctr_0 = m_timestep;
        m_wsurfel_base_time_0 = wsurfel_comm_cntr_to_basetime(m_wsurfel_comm_cntr);
        m_wsurfel_comm_ctr_0 = m_wsurfel_comm_cntr;
        //loads new ratio, which internally update m_base_time_inc, m_time_inc, and m_n_wsurfel_comm_base_steps
        BASETIME base_time_inc_before_time_ratio_update = m_base_time_inc;
        set_therm_time_ratio(phase.therm_time_ratio, phase.radiation_update_ratio);
        //The previous increment might be wrong, needs to correct it 
        m_base_time += m_base_time_inc - base_time_inc_before_time_ratio_update;
        tATOMIC_REF(m_time).store(m_base_time / m_n_base_timesteps_per_timestep, std::memory_order_relaxed);
        //Adjusts the solvers time-managers
        sync_solvers_tm_with_time_reference();
      }
#if !BUILD_GPU
      if (sim.is_radiation_model) {
        BASETIME rad_start_time = std::max(m_lb_tm.m_next_base_time, m_conduction_pde_tm.m_next_base_time);
        m_radiation_tm.start_new_time_coupling_phase(n_rad_base_steps(), rad_start_time, phase.start, false, this_sp_scale_prior_base_time);
      }
#endif
      // //If the span of the frozen phase does not respect the periodicity of the cross-realm comm, it is needed to we do
      // //extra work here, updating the expected ssp_timestep in the WSURFEL_RECV_TYPEreceive channel.
      // g_strand_mgr.m_recv_channel[WSURFEL_RECV_TYPE].update_ssp_timestep_to_next_phase(next_phase_start);
      break;
    }
    default:
      msg_print("Time phase coupling scheme %d not supported", phase.time_coupling);
      break;
    }
  }

  if (advanced_to_next_phase) {
    //Updates the averaging window start & end timesteps for the new phase
    sim.time_coupling_info.update_phase_averaging_start_end();
    //Checks if in the new phase all enabled solvers are active and advance with the same rate
    check_if_all_solvers_have_same_timestep();
  } else if (m_base_time > sim.time_coupling_info.m_averaging_end_basetime) {
    //Loosely coupled (staggered) solvers, averaging continuously through out the phase. Moves to the next averaging window
    sim.time_coupling_info.update_staggered_averaging_start_end();
  }

  // LOG_ON("TIME",LOG_FUNC).format("m_base_time {} m_time {}", m_base_time, m_time);

  m_lb_tm.advance(m_base_time, finest_scale);
  if(sim.is_heat_transfer)
    m_t_pde_tm.advance(m_base_time, finest_scale);
  if (sim.is_turb_model)
    m_ke_pde_tm.advance(m_base_time, finest_scale);
  if (sim.is_scalar_model)
    m_uds_pde_tm.advance(m_base_time, finest_scale);
  if (sim.is_conduction_model)
    m_conduction_pde_tm.advance(m_base_time, finest_scale);

  if(sim.is_particle_model)
    m_particle_tm.advance(m_base_time, finest_scale);

#if !BUILD_GPU
  if (sim.is_radiation_model)
    m_radiation_tm.advance(m_base_time);
#endif

  m_base_time_parity ^= 1;
  compute_coarsest_active_scales(finest_scale);

#ifdef DEBUG_TIMESTEP_UPDATE
  msg_print("After timestep_update; m_base_time %d", m_base_time);
  msg_print("m_time %d, m_base_time_parity = %d",
            m_time, m_base_time_parity);
  msg_print("m_coarsest_active_scales {%d, %d}",
            m_coarsest_active_scales[0],  m_coarsest_active_scales[1]);
  for(SCALE scale = FINEST_SCALE; scale >= m_coarsest_active_scales[0]; scale --) {
    msg_print("Scale %d regular odd even active_solver_masks[0] {%d, %d, %d}", scale,
        m_active_solver_masks[0][scale], m_odd_active_solver_masks[0][scale],
        m_even_active_solver_masks[0][scale]);
  }
  for(SCALE scale = FINEST_SCALE; scale >= m_coarsest_active_scales[1]; scale --) {
    msg_print("Scale %d regular odd even active_solver_masks[1] {%d, %d, %d}", scale,
        m_active_solver_masks[1][scale], m_odd_active_solver_masks[1][scale],
        m_even_active_solver_masks[1][scale]);
  }
  msg_print("");
#endif

  if(sim.switch_acous_during_simulation) {
    do_acous_switch();
  }
  update_solver_status(this_sp_is_frozen, other_solver_sp_is_frozen);
}

// precisely what time it is, so use relaxed operations here
VOID sTIMESCALE_INFO::update_solver_status(const bool is_this_sp_frozen, const bool is_other_solver_sp_frozen) {
  tATOMIC_REF(solver_status.status).store(g_timescale.m_time, std::memory_order_relaxed);
  if(sim.is_conduction_sp) {
    tATOMIC_REF(solver_status.cond_status).store(g_timescale.m_conduction_pde_tm.m_time, std::memory_order_relaxed);
    tATOMIC_REF(solver_status.flow_status).store(REALM_STATUS_INACTIVE, std::memory_order_relaxed);
  } else {
    tATOMIC_REF(solver_status.flow_status).store(g_timescale.m_lb_tm.m_time, std::memory_order_relaxed);
    tATOMIC_REF(solver_status.cond_status).store(REALM_STATUS_INACTIVE, std::memory_order_relaxed);
  }
}

/*********************************
 * Active-passive switching
 **********************************/

// This is not used yet in the nextgen code, but in the mainline it is called just
// before the main loop. It thus precedes the processing of any events and can use
// either of the two sim.thermal_accel structs, which at that point are identical.


VOID sTIMESCALE_INFO::calculate_T_lb_pde_solver_switch_interval()
{
//specifies that thermal acceleration is defined in flow timesteps only if conduction is active
cSTRING realm_str = (sim.is_conduction_model) ? "flow " : "";
#if BUILD_D19_LATTICE
  if (!sim.is_prescribed_T_solver_switch && my_proc_id == 0) {
    if (sim.thermal_accel.is_hacked) { //restore from checkpoint
      if (sim.thermal_accel.hacked_start > 0) {
        asINT32 start_time = sim.thermal_accel.hacked_start;
        if (sim.thermal_accel.hacked_period > 0)
          msg_print_no_prefix ("Thermal solver acceleration will be turn on at %stimestep %d for %d %stimesteps",
                               realm_str, start_time, sim.thermal_accel.hacked_period, realm_str);
        else
          msg_print_no_prefix ("Thermal solver acceleration will be turn on at %stimestep %d",
                               realm_str, start_time);
      } else { //sim.thermal_accel[EVENT_QUEUE_ID_REFERENCE].hacked_stop > 0
         msg_print_no_prefix ("Thermal solver acceleration will be turn off at %stimestep %d",
                              realm_str, sim.thermal_accel.hacked_stop);
      }
    } else if(sim.thermal_accel.start >= 0) {
      dFLOAT end = sim.thermal_accel.start + (dFLOAT)sim.thermal_accel.repeat * sim.thermal_accel.period - 1;
      msg_print_no_prefix ("Thermal solver will be accelerated from %d to %g %stimesteps with period %d and interval %d. ",
                           sim.thermal_accel.start, end, realm_str,
                           sim.thermal_accel.period, sim.thermal_accel.interval);
    }
  }
#else
  if(sim.thermal_accel.start >= 0 && my_proc_id == 0) {
    dFLOAT end = sim.thermal_accel.start + (dFLOAT)sim.thermal_accel.repeat * sim.thermal_accel.period - 1;
    msg_print_no_prefix ("Thermal solver will be accelerated from %d to %g %stimesteps with period %d and interval %d. ",
                         sim.thermal_accel.start, end, realm_str, 
                         sim.thermal_accel.period, sim.thermal_accel.interval);
  }
#endif
}

// This is not called either in the nextgen code ore in the main line; as far as I
// can tell, going back to 4.1a, it has never been used. Thus it's a little hard
// to know what it's intended use is, and how to update it appropriately to select
// the correct thermal_accel. If it is ever used, this will be necessary.

//#ifdef THIS_FUNCTION_IS_NOW_NEEDED_SO_FIX_IT

VOID sTIMESCALE_INFO::switch_T_solver(T_SOLVER_TYPE &T_solver_type)
{
  if(T_solver_type == PDE_TEMPERATURE)
    T_solver_type = LB_TEMPERATURE;
  else
    T_solver_type = PDE_TEMPERATURE;
}

BOOLEAN sTIMESCALE_INFO::switch_recv_group_solver_type(const BOOLEAN is_surfel_recv, const TIMESTEP prev_timestep, const SCALE scale)
{
  asINT32 timestep_increment = 1 << (FINEST_SCALE - scale);
  TIMESTEP next_timestep = prev_timestep + timestep_increment;
    if (sim.is_heat_transfer) {
#if BUILD_D19_LATTICE
      if(sim.thermal_accel.switch_recv_group_solver(is_surfel_recv, prev_timestep, next_timestep))
        return TRUE;
      else
        return FALSE;
#endif
    }
  return FALSE;
}

VOID sTIMESCALE_INFO::init_last_active_solver_masks() {
  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, COARSEST_SCALE) {
    m_last_active_solver_masks[scale] = sim.init_solver_mask;
#ifndef SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
    m_last_odd_active_solver_masks[scale] = sim.init_solver_mask;
#endif
    m_last_even_active_solver_masks[scale] = sim.init_solver_mask;
  }
}

void sRADIATION_TIMESTEP_MANAGER::start_new_time_coupling_phase(BASETIME _n_base_steps, 
                                                                BASETIME rad_start_base_time, 
                                                                BASETIME phase_start_base_time, 
                                                                bool this_sp_is_frozen_this_phase, 
                                                                BASETIME this_sp_scale_prior_base_time[])
{
  LOG_MSG("RAD").printf("phase start time %d rad_start_base_time %d sp_frozen %d", phase_start_base_time, rad_start_base_time, this_sp_is_frozen_this_phase);
  if (m_this_sp_is_currently_frozen && !this_sp_is_frozen_this_phase) {
    // Now we unfreeze
    m_n_base_steps = _n_base_steps;
    m_next_recv_base_time = rad_start_base_time;
    m_next_send_base_time = rad_start_base_time;
    m_do_radiation_sends = false;
    m_do_radiation_recvs = false;
    m_prior_send_base_time = phase_start_base_time - m_prior_send_base_time;
    m_prior_recv_base_time = phase_start_base_time - m_prior_recv_base_time;

    for (SCALE scale=0; scale < sim.num_scales; scale++) {
      this_sp_scale_prior_base_time[scale] = phase_start_base_time - m_this_sp_save_scale_prior_base_time_diff[scale];
    }

    m_this_sp_is_currently_frozen = false;
    LOG_MSG("RAD").printf("unfreeze");
  } else if (!m_this_sp_is_currently_frozen && this_sp_is_frozen_this_phase) {
    LOG_MSG("RAD").printf("freeze");
    // freeze the solver, save the important information
    m_prior_send_base_time = phase_start_base_time - m_prior_send_base_time;
    m_prior_recv_base_time = phase_start_base_time - m_prior_recv_base_time;

    for (SCALE scale=0; scale < sim.num_scales; scale++) {
      m_this_sp_save_scale_prior_base_time_diff[scale] = phase_start_base_time - this_sp_scale_prior_base_time[scale];
    }

    m_this_sp_is_currently_frozen = true;

  } else {
    m_n_base_steps = _n_base_steps;
    m_next_recv_base_time = rad_start_base_time;
    m_next_send_base_time = rad_start_base_time;
    m_do_radiation_sends = true;
    m_do_radiation_recvs = true;
  }

  LOG_MSG("RAD",LOG_FUNC).format("m_next_send_base_time {} m_next_recv_base_time {} m_do_radiation_sends {} m_do_radiation_recvs {}",m_next_send_base_time, m_next_recv_base_time, m_do_radiation_sends, m_do_radiation_recvs);
}

void sRADIATION_TIMESTEP_MANAGER::init(BASETIME start_base_time) 
{
  //Radiation does not keep track of a phase reference,
  m_this_sp_save_scale_prior_base_time_diff = std::make_unique<BASETIME[]>(sim.num_scales);
  m_this_sp_is_currently_frozen = false;

  m_next_send_base_time = (start_base_time > m_base_time_0) 
                          ? m_base_time_0 + ((start_base_time - m_base_time_0 - 1) / m_n_base_steps + 1) * m_n_base_steps
                          : m_base_time_0 + ((start_base_time - m_base_time_0) / m_n_base_steps) * m_n_base_steps;
  BASETIME last_next_base_time = m_next_send_base_time - m_n_base_steps;

  m_prior_send_base_time = last_next_base_time;
  
  if (last_next_base_time < 0) {
    /* normal start from t=0 */
    m_time = -1;
    m_do_radiation_sends = false;
    // set this to -1 for seeding to work correctly
    m_prior_send_base_time = -1;

    // By setting the recv to the next radiation interval, we 
    // introduce the necessary lag
    m_next_recv_base_time = m_next_send_base_time + m_n_base_steps;
  } else {
    /* checkpoint restore */
    if (start_base_time == last_next_base_time)
      m_do_radiation_sends = true;
    else {
      m_do_radiation_sends = false;
    }
  }

  // msg_print("rad start_base_time %d n_base_steps %d m_prior_send_base_time %d m_next_send_base_time %d do_sends %d do_recvs %d m_next_recv_base_time %d m_prior_recv_base_time %d", start_base_time, m_n_base_steps, m_prior_send_base_time, m_next_send_base_time, m_do_radiation_sends, m_do_radiation_recvs, m_next_recv_base_time, m_prior_recv_base_time);

  // LOG_ON("TIME",LOG_FUNC).format("m_next_send_base_time {} m_next_recv_base_time {} m_do_radiation_sends {} m_do_radiation_recvs {}",m_next_send_base_time, m_next_recv_base_time, m_do_radiation_sends, m_do_radiation_recvs);

  advance(start_base_time);

}

VOID sRADIATION_TIMESTEP_MANAGER::advance(BASETIME base_time)
{
  if (m_this_sp_is_currently_frozen) {
    return;
  }

  if (base_time == m_next_recv_base_time) {
    m_do_radiation_recvs = true;
    m_next_recv_base_time += m_n_base_steps;
  } else {
    m_do_radiation_recvs = false;
  }

  if (m_next_send_base_time == base_time) {
    m_do_radiation_sends = true;
    m_next_send_base_time += m_n_base_steps;
  } else {
    m_do_radiation_sends = false;
  }

  // if (m_do_radiation_sends || m_do_radiation_recvs)
    // msg_print("ADVANCE base time %d next_recv %d next send %d", base_time, m_next_recv_base_time, m_next_send_base_time);
}

VOID sRADIATION_TIMESTEP_MANAGER::set_phase_time_reference(BASETIME base_time_0, BASETIME n_base_steps_ref) {
  //Radiation only keep tracks of base times when info is sent/recv to/from the RPs, so only reference needed is a base
  //time when a send/recv occured
  m_base_time_0 = base_time_0;
  m_n_base_steps = n_base_steps_ref;
}

void sRADIATION_TIMESTEP_MANAGER::write_ckpt(const sTIMESTEP_MANAGER& lb_tm, const sTIMESTEP_MANAGER& cond_tm) 
{
#if !BUILD_GPU
  LGI_CKPT_RADIATION_TM record;
  size_t n_bytes = sizeof(record);
  if (sim.is_flow) {
    n_bytes += sizeof(LGI_CKPT_RADIATION_TIMESTEP)*sim.num_scales;
  }

  if (sim.is_conduction_model) {
    n_bytes += sizeof(LGI_CKPT_RADIATION_TIMESTEP)*sim.num_scales;
  }

  record.tag.length = n_bytes;

  lgi_write_init_tag(&record.tag, LGI_CKPT_RADIATION_TM_TAG, n_bytes);

  record.time = m_time;
  record.next_send_base_time = m_next_send_base_time;
  record.next_recv_base_time = m_next_recv_base_time;
  record.prior_send_base_time = m_prior_send_base_time;
  record.prior_recv_base_time = m_prior_recv_base_time;
  record.n_base_steps = m_n_base_steps;
  record.do_radiation_recvs = m_do_radiation_recvs;
  record.do_radiation_sends = m_do_radiation_sends;
  record.this_sp_is_currently_frozen = m_this_sp_is_currently_frozen;

  write_ckpt_lgi_head(record);

  if (sim.is_flow) {
    for (SCALE scale=0; scale<sim.num_scales; scale++) {
      LGI_CKPT_RADIATION_TIMESTEP t;
      t.time = lb_tm.m_scale_prior_base_time[scale];
      write_ckpt_lgi(t);
    }
  }

  if (sim.is_conduction_model) {
    for (SCALE scale=0; scale<sim.num_scales; scale++) {
      LGI_CKPT_RADIATION_TIMESTEP t;
      t.time = cond_tm.m_scale_prior_base_time[scale];
      write_ckpt_lgi(t);
    }
  }
#endif
}

void sRADIATION_TIMESTEP_MANAGER::read_ckpt(sTIMESTEP_MANAGER& lb_tm, sTIMESTEP_MANAGER& cond_tm) 
{
#if !BUILD_GPU
  LGI_CKPT_RADIATION_TM record;

  size_t n_bytes = sizeof(record);
  if (sim.is_flow) {
    n_bytes += sizeof(LGI_CKPT_RADIATION_TIMESTEP)*sim.num_scales;
  }

  if (sim.is_conduction_model) {
    n_bytes += sizeof(LGI_CKPT_RADIATION_TIMESTEP)*sim.num_scales;
  }

  read_lgi_head(record);

  // if (record.tag.length != n_bytes) {
  //   msg_internal_error("%d, %d, Error reading radiation tm ckpt data!", record.tag.length, n_bytes);
  // }

  m_time = record.time;
  m_next_send_base_time = record.next_send_base_time;
  m_next_recv_base_time = record.next_recv_base_time;
  m_prior_send_base_time = record.prior_send_base_time;
  m_prior_recv_base_time = record.prior_recv_base_time;
  m_n_base_steps = record.n_base_steps;
  m_do_radiation_recvs = record.do_radiation_recvs;
  m_do_radiation_sends = record.do_radiation_sends;
  m_this_sp_is_currently_frozen = record.this_sp_is_currently_frozen;

  if (sim.is_flow && !lb_tm.m_scale_prior_base_time) {
    lb_tm.m_scale_prior_base_time = std::make_unique<BASETIME[]>(sim.num_scales);
    for (SCALE scale=0; scale<sim.num_scales; scale++) {
      LGI_CKPT_RADIATION_TIMESTEP t;
      read_lgi(t);
      lb_tm.m_scale_prior_base_time[scale] = t.time;
    }
  }

  if (sim.is_conduction_model && !cond_tm.m_scale_prior_base_time) {
    cond_tm.m_scale_prior_base_time = std::make_unique<BASETIME[]>(sim.num_scales);
    for (SCALE scale=0; scale<sim.num_scales; scale++) {
      LGI_CKPT_RADIATION_TIMESTEP t;
      read_lgi(t);
      cond_tm.m_scale_prior_base_time[scale] = t.time;
    }
  }

#endif
}
