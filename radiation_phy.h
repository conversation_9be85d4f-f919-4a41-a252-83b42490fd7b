#ifndef EXA_SIMENG_RADIATION_PHY_H
#define EXA_SIMENG_RADIATION_PHY_H

#include PHYSTYPES_H

static constexpr float ZERO_EMISSIVITY_EPSILON = 1.0e-6;

class sRADIATION_PATCH_GROUP;

void do_radiation_patch_group(sRADIATION_PATCH_GROUP* group,
                              TIMESTEP prior_rad_index,
                              bool do_recvs,
                              bool do_sends,
                              BASETIME send_integration_interval,
                              BASETIME total_send_interval,
                              bool restart_for_ckpt_restore = false);

// void accumulate_radiation_patch_net_rad_flux(sRADIATION_PATCH_GROUP* group,
//                                              TIMESTEP prior_rad_index);
//
// void accumulate_radiation_patch_blackbody_power(sRADIATION_PATCH_GROUP* group,
//                                                 BASETIME integration_interval,
//                                                 BASETIME total_send_interval,
//                                                 asINT32 prior_rad_index);

#endif
