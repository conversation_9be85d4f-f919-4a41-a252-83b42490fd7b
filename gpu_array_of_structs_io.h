#ifndef GPU_AOS_IO_H
#define GPU_AOS_IO_H

#include "common_sp.h"
#include <cassert>
#include <algorithm>
#include <vector>

namespace GPU {
namespace detail {

__HOST__DEVICE__
static INLINE bool is_aligned(const void* pointer,
                              size_t byte_count)
{
  return ((uintptr_t) pointer) % byte_count == 0;
}

template<typename STREAM_ELEM_TYPE, typename S>
__DEVICE__ void INLINE
read_aos_coalesced_impl(const S* aos_in,
                        S* aos_out,
                        size_t num_passes_as_stream_elem,
                        size_t max_num_of_bytes_read_as_stream_elem,
                        size_t size_of_stream,
                        int tid,
                        int num_threads) {
  
  const STREAM_ELEM_TYPE* input_as_stream_of_elems = reinterpret_cast<const STREAM_ELEM_TYPE*>(aos_in);
  STREAM_ELEM_TYPE* output_as_stream_of_elems = reinterpret_cast<STREAM_ELEM_TYPE*>(aos_out);
  const std::byte* input_as_byte_stream = reinterpret_cast<const std::byte*>(aos_in);
  std::byte* output_as_byte_stream = reinterpret_cast<std::byte*>(aos_out);

  //read as stream elems
  {
    size_t loc = tid;
    while(loc < num_passes_as_stream_elem) {
      output_as_stream_of_elems[loc] = input_as_stream_of_elems[loc];
      loc += num_threads;
    }
  }
  //read remaining bytes as byte stream
  {    
    size_t loc = max_num_of_bytes_read_as_stream_elem + tid;
    while (loc < size_of_stream) {
      output_as_byte_stream[loc] = input_as_byte_stream[loc];
      loc += num_threads;
    }
  }
}


template<typename STREAM_ELEM_TYPE, typename S>
__HOST__DEVICE__
static void alignment_checks(const S* aos_input_array,
                             S* aos_output_array) {
  static_assert(sizeof(S) > sizeof(STREAM_ELEM_TYPE),
                "Size of struct must be greater than size of stream element type");
  
  constexpr size_t minimum_alignment = sizeof(STREAM_ELEM_TYPE);
  cassert(is_aligned(aos_input_array, minimum_alignment));
  cassert(is_aligned(aos_output_array, minimum_alignment));
}


template<typename STREAM_ELEM_TYPE = float, typename S>
__DEVICE__ INLINE void
read_aos_coalesced_tid(const S* aos_input_array,
                       S* aos_output_array,
                       size_t num_elems,
                       int tid,
                       int num_threads) {

  alignment_checks<STREAM_ELEM_TYPE, S>(aos_input_array, aos_output_array);
  const size_t size_of_stream = sizeof(S) * num_elems;
  constexpr size_t size_of_stream_elem = sizeof(STREAM_ELEM_TYPE);
  const size_t num_passes_as_stream_elem = size_of_stream / size_of_stream_elem;
  const size_t max_num_of_bytes_read_as_stream_elem = num_passes_as_stream_elem * size_of_stream_elem;
  assert(size_of_stream >= max_num_of_bytes_read_as_stream_elem);

  
  read_aos_coalesced_impl<STREAM_ELEM_TYPE>(aos_input_array,
                                            aos_output_array,
                                            num_passes_as_stream_elem,
                                            max_num_of_bytes_read_as_stream_elem,
                                            size_of_stream,
                                            tid, num_threads);
}
}//namespace detail

/*@fcn write_aos_coalesced
 * Given an array of structs "aos_input_array" in global or shared memory, this
 * API reinterprets this memory as a stream of STREAM_ELEM_TYPE chunks, and has
 * participating threads write the stream data to the output array of structs
 * "aos_output_array" in a coalesced fashion
 */
template<typename STREAM_ELEM_TYPE = float, typename S, size_t N>
__DEVICE__ INLINE void write_aos_coalesced(const S (&aos_input_array) [N],
                                           S (&aos_output_array) [N],
                                           int tid, int num_threads) {
  
  detail::read_aos_coalesced_tid<STREAM_ELEM_TYPE>(aos_input_array,
                                                   aos_output_array,
                                                   N, tid, num_threads);
}

/*@fcn write_aos_coalesced
 * Overload variant of write_aos_coalesced that accepts an additional "active_elements"
 * argument specifies which elements in aos_input_array are to be copied to aos_output_array
 * "active_elements" must be allocated in shared or global memory
 */
template<typename STREAM_ELEM_TYPE = float, typename S, size_t N>
__DEVICE__ INLINE void write_aos_coalesced(const S (&aos_input_array) [N],
                                           const uINT8 (&active_elements) [N],
                                           S* aos_output_array,
                                           int tid, int num_threads) {
  size_t num_contiguous_elems = 0;
  size_t curr_buff_index = 0;
  ccDOTIMES(s, N) {
    if (active_elements[s]) {
      num_contiguous_elems++;
    } else if (num_contiguous_elems) {
      detail::read_aos_coalesced_tid(aos_input_array + curr_buff_index,
                                     aos_output_array + curr_buff_index,
                                     num_contiguous_elems,
                                     tid, num_threads);
      curr_buff_index += (num_contiguous_elems + 1); //+1 for non active element
      num_contiguous_elems = 0;
    } else {
      curr_buff_index += 1;
    }
  }

  if (num_contiguous_elems) {
    detail::read_aos_coalesced_tid(aos_input_array + curr_buff_index,
                                   aos_output_array + curr_buff_index,
                                   num_contiguous_elems,
                                   tid, num_threads);
  }  
}

} //namespace GPU
#endif
