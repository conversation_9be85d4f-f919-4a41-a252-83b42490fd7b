#ifndef _TRAJECTORY_MEAS_H
#define _TRAJECTORY_MEAS_H

// These templates define a class that facilitates moving the three types of trajectory data
// from the SP to the CP. It currently handles hitpoints, vertices, and instances of parcel
// creation (refered to as startpoints herein). The three data types are defined in cp_sp_lib.

// Measurements are added to a FIFO buffer as they become available. At the end of each timestep
// the SP send the content of the FIFO to the CP.


#include <typeinfo>
#include SP_H
#include "common_sp.h"
#include "particle_solver_data.h"
#include "trajectory_window.h"
#include "thread_run.h"
#include "surfel_dyn_sp.h"

extern BOOLEAN g_has_trajectory_windows;
inline BOOLEAN has_trajectory_window() { return g_has_trajectory_windows; }

typedef class sSP_TRAJECTORY_ID_MAP {
 private:
  // This is the start of the time interval that the map currently covers. The length of the map is
  // TRAJECTORY_ID_MAP_SEND_PERIOD, except for a map created when resuming from checkpoint on a 
  // different number of processors. The length of that map is TRAJECTORY_ID_MAP_SEND_PERIOD +
  // TRAJECTORY_ID_MAP_RECV_DELAY.
  TIMESTEP m_current_start_timestep;
  sINT32 m_n_sps;
  asINT32 m_n_windows;
  sINT32 m_n_emitters;
  sINT32 m_n_timesteps;
  std::vector<TRAJECTORY_ID> m_base_global_ids;
  MPI_Request m_recv_request;
  
  asINT32 count_total_pri_emitters();

 public:
  sSP_TRAJECTORY_ID_MAP() { m_recv_request = MPI_REQUEST_NULL; }

  VOID post_map_recv() { 
    MPI_Irecv(m_base_global_ids.data(), m_base_global_ids.size(), eMPI_asINT32, eMPI_sp_cp_rank(), 
              eMPI_TRAJECTORY_GLOBAL_ID_TAG, eMPI_sp_cp_comm, &m_recv_request); 
  }

  VOID cancel_pending_recv() {
    if (m_recv_request != MPI_REQUEST_NULL)
      MPI_Cancel(&m_recv_request);
  }
  VOID initialize(asINT32 n_sps, bool is_ckpt_resume_map = false) {
    m_n_sps = n_sps;
    m_n_windows = g_trajectory_windows.size();
    m_n_emitters = count_total_pri_emitters();
    if (is_ckpt_resume_map)
      m_n_timesteps = TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY;
    else
      m_n_timesteps = TRAJECTORY_ID_MAP_SEND_PERIOD;

    m_base_global_ids.resize(m_n_timesteps * m_n_sps * m_n_windows * m_n_emitters);
    m_current_start_timestep = ((g_timescale.time_flow() - TRAJECTORY_ID_MAP_RECV_DELAY) 
                                / TRAJECTORY_ID_MAP_SEND_PERIOD * TRAJECTORY_ID_MAP_SEND_PERIOD);
    m_recv_request = MPI_REQUEST_NULL;
    m_comm_ready = false;
    m_comm_done = false;
  }

  asINT32 total_pri_emitters() { return m_n_emitters; }

  TIMESTEP current_start_timestep() { return m_current_start_timestep; }

  bool remap_at_end_of_this_timestep(TIMESTEP t) // "remap" means convert from local IDs to global IDs
  { return (t + 1) - m_current_start_timestep == TRAJECTORY_ID_MAP_SEND_PERIOD + TRAJECTORY_ID_MAP_RECV_DELAY; }

  bool is_timestep_in_map_range(TIMESTEP t) {
    return (t >= m_current_start_timestep 
            && t - m_current_start_timestep < m_n_timesteps);
  }

  bool is_timestep_before_map_range(TIMESTEP t) {
    return t < m_current_start_timestep;
  }

  asINT32 global_base_id(TIMESTEP t, asINT32 sp, asINT32 window, asINT32 emitter) {
    return m_base_global_ids[ (((t - m_current_start_timestep) * m_n_sps + sp) * m_n_windows + window) * m_n_emitters + emitter ];
  }

  VOID receive_base_global_ids() {
    complete_mpi_request(&m_recv_request, MPI_SLEEP_LONG);
    remap_parcel_trajectory_ids();
    post_map_recv();
  }

  // This is used on checkpoint restore if resuming on a different number of processors to fill in the map
  // from the checkpoint file.
  std::vector<TRAJECTORY_ID> &base_global_ids() { return m_base_global_ids; }

  VOID finalize() { if (m_recv_request != MPI_REQUEST_NULL) MPI_Cancel(&m_recv_request); }

  volatile bool m_comm_ready;
  volatile bool m_comm_done;
  VOID wait_for_trajectory_global_id_comm_done() {
    while (m_comm_done == false) {
      sp_thread_sleep(MPI_SLEEP_LONG);
    }
    m_comm_done = false;
  }
  bool is_trajectory_global_id_comm_ready() {
    return m_comm_ready;
  }
  VOID mark_trajectory_global_id_comm_done() {
    m_comm_done = true;
    m_comm_ready = false;
  }
  VOID mark_trajectory_global_id_comm_ready() {
    m_comm_ready = true;
  }
 private:
  VOID remap_parcel_trajectory_id(PARCEL_STATE parcel);
  VOID remap_voxel_trajectory_ids(UBLK ublk, asINT32 voxel);
  VOID remap_surfel_trajectory_ids(sSURFEL* surfel);
  VOID remap_parcels_on_surfels_of_type(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
 public:
  VOID remap_parcel_trajectory_ids(); // "remap" means convert from local IDs to global IDs

} *SP_TRAJECTORY_ID_MAP;

// The three datatypes this template may be used with are defined in
// cp_sp_lib (sTRAJECTORY_VERTEX, sTRAJECTORY_HITPOINT, and sTRAJECTORY_STARTPOINT).

template <typename DATA_TYPE>
class sSP_TRAJECTORY_MANAGER
{
 private:

  // m_data_fifo holds the data elements recorded by the main thread. Since we only send
  // one message per timestep with all elements that were recorded in that timestep, we
  // don't actually need a fifo. However, a fifo would allow the comm thread to send multiple
  // messages during a timestep asynchronous to the compute thread (modulo the fact that 
  // std::deque is not necessarily thread safe).
  //
  // Given that we don't actually need a fifo, we could use an std::vector or any other
  // extensible vector. However, we'd prefer a block-oriented vector (tearray or std::deque)
  // to prevent hiccups associated with copying the content of the vector when it needs to
  // grow. Thus std::queue built on std::deque fits the bill.
  //
  // Finally, note that if we opted for std::vector, we could have a pair of vectors and
  // switch back-and-forth between the vectors on successive timesteps, eliminating the need
  // for copying the content of m_data_fifo into a separate send buffer. VMEM_VECTOR would
  // probably be an even better choice than std::vector.

  std::deque<DATA_TYPE> m_data_fifo;
  size_t m_fifo_max_size;

  std::vector<DATA_TYPE> m_send_buffer;
  MPI_Request m_send_request;

  std::string m_data_type_name;

  volatile bool m_ready_to_send;
  volatile bool m_prev_data_sent;

 public:

  sSP_TRAJECTORY_MANAGER() { m_data_type_name = typeid(DATA_TYPE).name(); }

  ~sSP_TRAJECTORY_MANAGER(){}

  VOID initialize()
  {
    // Can't initialize the MPI data types needed in the constructor because MPI_Init may not have been called yet.
    DATA_TYPE::define_mpi_type();
    m_fifo_max_size = 0;
    m_send_request = MPI_REQUEST_NULL;
    insert_header_element(g_timescale.time_flow()); // first element is a dummy element that just includes the timestep
    m_ready_to_send = false;
    m_prev_data_sent = false;
  }

  VOID mark_trajectory_data_ready_to_send() {
    m_ready_to_send = true;
  }

  VOID wait_for_previous_data_send() {
    while (!m_prev_data_sent) {
      sp_thread_sleep(MPI_SLEEP_SHORT);
    }
    m_data_fifo.clear();
    m_prev_data_sent = false;
  }

  VOID send_trajectory_data_to_cp()
  {
    if (!m_ready_to_send)
      return;
    // Make sure prior send is complete before altering the send buffer
    complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);

    // Fill the send buffer
    size_t num_elements_in_message = m_data_fifo.size();
    if (num_elements_in_message > m_fifo_max_size)
      m_fifo_max_size = num_elements_in_message;

    // If the previously allocated send buffer is so large that it is wasting storage, free the 
    // existing storage and start over. The only way to do this with std::vector is to use swap.
    if (m_send_buffer.capacity() > (32 * 1024)
        && 2.5 * num_elements_in_message < m_send_buffer.capacity()) {
      std::vector<DATA_TYPE> new_send_buffer;
      m_send_buffer.swap(new_send_buffer);
    }
    m_send_buffer.clear();
    if (num_elements_in_message > m_send_buffer.size())
      m_send_buffer.resize(num_elements_in_message);

    ccDOTIMES(element_count, num_elements_in_message) {
      m_send_buffer[element_count] = m_data_fifo[element_count];
    }
    if (g_trajectory_use_blocking_synchronous_send) {
      // Use MPI_Issend to ensure we do not overrun the CP
      MPI_Issend(m_send_buffer.data(), num_elements_in_message, DATA_TYPE::m_eMPI_data_type,
                 eMPI_sp_cp_rank(), DATA_TYPE::mpi_tag(), eMPI_sp_cp_comm, &m_send_request);
    } else {
      // Consider using MPI_Issend to ensure we do not overrun the CP
#if defined(_EXA_IMPI)
      MPI_Issend(m_send_buffer.data(), num_elements_in_message, DATA_TYPE::m_eMPI_data_type, 
                eMPI_sp_cp_rank(), DATA_TYPE::mpi_tag(), eMPI_sp_cp_comm, &m_send_request);
#else
      MPI_Isend(m_send_buffer.data(), num_elements_in_message, DATA_TYPE::m_eMPI_data_type, 
                eMPI_sp_cp_rank(), DATA_TYPE::mpi_tag(), eMPI_sp_cp_comm, &m_send_request);
#endif
    }

    // If the previously allocated fifo is so large that it is wasting storage, free the 
    // existing storage and start over. The only way to do this with std::deque is to use swap.
    if (m_fifo_max_size > (32 * 1024)
       && 2.5 * num_elements_in_message < m_fifo_max_size) {
      std::deque<DATA_TYPE> new_data_fifo;
      m_data_fifo.swap(new_data_fifo);
      m_fifo_max_size = 0;
    }

    //msg_print("Sending %d elements at T %ld",num_elements_in_message, g_timescale.m_time);
    insert_header_element(g_timescale.time_flow() + 1);
    m_ready_to_send = false;
    m_prev_data_sent = true;
  }

  VOID record(DATA_TYPE &elt) { m_data_fifo.push_back(elt); }

  VOID insert_header_element(TIMESTEP timestep) {
#if TRAJECTORY_DATA_MSG_INCLUDES_HEADER_ELEMENT
    DATA_TYPE elt;
    elt.ts_index = timestep;  // header flag
    elt.order_in_ts = 0;
    m_data_fifo.push_back(elt);
#endif
  }
};

typedef class sSP_TRAJECTORY_MANAGER<sTRAJECTORY_VERTEX>     sSP_TRAJECTORY_VERTEX_MANAGER,     *SP_TRAJECTORY_VERTEX_MANAGER;
typedef class sSP_TRAJECTORY_MANAGER<sTRAJECTORY_HITPOINT>   sSP_TRAJECTORY_HITPOINT_MANAGER,   *SP_TRAJECTORY_HITPOINT_MANAGER;
typedef class sSP_TRAJECTORY_MANAGER<sTRAJECTORY_STARTPOINT> sSP_TRAJECTORY_STARTPOINT_MANAGER, *SP_TRAJECTORY_STARTPOINT_MANAGER;

extern sSP_TRAJECTORY_ID_MAP g_trajectory_id_map;
extern sSP_TRAJECTORY_VERTEX_MANAGER     sp_trajectory_vertex_manager;
extern sSP_TRAJECTORY_HITPOINT_MANAGER   sp_trajectory_hitpoint_manager;
extern sSP_TRAJECTORY_STARTPOINT_MANAGER sp_trajectory_startpoint_manager;

#endif
