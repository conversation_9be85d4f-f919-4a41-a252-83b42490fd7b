/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#ifndef _SIMENG_EVENT_QUEUE_EVENT_H
#define _SIMENG_EVENT_QUEUE_EVENT_H

#include "common_sp.h"

class sEVENT_QUEUE_EVENT {
public:
  EVENT_ID  id;
  dFLOAT    arg;
  BASETIME  timestep;
  sINT32    recur_period;
  BASETIME  recur_end;

  sEVENT_QUEUE_EVENT () {
    id = EVENT_ID_INVALID;
    arg = -1;
    timestep = BASETIME_INVALID;
    recur_period = TIMESTEP_INVALID;
    recur_end = BASETIME_INVALID;
  }

  static void set_received_exit_event();

  virtual BOOLEAN unique(VOID) = 0;
  virtual BOOLEAN process() = 0;
  virtual VOID print(VOID) = 0;
  virtual ~sEVENT_QUEUE_EVENT() { }

protected:
  static bool do_not_process();
  bool static comm_thread_received_exit_event;
  [[nodiscard]] bool static wait_for_comm_thread_or_exit(cBOOLEAN& flag);

};


#endif /* #ifndef _SIMENG_EVENT_QUEUE_EVENT_H */

