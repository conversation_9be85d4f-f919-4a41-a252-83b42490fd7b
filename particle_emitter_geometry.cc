/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_emitter_geometry.h"
#include "particle_random_properties.h"
#include "eqns.h"
#include G3_H

asINT32 sEMISSION_POINTS::emission_points_ids = 0;
asINT32 sEMISSION_SURFACE::emission_surfaces_ids = 0;


VOID sEMISSION_POINTS::sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_spacing[3]){

  asINT32 num_points = m_points.size() / 3;

  //Pick a random integer from [0, n).
  asINT32 nth_point = std::min(int((g_random_particle_properties->uniform() + 1.0) / 2.0 * num_points), num_points - 1);

  pos[0] = m_points[3 * nth_point];
  pos[1] = m_points[3 * nth_point + 1];
  pos[2] = m_points[3 * nth_point + 2];
}

VOID sEMISSION_BOX::sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_spacing[3])
{
  if (fixed_release_points) {
    asINT32 npt = emission_points.m_points.size();

    //random [0, n)
    asINT32 n = std::min(int((g_random_particle_properties->uniform() + 1.0)/2.0*npt), npt-1);

    vcopy(pos, emission_points.m_points[n]->m_point);
  } else {
    //Pick points with a uniform distirbution from within the box's body csys.
    dFLOAT x = g_random_particle_properties->uniform();
    dFLOAT y = g_random_particle_properties->uniform();
    dFLOAT z = g_random_particle_properties->uniform();
    pos[0] = x * m_size[0] * 0.5 + m_center[0];
    pos[1] = y * m_size[1] * 0.5 + m_center[1];
    pos[2] = z * m_size[2] * 0.5 + m_center[2];

    transform_position_from_geometry_to_lattice_csys(pos);
  }
}


dFLOAT random_position_on_major_axis_for_uniform_dist(dFLOAT r0, dFLOAT r1)
{
  // Compute a random coordinate (nondimensionalized by the cylinder length) along the major axis of a
  // cylinder such that, after repedetivly distributing points uniformly at the chosen cylinder's
  // cross section, points within the volume of the cylinder will be uniformly distributed.

  dFLOAT l = 0.5 * ( g_random_particle_properties->uniform() + 1.0 );
  dFLOAT delta_r = r1 - r0;
  if( std::fabs(delta_r) < 1.0e-5 * std::min(r0, r1))
    return l;
  l = (std::pow((r1*r1*r1 - r0*r0*r0) * l + r0*r0*r0, 1.0 / 3.0) - r0 ) / delta_r;
  return l;
}

VOID sEMISSION_CYLINDER::sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_points, sPARTICLE_VAR release_spacing[3])
{
  if (fixed_points) {
    asINT32 npt = emission_points.m_points.size();

    //random [0, n)
    asINT32 n = std::min(int((g_random_particle_properties->uniform() + 1.0)/2.0*npt), npt-1);
    vcopy(pos, emission_points.m_points[n]->m_point);
  } else {
    //Pick point uniformly distributed within the cylinder.
    dFLOAT l = random_position_on_major_axis_for_uniform_dist(m_radii[0], m_radii[1]);
    dFLOAT r = std::sqrt(0.5 * ( g_random_particle_properties->uniform() + 1.0 ));
    dFLOAT theta = M_PI * g_random_particle_properties->uniform();
    //Get the coordinates in the cylinder's body aligned csys.
    pos[0] = l * m_cylinder_length;
    dFLOAT local_cylinder_radius = m_radii[0] + l * (m_radii[1] - m_radii[0]);
    pos[1] = r * local_cylinder_radius * cos(theta);
    pos[2] = r * local_cylinder_radius * sin(theta);

    transform_position_from_geometry_to_lattice_csys(pos);
  }
}

//the result is in default csys
//vec must be in default csys
VOID sEMISSION_CYLINDER::sample_position_with_vector(sPARTICLE_VAR pos[3], sPARTICLE_VAR tire_csys[3][3], sPARTICLE_VAR vec[3])
{
  sPARTICLE_VAR nor[3], tan[3];

  sPARTICLE_VAR s = vdot(vec, m_cylinder_axis);
  vscale(nor, s, m_cylinder_axis);
  vsub(nor, vec, nor);
  vunitize(nor);

  //tangent on the cylinder
  vcross(tan, nor, m_cylinder_axis);

  vcopy(tire_csys[0], tan);
  vcopy(tire_csys[1], nor);
  vcopy(tire_csys[2], m_cylinder_axis);

  sdFLOAT loc = 0.5*(g_random_particle_properties->uniform() + 1.0)*m_cylinder_length;
  vscale(pos, loc, m_cylinder_axis);

  s = m_radii[0]*(1.0-loc) + m_radii[1]*loc;
  vscale(nor, s, nor);

  vinc(pos, m_centers[0]);
  vinc(pos, nor);

  vinc(pos, sim.case_origin);
}

VOID sEMISSION_CYLINDER::principle_direction(dFLOAT principle_direction_vector[])
{
  dFLOAT center0[3], center1[3];

  vcopy(center0, m_centers[0]);
  vcopy(center1, m_centers[1]);

  transform_position_from_geometry_to_lattice_csys(center0);
  transform_position_from_geometry_to_lattice_csys(center1);

  vsub(principle_direction_vector, center1, center0);
  vunitize(principle_direction_vector);
}

VOID sEMISSION_SURFACE::sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_points, sPARTICLE_VAR release_spacing[3])
{
  if( fixed_points ) {

    std::vector<sPFACET> &facets = m_facets;

    asINT32 num_facets = facets.size();


    sdFLOAT loc = 0.5*(g_random_particle_properties->uniform() + 1.0);

    //bisection search
    int bot = 0, top = num_facets-1;
    int mid = (bot + top)/2;
    while(top - bot > 1)
      {
        if(loc >= facets[mid].m_weight) bot = mid;
        else top = mid;

        mid = (bot + top)/2;
      }

    asINT32 n = loc <= facets[bot].m_weight ? bot : top;

    current_face = n;
    sPFACET &facet = facets[n];

    ccDOTIMES(i, 3) {
      pos[i] = facet.m_centroid.pcoord[i];
    }
  } else {
    std::vector<sPFACET> &facets = m_facets;
    asINT32 num_facets = facets.size();

    sdFLOAT loc = 0.5*(g_random_particle_properties->uniform() + 1.0);

    //bisection search
    int bot = 0, top = num_facets-1;
    int mid = (bot + top)/2;
    while(top - bot > 1)
      {
        if(loc >= facets[mid].m_weight) bot = mid;
        else top = mid;

        mid = (bot + top)/2;
      }

    asINT32 n = loc <= facets[bot].m_weight ? bot : top;

    current_face = n;
    sPFACET &facet = facets[n];

    //find a random point inside the polygon, assume convex polygon
    vzero(pos);

    int nv = facet.m_vertices.size();


    //  if(1)
    if(nv != 3)
      {
        //WRONG this is not a uniform distribution on the polygon
        sdFLOAT sum_weight = 0.0;
        ccDOTIMES(i, nv) {
          sdFLOAT weight = 0.5*(g_random_particle_properties->uniform() + 1.0);
          sum_weight += weight;

          ccDOTIMES(j, 3) {
            pos[j] += weight*facet.m_vertices[i].pcoord[j];
          }
        }
        vscale(pos, 1.0/sum_weight, pos);
      }
    else
      {
        //uniform distribution on a triangle
        sdFLOAT z = std::min(0.5*(g_random_particle_properties->uniform() + 1.0), 1.0);
        sdFLOAT alpha = std::sqrt(1.0-z);
        sdFLOAT beta = std::min(0.5*(g_random_particle_properties->uniform() + 1.0), 1.0);

        ccDOTIMES(j, 3) {
          pos[j] =  (1.0-beta)*alpha*facet.m_vertices[0].pcoord[j]
            + beta*alpha*facet.m_vertices[1].pcoord[j]
            + (1.0-alpha)*facet.m_vertices[2].pcoord[j];
        }
      }
  }

}


VOID sEMISSION_BOX::init_fixed_emission_points()
{
  if(emission_points.fixed_release_points == FALSE)
    return;

  sCSYS &box_and_default = sim.csys_table[m_csys_id];
  sCSYS &emi_and_default = sim.csys_table[emission_points.m_csys_id];
  sCSYS box_and_emi;
  dFLOAT wmin[3];
  dFLOAT wmax[3];

  //transform from box to default, then from default to emission
  //l_to_g is from box to emission
  //g_to_l is from emission to box
  box_and_emi.l_to_g_xform = g3_xform_composition(box_and_default.l_to_g_xform, emi_and_default.g_to_l_xform);
  box_and_emi.g_to_l_xform = g3_xform_composition(emi_and_default.l_to_g_xform, box_and_default.g_to_l_xform);

  //  box_and_emi = box_and_default;
  dFLOAT pt[3];
  ccDOTIMES(i, 2) {
    ccDOTIMES(j, 2) {
      ccDOTIMES(k, 2) {
        pt[0] = (i - 0.5) * m_size[0] + m_center[0];
        pt[1] = (j - 0.5) * m_size[1] + m_center[1];
        pt[2] = (k - 0.5) * m_size[2] + m_center[2];
        //transform to emission sys
        xform_point(pt, &box_and_emi);
        ccDOTIMES(dim, 3) {
          if(i == 0 && j == 0 && k == 0) {
            wmin[dim] = pt[dim];
            wmax[dim] = pt[dim];
          } else {
            wmin[dim] = std::min(wmin[dim], pt[dim]);
            wmax[dim] = std::max(wmax[dim], pt[dim]);
          }
        } // for dim
      } // for k
    } // for j
  } // for i

  int n_points[3], min_plane[3];
  ccDOTIMES(i, 3) {
    n_points[i] = (int)((wmax[i] - wmin[i])/emission_points.m_release_spacing[i]) + 2;
    min_plane[i] = (int)(wmin[i]/emission_points.m_release_spacing[i]) - 1;
  }

  ccDOTIMES(i, n_points[0]) {
    ccDOTIMES(j, n_points[1]) {
      ccDOTIMES(k, n_points[2]) {
        //pt is in emission sys
        pt[0] = (min_plane[0] + i)*emission_points.m_release_spacing[0];
        pt[1] = (min_plane[1] + j)*emission_points.m_release_spacing[1];
        pt[2] = (min_plane[2] + k)*emission_points.m_release_spacing[2];

        //transform from emission sys to box sys
        xform_point_inv(pt, &box_and_emi);

        int n;
        for(n=0; n<3; n++)
          if(pt[n] < m_corners[0][n] || pt[n] > m_corners[1][n]) 
            break;

        if(n < 3) continue;

        //transform from box to default sys
        xform_point(pt, &box_and_default);
        //translate from the default sys to the lattice sys
        ccDOTIMES(dim, 3) {
          pt[dim] += sim.case_origin[dim];
        }

        FIXED_EMISSION_LOCATION emi_pt = xnew sFIXED_EMISSION_LOCATION;
        vcopy(emi_pt->m_point, pt);
        emi_pt->m_ublk = m_ublk_octree->find(pt[0], pt[1], pt[2], emi_pt->m_voxel);

        emission_points.m_points.push_back(emi_pt);
      }
    }
  }

  if(emission_points.m_points.size() == 0)
    msg_internal_error("No emission points inside emission box");
}

VOID sEMISSION_POINTS::precompute_geometry_chars()
{
  //Create the octree for the ublks in this box.
  m_ublk_octree = xnew sSUB_OCTREE;
  m_ublk_octree->init();

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (ublk_touches_emission_geometry(farblk)) {
        m_ublk_octree->register_ublk(farblk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale){
      if (ublk_touches_emission_geometry(nearblk)) {
        m_ublk_octree->register_ublk(nearblk);
      }
    }
  }

  m_ublk_octree->build_octree();
}

VOID sEMISSION_BOX::principle_direction(dFLOAT principle_direction_vector[])
{
  dFLOAT x_area = m_size[1]*m_size[2];
  dFLOAT y_area = m_size[0]*m_size[2];
  dFLOAT z_area = m_size[0]*m_size[1];

  dFLOAT origin[] = {0.0, 0.0, 0.0};
  dFLOAT direction[] = {0.0, 0.0, 0.0};
  transform_position_from_geometry_to_lattice_csys(origin);

  if(x_area >= y_area && x_area >= z_area) {
    direction[0] = 1.0;
  } else if(y_area >= x_area && y_area >= z_area) {
    direction[1] = 1.0;
  } else {
    direction[2] = 1.0;
  }

  transform_position_from_geometry_to_lattice_csys(direction);
  vsub(principle_direction_vector, direction, origin);
  vunitize(principle_direction_vector);
}

VOID sEMISSION_BOX::precompute_geometry_chars()
{
  //Create the octree for the ublks in this box.
  m_ublk_octree = xnew sSUB_OCTREE;
  m_ublk_octree->init();

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (ublk_touches_emission_geometry(farblk)) {
        m_ublk_octree->register_ublk(farblk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale){
      if (ublk_touches_emission_geometry(nearblk)) {
        m_ublk_octree->register_ublk(nearblk);
      }
    }
  }
  m_ublk_octree->build_octree();

  init_fixed_emission_points();
}

VOID sEMISSION_CYLINDER::init_fixed_emission_points()
{
  if(emission_points.fixed_release_points == FALSE)
    return;

  sCSYS &cylinder_and_default = m_body_csys;
  sCSYS &emi_and_default = sim.csys_table[emission_points.m_csys_id];
  sCSYS cylinder_and_emi;
  dFLOAT r1 = m_radii[0], r2 = m_radii[1];
  dFLOAT h = m_cylinder_length;
  dFLOAT wmin[3], wmax[3];

  //transform from cylinder to default, then from default to emission direction
  //l_to_g is from cylinder to emission
  //g_to_l is from emission to cylinder
  cylinder_and_emi.l_to_g_xform = g3_xform_composition(
                                                       cylinder_and_default.l_to_g_xform, emi_and_default.g_to_l_xform);
  cylinder_and_emi.g_to_l_xform = g3_xform_composition(
                                                       emi_and_default.l_to_g_xform, cylinder_and_default.g_to_l_xform);

  //  cylinder_and_emi = cylinder_and_default;

  //see compute_direction_transformation
  //local (1,0,0) corresponds the cylinder axis. (0,1,0) and (0,0,1) correspond the 2 and 3 line of the matrix
  ccDOTIMES(i, 3) {
    dFLOAT e1 = cylinder_and_emi.l_to_g_xform.xcoord[1][i];
    dFLOAT e2 = cylinder_and_emi.l_to_g_xform.xcoord[2][i];
    dFLOAT wlen = std::sqrt(e1*e1 + e2*e2);
    dFLOAT p1 = cylinder_and_emi.l_to_g_xform.xcoord[3][i];  //(0,0,0) bottom center
    dFLOAT p2 = cylinder_and_emi.l_to_g_xform.xcoord[0][i]*h + p1; //(h,0,0) top center
    wmax[i] = std::max(p1 + r1*wlen, p2 + r2*wlen);
    wmin[i] = std::min(p1 - r1*wlen, p2 - r2*wlen);

    //    printf("wlen %g  %g  %g\n", wlen, p1*LENGTH_TO_MKS_SCALE_FACTOR, p2*LENGTH_TO_MKS_SCALE_FACTOR);
  }

  //  printf("min %g %g %g\n", wmin[0]*LENGTH_TO_MKS_SCALE_FACTOR, wmin[1]*LENGTH_TO_MKS_SCALE_FACTOR, wmin[2]*LENGTH_TO_MKS_SCALE_FACTOR);
  //  printf("max %g %g %g\n", wmax[0]*LENGTH_TO_MKS_SCALE_FACTOR, wmax[1]*LENGTH_TO_MKS_SCALE_FACTOR, wmax[2]*LENGTH_TO_MKS_SCALE_FACTOR);

  int n_points[3], min_plane[3];
  ccDOTIMES(i, 3) {
    n_points[i] = (int)((wmax[i] - wmin[i])/emission_points.m_release_spacing[i]) + 2;
    min_plane[i] = (int)(wmin[i]/emission_points.m_release_spacing[i]) - 1;
  }

  dFLOAT pt[3];
  ccDOTIMES(i, n_points[0]) {
    ccDOTIMES(j, n_points[1]) {
      ccDOTIMES(k, n_points[2]) {
        //pt is in emission sys
        pt[0] = (min_plane[0] + i) * emission_points.m_release_spacing[0];
        pt[1] = (min_plane[1] + j) * emission_points.m_release_spacing[1];
        pt[2] = (min_plane[2] + k) * emission_points.m_release_spacing[2];

        //transform from emission sys to cylinder sys
        xform_point_inv(pt, &cylinder_and_emi);

        if (pt[0] < 0.0 || pt[0] > h)
          continue;
        if (std::sqrt(pt[1] * pt[1] + pt[2] * pt[2]) > r1 * (1.0 - pt[0] / h) + r2 * pt[0] / h)
          continue;

        //transform from box to default sys
        xform_point(pt, &cylinder_and_default);
        //translate from the default sys to the lattice sys
        ccDOTIMES(dim, 3) {
          pt[dim] += sim.case_origin[dim];
        }

        FIXED_EMISSION_LOCATION emi_pt = xnew sFIXED_EMISSION_LOCATION;
        vcopy(emi_pt->m_point, pt);
        emi_pt->m_ublk = m_ublk_octree->find(pt[0], pt[1], pt[2], emi_pt->m_voxel);

        emission_points.m_points.push_back(emi_pt);
      }
    }
  }

  if(emission_points.m_points.size() == 0)
    msg_internal_error("No emission points with the requested spacing were found inside the emission cylinder.");
}

VOID sEMISSION_CYLINDER::precompute_geometry_chars() {

  //Create the body_csys which is used to convert coordinates between the cylinder's body aligned csys and the csys the geometry was described in.
  sG3_VEC cylinder_major_axis_in_cylinder_csys = {1.0 , 0.0, 0.0};  //By Convenction, e_1 is defined as the major axis of the cylinder
  sG3_VEC cylinder_major_axis, origin;
  sG3_XFORM rotation, translation;

  vcopy(cylinder_major_axis.vcoord, m_cylinder_axis);
  sG3_VEC Omega = g3_vec_cross(cylinder_major_axis_in_cylinder_csys, cylinder_major_axis);
  sPARTICLE_VAR sin_omega = std::sqrt(g3_vec_dot(Omega, Omega)); //the sine of the angle between the two vectors

  if(sin_omega < 1.0e-6)
    {
      rotation = g3_xform_make_unit();
    }
  else
    {
      sPARTICLE_VAR omega = asin(sin_omega);
      Omega = g3_vec_divide(Omega , sin_omega); //unitize to get the axis of rotation

      //Make a transformation that rotates one vector to the other.
      //Ignore what happens to the minor axes since the cylinder is symetric.
      rotation = g3_xform_make_rotate(Omega, omega);
    }

  vcopy(origin.vcoord, m_centers[0]); //By convention, the cylinder body aligned csys origin is at the first face center.
  translation = g3_xform_make_shift(origin);

  m_body_csys.l_to_g_xform = g3_xform_composition(rotation, translation);

#if 0
  //check which csys m_centers is in
  printf("center0 %15.8e %15.8e %15.8e\n",
         m_centers[0][0] * LENGTH_TO_MKS_SCALE_FACTOR,
         m_centers[0][1] * LENGTH_TO_MKS_SCALE_FACTOR,
         m_centers[0][2] * LENGTH_TO_MKS_SCALE_FACTOR);

  printf("center1 %15.8e %15.8e %15.8e\n",
         m_centers[1][0] * LENGTH_TO_MKS_SCALE_FACTOR,
         m_centers[1][1] * LENGTH_TO_MKS_SCALE_FACTOR,
         m_centers[1][2] * LENGTH_TO_MKS_SCALE_FACTOR);
#endif

  //Also compute the inverse transformation.
  rotation = g3_xform_transpose_rotation_matrix(rotation);
  origin = g3_xform_vec(origin, rotation);
  origin = g3_vec_negate(origin);
  translation = g3_xform_make_shift(origin);
  m_body_csys.g_to_l_xform = g3_xform_composition(rotation, translation);


#if 0
  ccDOTIMES(i, 4) {
    printf("xform0 %15.8e  %15.8e  %15.8e\n",
           m_body_csys.l_to_g_xform.xcoord[i][0], m_body_csys.l_to_g_xform.xcoord[i][1], m_body_csys.l_to_g_xform.xcoord[i][2]);
  }

  ccDOTIMES(i, 4) {
    printf("xform1 %15.8e  %15.8e  %15.8e\n",
           m_body_csys.g_to_l_xform.xcoord[i][0], m_body_csys.g_to_l_xform.xcoord[i][1], m_body_csys.g_to_l_xform.xcoord[i][2]);
  }
#endif

  std::string csys_name = "cylinder_body_csys";
  m_body_csys.name = xnew char[ csys_name.size() + 1 ];
  m_body_csys.name[csys_name.size()] = '\0';
  memcpy(m_body_csys.name, csys_name.c_str(), csys_name.size() + 1);

  //Build an octree for this emission geometry:
  m_ublk_octree = xnew sSUB_OCTREE;
  m_ublk_octree->init();
  asINT32 num_added = 0;

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (ublk_touches_emission_geometry(farblk) && farblk->lrf_physics_descriptor() == nullptr) {
        m_ublk_octree->register_ublk(farblk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale){
      if (ublk_touches_emission_geometry(nearblk) && nearblk->lrf_physics_descriptor() == nullptr) {
        m_ublk_octree->register_ublk(nearblk);
      }
    }
  }
  m_ublk_octree->build_octree();
  init_fixed_emission_points();
}


VOID sEMISSION_SURFACE::precompute_geometry_chars() {

  ccDOTIMES(facet_num, m_facets.size()) {
    std::vector<sG3_POINT> &m_vertices = m_facets[facet_num].m_vertices; //vertices of a facet
    int nv = m_vertices.size();
    //refer to g3_point_array_area
    sG3_VEC area_vec = g3_vec_null();

    ccDOTIMES(j, nv) {
      sG3_POINT &p0 = m_vertices[j];

      //bounding box
      ccDOTIMES(k, 3) {
        if(facet_num == 0 && j == 0) {
          m_bounding_box[0][k] = p0.pcoord[k];
          m_bounding_box[1][k] = p0.pcoord[k];
        }
        else {
          m_bounding_box[0][k] = std::min(m_bounding_box[0][k], p0.pcoord[k]);
          m_bounding_box[1][k] = std::max(m_bounding_box[1][k], p0.pcoord[k]);
        }
      } //k dim

        //normal and area  WARNING: assume all points coplanar but not colinear
      sG3_POINT &p1 = m_vertices[(j+1)%nv];
      sG3_VEC contribution = g3_point_area_contribution(p0, p1);
      area_vec = g3_vec_sum(area_vec, contribution);
    } //j # vertices

    dFLOAT area = g3_vec_length(area_vec);
    m_facets[facet_num].m_area = area;
    m_facets[facet_num].m_weight = facet_num == 0 ? area : m_facets[facet_num-1].m_weight + area;
    area_vec = g3_vec_normalize(area_vec);
    m_facets[facet_num].m_normal = area_vec;

    //compute centroid
    sG3_POINT &m_centroid = m_facets[facet_num].m_centroid;
    m_centroid = g3_point_null();

    ccDOTIMES(j, nv) {
      sG3_POINT &p0 = m_vertices[j];

      m_centroid = g3_point_sum(m_centroid, p0);
    } //j # vertices

      //use a simple way to compute centroid
    ccDOTIMES(k, 3) {
      m_centroid.pcoord[k] /= nv;
    } //k dim

  } //facet_num

  sdFLOAT max_weight = m_facets[m_facets.size()-1].m_weight;
  ccDOTIMES(facet_num, m_facets.size()) {
    m_facets[facet_num].m_weight /= max_weight;
  }

  m_ublk_octree = xnew sSUB_OCTREE;
  m_ublk_octree->init();
  
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      if (ublk_touches_emission_geometry(farblk)) {
        m_ublk_octree->register_ublk(farblk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale){
      if (ublk_touches_emission_geometry(nearblk)) {
        m_ublk_octree->register_ublk(nearblk);
      }
    }
  }
  m_ublk_octree->build_octree();
}


VOID sEMISSION_GEOMETRY::transform_position_from_geometry_to_lattice_csys(sPARTICLE_VAR position[3])
{
  sCSYS &csys = sim.csys_table[m_csys_id];
  sG3_POINT position_in_body_csys;
  vcopy(position_in_body_csys.pcoord, position);

  //Convert the position from the geometry's csys to the default csys.
  sG3_POINT transformed_position  = g3_xform_point(position_in_body_csys, csys.l_to_g_xform);
  vcopy(position, transformed_position.pcoord);

  //Lastly, transform the position from the default csys to the lattice csys.
  vinc(position, sim.case_origin);

}

VOID sEMISSION_GEOMETRY::transform_position_from_lattice_to_geometry_csys(sPARTICLE_VAR position[3])
{

  sCSYS &csys = sim.csys_table[m_csys_id];
  sG3_POINT position_in_default_csys;

  //First convert from the lattice csys to the default csys
  vsub(position_in_default_csys.pcoord, position, sim.case_origin);

  //Lastly, convert the position from the default csys to the geometry's csys.
  sG3_POINT transformed_position = g3_xform_point(position_in_default_csys, csys.g_to_l_xform);
  vcopy(position, transformed_position.pcoord);
}

BOOLEAN sEMISSION_POINTS::ublk_touches_emission_geometry(UBLK ublk) {
  dFLOAT location[3];
  dFLOAT voxel_size = scale_to_voxel_size(ublk->scale());

  ccDOTIMES(dim,3) {
    location[dim] = ublk->location(dim);// + voxel_size;
  }

  ccDOTIMES(nth_point, m_points.size()/3) {
    dFLOAT pos[3];
    pos[0] = m_points[nth_point * 3];
    pos[1] = m_points[nth_point * 3 + 1];
    pos[2] = m_points[nth_point * 3 + 2];

    int dim;
    for(dim = 0; dim < 3; dim++) {
      if (pos[dim] < location[dim] || pos[dim] >= location[dim] + voxel_size * 2)
        break;
    }
    if (dim == 3) 
      return TRUE;
  }
  return FALSE;
}

BOOLEAN sEMISSION_BOX::ublk_touches_emission_geometry(UBLK ublk) {
  //Get the location of ublk center.
  dFLOAT location[3];
  dFLOAT ublk_size = scale_to_voxel_size(ublk->scale()) * 2.0;

  ccDOTIMES(dim,3) {
    location[dim] = ublk->location(dim);// + voxel_size;
  }

  //Convert the ublk location from the lattice csys to the box's body aligned csys.
  transform_position_from_lattice_to_geometry_csys(location);

  //check if the ublk location is within each dimension of the box extended by sqrt(3) * voxel_size;
  dFLOAT *min = m_corners[0];
  dFLOAT *max = m_corners[1];
  dFLOAT offset_distance =  ublk_size * std::sqrt(3);
  ccDOTIMES(dim,3) {
    if ((location[dim] + ublk_size) < (min[dim] - offset_distance)) // check right corner
      return(FALSE);
    if (location[dim] > (max[dim] + offset_distance))               // check left corner
      return(FALSE);
  }
  return TRUE;
}


VOID sEMISSION_CYLINDER::transform_position_from_geometry_to_lattice_csys(sPARTICLE_VAR position[3]) {
  //This method is specailized for cylinders because the cylinder body csys isn't
  //necessecarily aligned with the csys the geometry is described in.

  //First convert the position from the cylinder's body csys to the csys the geometry was provided in
  sG3_POINT position_in_body_csys;
  vcopy(position_in_body_csys.pcoord, position);
  sG3_POINT position_in_geometry_csys  = g3_xform_point(position_in_body_csys, m_body_csys.l_to_g_xform);
  //position_in_geometry_csys is in default
  vcopy(position, position_in_geometry_csys.pcoord);
  //Lastly, transform the position from the default csys to the lattice csys
  vinc(position, sim.case_origin);
}

VOID sEMISSION_CYLINDER::transform_position_from_lattice_to_geometry_csys(sPARTICLE_VAR position[3]) {
  sCSYS &csys = sim.csys_table[m_csys_id];
  sG3_POINT position_in_default_csys;

  //First convert from the lattice csys to the defualt csys
  vsub(position_in_default_csys.pcoord, position, sim.case_origin);
  sG3_POINT transformed_position =  g3_xform_point(position_in_default_csys, m_body_csys.g_to_l_xform);

  vcopy(position, transformed_position.pcoord);
}


BOOLEAN sEMISSION_CYLINDER::ublk_touches_emission_geometry(UBLK ublk) {
  dFLOAT location[3];
  dFLOAT voxel_size = scale_to_voxel_size(ublk->scale());
  //Get the location of ublk center.
  ccDOTIMES(dim,3) {
    location[dim] = ublk->location(dim) + voxel_size;
  }

  //Convert the ublk's center position from the lattice csys to the cylinder's body aligned csys.
  transform_position_from_lattice_to_geometry_csys(location);

  //Check if the position is within an offset_distance of the cylinder boundary.
  dFLOAT offset_distance = voxel_size * 2 * sqrt(3);
  if ( ( location[0] < -offset_distance                    ) ||
       ( location[0] > m_cylinder_length + offset_distance )   )
    return(FALSE);

  //Check that the postition is within an offset distance of the local radius of the cylinder.
  dFLOAT local_radius = m_radii[0] + (m_radii[1] - m_radii[0]) * location[0] / m_cylinder_length ;
  dFLOAT radius =  std::sqrt(location[1] * location[1] + location[2] * location[2]);
  if( radius > local_radius + offset_distance)
    return(FALSE);
  return(TRUE);
}

BOOLEAN sEMISSION_SURFACE::ublk_touches_emission_geometry(UBLK ublk) {
  dFLOAT location[3];
  dFLOAT voxel_size = scale_to_voxel_size(ublk->scale());
  dFLOAT offset_distance = 2 * voxel_size; //voxel_size * 2 * std::sqrt(3);
  dFLOAT *min = m_bounding_box[0];
  dFLOAT *max = m_bounding_box[1];
  //get location of ublk center
  ccDOTIMES(dim,3) {
    location[dim] = ublk->location(dim) + voxel_size;
  }

  ccDOTIMES(dim,3) {
    if(location[dim] + offset_distance < min[dim] || location[dim] - offset_distance > max[dim]) return(FALSE);
  }
  return(TRUE);
}


