/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1995-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */

/*--------------------------------------------------------------------------*
 * Fluid voxel dynamics attributes
 *
 * Dalon Work, Exa Corporation
 * Created Sep 22, 2017
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_BSURFEL_BODY_FORCE_H_
#define _SIMENG_BSURFEL_BODY_FORCE_H_

template<size_t N_VOXELS>
struct tBSURFEL_BODY_FORCE_COMM {
  STP_PHYS_VARIABLE m_body_force[3][N_VOXELS];
#if BUILD_D19_LATTICE
  STP_PHYS_VARIABLE m_body_force_pfld[3][N_VOXELS];
#endif
  STP_PHYS_VARIABLE m_weight[N_VOXELS];
  tBITSET<N_VOXELS> m_is_ib_interior;
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tBSURFEL_BODY_FORCE {
#if EXA_USE_SSE
  static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
  static const size_t ALIGNMENT = 32;
#endif

  EXTRACT_UBLK_TRAITS

  using sBSURFEL_BODY_FORCE_COMM = tBSURFEL_BODY_FORCE_COMM<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT m_body_force[3];
  
#if BUILD_D19_LATTICE
  ALIGNED_UBFLOAT m_body_force_pfld[3];
#endif
  ALIGNED_UBFLOAT m_weight;
  VOXEL_MASK m_is_ib_interior;

  tBSURFEL_BODY_FORCE()
  {
    reset();
  }

  VOID add_bsurfel_contribution(uINT8 voxel, dFLOAT weight, dFLOAT body_force[3], dFLOAT body_force_pfld[3], STP_DGEOM_VARIABLE area)  //PF
  {
    dFLOAT w2 = weight * weight;
    dFLOAT area_used = 1.0;
#if BUILD_D19_LATTICE
    if (sim.is_pf_model)
      area_used = area;
#endif
    ccDOTIMES(axis, 3) {
      m_body_force[axis][voxel] += w2 * body_force[axis] * area_used;
#if BUILD_D19_LATTICE
      m_body_force_pfld[axis][voxel] += w2 * body_force_pfld[axis] * area_used;
#endif
    }
    m_weight[voxel] += weight * area_used;
  }

  VOID set_ib_interior(uINT8 voxel)
  {
    m_is_ib_interior.set(voxel);
  }

  INLINE VOID reset()
  {
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      m_weight[voxel] = 0;
      m_body_force[0][voxel] = 0;
      m_body_force[1][voxel] = 0;
      m_body_force[2][voxel] = 0;
#if BUILD_D19_LATTICE
      m_body_force_pfld[0][voxel] = 0;
      m_body_force_pfld[1][voxel] = 0;
      m_body_force_pfld[2][voxel] = 0;
#endif
    }
    m_is_ib_interior.reset_all();
  }

  INLINE __HOST__DEVICE__ VOXEL_MASK get_ib_interior() { return m_is_ib_interior; }

  static VOID add_send_size(asINT32 &send_size)
  {
    send_size += sizeof(sBSURFEL_BODY_FORCE_COMM) / sizeof(sdFLOAT);
  }

  void fill_send_buffer(sdFLOAT* &send_buffer)
  {
    sBSURFEL_BODY_FORCE_COMM* field = reinterpret_cast<sBSURFEL_BODY_FORCE_COMM*>(send_buffer);
    memcpy(field->m_body_force, m_body_force, sizeof(field->m_body_force));
#if BUILD_D19_LATTICE
    memcpy(field->m_body_force_pfld, m_body_force_pfld, sizeof(field->m_body_force_pfld));
#endif
    memcpy(field->m_weight,     m_weight,     sizeof(field->m_weight));
    field->m_is_ib_interior = m_is_ib_interior;
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  void expand_recv_buffer(sdFLOAT* &recv_buffer)
  {
    sBSURFEL_BODY_FORCE_COMM* field = reinterpret_cast<sBSURFEL_BODY_FORCE_COMM*>(recv_buffer);
    ccDOTIMES(i, 3) {
      ccDOTIMES(v, N_VOXELS) {
        m_body_force[i][v] += field->m_body_force[i][v];
#if BUILD_D19_LATTICE
	m_body_force_pfld[i][v] += field->m_body_force_pfld[i][v];
#endif
      }
    }
    ccDOTIMES(v, N_VOXELS) {
      m_weight[v] += field->m_weight[v];
    }
    m_is_ib_interior |= field->m_is_ib_interior;
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID print_voxel_data(std::ostream& os, asINT32 print_voxel)
  {
    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_IB_BF");
    sim_print<3, N_VOXELS>(os, "body_force", m_body_force, loop_limits(0, 2), print_voxel);
    sim_print<N_VOXELS>(os, "weight", m_weight, print_voxel);
    sim_print<bool>(os, "is_interior", m_is_ib_interior.test(print_voxel));
    os << PRINT_OPTS::VS;
  }

  friend std::ostream& operator << (std::ostream& os, const tBSURFEL_BODY_FORCE& bf)
  {
    os << "w : ";
    for (int i = 0; i < N_VOXELS; i++) {
      os << fmt::binary(bf->m_weight( i )) << ' ';
    }
    os << "\nfx: ";
    for (int i = 0; i < N_VOXELS; i++) {
      os << fmt::binary(bf->m_body_force[0](i)) << ' ';
    }
    os << "\nfy: ";
    for (int i = 0; i < N_VOXELS; i++) {
      os << fmt::binary(bf->m_body_force[1](i)) << ' ';
    }
    os << "\nfz: ";
    for (int i = 0; i < N_VOXELS; i++) {
      os << fmt::binary(bf->m_body_force[2](i)) << ' ';
    }
    os << "\nib: ";
    for (int i = 0; i < N_VOXELS; i++) {
      os << bf->m_is_ib_interior.test(i) << ' ';
    }
    os << "\n";

    return os;
  }

#undef ALIGNED_UBFLOAT
};

typedef tBSURFEL_BODY_FORCE<UBLK_SDFLOAT_TYPE_TAG>  sBSURFEL_BODY_FORCE_8;
typedef tBSURFEL_BODY_FORCE<UBLK_UBFLOAT_TYPE_TAG>            sBSURFEL_UBFLOAT_BODY_FORCE_8;

#ifdef BUILD_GPU
typedef tBSURFEL_BODY_FORCE<MBLK_SDFLOAT_TYPE_TAG> sBMSFL_BODY_FORCE;
typedef tBSURFEL_BODY_FORCE<MBLK_UBFLOAT_TYPE_TAG> sBMSFL_UBFLOAT_BODY_FORCE;
#endif

#endif
