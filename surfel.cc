/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "surfel.h"
#include "sim.h"
#include "mirror.h"
#include "slrf.h"
#include "mlrf.h"
#include "comm_utils.h"
#include PHYSICS_H

asINT32 g_v2s_data_offset_table[as_int(V2S_DATA_TYPES::N_TYPES) + 1];
#if BUILD_GPU
asINT32 g_v2s_data_offset_table_64[as_int(V2S_DATA_TYPES::N_TYPES) + 1];
#endif

template<typename SFL_TYPE_TAG>
__HOST__ size_t tSURFEL_V2S_DATA_MEM_POOL<SFL_TYPE_TAG>::size_of_allocated_elems(size_t n_elems) {
  return tSURFEL_V2S_DATA<SFL_TYPE_TAG>::size() * n_elems;
}

void allocate_host_v2s_buffer() {
  sSURFEL_V2S_DATA_MEM_POOL::get_instance().allocate_pool(sSURFEL_V2S_DATA_MEM_POOL::N_DATASETS);
}

template<typename SFL_TYPE_TAG>
void tSURFEL_V2S_DATA<SFL_TYPE_TAG>::build_offset_table() {

  auto& v2s_data_offset_table = SFL_TYPE_TAG::is_msfl()? g_v2s_data_offset_table_64 : g_v2s_data_offset_table;
  ccDOTIMES(i, as_int(V2S_DATA_TYPES::N_TYPES)) {
    v2s_data_offset_table[i] = -1;
  }
  
  size_t table_offet = 0;
  constexpr auto lb_index = as_int(V2S_DATA_TYPES::LB);
  constexpr auto turb_index = as_int(V2S_DATA_TYPES::TURB);
  constexpr auto temp_index = as_int(V2S_DATA_TYPES::TEMP);
  constexpr auto uds_index = as_int(V2S_DATA_TYPES::UDS);
  constexpr auto pf_index = as_int(V2S_DATA_TYPES::PF);

  static_assert(sizeof(tSURFEL_V2S_DATA<SFL_TYPE_TAG>) == 1,
                "Offset table initialization assumes tSURFEL_V2S_DATA is empty");
  
  size_t element_size = 0;
  
  v2s_data_offset_table[lb_index] = 0;

  size_t alignment = get_alignment_of<tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>>();
  
  if (sim.is_lb_model) {
    element_size = v2s_data_offset_table[lb_index] + sizeof(tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>);
  }

  if (sim.is_turb_model) {
    alignment = get_alignment_of<tSURFEL_V2S_TURB_DATA<SFL_TYPE_TAG>>();
    v2s_data_offset_table[turb_index] = get_byte_aligned(element_size, alignment);
    element_size = v2s_data_offset_table[turb_index] + sizeof(tSURFEL_V2S_TURB_DATA<SFL_TYPE_TAG>);
  }

  if (sim.is_heat_transfer) {
    alignment = get_alignment_of<tSURFEL_V2S_T_DATA<SFL_TYPE_TAG>>();
    v2s_data_offset_table[temp_index] = get_byte_aligned(element_size, alignment);
    element_size = v2s_data_offset_table[temp_index] + sizeof(tSURFEL_V2S_T_DATA<SFL_TYPE_TAG>);
  }    
  
  if (sim.n_user_defined_scalars) {
    alignment = get_alignment_of<tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>>();
    v2s_data_offset_table[uds_index] = get_byte_aligned(element_size, alignment);
    element_size = v2s_data_offset_table[uds_index] + sim.n_user_defined_scalars * sizeof(tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>);

#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      alignment = get_alignment_of<tSURFEL_V2S_PF_DATA<SFL_TYPE_TAG>>();
      v2s_data_offset_table[pf_index] = get_byte_aligned(element_size, alignment);
      element_size = v2s_data_offset_table[pf_index] + sizeof(tSURFEL_V2S_PF_DATA<SFL_TYPE_TAG>);
    }
#endif
  }

  //We want every new element to be aligned to boundary of V2S_DATA (V2S_LB_DATA)
  alignment = std::max(size_t(8), get_alignment_of<tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>>());
  v2s_data_offset_table[as_int(V2S_DATA_TYPES::N_TYPES)] = get_byte_aligned(element_size, alignment);
}

template<typename SFL_TYPE_TAG>
void tSURFEL_V2S_DATA_MEM_POOL<SFL_TYPE_TAG>::allocate_pool(size_t n_elems) {
  tSURFEL_V2S_DATA<SFL_TYPE_TAG>::build_offset_table();
  m_n_elems = n_elems;
  size_t alignment = std::max(size_t(8), get_alignment_of<tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>>());
  m_buff = new (std::align_val_t(alignment)) std::byte[size_of_allocated_elems(n_elems)];
}

template<typename SFL_TYPE_TAG>
void tSURFEL_V2S_DATA_MEM_POOL<SFL_TYPE_TAG>::clear(size_t n_elems) {
  cassert(n_elems <= m_n_elems);
  memset(this->m_buff, 0, size_of_allocated_elems(n_elems));
}

template<typename SFL_TYPE_TAG>
__HOST__ VOID tSURFEL_V2S_DATA<SFL_TYPE_TAG>::clear() {
  static_assert(sizeof(tSURFEL_V2S_DATA<SFL_TYPE_TAG>) == 1,
                "This clearing logic assumes that V2S_DATA is empty");
  memset(this, 0, size());
}

template class tSURFEL_V2S_DATA_MEM_POOL<SFL_SDFLOAT_TYPE_TAG>;
template class tSURFEL_V2S_DATA<SFL_SDFLOAT_TYPE_TAG>;

#if BUILD_GPU
template class tSURFEL_V2S_DATA_MEM_POOL<MSFL_SDFLOAT_TYPE_TAG>;
template class tSURFEL_V2S_DATA<MSFL_SDFLOAT_TYPE_TAG>;
#endif

sINT32 surfel_base_size = 0;
sINT32 surfel_base_ckpt_size = 0;

// These 2 variables will be used in the future if any of the types derived from
// sSURFEL_BASE actually add extra data fields. See ublk.h to see how this is used.
static sINT16 surfel_base_max_alignment   = 0;
static sINT16 surfel_base_size_before_pad = 0;

template<>
sSURFEL_MEAS_CELL_PTR *sSURFEL::create_meas_cell_ptrs(
    tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data, asINT32 n_meas_windows) {

	return dyn_data->allocate_meas_cell_ptrs(n_meas_windows);
}

template<>
sSURFEL_MEAS_CELL_PTR *sSURFEL::add_meas_cell_ptr(
    tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data) {

	return dyn_data->add_meas_cell_ptr();
}

template<>
VOID sSURFEL::fill_dynamics_data(DGF_SURFEL_DESC surfel_desc,
                                 STP_PHYSTYPE_TYPE sim_phys_type,
                                 sPHYSICS_DESCRIPTOR *surface_phys_desc,
                                 dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                                 cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data)
{
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data = dynamics_data();
  initialize_dynamics_data(dyn_data, sim_phys_type, this, surface_phys_desc,
                           sum_pgram_volumes, seed_from_meas_data);
}

template<>
VOID sSURFEL::fill_mirror_data(sSURFEL *mirror_surfel, STP_DIRECTION direction,
                                    STP_UNIVERSAL_LATVEC_MASK latvec_mask) {
  sSURFEL_MIRROR_DATA *mirror_data        = this->mirror_data();
  if (mirror_data->m_mirror_surfel == NULL) {
    mirror_data->m_mirror_surfel        = mirror_surfel;
    mirror_data->m_surfel_mirror_config = find_mirror_surfel_config(direction);
    mirror_data->m_latvec_state_mask    = latvec_mask;
    mirror_data->m_next_mirror_data     = NULL;
  } else {
    //printf("multiple mirror surfels %d of a real surfel %d not implemented yet\n",
    //                   mirror_surfel->id(), id());
    while (mirror_data->m_next_mirror_data) {
      //printf("found second mirror surfel\n");
      mirror_data = mirror_data->m_next_mirror_data;
    }
    mirror_data->m_next_mirror_data = new sSURFEL_MIRROR_DATA;
    sSURFEL_MIRROR_DATA *new_mirror_data    = mirror_data->m_next_mirror_data;
    new_mirror_data->m_mirror_surfel        = mirror_surfel;
    new_mirror_data->m_surfel_mirror_config = find_mirror_surfel_config(direction);
    new_mirror_data->m_latvec_state_mask    = latvec_mask;
    new_mirror_data->m_next_mirror_data     = NULL;
  }

}

template<>
VOID sSURFEL::set_clone_surfel_index(uINT32 clone_surfel_index) {

  even_odd_data()->m_clone_surfel.set_clone_surfel_id(clone_surfel_index);
}


template<>
sSURFEL *sSURFEL::clone_surfel() {
  if (is_even_or_odd())
    return even_odd_data()->m_clone_surfel.clone_surfel();
  else
    return nullptr;
}

static VOID assign_surfel_meas_cell_ptrs(SURFEL surfel, asINT16 n_meas_cells, sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptr,
                                         BOOLEAN is_surfel_moving, BOOLEAN is_even) {
  ccDOTIMES(w, n_meas_cells) {
    MEAS_WINDOW window = g_meas_windows[surfel_meas_cell_ptr->window_index()];
    STP_MEAS_CELL_INDEX meas_cell_index =
            window->m_meas_cell_index_map[surfel_meas_cell_ptr->index()].m_new_sp_cell_index;
    // for a dev window and a moving surfel, leave meas_cell_ptr as an index with an axis
    if (window->is_development && is_surfel_moving) {
      asINT32 axis = surfel_meas_cell_ptr->axis();
      asINT32 face_index = surfel_meas_cell_ptr->face();
      surfel_meas_cell_ptr->set_index(meas_cell_index - window->entity_first_segment[axis][face_index]);
      surfel_meas_cell_ptr->mark_should_rotate_vector_to_grf();
    } else {
      MEAS_CELL_VAR *meas_cell = window->meas_cell(meas_cell_index);
      surfel_meas_cell_ptr->set_variables(meas_cell);
// Counting only odd surfels as the number of shobs contributing to a meas
// cell. Resetting of accumulated value should happen at every even and odd timestep,
// for correct calculation of standar deviation. 
      if (window->contains_std_dev_vars && !is_even) {
        sSTD_CELL_VAR *std_cell = surfel_meas_cell_ptr->std_cell_ptr(window->n_variables);
        std_cell->add_shob();
      }
      if (is_surfel_moving) {
        if (!window->is_output_in_local_csys) {
          surfel_meas_cell_ptr->mark_should_rotate_vector_to_grf();
        } else if (window->is_composite) {
          if (!window->meas_cell_output_in_local_csys[meas_cell_index])
            surfel_meas_cell_ptr->mark_should_rotate_vector_to_grf();
        }
      }
    }
    surfel_meas_cell_ptr++;
  }
}

template<>
VOID sSURFEL::resolve_meas_cell_ptrs() {
  SURFEL_DYNAMICS_DATA surfel_dyn_data = dynamics_data();
  asINT16 n_meas_cells = surfel_dyn_data->n_meas_cell_ptrs();
  sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptr = surfel_dyn_data->surfel_meas_cell_ptrs();
  assign_surfel_meas_cell_ptrs(this, n_meas_cells, surfel_meas_cell_ptr, is_surfel_moving(), is_even());
}

template<typename SFL_TYPE_TAG>
VOID seed_copy_even_to_odd(tSURFEL<SFL_TYPE_TAG>* even_surfel,
                           asINT32 esoxor,
                           tSURFEL<SFL_TYPE_TAG>* odd_surfel,
                           asINT32 osoxor,
                           BOOLEAN is_full_checkpoint_restore, 
                           BOOLEAN is_mirror);

template<>
VOID sSURFEL::seed_copy_even_to_odd(SURFEL even_surfel,
                                    BOOLEAN is_full_checkpoint_restore, 
                                    BOOLEAN is_mirror) {
  ::seed_copy_even_to_odd(even_surfel, 0 /*esoxor*/,
                          this /*odd_surfel*/, 0/*osoxor*/,
                          is_full_checkpoint_restore, is_mirror);
    if (sim.is_conduction_model && even_surfel->is_conduction_surface()) {
      conduction_data()->seed_copy_even_to_odd(even_surfel->conduction_data());
    }
}


VOID find_ublk_interactions(REALM realm,
                            DGF_SURFEL_DESC surfel_desc, 
                            uINT16 *p_n_ublk_interactions,
                            sSURFEL_UBLK_INTERACTION **p_ublk_interactions,
                            BOOLEAN is_ghost)
{
  cDGF_SURFEL &surfel = surfel_desc->s;  

  // For ghost surfels, the number of interacting ublks may be fewer than
  // indicated by the descriptor.
  asINT32 n_interacting_ublks_from_desc = surfel.num_interacting_ublks;

  static std::vector <asINT32> total_ublk_weights;
  if (n_interacting_ublks_from_desc > total_ublk_weights.size() ) {
    total_ublk_weights.resize(n_interacting_ublks_from_desc);
  }

  cDGF_SURFEL_UBLK_WEIGHT_SET_DESC *s_ublk_weight_set_desc =
              &surfel_desc->surfel_ublk_weight_sets[0];

  asINT32 n_bytes_for_ublk_interactions = 0;


  STP_SCALE surfel_scale = surfel.surfel_scale;
  
  STP_EVEN_ODD even_odd = surfel.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK;
  BOOLEAN is_even = (even_odd == STP_PROCESS_ON_EVEN_TIMES); 
  BOOLEAN is_odd  = (even_odd == STP_PROCESS_ON_ODD_TIMES); 

  asINT32 n_interacting_ublks = 0;

  ccDOTIMES( iu, n_interacting_ublks_from_desc) {
    if (is_ghost) {
      // Ignore interactions with ublks that live on another SP
      SHOB_ID ublk_id = s_ublk_weight_set_desc->w.src_ublk_id;
      UBLK interacting_ublk = ublk_from_id(ublk_id, realm);
      if((interacting_ublk == NULL) || (interacting_ublk->is_ghost())) {
        s_ublk_weight_set_desc++;
        continue;
      }
    }
    total_ublk_weights[iu] = 0;
    asINT32 n_s_v_weight_sets = s_ublk_weight_set_desc->w.num_s_v_weight_sets;
    cDGF_SURFEL_VOXEL_WEIGHT_SET_DESC *s_v_weight_set = &s_ublk_weight_set_desc->surfel_voxel_weight_sets[0];

    ccDOTIMES (iv, n_s_v_weight_sets) {
      STP_LATVEC_MASK_WORD lv_mask = s_v_weight_set->w.latvec_mask;

      while (lv_mask)  {
        total_ublk_weights[iu] += (lv_mask & 1);
        lv_mask = lv_mask >> 1;
      }

      s_v_weight_set++;
    }
    n_bytes_for_ublk_interactions +=
      sSURFEL_UBLK_INTERACTION::size(total_ublk_weights[iu], n_s_v_weight_sets);

    s_ublk_weight_set_desc++;
    n_interacting_ublks++;
  }
  //msg_print("Surfel %d: n_bytes_for_ublk_interactions is %d",surfel.surfel_id, n_bytes_for_ublk_interactions);
  *p_n_ublk_interactions = n_interacting_ublks;

  *p_ublk_interactions = (SURFEL_UBLK_INTERACTION)
                                new char[n_bytes_for_ublk_interactions];

  SURFEL_UBLK_INTERACTION ublk_interaction = *p_ublk_interactions;
  char *base_ptr = (char *) ublk_interaction;
  s_ublk_weight_set_desc = &surfel_desc->surfel_ublk_weight_sets[0];

  ccDOTIMES( iu, n_interacting_ublks_from_desc) {

    UBLK interacting_ublk = ublk_from_id(s_ublk_weight_set_desc->w.src_ublk_id, realm);

    if(is_ghost) {
      if((interacting_ublk == NULL) || (interacting_ublk->is_ghost())) {
        //msg_print("  Skipping an interaction; Surfel ID = %d, Ublk ID = %d",surfel.surfel_id, ublk_interaction->m_ublk_id);
        s_ublk_weight_set_desc++;
        continue;
      }
    }

    asINT32 voxel_size_for_approximate_weights = scale_to_voxel_size(surfel_scale);
    sdFLOAT pfluid_scaling = 1.0;

    /** Use coarse scale for even odd surfels */
    if (is_even || is_odd) {
      if (g_pfc_even_odd_surfel_v2s_use_vr_fine == 0) {
        voxel_size_for_approximate_weights *= 2;
      /** If UBLK is same scale as this even odd surfel (i.e., fine or VR-fine) */
      if (surfel_scale == interacting_ublk->scale())
        pfluid_scaling /= (1 << sim.num_dims);
      } else if (g_pfc_even_odd_surfel_v2s_use_vr_fine == 1) {
        if (surfel_scale > interacting_ublk->scale()) {
          cassert(surfel_scale - interacting_ublk->scale() == 1);
          pfluid_scaling *= (1 << sim.num_dims);
        }
      } else {
        msg_internal_error("Invalid value for parameter pfc_even_odd_surfel_v2s_use_vr_fine = %d",
            g_pfc_even_odd_surfel_v2s_use_vr_fine);
      }
    }

    //msg_print("  Including an interaction; Surfel ID = %d, Ublk ID = %d",surfel.surfel_id, ublk_interaction->m_ublk_id);
    ublk_interaction->set_ublk_id(s_ublk_weight_set_desc->w.src_ublk_id);
    asINT32 n_s_v_weight_sets =
        s_ublk_weight_set_desc->w.num_s_v_weight_sets;

    ublk_interaction->m_n_weight_sets = n_s_v_weight_sets;
    ublk_interaction->m_total_weights = total_ublk_weights[iu];

#if 0
// #ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
    asINT32 interacting_ublk_region = interacting_ublk->get_ublk_box()->m_isolated_domain;
    // Following is a rather convoluted check. Two conditions are flagged as errors:
    // (1) A conduction UBLK interacts with a surfel of different fluid region index
    // (2) A non-conduction UBLK interacts with a conduction surfel determined using fluid region index
    if ((interacting_ublk->is_conduction_solid()
          && surfel.fluid_region_index != interacting_ublk_region)
        || (!interacting_ublk->is_conduction_solid()
          && (surfel.fluid_region_index != -1
            && is_pd_conduction_solid(volume_physics_desc_from_part_index(surfel.fluid_region_index)))))
      msg_internal_error("Surfel %d of region %d is set-up to interact with UBLK %d of region %d",
          surfel.surfel_id, surfel.fluid_region_index,
          interacting_ublk->id(), interacting_ublk_region);
#endif

    VOXEL_MASK_8 voxels_advect_to_surfel_mask{0};
    cDGF_SURFEL_VOXEL_WEIGHT_SET_DESC *s_v_weight_set =
                &s_ublk_weight_set_desc->surfel_voxel_weight_sets[0];
    STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();
    uINT8 *voxel_info          = ublk_interaction->voxel_info();
    asINT32 n_weight_sets_pde_only = 0;
    ccDOTIMES (iv, n_s_v_weight_sets)
    {
      STP_LATVEC_MASK_WORD lv_mask = s_v_weight_set->w.latvec_mask;
      sSURFEL_VOXEL_INTERACTION s_v_interaction;
      asINT32 n_voxel_weights  = 0, n_voxel_weights_pde_only = 0, latvec = 0;
      //  The size N_LATTICE_VECTOR_PAIRS should be enough.
      uINT8 latvecs[N_MOVING_STATES] = {0};
      while (lv_mask) {
        if (lv_mask & 1) {
          latvecs[n_voxel_weights] = latvec;
          n_voxel_weights++;
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
          if (latvec >= N_MOVING_STATES)
            n_voxel_weights_pde_only++;
#endif
        }
        lv_mask = lv_mask >> 1;
        latvec++;
      }

      // initializing the scale difference to be 0 for now
      // This will be updated after ublks have been read in
      s_v_interaction.set_n_voxel_weights_scale_diff(n_voxel_weights, 0);
      s_v_interaction.set_n_voxel_weights_pde_only(n_voxel_weights_pde_only);
      s_v_interaction.set_voxel_id_phase_mask(s_v_weight_set->w.voxel_id_phase_mask);
#if !BUILD_GPU
      s_v_interaction.set_vsurfel_ratio(s_v_weight_set->w.vsurfel_ratio);

      // CONDUCTION-TODO: Compute and store only if surfel is a conduction surfel
      if (g_pfc_use_approximate_weights) {
        asINT32 voxel = s_v_interaction.voxel_id();
        sdFLOAT pfluid = interacting_ublk->is_near_surface()
                         ? interacting_ublk->surf_geom_data()->pfluids[voxel]
                         : 1.0;
        pfluid *= pfluid_scaling;

        sdFLOAT dist = 0.0;
        ccDOTIMES (i, 3)
          dist += (surfel.centroid[i] - interacting_ublk->centroids (voxel, i))
                  * (surfel.centroid[i] - interacting_ublk->centroids (voxel, i));
        dist = sqrt(dist) / voxel_size_for_approximate_weights;

        sdFLOAT approximate_weight = approximate_pgram_dist_weighting(dist) * approximate_pgram_volume_weighting(pfluid);

        if (((is_even || is_odd) && g_pfc_even_odd_surfel_v2s_use_vr_fine == 0)
            || g_pfc_regular_surfel_v2s_use_vr_fine == 0) {
          /* Since VR Coarse voxels are already accounted for, underlying
           * VR Fine voxels should not contribute anything to V2S of
           * even/odd surfels */
          if (surfel_scale - interacting_ublk->scale() == 0
              && interacting_ublk->is_vr_fine()) {
            approximate_weight = 0.0;
          }
        } else if (((is_even || is_odd) && g_pfc_even_odd_surfel_v2s_use_vr_fine == 1)
            || g_pfc_regular_surfel_v2s_use_vr_fine == 1) {
          /* If this is an even/odd surfel, we get contributions from
           * coarse voxel only if it has no underlying VRFine. For
           * coarse voxels which have underlying VRFine, we set 0 weights
           * since those contributions come from VRFine */
          if (surfel_scale - interacting_ublk->scale() == 1
              && interacting_ublk->is_vr_coarse()) {
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data = interacting_ublk->vr_coarse_data();
            if (vr_coarse_data->vr_fine_ublk(voxel).ublk() != NULL)
              approximate_weight = 0.0;
          }
        }

        s_v_interaction.set_approximate_weight(approximate_weight);
      }
#endif

      if (n_voxel_weights == n_voxel_weights_pde_only)
        n_weight_sets_pde_only++;
      else
        voxels_advect_to_surfel_mask.set (s_v_interaction.voxel_id() );

      voxel_info = ublk_interaction->set_voxel_interaction(voxel_info,
                                                           &s_v_interaction);
      ccDOTIMES (iw, n_voxel_weights) {
        *weights++ = s_v_weight_set->weights[iw].weight;
        *voxel_info++ = latvecs[iw];
      }
      s_v_weight_set++;
    }
    //ublk_interaction->m_n_weight_sets_pde_only = n_weight_sets_pde_only;
    ublk_interaction->m_voxels_advect_to_surfel_mask = voxels_advect_to_surfel_mask;

    ublk_interaction = (SURFEL_UBLK_INTERACTION) ((uINT8 *) ublk_interaction +
                                               ublk_interaction->size());
    char *new_ui_ptr = (char *) ublk_interaction;
    s_ublk_weight_set_desc++;
  }
}

template<>
VOID sSURFEL::add_surfel_interactions(DGF_SURFEL_DESC surfel_desc) {

  s2s_advect_data()->create_surfel_interactions(surfel_desc);
}

template<>
VOID sSURFEL::init(DGF_SURFEL_DESC surfel_desc) 
{
  m_scale = surfel_desc->s.surfel_scale;
  m_id = surfel_desc->s.surfel_id;

  //This is required for building the surfel edge connectivity
  auINT32 flags = surfel_desc->s.surfel_flags;
  set_backside((flags & DGF_SURFEL_DERIVED_FROM_OPEN_SHELL) && (flags & DGF_SURFEL_OPEN_SHELL_INVERTED));

#if BUILD_D19_LATTICE
  set_frozen(FALSE);
#endif

  centroid[0] = (STP_GEOM_VARIABLE) surfel_desc->s.centroid[0];
  centroid[1] = (STP_GEOM_VARIABLE) surfel_desc->s.centroid[1];
  centroid[2] = (STP_GEOM_VARIABLE) surfel_desc->s.centroid[2];
  area        = (STP_GEOM_VARIABLE) surfel_desc->s.area;
  normal[0] = (STP_GEOM_VARIABLE) surfel_desc->s.normal[0];
  normal[1] = (STP_GEOM_VARIABLE) surfel_desc->s.normal[1];
  normal[2] = (STP_GEOM_VARIABLE) surfel_desc->s.normal[2];

  m_face_index = surfel_desc->s.face_index;
  if (surfel_desc->s.opposite_index > -1) {
    m_opposite_surfel = (sSURFACE_SHOB *)(uINT64)surfel_desc->s.opposite_index;
  } else {
    m_opposite_surfel = (sSURFACE_SHOB *)INVALID_SHOB_ID;
  }

  m_ublk_interactions   = NULL;
  m_n_ublk_interactions = 0;

  incoming_latvec_mask = surfel_desc->s.incoming_mask;
  this->set_ref_frame_index((surfel_desc->s.lrf_index < 0) ? SRI_GLOBAL_REF_FRAME_INDEX: surfel_desc->s.lrf_index);
  set_even_odd_mask(surfel_desc->s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK);
  m_home_sp = my_proc_id;
  // init the appropriat+e solver data blocks
  SURFEL_APPLY(init, (surfel_desc));
  if (this->is_lrf())
    this->lrf_data()->init(surfel_desc);
#if BUILD_D19_LATTICE
  if (sim.store_frozen_vars)
    this->frozen_data()->init(surfel_desc);

  if (sim.is_pf_model)
    this->pf_data()->init(surfel_desc);
#endif
}

template<>
SP_TIMER_TYPE sSURFEL::timer()
{

  // In surfel centric code, sSURFEL_BASE is actually DYN_SURFEL
  // sampling surfel is derived from shob directly and not being considered here
  if (!dynamics_data())
    msg_internal_error("Dynamics data is missing for surfel %d", id());

  asINT32 dyn_type = dynamics_data()->dynamics_type();                       
  switch (dyn_type) {                                                      
                                                                           
  case SLIP_SURFEL_TYPE:
  case SLIP_FIXED_TEMP_SURFEL_TYPE:
  case SLIP_THERMAL_RESIST_SURFEL_TYPE:
  case SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:
    if(is_fringe())
      return SP_FRINGE_SURFEL_DYN_TIMER;
    else if(is_fringe2())
      return SP_FRINGE2_SURFEL_DYN_TIMER;
    else
      return SP_STANDARD_WALL_SURFEL_DYN_TIMER;
    break;
 
  case VEL_SLIP_SURFEL_TYPE:
    return SP_VEL_WALL_SURFEL_DYN_TIMER;
    break;
  
  case ANGULAR_SLIP_SURFEL_TYPE:
  case ANGULAR_SLIP_THERMAL_RESIST_SURFEL_TYPE:
  case ANGULAR_SLIP_FIXED_TEMP_SURFEL_TYPE:
  case ANGULAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:  
    return SP_ANGULAR_WALL_SURFEL_DYN_TIMER;
    break;
  
  case LINEAR_SLIP_SURFEL_TYPE:
  case LINEAR_SLIP_THERMAL_RESIST_SURFEL_TYPE:
  case LINEAR_SLIP_FIXED_TEMP_SURFEL_TYPE:
  case LINEAR_SLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:
    return SP_LINEAR_WALL_SURFEL_DYN_TIMER;
    break;
    
  case NOSLIP_SURFEL_TYPE:                                                
  case NOSLIP_FIXED_TEMP_SURFEL_TYPE:
  case NOSLIP_THERMAL_RESIST_SURFEL_TYPE:
  case NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:
    return SP_NOSLIP_SURFEL_DYN_TIMER;
    break;
 
  case ANGULAR_NOSLIP_SURFEL_TYPE:
  case ANGULAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE:
  case ANGULAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE:
  case ANGULAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:  
    return SP_ANGULAR_WALL_SURFEL_DYN_TIMER;
    break;
  
  case LINEAR_NOSLIP_SURFEL_TYPE:
  case LINEAR_NOSLIP_THERMAL_RESIST_SURFEL_TYPE:
  case LINEAR_NOSLIP_FIXED_TEMP_SURFEL_TYPE:
  case LINEAR_NOSLIP_FIXED_HEAT_FLUX_SURFEL_TYPE:
    return SP_LINEAR_WALL_SURFEL_DYN_TIMER;
    break;
 
  case STATIC_PRESSURE_FREE_DIR_SURFEL_TYPE:
  case STATIC_PRESSURE_FIXED_DIR_SURFEL_TYPE:
  case STAG_PRESSURE_FREE_DIR_SURFEL_TYPE:
  case STAG_PRESSURE_FIXED_DIR_SURFEL_TYPE:
    return SP_PRESSURE_SURFEL_DYN_TIMER;
    break;
  
  case MASS_FLUX_SURFEL_TYPE:                                                
  case MASS_FLOW_SURFEL_TYPE:                                                
    return SP_MASS_FLUX_SURFEL_DYN_TIMER;
    break;
  case FIXED_VEL_SURFEL_TYPE:
  case TURB_VEL_SURFEL_TYPE:
    return SP_FIXED_VEL_SURFEL_DYN_TIMER;
    break;
  case SOURCE_SURFEL_TYPE:
  case TURB_SOURCE_SURFEL_TYPE:
    return SP_SOURCE_SURFEL_DYN_TIMER;
    break;
  case PASS_THRU_SURFEL_TYPE:
    return SP_PRESSURE_SURFEL_DYN_TIMER;
    break;
  
  case CONDUCTION_ADIABATIC_SURFEL_TYPE:
  case CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
  case CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
  case CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
  case CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE:
  case CONDUCTION_CONTACT_SURFEL_TYPE:
    return SP_CONDUCTION_SURFEL_DYN_TIMER;
    break;
 
  default:
    msg_print("Invalid surfel dynamics type %d for surfel %d", dyn_type, id());
    return SP_INVALID_TIMER;
    break;
  }
  return SP_INVALID_TIMER;
}

// Question: why sending normals again during initialization? Could the normal be different from
// what is in the LGI file?
template<>
VOID sSURFEL::fill_send_surfel_type_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element)
{
  memcpy(&element->m_surfel_type,
         &m_surfel_attributes.m_surfel_type,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_surfel_type));
  memcpy(&element->m_incoming_latvec_mask,
         &incoming_latvec_mask,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT,m_incoming_latvec_mask));

  element->m_dynamics_type = has_dynamics_data()? dynamics_data()->m_dynamics_type : -1;
}

template<>
VOID sSURFEL::fill_send_init_info_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element)
{

  memcpy(element->m_normal, 
         normal,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_normal));
  if(sim.is_particle_model) {
    element->m_surfel_material_id = p_data()->s.surface_material_id;
  }

  if (sim.is_turb_model && is_inlet_or_outlet())
    memcpy(&element->m_u,
           &(dyn_turb_data()->u),
           struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_u));

  memcpy(element->m_in_states_voxel_weight,
         in_states_voxel_weight,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_in_states_voxel_weight));

  memcpy(element->m_in_states_voxel_weight2,
         in_states_voxel_weight2,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_in_states_voxel_weight2));

  memcpy(&element->m_lrf_v2s_scale_diff,
         &(lrf_data()->lrf_v2s_scale_diff),
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_lrf_v2s_scale_diff));

#ifdef BUILD_5G_LATTICE
  memcpy(&element->m_potential, &lb_data()->potential, sizeof(element->m_potential));
  memcpy(&element->m_porosity, &lb_data()->porosity, sizeof(element->m_porosity));
#endif
  
  if(sim.is_particle_model) {
    element->m_surface_material_id = p_data()->s.surface_material_id;
  }

  //if (sim.is_conduction_model && is_conduction_surface()) {
  if (sim.is_conduction_model) {
    if (is_conduction_surface()) {
      memcpy(&element->m_sampling_weight_with_interface_s2s_inv,
             &(conduction_data()->sampling_weight_with_interface_s2s_inv),
             struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_sampling_weight_with_interface_s2s_inv));
      memcpy(&element->m_sampling_weight_without_interface_s2s_inv,
             &(conduction_data()->sampling_weight_without_interface_s2s_inv),
             struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_sampling_weight_without_interface_s2s_inv));
    }
    if (is_conduction_shell() && sim.use_implicit_shell_solver) {
      element->m_implicit_shell_state_index = this->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
    }
  }

}

template<>
VOID sSURFEL::upgrade_surfel_type(uINT32 surfel_type) {

  tSURFEL_ATTRS<1> surfel_attrs;
  surfel_attrs.m_surfel_type = surfel_type;
  set_wall(surfel_attrs.m_is_wall);
  set_not_free_slip_wall(surfel_attrs.m_is_not_free_slip_wall);
  set_inlet_or_outlet(surfel_attrs.m_is_inlet_or_outlet);
  set_weightless_mlrf(surfel_attrs.m_is_weightless_mlrf);
}

template<>
VOID sSURFEL::unpack_recv_surfel_type_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element)
{
  upgrade_surfel_type(element->m_surfel_type);
  memcpy(&incoming_latvec_mask, &element->m_incoming_latvec_mask,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT,m_incoming_latvec_mask));

  if (has_dynamics_data()) {
    dynamics_data()->m_dynamics_type = element->m_dynamics_type;
  }  
}

template<>
VOID sSURFEL::unpack_recv_init_info_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element)
{
  memcpy(normal, element->m_normal, struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_normal));

  if(sim.is_particle_model) {
    p_data()->s.surface_material_id = element->m_surfel_material_id;
  }
  
  if (sim.is_turb_model && is_inlet_or_outlet())
    memcpy(&(dyn_turb_data()->u), &element->m_u, struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_u));

  memcpy(in_states_voxel_weight, element->m_in_states_voxel_weight,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_in_states_voxel_weight));

  memcpy(in_states_voxel_weight2, element->m_in_states_voxel_weight2,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_in_states_voxel_weight2));

  memcpy(&(lrf_data()->lrf_v2s_scale_diff),
         &element->m_lrf_v2s_scale_diff,
         struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_lrf_v2s_scale_diff));

#ifdef BUILD_5G_LATTICE
  memcpy(&lb_data()->potential, &element->m_potential, sizeof(element->m_potential));
  memcpy(&lb_data()->porosity, &element->m_porosity, sizeof(element->m_porosity));
#endif

  if (sim.is_conduction_model) {
    if (is_conduction_surface()) {
      memcpy(&(conduction_data()->sampling_weight_with_interface_s2s_inv), &element->m_sampling_weight_with_interface_s2s_inv,
             struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_sampling_weight_with_interface_s2s_inv));
      memcpy(&(conduction_data()->sampling_weight_without_interface_s2s_inv), &element->m_sampling_weight_without_interface_s2s_inv,
             struct_field_size(SURFEL_INIT_INFO_SEND_ELEMENT, m_sampling_weight_without_interface_s2s_inv));
    }
    if (is_conduction_shell() && sim.use_implicit_shell_solver) {
      this->shell_conduction_implicit_solver_data()->implicit_shell_state_index = element->m_implicit_shell_state_index + sim.implicit_shell_solver_state_index_offset[this->m_home_sp];
    }
  }

  if(sim.is_particle_model) {
    p_data()->s.surface_material_id = element->m_surface_material_id;
  }
  
}

template<>
VOID sSURFEL::fill_send_bc_type_buffer(SURFEL_BCTYPE_SEND_ELEMENT element)
{
  element->m_boundary_condition_type = lb_data()->boundary_condition_type; 
}

template<>
VOID sSURFEL::unpack_recv_bc_type_buffer(SURFEL_BCTYPE_SEND_ELEMENT element)
{
  lb_data()->boundary_condition_type = element->m_boundary_condition_type;
}

template<>
VOID sSURFEL::count_neighbor_ublks_bytes(uINT32 &num_bytes){
  num_bytes++;
  uINT8 num_ublks = p_data()->neighbor_ublks.size();
  num_bytes += num_ublks *(sizeof(UBLK_ID) + sizeof(VOXEL_MASK_8));
}

template<>
VOID sSURFEL::fill_neighbor_ublks(uINT8 *& buffer) {
  uINT8 num_ublks = p_data()->neighbor_ublks.size();
  pack_and_advance(buffer, &num_ublks);
  ccDOTIMES(iu, num_ublks) {
    UBLK_ID ublk_id = p_data()->neighbor_ublks[iu]->id();
    pack_and_advance(buffer, &ublk_id);
    VOXEL_MASK_8 voxel_mask = p_data()->neighbor_voxel_masks[iu];
    pack_and_advance(buffer, &voxel_mask);
  }
}

//REALM_ASSUMPTION: Assumed that this is used only for particle
// modeling, and that the neighbor ublks are all in FLOW_REALM
template<>
VOID sSURFEL::unpack_neighbor_ublks(uINT8 *& buffer) {
  uINT8 num_ublks;
  unpack_and_advance(&num_ublks, buffer);
  ccDOTIMES(iu, num_ublks) {
    UBLK_ID ublk_id;
    VOXEL_MASK_8 voxel_mask;
    unpack_and_advance(&ublk_id, buffer);
    unpack_and_advance(&voxel_mask, buffer);
    // FLOW_REALM assumed
    UBLK ublk = ublk_from_id(ublk_id);
    if (ublk && !ublk->is_ghost()) {
      p_data()->neighbor_ublks.push_back(ublk);
      p_data()->neighbor_voxel_masks.push_back(voxel_mask);
    }
  }
}

#if BUILD_5G_LATTICE
template<>
VOID sSURFEL::fill_send_potential_buffer(SURFEL_POTENTIAL_SEND_ELEMENT element) {
  element->m_lb_potential = lb_data()->potential;
  if (g_is_multi_component)
    element->m_mc_potential = mc_data()->potential;
}

template<>
VOID sSURFEL::unpack_recv_potential_buffer(SURFEL_POTENTIAL_SEND_ELEMENT element) {
  lb_data()->potential = element->m_lb_potential;
  if (g_is_multi_component)
    mc_data()->potential = element->m_mc_potential;
}
#endif

// 5G APM is not supported
#if !BUILD_5G_LATTICE
template<>
uINT64 sSURFEL::surfel_pair_dyn_ckpt_len()
{
  sSURFEL_PAIR* surfel_pair = get_surfel_pair();
  uINT64 ckpt_len = 0;
  if (surfel_pair != nullptr) {
    CALL_SURFEL_PAIR_DYN_METHOD(surfel_pair, ckpt_len, (ckpt_len));
  }
  return ckpt_len;
}

template<>
VOID sSURFEL::read_surfel_pair_dyn_ckpt()
{
  sSURFEL_PAIR* surfel_pair = get_surfel_pair();
  if (surfel_pair != nullptr) {
    CALL_SURFEL_PAIR_DYN_METHOD(surfel_pair, read_ckpt, ());
  }
}

template<>
VOID sSURFEL::write_surfel_pair_dyn_ckpt()
{
  // Find the surfel pair in the group
  // write ckpt only when after reading the exterior (second) surfel since it has the surfel pair ptr
  sSURFEL_PAIR* surfel_pair = get_surfel_pair();
  if (surfel_pair != nullptr) {
    CALL_SURFEL_PAIR_DYN_METHOD(surfel_pair, write_ckpt, ());
  }
}

template<>
VOID sSURFEL::write_surfel_pair_dyn_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  // Find the surfel pair in the group
  // write ckpt only when after reading the exterior (second) surfel since it has the surfel pair ptr
  sSURFEL_PAIR* surfel_pair = get_surfel_pair();
  if (surfel_pair != nullptr) {
    CALL_SURFEL_PAIR_DYN_METHOD(surfel_pair, write_ckpt, (pio_ckpt_buff));
  }
}
#endif

struct Shob_len
{
  Shob_len(SHOB_ID id_, bool verbose_):id(id_), verbose(verbose_){}

  template <typename T>
  void add() {
    constexpr size_t size = sizeof(T);
    add<T>(size);
  }
  template <typename T>
  void add(const size_t size) {
    if(verbose)
      printf("Surf_ID=%d %s adding %zu bytes\n",id, typeid(T).name(), size);
    len += size;
  }

  const SHOB_ID id;
  const bool verbose=false;
  uINT64 len=0;

};

template<>
uINT64 sSURFEL::ckpt_len(bool verbose)
{
  Shob_len surf_len(id(), verbose);

  // XDU: should be removed after is_outflux_copied flag is not ckpted.
  if(is_isurfel())
    surf_len.add<asINT32>();
    
  surf_len.add<BOOLEAN>();

  if (sim.is_lb_model) {
    surf_len.add<sSURFEL_LB_DATA>(lb_data()->ckpt_len());
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      surf_len.add<sSURFEL_V2S_LB_DATA>(v2s_lb_data()->ckpt_len());
    if (has_two_copies_of_outflux())
      surf_len.add<sSURFEL_S2S_LB_DATA>(s2s_lb_data()->ckpt_len());
  }
  if (sim.is_turb_model) {
    surf_len.add<sSURFEL_TURB_DATA>(turb_data()->ckpt_len());
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      surf_len.add<sSURFEL_V2S_TURB_DATA>(v2s_turb_data()->ckpt_len());
  }
  if (sim.is_heat_transfer) {
    surf_len.add<sSURFEL_T_DATA>(sizeof(*t_data()));
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      surf_len.add<sSURFEL_V2S_T_DATA>(v2s_t_data()->ckpt_len());
    if (has_two_copies_of_outflux())
      surf_len.add<sSURFEL_S2S_T_DATA>(s2s_t_data()->ckpt_len());
  }
  if(sim.is_conduction_model && is_conduction_surface()) {
    surf_len.add<sSURFEL_CONDUCTION_DATA>(conduction_data()->ckpt_len());
  }
  if(sim.is_conduction_model && is_conduction_interface()) {
    if (is_conduction_surface()) {
      surf_len.add<sCONDUCTION_INTERFACE_SOLID_DATA>(conduction_interface_solid_data()->ckpt_len());
    } else if (is_conduction_shell()) {
      surf_len.add<sCONDUCTION_INTERFACE_OPEN_SHELL_DATA>(conduction_interface_open_shell_data()->ckpt_len());
    } else {
      surf_len.add<sCONDUCTION_INTERFACE_FLUID_DATA>(conduction_interface_fluid_data()->ckpt_len());
    }
  }
  if(sim.is_conduction_model && is_conduction_shell()) {
    surf_len.add<sSURFEL_SHELL_CONDUCTION_DATA>(shell_conduction_data()->ckpt_len());
  }

  if (is_lrf())
    surf_len.add<sSURFEL_LRF_DATA>(sizeof(*lrf_data()));

  if (sim.is_lb_model && (bool) g_is_multi_component) {
    surf_len.add<sSURFEL_MC_DATA>(sizeof(*mc_data()));
    if (has_two_copies_of_outflux())
      surf_len.add<sSURFEL_S2S_MC_DATA>(s2s_mc_data()->ckpt_len());
  }

  if (sim.is_particle_model)
    surf_len.add<sSURFEL_PARTICLE_DATA>(p_data()->ckpt_len());

  if (sim.is_scalar_model) {
    surf_len.add<sSURFEL_UDS_DATA>(sim.n_user_defined_scalars * sizeof(*uds_data(0)));
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      surf_len.add<sSURFEL_V2S_UDS_DATA>(sim.n_user_defined_scalars * v2s_uds_data(0)->ckpt_len());
    if (sim.uds_solver_type==LB_UDS && has_two_copies_of_outflux())
      surf_len.add<sSURFEL_S2S_UDS_DATA>(sim.n_user_defined_scalars * s2s_uds_data(0)->ckpt_len());
#if BUILD_D19_LATTICE
    if (sim.is_pf_model){
      surf_len.add<sSURFEL_PF_DATA>(pf_data()->ckpt_len());
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
        surf_len.add<SURFEL_V2S_DATA>( v2s_pf_data()->ckpt_len());
    }
#endif
  }

  if (sim.is_radiation_model) {
    if (is_radiation()) {
      auto rad_data = radiation_data();
      surf_len.add<sSURFEL_RADIATION_DATA>(rad_data->ckpt_len());

      if (is_conduction_open_shell()) {
        rad_data++;
        surf_len.add<sSURFEL_RADIATION_DATA>(rad_data->ckpt_len());
      }
    }
  }

#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze)
    surf_len.add<BOOLEAN>(sizeof(this->is_frozen()));

  if (sim.store_frozen_vars)
    surf_len.add<sSURFEL_FROZEN_DATA>(frozen_data()->ckpt_len());
#endif

  if (!is_lrf() && !is_mirror())
    surf_len.add<sSURFEL_DYNAMICS_DATA>(surfel_dyn_ckpt_len(dynamics_data()));
#if !BUILD_5G_LATTICE
  if (is_isurfel()) {
    surf_len.add<sSURFEL_PAIR>(surfel_pair_dyn_ckpt_len());                            
  }
#endif
  return surf_len.len;
}

template<>
VOID sSURFEL::init_outflux_copied() {
  STP_SCALE surfel_scale = scale();
  //BASETIME timestep = g_timescale.m_time - (1 << (sim.num_scales - 1 - surfel_scale));
  set_outflux_copied(FALSE);
  if (is_s2s_destination()) {
    if (!is_even_or_odd()) {
      // If timestep is odd, m_outflux_copied should be set to 1
      //if (!sim_is_timestep_odd(surfel_scale, timestep))
      if (g_timescale.is_timestep_odd(surfel_scale))
        set_outflux_copied(TRUE);              
    } else {
      if (is_odd() && g_timescale.is_timestep_odd(coarsen_scale(surfel_scale)))
        set_outflux_copied(TRUE);
      if (is_even()) {
        BOOLEAN is_coarse_timestep_odd = g_timescale.is_timestep_odd(coarsen_scale(surfel_scale));
        if (g_timescale.is_timestep_odd(surfel_scale) ^ is_coarse_timestep_odd) {
          set_outflux_copied(TRUE);
        }
      } 
    }
  }
}

template<>
VOID sSURFEL::read_ckpt() {
  // msg_print("===== surfel %d", this->id());
  g_timescale.m_lb_tm.m_time++;
  STP_SCALE surfel_scale = scale();
  set_outflux_copied(FALSE);
  if (is_s2s_destination()) {
    if (!is_even_or_odd()) {
      // If timestep is odd, m_outflux_copied should be set to 1
      if (g_timescale.is_timestep_odd(surfel_scale))
        set_outflux_copied(TRUE);              
    } else {
      if (is_odd() && g_timescale.is_timestep_odd(coarsen_scale(surfel_scale)))
        set_outflux_copied(TRUE);
      if (is_even()) {
        BOOLEAN is_coarse_timestep_odd = g_timescale.is_timestep_odd(coarsen_scale(surfel_scale));
        if (g_timescale.is_timestep_odd(surfel_scale) ^ is_coarse_timestep_odd) {
          set_outflux_copied(TRUE);
        }
      } 
    }
  }
  g_timescale.m_lb_tm.m_time--;
  asINT32 dynamics_type;
  if(is_isurfel()){
    read_lgi(dynamics_type);
    this->dynamics_data()->init(dynamics_type);
  }

  BOOLEAN is_outflux_copied;
  read_lgi(is_outflux_copied);
  //is_outflux_copied only relevant for s2s for flow surfels, not needed for conduction surfels
  if (is_outflux_copied != this->is_outflux_copied() && !is_conduction_surfel())
    msg_warn("Full ckpt surfel %d is_outflux_copied %d different from %d in ckpt file.", id(), this->is_outflux_copied(), is_outflux_copied);

#if BUILD_D19_LATTICE  
#endif
  if (sim.is_lb_model) {
    lb_data()->read_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_lb_data()->read_ckpt();
    if (has_two_copies_of_outflux()) 
      s2s_lb_data()->read_ckpt();
  }
  if (sim.is_turb_model) {
    turb_data()->read_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_turb_data()->read_ckpt();
  }
  if (sim.is_heat_transfer) {
    t_data()->read_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_t_data()->read_ckpt();
    if (has_two_copies_of_outflux())
      s2s_t_data()->read_ckpt();
  }
  if(sim.is_conduction_model && is_conduction_surface()) {
    conduction_data()->read_ckpt();
  }
  if(sim.is_conduction_model && is_conduction_interface()) {
    if (is_conduction_surface()) {
      conduction_interface_solid_data()->read_ckpt();
    } else if (is_conduction_shell()) {
      conduction_interface_open_shell_data()->read_ckpt();
    } else {
      conduction_interface_fluid_data()->read_ckpt();
    }
  }
  if(sim.is_conduction_model && is_conduction_shell()) {
    shell_conduction_data()->read_ckpt();
  }
  if (is_lrf()) {
    sSURFEL_LRF_DATA lrf_data; 
    lrf_data.read_ckpt();
    // Copy to m_lrf_solver_data_from_ckpt otherwise will be cleared in accumulate_all_v2s_and_s2s_weights()
    memcpy(&this->m_lrf_solver_data_from_ckpt, &lrf_data, sizeof(this->m_lrf_solver_data_from_ckpt));
#if DEBUG_NEXTGEN
    if (id() == 215)
      msg_print("T %ld S %d read lrf data ckpt lrf_v2s_scale_diff %f", g_timescale.m_time, id(), lrf_data.lrf_v2s_scale_diff);
#endif
  }
  if (sim.is_lb_model && (bool) g_is_multi_component) {
    mc_data()->read_ckpt();
    if (has_two_copies_of_outflux())
      s2s_mc_data()->read_ckpt();
  }

  if (sim.is_particle_model)
    p_data()->read_ckpt();
  
  if (sim.is_scalar_model) {
    BOOLEAN is_uds_lb_solver = (sim.uds_solver_type == LB_UDS);
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){ 
      uds_data(nth_uds)->read_ckpt();
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
	v2s_uds_data(nth_uds)->read_ckpt();
      if (is_uds_lb_solver && has_two_copies_of_outflux())
	s2s_uds_data(nth_uds)->read_ckpt();
    }
#if BUILD_D19_LATTICE
    if (sim.is_pf_model){
      pf_data()->read_ckpt();
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
        v2s_pf_data()->read_ckpt();
    }
#endif
  }

  if (sim.is_radiation_model) {
    if (is_radiation()) {
      auto rad_data = radiation_data();
      rad_data->read_ckpt();

      if (is_conduction_open_shell()) {
        rad_data++;
        rad_data->read_ckpt();
      }
    }
  }

#if BUILD_D19_LATTICE

  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen;
    read_lgi(is_frozen);
    set_frozen(is_frozen);
  }

  if (g_full_ckpt_with_frozen_vars) {
    if (sim.store_frozen_vars) {
      frozen_data()->read_ckpt();
    } else {
      static sSURFEL_FROZEN_DATA frozen_data;
      frozen_data.read_ckpt();
    }
  }
#endif
  if (!is_lrf() && !is_mirror())
    surfel_dyn_read_ckpt(dynamics_data());

#if !BUILD_5G_LATTICE
  if (is_isurfel()) {
    read_surfel_pair_dyn_ckpt();                            
  }
#endif
}


template<>
size_t sSURFEL::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{

  size_t initial_offset = pio_ckpt_buff.m_offset;//deb purposes

  // XDU: temporarily write these in full ckpt for sanity checks. Should be removed after everything works fine.
  BOOLEAN is_outflux_copied = this->is_outflux_copied();
  pio_ckpt_buff.write(&is_outflux_copied);

  if (sim.is_lb_model) {
    lb_data()->write_ckpt(pio_ckpt_buff);
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      pio_ckpt_buff.write(v2s_lb_data());
    if (has_two_copies_of_outflux()) // even odd surfel already has v2s ckcpted which includes s2s
      pio_ckpt_buff.write(s2s_lb_data());
  }
  if (sim.is_turb_model) {
    pio_ckpt_buff.write(turb_data());
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      pio_ckpt_buff.write(v2s_turb_data());
 }
  if (sim.is_heat_transfer) {
    pio_ckpt_buff.write(t_data());
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      pio_ckpt_buff.write(v2s_t_data());
    if (has_two_copies_of_outflux())
      pio_ckpt_buff.write(s2s_t_data());
  }

  if (is_lrf())
    pio_ckpt_buff.write(this->lrf_data());

  if (sim.is_lb_model && (bool) g_is_multi_component) {
    pio_ckpt_buff.write(mc_data());
    if (has_two_copies_of_outflux())
      pio_ckpt_buff.write(s2s_mc_data());
  }
  if (sim.is_particle_model)
    p_data()->write_ckpt(pio_ckpt_buff);

  if (sim.is_scalar_model) {
    BOOLEAN is_uds_lb_solver = (sim.uds_solver_type == LB_UDS);
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){ 
      pio_ckpt_buff.write(uds_data(nth_uds));
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
	      pio_ckpt_buff.write(v2s_uds_data(nth_uds));
      if (is_uds_lb_solver && has_two_copies_of_outflux())
	      pio_ckpt_buff.write(s2s_uds_data(nth_uds));
    }
#if BUILD_D19_LATTICE
    if (sim.is_pf_model){
      pio_ckpt_buff.write(pf_data());
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
        pio_ckpt_buff.write(v2s_pf_data());
    }
#endif
  }

#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen = this->is_frozen();
    pio_ckpt_buff.write(&is_frozen);
  }

  if (sim.store_frozen_vars) {
    pio_ckpt_buff.write(frozen_data());
  }
#endif
  if (!is_lrf() && !is_mirror())
    surfel_dyn_write_ckpt(pio_ckpt_buff,dynamics_data());

#if !BUILD_5G_LATTICE
  if (is_isurfel()) {
    write_surfel_pair_dyn_ckpt(pio_ckpt_buff);                            
  }
#endif

  size_t writtenSz = pio_ckpt_buff.m_offset - initial_offset;
  
  dassert(writtenSz == this->ckpt_len() && " SURFEL written size does not match ckpt_len()\n");
  return writtenSz;
}

template<>
VOID sSURFEL::write_ckpt()
{

  // XDU: temporarily write these in full ckpt for sanity checks. Should be removed after everything works fine.
  BOOLEAN is_outflux_copied = this->is_outflux_copied();
  asINT32 dynamics_type = this->dynamics_data()->dynamics_type();
  if(is_isurfel())
    write_ckpt_lgi(dynamics_type);
  write_ckpt_lgi(is_outflux_copied);

  if (sim.is_lb_model) {
    lb_data()->write_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_lb_data()->write_ckpt();
    if (has_two_copies_of_outflux()) // even odd surfel already has v2s ckcpted which includes s2s
      s2s_lb_data()->write_ckpt();
  }
  if (sim.is_turb_model) {
    turb_data()->write_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_turb_data()->write_ckpt();
 }
  if (sim.is_heat_transfer) {
    t_data()->write_ckpt();
    if (has_v2s_data() && is_even_or_odd() && !is_mirror())
      v2s_t_data()->write_ckpt();
    if (has_two_copies_of_outflux())
      s2s_t_data()->write_ckpt();
  }

  if(sim.is_conduction_model && is_conduction_surface()) {
    conduction_data()->write_ckpt();
  }
  if(sim.is_conduction_model && is_conduction_interface()) {
    if (is_conduction_surface()) {
      conduction_interface_solid_data()->write_ckpt();
    } else if (is_conduction_shell()) {
      conduction_interface_open_shell_data()->write_ckpt();
    } else {
      conduction_interface_fluid_data()->write_ckpt();
    }
  }
  if(sim.is_conduction_model && is_conduction_shell()) {
    shell_conduction_data()->write_ckpt();
  }

  if (is_lrf())
    this->lrf_data()->write_ckpt();

  if (sim.is_lb_model && (bool) g_is_multi_component) {
    mc_data()->write_ckpt();
    if (has_two_copies_of_outflux())
      s2s_mc_data()->write_ckpt();
  }
  if (sim.is_particle_model)
    p_data()->write_ckpt();

  if (sim.is_scalar_model) {
    BOOLEAN is_uds_lb_solver = (sim.uds_solver_type == LB_UDS);
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){ 
      uds_data(nth_uds)->write_ckpt();
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
	v2s_uds_data(nth_uds)->write_ckpt();
      if (is_uds_lb_solver && has_two_copies_of_outflux())
	s2s_uds_data(nth_uds)->write_ckpt();
    }
#if BUILD_D19_LATTICE
    if (sim.is_pf_model){
      pf_data()->write_ckpt();
      if (has_v2s_data() && is_even_or_odd() && !is_mirror())
        v2s_pf_data()->write_ckpt();
    }
#endif
  }

  if (sim.is_radiation_model) {
    if (is_radiation()) {
      auto rad_data = radiation_data();
      rad_data->write_ckpt();

      if (is_conduction_open_shell()) {
        rad_data++;
        rad_data->write_ckpt();
      }
    }
  }

#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen = this->is_frozen();
    write_ckpt_lgi(is_frozen);
  }

  if (sim.store_frozen_vars) {
    frozen_data()->write_ckpt();
  }
#endif
  if (!is_lrf() && !is_mirror())
    surfel_dyn_write_ckpt(dynamics_data());

#if !BUILD_5G_LATTICE
  if (is_isurfel()) {
    write_surfel_pair_dyn_ckpt();                            
  }
#endif
}

template<>
template <typename T>
VOID sSURFEL::wall_velocity(T wall_velocity[3]) {

#if !BUILD_5G_LATTICE
  if(this->is_slip_surfel()) {
    if(this->is_moving()) {
      MOVING_SLIP_SURFEL_DATA dynamics_data = (MOVING_SLIP_SURFEL_DATA)this->dynamics_data();
      SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (SLIP_SURFEL_PHYSICS_DESCRIPTOR) dynamics_data->physics_descriptor();
      STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(this->scale());
      STP_PHYS_VARIABLE wall_vel[3];
      dynamics_data->wall_velocity(this, wall_vel, TRUE, group_voxel_size);
      vcopy(wall_velocity, wall_vel);
      return;
    }else {
      SLIP_SURFEL_DATA dynamics_data = (SLIP_SURFEL_DATA)this->dynamics_data();
      SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (SLIP_SURFEL_PHYSICS_DESCRIPTOR)dynamics_data->physics_descriptor();
      asINT32 vel_ref_frame_index = pd->parameters()->ref_frame.value;
      asINT32 ref_frame_index = (asINT32) this->ref_frame_index();
      LRF_PHYSICS_DESCRIPTOR lrf = (is_ref_frame_local(ref_frame_index)
                                    ? &sim.lrf_physics_descs[ref_frame_index] : NULL);
      sdFLOAT wall_vel[3] = {0.0, 0.0, 0.0};
      BOOLEAN moving_wall_tang = TRUE;
      BOOLEAN some_parameter_time_varying =
        update_time_and_space_varying_surfel_physics_parms(cast_as_regular_array(this->centroid),
                                                           cast_as_regular_array(this->normal), pd);
      if (some_parameter_time_varying) {
        dynamics_data->update_from_pd_parms(this, pd);
      }
      STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(this->scale());
      BOOLEAN moving_wall = dynamics_data->maybe_update_wall_velocity(this, wall_vel, lrf, pd, group_voxel_size,
                                                     moving_wall_tang, vel_ref_frame_index);
      vcopy(wall_velocity,wall_vel);

     // vzero(wall_velocity);
      return;
    }
  }
#endif

  if(this->is_noslip_surfel()) {
    if(this->is_moving()) {
#if BUILD_5G_LATTICE
      MOVING_NOSLIP_5G_SURFEL_DATA dynamics_data = (MOVING_NOSLIP_5G_SURFEL_DATA)this->dynamics_data();
#else
      MOVING_NOSLIP_SURFEL_DATA dynamics_data = (MOVING_NOSLIP_SURFEL_DATA)this->dynamics_data();
#endif
      STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(this->scale());
      STP_PHYS_VARIABLE wall_vel[3];
      dynamics_data->wall_velocity(this, wall_vel, TRUE, group_voxel_size);
      vcopy(wall_velocity, wall_vel);
      return;
    } else {
#if BUILD_5G_LATTICE
      NOSLIP_5G_SURFEL_DATA dynamics_data = (NOSLIP_5G_SURFEL_DATA)this->dynamics_data();
#else
      NOSLIP_SURFEL_DATA dynamics_data = (NOSLIP_SURFEL_DATA)this->dynamics_data();
#endif
      vzero(wall_velocity);
      return;
    }
  }
  vzero(wall_velocity);//or maybe:  msg_error("Unexpected surfel type encountered when computing wall BC velocity.\n");
}

template VOID sSURFEL::wall_velocity<sFLOAT>(sFLOAT[3]);
template VOID sSURFEL::wall_velocity<dFLOAT>(dFLOAT[3]);

template<>
VOID tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>::print_surfel_data(std::ostream& os){

  using namespace UBLK_SURFEL_PRINT_UTILS;

  sim_print_data_header(os, "SURFEL_LRF_DATA");
  sim_print(os, "s2s_factor", lrf_s2s_factor);
  sim_print(os, "v2s_dist", lrf_v2s_dist);
  sim_print(os, "factor_pair", lrf_s2s_factor_pair);
  sim_print(os, "v2s_dist_pair", lrf_v2s_dist_pair);
  sim_print(os, "v2s_scale_diff", lrf_v2s_scale_diff);
  sim_print(os, "v2s_scale_diff_pair", lrf_v2s_scale_diff_pair);
}

template<>
VOID sSURFEL::print_surfel_content(std::ostream& os,
                                   const SURFEL_PRINT_OPTS& opts){

  int time_adjusted = (opts.timestep >=0) ? opts.timestep:g_timescale.m_end_time+opts.timestep;

  if ( g_timescale.m_time >= time_adjusted ){
    SURFEL_PRINT_OPTS::init_stream(os);
    opts.print_surfel_header(os,id(),g_timescale.m_time,scale(),is_ghost());

     if (this->is_lrf()) {
      lrf_data()->print_surfel_data(os);
    }

    if ( opts.data_print_mask & opts.LB_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_LB_DATA_TYPE)) {
        lb_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.TURB_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_TURB_DATA_TYPE)) {
        turb_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.T_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_T_DATA_TYPE)) {
        t_data()->print_surfel_data(os);
      }
    }

    // CONDUCTION-TODO: define for conduction
    // if ( opts.data_print_mask & opts.CONDUCTION_DATA_PRINT_MASK ){
    //   if( does_surfel_have_data_of_type(SURFEL_CONDUCTION_DATA_TYPE)) {
    //     conduction_data()->print_surfel_data(os);
    //   }
    // }

    if ( opts.data_print_mask & opts.UDS_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_UDS_DATA_TYPE)) {
	ccDOTIMES(nth_uds,sim.n_user_defined_scalars) 
	  uds_data(nth_uds)->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.PF_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_PF_DATA_TYPE)) {
        pf_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.MC_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_MC_DATA_TYPE)) {
        mc_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.LRF_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_LRF_DATA_TYPE)) {
        lrf_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.MLRF_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_MLRF_DATA_TYPE)) {
        mlrf_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.EVEN_ODD_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_EVEN_ODD_DATA_TYPE)) {
        //We leave this empty for now
        //even_odd_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.ISURFEL_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_ISURFEL_DATA_TYPE)) {
        //
      }
    }

    if ( opts.data_print_mask & opts.S2S_ADVECT_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_S2S_ADVECT_DATA_TYPE)) {
        s2s_advect_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.MIRROR_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_MIRROR_DATA_TYPE)) {
        //Leave this empty for now
        //mirror_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.V2S_LB_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_V2S_LB_DATA_TYPE)) {
        v2s_lb_data()->print_surfel_data(os);
      }
      else if (opts.s_v2s_data){
        opts.s_v2s_data->v2s_lb_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.V2S_TURB_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_V2S_TURB_DATA_TYPE)) {
        v2s_turb_data()->print_surfel_data(os);
      }
      else if (opts.s_v2s_data){
        opts.s_v2s_data->v2s_turb_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.V2S_T_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_V2S_T_DATA_TYPE)) {
        v2s_t_data()->print_surfel_data(os);
      }
      else if (opts.s_v2s_data){
        opts.s_v2s_data->v2s_t_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.V2S_UDS_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_V2S_UDS_DATA_TYPE)) {
	ccDOTIMES(nth_uds,sim.n_user_defined_scalars) 
	  v2s_uds_data(nth_uds)->print_surfel_data(os);
      }
      else if (opts.s_v2s_data){
	opts.s_v2s_data->v2s_uds_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.V2S_PF_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_V2S_PF_DATA_TYPE)) {
        v2s_pf_data()->print_surfel_data(os);
      }
      else if (opts.s_v2s_data){
        opts.s_v2s_data->v2s_pf_data()->print_surfel_data(os);
      }
    }

    if ( opts.data_print_mask & opts.DYN_DATA_PRINT_MASK ){
      if( does_surfel_have_data_of_type(SURFEL_DYN_DATA_TYPE)) {
        //Leave this empty for now
        //dyn_data()->print_surfel_data(os);
      }
    }

    opts.print_surfel_footer(os,id(),g_timescale.m_time);
  }
}

VOID sSURFEL_PAIR::resolve_meas_cell_ptrs()
{
  SURFEL apm_fluid_surfel = m_exterior_surfel;
  SURFEL_DYNAMICS_DATA surfel_dyn_data = apm_fluid_surfel->dynamics_data();
  asINT32 n_meas_cells = surfel_dyn_data->n_meas_cell_ptrs();
  sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptr = surfel_dyn_data->surfel_meas_cell_ptrs();
  assign_surfel_meas_cell_ptrs(apm_fluid_surfel, n_meas_cells, surfel_meas_cell_ptr, FALSE, apm_fluid_surfel->is_even()); // Not dealing with moving isurfels
  SURFEL conduction_surfel = m_interior_surfel;
  SURFEL_DYNAMICS_DATA cond_surfel_dyn_data = conduction_surfel->dynamics_data();
  n_meas_cells = cond_surfel_dyn_data->n_meas_cell_ptrs();
  sSURFEL_MEAS_CELL_PTR *cond_surfel_meas_cell_ptr = cond_surfel_dyn_data->surfel_meas_cell_ptrs();
  assign_surfel_meas_cell_ptrs(conduction_surfel, n_meas_cells, cond_surfel_meas_cell_ptr, FALSE, conduction_surfel->is_even()); // Not dealing with moving isurfels
}

VOID print_surfel_states(const char *print_string, SURFEL_V2S_DATA surfel_v2s_data, SURFEL surfel, std::ostream& os) {
  surfel->print_surfel_content(os,
                               SURFEL_PRINT_OPTS(SURFEL_PRINT_OPTS::ALL_DATA_PRINT_MASK,print_string,0,surfel_v2s_data));
}


#if BUILD_GPU
INIT_MSFL(tSURFEL_LRF_DATA) {
  COPY_SFL_TO_MSFL(lrf_s2s_factor);
  COPY_SFL_TO_MSFL(lrf_v2s_dist);
  COPY_SFL_TO_MSFL(lrf_s2s_factor_pair);
  COPY_SFL_TO_MSFL(lrf_v2s_dist_pair);
  COPY_SFL_TO_MSFL(lrf_v2s_scale_diff);
  COPY_SFL_TO_MSFL(lrf_v2s_scale_diff_pair);
  VEC_COPY_SFL_TO_MSFL(density_pair, N_TIME_INDICES);
  VEC2_COPY_SFL_TO_MSFL(momentum_pair, N_TIME_INDICES, N_AXES);
  VEC_COPY_SFL_TO_MSFL(K_pair, N_TIME_INDICES);
  VEC_COPY_SFL_TO_MSFL(E_pair, N_TIME_INDICES);
  VEC_COPY_SFL_TO_MSFL(temp_sample_pair, N_TIME_INDICES);
  VEC_COPY_SFL_TO_MSFL(entropy_sample_pair, N_TIME_INDICES);
  VEC2_COPY_SFL_TO_MSFL(uds_value_pair, MAX_N_USER_DEFINED_SCALARS, N_TIME_INDICES);
#if BUILD_D19_LATTICE
  VEC_COPY_SFL_TO_MSFL(pf_pressure_pair, N_TIME_INDICES);
  VEC2_COPY_SFL_TO_MSFL(pf_vel_pair, N_TIME_INDICES, 3);
  VEC2_COPY_SFL_TO_MSFL(pf_grad_chem_pair, N_TIME_INDICES, 3);
  VEC2_COPY_SFL_TO_MSFL(pf_grad_order_pair, N_TIME_INDICES, 4);
#endif
}
#endif
