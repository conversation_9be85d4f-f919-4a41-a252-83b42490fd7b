/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2001, 1995-2000 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Fluid dynamics for D{1,3}9 lattice
 *
 * James Hoch, Exa Corporation
 * Created Fri Nov 7, 2008
 *--------------------------------------------------------------------------*/

#ifndef _GRADIENT_H
#define _GRADIENT_H

#include "ublk.h"
#include "scalar_data.h"


// A pointer to an sUBLK_NEIGHBOR_COLLECTOR object is passed to collect_neighbor_values.
// collect_neighbor_values collects neighbor variable values for all voxels in a ublk.
// The neighbor values can be used straighforwardly in a gradient calculation.
//
// An sUBLK_NEIGHBOR_COLLECTOR object must provide the following interface:
//
//     // Returns number of variables to collect.
//     asINT32 n_variables();
//
//     // Returns pointer to neighbor variable slot filled in by collect_neighbor_values.
//     sdFLOAT *neighbor_variable_ptr(asINT32 var, asINT32 face, asINT32 voxel);
//
//     // Returns a variable value from a particular voxel of a ublk.
//     sdFLOAT extract_voxel_variable(asINT32 var,
//				      UBLK ublk, asINT32 voxel,
//				      asINT32 prior_timestep_index);
//
//     // Returns the PDE_BC_TYPE of a variable.
//     PDE_BC_TYPE bc_type(asINT32 var, asINT32 voxel);
//
//     // For a variable of type PDE_BC_TYPE_FIXED, this returns the fixed value.
//     sdFLOAT fixed_variable(asINT32 var, asINT32 voxel);
//
// Here is an example taken from the temperature solver:
//
//  typedef class sUBLK_T_SOLVER_NEIGHBOR_COLLECTOR {
//  public:
//    asINT32 n_variables() { return 3; } // Sorted as temp, density, diffusivity
//
//    // These neighbor values are filled in by collect_neighbor_values.
//    // They are accessed as a 3D array below:  sdFLOAT neighbor_vars[3][6][8]
//    sdFLOAT neighbor_temp[6][8];      // 6 faces X 8 voxels
//    sdFLOAT neighbor_density[6][8];
//    sdFLOAT neighbor_diffusivity[6][8];
//
//    sdFLOAT *neighbor_variable_ptr(asINT32 var, asINT32 face, asINT32 voxel) {
//      sdFLOAT (*neighbor_vars)[6][8] = (sdFLOAT (*)[6][8])neighbor_temp;
//      return &neighbor_vars[var][face][voxel];
//    }
//
//    sdFLOAT extract_voxel_variable(asINT32 var,
//  				 UBLK ublk, asINT32 voxel,
//  				 asINT32 prior_timestep_index)
//    {
//      switch (var) {
//      case 0:
//        return ublk->t_data()->fluid_temp[prior_timestep_index][voxel];
//      case 1:
//        return ublk->lb_data()->density[prior_timestep_index][voxel];
//      case 2:
//        return ublk->t_data()->diffusivity[prior_timestep_index][voxel];
//      default:
//        return 0; // keep compiler happy
//      }
//    }
//
//    // Use constructor to initialize the items that never change
//    sUBLK_T_SOLVER_NEIGHBOR_COLLECTOR() {
//      ccDOTIMES(v, 8) {
//        bc_type_density[v] = PDE_BC_TYPE_NORMAL;
//        bc_type_diffusivity[v] = PDE_BC_TYPE_NORMAL;
//      }
//    }
//
//    //// The methods below are only called by collect_neighbor_values for a voxel
//    //// whose bit is not set in the ublk's all_neighbors_voxel_mask.
//
//    // These are accessed as a 2D array below:  PDE_BC_TYPE bc_types[3][8]
//    PDE_BC_TYPE bc_type_temp[8];
//
//  private: // These are private because they should only be touched by the constructor
//    PDE_BC_TYPE bc_type_density[8];
//    PDE_BC_TYPE bc_type_diffusivity[8];
//  public:
//
//    PDE_BC_TYPE bc_type(asINT32 var, asINT32 voxel) {
//      PDE_BC_TYPE (*bc_types)[8] = (BC_TYPE (*)[8])bc_type_temp;
//      return bc_types[var][voxel];
//    }
//
//    // These are only used if bc_type is FIXED for a particular variable in a voxel
//    sdFLOAT fixed_temp[8];
//    sdFLOAT fixed_density[8];
//    sdFLOAT fixed_diffusivity[8];
//
//    sdFLOAT fixed_variable(asINT32 var, asINT32 voxel) {
//      sdFLOAT (*fixed_vars)[8] = (sdFLOAT (*)[8])fixed_temp;
//      return fixed_vars[var][voxel];
//    }
//
//  } *UBLK_T_SOLVER_NEIGHBOR_COLLECTOR;

inline UBLK_UDS_DATA get_ublk_uds_data(asINT32 nth_uds, UBLK ublk) 
{
  UBLK_UDS_DATA ublk_uds_data = ublk->uds_data(nth_uds);
  return ublk_uds_data;
}

inline NEAR_UBLK_UDS_DATA get_nearblk_uds_data(asINT32 nth_uds, UBLK nearblk)
{
  NEAR_UBLK_UDS_DATA nearblk_uds_data = nearblk->surf_uds_data(nth_uds);
  return nearblk_uds_data;
}

inline SURFEL_UDS_DATA get_surfel_uds_data(asINT32 nth_uds, SURFEL surfel) 
{
  SURFEL_UDS_DATA surfel_uds_data = surfel->uds_data(nth_uds);
  return surfel_uds_data;
}

inline SAMPLING_SURFEL_UDS_DATA get_sampling_surfel_uds_data(asINT32 nth_uds, SAMPLING_SURFEL sampling_surfel) 
{
  SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = sampling_surfel->uds_data(nth_uds);
  return sampling_surfel_uds_data;
}

/*
inline UBLK_UBFLOAT_UDS_DATA get_ublk_ubfloat_uds_data(asINT32 nth_uds, UBLK_UBFLOAT ublk) 
{
  UBLK_UBFLOAT_UDS_DATA ublk_uds_data = ublk->uds_data();
  return (ublk_uds_data+nth_uds);
}
inline NEARBLK_UBFLOAT_UDS_DATA get_nearblk_ubfloat_uds_data(asINT32 nth_uds, UBLK_UBFLOAT nearblk)
{
  NEARBLK_UBFLOAT_UDS_DATA nearblk_uds_data = nearblk->surf_uds_data();
  return (nearblk_uds_data+nth_uds);
}
*/




enum PDE_BC_TYPE {
  PDE_BC_TYPE_FLOATING,
  PDE_BC_TYPE_FIXED,
  PDE_BC_TYPE_PARTIAL,
  PDE_BC_TYPE_NORMAL
};


struct sPRIOR_FINE_INDICES {
  asINT8 indices[N_SOLVERS];
  
  sPRIOR_FINE_INDICES() {
    ccDOTIMES(nth_solver, N_SOLVERS) {
      indices[nth_solver] = sim_prior_timestep_index_one_finer();
    }
  }
  
};

// XDU temporarily place this here
asINT32 collect_fine_split_diag_neighbors(TAGGED_UBLK fine_neighbor, UBLK fine_ublk,
                                     asINT32 voxel, asINT32 latvec,
                                     sTAGGED_SPLIT_FACTOR *neighbor_split_facs);


// VINIT temporarily place this here
asINT32 collect_fine_split_neighbors(TAGGED_UBLK fine_neighbor, UBLK fine_ublk,
                                     asINT32 axis, asINT32 dir,
                                     sTAGGED_SPLIT_FACTOR *neighbor_split_facs);

static sPRIOR_FINE_INDICES prior_fine;
template <typename UBLK_NEIGHBOR_COLLECTOR>
VOID collect_neighbor_values(UBLK ublk, VOXEL_MASK_8 voxel_mask,
                             UBLK_NEIGHBOR_COLLECTOR collector, 
                             asINT8 prior_indices[N_SOLVERS],
                             BOOLEAN near_surface_p,
                             asINT32 nth_uds = -1)
  
{
  
  asINT32 n_dims = sim.num_dims;

#define at_variable(var, voxel)                                       \
  collector->extract_voxel_variable(var, ublk, voxel, prior_indices, nth_uds)
  //collector->set_DX_to_one();

  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();
  ccDOTIMES(voxel, N_VOXELS_8) {
    if (voxel_mask.test(voxel)) {
      // Loop over upto three axis directions, and determine neighbors.
      if (ublk->all_neighbors_voxel_mask.test(voxel)) {
        // Simple case: voxel has all neighbors at same scale and all PBL factors are 0
        ccDOTIMES(axis, sim.num_dims) {
          TAGGED_UBLK forward_neighbor, backward_neighbor;
          asINT32 neighbor_voxel;
          auINT32 forward_face = stp_axis_to_pos_face(axis);
          auINT32 backward_face = stp_axis_to_neg_face(axis);
          same_scale_voxel_neighbors_on_axis(axis, ublk,
                                             voxel, neighbor_voxel,
                                             forward_neighbor,
                                             backward_neighbor);
          UBLK forward_ublk = forward_neighbor.ublk();
          UBLK backward_ublk = backward_neighbor.ublk();


          
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
          ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
            sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
            *forward_var = collector->extract_voxel_variable(v, forward_ublk, neighbor_voxel, prior_indices, nth_uds);
            
            sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
            *backward_var = collector->extract_voxel_variable(v, backward_ublk, neighbor_voxel, prior_indices, nth_uds);
            //if(v==0)
/*             { */
/*               asINT32 v1 = collector->n_variables() + v; */
/*               sdFLOAT *forward_var1 = collector->neighbor_variable_ptr(v1, forward_face, voxel); */
/*               *forward_var1 =  *forward_var; */
/*               sdFLOAT *backward_var1 = collector->neighbor_variable_ptr(v1, backward_face, voxel); */
/*               *backward_var1 = *backward_var; */
/*             } */
            
          } 
        }
      }
      else {
        ccDOTIMES(axis, sim.num_dims) {
          asINT32 neighbor_voxel;
          asINT32 forward_face, backward_face;
          TAGGED_UBLK forward_neighbor, backward_neighbor;
          BOOLEAN is_forward_neighbor_different_scale;
          BOOLEAN is_backward_neighbor_different_scale;
          BOOLEAN is_forward_neighbor_finer_scale;
          BOOLEAN is_backward_neighbor_finer_scale;
          voxel_neighbors_on_axis(axis, ublk, voxel, neighbor_voxel,
                                  forward_face, backward_face,
                                  forward_neighbor, backward_neighbor,
                                  is_forward_neighbor_different_scale,
                                  is_backward_neighbor_different_scale,
                                  is_forward_neighbor_finer_scale,
                                  is_backward_neighbor_finer_scale);

          
          // scale interface is not marked split, however its 8 children can be
          // split. split advect factors from the fine children to VR fine
          // ublks should be used to assess connectivity and magnitude of
          // contributions.
          if (is_forward_neighbor_finer_scale) {
            auINT32 fine_ublk_index = neighbor_voxel_along_axis(voxel, axis);
            sSCALE_BOX_INTERFACE *interface = forward_neighbor.interface();
            TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[fine_ublk_index];
            sdFLOAT n_fine_voxels = 0.0;
            sdFLOAT pfluid_sum = 0.0;
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
              *forward_var = 0;
            }
            if (fine_tagged_ublk.is_ublk_split()) {
              asINT32 fine_blk_idx = voxel;
              sVR_COARSE_INTERFACE_DATA *vr_coarse_data =  ublk->vr_coarse_data();
              UBLK vr_fine_blk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
              sTAGGED_SPLIT_FACTOR for_split_facs[MAX_SPLIT_UBLKS];
              asINT32 n_for_splits =
                collect_fine_split_neighbors(fine_tagged_ublk, vr_fine_blk, axis, 1,
                                             for_split_facs);
              ccDOTIMES(nInt, n_for_splits) {
                sdFLOAT split_fac  = for_split_facs[nInt].neighbor_split_factor;
                TAGGED_UBLK fine_neighbor  = for_split_facs[nInt].tagged_neighbor;
                UBLK fine_ublk = fine_neighbor.ublk();
                DO_VOXELS_IN_MASK(fine_voxel, fine_ublk->fluid_like_voxel_mask) {
                  sdFLOAT pfluid_split_fac = split_fac;

#ifdef PFLUID_SCALING_DISABLED
                  if (fine_ublk->is_near_surface()) {
                    pfluid_split_fac = fine_ublk->surf_geom_data()->pfluids[fine_voxel] * split_fac;
                  }
#endif
                  ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                    sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                    *forward_var += 
                      collector->extract_voxel_variable(v, fine_ublk, fine_voxel, prior_fine.indices, nth_uds) *
                      pfluid_split_fac;
                  }
                  n_fine_voxels += split_fac;
                  pfluid_sum += pfluid_split_fac;
                }
              }
            } else if (fine_tagged_ublk.is_ublk()) {
              UBLK fine_ublk = interface->m_fine_ublks[fine_ublk_index].ublk();
              if (fine_ublk != NULL) {
                // If it is any consolation, you can rest assure that
                //FINE_UBLK is not VR fine 
                // Check voxel mask to see if the neighbor voxel is legit 
                DO_VOXELS_IN_MASK(fine_voxel, fine_ublk->fluid_like_voxel_mask) {
                  sdFLOAT pfluid = 1.0;
                  if (fine_ublk->is_near_surface()) {
                    pfluid = fine_ublk->surf_geom_data()->pfluids[fine_voxel];
                  }
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
                  ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                    sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                    *forward_var +=
                      collector->extract_voxel_variable(v, fine_ublk, fine_voxel, prior_fine.indices, nth_uds) *
                      pfluid;
                  }
                  n_fine_voxels += 1.0;
                  pfluid_sum += pfluid;
                }
              }
            }
            if (n_fine_voxels <= 0) {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              //collector->neighbor_DX[forward_face][voxel] = 0.0;
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                  
                switch (collector->bc_type(v, voxel)) {
                case PDE_BC_TYPE_NORMAL:
                case PDE_BC_TYPE_FLOATING:
                  *forward_var = at_variable(v, voxel);
                  break;
                case PDE_BC_TYPE_FIXED:
                  *forward_var = collector->fixed_variable(v, voxel);
                  break;
                case PDE_BC_TYPE_PARTIAL:
                  *forward_var = at_variable(v, voxel);
                  break;
                }
              }
            } else {
              //sdFLOAT one_over_n_fine_voxels = 1.0F / n_fine_voxels;
              sdFLOAT one_over_n_fine_voxels = 1.0 / pfluid_sum;
              if (near_surface_p) {
                NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
                sdFLOAT one_minus_pbl_p = fabs(surf_data->post_advect_scale_factors[STATE_N(axis)][voxel]);
                sdFLOAT pbl_p = 1.0 - one_minus_pbl_p;
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
                ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                  sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                  *forward_var *= one_over_n_fine_voxels;
                
                  switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *forward_var = pbl_p * collector->fixed_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                  }
                } 
              } else {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
                ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                  sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                  *forward_var *= one_over_n_fine_voxels;
                }
              }
            }
          } else if (forward_neighbor.is_null()) {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            //collector->neighbor_DX[forward_face][voxel] = 0.0;
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
             
              switch (collector->bc_type(v, voxel)) {
              case PDE_BC_TYPE_NORMAL:
              case PDE_BC_TYPE_FLOATING:
                *forward_var = at_variable(v, voxel);
                break;
              case PDE_BC_TYPE_FIXED:
                *forward_var = collector->fixed_variable(v, voxel);
                break;
              case PDE_BC_TYPE_PARTIAL:
                *forward_var = at_variable(v, voxel);
                break;
              }
            }
	  } else if (!forward_neighbor.is_ublk_split()) {
	    UBLK forward_ublk = forward_neighbor.ublk();
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
	    ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
	      sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
	      *forward_var = collector->extract_voxel_variable(v, forward_ublk, neighbor_voxel, prior_indices, nth_uds);
	    } 
	    if (near_surface_p) {
	      NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
              sdFLOAT one_minus_pbl_p = surf_data->post_advect_scale_factors[STATE_N(axis)][voxel];
              sdFLOAT pbl_p = 1.0 - one_minus_pbl_p;
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *forward_var = pbl_p * collector->fixed_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                }
              }	      
	    }
          } else { //forward ublk is split
            sTAGGED_SPLIT_FACTOR forward_split_facs[MAX_SPLIT_UBLKS];
            asINT32 n_forward_splits =
              tagged_neighbor_from_split_ublk_site(forward_neighbor,
                                                   ublk->advect_from_split_mask(),
                                                   voxel, STATE_P(axis),
                                                   split_advect_info,
                                                   ublk->m_split_tagged_instance.get(),
                                                   forward_split_facs);
          
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
              *forward_var = 0;
            }
            sdFLOAT split_fac_sum = 0.0;
            ccDOTIMES(nInt, n_forward_splits) {
              sdFLOAT split_fac = forward_split_facs[nInt].neighbor_split_factor;
              forward_neighbor = forward_split_facs[nInt].tagged_neighbor;
              split_fac_sum += split_fac;

              UBLK forward_ublk = forward_neighbor.ublk();
            
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                *forward_var += split_fac*collector->extract_voxel_variable(v, forward_ublk, neighbor_voxel, prior_indices, nth_uds);
              }
            }
            //printf("sum of split factors %e\n", split_fac_sum);
            if (near_surface_p) {
              NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
              sdFLOAT one_minus_pbl_p = surf_data->post_advect_scale_factors[STATE_N(axis)][voxel];
              sdFLOAT pbl_p = 1.0 - one_minus_pbl_p;

#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *forward_var = collector->neighbor_variable_ptr(v, forward_face, voxel);
                switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *forward_var = pbl_p * at_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *forward_var = pbl_p * collector->fixed_variable(v, voxel) + one_minus_pbl_p * *forward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                }
              }
            }
          }

          if (is_backward_neighbor_finer_scale) {
            auINT32 fine_ublk_index = neighbor_voxel_along_axis(voxel, axis);
            sSCALE_BOX_INTERFACE *interface = backward_neighbor.interface();
            TAGGED_UBLK fine_tagged_ublk = interface->m_fine_ublks[fine_ublk_index];
            sdFLOAT n_fine_voxels = 0;
            sdFLOAT pfluid_sum = 0.0;
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
              *backward_var = 0;
            }
            if (fine_tagged_ublk.is_ublk_split()) {              
              asINT32 fine_blk_idx = voxel;
              sVR_COARSE_INTERFACE_DATA *vr_coarse_data =  ublk->vr_coarse_data();
              UBLK vr_fine_blk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
              sTAGGED_SPLIT_FACTOR bac_split_facs[MAX_SPLIT_UBLKS];
              asINT32 n_bac_splits =
                collect_fine_split_neighbors(fine_tagged_ublk, vr_fine_blk, axis, -1,
                                             bac_split_facs);
              ccDOTIMES(nInt, n_bac_splits) {
                sdFLOAT split_fac  = bac_split_facs[nInt].neighbor_split_factor;
                TAGGED_UBLK fine_neighbor  = bac_split_facs[nInt].tagged_neighbor;
                UBLK fine_ublk = fine_neighbor.ublk();
                DO_VOXELS_IN_MASK(fine_voxel, fine_ublk->fluid_like_voxel_mask) {
                  sdFLOAT pfluid_split_fac = split_fac;
#ifdef PFLUID_SCALING_DISABLED
                  if (fine_ublk->is_near_surface()) {
                    pfluid_split_fac = fine_ublk->surf_geom_data()->pfluids[fine_voxel] * split_fac;
                  }
#endif
                  ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                    sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                    *backward_var += 
                      collector->extract_voxel_variable(v, fine_ublk, fine_voxel, prior_fine.indices, nth_uds) *
                      pfluid_split_fac;
                  }
                  n_fine_voxels += split_fac;
                  pfluid_sum += pfluid_split_fac;
                }
              }
            } else if (fine_tagged_ublk.is_ublk()) {
              UBLK fine_ublk = fine_tagged_ublk.ublk();
              if (fine_ublk != NULL) {
                // If it is any consolation, you can rest assure that
                //  FINE_UBLK is not VR fine 
                // Check voxel mask to see if the neighbor voxel is legit 
                DO_VOXELS_IN_MASK(fine_voxel, fine_ublk->fluid_like_voxel_mask) {
                  sdFLOAT pfluid = 1.0;
                  if (fine_ublk->is_near_surface()) {
                    pfluid = fine_ublk->surf_geom_data()->pfluids[fine_voxel];
                  }
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
                  ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                    sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                    *backward_var +=
                      collector->extract_voxel_variable(v, fine_ublk, fine_voxel, prior_fine.indices, nth_uds) *
                      pfluid;
                  }
                  n_fine_voxels += 1.0;
                  pfluid_sum += pfluid;
                }
              }
            }
            if (n_fine_voxels <= 0) {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              //collector->neighbor_DX[backward_face][voxel] = 0.0;
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                  
                switch (collector->bc_type(v, voxel)) {
                case PDE_BC_TYPE_NORMAL:
                case PDE_BC_TYPE_FLOATING:
                  *backward_var = at_variable(v, voxel);
                  break;
                case PDE_BC_TYPE_FIXED:
                  *backward_var = collector->fixed_variable(v, voxel);
                  break;
                case PDE_BC_TYPE_PARTIAL:
                  *backward_var = at_variable(v, voxel);
                  break;
                }
              }
            } else {
              sdFLOAT one_over_n_fine_voxels = 1.0 / pfluid_sum;
              //sdFLOAT one_over_n_fine_voxels = 1.0F / n_fine_voxels;
              if (near_surface_p) {
                NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
                sdFLOAT one_minus_pbl_n = fabs(surf_data->post_advect_scale_factors[STATE_P(axis)][voxel]);
                sdFLOAT pbl_n = 1.0 - one_minus_pbl_n;
#if defined(INTEL_COMPILER)
#pragma unroll
#endif   
                ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                  sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                  *backward_var *= one_over_n_fine_voxels;
                  
                  switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *backward_var = pbl_n * collector->fixed_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                  }
                }
              } else {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
                ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                  sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                  *backward_var *= one_over_n_fine_voxels;
                }
              }
            }
          } else if (backward_neighbor.is_null()) {
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            //collector->neighbor_DX[backward_face][voxel] = 0.0;
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
              
              switch (collector->bc_type(v, voxel)) {
              case PDE_BC_TYPE_NORMAL:
              case PDE_BC_TYPE_FLOATING:
                *backward_var = at_variable(v, voxel);
                break;
              case PDE_BC_TYPE_FIXED:
                *backward_var = collector->fixed_variable(v, voxel);
                break;
              case PDE_BC_TYPE_PARTIAL:
                *backward_var = at_variable(v, voxel);
                break;
              }
            }
	  } else if (!backward_neighbor.is_ublk_split()) {
	    UBLK backward_ublk = backward_neighbor.ublk();
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
	      *backward_var = collector->extract_voxel_variable(v, backward_ublk, neighbor_voxel, prior_indices, nth_uds);
	    }
	    if (near_surface_p) {
              NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
              sdFLOAT one_minus_pbl_n = fabs(surf_data->post_advect_scale_factors[STATE_P(axis)][voxel]);
              sdFLOAT pbl_n = 1.0 - one_minus_pbl_n;

#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *backward_var = pbl_n * collector->fixed_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                }
              }
	    }
          } else { // ublk is split
            sTAGGED_SPLIT_FACTOR backward_split_facs[MAX_SPLIT_UBLKS];
            asINT32 n_backward_splits =
              tagged_neighbor_from_split_ublk_site(backward_neighbor,
                                                   ublk->advect_from_split_mask(),
                                                   voxel, STATE_N(axis),
                                                   split_advect_info,
                                                   ublk->m_split_tagged_instance.get(),
                                                   backward_split_facs);
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
            ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
              sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
              *backward_var = 0;
            }

            sdFLOAT split_fac_sum = 0.0;
            ccDOTIMES(nInt, n_backward_splits) {
              sdFLOAT split_fac = backward_split_facs[nInt].neighbor_split_factor;
              backward_neighbor = backward_split_facs[nInt].tagged_neighbor;
              split_fac_sum += split_fac;
              UBLK backward_ublk = backward_neighbor.ublk();
              // If we really had a coarser neighbor (as opposed to a VR fine
              //at the same scale), we would want to use coarse_neighbor_voxel.
              // asINT32 coarse_neighbor_voxel =
              //   neighbor_voxel_along_axis(vr_topology->coarse_voxel(), axis);
#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                *backward_var += split_fac*collector->extract_voxel_variable(v, backward_ublk, neighbor_voxel, prior_indices, nth_uds);
              }
            }
            //printf("sum of split factors %e\n", split_fac_sum);
            if (near_surface_p) {
              sdFLOAT one_minus_pbl_n = 1.0;
              NEAR_UBLK_LB_DATA surf_data = ublk->surf_lb_data();
              one_minus_pbl_n = fabs(surf_data->post_advect_scale_factors[STATE_P(axis)][voxel]);
              sdFLOAT pbl_n = 1.0 - one_minus_pbl_n;

#if defined(INTEL_COMPILER)
#pragma unroll
#endif
              ccDOTIMES(v, collector->n_variables()) { // force compiler to unroll this loop
                sdFLOAT *backward_var = collector->neighbor_variable_ptr(v, backward_face, voxel);
                switch (collector->bc_type(v, voxel)) {
                  case PDE_BC_TYPE_NORMAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_PARTIAL:
                    *backward_var = pbl_n * at_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FIXED:
                    *backward_var = pbl_n * collector->fixed_variable(v, voxel) + one_minus_pbl_n * *backward_var;
                    break;
                  case PDE_BC_TYPE_FLOATING:
                    break;
                }
              }
            }
          }
        } 
      }
    }
  }
}

#endif
