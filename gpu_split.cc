/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "ublk.h"
#include "split_data.h"
#include "box_advect_gen_headers.h"
#include "gpu_host_include.h"

UBLK_OFFSET_CONTRIBUTIONS_MAP& get_advection_map() {
  static UBLK_OFFSET_CONTRIBUTIONS_MAP advection_map;
  if (advection_map.empty()) {
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
    compute_ublk_neighbor_contributions(advection_map,
                                        sim.is_2d(),
                                        STP_LATTICE_D19);

#elif BUILD_D39_LATTICE
    compute_ublk_neighbor_contributions(advection_map,
                                        sim.is_2d(),
                                        STP_LATTICE_D39);
#endif      
  }
  return advection_map;
}

template<SPLIT_FACTOR_TYPE SFT>
static
VOID init_split_factor_start_indices_for_ublk(UBLK ublk,
                                              typename SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>::SPLIT_NBR_MASK& split_neighbor_mask,
                                              std::vector<sINT16>& nbr_ublk_factor_start_index);

template<>
VOID init_split_factor_start_indices_for_ublk<SPEED1>(UBLK ublk,
                                                      tBITSET<N_STATES, uINT32>& split_neighbor_mask,
                                                      std::vector<sINT16>& nbr_ublk_factor_start_index) {

  auto& box_access = ublk->m_box_access;
  UBLK_OFFSET_CONTRIBUTIONS_MAP& neighbor_contributions = get_advection_map();
  sSPLIT_ADVECT_FACTORS* split_advect_factors = ublk->get_split_advect_factors();

  sINT16 nbr_factor_start_index = 0;
  asINT32 state_direction = 0;

  //We start with the non moving state, and work our way in
  //ascending order of moving states, hence the awkward initialization
  //of loop variable i
  for ( int i = -1; i < N_MOVING_STATES; i++) {

    UBLK_OFFSET_TUPLE ublk_offset;

    if ( i == -1 ) { //Non-moving states
      ublk_offset = UBLK_OFFSET_TUPLE(0, 0, 0);
      state_direction = V_0_0_0;
    } else {
      ublk_offset = UBLK_OFFSET_TUPLE(state_vx(i), state_vy(i), state_vz(i));
      state_direction = i;
    }

    //We can loop over all lattice directions, but not all offsets are keys
    //for example in 2D, not all ublk offsets are valid
    if (neighbor_contributions.count(ublk_offset)) {

      TAGGED_UBLK neighbor_tagged_ublk = box_access.neighbor_ublk(ublk_offset);

      if ( neighbor_tagged_ublk.is_ublk_scale_interface() ||
           (!neighbor_tagged_ublk.is_ublk()) ||
           (!neighbor_tagged_ublk.is_ublk_split())) {
        continue;
      };

      split_neighbor_mask.set(state_direction);

      nbr_ublk_factor_start_index.push_back(nbr_factor_start_index);
      
      nbr_factor_start_index +=
        neighbor_tagged_ublk.split_ublks()->num_split_ublks() * neighbor_contributions[ublk_offset].size();
      

    } //count
  } // for
}

template<>
VOID init_split_factor_start_indices_for_ublk<SPEED2>(UBLK ublk,
                                                      tBITSET<N_CARDINAL_DIRECTIONS, uINT8>& split_neighbor_mask,
                                                      std::vector<sINT16>& nbr_ublk_factor_start_index) {
  asINT32 start_index = 0;

  auto& box_access = ublk->box_access();

  for ( asINT32 axis = 0 ; axis < sim.num_dims; axis++ ) {

    TAGGED_UBLK forward_neighbor = box_access.forward_neighbor(axis);
    TAGGED_UBLK backward_neighbor = box_access.backward_neighbor(axis);

    if (forward_neighbor.is_ublk() && forward_neighbor.is_ublk_split()) {
      split_neighbor_mask.set(2 * axis);
      nbr_ublk_factor_start_index.push_back(start_index);
      start_index += N_VOXELS_8 * forward_neighbor.split_ublks()->num_split_ublks();
    }

    if (backward_neighbor.is_ublk() && backward_neighbor.is_ublk_split()) {
      split_neighbor_mask.set(2 * axis + 1);
      nbr_ublk_factor_start_index.push_back(start_index);
      start_index += N_VOXELS_8 * backward_neighbor.split_ublks()->num_split_ublks();
    }
  }  
}

template<SPLIT_FACTOR_TYPE SFT>
struct tUBLK_SPLIT_FACTOR_AUXILLARY_DATA {
  
  using TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>;
  using SPLIT_MASK = typename TRAITS::SPLIT_NBR_MASK;
  
  SPLIT_MASK m_split_neighbor_mask;
  std::vector<sINT16> m_nbr_ublk_factor_start_index;
};

using sUBLK_SPEED1_SPLIT_FACTOR_AUXILLARY_DATA = tUBLK_SPLIT_FACTOR_AUXILLARY_DATA<SPEED1>;
using sUBLK_SPEED2_SPLIT_FACTOR_AUXILLARY_DATA = tUBLK_SPLIT_FACTOR_AUXILLARY_DATA<SPEED2>;

template<SPLIT_FACTOR_TYPE SFT, typename SPLIT_FACTOR_COMPONENT_TYPE = tSPLIT_FACTOR_COMPONENT_TYPE<SFT>>
static VOID
set_split_factor_auxillary_data_and_component_counts(std::array<tUBLK_SPLIT_FACTOR_AUXILLARY_DATA<SFT>, 8>& split_auxillary_data,
                                                     unsigned (&n_elements) [(int) SPLIT_FACTOR_COMPONENT_TYPE::N_COMPONENTS],
                                                     const std::array<sUBLK*,8>& ublks_to_pack,
                                                     int n_ublks_to_pack) {
  
  for (int u = 0; u < n_ublks_to_pack; u++) {
    UBLK child_ublk = ublks_to_pack[u];
    auto& child_ublk_split_auxillary_data = split_auxillary_data[u];

    init_split_factor_start_indices_for_ublk<SFT>(child_ublk,
                                                  child_ublk_split_auxillary_data.m_split_neighbor_mask,
                                                  child_ublk_split_auxillary_data.m_nbr_ublk_factor_start_index);
    
    auto child_split_advect_factors = child_ublk->get_split_advect_factors();
    if (child_split_advect_factors) {
      n_elements[(int) SPLIT_FACTOR_COMPONENT_TYPE::NBR_UBLK_FACTORS_START_INDICES] +=
        child_ublk_split_auxillary_data.m_nbr_ublk_factor_start_index.size();
      
      n_elements[(int) SPLIT_FACTOR_COMPONENT_TYPE::FACTORS] +=
        child_split_advect_factors->get<SFT>().num_factors();

#if DEBUG_SPLIT_ADVECT_FACTORS
      n_elements[(int) SPLIT_FACTOR_COMPONENT_TYPE::DEBUG_DATA] +=
        child_split_advect_factors->get<SFT>().num_factors();
#endif
    }
  }  
}

template<SPLIT_FACTOR_TYPE SFT>
static VOID init_mblk_split_factors(const std::array<sUBLK*,8>& ublks_to_pack,
                                        int n_ublks_to_pack,
                                        sHMBLK& mega,
                                        std::array<tUBLK_SPLIT_FACTOR_AUXILLARY_DATA<SFT>, 8>& split_auxillary_data) {

  using TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>;
  using SPLIT_MASK = typename TRAITS::SPLIT_NBR_MASK;  
  size_t n_factors_copied = 0;
  size_t n_nbr_ublk_factors_start_indices_copied = 0;
  auto split_advect_factors = mega.get_split_advect_factors();
  sFLOAT* factors = split_advect_factors->get<SFT>().factors();
  sSPLIT_FACTOR_DEBUG_INFO* debug_data = split_advect_factors->get<SFT>().debug_data();
  SPLIT_MASK* split_masks = split_advect_factors->get<SFT>().split_neighbor_masks();
  sINT16* nbr_ublk_factors_start_indices = split_advect_factors->get<SFT>().nbr_ublk_factors_start_indices();
  
  for (int u = 0; u < n_ublks_to_pack; u++) {
    UBLK child_ublk = ublks_to_pack[u];

    auto child_split_advect_factors = child_ublk->get_split_advect_factors();
    unsigned num_child_factors = child_split_advect_factors?
                                 child_split_advect_factors->get<SFT>().num_factors() : 0;

    auto& child_ublk_auxillary_data = split_auxillary_data[u];
    
    if (num_child_factors && factors) {
      split_advect_factors->get<SFT>().set_split_nbr_mask_for_child_ublk(u, child_ublk_auxillary_data.m_split_neighbor_mask);
      split_advect_factors->get<SFT>().set_nbr_ublk_factors_start_index_offsets(u, n_nbr_ublk_factors_start_indices_copied);
      
      {//factors
        memcpy(factors + n_factors_copied,
               child_split_advect_factors->get<SFT>().factors(),
               num_child_factors * sizeof(sFLOAT));
      }

#if DEBUG_SPLIT_ADVECT_FACTORS
      {//debug_data
        assert(debug_data);
        memcpy(debug_data + n_factors_copied,
               child_split_advect_factors->get<SFT>().debug_data(),
               num_child_factors * sizeof(sSPLIT_FACTOR_DEBUG_INFO));
      }
#endif
      
      {//ublk_factor_start_indices
        size_t n_indices = child_ublk_auxillary_data.m_nbr_ublk_factor_start_index.size();
        sINT16* start_indices = nbr_ublk_factors_start_indices + n_nbr_ublk_factors_start_indices_copied;
        for (int i = 0; i < n_indices; i++) {
          //Add n_factors_copied so that the start indices reflect that they are for the MBLK
          start_indices[i] = child_ublk_auxillary_data.m_nbr_ublk_factor_start_index[i] +
                             n_factors_copied;
        }
      }

      //Update this here since indices depend on the prior value
      n_factors_copied += num_child_factors;
      n_nbr_ublk_factors_start_indices_copied += child_ublk_auxillary_data.m_nbr_ublk_factor_start_index.size();
    }   
  }
}

static VOID init_mblk_split_info_single_instance_data(const std::array<sUBLK*,8>& ublks_to_pack,
                                                          int n_ublks_to_pack,
                                                          sHMBLK& mega) {
  
  for (int u = 0; u < n_ublks_to_pack; u++) {
    UBLK child_ublk = ublks_to_pack[u];
    asINT32 single_instance_index = get_single_instance_for_split_ublk(child_ublk, 0 /*child ublk*/);
    if (single_instance_index == INVALID_SPLIT_INSTANCE) {
      mega.m_split_info->set_interacts_with_single_instance(u, 0);
      mega.m_split_info->set_split_instance_index(u, 0);       
    } else {
      mega.m_split_info->set_interacts_with_single_instance(u, 1);
      mega.m_split_info->set_split_instance_index(u, single_instance_index);
    }       
  }  
}

static VOID init_mblk_split_info(const std::array<sUBLK*,8>& ublks_to_pack,
                                     int n_ublks_to_pack,
                                     sHMBLK& mega,
                                     std::array<sUBLK_SPEED1_SPLIT_FACTOR_AUXILLARY_DATA, 8>& speed1_auxillary_data,
                                     std::array<sUBLK_SPEED2_SPLIT_FACTOR_AUXILLARY_DATA, 8>& speed2_auxillary_data) {
  init_mblk_split_info_single_instance_data(ublks_to_pack, n_ublks_to_pack, mega);
  init_mblk_split_factors<SPEED1>(ublks_to_pack, n_ublks_to_pack, mega, speed1_auxillary_data);
  init_mblk_split_factors<SPEED2>(ublks_to_pack, n_ublks_to_pack, mega, speed2_auxillary_data);
}

VOID init_mblk_split_data(const std::array<sUBLK*,8>& ublks_to_pack,
                              int n_ublks_to_pack,
                              sHMBLK& mega) {

  BOOLEAN allocate_split_info = std::any_of(ublks_to_pack.begin(),
                                            ublks_to_pack.begin() + n_ublks_to_pack,
                                            [] (sUBLK* u) {
                                              return !u->has_empty_split_info();
                                            });
  
  if (allocate_split_info) {

    unsigned n_speed1_elements[(int) SPEED1_COMPONENTS::N_COMPONENTS] = {0};
    unsigned n_speed2_elements[(int) SPEED2_COMPONENTS::N_COMPONENTS] = {0};
    std::array<sUBLK_SPEED1_SPLIT_FACTOR_AUXILLARY_DATA, 8> child_ublk_speed1_auxillary_data;
    std::array<sUBLK_SPEED2_SPLIT_FACTOR_AUXILLARY_DATA, 8> child_ublk_speed2_auxillary_data;

    set_split_factor_auxillary_data_and_component_counts<SPEED1>(child_ublk_speed1_auxillary_data,
                                                                 n_speed1_elements,
                                                                 ublks_to_pack,
                                                                 n_ublks_to_pack);

    set_split_factor_auxillary_data_and_component_counts<SPEED2>(child_ublk_speed2_auxillary_data,
                                                                 n_speed2_elements,
                                                                 ublks_to_pack,
                                                                 n_ublks_to_pack);    

    size_t split_info_bytes = tSPLIT_INFO<64>::SIZE(n_speed1_elements, n_speed2_elements);
    auto alloc_type = HD_SLAB_ALLOCATOR_TYPES::MBLK_BOX;
    mega.m_split_info = (tSPLIT_INFO<64>*) hd_slab_malloc(alloc_type,
                                                          split_info_bytes,
                                                          32);
    memset(mega.m_split_info, 0, split_info_bytes);
    {//Note that this step is critical and must be done before split factors can be initialized
      auto split_advect_factors = mega.get_split_advect_factors();
      split_advect_factors->set_n_component_elements(n_speed1_elements, n_speed2_elements);
    }    
    init_mblk_split_info(ublks_to_pack, n_ublks_to_pack, mega,
                             child_ublk_speed1_auxillary_data,
                             child_ublk_speed2_auxillary_data);
  }
}
