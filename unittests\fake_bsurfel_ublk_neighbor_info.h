
struct sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO 
{
  private:
    friend class cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER;
  uINT8  m_voxel;
  dFLOAT m_interpolation_weight; 
  dFLOAT m_distribution_weight;
  dFLOAT m_normalizing_weight;
  dFLOAT m_bsurfel_contribution_to_ublk[3];
  dFLOAT m_voxel_volume;
  dFLOAT m_pfluid;
  sdFLOAT m_lb_next_vel[3];
  sdFLOAT m_lb_next_density;
  sdFLOAT m_t_next_fluid_temp;
  sdFLOAT m_ke_prior_turb_ke;
  sdFLOAT m_ke_prior_turb_df;

  public:

  // constructor
  sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO() = default;
  sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO(const sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO&) = default;

  UBLK ublk() { return nullptr; }
  uINT8 voxel() { return m_voxel; }

  dFLOAT interpolation_weight() { return m_interpolation_weight; }
  dFLOAT distribution_weight() { return m_distribution_weight; }

  void add_bsurfel_contribution_to_ublk(dFLOAT computed_body_force[3]) 
  {for(int i=0;i<3;i++) { computed_body_force[i] = m_bsurfel_contribution_to_ublk[i]; } }

  dFLOAT normalizing_weight() { return m_normalizing_weight; }

  dFLOAT voxel_volume(asINT32 bsurfel_scale)
  {
    static const bool is_2d = sim.is_2d();
    return m_voxel_volume;
  }

  sdFLOAT lb_next_density() { 
    return m_lb_next_density;
  }

  dFLOAT pfluid() { return m_pfluid; }

  struct sINTERPOLATE_FLUID_PROPERTIES_HELPER { };

  sINTERPOLATE_FLUID_PROPERTIES_HELPER interpolate_fluid_properties_helper() { return sINTERPOLATE_FLUID_PROPERTIES_HELPER(); }

  sdFLOAT lb_next_vel(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h, int axis)
  {
    return m_lb_next_vel[axis];
  }

  sdFLOAT lb_next_density(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h) { 
    return m_lb_next_density;
  }

  sdFLOAT t_next_fluid_temp(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h) {
    return m_t_next_fluid_temp;
  }

  sdFLOAT ke_prior_turb_ke(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h) {
    return m_ke_prior_turb_ke;
  }

  sdFLOAT ke_prior_turb_df(const sINTERPOLATE_FLUID_PROPERTIES_HELPER& h) {
    return m_ke_prior_turb_df;
  }

}; 

class cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER
{
  sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO b;
  using cB = cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER;

public:

  cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER()
  {
    init();
  }

  cB& init()
  {
    b.m_voxel = 1;
    b.m_interpolation_weight = 1.0;
    b.m_distribution_weight = 1.0;
    b.m_normalizing_weight = 1.0;
    b.m_bsurfel_contribution_to_ublk[0] = 1.0;
    b.m_bsurfel_contribution_to_ublk[1] = 1.0;
    b.m_bsurfel_contribution_to_ublk[2] = 1.0;
    b.m_voxel_volume = 1.0;
    b.m_lb_next_density = 1.0;
    b.m_pfluid = 1.0;
    b.m_lb_next_vel[0] = 1.0;
    b.m_lb_next_vel[1] = 1.0;
    b.m_lb_next_vel[2] = 1.0;
    b.m_t_next_fluid_temp = 1.0;
    b.m_ke_prior_turb_ke = 1.0;
    b.m_ke_prior_turb_df = 1.0;
    return *this;
  }

  cB& voxel(uINT8 v) { b.m_voxel = v; return *this; }
  cB& interpolation_weight(dFLOAT w) { b.m_interpolation_weight = w; return *this;}
  cB& distribution_weight(dFLOAT w) { b.m_distribution_weight = w; return *this;}
  cB& normalizing_weight(dFLOAT w) { b.m_normalizing_weight = w; return *this;}
  cB& bsurfel_contribution_to_ublk(dFLOAT x, dFLOAT y, dFLOAT z) {
    b.m_bsurfel_contribution_to_ublk[0] = x;
    b.m_bsurfel_contribution_to_ublk[1] = y;
    b.m_bsurfel_contribution_to_ublk[2] = z;
    return *this;
  }
  cB& voxel_volume(dFLOAT v) { b.m_voxel_volume = v; return *this; }
  cB& lb_next_density(dFLOAT v) { b.m_lb_next_density = v; return *this; }
  cB& pfluid(dFLOAT p) { b.m_pfluid = p; return *this; }

  cB& lb_next_vel(sdFLOAT x, sdFLOAT y, sdFLOAT z)
  {
    b.m_lb_next_vel[0] = x;
    b.m_lb_next_vel[1] = y;
    b.m_lb_next_vel[2] = z;
    return *this;
  }

  cB& lb_next_density(sdFLOAT a) { b.m_lb_next_density = a; return *this; }
  cB& t_next_fluid_temp(sdFLOAT t) { b.m_t_next_fluid_temp = t; return *this; }
  cB& ke_prior_turb_ke(sdFLOAT k) { b.m_ke_prior_turb_ke = k; return *this; }
  cB& ke_prior_turb_df(sdFLOAT k) { b.m_ke_prior_turb_df = k; return *this; }
  sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO done() { return b; }

};
