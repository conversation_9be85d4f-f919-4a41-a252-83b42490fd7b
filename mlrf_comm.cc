/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Communication for mlrf surfels
 *
 * Vinit Gupta, Exa Corporation
 * Created Thu Nov 30, 2017
 *--------------------------------------------------------------------------*/

#include "mlrf_comm.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "mlrf.h"

VOID cMLRF_COMM::process_posted_pre_dyn_recvs() {
  // The send_req flag is cleared when the receives are posted, and should not be set by the sim thread until after they have been completed
  // The completed flag is cleared when the receives are posted and set below
  if (!m_pre_dyn_recvs_completed) {
    BOOLEAN all_predyn_receives_completed = TRUE;
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_pre_dyn_recv_req.m_timestep, FINEST_SCALE);
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        if (!group->complete_receives(MLRF_PREDYN))
          all_predyn_receives_completed = FALSE;

        if(sim.is_particle_model) {
#if !BUILD_GPU          
          if (!group->complete_parcel_count_receives()) { //Only the parcel counts are need at this stage.
            all_predyn_receives_completed = FALSE;
          }
#else
          msg_internal_error("Particle modelling with LRFs not supported on GPUs.");
#endif          
        }
      }
    }
    if (all_predyn_receives_completed) {
      m_pre_dyn_recvs_completed = true;

      if(sim.is_particle_model) {
#if !BUILD_GPU        
        //once the parcel counts have been received, allocate buffers and post the parcel state receives.
        SCALE coarsest_active_scale = compute_coarsest_active_scale(m_pre_dyn_recv_req.m_timestep, FINEST_SCALE);
        for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
          DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
            group->post_parcel_state_receives(); 
          }
        }
#else
        msg_internal_error("Particle modelling with LRFs not supported on GPUs.");
#endif        
      }

      g_strand_mgr.mlrf_recv_update_dcntr(SID_MLRF_COMM_TYPE, m_pre_dyn_recv_req.m_timestep );
      LOG_MSG("RECV_DEPEND").printf("Completed pre dyn recv current time %d",m_pre_dyn_recv_req.m_timestep);
    }
  }
}

VOID cMLRF_COMM::process_posted_post_dyn_recvs() {
  // The send_req flag is cleared when the receives are posted, and should not be set by the sim thread until after they have been completed
  // The completed flag is cleared when the receives are posted and set below
  if (!m_post_dyn_recvs_completed) {
    if (!m_pre_dyn_recvs_completed)
      return;
    BOOLEAN all_postdyn_receives_completed = TRUE;
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_post_dyn_recv_req.m_timestep, FINEST_SCALE);
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        if (!group->complete_receives(MLRF_POSTDYN))
          all_postdyn_receives_completed = FALSE;

        if(sim.is_particle_model) {
#if !BUILD_GPU          
          if (!group->complete_parcel_state_receives())
            all_postdyn_receives_completed = FALSE;
#else
          msg_internal_error("Particle modelling with LRFs not supported on GPUs.");
#endif          
        }

      }
    }
    if (all_postdyn_receives_completed) {
      m_post_dyn_recvs_completed = true;
      g_strand_mgr.mlrf_recv_update_dcntr(MCD_MLRF_COMM_TYPE,m_post_dyn_recv_req.m_timestep);
      LOG_MSG("RECV_DEPEND").printf("Completed mass correction recv current time %d", m_post_dyn_recv_req.m_timestep);
    }
  }
}

VOID cMLRF_COMM::process_requests() {
  if (m_pre_dyn_recv_req.m_post_req_flag.load(std::memory_order_acquire)) {
    //msg_print("pre_dyn_recv_req timestep %d", m_pre_dyn_recv_req.m_timestep);
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_pre_dyn_recv_req.m_timestep, FINEST_SCALE);
    TIMESTEP timestep = m_pre_dyn_recv_req.m_timestep;
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      auINT32 even_odd_mask = (sim_is_timestep_odd(scale, timestep) ?
                               STP_PROCESS_ON_ODD_TIMES
                               : STP_PROCESS_ON_EVEN_TIMES);
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        LOG_MSG("RECV_DEPEND").printf("Posting pre_dyn_recv_req timestep %d scale %d even_odd_mask %d", timestep, scale, even_odd_mask);
        if (group->m_even_odd & even_odd_mask) {
          group->post_pre_dynamics_receives();
        }
        if(sim.is_particle_model) {
#if !BUILD_GPU          
          group->post_parcel_count_receives();
#else
          msg_internal_error("Particle modelling with LRFs not supported on GPUs.");
#endif          
        }
      }
    }
    m_pre_dyn_recvs_completed = false;
    m_pre_dyn_recv_req.m_post_req_flag.store(false, std::memory_order_release);
  }

  if (m_post_dyn_recv_req.m_post_req_flag.load(std::memory_order_acquire)) {
    //msg_print("post_dyn_recv_req timestep %d", m_post_dyn_recv_req.m_timestep);
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_post_dyn_recv_req.m_timestep, FINEST_SCALE);
    TIMESTEP timestep = m_post_dyn_recv_req.m_timestep;
    //msg_print("post_dyn_recv_req timestep %d coarsest %d",timestep,coarsest_active_scale);
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      auINT32 even_odd_mask = (sim_is_timestep_odd(scale, timestep) ?
                               STP_PROCESS_ON_ODD_TIMES
                               : STP_PROCESS_ON_EVEN_TIMES);
      LOG_MSG("RECV_DEPEND").printf("Posting post_dyn_recv_req timestep %d scale %d even_odd_mask %d", timestep, scale, even_odd_mask);
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        if (group->m_even_odd & even_odd_mask) {
          group->post_post_dynamics_receives();
        }
      }
    }

    m_post_dyn_recvs_completed = false;
    m_post_dyn_recv_req.m_post_req_flag.store(false, std::memory_order_release);
  }

  if (m_pre_dyn_send_req.m_post_req_flag.load(std::memory_order_acquire)) {
    //msg_print("pre_dyn_send_req timestep %d", m_pre_dyn_send_req.m_timestep);
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_pre_dyn_send_req.m_timestep, FINEST_SCALE);
    TIMESTEP timestep = m_pre_dyn_send_req.m_timestep;
    TIMESTEP_PARITY parity = timestep & 0x1;
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      auINT32 even_odd_mask = (sim_is_timestep_odd(scale, timestep) ?
                               STP_PROCESS_ON_ODD_TIMES
                               : STP_PROCESS_ON_EVEN_TIMES);
      //msg_print("  pre_dyn_send_req timestep %d scale %d even_odd_mask %d",timestep, scale, even_odd_mask);
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        ACTIVE_SOLVER_MASK active_solver_mask = g_timescale.m_active_solver_masks[parity][scale];
        ACTIVE_SOLVER_MASK even_active_solver_mask = g_timescale. m_even_active_solver_masks[parity][scale];
        ACTIVE_SOLVER_MASK odd_active_solver_mask = g_timescale. m_odd_active_solver_masks[parity][scale];
        if (group->m_even_odd == STP_PROCESS_ON_ALL_TIMES) {
          group->copy_to_pre_dynamics_send_buffers(active_solver_mask);
        } else if ((even_active_solver_mask != 0)
                   && (group->m_even_odd == STP_PROCESS_ON_EVEN_TIMES)) {
          group->copy_to_pre_dynamics_send_buffers(even_active_solver_mask);
        } else if ((odd_active_solver_mask != 0)
                   && (group->m_even_odd == STP_PROCESS_ON_ODD_TIMES)) {
          group->copy_to_pre_dynamics_send_buffers(odd_active_solver_mask);
        }

        LOG_MSG("RECV_DEPEND").printf("Posting pre_dyn_send_req timestep %d scale %d even_odd_mask %d", timestep, scale, even_odd_mask);
        if (group->m_even_odd & even_odd_mask) {
          group->post_pre_dynamics_sends();
        }
        if(sim.is_particle_model) {
#if !BUILD_GPU          
          group->count_parcels_and_fill_send_buffers();
          group->post_parcel_sends(); //Parcel states and parcel count message sends are posted at the same time as the pre dyn mlrf messages.
#else
          msg_internal_error("Particle modelling with LRFs not supported on GPUs.");
#endif          
        }
      }
    }
    // update send deps
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("process_mlrf_comm_requests predyn send - m_post_req_flag = FALSE, m_update_req_flag = TRUE");

    m_pre_dyn_send_req.m_post_req_flag.store(false, std::memory_order_release);
    g_strand_mgr.mlrf_send_update_dcntr(SID_MLRF_COMM_TYPE,timestep);
  }

  if (m_post_dyn_send_req.m_post_req_flag.load(std::memory_order_acquire)) {
    SCALE coarsest_active_scale = compute_coarsest_active_scale(m_post_dyn_send_req.m_timestep, FINEST_SCALE);
    TIMESTEP timestep = m_post_dyn_send_req.m_timestep;
    for(asINT32 scale = FINEST_SCALE; scale >= coarsest_active_scale; --scale) {
      auINT32 even_odd_mask = (sim_is_timestep_odd(scale, timestep) ?
                               STP_PROCESS_ON_ODD_TIMES
                               : STP_PROCESS_ON_EVEN_TIMES);
      DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, scale) {
        LOG_MSG("RECV_DEPEND").printf("Posting post_dyn_send_req timestep %d scale %d even_odd_mask %d", timestep, scale, even_odd_mask);
        if (group->m_even_odd & even_odd_mask) {
          group->post_post_dynamics_sends();
        }
      }
    }

    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "process_mlrf_comm_requests postdyn send - m_post_req_flag = FALSE");

    m_post_dyn_send_req.m_post_req_flag.store(false, std::memory_order_release);
  }
}
