/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created Thurs May 22, 2008
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_T_SOLVER_DATA_H
#define _SIMENG_T_SOLVER_DATA_H

#include "common_sp.h"
#include "vectorization_support.h"
#include "shob.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "sim.h"
#include "meas_cell.h"
#include "debug_print_spec.h"
#include "gpu_host_init.h"
#include "mme_ckpt.h"

inline namespace SIMULATOR_NAMESPACE {
  
// forward declarations specific to this solver data-block
template <typename UBLK_TYPE_TAG> class tUBLK_T_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_T_DATA;

template <typename SFL_TYPE_TAG> struct tSURFEL_T_DATA;	 
template <typename SFL_TYPE_TAG> struct tSURFEL_S2S_T_DATA;
template<typename SFL_TYPE_TAG> struct tSURFEL_V2S_T_DATA;	 

typedef tUBLK_T_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_T_DATA,         *UBLK_T_DATA;
typedef tUBLK_T_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_T_DATA, *UBLK_UBFLOAT_T_DATA;
typedef tNEARBLK_T_DATA<UBLK_SDFLOAT_TYPE_TAG> sNEAR_UBLK_T_DATA,            *NEAR_UBLK_T_DATA;
typedef tNEARBLK_T_DATA<UBLK_UBFLOAT_TYPE_TAG> sNEAR_UBLK_UBFLOAT_T_DATA,    *NEAR_UBLK_UBFLOAT_T_DATA;

#ifdef BUILD_GPU
typedef tUBLK_T_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_T_DATA,         *MBLK_T_DATA;
typedef tUBLK_T_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_T_DATA, *MBLK_UBFLOAT_T_DATA;
typedef tNEARBLK_T_DATA<MBLK_SDFLOAT_TYPE_TAG> sNEAR_MBLK_T_DATA,            *NEAR_MBLK_T_DATA;
typedef tNEARBLK_T_DATA<MBLK_UBFLOAT_TYPE_TAG> sNEAR_MBLK_UBFLOAT_T_DATA,    *NEAR_MBLK_UBFLOAT_T_DATA;

typedef tSURFEL_T_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_T_DATA, *MSFL_T_DATA;	 
typedef tSURFEL_S2S_T_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_S2S_T_DATA, *MSFL_S2S_T_DATA;
typedef tSURFEL_V2S_T_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_V2S_T_DATA, *MSFL_V2S_T_DATA;	 
#endif


template<size_t N_VOXELS>
 struct tUBLK_T_SCALAR_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
  SURFEL_STATE      m_states_t[N_STATES][N_VOXELS];
}; 

#if BUILD_6X_SOLVER
template<size_t N_VOXELS>
struct tUBLK_LB_ENTROPY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
  STP_PHYS_VARIABLE m_entropy[N_VOXELS];
  STP_PHYS_VARIABLE m_diffusivity[N_VOXELS];
  SURFEL_STATE      m_states_t[N_STATES][N_VOXELS];
}; 
#endif

#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
template<size_t N_VOXELS>	 
struct tUBLK_LB_ENERGY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
  STP_PHYS_VARIABLE m_entropy[N_VOXELS];
  STP_PHYS_VARIABLE m_diffusivity[N_VOXELS];
  SURFEL_STATE      m_states_t[N_STATES][N_VOXELS];
}; 
#endif

template<size_t N_VOXELS>	 
struct tUBLK_T_PDE_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
  STP_PHYS_VARIABLE m_diffusivity[N_VOXELS];
  STP_PHYS_VARIABLE m_grad_t_cross[3][N_VOXELS];
};

template<size_t N_VOXELS>
 struct tUBLK_ENTROPY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
  STP_PHYS_VARIABLE m_diffusivity[N_VOXELS];
  STP_PHYS_VARIABLE m_entropy[N_VOXELS];
  STP_PHYS_VARIABLE m_grad_S[3][N_VOXELS];
#if BUILD_D39_LATTICE
  STP_PHYS_VARIABLE m_div_vel[N_VOXELS];
  STP_PHYS_VARIABLE m_eta_art[N_VOXELS];
  STP_PHYS_VARIABLE m_hyb_force[3][N_VOXELS];
#endif
};

#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
template<size_t N_VOXELS>
 struct tUBLK_MME_T_SEND_FIELD
{
  STP_PHYS_VARIABLE m_fluid_temp[N_VOXELS];
};
#endif

//No nearblk_t_data need to be comm'ed

//------------------------------------------------------------------------------
// sUBLK_T_DATA
//------------------------------------------------------------------------------
// WE ARE CURRENTLY WASTING SPACE USED BY FLUID_TEMP_PRE FOR UBLK WITH COPIES
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_LB_SOLVER_T_DATA
{
  EXTRACT_UBLK_TRAITS
	//const static asINT32 N_STATES_COPIES = 2;
#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT m_states_t[N_STATES];

#undef ALIGNED_UBFLOAT
};

typedef tUBLK_LB_SOLVER_T_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_LB_SOLVER_T_DATA,         *UBLK_LB_SOLVER_T_DATA;
typedef tUBLK_LB_SOLVER_T_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_LB_SOLVER_T_DATA, *UBLK_UBFLOAT_LB_SOLVER_T_DATA;

#ifdef BUILD_GPU
typedef tUBLK_LB_SOLVER_T_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_LB_SOLVER_T_DATA,         *MBLK_LB_SOLVER_T_DATA;
typedef tUBLK_LB_SOLVER_T_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_LB_SOLVER_T_DATA, *MBLK_UBFLOAT_LB_SOLVER_T_DATA;

INIT_MBLK(UBLK_LB_SOLVER_T_DATA) 
{
  VEC_COPY_UBLK_TO_MBLK(m_states_t,N_STATES);
}

#endif


template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_PDE_SOLVER_T_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T
#if !BUILD_6X_SOLVER && !BUILD_D39_LATTICE
  ALIGNED_UBFLOAT diffusivity;
#endif
  union {
    struct {
      // For T pde solver and entropy pde solver
      ALIGNED_UBFLOAT grad_t_cross[3]; //used in anti-diffusion for cross-derivative term from time

      //for flux-limiter, originally located in NEARBLK, move here to match data size of T_lb_scalar data
      ALIGNED_UBFLOAT T_pde_1[2][3];
      ALIGNED_UBFLOAT T_pde_2[2][3];
    };
    struct {
      // For Entropy solver
      ALIGNED_UBFLOAT grad_S[3]; //used in anti-diffusion for cross-derivative term from time
      //for flux-limiter, originally located in NEARBLK, move here to match data size of T_lb_scalar data
      ALIGNED_UBFLOAT S_pde_1[2][3];
      ALIGNED_UBFLOAT S_pde_2[2][3];
    };
  };


#undef ALIGNED_UBFLOAT
};
typedef tUBLK_PDE_SOLVER_T_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_PDE_SOLVER_T_DATA,         *UBLK_PDE_SOLVER_T_DATA;
typedef tUBLK_PDE_SOLVER_T_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_PDE_SOLVER_T_DATA, *UBLK_UBFLOAT_PDE_SOLVER_T_DATA;
#ifdef BUILD_GPU
typedef tUBLK_PDE_SOLVER_T_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_PDE_SOLVER_T_DATA,         *MBLK_PDE_SOLVER_T_DATA;
typedef tUBLK_PDE_SOLVER_T_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_PDE_SOLVER_T_DATA, *MBLK_UBFLOAT_PDE_SOLVER_T_DATA;

INIT_MBLK(UBLK_PDE_SOLVER_T_DATA) {
#if !BUILD_6X_SOLVER
  COPY_UBLK_TO_MBLK(diffusivity);
#endif

  if (sim.T_solver_type == PDE_TEMPERATURE) {
    VEC_COPY_UBLK_TO_MBLK(grad_t_cross,3);
    VEC2_COPY_UBLK_TO_MBLK(T_pde_1,2,3);
    VEC2_COPY_UBLK_TO_MBLK(T_pde_2,2,3);
  }
  else {
    VEC_COPY_UBLK_TO_MBLK(grad_S,3);
    VEC2_COPY_UBLK_TO_MBLK(S_pde_1,2,3);
    VEC2_COPY_UBLK_TO_MBLK(S_pde_2,2,3);
  }

}
#endif

template<typename UBLK_TYPE_TAG>
union tLB_PDE_DATA {
  EXTRACT_UBLK_TRAITS
  tUBLK_PDE_SOLVER_T_DATA<UBLK_TYPE_TAG> pde_t_data;
  tUBLK_LB_SOLVER_T_DATA<UBLK_TYPE_TAG> lb_t_data;
};

typedef tLB_PDE_DATA<UBLK_SDFLOAT_TYPE_TAG> uLB_PDE_DATA,         *LB_PDE_DATA;
typedef tLB_PDE_DATA<UBLK_UBFLOAT_TYPE_TAG> uUBFLOAT_LB_PDE_DATA, *UBFLOAT_LB_PDE_DATA;

template <typename UBLK_TYPE_TAG>
struct tPDE_DATA_WITH_TWO_COPIES {
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T
#if !BUILD_6X_SOLVER && !BUILD_D39_LATTICE
  ALIGNED_UBFLOAT diffusivity;
#endif
  union {
    ALIGNED_UBFLOAT grad_t_cross[3]; //used in anti-diffusion for cross-derivative term from time
    ALIGNED_UBFLOAT grad_S[3]; //used in anti-diffusion for cross-derivative term from time
  };
#undef ALIGNED_UBFLOAT
};

#if BUILD_D19_LATTICE

//melting solver
__HOST__DEVICE__
inline VOID get_temp_dep_ab_vec(vxFLOAT temp, vxFLOAT *nu, vxFLOAT *pr)
{
  auto& simc = get_simc_ref();
  vxFLOAT yy = temp * simc.one_over_1k_lat - 273.15F;
  cset(yy, -10.95F, (yy < -10.95F));
  cset(yy, 30.0F, (yy > 30.0F));
  vxFLOAT y0 = yy + 8.0F;
  vxFLOAT y1 = yy - 25.0F;
  vxFLOAT y2 = yy - 60.0F;
  vxFLOAT yc = y1 * y2 - 0.8658F * y0 * y2 + 0.21F * y0 * y1;
  if (nu) 
   *nu = 0.001F * yc;
  if (pr) 
    *pr = simc.fluid_Prandtl_number * 0.001F * yc;
  return;
} 

//melting solver
__HOST__DEVICE__
inline vxFLOAT calculate_real_temp(vxFLOAT temp)
{
  auto& sim = get_sim_ref();
  auto& simc = *sim.c();  
  vxFLOAT T_real = temp;
  vxFLOAT T_bound = simc.T_melt + simc.T_melt_up;
  cset(T_real, simc.T_melt, (temp>=simc.T_melt && temp <= T_bound));
  cset(T_real, temp - simc.T_melt_up, (temp > T_bound));
  return T_real;
}

__HOST__DEVICE__
inline sdFLOAT calculate_real_temp(sdFLOAT temp)
{
  auto& sim = get_sim_ref();
  auto& simc = *sim.c();    
  if (temp > simc.T_melt) {
    if (temp <= simc.T_melt + simc.T_melt_up) 
      return simc.T_melt;
    else //temp > sim.T_melt + sim.T_melt_up
      return (temp - simc.T_melt_up);
  } else
    return temp;
}
//melting solver


//for temp dependent liquid
__HOST__DEVICE__
inline VOID get_temp_dep_nu_pr(sdFLOAT temp, sdFLOAT *nu, sdFLOAT *pr)
{
#if !GPU_COMPILER
  sdFLOAT  yy = temp * sim.one_over_1k_lat - 273.15F;

  if (yy < g_lpms->liq_T_f) yy = g_lpms->liq_T_f;
  if (yy > g_lpms->liq_T_b) yy = g_lpms->liq_T_b;

  sdFLOAT yy0 = - yy;
  sdFLOAT yy4 = yy * yy * yy * yy;
  if (yy > 0.0) {
    yy0 = 0.0;
    yy4 = 0.0;
  }

  sdFLOAT xz1 = yy - g_lpms->x1;
  sdFLOAT xz2 = yy - g_lpms->x2;
  sdFLOAT xz3 = yy - g_lpms->x3;
  sdFLOAT xz4 = yy - g_lpms->x4;
  sdFLOAT xz5 = yy - g_lpms->x5;
  
  sdFLOAT y1 = xz2 * xz3 * xz4 * xz5 / g_lpms->cx1;
  sdFLOAT y2 = xz1 * xz3 * xz4 * xz5 / g_lpms->cx2; 
  sdFLOAT y3 = xz1 * xz2 * xz4 * xz5 / g_lpms->cx3;
  sdFLOAT y4 = xz1 * xz2 * xz3 * xz5 / g_lpms->cx4;
  sdFLOAT y5 = xz1 * xz2 * xz3 * xz4 / g_lpms->cx5;

  if (nu) 
    *nu = y1 * g_lpms->liq_v1 + y2 * g_lpms->liq_v2 + y3 * g_lpms->liq_v3 
      + y4 * g_lpms->liq_v4 + y5 * g_lpms->liq_v5 
      + g_lpms->liq_v6 * yy4 + g_lpms->liq_v7 * yy0; 
  
  if (pr)
    *pr = y1 * g_lpms->liq_pr1 + y2 * g_lpms->liq_pr2 + y3 * g_lpms->liq_pr3 
      + y4 * g_lpms->liq_pr4 + y5 * g_lpms->liq_pr5
      + g_lpms->liq_pr6 * yy4 + g_lpms->liq_pr7 * yy0;
#else
  //We need to initialize g_lpms before this code an be made active
  assert(false);
#endif
}

//for melting solver
__HOST__DEVICE__ inline VOID get_temp_dep_ab(sdFLOAT temp, sdFLOAT *nu, sdFLOAT *pr)
{
  auto& sim = get_sim_ref();
  auto& simc = *sim.c();
  sdFLOAT yy = temp * simc.one_over_1k_lat - 273.15F;
  if (yy < -10.95F) yy = -10.95F;
  if (yy > 30.0F) yy = 30.0F;
  sdFLOAT y0 = yy + 8.0F;
  sdFLOAT y1 = yy - 25.0F;
  sdFLOAT y2 = yy - 60.0F;
  sdFLOAT yc = y1 * y2 - 0.8658F * y0 * y2 + 0.21F * y0 * y1;
  if (nu) 
    *nu = 0.001F * yc;
  if (pr)
    *pr = simc.fluid_Prandtl_number * 0.001F *yc;
} 

#endif

template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_T_DATA
{
#if EXA_USE_SSE
  static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
  static const size_t ALIGNMENT = 32;
#endif

  EXTRACT_UBLK_TRAITS

  using uLB_PDE_DATA = tLB_PDE_DATA<UBLK_TYPE_TAG>;
  using sPDE_DATA_WITH_TWO_COPIES = tPDE_DATA_WITH_TWO_COPIES<UBLK_TYPE_TAG>;
  using sUBLK_LB_SOLVER_T_DATA = tUBLK_LB_SOLVER_T_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PDE_SOLVER_T_DATA = tUBLK_PDE_SOLVER_T_DATA<UBLK_TYPE_TAG>;

  using sUBLK_T_SCALAR_SEND_FIELD = tUBLK_T_SCALAR_SEND_FIELD<N_VOXELS>;
  using sUBLK_T_PDE_SEND_FIELD = tUBLK_T_PDE_SEND_FIELD<N_VOXELS>;
  using sUBLK_ENTROPY_SEND_FIELD = tUBLK_ENTROPY_SEND_FIELD<N_VOXELS>;
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE  
  using sUBLK_LB_ENERGY_SEND_FIELD = tUBLK_LB_ENERGY_SEND_FIELD<N_VOXELS>;
#endif
  
#if BUILD_6X_SOLVER
  using sUBLK_LB_ENTROPY_SEND_FIELD = tUBLK_LB_ENTROPY_SEND_FIELD<N_VOXELS>;
#endif

#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
  using sUBLK_MME_T_SEND_FIELD = tUBLK_MME_T_SEND_FIELD<N_VOXELS>;
#endif

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT fluid_temp[N_TIME_INDICES];
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  ALIGNED_UBFLOAT diffusivity[N_TIME_INDICES];
#endif

  ALIGNED_UBFLOAT grad_t[3];
  ALIGNED_UBFLOAT temp_coupling;
  ALIGNED_UBFLOAT fluid_temp_pre;
  // Additional memory needed for entropy solver
  ALIGNED_UBFLOAT entropy[N_TIME_INDICES];
#if BUILD_D19_LATTICE
  ALIGNED_UBFLOAT force[3];  // for new frac adv scheme currently enabled only for 19s entropy solver
#endif
#if BUILD_D39_LATTICE
  ALIGNED_UBFLOAT div_vel[N_TIME_INDICES];  // divergence of velocity
  ALIGNED_UBFLOAT eta_art[N_TIME_INDICES];  // artificial viscosity proportional to pressure gradient
  ALIGNED_UBFLOAT hyb_force[N_TIME_INDICES][3];// hybrid force for rescaling speed of sound
#endif
  static size_t SIZE(BOOLEAN has_two_copies) {
    if (has_two_copies)
      return (sizeof(tUBLK_T_DATA) + sizeof(uLB_PDE_DATA)*2);
    else
      return (sizeof(tUBLK_T_DATA) + sizeof(uLB_PDE_DATA) + sizeof(sPDE_DATA_WITH_TWO_COPIES));
  }

  __HOST__DEVICE__ sUBLK_LB_SOLVER_T_DATA *lb_t_data(asINT32 timestep_index) {
      return  ((sUBLK_LB_SOLVER_T_DATA *)
              ((char *)this + timestep_index*sizeof(uLB_PDE_DATA) + sizeof(tUBLK_T_DATA)));
  }

  __HOST__DEVICE__ sUBLK_PDE_SOLVER_T_DATA *pde_t_data(asINT32 timestep_index) {
      return (sUBLK_PDE_SOLVER_T_DATA *)((char *)this + timestep_index*sizeof(uLB_PDE_DATA) + sizeof(tUBLK_T_DATA));
  }

  // WARNING: I don't think these methods work unless the template parameter T is sdFLOAT[ubFLOAT::N_VOXELS], or in
  // the case of p_diffusivity, ubFLOAT. You can't index a ubFLOAT with a voxel index, or a sdFLOAT[ubFLOAT::N_VOXELS]
  // with a voxor index, and get a correct result. - Sam

  sdFLOAT T_pde_1(sINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(0);
    return pde_t_data->T_pde_1[index][axis][voxel];
  }

  sdFLOAT& set_T_pde_1(asINT32 timestep_index, sINT32 face, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(timestep_index);
    return (pde_t_data->T_pde_1[face][axis][voxel]);
  }
  sdFLOAT& set_S_pde_1(asINT32 timestep_index, asINT32 face, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(timestep_index);
    return (pde_t_data->S_pde_1[face][axis][voxel]);
  }
  sdFLOAT T_pde_2(sINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(0);
    return pde_t_data->T_pde_2[index][axis][voxel];
  }
  sdFLOAT& set_T_pde_2(asINT32 timestep_index, sINT32 face, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(timestep_index);
    return (pde_t_data->T_pde_2[face][axis][voxel]);
  }
  sdFLOAT& set_S_pde_2(asINT32 timestep_index, sINT32 face, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(timestep_index);
    return (pde_t_data->S_pde_2[face][axis][voxel]);
  }
//  sdFLOAT *p_diffusivity(asINT32 index, asINT32 voxor) {
//    tUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data();
//    return (sdFLOAT * )(pde_t_data->diffusivity[index] + voxor);
//  }
  vxFLOAT_BASE& ub_diffusivity(asINT32 index, asINT32 voxor) {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    return (diffusivity[index][voxor]);
#else
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return (pde_t_data->diffusivity[voxor]);
#endif
  }

  size_t diffusivity_send_size() {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    return sizeof(diffusivity[0]);
#else
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(0);
    return sizeof(pde_t_data->diffusivity);
#endif
  }
  __HOST__DEVICE__ sdFLOAT get_diffusivity(asINT32 index, asINT32 voxel) {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    return diffusivity[index][voxel];
#else
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->diffusivity[voxel];
#endif
  }

  sdFLOAT& set_diffusivity(asINT32 index, asINT32 voxel) {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    return diffusivity[index][voxel];
#else
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->diffusivity[voxel];
#endif
  }

  __DEVICE__
  vxFLOAT_BASE& set_diffusivity_voxor(asINT32 index, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,tubFLOAT<N_VOXELS>>::value, "Function must be called on vector type" );
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    return diffusivity[index][voxor];
#else
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->diffusivity[voxor];
#endif
  }

  __HOST__DEVICE__ sdFLOAT grad_t_cross(asINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->grad_t_cross[axis][voxel];
  }

  size_t grad_t_cross_send_size() {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(0);
    return sizeof(pde_t_data->grad_t_cross[0]);
  }

  sdFLOAT& set_grad_t_cross(asINT32 index, asINT32 axis, asINT32 voxel) {
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->grad_t_cross[axis][voxel];
  }

  __DEVICE__
  vxFLOAT_BASE& set_grad_t_cross_voxor(asINT32 index, asINT32 axis, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,ubFLOAT>::value, "Function must be called on vector type" );
    sUBLK_PDE_SOLVER_T_DATA *pde_t_data = this->pde_t_data(index);
    return pde_t_data->grad_t_cross[axis][voxor];
  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_T_DATA *dest_ublk_t_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    BOOLEAN is_T_pde_on = (sim.T_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.T_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif
    tUBLK_T_DATA* d = dest_ublk_t_data;
    ccDOTIMES(idx,N_TIME_INDICES) {
      d->fluid_temp [idx][dest_voxel] = fluid_temp [idx][source_voxel];
    }
    d->temp_coupling[dest_voxel]  = temp_coupling[source_voxel];

    if (is_T_pde_on) {
      ccDOTIMES(timestep, N_TIME_INDICES) {
        d->set_diffusivity(timestep,     dest_voxel) = get_diffusivity(timestep,     source_voxel);
        d->set_grad_t_cross(timestep, 0, dest_voxel) = grad_t_cross(timestep, 0, source_voxel);
        d->set_grad_t_cross(timestep, 1, dest_voxel) = grad_t_cross(timestep, 1, source_voxel);
        d->set_grad_t_cross(timestep, 2, dest_voxel) = grad_t_cross(timestep, 2, source_voxel);
      }
    } else if (is_entropy_solver_on) {
      ccDOTIMES(timestep, N_TIME_INDICES) {
        d->set_diffusivity(timestep, dest_voxel) = get_diffusivity(timestep, source_voxel);
        d->entropy[timestep][dest_voxel] = entropy[timestep][source_voxel];
        d->pde_t_data(timestep)->grad_S[0][dest_voxel] = pde_t_data(timestep)->grad_S[0][source_voxel];
        d->pde_t_data(timestep)->grad_S[1][dest_voxel] = pde_t_data(timestep)->grad_S[1][source_voxel];
        d->pde_t_data(timestep)->grad_S[2][dest_voxel] = pde_t_data(timestep)->grad_S[2][source_voxel];
      }
    }
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_entropy_solver_on || is_lb_energy_solver_on){
      ccDOTIMES(timestep, N_TIME_INDICES) {
        d->set_diffusivity(timestep, dest_voxel) = get_diffusivity(timestep, source_voxel);
        d->entropy[timestep][dest_voxel] = entropy[timestep][source_voxel];
      }
    }
#endif
#if BUILD_D39_LATTICE
    if (is_entropy_solver_on) {
      ccDOTIMES(timestep, N_TIME_INDICES) {
	d->div_vel[timestep][dest_voxel]      = div_vel[timestep][source_voxel];
	d->eta_art[timestep][dest_voxel]      = eta_art[timestep][source_voxel];
	ccDOTIMES(axis, 3) {
	  d->hyb_force[timestep][axis][dest_voxel] = hyb_force[timestep][axis][source_voxel];
	}
      }
    }
#endif
  }

  __DEVICE__
  VOID explode_voxel(tUBLK_T_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_t_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    asINT32 prior_timestep_index = (prior_solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    auto& sim = get_sim_ref();
    BOOLEAN is_T_pde_on = (sim.c()->T_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.c()->T_solver_type == PDE_ENTROPY);  //pde entropy
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_entropy_solver_on = (sim.c()->T_solver_type == LB_ENTROPY);
    BOOLEAN is_lb_energy_solver_on = (sim.c()->T_solver_type == LB_ENERGY);
#endif

    auto* f = fine_ublk_t_data;

    vxFLOAT fluid_temp = this->fluid_temp[prior_timestep_index][voxel_to_explode];

    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
        f->fluid_temp[timestep][voxor]   = fluid_temp;
      }
    }

    vxFLOAT temp_coupling = this->temp_coupling[voxel_to_explode];

    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      f->temp_coupling[voxor]   = temp_coupling;
    }

    vxFLOAT diffusivity = get_diffusivity(prior_timestep_index, voxel_to_explode);

    if (is_T_pde_on) {
      vxFLOAT grad_t_cross0 = grad_t_cross(prior_timestep_index, 0, voxel_to_explode);
      vxFLOAT grad_t_cross1 = grad_t_cross(prior_timestep_index, 1, voxel_to_explode);
      vxFLOAT grad_t_cross2 = grad_t_cross(prior_timestep_index, 2, voxel_to_explode);
      ccDOTIMES(timestep, N_TIME_INDICES) {
        ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
          f->set_diffusivity_voxor(timestep,     voxor) = diffusivity;
          f->set_grad_t_cross_voxor(timestep, 0, voxor) = grad_t_cross0;
          f->set_grad_t_cross_voxor(timestep, 1, voxor) = grad_t_cross1;
          f->set_grad_t_cross_voxor(timestep, 2, voxor) = grad_t_cross2;
        }
      }
    } else if (is_entropy_solver_on) {
      vxFLOAT entropy     = this->entropy[prior_timestep_index][voxel_to_explode];
      vxFLOAT pde_t_data0 = pde_t_data(prior_timestep_index)->grad_S[0][voxel_to_explode];
      vxFLOAT pde_t_data1 = pde_t_data(prior_timestep_index)->grad_S[1][voxel_to_explode];
      vxFLOAT pde_t_data2 = pde_t_data(prior_timestep_index)->grad_S[2][voxel_to_explode];

      ccDOTIMES(timestep, N_TIME_INDICES) {
        ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
          f->set_diffusivity_voxor(timestep, voxor) = diffusivity;
          f->entropy[timestep][voxor] = entropy;
          f->pde_t_data(timestep)->grad_S[0][voxor] = pde_t_data0;
          f->pde_t_data(timestep)->grad_S[1][voxor] = pde_t_data1;
          f->pde_t_data(timestep)->grad_S[2][voxor] = pde_t_data2;
        }
      }
    }
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_entropy_solver_on || is_lb_energy_solver_on) {
      vxFLOAT entropy = this->entropy[prior_timestep_index][voxel_to_explode];

      ccDOTIMES(timestep, N_TIME_INDICES) {
        ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
          f->set_diffusivity_voxor(timestep, voxor) = diffusivity;
          f->entropy[timestep][voxor] = entropy;
        }
      }
    }
#endif
#if BUILD_D39_LATTICE
    if (is_entropy_solver_on) {
      vxFLOAT div_vel = this->div_vel[prior_timestep_index][voxel_to_explode];
      vxFLOAT eta_art = this->eta_art[prior_timestep_index][voxel_to_explode];
      vxFLOAT hyb_force0 = this->hyb_force[prior_timestep_index][0][voxel_to_explode];
      vxFLOAT hyb_force1 = this->hyb_force[prior_timestep_index][1][voxel_to_explode];
      vxFLOAT hyb_force2 = this->hyb_force[prior_timestep_index][2][voxel_to_explode];
      ccDOTIMES(timestep, N_TIME_INDICES) {
        ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
          f->div_vel[timestep][voxor]      = div_vel;
          f->eta_art[timestep][voxor]      = eta_art;
          f->hyb_force[timestep][0][voxor] = hyb_force0;
          f->hyb_force[timestep][1][voxor] = hyb_force1;
          f->hyb_force[timestep][2][voxor] = hyb_force2;
        }
      }
    }
#endif
  }

#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
  VOID copy_voxel_mme_data(tUBLK_T_DATA *dest_ublk_t_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_T_DATA* d = dest_ublk_t_data;
    
    ccDOTIMES(index, N_TIME_INDICES)
      d->fluid_temp [index][dest_voxel] = fluid_temp [index][source_voxel];
  }

  VOID explode_voxel_mme(tUBLK_T_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_t_data, asINT32 voxel_to_explode, SOLVER_INDEX_MASK solver_ts_index_mask)
  {
    asINT32 coarse_timestep_index = (solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    auto f = fine_ublk_t_data;
    asINT32 fine_timestep_index = 0; // explode_voxel_mme happens on odd fine timesteps which is always 0
    vxFLOAT coarse_fluid_temp = fluid_temp[coarse_timestep_index][voxel_to_explode];
    ccDO_UBLK(voxor) {
      f->fluid_temp[fine_timestep_index][voxor]   = coarse_fluid_temp;
    }
  }
#elif BUILD_5G_LATTICE
  VOID copy_voxel_mme_data(tUBLK_T_DATA *dest_ublk_t_data, asINT32 dest_voxel, asINT32 source_voxel) {}

  VOID explode_voxel_mme(tUBLK_T_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_t_data, asINT32 voxel_to_explode, asINT32 coarse_timestep_index) {}
#endif
  
  VOID write_ckpt(BOOLEAN has_two_copies)                      { write_ckpt_lgi(this, SIZE(has_two_copies)); }
  VOID read_ckpt(BOOLEAN has_two_copies)                       { read_lgi(this, SIZE(has_two_copies));       }
  uINT64 ckpt_len(BOOLEAN has_two_copies)                      { return SIZE(has_two_copies);                }
  
  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas)
  {
    auto& simc = get_simc_ref();
    auto& timescale = get_timescale_ref();
    asINT32 prior_index = timescale.m_t_pde_tm.prior_timestep_index(scale);
#if BUILD_D19_LATTICE
    if (simc.use_melting_solver) {
      DO_VOXELS_IN_MASK(voxel, voxel_mask) {
        meas->var[voxel] = calculate_real_temp(fluid_temp[prior_index][voxel]);
      }
      meas++;
    } else 
#endif
    {
      DO_VOXELS_IN_MASK(voxel, voxel_mask) {
        meas->var[voxel] = fluid_temp[prior_index][voxel];
      }
      meas++;
    }
  }
  

  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
    if (is_lb_energy_solver_on)
      send_size += ( sizeof(sUBLK_LB_ENERGY_SEND_FIELD) / sizeof(sdFLOAT));
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on)
      send_size += ( sizeof(sUBLK_LB_ENTROPY_SEND_FIELD) / sizeof(sdFLOAT));
#endif
    else
      send_size += ( sizeof(sUBLK_T_SCALAR_SEND_FIELD) / sizeof(sdFLOAT));
#else
    send_size += ( sizeof(sUBLK_T_SCALAR_SEND_FIELD) / sizeof(sdFLOAT));
#endif
    BOOLEAN is_T_pde_on = (sim.T_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_S_pde_on = (sim.T_solver_type == PDE_ENTROPY);
    if(is_T_pde_on) {
      tpde_send_size += ( sizeof(sUBLK_T_PDE_SEND_FIELD) / sizeof(sdFLOAT));
    } else if (is_S_pde_on) {
      tpde_send_size += ( sizeof(sUBLK_ENTROPY_SEND_FIELD) / sizeof(sdFLOAT));
    }
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask, BOOLEAN is_vr_fine = FALSE) {
    asINT32 timestep_index = (solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    BOOLEAN is_T_pde_on = (send_data_info.t_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.T_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif
    if(is_T_pde_on) {//only in 19s
      sUBLK_T_PDE_SEND_FIELD* field = reinterpret_cast<sUBLK_T_PDE_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_PDE_SOLVER_T_DATA* pde_t_data = this->pde_t_data(timestep_index);
      memcpy(field->m_fluid_temp,   &(fluid_temp[timestep_index]),   sizeof(field->m_fluid_temp));
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      memcpy(field->m_diffusivity,  &(diffusivity[timestep_index]),  sizeof(field->m_diffusivity));
#else
      memcpy(field->m_diffusivity,  &(pde_t_data->diffusivity),  sizeof(field->m_diffusivity));
#endif
      memcpy(field->m_grad_t_cross, &(pde_t_data->grad_t_cross), sizeof(field->m_grad_t_cross));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else if (is_entropy_solver_on) { //19s and 33s
      sUBLK_ENTROPY_SEND_FIELD* field = reinterpret_cast<sUBLK_ENTROPY_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_PDE_SOLVER_T_DATA* pde_t_data = this->pde_t_data(timestep_index);
      memcpy(field->m_fluid_temp,  &(fluid_temp[timestep_index]),     sizeof(field->m_fluid_temp));
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      memcpy(field->m_diffusivity, &(diffusivity[timestep_index]),    sizeof(field->m_diffusivity));
#else
      memcpy(field->m_diffusivity, &(pde_t_data->diffusivity),        sizeof(field->m_diffusivity));
#endif
      memcpy(field->m_entropy,     &(entropy[timestep_index]),        sizeof(field->m_entropy));
      memcpy(field->m_grad_S,      &(pde_t_data->grad_S),             sizeof(field->m_grad_S));
#if BUILD_D39_LATTICE
      memcpy(field->m_div_vel,   div_vel,   sizeof(field->m_div_vel));
      memcpy(field->m_eta_art,   eta_art,   sizeof(field->m_eta_art));
      memcpy(field->m_hyb_force, hyb_force, sizeof(field->m_hyb_force));
#endif
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    } 
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      sUBLK_LB_ENTROPY_SEND_FIELD* field = reinterpret_cast<sUBLK_LB_ENTROPY_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
      memcpy(&field->m_fluid_temp,  &(fluid_temp[timestep_index]),  sizeof(field->m_fluid_temp));
      memcpy(&field->m_entropy,     &(entropy[timestep_index]),     sizeof(field->m_entropy));
      memcpy(&field->m_diffusivity, &(diffusivity[timestep_index]),     sizeof(field->m_diffusivity));
      memcpy(field->m_states_t,     lb_t_data->m_states_t,          sizeof(field->m_states_t));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
#if  BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on){
      sUBLK_LB_ENERGY_SEND_FIELD* field = reinterpret_cast<sUBLK_LB_ENERGY_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
      memcpy(&field->m_fluid_temp,  &(fluid_temp[timestep_index]),  sizeof(field->m_fluid_temp));
      memcpy(&field->m_entropy,     &(entropy[timestep_index]),     sizeof(field->m_entropy));
      memcpy(&field->m_diffusivity, &(diffusivity[timestep_index]), sizeof(field->m_diffusivity));
      memcpy(field->m_states_t,     lb_t_data->m_states_t,          sizeof(field->m_states_t));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
    else {
      sUBLK_T_SCALAR_SEND_FIELD* field = reinterpret_cast<sUBLK_T_SCALAR_SEND_FIELD*>(send_data_info.send_buffer);
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
      memcpy(field->m_fluid_temp, &(fluid_temp[timestep_index]), sizeof(field->m_fluid_temp));
      memcpy(field->m_states_t,   lb_t_data->m_states_t,         sizeof(field->m_states_t));
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask, BOOLEAN is_vr_fine = FALSE) {
    asINT32 timestep_index = (solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    BOOLEAN is_T_pde_on = (recv_data_info.t_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.T_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif
    if (is_T_pde_on) { //only in 19s
      sUBLK_T_PDE_SEND_FIELD* field = reinterpret_cast<sUBLK_T_PDE_SEND_FIELD*>(recv_data_info.recv_buffer);
      sUBLK_PDE_SOLVER_T_DATA* pde_t_data = this->pde_t_data(timestep_index);
      memcpy(&(fluid_temp[timestep_index]),   field->m_fluid_temp,    sizeof(field->m_fluid_temp));
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      memcpy(&(diffusivity[timestep_index]),  field->m_diffusivity,   sizeof(field->m_diffusivity));
#else
      memcpy(&(pde_t_data->diffusivity),      field->m_diffusivity,   sizeof(field->m_diffusivity));
#endif
      memcpy(&(pde_t_data->grad_t_cross),     field->m_grad_t_cross,  sizeof(field->m_grad_t_cross));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else if (is_entropy_solver_on) { //19s and 33s
      sUBLK_ENTROPY_SEND_FIELD* field = reinterpret_cast<sUBLK_ENTROPY_SEND_FIELD*>(recv_data_info.recv_buffer);
      sUBLK_PDE_SOLVER_T_DATA* pde_t_data = this->pde_t_data(timestep_index);
      memcpy(&(fluid_temp[timestep_index]),   field->m_fluid_temp,    sizeof(field->m_fluid_temp));
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
      memcpy(&(diffusivity[timestep_index]),      field->m_diffusivity,   sizeof(field->m_diffusivity));
#else
      memcpy(&(pde_t_data->diffusivity),      field->m_diffusivity,   sizeof(field->m_diffusivity));
#endif
      memcpy(&(entropy[timestep_index]),      field->m_entropy,       sizeof(field->m_entropy));
      memcpy(&(pde_t_data->grad_S),           field->m_grad_S,        sizeof(field->m_grad_S));
#if BUILD_D39_LATTICE
      memcpy(div_vel,   &field->m_div_vel,   sizeof(field->m_div_vel));
      memcpy(eta_art,   &field->m_eta_art,   sizeof(field->m_eta_art));
      memcpy(hyb_force, &field->m_hyb_force, sizeof(field->m_hyb_force));
#endif
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    } 
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      sUBLK_LB_ENTROPY_SEND_FIELD* field = reinterpret_cast<sUBLK_LB_ENTROPY_SEND_FIELD*>(recv_data_info.recv_buffer);
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
#else
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(ONLY_ONE_COPY);
      if (is_vr_fine)   // ghost vr fine ublks have two sets of states
        lb_t_data = this->lb_t_data(timestep_index);
#endif
      memcpy(&(fluid_temp[timestep_index]),     field->m_fluid_temp,    sizeof(field->m_fluid_temp));
      memcpy(&(entropy[timestep_index]),        field->m_entropy,       sizeof(field->m_entropy));
      memcpy(&(diffusivity[timestep_index]),    field->m_diffusivity,   sizeof(field->m_diffusivity));
      memcpy(lb_t_data->m_states_t,             field->m_states_t,      sizeof(field->m_states_t));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on) {
      sUBLK_LB_ENERGY_SEND_FIELD* field = reinterpret_cast<sUBLK_LB_ENERGY_SEND_FIELD*>(recv_data_info.recv_buffer);
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
#else
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(ONLY_ONE_COPY);
      if (is_vr_fine)   // ghost vr fine ublks have two sets of states
        lb_t_data = this->lb_t_data(timestep_index);
#endif
      memcpy(&(fluid_temp[timestep_index]),     field->m_fluid_temp,    sizeof(field->m_fluid_temp));
      memcpy(&(entropy[timestep_index]),        field->m_entropy,       sizeof(field->m_entropy));
      memcpy(&(diffusivity[timestep_index]),    field->m_diffusivity,   sizeof(field->m_diffusivity));
      memcpy(lb_t_data->m_states_t,             field->m_states_t,      sizeof(field->m_states_t));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
    else {
      sUBLK_T_SCALAR_SEND_FIELD* field = reinterpret_cast<sUBLK_T_SCALAR_SEND_FIELD*>(recv_data_info.recv_buffer);
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(timestep_index);
#else
      sUBLK_LB_SOLVER_T_DATA* lb_t_data =  this->lb_t_data(ONLY_ONE_COPY); // Ghost ublks have one set of states
      if (is_vr_fine)   // ghost vr fine ublks have two sets of states
        lb_t_data = this->lb_t_data(timestep_index);
#endif
      memcpy(&(fluid_temp[timestep_index]), field->m_fluid_temp, sizeof(field->m_fluid_temp));
      memcpy(lb_t_data->m_states_t,         field->m_states_t,   sizeof(field->m_states_t));
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  static VOID add_mme_send_size(asINT32 &send_size) {
#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
    send_size      += (sizeof(sUBLK_MME_T_SEND_FIELD) / sizeof(sdFLOAT));
#endif
  }
  
#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, SOLVER_INDEX_MASK solver_ts_index_mask)   {
    asINT32 timestep_index = (solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    sUBLK_MME_T_SEND_FIELD* field = reinterpret_cast<sUBLK_MME_T_SEND_FIELD*>(send_buffer);
    memcpy(field->m_fluid_temp, &(fluid_temp[timestep_index]), sizeof(field->m_fluid_temp));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
    sUBLK_MME_T_SEND_FIELD* field = reinterpret_cast<sUBLK_MME_T_SEND_FIELD*>(recv_buffer);
    memcpy(&(fluid_temp[timestep_index]), field->m_fluid_temp, sizeof(field->m_fluid_temp));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
#elif BUILD_5G_LATTICE
  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, asINT32 timestep_index)   { }
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, asINT32 timestep_index) { }
#endif

private:
  VOID print_lb_scalar_solver_data_for_voxel(std::ostream& os,
                                             asINT32 print_voxel,
                                             asINT32 states_index)
  {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    os << "T_SOLVER_TYPE :: LB_SCALAR " << PRINT_OPTS::V2S;

    sim_print<N_LATTICE_VECTORS+1, N_VOXELS>(os, "lb_t_states",
                                           lb_t_data(states_index)->m_states_t,
                                           loop_limits(0, N_LATTICE_VECTORS), print_voxel);

    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "fluid_temp", fluid_temp, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

  VOID print_entropy_solver_data_for_voxel(std::ostream& os,
                                           asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    os << "T_SOLVER_TYPE :: ENTROPY " << PRINT_OPTS::V2S;

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "fluid_temp", fluid_temp, t_step, print_voxel);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "entropy", entropy, t_step, print_voxel);
      sim_print_vs(os);
    }
  }
  
  VOID print_lb_entropy_solver_data_for_voxel(std::ostream& os,
                                             asINT32 print_voxel,
                                             asINT32 states_index)
  {
    using namespace UBLK_SURFEL_PRINT_UTILS;
    os << "T_SOLVER_TYPE :: LB_ENTROPY " << PRINT_OPTS::V2S;
    sim_print<N_LATTICE_VECTORS+1, N_VOXELS>(os, "lb_entropy_states",
                                           lb_t_data(states_index)->m_states_t,
                                           loop_limits(0, N_LATTICE_VECTORS), print_voxel);
    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "fluid_temp", fluid_temp, t_step, print_voxel);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "entropy", entropy, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

  VOID print_lb_energy_solver_data_for_voxel(std::ostream& os,
                                             asINT32 print_voxel,
                                             asINT32 states_index)
  {
    using namespace UBLK_SURFEL_PRINT_UTILS;
    os << "T_SOLVER_TYPE :: LB_ENERGY " << PRINT_OPTS::V2S;
    sim_print<N_LATTICE_VECTORS+1, N_VOXELS>(os, "lb_energy_states",
					     lb_t_data(states_index)->m_states_t,
					     loop_limits(0, N_LATTICE_VECTORS), print_voxel);
    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "fluid_temp", fluid_temp, t_step, print_voxel);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "entropy", entropy, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

  VOID print_pde_solver_data_for_voxel(std::ostream& os,
                                       asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    os << "T_SOLVER_TYPE :: PDE " << PRINT_OPTS::V2S;

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "SOLVER_DATA", t_step);
      sim_print<N_TIME_INDICES, N_VOXELS>(os, "fluid_temp", fluid_temp, t_step, print_voxel);
      sim_print_vs(os);
    }
  }

public:
  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel,
                        asINT32 states_index)
  {
    UBLK_SURFEL_PRINT_UTILS::sim_print_data_header(os,"UBLK_T_DATA");

    switch ( sim.T_solver_type ) {

    case LB_TEMPERATURE :
      print_lb_scalar_solver_data_for_voxel(os,print_voxel,states_index);
      break;

    case PDE_ENTROPY :
      print_entropy_solver_data_for_voxel(os,print_voxel);
      break;

    case PDE_TEMPERATURE :
      print_pde_solver_data_for_voxel(os,print_voxel);
      break;

    case LB_ENTROPY :
      print_lb_entropy_solver_data_for_voxel(os, print_voxel, states_index);
      break;

    case LB_ENERGY :
      print_lb_energy_solver_data_for_voxel(os, print_voxel, states_index);
      break;  

    default :
      os << "Invalid T_SOLVER_TYPE " << (int) sim.T_solver_type << '\n';
    }
  }
      
#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(UBLK_T_DATA) {
  VEC_COPY_UBLK_TO_MBLK(fluid_temp,N_TIME_INDICES);
#if BUILD_6X_SOLVER
  VEC_COPY_UBLK_TO_MBLK(diffusivity,N_TIME_INDICES);
#endif
  VEC_COPY_UBLK_TO_MBLK(grad_t,3);
  COPY_UBLK_TO_MBLK(temp_coupling);
  COPY_UBLK_TO_MBLK(fluid_temp_pre);
  VEC_COPY_UBLK_TO_MBLK(entropy,N_TIME_INDICES);
#if BUILD_D19_LATTICE
  VEC_COPY_UBLK_TO_MBLK(force,3);
#endif
#if BUILD_D39_LATTICE
  VEC_COPY_UBLK_TO_MBLK(div_vel,N_TIME_INDICES);  // divergence of velocity
  VEC_COPY_UBLK_TO_MBLK(eta_art,N_TIME_INDICES);  // artificial viscosity proportional to pressure gradient
  VEC2_COPY_UBLK_TO_MBLK(hyb_force,N_TIME_INDICES,3);// hybrid force for rescaling speed of sound
#endif
  COPY_UBLK_TO_MBLK(lb_t_data(0));
  if (sim.T_solver_type == PDE_TEMPERATURE || sim.T_solver_type == PDE_ENTROPY) {
    COPY_UBLK_TO_MBLK(pde_t_data(0));
  }
  else if (sim.T_solver_type == LB_TEMPERATURE || sim.T_solver_type == LB_ENTROPY) {
    COPY_UBLK_TO_MBLK(lb_t_data(0));
  }
}
#endif

//------------------------------------------------------------------------------
// sNEARBLK_T_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_T_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT heat_wall;
  ALIGNED_UBFLOAT temp_wall;
  //for sliding mesh
  ALIGNED_UBFLOAT T_flux_wall;
#if BUILD_6X_SOLVER
  ALIGNED_UBFLOAT S_flux_wall;
#endif
  union {
    ALIGNED_UBFLOAT T_lrf[2][3];
    ALIGNED_UBFLOAT S_lrf[2][3];
  };
#if BUILD_D39_LATTICE
  ALIGNED_UBFLOAT den_hyb_scale;
#endif

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_SURF_T_DATA");
    sim_print<N_VOXELS>(os, "heat_wall", heat_wall, print_voxel);
    sim_print<N_VOXELS>(os, "temp_wall", temp_wall, print_voxel);
    sim_print<N_VOXELS>(os, "T_flux_wall", T_flux_wall, print_voxel);
    sim_print_vs(os);
    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "NBLK_T_DATA", t_step);
      sim_print<2, 3, N_VOXELS>(os, "T_lrf", T_lrf, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "S_lrf", S_lrf, t_step, loop_limits(0, 2), print_voxel);
      sim_print_vs(os);
    }
  }

  // stay states needed for nearblks because of modified advection scheme
  //ALIGNED_UBFLOAT m_states_t_stay[N_STATES];
  
  // All NEARBLK_T_DATA are used locally
  __DEVICE__
  VOID explode_voxel(tNEARBLK_T_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_t_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor) {}
  
  /* VOID write_ckpt() { write_ckpt_lgi(*this); }
     VOID read_ckpt()  { read_lgi(*this); }
  */
  uINT64 ckpt_len() { return 0; }
  VOID write_ckpt() {           }
  VOID read_ckpt()  {           }

  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    // Nothing to add
  }

  VOID fill_send_buffer(UBLK_SEND_DATA_INFO &send_data_info) {
    // Nothing to send
  }

  VOID expand_recv_buffer(UBLK_RECV_DATA_INFO &recv_data_info) {
    // Nothing to recv
  }

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_T_DATA) {

  COPY_UBLK_TO_MBLK(heat_wall);
  COPY_UBLK_TO_MBLK(temp_wall);
  //for sliding mesh
  COPY_UBLK_TO_MBLK(T_flux_wall);
#if BUILD_6X_SOLVER
  COPY_UBLK_TO_MBLK(S_flux_wall);
#endif
  if (sim.T_solver_type == PDE_TEMPERATURE || sim.T_solver_type == LB_TEMPERATURE) {
    VEC2_COPY_UBLK_TO_MBLK(T_lrf,2,3);
  }
  else {
    VEC2_COPY_UBLK_TO_MBLK(S_lrf,2,3);
  }

#if BUILD_D39_LATTICE
  COPY_UBLK_TO_MBLK(den_hyb_scale);
#endif
}
#endif

typedef struct sSURFEL_V2S_T_SCALAR_SEND_FIELD
{
  SURFEL_STATE      m_out_flux_t[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE m_T_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE m_heat_flux_cross_lrf;
} *SURFEL_V2S_T_SCALAR_SEND_FIELD;

typedef struct sSURFEL_T_SCALAR_SEND_FIELD
{
  STP_PHYS_VARIABLE m_turb_heat_flux;
  STP_PHYS_VARIABLE m_temp_sample;
  STP_PHYS_VARIABLE m_heat_bc;
  STP_PHYS_VARIABLE m_temp_bc;
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
} *SURFEL_T_SCALAR_SEND_FIELD;

#if BUILD_6X_SOLVER
typedef struct sSURFEL_V2S_LB_ENTROPY_SEND_FIELD
{
  SURFEL_STATE      m_out_flux_t[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE m_kappa;
  STP_PHYS_VARIABLE m_T0;  // additional variable may be required to remove the entropy flux
  STP_PHYS_VARIABLE m_S0;  // additional variable may be required to remove the entropy flux
  STP_PHYS_VARIABLE m_T_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE m_heat_flux_cross_lrf;
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
} *SURFEL_V2S_LB_ENTROPY_SEND_FIELD;

typedef struct sSURFEL_LB_ENTROPY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_turb_heat_flux;
  STP_PHYS_VARIABLE m_temp_sample;
  STP_PHYS_VARIABLE m_entropy_sample;
  STP_PHYS_VARIABLE m_heat_bc;
  STP_PHYS_VARIABLE m_temp_bc;
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
} *SURFEL_LB_ENTROPY_SEND_FIELD;

#endif

#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
typedef struct sSURFEL_V2S_LB_ENERGY_SEND_FIELD
{
  SURFEL_STATE      m_out_flux_t[N_SURFEL_PGRAM_VOLUMES];
  //STP_PHYS_VARIABLE m_kappa;
  STP_PHYS_VARIABLE m_T_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE m_heat_flux_cross_lrf;
} *SURFEL_V2S_LB_ENERGY_SEND_FIELD;

typedef struct sSURFEL_LB_ENERGY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_turb_heat_flux;
  STP_PHYS_VARIABLE m_temp_sample;
  STP_PHYS_VARIABLE m_entropy_sample;
  STP_PHYS_VARIABLE m_heat_bc;
  STP_PHYS_VARIABLE m_temp_bc;
} *SURFEL_LB_ENERGY_SEND_FIELD;
#endif

typedef struct sSURFEL_V2S_T_PDE_SEND_FIELD
{// for both T_pde and S_pde
  STP_PHYS_VARIABLE m_kappa;
  STP_PHYS_VARIABLE m_T0;
  STP_PHYS_VARIABLE m_heat_flux_cross_lrf;
} *SURFEL_V2S_T_PDE_SEND_FIELD;

typedef struct sSURFEL_T_PDE_SEND_FIELD
{
  STP_PHYS_VARIABLE m_turb_heat_flux;
  STP_PHYS_VARIABLE m_temp_sample;
  STP_PHYS_VARIABLE m_heat_bc;
  STP_PHYS_VARIABLE m_temp_bc;
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
} *SURFEL_T_PDE_SEND_FIELD;

typedef struct sSURFEL_ENTROPY_SEND_FIELD
{
  STP_PHYS_VARIABLE m_turb_heat_flux;
  STP_PHYS_VARIABLE m_temp_sample;
  STP_PHYS_VARIABLE m_entropy_sample;
  //STP_PHYS_VARIABLE m_kappa;
  //STP_PHYS_VARIABLE m_T0;
  //STP_PHYS_VARIABLE m_heat_flux_cross_lrf;
  STP_PHYS_VARIABLE m_heat_bc;
  STP_PHYS_VARIABLE m_temp_bc;
#if BUILD_D39_LATTICE
  STP_PHYS_VARIABLE m_hyb_force[3];
#endif
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
} *SURFEL_ENTROPY_SEND_FIELD;


//------------------------------------------------------------------------------
// sSURFEL_T_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_V2S_T_DATA {
  EXTRACT_SURFEL_TRAITS
#if BUILD_6X_SOLVER
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_T_bar[N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<SURFEL_STATE, N>      m_in_states_t[N_SURFEL_PGRAM_VOLUMES]; 
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_kappa; // Used by PDE_T, PDE_entropy, LB_entropy solvers
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_T0;    // Used by PDE_T, PDE_entropy, LB_entropy solvers
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_S0;    // Used by LB_entropy solver
#else   //5X, 39s, and 5g
  union{
    struct { //scalar solver
      tSFL_VAR<STP_PHYS_VARIABLE, N> m_T_bar[N_SURFEL_PGRAM_VOLUMES];
      tSFL_VAR<STP_PHYS_VARIABLE, N> m_in_states_t[N_SURFEL_PGRAM_VOLUMES];
    };
    struct {  //pde solver
      tSFL_VAR<STP_PHYS_VARIABLE, N> m_kappa;
      tSFL_VAR<STP_PHYS_VARIABLE, N> m_T0;
    };
  };
#endif

  tSFL_VAR<STP_PHYS_VARIABLE, N> m_heat_flux_cross_lrf;
  
  // Even though this is part of the scalar solver, this is
  // put in the end to aid reset
  tSFL_VAR<SURFEL_STATE, N> m_out_flux_t[N_SURFEL_PGRAM_VOLUMES];

  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_V2S_T_DATA");
    sim_print(os, "heat_flux_cross_lrf", m_heat_flux_cross_lrf);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "out_flux_t", m_out_flux_t);

    switch ( sim.T_solver_type ) {
    case LB_TEMPERATURE :
      sim_print_vs(os);
      sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "T_bar", m_T_bar);
      sim_print_vs(os);
      sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "in_states_t", m_in_states_t);
      break;
    case PDE_TEMPERATURE :
      sim_print_vs(os);
      sim_print(os, "kappa", m_kappa);
      sim_print(os, "T0", m_T0);
      break;
    default:
      ;
    }
  }

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID read_ckpt()  { read_lgi(*this);       }
  VOID write_ckpt() { write_ckpt_lgi(*this); }

  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_V2S_T_DATA));
  }
  VOID reset_except_outflux_t() {
    memset(this, 0, (sizeof(tSURFEL_V2S_T_DATA) - sizeof(STP_PHYS_VARIABLE)*N_SURFEL_PGRAM_VOLUMES));
  }

  __DEVICE__ VOID reset(int soxor,
                        BOOLEAN is_even_or_odd,
                        BOOLEAN is_odd,
                        BOOLEAN is_timestep_even) {
    
    if (!is_even_or_odd || is_timestep_even) {
#if BUILD_6X_SOLVER
      ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
        m_T_bar[i][soxor] = 0.0F;
        m_in_states_t[i][soxor] = 0.0F;
      }
      m_kappa[soxor] = 0.0F;
      m_T0[soxor] = 0.0F;
      m_S0[soxor] = 0.0F;
#else   //5X, 39s, and 5g
      assert(false);
#endif

      m_heat_flux_cross_lrf[soxor] = 0.0F;

      if (!is_odd) {
        ccDOTIMES(i, N_SURFEL_PGRAM_VOLUMES) {
          m_out_flux_t[i][soxor] = 0.0F;
        }
      }
    }
  }
  VOID reflect_to_real_surfel(tSURFEL_V2S_T_DATA *mirror_v2s_t_data,
                              STP_LATVEC_MASK latvec_mask,
                              STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                              STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                              BOOLEAN is_T_lb_solver_on) {
    if (is_T_lb_solver_on) {
#if BUILD_6X_SOLVER
      if (sim.T_solver_type == LB_ENTROPY) {
	m_kappa += mirror_v2s_t_data->m_kappa;
      }
#endif
      ccDOTIMES(state_index, N_MOVING_STATES) {
        if (((latvec_mask >> state_index) & 1) == 0)
          continue;
        STP_STATE_INDEX source_state_index = state_index;
        STP_STATE_INDEX source_latvec_pair_index = state_latvec_pair(source_state_index);
        STP_STATE_INDEX mirror_latvec_pair_index = reflected_latvec_pair[source_latvec_pair_index];

        m_in_states_t[source_latvec_pair_index] += mirror_v2s_t_data->m_in_states_t[mirror_latvec_pair_index];
        m_T_bar[source_latvec_pair_index] += mirror_v2s_t_data->m_T_bar[mirror_latvec_pair_index];
      }
    } else {
      m_kappa += mirror_v2s_t_data->m_kappa;
    }
  }
  VOID reflect_to_mirror_surfel(tSURFEL_V2S_T_DATA *real_v2s_t_data,
                                STP_LATVEC_MASK latvec_mask,
                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES],
                                STP_STATE_INDEX   reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                BOOLEAN is_T_lb_solver_on) {
    if (is_T_lb_solver_on) {
#if BUILD_6X_SOLVER
      if (sim.T_solver_type == LB_ENTROPY) {
	m_kappa = real_v2s_t_data->m_kappa;
      }
#endif
      ccDOTIMES(state_index, N_MOVING_STATES) {
        if (((latvec_mask >> state_index) & 1) == 0)
          continue;
        STP_STATE_INDEX mirror_state_index        = state_index;
        STP_STATE_INDEX mirror_latvec_pair_index  = state_latvec_pair(mirror_state_index);
        STP_STATE_INDEX source_latvec_pair_index  = reflected_latvec_pair[mirror_latvec_pair_index];
        m_out_flux_t[mirror_latvec_pair_index] = real_v2s_t_data->m_out_flux_t[source_latvec_pair_index];
        m_T_bar[mirror_latvec_pair_index] = real_v2s_t_data->m_T_bar[source_latvec_pair_index];
      }
    } else {
      m_kappa = real_v2s_t_data->m_kappa;
      //T0 = source_t_data->T0; //lrf surfel cannot be mirror surfel
    }
  }

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_V2S_T_DATA *even_v2s_t_data,
                             asINT32 esoxor, asINT32 osoxor) {
    auto& simc = get_simc_ref();
    if (simc.is_T_S_solver_type_lb()) {
      ccDOTIMES(j, N_SURFEL_PGRAM_VOLUMES) {
        m_in_states_t[j][osoxor] = even_v2s_t_data->m_in_states_t[j][esoxor];
      }
    }    
  }

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_T_DATA *even_v2s_t_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
                                        asINT32 osoxor, asINT32 esoxor) {
    auto& simc = get_simc_ref();
    if (simc.T_solver_type == PDE_TEMPERATURE
        || simc.T_solver_type == PDE_ENTROPY
#if BUILD_6X_SOLVER      
        || simc.T_solver_type == LB_ENTROPY
#endif
        ){
      m_kappa[osoxor] = even_v2s_t_data->m_kappa[esoxor] * mme_weight_inverse;
      m_kappa[osoxor] *= simc.thermal_feedback_is_on ? mme_weight_inverse/even_density : 1;    
    }
  }

  __HOST__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_T_DATA *even_v2s_t_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);  

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
    if (is_lb_entropy_solver_on)
      send_size += (sizeof(sSURFEL_V2S_LB_ENTROPY_SEND_FIELD) / sizeof(sdFLOAT));
    else
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    if (sim.T_solver_type == LB_ENERGY)  
      send_size += (sizeof(sSURFEL_V2S_LB_ENERGY_SEND_FIELD) / sizeof(sdFLOAT));
    else
#endif
      send_size += (sizeof(sSURFEL_V2S_T_SCALAR_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += ( sizeof(sSURFEL_V2S_T_PDE_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    BOOLEAN is_T_pde_on = (send_data_info.t_solver_type == PDE_TEMPERATURE);
     BOOLEAN is_entropy_pde_on = (send_data_info.t_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif 
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif 
    if(is_T_pde_on || is_entropy_pde_on) {
      SURFEL_V2S_T_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_T_PDE_SEND_FIELD>(send_data_info.send_buffer);
      field->m_kappa               = m_kappa;
      field->m_T0                  = m_T0;
      field->m_heat_flux_cross_lrf = m_heat_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      SURFEL_V2S_LB_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_ENTROPY_SEND_FIELD>(send_data_info.send_buffer);  
      memcpy(field->m_out_flux_t, m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(field->m_T_bar,      m_T_bar,      sizeof(field->m_T_bar)); 
      field->m_kappa               = m_kappa;
      field->m_T0                  = m_T0;
      field->m_S0                  = m_S0;
      field->m_heat_flux_cross_lrf = m_heat_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif 
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on) {
      SURFEL_V2S_LB_ENERGY_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_ENERGY_SEND_FIELD>(send_data_info.send_buffer);  
      memcpy(field->m_out_flux_t, m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(field->m_T_bar,      m_T_bar,      sizeof(field->m_T_bar)); 
      //field->m_kappa               = m_kappa;
      field->m_heat_flux_cross_lrf = m_heat_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif 
    else { //lb_temperature
      SURFEL_V2S_T_SCALAR_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_T_SCALAR_SEND_FIELD>(send_data_info.send_buffer);
      memcpy(field->m_out_flux_t, m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(field->m_T_bar,      m_T_bar,      sizeof(field->m_T_bar));
      field->m_heat_flux_cross_lrf = m_heat_flux_cross_lrf;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    BOOLEAN is_T_pde_on = (recv_data_info.t_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_pde_on = (recv_data_info.t_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif  
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif 
    if(is_T_pde_on || is_entropy_pde_on) {
      SURFEL_V2S_T_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_T_PDE_SEND_FIELD>(recv_data_info.recv_buffer);
      m_kappa               = field->m_kappa;
      m_T0                  = field->m_T0;
      m_heat_flux_cross_lrf = field->m_heat_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      SURFEL_V2S_LB_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_ENTROPY_SEND_FIELD>(recv_data_info.recv_buffer);  
      memcpy(m_out_flux_t, field->m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(m_T_bar,      field->m_T_bar,      sizeof(field->m_T_bar));
      m_kappa               = field->m_kappa;
      m_T0                  = field->m_T0;
      m_S0                  = field->m_S0;
      m_heat_flux_cross_lrf = field->m_heat_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on) {
      SURFEL_V2S_LB_ENERGY_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_LB_ENERGY_SEND_FIELD>(recv_data_info.recv_buffer);  
      memcpy(m_out_flux_t, field->m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(m_T_bar,      field->m_T_bar,      sizeof(field->m_T_bar));
      //m_kappa               = field->m_kappa;
      m_heat_flux_cross_lrf = field->m_heat_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
    else {
      SURFEL_V2S_T_SCALAR_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_T_SCALAR_SEND_FIELD>(recv_data_info.recv_buffer);
      memcpy(m_out_flux_t, field->m_out_flux_t, sizeof(field->m_out_flux_t));
      memcpy(m_T_bar,      field->m_T_bar,      sizeof(field->m_T_bar));
      m_heat_flux_cross_lrf = field->m_heat_flux_cross_lrf;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

};

using sSURFEL_V2S_T_DATA = tSURFEL_V2S_T_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_T_DATA = sSURFEL_V2S_T_DATA*;

template<>
__HOST__ INLINE
VOID tSURFEL_V2S_T_DATA<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(tSURFEL_V2S_T_DATA<SFL_SDFLOAT_TYPE_TAG> *even_v2s_t_data,
                                                             STP_PHYS_VARIABLE even_density,
                                                             STP_SURFEL_WEIGHT mme_weight_inverse,
                                                             STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) {
  this->pre_advect_init_copy_even_to_odd(even_v2s_t_data, even_density, mme_weight_inverse, s2s_sampling_weight_inverse, 0, 0);
}

#if BUILD_GPU
INIT_MSFL(tSURFEL_V2S_T_DATA);
#endif

template<typename SFL_TYPE_TAG>
struct tSURFEL_T_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N> turb_heat_flux;
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample;
  tSFL_VAR<STP_PHYS_VARIABLE, N> entropy_sample;

  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_bc;
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_bc;
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample_prime;
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N> entropy_sample_prime;
#endif
  tSFL_VAR<STP_PHYS_VARIABLE, N> un_wall_temp;
#if BUILD_6X_SOLVER
  tSFL_VAR<STP_PHYS_VARIABLE, N> un_t_p;
#else
  tSFL_VAR<STP_PHYS_VARIABLE, N> un_rho_p;
#endif
  //STP_PHYS_VARIABLE un_temp_proj;
  //STP_PHYS_VARIABLE un_ktn;

#if BUILD_D39_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N>  hyb_force[3]; // hybrid force sampled from the voxels
#endif // BUILD_D39_LATTICE

  tSFL_VAR<STP_PHYS_VARIABLE, N> init_ht; //momentum freeze
  tSFL_VAR<STP_PHYS_VARIABLE, N> init_mt; //momentum freeze
 
  //for new TWF of 6X MF
#if BUILD_6X_SOLVER
  tSFL_VAR<STP_PHYS_VARIABLE, N> init_ht5;
  tSFL_VAR<STP_PHYS_VARIABLE, N> init_rh; 
  tSFL_VAR<STP_PHYS_VARIABLE, N> init_tke;
#endif

  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_flux;
  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_flux_prime;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
  tSFL_VAR<STP_PHYS_VARIABLE, N> outgoing_heat_flux[N_TIME_INDICES]; // CONDUCTION-TODO: should be moved to s2s_t_data
#endif
  tSFL_VAR<STP_PHYS_VARIABLE, N> s2s_heat_flux_source; // CONDUCTION-TODO: should be moved to s2s_t_data

  VOID print_surfel_data(std::ostream& os) {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_T_DATA");
    sim_print(os, "turb_heat_flux", turb_heat_flux);
    sim_print(os, "temp_sample", temp_sample);
    sim_print(os, "entropy_sample", entropy_sample);
    sim_print(os, "heat_bc", heat_bc);
    sim_print(os, "temp_bc", temp_bc);
    sim_print(os, "temp_sample_prime", temp_sample_prime);
    sim_print(os, "un_wall_temp", un_wall_temp);
#if BUILD_6X_SOLVER
    sim_print(os, "un_t_p", un_t_p);
#else
    sim_print(os, "un_rho_p", un_rho_p);
#endif
    //STP_PHYS_VARIABLE un_temp_proj;
    //STP_PHYS_VARIABLE un_ktn;

#if BUILD_D39_LATTICE
    sim_print<3>(os, "hyb_force", hyb_force); // hybrid force sampled from the voxels
#endif // BUILD_D39_LATTICE

    sim_print(os, "init_ht", init_ht); //momentum freeze
    sim_print(os, "init_mt", init_mt); //momentum freeze
  }

  VOID pre_advect_init();
  
  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_T_DATA *even_surfel_t_data, 
					STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse, 
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
                                        asINT32 osoxor, asINT32 esoxor) 
  {
    auto& simc = get_simc_ref();
    /* Since the temp_sample is rescaled in surfel dynamics, we need a pre-rescaled
     * (real post-advect) temp_sample from even_surfel to update odd_surfel. */
    BOOLEAN is_entropy_solver = (simc.T_solver_type == PDE_ENTROPY 
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
                                 || simc.T_solver_type == LB_ENTROPY
                                 || simc.T_solver_type == LB_ENERGY
#endif
                                );  

    temp_sample[osoxor] = even_surfel_t_data->temp_sample[esoxor] * 
      (g_use_lrf_s2s_sampling_temp ? s2s_sampling_weight_inverse : mme_weight_inverse);

    if (is_entropy_solver) {
      entropy_sample[osoxor] = even_surfel_t_data->entropy_sample[esoxor] * 
        (g_use_lrf_s2s_sampling_temp ? s2s_sampling_weight_inverse : mme_weight_inverse);
#if BUILD_D39_LATTICE
      if (simc.T_solver_type == PDE_ENTROPY) {
#if !BUILD_GPU
        memcpy(this->hyb_force, even_surfel_t_data->hyb_force, sizeof(this->hyb_force));
#else
        assert(false);
#endif
      }
#endif
    }

#ifdef OLD_SURREL_DATA_STRUCTURE
    if (simc.T_solver_type == PDE_TEMPERATURE
        || simc.T_solver_type == PDE_ENTROPY
#if BUILD_6X_SOLVER      
        || simc.T_solver_type == LB_ENTROPY
#endif
       ){
      kappa[osoxor] = even_surfel_t_data->kappa[esoxor] * mme_weight_inverse;
      kappa[osoxor] *= simc.thermal_feedback_is_on ? mme_weight_inverse/even_density : 1;
    }
#endif
  }

  VOID seed_copy_even_to_odd(tSURFEL_T_DATA *even_surfel_t_data);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
    if (is_lb_entropy_solver_on) {
      send_size += (sizeof(sSURFEL_LB_ENTROPY_SEND_FIELD) / sizeof(sdFLOAT));
    } else
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    if (sim.T_solver_type == LB_ENERGY) {
      send_size += (sizeof(sSURFEL_LB_ENERGY_SEND_FIELD) / sizeof(sdFLOAT));
    } else
#endif
      send_size += (sizeof(sSURFEL_T_SCALAR_SEND_FIELD) / sizeof(sdFLOAT));
    if (sim.T_solver_type == PDE_TEMPERATURE)
      tpde_send_size += ( sizeof(sSURFEL_T_PDE_SEND_FIELD) / sizeof(sdFLOAT));
    else if (sim.T_solver_type == PDE_ENTROPY)
      tpde_send_size += ( sizeof(sSURFEL_ENTROPY_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
#if BUILD_D19_LATTICE
    init_ht = -1.0;  //will be assigned in surfel seeding
    init_mt = -1.0;
#endif
#if BUILD_6X_SOLVER
    init_ht5 = -1.0;
    init_rh = -1.0;
    init_tke = -1.0;
#endif
  }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_T_DATA *mirror_t_data,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 //STP_STATE_INDEX reflected_states[N_STATES],
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_T_DATA *source_t_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 //STP_STATE_INDEX reflected_states[N_STATES],
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    BOOLEAN is_T_pde_on = (send_data_info.t_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.T_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif 
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif 
    if(is_T_pde_on) {
      SURFEL_T_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_T_PDE_SEND_FIELD>(send_data_info.send_buffer);
      field->m_turb_heat_flux   = turb_heat_flux;
      field->m_temp_sample      = temp_sample;
      field->m_heat_bc          = heat_bc;
      field->m_temp_bc          = temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(field->m_outgoing_heat_flux, outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
#endif
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else if (is_entropy_solver_on) {
      SURFEL_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_ENTROPY_SEND_FIELD>(send_data_info.send_buffer);
      field->m_turb_heat_flux      = turb_heat_flux;
      field->m_temp_sample         = temp_sample;
      field->m_entropy_sample      = entropy_sample;
      field->m_heat_bc             = heat_bc;
      field->m_temp_bc             = temp_bc;
#if BUILD_D39_LATTICE
      memcpy(&field->m_hyb_force, &hyb_force, sizeof(field->m_hyb_force));
#endif
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(field->m_outgoing_heat_flux, outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
#endif
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }     
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      SURFEL_LB_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_LB_ENTROPY_SEND_FIELD>(send_data_info.send_buffer);
      field->m_turb_heat_flux = turb_heat_flux;
      field->m_temp_sample         = temp_sample;
      field->m_entropy_sample      = entropy_sample;
      field->m_heat_bc          = heat_bc;
      field->m_temp_bc          = temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(field->m_outgoing_heat_flux, outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
#endif
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on) {
      SURFEL_LB_ENERGY_SEND_FIELD field = reinterpret_cast<SURFEL_LB_ENERGY_SEND_FIELD>(send_data_info.send_buffer);
      field->m_turb_heat_flux   = turb_heat_flux;
      field->m_temp_sample      = temp_sample;
      field->m_entropy_sample   = entropy_sample;
      field->m_heat_bc          = heat_bc;
      field->m_temp_bc          = temp_bc;
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
    else {
      SURFEL_T_SCALAR_SEND_FIELD field = reinterpret_cast<SURFEL_T_SCALAR_SEND_FIELD>(send_data_info.send_buffer);
      field->m_turb_heat_flux   = turb_heat_flux;
      field->m_temp_sample      = temp_sample;
      field->m_heat_bc          = heat_bc;
      field->m_temp_bc          = temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(field->m_outgoing_heat_flux, outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
#endif
      field++;
      send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    BOOLEAN is_T_pde_on = (recv_data_info.t_solver_type == PDE_TEMPERATURE);
    BOOLEAN is_entropy_solver_on = (sim.T_solver_type == PDE_ENTROPY);
#if BUILD_6X_SOLVER
    BOOLEAN is_lb_entropy_solver_on = (sim.T_solver_type == LB_ENTROPY);
#endif 
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    BOOLEAN is_lb_energy_solver_on = (sim.T_solver_type == LB_ENERGY);
#endif 
    if (is_T_pde_on) {
      SURFEL_T_PDE_SEND_FIELD field = reinterpret_cast<SURFEL_T_PDE_SEND_FIELD>(recv_data_info.recv_buffer);
      turb_heat_flux   = field->m_turb_heat_flux;
      temp_sample      = field->m_temp_sample;
      heat_bc          = field->m_heat_bc;
      temp_bc          = field->m_temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(outgoing_heat_flux, field->m_outgoing_heat_flux, sizeof(outgoing_heat_flux));
#endif
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    } else if (is_entropy_solver_on) {
      SURFEL_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_ENTROPY_SEND_FIELD>(recv_data_info.recv_buffer);
      turb_heat_flux      = field->m_turb_heat_flux;
      temp_sample         = field->m_temp_sample;
      entropy_sample      = field->m_entropy_sample;
      heat_bc             = field->m_heat_bc;
      temp_bc             = field->m_temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(outgoing_heat_flux, field->m_outgoing_heat_flux, sizeof(outgoing_heat_flux));
#endif
#if BUILD_D39_LATTICE
      memcpy(hyb_force, &(field->m_hyb_force), sizeof(field->m_hyb_force));
#endif
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    } 
#if BUILD_6X_SOLVER
    else if (is_lb_entropy_solver_on) {
      SURFEL_LB_ENTROPY_SEND_FIELD field = reinterpret_cast<SURFEL_LB_ENTROPY_SEND_FIELD>(recv_data_info.recv_buffer);
      turb_heat_flux   = field->m_turb_heat_flux;
      temp_sample      = field->m_temp_sample;
      entropy_sample   = field->m_entropy_sample;
      heat_bc          = field->m_heat_bc;
      temp_bc          = field->m_temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(outgoing_heat_flux, field->m_outgoing_heat_flux, sizeof(outgoing_heat_flux));
#endif
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
    else if (is_lb_energy_solver_on) {
      SURFEL_LB_ENERGY_SEND_FIELD field = reinterpret_cast<SURFEL_LB_ENERGY_SEND_FIELD>(recv_data_info.recv_buffer);
      turb_heat_flux   = field->m_turb_heat_flux;
      temp_sample      = field->m_temp_sample;
      entropy_sample   = field->m_entropy_sample;
      heat_bc          = field->m_heat_bc;
      temp_bc          = field->m_temp_bc;
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
#endif
    else {
      SURFEL_T_SCALAR_SEND_FIELD field = reinterpret_cast<SURFEL_T_SCALAR_SEND_FIELD>(recv_data_info.recv_buffer);
      turb_heat_flux   = field->m_turb_heat_flux;
      temp_sample      = field->m_temp_sample;
      heat_bc          = field->m_heat_bc;
      temp_bc          = field->m_temp_bc;
#ifndef CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
      memcpy(outgoing_heat_flux, field->m_outgoing_heat_flux, sizeof(outgoing_heat_flux));
#endif
      field++;
      recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
    }
  }
};

using sSURFEL_T_DATA = tSURFEL_T_DATA<SFL_SDFLOAT_TYPE_TAG>;
using  SURFEL_T_DATA = sSURFEL_T_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_T_DATA);
#endif

template<typename SFL_TYPE_TAG>
struct tSURFEL_S2S_T_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<SURFEL_STATE, N>  m_out_flux_t[N_S2S_TIME_INDICES][N_SURFEL_PGRAM_VOLUMES];

  uINT64 ckpt_len() { return sizeof(*this); }
  VOID read_ckpt()  { read_lgi(*this);      }
  VOID write_ckpt() { write_ckpt_lgi(*this);}

};

using sSURFEL_S2S_T_DATA = tSURFEL_S2S_T_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_S2S_T_DATA = sSURFEL_S2S_T_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_S2S_T_DATA);
#endif

static const asINT32 SURFEL_T_DATA_CKPT_SIZE = sizeof(sSURFEL_T_DATA);

//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_T_DATA
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_T_DATA
{
  STP_PHYS_VARIABLE fluid_temp;
  STP_PHYS_VARIABLE fluid_temp_prime;
  VOID pre_advect_init() {
    fluid_temp = 0;
  }
  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_T_DATA *even_surfel_t_data);
  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }
} *SAMPLING_SURFEL_T_DATA;

template<typename SIM_INFO_TYPE>
VOID set_hs_and_ts_solver_types(SIM_INFO_TYPE& sim_info) {
  //determine T_solver type for HS and TS
  BOOLEAN is_pre_7_25_cdi = ((sim_info.cdi_major_version < 7) ||
			     (sim_info.cdi_major_version==7 && sim_info.cdi_minor_version < 25));
  sim_info.use_lb_energy_solver = FALSE;
  sim_info.use_hs_lb_entropy_solver = FALSE;
 
  //no worry about 5X any more, it is phased out.
#if BUILD_6X_SOLVER
  if (sim_info.is_high_subsonic_mach_regime) {
    if (is_pre_7_25_cdi) {
      sim_info.use_lb_energy_solver = TRUE;
    } else {
      if (sim_info.is_legacy_energy_solver)
	sim_info.use_hs_lb_entropy_solver = TRUE;
      else
	sim_info.use_lb_energy_solver = TRUE;
    }
  }
#elif BUILD_D39_LATTICE
  if (is_pre_7_25_cdi) {
    sim_info.use_lb_energy_solver = TRUE;
  } else {
    if (!(sim_info.is_legacy_energy_solver && sim_info.use_hybrid_ts_hs_solver))
      sim_info.use_lb_energy_solver = TRUE;
  }
#endif  
}

} //inline namespace SIMULATOR_NAMESPACE
#endif//_SIMENG_T_SOLVER_DATA_H
