/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Common definitions for sim engine
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Jan 21 1994
 *--------------------------------------------------------------------------*/

#ifndef __SIM_ENG_COMMON_H
#define __SIM_ENG_COMMON_H

#ifdef __LINUX__

#define DEBUG_MPI_CALLS 0 

// For the time being, continue to use old stencil construction
// for particle solver. The unified surfel stencil construction
// will need to be used eventually.
#define CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
#define CONDUCTION_ENABLE_MESSAGE_FOR_DISC_DEBUGGING
#define CONDUCTION_ENABLE_SHELL_CONDUCTION 1
#define CONDUCTION_HACK_SHELL_CONDUCTION_ALWAYS_ON
#define CONDUCTION_ENABLE_DEFENSIVE_CHECKS
#define CONDUCTION_SHARED_SURFEL 0
#define CONDUCTION_SHARED_VOXEL_MEAS 1
#define CONDUCTION_USE_COMMON_MIN_FACE_AREA
#define CONDUCTION_DISABLE_MULTIPROC_SHELL_CONDUCTION
#define CONDUCTION_DISABLE_STENCIL_SIM_ERRORS
#define CONDUCTION_HARDCODE_ONE_SIDE_OF_ISURFEL_AS_SHELL
#define CONDUCTION_HACK_HARDCODED_SHELL_INFO 0 
#define CONDUCTION_PERMIT_REDUNDANT_STORAGE_FOR_SHELLS 1
#define CONDUCTION_DISABLE_FILM_SOLVER_WITH_SHELL_CONDUCTION
#define CONDUCTION_HACK_SURFEL_MEAS_FOR_SHELLS
#define CONDUCTION_USE_HARDCODED_SURFEL_INIT_FOR_ISURFELS
#define CONDUCTION_ENFORCE_SURFEL_STENCILS_INITIALIZE_DUE_TO_HARDCODE_IN_CP
// Uncomment for the simulator to print any empty edges for debugging.
// #define PRINT_EMPTY_SHELL_EDGES
// For now, grad_t is calculated separately than temp_sample. This is
// because every BC edge requires its own temp_sample set and this would mean
// define global storage for all BC edges on this SP. If grad_t is not
// combined with temp_sample calculation, then temp_sample set for
// every BC edge can be computed on the fly in surfel dynamics. Whether
// this is most optimal is yet to be studied.
#define CONDUCTION_USE_SEPARATE_SAMPLE_CALLS_FOR_SHELL_SURFELS
// For the first stage of development, only isotropic conductivity is considered
//#define CONDUCTION_USE_ISOTROPIC_CONDUCTIVITY_FOR_SHELL_CONDUCTION
// Temporarily allow unnecessary datablocks
#define CONDUCTION_ALLOW_UNNECESSARY_DATABLOCKS_TEMPORARILY
// DISC provides v2s information based on whether the given voxel does
// any V2S into the surfel - for odd surfels, this means that the voxels
// at > 1 voxel length away from the surfel are listed which will lead
// to incorrect sampling. As a workaround, we will ignore odd surfels'
// v2s information and instead use corresponding even surfel's v2s information.
// Also, even surfel S2V maybe missing VR Coarse voxels to which contribution is to be
// sent. Hence, we will fully copy even surfel information into odd
// counterparts till we get correct v2s/s2v info from discretizer
#define CONDUCTION_FULL_COPY_EVEN_INFO_INTO_ODD_SURFELS
// Till simulator support for FSETs comes in, use following macro
// to performed explicit loop/testing for shell surfels
#define CONDUCTION_SHELL_SURFEL_FSET_NOT_DEFINED
// Have to figure out a way to communicate only one time-index. For now,
// use this macro to keep track of places where both time indices are sent/received.
#define CONDUCTION_SHELL_COMM_BOTH_TIME_INDICES
#define CONDUCTION_USE_CHAR_DENSITY_FOR_CONDUCTION_VOLUMES
// We are not handling sampling distances for thin volumes correctly yet due
// to the S2S component. 
#define CONDUCTION_SET_LOWER_LIMIT_FOR_SAMPLING_DISTANCE_IN_S2S_SITUATIONS
// For source terms for volumetric sides in quasi-1D system, a simple
// finite-difference form can be used. A more advanced (and accurate)
// form would be to use summ_over_x[ w_a(x) * (k(x) . grad_t(x)) . n_a)]
// to given an estimate of purely volume-side heat flux information
#define CONDUCTION_USE_SIMPLE_FD_FOR_QUASI_1D_SOURCE
// Implementing grad_t form is more work on the fluid-side (due to
// requiring accurate grad_t's at voxels). We will keep this for later.
#define CONDUCTION_QUASI_1D_SOURCE_USE_SIMPLE_FD_FOR_FLUID_SIDE
// Discretizer does not generate any v2s/s2s/s2v weight for "tiny surfels"
// which is any surfel with area < 2e-7. However, if those surfels could
// not merged during physics merger to neighbors, they are still reported
// in the LGI as valid surfels (but with no weights). This will lead
// to sampling on such surfels equal to 0. We throw errors for surfels
// which have sample values of 0, but we will use following macro
// to throw a warning for tiny surfels instead. Ideally, this should
// be caught during parsing. One possible fix might be that the discretizer
// forces merge of such tiny surfels into their neighbors.
#define CONDUCTION_ISSUE_WARNING_FOR_TINY_SURFELS_WITH_NO_WEIGHTS
#define TINY_SURFEL_AREA_THRESHOLD 2.0e-7
// In conduction voxel dynamics, the special case uses a distance
// criteria if following is defined. If not, the simple form is used
// only when pfluid of both voxels is 1.0
#define CONDUCTION_SIMPLE_FD_FLUX_FORM_BASED_ON_DISTANCE
// Order of neighbors to include in LS stencil on first attempt
// i.e. 1 is root surfel neighs, 2 is next neighbors, etc.
// If matrix is degenerate, the order will increase and try again.
// Now set using g_pfc_shell_LSQ_stencil_max_neighbor_order (defaults to 2)
//#define CONDUCTION_SHELL_LS_STENCIL_NEIGH_ORDER_MAX 2
// Use harmonic mean for interpolating conductivity to edges in shell
// conduction solver. This is okay when shells have isotropic conductivity,
// but is not correct when the conductivity is anisotropic.
#define CONDUCTION_SHELL_HARMONIC_MEAN_FOR_CONDUCTIVITY
// In stencil construction, maintain ordering as per old scheme (i.e.,
// is the 0th edge going from vertex 0 -> 1 or (n-1) -> 0
#define CONDUCTION_STENCILS_REPLICATE_OLD_ORDERING
// Following captures computing s2s weights on the fly. We will need
// to compute this elsewhere (DISC or during s2s interactions) and
// store. If pgram based s2s weights are being used, following
// should not do anything
#define CONDUCTION_COMPUTE_S2S_WEIGHTS_ON_THE_FLY
// Fetch staggered coupling parameters via exa turb files. Use this
// hack till simulator provides phase table and individual phase
// information correctly
#define CONDUCTION_HACKED_STAGGERED_COUPLING_FRAMEWORK
// Do we need LRF capability for conduction surfaces? For now assume
// answer is NO.
//#define CONDUCTION_DISALLOW_LRF_CONDUCTION_SURFACES
// Disable use of last_* masks. Use this till we are sure we can
// get rid of all the last_* masks
#define CONDUCTION_ENABLE_PTHRU_COARSE_TO_FINE

// Could possibly include passthrough flux into fixed temp surfels in calculation of flux like q_anisotropic is
// Right now, the passed through energy is stored in q_passthrough, but not utilized.
#define CONDUCTION_ENABLE_PTHRU_TO_DIRICHLET_BOUNDARIES 1
#define CONDUCTION_ENABLE_PTHRU_TO_COUPLED_BOUNDARIES 1

// Across LRF is not fully implemented yet
#define CONDUCTION_ENABLE_PTHRU_ACROSS_LRF 1

#define CONDUCTION_ENABLE_PTHRU_TO_SURFELS CONDUCTION_ENABLE_PTHRU_TO_DIRICHLET_BOUNDARIES \
                                        || CONDUCTION_ENABLE_PTHRU_TO_COUPLED_BOUNDARIES   \
                                        || CONDUCTION_ENABLE_PTHRU_ACROSS_LRF

#if !BUILD_GPU
#define SUPERCYCLING_DO_NOT_USE_LAST_ODD_MASKS
#endif
// When flow is subcycling, multiple comms would happen two LB
// dynamics, and currently we are comm'ing all solvers (not just
// the active ones). This means, ghost UBLKs may end up with
// states from wrong index. Till we comm only the active solvers,
// following enforces two state copies
#define ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
// Conduction solver code is sprinkled with a number of parameters,
// options and flags. Following macros uses default values rather
// than check the branches
#define CONDUCTION_USE_DEFAULT_FOR_ALL_PARAMETERS
// To keep things simple, do not under-relax heat flux values
// at layer-layer interface. It might be eventually desirable
// to under-relax these to improve stability.
#define CONDUCTION_DO_NOT_UNDERRELAX_SHELL_LAYER_INTERFACE_FLUXES
// Since conduction V2S weights have an independent definition, they
// should be normalized (by whoever computes them - discretizer or simulator).
// If the summation of v2s weights add up to 1, there will be no
// need to normalize them during surfel dynamics. Following macro
// will be used to wrap such normalizing code till the
// normalizaiton-at-definition is implemented.
// Note that s2s weights are not included in this normalization
// i.e., summ(v2s weights) = 1, summ(v2s + s2s weights) >= 1
// with the equality holding if there is no s2s interaction.
#define CONDUCTION_V2S_WEIGHTS_NEED_NORMALIZATION
// For now, we normalize v2s sampled values in surfel dynamics.
// This need not be the case for conduction surfels since all
// varieties of conduction surfels complete their v2s in a single
// TS (including odd surfels). Moreoever, once v2s weights
// are normalized at definition, there will be no normalization
// necessary at all.
#define CONDUCTION_NORMALIZE_SAMPLES_IN_SDYN

// SIMSIZES needs to be modified to support both realms. For now, use FLOW sizes for both.
#define CONDUCTION_SIMSIZES_USE_FLOW_SIZES
#define CONDUCTION_KAPPA_UNITS_CHANGE_MAJOR_VERSION   9
#define CONDUCTION_KAPPA_UNITS_CHANGE_MINOR_VERSION   5

#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
//#define CONDUCTION_DEBUG_NANS
#endif

#if DEBUG
// The latest Intel compiler (version 11) will inline if compiling debug
// when it sees __forceinline. This creates problems for debugging.
#define INLINE inline
#else
#define INLINE inline
#endif

#else
#define INLINE inline
#endif

#define MLRF_CLEAR_DEPOTS 0
#define MLRF_TEST_MESSAGES 0
#define MLRF_TAG_MESSAGES 0
#define MLRF_TAG_MESSAGES_VERBOSE 0

#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <pthread.h>
#include <sched.h>
#include <limits>
#include "simulator_namespace.h"

#if BUILD_D19_LATTICE
#if SOLVER_VERSION==6
#define BUILD_6X_SOLVER 1
#elif SOLVER_VERSION==5
#define BUILD_5X_SOLVER 1
#else
#error "The version of the SOLVER component is neither 5 nor 6"
#endif
#endif

#ifdef __LINUX__

#if DEBUG
#define _INLINE_ inline
//NVCC-GCC doesn't seem to inline even small functions in debug mode causing linker issues
#define _ALWAYS_INLINE_ inline
#else
#define _INLINE_ inline
#define _ALWAYS_INLINE_ __attribute__((always_inline)) inline
#endif

#else
#define _INLINE_ inline
#endif

#if defined(_EXA_MPI)
#include <mpi.h> 
#endif

#if defined(__INTEL_COMPILER)
#define CONST const
#elif defined(_EXA_CLANG) || defined(__GNUC__)
#define CONST constexpr
#endif

#include XNEW_H
#include SRI_H
#include SCALAR_H
#include PLATFORM_H

#include LGI_H
#include CDI_H
#include FANTABLE_H
#include BG_H
#include EXATIME_H
#include PAGED_BITMAP_H
#include JOBCTL_SERVER_H

#ifdef	__cplusplus
extern "C" {
#endif

#include MSGERR_H
#include DEBUG_H
#include MALLOC_H
#include LOOP_H
#include SORT_H
#include XARRAY_H
#include EARRAY_H
#include VHASH_H
#include PHYSTYPES_H
#include UNITS_H
#include EXPRLANG_H

#include G1_H
#include G2_H
#include G3_H


#ifdef	__cplusplus
}
#endif

#define DISABLE_COMM_STATE_COMPRESSION 0

#define DISABLE_COMM_SOLVER_DATA_COMPRESSION 0

#define MAX_N_SEED_UDS_VARS  (MAX_N_USER_DEFINED_SCALARS * DGF_N_SEED_UDS_VARS)

#if DEBUG
#define dassert(expr) assert(expr)
#else
#define dassert(expr)
#endif

#if !BUILD_GPU
#define ENABLE_CONSISTENCY_CHECKS 1
#else
// consistency checks changes GPU results, so I'm turning them off by default
#define ENABLE_CONSISTENCY_CHECKS 0
#endif


#if ENABLE_CONSISTENCY_CHECKS
#define cassert(expr) assert(expr)
#else
#define cassert(expr)
#endif

#define DEBUG_INLINE_FAN 0

#include "logging.h"
#include SP_H
#include DISC_INTERCOMM_H

// AVX makefile option turns off SSE vectorization
#if (defined(__AMD64_LINUX__) || defined(__AMD64_LINUX2_64__) || defined(__X86_LINUX__))

#if BUILD_AVX && !GPU_COMPILER
#define EXA_USE_AVX  1
#define EXA_USE_SSE  0
#elif !GPU_COMPILER
#define EXA_USE_AVX  0
#define EXA_USE_SSE  1
#else
#define EXA_USE_AVX  0
#define EXA_USE_SSE  0
#endif

#else
#define EXA_USE_AVX  0
#define EXA_USE_SSE  0
#endif


// SSE uses 16-byte alignment, whereas AVX uses 32-byte alignment
// Also distinguish gcc/clang
#if EXA_USE_AVX

#if defined(__INTEL_COMPILER)
#define ALIGN_VECTOR  __declspec(align(32))
#elif defined(_EXA_CLANG)
#define ALIGN_VECTOR  __attribute__ (( aligned(32) ))
#else
#define ALIGN_VECTOR alignas(32)
#endif

#elif EXA_USE_SSE

#if defined(__INTEL_COMPILER)
#define ALIGN_VECTOR  __declspec(align(16))
#elif defined(_EXA_CLANG)
#define ALIGN_VECTOR  __attribute__ (( aligned(16) ))
#endif

#elif GPU_COMPILER

//For the GPU, some structures have aligned float arrays to
//match the size of their CPU counterparts. This is especially
//important to make sure we are accessing members at the same offset.
//An example of a structure where this is important is DCACHE
#if defined(__INTEL_COMPILER)
#define ALIGN_VECTOR  __declspec(align(32))
#elif defined(_EXA_CLANG)
#define ALIGN_VECTOR  __attribute__ (( aligned(32) ))
#endif

#else

#define ALIGN_VECTOR

#endif

//Packed and align attributes
#if defined(_EXA_CLANG)
#define __ALIGN__(val) __attribute__ ((aligned(val)))
#define __PACKED__ __attribute__ ((packed))
#elif defined(__INTEL_COMPILER)
#define __ALIGN__(val)  __declspec(align(val))
#define __PACKED__  __declspec(packed)
#else
#define __ALIGN__(val)
#define __PACKED__
#endif

extern sdFLOAT pressure_bc_omega_u_n;
extern sdFLOAT mass_flux_bc_omega_rho;
extern sdFLOAT fixed_vel_bc_omega_rho;

#if (defined(__alpha))
  /* This is necessary due to prevent page allocated via mmap
   * from blocking the growth of the heap
   */

  extern unsigned long __sbrk_override;
#endif

/*--------------------------------------------------------------------------*
 * Definitions to migrate to SCALAR
 *--------------------------------------------------------------------------*/

/* Boolean optimized for space */
typedef csINT8 cBOOLEAN;
typedef sINT8  BOOLEAN8;

/* @@@ The SGI compiler seems to have a bug where it doesn't handle a cast of
 * a 64-bit integer to a 32-bit integer properly (i.e., by zeroing the upper 
 * 32 bits.
 */
#if defined(__sgi)
#define low_32_of_64(x) ((x << 32) >> 32)
#else
#define low_32_of_64(x) ((uINT32)x)
#endif

/*--------------------------------------------------------------------------*
 * Master compile-time flags
 *--------------------------------------------------------------------------*/

#define DELP_ZERO_OFF 		0
#define RI_DIAG_WINS            0


#define EXPLICIT_F_D_MODEL 	1

/* For the time being, scale single voxel measurements by 4 in 2D and 8 in 3D
 * in order to ensure that existing scaling procedures for mme.nc files that
 * assume ublk measurements will still work. Only 4 is required in 2D 
 * because there are 2 voxels that contribute to a single meas cell.
 */
#define SCALE_SINGLE_VOXEL_MEAS_BY_DIMS		1


/*--------------------------------------------------------------------------*
 * Validation functions
 *--------------------------------------------------------------------------*/
VOID report_ckpt_restore_inconsistency(cSTRING description);


#define DO_ALL_AXES(axis, forward_face, backward_face, n_dims)  \
  asINT32 axis;                                                 \
  asINT32 forward_face;                                         \
  asINT32 backward_face;                                        \
  asINT32 ___(n_dims) = sim.num_dims;                                 \
  for (axis = 0;                                                      \
       forward_face = stp_axis_to_pos_face(axis),                     \
         backward_face = stp_axis_to_neg_face(axis),                  \
         axis < ___(n_dims);                                          \
       axis++)

/*--------------------------------------------------------------------------*
 * Struct field offsets and displacements
 *--------------------------------------------------------------------------*/
#define struct_field_disp(type, member) 	        (pointer_to_uINT32(&(((type) 0)->member)))
#define struct_field_disp_from_ptr(type, ptr_method)    (pointer_to_uINT32((((type) 0)->ptr_method)))
#define struct_field_size(type, member)		        (sizeof (((type) 0)->member))
#define struct_field_disp_from_array_elt(type, array_member, elt) 	        (pointer_to_uINT32(&(((type) 0)->array_member[elt])))
#define struct_field_elt_size(type, member)		(sizeof (((type) 0)->member[0]))

#define struct_section_size(type, first_member, last_member)  (struct_field_disp(type, last_member)     \
                                                               + struct_field_size(type, last_member)   \
                                                               - struct_field_disp(type, first_member))


#define force_bool_0_or_1(B)			((B) != 0)

/*--------------------------------------------------------------------------*
 * Utility macros
 *--------------------------------------------------------------------------*/

/* This copies all of the elements of the src array to the dest array */
#define COPY_ARRAY(dest, src, size) \
   DOTIMES(copy_array_i, size, {dest[copy_array_i] = src[copy_array_i];})
#ifndef MAX
#undef MAX	/* Alpha compiler seems to get here even though MAX is defined */
#define MAX(_a,_b) ((_a) > (_b) ? (_a) : (_b))
#endif
#ifndef MIN
#undef MIN	/* Alpha compiler seems to get here even though MIN is defined */
#define MIN(_a,_b) ((_a) < (_b) ? (_a) : (_b))
#endif
#define ABS(_a) ((_a) < 0 ? -(_a) : (_a))


/*--------------------------------------------------------------------------*
 * Other utility functions
 *--------------------------------------------------------------------------*/

/* Returns the base 2 logarithm of x.  X must be greater than 0 */
auINT32 simeng_log2(asINT32 x);

/* Returns the total count of bits in x (assuming X has 32 bits) */
auINT32 bitcount32(auINT32 x);

/* POS_ROUND ronuds a positive float to the nearest integer */
#define pos_round(_x) ((_x) + 0.5)

#define nwords64(_data) ((sizeof(_data) + 7)/8)

#define odd_p(x) ((x & 0x1) == 1)
#define even_p(x) ((x & 0x1) == 0)

/* 3D Vectors */
#define vdot(a, b)	((a)[0] * (b)[0] + (a)[1] * (b)[1] + (a)[2] * (b)[2])
#define vscale(r, s, v)	{ (r)[0] = (s) * (v)[0]; (r)[1] = (s) * (v)[1]; (r)[2] = (s) * (v)[2]; }
#define vsub(r, a, b)	{ (r)[0] = (a)[0] - (b)[0]; (r)[1] = (a)[1] - (b)[1]; (r)[2] = (a)[2] - (b)[2]; }
#define vadd(r, a, b)	{ (r)[0] = (a)[0] + (b)[0]; (r)[1] = (a)[1] + (b)[1]; (r)[2] = (a)[2] + (b)[2]; }
#define vinc(r, a)	{ (r)[0] += (a)[0]; (r)[1] += (a)[1]; (r)[2] += (a)[2]; }
#define vdec(r, a)	{ (r)[0] -= (a)[0]; (r)[1] -= (a)[1]; (r)[2] -= (a)[2]; }
#define vmul(r, s)      { (r)[0] *= (s); (r)[1] *= (s); (r)[2] *= (s); }
#define vmac(r, s, a)	{ (r)[0] += (s) * (a)[0]; (r)[1] += (s) * (a)[1]; (r)[2] += (s) * (a)[2]; }  //multiply, accumualte
#define vlensqrd(v)  vdot(v,v) //length squared, more efficient than vlength since avoids computing the square root
#define vlength(v)   std::sqrt(vdot(v,v)) //do not enclose in braces to avoid interpreting it as initializer list
#define vcopy(r, a)	{ (r)[0] = (a)[0]; (r)[1] = (a)[1]; (r)[2] = (a)[2]; }
#define vneg(r, a)	{ (r)[0] = -(a)[0]; (r)[1] = -(a)[1]; (r)[2] = -(a)[2]; }
#define vset(r, a)  { (r)[0] = a; (r)[1] = a; (r)[2] = a; }
#define vzero(r)	{ (r)[0] = 0; (r)[1] = 0; (r)[2] = 0; }
#define vcross(rr, aa, bb)                                \
{                                                         \
  (rr)[0] = (aa)[1] * (bb)[2] - (aa)[2] * (bb)[1];        \
  (rr)[1] = (aa)[2] * (bb)[0] - (aa)[0] * (bb)[2];        \
  (rr)[2] = (aa)[0] * (bb)[1] - (aa)[1] * (bb)[0];        \
}

#define vunitize(a)               \
{                                 \
  dFLOAT _mag = sqrt(vdot(a,a));  \
  (a)[0] /= _mag;                 \
  (a)[1] /= _mag;                 \
  (a)[2] /= _mag;                 \
}

void matrix_transpose(double result[3][3], double src[3][3]);

template <typename FLOAT_1, typename FLOAT_2, typename FLOAT_3>
void matrix_vector_product(FLOAT_1 result[3], const FLOAT_2 mat[3][3], const FLOAT_3 vec[3]) {
  result[0] = mat[0][0] * vec[0] + mat[0][1] * vec[1] + mat[0][2] * vec[2];
  result[1] = mat[1][0] * vec[0] + mat[1][1] * vec[1] + mat[1][2] * vec[2];
  result[2] = mat[2][0] * vec[0] + mat[2][1] * vec[1] + mat[2][2] * vec[2];
}

/* for LB_UDS solver seeding */
#define find_uds_indices(var_index, index_in_dgf_seed_enum, nth_uds)     \
{                                                                        \
   (index_in_dgf_seed_enum) = (var_index) % DGF_N_SEED_UDS_VARS;         \
   (nth_uds) = ((var_index) - (index_in_dgf_seed_enum)) / DGF_N_SEED_UDS_VARS; \
}

/* 2D Vectors */
#define vdot2(a, b)	((a)[0] * (b)[0] + (a)[1] * (b)[1])
#define vscale2(r, s, v)	{ (r)[0] = (s) * (v)[0]; (r)[1] = (s) * (v)[1]; }
#define vsub2(r, a, b)	{ (r)[0] = (a)[0] - (b)[0]; (r)[1] = (a)[1] - (b)[1]; }
#define vadd2(r, a, b)	{ (r)[0] = (a)[0] + (b)[0]; (r)[1] = (a)[1] + (b)[1]; }
#define vinc2(r, a)	{ (r)[0] += (a)[0]; (r)[1] += (a)[1]; }
#define vdec2(r, a)	{ (r)[0] -= (a)[0]; (r)[1] -= (a)[1]; }
#define vmul2(r, s)      { (r)[0] *= (s); (r)[1] *= (s); }
#define vmac2(r, s, a)	{ (r)[0] += (s) * (a)[0]; (r)[1] += (s) * (a)[1]; }  //multiply, accumualte
#define vlensqrd2(v)  vdot2(v,v) //length squared, more efficient than vlength since avoids computing the square root
#define vlength2(v)   std::sqrt(vdot2(v,v)) //do not enclose in braces to avoid interpreting it as initializer list
#define vcopy2(r, a)	{ (r)[0] = (a)[0]; (r)[1] = (a)[1]; }
#define vneg2(r, a)	{ (r)[0] = -(a)[0]; (r)[1] = -(a)[1]; }
#define vzero2(r)	{ (r)[0] = 0; (r)[1] = 0; }
#define vrotate2(r, a, v)				           \
{                                          \
  (r)[0] = (v)[0]*cos(a) - (v)[1]*sin(a);  \
  (r)[1] = (v)[0]*sin(a) + (v)[1]*cos(a);  \
}

#define vrotatecsys2(r, a, v)				           \
{                                          \
  (r)[0] = (v)[0]*cos(a) + (v)[1]*sin(a);  \
  (r)[1] = (v)[1]*cos(a) - (v)[0]*sin(a);  \
}

// dot product of 2x2 symmetric tensor (stored as {xx,xy,yy} and 2d vector
#define stdotv2(r, t, v)                   \
{                                          \
  (r)[0] = (t)[0]*(v)[0] + (t)[1]*(v)[1];  \
  (r)[1] = (t)[1]*(v)[0] + (t)[2]*(v)[1];  \
}


#define vNewFromOp2_dFLOAT(vname, vOp, ...) \
  dFLOAT vname[2];                          \
  vOp(vname, __VA_ARGS__);

#define vNewFromOp2_sdFLOAT(vname, vOp, ...) \
  sdFLOAT vname[2];                           \
  vOp(vname, __VA_ARGS__);

#define symmTensorDoubleDotVec2(_tens, _v) ((_tens)[0]*(_v)[0]*(_v)[0] + 2.0*(_tens)[1]*(_v)[0]*(_v)[1] + (_tens)[2]*(_v)[1]*(_v)[1])

#define vcross2(aa, bb) ((aa)[0] * (bb)[1] - (aa)[1] * (bb)[0]) // returns dFLOAT unlike vcross for 3d vectors which assigns to vector

#define vunitize2(a)				        \
{						                        \
  dFLOAT _mag = sqrt(vdot2(a,a));		\
  (a)[0] /= _mag;				            \
  (a)[1] /= _mag;				            \
}

void matrix_transpose2(double result[2][2], double src[2][2]);

template <typename FLOAT_1, typename FLOAT_2, typename FLOAT_3>
void matrix_vector_product2(FLOAT_1 result[2], const FLOAT_2 mat[2][2], const FLOAT_3 vec[2]) {
  result[0] = mat[0][0] * vec[0] + mat[0][1] * vec[1];
  result[1] = mat[1][0] * vec[0] + mat[1][1] * vec[1];
}

/* for T_solver switch  */

#define LARGEST_BASETIME  0x7fffffffffffffff
#define LARGEST_TIMESTEP  0x7fffffff

BASETIME round_up_time(dFLOAT t, dFLOAT ct, dFLOAT t0=0);
BASETIME round_down_time(dFLOAT t, dFLOAT ct, dFLOAT t0=0);
asINT32 gcd(asINT32 a, asINT32 b);
asINT32 gcd(asINT32 a, asINT32 b, asINT32 c) ;
asINT32 lcm(asINT32 a, asINT32 b);
asINT32 lcm(asINT32 a, asINT32 b, asINT32 c) ;
asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d) ;
asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e) ;

/*--------------------------------------------------------------------------*
 * Simulation run info 
 *--------------------------------------------------------------------------*/
#if defined(_EXA_MPI)
extern sCP_EARLY_SIM_RUN_INFO early_sim_run_info;
extern sCP_LATE_SIM_RUN_INFO sim_run_info;

inline BOOLEAN sim_ri_diagnostics()
{
  return sim_run_info.run_options & SIM_RI_DIAGNOSTICS; 
}
#endif
typedef struct sCOUPLING_PHASE_TIME_DESC_SP {
  // coupling_time is the time for PF to read in PT data
  TIMESTEP m_coupling_time;
  TIMESTEP m_period;
  TIMESTEP m_delay;
}*COUPLING_PHASE_TIME_DESC_SP;

typedef struct sMEAS_PHASE_TIME_DESC_SP {
  // output_time is the beginning of each period
  TIMESTEP m_output_time;
  TIMESTEP m_period;
  TIMESTEP m_interval;
  asINT32  m_repeat;
}*MEAS_PHASE_TIME_DESC_SP;

typedef struct sSTATE_PAIR {
  uINT8 from;
  uINT8 to; 
} *STATE_PAIR;

typedef struct sSTATE_PAIR_TABLE {
  sSTATE_PAIR pairs_z[2];
  sSTATE_PAIR pairs_y[2];
  sSTATE_PAIR pairs_yz[4];
} *STATE_PAIR_TABLE;

typedef struct sPARITY_STATE_PAIR {
  uINT8 index1;
  uINT8 index2;
} *PARITY_STATE_PAIR;
    
const vFLOAT MAX_BC_PRESSURE = 0.3;
const vFLOAT MIN_BC_PRESSURE = 0.02;

// for high subsonic case, the min bc pressure is scaled by the minimum lattice gas constant 0.6
const vFLOAT MIN_BC_PRESSURE_HIGH_SUBSONIC = 0.012;

#if BUILD_D39_LATTICE
// for high subsonic case, the min bc pressure is scaled by the minimum lattice gas constant 0.25
const vFLOAT MIN_BC_PRESSURE_TRANSONIC = 0.005;
#endif 

const vFLOAT MIN_BC_DENSITY = 0.025;
const vFLOAT MAX_BC_DENSITY = 0.4;

//const vFLOAT MAX_SURFEL_BC_VEL_D34 = 0.35;
#if BUILD_D39_LATTICE
const vFLOAT MAX_SURFEL_BC_VEL = 2.0 * 0.5797; // 2.0 Mach with R=0.4 and T=1.5T0 and gamma=1.4
#else
const vFLOAT MAX_SURFEL_BC_VEL = 0.35 * 0.727393; // 0.35 * D19_sound_speed / D34_sound_speed
const vFLOAT MAX_SURFEL_BC_VEL_HS = 0.95 * 0.70711; // 0.95 Mach with R=1/1.4 and T=1.5T0 and gamma=1.4
#endif 

//const vFLOAT DEFAULT_MAX_VEL_D34  = 0.6;
const vFLOAT DEFAULT_MAX_VEL  = 0.6 * 0.727393;  // 0.6 * D19_sound_speed / D34_sound_speed
const vFLOAT DEFAULT_MAX_VEL_AERO  = 0.57735027;   // 1.0/sqrt(3)

//const vFLOAT MAX_MOVING_SURFACE_VEL_D34 = MAX_SURFEL_BC_VEL_D34;
const vFLOAT MAX_MOVING_SURFACE_VEL = MAX_SURFEL_BC_VEL;

//const vFLOAT MAX_BC_MASS_FLUX_D34 = MAX_SURFEL_BC_VEL_D34 * MAX_BC_DENSITY;
const vFLOAT MAX_BC_MASS_FLUX = MAX_SURFEL_BC_VEL * MAX_BC_DENSITY;

const asINT32 MAX_SPLIT_UBLKS = 256;

/*--------------------------------------------------------------------------*
 * Solver identity
 *--------------------------------------------------------------------------*/
typedef STP_ACTIVE_SOLVER_MASK ACTIVE_SOLVER_MASK;

__HOST__DEVICE__
inline BOOLEAN is_solver_active(SOLVER snumber, ACTIVE_SOLVER_MASK mask) { return (mask >> snumber) & 1; }

// There are special variations of active_solver_mask to optimize the code for those solvers.
// The default choice is 0, in which case no optimizations are performed.
#define DEFAULT_ACTIVE_SOLVER_MASK 0

// This macro assumes the existence of the variable "active_solver_mask"
#define CALL_SOLVER_MASK_FCN_SET(fcn_root_name, arglist)                                  \
  switch (active_solver_mask) {                                                           \
  case LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE | UDS_PDE_ACTIVE:                      \
    fcn_root_name< LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE | UDS_PDE_ACTIVE >arglist; \
    break;                                                                                \
  case LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE:                                          \
    fcn_root_name< LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE >arglist;                     \
    break;                                                                                \
  case LB_ACTIVE | KE_PDE_ACTIVE:                                                         \
    fcn_root_name< LB_ACTIVE | KE_PDE_ACTIVE >arglist;                                    \
    break;                                                                                \
  case LB_ACTIVE | T_PDE_ACTIVE:                                                          \
    fcn_root_name< LB_ACTIVE | T_PDE_ACTIVE >arglist;                                     \
    break;                                                                                \
  case LB_ACTIVE:                                                                         \
    fcn_root_name< LB_ACTIVE >arglist;                                                    \
    break;                                                                                \
  default:                                                                                \
    fcn_root_name<DEFAULT_ACTIVE_SOLVER_MASK>arglist;                                     \
    break;                                                                                \
  }
  

/*--------------------------------------------------------------------------*
 * Realms - conduction & flow
 * -------------------------------------------------------------------------*/

// Realms are enumerated as STP_REALM in PHYSTYPES.
// REALM is an int type that can be used as an lvalue.

typedef sINT8 REALM;

#define DO_REALMS(realm_var) \
  for(REALM realm_var = STP_FLOW_REALM; realm_var < STP_N_REALMS; realm_var++)

/*--------------------------------------------------------------------------*
 * Prior time step index mask
 * -------------------------------------------------------------------------*/

typedef STP_PRIOR_SOLVER_INDEX_MASK SOLVER_INDEX_MASK;
typedef uINT64 SOLVER_PHASE_MASKS;

__HOST__DEVICE__ INLINE uINT32 coarse_time_index_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return (prior_time_index_mask >> N_SOLVERS );
}
__HOST__DEVICE__ INLINE uINT32 lb_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> LB_SOLVER) & 1);
}
__HOST__DEVICE__ INLINE uINT32 coarse_lb_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> (N_SOLVERS + LB_SOLVER)) & 1);
}
__HOST__DEVICE__ INLINE uINT32 turb_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> TURB_SOLVER) & 1);
}
__HOST__DEVICE__ INLINE uINT32 coarse_turb_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >>(N_SOLVERS +  TURB_SOLVER)) & 1);
}
__HOST__DEVICE__ INLINE uINT32 t_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> T_SOLVER) & 1);
}
__HOST__DEVICE__ INLINE uINT32 uds_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> UDS_SOLVER) & 1);
}
__HOST__DEVICE__ INLINE uINT32 coarse_t_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> (N_SOLVERS + T_SOLVER)) & 1);
}
__HOST__DEVICE__ INLINE uINT32 coarse_uds_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> (N_SOLVERS + UDS_SOLVER)) & 1);
}
__HOST__DEVICE__ INLINE uINT32 conduction_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> CONDUCTION_SOLVER) & 1);
}
__HOST__DEVICE__ INLINE uINT32 coarse_conduction_index_from_mask(SOLVER_INDEX_MASK prior_time_index_mask) {
  return ((prior_time_index_mask >> (N_SOLVERS + CONDUCTION_SOLVER)) & 1);
}
__HOST__DEVICE__ INLINE BOOLEAN is_index_odd(uINT32 index) { return index == 1; }


/*--------------------------------------------------------------------------*
 * T_Solver identity
 *--------------------------------------------------------------------------*/
enum T_SOLVER_TYPE
{
  INVALID,
  LB_TEMPERATURE,
  PDE_TEMPERATURE,
  LB_ENTROPY,
  PDE_ENTROPY,
  LB_ENERGY,
  N_T_SOLVER_TYPES
};

/*--------------------------------------------------------------------------*
 * UDS_Solver identity
 *--------------------------------------------------------------------------*/
enum UDS_SOLVER_TYPE
{
  INVALID_UDS,
  LB_UDS,
  PDE_UDS
};

/*--------------------------------------------------------------------------*
 * PDE solver neighbors
 *--------------------------------------------------------------------------*/
enum neighbor_index
{
  FOR = 0,
  BACK = 1,
};

/*--------------------------------------------------------------------------*
 * 5G solver types
 *--------------------------------------------------------------------------*/
enum SOLVER_INDEX 
{
    MRT_SOLVER_INDEX,
    STOKES_SOLVER_INDEX,
    NWM_SOLVER_INDEX,
    MRT_TRACELESS_SOLVER_INDEX,

    MAXIMUM_NUM_SOLVERS
};

enum WEIGHTED_VISC
{
    DENSITY_WEIGHT_TAU,
    DENSITY_WEIGHT_OMEGA
};

enum MULTIPHASE_TYPE {
    IDEAL_IDEAL,
    PR_PR,
    IDEAL,
    PR,
    IDEAL_PR
};

enum UDS_CARRIER {
  COMP_0,
  COMP_1,
  MIXTURE,

  MAXIMUM_NUM_UDS_CARRIERS
};

#if BUILD_D39_LATTICE || BUILD_5G_LATTICE || BUILD_6X_SOLVER
#define ENABLE_FRACTIONAL_ADVECTION_SCHEME 0
#define COMP_BGK 0
#elif BUILD_5X_SOLVER
#define COMP_BGK 1
#define ENABLE_FRACTIONAL_ADVECTION_SCHEME 1
#endif


// For timing and load factors
#define DEBUG_SP_COUNTERS 0
#define DEBUG_SP_TIMER_SWITCH 0

#define ONLY_ONE_COPY 0

typedef struct sSURFEL_SEND_DATA_INFO {
  sdFLOAT *send_buffer;
  T_SOLVER_TYPE t_solver_type;
}*SURFEL_SEND_DATA_INFO;

typedef struct sSURFEL_RECV_DATA_INFO {
  sdFLOAT *recv_buffer;
  T_SOLVER_TYPE t_solver_type;
}*SURFEL_RECV_DATA_INFO;

typedef sSURFEL_SEND_DATA_INFO sUBLK_SEND_DATA_INFO, *UBLK_SEND_DATA_INFO;
typedef sSURFEL_RECV_DATA_INFO sUBLK_RECV_DATA_INFO, *UBLK_RECV_DATA_INFO;

#define SURFEL_EVENT_QUEUE_STRAND INTERIOR_NS_A_STRAND
#define UBLK_EVENT_QUEUE_STRAND  TIMESTEP_UPDATE_STRAND

#define D39_ADVECTION_SUPPORT   0 //XDU: code to be implemented for D39

__HOST__DEVICE__ void breakpoint();

#include "compile_time_loop.h"

//------------------------------------------------------------------------------
// get_byte_aligned(offset,alignment)
// Rounds offset up to nearest compiler alignment boundary
//------------------------------------------------------------------------------
__HOST__DEVICE__ inline size_t get_byte_aligned(size_t offset, size_t alignment)
{
  return (offset - 1u + alignment) & -alignment;
};

template <int N>
struct Inverse_power_of_two_array
{
  dFLOAT data[N];

#if !GPU_COMPILER
  //Dynamic initialization of a global variable is not supported in GPU
  Inverse_power_of_two_array() {
    ccDOTIMES(i, N) {
      data[i] = 1.0/(1L<<i);
    }
  }
#endif
  
  __HOST__DEVICE__ dFLOAT operator [] (int i) const
  {
    return data[i];
  }
};
// Since max_scales is 16, we size the following array to be 31
//extern dFLOAT g_dfloat_inverse_power_of_two[2 * STP_MAX_SCALES - 1];
extern Inverse_power_of_two_array <2 * STP_MAX_SCALES -1> g_dfloat_inverse_power_of_two;

#if BUILD_GPU
namespace GPU {
  extern __CONSTANT__ Inverse_power_of_two_array <2 * STP_MAX_SCALES -1> g_dfloat_inverse_power_of_two;
}
#endif

__HOST__DEVICE__ INLINE dFLOAT dfloat_inverse_power_of_two(int i) {
  return HD_NAMESPACE::g_dfloat_inverse_power_of_two[i];
}

#if GPU_COMPILER
#define HOST_MSG_ERROR_OR_DEVICE_ASSERT(...) assert(false);
#define HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT(...) assert(false);
#else
#define HOST_MSG_ERROR_OR_DEVICE_ASSERT(...) msg_internal_error(__VA_ARGS__)
#define HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT(...) msg_internal_error(__VA_ARGS__)
#endif

//------------------------------------------------------------------------------
// template get_alignment_of<typename>
// Tricks the compiler into telling us the required alignment of a given object
//------------------------------------------------------------------------------
template <typename T>
__HOST__DEVICE__ constexpr size_t get_alignment_of()
{
  return alignof(T);
};

#endif /* __SIM_ENG_COMMON_H */


