/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include <algorithm>
#include "common_sp.h"
#include "phys_type_map.h"
#include "sim.h"
#include "eqns.h"
#include "shob_dyn.h"
#include "surfel_dyn_sp.h"
#include "isurfel_dyn_sp.h"
#include "bsurfel_dyn_sp.h"
#include "voxel_dyn_sp.h"
#include "seed_sp.h"
#include "bsurfel.h"
#include "turb_synth_info.h"
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_sim.h"
#include "particle_emitter_configurations.h"
//#endif

const auINT32 SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK = 1;
const auINT32 TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK = 2;
const asINT32 N_RESERVED_DEPENDENCE_MASK_BITS = 2; // space and time bits (must be LSB)

EXPRLANG_PROGRAM g_case_program = NULL;
UNITS_DB units_db = NULL;

static BOOLEAN is_normal_input_to_programs;
static BOOLEAN is_time_input_to_programs;

static BOOLEAN are_expr_relative_to_user_csys;

std::map<asINT32, asINT32> g_region_to_material_map;

// We don't expect errors because ExaCASE should not let errors past it.
// Thus we don't worry too much about elegant error messages.
extern "C" VOID sim_expr_errfcn(asINT32 substr_start, asINT32 substr_end,
                                asINT32 lineno, cSTRING msg)
{
  msg_print("Error in CDI equations: line %d (%d - %d): %s",
            lineno, substr_start, substr_end, msg);
}


static asINT32 units_db_index = 0;
static asINT32 units_db_length;

// Same semantics as fgets
extern "C" char *sim_units_db_read_fcn(char *s, int n, void *_stream)
{
  LGI_STREAM stream = (LGI_STREAM)_stream;
  char *t = s;

  for (asINT32 i=1; i<n; i++) { // at most n-1 chars
    lgi_read(stream, t, 1);
    units_db_index++;

    if (*t == '\n')
      break;
    t++;
    if (units_db_index == units_db_length)
      break;
  }
  *t='\0';

  return s;
}

// Define the global variables with default values
dFLOAT g_lattice_power_to_power_density_slope = 1.0;
dFLOAT g_lattice_power_to_power_density_offset = 0.0;
dFLOAT g_lattice_power_to_heat_flux_slope = 1.0;
dFLOAT g_lattice_power_to_heat_flux_offset = 0.0;

// Initialize the conversion factors in a function that's called early in the initialization process
static VOID initialize_power_density_unit_conversion_factors()
{
  // Compute conversion from total imposed heat/unit volume to imposed heat in powerdensity units
  // Conversion = Old Units -> New Units (PowerDensity -> PowerDensity)
  // Old Units = LatticePower/LatticeVolume 
  // New Units = LatticePowerDensity (LatticeSpecificEnthalpy*LatticeDensity/LatticeTimeInc)
  find_units_conversion_coefficients("LatticePower/LatticeVolume", "LatticePowerDensity", 
                                    &g_lattice_power_to_power_density_slope, 
                                    &g_lattice_power_to_power_density_offset);

  find_units_conversion_coefficients("LatticePower/LatticeArea", "LatticeWallHeatFlux", 
                                    &g_lattice_power_to_heat_flux_slope, 
                                    &g_lattice_power_to_heat_flux_offset);

}


VOID read_units_database(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_UNITS_DB_TAG)
    return;

  LGI_UNITS_DB_HEADER header;
  lgi_read_next_head(stream, header);

  if (header.tag.id != LGI_UNITS_DB_TAG) {
    msg_error("Missing %s record (ID %d) in LGI file.",
              lgi_tag_namestring(LGI_UNITS_DB_TAG), LGI_UNITS_DB_TAG);
  }

  units_db = units_create_units_db();
  units_db_index = 0;
  units_db_length = header.units_db_length;
  UNITS_STATUS status = units_initialize_db_from_stream(units_db, stream, sim_units_db_read_fcn, NULL);

  if (status != UNITS_STATUS_OK)
    msg_internal_error("CDI file contains an unreadable units database.");

  // MKS velocity scaling is needed for --seed_mks capability.
  dFLOAT offset;
  find_units_conversion_coefficients("LatticeVelocity", "m/sec", &sim.mks_vel_scaling, &offset);

  if(sim.is_hydrogen_eos) {
    //this section is added for hydrogen eos 
    find_units_conversion_coefficients("LatticeDensity", "kg/m^3", &sim.mks_density_scaling, &offset);
    sim.hydrogen_beta_fac =  0.007691 * sim.mks_density_scaling/g_density_scale_factor;
  }
  
  particle_sim.compute_lattice_time_correction_factor(units_db);

  // This was previously used to convert conductivity to correct enthalpy basis
  // No longer needed after updating units.dat

  // // Scaling factors used in physics for conductivity and HTC
  // dFLOAT scale_length;
  // find_units_conversion_coefficients("LatticeLength", "meter", &scale_length, &offset);
  
  // dFLOAT scale_conductivity;
  // find_thermal_units_conversion_coefficients("LatticeThermalConductivity", "W/(m*degK)", &scale_conductivity, &offset);
  
  // dFLOAT scale_htc;
  // find_thermal_units_conversion_coefficients("LatticeHeatTransferCoeff", "W/(m^2*degK)", &scale_htc, &offset);
  
  // sim.A_scale = scale_conductivity / scale_length / scale_htc;
  // sim.conductivity_scale_factor = sim.A_scale * (sim.char_density / 0.22 * sim.C_p * g_lb_temp_constant);

  initialize_power_density_unit_conversion_factors();
}

static inline EXPRLANG_TYPE create_uval_type(UNITS_DB units_db, cSTRING unit_name)
{
  UNITS_UNIT temp_unit;
  units_parse_unit(units_db, unit_name, &temp_unit);
  return exprlang_make_uval_type(temp_unit);
}

static VOID define_eqns_input_variables(EXPRLANG_PROGRAM program)
{
  UNITS_UNIT lattice_length_unit;
  UNITS_UNIT lattice_time_unit;
  UNITS_UNIT lattice_velocity_unit;
  UNITS_UNIT dimensionless_unit;
  units_parse_unit(units_db, "LatticeLength", &lattice_length_unit);
  units_parse_unit(units_db, "LatticeTime", &lattice_time_unit);
  units_parse_unit(units_db, "LatticeVelocity", &lattice_velocity_unit);
  units_parse_unit(units_db, "dimensionless", &dimensionless_unit);

  UNITS_UNIT mks_time_unit; // seconds
  units_parse_unit(units_db, "sec", &mks_time_unit);
  {
    EXPRLANG_TYPE lattice_length_type = exprlang_make_uval_type(lattice_length_unit);
    EXPRLANG_TYPE lattice_time_type = exprlang_make_uval_type(lattice_time_unit);
    EXPRLANG_TYPE dimensionless_type = exprlang_make_uval_type(dimensionless_unit);
    EXPRLANG_TYPE mks_time_type = exprlang_make_uval_type(mks_time_unit);

    /** Define the input variables for x,y,z spatial coordinates.
    *** The values are set later, during evaluation.
    ***
    *** The dependence flags for x,y,z are set non-zero.  This will optimize
    *** repeated evaluations of the same equations when only x, y, and z change.
    */

    exprlang_define_input_variable(program,
                                   "x",
                                   lattice_length_type,
                                   FALSE,
                                   SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
    exprlang_define_input_variable(program,
                                   "y",
                                   lattice_length_type,
                                   FALSE,
                                   SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
    exprlang_define_input_variable(program,
                                   "z",
                                   lattice_length_type,
                                   FALSE,
                                   SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);

    // Define nx, ny, and nz for CDI version 2.1 and beyond
    // if (is_normal_input_to_programs)
    {
      /** Define the input variables for nx,ny,nz surface normal components.
      *** The values are set later, during evaluation.
      ***
      *** The dependence flags for nx,ny,nz are set to 1.  This will optimize
      *** repeated evaluations of the same equations when only nx, ny, and nz change.
      */

      exprlang_define_input_variable(program,
                                     "nx",
                                     dimensionless_type,
                                     FALSE,
                                     SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
      exprlang_define_input_variable(program,
                                     "ny",
                                     dimensionless_type,
                                     FALSE,
                                     SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
      exprlang_define_input_variable(program,
                                     "nz",
                                     dimensionless_type,
                                     FALSE,
                                     SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
    }

    // Define t for CDI version 2.6 and beyond
    if (is_time_input_to_programs)
    {
      /** Define the input variable for t (time). The value is set later, during evaluation.
      ***
      *** The dependence flags for t is set to 2.  This will optimize repeated evaluations
      *** of the same equations when only t changes.
      */
      exprlang_define_input_variable(program,
                                     "t",
                                     lattice_time_type,
                                     FALSE,
                                     TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);

      if (sim.is_ptherm_time_input_to_programs)  // Only supported for cdi version >= 7.22
        exprlang_define_input_variable(program,
                                     "t_ptherm",
                                     mks_time_type,
                                     FALSE,
                                     TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);

    }

    /** Define and set the values of the boolean constant "true" and "false". */
    EXPRLANG_TYPE boolean_type = exprlang_make_boolean_type();
    exprlang_define_input_variable(program, "true", boolean_type, FALSE, 0);
    exprlang_define_input_variable(program, "false", boolean_type, FALSE, 0);

    exprlang_set_boolean_input_variable_value(program, "true", TRUE);
    exprlang_set_boolean_input_variable_value(program, "false", FALSE);

    // Define and set the values in the "case" struct.
    const asINT32 N_CASE_MEMBERS = 3;

    cSTRING case_member_names[N_CASE_MEMBERS]={"num_res_levels", "char_length", "char_velocity"};
    cSTRING case_member_units[N_CASE_MEMBERS]={"dimensionless", "LatticeLength", "LatticeVelocity"};

    EXPRLANG_TYPE case_member_types[N_CASE_MEMBERS];
    ccDOTIMES(i, N_CASE_MEMBERS) {
      case_member_types[i] = create_uval_type(units_db, case_member_units[i]);
    }

    EXPRLANG_TYPE case_type = exprlang_make_struct_type(N_CASE_MEMBERS, case_member_names, case_member_types);

    exprlang_define_input_variable(program, "case", case_type, FALSE, 0);
    exprlang_set_struct_uval_input_variable_value(program, "case", 0, sim.num_scales);
    exprlang_set_struct_uval_input_variable_value(program, "case", 1, sim.char_length);
    exprlang_set_struct_uval_input_variable_value(program, "case", 2, sim.char_vel);
  }
}

BOOLEAN sTABLE::add_to_eqns()
{
  EXPRLANG_PROGRAM program = g_case_program;

  if (program && this->vtable) {
    exprlang_set_v_table_input_variable_value(program, this->name, this->vtable);
    return TRUE;
  }
  return FALSE;
}

static VOID define_eqns_table_input_variables(EXPRLANG_PROGRAM program)
{
  ccDOTIMES(i, sim.n_tables) {
    TABLE table = sim.tables + i;

    // setup an associated variable for equation eval
    exprlang_define_v_table_input_variable(program, table->name);
    exprlang_set_v_table_input_variable_value(program, table->name, table->vtable);
  }
}

#define N_COORD_MEMBERS 10

static VOID define_eqns_csys_input_variables(EXPRLANG_PROGRAM program)
{
  cSTRING coord_names[N_COORD_MEMBERS]={"x","y","z",
                                        "nx","ny","nz",
                                        "r","rho","phi","theta"};
  cSTRING coord_units[N_COORD_MEMBERS]={"LatticeLength","LatticeLength","LatticeLength",
                                        "dimensionless","dimensionless","dimensionless",
                                        "LatticeLength","LatticeLength",
                                        "radian","radian"};

  EXPRLANG_TYPE coord_types[N_COORD_MEMBERS];
  ccDOTIMES(i, N_COORD_MEMBERS) {
    coord_types[i] = create_uval_type(units_db, coord_units[i]);
  }

  EXPRLANG_TYPE csys_type = exprlang_make_struct_type(N_COORD_MEMBERS, coord_names, coord_types);

  ccDOTIMES(j, sim.n_csys) {
    exprlang_define_input_variable(program, sim.csys_table[j].name, csys_type,
                                   FALSE, SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK);
  }
}

inline dFLOAT extract_output_variable_value(EXPRLANG_PROGRAM program,
                                                   PHYSICS_VARIABLE var,
                                                   cSTRING cdi_desc_var_name,
                                                   EQN_ERROR_HANDLER error_handler,
                                                   STP_GEOM_VARIABLE *point)
{
  EXPRLANG_VALUE value = exprlang_get_output_variable_value(program, var->program_index);
  CHARACTER err_buffer[128];

  if (value == NULL) {
    msg_internal_error("Unable to extract variable \"%s\" from case equations.", var->name);
    return 0;
  } else {
    dFLOAT converted_value =
      exprlang_uval_value_value(value) * var->conversion_slope + var->conversion_offset;

    // Test for a Nan.
    if (is_dfloat_nan(converted_value)) { // ISNANFUNC
      error_handler(EQN_ERROR_FPE, var, cdi_desc_var_name, converted_value, 0, point);
    }
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("extract output variable %s var_value %.16g u_value %.16g",
              cdi_desc_var_name,var->value, converted_value);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("value %.16g slope %.16g offset %.16g",
              exprlang_uval_value_value(value), var->conversion_slope, var->conversion_offset);
#endif
#if DEBUG_T_PTHERM_EQNS
    if (my_proc_id == 2)
      msg_print("TS %ld extract output variable %s value %f", g_timescale.m_time,
              cdi_desc_var_name, converted_value);
#endif
    return converted_value;
  }
}

// The following 3 functions [calculate_theta, calculate_phi,
// calculate_other_csys_values] are copied from ExaCASE/CPC usage
// they should be placed in some general (math) utility component
static inline dFLOAT calculate_theta(dFLOAT x, dFLOAT y)
{
  if (x > 0.0)
    return atan(y/x);
  else if (x < 0.0)
    return ((y < 0.0) ? (atan(y/x) - M_PI) : (atan(y/x) + M_PI));
  else {
    if (y == 0.0)
      return 0.0;
    else
      return ((y > 0.0) ? M_PI_2 : -M_PI_2);
  }
}

static inline dFLOAT calculate_phi(dFLOAT r, dFLOAT z)
{
  if (z > 0.0)
    return atan(r/z);
  else
    return ((z < 0.0) ? (atan(r/z) + M_PI) : M_PI_2);
}

static inline VOID calculate_other_csys_values(dFLOAT x, dFLOAT y, dFLOAT z, dFLOAT &r,
                                               dFLOAT &rho, dFLOAT &phi, dFLOAT &theta)
{
  dFLOAT r_squared = x * x + y * y;
  r = sqrt(r_squared);
  rho = sqrt(r_squared + z*z);
  phi = calculate_phi(r, z);
  theta = calculate_theta(x, y);
}

// perform any necessary global to local coord transformations
static inline VOID set_local_coordinate_variables(EXPRLANG_PROGRAM program,
                                                  dFLOAT fx, dFLOAT fy, dFLOAT fz,
                                                  STP_GEOM_VARIABLE normal[3])
{
  sG3_POINT global_pt = g3_point_make(fx, fy, fz);
  sG3_VEC global_norm;
  if (normal)
    global_norm = g3_vec_make(normal[0], normal[1], normal[2]);

  ccDOTIMES(i, sim.n_csys) {
    CSYS csys = sim.csys_table + i;
    sG3_POINT local_pt = g3_xform_point(global_pt, csys->g_to_l_xform);
    dFLOAT local_x = g3_point_x(local_pt);
    dFLOAT local_y = g3_point_y(local_pt);
    dFLOAT local_z = g3_point_z(local_pt);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 0, local_x);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 1, local_y);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 2, local_z);

    if (normal) {
      sG3_VEC local_norm = g3_xform_vec(global_norm, csys->g_to_l_xform);

      exprlang_set_struct_uval_input_variable_value(program, csys->name, 3, g3_vec_x(local_norm));
      exprlang_set_struct_uval_input_variable_value(program, csys->name, 4, g3_vec_y(local_norm));
      exprlang_set_struct_uval_input_variable_value(program, csys->name, 5, g3_vec_z(local_norm));
    } else {
      exprlang_set_struct_uval_input_variable_value(program, csys->name, 3, 0);
      exprlang_set_struct_uval_input_variable_value(program, csys->name, 4, 0);
      exprlang_set_struct_uval_input_variable_value(program, csys->name, 5, 0);
    }

    dFLOAT r, rho, phi, theta;
    calculate_other_csys_values(local_x, local_y, local_z, r, rho, phi, theta);

    exprlang_set_struct_uval_input_variable_value(program, csys->name, 6, r);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 7, rho);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 8, phi);
    exprlang_set_struct_uval_input_variable_value(program, csys->name, 9, theta);
  }
}

VOID eval_program(EXPRLANG_PROGRAM program, STP_GEOM_VARIABLE point[3], 
                         STP_GEOM_VARIABLE normal[3], BASETIME timestep,
                         sFLOAT powertherm_time)
{
  dFLOAT x = point[0];
  dFLOAT y = point[1];
  dFLOAT z = point[2];
  if (are_expr_relative_to_user_csys) {
    x -= sim.case_origin[0];
    y -= sim.case_origin[1];
    z -= sim.case_origin[2];
  }

  exprlang_program_reset_changeable_constants(program);

  exprlang_set_uval_input_variable_value(program, "x", x);
  exprlang_set_uval_input_variable_value(program, "y", y);
  exprlang_set_uval_input_variable_value(program, "z", z);

  if (normal /* && is_normal_input_to_programs */) {
    exprlang_set_uval_input_variable_value(program, "nx", normal[0]);
    exprlang_set_uval_input_variable_value(program, "ny", normal[1]);
    exprlang_set_uval_input_variable_value(program, "nz", normal[2]);
  } else {
    exprlang_set_uval_input_variable_value(program, "nx", 0);
    exprlang_set_uval_input_variable_value(program, "ny", 0);
    exprlang_set_uval_input_variable_value(program, "nz", 0);
  }

  if (is_time_input_to_programs) {
    exprlang_set_uval_input_variable_value(program, "t", timestep);
    if (sim.is_ptherm_time_input_to_programs)
      exprlang_set_uval_input_variable_value(program, "t_ptherm", powertherm_time);
  }

  // perform any necessary global to local coord transformations
  set_local_coordinate_variables(program, x, y, z, normal);

  BOOLEAN ok_p = exprlang_eval(program, sim_expr_errfcn);

  if (!ok_p) {
    msg_internal_error("Errors found in evaluation of CDI equations.");
  }
}

VOID sPHYSICS_DESCRIPTOR::eval_time_varying_only_parameter_program(BASETIME timestep,
                                                                   sFLOAT powertherm_time,
                                                                   EQN_ERROR_HANDLER error_handler)
{
  STP_GEOM_VARIABLE point_or_normal[3] = {0};
  eval_program(parameter_program, point_or_normal, point_or_normal, timestep, powertherm_time);

  CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
  PHYSICS_VARIABLE parameter = parameters();
  ccDOTIMES(i, n_parameters) {
    if (parameter->is_time_varying && !parameter->is_space_varying) {
      cSTRING cdi_desc_var_name = ptd->continuous_dp_var[i%ptd->n_continuous_dp]->name; // account for shells with n_layers > 1
      parameter->value = extract_output_variable_value(parameter_program, parameter, cdi_desc_var_name, error_handler, point_or_normal);
    }
    parameter++;
  }

  //uds
  if (n_uds_parameters > 0) {
    CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
    PHYSICS_VARIABLE uds_parameter = uds_parameters();
    
    ccDOTIMES(i, n_uds_parameters) {
      if (uds_parameter->is_time_varying && !uds_parameter->is_space_varying) {
	asINT32 index = i % uds_phys_type_desc->n_parameters;
	cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	
	uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point_or_normal);
      }
      uds_parameter++;
    }
  }
}

VOID sPHYSICS_DESCRIPTOR::eval_time_and_space_varying_parameter_program(STP_GEOM_VARIABLE point[3],
                                                                        STP_GEOM_VARIABLE normal[3],
                                                                        BASETIME timestep,
                                                                        sFLOAT powertherm_time,
                                                                        EQN_ERROR_HANDLER error_handler)
{
  eval_program(parameter_program, point, normal, timestep, powertherm_time);

  CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
  PHYSICS_VARIABLE parameter = parameters();
  ccDOTIMES(i, n_parameters) {
    // For the moment, we don't have a time varying only parameter
    // program, so we consider all time varying only parameters to
    // be time *and* space varying.
    if (parameter->is_time_varying /* && parameter->is_space_varying */ ) {
      cSTRING cdi_desc_var_name = ptd->continuous_dp_var[i%ptd->n_continuous_dp]->name; // account for shells with n_layers > 1
      parameter->value = extract_output_variable_value(parameter_program, parameter, cdi_desc_var_name, error_handler, point);
    }
    parameter++;
  }

  //UDS
  if (n_uds_parameters > 0) {
    CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
    PHYSICS_VARIABLE uds_parameter = uds_parameters();
    ccDOTIMES(i, n_uds_parameters) {
      // For the moment, we don't have a time varying only parameter
      // program, so we consider all time varying only parameters to
      // be time *and* space varying.
      if (uds_parameter->is_time_varying /* && uds_parameter->is_space_varying */ ) {
	asINT32 index = i % uds_phys_type_desc->n_parameters;
	cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	
	uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point);
      }
      uds_parameter++;
    }
  }
}

VOID sPHYSICS_DESCRIPTOR::eval_space_varying_only_parameter_program(STP_GEOM_VARIABLE point[3],
                                                                    STP_GEOM_VARIABLE normal[3],
                                                                    EQN_ERROR_HANDLER error_handler)
{
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  if (parameter_program) {
    eval_program(parameter_program, point, normal, timestep, powertherm_time);

    CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
    PHYSICS_VARIABLE parameter = parameters();
    ccDOTIMES(i, n_parameters) {
      if (!parameter->is_time_varying && parameter->is_space_varying) {
        cSTRING cdi_desc_var_name = ptd->continuous_dp_var[i%ptd->n_continuous_dp]->name; // account for shells with n_layers > 1
        parameter->value = extract_output_variable_value(parameter_program, parameter, cdi_desc_var_name, error_handler, point);
      }
      parameter++;
    }

    if (n_uds_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
      PHYSICS_VARIABLE uds_parameter = uds_parameters();
      ccDOTIMES(i, n_uds_parameters) {
	if (!uds_parameter->is_time_varying && uds_parameter->is_space_varying) {
	  asINT32 index = i % uds_phys_type_desc->n_parameters;
	  cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	  
	  uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point);
	}
	uds_parameter++;
      }
    }
  }
}

VOID sPHYSICS_DESCRIPTOR::eval_space_and_table_varying_parameter_program(STP_GEOM_VARIABLE point[3],
                                                                         STP_GEOM_VARIABLE normal[3],
                                                                         EQN_ERROR_HANDLER error_handler)
{
  BASETIME timestep = 0;
  sFLOAT powertherm_time = 0.0;
  if (parameter_program) {
    eval_program(parameter_program, point, normal, timestep, powertherm_time);

    CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
    PHYSICS_VARIABLE parameter = parameters();
    ccDOTIMES(i, n_parameters) {
      if (!parameter->is_time_varying && parameter->is_eqn) {
        cSTRING cdi_desc_var_name = ptd->continuous_dp_var[i%ptd->n_continuous_dp]->name; // account for shells with n_layers > 1
        parameter->value = extract_output_variable_value(parameter_program, parameter, cdi_desc_var_name, error_handler, point);
      }
      parameter++;
    }

    if (n_uds_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
      PHYSICS_VARIABLE uds_parameter = uds_parameters();
      ccDOTIMES(i, n_uds_parameters) {
	if (!uds_parameter->is_time_varying && uds_parameter->is_eqn) {
	  asINT32 index = i % uds_phys_type_desc->n_parameters;
	  cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	  
	  uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point);
	}
	uds_parameter++;
      }
    }
  }

  some_constant_parameter_in_need_of_eval = FALSE;
}

VOID sPHYSICS_DESCRIPTOR::eval_initial_condition_program(STP_GEOM_VARIABLE point[3],
                                                         STP_GEOM_VARIABLE normal[3],
                                                         EQN_ERROR_HANDLER error_handler)
{
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  if (initial_condition_program) {
    eval_program(initial_condition_program, point, normal, timestep, powertherm_time);

    CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
    PHYSICS_VARIABLE var = initial_conditions();
    ccDOTIMES(i, n_initial_conditions) {
      if (var->is_space_varying) {
        cSTRING cdi_desc_var_name = ptd->initial_condition_var[i%ptd->n_initial_conditions]->name; // account for shells with n_layers > 1
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("before extract output variable i %d %s",i,
              cdi_desc_var_name);
#endif
        var->value = extract_output_variable_value(initial_condition_program, var, cdi_desc_var_name, error_handler, point);
      }
      var++;
    }

    if (n_uds_initial_conditions > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
      PHYSICS_VARIABLE uds_var = uds_initial_conditions();
      ccDOTIMES(i, n_uds_initial_conditions) {
	if (uds_var->is_space_varying) {
	  asINT32 index = i % uds_phys_type_desc->n_initial_conditions;
	  cSTRING cdi_desc_var_name = uds_phys_type_desc->initial_condition_var[index]->name;	  
	  uds_var->value = extract_output_variable_value(initial_condition_program, uds_var, cdi_desc_var_name, error_handler, point);
	}
	uds_var++;
      }
    }
  }
}

VOID sPHYSICS_DESCRIPTOR::eval_space_and_table_varying_uds_parameter_program(STP_GEOM_VARIABLE point[3],
									     STP_GEOM_VARIABLE normal[3],
									     EQN_ERROR_HANDLER error_handler)
{
  assert(n_uds_parameters > 0);
  BASETIME timestep = 0;
  sFLOAT powertherm_time = 0.0;
  if (parameter_program) {
    eval_program(parameter_program, point, normal, timestep, powertherm_time);
    
    CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
    CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
    PHYSICS_VARIABLE uds_parameter = uds_parameters();
    ccDOTIMES(i, n_uds_parameters) {
      if (!uds_parameter->is_time_varying && uds_parameter->is_eqn) {
	asINT32 index = i % uds_phys_type_desc->n_parameters;
	cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	  
	uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point);
      }
      uds_parameter++;
    }
  }

  //some_constant_parameter_in_need_of_eval = FALSE;  //not go through all parameters
}


VOID sPHYSICS_DESCRIPTOR::eval_time_and_space_varying_uds_parameter_program(STP_GEOM_VARIABLE point[3],
									    STP_GEOM_VARIABLE normal[3],
									    BASETIME timestep,
									    sFLOAT powertherm_time,
									    EQN_ERROR_HANDLER error_handler)
{
  assert(n_uds_parameters > 0);
  eval_program(parameter_program, point, normal, timestep, powertherm_time);
  
  CDI_PHYS_TYPE_DESCRIPTOR ptd = phys_type_desc;
  CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(ptd->cdi_physics_type);
  PHYSICS_VARIABLE uds_parameter = uds_parameters();
  ccDOTIMES(i, n_uds_parameters) {
    // For the moment, we don't have a time varying only parameter
    // program, so we consider all time varying only parameters to
    // be time *and* space varying.
    if (uds_parameter->is_time_varying /* && uds_parameter->is_space_varying */ ) {
      asINT32 index = i % uds_phys_type_desc->n_parameters;
      cSTRING cdi_desc_var_name = uds_phys_type_desc->parameter_var[index]->name;	
      uds_parameter->value = extract_output_variable_value(parameter_program, uds_parameter, cdi_desc_var_name, error_handler, point);
    }
    uds_parameter++;
  }
}


static VOID read_and_define_eqns_structs(LGI_STREAM stream, EXPRLANG_PROGRAM program)
{
  while (1) {
    LGI_TAG tag = lgi_peek_tag(stream);
    if (tag.id != LGI_EQN_STRUCT_TAG)
      return;

    LGI_EQN_STRUCT header;
    lgi_read_next_head(stream, header);

    STRING struct_name = xnew char[header.name_length + 1];
    lgi_read(stream, struct_name, header.name_length);
    struct_name[header.name_length] = '\0';

    cSTRING *member_names = xnew cSTRING[header.n_members];
    EXPRLANG_TYPE *member_types = xnew EXPRLANG_TYPE[header.n_members];
    dFLOAT *member_values = xnew dFLOAT[header.n_members];

    ccDOTIMES(i, header.n_members) {
      LGI_EQN_STRUCT_MEMBER lgi_member;
      lgi_read(stream, lgi_member);
      STRING name = xnew char[lgi_member.name_length + 1];
      STRING unit = xnew char[lgi_member.unit_length + 1];
      STRING unit_class = xnew char[lgi_member.unit_class_length + 1];
      lgi_read(stream, name, lgi_member.name_length);
      lgi_read(stream, unit, lgi_member.unit_length);
      lgi_read(stream, unit_class, lgi_member.unit_class_length);
      name[lgi_member.name_length] = '\0';
      unit[lgi_member.unit_length] = '\0';
      unit_class[lgi_member.unit_class_length] = '\0';
      member_names[i] = name;
      member_types[i] = create_uval_type(units_db, unit);
      member_values[i] = lgi_member.value;
      delete [] unit;
      delete [] unit_class;
    }
    
    EXPRLANG_TYPE struct_type = exprlang_make_struct_type(header.n_members, member_names, member_types);
    // struct_name is referenced internally by EXPRLANG. Do not free it.
    exprlang_define_input_variable(program, struct_name, struct_type, FALSE, 0);
    ccDOTIMES(i, header.n_members) {
      exprlang_set_struct_uval_input_variable_value(program, struct_name, i, member_values[i]);
      delete[] member_names[i];
      exprlang_type_free(member_types[i]);
    }

    delete[] member_names;
    delete[] member_types;
    delete[] member_values;
  }
}

static VOID finalize_eqns(LGI_STREAM stream)
{
  EXPRLANG_PROGRAM program = g_case_program;

  if (program) { // CDI may lack both undb and eqns

    exprlang_set_rand_dependence_mask(program, (SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK
                                                | TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK));
    exprlang_set_randt_dependence_mask(program, (TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK));

    // Use a seed for the random number generator that is unique to this processor. Also
    // make sure it is not zero.
    uINT64 rand_seed = ((my_proc_id + 1) << 4) + 2;
    exprlang_init_rand(rand_seed);
    uINT64 randt_seed = (513 << 9) + 7;
    exprlang_init_randt(randt_seed);

    define_eqns_input_variables(program);
    define_eqns_table_input_variables(program);
    define_eqns_csys_input_variables(program);
    read_and_define_eqns_structs(stream, program);

    BOOLEAN ok_p = exprlang_check(program, sim_expr_errfcn);

    if (!ok_p)
      msg_internal_error("Errors in CDI equations.");

    exprlang_set_selective_evaluation(program, (SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK
                                                | TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK));

    // eval_constant_eqns(program);
  }
}

VOID read_equations(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_EQUATIONS_TAG)
    return;

  if (units_db == NULL) {
    // Can't have equations if no units database
    msg_error("Missing %s record (ID %d) in LGI file.",
              lgi_tag_namestring(LGI_UNITS_DB_TAG), LGI_UNITS_DB_TAG);
  }

  LGI_EQUATIONS_HEADER header;
  lgi_read_next_head(stream, header);

  if (header.eqns_length > 0) {
    char *eqns = xnew char [ header.eqns_length + 1 ];

    lgi_read(stream, eqns, header.eqns_length);

    eqns[header.eqns_length] = '\0';

    EXPRLANG_PROGRAM program = exprlang_parse(eqns,
                                              units_db,
                                              NULL);
    delete[] eqns;

    is_normal_input_to_programs = header.normal_is_input;
    is_time_input_to_programs = header.time_is_input;
    sim.is_ptherm_time_input_to_programs = header.ptherm_time_is_input;
    are_expr_relative_to_user_csys = header.are_expr_relative_to_user_csys;

    if (program == NULL) {
      msg_internal_error("CDI file contains unreadable equations.");
    }
    else {
      g_case_program = program;
    }
  }
  
  finalize_eqns(stream);
}

VOID set_randt_initial_update_time(BASETIME time)
{
  if (g_case_program)
    exprlang_set_randt_initial_update_time(g_case_program, time);
}

static void output_variable_conversion_coefficients(cSTRING variable_name,
                                                    EXPRLANG_TYPE type,
                                                    cSTRING target_unit_name,
                                                    dFLOAT *slope_return,
                                                    dFLOAT *offset_return)
{
  UNITS_UNIT unit = exprlang_uval_type_unit(type);
  UNITS_UNIT target_unit;
  if (units_parse_unit(units_db, target_unit_name, &target_unit) != UNITS_STATUS_OK) {
    msg_internal_error("Units database is missing unit \"%s\".", target_unit_name);
  }
  else {
    UNITS_STATUS status = units_conversion_coefficients(units_db,
                                                        unit,
                                                        target_unit,
                                                        slope_return,
                                                        offset_return);
    if (status != UNITS_STATUS_OK) {
      msg_internal_error("Error in converting the units for variable \"%s\".", variable_name);
    }
  }
}

void output_variable_conversion_coefficients_and_index(cSTRING variable_name,
                                                              cSTRING unit_class,
                                                              asINT32 *index_return,
                                                              dFLOAT *slope_return,
                                                              dFLOAT *offset_return)
{
  asINT32 index = exprlang_find_output_variable(g_case_program, variable_name);
  if (index < 0)
    msg_internal_error("Case parameter refers to undefined variable \"%s\".",
                       variable_name);
  else {
    EXPRLANG_TYPE type = exprlang_output_variable_type(g_case_program, index);

    if (exprlang_type_kind(type) != EXPRLANG_TYPE_KIND_IS_UVAL)
      msg_internal_error("Variable \"%s\" has incorrect type.", variable_name);
    else {
      *index_return = index;
      if (strcmp(unit_class, "Dimensionless") == 0)
        output_variable_conversion_coefficients(variable_name,
                                                type,
                                                "1",
                                                slope_return,
                                                offset_return);
      else {
        char lattice_unit_name[200];
        if (sim.is_2d() && strcmp(unit_class, "MassFlow") == 0) {
          // In 2D case, mass flow unit class is actually "MassFlowPerLength" in PowerCASE. For Mass flow variable defined in equations, simulator needs to use the correct
          // unit class when converting to lattice units. See PR40291 and PR48721
          sprintf(lattice_unit_name, "Lattice%sPerLength", unit_class);
        } else {
          sprintf(lattice_unit_name, "Lattice%s", unit_class);
        }
        output_variable_conversion_coefficients(variable_name,
                                                type,
                                                lattice_unit_name,
                                                slope_return,
                                                offset_return);
      }
    }
  }
}

static VOID init_physics_desc(PHYSICS_DESCRIPTOR desc, cSTRING name, CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc, LGI_PHYSICS_DESCRIPTOR lgi_physics_desc)
{
  desc->name = name;
  desc->phys_type_desc = phys_type_desc;
  desc->n_parameters = phys_type_desc->n_continuous_dp;
  desc->n_initial_conditions = phys_type_desc->n_initial_conditions;
  desc->coupling_model_index = -1;
  desc->init_from_coupling_model_p = 0;
  if(desc->n_layers > 0) {
    desc->n_initial_conditions *= desc->n_layers;
    desc->n_parameters         *= desc->n_layers;
  }

#if BUILD_D39_LATTICE
  if (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_LOCAL_REF_FRAME)
    desc->n_parameters = desc->n_parameters + 1;
#endif

  if (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_SOLID_CONDUCTOR) {
    // account for the additional (not in CDI) scaled_volume_fraction and total_simulated_volume parameters
    desc->_parameters = cnew sPHYSICS_VARIABLE[desc->n_parameters + 2];
  } else if (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_CONDUCTION_LAYER) {
    // account for the additional (not in CDI) face_area parameter for each layer
    desc->_parameters = cnew sPHYSICS_VARIABLE[desc->n_parameters + desc->n_layers];
  } else if (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX) {
    // account for the additional (not in CDI) face_area and is_heat_flux parameters 
    desc->_parameters = cnew sPHYSICS_VARIABLE[desc->n_parameters + 2];
  } else {
    desc->_parameters = cnew sPHYSICS_VARIABLE[desc->n_parameters];
  }

  desc->_initial_conditions = cnew sPHYSICS_VARIABLE [ desc->n_initial_conditions ];

  //UDS
  desc->n_uds_parameters = lgi_physics_desc.n_uds_parameters;
  desc->n_uds_initial_conditions = lgi_physics_desc.n_uds_initial_conditions;
  if (desc->n_uds_parameters > 0)
    desc->_uds_parameters = cnew sPHYSICS_VARIABLE [ desc->n_uds_parameters ];
  else
    desc->_uds_parameters = NULL;
  if (desc->n_uds_initial_conditions > 0)
    desc->_uds_initial_conditions = cnew sPHYSICS_VARIABLE [ desc->n_uds_initial_conditions ];
  else
    desc->_uds_initial_conditions = NULL;
}

static cSTRING boundary_seed_var_name(DGF_BOUNDARY_SEED_VAR_TYPE var_type)
{
  switch (var_type) {
  case DGF_BOUNDARY_SEED_VAR_PRESSURE: return "pressure";
  case DGF_BOUNDARY_SEED_VAR_XVEL:     return "x_velocity";
  case DGF_BOUNDARY_SEED_VAR_YVEL:     return "y_velocity";
  case DGF_BOUNDARY_SEED_VAR_ZVEL:     return "z_velocity";
  case DGF_BOUNDARY_SEED_VAR_TEMP:     return "temperature";
  case DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY: return "turb_kinetic_energy";
  case DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION:    return "turb_dissipation";

  case DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX: return "x_mass_flux";
  case DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX: return "y_mass_flux";
  case DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX: return "z_mass_flux";

  case DGF_BOUNDARY_SEED_VAR_MASS_FLOW:   return "mass_flow";
  default:                                return "unrecognized variable";
  }
}

static DGF_BOUNDARY_SEED_VAR_TYPE boundary_seed_var_from_cdi_var(asINT32 cdi_id)
{
  switch (cdi_id) {
  case CDI_VAR_ID_NEW_PRESSURE: return DGF_BOUNDARY_SEED_VAR_PRESSURE;
  case CDI_VAR_ID_VEL_X:    return DGF_BOUNDARY_SEED_VAR_XVEL;
  case CDI_VAR_ID_VEL_Y:    return DGF_BOUNDARY_SEED_VAR_YVEL;
  case CDI_VAR_ID_VEL_Z:    return DGF_BOUNDARY_SEED_VAR_ZVEL;
  case CDI_VAR_ID_TEMP:     return DGF_BOUNDARY_SEED_VAR_TEMP;
  case CDI_VAR_ID_TURB_KE:  return DGF_BOUNDARY_SEED_VAR_TURB_KINETIC_ENERGY;
  case CDI_VAR_ID_TURB_DISSIPATION: return DGF_BOUNDARY_SEED_VAR_TURB_DISSIPATION;

  case CDI_VAR_ID_NEW_MASS_FLUX_X:  return DGF_BOUNDARY_SEED_VAR_X_MASS_FLUX;
  case CDI_VAR_ID_NEW_MASS_FLUX_Y:  return DGF_BOUNDARY_SEED_VAR_Y_MASS_FLUX;
  case CDI_VAR_ID_NEW_MASS_FLUX_Z:  return DGF_BOUNDARY_SEED_VAR_Z_MASS_FLUX;

  case CDI_VAR_ID_MASS_FLOW_RATE:   return DGF_BOUNDARY_SEED_VAR_MASS_FLOW;
  default:                          return DGF_BOUNDARY_SEED_VAR_NONE;
  }
}

VOID read_physics_descriptor(LGI_STREAM stream, PHYSICS_DESCRIPTOR physics_desc,
                             sINT16 index, BOOLEAN is_record_head)
{
  LGI_PHYSICS_DESCRIPTOR lgi_physics_desc;
  if (is_record_head) {
    lgi_read_next_head(stream, lgi_physics_desc);
  } else {
    lgi_read(stream, lgi_physics_desc);
  }

  char *physics_desc_name = xnew char [ lgi_physics_desc.name_length ];

  if (lgi_physics_desc.name_length > 0) {
    lgi_read(stream, physics_desc_name, lgi_physics_desc.name_length);
  }
  
  // Check if this is a heat flow physics type and convert it to heat flux
  BOOLEAN is_heat_flow_physics = FALSE;
  if (lgi_physics_desc.phys_type_desc_index == CDI_PHYS_TYPE_FIXED_HEAT_FLOW) {
    // Convert to heat flux physics type
    lgi_physics_desc.phys_type_desc_index = CDI_PHYS_TYPE_FIXED_HEAT_FLUX;
    is_heat_flow_physics = TRUE;
  }

  CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(lgi_physics_desc.phys_type_desc_index);
  asINT32 n_continuous_dp = phys_type_desc->n_continuous_dp;
  asINT32 n_initial_conditions = phys_type_desc->n_initial_conditions;

  asINT32 lgi_n_params = lgi_physics_desc.n_parameters;
#if BUILD_D39_LATTICE
  BOOLEAN is_lrf_descriptor = (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_LOCAL_REF_FRAME);

  if (is_lrf_descriptor)
    lgi_n_params--;
#endif
  
  if(lgi_physics_desc.n_layers > 0) {
    n_continuous_dp *= lgi_physics_desc.n_layers;
    n_initial_conditions *= lgi_physics_desc.n_layers;
  }
  if ((lgi_n_params != n_continuous_dp)
      || (lgi_physics_desc.n_initial_conditions != n_initial_conditions))
    msg_internal_error("Inconsistency in parameter or initial condition count of LGI physics descriptor.");

  switch (phys_type_desc->cdi_physics_type) {
    case ADIABATIC_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      if (sim.is_heat_transfer)
        //msg_internal_error("Acoustic Porous Media is not supported for cases with heat transfer");
      break;
#ifdef APM_WITH_HEAT_TRANSFER
    case FIXED_TEMP_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
    case VARIABLE_HEAT_RATE_ACOUSTIC_POROUS_CDI_FLUID_PHYSICS_TYPE_CASE:
      msg_internal_error("Only Adiabatic Acoustic Porous Media is supported");
      break;
#endif
  }

  physics_desc->n_layers = lgi_physics_desc.n_layers;
  init_physics_desc(physics_desc, physics_desc_name, phys_type_desc, lgi_physics_desc);

  physics_desc->index = index;
  physics_desc->coupling_model_index = lgi_physics_desc.coupling_model_index;
  physics_desc->init_from_coupling_model_p = lgi_physics_desc.init_pf_bc_coupling_p;

  physics_desc->parameter_program = g_case_program;
  physics_desc->initial_condition_program = g_case_program;

#if BUILD_D39_LATTICE
  if (is_lrf_descriptor) {
    ((LRF_PHYSICS_DESCRIPTOR)physics_desc)->has_transonic_flow = FALSE;
  }
#endif
  
  asINT32 data_table_len = lgi_physics_desc.data_table_length;
  if (data_table_len > 0) {
    physics_desc->data_table_string = xnew char [ data_table_len + 1 ];
    lgi_read(stream, physics_desc->data_table_string, data_table_len);
    physics_desc->data_table_string[data_table_len] = '\0';
  } else {
    physics_desc->data_table_string = NULL;
  }

  PHYSICS_VARIABLE vars = physics_desc->parameters();
  asINT32 n_vars = physics_desc->n_parameters;
  asINT32 n_vars_in_cdi_desc = phys_type_desc->n_continuous_dp;
  if (phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_CONDUCTION_LAYER) {
    // account for extra face area allocated in the parameters, but not in CDI
    n_vars += lgi_physics_desc.n_layers; 
    n_vars_in_cdi_desc ++; 
  }
  const sCDI_PHYS_TYPE_DESCRIPTOR::sDP_VAR **var = phys_type_desc->continuous_dp_var;

  // One pass thru loop for parameters; a second pass for initial conditions
  for(asINT32 k=0; k<2; k++,
        n_vars = physics_desc->n_initial_conditions,
        n_vars_in_cdi_desc = phys_type_desc->n_initial_conditions,
        vars = physics_desc->initial_conditions(),
        var = phys_type_desc->initial_condition_var) {
    ccDOTIMES(j, n_vars) {
      // For shell layers the variables repeat after reading variables of each layer.
      asINT32 i = j;
      if(lgi_physics_desc.n_layers > 0) 
        i = j % n_vars_in_cdi_desc; 
      
      LGI_PHYSICS_VARIABLE lgi_var;

      lgi_read(stream, lgi_var);

      vars[j].value = lgi_var.value;
      vars[j].is_eqn = FALSE;
      cSTRING heat_flow_unit_class = NULL;

      // Handle heat flow variables
      if (is_heat_flow_physics && k == 0) {
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS params =
            ((CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)physics_desc)->parameters();
        heat_flow_unit_class = "Power";

        if (var[i]->id == CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_IN) {
          // Map to heat_flux_1
          params->heat_flux_1.value = vars[j].value;
        } else if (var[i]->id == CDI_VAR_ID_CONDUCTOR_WALL_HEAT_FLOW_OUT) {
          // Map to heat_flux_2
          params->heat_flux_2.value = vars[j].value;
        }
        params->is_heat_flow.value = 1.0; // this is a heat_flow bc, need to calculate face area
      }

#if BUILD_D39_LATTICE
      if (is_lrf_descriptor && k==0 & i==(n_vars-1)) {//the last lrf parameters is hacked in;
        if (vars[j].value > 0)
          ((LRF_PHYSICS_DESCRIPTOR)physics_desc)->has_transonic_flow = TRUE;
        continue;
      }
#endif

      // A ref frame index must be constant so we can convert it here.
      if (var[i]->id == CDI_VAR_ID_REF_FRAME) {
        vars[j].value = translate_cdi_ref_frame_index(vars[j].value);

        if (vars[j].value < SRI_MIN_REF_FRAME_INDEX)
          msg_internal_error("CDI physics descriptor '%s' includes invalid reference"
                             " frame index %d.",
                             physics_desc_name == NULL ? "Unknown" : physics_desc_name,
                             (int)vars[j].value);
        else if (vars[j].value >= sim.n_lrf_physics_descs)
          msg_internal_error("CDI physics descriptor '%s' includes rotating reference"
                             " frame index %d, but there are only %d rotating reference frames"
                             " in the case.",
                             physics_desc_name == NULL ? "Unknown" : physics_desc_name,
                             (int)vars[j].value, sim.n_lrf_physics_descs);
      }
      
      if (var[i]->id == CDI_VAR_ID_REVERSE_MASS_FLOW) {
        BOOLEAN reverse_mass_flow_p = vars[j].value;
        vars[j].value = (reverse_mass_flow_p)? -1 : 1; // we need to do this because PowerCASE prefers
                                                       // to use booleans for this variable. Its name in 
                                                       // MASS_FLOW_SURFEL_PARAMETERS is mass_flow_sign
      }

      if (lgi_var.name_length > 0) {
        vars[j].name = xnew char [ lgi_var.name_length ];

        lgi_read(stream, (void *)vars[j].name, lgi_var.name_length);

        if (g_case_program == NULL)
          msg_internal_error("Variable \"%s\" referenced in CDI file, but no equations"
                             " are present in CDI file.", vars[j].name);

        asINT32 prog_index;
        cSTRING unit_class = is_heat_flow_physics ? heat_flow_unit_class : var[i]->unit_class; 
        output_variable_conversion_coefficients_and_index(vars[j].name,
                                                          unit_class,
                                                          &prog_index,
                                                          &vars[j].conversion_slope,
                                                          &vars[j].conversion_offset);

        auINT32 dmask = exprlang_output_variable_dependence_mask(g_case_program,
                                                                 prog_index);

        vars[j].is_eqn = TRUE;
        vars[j].is_space_varying = (dmask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
        vars[j].is_time_varying = (dmask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
        vars[j].program_index = prog_index;
      }

      // skip reading face_area parameter for shell layer
      if (k == 0 && phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_CONDUCTION_LAYER && (i == (phys_type_desc->n_continuous_dp - 1))) { j++; }
    }
  }
  
  //UDS
  BOOLEAN process_uds_data = (sim.uds_solver_type == LB_UDS && (physics_desc->n_uds_parameters > 0)); //parameters must be in uds data			      
  if (process_uds_data) {
    asINT32 n_scalars = sim.n_user_defined_scalars;
    CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(lgi_physics_desc.phys_type_desc_index);
    
    PHYSICS_VARIABLE uds_vars = physics_desc->uds_parameters();
    asINT32 n_uds_vars = physics_desc->n_uds_parameters;
    const sCDI_UDS_PHYS_TYPE_DESCRIPTOR::sDP_VAR **var = uds_phys_type_desc->parameter_var;
    BOOLEAN is_parameter = TRUE;
    
    // One pass thru loop for uds parameters; a second pass for uds initial conditions
    for(asINT32 k=0; k<2; k++,
	  is_parameter = TRUE,
	  n_uds_vars = physics_desc->n_uds_initial_conditions,
	  uds_vars = physics_desc->uds_initial_conditions(),
	  var = uds_phys_type_desc->initial_condition_var) {
      ccDOTIMES(i, n_uds_vars) {
	LGI_PHYSICS_VARIABLE lgi_var;
	
	lgi_read(stream, lgi_var);
	uds_vars[i].value = lgi_var.value;
	uds_vars[i].is_eqn = FALSE;
	
	if (lgi_var.name_length > 0) {  //eqn
	  uds_vars[i].name = xnew char [ lgi_var.name_length ];
	  
	  lgi_read(stream, (void *)uds_vars[i].name, lgi_var.name_length);
	  
	  if (g_case_program == NULL)
	    msg_internal_error("Variable \"%s\" referenced in CDI file, but no equations"
                             " are present in CDI file.", uds_vars[i].name);
	  
	  asINT32 prog_index;

	  asINT32 index;	  
	  if (is_parameter) {
	    index = i % uds_phys_type_desc->n_parameters;	    
	  } else { //initial condition
	    index = i % uds_phys_type_desc->n_initial_conditions;	    
	  }
	      	  
	  cSTRING unit_class = var[index]->unit_class;
	  
	  output_variable_conversion_coefficients_and_index(uds_vars[i].name,
							    unit_class,
							    &prog_index,
							    &uds_vars[i].conversion_slope,
							    &uds_vars[i].conversion_offset);
	  
	  auINT32 dmask = exprlang_output_variable_dependence_mask(g_case_program,
								   prog_index);
	  
	  uds_vars[i].is_eqn = TRUE;
	  uds_vars[i].is_space_varying = (dmask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
	  uds_vars[i].is_time_varying = (dmask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
	  uds_vars[i].program_index = prog_index;        
	}
      }
    }
  }
			      
  
  PHYSICS_VARIABLE parm = physics_desc->parameters();

  // initialize these to default values. The following
  // loop will flip them if some parameter satisfies them
  physics_desc->some_parameter_time_varying = FALSE;
  physics_desc->some_parameter_time_and_space_varying = FALSE;
  physics_desc->some_parameter_time_varying_only = FALSE;
  physics_desc->some_constant_parameter_in_need_of_eval = FALSE;
  physics_desc->all_parameters_sharable = TRUE;
  physics_desc->some_uds_parameter_time_varying = FALSE;  //LB_UDS
  physics_desc->all_uds_parameters_sharable = TRUE;       //LB_UDS
			      

  ccDOTIMES(i, physics_desc->n_parameters) {
    if (parm->is_time_varying) {
      physics_desc->some_parameter_time_varying = TRUE;
    }

    if (parm->is_time_varying && parm->is_space_varying)
      physics_desc->some_parameter_time_and_space_varying = TRUE;

    if (parm->is_time_varying && !parm->is_space_varying)
      physics_desc->some_parameter_time_varying_only = TRUE;

    if (!parm->is_time_varying && !parm->is_space_varying && parm->is_eqn)
      physics_desc->some_constant_parameter_in_need_of_eval = TRUE;

    if (!parm->is_sharable())
      physics_desc->all_parameters_sharable = FALSE;

    parm++;
  }

  //UDS
  if (process_uds_data) {
    PHYSICS_VARIABLE uds_parm = physics_desc->uds_parameters();
    ccDOTIMES(i, physics_desc->n_uds_parameters) {
      if (uds_parm->is_time_varying) {
	physics_desc->some_parameter_time_varying = TRUE;
	physics_desc->some_uds_parameter_time_varying = TRUE;
#if BUILD_GPU
	msg_error("Time-Varying equations are not supported on GPU");
#endif
      }
      
      if (uds_parm->is_time_varying && uds_parm->is_space_varying)
	physics_desc->some_parameter_time_and_space_varying = TRUE;
      
      if (uds_parm->is_time_varying && !uds_parm->is_space_varying)
	physics_desc->some_parameter_time_varying_only = TRUE;
      
      if (!uds_parm->is_time_varying && !uds_parm->is_space_varying && uds_parm->is_eqn)
	physics_desc->some_constant_parameter_in_need_of_eval = TRUE;
      
      if (!uds_parm->is_sharable()) {
	physics_desc->all_parameters_sharable = FALSE;
	physics_desc->all_uds_parameters_sharable = FALSE;
      }
      
      uds_parm++;
    }
  }

  // For the moment, we don't have a separate time varying only parameter
  // program, so we consider all time varying only parameters to be time
  // *and* space varying. Otherwise we will unnecessarily evaluate the
  // same program multiple times.
  //
  // Note that there is a similar consideration at the bottom of
  // sEVENT_READ_TABLE::process in async_events.cc.
  if (physics_desc->some_parameter_time_and_space_varying)
    physics_desc->some_parameter_time_varying_only = FALSE;
}

VOID global_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                              cSTRING cdi_desc_var_name, vFLOAT value,
                              vFLOAT aux_value,         // min or max
                              STP_GEOM_VARIABLE _point[3])         // unused
{
  CHARACTER err_buffer[128];

  cSTRING case_var = physics_var->name;
  asINT32 scale = -1;

  STP_GEOM_VARIABLE point[3] = { 0 };

  switch (type) {
  case EQN_ERROR_FPE:
    {
      simerr_report_error_code(SP_EER_FPE, scale, point, cdi_desc_var_name, case_var, value);
      break;
    }

  case EQN_ERROR_TOO_LARGE:
    {
      simerr_report_error_code(SP_EER_TOO_BIG, scale, point, cdi_desc_var_name, case_var, value, aux_value);
      break;
    }
  case EQN_ERROR_TOO_SMALL:
    {
      simerr_report_error_code(SP_EER_TOO_SMALL, scale, point, cdi_desc_var_name, case_var, value, aux_value);
      break;
    }
  default:
    msg_internal_error("Unrecognized eqn error type.");
    break;
  }
}

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
VOID xform_point_inv(dFLOAT dpoint[3], CSYS csys)
{
  sG3_POINT point;
  point.pcoord[0] = dpoint[0];
  point.pcoord[1] = dpoint[1];
  point.pcoord[2] = dpoint[2];

  point = g3_xform_point(point, csys->g_to_l_xform);

  dpoint[0] = point.pcoord[0];
  dpoint[1] = point.pcoord[1];
  dpoint[2] = point.pcoord[2];
}
//#endif

VOID xform_point(dFLOAT dpoint[3], CSYS csys)
{
  sG3_POINT point;
  point.pcoord[0] = dpoint[0];
  point.pcoord[1] = dpoint[1];
  point.pcoord[2] = dpoint[2];

  point = g3_xform_point(point, csys->l_to_g_xform);

  dpoint[0] = point.pcoord[0];
  dpoint[1] = point.pcoord[1];
  dpoint[2] = point.pcoord[2];
}

VOID read_gravity_buoyancy(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_GRAVITY_BUOYANCY_TAG) {
    return;
  }

  sim.gravity_buoyancy_desc = cnew sGRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR;

  sim.gravity_buoyancy_desc->m_type_index = GRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR_TYPE;
  read_physics_descriptor(stream, sim.gravity_buoyancy_desc, 0, TRUE);
  sim.gravity_buoyancy_desc->check_consistency();

  GRAVITY_BUOYANCY_PARAMETERS parms = sim.gravity_buoyancy_desc->parameters();

  sim.gravity[0] = parms->gravity[0].cvalue();
  sim.gravity[1] = parms->gravity[1].cvalue();
  sim.gravity[2] = parms->gravity[2].cvalue();

  asINT32 csys_index = parms->coord_sys.value;
  if (is_csys_index_non_default(csys_index)) {
    CSYS csys = &sim.csys_table[csys_index];
    xform_vector(sim.gravity, csys);
  }

  sim.buoyancy_ref_temp = parms->ref_temp.cvalue();
  sim.buoyancy_thermal_exp_coeff = parms->thermal_exp_coeff.cvalue();

  BOOLEAN gravity_non_zero = (sim.gravity[0] != 0) || (sim.gravity[1] != 0) || (sim.gravity[2] != 0);
  sim.is_buoyancy = (sim.is_heat_transfer && (sim.buoyancy_thermal_exp_coeff != 0)
                     && (sim.buoyancy_ref_temp >= 0) && gravity_non_zero);
  sim.is_gravity = gravity_non_zero;
}

VOID read_body_force_descriptors(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_BODY_FORCE_DESCRIPTORS_TAG) {
    return;
  }

  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  g_cdi_body_force_desc_from_part_index = cnew BODY_FORCE_PHYSICS_DESCRIPTOR[g_num_parts];

  sim.body_force_descs      = cnew sBODY_FORCE_PHYSICS_DESCRIPTOR[header.n_physics_descs];
  sim.n_body_force_descs    = header.n_physics_descs;

  ccDOTIMES(i, sim.n_body_force_descs) {
    cdiINT32 n_regions;
    lgi_read(stream, n_regions);
    if (n_regions == 0) { // if 0, a single body force applies to all regions
      ccDOTIMES(j, g_num_parts) {
        g_cdi_body_force_desc_from_part_index[j] = sim.body_force_descs;
      }
    }
    ccDOTIMES(j, n_regions) {
      cdiINT32 region_id;
      lgi_read(stream, region_id);
      g_cdi_body_force_desc_from_part_index[region_id] = sim.body_force_descs + i;
    }
    sim.body_force_descs[i].m_type_index = BODY_FORCE_PHYSICS_DESCRIPTOR_TYPE;
    read_physics_descriptor(stream, sim.body_force_descs + i, i, FALSE);
    sim.body_force_descs[i].check_consistency();
  }
}

VOID sCONDUCTION_MATERIAL::eval_temp_varying_only_parameter(dFLOAT temp_in, asINT32 scale, STP_GEOM_VARIABLE *point)
{
  auto eval_matprop_lambda = [this, &temp_in] (auto parm, auto parmType, auto parmName) {
    if (parm->is_temp_varying)  {
      const std::string &curveName = property_curve_map[parmType].first; 
      const std::string &propName = property_curve_map[parmType].second; 
      parm->value = g_conduction_sim_info.material_data_curves.at(curveName).interpolate(propName, temp_in);
    }
  };

  eval_matprop_lambda(&density, MT_DATA_DENSITY, "density");
  eval_matprop_lambda(&specific_heat, MT_DATA_SPECIFIC_HEAT, "specific_heat");
  eval_matprop_lambda(&(conductivity[0]), MT_DATA_CONDUCTIVITY_11,"conductivity[0]");
  eval_matprop_lambda(&(conductivity[1]), MT_DATA_CONDUCTIVITY_22,"conductivity[1]");
  eval_matprop_lambda(&(conductivity[2]), MT_DATA_CONDUCTIVITY_33,"conductivity[2]");

  sdFLOAT kappa_principal[3] = {0, 0, 0};
  ccDOTIMES (i, sim.num_dims) {
    kappa_principal[i] = conductivity[i].value/(density.value * specific_heat.value);
  }

  // Check if principal kappa values exceed CFL condition
  sdFLOAT kappa_max = MAX(MAX(kappa_principal[0], kappa_principal[1]), kappa_principal[2]);
  const sdFLOAT kappa_scale_down = g_pfc_kappa_ceiling / (g_timescale.conduction_delta_t() * kappa_max);

  if (kappa_scale_down < 1.0) {
    simerr_report_error_code(SP_EER_CONDUCTION_LATTICE_DIFFUSIVITY_EXCEEDED, scale, point, temp_in, kappa_max);
  }
}

VOID sCONDUCTION_MATERIAL::eval_space_varying_only_parameter_program (STP_GEOM_VARIABLE point[3],
                                                                      STP_GEOM_VARIABLE normal[3],
                                                                      EQN_ERROR_HANDLER error_handler) 
{
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  if (parameter_program) {
    eval_program(parameter_program, point, normal, timestep, powertherm_time);
    auto eval_matprop_lambda = [point, error_handler, parameter_program = parameter_program] (auto parm, auto parmName) {
      if (!parm->is_time_varying && parm->is_space_varying)  {
        parm->value = extract_output_variable_value(parameter_program, parm, parmName, error_handler, point);
      }
    };
    eval_matprop_lambda(&density, "density");
    eval_matprop_lambda(&specific_heat, "specific_heat");
    eval_matprop_lambda(&(conductivity[0]), "conductivity[0]");
    eval_matprop_lambda(&(conductivity[1]), "conductivity[1]");
    eval_matprop_lambda(&(conductivity[2]), "conductivity[2]");
  }
}

VOID sCONDUCTION_MATERIAL::eval_space_and_table_varying_parameter_program (STP_GEOM_VARIABLE point[3],
                                                                           STP_GEOM_VARIABLE normal[3],
                                                                           EQN_ERROR_HANDLER error_handler) 
{
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  if (parameter_program) {
    eval_program(parameter_program, point, normal, timestep, powertherm_time);
    auto eval_matprop_lambda = [point, error_handler, parameter_program = parameter_program] (auto parm, auto parmName) {
      if (!parm->is_time_varying && parm->is_eqn)  {
        parm->value = extract_output_variable_value(parameter_program, parm, parmName, error_handler, point);
      }
    };
    eval_matprop_lambda(&density, "density");
    eval_matprop_lambda(&specific_heat, "specific_heat");
    eval_matprop_lambda(&(conductivity[0]), "conductivity[0]");
    eval_matprop_lambda(&(conductivity[1]), "conductivity[1]");
    eval_matprop_lambda(&(conductivity[2]), "conductivity[2]");
  }

  some_constant_parameter_in_need_of_eval = FALSE;
}



static void get_data_curve_and_property_name(const std::string& text, std::string& curve_name, std::string& prop_name) {
  size_t period_pos = text.find('.');
  if (period_pos != std::string::npos) {
    if (period_pos > 0) {  // Make sure period_pos is not 0
      curve_name = text.substr(0, period_pos);
    } else {
      curve_name = ""; // Or handle it differently if needed
    }
    prop_name = text.substr(period_pos + 1);
  } else {
    curve_name = text;
    prop_name = "";
  }
}

VOID read_conduction_solid_materials(LGI_STREAM stream) {
  LGI_CONDUCTION_SOLID_MATERIAL_HEADER header;
  lgi_read_next_head(g_lgi_stream, header);
  CONDUCTION_SIM_INFO parameters = &g_conduction_sim_info;
  parameters->num_materials = 0;
  ccDOTIMES(material_index, header.n_materials) {

    asINT32 name_length;
    std::string name;
    lgi_read(stream, &name_length, sizeof(name_length));
    if (name_length > 1) lgi_read(stream, name, name_length);

    CONDUCTION_MATERIAL new_material = xnew sCONDUCTION_MATERIAL(name, g_case_program);


    new_material->m_name[name_length] = '\0';
    int default_axis = 0;
    auto read_material_prop_lambda = [material = new_material, stream, material_index]
                                      (auto &parm, auto propName, auto unitClass, auto axis) {
      LGI_PHYSICS_VARIABLE lgi_var;
      lgi_read(stream, lgi_var);
      parm.value = lgi_var.value;
      parm.is_eqn = parm.is_space_varying = parm.is_time_varying = parm.is_temp_varying = FALSE;
      if (lgi_var.name_length > 1 && lgi_var.type == IS_EQN) {
        parm.name = xnew char [lgi_var.name_length];
        lgi_read(stream, (void *)parm.name, lgi_var.name_length);
        if (g_case_program == NULL)
          msg_internal_error("Variable \"%s\" referenced in CDI file, but no equations"
                             " are present in CDI file.", parm.name);
        asINT32 prog_index;
        cSTRING unit_class = unitClass;
        output_variable_conversion_coefficients_and_index(parm.name,
                                                          unitClass,
                                                          &prog_index,
                                                          &parm.conversion_slope,
                                                          &parm.conversion_offset);
        auINT32 dmask = exprlang_output_variable_dependence_mask(g_case_program,
                                                                 prog_index);
        parm.is_eqn = TRUE; 
        parm.is_space_varying = (dmask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
        parm.is_time_varying = (dmask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
        if (parm.is_time_varying) {
          msg_internal_error("Time-varying property \"%s\" for material \"%s\" is not supported.", propName, material->m_name.c_str());
        }
        if (parm.is_time_varying && parm.is_space_varying)
          material->some_parameter_time_and_space_varying = TRUE;
        if (parm.is_time_varying && !parm.is_space_varying)
          material->some_parameter_time_varying_only = TRUE;
        if (!parm.is_time_varying && !parm.is_space_varying && parm.is_eqn)
          material->some_constant_parameter_in_need_of_eval = TRUE;
        if (!parm.is_sharable())
          material->all_parameters_sharable = FALSE;
        parm.program_index = prog_index;
      } else if (lgi_var.name_length > 1 && lgi_var.type == IS_DATA_CURVE) {
        parm.name = xnew char [lgi_var.name_length];
        lgi_read(stream, (void *)parm.name, lgi_var.name_length);
        std::string data_curve_name = "", prop_name = "";
        get_data_curve_and_property_name(parm.name, data_curve_name, prop_name);
        std::pair<std::string, std::string> pairName(data_curve_name, prop_name);
        if(propName == "density")
          material->property_curve_map[MT_DATA_DENSITY] = pairName;
        else if(propName == "specific_heat")
          material->property_curve_map[MT_DATA_SPECIFIC_HEAT] = pairName;
        else if(propName == "conductivity") {
          MT_DATA cond_data_type;
          switch (axis) {
            case 0:
              cond_data_type = MT_DATA_CONDUCTIVITY_11;
              break;
            case 1:
              cond_data_type = MT_DATA_CONDUCTIVITY_22;
              break;
            case 2:
              cond_data_type = MT_DATA_CONDUCTIVITY_33;
              break;
            default:
              msg_internal_error("Incorrect thermal conductivity component %d for material \"%s\".", 
                                 axis, material->m_name.c_str());
          }
          material->property_curve_map[cond_data_type] = pairName;
        }
        else
          msg_error("Unsupported material property \"%s\"", propName);
        parm.is_temp_varying = TRUE;
        material->some_parameter_temp_varying = TRUE;
        material->all_parameters_sharable = FALSE;
      }
    };
    read_material_prop_lambda(new_material->density, "density", "Density", 0);
    read_material_prop_lambda(new_material->specific_heat, "specific_heat", "SpecificHeat", 0);
    ccDOTIMES(i,3) {
      read_material_prop_lambda(new_material->conductivity[i], "conductivity", "ThermalConductivity", i);
    }
    

    // Need to scale conductivity to the correct enthalpy unit basis for cdi
    // versions <= 9.4 (this may change depending on other branches in development)
    if ((sim.cdi_major_version == CONDUCTION_KAPPA_UNITS_CHANGE_MAJOR_VERSION
        && sim.cdi_minor_version <= CONDUCTION_KAPPA_UNITS_CHANGE_MINOR_VERSION)
        || sim.cdi_major_version < CONDUCTION_KAPPA_UNITS_CHANGE_MAJOR_VERSION) {
      // Old Units = LatticePower/(LatticeLength*LatticeTemperature)
      // New Units = LatticeSpecificEnthalpy*LatticeMass/(LatticeTime*LatticeLength*LatticeTemperature)
      // Conversion = LatticePower -> LatticeSpecificEnthalpy*LatticeMass/LatticeTime
      dFLOAT scale_conductivity_slope;
      dFLOAT scale_conductivity_offset;
      find_units_conversion_coefficients("LatticePower/LatticeLength/LatticeTemperature",
        "LatticeSpecificEnthalpy*LatticeLength*LatticeVelocity*LatticeDensity/LatticeTemperature",
        &scale_conductivity_slope, &scale_conductivity_offset);
      ccDOTIMES(i,3) {
        new_material->conductivity[i].value *= scale_conductivity_slope;
      }
    }

    lgi_read(stream, new_material->is_isotropy);
    lgi_read(stream, new_material->anisotropy_type);
    
    parameters->conduction_materials.push_back(new_material);

    parameters->num_materials++;

  }

  //store scale_conductivity from W/mK to LatticeThermalCond to scale 
  dFLOAT scale_conductivity;
  dFLOAT offset;
  find_thermal_units_conversion_coefficients("W/(m*degK)", "LatticeThermalConductivity", &scale_conductivity, &offset);
  sim.scale_conductivity = scale_conductivity * g_density_scale_factor;

}

//Convert units of hardwired properties used by the conduction solver
VOID convert_conduction_sim_info_units()
{
  dFLOAT scale_slope;
  dFLOAT scale_offset;
  
  find_units_conversion_coefficients("1/degK","LatticeInvTemperature", &scale_slope, &scale_offset);
  g_conduction_sim_info.air_beta *= scale_slope;
  
  find_units_conversion_coefficients("m^2/sec","LatticeKinematicViscosity", &scale_slope, &scale_offset);
  g_conduction_sim_info.air_nu *= scale_slope;
  
  find_units_conversion_coefficients("J/(kg*degK)","LatticeSpecificEntropy", &scale_slope, &scale_offset);
  g_conduction_sim_info.air_cp *= scale_slope;
  
  find_units_conversion_coefficients("kg/m^3","LatticeDensity", &scale_slope, &scale_offset);
  g_conduction_sim_info.air_density *= scale_slope;
  
  find_units_conversion_coefficients("W/(m*degK)","LatticeThermalConductivity", &scale_slope, &scale_offset);
  g_conduction_sim_info.air_conductivity *= scale_slope;
  
  find_units_conversion_coefficients("m/sec^2","LatticeAcceleration", &scale_slope, &scale_offset);
  g_conduction_sim_info.gravity_mag *= scale_slope;
    
  g_conduction_sim_info.ra_num = g_conduction_sim_info.gravity_mag * g_conduction_sim_info.air_beta *
                                 g_conduction_sim_info.air_Pr / (g_conduction_sim_info.air_nu*g_conduction_sim_info.air_nu);

}

VOID sRADIATION_SURFACE_CONDITION::eval_space_and_table_varying_parameter_program (STP_GEOM_VARIABLE point[3],
                                                                                   STP_GEOM_VARIABLE normal[3],
                                                                                   EQN_ERROR_HANDLER error_handler) 
{
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  if (m_parameter_program) {
    eval_program(m_parameter_program, point, normal, timestep, powertherm_time);
    if (!m_emissivity.is_time_varying && m_emissivity.is_eqn)  {
      m_emissivity.value = std::clamp(extract_output_variable_value(m_parameter_program, &m_emissivity, "emissivity", error_handler, point), 0.0, 1.0);
    }
  }

  m_constant_parameter_in_need_of_eval = FALSE;
}

VOID read_radiation_surface_conditions(LGI_STREAM stream) {
  LGI_RADIATION_SURFACE_CONDITION_HEADER header;
  lgi_read_next_head(stream, header);

  sim.radiation_surface_conditions.reserve(header.n_surface_conditions+1);

  ccDOTIMES(surface_condition_index, header.n_surface_conditions) {

    asINT32 name_length;
    std::string name;
    lgi_read(stream, &name_length, sizeof(name_length));
    if (name_length > 1) lgi_read(g_lgi_stream, name, name_length);

    sRADIATION_SURFACE_CONDITION rad_surf_cond(name, g_case_program);

    LGI_PHYSICS_VARIABLE lgi_var;
    lgi_read(stream, lgi_var);

    auto parm = &rad_surf_cond.m_emissivity;
    parm->value = lgi_var.value;
    parm->is_eqn = parm->is_space_varying = parm->is_time_varying = FALSE;
    if (lgi_var.name_length > 0) {
      parm->name = new char[lgi_var.name_length+1]{};
      lgi_read(stream, (void *)parm->name, lgi_var.name_length);
      if (g_case_program == NULL)
        msg_internal_error("Variable \"%s\" referenced in CDI file, but no equations"
                           " are present in CDI file.", parm->name);
      asINT32 prog_index;
      output_variable_conversion_coefficients_and_index(parm->name,
                                                        "Dimensionless",
                                                        &prog_index,
                                                        &parm->conversion_slope,
                                                        &parm->conversion_offset);
      auINT32 dmask = exprlang_output_variable_dependence_mask(g_case_program,
                                                               prog_index);
      parm->is_eqn = TRUE; 
      parm->is_space_varying = (dmask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
      parm->is_time_varying = (dmask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
      if (parm->is_time_varying) {
        msg_error("Time-varying emissivity for radiation condition \"%s\" is not currently supported.",rad_surf_cond.m_name.c_str());
      }
      if (parm->is_time_varying && parm->is_space_varying)
        rad_surf_cond.m_parameter_time_and_space_varying = TRUE;
      if (parm->is_time_varying && !parm->is_space_varying)
        rad_surf_cond.m_parameter_time_varying_only = TRUE;
      if (!parm->is_time_varying && !parm->is_space_varying && parm->is_eqn)
        rad_surf_cond.m_constant_parameter_in_need_of_eval = TRUE;
      if (!parm->is_sharable())
        rad_surf_cond.m_parameter_sharable = FALSE;
      parm->program_index = prog_index;

    }
    sim.radiation_surface_conditions.push_back(rad_surf_cond);
  }

  sRADIATION_SURFACE_CONDITION auto_black_body_rad_surf_cond("auto_gen_blackbody_surface_condition", g_case_program);
  auto_black_body_rad_surf_cond.m_emissivity.value = 1.0f;
  sim.radiation_surface_conditions.push_back(auto_black_body_rad_surf_cond);

}

VOID read_global_ref_frame(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_NON_INERTIAL_FRAME_TAG) {
    return;
  }

  sim.grf_physics_desc = cnew sGRF_PHYSICS_DESCRIPTOR;

  sim.grf_physics_desc->m_type_index = GRF_PHYSICS_DESCRIPTOR_TYPE;
  read_physics_descriptor(stream, sim.grf_physics_desc, 0, TRUE);
  sim.grf_physics_desc->check_consistency();

  GRF_PARAMETERS parms = sim.grf_physics_desc->parameters();

  BOOLEAN is_angular_vel_time_varying = (!parms->angular_vel[0].is_constant()
                                         || !parms->angular_vel[1].is_constant()
                                         || !parms->angular_vel[2].is_constant());

  BOOLEAN is_ref_vel_time_varying = (!parms->ref_vel[0].is_constant()
                                     || !parms->ref_vel[1].is_constant()
                                     || !parms->ref_vel[2].is_constant());

  BOOLEAN is_ref_accel_time_varying = (!parms->ref_accel[0].is_constant()
                                       || !parms->ref_accel[1].is_constant()
                                       || !parms->ref_accel[2].is_constant());

  if (is_ref_accel_time_varying
      || (parms->ref_accel[0].value != 0)
      || (parms->ref_accel[1].value != 0)
      || (parms->ref_accel[2].value != 0)
      || (parms->ref_vel_t0[0].value != 0) // Can't be fcn of time
      || (parms->ref_vel_t0[1].value != 0)
      || (parms->ref_vel_t0[2].value != 0)
      || is_angular_vel_time_varying
      || (parms->angular_vel[0].value != 0)
      || (parms->angular_vel[1].value != 0)
      || (parms->angular_vel[2].value != 0)
      || is_ref_vel_time_varying
      || (parms->ref_vel[0].value != 0)
      || (parms->ref_vel[1].value != 0)
      || (parms->ref_vel[2].value != 0)) {
    sim.grf.is_defined = TRUE;
  } else {
    return;
  }

  asINT32 csys_index = parms->coord_sys.value;
  CSYS csys = NULL;
  if (is_csys_index_non_default(csys_index))
    csys = &sim.csys_table[csys_index];

  sim.grf_physics_desc->is_angular_vel_time_varying = is_angular_vel_time_varying;
  sim.grf_physics_desc->is_ref_vel_time_varying = is_ref_vel_time_varying;
  sim.grf_physics_desc->is_ref_accel_time_varying = is_ref_accel_time_varying;

  if (sim.grf_physics_desc->some_parameter_time_varying)
    sim.grf_physics_desc->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, global_eqn_error_handler);

  // Ref point must be constant
  sim.grf.ref_point[0] = parms->ref_point[0].cvalue();
  sim.grf.ref_point[1] = parms->ref_point[1].cvalue();
  sim.grf.ref_point[2] = parms->ref_point[2].cvalue();

  if (!CDI_VERSION_AT_LEAST(sim.cdi_major_version, sim.cdi_minor_version, 3, 11)) {
    // In old CDI files, the ref point was erroneously shifted by the origin of
    // the default csys (PR 10913)
    vsub(sim.grf.ref_point, sim.grf.ref_point, sim.case_origin);
  }

  if (csys != NULL) {
    // Transform point from local csys to default csys
    xform_point(sim.grf.ref_point, csys);
  }

  // Transform point from default csys to lattice csys
  vadd(sim.grf.ref_point, sim.grf.ref_point, sim.case_origin);

  dFLOAT timestep_per_lat_time_inc = 1.0 / g_adv_fraction;

  dFLOAT grf_angular_vel[3];
  grf_angular_vel[0] = parms->angular_vel[0].value;
  grf_angular_vel[1] = parms->angular_vel[1].value;
  grf_angular_vel[2] = parms->angular_vel[2].value;
  if (csys != NULL) xform_vector(grf_angular_vel, csys);

  if (is_angular_vel_time_varying
      || (parms->angular_vel[0].value != 0)
      || (parms->angular_vel[1].value != 0)
      || (parms->angular_vel[2].value != 0)) {
    sim.grf.is_rotation = TRUE;
  }

  dFLOAT du[3];         // dU/dt
  dFLOAT grf_domega[3];
  if (is_ref_accel_time_varying
      || (parms->ref_accel[0].value != 0) || (parms->ref_accel[1].value != 0) || (parms->ref_accel[2].value != 0)
      || (parms->ref_vel_t0[0].value != 0) || (parms->ref_vel_t0[1].value != 0) || (parms->ref_vel_t0[2].value != 0)) {
    // User specified ref point acceleration
    sim.grf_physics_desc->is_motion_via_accel = TRUE;
    du[0] = parms->ref_accel[0].value;
    du[1] = parms->ref_accel[1].value;
    du[2] = parms->ref_accel[2].value;
    if (csys != NULL) xform_vector(du, csys);

    sim.grf.ref_pt_vel[0] = parms->ref_vel_t0[0].value;
    sim.grf.ref_pt_vel[1] = parms->ref_vel_t0[1].value;
    sim.grf.ref_pt_vel[2] = parms->ref_vel_t0[2].value;
    if (csys != NULL) xform_vector(sim.grf.ref_pt_vel, csys);

    if (sim.grf_physics_desc->some_parameter_time_varying)
      sim.grf_physics_desc->eval_time_varying_only_parameter_program(g_timescale.next_time(), g_timescale.m_powertherm_time, global_eqn_error_handler);

    sim.grf.angular_vel1[0] = parms->angular_vel[0].value;
    sim.grf.angular_vel1[1] = parms->angular_vel[1].value;
    sim.grf.angular_vel1[2] = parms->angular_vel[2].value;
    if (csys != NULL) xform_vector(sim.grf.angular_vel1, csys);

    sim.grf.linear_accel1[0] = parms->ref_accel[0].value;
    sim.grf.linear_accel1[1] = parms->ref_accel[1].value;
    sim.grf.linear_accel1[2] = parms->ref_accel[2].value;
    if (csys != NULL) xform_vector(sim.grf.linear_accel1, csys);

    if (g_timescale.m_time > 0) {
      // Restore from checkpoint
      sim.grf_physics_desc->eval_time_varying_only_parameter_program(g_timescale.m_time - 1, g_timescale.m_powertherm_time, global_eqn_error_handler);

      dFLOAT angular_vel_m1[3];
      angular_vel_m1[0] = parms->angular_vel[0].value;
      angular_vel_m1[1] = parms->angular_vel[1].value;
      angular_vel_m1[2] = parms->angular_vel[2].value;
      if (csys != NULL) xform_vector(angular_vel_m1, csys);

      // The subtract in the next line is shorthand for:
      //
      //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     rad/LatticeTimeInc^2
      //
      grf_domega[0] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[0] - angular_vel_m1[0]);
      grf_domega[1] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[1] - angular_vel_m1[1]);
      grf_domega[2] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[2] - angular_vel_m1[2]);
    } else {
      // See the comment on the then arm of this conditional for an explanation of the multiply by timestep_per_lat_time_inc.
      grf_domega[0] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[0] - grf_angular_vel[0]);
      grf_domega[1] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[1] - grf_angular_vel[1]);
      grf_domega[2] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[2] - grf_angular_vel[2]);
    }
  }
  else {
    // User specified ref point velocity
    sim.grf.ref_pt_vel[0] = parms->ref_vel[0].value;
    sim.grf.ref_pt_vel[1] = parms->ref_vel[1].value;
    sim.grf.ref_pt_vel[2] = parms->ref_vel[2].value;
    if (csys != NULL) xform_vector(sim.grf.ref_pt_vel, csys);

    if (sim.grf_physics_desc->some_parameter_time_varying)
      sim.grf_physics_desc->eval_time_varying_only_parameter_program(g_timescale.next_time(), g_timescale.m_powertherm_time, global_eqn_error_handler);

    sim.grf.ref_pt_vel1[0] = parms->ref_vel[0].value;
    sim.grf.ref_pt_vel1[1] = parms->ref_vel[1].value;
    sim.grf.ref_pt_vel1[2] = parms->ref_vel[2].value;
    if (csys != NULL) xform_vector(sim.grf.ref_pt_vel1, csys);

    sim.grf.angular_vel1[0] = parms->angular_vel[0].value;
    sim.grf.angular_vel1[1] = parms->angular_vel[1].value;
    sim.grf.angular_vel1[2] = parms->angular_vel[2].value;
    if (csys != NULL) xform_vector(sim.grf.angular_vel1, csys);

    if (g_timescale.m_time > 0) {
      // Seeded case
      sim.grf_physics_desc->eval_time_varying_only_parameter_program(g_timescale.m_time - 1, g_timescale.m_powertherm_time, global_eqn_error_handler);

      dFLOAT ref_vel_m1[3];
      ref_vel_m1[0] = parms->ref_vel[0].value;
      ref_vel_m1[1] = parms->ref_vel[1].value;
      ref_vel_m1[2] = parms->ref_vel[2].value;
      if (csys != NULL) xform_vector(ref_vel_m1, csys);

      // The subtract in the next line is shorthand for:
      //
      //     (U cell/LatticeTimeInc - V cell/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     cell/LatticeTimeInc^2
      //
      du[0] = 0.5 * timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[0] - ref_vel_m1[0]);
      du[1] = 0.5 * timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[1] - ref_vel_m1[1]);
      du[2] = 0.5 * timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[2] - ref_vel_m1[2]);

      dFLOAT angular_vel_m1[3];
      angular_vel_m1[0] = parms->angular_vel[0].value;
      angular_vel_m1[1] = parms->angular_vel[1].value;
      angular_vel_m1[2] = parms->angular_vel[2].value;
      if (csys != NULL) xform_vector(angular_vel_m1, csys);

      // The subtract in the next line is shorthand for:
      //
      //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     rad/LatticeTimeInc^2
      //
      grf_domega[0] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[0] - angular_vel_m1[0]);
      grf_domega[1] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[1] - angular_vel_m1[1]);
      grf_domega[2] = 0.5 * timestep_per_lat_time_inc * (sim.grf.angular_vel1[2] - angular_vel_m1[2]);
    } else {
      // The subtract in the next line is shorthand for:
      //
      //     (U cell/LatticeTimeInc - V cell/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     cell/LatticeTimeInc^2
      //
      du[0] = timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[0] - sim.grf.ref_pt_vel[0]);
      du[1] = timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[1] - sim.grf.ref_pt_vel[1]);
      du[2] = timestep_per_lat_time_inc * (sim.grf.ref_pt_vel1[2] - sim.grf.ref_pt_vel[2]);

      // The subtract in the next line is shorthand for:
      //
      //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
      //
      // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
      //
      //     rad/LatticeTimeInc^2
      //
      grf_domega[0] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[0] - grf_angular_vel[0]);
      grf_domega[1] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[1] - grf_angular_vel[1]);
      grf_domega[2] = timestep_per_lat_time_inc * (sim.grf.angular_vel1[2] - grf_angular_vel[2]);
    }
  }

  // A = dU/dt - U x omega
  // (U x omega) has units of LatticeVelocity/timestep (just like du)
  dFLOAT cross[3]; vcross(cross, sim.grf.ref_pt_vel, grf_angular_vel);
  sim.grf.linear_accel[0] = du[0] - cross[0];
  sim.grf.linear_accel[1] = du[1] - cross[1];
  sim.grf.linear_accel[2] = du[2] - cross[2];

  vcopy(sim.grf.linear_accel_m1, sim.grf.linear_accel);

  sim.grf.angular_vel[0] = grf_angular_vel[0];
  sim.grf.angular_vel[1] = grf_angular_vel[1];
  sim.grf.angular_vel[2] = grf_angular_vel[2];

  dFLOAT omega = sqrt(vdot(grf_angular_vel, grf_angular_vel));
  if (omega != 0) {
    dFLOAT a[3];
    vcopy(a, grf_angular_vel);
    vscale(a, 1/omega, a);      // unitize
    vcopy(sim.grf.last_axis, a);

    dFLOAT ref_pt_vel_par[3]; // component of ref pt vel parallel to angular vel
    calculate_point_on_rotation_axis(sim.grf.ref_point, sim.grf.ref_pt_vel, sim.grf.last_axis,
                                     sim.grf.angular_vel, sim.grf.last_pt_on_axis, ref_pt_vel_par);
  }

  dFLOAT d[3];  // velocity direction - unit vector
  vcopy(d, sim.grf.ref_pt_vel);
  dFLOAT vmag = sqrt(vdot(d,d));
  if (vmag != 0) {
    vscale(d, 1/vmag, d);
    vcopy(sim.grf.last_ref_vel_dir, d);
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    sim.grf.domega[scale][0] = grf_domega[0];
    sim.grf.domega[scale][1] = grf_domega[1];
    sim.grf.domega[scale][2] = grf_domega[2];
  }

  sim.grf.is_time_varying = (sim.grf.is_defined
                             && (sim.grf_physics_desc->is_angular_vel_time_varying
                                 // we assume this implies ref pt vel is time-varying
                                 || sim.grf_physics_desc->is_motion_via_accel
                                 || sim.grf_physics_desc->is_ref_vel_time_varying));
}



VOID read_lrf_physics_descriptors(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_LRF_PHYSICS_DESCRIPTORS_TAG) {
    return;
  }

  dFLOAT timestep_per_lat_time_inc = 1.0 / g_adv_fraction;

  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  sim.lrf_physics_descs = new sLRF_PHYSICS_DESCRIPTOR[header.n_physics_descs];
  sim.n_lrf_physics_descs = header.n_physics_descs;

  if (sim.n_lrf_physics_descs > 0) {
    if (sim.n_lrf_physics_descs > MAX_LOCAL_REF_FRAMES)
      msg_internal_error("Number of local rotating reference frames (%d) exceeds maximum of %d",
                         sim.n_lrf_physics_descs, MAX_LOCAL_REF_FRAMES);

    LRF_PHYSICS_DESCRIPTOR lrf = sim.lrf_physics_descs;
    for (asINT32 i=0; i<sim.n_lrf_physics_descs; i++, lrf++) {
      lrf->m_type_index = LRF_PHYSICS_DESCRIPTOR_TYPE;
      read_physics_descriptor(stream, lrf, i, FALSE);

      if (i == 0)
        lrf->check_consistency();       // only need to check the first

      LRF_PARAMETERS parms = lrf->parameters();
      lrf->is_angular_vel_time_varying = !parms->angular_vel.is_constant();
      lrf->is_angular_vel_non_zero = lrf->is_angular_vel_time_varying || (parms->angular_vel.value != 0);
      lrf->is_rotational_dynamics_on = parms->is_velocity_via_torque.value;
      // SP 0 checkpoints all LRFs so the LRF must be active on SP 0
      lrf->is_active = (my_proc_id == 0);

      if (!parms->start_point[0].is_constant() || !parms->start_point[1].is_constant() || !parms->start_point[2].is_constant()
          || !parms->end_point[0].is_constant() || !parms->end_point[1].is_constant() || !parms->end_point[2].is_constant())
        msg_internal_error("The axis of LRF %s is space or time dependent.", lrf->name);

      lrf->axis[0] = parms->end_point[0].value - parms->start_point[0].value;
      lrf->axis[1] = parms->end_point[1].value - parms->start_point[1].value;
      lrf->axis[2] = parms->end_point[2].value - parms->start_point[2].value;

      dFLOAT one_over_axis_mag = 1.0 / sqrt(vdot(lrf->axis, lrf->axis));
      lrf->axis[0] *= one_over_axis_mag;
      lrf->axis[1] *= one_over_axis_mag;
      lrf->axis[2] *= one_over_axis_mag;

      lrf->point[0] = parms->start_point[0].value;
      lrf->point[1] = parms->start_point[1].value;
      lrf->point[2] = parms->start_point[2].value;

      lrf->is_mlrf_on = parms->type.value == CDI_SLIDING_LRF;
      lrf->containing_lrf_index = SRI_GLOBAL_REF_FRAME_INDEX;    //default to global ref. frame
      lrf->initial_angle_rotated = 0.0;
      lrf->angle_rotated         = 0.0;
      lrf->initial_n_revolutions = 0;
      lrf->n_revolutions = 0;

      memset(lrf->local_to_global_rotation_matrix, 0, 9 * sizeof(lrf->local_to_global_rotation_matrix[0][0]));
      memset(lrf->local_to_containing_rotation_matrix, 0, 9 * sizeof(lrf->local_to_containing_rotation_matrix[0][0]));
      memset(lrf->containing_to_local_rotation_matrix, 0, 9 * sizeof(lrf->containing_to_local_rotation_matrix[0][0]));
      memset(lrf->local_to_containing_rotation_matrix1, 0, 9 * sizeof(lrf->local_to_containing_rotation_matrix1[0][0]));
      memset(lrf->containing_to_local_rotation_matrix1, 0, 9 * sizeof(lrf->containing_to_local_rotation_matrix1[0][0]));

      ccDOTIMES(i, 3) {
        lrf->local_to_global_rotation_matrix[i][i] = 1.0;
        lrf->local_to_containing_rotation_matrix[i][i] = 1.0;
        lrf->local_to_containing_rotation_matrix1[i][i] = 1.0;
        lrf->containing_to_local_rotation_matrix[i][i] = 1.0;
        lrf->containing_to_local_rotation_matrix1[i][i] = 1.0;
      }
 
      lrf->n_scales = 0;
      if (lrf->is_angular_vel_time_varying) {
        lrf->n_scales = sim.num_scales;
        lrf->domega = xnew dFLOAT [ sim.num_scales ];
        lrf->domega_total = xnew dFLOAT [3 * sim.num_scales];
        lrf->domega_linear_accel = xnew dFLOAT [3 * sim.num_scales];
        lrf->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, global_eqn_error_handler);
        lrf->omega = parms->angular_vel.value;

        dFLOAT domega;
        if (g_timescale.m_time > 0) {
          lrf->eval_time_varying_only_parameter_program(g_timescale.m_time - 1, g_timescale.m_powertherm_time, global_eqn_error_handler);
          dFLOAT omega_m1 = parms->angular_vel.value;
          lrf->eval_time_varying_only_parameter_program(g_timescale.next_time(), g_timescale.m_powertherm_time, global_eqn_error_handler);
          lrf->omega1 = parms->angular_vel.value;
          // The subtract in the next line is shorthand for:
          //
          //     (U rad/LatticeTimeInc - V rad/LatticeTimeInc) / (1 timestep)
          //
          // Hence the need to multiply by timestep_per_lat_time_inc to convert to:
          //
          //     rad/LatticeTimeInc^2
          //
          domega = 0.5 * timestep_per_lat_time_inc * (lrf->omega1 - omega_m1);
        } else {
          lrf->eval_time_varying_only_parameter_program(g_timescale.next_time(), g_timescale.m_powertherm_time, global_eqn_error_handler);
          lrf->omega1 = parms->angular_vel.value;
          // The comment on the then side of this conditional explains the
          // multiply by timestep_per_lat_time_inc.
          domega = timestep_per_lat_time_inc * (lrf->omega1 - lrf->omega);
        }

        // Is this necessary?
        DO_SCALES_FINE_TO_COARSE(scale) {
          lrf->domega[scale] = domega;
          if (lrf->domega_total) {
            dFLOAT *domega_total = &lrf->domega_total[3 * scale];
            vcopy(domega_total, lrf->axis);
            vscale(domega_total, domega, domega_total);
          }
          if (lrf->domega_linear_accel)
            memset(&lrf->domega_linear_accel[3 * scale], 0, 3 * sizeof(lrf->domega_linear_accel[0]));
        }
      } else if (lrf->is_rotational_dynamics_on) {
        lrf->n_scales = sim.num_scales;
        lrf->domega = xnew dFLOAT [ sim.num_scales ];
        lrf->domega_total = xnew dFLOAT [3 * sim.num_scales];
        lrf->domega_linear_accel = xnew dFLOAT [3 * sim.num_scales];
        DO_SCALES_FINE_TO_COARSE(scale) {
          lrf->domega[scale] = 0.0;
          if (lrf->domega_total) {
            dFLOAT *domega_total = &lrf->domega_total[3 * scale];
            vcopy(domega_total, lrf->axis);
            vscale(domega_total, lrf->domega[scale], domega_total);
          }
          if (lrf->domega_linear_accel)
            memset(&lrf->domega_linear_accel[3 * scale], 0, 3 * sizeof(lrf->domega_linear_accel[0]));
        }
      } else {
        lrf->domega = NULL;
        lrf->domega_total = NULL;
        lrf->domega_linear_accel = NULL;
        lrf->omega = parms->angular_vel.value;
        lrf->omega1 = lrf->omega;
      }

      vscale(lrf->angular_vel, lrf->omega, lrf->axis);

    }
  }
}
dFLOAT eval_time_varying_only_parameter_program_for_uds(BASETIME timestep,
                                                        sFLOAT powertherm_time,
                                                         STP_GEOM_VARIABLE normal[3],
                                                         EXPRLANG_PROGRAM program,
                                                         PHYSICS_VARIABLE  var)
{
  CHARACTER err_buffer[128];
  STP_GEOM_VARIABLE point[3] = {0.0};
  eval_program(program, point, normal, timestep, powertherm_time);
  EXPRLANG_VALUE value = exprlang_get_output_variable_value(program, var->program_index);
  
  if (value == NULL) {
    msg_internal_error("Unable to extract variable \"%s\" from case equations.", var->name);
    return 0;
  } else {
    dFLOAT converted_value =
      exprlang_uval_value_value(value) * var->conversion_slope + var->conversion_offset;
    return converted_value;
  }
}

dFLOAT eval_space_varying_only_parameter_program_for_uds(STP_GEOM_VARIABLE point[3],
                                                         STP_GEOM_VARIABLE normal[3],
                                                         EXPRLANG_PROGRAM program,
                                                         PHYSICS_VARIABLE  var)
{
  CHARACTER err_buffer[128];
  BASETIME timestep = 0;
  sFLOAT  powertherm_time = 0.0;
  eval_program(program, point, normal, timestep, powertherm_time);
  EXPRLANG_VALUE value = exprlang_get_output_variable_value(program, var->program_index);
  
  if (value == NULL) {
    msg_internal_error("Unable to extract variable \"%s\" from case equations.", var->name);
    return 0;
  } else {
    dFLOAT converted_value =
      exprlang_uval_value_value(value) * var->conversion_slope + var->conversion_offset;
    return converted_value;
  }
}

dFLOAT eval_space_and_time_varying_parameter_program_for_uds(STP_GEOM_VARIABLE point[3],
                                                             STP_GEOM_VARIABLE normal[3],
                                                             BASETIME timestep,
                                                             sFLOAT powertherm_time,
                                                             EXPRLANG_PROGRAM program,
                                                             PHYSICS_VARIABLE  var)
{
  CHARACTER err_buffer[128];
  eval_program(program, point, normal, timestep, powertherm_time);
  EXPRLANG_VALUE value = exprlang_get_output_variable_value(program, var->program_index);
  
  if (value == NULL) {
    msg_internal_error("Unable to extract variable \"%s\" from case equations.", var->name);
    return 0;
  } else {
    dFLOAT converted_value =
      exprlang_uval_value_value(value) * var->conversion_slope + var->conversion_offset;
    return converted_value;
  }
}



VOID read_fluid_physics_descriptors(LGI_STREAM stream)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  sim.volume_physics_descs         = cnew sPHYSICS_DESCRIPTOR [ sim.n_volume_physics_descs ];
  sim.n_fluid_physics_descs        = header.n_physics_descs;
  if (sim.n_fluid_physics_descs > sim.n_volume_physics_descs)
    // some unused descriptors at end of CDI FLUD chunk 
    sim.n_fluid_physics_descs = sim.n_volume_physics_descs;

  sim.user_specified_dns_nu_over_t = (header.flags & LGI_USER_SPECIFIED_DNS_NU_OVER_T) != 0;

  ccDOTIMES(i, sim.n_fluid_physics_descs) {
    sim.volume_physics_descs[i].m_type_index = FLUID_PHYSICS_DESCRIPTOR_TYPE;
    read_physics_descriptor(stream, sim.volume_physics_descs + i, i, FALSE);
  }
}

VOID read_scas_chunk(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_SCAS_TAG) {
    return;
  }

  LGI_SCAS_HEADER header;
  lgi_read_next_head(stream, header);

  g_region_to_material_map.clear();
  
  ccDOTIMES(i, header.n_assignments) {
    asINT32 region_id, material_index;
    lgi_read(stream, &region_id, sizeof(region_id));
    lgi_read(stream, &material_index, sizeof(material_index));
    g_region_to_material_map[region_id] = material_index;
  }
}

// Helper function to get material index for a region
asINT32 get_scas_material_index(asINT32 region_id)
{
  auto it = g_region_to_material_map.find(region_id);
  return it != g_region_to_material_map.end() ? it->second : -1;
}

VOID read_hcsd_physics_descriptors(LGI_STREAM stream)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  // Compute and store conversion from total imposed heat/unit volume to imposed heat in powerdensity units
  // Conversion = Old Units -> New Units (PowerDensity -> PowerDensity)
  // Old Units = LatticePower/LatticeVolume 
  // New Units = LatticePowerDensity (LatticeSpecificEnthalpy*LatticeDensity/LatticeTimeInc)
  dFLOAT slope;
  dFLOAT offset;
  find_units_conversion_coefficients("LatticePower/LatticeVolume", "LatticePowerDensity", &slope, &offset);

  // First read all descriptors normally
  asINT32 n_phys_descs_to_read = header.n_physics_descs;
  if ((sim.n_fluid_physics_descs + header.n_physics_descs) > sim.n_volume_physics_descs)
  {
    n_phys_descs_to_read = sim.n_volume_physics_descs - sim.n_fluid_physics_descs;
  }

  // Base index where HCSD descriptors start
  asINT32 hcsd_start_index = sim.n_fluid_physics_descs;

  ccDOTIMES(i, n_phys_descs_to_read)
  {
    read_physics_descriptor(stream, sim.volume_physics_descs + hcsd_start_index + i, i, FALSE);
  }

  // Count needed extra descriptors and collect material info
  asINT32 n_extra_descs = 0;
  std::vector<asINT32> descs_to_split; // Keep track of which descriptors need splitting

  // First pass: identify which descriptors need splitting
  for (asINT32 i = 0; i < n_phys_descs_to_read; i++)
  {
    PHYSICS_DESCRIPTOR pd = &sim.volume_physics_descs[hcsd_start_index + i];

    // Find material parameter if it exists
    asINT32 material_param_index = -1;
    ccDOTIMES(j, pd->n_parameters)
    {
      if (pd->phys_type_desc->continuous_dp_var[j]->id == CDI_VAR_ID_SOLID_MATERIAL &&
          pd->parameters()[j].value == -1)
      {
        material_param_index = j;
        break;
      }
    }

    if (material_param_index != -1)
    {
      // Count unique materials for regions using this descriptor
      std::set<asINT32> unique_materials;
      bool has_parts_with_material = false;

      // Scan through all parts to find ones using this descriptor
      ccDOTIMES(part_idx, g_num_parts)
      {
        if (g_cdi_volume_physics_desc_index_from_part_index[part_idx] == (hcsd_start_index + i))
        {
          asINT32 material_index = get_scas_material_index(part_idx);
          if (material_index != -1)
          {
            unique_materials.insert(material_index);
            has_parts_with_material = true;
          }
        }
      }

      // Only split if we have parts with valid materials AND multiple unique materials
      if (has_parts_with_material && unique_materials.size() > 1)
      {
        descs_to_split.push_back(i);
        n_extra_descs += unique_materials.size() - 1; // -1 because first material uses original descriptor
      }
      else
      {
        // Physics descriptor remains unsplit - either no parts have valid materials or only one unique material
        CONDUCTION_SOLID_PARAMETERS csp = (CONDUCTION_SOLID_PARAMETERS)pd->parameters();
        csp->scaled_volume_fraction.value = slope * 1.0;

        if (has_parts_with_material && unique_materials.size() == 1)
        {
          // Single material case - update the material index from -1 to the actual material index
          csp->material_index.value = *unique_materials.begin();
          msg_print(" unsplit pd %s updated material index to %g\n", pd->name, csp->material_index.value);
        }
        else if (!has_parts_with_material)
        {
          // No parts have valid material indices from SCAS chunk
          // This could happen if SCAS chunk is missing or incomplete
          // For safety, assign a default material index of 0 to prevent segfaults
          if (csp->material_index.value == -1)
          {
            csp->material_index.value = 0;
            msg_print(" unsplit pd %s: no SCAS material assignments found, defaulting material index to 0\n", pd->name);
          }
        }
      }
    }
    else { 
      // initialize the volume fraction used to compute power density from power for this physics descriptor if required
      CONDUCTION_SOLID_PARAMETERS csp = (CONDUCTION_SOLID_PARAMETERS)pd->parameters();
      // store the volume fraction scaled by the unit conversion from latticepower/latticevolume -> latticepowerdensity
      csp->scaled_volume_fraction.value = slope * 1.0;
    }
  }

  if (n_extra_descs > 0)
  {
    // Allocate new array with space for extra descriptors
    asINT32 new_total = hcsd_start_index + n_phys_descs_to_read + n_extra_descs;
    PHYSICS_DESCRIPTOR new_descs = cnew sPHYSICS_DESCRIPTOR[new_total];

    // Copy all existing descriptors (fluid + original HCSD), shallow copy of _parameters and _initial_conditions pointers
    memcpy(new_descs, sim.volume_physics_descs,
           (hcsd_start_index + n_phys_descs_to_read) * sizeof(sPHYSICS_DESCRIPTOR));

    // Process descriptors that need splitting
    asINT32 extra_idx = 0;
    for (auto orig_desc_idx : descs_to_split)
    {
      PHYSICS_DESCRIPTOR pd = &new_descs[hcsd_start_index + orig_desc_idx];

      // Find material parameter
      asINT32 material_param_index = -1;
      ccDOTIMES(j, pd->n_parameters)
      {
        if (pd->phys_type_desc->continuous_dp_var[j]->id == CDI_VAR_ID_SOLID_MATERIAL &&
            pd->parameters()[j].value == -1)
        {
          material_param_index = j;
          break;
        }
      }

      // Group regions by material
      std::map<asINT32, std::vector<asINT32>> material_to_parts;
      std::vector<asINT32> parts_without_material; // Track parts with material_index == -1

      ccDOTIMES(part_idx, g_num_parts)
      {
        if (g_cdi_volume_physics_desc_index_from_part_index[part_idx] == (hcsd_start_index + orig_desc_idx))
        {
          asINT32 material_index = get_scas_material_index(part_idx);
          if (material_index != -1)
          {
            material_to_parts[material_index].push_back(part_idx);
          }
          else
          {
            parts_without_material.push_back(part_idx);
          }
        }
      }

      // Calculate total volume for all regions in this descriptor (including those without material assignments)
      dFLOAT total_desc_volume = 0.0;
      for (const auto& [mat_idx, parts] : material_to_parts)
      {
        for (auto part_idx : parts)
        {
          total_desc_volume += g_part_total_volumes[part_idx];
        }
      }
      // Also include parts without material assignments
      for (auto part_idx : parts_without_material)
      {
        total_desc_volume += g_part_total_volumes[part_idx];
      }

      // Update original descriptor with first material's parts
      auto it = material_to_parts.begin();
      CONDUCTION_SOLID_PARAMETERS csp = (CONDUCTION_SOLID_PARAMETERS)pd->parameters();
      csp->material_index.value = it->first;
      msg_print(" old pd %s material index %g\n", pd->name, csp->material_index.value);
      dFLOAT first_material_volume = 0.0;

      for (auto part_idx : it->second)
      {
        first_material_volume += g_part_total_volumes[part_idx];
      }

      // Include parts without material assignments in the original descriptor
      for (auto part_idx : parts_without_material)
      {
        first_material_volume += g_part_total_volumes[part_idx];
      }

      // store the volume fraction scaled by the unit conversion from latticepower/latticevolume -> latticepowerdensity
      csp->scaled_volume_fraction.value = total_desc_volume > 0.0? slope * first_material_volume/total_desc_volume : 0.0;

      // imposed_heat value is not changed here since it could be an expression, so use the volume fraction to scale it at the point of evaluation

      for (auto part_idx : it->second)
      {
        g_cdi_volume_physics_desc_index_from_part_index[part_idx] = hcsd_start_index + orig_desc_idx;
      }

      // Assign parts without material to the original descriptor as well
      for (auto part_idx : parts_without_material)
      {
        g_cdi_volume_physics_desc_index_from_part_index[part_idx] = hcsd_start_index + orig_desc_idx;
      }

      ++it;

      // Create new descriptors for remaining materials
      for (; it != material_to_parts.end(); ++it)
      {
        PHYSICS_DESCRIPTOR new_pd = &new_descs[hcsd_start_index + n_phys_descs_to_read + extra_idx];

        // Copy descriptor and update material-specific info
        memcpy(new_pd, pd, sizeof(sPHYSICS_DESCRIPTOR));

        // Create new parameters array and deep copy, since the material_index parameter is different
        // Initial conditions ptr is shared
        PHYSICS_VARIABLE new_params = new sPHYSICS_VARIABLE[pd->n_parameters + 2];
        for (sINT16 i = 0; i < (pd->n_parameters+2); i++)
        {
          new_params[i] = pd->parameters()[i];
          if (pd->parameters()[i].name) // If the parameter is an eqn
          {
            new_params[i].name = strdup(pd->parameters()[i].name); // Deep copy the name string
          }
        }

        // Set the new parameters array
        new_pd->_parameters = new_params;

        // Calculate and store volume fraction for this material
        dFLOAT material_volume = 0.0;
        for (auto part_idx : it->second)
        {
          material_volume += g_part_total_volumes[part_idx];
        }
        CONDUCTION_SOLID_PARAMETERS new_csp = (CONDUCTION_SOLID_PARAMETERS)new_pd->parameters();
        new_csp->scaled_volume_fraction.value = slope * material_volume / total_desc_volume;
        // total_heat is already copied and remains unchanged
        
        // Update material index, should be safe now
        new_csp->material_index.value = it->first;
        msg_print(" new pd %s material index %g\n", new_pd->name, new_csp->material_index.value);

        // Update mapping for parts using this material
        for (auto part_idx : it->second)
        {
          g_cdi_volume_physics_desc_index_from_part_index[part_idx] =
              hcsd_start_index + n_phys_descs_to_read + extra_idx;
        }

        extra_idx++;
      }
    }

    // Update sim structure
    delete[] sim.volume_physics_descs;
    sim.volume_physics_descs = new_descs;
    sim.n_volume_physics_descs = new_total;
  }
  else
  {
    sim.n_volume_physics_descs = hcsd_start_index + n_phys_descs_to_read;
  }

  // compute total simulated volumes for all solid physics descriptors, which is used for heat input scaling if applicable
  asINT32 n_total_solid_descs = sim.n_volume_physics_descs - hcsd_start_index;
  for (asINT32 desc_idx = hcsd_start_index; desc_idx < hcsd_start_index + n_total_solid_descs; desc_idx++)
  {
    PHYSICS_DESCRIPTOR pd = &sim.volume_physics_descs[desc_idx];
    dFLOAT total_volume = 0.0;

    // Sum volumes of all parts assigned to this descriptor
    ccDOTIMES(part_idx, g_num_parts)
    {
      if (g_cdi_volume_physics_desc_index_from_part_index[part_idx] == desc_idx)
      {
        total_volume += g_part_total_volumes[part_idx];
      }
    }

    // Store the total volume in the descriptor's parameters
    ((CONDUCTION_SOLID_PARAMETERS)pd->parameters())->total_simulated_volume.value = total_volume;
    msg_print(" final pd %s material index %g\n", pd->name, ((CONDUCTION_SOLID_PARAMETERS)pd->parameters())->material_index.value);
  }

}

VOID read_shell_config_physics_descriptors(LGI_STREAM stream)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);
  sim.n_shell_config_physics_descs = header.n_physics_descs;
  sim.is_shell_conduction_model = TRUE;

  sim.shell_config_physics_descs         = cnew sSHELL_CONFIG_PHYSICS_DESCRIPTOR [ sim.n_shell_config_physics_descs ];
  ccDOTIMES(i, header.n_physics_descs) {
    read_physics_descriptor(stream, sim.shell_config_physics_descs + i, i, FALSE);
    sim.shell_config_physics_descs[i].set_if_initial_conditions_constant();
    sim.shell_config_physics_descs[i].m_type_index = SHELL_PHYSICS_DESCRIPTOR_TYPE;
  }
}

VOID read_movb_physics_descriptors(LGI_STREAM stream)
{
  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  sim.movb_physics_descs           = new sMOVB_PHYSICS_DESCRIPTOR[ header.n_physics_descs ];
  sim.n_movb_physics_descs         = header.n_physics_descs;

  MOVB_PHYSICS_DESCRIPTOR movb_physics_desc = sim.movb_physics_descs;
  ccDOTIMES(i, sim.n_movb_physics_descs) {
    movb_physics_desc->m_type_index = MOVB_PHYSICS_DESCRIPTOR_TYPE;
    read_physics_descriptor(stream, movb_physics_desc, i, FALSE);
    movb_physics_desc->angle_rotated = 0.0F;
    if (!movb_physics_desc->all_parameters_sharable)
      msg_internal_error("Space varying movb parameter for \"%s\" not supported", movb_physics_desc->name);

    asINT32 tire_index = movb_physics_desc->parameters()->deforming_tire_index.value;

    // we have to check to see if tires actually exist in this case.
    // old cdi files (which never had deforming tires) treated this 
    // as a boolean. As such, the value was always 0.
    if ( tire_index >= 0 && sim.n_tires > 0) {
      movb_physics_desc->tire = &sim.tires[tire_index];
    }
    else {
      movb_physics_desc->tire = nullptr;
    }

    movb_physics_desc++;
  }

}

static std::vector<sPHYSICS_DESCRIPTOR> flow_surface_phys_descs;
static std::vector<sPHYSICS_DESCRIPTOR> thermal_surface_phys_descs;

static asINT32 derive_solid_cdi_phys_type(asINT32 flow_cdi_phys_type, asINT32 &shell_configuration_offset)
{
  sSLIP_SURFEL_PARAMETERS slip_params;
  sNOSLIP_SURFEL_PARAMETERS noslip_params;
  asINT32 slip_shell_config_offset = &slip_params.shell_configuration - (PHYSICS_VARIABLE)&slip_params;
  asINT32 noslip_shell_config_offset = &noslip_params.shell_configuration - (PHYSICS_VARIABLE)&noslip_params;

  switch (flow_cdi_phys_type) {
  case CDI_PHYS_TYPE_SLIP95:
  case CDI_PHYS_TYPE_LINEAR_SLIP:
  case CDI_PHYS_TYPE_VEL_SLIP:
  case CDI_PHYS_TYPE_ANGULAR_SLIP:
    shell_configuration_offset = slip_shell_config_offset;
    return CDI_PHYS_TYPE_ADIABATIC;

  case CDI_PHYS_TYPE_TRUE_NOSLIP:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
    shell_configuration_offset = noslip_shell_config_offset;
    return CDI_PHYS_TYPE_ADIABATIC;

  case CDI_PHYS_TYPE_SLIP95_FIXED_TEMP:
  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP:
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP:
    shell_configuration_offset = slip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_TEMP;

  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP:
    shell_configuration_offset = noslip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_TEMP;
      
  case CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX:
  case CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX:
  case CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX:
    shell_configuration_offset = slip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_HEAT_FLUX;

  case CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX:
    shell_configuration_offset = noslip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_HEAT_FLUX;
      
  case CDI_PHYS_TYPE_ADIABATIC:
  case CDI_PHYS_TYPE_FIXED_TEMP:
  case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
  case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
  case CDI_PHYS_TYPE_CONTACT_RESISTANCE:
    shell_configuration_offset = -1;
    return flow_cdi_phys_type;

  case CDI_PHYS_TYPE_SLIP95_COUPLED:
  case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:
  case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:
    shell_configuration_offset = slip_shell_config_offset;
    return CDI_PHYS_TYPE_COUPLED_THERMAL;

  case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED:
    shell_configuration_offset = noslip_shell_config_offset;
    return CDI_PHYS_TYPE_COUPLED_THERMAL;

  case CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST:
  case CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST:
  case CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST:
    shell_configuration_offset = slip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP;

  case CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST:
  case CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST:
  case CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST:
    shell_configuration_offset = noslip_shell_config_offset;
    return CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP;

  default:
    return CDI_PHYS_TYPE_NONE;
  }
}

static VOID copy_param(sPHYSICS_VARIABLE &to, sPHYSICS_VARIABLE &from, PHYSICS_DESCRIPTOR to_pd)
{
  to = from;

  if (to.is_time_varying)
    to_pd->some_parameter_time_varying = TRUE;

  if (to.is_time_varying && to.is_space_varying)
    to_pd->some_parameter_time_and_space_varying = TRUE;

  if (to.is_time_varying && !to.is_space_varying)
    to_pd->some_parameter_time_varying_only = TRUE;

  if (!to.is_time_varying && !to.is_space_varying && to.is_eqn)
    to_pd->some_constant_parameter_in_need_of_eval = TRUE;

  if (!to.is_sharable())
    to_pd->all_parameters_sharable = FALSE;
}

// For old CDI files, this derives a set of solid surface physics descriptors from the fluid descriptors
static VOID derive_solid_surface_phys_descs()
{
  sim.n_thermal_surface_physics_descs = sim.n_flow_surface_physics_descs;
  sim.thermal_surface_physics_descs = cnew sPHYSICS_DESCRIPTOR [ sim.n_thermal_surface_physics_descs ];

  ccDOTIMES(i, sim.n_thermal_surface_physics_descs) {
    PHYSICS_DESCRIPTOR flow_pd = sim.flow_surface_physics_descs + i;
    PHYSICS_DESCRIPTOR solid_pd = sim.thermal_surface_physics_descs + i;
    
    asINT32 flow_cdi_phys_type = flow_pd->phys_type_desc->cdi_physics_type;
    asINT32 shell_config_offset;
    asINT32 solid_cdi_phys_type = derive_solid_cdi_phys_type(flow_cdi_phys_type, shell_config_offset);

    if (solid_cdi_phys_type == CDI_PHYS_TYPE_NONE)
      continue;

    solid_pd->name = flow_pd->name;
    solid_pd->parameter_program = flow_pd->parameter_program;
    solid_pd->init_from_coupling_model_p = FALSE;
    solid_pd->coupling_model_index = -1; 
    // solid_pd->boundary_seed_var_spec_index = -1;
    solid_pd->m_type_index = flow_pd->m_type_index;

    solid_pd->all_parameters_sharable = TRUE;
    solid_pd->some_parameter_time_varying = FALSE;
    solid_pd->some_parameter_time_and_space_varying = FALSE;
    solid_pd->some_parameter_time_varying_only = FALSE;
    solid_pd->some_constant_parameter_in_need_of_eval = FALSE;

    CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = cdi_lookup_physics(solid_cdi_phys_type);
    solid_pd->phys_type_desc = phys_type_desc;
    solid_pd->n_parameters = phys_type_desc->n_continuous_dp;

    PHYSICS_VARIABLE solid_params = cnew sPHYSICS_VARIABLE [ solid_pd->n_parameters ];
    solid_pd->_parameters = solid_params;
    PHYSICS_VARIABLE flow_params = flow_pd->parameters();
    // Thermal physics descriptors no longer have a ref frame variable
    // solid_params[0].value = flow_params[0].value; // This is CDI_VAR_ID_REF_FRAME, which previously was universally at the base of surface physics descriptors

    if (solid_cdi_phys_type == flow_cdi_phys_type) {
      ccDOTIMES(i, solid_pd->n_parameters)
        copy_param(solid_params[i], flow_params[i], solid_pd);
    } else {
      // The code here depends on the layout of parameters for each flow physics type in CDI/cdi_physics.cc
      switch (solid_cdi_phys_type) {
      case CDI_PHYS_TYPE_ADIABATIC: {
        CONDUCTION_ADIABATIC_SURFEL_PARAMETERS p = (CONDUCTION_ADIABATIC_SURFEL_PARAMETERS)solid_params;
        copy_param(p->shell_configuration, flow_params[shell_config_offset], solid_pd);  // CDI_VAR_ID_SHELL_CONFIGURATION
        p->radiation_surface_condition.value = -1;                                       // CDI_VAR_ID_RADIATION_SURFACE_COND
        ((CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR)solid_pd)->check_consistency();
        break;
      }
      case CDI_PHYS_TYPE_FIXED_TEMP: {
        CONDUCTION_FIXED_TEMP_SURFEL_PARAMETERS p = (CONDUCTION_FIXED_TEMP_SURFEL_PARAMETERS)solid_params;
        copy_param(p->shell_configuration, flow_params[shell_config_offset], solid_pd);  // CDI_VAR_ID_SHELL_CONFIGURATION
        p->radiation_surface_condition.value = -1;                                       // CDI_VAR_ID_RADIATION_SURFACE_COND
        copy_param(p->temperature, flow_params[flow_pd->n_parameters - 1], solid_pd);    // CDI_VAR_ID_TEMP
        ((CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)solid_pd)->check_consistency();
        break;
      }
      case CDI_PHYS_TYPE_FIXED_HEAT_FLOW: 
      case CDI_PHYS_TYPE_FIXED_HEAT_FLUX: {
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS p = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS)solid_params;
        copy_param(p->shell_configuration, flow_params[shell_config_offset], solid_pd);  // CDI_VAR_ID_SHELL_CONFIGURATION
        p->radiation_surface_condition.value = -1;                                       // CDI_VAR_ID_RADIATION_SURFACE_COND
        copy_param(p->heat_flux_1, flow_params[flow_pd->n_parameters - 1], solid_pd);    // CDI_VAR_ID_WALL_HEAT_FLUX (into solid)
        copy_param(p->heat_flux_2, flow_params[flow_pd->n_parameters - 1], solid_pd);    // CDI_VAR_ID_HEAT_FLUX_2 (into fluid)
        ((CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)solid_pd)->check_consistency();
        break;
      }
      case CDI_PHYS_TYPE_COUPLED_THERMAL: {
        CONDUCTION_COUPLED_THERMAL_SURFEL_PARAMETERS p = (CONDUCTION_COUPLED_THERMAL_SURFEL_PARAMETERS)solid_params;
        copy_param(p->shell_configuration, flow_params[shell_config_offset], solid_pd);  // CDI_VAR_ID_SHELL_CONFIGURATION
        p->radiation_surface_condition.value = -1;  // CDI_VAR_ID_RADIATION_SURFACE_COND
        ((CONDUCTION_COUPLED_THERMAL_SURFEL_PHYSICS_DESCRIPTOR)solid_pd)->check_consistency();
        break;
      }
      case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP: {
        CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS p = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PARAMETERS)solid_params;
        copy_param(p->shell_configuration, flow_params[flow_pd->n_parameters - shell_config_offset], solid_pd);  // CDI_VAR_ID_SHELL_CONFIGURATION
        p->radiation_surface_condition.value = -1;                                             // CDI_VAR_ID_RADIATION_SURFACE_COND
        copy_param(p->ambient_temp, flow_params[flow_pd->n_parameters - 4], solid_pd);         // CDI_VAR_ID_TEMP
        copy_param(p->ext_heat_xfer_coeff, flow_params[flow_pd->n_parameters - 3], solid_pd);  // CDI_VAR_ID_EXT_HEAT_XFER_COEFF
        ((CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)solid_pd)->check_consistency();
        break;
      }
      default:
        break;
      }
    }
  }
}

VOID calculate_heat_flow_face_areas()
{
  // Skip if there are no thermal physics descriptors
  if (thermal_surface_phys_descs.size() == 0) {
    return;
  }

  // For each face, accumulate areas for heat flow BCs
  for (asINT32 face_idx = 0; face_idx < g_num_faces; face_idx++) {
    // Get the face area
    dFLOAT face_area = g_flow_bc_total_areas[face_idx];

    // Get the thermal physics descriptor indices for this face
    asINT32 front_thermal_pd_idx = g_cdi_front_thermal_surface_physics_desc_index_from_face_index[face_idx];
    asINT32 back_thermal_pd_idx = g_cdi_back_thermal_surface_physics_desc_index_from_face_index[face_idx];

    // Process front thermal BC
    if (front_thermal_pd_idx >= 0) {
      PHYSICS_DESCRIPTOR pd = &thermal_surface_phys_descs[front_thermal_pd_idx];
      if (pd->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX)
      {
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR heat_flux_pd =
            (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)pd;
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS params = heat_flux_pd->parameters();

        if (params->is_heat_flow.value > 0) { // params->face_area should have been initialized to 0 in init_physics_desc
          params->face_area.value = face_area; 
        }
      }
    } 

    if (back_thermal_pd_idx >= 0) { // Process back thermal BC
      PHYSICS_DESCRIPTOR pd = &thermal_surface_phys_descs[back_thermal_pd_idx];
      if (pd->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX)
      {
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR heat_flux_pd =
            (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)pd;
        CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PARAMETERS params = heat_flux_pd->parameters();

        if (params->is_heat_flow.value > 0) { // params->face_area should have been initialized to 0 in init_physics_desc
          params->face_area.value = face_area; 
        }
      }
    }
  }

}

static VOID merge_wall_and_thermal_surface_phys_desc(asINT32 flow_phys_desc_index, asINT32 thermal_phys_desc_index) 
{
  PHYSICS_DESCRIPTOR wall_surface_phys_desc    = &flow_surface_phys_descs[flow_phys_desc_index];
  PHYSICS_DESCRIPTOR thermal_surface_phys_desc = &thermal_surface_phys_descs[thermal_phys_desc_index];

  asINT32 thermal_cdi_phys_type = thermal_surface_phys_desc->phys_type_desc->cdi_physics_type;
  asINT32 wall_cdi_phys_type    = wall_surface_phys_desc->phys_type_desc->cdi_physics_type;

  switch (thermal_cdi_phys_type) {
  case CDI_PHYS_TYPE_COUPLED_THERMAL: {
    asINT32 augmented_wall_cdi_phys_type = -1;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95_COUPLED; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED; break;

    // no need to augment wall_surface_phys_desc with added parameters
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: 
      break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    if (augmented_wall_cdi_phys_type >= 0)
      register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                              0, NULL,
                                              flow_surface_phys_descs);
    break;
  }
  case CDI_PHYS_TYPE_FIXED_TEMP: {
    CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR tpd = (CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)thermal_surface_phys_desc;
    asINT32 augmented_wall_cdi_phys_type;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95_FIXED_TEMP; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_TEMP; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_TEMP; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_TEMP; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_TEMP; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_TEMP; break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                            1, &tpd->parameters()->temperature,
                                            flow_surface_phys_descs);
    break;
  }

  case CDI_PHYS_TYPE_FIXED_HEAT_FLUX: {
    CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR tpd = (CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)thermal_surface_phys_desc;
    asINT32 augmented_wall_cdi_phys_type;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95_FIXED_HEAT_FLUX; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP_FIXED_HEAT_FLUX; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP_FIXED_HEAT_FLUX; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP_FIXED_HEAT_FLUX; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP_FIXED_HEAT_FLUX; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP_FIXED_HEAT_FLUX; break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                            1, &tpd->parameters()->heat_flux_2,  // heat_flux_1 into solid; heat_flux_2 into fluid
                                            flow_surface_phys_descs);
    break;
  }

  case CDI_PHYS_TYPE_ADIABATIC: {
    asINT32 augmented_wall_cdi_phys_type = -1;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:         
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:    
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:   
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:    
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:  
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP: 
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP; break;
      break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    if (augmented_wall_cdi_phys_type >= 0)
      register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                              0, NULL,
                                              flow_surface_phys_descs);
    break;
  }
  
  case CDI_PHYS_TYPE_THERMAL_RESIST: {
    THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR tpd = (THERMAL_RESIST_SURFEL_PHYSICS_DESCRIPTOR)thermal_surface_phys_desc;
    asINT32 augmented_wall_cdi_phys_type;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST; break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                            4, &tpd->parameters()->ambient_temp,
                                            flow_surface_phys_descs);
    break;
  }

  case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP: {
    CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR tpd = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)thermal_surface_phys_desc;
    asINT32 augmented_wall_cdi_phys_type;
    switch (wall_cdi_phys_type) {
    case CDI_PHYS_TYPE_SLIP95:
    case CDI_PHYS_TYPE_SLIP95_COUPLED:         augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_SLIP95_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_LINEAR_SLIP:
    case CDI_PHYS_TYPE_LINEAR_SLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_SLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_ANGULAR_SLIP:
    case CDI_PHYS_TYPE_ANGULAR_SLIP_COUPLED:   augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_SLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_TRUE_NOSLIP:
    case CDI_PHYS_TYPE_TRUE_NOSLIP_COUPLED:    augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_TRUE_NOSLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_LINEAR_NOSLIP:
    case CDI_PHYS_TYPE_LINEAR_NOSLIP_COUPLED:  augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_LINEAR_NOSLIP_THERMAL_RESIST; break;
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP:
    case CDI_PHYS_TYPE_ANGULAR_NOSLIP_COUPLED: augmented_wall_cdi_phys_type = CDI_PHYS_TYPE_ANGULAR_NOSLIP_THERMAL_RESIST; break;
    default:
      msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
    }
    sPHYSICS_VARIABLE new_parameters[4] = { 0 };
    new_parameters[0] = tpd->parameters()->ambient_temp;
    new_parameters[1] = tpd->parameters()->ext_heat_xfer_coeff;
    // The choice of values here for wall_thickness and wall_conductivity ensures they have no effect in the thermal resistance formulation
    new_parameters[2].value = 0;  // wall_thickness
    new_parameters[3].value = 1;  // wall_conductivity
    
    register_thermal_wall_surface_phys_desc(wall_surface_phys_desc, thermal_surface_phys_desc, augmented_wall_cdi_phys_type,
                                            4, new_parameters,
                                            flow_surface_phys_descs);
    break;
  }
  default:
    msg_internal_error("Invalid combination of wall and thermal BC CDI types: (%d, %d)", wall_cdi_phys_type, thermal_cdi_phys_type);
  }
}

static VOID merge_wall_and_thermal_surface_phys_descs()
{
  if (!sim.is_heat_transfer || !sim.is_flow)
    return;

  ccDOTIMES(face_index, g_num_faces) {
    //Front descriptors
    asINT32 flow_phys_desc_index = g_cdi_front_flow_surface_physics_desc_index_from_face_index[face_index];
    asINT32 thermal_phys_desc_index = g_cdi_front_thermal_surface_physics_desc_index_from_face_index[face_index];
    if (flow_phys_desc_index >= 0 && thermal_phys_desc_index >= 0)
      merge_wall_and_thermal_surface_phys_desc(flow_phys_desc_index, thermal_phys_desc_index);
    //Back descriptors
    flow_phys_desc_index = g_cdi_back_flow_surface_physics_desc_index_from_face_index[face_index];
    thermal_phys_desc_index = g_cdi_back_thermal_surface_physics_desc_index_from_face_index[face_index];
    if (flow_phys_desc_index >= 0 && thermal_phys_desc_index >= 0)
      merge_wall_and_thermal_surface_phys_desc(flow_phys_desc_index, thermal_phys_desc_index);
  }
}

static VOID create_swapped_contact_heat_flux_phys_descs()
{
  if (!sim.is_conduction_model)
    return;

  asINT32 n_thermal_phys_descs = thermal_surface_phys_descs.size();
  for (asINT32 i = 0; i < n_thermal_phys_descs; i++) {
    PHYSICS_DESCRIPTOR thermal_phys_desc = &thermal_surface_phys_descs[i];
    if (thermal_phys_desc->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX) {
      register_phys_desc_with_swapped_in_out_heat_fluxes(thermal_phys_desc, thermal_surface_phys_descs);
    }
  }
}      

static VOID store_final_surface_phys_descs()
{
  sim.n_flow_surface_physics_descs = flow_surface_phys_descs.size();
  sim.flow_surface_physics_descs = xnew sPHYSICS_DESCRIPTOR [ sim.n_flow_surface_physics_descs ];
  if (sim.n_flow_surface_physics_descs > 0) {
    memcpy(sim.flow_surface_physics_descs, &flow_surface_phys_descs[0], sim.n_flow_surface_physics_descs * sizeof(sPHYSICS_DESCRIPTOR));
  }

  sim.n_thermal_surface_physics_descs = thermal_surface_phys_descs.size();
  sim.thermal_surface_physics_descs = xnew sPHYSICS_DESCRIPTOR [ sim.n_thermal_surface_physics_descs ];
  if (sim.n_thermal_surface_physics_descs > 0) {
    memcpy(sim.thermal_surface_physics_descs, &thermal_surface_phys_descs[0], sim.n_thermal_surface_physics_descs * sizeof(sPHYSICS_DESCRIPTOR));
  }
}

/** Sometimes, people really like to use 0 Kelvin as an inlet/outlet
    radiation temperature, but we use 0 later on as a check to see if
    any radiation surfels were not initialized correctly. So now
    I am making sure that it's just slightly NOT 0.
*/
static void fix_inlet_outlet_radiation_temp()
{
  for (sPHYSICS_DESCRIPTOR& pd : flow_surface_phys_descs) {
    for (int i = 0; i< pd.n_parameters; i++) {
      if (pd.phys_type_desc->continuous_dp_var[i]->id == CDI_VAR_ID_RADIATION_TEMP) {
        if (pd._parameters[i].value == 0.0) {
          pd._parameters[i].value = std::numeric_limits<sFLOAT>::epsilon();
        }
      }
    }
  }
}

VOID read_flow_surface_physics_descriptors(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_FLOW_SURFACE_PHYSICS_DESCRIPTORS_TAG) {
    return;
  }

  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  if (header.n_physics_descs <= 0)
    return;

  if (sim.cdi_major_version < 9 || (sim.cdi_major_version == 9 && sim.cdi_minor_version <= 4)
      || !sim.is_heat_transfer) {
    sim.flow_surface_physics_descs = cnew sPHYSICS_DESCRIPTOR [ header.n_physics_descs ];
    sim.n_flow_surface_physics_descs = header.n_physics_descs;

    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      sim.flow_surface_physics_descs[i].m_type_index = SURFACE_PHYSICS_DESCRIPTOR_TYPE;
      read_physics_descriptor(stream, sim.flow_surface_physics_descs + i, i, FALSE);
    }
  } else {
    flow_surface_phys_descs.reserve(2 * header.n_physics_descs); // space for thermal wall BCs
    flow_surface_phys_descs.resize(header.n_physics_descs);
    ccDOTIMES(i, header.n_physics_descs) {
      flow_surface_phys_descs[i].m_type_index = SURFACE_PHYSICS_DESCRIPTOR_TYPE;
      read_physics_descriptor(stream, &flow_surface_phys_descs[i], i, FALSE);
    }
  }

  if (sim.cdi_major_version < 9 || (sim.cdi_major_version == 9 && sim.cdi_minor_version <= 4)) {
    if (sim.is_conduction_model)
      // Build the vector of sim.solid_surface_physics_descriptors
      derive_solid_surface_phys_descs();
    // There are some old flow CDI files incorrectly marked as conduction-only. Keeping around
    // the flow surface physics descriptors allows them to run.
    //if (!sim.is_flow) { // sim.is_lb_model is always set for some reason
    //  sim.n_flow_surface_physics_descs = 0;
    //  sim.flow_surface_physics_descs = NULL;
    //}

    // Need to scale conductivity to the correct enthalpy unit basis for cdi versions <= 9.5.
    // Calling correct_phys_desc_thermal_units to check version and scale if necessary.
    if (sim.is_heat_transfer)
      correct_phys_desc_thermal_units();
  }

  fix_inlet_outlet_radiation_temp();
}

// Helper function to get shell configuration index from a thermal physics descriptor
static asINT32 get_shell_config_index_from_thermal_phys_desc(PHYSICS_DESCRIPTOR pd)
{
  asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
  
  // Different thermal BC types might store the shell configuration in different places
  // We need to check the type and access the appropriate structure
  switch (cdi_phys_type) {
    case CDI_PHYS_TYPE_ADIABATIC:
      return ((CONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR)pd)->parameters()->shell_configuration.value;
    
    // Add cases for other thermal BC types that can have shell configurations
    // For example:
    case CDI_PHYS_TYPE_FIXED_TEMP:
      return ((CONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR)pd)->parameters()->shell_configuration.value;
    
    case CDI_PHYS_TYPE_FIXED_HTC_AMBIENT_TEMP:
      return ((CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR)pd)->parameters()->shell_configuration.value;
    
    case CDI_PHYS_TYPE_COUPLED_THERMAL:
      return ((CONDUCTION_COUPLED_THERMAL_SURFEL_PHYSICS_DESCRIPTOR)pd)->parameters()->shell_configuration.value; 
    
    case CDI_PHYS_TYPE_FIXED_HEAT_FLOW:
    case CDI_PHYS_TYPE_FIXED_HEAT_FLUX:
      return ((CONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR)pd)->parameters()->shell_configuration.value;
    
    default:
      return -1; // No shell configuration for this type
  }
}

VOID calculate_shell_layer_face_areas()
{
  // Skip if there are no shell configurations
  if (sim.n_shell_config_physics_descs <= 0) {
    return;
  }

  // Initialize shell layer face areas to zero
  for (asINT32 i = 0; i < sim.n_shell_config_physics_descs; i++) {
    SHELL_CONFIG_PHYSICS_DESCRIPTOR spd = &sim.shell_config_physics_descs[i];
    for (asINT32 n = 0; n < spd->n_layers; n++) {
      spd->parameters(n)->face_area.value = 0.0;
    }
  }
  
  // For each face
  for (asINT32 face_idx = 0; face_idx < g_num_faces; face_idx++) {
    // Get the face area
    dFLOAT face_area = g_flow_bc_total_areas[face_idx];
    
    // Check front side and back side
    sINT32 front_phys_desc_idx = g_cdi_front_thermal_surface_physics_desc_index_from_face_index[face_idx];
    sINT32 back_phys_desc_idx = g_cdi_back_thermal_surface_physics_desc_index_from_face_index[face_idx];
    if (front_phys_desc_idx >= 0) {
      // Get the shell configuration index from the physics descriptor
      asINT32 shell_config_idx = get_shell_config_index_from_thermal_phys_desc(
          &sim.thermal_surface_physics_descs[front_phys_desc_idx]);
      
      if (shell_config_idx >= 0 && shell_config_idx < sim.n_shell_config_physics_descs) {
        SHELL_CONFIG_PHYSICS_DESCRIPTOR spd = &sim.shell_config_physics_descs[shell_config_idx];
        for (asINT32 n = 0; n < spd->n_layers; n++) {
          spd->parameters(n)->face_area.value = face_area;
        }
      }
    } else if (back_phys_desc_idx >= 0) {
      // Get the shell configuration index from the physics descriptor
      asINT32 shell_config_idx = get_shell_config_index_from_thermal_phys_desc(
          &sim.thermal_surface_physics_descs[back_phys_desc_idx]);
      
      if (shell_config_idx >= 0 && shell_config_idx < sim.n_shell_config_physics_descs) {
        SHELL_CONFIG_PHYSICS_DESCRIPTOR spd = &sim.shell_config_physics_descs[shell_config_idx];
        for (asINT32 n = 0; n < spd->n_layers; n++) {
          spd->parameters(n)->face_area.value = face_area;
        }
      }
    }
  }
}

// The CP arranges for this record to follow the corresponding record with flow surface physics descriptors.
VOID read_thermal_surface_physics_descriptors(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_THERMAL_SURFACE_PHYSICS_DESCRIPTORS_TAG) {
    return;
  }

  LGI_PHYSICS_DESCRIPTORS_HEADER header;
  lgi_read_next_head(stream, header);

  if (header.n_physics_descs <= 0) {
    store_final_surface_phys_descs();
    return;
  }

  thermal_surface_phys_descs.resize(header.n_physics_descs);

  ccDOTIMES(i, header.n_physics_descs) {
    thermal_surface_phys_descs[i].m_type_index = SURFACE_PHYSICS_DESCRIPTOR_TYPE;
    read_physics_descriptor(stream, &thermal_surface_phys_descs[i], i, FALSE);
  }

  // Heat flow bc was added in cdi version 9.18
  if (sim.cdi_major_version >= 9 && sim.cdi_minor_version >= 18) {
    // Calculate face areas for heat flow BCs if required
    calculate_heat_flow_face_areas();
  }

  // done reading both flow and thermal descs
  merge_wall_and_thermal_surface_phys_descs();
  create_swapped_contact_heat_flux_phys_descs();
  store_final_surface_phys_descs();

  // Need to scale conductivity to the correct enthalpy unit basis for cdi versions <= 9.5.
  // Calling correct_phys_desc_thermal_units to check version and scale if necessary.
  correct_phys_desc_thermal_units();


}

//definition
std::vector<sCDI_FACE_INDEX_TO_PHYSICS_INFO> g_cdi_face_index_to_flow_physics_info;

VOID read_face_id_to_flow_surface_physics(LGI_STREAM stream) {
  
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_FACE_ID_TO_SURFACE_PHYSICS_TAG) { return; }

  LGI_FACE_ID_TO_SURFACE_PHYSICS_HEADER header;
  lgi_read_next_head(stream, header);
  ccDOTIMES(i, header.n_faces) {
    LGI_FACE_ID_TO_SURFACE_PHYSICS lgi_data;
    lgi_read(stream, lgi_data);
    g_cdi_face_index_to_flow_physics_info.push_back(lgi_data);
  }
}

VOID read_bc_turb_vel(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_TURB_SYNTH_VEL_TAG) {
    return;
  }

  LGI_TURB_VEL_HEADER header;
  lgi_read_next_head(stream, header);
  uINT32 nvels = header.nvels;
  g_turb_info.m_nvel.push_back(nvels);

  // CDI reader uses doubles to store velocities so use dFLOAT here
  dFLOAT vel_tmp[3];
  sdFLOAT *vel_array = new sdFLOAT[3 * nvels];
  ccDOTIMES(t, nvels) {
    lgi_read(stream, vel_tmp, 3 * sizeof(dFLOAT));
    vel_array[t] = vel_tmp[0];
    vel_array[t + nvels] = vel_tmp[1];
    vel_array[t + 2 * nvels] = vel_tmp[2];
  }

  uINT32 nTables = g_turb_info.m_nvel.size();
  g_turb_info.m_v.resize(nTables);
  g_turb_info.m_v[nTables - 1] = vel_array;
}

// For each coupling struct, this function finds the physics descriptor that
// depends upon it.  This information is used when coupling data is received
// during simulation to update the appropriate set of voxel and surfel dyn
// groups.
VOID compute_dependencies_from_coupling_to_physics_descs()
{
  if (sim.is_conduction_sp)
    return;

   asINT32 n_spds = sim.n_flow_surface_physics_descs;
   PHYSICS_DESCRIPTOR pds = sim.flow_surface_physics_descs;
   ccDOTIMES(pd_index, n_spds) {
     asINT32 model_index = pds->coupling_model_index;
     if (model_index >= 0) {
       DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd =
                       xnew sDEPENDENT_PHYSICS_DESCRIPTOR;
       dependent_pd->physics_desc = pds;
       COUPLING_MODEL coupling_model = &(sim.coupling_models[model_index]);
       dependent_pd->next = coupling_model->dependent_physics_descs;
       coupling_model->dependent_physics_descs = dependent_pd;
     }
     pds++;
   }
}

VOID add_exprlang_dmask_for_shell_phys_var(PHYSICS_VARIABLE parm, EXPRLANG_DEPENDENCE_MASK& pd_dmask) {
  if (parm->name != NULL) { // only interested in space or time varying variables
    asINT32 prog_index = exprlang_find_output_variable(g_case_program, parm->name);
    if (prog_index < 0)
      msg_internal_error("While computing table dependencies, case parameter refers to undefined variable \"%s\".",
                          parm->name);
    EXPRLANG_DEPENDENCE_MASK parm_dmask = exprlang_output_variable_dependence_mask(g_case_program, prog_index);
    pd_dmask |= parm_dmask;
  }
}

VOID loop_through_shell_config_physics_descs(TABLE tables, asINT32 n_tables_this_pass) {
  const EXPRLANG_DEPENDENCE_MASK  RESERVED_DEPENDENCE_MASK = (1 << N_RESERVED_DEPENDENCE_MASK_BITS) - 1;
  asINT32 n_pds = sim.n_shell_config_physics_descs;
  for (asINT32 i=0; i < sim.n_shell_config_physics_descs; i++) {
    SHELL_CONFIG_PHYSICS_DESCRIPTOR pd = &(sim.shell_config_physics_descs[i]);
    EXPRLANG_DEPENDENCE_MASK pd_dmask = 0;
    PHYSICS_VARIABLE parm = pd->sPHYSICS_DESCRIPTOR::parameters();
    asINT32 n_parms = pd->sPHYSICS_DESCRIPTOR::n_parameters;
    for (asINT32 j=0; j<n_parms; j++, parm++) {
      add_exprlang_dmask_for_shell_phys_var(parm, pd_dmask);
    }


    // // Shell has 2 parameters in each layer that can be time/space varying: thickness and imposed_heat
    // for (asINT32 j = 0; j < pd->num_layers, j++) {
    //   SHELL_LAYER_PARAMETERS layer_param = pd->parameters(j);
    //   add_exprlang_dmask_for_shell_phys_var(&(layer_param->thickness), pd_dmask);
    //   add_exprlang_dmask_for_shell_phys_var(&(layer_param->imposed_heat), pd_dmask);
    // }
    if ((pd_dmask & ~RESERVED_DEPENDENCE_MASK) != 0) {
      for (asINT32 d=0; d<n_tables_this_pass; d++) {
        auINT32 table_dependence_mask = 1 << (d + N_RESERVED_DEPENDENCE_MASK_BITS);
        if (pd_dmask & table_dependence_mask) {
          // This physics descriptor depends on this table
          TABLE table = tables + d;
          BOOLEAN pd_already_in_list = FALSE;
          DEPENDENT_PHYSICS_DESCRIPTOR dpd = table->dependent_physics_descs;
          while (dpd != NULL) {
            if (dpd->physics_desc == pd) {
              pd_already_in_list = TRUE;
              break;
            }
            dpd = dpd->next;
          }
          if (!pd_already_in_list) {
            DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = xnew sDEPENDENT_PHYSICS_DESCRIPTOR;
            dependent_pd->physics_desc = pd;
            dependent_pd->next = table->dependent_physics_descs;
            table->dependent_physics_descs = dependent_pd;
          }
        }
      }
    }
  }
}

// For each table, this function computes the physics descriptors that depend on it.
// This information is used when the table is read during simulation to update the
// appropriate set of voxel and surfel dyn groups.
VOID compute_dependencies_from_tables_to_physics_descs()
{
  if (g_case_program == NULL) // CDI may lack both undb and eqns
    return;

  asINT32 n_tables = sim.n_tables;
  TABLE tables = sim.tables;

  const EXPRLANG_DEPENDENCE_MASK  RESERVED_DEPENDENCE_MASK = (1 << N_RESERVED_DEPENDENCE_MASK_BITS) - 1;
  const asINT32 N_FREE_BITS_DEPENDENCE_MASK = (8 * sizeof(EXPRLANG_DEPENDENCE_MASK)
                                               - N_RESERVED_DEPENDENCE_MASK_BITS);
  asINT32 n_passes = (n_tables + (N_FREE_BITS_DEPENDENCE_MASK - 1)) / N_FREE_BITS_DEPENDENCE_MASK;

  asINT32 n_tables_left = n_tables;
  for (asINT32 pass=0; pass<n_passes; pass++) {
    asINT32 n_tables_this_pass = MIN(N_FREE_BITS_DEPENDENCE_MASK, n_tables_left);

    for (asINT32 i=0; i<n_tables_this_pass; i++) {
      auINT32 table_dependence_mask = 1 << (i + N_RESERVED_DEPENDENCE_MASK_BITS);
      exprlang_set_v_table_input_variable_dependence_mask(g_case_program, (tables + i)->name,
                                                          table_dependence_mask);
    }

    BOOLEAN ok_p = exprlang_recheck(g_case_program, sim_expr_errfcn);

    if (!ok_p)
      msg_internal_error("Errors in CDI equations while computing table dependencies.");

    asINT32 n_pds = sim.n_volume_physics_descs;
    PHYSICS_DESCRIPTOR pds = sim.volume_physics_descs;
    const int N_PHYS_DESC_TYPES = 6; 
    for (asINT32 k = 0; k < (N_PHYS_DESC_TYPES-1); k++) {
      if (k == 1) {
        n_pds = sim.n_flow_surface_physics_descs;
        pds = sim.flow_surface_physics_descs;
      } else if (k == 2) {
        n_pds = sim.n_thermal_surface_physics_descs;
        pds = sim.thermal_surface_physics_descs;
      } else if (k == 3) {
        n_pds = sim.n_body_force_descs;
        pds = sim.body_force_descs;
      } else if (k == 4) {
        loop_through_shell_config_physics_descs(tables, n_tables_this_pass);
        continue;
      }

      PHYSICS_DESCRIPTOR pd = pds;
      for (asINT32 i=0; i<n_pds; i++, pd++) {
        PHYSICS_VARIABLE parm = pd->parameters();
        asINT32 n_parms = pd->n_parameters;
        EXPRLANG_DEPENDENCE_MASK pd_dmask = 0;
        for (asINT32 j=0; j<n_parms; j++, parm++) {
          if (parm->name != NULL) { // only interested in space or time varying variables
            asINT32 prog_index = exprlang_find_output_variable(g_case_program, parm->name);
            if (prog_index < 0)
              msg_internal_error("While computing table dependencies, case parameter refers to undefined variable \"%s\".",
                                 parm->name);
            EXPRLANG_DEPENDENCE_MASK parm_dmask = exprlang_output_variable_dependence_mask(g_case_program,
                                                                                           prog_index);
            pd_dmask |= parm_dmask;
          }
        }

        if ((pd_dmask & ~RESERVED_DEPENDENCE_MASK) != 0) {
          for (asINT32 d=0; d<n_tables_this_pass; d++) {
            auINT32 table_dependence_mask = 1 << (d + N_RESERVED_DEPENDENCE_MASK_BITS);
            if (pd_dmask & table_dependence_mask) {
              // This physics descriptor depends on this table
              TABLE table = tables + d;
              BOOLEAN pd_already_in_list = FALSE;
              DEPENDENT_PHYSICS_DESCRIPTOR dpd = table->dependent_physics_descs;
              while (dpd != NULL) {
                if (dpd->physics_desc == pd) {
                  pd_already_in_list = TRUE;
                  break;
                }
                dpd = dpd->next;
              }
              if (!pd_already_in_list) {
                DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = xnew sDEPENDENT_PHYSICS_DESCRIPTOR;
                dependent_pd->physics_desc = pd;
                dependent_pd->next = table->dependent_physics_descs;
                table->dependent_physics_descs = dependent_pd;
              }
            }
          }
        }
      }
    }

    // reset all the table dependence masks
    for (asINT32 n=0; n<n_tables_this_pass; n++)
      exprlang_set_v_table_input_variable_dependence_mask(g_case_program, (tables + n)->name, 0);

    tables += n_tables_this_pass;
    n_tables_left -= n_tables_this_pass;
  }
}
   
VOID read_case_origin(LGI_STREAM istream)
{
  LGI_CASE_ORIGIN record;
  lgi_read_next_head(istream, record);
  
  sim.case_origin[0] = record.case_origin[0];
  sim.case_origin[1] = record.case_origin[1];
  sim.case_origin[2] = record.case_origin[2];
}

VOID read_csys(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_CSYS_TAG) {
    return;
  }

  LGI_CSYS_HEADER header;
  lgi_read_next_head(stream, header);

  sim.n_csys = header.n_csys;

  sim.csys_table = xnew sCSYS [ header.n_csys ];

  CSYS csys = sim.csys_table;

  ccDOTIMES(i, header.n_csys) {
    LGI_CSYS lgi_csys;

    lgi_read(stream, lgi_csys);

    csys->name = xnew char [ lgi_csys.name_length + 1 ];

    lgi_read(stream, csys->name, lgi_csys.name_length);

    csys->name[lgi_csys.name_length] = '\0';

    ccDOTIMES(i, 4) {
      ccDOTIMES(j, 3) {
        // Must transpose xform matrix for G3
        csys->g_to_l_xform.xcoord[i][j] = lgi_csys.g_to_l_xform[j][i];
        csys->l_to_g_xform.xcoord[i][j] = lgi_csys.l_to_g_xform[j][i];
      }
    }

    csys++;
  }

  if (header.n_csys > 0) {
    CSYS csys = sim.csys_table; // default csys

    ccDOTIMES(i, 3) {
      ccDOTIMES(j, 3) {
        if (((i == j) && (csys->g_to_l_xform.xcoord[i][j] != 1))
            || ((i != j) && (csys->g_to_l_xform.xcoord[i][j] != 0)))
          msg_internal_error("Default coord system includes a rotation or scaling.");
      }
    }
  }

}

VOID read_tables(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_TABLE_COUNT_TAG) {
    return;
  }

  LGI_TABLE_COUNT header;
  lgi_read_next_head(stream, header);

  sim.n_tables = header.n_tables;

  sim.tables = cnew sTABLE [ header.n_tables ];

  TABLE table = sim.tables;

  ccDOTIMES(i, header.n_tables) {
    LGI_TABLE lgi_table;

    lgi_read_next_head(stream, lgi_table);

    table->read_during_sim = (sim.is_conduction_sp && lgi_table.is_hx_table) ? FALSE : lgi_table.read_during_sim;
    table->ready = FALSE;
    table->time_rel_realm = static_cast<STP_REALM>(lgi_table.time_rel_realm);
    table->period = lgi_table.period;
    table->end_time = lgi_table.end_time;
    table->next_periodic_read = lgi_table.next_periodic_read;

    table->name = xnew char [ lgi_table.name_length + 1 ];

    lgi_read(stream, table->name, lgi_table.name_length);

    table->name[lgi_table.name_length] = '\0';

    strncpy(table->smem_prefix, lgi_table.smem_prefix, LGI_TABLE_SMEM_NAME_LEN);

    // Pad the table size if we definitely plan to read the table again to allow
    // for slight expansion in the size of the table. Resizing was causing issues, 
    // so just for safety's sake, allocate 1 Mb instead of 1 kb.
    size_t bytes_allocated = (lgi_table.read_during_sim
                              ? lgi_table.table_length + 1024*1024
                              : lgi_table.table_length);
    table->bytes_allocated = bytes_allocated;

    std::string table_smem_name( std::string(table->smem_prefix) + std::string(table->name) );
    table->smem = new cSHARED_MEMORY(table_smem_name,table->bytes_allocated,cSHARED_MEMORY::MEM_ADDR_TYPE_RELATIVE, g_sp_node_comm.nprocs());
    table->serialized_table = (char*) table->smem->GetData();

    // We allocate tables in a round-robin fashion. Reserve clears the memory,
    // which places the table in the primary process's memory domain.
    bool i_own_this_table = i % g_sp_node_comm.nprocs() == g_sp_node_comm.rank();
    if ( i_own_this_table ) {
      // Reserve should provide the same address as GetData();
      table->serialized_table = (char*) table->smem->Reserve(table->bytes_allocated);
      lgi_read(stream, table->serialized_table, lgi_table.table_length);
    }
      // std::stringstream s; s << i;
      // std::ofstream sp_data("sp-init-"+s.str());
      // for(int i=0; i<lgi_table.table_length; i++) {
      //   fmt::print(sp_data, "{0:x}", table->serialized_table[i]);
      // }
      // sp_data << std::endl;
    table++;
  }

  // Wait for all sps on this node to finish processing the tables.
  MPI_Barrier(eMPI_sp_node_comm);

  table = sim.tables;
  ccDOTIMES(i, header.n_tables) {
    // this function fills in table->vtable and units_db from the shared memory region
    table->vtable = exprlang_make_v_table_value();
    exprlang_deserialize_v_table_in_place(table->serialized_table, units_db, table->vtable);
    table++;
  }

}


VOID add_initial_table_reads_to_event_queue()
{
  TABLE table = sim.tables;
  ccDOTIMES(i, sim.n_tables) {
    if (table->read_during_sim) {
      if (table->next_periodic_read <= table->end_time)
        add_event(new_event(EVENT_ID_READ_TABLE,
                            i, table->next_periodic_read,
                            table->period, table->end_time, table->time_rel_realm));
    }
    table++;
  }
}

static BOOLEAN is_ineligible_desc_name(cSTRING desc_name, asINT32 n_names,
    STRING name_vector)
{
  if (n_names <= 0 || !desc_name || !name_vector)
    return FALSE;

  STRING name = name_vector;
  ccDOTIMES(n,n_names) {
    if (!strcmp(desc_name,name)) {
      return TRUE;
    }
    name += strlen(name)+1;
  }
  return FALSE;
}

static VOID process_ineligible_coupling_phys_descs(asINT32 n_names,
                                                   STRING name_vector)
{
  if (n_names <= 0 || !name_vector)
    return;

  // surface physics descriptors in this list should not participate in surface
  // coupling, so reset the coupling model_index for these
  asINT32 n_spds = sim.n_flow_surface_physics_descs;
  PHYSICS_DESCRIPTOR pds = sim.flow_surface_physics_descs;
  ccDOTIMES(pd_index, n_spds) {
    asINT32 model_index = pds->coupling_model_index;
    if (model_index >= 0 &&
        is_ineligible_desc_name(pds->name,n_names,name_vector)) {
      pds->coupling_model_index = -1;
    }
    pds++;
  }
}

VOID read_coupling_models(LGI_STREAM stream)
{
  cDGF_COUPLING_MODELS header;
  header.read(stream);

  sim.n_coupling_models = header.n_coupling_models;
  sim.coupling_models = cnew sCOUPLING_MODEL [ header.n_coupling_models ];
  COUPLING_MODEL coupling_model = sim.coupling_models;

  char *ineligible_names = NULL;
  ccDOTIMES(i, header.n_coupling_models) {
    cDGF_COUPLING_MODEL dgf_coupling_model;
    dgf_coupling_model.read(stream);

    coupling_model->couple_during_sim_p = dgf_coupling_model.couple_during_sim_p;
    coupling_model->init_pf_bc_coupling_p = dgf_coupling_model.init_pf_bc_coupling_p;
    coupling_model->ready = FALSE;
    coupling_model->period = dgf_coupling_model.period;
    coupling_model->end_time = dgf_coupling_model.end_time;
    coupling_model->next_periodic_read = dgf_coupling_model.next_periodic_read;
    coupling_model->n_vars = dgf_coupling_model.n_vars;

    coupling_model->name = xnew char [ dgf_coupling_model.name_length + 1 ];
    coupling_model->var_types = xnew DGF_COUPLING_VAR_TYPE [ dgf_coupling_model.n_vars ];

    lgi_read(stream, coupling_model->name, dgf_coupling_model.name_length);

    coupling_model->name[dgf_coupling_model.name_length] = '\0';

    lgi_read(stream, coupling_model->var_types, sizeof(DGF_COUPLING_VAR_TYPE)*dgf_coupling_model.n_vars);

    if ((dgf_coupling_model.n_ineligible_pf_bcs > 0) &&
        (dgf_coupling_model.ineligible_pf_bc_names_length > 0)) {
      ineligible_names = xnew char [ dgf_coupling_model.ineligible_pf_bc_names_length ];
      lgi_read(stream, ineligible_names, dgf_coupling_model.ineligible_pf_bc_names_length);
      process_ineligible_coupling_phys_descs(dgf_coupling_model.n_ineligible_pf_bcs,ineligible_names);
      delete[] ineligible_names;
    }

    // for variable PowerTherm coupling periods
    ccDOTIMES(j, dgf_coupling_model.n_coupling_phases)
    {
      cDGF_COUPLING_PHASE coupling_phase;
      coupling_phase.read(stream);
      sCOUPLING_PHASE_TIME_DESC_SP time_desc;
      time_desc.m_coupling_time = coupling_phase.start;
      time_desc.m_period = coupling_phase.period;
      time_desc.m_delay = coupling_phase.delay;
      coupling_model->m_coupling_phase_descs.push_back(time_desc);
    }

    coupling_model++;
  }

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  coupling_model = sim.coupling_models;
  ccDOTIMES(i, sim.n_coupling_models)
  {
    asINT32 n_coupling_phases = coupling_model->m_coupling_phase_descs.size();
    ccDOTIMES(j, n_coupling_phases)
    {
      msg_print("m_coupling_phase_descs %d: start: %d  period %d", j, coupling_model->m_coupling_phase_descs[j].m_coupling_time,
                 coupling_model->m_coupling_phase_descs[j].m_period);
    }
  }
#endif
}

VOID add_initial_couplings_to_event_queue()
{
  COUPLING_MODEL coupling_model = sim.coupling_models;
  ccDOTIMES(i, sim.n_coupling_models) {
    if (coupling_model->couple_during_sim_p) {
      // For a fresh simulation from t=0, init_pf_bc_coupling_p is set if the initial
      // bc is set from coupling data. For a ckpt restart, init_pf_bc_coupling_p is
      // set if the original run already read coupling data (i.e., prior to the
      // restart timestep) or if the initial t=0 bc is set from coupling data.
      if (coupling_model->init_pf_bc_coupling_p) {
        add_event(new_event(EVENT_ID_READ_COUPLING_DATA,
                            i, g_timescale.time_flow(), 0, TIMESTEP_NEVER, STP_FLOW_REALM));
      }

      if (coupling_model->next_periodic_read <= coupling_model->end_time) {
        // regular periodic coupling model read event handled here
        add_event(new_event(EVENT_ID_READ_COUPLING_DATA,
                            i, coupling_model->next_periodic_read,
                            coupling_model->period, coupling_model->end_time, STP_FLOW_REALM));
      }
    }
    coupling_model++;
  }
}

VOID read_rotational_dynamics_descs(LGI_STREAM stream)
{
  cDGF_ROTATIONAL_DYNAMICS_HEADER header;
  header.read(stream);

  sim.n_rotational_dynamics_descs = header.n_rotational_dynamics_descs;
  sim.rotational_dynamics_descs = cnew sROTATIONAL_DYNAMICS_DESC[header.n_rotational_dynamics_descs];
  ROTATIONAL_DYNAMICS_DESC rot_dyn_desc = sim.rotational_dynamics_descs;

  ccDOTIMES(i, header.n_rotational_dynamics_descs) {
    cDGF_ROTATIONAL_DYNAMICS_DESC dgf_rot_dyn_desc;
    dgf_rot_dyn_desc.read(stream);

    rot_dyn_desc->lrf         = &sim.lrf_physics_descs[dgf_rot_dyn_desc.lrf_index];
    rot_dyn_desc->window      = g_meas_windows[dgf_rot_dyn_desc.window_index];
    rot_dyn_desc->ready       = FALSE;
    rot_dyn_desc->delay       = dgf_rot_dyn_desc.delay;
    rot_dyn_desc->eqn_time    = dgf_rot_dyn_desc.meas_start_time;
    rot_dyn_desc->start_time  = dgf_rot_dyn_desc.start_time;
    rot_dyn_desc->end_time    = dgf_rot_dyn_desc.end_time;
    rot_dyn_desc->count       = 0;

    rot_dyn_desc->resistive_torque_mpi_request = MPI_REQUEST_NULL;
    rot_dyn_desc->external_torque_mpi_request = MPI_REQUEST_NULL;

    LRF_PHYSICS_DESCRIPTOR lrf = rot_dyn_desc->lrf;

    if (sim.is_full_checkpoint_restore) {
      rot_dyn_desc->angular_acceleration_buffer = dgf_rot_dyn_desc.angular_acceleration_buffer;
      lrf->angular_acceleration = dgf_rot_dyn_desc.angular_acceleration;
    } else {
      lrf->omega  = dgf_rot_dyn_desc.initial_omega;
      lrf->omega1 = dgf_rot_dyn_desc.initial_omega;
      vscale(lrf->angular_vel, lrf->omega, lrf->axis);
    }

    LRF_PARAMETERS parms = rot_dyn_desc->lrf->parameters();
    rot_dyn_desc->is_resistive_torque_time_varying = !parms->resistive_torque.is_constant();
    rot_dyn_desc->is_external_torque_time_varying  = !parms->external_torque.is_constant();

    rot_dyn_desc++;
  }
}

VOID add_initial_rotational_dynamics_to_event_queue()
{
  ROTATIONAL_DYNAMICS_DESC rotdyn_desc = sim.rotational_dynamics_descs;
  ccDOTIMES(i, sim.n_rotational_dynamics_descs) {
    TIMESTEP period = rotdyn_desc->window->m_time_desc.period;
    TIMESTEP start  = rotdyn_desc->start_time;
    TIMESTEP end    = rotdyn_desc->end_time;

    add_event(new_event(EVENT_ID_ROTATIONAL_DYNAMICS, i, start, period, end, STP_FLOW_REALM));

    // in the case of a checkpoint restore, post this receive only if restarting from 
    // a checkpoint that was recorded after the SP completed recving alpha, but before the next CP send
    if (!sim.is_full_checkpoint_restore ||
        (sim.is_full_checkpoint_restore &&
         g_timescale.time_flow() > (rotdyn_desc->eqn_time - period + rotdyn_desc->delay))) {
      //asINT32 tag = make_mpi_tag(eMPI_MSG_ROTDYN, i);
      //g_sim_comm.irecv(&(rotdyn_desc->angular_acceleration_buffer), 1, eMPI_dFLOAT, eMPI_sp_cp_rank(),
      //                 tag, &(rotdyn_desc->mpi_request));
    }
    rotdyn_desc->resistive_torque_request = FALSE;
    rotdyn_desc->external_torque_request = FALSE;
    if (my_proc_id == 0 && (rotdyn_desc->is_resistive_torque_time_varying || rotdyn_desc->is_external_torque_time_varying)) {
      /**
       * When restarting from a checkpoint that was taken before SPs completed the recv from the CP (i.e.
       * before the end of the delay), the first rotational dynamics asynchronous event on the SP 
       * occurs before the first alpha-send from the CP. 
       * rotdyn_desc->count keeps track of how many values of time-varying torque have already been
       * used by the CP to calculate the angular acceleration. Hence, we do not need to increment 
       * it here if the first rotational dynamics async_event is going to occur before the first 
       * time alpha is computed on the CP.
       */
      if (!sim.is_full_checkpoint_restore ||
          (sim.is_full_checkpoint_restore &&
           g_timescale.time_flow() > (rotdyn_desc->eqn_time - period + rotdyn_desc->delay))) {
        rotdyn_desc->count++;
      }
      LRF_PARAMETERS parms = rotdyn_desc->lrf->parameters();
      if (rotdyn_desc->is_resistive_torque_time_varying) {
        rotdyn_desc->resistive_torque = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
      }
      if (rotdyn_desc->is_external_torque_time_varying) {
        rotdyn_desc->external_torque = new dFLOAT[NUM_TIME_VARYING_TORQUE_VALUES];
      }
      ccDOTIMES(j, NUM_TIME_VARYING_TORQUE_VALUES) {
        BASETIME eqn_base_time = sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(rotdyn_desc->eqn_time);
        rotdyn_desc->lrf->eval_time_varying_only_parameter_program(eqn_base_time, 0, global_eqn_error_handler);
        if (rotdyn_desc->is_resistive_torque_time_varying) rotdyn_desc->resistive_torque[j] = parms->resistive_torque.value;
        if (rotdyn_desc->is_external_torque_time_varying) rotdyn_desc->external_torque[j] = parms->external_torque.value;
        rotdyn_desc->eqn_time += period;
      }
      if (rotdyn_desc->is_resistive_torque_time_varying) {
        rotdyn_desc->resistive_torque_request = TRUE;
      }
      if (rotdyn_desc->is_external_torque_time_varying) {
        rotdyn_desc->external_torque_request = TRUE;
      }
    }

    rotdyn_desc++;
  }
}

asINT32 fill_coupling_model_surfel_ids(COUPLING_MODEL coupling_model)
{

  coupling_model->nsurfels = 0;
  for (DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = coupling_model->dependent_physics_descs;
      dependent_pd != NULL;
      dependent_pd = dependent_pd->next) {
    PHYSICS_DESCRIPTOR pd = dependent_pd->physics_desc;
    coupling_model->nsurfels += num_dependent_coupling_surfels(pd);
  }

  if (0 == coupling_model->nsurfels)
    return 0;

  coupling_model->surfel_ids = cnew STP_SURFEL_ID [ coupling_model->nsurfels ];
  STP_SURFEL_ID *ids = coupling_model->surfel_ids;
  for (DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = coupling_model->dependent_physics_descs;
      dependent_pd != NULL;
      dependent_pd = dependent_pd->next) {
    PHYSICS_DESCRIPTOR pd = dependent_pd->physics_desc;
    append_dependent_coupling_surfel_ids(pd, &ids);
  }

  return coupling_model->nsurfels;
}

VOID free_coupling_model_surfel_ids()
{
  ccDOTIMES(model_index,sim.n_coupling_models) {
    COUPLING_MODEL coupling_model = sim.coupling_models + model_index;
    if (coupling_model->nsurfels > 0 &&
        coupling_model->surfel_ids) {
      delete[] coupling_model->surfel_ids;
      coupling_model->surfel_ids = NULL;
    }
  }
}

BOOLEAN update_time_and_space_varying_surfel_physics_parms(STP_GEOM_VARIABLE surfel_centroid[3],
                                                           STP_GEOM_VARIABLE surfel_normal[3],
                                                           PHYSICS_DESCRIPTOR pd) {
  BOOLEAN some_parameter_time_varying = pd->some_parameter_time_varying;
  BOOLEAN some_parameter_time_and_space_varying = pd->some_parameter_time_and_space_varying;
  if (some_parameter_time_varying) {
    if (some_parameter_time_and_space_varying)
      pd->eval_time_and_space_varying_parameter_program(surfel_centroid, surfel_normal,
							g_timescale.m_time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
  }
  return some_parameter_time_varying;
}

VOID maybe_update_time_and_space_varying_shell_physics_parms(STP_GEOM_VARIABLE surfel_centroid[3],
                                                           STP_GEOM_VARIABLE surfel_normal[3],
                                                           SHELL_CONFIG_PHYSICS_DESCRIPTOR pd) {
  BOOLEAN some_parameter_time_varying = pd->some_parameter_time_varying;
  BOOLEAN some_parameter_time_and_space_varying = pd->some_parameter_time_and_space_varying;
  if (some_parameter_time_varying) {
    if (some_parameter_time_and_space_varying)
       pd->eval_time_and_space_varying_parameter_program(surfel_centroid, surfel_normal,
           g_timescale.m_time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
  }
}

VOID update_all_time_varying_fluid_physics_parameters() {
  static BASETIME last_update = -1;
  if (last_update >= g_timescale.m_time)
    return;
  last_update = g_timescale.m_time;
  ccDOTIMES(phys_desc_index, sim.n_volume_physics_descs) {
    PHYSICS_DESCRIPTOR pd = &sim.volume_physics_descs[phys_desc_index];
    if (pd->some_parameter_time_varying_only)
      pd->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
  }
}

VOID update_all_time_varying_surface_physics_parameters() {
  static BASETIME last_update = -1;
  if (last_update >= g_timescale.m_time)
    return;
  last_update = g_timescale.m_time;
  asINT32 n_surface_physics_descs = sim.n_flow_surface_physics_descs;
  PHYSICS_DESCRIPTOR surface_physics_descs = sim.flow_surface_physics_descs;
  for (int i=0; i<2; i++, n_surface_physics_descs = sim.n_thermal_surface_physics_descs, surface_physics_descs = sim.thermal_surface_physics_descs) {
    ccDOTIMES(phys_desc_index, n_surface_physics_descs) {
      PHYSICS_DESCRIPTOR pd = surface_physics_descs + phys_desc_index;
      if (pd->some_parameter_time_varying_only)
        pd->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
    }
  }
}

VOID update_all_time_varying_shell_physics_parameters() {
  static BASETIME last_update = -1;
  if (last_update >= g_timescale.m_time)
    return;
  last_update = g_timescale.m_time;
  asINT32 n_shell_physics_descs = sim.n_shell_config_physics_descs;
  SHELL_CONFIG_PHYSICS_DESCRIPTOR shell_physics_descs = sim.shell_config_physics_descs;
  ccDOTIMES(phys_desc_index, n_shell_physics_descs) {
    SHELL_CONFIG_PHYSICS_DESCRIPTOR pd = shell_physics_descs + phys_desc_index;
    BOOLEAN some_parameter_time_varying = pd->some_parameter_time_varying;
    if (some_parameter_time_varying) {
        pd->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
      }
    }
}

VOID update_all_time_varying_bsurfel_physics_parameters(BASETIME time)
{
  ccDOTIMES(phys_desc_index, sim.n_movb_physics_descs) {
    PHYSICS_DESCRIPTOR pd = &sim.movb_physics_descs[phys_desc_index];
    if (pd->some_parameter_time_varying_only)
      pd->eval_time_varying_only_parameter_program(time, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
  }
}

VOID update_all_time_varying_body_force_parameters() {
  static BASETIME last_update = -1;
  if (last_update >= g_timescale.m_time)
    return;
  last_update = g_timescale.m_time;
  ccDOTIMES(bf_desc_index, sim.n_body_force_descs) {
    BODY_FORCE_PHYSICS_DESCRIPTOR pd = &sim.body_force_descs[bf_desc_index];
    if (pd->some_parameter_time_varying_only)
      pd->eval_time_varying_only_parameter_program(last_update, g_timescale.m_powertherm_time, boundary_eqn_error_handler);
  }
}



VOID find_units_conversion_coefficients(cSTRING from_unit_name, cSTRING to_unit_name, 
                                        dFLOAT *slope, dFLOAT *offset)
{
  UNITS_UNIT from_unit;

  if (units_parse_unit(units_db, from_unit_name, &from_unit) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\" in fan data table", from_unit_name);
  UNITS_UNIT to_unit;
  if (units_parse_unit(units_db, to_unit_name, &to_unit) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\" in fan data table", to_unit_name);

  if (units_conversion_coefficients(units_db, from_unit, to_unit, slope, offset)
      != UNITS_STATUS_OK)
    msg_internal_error("Unable to convert from unit \"%s\" to unit \"%s\"", from_unit_name, to_unit_name);
}

/* This will scale any conductivity value found in the CDI physics descriptors
 * now rather than in the different surfel classes storing the variable.
 * Note: in old units version, resistivity already defined in correct enthalpy
 * based units, so no need to scale it.
 * 
 * Note 1: when conductivity is copied to surfel classes, it is scaled by g_density_scale_factor
 * to ensure that it has same scale as density. The same is true for other thermal properties
 * such as heat transfer coefficient, heat generation, and thermal resistance (scaled by
 * 1/g_density_scale_factor).
 * 
 * Note 2: In previous versions, most but not all thermal conductivities as well as
 * other thermal properties were also scaled by (sim.C_p * g_lb_temp_constant) which
 * is equal to 1 for D19 lattice. This has been removed as no explanation for the
 * scaling could be found. In other lattices, this scale may not be unity and could
 * cause issues. Specifically this scale factor was not applied to all thermal
 * properties.
 * */
VOID correct_phys_desc_thermal_units()
{

  // If older CDI version is from before thermal conductivity units were updated,
  // get scale factor to convert unit basis.
  if ((sim.cdi_major_version == CONDUCTION_KAPPA_UNITS_CHANGE_MAJOR_VERSION
      && sim.cdi_minor_version <= CONDUCTION_KAPPA_UNITS_CHANGE_MINOR_VERSION)
      || sim.cdi_major_version < CONDUCTION_KAPPA_UNITS_CHANGE_MAJOR_VERSION) {

    dFLOAT scale_conductivity_slope = 1.0;
    dFLOAT scale_conductivity_offset;

    find_units_conversion_coefficients("LatticePower/LatticeLength/LatticeTemperature",
        "LatticeSpecificEnthalpy*LatticeLength*LatticeVelocity*LatticeDensity/LatticeTemperature",
        &scale_conductivity_slope, &scale_conductivity_offset);

    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      PHYSICS_DESCRIPTOR phys_desc = sim.flow_surface_physics_descs + i;
      CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc = phys_desc->phys_type_desc;

      // Check if CDI_PHYSICS_DESCRIPTOR contains CDI_VAR_ID_CONTACT_RESISTANCE
      // or CDI_VAR_ID_WALL_CONDUCTIVITY variables and apply conversion if they do.
      cdiINT32 cond_index = phys_type_desc->continuous_dp_index(CDI_VAR_ID_WALL_CONDUCTIVITY);
      if (cond_index > -1) {
        phys_desc->parameters()[cond_index].value *= scale_conductivity_slope;
      }

      // Contact resistance seems to already be in correct unit basis. Keeping here just
      // in case
      // cdiINT32 resist_index = phys_type_desc->continuous_dp_index(CDI_VAR_ID_CONTACT_RESISTANCE);
      // if (resist_index > -1) {
      //   phys_desc->parameters()[resist_index].value /= scale_conductivity_slope;
      // }
    }
  }
}

/* Conversion of conductivity and HTC results in error for cases where
 * global parameters are specified in lattice units (see PR-49751).
 * Following is a hacky workaround, copies the implementation from
 * find_units_conversion_coefficients() but assumes that we have hit
 * the conversion limbo when status comes out as UNITS_STATUS_UNDEFINED_CHANGEABLE.
 * There must be a better, more general way of handling this. */
VOID find_thermal_units_conversion_coefficients(cSTRING from_unit_name, cSTRING to_unit_name, 
                                                dFLOAT *slope, dFLOAT *offset)
{
  UNITS_UNIT from_unit;

  if (units_parse_unit(units_db, from_unit_name, &from_unit) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\" in fan data table", from_unit_name);
  UNITS_UNIT to_unit;
  if (units_parse_unit(units_db, to_unit_name, &to_unit) != UNITS_STATUS_OK)
    msg_internal_error("Unable to parse unit \"%s\" in fan data table", to_unit_name);

  UNITS_STATUS status = units_conversion_coefficients(units_db, from_unit, to_unit, slope, offset);
  if (status != UNITS_STATUS_OK && status != UNITS_STATUS_UNDEFINED_CHANGEABLE) {
    msg_internal_error("Unable to convert from unit \"%s\" to unit \"%s\"", from_unit_name, to_unit_name);
  } else if (status == UNITS_STATUS_UNDEFINED_CHANGEABLE) {
    *slope = 1.0;
    *offset = 0.0;
  }
}

VOID setup_ib_var_vector(sPHYSICS_VARIABLE *vars, 
                         sINT32 *eqn_len, 
                         cSTRING unit_class,
                         LGI_STREAM lgi_stream)
{
  ccDOTIMES(axis, 3) {
    if (eqn_len[axis] > 1) { // account for trailing NULL character
      vars[axis].name = xnew char[eqn_len[axis]];
      lgi_read(lgi_stream, (void *) vars[axis].name, size_t(eqn_len[axis]));
      asINT32 prog_index;
      if (g_case_program == NULL)
        msg_internal_error("Variable \"%s\" referenced in CDI file, but no equations"
                           " are present in CDI file.", vars[axis].name);
      output_variable_conversion_coefficients_and_index(vars[axis].name,
                                                        unit_class,
                                                        &prog_index,
                                                        &vars[axis].conversion_slope,
                                                        &vars[axis].conversion_offset);


      auINT32 dmask = exprlang_output_variable_dependence_mask(g_case_program,
                                                               prog_index);

      vars[axis].is_eqn = TRUE;
      vars[axis].is_space_varying = (dmask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
      vars[axis].is_time_varying = (dmask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
      vars[axis].program_index = prog_index;
#if 0 // Now a variable may only depend on a table that is re-read during simulation
      if (!vars[axis].is_space_varying && !vars[axis].is_time_varying)
        msg_internal_error("Constant variable \"%s\" not evaluated in CDI equations.", vars[axis].name);
#endif
    }
    else {
    vars[axis].is_eqn = FALSE;
    vars[axis].is_space_varying = FALSE;
    vars[axis].is_time_varying = FALSE;
    }
  }
}


void extract_time_varying_value(sPHYSICS_VARIABLE &var, BASETIME timestep, sFLOAT powertherm_time)
{
    STP_GEOM_VARIABLE point_or_normal[3] = {0};
    eval_program(g_case_program, point_or_normal, point_or_normal, timestep, powertherm_time);
    var.value = extract_output_variable_value(g_case_program, &var, var.name, NULL, point_or_normal);

}


VOID init_particle_physics_variable_internal(PHYSICS_VARIABLE var, cSTRING unit_class) {
  //Initialize a physics variable for use with a particle sprayer model or a particle emitter model.
  var->is_eqn = FALSE;
  var->is_space_varying = FALSE;
  var->is_time_varying = FALSE;

  if(var->name == nullptr)
    return;

  var->is_eqn = TRUE;
  asINT32 program_index;
  output_variable_conversion_coefficients_and_index(var->name,
                                                    unit_class,
                                                    &program_index,
                                                    &var->conversion_slope,
                                                    &var->conversion_offset);
  auINT32 dependency_mask = exprlang_output_variable_dependence_mask(g_case_program, program_index);
  var->is_space_varying = (dependency_mask & SPACE_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
  var->is_time_varying  = (dependency_mask & TIME_DEPENDENT_INPUT_VAR_DEPENDENCE_MASK) != 0;
  var->program_index = program_index;

 //PowerCASE always converts an equation that is constant in space
  //and time into a constant value and then hide the origional
  //equation.  But until there is PowerCASE support for a rain emitter
  //wind speed, we may encounter a constant equation that can be
  //evaluated once and for all here.
  if (var->is_eqn  &&
      !var->is_space_varying &&
      !var->is_time_varying) { //if a constant expression
    STP_GEOM_VARIABLE point[3] = {0,0,0};
    STP_GEOM_VARIABLE normal[3] = {0};
    eval_program(g_case_program, point, normal, 0, 0);
    var->value = extract_output_variable_value(g_case_program, var, var->name, global_eqn_error_handler, point);
    var->is_eqn = FALSE;
    }
}

VOID init_array_of_physics_variables(asINT32 n_vars, 
                                     PHYSICS_VARIABLE vars[], 
                                     cSTRING unit_classes[], 
                                     cSTRING parameter_names[], 
                                     BOOLEAN allowed_to_vary_in_time[], 
                                     BOOLEAN allowed_to_vary_in_space[]) {
  ccDOTIMES(var_index, n_vars) {
    init_particle_physics_variable_internal(vars[var_index],
                                            unit_classes[var_index]);
    if(vars[var_index]->is_time_varying & !allowed_to_vary_in_space[var_index]) {
      msg_error("%s parameter should not vary in time yet variable %s does.\n",
                parameter_names[var_index], vars[var_index]->name);
    }
    if(vars[var_index]->is_space_varying & !allowed_to_vary_in_time[var_index]) {
      msg_error("%s parameter should not vary in space yet variable %s does.\n",
                parameter_names[var_index], vars[var_index]->name);
    }
  }
}

VOID eval_array_of_space_varying_physics_variables(asINT32 n_vars,
                                                   PHYSICS_VARIABLE vars[],
                                                   STP_GEOM_VARIABLE point[3],
                                                   STP_GEOM_VARIABLE normal[3],
                                                   asINT32 timestep,
                                                   sFLOAT  powertherm_time){
  ccDOTIMES(var_index,  n_vars) {
    cSTRING var_name = vars[var_index]->name;
    if( vars[var_index]->is_eqn )
      if( vars[var_index]->is_space_varying ) {
        eval_program(g_case_program, point, normal, timestep, powertherm_time);
        vars[var_index]->value = extract_output_variable_value(g_case_program, vars[var_index], var_name, global_eqn_error_handler, point);
      }
  }
}

BOOLEAN eval_array_of_time_varying_physics_variables(asINT32 n_vars, PHYSICS_VARIABLE vars[], BASETIME timestep,
                                                     sFLOAT powertherm_time){
  BOOLEAN some_values_changed = FALSE;
  ccDOTIMES(var_index,  n_vars) {
    cSTRING var_name = vars[var_index]->name;
    if( vars[var_index]->is_eqn )
      if( vars[var_index]->is_time_varying && !vars[var_index]->is_space_varying ) { //time varying only
        STP_GEOM_VARIABLE eval_point[3] = {0.0, 0.0, 0.0};
        STP_GEOM_VARIABLE eval_normal[3] = {0.0, 0.0, 0.0};
        dFLOAT old_value = vars[var_index]->value;
        eval_program(g_case_program, eval_point, eval_normal, timestep, powertherm_time);
        vars[var_index]->value = extract_output_variable_value(g_case_program, vars[var_index], var_name, global_eqn_error_handler, eval_point);
        if(old_value != vars[var_index]->value )
          some_values_changed = TRUE;
      }
  }
  return some_values_changed;
}

VOID sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables() {
  //Initialize the following variables:
  //emission_rate
  PHYSICS_VARIABLE vars[1];
  vars[0] = &m_emission_rate;

 cSTRING possible_unit_classes[] = {
    "InvTime", "MassFlow", "VolumeFlow", "Velocity", "Density/LatticeTimeInc",
    "InvTime/LatticeLength", "MassFlow/LatticeLength", "VolumeFlow/LatticeLength", "Velocity/LatticeLength", "Density/LatticeTimeInc/LatticeLength"};
  cSTRING unit_classes[1];
  BOOLEAN is_tire = emitter_configuration_type() == sPARTICLE_EMITTER_CONFIGURATION_BASE::TIRE;
  
  switch(m_emission_rate_type) {
    case LGI::EMISSION_RATE_PARTICLE:
    unit_classes[0] = possible_unit_classes[is_tire ? 5 : 0];
    break;
  case LGI::EMISSION_RATE_MASSFLOW:
    unit_classes[0] = possible_unit_classes[is_tire ? 6 : 1];
    break;
  case LGI::EMISSION_RATE_VOLUMETRIC:
    unit_classes[0] = possible_unit_classes[is_tire ? 7 : 2];
    break;
  case LGI::EMISSION_RATE_DEPTH:
    unit_classes[0] = possible_unit_classes[is_tire ? 8 : 3];
    break;
  case LGI::EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME:
    unit_classes[0] = possible_unit_classes[is_tire ? 9 : 4];
    break;
  default:;
  }
  cSTRING parameter_names[1] = {"Emission Rate"};
  BOOLEAN allowed_to_vary_in_space[1] = {FALSE};
  BOOLEAN allowed_to_vary_in_time[1]  = {TRUE};
  init_array_of_physics_variables(1, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}

VOID sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                                STP_GEOM_VARIABLE normal[3],
                                                                                BASETIME timestep,
                                                                                sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = {&m_emission_rate};
  eval_array_of_space_varying_physics_variables(4, vars, point, normal, timestep, powertherm_time);
}

BOOLEAN sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time)
{
  PHYSICS_VARIABLE vars[] = {&m_emission_rate};
  return eval_array_of_time_varying_physics_variables(1, vars, timestep, powertherm_time);
}

VOID sFULL_CONE_NOZZLE_CONFIGURATION::initialize_physics_variables() {
  sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables();
  //m_cone_half_angle needs initialization
  PHYSICS_VARIABLE vars[] = {&m_cone_half_angle};
  cSTRING unit_classes[] = {"Angle"};
  cSTRING parameter_names[] = {"Cone Half Angle"};
  BOOLEAN allowed_to_vary_in_space[] = {TRUE};
  BOOLEAN allowed_to_vary_in_time[]  = {TRUE};
  init_array_of_physics_variables(1, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}

VOID sFULL_CONE_NOZZLE_CONFIGURATION::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                          STP_GEOM_VARIABLE normal[3],
                                                                          BASETIME timestep,
                                                                          sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = {&m_cone_half_angle};
  eval_array_of_space_varying_physics_variables(1, vars, point, normal, timestep, powertherm_time);
}

BOOLEAN sFULL_CONE_NOZZLE_CONFIGURATION::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time) 
{
  BOOLEAN some_values_changed = 
    sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(timestep, powertherm_time);
  PHYSICS_VARIABLE vars[] = {&m_cone_half_angle};
  return some_values_changed | eval_array_of_time_varying_physics_variables(1, vars, timestep, powertherm_time);
}

VOID sHOLLOW_CONE_NOZZLE_CONFIGURATION::initialize_physics_variables() {
  sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables();
  //m_angle_stddev
  PHYSICS_VARIABLE vars[] = {&m_mean_angle, &m_angle_stddev};
  cSTRING unit_classes[] = {"Angle","Angle"};
  cSTRING parameter_names[] = {"Mean Angle", "Angle stddev"};
  BOOLEAN allowed_to_vary_in_space[] = {TRUE, TRUE};
  BOOLEAN allowed_to_vary_in_time[]  = {TRUE, TRUE};
  init_array_of_physics_variables(2, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}

VOID sHOLLOW_CONE_NOZZLE_CONFIGURATION::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                             STP_GEOM_VARIABLE normal[3],
                                                                             BASETIME timestep,
                                                                             sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = {&m_mean_angle, &m_angle_stddev};
  eval_array_of_space_varying_physics_variables(2, vars, point, normal, timestep, powertherm_time);
}

BOOLEAN sHOLLOW_CONE_NOZZLE_CONFIGURATION::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time)
{
  BOOLEAN some_values_changed = 
    sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(timestep, powertherm_time);
  PHYSICS_VARIABLE vars[] = {&m_mean_angle, &m_angle_stddev};
  return some_values_changed | eval_array_of_time_varying_physics_variables(2, vars, timestep, powertherm_time);
}

VOID sELLIPTICAL_CONE_NOZZLE_CONFIGURATION::initialize_physics_variables() {
  sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables();
  PHYSICS_VARIABLE vars[] = {&m_major_cone_half_angle_distribution_param1,
                             &m_major_cone_half_angle_distribution_param2,
                             &m_minor_cone_half_angle_distribution_param1,
                             &m_minor_cone_half_angle_distribution_param2};
  cSTRING unit_classes[] = {"Angle","Angle","Angle","Angle"};
  cSTRING parameter_names[] = {"Major half angle",
                               "Major half angle stddev",
                               "Minor half angle",
                               "Minor half angle stddev"};
  BOOLEAN allowed_to_vary_in_space[] = {TRUE, TRUE, TRUE, TRUE};
  BOOLEAN allowed_to_vary_in_time[]  = {TRUE, TRUE, TRUE, TRUE};
  init_array_of_physics_variables(4, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}

VOID sELLIPTICAL_CONE_NOZZLE_CONFIGURATION::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                                 STP_GEOM_VARIABLE normal[3],
                                                                                 BASETIME timestep,
                                                                                 sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = {&m_major_cone_half_angle_distribution_param1,
                             &m_major_cone_half_angle_distribution_param2,
                             &m_minor_cone_half_angle_distribution_param1,
                             &m_minor_cone_half_angle_distribution_param2};
  eval_array_of_space_varying_physics_variables(4, vars, point, normal, timestep, powertherm_time);
}

BOOLEAN sELLIPTICAL_CONE_NOZZLE_CONFIGURATION::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time)
{
  BOOLEAN some_values_changed = 
    sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(timestep, powertherm_time);
  PHYSICS_VARIABLE vars[] = {&m_major_cone_half_angle_distribution_param1,
                             &m_major_cone_half_angle_distribution_param2,
                             &m_minor_cone_half_angle_distribution_param1,
                             &m_minor_cone_half_angle_distribution_param2};
  return some_values_changed | eval_array_of_time_varying_physics_variables(4, vars, timestep, powertherm_time);
}

VOID sRAIN_EMITTER_CONFIGURATION::initialize_physics_variables() {

  sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables();

  //m_diameter_mean;
  //m_diameter_dist_param2;
  PHYSICS_VARIABLE vars[] = { &m_diameter_distribution_param1,  
                              &m_diameter_distribution_param2,
                              &m_free_stream_velocity_x,
                              &m_free_stream_velocity_y,
                              &m_free_stream_velocity_z };
  
  cSTRING unit_classes[] = {"Length",
                            "Length",
                            "Velocity", 
                            "Velocity",
                            "Velocity"};
  
  cSTRING parameter_names[] = {"Mean Diameter",
                               "Diameter stddev/range",
                               "Rain emitter air velocity x",  
                               "Rain emitter air velocity y",  
                               "Rain emitter air velocity z"};
  
  BOOLEAN allowed_to_vary_in_space[] = {FALSE,
                                        FALSE,
                                        FALSE,
                                        FALSE,
                                        FALSE};
  
  BOOLEAN allowed_to_vary_in_time[]  = {TRUE,
                                        TRUE,
                                        TRUE,
                                        TRUE,
                                        TRUE};
  
  init_array_of_physics_variables(5, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}


VOID sRAIN_EMITTER_CONFIGURATION::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                                 STP_GEOM_VARIABLE normal[3],
                                                                                 BASETIME timestep,
                                                                                 sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = { &m_diameter_distribution_param1,  
                              &m_diameter_distribution_param2};
  eval_array_of_space_varying_physics_variables(2, vars, point, normal, timestep, powertherm_time);
}
BOOLEAN sRAIN_EMITTER_CONFIGURATION::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time)
{

  //Evaluate the release rate
  BOOLEAN some_values_changed = 
    sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(timestep, powertherm_time);
  
  //Evaluate the diameter distribution parameters.
  PHYSICS_VARIABLE diameter_vars[] = { &m_diameter_distribution_param1,  
                                       &m_diameter_distribution_param2};
  some_values_changed |= eval_array_of_time_varying_physics_variables(2, diameter_vars, timestep, powertherm_time);
  

  //Evaluate the free stream velocity variables. We only check them
  //for changes after being converted to the global body fixed reference
  //frame.
  PHYSICS_VARIABLE velocity_vars[] = { &m_free_stream_velocity_x,
                                       &m_free_stream_velocity_y,
                                       &m_free_stream_velocity_z};
 
  //Evaluate the velocity variables.
  eval_array_of_time_varying_physics_variables(3, velocity_vars, timestep, powertherm_time);
  
  //Convert the velocity vector out of the users's csys to the lattice csys
  sPARTICLE_VAR new_free_stream_velocity[N_SPACE_DIMS]; 
  new_free_stream_velocity[0] = m_free_stream_velocity_x.value;
  new_free_stream_velocity[1] = m_free_stream_velocity_y.value;
  new_free_stream_velocity[2] = m_free_stream_velocity_z.value;

  CSYS free_stream_vel_csys = &sim.csys_table[m_free_stream_velocity_csys];
  xform_vector(new_free_stream_velocity, free_stream_vel_csys);
  
  //Convert to the global body fixed reference frame if needed.
  asINT32 vel_ref_frame_index = m_free_stream_velocity_refrerence_frame_index;
  if(vel_ref_frame_index != SRI_GLOBAL_REF_FRAME_INDEX) {
    STP_VEC_FLOAT simvol_midpoint[N_SPACE_DIMS]; //Probably ought to use the emitter geometry midpoint here but it's assciated with the emitter and not the emitter configuration. 
    vscale(simvol_midpoint, 0.5, sim.simvol_size);
    vinc(simvol_midpoint, sim.case_origin);
    STP_VEC_FLOAT  input_velocity[N_SPACE_DIMS];
    vcopy(input_velocity, new_free_stream_velocity);
    STP_VEC_FLOAT  output_velocity[N_SPACE_DIMS];
    vzero(output_velocity);
    convert_vel_between_ref_frames(input_velocity, 
                                   simvol_midpoint, 
                                   output_velocity, 
                                   vel_ref_frame_index,
                                   SRI_GLOBAL_REF_FRAME_INDEX);
    vcopy(new_free_stream_velocity, output_velocity);
  } 
 
  //Check if the velocity in the body fixed reference frame changed.
  ccDOTIMES(i, N_SPACE_DIMS) {
    if (new_free_stream_velocity[i] != m_free_stream_velocity[i]) {
      some_values_changed = true;
      break;
    }
  }
  
  //Store the new values
  vcopy(m_free_stream_velocity, new_free_stream_velocity);
  return some_values_changed;
}

VOID sTIRE_EMITTER_CONFIGURATION::initialize_physics_variables() {
  sPARTICLE_EMITTER_CONFIGURATION_BASE::initialize_physics_variables();
  PHYSICS_VARIABLE vars[] = {&m_cdi_angular_emission_distribution_parameter,
                             &m_cdi_angular_emission_distribution_ratio };
  cSTRING unit_classes[] = {"Dimensionless",
                            "Dimensionless"};
  cSTRING parameter_names[] = {"Spatial distribution parameter",
                               "Spatial distribution ratio"};
  BOOLEAN allowed_to_vary_in_space[] = {FALSE,
                                        FALSE};
  BOOLEAN allowed_to_vary_in_time[]  = {TRUE,
                                        TRUE};
  init_array_of_physics_variables(2, vars, unit_classes, parameter_names, allowed_to_vary_in_space, allowed_to_vary_in_time);
}

VOID sTIRE_EMITTER_CONFIGURATION::eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                                          STP_GEOM_VARIABLE normal[3],
                                                                          BASETIME timestep,
                                                                          sFLOAT powertherm_time) {
  PHYSICS_VARIABLE vars[] = {&m_cdi_angular_emission_distribution_parameter,
                             &m_cdi_angular_emission_distribution_ratio };
  eval_array_of_space_varying_physics_variables(2, vars, point, normal, timestep, powertherm_time);
}
BOOLEAN sTIRE_EMITTER_CONFIGURATION::eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time)
{
  BOOLEAN some_values_changed = 
    sPARTICLE_EMITTER_CONFIGURATION_BASE::eval_time_only_varying_physics_variables(timestep, powertherm_time);
  PHYSICS_VARIABLE vars[] = {&m_cdi_angular_emission_distribution_parameter,
                             &m_cdi_angular_emission_distribution_ratio };
  return some_values_changed | eval_array_of_time_varying_physics_variables(2, vars, timestep, powertherm_time);
}
