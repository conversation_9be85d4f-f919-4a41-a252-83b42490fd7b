/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cp.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                       Created Mon, Dec 10, 2008
//----------------------------------------------------------------------------

#ifndef _SIMENG_DGF_READER_H_
#define _SIMENG_DGF_READER_H_
#include "common_sp.h"

extern LGI_STREAM g_lgi_stream;

template< typename TYPE >
inline VOID read_lgi_head(TYPE &object)
{
  lgi_read_next_head(g_lgi_stream, object);
}

template< typename TYPE >
inline VOID read_lgi_head(TYPE *buf, size_t n_bytes)
{
  lgi_read_next_head(g_lgi_stream, buf, n_bytes);
}

template< typename TYPE >
inline VOID read_lgi(TYPE &object)
{
  lgi_read(g_lgi_stream, object);
}

template< typename TYPE >
inline VOID read_lgi(TYPE *buf, size_t n_bytes)
{
  lgi_read(g_lgi_stream, buf, n_bytes);
}

typedef struct sDGF_READER
{
  VOID read_records_from_stream();

protected:
  VOID open_dgf_stream();
  VOID close_dgf_stream();

  VOID read_global_part_names();
  VOID read_global_face_names();
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  VOID read_vertices_header();
//#endif
  VOID read_control_record();
  VOID read_coupling_meas_window();
  VOID read_smart_seed_control_record();
  VOID read_smart_seed_initial_movb_xforms();
  VOID read_smart_seed_initial_lrf_rotations();
  VOID read_global_info_record();
  VOID read_cdi_global_info();
  VOID read_time_coupling_phases();
  VOID read_cp_to_sp_info();
  VOID validate_version(asINT32 version);
  
#if BUILD_5G_LATTICE
  VOID read_multi_component();
#endif

  VOID read_scalar_material();
  VOID read_meas_windows();
  VOID read_quad_mesh_info();
  VOID read_meas_window_master_sps_and_output_times();
  VOID read_meas_cell_reference_frame_conflicts();
  VOID read_meas_vars_list();
  VOID read_meas_window_ckpt_data();
  VOID read_trajectory_id_map();
  template <typename UBLK_PROC> VOID read_ublk_table(STP_REALM realm);
  VOID read_deforming_tire_table();
  VOID read_bsurfel_table();
  VOID read_surfel_table(STP_REALM realm);
  VOID read_gap_contact_table();
  VOID read_averaged_contacts();
  VOID read_seed_from_meas_scale_factors();
  VOID read_disc_surfel_record();
  VOID read_neighbor_mask_table();
  VOID read_nmi_tables();

  VOID read_ice_accretion_parameters();
  VOID read_particle_globals();
  VOID read_particle_materials();
  VOID read_anisotropic_part_axis();
  VOID read_particle_emitter_configurations();
  VOID read_particle_emitters();

  VOID read_particle_surface_interaction_parameters();
  VOID read_particle_screens();
  VOID read_virtual_wipers();
  VOID read_data_curves();
  VOID read_particle_property_rng_state();
  VOID read_particle_emitter_ckpt();
  VOID read_particle_emitters_state_ckpt();
  VOID read_surface_emitter_geometry();
  VOID read_implicit_solver_data();

}* DGF_READER;

extern sDGF_READER g_dgf_reader; // reference to global instance

#endif// _SIMENG_DGF_READER_H_
