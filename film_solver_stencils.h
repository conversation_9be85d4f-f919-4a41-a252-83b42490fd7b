#ifndef FILM_SOLVER_STENCILS_H_
#define FILM_SOLVER_STENCILS_H_
#include "common_sp.h"
#include "particle_solver_data.h"
#include "surfel.h"
#include "surfel_vertices.h"
#include <vector>
#include "simulator_namespace.h"

//Define some vectors that are needed for for sufel p_data blocks.
//Vector of references to other surfels in a surfel's stencil:

//Vector of references to structs containing info needed to initialize the stencils:
typedef struct sFILM_STENCIL_INIT_INFO {
  asINT32 sharp_edge_count_to_center;
  sSURFEL* surfel;

  sFILM_STENCIL_INIT_INFO() {}
  sFILM_STENCIL_INIT_INFO(sSURFEL* init_surfel, asINT32 init_edge_count) {
    surfel = init_surfel;
    sharp_edge_count_to_center = init_edge_count;
  }

}* FILM_STENCIL_INIT_INFO;


//struct sFILM_STENCIL_INIT_INFO_ORDER;
typedef struct sFILM_STENCIL_INIT_INFO_PREDICATE {
  sSURFEL* reference_surfel;
  sFILM_STENCIL_INIT_INFO_PREDICATE(sSURFEL* surfel){
    reference_surfel = surfel;
  }

  //define an order operator
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  bool operator()(sFILM_STENCIL_INIT_INFO const &a, sFILM_STENCIL_INIT_INFO const &b) const;
#endif
  bool operator()(sSURFEL_STENCIL_INIT_INFO const &a, sSURFEL_STENCIL_INIT_INFO const &b) const;
#if 0
  //define an identity operator
  bool operator()(sFILM_STENCIL_INIT_INFO const &a) const;
#endif
}*FILM_STENCIL_INIT_INFO_ORDER;

typedef std::vector<sFILM_STENCIL_INIT_INFO> sFILM_STENCIL_INIT_INFO_VECTOR;
typedef sFILM_STENCIL_INIT_INFO_VECTOR* FILM_STENCIL_INIT_INFO_VECTOR;


typedef struct sSTENCIL_SIM_ERROR_DATA {
  SP_EEP_TYPES error_code;
  STP_SCALE scale;
  STP_GEOM_VARIABLE centroid[3];
  std::string comment;
} *STENCIL_SIM_ERROR_DATA;

#define MAX_STENCIL_ERROR_LIMIT (2<<22)

dFLOAT minor_determinate(int i, int j, dFLOAT A[3][3]);
dFLOAT invert_rank3_matrix(dFLOAT A[3][3], dFLOAT A_inv[3][3]);
VOID retrieve_ghost_side_interface_stencils(asINT32 pass);
VOID retrieve_dynamics_side_interface_stencils(asINT32 pass);
long int measure_film_solver_stencil_memory_usage();

typedef struct sSURFELS_ID_SET {
  std::unordered_map<SHOB_ID, SURFEL> set_of_ids;
  VOID clear_set_of_ids() {
    set_of_ids.clear();
  }

  SURFEL find(SHOB_ID id) {
    auto it = set_of_ids.find(id);
    if (it != set_of_ids.end()) {
      return it->second;
    } else {
      return nullptr;
    }
  }
  VOID construct_set_of_ids();

  VOID insert_new_id(SURFEL surfel) {
    std::pair<SHOB_ID, SURFEL> id_surfel(surfel->id(), surfel);
    set_of_ids.insert(id_surfel);
  }

  BOOLEAN is_id_in_set(SHOB_ID id) {
    auto it = set_of_ids.find(id);
    return it != set_of_ids.end();
  }
}*SURFELS_ID_SET;
extern sSURFELS_ID_SET g_surfel_ids;

#define ENABLE_CONNECTIVITY_INIT_DATA
#endif
