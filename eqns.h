/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_EQNS_H
#define _SIMENG_EQNS_H

#include <vector>
#include "common_sp.h"
#include "event_queue.h"
#include "bsurfel_tire.h"

#include SMEM_H
extern EXPRLANG_PROGRAM g_case_program;

VOID read_units_database(LGI_STREAM stream);
VOID read_equations(LGI_STREAM stream);
VOID read_global_ref_frame(LGI_STREAM stream);
VOID read_lrf_physics_descriptors(LGI_STREAM stream);
VOID read_gravity_buoyancy(LGI_STREAM stream);
VOID read_body_force_descriptors(LGI_STREAM stream);
VOID read_fluid_physics_descriptors(LGI_STREAM stream);
VOID read_scas_chunk(LGI_STREAM stream);
VOID read_hcsd_physics_descriptors(LGI_STREAM stream);
VOID read_conduction_solid_materials(LGI_STREAM stream);
VOID convert_conduction_sim_info_units();
VOID read_radiation_surface_conditions(LGI_STREAM stream);
VOID read_shell_config_physics_descriptors(LGI_STREAM stream);
VOID read_movb_physics_descriptors(LGI_STREAM stream);
VOID read_flow_surface_physics_descriptors(LGI_STREAM stream);
VOID read_thermal_surface_physics_descriptors(LGI_STREAM stream);
VOID read_face_id_to_flow_surface_physics(LGI_STREAM stream);
VOID read_bc_turb_vel(LGI_STREAM stream);
VOID compute_dependencies_from_tables_to_physics_descs();
VOID compute_dependencies_from_coupling_to_physics_descs();
VOID read_case_origin(LGI_STREAM stream);
VOID read_csys(LGI_STREAM stream);
VOID add_initial_table_reads_to_event_queue();

// CSYS 0 is validated as the default at initialization
#define is_csys_index_non_default(csys_index) ((csys_index) > 0)

extern UNITS_DB units_db;
extern dFLOAT g_lattice_power_to_power_density_slope;
extern dFLOAT g_lattice_power_to_power_density_offset;
extern dFLOAT g_lattice_power_to_heat_flux_slope;
extern dFLOAT g_lattice_power_to_heat_flux_offset;

VOID read_tables(LGI_STREAM stream);
VOID set_randt_initial_update_time(BASETIME time);

typedef struct sCSYS  {
  STRING name;
  sG3_XFORM g_to_l_xform;   /* 2D section plane transform */
  sG3_XFORM l_to_g_xform;   /* 2D section plane transform */

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sCSYS* h_ptr);

  
} *CSYS;

__HOST__DEVICE__
inline VOID xform_vector(dFLOAT dvec[3], CSYS csys)
{
  sG3_VEC vec;
  vec.vcoord[0] = dvec[0];
  vec.vcoord[1] = dvec[1];
  vec.vcoord[2] = dvec[2];

  vec = g3_xform_vec(vec, csys->l_to_g_xform);

  dvec[0] = vec.vcoord[0];
  dvec[1] = vec.vcoord[1];
  dvec[2] = vec.vcoord[2];
}

template < typename FLOAT_TYPE,  typename MATRIX_TYPE >
__HOST__DEVICE__ VOID rotate_vector( FLOAT_TYPE input_vec[3],
				     FLOAT_TYPE output_vec[3],
				     MATRIX_TYPE trans_matrix[3][3],
				     BOOLEAN transpose = false )
{
  FLOAT_TYPE input_vec0 = input_vec[0];
  FLOAT_TYPE input_vec1 = input_vec[1];
  FLOAT_TYPE input_vec2 = input_vec[2];
  if (! transpose) {
    ccDOTIMES(i, 3) {
      output_vec[i] = input_vec0 * trans_matrix[i][0] + input_vec1 * trans_matrix[i][1] + input_vec2 * trans_matrix[i][2];
    }
  } else {
    ccDOTIMES(i, 3) {
      output_vec[i] = input_vec0 * trans_matrix[0][i] + input_vec1 * trans_matrix[1][i] + input_vec2 * trans_matrix[2][i];
    }
  }
}



//template < typename FLOAT_TYPE, typename MATRIX_TYPE >
//VOID rotate_vector( FLOAT_TYPE input_vec[3], FLOAT_TYPE output_vec[3], MATRIX_TYPE trans_matrix[3][3]);

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
VOID xform_point(dFLOAT dvec[3], CSYS csys);
VOID xform_point_inv(dFLOAT dvec[3], CSYS csys);
//#endif

struct sPHYSICS_DESCRIPTOR; // To allow sTABLE to reference this struct

typedef struct sDEPENDENT_PHYSICS_DESCRIPTOR {
  sPHYSICS_DESCRIPTOR *physics_desc;
  sDEPENDENT_PHYSICS_DESCRIPTOR *next;
} *DEPENDENT_PHYSICS_DESCRIPTOR;

typedef struct sTABLE {
  STRING name;
  cBOOLEAN read_during_sim;
  cBOOLEAN ready;
  STP_REALM time_rel_realm;
  TIMESTEP period;
  TIMESTEP end_time;
  TIMESTEP next_periodic_read;
  size_t bytes_allocated;  // space reserved for initial serialized version of table
  char smem_prefix[LGI_TABLE_SMEM_NAME_LEN];
  cSHARED_MEMORY *smem;
  EXPRLANG_VALUE vtable;
  // @@@ The serialized table should be the same as vtable except that vtable is wrapped
  // in an EXPRLANG_VALUE object. This can be fixed when we switch to EQN.
  char *serialized_table;

  DEPENDENT_PHYSICS_DESCRIPTOR dependent_physics_descs;

  DEPENDENT_PHYSICS_DESCRIPTOR physics_descs_dependent_via_curved_hx;

  BOOLEAN add_to_eqns();
} *TABLE;

typedef struct sCOUPLING_MODEL {
  STRING name;
  cBOOLEAN couple_during_sim_p;
  cBOOLEAN init_pf_bc_coupling_p;
  cBOOLEAN ready;
  TIMESTEP period;
  TIMESTEP end_time;
  TIMESTEP next_periodic_read;
  sINT32 n_vars;
  DGF_COUPLING_VAR_TYPE *var_types;
  asINT32 nsurfels;
  STP_SURFEL_ID *surfel_ids;
  DEPENDENT_PHYSICS_DESCRIPTOR dependent_physics_descs;

  // for variable PowerTherm coupling
  std::vector<sCOUPLING_PHASE_TIME_DESC_SP> m_coupling_phase_descs;
} *COUPLING_MODEL;

typedef struct sPHYSICS_VARIABLE {
  cBOOLEAN is_eqn;      // is_space_varying || is_time_varying
  cBOOLEAN is_space_varying;
  cBOOLEAN is_time_varying;

  // These 4 fields are only meaningful of IS_EQN is true
  sINT16 program_index;
  cSTRING name;
  dFLOAT conversion_slope;
  dFLOAT conversion_offset;

  dFLOAT value; // evaluated initially even if eqn

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sPHYSICS_VARIABLE* h_ptr,
				      size_t n_vars);  

  // CVALUE returns the variable value on the assumption it is neither
  // time nor space varying. If the variable was expressed via an equation
  // (which is neither time nor space varying), the value was set at
  // the time this object was constructed.
  __HOST__DEVICE__ dFLOAT cvalue() {
#if !GPU_COMPILER    
    if (is_space_varying || is_time_varying)      
      msg_internal_error("Unexpected space or time varying variable");
#endif    
    return value;
  }

  __HOST__DEVICE__ dFLOAT svalue() {     // shared value
#if !GPU_COMPILER        
    if (is_space_varying && !is_time_varying)
      msg_internal_error("Shared variable is space varying but not time varying.");
#endif    
    return value;       // returns 0 if time varying
  }


  // A variable is sharable across many voxels if either (a) it is a
  // constant, or (b) it is time-varying. We want to evaluate a
  // spatially varying variable at initialization time if possible
  // (i.e., if it is not a function of time). Thus a spatially varying
  // variable that is *not* time varying cannot be shared.
  __HOST__DEVICE__ BOOLEAN is_sharable() { return (!is_space_varying) || (is_time_varying); }

  __HOST__DEVICE__ BOOLEAN is_constant() { return (!is_space_varying) && (!is_time_varying); }

  __HOST__DEVICE__ BOOLEAN is_time_varying_only() { return is_time_varying && !is_space_varying; }

} *PHYSICS_VARIABLE;

typedef struct sCOND_PHYSICS_VARIABLE : sPHYSICS_VARIABLE {
  cBOOLEAN is_temp_varying;
  __HOST__DEVICE__ BOOLEAN is_temp_varying_only() { return is_temp_varying && !is_eqn; }

} *COND_PHYSICS_VARIABLE;

typedef enum {
  EQN_ERROR_FPE,

  // These two error types are currently not used.
  EQN_ERROR_TOO_LARGE,
  EQN_ERROR_TOO_SMALL
} EQN_ERROR_TYPE;


typedef enum {
  IS_EQN = 1,
  IS_DATA_CURVE = 2
} PARM_TYPE;

typedef VOID (*EQN_ERROR_HANDLER)(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                                  cSTRING cdi_desc_var_name, vFLOAT value,
                                  vFLOAT aux_value,     // min or max
                                  STP_GEOM_VARIABLE point[3]);

enum {
  GRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR_TYPE,
  BODY_FORCE_PHYSICS_DESCRIPTOR_TYPE,
  FLUID_PHYSICS_DESCRIPTOR_TYPE,
  SURFACE_PHYSICS_DESCRIPTOR_TYPE,
  MOVB_PHYSICS_DESCRIPTOR_TYPE,
  GRF_PHYSICS_DESCRIPTOR_TYPE,
  LRF_PHYSICS_DESCRIPTOR_TYPE,
  SHELL_PHYSICS_DESCRIPTOR_TYPE,
  NUMBER_OF_PHYSICS_DESCRIPTOR_TYPES
};

struct sDEPENDENT_SHOB_DYN_GROUP; // So this can be referenced in PHYSICS_DESCRIPTOR

typedef class sPHYSICS_DESCRIPTOR {
public:
  auINT8 m_type_index;  // Physics descriptor type index
  cSTRING name;         // Face name for surface physics
  EXPRLANG_PROGRAM parameter_program;

  cBOOLEAN some_parameter_time_varying;
  cBOOLEAN some_parameter_time_varying_only;
  cBOOLEAN some_parameter_time_and_space_varying;
  cBOOLEAN all_parameters_sharable;
  // A constant parameter will appear in CDI as a variable if it depends on a table
  // that is re-read during simulation. This flag is only used during initialization
  // to ensure that parameter_program is evaluated at least once.
  cBOOLEAN some_constant_parameter_in_need_of_eval;

  sINT8 init_from_coupling_model_p;
  sINT16 coupling_model_index;
  
  sINT8 n_layers = -1;
  // some voxel or surfel on this SP uses this descriptor (only used for fan today)
  cBOOLEAN requires_reduction;  // requires global reduction
  sINT16 index;                 // index in table

  sDEPENDENT_SHOB_DYN_GROUP *dependent_shob_dyn_groups;

  STRING data_table_string;

  EXPRLANG_PROGRAM initial_condition_program;
  CDI_PHYS_TYPE_DESCRIPTOR phys_type_desc;
  sINT16 n_parameters;          // copied from CDI_PHYS_TYPE_DESC (n_continuous_dp)
  sINT16 n_initial_conditions;  // copied from CDI_PHYS_TYPE_DESC

  PHYSICS_VARIABLE _parameters;                 // vector of parameters
  PHYSICS_VARIABLE _initial_conditions;         // vector of initial conditions

  //new for LB_UDS
  sINT16 n_uds_parameters;                          
  sINT16 n_uds_initial_conditions;                 
  PHYSICS_VARIABLE _uds_parameters;                 // vector of uds parameters  
  PHYSICS_VARIABLE _uds_initial_conditions;         // vector of uds initial conditions
  cBOOLEAN some_uds_parameter_time_varying;
  cBOOLEAN all_uds_parameters_sharable;

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sPHYSICS_DESCRIPTOR* h_ptr,
				      size_t n_physics_desc);

  // PARAMETERS and INITIAL_CONDITIONS are intended to be specialized by each more
  // specific physics descriptor
  __HOST__DEVICE__ PHYSICS_VARIABLE parameters() { return _parameters; }
  __HOST__DEVICE__  PHYSICS_VARIABLE initial_conditions() { return _initial_conditions; }

  __HOST__DEVICE__ PHYSICS_VARIABLE uds_parameters() { return _uds_parameters; }
  __HOST__DEVICE__  PHYSICS_VARIABLE uds_initial_conditions() { return _uds_initial_conditions; }

  // Ultimately we want separate programs for computing initial conditions,
  // for computing t=0 parameters, and for computing t>0 parameters. The
  // t>0 program does not need to evaluate those parameters which are only
  // space varying (i.e., not time varying). The t=0 program only needs to
  // evaluate those parameters which are only space varying.  
  VOID eval_initial_condition_program(STP_GEOM_VARIABLE point[3], STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h);  // t = 0

  VOID eval_space_varying_only_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h); // t = 0
  VOID eval_space_and_table_varying_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h);
  VOID eval_temp_varying_only_parameter(dFLOAT temperature_in);
  VOID eval_time_varying_only_parameter_program(BASETIME timestep, sFLOAT powertherm_time, EQN_ERROR_HANDLER h);
  VOID eval_time_and_space_varying_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], BASETIME timestep, sFLOAT powertherm_time, EQN_ERROR_HANDLER h);

  VOID eval_space_and_table_varying_uds_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h); //LB_UDS
  VOID eval_time_and_space_varying_uds_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], BASETIME timestep, sFLOAT powertherm_time, EQN_ERROR_HANDLER h); //LB_UDS

  BOOLEAN is_body_physics_descriptor() { return (m_type_index == BODY_FORCE_PHYSICS_DESCRIPTOR_TYPE); }
  BOOLEAN is_fluid_physics_descriptor() { return (m_type_index == FLUID_PHYSICS_DESCRIPTOR_TYPE); }
  BOOLEAN is_surface_physics_descriptor() { return (m_type_index == SURFACE_PHYSICS_DESCRIPTOR_TYPE); }
  BOOLEAN is_shell_physics_descriptor() { return (m_type_index == SHELL_PHYSICS_DESCRIPTOR_TYPE); }
  BOOLEAN is_movb_physics_descriptor() { return (m_type_index == MOVB_PHYSICS_DESCRIPTOR_TYPE); }
  BOOLEAN is_lrf_physics_descriptor() { return (m_type_index == LRF_PHYSICS_DESCRIPTOR_TYPE); }

} *PHYSICS_DESCRIPTOR;




typedef struct sSHELL_LAYER_PARAMETERS {
  sPHYSICS_VARIABLE layer_special_material;
  sPHYSICS_VARIABLE material_index;
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE anisotropy_use_part_axis;
  sPHYSICS_VARIABLE axis[3];
  sPHYSICS_VARIABLE imposed_heat;
  sPHYSICS_VARIABLE thickness;
  sPHYSICS_VARIABLE specify_heat_via_power_density;
  sPHYSICS_VARIABLE imposed_heat_in_power;
  sPHYSICS_VARIABLE layer_contact_area;
  sPHYSICS_VARIABLE front_rad_surf_cond;
  sPHYSICS_VARIABLE back_rad_surf_cond;
  sPHYSICS_VARIABLE face_area;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "SHELL_PARAMETERS",
                            BOOLEAN is_called_from_derived_class = FALSE) {
    asINT32 i = 0;
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SOLID_MATERIAL
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SHELL_LAYER_THICKNESS
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SPECIAL_LAYER
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_LAYER_CONTACT_AREA
        || ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RADIATION_SURFACE_COND
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }

} *SHELL_LAYER_PARAMETERS;

typedef struct sSHELL_LAYER_INITIAL_CONDITIONS {
  sPHYSICS_VARIABLE temperature;
  VOID check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd) {
    if ( (ptd->initial_condition_var[0]->id != CDI_VAR_ID_TEMP))
      msg_internal_error("SHELL_INITIAL_CONDITIONS is not consistent with CDI physics type %d.", ptd->cdi_physics_type);
  }

  asINT32 num_of_vars() {
    return sizeof(this) / sizeof(sPHYSICS_VARIABLE);
  }

} *SHELL_LAYER_INITIAL_CONDITIONS;

typedef struct sSHELL_CONFIG_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  SHELL_LAYER_PARAMETERS parameters(asINT8 nth_layer)
    { return (((SHELL_LAYER_PARAMETERS)_parameters) + nth_layer); }
  SHELL_LAYER_INITIAL_CONDITIONS initial_conditions(asINT8 nth_layer) {
    return (((SHELL_LAYER_INITIAL_CONDITIONS)_initial_conditions) + nth_layer);
  }

  VOID check_consistency(){
    ccDOTIMES(nth_layer, this->n_layers)
        parameters(nth_layer)->check_consistency(phys_type_desc);
  }
  VOID set_if_initial_conditions_constant() {
    _are_initial_conditions_constant = TRUE;
    PHYSICS_VARIABLE ic = sPHYSICS_DESCRIPTOR::initial_conditions();
    ccDOTIMES(i, n_initial_conditions) {
      if (ic->is_space_varying) _are_initial_conditions_constant = FALSE;
      ic++;
    }
  }
  BOOLEAN are_initial_conditions_constant() { return _are_initial_conditions_constant; }
  cBOOLEAN _are_initial_conditions_constant;

} *SHELL_CONFIG_PHYSICS_DESCRIPTOR;


typedef struct sGRF_PARAMETERS {
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE ref_point[3];

  // Either the user provides the ref point velocity or the ref point acceleration, not both.
  // If the velocity is zero, we assume the user specified an acceleration. It is perfectly
  // fine it the acceleration is zero as well.

  // All these values are relative to the body-fixed (non-inertial) frame
  sPHYSICS_VARIABLE ref_vel[3];
  sPHYSICS_VARIABLE ref_accel[3];
  sPHYSICS_VARIABLE ref_vel_t0[3]; // vel at t=0
  sPHYSICS_VARIABLE angular_vel[3];

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "GRF_PARAMETERS",
                            BOOLEAN check_cdi_phys_type = TRUE) {

    if (check_cdi_phys_type
        && (CDI_PHYS_TYPE_NON_INERTIAL_FRAME != ptd->cdi_physics_type)) {
      msg_internal_error("LGI and CDI file inconsistency: The LGI file expects a non-inertial frame definition"
                         " (CDI type %d), but the CDI file contains type %d.",
                         CDI_PHYS_TYPE_NON_INERTIAL_FRAME, ptd->cdi_physics_type);
    }

    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_X_T0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Y_T0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VEL_Z_T0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL_Z)) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }

} *GRF_PARAMETERS;


typedef struct sGRF_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  GRF_PARAMETERS parameters()
    { return (GRF_PARAMETERS)_parameters; }

  VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }

  cBOOLEAN is_angular_vel_time_varying;
  cBOOLEAN is_ref_vel_time_varying;
  cBOOLEAN is_ref_accel_time_varying;
  cBOOLEAN is_motion_via_accel;

} *GRF_PHYSICS_DESCRIPTOR;


typedef struct sLRF_PARAMETERS {
  sPHYSICS_VARIABLE angular_vel;
  sPHYSICS_VARIABLE start_point[3];     // start point of axis
  sPHYSICS_VARIABLE end_point[3];       // end point of axis
  sPHYSICS_VARIABLE type;               // CDI_STATIC_LRF or CDI_SLIDING_LRF
  sPHYSICS_VARIABLE resistive_torque;
  sPHYSICS_VARIABLE external_torque;
  sPHYSICS_VARIABLE is_velocity_via_torque;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "LRF_PARAMETERS",
                            BOOLEAN check_cdi_phys_type = TRUE) {

    if (check_cdi_phys_type
        && (CDI_PHYS_TYPE_LOCAL_REF_FRAME != ptd->cdi_physics_type)) {
      msg_internal_error("LGI and CDI file inconsistency: The LGI file expects a local reference frame definition"
                         " (CDI type %d), but the CDI file contains type %d.",
                         CDI_PHYS_TYPE_LOCAL_REF_FRAME, ptd->cdi_physics_type);
    }

    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_BEGIN_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_BEGIN_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_BEGIN_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_END_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_END_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CYL_END_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MRF_TYPE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESISTIVE_TORQUE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_EXTERNAL_TORQUE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VELOCITY_VIA_TORQUE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_INITIAL_ANG_VEL)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MOMENT_OF_INERTIA)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_START_TIME)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_END_TIME)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TIME_ACCURATE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MEAS_WINDOW_INDEX)) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }

} *LRF_PARAMETERS;


typedef struct sLRF_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {

public:
  
  __HOST__DEVICE__ LRF_PARAMETERS parameters()
    { return (LRF_PARAMETERS)_parameters; }

  VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }


  template<typename FLOAT_TYPE>
  __HOST__DEVICE__ VOID transform_point_to_containing(FLOAT_TYPE centroid[3], FLOAT_TYPE centroid_xformed[3])
  {
    FLOAT_TYPE r_local[3];   
    vsub(r_local,centroid,point);
    rotate_vector(r_local, centroid_xformed,local_to_containing_rotation_matrix);   
    vadd(centroid_xformed,centroid_xformed,point);                 
  }                                             

  VOID print()
  {
    printf("LRF index = %d, containing_index = %d\n", index, containing_lrf_index);
    printf("initial angle rotated = %f * PI\n", initial_angle_rotated / (4.0 * atan(1.0)));
    printf("angle rotated = %f * PI\n", angle_rotated / (4.0 * atan(1.0)));
    printf("angle rotated1 = %f * PI\n", angle_rotated1 / (4.0 * atan(1.0)));
    printf("omega = %E\n", omega);
    printf("omega1 = %E\n", omega1);
    printf("omega_total = %E %E %E\n", omega_total[0], omega_total[1], omega_total[2]);

    printf("\nlocal_to_containing_rotation_matrix = \n");
    ccDOTIMES(i, 3) {
      printf("%E %E %E\n", local_to_containing_rotation_matrix[i][0], local_to_containing_rotation_matrix[i][1],
          local_to_containing_rotation_matrix[i][2]);
    }
    printf("containing_to_local_rotation_matrix = \n");
    ccDOTIMES(i, 3) {
      printf("%E %E %E\n", containing_to_local_rotation_matrix[i][0], containing_to_local_rotation_matrix[i][1],
          containing_to_local_rotation_matrix[i][2]);
    }
    printf("local_to_containing_rotation_matrix1 = \n");
    ccDOTIMES(i, 3) {
      printf("%E %E %E\n", local_to_containing_rotation_matrix1[i][0], local_to_containing_rotation_matrix1[i][1],
          local_to_containing_rotation_matrix1[i][2]);
    }
    printf("containing_to_local_rotation_matrix1 = \n");
    ccDOTIMES(i, 3) {
      printf("%E %E %E\n", containing_to_local_rotation_matrix1[i][0], containing_to_local_rotation_matrix1[i][1],
          containing_to_local_rotation_matrix1[i][2]);
    }
    printf("local_to_global_rotation_matrix = \n");
    ccDOTIMES(i, 3) {
      printf("%E %E %E\n", local_to_global_rotation_matrix[i][0], local_to_global_rotation_matrix[i][1],
          local_to_global_rotation_matrix[i][2]);
    }


    if (is_angular_vel_time_varying) {
      printf("\n domega = ");
      if (domega != NULL && n_scales > 0) {
        ccDOTIMES(i, n_scales) {
          printf("%E ", domega[i]);
        }
      }

      if (domega_total && n_scales > 0) {
        printf("\n domega_total = ");
        ccDOTIMES(i, n_scales) {
          printf("%E %E %E\n", domega_total[3 * i], domega_total[3 * i + 1], domega_total[3 * i + 2]);
        }
      }

      if (domega_total && n_scales > 0) {
        printf("\n domega_linear_accel = ");
        ccDOTIMES(i, n_scales) {
          printf("%E %E %E\n", domega_linear_accel[3 * i], domega_linear_accel[3 * i + 1], domega_linear_accel[3 * i + 2]);
        }
      }
    }
    printf("linear_vel = %E %E %E\n", linear_vel[0], linear_vel[1], linear_vel[2]);
    printf("linear_accel = %E %E %E\n", linear_accel[0], linear_accel[1], linear_accel[2]);
  }

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const sLRF_PHYSICS_DESCRIPTOR* h_ptr,
				      size_t n_physics_desc);
public:
  
  cBOOLEAN is_angular_vel_non_zero;
  cBOOLEAN is_angular_vel_time_varying;
  cBOOLEAN is_active;           // is this LRF used by any local voxels or surfels
                                // OR does this SP have to report the state of this
                                //    ref frame for some meas window
  cBOOLEAN is_mlrf_on;
  cBOOLEAN is_rotational_dynamics_on; // True if LRF is rotated via measured torque
  sINT32 containing_lrf_index;  // parent lrf if nested; -1 if not nested
  sINT32 lrf_index;             // index of this lrf

  dFLOAT axis[3];
  dFLOAT point[3];              // axis runs through this point. Defined relative to global coord sys
  dFLOAT angular_vel[3];        // angular vel (as a vector) at time t
  dFLOAT *domega;               // time derivitive of angular_vel magnitude (per scale)
  dFLOAT omega;                 // angular vel magnitude at time t
  dFLOAT omega1;                // angular vel magnitude at time t+1
  dFLOAT angular_acceleration;  // angular acceleration at time t
  dFLOAT initial_angle_rotated; // initial rotation angle (necessary after ckpt restore)
  dFLOAT angle_rotated;         // angle rotation history
  dFLOAT angle_rotated1;        // angle rotation history at next timestep
  sINT32 initial_n_revolutions;	// initial angle revolutions (necessary after ckpt restore)
  sINT32 n_revolutions;	        // angle revolution history

  dFLOAT local_to_global_rotation_matrix[3][3];           // transform from local coord sys to global coord sys
  dFLOAT local_to_containing_rotation_matrix[3][3];       // wrt current timestep (angle_rotated)
  dFLOAT containing_to_local_rotation_matrix[3][3];       // wrt current timestep (angle_rotated)
  dFLOAT local_to_containing_rotation_matrix1[3][3];      // wrt next timestep (angle_rotated1)
  dFLOAT containing_to_local_rotation_matrix1[3][3];      // wrt next timestep (angle_rotated1)

  uINT32 n_scales;                          // Number of VR scales. Also the dimension of domega. Not used if
                                            // angular vel is constant in time

  dFLOAT omega_total[3];                    // Accumulated omega in LRF coordinate frame
  dFLOAT omega_total1[3];                   // Ditto but for next time step
  dFLOAT *domega_total;                     // Vector of accumulated time derivatives of omega
  dFLOAT *domega_linear_accel;              // Vector of acceleration terms due to time-dependent omega
                                            //   j-th element is SUM_i  domega_i x (c_LRF - c_i) for j-th VR scale
  dFLOAT linear_vel[3];                     // Accumulated LRF velocity due to non co-located axes in
                                            //   LRF coordinate frame
  dFLOAT linear_accel[3];                   // Accumulated LRF acceleration due to non co-located axes in

#if BUILD_D39_LATTICE
  cBOOLEAN has_transonic_flow;              //new, useful when sim.use_hybrid_ts_hs_solver is TRUE;
#endif
                                            //   LRF coordinate frame
} *LRF_PHYSICS_DESCRIPTOR;

typedef struct sROTATING_TIRE_PARAMETERS {
  sPHYSICS_VARIABLE ref_frame;
  sPHYSICS_VARIABLE angular_vel;
  sPHYSICS_VARIABLE axis_unit_vec[3];
  sPHYSICS_VARIABLE axis_point[3];
  sPHYSICS_VARIABLE deforming_tire_index;
  sPHYSICS_VARIABLE average_across_deforming_facets;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ROTATING_TIRE_PARAMETERS",
                            BOOLEAN check_cdi_phys_type = TRUE) {

    if (check_cdi_phys_type
        && (CDI_PHYS_TYPE_ROTATING_TIRE != ptd->cdi_physics_type)) {
      msg_internal_error("LGI and CDI file inconsistency: The LGI file expects a rotating tire definition"
                         " (CDI type %d), but the CDI file contains type %d.",
                         CDI_PHYS_TYPE_ROTATING_TIRE, ptd->cdi_physics_type);
    }

    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_FRAME)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_UNIT_VEC_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DEFORMING_TIRE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_AVERAGE_MEA_ACROSS)) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }

} *ROTATING_TIRE_PARAMETERS;

struct sSIM_TIRE;

typedef struct sROTATING_TIRE_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  ROTATING_TIRE_PARAMETERS parameters()
    { return ROTATING_TIRE_PARAMETERS(_parameters); }

  VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }

  dFLOAT initial_angle_rotated;
  dFLOAT angle_rotated;                 
  STP_PHYS_VARIABLE angular_vel_vec[3]; 
  sBG_TRANSFORM3d motion_xform;         
  sSIM_TIRE* tire;

  bool is_deforming() {
    return tire != nullptr;
  }

} *ROTATING_TIRE_PHYSICS_DESCRIPTOR;

typedef sROTATING_TIRE_PHYSICS_DESCRIPTOR sMOVB_PHYSICS_DESCRIPTOR, *MOVB_PHYSICS_DESCRIPTOR;

typedef struct sGRAVITY_BUOYANCY_PARAMETERS {
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE gravity[3];

  sPHYSICS_VARIABLE ref_temp;
  sPHYSICS_VARIABLE thermal_exp_coeff;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "GRAVITY_BUOYANCY_PARAMETERS",
                            BOOLEAN check_cdi_phys_type = TRUE) {

    if (check_cdi_phys_type
        && (CDI_PHYS_TYPE_GRAVITY_BUOYANCY != ptd->cdi_physics_type)) {
      msg_internal_error("LGI and CDI file inconsistency: The LGI file expects a non-inertial frame definition"
                         " (CDI type %d), but the CDI file contains type %d.",
                         CDI_PHYS_TYPE_GRAVITY_BUOYANCY, ptd->cdi_physics_type);
    }

    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_TEMP)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_THERMAL_EXP_COEFF)) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }

} *GRAVITY_BUOYANCY_PARAMETERS;


typedef struct sGRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  GRAVITY_BUOYANCY_PARAMETERS parameters()
    { return (GRAVITY_BUOYANCY_PARAMETERS)_parameters; }

  VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }

} *GRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR;

typedef struct sBODY_FORCE_PARAMETERS {
  sPHYSICS_VARIABLE coord_sys;
  sPHYSICS_VARIABLE body_force[3];

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "GRAVITY_BUOYANCY_PARAMETERS",
                            BOOLEAN check_cdi_phys_type = TRUE) {

    if (check_cdi_phys_type
        && (CDI_PHYS_TYPE_BODY_FORCE != ptd->cdi_physics_type)) {
      msg_internal_error("LGI and CDI file inconsistency: The LGI file expects a global body force definition"
                         " (CDI type %d), but the CDI file contains type %d.",
                         CDI_PHYS_TYPE_BODY_FORCE, ptd->cdi_physics_type);
    }

    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ACCEL_Z)
        || ptd->n_continuous_dp < i) {
      msg_internal_error("%s is not consistent with CDI physics type %d.",
                         name, ptd->cdi_physics_type);
    }
    return i;
  }

} *BODY_FORCE_PARAMETERS;

typedef struct sBODY_FORCE_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  __HOST__DEVICE__ BODY_FORCE_PARAMETERS parameters()
    { return (BODY_FORCE_PARAMETERS)_parameters; }

  VOID check_consistency()
    { parameters()->check_consistency(phys_type_desc); }

} *BODY_FORCE_PHYSICS_DESCRIPTOR;

class sMEAS_WINDOW;

#define ROTDYN_STR(x) #x
#define ROTDYN_PRINT_FLOAT(x) printf(ROTDYN_STR(x) " = %.10e\n", x);
#define ROTDYN_PRINT_INT(x) printf(ROTDYN_STR(x) " = %d\n", x);

const asINT32 NUM_TIME_VARYING_TORQUE_VALUES = 1000; // if changed, edit this in CP too

typedef struct sROTATIONAL_DYNAMICS_DESC {
  LRF_PHYSICS_DESCRIPTOR lrf;
  sMEAS_WINDOW           *window;
  cBOOLEAN               ready;
  TIMESTEP               start_time;
  TIMESTEP               end_time;
  TIMESTEP               eqn_time;
  TIMESTEP               delay;
  dFLOAT                 angular_acceleration_buffer;
  asINT32                count;
  dFLOAT                 *resistive_torque;
  dFLOAT                 *external_torque;
  cBOOLEAN               is_resistive_torque_time_varying;
  cBOOLEAN               is_external_torque_time_varying;
  BOOLEAN                resistive_torque_request;
  BOOLEAN                external_torque_request;
  MPI_Request            resistive_torque_mpi_request;
  MPI_Request            external_torque_mpi_request;

  void print()
  {
    ROTDYN_PRINT_INT(start_time)
    ROTDYN_PRINT_INT(end_time)
    ROTDYN_PRINT_INT(eqn_time)
    ROTDYN_PRINT_INT(delay)
    ROTDYN_PRINT_INT(count)
    ROTDYN_PRINT_FLOAT(lrf->omega)
    ROTDYN_PRINT_FLOAT(lrf->omega1)
    ROTDYN_PRINT_FLOAT(lrf->angular_acceleration)
    ROTDYN_PRINT_FLOAT(angular_acceleration_buffer)
    ROTDYN_PRINT_INT(is_resistive_torque_time_varying)
    ROTDYN_PRINT_INT(is_external_torque_time_varying)
  }
  
} *ROTATIONAL_DYNAMICS_DESC;

VOID global_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                              cSTRING cdi_desc_var_name, vFLOAT value,
                              vFLOAT aux_value,         // min or max
                              STP_GEOM_VARIABLE point[3]);         // unused

VOID read_coupling_models(LGI_STREAM stream);
VOID add_initial_couplings_to_event_queue();
asINT32 fill_coupling_model_surfel_ids(COUPLING_MODEL coupling_model);
VOID free_coupling_model_surfel_ids();
VOID update_all_time_varying_fluid_physics_parameters();
VOID update_all_time_varying_bsurfel_physics_parameters(BASETIME time);
VOID update_all_time_varying_surface_physics_parameters();
VOID update_all_time_varying_shell_physics_parameters();
VOID update_all_time_varying_body_force_parameters();
BOOLEAN update_time_and_space_varying_surfel_physics_parms(STP_GEOM_VARIABLE surfel_centroid[3],
                                                           STP_GEOM_VARIABLE surfel_normal[3],
                                                           PHYSICS_DESCRIPTOR pd);
VOID maybe_update_time_and_space_varying_shell_physics_parms(STP_GEOM_VARIABLE surfel_centroid[3],
                                                             STP_GEOM_VARIABLE surfel_normal[3],
                                                             SHELL_CONFIG_PHYSICS_DESCRIPTOR pd);
VOID read_rotational_dynamics_descs(LGI_STREAM stream);
VOID add_initial_rotational_dynamics_to_event_queue();

VOID find_units_conversion_coefficients(cSTRING from_unit_name, cSTRING to_unit_name, 
                                        dFLOAT *slope, dFLOAT *offset);

VOID correct_phys_desc_thermal_units();

VOID find_thermal_units_conversion_coefficients(cSTRING from_unit_name, cSTRING to_unit_name, 
                                                dFLOAT *slope, dFLOAT *offset);
VOID setup_ib_var_vector(sPHYSICS_VARIABLE *vars, sINT32 *eqn_len, cSTRING unit_class, LGI_STREAM lgi_stream);
void extract_time_varying_value(sPHYSICS_VARIABLE &var, BASETIME timestep, sFLOAT powertherm_time);

VOID calculate_shell_layer_face_areas();

dFLOAT eval_space_varying_only_parameter_program_for_uds(STP_GEOM_VARIABLE point[3],
                                                         STP_GEOM_VARIABLE normal[3],
                                                         EXPRLANG_PROGRAM initial_condition_program,
                                                         PHYSICS_VARIABLE  var);

dFLOAT eval_time_varying_only_parameter_program_for_uds(BASETIME timestep,
                                                        sFLOAT powertherm_time,
                                                        STP_GEOM_VARIABLE normal[3],
                                                        EXPRLANG_PROGRAM program,
                                                        PHYSICS_VARIABLE  var);

extern dFLOAT eval_space_and_time_varying_parameter_program_for_uds(STP_GEOM_VARIABLE point[3],
                                                                    STP_GEOM_VARIABLE normal[3],
                                                                    BASETIME timestep,
                                                                    sFLOAT powertherm_time,
                                                                    EXPRLANG_PROGRAM program,
                                                                    PHYSICS_VARIABLE  var);
#endif /* _SIMENG_EQNS_H */

