/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_SURFEL_H
#define _SIMENG_SURFEL_H

#include "common_sp.h"
#include "surface_shob.h"
#include "sim.h"
#include "lb_solver_data.h"
#include "turb_solver_data.h"
#include "t_solver_data.h"
#include "conduction_data.h"
#include "conduction_shell_edge.h"
#include "radiation_solver_data.h"
#include "mc_data_5g.h"
#include "mlrf_depots.h"
#include "dgf_reader_sp.h"
#include "scalar_data.h"
#include "sp_timers.h"
#include "neighbor_sp.h"
#include "particle_solver_data.h"
#include "ublk_surfel_offset_table.h"

// JEH Rename this to SURFEL_DATA_BLOCK_TYPE and perhaps SURFEL_LB_DATA_BLOCK_TYPE, etc.
// DO NOT CHANGE THE ORDER OF THESE
enum SURFEL_DATA_TYPE
{
  SURFEL_LB_DATA_TYPE,
  SURFEL_TURB_DATA_TYPE,
  SURFEL_OUTFLUX_DATA_TYPE,
  SURFEL_T_DATA_TYPE,
#if !BUILD_GPU
  SURFEL_CONDUCTION_DATA_TYPE,
  SURFEL_RADIATION_DATA_TYPE,
#endif
  SURFEL_UDS_DATA_TYPE,
  SURFEL_PF_DATA_TYPE,
  SURFEL_MC_DATA_TYPE,
  SURFEL_FROZEN_DATA_TYPE,
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  SURFEL_P_DATA_TYPE,
//#endif
  SURFEL_OUTFLUX_T_DATA_TYPE,
  SURFEL_LRF_DATA_TYPE,
  SURFEL_MLRF_DATA_TYPE,
  SURFEL_EVEN_ODD_DATA_TYPE,
  SURFEL_ISURFEL_DATA_TYPE,
#if !BUILD_GPU
  SURFEL_SHELL_CONDUCTION_DATA_TYPE,
  SURFEL_CONDUCTION_INTERFACE_FLUID_DATA_TYPE,
  SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE,
  //surfel_type() distinguishes between closed and open shells, so both share data type in offset table
  SURFEL_CONDUCTION_INTERFACE_SHELL_DATA_TYPE = SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE,
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE,
#endif
  SURFEL_S2S_ADVECT_DATA_TYPE,
  SURFEL_MIRROR_DATA_TYPE,
  SURFEL_V2S_LB_DATA_TYPE,
  SURFEL_V2S_TURB_DATA_TYPE,
  SURFEL_V2S_T_DATA_TYPE,
  SURFEL_DYN_DATA_TYPE,
  SURFEL_FRONT_DYN_DATA_TYPE = SURFEL_DYN_DATA_TYPE,
  SURFEL_OUTFLUX_MC_DATA_TYPE,
  SURFEL_OUTFLUX_UDS_DATA_TYPE,
  SURFEL_V2S_UDS_DATA_TYPE,
  SURFEL_V2S_PF_DATA_TYPE  
};

#define SURFEL_APPLY(method, args)      \
  this->lb_data()->method args;         \
  if (!this->is_conduction_surfel()) {  \
    if (sim.is_turb_model)              \
      this->turb_data()->method args;   \
    if (sim.is_heat_transfer)           \
      this->t_data()->method args;      \
    if (g_is_multi_component)           \
      this->mc_data()->method args; 		\
    if (sim.is_particle_model)          \
      this->p_data()->method args;      \
  }  

// This macro assumes that arg1 has data blocks with the same accessor methods as a surfel.
// Obviously this means that arg1 can be a surfel.
#define SURFEL_APPLY_1(method, arg1)                      \
  this->lb_data()->method (arg1->lb_data());              \
  if (!this->is_conduction_surfel()) {                    \
    if (sim.is_turb_model)                                \
      this->turb_data()->method (arg1->turb_data());      \
    if (sim.is_heat_transfer)                             \
      this->t_data()->method (arg1->t_data());            \
    if (sim.is_scalar_model) {                            \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data(); \
      SURFEL_UDS_DATA arg1_uds_data = arg1->uds_data();   \
      for (asINT32 nth_uds = 0;                           \
          nth_uds < sim.n_user_defined_scalars;           \
          nth_uds++,surfel_uds_data++,arg1_uds_data++)    \
        surfel_uds_data->method(arg1_uds_data);		        \
      if (sim.is_pf_model)                                \
        this->pf_data()->method (arg1->pf_data());        \
    }									                                    \
    if (g_is_multi_component)                             \
      this->mc_data()->method (arg1->mc_data());          \
  }

// This macro assumes that arg1 has data blocks with the same accessor methods as a surfel.
// Obviously this means that arg1 can be a surfel.
#define SURFEL_APPLY_4(method, arg1, arg2, arg3, arg4)                    \
  this->lb_data()->method (arg1->lb_data(), arg2, arg3, arg4);            \
  if (!this->is_conduction_surfel()) {                                    \
    if (sim.is_turb_model)                                                \
      this->turb_data()->method (arg1->turb_data(), arg2, arg3, arg4);    \
    if (sim.is_heat_transfer)                                             \
      this->t_data()->method (arg1->t_data(), arg2, arg3, arg4);          \
    if (sim.is_scalar_model)    {                                         \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                 \
      SURFEL_UDS_DATA arg1_uds_data = arg1->uds_data();                   \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;     \
          nth_uds++,surfel_uds_data++,arg1_uds_data++)                    \
        surfel_uds_data->method(arg1_uds_data,arg2, arg3, arg4 );         \
      if (sim.is_pf_model)                                                \
        this->pf_data()->method(arg1->pf_data(), arg2, arg3, arg4);       \
    }									                                                    \
    if (g_is_multi_component)                                             \
      this->mc_data()->method (arg1->mc_data(), arg2, arg3, arg4);        \
  }

#define SURFEL_APPLY_ACTIVE(active_solver_mask, method, args)                     \
  if (this->is_conduction_surfel()) {                                             \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask))                  \
      if (this->is_conduction_surface()) this->conduction_data()->method args;    \
  } else {                                                                        \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method args;                                               \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method args;                                             \
    if (is_solver_active(T_SOLVER, active_solver_mask))                           \
      this->t_data()->method args;                                                \
    if (is_solver_active(UDS_SOLVER, active_solver_mask))    {                    \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;             \
          nth_uds++,surfel_uds_data++)                                            \
        surfel_uds_data->method args ;                                            \
      if (sim.is_pf_model)                                                        \
        this->pf_data()->method args;                                             \
    }									                                                            \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
      this->mc_data()->method args;                                               \
  }

// This macro assumes that both the base data block and the v2s
// data block have implementations of the specified method. The same
// args are passed to all methods.
#define SURFEL_APPLY_ACTIVE_DUAL(active_solver_mask, method, args)                  \
  if (this->is_conduction_surfel()) {                                               \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask)) {                  \
      if (this->is_conduction_surface()) this->conduction_data()->method args;      \
      if (this->is_conduction_shell()) this->shell_conduction_data()->method args;  \
    }                                                                               \
  } else {                                                                          \
    if (is_solver_active(LB_SOLVER, active_solver_mask)) {                          \
      this->lb_data()->method args;                                                 \
      this->v2s_lb_data()->method args;                                             \
    }                                                                               \
    if (is_solver_active(TURB_SOLVER, active_solver_mask)) {                        \
      this->turb_data()->method args;                                               \
      this->v2s_turb_data()->method args;                                           \
      if (this->is_inlet_or_outlet()) this->dyn_turb_data()->method args;           \
    }                                                                               \
    if (is_solver_active(T_SOLVER, active_solver_mask)) {                           \
      this->t_data()->method args;                                                  \
      this->v2s_t_data()->method args;                                              \
    }                                                                               \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                         \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                           \
      SURFEL_V2S_UDS_DATA surfel_v2s_uds_data = this->v2s_uds_data();               \
      for (asINT32 nth_uds = 0;                                                     \
           nth_uds < sim.n_user_defined_scalars;                                    \
           nth_uds++,surfel_uds_data++,surfel_v2s_uds_data++) {                     \
        surfel_uds_data->method args ;                                              \
        surfel_v2s_uds_data->method args;                                           \
      }                                                                             \
      if (sim.is_pf_model)  {                                                       \
        this->pf_data()->method args;                                               \
        this->v2s_pf_data()->method args;                                           \
      }                                                                             \
    }									                                                              \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask)) {  \
      this->mc_data()->method args; 					                                      \
    }                                                                               \
    if (sim.is_particle_model) {                                                    \
      this->p_data()->method args;                                                  \
    }                                                                               \
  }

// This macro assumes that arg1 has data blocks with the same accessor methods as a surfel.
// Obviously this means that arg1 can be a surfel.
#define SURFEL_APPLY_ACTIVE_1(active_solver_mask, method, arg1)                   \
  if (!this->is_conduction_surfel()) {                                            \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method (arg1->lb_data());                                  \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method (arg1->turb_data());                              \
    if (is_solver_active(T_SOLVER, active_solver_mask))                           \
      this->t_data()->method (arg1->t_data());                                    \
    if (sim.is_particle_model)                                                    \
      this->p_data()->method (arg1->p_data());                                    \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                       \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                         \
      SURFEL_UDS_DATA arg1_uds_data = arg1->uds_data();                           \
      for (asINT32 nth_uds = 0;                                                   \
           nth_uds < sim.n_user_defined_scalars;                                  \
           nth_uds++,surfel_uds_data++,arg1_uds_data++)                           \
        surfel_uds_data->method(arg1_uds_data);                                   \
      if (sim.is_pf_model)                                                        \
        this->pf_data()->method (arg1->pf_data());                                \
    }									                                                            \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
      this->mc_data()->method (arg1->mc_data());                                  \
  }

#define SURFEL_APPLY_ACTIVE_2(active_solver_mask, method, arg1, arg2)             \
  if (!this->is_conduction_surfel()) {                                            \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method (arg1->lb_data(), arg2);                            \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method (arg1->turb_data(), arg2);                        \
    if (is_solver_active(T_SOLVER, active_solver_mask))                           \
      this->t_data()->method (arg1->t_data(), arg2);                              \
    if (sim.is_particle_model)                                                    \
      this->p_data()->method (arg1->p_data(), arg2);                              \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                       \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;             \
          nth_uds++,surfel_uds_data++)                                            \
        surfel_uds_data->method( arg1->uds_data(), arg2);                         \
      if (sim.is_pf_model)                                                        \
        this->pf_data()->method (arg1->pf_data(), arg2);                          \
    }									                                                            \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
      this->mc_data()->method (arg1->mc_data(), arg2);                            \
  }

// This macro assumes that arg1 has data blocks with the same accessor methods as a surfel.
// Obviously this means that arg1 can be a surfel.
#define SURFEL_APPLY_ACTIVE_4(active_solver_mask, method, arg1, arg2, arg3, arg4) \
  if (!this->is_conduction_surfel()) {                                            \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method (arg1->lb_data(), arg2, arg3, arg4);                \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method (arg1->turb_data(), arg2, arg3, arg4);            \
    if (is_solver_active(T_SOLVER, active_solver_mask))                           \
      this->t_data()->method (arg1->t_data(), arg2, arg3, arg4);                  \
    if (is_solver_active(UDS_SOLVER, active_solver_mask))    {                    \
      SURFEL_UDS_DATA surfel_uds_data = this->uds_data();                         \
      SURFEL_UDS_DATA arg1_uds_data = arg1->uds_data();                           \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;             \
          nth_uds++,surfel_uds_data++,arg1_uds_data++)                            \
        surfel_uds_data->method(arg1_uds_data, arg2, arg3, arg4);                 \
      if (sim.is_pf_model)                                                        \
        this->pf_data()->method (arg1->pf_data(), arg2, arg3, arg4);              \
    }									                                                            \
  if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->mc_data()->method (arg1->mc_data(), arg2, arg3, arg4);                \
  }

// sSURFEL_SEND_ELEMENT must be larger in size than
// sSURFEL_INIT_INFO_SEND_ELEMENT since the send buffer allocated is reused 
typedef struct sSURFEL_INIT_INFO_SEND_ELEMENT 
{
  uINT32            m_surfel_type;
  asINT32            m_surfel_material_id; 
  STP_GEOM_VARIABLE m_normal[N_AXES];
  STP_PHYS_VARIABLE m_u[2];
  STP_SURFEL_WEIGHT m_in_states_voxel_weight[N_SURFEL_PGRAM_VOLUMES];
  STP_SURFEL_WEIGHT m_in_states_voxel_weight2[N_SURFEL_PGRAM_VOLUMES];
  STP_LATVEC_MASK   m_incoming_latvec_mask;
  STP_GEOM_VARIABLE m_lrf_v2s_scale_diff;
#if BUILD_D39_LATTICE
  STP_SURFEL_WEIGHT m_mme_weight;
  STP_SURFEL_WEIGHT m_s2s_sampling_weight;
#elif BUILD_5G_LATTICE
  STP_PHYS_VARIABLE m_potential;
  STP_PHYS_VARIABLE m_porosity;
#endif
  sdFLOAT           m_sampling_weight_with_interface_s2s_inv;
  sdFLOAT           m_sampling_weight_without_interface_s2s_inv;
  sINT32 m_surface_material_id;
  asINT32 m_dynamics_type;
  sINT32 m_implicit_shell_state_index;
  //sSURFEL_SHELL_CONDUCTION_DATA m_surfel_shell_conduction_data;
} *SURFEL_INIT_INFO_SEND_ELEMENT;

// TODO: Should change the struct below to use offsets which are dynamically computed
// based on active solvers. This will then allow us to allocate a send buffer
// which is sized based on the active solvers
template <typename sSURFEL_T_SEND_FIELD_TYPE>
struct sSURFEL_SEND_ELEMENT_BASE 
{
  STP_GEOM_VARIABLE       m_lrf_s2s_factor_pair;
  STP_GEOM_VARIABLE       m_lrf_v2s_dist_pair;
  STP_GEOM_VARIABLE       m_lrf_v2s_scale_diff_pair;

  sSURFEL_LB_SEND_FIELD        m_lb_data;
  sSURFEL_TURB_SEND_FIELD      m_turb_data;
  sSURFEL_T_SEND_FIELD_TYPE    m_t_data;
  sSURFEL_CONDUCTION_SEND_FIELD    m_conduction_data;
  sSURFEL_UDS_PDE_SEND_FIELD    m_uds_data; 
  sSURFEL_MC_SEND_FIELD        m_mc_data;
  sSURFEL_PF_SEND_FIELD        m_pf_data;
#if BUILD_D19_LATTICE
  sSURFEL_FROZEN_SEND_FIELD    m_frozen_data;
#endif 

  sSURFEL_LB_SEND_FIELD        *lb_data()   { return &m_lb_data; }
  sSURFEL_TURB_SEND_FIELD      *turb_data() { return &m_turb_data; }
  sSURFEL_T_SEND_FIELD_TYPE    *t_data()    { return &m_t_data; }
  sSURFEL_CONDUCTION_SEND_FIELD *conduction_data() { return &m_conduction_data; }
  sSURFEL_UDS_PDE_SEND_FIELD   *uds_data() { return &m_uds_data; }
  sSURFEL_MC_SEND_FIELD        *mc_data()   { return &m_mc_data; }
  sSURFEL_PF_SEND_FIELD        *pf_data()   { return &m_pf_data; }
#if BUILD_D19_LATTICE
  sSURFEL_FROZEN_SEND_FIELD    *frozen_data()   { return &m_frozen_data; }
#endif

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  sSURFEL_P_DATA_SEND_FIELD     m_p_data;
  sSURFEL_P_DATA_SEND_FIELD     *p_data()    { return (&m_p_data); }
//#endif
};

// typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_T_UDS_SEND_FIELD> sSURFEL_SEND_ELEMENT, *SURFEL_SEND_ELEMENT;              //sSURFEL_SEND_ELEMENT adopts sSURFEL_T_UDS_SEND_FIELD as default in thermal case
typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_T_PDE_SEND_FIELD>    sSURFEL_T_PDE_SEND_ELEMENT, *SURFEL_T_PDE_SEND_ELEMENT;
typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_ENTROPY_SEND_FIELD>  sSURFEL_ENTROPY_SEND_ELEMENT, *SURFEL_ENTROPY_SEND_ELEMENT;
#if BUILD_6X_SOLVER
typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_LB_ENTROPY_SEND_FIELD>  sSURFEL_LB_ENTROPY_SEND_ELEMENT, *SURFEL_LB_ENTROPY_SEND_ELEMENT;
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_LB_ENERGY_SEND_FIELD>  sSURFEL_LB_ENERGY_SEND_ELEMENT, *SURFEL_LB_ENERGY_SEND_ELEMENT;
#endif
typedef sSURFEL_SEND_ELEMENT_BASE<sSURFEL_UDS_PDE_SEND_FIELD>    sSURFEL_UDS_PDE_SEND_ELEMENT, *SURFEL_UDS_PDE_SEND_ELEMENT;

typedef struct sSURFEL_BCTYPE_SEND_ELEMENT 
{
  sINT8             m_boundary_condition_type;

} *SURFEL_BCTYPE_SEND_ELEMENT;

#if BUILD_5G_LATTICE
typedef struct sSURFEL_POTENTIAL_SEND_ELEMENT 
{
  STP_PHYS_VARIABLE            m_lb_potential;
  STP_PHYS_VARIABLE            m_mc_potential;
} *SURFEL_POTENTIAL_SEND_ELEMENT;
#endif
typedef struct sSURFEL_LRF_SEND_FIELD
{
  STP_GEOM_VARIABLE lrf_s2s_factor_pair;
  STP_GEOM_VARIABLE lrf_v2s_dist_pair;
  STP_GEOM_VARIABLE lrf_v2s_scale_diff;
  STP_GEOM_VARIABLE lrf_v2s_scale_diff_pair;
  STP_PHYS_VARIABLE density_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE momentum_pair[N_TIME_INDICES][N_AXES];
  STP_PHYS_VARIABLE K_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE E_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE temp_sample_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE entropy_sample_pair[N_TIME_INDICES];  
#if BUILD_D39_LATTICE
  STP_PHYS_VARIABLE hyb_force_pair[N_TIME_INDICES][N_AXES];
#endif
  STP_PHYS_VARIABLE uds_value_pair[MAX_N_USER_DEFINED_SCALARS][N_TIME_INDICES];
#if BUILD_D19_LATTICE
  STP_PHYS_VARIABLE pf_pressure_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE pf_vel_pair[N_TIME_INDICES][3];
  STP_PHYS_VARIABLE pf_grad_chem_pair[N_TIME_INDICES][3];
  STP_PHYS_VARIABLE pf_grad_order_pair[N_TIME_INDICES][4];
#endif
} *SURFEL_LRF_SEND_FIELD;


template<typename SFL_TYPE_TAG>
struct tSURFEL_LRF_DATA {
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_s2s_factor;
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_v2s_dist;
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_s2s_factor_pair;
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_v2s_dist_pair;
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_v2s_scale_diff;
  tSFL_VAR<STP_GEOM_VARIABLE, N> lrf_v2s_scale_diff_pair;
  tSFL_VAR<STP_PHYS_VARIABLE, N> density_pair[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> momentum_pair[N_TIME_INDICES][N_AXES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> K_pair[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> E_pair[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample_pair[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> entropy_sample_pair[N_TIME_INDICES];
#if BUILD_D39_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N> hyb_force_pair[N_TIME_INDICES][N_AXES];
#endif
  tSFL_VAR<STP_PHYS_VARIABLE, N> uds_value_pair[MAX_N_USER_DEFINED_SCALARS][N_TIME_INDICES];
#if BUILD_D19_LATTICE
  tSFL_VAR<STP_PHYS_VARIABLE, N> pf_pressure_pair[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> pf_vel_pair[N_TIME_INDICES][3];
  tSFL_VAR<STP_PHYS_VARIABLE, N> pf_grad_chem_pair[N_TIME_INDICES][3];
  tSFL_VAR<STP_PHYS_VARIABLE, N> pf_grad_order_pair[N_TIME_INDICES][4]; 
#endif
  
  VOID print_surfel_data(std::ostream& os);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_LRF_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_LRF_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    SURFEL_LRF_SEND_FIELD field = reinterpret_cast<SURFEL_LRF_SEND_FIELD>(send_data_info.send_buffer);
    field->lrf_s2s_factor_pair = lrf_s2s_factor_pair;
    field->lrf_v2s_dist_pair = lrf_v2s_dist_pair;
    field->lrf_v2s_scale_diff_pair = lrf_v2s_scale_diff_pair;
    memcpy(field->density_pair, density_pair, sizeof(field->density_pair));
    memcpy(field->momentum_pair, momentum_pair, sizeof(field->momentum_pair));
    memcpy(field->K_pair, K_pair, sizeof(field->K_pair));
    memcpy(field->E_pair, E_pair, sizeof(field->E_pair));
    memcpy(field->temp_sample_pair, temp_sample_pair, sizeof(field->temp_sample_pair));
    memcpy(field->entropy_sample_pair, entropy_sample_pair, sizeof(field->entropy_sample_pair));
#if BUILD_D39_LATTICE
    memcpy(field->hyb_force_pair, hyb_force_pair , sizeof(field->hyb_force_pair));
#endif
    memcpy(field->uds_value_pair, uds_value_pair, sizeof(field->uds_value_pair));
#if BUILD_D19_LATTICE
    memcpy(field->pf_pressure_pair, pf_pressure_pair, sizeof(field->pf_pressure_pair));
    memcpy(field->pf_vel_pair, pf_vel_pair, sizeof(field->pf_vel_pair));
    memcpy(field->pf_grad_chem_pair, pf_grad_chem_pair, sizeof(field->pf_grad_chem_pair));
    memcpy(field->pf_grad_order_pair, pf_grad_order_pair, sizeof(field->pf_grad_order_pair));
#endif
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    SURFEL_LRF_SEND_FIELD field = reinterpret_cast<SURFEL_LRF_SEND_FIELD>(recv_data_info.recv_buffer);

    lrf_s2s_factor_pair = field->lrf_s2s_factor_pair;
    lrf_v2s_dist_pair = field->lrf_v2s_dist_pair;
    lrf_v2s_scale_diff_pair = field->lrf_v2s_scale_diff_pair;
    memcpy( density_pair, field->density_pair , sizeof(field->density_pair));
    memcpy( momentum_pair, field->momentum_pair , sizeof(field->momentum_pair));
    memcpy( K_pair, field->K_pair , sizeof(field->K_pair));
    memcpy( E_pair, field->E_pair , sizeof(field->E_pair));
    memcpy( temp_sample_pair, field->temp_sample_pair , sizeof(field->temp_sample_pair));
    memcpy( entropy_sample_pair, field->entropy_sample_pair , sizeof(field->entropy_sample_pair));
#if BUILD_D39_LATTICE
    memcpy( hyb_force_pair, field->hyb_force_pair , sizeof(field->hyb_force_pair));
#endif
    memcpy( uds_value_pair, field->uds_value_pair , sizeof(field->uds_value_pair));
#if BUILD_D19_LATTICE
    memcpy( pf_pressure_pair, field->pf_pressure_pair , sizeof(field->pf_pressure_pair));
    memcpy( pf_vel_pair, field->pf_vel_pair , sizeof(field->pf_vel_pair));
    memcpy( pf_grad_chem_pair, field->pf_grad_chem_pair , sizeof(field->pf_grad_chem_pair));
    memcpy( pf_grad_order_pair, field->pf_grad_order_pair , sizeof(field->pf_grad_order_pair));
#endif
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_LRF_DATA *source_lrf_data,
      STP_LATVEC_MASK latvec_state_mask,
      STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
      STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
  {
    ccDOTIMES(n, N_TIME_INDICES) {
      density_pair[n] = source_lrf_data->density_pair[n];
    }
    ccDOTIMES(axis, N_AXES) {
      ccDOTIMES(n, N_TIME_INDICES) {
        momentum_pair[n][axis] = source_lrf_data->momentum_pair[axis][n] * velocity_mirror_sign_factor[axis];
#if BUILD_D19_LATTICE
	pf_vel_pair[n][axis] = source_lrf_data->pf_vel_pair[n][axis] * velocity_mirror_sign_factor[axis];
        pf_grad_chem_pair[n][axis] = source_lrf_data->pf_grad_chem_pair[n][axis] * velocity_mirror_sign_factor[axis];
        pf_grad_order_pair[n][axis] = source_lrf_data->pf_grad_order_pair[n][axis] * velocity_mirror_sign_factor[axis];
#endif
      }
    }
  }

  VOID init(DGF_SURFEL_DESC surfel_desc);
  VOID write_ckpt();
  VOID read_ckpt();
  uINT64 ckpt_len(bool verbose=false);
};

#if BUILD_GPU
INIT_MSFL(tSURFEL_LRF_DATA);
#endif

template<>
INLINE VOID tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>::init(DGF_SURFEL_DESC surfel_desc) {
  memset(this, 0, sizeof(*this));
}

template<>
INLINE VOID tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>::write_ckpt() {
  write_ckpt_lgi(*this);
}

template<>
INLINE VOID tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>::read_ckpt() {
  read_lgi(*this);
}

template<>
INLINE uINT64 tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>::ckpt_len(bool verbose) {
  return sizeof(*this);
}

using sSURFEL_LRF_DATA = tSURFEL_LRF_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_LRF_DATA = sSURFEL_LRF_DATA*;

extern sINT32 surfel_base_size;
extern sINT32 surfel_base_ckpt_size;

//const asINT32 N_VOXEL_RESERVE_BITS = 4;

struct sFLUID_PHYSICS_DESCRIPTOR;

inline namespace
SIMULATOR_NAMESPACE {
 template<typename SFL_TYPE_TAG> struct tS2S_ADVECT_DATA;
 using sS2S_ADVECT_DATA = tS2S_ADVECT_DATA<SFL_SDFLOAT_TYPE_TAG>;
 using sMSFL_S2S_ADVECT_DATA = tS2S_ADVECT_DATA<MSFL_SDFLOAT_TYPE_TAG>;
  
 struct sSURFEL_MIRROR_DATA;

 template<typename SFL_TYPE_TAG> struct tSURFEL_S2S_LB_DATA;
 using sSURFEL_S2S_LB_DATA = tSURFEL_S2S_LB_DATA<SFL_SDFLOAT_TYPE_TAG>;

 template<typename SFL_TYPE_TAG> struct tSURFEL_EVEN_ODD_DATA;
 using sSURFEL_EVEN_ODD_DATA = tSURFEL_EVEN_ODD_DATA<SFL_SDFLOAT_TYPE_TAG>;

 template<typename SFL_TYPE_TAG> struct tSURFEL_MLRF_DATA;  
 using sSURFEL_MLRF_DATA = tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>;
 template <typename sPHYSICS_DESCRIPTOR_TYPE> class tSURFEL_DYNAMICS_DATA;

 typedef tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> sSURFEL_DYNAMICS_DATA, *SURFEL_DYNAMICS_DATA;  
}

struct sFLUID_PHYSICS_DESCRIPTOR;

template<typename SFL_TYPE_TAG>
class tSURFEL_V2S_DATA_MEM_POOL;

template<typename SFL_TYPE_TAG>
class tLOCAL_SFL_V2S_DATA;

enum class V2S_DATA_TYPES {
  LB,
  TURB,
  TEMP,
  UDS,
  PF,
  N_TYPES
};

__HOST__DEVICE__ constexpr inline
asINT32 as_int(V2S_DATA_TYPES type) {
  return static_cast<int>(type);
}

/** @brief SURFEL_V2S_DATA is now dynamically sized via an offset table - see PR 52577*/
extern asINT32 g_v2s_data_offset_table[as_int(V2S_DATA_TYPES::N_TYPES) + 1]; //+1 to hold size of V2S DATA
extern asINT32 g_v2s_data_offset_table_64[as_int(V2S_DATA_TYPES::N_TYPES) + 1]; //+1 to hold size of V2S DATA

namespace GPU {
extern __CONSTANT__ __DEVICE__ asINT32 g_v2s_data_offset_table_64[as_int(V2S_DATA_TYPES::N_TYPES) + 1];
}

template<typename SFL_TYPE_TAG>
INLINE __HOST__DEVICE__ asINT32 get_v2s_data_offset(asINT32 component_index);

template<>
INLINE __HOST__DEVICE__ asINT32 get_v2s_data_offset<SFL_SDFLOAT_TYPE_TAG>(asINT32 component_index) {  
#if DEVICE_COMPILATION_MODE  
  assert(false); return -1;
#else
  cassert(::g_v2s_data_offset_table[component_index] >= 0);
  return ::g_v2s_data_offset_table[component_index];
#endif
}

template<>
INLINE __HOST__DEVICE__ asINT32 get_v2s_data_offset<MSFL_SDFLOAT_TYPE_TAG>(asINT32 component_index) {
#if DEVICE_COMPILATION_MODE
  cassert(GPU::g_v2s_data_offset_table_64[component_index] >= 0);
  return GPU::g_v2s_data_offset_table_64[component_index];
#else
  return ::g_v2s_data_offset_table_64[component_index];
#endif
}

/** @brief A light weight class that points to surfel V2S data that is managed by a memory pool*/
template<typename SFL_TYPE_TAG>
class tSURFEL_V2S_DATA {

private:
  friend class tSURFEL_V2S_DATA_MEM_POOL<SFL_TYPE_TAG>;
  static void build_offset_table();

public:

  //All constructor and assignment member funcitons are deleted because we want
  //tSURFEL_V2S_DATA to always be a reference or pointer to data allocated by
  //the memory pool, or stack data that is an instance of tLOCAL_SFL_V2S_DATA.
  //Constructing tSURFEL_V2S_DATA standalone could lead to invalid memory reads and writes
  //from functions such as clear and v2s_*_data methods
  tSURFEL_V2S_DATA() = delete;
  tSURFEL_V2S_DATA(const tSURFEL_V2S_DATA&) = delete;
  tSURFEL_V2S_DATA& operator=(const tSURFEL_V2S_DATA&) = delete;
  tSURFEL_V2S_DATA(tSURFEL_V2S_DATA&&) = delete;
  tSURFEL_V2S_DATA& operator=(tSURFEL_V2S_DATA&&) = delete;
  
  __HOST__DEVICE__ static size_t size() {
    return get_v2s_data_offset<SFL_TYPE_TAG>(as_int(V2S_DATA_TYPES::N_TYPES));
  }

  __HOST__DEVICE__ auto v2s_lb_data() {
    constexpr auto lb_index = as_int(V2S_DATA_TYPES::LB);
    return reinterpret_cast<tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>*>(reinterpret_cast<char*>(this) +
                                                                get_v2s_data_offset<SFL_TYPE_TAG>(lb_index));
  }
  __HOST__DEVICE__ auto v2s_turb_data() {
    constexpr auto turb_index = as_int(V2S_DATA_TYPES::TURB);
    return reinterpret_cast<tSURFEL_V2S_TURB_DATA<SFL_TYPE_TAG>*>(reinterpret_cast<char*>(this) +
                                                                  get_v2s_data_offset<SFL_TYPE_TAG>(turb_index));
  }
  __HOST__DEVICE__ auto v2s_t_data() {
    constexpr auto temp_index = as_int(V2S_DATA_TYPES::TEMP);
    return reinterpret_cast<tSURFEL_V2S_T_DATA<SFL_TYPE_TAG>*>(reinterpret_cast<char*>(this) +
                                                               get_v2s_data_offset<SFL_TYPE_TAG>(temp_index));
  }
  __HOST__DEVICE__ tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG> *v2s_uds_data() {
    return this->v2s_uds_data(0);
  }
  __HOST__DEVICE__ auto v2s_uds_data(asINT32 nth_uds) {
    constexpr auto uds_index = as_int(V2S_DATA_TYPES::UDS);
    return reinterpret_cast<tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>*>(reinterpret_cast<char*>(this) +
                                                                 get_v2s_data_offset<SFL_TYPE_TAG>(uds_index) +
                                                                 nth_uds * sizeof(tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>));
  }
  __HOST__DEVICE__ auto v2s_pf_data() {
    constexpr auto pf_index = as_int(V2S_DATA_TYPES::PF);
    return reinterpret_cast<tSURFEL_V2S_PF_DATA<SFL_TYPE_TAG>*>(reinterpret_cast<char*>(this) +
                                                               get_v2s_data_offset<SFL_TYPE_TAG>(pf_index));
  }
  __HOST__ VOID clear();
};

using sSURFEL_V2S_DATA = tSURFEL_V2S_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_DATA = sSURFEL_V2S_DATA*;

/** @brief Instance of surfel V2S data on the stack*/
template<typename SFL_TYPE_TAG>
struct tLOCAL_SFL_V2S_DATA {

  __HOST__DEVICE__ auto& v2s_data() {
    cassert(buff_size() >= tSURFEL_V2S_DATA<SFL_TYPE_TAG>::size());
    return *reinterpret_cast<tSURFEL_V2S_DATA<SFL_TYPE_TAG>*>(this);
  }
  
  __HOST__DEVICE__ static constexpr size_t buff_size() {
    return \
      sizeof(tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>) +
      sizeof(tSURFEL_V2S_TURB_DATA<SFL_TYPE_TAG>) +
      sizeof(tSURFEL_V2S_T_DATA<SFL_TYPE_TAG>) +
      sizeof(tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>) * MAX_N_USER_DEFINED_SCALARS +
      sizeof(tSURFEL_V2S_PF_DATA<SFL_TYPE_TAG>);
  }

private:  
  std::byte m_buff[buff_size()];  
};

using LOCAL_SFL_V2S_DATA = tLOCAL_SFL_V2S_DATA<SFL_SDFLOAT_TYPE_TAG>;

/** @brief Abstraction to get the correct SURFEL_V2S_DATA* for a surfel pair

Performing regular pointer arithematic or element access  (++, --, []) on SURFEL_V2S_DATA* is no longer valid
since V2S_DATA is now dynamically sized via a global offset table. Therefore use this abstraction to correctly
retreive the correct neighboring SURFEL_V2S_DATA*, for a surfel pair.
*/
template<typename SFL_TYPE_TAG>
struct tSFL_V2S_DATA_PAIR_PTR {
  
  __HOST__DEVICE__ explicit tSFL_V2S_DATA_PAIR_PTR(tSURFEL_V2S_DATA<SFL_TYPE_TAG>* v2s_data):
    m_v2s_data(v2s_data) {
  }

  //No need __HOST__DEVICE__ attribute for defaults
  tSFL_V2S_DATA_PAIR_PTR(const tSFL_V2S_DATA_PAIR_PTR& other) = default;
  tSFL_V2S_DATA_PAIR_PTR& operator=(const tSFL_V2S_DATA_PAIR_PTR& other)=default;
  tSFL_V2S_DATA_PAIR_PTR(tSFL_V2S_DATA_PAIR_PTR&& other) = default;
  tSFL_V2S_DATA_PAIR_PTR& operator=(tSFL_V2S_DATA_PAIR_PTR&& other)=default;

  __HOST__DEVICE__ auto& operator[](int index) {
    cassert(index >= 0 && index < 2);
    return *((tSURFEL_V2S_DATA<SFL_TYPE_TAG>*) ((char*) m_v2s_data + index * tSURFEL_V2S_DATA<SFL_TYPE_TAG>::size()));
  }

  __HOST__DEVICE__ const auto& operator[](int index) const {
    return const_cast<tSFL_V2S_DATA_PAIR_PTR*>(this)->operator[](index);
  }
  
private:
  tSURFEL_V2S_DATA<SFL_TYPE_TAG>* m_v2s_data;
};

using SFL_V2S_DATA_PAIR_PTR = tSFL_V2S_DATA_PAIR_PTR<SFL_SDFLOAT_TYPE_TAG>;

/** @brief declares an instance of surfel V2S data pair on the stack*/
template<typename SFL_TYPE_TAG>
struct tLOCAL_SFL_V2S_DATA_PAIR {
  __HOST__DEVICE__ auto v2s_data_pair() {
    return tSFL_V2S_DATA_PAIR_PTR<SFL_TYPE_TAG>((tSURFEL_V2S_DATA<SFL_TYPE_TAG>*) this);
  }
private:
  std::byte m_buff[2 * tLOCAL_SFL_V2S_DATA<SFL_TYPE_TAG>::buff_size()];
};

using LOCAL_SFL_V2S_DATA_PAIR = tLOCAL_SFL_V2S_DATA_PAIR<SFL_SDFLOAT_TYPE_TAG>;


/** @brief A singleton class that manages a pre-allocated chunk of memory for surfel operations

The chunk of memory managed by this class resides either on the heap (CPU) or global memory (GPU). 
The pool is only used by surfels that do not have V2S data allocated as part of their overall allocation,
i.e, only surfels that have their 'has_v2s_data' attribute bit set to false.

@warning the 'allocate_pool' method should be called once, at the start of simulation

*/
template<typename SFL_TYPE_TAG>
class tSURFEL_V2S_DATA_MEM_POOL {

public:
  
  constexpr static size_t N_SURFELS_PER_PASS = 128;
  constexpr static size_t N_SURFEL_PAIRS_PER_PASS = 64;
  constexpr static size_t N_DATASETS = MAX(N_SURFELS_PER_PASS, (N_SURFEL_PAIRS_PER_PASS * 2));

  static __HOST__ auto& get_instance() {
    static tSURFEL_V2S_DATA_MEM_POOL instance;
    return instance;
  }

  static __HOST__ size_t size_of_allocated_elems(size_t n_elems);
  
  __HOST__ void allocate_pool(size_t n_elems);
   
  __HOST__ void clear(size_t n_elems = N_SURFELS_PER_PASS);

  __HOST__DEVICE__ auto v2s_data(size_t index) {
    cassert(index < m_n_elems);
    std::byte* loc = m_buff + index * tSURFEL_V2S_DATA<SFL_TYPE_TAG>::size();
    return reinterpret_cast<tSURFEL_V2S_DATA<SFL_TYPE_TAG>*>(loc);
  }

private:
  tSURFEL_V2S_DATA_MEM_POOL() = default;
  std::byte* m_buff;
  size_t m_n_elems;  
};

using sSURFEL_V2S_DATA_MEM_POOL = tSURFEL_V2S_DATA_MEM_POOL<SFL_SDFLOAT_TYPE_TAG>;
void allocate_host_v2s_buffer();

#if BUILD_GPU
using sMSFL_V2S_DATA_MEM_POOL = tSURFEL_V2S_DATA_MEM_POOL<MSFL_SDFLOAT_TYPE_TAG>;
#endif

#if BUILD_GPU
using sMSFL_V2S_DATA = tSURFEL_V2S_DATA<MSFL_SDFLOAT_TYPE_TAG>;
using MSFL_V2S_DATA = sMSFL_V2S_DATA*;
#endif

template<typename SURFEL_TYPE> struct tSURFEL_GROUP;
typedef tSURFEL_GROUP<sSURFEL> sSURFEL_GROUP, *SURFEL_GROUP;

struct sSURFEL_PAIR;

inline namespace
SIMULATOR_NAMESPACE {

//------------------------------------------------------------------------------
// SURFEL
// Aligning in_states and out flux on a multiple of 64-bits is advantageous so
// state parity pairs always reside in a single cache line. This will help all 
// surfel advection processes.
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL : public tSURFACE_SHOB<SFL_TYPE_TAG>
{
  EXTRACT_SURFEL_TRAITS
  using sSURFEL_LRF_DATA = tSURFEL_LRF_DATA<SFL_TYPE_TAG>;
  static constexpr size_t ALIGNMENT = (N == 1)? 16 : (GPU::MINIMUM_CUDA_ALIGNMENT * 2);
      
  tSURFEL(): tSURFACE_SHOB<SFL_TYPE_TAG>() {
    memset(this, 0, sizeof(*this));
  }
  
  tSURFEL*                 m_next;
  tSURFEL*                 m_prev;
  sSURFEL_GROUP* m_group;

  // XDU: store the pointer to surfel pair to ease APM surfel pair ckpting.
  sSURFEL_PAIR*  m_surfel_pair;

  const static size_t outflux_size = sizeof(SURFEL_STATE) * N_SURFEL_PGRAM_VOLUMES;

  __HOST__DEVICE__ uINT32 surfel_type() const {return this->m_surfel_attributes.surfel_type();}
  VOID set_group(sSURFEL_GROUP* group)  { m_group = group; }
  
  sSURFEL_PAIR* get_surfel_pair() { return m_surfel_pair; }
  VOID set_surfel_pair(sSURFEL_PAIR* surfel_pair) { m_surfel_pair = surfel_pair; }


  tSFL_VAR<STP_SURFEL_WEIGHT, N> in_state_scale_factors[N_LATTICE_VECTOR_PAIRS];  

  union {
    tSFL_VAR<STP_SURFEL_WEIGHT, N> voxel_surfel_weight_total; // only used during initialization           
    cNEIGHBOR_SP m_surfel_group_dest_sp;         // Used for recording dest SP of surfels
  }; 
                   
  // Height above surface of MME sample
  tSFL_VAR<STP_GEOM_VARIABLE, N> y_sample;
  
  // Fraction of V2S as opposed to S2S.
  tSFL_VAR<STP_GEOM_VARIABLE, N> percent_v2s;
  
  tSFL_VAR<STP_SURFEL_WEIGHT, N> seed_mme_weight;
  tSFL_VAR<STP_SURFEL_WEIGHT, N> mme_weight;
  tSFL_VAR<STP_SURFEL_WEIGHT, N> s2s_sampling_weight;

  union {
    // Temporary double precision pgram volumes are used only during initialization to compute
    // post-advect scale factors
    struct {
      dFLOAT m_dp_pgram_volumes[N_SURFEL_PGRAM_VOLUMES];
      sSURFEL_LRF_DATA   m_lrf_solver_data_from_ckpt;
    };
    // temporary field used during initialization to store the associated
    // surface coupling window meas index. Is used in sequence after the pgram
    // volumes and before the voxel weight data is assigned.
    sINT32 coupling_meas_index;
    struct {
      tSFL_VAR<STP_SURFEL_WEIGHT, N> in_states_voxel_weight [N_SURFEL_PGRAM_VOLUMES];
      sSURFEL_LRF_DATA  lrf_solver_data;
      tSFL_VAR<STP_SURFEL_WEIGHT, N> in_states_voxel_weight2[N_SURFEL_PGRAM_VOLUMES];
    };
  };

  __HOST__DEVICE__ dFLOAT *dp_pgram_volumes() { return m_dp_pgram_volumes; } // required for templated code
  // Used during initialization for storing meas desc and meas cell indices.
  // TODO: Can be stored on top of some other quantity.
  uINT32  m_seed_from_meas_desc_index;
  uINT32  m_seed_from_meas_cell_index;
  STP_PROC   m_home_sp;
  tSFL_VAR<uINT8, N>  m_vr_phase_mask;
  STP_PROC home_sp() {
    return m_home_sp;
  }

  VOID rescale_seed_from_meas_data();

  // SURFEL_CENTRIC: surfel-ublk interaction data (32 bytes)
  // m_ublk_interactions will be of size m_n_ublk_interactions
  // m_weights and m_latvecs will be of size m_n_total_wegiths
  
  void add_ublk_interactions(uINT16 n_ublk_interactions, SURFEL_UBLK_INTERACTION ublk_interactions);

  void clear_ublk_interactions();

  void add_surfel_interactions(DGF_SURFEL_DESC surfel_desc);

  // Solver data-block access methods (LB data block is always present)
  using sSURFEL_LB_DATA = tSURFEL_LB_DATA<SFL_TYPE_TAG>;
  using sSURFEL_TURB_DATA = tSURFEL_TURB_DATA<SFL_TYPE_TAG>;
  using sSURFEL_S2S_LB_DATA = tSURFEL_S2S_LB_DATA<SFL_TYPE_TAG>;
  using sSURFEL_S2S_T_DATA = tSURFEL_S2S_T_DATA<SFL_TYPE_TAG>;
  using sSURFEL_S2S_UDS_DATA = tSURFEL_S2S_UDS_DATA<SFL_TYPE_TAG>;
  using sSURFEL_T_DATA = tSURFEL_T_DATA<SFL_TYPE_TAG>;
  using sSURFEL_MC_DATA = tSURFEL_MC_DATA<SFL_TYPE_TAG>;
  using sSURFEL_CONDUCTION_DATA = tSURFEL_CONDUCTION_DATA<SFL_TYPE_TAG>;
  using sSURFEL_FROZEN_DATA = tSURFEL_FROZEN_DATA<SFL_TYPE_TAG>;
  using sSURFEL_UDS_DATA = tSURFEL_UDS_DATA<SFL_TYPE_TAG>;
  using sSURFEL_PF_DATA = tSURFEL_PF_DATA<SFL_TYPE_TAG>;
  using sS2S_ADVECT_DATA = tS2S_ADVECT_DATA<SFL_TYPE_TAG>;
  using sSURFEL_MLRF_DATA = tSURFEL_MLRF_DATA<SFL_TYPE_TAG>;
  using sSURFEL_EVEN_ODD_DATA = tSURFEL_EVEN_ODD_DATA<SFL_TYPE_TAG>;
  using sSURFEL_V2S_LB_DATA = tSURFEL_V2S_LB_DATA<SFL_TYPE_TAG>;
  using sSURFEL_V2S_TURB_DATA = tSURFEL_V2S_TURB_DATA<SFL_TYPE_TAG>;
  using sSURFEL_V2S_T_DATA = tSURFEL_V2S_T_DATA<SFL_TYPE_TAG>;
  using sSURFEL_S2S_MC_DATA = tSURFEL_S2S_MC_DATA<SFL_TYPE_TAG>;
  using sSURFEL_SHELL_CONDUCTION_DATA = tSURFEL_SHELL_CONDUCTION_DATA<SFL_TYPE_TAG>;
  using sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA = tSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA<SFL_TYPE_TAG>;
  using sCONDUCTION_INTERFACE_BASE_DATA = tCONDUCTION_INTERFACE_BASE_DATA<SFL_TYPE_TAG>;
  using sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA = tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG>;
  using sCONDUCTION_INTERFACE_FLUID_DATA = tCONDUCTION_INTERFACE_FLUID_DATA<SFL_TYPE_TAG>;
  using sCONDUCTION_INTERFACE_SOLID_DATA = tCONDUCTION_INTERFACE_SOLID_DATA<SFL_TYPE_TAG>;
  using sCONDUCTION_INTERFACE_OPEN_SHELL_DATA = tCONDUCTION_INTERFACE_OPEN_SHELL_DATA<SFL_TYPE_TAG>;
  using sSURFEL_RADIATION_DATA = tSURFEL_RADIATION_DATA<SFL_TYPE_TAG>;
  using sSURFEL_V2S_UDS_DATA = tSURFEL_V2S_UDS_DATA<SFL_TYPE_TAG>;
  using sSURFEL_V2S_PF_DATA = tSURFEL_V2S_PF_DATA<SFL_TYPE_TAG>;

  // TO DO, the code below just makes the compiler happy
  using sSURFEL_PARTICLE_DATA = ::sSURFEL_PARTICLE_DATA;
  using sSURFEL_MIRROR_DATA = ::sSURFEL_MIRROR_DATA;
  using sSURFEL_DYNAMICS_DATA = ::sSURFEL_DYNAMICS_DATA;
  
  
  INLINE __HOST__DEVICE__ static asINT32 get_surfel_data_offset(asINT32 surfel_type, asINT32 data_type) {
    return ::get_surfel_data_offset<N>(surfel_type, data_type);
  }

  INLINE __HOST__ static asINT32& set_surfel_data_offset(asINT32 surfel_type, asINT32 data_type) {
    return ::set_surfel_data_offset<N>(surfel_type, data_type);
  }
  
  __HOST__DEVICE__ sSURFEL_LB_DATA* lb_data() { return &m_lb_data; }
  __HOST__DEVICE__ const sSURFEL_LB_DATA* lb_data() const { return &m_lb_data; }

  __HOST__DEVICE__ sSURFEL_TURB_DATA*   turb_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_TURB_DATA_TYPE) > 0);
    return (sSURFEL_TURB_DATA*)((char*)this + sizeof(tSURFEL));
  }

  __HOST__DEVICE__ sSURFEL_S2S_LB_DATA *s2s_lb_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_DATA_TYPE) > 0);
    return (sSURFEL_S2S_LB_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_S2S_T_DATA *s2s_t_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_T_DATA_TYPE) > 0);
    return (sSURFEL_S2S_T_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_T_DATA_TYPE));
  }

  sSURFEL_S2S_MC_DATA *s2s_mc_data() {
    cassert(get_surfel_data_offset(surfel_type(),SURFEL_OUTFLUX_MC_DATA_TYPE) > 0);
    return (sSURFEL_S2S_MC_DATA *)((char*)this + get_surfel_data_offset(surfel_type(),SURFEL_OUTFLUX_MC_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_S2S_UDS_DATA *s2s_uds_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_UDS_DATA_TYPE) > 0);
    return (sSURFEL_S2S_UDS_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_UDS_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_S2S_UDS_DATA *s2s_uds_data(asINT32 nth_uds) {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_UDS_DATA_TYPE) > 0);
    return (sSURFEL_S2S_UDS_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_OUTFLUX_UDS_DATA_TYPE)) + nth_uds;
  }

  __HOST__DEVICE__ sSURFEL_T_DATA* t_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_T_DATA_TYPE) > 0);
    return (sSURFEL_T_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_T_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_MC_DATA *mc_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_MC_DATA_TYPE) > 0);
    return (sSURFEL_MC_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_MC_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_CONDUCTION_DATA* conduction_data() {
#if !BUILD_GPU
    dassert(get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_DATA_TYPE) > 0);
    return (sSURFEL_CONDUCTION_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_DATA_TYPE));
#else
    assert(false);
    return nullptr;
#endif
  }

  sCONDUCTION_INTERFACE_FLUID_DATA* conduction_interface_fluid_data() {
#if !BUILD_GPU
    dassert(get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_FLUID_DATA_TYPE) > 0);
    return (CONDUCTION_INTERFACE_FLUID_DATA)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_FLUID_DATA_TYPE));
#else
    assert(false);
    return nullptr;
#endif
  }

  sCONDUCTION_INTERFACE_SOLID_DATA* conduction_interface_solid_data() {
#if !BUILD_GPU
    dassert(get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE) > 0);
    return (CONDUCTION_INTERFACE_SOLID_DATA)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_SOLID_DATA_TYPE));
#else
    assert(false);
#endif
  }

  sCONDUCTION_INTERFACE_OPEN_SHELL_DATA* conduction_interface_open_shell_data() {
#if !BUILD_GPU
    dassert(get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_SHELL_DATA_TYPE) > 0);
    return (CONDUCTION_INTERFACE_OPEN_SHELL_DATA)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_CONDUCTION_INTERFACE_SHELL_DATA_TYPE));
#else
    assert(false);
#endif
  }

  sCONDUCTION_INTERFACE_BASE_DATA* conduction_interface_base_data() {
#if !BUILD_GPU
    if (this->is_conduction_surface()) { //conduction closed shell
      return (sCONDUCTION_INTERFACE_BASE_DATA*)conduction_interface_solid_data();
    } else if (this->is_conduction_shell()) { //conduction open shell
      return (sCONDUCTION_INTERFACE_BASE_DATA*)conduction_interface_open_shell_data();
    } else { //fluid
      return (sCONDUCTION_INTERFACE_BASE_DATA*)conduction_interface_fluid_data();
    }
#else
    assert(false);
#endif
  }

  sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA* conduction_interface_sampled_base_data() {
#if !BUILD_GPU
    if (this->is_conduction_surface()) { //conduction closed shell
      return (sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA*)conduction_interface_solid_data();
    } else if (this->is_conduction_shell()) {
      msg_internal_error("Open shells do not have sampled data");
      return nullptr;
    } else { //fluid
      return (sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA*)conduction_interface_fluid_data();
    }
#else
    assert(false);
#endif
  }

  __HOST__DEVICE__ sSURFEL_RADIATION_DATA* radiation_data() {
#if !BUILD_GPU
    dassert(get_surfel_data_offset(surfel_type(), SURFEL_RADIATION_DATA_TYPE) > 0);
    return (sSURFEL_RADIATION_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_RADIATION_DATA_TYPE));
#else
    assert(false);
#endif
  }

#if BUILD_D19_LATTICE
  __HOST__DEVICE__ sSURFEL_FROZEN_DATA* frozen_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_FROZEN_DATA_TYPE) > 0);
    return (sSURFEL_FROZEN_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_FROZEN_DATA_TYPE));
  }
#endif

  __HOST__DEVICE__ sSURFEL_PARTICLE_DATA* p_data() {
#if BUILD_GPU
    //Particle data is not supported just yet for GPU builds
    assert(false);
    return nullptr;
#else    
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_P_DATA_TYPE) > 0);
    return (SURFEL_PARTICLE_DATA)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_P_DATA_TYPE));
#endif
  }
  
  __HOST__DEVICE__ sSURFEL_UDS_DATA* uds_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_UDS_DATA_TYPE) > 0);
    return (sSURFEL_UDS_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_UDS_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_UDS_DATA *uds_data(asINT32 nth_uds) {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_UDS_DATA_TYPE) > 0);
    return (sSURFEL_UDS_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_UDS_DATA_TYPE)) + nth_uds;
  }

  __HOST__DEVICE__  sSURFEL_PF_DATA*      pf_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_PF_DATA_TYPE) > 0);    
    return (sSURFEL_PF_DATA*)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_PF_DATA_TYPE));
  }

  __HOST__DEVICE__ sS2S_ADVECT_DATA *s2s_advect_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_S2S_ADVECT_DATA_TYPE) > 0);
    return (sS2S_ADVECT_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_S2S_ADVECT_DATA_TYPE));
  }

  __HOST__DEVICE__ tSFL_VAR<SURFEL_STATE, N>* curr_outflux(asINT32 timestep_toggle) {
    return s2s_lb_data()->m_out_flux[timestep_toggle];
  }

  __HOST__DEVICE__ auto curr_ustar_0() {
    return s2s_lb_data()->ustar_0;
  }

  __HOST__DEVICE__ tSFL_VAR<SURFEL_STATE, N>* curr_outflux_t(asINT32 timestep_toggle) {
    return s2s_t_data()->m_out_flux_t[timestep_toggle];
  }

  auto curr_outflux_mc(asINT32 timestep_toggle) {
    return s2s_mc_data()->m_out_flux[timestep_toggle];
  }

  __HOST__DEVICE__ tSFL_VAR<SURFEL_STATE, N>* curr_outflux_uds(asINT32 timestep_toggle) {
    return s2s_uds_data()->m_out_flux_uds[timestep_toggle];
  }

  __HOST__DEVICE__ tSFL_VAR<SURFEL_STATE, N>* curr_outflux_uds(asINT32 timestep_toggle, asINT32 nth_uds) {
    return s2s_uds_data(nth_uds)->m_out_flux_uds[timestep_toggle];
  }

  /*  SURFEL_STATE *prev_outflux(asINT32 timestep_toggle) {
      return (SURFEL_STATE *)(s2s_lb_data()->m_out_flux[1-timestep_toggle]);
      }*/

  __HOST__DEVICE__ sSURFEL_MIRROR_DATA *mirror_data() {
#if BUILD_GPU
    assert(false); return nullptr;
#else    
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_MIRROR_DATA_TYPE) > 0);
    return (sSURFEL_MIRROR_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_MIRROR_DATA_TYPE));
#endif
  }

  __HOST__DEVICE__ sSURFEL_MLRF_DATA *mlrf_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_MLRF_DATA_TYPE) > 0);
    return (sSURFEL_MLRF_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_MLRF_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_EVEN_ODD_DATA *even_odd_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_EVEN_ODD_DATA_TYPE) > 0);
    return (sSURFEL_EVEN_ODD_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_EVEN_ODD_DATA_TYPE));
  }

  __HOST__DEVICE__ const sSURFEL_V2S_LB_DATA *v2s_lb_data() const {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_LB_DATA_TYPE) > 0);
    return (const sSURFEL_V2S_LB_DATA *)((const std::byte*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_LB_DATA_TYPE));
  }
  __HOST__DEVICE__ sSURFEL_V2S_LB_DATA *v2s_lb_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_LB_DATA_TYPE) > 0);
    return (sSURFEL_V2S_LB_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_LB_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_V2S_TURB_DATA *v2s_turb_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_TURB_DATA_TYPE) > 0);
    return (sSURFEL_V2S_TURB_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_TURB_DATA_TYPE));
  }

  __HOST__DEVICE__ sSURFEL_V2S_T_DATA *v2s_t_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_T_DATA_TYPE) > 0);
    return (sSURFEL_V2S_T_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_T_DATA_TYPE));
  }

  sSURFEL_SHELL_CONDUCTION_DATA * shell_conduction_data() {
#if !BUILD_GPU
    cassert(this->is_conduction_shell());
    return (sSURFEL_SHELL_CONDUCTION_DATA*)((char *)this + get_surfel_data_offset(surfel_type(), SURFEL_SHELL_CONDUCTION_DATA_TYPE));
#else
    assert(false);
    return nullptr;
#endif
  }

  sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA * shell_conduction_implicit_solver_data() {
#if !BUILD_GPU
    cassert(this->is_conduction_shell());
    return (sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA*)((char *)this + get_surfel_data_offset(surfel_type(), SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA_TYPE));
#else
    assert(false);
    return nullptr;
#endif
  }

  __HOST__DEVICE__ sSURFEL_V2S_UDS_DATA *v2s_uds_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_UDS_DATA_TYPE) > 0);
    return (sSURFEL_V2S_UDS_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_UDS_DATA_TYPE));
  }

   __HOST__DEVICE__ sSURFEL_V2S_UDS_DATA *v2s_uds_data(asINT32 nth_uds) {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_UDS_DATA_TYPE) > 0);
    return (sSURFEL_V2S_UDS_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_UDS_DATA_TYPE)) + nth_uds;
  }

   __HOST__DEVICE__ sSURFEL_V2S_PF_DATA *v2s_pf_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_V2S_PF_DATA_TYPE) > 0);
    return (sSURFEL_V2S_PF_DATA *)((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_V2S_PF_DATA_TYPE));
  }

  __HOST__DEVICE__ tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dynamics_data() {
    cassert(get_surfel_data_offset(surfel_type(), SURFEL_DYN_DATA_TYPE) > 0);
    
    return (tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *)
      ((char*)this + get_surfel_data_offset(surfel_type(), SURFEL_DYN_DATA_TYPE));
  }

  // The front and back dynamics data blocks are stored back-to-back (front followed by back)
  // To retrieve the back, use the next_dynamics_data() method provided by the front dynamics data
  tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *front_dynamics_data() { return dynamics_data(); }

  __HOST__DEVICE__ tBC_TURB_DATA<SFL_TYPE_TAG> *dyn_turb_data(); 
  __HOST__DEVICE__ sSURFEL_LRF_DATA* lrf_data() { return &lrf_solver_data; }

  __HOST__DEVICE__ sLRF_PHYSICS_DESCRIPTOR *lrf_physics_descriptor() {
    LRF_PHYSICS_DESCRIPTOR lrf = NULL;
    if (this->ref_frame_index() >= 0) {
      auto& sim = get_sim_ref();
      lrf = &sim.lrf_physics_descs[this->ref_frame_index()];
    }
    return lrf;
  }

  BOOLEAN is_surfel_moving() {
    return lrf_is_rotating(lrf_physics_descriptor());
  }

  tSURFEL *clone_surfel();

  auto pgram_volumes() { return lb_data()->pgram_volumes; } // required for templated code (see surfel_dyn.h)

  static BOOLEAN     uses_shared_mem()  { return FALSE; }
  static cSTRING     name()             { return "DYN_SURFEL";}
  static SHOB_TYPE   static_type()      { return SURFEL_TYPE; }
  //static asINT32     states_offset()    { return struct_field_disp(sSURFEL *, lb_data()->in_states); }
  //static asINT32     states_size()      { return struct_field_size(sSURFEL *, lb_data()->in_states); }
  static asINT32     states_offset()    { return 0; }
  static asINT32     states_size()      { return 0; }

  BOOLEAN is_home_voxel(STP_LOCATION voxel_location, asINT32 voxel_size);

  VOID pre_advect_init(ACTIVE_SOLVER_MASK mask) {
    msg_internal_error("Never called");
  }

  __HOST__DEVICE__ VOID pre_advect_init_copy_even_to_odd(ACTIVE_SOLVER_MASK mask,
                                                         tSURFEL *even_surfel,
                                                         asINT32 osoxor,
                                                         asINT32 esoxor) {
    auto& simc = get_simc_ref();
    STP_SURFEL_WEIGHT mme_weight_inverse = even_surfel->mme_weight[esoxor] == 0 ?  0 : 1.0F / even_surfel->mme_weight[esoxor]; 
    STP_SURFEL_WEIGHT s2s_sampling_weight_inverse = even_surfel->s2s_sampling_weight[esoxor] == 0 ? 0 : 1.0F / even_surfel->s2s_sampling_weight[esoxor];
    if (simc.is_lb_model) {
      v2s_lb_data()->pre_advect_init_copy_even_to_odd(even_surfel->v2s_lb_data(),
                                                      even_surfel->v2s_lb_data()->m_density[esoxor],
                                                      mme_weight_inverse,
                                                      s2s_sampling_weight_inverse,
                                                      osoxor, esoxor);
    }
    if (simc.is_turb_model) {
      v2s_turb_data()->pre_advect_init_copy_even_to_odd(even_surfel->v2s_turb_data(),
                                                        even_surfel->v2s_lb_data()->m_density[esoxor],
                                                        mme_weight_inverse,
                                                        s2s_sampling_weight_inverse,
                                                        osoxor, esoxor);
      turb_data()->s_mag[osoxor] = even_surfel->turb_data()->s_mag[esoxor];
      turb_data()->gamma_swirl[osoxor] = even_surfel->turb_data()->gamma_swirl[esoxor];
    }
    if (simc.is_heat_transfer) {
      v2s_t_data()->pre_advect_init_copy_even_to_odd(even_surfel->v2s_t_data(),
                                                     even_surfel->v2s_lb_data()->m_density[esoxor],
                                                     mme_weight_inverse,
                                                     s2s_sampling_weight_inverse,
                                                     osoxor, esoxor);
      t_data()->pre_advect_init_copy_even_to_odd(even_surfel->t_data(),
						 even_surfel->v2s_lb_data()->m_density[esoxor],
						 mme_weight_inverse,
						 s2s_sampling_weight_inverse,
                                                 osoxor, esoxor);
      /* Since the temp_sample is rescaled in surfel dynamics, we need a pre-rescaled
       * (real post-advect) temp_sample from even_surfel to update odd_surfel. */
      /*t_data()->temp_sample = even_surfel->t_data()->temp_sample *
	(g_use_lrf_s2s_sampling_temp ? s2s_sampling_weight_inverse : mme_weight_inverse); */
    }
    if (simc.is_scalar_model) {
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
	v2s_uds_data(nth_uds)->pre_advect_init_copy_even_to_odd(even_surfel->v2s_uds_data(nth_uds),
								even_surfel->v2s_lb_data()->m_density[esoxor],
								mme_weight_inverse,
								s2s_sampling_weight_inverse,
								osoxor, esoxor);
	
	uds_data(nth_uds)->uds_value[osoxor] = even_surfel->uds_data(nth_uds)->uds_value[esoxor] *
					   (g_use_lrf_s2s_sampling_uds ? s2s_sampling_weight_inverse : mme_weight_inverse);		
      }
#if BUILD_D19_LATTICE
      if (simc.is_pf_model) {
        v2s_pf_data()->pre_advect_init_copy_even_to_odd(even_surfel->v2s_pf_data(),
							even_surfel->v2s_lb_data()->m_density[esoxor],
							mme_weight_inverse,
							s2s_sampling_weight_inverse,
							osoxor, esoxor);
      }
#endif
    }
  }

  __HOST__ VOID pre_advect_init_copy_even_to_odd(ACTIVE_SOLVER_MASK mask,
                                                 tSURFEL *even_surfel);  

  VOID seed_copy_even_to_odd(tSURFEL *even_surfel, BOOLEAN is_full_checkpoint_restore,
                             BOOLEAN is_mirror);

  __DEVICE__
  VOID copy_active_data_for_mlrf_surfels(MLRF_PRE_DYNAMICS_DEPOT pre_dynamics_depot,
                                         ACTIVE_SOLVER_MASK active_solver_mask,
                                         REQUIRE_INDEX_IF_MSFL(soxor));

  uINT64 ckpt_len(bool verbose=false);
  VOID write_ckpt();
  size_t write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();

  VOID init_outflux_copied();

  uINT64 surfel_pair_dyn_ckpt_len();
  VOID read_surfel_pair_dyn_ckpt();
  VOID write_surfel_pair_dyn_ckpt();
  VOID write_surfel_pair_dyn_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  
  sSURFEL_MEAS_CELL_PTR *create_meas_cell_ptrs(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data,
                                               asINT32 n_meas_cells);

  sSURFEL_MEAS_CELL_PTR *add_meas_cell_ptr(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dyn_data);

  VOID resolve_meas_cell_ptrs();

  VOID fill_dynamics_data(DGF_SURFEL_DESC surfel_desc,
                          STP_PHYSTYPE_TYPE sim_phys_type,
                          sPHYSICS_DESCRIPTOR *surface_phys_desc,
                          dFLOAT sum_pgram_volume[N_NONZERO_ENERGIES],
                          cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data);

  VOID fill_mirror_data(tSURFEL *mirror_surfel, STP_DIRECTION direction,
                        STP_UNIVERSAL_LATVEC_MASK latvec_mask);

  VOID set_clone_surfel_index(uINT32 odd_surfel_index);

  // Call the init() method of all solver data blocks
  VOID init(DGF_SURFEL_DESC surfel_desc);

  VOID fill_send_bc_type_buffer(SURFEL_BCTYPE_SEND_ELEMENT element);
  VOID unpack_recv_bc_type_buffer(SURFEL_BCTYPE_SEND_ELEMENT element);
  VOID fill_send_init_info_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element);
  VOID unpack_recv_init_info_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element);
  VOID fill_send_surfel_type_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element);
  VOID unpack_recv_surfel_type_buffer(SURFEL_INIT_INFO_SEND_ELEMENT element);
  VOID upgrade_surfel_type(uINT32 surfel_type);
  VOID count_neighbor_ublks_bytes(uINT32 &num_bytes);
  VOID fill_neighbor_ublks(uINT8 * &buffer);
  VOID unpack_neighbor_ublks(uINT8  * &buffer);

#if BUILD_5G_LATTICE
  VOID fill_send_potential_buffer(SURFEL_POTENTIAL_SEND_ELEMENT element);
  VOID unpack_recv_potential_buffer(SURFEL_POTENTIAL_SEND_ELEMENT element);
#endif

  static VOID add_send_size_static_flow(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size, asINT32 &tpde_send_size) {
    if (is_solver_active(LB_SOLVER, active_solver_mask)) {
      sSURFEL_LB_DATA ::add_send_size(send_size, tpde_send_size);
      sSURFEL_V2S_LB_DATA ::add_send_size(send_size, tpde_send_size);
    }
    if (is_solver_active(TURB_SOLVER, active_solver_mask)) {
      sSURFEL_TURB_DATA ::add_send_size(send_size, tpde_send_size);
      sSURFEL_V2S_TURB_DATA ::add_send_size(send_size, tpde_send_size);
      // Even we only need bc_turb_data for inlet and outlet surfels, we should allocate the space here since 
      // we don't know the ghost surfel type when the recv buffer is being allocated.
      sBC_TURB_DATA ::add_send_size(send_size, tpde_send_size);
    }
    if (is_solver_active(T_SOLVER, active_solver_mask)) {
      sSURFEL_T_DATA ::add_send_size(send_size, tpde_send_size);
      sSURFEL_V2S_T_DATA ::add_send_size(send_size, tpde_send_size);
    }
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask)) {
      sSURFEL_MC_DATA::add_send_size(send_size, tpde_send_size);
    }
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
      sSURFEL_UDS_DATA::add_send_size(send_size, tpde_send_size);
      sSURFEL_V2S_UDS_DATA ::add_send_size(send_size, tpde_send_size);
      if(sim.is_pf_model) {
        sSURFEL_PF_DATA::add_send_size(send_size, tpde_send_size);
        sSURFEL_V2S_PF_DATA::add_send_size(send_size, tpde_send_size);
      }
    }
#if BUILD_D19_LATTICE
    if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, active_solver_mask)) {
      sSURFEL_FROZEN_DATA::add_send_size(send_size, tpde_send_size);
    }
#endif
  }
  
  VOID add_send_size(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size, asINT32 &tpde_send_size) {
    if (this->is_conduction_surfel()) {
      if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask)) {
        if (this->is_conduction_surface()) sSURFEL_CONDUCTION_DATA::add_send_size(send_size, tpde_send_size);
        if (this->is_conduction_shell()) this->shell_conduction_data()->add_send_size(send_size, tpde_send_size);
      }
    } else {
      add_send_size_static_flow(active_solver_mask, send_size, tpde_send_size);
    }
    if (this->is_lrf()) {
      sSURFEL_LRF_DATA::add_send_size(send_size, tpde_send_size);
    }
  }
  
  VOID fill_film_data(sdFLOAT * &send_buffer) {
    p_data()->fill_film_data(send_buffer);
  }

  VOID fill_solid_parcel_data(sdFLOAT * &send_buffer) {
    p_data()->fill_solid_parcel_data(send_buffer);
  }

  VOID fill_send_buffer(ACTIVE_SOLVER_MASK active_solver_mask, sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_APPLY_ACTIVE_DUAL(active_solver_mask, fill_send_buffer, (send_data_info));
    if (!this->is_conduction_surfel()) {
#if BUILD_D19_LATTICE
      if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, active_solver_mask))
        this->frozen_data()->fill_send_buffer(send_data_info);
#endif
    }
    if (this->is_lrf())
      this->lrf_data()->fill_send_buffer(send_data_info);
  }

  template <typename T>
  VOID wall_velocity(T wall_velocity[3]);

  VOID accumulate_film_data(sdFLOAT * &recv_buffer) {
    p_data()->accumulate_film_data(recv_buffer);
  }

  VOID accumulate_solid_parcel_data(sdFLOAT * &recv_buffer) {
    p_data()->accumulate_solid_parcel_data(recv_buffer);
  }

  VOID expand_recv_buffer(ACTIVE_SOLVER_MASK active_solver_mask, sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_APPLY_ACTIVE_DUAL(active_solver_mask, expand_recv_buffer, (recv_data_info));
    if (!this->is_conduction_surfel()) {
#if BUILD_D19_LATTICE
      if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, active_solver_mask))
        this->frozen_data()->expand_recv_buffer(recv_data_info);
#endif
    }
    if(this->is_lrf())
      this->lrf_data()->expand_recv_buffer(recv_data_info);
  }

  VOID reflect_from_mirror_surfel_to_real_surfel(sSURFEL *mirror_surfel,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                 ACTIVE_SOLVER_MASK mask) 
  {
    cassert(!this->is_conduction_surfel());
    SURFEL_APPLY_ACTIVE_4(mask, reflect_from_mirror_surfel_to_real_surfel, 
                          mirror_surfel, latvec_state_mask, reflected_latvec_pair, mirror_sign_factor);
  }
  
  VOID conduction_reflect_from_mirror_surfel_to_real_surfel(sSURFEL *mirror_surfel,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                 ACTIVE_SOLVER_MASK mask) 
  {
    cassert(this->is_conduction_surfel());
    this->conduction_data()->reflect_from_mirror_surfel_to_real_surfel(mirror_surfel->conduction_data());
  }

  VOID reflect_from_real_surfel_to_mirror_surfel(sSURFEL *surfel,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                 ACTIVE_SOLVER_MASK mask)
  {
    cassert(!surfel->is_conduction_surfel());
    SURFEL_APPLY_ACTIVE_4(mask, reflect_from_real_surfel_to_mirror_surfel,
                          surfel, latvec_state_mask, reflected_latvec_pair, mirror_sign_factor);
#if BUILD_D19_LATTICE
    if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, mask))
      this->frozen_data()->reflect_from_real_surfel_to_mirror_surfel(surfel->frozen_data(), latvec_state_mask, reflected_latvec_pair, mirror_sign_factor);
#endif
    if(surfel->is_lrf())
      this->lrf_data()->reflect_from_real_surfel_to_mirror_surfel(surfel->lrf_data() ,latvec_state_mask, reflected_latvec_pair, mirror_sign_factor);
  }
  
  VOID conduction_reflect_from_real_surfel_to_mirror_surfel(sSURFEL *surfel,
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                 ACTIVE_SOLVER_MASK mask)
  {
    cassert(surfel->is_conduction_surfel());
    this->conduction_data()->reflect_from_real_surfel_to_mirror_surfel(surfel->conduction_data());
  }

  // Solver data-blocks. Place the turb solver block immediately after the LB solver block to increase
  // cache locality for isothermal simulations (i.e. T solver not active).
  tSURFEL_LB_DATA<SFL_TYPE_TAG>    m_lb_data;
  
  static uINT64 size(asINT32 surfel_type, BOOLEAN is_mirror_surfel,
                     STP_PHYSTYPE_TYPE phys_type, STP_PHYSTYPE_TYPE back_phys_type = STP_INVALID_PHYSTYPE_TYPE) {
    size_t dynamics_data_size = 0;
    // mirror surfels do not contribute to measurement windows
    if (!is_mirror_surfel && phys_type != STP_MLRF_SURFEL_TYPE) {
      dynamics_data_size = surfel_dynamics_data_size(phys_type);
      if (back_phys_type != STP_INVALID_PHYSTYPE_TYPE)
        dynamics_data_size += surfel_dynamics_data_size(back_phys_type);
    }
    return (get_surfel_data_offset(surfel_type, SURFEL_DYN_DATA_TYPE) +
	    dynamics_data_size);
  }

  BOOLEAN has_dynamics_data() const {
    return !this->is_lrf() && !this->is_mirror();
  }

  SP_TIMER_TYPE timer();

  BOOLEAN does_surfel_have_data_of_type(SURFEL_DATA_TYPE type) {
    return (g_surfel_data_offset_table[surfel_type()][type]);
  }

  VOID print_surfel_content(std::ostream& os,const SURFEL_PRINT_OPTS& opts);

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const tSURFEL* h_ptr,
                                      const sMEAS_WINDOW_COLLECTION& windows);
}; //tSURFEL

using sSURFEL = tSURFEL<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL = sSURFEL*;

template<> 
__HOST__ INLINE VOID tSURFEL<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(ACTIVE_SOLVER_MASK mask,
                                                                  tSURFEL<SFL_SDFLOAT_TYPE_TAG> *even_surfel) {
  this->pre_advect_init_copy_even_to_odd(mask, even_surfel, 0, 0);
}

template<>
__HOST__ INLINE void tSURFEL<SFL_SDFLOAT_TYPE_TAG>::add_ublk_interactions(uINT16 n_ublk_interactions,
                                                       SURFEL_UBLK_INTERACTION ublk_interactions) {
  this->m_n_ublk_interactions = n_ublk_interactions;
  this->m_ublk_interactions = ublk_interactions;
}

template<>
__HOST__ INLINE void tSURFEL<SFL_SDFLOAT_TYPE_TAG>::clear_ublk_interactions() {
  this->m_n_ublk_interactions = 0;
  char* sfl_interactions_to_delete = (char*) this->m_ublk_interactions;
  delete[] (sfl_interactions_to_delete);
  this->m_ublk_interactions = nullptr;  
}

template<>
__HOST__DEVICE__ INLINE BOOLEAN tSURFEL<SFL_SDFLOAT_TYPE_TAG>::is_home_voxel(STP_LOCATION voxel_location, asINT32 voxel_size)
{
  sdFLOAT x_min = voxel_location[0];
  sdFLOAT y_min = voxel_location[1];
  sdFLOAT z_min = voxel_location[2];
  sdFLOAT x_max = x_min + voxel_size;
  sdFLOAT y_max = y_min + voxel_size;
  sdFLOAT z_max = z_min + voxel_size;

  return ((this->centroid[0] >= x_min) && (this->centroid[0] <= x_max) &&
          (this->centroid[1] >= y_min) && (this->centroid[1] <= y_max) &&
          (this->centroid[2] >= z_min) && (this->centroid[2] <= z_max));
}

#if BUILD_GPU
  using sMSFL = tSURFEL<MSFL_SDFLOAT_TYPE_TAG>;
  using MSFL = sMSFL*;
#endif  
  
} // SIMULATOR_NAMESPACE

VOID print_surfel_states(const char *print_string, SURFEL_V2S_DATA surfel_v2s_data, SURFEL surfel, std::ostream& os = std::cout);

VOID find_ublk_interactions(REALM realm,
                            DGF_SURFEL_DESC surfel_desc,
                            uINT16 *p_n_ublk_interactions,
                            sSURFEL_UBLK_INTERACTION **p_ublk_interactions,
                            BOOLEAN is_ghost);

VOID initialize_dynamics_data(asINT32 fluid_region_indices[2],
                              STP_PHYSTYPE_TYPE sim_phys_type,
                              PHYSICS_DESCRIPTOR physics_desc,
                              sSURFEL_PAIR *surfel_PAIR);
struct sSURFEL_PAIR_GROUP;  // forward declaration

typedef struct sSURFEL_PAIR
{
  sSURFEL *m_interior_surfel;
  sSURFEL *m_exterior_surfel;
  // JEH This should be of type eISURFEL_DYN_TYPE, but that requires including isurfel_dyn_sp.h, which causes a mess...
  sINT32 m_dynamics_type;
  // Inverse of the sum of the parallelogram volumes
  STP_GEOM_VARIABLE m_inverse_sum_volumes[N_NONZERO_ENERGIES];

#if BUILD_D39_LATTICE
  STP_GEOM_VARIABLE m_inverse_total_volume_d39;
  STP_GEOM_VARIABLE m_inverse_total_volume_d19;
#endif

  sSURFEL_PAIR* m_next;
  sSURFEL_PAIR* m_prev;
  sSURFEL_PAIR_GROUP* m_group;

  // Used when adding surfel pairs in the group in id order
  SHOB_ID id() { return m_interior_surfel->id(); }

  VOID set_group(sSURFEL_PAIR_GROUP* group) { m_group = group; }
  asINT32 dynamics_type() {
    return m_dynamics_type;
  }

  VOID init(sSURFEL *interior_surfel, sSURFEL *exterior_surfel,
#if BUILD_D39_LATTICE
            dFLOAT inverse_total_volume_d39,
            dFLOAT inverse_total_volume_d19,
#endif
            dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES]) {
    m_interior_surfel = interior_surfel;
    m_exterior_surfel = exterior_surfel;
    ccDOTIMES(i, N_NONZERO_ENERGIES) {
      m_inverse_sum_volumes[i] = 1.0/sum_pgram_volumes[i];
    }
#if BUILD_D39_LATTICE
     m_inverse_total_volume_d39 = inverse_total_volume_d39;
     m_inverse_total_volume_d19 = inverse_total_volume_d19;
#endif
    m_next = NULL;
    m_prev = NULL;
  }

  VOID fill_interface_dynamics_data(asINT32 fluid_region_indices[2],
                                    STP_PHYSTYPE_TYPE sim_phys_type,
                                    PHYSICS_DESCRIPTOR physics_desc) {

#if !BUILD_5G_LATTICE
    initialize_dynamics_data(fluid_region_indices, sim_phys_type, physics_desc, this);
#endif
  }

  VOID resolve_meas_cell_ptrs(); 

  static size_t size(asINT32 surfel_pair_type,
                     STP_PHYSTYPE_TYPE sim_phys_type);
  
  // dynamics() is overwritten in derived classes like sAPM_SLIP_ISURFEL_DATA
  // MEAS_WINDOWS* window is not needed anymore since the window can be obtained from meas_cell_ptr.
  VOID dynamics(STP_PHYS_VARIABLE vel[3],
                sdFLOAT group_voxel_size, sdFLOAT meas_scale_factor,
                sdFLOAT one_over_nu_molecular, LRF_PHYSICS_DESCRIPTOR lrf,
                BOOLEAN is_moving_slip_surfel, BOOLEAN is_vel_tangential,
                ACTIVE_SOLVER_MASK active_solver_mask,
                ACTIVE_SOLVER_MASK full_active_solver_mask,
                sSURFEL_V2S_DATA *surfel_v2s_data)
  {
    return;
  }

  // Surfel pair is fringe if either surfel is fringe. The logic should be consistent in add_isurfel() and add_lrf_surfel()
  BOOLEAN is_fringe() {
    return (m_interior_surfel->is_fringe() || m_exterior_surfel->is_fringe());
  }
  
  VOID set_fringe() {
    m_interior_surfel->set_fringe(TRUE);
    m_exterior_surfel->set_fringe(TRUE);
  }

  // Surfel pair is fringe2 if either surfel is fringe2.
  BOOLEAN is_fringe2() {
    return (m_interior_surfel->is_fringe2() || m_exterior_surfel->is_fringe2());
  }

  VOID set_fringe2() {
    m_interior_surfel->set_fringe2(TRUE);
    m_exterior_surfel->set_fringe2(TRUE);
  }

  VOID ckpt_len() {}
  VOID read_ckpt() {}
  VOID write_ckpt() {}

} *SURFEL_PAIR;

union sTAGGED_S2S_SRC_SURFEL {
private:
  sSURFEL       *m_src_surfel;
  STP_SURFEL_ID  m_src_surfel_id;

public:
  sSURFEL *src_surfel() {
    return (sSURFEL *)scalar_mask_pointer(m_src_surfel, ~1);
  }
  STP_SURFEL_ID src_surfel_id() {
    return m_src_surfel_id;
  }
  BOOLEAN is_lrf_src() {
    return scalar_mask_pointer(m_src_surfel, 1) != 0;
  }
  VOID mark_as_lrf_src() {
    m_src_surfel = (sSURFEL *) scalar_or_pointer(m_src_surfel, 1);
  }
  VOID unmark_as_lrf_src() {
    m_src_surfel = (sSURFEL *) scalar_mask_pointer(m_src_surfel, ~1);
  }
  VOID set_surfel_id(STP_SURFEL_ID surfel_id) {
    m_src_surfel_id = surfel_id;
  }
  VOID set_src_surfel(sSURFEL *surfel) {
    m_src_surfel = surfel;
  }
};

typedef class sSURFEL_SURFEL_INTERACTION {
public:
  sTAGGED_S2S_SRC_SURFEL m_tagged_src_surfel;

  uINT8 m_n_weights;
  // even odd mask for src surfel
  uINT8 m_even_odd_mask;
private:
  uINT16 padding;
  STP_SURFEL_WEIGHT m_first_weight;

public:

  // Surfel surfel interaction information is split into two pieces
  // First piece contains the weights associated with surfel-surfel
  // interaction.
  // Second piece contains latvecs for each surfel-surfel interaction.
  STP_SURFEL_WEIGHT *weights() {
    return &m_first_weight;
  }

  uINT8 *latvecs() {
    return (uINT8 *)&m_first_weight + (m_n_weights) * sizeof(STP_SURFEL_WEIGHT);
  }
  static asINT32 size(uINT8 n_weights) {
    // Even if n_weights = 0, alignment with 8 byte boundary should
    // make the total_size = 16 bytes
    size_t total_size = sizeof(STP_SURFEL_WEIGHT) * (n_weights - 1)
                        + n_weights // for latvecs
                        + sizeof(sSURFEL_SURFEL_INTERACTION);
    // The end point will be aligned at the 8 byte boundary.
    return get_byte_aligned(total_size, sizeof(SURFEL));
  }

  asINT32 size() {
    // Even if n_weights = 0, alignment with 8 byte boundary should
    // make the total_size = 16 bytes
    size_t total_size = sizeof(STP_SURFEL_WEIGHT) * (m_n_weights - 1)
                        + m_n_weights // for latvecs
                        + sizeof(sSURFEL_SURFEL_INTERACTION);
    // The end point will be aligned at the 8 byte boundary.
    return get_byte_aligned(total_size, sizeof(SURFEL));
  }

} *SURFEL_SURFEL_INTERACTION;

inline namespace
SIMULATOR_NAMESPACE {

template<typename SFL_TYPE_TAG>
struct tS2S_ADVECT_DATA {
};

#if BUILD_GPU  
template<>
struct tS2S_ADVECT_DATA<MSFL_SDFLOAT_TYPE_TAG> {
  sMSFL_sMSFL_INTERACTIONS *m_surfel_interactions;
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const tS2S_ADVECT_DATA<MSFL_SDFLOAT_TYPE_TAG>* h_ptr);
};
#endif
  
template<>  
struct tS2S_ADVECT_DATA<SFL_SDFLOAT_TYPE_TAG> {
  uINT16                     m_n_src_surfels; // Number of surfels that contribute to DEST_SURFEL
  cBOOLEAN                   some_lrf_src;
  sSURFEL_SURFEL_INTERACTION *m_surfel_interactions;
  VOID create_surfel_interactions(DGF_SURFEL_DESC surfel_desc);

  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_S2S_ADVECT_DATA");
    sim_print(os, "m_n_src_surfels", m_n_src_surfels);
    sim_print(os, "some_lrf_src", static_cast<bool>(some_lrf_src));
  }

};

using sS2S_ADVECT_DATA = tS2S_ADVECT_DATA<SFL_SDFLOAT_TYPE_TAG>;
using S2S_ADVECT_DATA = sS2S_ADVECT_DATA*;
	 
}//SIMULATOR_NAMESPACE

VOID initialize_dynamics_data(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *dynamics_data,
                              STP_PHYSTYPE_TYPE sim_phys_type,
                              SURFEL surfel,
                              sPHYSICS_DESCRIPTOR *surface_phys_desc,
                              dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES],
                              cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data,
                              bool invert_normal = false);

//------------------------------------------------------------------------------
// sGHOST_SURFEL
//------------------------------------------------------------------------------
template<>
VOID sSURFEL::init(DGF_SURFEL_DESC surfel_desc);


uINT64 surfel_dyn_ckpt_len(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *sdyn_data);
VOID surfel_dyn_read_ckpt(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *sdyn_data);
VOID surfel_dyn_write_ckpt(tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *sdyn_data);
VOID surfel_dyn_write_ckpt(sCKPT_BUFFER& pio_ckpt_buff,tSURFEL_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR> *sdyn_data);

//------------------------------------------------------------------------------
// Footnote 1: Turbulence boundary conditions. An inlet/outlet surfel has either:
// (1) a specified intensity and length scale,
// (2) a specified turbulence kinetic energy and dissipation, or
// (3) it requires that k and epsilon be extrapolated.
//
// The actual type is only known by voxels in the vicinity, Maybe we
// should change this, so that a surfel knows which of the 3 it is.
//
// Note that this space is wasted for wall surfels.
//------------------------------------------------------------------------------

//------------------------------------------------------------------------------
// Footnote 2: IN_STATE_SCALE_FACTORS are used to scale up IN_STATES after voxel to
// surfel (V2S) and surfel to surfel (S2S) advection. During V2S, we
// collect both inward and outward pointing states. During S2S, however, 
// we only collect inward pointing states (since a surfel's outflux only
// includes outward pointing states). Thus we must scale up the outward
// pointing states to account for the missing piece from S2S.
//
//------------------------------------------------------------------------------

//------------------------------------------------------------------------------
// Footnote 3: Only save dynamic data. Saving static data can cause problems with the
// simulator initialization sequence when restoring from full checkpoint.
// The information of surfel scatter correction needs to be saved for ckpt 
// at nonequilibrium time step.
//------------------------------------------------------------------------------

#endif  /* _SIMENG_SURFEL_H */
