#ifndef EXA_SIMENG_RADIATION_COMM_H
#define EXA_SIMENG_RADIATION_COMM_H

#include "fset.h"
#include "comm_groups.h"
#include SP_H

#include <chrono>

struct sRAD_PATCH_SURFEL_PTR_TRAITS {

  enum ATTRIBUTES {
    BACKSIDE,
    N
  };

  using PTR = sSURFEL*;
  constexpr static auto ALIGNMENT = sSURFEL::ALIGNMENT;

  static constexpr size_t num_bits_for_attrib(ATTRIBUTES a) {
    return 1;
  }

};

using sRAD_PATCH_SURFEL_PTR = tTAGGED_PTR<sRAD_PATCH_SURFEL_PTR_TRAITS>;

class sRP_RECV_GROUP : public sRECV_GROUP_BASE
{

  mutable cExaMsg<std::byte> m_recv_msg;
  STP_PROC m_src_rp;
  size_t m_recv_patches;

public:

  explicit sRP_RECV_GROUP(SCALE scale, RP_PROC src_rp) : m_src_rp ( rp_proc_to_stp_proc(src_rp) ), m_recv_patches(0) {
    m_scale = scale;
  }

public:

  void allocate_recv_buffer(size_t n_patches);

  void post_recv() override final;

  virtual VOID complete_recv() override final { 
    complete_mpi_request_while_processing_cp_messages(m_recv_msg, MPI_SLEEP_LONG);
  }
  
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) override final { assert(false); }

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override final { assert(false); }

  bool is_recv_request_null() override final;

  void cancel_pending_recv() override final;

  virtual bool is_recv_ready() override final;

  const std::byte * buffer() const { return m_recv_msg.buffer(); }

  RP_PROC src_rp() const { return stp_proc_to_rp_proc(m_src_rp); }
};

class cRP_RECV_CHANNEL
{
public:

  using Clock = std::chrono::high_resolution_clock;
  using Time = Clock::time_point;
  using Duration = Clock::duration;
private:

  enum class eSTATE {
    RECEIVING,
    UNPACKING,
    POSTING,
  };
  
  std::vector<sRP_RECV_GROUP*> m_recv_groups;
  pthread_mutex_t m_rp_mutex;
  pthread_cond_t m_rp_cond;
  size_t m_recvs_left;
  eSTATE m_state;

  Duration m_total_wait_time;
  size_t m_wait_count;

public:

  void init(std::vector<sRP_RECV_GROUP*>&& recv_groups);

  bool process_posted_receives();

  // replace this with something integrated with strand_mgr.
  // called by the main thread
  Duration wait_for_receives();
  void set_post_receives();

  void cancel_pending_recvs();

  std::pair<double,size_t> report_wait_time_in_seconds() const noexcept {
    return {std::chrono::duration<double>(m_total_wait_time).count(), m_wait_count};
  }
};

class sRP_SEND_GROUP : public sSEND_GROUP_BASE
{
protected:
  cExaMsg<std::byte> m_send_msg;
  STP_PROC m_dest_rp;

public:

  explicit sRP_SEND_GROUP(SCALE scale, RP_PROC dest_rp) : m_dest_rp( rp_proc_to_stp_proc(dest_rp) )
  {
    m_scale = scale;
  }

  virtual void complete_send() override {
    complete_mpi_request_while_processing_cp_messages(m_send_msg, MPI_SLEEP_LONG);
  }

  STP_PROC dest_rp() const { return m_dest_rp; }

};

class sRP_SEND_SOLVE_INFO : public sRP_SEND_GROUP
{
public:
  enum {
    CURR_ACTIVE_SOLVER_MASK,
    NEXT_ACTIVE_SOLVER_MASK,
    FLOW_TIMESTEP,
    COND_TIMESTEP,
    N_VALUES,
  };

  int64_t values[N_VALUES];

  explicit sRP_SEND_SOLVE_INFO() : sRP_SEND_GROUP(-1,RP_PROC_INVALID) { }

  virtual void send(ACTIVE_SOLVER_MASK) override;

  virtual void complete_send() override { }

};

class sRADIATION_PATCH_GROUP : public sRP_SEND_GROUP
{
public:
  enum class eREALM {
    CONDUCTION,
    FLOW,
  };

  explicit sRADIATION_PATCH_GROUP(SCALE scale, RP_PROC dest_rp, eREALM realm) : 
    sRP_SEND_GROUP(scale, dest_rp), 
    m_recv_group(scale, dest_rp),
    m_num_time_varying_emissivity_patches(0),
    m_realm(realm)
  { }

  void pack();

  virtual void send(ACTIVE_SOLVER_MASK) override;

  // This vector is taken by value on purpose, because the 
  // original map doesn't need it anymore
  void add_patch(std::vector<sRAD_PATCH_SURFEL_PTR> surfels, bool emissivity_is_time_varying);

  void allocate_send_buffer();
  void allocate_recv_buffer();

  size_t num_patches() const { return m_patch_surfel_count.size(); }

  auto& surfels() { return m_patch_surfels; }

  const auto& patch_surfel_counts() { return m_patch_surfel_count; }

  dFLOAT * patch_bb_power() { return reinterpret_cast<dFLOAT*>(m_send_msg.buffer()); }

  dFLOAT * patch_emissive_power() { return patch_bb_power() + num_patches(); }

  const dFLOAT * patch_h_power() const { return reinterpret_cast<const dFLOAT*>(m_recv_group.buffer()); }

  VOID post_recv() { m_recv_group.post_recv(); }

  VOID complete_recv() { m_recv_group.complete_recv(); }

  sRP_RECV_GROUP* recv_group() { return &m_recv_group; }

  eREALM realm() const { return m_realm; }

  size_t num_time_varying_emissivity_patches() const noexcept { return m_num_time_varying_emissivity_patches; }

  void cancel_send();

private:

  std::vector<uINT32> m_patch_surfel_count;
  std::deque<sRAD_PATCH_SURFEL_PTR> m_patch_surfels;
  int64_t m_num_time_varying_emissivity_patches;

  sRP_RECV_GROUP m_recv_group;

  eREALM m_realm;

};

struct sRADIATION_PATCH_GROUP_ORDER
{
  bool operator () (const sRADIATION_PATCH_GROUP* a, const sRADIATION_PATCH_GROUP* b) const
  {
    if (a->realm() < b->realm()) {
      return a;
    } else if (a->realm() == b->realm()) {
      return a->dest_rp() < b->dest_rp();
    } else {
      return b;
    }
  }
};

typedef struct sRADIATION_PATCH_FSET :
  public tSP_FSET < sRADIATION_PATCH_GROUP*, sRADIATION_PATCH_GROUP_ORDER > 
{

  public:
  sRADIATION_PATCH_GROUP* find_send_group(asINT32 scale, RP_PROC dest_rp, sRADIATION_PATCH_GROUP::eREALM realm) 
  {
    sRADIATION_PATCH_GROUP signature(scale, dest_rp, realm);
    signature.m_scale = scale;
    return (this->find_group(&signature));
  }

  sRADIATION_PATCH_GROUP* create_group (asINT32 scale, RP_PROC dest_rp, sRADIATION_PATCH_GROUP::eREALM realm) 
  {

    sRADIATION_PATCH_GROUP* group = find_send_group(scale, dest_rp, realm);
    if (nullptr == group) {
        group = new sRADIATION_PATCH_GROUP(scale, dest_rp, realm);
        add_group(group);
    }

    return group;
  }

} *RADIATION_PATCH_FSET;

inline const sTIMESTEP_MANAGER& get_solver_tm(sRADIATION_PATCH_GROUP::eREALM realm)
{
  auto& timescale = get_timescale_ref();
  if (realm == sRADIATION_PATCH_GROUP::eREALM::CONDUCTION) {
    return timescale.m_conduction_pde_tm;
  } else {
    return timescale.m_lb_tm;
  }
}

extern sRADIATION_PATCH_FSET g_radiation_patches_fset;

void build_rp_recv_channel();

void do_initial_radiation_sends();
void do_initial_radiation_recvs();
void radiation_cancel_pending_sends();

// cRADIATION_WAIT_TIME communicates to the CP the fact
// that the SP had to wait for radiation data before continuting.
class cRADIATION_WAIT_TIME { 
  sRP_STATUS m_status; // reuse sRP_STATUS so we don't have to do anything important
  std::atomic<bool> m_please_send_msg_to_cp = false; 
  MPI_Request m_req = MPI_REQUEST_NULL;

public:

  static constexpr auto MIN_WAIT_TIME = std::chrono::milliseconds(1);

  // called by compute thread
  void report_wait(TIMESTEP flow_timestep, TIMESTEP cond_timestep, dFLOAT solution_time);

  // Called by comm thread
  void maybe_send_msg_to_cp();

};

extern cRADIATION_WAIT_TIME g_radiation_wait_time;

#endif
