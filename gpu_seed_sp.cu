#include "common_sp.h"
#include "sp_timers.h"
#include "shob.h"
#include "seed_sp.h"
#include "sim.h"
#include "comm_groups.h"
#include PHYSICS_H
#include "gpu_globals.hcu"
#include "ublk_seed.h"
#include "gpu_copy_to_device.h"
#include "gpu_exprlang.h"
#include "gpu_comm_groups.h"
#include "shob_groups.h"

__DEVICE__ VOID
LES_FLUID_INITIAL_CONDITIONS_VALUES::read_exprlang_device_data(int group_id,
                                                               int voxor) {
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  cassert(d->has_space_or_time_varying_var_data());
  auto mask = d->exprlang_var_mask(group_id);
  uINT32 mblk_var_start_index = d->var_start_index(group_id);
  uINT32 voxor_var_index = mblk_var_start_index + voxor;
  int n_exprlang_bits = 0;
  for (int i = 0; i < N_VARS; i++) {
    if (mask.test(i)) {
      uINT32 var_index = voxor_var_index + N_VOXELS_64 * n_exprlang_bits;
      m_vars[i] = d->var_value(var_index);
      n_exprlang_bits++;
    }
  }
}

namespace GPU {

void seed_surfels(SURFEL_GROUP_TYPE group_type,
                  const size_t range[2],
                  ACTIVE_SOLVER_MASK active_solver_mask,
                  SOLVER_INDEX_MASK seed_solver_index_mask,
                  BOOLEAN is_full_checkpoint_restore);

void explode_init_all_vr_groups_of_scale(SCALE fine_scale,
                                         SOLVER_INDEX_MASK seed_solver_index_mask);

__global__ VOID surfel_adjust_v2s_dist(UBLK_GROUP_TYPE group_type,
                                       size_t ublk_offset,
                                       const MBLK_VOXEL_MASK* d_adjust_v2s_masks) {
  size_t ublk_index = ublk_offset + blockIdx.x;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  auto& simc = get_simc_ref();
  MBLK_VOXEL_MASK v2s_dist_mask = d_adjust_v2s_masks[ublk->id()];
  if (v2s_dist_mask.test(threadIdx.x)) {
    ublk->surf_lb_data()->v2s_dist[threadIdx.x] = 0;
  }
}

VOID surfel_adjust_v2s_dist() {

  size_t n_bytes = ::g_mblk_adjust_v2s_dist_masks.size() * sizeof(MBLK_VOXEL_MASK);
  sDEV_PTR d_adjust_v2s_masks = GPU::malloc(n_bytes, "adjust_v2s_distance_masks");
  cCOPY_TO_DEVICE::trivial_copy(d_adjust_v2s_masks, n_bytes,
                                &g_mblk_adjust_v2s_dist_masks[0]);

  DO_SCALES_FINE_TO_COARSE(scale) {
    for (UBLK_GROUP_TYPE group_type : nSHOB_CATEGORIES::NEARBLK_TYPES) {
      for(int dest_sp : GPU::get_ublk_group_dest_sp(group_type, scale)) {
        const auto range = GPU::get_ublk_group_range(group_type, scale, dest_sp);
        cassert(!GPU::is_empty_group(range));
        const size_t num_ublks = range[1] - range[0];
        GPU::surfel_adjust_v2s_dist<<<num_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
          (group_type,
           range[0],
           (const MBLK_VOXEL_MASK*) d_adjust_v2s_masks.get());
        cudaStreamSynchronize(g_stream);
        checkCudaErrors( cudaPeekAtLastError() );    
      }
    } //nearblk-loop
  }

  checkCudaErrors( cudaPeekAtLastError() );

  cudaStreamSynchronize(g_stream);

  ::g_mblk_adjust_v2s_dist_masks.clear();
}

__global__ VOID reset_vr_fine_states_after_surfel_seed_kernel(UBLK_GROUP_TYPE group_type,
                                                              size_t ublk_offset) {
  size_t ublk_index = ublk_offset + blockIdx.x;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  for (int latvec = 0; latvec < N_STATES; latvec++) {
    ublk->lb_states(0)->m_states[latvec][threadIdx.x] = 0;
    ublk->lb_states(1)->m_states[latvec][threadIdx.x] = 0;
  }

  auto& simc = get_simc_ref();
  if ((simc.init_solver_mask & T_PDE_ACTIVE) && simc.is_T_S_solver_type_lb()) {
    for (int latvec = 0; latvec < N_STATES; latvec++) {
      ublk->t_data()->lb_t_data(0)->m_states_t[latvec][threadIdx.x] = 0;
      ublk->t_data()->lb_t_data(1)->m_states_t[latvec][threadIdx.x] = 0;
    }
  }
}

VOID reset_vr_fine_states_after_surfel_seed(STP_SCALE scale) {

  for (UBLK_GROUP_TYPE group_type: nSHOB_CATEGORIES::VRFINE_TYPES) {
    DO_MBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
      if (group->n_shob_ptrs() == 0) {
        continue;
      }

      const auto range = GPU::get_ublk_group_range(group_type, scale, group->m_dest_sp.nsp()); 
      size_t n_fine_ublks = range[1] - range[0];
      reset_vr_fine_states_after_surfel_seed_kernel<<<
        n_fine_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>(group_type,
                                                             range[0]);
    }
  }
}

__global__ VOID seed_ublks(UBLK_GROUP_TYPE group_type,
                           STP_SCALE scale,
                           size_t ublk_offset,
                           SOLVER_INDEX_MASK solver_mask) {
  size_t ublk_index = ublk_offset + blockIdx.x;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  auto& simc = get_simc_ref();
  ublk->seed(simc.do_smart_seed, solver_mask);
}

VOID seed_ublks(UBLK_GROUP_TYPE group_type,
                STP_SCALE scale,
                int dest_sp,
                const size_t range[2],
                SOLVER_INDEX_MASK solver_mask) {

  g_exprlang_mgr.init_space_varying_initial_conditions(group_type, scale, dest_sp);
  size_t num_ublks = range[1] - range[0];
  const size_t group_size = EXPRLANG_DEVICE_DATA::get_data()->debugGet_m_current_group_size();
  // assert(num_ublks <= group_size);
  seed_ublks<<<num_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
    (group_type, scale, range[0], solver_mask);

  cudaStreamSynchronize(g_stream);
  checkCudaErrors( cudaPeekAtLastError() );
}

__global__ static
VOID update_closest_surfel_BC_based_on_smart_seed_markers_for_nearblocks(UBLK_GROUP_TYPE group_type,
                                                                         size_t ublk_offset) {
  size_t ublk_index = ublk_offset + blockIdx.x;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  auto& simc = get_simc_ref();
  update_closest_surfel_BC_based_on_smart_seed_markers(ublk);
}

static VOID update_closest_surfel_BC_based_on_smart_seed_markers_for_nearblocks() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    for (UBLK_GROUP_TYPE group_type : nSHOB_CATEGORIES::NEARBLK_TYPES) {
      for(int dest_sp : GPU::get_ublk_group_dest_sp(group_type, scale)) {
        const auto range = GPU::get_ublk_group_range(group_type, scale, dest_sp);
        cassert(!GPU::is_empty_group(range));
        const size_t num_ublks = range[1] - range[0];
        GPU::update_closest_surfel_BC_based_on_smart_seed_markers_for_nearblocks<<<num_ublks, N_VOXELS_64,
          NO_DYN_SHMEM, g_stream>>>
          (group_type,
           range[0]);
        cudaStreamSynchronize(g_stream);
        checkCudaErrors( cudaPeekAtLastError() );
      }
    } //nearblk-loop
  }
}
} //namespace GPU


VOID cDEVICE_SHOBS_SEEDER::seed_all_dynblks() {
  
  DO_SCALES_FINE_TO_COARSE(scale) {

    if (g_timescale.m_timestep > 0) {
      TIMESTEP seed_timestep = g_timescale.m_timestep + (1 << (sim.num_scales - 1 - scale));
      m_seed_prior_solver_index_masks[scale] = compute_seed_solver_index_mask(scale, seed_timestep);
    }
    
    SOLVER_INDEX_MASK scale_solver_index_mask = m_seed_prior_solver_index_masks[scale];
    auto seedUblksInGroupCategory = [scale, scale_solver_index_mask](UBLK_GROUP_TYPE group_type) {
      for(int dest_sp : GPU::get_ublk_group_dest_sp(group_type, scale))
      {
        const auto range = GPU::get_ublk_group_range(group_type, scale, dest_sp);
        cassert(!GPU::is_empty_group(range));
        GPU::seed_ublks(group_type, scale, dest_sp, range.data(), scale_solver_index_mask);
      }
    };
    //Seed farblocks
    for (UBLK_GROUP_TYPE group_type : nSHOB_CATEGORIES::FARBLK_TYPES) {
      seedUblksInGroupCategory(group_type);
    }//farblk-loop

    //Seed nearblocks
    for (UBLK_GROUP_TYPE group_type : nSHOB_CATEGORIES::NEARBLK_TYPES) {
      seedUblksInGroupCategory(group_type);
    } //nearblk-loop
  } //scale-loop
}

VOID cDEVICE_SHOBS_SEEDER::do_seed_all_surfels(BOOLEAN is_full_checkpoint_restore) {

  if (!is_full_checkpoint_restore) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      SOLVER_INDEX_MASK scale_solver_index_mask = m_seed_prior_solver_index_masks[scale];
      //Dyn surfels
      // for (SURFEL_BASE_GROUP_TYPE group_type : shob_categories::base_surfel_types) {
      for (SURFEL_GROUP_TYPE group_type : nSHOB_CATEGORIES::DYN_SURFEL_FLOW_TYPES) {

        size_t range[2];

        for(int dest_sp : GPU::get_surfel_group_dest_sp((SURFEL_GROUP_TYPE)group_type, scale)) {
          const auto range = GPU::get_surfel_group_range((SURFEL_GROUP_TYPE)group_type, scale, dest_sp);
          cassert(!GPU::is_empty_group(range));
          if (!GPU::is_empty_group(range)) {
            PHYSICS_DESCRIPTOR pd = sim.flow_surface_physics_descs;
            for(size_t i=0; i<sim.n_flow_surface_physics_descs; i++)
            {
              if (pd->is_surface_physics_descriptor() && pd->some_parameter_time_and_space_varying) {
                GPU::g_exprlang_mgr.fill_surfel_varying_data(pd, group_type, scale, GPU::EXPRLANG_MSFL_DATA::PARAM_TYPE::TIME_AND_SPACE,dest_sp);
              }
              pd++;
            }
            GPU::seed_surfels((SURFEL_GROUP_TYPE)group_type,
                              range.data(),
                              sim.init_solver_mask,
                              scale_solver_index_mask,
                              is_full_checkpoint_restore);
          }
        }
      }//dyn-surfel-loop
    }//scale-loop
    
    if (sim.do_smart_seed) {
      copy_boundary_seeding_counts_d2h();
    }  
  }
  else
  {
    msg_internal_error("Cannot handle full checkpoint restore");
  }
}

VOID cDEVICE_SHOBS_SEEDER::do_dynblk_explode_init(SCALE fine_scale) {
  STP_SCALE coarse_scale = coarsen_scale(fine_scale);
  SOLVER_INDEX_MASK seed_solver_index_mask = m_seed_prior_solver_index_masks[coarse_scale];
  GPU::explode_init_all_vr_groups_of_scale(fine_scale, seed_solver_index_mask);
}

static VOID compute_nearblock_pressure_gradients() {
  if (sim.is_full_checkpoint_restore) {
    msg_internal_error("Full checkpoint restore is not supported on GPUs.");
  } else {
    if (sim.is_turb_model) {
      DO_SCALES_FINE_TO_COARSE(scale) {
        for (UBLK_GROUP_TYPE group_type : nSHOB_CATEGORIES::NEARBLK_TYPES) {
          for(int dest_sp : GPU::get_ublk_group_dest_sp(group_type, scale)) {
            const auto range = GPU::get_ublk_group_range(group_type, scale, dest_sp);
            cassert(!GPU::is_empty_group(range));
            asINT32 next_timestep_index = sim_prior_timestep_index(scale, g_timescale.m_timestep) ^ 1;
            GPU::compute_pressure_gradients(group_type, range.data(), next_timestep_index);
          }
        }
      }
    }
  }  
}

VOID do_preliminary_ublk_comm_on_gpu(asINT32 timestep, BOOLEAN next_index, BOOLEAN is_seed = FALSE)
{
  // printf("MPI(%d), do_preliminary_ublk_comm_on_gpu()\n", my_proc_id);
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: do_preliminary_ublk_comm_on_gpu timestep %d next_indx %d is_seed %d ", timestep, next_index, is_seed);
  DO_SCALES_COARSE_TO_FINE(scale) {
    for(auto& recv_group : g_far_mblk_recv_fset.m_sets[scale]) {
      recv_group->post_recv();
      recv_group->t_solver_type = sim.T_solver_type;
    }
    for(auto& recv_group : g_near_mblk_recv_fset.m_sets[scale]) {
      recv_group->post_recv();
      recv_group->t_solver_type = sim.T_solver_type;
    }

    ACTIVE_SOLVER_MASK active_solver_mask = sim.init_solver_mask;
    asINT32 prior_timestep_index = sim_prior_timestep_index_mask(scale, timestep);
    if(!g_timescale.m_all_solvers_have_same_timestep)
      prior_timestep_index = prior_timestep_index_mask(scale, timestep, is_seed);
    if (next_index)
      prior_timestep_index ^= active_solver_mask;

    for(auto& send_group : g_mblk_send_fset.m_sets[scale]) {
      send_group->t_solver_type = sim.T_solver_type;
      send_group->send(sim.init_solver_mask, prior_timestep_index);
    }
    for(auto& recv_group : g_far_mblk_recv_fset.m_sets[scale]) {
      recv_group->complete_recv();
      recv_group->unpack(sim.init_solver_mask, prior_timestep_index);
    }
    
    for(auto& recv_group : g_near_mblk_recv_fset.m_sets[scale]) {
      recv_group->complete_recv();
      recv_group->unpack(sim.init_solver_mask, prior_timestep_index);
    }
  }
}
static void do_prelim_surfel_comm_post_recv_on_gpu(SCALE scale)
{
  for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) 
    {
      recv_group->post_init_info_recv();
      recv_group->t_solver_type = sim.T_solver_type;
    }
}

void do_preliminary_surfel_type_comm_on_gpu() 
{
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: do_preliminary_surfel_type_comm_on_gpu");
  DO_SCALES_COARSE_TO_FINE(scale) {
    
    do_prelim_surfel_comm_post_recv_on_gpu(scale);
    
    for(auto& send_group : g_msfl_send_fset.m_sets[scale]) 
    {
      send_group->send_surfel_type();
    }
    
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) 
    {
      recv_group->complete_recv();
      recv_group->unpack_surfel_type();
    }
  }
}
void  do_preliminary_surfel_comm_on_gpu() 
{
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: do_preliminary_surfel_comm_on_gpu");
  DO_SCALES_COARSE_TO_FINE(scale) 
  {
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) 
    {
      recv_group->post_recv();
    }    
    for(auto& send_group : g_msfl_send_fset.m_sets[scale]) 
    {
      send_group->t_solver_type = sim.T_solver_type;
      send_group->send(sim.init_solver_mask);
    }
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) 
    {
      recv_group->complete_recv();
      if (recv_group->m_recv_msg.m_request == MPI_REQUEST_NULL)
          LOG_MSG("STRAND_SWITCHING").printf( "GPU do_preliminary_surfel_comm_on_gpu MPI_REQUEST_NULL 1");
      recv_group->unpack(sim.init_solver_mask, 0);
    }

    do_prelim_surfel_comm_post_recv_on_gpu(scale);    
    for(auto& send_group : g_msfl_send_fset.m_sets[scale]) 
    {
      send_group->t_solver_type = sim.T_solver_type;
      send_group->send_init_info(sim.init_solver_mask);
    }
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) 
    {
      recv_group->complete_recv();
      if (recv_group->m_recv_msg.m_request == MPI_REQUEST_NULL)
          LOG_MSG("STRAND_SWITCHING").printf( "GPU do_preliminary_surfel_comm_on_gpu MPI_REQUEST_NULL 2");
      recv_group->unpack_init_info(sim.init_solver_mask);
    }
  }
}


void  accumulate_all_v2s_and_s2s_weights_on_gpu() {} //TODO

VOID cDEVICE_SHOBS_SEEDER::process() {
  
  if (!sim.is_full_checkpoint_restore) {
    seed_all_dynblks();
  }
  
  GPU::surfel_adjust_v2s_dist();

  do_preliminary_ublk_comm_on_gpu(g_timescale.m_time, TRUE);
  //do_preliminary_surfel_type_comm_on_gpu();
  //accumulate_all_v2s_and_s2s_weights_on_gpu(); //TODO
  
  compute_nearblock_pressure_gradients();

  do_preliminary_ublk_comm_on_gpu(g_timescale.m_time, FALSE);

  DO_SCALE_FROM_COARSE_TO_FINE(scale, sim.coarsest_scale(), sim.finest_scale()) {
    do_dynblk_explode_init(scale);
  }

  do_seed_all_surfels(sim.is_full_checkpoint_restore);    

  //mlrf surfels
  DO_SCALES_FINE_TO_COARSE(scale) {
    seed_mlrf_surfels(m_seed_prior_solver_index_masks[scale], scale,
                      sim.is_full_checkpoint_restore);
  }

  do_preliminary_surfel_comm_on_gpu();
    
  if (!sim.is_full_checkpoint_restore) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      GPU::reset_vr_fine_states_after_surfel_seed(scale);
    }
  }
 
  GPU::update_closest_surfel_BC_based_on_smart_seed_markers_for_nearblocks();
}
