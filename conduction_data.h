/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Nagendra Krishnamurthy, Dassault Systemes
 * Created March 4, 2019
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_CONDUCTION_DATA_H
#define _SIMENG_CONDUCTION_DATA_H

#include "shob.h"
#include "sim.h"
#include "dgf_reader_sp.h"
#include "mme_ckpt.h"
#include "simerr.h"

#if BUILD_DOUBLE_PRECISION
#define SDFLOAT_EPSILON DFLOAT_EPSILON
#else
#define SDFLOAT_EPSILON SFLOAT_EPSILON
#endif


struct sCONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR;

VOID finalize_conduction_nearblk_passthrough_weights(UBLK dyn_ublk);

// CONDUCTION-TODO: Remove these declarations once code is moved to conduction_data.cc
VOID compute_ls_surfel_weights(UBLK ublk, asINT32 voxel, sSURFEL *surfel,
                               sdFLOAT &weight_dirichlet, sdFLOAT &weight_neumann,
                               sdFLOAT (&term_dirichlet)[4], sdFLOAT (&term_neumann)[4]);


inline namespace SIMULATOR_NAMESPACE {

// forward declarations specific to this solver data-block
template <typename UBLK_TYPE_TAG> class tUBLK_CONDUCTION_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_CONDUCTION_DATA;
template <typename UBLK_TYPE_TAG> class tUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA;

typedef tUBLK_CONDUCTION_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_CONDUCTION_DATA,         *UBLK_CONDUCTION_DATA;
typedef tUBLK_CONDUCTION_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_CONDUCTION_DATA, *UBLK_UBFLOAT_CONDUCTION_DATA;

typedef tNEARBLK_CONDUCTION_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEARBLK_CONDUCTION_DATA,            *NEARBLK_CONDUCTION_DATA;
typedef tNEARBLK_CONDUCTION_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEARBLK_UBFLOAT_CONDUCTION_DATA,    *NEARBLK_UBFLOAT_CONDUCTION_DATA;

typedef tUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA,         *UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA;
typedef tUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_CONDUCTION_IMPLICIT_SOLVER_DATA, *UBLK_UBFLOAT_CONDUCTION_IMPLICIT_SOLVER_DATA;

template <typename SFL_TYPE_TAG> struct tSURFEL_CONDUCTION_DATA;

typedef tSURFEL_CONDUCTION_DATA<SFL_SDFLOAT_TYPE_TAG> sSURFEL_CONDUCTION_DATA, *SURFEL_CONDUCTION_DATA;




typedef enum {
  MT_DATA_DENSITY,
  MT_DATA_CONDUCTIVITY_11,
  MT_DATA_CONDUCTIVITY_22,
  MT_DATA_CONDUCTIVITY_33,
  MT_DATA_SPECIFIC_HEAT,
  NUM_MT_DATA
} MT_DATA;

typedef struct sDATA_CURVES {
  std::unordered_map<std::string, std::vector<std::pair<double, double>>> mt_data;
 public:
  double interpolate(const std::string &prop_name, const double temp_value) const;
} *DATA_CURVES;

typedef struct sCONDUCTION_MATERIAL {
  std::string m_name;
  EXPRLANG_PROGRAM parameter_program;
  cBOOLEAN some_parameter_temp_varying;
  cBOOLEAN some_parameter_time_varying;
  cBOOLEAN some_parameter_time_varying_only;
  cBOOLEAN some_parameter_time_and_space_varying;
  cBOOLEAN all_parameters_sharable;
  cBOOLEAN some_constant_parameter_in_need_of_eval;

  sCOND_PHYSICS_VARIABLE density;
  sCOND_PHYSICS_VARIABLE specific_heat;
  sCOND_PHYSICS_VARIABLE conductivity[3];
  BOOLEAN is_isotropy;
  CONDUCTION_ANISOTROPY_TYPE anisotropy_type;
  std::pair<std::string,std::string> property_curve_map[NUM_MT_DATA];

  sCONDUCTION_MATERIAL(std::string &name, EXPRLANG_PROGRAM case_program) {
      m_name = name;
      parameter_program = case_program;
      some_parameter_temp_varying = FALSE;
      some_parameter_time_varying = FALSE;
      some_parameter_time_varying_only = FALSE;
      some_parameter_time_and_space_varying = FALSE;
      all_parameters_sharable = TRUE;
      some_constant_parameter_in_need_of_eval = FALSE;
      is_isotropy = TRUE;
      anisotropy_type = INVALID_ANISOTROPY;
  }
  VOID eval_temp_varying_only_parameter(dFLOAT temp_in, asINT32 scale, STP_GEOM_VARIABLE *point);
  VOID eval_space_varying_only_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h); // t = 0
  VOID eval_space_and_table_varying_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h);

} *CONDUCTION_MATERIAL;

typedef struct sCONDUCTION_PART_AXIS {
  asINT32 part_index;
  dFLOAT axis_dir[3];
} *CONDUCTION_PART_AXIS;

typedef struct sCONDUCTION_SIM_INFO {
  asINT32 num_materials;
  std::vector<CONDUCTION_MATERIAL> conduction_materials;
  asINT32 num_axes;
  asINT32 num_data_curves;
  std::unordered_map<std::string, sDATA_CURVES> material_data_curves;
  std::vector<CONDUCTION_PART_AXIS> conduction_part_axes;
  //add global variables for AIR fluid properties 
  //this is meant to be used for the conduction/convection flux in the air layer in shells
  //to be removed when support for fluid material library is added to powercase for shell layers
  //properties are initialized in SI units and later converted to lattice units
  //properties at 1 atm and 25 deg C
  sdFLOAT air_Pr = 0.707;             //Prandtl number 
  sdFLOAT air_beta = 3.38e-3;         //thermal expansion coefficient // 1/K
  sdFLOAT air_nu = 15.52e-6;          //kinematic viscosity           // m2/sec
  sdFLOAT air_cp = 1006;              //specific heat                 // J/kg/degK
  sdFLOAT air_density = 1.184;        //density                       // kg/m^3
  sdFLOAT air_conductivity = 0.02624; //thermal conductivity          // W/(m*degK)
  sdFLOAT gravity_mag = 9.81;         //magnitude of gravity vector   // m/sec2
  sdFLOAT ra_num = 0;                 //Rayleigh number, stores the static part of the Ra num
  
} *CONDUCTION_SIM_INFO;

typedef enum {
  INVALID_BC = -1,
  PRESCRIBED_TEMP_BC,
  PRESCRIBED_FLUX_BC,
  THERMAL_RESISTANCE_BC,
  INTERFACE_BC,
  LRF_BC,
  ADIABATIC_BC
} CONDUCTION_SURFEL_BC_TYPE;

INLINE sdFLOAT vr_scale_up(const asINT32 scale, sdFLOAT value) {
  cassert(scale >= 0);
  return value * scale_to_voxel_size(scale);
}

INLINE sdFLOAT vr_scale_down(const asINT32 scale, sdFLOAT value) {
  cassert(scale >= 0);
  return value / scale_to_voxel_size(scale);
}


/** @brief Returns scale factor for rescaling length 

Calculates scale factor to rescale length (and conductivity) between different scales where 
scale_diff = scale_from - scale_to. 
Is a constexpr so that it can be evaluated at compile time for surfel edge calculations at VR interfaces.
*/
constexpr sdFLOAT vr_length_scale_factor(const asINT32 scale_diff) {
  return (scale_diff >= 0) ? (1.0 / (1 << scale_diff)) : (1 << -scale_diff);
}

/** @brief Returns scale factor for rescaling inverse of length 

Calculates scale factor to rescale inverse of length (and gradT) between different scales where 
scale_diff = scale_from - scale_to.
Is a constexpr so that it can be evaluated at compile time for surfel edge calculations at VR interfaces.
*/
constexpr sdFLOAT vr_inverse_length_scale_factor(const asINT32 scale_diff) {
  return (scale_diff >= 0) ? (1 << scale_diff) : (1.0 / (1 << -scale_diff));
}

inline sdFLOAT approximate_pgram_dist_weighting(sdFLOAT dist) {
  sdFLOAT scaled_dist = dist / g_pfc_approximate_pgram_dist_max;
  sdFLOAT weight = 0.0;
  if (scaled_dist <= 1.0)
    weight = 1.2
             - 1.51 * scaled_dist
             - 1.32 * scaled_dist * scaled_dist
             + 2.58 * scaled_dist * scaled_dist * scaled_dist
             - 0.95 * scaled_dist * scaled_dist * scaled_dist * scaled_dist;

  return weight;
}

inline sdFLOAT approximate_pgram_volume_weighting(sdFLOAT pfluid) {
  sdFLOAT weight = 0.53 * pfluid
                   + 0.93 * pfluid * pfluid
                   - 0.68 * pfluid * pfluid * pfluid
                   + 0.40 * pfluid * pfluid * pfluid * pfluid;
  return weight;
}

/* scale_diff is surfel_scale - ublk_scale */
INLINE sdFLOAT get_area_scaling(const asINT32 scale_diff) {
  sdFLOAT area_scaling = 1.0;
  if (scale_diff > 0) {
    area_scaling /= (1 << (scale_diff * (sim.num_dims - 1)));
  }
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
  else if (scale_diff < 0) {
    // Will this ever happen?
    msg_internal_error("Surfel is coarser scale than UBLK");
  }
#endif
  return area_scaling;
}


/**
 @details 
 \f[
  w(r) = \begin{cases} 1-6r^2+8r^3-3r^4 & r < 1 \\
  0 & r \geqslant 1
  \end{cases}
 \f]
 */
INLINE sdFLOAT ls_dist_wt_quartic(const sdFLOAT dist)
{
  if (dist < 1.0)
    return 1.0 - 6.0 * dist * dist + 8 * dist * dist * dist - 3 * dist * dist * dist * dist;
  else
    return 0.0;
}

INLINE sdFLOAT stencil_dist_weight(const sdFLOAT dist)
{
  return ls_dist_wt_quartic(dist);
}

INLINE sdFLOAT stencil_area_weight(const sdFLOAT area)
{
  return area;
}

template <typename FLOAT_TYPE_1, typename FLOAT_TYPE_2>
VOID maybe_apply_periodicity_correction_to_vector(FLOAT_TYPE_1 vec[3], const FLOAT_TYPE_2 voxel_size) {
  ccDOTIMES (axis, 3) {
    if (vec[axis] > 2.0 * voxel_size)
      vec[axis] -= sim.simvol_size[axis];
    else if (vec[axis] < -2.0 * voxel_size)
      vec[axis] += sim.simvol_size[axis];
  }
}


#if !BUILD_FOR_SIMSIZES
template<>
inline VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, sFLOAT>(vxFLOAT vec[3], const sFLOAT voxel_size){
  ccDOTIMES (axis, 3) {
    cset(vec[axis], vec[axis] - sim.simvol_size[axis], vec[axis] > 2.0F * voxel_size);
    cset(vec[axis], vec[axis] + sim.simvol_size[axis], vec[axis] < -2.0F * voxel_size);
  }
}

template<>
inline VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, dFLOAT>(vxFLOAT vec[3], const dFLOAT voxel_size){
  ccDOTIMES (axis, 3) {
    cset(vec[axis], vec[axis] - sim.simvol_size[axis], vec[axis] > 2.0F * voxel_size);
    cset(vec[axis], vec[axis] + sim.simvol_size[axis], vec[axis] < -2.0F * voxel_size);
  }
}

#else
template<>
inline VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, sFLOAT>(vxFLOAT vec[3], const sFLOAT voxel_size){
//ccDOTIMES (axis, 3) {
//  cset(vec[axis], vec[axis] - sim.simvol_size[axis], vec[axis] > 2.0F * voxel_size);
//  cset(vec[axis], vec[axis] + sim.simvol_size[axis], vec[axis] < -2.0F * voxel_size);
//}
}

template<>
inline VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, dFLOAT>(vxFLOAT vec[3], const dFLOAT voxel_size){
//ccDOTIMES (axis, 3) {
//  cset(vec[axis], vec[axis] - sim.simvol_size[axis], vec[axis] > 2.0F * voxel_size);
//  cset(vec[axis], vec[axis] + sim.simvol_size[axis], vec[axis] < -2.0F * voxel_size);
//}
}

#endif

// We force the cen_at and cen_nbr to be dFLOATs
// since in many application cases, the centroid locations are very large and
// floats may not sufficiently hold the centroid value
template <typename FLOAT_TYPE_ARG_1, typename FLOAT_TYPE_ARG_2,typename FLOAT_TYPE_RESULT>
VOID get_vector_to_neighbor(FLOAT_TYPE_RESULT vec[3], const FLOAT_TYPE_ARG_1 vec_at[3],
                            const FLOAT_TYPE_ARG_2 vec_nbr[3], const STP_GEOM_VARIABLE voxel_size) {
  dFLOAT vec_at_double[3];
  dFLOAT vec_nbr_double[3];

  vcopy(vec_at_double, vec_at);
  vcopy(vec_nbr_double, vec_nbr);

  vsub(vec, vec_nbr_double, vec_at_double);
  // CONDUCTION-CHECK: should this be moved to solver filling instead?
  /** First correct the neighbor centroid for periodicity if needed */
  maybe_apply_periodicity_correction_to_vector(vec, voxel_size);
  vmul(vec, 1.0 / voxel_size);
}

template <>
inline VOID get_vector_to_neighbor<vxFLOAT, vxFLOAT, vxFLOAT>(vxFLOAT vec[3], const vxFLOAT vec_at[3],
                            const vxFLOAT vec_nbr[3], const STP_GEOM_VARIABLE voxel_size) {
  vsub(vec, vec_nbr, vec_at);
  // CONDUCTION-CHECK: should this be moved to solver filling instead?
  /** First correct the neighbor centroid for periodicity if needed */
  maybe_apply_periodicity_correction_to_vector(vec, voxel_size);
  vmul(vec, 1.0 / voxel_size);
}

template<size_t N_VOXELS>
struct tUBLK_CONDUCTION_SEND_FIELD
{
  STP_PHYS_VARIABLE m_solid_temp[N_VOXELS];
  STP_PHYS_VARIABLE m_grad_t[3][N_VOXELS];
  STP_PHYS_VARIABLE m_conductivity[3][3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_VOXELS];
  STP_PHYS_VARIABLE m_C_p[N_VOXELS];
};

template<size_t N_VOXELS>
struct tNEARBLK_CONDUCTION_SEND_FIELD
{
  STP_PHYS_VARIABLE m_passthrough_source[N_VOXELS];
};

//------------------------------------------------------------------------------
// sUBLK_CONDUCTION_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tUBLK_CONDUCTION_DATA
{
  EXTRACT_UBLK_TRAITS
  
#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  ALIGNED_UBFLOAT solid_temp[N_TIME_INDICES];

  ALIGNED_UBFLOAT grad_t[N_TIME_INDICES][3];

  // Conductivity value is at the finest scale. The scale up for coarse scales happens during
  // the final flux calculation
  ALIGNED_UBFLOAT conductivity[N_TIME_INDICES][3][3]; // CONDUCTION-CHECK: repetition with spec data
  ALIGNED_UBFLOAT C_p[N_TIME_INDICES]; // CONDUCTION-TODO: Combine rho and C_p into heat_capacity - Note: cannot do that if one or both are temp-varying
  ALIGNED_UBFLOAT density[N_TIME_INDICES];
  ALIGNED_UBFLOAT sum_of_fluxes;
  ALIGNED_UBFLOAT volumetric_source;

  /* Store whether the timestep at which last flux accumulated was even (0) or odd (1) */
  VOXEL_MASK flux_accumulated_at_face_mask[6];
 
  VOID set_init_flux_accumulated_at_face(asINT32 scale) {
    for (asINT32 face = 0; face < 6; face++) {
      if (!is_next_timestep_odd(scale)) 
        flux_accumulated_at_face_mask[face].set_all();
    }
  }

  INLINE bool is_next_timestep_odd (asINT32 scale) {
    return g_timescale.m_conduction_pde_tm.is_timestep_odd(scale) ^ 1;
  }

  VOID reset_flux_accumulated_at_face_masks() {
    ccDOTIMES(i, 6) {
      flux_accumulated_at_face_mask[i].reset_all();
    }
  }

  VOID set_flux_accumulated_at_face(asINT32 face, asINT32 voxel, asINT32 scale) {
    cassert(face >= 0 && face < 6);
    flux_accumulated_at_face_mask[face].set_or_reset(voxel, is_next_timestep_odd(scale));
  }

  bool is_flux_accumulated_at_face(asINT32 face, asINT32 voxel, asINT32 scale) {
    cassert(face >= 0 && face < 6);
    return flux_accumulated_at_face_mask[face].test(voxel) == is_next_timestep_odd(scale);
  }

  VOID set_flux_accumulated_at_face_init(asINT32 face, asINT32 voxel) {
    cassert(face >= 0 && face < 6);
    flux_accumulated_at_face_mask[face].set(voxel);
  }

  bool is_flux_accumulated_at_face_init(asINT32 face, asINT32 voxel) {
    cassert(face >= 0 && face < 6);
    return flux_accumulated_at_face_mask[face].test(voxel);
  }

  static size_t SIZE() {
    return sizeof(tUBLK_CONDUCTION_DATA);
  }

  sdFLOAT get_conductivity(asINT32 index, asINT32 axis1, asINT32 axis2, asINT32 voxel) {
    return this->conductivity[index][axis1][axis2][voxel];
  }

  sdFLOAT& set_conductivity(asINT32 index, asINT32 axis1, asINT32 axis2, asINT32 voxel) {
    return this->conductivity[index][axis1][axis2][voxel];
  }

  vxFLOAT_BASE& set_conductivity_voxor(asINT32 index, asINT32 axis1, asINT32 axis2, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,ubFLOAT>::value, "Function must be called on vector type" );
    return this->conductivity[index][axis1][axis2][voxor];
  }

  sdFLOAT get_grad_t(asINT32 index, asINT32 comp, asINT32 voxel) {
    return this->grad_t[index][comp][voxel];
  }

  sdFLOAT& set_grad_t(asINT32 index, asINT32 comp, asINT32 voxel) {
    return this->grad_t[index][comp][voxel];
  }

  vxFLOAT_BASE& set_grad_t_voxor(asINT32 index, asINT32 comp, asINT32 voxor) {
    static_assert( std::is_same<UBFLOAT_DATA_TYPE,ubFLOAT>::value, "Function must be called on vector type" );
    return this->grad_t[index][comp][voxor];
  }

  VOID copy_voxel_temperatures()
  {
    ccDOTIMES(voxel, N_VOXELS) {
      this->solid_temp[0][voxel] = this->solid_temp[1][voxel];
    }
  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_CONDUCTION_DATA *dest_conduction_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    UBLK_CONDUCTION_DATA d = dest_conduction_data;
    ccDOTIMES (timestep, N_TIME_INDICES) {
      d->solid_temp[timestep][dest_voxel] = solid_temp[timestep][source_voxel];
      ccDOTIMES (i, 3) {
        d->set_grad_t(timestep, i, dest_voxel) = get_grad_t(timestep, i, source_voxel);
        ccDOTIMES (j, 3) {
          d->set_conductivity(timestep, i, j, dest_voxel) = get_conductivity(timestep, i, j, source_voxel);
        }
      }
    }
  }

  VOID copy_voxel_mme_data(tUBLK_CONDUCTION_DATA *dest_conduction_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    UBLK_CONDUCTION_DATA d = dest_conduction_data;
    ccDOTIMES (timestep, N_TIME_INDICES) {
      d->solid_temp[timestep][dest_voxel] = solid_temp[timestep][source_voxel];
    }
  }

  VOID explode_voxel(tUBLK_CONDUCTION_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_conduction_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    asINT32 prior_timestep = (solver_ts_index_mask & CONDUCTION_PDE_ACTIVE) >> CONDUCTION_SOLVER;
    auto* f = fine_ublk_conduction_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
        f->density[timestep][voxor] = density[prior_timestep][voxel_to_explode];
        f->C_p[timestep][voxor] = C_p[prior_timestep][voxel_to_explode];
        //Temperature is exploded elsewhere since it requires interpolation
        ccDOTIMES (i, 3) {
          vxFLOAT grad_t = 0.5f * get_grad_t(prior_timestep, i, voxel_to_explode);
          f->set_grad_t_voxor(timestep, i, voxor) = grad_t;
          ccDOTIMES (j, 3) {
            vxFLOAT conductivity = get_conductivity(prior_timestep, i, j, voxel_to_explode);
            f->set_conductivity_voxor(timestep, i, j, voxor) = conductivity;
          }
        }
      }
    }
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &conduction_send_size) {
    send_size            += ( sizeof(tUBLK_CONDUCTION_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
    conduction_send_size += ( sizeof(tUBLK_CONDUCTION_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 solver_ts_index_mask, BOOLEAN is_vr_fine = FALSE) {
    asINT32 timestep_index = (solver_ts_index_mask & CONDUCTION_PDE_ACTIVE) >> CONDUCTION_SOLVER;
#ifdef SS_CYCLING_TEMPORARY_FIX_FOR_UBLK_COMM
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Filling Index, orig: %d, new: %d", timestep_index,
        last_updated_index);
    timestep_index = last_updated_index;
#endif
    auto field = reinterpret_cast<tUBLK_CONDUCTION_SEND_FIELD<N_VOXELS>*>(send_data_info.send_buffer);
    memcpy(field->m_solid_temp,   &(solid_temp[timestep_index]),   sizeof(field->m_solid_temp));
    memcpy(field->m_grad_t,       &(grad_t[timestep_index]),       sizeof(field->m_grad_t));
    memcpy(field->m_conductivity, &(conductivity[timestep_index]), sizeof(field->m_conductivity));
    memcpy(field->m_density,      &(density[timestep_index]),      sizeof(field->m_density));
    memcpy(field->m_C_p,      &(C_p[timestep_index]),      sizeof(field->m_C_p));
#ifdef SS_CYCLING_TEMPORARY_FIX_FOR_UBLK_COMM
    field->m_last_updated_index = last_updated_index;
#endif
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 solver_ts_index_mask, BOOLEAN is_vr_fine = FALSE) {
    asINT32 timestep_index = (solver_ts_index_mask & CONDUCTION_PDE_ACTIVE) >> CONDUCTION_SOLVER;
    auto field = reinterpret_cast<tUBLK_CONDUCTION_SEND_FIELD<N_VOXELS>*>(recv_data_info.recv_buffer);
#ifdef SS_CYCLING_TEMPORARY_FIX_FOR_UBLK_COMM
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Receiving Index, orig: %d, new: %d", timestep_index,
        field->m_last_updated_index);
    timestep_index = field->m_last_updated_index;
#endif
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Temp: %.17g", *(field->m_solid_temp));
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Grad_t: %.17g %.17g %.17g", *(field->m_grad_t[0]),
        *(field->m_grad_t[1]), *(field->m_grad_t[2]));
#endif
    memcpy(&(solid_temp[timestep_index]),   field->m_solid_temp,   sizeof(field->m_solid_temp));
    memcpy(&(grad_t[timestep_index]),       field->m_grad_t,       sizeof(field->m_grad_t));
    memcpy(&(conductivity[timestep_index]), field->m_conductivity, sizeof(field->m_conductivity));
    memcpy(&(density[timestep_index]),      field->m_density,      sizeof(field->m_density));
    memcpy(&(C_p[timestep_index]),      field->m_C_p,      sizeof(field->m_C_p));
#ifdef SS_CYCLING_TEMPORARY_FIX_FOR_UBLK_COMM
    last_updated_index = field->m_last_updated_index;
#endif
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  // CONDUCTION-CHECK: Is anything needed to be sent/received in mme buffers?
  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, asINT32 timestep_index)   { }
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, asINT32 timestep_index) { }
  uINT64 ckpt_len() { return sizeof(*this); }
  VOID read_ckpt()  {
    read_lgi(*this);
  }

  VOID write_ckpt() { write_ckpt_lgi(*this);}
  
  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas)
  {
    auto& timescale = get_timescale_ref();
    asINT32 prior_index = timescale.m_conduction_pde_tm.prior_timestep_index(scale);
    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = solid_temp[prior_index][voxel];
    }
    meas++;
  }
#undef ALIGNED_UBFLOAT    
};

//------------------------------------------------------------------------------
// sNEARBLK_CONDUCTION_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG> 
struct ALIGN_VECTOR tNEARBLK_CONDUCTION_DATA
{
  // CONDUCTION-TODO: It looks like conduction nearblks contain sNEARBLK_LB_DATA as well. This should not be necessary
  // and likely can be removed (maybe with some changes) 

  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  ALIGNED_UBFLOAT heat_wall;
  ALIGNED_UBFLOAT face_areas[6];
  ALIGNED_UBFLOAT ls_grad_coeff_matrix[4][27];
  ALIGNED_UBFLOAT ls_grad_surfel_contribution[4];
  ALIGNED_UBFLOAT ls_mat_a_inv[4][4];
  ALIGNED_UBFLOAT passthrough_source[N_TIME_INDICES];
  ALIGNED_UBFLOAT passthrough_summ_inv;

  static VOID add_send_size(asINT32 &send_size, asINT32 &conduction_send_size) {
    send_size            += (sizeof(tNEARBLK_CONDUCTION_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
    conduction_send_size += (sizeof(tNEARBLK_CONDUCTION_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & CONDUCTION_PDE_ACTIVE) >> CONDUCTION_SOLVER;
    auto field = reinterpret_cast<tNEARBLK_CONDUCTION_SEND_FIELD<N_VOXELS>*>(send_data_info.send_buffer);
    memcpy(field->m_passthrough_source,   &(passthrough_source[timestep_index]),   sizeof(field->m_passthrough_source));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & CONDUCTION_PDE_ACTIVE) >> CONDUCTION_SOLVER;
    auto field = reinterpret_cast<tNEARBLK_CONDUCTION_SEND_FIELD<N_VOXELS>*>(recv_data_info.recv_buffer);
    memcpy(passthrough_source[timestep_index],   field->m_passthrough_source,   sizeof(field->m_passthrough_source));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
  VOID write_ckpt()                      { write_ckpt_lgi(*this); }
  VOID read_ckpt()                       { read_lgi(*this);       }
  uINT64 ckpt_len()                     { return sizeof(*this); }

#undef ALIGNED_UBFLOAT    
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tVR_FINE_CONDUCTION_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  // First index is the axis along which fine neighbor exists
  ALIGNED_UBFLOAT fine_solid_temp_nm1[3];
  ALIGNED_UBFLOAT fine_grad_t_nm1[3][3];
  ALIGNED_UBFLOAT fine_conductivity_nm1[3][3][3];
//  uINT64 ckpt_len() { return sizeof(*this); }
//  VOID read_ckpt()  { read_lgi(*this);      }
//  VOID write_ckpt() { write_ckpt_lgi(*this);}
#undef ALIGNED_UBFLOAT    
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  ALIGNED_UBFLOAT rhs_bc;
  ALIGNED_UBFLOAT implicit_matrix_A_diagonal;
  ALIGNED_UBFLOAT implicit_rho_cp_vec;
  sINT32 implicit_solid_state_index[N_VOXELS]; 
  std::vector<sINT32> nbr_state_index_vector[N_VOXELS];
  std::vector<dFLOAT> nbr_offdiagonal_term_vector[N_VOXELS];
#undef ALIGNED_UBFLOAT    
};

typedef struct sSURFEL_CONDUCTION_SEND_FIELD
{
  STP_PHYS_VARIABLE m_conductivity_nn_sample_prime;
  STP_PHYS_VARIABLE m_temp_sample_pair[N_TIME_INDICES];
  STP_PHYS_VARIABLE m_heat_flux_prime; 
  STP_PHYS_VARIABLE m_outgoing_heat_flux[N_TIME_INDICES];
  STP_PHYS_VARIABLE m_wall_temp;
} *SURFEL_CONDUCTION_SEND_FIELD;

//------------------------------------------------------------------------------
// sSURFEL_CONDUCTION_DATA
//------------------------------------------------------------------------------
template <typename SFL_TYPE_TAG>
struct tSURFEL_CONDUCTION_DATA   // JEH Why not derived from base class with m_physics_desc ?
{
  EXTRACT_SURFEL_TRAITS
  // CONDUCTION-TODO: The values of conductivity stored at finest scale whereas grad_t stored at local scale
  // is very confusing. We need to store all values at local scale.
  //CONDUCTION-TODO: the surfel sampled conductivities should live on the dynamics block so that we can define normal conductivity
  //      as per isotropic/anisotropic type
  //CONDUCTION-CHECK: Do we really need the prime values? In what situations are they needed and is it still valid to
  //have them for conduction solver? Answer: For subcycling
  // Following fields are sampled, and will need to be re-initialized to 0 after sampling is complete
  // CONDUCTION-TODO: A general way to re-init following fields together rather than hard-coding in
  // surfel_process_control
  // CONDUCTION-TODO: conductivity should be called conductivity_sample since it is really is just a sample
  tSFL_VAR<STP_PHYS_VARIABLE, N> conductivity_nn_sample; // (stores conductivity_sample[3][3] DOT normal[3]) DOT normal[3]
  tSFL_VAR<STP_PHYS_VARIABLE, N> rho_C_p_sample;
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample;
  tSFL_VAR<STP_PHYS_VARIABLE, N> grad_t_dot_n;
  tSFL_VAR<STP_PHYS_VARIABLE, N> q_anisotropic;
#if CONDUCTION_ENABLE_PTHRU_TO_SURFELS
  tSFL_VAR<STP_PHYS_VARIABLE, N> q_passthrough;
#endif

  tSFL_VAR<STP_PHYS_VARIABLE, N> conductivity_nn_sample_prime;
  tSFL_VAR<STP_PHYS_VARIABLE, N> rho_C_p_sample_prime;
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample_prime;
  tSFL_VAR<STP_PHYS_VARIABLE, N> grad_t_dot_n_prime;

  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_sample_pair[N_TIME_INDICES];

  // NOTE: Unlike turb_heat_flux, heat_flux* are stored per unit area. This avoids dividing
  // by surfel area during fixed contribution calculation to LS
  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_flux; // slot to accumulate heat flux (if this surfel is part of a material interface)
  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_flux_prime; // final heat flux to be distributed / used for LS grad

  tSFL_VAR<STP_PHYS_VARIABLE, N> heat_bc; //W/m^2
  // This is the temperature BC for neighboring conduction voxels (and perhaps the outside of a shell that wraps conduction
  // voxels). Does not apply to a shell from an open shell because that could have separate BCs on front and back.
  tSFL_VAR<STP_PHYS_VARIABLE, N> temp_bc; 
#ifndef CONDUCTION_USE_SIMPLE_FD_FOR_QUASI_1D_SOURCE
  tSFL_VAR<sdFLOAT, N> conductivity_dot_grad_t_dot_n;
  tSFL_VAR<sdFLOAT, N> conductivity_dot_grad_t_dot_n_prime;
#error Using (conductivity . grad_t) . n for source calculation in quasi-1D is not fully implemented
#endif

  tSFL_VAR<sdFLOAT, N> wall_temp;
  tSFL_VAR<STP_PHYS_VARIABLE, N> outgoing_heat_flux[N_TIME_INDICES];
  tSFL_VAR<STP_PHYS_VARIABLE, N> s2s_recv_factor;
  tSFL_VAR<sdFLOAT, N> s2s_heat_flux_source;

  tSFL_VAR<sdFLOAT, N> sampling_weight_without_interface_s2s_inv; //maybe defined differently than standard mme_weight
  tSFL_VAR<sdFLOAT, N> sampling_weight_with_interface_s2s_inv; //maybe defined differently than standard mme_weight
  tSFL_VAR<sdFLOAT, N> sampling_distance_with_interface_s2s;

  CONDUCTION_SURFEL_BC_TYPE bc_type = INVALID_BC;

  __HOST__DEVICE__ VOID update_underrelaxed_heat_flux(const BOOLEAN is_seeding_call, const sdFLOAT heat_flux_value);

#ifdef CONDUCTION_FULL_COPY_EVEN_INFO_INTO_ODD_SURFELS
  VOID full_copy_even_to_odd(tSURFEL_CONDUCTION_DATA *even_surfel_conduction) {
    memcpy(this, even_surfel_conduction, sizeof(*this));
  }
#endif

  VOID set_bc_type_from_pd(sSURFEL* surfel, PHYSICS_DESCRIPTOR pd, STP_PHYSTYPE_TYPE phystype);

  BOOLEAN is_prescribed_temp() { return bc_type == PRESCRIBED_TEMP_BC; }

  BOOLEAN is_prescribed_flux() { return (bc_type == PRESCRIBED_FLUX_BC) || (bc_type == ADIABATIC_BC); }

  BOOLEAN is_thermal_resistance() { return bc_type == THERMAL_RESISTANCE_BC; }

  // CONDUCTION-TODO: Remove pre_advect_init and pre_advect_init_copy.. since they are not used anymore
  VOID pre_advect_init() {
    temp_sample = 0.0;
    conductivity_nn_sample = 0.0;
    q_anisotropic = 0.0;
#if CONDUCTION_ENABLE_PTHRU_TO_SURFELS
    q_passthrough = 0.0;
#endif
  }
  
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_CONDUCTION_DATA *even_surfel_conduction);

  VOID seed_copy_even_to_odd(tSURFEL_CONDUCTION_DATA *even_surfel_conduction);
  
  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_CONDUCTION_DATA *mirror_conduction_data);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_CONDUCTION_DATA *source_conduction_data);

  VOID clear_sampled_quantities() { 
    temp_sample = 0.0;
    grad_t_dot_n = 0.0;
    conductivity_nn_sample = 0.0;
    q_anisotropic = 0.0;
#if CONDUCTION_ENABLE_PTHRU_TO_SURFELS
    q_passthrough = 0.0;
#endif
    s2s_heat_flux_source = 0.0;
    rho_C_p_sample = 0.0;
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &conduction_send_size) {
    send_size            += (sizeof(sSURFEL_CONDUCTION_SEND_FIELD) / sizeof(sdFLOAT));
    conduction_send_size += (sizeof(sSURFEL_CONDUCTION_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    SURFEL_CONDUCTION_SEND_FIELD field = reinterpret_cast<SURFEL_CONDUCTION_SEND_FIELD>(send_data_info.send_buffer);
    field->m_conductivity_nn_sample_prime = conductivity_nn_sample_prime;
    memcpy(field->m_temp_sample_pair, temp_sample_pair, sizeof(field->m_temp_sample_pair));
    field->m_heat_flux_prime   = heat_flux_prime;
    memcpy(field->m_outgoing_heat_flux, outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
    field->m_wall_temp = wall_temp;
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    SURFEL_CONDUCTION_SEND_FIELD field = reinterpret_cast<SURFEL_CONDUCTION_SEND_FIELD>(recv_data_info.recv_buffer);
    conductivity_nn_sample_prime = field->m_conductivity_nn_sample_prime;
    memcpy(temp_sample_pair, field->m_temp_sample_pair, sizeof(field->m_temp_sample_pair));
    heat_flux_prime   = field->m_heat_flux_prime;
    memcpy(outgoing_heat_flux, field->m_outgoing_heat_flux, sizeof(field->m_outgoing_heat_flux));
    wall_temp = field->m_wall_temp;
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  STP_PHYS_VARIABLE normal_conductivity () const {
    return conductivity_nn_sample_prime;
  }

  uINT64 ckpt_len() { 
    uINT64 len = sizeof(conductivity_nn_sample)
               + sizeof(rho_C_p_sample)
               + sizeof(temp_sample)
               + sizeof(grad_t_dot_n)
               + sizeof(q_anisotropic)
#if CONDUCTION_ENABLE_PTHRU_TO_SURFELS
               + sizeof(q_passthrough)
#endif
               + sizeof(conductivity_nn_sample_prime)
               + sizeof(rho_C_p_sample_prime)
               + sizeof(temp_sample_prime)
               + sizeof(grad_t_dot_n_prime)
               + sizeof(temp_sample_pair)
               + sizeof(heat_flux)
               + sizeof(heat_flux_prime)
               + sizeof(heat_bc)
               + sizeof(temp_bc)
#ifndef CONDUCTION_USE_SIMPLE_FD_FOR_QUASI_1D_SOURCE
               + sizeof(conductivity_dot_grad_t_dot_n)
               + sizeof(conductivity_dot_grad_t_dot_n_prime)
#endif
               + sizeof(wall_temp)
               + sizeof(outgoing_heat_flux)
               + sizeof(s2s_recv_factor)
               + sizeof(s2s_heat_flux_source);
    return len;
  }   
  VOID read_ckpt()  { read_lgi(&conductivity_nn_sample, ckpt_len());      }
  VOID write_ckpt() { write_ckpt_lgi(&conductivity_nn_sample, ckpt_len());} 
};

// CONDUCTION-CHECK: Do we need a SURFEL_V2S_CONDUCTION_DATA?

typedef struct sSURFEL_SHELL_LAYER_SEND_FIELD {
#ifdef CONDUCTION_SHELL_COMM_BOTH_TIME_INDICES
  STP_PHYS_VARIABLE m_temp[N_TIME_INDICES];
  STP_PHYS_VARIABLE m_grad_t[N_TIME_INDICES][2];
  STP_PHYS_VARIABLE m_conductivity[N_TIME_INDICES][4];
  STP_PHYS_VARIABLE m_heat_capacity[N_TIME_INDICES];
  STP_PHYS_VARIABLE m_passthrough_source[N_TIME_INDICES];
#else
  STP_PHYS_VARIABLE m_temp;
  STP_PHYS_VARIABLE m_grad_t[2];
  STP_PHYS_VARIABLE m_conductivity[4];
  STP_PHYS_VARIABLE m_heat_capacity;
  STP_PHYS_VARIABLE m_passthrough_source;
#endif
} *SURFEL_SHELL_LAYER_SEND_FIELD;

typedef struct sWSURFEL_SHELL_LAYER_SEND_FIELD {
  sdFLOAT m_conductivity[N_TIME_INDICES];
  sdFLOAT m_temp[N_TIME_INDICES];
  sdFLOAT m_thickness;
  sdFLOAT m_heat_capacity[N_TIME_INDICES];
  sdFLOAT m_source_per_area;
} *WSURFEL_SHELL_LAYER_SEND_FIELD;

typedef struct sWSURFEL_LAYER_RAD_SEND_FIELD {
  sdFLOAT m_front_emissivity;
  sdFLOAT m_back_emissivity;
  sdFLOAT m_contact_area;
} *WSURFEL_LAYER_RAD_SEND_FIELD;

typedef struct sWSURFEL_SAMPLED_BASE_SEND_FIELD {
  sdFLOAT m_conductivity;
  sdFLOAT m_temp;
  sdFLOAT m_thickness;
  sdFLOAT m_heat_capacity;
  sdFLOAT m_source_per_area;
  dFLOAT  m_heat_flux_prime; //needs to be dFLOAT so both realms underrelax interface heat flux equally
  sdFLOAT  m_accumulated_heat_flux;
  // sdFLOAT m_wall_temp_underrelaxed;
} *WSURFEL_SAMPLED_BASE_SEND_FIELD;

typedef struct sWSURFEL_FLOW_SEND_FIELD {
  sdFLOAT m_meas_corrected_mass;
  sdFLOAT m_meas_force[3];
  sdFLOAT m_mom[3];
  sdFLOAT m_eff_htc;
  sdFLOAT m_meas_temp_wall_real;
  sdFLOAT m_meas_htc;
  sdFLOAT m_mom_density;
  sdFLOAT m_mass;
  sdFLOAT m_h_t_edge;
  sdFLOAT m_q_t;
  //CONDUCTION-TODO: Needed for staggered solvers (maybe in the future we can optimize this)
  sdFLOAT m_avg_temp_near_wall;
  sdFLOAT m_avg_htc;
  // sdFLOAT m_temp_near_wall_accum;
  // sdFLOAT m_htc_accum;
} *WSURFEL_FLOW_SEND_FIELD;

typedef struct sWSURFEL_CONDUCTION_SEND_FIELD {
  //CONDUCTION-TODO: Needed for staggered solvers (maybe in the future we can optimize this)
  alignas(8) sdFLOAT m_avg_temp;
  // sdFLOAT m_temp_accum;
} *WSURFEL_CONDUCTION_SEND_FIELD;

typedef struct sWSURFEL_OPEN_SHELL_SEND_FIELD {
  sdFLOAT  m_accumulated_heat_flux_front;
  sdFLOAT  m_accumulated_heat_flux_back;
  sdFLOAT m_avg_temp_front;
  sdFLOAT m_avg_temp_back;
  sdFLOAT m_quasi_1D_bc_tdma;
  sdFLOAT m_quasi_1D_bc_rhs;
} *WSURFEL_OPEN_SHELL_SEND_FIELD;

typedef sINT32 STATE_VECTOR_INDEX; 
extern std::unordered_map<SHOB_ID, STATE_VECTOR_INDEX> g_implicit_matrix_mapping; // map surfel id to index of temperature for first layer of surfel

template<typename SFL_TYPE_TAG>
struct tSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA
{
  EXTRACT_SURFEL_TRAITS
  std::map<STATE_VECTOR_INDEX, sdFLOAT> stencil_coeffs[2]; // map has smaller memory than unordered_map. this map is expected to be small so the search time isn't important.
  std::vector<dFLOAT> coeffs;
  std::vector<STATE_VECTOR_INDEX> global_indices;
  std::vector<STATE_VECTOR_INDEX> column_indices;
  std::vector<sdFLOAT> column_vals;
  std::map<int, sdFLOAT> row_idx_val;
  sINT32 implicit_shell_state_index;
  bool cell_is_bad = false;
  bool cell_has_anti_diffusion = false;
  bool cell_is_tiny = false;
  bool cell_has_extreme_area_ratio = false;
  bool system_row_needs_to_be_updated = false;
  bool source_needs_to_be_updated = false;
  bool bc_needs_to_be_updated = false;
  sdFLOAT source_vals = 0.0;
  sdFLOAT bc_vals = 0.0;
  bool cell_has_extreme_nonorthogonality = false;
  bool use_secondary_gradients = true;
};

struct sIMPLICIT_SOLVER_STENCIL_ERROR
{
  SP_EEP_TYPES implicit_solver_error_type;
  asINT32 root_surfel_scale;
  SURFEL_ID root_surfel_id;
  SURFEL_ID nbr_surfel_id;
  sdFLOAT root_surfel_area;
  sdFLOAT nbr_surfel_area;
  sdFLOAT root_centroid_x;
  sdFLOAT root_centroid_y;
  sdFLOAT root_centroid_z;
  sdFLOAT nbr_centroid_x;
  sdFLOAT nbr_centroid_y;
  sdFLOAT nbr_centroid_z;
  sdFLOAT error_data;
  std::string str;
};

extern std::vector<sIMPLICIT_SOLVER_STENCIL_ERROR> g_implicit_shell_solver_simerrs;

typedef tSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA<SFL_SDFLOAT_TYPE_TAG> sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA, *SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA;

typedef struct sLAYER_RAD_DATA {
public:
  STP_PHYS_VARIABLE front_emissivity;
  STP_PHYS_VARIABLE back_emissivity;
  STP_PHYS_VARIABLE contact_area;

  bool needs_radiation_model() {
    return (front_emissivity > -1);
  }

  static asINT32 rad_send_size() {
    return sizeof(sWSURFEL_LAYER_RAD_SEND_FIELD) / sizeof(sdFLOAT);
  }

  VOID fill_wsurfel_rad_send_buffer(sdFLOAT* &send_buffer) {
    WSURFEL_LAYER_RAD_SEND_FIELD field = reinterpret_cast<WSURFEL_LAYER_RAD_SEND_FIELD>(send_buffer);
    memcpy(&field->m_front_emissivity, &front_emissivity, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_front_emissivity));
    memcpy(&field->m_back_emissivity, &back_emissivity, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_back_emissivity));
    memcpy(&field->m_contact_area, &contact_area, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_contact_area));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_wsurfel_rad_recv_buffer(sdFLOAT* &recv_buffer) {
    WSURFEL_LAYER_RAD_SEND_FIELD field = reinterpret_cast<WSURFEL_LAYER_RAD_SEND_FIELD>(recv_buffer);
    memcpy(&front_emissivity, &field->m_front_emissivity, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_front_emissivity));
    memcpy(&back_emissivity, &field->m_back_emissivity, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_back_emissivity));
    memcpy(&contact_area, &field->m_contact_area, struct_field_size(WSURFEL_LAYER_RAD_SEND_FIELD, m_contact_area));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

} *LAYER_RAD_DATA;

typedef struct sSHELL_LAYER_DATA {
// making conductivity private to ensure it is not directly accessed
// (since it is now a 2x2 tensor and normal value stored in a 4 element array)
private:
  // Conductivity is a 2D symmetric tensor in tangential plane + a normal conductivity
  // it is being stored as {xx, xy, yy, normal} where xx,xy and yy are 2D tensor terms
  STP_PHYS_VARIABLE conductivity[N_TIME_INDICES][4];
  STP_PHYS_VARIABLE temperature[N_TIME_INDICES];
public:
  STP_GEOM_VARIABLE source_per_area; // q_dot * layer_thickness
  STP_GEOM_VARIABLE thickness;
  
  STP_PHYS_VARIABLE grad_t[N_TIME_INDICES][2];
  dFLOAT dT_prime;
  dFLOAT accumulated_dT;
  STP_PHYS_VARIABLE accumulated_dE;
  STP_PHYS_VARIABLE heat_capacity[N_TIME_INDICES]; // rho * C_p * (surfel_area * layer_thickness)
  STP_PHYS_VARIABLE passthrough_source[N_TIME_INDICES];
  asINT32 layer_rad_data_index;
  VOID set_conductivity(sdFLOAT kappa[4]) {
    ccDOTIMES (k, N_TIME_INDICES) {
      conductivity[k][0] = kappa[0];
      conductivity[k][1] = kappa[1];
      conductivity[k][2] = kappa[2];
      conductivity[k][3] = kappa[3];
    }
  }
  // Implicit solver data
  sSURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA layer_matrix_data;

  LAYER_RAD_DATA layer_rad_data();

  sdFLOAT normal_conductivity(const asINT32 index) {
    // return 4th element corresponding to normal term
    return conductivity[index][3];
  }

  sdFLOAT * normal_conductivity_ptr(const asINT32 index) {
    // return pointer to 4th element corresponding to normal term; for use ib fill_send_beffer and exand_send_buffer
    return &conductivity[index][3];
  }

  bool is_insulation_layer() {
    return (layer_rad_data_index > -1);
  }

  bool is_air_layer() {
    if ( is_insulation_layer() && (normal_conductivity(0) > SFLOAT_EPSILON) )
      return true;
    else 
      return false;
  }

  const sdFLOAT* conductivity_ptr(const asINT32 index) {
    // return pointer to 4th element corresponding to normal term; for use ib fill_send_beffer and exand_send_buffer
    return conductivity[index];
  }

  // returns transverse conductivity normal to the edge
  template <typename FLOAT>
  FLOAT edge_normal_conductivity(const asINT32 index, const FLOAT edge_normal[2]) {
    return conductivity[index][0]*edge_normal[0]*edge_normal[0]
         + 2.0*conductivity[index][1]*edge_normal[0]*edge_normal[1]
         + conductivity[index][2]*edge_normal[1]*edge_normal[1];
  }

  const sdFLOAT* local_transverse_conductivity(const asINT32 index) {
    return conductivity[index];
  }

  VOID rotated_transverse_conductivity(sdFLOAT* kappa, const sdFLOAT angle, const asINT32 index) {
    sdFLOAT cos_angle = cos(angle);
    sdFLOAT sin_angle = sin(angle);
    sdFLOAT sin_two_angle = sin(2.0*angle);

    kappa[0] = conductivity[index][0]*cos_angle*cos_angle - conductivity[index][1]*sin_two_angle + conductivity[index][2]*sin_angle*sin_angle;
    kappa[1] = conductivity[index][1]*cos(2.0*angle) + sin_two_angle*(conductivity[index][0] - conductivity[index][2])/2.0;
    kappa[2] = conductivity[index][0]*sin_angle*sin_angle + conductivity[index][1]*sin_two_angle + conductivity[index][2]*cos_angle*cos_angle;
  }

  VOID accumulate_dE(sdFLOAT dE) {
    accumulated_dE += dE;
  }

  sdFLOAT temp(const asINT32 index) {
    return temperature[index] + accumulated_dT;
  }

  sdFLOAT get_temp(const asINT32 index) {
    return temperature[index];
  }

  VOID set_temp(const asINT32 index, const sdFLOAT new_temp) {
    temperature[index] = new_temp;
  }

  VOID compute_applicable_and_passthrough_energy(const sdFLOAT T_cap_min,
      const sdFLOAT T_cap_max, const sdFLOAT area, const asINT32 prior_index)
  {
    const sdFLOAT energy_per_temp = this->heat_capacity[prior_index] * area;
    sdFLOAT allowed_dE_min = (T_cap_min - this->temp(prior_index)) * energy_per_temp;
    sdFLOAT allowed_dE_max = (T_cap_max - this->temp(prior_index)) * energy_per_temp;

    sdFLOAT applicable_dE = this->accumulated_dE;
    applicable_dE = MIN(applicable_dE, allowed_dE_max);
    applicable_dE = MAX(applicable_dE, allowed_dE_min);

    // CONDUCTION-TODO: To be in simerr
    this->passthrough_source[prior_index^1] = this->accumulated_dE - applicable_dE;
    this->accumulated_dE = applicable_dE;
  }

  /**
   @brief Calculates scaled interfacial area of shell layer

   @details Calculates interfacial area of shell layer's edge at surfel's scale which
            is given by:

            \f$A_{e}=\frac{L_{e}}{2\lambda_{s}} \left(\tau_{s} + \tau_{n}\frac{\lambda_{n}}{\lambda_{s}}\right)\f$

            Where \f$L_{e}\f$ is the edge length at the finest scale, \f$\tau_{s}\f$ and \f$\tau_{n}\f$ are the layer
            thicknesses at the surfel's and neighbor surfel's scales, respectively, and \f$\lambda_{s}\f$ and]
            \f$\lambda_{n}\f$ are the surfel's and neighbor surfel's scaling factors, respectively.
  */
  template<asINT32 SCALE_DIFF>
  sdFLOAT scaled_interfacial_area(const sdFLOAT edge_length, sSHELL_LAYER_DATA* nbr_nth_layer,
      const asINT32 scale_at) {
    if constexpr(SCALE_DIFF == 0){
      return edge_length * 0.5 * (this->thickness + nbr_nth_layer->thickness);
    } else if constexpr(SCALE_DIFF == 1) {
      constexpr sdFLOAT vr_thickness_scaling = 1.0 / (1 << SCALE_DIFF);
      return edge_length * 0.5 * (this->thickness + nbr_nth_layer->thickness*vr_thickness_scaling);
    } else if constexpr(SCALE_DIFF == -1) {
      constexpr sdFLOAT vr_thickness_scaling = 1 << -SCALE_DIFF;
      return edge_length * 0.5 * (this->thickness + nbr_nth_layer->thickness*vr_thickness_scaling);
    } else {
      msg_internal_error("Difference in scales at shell edge is %d. Value must be -1, 0 or 1", SCALE_DIFF);
      return 0.;
    }
  }

  sdFLOAT scaled_interfacial_area(const sdFLOAT edge_length_finest_scale, sSHELL_LAYER_DATA* nbr_nth_layer, const asINT32 root_scale_factor, const asINT32 nbr_scale_factor) {
    sdFLOAT scaled_root_thickness = root_scale_factor * this->thickness;
    sdFLOAT scaled_nbr_thickness = nbr_scale_factor * nbr_nth_layer->thickness;
    sdFLOAT interfacial_area = edge_length_finest_scale * 0.5 * (scaled_root_thickness + scaled_nbr_thickness);
    return interfacial_area;
  }

  /**
   @brief Returns the rotated and scaled conductivity of layer

   @details Rotates conductivity tensor of layer by provided rotation angle and re-scales to new
            scale defined by scale_diff = scale_from - scale_to.
  */
  template<asINT32 SCALE_DIFF>
  VOID rotated_scaled_conductivity(sdFLOAT kappa[3], const sdFLOAT unfolding_rotation,
      const asINT32 prior_index) {
    this->rotated_transverse_conductivity(kappa, unfolding_rotation, prior_index);
    if constexpr(SCALE_DIFF == 0) {
      return;
    } else {
      constexpr sdFLOAT vr_cond_scale = vr_length_scale_factor(SCALE_DIFF);
      vmul(kappa, vr_cond_scale);
    }
  }

  VOID rotated_conductivity(sdFLOAT kappa[3], const sdFLOAT unfolding_rotation,
      const asINT32 prior_index) {
    this->rotated_transverse_conductivity(kappa, unfolding_rotation, prior_index);
  }

  /**
   @brief Returns the rotated and scaled gradT of layer

   @details Rotates gradT of layer by provided rotation angle and re-scales to new
            scale defined by scale_diff = scale_from - scale_to.
  */
  template<asINT32 SCALE_DIFF>
  VOID rotated_scaled_grad_t(sdFLOAT temp_grad[2], const sdFLOAT unfolding_rotation,
      const asINT32 prior_index) {
    vrotate2(temp_grad, unfolding_rotation, this->grad_t[prior_index]);
    if constexpr(SCALE_DIFF == 0) {
      return;
    } else {
      constexpr sdFLOAT vr_grad_scale = vr_inverse_length_scale_factor(SCALE_DIFF);
      vmul(temp_grad, vr_grad_scale);
    }
  }
  template<asINT32 SCALE_DIFF>
  VOID rotated_scaled_grad_t(sdFLOAT temp_grad[2], const sdFLOAT unfolding_rotation,
      const asINT32 prior_index, STATE_VECTOR_INDEX column_index) {
    // This one is for the implicit solver
    sdFLOAT temp_grad_coeffs[2];
    ccDOTIMES(i, 2) {
      temp_grad_coeffs[i] = layer_matrix_data.stencil_coeffs[i][column_index];
      //temp_grad_coeffs[i] = layer_matrix_data.stencil_coeffs[i].at(column_index);
    }
    vrotate2(temp_grad, unfolding_rotation, temp_grad_coeffs);
    if constexpr(SCALE_DIFF == 0) {
      return;
    } else {
      //constexpr sdFLOAT vr_grad_scale = vr_grad_t_scale_factor(SCALE_DIFF);
      constexpr sdFLOAT vr_grad_scale = vr_inverse_length_scale_factor(SCALE_DIFF);
      vmul(temp_grad, vr_grad_scale);
    }
  }
  template<asINT32 SCALE_DIFF>
  VOID rotated_scaled_grad_t(sdFLOAT temp_grad_coeffs[2], const sdFLOAT unfolding_rotation) {
    // Also used for the implicit solver, but for the boundary stencil terms in the secondary gradient calculation.
    sdFLOAT rotated_temp_grad_coeffs[2];
    vrotate2(rotated_temp_grad_coeffs, unfolding_rotation, temp_grad_coeffs);
    vcopy2(temp_grad_coeffs, rotated_temp_grad_coeffs);
    if constexpr(SCALE_DIFF == 0) {
      return;
    } else {
      //constexpr sdFLOAT vr_grad_scale = vr_grad_t_scale_factor(SCALE_DIFF);
      constexpr sdFLOAT vr_grad_scale = vr_inverse_length_scale_factor(SCALE_DIFF);
      vmul(temp_grad_coeffs, vr_grad_scale);
    }
  }
  VOID rotated_grad_t(sdFLOAT temp_grad[2], const sdFLOAT unfolding_rotation,
      const asINT32 prior_index, STATE_VECTOR_INDEX column_index) {
    // This one is for the implicit solver
    sdFLOAT temp_grad_coeffs[2];
    ccDOTIMES(i, 2) {
      temp_grad_coeffs[i] = layer_matrix_data.stencil_coeffs[i][column_index];
    }
    vrotate2(temp_grad, unfolding_rotation, temp_grad_coeffs);
  }
  VOID rotated_grad_t(sdFLOAT temp_grad_coeffs[2], const sdFLOAT unfolding_rotation) {
    // Also used for the implicit solver, but for the boundary stencil terms in the secondary gradient calculation.
    sdFLOAT rotated_temp_grad_coeffs[2];
    vrotate2(rotated_temp_grad_coeffs, unfolding_rotation, temp_grad_coeffs);
    vcopy2(temp_grad_coeffs, rotated_temp_grad_coeffs);
  }

  uINT64 ckpt_len()  { 
    return (sizeof(conductivity) + sizeof(temperature) + sizeof(source_per_area) + sizeof(thickness) + sizeof(grad_t) 
            + sizeof(accumulated_dT) + sizeof(heat_capacity) + sizeof(passthrough_source));
  }

  VOID read_ckpt()  { 
    read_lgi(conductivity);
    read_lgi(temperature);
    read_lgi(source_per_area);
    read_lgi(thickness);
    read_lgi(grad_t);
    read_lgi(accumulated_dT);
    read_lgi(heat_capacity);
    read_lgi(passthrough_source);
  }

  VOID write_ckpt() {
    write_ckpt_lgi(conductivity);
    write_ckpt_lgi(temperature);
    write_ckpt_lgi(source_per_area);
    write_ckpt_lgi(thickness);
    write_ckpt_lgi(grad_t);
    write_ckpt_lgi(accumulated_dT);
    write_ckpt_lgi(heat_capacity);
    write_ckpt_lgi(passthrough_source);
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    SURFEL_SHELL_LAYER_SEND_FIELD field = reinterpret_cast<SURFEL_SHELL_LAYER_SEND_FIELD>(send_data_info.send_buffer);
#ifdef CONDUCTION_SHELL_COMM_BOTH_TIME_INDICES
    // CONDUCTION-TODO: Send/receive only required time index
    memcpy(field->m_temp, temperature, sizeof(field->m_temp));
    memcpy(field->m_grad_t, grad_t, sizeof(field->m_grad_t));
    memcpy(field->m_conductivity, conductivity, sizeof(field->m_conductivity));
    memcpy(field->m_heat_capacity, heat_capacity, sizeof(field->m_heat_capacity));
    memcpy(field->m_passthrough_source, passthrough_source, sizeof(field->m_passthrough_source));
#else
#error Single time index receive is not implemented
    field->m_temp = temperature[time_index];
    memcpy(field->m_grad_t, grad_t[time_index], sizeof(field->m_grad_t));
    memcpy(field->m_conductivity, conductivity[time_index], sizeof(field->m_conductivity));
    field->m_heat_capacity = heat_capacity[time_index];
    field->m_passthrough_source = passthrough_source[time_index];
#endif
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    SURFEL_SHELL_LAYER_SEND_FIELD field = reinterpret_cast<SURFEL_SHELL_LAYER_SEND_FIELD>(recv_data_info.recv_buffer);
#ifdef CONDUCTION_SHELL_COMM_BOTH_TIME_INDICES
    // CONDUCTION-TODO: Send/receive only required time index
    memcpy(temperature, field->m_temp, sizeof(field->m_temp));
    memcpy(grad_t, field->m_grad_t, sizeof(field->m_grad_t));
    memcpy(conductivity, field->m_conductivity, sizeof(field->m_conductivity));
    memcpy(heat_capacity, field->m_heat_capacity, sizeof(field->m_heat_capacity));
    memcpy(passthrough_source, field->m_passthrough_source, sizeof(field->m_passthrough_source));
#else
#error Single time index receive is not implemented
    temperature[time_index] = field->m_temp;
    memcpy(grad_t[time_index], field->m_grad_t, sizeof(field->m_grad_t));
    memcpy(conductivity[time_index], field->m_conductivity, sizeof(field->m_conductivity));
    heat_capacity[time_index] = field->m_heat_capacity;
    passthrough_source[time_index] = field->m_passthrough_source;
#endif
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
  
  static asINT32 wsurfel_send_size() {
    return sizeof(sWSURFEL_SHELL_LAYER_SEND_FIELD) / sizeof(sdFLOAT);
  }

  // m_conductivity here is really normal conductivity, which is element 3 of the 4-element array.
  // because conductivity in SHELL_LAYER_DATA is organazised as a 2D array [time_index][dimension],
  // we have to extract the two elements for which dimesion=3 individually. 

  VOID fill_wsurfel_send_buffer(sdFLOAT* &send_buffer) {
    WSURFEL_SHELL_LAYER_SEND_FIELD field = reinterpret_cast<WSURFEL_SHELL_LAYER_SEND_FIELD>(send_buffer);
    ccDOTIMES(t_index,N_TIME_INDICES) {
      memcpy(&field->m_conductivity[t_index], normal_conductivity_ptr(t_index), struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_conductivity[t_index]));
    }
    memcpy(&field->m_temp, &temperature, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_temp));
    memcpy(&field->m_thickness, &thickness, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_thickness));
    memcpy(&field->m_heat_capacity, &heat_capacity, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_heat_capacity));
    memcpy(&field->m_source_per_area, &source_per_area, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_source_per_area));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_wsurfel_recv_buffer(sdFLOAT* &recv_buffer) {
    WSURFEL_SHELL_LAYER_SEND_FIELD field = reinterpret_cast<WSURFEL_SHELL_LAYER_SEND_FIELD>(recv_buffer);
    ccDOTIMES(t_index,N_TIME_INDICES) {
      memcpy(normal_conductivity_ptr(t_index), &field->m_conductivity[t_index], struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_conductivity[t_index]));
    }
    memcpy(&temperature, &field->m_temp, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_temp));
    memcpy(&thickness, &field->m_thickness, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_thickness));
    memcpy(&heat_capacity, &field->m_heat_capacity, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_heat_capacity));
    memcpy(&source_per_area, &field->m_source_per_area, struct_field_size(WSURFEL_SHELL_LAYER_SEND_FIELD, m_source_per_area));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

} *SHELL_LAYER_DATA;

typedef struct sSURFELS_SHELL_LAYER_DATA
{
  asINT32 n_shell_conduction_surfels_layers = 0;
  std::vector<sSHELL_LAYER_DATA> vec_layers_data;
  std::vector<sLAYER_RAD_DATA> layer_rad_data;
  SHELL_LAYER_DATA layer_data(asINT32 surfel_nth_layer) {
    return &vec_layers_data.at(surfel_nth_layer);
  }
} SURFELS_SHELL_LAYER_DATA;

extern sSURFELS_SHELL_LAYER_DATA g_surfels_shell_layer_data;
extern sCONDUCTION_SIM_INFO g_conduction_sim_info;

struct cSHELL_CONDUCTION_STENCIL_NEIGHBOR;

template<typename SFL_TYPE_TAG>
struct tSURFEL_SHELL_CONDUCTION_DATA
{
  EXTRACT_SURFEL_TRAITS

  // Rotation matrix to transform 3D global to surfel's 2D local coordinates
  // can be converted and stored as axis and angle form and use Rodrigues's 
  // rotation formula to reduce storage requirements
  STP_PHYS_VARIABLE local_rotation_mat[3][3];

  asINT32 stencil_info_internal_edge_index;
  asINT32 stencil_info_boundary_edge_index;
  asINT32 stencil_info_vertex_nbr_index;
  asINT32 stencil_info_end_index;
  

  asINT32 shell_layer_data_index;

  SHELL_CONFIG_PHYSICS_DESCRIPTOR m_shell_config_pd = nullptr;

  VOID set_stencil_info_internal_edge_index(const asINT32 index) {
    stencil_info_internal_edge_index = index;
  }

  VOID set_stencil_info_boundary_edge_index(const asINT32 index) {
    stencil_info_boundary_edge_index = index;
  }

  VOID set_stencil_info_vertex_nbr_index(const asINT32 index) {
    stencil_info_vertex_nbr_index = index;
  }

  VOID set_stencil_info_end_index(const asINT32 index) {
    stencil_info_end_index = index;
  }

  asINT32 get_stencil_start_index() { return stencil_info_internal_edge_index; }

  asINT32 get_stencil_internal_edge_index() { return stencil_info_internal_edge_index; }

  asINT32 num_internal_edges() {
    return stencil_info_boundary_edge_index - stencil_info_internal_edge_index;
  }

  asINT32 num_boundary_edges() {
    return stencil_info_vertex_nbr_index - stencil_info_boundary_edge_index;
  }

  asINT32 num_vertex_neighbors() {
    return stencil_info_end_index - stencil_info_vertex_nbr_index;
  }

  asINT8 num_layers() {
    cassert(m_shell_config_pd != nullptr);
    return m_shell_config_pd->n_layers;
  }

  sSHELL_CONFIG_PHYSICS_DESCRIPTOR* shell_config_pd() {
    return m_shell_config_pd;
  }

  VOID set_shell_config_pd(SHELL_CONFIG_PHYSICS_DESCRIPTOR spd) {
    m_shell_config_pd = spd;
  }

  VOID set_shell_layer_data_index(asINT32 &index) {
    shell_layer_data_index = index;
  }

  SHELL_LAYER_DATA layer(asINT32 nth_layer) {
    cassert(nth_layer < num_layers());
    return g_surfels_shell_layer_data.layer_data(shell_layer_data_index + nth_layer);
  }

  SHELL_LAYER_DATA layer_reversed(asINT32 nth_layer) {
    cassert(nth_layer < num_layers());
    return g_surfels_shell_layer_data.layer_data(shell_layer_data_index + 
        this->num_layers() - 1 - nth_layer);
  }

  VOID write_ckpt()    { 
    asINT8 n_layer = this->num_layers();
    for(int i = 0; i < n_layer; i++) {
      this->layer(i)->write_ckpt();
    }
  }

  VOID read_ckpt() { 
   //storage was already allocated in dgf_reader_sp.cc: read_surfel_table
   //needs the size of the vector to be constant between simulations
    asINT8 n_layer = this->num_layers();
    for(int i = 0; i < n_layer; i++) {
      this->layer(i)->read_ckpt();
    }
  }

  uINT64 ckpt_len() { return this->layer(0)->ckpt_len() * this->num_layers(); }

  // rotates a direction vector to local 2D coordinates
  // vec_global should be in plane of surfel so that it reduces to a 2d vector 
  template <typename FLOAT1, typename FLOAT2>
  VOID rotate_to_local_coordinates(FLOAT1 vec_local[2], const FLOAT2 vec_global[3]) {
    vec_local[0] = vdot(local_rotation_mat[0],vec_global);
    vec_local[1] = vdot(local_rotation_mat[1],vec_global);
  }

  // rotates a direction vector to local 2D coordinates
  // vec_global should be in plane of surfel so that it reduces to a 2d vector 
  template <typename FLOAT1, typename FLOAT2>
  VOID rotate_to_global_coordinates(FLOAT1 vec_global[3], const FLOAT2 vec_local[2]) {
    ccDOTIMES(i,3) {
      vec_global[i] = local_rotation_mat[0][i]*vec_local[0] + local_rotation_mat[1][i]*vec_local[1];
    }
  }

// Using rodriques' formula with axis-angle representation rather than rotation matrix
//   template <typename FLOAT1, typename FLOAT2>
//   VOID rotate_to_local_coordinates(FLOAT1 vec_local[2], const FLOAT2 vec_global[3]) {
//     FLOAT1 cos_theta = cos(local_rotation_angle);
//     FLOAT1 k_dot_v_term = vdot(local_rotation_axis,vec_global)*(1.0-cos_theta);
//     FLOAT1 sin_theta = sin(local_rotation_angle);
//     vscale2(vec_local,cos_theta,vec_global);
//     vec_local[0] += (local_rotation_axis[1]*vec_global[2]-local_rotation_axis[2]*vec_global[1])*sin_theta + local_rotation_axis[0]*k_dot_v_term;
//     vec_local[1] += (local_rotation_axis[2]*vec_global[0]-local_rotation_axis[0]*vec_global[2])*sin_theta + local_rotation_axis[1]*k_dot_v_term;
// #ifdef DEBUG
//     FLOAT1 z_element = vec_global[2]*cos_theta
//                      + (local_rotation_axis[0]*vec_global[1]-local_rotation_axis[1]*vec_global[0])*sin_theta
//                      + local_rotation_axis[2]*k_dot_v_term;
//     if (fabs(z_element) > 1e-3 ) {
//       std::cout << "Warning: Rotated vector in surfel is not 2D (vec = [" << vec_local[0] << ", " << vec_local[1] << ", " << (z_element) << "])" << std::endl;
//     }
// #endif
//   }

  VOID init();
  VOID seed(sSURFEL *surfel);
  VOID seed_conductivity(sSURFEL *surfel, int layer_index = -1);

  /* sampling/grad_t computation functions */
  VOID copy_seed_grad_t();
  VOID reset_grad_t(const asINT32 prior_index);
  VOID compute_grad_t(const asINT32 prior_index, std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max);

  VOID add_boundary_edge_lsq_contribution(cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr_stencil, const asINT32 prior_index,
      std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max);
  VOID add_internal_edge_lsq_contribution(cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr_stencil, const asINT32 prior_index,
      std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max);
  VOID add_vertex_nbr_lsq_contribution(cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr_stencil, const asINT32 prior_index);
  VOID gather_grad_t_from_stencil_surfels(const asINT32 prior_index, std::vector<sdFLOAT>& temp_cap_min,
      std::vector<sdFLOAT>& temp_cap_max);

  // Implicit solver
  VOID assemble_internal_edge_gradient_coefficients(sSURFEL* surfel); 
  VOID assemble_vertex_nbr_gradient_coefficients(sSURFEL* surfel); 
  VOID assemble_boundary_edge_gradient_coefficients(sSURFEL* surfel);

  /** Dynamics functions */
  typedef struct sFLUXES_SOURCES_AND_CAPS {
    std::vector<sdFLOAT> sum_fluxes_and_sources_in_layer;
    std::vector<sdFLOAT> temp_cap_min;
    std::vector<sdFLOAT> temp_cap_max;
  } *FLUXES_SOURCES_AND_CAPS;

  sdFLOAT get_layer_temperature(const asINT32 layer_ind, const asINT32 index) {
    return layer(layer_ind)->temp(index);
  }

  sdFLOAT get_layer_prior_temperature(const asINT32 layer_index, const asINT32 surfel_scale) {
    const asINT32 index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel_scale);
    return layer(layer_index)->temp(index);
  }

  const sdFLOAT* get_layer_gradient(const asINT32 layer_ind, const asINT32 index) {
    return layer(layer_ind)->grad_t[index];
  }

  const sdFLOAT* get_layer_conductivity(const asINT32 layer_ind, const asINT32 index) {
    return layer(layer_ind)->conductivity_ptr(index);
  }

  VOID dynamics(sSURFEL* root_surfel, const BOOLEAN is_seeding_call,
      sFLUXES_SOURCES_AND_CAPS &fluxes_sources_and_caps);

  VOID evolve(sSURFEL* root_surfel, const asINT32 prior_index);

  VOID update_passthrough(sSURFEL* root_surfel, const asINT32 prior_index,
      sFLUXES_SOURCES_AND_CAPS &fluxes_sources_and_caps);

  VOID enforce_temperature_caps_using_passthrough(sSURFEL* root_surfel, const asINT32 prior_index,
      sFLUXES_SOURCES_AND_CAPS &fluxes_sources_and_caps);

  VOID finalize_passthrough_quantities(const asINT32 prior_index);

  /* communication functions */
  VOID add_send_size(asINT32 &send_size, asINT32 &conduction_send_size) {
    send_size            += num_layers() * (sizeof(sSURFEL_SHELL_LAYER_SEND_FIELD) / sizeof(sdFLOAT));
    conduction_send_size += num_layers() * (sizeof(sSURFEL_SHELL_LAYER_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info) {
    ccDOTIMES(n, num_layers()) {
      SHELL_LAYER_DATA nth_layer = this->layer(n);
      nth_layer->fill_send_buffer(send_data_info);
    }
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info) {
    ccDOTIMES(n, num_layers()) {
      SHELL_LAYER_DATA nth_layer = this->layer(n);
      nth_layer->expand_recv_buffer(recv_data_info);
    }
  }

  asINT32 wsurfel_send_size() {
    asINT32 send_size = num_layers() * sSHELL_LAYER_DATA::wsurfel_send_size();
    ccDOTIMES(n, num_layers()) {
      SHELL_LAYER_DATA nth_layer = this->layer(n);
      if (nth_layer->is_insulation_layer())
        send_size += sLAYER_RAD_DATA::rad_send_size();
    }
    return send_size;
  }

  VOID fill_wsurfel_send_buffer(sdFLOAT* &send_buffer) {
    ccDOTIMES(n, num_layers()) {
      SHELL_LAYER_DATA nth_layer = this->layer(n);
      nth_layer->fill_wsurfel_send_buffer(send_buffer);
      if (nth_layer->is_insulation_layer())
        nth_layer->layer_rad_data()->fill_wsurfel_rad_send_buffer(send_buffer);
    }
  }
    
  VOID expand_wsurfel_recv_buffer(sdFLOAT* &recv_buffer) {
    ccDOTIMES(n, num_layers()) {
      SHELL_LAYER_DATA nth_layer = this->layer(n);
      nth_layer->expand_wsurfel_recv_buffer(recv_buffer);
      if (nth_layer->is_insulation_layer())
        nth_layer->layer_rad_data()->expand_wsurfel_rad_recv_buffer(recv_buffer);
    }
  }
};

typedef tSURFEL_SHELL_CONDUCTION_DATA<SFL_SDFLOAT_TYPE_TAG> sSURFEL_SHELL_CONDUCTION_DATA, *SURFEL_SHELL_CONDUCTION_DATA;

// CONDUCTION-TODO: We should have a "surfel_dcache" which should these repeatedly used values
#define GET_SHELL_CONDUCTION_VARS                                               \
  SURFEL_SHELL_CONDUCTION_DATA root_surfel_shell_conduction = this;             \
  const asINT32 num_layers = root_surfel_shell_conduction->num_layers();

#define DO_INTERNAL_EDGE_STENCILS(_root_shell_data, _n_int_edge)                         \
  for (asINT32 _n_int_edge = _root_shell_data->stencil_info_internal_edge_index;         \
       _n_int_edge < _root_shell_data->stencil_info_boundary_edge_index;                 \
       _n_int_edge++)

#define DO_STENCIL_NEIGHBORS(_root_shell_data, _n_int_edge, _is_boundary_nbr)            \
  for (auto [_n_int_edge, _is_boundary_nbr] = std::pair<asINT32, BOOLEAN>(_root_shell_data->stencil_info_internal_edge_index, FALSE); \
       _n_int_edge < _root_shell_data->stencil_info_end_index; \
       _n_int_edge++, _is_boundary_nbr = _n_int_edge >= _root_shell_data->stencil_info_boundary_edge_index && _n_int_edge < _root_shell_data->stencil_info_vertex_nbr_index)

#define DO_BOUNDARY_EDGE_STENCILS(_root_shell_data, _n_bnd_edge)                         \
  for (asINT32 _n_bnd_edge = _root_shell_data->stencil_info_boundary_edge_index;         \
       _n_bnd_edge < _root_shell_data->stencil_info_vertex_nbr_index;                    \
       _n_bnd_edge++)

#define DO_EDGE_NEIGHBOR_STENCILS(_root_shell_data, _n_edge)                         \
  for (asINT32 _n_edge = _root_shell_data->stencil_info_internal_edge_index;         \
       _n_edge < _root_shell_data->stencil_info_vertex_nbr_index;                    \
       _n_edge++)

#define DO_VERTEX_NEIGHBOR_STENCILS(_root_shell_data, _n_vert)                       \
  for (asINT32 _n_vert = _root_shell_data->stencil_info_vertex_nbr_index;            \
       _n_vert < _root_shell_data->stencil_info_end_index;                           \
       _n_vert++)

//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_CONDUCTION_DATA
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_CONDUCTION_DATA
{
  STP_PHYS_VARIABLE solid_temp;
  STP_PHYS_VARIABLE solid_temp_prime;
  VOID pre_advect_init() {
    solid_temp = 0;
  }
  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_CONDUCTION_DATA *even_surfel_conduction_data);
  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }
} *SAMPLING_SURFEL_CONDUCTION_DATA;

//------------------------------------------------------------------------------
// Datatypes for UBLK stencils used in least squares
//------------------------------------------------------------------------------
//CONDUCTION-CHECK - Change 4x4x4 to 19x8 so it is easy to check connectivity
typedef struct sCONDUCTION_UBLK_STENCIL_GEOM_DATA
{
  dFLOAT voxel_centroid[3][4][4][4];
  sdFLOAT voxel_pfluid[4][4][4];
#if DEBUG
  BOOLEAN is_voxel_collected[4][4][4];
  UBLK ublk[4][4][4];
#endif
} *CONDUCTION_UBLK_STENCIL_GEOM_DATA;

typedef struct sCONDUCTION_UBLK_STENCIL_TEMP_DATA
{
  sdFLOAT temperature[4][4][4];
} *CONDUCTION_UBLK_STENCIL_TEMP_DATA;

/** @brief Base class for fluid and solid interface data blocks

Following types are derived from this base class:
- Conduction open shell data: sCONDUCTION_INTERFACE_OPEN_SHELL_DATA
- Sampled volume data: sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA
  - Conduction closed shell: sCONDUCTION_INTERFACE_SOLID_DATA
  - Fluid: sCONDUCTION_INTERFACE_FLUID_DATA
*/
template<typename SFL_TYPE_TAG>
struct tCONDUCTION_INTERFACE_BASE_DATA {
  EXTRACT_SURFEL_TRAITS

  union {
    SHOB_ID m_coupled_surfel_id;  // An ID live here temporarily during parsing until we can convert it into a pointer
    sSURFEL* m_coupled_surfel;
  };
  
  VOID init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc);

};
typedef tCONDUCTION_INTERFACE_BASE_DATA<SFL_SDFLOAT_TYPE_TAG> sCONDUCTION_INTERFACE_BASE_DATA, *CONDUCTION_INTERFACE_BASE_DATA;

// Accumulated energy by the layers is checkpointed by the interface fluid data, although data resides in the ghost
// interface solid data while being accumulated. Since the ghost conduction surfel is not allocated yet when reading the
// checkpoint file, the accumulated_dE from the checkpoint is stored temporarily in a global map, and moved later
// during seeding to the interface data. 
extern std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>> g_ckpt_conduction_interface_accumulations_map;
extern std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>> g_ckpt_shell_layer_accumulations_map;
VOID fill_ckpt_conduction_interface_accumulations();
VOID fill_ckpt_shell_layer_accumulations();

template<typename SFL_TYPE_TAG>
struct tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA : public tCONDUCTION_INTERFACE_BASE_DATA<SFL_TYPE_TAG> {
  using BASE = tCONDUCTION_INTERFACE_BASE_DATA<SFL_TYPE_TAG>;
  // Data to be checkpointed starts here
  sdFLOAT conductivity;
  sdFLOAT temp;
  sdFLOAT thickness;
  sdFLOAT heat_capacity;
  sdFLOAT source_per_area;

  dFLOAT heat_flux_prime; //used to underrelax interface heat fluxes computed by quasi 1D, uses dFLOAT to be consistent
  sdFLOAT wall_temp_underrelaxed;
  
  dFLOAT accumulated_heat_flux;
  sdFLOAT s2v_heat_flux_prime;
  // Data to be checkpointed ends here

  VOID init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc) {
    BASE::init(surfel, surfel_desc);
    accumulated_heat_flux = 0.0;
    wall_temp_underrelaxed = 0.0;
    s2v_heat_flux_prime = 0.0;
  }
  
  uINT64 ckpt_len()  { 
    return (sizeof(conductivity) + sizeof(temp) + sizeof(thickness) + sizeof(heat_capacity) + sizeof(source_per_area) 
            + sizeof(heat_flux_prime) + sizeof(wall_temp_underrelaxed)
            + sizeof(accumulated_heat_flux) + sizeof(s2v_heat_flux_prime));
  }
  VOID read_ckpt()  { 
    read_lgi(conductivity);
    read_lgi(temp);
    read_lgi(thickness);
    read_lgi(heat_capacity);
    read_lgi(source_per_area);
    
    read_lgi(heat_flux_prime);
    read_lgi(wall_temp_underrelaxed);
    
    read_lgi(accumulated_heat_flux);
    read_lgi(s2v_heat_flux_prime);
  }
  VOID write_ckpt() {
    write_ckpt_lgi(conductivity);
    write_ckpt_lgi(temp);
    write_ckpt_lgi(thickness);
    write_ckpt_lgi(heat_capacity);
    write_ckpt_lgi(source_per_area);
    
    write_ckpt_lgi(heat_flux_prime);
    write_ckpt_lgi(wall_temp_underrelaxed);
    
    write_ckpt_lgi(accumulated_heat_flux);
    write_ckpt_lgi(s2v_heat_flux_prime);
  }

  // Not necessary unless we have wsurfels with different send sizes.
  // May be useful for shell surfels.
  static VOID add_send_size(asINT32 &send_size) {
    send_size += (sizeof(sWSURFEL_SAMPLED_BASE_SEND_FIELD) / sizeof(sdFLOAT));
  }

  static asINT32 send_size() {
    return sizeof(sWSURFEL_SAMPLED_BASE_SEND_FIELD) / sizeof(sdFLOAT);
  }

  VOID fill_send_buffer(sdFLOAT* &send_buffer) {
    WSURFEL_SAMPLED_BASE_SEND_FIELD field = reinterpret_cast<WSURFEL_SAMPLED_BASE_SEND_FIELD>(send_buffer);
    memcpy(&field->m_conductivity, &conductivity, sizeof(conductivity));
    memcpy(&field->m_temp, &temp, sizeof(temp));
    memcpy(&field->m_thickness, &thickness, sizeof(thickness));
    memcpy(&field->m_heat_capacity, &heat_capacity, sizeof(heat_capacity));
    memcpy(&field->m_source_per_area, &source_per_area, sizeof(source_per_area));
    memcpy(&field->m_heat_flux_prime, &heat_flux_prime, sizeof(heat_flux_prime));
    sdFLOAT coupled_accumulated_heat_flux; //information exchange between SPs kept as sdFLOAT
    if (include_coupled_accumulated_heat_flux()) {
      tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG> *coupled_interface_data = 
          this->m_coupled_surfel->conduction_interface_sampled_base_data();
      coupled_accumulated_heat_flux = coupled_interface_data->accumulated_heat_flux;
      coupled_interface_data->accumulated_heat_flux = 0.0;
    } else {
      coupled_accumulated_heat_flux = 0.0;
    }
    memcpy(&field->m_accumulated_heat_flux, &coupled_accumulated_heat_flux, sizeof(coupled_accumulated_heat_flux));
    // memcpy(&field->m_wall_temp_underrelaxed, &wall_temp_underrelaxed, sizeof(wall_temp_underrelaxed));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sdFLOAT* &recv_buffer) {
    WSURFEL_SAMPLED_BASE_SEND_FIELD field = reinterpret_cast<WSURFEL_SAMPLED_BASE_SEND_FIELD>(recv_buffer);
    memcpy(&conductivity, &field->m_conductivity, sizeof(conductivity));
    memcpy(&temp, &field->m_temp, sizeof(temp));
    memcpy(&thickness, &field->m_thickness, sizeof(thickness));
    memcpy(&heat_capacity, &field->m_heat_capacity, sizeof(heat_capacity));
    memcpy(&source_per_area, &field->m_source_per_area, sizeof(source_per_area));
    memcpy(&heat_flux_prime, &field->m_heat_flux_prime, sizeof(heat_flux_prime));
    sdFLOAT coupled_accumulated_heat_flux; //information exchange between SPs kept as sdFLOAT
    memcpy(&coupled_accumulated_heat_flux, &field->m_accumulated_heat_flux, sizeof(coupled_accumulated_heat_flux));
    if (include_coupled_accumulated_heat_flux()) {
      this->m_coupled_surfel->conduction_interface_sampled_base_data()->accumulated_heat_flux += coupled_accumulated_heat_flux;
    }
    // memcpy(&wall_temp_underrelaxed, &field->m_wall_temp_underrelaxed, sizeof(wall_temp_underrelaxed));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

 //private:
  // Comms accumulated heat flux into the surfel it is coupled to a conduction surfel that contains layers
  inline BOOLEAN include_coupled_accumulated_heat_flux() {
    if (this->m_coupled_surfel == NULL) {
       return FALSE;
    } else {
       return (!this->m_coupled_surfel->is_conduction_open_shell());
    }
  }
};
typedef tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_SDFLOAT_TYPE_TAG> sCONDUCTION_INTERFACE_SAMPLED_BASE_DATA, *CONDUCTION_INTERFACE_SAMPLED_BASE_DATA;

template<typename SFL_TYPE_TAG>
struct tCONDUCTION_INTERFACE_FLUID_DATA : public tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG> {
  using BASE = tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG>;
  using COUPLED_SURFEL = tSURFEL<SFL_TYPE_TAG>*;

  // Needed for update_surfel_meas_windows
  sdFLOAT meas_corrected_mass;
  sdFLOAT meas_force[3];
  sdFLOAT mom[3];
  sdFLOAT eff_htc;
  sdFLOAT meas_temp_wall_real;
  sdFLOAT meas_htc;

  // Needed for compute_interface_heat_flux and compute_out_states_t
  sdFLOAT mom_density;

  // Needed in compute_out_states_t and possibly elsewhere
  sdFLOAT mass;

  // Needed in compute_surfel_scalar_flux

  sdFLOAT q_t;

  // Needed in maybe_update_thermal_turb_values

  sdFLOAT h_t_edge;

  // Data to be checkpointed starts here
  // Coupling parameters used when time is decoupled
  sdFLOAT avg_temp_near_wall;
  sdFLOAT avg_htc;
  dFLOAT temp_near_wall_accum;
  dFLOAT htc_accum;
  // Data to be checkpointed ends here

  VOID accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel, BOOLEAN is_seeding);
  
  VOID init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc) {
    BASE::init(surfel, surfel_desc);
    avg_temp_near_wall = 0.0;
    avg_htc = 0.0;
    temp_near_wall_accum = 0.0;
    htc_accum = 0.0;
  }
  
  uINT64 ckpt_len()  { 
    uINT64 base_ckpt_len = BASE::ckpt_len()
                          + sizeof(dFLOAT) //coupled_accumulated_heat_flux
                          + sizeof(dFLOAT) //coupled_s2v_heat_flux_prime
                          + sizeof(avg_temp_near_wall)
                          + sizeof(avg_htc)
                          + sizeof(temp_near_wall_accum)
                          + sizeof(htc_accum)
                          + sizeof(sdFLOAT);
    if (include_layers_accum_dT(this->m_coupled_surfel)) {
      base_ckpt_len += 2 * sizeof(dFLOAT) * this->m_coupled_surfel->shell_conduction_data()->num_layers();
    }
    return base_ckpt_len;
  }

  VOID read_ckpt()  { 
    BASE::read_ckpt();
    //coupled accumulated flux info stored in a global map, since at this time is unknown if the coupled surfel has
    //been allocated, and filled 
    dFLOAT coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime;
    read_lgi(coupled_accumulated_heat_flux);
    read_lgi(coupled_s2v_heat_flux_prime);
    auto it = g_ckpt_conduction_interface_accumulations_map.find(std::make_pair(this->m_coupled_surfel_id, STP_COND_REALM));
    if (it == g_ckpt_conduction_interface_accumulations_map.end()) {
      //Needs to check if present because in an open shell scenario, both front & back fluid surfels store the
      //conduction data given that they have no knowledge if the opposite surfel is actually coupled. However, only one
      //should contribute to the accumulated data
      g_ckpt_conduction_interface_accumulations_map[std::make_pair(this->m_coupled_surfel_id, STP_COND_REALM)] = 
          {coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime};
    }
    sdFLOAT n_layers;
    read_lgi(n_layers);
    if (n_layers > 0) { 
      auto it = g_ckpt_shell_layer_accumulations_map.find(std::make_pair(this->m_coupled_surfel_id, STP_COND_REALM));
      BOOLEAN add_to_map = (it == g_ckpt_shell_layer_accumulations_map.end());
      dFLOAT layer_accum_dT, layer_dT_prime;
      ccDOTIMES(n, n_layers) {
        read_lgi(layer_accum_dT);
        read_lgi(layer_dT_prime);
        if (add_to_map) {
          g_ckpt_shell_layer_accumulations_map[std::make_pair(this->m_coupled_surfel_id, STP_COND_REALM)].push_back(layer_accum_dT);
          g_ckpt_shell_layer_accumulations_map[std::make_pair(this->m_coupled_surfel_id, STP_COND_REALM)].push_back(layer_dT_prime);
        }
      }
    }
    read_lgi(avg_temp_near_wall);
    read_lgi(avg_htc);
    read_lgi(temp_near_wall_accum);
    read_lgi(htc_accum);
  }

  VOID write_ckpt() {
    BASE::write_ckpt();
    dFLOAT coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime;
    if (BASE::include_coupled_accumulated_heat_flux()) {
      tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG> *coupled_interface_data = 
          this->m_coupled_surfel->conduction_interface_sampled_base_data();
      coupled_accumulated_heat_flux = coupled_interface_data->accumulated_heat_flux;
      coupled_s2v_heat_flux_prime = coupled_interface_data->s2v_heat_flux_prime;
    } else {
      coupled_accumulated_heat_flux = 0.0;
      coupled_s2v_heat_flux_prime = 0.0;
    }
    write_ckpt_lgi(coupled_accumulated_heat_flux);
    write_ckpt_lgi(coupled_s2v_heat_flux_prime);
    sdFLOAT n_layers;
    if (include_layers_accum_dT(this->m_coupled_surfel)) {
      dFLOAT layer_accum_dT, layer_dT_prime;
      SURFEL_SHELL_CONDUCTION_DATA shell_data = this->m_coupled_surfel->shell_conduction_data();
      n_layers = shell_data->num_layers();
      write_ckpt_lgi(n_layers);
      ccDOTIMES(n, n_layers) {
        SHELL_LAYER_DATA nth_layer = shell_data->layer(n);
        layer_accum_dT = nth_layer->accumulated_dT;
        layer_dT_prime = nth_layer->dT_prime;
        write_ckpt_lgi(layer_accum_dT);
        write_ckpt_lgi(layer_dT_prime);
      }
    } else {
      n_layers = 0;
      write_ckpt_lgi(n_layers);
    }
    write_ckpt_lgi(avg_temp_near_wall);
    write_ckpt_lgi(avg_htc);
    write_ckpt_lgi(temp_near_wall_accum);
    write_ckpt_lgi(htc_accum);
  }

  VOID add_send_size(asINT32 &send_size) {
    send_size += (BASE::send_size() + sizeof(sWSURFEL_FLOW_SEND_FIELD) / sizeof(sdFLOAT));
    COUPLED_SURFEL coupled_surfel = coupled_surfel_from_id();
    if (include_layers_accum_dT(coupled_surfel)) {
      send_size += (this->m_coupled_surfel->shell_conduction_data()->num_layers()) 
                   * struct_field_size(SHELL_LAYER_DATA, accumulated_dT) / sizeof(sdFLOAT);
    }
  }

  asINT32 send_size() {
    asINT32 base_send_size = (BASE::send_size() + sizeof(sWSURFEL_FLOW_SEND_FIELD) / sizeof(sdFLOAT));
    COUPLED_SURFEL coupled_surfel = coupled_surfel_from_id();
    if (include_layers_accum_dT(coupled_surfel)) {
      base_send_size += (coupled_surfel->shell_conduction_data()->num_layers()) 
                        * struct_field_size(SHELL_LAYER_DATA, accumulated_dT) / sizeof(sdFLOAT);
    }
    return base_send_size;
  }

  VOID fill_send_buffer(sdFLOAT* &send_buffer) {
    BASE::fill_send_buffer(send_buffer);
    WSURFEL_FLOW_SEND_FIELD field = reinterpret_cast<WSURFEL_FLOW_SEND_FIELD>(send_buffer);
    memcpy(&field->m_meas_corrected_mass, &meas_corrected_mass, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_corrected_mass));
    memcpy(&field->m_meas_force, &meas_force, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_force));
    memcpy(&field->m_mom, &mom, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mom));
    memcpy(&field->m_eff_htc, &eff_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_eff_htc));
    memcpy(&field->m_meas_temp_wall_real, &meas_temp_wall_real, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_temp_wall_real));
    memcpy(&field->m_mom_density, &mom_density, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mom_density));
    memcpy(&field->m_meas_htc, &meas_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_htc));
    memcpy(&field->m_mass, &mass, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mass));
    memcpy(&field->m_h_t_edge, &h_t_edge, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_h_t_edge));
    memcpy(&field->m_q_t, &q_t, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_q_t));
    memcpy(&field->m_avg_temp_near_wall, &avg_temp_near_wall, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_avg_temp_near_wall));
    memcpy(&field->m_avg_htc, &avg_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_avg_htc));
    // memcpy(&field->m_temp_near_wall_accum, &temp_near_wall_accum, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_temp_near_wall_accum));
    // memcpy(&field->m_htc_accum, &htc_accum, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_htc_accum));
    field++;
    if (include_layers_accum_dT()) {
      dFLOAT* accum_dT_field = reinterpret_cast<dFLOAT*>(field);
      SURFEL_SHELL_CONDUCTION_DATA shell_data = this->m_coupled_surfel->shell_conduction_data();
      ccDOTIMES(n, shell_data->num_layers()) {
        SHELL_LAYER_DATA nth_layer = shell_data->layer(n);
        memcpy(accum_dT_field, &nth_layer->accumulated_dT, struct_field_size(SHELL_LAYER_DATA, accumulated_dT));
        accum_dT_field++;
      }
      field = reinterpret_cast<WSURFEL_FLOW_SEND_FIELD>(accum_dT_field);
    }
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sdFLOAT* &recv_buffer) {
    BASE::expand_recv_buffer(recv_buffer);
    WSURFEL_FLOW_SEND_FIELD field = reinterpret_cast<WSURFEL_FLOW_SEND_FIELD>(recv_buffer);
    memcpy(&meas_corrected_mass, &field->m_meas_corrected_mass, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_corrected_mass));
    memcpy(&meas_force, &field->m_meas_force, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_force));
    memcpy(&mom, &field->m_mom, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mom));
    memcpy(&eff_htc, &field->m_eff_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_eff_htc));
    memcpy(&meas_temp_wall_real, &field->m_meas_temp_wall_real, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_temp_wall_real));
    memcpy(&meas_htc, &field->m_meas_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_meas_htc));
    memcpy(&mom_density, &field->m_mom_density, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mom_density));
    memcpy(&mass, &field->m_mass, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_mass));
    memcpy(&h_t_edge, &field->m_h_t_edge, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_h_t_edge));
    memcpy(&q_t, &field->m_q_t, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_q_t));
    memcpy(&avg_temp_near_wall, &field->m_avg_temp_near_wall, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_avg_temp_near_wall));
    memcpy(&avg_htc, &field->m_avg_htc, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_avg_htc));
    // memcpy(&temp_near_wall_accum, &field->m_temp_near_wall_accum, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_temp_near_wall_accum));
    // memcpy(&htc_accum, &field->m_htc_accum, struct_field_size(WSURFEL_FLOW_SEND_FIELD, m_htc_accum));
    field++;
    if (include_layers_accum_dT()) {
      dFLOAT* accum_dT_field = reinterpret_cast<dFLOAT*>(field);
      SURFEL_SHELL_CONDUCTION_DATA shell_data = this->m_coupled_surfel->shell_conduction_data();
      ccDOTIMES(n, shell_data->num_layers()) {
        SHELL_LAYER_DATA nth_layer = shell_data->layer(n);
        memcpy(&nth_layer->accumulated_dT, accum_dT_field, struct_field_size(SHELL_LAYER_DATA, accumulated_dT));
        accum_dT_field++;
      }
      field = reinterpret_cast<WSURFEL_FLOW_SEND_FIELD>(accum_dT_field);
    }
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

 private: 
  // Includes net energy in accumulated layers if coupled to a conduction surfel that contains layers
  // - Needed for supercycling only, so use a more complex mechanism to send info computed by the flow 
  //   rather than allocating extra memory
  // - send/recv buffers allocated before ids transformed to pointers, so needs retrieve pointer from ID
  COUPLED_SURFEL coupled_surfel_from_id() {
    if (this->m_coupled_surfel_id == INVALID_SHOB_ID) {
      return nullptr;
    } else {
      return regular_surfel_from_id(this->m_coupled_surfel_id, STP_COND_REALM);
    }
  }

  inline BOOLEAN include_layers_accum_dT() {
    return (this->m_coupled_surfel->is_conduction_shell());
  }

  inline BOOLEAN include_layers_accum_dT(COUPLED_SURFEL coupled_surfel) {
    return (coupled_surfel != nullptr && coupled_surfel->is_conduction_shell());
  }

};
typedef tCONDUCTION_INTERFACE_FLUID_DATA<SFL_SDFLOAT_TYPE_TAG> sCONDUCTION_INTERFACE_FLUID_DATA, *CONDUCTION_INTERFACE_FLUID_DATA;

template<typename SFL_TYPE_TAG>
struct tCONDUCTION_INTERFACE_SOLID_DATA : public tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG> {
  using BASE = tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG>;

  // Data to be checkpointed starts here
  // Coupling parameters used when time is decoupled
  sdFLOAT avg_temp;
  dFLOAT temp_accum;
  // Data to be checkpointed ends here

  sdFLOAT rad_flux;

  VOID accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel, BOOLEAN is_seeding);
  
  VOID init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc) {
    BASE::init(surfel, surfel_desc);
    avg_temp = 0.0;
    temp_accum = 0.0;
  }
  
  uINT64 ckpt_len()  { 
    return (BASE::ckpt_len() + sizeof(dFLOAT) + sizeof(dFLOAT) + sizeof(avg_temp) + sizeof(temp_accum));
  }

  VOID read_ckpt()  { 
    BASE::read_ckpt();
    //coupled accumulated flux info stored in a global map, since at this time is unknown if the coupled surfel has
    //been allocated, and filled 
    dFLOAT coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime;
    read_lgi(coupled_accumulated_heat_flux);
    read_lgi(coupled_s2v_heat_flux_prime);
    auto it = g_ckpt_conduction_interface_accumulations_map.find(std::make_pair(this->m_coupled_surfel_id, STP_FLOW_REALM));
    if (it == g_ckpt_conduction_interface_accumulations_map.end()) {
      //Needs to check if present because in an open shell scenario, both front & back fluid surfels store the
      //conduction data given that they have no knowledge if the opposite surfel is actually coupled. However, only one
      //should contribute to the accumulated data
      g_ckpt_conduction_interface_accumulations_map[std::make_pair(this->m_coupled_surfel_id, STP_FLOW_REALM)] = 
          {coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime};
    }
    read_lgi(avg_temp);
    read_lgi(temp_accum);
  }

  VOID write_ckpt() {
    BASE::write_ckpt();
    dFLOAT coupled_accumulated_heat_flux, coupled_s2v_heat_flux_prime;
    if (BASE::include_coupled_accumulated_heat_flux()) {
      tCONDUCTION_INTERFACE_SAMPLED_BASE_DATA<SFL_TYPE_TAG> *coupled_interface_data = 
          this->m_coupled_surfel->conduction_interface_sampled_base_data();
      coupled_accumulated_heat_flux = coupled_interface_data->accumulated_heat_flux;
      coupled_s2v_heat_flux_prime = coupled_interface_data->s2v_heat_flux_prime;
    } else {
      coupled_accumulated_heat_flux = 0.0;
      coupled_s2v_heat_flux_prime = 0.0;
    }
    write_ckpt_lgi(coupled_accumulated_heat_flux);
    write_ckpt_lgi(coupled_s2v_heat_flux_prime);
    write_ckpt_lgi(avg_temp);
    write_ckpt_lgi(temp_accum);
  }

  static VOID add_send_size(asINT32 &send_size) {
    send_size += (BASE::send_size() + sizeof(sWSURFEL_CONDUCTION_SEND_FIELD) / sizeof(sdFLOAT));
  }

  static asINT32 send_size() {
    return (BASE::send_size() + sizeof(sWSURFEL_CONDUCTION_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sdFLOAT* &send_buffer) {
    BASE::fill_send_buffer(send_buffer);
    WSURFEL_CONDUCTION_SEND_FIELD field = reinterpret_cast<WSURFEL_CONDUCTION_SEND_FIELD>(send_buffer);
    memcpy(&field->m_avg_temp, &avg_temp, struct_field_size(WSURFEL_CONDUCTION_SEND_FIELD, m_avg_temp));
    // memcpy(&field->m_temp_accum, &temp_accum, struct_field_size(WSURFEL_CONDUCTION_SEND_FIELD, m_temp_accum));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sdFLOAT* &recv_buffer) {
    BASE::expand_recv_buffer(recv_buffer);
    WSURFEL_CONDUCTION_SEND_FIELD field = reinterpret_cast<WSURFEL_CONDUCTION_SEND_FIELD>(recv_buffer);
    memcpy(&avg_temp, &field->m_avg_temp, struct_field_size(WSURFEL_CONDUCTION_SEND_FIELD, m_avg_temp));
    // memcpy(&temp_accum, &field->m_temp_accum, struct_field_size(WSURFEL_CONDUCTION_SEND_FIELD, m_temp_accum));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};
typedef tCONDUCTION_INTERFACE_SOLID_DATA<SFL_SDFLOAT_TYPE_TAG> sCONDUCTION_INTERFACE_SOLID_DATA, *CONDUCTION_INTERFACE_SOLID_DATA;

template<typename SFL_TYPE_TAG>
struct tCONDUCTION_INTERFACE_OPEN_SHELL_DATA : public tCONDUCTION_INTERFACE_BASE_DATA<SFL_TYPE_TAG> {
  using BASE = tCONDUCTION_INTERFACE_BASE_DATA<SFL_TYPE_TAG>;
  using COUPLED_SURFEL = tSURFEL<SFL_TYPE_TAG>*;

  // Data to be checkpointed starts here
  // Coupling parameters used when time is decoupled
  sdFLOAT avg_temp_front, avg_temp_back;
  dFLOAT temp_accum_front, temp_accum_back;
  // Data to be checkpointed ends here
  
  sdFLOAT net_rad_flux_front, net_rad_flux_back;

  // Fixed BC in open shell with distinct back BC can occur either at front or back
  // (Possible optimization is to create a derived interface data class with these values, used when 
  //  surfel->has_distinct_back_bc(), though this is not part of the surfel_type or data types for now, so 
  //  there is not a clear mechanism to determine if has distinct back bcs or not when allocating space) 
  sdFLOAT quasi_1D_bc_tdma;
  sdFLOAT quasi_1D_bc_rhs;

  VOID accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel, BOOLEAN is_seeding);
  
  VOID init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc) {
    BASE::init(surfel, surfel_desc);
    avg_temp_front = 0.0;
    avg_temp_back = 0.0;
    temp_accum_front = 0.0;
    temp_accum_back = 0.0;
  }

  uINT64 ckpt_len()  { 
    return (4 * sizeof(dFLOAT) + //accumulated_heat_flux for front & back coupled surfels
            sizeof(avg_temp_front) + sizeof(avg_temp_back) +
            sizeof(temp_accum_front) + sizeof(temp_accum_back));
  }
  VOID read_ckpt()  { 
    dFLOAT accumulated_heat_flux[2], s2v_heat_flux_prime[2];
    read_lgi(accumulated_heat_flux[0]);
    read_lgi(accumulated_heat_flux[1]);
    read_lgi(s2v_heat_flux_prime[0]);
    read_lgi(s2v_heat_flux_prime[1]);
    g_ckpt_conduction_interface_accumulations_map[std::make_pair(this->m_coupled_surfel_id, STP_FLOW_REALM)] = 
        {accumulated_heat_flux[0], s2v_heat_flux_prime[0], accumulated_heat_flux[1], s2v_heat_flux_prime[1]};
    read_lgi(avg_temp_front);
    read_lgi(avg_temp_back);
    read_lgi(temp_accum_front);
    read_lgi(temp_accum_back);
  }
  VOID write_ckpt() {
    dFLOAT accumulated_heat_flux[2], s2v_heat_flux_prime[2];
    get_coupled_surfel_ckpt_data(this->m_coupled_surfel, accumulated_heat_flux[0], s2v_heat_flux_prime[0]);
    get_coupled_surfel_ckpt_data(this->back_coupled_surfel(), accumulated_heat_flux[1], s2v_heat_flux_prime[1]);
    write_ckpt_lgi(accumulated_heat_flux[0]);
    write_ckpt_lgi(accumulated_heat_flux[1]);
    write_ckpt_lgi(s2v_heat_flux_prime[0]);
    write_ckpt_lgi(s2v_heat_flux_prime[1]);
    write_ckpt_lgi(avg_temp_front);
    write_ckpt_lgi(avg_temp_back);
    write_ckpt_lgi(temp_accum_front);
    write_ckpt_lgi(temp_accum_back);
  }

  static VOID add_send_size(asINT32 &send_size) {
    send_size += (sizeof(sWSURFEL_OPEN_SHELL_SEND_FIELD) / sizeof(sdFLOAT));
  }

  static asINT32 send_size() {
    return (sizeof(sWSURFEL_OPEN_SHELL_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sdFLOAT* &send_buffer) {
    WSURFEL_OPEN_SHELL_SEND_FIELD field = reinterpret_cast<WSURFEL_OPEN_SHELL_SEND_FIELD>(send_buffer);
    dFLOAT accumulated_heat_flux_front = get_accumulated_heat_flux(this->m_coupled_surfel);
    dFLOAT accumulated_heat_flux_back = get_accumulated_heat_flux(this->back_coupled_surfel());
    memcpy(&field->m_accumulated_heat_flux_front, &accumulated_heat_flux_front, sizeof(accumulated_heat_flux_front));
    memcpy(&field->m_accumulated_heat_flux_back, &accumulated_heat_flux_back, sizeof(accumulated_heat_flux_back));
    memcpy(&field->m_avg_temp_front, &avg_temp_front, sizeof(avg_temp_front));
    memcpy(&field->m_avg_temp_back, &avg_temp_back, sizeof(avg_temp_back));
    memcpy(&field->m_quasi_1D_bc_tdma, &quasi_1D_bc_tdma, sizeof(quasi_1D_bc_tdma));
    memcpy(&field->m_quasi_1D_bc_rhs, &quasi_1D_bc_rhs, sizeof(quasi_1D_bc_rhs));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sdFLOAT* &recv_buffer) {
    WSURFEL_OPEN_SHELL_SEND_FIELD field = reinterpret_cast<WSURFEL_OPEN_SHELL_SEND_FIELD>(recv_buffer);
    dFLOAT accumulated_heat_flux_front, accumulated_heat_flux_back;
    memcpy(&accumulated_heat_flux_front, &field->m_accumulated_heat_flux_front, sizeof(accumulated_heat_flux_front));
    memcpy(&accumulated_heat_flux_back, &field->m_accumulated_heat_flux_back, sizeof(accumulated_heat_flux_back));
    expand_accumulated_heat_flux(this->m_coupled_surfel, accumulated_heat_flux_front);
    expand_accumulated_heat_flux(this->back_coupled_surfel(), accumulated_heat_flux_back);
    memcpy(&avg_temp_front, &field->m_avg_temp_front, sizeof(avg_temp_front));
    memcpy(&avg_temp_back, &field->m_avg_temp_back, sizeof(avg_temp_back));
    memcpy(&quasi_1D_bc_tdma, &field->m_quasi_1D_bc_tdma, sizeof(quasi_1D_bc_tdma));
    memcpy(&quasi_1D_bc_rhs, &field->m_quasi_1D_bc_rhs, sizeof(quasi_1D_bc_rhs));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

 private:
  // Comms accumulated heat flux into the surfel it is coupled to coupled to a conduction surfel that contains layers
  COUPLED_SURFEL back_coupled_surfel() {
    if (this->m_coupled_surfel == nullptr) {
      return nullptr;
    } else {
      return static_cast<COUPLED_SURFEL>(this->m_coupled_surfel->opposite_surfel());
    }
  }
  inline BOOLEAN include_accumulated_heat_flux(COUPLED_SURFEL coupled_surfel) {
    if (coupled_surfel == nullptr) {
       return FALSE;
    } else {
       return (coupled_surfel->is_conduction_interface());
    }
  }
  VOID get_coupled_surfel_ckpt_data(COUPLED_SURFEL coupled_surfel, 
                                    dFLOAT &accumulated_heat_flux, dFLOAT &s2v_heat_flux_prime) {
    if (include_accumulated_heat_flux(coupled_surfel)) {
      CONDUCTION_INTERFACE_FLUID_DATA coupled_interface_data = coupled_surfel->conduction_interface_fluid_data();
      accumulated_heat_flux = coupled_interface_data->accumulated_heat_flux;
      s2v_heat_flux_prime = coupled_interface_data->s2v_heat_flux_prime;
    } else {
      accumulated_heat_flux = 0.0;
      s2v_heat_flux_prime = 0.0;
    }
  }
  dFLOAT get_accumulated_heat_flux(COUPLED_SURFEL coupled_surfel) {
    dFLOAT accumulated_heat_flux;
    if (include_accumulated_heat_flux(coupled_surfel)) {
      CONDUCTION_INTERFACE_FLUID_DATA coupled_interface_data = coupled_surfel->conduction_interface_fluid_data();
      accumulated_heat_flux = coupled_interface_data->accumulated_heat_flux;
      coupled_interface_data->accumulated_heat_flux = 0.0;
    } else {
      accumulated_heat_flux = 0.0;
    }
    return accumulated_heat_flux;
  }
  VOID expand_accumulated_heat_flux(COUPLED_SURFEL coupled_surfel, dFLOAT &accumulated_heat_flux) {
    if (include_accumulated_heat_flux(coupled_surfel)) {
      coupled_surfel->conduction_interface_fluid_data()->accumulated_heat_flux += accumulated_heat_flux;
    }
  }

};
typedef tCONDUCTION_INTERFACE_OPEN_SHELL_DATA<SFL_SDFLOAT_TYPE_TAG> sCONDUCTION_INTERFACE_OPEN_SHELL_DATA, *CONDUCTION_INTERFACE_OPEN_SHELL_DATA;

} //inline SIMULATOR_NAMESPACE


#endif//_SIMENG_CONDUCTION_DATA_H
