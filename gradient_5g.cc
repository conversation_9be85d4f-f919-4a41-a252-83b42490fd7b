/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("physics.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Implementation of the gradient operator
 *
 *--------------------------------------------------------------------------*/
#include "gradient_5g.h"
#include "ublk.h"

VOID neighbor2d(ubFLOAT &field, ubFLOAT stencil[][2], ubFLOAT gradient[])
{
  ccDO_UBLK(voxor) {
    for (asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; ++voxel) {
      gradient[0][voxor] += stencil[voxel][0][voxor] * field(voxel);
      gradient[1][voxor] += stencil[voxel][1][voxor] * field(voxel);
    }
  }
}


VOID neighbor3d(ubFLOAT &field, ubFLOAT stencil[][3], ubFLOAT gradient[])
{
  ccDO_UBLK(voxor) {
    for (asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; ++voxel) {
      gradient[0][voxor] += stencil[voxel][0][voxor] * field(voxel);
      gradient[1][voxor] += stencil[voxel][1][voxor] * field(voxel);
      gradient[2][voxor] += stencil[voxel][2][voxor] * field(voxel);
    }
  }
}


VOID neighbor3d_pbl(ubFLOAT &field, ubFLOAT stencil[], ubFLOAT gradient[])
{
  ccDO_UBLK(voxor) {
    gradient[0][voxor] += stencil[0][voxor] * field[voxor];
    gradient[1][voxor] += stencil[1][voxor] * field[voxor];
    gradient[2][voxor] += stencil[2][voxor] * field[voxor];
  }
}

//
// translate the pair of the coordinate of the ublk along an axis [0, 1, 2],
// and the voxel number to the global coordinates along that axis [0 ... 5].
//
inline asINT32 coord(asINT32 ublk_offset, asINT32 voxel, asINT32 axis)
{
  return (ublk_offset << 1) + ((voxel >> (2 - axis)) & 1);
}

class cGRADIENT_2D : public iGRADIENT_OP
{
protected:

  static sdFLOAT s_scale;
  static ubFLOAT s_stencils[3][3][ubFLOAT::N_VOXELS][2];
  asINT32 s_num_weights;

public:
  cGRADIENT_2D()	{ }

  VOID init_stencils(sdFLOAT s_weights[][2], sdFLOAT e2) {
    sdFLOAT weights[s_num_weights];

    for (asINT32 i = 0; i < s_num_weights; ++i) {
      weights[i] = s_weights[i][0] + e2 * s_weights[i][1];
    }

    for (asINT32 x = 0; x < 3; ++x) {
      for (asINT32 y = 0; y < 3; ++y) {
        for (asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; ++voxel) {

          asINT32 xv = coord(x, voxel, STP_X_AXIS);
          asINT32 yv = coord(y, voxel, STP_Y_AXIS);
          asINT32 zv = coord(1, voxel, STP_Z_AXIS);

          for (asINT32 center_voxel = 0; center_voxel < ubFLOAT::N_VOXELS; ++center_voxel) {
            //
            // the vector from center to the voxel
            //
            asINT32 xc = xv - coord(1, center_voxel, STP_X_AXIS);
            asINT32 yc = yv - coord(1, center_voxel, STP_Y_AXIS);
            asINT32 zc = zv - coord(1, center_voxel, STP_Z_AXIS);

            asINT32 index = xc * xc + yc * yc;

            if (index > s_num_weights || index == 0 || zc != 0) {
              s_stencils[x][y][voxel][STP_X_AXIS](center_voxel) = 0.;
              s_stencils[x][y][voxel][STP_Y_AXIS](center_voxel) = 0.;
            } else {
              index--;
              s_stencils[x][y][voxel][STP_X_AXIS](center_voxel) = weights[index] * xc;
              s_stencils[x][y][voxel][STP_Y_AXIS](center_voxel) = weights[index] * yc;
            }
          }
        }
      }
    }
  }

  VOID neighbor(asINT32 x, asINT32 y, asINT32 z, ubFLOAT &field, ubFLOAT gradient[]) {
    neighbor2d(field, s_stencils[x][y], gradient);
  }

  sdFLOAT get_scale() {
    return s_scale;
  }
};

sdFLOAT cGRADIENT_2D::s_scale;
ubFLOAT cGRADIENT_2D::s_stencils[3][3][ubFLOAT::N_VOXELS][2];

class cGRADIENT_2D_19P : public cGRADIENT_2D
{

public:

  cGRADIENT_2D_19P(sdFLOAT e2) {
    sdFLOAT s_weights[][2] = {
      { 4., 0.},	// 1
      { 1., 0.},	// 2
    };
    s_num_weights = sizeof(s_weights) / sizeof(s_weights[0]);
    s_scale = 12.0;
    init_stencils(s_weights, e2);
  }
};

class cGRADIENT_2D_125P : public cGRADIENT_2D
{

public:

  cGRADIENT_2D_125P(sdFLOAT e2) {

    /***************************************************************
     * Original fractal weights, first row + 4/7 * second row is the
     * real weights
     *
    	        { 2. /  5., -11. / 30.},	// 1
    	        { 7. / 45.,  -7. / 60.},	// 2
    	        {		0.,			0.},	// 3
        	    {		0.,	  7. /240.},	// 4
        	    {-1. / 45.,	  1. / 20.},	// 5
        	    {		0.,			0.},	// 6
        	    {		0.,			0.},	// 7
        	    { 1. /720.,	 -1. /480.}		// 8

    *****************************************************************/
    sdFLOAT s_weights[][2] = {
      { 960., 0.},    // 1
      { 448., 0.},    // 2
      {   0., 0.},    // 3
      {  84., 0.},    // 4
      {  32., 0.},    // 5
      {   0., 0.},    // 6
      {   0., 0.},    // 7
      {   1., 0.},    // 8
    };
    s_num_weights = sizeof(s_weights) / sizeof(s_weights[0]);
    s_scale = 5040.0;
    init_stencils(s_weights, e2);
  }
};

class cGRADIENT_3D : public iGRADIENT_OP
{
protected:

  asINT32 s_num_weights;
  static sdFLOAT s_scale;
  static ubFLOAT s_stencils[3][3][3][ubFLOAT::N_VOXELS][N_AXES];

public:
  cGRADIENT_3D() {
    s_num_weights = 2;
    s_scale = 1.0;
  }

  VOID init_stencils(sdFLOAT s_weights[][2], sdFLOAT e2) {
    sdFLOAT  weights[s_num_weights];

    for (asINT32 i = 0; i < s_num_weights; ++i) {
      weights[i] = s_weights[i][0] + e2 * s_weights[i][1];
    }

    for (asINT32 x = 0; x < 3; ++x) {
      for (asINT32 y = 0; y < 3; ++y) {
        for (asINT32 z = 0; z < 3; ++z) {
          for (asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; ++voxel) {

            asINT32 xv = coord(x, voxel, STP_X_AXIS);
            asINT32 yv = coord(y, voxel, STP_Y_AXIS);
            asINT32 zv = coord(z, voxel, STP_Z_AXIS);

            for (asINT32 center_voxel = 0; center_voxel < ubFLOAT::N_VOXELS; ++center_voxel) {
              //
              // the vector from center to the voxel
              //
              asINT32 xc = xv - coord(1, center_voxel, STP_X_AXIS);
              asINT32 yc = yv - coord(1, center_voxel, STP_Y_AXIS);
              asINT32 zc = zv - coord(1, center_voxel, STP_Z_AXIS);

              asINT32 index = xc * xc + yc * yc + zc * zc;

              if (index > s_num_weights || index == 0) {
                s_stencils[x][y][z][voxel][STP_X_AXIS](center_voxel) = 0.;
                s_stencils[x][y][z][voxel][STP_Y_AXIS](center_voxel) = 0.;
                s_stencils[x][y][z][voxel][STP_Z_AXIS](center_voxel) = 0.;
              } else {
                index--;
                s_stencils[x][y][z][voxel][STP_X_AXIS](center_voxel) = weights[index] * xc;
                s_stencils[x][y][z][voxel][STP_Y_AXIS](center_voxel) = weights[index] * yc;
                s_stencils[x][y][z][voxel][STP_Z_AXIS](center_voxel) = weights[index] * zc;
              }
            }
          }
        }
      }
    }
  }

  VOID neighbor(asINT32 x, asINT32 y, asINT32 z, ubFLOAT &field, ubFLOAT gradient[]) {
    neighbor3d(field, s_stencils[x][y][z], gradient);
  }

  sdFLOAT get_scale() {
    return s_scale;
  }
};

sdFLOAT cGRADIENT_3D::s_scale;
ubFLOAT cGRADIENT_3D::s_stencils[3][3][3][ubFLOAT::N_VOXELS][N_AXES];

class cGRADIENT_3D_19P : public cGRADIENT_3D
{

public:

  cGRADIENT_3D_19P(sdFLOAT e2) {
    sdFLOAT s_weights[][2] = {
      { 2. , 0.},	// 1
      { 1. , 0.},	// 2
    };
    s_num_weights = sizeof(s_weights) / sizeof(s_weights[0]);
    s_scale = 12.0;
    init_stencils(s_weights, e2);
  }
};

class cGRADIENT_3D_125P : public cGRADIENT_3D
{

public:

  cGRADIENT_3D_125P(sdFLOAT e2) {
    /***************************************************************
     * Original fractal weights, first row + 4/7 * second row is the
     * real weights
     *
     { 2. /  9.,	 -7. / 30.},	// 1
     { 1. / 10., -11. /120.},	// 2
     { 1. / 30.,  -1. / 40.},	// 3
     { 7. /360.,	 -1. / 60.},	// 4
     {-1. / 90.,	  1. / 40.},	// 5
     {-1. /180.,	  1. / 80.},	// 6
     {		0.,			0.},	// 7
     { 1. /720.,	 -1. /480.}		// 8

    *****************************************************************/
    sdFLOAT s_weights[][2] = {
      { 448., 0.},    // 1
      { 240., 0.},    // 2
      {  96., 0.},    // 3
      {  50., 0.},    // 4
      {  16., 0.},    // 5
      {   8., 0.},    // 6
      {   0., 0.},    // 7
      {   1., 0.},    // 8
    };
    s_num_weights = sizeof(s_weights) / sizeof(s_weights[0]);
    s_scale = 5040.0;
    init_stencils(s_weights, e2);
  }
};

class cGRADIENT_3D_pbl : public iGRADIENT_OP
{
protected:

  asINT32 s_num_weights;
  static sdFLOAT s_scale;
  static ubFLOAT s_stencils[N_MOVING_STATES][N_AXES];
  static asINT32 s_direction_to_latvec[27] ;

public:

  cGRADIENT_3D_pbl(sdFLOAT e2) {
    sdFLOAT s_weights[][2] = {
      { 2. , 0.},	// 1
      { 1. , 0.},	// 2
    };
    s_num_weights = sizeof(s_weights) / sizeof(s_weights[0]);
    s_scale = 12.0;
    init_stencils(s_weights, e2);
  }

  VOID init_stencils(sdFLOAT s_weights[][2], sdFLOAT e2) {
    sdFLOAT  weights[s_num_weights];

    for (asINT32 i = 0; i < s_num_weights; ++i) {
      weights[i] = s_weights[i][0] + e2 * s_weights[i][1];
    }

    // initialize the direction to lattice vector relation.
    // -1 means it is not a lattice dire

    for (asINT32 latvec = 0, x , y, z;
         (latvec < N_MOVING_STATES) && (x = state_vx(latvec),
                                        y = state_vy(latvec),
                                        z = state_vz(latvec),
                                        TRUE);
         latvec ++) {
      asINT32 direction_index = dir_index(x, y, z); //(x + 1) * 9 + (y + 1) * 3 + (z + 1);
      s_direction_to_latvec[direction_index] = latvec;
    }

    for (asINT32 x = 0; x < 3; ++x) {
      for (asINT32 y = 0; y < 3; ++y) {
        for (asINT32 z = 0; z < 3; ++z) {

          for (asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; ++voxel) {
            asINT32 xv = coord(x, voxel, STP_X_AXIS);
            asINT32 yv = coord(y, voxel, STP_Y_AXIS);
            asINT32 zv = coord(z, voxel, STP_Z_AXIS);

            for (asINT32 center_voxel = 0; center_voxel < ubFLOAT::N_VOXELS; ++center_voxel) {
              //
              // the vector from center to the voxel
              //
              asINT32 xc = xv - coord(1, center_voxel, STP_X_AXIS);
              asINT32 yc = yv - coord(1, center_voxel, STP_Y_AXIS);
              asINT32 zc = zv - coord(1, center_voxel, STP_Z_AXIS);

              asINT32 index = xc * xc + yc * yc + zc * zc;
              asINT32 latvec = s_direction_to_latvec[dir_index(xc,yc,zc)]; //s_direction_to_latvec[(xc + 1) * 9 + (yc + 1) * 3 + zc + 1];
              asINT32 ublk_index = dir_index(x-1, y-1, z-1); //x * 9 + y * 3 + z;

              if (index <= s_num_weights && index > 0) {
                index--;
                s_stencils[latvec][STP_X_AXIS](center_voxel) = weights[index] * xc;
                s_stencils[latvec][STP_Y_AXIS](center_voxel) = weights[index] * yc;
                s_stencils[latvec][STP_Z_AXIS](center_voxel) = weights[index] * zc;

                s_ublk_index[latvec][center_voxel] = ublk_index;
                s_voxel_index[latvec][center_voxel] = voxel;
              }
            }
          }
        }
      }
    }
  }

  VOID neighbor(asINT32 x, asINT32 y, asINT32 z, ubFLOAT &field, ubFLOAT gradient[]) {
    asINT32 direction_index = dir_index(x, y, z); //(x + 1) * 9 + (y + 1) * 3 + (z + 1);
    asINT32 latvec = s_direction_to_latvec[direction_index];

    neighbor3d_pbl(field, &s_stencils[latvec][0], gradient);
  }

  sdFLOAT get_scale() {
    return s_scale;
  }
};

sdFLOAT cGRADIENT_3D_pbl::s_scale;
ubFLOAT cGRADIENT_3D_pbl::s_stencils[N_MOVING_STATES][N_AXES];
asINT32 cGRADIENT_3D_pbl::s_direction_to_latvec[27];

iGRADIENT_OP *iGRADIENT_OP::create(asINT32 num_dims, asINT32 mp_gradient_level, sdFLOAT e2)
{
  enum GRADIENT_OP_TYPE {

    GRADIENT_2D,        // followed by 2D gradient operators
    GRADIENT_2D_19,
    GRADIENT_2D_125,

    GRADIENT_3D,        // followed by 3D gradient operators
    GRADIENT_3D_19,
    GRADIENT_3D_125,

    GRADIENT_3D_pbl        // followed by 3D pbl gradient operators
  } gradient_op_type;

  if (num_dims == 2) {
    gradient_op_type = (GRADIENT_OP_TYPE)(GRADIENT_2D + mp_gradient_level);
    msg_error("2D gradient operator does not work.");
  }
  else {
    gradient_op_type = (GRADIENT_OP_TYPE)(GRADIENT_3D + mp_gradient_level);
  }

  switch (gradient_op_type) {

  case GRADIENT_2D_19:
    return new cGRADIENT_2D_19P(e2);
  case GRADIENT_2D_125:
    return new cGRADIENT_2D_125P(e2);

  case GRADIENT_3D_19:
    return new cGRADIENT_3D_19P(e2);
  case GRADIENT_3D_125:
    return new cGRADIENT_3D_125P(e2);

  case GRADIENT_3D_pbl:
    return new cGRADIENT_3D_pbl(e2);
  default:
    return NULL;
  };
}

asINT32 iGRADIENT_OP::s_ublk_index[N_MOVING_STATES][ubFLOAT::N_VOXELS];
asINT32 iGRADIENT_OP::s_voxel_index[N_MOVING_STATES][ubFLOAT::N_VOXELS];
