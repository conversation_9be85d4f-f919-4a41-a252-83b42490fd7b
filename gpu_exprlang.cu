#include "gpu_exprlang.h"
#include "gpu_globals.hcu"
#include PHYSICS_H

namespace GPU {

//def
__device__ EXPRLANG_DEVICE_DATA* g_exprlang_device_data;
EXPRLANG_MANAGER g_exprlang_mgr;

VOID init_exprlang_mgr() { g_exprlang_mgr.init(); }

EXPRLANG_DEVICE_DATA::EXPRLANG_DEVICE_DATA(size_t max_group_size,
                                           size_t max_vars_size):
  m_current_group_size(0),
  m_current_var_array_size(0),
  m_max_group_size(max_group_size),
  m_max_var_array_size(max_vars_size)
{
  m_var_offset_from_group_index = GPU::malloc<uINT32>(max_group_size, "exprlang");
  m_exprlang_var_mask = GPU::malloc<EXPRLANG_VAR_MASK>(max_group_size, "exprlang");
  m_vars_d = GPU::malloc<dFLOAT>(max_vars_size, "exprlang");
  m_ptr_to_global_device_data = GPU::malloc<EXPRLANG_DEVICE_DATA>(1, "exprlang");  
  GPU::copy_h2d(m_ptr_to_global_device_data, this, 1);
  checkCudaErrors(cudaMemcpyToSymbol(GPU::g_exprlang_device_data,
                                     &m_ptr_to_global_device_data,
                                     sizeof(void*),
                                     0, cudaMemcpyHostToDevice));
}

VOID EXPRLANG_DEVICE_DATA::set_no_space_or_time_varying_data() {
  //Avoid the h2d copy if possible
  if (m_current_var_array_size != 0) {
    m_current_var_array_size = m_current_group_size = 0;
    GPU::copy_h2d(m_ptr_to_global_device_data, this, 1);
  }
}

VOID EXPRLANG_DEVICE_DATA::set_device_data(const std::vector<uINT32>& var_offset_from_group_index,
                                           const std::vector<dFLOAT>& vars_h,
                                           const std::vector<EXPRLANG_VAR_MASK>& var_masks) {

  assert(var_offset_from_group_index.size() <= m_max_group_size);
  assert(vars_h.size() <= m_max_var_array_size);

  m_current_group_size = var_offset_from_group_index.size();
  m_current_var_array_size = vars_h.size();
    
  GPU::copy_h2d(m_var_offset_from_group_index,
                var_offset_from_group_index.data(),
                var_offset_from_group_index.size());

  GPU::copy_h2d(m_vars_d, vars_h.data(), vars_h.size());

  GPU::copy_h2d(m_exprlang_var_mask, var_masks.data(), var_masks.size());
  GPU::copy_h2d(m_ptr_to_global_device_data, this, 1);
  
}

template<typename SHOB_GROUP_TYPE>
void tEXPRLANG_HOST_DATA<SHOB_GROUP_TYPE>::fill_and_port_to_device(sPHYSICS_DESCRIPTOR const*const pd) {

  if (has_space_or_time_varying_var_data()) {

    m_shared_host_buf.clear();
    m_shared_host_buf.reserve(m_vars_size);
    fill(m_group, pd);
      
    EXPRLANG_DEVICE_DATA::get_data()->set_device_data(m_var_offset_from_group_index,
                                                      m_shared_host_buf,
                                                      m_exprlang_var_mask);
  } else {
    EXPRLANG_DEVICE_DATA::get_data()->set_no_space_or_time_varying_data(); 
  }
}

template void tEXPRLANG_HOST_DATA<sMBLK_GROUP>::fill_and_port_to_device(sPHYSICS_DESCRIPTOR const*const pd);
template void tEXPRLANG_HOST_DATA<sMSFL_GROUP>::fill_and_port_to_device(sPHYSICS_DESCRIPTOR const*const pd);

void EXPRLANG_MBLK_IC_DATA::fill_data_for_child_ublk(sHMBLK* ublk,
                               int child_ublk,
                               sPHYSICS_DESCRIPTOR* pd,
                               EXPRLANG_VAR_MASK is_ic_space_varying,
                               VOXEL_IC_DATA& voxel_ic_data,
                               bool debug_centroids) {

  for (int v = 0, mv = N_VOXELS_8 * child_ublk; v < N_VOXELS_8; v++, mv++) {
    STP_GEOM_VARIABLE voxel_centroid[3];
    voxel_centroid[0] = ublk->centroids(mv, 0);
    voxel_centroid[1] = ublk->centroids(mv, 1);
    voxel_centroid[2] = ublk->centroids(mv, 2);
    if(debug_centroids)
    {
      printf("MPI(%d) : voxel_centroid = %f,%f,%f \n", my_proc_id, voxel_centroid[0], voxel_centroid[1], voxel_centroid[2]);
    }
    pd->eval_initial_condition_program(voxel_centroid, NULL, fluid_eqn_error_handler);
    PHYSICS_VARIABLE var = pd->initial_conditions();
    for (int i = 0; i < pd->n_initial_conditions; i++) {
      if (is_ic_space_varying.test(i)) {
        voxel_ic_data[i][mv] = var->value;
      }
      var++;
    } //ic-loop
  }
}

void EXPRLANG_MSFL_DATA::fill_data_for_child_sfl(sMSFL const *const msfl,
                               int child_sfl,
                               sPHYSICS_DESCRIPTOR* pd,
                               const EXPRLANG_VAR_MASK& is_msfl_data_from_eqn,
                               MSURFEL_DATA& msurfel_data) {

  STP_GEOM_VARIABLE sfl_centroid[3];
  v_copy(sfl_centroid, msfl->centroid, child_sfl);

  STP_GEOM_VARIABLE sfl_normal[3];
  v_copy(sfl_normal, msfl->normal, child_sfl);
  switch (m_param_type)
  {
    case PARAM_TYPE::SPACE_AND_TABLE:
      pd->eval_space_and_table_varying_parameter_program(sfl_centroid,
                                                      sfl_normal,
                                                      boundary_eqn_error_handler);
      break;
    case PARAM_TYPE::TIME_AND_SPACE:
      pd->eval_time_and_space_varying_parameter_program(sfl_centroid, sfl_normal,
        ::g_timescale.m_time, ::g_timescale.m_powertherm_time, boundary_eqn_error_handler);
      break;
    case PARAM_TYPE::ALL_SHARABLE:
        //pd already updated and valid for all surfels
      break;
    default:
      assert(false && "Not-defined parameter type yet\n");
      break;
  }

  PHYSICS_VARIABLE var = pd->parameters();
  int data_id=0;
  for (int i = 0; i < pd->n_parameters && data_id < is_msfl_data_from_eqn.count(); i++) {
    if (is_msfl_data_from_eqn.test(i)) {
      msurfel_data[data_id][child_sfl] = var->value;
      data_id++;
    }
    var++;
  } //eqn-loop
}

EXPRLANG_VAR_MASK EXPRLANG_MBLK_IC_DATA::get_exprlang_var_mask_for_mblk(sHMBLK* h_ublk,
                                                                        PD_INDEX_MAP& pd_index_map) {
  EXPRLANG_VAR_MASK mask(0);
  for (int child_ublk = 0; child_ublk < sHMBLK::N_UBLKS; child_ublk++) {
    uINT8 pd_index = (uINT8) -1;
    if (!h_ublk->is_solid(child_ublk) && !h_ublk->is_vr_fine()) {

      sPHYSICS_DESCRIPTOR* pd = nullptr;

      MBLK_VOXEL_MASK dynamics_voxel_mask = h_ublk->fluid_like_voxel_mask;
      if (child_ublk_mask(dynamics_voxel_mask, child_ublk).any())
      {
        pd = (sPHYSICS_DESCRIPTOR*) h_ublk->dynamics_data()->physics_descriptor(child_ublk);
      }
      else
      { //SPECIAL DYNAMICS
        sHMBLK::sUBLK_DYNAMICS_DATA* dynamics_data = h_ublk->dynamics_data();
        
        if (h_ublk->basic_fluid_voxel_mask.any()) {
          dynamics_voxel_mask &= ~h_ublk->basic_fluid_voxel_mask;
          dynamics_data = (sHMBLK::sUBLK_DYNAMICS_DATA*)dynamics_data->next();
        }
        
        while(child_ublk_mask(dynamics_voxel_mask, child_ublk).any()) {
          auto special_dyn_data = (sHMBLK::sSPECIAL_UBLK_DYNAMICS_DATA*) dynamics_data;
          MBLK_VOXEL_MASK special_fluid_voxel_mask = special_dyn_data->voxel_mask();

          if (special_fluid_voxel_mask.test(child_ublk)) 
          {
            pd = dynamics_data->physics_descriptor(child_ublk);       
          }
          dynamics_voxel_mask &= ~special_fluid_voxel_mask;
          dynamics_data = (sHMBLK::sUBLK_DYNAMICS_DATA*)dynamics_data->next();
        }
      }
       
      if (pd)
      {
        EXPRLANG_VAR_MASK pd_mask(0);
        auto it = pd_index_map.find(pd);
        if (it != pd_index_map.end()) {
          pd_index = it->second;
          pd_mask = m_pd_var_mask.at(pd_index);
        } else {
          PHYSICS_VARIABLE var = pd->initial_conditions();          
          for (int i = 0; i < pd->n_initial_conditions; i++) {
            if (var->is_space_varying) {
              pd_mask.set(i);
            }
            var++;
          }
          pd_index = m_pds.size();
          pd_index_map[pd] = pd_index;
          m_pds.push_back(pd);
          m_pd_var_mask.push_back(pd_mask);
        }
        mask |= pd_mask;
      }
    } //if h_ublk is not solid
    m_pd_index.push_back(pd_index);
  }//child ublk loop
    
  return mask;
}

EXPRLANG_VAR_MASK EXPRLANG_MSFL_DATA::get_exprlang_var_mask_for_msfl(sMSFL* msfl,
                                                                        PD_INDEX_MAP& pd_index_map) {
  uINT8 pd_index = (uINT8) -1;
       
  sPHYSICS_DESCRIPTOR* pd = (sPHYSICS_DESCRIPTOR*) msfl->dynamics_data()->physics_descriptor();

  assert( !msfl->is_ghost());
  EXPRLANG_VAR_MASK pd_mask(0);
  auto it = pd_index_map.find(pd);
  if (it != pd_index_map.end()) {
    pd_index = it->second;
    pd_mask = m_pd_var_mask.at(pd_index);
  } else {
    PHYSICS_VARIABLE var = pd->parameters();
    for (int i = 0; i < pd->n_parameters; i++) {
      if (var->is_eqn) { 
        pd_mask.set(i);
      }
      var++;
    }
    pd_index = m_pds.size();
    pd_index_map[pd] = pd_index;
    m_pds.push_back(pd);
    m_pd_var_mask.push_back(pd_mask);
  }
  m_pd_index.push_back(pd_index);

  return pd_mask;
}

void EXPRLANG_MBLK_IC_DATA::init_var_offsets_and_masks(sMBLK_GROUP* g) {

  // printf("MPI(%d) EXPRLANG_MBLK_IC_DATA::init_var_offsets_and_masks() g->m_send_group = %p\n", my_proc_id, g->m_send_group);

  PD_INDEX_MAP pd_index_map;
  auto h_ublk = g->shob_ptrs();
  m_vars_size = 0;
    
  while(h_ublk) {
    EXPRLANG_VAR_MASK is_ic_space_varying = get_exprlang_var_mask_for_mblk(h_ublk, pd_index_map);
    cassert(sLES_FLUID_INITIAL_CONDITIONS::num_of_vars() < 32);
    m_exprlang_var_mask.push_back(is_ic_space_varying);
    m_var_offset_from_group_index.push_back(m_vars_size);
    m_vars_size += N_VOXELS_64 * is_ic_space_varying.count();
    h_ublk = h_ublk->m_next;
  }
}

void EXPRLANG_MSFL_DATA::init_var_offsets_and_masks(sMSFL_GROUP* g) {

  PD_INDEX_MAP pd_index_map;
  auto msfl = g->shob_ptrs();
  m_vars_size = 0;
    
  while(msfl) {
    EXPRLANG_VAR_MASK is_msfl_data_from_eqn = get_exprlang_var_mask_for_msfl(msfl, pd_index_map);
    m_exprlang_var_mask.push_back(is_msfl_data_from_eqn);
    m_var_offset_from_group_index.push_back(m_vars_size);
    m_vars_size += N_SFLS_PER_MSFL * is_msfl_data_from_eqn.count();
    msfl = msfl->m_next;
  }
}
void EXPRLANG_MSFL_DATA::fill(sMSFL_GROUP* g, sPHYSICS_DESCRIPTOR const*const updated_pd)
{
  auto msfl = g->shob_ptrs();
  size_t msfl_index = 0;
  while(msfl) {

    EXPRLANG_VAR_MASK is_msfl_data_from_eqn = m_exprlang_var_mask.at(msfl_index);
    MSURFEL_DATA msurfel_data(is_msfl_data_from_eqn.count(), MSURFEL_VAR_ARRAY{0});
    auto pd_index = m_pd_index.at(msfl_index);
    auto sfl_pd = m_pds.at(pd_index);

    if(sfl_pd == updated_pd)
    {
      for (int child_sfl = 0; child_sfl < N_SFLS_PER_MSFL; child_sfl++) {
        if (msfl->is_valid_child_surfel(child_sfl)) {
            fill_data_for_child_sfl(msfl, child_sfl, sfl_pd, is_msfl_data_from_eqn, msurfel_data);
        } //valid_child test
      } //child_sfl loop
    }
    
    assert(m_shared_host_buf.size() == m_var_offset_from_group_index.at(msfl_index));

    const size_t msfl_var_offset = m_var_offset_from_group_index.at(msfl_index);
    ccDOTIMES(var, is_msfl_data_from_eqn.count())
      ccDOTIMES(s, N_SFLS_PER_MSFL) 
        m_shared_host_buf.push_back(msurfel_data[var][s]);
    
    msfl = msfl->m_next;
    msfl_index++;
  }//msfl loop

  assert(m_shared_host_buf.size() == m_vars_size);

}

void EXPRLANG_MBLK_IC_DATA::fill(sMBLK_GROUP* g, sPHYSICS_DESCRIPTOR const*const updated_pd) {
  
  auto h_ublk = g->shob_ptrs();
  size_t h_ublk_index = 0;

  while(h_ublk) {


    VOXEL_IC_DATA voxel_ic_data(sLES_FLUID_INITIAL_CONDITIONS::num_of_vars(), VOXEL_VAR_ARRAY{0});

      
    EXPRLANG_VAR_MASK is_ic_space_varying = m_exprlang_var_mask[h_ublk_index];

    for (int child_ublk = 0; child_ublk < sHMBLK::N_UBLKS; child_ublk++) {
      if (!h_ublk->is_solid(child_ublk) && !h_ublk->is_vr_fine()) {
        auto pd_index = m_pd_index.at(h_ublk_index*sHMBLK::N_UBLKS + child_ublk);
        auto pd = m_pds.at(pd_index);
        fill_data_for_child_ublk(h_ublk, child_ublk, pd, is_ic_space_varying, voxel_ic_data, false);//my_proc_id==0 && g->m_send_group);
      } //solid test
    } //child_ublk loop

    assert(m_shared_host_buf.size() == m_var_offset_from_group_index[h_ublk_index]);
      
    ccDO_BITSET(var, is_ic_space_varying) {
      ccDOTIMES(v, N_VOXELS_64) {
        m_shared_host_buf.push_back(voxel_ic_data[var][v]);
      }
    }

    h_ublk = h_ublk->m_next;
    h_ublk_index++;
  }//h_mblk loop
  assert(m_shared_host_buf.size() == m_vars_size);
}

VOID EXPRLANG_MANAGER::init() {
    
  size_t max_group_size = 0;
  size_t max_vars_size = 0;
    
  for (int group_type = 0; group_type < N_UBLK_GROUP_TYPES; group_type++) {
    MBLK_FSET mblk_group_fset = g_mblk_groups[group_type];
    if (mblk_group_fset && mblk_group_fset->n_groups()) {
      for (int scale = 0; scale < sim.num_scales; scale++) {        
        DO_MBLK_GROUPS_OF_SCALE_TYPE(g, scale, group_type) {
          auto ic_data = std::make_unique<EXPRLANG_MBLK_IC_DATA>(g);
          max_group_size = std::max(max_group_size, (size_t) g->n_shob_ptrs());
          /*if(group_type == FRINGE_FARBLK_GROUP_TYPE)
            printf("MPI(%d): EXPRLANG_MANAGER::init() scale=%d, group_size = %lu\n", my_proc_id, scale, (size_t)g->n_shob_ptrs());*/
          max_vars_size = std::max(max_vars_size, ic_data->var_array_len());
          const auto [it,success] = m_ic_data.emplace(sUBLK_KEY{static_cast<UBLK_GROUP_TYPE>(group_type),scale,g->m_dest_sp.nsp()}, std::move(ic_data));
          cassert(success);
        }        
      }
    }
  }

  for (SURFEL_GROUP_TYPE group_type : nSHOB_CATEGORIES::DYN_SURFEL_FLOW_TYPES) {
    MSFL_FSET msfl_group_fset = g_msfl_groups[group_type];
    if (msfl_group_fset && msfl_group_fset->n_groups()) {
      for (int scale = 0; scale < sim.num_scales; scale++) {        
        DO_MSFL_GROUPS_OF_SCALE_TYPE(g, scale, group_type) {
          auto sfl_data = std::make_unique<EXPRLANG_MSFL_DATA>(g);
          max_group_size = std::max(max_group_size, (size_t) g->n_shob_ptrs());
          max_vars_size = std::max(max_vars_size, sfl_data->var_array_len());
          const auto [it,success] = m_sfl_data.emplace(sSURFEL_KEY{group_type,scale,g->m_dest_sp.nsp()}, std::move(sfl_data));
          cassert(success);
        }        
      }
    }
  }

  EXPRLANG_DEVICE_DATA::configure(max_group_size, max_vars_size);
}

#if COMPILE_DEBUG_EXPRLANG_DEVICE_METHODS
#if GPU_COMPILER 
__global__ VOID
check_exprlang_device_var_mask(unsigned int msfl_id, int trace_id) {
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  auto mask = d->exprlang_var_mask(msfl_id);
  for (int i = 0; i < 31; i++) {
    if (mask.test(i)) {
      if (i<7 || i>9)
        printf("AAR7 %d-mask_ON %d group %u\n",trace_id, i,msfl_id);
    }
    // else
    // {
    //   printf("AAR7 mask_OFF %d group %d\n",i,msfl_id);
    // }
  }
}
__global__ VOID
check_exprlang_device_var_value(unsigned int msfl_id, int trace_id) {
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  auto mask = d->exprlang_var_mask(msfl_id);
  uINT32 msfl_var_start_index = d->var_start_index(msfl_id);
  auto soxor = get_dyn_soxor();
  uINT32 soxor_var_index = msfl_var_start_index + soxor;
  int n_exprlang_bits = 0;
  for (int i = 0; i < 31; i++) {
    if (mask.test(i)) {
      uINT32 var_index = soxor_var_index + N_SFLS_PER_MSFL * n_exprlang_bits;
      n_exprlang_bits++;
    }
  }
}

__device__ VOID
debug_exprlang_device_var_value(unsigned int msfl_id, int trace_id) {
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  auto mask = d->exprlang_var_mask(msfl_id);
  uINT32 msfl_var_start_index = d->var_start_index(msfl_id);
  auto soxor = get_dyn_soxor();
  uINT32 soxor_var_index = msfl_var_start_index + soxor;
  int n_exprlang_bits = 0;
  for (int i = 0; i < 31; i++) {
    if (mask.test(i)) {
      uINT32 var_index = soxor_var_index + N_SFLS_PER_MSFL * n_exprlang_bits;
      n_exprlang_bits++;
    }
  }
}
__device__ VOID
debug_exprlang_device_var_mask(unsigned int msfl_id, int trace_id) {
  auto d = GPU::EXPRLANG_DEVICE_DATA::get_data();
  auto mask = d->exprlang_var_mask(msfl_id);
  for (int i = 0; i < 31; i++) {
    if (mask.test(i)) {
      if (i<7 || i>9)
        printf("AAR7 %d-mask_ON %d group %u\n",trace_id, i,msfl_id);
    }
    // else
    // {
    //   printf("AAR7 mask_OFF %d group %d\n",i,msfl_id);
    // }
  }
}

#endif
VOID launch_check_exprlang_device_var_mask(unsigned int msfl_id, int trace_id) {

  check_exprlang_device_var_mask<<<1, 1, NO_DYN_SHMEM, g_stream>>>
(msfl_id, trace_id);

cudaStreamSynchronize(g_stream);
checkCudaErrors( cudaPeekAtLastError() );
}
VOID launch_check_exprlang_device_var_value(unsigned int msfl_id, int trace_id) {

  check_exprlang_device_var_value<<<1, 1, NO_DYN_SHMEM, g_stream>>>
(msfl_id, trace_id);

cudaStreamSynchronize(g_stream);
checkCudaErrors( cudaPeekAtLastError() );
}
#endif
}//namespace GPU
