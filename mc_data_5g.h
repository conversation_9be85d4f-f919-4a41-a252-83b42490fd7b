/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created Thurs May 22, 2008
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_MP_DATA_H
#define _SIMENG_MP_DATA_H

#include "common_sp.h"
#include "vectorization_support.h"
#include "shob.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "comm_compression.h"
#include "debug_print_spec.h"
#include "gpu_host_init.h"
#include "mme_ckpt.h"

#define ONLY_ONE_COPY 0

inline namespace SIMULATOR_NAMESPACE {
  
// forward declarations specific to this solver data-block
template <typename UBLK_TYPE_TAG> class tMC_DATA;
template <typename UBLK_TYPE_TAG> class tUBLK_MC_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_MC_DATA;

template<typename SFL_TYPE_TAG> struct tSURFEL_MC_DATA;

template <typename UBLK_TYPE_TAG> class tUBLK_PORE_MC_DATA;
typedef tMC_DATA<UBLK_SDFLOAT_TYPE_TAG> sMC_DATA,         *MC_DATA;
typedef tMC_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBFLOAT_MC_DATA, *UBFLOAT_MC_DATA;
typedef tUBLK_MC_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_MC_DATA,         *UBLK_MC_DATA;
typedef tUBLK_MC_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_MC_DATA, *UBLK_UBFLOAT_MC_DATA;
typedef tNEARBLK_MC_DATA<UBLK_SDFLOAT_TYPE_TAG>  sNEAR_UBLK_MC_DATA,            *NEAR_UBLK_MC_DATA;
typedef tNEARBLK_MC_DATA<UBLK_UBFLOAT_TYPE_TAG>  sNEAR_UBLK_UBFLOAT_MC_DATA,    *NEAR_UBLK_UBFLOAT_MC_DATA;

#ifdef BUILD_GPU
typedef tUBLK_MC_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_MC_DATA,         *MBLK_MC_DATA;
typedef tUBLK_MC_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_MC_DATA, *MBLK_UBFLOAT_MC_DATA;
typedef tNEARBLK_MC_DATA<MBLK_SDFLOAT_TYPE_TAG>  sNEAR_MBLK_MC_DATA,            *NEAR_MBLK_MC_DATA;
typedef tNEARBLK_MC_DATA<MBLK_UBFLOAT_TYPE_TAG>  sNEAR_MBLK_UBFLOAT_MC_DATA,    *NEAR_MBLK_UBFLOAT_MC_DATA;
typedef tSURFEL_MC_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_MC_DATA, *MSFL_MC_DATA;	 
#endif


typedef tUBLK_PORE_MC_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_PORE_MC_DATA,         *UBLK_PORE_MC_DATA;
typedef tUBLK_PORE_MC_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_PORE_MC_DATA, *UBLK_UBFLOAT_PORE_MC_DATA;

template<size_t N_VOXELS>
struct tUBLK_MC_SEND_FIELD

{
  STP_PHYS_VARIABLE m_vel[3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_VOXELS];
  STP_PHYS_VARIABLE m_potential[N_VOXELS];
  VOXEL_STATE       m_states_mc[N_STATES][N_VOXELS];
  STP_PHYS_VARIABLE m_grad_potential[3][N_VOXELS];
};

template<size_t N_VOXELS>
struct tUBLK_MC_MME_SEND_FIELD
{
  STP_PHYS_VARIABLE m_vel[3][N_VOXELS];
  STP_PHYS_VARIABLE m_density[N_VOXELS];
  STP_PHYS_VARIABLE m_potential[N_VOXELS];
};

template<size_t N_VOXELS>
struct tUBLK_PORE_MC_SEND_FIELD
{ //for 5G large_pore
  VOXEL_STATE       m_full_states[N_STATES][N_VOXELS];
  STP_PHYS_VARIABLE m_wall_potential[N_VOXELS];
};

// states data sits at the end of ublk_mc_data
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MC_STATES_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT m_states_mc[N_STATES];

#undef ALIGNED_UBFLOAT
};
typedef tUBLK_MC_STATES_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_MC_STATES_DATA,         *UBLK_MC_STATES_DATA;
typedef tUBLK_MC_STATES_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_MC_STATES_DATA, *UBLK_UBFLOAT_MC_STATES_DATA;

#ifdef BUILD_GPU
typedef tUBLK_MC_STATES_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_MC_STATES_DATA,         *MBLK_MC_STATES_DATA;
typedef tUBLK_MC_STATES_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_MC_STATES_DATA, *MBLK_UBFLOAT_MC_STATES_DATA;

INIT_MBLK(UBLK_MC_STATES_DATA) {
  VEC_COPY_UBLK_TO_MBLK(m_states_mc,N_STATES);
}
#endif



//------------------------------------------------------------------------------
// sUBLK_MC_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tMC_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT vel[3];
  ALIGNED_UBFLOAT density;
  ALIGNED_UBFLOAT potential;

  void print_voxel_data(std::ostream& os,
                        asINT32 print_voxel) {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print<3, N_VOXELS>(os, "vel", vel, loop_limits(0, 2), print_voxel);
    sim_print<N_VOXELS>(os, "density", density, print_voxel);
    sim_print<N_VOXELS>(os, "potential", potential, print_voxel);
  }

#undef ALIGNED_UBFLOAT
};

#ifdef BUILD_GPU
INIT_MBLK(MC_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(vel,3);
  COPY_UBLK_TO_MBLK(density);
  COPY_UBLK_TO_MBLK(potential);
}
#endif

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_MC_DATA
{
  EXTRACT_UBLK_TRAITS
  using sUBLK_MC_STATES_DATA = tUBLK_MC_STATES_DATA<UBLK_TYPE_TAG>;


  using sMC_DATA = tMC_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MC_SEND_FIELD = tUBLK_MC_SEND_FIELD<N_VOXELS>;
  using sUBLK_MC_MME_SEND_FIELD = tUBLK_MC_MME_SEND_FIELD<N_VOXELS>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  sMC_DATA m_mc_data[N_TIME_INDICES];
  ALIGNED_UBFLOAT dynamic_scalar_multiplier;
  ALIGNED_UBFLOAT grad_potential[2][3];

  void print_voxel_data(std::ostream& os,
                        asINT32 print_voxel,
                        asINT32 states_index) {

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_MC_STATE");
#if BUILD_5G_LATTICE
    ccDOTIMES(t_step,N_TIME_INDICES) {
      sim_print_loop_header(os, "TURB_DATA", t_step);
      m_mc_data[t_step].print_voxel_data(os, print_voxel);
      sim_print_vs(os);
    }
    sim_print<2,3, N_VOXELS>(os, "grad_potential", grad_potential, loop_limits(0,1), loop_limits(0, 2), print_voxel);
    if (g_is_multi_component) {
      sim_print<N_LATTICE_VECTORS + 1, N_VOXELS>(os, "mc_states_data", mc_states_data(states_index)->m_states_mc,
                                            loop_limits(0, N_LATTICE_VECTORS), print_voxel);
      sim_print_vs(os);
    }
#endif
  }

  static size_t SIZE(BOOLEAN has_two_copies) {
    if (has_two_copies)
      return (sizeof(tUBLK_MC_DATA) + sizeof(sUBLK_MC_STATES_DATA)*2);
    else
      return (sizeof(tUBLK_MC_DATA) + sizeof(sUBLK_MC_STATES_DATA));
  }

  sUBLK_MC_STATES_DATA *mc_states_data(asINT32 timestep_index) {
    return ((sUBLK_MC_STATES_DATA *)
            ((char *)this + timestep_index*sizeof(sUBLK_MC_STATES_DATA) + sizeof(tUBLK_MC_DATA)));
  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_MC_DATA *dest_ublk_mc_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_MC_DATA* d = dest_ublk_mc_data;   
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDOTIMES(axis, 3) {
        d->m_mc_data[timestep].vel[axis][dest_voxel] = m_mc_data[timestep].vel[axis][source_voxel];
      }
      d->m_mc_data[timestep].density[dest_voxel]     = m_mc_data[timestep].density[source_voxel];
      d->m_mc_data[timestep].potential[dest_voxel]   = m_mc_data[timestep].potential[source_voxel];
    }
    ccDOTIMES(timestep, 2) {
      ccDOTIMES(axis, 3) {
        d->grad_potential[timestep][axis][dest_voxel] = grad_potential[timestep][axis][source_voxel];
      }
    }
  }

  // @@@ vectorize this
  VOID explode_voxel(tUBLK_MC_DATA *fine_ublk_mc_data, asINT32 voxel_to_explode, asINT32 scale)
  {
#ifdef MERGE_VINIT_5G
    asINT32 prior_timestep = sim.lb_tm.prior_timestep_index(scale);
    UBLK_MC_DATA d = fine_ublk_mc_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDOTIMES(voxel, 8) {
        ccDOTIMES(axis, 3) {
          d->m_mc_data[timestep].vel[axis][voxel]  = m_mc_data[prior_timestep].vel[][axis][voxel_to_explode];
        }
        d->m_mc_data[timestep].density[voxel]      = m_mc_data[prior_timestep].density[voxel_to_explode];
        d->m_mc_data[timestep].potential[voxel]    = m_mc_data[prior_timestep].potential[voxel_to_explode];
      }
    }
    ccDOTIMES(timestep, 2) {
      ccDOTIMES(voxel, 8) {
        ccDOTIMES(axis, 3) {
          d->grad_potential[timestep][axis][voxel]  = grad_potential[prior_timestep][axis][voxel_to_explode];
        }
      }
    }
#endif
  }

  VOID copy_voxel_mme_data(tUBLK_MC_DATA *dest_ublk_mc_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_MC_DATA* d = dest_ublk_mc_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDOTIMES(axis, 3) {
        d->m_mc_data[timestep].vel[axis][dest_voxel]  = m_mc_data[timestep].vel[axis][source_voxel];
      }
      d->m_mc_data[timestep].density[dest_voxel]      = m_mc_data[timestep].density[source_voxel];
      d->m_mc_data[timestep].potential[dest_voxel]    = m_mc_data[timestep].potential[source_voxel];
    }
  }
  

  VOID explode_voxel_mme(tUBLK_MC_DATA *fine_ublk_mc_data, asINT32 voxel_to_explode,
                         SOLVER_INDEX_MASK solver_ts_index_mask)
  {
    asINT32 coarse_timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    msg_internal_error("Only one scale is supported for multiphase simulations");
    asINT32 fine_timestep_index = 0; // explode_voxel_mme happens on odd fine timesteps which is always 0
    tUBLK_MC_DATA* d = fine_ublk_mc_data;
    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDOTIMES(voxel, N_VOXELS) {
        ccDOTIMES(axis, 3) {
          d->m_mc_data[fine_timestep_index].vel[axis][voxel] = m_mc_data[coarse_timestep_index].vel[axis][voxel_to_explode];
        }
        d->m_mc_data[fine_timestep_index].density[voxel]     = m_mc_data[coarse_timestep_index].density[voxel_to_explode];
        d->m_mc_data[fine_timestep_index].potential[voxel]   = m_mc_data[coarse_timestep_index].potential[voxel_to_explode];
      }
    }
  }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  
  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas) {}
  
  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size      += ( sizeof(sUBLK_MC_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += ( sizeof(sUBLK_MC_SEND_FIELD) / sizeof(sdFLOAT));
  }

  static VOID add_mme_send_size(asINT32 &send_size) {
    send_size      += ( sizeof(sUBLK_MC_MME_SEND_FIELD) / sizeof(sdFLOAT));
  }

#ifdef MERGE_VINIT_COMPRESSION
   // revive the neighbor mask to carry out comm compression later
   // Why do we copy velocity, density from the first set?
#endif
  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
    sUBLK_MC_SEND_FIELD* field = reinterpret_cast<sUBLK_MC_SEND_FIELD*>(send_data_info.send_buffer);
    memcpy(field, &(m_mc_data[ONLY_ONE_COPY]), struct_field_disp(sUBLK_MC_SEND_FIELD*, m_states_mc));
    memcpy(field->m_grad_potential, grad_potential[timestep_index], sizeof(field->m_grad_potential));
    memcpy(field->m_states_mc, mc_states_data(timestep_index)->m_states_mc,
           struct_field_size(sUBLK_MC_SEND_FIELD*, m_states_mc));

    //g_comm_compression_info.fill_by_recipe(send_buffer, (VOXEL_STATE *)states,  nmi);
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {

    asINT32 timestep_index = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
    asINT32 state_timestep_index = timestep_index;
# else
    asINT32 state_timestep_index = ONLY_ONE_COPY;
#endif
    sUBLK_MC_SEND_FIELD* field = reinterpret_cast<sUBLK_MC_SEND_FIELD*>(recv_data_info.recv_buffer);
    memcpy(grad_potential[timestep_index], field->m_grad_potential, sizeof(field->m_grad_potential));
    //grad_potential has 2 time indices
    timestep_index = 0;
    memcpy(&(m_mc_data[timestep_index]), field, struct_field_disp(sUBLK_MC_SEND_FIELD*, m_states_mc));
    memcpy(mc_states_data(state_timestep_index)->m_states_mc, field->m_states_mc,
           struct_field_size(sUBLK_MC_SEND_FIELD*, m_states_mc));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = 0;
    sUBLK_MC_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_MC_MME_SEND_FIELD*>(send_buffer);
    memcpy(field->m_vel,       m_mc_data[timestep_index].vel,       sizeof(field->m_vel));
    memcpy(field->m_density,   m_mc_data[timestep_index].density,   sizeof(field->m_density));
    memcpy(field->m_potential, m_mc_data[timestep_index].potential, sizeof(field->m_potential));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, SOLVER_INDEX_MASK solver_ts_index_mask) {
    sUBLK_MC_MME_SEND_FIELD* field = reinterpret_cast<sUBLK_MC_MME_SEND_FIELD*>(recv_buffer);
    asINT32 timestep_index = 0;
    memcpy(m_mc_data[timestep_index].vel,       field->m_vel,       sizeof(field->m_vel));
    memcpy(m_mc_data[timestep_index].density,   field->m_density,   sizeof(field->m_density));
    memcpy(m_mc_data[timestep_index].potential, field->m_potential, sizeof(field->m_potential));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID reflect_velocity(STP_PHYS_VARIABLE mirror_sign_factor[N_AXES], VOXEL_NUM voxel) {
    ccDOTIMES(scheme, N_TIME_INDICES) {
      ccDOTIMES(axis, N_AXES) {
        m_mc_data[scheme].vel[axis][voxel] *= mirror_sign_factor[axis];
      }
    }
  }
#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(UBLK_MC_DATA) 
{
  VEC_COPY_UBLK_TO_MBLK(m_mc_data,N_TIME_INDICES);
  COPY_UBLK_TO_MBLK(dynamic_scalar_multiplier);
  COPY_UBLK_TO_MBLK(mc_states_data(0));

}
#endif

//------------------------------------------------------------------------------
// sUBLK_PORE_MC_DATA for 5G large_pore
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_PORE_MC_DATA
{
  EXTRACT_UBLK_TRAITS

  using sUBLK_PORE_MC_SEND_FIELD = tUBLK_PORE_MC_SEND_FIELD<N_VOXELS>;
 
#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  ALIGNED_UBFLOAT resistance[3];
  ALIGNED_UBFLOAT pm_residual;
  ALIGNED_UBFLOAT wall_potential;
  ALIGNED_UBFLOAT full_states[N_STATES];

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this); }
  uINT64 ckpt_len() { return sizeof(*this); }

  VOID init() {  //looks like no use
    memset(this, 0, sizeof(*this));
  }

  static VOID add_pore_send_size(asINT32 &send_size) {
     send_size      += (sizeof(tUBLK_PORE_MC_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }

  VOID fill_pore_send_buffer(sdFLOAT* &send_buffer) {
    auto* field = reinterpret_cast<sUBLK_PORE_MC_SEND_FIELD*>(send_buffer);
    memcpy(field->m_full_states, full_states, sizeof(field->m_full_states));
    memcpy(field->m_wall_potential, wall_potential, sizeof(field->m_wall_potential));
    field++;
    send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_pore_recv_buffer(sdFLOAT* &recv_buffer) {
    auto* field = reinterpret_cast<sUBLK_PORE_MC_SEND_FIELD*>(recv_buffer);
    memcpy(full_states, field->m_full_states, sizeof(field->m_full_states));
    memcpy(wall_potential, field->m_wall_potential, sizeof(field->m_wall_potential));
    field++;
    recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

#undef ALIGNED_UBFLOAT
};

//------------------------------------------------------------------------------
// sNEARBLK_MC_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_MC_DATA
{
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT srf_potential[N_MOVING_STATES];
  
  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){
     //We leave this blank for now
  }

  // @@@ vectorize this  //All NEARBLK_MC_DATA are used locally
  VOID explode_voxel(tNEARBLK_MC_DATA *fine_ublk_mc_data, asINT32 voxel_to_explode, asINT32 scale) 
  {}
  
  VOID explode_voxel_mme(tNEARBLK_MC_DATA *fine_ublk_mc_data, asINT32 voxel_to_explode, asINT32 scale) {}
  
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  //static VOID define_send_mpi_type(MPI_Datatype *eMPI_type);
  //static VOID define_recv_mpi_type(MPI_Datatype *eMPI_type);

  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size)
  {}

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index)
  {}
  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, asINT32 timestep_index)
  {}

#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_MC_DATA) 
{
  VEC_COPY_UBLK_TO_MBLK(srf_potential,N_MOVING_STATES);
}
#endif

// SURFEL_DATA
typedef struct sSURFEL_MC_SEND_FIELD
{
  SURFEL_STATE       m_out_flux[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE  m_density_pair;
  STP_PHYS_VARIABLE  m_momentum_pair[3];
  STP_PHYS_VARIABLE  m_rho_bar[N_SURFEL_PGRAM_VOLUMES];
  STP_PHYS_VARIABLE  m_rho_bar_ratio;
  STP_PHYS_VARIABLE  m_u_bar_ratio;
  STP_PHYS_VARIABLE  m_density;
  STP_PHYS_VARIABLE  m_potential;
} *SURFEL_MC_SEND_FIELD;

//------------------------------------------------------------------------------
// sSURFEL_MC_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_MC_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N>  potential;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  density_pair;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  momentum_pair[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  rho_bar_ratio;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  u_bar_ratio;  
  tSFL_VAR<SURFEL_STATE, N>  out_flux[N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<SURFEL_STATE, N> in_states[N_SURFEL_PGRAM_VOLUMES];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  density;
  tSFL_VAR<STP_PHYS_VARIABLE, N>  mss_home_voxel_density_ratio; 
  tSFL_VAR<STP_PHYS_VARIABLE, N>  momentum[3];
  tSFL_VAR<STP_PHYS_VARIABLE, N>  rho_bar[N_SURFEL_PGRAM_VOLUMES];

  uINT8 mss_home_voxel_surfel; // tag surfels in mss region. initialized as 0. you don't want it be cleaned

  VOID print_surfel_data(std::ostream& os) {
    using namespace UBLK_SURFEL_PRINT_UTILS;
    sim_print_data_header(os, "SURFEL_MC_DATA");
    sim_print(os, "potential", potential);
    sim_print(os, "density_pair", density_pair);
    sim_print<3>(os, "momentum_pair", momentum_pair);
    sim_print(os, "rho_bar_ratio", rho_bar_ratio);
    sim_print(os, "u_bar_ratio", u_bar_ratio);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "out_flux", out_flux);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "in_states", in_states);
    sim_print_vs(os);
    sim_print(os, "density", density);
    sim_print(os, "mss_home_voxel_density_ratio", mss_home_voxel_density_ratio);
    sim_print<3>(os, "momentum", momentum);
    sim_print_vs(os);
    sim_print<N_SURFEL_PGRAM_VOLUMES>(os, "rho_bar", rho_bar);
  }

#define LAST_FIELD_CLEARED rho_bar

  VOID pre_advect_init() {
    memset(this->in_states, 0, (struct_field_disp(tSURFEL_MC_DATA*, LAST_FIELD_CLEARED)
                                + sizeof(LAST_FIELD_CLEARED)
                                - struct_field_disp(tSURFEL_MC_DATA*, in_states)));
  }

#undef LAST_FIELD_CLEARED

  VOID pre_advect_init_copy_even_to_odd(tSURFEL_MC_DATA *even_surfel_mc_data, 
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_MC_DATA *even_surfel_mc_data,
                             asINT32 esoxor, asINT32 osoxor) {
    ccDOTIMES(j, N_SURFEL_PGRAM_VOLUMES) {
      in_states[j][osoxor] = even_surfel_mc_data->in_states[j][esoxor];
    }    
  }

  static VOID define_send_mpi_type(MPI_Datatype *eMPI_type);
  static VOID define_recv_mpi_type(MPI_Datatype *eMPI_type);

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_MC_SEND_FIELD field = reinterpret_cast<SURFEL_MC_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(&field->m_out_flux, out_flux, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_out_flux));
    memcpy(&field->m_density_pair, &density_pair, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_density_pair));
    memcpy(&field->m_momentum_pair, momentum_pair, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_momentum_pair)); 
    memcpy(&field->m_rho_bar, rho_bar, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_rho_bar));
    memcpy(&field->m_rho_bar_ratio, &rho_bar_ratio, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_rho_bar_ratio));
    memcpy(&field->m_u_bar_ratio, &u_bar_ratio, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_u_bar_ratio));
    memcpy(&field->m_density, &density, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_density));
    memcpy(&field->m_potential, &potential, 
           struct_field_size(SURFEL_MC_SEND_FIELD,m_potential));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT *>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_MC_SEND_FIELD field = reinterpret_cast<SURFEL_MC_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(out_flux, &field->m_out_flux,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_out_flux));
    memcpy(&density_pair, &field->m_density_pair,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_density_pair));
    memcpy(momentum_pair, &field->m_momentum_pair,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_momentum_pair));
    memcpy(rho_bar, field->m_rho_bar,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_rho_bar));
    memcpy(&rho_bar_ratio, &field->m_rho_bar_ratio,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_rho_bar_ratio));
    memcpy(&u_bar_ratio, &field->m_u_bar_ratio,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_u_bar_ratio));
    memcpy(&density, &field->m_density,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_density));
    memcpy(&potential, &field->m_potential,
           struct_field_size(SURFEL_MC_SEND_FIELD,m_potential));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT *>(field);
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size      += (sizeof(sSURFEL_MC_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_MC_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
  }

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }

  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_MC_DATA *mirror_mc_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_MC_DATA *source_mc_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
                                                 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
};

using sSURFEL_MC_DATA = tSURFEL_MC_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_MC_DATA = sSURFEL_MC_DATA*;

template<typename SFL_TYPE_TAG>
struct tSURFEL_S2S_MC_DATA
{
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<SURFEL_STATE,N> m_out_flux[N_S2S_TIME_INDICES][N_SURFEL_PGRAM_VOLUMES];

  uINT64 ckpt_len() { return sizeof(*this); }
  VOID read_ckpt()  { read_lgi(*this);      }
  VOID write_ckpt() { write_ckpt_lgi(*this);}

};


#if BUILD_GPU
INIT_MSFL(tSURFEL_MC_DATA);
#endif

static const asINT32 SURFEL_MC_DATA_CKPT_SIZE = sizeof(sSURFEL_MC_DATA);

//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_MC_DATA
//-----------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_MC_DATA
{
  STP_PHYS_VARIABLE  density;
  STP_PHYS_VARIABLE  vel[3];
  STP_PHYS_VARIABLE  mass_flux;

  VOID pre_advect_init() {
    memset(this, 0, sizeof(*this));
  }

  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_MC_DATA *even_surfel_mc_data);
  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

} *SAMPLING_SURFEL_MC_DATA;

} //inline namespace SIMULATOR_NAMESPACE
#endif//_SIMENG_MC_SOLVER_DATA_H
