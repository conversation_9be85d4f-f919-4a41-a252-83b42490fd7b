/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Strands that operate on farblks
 *
 * Vinit Gupta, Exa Corporation
 * Created Fri Sep 4, 2014
 *--------------------------------------------------------------------------*/

#include "shob_groups.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "strands.h"
#include "sleep.h"
#include "status.h"
#include "bsurfel_comm.h"
#include "ublk_process_control.h"
#include PHYSICS_H

#define DEBUG_ASM 0

#include "sp_timers.h"


#define TIME_FFA 0
VOID sFRINGE_FARBLKS_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  SCALE finest_scale = FINEST_SCALE;

  for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {

    m_ublk_process_control.init(scale);
    m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_FRINGE_FARBLK_GROUP_TYPE);
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);

    // updating consumer counts for the finer scale prevents race conditions at vr interfaces
    if ( scale != finest_scale ) {
      g_strand_mgr.update_consumer_counts(scale+1);
    }
  }
  g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale);

  g_strand_mgr.complete_timestep();
}

VOID sFRINGE_FARBLKS_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    SCALE finest_scale = FINEST_SCALE;


    for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
      if ( scale != finest_scale ) {
        g_strand_mgr.update_consumer_counts(scale+1);
      }
    }
    g_strand_mgr.update_consumer_counts(strand_coarsest_active_scale);

    if (sim.is_movb_sim()) {
      for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
        request_ublk_ib_bf_bcast_sends(scale,false);
      }
    }
  }
  g_strand_mgr.complete_timestep();

}

#if BUILD_5G_LATTICE
VOID sFRINGE_FARBLKS_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    SCALE finest_scale = FINEST_SCALE;

    for (asINT32 scale = finest_scale; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif

VOID sINTERIOR_FARBLKS2_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_ublk_process_control.init(scale);
    WITH_TIMER(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER) {
      m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_FARBLK_GROUP_TYPE);
    }
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
    g_strand_mgr.update_consumer_counts(scale);
  }
  g_strand_mgr.complete_timestep();
}

VOID sINTERIOR_FARBLKS2_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sINTERIOR_FARBLKS2_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif

VOID sINTERIOR_FARBLKS1_A_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    m_ublk_process_control.init(scale);
    m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
    g_strand_mgr.update_consumer_counts(scale);
  }
  g_strand_mgr.complete_timestep();
}


VOID sINTERIOR_FARBLKS1_B_STRAND::run() {
  if (sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sINTERIOR_FARBLKS1_C_STRAND::run() {
  if (sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif
