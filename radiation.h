#ifndef EXA_SIMENG_RADIATION_H
#define EXA_SIMENG_RADIATION_H

#include PHYSTYPES_H
#include <memory>

#include "simulator_namespace.h"
#include "shob.h"

class cRADIATION_PATCHES_IMPL;
struct sRADIATION_PATCH_FSET;

class cRADIATION_PATCHES
{

public:
  void init(STP_PROC num_rps);

  void register_surfel(sSURFEL* surfel, SHOB_ID patch_id, RP_PROC rp, bool backside);

  void do_initial_rp_comm();

  // after calling this function, this class dies and cannot be used anymore
  void build_radiation_fset(sRADIATION_PATCH_FSET& send_fset);

  // Both the constructor and destructor need to be in the cc file to avoid
  // compilation error with unique_ptr and the pimpl idiom.
  // It prevents the compiler from doing template instantiations where it
  // doesn't know what cRADIATION_PATCHES_IMPL is.
  cRADIATION_PATCHES();
  ~cRADIATION_PATCHES();

private:

  std::unique_ptr<cRADIATION_PATCHES_IMPL> m_pimpl;

};

struct sRADIATION_SURFACE_CONDITION
{
  sPHYSICS_VARIABLE m_emissivity;
  std::string m_name;
  EXPRLANG_PROGRAM m_parameter_program;
  cBOOLEAN m_parameter_time_varying;
  cBOOLEAN m_parameter_time_varying_only;
  cBOOLEAN m_parameter_time_and_space_varying;
  cBOOLEAN m_parameter_sharable;
  cBOOLEAN m_constant_parameter_in_need_of_eval;

public: 

  sRADIATION_SURFACE_CONDITION() = default;

  sRADIATION_SURFACE_CONDITION(const std::string& name, EXPRLANG_PROGRAM case_program) :
    m_name(name),
    m_parameter_program(case_program),
    m_parameter_time_varying(FALSE),
    m_parameter_time_varying_only(FALSE),
    m_parameter_time_and_space_varying(FALSE),
    m_parameter_sharable(TRUE),
    m_constant_parameter_in_need_of_eval(FALSE)
  {}

  VOID eval_space_and_table_varying_parameter_program(STP_GEOM_VARIABLE *point, STP_GEOM_VARIABLE normal[3], EQN_ERROR_HANDLER h);

};

extern cRADIATION_PATCHES g_radiation_patches;

#endif
