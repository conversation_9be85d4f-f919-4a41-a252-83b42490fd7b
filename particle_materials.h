#ifndef _PARTICLE_MATERIALS_H_
#define _PARTICLE_MATERIALS_H_

#include "particle_random_properties.h"
#include "particle_solver_data.h"
#include "bsurfel.h"
#include "sampling_surfel.h"

//---- Particle Material -----
typedef enum {LIQUID, SOLID, MASSLESS_TRACER, NUM_MATERIAL_TYPES} sMATERIAL_TYPES;
typedef class sPARTICLE_MATERIAL{
 private:
  std::string m_name;
  asINT32 m_id;
  sMATERIAL_TYPES m_material_type;
  sdFLOAT m_density_mean; //This name was chosen when only gaussian or unifomr distributinos were supported, now that others have been added, a more generic name like param1 would be more accurate.
  sdFLOAT m_density_dist_param2; //This may represent the range, or the standard deviation, or something else, depending on the distribution type.
  sPDF::ePDF_TYPE m_density_distribution;
  sdFLOAT m_dynamic_viscosity;
  sdFLOAT m_surface_tension;
  BOOLEAN m_breakup_allowed;
  friend class sDGF_READER;
 public:

  const char* name(){return(m_name.c_str());}
  asINT32 id() {return(m_id);}
  sMATERIAL_TYPES material_type() {return(m_material_type);};
  BOOLEAN is_liquid() {return(m_material_type == LIQUID);}
  BOOLEAN is_solid() {return(m_material_type == SOLID);}
  BOOLEAN is_massless_tracer() {return(m_material_type == MASSLESS_TRACER);}
  BOOLEAN breakup_allowed(){return(m_breakup_allowed);}
  sdFLOAT expected_material_density() { return m_density_mean; }
  sdFLOAT surface_tension_sigma() {return(m_surface_tension);} 
  BOOLEAN is_accretion_material(asINT32 accretion_material_index){return(m_id == accretion_material_index);} 


  sPDF::ePDF_TYPE density_distribution() {return m_density_distribution;}
  dFLOAT density_distribution_param1() { return m_density_mean; }
  dFLOAT density_distribution_param2() { return m_density_dist_param2; }

  sPARTICLE_MATERIAL(std::string &name) { 
      m_name = name;
  }

  VOID sample_material_properties(sPARTICLE_VAR &density, sPARTICLE_VAR &kinematic_viscosity, sPARTICLE_VAR &surface_tension );

} *PARTICLE_MATERIAL;


//----- Surface interaction parametes.
typedef class sSURFACE_INTERACTION_PROPERTY {

 private:
  char *m_name;
  asINT32 m_particle_material_id;
  asINT32 m_surface_material_id;
 
  sdFLOAT m_normal_restitution_coefficient_mean;
  sdFLOAT m_normal_restitution_coefficient_stddev;
  sPDF::ePDF_TYPE m_normal_restitution_coefficient_distribution;
  sdFLOAT m_tangential_restitution_coefficient_mean;
  sdFLOAT m_tangential_restitution_coefficient_stddev;
  sPDF::ePDF_TYPE m_tangential_restitution_coefficient_distribution;
  sdFLOAT m_scatter_angle_stddev;
  sPDF::ePDF_TYPE m_scatter_angle_distribution;

  BOOLEAN m_splash_model_enabled;
  BOOLEAN m_reentrainment_model_enabled;
  sdFLOAT m_reentrainment_length;


  friend class sDGF_READER;

  BOOLEAN generate_surface_realization_internal(STP_GEOM_VARIABLE surfel_normal[3], 
                                                dFLOAT basis[3],
                                                PARTICLE_VAR normal_restitution_coefficent, 
                                                PARTICLE_VAR transverse_restitution_coefficient, 
                                                sPARTICLE_VAR scattered_normal[3]);
  
 public:

  sdFLOAT m_reflection_enabled;
  sdFLOAT m_reflection_min_momentum;
  sdFLOAT m_reflection_min_normal_velocity;
  sdFLOAT m_reflection_min_angle;
  BOOLEAN m_disable_film_solver;


  sSURFACE_INTERACTION_PROPERTY(asINT32 name_length, LGI_PARTICLE_SURFACE_MATERIAL_REC record, char * name) {
    m_name = name;
    m_reflection_min_momentum = record.reflect_min_momentum;
    m_reflection_min_normal_velocity = record.reflect_min_normal_vel;
    m_reflection_min_angle = record.reflect_min_angle;
    m_reflection_enabled = record.reflection_enabled;
    m_normal_restitution_coefficient_mean = record.normal_rest_coeff;
    m_normal_restitution_coefficient_stddev = 0.0;
    m_normal_restitution_coefficient_distribution = lgi_to_pdfs_distribution(LGI::DISTRIBUTION_UNIFORM);
    m_tangential_restitution_coefficient_mean = record.tang_restitution_coeff;
    m_tangential_restitution_coefficient_stddev = 0.0;
    m_tangential_restitution_coefficient_distribution = lgi_to_pdfs_distribution(LGI::DISTRIBUTION_UNIFORM);
    m_scatter_angle_stddev = record.scatter_angle_range;
    m_scatter_angle_distribution = lgi_to_pdfs_distribution(record.scatter_angle_distribution);
    m_splash_model_enabled = record.splash_model_enabled;
    m_reentrainment_model_enabled = record.reentrainment_allowed;
    m_reentrainment_length = record.reentrainment_length;
    m_particle_material_id = record.particle_material_id;
    m_surface_material_id = record.surface_material_id;
    m_disable_film_solver = record.disable_film_solver;
  }
  ~sSURFACE_INTERACTION_PROPERTY() {}

  char* name(){return m_name;}
  BOOLEAN disintegrate_on_impact(){return FALSE;} //hit points only  //{return(!m_splash_model_enabled && !m_reentrainment_model_enabled );}
  BOOLEAN elastic_reflection(){return m_reflection_enabled && !m_splash_model_enabled && !m_reentrainment_model_enabled ;}
  BOOLEAN film_advection(){return (m_splash_model_enabled || m_reentrainment_model_enabled) || !m_reflection_enabled;}
  BOOLEAN splash_allowed(){return m_splash_model_enabled;}
  BOOLEAN reentrainment_allowed(){return (m_reentrainment_model_enabled || m_disable_film_solver);}
  sdFLOAT reentrainment_length(){return m_reentrainment_length;}
  template <typename SURFEL_TYPE>
  BOOLEAN generate_surface_realization(SURFEL_TYPE surfel, 
                                       PARTICLE_VAR normal_restitution_coefficent,
                                       PARTICLE_VAR transverse_restitution_coefficient,
                                       sPARTICLE_VAR normal[3]);

}*SURFACE_INTERACTION_PROPERTY;

#endif
