/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * New advection scheme involving swapping of states with neighboring ublks
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Vinit Gupta, Exa Corporation                      Created Wed, Aug 30, 2017
//----------------------------------------------------------------------------
//
#include "box_advect.h"

#if BUILD_AVX2 && !(BUILD_DOUBLE_PRECISION)
#include BOX_SWAP_NEIGHBORS_AVX_2D
#include BOX_SWAP_NEIGHBORS_AVX_3D
#else
#include BOX_SWAP_NEIGHBORS_2D
#include BOX_SWAP_NEIGHBORS_3D
#endif

inline namespace ADVECT_NAMESPACE {

template <BOOLEAN is_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_TWO_COPY_NBR>
INLINE VOID swap_advect(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
		        UBLK (&active_nbrs) [N_MOVING_STATES],
			BOOLEAN is_timestep_even,
			SOLVER_INDEX_MASK prior_solver_index_mask,
			ACTIVE_SOLVER_MASK active_solver_mask) {

  const auto& box_access = pde_advect_ublk->m_box_access;

  VOXEL_STATE (*states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(ONLY_ONE_COPY)->m_states);
  VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS] = NULL;
  if (ADVECT_TEMP) {
    states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t);
  }

  VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
      states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds);
  }

  VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS] = NULL;
#if BUILD_5G_LATTICE
  if (g_is_multi_component) {
    states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc);
  }
#endif

#if BUILD_D39_LATTICE
#if BUILD_AVX2 && !(BUILD_DOUBLE_PRECISION)
  if (is_2D){
    d39_avx_swap_advect_states_2D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
      (pde_advect_ublk,active_nbrs,is_timestep_even,
       states,states_t,states_mc,states_uds,
       box_access,
       prior_solver_index_mask,
       active_solver_mask);
  } else {
    d39_avx_swap_advect_states_3D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
      (pde_advect_ublk,active_nbrs,is_timestep_even,
       states,states_t,states_mc,states_uds,
       box_access,
       prior_solver_index_mask,
       active_solver_mask);
  }
#else
  if (is_2D){
    d39_swap_advect_states_2D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
      (pde_advect_ublk,active_nbrs,is_timestep_even,
       states,states_t,states_mc,states_uds,
       box_access,
       prior_solver_index_mask,
       active_solver_mask);
  } else {
    d39_swap_advect_states_3D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
      (pde_advect_ublk,active_nbrs,is_timestep_even,
       states,states_t,states_mc,states_uds,
       box_access,
       prior_solver_index_mask,
       active_solver_mask);
  }
#endif
#else
#if BUILD_AVX2 && !(BUILD_DOUBLE_PRECISION)
  
  if (is_2D){
    d19_avx_swap_advect_states_2D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
                                          (pde_advect_ublk,active_nbrs,is_timestep_even,
                                          states,states_t,states_mc,states_uds,
                                          box_access,
                                          prior_solver_index_mask,
                                          active_solver_mask);
  } else {
    d19_avx_swap_advect_states_3D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
                                          (pde_advect_ublk,active_nbrs,is_timestep_even,
                                          states,states_t,states_mc,states_uds,
                                          box_access,
                                          prior_solver_index_mask,
                                          active_solver_mask);
  }

#else
  if (is_2D){
    d19_swap_advect_states_2D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
                                          (pde_advect_ublk,active_nbrs,is_timestep_even,
                                          states,states_t,states_mc,states_uds,
                                          box_access,
                                          prior_solver_index_mask,
                                          active_solver_mask);
  } else {
    d19_swap_advect_states_3D<ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR>
                                          (pde_advect_ublk,active_nbrs,is_timestep_even,
                                          states,states_t,states_mc,states_uds,
                                          box_access,
                                          prior_solver_index_mask,
                                          active_solver_mask);
  }
#endif
  
#endif
  pde_advect_ublk->toggle_advected();
}

template VOID swap_advect<FALSE, FALSE, FALSE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE, FALSE, FALSE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE,  TRUE, FALSE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE,  TRUE, FALSE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE, FALSE, TRUE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE, FALSE, TRUE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE,  TRUE, TRUE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE,  TRUE, TRUE, TRUE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

//---

template VOID swap_advect<FALSE, FALSE, FALSE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE, FALSE, FALSE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE,  TRUE, FALSE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE,  TRUE, FALSE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					       UBLK (&active_nbrs) [N_MOVING_STATES],
					       BOOLEAN is_timestep_even,
					       SOLVER_INDEX_MASK prior_solver_index_mask,
					       ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE, FALSE, TRUE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE, FALSE, TRUE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect<FALSE,  TRUE, TRUE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);

template VOID swap_advect< TRUE,  TRUE, TRUE, FALSE>(sUBLK *pde_advect_ublk, VOXEL_MASK_8 voxel_mask,
					      UBLK (&active_nbrs) [N_MOVING_STATES],
					      BOOLEAN is_timestep_even,
					      SOLVER_INDEX_MASK prior_solver_index_mask,
					      ACTIVE_SOLVER_MASK active_solver_mask);
	 
} //ADVECT_NAMESPACE
