#ifndef _READ_LGI_SP_H_
#define _READ_LGI_SP_H_

#include CDI_H
#include LGI_H

#include <string>
#include <cstring> //for memset
#include <vector>


typedef class sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER : public sLGI_PARTICLE_EMITTER_CONFIGURATION_REC 
{
 public:

  //Define some methods that can populate this structure from an LGI stream and consume the required string chars.
  VOID read();
  VOID read_common_strings(std::string &name, std::string &emission_rate_variable_name); //read the string data common to all config types from the lgi stream
  VOID read_nozzle_strings();
  VOID read_rain_strings(std::string &wind_x_name,
                         std::string &wind_y_name,
                         std::string &wind_z_name);
  VO<PERSON> read_tire_strings(std::string &angular_emission_dist_parameter_variable_name,
                         std::string &angular_emission_dist_ratio_variable_name);

}* LGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER;



typedef class sLGI_NOZZLE_CONFIGURATION_REC_READER : public sLGI_NOZZLE_CONFIGURATION_REC
{
 public:
  //Declare some methofs to read into this record from an LGI stream and to consume the proper number of string characters.
  VOID read(); //read the fixed size data
  VOID read_common_strings( std::string &mean_diameter_varaible_name, std::string &diameter_range_varaible_name); //Read the strings common to all configuration types
  VOID read_tire_strings( std::string &mean_velocity_variable_name, //Read the string data specific to tire nozzle data.
                          std::string &velocity_range_variable_name,
                          std::string &tire_arc_position_variable_name,
                          std::string &emission_offset_angle_variable_name,
                          std::string &cone_half_angle_variable_name,
                          std::string &strectch_factor_variable_name);
  VOID read_full_cone_strings( std::string &mean_velocity_variable_name, //Read the strings specific to a full cone emitter configuration nozzle data.
                               std::string &velocity_range_varaible_name,
                               std::string &cone_half_angle_variable_name,
                               std::string &outer_half_angle_limit_variable_name); //added for cdi/513 (10/26/16)
  VOID read_hollow_cone_strings( std::string &mean_velocity_variable_name,  //Read the strings specific to a hollow cone emitter configuration nozzle data.
                                 std::string &velocity_range_varaible_name,
                                 std::string &mean_angle_variable_name,
                                 std::string &angle_stddev_variable_name,
                                 std::string &outer_half_angle_limit_variable_name, //added for cdi/513 (10/26/16)
                                 std::string &inner_half_angle_limit_variable_name);

  VOID read_elliptical_cone_strings( std::string &mean_velocity_variable_name,
                                     std::string &velocity_range_varaible_name,
                                     std::string &major_cone_half_angle_varaible_name,
                                     std::string &major_cone_half_angle_stddev_variable_name,
                                     std::string &minor_cone_half_angle_varaible_name,
                                     std::string &minor_cone_half_angle_stddev_variable_name,
                                     std::string &major_outer_half_angle_limit_variable_name,//added for cdi/513 (10/26/16)
                                     std::string &minor_outer_half_angle_limit_variable_name);
  VOID read_rain_strings();

}* LGI_NOZZLE_CONFIGURATION_REC_READER;


typedef class sLGI_SURFACE_EMITTER_REC_READER : public sLGI_SURFACE_EMITTER_REC 
{
 public:
  VOID read(std::string &name, std::vector<int> &face_list, std::vector<std::string> &sprayer_direction_variable_names);
}* LGI_SURFACE_EMITTER_REC_READER;



typedef class sLGI_VOLUME_EMITTER_REC_READER : public sLGI_VOLUME_EMITTER_REC
{
 public:
  VOID read(std::string &name,
            std::vector<std::string> &sprayer_direction_variable_names,
            std::vector<sLGI_CYLINDER_REC> &cylinders,
            std::vector<std::string> &cylinder_names,
            std::vector<sLGI_BOX_REC> &boxes,
            std::vector<std::string> &box_names);
}* LGI_VOLUME_EMITTER_REC_READER;

typedef class sLGI_POINT_EMITTER_REC_READER : public sLGI_POINT_EMITTER_REC
{
 public:
  VOID read(std::string &name,
            std::vector<std::string> &sprayer_direction_variable_names,
            std::vector<dFLOAT> &points);
}* LGI_POINT_EMITTER_REC_READER;

typedef class sLGI_TIRE_EMITTER_REC_READER : public sLGI_TIRE_EMITTER_REC 
{
 public: 
  VOID read(std::string &name,
            std::vector<std::string> &cylinder_names,
            std::vector<sLGI_CYLINDER_REC> &cylinders);

}* LGI_TIRE_EMITTER_REC_READER;

typedef class sLGI_RAIN_EMITTER_REC_READER : public sLGI_RAIN_EMITTER_REC
{
 public:
  VOID read(std::string &name,
            std::vector<sLGI_CYLINDER_REC> &cylinders,
            std::vector<std::string> &cylinder_names,
            std::vector<sLGI_BOX_REC> &boxes,
            std::vector<std::string> &box_names);
}* LGI_RAIN_EMITTER_REC_READER;


//Classes to read cylinder and box records from an LGI stream:
typedef struct sLGI_CYLINDER_REC_READER : public sLGI_CYLINDER_REC
 {
  VOID read(std::string &name);
}* LGI_CYLINDER_REC_WRITER;

//struct sCDI_BOX_;

typedef struct sLGI_BOX_REC_READER : public sLGI_BOX_REC
{
  VOID read(std::string &name);
}* LGI_BOX_REC_WRITER;

#endif
