#ifndef EXA_SIMENG_RADIATION_SOLVER_DATA_H
#define EXA_SIMENG_RADIATION_SOLVER_DATA_H

static constexpr size_t N_RADIATION_TIMESTEPS = 2;

template<typename SFL_TYPE_TAG>
struct tSURFEL_RADIATION_DATA
{
  sRADIATION_SURFACE_CONDITION* surface_condition;
  dFLOAT blackbody_power[N_RADIATION_TIMESTEPS];
  dFLOAT emissivity_power[N_RADIATION_TIMESTEPS]; // currently a constant, but could be temp/time/space varying

  sdFLOAT emissivity;
  sdFLOAT temp_prev;

  dFLOAT net_rad_flux_accum;
  dFLOAT irradiation_accum;

  sdFLOAT net_rad_flux_prev;  
  sdFLOAT irradiation_prev; // for measurements

  uINT64 ckpt_len() const {
    uINT64 len = 0;
    len += sizeof(blackbody_power[0])*N_RADIATION_TIMESTEPS;
    len += sizeof(emissivity_power[1])*N_RADIATION_TIMESTEPS;
    len += sizeof(temp_prev);
    len += sizeof(net_rad_flux_accum);
    len += sizeof(irradiation_accum);
    len += sizeof(net_rad_flux_prev);
    len += sizeof(irradiation_prev);
    return len;
  }

  void read_ckpt() {
    read_lgi(&blackbody_power[0],sizeof(blackbody_power[0])*N_RADIATION_TIMESTEPS);
    read_lgi(&emissivity_power[0],sizeof(emissivity_power[0])*N_RADIATION_TIMESTEPS);
    read_lgi(temp_prev);
    read_lgi(net_rad_flux_accum);
    read_lgi(irradiation_accum);
    read_lgi(net_rad_flux_prev);
    read_lgi(irradiation_prev);
  }

  void write_ckpt() {
    write_ckpt_lgi(&blackbody_power[0],sizeof(blackbody_power[0])*N_RADIATION_TIMESTEPS);
    write_ckpt_lgi(&emissivity_power[0],sizeof(emissivity_power[0])*N_RADIATION_TIMESTEPS);
    write_ckpt_lgi(temp_prev);
    write_ckpt_lgi(net_rad_flux_accum);
    write_ckpt_lgi(irradiation_accum);
    write_ckpt_lgi(net_rad_flux_prev);
    write_ckpt_lgi(irradiation_prev);
  }

};

using sSURFEL_RADIATION_DATA = tSURFEL_RADIATION_DATA<SFL_SDFLOAT_TYPE_TAG>;

#endif
