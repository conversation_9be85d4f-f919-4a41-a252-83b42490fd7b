/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef ADVECT_MBLK_H
#define ADVECT_MBLK_H

namespace GPU {

  
// @global : g_nbr_offsets
// This global is used to identify which neighbor is to 
// be retreived from the UBLK box
__constant__ sINT16 g_nbr_offsets[N_STATES][3]{{ 0, 0, 0 },
                                               { 1, 0, 0 },
                                               {-1, 0, 0 },
                                               { 0, 1, 0 },
                                               { 0,-1, 0 },
                                               { 0, 0, 1 },
                                               { 0, 0,-1 },
                                               { 1,-1, 0 },
                                               {-1, 1, 0 },
                                               { 1, 1, 0 },
                                               {-1,-1, 0 },
                                               {-1, 0, 1 },
                                               { 1, 0,-1 },
                                               {-1, 0,-1 },
                                               { 1, 0, 1 },
                                               { 0,-1, 1 },
                                               { 0, 1,-1 },
                                               { 0, 1, 1 },
                                               { 0,-1,-1 }};

/*============================================================================
 * @struct MBLK_ADVECTION_INTERACTION
 * This templated struct defines an interface to retrieve thread interactions
 * for a specific latvec pair. The advection kernel is itself templated on the
 * latvec pair, and is therefore to select the right template specialization of
 * MBLK_ADVECTION_INTERACTION to figure out interactions for the threads
 *
 * Interactions for the advecting UBLK is itself implicit and is not stored.
 * The code below is a little ugly but was written this way
 * due to limitatons of the NVCC compiler.
 *==========================================================================*/
template<int LP_PAIR>
struct MBLK_ADVECT_INTERACTION {};

#define DEFINE_ADVECTION_INTERFACE(lp_pair)				\
  template<>								\
  struct MBLK_ADVECT_INTERACTION<lp_pair> {                             \
									\
    constexpr static __host__ __device__ int  num_threads() {		\
      return 64 + sizeof(g_interaction_map_##lp_pair)/sizeof(int);	\
    }									\
									\
    static __device__ int  get_interaction(int thread) {		\
      return (thread < 64)? 0 : g_interaction_map_##lp_pair[thread - 64]; \
    }									\
									\
    static __device__ int get_interaction_voxel(int thread) {		\
      return (thread < 64)? thread : g_interaction_voxels_##lp_pair[thread - 64]; \
    }									\
									\
    static __device__ int get_child_ublk_offset(int thread) {		\
      return (thread < 64)? (thread >> 3) : g_child_ublk_offsets_##lp_pair[thread - 64]; \
    }									\
									\
    static_assert(sizeof(g_interaction_map_##lp_pair) == sizeof(g_interaction_voxels_##lp_pair), \
		  "Size of interactions map and voxels should be identical."); \
  };


/*============================================================================
 * INTERACTION LP 0, +X -X
 *==========================================================================*/

__constant__ int g_interaction_map_0[] = {    
  /*offset (1,0,0)*/
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
};

__constant__ int g_interaction_voxels_0[] = {
  /*offset (1,0,0)*/
  0, 1, 2, 3, 8, 9, 10, 11, 16, 17, 18, 19, 24, 25, 26, 27
};

__constant__ int g_child_ublk_offsets_0[] = {
  /*offset (1,0,0)*/
  4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7
};
  
/*============================================================================
 * INTERACTION LP 1, +Y -Y
 *==========================================================================*/

__constant__ int g_interaction_map_1[] = {
  /*offset (0,1,0)*/
  3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3
};
    
__constant__ int g_interaction_voxels_1[] = {
  /*offset (0,1,0)*/
  0, 1, 4, 5, 8, 9, 12, 13, 32, 33, 36, 37, 40, 41, 44, 45
};

__constant__ int g_child_ublk_offsets_1[] = {
  /*offset (0,1,0)*/
  2, 2, 2, 2, 3, 3, 3, 3, 6, 6, 6, 6, 7, 7, 7, 7
};  

/*============================================================================
 * INTERACTION LP 2, +Z -Z
 *==========================================================================*/

__constant__ int g_interaction_map_2[] = {
  /*offset (0,0,1)*/
  5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5
};
    
__constant__ int g_interaction_voxels_2[] = {
  /*offset (0,0,1)*/
  0, 2, 4, 6, 16, 18, 20, 22, 32, 34, 36, 38, 48, 50, 52, 54
};

__constant__ int g_child_ublk_offsets_2[] = {
  /*offset (0,0,1)*/
  1, 1, 1, 1, 3, 3, 3, 3, 5, 5, 5, 5, 7, 7, 7, 7
};    

/*============================================================================
 * INTERACTION LP 3, +X-Y -X+Y
 *==========================================================================*/
__constant__ int g_interaction_map_3[] = {
  /*offset (1,0,0)*/
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  /*offset (0,-1,0)*/
  4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
  /*offset (1, -1, 0)*/
  7, 7, 7, 7    
};
    
__constant__  int g_interaction_voxels_3[] = {
  /*offset (1,0,0)*/
  0,  1,  2,  3,  8,  9, 10, 11, 16, 17, 24, 25,
  /*offset (0,-1,0)*/
  22, 23, 30, 31, 50, 51, 54, 55, 58, 59, 62, 63,
  /*offset (1, -1, 0)*/
  18, 19, 26, 27          
};

__constant__  int g_child_ublk_offsets_3[] = {
  4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 7, 7, 0, 0, 1, 1, 4, 4, 4, 4, 5, 5, 5, 5, 4, 4, 5, 5
};
  
/*============================================================================
 * INTERACTION LP 4, +X+Y -X-Y
 *==========================================================================*/
__constant__ int g_interaction_map_4[] = {
  /*offset (1,0,0)*/
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  /*offset (0,1,0)*/
  3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
  /*offset (1, 1, 0)*/
  9, 9, 9, 9
};
    
__constant__  int g_interaction_voxels_4[] = {
  /*offset (1,0,0)*/
  2, 3, 10, 11, 16, 17, 18, 19, 24, 25, 26, 27,
  /*offset (0,1,0)*/
  4, 5, 12, 13, 32, 33, 36, 37, 40, 41, 44, 45, 
  /*offset (1, 1, 0)*/
  0, 1, 8, 9
};

__constant__ int g_child_ublk_offsets_4[] =  {
  4, 4, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 2, 2, 3, 3, 6, 6, 6, 6, 7, 7, 7, 7, 6, 6, 7, 7
};
  
/*============================================================================
 * INTERACTION LP 5, -X+Z +X-Z
 *==========================================================================*/
__constant__ int g_interaction_map_5[] = {
  /*offset (-1,0,0)*/
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  /*offset (0,0, 1)*/
  5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
  /*offset (-1,0, 1)*/
  11, 11, 11, 11
};
    
__constant__  int g_interaction_voxels_5[] = {
  /*offset (-1,0,0)*/
  37, 39, 44, 45, 46, 47, 53, 55, 60, 61, 62, 63,
  /*offset (0,0,1)*/
  0,  2,  4,  6, 16, 18, 20, 22, 32, 34, 48, 50,
  /*offset (-1,0,1)*/
  36, 38, 52, 54
};

__constant__ int g_child_ublk_offsets_5[] =  {
  0, 0, 1, 1, 1, 1, 2, 2, 3, 3, 3, 3, 1, 1, 1, 1, 3, 3, 3, 3, 5, 5, 7, 7, 1, 1, 3, 3
};  

/*============================================================================
 * INTERACTION LP 6, -X-Z +X+Z
 *==========================================================================*/
  
__constant__ int g_interaction_map_6[] = {
  /*offset (-1,0,0)*/
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  /*offset (0,0,-1)*/
  6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
  /*offset (-1,0,-1)*/
  13, 13, 13, 13
};
    
__constant__ int g_interaction_voxels_6[] = {
  /*offset (-1,0,0)*/
  36, 37, 38, 39, 44, 46, 52, 53, 54, 55, 60, 62,
  /*offset (0,0,-1)*/
  9, 11, 13, 15, 25, 27, 29, 31, 41, 43, 57, 59,
  /*offset (-1,0,-1)*/
  45, 47, 61, 63
};

__constant__ int g_child_ublk_offsets_6[] =  {
  0, 0, 0, 0, 1, 1, 2, 2, 2, 2, 3, 3, 0, 0, 0, 0, 2, 2, 2, 2, 4, 4, 6, 6, 0, 0, 2, 2
};  

/*============================================================================
 * INTERACTION LP 7, -Y+Z +Y-Z
 *==========================================================================*/
__constant__ int g_interaction_map_7[] = {
  /*offset (0,-1,0)*/
  4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
  /*offset (0, 0, +1)*/
  5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
  /*offset (0, -1, +1)*/
  15, 15, 15, 15
};
    
__constant__  int g_interaction_voxels_7[] = {
  /*offset (0,-1,0)*/
  19, 23, 26, 27, 30, 31, 51, 55, 58, 59, 62, 63,
  /*offset (0, 0, +1)*/
  0,  2,  4,  6, 16, 20, 32, 34, 36, 38, 48, 52,
  /*offset (0, -1, +1)*/
  18, 22, 50, 54
};

__constant__ int g_child_ublk_offsets_7[] =  {
  0, 0, 1, 1, 1, 1, 4, 4, 5, 5, 5, 5, 1, 1, 1, 1, 3, 3, 5, 5, 5, 5, 7, 7, 1, 1, 5, 5
};  

/*============================================================================
 * INTERACTION LP 8, +Y+Z -Y-Z
 *==========================================================================*/

__constant__ int g_interaction_map_8[] = {
  /*offset (0,1,0)*/
  3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
  /*offset (0,0,1)*/
  5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
  /*offset (0, 1, 1)*/
  17, 17, 17, 17
};
    
__constant__  int g_interaction_voxels_8[] = {
  /*offset (0,1,0)*/
  1, 5,  8,  9, 12, 13, 33, 37, 40, 41, 44, 45, 
  /*offset (0,0,1)*/
  2, 6, 16, 18, 20, 22, 34, 38, 48, 50, 52, 54,
  /*offset (0, 1, 1)*/
  0, 4, 32, 36
};

__constant__ int g_child_ublk_offsets_8[] =  {
  2, 2, 3, 3, 3, 3, 6, 6, 7, 7, 7, 7, 1, 1, 3, 3, 3, 3, 5, 5, 7, 7, 7, 7, 3, 3, 7, 7
};
  
DEFINE_ADVECTION_INTERFACE(0)
DEFINE_ADVECTION_INTERFACE(1)
DEFINE_ADVECTION_INTERFACE(2)
DEFINE_ADVECTION_INTERFACE(3)
DEFINE_ADVECTION_INTERFACE(4)
DEFINE_ADVECTION_INTERFACE(5)
DEFINE_ADVECTION_INTERFACE(6)
DEFINE_ADVECTION_INTERFACE(7)
DEFINE_ADVECTION_INTERFACE(8)

}//NAMESPACE GPU
  
#endif
