#ifndef _PARTICLE_SIM_INFO_H_
#define _PARTICLE_SIM_INFO_H_


#include "particle_emitters.h"
#include "particle_sim.h"
#include "particle_comm.h"

typedef struct sSURFACE_EROSION_PARAMS {
  sPARTICLE_VAR m_start_time;
  sPARTICLE_VAR m_surface_material_density;
  sPARTICLE_VAR m_F_s;
  sPARTICLE_VAR m_C_c;
  sPARTICLE_VAR m_C_d;
  sPARTICLE_VAR m_K;
  sPARTICLE_VAR m_U_tsh;
 sPARTICLE_VAR erosion_ratio(
                              sPARTICLE_VAR U, 
                              sPARTICLE_VAR theta);
} SURFACE_EROSION_PARAMS;

typedef struct sPARTICLE_SIM_INFO {
  asINT32 max_allowed_parcels;
  asINT32 num_materials;
  asINT32 num_emitter_configurations;
  asINT32 num_cdi_emitters; //CDI emitters may use several pieces of geometr while on the SP, one emitter is created per piece of geometry.
  asINT32 num_surface_properties;
  asINT32 num_screens;
  asINT32 num_virtual_wipers;

  sLGI_ICE_ACCRETION_REC ice_accretion_parameters;
  std::vector<asINT32> accretion_surface_ids;

  sSURFACE_EROSION_PARAMS erosion_parameters;

  sLGI_BOX_REC dispersion_box;
  char * dispersion_box_name;
  asINT32 gravity_csys_id;
 
  BOOLEAN feedback_enabled;
  BOOLEAN interpolate_local_velocity;
  
  sPARTICLE_VAR collision_detection_projection_time;
  sPARTICLE_VAR gravity[3];
  sPARTICLE_VAR min_diameter_for_drag_calculation;


  //hidden model parameters (set by CP via environment variables)
  sPARTICLE_VAR reentrainment_diameter;
  sPARTICLE_VAR reentrainment_diameter_range;
  sPARTICLE_VAR surface_tension_factor_at_splash;
  sPARTICLE_VAR shear_stress_factor_for_film;
  sPARTICLE_VAR max_radius_of_film_stencil;
  sPARTICLE_VAR advancing_contact_angle;
  sPARTICLE_VAR receding_contact_angle;
  BOOLEAN film_surface_tension_model_enabled;
  sPARTICLE_VAR m_min_reentrainment_length;

  std::vector<PARTICLE_MATERIAL> particle_materials;
  std::vector<PARTICLE_EMITTER_CONFIGURATION_BASE> emitter_configurations;
  std::vector<PARTICLE_EMITTER_BASE> emitters; 
  std::map<asINT32, PARTICLE_SCREEN> screens;  //Wurigen's map from a face index to a screen

  //emitter geometry primitaves ( which contains their ublk octrees )
  sEMISSION_CYLINDERS emission_cylinders;
  sEMISSION_BOXES emission_boxes;
  sEMISSION_SURFACES emission_surfaces;
  sEMISSION_POINT_SETS emission_points;
  
  std::vector<std::vector<SURFACE_INTERACTION_PROPERTY> > surface_interaction_matrix;  
  std::vector<VIRTUAL_WIPER> wipers;

  //BOOLEAN film_solver_is_on() {return film_solver_active;}
  BOOLEAN surface_tension_is_on() {return film_surface_tension_model_enabled;}
  BOOLEAN feedback_is_on(VOID) {return feedback_enabled;}
  
  } *PARTICLE_SIM_INFO;

//extern RANDOM_PARTICLE_PROPERTIES g_random_particle_properties;
extern sPARTICLE_SIM_INFO g_particle_sim_info;

#endif
