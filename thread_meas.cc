/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "meas.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "thread_run.h"

#if BUILD_GPU
#include "gpu_host_include.h"
#endif

#include "comm_utils.h"

#include <iostream>
#include <fstream>

VOID cMEAS_SEND_QUEUE::add_entry(sMEAS_WINDOW *window)
{
  m_queue.push(window);
}

VOID cMEAS_SEND_QUEUE::drain()
{
  m_queue.drain([](MEAS_WINDOW window) {
    if(window->m_n_child_sps == 0) {
      window->send();
    } else {
      BOOLEAN window_ready = TRUE;
      while (window->m_n_child_sps_ready < window->m_n_child_sps) {
        int flag;
        MPI_Status mpi_status;
        MPI_Test(&window->m_child_receive_requests[window->m_n_child_sps_ready], &flag, &mpi_status);
        if (!flag) {
          window_ready = FALSE;
          break;
        } else {
          window->m_n_child_sps_ready++;
        }
      }
      if(window_ready) {
        window->m_n_child_sps_ready = 0;
        window->send();
        window->post_tree_reduction_receives();
      } else {
        window->append_to_pending_queue();        
      }
    }
  });
}

cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>& 
sMEAS_WINDOW_ROTATING_BC_INFO::get_examsg(asINT32 index)
{
  // Allocate a vector which can hold the ExaMsg for all rotating BCs
  static std::vector<cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1> *> exa_send_msg = {nullptr};
  if (index >= exa_send_msg.size()) {
    for (int i = exa_send_msg.size(); i <= index; i++)
      exa_send_msg.push_back(nullptr);
  }
  if (exa_send_msg[index] == nullptr) {
    exa_send_msg[index] = new cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>;
    exa_send_msg[index]->init(my_proc_id, eMPI_sp_cp_rank());
    exa_send_msg[index]->set_nelems(1);
  }
  return *exa_send_msg[index];   
}

cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>& 
sMEAS_WINDOW_SLIDING_BC_INFO::get_examsg(asINT32 index)
{
  // Allocate a vector which can hold the ExaMsg for all sliding BCs
  static std::vector<cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1> *> exa_send_msg = {nullptr};
  if (index >= exa_send_msg.size()) {
    for (int i = exa_send_msg.size(); i <= index; i++)
      exa_send_msg.push_back(nullptr);
  }
  if (exa_send_msg[index] == nullptr) {
    exa_send_msg[index] = new cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1>;
    exa_send_msg[index]->init(my_proc_id, eMPI_sp_cp_rank());
    exa_send_msg[index]->set_nelems(1);
  }
  return *exa_send_msg[index];   
}

cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1>& 
sMEAS_WINDOW_LRF_INFO::get_examsg(asINT32 index)
{
  // Allocate a vector which can hold the ExaMsg for all time varying LRFs
  static std::vector<cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1> *> exa_send_msg = {nullptr};
  if (index >= exa_send_msg.size()) {
    for (int i = exa_send_msg.size(); i <= index; i++)
      exa_send_msg.push_back(nullptr);
  }
  if (exa_send_msg[index] == nullptr) {
    exa_send_msg[index] = new cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1>;
    exa_send_msg[index]->init(my_proc_id, eMPI_sp_cp_rank());
    exa_send_msg[index]->set_nelems(1);
  }
  return *exa_send_msg[index];   
}

cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1>& 
sMEAS_WINDOW_MOVB_INFO::get_examsg(asINT32 index)
{
  // Allocate a vector which can hold the ExaMsg for all MOVB
  static std::vector<cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1> *> exa_send_msg = {nullptr};
  if (index >= exa_send_msg.size()) {
    for (int i = exa_send_msg.size(); i <= index; i++)
      exa_send_msg.push_back(nullptr);
  }
  if (exa_send_msg[index] == nullptr) {
    exa_send_msg[index] = new cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1>;
    exa_send_msg[index]->init(my_proc_id, eMPI_sp_cp_rank());
    exa_send_msg[index]->set_nelems(1);
  }
  return *exa_send_msg[index];   
}

cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1>& 
sMEAS_WINDOW_GRF_INFO::get_examsg()
{
  static cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1> *exa_send_msg;
  if (exa_send_msg == nullptr) {
    exa_send_msg = new cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1>;
    exa_send_msg->init(my_proc_id, eMPI_sp_cp_rank());
    exa_send_msg->set_nelems(1);
  }
  return *exa_send_msg;   
}

VOID sMEAS_WINDOW::send() {
#if BUILD_GPU
  copy_stationary_meas_cells_from_gpu();
  convert_for_cp();
#endif
  int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);
  if (is_ref_frame_master_sp) {
    int ref_frame_tag = make_mpi_tag<eMPI_MSG::MRFRAME>(index);

    if (sim.grf.is_time_varying) {
      // Send the message to the CP (whose rank is N_SPS)
      cExaMsg<sGRF_MEAS_FRAME_SP_TO_CP_MSG, 1> grf_send_examsg = grf.get_examsg(); 
      complete_mpi_request_while_processing_cp_messages(grf_send_examsg, MPI_SLEEP_LONG);
      grf_send_examsg.settag(ref_frame_tag);
      g_exa_sp_cp_comm.isend(grf_send_examsg);
      //msg_print("      Sending grf window index %d tag %d", index,  ref_frame_tag);
    }

    MEAS_WINDOW_LRF_INFO lrf = lrfs;
    ccDOTIMES(i, g_n_time_varying_lrfs) {
      cExaMsg<sLRF_MEAS_FRAME_SP_TO_CP_MSG, 1> lrf_send_examsg = lrf->get_examsg(i);
      complete_mpi_request_while_processing_cp_messages(lrf_send_examsg, MPI_SLEEP_LONG);
      lrf_send_examsg.settag(ref_frame_tag);
      //msg_print("      Sending lrf %d window index %d tag %d",i, index,  ref_frame_tag);
      g_exa_sp_cp_comm.isend(lrf_send_examsg);
      lrf++;
    }

    MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = rotating_bcs;
    ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
      cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1> rotating_bc_send_examsg = rotating_bc->get_examsg(i);
      complete_mpi_request_while_processing_cp_messages(rotating_bc_send_examsg, MPI_SLEEP_LONG);
      //msg_print("      Sending rotating_bcs %d window index %d tag %d",i, index,  ref_frame_tag);
      rotating_bc_send_examsg.settag(ref_frame_tag);
      g_exa_sp_cp_comm.isend(rotating_bc_send_examsg);
      rotating_bc++;
    }

    MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = sliding_bcs;
    ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
      cExaMsg<sMBC_MEAS_FRAME_SP_TO_CP_MSG, 1> sliding_bc_send_examsg = sliding_bc->get_examsg(i);
      complete_mpi_request_while_processing_cp_messages(sliding_bc_send_examsg, MPI_SLEEP_LONG);
      sliding_bc_send_examsg.settag(ref_frame_tag);
      g_exa_sp_cp_comm.isend(sliding_bc_send_examsg);
      //msg_print("      Sending sliding_bcs %d window index %d tag %d",i, index,  ref_frame_tag);
      sliding_bc++;
    }

    MEAS_WINDOW_MOVB_INFO movb = movbs;
    ccDOTIMES(n, sim.n_movb_physics_descs) {
      cExaMsg<sMOVB_MEAS_FRAME_SP_TO_CP_MSG, 1> movb_send_examsg = movb->get_examsg(n);
      complete_mpi_request_while_processing_cp_messages(movb_send_examsg, MPI_SLEEP_LONG);
      movb_send_examsg.settag(ref_frame_tag);
      g_exa_sp_cp_comm.isend(movb_send_examsg);
      // Send the message to the CP (whose rank is N_SPS)
      // LOG_MSG("BSURFEL_MEAS","TS",g_timescale.m_time,"window",index) << "Sending movb";

      movb++;
    } 
  }

  if ( n_stationary_meas_cells > 0 ) {
    if(use_tree_reduction()) {
      // It's assumed this is not called unless the child SP messages have been received
      reduce_meas_cells();
      complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);
      MPI_Issend(m_send_buffer, n_total_vars_to_cp(), eMPI_REDUCTION_FLOAT, 
                 m_parent_rank, tag, eMPI_sp_cp_comm, &m_send_request);
    } else if (is_meas_vars_output_dp) {
      complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);
      MPI_Issend(m_send_buffer, n_total_vars_to_cp(), eMPI_dFLOAT, 
                 m_parent_rank, tag, eMPI_sp_cp_comm, &m_send_request);
    } else {
      complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);
      MPI_Issend(m_send_buffer, n_total_vars_to_cp(), eMPI_sFLOAT, 
                 m_parent_rank, tag, eMPI_sp_cp_comm, &m_send_request);
    }
  }

  // make sure the count has been sent
  // send the moving data buffer
  if ( n_global_moving_meas_cells > 0 ) {

    // Always complete this, because n_moving_cells could be 0 in a given time step,
    // leaving the moving_send_request unfulfilled.
    complete_mpi_request_while_processing_cp_messages(&m_moving_send_count_request, MPI_SLEEP_LONG);
    complete_mpi_request_while_processing_cp_messages(&m_moving_send_request, MPI_SLEEP_LONG);

    int moving_tag = make_mpi_tag<eMPI_MSG::MOVMWIN>(index);
    MPI_Issend(&n_send_moving_meas_cells, 1, eMPI_sINT32, eMPI_sp_cp_rank(), moving_tag, eMPI_sp_cp_comm, &m_moving_send_count_request);

    if (n_send_moving_meas_cells > 0 ) {
      n_moving_meas_cell_data = n_send_moving_meas_cells * (n_cp_variables + 1);
      LOG_MSG_IF(this->is_composite,"BSURFEL_MEAS","TS",g_timescale.m_time,"window",this->index) << "Sending";
      LOG_MSG_IF(this->is_composite,"BSURFEL_MEAS", LOG_TS, "window",this->index) << "global meas index "
        << reinterpret_cast<MOVING_MEAS_CELL_ID_AND_REF>(m_moving_send_buffer)->global_meas_index;
      if (use_tree_reduction()) {
        LOG_MSG("BSURFEL_MEAS",LOG_TS,"window",this->index) << "Tree Reduction";
        MPI_Issend(m_moving_send_buffer, n_moving_meas_cell_data, eMPI_REDUCTION_FLOAT, 
                   eMPI_sp_cp_rank(), moving_tag, eMPI_sp_cp_comm, &m_moving_send_request);
      } else if (is_meas_vars_output_dp) {
        MPI_Issend(m_moving_send_buffer, n_moving_meas_cell_data, eMPI_dFLOAT, 
                   eMPI_sp_cp_rank(), moving_tag, eMPI_sp_cp_comm, &m_moving_send_request);
      } else {
        MPI_Issend(m_moving_send_buffer, n_moving_meas_cell_data, eMPI_sFLOAT, 
                   eMPI_sp_cp_rank(), moving_tag, eMPI_sp_cp_comm, &m_moving_send_request);
      }
    }
  }

  insert_in_completion_queue();
  
}

VOID sMEAS_WINDOW::post_tree_reduction_receives()
{
  int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);
  ccDOTIMES(i, m_n_child_sps) {
    MPI_Irecv(m_child_meas_cells[i], (sINT64) n_cp_variables * m_n_child_meas_cells[i], eMPI_REDUCTION_FLOAT,
              m_child_sps[i], tag, eMPI_sp_cp_comm, &m_child_receive_requests[i]);
  }
}

VOID sMEAS_WINDOW::setup_tree_reduction()
{
  if (!use_tree_reduction()) {
    m_n_reduced_meas_cells = n_stationary_meas_cells;
  } else {
    int tag = make_mpi_tag<eMPI_MSG::MWIN>(index);

    if (m_n_child_sps <= 0) {
      m_n_reduced_meas_cells = n_stationary_meas_cells;
      // send reduced global meas cell indices to parent rank
      MPI_Send(m_my_global_meas_cell_indices, m_n_reduced_meas_cells, eMPI_MEAS_CELL_INDEX, 
               m_parent_rank, tag, eMPI_sp_cp_comm);
      // Since there are no child SPs, m_my_global_meas_cell_indices is not needed to carry out a "reduction"
      delete[] m_my_global_meas_cell_indices;
      m_my_global_meas_cell_indices = NULL;
    } else {
      MPI_Status mpi_status;
      // recv vector of meas cell indices that each child will send
      ccDOTIMES(i, m_n_child_sps) {
        MPI_Probe(m_child_sps[i], tag, eMPI_sp_cp_comm, &mpi_status);
        int n_cells;
        MPI_Get_count(&mpi_status, eMPI_MEAS_CELL_INDEX, &n_cells);
        m_n_child_meas_cells[i] = n_cells;
        m_child_global_meas_cell_indices[i] = new STP_MEAS_CELL_INDEX[ n_cells ];
        RECV_EXA_SIM_MSG<STP_MEAS_CELL_INDEX> recv_child_global_meas_cell_indices(tag, n_cells, m_child_global_meas_cell_indices[i], m_child_sps[i]);
        g_exa_sp_cp_comm.recv(recv_child_global_meas_cell_indices.mpi_msg);
      }

      std::vector<STP_MEAS_CELL_INDEX> reduced_meas_cell_indices; // auto deleted on exit from routine
      reduced_meas_cell_indices.reserve(2 * MAX(MAX(m_n_child_meas_cells[0], m_n_child_meas_cells[1]),
                                                n_stationary_meas_cells));
      m_n_reduced_meas_cells = compute_reduced_global_meas_cell_indices(reduced_meas_cell_indices);

      // send reduced global meas cell indices to parent rank
      MPI_Send(&reduced_meas_cell_indices[0], m_n_reduced_meas_cells, eMPI_MEAS_CELL_INDEX, 
               m_parent_rank, tag, eMPI_sp_cp_comm);

      m_child_meas_cells[0] = new REDUCTION_MEAS_CELL_VAR[ (sINT64)n_cp_variables * m_n_child_meas_cells[0] ];
      if (m_n_child_sps > 1)
        m_child_meas_cells[1] = new REDUCTION_MEAS_CELL_VAR[ (sINT64)n_cp_variables * m_n_child_meas_cells[1] ];
    }

    // post initial set of receives from children
    post_tree_reduction_receives();
  }
}
  

VOID sMEAS_WINDOW::insert_in_completion_queue() 
{
  MEAS_WINDOW window = this;
  TIMESTEP output_time      = window->current_update_time.output_time;
  TIMESTEP next_output_time = window->next_update_time.output_time;

  MEAS_WINDOW next_window = g_strand_mgr.m_meas_completion_queue.m_head;
  if(next_window == NULL
     || output_time < next_window->current_update_time.output_time
     || (output_time == next_window->current_update_time.output_time
         && (index < next_window->index))) {
    g_strand_mgr.m_meas_completion_queue.m_head = window;
    m_next_in_completion_queue = next_window;
  } else {
    MEAS_WINDOW previous_window;
    while (1) {
      previous_window = next_window;
      next_window = next_window->m_next_in_completion_queue;
      if (next_window == NULL 
          || (output_time < next_window->current_update_time.output_time)
          || (output_time == next_window->current_update_time.output_time
              && (index < next_window->index)))
        break;
    }
    previous_window->m_next_in_completion_queue = window;
    m_next_in_completion_queue = next_window;
  }
}

void sMEAS_WINDOW::set_send_buffers_ready()
{
  pthread_mutex_lock(&m_previous_send_mutex);
  m_all_send_buffers_ready = true;
  pthread_cond_signal(&m_previous_send_cv);
  pthread_mutex_unlock(&m_previous_send_mutex);
}

VOID cMEAS_COMPLETION_QUEUE::process()
{
  int flag;
  int moving_count_flag;
  int moving_flag;
  while (!is_empty()) {
    sMEAS_WINDOW *window = m_head;
    MPI_Test(&window->m_send_request,&flag,MPI_STATUS_IGNORE);
    MPI_Test(&window->m_moving_send_count_request,&moving_count_flag,MPI_STATUS_IGNORE);
    MPI_Test(&window->m_moving_send_request,&moving_flag,MPI_STATUS_IGNORE);
    if(flag && moving_count_flag && moving_flag) {
      window->set_send_buffers_ready();
      LOG_MSG_IF(window->is_composite,"BSURFEL_MEAS","TS",g_timescale.m_time,"window",window->index) << "cMEAS_COMPLETION_QUEUE Moving send buffer ready TRUE";
      m_head = window->m_next_in_completion_queue;
    } else {
      return;
    }
  }
}
    

VOID sMEAS_WINDOW::append_to_pending_queue() 
{
  MEAS_WINDOW window = this;
  window->m_next_in_pending_queue = NULL;
  if(g_strand_mgr.m_meas_pending_queue.m_head == NULL) {
    g_strand_mgr.m_meas_pending_queue.m_head = window;
    g_strand_mgr.m_meas_pending_queue.m_tail = window;
  } else {
    g_strand_mgr.m_meas_pending_queue.m_tail->m_next_in_pending_queue = window;
    g_strand_mgr.m_meas_pending_queue.m_tail = window;
  }
}

VOID cMEAS_PENDING_SEND_QUEUE::process()
{
  int flag;
  MPI_Status status;
  sMEAS_WINDOW *window = m_head;
  MEAS_WINDOW previous_window = NULL; // Non-null only if some window has been skipped
  while (window != NULL) {
    BOOLEAN window_ready = TRUE;
    while (window->m_n_child_sps_ready < window->m_n_child_sps) {
      int flag;
      MPI_Status mpi_status;
      MPI_Test(&window->m_child_receive_requests[window->m_n_child_sps_ready], &flag, &mpi_status);
      if (!flag) {
        window_ready = FALSE;
        break;
      } else {
        window->m_n_child_sps_ready++;
      }
    }
    if(window_ready) {

      // Send this window and remove it from the queue
      window->m_n_child_sps_ready = 0;
      window->send();
      window->post_tree_reduction_receives();
      if(previous_window == NULL) {
        m_head = window->m_next_in_pending_queue;
        window->m_next_in_pending_queue = NULL;
        window = m_head;
      } else {
        previous_window->m_next_in_pending_queue = window->m_next_in_pending_queue;
        window->m_next_in_pending_queue = NULL;
        window = previous_window->m_next_in_pending_queue;
      }
    } else {
      // Leave this window on the queue and go on to the next
      if(previous_window == NULL) {
        m_head = window;
      }
      previous_window = window;
      window = window->m_next_in_pending_queue;
    }
  }
  m_tail = previous_window; // NULL if nothing was skipped 
}

 



VOID sMEAS_WINDOW::reduce_meas_cells()
{
  // reduction of meas cells across 2 child SPs and this SP
  REDUCTION_MEAS_CELL_VAR *result = (REDUCTION_MEAS_CELL_VAR *) m_send_buffer;
  REDUCTION_MEAS_CELL_VAR *cells0 = m_child_meas_cells[0];
  REDUCTION_MEAS_CELL_VAR *cells1 = m_child_meas_cells[1];
  REDUCTION_MEAS_CELL_VAR *cells2 = (REDUCTION_MEAS_CELL_VAR *) convert_for_cp_buffer();

  STP_MEAS_CELL_INDEX *meas_cell_indices0 = m_child_global_meas_cell_indices[0];
  STP_MEAS_CELL_INDEX *meas_cell_indices1 = m_child_global_meas_cell_indices[1];
  STP_MEAS_CELL_INDEX *meas_cell_indices2 = m_my_global_meas_cell_indices;

  STP_MEAS_CELL_INDEX *end_meas_cell_indices0 = meas_cell_indices0 + m_n_child_meas_cells[0];
  STP_MEAS_CELL_INDEX *end_meas_cell_indices1 = meas_cell_indices1 + m_n_child_meas_cells[1];
  STP_MEAS_CELL_INDEX *end_meas_cell_indices2 = meas_cell_indices2 + n_stationary_meas_cells;

#define sum3(cellsA, cellsB, cellsC, meas_cell_indicesA, meas_cell_indicesB, meas_cell_indicesC)        \
  { ccDOTIMES(i, n_cp_variables)                                        \
      *result++ = *cellsA++  +  *cellsB++  +  *cellsC++;                \
    meas_cell_indicesA++;  meas_cell_indicesB++;  meas_cell_indicesC++; \
  }

#define sum2(cellsA, cellsB, meas_cell_indicesA, meas_cell_indicesB)    \
  { ccDOTIMES(i, n_cp_variables)                                        \
      *result++ = *cellsA++  +  *cellsB++;                              \
    meas_cell_indicesA++;  meas_cell_indicesB++;                        \
  }

#define sum1(cellsA, meas_cell_indicesA)        \
  { ccDOTIMES(i, n_cp_variables)                \
      *result++ = *cellsA++;                    \
    meas_cell_indicesA++;                       \
  }

  // loop advancing all 3 vectors of meas cell indices per iteration
  while (1) {
    // if one or more vectors is exhausted, set things up for next loop, and break
    if (meas_cell_indices2 >= end_meas_cell_indices2)
      break;
    if (meas_cell_indices1 >= end_meas_cell_indices1) {
      meas_cell_indices1 = meas_cell_indices2;
      end_meas_cell_indices1 = end_meas_cell_indices2;
      cells1 = cells2;
      break;
    }
    if (meas_cell_indices0 >= end_meas_cell_indices0) {
      meas_cell_indices0 = meas_cell_indices2;
      end_meas_cell_indices0 = end_meas_cell_indices2;
      cells0 = cells2;
      break;
    }

    STP_MEAS_CELL_INDEX min = MIN(*meas_cell_indices0, MIN(*meas_cell_indices1, *meas_cell_indices2));
    if (*meas_cell_indices0 == min) {
      if (*meas_cell_indices1 == min) {
        if (*meas_cell_indices2 == min) {
          sum3(cells0, cells1, cells2, meas_cell_indices0, meas_cell_indices1, meas_cell_indices2);
        } else {
          sum2(cells0, cells1, meas_cell_indices0, meas_cell_indices1);
        }
      } else {
        if (*meas_cell_indices2 == min) {
          sum2(cells0, cells2, meas_cell_indices0, meas_cell_indices2);
        } else {
          sum1(cells0, meas_cell_indices0);
        }
      }
    } else {
      if (*meas_cell_indices1 == min) {
        if (*meas_cell_indices2 == min) {
          sum2(cells1, cells2, meas_cell_indices1, meas_cell_indices2);
        } else {
          sum1(cells1, meas_cell_indices1);
        }
      } else {
        sum1(cells2, meas_cell_indices2);
      }
    }
  }

  // loop advancing 2 vectors of meas cell indices per iteration
  while (1) {
    // if one of the vectors is exhausted, finish the process consuming the remaining vector
    if (meas_cell_indices0 >= end_meas_cell_indices0) {
      ccDOTIMES(i, end_meas_cell_indices1 - meas_cell_indices1) {
        ccDOTIMES(j, n_cp_variables)
          *result++ = *cells1++;
      }
      break;
    }
    if (meas_cell_indices1 >= end_meas_cell_indices1) {
      ccDOTIMES(i, end_meas_cell_indices0 - meas_cell_indices0) {
        ccDOTIMES(j, n_cp_variables)
          *result++ = *cells0++;
      }
      break;
    }
        
    STP_MEAS_CELL_INDEX min = MIN(*meas_cell_indices0, *meas_cell_indices1);
    if (*meas_cell_indices0 == min) {
      if (*meas_cell_indices1 == min) {
        sum2(cells0, cells1, meas_cell_indices0, meas_cell_indices1); 
      } else {
        sum1(cells0, meas_cell_indices0);
      }
    } else {
      sum1(cells1, meas_cell_indices1);
    }
  }

#undef sum3
#undef sum2
#undef sum1

}
