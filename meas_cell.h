/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Measurement windows
 *
 * James Hoch, Exa Corporation
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MEAS_CELL_H
#define _SIMENG_MEAS_CELL_H

#define eMPI_MEAS_CELL_INDEX eMPI_sINT32 /* definition needs to be consistent w/ STP_MEAS_CELL_INDEX */

/*--------------------------------------------------------------------------*
 * Generic measurement cell definition
 *--------------------------------------------------------------------------*/
typedef dFLOAT MEAS_CELL_VAR;

struct sSTD_CELL_VAR {
private:
  asINT32 m_n_shobs;
  asINT32 m_n_counter;
public:
  sSTD_CELL_VAR() : m_n_shobs(0), m_n_counter(0)
  {
    static_assert(sizeof(sSTD_CELL_VAR) == sizeof(MEAS_CELL_VAR), "sSTD_CELL_VAR must be the same size as MEAS_CELL_VAR");
  }

  void add_shob() 
  {
    m_n_shobs++;
    m_n_counter++;
  }

  // decrements the counter and returns the old value
  __HOST__DEVICE__
  asINT32 decrement() 
  {
#if DEVICE_COMPILATION_MODE
    return atomicSub(&m_n_counter, 1);
#else
    return m_n_counter--;
#endif
  }

  __HOST__DEVICE__
  void reset() 
  {
    cassert(m_n_counter == 0);
    m_n_counter = m_n_shobs;
  }

  __HOST__DEVICE__
  asINT32 counter() const {
    return m_n_counter;
  }

  __HOST__DEVICE__
  asINT32 n_shobs() const {
    return m_n_shobs;
  }

};

typedef STP_CP_MEAS_DATA_TYPE CP_MEAS_CELL_VAR;
typedef dFLOAT REDUCTION_MEAS_CELL_VAR;
typedef MEAS_CELL_VAR* MOVING_MEAS_CELL_PTR;
#define eMPI_REDUCTION_FLOAT eMPI_dFLOAT

typedef POINTER_SIZE_INT MEAS_CELL_OFFSET; // offset from base of meas window cells

typedef struct sMOVING_MEAS_CELL_ID_AND_REF {
  STP_MEAS_CELL_INDEX global_meas_index;
  sINT32 ref_count;
} * MOVING_MEAS_CELL_ID_AND_REF;

// A MEAS_CELL_PTR is a pointer to a meas cell (block of dFLOAT variables)
// It initially starts out life as a meas cell index, which is converted to a
// meas cell pointer at the end of initialization (see
// sVOXEL_DYN_GROUP::resolve_meas_cell_ptrs and sSURFEL_DYN_GROUP::resolve_meas_cell_ptrs).
//
// For development windows referenced by moving ublks or surfels, we leave the
// meas cell pointer as an index because we also need to encode an axis and a
// part/face in the meas cell pointer.
//
// The second bit is set in the pointer/index to indicate that vector
// measurements (e.g. force, velocity) should be rotated to the global
// ref frame csys. For a non-composite meas window, this is only set
// for a meas cell if window->is_output_in_local_csys is false and the
// associated surfel or ublk resides in a sliding mesh ref frame. For
// a composite meas window, this will also be set if the meas cell spans a
// pair of sliding ref frames or a sliding ref frame and the global frame.
//
// The first bit is set in the pointer/index to indicate that there is another
// meas cell pointer for the same meas window.

struct sMEAS_CELL_PTR_TRAITS
{
  static_assert(alignof(MEAS_CELL_VAR) >= 8, "Alignment needs to be bigger");
  static constexpr uintptr_t reserved_bits = 3;
  static constexpr uintptr_t reserved_mask = ((uintptr_t) 1 << reserved_bits)-1;
  static constexpr uintptr_t ptr_mask = ~reserved_mask;
  static constexpr uintptr_t is_another_for_window_mask = (uintptr_t) 1 << 0;
  static constexpr uintptr_t should_rotate_vector_to_grf_mask = (uintptr_t) 1 << 1;

  static constexpr uintptr_t axis_bits = 2;
  static constexpr uintptr_t axis_mask = ((uintptr_t) 1 << axis_bits)-1;

  static constexpr uintptr_t reserved_axis_bits = reserved_bits + axis_bits;
  static constexpr uintptr_t reserved_axis_mask = ((uintptr_t) 1 << reserved_axis_bits)-1;
  static constexpr uintptr_t entity_bits = 15;
  static constexpr uintptr_t reserved_axis_entity_bits = reserved_bits + axis_bits + entity_bits;
  static constexpr uintptr_t reserved_axis_entity_mask = ((uintptr_t) 1 << reserved_axis_entity_bits)-1;
  static constexpr uintptr_t entity_mask = ((uintptr_t) 1 << entity_bits)-1;
};

class MEAS_CELL_PTR {
  union  {
    MEAS_CELL_VAR *m_vars;     // pointer to meas cell
    uintptr_t m_index;         // meas cell index (converted to pointer later)
  };

  using sTRAITS = sMEAS_CELL_PTR_TRAITS;

  public:
  MEAS_CELL_PTR()
  {
    m_index = 0;
  }

  MEAS_CELL_PTR(STP_MEAS_CELL_INDEX i)
  {
    m_index = 0;
    set_index(i);
  }

  VOID set_index(STP_MEAS_CELL_INDEX i)
  {
    static_assert( sizeof(i)*CHAR_BIT <= (sizeof(m_index)*CHAR_BIT - sTRAITS::reserved_axis_entity_bits), "Loss of precision in meas cell index!");
    m_index = (((uintptr_t) i << sTRAITS::reserved_axis_entity_bits) | (m_index & sTRAITS::reserved_axis_entity_mask));
  }

  __HOST__DEVICE__
  STP_MEAS_CELL_INDEX index() const
  {
    return m_index >> sTRAITS::reserved_axis_entity_bits;
  }

  // set_axis, set_part, and set_face will misbehave if used to reset a
  // non-zero axis or part/face index This is used for the moving_meas_cells
  // and bsurfels
  void reset()
  {
    m_index = 0;
  }

  void set_axis(asINT32 axis)
  {
    cassert(axis >= 0);
    cassert(axis < sTRAITS::axis_mask+1);
    m_index |= (((uintptr_t) axis & sTRAITS::axis_mask) << sTRAITS::reserved_bits);
  }

  __HOST__DEVICE__
  asINT32 axis() const
  {
    return (asINT32) ((m_index >> sTRAITS::reserved_bits) & sTRAITS::axis_mask);
  }

  VOID set_part(asINT32 part)
  {
    cassert(part >= 0 );
    cassert(part < sTRAITS::entity_mask+1);
    m_index |= ((uintptr_t) part & sTRAITS::entity_mask) << sTRAITS::reserved_axis_bits;
  }

  asINT32 part() const
  {
    return (asINT32) (m_index >> sTRAITS::reserved_axis_bits) & sTRAITS::entity_mask;
  }

  VOID set_face(asINT32 face)
  {
    set_part(face);
  }

  asINT32 face() const
  {
    return part();
  }

  // do not overwrite the last three bits
  VOID set_variables(MEAS_CELL_VAR* vars)
  {
    m_vars = (MEAS_CELL_VAR *) scalar_or_pointer(vars, (m_index & sTRAITS::reserved_mask));
  }

  __HOST__DEVICE__
  MEAS_CELL_VAR* variables() const
  {
    return (MEAS_CELL_VAR *) scalar_mask_pointer(m_vars, ~sTRAITS::reserved_mask);
  }

  __HOST__DEVICE__
  sSTD_CELL_VAR *std_cell_ptr(asINT32 offset) const
  {
    MEAS_CELL_VAR * tmp = variables() + offset;
    sSTD_CELL_VAR * res;
    memcpy(&res, &tmp, sizeof(MEAS_CELL_VAR*)); // memcpy avoids undefined behavior
    return res;
  }

  // This is only relevant for fluid meas windows, not surface meas windows.
  VOID mark_is_another_for_window()
  {
    m_vars = (MEAS_CELL_VAR *) scalar_or_pointer(m_vars, sTRAITS::is_another_for_window_mask);
  }

  VOID unmark_is_another_for_window()
  {
    m_vars = (MEAS_CELL_VAR *) scalar_mask_pointer(m_vars, ~sTRAITS::is_another_for_window_mask);
  }

  bool is_another_for_window() const
  {
    return scalar_mask_pointer_to_int(m_vars, sTRAITS::is_another_for_window_mask);
  }

  void mark_should_rotate_vector_to_grf()
  {
    m_vars = (MEAS_CELL_VAR *) scalar_or_pointer(m_vars, sTRAITS::should_rotate_vector_to_grf_mask);

  }
  void unmark_should_rotate_vector_to_grf()
  {
    m_vars = (MEAS_CELL_VAR *) scalar_mask_pointer(m_vars, ~sTRAITS::should_rotate_vector_to_grf_mask);
  }

  __HOST__DEVICE__
  bool should_rotate_vector_to_grf() const
  {
    return scalar_mask_pointer_to_int(m_vars, sTRAITS::should_rotate_vector_to_grf_mask);
  }

  __HOST__DEVICE__
  bool is_null() const
  {
    return m_vars == nullptr;
  }
  
};

#endif
