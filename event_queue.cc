/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include "async_events.h"
#include "event_queue.h"
#include "meas.h"
#include "sim.h"
#include "shob_groups.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "particle_sim_info.h"


#include <sys/wait.h>

#include PHYSICS_H

#define DEBUG_EVENTS 0

// 5 millisecond delay period
#define SP_POLL_DELAY 5 

EVENT_ID sEVENT_QUEUE::remove_recurring_event(EVENT_ID id)
{
  LOG_MSG("ASYNC", LOG_FUNC, LOG_TS, LOG_ATTR(id));

  bool event_removed = false;

  auto is_recurring = [id,&event_removed] (const std::unique_ptr<sEVENT_QUEUE_EVENT>& e) {
    if ((e->id == id) && e->recur_period > 0) {
      event_removed = true;
      return true;
    }
    return false;
  };

  cLOCK l = get_lock();
  queue.remove_if(is_recurring);

  return event_removed ? id : EVENT_ID_INVALID;
}

VOID sEVENT_QUEUE::add_entry(std::unique_ptr<sEVENT_QUEUE_EVENT> entry, sEVENT_QUEUE::cLOCK l)
{

  LOG_MSG("ASYNC", LOG_FUNC, LOG_TS, "event", entry->id, "add_timestep", entry->timestep);
  if( !l.is_locked() ) {
    l = std::move(get_lock());
  }

  // An SP that has sped ahead of everyone else could
  // be waiting for another event that won't happen
  // anymore. This signals to the main thread that it
  // needs to stop trying to process that event.
  if (entry->timestep < g_timescale.m_time) {
    if ( entry->id == EVENT_ID_EXIT ) {
      sEVENT_QUEUE_EVENT::set_received_exit_event();
    }
  }

  /* If event is meant to be unique, purge any other of that type */
  if (entry->unique()) {
    EVENT_ID id = entry->id;
    
    auto has_same_id = [id] (const std::unique_ptr<sEVENT_QUEUE_EVENT>& e) -> bool {
      return (e->id == id);
    };

    queue.remove_if(has_same_id);
  }

  /* Search for proper location to insert */
  {
    auto previous_it = queue.before_begin();
    auto current_it = queue.begin();

    for ( ; current_it != queue.end(); ++previous_it, ++current_it) {
      std::unique_ptr<sEVENT_QUEUE_EVENT>& current_entry = *current_it;

      /* If we've found an entry beyond (in time) the one we're placing, or we've found one
         that has an event id exceeding the new entry which is AT the current timestep, then
         insert the entry right here... */
      if ((current_entry->timestep > entry->timestep) || 
         ((current_entry->timestep == entry->timestep) && (current_entry->id > entry->id))) {
        queue.insert_after(previous_it,std::move(entry));
        return;
        /* Else, if we have a completely redundant event, update the old event */
      } else if ((current_entry->timestep == entry->timestep) &&
                 (current_entry->arg == entry->arg) &&
                 (current_entry->id ==  entry->id)) {
        if ((entry->recur_period > 0) && (current_entry->recur_period <= 0)) {
          current_entry->recur_period = entry->recur_period;
          current_entry->recur_end = entry->recur_end;
        }
        return;
      }
    }

    /* If we didn't find a location to insert, must be that we're just putting it
       at the beginning of an empty list, or at the very end */
    queue.insert_after(previous_it,std::move(entry));
  }

  return;
}

std::unique_ptr<sEVENT_QUEUE_EVENT> sEVENT_QUEUE::pop_if_next_timestep(BASETIME next_timestep) {
  auto l = get_lock();
  std::unique_ptr<sEVENT_QUEUE_EVENT> event{nullptr};
  if ( !queue.empty() ) {
    auto& head = queue.front();
    if (!g_strand_mgr.m_timestep_last_events_unlocked.load(std::memory_order_acquire) && head->timestep == BASETIME_LAST) {
      return nullptr;
    }

    if ( head->timestep <= next_timestep ) {
      event = std::move(head);
      queue.pop_front();
    }
  }

  LOG_MSG_IF(event,"ASYNC",LOG_FUNC, LOG_TS, "id",event->id);
  return event;
}

// called by the main thread
VOID sEVENT_QUEUE::process_queue(BASETIME current_time)
{
  BASETIME next_timestep = current_time;

  while (true) {
    auto event = pop_if_next_timestep(next_timestep);
    if (event == nullptr) break;
    if (event->id == EVENT_ID_EXIT) {
      next_timestep = event->timestep; // ensures that no events pop beyond this time-step
    }
    event->process();
    append_recurring_event(std::move(event));
  }

  // The exit event can be triggered in the above loop
  // So handle this now, before exiting this loop

  if(g_strand_mgr.m_exit) {
    if (!g_strand_mgr.m_halt) {
      // Advance the event queue head to t == BASETIME_LAST
      {
        auto not_last_timestep = [] (const std::unique_ptr<sEVENT_QUEUE_EVENT>& e) -> bool {
          return e->timestep != BASETIME_LAST;
        };

        cLOCK l = get_lock();
        queue.remove_if(not_last_timestep);
      }
    }
  }
}
 
VOID add_event(std::unique_ptr<sEVENT_QUEUE_EVENT> event, sEVENT_QUEUE::cLOCK l)
{
  g_async_event_queue.add_entry(std::move(event), std::move(l));
}

VOID process_event_replies()
{
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
  sASYNC_EVENT_MSG msg;
  RECV_EXA_SIM_MSG<sASYNC_EVENT_MSG, 1> event_msg(eMPI_ASYNC_EVENT_MSG_TAG, eMPI_sp_cp_rank());
  g_exa_sp_cp_comm.recv(event_msg.mpi_msg);
  msg = *event_msg.return_buffer();
  auto new_entry = new_event(msg.event_id, msg.arg, msg.timestep);
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS).format("Received event request ID {}",msg.event_id);
  switch(new_entry->id) {
    /* Transform the checkpoint & exit event into a checkpoint
     * at end of run event, and a reformulated end of run event */
    case EVENT_ID_MME_CKPT_EXIT: {
      auto mme_ckpt_event = new_event(EVENT_ID_MME_CKPT, new_entry->arg, BASETIME_LAST);
      auto exit_event = new_event(EVENT_ID_EXIT, -1, new_entry->timestep);
      // add the checkpoint first to ensure it gets added properly
      add_event(std::move(mme_ckpt_event));
      add_event(std::move(exit_event));
      break;
    };
    case EVENT_ID_FULL_CKPT_EXIT: {
      auto full_ckpt_event = new_event(EVENT_ID_FULL_CKPT, new_entry->arg, BASETIME_LAST);
      auto exit_event = new_event(EVENT_ID_EXIT, -1, new_entry->timestep);
      // add the checkpoint first to ensure it gets added properly
      add_event(std::move(full_ckpt_event));
      add_event(std::move(exit_event));
      break;
    };
  case EVENT_ID_START_EMITTER: {
    // No need to add an entry since we can set the emitter start time here
    asINT32 n_emitters = new_entry->arg;
    //msg_print("At sim time %ld start %d emitters at timestep %d.", g_timescale.m_time, n_emitters, new_entry->timestep);
    static asINT32 *recv_buffer = new asINT32 [n_emitters];
    MPI_Status mpi_status;
    MPI_Recv(recv_buffer, n_emitters, eMPI_sINT32, eMPI_sp_cp_rank(), eMPI_EMITTER_ID_TAG, eMPI_sp_cp_comm, &mpi_status);
    ccDOTIMES(i, n_emitters) {
      TIMESTEP flow_timestep = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(new_entry->timestep);
      g_particle_sim_info.emitters[recv_buffer[i]]->set_start_time(flow_timestep);
    }
    break;                     
  }
case EVENT_ID_START_WIPER: {
    // No need to add an entry since we can set the emitter start time here
    asINT32 n_wipers = new_entry->arg;
    //msg_print("At sim time %ld start %d wipers at timestep %d.", g_timescale.m_time, n_wipers, new_entry->timestep);
    static asINT32 *recv_buffer = new asINT32 [n_wipers];
    MPI_Status mpi_status;
    MPI_Recv(recv_buffer, n_wipers, eMPI_sINT32, eMPI_sp_cp_rank(), eMPI_WIPER_ID_TAG, eMPI_sp_cp_comm, &mpi_status);
    ccDOTIMES(i, n_wipers) {
      TIMESTEP flow_timestep = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(new_entry->timestep);
      g_particle_sim_info.wipers[recv_buffer[i]]->set_start_time(flow_timestep);
    }
    break;                     
  }
    default: {
      add_event(std::move(new_entry));		/* Event's ok, just add it */
      break;
    };
  }
}


VOID sEVENT_QUEUE::print_queue()
{
  sINT32 entrys_encountered = 0;

  for( const auto& entry : queue ) {
    entrys_encountered++;
    entry->print();
  }
  msg_print("Event queue has %d entries", entrys_encountered);
}

void sEVENT_QUEUE::append_recurring_event(std::unique_ptr<sEVENT_QUEUE_EVENT> entry)
{
  BASETIME next_timestep;

  if (entry->id == EVENT_ID_READ_COUPLING_DATA || entry->id == EVENT_ID_ROTATIONAL_DYNAMICS || entry->id == EVENT_ID_UNPACK_TBS_DATA) {
    auto realm_fn = &sim.realm_phase_time_info[STP_FLOW_REALM];
    next_timestep = realm_fn->realm_to_global_timestep(realm_fn->global_to_realm_timestep(entry->timestep) + entry->recur_period);
  } else if (entry->id == EVENT_ID_READ_TABLE) {
    asINT32 table_index = entry->arg;
    TABLE table = sim.tables + table_index;
    auto realm_fn = &sim.realm_phase_time_info[table->time_rel_realm];
    next_timestep = realm_fn->realm_to_global_timestep(realm_fn->global_to_realm_timestep(entry->timestep) + entry->recur_period);
  }
  else {
    next_timestep = entry->timestep + entry->recur_period;
  }

  if ((entry->recur_period > 0) && (next_timestep <= entry->recur_end)) {
    auto next_event = new_event(entry->id, entry->arg, next_timestep, entry->recur_period, entry->recur_end);
    add_entry(std::move(next_event));

#if DEBUG_VARIABLE_POWERTHERM_COUPLING
  msg_print("SP: at timestep %ld new entry %d occuring at timestep %d inserted in event_queue", g_timescale.m_time, entry->id, next_timestep);
#endif
  }
}


// The arg is an MPI tag, which is type int, so it's not appropriate to use one
// of our specialized int types. This tag is a eMPI_MSG_VTABLE tag.
BOOLEAN receive_vtable(int mpi_tag) 
{

  asINT32 table_index = mpi_msg_index(mpi_tag);

  TABLE table = sim.tables + table_index;

  if(table->ready) {
    // Results of last read have not been used yet. This one must be deferred.
    return FALSE;
  }

  // Receive the table size (header and data sequentially)
  VTABLE_XCHG_SIZE sizes[2];
  RECV_EXA_SIM_MSG<VTABLE_XCHG_SIZE, 2> vtable_size(mpi_tag, eMPI_sp_cp_rank());
  g_exa_sp_cp_comm.recv(vtable_size.mpi_msg);
  sizes[0] = vtable_size.return_buffer()[0];
  sizes[1] = vtable_size.return_buffer()[1];

  LOG_MSG("VTABLE",LOG_TIME,LOG_FUNC,"header",sizes[0],"data",sizes[1]);

  size_t table_size = sizes[0] + sizes[1];
  int owning_sp = table_index % g_sp_node_comm.nprocs();
  bool i_own_this_table = owning_sp == g_sp_node_comm.rank();

  // resize the shared_memory segment if the table is larger than what is currently allocated.
  if ( table_size > table->bytes_allocated ) {
    LOG_MSG("VTABLE",LOG_FUNC,"old size",table->bytes_allocated,"new size",table_size) << "Resize";
    table->bytes_allocated = table_size + 1024*1024;
    table->smem->Resize(table->bytes_allocated);

    if (i_own_this_table) {
      table->serialized_table = (char*) table->smem->Reserve(table->bytes_allocated);
    }
    else {
      table->serialized_table = (char*) table->smem->GetData();
    }
  }

  if ( i_own_this_table ) {
    RECV_EXA_SIM_MSG<char> table_header_msg(mpi_tag, sizes[0], table->serialized_table, eMPI_sp_cp_rank());
    RECV_EXA_SIM_MSG<char> table_data_msg(mpi_tag, sizes[1], table->serialized_table+sizes[0], eMPI_sp_cp_rank());
    g_exa_sp_cp_comm.irecv(table_header_msg.mpi_msg);
    complete_mpi_request_while_processing_cp_messages(&(table_header_msg.mpi_msg.m_request), MPI_SLEEP_LONG);

    g_exa_sp_cp_comm.irecv(table_data_msg.mpi_msg);
    complete_mpi_request_while_processing_cp_messages(&(table_data_msg.mpi_msg.m_request), MPI_SLEEP_LONG);

    // send a notification that the data has been stored
    cExaMsg<int,0> null_send{};
    null_send.settag(mpi_tag);
    ccDOTIMES(node_sp, g_sp_node_comm.nprocs()) {
      null_send.init(g_sp_node_comm.rank(),node_sp);
      g_sp_node_comm.isend(null_send);
      g_sp_node_comm.free(null_send);
    }
  }

  // wait for the notification from the owning sp that the data has been 
  // written, so now we can read from it
  cExaMsg<int,0> null_recv{};
  null_recv.settag(mpi_tag);
  null_recv.init(owning_sp,g_sp_node_comm.rank());
  g_sp_node_comm.recv(null_recv);

  table->ready = TRUE;
  LOG_MSG("VTABLE",LOG_TIME,LOG_FUNC) << "table->ready = TRUE";

  return TRUE;
}


BOOLEAN receive_ptherm_time_info()
{
  RECV_EXA_SIM_MSG<sPT_TIME_INFO> pt_time_data(eMPI_POWERTHERM_TIME_INFO_TAG, 
                                               1, &(sim.m_new_pt_time_info), 
                                               eMPI_sp_cp_rank() );
  g_exa_sp_cp_comm.recv(pt_time_data.mpi_msg);
#if DEBUG_T_PTHERM_EQNS
  if (my_proc_id == 0) {
    msg_print("T: %ld receive pf_start_time %d pt_start_time %f pt_pf_ratio %f", 
              g_timescale.m_time, 
              sim.m_new_pt_time_info.pf_start_time,
              sim.m_new_pt_time_info.pt_start_time, 
              sim.m_new_pt_time_info.pt_pf_time_ratio);
  }
#endif
  return TRUE;
}

BOOLEAN receive_coupling_model(int mpi_tag) 
{

  MPI_Status status;
  asINT32 model_index = mpi_msg_index(mpi_tag); 

  COUPLING_MODEL coupling_model = sim.coupling_models + model_index;

  if(coupling_model->ready) {
    // Results of last read have not been used yet. This one must be deferred.
    return FALSE;
  }

  asINT32 data_size = coupling_model->nsurfels * coupling_model->n_vars;
  if (data_size <= 0)
    return TRUE; // no data to process

  if (!sim.coupling_data_buf) {
    msg_internal_error("Uninitialized coupling data buffer");
  }
  RECV_EXA_SIM_MSG<sFLOAT> coupling_data(mpi_tag, data_size, sim.coupling_data_buf , eMPI_sp_cp_rank() );
  g_exa_sp_cp_comm.recv(coupling_data.mpi_msg);

  coupling_model->ready = TRUE;
  return TRUE;
}

BOOLEAN receive_rotational_dynamics(int mpi_tag)
{

  MPI_Status status;
  asINT32 rotational_dynamics_index = mpi_msg_index(mpi_tag); 
  ROTATIONAL_DYNAMICS_DESC rotational_dynamics_desc = sim.rotational_dynamics_descs + rotational_dynamics_index;

  if(rotational_dynamics_desc->ready) {
    // Results of last read have not been used yet. This one must be deferred.
    return FALSE;
  }
  RECV_EXA_SIM_MSG<dFLOAT, 1> angular_accelaration(mpi_tag, eMPI_sp_cp_rank());
  g_exa_sp_cp_comm.recv(angular_accelaration.mpi_msg);
  rotational_dynamics_desc->angular_acceleration_buffer = *angular_accelaration.return_buffer();
  rotational_dynamics_desc->ready = TRUE;
  return TRUE;
}

BOOLEAN maybe_receive_dsm(MPI_Status &probed_mpi_status)
{
  MPI_Status mpi_status;

  // dsm_ready tells the sim thread the dsms are ready to be used,
  // not the comm thread that they are ready to be communicated
  if(g_ublk_table[STP_FLOW_REALM].dsm_ready) {
    // Results of last read have not been used yet. This one must be deferred.
    return FALSE;
  }

  int n_dsms;
  MPI_Get_count(&probed_mpi_status,eMPI_sFLOAT,&n_dsms);
  g_ublk_table[STP_FLOW_REALM].maybe_allocate_dsm(n_dsms);
  RECV_EXA_SIM_MSG<sFLOAT> table_multipliers(eMPI_ASYNC_DSM_DATA_TAG, n_dsms, g_ublk_table[STP_FLOW_REALM].multipliers, eMPI_sp_cp_rank() );
  g_exa_sp_cp_comm.recv(table_multipliers.mpi_msg);
  g_ublk_table[STP_FLOW_REALM].dsm_ready = TRUE;

  return TRUE;
}


BOOLEAN maybe_post_recv_for_tbs_data(int mpi_tag)
{
  asINT32 imeas = mpi_msg_index(mpi_tag);
  if(!sim.transient_boundary_seeding[imeas].m_ready_to_recv) {
    return FALSE;
  }
  sim.transient_boundary_seeding[imeas].post_recv(imeas);
  return TRUE;
}

VOID test_posted_tbs_recvs()
{
  ccDOTIMES(imeas, sim.n_seed_from_meas_descs) {
    g_exa_sp_cp_comm.test(sim.transient_boundary_seeding[imeas].m_tbs_msg);
    if(sim.transient_boundary_seeding[imeas].m_tbs_msg.m_flag)
      sim.transient_boundary_seeding[imeas].m_recvd_data = TRUE;
  }
}
