/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Basic lattice definitions
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_LATTICE_H
#define _SIMENG_LATTICE_H

#include "common_sp.h"
#include "bitset.h"

/*--------------------------------------------------------------------------*
 * Basic state definitions
 *--------------------------------------------------------------------------*/

typedef STP_LB_STATE SURFEL_STATE;
typedef STP_LB_STATE VOXEL_STATE;

typedef STP_LB_STATE SURFEL_S2S_MASS;

#define eMPI_SURFEL_STATE eMPI_STP_LB_STATE

/*--------------------------------------------------------------------------*
 * Time Index of ublk variables                                             
 *--------------------------------------------------------------------------*/
#if BUILD_5G_LATTICE
#define N_TIME_INDICES    1
#else
#define N_TIME_INDICES    2
#endif

#define MULTI_TIME_INDEX (N_TIME_INDICES -1)

/*--------------------------------------------------------------------------*
 * Time Index of S2S variables                                             
 *--------------------------------------------------------------------------*/
#define N_S2S_TIME_INDICES 2

/*------------------------------------------------------------------------------*
 * Even vs odd timesteps for a scale (even preceeds odd since timestep 0 is even)
 *------------------------------------------------------------------------------*/
#define sim_prior_timestep_index_mask(scale, timestep) \
  (sim_is_timestep_odd(scale, timestep) ? sim.init_solver_mask : 0)
/* This is often used to swap back-and-forth between a pair of values */
#if MULTI_TIME_INDEX

#define sim_prior_timestep_index_one_finer() 1
#define sim_next_index(prior_index)    ((prior_index) ^ 1)
#else
#define sim_prior_timestep_index_one_finer() 0
#define sim_next_index(prior_index)  0
#endif

typedef STP_SCALE SCALE;


/*--------------------------------------------------------------------------*
 * Axes
 *--------------------------------------------------------------------------*/
typedef STP_AXIS 	AXIS;
typedef cSTP_AXIS 	cAXIS;
#define X_AXIS 		STP_X_AXIS
#define Y_AXIS 		STP_Y_AXIS
#define Z_AXIS 		STP_Z_AXIS
#define NO_AXIS 	STP_NO_AXIS
#define MAX_AXIS 	STP_MAX_AXIS

/*--------------------------------------------------------------------------*
 * Orthogonal directions
 *--------------------------------------------------------------------------*/

#define POS_X_DIR 		STP_POS_X_FACE
#define NEG_X_DIR 		STP_NEG_X_FACE
#define POS_Y_DIR 		STP_POS_Y_FACE
#define NEG_Y_DIR 		STP_NEG_Y_FACE
#define POS_Z_DIR 		STP_POS_Z_FACE
#define NEG_Z_DIR 		STP_NEG_Z_FACE
#define N_FACES                 STP_N_FACES

/*--------------------------------------------------------------------------*
 * Bits in ublk connect_masks
 *--------------------------------------------------------------------------*/
#define VOXEL_FINE_SCALE_BIT       0
#define VOXEL_COARSE_SCALE_BIT     1

/*-----------------------------------------------*
 * Voxel masks 
 *-----------------------------------------------*/

/* Voxel bitmask 
 *   The voxel mask bits are assigned to voxels using the following
 *   mapping:  (Bit 0 is the LSB and Bit 7 is the MSB of the byte)
 *     Bit    	  Voxel	       Bit	  Voxel
 *	0	-X -Y -Z	4	+X -Y -Z
 *	1	-X -Y +Z	5	+X -Y +Z
 *	2	-X +Y -Z	6	+X +Y -Z
 *	3	-X +Y +Z	7	+X +Y +Z
 */
using VOXEL_MASK_8 = tBITSET<N_VOXELS_8>;
using VOXEL_MASK_64 = tBITSET<N_VOXELS_64>;
using MBLK_VOXEL_MASK = tBITSET<N_VOXELS_64>;
using MSFL_MASK = tBITSET<N_SFLS_PER_MSFL>;

template<typename VOXEL_MASK>
__HOST__DEVICE__ VOXEL_MASK default_voxel_mask(BOOLEAN is_2d);

template<>
__HOST__DEVICE__ inline VOXEL_MASK_8 default_voxel_mask<VOXEL_MASK_8>(BOOLEAN is_2d) {
  return VOXEL_MASK_8(is_2d? 0x55 : 0xFF);
}

template<>
__HOST__DEVICE__ inline VOXEL_MASK_64 default_voxel_mask<VOXEL_MASK_64>(BOOLEAN is_2d) {
  return VOXEL_MASK_64(is_2d? 0x5555555555555555 : 0xFFFFFFFFFFFFFFFF);
}

// typedef uINT8 VOXEL_MASK;
typedef uINT8 VOXEL_NUM;

constexpr VOXEL_MASK_8 _axis_delta_to_voxel_mask_table[N_AXES][2] = {
  {VOXEL_MASK_8{0x0F}, VOXEL_MASK_8{0xF0}}, {VOXEL_MASK_8{0x33}, VOXEL_MASK_8{0xCC}}, {VOXEL_MASK_8{0x55}, VOXEL_MASK_8{0xAA}}
};

/* This returns a voxel mask for just the half of the voxels that are at */
/* the "delta" (high or low) end of the given axis */
constexpr VOXEL_MASK_8 axis_delta_to_voxel_mask(uINT8 axis, sINT8 delta)
{
  return _axis_delta_to_voxel_mask_table[axis][delta > 0];
}

__HOST__DEVICE__
constexpr VOXEL_MASK_8 child_ublk_mask(VOXEL_MASK_64 ublk_mask, uINT8 child_ublk) 
{
  return VOXEL_MASK_8( ublk_mask.get() >> N_VOXELS_8*child_ublk);
}

__HOST__DEVICE__
constexpr VOXEL_MASK_8 child_ublk_mask(VOXEL_MASK_8 ublk_mask, [[maybe_unused]] uINT8 child_ublk) 
{
  return ublk_mask;
}

#undef is_voxel_in_mask
template<size_t N, typename INTERNAL_TYPE>
__HOST__DEVICE__ constexpr bool is_voxel_in_mask(VOXEL_NUM voxel, tBITSET<N, INTERNAL_TYPE> mask) {
  return mask.test(voxel);
}

__HOST__DEVICE__ constexpr bool is_voxel_in_mask(VOXEL_NUM voxel, uINT8 mask) {
  return mask & (0x1 << voxel);
}

#define voxel_num_to_mask(voxel)	(1 << (voxel))

#if DEVICE_COMPILATION_MODE

#undef DO_VOXELS_IN_MASK
#define DO_VOXELS_IN_MASK(voxel_var, mask)                        \
  for(asINT32 __voxel_var = threadIdx.x % ubFLOAT::N_VOXELS,      \
      voxel_var = __voxel_var; \
      voxel_var < __voxel_var + 1; \
      voxel_var++)	\
    if ( mask.test(voxel_var) )

#else

#undef DO_VOXELS_IN_MASK
#define DO_VOXELS_IN_MASK(voxel_var, mask)                              \
  for(asINT32 voxel_var = 0; voxel_var < N_VOXELS_8; voxel_var++)	\
    if ( mask.test(voxel_var) )

#endif

// DO_ACTIVE_VOXELS iterates over voxels 0,2,4,6 for 2D cases
#define DO_ACTIVE_VOXELS(voxel_var)                                             \
  asINT32 ___(voxel_inc) = 4 - sim.num_dims;                                    \
  for(asINT32 voxel_var = 0; voxel_var < ubFLOAT::N_VOXELS; voxel_var += ___(voxel_inc))

#if DEVICE_COMPILATION_MODE
//Device version of DO_ACTIVE_VOXELS_IN_MASK is identical to DO_VOXELS_IN_MASK
//For 2D it is expected that the input mask accounts for the missing voxels
#define DO_ACTIVE_VOXELS_IN_MASK(voxel_var, mask)                                               \
  for(asINT32 __voxel_var = threadIdx.x % ubFLOAT::N_VOXELS,      \
      voxel_var = __voxel_var; \
      voxel_var < __voxel_var + 1; \
      voxel_var++)	\
    if ( mask.test(voxel_var) )
#else
#define DO_ACTIVE_VOXELS_IN_MASK(voxel_var, mask)                                               \
  asINT32 ___(voxel_inc) = 4 - sim.num_dims;                                                    \
  for(asINT32 voxel_var = 0; voxel_var < ubFLOAT::N_VOXELS; voxel_var += ___(voxel_inc))        \
    if (mask.test(voxel_var))
#endif

/* Converting between voxel numbers and coordinates (either 0 or 1) */
#define voxel_to_num(_x,_y,_z) (((_x) << 2) + ((_y) << 1) + (_z))
#define num_to_voxel_x(_num) ((_num) >> 2)
#define num_to_voxel_y(_num) (((_num) >> 1) & 1)
#define num_to_voxel_z(_num) ((_num) & 1)

inline uINT8 voxel_num_from_voxel_offsets(sINT32 x_offset, sINT32 y_offset, sINT32 z_offset) {
  return x_offset*4 + y_offset*2 + z_offset;
}

#define voxel_axis_to_num_offset(_axis) (1 << (Z_AXIS - (_axis)))

#define TWO_D_VR_FINE_UBLK_NUM_TO_COARSE_VOXEL_NUM(_num) 		\
    ((_num) << 1)	/* assumes Z is 2D wrap axis */

/* This returns the width of a voxel */
#define voxel_size(_scale) \
  (1 << (sim.num_scales - scale_to_index(_scale) - 1))

/* NEIGHBOR_VOXEL_ALONG_AXIS returns the voxel number for the 2 voxels that abut
 * the argument VOXEL along the given AXIS. Both the front and the back neighbor
 * have the same number independent of whether they reside in the same or different
 * ublks.
 */
__HOST__DEVICE__
constexpr asINT32 neighbor_voxel_along_axis(asINT32 voxel, asINT32 axis)
{
  cassert(voxel >= 0);
  cassert(voxel < N_VOXELS_8);
  asINT32 axis_mask = 4 >> axis;
  asINT32 neighbor_voxel = voxel ^ axis_mask;
  return neighbor_voxel;
}

const asINT32 NVELS = 1+2*MAX_STATE_COMPONENT_VEL; // the extra 1 is for the 0 velocity
const asINT32 NVELS_UBLK_SCALE = MAX_STATE_COMPONENT_VEL == 1? 
                                 1 + 2*MAX_STATE_COMPONENT_VEL :
                                 1 + 2*(MAX_STATE_COMPONENT_VEL-1); 
const asINT32 MAX_STATE_COMPONENT_VEL_UBLK_SCALE = MAX_STATE_COMPONENT_VEL == 1? 1 : (MAX_STATE_COMPONENT_VEL-1); 

extern STP_STATE_INDEX g_state_vel_to_index[NVELS][NVELS][NVELS];

extern STP_UBLK_NEIGHBOR_INDEX 
g_ublk_neighbor_vec_to_index[NVELS_UBLK_SCALE][NVELS_UBLK_SCALE][NVELS_UBLK_SCALE];

VOID init_state_vel_map();
VOID init_ublk_neighbor_vector_index_map();

inline STP_UBLK_NEIGHBOR_INDEX 
convert_ublk_neighbor_vector_to_index(STP_UBLK_NEIGHBOR_VEC vec)
{
  const int n = MAX_STATE_COMPONENT_VEL_UBLK_SCALE;
  if (abs(vec[0]) > n || abs(vec[1]) > n || abs(vec[2]) > n)
    msg_internal_error("Incorrect ublk neighbor vector");
 
  return g_ublk_neighbor_vec_to_index[n + vec[0]][n + vec[1]][n + vec[2]]; 
}

inline STP_UBLK_NEIGHBOR_MASK convert_ublk_neighbor_vector_to_mask(sINT8 xvec,
                                                                   sINT8 yvec,
                                                                   sINT8 zvec)
{
  STP_UBLK_NEIGHBOR_VEC vec = {xvec, yvec, zvec};
  asINT32 rval = convert_ublk_neighbor_vector_to_index(vec);
  if (rval == STP_INVALID_UBLK_NEIGHBOR)
    return 0x0;
  else
    return (STP_UBLK_NEIGHBOR_MASK(0x1) << rval);
}



/*--------------------------------------------------------------------------*
 * Tables of constants
 *--------------------------------------------------------------------------*/
// Use lattice_vector_pde_table which is actually state_table_d25 for d19 lattice in update_split_ublk_instance()
#if BUILD_D19_LATTICE
extern sSTATE_TABLE_ENTRY lattice_vector_pde_table[N_LATTICE_VECTORS_PDE]; 
#endif

extern sSTATE_TABLE_ENTRY state_table[N_STATES];
extern sNEIGHBOR_UBLK_VECTOR neighbor_ublk_vector_table[];

// mirror related lookup tables
extern STP_STATE_INDEX mirror_state_order[N_STATES];

extern asINT8 _state_energy[N_STATES];
extern SURFEL_STATE _float_state_vel[N_STATES][3];
extern SURFEL_STATE _float_state_energy[N_STATES];
extern SURFEL_STATE _latvec_pair_energy_one_weight[N_LATTICE_VECTOR_PAIRS];
extern SURFEL_STATE _latvec_pair_energy_one_float[N_LATTICE_VECTOR_PAIRS];
extern SURFEL_STATE _latvec_pair_first_layer_weight[N_LATTICE_VECTOR_PAIRS];
extern SURFEL_STATE _latvec_pair_first_layer_float[N_LATTICE_VECTOR_PAIRS];
extern SURFEL_STATE _float_state_weight[N_STATES];

#if GPU_COMPILER
namespace GPU {

#if BUILD_D19_LATTICE
  __CONSTANT__ __DEVICE__ extern sSTATE_TABLE_ENTRY lattice_vector_pde_table[N_LATTICE_VECTORS_PDE]; 
#endif

  __CONSTANT__ __DEVICE__ extern sSTATE_TABLE_ENTRY state_table[N_STATES];
  __CONSTANT__ __DEVICE__ extern sNEIGHBOR_UBLK_VECTOR neighbor_ublk_vector_table[];
  __CONSTANT__ __DEVICE__ extern asINT8 _state_energy[N_STATES];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _float_state_vel[N_STATES][3];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _float_state_energy[N_STATES];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _latvec_pair_energy_one_weight[N_LATTICE_VECTOR_PAIRS];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _latvec_pair_energy_one_float[N_LATTICE_VECTOR_PAIRS];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _latvec_pair_first_layer_weight[N_LATTICE_VECTOR_PAIRS];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _latvec_pair_first_layer_float[N_LATTICE_VECTOR_PAIRS];
  __CONSTANT__ __DEVICE__ extern SURFEL_STATE _float_state_weight[N_STATES];
  __CONSTANT__ __DEVICE__ extern STP_STATE_INDEX g_state_vel_to_index[NVELS][NVELS][NVELS];  
}
#endif

__HOST__DEVICE__ inline STP_STATE_INDEX convert_state_vel_to_latvec_index(STP_STATE_VEL v[N_AXES])
{
#if DEVICE_COMPILATION_MODE
  return GPU::g_state_vel_to_index[v[0]+MAX_STATE_COMPONENT_VEL] 
				  [v[1]+MAX_STATE_COMPONENT_VEL] 
				  [v[2]+MAX_STATE_COMPONENT_VEL];
#else  
  return g_state_vel_to_index[v[0]+MAX_STATE_COMPONENT_VEL] 
                             [v[1]+MAX_STATE_COMPONENT_VEL] 
                             [v[2]+MAX_STATE_COMPONENT_VEL];
#endif
}

__DEVICE__ inline STP_LATVEC_MASK convert_state_vel_to_latvec_mask(STP_STATE_VEL vx, STP_STATE_VEL vy, STP_STATE_VEL vz)
{
  STP_STATE_VEL vel[N_AXES] = {vx, vy, vz};
  asINT32 rval = convert_state_vel_to_latvec_index(vel);
  if (rval == STP_INVALID_STATE_INDEX)
    return 0x0;
  else
    return (STP_LATVEC_MASK(0x1) << rval);
}

/* IS_STATE_DUAL indicates which states are dual states. Each entry is guaranteed 
 * to be 0 or 1 so that it may be used as a shift count.
*/
extern csINT8 is_state_dual[N_STATES];

#if BUILD_D19_LATTICE
#define state_vx(i) 			(HD_NAMESPACE::lattice_vector_pde_table[(i)].vel[0])
#define state_vy(i) 			(HD_NAMESPACE::lattice_vector_pde_table[(i)].vel[1])
#define state_vz(i) 			(HD_NAMESPACE::lattice_vector_pde_table[(i)].vel[2])

#define state_vel(i, component) 	(HD_NAMESPACE::lattice_vector_pde_table[(i)].vel[(component)])
#define state_energy(i)   		(HD_NAMESPACE::lattice_vector_pde_table[(i)].energy)
#define state_weight(i)   		(HD_NAMESPACE::lattice_vector_pde_table[(i)].weight)

#else  // state_table is state_table_d19 for 5G and state_table_d39 D39
#define state_vx(i) 			(state_table[(i)].vel[0])
#define state_vy(i) 			(state_table[(i)].vel[1])
#define state_vz(i) 			(state_table[(i)].vel[2])

#define state_vel(i, component) 	(state_table[(i)].vel[(component)])
#define state_energy(i)   		(state_table[(i)].energy)
#define state_weight(i)   		(state_table[(i)].weight)
#endif

#define state_energy_index(i)		(HD_NAMESPACE::_state_energy[(i)])

#define float_state_weight(i)		(HD_NAMESPACE::_float_state_weight[(i)])

#define float_state_vel(i)		(HD_NAMESPACE::_float_state_vel[(i)])
                                        
#define latvec_pair_energy_one_weight(i)	(HD_NAMESPACE::_latvec_pair_energy_one_weight[i])
#define latvec_pair_energy_one_float(i)	(HD_NAMESPACE::_latvec_pair_energy_one_float[i])
#define latvec_pair_first_layer_weight(i)	(HD_NAMESPACE::_latvec_pair_first_layer_weight[i])
#define latvec_pair_first_layer_float(i)	(HD_NAMESPACE::_latvec_pair_first_layer_float[i])


#define g_state_table 		        state_table

#define g_mirror_state_order            mirror_state_order

VOID init_lattice_tables();


constexpr sINT32 face_index_to_axis(asINT32 face_index) {
  return (face_index >> 1);
}

constexpr sINT16 face_index_to_direction(asINT32 face_index) {
  return ((face_index & 1) * -2 + 1);
}

constexpr asINT32 face_index(asINT32 axis, sINT16 dir) {
  return (2* axis + (asINT32((-dir + 1)/2)));
}

inline STP_LATVEC_MASK convert_state_index_to_face_mask(STP_STATE_INDEX latvec) 
{
  STP_LATVEC_MASK face_mask = 0;
  if (state_vx(latvec) > 0)  {
    face_mask |= (1 << STP_POS_X_FACE);
  } else if (state_vx(latvec) < 0)  {
    face_mask |= (1 << STP_NEG_X_FACE);
  }
  if (state_vy(latvec) > 0)  {
    face_mask |= (1 << STP_POS_Y_FACE);
  } else if (state_vy(latvec) < 0)  {
    face_mask |= (1 << STP_NEG_Y_FACE);
  }
  if (state_vz(latvec) > 0)  {
    face_mask |= (1 << STP_POS_Z_FACE);
  } else if (state_vz(latvec) < 0)  {
    face_mask |= (1 << STP_NEG_Z_FACE);
  }
  return face_mask;
}

//Find the nearest voxel or ublk location for the given position.
template <typename T>
asINT32 lattice_location_from_position(T position, asINT32 ublk_or_voxel_size){
  return floor(position / ublk_or_voxel_size) * ublk_or_voxel_size;  
}

#if defined(BUILD_D19_LATTICE) || defined(BUILD_5G_LATTICE)
constexpr size_t N_CONNECT_MASK_BITS = N_LATTICE_VECTORS + 6;
#else
constexpr size_t N_CONNECT_MASK_BITS = N_LATTICE_VECTORS;
#endif

using CONNECT_MASK = tBITSET<N_CONNECT_MASK_BITS>;
using CARDINAL_CONNECT_MASK = tBITSET<N_CARDINAL_DIRECTIONS>;

// The first 6 latvec directions are in the cardinal directions. This will
// implicitly chop off everything else.
__HOST__DEVICE__ constexpr CARDINAL_CONNECT_MASK to_cardinal_connect_mask(CONNECT_MASK mask) {
  return CARDINAL_CONNECT_MASK( static_cast<CARDINAL_CONNECT_MASK>(mask.get() & uINT32(0xFF)));
}

_INLINE_ constexpr CONNECT_MASK get_default_voxel_fluid_connect_mask() {
  CONNECT_MASK res;
  res.set_all();
  return res;
}


#endif /* __LATTICE_H */

