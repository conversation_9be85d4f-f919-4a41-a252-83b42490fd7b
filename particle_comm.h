/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2011, 1993-2010 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

//TO_BE_DONE_GHOST items need to be addressed
// Fix scale loops to only include active scale
// Need to segregate sample surface surfels by the window they were generated for.
// Need to segregate sample surface surfels by the particle screen they were generated for.
// re-enable LRF/MLRF supprot
// LRF references in p_data from voxel were needed in old code for emitters and maybe other things. see if they can be eliminated
// Make sure need for initialize_advect_only_parcel_lists() can be eliminated
// Eliminate the need for calls to initialize_split_voxel_window_masks(), perhaps integrate that function to a better place.
// reenable symetry planes, process_mirror_group_parcels_near_symmetry_plane needs work since there are no mirror groups anymore.
// Connectivity and half edge computations for film only ghost surfels needs to be re-enabled.
// Consider rewriting how space and time varying physics variables are used to be consistent with the rest of the simulator (as well as how their information is sent from the CP to the SP).
// Enable collision detection for screens and sample surfaces
// determine what density momentum variables in a surfel lb datablock to use to compute near wall velocity.

#ifndef PARTICLE_COMM_H_
#define PARTICLE_COMM_H_
#include "common_sp.h"
#include "comm_groups.h"

/*--------------------------------------------------------------------------*
 * sPARCEL_SEND_GROUP
 *--------------------------------------------------------------------------*/
typedef class sPARCEL_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>> {
  private:
    std::vector<unsigned char>  m_send_buffer;
    std::vector<PARCEL_STATE>   m_parcels_to_send;
    asINT32                     m_n_send_bytes;
    MPI_Request                 m_send_request;
    MPI_Request                 m_second_send_request;
    bool                        m_copied_to_send_buffer;

  public:
    sPARCEL_SEND_GROUP(cNEIGHBOR_SP dest_sp, cSTP_SCALE scale) {
      m_dest_sp      = dest_sp;
      m_scale        = scale;
      m_n_send_bytes = 0;
      m_send_request = MPI_REQUEST_NULL;
      m_second_send_request = MPI_REQUEST_NULL;
      m_send_type = PARCEL_SEND_TYPE;
      m_copied_to_send_buffer = false;
    }

    void reset_copied_to_send_buffer() {
      m_copied_to_send_buffer = false;
      cassert(m_parcels_to_send.size() == 0);
    }
    bool is_copied_to_send_buffer() {
      return m_copied_to_send_buffer;
    }
    virtual void send(ACTIVE_SOLVER_MASK active_solver_mask);

    void add_parcel(sPARCEL_STATE *parcel) {
      m_parcels_to_send.push_back(parcel);
    }

} *PARCEL_SEND_GROUP;

struct PARCEL_SEND_GROUP_ORDER {
 BOOLEAN operator() (PARCEL_SEND_GROUP a, PARCEL_SEND_GROUP b) const
 {
     return a->m_dest_sp < b->m_dest_sp;
 }
};

typedef struct sPARCEL_COMM_SEND_FSET :
 public tSP_FSET < PARCEL_SEND_GROUP, PARCEL_SEND_GROUP_ORDER > {

   private:
   bool m_ready_to_send;

 public:
   sPARCEL_COMM_SEND_FSET() : m_ready_to_send(FALSE) {};

   PARCEL_SEND_GROUP find_or_create_group (cNEIGHBOR_SP dest_sp) {
     sPARCEL_SEND_GROUP signature(dest_sp, sim.num_scales-1);

     PARCEL_SEND_GROUP group = find_group(&signature);
     if (NULL == group) {
       group = new sPARCEL_SEND_GROUP(dest_sp, FINEST_SCALE);
       add_group(group);
     }
     return group;
   }

   PARCEL_SEND_GROUP find_send_group (cNEIGHBOR_SP dest_sp) {
     sPARCEL_SEND_GROUP signature(dest_sp, FINEST_SCALE);

     PARCEL_SEND_GROUP group = find_group(&signature);
     return group;
   }

} *PARCEL_COMM_SEND_FSET;

extern sPARCEL_COMM_SEND_FSET g_parcel_comm_send_fset;

asINT32 create_parcel_send_groups();
VOID add_parcel_to_send_group(PARCEL_STATE parcel, uINT32 dest_sp);

#define DO_PARCEL_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sPARCEL_COMM_SEND_FSET, g_parcel_comm_send_fset, group_var)

/*--------------------------------------------------------------------------*
 * sPARCEL_RECV_GROUP
 *--------------------------------------------------------------------------*/
typedef class sPARCEL_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>> {

private:
  std::vector<unsigned char> m_recv_buffer;
  bool m_count_received;
  MPI_Request m_recv_request;
  MPI_Request m_second_recv_request;

public:

  sPARCEL_RECV_GROUP(cNEIGHBOR_SP source_sp, cSTP_SCALE scale) {
    m_source_sp = source_sp;
    m_scale = scale;
    m_recv_request        = MPI_REQUEST_NULL;
    m_second_recv_request = MPI_REQUEST_NULL;
    m_count_received = false;
    m_recv_type = PARCEL_RECV_TYPE;
  }

  virtual VOID post_recv() override;
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
  virtual bool is_recv_request_null() override;
  virtual bool is_recv_ready() override;
  virtual void cancel_pending_recv()  override {
    if ( m_recv_request != MPI_REQUEST_NULL ) {
      MPI_Cancel(&m_recv_request);
    }
    if ( m_second_recv_request != MPI_REQUEST_NULL ) {
      MPI_Cancel(&m_second_recv_request);
    }
  }
} *PARCEL_RECV_GROUP;

/*--------------------------------------------------------------------------*
 * PARCEL_COMM_RECV_FSET
 *--------------------------------------------------------------------------*/

typedef struct sPARCEL_RECV_GROUP_ORDER {
  BOOLEAN operator() (PARCEL_RECV_GROUP a, PARCEL_RECV_GROUP b) const
  {
      return a->m_source_sp < b->m_source_sp;
  }
} *PARCEL_RECV_GROUP_ORDER;

typedef struct sPARCEL_COMM_RECV_FSET :
        public tSP_FSET < PARCEL_RECV_GROUP, sPARCEL_RECV_GROUP_ORDER>
{
  public:
    PARCEL_RECV_GROUP find_or_create_group (cNEIGHBOR_SP source_sp) {
      sPARCEL_RECV_GROUP signature(source_sp, FINEST_SCALE);
      PARCEL_RECV_GROUP group = find_group(&signature);
      if (NULL == group) {
        group = new sPARCEL_RECV_GROUP(source_sp, sim.num_scales-1);
        add_group(group);
      }
      return group;
    }

    PARCEL_RECV_GROUP find_recv_group (cNEIGHBOR_SP source_sp) {
      sPARCEL_RECV_GROUP signature(source_sp, FINEST_SCALE);
      PARCEL_RECV_GROUP group = find_group(&signature);
      return group;
    }
} *PARCEL_COMM_RECV_FSET;

extern sPARCEL_COMM_RECV_FSET g_parcel_comm_recv_fset;
VOID  create_parcel_recv_groups();

#define DO_PARCEL_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sPARCEL_COMM_RECV_FSET, g_parcel_comm_recv_fset, group_var)

#define DO_PARCEL_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sPARCEL_COMM_RECV_FSET, g_parcel_comm_recv_fset, group_var, scale)

#endif
