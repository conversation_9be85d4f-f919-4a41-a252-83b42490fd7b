#ifndef UBLK_ATTRIBUTES_H
#define UBLK_ATTRIBUTES_H

#include "simulator_namespace.h"

/*==============================================================================
 * @enum UBLK_ATTR_BIT
 * Storage for UBLKs is allocated based on the list of 5 enums, making
 * for a total of 32 combinations of UBLK types
 *=============================================================================*/
enum UBLK_ATTR_BIT {
  TWO_STATE_COPIES_BIT,
  NEAR_SURFACE_BIT,
  HAS_MIRROR_BIT,
#if !BUILD_GPU
  IS_CONDUCTION_SOLID_BIT,
#endif
  IS_VR_COARSE_BIT,
  IS_VR_FINE_BIT,
  N_UBLK_ATTR_BITS
};

const static asINT32 UBLK_DATA_TYPE_MASK = (1 << N_UBLK_ATTR_BITS) - 1;

template <size_t N_VOXELS>
class tUBLK_ATTRS;

/* SIZE_INVARIANT_UBLK_ATTRIBS
 * This macro captures attributes of a UBLK that do not change with the
 * the number of voxels that it is templated on. This is because when dealing
 * with mblks (N_VOXELS > 8), we will make sure to only compose them of
 * smaller microblocks that have the same size invariant attributes or "promote"
 * the entire mblk to have an attribute if any one of the child
 * microblock has that attribute (Ex: m_is_split), without impacting physics logic
 *
 * is a macro rather than a struct to preserve legacy UBLK layout
 * The macro can be conveniently included as a "prefix" to a mucher longer
 * bitfield
 */
#if !BUILD_GPU
#define SIZE_INVARIANT_UBLK_ATTRIBS \
      /*First 6 bits determine the memory needed by the ublk.*/ \
      unsigned int m_has_two_copies_of_states:1;   \
      unsigned int m_does_interact_with_surface:1; \
      unsigned int m_has_mirror:1;                 \
      unsigned int m_is_conduction_solid:1;        \
      unsigned int m_is_vr_coarse:1;               \
      unsigned int m_is_vr_fine:1;                 \
                                                   \
      unsigned int m_is_ghost:1;                   \
      unsigned int m_has_ghosts:1;                 \
      unsigned int m_is_mirror:1;                  \
      unsigned int m_is_split:1;                   \
      unsigned int m_is_ublk_simple:1;             \
      unsigned int m_advect_from_split_ublk:1;     \
      unsigned int m_has_real_ckpt_data:1;         \
      unsigned int m_is_fringe:1;                  \
      /* This bit signifies the cleared states for ublks with two copies */ \
      /* This bit signifies the advected states for ublks with one copy*/ \
      unsigned int m_is_ghost_vr_fine:1; \
      unsigned int m_is_mlrf_surfel_interacting:1;
#else
#define SIZE_INVARIANT_UBLK_ATTRIBS \
      /*First 6 bits determine the memory needed by the ublk.*/ \
      unsigned int m_has_two_copies_of_states:1;   \
      unsigned int m_does_interact_with_surface:1; \
      unsigned int m_has_mirror:1;                 \
      unsigned int m_is_vr_coarse:1;               \
      unsigned int m_is_vr_fine:1;                 \
                                                   \
      unsigned int m_is_ghost:1;                   \
      unsigned int m_has_ghosts:1;                 \
      unsigned int m_is_mirror:1;                  \
      unsigned int m_is_split:1;                   \
      unsigned int m_is_ublk_simple:1;             \
      unsigned int m_advect_from_split_ublk:1;     \
      unsigned int m_has_real_ckpt_data:1;         \
      unsigned int m_is_fringe:1;                  \
      /* This bit signifies the cleared states for ublks with two copies */ \
      /* This bit signifies the advected states for ublks with one copy*/ \
      unsigned int m_is_ghost_vr_fine:1; \
      unsigned int m_is_mlrf_surfel_interacting:1;
#endif

template <>
class tUBLK_ATTRS<N_VOXELS_8> {

  //Unfortunately we have to make this public because a lot of old code
  //directly referencing the attributes
public:
  // These bits are static. They are set during initialization and never change during a simulation
  union {
    struct {
      uINT32  m_ublk_type;
    };

    struct {

      SIZE_INVARIANT_UBLK_ATTRIBS

      unsigned int m_abuts_simvol_boundary:6;
      unsigned int m_is_fringe2:1;

      unsigned int m_advect_from_split_mask:8;   // If split advect factors are present

      unsigned int m_is_frozen:1;
      unsigned int m_does_interact_with_regular_surface:1; // False if only interacting with sampling surfels
      unsigned int m_is_vr_coarse_with_fine_children:1;
      unsigned int m_has_two_copy_neighbor:1;
    };
  };

  // These bits are dynamic. They are used by the compute thread to keep track of the state of the ublk,
  // and should never by touched by the comm thread.
  union {
    struct {
      uINT32 m_dynamic_bits;
    };
    struct {
        unsigned int m_this_is_first_surfel:1;
        unsigned int m_are_states_cleared_for_S2V:1;
        unsigned int m_is_exploded:1;
        unsigned int m_are_states_advected:1;
        unsigned int m_is_tobefringe:1;
    };
  };

public:

  tUBLK_ATTRS() : m_ublk_type(0) {}

  __HOST__DEVICE__ uINT8 ublk_type() const {return (m_ublk_type & UBLK_DATA_TYPE_MASK);}

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol_on_face(asINT32 face, [[maybe_unused]] asINT32 index = 0) const {
    return ((m_abuts_simvol_boundary >> face) & 1);
  }

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol([[maybe_unused]] asINT32 index = 0) const {
    return (m_abuts_simvol_boundary > 0);
  }

  __HOST__ VOID set_ublk_abuts_simvol_on_face(asINT32 face, [[maybe_unused]] asINT32 index = 0) {
    m_abuts_simvol_boundary |= (1 << face);
  }

  __HOST__DEVICE__ uINT8   advect_from_split_mask() const {
    return m_advect_from_split_mask;
  }
  __HOST__ VOID unset_advect_from_split_mask() {
    m_advect_from_split_mask = 0;
  }
  __HOST__ VOID set_advect_from_split_mask(asINT32 voxel) {
    m_advect_from_split_mask |= (1 << voxel);
  }

  __HOST__DEVICE__ BOOLEAN is_frozen([[maybe_unused]] asINT32 index = 0) const {
    return m_is_frozen;
  }

  __HOST__DEVICE__ VOID set_frozen(BOOLEAN value, [[maybe_unused]] asINT32 index = 0) {
    if (value)
      m_is_frozen = 1;
    else
      m_is_frozen = 0;
  }

  __HOST__DEVICE__ BOOLEAN is_near_regular_surface([[maybe_unused]] asINT32 index = 0) const {
    return m_does_interact_with_regular_surface;
  }

  __HOST__ VOID set_is_near_regular_surface([[maybe_unused]] asINT32 index = 0) {
    m_does_interact_with_regular_surface = 1;
  }

  __HOST__DEVICE__ BOOLEAN is_vr_coarse_with_fine_children([[maybe_unused]] asINT32 index = 0) const {
    return m_is_vr_coarse_with_fine_children;
  }

  VOID set_vr_coarse_with_fine_children(unsigned int v,
                                        [[maybe_unused]] asINT32 index = 0) {
    m_is_vr_coarse_with_fine_children = v;
  }

  __HOST__ VOID set_ublk_has_two_copy_neighbor([[maybe_unused]] asINT32 index = 0) {
    m_has_two_copy_neighbor = 1;
  }

  __HOST__DEVICE__ BOOLEAN ublk_has_two_copy_neighbor([[maybe_unused]] asINT32 index = 0) const {
    return m_has_two_copy_neighbor;
  }

  __HOST__DEVICE__ BOOLEAN is_fringe2([[maybe_unused]] asINT32 index = 0) const { return m_is_fringe2; }

  __HOST__DEVICE__ VOID set_fringe2([[maybe_unused]] asINT32 index = 0) { m_is_fringe2 = 1; }

};

template <size_t N_VOXELS>
class tUBLK_ATTRS {

public:

  enum {
    N_UBLKS = N_VOXELS / N_VOXELS_8
  };

  union {
    struct {
      uINT32 m_ublk_type;
    };

    struct {
      SIZE_INVARIANT_UBLK_ATTRIBS
      unsigned int m_are_states_cleared_for_S2V:1;
      unsigned int m_is_spatially_coherent : 1;
      unsigned int m_is_exploded:1;
    };
  };

private:
  tBITSET<N_FACES> m_abuts_simvol_boundary[N_UBLKS];
  tBITSET<N_VOXELS> m_advect_from_split_mask;
  tBITSET<N_VOXELS_64> m_is_frozen; //Waste some bits here since GPU atomic set only works with 64 wide masks
  tBITSET<N_UBLKS> m_does_interact_with_regular_surface;
  tBITSET<N_UBLKS> m_is_vr_coarse_with_fine_children;
  tBITSET<N_UBLKS> m_has_two_copy_neighbor;
  tBITSET<N_UBLKS> m_is_fringe2;

public:

  __HOST__DEVICE__ uINT8 ublk_type() const {return (m_ublk_type & UBLK_DATA_TYPE_MASK); }

  //Constructor
  tUBLK_ATTRS() : m_ublk_type(0) {
    this->m_is_ublk_simple = 1;
  }

  __HOST__ VOID set_has_two_copies_of_states(unsigned int v) {
    this->m_has_two_copies_of_states = v;
  }

  __HOST__ VOID set_is_spatially_coherent(unsigned int v) {
    this->m_is_spatially_coherent = v;
  }

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol_on_face(asINT32 face, asINT32 index) const {
    return m_abuts_simvol_boundary[index].test(face);
  }

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol(asINT32 index) const {
    return m_abuts_simvol_boundary[index].any();
  }

  __HOST__ VOID set_ublk_abuts_simvol_on_face(asINT32 face, asINT32 index) {
    m_abuts_simvol_boundary[index].set(face);
  }

  __HOST__DEVICE__ auto advect_from_split_mask() const {
    return m_advect_from_split_mask;
  }

  __HOST__ VOID unset_advect_from_split_mask() {
    m_advect_from_split_mask.set_all();
  }

  __HOST__ VOID set_advect_from_split_mask(asINT32 voxel) {
    m_advect_from_split_mask.set(voxel);
  }

  __HOST__DEVICE__ BOOLEAN is_frozen(asINT32 index) const {
    return m_is_frozen.test(index);
  }

  __HOST__DEVICE__ VOID set_frozen(BOOLEAN value, asINT32 index) {
    if (value) { m_is_frozen.gpu_atomic_set(index); }
  }

  __HOST__DEVICE__ BOOLEAN is_near_regular_surface(asINT32 index) const {
    return m_does_interact_with_regular_surface.test(index);
  }

  __HOST__ VOID set_is_near_regular_surface(asINT32 index) {
    m_does_interact_with_regular_surface.set(index);
  }

  __HOST__DEVICE__ BOOLEAN is_vr_coarse_with_fine_children(asINT32 index) const {
    return m_is_vr_coarse_with_fine_children.test(index);
  }

  VOID set_vr_coarse_with_fine_children(unsigned int v,
                                        asINT32 index) {
    m_is_vr_coarse_with_fine_children.set_or_reset(index, v);
  }

  __HOST__ VOID set_ublk_has_two_copy_neighbor(asINT32 index) {
    m_has_two_copy_neighbor.set(index);
  }

  __HOST__DEVICE__ BOOLEAN ublk_has_two_copy_neighbor(asINT32 index) const {
    return m_has_two_copy_neighbor.test(index);
  }

  __HOST__DEVICE__ BOOLEAN is_fringe2(asINT32 index) const { return m_is_fringe2.test(index); }

  __HOST__DEVICE__ VOID set_fringe2(asINT32 index) { m_is_fringe2.set(index); }

  __HOST__DEVICE__ BOOLEAN is_vr_fine() const { return this->m_is_vr_fine; }

  __HOST__DEVICE__ BOOLEAN is_vr_coarse() const { return this->m_is_vr_coarse; }

  VOID set_from_child_ublk_attributes(const tUBLK_ATTRS<N_VOXELS_8>& src,
                                      asINT32 index) {

    this->m_has_two_copies_of_states |= src.m_has_two_copies_of_states;
    this->m_does_interact_with_surface |= src.m_does_interact_with_surface;
    this->m_has_mirror |= src.m_has_mirror;
    this->m_is_vr_coarse |= src.m_is_vr_coarse;
    this->m_is_vr_fine |= src.m_is_vr_fine;
    this->m_is_ghost |= src.m_is_ghost;
    this->m_has_ghosts |= src.m_has_ghosts;
    this->m_is_mirror |= src.m_is_mirror;
    this->m_is_split |= src.m_is_split;
    //For a MBLK to be simple, all composing child ublks have to be simple
    this->m_is_ublk_simple &= src.m_is_ublk_simple;
    this->m_is_exploded |= src.m_is_exploded;
    this->m_advect_from_split_ublk |= src.m_advect_from_split_ublk;
    this->m_has_real_ckpt_data |= src.m_has_real_ckpt_data;
    this->m_is_fringe |= src.m_is_fringe;
    this->m_are_states_cleared_for_S2V |= src.m_are_states_cleared_for_S2V;
    this->m_is_ghost_vr_fine |= src.m_is_ghost_vr_fine;
    this->m_is_mlrf_surfel_interacting |= src.m_is_mlrf_surfel_interacting;

    for (int face = 0; face < N_FACES; face++) {
      if (src.does_ublk_abut_simvol(face)) {
        this->set_ublk_abuts_simvol_on_face(face, index);
      }
    }

    int voxel_shift = index * N_VOXELS_8;
    this->m_advect_from_split_mask |= tBITSET<N_VOXELS>(src.advect_from_split_mask() << voxel_shift);

    this->set_frozen(src.is_frozen(), index);
    this->set_vr_coarse_with_fine_children(src.is_vr_coarse_with_fine_children(), index);

    if (src.is_near_regular_surface()) {
      this->set_is_near_regular_surface(index);
    }
    if (src.ublk_has_two_copy_neighbor()) {
      this->set_ublk_has_two_copy_neighbor(index);
    }
    if (src.is_fringe2()) {
      this->set_fringe2(index);
    };
  }

};

#endif
