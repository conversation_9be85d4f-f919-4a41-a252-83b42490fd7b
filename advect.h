/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** ExaSIM Simulation Process                                             ***
 ***                                                                       ***
 *** Copyright (C) 2003, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "08")                     */ 

#ifndef _SP_ADVECT_H_
#define _SP_ADVECT_H_

#include "common_sp.h"
#include "fset.h"
#include "ublk_table.h"
#include "ublk.h"
#include "group.h"
#include "shob_groups.h"
#include VMEM_VECTOR_H

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID scale_up_z_only_states(UBLK_TYPE* ublk, dFLOAT fraction, asINT32 timestep_index,
                            BOOLEAN has_two_copies, ACTIVE_SOLVER_MASK active_solver_mask);

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID copy_z_only_states(UBLK_TYPE* ublk, asINT32 timestep_index, ACTIVE_SOLVER_MASK active_solver_mask);

template<typename UBLK_TYPE>
__HOST__DEVICE__ _INLINE_
VOID find_cardinal_neighbors(UBLK_TYPE* ublk,
			     UBLK_TYPE* neighbors[6],
			     asINT32 n_dims,
			     asINT32 child_ublk) {
  

  const auto& box_access = ublk->box_access(child_ublk);
  
  auto tagged_neighbor = box_access.forward_neighbor(0);
  if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
      && !tagged_neighbor.is_ublk_split()) {
    neighbors[0] = tagged_neighbor.ublk();
  }
  tagged_neighbor = box_access.backward_neighbor(0);
  if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
      && !tagged_neighbor.is_ublk_split()) {
    neighbors[1] = tagged_neighbor.ublk();
  }
  tagged_neighbor = box_access.forward_neighbor(1);
  if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
      && !tagged_neighbor.is_ublk_split()) {
    neighbors[2] = tagged_neighbor.ublk();
  }
  tagged_neighbor = box_access.backward_neighbor(1);
  if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
      && !tagged_neighbor.is_ublk_split()) {
    neighbors[3] = tagged_neighbor.ublk();
  }
  if (n_dims > 2) {
    tagged_neighbor = box_access.forward_neighbor(2);
    if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
        && !tagged_neighbor.is_ublk_split()) {
      neighbors[4] = tagged_neighbor.ublk();
    }
    tagged_neighbor = box_access.backward_neighbor(2);
    if (tagged_neighbor.is_ublk() && !tagged_neighbor.is_ublk_scale_interface()
        && !tagged_neighbor.is_ublk_split()) {
      neighbors[5] = tagged_neighbor.ublk();
    }
  }
}

template<typename TAGGED_UBLK_TYPE, typename UNARY_PRED>
__HOST__DEVICE__ INLINE
bool does_tagged_nbr_satisfy_unary_pred(TAGGED_UBLK_TYPE tagged_neighbor,
                                        UNARY_PRED unary_pred)
{ 
  if (tagged_neighbor.is_ublk_scale_interface()) {
    auto interface = tagged_neighbor.interface();
    for (auto fine_tagged_ublk : interface->m_fine_ublks) {
      if (does_tagged_nbr_satisfy_unary_pred(fine_tagged_ublk, unary_pred)) {
        return true;
      }
    }
  } else if (tagged_neighbor.is_ublk_split()) {
    auto split_vector = tagged_neighbor.split_ublks();
    auto split_tagged_ublks = split_vector->tagged_ublks();
    int num_split_ublks = split_vector->num_split_ublks();
    for (int i = 0; i < num_split_ublks; i++) {
      if (does_tagged_nbr_satisfy_unary_pred(split_tagged_ublks[i], unary_pred)) {
        return true;
      }
    }
  } else if (tagged_neighbor.is_ublk()) {
    auto child_ublk_offset = tagged_neighbor.get_offset_in_mega_block();
    auto ublk = tagged_neighbor.ublk();
    return unary_pred(ublk, child_ublk_offset);
  }
  return false;
}

template<typename UBLK_TYPE, typename UNARY_PRED>
__HOST__DEVICE__ _INLINE_
bool do_any_cardinal_neighbors_satisfy_func(const UBLK_TYPE* ublk,
                                            asINT32 n_dims,
                                            asINT32 child_ublk,
                                            UNARY_PRED unary_pred)
{
  

  const auto& box_access = const_cast<UBLK_TYPE*>(ublk)->box_access(child_ublk);
    
  for (int axis = 0; axis < n_dims; axis++) {
    auto fwd_tagged_neighbor = box_access.forward_neighbor(axis);
    auto bwd_tagged_neighbor = box_access.backward_neighbor(axis);
    if (does_tagged_nbr_satisfy_unary_pred(fwd_tagged_neighbor, unary_pred) ||
        does_tagged_nbr_satisfy_unary_pred(bwd_tagged_neighbor, unary_pred)) {
      return true;
    }
  }
  return false;
}


template<typename UBLK_TYPE>
__HOST__DEVICE__ _INLINE_
bool are_any_cardinal_neighbors_ghost(const UBLK_TYPE* ublk,
                                      asINT32 n_dims,
                                      asINT32 child_ublk) {
  
  auto is_gh = [] (const UBLK_TYPE* test_ublk, asINT32 child_ublk_index){
    return !test_ublk->is_solid(child_ublk_index) && test_ublk->is_ghost();
  };
  
  return do_any_cardinal_neighbors_satisfy_func(ublk, n_dims, child_ublk, is_gh);
}

_INLINE_ 
VOID find_cardinal_neighbors(sUBLK* ublk,
			     sUBLK* neighbors[6],
			     asINT32 n_dims) {
  find_cardinal_neighbors(ublk, neighbors, n_dims, 0 /*child_ublk*/);
}
#endif //_SP_ADVECT_H_
