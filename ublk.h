/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublks for all lattices
 *--------------------------------------------------------------------------*/
#ifndef UBLK_H_
#define UBLK_H_

#include "common_sp.h"
#include "mme_ckpt.h"
#include "ublk_surfel_offset_table.h"
#include "vectorization_support.h"
#include "shob.h"
#include "sim.h"
#include "ublk_neighbors.h"
#include "dgf_reader_sp.h"
#include "comm_compression.h"
#include "lb_solver_data.h"
#include "turb_solver_data.h"
#include "t_solver_data.h"
#include "conduction_data.h"
#include "scalar_data.h"
#include "mc_data_5g.h"
#include "particle_solver_data.h"
#include "bsurfel_body_force.h"
#include "debug_print_spec.h"
#include "sp_timers.h"
#include "shob_allocator.h"
#include "meas_cell.h"
#include "bitset.h"
#include "ublk_box.h"
#include "split_data.h"
#include "ublk_attributes.h"
#include "ublk_smart_seed_data.h"

#define PFLUID_WEIGHTS_MISMATCH_THRESHOLD 1.0e-6
#define PBL_THRESHOLD                     1.0e-6
#define PBL_VECTOR_MISMATCH_THRESHOLD     1.0e-4

typedef VOXEL_STATE UBLK_STATE[ubFLOAT::N_VOXELS];
struct sUBLK_DYNAMICS_ATTR_DATA; // forward declaration
class cMME_CKPT;

// SEND_ELEMENTs are the objects that constitute an MPI comm buffer 
// see comment about sUBLK_SEND_ELEMENT below
typedef struct sUBLK_INIT_INFO_SEND_ELEMENT 
{
  VOXEL_MASK_8  m_fluid_like_voxel_mask;
  VOXEL_MASK_8  m_all_neighbors_voxel_mask;
  VOXEL_MASK_8  m_voxel_smart_seed_mask;
  sVR_TOPOLOGY m_vr_topology;
#if BUILD_5G_LATTICE
  sdFLOAT m_porosity[ubFLOAT::N_VOXELS];
#endif
  sINT32 m_implicit_solid_state_index[ubFLOAT::N_VOXELS];
} *UBLK_INIT_INFO_SEND_ELEMENT;

typedef struct sNEARBLK_INIT_INFO_SEND_ELEMENT
{
  sUBLK_INIT_INFO_SEND_ELEMENT m_ublk_init_info;
  sdFLOAT m_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS];
  sdFLOAT m_pbl[ubFLOAT::N_VOXELS];
  uINT8   m_ref_frame_index;
  uINT8   m_is_frozen;
} *NEARBLK_INIT_INFO_SEND_ELEMENT;

typedef struct sCOND_NEARBLK_INIT_INFO_SEND_ELEMENT
{
  sdFLOAT m_passthrough_summ_inv[ubFLOAT::N_VOXELS];
} *COND_NEARBLK_INIT_INFO_SEND_ELEMENT;

//------------------------------------------------------------------------------
// sUBLK
//------------------------------------------------------------------------------

#define UBLK_APPLY_ACTIVE(active_solver_mask, method, args)                         \
  if (this->is_conduction_solid()) {                                                \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask))                    \
      this->conduction_data()->method args;                                         \
  } else {                                                                          \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                            \
      this->lb_data()->method args;                                                 \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                          \
      this->turb_data()->method args;                                               \
    if (is_solver_active(T_SOLVER, active_solver_mask))                             \
      this->t_data()->method args;                                                  \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->mc_data()->method args; 					                                      \
    if (is_solver_active(PARTICLE_SOLVER, active_solver_mask))                      \
      this->p_data()->method args;                                                  \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {		                      \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars; nth_uds++) {  \
        auto ublk_uds_data = this->uds_data(nth_uds);			                          \
        ublk_uds_data->method args;					                                        \
      }                                                                             \
      if (sim.is_pf_model)                                                          \
        this->pf_data()->method args;                                               \
    }                                                                               \
  }


// CONDUCTION-CHECK: If is_vr_fine is redundant (because ghost VRCoarse voxels do an explode of their own,
// remove the flag or even the use of this macro as a whole
#define UBLK_APPLY_ACTIVE_INCLUDE_VRFINE(active_solver_mask, method, arg1, arg2, is_vr_fine) \
  if (this->is_conduction_solid()) {                                              \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask))                  \
      this->conduction_data()->method (arg1, arg2);                               \
  } else {                                                                        \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method (arg1, arg2);                                       \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method (arg1, arg2);                                     \
    if (is_solver_active(T_SOLVER, active_solver_mask)) {                         \
      if (is_vr_fine)                                                             \
        this->t_data()->method (arg1, arg2, TRUE);                                \
      else                                                                        \
        this->t_data()->method (arg1, arg2, FALSE);                               \
    }                                                                             \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
      this->mc_data()->method (arg1, arg2); 					                            \
    if (is_solver_active(PARTICLE_SOLVER, active_solver_mask))                    \
      this->p_data()->method (arg1, arg2);                                        \
  }

// This macro assumes that arg1 has data blocks with the same accessor methods as a ublk.
// Obviously this means that arg1 can be a ublk.
#define UBLK_APPLY_ACTIVE_1(active_solver_mask, method, arg1)                       \
  if (!this->is_conduction_solid()) {                                               \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                            \
      this->lb_data()->method (arg1->lb_data());                                    \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                          \
      this->turb_data()->method (arg1->turb_data());                                \
    if (is_solver_active(T_SOLVER, active_solver_mask))                             \
      this->t_data()->method (arg1->t_data());                                      \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars; nth_uds++) {  \
        auto ublk_uds_data = this->uds_data(nth_uds);                               \
        auto arg1_uds_data = arg1->uds_data(nth_uds);                               \
        ublk_uds_data->method(arg1_uds_data);                                       \
      }									                                                            \
      if (sim.is_pf_model)						                                              \
        this->pf_data()->method (arg1->pf_data());                                  \
    }                                                                               \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->mc_data()->method (arg1->mc_data()); \
  }

#define UBLK_APPLY_ACTIVE_2(active_solver_mask, method, arg1, arg2)                 \
  if (!this->is_conduction_solid()) {                                               \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                            \
      this->lb_data()->method (arg1->lb_data(), arg2);                              \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                          \
      this->turb_data()->method (arg1->turb_data(), arg2);                          \
    if (is_solver_active(T_SOLVER, active_solver_mask))                             \
      this->t_data()->method (arg1->t_data(), arg2);                                \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;               \
          nth_uds++) {                                                              \
        auto ublk_uds_data = this->uds_data(nth_uds);                               \
        auto arg1_uds_data = arg1->uds_data(nth_uds);				                        \
        ublk_uds_data->method(arg1_uds_data, arg2); }			                          \
      if (sim.is_pf_model)                                                          \
        this->pf_data()->method (arg1->pf_data(), arg2);			                      \
    }                                                                               \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->mc_data()->method (arg1->mc_data(), arg2);                              \
  }

// This macro assumes that arg1 has data blocks with the same accessor methods as a ublk.
// Obviously this means that arg1 can be a ublk.
#define UBLK_APPLY_ACTIVE_3(active_solver_mask, method, arg1, arg2, arg3)           \
  if (this->is_conduction_solid()) {                                                \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask))                    \
      this->conduction_data()->method (arg1->conduction_data(), arg2, arg3);        \
  } else {                                                                          \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                            \
      this->lb_data()->method (arg1->lb_data(), arg2, arg3);                        \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                          \
      this->turb_data()->method (arg1->turb_data(), arg2, arg3);                    \
    if (is_solver_active(T_SOLVER, active_solver_mask))                             \
      this->t_data()->method (arg1->t_data(), arg2, arg3);                          \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;               \
           nth_uds++) {			                                                        \
        auto ublk_uds_data = this->uds_data(nth_uds);                               \
        auto arg1_uds_data = arg1->uds_data(nth_uds);				                        \
        ublk_uds_data->method(arg1_uds_data, arg2, arg3); }                         \
    if (sim.is_pf_model)                                                            \
      this->pf_data()->method (arg1->pf_data(), arg2, arg3);                        \
    }                                                                               \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->mc_data()->method (arg1->mc_data(), arg2, arg3);                        \
  }

// to avoid issues with scalar data blocks we add another mask without scalar data calling methods
// that don't exist
#define UBLK_APPLY_ACTIVE_4(active_solver_mask, method, arg1, arg2, arg3)         \
  if (!this->is_conduction_solid()) {                                             \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
      this->lb_data()->method (arg1->lb_data(), arg2, arg3);                      \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
      this->turb_data()->method (arg1->turb_data(), arg2, arg3);                  \
    if (is_solver_active(T_SOLVER, active_solver_mask))                           \
      this->t_data()->method (arg1->t_data(), arg2, arg3);                        \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
      this->mc_data()->method (arg1->mc_data(), arg2, arg3);                      \
  }

//------------------------------------------------------------------------------
// sUBLK_LB_STATES_DATA
//------------------------------------------------------------------------------
inline namespace SIMULATOR_NAMESPACE {
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_STATES_DATA
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  ALIGNED_UBFLOAT m_states[N_STATES];
#undef ALIGNED_UBFLOAT
};
}

typedef tUBLK_STATES_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_STATES_DATA,         *UBLK_STATES_DATA;
typedef tUBLK_STATES_DATA<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT_STATES_DATA, *UBLK_UBFLOAT_STATES_DATA;

#ifdef BUILD_GPU
typedef tUBLK_STATES_DATA<MBLK_SDFLOAT_TYPE_TAG> sMBLK_STATES_DATA,         *MBLK_STATES_DATA;
typedef tUBLK_STATES_DATA<MBLK_UBFLOAT_TYPE_TAG> sMBLK_UBFLOAT_STATES_DATA, *MBLK_UBFLOAT_STATES_DATA;

INIT_MBLK(UBLK_STATES_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(m_states,N_STATES);
}
#endif

inline namespace SIMULATOR_NAMESPACE {
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_TWO_STATES_DATA : public tUBLK_STATES_DATA<UBLK_TYPE_TAG>
{
  EXTRACT_UBLK_TRAITS
#define ALIGNED_UBFLOAT T ALIGN_VECTOR
  ALIGNED_UBFLOAT m_states_stay[N_STATES];
#undef ALIGNED_UBFLOAT
};
}

typedef tUBLK_TWO_STATES_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_TWO_STATES_DATA,         *UBLK_TWO_STATES_DATA;
typedef tUBLK_TWO_STATES_DATA<UBLK_SDFLOAT_TYPE_TAG> sUBLK_UBFLOAT_TWO_STATES_DATA, *UBLK_UBFLOAT_TWO_STATES_DATA;

template <BOOLEAN is_2D, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID ublk_adv_and_dyn(UBLK ublk, BOOLEAN is_timestep_even,
		      SOLVER_INDEX_MASK prior_solver_index_mask,
		      ACTIVE_SOLVER_MASK active_solver_mask);

VOID ublk_conduction_dyn(UBLK ublk,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         ACTIVE_SOLVER_MASK active_solver_mask);

VOID ublk_dyn_and_meas(UBLK ublk, BOOLEAN is_timestep_even,
                       SOLVER_INDEX_MASK prior_solver_index_mask,
                       ACTIVE_SOLVER_MASK active_solver_mask);

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID force_ublk_states_in_range(VOXEL_STATE ublk_states[N_STATES][UBLK_TYPE::N_VOXELS],
                                STP_PHYS_VARIABLE temp, auINT32 voxel);

#if BUILD_5G_LATTICE
template<BOOLEAN IS_UDS_LB>
VOID ublk_adv_states(UBLK ublk, asINT32 next_index);
VOID ublk_grad_porosity(UBLK ublk);
VOID compute_eigvec_110(ubFLOAT grad2[],ubFLOAT grad[],sdFLOAT *a,asINT32 voxel);
#endif

template<size_t N_VOXELS> struct tVR_INTERFACE_DATA;
template<typename UBLK_TYPE_TAG> struct tVR_FINE_INTERFACE_DATA;
template<typename UBLK_TYPE_TAG> struct tVR_COARSE_INTERFACE_DATA;

inline namespace SIMULATOR_NAMESPACE {
template<typename UBLK_TYPE_TAG> struct tUBLK_MIRROR_DATA;
}

struct sFLUID_PHYSICS_DESCRIPTOR;
template <typename sPHYSICS_DESCRIPTOR_TYPE, typename UBLK_TYPE_TAG> class tUBLK_DYNAMICS_DATA;
template <typename sPHYSICS_DESCRIPTOR_TYPE, typename UBLK_TYPE_TAG> class tSPECIAL_UBLK_DYNAMICS_DATA;
struct sPOROUS_MEDIA_PHYSICS_DESCRIPTOR;
template <typename sPHYSICS_DESCRIPTOR_TYPE, typename UBLK_TYPE_TAG> class tPOROUS_UBLK_DYNAMICS_DATA;
struct sLRF_PHYSICS_DESCRIPTOR;


#define NEARBLK_APPLY_ACTIVE(active_solver_mask, method, args)                      \
  UBLK_APPLY_ACTIVE(active_solver_mask, method, args)                               \
  if (!this->is_conduction_solid()) {                                               \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                            \
      this->surf_lb_data()->method args;                                            \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                          \
      this->surf_turb_data()->method args;                                          \
    if (is_solver_active(T_SOLVER, active_solver_mask))                             \
      this->surf_t_data()->method args;                                             \
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                         \
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars; nth_uds++) {  \
        auto nearblk_uds_data = this->surf_uds_data(nth_uds);                       \
        nearblk_uds_data->method args; }                                            \
      if (sim.is_pf_model)                                                          \
        this->surf_pf_data()->method args;				                                  \
    }                                                                               \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))    \
      this->surf_mc_data()->method args;                                            \
  }

#define NEARBLK_APPLY_ACTIVE_EXCLUDE_SURF_T(active_solver_mask, method, arg1, arg2, is_vr_fine) \
  UBLK_APPLY_ACTIVE_INCLUDE_VRFINE(active_solver_mask, method, arg1, arg2, is_vr_fine)          \
  if (this->is_conduction_solid()) {                                                            \
    if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask))                                \
      this->surf_conduction_data()->method (arg1, arg2);                                        \
  } else {                                                                                      \
    if (is_solver_active(LB_SOLVER, active_solver_mask))                                        \
      this->surf_lb_data()->method (arg1, arg2);                                                \
    if (is_solver_active(TURB_SOLVER, active_solver_mask))                                      \
      this->surf_turb_data()->method (arg1, arg2);                                              \
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))                \
      this->surf_mc_data()->method (arg1, arg2); \
  }

template<typename UBLK_TYPE> struct tUBLK_GROUP;
typedef tUBLK_GROUP<sUBLK> sUBLK_GROUP, *UBLK_GROUP;

//Should always be false for GPU builds
#define FORCE_64_BYTE_ALIGNMENT_OF_STATES 0

using VR_SCALE_DIFF_VOXEL_MASK_8 = tBITSET<2*N_VOXELS_8>;
using VR_SCALE_DIFF_VOXEL_MASK_64 = tBITSET<2*N_VOXELS_64>;

template<typename UBLK_TYPE_TAG>
struct tUBLK_LB_DATA_OR_EMPTY_STRUCT {
  struct EMPTY_LB_DATA {};
  using type = typename std::conditional<UBLK_TYPE_TAG::is_inline_layout(),
                                         tUBLK_LB_DATA<UBLK_TYPE_TAG>,
                                         EMPTY_LB_DATA>::type;
};

/*==============================================================================
 * @fcn def_child_ublk_index
 * We want some of the UBLK functions to accept a child ublk index argument
 * for MBLKs, and want to mandate call sites to provide it. On the other hand
 * client code dealing with regular microblocks should not have to specify an
 * index for these access functions, and it can default to a trivial child ublk index of 0.
 * 
 * The template function def_child_ublk_index thus returns a 0 for regular
 * UBLK but triggers a compilation error for MBLK methods unless an index argument
 * is specified, which is in contrast just using a default index argument of 0
 * ============================================================================*/
template<size_t N_VOXELS>
__HOST__DEVICE__ INLINE constexpr int def_child_ublk_index() {
  static_assert(N_VOXELS == 8,
                "This static assertion goes off because a mega-ublk method was "
                "called without specifying the child ublk index");
  return -1;
}

template<>
__HOST__DEVICE__ INLINE constexpr int def_child_ublk_index<N_VOXELS_8>() { return 0; }

#define REQUIRE_INDEX_IF_MBLK(index) \
  asINT32 index = def_child_ublk_index<N_VOXELS>()

#include "ublk_solver_data_layout.h"

/*==============================================================================
 * @struct SIMULATOR_NAMESPACE::tUBLK
 * ============================================================================*/
inline namespace SIMULATOR_NAMESPACE {
         
template<typename UBLK_TYPE_TAG>
class ALIGN_VECTOR tUBLK: public sSHOB,
                          public UBLK_TYPE_TAG::BLOCK_LAYOUT
{
 public:

  EXTRACT_UBLK_TRAITS

  using tUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using BLOCK_LAYOUT = typename UBLK_TYPE_TAG::BLOCK_LAYOUT;
  using sUBLK_STATES_DATA = tUBLK_STATES_DATA<UBLK_TYPE_TAG>;
  using sUBLK_TWO_STATES_DATA = tUBLK_TWO_STATES_DATA<UBLK_TYPE_TAG>;
  using sUBLK_LB_DATA = tUBLK_LB_DATA<UBLK_TYPE_TAG>;
  using sUBLK_TURB_DATA = tUBLK_TURB_DATA<UBLK_TYPE_TAG>;
  using sUBLK_T_DATA = tUBLK_T_DATA<UBLK_TYPE_TAG>;
  using sUBLK_UDS_DATA = tUBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PF_DATA = tUBLK_PF_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MC_DATA = tUBLK_MC_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MME_DNS_DATA = tUBLK_MME_DNS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MME_TURB_DATA = tUBLK_MME_TURB_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MME_T_DATA = tUBLK_MME_T_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MME_UDS_DATA = tUBLK_MME_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_MME_CONDUCTION_DATA = tUBLK_MME_CONDUCTION_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PARTICLE_DATA = tUBLK_PARTICLE_DATA<UBLK_TYPE_TAG>;
  using sUBLK_CONDUCTION_DATA = tUBLK_CONDUCTION_DATA<UBLK_TYPE_TAG>;
  using sUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA = tUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_LB_DATA = tNEARBLK_LB_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_DP_PASS_FACTORS_DATA = tNEARBLK_DP_PASS_FACTORS_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_TURB_DATA = tNEARBLK_TURB_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_T_DATA = tNEARBLK_T_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_UDS_DATA = tNEARBLK_UDS_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_PF_DATA = tNEARBLK_PF_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_MC_DATA = tNEARBLK_MC_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_MME_DATA = tNEARBLK_MME_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_FROZEN_DATA = tNEARBLK_FROZEN_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_GEOM_DATA = tNEARBLK_GEOM_DATA<UBLK_TYPE_TAG>;
  using sNEARBLK_CONDUCTION_DATA = tNEARBLK_CONDUCTION_DATA<UBLK_TYPE_TAG>;
  using sVR_INTERFACE_DATA = tVR_INTERFACE_DATA<N_VOXELS>;
  using sVR_FINE_INTERFACE_DATA = tVR_FINE_INTERFACE_DATA<UBLK_TYPE_TAG>;
  using sVR_COARSE_INTERFACE_DATA = tVR_COARSE_INTERFACE_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PORE_LB_DATA = tUBLK_PORE_LB_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PORE_MC_DATA = tUBLK_PORE_MC_DATA<UBLK_TYPE_TAG>;
  using sUBLK_PORE_UDS_DATA = tUBLK_PORE_UDS_DATA<UBLK_TYPE_TAG>;
  using sUBLK_CBF_LB_DATA = tUBLK_CBF_LB_DATA<UBLK_TYPE_TAG>;
  using sBSURFEL_BODY_FORCE = tBSURFEL_BODY_FORCE<UBLK_TYPE_TAG>;
  using sUBLK_DYNAMICS_DATA = tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG>;
  using sSPECIAL_UBLK_DYNAMICS_DATA = tSPECIAL_UBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG>;
  using sPOROUS_UBLK_DYNAMICS_DATA = tPOROUS_UBLK_DYNAMICS_DATA<sPOROUS_MEDIA_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG>;
  using sUBLK_MIRROR_DATA = tUBLK_MIRROR_DATA<UBLK_TYPE_TAG>;
  using sTAGGED_UBLK = tTAGGED_UBLK<UBLK_TYPE_TAG>;
  using sUBLK_VECTOR = tUBLK_VECTOR<UBLK_TYPE_TAG>;
  using sUBLK_BOX = tUBLK_BOX<UBLK_TYPE_TAG>; using UBLK_BOX = sUBLK_BOX*;
  using sSCALE_BOX_INTERFACE = tSCALE_BOX_INTERFACE<UBLK_TYPE_TAG>;
  using sBOX_ACCESS = tBOX_ACCESS<UBLK_TYPE_TAG>;
  using sUBLK_BOX_ACCESS = tUBLK_BOX_ACCESS<UBLK_TYPE_TAG>;
  using sUBLK_SMART_SEED_DATA = tUBLK_SMART_SEED_DATA<UBLK_TYPE_TAG>;
#define ALIGNED_UBFLOAT T ALIGN_VECTOR

  using ubV3 = tubV3<typename UBLK_TYPE_TAG::UBFLOAT_CONFIG_TYPE>;
  using sSPLIT_INFO = tSPLIT_INFO<N_VOXELS>;
  using sSPLIT_ADVECT_FACTORS = tSPLIT_ADVECT_FACTORS<N_VOXELS>;
  using sUBLK_UBFLOAT = tUBLK<typename UBLK_TYPE_TAG::UBFLOAT_TYPE_TAG>;
  using sUBLK_SDFLOAT = tUBLK<typename UBLK_TYPE_TAG::SDFLOAT_TYPE_TAG>;
  using sUBLK_ATTRS = tUBLK_ATTRS<N_VOXELS>;
  template<typename T>
  using sSCALAR_OR_ARRAY = tSCALAR_OR_ARRAY<T,N_UBLKS>;
  
#if BUILD_GPU
  static const size_t ALIGNMENT = N_VOXELS == N_VOXELS_8? 32 : 256;
#else
#if FORCE_64_BYTE_ALIGNMENT_OF_STATES
  static const size_t ALIGNMENT = 64;
#elif EXA_USE_SSE
  static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
  static const size_t ALIGNMENT = 32;
#endif
#endif

  static cSTRING name()        { return "MBLK"; }
  static asINT32 static_type() { return 0;          }

  __HOST__DEVICE__ static asINT32 explode_into_fine_state_index() { return 0; }
  __HOST__DEVICE__ static asINT32 coalesce_fine_state_index()     { return 0; }

  enum {
    moving_states_size   = sizeof(VOXEL_STATE) *N_MOVING_STATES*N_VOXELS,
    stop_state_size      = sizeof(VOXEL_STATE) *N_VOXELS,
    voxel_variables_size = sizeof(STP_PHYS_VARIABLE)*N_VOXELS
  };

#define ALIGNED_UBFLOAT T ALIGN_VECTOR

  __HOST__DEVICE__ sUBLK_UBFLOAT* to_ubfloat_ublk() { return reinterpret_cast<sUBLK_UBFLOAT*>(this); }
  __HOST__DEVICE__ sUBLK_SDFLOAT* to_sdfloat_ublk() { return reinterpret_cast<sUBLK_SDFLOAT*>(this); }

   // first two bytes will be squeezed behing the packed sSHOB struct
  tSPLIT_TAGGED_INSTANCE<N_VOXELS> m_split_tagged_instance;
  __ALIGN__(8) tBOX_ACCESS<UBLK_TYPE_TAG> m_box_access; //
  VOXEL_MASK           fluid_like_voxel_mask;   // voxels with pfluid > 0 [See footnote]
  VOXEL_MASK           basic_fluid_voxel_mask;  // voxels that are basic fluid (as opposed to porous, fan, etc) [See footnote]
  sSPLIT_INFO* m_split_info;
  sUBLK_ATTRS m_ublk_attributes;

  // These two flags used to be in a single flag, but that led to a race
  // condition between the UBLK_IB_BF_BCAST_SEND_GROUP and the bsurfel
  // update_position() function.  Now m_send_ib_bf_data is set by the UBLK_B
  // strands, and then cleared by the comm thread after the data is sent.

  using UBLK_CONNECT_MASK = tBITSET<N_CONNECT_MASK_BITS,uint8_t>;
  // Fluid connect masks contain a bit for each outward pointing latvec for each
  // voxel. A bit is set to true if any part of the fluid for this voxel lands
  // in the neighboring voxel along the latvec repesented by the bit.
  UBLK_CONNECT_MASK m_fluid_connect_masks[N_VOXELS];

  // Path connect masks contain a bit for each outward pointing latvec for each
  // voxel. A bit is set to true if any part of the fluid for this
  // voxel touches the fluid in the neighboring voxel along the latvec repesented
  // by the bit.
  UBLK_CONNECT_MASK m_path_connect_masks[N_VOXELS];

  VOXEL_MASK lrf_surfels_interaction_mask;
  VOXEL_MASK non_lrf_surfels_interaction_mask;
  VOXEL_MASK lb_interaction_voxel_mask;
  VOXEL_MASK pde_2_interaction_voxel_mask;

  // one bit for each voxel that has a different scale (D18 state) neighbor.
  // This is used while calculating pressure gradients in voxel_grads_phy.cc
  VOXEL_MASK m_are_any_neighbors_different_scale;
  // one bit for each voxel that has a split (D18 state) neighbor regardless of fluid connectivity.
  // This is used while calculating pressure gradients in voxel_grads_phy.cc
  // Also used during (PDE) gather advection in box_advect.cc
  VOXEL_MASK m_are_any_neighbors_split;

  // 2 bits per voxel to indicate the voxel abut different scale neighbor should go away
  using VR_SCALE_DIFF_VOXEL_MASK = tBITSET<2*N_VOXELS>;
  VR_SCALE_DIFF_VOXEL_MASK m_vr_scale_diff;
  sSCALAR_OR_ARRAY<STP_COORD> m_location[3];
  
  union {
    uINT8   m_dynamics_data_types[N_VOXELS]; // Maximum of 8 physics types
    uINT32  m_home_sp; // ghostblks don't have dynamics data blocks, so store the home sp here
  };
  // 84 bytes
  // Group of small, geometric parameters. [See footnote]
  sSCALAR_OR_ARRAY<sVR_TOPOLOGY> m_vr_topology;  
  VOXEL_MASK  voxel_smart_seed_mask;    // voxels which have been smart seeded
  VOXEL_MASK  all_neighbors_voxel_mask; // voxels where all neighbor voxels can be used in simple finite-differences [See footnote]
  VOXEL_MASK  neighbor_2_diff_scale_mask[6];  //new: in 6 coordinate dirs

  // 192 bytes
  bool m_send_ib_bf_data; // tells the sUBLK_IB_BF_BCAST_SEND_GROUP to send data
  bool m_is_bsurfel_interacting; // changes how this ublk is processed in the main thread
  sBSURFEL_BODY_FORCE *m_ib_bf;

  __HOST__DEVICE__ STP_COORD location(asINT32 axis, REQUIRE_INDEX_IF_MBLK(index)) { return m_location[axis][index]; }
  __HOST__DEVICE__ sVR_TOPOLOGY& vr_topology(REQUIRE_INDEX_IF_MBLK(index)) { return m_vr_topology[index]; }
  
  void fill_ib_bf_send_buffer(sdFLOAT* & send_buffer) {
    cassert(m_ib_bf);
    m_ib_bf->fill_send_buffer(send_buffer);
    // LOG_MSG("BSURFEL_COMM","ID",this->id(),LOG_FUNC).format("\n{}",*m_ib_bf);
  }

  void expand_ib_bf_recv_buffer(sdFLOAT* & recv_buffer) {
    if ( m_ib_bf == NULL ) {
      m_ib_bf = sSHOB_ALLOCATOR<sBSURFEL_BODY_FORCE>::malloc(sizeof(sBSURFEL_BODY_FORCE));
      memset(m_ib_bf, 0, sizeof(sBSURFEL_BODY_FORCE));
    }
    m_ib_bf->expand_recv_buffer(recv_buffer);
    // LOG_MSG("BSURFEL_COMM","ID",this->id(),LOG_FUNC).format("\n{}",*m_ib_bf);
  }

  __HOST__DEVICE__ BOOLEAN has_empty_split_info() const {
    return m_split_info == nullptr;
  }

  sSPLIT_NEIGHBOR_INFO *allocate_split_neighbor_info() {
    if (has_empty_split_info()) {
      m_split_info = cnew sSPLIT_INFO;
    }
    m_split_info->m_split_neighbor_info = new sSPLIT_NEIGHBOR_INFO;
    return m_split_info->m_split_neighbor_info;
  }

  __HOST__DEVICE__ sSPLIT_NEIGHBOR_INFO* split_neighbor_info() {
    return m_split_info? m_split_info->m_split_neighbor_info : nullptr;
  }

  VOID set_split_neighbor_info(sSPLIT_NEIGHBOR_INFO*  split_neighbor_info) {
    m_split_info->m_split_neighbor_info = split_neighbor_info;
  }

  __HOST__DEVICE__ typename sSPLIT_INFO::sSPLIT_ADVECT_INFO_TYPE* get_split_advect_info()
  {
    if constexpr (is_microblock<UBLK_TYPE_TAG>()) {
      return m_split_info? m_split_info->m_split_advection_info : nullptr;
    } else {
      return m_split_info;
    }
  }

  __HOST__DEVICE__ sSPLIT_ADVECT_FACTORS* get_split_advect_factors() {
    return m_split_info?
      m_split_info->get_split_advection_factors():
      nullptr;
  }

  tUBLK_TYPE* m_next;
  tUBLK_TYPE* m_prev;
  sUBLK_GROUP* m_group;

  // Solver-specific datablocks
  // For GPU builds no lb_data storage is allocated with compressed layout
  typename tUBLK_LB_DATA_OR_EMPTY_STRUCT<UBLK_TYPE_TAG>::type  m_lb_data;
  
  ubV3 centroids;
  
#if BUILD_5G_LATTICE
  ALIGNED_UBFLOAT porosity;  //5G large_pore
#endif

#if BUILD_D39_LATTICE && FORCE_64_BYTE_ALIGNMENT_OF_STATES

#if BUILD_DOUBLE_PRECISION
  //No padding needed, sizeof(UBLK) before padding == 1344 bytes
#else
  //Padding needed, sizeof(sUBLK) before padding  == 800 bytes
  char m_alignment_padding[32];
#endif

#elif BUILD_5G_LATTICE && FORCE_64_BYTE_ALIGNMENT_OF_STATES

#if BUILD_DOUBLE_PRECISION
  //No padding needed, sizeof(UBLK) before padding == 1024 bytes
#else
  //Padding needed, sizeof(sUBLK) before padding == 608 bytes, but with addition "porosity", the size of ublk becomes 640, no padding needed
  //char m_alignment_padding[32];
#endif

#elif BUILD_D19_LATTICE && FORCE_64_BYTE_ALIGNMENT_OF_STATES
  //No padding needed, sizeof(sUBLK) before padding == 1088 bytes DP
  //No padding needed, sizeof(sUBLK) before padding == 640 bytes SP
#endif


  ///////////////////////////////////////////////////////////
  //                     UBLK METHODS
  ///////////////////////////////////////////////////////////

  INLINE __HOST__ static uINT16& set_ublk_dynamics_data_size(asINT32 data_type) {
    return ::set_ublk_dynamics_data_size<N_VOXELS>(data_type);
  }

  INLINE __HOST__DEVICE__ static uINT16 get_ublk_dynamics_data_size(asINT32 data_type) {
    return ::get_ublk_dynamics_data_size<N_VOXELS>(data_type);
  }  

  __HOST__ VOID set_group(sUBLK_GROUP* group)  { m_group = group; }

  __HOST__ VOID set_box_indices(uINT16 indices[3], sUBLK_BOX *ublk_box);

  __HOST__DEVICE__ BOOLEAN are_any_neighbors_split() {
    return m_are_any_neighbors_split.any();
  }

  __HOST__DEVICE__ BOOLEAN are_any_neighbors_split(asINT32 voxel) {
    return m_are_any_neighbors_split.test(voxel);
  }

  __HOST__DEVICE__ VOID set_voxel_path_connect_mask(uINT32 voxel, CONNECT_MASK path_conn_mask) {
    m_path_connect_masks[voxel] = UBLK_CONNECT_MASK(path_conn_mask.get());
  }

  __HOST__DEVICE__ BOOLEAN are_any_neighbors_different_scale(asINT32 voxel) {
    return m_are_any_neighbors_different_scale.test(voxel);
  }

  __HOST__DEVICE__ VOID set_voxel_fluid_connect_mask(uINT32 voxel, CONNECT_MASK fluid_conn_mask) {
    m_fluid_connect_masks[voxel] = UBLK_CONNECT_MASK(fluid_conn_mask.get());
  }

  __HOST__DEVICE__ BOOLEAN has_basic_fluid(VOXEL_MASK voxel_mask) {
    return (basic_fluid_voxel_mask & voxel_mask).any();
  }

  __HOST__DEVICE__ BOOLEAN has_special_fluid() const {
    return (fluid_like_voxel_mask & (~basic_fluid_voxel_mask)).any();
  }
  
  __HOST__DEVICE__ CONNECT_MASK voxel_fluid_connect_mask(uINT32 voxel) {
    return CONNECT_MASK(m_fluid_connect_masks[voxel].get());
  }

  __HOST__DEVICE__ BOOLEAN is_voxel_path_connected_along_latvec(uINT32 voxel, STP_LATVEC_INDEX latvec) {
    return m_path_connect_masks[voxel].test(latvec);
  }

  __HOST__DEVICE__ BOOLEAN is_voxel_connected_along_latvec(uINT32 voxel, STP_LATVEC_INDEX latvec) {
    return m_path_connect_masks[voxel].test(latvec);
  }

  __HOST__DEVICE__ BOOLEAN is_voxel_connected_along_parity(uINT32 voxel, STP_LATVEC_INDEX latvec) {
    return m_fluid_connect_masks[voxel].test(latvec_parity(latvec));
  }

  __HOST__DEVICE__ BOOLEAN need_to_push_states(BOOLEAN is_timestep_even) {
    return (!does_advect_through_swap() ||
            !is_advection_done(is_timestep_even));
  }

  __HOST__ VOID set_exploded()   { m_ublk_attributes.m_is_exploded = 1;    }
  __HOST__ VOID unset_exploded() { m_ublk_attributes.m_is_exploded = 0;    }
  __HOST__DEVICE__ BOOLEAN is_exploded() { return m_ublk_attributes.m_is_exploded; }

  __HOST__DEVICE__ bool is_neighbor_finer_scale(asINT32 voxel)const {
    return m_vr_scale_diff.test(2 * voxel + VOXEL_FINE_SCALE_BIT);
  }

  __HOST__DEVICE__ bool is_neighbor_coarser_scale(asINT32 voxel) const{
    return m_vr_scale_diff.test(2 * voxel + VOXEL_COARSE_SCALE_BIT);
  }

  __HOST__ VOID set_neighbor_as_finer_scale(asINT32 voxel) {
    m_vr_scale_diff.set(2 * voxel + VOXEL_FINE_SCALE_BIT);
  }

  __HOST__ VOID set_neighbor_as_coarser_scale(asINT32 voxel) {
    m_vr_scale_diff.set(2 * voxel + VOXEL_COARSE_SCALE_BIT);
  }

  __HOST__DEVICE__ BOOLEAN are_voxor_voxels_near_vr_boundary(asINT32 voxor, asINT32 vmask) {
    if (!this->is_ublk_simple()) {
      ccDO_MASKED_VOXOR_VOXELS(voxel, voxor, vmask) {
        if (this->is_neighbor_finer_scale(voxel) ||
            this->is_neighbor_coarser_scale(voxel)) {
          return TRUE;
        }
      }
    }
    return FALSE;
  }
  
  /* Note:
   * In the case of MBLKs,
   * are_states_clear is TRUE if and ONLY if all child UBLKs have states cleared
   * set_states_clear is called if and ONLY if all child UBLKs have states cleared (see gpu_surfel_advect_phy.cu)
   */
  __HOST__DEVICE__ BOOLEAN are_states_clear() {
    return m_ublk_attributes.m_are_states_cleared_for_S2V;
  }
  __HOST__DEVICE__ VOID set_states_clear()    {
    m_ublk_attributes.m_are_states_cleared_for_S2V = 1;
  }

  __HOST__DEVICE__ VOID unset_states_clear()  {
    m_ublk_attributes.m_are_states_cleared_for_S2V = 0;
  }

  /* Note that the implementations of
   * __HOST__DEVICE__ BOOLEAN set_states_clear(int child_ublk)
   * __HOST__DEVICE__ BOOLEAN unset_states_clear(int child_ublk)
   * has been omitted on purpose since in most use cases, several GPU threads
   * can potentially call these functions. In these cases, it is better for
   * the implementation to use s2v_clear_data directly, to be aware of the
   * consequences of the S2V_CLEAR_DATA API calls
   *
   * Since are_states_clear is read only, it is thread safe and can be exposed here
   */

  __HOST__DEVICE__ BOOLEAN are_states_clear(int child_ublk) {
    if constexpr(is_microblock<UBLK_TYPE_TAG>()) {
      return this->are_states_clear();
    } else {
      return this->is_near_surface()?
        this->surf_geom_data()->s2v_clear_data.are_states_clear(child_ublk):
        are_states_clear();
    }
  }

  __HOST__DEVICE__ VOID toggle_advected()      { m_ublk_attributes.m_are_states_advected ^= 1; }
  __HOST__ VOID set_ublk_as_advected()   { m_ublk_attributes.m_are_states_advected = 1; }
  __HOST__ VOID unset_ublk_as_advected() { m_ublk_attributes.m_are_states_advected = 0; }
  // The meaning of m_are_states_advected is different on even and odd timesteps
  __HOST__DEVICE__ BOOLEAN is_advection_done(BOOLEAN is_timestep_even)const {
    return (!(m_ublk_attributes.m_are_states_advected ^ is_timestep_even));
  }

  __HOST__ VOID set_ublk_has_two_copy_neighbor(REQUIRE_INDEX_IF_MBLK(index)) {
    m_ublk_attributes.set_ublk_has_two_copy_neighbor(index);
  }
  
  __HOST__DEVICE__ BOOLEAN ublk_has_two_copy_neighbor(REQUIRE_INDEX_IF_MBLK(index)) const {
    return m_ublk_attributes.ublk_has_two_copy_neighbor(index);
  }
  __HOST__DEVICE__ BOOLEAN is_ublk_simple()const { return m_ublk_attributes.m_is_ublk_simple; }
  __HOST__ VOID set_ublk_simple()    { m_ublk_attributes.m_is_ublk_simple = 1; }

  __HOST__ VOID set_first_surfel()    { m_ublk_attributes.m_this_is_first_surfel = 1; }
  __HOST__DEVICE__ VOID unset_first_surfel()  { m_ublk_attributes.m_this_is_first_surfel = 0; }
  __HOST__DEVICE__ BOOLEAN is_first_surfel() const { return m_ublk_attributes.m_this_is_first_surfel; }

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol(asINT32 face,
                                                 REQUIRE_INDEX_IF_MBLK(index)) {
    return m_ublk_attributes.does_ublk_abut_simvol_on_face(face, index);
  }

  __HOST__DEVICE__ BOOLEAN does_ublk_abut_simvol(REQUIRE_INDEX_IF_MBLK(index)) const {
    return m_ublk_attributes.does_ublk_abut_simvol(index);
  }

  __HOST__DEVICE__ uINT8 ublk_type()     const    {return m_ublk_attributes.ublk_type();}

  __HOST__DEVICE__ BOOLEAN is_vr_fine() const     { return m_ublk_attributes.m_is_vr_fine;   }
  VOID   set_vr_fine()      { m_ublk_attributes.m_is_vr_fine = 1;      }
  __HOST__DEVICE__ BOOLEAN is_vr_coarse() const   { return m_ublk_attributes.m_is_vr_coarse; }
  VOID   set_vr_coarse()    { m_ublk_attributes.m_is_vr_coarse = 1;    }
  __HOST__DEVICE__ BOOLEAN is_vr_coarse_with_fine_children(REQUIRE_INDEX_IF_MBLK(index)) {
    return m_ublk_attributes.is_vr_coarse_with_fine_children(index);
  }
  VOID set_vr_coarse_with_fine_children(unsigned int v, REQUIRE_INDEX_IF_MBLK(index)) {
    m_ublk_attributes.set_vr_coarse_with_fine_children(v, index);
  }
  __HOST__DEVICE__ BOOLEAN has_mirror()      { return m_ublk_attributes.m_has_mirror;   }
  __HOST__DEVICE__ BOOLEAN is_split()     const   { return m_ublk_attributes.m_is_split;     }
  VOID    set_split()       { m_ublk_attributes.m_is_split = 1;        }
  __HOST__DEVICE__ VOID  unset_split()       { m_ublk_attributes.m_is_split = 0;        }
  __HOST__DEVICE__ BOOLEAN is_ghost() const       { return m_ublk_attributes.m_is_ghost;     }
  VOID    set_ghost()       { m_ublk_attributes.m_is_ghost = 1;        }
  __HOST__DEVICE__ BOOLEAN has_ghosts()  const    { return m_ublk_attributes.m_has_ghosts;   }
  VOID    set_has_ghosts()  { m_ublk_attributes.m_has_ghosts = 1;      }
  __HOST__DEVICE__ BOOLEAN is_near_surface()const { return m_ublk_attributes.m_does_interact_with_surface; }
  __HOST__ VOID set_is_near_regular_surface(REQUIRE_INDEX_IF_MBLK(index)) {
    m_ublk_attributes.set_is_near_regular_surface(index);
  }
  __HOST__DEVICE__ BOOLEAN is_near_regular_surface(REQUIRE_INDEX_IF_MBLK(index)) const {
    return m_ublk_attributes.is_near_regular_surface(index);
  }
  __HOST__DEVICE__ BOOLEAN has_two_copies()const  { return m_ublk_attributes.m_has_two_copies_of_states;   }
  __HOST__DEVICE__ BOOLEAN does_advect_through_swap()const { return !has_two_copies() && !is_ghost();};
  __HOST__DEVICE__ BOOLEAN is_fringe()     const  { return m_ublk_attributes.m_is_fringe;     }
  __HOST__DEVICE__ BOOLEAN is_fringe2(REQUIRE_INDEX_IF_MBLK(index)) const {
    return m_ublk_attributes.is_fringe2(index);
  }
  __HOST__DEVICE__ BOOLEAN is_fringe_or_fringe2(REQUIRE_INDEX_IF_MBLK(index))  const {
    return m_ublk_attributes.m_is_fringe || m_ublk_attributes.is_fringe2(index);
  }
  __HOST__DEVICE__ BOOLEAN is_tobefringe() const  { return m_ublk_attributes.m_is_tobefringe; }
  __HOST__ VOID    set_fringe()      { m_ublk_attributes.m_is_fringe = 1;        }
  __HOST__ VOID    set_fringe2(REQUIRE_INDEX_IF_MBLK(index)) { m_ublk_attributes.set_fringe2(index); }
  __HOST__ VOID    set_tobefringe()  { m_ublk_attributes.m_is_tobefringe = 1;    }
  __HOST__ VOID    unset_tobefringe(){ m_ublk_attributes.m_is_tobefringe = 0;    }

  __HOST__DEVICE__ BOOLEAN is_ghost_vr_fine() const     { return m_ublk_attributes.m_is_ghost_vr_fine;   }
  __HOST__ VOID    set_ghost_vr_fine()     { m_ublk_attributes.m_is_ghost_vr_fine = 1;      }

  __HOST__DEVICE__ BOOLEAN is_frozen(REQUIRE_INDEX_IF_MBLK(index)) const {
    return m_ublk_attributes.is_frozen(index);
  }
  __HOST__DEVICE__ VOID set_frozen(BOOLEAN value, REQUIRE_INDEX_IF_MBLK(index)) {
    m_ublk_attributes.set_frozen(value, index);
  }
  
#if BUILD_5G_LATTICE
  BOOLEAN is_mss_layer()    { return (m_location[2] == g_mp_mss_z); }
#endif  

  __HOST__DEVICE__ BOOLEAN is_conduction_solid() { 
#if !BUILD_GPU
    return m_ublk_attributes.m_is_conduction_solid;
#else
    return false;
#endif
  }
  BOOLEAN is_timestep_even() { return !sim_is_timestep_odd(scale(), g_timescale.m_timestep); }

  // These ublks contain real data to checkpoint
  __HOST__DEVICE__ BOOLEAN has_real_ckpt_data() const    { return m_ublk_attributes.m_has_real_ckpt_data;     }
  __HOST__ VOID    unset_real_ckpt_data()   { m_ublk_attributes.m_has_real_ckpt_data = 0;        }

  __HOST__DEVICE__ BOOLEAN is_mirror()     const         { return m_ublk_attributes.m_is_mirror;    }
  __HOST__ VOID set_is_mirror()             { m_ublk_attributes.m_is_mirror = 1;       }

  __HOST__DEVICE__ BOOLEAN is_solid() const              { return fluid_like_voxel_mask.none(); }
  __HOST__DEVICE__ BOOLEAN is_solid(int child_ublk) const{ return child_ublk_mask(fluid_like_voxel_mask, child_ublk).none(); }
  __HOST__DEVICE__ BOOLEAN is_voxel_solid(auINT32 voxel) { return !fluid_like_voxel_mask.test(voxel); }

  __HOST__DEVICE__ bool is_bsurfel_interacting() const { return m_is_bsurfel_interacting; }
  __HOST__DEVICE__ bool needs_to_send_ib_bf_data() { return m_send_ib_bf_data; }
  __HOST__ void set_send_ib_bf_data() { m_send_ib_bf_data = true; }
  __HOST__ void unset_send_ib_bf_data() { m_send_ib_bf_data = false; }

  __HOST__ VOID    set_mlrf_surfel_interacting() { m_ublk_attributes.m_is_mlrf_surfel_interacting = 1; }
  __HOST__DEVICE__ BOOLEAN is_mlrf_surfel_interacting() const  { return m_ublk_attributes.m_is_mlrf_surfel_interacting; }

  __HOST__DEVICE__ uINT8   advect_from_split_mask() const { return m_ublk_attributes.advect_from_split_mask(); }
  __HOST__ VOID unset_advect_from_split_mask() {
    m_ublk_attributes.unset_advect_from_split_mask();
  }
  __HOST__ VOID set_advect_from_split_mask(asINT32 voxel) {
    m_ublk_attributes.set_advect_from_split_mask(voxel);
  }

  __HOST__DEVICE__ BOOLEAN advect_from_split_ublk() const   { return m_ublk_attributes.m_advect_from_split_ublk; }
  __HOST__ VOID set_advect_from_split_ublk()   { m_ublk_attributes.m_advect_from_split_ublk = 1;    }
  __HOST__ VOID unset_advect_from_split_ublk() { m_ublk_attributes.m_advect_from_split_ublk = 0;    }

  __HOST__DEVICE__ BOOLEAN is_spatially_coherent() const { return m_ublk_attributes.m_is_spatially_coherent; }

  __HOST__DEVICE__ tUBLK_BOX<UBLK_TYPE_TAG> * get_ublk_box() {
    return m_box_access.m_ublk_box;
  }

  __HOST__ VOID set_ublk_box(tUBLK_BOX<UBLK_TYPE_TAG>* box) {
    m_box_access.m_ublk_box = box;
  }

  __HOST__ tUBLK_BOX_ACCESS<UBLK_TYPE_TAG>& box_access() {
    static_assert(UBLK_TYPE_TAG::N_VOXELS == N_VOXELS_8, "This verion of box_access should only be called for regular UBLKs.");
    return m_box_access.get_ublk_box_partition();
  }

  //It's to dangerous to permit accessing the box with a default child UBLK index on the GPU.
  //Most of the time it can lead to subtle incorrect behavior which is a pain to track down.
  //Please DO NOT add a default argument for this function.
  __HOST__DEVICE__ tUBLK_BOX_ACCESS<UBLK_TYPE_TAG>& box_access(asINT32 child_ublk_index) {
    return m_box_access.get_ublk_box_partition(child_ublk_index);
  }

  SP_TIMER_TYPE get_fluid_dyn_timer();

  __DEVICE__
  VOID explode_voxel(sUBLK_UBFLOAT *fine_ublk,
                     asINT32 voxel_to_explode,
                     ACTIVE_SOLVER_MASK solver_mask,
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask,
                     [[maybe_unused]] asINT32 gpu_fine_voxor)
  {
    if (this->is_conduction_solid()) {
      if (is_solver_active(CONDUCTION_SOLVER, solver_mask)) {
#if !BUILD_GPU
        this->conduction_data()->explode_voxel (fine_ublk->conduction_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
#else
        assert(false);
#endif
      }
    } else {
      auto& sim = get_sim_ref();
      if (is_solver_active(LB_SOLVER, solver_mask))
        this->lb_data()->explode_voxel(fine_ublk->lb_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
      if (is_solver_active(TURB_SOLVER, solver_mask))
        this->turb_data()->explode_voxel(fine_ublk->turb_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
      if (is_solver_active(T_SOLVER, solver_mask))
        this->t_data()->explode_voxel(fine_ublk->t_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
      if (is_solver_active(UDS_SOLVER, solver_mask)) {
        ccDOTIMES(nth_uds, sim.c()->n_user_defined_scalars) {
          this->uds_data(nth_uds)->explode_voxel(fine_ublk->uds_data(nth_uds), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
        }
#if BUILD_D19_LATTICE
        if (sim.c()->is_pf_model)
          this->pf_data()->explode_voxel (fine_ublk->pf_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
#endif
      }

      if (is_near_surface() && fine_ublk->is_near_surface()) {
        if (is_solver_active(LB_SOLVER, solver_mask))
          this->surf_lb_data()->explode_voxel(fine_ublk->surf_lb_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
        if (is_solver_active(TURB_SOLVER, solver_mask))
          this->surf_turb_data()->explode_voxel(fine_ublk->surf_turb_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
        if (is_solver_active(T_SOLVER, solver_mask))
          this->surf_t_data()->explode_voxel(fine_ublk->surf_t_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
        if (is_solver_active(UDS_SOLVER, solver_mask)) {
          ccDOTIMES(nth_uds, sim.c()->n_user_defined_scalars) {
            this->surf_uds_data(nth_uds)->explode_voxel(fine_ublk->surf_uds_data(nth_uds), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
          }
#if BUILD_D19_LATTICE
    if (sim.c()->is_pf_model)
            this->surf_pf_data()->explode_voxel (fine_ublk->surf_pf_data(), voxel_to_explode, prior_solver_ts_index_mask, gpu_fine_voxor);
#endif
        }
      }
    }
  }

  VOID explode_voxel_mme(sUBLK_UBFLOAT *fine_ublk, asINT32 voxel_to_explode,
                         ACTIVE_SOLVER_MASK solver_mask, SOLVER_INDEX_MASK solver_index_mask)
  {
    //UBLK_APPLY_ACTIVE_3(mask, explode_voxel, fine_ublk, voxel_to_explode, scale);
    if (!this->is_conduction_solid()) {
      if (is_solver_active(LB_SOLVER, solver_mask))
        this->lb_data()->explode_voxel_mme(fine_ublk->lb_data(), voxel_to_explode, solver_index_mask);
      if (is_solver_active(TURB_SOLVER, solver_mask))
        this->turb_data()->explode_voxel_mme(fine_ublk->turb_data(), voxel_to_explode, solver_index_mask);
      if (is_solver_active(T_SOLVER, solver_mask))
        this->t_data()->explode_voxel_mme(fine_ublk->t_data(), voxel_to_explode, solver_index_mask);
      if (is_solver_active(UDS_SOLVER, solver_mask)) {
        ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
          this->uds_data(nth_uds)->explode_voxel_mme(fine_ublk->uds_data(nth_uds), voxel_to_explode, solver_index_mask);
        }
#if BUILD_D19_LATTICE
        if (sim.is_pf_model)
          this->pf_data()->explode_voxel_mme(fine_ublk->pf_data(), voxel_to_explode, solver_index_mask);
#endif
      }
    }
  }

  INLINE uINT32 home_sp()
  {
    cassert( is_ghost() );
    return m_home_sp;
  }

  INLINE VOID set_bsurfel_interacting()
  {
    m_is_bsurfel_interacting = 1;
    if ( m_ib_bf == NULL ) {
      m_ib_bf = sSHOB_ALLOCATOR<sBSURFEL_BODY_FORCE>::malloc(sizeof(sBSURFEL_BODY_FORCE));
      memset(m_ib_bf, 0, sizeof(sBSURFEL_BODY_FORCE));
    }
    // LOG_MSG_IF(id() == 33003,"BSURFEL_COMM",LOG_FUNC);
  }

  INLINE VOID add_bsurfel_contribution(uINT8 voxel, dFLOAT weight, dFLOAT body_force[3], dFLOAT body_force_pfld[3], STP_DGEOM_VARIABLE area)
  {
    cassert(m_ib_bf != NULL);
    cassert(is_bsurfel_interacting());
    m_ib_bf->add_bsurfel_contribution(voxel, weight, body_force, body_force_pfld, area);

    // LOG_MSG("BSURFEL_COMM","ID",this->id(),LOG_FUNC).format("\n{}",*m_ib_bf);
  }

  INLINE VOID set_ib_interior(uINT8 voxel, BOOLEAN is_temp_pde_solver)
  {
    cassert(m_ib_bf != NULL);
    cassert(is_bsurfel_interacting());
    m_ib_bf->set_ib_interior(voxel);
    if (is_temp_pde_solver) {
      asINT32 prior_t_index = g_timescale.m_lb_tm.prior_timestep_index(scale());
      t_data()->set_diffusivity(prior_t_index, voxel) = g_nuT_floor;
    }
  };

  // This is currently called in physics (fluid_dyn_ublk_2, voxel_turb_omega_c)
  // It might be cheaper to set a DCACHE variable before doing dynamics instead.
  __HOST__DEVICE__ VOXEL_MASK get_ib_interior() {
    if ( m_ib_bf != NULL ) {
      return m_ib_bf->get_ib_interior();
    }
    else {
      return VOXEL_MASK{};
    }
  }

  INLINE VOID reset_ib_body_force()
  {
    if ( m_ib_bf != NULL ) {
      m_ib_bf->reset();
    }
  }

  INLINE VOID unset_is_bsurfel_interacting()
  {
    m_is_bsurfel_interacting = 0;
  }

  __HOST__DEVICE__ VOID get_voxel_location(asINT32 voxel, STP_LOCATION voxel_location)
  {
    asINT32 voxel_size = scale_to_voxel_size(scale());

    voxel_location[0] = m_location[0] + (voxel_size & (0 - num_to_voxel_x(voxel)));
    voxel_location[1] = m_location[1] + (voxel_size & (0 - num_to_voxel_y(voxel)));
    voxel_location[2] = m_location[2] + (voxel_size & (0 - num_to_voxel_z(voxel)));
  }

  void setid(SHOB_ID id) {
    m_id = id;
  }

  void setscale(int scale) {
    m_scale = scale;
  }

  VOID init(DGF_UBLK_BASE_DESC ublk_desc, size_t dyn_data_size);

  VOID init_p_data() {
    //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
    if (sim.is_particle_model) {
      VOXEL_MASK_8 voxel_mask{0};
      if(is_vr_fine()) {
        voxel_mask.set_all();
      }
      else {
        voxel_mask = fluid_like_voxel_mask;
      }
      this->p_data()->init2(voxel_mask);
    }
    //#endif
  }

  VOID set_distance_info(DGF_UBLK_BASE_DESC ublk_desc, BOOLEAN is_real_ublk_desc,
                         VOXEL_MASK_8 voxel_mask);

  VOID finalize_distance_info();

 public:

  // This is only for exceptional circumstances (like_checkpointing, when the voxel mask hasn't been established yet)
  __HOST__DEVICE__ auto lb_data_no_cassert() {
    return (sUBLK_LB_DATA *) BLOCK_LAYOUT::lb_data(this);
  }
  __HOST__DEVICE__ auto lb_data() {
    cassert(!is_solid());
    return lb_data_no_cassert();
  }
  __HOST__DEVICE__ auto lb_data() const {
    cassert(!is_solid());
    return (const sUBLK_LB_DATA *) BLOCK_LAYOUT::lb_data(const_cast<tUBLK_TYPE*>(this));
  }
  // This is only for exceptional circumstances (like_checkpointing, when the voxel mask hasn't been established yet)
  __HOST__DEVICE__ auto turb_data_no_cassert() {
    return (sUBLK_TURB_DATA*) BLOCK_LAYOUT::turb_data(this);
  }  
  __HOST__DEVICE__ auto turb_data() {
    cassert(!is_solid());    
    return turb_data_no_cassert();
  }
  __HOST__DEVICE__ auto turb_data() const {
    cassert(!is_solid());    
    return (const sUBLK_TURB_DATA*) BLOCK_LAYOUT::turb_data(const_cast<tUBLK_TYPE*>(this));
  }
  __HOST__DEVICE__ auto turb_data(BOOLEAN has_two_copies_of_states) {
    cassert(!is_solid());    
    return (sUBLK_TURB_DATA*) BLOCK_LAYOUT::turb_data(this, has_two_copies_of_states);
  }  
  __HOST__DEVICE__ auto t_data(){ return (sUBLK_T_DATA*) BLOCK_LAYOUT::t_data(this); }
  __HOST__DEVICE__ auto conduction_data() { return (sUBLK_CONDUCTION_DATA*) BLOCK_LAYOUT::conduction_data(this); }
  __HOST__DEVICE__ auto conduction_implicit_solver_data() {
    cassert(is_conduction_solid());
    return (sUBLK_CONDUCTION_IMPLICIT_SOLVER_DATA*) BLOCK_LAYOUT::conduction_implicit_solver_data(this); 
  }
  __HOST__DEVICE__ auto uds_data(){ return (sUBLK_UDS_DATA*) BLOCK_LAYOUT::uds_data(this); }
  __HOST__DEVICE__ auto uds_data(asINT32 nth_uds) {
    return (sUBLK_UDS_DATA*) BLOCK_LAYOUT::uds_data(this, nth_uds);
  }
  __HOST__DEVICE__ auto pf_data(){ return (sUBLK_PF_DATA*) BLOCK_LAYOUT::pf_data(this); } 
  __HOST__DEVICE__ auto mc_data(){ return (sUBLK_MC_DATA*) BLOCK_LAYOUT::mc_data(this); }
  __HOST__DEVICE__ auto avg_mme_data(){ return (sUBLK_MME_TURB_DATA*) BLOCK_LAYOUT::avg_mme_data(this); }
  __HOST__DEVICE__ auto avg_mme_t_data(){ return (sUBLK_MME_T_DATA*) BLOCK_LAYOUT::avg_mme_t_data(this); }
  __HOST__DEVICE__ auto avg_mme_uds_data(){ return (sUBLK_MME_UDS_DATA*) BLOCK_LAYOUT::avg_mme_uds_data(this); }
  __HOST__DEVICE__ auto avg_mme_uds_data(asINT32 nth_uds){ return (sUBLK_MME_UDS_DATA*) BLOCK_LAYOUT::avg_mme_uds_data(this, nth_uds); }
  __HOST__DEVICE__ auto avg_mme_conduction_data(){ return (sUBLK_MME_CONDUCTION_DATA*) BLOCK_LAYOUT::avg_mme_conduction_data(this); }
  __HOST__DEVICE__ auto p_data(){ return (sUBLK_PARTICLE_DATA*) BLOCK_LAYOUT::p_data(this); }
  __HOST__DEVICE__ auto surf_lb_data(){ return (sNEARBLK_LB_DATA*) BLOCK_LAYOUT::surf_lb_data(this); }
  __HOST__DEVICE__ auto surf_turb_data(){ return (sNEARBLK_TURB_DATA*) BLOCK_LAYOUT::surf_turb_data(this); }
  __HOST__DEVICE__ auto surf_t_data(){ return (sNEARBLK_T_DATA*) BLOCK_LAYOUT::surf_t_data(this); }
  __HOST__DEVICE__ auto surf_conduction_data() { return (sNEARBLK_CONDUCTION_DATA*) BLOCK_LAYOUT::surf_conduction_data(this); }
  __HOST__DEVICE__ auto surf_uds_data(){ return (sNEARBLK_UDS_DATA*) BLOCK_LAYOUT::surf_uds_data(this); }
  __HOST__DEVICE__ auto surf_uds_data(asINT32 nth_uds) {
    return (sNEARBLK_UDS_DATA*) BLOCK_LAYOUT::surf_uds_data(this, nth_uds);
  }
  __HOST__DEVICE__ auto surf_pf_data(){ return (sNEARBLK_PF_DATA*) BLOCK_LAYOUT::surf_pf_data(this); }
  __HOST__DEVICE__ auto surf_mc_data(){ return (sNEARBLK_MC_DATA*) BLOCK_LAYOUT::surf_mc_data(this); }
  __HOST__DEVICE__ auto surf_avg_mme_data(){ return (sNEARBLK_MME_DATA*) BLOCK_LAYOUT::surf_avg_mme_data(this); }
  __HOST__DEVICE__ auto surf_frozen_data(){ return (sNEARBLK_FROZEN_DATA*) BLOCK_LAYOUT::surf_frozen_data(this); }
  __HOST__DEVICE__ auto surf_geom_data(){ return (sNEARBLK_GEOM_DATA*) BLOCK_LAYOUT::surf_geom_data(this); }
  __HOST__DEVICE__ auto vr_data(){ return (sVR_INTERFACE_DATA*) BLOCK_LAYOUT::vr_data(this); }
  __HOST__DEVICE__ auto vr_fine_data(){
    cassert(is_vr_fine());
    return (sVR_FINE_INTERFACE_DATA*) BLOCK_LAYOUT::vr_fine_data(this);
  }
  __HOST__DEVICE__ auto vr_coarse_data(){    
    cassert(is_vr_coarse());
    return (sVR_COARSE_INTERFACE_DATA*) BLOCK_LAYOUT::vr_coarse_data(this);
  }

  __HOST__DEVICE__ auto mirror_data(){ return (sUBLK_MIRROR_DATA*) BLOCK_LAYOUT::mirror_data(this); }

  __HOST__DEVICE__ auto dynamics_data() {
    return (tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG> *) BLOCK_LAYOUT::dynamics_data(this);
  }
  
  // Double precision post advect scale factors are overlaid on states data during initialization
  __HOST__DEVICE__ auto double_precision_pas_factors() { return (dFLOAT *) BLOCK_LAYOUT::double_precision_pas_factors(this); }
  
  __HOST__DEVICE__ auto lb_states(asINT32 timestep_toggle) {
    return (sUBLK_STATES_DATA*) BLOCK_LAYOUT::lb_states(this, timestep_toggle);
  }
  __HOST__DEVICE__ auto lb_states(asINT32 timestep_toggle) const {
    return (const sUBLK_STATES_DATA*) BLOCK_LAYOUT::lb_states(const_cast<tUBLK_TYPE*>(this), timestep_toggle);
  }

  __HOST__DEVICE__ auto pore_lb_data() { return (sUBLK_PORE_LB_DATA *) BLOCK_LAYOUT::pore_lb_data(this); }
  __HOST__DEVICE__ auto pore_mc_data() {  return (sUBLK_PORE_MC_DATA *) BLOCK_LAYOUT::pore_mc_data(this); }
  __HOST__DEVICE__ auto pore_uds_data() { return (sUBLK_PORE_UDS_DATA *) BLOCK_LAYOUT::pore_uds_data(this); }
  __HOST__DEVICE__ auto pore_uds_data(asINT32 nth_uds) {
    return (sUBLK_PORE_UDS_DATA *) BLOCK_LAYOUT::pore_uds_data(this, nth_uds);
  }
  __HOST__DEVICE__ auto cbf_lb_data() { return (sUBLK_CBF_LB_DATA *) BLOCK_LAYOUT::cbf_lb_data(this); }

  //Smart seed data overlays the states of the ublk for INLINE BLOCK LAYOUT
  __HOST__DEVICE__ auto smart_seed_data() { return (sUBLK_SMART_SEED_DATA *) BLOCK_LAYOUT::smart_seed_data(this); }

  //Smart seed uds data overlays the lb_uds_data of the ublk for INLINE BLOCK LAYOUT
  __HOST__DEVICE__ auto smart_seed_uds_data(asINT32 nth_uds) {
    return (sUBLK_SMART_SEED_UDS_DATA *) uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY);
  }
  
  template<typename ATTRIBUTES_SPEC>
  static uINT64 size(sUBLK_ATTRS ublk_attrs,
                     asINT32 n_phys_types,
                     STP_PHYSTYPE_TYPE* phys_types,
                     ATTRIBUTES_SPEC attribute_data) {
    return BLOCK_LAYOUT::size(ublk_attrs, n_phys_types, phys_types, attribute_data);
  }

  template<typename ATTRIBUTES_SPEC>
  static uINT64 size_of_dyn_data(sUBLK_ATTRS ublk_attrs,
                                 asINT32 n_phys_types,
                                 STP_PHYSTYPE_TYPE* phys_types,
                                 ATTRIBUTES_SPEC attribute_data) {
    size_t dynamics_data_size = 0;
    // VRFINE, ghost  and mirror ublks should not have dynamics blocks
    // mirror_ublks have phys_types STP_UBLK_MIRROR_TYPE for which the size is 0
    if (!ublk_attrs.m_is_vr_fine && !ublk_attrs.m_is_ghost) {
      dynamics_data_size = compute_ublk_dynamics_data_size<tUBLK_SDFLOAT_TYPE_TAG<N_VOXELS>>(n_phys_types, phys_types, attribute_data);
    }
    return dynamics_data_size;
  }
  
  size_t size_of_dyn_data() const;

  uINT64 size() const {
    return BLOCK_LAYOUT::size(this);
  }
  
  __HOST__DEVICE__ VOID reset_states(VOXEL_MASK voxel_mask);

  __HOST__DEVICE__ VOID seed(BOOLEAN is_smart_seed, SOLVER_INDEX_MASK seed_solver_index_mask);

  sdFLOAT lb_states(asINT32 timestep_index, asINT32 latv, asINT32 voxel) {
    sdFLOAT *states = (sdFLOAT *) lb_states(timestep_index)->m_states[latv];
    return states[voxel];
  }

  sUBLK_SMART_SEED_DATA *alt_smart_seed_data() {
    return (sUBLK_SMART_SEED_DATA *) surf_lb_data()->post_advect_scale_factors;
  }

  // During initialization temporary data is stored on states and post_advect_scale_factors
  VOID process_surfel_interaction_data();
  VOID convert_dp_post_advect_scale_factors_to_sp();
  VOID seed_momentum_solver();
  VOID seed_T_pde_solver();
  VOID seed_T_scalar_solver();

  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
    __HOST__
    VOID maybe_init_states(asINT32 prev_lb_index, asINT32 prev_t_index, asINT32 prev_uds_index,
                           cBOOLEAN is_turb_model) {

    if (!are_states_clear()) {
      asINT32 curr_lb_index = 1 ^ prev_lb_index;
      asINT32 curr_t_index = 1 ^ prev_t_index;
      asINT32 curr_uds_index = 1 ^ prev_uds_index;
      VOXEL_STATE (*prev_states)[N_VOXELS] = lb_states(prev_lb_index)->m_states;
      VOXEL_STATE (*curr_states)[N_VOXELS] = lb_states(curr_lb_index)->m_states;
      VOXEL_STATE (*prev_states_t)[N_VOXELS] = NULL;
      VOXEL_STATE (*curr_states_t)[N_VOXELS] = NULL;
      VOXEL_STATE (*prev_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
      VOXEL_STATE (*curr_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
      if (IS_T_LB_SOLVER_ON) {
        prev_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(prev_t_index)->m_states_t;
        curr_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(curr_t_index)->m_states_t;
      }
      if (IS_UDS_LB_SOLVER_ON) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  prev_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds;
	  curr_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
        }
      }
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
#if BUILD_5X_SOLVER
      if (is_turb_model) {
        sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
        ccDOTIMES(i, N_MOVING_STATES) {
          ccDOTIMES (voxel, N_VOXELS) {
            //if (is_voxel_in_mask(voxel, ublk->fluid_like_voxel_mask)) {
            curr_states[i][voxel] = fractional_factor * prev_states[i][voxel];
            //}
          }
        }
      } else {
        memset(curr_states, 0, tUBLK::moving_states_size);
        //memcpy(curr_states, prev_states, tUBLK::moving_states_size);
      }
#else
      memset(curr_states, 0, tUBLK::moving_states_size);
#endif
      if (IS_T_LB_SOLVER_ON) {
        memset(curr_states_t, 0, tUBLK::moving_states_size);
      }      
#elif BUILD_5G_LATTICE
      cassert(g_adv_fraction == 1.0);
      memset(curr_states, 0, tUBLK::moving_states_size);
      if (g_is_multi_component) {
        VOXEL_STATE (*curr_states_mc)[N_VOXELS] = mc_data()->mc_states_data(curr_lb_index)->m_states_mc;
        VOXEL_STATE (*prev_states_mc)[N_VOXELS] = mc_data()->mc_states_data(prev_lb_index)->m_states_mc;
        memset(curr_states_mc, 0, tUBLK::moving_states_size);
        memcpy(curr_states_mc[V_0_0_0], prev_states_mc[V_0_0_0], tUBLK::stop_state_size);
      }
#endif

      if (IS_UDS_LB_SOLVER_ON) {
	ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
	  memset(curr_states_uds[nth_uds], 0, tUBLK::moving_states_size);
#if BUILD_D19_LATTICE
	if(sim.is_pf_model)
          memset(surf_pf_data()->srf_potential, 0, tUBLK::moving_states_size);
#endif
      }
      
      // TODO We do not need two copies of stop state
      ccDOTIMES (voxel, N_VOXELS) {
        curr_states[V_0_0_0][voxel] = prev_states[V_0_0_0][voxel];
      }
      if (IS_T_LB_SOLVER_ON) {
        ccDOTIMES (voxel, N_VOXELS) {
          curr_states_t[V_0_0_0][voxel] = prev_states_t[V_0_0_0][voxel];
        }
      }
      if (IS_UDS_LB_SOLVER_ON) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  ccDOTIMES (voxel, N_VOXELS) {
	    curr_states_uds[nth_uds][V_0_0_0][voxel] = prev_states_uds[nth_uds][V_0_0_0][voxel];
	  }
        }
      }
      set_states_clear();
    }

  }

  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
    __HOST__DEVICE__
    VOID maybe_init_states(asINT32 prev_lb_index, asINT32 prev_t_index, asINT32 prev_uds_index,
                           cBOOLEAN is_turb_model,
                           int voxel) {
    auto& simc = get_simc_ref();

    asINT32 curr_lb_index = 1 ^ prev_lb_index;
    asINT32 curr_t_index = 1 ^ prev_t_index;
    asINT32 curr_uds_index = 1 ^ prev_uds_index;
    
    VOXEL_STATE (*prev_states)[N_VOXELS] = lb_states(prev_lb_index)->m_states;
    VOXEL_STATE (*curr_states)[N_VOXELS] = lb_states(curr_lb_index)->m_states;
    VOXEL_STATE (*prev_states_t)[N_VOXELS] = NULL;
    VOXEL_STATE (*curr_states_t)[N_VOXELS] = NULL;
    VOXEL_STATE (*prev_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
    VOXEL_STATE (*curr_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
    if (IS_T_LB_SOLVER_ON) {
      prev_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(prev_t_index)->m_states_t;
      curr_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(curr_t_index)->m_states_t;
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars){
	prev_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds;
	curr_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
      }
    }
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
#if BUILD_5X_SOLVER
    if (is_turb_model) {
      sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
      ccDOTIMES(i, N_MOVING_STATES) {
        curr_states[i][voxel] = fractional_factor * prev_states[i][voxel];
      }
    } else {
      ccDOTIMES(i, N_MOVING_STATES) {
        curr_states[i][voxel] = 0;
      }
    }
#else
    ccDOTIMES(i, N_MOVING_STATES) {
      curr_states[i][voxel] = 0;
    }
#endif
    if (IS_T_LB_SOLVER_ON) {
      ccDOTIMES(i, N_MOVING_STATES) {
        curr_states_t[i][voxel] = 0;
      }
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds,simc.n_user_defined_scalars) {
	ccDOTIMES(i, N_MOVING_STATES) {
	  curr_states_uds[nth_uds][i][voxel] = 0;
	}
      }
#if BUILD_D19_LATTICE
      if (simc.is_pf_model) {
	ccDOTIMES(ip, N_MOVING_STATES)
	  surf_pf_data()->srf_potential[ip][voxel] = 0;
      }
#endif
    }
#elif BUILD_5G_LATTICE
    assert(false);
#endif
    // TODO We do not need two copies of stop state
    curr_states[V_0_0_0][voxel] = prev_states[V_0_0_0][voxel];

    if (IS_T_LB_SOLVER_ON) {
      curr_states_t[V_0_0_0][voxel] = prev_states_t[V_0_0_0][voxel];
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars){
	curr_states_uds[nth_uds][V_0_0_0][voxel] = prev_states_uds[nth_uds][V_0_0_0][voxel];
      }
    }
  }

  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
    __HOST__DEVICE__
    VOID maybe_init_states_vr_fine(asINT32 prev_lb_index, asINT32 prev_t_index, asINT32 prev_uds_index,
                                   cBOOLEAN is_turb_model,
                                   int voxel) {
    auto& simc = get_simc_ref();
    
    asINT32 curr_lb_index = 1 ^ prev_lb_index;
    asINT32 curr_t_index = 1 ^ prev_t_index;
    asINT32 curr_uds_index = 1 ^ prev_uds_index;
    VOXEL_STATE (*curr_states)[N_VOXELS] = lb_states(curr_lb_index)->m_states;
    VOXEL_STATE (*curr_states_t)[N_VOXELS] = NULL;
    VOXEL_STATE (*curr_states_uds)[N_VOXELS] = NULL;
    ccDOTIMES(i, N_MOVING_STATES) {
      curr_states[i][voxel] = 0;
    }
    if (IS_T_LB_SOLVER_ON) {
      curr_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(curr_t_index)->m_states_t;
      ccDOTIMES(i, N_MOVING_STATES) {
        curr_states_t[i][voxel] = 0;
      }
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds,simc.n_user_defined_scalars) {
	curr_states_uds = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
	ccDOTIMES(i, N_MOVING_STATES) {
	  curr_states_uds[i][voxel] = 0;
	}
      }
#if BUILD_D19_LATTICE
      if (simc.is_pf_model) {
	ccDOTIMES(ip, N_MOVING_STATES)
	  surf_pf_data()->srf_potential[ip][voxel] = 0;
      }
#endif
    }
  }

  template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
    VOID copy_states(asINT32 prev_lb_index, asINT32 prev_t_index, asINT32 prev_uds_index,
                     cBOOLEAN is_turb_model) {

    asINT32 curr_lb_index = 1 ^ prev_lb_index;
    asINT32 curr_t_index = 1 ^ prev_t_index;
    asINT32 curr_uds_index = 1 ^ prev_uds_index;
    VOXEL_STATE (*prev_states)[N_VOXELS] = lb_states(prev_lb_index)->m_states;
    VOXEL_STATE (*curr_states)[N_VOXELS] = lb_states(curr_lb_index)->m_states;
    VOXEL_STATE (*prev_states_t)[N_VOXELS] = NULL;
    VOXEL_STATE (*curr_states_t)[N_VOXELS] = NULL;
    VOXEL_STATE (*prev_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
    VOXEL_STATE (*curr_states_uds[MAX_N_USER_DEFINED_SCALARS])[N_VOXELS] = {NULL};
    if (IS_T_LB_SOLVER_ON) {
      prev_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(prev_t_index)->m_states_t;
      curr_states_t = (VOXEL_STATE (*)[N_VOXELS]) t_data()->lb_t_data(curr_t_index)->m_states_t;
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	prev_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds;
	curr_states_uds[nth_uds] = (VOXEL_STATE (*)[N_VOXELS]) uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
      }
    }

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
    if (is_turb_model) {
      sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
      ccDOTIMES(i, N_MOVING_STATES) {
        ccDOTIMES (voxel, N_VOXELS) {
          //if (is_voxel_in_mask(voxel, ublk->fluid_like_voxel_mask)) {
          curr_states[i][voxel] = prev_states[i][voxel];
          //}
        }
      }
    } else {
      memset(curr_states, 0, tUBLK::moving_states_size);
      //memcpy(curr_states, prev_states, tUBLK::moving_states_size);
    }
    if (IS_T_LB_SOLVER_ON) {
      ccDOTIMES(i, N_MOVING_STATES) {
        ccDOTIMES (voxel, N_VOXELS) {
          curr_states_t[i][voxel] = prev_states_t[i][voxel];
        }
      }
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
	ccDOTIMES(i, N_MOVING_STATES) {
	  ccDOTIMES (voxel, N_VOXELS) {
	    curr_states_uds[nth_uds][i][voxel] = prev_states_uds[nth_uds][i][voxel];
	  }
	}
      }
    }
#elif BUILD_5G_LATTICE
    cassert(g_adv_fraction == 1.0);
    memset(curr_states, 0, tUBLK::moving_states_size);
    if (g_is_multi_component) {
      VOXEL_STATE (*curr_states_mc)[N_VOXELS] = mc_data()->mc_states_data(curr_lb_index)->m_states_mc;
      VOXEL_STATE (*prev_states_mc)[N_VOXELS] = mc_data()->mc_states_data(prev_lb_index)->m_states_mc;
      memset(curr_states_mc, 0, tUBLK::moving_states_size);
      memcpy(curr_states_mc[V_0_0_0], prev_states_mc[V_0_0_0], tUBLK::stop_state_size);
    }
#endif
    // TODO We do not need two copies of stop state
    memcpy(curr_states[V_0_0_0], prev_states[V_0_0_0], tUBLK::stop_state_size);
    if (IS_T_LB_SOLVER_ON) {
      memcpy(curr_states_t[V_0_0_0], prev_states_t[V_0_0_0], tUBLK::stop_state_size);
    }
    if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	ccDOTIMES (voxel, N_VOXELS) {
	  curr_states_uds[nth_uds][V_0_0_0][voxel] = prev_states_uds[nth_uds][V_0_0_0][voxel];
	}
      }
    }
  }

  VOID determine_if_ublk_is_simple();
  VOID post_seed_init();

  __HOST__DEVICE__
  VOID post_advect_finalize(SOLVER_INDEX_MASK prior_solver_index_mask,
                            ACTIVE_SOLVER_MASK active_solver_mask) {
#if PERFORMANCE_TESTING
    if (!is_near_surface()) {
      msg_internal_error("post_advect_init should only be called for nearblks");
    }
#endif
    auto& simc = get_simc_ref();

    ACTIVE_SOLVER_MASK is_lb_active = active_solver_mask & LB_ACTIVE;
    ACTIVE_SOLVER_MASK is_turb_active = active_solver_mask & KE_PDE_ACTIVE;
    ACTIVE_SOLVER_MASK is_temp_active = active_solver_mask & T_PDE_ACTIVE;
    ACTIVE_SOLVER_MASK is_uds_active = active_solver_mask & UDS_PDE_ACTIVE;

    asINT32 prior_lb_index = lb_index_from_mask(prior_solver_index_mask);
    asINT32 curr_lb_index = 1 ^ prior_lb_index;
    VOXEL_STATE (*curr_states)[N_VOXELS] = lb_states(curr_lb_index)->m_states;
#if BUILD_5G_LATTICE
    VOXEL_STATE (*curr_states_mc)[N_VOXELS] = NULL;
    if (g_is_multi_component)
      curr_states_mc = mc_data()->mc_states_data(curr_lb_index)->m_states_mc;
#endif
    asINT32 prior_t_index = t_index_from_mask(prior_solver_index_mask);

    auto surf_lb = surf_lb_data();
#if BUILD_D19_LATTICE
    sNEARBLK_PF_DATA* surf_pf = NULL;
    if (simc.is_pf_model)
      surf_pf = surf_pf_data();
#endif
   
    // TODO Vectorize this
    ccDOTIMES(voxel, N_VOXELS) {
      STP_GEOM_VARIABLE inverse = 0.0;
      STP_GEOM_VARIABLE non_lrf_s2v_weight = surf_lb->s2v_weight[voxel] -
        surf_lb->lrf_s2v_weight[voxel];
      if (non_lrf_s2v_weight != 0.0)
        inverse = 1.0F / non_lrf_s2v_weight;
      surf_lb->pre_cf_n[voxel] = surf_lb->post_cf_n[voxel]*inverse;
      surf_lb->pre_delp_factor[voxel] = surf_lb->post_delp_factor[voxel]*inverse;  //lb_energy
      surf_lb->ustar_0[voxel] *= inverse;
#if BUILD_D19_LATTICE
      if (is_uds_active && simc.is_pf_model) 
	surf_pf->pre_cf_n[voxel] = surf_pf->post_cf_n[voxel]*inverse;
#endif

#ifdef DEBUG_CONDUCTION_SOLVER
      LOG_MSG_IF(this->id() == 178 && voxel == 6, "CONDUCTION_SOLVER", LOG_TS).printf("post_cf_n: %g, inverse: %g, pre_cf_n: %g",
         surf_lb->post_cf_n[voxel], inverse, surf_lb->pre_cf_n[voxel]);
#endif

      if (lrf_surfels_interaction_mask.test(voxel)) {
        inverse = 0.0;
        if (surf_lb->lrf_s2v_weight[voxel] != 0.0)
          inverse = 1.0F / surf_lb->lrf_s2v_weight[voxel];

        surf_lb->lrf_v2s_dist[voxel] *= inverse;
        surf_lb->lrf_s2s_factor[voxel] *= inverse;
        if (is_turb_active) {
          surf_lb->ustar_0_pair[voxel] *= inverse;
        }
      }
      if (lb_interaction_voxel_mask.test(voxel)) {
        inverse = 0.0;
        if (surf_lb->s2v_weight[voxel] != 0.0)
          inverse = 1.0F / surf_lb->s2v_weight[voxel];
        if (is_temp_active) {
          surf_t_data()->temp_wall[voxel] *= inverse;
        }
        if (is_turb_active) {
          if (g_some_face_has_ltt_transition_enabled) {
            surf_turb_data()->ltt_index[voxel] *= inverse;
          }
        }

        if (is_uds_active) {
          //DO_NEARBLK_UDS_DATA(n2, sim.n_user_defined_scalars, this, surf_uds) {
	  ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
	    auto surf_uds =surf_uds_data(nth_uds);
            surf_uds->uds_value[voxel] *= inverse;
          }
        }

        //CHECK: Any value to be finalized here?

        BOOLEAN is_some_state_negative = FALSE;
        BOOLEAN is_some_state_negative_mc = FALSE;
        ccDOTIMES(latv, N_MOVING_STATES)
        {
          if (curr_states[latv][voxel] < 0.0) {
            is_some_state_negative = TRUE;
            break;
          }
        }
        if (is_some_state_negative) {
          sdFLOAT temp_1;
          if (sim.thermal_feedback_is_on) {
            if (g_use_s2v_protection) {
              temp_1 = t_data()->fluid_temp[prior_t_index][voxel];
              force_ublk_states_in_range<tUBLK_TYPE>(curr_states, temp_1, voxel);
            }
          } else {
            temp_1 = g_lb_temp_constant;
            force_ublk_states_in_range<tUBLK_TYPE>(curr_states, temp_1, voxel);
          }
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          ccDOTIMES(latv, N_MOVING_STATES)
          {
            if (curr_states_mc[latv][voxel] < 0.0) {
              is_some_state_negative_mc = TRUE;
              break;
            }
          }
          if (is_some_state_negative_mc) {
            sdFLOAT temp_1;
            if (sim.thermal_feedback_is_on) {
              if (g_use_s2v_protection) {
                temp_1 = t_data()->fluid_temp[prior_t_index][voxel];
                force_ublk_states_in_range<tUBLK_TYPE>(curr_states_mc, temp_1, voxel);
              }
            } else {
              temp_1 = g_lb_temp_constant;
              force_ublk_states_in_range<tUBLK_TYPE>(curr_states_mc, temp_1, voxel);
            }
          }
        }
#endif
      }
    }
    size_t memset_data_size = voxel_variables_size;
    memset((void *)surf_lb->post_cf_n, 0, memset_data_size);
    memset((void *)surf_lb->post_delp_factor, 0, memset_data_size);
#if BUILD_D19_LATTICE
    if(is_uds_active && simc.is_pf_model){
      memset((void *)surf_pf->post_cf_n, 0, memset_data_size);
    }
#endif
  }

  VOID init_surfel_interaction_data();

  VOID set_voxel_masks(VOXEL_MASK_8 voxel_mask) {
    basic_fluid_voxel_mask = voxel_mask; // done in the constructor
    fluid_like_voxel_mask |= voxel_mask; // done in add_quantum_internal
  }

  sUBLK_MEAS_CELL_PTR *create_meas_cell_ptrs(tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG> *dyn_data,
                                             asINT32 n_meas_cells);

  VOID set_fluid_physics_descriptor(sPHYSICS_DESCRIPTOR *fluid_phys_desc);

  __HOST__DEVICE__ sLRF_PHYSICS_DESCRIPTOR *lrf_physics_descriptor() {
    LRF_PHYSICS_DESCRIPTOR lrf = NULL;
    if (ref_frame_index() >= 0) {
      auto& sim = get_sim_ref();
      lrf = &sim.lrf_physics_descs[ref_frame_index()];
    }
    return lrf;
  }
  __HOST__DEVICE__ BOOLEAN is_ublk_moving() {
    return (lrf_physics_descriptor()!= NULL && lrf_physics_descriptor()->is_mlrf_on);
  }

  VOID fill_dynamics_data(PHYSICS_DESCRIPTOR fluid_phys_desc,
                          VOXEL_MASK_8 voxel_mask,
                          STP_PHYSTYPE_TYPE sim_phys_type,
                          UBLK ublk,
                          BOOLEAN is_real_ublk_desc, DGF_UBLK_BASE_DESC ublk_desc,
                          tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_TYPE_TAG>* (&dynamics_data),
                          sUBLK_DYNAMICS_ATTR_DATA *attribute_data);

  VOID fill_uds_data(PHYSICS_DESCRIPTOR fluid_phys_desc,
		     VOXEL_MASK_8 voxel_mask);

  VOID fill_mirror_data(UBLK mirror_ublk, STP_STATE_INDEX state_index);

  VOID resolve_meas_cell_ptrs();

  VOID reflect_solver_data_from_real_voxel_to_mirror_voxel(sUBLK_SDFLOAT* mirror_ublk,
                                                           VOXEL_NUM mirror_voxel,
                                                           asINT32 voxel,
                                                           STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                           ACTIVE_SOLVER_MASK mask)
  {
    // note that the velocity will only be copied if the LB solver is active,
    copy_voxel_for_gradient_calculation(mirror_ublk, mirror_voxel, voxel, mask);
    // reflect the copied velocity if the LB solver is active
    if (!is_conduction_solid() && (mask & LB_ACTIVE)) {
      mirror_ublk->lb_data()->reflect_velocity(mirror_sign_factor, mirror_voxel);
    }
#if BUILD_D19_LATTICE
    if ((mask & UDS_PDE_ACTIVE) && sim.is_pf_model)
      mirror_ublk->pf_data()->reflect_velocity(mirror_sign_factor, mirror_voxel);
#endif
  }

  VOID copy_voxel_for_gradient_calculation(sUBLK_SDFLOAT* dest_ublk, asINT32 dest_voxel, asINT32 source_voxel, ACTIVE_SOLVER_MASK mask)
  { UBLK_APPLY_ACTIVE_3(mask, copy_voxel_for_gradient_calculation, dest_ublk, dest_voxel, source_voxel); }
  

  __HOST__DEVICE__ VOXEL_MASK only_lrf_surfels_mask(asINT32 index) {
    return surf_geom_data()->only_lrf_surfels_mask[index];
  }

  VOID reflect_mme_solver_data_from_real_voxel_to_mirror_voxel(sUBLK_SDFLOAT* mirror_ublk,
                                                               VOXEL_NUM mirror_voxel,
                                                               asINT32 voxel,
                                                               STP_PHYS_VARIABLE mirror_sign_factor[N_AXES],
                                                               ACTIVE_SOLVER_MASK mask)
  {
    // note that the velocity will only be copied if the LB solver is active,
    copy_voxel_mme_data(mirror_ublk, mirror_voxel, voxel, mask);
    // reflect the copied velocity if the LB solver is active
    if (!is_conduction_solid() && (mask & LB_ACTIVE)) {
      mirror_ublk->lb_data()->reflect_velocity(mirror_sign_factor, mirror_voxel);
      if (g_is_multi_component)
        mirror_ublk->mc_data()->reflect_velocity(mirror_sign_factor, mirror_voxel);
    }
#if BUILD_D19_LATTICE
    if ((mask & UDS_PDE_ACTIVE) && sim.is_pf_model)
      mirror_ublk->pf_data()->reflect_velocity(mirror_sign_factor, mirror_voxel);
#endif
  }


  VOID copy_voxel_mme_data(sUBLK_SDFLOAT *dest_ublk, asINT32 dest_voxel, asINT32 source_voxel, ACTIVE_SOLVER_MASK mask)
  { UBLK_APPLY_ACTIVE_3(mask, copy_voxel_mme_data, dest_ublk, dest_voxel, source_voxel); }

  //speed 2
  __HOST__DEVICE__ T*  pbl_p_2(asINT32 axis) { return  (surf_geom_data()->pbl_2 + STATE_P(axis)); }
  __HOST__DEVICE__ T*  pbl_n_2(asINT32 axis) { return  (surf_geom_data()->pbl_2 + STATE_N(axis)); }

  // shared between all ublk types
  VOID fill_send_init_info_buffer(UBLK_INIT_INFO_SEND_ELEMENT element) {
    element->m_fluid_like_voxel_mask = this->fluid_like_voxel_mask;
    element->m_all_neighbors_voxel_mask = this->all_neighbors_voxel_mask;
    element->m_voxel_smart_seed_mask = this->voxel_smart_seed_mask;
    memcpy(&element->m_vr_topology, &this->vr_topology(), sizeof(sVR_TOPOLOGY));
#if BUILD_5G_LATTICE
    memcpy(element->m_porosity, this->porosity, sizeof(element->m_porosity));
#endif 
    if (sim.is_conduction_model && this->is_conduction_solid() && sim.use_implicit_solid_solver) {
      //DEBUG_IMPLICIT
      //msg_print("fill_send_init_info: U %d index %d SP %d", this->id(), this->conduction_implicit_solver_data()->implicit_solid_state_index[0], this->m_home_sp);
      memcpy(element->m_implicit_solid_state_index, this->conduction_implicit_solver_data()->implicit_solid_state_index, sizeof(element->m_implicit_solid_state_index));
    }
  }

  VOID fill_send_init_info_for_nearblk(NEARBLK_INIT_INFO_SEND_ELEMENT element) {
    fill_send_init_info_buffer(&element->m_ublk_init_info);
    memcpy(element->m_pas_factors, surf_lb_data()->post_advect_scale_factors,
           sizeof(element->m_pas_factors));
    memcpy(element->m_pbl, surf_geom_data()->percent_boundary_layer,
           sizeof(element->m_pbl));
    // Comm ref_frame_index for nearblks since it is used in v2s
#if BUILD_D39_LATTICE
    element->m_ref_frame_index = this->ref_frame_index();
#elif BUILD_D19_LATTICE
    element->m_is_frozen = this->is_frozen();
#endif
  }

  VOID fill_send_init_info_for_cond_nearblk(COND_NEARBLK_INIT_INFO_SEND_ELEMENT element) {
    cassert(this->is_conduction_solid());
    memcpy(element->m_passthrough_summ_inv, this->surf_conduction_data()->passthrough_summ_inv,
           sizeof(element->m_passthrough_summ_inv));
  }

  VOID unpack_recv_init_info_buffer(UBLK_INIT_INFO_SEND_ELEMENT element) {
    // fluid like voxel mask is set to 0Xff for all ghost ublks during parsing
    this->fluid_like_voxel_mask = element->m_fluid_like_voxel_mask;
    this->all_neighbors_voxel_mask = element->m_all_neighbors_voxel_mask;
    this->voxel_smart_seed_mask = element->m_voxel_smart_seed_mask;
    memcpy(&this->vr_topology(), &element->m_vr_topology, sizeof(sVR_TOPOLOGY));
#if BUILD_5G_LATTICE
    memcpy(this->porosity, element->m_porosity, sizeof(element->m_porosity));
#endif
    if (sim.is_conduction_model && this->is_conduction_solid() && sim.use_implicit_solid_solver) {
      //memcpy(this->conduction_implicit_solver_data()->implicit_solid_state_index, element->m_implicit_solid_state_index, sizeof(element->m_implicit_solid_state_index));
      DO_ACTIVE_VOXELS(voxel){
        if (element->m_implicit_solid_state_index[voxel] != -1) {
          this->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] = element->m_implicit_solid_state_index[voxel] + sim.implicit_solid_solver_state_index_offset[this->m_home_sp];
          //DEBUG_IMPLICIT
          //msg_print("unpack: U %d V %d elem index %d SP %d this index %d", this->id(), voxel, element->m_implicit_solid_state_index[voxel], this->m_home_sp, this->conduction_implicit_solver_data()->implicit_solid_state_index[voxel]);
        } else {
          this->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] = -1;
        }
      }
    }
  }

  VOID unpack_recv_init_info_for_nearblk(NEARBLK_INIT_INFO_SEND_ELEMENT element) {
    unpack_recv_init_info_buffer(&element->m_ublk_init_info);
    memcpy(surf_lb_data()->post_advect_scale_factors, element->m_pas_factors,
           sizeof(element->m_pas_factors));
    memcpy(surf_geom_data()->percent_boundary_layer, element->m_pbl,
          sizeof(element->m_pbl));
#if BUILD_D39_LATTICE
    this->set_ref_frame_index(element->m_ref_frame_index);
#endif
    this->set_frozen(element->m_is_frozen);
  }

  VOID unpack_recv_init_info_for_cond_nearblk(COND_NEARBLK_INIT_INFO_SEND_ELEMENT element) {
    cassert(this->is_conduction_solid());
    memcpy(this->surf_conduction_data()->passthrough_summ_inv, element->m_passthrough_summ_inv,
           sizeof(element->m_passthrough_summ_inv));
    // This is likely unnecessary, but is being done to avoid issues with masks in ghost ublks being read accidently
    this->conduction_data()->reset_flux_accumulated_at_face_masks();
  }

  VOID fill_send_buffer_for_farblk(NEIGHBOR_MASK_INDEX nmi, ACTIVE_SOLVER_MASK active_solver_mask,
                                   sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index) {
    asINT32 lb_index = (timestep_index & LB_ACTIVE) >> LB_SOLVER;
    g_comm_compression_info.fill_by_recipe(send_data_info.send_buffer, (VOXEL_STATE *)lb_states(lb_index)->m_states, nmi);
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Sending farblk ghost info on %d[%d]",
        this->id(), timestep_index);
#endif
    UBLK_APPLY_ACTIVE(active_solver_mask, fill_send_buffer, (send_data_info, timestep_index));        
  }

  // These expand into ghostblocks, which have only one copy of the states. The timestep index is required, however,
  // for unpacking the solver data
  VOID expand_recv_buffer_for_farblk(NEIGHBOR_MASK_INDEX nmi, ACTIVE_SOLVER_MASK active_solver_mask,
                                     sUBLK_RECV_DATA_INFO &recv_data_info,  asINT32 timestep_index) {
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
    asINT32 lb_index = this->has_two_copies() ? (timestep_index & LB_ACTIVE) >> LB_SOLVER : ONLY_ONE_COPY;
    g_comm_compression_info.expand_by_recipe(recv_data_info.recv_buffer, (VOXEL_STATE *)lb_states(lb_index)->m_states, nmi);
#else
    g_comm_compression_info.expand_by_recipe(recv_data_info.recv_buffer, (VOXEL_STATE *)lb_states(ONLY_ONE_COPY)->m_states, nmi);
#endif
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Receiving farblk ghost info on %d[%d]",
        this->id(), timestep_index);
#endif
    UBLK_APPLY_ACTIVE(active_solver_mask, expand_recv_buffer,(recv_data_info,  timestep_index));    
  }

  VOID fill_send_buffer_for_nearblk(NEIGHBOR_MASK_INDEX nmi, ACTIVE_SOLVER_MASK active_solver_mask,
                                    sUBLK_SEND_DATA_INFO &send_data_info, asINT32 timestep_index) {
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    LOG_MSG_IF(this->id() == 392, "CONDUCTION_SOLVER", LOG_BASE_TS).printf("Filling nearblk ghost info on %d[%d] for %d",
        this->id(), timestep_index, active_solver_mask);
#endif
    if (!this->is_conduction_solid()) {
      asINT32 lb_index = (timestep_index & LB_ACTIVE) >> LB_SOLVER;
      g_comm_compression_info.fill_by_recipe(send_data_info.send_buffer, (VOXEL_STATE *)lb_states(lb_index)->m_states, nmi);
    }
    NEARBLK_APPLY_ACTIVE_EXCLUDE_SURF_T(active_solver_mask, fill_send_buffer, send_data_info, timestep_index, this->is_vr_fine());
    if (!this->is_conduction_solid()) {
      if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
        ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
          sUBLK_UDS_DATA* ublk_uds_data = this->uds_data(nth_uds);
          ublk_uds_data->fill_send_buffer(send_data_info, timestep_index);
        }
#if BUILD_D19_LATTICE
        if (sim.is_pf_model) {
          this->pf_data()->fill_send_buffer(send_data_info, timestep_index);
          this->surf_pf_data()->fill_send_buffer(send_data_info, timestep_index);
        }
#endif      
      }
#if BUILD_D19_LATTICE
      if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, active_solver_mask))
        this->surf_frozen_data()->fill_send_buffer(send_data_info, timestep_index);
#endif
    }
  }

  VOID expand_recv_buffer_for_nearblk(NEIGHBOR_MASK_INDEX nmi, ACTIVE_SOLVER_MASK active_solver_mask,
                                      sUBLK_RECV_DATA_INFO &recv_data_info,  asINT32 timestep_index) {
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    LOG_MSG_IF(this->id() == 392, "CONDUCTION_SOLVER", LOG_BASE_TS).printf("Receiving nearblk ghost info on %d[%d] for %d",
        this->id(), timestep_index, active_solver_mask);
#endif
    if (!this->is_conduction_solid()) {
      // For vr fine nearblks, we comm them to make sure the states are the same, thus expand to the corresponding states!
      asINT32 lb_index = (timestep_index & LB_ACTIVE) >> LB_SOLVER;
#ifdef ENFORCE_GHOST_UBLKS_WITH_TWO_STATE_COPIES
      if (this->has_two_copies())
#else
      if (this->is_vr_fine())
#endif
        g_comm_compression_info.expand_by_recipe(recv_data_info.recv_buffer, (VOXEL_STATE *)lb_states(lb_index)->m_states, nmi);
      else
        g_comm_compression_info.expand_by_recipe(recv_data_info.recv_buffer, (VOXEL_STATE *)lb_states(ONLY_ONE_COPY)->m_states, nmi);
    }
    NEARBLK_APPLY_ACTIVE_EXCLUDE_SURF_T(active_solver_mask, expand_recv_buffer, recv_data_info, timestep_index, this->is_vr_fine());
    if (!this->is_conduction_solid()) {
      if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
        ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
          sUBLK_UDS_DATA* ublk_uds_data = this->uds_data(nth_uds);
          ublk_uds_data->expand_recv_buffer(recv_data_info, timestep_index);
        }
#if BUILD_D19_LATTICE
        if (sim.is_pf_model) {
          this->pf_data()->expand_recv_buffer(recv_data_info, timestep_index);
          this->surf_pf_data()->expand_recv_buffer(recv_data_info, timestep_index);
        }
#endif
      }
#if BUILD_D19_LATTICE
      if (sim.store_frozen_vars && is_solver_active(LB_SOLVER, active_solver_mask))
        this->surf_frozen_data()->expand_recv_buffer(recv_data_info, timestep_index);
#endif
    }
  }

  VOID fill_mme_send_buffer(ACTIVE_SOLVER_MASK active_solver_mask,
                            sdFLOAT* &send_buffer, asINT32 solver_ts_index_mask) {
    UBLK_APPLY_ACTIVE(active_solver_mask, fill_mme_send_buffer, (send_buffer, solver_ts_index_mask));
  }

  VOID expand_mme_recv_buffer(ACTIVE_SOLVER_MASK active_solver_mask,
                              sdFLOAT* &recv_buffer,  asINT32 solver_ts_index_mask) {
    UBLK_APPLY_ACTIVE(active_solver_mask, expand_mme_recv_buffer,(recv_buffer,  solver_ts_index_mask));
    LOG_MSG_IF(id() == 28662,"BSURFEL_COMM","ghost",is_ghost(), LOG_ATTR(solver_ts_index_mask),LOG_TS) << "expand_mme_recv_buffer";
  }

#if BUILD_5G_LATTICE
  VOID fill_pore_send_buffer(sdFLOAT* &send_buffer) {
    this->pore_lb_data()->fill_pore_send_buffer(send_buffer);
    
    if (g_is_multi_component) {
      this->pore_mc_data()->fill_pore_send_buffer(send_buffer);
    }

    if (sim.is_scalar_model) {
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars; nth_uds++) {
	this->pore_uds_data(nth_uds)->fill_pore_send_buffer(send_buffer);
      }
    }
  }

  VOID expand_pore_recv_buffer(sdFLOAT* &recv_buffer) {
    this->pore_lb_data()->expand_pore_recv_buffer(recv_buffer);
    
    if (g_is_multi_component) {
      this->pore_mc_data()->expand_pore_recv_buffer(recv_buffer);
    }

    if (sim.is_scalar_model) {
      for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars; nth_uds++) {
	this->pore_uds_data(nth_uds)->expand_pore_recv_buffer(recv_buffer);
      }
    }
  }
#endif

  uINT64 ckpt_len(bool verbose=false);
  VOID write_ckpt(uINT64 len);
  size_t write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();
  size_t read_ckpt(u_char* buff);

  static VOID add_send_size_for_farblk(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size, asINT32 &tpde_send_size) {
    // This doesn't work because the APPLY macros use "this"; can't do that in a static member function
    //    UBLK_APPLY_ACTIVE(active_solver_mask, add_send_size, send_size);
    if (sim.is_conduction_sp) {
      if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask)) {
        tUBLK_CONDUCTION_DATA<UBLK_TYPE_TAG>::add_send_size(send_size, tpde_send_size);
      }
    } else {
      if (is_solver_active(LB_SOLVER, active_solver_mask)) {
        sUBLK_LB_DATA::add_send_size(send_size, tpde_send_size);
      }
      if (is_solver_active(TURB_SOLVER, active_solver_mask)) {
        sUBLK_TURB_DATA::add_send_size(send_size, tpde_send_size);
      }
      if (is_solver_active(T_SOLVER, active_solver_mask)) {
        sUBLK_T_DATA::add_send_size(send_size, tpde_send_size);
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask)) {
        sUBLK_MC_DATA::add_send_size(send_size, tpde_send_size);
      }
#endif
      if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
        sUBLK_UDS_DATA::add_send_size(send_size, tpde_send_size);
#if BUILD_D19_LATTICE
        if(sim.is_pf_model)
          sUBLK_PF_DATA::add_send_size(send_size, tpde_send_size);
#endif
      }
    }
  }

  static VOID add_send_size_for_nearblk(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size, asINT32 &tpde_send_size) {
    if (sim.is_conduction_sp) {
      if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask)) {
        tUBLK_CONDUCTION_DATA<UBLK_TYPE_TAG>::add_send_size(send_size, tpde_send_size);
        tNEARBLK_CONDUCTION_DATA<UBLK_TYPE_TAG>::add_send_size(send_size, tpde_send_size);
      }
    } else {
      if (is_solver_active(LB_SOLVER, active_solver_mask)) {
        sUBLK_LB_DATA::add_send_size(send_size, tpde_send_size);
        sNEARBLK_LB_DATA::add_send_size(send_size, tpde_send_size);
      }
      if (is_solver_active(TURB_SOLVER, active_solver_mask)) {
        sUBLK_TURB_DATA::add_send_size(send_size, tpde_send_size);
        sNEARBLK_TURB_DATA::add_send_size(send_size, tpde_send_size);
      }
      if (is_solver_active(T_SOLVER, active_solver_mask)) {
        sUBLK_T_DATA::add_send_size(send_size, tpde_send_size);
        // tNEARBLK_T_DATA<UBFLOAT_DATA_TYPE>::add_send_size(send_size, tpde_send_size);
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask)) {
        sUBLK_MC_DATA::add_send_size(send_size, tpde_send_size);
        sNEARBLK_MC_DATA::add_send_size(send_size, tpde_send_size);
      }
#endif
      if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
        sUBLK_UDS_DATA::add_send_size(send_size, tpde_send_size);
#if BUILD_D19_LATTICE
        if(sim.is_pf_model){
          sUBLK_PF_DATA::add_send_size(send_size, tpde_send_size);
          sNEARBLK_PF_DATA::add_send_size(send_size, tpde_send_size);
        }
#endif
      }
#if BUILD_D19_LATTICE
      if (sim.store_frozen_vars && (active_solver_mask & LB_ACTIVE))
        sNEARBLK_FROZEN_DATA::add_send_size(send_size);
#endif
    }
  }

  static VOID add_mme_send_size(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size) {
    if (is_solver_active(LB_SOLVER, active_solver_mask)) {
      sUBLK_LB_DATA::add_mme_send_size(send_size);
    }
#if BUILD_D39_LATTICE || BUILD_D19_LATTICE
    if (is_solver_active(T_SOLVER, active_solver_mask)) {
      sUBLK_T_DATA::add_mme_send_size(send_size);
    }
#endif
#if BUILD_5G_LATTICE
    if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask)) {
      sUBLK_MC_DATA::add_mme_send_size(send_size);
    }
#endif
#if BUILD_D19_LATTICE
    if (is_solver_active(UDS_SOLVER, active_solver_mask)) {
      sUBLK_UDS_DATA::add_mme_send_size(send_size);
      if(sim.is_pf_model) {
        sUBLK_PF_DATA::add_mme_send_size(send_size);
      }
    }
#endif
  }

#if BUILD_5G_LATTICE
  static VOID add_pore_send_size(ACTIVE_SOLVER_MASK active_solver_mask, asINT32 &send_size) {
    //if (sim.is_large_pore) { //no need
    {
      tUBLK_PORE_LB_DATA<UBLK_TYPE_TAG>::add_pore_send_size(send_size);
    
      if (g_is_multi_component)
        tUBLK_PORE_MC_DATA<UBLK_TYPE_TAG>::add_pore_send_size(send_size);

      if (sim.is_scalar_model)
        tUBLK_PORE_UDS_DATA<UBLK_TYPE_TAG>::add_pore_send_size(send_size);
    }
  }
#endif
  
  VOID print_ublk_content(std::ostream& os,
                          const tUBLK_PRINT_OPTS& opts)
  {

    int time_adjusted = (opts.timestep >=0) ? opts.timestep:g_timescale.m_end_time+opts.timestep;

    asINT32 time_index = (1 ^ g_timescale.m_lb_tm.prior_timestep_index(scale()));
    if ( g_timescale.m_time >= time_adjusted ){
      tUBLK_PRINT_OPTS::init_stream(os);
      bool is_ublk_ghost = is_vr_fine() ? is_ghost_vr_fine() : is_ghost();
      if (opts.voxel_id != -1) {
        opts.print_ublk_header(os, opts.voxel_id, id(), g_timescale.m_time,
                               scale(), is_ublk_ghost,  has_two_copies(), time_index, centroids);
        print_ublk_voxel_content(os, opts, opts.voxel_id);
        opts.print_ublk_footer(os, opts.voxel_id, id(), g_timescale.m_time);
      } else {    // print all voxels
        ccDOTIMES(print_voxel, N_VOXELS) {
          opts.print_ublk_header(os, print_voxel, id(), g_timescale.m_time,
                                 scale(), is_ublk_ghost, has_two_copies(), time_index, centroids);
          print_ublk_voxel_content(os, opts, print_voxel);
          opts.print_ublk_footer(os, print_voxel, id(), g_timescale.m_time);
        }
      }
    }
  }

  BOOLEAN has_no_dynamics_data() {
    return is_solid() || is_mirror() || is_ghost() || is_vr_fine();
  }
  
  template<UBLK_DATA_TYPE data_type>
  static __HOST__DEVICE__ bool does_ublk_have_data_of_type(sUBLK* ublk) {
    //Solid UBLKs are special, even though they have a valid type, they are not assigned
    //storage to hold special data. Solid VR ublks have data
    return BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE>(ublk) &&
           (!ublk->is_solid() || (ublk->is_solid() && (ublk->is_vr_fine() || ublk->is_vr_coarse())));
  }
  
  VOID print_ublk_voxel_content(std::ostream& os,
                                const tUBLK_PRINT_OPTS& opts,
                                asINT32 print_voxel);

  VOID print_ublk_lb_states_content(std::ostream& os,
                                    asINT32 print_voxel,
                                    asINT32 states_index);

#undef ALIGNED_UBFLOAT
};


///////////////////////////////////////////////////////////////////////
//UBLK ALIGNMENT TEST

#if FORCE_64_BYTE_ALIGNMENT_OF_STATES
 static_assert(((sUBLK::ALIGNMENT & 63) == 0) && ((sizeof(sUBLK) & 63) == 0),
	       "Start address of UBLK and sizeof(UBLK) must be 64 byte aligned if "
	       "the states are to enforce 64 byte alignment of states");
#endif

///////////////////////////////////////////////////////////////////////
} //SIMULATOR_NAMESPACE

#ifdef BUILD_GPU
typedef tUBLK<UBLK_SDFLOAT_TYPE_TAG> sUBLK, *UBLK;
typedef tUBLK<MBLK_SDFLOAT_TYPE_TAG> sMBLK, *MBLK;
constexpr size_t N_UBLKS_PER_MBLK = sMBLK::N_VOXELS / sUBLK::N_VOXELS;
#else
typedef tUBLK<UBLK_SDFLOAT_TYPE_TAG> sUBLK, *UBLK;
#endif

#if GPU_COMPILER
typedef tUBLK<MBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT, *UBLK_UBFLOAT;
typedef tUBLK<MBLK_SDFLOAT_TYPE_TAG> sUBLK_SDFLOAT, *UBLK_SDFLOAT;
#else
typedef tUBLK<UBLK_UBFLOAT_TYPE_TAG> sUBLK_UBFLOAT, *UBLK_UBFLOAT;
typedef tUBLK<UBLK_SDFLOAT_TYPE_TAG> sUBLK_SDFLOAT, *UBLK_SDFLOAT;
#endif

// ubsan complains when calling a member function on a nullptr,
// so use this function to add an explicit nullptr check
// and ubsan is correct - by using a member function, the compiler
// could assume it is NOT null, and optimize based on that assumption.
// That would be bad.
template<typename UBLK_TYPE>
typename UBLK_TYPE::sUBLK_SDFLOAT* ublk_to_sdfloat_safe(UBLK_TYPE* ublk) {
  if (ublk != nullptr) {
    return ublk->to_sdfloat_ublk();
  } else {
    return nullptr;
  }
}

VOID reduce_to_one_copy(sUBLK* ublk);

template<typename UBLK_TYPE>
__HOST__DEVICE__
inline VOID convert_vel_between_ref_frames_uargs(STP_PHYS_VARIABLE input_vel[3],
                                                 asINT32 voxel, UBLK_TYPE ublk,
                                                 STP_PHYS_VARIABLE output_vel[3],
                                                 asINT32 from_ref_frame_index,
                                                 asINT32 to_ref_frame_index) {
  STP_GEOM_VARIABLE surfel_centroid[3];
  surfel_centroid[0] = ublk->centroids(voxel, 0);
  surfel_centroid[1] = ublk->centroids(voxel, 1);
  surfel_centroid[2] = ublk->centroids(voxel, 2);
  convert_vel_between_ref_frames(input_vel, surfel_centroid, output_vel,
                                 from_ref_frame_index, to_ref_frame_index);
}


//#include "ublk_neighbors_iterate.h"
#include "box_advect.h"

/*--------------------------------------------------------------------------
 * Footnote: Basic vs Special Fluid Voxels
 *
 * For every simulation, there is a distinguished voxel dynamics type that is
 * considered to be the "basic" fluid type. VVFLUID is currently the one basic
 * fluid supported in the code. There can be several variants of the basic fluid
 * type (currently VVFLUID and NEAR_SURFACE_VVFLUID) as long as a ublk never
 * contains a mixture of the variants. This means that we must promote VVFLUID
 * voxels to NEAR_SURFACE_VVFLUID voxels to meet this restriction.
 *
 * We choose to distinguish one fluid type per simulation so that certain voxel 
 * dynamics related information for the distinguished type can be stored in the 
 * ublk itself, rather than in the voxel dynamics quantum.
 *
 * The restriction that a ublk never contain a mixture of the basic fluid
 * variants may increase performance because certain computations (e.g., moment
 * calculations and fluid dynamics) can be performed for several voxels
 * simultaneously.
 * 
 * Fluid-like voxel dynamics types other than the basic type are "special"
 * types. We have the same performance-related motivation to promote special
 * fluid voxels in order to guarantee that a ublk never contain a mixture of 
 * the variants of a special fluid type.
 *
 * For VR fine ublks this mask contains the real fluid mask from the lgi file
 * which has been overwritten to 1's in fluid_like_voxel_mask.
 *--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------
 * Footnote: fluid_like_voxel_mask
 * 
 * For ghostblks and mirror ublks, fluid_like_voxel_mask is a copy of the voxel
 * mask that resides in the associated dynblk or vrblk. As such, it indicates
 * which voxels are fluid.
 *
 * For a VR-fine ublk, fluid_like_voxel_mask is 0 if the parent coarse voxel
 * corresponds to a 0 in the coarse ublk fluid_like_voxel_mask. Otherwise,
 * fluid_like_voxel_mask for the VR-fine ublk indicates which VR fine voxels
 * overlap the fluid portion of the associated coarse voxel.
 *--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------
 * Footnote: all_neighbors_voxel_mask
 * 
 * For a dynamics voxel (pfluid > 0), a bit is set in this mask if all neighbors
 * of the voxel are simple (i.e., same scale) and the voxel is not near the
 * surface (i.e., PBL is 0).
 *
 * For an advect-only voxel (pfluid = 0) in a dynblk, this bit is also set if
 * the voxel will contain fluid values that can be used in finite-difference
 * calculations (i.e. the advect-only voxel is the target of a pre-grad copy
 * operation). 
 *
 * Currently the setting for advect-only voxels is only used by
 * VOXEL_DYN_GROUP::post_seed_init to check the validity of connect masks and
 * neighbor pointers (i.e. if a voxel's connect mask says it is connected on a
 * face, will the wired neighbor voxel on that face have valid fluid data for
 * use in finite difference calculations).
 *
 * @@@ Current defect: everywhere that fluid_like_voxel_mask is used to guide
 * the process of adding up 8 fine voxels for a gradient calculation should
 * actually use (fluid_like_voxel_mask | all_neighbors_voxel_mask).
 *--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------
 * Footnote: basic_fluid_voxel_mask 
 * 
 * The basic_fluid_voxel_mask field indicates which voxels are of the basic
 * fluid type found in the simulation. So, for instance, in a simulation which
 * includes standard fluid and porous media, only standard fluid voxels are
 * mentioned in this mask (whether they are far-from surface or near-surface
 * fluid). Remember that all standard fluid voxels within a ublk are guaranteed
 * to be of the same variety.
 * *--------------------------------------------------------------------------*/

#if BUILD_GPU
extern std::vector<sHMBLK::UBLK_BOX> g_mega_boxes;
#endif

#endif  /* UBLK_H_ */
