/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include XNEW_H
#include "read_pm_lgi_records.h"


extern LGI_STREAM g_lgi_stream; //SP stream




//Methods for reading an LGI emitter configuration from the LGI stream and the associated string characters:

VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER::read(){
  //read from the LGI stream into this structure without consumming any string data
  lgi_read(g_lgi_stream, this, sizeof( sLGI_PARTICLE_EMITTER_CONFIGURATION_REC));
}
VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER::read_common_strings(std::string &name, std::string &emission_rate_variable_name) //read the string data common to all config types from the lgi stream
{
  //read the name
  lgi_read(g_lgi_stream, name, name_length);

  //read the emission rate variable name
  lgi_read(g_lgi_stream, emission_rate_variable_name,
           emission_rate_variable_name_length);

  //printf("SP read %d bytes for emitter_config %s name.\n", name_length, name.c_str());
  //printf("SP read %d bytes for emitter_config %s emission_rate_variable_name.\n", emission_rate_variable_name_length, name.c_str());

}

VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER::read_nozzle_strings() {
  //Nothing special is needed for now.
}

VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER::read_rain_strings(
                                                                       std::string &wind_x_name,
                                                                       std::string &wind_y_name,
                                                                       std::string &wind_z_name) {
  //Read the equation names for the three components of wind velocity vector.
  lgi_read(g_lgi_stream, wind_x_name, specialized_parameters.rain_params.wind_x_name_length);
  lgi_read(g_lgi_stream, wind_y_name, specialized_parameters.rain_params.wind_y_name_length);
  lgi_read(g_lgi_stream, wind_z_name, specialized_parameters.rain_params.wind_z_name_length);
}

VOID sLGI_PARTICLE_EMITTER_CONFIGURATION_REC_READER::
read_tire_strings(std::string &angular_emission_dist_parameter_variable_name,
                  std::string &angular_emission_dist_ratio_variable_name)
{

  //  read the angular_emission_dist_parameter_variable name
  //  read the emission_rate_ratio_variable_name


  lgi_read(g_lgi_stream, angular_emission_dist_parameter_variable_name,
           specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length);

  lgi_read(g_lgi_stream, angular_emission_dist_ratio_variable_name,
           specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length);

  //printf("SP read %d bytes for emitter_config angular_emission_distribution_parameter.\n",
  //       specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length );
  //printf("SP read %d bytes for emitter_config angular_emission_distribution_ratio.\n",
  //       specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length);


}



//Define some methods that read from an LGI stream into a nozzle data record and consume the proper number of string characters.

//Read the fixed size data
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read() {
  lgi_read(g_lgi_stream, this, sizeof(sLGI_NOZZLE_CONFIGURATION_REC));
  //printf("SP read %d bytes for tire station fixed size data.\n",sizeof(sLGI_NOZZLE_CONFIGURATION_REC));

}

//Read in strings that are common to all types of nozzle data
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_common_strings( std::string &diameter_distribution_param1_variable_name, std::string &diameter_distribution_param2_variable_name) {
  //read in mean diameter variable name
  lgi_read(g_lgi_stream, diameter_distribution_param1_variable_name,
           diameter_distribution_param1_variable_name_length);

  //read in diameter range variable name
  lgi_read(g_lgi_stream, diameter_distribution_param2_variable_name,
           diameter_distribution_param2_variable_name_length);

  //printf("SP read %d bytes for tire station diameter dist param1.\n",
  //       diameter_distribution_param1_variable_name_length);
  
  //printf("SP read %d bytes for tire station diameter dist param2.\n",
  //       diameter_distribution_param2_variable_name_length);
  

}

//Read in strings that are written only for tire emitters
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_tire_strings( std::string &velocity_magnitude_distribution_param1_variable_name,
                                                       std::string &velocity_magnitude_distribution_param2_variable_name,
                                                       std::string &tire_arc_position_variable_name,
                                                       std::string &emission_offset_angle_variable_name,
                                                       std::string &cone_half_angle_variable_name,
                                                       std::string &stretch_factor_variable_name) {

   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param1_variable_name,
           specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length);


   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param2_variable_name,
           specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length);


   lgi_read(g_lgi_stream, tire_arc_position_variable_name,
           specialized_parameters.tire_params.tire_arc_position_variable_name_length);

   lgi_read(g_lgi_stream, emission_offset_angle_variable_name,
           specialized_parameters.tire_params.emission_offset_angle_variable_name_length);

   lgi_read(g_lgi_stream, cone_half_angle_variable_name,
           specialized_parameters.tire_params.cone_half_angle_variable_name_length);

   lgi_read(g_lgi_stream, stretch_factor_variable_name,
           specialized_parameters.tire_params.stretch_factor_variable_name_length);
#if 0
   printf("SP read %d bytes for tire station vel mag dist param1.\n",
          specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length );
   
   printf("SP read %d bytes for tire station vel mag dist param2.\n",
          specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length );
   printf("SP read %d bytes for tire station position.\n",
          specialized_parameters.tire_params.tire_arc_position_variable_name_length );
   printf("SP read %d bytes for cone half angle var.\n",
          specialized_parameters.tire_params.cone_half_angle_variable_name_length);
   printf("SP read %d bytes for stretch factor var.\n",
          specialized_parameters.tire_params.stretch_factor_variable_name_length);
#endif
  
}


//Read in strings that are written only for full cone nozzle data
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_full_cone_strings( std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                   std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                   std::string &cone_half_angle_variable_name,
                                                                   std::string &outer_half_angle_limit_variable_name) {

 
   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param1_variable_name,
           specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length);


   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param2_variable_name,
           specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length);


   lgi_read(g_lgi_stream, cone_half_angle_variable_name,
           specialized_parameters.full_cone_params.cone_half_angle_variable_name_length);
   
   lgi_read(g_lgi_stream, outer_half_angle_limit_variable_name,
           specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length);

}

//Read in strings specific to hollow cone nozzle configuration data
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_hollow_cone_strings( std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                     std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                     std::string &angle_distribution_param1_variable_name,
                                                                     std::string &angle_distribution_param2_variable_name,
                                                                     std::string &outer_half_angle_limit_variable_name,
                                                                     std::string &inner_half_angle_limit_variable_name) {

   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param1_variable_name,
           specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length);

   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param2_variable_name,
           specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length);

   lgi_read(g_lgi_stream, angle_distribution_param1_variable_name,
           specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length);

   lgi_read(g_lgi_stream, angle_distribution_param2_variable_name,
           specialized_parameters.hollow_cone_params.angle_distribution_param2_variable_name_length);
   
   lgi_read(g_lgi_stream, outer_half_angle_limit_variable_name,
           specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length);

   lgi_read(g_lgi_stream, inner_half_angle_limit_variable_name,
           specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length);


}

//Read in strings that were only written for eliptical cone nozzle emitter configurations.
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_elliptical_cone_strings( std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                         std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                         std::string &major_cone_half_angle_distribution_param1_variable_name,
                                                                         std::string &major_cone_half_angle_distribution_param2_variable_name,
                                                                         std::string &minor_cone_half_angle_distribution_param1_variable_name,
                                                                         std::string &minor_cone_half_angle_distribution_param2_variable_name,
                                                                         std::string &major_outer_half_angle_limit_variable_name,//added for cdi/513 (10/26/16)
                                                                         std::string &minor_outer_half_angle_limit_variable_name) {

   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param1_variable_name,
           specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length);


   lgi_read(g_lgi_stream, velocity_magnitude_distribution_param2_variable_name,
           specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length);

   lgi_read(g_lgi_stream, major_cone_half_angle_distribution_param1_variable_name,
           specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length);

   lgi_read(g_lgi_stream, major_cone_half_angle_distribution_param2_variable_name,
           specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length);

   lgi_read(g_lgi_stream, minor_cone_half_angle_distribution_param1_variable_name,
           specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length);

   lgi_read(g_lgi_stream, minor_cone_half_angle_distribution_param2_variable_name,
           specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length);

   lgi_read(g_lgi_stream, minor_outer_half_angle_limit_variable_name,
           specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length);

   lgi_read(g_lgi_stream, major_outer_half_angle_limit_variable_name,
            specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length);


}

//Read in strings that were only written for rain emitter configuration nozzle data
VOID sLGI_NOZZLE_CONFIGURATION_REC_READER::read_rain_strings() {
  //There are no strings specific to a rain emitter configuration for now.
}
//Wurigen says: TMP for elliptical nozzle, after reading the names discard them
std::vector<std::string> g_major_ellipse_direction_variable_names;

// ==========Particle emitters=============

//---------LGI Surface Emitters:
//Method to read a surface emitter lgi record from the lgi stream along with the required strings.
VOID sLGI_SURFACE_EMITTER_REC_READER::read(std::string &name, std::vector<int> &face_list, std::vector<std::string> &spray_direction_variable_names) {
  lgi_read(g_lgi_stream, this, sizeof(sLGI_SURFACE_EMITTER_REC));
  lgi_read(g_lgi_stream, name, name_length);
  face_list.resize(n_faces);
  lgi_read(g_lgi_stream, (VOID*)face_list.data(), sizeof(int) * n_faces);
  spray_direction_variable_names.resize(3);
  ccDOTIMES(axis,3) {
    lgi_read(g_lgi_stream, spray_direction_variable_names[axis], spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
  {
    g_major_ellipse_direction_variable_names.resize(3);
    ccDOTIMES(axis,3) {
      lgi_read(g_lgi_stream, g_major_ellipse_direction_variable_names[axis], major_ellipse_direction_name_lengths[axis]);
    }
  }
}


//------- LGI volume emitters:
VOID sLGI_VOLUME_EMITTER_REC_READER::read(std::string &name,
                                   std::vector<std::string> &spray_direction_variable_names,
                                   std::vector<sLGI_CYLINDER_REC> &cylinders,
                                   std::vector<std::string> &cylinder_names,
                                   std::vector<sLGI_BOX_REC> &boxes,
                                   std::vector<std::string> &box_names) {

  lgi_read(g_lgi_stream, this, sizeof(sLGI_VOLUME_EMITTER_REC));
  lgi_read(g_lgi_stream, name, name_length);

  spray_direction_variable_names.resize(3);
  ccDOTIMES(axis,3) {
    lgi_read(g_lgi_stream, spray_direction_variable_names[axis], spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
  {
    g_major_ellipse_direction_variable_names.resize(3);
    ccDOTIMES(axis,3) {
      lgi_read(g_lgi_stream, g_major_ellipse_direction_variable_names[axis], major_ellipse_direction_name_lengths[axis]);
    }
  }

  cylinder_names.resize(num_cylinders);
  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_READER cylinder;
    cylinder.read(cylinder_names[cylinder_index]);
    cylinders.push_back(cylinder);
  }

  box_names.resize(num_boxes);
  ccDOTIMES(box_index, num_boxes) {
    sLGI_BOX_REC_READER box;
    box.read(box_names[box_index]);
    boxes.push_back(box);
  }

}


// ----------LGI Point emitters:

VOID sLGI_POINT_EMITTER_REC_READER::read(std::string &name,
                                         std::vector<std::string> &spray_direction_variable_names,
                                         std::vector<dFLOAT> &points) 
{
  lgi_read(g_lgi_stream, this, sizeof(sLGI_POINT_EMITTER_REC));
  lgi_read(g_lgi_stream, name, name_length);
  spray_direction_variable_names.resize(3);
  ccDOTIMES(axis,3) {
    lgi_read(g_lgi_stream, spray_direction_variable_names[axis], spray_direction_variable_name_lengths[axis]);
  }

  if(elliptical_nozzle)
  {
    g_major_ellipse_direction_variable_names.resize(3);
    ccDOTIMES(axis,3) {
      lgi_read(g_lgi_stream, g_major_ellipse_direction_variable_names[axis], major_ellipse_direction_name_lengths[axis]);
    }
  }

  points.resize( num_points * 3 );
  lgi_read(g_lgi_stream, points.data(), sizeof(dFLOAT) * num_points * 3);
}


// -----------LGI Tire emitters:



VOID sLGI_TIRE_EMITTER_REC_READER::read(std::string &name,
                                 std::vector<std::string> &cylinder_names,
                                 std::vector<sLGI_CYLINDER_REC> &cylinders) {
  lgi_read(g_lgi_stream, this, sizeof(sLGI_TIRE_EMITTER_REC));
  lgi_read(g_lgi_stream, name, name_length);
  cylinder_names.resize(num_cylinders);
  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_READER cylinder;
    cylinder.read(cylinder_names[cylinder_index]);
    cylinders.push_back(cylinder);
  }
}

// -------------LGI Rain emitters:



VOID sLGI_RAIN_EMITTER_REC_READER::read(std::string &name,
                                 std::vector<sLGI_CYLINDER_REC> &cylinders,
                                 std::vector<std::string> &cylinder_names,
                                 std::vector<sLGI_BOX_REC> &boxes,
                                 std::vector<std::string> &box_names) {

  lgi_read(g_lgi_stream, this, sizeof(sLGI_RAIN_EMITTER_REC));
  lgi_read(g_lgi_stream, name, name_length);

  cylinder_names.resize(num_cylinders);
  ccDOTIMES(cylinder_index, num_cylinders) {
    sLGI_CYLINDER_REC_READER cylinder;
    cylinder.read(cylinder_names[cylinder_index]);
    cylinders.push_back(cylinder);
  }

  box_names.resize(num_boxes);
  ccDOTIMES(box_index, num_boxes) {
    sLGI_BOX_REC_READER box;
    box.read(box_names[box_index]);
    boxes.push_back(box);
  }
}



VOID sLGI_CYLINDER_REC_READER::read(std::string &name) {
  lgi_read(g_lgi_stream, this, sizeof(sLGI_CYLINDER_REC));
  lgi_read(g_lgi_stream, name, name_length);
}


VOID sLGI_BOX_REC_READER::read(std::string &name) {
  lgi_read(g_lgi_stream, this, sizeof(sLGI_BOX_REC));
  lgi_read(g_lgi_stream, name, name_length);
}
