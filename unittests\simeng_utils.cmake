
#===============================================================
# Find the current registry
#===============================================================
set(EXA_REGISTRY "" CACHE STRING "The current registry to build agaisnt")

set(EXA_REGISTRY $ENV{R})
if(EXA_REGISTRY)
  message(STATUS "VCC registry found ${EXA_REGISTRY}")
else(EXA_REGISTRY)
  message(FATAL_ERROR "NO VCC registry found! Set the current registry to build agianst using srdist")
endif(EXA_REGISTRY)

set(EXA_REGISTRY_VCCSET "${EXA_REGISTRY}/vccset")
if(NOT IS_DIRECTORY "${EXA_REGISTRY_VCCSET}")
  message(FATAL_ERROR "Could not find vccset/ directory!")
endif()

#=======================================================
# Setup the externally generated object files
#=======================================================

set(SIMENG_SRC_DIR "${CMAKE_SOURCE_DIR}/..")

set(SIMENG_SRC_FILES 	
"${SIMENG_SRC_DIR}/sim.cc"
"${SIMENG_SRC_DIR}/common_sp.cc"
"${SIMENG_SRC_DIR}/lattice.cc"
"${SIMENG_SRC_DIR}/seed_sp.cc"
"${SIMENG_SRC_DIR}/mirror.cc"
"${SIMENG_SRC_DIR}/voxel_dyn_sp.cc"
"${SIMENG_SRC_DIR}/fset.cc"
"${SIMENG_SRC_DIR}/shob.cc"
"${SIMENG_SRC_DIR}/ublk_neighbors.cc"
"${SIMENG_SRC_DIR}/group.cc"
"${SIMENG_SRC_DIR}/random.cc"
"${SIMENG_SRC_DIR}/surfel_dyn_sp.cc"
"${SIMENG_SRC_DIR}/bsurfel.cc"
"${SIMENG_SRC_DIR}/bsurfel_comm.cc"
"${SIMENG_SRC_DIR}/bsurfel_dyn_meas.cc"
"${SIMENG_SRC_DIR}/bsurfel_dyn_sp.cc"
"${SIMENG_SRC_DIR}/bsurfel_strands.cc"
"${SIMENG_SRC_DIR}/bsurfel_table.cc"
"${SIMENG_SRC_DIR}/bsurfel_util.cc"
"${SIMENG_SRC_DIR}/vr.cc"
"${SIMENG_SRC_DIR}/surfel_advect_sp.cc"
"${SIMENG_SRC_DIR}/ckpt.cc"
"${SIMENG_SRC_DIR}/sp_timers.cc"
"${SIMENG_SRC_DIR}/boltz_diags.cc"
"${SIMENG_SRC_DIR}/async_events.cc"
"${SIMENG_SRC_DIR}/event_queue.cc"
"${SIMENG_SRC_DIR}/eqns.cc"
"${SIMENG_SRC_DIR}/fan.cc"
"${SIMENG_SRC_DIR}/slrf.cc"
"${SIMENG_SRC_DIR}/mlrf.cc"
"${SIMENG_SRC_DIR}/mlrf_comm.cc"
"${SIMENG_SRC_DIR}/vectorization_support.cc"
"${SIMENG_SRC_DIR}/simerr.cc"
"${SIMENG_SRC_DIR}/phys_type_map.cc"
"${SIMENG_SRC_DIR}/dgf_reader_sp.cc"
"${SIMENG_SRC_DIR}/shob_groups.cc"
"${SIMENG_SRC_DIR}/ublk_table.cc"
"${SIMENG_SRC_DIR}/surfel_table.cc"
"${SIMENG_SRC_DIR}/parse_shob_descs.cc"
"${SIMENG_SRC_DIR}/meas.cc"
"${SIMENG_SRC_DIR}/comm_compression.cc"
"${SIMENG_SRC_DIR}/sampling_surfel.cc"
"${SIMENG_SRC_DIR}/lb_solver_data.cc"
"${SIMENG_SRC_DIR}/t_solver_data.cc"
"${SIMENG_SRC_DIR}/turb_solver_data.cc"
"${SIMENG_SRC_DIR}/isurfel_dyn_sp.cc"
"${SIMENG_SRC_DIR}/box_advect.cc"
"${SIMENG_SRC_DIR}/swap_advect.cc"
"${SIMENG_SRC_DIR}/surfel.cc"
"${SIMENG_SRC_DIR}/ublk.cc"
"${SIMENG_SRC_DIR}/advect.cc"
"${SIMENG_SRC_DIR}/scalar_data.cc"
"${SIMENG_SRC_DIR}/mc_data_5g.cc"
"${SIMENG_SRC_DIR}/particle_solver_data.cc"
"${SIMENG_SRC_DIR}/film_solver_stencils.cc"
"${SIMENG_SRC_DIR}/particle_emitter_configurations.cc"
"${SIMENG_SRC_DIR}/particle_emitters.cc"
"${SIMENG_SRC_DIR}/particle_materials.cc"
"${SIMENG_SRC_DIR}/particle_emitter_geometry.cc"
"${SIMENG_SRC_DIR}/virtual_wipers.cc"
"${SIMENG_SRC_DIR}/particle_sim.cc"
"${SIMENG_SRC_DIR}/particle_sim_info.cc"
"${SIMENG_SRC_DIR}/trajectory_meas.cc"
"${SIMENG_SRC_DIR}/trajectory_window.cc"
"${SIMENG_SRC_DIR}/eos.cc"
"${SIMENG_SRC_DIR}/gradient_5g.cc"
"${SIMENG_SRC_DIR}/turb_synth_info.cc"
"${SIMENG_SRC_DIR}/read_pm_lgi_records.cc"
"${SIMENG_SRC_DIR}/particle_random_properties.cc"
"${SIMENG_SRC_DIR}/timescale.cc"
"${SIMENG_SRC_DIR}/status.cc"
"${SIMENG_SRC_DIR}/sleep.cc"
"${SIMENG_SRC_DIR}/run_threads.cc"
"${SIMENG_SRC_DIR}/sync_threads.cc"
"${SIMENG_SRC_DIR}/comm_utils.cc"
"${SIMENG_SRC_DIR}/comm_thread.cc"
"${SIMENG_SRC_DIR}/comm_groups.cc"
"${SIMENG_SRC_DIR}/thread_meas.cc"
"${SIMENG_SRC_DIR}/strand_mgr.cc"
"${SIMENG_SRC_DIR}/far_shobs_strands.cc"
"${SIMENG_SRC_DIR}/time_update_strand.cc"
"${SIMENG_SRC_DIR}/surfel_process_control.cc"
"${SIMENG_SRC_DIR}/ublk_process_control.cc"
"${SIMENG_SRC_DIR}/sliding_mesh_strands.cc"
"${SIMENG_SRC_DIR}/near_shobs_strands.cc"
"${SIMENG_SRC_DIR}/logging.cc"
"${SIMENG_SRC_DIR}/box_advect_split.cc"
)

#===============================================================
# Setup helpful macros
#===============================================================

macro(set_component_dir_using_vccset _component)
  message("Attempting to resolve component ${_component}")
  string(TOLOWER ${_component} _lowercase)
  set(${_component}_D "${EXA_REGISTRY_VCCSET}/${_lowercase}")
endmacro()

macro(make_vcc_build_defines _component _header _header_directory_prefix)
  set_component_dir_using_vccset(${_component})
  set(${_component}_HEADER_DEFINE ${_component}_H="${${_component}_D}${_header_directory_prefix}${_header}")
  set(SIMENG_PHYSICS_DEFINES ${SIMENG_PHYSICS_DEFINES} ${${_component}_HEADER_DEFINE} ) 
endmacro()

macro(make_vcc_from_existing_dir _name _new_name _header)
  set(${_new_name}_HEADER_DEFINE ${_new_name}_H="${${_name}_D}/${_header}")
  set(SIMENG_PHYSICS_DEFINES ${SIMENG_PHYSICS_DEFINES} ${${_new_name}_HEADER_DEFINE} ) 
endmacro()

# # SP_VERSION
# execute_process(COMMAND vwhich simeng --name
#   OUTPUT_VARIABLE SP_VERSION
#   ERROR_VARIABLE  SP_VERSION_ERROR
#   OUTPUT_STRIP_TRAILING_WHITESPACE)
# set(SP_VERSION_VAR SP_VERSION="${SP_VERSION}")
#
# # PHYSICS VERSION
# execute_process(COMMAND vwhich physics --name
#   OUTPUT_VARIABLE PHYSICS_VERSION
#   ERROR_VARIABLE  PHYSICS_VERSION_ERROR
#   OUTPUT_STRIP_TRAILING_WHITESPACE)
# set(PHYSICS_VERSION_VAR PHYSICS_VERSION="${PHYSICS_VERSION}")
#
# # PRODUCT VERSION
# execute_process(COMMAND vwhich product --name
#   OUTPUT_VARIABLE PRODUCT_VERSION
#   ERROR_VARIABLE  PRODUCT_VERSION_ERROR
#   OUTPUT_STRIP_TRAILING_WHITESPACE)
# set(PRODUCT_VERSION_VAR  PRODUCT_VERSION="${PRODUCT_VERSION}")
#
# # dependencies:
# # simeng depends on libs and headers
# # physics depends on headers only 
# # headers overlap for both simeng physics
#

macro(vcc_includes)
  make_vcc_build_defines(SRI export.h /)
  make_vcc_build_defines(PLATFORM platform.h /)
  make_vcc_build_defines(SCALAR scalar.h /)
  make_vcc_build_defines(LOOP loop.h /)
  make_vcc_build_defines(MSGERR msgerr.h /)
  make_vcc_build_defines(DEBUG debug.h /)
  make_vcc_build_defines(MALLOC malloc.h /)
  make_vcc_build_defines(VHASH vhash.h /)
  make_vcc_build_defines(THASH thash.h /)

  make_vcc_build_defines(JOBCTL jobctl_server.h /)
  # don't use above use these:
  make_vcc_from_existing_dir(JOBCTL JOBCTL_SERVER jobctl_server.h)
  make_vcc_from_existing_dir(JOBCTL JOBCTL_SERVER_CPP jobctl_server_cpp.h)

  make_vcc_build_defines(JOBCTL_PATHLOCKS pathlocks.h /)
  make_vcc_build_defines(EXATIME exatime.h /)
  make_vcc_build_defines(EXALIC exalic.h /)
  make_vcc_build_defines(SORT sort.h /)
  make_vcc_build_defines(XARRAY xarray3.h /)
  make_vcc_build_defines(EARRAY earray.h /)
  make_vcc_build_defines(TEARRAY tearray.h /)
  make_vcc_build_defines(VMEM vmem.h /)
  make_vcc_build_defines(XNEW xnew.h /)
  make_vcc_build_defines(PAGED_BITMAP paged_bitmap.h /)
  make_vcc_build_defines(FFTW fftw_export.h /)
  make_vcc_build_defines(XRAND xrand.h /)

  make_vcc_build_defines(TPI tpi_common.h /)
  make_vcc_from_existing_dir(TPI TPI_COMMON tpi_common.h)

  # special
  make_vcc_build_defines(TURB_SYNTH turb_synth.h /)
  make_vcc_from_existing_dir(TURB_SYNTH TURB_SYNTH_SIM turb_synth_sim.h)

  make_vcc_build_defines(GRID grid.h /)
  make_vcc_build_defines(INTERP quadNodeContributions.h /)
  make_vcc_from_existing_dir(INTERP QUADNODECONTRIBUTIONS quadNodeContributions.h)

  make_vcc_build_defines(EXPRLANG exprlang.h /)
  make_vcc_build_defines(UNITS units.h /)
  make_vcc_build_defines(FANTABLE cFANTABLE.h /)
  make_vcc_build_defines(G3 g3.h /)
  make_vcc_build_defines(G2 g2.h /)
  make_vcc_build_defines(G1 g1.h /)
  make_vcc_build_defines(BG kernel.h /)
  make_vcc_build_defines(PHYSTYPES export.h /)
  make_vcc_build_defines(CIO cio.h /)
  make_vcc_build_defines(HDF5 hdf5.h /amd64_linux/include/)
  make_vcc_build_defines(CDI cdi_export.h /)
  make_vcc_build_defines(PRI pri.h /)
  make_vcc_build_defines(LGI lgi.h /)
  make_vcc_build_defines(LGI_INTERFACE lgi_interface.h /)
  make_vcc_build_defines(SIMENG simeng.h /)
  make_vcc_build_defines(PHYSICS export.h /)
  #make_vcc_build_defines(SIMENG_PARTICLES particle_sim_info.h /)
  make_vcc_from_existing_dir(SIMENG SIMENG_PARTICLES particle_sim_info.h)
  make_vcc_build_defines(EXATIME exatime.h /)

  # special
  make_vcc_build_defines(CP_SP_LIB export_sp.h /)
  make_vcc_from_existing_dir(CP_SP_LIB SP export_sp.h)

  make_vcc_build_defines(VMEM_VECTOR vmem_vector.h /)
  make_vcc_build_defines(VMEM vmem.h /)
  make_vcc_build_defines(XRAND xrand.h /)
  make_vcc_build_defines(PDFS pdfs.h /)
  make_vcc_build_defines(SIMUTILS simutils.h /)
  make_vcc_build_defines(DISC_SIM_COMM_LIB simutils.h /)

  set_component_dir_using_vccset(DISC_SIM_COMM_LIB)
  make_vcc_from_existing_dir(DISC_SIM_COMM_LIB DISC_SIM_LOG disc_sim_log.h)
  make_vcc_from_existing_dir(DISC_SIM_COMM_LIB SIM_INTERCOMM export_sim_intercomm.h)
  make_vcc_from_existing_dir(DISC_SIM_COMM_LIB DISC_INTERCOMM export_disc_intercomm.h)

  make_vcc_build_defines(FMT export.h /)


endmacro()

macro(simulator_libs)
  find_library(LGI_LIB
    NAMES lgi
    PATHS ${LGI_D}/amd64_linux/
    REQUIRED
    )
  find_library(CDI_LIB
    NAMES cdi
    PATHS ${CDI_D}/amd64_linux/
    REQUIRED
    )
  find_library(CDI_LIB
    NAMES cdi
    PATHS ${CDI_D}/amd64_linux/
    REQUIRED
    )
  find_library(EXPRLANG_LIB
    NAMES exprlang
    PATHS ${EXPRLANG_D}/amd64_linux/
    REQUIRED
    )
  find_library(UNITS_LIB
    NAMES units
    PATHS ${UNITS_D}/amd64_linux/
    REQUIRED
    )

  # not defined
  set_component_dir_using_vccset(ESTRING)
  find_library(ESTRING_LIB
    NAMES estring
    PATHS ${ESTRING_D}/amd64_linux/
    REQUIRED
    )
  find_library(VHASH_LIB
    NAMES vhash
    PATHS ${VHASH_D}/amd64_linux/
    REQUIRED
    )
  find_library(THASH_LIB
    NAMES thash
    PATHS ${THASH_D}/amd64_linux/
    REQUIRED
    )
  find_library(JOBCTL_SERVER_LIB
    NAMES jobctl_server
    PATHS ${JOBCTL_D}/amd64_linux/
    REQUIRED
    )
  set_component_dir_using_vccset(CCUTILS)
  find_library(CCUTILS_LIB
    NAMES ccutils
    PATHS ${CCUTILS_D}/amd64_linux/
    REQUIRED
    )
  find_library(EXATIME_LIB
    NAMES exatime
    PATHS ${EXATIME_D}/amd64_linux/
    REQUIRED
    )
  find_library(VMEM_LIB
    NAMES vmem
    PATHS ${VMEM_D}/amd64_linux/
    REQUIRED
    )
  find_library(TURB_SYNTH_LIB
    NAMES turb_synth
    PATHS ${TURB_SYNTH_D}/amd64_linux/
    REQUIRED
    )
  #find_vcc_component_dir(FFTW)
  find_library(FFTW_LIB
    NAMES fftw3
    PATHS ${FFTW_D}/amd64_linux/
    REQUIRED
    )
  find_library(XRAND_LIB
    NAMES xrand
    PATHS ${XRAND_D}/amd64_linux/
    REQUIRED
    )
  find_library(FANTABLE_LIB
    NAMES fantable
    PATHS ${FANTABLE_D}/amd64_linux/
    REQUIRED
    )
  find_library(BG_LIB
    NAMES bg
    PATHS ${BG_D}/amd64_linux/
    REQUIRED
    )
  find_library(G3_LIB
    NAMES g3
    PATHS ${G3_D}/amd64_linux/
    REQUIRED
    )
  find_library(G2_LIB
    NAMES g2
    PATHS ${G2_D}/amd64_linux/
    REQUIRED
    )
  find_library(G1_LIB
    NAMES g1
    PATHS ${G1_D}/amd64_linux/
    REQUIRED
    )
  find_library(XARRAY_LIB
    NAMES xarray
    PATHS ${XARRAY_D}/amd64_linux/
    REQUIRED
    )
  find_library(EARRAY_LIB
    NAMES earray
    PATHS ${EARRAY_D}/amd64_linux/
    REQUIRED
    )
  find_library(SORT_LIB
    NAMES sort
    PATHS ${SORT_D}/amd64_linux/
    REQUIRED
    )
  #find_library(EXALIC_LIB
  #  NAMES exalic
  #  PATHS ${EXALIC_D}/amd64_linux/
  #  )
  find_library(PLATFORM_LIB
    NAMES platform
    PATHS ${PLATFORM_D}/amd64_linux/
    REQUIRED
    )
  find_library(MALLOC_LIB
    NAMES malloc
    PATHS ${MALLOC_D}/amd64_linux/
    REQUIRED
    )
  find_library(INTERP_LIB
    NAMES interp
    PATHS ${INTERP_D}/amd64_linux/
    REQUIRED
    )
  find_library(MSGERR_LIB
    NAMES msgerr
    PATHS ${MSGERR_D}/amd64_linux/
    REQUIRED
    )
  find_library(XNEW_LIB
    NAMES xnew
    PATHS ${XNEW_D}/amd64_linux/
    REQUIRED
    )
  find_library(PAGED_BITMAP_LIB
    NAMES paged_bitmap
    PATHS ${PAGED_BITMAP_D}/amd64_linux/
    REQUIRED
    )
  find_library(XRAND_LIB
    NAMES xrand
    PATHS ${XRAND_D}/amd64_linux/
    REQUIRED
    )
  find_library(PDFS_LIB
    NAMES pdfs
    PATHS ${PDFS_D}/amd64_linux/
    REQUIRED
    )
  find_library(SIMUTILS_LIB
    NAMES simutils
    PATHS ${SIMUTILS_D}/amd64_linux/
    REQUIRED
    )
  find_library(EXA_DEBUG_LIB
    NAMES debug
    PATHS ${DEBUG_D}/amd64_linux
    REQUIRED
    )

  # This is a special case CP_SP_LIB has some dependencies
  set(CP_SP_LIB_SUFFIX "")
  if(SIMENG_DP)
    set(CP_SP_LIB_SUFFIX "${CP_SP_LIB_SUFFIX}_dp")
  endif()
  if(SIMENG_AVX)
    set(CP_SP_LIB_SUFFIX "${CP_SP_LIB_SUFFIX}_avx")
  endif()

  find_library(SP_LIB
    NAMES sp
    PATHS ${CP_SP_LIB_D}/amd64_linux_hpmpi${CP_SP_LIB_SUFFIX}
    REQUIRED
    )

  find_library(PHYSICS_LIB
    NAMES physics physics_d19 physics_5g
    PATHS ${PHYSICS_D}/amd64_linux_hpmpi${EXA_TARGET_DIR_SUFFIX}
    REQUIRED
    )
  # find_library(SIMENG_LIB
  #   NAMES simeng simeng_d19 simeng_5g
  #   PATHS ${SIMENG_D}/amd64_linux_hpmpi${EXA_TARGET_DIR_SUFFIX}
  #   )

  set(SIMULATOR_LIB_LIST
    ${SIMENG_LIB}
    ${PHYSICS_LIB}
    ${LGI_LIB}
    ${CDI_LIB}
    ${EXPRLANG_LIB}
    ${UNITS_LIB}
    ${INTERP_LIB}
    ${ESTRING_LIB}
    ${VHASH_LIB}
    ${THASH_LIB}
    ${JOBCTL_SERVER_LIB}
    ${CCUTILS_LIB}
    ${EXATIME_LIB}
    ${VMEM_LIB}
    ${TURB_SYNTH_LIB}
    ${FFTW_LIB}
    ${XRAND_LIB}
    ${FANTABLE_LIB}
    ${BG_LIB}
    ${G3_LIB}
    ${G2_LIB}
    ${G1_LIB}
    ${XARRAY_LIB}
    ${EARRAY_LIB}
    ${SORT_LIB}
    ${EXALIC_LIB}
    ${PLATFORM_LIB}
    ${MALLOC_LIB}
    ${MSGERR_LIB}
    ${XNEW_LIB}
    ${PAGED_BITMAP_LIB}
    ${XRAND_LIB}
    ${PDFS_LIB}
    ${SIMUTILS_LIB}
    ${SP_LIB}
    ${EXA_DEBUG_LIB}
    ${PLATFORM_LIB}
    )

endmacro()

