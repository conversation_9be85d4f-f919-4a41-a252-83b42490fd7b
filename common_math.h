/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Created December 1, 2023
 *--------------------------------------------------------------------------*/

#pragma once
#include <Eigen/Dense>

template <size_t SIZE>
inline std::pair<BOOLEAN, dFLOAT> invert_matrix(dFLOAT (&matrix)[SIZE][SIZE], dFLOAT (&new_matrix)[SIZE][SIZE])
{

  // Convert matrix to Eigen matrix using Eigen::Map
  Eigen::Map<Eigen::Matrix<double, SIZE, SIZE, Eigen::RowMajor> > mat(*matrix);

  // Use a rank-revealing matrix decomposition to get the rank and determine if
  // it's invertible
  Eigen::CompleteOrthogonalDecomposition<Eigen::Matrix<double, SIZE, SIZE, Eigen::RowMajor> > cod(mat);
  // Maybe better to return the rank, but for now just stick to the determinant
  // since that's what people are using right now
  //int matrix_rank = cod.rank();
  dFLOAT determinant = mat.determinant();
  BOOLEAN is_invertible = cod.isInvertible();

  if (!is_invertible) {
    return std::make_pair(is_invertible, determinant); // I prefer to return the rank instead of the determinant
  }

  // Use Eigen to compute the inverse if it's full rank
  Eigen::Map<Eigen::Matrix<double, SIZE, SIZE, Eigen::RowMajor> > nmat(*new_matrix); // Using map to have eigen matrix share memory with new_matrix
  nmat = mat.inverse();

  return std::make_pair(is_invertible, determinant);
}

template<size_t SIZE>
inline std::pair<BOOLEAN, dFLOAT> invert_full_matrix(dFLOAT (&matrix)[SIZE][SIZE], dFLOAT (&matrix_inv)[SIZE][SIZE])
{
  std::pair<BOOLEAN, dFLOAT> is_invertible_and_determinant = invert_matrix<SIZE>(matrix, matrix_inv);
  return is_invertible_and_determinant;
}

template<size_t N, size_t SIZE>
inline std::pair<BOOLEAN, dFLOAT> invert_partial_matrix(dFLOAT (&matrix)[N][N], dFLOAT (&matrix_inv)[N][N])
{
  assert(SIZE <= N);

  memset(matrix_inv, 0, sizeof(matrix_inv));

  dFLOAT partial_matrix[SIZE][SIZE];
  dFLOAT partial_matrix_inv[SIZE][SIZE];

  memset(partial_matrix_inv, 0, sizeof(partial_matrix_inv));

  // Copy contents into partial_matrix
  for (asINT32 i = 0; i < SIZE; i++) {
    for (asINT32 j = 0; j < SIZE; j++) {
      partial_matrix[i][j] = matrix[i][j];
    }
  }

  std::pair<BOOLEAN, dFLOAT> is_invertible_and_determinant = invert_matrix<SIZE>(partial_matrix, partial_matrix_inv);

  if (is_invertible_and_determinant.first) {
    // Copy contents of partial_matrix_inv back into matrix_inv
    for (asINT32 i = 0; i < SIZE; i++) {
      for (asINT32 j = 0; j < SIZE; j++) {
        matrix_inv[i][j] = partial_matrix_inv[i][j];
      }
    }

  }
  return is_invertible_and_determinant;
}
