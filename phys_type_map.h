/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef _SIMENG_PHYS_TYPE_MAP_H_
#define _SIMENG_PHYS_TYPE_MAP_H_

#include <map>
#include "eqns.h"
#include "sim.h"

STP_PHYSTYPE_TYPE  voxel_sim_type_from_cdi_type(PHYSICS_DESCRIPTOR fluid_phys_desc, BOOLEAN is_near_surf);
STP_PHYSTYPE_TYPE  voxel_sim_type_from_cdi_type(asINT32 cdi_phys_type, BOOLEAN is_near_surf);
STP_PHYSTYPE_TYPE  surfel_sim_type_from_cdi_type(PHYSICS_DESCRIPTOR surface_phys_desc);
BOOLEAN            is_cdi_physics_type_basic_fluid(asINT32 cdi_physics_type);

extern sINT32 *g_cdi_volume_physics_desc_index_from_part_index;
extern dFLOAT *g_flow_bc_total_areas;
extern dFLOAT *g_part_total_volumes;
extern sINT32 g_num_parts; // parts in CDI plus those generated internal to discretizer

inline PHYSICS_DESCRIPTOR volume_physics_desc_from_part_index(asINT32 part_index)
{ 
  if (part_index >= g_num_parts) {
    msg_internal_error("Part index exceeds maximum.");
  }

  asINT32 phys_desc_index = g_cdi_volume_physics_desc_index_from_part_index[part_index];

  if (phys_desc_index < 0)
    return NULL; // likely a solid region

  PHYSICS_DESCRIPTOR pd = &sim.volume_physics_descs[phys_desc_index];
  return pd;
}

extern BODY_FORCE_PHYSICS_DESCRIPTOR *g_cdi_body_force_desc_from_part_index;

inline BODY_FORCE_PHYSICS_DESCRIPTOR body_force_desc_from_part_index(asINT32 part_index)
{
  if (g_cdi_body_force_desc_from_part_index == NULL) {
    return NULL;
  }

  if (part_index >= g_num_parts) {
    msg_internal_error("Part index exceeds maximum.");
  }
  
  return g_cdi_body_force_desc_from_part_index[part_index];
}

extern sINT32 g_num_faces;
extern sINT32 *g_face_index_to_part_index;
extern sINT32 *g_face_index_to_opposite_face_index;
extern sINT8  *g_face_index_is_front_only;
extern sINT32 *g_cdi_front_flow_surface_physics_desc_index_from_face_index;
extern sINT32 *g_cdi_front_thermal_surface_physics_desc_index_from_face_index;
extern sINT32 *g_cdi_back_flow_surface_physics_desc_index_from_face_index;
extern sINT32 *g_cdi_back_thermal_surface_physics_desc_index_from_face_index;

asINT32 find_merged_wall_and_thermal_surface_phys_desc(asINT32 wall_surface_phys_desc_index, asINT32 thermal_surface_phys_desc_index);

asINT32 register_thermal_wall_surface_phys_desc(PHYSICS_DESCRIPTOR wall_surface_phys_desc, 
                                                PHYSICS_DESCRIPTOR thermal_surface_phys_desc,
                                                asINT32 new_wall_cdi_phys_type,
                                                asINT32 n_new_parameters,
                                                PHYSICS_VARIABLE new_parameters,
                                                std::vector<sPHYSICS_DESCRIPTOR> &flow_surface_phys_descs);

asINT32 find_phys_desc_with_swapped_in_out_heat_fluxes(asINT32 contact_heat_flux_phys_desc_index);

asINT32 register_phys_desc_with_swapped_in_out_heat_fluxes(PHYSICS_DESCRIPTOR heat_flux_phys_desc, // type CDI_PHYS_TYPE_FIXED_HEAT_FLUX
                                                           std::vector<sPHYSICS_DESCRIPTOR> &thermal_surface_phys_descs);


inline BOOLEAN is_front_side_from_face_index(asINT32 face_index) {
  //Returns true if face is FrontOnly or FrontAndBack
  return g_face_index_is_front_only[face_index] || (g_face_index_to_opposite_face_index[face_index] == face_index);
}

inline BOOLEAN is_back_side_only_from_face_index(asINT32 face_index) {
  //Returns true only if face is BackOnly, so the orientation of the face is explicityly defined when requested based on
  //the face index. Either is front (FrontOnly or FrontAndBack) or is back (BackOnly).
  return !g_face_index_is_front_only[face_index] && (g_face_index_to_opposite_face_index[face_index] != face_index);
}

inline sINT32 flow_phys_desc_index_from_face_index(asINT32 face_index, bool is_backside) {
  return ((is_backside) 
          ? g_cdi_back_flow_surface_physics_desc_index_from_face_index[face_index]
          : g_cdi_front_flow_surface_physics_desc_index_from_face_index[face_index]);
}

inline sINT32 thermal_phys_desc_index_from_face_index(asINT32 face_index, bool is_backside) {
  return ((is_backside) 
          ? g_cdi_back_thermal_surface_physics_desc_index_from_face_index[face_index]
          : g_cdi_front_thermal_surface_physics_desc_index_from_face_index[face_index]);
}

inline PHYSICS_DESCRIPTOR surface_physics_desc_from_face_index(asINT32 face_index,
                                                               bool is_backside,
                                                               bool is_thermal_phys_desc,
                                                               bool surfel_interacts_with_conduction_voxels = false)
{ 
  if (face_index >= g_num_faces) {
    msg_internal_error("Face index exceeds maximum.");
  }

  PHYSICS_DESCRIPTOR pd = NULL;
  if (is_thermal_phys_desc) {
    asINT32 thermal_phys_desc_index = thermal_phys_desc_index_from_face_index(face_index, is_backside);
    if (thermal_phys_desc_index < 0)
      return NULL;

    if (sim.n_thermal_surface_physics_descs > 0) {
      cassert(thermal_phys_desc_index < sim.n_thermal_surface_physics_descs);
    }

    // For old (pre 9.5) CDI files, some of the physics descs in the CDI srpt chunk do not map to solid surface physics descs
    if (sim.n_thermal_surface_physics_descs > 0 && sim.thermal_surface_physics_descs[thermal_phys_desc_index].phys_type_desc) {
      pd = &sim.thermal_surface_physics_descs[thermal_phys_desc_index];

      if (pd->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX
          && surfel_interacts_with_conduction_voxels
          && (sim.cdi_major_version > 9 || (sim.cdi_major_version == 9 && sim.cdi_minor_version >= 5))) {
        // If a thermal insulator part with a heat flux BC overlaps a thermal conductor part, the resulting conduction
        // surfels will talk to the voxels of the conductor, but be associated with the insulator. The Heat Flux Out
        // variable of the heat flux BC must be used for the surfels (regular conduction surfels use the Heat Flux In
        // variable).
        asINT32 part_index = g_face_index_to_part_index[face_index];
        PHYSICS_DESCRIPTOR part_physics_descriptor = volume_physics_desc_from_part_index(part_index);
        ////////// if (part_physics_descriptor->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_SOLID_INSULATOR) {
        if (part_physics_descriptor == NULL) { // NULL ==> CDI_PHYS_TYPE_SOLID_INSULATOR part (such physics descriptors are stripped out by the CP)
          asINT32 swapped_heat_flux_phys_desc_index = find_phys_desc_with_swapped_in_out_heat_fluxes(thermal_phys_desc_index);
          cassert(swapped_heat_flux_phys_desc_index >= 0);
          pd = &sim.thermal_surface_physics_descs[swapped_heat_flux_phys_desc_index];
        }
      }
    }
  } else {
    asINT32 flow_phys_desc_index = flow_phys_desc_index_from_face_index(face_index, is_backside);
    if (flow_phys_desc_index < 0)
      return NULL;

    if (sim.n_flow_surface_physics_descs > 0) {
      asINT32 thermal_phys_desc_index = thermal_phys_desc_index_from_face_index(face_index, is_backside);
      if (sim.is_heat_transfer
          && (thermal_phys_desc_index >= 0)   // -1 for inlet or outlet
          && (sim.cdi_major_version > 9 || (sim.cdi_major_version == 9 && sim.cdi_minor_version >= 5))) {
        asINT32 merged_phys_desc_index = find_merged_wall_and_thermal_surface_phys_desc(flow_phys_desc_index, thermal_phys_desc_index);
        if (merged_phys_desc_index >= 0) // -1 for COUPLED surface physics descriptors - no merging was necessary
          flow_phys_desc_index = merged_phys_desc_index;
      }
        
      pd = &sim.flow_surface_physics_descs[flow_phys_desc_index];
    }
  }
  return pd;
}

inline PHYSICS_DESCRIPTOR surface_physics_desc_from_contact_index(asINT32 phys_desc_index, bool use_heat_flux_2)
{ 
  if (phys_desc_index < 0)
    return NULL;

  cassert(phys_desc_index < sim.n_thermal_surface_physics_descs);

  PHYSICS_DESCRIPTOR pd = &sim.thermal_surface_physics_descs[phys_desc_index];

  if (use_heat_flux_2
      && pd->phys_type_desc->cdi_physics_type == CDI_PHYS_TYPE_FIXED_HEAT_FLUX) {
    asINT32 swapped_heat_flux_phys_desc_index = find_phys_desc_with_swapped_in_out_heat_fluxes(phys_desc_index);
    cassert(swapped_heat_flux_phys_desc_index >= 0);
    pd = &sim.thermal_surface_physics_descs[swapped_heat_flux_phys_desc_index];
  }

  return pd;
}

BOOLEAN            is_cdi_physics_type_fluid_like(asINT32 cdi_physics_type);
BOOLEAN            is_cdi_physics_type_apm(asINT32 cdi_physics_type);
BOOLEAN            is_cdi_physics_type_conduction_solid(asINT32 cdi_physics_type);
BOOLEAN            is_pd_fluid_like(PHYSICS_DESCRIPTOR pd);
BOOLEAN            is_pd_apm(PHYSICS_DESCRIPTOR pd);
BOOLEAN            is_pd_conduction_solid(PHYSICS_DESCRIPTOR pd);

#endif  // _SIMENG_PHYS_TYPE_MAP_H_
