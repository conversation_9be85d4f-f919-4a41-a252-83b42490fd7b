/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Utility functions shared by CP and SP
 *
 * Jim Salem, Exa Corporation 
 * Created Wed Jan 19 1994
 *--------------------------------------------------------------------------*/

#include "common_sp.h"

/*
dFLOAT g_dfloat_inverse_power_of_two[31] = { 1.0/(1<<0),  1.0/(1<<1),  1.0/(1<<2),  1.0/(1<<3),
                                             1.0/(1<<4),  1.0/(1<<5),  1.0/(1<<6),  1.0/(1<<7),
                                             1.0/(1<<8),  1.0/(1<<9),  1.0/(1<<10), 1.0/(1<<11),
                                             1.0/(1<<12), 1.0/(1<<13), 1.0/(1<<14), 1.0/(1<<15),
                                             1.0/(1<<16), 1.0/(1<<17), 1.0/(1<<18), 1.0/(1<<19), 
                                             1.0/(1<<20), 1.0/(1<<21), 1.0/(1<<22), 1.0/(1<<23), 
                                             1.0/(1<<24), 1.0/(1<<25), 1.0/(1<<26), 1.0/(1<<27), 
                                             1.0/(1<<28), 1.0/(1<<29), 1.0/(1<<30) 
};
};*/
Inverse_power_of_two_array <2 * STP_MAX_SCALES - 1> g_dfloat_inverse_power_of_two;

/*--------------------------------------------------------------------------*
 * Validation functions
 *--------------------------------------------------------------------------*/

VOID report_ckpt_restore_inconsistency(cSTRING description)
{
  msg_error("Inconsistency found while restoring from checkpoint file: %s."
	    " Perhaps the checkpoint file and LGI file are not from the same case.",
	    description);
}

/*--------------------------------------------------------------------------*
 * Vector utilities
 *--------------------------------------------------------------------------*/

VOID vector_print_msg_asINT32(asINT32 *vector, asINT32 size)
/* Prints the vector elements separated by commas using msg_cont */
{
  DOTIMES(i, size, {
    if (i == 0) msg_cont("%ld", (long) vector[i]);
      else msg_cont(", %ld", (long) vector[i]);    
  });
}

VOID vector_print_msg_cuINT8(cuINT8 *vector, asINT32 size)
/* Prints the vector elements separated by commas using msg_cont */
{
  DOTIMES(i, size, {
    if (i == 0) msg_cont("x%X", (unsigned int) vector[i]);
      else msg_cont(", x%X", (unsigned int) vector[i]);    
  });
}


//----------------------------------------------------------------------------
// round_up_time
// Round up a signed positive int to a multiple of a given integer. 
// If the result will overflow a signed int, round down instead.
//----------------------------------------------------------------------------
BASETIME round_up_time(dFLOAT t, dFLOAT ct, dFLOAT t0)
{
  BASETIME rounded_up = t0 + ct * ceil((t-t0) / ct);
  if (rounded_up < 0L || rounded_up > BASETIME_MAX) { 
    //must have overflown BASETIME (sINT64) max value or exceed upper time limit (BASETIME_MAX)
    return BASETIME_MAX;
  } 
  return rounded_up;
}

BASETIME round_down_time(dFLOAT t, dFLOAT ct, dFLOAT t0)
{
  return t0 + ct * floor((t-t0) / ct);
}

//------------------------------------------------------------------------------------------
// the least common multiple(lcm) and the greatest commom divisor(gcd) of multiple integers
//------------------------------------------------------------------------------------------

asINT32 gcd(asINT32 a, asINT32 b) 
{
  // a is the dividend and b is the divisor 
  if(a == 0 || b==0)
    return 0;
  else 
    {
      asINT32 r = a % b; //remainder
    
      while(r != 0) {
	a = b;
	b = r;
	r = a % b;
      }  
      return b;
    }
}

asINT32 gcd(asINT32 a, asINT32 b, asINT32 c) 
{
  
  asINT32 d1 = gcd(a,b);
  asINT32 d2 = gcd(b,c);
  
  return gcd(d1, d2);
}

asINT32 lcm(asINT32 a, asINT32 b)
{
  asINT32 n_gcd = gcd(a, b);  //gcd of a and b 
  if(n_gcd == 0)
    return 0;
  else
    return (a * b / n_gcd);

}
  
asINT32 lcm(asINT32 a, asINT32 b, asINT32 c) 
{
  return lcm(lcm(a,b), c);
}

asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d)
{
  return lcm(lcm(a,b), lcm(c,d));
}

asINT32 lcm(asINT32 a, asINT32 b, asINT32 c, asINT32 d, asINT32 e)
{
  return lcm(lcm(a,b), lcm(c,d), e);
}


/*--------------------------------------------------------------------------*
 * Misc. support functions
 *--------------------------------------------------------------------------*/
/* Returns the base 2 logarithm of x, rounded up.  X must be greater than 0 */
auINT32 simeng_log2(asINT32 x)
{
  auINT32 retval = 0;
  auINT32 maxval = 1;
  if (x <= 0)
    msg_internal_error("simeng_log2: Tried to take log of non-positive number.");

  while (maxval < x) {
    maxval <<= 1;
    retval += 1;
  }

  return retval;
}


#define BITCOUNT_MASK_1 0x55555555
#define BITCOUNT_MASK_2 0x33333333
#define BITCOUNT_MASK_3 0x0F0F0F0F
#define BITCOUNT_MASK_4 0x00FF00FF
#define BITCOUNT_MASK_5 0x0000FFFF
/* Returns the total count of bits in x */
auINT32 bitcount32(auINT32 x)
{
  /* Sum every group of 2 bits */
  x = (x & BITCOUNT_MASK_1) + ((x >> 1) & BITCOUNT_MASK_1);
  /* Sum every group of 4 bits */
  x = (x & BITCOUNT_MASK_2) + ((x >> 2) & BITCOUNT_MASK_2);
  /* Sum every group of 8 bits */
  x = (x & BITCOUNT_MASK_3) + ((x >> 4) & BITCOUNT_MASK_3);
  /* Sum every group of 16 bits */
  x = (x & BITCOUNT_MASK_4) + ((x >> 8) & BITCOUNT_MASK_4);
  /* Sum the remaining half words */
  x = (x & BITCOUNT_MASK_5) + (x >> 16);

  return x;
}

sCP_EARLY_SIM_RUN_INFO early_sim_run_info = { 0 };
sCP_LATE_SIM_RUN_INFO  sim_run_info = { 0 };

void matrix_transpose(double result[3][3], double src[3][3])
{
  ccDOTIMES(i, 3) {
    ccDOTIMES(j, 3) {
      result[i][j] = src[j][i];
    }
  }
}

void matrix_transpose2(double result[2][2], double src[2][2])
{
  ccDOTIMES(i, 2) {
    ccDOTIMES(j, 2) {
      result[i][j] = src[j][i];
    }
  }
}

void breakpoint() {};
