/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2022, 1993-2021 Dassault Systemes Simulia Corp.         ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Simulia Corp.                                                         ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "gpu_gather_advect.hcu"
#include "gpu_shobs.hcu"
#include "gpu_globals.hcu"
#include "shob_groups.h"
#include "split_data.h"
#include "vr_coalesce_explode.h"

namespace GPU {
namespace GATHER_ADVECT {

__constant__ uint8_t g_advect_neighbor_ublk_indices[N_UBLK_ADVECT_INTERACTIONS] = {
  0, 0, 0, 0, //l - 0
  0, 0, 0, 0, // l - 2
  0, 0, 0, 0, // l - 4
  0, 0, 0, 0, // l- 6, 8
  0, 0, 0, 0, // l- 10, 12
  0, 0, 0, 0,  // l- 14, 16
  1, 1, 1, 1, // l - 1
  1, 1, 1, 1, // l -7, 9
  1, 1, 1, 1,  // l - 10, 12
  2, 2, 2, 2, // l - 3
  2, 2, 2, 2, // l - 6, 9
  2, 2, 2, 2, // l - 14, 17
  3, 3, 3, 3, // l - 5
  3, 3, 3, 3, // l - 11, 12
  3, 3, 3, 3,  // l - 15, 17
  4, 4, //l - 7
  5, 5, // l - 9
  6, 6, // l - 11
  7, 7, // l - 13
  8, 8, // l - 15
  9, 9  // l - 17
};

__constant__ uint8_t g_advect_neighbor_ublk_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D] = {
  0, 0, //l - 0
  0, 0, // l - 2
  0, 0, // l- 6, 8
  0, 0, 0, 0, // l- 10, 12
  0, 0, 0, 0, // l- 14, 16
  1, 1, // l - 1
  1, 1, // l -7, 9
  1, 1, 1, 1,// l - 10, 12
  2, 2, // l - 3
  2, 2, // l - 6, 9
  2, 2, 2, 2,// l - 14, 17
  4, //l - 7
  5 // l - 9
};

__constant__ uint8_t g_thread_split_factor_indices[N_UBLK_ADVECT_INTERACTIONS] = {
  0, 1, 2, 3, //l - 0
  0, 1, 2, 3, // l - 2
  0, 1, 2, 3, // l - 4
  0, 1, 0, 1, // l- 6, 8
  0, 1, 0, 1, // l- 10, 12
  0, 1, 0, 1,  // l- 14, 16
  0, 1, 2, 3, // l - 1
  0, 1, 0, 1, // l -7, 9
  0, 1, 0, 1,  // l - 10, 12
  0, 1, 2, 3, // l - 3
  0, 1, 0, 1, // l - 6, 9
  0, 1, 0, 1, // l - 14, 17
  0, 1, 2, 3, // l - 5
  0, 1, 0, 1, // l - 11, 12
  0, 1, 0, 1,  // l - 15, 17
  0, 1, //l - 7
  0, 1, // l - 9
  0, 1, // l - 11
  0, 1, // l - 13
  0, 1, // l - 15
  0, 1  // l - 17
};


__constant__ uint8_t g_thread_split_factor_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D] = {
  0, 1, //l - 0
  0, 1, // l - 2
  0, 0, // l- 6, 8
  0, 1, 0, 1, // l- 10, 12
  0, 1, 0, 1, // l- 14, 16
  0, 1, // l - 1
  0, 0, // l -7, 9
  0, 1, 0, 1,// l - 10, 12
  0, 1, // l - 3
  0, 0, // l - 6, 9
  0, 1, 0, 1,// l - 14, 17
  0, //l - 7
  0 // l - 9
};

__constant__ uint8_t g_advect_src_voxels[N_UBLK_ADVECT_INTERACTIONS] = {
  0, 1, 2, 3, //l - 0
  0, 1, 4, 5, // l - 2
  0, 2, 4, 6, // l - 4
  2, 3, 0, 1, // l- 6, 8
  4, 6, 5, 7, // l- 10, 12
  2, 6, 0, 4,  // l- 14, 16
  0, 1, 2, 3, // l - 1
  0, 1, 2, 3, // l -7, 9
  0, 2, 1, 3,  // l - 10, 12
  0, 1, 4, 5, // l - 3
  0, 1, 4, 5, // l - 6, 9
  0, 4, 1, 5, // l - 14, 17
  0, 2, 4, 6, // l - 5
  0, 2, 4, 6, // l - 11, 12
  0, 4, 2, 6,  // l - 15, 17
  2, 3, //l - 7
  0, 1, // l - 9
  4, 6, // l - 11
  5, 7, // l - 13
  2, 6, // l - 15
  0, 4 // l - 17
};

__constant__ uint8_t g_advect_src_voxels_2D[N_UBLK_ADVECT_INTERACTIONS_2D] = {
  0, 2, //l - 0
  0, 4, // l - 2
  2, 0, // l- 6, 8
  4, 6, 4, 6, // l- 10, 12
  2, 6, 0, 4,  // l- 14, 16
  0, 2, // l - 1
  0, 2, // l -7, 9
  0, 2, 0, 2,  // l - 10, 12
  0, 4, // l - 3
  0, 4, // l - 6, 9
  0, 4, 0, 4, // l - 14, 17
  2, //l - 7
  0, // l - 9
};

__constant__ uint8_t g_advect_dst_voxels[N_UBLK_ADVECT_INTERACTIONS] = {
  4, 5, 6, 7, //l - 0
  2, 3, 6, 7, // l - 2
  1, 3, 5, 7, // l - 4
  4, 5, 6, 7, // l- 6, 8
  1, 3, 0, 2, // l- 10, 12
  1, 5, 3, 7,  // l- 14, 16
  4, 5, 6, 7, // l - 1
  6, 7, 4, 5, // l -7, 9
  5, 7, 4, 6,  // l - 10, 12
  2, 3, 6, 7, // l - 3
  6, 7, 2, 3, // l - 6, 9
  3, 7, 2, 6, // l - 14, 17
  1, 3, 5, 7, // l - 5
  5, 7, 1, 3, // l - 11, 12
  3, 7, 1, 5,  // l - 15, 17
  4, 5, //l - 7
  6, 7, // l - 9
  1, 3, // l - 11
  0, 2, // l - 13
  1, 5, // l - 15
  3, 7 // l - 17
};

__constant__ uint8_t g_advect_dst_voxels_2D[N_UBLK_ADVECT_INTERACTIONS_2D] = {
  4, 6, //l - 0
  2, 6, // l - 2
  4, 6, // l- 6, 8
  0, 2, 0, 2, // l- 10, 12
  0, 4, 2, 6,  // l- 14, 16
  4, 6, // l - 1
  6, 4, // l -7, 9
  4, 6, 4, 6,  // l - 10, 12
  2, 6, // l - 3
  6, 2, // l - 6, 9
  2, 6, 2, 6, // l - 14, 17
  4, //l - 7
  6 // l - 9
};  

__constant__ int8_t g_advect_neighbor_offsets[10][3] = {
  {0, 0, 0},
  {1, 0, 0},
  {0, 1, 0},
  {0, 0, 1},
  {1,-1, 0},
  {1, 1, 0},
  {-1,0, 1},
  {-1,0,-1},
  {0,-1, 1},
  {0, 1, 1},
};

/* Indices into (STATE_TABLE_D19) */
__constant__ uint8_t g_advect_velocity_indices[N_UBLK_ADVECT_INTERACTIONS] = {
  0, 0, 0, 0, //l - 0
  2, 2, 2, 2, // l - 2
  4, 4, 4, 4, // l - 4
  6, 6, 8, 8, // l- 6, 8
  10, 10, 12, 12, // l- 10, 12
  14, 14, 16, 16,  // l- 14, 16
  1, 1, 1, 1, // l - 1
  7, 7, 9, 9, // l -7, 9
  10, 10, 12, 12,  // l - 10, 12
  3, 3, 3, 3, // l - 3
  6, 6, 9, 9, // l - 6, 9
  14, 14, 17, 17, // l - 14, 17
  5, 5, 5, 5, // l - 5
  11, 11, 12, 12, // l - 11, 12
  15, 15, 17, 17,  // l - 15, 17
  7, 7, //l - 7
  9, 9, // l - 9
  11, 11, // l - 11
  13, 13, // l - 13
  15, 15, // l - 15
  17, 17 // l - 17    
};

__constant__ uint8_t g_advect_velocity_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D] = {
  0, 0, //l - 0
  2, 2, // l - 2
  6, 8, // l- 6, 8
  10, 10, 12, 12, // l- 10, 12
  14, 14, 16, 16,  // l- 14, 16
  1, 1,// l - 1
  7, 9,// l -7, 9
  10, 10, 12, 12,  // l - 10, 12
  3, 3,// l - 3
  6, 9,// l - 6, 9
  14, 14, 17, 17, // l - 14, 17
  7, //l - 7
  9 // l - 9
};

__constant__ int8_t g_state_velocities[N_STATES][3] = {
  { 1, 0, 0 },
  {-1, 0, 0 },
  { 0, 1, 0 },
  { 0,-1, 0 },
  { 0, 0, 1 },
  { 0, 0,-1 },
  { 1,-1, 0 },
  {-1, 1, 0 },
  { 1, 1, 0 },
  {-1,-1, 0 },
  {-1, 0, 1 },
  { 1, 0,-1 },
  {-1, 0,-1 },
  { 1, 0, 1 },
  { 0,-1, 1 },
  { 0, 1,-1 },
  { 0, 1, 1 },
  { 0,-1,-1 }
};
} //namespace GATHER_ADVECT

using namespace GATHER_ADVECT;

_ALWAYS_INLINE_
__device__ void push_states_onto_swap_advect_neighbor(const int dest_voxel,
                                                      const int src_voxel,
                                                      VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],
                                                      VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                                                      int latvec) {

  asINT32 parity = state_parity(latvec);
  src_states[latvec][src_voxel] = p_states[parity][dest_voxel];
}

template<BOOLEAN HAS_S2V, BOOLEAN IS_VR_FINE>
_ALWAYS_INLINE_ __device__
void append_v2v_contribution(UBLK pde_advect_ublk,
                             BOOLEAN is_timestep_even,
                             const int dest_voxel,
                             const int src_voxel,
                             VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                             VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                             int latvec) {
      
  if constexpr (HAS_S2V) {
    auto pas_factor = pde_advect_ublk->surf_lb_data()->post_advect_scale_factors[latvec][dest_voxel];
    if constexpr (IS_VR_FINE) {
      if (is_timestep_even) {
        if (((states[latvec][dest_voxel] == 0.0) && (pas_factor > 0))) {
          pas_factor = 1.0;
        }
      }
    }    
    states[latvec][dest_voxel] += pas_factor * src_states[latvec][src_voxel];
  } else {
    states[latvec][dest_voxel]  = src_states[latvec][src_voxel];
  }
}

template<BOOLEAN HAS_S2V, BOOLEAN IS_VR_FINE>
_ALWAYS_INLINE_ __device__
void append_v2v_split_contribution(UBLK pde_advect_ublk,
                                   BOOLEAN is_timestep_even,
                                   int dst_ublk_offset,
                                   TAGGED_UBLK neighbor_tagged_ublk,
                                   int nbr_latvec,
                                   tTHREAD_SPLIT_FACTOR_HANDLE<SPEED1>& split_advect_factors,
                                   const int child_dst_voxel,
                                   const int child_src_voxel,
                                   int latvec,
                                   SOLVER_INDEX_MASK prior_solver_index_mask) {

  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  STATE_PTR states = pde_advect_ublk->lb_states(prev_lb_index ^ 1)->m_states;
  sdFLOAT pas_factor = 1.0F;
    
  sUBLK_VECTOR_64* split_vector = neighbor_tagged_ublk.split_ublks();
  TAGGED_UBLK* split_tagged_ublks = split_vector->tagged_ublks();
  int num_split_ublks = split_vector->num_split_ublks();

  int dest_voxel = child_dst_voxel + (dst_ublk_offset << 3);

  if constexpr(!HAS_S2V) {
    states[latvec][dest_voxel] = 0.0F;
  }
  else {
    pas_factor = pde_advect_ublk->surf_lb_data()->post_advect_scale_factors[latvec][dest_voxel];        
    if constexpr (IS_VR_FINE) {          
      if (is_timestep_even) {
        auto vr_fine_data = pde_advect_ublk->vr_fine_data();
        BOOLEAN no_even_s2v = !vr_fine_data->m_even_s2v_weights_mask[latvec].test(dest_voxel);
        if (((no_even_s2v) && (pas_factor > 0))) {
          pas_factor = 1.0;
        }
      }
    }
  }
      
  for (int u = 0; u < num_split_ublks; u++) {

#if DEBUG_SPLIT_ADVECT_FACTORS
    sFLOAT split_factor = split_advect_factors.next_factor(u, latvec,
                                                           child_src_voxel,
                                                           child_dst_voxel);
#else
    sFLOAT split_factor = split_advect_factors.next_factor();
#endif
    TAGGED_UBLK split_tagged_ublk = split_tagged_ublks[u];
    UBLK neighbor_ublk = split_tagged_ublk.ublk();
    int neighbor_child_ublk_offset = split_tagged_ublk.get_offset_in_mega_block();
    STATE_PTR src_states = (neighbor_ublk->has_two_copies())?
      neighbor_ublk->lb_states(prev_lb_index)->m_states:
      neighbor_ublk->lb_states(ONLY_ONE_COPY)->m_states;
      
    int nbr_src_voxel = child_src_voxel + (neighbor_child_ublk_offset << 3);
      
    if (HAS_S2V) {
      states[latvec][dest_voxel] += split_factor * pas_factor * src_states[latvec][nbr_src_voxel];
    } else {
      states[latvec][dest_voxel] += split_factor * src_states[latvec][nbr_src_voxel];
    }      
  } // split-instance loop
}

// This should be replaced by the post_advect_finalize function in ublk.h, once it
// is vectorized
__device__
VOID post_advect_finalize(UBLK ublk,
                          SOLVER_INDEX_MASK prior_solver_index_mask,
                          ACTIVE_SOLVER_MASK active_solver_mask) {

  ACTIVE_SOLVER_MASK is_lb_active = active_solver_mask & LB_ACTIVE;
  ACTIVE_SOLVER_MASK is_turb_active = active_solver_mask & KE_PDE_ACTIVE;
  ACTIVE_SOLVER_MASK is_temp_active = active_solver_mask & T_PDE_ACTIVE;
  ACTIVE_SOLVER_MASK is_uds_active = active_solver_mask & UDS_PDE_ACTIVE;

  asINT32 prior_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = 1 ^ prior_lb_index;
  VOXEL_STATE (*curr_states)[sUBLK::N_VOXELS] = ublk->lb_states(curr_lb_index)->m_states;
  asINT32 prior_t_index = t_index_from_mask(prior_solver_index_mask);

  auto surf_lb = ublk->surf_lb_data();

  asINT32 voxel = threadIdx.x;
    
  // TODO Vectorize this
  STP_GEOM_VARIABLE inverse = 0.0;
  STP_GEOM_VARIABLE non_lrf_s2v_weight = surf_lb->s2v_weight[voxel] - surf_lb->lrf_s2v_weight[voxel];
    
  if (non_lrf_s2v_weight != 0.0) { inverse = 1.0F / non_lrf_s2v_weight; }
  surf_lb->pre_cf_n[voxel] = surf_lb->post_cf_n[voxel] * inverse;
  surf_lb->ustar_0[voxel] *= inverse;
  auto& simc = get_simc_ref();

  if (ublk->lrf_surfels_interaction_mask.test(voxel)) {
    inverse = 0.0;
    if (surf_lb->lrf_s2v_weight[voxel] != 0.0) {
      inverse = 1.0F / surf_lb->lrf_s2v_weight[voxel];
    }

    surf_lb->lrf_v2s_dist[voxel] *= inverse;
    surf_lb->lrf_s2s_factor[voxel] *= inverse;
    if (is_turb_active) {
      surf_lb->ustar_0_pair[voxel] *= inverse;
    }
  }
    
  if (ublk->lb_interaction_voxel_mask.test(voxel)) {
    inverse = 0.0;
    if (surf_lb->s2v_weight[voxel] != 0.0) {
      inverse = 1.0F / surf_lb->s2v_weight[voxel];
    }
    if (is_temp_active) {
      ublk->surf_t_data()->temp_wall[voxel] *= inverse;
    }
    if (is_turb_active) {
      if (g.some_face_has_ltt_transition_enabled) {
        ublk->surf_turb_data()->ltt_index[voxel] *= inverse;
      }
    }

    if (is_uds_active) {
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars)
        ublk->surf_uds_data(nth_uds)->uds_value[voxel] *= inverse;
    }

    BOOLEAN is_some_state_negative = FALSE;
    BOOLEAN is_some_state_negative_mc = FALSE;
    ccDOTIMES(latv, N_MOVING_STATES) {
      if (curr_states[latv][voxel] < 0.0) {
        is_some_state_negative = TRUE;
        // printf("NEGATIVE STATE:: UBLK %d, latvec %d, voxel %d, state %f, loc (%d, %d, %d)\n", 
        //        ublk->id(), 
        //        latv, 
        //        voxel, 
        //        curr_states[latv][voxel], 
        //        ublk->location(0, voxel/N_VOXELS_8), 
        //        ublk->location(1, voxel/N_VOXELS_8), 
        //        ublk->location(2, voxel/N_VOXELS_8);
        break;
      }
    }
      
    if (is_some_state_negative) {
      sdFLOAT temp_1;
      if (simc.thermal_feedback_is_on) {
        if (g.use_s2v_protection) {
          temp_1 = ublk->t_data()->fluid_temp[prior_t_index][voxel];
          force_ublk_states_in_range<GPU::sUBLK>(curr_states, temp_1, voxel);
        }
      } else {
        temp_1 = g_lb_temp_constant;
        force_ublk_states_in_range<GPU::sUBLK>(curr_states, temp_1, voxel);
      }
    }     
  }
  surf_lb->post_cf_n[voxel] = 0;
}

template<BOOLEAN IS_2D, BOOLEAN IS_PARITY, BOOLEAN HAS_S2V, BOOLEAN IS_VR_FINE>
__inline__  __device__  void split_advect_mblk(UBLK ublk,
                                               BOOLEAN is_timestep_even,
                                               SOLVER_INDEX_MASK prior_solver_index_mask,
                                               int interaction,
                                               int child_ublk_offset) {

  using TRAITS = INTERACTION_TRAITS<IS_2D>;
  int neighbor_index = TRAITS::neighbor_ublk_index(interaction);

  STP_STATE_VEL offsets[3] = {g_advect_neighbor_offsets[neighbor_index][0],
                              g_advect_neighbor_offsets[neighbor_index][1],
                              g_advect_neighbor_offsets[neighbor_index][2]};

  if (IS_PARITY) { offsets[0] *= -1; offsets[1] *= -1; offsets[2] *= -1; }

  auto nbr_latvec = convert_state_vel_to_latvec_index(offsets);
    
  const auto& box_access = ublk->box_access(child_ublk_offset);
  TAGGED_UBLK neighbor_tagged_ublk = box_access.neighbor_ublk(offsets);
    
  STP_STATE_INDEX neighbor_latvec_index = convert_state_vel_to_latvec_index(offsets);
    
  int latvec = TRAITS::velocity_index(interaction);
  int src_voxel, dst_voxel;
  if (IS_PARITY) {
    latvec = state_parity(latvec);
    dst_voxel = TRAITS::src_voxel(interaction);
    src_voxel = TRAITS::dst_voxel(interaction);
  } else {
    src_voxel = TRAITS::src_voxel(interaction);
    dst_voxel = TRAITS::dst_voxel(interaction);
  }

  // if (ublk->id() == 994 && child_ublk_offset == 7 && dst_voxel == 0 && latvec == 1) {
  //   printf("ThreadIdx.x %d\n", threadIdx.x);
  // }

  //This is a divergent branch, we'll try optimizing this later
  if (neighbor_tagged_ublk.is_ublk_split()) {
      
    auto split_advect_factors = ublk->get_split_advect_factors();
    int num_split_ublks = neighbor_tagged_ublk.split_ublks()->num_split_ublks();
    auto split_factor_handle = tTHREAD_SPLIT_FACTOR_HANDLE<SPEED1>(split_advect_factors,
                                                                   TRAITS::split_factor_index(interaction),
                                                                   child_ublk_offset,
                                                                   nbr_latvec,
                                                                   latvec,
                                                                   num_split_ublks);
    
    append_v2v_split_contribution<HAS_S2V, IS_VR_FINE>(ublk, is_timestep_even, child_ublk_offset,
                                                       neighbor_tagged_ublk, nbr_latvec,
                                                       split_factor_handle, dst_voxel, src_voxel,
                                                       latvec, prior_solver_index_mask);
  } else if (neighbor_tagged_ublk.is_simple_fluid_ublk()) {
    asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    STATE_PTR states = ublk->lb_states(prev_lb_index ^ 1)->m_states;
    UBLK neighbor_ublk = neighbor_tagged_ublk.ublk();
    STATE_PTR src_states = (neighbor_ublk->has_two_copies())?
      neighbor_ublk->lb_states(prev_lb_index)->m_states:
      neighbor_ublk->lb_states(ONLY_ONE_COPY)->m_states;
    dst_voxel += (child_ublk_offset << 3);
    src_voxel += (neighbor_tagged_ublk.get_offset_in_mega_block() << 3);
    append_v2v_contribution<HAS_S2V, IS_VR_FINE>(ublk, is_timestep_even, dst_voxel, src_voxel, states, src_states, latvec);
  }
}
  
template<BOOLEAN IS_2D, BOOLEAN IS_PARITY, BOOLEAN HAS_S2V, BOOLEAN IS_VR_FINE>
__inline__  __device__  void pde_advect_mblk(UBLK ublk,
                                             BOOLEAN is_timestep_even,
                                             SOLVER_INDEX_MASK prior_solver_index_mask,
                                             int interaction,
                                             int child_ublk_offset) { 

  using TRAITS = INTERACTION_TRAITS<IS_2D>;
  int neighbor_index = TRAITS::neighbor_ublk_index(interaction);

  STP_STATE_VEL offsets[3] = {g_advect_neighbor_offsets[neighbor_index][0],
                              g_advect_neighbor_offsets[neighbor_index][1],
                              g_advect_neighbor_offsets[neighbor_index][2]};
    
  if constexpr (IS_PARITY) { offsets[0] *= -1; offsets[1] *= -1; offsets[2] *= -1; }
    
  const auto& box_access = ublk->box_access(child_ublk_offset);
  TAGGED_UBLK neighbor_tagged_ublk = box_access.neighbor_ublk(offsets);
  int neighbor_child_ublk_offset = neighbor_tagged_ublk.get_offset_in_mega_block();

  if ( neighbor_tagged_ublk.is_simple_fluid_ublk(neighbor_child_ublk_offset) ) {

    UBLK neighbor_ublk = neighbor_tagged_ublk.ublk(); 

    asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    STATE_PTR prev_states = ublk->lb_states(prev_lb_index)->m_states; 
    STATE_PTR states = ublk->lb_states(prev_lb_index ^ 1)->m_states;

    STATE_PTR src_states = (neighbor_ublk->has_two_copies())?
      neighbor_ublk->lb_states(prev_lb_index)->m_states:
      neighbor_ublk->lb_states(ONLY_ONE_COPY)->m_states;

    int src_voxel = neighbor_child_ublk_offset << 3;
    int dst_voxel = child_ublk_offset << 3;
    int latvec = TRAITS::velocity_index(interaction);

    if (!IS_PARITY) {
      src_voxel += TRAITS::src_voxel(interaction);
      dst_voxel += TRAITS::dst_voxel(interaction);
    } else {
      src_voxel += TRAITS::dst_voxel(interaction);
      dst_voxel += TRAITS::src_voxel(interaction);
      latvec = state_parity(latvec);	
    }

#ifdef DEBUG_GPU      
    if (ublk->id() == 45123 && dst_voxel == 0 && latvec == 2) {
      printf("TS %ld, Block ID %d, ThreadId %d, dst_ublk %d, src_ublk %d, "
             "src_is_vr_fine %d, src_is_vr_coarse %d, src_voxel %d, dst_voxel %d,"
             "src_state %f, dst_state %f, is_vr %d, has_s2v %d\n",
             g_timescale.m_time, blockIdx.x, threadIdx.x,
             ublk->id(), neighbor_ublk->id(), neighbor_ublk->is_vr_fine(), neighbor_ublk->is_vr_coarse(), src_voxel, dst_voxel,
             src_states[latvec][src_voxel], states[latvec][dst_voxel], IS_VR_FINE, HAS_S2V);
    }
#endif
    append_v2v_contribution<HAS_S2V, IS_VR_FINE>(ublk, is_timestep_even, dst_voxel,
                                                 src_voxel, states, src_states, latvec);

    //INFBLK2 strand always goes first, hence the PDE advect UBLK pushes into the parity
    //slot of its one copy neighbor
    if (neighbor_ublk->does_advect_through_swap()) {
      push_states_onto_swap_advect_neighbor(dst_voxel, src_voxel, prev_states, src_states, latvec);
    }
  }
}

template<BOOLEAN IS_2D, BOOLEAN IS_NEAR, BOOLEAN IS_VR_FINE>
__global__  void pde_advect_mblks(UBLK_GROUP_TYPE group_type,
                                  size_t offset,
                                  BOOLEAN is_timestep_even,
                                  SOLVER_INDEX_MASK prior_solver_index_mask) {

  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;

  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];

  auto [interaction, child_ublk] = INTERACTION_TRAITS<IS_2D>::get_thread_adv_interaction_id_and_child_ublk();

  if ( !ublk->is_solid(child_ublk) ) {

    if (ublk->are_any_neighbors_split()) {
      if (IS_NEAR && ublk->are_states_clear(child_ublk)) {
        split_advect_mblk<IS_2D, FALSE, TRUE, IS_VR_FINE>(ublk, is_timestep_even,
                                                          prior_solver_index_mask,
                                                          interaction, child_ublk);
        split_advect_mblk<IS_2D, TRUE, TRUE, IS_VR_FINE>(ublk, is_timestep_even,
                                                         prior_solver_index_mask,
                                                         interaction, child_ublk);
      } else {
        split_advect_mblk<IS_2D, FALSE, FALSE, IS_VR_FINE>(ublk, is_timestep_even,
                                                           prior_solver_index_mask,
                                                           interaction, child_ublk);
        split_advect_mblk<IS_2D, TRUE, FALSE, IS_VR_FINE>(ublk, is_timestep_even,
                                                          prior_solver_index_mask,
                                                          interaction, child_ublk);
      }
    }
    else {
      if (IS_NEAR && ublk->are_states_clear(child_ublk)) {
        pde_advect_mblk<IS_2D, FALSE, TRUE, IS_VR_FINE>(ublk, is_timestep_even,
                                                        prior_solver_index_mask,
                                                        interaction, child_ublk);
        pde_advect_mblk<IS_2D, TRUE, TRUE, IS_VR_FINE>(ublk, is_timestep_even,
                                                       prior_solver_index_mask,
                                                       interaction, child_ublk);
      } else {
        pde_advect_mblk<IS_2D, FALSE, FALSE, IS_VR_FINE>(ublk, is_timestep_even,
                                                         prior_solver_index_mask,
                                                         interaction, child_ublk);
        pde_advect_mblk<IS_2D, TRUE, FALSE, IS_VR_FINE>(ublk, is_timestep_even,
                                                        prior_solver_index_mask,
                                                        interaction, child_ublk);
      }
    }
  }
}

__global__ void copy_z_only_states(UBLK_GROUP_TYPE group_type,
                                   size_t offset,
                                   SOLVER_INDEX_MASK prior_solver_index_mask,
                                   ACTIVE_SOLVER_MASK active_solver_mask) {

  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = 1 ^ prev_lb_index;
  
  if (g_adv_fraction < 1.0f) {
    scale_up_z_only_states(ublk, g_one_over_adv_fraction, curr_lb_index, TRUE, active_solver_mask);
  } else {
    copy_z_only_states(ublk, curr_lb_index, active_solver_mask);
  }
}

template<BOOLEAN IS_NEAR>  
__global__ void pde_advect_mblk_non_moving_state(UBLK_GROUP_TYPE group_type,
                                                 size_t offset,
                                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                                 ACTIVE_SOLVER_MASK active_solver_mask) {

  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;
      
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
  int child_ublk = threadIdx.x / N_VOXELS_8;
  
  if ((!IS_NEAR) || (IS_NEAR && !ublk->are_states_clear(child_ublk))) {
    assert(ublk->has_two_copies() && "ublk must have two copies of states");
    asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    STATE_PTR prev_states = ublk->lb_states(prev_lb_index)->m_states; 
    STATE_PTR states = ublk->lb_states(prev_lb_index ^ 1)->m_states;      
    states[V_0_0_0][threadIdx.x] = prev_states[V_0_0_0][threadIdx.x];
  }
}

__global__ void post_advect_finalize(UBLK_GROUP_TYPE group_type,
                                     size_t offset,
                                     SOLVER_INDEX_MASK prior_solver_index_mask,
                                     ACTIVE_SOLVER_MASK active_solver_mask) {
    
  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;
      
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
    
  post_advect_finalize(ublk, prior_solver_index_mask, active_solver_mask);
    
}

//Defined in PHYSICS
VOID compute_pressure_gradients(UBLK_GROUP_TYPE group_type,
                                const size_t* range,
                                asINT32 voxel_grad_timestep_index);
  
/*=============================================================
 * @advect_ublks_interactions
 * Advection algorithm that processes 72 possible
 * UBLK interactions across 92 threads (3 Warps)
 * Inspired by Casey Bartlett's advection scheme
 *=============================================================*/
template<BOOLEAN IS_NEAR, BOOLEAN IS_VR_FINE>
VOID advect_interactions(UBLK_GROUP_TYPE group_type,
                         BOOLEAN is_T_S_lb_solver_on,
			 BOOLEAN is_UDS_lb_solver_on,
                         const size_t* range,
                         BOOLEAN is_timestep_even,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         ACTIVE_SOLVER_MASK active_solver_mask) {
  
  size_t n_adv_threads_per_block = n_advect_interactions_threads_per_block();

  size_t num_advect_ublks = range[1] - range[0];

  assert(num_advect_ublks > 0);

  if (sim.is_2d()) {
    pde_advect_mblks<TRUE, IS_NEAR, IS_VR_FINE><<<num_advect_ublks, n_adv_threads_per_block, NO_DYN_SHMEM, g_stream>>>
    (group_type,
     range[0],
     is_timestep_even,
     prior_solver_index_mask);
  } else {
    pde_advect_mblks<FALSE, IS_NEAR, IS_VR_FINE><<<num_advect_ublks, n_adv_threads_per_block, NO_DYN_SHMEM, g_stream>>>
      (group_type,
       range[0],
       is_timestep_even,
       prior_solver_index_mask);    
  }

  checkCudaErrors( cudaPeekAtLastError() );    
  cudaStreamSynchronize(g_stream);

  pde_advect_mblk_non_moving_state<IS_NEAR><<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>(
                                                                                                       group_type,
                                                                                                       range[0],
                                                                                                       prior_solver_index_mask,
                                                                                                       active_solver_mask);

  if (sim.is_2d()) {
    copy_z_only_states<<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>(
                                                                                  group_type,
                                                                                  range[0],
                                                                                  prior_solver_index_mask,
                                                                                  active_solver_mask);    
  }

  checkCudaErrors( cudaPeekAtLastError() );
  cudaStreamSynchronize(g_stream);
    
  if constexpr (IS_NEAR && (!IS_VR_FINE)) {

    asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);

    if (::sim.is_turb_model) {
      compute_pressure_gradients(group_type, range, prev_lb_index);
    }
  
    post_advect_finalize<<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
      (group_type,
       range[0],
       prior_solver_index_mask,
       active_solver_mask);
  }

  checkCudaErrors( cudaPeekAtLastError() );    
  cudaStreamSynchronize(g_stream);

}


VOID gather_advect(UBLK_GROUP_TYPE gtype,
                   BOOLEAN is_T_S_lb_solver_on,
		   BOOLEAN is_UDS_lb_solver_on,
                   const size_t* range,
                   BOOLEAN is_timestep_even,
                   SOLVER_INDEX_MASK prior_solver_index_mask,
                   ACTIVE_SOLVER_MASK active_solver_mask) {

  if (is_non_vr_nearblock_group_type(gtype)) {
    advect_interactions<TRUE, FALSE>(gtype, is_T_S_lb_solver_on, is_UDS_lb_solver_on, range,
                                     is_timestep_even, prior_solver_index_mask, active_solver_mask);
  } else if (is_vrfine_nearblock_group_type(gtype)) {
    advect_interactions<TRUE, TRUE>(gtype, is_T_S_lb_solver_on, is_UDS_lb_solver_on, range,
                                    is_timestep_even, prior_solver_index_mask, active_solver_mask);      
  } else if (is_vrfine_farblock_group_type(gtype)) {
    advect_interactions<FALSE, TRUE>(gtype, is_T_S_lb_solver_on, is_UDS_lb_solver_on, range,
                                     is_timestep_even, prior_solver_index_mask, active_solver_mask);      
  } else {
    advect_interactions<FALSE, FALSE>(gtype, is_T_S_lb_solver_on, is_UDS_lb_solver_on, range,
                                      is_timestep_even, prior_solver_index_mask, active_solver_mask);
  }
}

__global__ void prepare_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                               size_t offset,
                                               BOOLEAN is_LB_active,
                                               SOLVER_INDEX_MASK prior_solver_index_mask,
                                               ACTIVE_SOLVER_MASK active_solver_mask) {

  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;
      
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
    
  prepare_ublk_for_next_timestep(ublk, prior_solver_index_mask, active_solver_mask, is_LB_active);
    
}
  
VOID prepare_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                    const size_t* range,
                                    BOOLEAN is_LB_active,
                                    SOLVER_INDEX_MASK prior_solver_index_mask,
                                    ACTIVE_SOLVER_MASK active_solver_mask) {

  size_t num_advect_ublks = range[1] - range[0];

  assert(num_advect_ublks > 0);

  prepare_ublk_group_for_next_ts<<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
    (group_type, 
     range[0],
     is_LB_active, prior_solver_index_mask,
     active_solver_mask);

  checkCudaErrors( cudaPeekAtLastError() );
    
  cudaStreamSynchronize(g_stream);    
}


template<BOOLEAN DO_EXPLODE>
__global__ void prepare_vr_fine_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                                       size_t offset,
                                                       SOLVER_INDEX_MASK prior_solver_index_mask) {

  size_t ublk_index = offset +
    (blockIdx.x * gridDim.y + blockIdx.y)*gridDim.z + blockIdx.z;
      
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];
    
  prepare_vr_fine_ublk_for_next_timestep<DO_EXPLODE>(ublk, prior_solver_index_mask);
    
}
  
template<BOOLEAN DO_EXPLODE>
VOID prepare_vr_fine_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                            const size_t* range,
                                            SOLVER_INDEX_MASK prior_solver_index_mask) {

  size_t num_advect_ublks = range[1] - range[0];

  prepare_vr_fine_ublk_group_for_next_ts<DO_EXPLODE><<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
    (group_type, 
     range[0],
     prior_solver_index_mask);

  checkCudaErrors( cudaPeekAtLastError() );
    
  cudaStreamSynchronize(g_stream);    
}

template
VOID prepare_vr_fine_ublk_group_for_next_ts<TRUE>(UBLK_GROUP_TYPE group_type,
                                                  const size_t* range,
                                                  SOLVER_INDEX_MASK prior_solver_index_mask);

template
VOID prepare_vr_fine_ublk_group_for_next_ts<FALSE>(UBLK_GROUP_TYPE group_type,
                                                   const size_t* range,
                                                   SOLVER_INDEX_MASK prior_solver_index_mask);  

__global__ void vr_coarse_coalesce_kernel(UBLK_GROUP_TYPE group_type,
                                          size_t offset,
                                          SOLVER_INDEX_MASK prior_solver_index_mask,
                                          ACTIVE_SOLVER_MASK active_solver_mask)
{
  size_t ublk_index = offset + blockIdx.x;
  UBLK ublk = g_ublk_groups[group_type].m_ublks[ublk_index];

  if ( ublk->is_vr_coarse() ) {
    execute_coalesce_rule(ublk, prior_solver_index_mask, active_solver_mask);
  }
}

VOID vr_coarse_coalesce(UBLK_GROUP_TYPE group_type,
                        const size_t range[2],
                        SOLVER_INDEX_MASK prior_solver_index_mask,
                        ACTIVE_SOLVER_MASK active_solver_mask) {

  size_t num_advect_ublks = range[1] - range[0];

  vr_coarse_coalesce_kernel<<<num_advect_ublks, N_VOXELS_64, NO_DYN_SHMEM, g_stream>>>
                            (group_type,
                             range[0],
                             prior_solver_index_mask,
                             active_solver_mask);
  checkCudaErrors( cudaPeekAtLastError() );
    
  cudaStreamSynchronize(g_stream);
}

} //namespace GPU
