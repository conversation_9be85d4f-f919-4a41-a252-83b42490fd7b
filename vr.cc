/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Variable Resolution 
 *--------------------------------------------------------------------------*/
#include "common_sp.h"
#include "random.h"
#include "sim.h"
#include "group.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "vr.h"
#include "vr_coalesce_explode.h"
#include "sp_timers.h"
#include "mirror.h"
#include "gather_advect.h"
#include "gpu_ublk_process_control.h"
#include "gpu_host_include.h"

#include <vector>
#include PHYSICS_H

#define DEBUG_VR_COARSE(ublk, voxel) (FALSE)
#define COARSE_UBLK_OF_INTEREST (1895)
#define COARSE_VOXEL_OF_INTEREST (6)
//#define DEBUG_VR_COARSE(ublk, voxel) ((ublk)->id() == (COARSE_UBLK_OF_INTEREST) && voxel == (COARSE_VOXEL_OF_INTEREST))

#define DEBUG_VR_FINE(ublk) (FALSE)
#define FINE_UBLK_OF_INTEREST (1897)
#define FINE_VOXEL_OF_INTEREST (2)
#define LATVEC_OF_INTEREST (2)
//#define DEBUG_VR_FINE(ublk) ((ublk)->id() == (FINE_UBLK_OF_INTEREST)) 

#define UBLK_OF_INTEREST (1895)
#define VOXEL_OF_INTEREST (6)
#define DEBUG_UBLK(ublk) (FALSE)
//#define DEBUG_UBLK(ublk) ((ublk)->id() == (UBLK_OF_INTEREST))

static VOXEL_MASK_8 get_ublk_face_voxel_mask(int face)
{
  if (sim.is_2d()) {
    cassert( face < sizeof(sim_voxel_mask_on_ublk_face_2d)/sizeof(VOXEL_MASK_8));
  } else {
    cassert( face < sizeof(sim_voxel_mask_on_ublk_face)/sizeof(VOXEL_MASK_8));
  }
  return sim.is_2d() ? sim_voxel_mask_on_ublk_face_2d[face] : sim_voxel_mask_on_ublk_face[face];
}

static sINT32 get_neighbor_ublk_index(sINT32 ublk_index, int i, int j, int k)
{
  if ( i != 0 ) {
    ublk_index = neighbor_voxel_along_axis(ublk_index, 0);
  }
  if ( j != 0 ) {
    ublk_index = neighbor_voxel_along_axis(ublk_index, 1);
  }
  if ( k != 0 ) {
    ublk_index = neighbor_voxel_along_axis(ublk_index, 2);
  }
  return ublk_index;
}

static sINT32 get_neighbor_ublk_index(sINT32 ublk_index, sINT16 offsets[3])
{
  return get_neighbor_ublk_index(ublk_index, offsets[0], offsets[1], offsets[2]);
}

static VOXEL_MASK_8 get_ublks_to_test(sINT16 offsets[3])
{
  VOXEL_MASK_8 fine_ublks_to_test{0xff};
  for(int i=0; i<3; i++) {
    if ( offsets[i] != 0 ) {
      fine_ublks_to_test &= get_ublk_face_voxel_mask(face_index(i, offsets[i]));
    }
  }
  return fine_ublks_to_test;
}

static void set_finer_scale_bits(UBLK ublk, STP_FACE opposite_face)
{
  ublk->vr_topology().add_face_to_different_scale_mask(opposite_face);
  ublk->m_are_any_neighbors_different_scale |= get_ublk_face_voxel_mask(opposite_face);
}

static void set_face_finer_scale_bits(TAGGED_UBLK *fine_neighbor, sINT16 offsets[3])
{
  int axis = (offsets[1] != 0)*1 + (offsets[2] != 0)*2;
  int dir = offsets[0] + offsets[1] + offsets[2];
  STP_FACE face = face_index(axis,dir);
  STP_FACE opposite_face = stp_opposite_face(face);

  fine_neighbor->apply_to_ublk_or_split_ublks( 
                                              [&] (UBLK ublk) 
                                              {
                                              // if ( ublk->id() == 53339 ) {
                                              //   std::cout << "Found fine neighbor ublk " << ublk->id() << std::endl;
                                              // }
                                              set_finer_scale_bits(ublk, opposite_face);
                                              }
                                             );
}

static void set_coarse_ublk_face_finer_scale_bits(UBLK coarse_ublk, sINT16 offsets[3])
{
  int axis = (offsets[1] != 0)*1 + (offsets[2] != 0)*2;
  int dir = offsets[0] + offsets[1] + offsets[2];
  STP_FACE face = face_index(axis,dir);
  coarse_ublk->vr_topology().add_face_to_finer_scale_mask(face);
  coarse_ublk->vr_topology().add_face_to_different_scale_mask(face);
  coarse_ublk->m_are_any_neighbors_different_scale |= get_ublk_face_voxel_mask(face);
}

static bool add_scale_interface(UBLK coarse_ublk,
                                sVR_COARSE_INTERFACE_DATA* vr_coarse_data,
                                TAGGED_UBLK* tagged_coarse_neighbor,
                                VOXEL_MASK_8 fine_ublks_to_test,
                                sINT16 offsets[3])
{

  sSCALE_BOX_INTERFACE scale_interface;
  bool found_fine_neighbor = false;

  int count = std::abs(offsets[0]) + std::abs(offsets[1]) + std::abs(offsets[2]);

  DO_VOXELS_IN_MASK(fine_ublk_idx, fine_ublks_to_test) {
    if ( UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_ublk_idx).ublk() ) {
      sBOX_ACCESS* box_access = &fine_ublk->box_access();
      TAGGED_UBLK *fine_tagged_ublk = box_access->tagged_ublk();
      TAGGED_UBLK *fine_neighbor = box_access->checked_neighbor_ublk(fine_tagged_ublk, offsets);
      if (!fine_neighbor) {
        msg_internal_error("Failed to find fine neighbor ublk during VR setup");
      }

      // We don't want to add VR fine ublks to the scale interface.
      if (fine_neighbor->is_ublk() && (fine_neighbor->is_ublk_split() || !fine_neighbor->ublk()->is_vr_fine() ) ) {
        found_fine_neighbor = true;

        asINT32 neighbor_ublk_index = get_neighbor_ublk_index(fine_ublk_idx, offsets);

        scale_interface.m_fine_ublks[neighbor_ublk_index] = *fine_neighbor;

        if ( count == 1 ) {
          set_face_finer_scale_bits(fine_neighbor, offsets);
        }

        if (fine_neighbor->is_ublk_last()) {
          UBLK jump_ublk = NULL;
          if (fine_neighbor->is_ublk_split()) { 
            sUBLK_VECTOR *split_ublks  = fine_neighbor->split_ublks();
            fine_neighbor = split_ublks->m_tagged_ublks; 
          }
          jump_ublk = fine_neighbor->ublk();

          box_access = &jump_ublk->box_access();
          fine_neighbor = box_access->tagged_ublk();
        }
        // next fine ublk - it is okay for this check to fail, as ublk boxes at the edge
        // of a non-periodic simvol do not have a border to offset into.
        fine_neighbor = box_access->checked_neighbor_ublk(fine_neighbor, offsets);
        if (fine_neighbor) {
          if (fine_neighbor->is_ublk() && !fine_neighbor->is_ublk_scale_interface()) {
            if (fine_neighbor->is_ublk_split() || !fine_neighbor->ublk()->is_vr_fine() ) {
              scale_interface.m_fine_ublks[fine_ublk_idx] = *fine_neighbor;
            }
          }
        }
      }
    }
  }

  if ( count == 1 && found_fine_neighbor) {
    set_coarse_ublk_face_finer_scale_bits(coarse_ublk, offsets);
  }

  if ( found_fine_neighbor ) {
    sSCALE_BOX_INTERFACE *scale_box_interface;
    if ( tagged_coarse_neighbor->is_ublk_scale_interface() ) {
      scale_box_interface = tagged_coarse_neighbor->interface();
    }
    else {
      scale_box_interface = sSHOB_ALLOCATOR<sSCALE_BOX_INTERFACE>::malloc(sizeof(sSCALE_BOX_INTERFACE));
      new (scale_box_interface) sSCALE_BOX_INTERFACE();
    }

    ccDOTIMES(i, N_VOXELS_8) {
      if ( scale_box_interface->m_fine_ublks[i].is_null() ) {
        scale_box_interface->m_fine_ublks[i] = scale_interface.m_fine_ublks[i];
      }
    }
    tagged_coarse_neighbor->set_scale_interface(scale_box_interface);
  }
  return found_fine_neighbor;
}
static void set_edge_neighbor_masks(sUBLK * coarse_ublk,
                                    TAGGED_UBLK *coarse_tagged_ublk,
                                    sBOX_ACCESS& coarse_ublk_box_access,
                                    sVR_COARSE_INTERFACE_DATA * coarse_vr_data,
                                    bool are_neighbors_fine[3][3][3])
{

  sINT16 kstart = sim.is_2d() ? 0 : -1;
  sINT16 kkstart = sim.is_2d() ? 1 : 0;
  sINT16 kend = sim.is_2d() ? 1 : 2;
  for(sINT16 i=-1, ii=0; i<2; i++, ii++) {
    for(sINT16 j=-1, jj=0; j<2; j++, jj++) {
      for(sINT16 k=kstart, kk=kkstart; k<kend; k++, kk++) {
        int count = std::abs(i) + std::abs(j) + std::abs(k);
        if (count != 2) {
          continue;
        }

        if ( are_neighbors_fine[ii][jj][kk] ) {
          bool are_both_cardinal_neighbors_fine = true;
          if ( i != 0 ) {
            are_both_cardinal_neighbors_fine &= are_neighbors_fine[ii][1][1];
          }
          if ( j != 0 ) {
            are_both_cardinal_neighbors_fine &= are_neighbors_fine[1][jj][1];
          }
          if ( k != 0 ) {
            are_both_cardinal_neighbors_fine &= are_neighbors_fine[1][1][kk];
          }

          if (are_both_cardinal_neighbors_fine) {
            sINT16 offsets[3] = {i,j,k};
            TAGGED_UBLK * neighbor = coarse_ublk_box_access.checked_neighbor_ublk(coarse_tagged_ublk, offsets);
            if (!neighbor) {
              msg_internal_error("Failed to find neighbor ublk during VR setup");
            }
            VOXEL_MASK_8 coarse_voxel_mask = get_ublks_to_test(offsets);
            sINT16 neighbor_offset[3] = { (sINT16)-i, (sINT16)-j, (sINT16)-k};
            VOXEL_MASK_8 coarse_neighbor_voxel_mask = get_ublks_to_test(neighbor_offset);
            DO_VOXELS_IN_MASK(fine_ublk_idx, coarse_voxel_mask) {
              if (UBLK vr_fine_ublk = coarse_vr_data->vr_fine_ublk(fine_ublk_idx).ublk()) {
                auto& vr_fine_ublk_box_access = vr_fine_ublk->box_access();
                TAGGED_UBLK *vr_fine_tagged_ublk = vr_fine_ublk_box_access.tagged_ublk();
                TAGGED_UBLK *fine_neighbor = vr_fine_ublk_box_access.checked_neighbor_ublk(vr_fine_tagged_ublk, offsets);
                if (!fine_neighbor) {
                  msg_internal_error("Failed to fine neighbor ublk during VR setup");
                }

                if (fine_neighbor->is_ublk() && (fine_neighbor->is_ublk_split() || !fine_neighbor->ublk()->is_vr_fine() ) ) {
                  fine_neighbor->apply_to_ublk_or_split_ublks( 
                                                              [&] (UBLK ublk)
                                                              {
                                                              ublk->m_are_any_neighbors_different_scale |= coarse_neighbor_voxel_mask;
                                                              }
                                                             );
                }
              }
            }
          }
        }
      }
    }
  }  
}

static void assign_scale_interface_impl(sUBLK * coarse_ublk) {
  auto& coarse_ublk_box_access = coarse_ublk->box_access();
  TAGGED_UBLK *coarse_tagged_ublk = coarse_ublk_box_access.tagged_ublk();
  sVR_COARSE_INTERFACE_DATA *vr_coarse_data =  coarse_ublk->vr_coarse_data();
  bool are_neighbors_fine[3][3][3];
  sINT16 kstart = sim.is_2d() ? 0 : -1;
  sINT16 kkstart = sim.is_2d() ? 1 : 0;
  sINT16 kend = sim.is_2d() ? 1 : 2;

  for(sINT16 i=-1, ii=0; i<2; i++, ii++) {
    for(sINT16 j=-1, jj=0; j<2; j++, jj++) {
      for(sINT16 k = kstart, kk=kkstart; k < kend; k++, kk++) {
        if (i == 0 && j == 0 && k == 0) {
          continue;
        }

        sINT16 offsets[3] = {i,j,k};
        TAGGED_UBLK *neighbor = coarse_ublk_box_access.checked_neighbor_ublk(coarse_tagged_ublk, offsets); 

        if (!neighbor) {
          msg_internal_error("Failed to find neighbor ublk during VR setup");
        }
        int count = std::abs(offsets[0]) + std::abs(offsets[1]) + std::abs(offsets[2]);

        if (!neighbor->is_ublk() || neighbor->is_ublk_scale_interface()) {
          VOXEL_MASK_8 fine_ublks_to_test = get_ublks_to_test(offsets);
          are_neighbors_fine[ii][jj][kk] = add_scale_interface(coarse_ublk, vr_coarse_data, neighbor, fine_ublks_to_test, offsets);
        }
        else {
          are_neighbors_fine[ii][jj][kk] = false;
        }
      }
    }
  }

  set_edge_neighbor_masks(coarse_ublk, coarse_tagged_ublk, coarse_ublk_box_access, vr_coarse_data, are_neighbors_fine);

  asINT32 n_fine_blks = N_VOXELS_8;
  for (asINT32 fine_ublk_idx = 0; fine_ublk_idx < n_fine_blks; fine_ublk_idx++) {
    UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_ublk_idx).ublk();
    if (fine_ublk) {
      asINT32 coarse_voxel = fine_ublk_idx;
      coarse_ublk->m_are_any_neighbors_different_scale.set(coarse_voxel);
    }
  }
}

// pull out a list of all the coarse ublks and process each coarse ublk in turn.
// Sibling VR Fine ublks are held next to each other in the groups. We use this
// to prevent processing the same coarse ublk multiple times.
VOID assign_scale_interface() {
  std::vector<UBLK> coarse_ublks;
  coarse_ublks.reserve(1024);
  UBLK last_coarse_ublk = nullptr;

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(vr_fine_ublk, scale) {
      sVR_FINE_INTERFACE_DATA *vr_fine_data =  vr_fine_ublk->vr_fine_data();
      UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
      if (coarse_ublk != last_coarse_ublk) {
        coarse_ublks.push_back(coarse_ublk);
        last_coarse_ublk = coarse_ublk;
      }
    }

    DO_SOLID_VRBLKS_OF_SCALE(solid_vr_fine_ublk, scale) {
      sVR_FINE_INTERFACE_DATA *vr_fine_data =  solid_vr_fine_ublk->vr_fine_data();
      UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
      assign_scale_interface_impl(coarse_ublk);
      if (coarse_ublk != last_coarse_ublk) {
        coarse_ublks.push_back(coarse_ublk);
        last_coarse_ublk = coarse_ublk;
      }
    }
  }

  for(UBLK coarse_ublk : coarse_ublks) {
    assign_scale_interface_impl(coarse_ublk);
  }

}

static
TAGGED_UBLK * find_ublk_box_from_location(asINT32 scale, STP_COORD location[3]){
  DO_UBLK_BOXES_OF_SCALE(ublk_box, scale) {
    if ((location[0] > ublk_box->m_location[0]) &&
        (location[1] > ublk_box->m_location[1]) &&
        ((sim.num_dims == 2) ||
         (location[2] > ublk_box->m_location[2]))) {
      uINT16 indices[3];
      ublk_box->get_indices(location, indices);
      if ((indices[0] < ublk_box->m_size[0] - 1) &&
          (indices[1] < ublk_box->m_size[1] - 1) &&
          ((sim.num_dims == 2) ||
           (indices[2] <ublk_box->m_size[2] - 1))) {
        TAGGED_UBLK * tagged_ublk = ublk_box->tagged_ublk(indices);
        if (!tagged_ublk->is_null())
          return tagged_ublk;
      }
    }
  }
  return NULL;
}

static VOID compute_explode_states_internal(const STP_LATVEC_MASK mask,
                                            STATE_TABLE_ENTRY state_table,
                                            const asINT32 n_moving_states,
                                            cVR_STATES states[N_VOXELS_8])
{
  BOOLEAN is_2d = sim.is_2d();
  /* Loop over each fine microblock and state */

  asINT32 coarse_voxel_idx = 0;
  DO_ACTIVE_VOXELS(coarse_voxel) {

    // lower-left coordinate of the coarse voxel in coordinates local to the coarse ublk
    STP_LOCATION coarse_voxel_loc = { num_to_voxel_x(coarse_voxel),
      num_to_voxel_y(coarse_voxel),
      num_to_voxel_z(coarse_voxel)};

    /* This is a valid microblock to check */
    ccDOTIMES(state, n_moving_states) {

      asINT32 vel[3] = {state_table[state].vel[0], state_table[state].vel[1], state_table[state].vel[2]};

      // For 2D, states with non-zero Z state velocities are included, but
      // the velocity is set to zero while checking where the particles end
      // up
      if (is_2d) vel[2] = 0;

      // if any of the subdirections comprising the state contain a fine
      // neighbor, we must explode this state, even if the destination ublk
      // is not fine (See comment at top of this file) So for state (-1, 1,1),
      // we would loop through 8 substates (not all of them may be valid states),
      // -1 1 1
      // -1 1 0
      // -1 0 1
      // -1 0 0
      //  0 1 1
      //  0 1 0
      //  0 0 1
      //  0 0 0
      //  to check if any of these sub-states has a fine mask set

      // nx, ny, nz are the number of sub-states in the x, y, and z
      // directions. So if the state velocity vx is -2, then the sub-states
      // present along x could be -2, -1, 0, and thus nx = 3. The actual
      // state mask is checked later to filter out states which are not
      // present

      asINT32 nx = 1 + abs(vel[0]), ny = 1 + abs(vel[1]), nz = 1 + abs(vel[2]);
      asINT32 vx = vel[0];


      ccDOTIMES(x, nx) {
        asINT32 vy = vel[1];
        ccDOTIMES(y, ny) {
          asINT32 vz = vel[2];
          ccDOTIMES(z, nz) {

            // find out where the particles end up for each sub-state
            STP_LOCATION dest_coarse_voxel_loc = {coarse_voxel_loc[0] + vx,
              coarse_voxel_loc[1] + vy,
              coarse_voxel_loc[2] + vz};
            // convert the destination (which is on the coarse voxel scale) to the coarse ublk scale
            // If the dest coordinate is negative, subtract 1, and
            // then perform an *integer* division by 2. If positive, perform
            // an *integer* division by 2. This yields the lower left-based
            // coordinate of the coarse scale ublk that the particles end up in.
            STP_LOCATION dest_coarse_ublk_loc;
            ccDOTIMES(i, 3) {
              dest_coarse_ublk_loc[i] = dest_coarse_voxel_loc[i] < 0 ?
                (dest_coarse_voxel_loc[i] - 1) / 2 : (dest_coarse_voxel_loc[i]) / 2;
            }

            // the state mask for the fine neighbors is related to the destination on
            // the coarse ublk scale, so check the sub-state dest against the state mask
            //
            if (mask & convert_ublk_neighbor_vector_to_mask(dest_coarse_ublk_loc[0],
                                                            dest_coarse_ublk_loc[1],
                                                            dest_coarse_ublk_loc[2])) {
              states[coarse_voxel].set(state);
              // break out of the nested sub-state do loops and the State loop
              goto next_state;
            }
            (vz > 0) ? --vz : ++vz;
          }
          (vy > 0) ? --vy : ++vy;
        }
        (vx > 0) ? --vx : ++vx;
      }

next_state: ;
    }                        /* State loop */

  }                        /* Coarse voxel loop */

}

static VOID compute_explode_states(VR_CONFIG config,
                                   asINT32 n_moving_states,
                                   STATE_TABLE_ENTRY state_table)
{
  STP_LATVEC_MASK fine_mask = config->fine_mask;
  compute_explode_states_internal(fine_mask, state_table, n_moving_states, config->explode_states);
}

static VOID compute_coalesce_states(VR_CONFIG config,
                                    asINT32 n_moving_states,
                                    STATE_TABLE_ENTRY state_table)
{

  cVR_STATES (&coalesce_states)[N_VOXELS_8] = config->coalesce_states;
  cVR_STATES (&explode_states)[N_VOXELS_8] = config->explode_states;
  // compute_explode_states *must* be called before this function is called

  ccDOTIMES(i, N_VOXELS_8) {
    ccDO_VR_STATES(state, explode_states[i]) {
      coalesce_states[i].set(state_parity(state));
    }
  }
}

// We use a deque here because we don't want the VR configs to
// move in memory. Each ublk has a pointer to a specific sVR_CONFIG.
// As long as we only add vr configs to the back of the deque, they
// won't move.
static std::deque<sVR_CONFIG> vr_configs;

const std::deque<sVR_CONFIG>& get_vr_configs()
{
  return vr_configs;
}

size_t get_vr_config_idx(const sVR_CONFIG& config)
{
  auto it = std::find( vr_configs.begin(), vr_configs.end(), config );
  if ( it == vr_configs.end() ) {
    msg_internal_error("Cannot find vr_config");
  }
  return std::distance(vr_configs.begin(), it);
}

VR_CONFIG find_vr_config(STP_LATVEC_MASK fine_mask)
{
  /* If a configuration exists with the same attributes, return it. */
  auto it = std::find_if( vr_configs.begin(), vr_configs.end(), 
                          [fine_mask](const sVR_CONFIG& config) { return config.fine_mask == fine_mask; });

  if ( it != vr_configs.end() ) {
    return &(*it);
  }

  /* Otherwise, create a new configuration. */
  vr_configs.emplace_back(fine_mask);
  sVR_CONFIG& config = vr_configs.back();

  /* Compute which states to explode and coalesce */
  compute_explode_states(&config, N_MOVING_STATES, g_state_table);

  // *MUST* be called only after compute_explode_states is called
  // since the coalesce states are initialized from the explode states
  compute_coalesce_states(&config, N_MOVING_STATES, g_state_table);

  return &config;
}

asINT32 collect_fine_split_neighbors(TAGGED_UBLK fine_neighbor, UBLK fine_ublk,
				     asINT32 axis, asINT32 dir,
				     sTAGGED_SPLIT_FACTOR *neighbor_split_facs)
{
  STP_FACE face = face_index(axis, dir);
  asINT32 n_face_voxels = sim.is_2d() ? 2 : 4;

  auto& box_access = fine_ublk->box_access();

  SPLIT_ADVECT_INFO fine_split_advect_info  = fine_ublk->get_split_advect_info();
  uINT8 fine_advect_from_split_mask         = fine_ublk->advect_from_split_mask();

  // collect all the split neighbors that advect to the vr fine
  // voxels adjacent to the face
  asINT32 n_neighbor_splits = 0;
  ccDOTIMES(nfv, n_face_voxels) {
    asINT32 vrf_voxel = sim.is_2d()?
      (sim_voxels_on_ublk_face_2d[face][nfv]) :
      sim_voxels_on_ublk_face[face][nfv];
    if (fine_ublk->surf_geom_data()->pfluids[vrf_voxel] <= 0.0)
      continue;

    if (!fine_ublk->is_voxel_connected_along_latvec(vrf_voxel, face))
      continue;

    sTAGGED_SPLIT_FACTOR split_facs[MAX_SPLIT_UBLKS];
    asINT32 n_splits =
      tagged_neighbor_from_split_ublk_site(fine_neighbor,
					   fine_advect_from_split_mask,
					   vrf_voxel, face,
					   //vrf_voxel, STATE_P(axis),
					   fine_split_advect_info,
					   fine_ublk->m_split_tagged_instance.get(),
					   split_facs);
    // collecting unique neighbors in neighbor_split_facs
    ccDOTIMES(ns, n_splits) {
      TAGGED_UBLK neighbor  = split_facs[ns].tagged_neighbor;
      BOOLEAN is_split_neighbor_present = FALSE;
      ccDOTIMES(nb, n_neighbor_splits) {
	if (neighbor.ublk() == neighbor_split_facs[nb].tagged_neighbor.ublk()) {
	  is_split_neighbor_present = TRUE;
	  break;
	}
      }
      if (!is_split_neighbor_present) {
	neighbor_split_facs[n_neighbor_splits] = split_facs[ns];
	n_neighbor_splits++;
      }
    }
  }
  return n_neighbor_splits;
}
template <BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
#if !DEBUG
static INLINE
#endif
VOID reset_curr_vr_fine_states(UBLK ublk, SOLVER_INDEX_MASK prior_solver_index_mask) {
  asINT32 curr_lb_index = 1 ^ lb_index_from_mask(prior_solver_index_mask);
  VOXEL_STATE (*ublk_states)[N_VOXELS_8] = ublk->lb_states(curr_lb_index)->m_states;
  if (ublk->are_states_clear() == 0) {
    memset(ublk_states, 0, sUBLK::moving_states_size);
    if (IS_T_S_LB_SOLVER_ON) {
      asINT32 curr_t_index  = 1 ^ t_index_from_mask(prior_solver_index_mask);
      VOXEL_STATE (*ublk_states_t)[N_VOXELS_8] = NULL;
      ublk_states_t = (VOXEL_STATE (*)[N_VOXELS_8]) ublk->t_data()->lb_t_data(curr_t_index)->m_states_t;
      memset(ublk_states_t, 0, sUBLK::moving_states_size);
    }
    if (IS_UDS_LB_SOLVER_ON) {
      asINT32 curr_uds_index  = 1 ^ uds_index_from_mask(prior_solver_index_mask);
      VOXEL_STATE (*ublk_states_uds)[N_VOXELS_8] = NULL;
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
	ublk_states_uds = (VOXEL_STATE (*)[N_VOXELS_8]) ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds;
	memset(ublk_states_uds, 0, sUBLK::moving_states_size);
      }
    }
    ublk->set_states_clear();
  }
}

template<BOOLEAN DO_EXPLODE, BOOLEAN IS_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
VOID vr_fine_advect(UBLK fine_ublk, SOLVER_INDEX_MASK prior_solver_index_mask,
                    ACTIVE_SOLVER_MASK active_solver_mask) {
  sVR_FINE_INTERFACE_DATA *vr_fine_data =  fine_ublk->vr_fine_data();
  UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();


  /* Only done on even time steps, since explode fills in prev states */
#if defined(DEBUG_CONDUCTION_SOLVER)
    if (DEBUG_VR_FINE(fine_ublk) || DEBUG_UBLK(coarse_ublk)) {
      //UBLK mirror_ublk = ublk_from_id(2034,STP_COND_REALM);
      //msg_print("Before explode T %ld U %d mirrorU %d", g_timescale.m_time, fine_ublk->id(), mirror_ublk->id());
      //msg_print("index 0: ublk_state[17][4] = %g", fine_ublk->lb_states(0)->m_states[17][4]);
      //msg_print("index 1: ublk_state[17][4] = %g", fine_ublk->lb_states(1)->m_states[17][4]);
      //msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
      //msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("CU %d FU %d vr_fine_advect before explode", coarse_ublk->id(), fine_ublk->id());
      asINT32 voxel = FINE_VOXEL_OF_INTEREST;
      asINT32 latvec = LATVEC_OF_INTEREST;
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(0)->m_states[latvec][voxel]);
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(1)->m_states[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(0)->m_states_t[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(1)->m_states_t[latvec][voxel]);
    }
#endif
  if (DO_EXPLODE && !fine_ublk->is_exploded()) {
    execute_explode_and_mirror_rule<ADVECT_TEMP, ADVECT_UDS>(coarse_ublk, prior_solver_index_mask, active_solver_mask);
  }
#if defined(DEBUG_CONDUCTION_SOLVER)
    if (DEBUG_VR_FINE(fine_ublk) || DEBUG_UBLK(coarse_ublk)) {
      //UBLK mirror_ublk = ublk_from_id(2034,STP_COND_REALM);
      //msg_print("After explode T %ld U %d mirrorU %d", g_timescale.m_time, fine_ublk->id(), mirror_ublk->id());
      //msg_print("index 0: ublk_state[17][4] = %g", fine_ublk->lb_states(0)->m_states[17][4]);
      //msg_print("index 1: ublk_state[17][4] = %g", fine_ublk->lb_states(1)->m_states[17][4]);
      //msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
      //msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("vr_fine_advect after explode");
      asINT32 voxel = FINE_VOXEL_OF_INTEREST;
      asINT32 latvec = LATVEC_OF_INTEREST;
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(0)->m_states_t[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(1)->m_states_t[latvec][voxel]);
    }
#endif
  /* Only done on even time steps, since explode fills in prev states */
  if (DO_EXPLODE && fine_ublk->has_mirror()) {
    reflect_prev_states_to_mirror_ublk(fine_ublk, prior_solver_index_mask);
  }

  // if (fine_ublk->id() == 21) {
    // print_voxel_states("1: After Explode", fine_ublk->id(), -1, 0,-1);
    // print_voxel_states("2: After Explode", fine_ublk->id(), -1, 0,0);
  // }

  reset_curr_vr_fine_states<ADVECT_TEMP, ADVECT_UDS>(fine_ublk, prior_solver_index_mask);

  // if (fine_ublk->id() == 21) {
    // print_voxel_states("1: After Reset", fine_ublk->id(), -1, 0, -1);
    // print_voxel_states("2: After Reset", fine_ublk->id(), -1, 0, 0);
  // }

#if defined(DEBUG_CONDUCTION_SOLVER)
    if (DEBUG_VR_FINE(fine_ublk) || DEBUG_UBLK(coarse_ublk)) {
      //msg_print("Before gather advect T %ld U %d", g_timescale.m_time, fine_ublk->id());
      //msg_print("ublk_state[17][4] = %g", fine_ublk->lb_states(0)->m_states[17][4]);
      //msg_print("ublk_state[17][4] = %g", fine_ublk->lb_states(1)->m_states[17][4]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("vr_fine_advect before gather adv");
      asINT32 voxel = FINE_VOXEL_OF_INTEREST;
      asINT32 latvec = LATVEC_OF_INTEREST;
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(0)->m_states[latvec][voxel]);
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(1)->m_states[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(0)->m_states_t[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(1)->m_states_t[latvec][voxel]);
    }
#endif
  if (fine_ublk->m_are_any_neighbors_split.any()) {
    gather_advect<TRUE, IS_2D, ADVECT_TEMP, ADVECT_UDS>(fine_ublk, fine_ublk->fluid_like_voxel_mask,
                                            DO_EXPLODE, prior_solver_index_mask,
                                            active_solver_mask);
  } else {

    UBLK active_nbrs[N_MOVING_STATES] = {nullptr};

    collect_advect_neighbors<IS_2D, ADVECT_TEMP, ADVECT_UDS, FALSE /*PDE*/>(fine_ublk,
									    prior_solver_index_mask,
									    DO_EXPLODE,
									    active_nbrs);

    //We cannot use are_states_clear attribute by itself to call
    //different template versions of gather_advect_for_unsplit_neighbors.
#if EXA_USE_AVX && !(BUILD_DOUBLE_PRECISION)
    if (g_use_avx2_advection) {
      ADVECT_VECTORIZED::gather_advect_for_unsplit_neighbors<TRUE, IS_2D, ADVECT_TEMP, ADVECT_UDS, TRUE>(fine_ublk,
													 fine_ublk->fluid_like_voxel_mask,
													 active_nbrs,
													 DO_EXPLODE,
													 prior_solver_index_mask, active_solver_mask);
      
    } else {
      ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<TRUE, IS_2D, ADVECT_TEMP, ADVECT_UDS, TRUE>(fine_ublk,
												     fine_ublk->fluid_like_voxel_mask,
												     active_nbrs,
												     DO_EXPLODE,
												     prior_solver_index_mask, active_solver_mask);
    }
#else
    ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<TRUE, IS_2D, ADVECT_TEMP, ADVECT_UDS, TRUE>(fine_ublk,
												   fine_ublk->fluid_like_voxel_mask,
												   active_nbrs,
												   DO_EXPLODE,
												   prior_solver_index_mask, active_solver_mask);
#endif
  }

  // if (fine_ublk->id() == 21) {
    // print_voxel_states("1: After Advect", fine_ublk->id(), -1, 0, -1);
    // print_voxel_states("2: After Advect", fine_ublk->id(), -1, 0, 0);
  // }
#if defined(DEBUG_CONDUCTION_SOLVER)
    if (DEBUG_VR_FINE(fine_ublk) || DEBUG_UBLK(coarse_ublk)) {
      //msg_print("After gather advect T %ld U %d", g_timescale.m_time, fine_ublk->id());
      //msg_print("ublk_state[17][4] = %g", fine_ublk->lb_states(0)->m_states[17][4]);
      //msg_print("ublk_state[17][4] = %g", fine_ublk->lb_states(1)->m_states[17][4]);
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("vr_fine_advect after gather adv");
      asINT32 voxel = FINE_VOXEL_OF_INTEREST;
      asINT32 latvec = LATVEC_OF_INTEREST;
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(0)->m_states[latvec][voxel]);
      //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state[%d][%d] = %g", latvec,voxel,fine_ublk->lb_states(1)->m_states[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 0 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(0)->m_states_t[latvec][voxel]);
      LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("index 1 state_t[%d][%d] = %g", latvec,voxel,fine_ublk->t_data()->lb_t_data(1)->m_states_t[latvec][voxel]);
    }
#endif

  /* Only done on even time steps */
  if (DO_EXPLODE && fine_ublk->has_mirror()) {
    reflect_curr_states_to_mirror_ublk(fine_ublk, prior_solver_index_mask);
  }
  fine_ublk->unset_states_clear();
#if defined(DEBUG_CONDUCTION_SOLVER)
      if (fine_ublk->id()==UBLK_OF_INTEREST){
	LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("vr_fine_adv: unset_states_clear");
      }
#endif
}

#if BUILD_GPU
namespace GPU {
template<BOOLEAN DO_EXPLODE>
VOID prepare_vr_fine_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                            const size_t* range,
                                            SOLVER_INDEX_MASK prior_solver_index_mask);
}
#endif

template <BOOLEAN DO_EXPLODE>
VOID advect_vr_fine_ublks_internal(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask, 
                                   SOLVER_INDEX_MASK prior_solver_index_mask,
                                   UBLK_GROUP_TYPE group_type,
                                   BOOLEAN is_timestep_even) 
{
#if !BUILD_GPU
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE);
  BOOLEAN is_T_S_lb_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE);
  BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);

  BOOLEAN is_2D = sim.num_dims == 2;

  // Sequence of operations on even time steps
  // Explode previous coarse data onto previous fine data
  // Mirror previous fine data onto previous data in mirror ublks
  // reset current states for VR fine ublks to 0
  // advect into VR fine ublks
  // Mirror current fine data onto current data in mirror ublks

  // Sequence of operations on odd time steps
  // reset current states for VR fine ublks to 0
  // advect into VR fine ublks

  //TODO look at it again for time advancing issues.

  auINT32 odd_timestep_mask = (1 << (sim.num_scales - scale)) - 1;

  if (is_T_S_lb_solver_on && is_2D) {
    if (is_uds_lb_solver_on) {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
          DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif      
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("bv2v", fine_ublk->id(), -1, 0);
          }
#endif
	  vr_fine_advect<DO_EXPLODE, TRUE , TRUE, TRUE >(fine_ublk, prior_solver_index_mask,
							 active_solver_mask);

#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("av2v", fine_ublk->id(), -1, 0);
          }
#endif
	}
      } 
    } else {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif
#ifdef DEBUG_NEXTGEN
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("bv2v", fine_ublk->id(), -1, 0);
	  }
#endif
	  vr_fine_advect<DO_EXPLODE, TRUE , TRUE, FALSE >(fine_ublk, prior_solver_index_mask,
							  active_solver_mask);
#ifdef DEBUG_NEXTGEN
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("av2v", fine_ublk->id(), -1, 0);
	  }
#endif
	}
      }
    }
  } else if (!is_T_S_lb_solver_on && is_2D) {
    if (is_uds_lb_solver_on) {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif 
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("BV2V", fine_ublk->id(), -1, 0);
          }
#endif
	  vr_fine_advect<DO_EXPLODE, TRUE , FALSE, TRUE>(fine_ublk, prior_solver_index_mask,
							 active_solver_mask);
#ifdef debug_nextgen
          if (fine_ublk->id() == 44) {
            print_voxel_states("av2v", fine_ublk->id(), -1, 0);
          }
#endif
	}
      }
    } else {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif
#ifdef DEBUG_NEXTGEN
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("BV2V", fine_ublk->id(), -1, 0);
	  }
#endif

	  vr_fine_advect<DO_EXPLODE, TRUE , FALSE, FALSE>(fine_ublk, prior_solver_index_mask,
							  active_solver_mask);
#ifdef debug_nextgen
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("av2v", fine_ublk->id(), -1, 0);
	  }
#endif
	}
      }
    }
  } else if (is_T_S_lb_solver_on && !is_2D) {
    if (is_uds_lb_solver_on) {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif

#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,BV2V", fine_ublk->id(), -1, 0);
          }
#endif
	  vr_fine_advect<DO_EXPLODE, FALSE, TRUE, TRUE >(fine_ublk, prior_solver_index_mask,
							 active_solver_mask);
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,AV2V", fine_ublk->id(), -1, 0);
          }
#endif
	}
      }
    } else {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif
	  
#ifdef DEBUG_NEXTGEN
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("VRFINE,BV2V", fine_ublk->id(), -1, 0);
	  }
#endif

	  vr_fine_advect<DO_EXPLODE, FALSE, TRUE, FALSE >(fine_ublk, prior_solver_index_mask,
							  active_solver_mask);
#ifdef DEBUG_NEXTGEN
	  if (fine_ublk->id() == 44) {
	    print_voxel_states("VRFINE,AV2V", fine_ublk->id(), -1, 0);
	  }
#endif
	}
      }
    }
  } else {
    if (is_uds_lb_solver_on) {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,BV2V", fine_ublk->id(), -1, 0);
          }
#endif
	  vr_fine_advect<DO_EXPLODE, FALSE, FALSE, TRUE>(fine_ublk, prior_solver_index_mask,
							 active_solver_mask);
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,AV2V", fine_ublk->id(), -1, 0);
          }
#endif
	}
      }
    } else {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
	DO_UBLKS_OF_GROUP(fine_ublk, group) {
#if ENABLE_SIM_COUNTERS
	  if (!fine_ublk->is_fringe_or_fringe2()) {
	    if (fine_ublk->is_near_surface())
	      timer_accum_counters(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, 0, 1);
	    else  
	      timer_accum_counters(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER, 0, 1);      
	  }
#endif
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,BV2V", fine_ublk->id(), -1, 0);
          }
#endif
	  vr_fine_advect<DO_EXPLODE, FALSE, FALSE, FALSE>(fine_ublk, prior_solver_index_mask,
							  active_solver_mask);
#ifdef DEBUG_NEXTGEN
          if (fine_ublk->id() == 44) {
            print_voxel_states("VRFINE,AV2V", fine_ublk->id(), -1, 0);
          }
#endif
	}
      }
    }
  }

  // DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
  //   DO_UBLKS_OF_GROUP(fine_ublk, group) {
  //     print_voxel_states("After VR Fine Advect", fine_ublk->id(),-1,0);
  //   }
  // }
#else
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE);
  BOOLEAN is_T_S_lb_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE);
  BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);

  // Sequence of operations on even time steps
  // Explode previous coarse data onto previous fine data
  // Mirror previous fine data onto previous data in mirror ublks
  // reset current states for VR fine ublks to 0
  // advect into VR fine ublks
  // Mirror current fine data onto current data in mirror ublks

  // Sequence of operations on odd time steps
  // reset current states for VR fine ublks to 0
  // advect into VR fine ublks

  //TODO look at it again for time advancing issues.
  DO_MBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
    if (group->n_shob_ptrs() == 0) {
      continue;
    }

    const auto vr_fine_range = GPU::get_ublk_group_range(group_type, scale, group->m_dest_sp.nsp());

#ifdef DEBUG_GPU_VR
    int debug_ublk_id = 1090;
    int child_ublk_index = 2;
    sHMBLK* h_ublk = find_mblk_in_group(group, debug_ublk_id);
#endif 

#ifdef DEBUG_GPU_VR
    if (h_ublk) {
      print_mblk(h_ublk, child_ublk_index, 0, "1: Before states clear");
      print_mblk(h_ublk, child_ublk_index, -1, "2: Before states clear");
      //print_mblk(h_ublk, child_ublk_index, -1, "2: Before advect");
    }
#endif
    
    if (is_T_S_lb_solver_on) {
      if (is_uds_lb_solver_on)
	GPU::reset_curr_vr_fine_states<TRUE, TRUE>(scale, group_type, vr_fine_range.data(), prior_solver_index_mask, active_solver_mask);
      else
	GPU::reset_curr_vr_fine_states<TRUE, FALSE>(scale, group_type, vr_fine_range.data(), prior_solver_index_mask, active_solver_mask);
    }
    else {
      if (is_uds_lb_solver_on)
	GPU::reset_curr_vr_fine_states<FALSE, TRUE>(scale, group_type, vr_fine_range.data(), prior_solver_index_mask, active_solver_mask);
      else
	GPU::reset_curr_vr_fine_states<FALSE, FALSE>(scale, group_type, vr_fine_range.data(), prior_solver_index_mask, active_solver_mask);
    }

#ifdef DEBUG_GPU_VR
    if (h_ublk) {
      print_mblk(h_ublk, child_ublk_index, 0, "1: Before advect");
      print_mblk(h_ublk, child_ublk_index, -1, "2: Before advect");
      //print_mblk(h_ublk, child_ublk_index, -1, "2: Before advect");
    }
#endif
    
    GPU::gather_advect(group_type, is_T_S_lb_solver_on, is_uds_lb_solver_on,
                             vr_fine_range.data(), is_timestep_even,
                             prior_solver_index_mask, active_solver_mask);

#ifdef DEBUG_GPU_VR
    if (h_ublk) {
      print_mblk(h_ublk, child_ublk_index, 0, "1: After advect");
      print_mblk(h_ublk, child_ublk_index, -1, "2: After advect");
    }
#endif
  
    GPU::prepare_vr_fine_ublk_group_for_next_ts<DO_EXPLODE>(group_type,
                                                            vr_fine_range.data(),
                                                            prior_solver_index_mask);

  }

#endif
}

template VOID advect_vr_fine_ublks_internal<TRUE>(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask,
                                                  SOLVER_INDEX_MASK prior_solver_index_mask,
                                                  UBLK_GROUP_TYPE group_type,
                                                  BOOLEAN is_timestep_even);

template VOID advect_vr_fine_ublks_internal<FALSE>(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask, 
                                                   SOLVER_INDEX_MASK prior_solver_index_mask,
                                                   UBLK_GROUP_TYPE group_type,
                                                   BOOLEAN is_timestep_even);

#if !BUILD_GPU
template VOID execute_coalesce_rule<sUBLK> (UBLK coarse_ublk,
                                            SOLVER_INDEX_MASK prior_solver_index_mask,
                                            ACTIVE_SOLVER_MASK active_solver_mask);
#endif

VOID reset_post_advect_scale_factors_for_diagonal_coalesced_states(UBLK coarse_ublk,
                                                                   STP_GEOM_VARIABLE (*pas_factors)[N_VOXELS_8])
{
  sVR_COARSE_INTERFACE_DATA *vr_coarse_data =  coarse_ublk->vr_coarse_data();
  VR_CONFIG config = vr_coarse_data->vr_config();
  asINT32 n_fine_blks = N_VOXELS_8;

  for (asINT32 fine_blk_idx = 0; fine_blk_idx < n_fine_blks; fine_blk_idx++) {

    //asINT32 fine_blk_idx = coarse_voxel;
    UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
    if (vr_fine_ublk_is_invalid(fine_ublk,0))
      continue;

    asINT32 coarse_voxel = fine_blk_idx;
    const cVR_STATES& states = config->coalesce_states[fine_blk_idx];
    /* Loop over states */
    ccDO_VR_STATES(state_index, states) {
      // setting post advect scale factor for coarse ublks to 0 so that advection does not contribute
      if ((state_vx(state_index) && state_vy(state_index)) ||
          (state_vx(state_index) && state_vz(state_index)) ||
          (state_vy(state_index) && state_vz(state_index))) {
        pas_factors[state_index][coarse_voxel] = 0.0;
      }
    } /* state loop */
  } /* Fine microblock loop */
}
