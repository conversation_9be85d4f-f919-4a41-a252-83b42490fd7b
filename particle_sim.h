#ifndef _PARTICLE_SIM_H_
#define _PARTICLE_SIM_H_

#include SP_H
#include SIMUTILS_H
#include "common_sp.h"
#include "particle_solver_data.h"
#include "virtual_wipers.h"
#include "shob_octree.h"
#include "vr.h"
#include "surfel_dyn_sp.h"
#include "surfel_advect_sp.h"
#include "surfel_vertices.h"
#include "particle_materials.h"


typedef struct sPARTICLE_SCREEN {
  std::string m_name;
  dFLOAT  opening_size;
  dFLOAT  pass_thru_fraction;
  asINT32 surface_material;
  dFLOAT measured_fraction;
} *PARTICLE_SCREEN;

extern sSIMUTILS_PARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE g_terminal_velocity_table;
extern sPARTICLE_VAR g_particle_lattice_time_correction;
extern sPARTICLE_VAR g_particle_one_over_lattice_time_correction;
extern sPARTICLE_VAR g_particle_density_scale;

extern sPARTICLE_VAR g_sp_lost_particle_mass;
extern sPARTICLE_VAR g_sp_emitted_particle_mass;
extern sPARTICLE_VAR g_sp_evaporated_particle_mass;
extern asINT32 g_sp_evaporated_particles;
extern asINT32 g_num_thermal_timesteps;
extern asINT32 g_num_momentum_timesteps;

#if DEBUG_PARTICLE_SUMMARY
extern asINT32 g_emitted_parcel_count;
extern asINT32 g_parcel_dynamics_count;
extern asINT32 g_parcel_massless_tracer_dynamics_count;
extern asINT32 g_accumulated_parcel_count;
extern asINT32 g_reentrained_parcel_count;
extern asINT32 g_film_dynamics_count;
extern asINT32 g_surface_parcel_dynamics_count;
extern asINT32 g_parcel_mlrf_film_collision_count;
extern asINT32 g_parcel_screen_collision_count;
extern asINT32 g_numerically_reentrained_parcel_count;
extern asINT32 g_parcel_collision_count;
extern asINT32 g_parcel_bsurfel_collision_count;
extern asINT32 g_parcel_sample_surface_collision_count;
extern asINT32 g_parcel_sample_surface_film_collision_count;
extern asINT32 g_parcel_screen_collision_count;
extern asINT32 g_parcel_mlrf_collision_count;
extern asINT32 g_surface_parcel_send_count;
extern asINT32 g_fluid_parcel_send_count;
extern asINT32 g_surface_parcel_receive_count;
extern asINT32 g_fluid_parcel_receive_count;
#endif

typedef class sPARTICLE_SIM {
  friend class sTIRE_EMITTER;
  template<typename> friend class tCONFIGURABLE_EMITTER_BASE;

 public:
  sPARTICLE_SIM(VOID);
  ~sPARTICLE_SIM(VOID);

  // Initialization functions
  VOID set_film_solver_status();
  VOID set_thermal_particle_solver_status();
  asINT32 initialize();
  VOID log_debug_summary();
  VOID compute_film_solver_stencils_and_create_film_only_ghosts();
  VOID send_queued_stencil_sim_errors();
  VOID compute_curvature(SURFEL surfel);
  VOID compute_curvature_of_surfels();
  VOID find_surfels_that_intersect_mlrf_interfaces();
  VOID find_surfels_that_intersect_sampling_surfaces();
  VOID check_surfel_neighbor(); 
  VOID init_parcel_ublk_check();
  VOID init_parcel_ublk_check(UBLK ublk);
  asINT32 uninitialize(VOID);

  VOID update_all_virtual_wiper_positions();

  // release parcels and perform parcel dynamics functions in a voxel
  VOID parcel_dynamics(UBLK_GROUP_TYPE ublk_group_type, SCALE scale);
  asINT32 particle_dynamics(UBLK ublk);
  asINT32 parcel_dynamics(UBLK ublk, asINT32 voxel);
  asINT32 parcel_dynamics(UBLK ublk, asINT32 voxel, VOXEL_GRADS grads, sPARTICLE_VAR min_part_diam);
  asINT32 explicit_integrator_parcel_dynamics(UBLK ublk, asINT32 voxel, VOXEL_GRADS grads);
  asINT32 explicit_integrator_parcel_dynamics(UBLK ublk, asINT32 voxel, VOXEL_GRADS grads, sPARTICLE_VAR min_part_diam);
  asINT32 first_order_integrator_parcel_dynamics(UBLK ublk, asINT32 voxel);
  VOID parcel_breakup(UBLK ublk, asINT32 voxel);
  VOID release_parcels();
  std::string truncate_error_message(std::string &err_msg);

  asINT32 move_parcels_to_new_container(UBLK ublk, asINT32 voxel);
  VOID move_parcels_to_new_container();
  template <typename FLOAT_TYPE>
  static bool is_position_inside_ublk(FLOAT_TYPE position[3], UBLK ublk, asINT32 &voxel);
  template <typename FLOAT_TYPE>
    UBLK find_containing_neighbor_ublk(FLOAT_TYPE position[3], SURFEL surfel, asINT32 &home_voxel);
  
  // surface collision functions
  enum eCOLLISION_RETURN_TYPE {
    IN,
    OUT,
    NEITHER,
    REMOVE_FROM_LIST
  };

  VOID surface_collision();
  VOID surface_collision(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
  VOID mlrf_surface_collision(SCALE scale);
  VOID sampling_surface_collision(SURFEL_BASE_GROUP_TYPE surfel_group_type,
                                  SCALE scale);
  VOID ghost_sampling_surface_collision(SCALE scale);
  VOID ghost_surface_collision(SCALE scale);
  VOID bsurfel_surface_collision(SCALE scale);

  VOID surface_collision(sSURFEL *surfel);
  VOID lrf_surface_collision(sSURFEL *surfel);
  VOID screen_collision(SAMPLING_SURFEL surfel);
  VOID surface_collision(SAMPLING_SURFEL surfel);
  VOID surface_collision(UBLK ublk, asINT32 voxel, sBSURFEL *bsurfel, PARTICLE_VAR bsurfel_velocity);
  template <typename SURFEL_TYPE>
  eCOLLISION_RETURN_TYPE surface_shob_collision_detection_internal(SURFEL_TYPE surfel,
                                                                   UBLK ublk,
                                                                   asINT32 voxel,
                                                                   asINT32 scale,
                                                                   sPARCEL_STATE * parcel, 
                                                                   sPARTICLE_VAR * x_minus,
                                                                   sPARTICLE_VAR & t_minus,
                                                                   sPARTICLE_VAR tolerance,
                                                                   BOOLEAN allow_backside_collision = TRUE);

  template <typename SURFEL_TYPE>
  VOID solid_particle_reflection(UBLK ublk, SURFEL_TYPE surfel, sPARCEL_STATE * parcel, 
                                 sPARCEL_LIST * parcel_list, sPARTICLE_VAR t_minus, 
                                 sPARTICLE_VAR * v_minus, sPARTICLE_VAR * x_minus, 
                                 sPARTICLE_VAR * wall_velocity,
                                 BOOLEAN & parcel_was_removed_from_list, 
                                 asINT32 surface_material_id, asINT32 particle_material_id, 
                                 BOOLEAN collision_with_screen,
                                 dFLOAT override_measured_fraction = -1);

  VOID wait_for_parcels_tobe_copied_to_send_buffer();
  VOID complete_parcel_recvs();

  // Accumulation of film
  VOID clear_film(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
  VOID clear_ghost_film(SCALE scale);
  VOID accumulate_film(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);

  asINT32 accumulate_film(SURFEL surfel); //This is a wrapper for one of the following:
  asINT32 accumulate_film_faster(SURFEL surfel);
  asINT32 accumulate_film_per_parcel(SURFEL surfel);


  // film dynamics and kinematics functions
  VOID film_dynamics(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
  asINT32 film_dynamics(SURFEL surfel);

  asINT32 move_parcels_on_surface(SURFEL surfel);
  VOID aggregate_surface_parcels(SURFEL surfel);

  BOOLEAN breakup_split_condition(PARCEL_STATE parcel);

  VOID film_breakup(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
  asINT32 film_breakup(SURFEL surfel);

  VOID film_kinematics(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);
  VOID film_measurements(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale);

  BOOLEAN reentrainment_criteria_is_satisfied(SURFEL surfel,
                                              sPARTICLE_VAR film_thickness_already_removed,
                                              PARCEL_STATE parcel,
                                              sPARTICLE_VAR minimal_capillary_length,
                                              sPARTICLE_VAR user_specified_reentrainment_length);


  VOID compute_film_contact_line_forces();
  VOID surfel_compute_film_contact_line_forces(SURFEL surfel);

  VOID redistribute_film_contact_line_forces();
  VOID surfel_redistribute_film_contact_line_forces(SURFEL surfel);

  VOID ghost_sampling_surface_measurement(SCALE scale);
  VOID record_surface_measurements(SURFEL surfel, SCALE scale);
  VOID record_surface_measurements(SAMPLING_SURFEL surfel, SCALE scale);

  VOID process_parcels_near_symmetry_plane(SCALE scale);

  size_t ckpt_len();
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID write_ckpt();

  // Communication related functions in particle_comm.cc
  VOID queue_parcel_sends();
  VOID send_parcels_before_ckpt();
  VOID do_film_comm_post_recv(SCALE scale);
  VOID do_film_comm_send(SCALE scale);
  VOID do_film_comm_accumulate_recv_buffers(SCALE scale);

  BOOLEAN is_timestep_before_accumulation() {
    if ((g_n_accumulation_base_steps == 1 && g_particle_subcycling_info.is_active) ||
        (g_n_accumulation_base_steps > 1 && (g_timescale.time_flow() + 2) % g_n_accumulation_base_steps == 0))
      return true;
    else
      return false;
  }

  BOOLEAN is_timestep_for_accumulation() {
    if ((g_n_accumulation_base_steps == 1 && g_particle_subcycling_info.is_active) ||
        (g_n_accumulation_base_steps > 1 && (g_timescale.time_flow() + 1) % g_n_accumulation_base_steps == 0))
      return true;
    else
      return false;
  }

  BOOLEAN is_timestep_after_accumulation() {
    if ((g_n_accumulation_base_steps == 1 && g_particle_subcycling_info.is_active) ||
        (g_n_accumulation_base_steps > 1 && (g_timescale.time_flow() + 1) % g_n_accumulation_base_steps == 1))
      return true;
    else
      return false;
  }

  VOID destroy_parcel(PARCEL_STATE parcel_to_delete, BOOLEAN deleted_unexpectedly);
  BOOLEAN destroy_parcel(
                         UBLK ublk,
                         sPARCEL_STATE *particle_to_delete,
                         BOOLEAN has_already_had_pmr_handled,
                         BOOLEAN deleted_unexpectedly);

  BOOLEAN destroy_parcel(
                         SURFEL surfel,
                         PARCEL_STATE parcel_to_delete,
                         BOOLEAN has_already_had_pmr_handled,
                         BOOLEAN deleted_unexpectedly);

  BOOLEAN destroy_parcel_on_list(UBLK ublk,
                                 sPARCEL_LIST *parcel_list_to_delete_from,
                                 BOOLEAN has_already_had_pmr_handled,
                                 BOOLEAN deleted_unexpectedly);

  BOOLEAN destroy_parcel_on_list(
                                 SURFEL surfel,
                                 PARCEL_LIST parcel_list_to_delete_from,
                                 BOOLEAN has_already_had_pmr_handled,
                                 BOOLEAN deleted_unexpectedly);

  static sPARTICLE_VAR limit_delta_t_in_range(sPARTICLE_VAR t, SCALE scale);

 private:

  PARCEL_STATE add_parcel(asINT32 emission_box_id,
                          asINT32 parent_parcel_id,
                          sPARTICLE_VAR num_particles_in_new_parcel,
                          sPARTICLE_VAR *x, PARTICLE_VAR v,
                          sPARTICLE_VAR diameter,
                          sPARTICLE_VAR particle_density,
                          sPARTICLE_VAR unevaporated_mass,
                          sPARTICLE_VAR particle_surface_tension,
                          sPARTICLE_VAR particle_kinematic_viscosity,
                          asINT32 num_initial_reflections,
                          BOOLEAN interpret_velocity_as_drift,
                          asINT32 particle_aggl_applied,
                          eDYNAMICS_TYPE dynamics_type,
                          UBLK ublk, asINT32 voxel);

  PARCEL_STATE add_parcel(const sPARCEL_STATE &parcel, SURFEL surfel);

  inline sPARTICLE_VAR lattice_time_correction() {return g_particle_lattice_time_correction;}
  inline sPARTICLE_VAR one_over_lattice_time_correction() {return g_particle_one_over_lattice_time_correction;}
  

  BOOLEAN update_parcel_container(sPARCEL_LIST*current_parcel_list,
                                  UBLK init_ublk,
                                  asINT32 previous_voxel,
                                  UBLK &new_ublk,
                                  asINT32 &new_voxel);



  VOID compute_ref_frame_accel(LRF_PHYSICS_DESCRIPTOR lrf,
                               asINT32 scale,
                               sPARTICLE_VAR at_position[3],
                               sPARTICLE_VAR local_velocity[3],
                               sPARTICLE_VAR ref_frame_acceleration[3]);

  VOID maybe_send_final_vertex(UBLK ublk, PARCEL_STATE parcel, BOOLEAN already_had_a_hitpoint);

  VOID interrupt_trace_for_parcel_crossing_a_periodic_bc(UBLK ublk, 
                                                         PARCEL_STATE parcel, 
                                                         sPARTICLE_VAR old_position[N_SPACE_DIMS]);

  VOID interrupt_trace_for_a_breakup( UBLK ublk,
                                                 PARCEL_STATE parcel,    
                                                 std::vector<PARCEL_STATE> &child_parcels);
  
  VOID interrupt_trace_due_to_a_discontinuity(UBLK ublk,
                                              PARCEL_STATE parent_parcel,
                                              PARTICLE_EVENT_TYPE event_type,
                                              std::vector<PARCEL_STATE> &child_parcels,
                                              std::vector<PARTICLE_EVENT_TYPE> &child_source_flags);

  // Measurement related functions
  VOID measure_trajectory_hitpoint(UBLK ublk,
                                   PARCEL_STATE parcel,
                                   sPARTICLE_VAR fractional_time_of_impact,
                                   asINT32 surface_index,
                                   sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                   sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                   STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                   PARTICLE_EVENT_TYPE event_type);

  VOID measure_trajectory_reflection(UBLK ublk,
                                     PARCEL_STATE parcel,
                                     sPARTICLE_VAR fractional_time_of_impact,
                                     asINT32 surface_index,
                                     sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                     sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                     sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                     STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                     PARTICLE_EVENT_TYPE event_type,
                                     dFLOAT override_measurable_fraction = 0);

  VOID measure_trajectory_screen_passthrough(UBLK ublk,
                                             PARCEL_STATE parcel,
                                             sPARTICLE_VAR fractional_time_of_impact,
                                             asINT32 surface_index,
                                             sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                             sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                             sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                             STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                             PARTICLE_EVENT_TYPE event_type,
                                             dFLOAT override_measurable_fraction = 0);

  VOID measure_trajectory_splash(UBLK ublk,
                                 PARCEL_STATE parent_parcel,
                                 sPARTICLE_VAR fractional_time_of_impact,
                                 asINT32 surface_index,
                                 sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                 sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                 sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                 STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                 PARTICLE_EVENT_TYPE event_type,
                                 std::vector<PARCEL_STATE> &child_parcels,
                                 std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                                 dFLOAT override_measurable_fraction = 0);

  VOID measure_trajectory_vertex(UBLK ublk,
                                 PARCEL_STATE parcel,
                                 PARTICLE_EVENT_TYPE event_type,
                                 BOOLEAN force_measurement);
  VOID measure_trajectory_split_event(UBLK ublk, PARCEL_STATE parcel, PARTICLE_EVENT_TYPE event_type,
                                      std::vector<PARCEL_STATE> &child_parcels,
                                      std::vector<PARTICLE_EVENT_TYPE> &child_source_flags);
  VOID measure_trajectory_merge_event(UBLK ublk, PARCEL_STATE parcel, PARTICLE_EVENT_TYPE event_type,
                                      std::vector<PARCEL_STATE> &parent_parcels);


  template <typename SURFEL_TYPE>
    bool find_home_ublk(SURFEL_TYPE surfel, UBLK &home_ublk, sINT32 &home_voxel);
  VOID assign_neighbor_ublks();
  template <typename SURFEL_TYPE>
    VOID assign_neighbor_ublks(SURFEL_TYPE surfel);

  VOID flag_surfels_wiped_by_virtual_wiper_models();
  BOOLEAN surfel_is_near_any_virtual_wipers(SURFEL surfel,
                                            PARTICLE_VAR blade_to_voxel_centroid_dist,
                                            PARTICLE_VAR blade_normal_direction,
                                            PARTICLE_VAR local_blade_velocity,
                                            PARTICLE_VAR blade_midpoint);
  sPARTICLE_VAR compute_explicit_terms_for_surface_film(PARTICLE_VAR inplane_gravity,
                                                        PARTICLE_VAR ref_frame_accel,
                                                        PARTICLE_VAR wall_velocity,
                                                        sPARTICLE_VAR film_thickness,
                                                        PARTICLE_VAR film_velocity,
                                                        sPARTICLE_VAR film_density,
                                                        sPARTICLE_VAR film_kinematic_vicosity,
                                                        sPARTICLE_VAR wettedness,
                                                        PARTICLE_VAR shear_stress,
                                                        PARTICLE_VAR v_dot);
  VOID compute_equilibrium_film_velocity(LRF_PHYSICS_DESCRIPTOR lrf,
                                         ANGULAR_MOVING_SURFEL_SPEC angular_spec,
                                         SURFEL surfel,
                                         PARTICLE_VAR inplane_gravity,
                                         PARTICLE_VAR wall_velocity,
                                         sPARTICLE_VAR film_thickness,
                                         sPARTICLE_VAR film_density,
                                         sPARTICLE_VAR film_kinematic_vicosity,
                                         PARTICLE_VAR shear_stress,
                                         PARTICLE_VAR tangential_particle_momentum_flux,
                                         PARTICLE_VAR equilibrium_velocity);
  VOID surface_tension_model_parcel_kinematics(sPARTICLE_VAR local_timestep,
                                               PARCEL_STATE parcel,
                                               sPARTICLE_VAR film_velocity[N_SPACE_DIMS],
                                               sPARTICLE_VAR film_thickness,
                                               sPARTICLE_VAR film_mass,
                                               sPARTICLE_VAR surfel_area,
                                               sPARTICLE_VAR surface_tension_acceleration[N_SPACE_DIMS],
                                               sPARTICLE_VAR degennes_length,
                                               sPARTICLE_VAR t_st_life,
                                               asINT32 scale);
  VOID splash_parcel(asINT32 scale,
                     UBLK ublk,
                     asINT32 voxel,
                     PARCEL_STATE parcel,
                     std::vector<PARCEL_STATE> &child_parcels,
                     std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                     SURFEL surfel, 
                     SURFACE_INTERACTION_PROPERTY surface_interaction_params);
  VOID splash_parcel(asINT32 scale,
                     UBLK ublk,
                     asINT32 voxel,
                     PARCEL_STATE parcel,
                     std::vector<PARCEL_STATE> &child_parcels,
                     std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                     SAMPLING_SURFEL sample_surfel);
  VOID splash_parcel(asINT32 scale,
                     UBLK ublk,
                     asINT32 voxel,
                     PARCEL_STATE parcel,
                     std::vector<PARCEL_STATE> &child_parcels,
                     std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                     sBSURFEL *bsurfel,
                     PARTICLE_VAR bsurfel_velocity);
  VOID splash_parcel_internal(asINT32 scale,
                              UBLK ublk,
                              asINT32 voxel,
                              PARCEL_STATE parcel,
                              std::vector<PARCEL_STATE> &child_parcels,
                              std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                              sPARTICLE_VAR film_thickness,
                              sPARTICLE_VAR wall_velocity[3],
                              auINT64 &num_splashed,
                              sPARTICLE_VAR &mass_splashed,
                              sPARTICLE_VAR &sum_diameter_splashed,
                              sPARTICLE_VAR &sum_density_splashed,
                              asINT32 surfel_scale,
                              STP_GEOM_VARIABLE normal[3],
                              STP_GEOM_VARIABLE centroid[3],
                              sPARTICLE_VAR any_surfel_vertex[3],
                              BOOLEAN force_mass_fraction_one,
                              BOOLEAN mirror_cone_angle);
   public:
  double convert_dimensional_parameter(double input_value, std::string input_unit_string, std::string output_unit_string);
  sPARTICLE_VAR evaluate_particle_vapor_phase_saturation_density(sPARTICLE_VAR temperature);
  sPARTICLE_VAR evaluate_particle_vapor_phase_saturation_density_derivative(sPARTICLE_VAR temperature);
  sPARTICLE_VAR compute_wetbulb_temp(sPARTICLE_VAR temperature_guess, sPARTICLE_VAR air_temperature, sPARTICLE_VAR air_density, sPARTICLE_VAR ambient_vapor_density);
  sPARTICLE_VAR compute_local_evaporation_rate(PARCEL_STATE parcel, sPARTICLE_VAR air_temperature, sPARTICLE_VAR air_density, sPARTICLE_VAR ambient_vapor_density);
  sPARTICLE_VAR compute_local_evaporation_rate(
                                               int component_index,
                                               sPARTICLE_VAR mass_fractions[NUM_PARTICLE_MATERIAL_SPECIES - 1],
                                               sPARTICLE_VAR diameter, 
                                               sPARTICLE_VAR surface_temperature, 
                                               sPARTICLE_VAR air_temperature, 
                                               sPARTICLE_VAR ambient_vapor_density);

  // Utility functions
  //UBLK traverse_lattice(UBLK start_ublk, PARTICLE_VAR displacement, asINT32 recursion_depth);
  BOOLEAN position_is_inside_dispersion_box(LRF_PHYSICS_DESCRIPTOR lrf, sPARTICLE_VAR x[3]);
  BOOLEAN position_is_outside_dispersion_box(LRF_PHYSICS_DESCRIPTOR lrf, sPARTICLE_VAR x[3]);

//  sPARTICLE_VAR evaluate_particle_vapor_phase_saturation_pressure_mks(sPARTICLE_VAR temperature);
  VOID compute_lattice_time_correction_factor(UNITS_DB units_db);
  VOID sample_surface_collision_detection();
  VOID accumulate_film_properties(STP_PHASE phase,
                                  UBLK nearblk,
                                  asINT32 voxel,
                                  asINT32 is_coarse_ublk_vr_interface);

  VOID terminal_velocity(PARTICLE_VAR local_gravity_vector,
                         sPARTICLE_VAR rho_air,
                         sPARTICLE_VAR kinematic_viscosity_of_air,
                         sPARTICLE_VAR rho_particle,
                         sPARTICLE_VAR particle_diameter,
                         PARTICLE_VAR terminal_velocity_vector);
  void accumulate_parcel_data(PARCEL_STATE parcel, 
                              SAMPLING_SURFEL surfel, 
                              int dir);

  template <typename SURFEL_TYPE>
    sPARTICLE_VAR compute_normal_distance_to_centroid(sPARTICLE_VAR x[3], SURFEL_TYPE surfel);
  template <typename SURFEL_TYPE>
    sPARTICLE_VAR position_is_within_surfel_perimeter(SURFEL_TYPE surfel, PARTICLE_VAR position);

}*PARTICLE_SIM;

extern sPARTICLE_SIM particle_sim;
VOID compute_rain_emitter_emission_rates();


class sDEBUG_BOX {
  private:
  double m_2pt_box[2][3];

 public:
 sDEBUG_BOX(double x[3], double tolerance) :
  sDEBUG_BOX(
             x[0] - tolerance, x[1] - tolerance, x[2] - tolerance,
             x[0] + tolerance, x[1] + tolerance, x[2] + tolerance)
    {}

 sDEBUG_BOX(double x[2][3]) :
  sDEBUG_BOX(x[0], x[1])  {}

 sDEBUG_BOX(double x1[3], double x2[3]) :
  sDEBUG_BOX(x1[0], x1[1], x1[2], x2[0], x2[1], x2[2])  {}

  sDEBUG_BOX(
             double x1, double y1, double z1,
             double x2, double y2, double z2) {
    m_2pt_box[0][0] = std::min(x1, x2);
    m_2pt_box[0][1] = std::min(y1, y2);
    m_2pt_box[0][2] = std::min(z1, z2);
    m_2pt_box[1][0] = std::max(x1, x2);
    m_2pt_box[1][1] = std::max(y1, y2);
    m_2pt_box[1][2] = std::max(z1, z2);
  }

  template <typename VAR_TYPE>
    bool contains(VAR_TYPE x[3]) {
    return contains(x[0], x[1], x[2]);
  }

  template <typename VAR_TYPE>
    bool spans_in_x(VAR_TYPE x) {
    return spans_dim(0, x);
  }

  template <typename VAR_TYPE>
    bool spans_in_y(VAR_TYPE y) {
    return spans_dim(1, y);
  }

  template <typename VAR_TYPE>
    bool spans_in_z(VAR_TYPE z) {
    return spans_dim(2, z);
  }


  template <typename VAR_TYPE>
    bool spans_dim(int dim, VAR_TYPE x) {
    if(x < m_2pt_box[0][dim] ||
       x > m_2pt_box[1][dim])
      return false;
    return true;
  }

  template <typename VAR_TYPE>
    bool contains(VAR_TYPE x, VAR_TYPE y, VAR_TYPE z) {
    if(spans_in_x(x) && spans_in_y(y) && spans_in_z(z) )
      return true;
    return false;
  }

};



template <typename DATA_TYPE, typename LARGEST_FUNDAMENTAL_DATA_TYPE>
class tAUX_DEBUG {
public:
  static std::string stream_bits(const DATA_TYPE x) {
    int max_bits = sizeof(DATA_TYPE);
    if(max_bits > sizeof(LARGEST_FUNDAMENTAL_DATA_TYPE))
      return "..too many bits..";

    if(x == 0)
      return "0";

    DATA_TYPE x_temp = x;
    LARGEST_FUNDAMENTAL_DATA_TYPE data = 0;
    memcpy(&data, &x_temp, sizeof(DATA_TYPE));
    
    std::stringstream bits;
    while(data > 0) {
      bits << ((data & 0x1) ? "1" : "0");
      //printf("data %llu result %s.\n", data, bits.str().c_str());
      data = data >> 1;
    }
    
    std::string result = bits.str();
    std::reverse(result.rbegin(), result.rend());
    return result;
  }
};
template <typename DATA_TYPE>
constexpr auto convert_to_string_in_binary = &tAUX_DEBUG<DATA_TYPE, unsigned long long int>::stream_bits;


#endif
