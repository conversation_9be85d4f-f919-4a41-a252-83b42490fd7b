/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef GPU_SHOBS_H
#define GPU_SHOBS_H

#if GPU_COMPILER
#include "cuda.h"
#endif
#include "ublk.h"

namespace GPU {
  
  using sUBLK = tUBLK<MBLK_SDFLOAT_TYPE_TAG>; using UBLK = sUBLK*;
  static_assert(sizeof(GPU::sUBLK) == sizeof(::sMBLK), "Static size of HOST and DEVICE UBLKs must match.");

  using sUBLK_BOX = tUBLK_BOX<MBLK_SDFLOAT_TYPE_TAG>; using UBLK_BOX = sUBLK_BOX*;
  using sBOX_ACCESS = tUBLK_BOX_ACCESS<MBLK_SDFLOAT_TYPE_TAG>; using BOX_ACCESS = sBOX_ACCESS*;

  // using sUBLK_BOX_PLUS = tUBLK_BOX_PLUS<MBLK_SDFLOAT_TYPE_TAG>; using UBLK_BOX_PLUS = sUBLK_BOX_PLUS*;
  using sUBLK_UBFLOAT = tUBLK<MBLK_UBFLOAT_TYPE_TAG>;
  using UBFLOAT_UBLK  = sUBLK_UBFLOAT*;

  using TAGGED_UBLK = tTAGGED_UBLK<MBLK_SDFLOAT_TYPE_TAG>;

  static_assert(sizeof(GPU::TAGGED_UBLK) == sizeof(::TAGGED_UBLK), "Static size of HOST and DEVICE TAGGED_UBLKs must be the same");

  using sSURFEL = tSURFEL<MSFL_SDFLOAT_TYPE_TAG>; using SURFEL = sSURFEL*;

  using sSCALE_BOX_INTERFACE = tSCALE_BOX_INTERFACE<MBLK_SDFLOAT_TYPE_TAG>;
  using SCALE_BOX_INTERFACE = sSCALE_BOX_INTERFACE*;

}

#endif
