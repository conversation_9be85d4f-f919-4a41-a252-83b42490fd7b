/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include	"common_sp.h"
#include	"sim.h"
#include	"eqns.h"
#include	"simerr.h"
#include        "shob_groups.h"
#include	"thread_run.h"
#include	"comm_groups.h"
#include	"strand_mgr.h"

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include <bitset>
//#endif

// This flag was created to allow errors to be suppressed during the calls to
// to SURFEL_DYN_GROUP::dynamics inside surfel seeding.
cBOOLEAN simerr_suppress_errors = FALSE;

static asINT32 cp_error_count = 0;
static asINT32 cp_error_stream_send_count = 0;
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
//additional film and particle simerror types exceeded the 64bit limit
static std::bitset<SP_EER_NUMERRS>disabled_errors_mask;
#else
static auINT64 disabled_errors_mask[] = {0x0UL, 0x0UL};
#endif
static MPI_Datatype eMPI_cp_ack_sp_errs_type;
static sCHAR_FIFO mpi_queue(16 * 1024);

/*--------------------------------------------------------------------------*
 * Generate warning/error to send up-stream to the CP
* Returns TRUE if there are still errors remaining to process
*--------------------------------------------------------------------------*/

BOOLEAN is_simerr_queue_empty() {
 asINT32 curr_size = mpi_queue.readyDataSize();
 return (curr_size <= 0);
}



BOOLEAN simerr_mpi_process(BOOLEAN probe_p)
{

  asINT32 curr_size = mpi_queue.readyDataSize();
  unsigned char msg_size;

  if (curr_size > 0) {
    mpi_queue.peekBytes((char *) &msg_size, 1);

    while (curr_size > msg_size) { // make sure the msg plus the size byte are available

      /* If we're due for an ack, see if there's one present */
      if (cp_error_stream_send_count >= cp_error_count) {
	sCP_ACK_SP_ERRS_INFO_MSG ack_msg;
	MPI_Status status;

        int new_request;

        // probe_p == FALSE implies that Iprobe has already been called and returned TRUE 

        if(probe_p) {
          MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_CP_ACK_SP_ERRS_INFO_TAG,
                     eMPI_sp_cp_comm, &new_request, &status);
        } else {
          new_request = 1;
        }

        /* If we got an ack, receive it */
        if (new_request) {
          RECV_EXA_SIM_MSG<sCP_ACK_SP_ERRS_INFO_MSG, 1> recv_ack_msg(eMPI_CP_ACK_SP_ERRS_INFO_TAG, eMPI_sp_cp_rank());
          g_exa_sp_cp_comm.recv(recv_ack_msg.mpi_msg);
          ack_msg = *recv_ack_msg.return_buffer();
          cp_error_count = ack_msg.acceptable_message_count;
	  cp_error_stream_send_count = 0;
        }
        /* If we didn't get an ack, can't do anything else here for now */
        else {
          return(mpi_queue.readyDataSize() > 0);
        }
      }

      CHARACTER msgBuf[SP_EER_MAXPRMSTR * 2];
      mpi_queue.retrieveBytes(msgBuf, msg_size + 1);

      // Don't send msg_size inside message (thus send msgBuf + 1)
      MPI_Send(msgBuf + 1, msg_size, eMPI_EER_PRMSTR, eMPI_sp_cp_rank(),
	       eMPI_SP_ERROR_TAG, eMPI_sp_cp_comm);

      cp_error_stream_send_count++;

      curr_size = mpi_queue.readyDataSize();

      if (curr_size > 0) {
        mpi_queue.peekBytes((char *) &msg_size, 1);
      } else {
        msg_size = 0;
      }
    }
  }

  return(mpi_queue.readyDataSize() > 0);
}

static VOID
simerr_generate_error_sfloat(BASETIME step, SP_EEP_TYPES error_code, asINT32 _scale,
		      sFLOAT *point, va_list ap) 
{
  asINT32 scale = sim.num_scales - _scale - 1;

  /* If this error type suppressed, dump it right here */
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  if(disabled_errors_mask[error_code])
    return;
#else
  if (((auINT64) 0x01 << (auINT64) error_code) & disabled_errors_mask)
    return;
#endif
  char full_format[8 * 1024]; // ample space
  spGenerateSpErrorPacketSingle(full_format, step, error_code, scale, point, ap);

  unsigned char fmtlen = (unsigned char) strlen(full_format);
  while (mpi_queue.availableSpace() < (fmtlen + 1)) {
    if (!g_strand_mgr.m_comm_thread_started) {
      simerr_mpi_process();
    }
    sp_thread_sleep(MPI_SLEEP_LONG);
  }
  mpi_queue.insertBytes((char *) &fmtlen, 1);
  mpi_queue.insertBytes(full_format, fmtlen);
}

static VOID
simerr_generate_error_dfloat(BASETIME step, SP_EEP_TYPES error_code, asINT32 _scale,
		      dFLOAT *point, va_list ap) 
{
  asINT32 scale = sim.num_scales - _scale - 1;

  /* If this error type suppressed, dump it right here */
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  if(disabled_errors_mask[error_code])
    return;
#else
  if (((auINT64) 0x01 << (auINT64) error_code) & disabled_errors_mask)
    return;
#endif
  char full_format[8 * 1024]; // ample space
  spGenerateSpErrorPacketDouble(full_format, step, error_code, scale, point, ap);

  unsigned char fmtlen = (unsigned char) strlen(full_format);
  while (mpi_queue.availableSpace() < (fmtlen + 1)) {
    if (!g_strand_mgr.m_comm_thread_started) {
      simerr_mpi_process();
    }
    sp_thread_sleep(MPI_SLEEP_LONG);
  }
  mpi_queue.insertBytes((char *) &fmtlen, 1);
  mpi_queue.insertBytes(full_format, fmtlen);
}

VOID
simerr_report_error_code(SP_EEP_TYPES error_code, asINT32 scale, STP_GEOM_VARIABLE *point, ...) 
{
  if (simerr_suppress_errors)
    return;
  va_list ap;
  BOOLEAN geom_var_is_double = (sizeof(STP_GEOM_VARIABLE) == 8); 
  if(geom_var_is_double) {  
    dFLOAT fpoint[3];
    fpoint[0] = (dFLOAT) point[0];
    fpoint[1] = (dFLOAT) point[1];
    fpoint[2] = (dFLOAT) point[2];
    va_start(ap, point);
    BASETIME step = sim.initialization_complete_p ? g_timescale.m_time : (BASETIME) -1;
    simerr_generate_error_dfloat(step, error_code, scale, fpoint, ap);
  } else  {
    sFLOAT fpoint[3];
    fpoint[0] = (sFLOAT) point[0];
    fpoint[1] = (sFLOAT) point[1];
    fpoint[2] = (sFLOAT) point[2];
    va_start(ap, point);
    BASETIME step = sim.initialization_complete_p ? g_timescale.m_time : (BASETIME) -1;
    simerr_generate_error_sfloat(step, error_code, scale, fpoint, ap);
  }
}

VOID simerr_init(asINT32 initial_sp_error_count) 
{
  cp_error_count = initial_sp_error_count;
  eMPI_ack_sp_errs_type_init(&eMPI_cp_ack_sp_errs_type);


#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  //extra sim errors defined for particle modeling exceeded the 64 bit limit.
  disabled_errors_mask.reset();
#else
  if (SP_EER_NUMERRS > 64)
    // simerr_enable_warning is using a 64-bit mask to encode which errors
    // are disabled. If we grow to more than 64 errors, replace the mask
    // with a byte or bit array.
    msg_internal_error("Too many .simerr error types to encode in 64-bit mask.");
#endif
}

BOOLEAN simerr_enable_warning(SP_EEP_TYPES type, BOOLEAN enable)
{
#if 1 //#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  std::bitset<SP_EER_NUMERRS>old_disabled_errors_mask(disabled_errors_mask);
  if(enable) {
    disabled_errors_mask[type] = FALSE;
  } else {
    disabled_errors_mask[type] = TRUE;
  }
  return(disabled_errors_mask == old_disabled_errors_mask);  //return true if any changes
#else
  auINT64 old_disabled_errors_mask = disabled_errors_mask;

  if (enable) {
    disabled_errors_mask = (disabled_errors_mask & ~((auINT64) 0x01UL << type));
  } else {
    disabled_errors_mask |= ((auINT64) 0x01UL << type);
  }

  return((old_disabled_errors_mask != disabled_errors_mask) ? TRUE : FALSE);
#endif
}

VOID simerr_finalize()
{
  while (1) {
    MPI_Status status;
    int new_request;
    MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_CP_ACK_SP_ERRS_INFO_TAG,
               eMPI_sp_cp_comm, &new_request, &status);

    if (new_request) {
      sCP_ACK_SP_ERRS_INFO_MSG ack_msg;
      RECV_EXA_SIM_MSG<sCP_ACK_SP_ERRS_INFO_MSG, 1> recv_ack_msg(eMPI_CP_ACK_SP_ERRS_INFO_TAG, eMPI_sp_cp_rank());
      g_exa_sp_cp_comm.recv(recv_ack_msg.mpi_msg);
      ack_msg = *recv_ack_msg.return_buffer();
    } else {
      break;
    }
  }
}
