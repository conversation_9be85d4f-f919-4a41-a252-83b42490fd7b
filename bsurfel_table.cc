/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "bsurfel_table.h"
#include "sim.h"


//----------------------------------------------------------------------------
// sBSURFEL_TABLE::bsurfel_from_id
//----------------------------------------------------------------------------
sBSURFEL* sBSURFEL_TABLE::bsurfel_from_id(SHOB_ID id)
{
  // binary search
  SHOB_ID_ORDER id_order;
  ITERATOR item  = lower_bound(m_bsurfels.begin(), m_bsurfels.end(), id, id_order);

  if (item == m_bsurfels.end())
    return NULL;

  SHOB bsurfel    = *item;

  if (bsurfel != NULL && bsurfel->id() != id)
    bsurfel = NULL;

  return static_cast<sBSURFEL*>(bsurfel);
}

VOID sBSURFEL_TABLE::add(SHOB bsurfel)
{
  m_bsurfels.push_back(bsurfel);
}

VOID sBSURFEL_TABLE::sort()
{
  // binary search
  SHOB_ID_ORDER id_order;
  std::sort(m_bsurfels.begin(), m_bsurfels.end(), id_order);
}
