/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "strand_mgr.h" //Needed to access g_strand_mgr props and methods
#include "seed_sp.h"
#include "wsurfel_comm.h"

//=============================================================================
// sSEND QUEUE CLASS DEFS
//=============================================================================
VOID cSEND_QUEUE::add_sp_entry(sSP_SEND_GROUP_BASE *group, ACTIVE_SOLVER_MASK active_solver_mask, bool surfel_send_group)
{
  if(sim.prepare_calibration_run && !surfel_send_group)
    return;

  g_sync_threads.wake_up_comm_thread();
#if 0
  LOG_MSG("RECV_DEPEND").format("Adding to Send Queue type {} scale {} for SP {}, size {}", group->m_send_type,
          group->m_scale, group->m_dest_sp, group->m_sendsize);
#endif

  group->reset_send_data_copied();

  if(surfel_send_group && g_timescale.m_time > 0)
    group->t_solver_type = sim.T_solver_type;
  else {
    if(sim.thermal_accel.do_T_solver_switch) {
      group->switch_T_solver();
    }
  }

  m_queue.push({group, active_solver_mask});

  // LOG_MSG("RECV_DEPEND").printf("Adding to Send Queue type %d scale %d for SP %d, size %d", 
                                       // group->m_send_type, group->m_scale, group->dest_rank(), group->m_sendsize);
}

VOID cSEND_QUEUE::add_rp_entry(sRP_SEND_GROUP *group)
{
  group->reset_send_data_copied();
  m_queue.push({group, DEFAULT_ACTIVE_SOLVER_MASK});
}

VOID cSEND_QUEUE::drain()
{
  m_queue.drain([](sSEND_QUEUE_ENTRY& entry) {
                sSEND_GROUP_BASE * group = entry.m_group;
                group->send(entry.m_active_solver_mask);
                // LOG_MSG("RECV_DEPEND").format("Sending Type {} scale {} to SP {} size {}", 
                                              // group->m_send_type, group->m_scale, group->m_dest_sp, group->m_sendsize);
                group->set_send_data_copied();
                });
}

//=============================================================================
// COLLECTIVE QUEUE CLASS DEFS
//=============================================================================
cCOLLECTIVE_QUEUE::cCOLLECTIVE_QUEUE(asINT32 capacity) :
  m_capacity(capacity),
  m_head(0),
  m_tail(0)
{
  m_entries = xnew sCOLLECTIVE_GROUP*[capacity + 1];
}

VOID cCOLLECTIVE_QUEUE::add_entry(sCOLLECTIVE_GROUP *group) 
{
  g_sync_threads.wake_up_comm_thread();
  
  if (n_entries() == m_capacity) {
    msg_internal_error("Collective queue is full: n_entries %d capacity %d",
                       n_entries(), m_capacity);
  }

  m_entries[m_tail] = group;
  if(m_tail == m_capacity)
    m_tail = 0;
  else
    m_tail++;

  LOG_ON("PHASE_FIELD")<<"add_entry";
}

BOOLEAN cCOLLECTIVE_QUEUE::drain()
{
  BOOLEAN some_group_add_to_list = FALSE;

  while (!is_empty()) {
    some_group_add_to_list = TRUE;

    sCOLLECTIVE_GROUP *group = m_entries[m_head];
    //group->collective_call();
    g_strand_mgr.add_to_collective_list(group);

    if (m_head == m_capacity)
      m_head = 0;
    else
      m_head++;

    LOG_ON("PHASE_FIELD")<<"drain";
  }

  return some_group_add_to_list;
}

//=============================================================================
// RECEIVE CHANNEL CLASS DEFS
//=============================================================================
VOID cRECV_CHANNEL::init()
{

  // The m_n_expected array is allocated and initialized before this method is called.
  // This depends on prior initialization of the m_n_expected array.

  m_finest_non_empty_scale = STP_SCALE_INVALID;
  DO_SCALES_FINE_TO_COARSE(scale) {

    auINT32 coarseness = (sim.num_scales - 1) - scale;
    auINT32 finer_scales_timestep_mask =  (0x1 << coarseness) - 1;

    TIMESTEP initial_scale_timestep =  get_init_scale_time_step(scale);

    if (m_n_expected_scale[scale] > 0) {
      if (m_finest_non_empty_scale == STP_SCALE_INVALID) {
        m_finest_non_empty_scale = scale;
      }
      m_scale_timestep[scale] = initial_scale_timestep;
    } else {
      m_scale_timestep[scale] = TIMESTEP_LAST;
    }
    
    m_n_advanced_ssps[scale] = 0;
    ccDOTIMES(ssp, m_n_source_sps) {
      m_count[ssp][scale] = m_n_expected_ssp[ssp][scale];
      if (m_n_expected_ssp[ssp][scale] > 0) {
        m_ssp_timestep[ssp][scale]  = initial_scale_timestep;
      } else {
        m_ssp_timestep[ssp][scale]  = TIMESTEP_LAST;
        m_n_advanced_ssps[scale]++;
      }
    }
  }

  init_m_scale_sequence_scales();
}

void cRECV_CHANNEL::allocate_arrays(RECV_TYPE rtype, asINT32 num_scales, asINT32 n_source_sps)
{
  m_type = rtype;
  m_n_source_sps = n_source_sps;
  m_n_expected_scale = cnew asINT32[num_scales];
  m_n_expected_ssp = xnew uINT16*[n_source_sps];
  m_n_expected = 0;
  m_recv_groups = new SP_RECV_GROUP_BASE*[num_scales];
  ccDOTIMES(ssp, n_source_sps) {
    m_n_expected_ssp[ssp] = cnew uINT16[num_scales];
  }
  ccDOTIMES(scale, num_scales) {
    m_recv_groups[scale] = new sSP_RECV_GROUP_BASE*[n_source_sps];
    ccDOTIMES(ssp, n_source_sps) {
      m_recv_groups[scale][ssp] = NULL;
    }
  }

  m_scale_timestep = xnew TIMESTEP [sim.num_scales];
  m_n_advanced_ssps = xnew STP_PROC [sim.num_scales];
  m_ssp_timestep = xnew TIMESTEP *[n_source_sps];
  m_count = xnew uINT16 *[n_source_sps];
  ccDOTIMES(ssp, n_source_sps) {
    m_ssp_timestep[ssp] = xnew TIMESTEP[sim.num_scales];
    m_count[ssp] = xnew uINT16[sim.num_scales];
  }
}

// This function is called because more send and receive groups are added
// surfel film solver
void cRECV_CHANNEL::update_allocations(asINT32 n_source_sps)
{
  if (m_n_source_sps == n_source_sps)
    return;

  SCALE num_scales = sim.num_scales;

  if (m_n_expected_ssp) {
    ccDOTIMES(ssp, m_n_source_sps) {
      delete (m_n_expected_ssp[ssp]);
    }
    delete(m_n_expected_ssp);
  }

  m_n_expected_ssp = xnew uINT16*[n_source_sps];
  m_n_expected = 0;
  ccDOTIMES(ssp, n_source_sps) {
    m_n_expected_ssp[ssp] = cnew uINT16[num_scales];
  }


  ccDOTIMES(scale, num_scales) {
    if (m_recv_groups[scale]) {
      delete (m_recv_groups[scale]);
    }
    m_recv_groups[scale] = new sSP_RECV_GROUP_BASE*[n_source_sps];
    ccDOTIMES(ssp, n_source_sps) {
      m_recv_groups[scale][ssp] = NULL;
    }
  }

  if (m_ssp_timestep) {
    ccDOTIMES(ssp, m_n_source_sps) {
      if (m_ssp_timestep[ssp])
        delete (m_ssp_timestep[ssp]);
    }
    delete (m_ssp_timestep);
  }
  if (m_count) {
    ccDOTIMES(ssp, m_n_source_sps) {
      if (m_count[ssp])
        delete (m_count[ssp]);
    }
    delete (m_count);
  }
  m_ssp_timestep = xnew TIMESTEP *[n_source_sps];
  m_count = xnew uINT16 *[n_source_sps];
  ccDOTIMES(ssp, n_source_sps) {
    m_ssp_timestep[ssp] = xnew TIMESTEP[sim.num_scales];
    m_count[ssp] = xnew uINT16[sim.num_scales];
  }
  m_n_source_sps = n_source_sps;
}

VOID cRECV_CHANNEL::update_source_sps(asINT32 n_neighbor_sps)
{
  cassert(n_neighbor_sps == m_n_source_sps);
  // The m_n_expected array is allocated and initialized before this method is called.

  // This depends on prior initialization of the m_n_expected array.

  m_finest_non_empty_scale = STP_SCALE_INVALID;
  DO_SCALES_FINE_TO_COARSE(scale) {

    auINT32 coarseness = (sim.num_scales - 1) - scale;
    auINT32 finer_scales_timestep_mask =  (0x1 << coarseness) - 1;

    TIMESTEP initial_scale_timestep =  get_init_scale_time_step(scale);

    if (m_n_expected_scale[scale] > 0) {
      if (m_finest_non_empty_scale == STP_SCALE_INVALID) {
        m_finest_non_empty_scale = scale;
      }
      m_scale_timestep[scale] = initial_scale_timestep;
    } else {
      m_scale_timestep[scale] = TIMESTEP_LAST;
    }

    // The number of source SPs may change after initialization because film surfels
    // may be commed from SPs the decomposer does not know about.
    for(int ssp = m_n_source_sps; ssp < n_neighbor_sps; ssp++) {
      m_n_expected_ssp[ssp] = cnew uINT16[sim.num_scales];
    }

    m_n_advanced_ssps[scale] = 0;
    ccDOTIMES(ssp, n_neighbor_sps) {
      m_count[ssp][scale] = m_n_expected_ssp[ssp][scale];
      if (m_n_expected_ssp[ssp][scale] > 0) {
        m_ssp_timestep[ssp][scale]  = initial_scale_timestep;
      } else {
        m_ssp_timestep[ssp][scale]  = TIMESTEP_LAST;
        m_n_advanced_ssps[scale]++;
      }
    }
  }

  init_m_scale_sequence_scales();
  m_n_source_sps = n_neighbor_sps;
}

void cRECV_CHANNEL::add_recv_group(SP_RECV_GROUP_BASE group)
{
  asINT32 scale = group->scale();
  asINT32 source_sp = group->source_nsp();
  m_recv_groups[scale][source_sp]  = group;
  m_n_expected_ssp[source_sp][scale] = 1;
  m_n_expected_scale[scale]++;
  m_n_expected++;
}

/*=============================================================================
 * get_init_scale_time_step - This function computes a channel's intial value
 * for m_scale_timestep.
 *
 * The logic has two main branches :
 * a) One for handling same timestep dependecies such as surfel receives
 * b) For handling future timestep dependencies such as ublk comm
 *
 * a)Same timestep dependencies
 * These are not assumed to be implicitly satisfied at the start
 * Therefore to account for communication at initialization, we restore the
 * initial values of the channel to what it would have been when the
 * simulation was stopped/check-pointed.
 * For TS = 0, we assume that the initial values of m_scale_t_step is -1
 *
 * EXAMPLE :
 * Consider three active scales in the simulation
 *
 * m_scale_timestep table for surfel receives, sim starts at TS = 0
 * TS\SCALE  2 |  1  |  0
 * -1       -1 | -1  | -1
 *  0        0 | -1  | -1
 *  1        1 |  1  | -1
 *  2        2 |  1  | -1
 *  3        3 |  3  |  3
 *
 *  Cycle repeats
 *
 * If we checkpoint at TS = 1248, values of the scale timesteps at TS = 1247
 *
 * TS\SCALE  2 |  1  |  0
 * 1246    1246|1245 |1243
 * 1247    1247|1247 |1247
 * 1248    1248|1247 |1247
 * 1249    1249|1249 |1247
 * 1250    1250|1249 |1247
 * 1251    1251|1251 |1251
 *
 * These values for TS=1247 are used to initialize m_scale_t_step for TS = 1248
 *
 * b) UBLK receives
 * Ublk dependencies are assumed to be satisfied at the start of the timestep. Therefore,
 * the scale timestep for a receive channel would have progressed to value that matches a
 * receive completed right before the simulation was check-pointed.
 * Since scale information is needed when a finer scale is computed, at TS = 0, we initialize
 * scale timestep values to 2^( num_scales() -1 - scale - 1) - 1
 *
 * m_scale_timestep table for ublk receives, sim starts at TS = 0
 * TS\SCALE  2 |  1  |  0
 * -1        0 |  0  |  1
 *  0        0 |  0  |  1
 *  1        1 |  0  |  1
 *  2        2 |  2  |  1
 *  3        3 |  2  |  1
 *  4        4 |  4  |  1
 *  5        5 |  4  |  5
 *
 *  Cycle repeats
 *
 * If we checkpoint at TS = 1248, the ckpt file is written in the end of TS 1247.
 * Values of the scale timesteps around TS = 1247 are
 *
 * TS\SCALE  2 |  1  |  0
 * 1244    1244|1244 |1245
 * 1245    1245|1244 |1245
 * 1246    1246|1246 |1245  
 * 1247    1247|1246 |1245
 * 1248    1248|1248 |1245
 * 1249    1249|1248 |1249
 *
 * Before ckpting, all the posted sends are completed. When resuming, the recv channel 
 * should expect the data for the next send. In the beginning of TS 1248, we know that
 * for scale 1, the send posted at TS 1246 is finished, so the recv channel should expect
 * data for TS 1248; while for scale 2, sending data of TS 1245 will be posted in TS 1248,
 * which has not happened yet, so the recv channel should still expect data for TS 1245.
 * As a result, when resuming at TS 1248, the m_n_scale_timestep should be
 *
 * TS\SCALE  2 |  1  |  0
 * 1248    1248|1248 |1245
 *
 */
TIMESTEP cRECV_CHANNEL::get_init_scale_time_step(asINT32 scale){
  TIMESTEP start_cntr = (m_type == WSURFEL_RECV_TYPE)
                        ? g_timescale.start_next_wsurfel_comm_cntr()
                        : g_timescale.start_timestep_cntr();
  TIMESTEP init_step     = 0.0;
  TIMESTEP init_step_t_0 = 0.0;
  auINT32  coarseness = (sim.num_scales - 1) - scale;
  auINT32  scale_timestep_mask =  (0x1 << coarseness);

  //If recvs expected at the same timestep, the initial step of the recv channel is last_time_scale given that
  //theoretically was reset in the middle of it.
  //Otherwise, should be the timestep when the scale is active for the first time, either at cntr or after it.
  if (does_recv_satisfy_same_timestep_dep(m_type)){
    init_step = init_step_t_0 = -1;
    if ( coarseness > 0 ) {
      if ( start_cntr > 0 ) {
        //Statement below truly is
        //init_step = init_step_t_0 + scale_timestep_mask * ( ( start_cntr - 1 - init_step_t_0)/scale_timestep_mask);
        if(sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore)
          init_step = init_step_t_0 + (start_cntr & (~(scale_timestep_mask-1)));
      }
    } else {
      if(sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore)
        init_step = init_step_t_0 + start_cntr;
      else
        init_step = init_step_t_0;
    }
  } else {
    if ( coarseness > 0 ) {
      init_step = init_step_t_0 = (0x1 << (coarseness - 1)) - 1;

      if ( (start_cntr - init_step) > 0 && (sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore) ) {
        init_step = init_step_t_0 + (((start_cntr) >> coarseness) << coarseness);
      }
    } else {
      if(sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore)
        init_step = start_cntr;
      else
        init_step = 0;
    }
  }
  return init_step;
}

TIMESTEP cRECV_CHANNEL::get_scale_time_step(asINT32 scale, TIMESTEP timestep_cntr, BOOLEAN get_last_active) {
  // COND-TODO:
  // Previous implementation above might work for non-zero start times. It would be replaced simply by 
  // return get_scale_time_step(scale, start_cntr, does_recv_satisfy_same_timestep_dep(m_type));
  //
  // Determines the theoretical timestep of the scale being checked prior to the cntr when the scale was active
  TIMESTEP last_time_scale;
  auINT32  coarseness = (sim.num_scales - 1) - scale;
  auINT32  scale_timestep_inc =  (0x1 << coarseness);
  if (coarseness == 0) { //finest scale
    last_time_scale = timestep_cntr - 1;
  } else {
    auINT32  rounding_mask = ~(scale_timestep_inc-1);
    last_time_scale = (timestep_cntr & rounding_mask) - 1; //rounded to a multiple of the timestep periodicity of the scale
  }
  if (get_last_active){
    return last_time_scale;
  } else { //get_next_active
    return last_time_scale + scale_timestep_inc;
  }
}


// There are these differences between the set of receives required for each
// timestep for surfel comm and ublk comm:
//   a. Surfel and MME (and Pore for 5G) receives must be completed in the same timestep the sends are initiated;
//       ublk receives are completed in later timesteps
//   b. For surfel and MME (and Pore for 5G) receives all active scales are sent and received, but for ublk comm only
//       the finest scale and coarsen(coarsest active scale) are required.
//
//  For surfel comm, the order in which receives are needed is the same order in which the
// messages are sent, so we can just complete receives in that order. For ublks
// the order is different, because we only need two scales per timestep.
VOID cRECV_CHANNEL::init_m_scale_sequence_scales(){
  if (does_recv_satisfy_same_timestep_dep(m_type)) {

    m_scale_sequence_scales = xnew STP_SCALE[sim.num_scales];

    // The m_scale_sequence_scales is ordered fine to coarse
    ccDOTIMES(scale_index, sim.num_scales) {
      m_scale_sequence_scales[scale_index] = sim.num_scales - 1 - scale_index;
    }

    //Determines first timestep dependent on completed receives
    //Note that wsurfel cross-realm active only when both realms are active. Thus, its starting base time is set by the
    //realm with the larger timesteps, which might not processed by this SP. However, conversion to timestep counter to
    //determine the base is still valid, since it rounds to the floor, so we can still use this start base time
    if (m_type == WSURFEL_RECV_TYPE) {
      m_scale_sequence_timestep = g_timescale.start_next_wsurfel_comm_cntr();
      //cross-realm comm is sensitive to time coupling scheme, so fills m_next_phase_timestep to detect phase change
      //when progress_scale_sequence() is invoked
      sTIME_COUPLING_PHASE* next_phase = sim.time_coupling_info.next_phase();
      m_next_phase_timestep = (next_phase != nullptr) 
                              ? g_timescale.basetime_to_next_wsurfel_comm_cntr(next_phase->start)
                              : TIMESTEP_LAST;
      m_next_phase_idx = sim.time_coupling_info.idx_next_phase();
    } else {
      m_scale_sequence_timestep = g_timescale.start_timestep_cntr();
    }
    STP_SCALE coarsest_active = compute_coarsest_active_scale(m_scale_sequence_timestep, FINEST_SCALE);
    m_scale_sequence_nscales = sim.num_scales - coarsest_active;

  } else {
    m_scale_sequence_scales    = xnew STP_SCALE[2];
    m_scale_sequence_scales[0] = FINEST_SCALE; // This never changes
    m_scale_sequence_timestep  = g_timescale.start_timestep_cntr() + 1;  // First timestep dependent on completed receives
    STP_SCALE coarsest_active  = compute_coarsest_active_scale(m_scale_sequence_timestep, FINEST_SCALE);
    STP_SCALE coarser_scale    = coarsen_scale(coarsest_active);

    //The next few statements determine if the UBLK data we commed at initialization suffices to satisfy the first
    //dependent timestep's coarser_scale dependencies or not.
    STP_SCALE coarser_scale_span = (sim.num_scales - 1) - coarser_scale;
    TIMESTEP coarser_scale_span_mask = 0x1 << coarser_scale_span;
    BOOLEAN coarser_scale_is_active_at_start = (m_scale_sequence_timestep % (0x1 << coarser_scale_span)) == 0;
    TIMESTEP channel_scale_time_step = get_init_scale_time_step(coarser_scale);

    //We expect a receive for the coarser_scale, if the channel's scale_timestep indicates that we need
    //the data for m_scale_sequence_timestep. If the coarser_scale was not active at the start of the sim,
    //then the initial UBLK comm already satisfies this timestep's receive dependencies. Note that we are 
    //looking at receive dependencies for m_scale_sequence_timestep = g_timescale.start_timestep_cntr() + 1
    BOOLEAN is_coarser_scale_data_expected = (channel_scale_time_step <= m_scale_sequence_timestep)
                                              && coarser_scale_is_active_at_start; //at g_timescale.start_timestep_cntr()
    BOOLEAN coarse_receive_valid_scale     = coarser_scale >= COARSEST_SCALE;
    m_scale_sequence_scales[1]             = coarser_scale;
    m_scale_sequence_nscales               = (is_coarser_scale_data_expected && coarse_receive_valid_scale) ? 2 : 1;
  }

  m_scale_sequence_ssp = 0;
  m_scale_sequence_scale_index = 0;
}

// Since there is no synchronization between SPs, it is possible for a message sent by one SP during timestep N
// to arrive earlier that one sent by another (for the same scale) during timestep N-1; messages from multiple SP's
// for a given scale cannot be regarded as time-ordered. Therefore we need to track the source timestep for every message
// received on an individual SSP basis, and we need to know the number expected for each SSP for each scale; this is
// recorded in the m_n_expected_ssp[m_neighbor_sp_map.get_n_neighbor_sps()][sim.num_scales] array.

BOOLEAN cRECV_CHANNEL::update(STP_PROC ssp, SCALE scale) {
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("Recv channel update called for type %d ssp %d scale %d", m_type, ssp, scale);

  BOOLEAN scale_updates_complete = FALSE;          // This is the return value of the function. It indicates whether this
                                                   // receive completed the full set for the specified type and scale, which
                                                   // is used by the caller to update the consumer counts.
  m_count[ssp][scale]--;
  if (m_count[ssp][scale] == 0) {

    // This conditional is just to avoid advancing the counter more than once for the same ssp.
    // This must be determined before the ssp_timestep is incremented.

    if (m_ssp_timestep[ssp][scale] == m_scale_timestep[scale]) {
      // about to advance the ssp timestep past the scale timestep
      m_n_advanced_ssps[scale]++;
    }

    asINT32 scaled_timestep_increment = 1 << (FINEST_SCALE - scale);
    m_ssp_timestep[ssp][scale] += scaled_timestep_increment;
    m_count[ssp][scale] = m_n_expected_ssp[ssp][scale];

    // The scale_timestep is the MIN of all the ssp_timesteps for that scale. Rather than doing a MIN
    // across all SSP's, we keep track of the number that have advanced ahead of the scale_timestep.
    // When they have all advanced, the scale_timestep is advanced.

    if (m_n_advanced_ssps[scale] == m_n_source_sps) {
      pthread_mutex_lock(&g_strand_mgr.m_time_update_mutex);
      m_scale_timestep[scale] += scaled_timestep_increment;
      m_n_advanced_ssps[scale] = 0;
      scale_updates_complete = TRUE;

      // m_unpacked indicates the state of receives corresponding to the current scale timestep. When
      // we change that timestep, it is set to FALSE for all SSP's.
      ccDOTIMES(nth_ssp, m_n_source_sps) {
        if (m_recv_groups[scale][nth_ssp] != NULL) {
          if (! m_recv_groups[scale][nth_ssp]->is_unpacked()) {
            msg_internal_error("update_recv_channel detected a group that has not been unpacked");
          }
          m_recv_groups[scale][nth_ssp]->reset_unpacked();
        }
      }

      // Account for any SSP's that have moved on. SSP's that send nothing for a particular scale have
      // their m_ssp_timesteps initialized to TIMESTEP_LAST, so they will be accounted for here.

      ccDOTIMES(i, m_n_source_sps) {
        if (m_ssp_timestep[i][scale] > m_scale_timestep[scale])
          m_n_advanced_ssps[scale]++;
      }

      g_strand_mgr.recv_channel_record_completion(m_type,scale);

      pthread_mutex_unlock(&g_strand_mgr.m_time_update_mutex);
    } //rchannel->m_n_advanced_ssps[scale] == g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps()
  } //channel->m_count[ssp][scale] == 0
  return scale_updates_complete;
}

// Revisits the expected ssp timestep when the comm is re-started at the beginning of the next time phase
VOID cRECV_CHANNEL::update_ssp_timestep_to_next_phase(BASETIME next_phase_start) {
  TIMESTEP timestep_ctr, next_timestep;
  if (m_type == WSURFEL_RECV_TYPE) {
    timestep_ctr = g_timescale.m_wsurfel_comm_cntr;
    next_timestep = g_timescale.basetime_to_next_wsurfel_comm_cntr(next_phase_start);
  } else {
    timestep_ctr = g_timescale.m_timestep;
    next_timestep = g_timescale.basetime_to_strands_timestep_cntr(next_phase_start);
  }
  //Next timestep when the counter will be incremented is at or after the next phase starts for the finest scale, which
  //will correspond to next the next increment in the wsurfel comm timestep cntr (note that even if the ratio is
  //different at the next phase, we are looking at the first counter value which will be the same since we round up).
  //Thus, their difference provides the offset to be applied to determine the expect ssp timesteps based on the last
  //timesteps that was active
  TIMESTEP delta_ctr = next_timestep - (timestep_ctr+1);
  pthread_mutex_lock(&g_strand_mgr.m_time_update_mutex);
  for (asINT32 scale = FINEST_SCALE; scale >= 0; scale--) {
    TIMESTEP last_scale_timestep = get_scale_time_step(scale, next_timestep, TRUE);
    TIMESTEP last_ssp_timestep_ctr = last_scale_timestep - delta_ctr;
    //Updates the scale time-step to the last one when it was expected to have received data
    if (m_n_expected_scale[scale] > 0) {
      m_scale_timestep[scale] = last_ssp_timestep_ctr;
    }
    m_n_advanced_ssps[scale] = 0;
    //Advance the ssp_timestep accordingly for those SSPs that send data, and update the counter of advanced SSPs
    //for those that are not sending data, mimicking what would have been done by cRECV_CHANNEL::update
    ccDOTIMES(ssp, m_n_source_sps) {
      if (m_n_expected_ssp[ssp][scale] > 0) {
        m_ssp_timestep[ssp][scale] = last_ssp_timestep_ctr;
      } else {
        m_n_advanced_ssps[scale]++; //accounts for SPs that are not expected to send data in the counter
      }
    }
  }
  pthread_mutex_unlock(&g_strand_mgr.m_time_update_mutex);
}

VOID cRECV_CHANNEL::update_scale_sequence_to_next_phase() {
  //Searches for the next phase where the comm will be active, i.e. that it is not frozen
  std::vector<sTIME_COUPLING_PHASE>& phases = sim.time_coupling_info.m_phases;
  while (m_next_phase_idx < phases.size() && 
         phases[m_next_phase_idx].time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) {
    m_next_phase_idx++;
  }
  if (m_next_phase_idx >= phases.size()) {
    //last phase is frozen, there will be no more comm so no update needed for m_scale_sequence_timestep
    m_next_phase_timestep = TIMESTEP_LAST;
    return;
  } else if (phases[m_next_phase_idx].time_coupling == eTIME_COUPLING_SCHEME::SameRate) {
    //comm occuring next time the solver with the larger time-step is active
    m_scale_sequence_timestep = g_timescale.basetime_to_next_wsurfel_comm_cntr(phases[m_next_phase_idx].start);
  } else {
    //COND-TODO: When staggered coupling is enabled, we need to increment m_scale_sequence_timestep by more than 1. 
    //Create m_scale_sequence_timestep_inc variable and fill it here, but for now, just throw an error
    msg_internal_error("update scale sequence does not support coupling scheme %d", phases[m_next_phase_idx].time_coupling);
  }
  //Advances the phase being processed in the recv channel by moving ahead the next_phase
  m_next_phase_idx++;
  m_next_phase_timestep = (m_next_phase_idx < phases.size())
                          ? g_timescale.basetime_to_next_wsurfel_comm_cntr(phases[m_next_phase_idx].start)
                          : TIMESTEP_LAST;
  // msg_print("next phase %zu timestep %d", m_next_phase_idx, m_next_phase_timestep);
}

//  process_posted_receives
//
// This method is designed to process receives in the order that they are
// required. For any given timestep a consumer of receives of a ublk
// type requires the finest and coarsen(corsest_active_scales) for the
// next timestep, while a  consumer of receives of a surfel type requires
// all active scales for the current timestep.

//  This method is intended to be called multiple times for each value of
//  m_scale_sequence_timestep, and to continue where it left off with each
//  succeeding call. This is accomplished by means of the persistent loop
//  indices m_scale_sequence_scale_index and m_scale_sequence_ssp. The
//  outer loop is over one or two scales; the finest scale (always), and
//  coarsen(coarsest_active_scale(m_scale_sequence_timestep)), if it exists.
//  The inner loop is over ssps.
//
//  For each scale and ssp for which receives are expected, we first test
//  to see that the receive has been posted; if it has not, the receive
//  request will be NULL, and we can proceed no further, and the method
//  immediately returns. If it has been posted, we attempt to complete
//  it. If successful, we call complete_ghost_update, which will either
//  unpack the receive or add it to the unpacking list. If not, the two
//  persistent indices are updated, and the method returns. Thus requests
//  are processed in strict order; we never jump over one and go to the next.
//
//  If the outer loop completes, we have completed all the posted receives
//  for the set of scales associated with m_scale_sequence_timestep. We
//  can thus increment m_scale_sequence_timestep and compute a new
//  coarsest_active_scale. If coarsen(coarsest_active_scale_ exists,
//  m_scale_sequence_nscales is set to 2; otherwise it is set to 1. The
//  two persistent indices are rest to 0.
//
//  The method may process multiple scale_sequence_timesteps; it does
//  not return until it encounters a receive which cannot be completed.
//
//  The method returns 1 if any receive is completed, a value which may be
//  used by the caller for performance optimization.
BOOLEAN cRECV_CHANNEL::is_prepare_calibration_ts(STP_SCALE scale, STP_PROC ssp, SP_RECV_GROUP_BASE group) {
  if(sim.calibration_params.iteration_number < 0 || m_ssp_timestep[ssp][scale] == 0)
    return FALSE;
  TIMESTEP previous_timestep = m_ssp_timestep[ssp][scale];
  asINT32 timestep_increment = 1 << (FINEST_SCALE - scale);
  TIMESTEP next_timestep = previous_timestep + timestep_increment;
  previous_timestep /= sim.calibration_params.calib_period;
  next_timestep /= sim.calibration_params.calib_period;
  if(previous_timestep == next_timestep)
    return FALSE;
  else if(group->m_recv_type == FARBLK_RECV_TYPE || group->m_recv_type == NEARBLK_RECV_TYPE) {
    return TRUE;
  }
  else
    return FALSE;
}
BOOLEAN cRECV_CHANNEL::process_posted_receives() {
  BOOLEAN some_receive_completed = FALSE;
  if (m_n_expected == 0)
    return FALSE ;

  while(1) {
    ccDO_FROM_BELOW(scale_index, m_scale_sequence_scale_index, m_scale_sequence_nscales) {
      STP_SCALE scale = m_scale_sequence_scales[scale_index];
      ccDO_FROM_BELOW(ssp, m_scale_sequence_ssp, m_n_source_sps) {
        m_scale_sequence_ssp = 0; // The next loop should start at 0
        if (m_n_expected_ssp[ssp][scale] != 0) {
          sSP_RECV_GROUP_BASE* group = m_recv_groups[scale][ssp];
          if(is_prepare_calibration_ts(scale, ssp, group))
            return FALSE;
          if (group->is_recv_request_null()) {
            m_scale_sequence_ssp = ssp;
            m_scale_sequence_scale_index = scale_index;
            return some_receive_completed;
          }
          if (group->is_recv_ready()) {
            LOG_MSG("STRAND_SWITCHING").format("PPR: completed recv type {} scale {} from SP {} consumers {} current time {}",
                    group->m_recv_type, group->m_scale, group->m_source_sp,
                    g_strand_mgr.m_active_consumers_count[group->m_recv_type][group->m_scale],
                    g_timescale.m_time);

            some_receive_completed = TRUE;
            g_strand_mgr.add_to_unpacking_list(group);
          } else {
            m_scale_sequence_ssp = ssp;
            m_scale_sequence_scale_index = scale_index;
            //msg_print("Return1 ssp %d sssix %d ssts %d",m_scale_sequence_ssp, m_scale_sequence_scale_index,m_scale_sequence_timestep );
            return some_receive_completed;
          }
        } else {
          //msg_print("m_n_expected_ssp[%d][%d] = %d",ssp,scale,m_n_expected_ssp[ssp][scale]);
        }
      }
    }
    progress_scale_sequence();
  }
  msg_internal_error("Unexpected exit from process_posted_receives");
  return some_receive_completed;
}

VOID cRECV_CHANNEL::progress_scale_sequence() {
  m_scale_sequence_timestep++;
  // if ((m_type == WSURFEL_RECV_TYPE) && (m_scale_sequence_timestep == m_next_phase_timestep)) {
  //   //New time coupling phase reached, updates m_scale_sequence_timestep accordingly
  //   this->update_scale_sequence_to_next_phase();
  // }
  STP_SCALE coarsest_active = compute_coarsest_active_scale(m_scale_sequence_timestep, FINEST_SCALE);

  if (does_recv_satisfy_same_timestep_dep(m_type)) {
    m_scale_sequence_nscales = sim.num_scales - coarsest_active;
  } else {
    STP_SCALE coarse_receive_scale  = coarsen_scale(coarsest_active);
   //We need coarsest scale comm except - a) close to start of simulation where the comm might already be complete
    // b) if the scale doesn't exist, that is it is smaller than the coarsest scale
    STP_SCALE scale_span = (sim.num_scales - 1) - coarse_receive_scale;
    auINT32   scale_span_mask = 0x1 << scale_span;
    TIMESTEP  init_coarse_scale_t_step = get_init_scale_time_step(coarse_receive_scale);
    //Is the coarse_receive_scale active between the sim start time and the channel seq time?
    BOOLEAN is_coarse_scale_present = 
        ( g_timescale.start_timestep_cntr() & (~(scale_span_mask-1)) ) + scale_span_mask < m_scale_sequence_timestep;
    //A send is required if the channel's timestep is greater than the initial scale time step
    //or if that scale existed after the initial comm and before m_scale_seq_timestep
    BOOLEAN coarse_send_valid_timestep = (m_scale_sequence_timestep > init_coarse_scale_t_step) || is_coarse_scale_present;
    BOOLEAN coarse_receive_valid_scale = coarse_receive_scale >= COARSEST_SCALE;

    m_scale_sequence_scales[1] =  coarse_receive_scale;
    m_scale_sequence_nscales = (coarse_send_valid_timestep  && coarse_receive_valid_scale) ? 2 : 1;
  }

  m_scale_sequence_ssp = 0;
  m_scale_sequence_scale_index = 0;
}

VOID cRECV_CHANNEL::cancel_pending_recvs() {

  DO_SCALES_FINE_TO_COARSE(scale) {
    ccDOTIMES(ssp, m_n_source_sps) {
      if (m_n_expected_ssp[ssp][scale] != 0) {
        SP_RECV_GROUP_BASE group = m_recv_groups[scale][ssp];
        group->cancel_pending_recv();
      }
    }
  }
}

VOID cRECV_CHANNEL::complete_pending_recvs() {

  DO_SCALES_FINE_TO_COARSE(scale) {
    while (m_scale_timestep[scale] < g_timescale.m_timestep) {
      process_posted_receives();
      g_strand_mgr.process_unpacking_list(m_type, this);
    }
  }
  g_strand_mgr.process_unpacking_list(m_type, this);
}

VOID cRECV_CHANNEL::post_initial_recvs() {

  DO_SCALES_FINE_TO_COARSE(scale) {
    ccDOTIMES(ssp, m_n_source_sps) {
      if (m_n_expected_ssp[ssp][scale] != 0) {
        SP_RECV_GROUP_BASE group = m_recv_groups[scale][ssp];
        group->post_recv();
      }
    }
  }
}


//=============================================================================
// SEND CHANNEL CLASS DEFS
//=============================================================================
VOID cSEND_CHANNEL::allocate(SEND_TYPE stype) {
  m_send_type = stype;
  asINT32 n_dsps = g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps();
  m_total_send_contributors = xnew asINT32*[sim.num_scales];
  m_send_contributors_cntr = xnew asINT32*[sim.num_scales];
  m_send_groups = xnew SP_SEND_GROUP*[sim.num_scales];

  ccDOTIMES(scale, sim.num_scales) {
    m_total_send_contributors[scale] = xnew asINT32[n_dsps];
    m_send_contributors_cntr[scale] = xnew asINT32[n_dsps];
    m_send_groups[scale] = xnew SP_SEND_GROUP[n_dsps];
    ccDOTIMES(dsp, n_dsps) {
      m_total_send_contributors[scale][dsp] = 0;
      m_send_contributors_cntr[scale][dsp] = 0;
      m_send_groups[scale][dsp] = NULL;
    }
  }
}

// This assumes that the active solver mask is the same for all contributors. Probably a safe bet.
VOID cSEND_CHANNEL::contribute_to_send_queue(STP_SCALE scale, STP_PROC dsp, ACTIVE_SOLVER_MASK active_solver_mask) {
  m_send_contributors_cntr[scale][dsp]--;
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld, Strand %d, Send_type %d, contributors[%d][%d] %d",g_timescale.m_time, g_running_strand, m_send_type, scale,dsp,m_send_contributors_cntr[scale][dsp]);
  if(m_send_contributors_cntr[scale][dsp] == 0) {
    BOOLEAN is_surfel_send_group = (m_send_type == SURFEL_SEND_TYPE);
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding entry to send queue", g_timescale.m_time, g_running_strand);
    g_strand_mgr.m_send_queue->add_sp_entry(m_send_groups[scale][dsp], active_solver_mask, is_surfel_send_group);
    m_send_contributors_cntr[scale][dsp] =  m_total_send_contributors[scale][dsp];
  }
}

// FRINGE_WSURF_SAMPLING, INTERIOR_WSURF_SAMPLING, FRINGE2_SURF
#define N_CONTACT_INTERFACE_SENDING_STRANDS 3

VOID cSEND_CHANNEL::initialize_send_channel()
{
    
  ccDOTIMES(scale,sim.num_scales) {
    if(m_send_type == WSURFEL_SEND_TYPE) {
      DO_WSURFEL_SEND_GROUPS_OF_SCALE(ws_send_group, scale) {
        STP_PROC dest_sp = ws_send_group->dest_nsp();
        m_send_groups[scale][dest_sp] = ws_send_group;
        m_total_send_contributors[scale][dest_sp] = N_CONTACT_INTERFACE_SENDING_STRANDS;
        m_send_contributors_cntr[scale][dest_sp] = N_CONTACT_INTERFACE_SENDING_STRANDS;
      }
    } else if (m_send_type == CONTACT_SEND_TYPE) {
      DO_CONTACT_SEND_GROUPS_OF_SCALE(contact_send_group, scale) {
        STP_PROC dest_sp = contact_send_group->dest_nsp();
        m_send_groups[scale][dest_sp] = contact_send_group;
        m_total_send_contributors[scale][dest_sp] = N_CONTACT_INTERFACE_SENDING_STRANDS;
        m_send_contributors_cntr[scale][dest_sp] = N_CONTACT_INTERFACE_SENDING_STRANDS;
      }
    } else {
      ccDOTIMES(surfel_base_group_type, N_SURFEL_BASE_GROUP_TYPES) {
        DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(sgroup, scale, surfel_base_group_type) {
          SP_SEND_GROUP *send_group = (SP_SEND_GROUP *)&sgroup->m_send_group;
          LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "base_type %d, send_type %d",surfel_base_group_type,m_send_type);
          if(*send_group != NULL) {
            STP_PROC dest_sp = sgroup->dest_nsp();
            if(m_send_groups[scale][dest_sp] != NULL) {
              if(m_send_groups[scale][dest_sp] != *send_group) {
                msg_internal_error("initialize_send_channel: inconsistency in send group pointers");
              } else {
                //*send_group = NULL;
              }
            } else {
              m_send_groups[scale][dest_sp] = *send_group;
              //*send_group = NULL;
            }
            m_total_send_contributors[scale][dest_sp]++;
            m_send_contributors_cntr[scale][dest_sp]++;
            LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "total_con[%d][%d] %d, cntr %d",scale,dest_sp,m_total_send_contributors[scale][dest_sp],m_send_contributors_cntr[scale][dest_sp]);
          }
        }
      }
    }
  }
}

