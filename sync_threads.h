/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef SIMENG_SYNC_THREADS_H_
#define SIMENG_SYNC_THREADS_H_
#include "common_sp.h"
#include "thread_run.h"

//=============================================================================
// SYNC THREADS
// Information required by  a sim strand. Most of this information is determined during initialization.
class sSYNC_THREADS {
  pthread_mutex_t m_comm_thread_mutex;
  pthread_cond_t  m_comm_thread_cv;

  pthread_mutex_t m_compute_thread_mutex;
  pthread_cond_t  m_compute_thread_cv;
  BOOLEAN         m_is_compute_thread_awake;

  public:
  sSYNC_THREADS();
  VOID wake_up_comm_thread();
  VOID wake_up_compute_thread();
  timespec set_timeout(SP_SLEEP_INDEX sleepIndex);
  VOID wait_for_compute_thread(SP_SLEEP_INDEX sleepIndex) ;
  VOID wait_for_comm_thread() ;
};

#endif /* SIMENG_SYNC_THREADS_H_ */
