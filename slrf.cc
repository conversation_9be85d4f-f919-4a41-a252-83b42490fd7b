/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * sLRF definitions (functional sets and groups)
 *
 * Created Fri Oct 15, 2010
 *--------------------------------------------------------------------------*/

#include "slrf.h"
#include "sim.h"
#include "sp_timers.h"
#include "surfel_advect_sp.h"
VOID surfel_pair_exchange_slrf_s2s_and_v2s_factors(sSURFEL_PAIR *quantum) {
  SURFEL surfels[2];
  surfels[EXT_INDEX] = quantum->m_exterior_surfel;
  surfels[INT_INDEX] = quantum->m_interior_surfel;

  surfels[EXT_INDEX]->lrf_data()->lrf_s2s_factor_pair = surfels[INT_INDEX]->lrf_data()->lrf_s2s_factor;
  surfels[INT_INDEX]->lrf_data()->lrf_s2s_factor_pair = surfels[EXT_INDEX]->lrf_data()->lrf_s2s_factor;

  surfels[EXT_INDEX]->lrf_data()->lrf_v2s_dist_pair = 1.0;
  if (surfels[INT_INDEX]->percent_v2s != 0) {
    surfels[EXT_INDEX]->lrf_data()->lrf_v2s_dist_pair =
        surfels[INT_INDEX]->lrf_data()->lrf_v2s_dist / surfels[INT_INDEX]->percent_v2s;
  }

  surfels[INT_INDEX]->lrf_data()->lrf_v2s_dist_pair = 1.0;
  if (surfels[EXT_INDEX]->percent_v2s != 0) {
    surfels[INT_INDEX]->lrf_data()->lrf_v2s_dist_pair =
        surfels[EXT_INDEX]->lrf_data()->lrf_v2s_dist / surfels[EXT_INDEX]->percent_v2s;
  }

  surfels[EXT_INDEX]->lrf_data()->lrf_v2s_scale_diff_pair = 1.0;
  if (surfels[INT_INDEX]->voxel_surfel_weight_total != 0) {
    surfels[EXT_INDEX]->lrf_data()->lrf_v2s_scale_diff_pair =
      (surfels[INT_INDEX]->lrf_data()->lrf_v2s_scale_diff / surfels[INT_INDEX]->voxel_surfel_weight_total);
  }


  surfels[INT_INDEX]->lrf_data()->lrf_v2s_scale_diff_pair = 1.0;
  if (surfels[EXT_INDEX]->voxel_surfel_weight_total != 0) {
    surfels[INT_INDEX]->lrf_data()->lrf_v2s_scale_diff_pair =
      (surfels[EXT_INDEX]->lrf_data()->lrf_v2s_scale_diff / surfels[EXT_INDEX]->voxel_surfel_weight_total);
  }
  //surfels[0]->percent_v2s = 0;
  //surfels[1]->percent_v2s = 0;
}
