# ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "78")
##############################################################################
### PowerFLOW Simulator Simulation Process                                 ###
###                                                                        ###
### Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.         ###
### All Rights Reserved.                                                   ###
### US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                     ###
###        5,640,335; 5,848,260; 5,910,902; 5,953,239;                     ###
###        6,089,744; 7,558,714                                            ###
### UK FR DE Pat 0 538 415                                                 ###
###                                                                        ###
### This computer program is the property of Dassault Systemes Americas Corp. ###
### and contains its confidential trade secrets.  Use, examination, copying, ###
### transfer and disclosure to others, in whole or in part, are prohibited ###
### except with the express prior written consent of Dassault Systemes     ###
### Americas Corp.                                                         ###
##############################################################################
# ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78")
SIMENG_D=-DSIMENG_H=\"$(SIMENG_DIR)/simeng.h\"

SIMENG_D19_A=$(SIMENG_DIR)/$T/libsimeng_d19.a
SIMENG_D39_A=$(SIMENG_DIR)/$T/libsimeng_d39.a
SIMENG_5G_A=$(SIMENG_DIR)/$T/libsimeng_5g.a

#The libraries below are used by the decomposer and discretizer
#The methods themselves in these libraries are exposed via the 
#SIM_SIZES component
SIMENG_SIMSIZES_DEFINE_ONCE_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_avx/simsizes_define_once.a
SIMENG_SIMSIZES_D19_SP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_avx/simulator_simsizes_lib_d19_sp.a
SIMENG_SIMSIZES_D19_DP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_dp_avx/simulator_simsizes_lib_d19_dp.a
SIMENG_SIMSIZES_D39_SP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_d39_avx/simulator_simsizes_lib_d39_sp.a
SIMENG_SIMSIZES_D39_DP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_d39_dp_avx/simulator_simsizes_lib_d39_dp.a
SIMENG_SIMSIZES_D5G_SP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_5g_avx/simulator_simsizes_lib_d5g_sp.a
SIMENG_SIMSIZES_D5G_DP_A=$(SIMENG_DIR)/amd64_gcc9_hpmpi_5g_dp_avx/simulator_simsizes_lib_d5g_dp.a
