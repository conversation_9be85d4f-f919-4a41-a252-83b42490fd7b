/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/
 
#include "sim.h"
#include "sampling_surfel.h"
#include "comm_utils.h"


struct Shob_len
{
  Shob_len(SHOB_ID id_, bool verbose_):id(id_), verbose(verbose_){}

  template <typename T>
  void add() {
    constexpr size_t size = sizeof(T);
    add<T>(size);
  }
  template <typename T>
  void add(const size_t size) {
    if(verbose)
      printf("Samp_Surf_ID=%d %s adding %zu bytes\n",id, typeid(T).name(), size);
    len += size;
  }

  const SHOB_ID id;
  const bool verbose=false;
  uINT64 len=0;

};

uINT64 sSAMPLING_SURFEL::ckpt_len(bool verbose)
{ 
  Shob_len surf_len(id(), verbose);

  surf_len.add<sSAMPLING_SURFEL_LB_DATA>();
  
  if (sim.is_turb_model)
    surf_len.add<sSAMPLING_SURFEL_TURB_DATA>();
  if (sim.is_heat_transfer)
    surf_len.add<sSAMPLING_SURFEL_T_DATA>();
  if (sim.is_scalar_model)
    surf_len.add<sSAMPLING_SURFEL_UDS_DATA>(sim.n_user_defined_scalars*sizeof(sSAMPLING_SURFEL_UDS_DATA));

  if(sim.is_particle_model)
    surf_len.add<sSAMPLING_SURFEL_PARTICLE_DATA::sFIXED_SIZE_SAMPLING_SURFEL_PARTICLE_DATA>();

  if (g_is_multi_component)
    surf_len.add<sSAMPLING_SURFEL_MC_DATA>();
  
  if(sim.is_conduction_model && is_conduction_surface()) {
    surf_len.add<sSAMPLING_SURFEL_CONDUCTION_DATA>();
  }
  
  return surf_len.len;

}

VOID sSAMPLING_SURFEL::write_ckpt()
{
  SAMPLING_SURFEL_APPLY(write_ckpt, ());
}

size_t sSAMPLING_SURFEL::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t initial_offset = pio_ckpt_buff.m_offset;//deb purposes

  pio_ckpt_buff.write( this->lb_data() );
  if (sim.is_turb_model)                                                        
    pio_ckpt_buff.write( this->turb_data() );                             
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation)             
    pio_ckpt_buff.write( this->t_data());                                     
  if (sim.is_scalar_model){              					
    SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = this->uds_data(); 
    for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;       
         nth_uds++,sampling_surfel_uds_data++)                            
      pio_ckpt_buff.write(sampling_surfel_uds_data);		                     	
  }										
  if (g_is_multi_component)                                                     
    pio_ckpt_buff.write(this->mc_data());
  if(sim.is_particle_model)
    pio_ckpt_buff.write(&(this->p_data()->s));
  
  if (sim.is_conduction_model && is_conduction_surface())
    pio_ckpt_buff.write( this->conduction_data());

  size_t writtenSz = pio_ckpt_buff.m_offset - initial_offset;
  
  dassert(writtenSz == this->ckpt_len() && " SAMPLING_SURFEL written size does not match ckpt_len()\n");
  return writtenSz;
}
VOID sSAMPLING_SURFEL::read_ckpt()
{
  SAMPLING_SURFEL_APPLY(read_ckpt, ());
}

VOID sSAMPLING_SURFEL::init(DGF_SURFEL_DESC surfel_desc, bool is_ghost)
{
  m_scale = surfel_desc->s.surfel_scale;
  m_id = surfel_desc->s.surfel_id;
  sample_weight_sum = 0;
  m_sampling_surfel_type = 0;
  m_face_index = surfel_desc->s.face_index;
  vcopy(centroid, surfel_desc->s.centroid);
  area = surfel_desc->s.area;
  m_ref_frame_index = surfel_desc->s.lrf_index;
  set_even_odd_mask(surfel_desc->s.surfel_flags & DGF_SURFEL_EVEN_ODD_MASK);
  incoming_latvec_mask = surfel_desc->s.incoming_mask;
  normal[0] = surfel_desc->s.normal[0];
  normal[1] = surfel_desc->s.normal[1];
  normal[2] = surfel_desc->s.normal[2];
  m_opposite_surfel = nullptr;
  if (is_ghost)
    m_surfel_attributes.m_is_ghost = 1;


  // initialize the appropriate solver data blocks
  SAMPLING_SURFEL_APPLY(init, ());
}

VOID sSAMPLING_SURFEL::count_neighbor_ublks_bytes(uINT32 &num_bytes ) {
  uINT8 num_ublks = p_data()->neighbor_ublks.size();
  num_bytes++;
  num_bytes += num_ublks *(sizeof(UBLK_ID) + sizeof(VOXEL_MASK_8));
}

VOID sSAMPLING_SURFEL::fill_neighbor_ublks(uINT8 *& buffer) {
  uINT8 num_ublks = p_data()->neighbor_ublks.size();
  pack_and_advance(buffer, &num_ublks);
  ccDOTIMES(iu, num_ublks) {
    UBLK_ID ublk_id = p_data()->neighbor_ublks[iu]->id();
    pack_and_advance(buffer, &ublk_id);
    VOXEL_MASK_8 voxel_mask = p_data()->neighbor_voxel_masks[iu];
    pack_and_advance(buffer, &voxel_mask);
  }
}

VOID sSAMPLING_SURFEL::unpack_neighbor_ublks(uINT8 *& buffer) {
  uINT8 num_ublks;
  unpack_and_advance(&num_ublks, buffer);
  ccDOTIMES(iu, num_ublks) {
    UBLK_ID ublk_id;
    VOXEL_MASK_8 voxel_mask;
    unpack_and_advance(&ublk_id, buffer);
    unpack_and_advance(&voxel_mask, buffer);
    UBLK ublk = ublk_from_id(ublk_id);
    if (ublk && !ublk->is_ghost()) {
      p_data()->neighbor_ublks.push_back(ublk);
      p_data()->neighbor_voxel_masks.push_back(voxel_mask);
    }
  }
}
VOID sSAMPLING_SURFEL::resolve_meas_cell_ptrs() {
  asINT32 n_windows = m_surfel_meas_data.m_n_meas_cell_ptrs;
  sSURFEL_MEAS_CELL_PTR *surfel_meas_cell_ptr = m_surfel_meas_data.m_surfel_meas_cell_ptrs;
 //TODO what about multiple meas cell pointers per window?
  ccDOTIMES(w, n_windows) {
    MEAS_WINDOW window = g_meas_windows[surfel_meas_cell_ptr->window_index()];
    STP_MEAS_CELL_INDEX meas_cell_index =
            window->m_meas_cell_index_map[surfel_meas_cell_ptr->index()].m_new_sp_cell_index;
    MEAS_CELL_VAR *meas_cell = window->meas_cell(meas_cell_index);
    surfel_meas_cell_ptr->set_variables(meas_cell);
    if (window->contains_std_dev_vars) {
      sSTD_CELL_VAR *std_cell = surfel_meas_cell_ptr->std_cell_ptr(window->n_variables);
      std_cell->add_shob();
    }
    surfel_meas_cell_ptr++;
  }
}
