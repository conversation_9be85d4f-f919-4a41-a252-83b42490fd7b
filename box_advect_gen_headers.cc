/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Mukul Rao, Exa Corporation                      Created April, 2018
//----------------------------------------------------------------------------
//
#include <list>
#include <map>
#include "lattice.h"
#include <cassert>
#include <algorithm>
#include "box_advect_gen_headers.h"
#include "vectorization_support.h"

//Used to control indentation of print statements
const char* INDENT1 = "  ";
const char* INDENT2 = "    ";
const char* INDENT3 = "      ";
const char* INDENT4 = "        ";
const char* INDENT5 = "          ";
const char* INDENT6 = "            ";

typedef enum:int {
  _2D_ = true,
  _3D_ = false,
} dimension;

asINT32 get_latvec_from_offset(const UBLK_OFFSET_TUPLE& offset) {

  STP_STATE_VEL offsets[3] = {(STP_STATE_VEL) std::get<0>(offset),
			      (STP_STATE_VEL) std::get<1>(offset),
			      (STP_STATE_VEL) std::get<2>(offset)};

  return convert_state_vel_to_latvec_index((STP_STATE_VEL*) offsets);
}

//Comparator for a ublk vector, used in sorting the contributing elements in the vector
//by lat_vec
bool sort_contributions_by_latvec(const sNEIGHBOR_CONTRIBUTION& data1,
                                  const sNEIGHBOR_CONTRIBUTION& data2){

  if (data1.latvec < data2.latvec) {
    return true;
  } else if ((data1.latvec == data2.latvec) &&
             (data1.src_voxel < data2.src_voxel)){
    return true;
  } else {
    return false;
  }
}

using UBLK_OFFSET_TUPLE_ARRAY = UBLK_OFFSET_TUPLE[N_MOVING_STATES];

//=============================================================================
// Given a destination voxel, this function finds its source along a latvec
template<STP_LATTICE_TYPE ltype>
asINT32 neighbor_voxel_along_state_direction(asINT32 voxel, asINT32 latvec) {
 throw std::string("No default template implementation exists for function neighbor_voxel_along_state_direction.");
}

template<>
asINT32 neighbor_voxel_along_state_direction<STP_LATTICE_D19>(asINT32 voxel, asINT32 latvec)
{
  sINT16 v_offsets[3], c_offsets[3];

  c_offsets[0] = state_vx(latvec);
  c_offsets[1] = state_vy(latvec);
  c_offsets[2] = state_vz(latvec);
  v_offsets[0] = ((voxel >> 2) & 1) + c_offsets[0] + 1;
  v_offsets[1] = ((voxel >> 1) & 1) + c_offsets[1] + 1;
  v_offsets[2] = ((voxel     ) & 1) + c_offsets[2] + 1;
  asINT32 neighbor_voxel = (((v_offsets[0] + 1) & 1) << 2) +
                           (((v_offsets[1] + 1) & 1) << 1) +
                           ((v_offsets[2] + 1) & 1);
  return neighbor_voxel;
}

template<>
asINT32 neighbor_voxel_along_state_direction<STP_LATTICE_D39>(asINT32 voxel, asINT32 latvec)
{
  sINT16 v_offsets[3], c_offsets[3];
  c_offsets[0] = state_vx(latvec) > 1 ? 0 : state_vx(latvec);
  c_offsets[1] = state_vy(latvec) > 1 ? 0 : state_vy(latvec);
  c_offsets[2] = state_vz(latvec) > 1 ? 0 : state_vz(latvec);
  v_offsets[0] = ((voxel >> 2) & 1) + c_offsets[0] + 1;
  v_offsets[1] = ((voxel >> 1) & 1) + c_offsets[1] + 1;
  v_offsets[2] = ((voxel     ) & 1) + c_offsets[2] + 1;
  asINT32 neighbor_voxel = (((v_offsets[0] + 1) & 1) << 2) +
                           (((v_offsets[1] + 1) & 1) << 1) +
                           ((v_offsets[2] + 1) & 1);
  return neighbor_voxel;
}


//=============================================================================
// ADD UBLK CONTRIBUTIONS
// This function identifies the neighbor source voxel and the
// neighbor ublk, given a destination voxel and a lattice direction
// It then identifies the offsets for the neighbor ublk and appends a ublk_src_data
// element to the corresponding ublk list
VOID  add_ublk_contributions(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                             UBLK_OFFSET_TUPLE_ARRAY& ublk_offset_for_each_latvec,
                             asINT32 dst_voxel,
                             asINT32 latvec,
                             asINT32 latvec_0,
                             bool is_2D,
                             STP_LATTICE_TYPE ltype){

  //To determine ublk offsets given voxel number
  const static sINT16 ublk_voxel_offset_map[6] = {-1, -1, 0, 0, 1, 1};

  UBLK_OFFSET_TUPLE ublk_offset;
  asINT32 neighbor_voxel;
  if (ltype == STP_LATTICE_D19)
    neighbor_voxel = neighbor_voxel_along_state_direction<STP_LATTICE_D19>(dst_voxel, latvec_0);
  else if (ltype == STP_LATTICE_D39){
    neighbor_voxel = neighbor_voxel_along_state_direction<STP_LATTICE_D39>(dst_voxel, latvec_0);
  }


  if ( latvec == latvec_0 ) {
    // These are coordinates of a voxel in ublk + MAX_STATE
    // This is done to make the smallest value 0, since this is later used as an
    // index in the ublk_voxel_offset_map.
    auINT16 voxel_cs[3];
    voxel_cs[0] = ((dst_voxel >> 2) & 1) + 2;
    voxel_cs[1] = ((dst_voxel >> 1) & 1) + 2;
    voxel_cs[2] = ((dst_voxel     ) & 1) + 2;

    sINT16 v_offsets[3] = {0};

    v_offsets[0] = state_vx(latvec_0) + voxel_cs[0];
    v_offsets[1] = state_vy(latvec_0) + voxel_cs[1];
    v_offsets[2] = state_vz(latvec_0) + voxel_cs[2];

    sINT16 u_offsets[3] = {0};
    u_offsets[0] = ublk_voxel_offset_map[v_offsets[0]];
    u_offsets[1] = ublk_voxel_offset_map[v_offsets[1]];
    u_offsets[2] = ublk_voxel_offset_map[v_offsets[2]];

    if (is_2D) { //Exit if ublk or voxel is out of plane
      if ((u_offsets[2] != 0) || (neighbor_voxel & 0x1)) {
        return;
      }
    }

    std::get<0>(ublk_offset) = u_offsets[0];
    std::get<1>(ublk_offset) = u_offsets[1];
    std::get<2>(ublk_offset) = u_offsets[2];
    ublk_offset_for_each_latvec[latvec] = ublk_offset;
  }
  else
  {
    ublk_offset = ublk_offset_for_each_latvec[latvec] =  ublk_offset_for_each_latvec[latvec_0];
  }

  sNEIGHBOR_CONTRIBUTION ublk_src_data;
  ublk_src_data.src_voxel = neighbor_voxel;
  ublk_src_data.dst_voxel = dst_voxel;
  ublk_src_data.latvec = latvec_parity(latvec);
  ublk_offset_map[ublk_offset].emplace_back(ublk_src_data);
}

//=============================================================================
// Certain latvec's that have non-zero z states, contribute along their projected
// directions for 2D cases. This function returns the projected lattice direction
asINT32 flatten_latvec_for_2D(asINT32 latvec){
  asINT32 latvec_0 = latvec;
  if (state_vx(latvec) && state_vy(latvec)) {
    //24, 26 ---> 12
    //25, 27 ---> 13
    //28, 30 ---> 14
    //29, 31 ---> 15
    switch (latvec) {
    case 24:
    case 26:
      latvec_0 = 12;
      break;
    case 25:
    case 27:
      latvec_0 = 13;
      break;
    case 28:
    case 30:
      latvec_0 = 14;
      break;
    case 29:
    case 31:
      latvec_0 = 15;
      break;
    }
  }
  else if (state_vx(latvec)) {
    // assumes first two states are in x direction
    latvec_0 = (1 - state_vx(latvec)) >> 1;
  }
  else if (state_vy(latvec)) {
    // assumes next two states are in y direction
    latvec_0 = ((1 - state_vy(latvec)) >> 1) + 2;
  }
  return latvec_0;
}


//=============================================================================
VOID compute_ublk_neighbor_contributions(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                         BOOLEAN is_2D,
                                         STP_LATTICE_TYPE ltype) {
  asINT32 parity;
  asINT32 latvec_0;
  ccDOTIMES(voxel,ubFLOAT::N_VOXELS){
    //Don't count odd voxels for 2D
    if ( is_2D && (voxel & 0x1) ){
      continue;
    }

    UBLK_OFFSET_TUPLE_ARRAY ublk_offset_for_each_latvec;

    ccDOTIMES(latvec,N_MOVING_STATES){
      asINT32 latvec_0 = latvec;
      asINT32 parity = state_parity(latvec);
      if ( is_2D && (state_vz(parity) != 0)) {
        latvec_0 = flatten_latvec_for_2D(latvec);
      }
      add_ublk_contributions(ublk_offset_map, ublk_offset_for_each_latvec,
                             voxel, latvec, latvec_0, is_2D, ltype);
    }//ccDOTIMES latvec
  }//ccDOTIMES voxels

  //Sort all contributions by latvec
  for(auto& ublk_contributions_pair : ublk_offset_map){
    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_contributions = ublk_contributions_pair.second;
    std::sort(ublk_contributions.begin(),ublk_contributions.end(),sort_contributions_by_latvec);
  }
}


#if defined(BOX_ADVECT_GEN_FILES)

//=============================================================================
// Prints src voxels
VOID print_src_voxel_list(std::ofstream& ofs,
                          std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector,
                          int start_index,
                          int end_index){

  assert(start_index < end_index);
  auto start_iterator = ublk_src_data_vector.begin() + start_index;
  auto end_iterator   = ublk_src_data_vector.begin() + end_index;
  assert(start_iterator < end_iterator);
  ofs << INDENT5 << "constexpr asINT32 src_voxels[] = {";
  while ( start_iterator != end_iterator ) {
    if ( (start_iterator+1) == end_iterator ) {
     ofs << (*start_iterator).src_voxel;
    } else {
      ofs << (*start_iterator).src_voxel << ",";
    }
    start_iterator++;
  }
  ofs << "};" << std::endl;
}

//=============================================================================
// Prints dst voxels
VOID print_dst_voxel_list(std::ofstream& ofs,
                          std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector,
                          int start_index,
                          int end_index){

  assert(start_index < end_index);
  auto start_iterator = ublk_src_data_vector.begin() + start_index;
  auto end_iterator   = ublk_src_data_vector.begin() + end_index;
  assert(start_iterator < end_iterator);
  ofs << INDENT5 << "constexpr asINT32 dest_voxels[] = {";
  while ( start_iterator != end_iterator ) {
    if ( (start_iterator+1) == end_iterator ) {
     ofs << (*start_iterator).dst_voxel;
    } else {
      ofs << (*start_iterator).dst_voxel << ",";
    }
    start_iterator++;
  }
  ofs <<"};" << std::endl;
}

//=============================================================================
// Prints neighbor ublk's offsets
std::ostream& operator<<(std::ostream& ofs,UBLK_OFFSET_TUPLE& offsets){
  ofs << INDENT1
      << "constexpr sINT16 offsets[3] = {"
      << std::get<0>(offsets) << ","
      << std::get<1>(offsets) << ","
      << std::get<2>(offsets) << "}; \n";

  return ofs;
}


//=============================================================================
//List of different printer tags for combinations of lattice type, dimensionality
// and advection algorithm type
typedef enum:int {
  unset_printer_type = -1,
  D19_2D_gather_advect,
  D19_2D_swap_advect,
  D19_3D_gather_advect,
  D19_3D_swap_advect,
  D39_2D_gather_advect,
  D39_2D_swap_advect,
  D39_3D_gather_advect,
  D39_3D_swap_advect,
  D19_2D_split_advect,
  D19_3D_split_advect,
  D39_2D_split_advect,
  D39_3D_split_advect,
} advect_printer_type;

//=============================================================================
//All printer objects implement the ADVECT_PRINTER_INTERFACE
//The idea is that different printer objects will specialize what happens for a
//given source-destination voxel pair, along a certain lat-vec. Depending on the
//dimensionality and lattice type, the ublk_offset_map is fixed and will not change
//among different printer objects that have the same dimensionality and lattice type.
typedef class ADVECT_PRINTER_INTERFACE {

public:

  //Constructor
  ADVECT_PRINTER_INTERFACE(const std::string& fname,
                           dimension d,
                           advect_printer_type tag,
                           STP_LATTICE_TYPE ltype
                           ):file_name(fname),
                             dim(d),TAG(tag),
                             ltype(ltype),
                             ofs(std::ofstream())
 {
     ofs.open(fname.c_str());
     if ( !ofs.is_open() || !ofs.good() ){
       throw (std::string("Could not open file - ") + file_name);
     }
     init_state_vel_map();
  }

  //Dispose of file handle on destruction
  virtual ~ADVECT_PRINTER_INTERFACE(){ofs.close();};

  /* PRINT TO FILE
   *This is the main print routine called by clients
   *Concrete objects that implement this interface override the many
   *sub functions of print_to_file.
   *The structure of the file is described below
   *
   * Some file.cc
   *
   * File includes and globals //written by print_file_header();
   *
   * VOID some_func(args) {    //written by print_function_signature();
   *
   *   var_type globals;       //written by print_function_globals();
   *
   *   For each ublk offset    //See print_ublk_neighbor_contributions
   *   if (neighbor is valid)
   *   {
   *      local vars;          //written by print_local_variables_for_neighbor()
   *
   *                           //written by print_contributions_for_offset
   *
   *   }
   *
   *
   *
   *
   *   }//some_func            //written by print_function_closure();
   *
   *  File closure statements  //written by print_file_closure();
   */
  void print_to_file(){

    print_file_header();
    print_function_signature();
    print_function_globals();

    //Note that ublk_offset_map is fixed by dimensionality and lattice type
    //No printing is done in this step
    UBLK_OFFSET_CONTRIBUTIONS_MAP ublk_offset_map;
    compute_ublk_neighbor_contributions(ublk_offset_map, dim, ltype);

    print_ublk_neighbor_contributions(ublk_offset_map);
    print_function_closure();
    print_file_closure();
  }

private:

  //Generic includes, concrete objects can choose to include
  //more files if need be
  virtual void print_file_header() {
    ofs << "#include \"../common_sp.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl
        << "#include \"../box_advect.h\""<<std::endl <<std::endl;
  }

  virtual void print_file_closure() {
  }

  virtual void print_function_signature() = 0;
  virtual void print_function_globals() = 0;
  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) = 0;
  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE offset) = 0;
  virtual void print_function_closure() = 0;

  void print_ublk_neighbor_contributions(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map){
    for ( int i = -1; i < N_MOVING_STATES; i++) {
      UBLK_OFFSET_TUPLE ublk_offset;

      if ( i == -1 ) { //Non-moving states
        ublk_offset = UBLK_OFFSET_TUPLE(0, 0, 0);
      } else {
        ublk_offset = UBLK_OFFSET_TUPLE(state_vx(i), state_vy(i), state_vz(i));
      }

      //We can loop over all lattice directions, but not all offsets are keys
      //for example in 2D, not all ublk offsets are keys
      if (ublk_offset_map.count(ublk_offset)) {
        ofs << "//================================================================================\n";
        ofs << INDENT1 <<"{" << std::endl;
        ofs <<  ublk_offset;

        print_local_variables_for_neighbor(ublk_offset);

	if (i == -1) {
	  ofs << INDENT2 << "{ //SELF\n";
	} else {
	  ofs << INDENT2 << "if (is_src_ublk_valid) {\n";
	}        

        print_contributions_for_offset(ublk_offset_map, ublk_offset);

        ofs  << INDENT2 << "}//is_src_ublk_valid\n";
        ofs  << INDENT1 << "}//" << ublk_offset << std::endl;
      }
    }
  }

protected:
  std::string file_name;
  dimension dim;
  advect_printer_type TAG;
  STP_LATTICE_TYPE ltype;
  std::ofstream ofs;

} advect_printer_interface;

//=============================================================================
typedef class GATHER_ADVECT_PRINTER : public ADVECT_PRINTER_INTERFACE {

protected:
  GATHER_ADVECT_PRINTER(const std::string& fname):
    ADVECT_PRINTER_INTERFACE(fname, _3D_, unset_printer_type, STP_INVALID_LATTICE_TYPE){

  }

  virtual void print_file_header() override {
    ofs << "#include \"../common_sp.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl
        << "#include \"../gather_advect.h\""<<std::endl <<std::endl;
  }


  virtual void print_function_globals() override {
    ofs  << INDENT1 << "VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;\n"
	 << INDENT1 << "VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};\n";
  }

  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) override {
    ofs << INDENT1 << "src_states = src_states_t = src_states_mc = NULL;\n";
    ofs << INDENT1 << "if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}\n";
    ofs << INDENT1 << "TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);\n";
    const asINT32 latvec = get_latvec_from_offset(offset);
    if (latvec != V_0_0_0) {
      ofs << INDENT1 << "BOOLEAN is_src_ublk_valid = advect_nbrs["
	           << latvec
	           << "] != nullptr;\n";    
    }
    
  }

  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE ublk_offset) override {

    ofs << INDENT3 << "FILL_PREV_STATES_FOR_NEIGHBOR_UBLK\n";

    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector = ublk_offset_map[ublk_offset];
    size_t list_size = ublk_src_data_vector.size();
    asINT32 current_lat_vec = ublk_src_data_vector.front().latvec;

    int count = 0, start_index = 0;

    for ( auto list_element : ublk_src_data_vector) {

      if (list_element.latvec != current_lat_vec){
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 n_src_voxels = " << count - start_index << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        start_index = count;
        current_lat_vec = list_element.latvec;
        ofs  << INDENT5 << "APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC \n";
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS \n";
        ofs  << INDENT4 << "}\n";
      }
      if (count + 1 == list_size) {
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 n_src_voxels = " << count - start_index + 1 << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);
        ofs  << INDENT5 << "APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC \n";
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS \n";
        ofs  << INDENT4 << "}\n";
      }

      count++;
    }

  }

  virtual void print_function_closure() override {
    ofs << "}\n\n";
  }

} GATHER_ADVECT_PRINTER;

//=============================================================================
typedef class D19_2D_GATHER_ADVECT_PRINTER : public GATHER_ADVECT_PRINTER {
public:
  D19_2D_GATHER_ADVECT_PRINTER(const std::string& fname):
    GATHER_ADVECT_PRINTER(fname){

    //Override dim and TAG
    dim = _2D_;
    TAG = D19_2D_gather_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
} D19_2D_GATHER_ADVECT_PRINTER;

//=============================================================================
typedef class D19_3D_GATHER_ADVECT_PRINTER : public GATHER_ADVECT_PRINTER {
public:
  D19_3D_GATHER_ADVECT_PRINTER(const std::string& fname):
    GATHER_ADVECT_PRINTER(fname){

    //Override dim and TAG
    dim = _3D_;
    TAG = D19_3D_gather_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_collect_advect_states_3D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
} D19_3D_GATHER_ADVECT_PRINTER;

//=============================================================================
typedef class D39_2D_GATHER_ADVECT_PRINTER : public GATHER_ADVECT_PRINTER {
public:
  D39_2D_GATHER_ADVECT_PRINTER(const std::string& fname):
    GATHER_ADVECT_PRINTER(fname){

    //Override dim and TAG
    dim = _2D_;
    TAG = D39_2D_gather_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
} D39_2D_GATHER_ADVECT_PRINTER;

//=============================================================================
typedef class D39_3D_GATHER_ADVECT_PRINTER : public GATHER_ADVECT_PRINTER {
public:
  D39_3D_GATHER_ADVECT_PRINTER(const std::string& fname):
    GATHER_ADVECT_PRINTER(fname){

    //Override dim and TAG
    dim = _3D_;
    TAG = D19_3D_gather_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_collect_advect_states_3D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
} D39_3D_GATHER_ADVECT_PRINTER;

//=============================================================================
class GATHER_SPLIT_ADVECT_PRINTER : public ADVECT_PRINTER_INTERFACE {

protected:
  GATHER_SPLIT_ADVECT_PRINTER(const std::string& fname):
    ADVECT_PRINTER_INTERFACE(fname, _3D_, unset_printer_type, STP_INVALID_LATTICE_TYPE){

  }

  virtual void print_file_header() override {
    ofs << "#include \"../common_sp.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl
        << "#include \"../gather_advect.h\""<<std::endl <<std::endl;
  }


  virtual void print_function_globals() override {
    ofs  << INDENT1 << "VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;\n"
	 << INDENT1 << "VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};\n";
  }

  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) override {
    ofs << INDENT1 << "src_states = src_states_t = src_states_mc = NULL;\n";
    ofs << INDENT1 << "if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}\n";
    ofs << INDENT1 << "TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);\n";
    ofs << INDENT1 << "BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);\n";
    ofs << INDENT1 << "BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();\n";
  }

  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE ublk_offset) override {

    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector = ublk_offset_map[ublk_offset];
    size_t list_size = ublk_src_data_vector.size();
    asINT32 current_lat_vec = ublk_src_data_vector.front().latvec;

    int count = 0, start_index = 0;

    for ( auto list_element : ublk_src_data_vector) {

      if (list_element.latvec != current_lat_vec){
        ofs << INDENT3 << "{" << std::endl;
        ofs << INDENT5<< "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5<< "constexpr asINT32 n_src_voxels = " << count - start_index << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        start_index = count;
        current_lat_vec = list_element.latvec;
        ofs  << INDENT5 << "if(is_src_ublk_split) {\n";
        ofs  << INDENT6 << "APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC\n";
        ofs  << INDENT5 << "} else {\n";
        ofs  << INDENT6 << "FILL_PREV_STATES_FOR_NEIGHBOR_UBLK \n";
        ofs  << INDENT6 << "APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC \n";
        ofs  << INDENT5 << "}\n";
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT\n";
        ofs  << INDENT3 << "}\n";
      }
      if (count + 1 == list_size) {
        ofs << INDENT3 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 n_src_voxels = " << count - start_index + 1 << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);

        ofs  << INDENT5 << "if(is_src_ublk_split) {\n";
        ofs  << INDENT6 << "APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC\n";
        ofs  << INDENT5 << "} else {\n";
        ofs  << INDENT6 << "FILL_PREV_STATES_FOR_NEIGHBOR_UBLK \n";
        ofs  << INDENT6 << "APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC \n";
        ofs  << INDENT5 << "}\n";
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT\n";
        ofs  << INDENT3 << "}\n";
      }

      count++;
    }

  }

  virtual void print_function_closure() override {
    ofs << "split_advect_factor->reset();\n";
    ofs << "}\n\n";
  }
};

//=============================================================================
class D19_2D_GATHER_SPLIT_ADVECT_PRINTER : public GATHER_SPLIT_ADVECT_PRINTER {

public:
  D19_2D_GATHER_SPLIT_ADVECT_PRINTER(const std::string& fname):
    GATHER_SPLIT_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D19_2D_split_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SPLIT_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_split_advect_2D(SPLIT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D19_3D_GATHER_SPLIT_ADVECT_PRINTER : public GATHER_SPLIT_ADVECT_PRINTER {

public:
  D19_3D_GATHER_SPLIT_ADVECT_PRINTER(const std::string& fname):
    GATHER_SPLIT_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D19_3D_split_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SPLIT_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_split_advect_3D(SPLIT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D39_2D_GATHER_SPLIT_ADVECT_PRINTER : public GATHER_SPLIT_ADVECT_PRINTER {

public:
  D39_2D_GATHER_SPLIT_ADVECT_PRINTER(const std::string& fname):
    GATHER_SPLIT_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D39_2D_split_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SPLIT_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_split_advect_2D(SPLIT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D39_3D_GATHER_SPLIT_ADVECT_PRINTER : public GATHER_SPLIT_ADVECT_PRINTER {

public:
  D39_3D_GATHER_SPLIT_ADVECT_PRINTER(const std::string& fname):
    GATHER_SPLIT_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D39_3D_split_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SPLIT_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_split_advect_3D(SPLIT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

static BOOLEAN is_offset_eligible_for_self_ublk_swap_check(const UBLK_OFFSET_TUPLE& offset) {
  asINT32 x = std::get<0>(offset);
  asINT32 y = std::get<1>(offset);
  asINT32 z = std::get<2>(offset);

  return (x == 1 && y == 0 && z == 0) ||
	 (x == 0 && y == 1 && z == 0) ||
	 (x == 0 && y == 0 && z == 1) ;
}

static bool should_skip_alternate_contributions(const UBLK_OFFSET_TUPLE offsets){
 return (std::get<0>(offsets) == 0) &&
	(std::get<1>(offsets) == 0) &&
	(std::get<2>(offsets) == 0);
}

//=============================================================================
typedef class SWAP_ADVECT_PRINTER : public ADVECT_PRINTER_INTERFACE {

protected:
  SWAP_ADVECT_PRINTER(const std::string& fname):
      ADVECT_PRINTER_INTERFACE(fname, _3D_,
                               unset_printer_type, STP_INVALID_LATTICE_TYPE){

  }

  virtual void print_file_header() override {
    ofs << "#include \"../common_sp.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl
        << "#include \"../swap_advect.h\""<<std::endl <<std::endl;
  }

  virtual void print_function_globals() override {
    ofs  << INDENT1 << "VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;\n"
	 << INDENT1 << "VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};\n"
         << INDENT1 << "VOXEL_STATE (*curr_src_states)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*curr_src_states_t)[ubFLOAT::N_VOXELS] = NULL;\n"
         << INDENT1 << "VOXEL_STATE (*curr_src_states_mc)[ubFLOAT::N_VOXELS] = NULL;\n"
	 << INDENT1 << "VOXEL_STATE (*curr_src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};\n";
  }

  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) override {
    ofs << INDENT1 << "src_states = src_states_t = src_states_mc = NULL;\n";
    ofs << INDENT1 << "curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;\n";
    ofs << INDENT1 << "if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}\n";
    ofs << INDENT1 << "TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);\n";
    const asINT32 latvec = get_latvec_from_offset(offset);
    if (latvec != V_0_0_0) {
      ofs << INDENT1 << "BOOLEAN is_src_ublk_valid = advect_nbrs["
	           << latvec
	           << "] != nullptr;\n";    
    }
    
  }

  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE ublk_offset) override {


    ofs << INDENT3 << "FILL_PREV_STATES_FOR_NEIGHBOR_UBLK\n";
    ofs << INDENT3 << "BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();\n";

    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector = ublk_offset_map[ublk_offset];
    size_t list_size = ublk_src_data_vector.size();
    asINT32 current_lat_vec = ublk_src_data_vector.front().latvec;
    bool skip_alt_latvecs = should_skip_alternate_contributions(ublk_offset);
    int count = 0, start_index = 0;

    for ( auto list_element : ublk_src_data_vector) {
      //For swap advection we skip alternate latvecs for intra ublk
      //contributions
      if (skip_alt_latvecs && (current_lat_vec & 0x1)) {
        start_index = count;
        current_lat_vec = list_element.latvec;
        count++;
        continue;
      }

      if (list_element.latvec != current_lat_vec){
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 n_src_voxels = " << count - start_index << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count);
        start_index = count;
        current_lat_vec = list_element.latvec;
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES \n";
        ofs  << INDENT5 << "SWAP_VOXEL_STATES_WITH_NEIGHBOR \n";
        ofs  << INDENT4 << "}\n";
      }
      if ( count + 1  == list_size ) {
        if (skip_alt_latvecs && (current_lat_vec & 0x1)){ continue; };
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 n_src_voxels = " << count - start_index + 1 << ";" << std::endl;
        print_src_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);
        print_dst_voxel_list(ofs, ublk_src_data_vector, start_index, count + 1);
        ofs  << INDENT5 << "PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES \n";
        ofs  << INDENT5 << "SWAP_VOXEL_STATES_WITH_NEIGHBOR \n";
        ofs  << INDENT4 << "}\n";
      }

      count++;
    }

  }

  virtual void print_function_closure() override {
    ofs << "swap_along_latvec_pairs<ADVECT_TEMP, ADVECT_UDS>(states,states_t,states_mc, states_uds);\n";
    ofs << "}\n\n";
  }

} SWAP_ADVECT_PRINTER;

//=============================================================================
typedef class D19_2D_SWAP_ADVECT_PRINTER : public SWAP_ADVECT_PRINTER {
public:
  D19_2D_SWAP_ADVECT_PRINTER(const std::string& fname):
    SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D19_2D_swap_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_swap_advect_states_2D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
} D19_2D_SWAP_ADVECT_PRINTER;

//=============================================================================
typedef class D19_3D_SWAP_ADVECT_PRINTER : public SWAP_ADVECT_PRINTER {
public:
  D19_3D_SWAP_ADVECT_PRINTER(const std::string& fname):
    SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D19_3D_swap_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d19_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
} D19_3D_SWAP_ADVECT_PRINTER;

//=============================================================================
typedef class D39_2D_SWAP_ADVECT_PRINTER : public SWAP_ADVECT_PRINTER {
public:
  D39_2D_SWAP_ADVECT_PRINTER(const std::string& fname):
    SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D39_2D_swap_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_swap_advect_states_2D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
} D39_2D_SWAP_ADVECT_PRINTER;

//=============================================================================
typedef class D39_3D_SWAP_ADVECT_PRINTER : public SWAP_ADVECT_PRINTER {
public:
  D39_3D_SWAP_ADVECT_PRINTER(const std::string& fname):
    SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D39_3D_swap_advect;
    ltype = STP_LATTICE_D39;
  }
private:

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "VOID d39_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
} D39_3D_SWAP_ADVECT_PRINTER;



//=============================================================================
// Prints vectorization masks for src-dst voxel pairs

#include <bitset>

VOID print_voxel_masks(std::ofstream& ofs,
		       std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector,
		       int start_index,
		       int end_index,
		       BOOLEAN for_src){

  assert(start_index < end_index);
  auto start_iterator = ublk_src_data_vector.begin() + start_index;
  auto end_iterator   = ublk_src_data_vector.begin() + end_index;
  assert(start_iterator < end_iterator);

  asINT32 perm_indices[] = {0,1,2,3,4,5,6,7};
  
  auINT8 mask = 0x0;
  
  while ( start_iterator != end_iterator ) {
    asINT32 src_voxel = for_src? (*start_iterator).src_voxel : (*start_iterator).dst_voxel;
    asINT32 dst_voxel = for_src? (*start_iterator).dst_voxel : (*start_iterator).src_voxel;
    
    mask |= (0x1 << src_voxel);
    perm_indices[dst_voxel] = src_voxel;
    start_iterator++;
  }

  //Print bit-masks
  ofs << INDENT5
      << "constexpr auINT8 "
      << (for_src? "src_voxel_mask " : "dst_voxel_mask ")
      <<"= 0b"<< std::bitset<8>(mask)
      << ";\n";

  //Print permutation indices
  ofs << INDENT5
      << "const     __m256i "
      << (for_src? "src_perm_ind = " : "dst_perm_ind = ")
      << "PERM_IND_PS(";

  for (int i = 7; i > 0 ; i--) {
    ofs << perm_indices[i] << ", ";
  }

  ofs << perm_indices[0] << ");\n";

}

//=============================================================================
class AVX_GATHER_ADVECT_PRINTER : public ADVECT_PRINTER_INTERFACE {

public:
  AVX_GATHER_ADVECT_PRINTER(const std::string& fname):
    ADVECT_PRINTER_INTERFACE(fname, _3D_, unset_printer_type, STP_INVALID_LATTICE_TYPE){    
  }

  virtual void print_file_header() override {
    ofs << "#include \"../common_sp.h\""<<std::endl
	<< "#include \"../gather_advect.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl;
  }


  virtual void print_function_globals() override {
    ofs << "const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);\n"
	<<"const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);\n"
	<<"const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);\n"
        <<"constexpr asINT32 voxor = 0;\n";
  }

  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) override {

    const asINT32 latvec = get_latvec_from_offset(offset);
    
    if (latvec != V_0_0_0) {
      ofs << INDENT1 << "BOOLEAN is_src_ublk_valid = advect_nbrs["
	           << latvec
	           << "] != nullptr;\n";    
    }
  }

  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE ublk_offset) override {                    

    const asINT32 latvec = get_latvec_from_offset(ublk_offset);
    
    if (latvec != V_0_0_0) {
      ofs << INDENT3 << "sUBLK*  src_ublk = advect_nbrs["<< latvec << "];\n";
    } else {
      ofs << INDENT3 << "sUBLK*  src_ublk = pde_advect_ublk;\n";
    }
    
    ofs << INDENT3 << "BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();\n";

    ofs << INDENT3 << "if (src_tagged_ublk_has_two_copies){\n" 
	<< INDENT4 << "explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>"
                      "(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);\n"        
	<< INDENT3 << "}\n";
    
    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector = ublk_offset_map[ublk_offset];
    size_t list_size = ublk_src_data_vector.size();
    asINT32 current_lat_vec = ublk_src_data_vector.front().latvec;

    int count = 0, start_index = 0;

    for ( auto list_element : ublk_src_data_vector) {

      if (list_element.latvec != current_lat_vec){
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;        
        print_voxel_masks(ofs, ublk_src_data_vector, start_index, count, TRUE /*src*/);
	print_voxel_masks(ofs, ublk_src_data_vector, start_index, count, FALSE /*dst*/);
        start_index = count;
        current_lat_vec = list_element.latvec;
        ofs  << INDENT5 << "APPEND_V2V_VECTORIZED\n";
        ofs  << INDENT4 << "}\n";
      }
      if (count + 1 == list_size) {
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;        
        print_voxel_masks(ofs, ublk_src_data_vector, start_index, count + 1, TRUE /*src*/);
	print_voxel_masks(ofs, ublk_src_data_vector, start_index, count + 1, FALSE /*dst*/);
        ofs  << INDENT5 << "APPEND_V2V_VECTORIZED\n";
        ofs  << INDENT4 << "}\n";
      }

      count++;
    }

  }

  virtual void print_function_closure() override {
    ofs << "}\n\n";
  }

};


//=============================================================================
class D19_AVX_3D_GATHER_ADVECT_PRINTER : public AVX_GATHER_ADVECT_PRINTER {

public:
  D19_AVX_3D_GATHER_ADVECT_PRINTER(const std::string& fname):
    AVX_GATHER_ADVECT_PRINTER (fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D19_3D_gather_advect;
    ltype = STP_LATTICE_D19;
  }
  
  virtual void print_function_signature() override {

    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d19_avx_collect_advect_states_3D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D19_AVX_2D_GATHER_ADVECT_PRINTER : public AVX_GATHER_ADVECT_PRINTER {

public:
  D19_AVX_2D_GATHER_ADVECT_PRINTER(const std::string& fname):
    AVX_GATHER_ADVECT_PRINTER (fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D19_2D_gather_advect;
    ltype = STP_LATTICE_D19;
  }
  
  virtual void print_function_signature() override {

    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d19_avx_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D39_AVX_3D_GATHER_ADVECT_PRINTER : public AVX_GATHER_ADVECT_PRINTER {

public:
  D39_AVX_3D_GATHER_ADVECT_PRINTER(const std::string& fname):
    AVX_GATHER_ADVECT_PRINTER (fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D39_3D_gather_advect;
    ltype = STP_LATTICE_D39;
  }
  
  virtual void print_function_signature() override {

    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d39_avx_collect_advect_states_3D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D39_AVX_2D_GATHER_ADVECT_PRINTER : public AVX_GATHER_ADVECT_PRINTER {

public:
  D39_AVX_2D_GATHER_ADVECT_PRINTER(const std::string& fname):
    AVX_GATHER_ADVECT_PRINTER (fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D39_2D_gather_advect;
    ltype = STP_LATTICE_D39;
  }
  
  virtual void print_function_signature() override {

    ofs  << "template <BOX_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d39_avx_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class AVX_SWAP_ADVECT_PRINTER : public ADVECT_PRINTER_INTERFACE {

public:
  AVX_SWAP_ADVECT_PRINTER(const std::string& fname):
    ADVECT_PRINTER_INTERFACE(fname, _3D_, unset_printer_type, STP_INVALID_LATTICE_TYPE){
  }

  
  virtual void print_file_header() override {
    ofs << "#include \"../common_sp.h\""<<std::endl
	<< "#include \"../swap_advect.h\""<<std::endl
        << "#include \"../box_advect_gen_headers.h\""<<std::endl;
  }
  

  virtual void print_function_globals() override {
    ofs << "const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);\n"
	<<"const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);\n"
	<<"const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);\n"
	<<"constexpr asINT32 voxor = 0;\n";
  }

  virtual void print_local_variables_for_neighbor(const UBLK_OFFSET_TUPLE& offset) override {

    const asINT32 latvec = get_latvec_from_offset(offset);
    
    if (latvec != V_0_0_0) {
      ofs << INDENT1 << "BOOLEAN is_src_ublk_valid = advect_nbrs["
	           << latvec
	           << "] != nullptr;\n";    
    }
  }

  virtual void print_contributions_for_offset(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                              const UBLK_OFFSET_TUPLE ublk_offset) override {                    

    const asINT32 latvec = get_latvec_from_offset(ublk_offset);

    if (latvec != V_0_0_0) {
      ofs << INDENT3 << "sUBLK*  src_ublk = advect_nbrs["<< latvec << "];\n";
      ofs << INDENT3 << "BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();\n";
    } else {
      ofs << INDENT3 << "sUBLK*  src_ublk = pde_advect_ublk;\n";
      ofs << INDENT3 << "constexpr BOOLEAN src_tagged_ublk_has_two_copies = FALSE;\n";
    }    

    if (latvec != V_0_0_0) {
      ofs << INDENT3 << "if (HAS_TWO_COPY_NBR && src_tagged_ublk_has_two_copies){\n" 
	  << INDENT4 << "explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>"
                	"(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);\n"        
	  << INDENT3 << "}\n";

    }
 	
    std::vector<sNEIGHBOR_CONTRIBUTION>& ublk_src_data_vector = ublk_offset_map[ublk_offset];
    size_t list_size = ublk_src_data_vector.size();

    asINT32 current_lat_vec = ublk_src_data_vector.front().latvec;
    bool skip_alt_latvecs = should_skip_alternate_contributions(ublk_offset);
    int count = 0, start_index = 0;

    for ( auto list_element : ublk_src_data_vector) {
      //For swap advection we skip alternate latvecs for intra ublk
      //contributions
      if (skip_alt_latvecs && (current_lat_vec & 0x1)) {
        start_index = count;
        current_lat_vec = list_element.latvec;
        count++;
        continue;
      }

      if (list_element.latvec != current_lat_vec){
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;        
        print_voxel_masks(ofs, ublk_src_data_vector, start_index, count, TRUE /*src*/);
	print_voxel_masks(ofs, ublk_src_data_vector, start_index, count, FALSE /*dst*/);
        start_index = count;
        current_lat_vec = list_element.latvec;
        ofs  << INDENT5 << "SWAP_V2V_VECTORIZED\n";
        ofs  << INDENT4 << "}\n";
      }
      if ( count + 1  == list_size ) {
        if (skip_alt_latvecs && (current_lat_vec & 0x1)){ continue; };
        ofs << INDENT4 << "{" << std::endl;
        ofs << INDENT5 << "constexpr asINT32 latvec = " << current_lat_vec << ";" << std::endl;        
        print_voxel_masks(ofs, ublk_src_data_vector, start_index, count + 1, TRUE /*src*/);
	print_voxel_masks(ofs, ublk_src_data_vector, start_index, count + 1, FALSE /*dst*/);
        ofs  << INDENT5 << "SWAP_V2V_VECTORIZED\n";
        ofs  << INDENT4 << "}\n";
      }

      count++;
    }    

  }

  virtual void print_function_closure() override {
    ofs << "swap_along_latvec_pairs<ADVECT_TEMP, ADVECT_UDS>(states,states_t,states_mc, states_uds);\n";
    ofs << "}\n\n";
  }

};

//=============================================================================
class D19_AVX_2D_SWAP_ADVECT_PRINTER : public AVX_SWAP_ADVECT_PRINTER {
public:
  D19_AVX_2D_SWAP_ADVECT_PRINTER(const std::string& fname):
    AVX_SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D19_2D_swap_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d19_avx_swap_advect_states_2D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
} ;

//=============================================================================
class D19_AVX_3D_SWAP_ADVECT_PRINTER : public AVX_SWAP_ADVECT_PRINTER {
public:
  D19_AVX_3D_SWAP_ADVECT_PRINTER(const std::string& fname):
    AVX_SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D19_3D_swap_advect;
    ltype = STP_LATTICE_D19;
  }

  virtual void print_function_signature() override {
        ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
        ofs  << "static INLINE \n VOID d19_avx_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
    //    ofs  << "{ \n\n";
  }
};

//=============================================================================
class D39_AVX_2D_SWAP_ADVECT_PRINTER : public AVX_SWAP_ADVECT_PRINTER {
public:
  D39_AVX_2D_SWAP_ADVECT_PRINTER(const std::string& fname):
    AVX_SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _2D_;
    TAG   =  D39_2D_swap_advect;
    ltype = STP_LATTICE_D39;
  }

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d39_avx_swap_advect_states_2D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
};

//=============================================================================
class D39_AVX_3D_SWAP_ADVECT_PRINTER : public AVX_SWAP_ADVECT_PRINTER {
public:
  D39_AVX_3D_SWAP_ADVECT_PRINTER(const std::string& fname):
    AVX_SWAP_ADVECT_PRINTER(fname){
    //Override dim and TAG
    dim   = _3D_;
    TAG   =  D39_3D_swap_advect;
    ltype = STP_LATTICE_D39;
  }
private:

  virtual void print_function_signature() override {
    ofs  << "template <SWAP_ADVECT_TEMPLATE_PARAMETERS>" << std::endl;
    ofs  << "static INLINE \n VOID d39_avx_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { \n\n";
  }
};


//=============================================================================
int main(void) {

  //Code below could be replace by a factory idiom
#if BUILD_D39_LATTICE

#if EXA_USE_AVX && !(BUILD_DOUBLE_PRECISION)
  D39_AVX_2D_SWAP_ADVECT_PRINTER("swap_advect_avx_d39_2d.cc").print_to_file();
  D39_AVX_3D_SWAP_ADVECT_PRINTER("swap_advect_avx_d39_3d.cc").print_to_file();   
  D39_AVX_3D_GATHER_ADVECT_PRINTER("box_advect_avx_d39_3d.cc").print_to_file();
  D39_AVX_2D_GATHER_ADVECT_PRINTER("box_advect_avx_d39_2d.cc").print_to_file();
  D39_2D_GATHER_ADVECT_PRINTER("box_advect_d39_2d.cc").print_to_file();
  D39_3D_GATHER_ADVECT_PRINTER("box_advect_d39_3d.cc").print_to_file();
  D39_2D_SWAP_ADVECT_PRINTER("swap_advect_d39_2d.cc").print_to_file();
  D39_3D_SWAP_ADVECT_PRINTER("swap_advect_d39_3d.cc").print_to_file();  
#else
  D39_2D_GATHER_ADVECT_PRINTER("box_advect_d39_2d.cc").print_to_file();
  D39_3D_GATHER_ADVECT_PRINTER("box_advect_d39_3d.cc").print_to_file();
  D39_2D_SWAP_ADVECT_PRINTER("swap_advect_d39_2d.cc").print_to_file();
  D39_3D_SWAP_ADVECT_PRINTER("swap_advect_d39_3d.cc").print_to_file();
#endif
   D39_2D_GATHER_SPLIT_ADVECT_PRINTER("box_split_advect_d39_2d.cc").print_to_file();
   D39_3D_GATHER_SPLIT_ADVECT_PRINTER("box_split_advect_d39_3d.cc").print_to_file();

#else //BUILD_D19_LATTICE || BUILD_5G_LATTICE

#if EXA_USE_AVX && !(BUILD_DOUBLE_PRECISION)
   D19_AVX_2D_SWAP_ADVECT_PRINTER("swap_advect_avx_d19_2d.cc").print_to_file();
   D19_AVX_3D_SWAP_ADVECT_PRINTER("swap_advect_avx_d19_3d.cc").print_to_file();   
   D19_AVX_3D_GATHER_ADVECT_PRINTER("box_advect_avx_d19_3d.cc").print_to_file();
   D19_AVX_2D_GATHER_ADVECT_PRINTER("box_advect_avx_d19_2d.cc").print_to_file();
   D19_2D_GATHER_ADVECT_PRINTER("box_advect_d19_2d.cc").print_to_file();
   D19_3D_GATHER_ADVECT_PRINTER("box_advect_d19_3d.cc").print_to_file();
   D19_2D_SWAP_ADVECT_PRINTER("swap_advect_d19_2d.cc").print_to_file();
   D19_3D_SWAP_ADVECT_PRINTER("swap_advect_d19_3d.cc").print_to_file();    
#else
   D19_2D_GATHER_ADVECT_PRINTER("box_advect_d19_2d.cc").print_to_file();
   D19_3D_GATHER_ADVECT_PRINTER("box_advect_d19_3d.cc").print_to_file();
   D19_2D_SWAP_ADVECT_PRINTER("swap_advect_d19_2d.cc").print_to_file();
   D19_3D_SWAP_ADVECT_PRINTER("swap_advect_d19_3d.cc").print_to_file();   
#endif
   D19_2D_GATHER_SPLIT_ADVECT_PRINTER("box_split_advect_d19_2d.cc").print_to_file();
   D19_3D_GATHER_SPLIT_ADVECT_PRINTER("box_split_advect_d19_3d.cc").print_to_file();

#endif
}


#endif
