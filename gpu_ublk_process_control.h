#ifndef SIMENG_GPU_UBLK_PROCESS_CONTROL_H
#define SIMENG_GPU_UBLK_PROCESS_CONTROL_H

#if BUILD_GPU

#include PHYSICS_H

namespace GPU {

VOID gather_advect(UBLK_GROUP_TYPE group_type,
                   BOOLEAN is_T_S_lb_solver_on,
		   BOOLEAN is_UDS_lb_solver_on,
                   const size_t* range,
                   BOOLEAN is_timestep_even,
                   SOLVER_INDEX_MASK prior_solver_index_mask,
                   ACTIVE_SOLVER_MASK active_solver_mask);

VOID swap_advect(UBLK_GROUP_TYPE group_type,
                 BOOLEAN is_T_S_lb_solver_on,
		 BOOLEAN is_UDS_lb_solver_on,
                 const size_t* range,
                 SOLVER_INDEX_MASK prior_solver_index_mask);

VOID set_ublk_group_range_for_scale(size_t *range, UBLK_GROUP_TYPE group_type, int scale);
VOID set_vr_coarse_explode_group_range_for_scale(size_t range[2], UBLK_GROUP_TYPE group_type, SCALE scale);



VOID ublk_dynamics(UBLK_GROUP_TYPE group_type,
                   STP_SCALE scale,
                   const size_t* range,
                   FLUID_DYN_DCACHE dcache,
                   bool doMeasurements,
                   bool group_has_voxels_with_special_fluid);

VOID prepare_ublk_group_for_next_ts(UBLK_GROUP_TYPE group_type,
                                    const size_t* range,
                                    BOOLEAN is_LB_active,
                                    SOLVER_INDEX_MASK prior_solver_index_mask,
                                    ACTIVE_SOLVER_MASK active_solver_mask);
}
#endif

#endif
