/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "strand_mgr.h"
#include "status.h"
#include "shob_groups.h"
#include "timescale.h"
#include "thread_run.h"
#include "mlrf.h"
#include "bsurfel_comm.h"
#include "trajectory_meas.h"
#include "seed_sp.h"
#include "gpu_host_include.h"
#include "atomic_ref.h"
#include "conduction_contact_averaging.h"
#include "implicit_shell_solver.h"
#include "implicit_solid_solver.h"

inline BOOLEAN process_posted_shob_receives() {
  BOOLEAN some_receive_completed = FALSE;
  ccDOTIMES(rtype, N_RECV_TYPES) {
    cRECV_CHANNEL *rchannel =  &g_strand_mgr.m_recv_channel[rtype];
    if (rchannel->process_posted_receives())
      some_receive_completed = TRUE;
  }

  if (g_strand_mgr.m_rp_recv_channel.process_posted_receives()) {
    some_receive_completed = TRUE;
  }

  return  some_receive_completed;
}

inline BOOLEAN process_collective_queue() {
  BOOLEAN some_group_on_collective_queue = FALSE;
  some_group_on_collective_queue = g_strand_mgr.m_collective_queue->drain();
  return  some_group_on_collective_queue;
}

static inline VOID cancel_pending_mlrf_recvs() {
  //msg_print("Canceling receives; there are %d groups", g_mlrf_surfel_fset.n_groups());
  DO_MLRF_SURFEL_GROUPS(group) {
    group->cancel_receives();
  }
}

VOID process_checkpoints_comm_thread();

static VOID process_vtable_messages()
{
  MPI_Status status;
  int flag;
  ccDOTIMES(table_index, sim.n_tables) {
    int tag = make_mpi_tag<eMPI_MSG::VTABLE>(table_index);
    MPI_Iprobe(eMPI_sp_cp_rank(), tag, eMPI_sp_cp_comm, &flag, &status);
    if (flag) {
      receive_vtable(tag); // Receive the table if we're ready for it
    }
  }
}

static VOID process_ptherm_time_message()
{
  MPI_Status status;
  int flag;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_POWERTHERM_TIME_INFO_TAG, eMPI_sp_cp_comm, &flag, &status);
  if (flag) {
    receive_ptherm_time_info(); // Receive the model if we're ready for it
  }
}


static VOID process_coupling_model_messages()
{
  MPI_Status status;
  int flag;
  ccDOTIMES(coupling_model_index, sim.n_coupling_models) {
    int tag = make_mpi_tag<eMPI_MSG::COUPLING>(coupling_model_index);
    MPI_Iprobe(eMPI_sp_cp_rank(), tag, eMPI_sp_cp_comm, &flag, &status);
    if (flag) {
      receive_coupling_model(tag); // Receive the model if we're ready for it
    }
  }
}
static VOID process_tbs_messages()
{
  ccDOTIMES(imeas, sim.n_seed_from_meas_descs) {
    int tag = make_mpi_tag<eMPI_MSG::TBS>(imeas);
    maybe_post_recv_for_tbs_data(tag);
  }
}

static VOID process_rotdyn_messages()
{
  MPI_Status status;
  int flag;
  ccDOTIMES(table_index, sim.n_rotational_dynamics_descs) {
    int tag = make_mpi_tag<eMPI_MSG::ROTDYN>(table_index);
    MPI_Iprobe(eMPI_sp_cp_rank(), tag, eMPI_sp_cp_comm, &flag, &status);
    if (flag) {
      receive_rotational_dynamics(tag); // Receive the table if we're ready for it
    }
  }
}

static VOID process_dsm_messages()
{
  MPI_Status status;
  int flag;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_ASYNC_DSM_DATA_TAG, eMPI_sp_cp_comm, &flag, &status);
  if (flag) {
    maybe_receive_dsm(status); // Receive the dsm if we're ready for it
  }
}

static void process_unlock_timestep_last_events_msg()
{
  MPI_Recv(NULL,0, MPI_INT, eMPI_sp_cp_rank(), eMPI_UNLOCK_TIMESTEP_LAST_EVENTS_TAG, eMPI_sp_cp_comm, MPI_STATUS_IGNORE);
  g_strand_mgr.m_timestep_last_events_unlocked.store(true, std::memory_order_release);
}

static void probe_and_process_unlock_timestep_last_events_msg()
{
  MPI_Status status;
  int flag = 0;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_UNLOCK_TIMESTEP_LAST_EVENTS_TAG, eMPI_sp_cp_comm, &flag, &status);

  if (flag) {
    process_unlock_timestep_last_events_msg();
  }
}

static void process_sim_finished_msg()
{
  MPI_Recv(NULL,0,MPI_INT,eMPI_sp_cp_rank(),eMPI_SIM_FINISHED_TAG, eMPI_sp_cp_comm, MPI_STATUS_IGNORE);
  g_strand_mgr.m_received_sim_finished_msg = true;
}

static void probe_and_process_sim_finished_msg()
{
  MPI_Status status;
  int flag = 0;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_SIM_FINISHED_TAG, eMPI_sp_cp_comm, &flag, &status);

  if (flag) {
    process_sim_finished_msg();
  }
}

static void finish_processing_async_event_msg()
{
  process_event_replies();
  AsyncEventQueuePendingRequest::finish_req();
}

static void probe_and_process_async_event_msg() {
  MPI_Status status;
  int flag = 0;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_ASYNC_EVENT_MSG_TAG, eMPI_sp_cp_comm, &flag, &status);

  if (flag) {
    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
    finish_processing_async_event_msg();
  }
}

static bool maybe_process_async_event_request()
{
  if ( AsyncEventQueuePendingRequest::is_event_req_pending() ) {
    return false;
  }

    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS) << "Trying to process event_request";
    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS) << "process event_request";

    SEND_EXA_SIM_MSG<BASETIME, 1> event_reply_time(eMPI_ASYNC_EVENT_REPLY_TAG);
    RECV_EXA_SIM_MSG<sINT32, 1> recv_dummy_msg(eMPI_ASYNC_EVENT_REQ_TAG, eMPI_sp_cp_rank());
    g_exa_sp_cp_comm.recv(recv_dummy_msg.mpi_msg);

    // Lock the queue here to ensure consistency between CP & main/comm threads
    sEVENT_QUEUE::cLOCK lock = g_async_event_queue.get_lock();

    BASETIME reply_time;
    if (sim.has_no_ublks_or_surfels()) {
      // An SP that has no ublks or surfels should not participate in the timestep negotiations, as it will be so far
      // ahead of everyone else.  
      reply_time = 0;
    } else if (tATOMIC_REF(g_strand_mgr.m_exit).load(std::memory_order_acquire)) {
      // Mark the reply time as invalid to let CP know that all events that require extending the end time or to do some
      // additional tasks upon ending should not be scheduled, since this SP has already started closing shop
      reply_time = BASETIME_INVALID;
    } else {
      //does not need to wait till next_time(), since for the supercycled solver can correspond to a much later
      //base time than needed
      reply_time = g_timescale.time() + 1;
    }
    *event_reply_time.return_buffer() = reply_time;
    g_exa_sp_cp_comm.send(event_reply_time.mpi_msg);
    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS).format("Sending timestep {}",reply_time);

    // adding 1 since the strand time lags by 1 time step
    add_event(new_event(EVENT_ID_EVENT_REQ, -1, reply_time + 1), std::move(lock));

    return true;
}

static void probe_and_process_async_event_request() {
  MPI_Status status;
  int flag = 0;
  MPI_Iprobe(eMPI_sp_cp_rank(), eMPI_ASYNC_EVENT_REQ_TAG, eMPI_sp_cp_comm, &flag, &status);

  if (flag) {
    maybe_process_async_event_request();
  }
}


static bool is_work_left()
{
  return (!tATOMIC_REF(g_strand_mgr.m_exit).load(std::memory_order_acquire) || g_strand_mgr.m_meas_pending_queue.m_head != NULL);
}

static sSIM_SOLVER_STATUS atomically_read_solver_status() {
  // We don't need to know precisely what time it is, so we don't need
  // to worry about memory ordering here
  sSIM_SOLVER_STATUS s;
  s.status      = tATOMIC_REF(g_timescale.solver_status.status).load(std::memory_order_relaxed);
  s.flow_status = tATOMIC_REF(g_timescale.solver_status.flow_status).load(std::memory_order_relaxed);
  s.cond_status = tATOMIC_REF(g_timescale.solver_status.cond_status).load(std::memory_order_relaxed);
  return s;
}

/*
In a flow-only or conduction-only simulation, we have 2 threads (comm & compute) bound to a core.
If the compute thread has nothing to do (no strand is running), we don’t want the comm thread to sleep,
because it is not stealing cycles from the compute thread. But in a flow+conduction simulation,
we have 2 SPs or 4 threads bound to a core, so ideally we want a comm thread to sleep if either
compute thread has work to do. But since an SP does not have access to the other SP’s g_running_strand variable,
we cannot implement this directly. As a compromise, the comm thread of the less frequently
running SP (i.e. the super-cycled SP) should always sleep, so as not to steal cycles from the other SP’s compute thread.
Up until recently, our code only allowed the conduction SPs to be super-cycled, but with introduction of support
for “Advance Solvers at Different Rates” in recent days, sometimes the flow SPs will be super-cycled,
and sometimes the conduction SPs.
Ideally, we would support a more direct implementation of the desired behavior here
by allowing the 2 SPs to see each other’s g_running_strand variable (via shared memory).
 */
static bool comm_thread_on_SP_should_always_sleep() {
  bool is_cond_and_flow_sim = sim.is_conduction_model && sim.is_flow;
#if BUILD_GPU
  // PR - 55325
  //Different solver rates/super cycling isn't supported on the GPU
  cassert(g_timescale.is_this_sp_realm_subcycled(sim.is_conduction_sp, is_cond_and_flow_sim) == false);
  return false;
#else
  return !g_timescale.is_this_sp_realm_subcycled(sim.is_conduction_sp, is_cond_and_flow_sim);
#endif
}

VOID *comm_thread_fcn(void * noarg) {
  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &g_strand_mgr.comm_thread_start_timespec);

#if BUILD_GPU
  // The comm thread and the main thread need to be on the same device!
  GPU::set_device_comm_thread();
#endif

  if ( sim.is_mme_comm_needed() ) {
    g_strand_mgr.post_initial_recvs();
  }

  g_strand_mgr.m_is_comm_thread_done = FALSE;
  // Comm thread continuous loop
  // This variable is set by the comm thread and read by the compute thread, so we
  // don't need to worry about memory ordering here.
  while (!g_strand_mgr.m_received_sim_finished_msg.load(std::memory_order_relaxed)) {
    //LOG_MSG("STRAND_SWITCHING").printf("COMM THREAD  while (!g_strand_mgr.m_received_sim_finished_msg)");
    if (!is_work_left()) {
      sSIM_SOLVER_STATUS status_done;
      status_done.status = SIM_STATUS_DONE;
      status_done.flow_status = (sim.is_conduction_sp) ? REALM_STATUS_INACTIVE : g_timescale.time_flow();
      status_done.cond_status = (sim.is_conduction_sp) ? g_timescale.time_cond() : REALM_STATUS_INACTIVE;
      StatusTree::maybe_send_status(status_done);
    }
    else {
      StatusTree::maybe_send_status(atomically_read_solver_status());
    }

    if ((g_strand_mgr.m_ready_mask.load(std::memory_order_relaxed) & g_strand_mgr.m_runstate_mask.load(std::memory_order_relaxed)).any()) {
      g_sync_threads.wake_up_compute_thread();
    }

    // Shob sends. Always drain the send queue. Note that this can block if prior sends have not completed,
    // which is not expected.

    g_strand_mgr.m_send_queue->drain();

    // Measurement queues. These do not block.

    // Move forward any pending reduction window sends
    g_strand_mgr.m_meas_pending_queue.process();

    // Complete prior sends if possible
    g_strand_mgr.m_meas_completion_queue.process();

    // Empty the send queue. All non-reduction windows on the queue are sent;
    // reduction windows not ready to send are moved to the pending_queue.
    g_strand_mgr.m_meas_send_queue->drain();

    // Sliding mesh comm
    g_strand_mgr.m_mlrf_comm.process_requests();
    g_strand_mgr.m_mlrf_comm.process_posted_pre_dyn_recvs();
    g_strand_mgr.m_mlrf_comm.process_posted_post_dyn_recvs();

    // Fan comm
    g_strand_mgr.m_fan_comm.process_requests();

    if (sim.is_radiation_model) {
      g_radiation_wait_time.maybe_send_msg_to_cp();
    }

    // Averaged thermal contact reductions
    if (g_thermal_averaged_contacts.is_enabled_in_sp()) {
      // Accumulates groups and makes calls to MPI_Iallreduce
      g_thermal_averaged_contacts.post_reductions();
      // Completes MPI_Iallreduce and copies reduction buffer contents to accumulators
      g_thermal_averaged_contacts.complete_reductions();
    }

    if (has_trajectory_window() && sim.is_particle_model) {
      // CP prefers to receive in this order: startpoints, vertices, hitpoints
      sp_trajectory_startpoint_manager.send_trajectory_data_to_cp();
      sp_trajectory_vertex_manager.send_trajectory_data_to_cp();
      sp_trajectory_hitpoint_manager.send_trajectory_data_to_cp();
      if (g_trajectory_id_map.is_trajectory_global_id_comm_ready()) {
        g_trajectory_id_map.receive_base_global_ids(); // completes a receive posted many timesteps earlier
        g_trajectory_id_map.mark_trajectory_global_id_comm_done();
      }
    }

#if !GPU_COMPILER && !BUILD_GPU
    if (sim.is_conduction_sp && sim.is_shell_conduction_model && sim.use_implicit_shell_solver) {
      setup_implicit_shell_solver();
    }
#endif
 
#if !GPU_COMPILER && !BUILD_GPU
    if (sim.is_conduction_sp && sim.is_shell_conduction_model && sim.use_implicit_shell_solver) {
      implicit_shell_solver_evolve();
    }
#if !BUILD_5G_LATTICE
    if (sim.is_conduction_sp && sim.is_conduction_model && sim.use_implicit_solid_solver) {
      //if (sim.implicit_solid_solver_matrix_varying)  //temperature/time varying properties
      //msg_print("solve T %d", g_timescale.m_time);
      setup_implicit_solid_solver();
      implicit_solid_solver_evolve();
    }
#endif
#endif

    // Receives
    if (process_posted_shob_receives()) {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("Process_posted_receives() returned TRUE");
    }

#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      if (process_collective_queue()) {
	LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("Process_collective_queue() returned TRUE");
      }
    }
#endif

    // The while loop below attempts to receive all detected messages and
    // dispatch appropriately according to the tag. However there are some
    // messages that cannot be received this way, and they may prevent the
    // Iprobe from matching other messages which we are prepared to receive.
    // Consequently if we break the loop with any message unreceived (that is,
    // without getting a flag == FALSE response from the Iprobe), we enter a
    // secondary procedure which explicitly probes for each type.
    //
    // The loop below, as it is currently implemented, breaks out for the
    // majority of cases.  Perhaps we should eliminate the loop entirely and
    // just loop over each message type explicitly?

    BOOLEAN break_dispatch_loop = FALSE;

    MPI_Status status;
    int flag = 1;
    while(flag && !break_dispatch_loop) {

      //LOG_MSG("STRAND_SWITCHING").printf("COMM THREAD  JUST BEFORE IPROBE FLAG: %d break_dispatch_loop: %d", flag, break_dispatch_loop);

      MPI_Iprobe(MPI_ANY_SOURCE, MPI_ANY_TAG, eMPI_sp_cp_comm, &flag, &status);

      //LOG_MSG("STRAND_SWITCHING").printf("COMM THREAD FLAG: %d break_dispatch_loop: %d", flag, break_dispatch_loop);

      if (flag) {

        eMPI_MSG msg_type =  mpi_msg_type(status.MPI_TAG);
        //LOG_MSG("STRAND_SWITCHING").printf("COMM THREAD MESSAGE: %d", int(msg_type));
        switch (msg_type) {
        case eMPI_MSG::SHOB:
          break_dispatch_loop = TRUE;
          break;
        case eMPI_MSG::VTABLE:
          if (!receive_vtable(status.MPI_TAG)) {
            break_dispatch_loop = TRUE;
          }
          break;
        case eMPI_MSG::COUPLING:
          if (!receive_coupling_model(status.MPI_TAG)) {
            break_dispatch_loop = TRUE;
          }
          break;
        case eMPI_MSG::ROTDYN:
          if (!receive_rotational_dynamics(status.MPI_TAG)) {
            break_dispatch_loop = TRUE;
          }
          break;
        case eMPI_MSG::TBS:
          if(!maybe_post_recv_for_tbs_data(status.MPI_TAG))
            break_dispatch_loop = TRUE;
          break;
        case eMPI_MSG::MWIN:
          break_dispatch_loop = TRUE;
          break;

        case eMPI_MSG::MISC:
          {
            switch(status.MPI_TAG) {
            case eMPI_SP_STATUS_TAG:
              StatusTree::process_status_msg(status);
              StatusTree::maybe_send_status(atomically_read_solver_status());
              break;
            case eMPI_CP_ACK_SP_ERRS_INFO_TAG:
              if (is_simerr_queue_empty()) {
                break_dispatch_loop = TRUE;
              } else {
                simerr_mpi_process(FALSE);                  // FALSE indicates no need to probe again
              }
              break;
            case  eMPI_ASYNC_DSM_DATA_TAG:
              if (!maybe_receive_dsm(status)) {
                break_dispatch_loop = TRUE;
              }
              break;
            case eMPI_UNLOCK_TIMESTEP_LAST_EVENTS_TAG:
              process_unlock_timestep_last_events_msg();
              break_dispatch_loop = TRUE;
              break;
            case eMPI_SIM_FINISHED_TAG:
              process_sim_finished_msg();
              break_dispatch_loop = TRUE;
              break;
            case eMPI_ASYNC_EVENT_MSG_TAG:
              finish_processing_async_event_msg();
              break_dispatch_loop = TRUE;
              break;
            case eMPI_ASYNC_EVENT_REQ_TAG:
              if(!maybe_process_async_event_request()) {
                break_dispatch_loop = TRUE;
              }
              break;
            case eMPI_POWERTHERM_TIME_INFO_TAG:
              receive_ptherm_time_info();
              break_dispatch_loop = TRUE;
              break;
            // these message may arrive before the receives are posted.
            case eMPI_EMITTER_ID_TAG:
            case eMPI_WIPER_ID_TAG:
            case eMPI_MLRF_ITOE_PARCEL_COUNT_TAG:
            case eMPI_MLRF_ETOI_PARCEL_COUNT_TAG:
            case eMPI_MLRF_ITOE_PARCEL_COMM_TAG:
            case eMPI_MLRF_ETOI_PARCEL_COMM_TAG:
            case eMPI_MLRF_PRE_DYN_ETOI_TAG:
            case eMPI_MLRF_POST_DYN_ETOI_TAG:
            case eMPI_MLRF_PRE_DYN_ITOE_TAG:
            case eMPI_MLRF_POST_DYN_ITOE_TAG:
            case eMPI_PARCEL_COUNT_TAG:
            case eMPI_PARCEL_COMM_TAG:
              break_dispatch_loop = TRUE;
              break;
            default:
              msg_internal_error("Unexpected MISC message with msg_type %d tag %d received from SP %d",
                                 (int) msg_type, status.MPI_TAG,  status.MPI_SOURCE);
              break;
            }
            break;
          default:
            msg_internal_error("Unexpected message with msg_type %d tag %d received from SP %d; status tag is %d",
                               (int) msg_type, status.MPI_TAG,  status.MPI_SOURCE, eMPI_SP_STATUS_TAG);
          break;
          }
        }
      }
    }

    if (break_dispatch_loop) {
      StatusTree::receive_status_updates();
      probe_and_process_async_event_request();
      probe_and_process_async_event_msg();
      probe_and_process_unlock_timestep_last_events_msg();
      probe_and_process_sim_finished_msg();
      process_vtable_messages();
      process_ptherm_time_message();
      process_coupling_model_messages();
      process_rotdyn_messages();
      process_dsm_messages();
      process_tbs_messages();
      simerr_mpi_process();
    }
    simerr_mpi_process();    
    g_strand_mgr.process_unpacking_list();
#if BUILD_D19_LATTICE
    if (sim.is_pf_model)
      g_strand_mgr.process_collective_list();  //PF
#endif
    if(g_seed_calibration_iteration.m_do_calib_seed_comm) {
      g_seed_calibration_iteration.comm_shobs();
      g_seed_calibration_iteration.m_do_calib_seed_comm = FALSE;
    }
    process_checkpoints_comm_thread();
    test_posted_tbs_recvs();

    // This check doesn't manage any memory state, so we can load it relaxed
    if (tATOMIC_REF(g_running_strand).load(std::memory_order_relaxed) != NO_STRAND
        || comm_thread_on_SP_should_always_sleep()) {
      g_sync_threads.wait_for_compute_thread(THREAD_SLEEP_SHORT);
    }
  }  // End of main loop

  cancel_pending_mlrf_recvs();
  cancel_pending_fan_recvs();
  // Finish the fan send
  g_strand_mgr.m_fan_comm.process_requests();
  g_strand_mgr.m_meas_send_queue->drain();
  g_strand_mgr.m_send_queue->drain();
  while (simerr_mpi_process()) { sp_thread_sleep(MPI_SLEEP_LONG); }
  while(g_strand_mgr.m_meas_pending_queue.m_head != NULL) {
    g_strand_mgr.m_meas_pending_queue.process();
  }
  while(g_strand_mgr.m_meas_completion_queue.m_head != NULL) {
    g_strand_mgr.m_meas_completion_queue.process();
  }

  // If a trajectory window is left behind, send it now.
  if (has_trajectory_window() && sim.is_particle_model) {
    // CP prefers to receive in this order: startpoints, vertices, hitpoints
    sp_trajectory_startpoint_manager.send_trajectory_data_to_cp();
    sp_trajectory_vertex_manager.send_trajectory_data_to_cp();
    sp_trajectory_hitpoint_manager.send_trajectory_data_to_cp();
    if (g_trajectory_id_map.is_trajectory_global_id_comm_ready()) {
      g_trajectory_id_map.receive_base_global_ids(); // completes a receive posted many timesteps earlier
      g_trajectory_id_map.mark_trajectory_global_id_comm_done();
    }
  }
  // It is possible that there are ckpt events scheduled for BASETIME_LAST, process them here
  while (!g_strand_mgr.m_timestep_last_events_processed) {
    process_checkpoints_comm_thread();
  }

  clock_gettime(CLOCK_THREAD_CPUTIME_ID, &g_strand_mgr.comm_thread_stop_timespec);
  g_strand_mgr.m_is_comm_thread_done = TRUE;

  return(NULL);
}
