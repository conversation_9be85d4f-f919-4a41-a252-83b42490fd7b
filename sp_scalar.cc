/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1994-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

//----------------------------------------------------------------------------
// James F. Kelly                                             Aug 11, 2013
//----------------------------------------------------------------------------
#include "sp_scalar.h"
#include "eqns.h"

std::vector<sSP_UDS> g_sp_scalars; 
std::vector<sSP_UDS_BOUNDARY_REGION> g_sp_uds_boundary_regions; 

sSP_UDS ::  sSP_UDS(asINT32 uds_index) {
    cBOOLEAN pde_solver                          = TRUE;
}


VOID sSP_UDS :: seed_uds_simple_ublk_datablock(UBLK ublk, UBLK_UDS_DATA uds_data, asINT32 fluid_region) {
  DO_ACTIVE_VOXELS(voxel) {
    STP_GEOM_VARIABLE voxel_centroid[3];
    STP_GEOM_VARIABLE normal[3] = {0.0};
    voxel_centroid[0] = ublk->centroids(voxel, 0);
    voxel_centroid[1] = ublk->centroids(voxel, 1);
    voxel_centroid[2] = ublk->centroids(voxel, 2);

/*    if (initial_conditions[fluid_region].is_eqn) {
      uds_data->uds[0][voxel] = eval_space_varying_only_parameter_program_for_uds(voxel_centroid,
                                  normal,g_case_program,&initial_conditions[fluid_region]);
      uds_data->uds[1][voxel] = uds_data->uds[0][voxel];
      

    }
    else {
      uds_data->uds[0][voxel] = initial_conditions[fluid_region].value;
      uds_data->uds[1][voxel] = initial_conditions[fluid_region].value; 
    }*/
    // need to fix later
    
    if (is_pde_solver()) {
      if (is_turb_schmidt_number_specified()) {
	uds_data->pde_uds_data()->diffusion_coef[0][voxel] = 0.0;
	uds_data->pde_uds_data()->diffusion_coef[1][voxel] = 0.0;
      } else {
	uds_data->pde_uds_data()->diffusion_coef[0][voxel] = diffusion_coefficient;
	uds_data->pde_uds_data()->diffusion_coef[1][voxel] = diffusion_coefficient;
      }
    }

    if (source_term.is_eqn) {
      // evaluate source term at time t=0 here
      uds_data->volume_source_term[voxel] = eval_space_and_time_varying_parameter_program_for_uds(voxel_centroid, normal, 0, 0, 
                                                                                                 g_case_program,&source_term);
    } else {
      uds_data->volume_source_term[voxel] = source_term.value;
    }
    if (ublk->is_near_surface()) {
      ublk->surf_uds_data()->surfel_source_term[0][voxel] = 0.0;
      ublk->surf_uds_data()->surfel_source_term[1][voxel] = 0.0;  
    }
  }
}

VOID sSP_SUDS :: seed_uds_real_ublk_datablock(UBLK ublk, asINT32 voxel, UBLK_UDS_DATA uds_data, asINT32 fluid_region) {
  // Set up the uds_data here from the phys_type_description (before it wasn't availalle 

  STP_GEOM_VARIABLE voxel_centroid[3];
  STP_GEOM_VARIABLE normal[3] = {0.0};
  voxel_centroid[0] = ublk->centroids(voxel, 0);
  voxel_centroid[1] = ublk->centroids(voxel, 1);
  voxel_centroid[2] = ublk->centroids(voxel, 2);
  // New field values are stored in 
  /*if (initial_conditions[fluid_region].is_eqn) {
    scalar_data->q[0][voxel] = eval_space_varying_only_parameter_program_for_uds(voxel_centroid,
    scalar_data->q[1][voxel] = scalar_data->q[0][voxel];
  } else {
    scalar_data->q[0][voxel] = initial_conditions[fluid_region].value;
    scalar_data->q[1][voxel] = initial_conditions[fluid_region].value; 
  }*/
   // need to fix later TODO
  if (is_pde_solver()) {
    if(sim.is_turb_model){
      //scalar_data->diffusion_coef[0][voxel] = g_nu_molecular/schmidt_number+(ublk->turb_data())->eddy_visc[0][voxel]/turb_schmidt_number;
    //scalar_data->diffusion_coef[1][voxel] = scalar_data->diffusion_coef[0][voxel];
    } else {
      uds_data->pde_uds_data()->diffusion_coef[0][voxel] = g_nu_molecular/schmidt_number;
      uds_data->pde_uds_data()->diffusion_coef[1][voxel] = uds_data->pde_uds_data()->diffusion_coef[0][voxel];
    }
  }
  // Iniitialize from the phys_type_descriptor
  if (source_term.is_eqn) {
    uds_data->volume_source_term[voxel] = eval_space_and_time_varying_parameter_program_for_uds(voxel_centroid, normal, 0, 0,
                                                                                               g_case_program,&source_term);
  } else {
    uds_data->volume_source_term[voxel] = source_term.value;
  }
  if (ublk->is_near_surface()) {
    ublk->surf_uds_data()->surfel_source_term[0][voxel] = 0.0;
    ublk->surf_uds_data()->surfel_source_term[1][voxel] = 0.0;  
  }
}


VOID sSP_UDS :: evaluate_source_term(UBLK ublk, UBLK_UDS_DATA ublk_uds_data,  uINT8 voxel_mask) {
  
  asINT32 time = sim.time;
  DO_VOXELS_IN_MASK(voxel, voxel_mask) {
     STP_GEOM_VARIABLE voxel_centroid[3];
     STP_GEOM_VARIABLE normal[3] = {0.0};
     voxel_centroid[0] = ublk->centroids(voxel, 0);
     voxel_centroid[1] = ublk->centroids(voxel, 1);
     voxel_centroid[2] = ublk->centroids(voxel, 2);

     ublk_uds_data->volume_source_term[voxel] = 
       eval_space_and_time_varying_parameter_program_for_uds(voxel_centroid, normal, time, 
                                                             g_case_program, &source_term);
   }
}



VOID sSP_UDS_BOUNDARY_CONDITION :: evaluate_uds_boundary_condition(SURFEL surfel, SURFEL_UDS_DATA surfel_uds_data, 
                                                                         asINT32 timestep,
                                                                         sFLOAT powertherm_time) {
  if (flux_or_value.is_eqn) {
    if (surfel_uds_data->is_flux_bc) {
      surfel_uds_data->uds_flux_bc = eval_time_varying_only_parameter_program_for_uds(timestep, powertherm_time,
          surfel->normal,g_case_program,&flux_or_value);
    } else if (surfel_uds_data->is_outlet_bc) {
      surfel_uds_data->uds_value_bc = 0.0;     
    } else {
      surfel_uds_data->uds_value_bc =eval_time_varying_only_parameter_program_for_uds(timestep, powertherm_time, 
          surfel->normal,g_case_program,&flux_or_value);
    } 
  }
}
