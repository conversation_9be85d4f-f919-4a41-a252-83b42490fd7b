/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Allocates/frees an aligned block of memory
 *
 * Dalon Work, Exa Corporation Created Oct 05, 2017
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_ALIGNED_MEMORY_H 
#define _SIMENG_ALIGNED_MEMORY_H

#include <cstdlib>
#include "stdint.h"
#include <new>
#include <assert.h>
#include <cstring>
#include <memory>

#include "memory_slab.h"

#ifndef SIMENG_REPORT_MEM_USAGE
#define SIMENG_REPORT_MEM_USAGE 0
#endif

namespace detail {
  inline
  void * aligned_malloc(size_t n_bytes, size_t align)
  {
    void * ptr = nullptr;
    int err = posix_memalign(&ptr, align, n_bytes);
    return ptr;
  }

  inline void aligned_free(void * ptr)
  {
    free(ptr);
  }
}


template<bool Recording, typename T> struct ShobCounter;

template<typename T>
struct ShobCounter<1,T>
{
  static unsigned long value;
  static void increment() { value++; }
  static void decrement() { value--; }
};

template<typename T> unsigned long ShobCounter<1,T>::value = 0;

template<typename T>
struct ShobCounter<0,T>
{
  static const long value = 0;
  static void increment() {}
  static void decrement() {}
};

template<typename T>
struct sSHOB_ALLOCATOR : public ShobCounter<SIMENG_REPORT_MEM_USAGE,T>
{
  typedef struct ShobCounter<SIMENG_REPORT_MEM_USAGE,T> Base;
  static T * malloc ( size_t n_bytes, bool temp = false )
  {
    // allocate slabs in 4 MB chunks, or 1000 pages
    static cMEMORY_SLAB slab_allocator(4096*1000);
    T * aligned_ptr = nullptr;
    if (temp) {
      aligned_ptr = (T*) detail::aligned_malloc(n_bytes, T::ALIGNMENT);
    }
    else {
      aligned_ptr = (T*) slab_allocator.malloc(n_bytes, T::ALIGNMENT);
    }
    Base::increment();
    return aligned_ptr;
  }

  // This should only be called on pointers that were malloced using temp = true
  static void free ( T * aligned_ptr )
  {
    detail::aligned_free(aligned_ptr);
    Base::decrement();
  }

  static unsigned long count() { return Base::value; }
private:
};

#endif
