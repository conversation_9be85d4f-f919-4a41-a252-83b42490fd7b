#ifndef _SIMENG_FACE_POLYGONS_H
#define _SIMENG_FACE_POLYGONS_H

#include "common_sp.h"
#include "shob.h"

class sFPOLY_VERTEX {
public:
  sdFLOAT m_plane_coords[2];
  sFPOLY_VERTEX(cDGF_VERTEX_2D_POINT p) {
    m_plane_coords[0] = p.vtx_coord[0];
    m_plane_coords[1] = p.vtx_coord[1];
  }
};

class sCONVEX_VERTICES_LOOP {
  sINT32 m_n_vertices;
  sINT32 *m_vertex_indices;
public:
  sCONVEX_VERTICES_LOOP() {
    m_n_vertices = 0;
    m_vertex_indices = nullptr;
  }
  sCONVEX_VERTICES_LOOP(cDGF_VOXEL_FPOLYGON_DESC &polygon_desc) {
    m_n_vertices = polygon_desc.f.num_fpoly_vertices;
    m_vertex_indices = new sINT32[m_n_vertices];
    ccDOTIMES(vert, m_n_vertices) {
      m_vertex_indices[vert] = polygon_desc.vertices[vert].fpoly_vertex_index;
    }
  }
  bool does_point_overlap(dFLOAT pt[2]);
};

typedef struct sVOXEL_FACE_POLYGON
{
public:
    uINT8  m_voxel;
    uINT8  m_voxel_face; // Between 0 and 5
    sINT8  m_ublk_instance;
    uINT8  m_num_loops;
    sCONVEX_VERTICES_LOOP *m_vertex_loops;
    sUBLK *ublk[2];


   sVOXEL_FACE_POLYGON() {
     m_voxel = 0 ;
     m_voxel_face = 0;
     m_ublk_instance = 0;
     m_num_loops = 0;
     m_vertex_loops = nullptr;
     ublk[0] = nullptr;
     ublk[1] = nullptr;
   }
   sVOXEL_FACE_POLYGON(uINT8 num_loops, uINT8 voxel,
                        uINT8 voxel_face, uINT8 neighbor_instance);
   VOID add_convex_loop(cDGF_VOXEL_FPOLYGON_DESC &polygon_desc);
   bool does_point_overlap(dFLOAT pt[3]);

} *FACE_POLYGON;


class cFACE_POLYGONS_INFO {
  std::vector<sVOXEL_FACE_POLYGON> m_face_polygons;
  std::vector<sFPOLY_VERTEX> m_fpoly_vertices;

public:
  sINT32 m_n_face_polygons;
  sINT32 m_n_fpoly_vertices;

  sVOXEL_FACE_POLYGON add_face_polygons(sINT32 num_loops, uINT8 voxel,
                                         uINT8 voxel_face, uINT8 neighbor_instance);
  sVOXEL_FACE_POLYGON &face_polygon(sINT32 polygon_index) {
    return m_face_polygons.at(polygon_index);
  }
  sINT32 add_fpoly_vertex(cDGF_VERTEX_2D_POINT p) {
    sFPOLY_VERTEX fpv(p);
    m_fpoly_vertices.push_back(fpv);
    return(m_fpoly_vertices.size()-1);
  }
  sFPOLY_VERTEX & fpoly_vertex(int p_index) {
    return m_fpoly_vertices[p_index];
  }
};
extern cFACE_POLYGONS_INFO g_face_polygon_info;

typedef class sSPLIT_NEIGHBOR_INFO {
  const static int N_3D_NEIGHBOR_VOXELS = 26;
  const static int N_FACE_NEIGHBORS = 6; 
  const static int N_EDGE_NEIGHBORS = 12;
  const static int N_CORNER_NEIGHBORS = 8;

  const static int FIRST_EDGE_NEIGHBOR = 6;
  const static int FIRST_CORNER_NEIGHBOR = 18;

  const static char NEIGHBORS_NONE = -1;
  const static char NEIGHBORS_SINGLE = -2;
  const static char NEIGHBORS_MULTIPLE = -3;

  sINT8 m_ublk_instances[ubFLOAT::N_VOXELS][N_3D_NEIGHBOR_VOXELS];
  uINT8 m_start_face_polygon_indices[ubFLOAT::N_VOXELS][N_3D_NEIGHBOR_VOXELS];
  std::vector<sVOXEL_FACE_POLYGON> m_face_polygons;
public:
  static int n_face_neighbors(){
    return N_FACE_NEIGHBORS;
  }
  static int n_edge_neighbors(){
    return N_EDGE_NEIGHBORS;
  }

  STP_LATVEC_INDEX neighbor_to_latvec_index(uINT8 neighbor);
  VOID init(sUBLK *ublk);
  VOID init_face_connectivity(std::vector<sVOXEL_FACE_POLYGON> &voxel_face_polygons);
  VOID update_face_connectivity(sUBLK *ublk);
  VOID set_face_single_ublk_instance(sUBLK *ublk);
  VOID update_edge_connectivity(sUBLK *ublk);
  VOID update_split_ublk_instances_across_faces(sUBLK *ublk);
  sSPLIT_NEIGHBOR_INFO() {
    memset(m_ublk_instances, 0, ubFLOAT::N_VOXELS*N_3D_NEIGHBOR_VOXELS);
    memset(m_start_face_polygon_indices, -1, ubFLOAT::N_VOXELS*N_3D_NEIGHBOR_VOXELS);
  }

  bool any_polygons_on_face(uINT8 voxel, uINT8 face_index) {
    cassert(voxel < ubFLOAT::N_VOXELS);
    cassert(face_index < STP_N_FACES);
    return ((m_start_face_polygon_indices[voxel][face_index+1] - m_start_face_polygon_indices[voxel][face_index]));
  }
  int find_overlapping_fpolygon(dFLOAT pt[3], dFLOAT voxel_dx, uINT8 voxel, uINT8 face_index);
  UBLK find_split_ublk_across_face(dFLOAT pt[3], sUBLK *ublk, uINT8 voxel, uINT8 face_index);

  void convert_to_2D_fpolygon_coord(dFLOAT pt[3], dFLOAT voxel_dx, uINT8 face_index, dFLOAT pt_2d[2]);

  bool is_valid_instance(sINT8 neighbor_instance) {
    return (neighbor_instance > NEIGHBORS_NONE);
  }
  bool is_ublk_instance_single(uINT8 voxel, uINT8 neighbor) {
    return (m_ublk_instances[voxel][neighbor] > NEIGHBORS_NONE);
  }
  bool is_ublk_instance_to_be_set(uINT8 voxel, uINT8 neighbor) {
    return (m_ublk_instances[voxel][neighbor] == NEIGHBORS_SINGLE);
  }
  bool are_ublk_instances_multiple(uINT8 voxel, uINT8 neighbor) {
    return (m_ublk_instances[voxel][neighbor] == NEIGHBORS_MULTIPLE);
  }
  sINT8 ublk_instance(uINT8 voxel, uINT8 neighbor) {
    return m_ublk_instances[voxel][neighbor];
  }
  static bool is_face_neighbor(uINT8 neighbor) {
    return (neighbor < N_FACE_NEIGHBORS);
  }
  static bool is_edge_neighbor(uINT8 neighbor) {
    return (neighbor >= FIRST_EDGE_NEIGHBOR) && (neighbor < FIRST_EDGE_NEIGHBOR + N_EDGE_NEIGHBORS);
  }
  static bool is_corner_neighbor(uINT8 neighbor) {
    return (neighbor >= FIRST_CORNER_NEIGHBOR ) && (neighbor < FIRST_CORNER_NEIGHBOR + N_CORNER_NEIGHBORS);
  }
} *SPLIT_NEIGHBOR_INFO;

bool compute_intersection_on_face(uINT8 latvec, dFLOAT start_pt[3], dFLOAT end_pt[3], dFLOAT voxel_dx,
                                  dFLOAT intersecting_pt[3]);
#endif
