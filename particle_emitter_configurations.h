#ifndef _PARTICLE_EMITTER_CONFIGURATIONS_H_
#define _PARTICLE_EMITTER_CONFIGURATIONS_H_

#include "particle_materials.h"
#include "particle_emitter_geometry.h"

//====== Emitter Configurations ======
typedef class sPARTICLE_EMITTER_CONFIGURATION_BASE
{
 public:
  enum eEMITTER_CONFIGURATION_TYPES {
    FULL_CONE,
    HOLLOW_CONE,
    ELLIPTICAL_CONE,
    RAIN,
    TIRE,
    UNSPECIFIED,
    NUM_EMITTER_CONFIGURATION_TYPES,
  };

 protected:
  static asINT32 m_id_count;
  std::string m_name;
  asINT32 m_id;
  eEMITTER_CONFIGURATION_TYPES m_emitter_configuration_type;
  sPHYSICS_VARIABLE m_emission_rate;
  LGI::eLGI_EMISSION_RATE_TYPE m_emission_rate_type;

 public:
  sPARTICLE_EMITTER_CONFIGURATION_BASE(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                       std::string &name,
                                       std::string &emission_rate_variable_name){

    m_name = name;
    m_id = m_id_count++;
    m_emission_rate.value = lgi_record.emission_rate_constant;
    m_emission_rate.name = nullptr;
    if( lgi_record.emission_rate_variable_name_length !=0 ) {
      m_emission_rate.name = cnew char[lgi_record.emission_rate_variable_name_length + 1];
      strncpy((char*)m_emission_rate.name, emission_rate_variable_name.c_str(), lgi_record.emission_rate_variable_name_length);
    }
    m_emission_rate_type = lgi_record.emission_rate_type;
  }
  virtual eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return UNSPECIFIED;}
  virtual VOID initialize_physics_variables();
  virtual BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  virtual VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                                    STP_GEOM_VARIABLE normal[3],
                                                    BASETIME timestep,
                                                    sFLOAT powertherm_time);
  virtual dFLOAT expected_particle_diameter() = 0;
  virtual dFLOAT expected_particle_diameter_stddev() = 0;
  virtual dFLOAT expected_particle_volume() = 0;
  virtual sPARTICLE_VAR  diameter_distribution_param1() = 0;
  virtual sPDF::ePDF_TYPE diameter_distribution() = 0;
  virtual sPARTICLE_VAR diameter_distribution_param2() = 0;
  virtual PARTICLE_MATERIAL material() = 0; 
  virtual sPARTICLE_VAR diameter_min() = 0;
  virtual sPARTICLE_VAR diameter_max() = 0;
  const char* name(){return m_name.c_str();}
  asINT32 id() {return m_id;}
  dFLOAT emission_rate() {return m_emission_rate.value;}
  LGI::eLGI_EMISSION_RATE_TYPE emission_rate_type() {return m_emission_rate_type;}
  SIMUTILS::eEMISSION_RATE_TYPES  simutils_emission_rate_type() {
    switch(m_emission_rate_type) {
      case LGI::EMISSION_RATE_NONE:
        return SIMUTILS::EMISSION_RATE_NONE;
      case LGI::EMISSION_RATE_MASSFLOW:
        return SIMUTILS::EMISSION_RATE_MASSFLOW;
      case LGI::EMISSION_RATE_PARTICLE:
        return SIMUTILS::EMISSION_RATE_PARTICLE;
      case LGI::EMISSION_RATE_VOLUMETRIC:
        return SIMUTILS::EMISSION_RATE_VOLUMETRIC;
      case LGI::EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME:
        return SIMUTILS::EMISSION_RATE_MASSFLOW_PER_UNIT_VOLUME;
      case LGI::EMISSION_RATE_DEPTH:
        return SIMUTILS::EMISSION_RATE_DEPTH;
    case LGI::EMISSION_RATE_LWC:
        return SIMUTILS::EMISSION_RATE_LWC;
      case LGI::NUM_FLOW_RATE_TYPES:
      default:
        return SIMUTILS::EMISSION_RATE_NONE;
    }
    return SIMUTILS::EMISSION_RATE_NONE;
  }
}* PARTICLE_EMITTER_CONFIGURATION_BASE;


typedef class sKNOWN_DIAMETER_SPRAY_DATA_BASE //This is used for rain emitters where the velocity magnitude is not specified directly (only diameter is specified).
{
 protected:
  asINT32 m_material_index;
  PARTICLE_MATERIAL m_material;
  BOOLEAN m_limit_diameter;
  sPHYSICS_VARIABLE m_diameter_distribution_param1;
  sPDF::ePDF_TYPE m_diameter_distribution;
  sPHYSICS_VARIABLE m_diameter_distribution_param2;
  sPARTICLE_VAR m_diameter_min;
  sPARTICLE_VAR m_diameter_max;
 public:
  sKNOWN_DIAMETER_SPRAY_DATA_BASE(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                  std::string &diameter_distribution_param1_variable_name,
                                  std::string &diameter_distribution_param2_variable_name,
                                  PARTICLE_MATERIAL material,
                                  BOOLEAN limit_diameter);

  sKNOWN_DIAMETER_SPRAY_DATA_BASE(sPARTICLE_VAR diameter_distribution_param1,
                                  sPARTICLE_VAR diameter_distribution_param2,
                                  sPDF::ePDF_TYPE diameter_distribution,
                                  sPARTICLE_VAR diameter_min,
                                  sPARTICLE_VAR diameter_max,
                                  PARTICLE_MATERIAL material,
                                  BOOLEAN limit_diameter ) {//This form is used by tire emitters after interpolation between two arc stations.
    m_diameter_distribution_param1.value = diameter_distribution_param1;
    m_diameter_distribution_param1.name = nullptr;
    m_diameter_distribution_param2.value = diameter_distribution_param2;
    m_diameter_distribution_param2.name = nullptr;
    m_diameter_distribution = diameter_distribution;
    m_diameter_min = diameter_min;
    m_diameter_max = diameter_max;
    m_limit_diameter = limit_diameter;
    m_material = material;
  }
  PARTICLE_MATERIAL material() {return m_material;}
  virtual LGI_EMITTER_NOZZLE_PARAM_TYPE nozzle_type() {return UNDEFINED_NOZZLE;};
  sPDF::ePDF_TYPE diameter_distribution() { return m_diameter_distribution; }
  sPARTICLE_VAR diameter_distribution_param1()   { return m_diameter_distribution_param1.value; }
  sPARTICLE_VAR diameter_distribution_param2()   { return m_diameter_distribution_param2.value; }
  sPARTICLE_VAR diameter_min() {return m_diameter_min; }
  sPARTICLE_VAR diameter_max() {return m_diameter_max; }
  sPARTICLE_VAR sample_spray_droplet_size();
}*KNOWN_DIAMETER_SPRAY_DATA_BASE;

typedef class sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE : public sKNOWN_DIAMETER_SPRAY_DATA_BASE
{
 protected:
  sPHYSICS_VARIABLE m_velocity_magnitude_distribution_param1;
  sPDF::ePDF_TYPE m_velocity_magnitude_distribution;
  sPHYSICS_VARIABLE m_velocity_magnitude_distribution_param2;
 public:
  sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                               std::string &diameter_distribution_param1_variable_name,
                                               std::string &diameter_distribution_param2_variable_name,
                                               PARTICLE_MATERIAL material,
                                               BOOLEAN limit_diameter,
                                               std::string &velocity_magnitude_distribution_param1_variable_name,
                                               std::string &velocity_magnitude_distribution_param2_variable_name);

 sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(sPARTICLE_VAR diameter_distribution_param1,  //This form is used by tire emitters while interpolation between two arc stations.
                                              sPARTICLE_VAR diameter_distribution_param2,
                                              sPDF::ePDF_TYPE diameter_distribution,
                                              sPARTICLE_VAR diameter_min,
                                              sPARTICLE_VAR diameter_max,
                                              PARTICLE_MATERIAL material,
                                              BOOLEAN limit_diameter,
                                              sPARTICLE_VAR velocity_magnitude_distribution_param1,
                                              sPARTICLE_VAR velocity_magnitude_distribution_param2,
                                              sPDF::ePDF_TYPE velocity_magnitude_distribution) :
  sKNOWN_DIAMETER_SPRAY_DATA_BASE(diameter_distribution_param1,
                                  diameter_distribution_param2,
                                  diameter_distribution,
                                  diameter_min,
                                  diameter_max,
                                  material,
                                  limit_diameter) {
    m_velocity_magnitude_distribution_param1.value = velocity_magnitude_distribution_param1;
    m_velocity_magnitude_distribution_param1.is_eqn = FALSE;
    m_velocity_magnitude_distribution_param2.value = velocity_magnitude_distribution_param2;
    m_velocity_magnitude_distribution_param2.is_eqn = FALSE;
    m_velocity_magnitude_distribution = velocity_magnitude_distribution;
  }
  sPDF::ePDF_TYPE velocity_magnitude_distribution()      { return m_velocity_magnitude_distribution; }
  sPARTICLE_VAR velocity_magnitude_distribution_param1() { return m_velocity_magnitude_distribution_param1.value; }
  sPARTICLE_VAR velocity_magnitude_distribution_param2() { return m_velocity_magnitude_distribution_param2.value; }
  sPARTICLE_VAR sample_spray_velocity_magnitude();
}* KNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE;


typedef class sNOZZLE_EMITTER_CONFIGURATION_BASE : public sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE ,
  public sPARTICLE_EMITTER_CONFIGURATION_BASE
{
 protected:
  tPDF<sPARTICLE_VAR>* m_cone_angle_generator; //Some of the RNGs need to recompute lookup tables when any parameters are changed.
                                               //This was added so that the parameters would only need to be set once at initialization.

 public:
  sNOZZLE_EMITTER_CONFIGURATION_BASE(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                     std::string &name,
                                     PARTICLE_MATERIAL material,
                                     std::string &emission_rate_variable_name,
                                     sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                     std::string &diameter_distribution_param1_variable_name,
                                     std::string &diameter_distribution_param2_variable_name,
                                     std::string &velocity_magnitude_distribution_param1_variable_name,
                                     std::string &velocity_magnitude_distribution_param2_variable_name);
  dFLOAT expected_particle_diameter();
  dFLOAT expected_particle_diameter_stddev();
  dFLOAT expected_particle_volume();
  virtual VOID sample_spray_direction(sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]) = 0;
  sPDF::ePDF_TYPE diameter_distribution() { return m_diameter_distribution; }
  sPARTICLE_VAR diameter_distribution_param1()   { return m_diameter_distribution_param1.value; }
  sPARTICLE_VAR diameter_distribution_param2()   { return m_diameter_distribution_param2.value; }
  PARTICLE_MATERIAL material() {return m_material; }
  sPARTICLE_VAR diameter_min() {return m_diameter_min; }
  sPARTICLE_VAR diameter_max() {return m_diameter_max; }

}* NOZZLE_EMITTER_CONFIGURATION_BASE;


//----- Nozzle Emitter Configurations -----

typedef class sFULL_CONE_NOZZLE_CONFIGURATION : public sNOZZLE_EMITTER_CONFIGURATION_BASE
{
 private:

  sPHYSICS_VARIABLE m_cone_half_angle;
  sPDF::ePDF_TYPE m_cone_half_angle_distribution;
  sPHYSICS_VARIABLE m_outer_half_angle_limit;


 public:
  sFULL_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                  sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                  std::string &name,
                                  PARTICLE_MATERIAL material,
                                  std::string &emission_rate_variable_name,
                                  std::string &diameter_distribution_param1_variable_name,
                                  std::string &diameter_distribution_param2_variable_name,
                                  std::string &velocity_magnitude_distribution_param1_variable_name,
                                  std::string &velocity_magnitude_distribution_param2_variable_name,
                                  std::string &cone_half_angle_variable_name,
                                  std::string &outer_half_angle_limit_variable_name);

  eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return FULL_CONE;}
  LGI_EMITTER_NOZZLE_PARAM_TYPE nozzle_type() {return FULL_CONE_NOZZLE;};
  VOID initialize_physics_variables(); //defined in eqns.cc
  BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                            STP_GEOM_VARIABLE normal[3],
                                            BASETIME timestep,
                                            sFLOAT powertherm_time);
  VOID sample_spray_direction(sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]);
}*FULL_CONE_NOZZLE_CONFIGURATION;

typedef class sHOLLOW_CONE_NOZZLE_CONFIGURATION : public sNOZZLE_EMITTER_CONFIGURATION_BASE
{
 private:
  sPHYSICS_VARIABLE m_mean_angle; //angle between spray pattern maximum and sprayer axis
  sPDF::ePDF_TYPE m_angle_distribution;
  sPHYSICS_VARIABLE m_angle_stddev;
  sPHYSICS_VARIABLE m_inner_half_angle_limit; //added for cdi/513 (10/26/2016)
  sPHYSICS_VARIABLE m_outer_half_angle_limit;
 public:
  sHOLLOW_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                    sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                    std::string &name,
                                    PARTICLE_MATERIAL material,
                                    std::string &emission_rate_variable_name,
                                    std::string &diameter_distribution_param1_variable_name,
                                    std::string &diameter_distribution_param2_variable_name,
                                    std::string &velocity_magnitude_distribution_param1_variable_name,
                                    std::string &velocity_magnitude_distribution_param2_variable_name,
                                    std::string &mean_angle_variable_name,
                                    std::string &angle_stddev_variable_name,
                                    std::string &outer_half_angle_limit_variable_name,
                                    std::string &inner_half_angle_limit_variable_name);


  eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return HOLLOW_CONE;}
  LGI_EMITTER_NOZZLE_PARAM_TYPE nozzle_type() {return HOLLOW_CONE_NOZZLE;};
  VOID initialize_physics_variables(); //defined in eqns.cc
  BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                            STP_GEOM_VARIABLE normal[3],
                                            BASETIME timestep,
                                            sFLOAT powertherm_time);
  VOID sample_spray_direction(sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]);
}* HOLLOW_CONE_NOZZLE_CONFIGURATION;



typedef class sELLIPTICAL_CONE_NOZZLE_CONFIGURATION : public sNOZZLE_EMITTER_CONFIGURATION_BASE
{
 private:
  sPDF::ePDF_TYPE m_major_cone_half_angle_distribution;
  sPHYSICS_VARIABLE m_major_cone_half_angle_distribution_param1;
  sPHYSICS_VARIABLE m_major_cone_half_angle_distribution_param2;
  sPDF::ePDF_TYPE m_minor_cone_half_angle_distribution;
  sPHYSICS_VARIABLE m_minor_cone_half_angle_distribution_param1;
  sPHYSICS_VARIABLE m_minor_cone_half_angle_distribution_param2;
  sPHYSICS_VARIABLE m_major_half_angle_limit; //added for cdi/513 (10/26/2016)
  sPHYSICS_VARIABLE m_minor_half_angle_limit;
 public:
  sELLIPTICAL_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                        sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                        std::string &name,
                                        PARTICLE_MATERIAL material,
                                        std::string &emission_rate_variable_name,
                                        std::string &diameter_distribution_param1_variable_name,
                                        std::string &diameter_distribution_param2_variable_name,
                                        std::string &velocity_magnitude_distribution_param1_variable_name,
                                        std::string &velocity_magnitude_distribution_param2_variable_name,
                                        std::string &major_cone_half_angle_distribution_param1_variable_name,
                                        std::string &major_cone_half_angle_distribution_param2_variable_name,
                                        std::string &minor_cone_half_angle_distribution_param1_variable_name,
                                        std::string &minor_cone_half_angle_distribution_param2_variable_name,
                                        std::string &major_outer_half_angle_limit_variable_name,
                                        std::string &minor_outer_half_angle_limit_variable_name);
  eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return ELLIPTICAL_CONE;}
  LGI_EMITTER_NOZZLE_PARAM_TYPE nozzle_type() {return(ELLIPTICAL_CONE_NOZZLE);};
  VOID initialize_physics_variables(); //defined in eqns.cc
  BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                            STP_GEOM_VARIABLE normal[3],
                                            BASETIME timestep,
                                            sFLOAT powertherm_time);
  VOID sample_spray_direction(sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]);
}* ELLIPTICAL_CONE_NOZZLE_CONFIGURATION;


//----- Rain Emitter Configurations -------

typedef class sRAIN_EMITTER_CONFIGURATION : public virtual sKNOWN_DIAMETER_SPRAY_DATA_BASE ,
  public sPARTICLE_EMITTER_CONFIGURATION_BASE
{
 private:
  sPHYSICS_VARIABLE m_free_stream_velocity_x;
  sPHYSICS_VARIABLE m_free_stream_velocity_y;
  sPHYSICS_VARIABLE m_free_stream_velocity_z;
 sPARTICLE_VAR m_free_stream_velocity[N_SPACE_DIMS];
 asINT32 m_free_stream_velocity_csys;
 asINT32 m_free_stream_velocity_refrerence_frame_index;

 public:
 sRAIN_EMITTER_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                             sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                             std::string &name,
                             PARTICLE_MATERIAL material,
                             std::string &emission_rate_variable_name,
                             std::string &diameter_distribution_param1_variable_name,
                             std::string &diameter_distribution_param2_variable_name,
                             std::string &wind_velocity_x_variable_name,
                             std::string &wind_velocity_y_variable_name,
                             std::string &wind_velocity_z_variable_name) :
  sKNOWN_DIAMETER_SPRAY_DATA_BASE(nozzle_record,
                                  diameter_distribution_param1_variable_name,
                                  diameter_distribution_param2_variable_name,
                                  material,
                                  material->is_liquid()),
    sPARTICLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                         name,
                                         emission_rate_variable_name)
      {
        m_free_stream_velocity_csys = lgi_record.specialized_parameters.rain_params.wind_velocity_csys_index;
        asINT32 cdi_ref_frame_index = lgi_record.specialized_parameters.rain_params.wind_velocity_reference_frame_index;
        m_free_stream_velocity_refrerence_frame_index = translate_cdi_ref_frame_index(cdi_ref_frame_index);

        m_free_stream_velocity_x.value = lgi_record.specialized_parameters.rain_params.wind_x_constant;
        m_free_stream_velocity_x.name = nullptr;
        if(lgi_record.specialized_parameters.rain_params.wind_x_name_length != 0) {
          m_free_stream_velocity_x.name = cnew char[lgi_record.specialized_parameters.rain_params.wind_x_name_length + 1];
          strncpy((char*)m_free_stream_velocity_x.name, wind_velocity_x_variable_name.c_str(),  lgi_record.specialized_parameters.rain_params.wind_x_name_length);
          //((char*)m_free_stream_velocity_x.name)[lgi_record.specialized_parameters.rain_params.wind_x_name_length] = '\0';
        }

        m_free_stream_velocity_y.value = lgi_record.specialized_parameters.rain_params.wind_y_constant;
        m_free_stream_velocity_y.name = nullptr;
        if(lgi_record.specialized_parameters.rain_params.wind_y_name_length != 0) {
          m_free_stream_velocity_y.name = cnew char[lgi_record.specialized_parameters.rain_params.wind_y_name_length + 1];
          strncpy((char*)m_free_stream_velocity_y.name, wind_velocity_y_variable_name.c_str(),  lgi_record.specialized_parameters.rain_params.wind_y_name_length);
          //((char*)m_free_stream_velocity_y.name)[lgi_record.specialized_parameters.rain_params.wind_y_name_length] = '\0';
        }

        m_free_stream_velocity_z.value = lgi_record.specialized_parameters.rain_params.wind_z_constant;
        m_free_stream_velocity_z.name = nullptr;
        if(lgi_record.specialized_parameters.rain_params.wind_z_name_length != 0) {
          m_free_stream_velocity_z.name = cnew char[lgi_record.specialized_parameters.rain_params.wind_z_name_length + 1];
          strncpy((char*)m_free_stream_velocity_z.name, wind_velocity_z_variable_name.c_str(),  lgi_record.specialized_parameters.rain_params.wind_z_name_length);
          //((char*)m_free_stream_velocity_z.name)[lgi_record.specialized_parameters.rain_params.wind_z_name_length] = '\0';
        }
        vzero(m_free_stream_velocity);
      }

  VOID initialize_physics_variables(); //defined in eqns.cc
  BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                            STP_GEOM_VARIABLE normal[3],
                                            BASETIME timestep,
                                            sFLOAT powertherm_time);
  dFLOAT expected_particle_diameter();
  dFLOAT expected_particle_diameter_stddev();
  dFLOAT expected_particle_volume();
  dFLOAT evaluate_particle_diameter_pdf(sPARTICLE_VAR diameter);
  VOID sample_spray_direction(sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]);

  VOID free_stream_velocity(sPARTICLE_VAR free_stream_velocity[3]){
    vcopy(free_stream_velocity, m_free_stream_velocity);
  }

  sPDF::ePDF_TYPE diameter_distribution() { return m_diameter_distribution; }
  sPARTICLE_VAR diameter_distribution_param1()   { return m_diameter_distribution_param1.value; }
  sPARTICLE_VAR diameter_distribution_param2()   { return m_diameter_distribution_param2.value; }
  PARTICLE_MATERIAL material() {return m_material; }
  sPARTICLE_VAR diameter_min() {return m_diameter_min; }
  sPARTICLE_VAR diameter_max() {return m_diameter_max; }
  
  eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return RAIN;}
}* RAIN_EMITTER_CONFIGURATION;

//----- Tire Emitter Configurations -------
typedef class sTIRE_ARC_STATION_DATA : public virtual sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE
{
  //This class defines the data stored at a particular station around the tire emitter arc.
  //Tire emitter configurations maintain a vector of these objects.
 public:
  sPHYSICS_VARIABLE m_tire_arc_position;
  sPHYSICS_VARIABLE m_offset_angle;
  sPDF::ePDF_TYPE m_offset_angle_distribution;
  sPHYSICS_VARIABLE m_cone_half_angle;
  sPHYSICS_VARIABLE m_stretch_factor;
 public:
  sTIRE_ARC_STATION_DATA(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                         std::string &diameter_distribution_param1_variable_name,
                         std::string &diameter_distribution_param2_variable_name,
                         PARTICLE_MATERIAL material,
                         BOOLEAN is_liquid_not_solid,
                         std::string &velocity_magnitude_distribution_param1_variable_name,
                         std::string &velocity_magnitude_distribution_param2_variable_name,
                         std::string &tire_arc_position_variable_name,
                         std::string &emission_offset_angle_variable_name,
                         std::string &cone_half_angle_varaible_name,
                         std::string &stretch_factor_variable_name);
  typedef struct sPREDICATE { //for sorting and searching of std vectors of these data elements.
    sdFLOAT m_tire_arc_position;
    sPREDICATE(){};
    sPREDICATE(sFLOAT tire_arc_position) {
      m_tire_arc_position = tire_arc_position;
    }

    //Identity defintion:
    bool operator()(sTIRE_ARC_STATION_DATA const &a) const {
      return( a.m_tire_arc_position.value == m_tire_arc_position);
    }

    //Order definition:
    bool operator()(sTIRE_ARC_STATION_DATA const &a, sTIRE_ARC_STATION_DATA const &b) const {
      return( a.m_tire_arc_position.value < b.m_tire_arc_position.value );
    }
  }*PREDICATE;
}*TIRE_ARC_STATION_DATA;

#include "read_pm_lgi_records.h"
typedef class sTIRE_EMITTER_CONFIGURATION : public sPARTICLE_EMITTER_CONFIGURATION_BASE
{
 private:
  PARTICLE_MATERIAL m_material;
  asINT32 m_material_index;
  sPDF::ePDF_TYPE m_angular_emission_distribution_type;
  sPHYSICS_VARIABLE m_cdi_angular_emission_distribution_parameter;
  sPHYSICS_VARIABLE m_cdi_angular_emission_distribution_ratio;
  sPARTICLE_VAR m_pdfs_angular_emission_dist_param1;
  sPARTICLE_VAR m_pdfs_angular_emission_dist_param2;
  sPARTICLE_VAR m_pdfs_angular_emission_dist_param3;
  sPARTICLE_VAR m_start;
  sPARTICLE_VAR m_end;
  std::vector<sTIRE_ARC_STATION_DATA> m_configuration_interpolation_nodes;
  sPARTICLE_VAR m_average_particle_diameter;
  sPARTICLE_VAR m_average_particle_diameter_stddev;
  sPARTICLE_VAR m_average_particle_volume;
 public:
  sTIRE_EMITTER_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                              std::vector<sLGI_NOZZLE_CONFIGURATION_REC_READER> &nozzle_records,
                              std::string &name,
                              PARTICLE_MATERIAL material,
                              std::string &emission_rate_variable_name,
                              std::string &cdi_angular_emission_dist_parameter_variable_name,
                              std::string &cdi_angular_emission_dist_ratio_variable_name,
                              std::vector<std::string> &diameter_distribution_param1_variable_names,
                              std::vector<std::string> &diameter_distribution_param2_variable_names,
                              std::vector<std::string> &velocity_magnitude_distribution_param1_variable_names,
                              std::vector<std::string> &velocity_magnitude_distribution_param2_variable_names,
                              std::vector<std::string> &tire_arc_position_variable_names,
                              std::vector<std::string> &emission_offset_angle_variable_names,
                              std::vector<std::string> &cone_half_angle_variable_names,
                              std::vector<std::string> &stretch_factor_variable_names );
  PARTICLE_MATERIAL material() {return m_material;}
  VOID initialize_physics_variables(); //defined in eqns.cc
  BOOLEAN eval_time_only_varying_physics_variables(BASETIME timestep, sFLOAT powertherm_time);
  VOID eval_space_varying_physics_variables(STP_GEOM_VARIABLE point[3],
                                            STP_GEOM_VARIABLE normal[3],
                                            BASETIME timestep,
                                            sFLOAT powertherm_time);
  sPDF::ePDF_TYPE velocity_magnitude_distribution() { return m_configuration_interpolation_nodes[0].velocity_magnitude_distribution(); }
  sPDF::ePDF_TYPE diameter_distribution() { return m_configuration_interpolation_nodes[0].diameter_distribution(); }
  sPDF::ePDF_TYPE cone_half_angle_distribution() { return m_configuration_interpolation_nodes[0].m_offset_angle_distribution; }
  eEMITTER_CONFIGURATION_TYPES emitter_configuration_type() {return TIRE;}
  dFLOAT expected_particle_volume() {return m_average_particle_volume; } //These quantities are computed by the constructor using SIMUTILS
  dFLOAT expected_particle_diameter() {return m_average_particle_diameter;};
  dFLOAT expected_particle_diameter_stddev() {return m_average_particle_diameter_stddev;};
  sPARTICLE_VAR diameter_distribution_param1()   { return m_configuration_interpolation_nodes[0].diameter_distribution_param1(); }
  sPARTICLE_VAR diameter_distribution_param2()   { return m_configuration_interpolation_nodes[0].diameter_distribution_param2(); }
  sPARTICLE_VAR diameter_min() {return 0.0; }
  sPARTICLE_VAR diameter_max() {return -1; }

  //Find the two stations between which the specified angle resides.
  asINT32 find_interpolation_neighbor_nodes(sPARTICLE_VAR angle);

  //Compute the fraction of distance a particular angle is within an interval between two stations.
  sPARTICLE_VAR compute_interpolation_fraction(asINT32 first_interpolation_node_index, sPARTICLE_VAR angle) {
    sPARTICLE_VAR fraction_of_interval = (angle - m_configuration_interpolation_nodes[first_interpolation_node_index].m_tire_arc_position.value ) /
      (m_configuration_interpolation_nodes[first_interpolation_node_index + 1].m_tire_arc_position.value -
       m_configuration_interpolation_nodes[first_interpolation_node_index].m_tire_arc_position.value);
    return fraction_of_interval;
  }
  //Interpolate various properties at the specified angle:
  sPARTICLE_VAR velocity_magnitude_distribution_param1(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR velocity_mag_dist_param1_left = m_configuration_interpolation_nodes[first_interpolation_node_index].velocity_magnitude_distribution_param1();
    sPARTICLE_VAR velocity_mag_dist_param1_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].velocity_magnitude_distribution_param1();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return velocity_mag_dist_param1_left * (1.0 - fraction_of_interval) + velocity_mag_dist_param1_right * fraction_of_interval;
  }
  sPARTICLE_VAR velocity_magnitude_distribution_param2(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR velocity_mag_dist_param2_left = m_configuration_interpolation_nodes[first_interpolation_node_index].velocity_magnitude_distribution_param2();
    sPARTICLE_VAR velocity_mag_dist_param2_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].velocity_magnitude_distribution_param2();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return velocity_mag_dist_param2_left * (1.0 - fraction_of_interval) + velocity_mag_dist_param2_right * fraction_of_interval;
  }
  sPARTICLE_VAR diameter_distribution_param1(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR diameter_dist_param1_left = m_configuration_interpolation_nodes[first_interpolation_node_index].diameter_distribution_param1();
    sPARTICLE_VAR diameter_dist_param1_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].diameter_distribution_param1();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return diameter_dist_param1_left * (1.0 - fraction_of_interval) + diameter_dist_param1_right * fraction_of_interval;
  }
  sPARTICLE_VAR diameter_distribution_param2(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR diameter_dist_param2_left = m_configuration_interpolation_nodes[first_interpolation_node_index].diameter_distribution_param2();
    sPARTICLE_VAR diameter_dist_param2_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].diameter_distribution_param2();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return diameter_dist_param2_left * (1.0 - fraction_of_interval) + diameter_dist_param2_right * fraction_of_interval;
  }
  sPARTICLE_VAR diameter_min(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR diameter_min_left = m_configuration_interpolation_nodes[first_interpolation_node_index].diameter_min();
    sPARTICLE_VAR diameter_min_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].diameter_min();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return diameter_min_left * (1.0 - fraction_of_interval) + diameter_min_right * fraction_of_interval;
  }
  sPARTICLE_VAR diameter_max(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR diameter_max_left = m_configuration_interpolation_nodes[first_interpolation_node_index].diameter_max();
    sPARTICLE_VAR diameter_max_right = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].diameter_max();
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return diameter_max_left * (1.0 - fraction_of_interval) + diameter_max_right * fraction_of_interval;
  }
  sPARTICLE_VAR cone_half_angle(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR cone_half_angle1 = m_configuration_interpolation_nodes[first_interpolation_node_index].m_cone_half_angle.value;
    sPARTICLE_VAR cone_half_angle2 = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].m_cone_half_angle.value;
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return cone_half_angle1 * (1.0 - fraction_of_interval) + cone_half_angle2 * fraction_of_interval;
  }
  sPARTICLE_VAR offset_angle(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR offset_angle1 = m_configuration_interpolation_nodes[first_interpolation_node_index].m_offset_angle.value;
    sPARTICLE_VAR offset_angle2 = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].m_offset_angle.value;
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return offset_angle1 * (1.0 - fraction_of_interval) + offset_angle2 * fraction_of_interval;
  }
  sPARTICLE_VAR stretch_factor(sPARTICLE_VAR angle) {
    asINT32 first_interpolation_node_index = find_interpolation_neighbor_nodes(angle);
    sPARTICLE_VAR stretch_factor1 = m_configuration_interpolation_nodes[first_interpolation_node_index].m_stretch_factor.value;
    sPARTICLE_VAR stretch_factor2 = m_configuration_interpolation_nodes[first_interpolation_node_index + 1].m_stretch_factor.value;
    sPARTICLE_VAR fraction_of_interval = compute_interpolation_fraction(first_interpolation_node_index, angle);
    return stretch_factor1 * (1.0 - fraction_of_interval) + stretch_factor2 * fraction_of_interval;
  }
  sPARTICLE_VAR sample_arc_position();
  VOID sample_spray_direction(sPARTICLE_VAR angle, sPARTICLE_VAR velocity_direction[N_SPACE_DIMS]);
  sPARTICLE_VAR sample_spray_velocity_magnitude(sPARTICLE_VAR angle);
  sPARTICLE_VAR sample_spray_droplet_size(sPARTICLE_VAR angle);

}*TIRE_EMITTER_CONFIGURATION;

SIMUTILS::ePDF_TYPES convert_pdfs_pdf_type_to_simutils_pdf_type(sPDF::ePDF_TYPE pdfs_pdf_type);

#endif
