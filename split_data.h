#ifndef SPLIT_INFO_H
#define SPLIT_INFO_H
#include "offset_based_interface.h"
#include "bitset.h"
#include "simulator_namespace.h"

/*=================================================================================================
 * To help with debugging, setting DEBUG_SPLIT_ADVECT_FACTORS to 1, will require client code to provide
 * additional information at the call sites, which is matched against additional order data that is stored
 * as part of the split factors during initialization.
 *
 * Note that if you set the DEBUG_SPLIT_ADVECT_FACTORS macro to 1 below for debugging split
 * factors, you will have to recompile both PHYSICS and SIMENG
 * This is to have consistent code implementations for sSPLIT_ADVECT_FACTORS
 * in both components. If both components are not compiled, 
 * you might see a segfault or undefined behavior
 *=================================================================================================*/
#define DEBUG_SPLIT_ADVECT_FACTORS 0

enum class sSPLIT_ADVECT_FACTOR_COMPILE_MODE : bool {
  debug   = false,
  release = true
};

enum SPLIT_FACTOR_TYPE {
  SPEED1,
  SPEED2
};

template<size_t N_VOXELS, SPLIT_FACTOR_TYPE>
struct SPLIT_FACTOR_TRAITS;

constexpr static sINT16 INVALID_SPLIT_START_INDEX = -1;

template<size_t N_VOXELS>
struct SPLIT_FACTOR_TRAITS<N_VOXELS, SPEED1> {
  enum {
    RESERVE_COUNT = 64,
    N_START_INDICES = N_STATES
  };
  using SPLIT_NBR_MASK = tBITSET<N_STATES, uINT32>;
};

template<size_t N_VOXELS>
struct SPLIT_FACTOR_TRAITS<N_VOXELS, SPEED2> {
  enum {
    RESERVE_COUNT = 16,
    N_START_INDICES = N_CARDINAL_DIRECTIONS
  };
  using SPLIT_NBR_MASK = tBITSET<N_CARDINAL_DIRECTIONS, uINT8>;
};

/*=================================================================================================
 * @struct tSPLIT_ADVECT_FACTORS_DATA
 * This is the lowest level layer in the SPLIT FACTOR information heirarchy
 * 
 * sSPLIT_INFO->sSPLIT_ADVECT_FACTORs->SPLIT_ADVECT_FACTORS_DATA
 *
 * This structure contains the actual split factor array "m_factors", which is a contiguous array of
 * floats that must be accessed by client code in "advection" order. While this makes client code
 * tricky, it allows for more efficient memory loads, and avoids overhead associated with factor lookup.
 *
 * There is additional auxillary information indication where split factors for certain neighbors begin
 * "m_start_indices".
 *=================================================================================================*/
template<typename TRAITS>
struct tSPLIT_ADVECT_FACTORS_DATA;

template<SPLIT_FACTOR_TYPE SFT>
struct tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_8, SFT>> {

  using TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_8, SFT>;
  
  tSPLIT_ADVECT_FACTORS_DATA(){
    m_factors.reserve(TRAITS::RESERVE_COUNT);
    for (auto& index : m_start_indices) { index = INVALID_SPLIT_START_INDEX; };
  }

  tSPLIT_ADVECT_FACTORS_DATA(const tSPLIT_ADVECT_FACTORS_DATA&) = delete;
  tSPLIT_ADVECT_FACTORS_DATA(tSPLIT_ADVECT_FACTORS_DATA&&) = delete;  
  
  bool is_empty() const { return m_factors.empty(); }

  void advance_split_factor_index(size_t npos) const {
    m_index += npos;
    cassert(m_index <= m_factors.size());
  }

  const sFLOAT* next_factor_ptr() const {
    return &m_factors[m_index];
  }

  /* Use for vectorized multiply-add*/
  sFLOAT* next_factor_ptr() {
    return &m_factors[m_index];
  }  

  /* Use for scalar multiply-add*/  
  sFLOAT next_factor() const {
    sFLOAT factor = m_factors[m_index];
    m_index++;
    return factor;
  }

  VOID add(sFLOAT val) {
    m_factors.push_back(val);
  }

  VOID reset() const {
    //We should have accessed all factors we added
    assert(m_index == m_factors.size());
    m_index = 0;
  }

  VOID reset_without_assert() const {
    m_index = 0;
  }

  /* @fcn repos_to_read_factors_for_neighbor
   *  Reposition the split factor index (m_index) to begin reading from the start of
   *  user specified neighbor offset (0  - 18 for D19 lattice)
   */  
  VOID repos_to_read_factors_for_neighbor(asINT32 neighbor_offset) const {
    m_index = m_start_indices[neighbor_offset];
    dassert(m_index != INVALID_SPLIT_START_INDEX);
  }


  VOID set_start_index_for_latvec(asINT32 latvec,sINT16 index) {
    m_start_indices[latvec] = index;
  }

  /* @fcn repos_to_read_factors_for_neighbor_V_0_0_0_along_latvec
   *  Reposition the split factor index (m_index) to begin reading from the start of
   *  user specified latvec, for self contribution (i.i, neighbor V_0_0_0)
   */
  VOID repos_to_read_factors_for_neighbor_V_0_0_0_along_latvec(asINT32 latvec, asINT32 num_instances) const;

  size_t num_factors() const { return m_factors.size(); }

  sFLOAT* factors() { return &m_factors[0]; }

  sINT16* start_indices() { return &m_start_indices[0]; }
  
protected:
  
  mutable asINT32 m_index = 0;
  //Start indices to the split factor contributions off different neighbors
  std::array<sINT16, TRAITS::N_START_INDICES> m_start_indices;
  std::vector<sFLOAT> m_factors;  
  
};

using UBLK_SPEED1_SPLIT_FACTOR_TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_8, SPLIT_FACTOR_TYPE::SPEED1>;

/* repos_to_read_factors_for_neighbor_V_0_0_0_along_latvec
 * Implementation makes sense only for SPEED1 split factors since SPEED2 factors by definition lie outside the UBLK
 */
template<>
VOID INLINE
tSPLIT_ADVECT_FACTORS_DATA<UBLK_SPEED1_SPLIT_FACTOR_TRAITS>::repos_to_read_factors_for_neighbor_V_0_0_0_along_latvec(asINT32 latvec,
                                                                                                                     asINT32 num_instances) const {
  m_index = m_start_indices[V_0_0_0];
  const asINT32 jump = sim.is_2d() ? 2 : 4;
  assert(m_index != -1);
  m_index = m_index + jump * latvec * num_instances;
}

/*=================================================================================================
 * @enums SPEED1_COMPONENTS and SPEED2_COMPONENTS
 * These enum lists are used to defined the tOFFSET_BASED_INTERFACE for GPU split data
 *=================================================================================================*/
enum class SPEED1_COMPONENTS : int {
  INTERACTIONS = 0,
  FACTORS = 1,
  NBR_UBLK_FACTORS_START_INDICES = 2,
  DEBUG_DATA = 3, // Only holds data when DEBUG_SPLIT_ADVECT_FACTORS macro is 1
  N_COMPONENTS = 4
};

enum class SPEED2_COMPONENTS : int {
  FACTORS = 0,
  NBR_UBLK_FACTORS_START_INDICES = 1,
  DEBUG_DATA = 2,  // Only holds data when DEBUG_SPLIT_ADVECT_FACTORS macro is 1
  N_COMPONENTS = 3
};

struct SPLIT_ADVECT_INTERACTION {

  __HOST__ VOID set_is_reduction_thread() { m_is_reduction_thread = 1; }
  __HOST__ VOID set_is_parity() { m_is_parity = 1; }
  __HOST__ VOID set_is_sync_needed() { m_is_sync_needed = 1; }
  __HOST__ void set_neighbor_offset(asINT32 offset) { m_neighbor_offset = offset; }

  __HOST__DEVICE__ std::uint8_t is_reduction_thread() { return m_is_reduction_thread; }
  __HOST__DEVICE__ std::uint8_t is_parity() { return m_is_parity; }
  __HOST__DEVICE__ std::uint8_t sync_needed() { return m_is_sync_needed; }
  __HOST__DEVICE__ std::uint8_t neighbor_offset() { return m_neighbor_offset; }
  
private:
  union {
    std::uint8_t m_mask;
    struct {
      std::uint8_t m_is_reduction_thread : 1;
      std::uint8_t m_is_parity : 1;
      std::uint8_t m_is_sync_needed: 1;
      std::uint8_t m_neighbor_offset: 5;
    };
  };  
};

using sMBLK_SPEED1_SPLIT_ADVECT_FACTORS_DATA = tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED1>>;
using sMBLK_SPEED2_SPLIT_ADVECT_FACTORS_DATA = tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED2>>;

template<SPLIT_FACTOR_TYPE SFT>
using tSPLIT_FACTOR_COMPONENT_TYPE = typename std::conditional<SFT == SPEED1,
                                                              SPEED1_COMPONENTS,
                                                              SPEED2_COMPONENTS>::type;

struct sSPLIT_FACTOR_DEBUG_INFO;

/*=================================================================================================
 * @struct tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>>
 * Specialization for holding split data for MBLKs on the GPU
 * Unlike the CPU version, this is based on the OFFSET_BASE_INTERFACE, to allow for coalesced access
 * of data. Additionally the offset interface accounts for the fact the CUDA does not support STL
 * vectors as of this writing.
 *=================================================================================================*/
template<SPLIT_FACTOR_TYPE SFT>
struct tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>> :
  public  tOFFSET_BASED_INTERFACE<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>, float,
                                  tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>>>
{

  using TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_64, SFT>;
  using SPLIT_MASK = typename TRAITS::SPLIT_NBR_MASK;
  
  __HOST__ tSPLIT_ADVECT_FACTORS_DATA(): m_split_neighbor_masks{0} {}

  __HOST__ VOID set_split_nbr_mask_for_child_ublk(int child_ublk, SPLIT_MASK m) {
    m_split_neighbor_masks[child_ublk] = m;
  }

  __HOST__ SPLIT_MASK get_nbr_neighbor_mask_for_latvec(int child_ublk) const {
    return m_split_neighbor_masks[child_ublk];
  }

  __HOST__ VOID set_nbr_ublk_factors_start_index_offsets(int child_ublk, uint8_t offset) {
    m_nbr_ublk_factors_start_index_offsets[child_ublk] = offset;
  }

  __HOST__ uint8_t get_nbr_ublk_factors_start_index_offsets(int child_ublk) const {
    return m_nbr_ublk_factors_start_index_offsets[child_ublk];
  }  

  __HOST__DEVICE__ SPLIT_MASK* split_neighbor_masks() { return m_split_neighbor_masks; }
  
  __HOST__DEVICE__ sINT16* nbr_ublk_factors_start_indices() {
    return this->template get<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>::NBR_UBLK_FACTORS_START_INDICES>();
  }

  __HOST__DEVICE__ size_t num_nbr_ublk_factors_start_indices() const {
    return this->template get_n_elements<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>::NBR_UBLK_FACTORS_START_INDICES>();
  }
  
  __HOST__DEVICE__ sFLOAT* get_factors_start_location(int child_ublk,
                                                      int nbr_latvec,
                                                      int advect_latvec,
                                                      int num_split_instaces);
  
  __HOST__DEVICE__ sFLOAT* factors() { return this->template get<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>::FACTORS>(); }

  __HOST__DEVICE__ sSPLIT_FACTOR_DEBUG_INFO* debug_data() {
    return this->template get<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>::DEBUG_DATA>();
  }

  __HOST__DEVICE__ sINT16 num_factors() const { return this->template get_n_elements<tSPLIT_FACTOR_COMPONENT_TYPE<SFT>::FACTORS>(); }
  
protected:
  
  SPLIT_MASK m_split_neighbor_masks[N_VOXELS_64 / N_VOXELS_8];
  uint8_t m_nbr_ublk_factors_start_index_offsets[N_VOXELS_64 / N_VOXELS_8];

  //SPLIT_ADVECT_INTERACTION* s;
  //sFLOAT* factors;
  //sINT16* nbr_start_index_offsets;
  //
  //#if DEBUG_SPLIT_ADVECT_FACTORS
  //sDEBUG_INFO*
  //#endif
};

template<>
template<>
struct sMBLK_SPEED1_SPLIT_ADVECT_FACTORS_DATA::COMPONENT_TRAITS<SPEED1_COMPONENTS::INTERACTIONS> { using type = SPLIT_ADVECT_INTERACTION; };

template<>
template<>
struct sMBLK_SPEED1_SPLIT_ADVECT_FACTORS_DATA::COMPONENT_TRAITS<SPEED1_COMPONENTS::NBR_UBLK_FACTORS_START_INDICES> { using type = sINT16; };

template<>
template<>
struct sMBLK_SPEED2_SPLIT_ADVECT_FACTORS_DATA::COMPONENT_TRAITS<SPEED2_COMPONENTS::NBR_UBLK_FACTORS_START_INDICES> { using type = sINT16; };

template<>
template<>
struct sMBLK_SPEED1_SPLIT_ADVECT_FACTORS_DATA::COMPONENT_TRAITS<SPEED1_COMPONENTS::DEBUG_DATA> { using type = sSPLIT_FACTOR_DEBUG_INFO; };

template<>
template<>
struct sMBLK_SPEED2_SPLIT_ADVECT_FACTORS_DATA::COMPONENT_TRAITS<SPEED2_COMPONENTS::DEBUG_DATA> { using type = sSPLIT_FACTOR_DEBUG_INFO; };


using MBLK_SPEED1_SPLIT_FACTOR_TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPLIT_FACTOR_TYPE::SPEED1>;
using MBLK_SPEED2_SPLIT_FACTOR_TRAITS = SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPLIT_FACTOR_TYPE::SPEED2>;
using MBLK_SPEED1_SPLIT_FACTOR_DATA = tSPLIT_ADVECT_FACTORS_DATA<MBLK_SPEED1_SPLIT_FACTOR_TRAITS>;
using MBLK_SPEED2_SPLIT_FACTOR_DATA = tSPLIT_ADVECT_FACTORS_DATA<MBLK_SPEED2_SPLIT_FACTOR_TRAITS>;


__HOST__DEVICE__ INLINE
int latvec_offset_from_start_of_neighbor_split_factors_3D(int nbr_latvec,
                                                          int advect_latvec) {

  // nbr-latvec -> advect-latvec
  constexpr int8_t latvec_offset_table_3D[N_CARDINAL_DIRECTIONS + 1/*self*/][N_MOVING_STATES] = {
  // latvecs
  //  0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17
    {-1,  0, -1, -1, -1, -1, -1,  4, -1,  6,  8, -1, 10, -1, -1, -1, -1, -1}, // +X
    { 0, -1, -1, -1, -1, -1,  4, -1,  6, -1, -1,  8, -1, 10, -1, -1, -1, -1}, // -X
    {-1, -1, -1,  0, -1, -1,  4, -1, -1,  6, -1, -1, -1, -1,  8, -1, -1, 10}, // +Y
    {-1, -1,  0, -1, -1, -1, -1,  4,  6, -1, -1, -1, -1, -1, -1,  8, 10, -1}, // -Y
    {-1, -1, -1, -1, -1,  0, -1, -1, -1, -1, -1,  4,  6, -1, -1,  8, -1, 10}, // +Z
    {-1, -1, -1, -1,  0, -1, -1, -1, -1, -1,  4, -1, -1,  6,  8, -1, 10, -1}, // -Z
    { 0,  4,  8, 12, 16, 20, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46}  // V_0_0_0
  };

  if (nbr_latvec < N_CARDINAL_DIRECTIONS) {
    return latvec_offset_table_3D[nbr_latvec][advect_latvec];
  } else if (nbr_latvec == V_0_0_0) {
    return latvec_offset_table_3D[N_CARDINAL_DIRECTIONS][advect_latvec];
  } else {
    return 0;
  }
}

__HOST__DEVICE__ INLINE
int latvec_offset_from_start_of_neighbor_split_factors_2D(int nbr_latvec,
                                                          int advect_latvec) {

  constexpr size_t N_CARDINAL_DIRECTIONS_2D = 4;
  // nbr-latvec -> advect-latvec
  constexpr int8_t latvec_offset_table_2D[N_CARDINAL_DIRECTIONS_2D + 1/*self*/][N_MOVING_STATES] = {
  // latvecs
  //  0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17
    {-1,  0, -1, -1, -1, -1, -1,  2, -1,  3,  4, -1,  6, -1, -1, -1, -1, -1}, // +X
    { 0, -1, -1, -1, -1, -1,  2, -1,  3, -1, -1,  4, -1,  6, -1, -1, -1, -1}, // -X
    {-1, -1, -1,  0, -1, -1,  2, -1, -1,  3, -1, -1, -1, -1,  4, -1, -1,  6}, // +Y
    {-1, -1,  0, -1, -1, -1, -1,  2,  3, -1, -1, -1, -1, -1, -1,  4,  6, -1}, // -Y
    { 0,  2,  4,  6, -1, -1,  8,  9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26}  // V_0_0_0
  };

  if (nbr_latvec < N_CARDINAL_DIRECTIONS_2D) {
    return latvec_offset_table_2D[nbr_latvec][advect_latvec];
  } else if (nbr_latvec == V_0_0_0) {
    return latvec_offset_table_2D[N_CARDINAL_DIRECTIONS_2D][advect_latvec];
  } else {
    return 0;
  }  
}

__HOST__DEVICE__ INLINE
int latvec_offset_from_start_of_neighbor_split_factors(int nbr_latvec,
                                                       int advect_latvec) {
  return get_simc_ref().is_3d()?
    latvec_offset_from_start_of_neighbor_split_factors_3D(nbr_latvec, advect_latvec):
    latvec_offset_from_start_of_neighbor_split_factors_2D(nbr_latvec, advect_latvec);
}

template<SPLIT_FACTOR_TYPE SFT>
__HOST__DEVICE__ INLINE int thread_advect_stride(int advect_latvec) {
  if constexpr (SFT == SPEED1) {
    if (get_simc_ref().is_3d()) {
      return (advect_latvec < N_CARDINAL_DIRECTIONS)? 4 : 2;
    } else {
      //For 2D advection, cartesian directions have two interactions
      //+X+Y, -X-Y, +X-Y, -X+Y have just one interaction
      //+X+Z, -X-Z ...etc have 2 projected interactions along X-Y plane
      return (advect_latvec < N_CARDINAL_DIRECTIONS)? 2 :
             (advect_latvec < D_N1_0_P1_D19)? 1 : 2;
    }
  } else {
    return 8;
  }
}

/*=================================================================================================
 * @fcn MBLK_SPEED1_SPLIT_FACTOR_DATA::get_factors_start_location
 * Given a child_ublk for a MBLK, the neighbor latvec we advecting in, and the advect_latvec (i.e,
 * the latvec along which this neighbor contributes), and the number of split instances, this API
 * returns a pointer to first split factor relevant for the interaction described by the input args.
 *
 * Note that neighbor_latvec and advect_latvec are different. For instance the neighbor_latvec is
 * 0 for the +X neighbor, but this neighbor can contribute along advect latvecs (1, 7, 9, 10, 12)
 *=================================================================================================*/
template<>
__HOST__DEVICE__ INLINE sFLOAT* MBLK_SPEED1_SPLIT_FACTOR_DATA::get_factors_start_location(int child_ublk,
                                                                                          int nbr_latvec,
                                                                                          int advect_latvec,
                                                                                          int num_split_instaces)
{
  
  SPLIT_MASK child_split_mask = m_split_neighbor_masks[child_ublk];
  SPLIT_MASK all_bits_lower_than_nbr = SPLIT_MASK(((int) 0x1 << nbr_latvec) - 1);    
  SPLIT_MASK child_split_mask_lower_bits = child_split_mask & all_bits_lower_than_nbr;
#if DEVICE_COMPILATION_MODE    
  int nbr_index_in_start_index_offsets = __popc(child_split_mask_lower_bits.get());
#else
  int nbr_index_in_start_index_offsets = child_split_mask_lower_bits.count();
#endif
  bool is_not_V_0_0_0 = bool(V_0_0_0 ^ nbr_latvec);
  bool split_mask_has_V_0_0_0 = child_split_mask.test(V_0_0_0);
  uint8_t V_0_0_0_correction = is_not_V_0_0_0 && split_mask_has_V_0_0_0;
  // Line of code below adds 1 (V_0_0_0_correction), if the nbr_latvec is not V_0_0_0 and
  // the split mask contains V_0_0_0. This is to account for the fact that split factor
  // contributions for V_0_0_0 begin at the very start of the factors array.
  // If the split mask indicates that V_0_0_0 th bit is not active, it means that the UBLK
  // is not split, and split factors do not have self contributions. In that case, the correction
  // of adding 1 is not necessary.
  uint8_t nbr_index = V_0_0_0_correction + is_not_V_0_0_0 * nbr_index_in_start_index_offsets;
  sINT16 nbr_start_index_offset = m_nbr_ublk_factors_start_index_offsets[child_ublk] + nbr_index;
  sINT16 nbr_ublk_factor_start_index = this->nbr_ublk_factors_start_indices()[nbr_start_index_offset];
  return this->factors() + nbr_ublk_factor_start_index +
    num_split_instaces * latvec_offset_from_start_of_neighbor_split_factors(nbr_latvec, advect_latvec);
}

//Similar to MBLK_SPEED1_SPLIT_FACTOR_DATA::get_factors_start_location, refer for description
template<>
__HOST__DEVICE__ INLINE sFLOAT* MBLK_SPEED2_SPLIT_FACTOR_DATA::get_factors_start_location(int child_ublk,
                                                                                             int nbr_latvec,
                                                                                             int advect_latvec,
                                                                                             int num_split_instaces) {
  
  SPLIT_MASK child_split_mask = m_split_neighbor_masks[child_ublk];
  SPLIT_MASK all_bits_lower_than_nbr = SPLIT_MASK(((int) 0x1 << nbr_latvec) - 1);    
  SPLIT_MASK child_split_mask_lower_bits = child_split_mask & all_bits_lower_than_nbr;
#if DEVICE_COMPILATION_MODE    
  int nbr_index_in_start_index_offsets = __popc(child_split_mask_lower_bits.get());
#else
  int nbr_index_in_start_index_offsets = child_split_mask_lower_bits.count();
#endif    
  sINT16 nbr_start_index_offset = m_nbr_ublk_factors_start_index_offsets[child_ublk] + nbr_index_in_start_index_offsets;
  sINT16 nbr_ublk_factor_start_index = this->nbr_ublk_factors_start_indices()[nbr_start_index_offset];
  return this->factors() + nbr_ublk_factor_start_index;
}

struct sSPLIT_FACTOR_DEBUG_INFO {

  sSPLIT_FACTOR_DEBUG_INFO(
                           sINT8 split_instance,
                           sINT8 advect_latvec,
                           sINT8 src_voxel,
                           sINT8 dst_voxel):
    m_split_instance(split_instance),
    m_advect_latvec(advect_latvec),
    m_src_voxel(src_voxel),
    m_dst_voxel(dst_voxel)
  {

  }

  sINT8 m_split_instance; //1byte
  sINT8 m_advect_latvec; //1byte
  sINT8 m_src_voxel; //1byte
  sINT8 m_dst_voxel; //1byte
};

template<typename TRAITS>
struct tSPLIT_ADVECT_FACTORS_DATA_DEBUG : public tSPLIT_ADVECT_FACTORS_DATA<TRAITS> {
  
  sFLOAT next_factor(asINT32 split_instance,
                     asINT32 advect_latvec,
                     asINT32 src_voxel,
                     asINT32 dst_voxel) const {

    asINT32 index = this->m_index;
    assert(index < this->m_factors.size());

    const auto& debug_info = this->m_debug_info[index];

    assert(debug_info.m_split_instance == (sINT8) split_instance);
    assert(debug_info.m_advect_latvec == (sINT8) advect_latvec);
    assert(debug_info.m_src_voxel == (sINT8) src_voxel);
    assert(debug_info.m_dst_voxel == (sINT8) dst_voxel);
    this->m_index++;
    return this->m_factors[index];
  }


  VOID add(asINT32 split_instance,
           asINT32 advect_latvec,
           asINT32 src_voxel,
           asINT32 dst_voxel,
           sFLOAT  split_fac) {

    this->m_debug_info.emplace_back(
                                    (sINT8) split_instance,
                                    (sINT8) advect_latvec,
                                    (sINT8) src_voxel,
                                    (sINT8) dst_voxel
                                    );

    this->m_factors.push_back(split_fac);
  }

  sSPLIT_FACTOR_DEBUG_INFO* debug_data() { return &m_debug_info[0]; }

private:
  std::vector<sSPLIT_FACTOR_DEBUG_INFO> m_debug_info;
};

/*=================================================================================================
 * @struct SPLIT_ADVECT_FACTORS
 * The new SPLIT_ADVECT_FACTORS was introduced to serve as a replacement to sSPLIT_ADVECT_INFO. 
 * This approach lays out split factors in a contiguous array, and requires client code to access them
 * precisely in the order they were pre-arranged. This allows for efficient accesses of the factors from
 * memory unlike the old approach with sSPLIT_ADVECT_INFO, which also had an overhead related to lookup since
 * accesses were random.
 *=================================================================================================*/
template<size_t N_VOXELS>
struct tSPLIT_ADVECT_FACTORS;

template<>
struct tSPLIT_ADVECT_FACTORS<N_VOXELS_8> {
  
private:

#if DEBUG_SPLIT_ADVECT_FACTORS
  tSPLIT_ADVECT_FACTORS_DATA_DEBUG<SPLIT_FACTOR_TRAITS<N_VOXELS_8, SPLIT_FACTOR_TYPE::SPEED1>> m_speed1;
  tSPLIT_ADVECT_FACTORS_DATA_DEBUG<SPLIT_FACTOR_TRAITS<N_VOXELS_8, SPLIT_FACTOR_TYPE::SPEED2>> m_speed2;
#else
  tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_8, SPLIT_FACTOR_TYPE::SPEED1>> m_speed1;
  tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_8, SPLIT_FACTOR_TYPE::SPEED2>> m_speed2;
#endif

public:  
  template<SPLIT_FACTOR_TYPE T>
  auto& get() {
    if constexpr((int) T == SPLIT_FACTOR_TYPE::SPEED1) {
      return m_speed1;
    } else {
      return m_speed2;
    }
  }

  template<SPLIT_FACTOR_TYPE T>  
  const auto& get() const {
    return (const_cast<tSPLIT_ADVECT_FACTORS*>(this))->get<T>();
  }

  VOID reset_without_assert() const {
    m_speed1.reset_without_assert();
    m_speed2.reset_without_assert();    
  }

  VOID reset() const {
    m_speed1.reset();
  }
};

using sSPLIT_ADVECT_FACTORS = tSPLIT_ADVECT_FACTORS<N_VOXELS_8>;

template<>
struct tSPLIT_ADVECT_FACTORS<N_VOXELS_64> {

private:  
  using SPEED1_TYPE = tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED1>>;
  using SPEED2_TYPE = tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED2>>;

  __HOST__DEVICE__ SPEED1_TYPE& speed1() {
    return (SPEED1_TYPE&) *(reinterpret_cast<char*>(this));
  }

  __HOST__DEVICE__ SPEED2_TYPE& speed2() {
    return (SPEED2_TYPE&) *(reinterpret_cast<char*>(this) + speed1().size());
  }

public:
  // the cuda compiler complains about missing a return value here
#pragma nv_diag_suppress implicit_return_from_non_void_function
  template<SPLIT_FACTOR_TYPE T>
  __HOST__DEVICE__ auto& get() {
    if constexpr((int) T == SPLIT_FACTOR_TYPE::SPEED1) {
      return speed1();
    } else {
      return speed2();
    }
  }

  template<SPLIT_FACTOR_TYPE T>  
  __HOST__DEVICE__ const auto& get() const {
    return (const_cast<tSPLIT_ADVECT_FACTORS*>(this))->get<T>();
  }

  __HOST__ VOID set_n_component_elements(const unsigned (&n_speed1_elements) [(int) SPEED1_COMPONENTS::N_COMPONENTS],
                                         const unsigned (&n_speed2_elements) [(int) SPEED2_COMPONENTS::N_COMPONENTS]) {
    this->get<SPEED1>().set_n_component_elements(n_speed1_elements);
    this->get<SPEED2>().set_n_component_elements(n_speed2_elements);
  }
  
  __HOST__ static size_t SIZE(const unsigned (&n_speed1_elements) [(int) SPEED1_COMPONENTS::N_COMPONENTS],
                              const unsigned (&n_speed2_elements) [(int) SPEED2_COMPONENTS::N_COMPONENTS]) {
    return \
      tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED1>>::size(n_speed1_elements) +
      tSPLIT_ADVECT_FACTORS_DATA<SPLIT_FACTOR_TRAITS<N_VOXELS_64, SPEED2>>::size(n_speed2_elements);
  }

  __HOST__ size_t size() const {
    return this->get<SPEED1>().size() + this->get<SPEED2>().size();
  }
};

#if GPU_COMPILER

/*=================================================================================================
 * @struct tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE
 * A GPU thread constructs a "handle" to the split factors of interest, specified in the handle's
 * constructor. Unlike the CPU split factors, where the counter m_index is allocated as part of 
 * the split data, in the GPU world this is not viable since there are multiple
 * threads involved. One could have had the counter stored in global memory but then we would have
 * to read and write to the DRAM and "reset" when we're done. Instead having the m_index variable be
 * a local variable possible allocated as a thread register variable, seemed more performant. 
 *=================================================================================================*/
template<SPLIT_FACTOR_TYPE SFT>
struct tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE {
  __DEVICE__ tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE(tSPLIT_ADVECT_FACTORS<N_VOXELS_64>* mblk_split_factors,
                                                 int thread_voxel_index,
                                                 int child_ublk_index,
                                                 int nbr_latvec,
                                                 int advect_latvec,
                                                 int num_instances): m_factors(nullptr),
                                                                     m_index(0),
                                                                     m_stride(thread_advect_stride<SFT>(advect_latvec)) {

    cassert(mblk_split_factors);
    m_factors = mblk_split_factors->get<SFT>().factors();
    sFLOAT* factor_start_for_nbr_latvec =
      mblk_split_factors->get<SFT>().get_factors_start_location(child_ublk_index,
                                                                nbr_latvec,
                                                                advect_latvec,
                                                                num_instances);
    
    m_index = factor_start_for_nbr_latvec - m_factors;
    m_index += thread_voxel_index;
  }

  __DEVICE__ sFLOAT next_factor() const {
    sFLOAT factor = m_factors[m_index];
    m_index += m_stride;
    return factor;
  }

  __DEVICE__ VOID make_unit_stride() {
    m_stride = 1;
  }

protected:
  sFLOAT* m_factors;
  uint8_t m_stride;
  mutable int m_index;  
};

/*=================================================================================================
 * @struct tTHREAD_SPLIT_FACTOR_HANDLE_DEBUG
 * Debug version of the thread split factor handle. Similar to the debug version of the CPU split
 * factors, the next_factor method here does a stringent test to make sure threads are indeed acessing
 * the data relevant to their interactions.
 *=================================================================================================*/
template<SPLIT_FACTOR_TYPE SFT>
struct tTHREAD_SPLIT_FACTOR_HANDLE_DEBUG : public tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE<SFT> {
  __DEVICE__ tTHREAD_SPLIT_FACTOR_HANDLE_DEBUG(tSPLIT_ADVECT_FACTORS<N_VOXELS_64>* mblk_split_factors,
                                               int thread_voxel_index,
                                               int child_ublk_index,
                                               int nbr_latvec,
                                               int advect_latvec,
                                               int num_instances):
    tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE<SFT>(mblk_split_factors,
                                             thread_voxel_index,
                                             child_ublk_index,
                                             nbr_latvec,
                                             advect_latvec,
                                             num_instances),
    m_debug_info(mblk_split_factors->get<SFT>().debug_data()) {
    assert(this->m_debug_info);
  }

  __DEVICE__ sFLOAT next_factor(sINT8 split_instance,
                                sINT8 advect_latvec,
                                sINT8 child_src_voxel,
                                sINT8 child_dst_voxel) const {
    
    sFLOAT factor = this->m_factors[this->m_index];
    sSPLIT_FACTOR_DEBUG_INFO d = this->m_debug_info[this->m_index];
    assert(d.m_split_instance == split_instance);
    assert(d.m_advect_latvec == advect_latvec);
    assert(d.m_src_voxel == child_src_voxel);
    assert(d.m_dst_voxel == child_dst_voxel);    
    this->m_index += this->m_stride;
    return factor;
  }  
private:
  sSPLIT_FACTOR_DEBUG_INFO* m_debug_info;    
};

#if DEBUG_SPLIT_ADVECT_FACTORS
template<SPLIT_FACTOR_TYPE SFT>
using tTHREAD_SPLIT_FACTOR_HANDLE = tTHREAD_SPLIT_FACTOR_HANDLE_DEBUG<SFT>;
#else
template<SPLIT_FACTOR_TYPE SFT>
using tTHREAD_SPLIT_FACTOR_HANDLE = tTHREAD_SPLIT_FACTOR_HANDLE_RELEASE<SFT>;
#endif
#endif

class sSPLIT_NEIGHBOR_INFO;

/*=================================================================================================
 * @struct sADVECT_SCALE_FACTOR_INFO & sSPLIT_ADVECT_INFO
 * These are primarily used in initialization and serve as bridge to the more recent SPLIT_ADVECT_FACTOR
 * scheme. Some solvers still use this legacy infrastructure to support split processing. 
 *=================================================================================================*/

struct sADVECT_SCALE_FACTOR_INFO {
  sINT16   src_ublk_instance;
  uINT16   latvec;
  sdFLOAT  advect_scale_factor;
};

typedef struct sSPLIT_ADVECT_INFO {
  sINT16                    m_total_advect_scale_factors;
  // The dimension of m_tot_advect_scale_factors can be made ubFLOAT::N_VOXELS + 1 
  // to avoid the if condition in
  // its use or first value can be cumulative of voxel 0
  sINT16                    m_tot_advect_scale_factors[N_VOXELS_8];
  std::vector <sADVECT_SCALE_FACTOR_INFO> m_advect_scale_factor_info;
  uINT8                     m_interacts_with_single_instance;
  uINT8                     m_split_instance_index;
  uINT32                    m_advect_same_instance_mask;
  uINT32                    m_advect_one_instance_masks[N_VOXELS_8];
  uINT8                     m_src_ublk_instances[N_VOXELS_8][32];

  sSPLIT_ADVECT_INFO() {
    memset(this, 0, sizeof(*this));
  }

  __HOST__DEVICE__ uINT8 interacts_with_single_instance(int child_ublk_index = 0) { return m_interacts_with_single_instance; }
  __HOST__DEVICE__ uINT8 split_instance_index(int child_ublk_index = 0) { return m_split_instance_index; }
  
} *SPLIT_ADVECT_INFO;

/*=================================================================================================
 * @struct tSPLIT_INFO
 * Top level structure that provides to different split related data:
 * - SPLIT_ADVECT_INFO - Used in in split factor intialization and still used in voxel dynamics in some places
 * - SPLIT_ADVECT_FACTORS - Used in voxel advection and dynamics
 * - SPLIT_NEIGHBOR_INFO - Used in particle modelling
 *=================================================================================================*/
template<size_t N_VOXELS>
struct tSPLIT_INFO;

template<>
struct tSPLIT_INFO<N_VOXELS_8> {
  using sSPLIT_ADVECT_INFO_TYPE = sSPLIT_ADVECT_INFO;
  sSPLIT_ADVECT_INFO *m_split_advection_info;
  tSPLIT_ADVECT_FACTORS<N_VOXELS_8> m_split_advection_factors;
  sSPLIT_NEIGHBOR_INFO* m_split_neighbor_info;

  __HOST__DEVICE__ tSPLIT_ADVECT_FACTORS<N_VOXELS_8>* get_split_advection_factors() {
    return &this->m_split_advection_factors;
  }
  
  __HOST__DEVICE__ BOOLEAN has_split_neighbor_info() {
    return m_split_neighbor_info != nullptr;
  }
};

template<>
struct tSPLIT_INFO<N_VOXELS_64> {

  // m_interacts_with_single_instance and m_split_instance_index were part of SPLIT_ADVECT_INFO,
  // but on the GPU we don't need the other SPLIT_ADVECT_INFO members.
  //Hence we get rid of the pointer to SPLIT_ADVECT_INFO and just move the two useful members here.
  using sSPLIT_ADVECT_INFO_TYPE = tSPLIT_INFO<N_VOXELS_64>;

  VOID set_interacts_with_single_instance(int child_ublk_index, uINT8 v) {m_interacts_with_single_instance[child_ublk_index] = v; }
  VOID set_split_instance_index(int child_ublk_index, uINT8 v) {m_split_instance_index[child_ublk_index] = v; }
  
  __HOST__DEVICE__ uINT8 interacts_with_single_instance(int child_ublk_index) { return m_interacts_with_single_instance[child_ublk_index]; }
  __HOST__DEVICE__ uINT8 split_instance_index(int child_ublk_index) { return m_split_instance_index[child_ublk_index]; }

  __HOST__ static size_t SIZE(const unsigned (&n_speed1_elements) [(int) SPEED1_COMPONENTS::N_COMPONENTS],
                              const unsigned (&n_speed2_elements) [(int) SPEED2_COMPONENTS::N_COMPONENTS]) {
    return sizeof(tSPLIT_INFO<N_VOXELS_64>) + tSPLIT_ADVECT_FACTORS<N_VOXELS_64>::SIZE(n_speed1_elements, n_speed2_elements);
  }

  __HOST__ size_t size() const {
    return sizeof(*this) + get_split_advection_factors()->size();
  }
  
private:  
  uINT8 m_interacts_with_single_instance[N_VOXELS_8];
  uINT8 m_split_instance_index[N_VOXELS_8];

public:
  __HOST__DEVICE__ tSPLIT_ADVECT_FACTORS<N_VOXELS_64>* get_split_advection_factors() {
    return (tSPLIT_ADVECT_FACTORS<N_VOXELS_64>*) (reinterpret_cast<char*>(this) + sizeof(tSPLIT_INFO<N_VOXELS_64>));
  }

  __HOST__DEVICE__ const tSPLIT_ADVECT_FACTORS<N_VOXELS_64>* get_split_advection_factors() const {
    return const_cast<tSPLIT_INFO<64>*>(this)->get_split_advection_factors();
  }  
};

using sSPLIT_INFO = tSPLIT_INFO<N_VOXELS_8>;

#if BUILD_GPU
using sSPLIT_INFO_64 = tSPLIT_INFO<N_VOXELS_64>;
#endif

constexpr asINT32 INVALID_SPLIT_INSTANCE = -1;

template<typename UBLK_TYPE>
INLINE __HOST__DEVICE__
asINT32 get_single_instance_for_split_ublk(UBLK_TYPE ublk,
                                           int child_ublk_index){
  
  auto split_advect_info = ublk->get_split_advect_info();
  asINT32 split_instance_index = INVALID_SPLIT_INSTANCE;
  
  if (split_advect_info == nullptr) {
    split_instance_index = ublk->m_split_tagged_instance.get(child_ublk_index);
  } else if (split_advect_info->interacts_with_single_instance(child_ublk_index)) {
    split_instance_index = split_advect_info->split_instance_index(child_ublk_index);
  }
  return split_instance_index;
}

INLINE __HOST__ asINT32 get_single_instance_for_split_ublk(sUBLK* ublk) {
  return get_single_instance_for_split_ublk(ublk, 0);
}

template<typename UBLK_TYPE>
INLINE __HOST__DEVICE__
BOOLEAN ublk_interacts_with_multiple_split_instances(UBLK_TYPE ublk,
                                                     asINT32 split_instance_index,
                                                     asINT32 child_ublk_index) {
  if (ublk->are_any_neighbors_split()) {
    if (split_instance_index == INVALID_SPLIT_INSTANCE) {
       return TRUE;
    } else {
       return (ublk->is_split()? split_instance_index != ublk->m_split_tagged_instance.get(child_ublk_index) : FALSE);
    }
  } else {
    //If UBLK does not interact with split instances, then it definitely does not
    //interact with multiple split instances
    return FALSE;
  }
}

template<size_t N_VOXELS>
class tSPLIT_TAGGED_INSTANCE {
public:
  __HOST__ VOID set(asINT32 index, uINT8 i) {
    m_instance[index] = i;
  }
  __HOST__DEVICE__ uINT8 get(asINT32 index) {
    return m_instance[index];
  }  
  enum {
    N_UBLKS = N_VOXELS / N_VOXELS_8
  };
private:
  uINT8 m_instance[N_UBLKS];
};

template<>
class __PACKED__ tSPLIT_TAGGED_INSTANCE<N_VOXELS_8> {
public:
  __HOST__ VOID set_lgi_instance(uINT8 i) {
    m_instance_in_lgi = i;
  }
  __HOST__ VOID set(uINT8 i) {
    m_instance = i;
  }  
  __HOST__DEVICE__ uINT8 get_lgi_instance([[maybe_unused]] asINT32 index = 0) {
    return m_instance_in_lgi;
  }
  __HOST__DEVICE__ uINT8 get([[maybe_unused]] asINT32 index = 0) {
    return m_instance;
  }
private:
  uINT8  m_instance_in_lgi;
  uINT8  m_instance;  
};

#if BUILD_GPU
VOID init_mblk_split_data(const std::array<sUBLK*,N_VOXELS_8>& ublks_to_pack,
                              int n_ublks_to_pack,
                              sHMBLK& mega);
#endif
#endif
