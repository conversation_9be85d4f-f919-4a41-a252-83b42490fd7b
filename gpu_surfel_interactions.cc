/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "gpu_surfel_interactions.h"
#include "surfel.h"
#include "gpu_host_include.h"
#include <cstdlib>
#include PHYSICS_H

std::vector<MBLK_VOXEL_MASK> g_mblk_adjust_v2s_dist_masks;

/*=================================================================================================
 *
 *================================================================================================*/
MSFL_S2V_BATCH_DATA* MSFL_S2V_BATCH_DATA::get_instance() {
  static MSFL_S2V_BATCH_DATA data;
  return &data;
}

VOID MSFL_S2V_BATCH_DATA::add_group_h_s2v_data(const MSFL_S2V_BATCH_DATA::KEY& group_signature,
                                               S2V_PER_GROUP_BATCH_INFO* h) {
  const auto [it,success] = h_s2v_per_group_batch_info.emplace(group_signature, h);
  assert(success);
}

VOID MSFL_S2V_BATCH_DATA::add_group_d_s2v_data(const MSFL_S2V_BATCH_DATA::KEY& group_signature,
                                               GPU::Ptr<S2V_PER_GROUP_BATCH_INFO> d) {
  const auto [it,success] = d_s2v_per_group_batch_info.emplace(group_signature, d);
  assert(success);
}

const S2V_PER_GROUP_BATCH_INFO* MSFL_S2V_BATCH_DATA::get_group_h_s2v_data(const MSFL_S2V_BATCH_DATA::KEY& group_signature) const {
  return h_s2v_per_group_batch_info.at(group_signature);
}

GPU::Ptr<S2V_PER_GROUP_BATCH_INFO> MSFL_S2V_BATCH_DATA::get_group_d_s2v_data(const MSFL_S2V_BATCH_DATA::KEY& group_signature) const {
  return d_s2v_per_group_batch_info.at(group_signature);
}

/*=================================================================================================
 * @fcn compare_interactions
 * Comparator for sorting MSFL interactios
 *================================================================================================*/
bool compare_interactions(const sMSFL_INTERACTION& i1, const sMSFL_INTERACTION& i2) {

  //If one of the interactions if PDE2, the non-PDE2 interaction is inferior for the sort
  //if both are PDE2, then the usual sort rules apply
  if (i1.is_pde_2_interaction() + i2.is_pde_2_interaction() == 1) {
    return i2.is_pde_2_interaction();
  }
  
  if (i1.m_mblk_index  < i2.m_mblk_index) { return true; }
  if (i1.m_mblk_index  > i2.m_mblk_index) { return false; }

  if (i1.m_latvec < i2.m_latvec) { return true; }
  if (i1.m_latvec > i2.m_latvec) { return false; }
  
  if (i1.m_voxel < i2.m_voxel) { return true; }
  if (i1.m_voxel > i2.m_voxel) { return false; }

  if (i1.phase_mask() > i2.phase_mask()) { return true; }
  if (i1.phase_mask() < i2.phase_mask()) { return false; }
 
  if (i1.m_dst_sfl_index < i2.m_dst_sfl_index) { return true; }
  if (i1.m_dst_sfl_index > i2.m_dst_sfl_index) { return false; }
  
  return false;  
}

bool compare_interactions_warp(const sMSFL_INTERACTION& i1, const sMSFL_INTERACTION& i2) {
  
  if (i1.m_mblk_index  < i2.m_mblk_index) { return true; }
  if (i1.m_mblk_index  > i2.m_mblk_index) { return false; }

  if (i1.m_latvec < i2.m_latvec) { return true; }
  if (i1.m_latvec > i2.m_latvec) { return false; }
 
  if (i1.m_dst_sfl_index < i2.m_dst_sfl_index) { return true; }
  if (i1.m_dst_sfl_index > i2.m_dst_sfl_index) { return false; }
  
  return false;  
}


template<typename INTERACTION_TYPE>
__inline__
bool is_next_neighbor_same_color(INTERACTION_TYPE* tbi,//thread block interactions
				 int thread_id,
				 int max_thread_id) {
  
  return ((thread_id > -1) && (thread_id + 1 <= max_thread_id))?
    (tbi[thread_id].m_dst_sfl_index == tbi[thread_id + 1].m_dst_sfl_index) &&
    (state_latvec_pair(tbi[thread_id].m_latvec) ==
     state_latvec_pair(tbi[thread_id + 1].m_latvec)) :
    false;
}

template<typename INTERACTION_TYPE>
void mark_v2s_or_s2s_lp_peers_for_thread_block(std::vector<INTERACTION_TYPE>& device_interactions,
					       uINT8* lp_peers,
					       int thread_block_start_index,
					       int thread_block_end_index) {
  
  const int max_thread_id = (thread_block_end_index -1) & (N_SFLS_PER_MSFL - 1);
  INTERACTION_TYPE* thread_block_interactions = &device_interactions[0] + thread_block_start_index;
  
  for (int index = thread_block_start_index; index < thread_block_end_index; index++) {

    int thread_id = index & (N_SFLS_PER_MSFL - 1);    
    uINT8 n_peers = 0;
    int peer_index = thread_id - 1;
    
    if(!is_next_neighbor_same_color(thread_block_interactions, peer_index, max_thread_id)) {
      peer_index++; n_peers++;
      while(is_next_neighbor_same_color(thread_block_interactions, peer_index, max_thread_id)) {
    	peer_index++; n_peers++;
      }
    }

    lp_peers[index] = n_peers;
  }  
}

template<typename INTERACTION_TYPE>
void mark_v2s_or_s2s_peers_for_thread_block(std::vector<INTERACTION_TYPE>& device_interactions,
					    uINT64* peers,
					    int thread_block_start_index,
					    int thread_block_end_index) {
  
  const int max_thread_id = (thread_block_end_index -1) & (N_SFLS_PER_MSFL - 1);
  INTERACTION_TYPE* thread_block_interactions = &device_interactions[0] + thread_block_start_index;
  uINT64 warp_reduction_masks[N_SFLS_PER_MSFL] = {0};
    
  for (int index = thread_block_start_index; index < thread_block_end_index; index++) {

    int thread_id = index & (N_SFLS_PER_MSFL - 1);
    int dst_sfl_index = thread_block_interactions[thread_id].m_dst_sfl_index;
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE    
    if (!thread_block_interactions[thread_id].is_pde_2_interaction())
#endif
      {
        warp_reduction_masks[dst_sfl_index] |= (uINT64(0x1) << thread_id);
      }
  }

  for (int index = thread_block_start_index; index < thread_block_end_index; index++) {
    int thread_id = index & (N_SFLS_PER_MSFL - 1);
    int dst_sfl_index = thread_block_interactions[thread_id].m_dst_sfl_index;
    peers[index] = warp_reduction_masks[dst_sfl_index];
  }
}

void mark_peers_for_thread_block(std::vector<sMSFL_INTERACTION>& device_interactions,
				 uINT8* v2s_lp_peers,
				 uINT64* v2s_peers,
				 int thread_block_start_index,
				 int thread_block_end_index) {

  mark_v2s_or_s2s_peers_for_thread_block(device_interactions, v2s_peers,
					 thread_block_start_index, thread_block_end_index);

  mark_v2s_or_s2s_lp_peers_for_thread_block(device_interactions, v2s_lp_peers,
					    thread_block_start_index, thread_block_end_index);  

}

/*=================================================================================================
 * @fcn mark_v2s_peers
 * This function scans adjacent interactions and assigns a master thread for reduction purposes.
 * A non-zero number of peers indicates that a thread is a reducing thread
 *================================================================================================*/
void mark_v2s_peers(std::vector<sMSFL_INTERACTION>& interactions,
		    unsigned num_speed1_interactions,
		    uINT8* v2s_lp_peers,
		    uINT64* v2s_peers) {
  
  for (int thread_block_start_index = 0;
       thread_block_start_index < num_speed1_interactions;
       thread_block_start_index += N_SFLS_PER_MSFL) {

    int thread_block_end_index = thread_block_start_index + N_SFLS_PER_MSFL;
    thread_block_end_index = (thread_block_end_index < num_speed1_interactions)?
      thread_block_end_index : num_speed1_interactions;

    mark_peers_for_thread_block(interactions,
				v2s_lp_peers,
				v2s_peers,
				thread_block_start_index,
				thread_block_end_index);
  }
}

/*=================================================================================================
 * @fcn mark_start_end_indices
 *================================================================================================*/
void mark_start_end_indices(const std::vector<sMSFL_INTERACTION>& interactions,
			    unsigned num_speed1_interactions,
			    std::vector<signed>& unique_ublk_start_indices,
			    std::vector<signed>& unique_ublk_end_indices,
			    std::vector<signed>& pde2_ublk_start_indices) {

  size_t num_interactions = interactions.size();
  size_t num_unique_ublks = unique_ublk_start_indices.size();

  // SPEED 1
  for (int i = 0; i < num_speed1_interactions; i++) {
    const sMSFL_INTERACTION& msi = interactions[i];
    if (unique_ublk_start_indices[msi.m_mblk_index] == -1) {
      unique_ublk_start_indices[msi.m_mblk_index] = i;
    }
    unique_ublk_end_indices[msi.m_mblk_index] = i + 1; //Plus one to make it exclusive
  }

  // SPEED 2
  for (int i = num_speed1_interactions; i < num_interactions; i++) {
    const sMSFL_INTERACTION& msi = interactions[i];
    if (pde2_ublk_start_indices[msi.m_mblk_index] == -1) {
      pde2_ublk_start_indices[msi.m_mblk_index] = i;
    }
  }

  pde2_ublk_start_indices[num_unique_ublks] = num_interactions;
  
  // Some UBKLs might potentially not have speed 1 or 2 interactions
  // Mark these indicies appropriately
  for (int i = num_unique_ublks - 1; i >= 0;  i--) {
    if (unique_ublk_start_indices[i] == -1) {
      unique_ublk_start_indices[i] = unique_ublk_end_indices[i] =
        i > 0? (unique_ublk_end_indices[i - 1]) : 0;
    }
    if (pde2_ublk_start_indices[i] == -1) {
      pde2_ublk_start_indices[i] = pde2_ublk_start_indices[i + 1];
    }
  }

  //Verify monotonic sequence
  for (int i = 0; i < num_unique_ublks; i++) {
    assert(pde2_ublk_start_indices[i] != -1);
    assert(pde2_ublk_start_indices[i] <= pde2_ublk_start_indices[i + 1]);
  }
}

using THREAD_ID_ARRAY = std::array<std::vector<unsigned>, N_SFLS_PER_MSFL>;

template<typename INTERACTION_TYPE>
void bubble_element(std::vector<INTERACTION_TYPE>& e,
		    int target_index,
		    int current_index,
		    std::array<THREAD_ID_ARRAY, N_LATTICE_VECTORS_D25/2>& counts) {
  
  assert(current_index > target_index);

  while(current_index > target_index) {

    //First we increment all thread counts associated with
    //previous element to reflect its new position
    int latvec = e[current_index - 1].m_latvec;
    int lp = state_latvec_pair(latvec);
    int dst_sfl_index = e[current_index - 1].m_dst_sfl_index;
    auto& srcs = counts[lp][dst_sfl_index];
    for (auto& thread_id : srcs) {
      thread_id++;
      std::swap(e[current_index], e[current_index - 1]);
      current_index--;      
    }    
  }  
}

/*=================================================================================================
 * @fcn group_peer_dst_sfl_interactions
 * This function scans interactions handled by a thread block and places v2s / s2s interactions that
 * write to the same destination surfel latvec pair adjacent to each other. In other words they
 * are peers for reduction.
 *================================================================================================*/
template<typename INTERACTION_TYPE>
void group_peer_dst_sfl_interactions(std::vector<INTERACTION_TYPE>& e,
				     size_t start_index,
				     size_t end_index) {

  
  std::array<THREAD_ID_ARRAY, N_LATTICE_VECTORS_D25/2> counts;
  
  for (size_t index = start_index; index < end_index; index++) {
    size_t thread_id = index & 63;
    size_t warp_id  = thread_id >> 5;
    auto interaction = e[index];
    int latvec = interaction.m_latvec;
    int lp = state_latvec_pair(latvec);
    int dst_sfl_index = interaction.m_dst_sfl_index;
    auto& srcs = counts[lp][dst_sfl_index];

    if (!srcs.empty()) {
      size_t last_thread_id = srcs.back();
      if(thread_id != (last_thread_id + 1)) {
	bubble_element(e, start_index + last_thread_id + 1,
		       start_index + thread_id, counts);
      }
      thread_id = last_thread_id + 1;
    }
    srcs.push_back(thread_id);
  }
}

VOID init_msfl_interactions(sMSFL* mega_surfel,
                            std::array<sSURFEL*,N_SFLS_PER_MSFL>& sfls_to_pack,
                            size_t n_sfls_packed,
                            const std::vector<sHMBLK*>& mblks,
                            const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                            std::vector<sMSFL_INTERACTION>& interactions,
                            std::vector<sHMBLK*>& unique_ublks) {

  std::unordered_map<STP_SHOB_ID, unsigned> unique_ublk_interaction_index_map;  

  for (int s = 0; s < n_sfls_packed; s++) {
    sSURFEL* surfel = sfls_to_pack[s];
    SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
    asINT32 n_ublks                          = surfel->m_n_ublk_interactions;
    asINT32 n_ublks_pde_only                 = surfel->m_n_interacting_ublks_pde_only;

    for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
      STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();
      UBLK ublk                  = ublk_interaction->ublk();
      uINT8 *voxel_info          = ublk_interaction->voxel_info();
      auINT32 n_weight_sets      = ublk_interaction->m_n_weight_sets;
      uINT32  ublk_scale_diff    = surfel->scale() - ublk->scale();

      auto [mblk_id, ublk_index] = child_ublk_to_mblk_map.at(ublk->id());
      unsigned unique_ublk_interaction_index;

      if(unique_ublk_interaction_index_map.find(mblk_id) !=
         unique_ublk_interaction_index_map.end()) {
        unique_ublk_interaction_index = unique_ublk_interaction_index_map.at(mblk_id);	
      } else {
        unique_ublk_interaction_index = unique_ublk_interaction_index_map.size();
        unique_ublk_interaction_index_map[mblk_id] = unique_ublk_interaction_index;
        unique_ublks.push_back(mblks[mblk_id]);
      }

      assert(unique_ublk_interaction_index < std::numeric_limits<decltype(sMSFL_INTERACTION::m_mblk_index)>::max());

      //Set scale diff clear bit for v2s
      mblks[mblk_id]->surf_geom_data()->s2v_clear_data.set_clear_bit_for_scale_diff(ublk_index, ublk_scale_diff);
       
      ccDOTIMES (iv, n_weight_sets) {
        sSURFEL_VOXEL_INTERACTION s_v_interaction;
        voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
        asINT32 n_voxel_weights = s_v_interaction.n_voxel_weights();
        asINT32 phase_mask = s_v_interaction.phase_mask();
        asINT32 voxel      = s_v_interaction.voxel_id();      
        asINT32 scale_diff = s_v_interaction.scale_diff();
        asINT32 n_weights_pde_only = s_v_interaction.n_voxel_weights_pde_only();
        asINT32 mega_voxel = voxel + ublk_index * sUBLK::N_VOXELS;

        if (n_voxel_weights > n_weights_pde_only) {
          if ((!surfel->is_wall() || !surfel->is_not_free_slip_wall()) &&
              !surfel->is_lrf()) {
            g_mblk_adjust_v2s_dist_masks[mblk_id].set(mega_voxel);
          }
        }
        
        ccDOTIMES (iw, n_voxel_weights) {
          STP_LATVEC_INDEX latvec = *voxel_info++;
          STP_SURFEL_WEIGHT weight = *weights++;

          auto msfl_interaction = sMSFL_INTERACTION(latvec, phase_mask, scale_diff,
                                                          unique_ublk_interaction_index,
                                                          mega_voxel, s, weight);
          interactions.push_back(msfl_interaction);
        }
      }
    }//interaction-loop

    surfel->clear_ublk_interactions();
  } //surfel-loop
}

VOID write_interactions_to_file(std::ofstream& ofs,
				sMSFL* msfl,
				std::vector<sMSFL_INTERACTION>& interactions,
				std::vector<sHMBLK*> unique_ublks,
				const std::string& msg ) {

  ofs << "===================================================================\n";
  ofs << msg << ", MSFL:: " << msfl->id() << "\n";
  int count = 0;
  for (auto interaction : interactions) {
    ofs << count << ". ";
    ofs << "(U:" << unique_ublks[interaction.m_mblk_index]->id()
	<< ",P:" << int(interaction.phase_mask())
	<< ",L:" << int(interaction.m_latvec)
	<< ",V:" << int(interaction.m_voxel)
	<< ",SFL:" << int(interaction.m_dst_sfl_index)
	<< ", W:" << interaction.m_weight
        << ")\n";
    count += 1;
  }
  ofs << "\n";
}

/*=================================================================================================
 * @fcn init_msfl_interactions
 * Initializes interactions for host MSFLs
 *================================================================================================*/
VOID init_msfl_interactions(sMSFL* msfl,
                            std::array<sSURFEL*,N_SFLS_PER_MSFL>& sfls_to_pack,
                            size_t n_sfls_packed,
                            const std::vector<sHMBLK*>& mblks,
                            const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) {

  std::vector<sMSFL_INTERACTION> interactions;
  std::vector<sHMBLK*> unique_ublks;
  init_msfl_interactions(msfl, sfls_to_pack,
                         n_sfls_packed, mblks,
                         child_ublk_to_mblk_map,
                         interactions,
                         unique_ublks);

#ifdef DEBUG_MEGASURFEL_INTERACTIONS_INIT
  if (msfl->id() == 199) {
    std::ofstream ofs("interactions.txt", std::ofstream::out);        
    write_interactions_to_file(ofs, msfl, interactions, unique_ublks,  "Before sorting");
    ofs.close();
  }
#endif

  unsigned num_unique_ublks = unique_ublks.size();
  unsigned num_interactions = interactions.size();

  std::sort(interactions.begin(),
	    interactions.end(),
	    compare_interactions);

  unsigned num_speed1_interactions = 0;

  for(auto& interaction : interactions) {
    if (interaction.is_pde_2_interaction()) {
      break;
    }
    num_speed1_interactions++;
  }

  //Only prepare v2s masks for SPEED1 interactions
  for(size_t warp_sort_index = 0;
      warp_sort_index < num_speed1_interactions;
      warp_sort_index += N_SFLS_PER_MSFL) {

    size_t warp_sort_index_end = warp_sort_index + N_SFLS_PER_MSFL;
    warp_sort_index_end = (warp_sort_index_end < num_speed1_interactions)? warp_sort_index_end : num_speed1_interactions;
	
    std::sort(interactions.begin() + warp_sort_index,
	      interactions.begin() + warp_sort_index_end,
	      compare_interactions_warp);
	
    group_peer_dst_sfl_interactions(interactions, warp_sort_index, warp_sort_index_end);
  }

#ifdef DEBUG_MEGASURFEL_INTERACTIONS_INIT
  if (msfl->id() == 199) {
    std::ofstream ofs("interactions.txt", std::ios_base::app);    
    write_interactions_to_file(ofs, msfl, interactions, unique_ublks,  "After sorting");
    ofs.close();
  }
#endif  

  unsigned n_component_elements[sMSFL_INTERACTIONS::N_COMPONENTS] = {
    num_unique_ublks, //unique_ublks
    num_unique_ublks, //start_indices
    num_unique_ublks, //end_indices
    num_unique_ublks + 1, //pde2_indices
    num_interactions,//sMSFL_INTERACTION
    GPU::g_use_repeatable_reduction? num_speed1_interactions : 0,//v2s_lp_peers
    GPU::g_use_repeatable_reduction? num_speed1_interactions : 0,//v2s_peers
    GPU::g_use_repeatable_reduction? num_interactions : 0,//s2v_lp_mask
    GPU::g_use_repeatable_reduction? num_interactions : 0//s2v_mask
  };
  
  size_t n_bytes = sMSFL_INTERACTIONS::size(n_component_elements);
  int ierr = posix_memalign((void**) &msfl->m_interactions, GPU::MINIMUM_CUDA_ALIGNMENT, n_bytes);
  memset(msfl->m_interactions, 0, n_bytes);

  msfl->m_interactions->set_num_speed1_interactions(num_speed1_interactions);
  msfl->m_interactions->set_n_component_elements(n_component_elements);
  
  memcpy(msfl->m_interactions->get<MSFL_INT_COMPONENTS::INTERACTIONS>(),
	 &interactions[0], sizeof(sMSFL_INTERACTION) * num_interactions);

  memcpy(msfl->m_interactions->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>(),
	 &unique_ublks[0], sizeof(sHMBLK*) * num_unique_ublks);

  if (GPU::g_use_repeatable_reduction) {
    mark_v2s_peers(interactions, num_speed1_interactions,
                   msfl->m_interactions->get<MSFL_INT_COMPONENTS::V2S_LP_PEERS>(),
                   msfl->m_interactions->get<MSFL_INT_COMPONENTS::V2S_PEERS>());
  }

  std::vector<signed> unique_ublk_start_indices(unique_ublks.size(), -1);
  std::vector<signed> unique_ublk_end_indices(unique_ublks.size(), -1);
  std::vector<signed> pde2_ublk_start_indices(unique_ublks.size() + 1, -1);

  mark_start_end_indices(interactions, num_speed1_interactions,
			 unique_ublk_start_indices,
			 unique_ublk_end_indices,
			 pde2_ublk_start_indices);

  memcpy(msfl->m_interactions->get<MSFL_INT_COMPONENTS::START_INDICES>(),
	 &unique_ublk_start_indices[0],
	 sizeof(unsigned) * num_unique_ublks);

  memcpy(msfl->m_interactions->get<MSFL_INT_COMPONENTS::END_INDICES>(),
	 &unique_ublk_end_indices[0],
	 sizeof(unsigned) * num_unique_ublks);

  memcpy(msfl->m_interactions->get<MSFL_INT_COMPONENTS::PDE2_INDICES>(),
	 &pde2_ublk_start_indices[0],
	 sizeof(unsigned) * (num_unique_ublks + 1));  
}

/*===================================================================================
 *
 *==================================================================================*/
void mark_s2v_peers(sHMBLK* mblk,
		    sMSFL_INTERACTIONS* msfl_interactions,
		    unsigned thread_block_start_index,
		    unsigned thread_block_end_index) {

  auto interactions = msfl_interactions->get<MSFL_INT_COMPONENTS::INTERACTIONS>();
  constexpr auto N_VOXELS_PER_MBLK = sHMBLK::N_VOXELS;
  uINT64 lv_interactions[N_LATTICE_VECTORS_D25][N_VOXELS_PER_MBLK];
  uINT64 v_interactions[N_VOXELS_PER_MBLK];
  memset(lv_interactions, 0, sizeof(lv_interactions));
  memset(v_interactions, 0, sizeof(v_interactions));
  
  for (unsigned index = thread_block_start_index, threadId = 0;
       index < thread_block_end_index; index++, threadId++) {
    auto interaction = interactions[index];
    uINT8 latvec = interaction.m_latvec;
    uINT8 voxel  = interaction.m_voxel;
    auto ublk_index = interaction.m_mblk_index;
    sHMBLK* src_ublk = (sHMBLK*) msfl_interactions->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>()[ublk_index];
    if (src_ublk == mblk) {
      lv_interactions[latvec][voxel] |= ((uINT64) 0x1) << threadId;
      //PDE2 interactions don't participate in surfel to voxel reduction where no LP is involved
      if (!interaction.is_pde_2_interaction()) {
	v_interactions[voxel] |= ((uINT64) 0x1) << threadId;
      }
    }
  }

  for (unsigned l = 0; l < N_LATTICE_VECTORS_D25; l++) {
    for (unsigned v = 0; v < N_VOXELS_PER_MBLK; v++) {
      auto peer_mask = lv_interactions[l][v];
      unsigned thread = 0;
      while(peer_mask) {
	if (peer_mask & (uINT64) 0x1) {
	  assert(thread + thread_block_start_index < msfl_interactions->get_n_elements<MSFL_INT_COMPONENTS::INTERACTIONS>());
	  msfl_interactions->get<MSFL_INT_COMPONENTS::S2V_LP_MASKS>()[thread + thread_block_start_index]
	    = lv_interactions[l][v];
	}
	peer_mask >>= 1;
	thread++;
      }
    }
  }

  for (unsigned v = 0; v < N_VOXELS_PER_MBLK; v++) {
    auto peer_mask = v_interactions[v];
    unsigned thread = 0;
    while(peer_mask) {
      if (peer_mask & (uINT64) 0x1) {
	assert(thread + thread_block_start_index < msfl_interactions->get_n_elements<MSFL_INT_COMPONENTS::INTERACTIONS>());
	msfl_interactions->get<MSFL_INT_COMPONENTS::S2V_MASKS>()[thread + thread_block_start_index]
	  = v_interactions[v];
      }
      peer_mask >>= 1;
      thread++;
    }
  }  
}

static
void prepare_s2v_bitmasks_on_host(sHMBLK* mblk,
				  sMSFL** h_msfls_in_batch,
				  uINT16 num_interacting_msfls,
				  unsigned* interacting_msfl_batch_indices) {

  for (int s = 0; s < num_interacting_msfls; s++) {
    unsigned interacting_sfl_index = interacting_msfl_batch_indices[s];
    sMSFL* msfl = h_msfls_in_batch[interacting_sfl_index];
    auto interactions = msfl->m_interactions;

    for (int speed = 0; speed < 2; speed++) {
      unsigned interaction_start_index = 0;
      unsigned interaction_end_index = 0;    
      for (unsigned i = 0; i < interactions->get_n_elements<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>(); i++) {
	if ((sHMBLK*) interactions->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>()[i] == mblk) {
	  if (speed == 0) {
	    interaction_start_index = interactions->get<MSFL_INT_COMPONENTS::START_INDICES>()[i];
	    interaction_end_index = interactions->get<MSFL_INT_COMPONENTS::END_INDICES>()[i];
	  } else {
	    interaction_start_index = interactions->get<MSFL_INT_COMPONENTS::PDE2_INDICES>()[i];
	    interaction_end_index = interactions->get<MSFL_INT_COMPONENTS::PDE2_INDICES>()[i + 1];	    
	  }
	  break;
	}
      }
    
      assert(interaction_end_index - interaction_start_index >= 0);

      if (GPU::g_use_repeatable_reduction) {
        for (unsigned thread_block_start_index = interaction_start_index;
             thread_block_start_index < interaction_end_index;
             thread_block_start_index += N_SFLS_PER_MSFL) {
      
          unsigned thread_block_end_index = thread_block_start_index + N_SFLS_PER_MSFL;
          thread_block_end_index = (thread_block_end_index < interaction_end_index)?
            thread_block_end_index : interaction_end_index;
          mark_s2v_peers(mblk, interactions, thread_block_start_index, thread_block_end_index);
        }
      }
    } // speed - loop
  } // sfl - loop
}
static void prepare_s2v_bitmasks_on_host(S2V_PER_GROUP_BATCH_INFO *s2v_info,
                                         sMSFL ** batch_msfls,
                                         size_t num_sfls,
                                         size_t batch_size) {

  unsigned num_batches = s2v_info->get_n_elements<S2V_COMPONENTS::N_UNIQUE_MBLKS_PER_BATCH>();
  unsigned mblk_batch_offset = 0;
  sHMBLK **unique_batch_ublks = s2v_info->get<S2V_COMPONENTS::UNIQUE_UBLKS>();

  for (unsigned batch = 0; batch < num_batches; batch++)
  {

    unsigned batch_start_index = batch * batch_size;
    unsigned batch_end_index = (batch + 1) * batch_size;
    // trim
    batch_end_index = (batch_end_index < num_sfls) ? batch_end_index : num_sfls;

    unsigned num_unique_ublks_per_batch = s2v_info->get<S2V_COMPONENTS::N_UNIQUE_MBLKS_PER_BATCH>()[batch];

    sMSFL **msfls_in_batch = batch_msfls + batch_start_index;

    for (unsigned i = 0; i < num_unique_ublks_per_batch; i++)
    {

      sHMBLK *mblk = unique_batch_ublks[i + mblk_batch_offset];

      uINT16 num_interacting_msfls =
          s2v_info->get<S2V_COMPONENTS::N_INT_MSFLS>()[i + mblk_batch_offset];

      unsigned interacting_msfl_start_index =
          s2v_info->get<S2V_COMPONENTS::INT_MSFLS_START_INDEX>()[i + mblk_batch_offset];

      unsigned *interacting_msfl_batch_indices = s2v_info->get<S2V_COMPONENTS::INT_MSFL_BATCH_INDICES>() +
                                                 interacting_msfl_start_index;

      prepare_s2v_bitmasks_on_host(mblk, msfls_in_batch,
                                   num_interacting_msfls,
                                   interacting_msfl_batch_indices);
    }

    mblk_batch_offset += num_unique_ublks_per_batch;
  }
}

VOID S2V_PER_GROUP_BATCH_INFO::init(const unsigned (&n_component_elements) [(int) S2V_COMPONENTS::N_COMPONENTS],
				    const std::vector<const sHMBLK*>& unique_s2v_ublks_of_scale,
				    const std::vector<unsigned>& interacting_msfl_batch_indices,
				    const std::vector<uINT16>& num_interacting_msfls,
				    const std::vector<unsigned>& interacting_msfl_start_index,
				    const std::vector<unsigned>& unique_s2v_ublks_per_batch) {

  this->set_n_component_elements(n_component_elements);
    
  memcpy(this->get<S2V_COMPONENTS::UNIQUE_UBLKS>(),
	 &unique_s2v_ublks_of_scale[0],
	 n_component_elements[(int) S2V_COMPONENTS::UNIQUE_UBLKS] * sizeof(sMBLK*));

  memcpy(this->get<S2V_COMPONENTS::INT_MSFL_BATCH_INDICES>(),
	 &interacting_msfl_batch_indices[0],
	 n_component_elements[(int) S2V_COMPONENTS::INT_MSFL_BATCH_INDICES] * sizeof(unsigned));

  memcpy(this->get<S2V_COMPONENTS::N_INT_MSFLS>(),
	 &num_interacting_msfls[0],
	 n_component_elements[(int) S2V_COMPONENTS::N_INT_MSFLS] * sizeof(uINT16));

  memcpy(this->get<S2V_COMPONENTS::INT_MSFLS_START_INDEX>(),
	 &interacting_msfl_start_index[0],
	 n_component_elements[(int) S2V_COMPONENTS::INT_MSFLS_START_INDEX] * sizeof(unsigned));

  memcpy(this->get<S2V_COMPONENTS::N_UNIQUE_MBLKS_PER_BATCH>(),
	 &unique_s2v_ublks_per_batch[0],
	 n_component_elements[(int) S2V_COMPONENTS::N_UNIQUE_MBLKS_PER_BATCH] * sizeof(unsigned));
}

VOID prepare_s2v_info(sMSFL** batch_msfls,
		      unsigned batch_start_index,
		      unsigned batch_end_index,
		      std::vector<const sHMBLK*>& unique_s2v_ublks_of_scale,
		      std::vector<unsigned>& interacting_msfl_batch_indices,
		      std::vector<uINT16>& num_interacting_msfls,
		      std::vector<unsigned>& interacting_msfl_start_index,
		      std::vector<unsigned>& unique_s2v_ublks_per_batch) {
  
  std::unordered_map<const sHMBLK*, std::vector<unsigned>> unique_ublk_indicies_map;  
  
  for (unsigned index = batch_start_index, local_index = 0;
       index < batch_end_index;
       index++, local_index++) {
    
    const sMSFL* msfl = batch_msfls[index];
    unsigned num_unique_ublks = msfl->m_interactions->get_n_elements<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>();
    
    for (unsigned u = 0; u < num_unique_ublks; u++) {
      
      auto mblk = (const sHMBLK*) msfl->m_interactions->get<MSFL_INT_COMPONENTS::UNIQUE_UBLKS>()[u];

      unique_ublk_indicies_map[mblk].push_back(local_index);
    }
  }

  unsigned batch_interacting_msfl_start_index = interacting_msfl_batch_indices.size();
  
  for (const auto& e : unique_ublk_indicies_map) {
    unique_s2v_ublks_of_scale.push_back(e.first);
    const std::vector<unsigned>& unique_ublk_interacting_sfl_indices = e.second;
    
    for (unsigned interacting_sfl_index : unique_ublk_interacting_sfl_indices) {
      interacting_msfl_batch_indices.push_back(interacting_sfl_index);      
    }

    cassert(unique_ublk_interacting_sfl_indices.size() < std::numeric_limits<uINT16>::max());
    num_interacting_msfls.push_back(unique_ublk_interacting_sfl_indices.size());

    cassert(batch_interacting_msfl_start_index < std::numeric_limits<unsigned>::max());
    interacting_msfl_start_index.push_back(batch_interacting_msfl_start_index);
    batch_interacting_msfl_start_index = interacting_msfl_batch_indices.size();
  }

  unique_s2v_ublks_per_batch.push_back(unique_ublk_indicies_map.size());  
}

/*===================================================================================
 *
 *==================================================================================*/
S2V_PER_GROUP_BATCH_INFO* build_s2v_data_for_msfl_batch(sMSFL** batch_msfls,
                                                        size_t num_msfls_this_batch,
                                                        size_t batch_size) {

  std::vector<const sHMBLK*> unique_s2v_ublks_of_scale;
  std::vector<unsigned> interacting_msfl_batch_indices;
  std::vector<uINT16> num_interacting_msfls;
  std::vector<unsigned> interacting_msfl_start_index;
  std::vector<unsigned> unique_s2v_ublks_per_batch;

  unsigned num_batches = ceil((float) num_msfls_this_batch / batch_size);

  for (unsigned batch = 0; batch < num_batches; batch++) {
    unsigned batch_start_index = batch * batch_size;
    unsigned batch_end_index   = (batch + 1) * batch_size;
    //trim
    batch_end_index = (batch_end_index < num_msfls_this_batch)? batch_end_index : num_msfls_this_batch;

    prepare_s2v_info(batch_msfls,
                     batch_start_index,
                     batch_end_index,
                     unique_s2v_ublks_of_scale,
                     interacting_msfl_batch_indices,
                     num_interacting_msfls,
                     interacting_msfl_start_index,
                     unique_s2v_ublks_per_batch);
  }

  unsigned num_unique_ublks_for_scale = unique_s2v_ublks_of_scale.size();
  unsigned num_total_interacting_sfl_indices = interacting_msfl_batch_indices.size();
  assert(num_batches == unique_s2v_ublks_per_batch.size());

  unsigned n_component_elements[S2V_PER_GROUP_BATCH_INFO::N_COMPONENTS] = {
    num_unique_ublks_for_scale, //unique_ublks
    num_total_interacting_sfl_indices, //interacting_msfl_indices
    num_unique_ublks_for_scale, //n_interacting_msfls
    num_unique_ublks_for_scale, //interacting_msfl_start_index
    num_batches //n_unique_ublks_per_batch
  };

  unsigned bytes = S2V_PER_GROUP_BATCH_INFO::size(n_component_elements);

  auto h_s2v_info_per_scale = static_cast<S2V_PER_GROUP_BATCH_INFO*>(malloc(bytes));
  memset(h_s2v_info_per_scale, 0, bytes);

  h_s2v_info_per_scale->init(n_component_elements,
                             unique_s2v_ublks_of_scale,
                             interacting_msfl_batch_indices,
                             num_interacting_msfls,
                             interacting_msfl_start_index,
                             unique_s2v_ublks_per_batch);

  prepare_s2v_bitmasks_on_host(h_s2v_info_per_scale, batch_msfls,
                               num_msfls_this_batch, batch_size);

  return h_s2v_info_per_scale;
}

VOID build_s2v_data_for_msfl_fset(SURFEL_GROUP_TYPE group_type,
                                  MSFL_FSET  msfl_group_fset,
                                  size_t batch_size) {

  size_t num_msfls_of_group_type = 0;
  for (int scale = 0; scale < sim.num_scales; scale++) {
    const auto& set_of_msfl_groups = msfl_group_fset->get_groups_of_scale(scale);
    size_t num_msfls_of_scale = 0;

    for (auto msfl_group : set_of_msfl_groups) {
      num_msfls_of_scale += msfl_group->n_shob_ptrs();
    }
    
    std::vector<sMSFL*> batch_msfls(num_msfls_of_scale, nullptr);
    size_t count = 0;
    for (auto msfl_group : set_of_msfl_groups) {
        sMSFL* h_msfl = msfl_group->shob_ptrs();
        while(h_msfl) {
          batch_msfls[count] = h_msfl;
          h_msfl = h_msfl->m_next;
          count++;
        }

        if (group_type != MLRF_SURFEL_GROUP_TYPE) {
        
        GPU::MSFL_GROUP_KEY key = {group_type, scale, msfl_group->m_dest_sp.nsp()};
        const auto range = GPU::get_surfel_group_range(group_type, scale, (int)msfl_group->m_dest_sp.nsp());
        //GPU::set_group_range_for_scale(range, scale, (int)msfl_group->m_dest_sp.nsp());

        size_t num_msfls_of_sp = range[1] - range[0];
        size_t range_start_for_scale = range[0] - num_msfls_of_group_type;
        auto s2v_data = build_s2v_data_for_msfl_batch(batch_msfls.data() + range_start_for_scale,
                                                      num_msfls_of_sp,
                                                      batch_size);

        MSFL_S2V_BATCH_DATA::get_instance()->add_group_h_s2v_data(key, s2v_data);
      }
    }

    if (group_type == MLRF_SURFEL_GROUP_TYPE) {
      for (auto mlrf_group : g_mlrf_msfl_fset.get_groups_of_scale(scale)) {        
        GPU::MSFL_GROUP_KEY key = {MLRF_SURFEL_GROUP_TYPE, scale, 0 /*dst sp*/,
                                   mlrf_group->m_even_odd, mlrf_group->m_lrf_physics_desc};
        size_t range[2];
        GPU::set_mlrf_msfl_group_range_for_scale(range, scale,
                                                 mlrf_group->m_even_odd,
                                                 mlrf_group->m_lrf_physics_desc);
        size_t num_msfls_of_scale_and_even_odd = range[1] - range[0];
         if(!num_msfls_of_scale_and_even_odd) {
          msg_internal_error("Attempt to create auxillary S2V data for an empty sliding mesh group, "
                             "scale %d, even_odd %d", scale, mlrf_group->m_even_odd);
        }
        size_t range_start_for_scale = range[0] - num_msfls_of_group_type;
        auto s2v_data = build_s2v_data_for_msfl_batch(batch_msfls.data() + range_start_for_scale,
                                                      num_msfls_of_scale_and_even_odd,
                                                      batch_size);
        MSFL_S2V_BATCH_DATA::get_instance()->add_group_h_s2v_data(key, s2v_data);
      }
    }

    num_msfls_of_group_type += num_msfls_of_scale;
  } //scale-loop
}

/*=================================================================================================
 * @fcn compare_s2s_interactions
 *================================================================================================*/
bool compare_s2s_interactions(const sMSFL_sMSFL_INTERACTION& i1, const sMSFL_sMSFL_INTERACTION& i2) {
  
  if (i1.m_msfl_index  < i2.m_msfl_index) { return true; }
  if (i1.m_msfl_index > i2.m_msfl_index) { return false; }

  if (i1.m_latvec < i2.m_latvec) { return true; }
  if (i1.m_latvec > i2.m_latvec) { return false; }
  
  if (i1.m_src_sfl_index < i2.m_src_sfl_index) { return true; }
  if (i1.m_src_sfl_index > i2.m_src_sfl_index) { return false; }

  if (i1.even_odd_mask() > i2.even_odd_mask()) { return true; }
  if (i1.even_odd_mask() < i2.even_odd_mask()) { return false; }
 
  if (i1.m_dst_sfl_index < i2.m_dst_sfl_index) { return true; }
  if (i1.m_dst_sfl_index > i2.m_dst_sfl_index) { return false; }
  
  return false;  
}

bool compare_s2s_interactions_warp(const sMSFL_sMSFL_INTERACTION& i1, const sMSFL_sMSFL_INTERACTION& i2) {
  
  if (i1.m_msfl_index  < i2.m_msfl_index) { return true; }
  if (i1.m_msfl_index > i2.m_msfl_index) { return false; }

  if (i1.m_latvec < i2.m_latvec) { return true; }
  if (i1.m_latvec > i2.m_latvec) { return false; }
 
  if (i1.m_dst_sfl_index < i2.m_dst_sfl_index) { return true; }
  if (i1.m_dst_sfl_index > i2.m_dst_sfl_index) { return false; }
  
  return false;  
}

/*=================================================================================================
 * @fcn mark_s2s_peers
 * This function scans adjacent interactions and assigns a master thread for reduction purposes.
 * A non-zero number of peers indicates that a thread is a reducing thread
 *================================================================================================*/
void mark_s2s_peers(std::vector<sMSFL_sMSFL_INTERACTION>& interactions,
                    BOOLEAN allocate_s2s_lp_peers,
		    BOOLEAN allocate_s2s_peers,
		    uINT8* s2s_lp_peers,
		    uINT64* s2s_peers) {

  size_t num_interactions = interactions.size();
  for (int thread_block_start_index = 0;
       thread_block_start_index < num_interactions;
       thread_block_start_index += N_SFLS_PER_MSFL) {

    int thread_block_end_index = thread_block_start_index + N_SFLS_PER_MSFL;
    thread_block_end_index = (thread_block_end_index < num_interactions)?
      thread_block_end_index : num_interactions;

    if (allocate_s2s_lp_peers) {
      mark_v2s_or_s2s_lp_peers_for_thread_block(interactions, s2s_lp_peers,
                                                thread_block_start_index, thread_block_end_index);
    }

    if (allocate_s2s_peers) {
      mark_v2s_or_s2s_peers_for_thread_block(interactions, s2s_peers,
					     thread_block_start_index, thread_block_end_index);
    }
  }
}

VOID init_msfl_s2s_interactions(sMSFL* msfl,
                                const std::array<sSURFEL*,N_SFLS_PER_MSFL>& sfls_to_pack,
                                size_t n_sfls_packed,
                                std::vector<sMSFL*> msfls,
                                const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                std::vector<sMSFL_sMSFL_INTERACTION>& interactions,
                                std::vector<sMSFL*>& unique_sfls) {

  std::unordered_map<STP_SHOB_ID, unsigned> unique_sfl_interaction_index_map;
  
  for (int s = 0; s < n_sfls_packed; s++) {
    sSURFEL* surfel = sfls_to_pack[s];
    
    if (surfel->is_s2s_destination()) {
      uINT8 dst_sfl_index = s;
      S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
      asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;
      SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;

      ccDOTIMES(nth_src_surfel, n_src_surfels) {
	asINT32 n_weights = surfel_interaction->m_n_weights;
	sTAGGED_S2S_SRC_SURFEL tagged_src_surfel = surfel_interaction->m_tagged_src_surfel;
	SURFEL src_surfel = tagged_src_surfel.src_surfel();
        auto [src_msfl_id, src_sfl_index] = child_sfl_to_msfl_map.at(src_surfel->id());
	MSFL src_msfl = msfls[src_msfl_id];
	uINT8 interaction_even_odd_mask = surfel_interaction->m_even_odd_mask;
        BOOLEAN is_lrf_src = tagged_src_surfel.is_lrf_src();
	unsigned unique_sfl_interaction_index;
      
	if(unique_sfl_interaction_index_map.find(src_msfl_id) !=
	   unique_sfl_interaction_index_map.end()) {
	  unique_sfl_interaction_index = unique_sfl_interaction_index_map.at(src_msfl_id);
	} else {
	  unique_sfl_interaction_index = unique_sfl_interaction_index_map.size();
	  unique_sfl_interaction_index_map[src_msfl_id] = unique_sfl_interaction_index;
	  unique_sfls.push_back(src_msfl);
	}

        assert(unique_sfl_interaction_index < std::numeric_limits<uINT16>::max());
        
	STP_SURFEL_WEIGHT *weights = surfel_interaction->weights();
	auINT8            *latvecs = surfel_interaction->latvecs();
	
	ccDOTIMES(nth_weight, n_weights) {
	  STP_SURFEL_WEIGHT weight = *weights++;
	  asINT32 latvec = *latvecs++;                  //inwards to dest surfel
	  auto msfl_s2s_interaction = sMSFL_sMSFL_INTERACTION(latvec,
                                                              interaction_even_odd_mask,
                                                              is_lrf_src,
                                                              unique_sfl_interaction_index,
                                                              src_sfl_index, dst_sfl_index, weight);
          cassert(msfl_s2s_interaction.even_odd_mask() == interaction_even_odd_mask);
          cassert(msfl_s2s_interaction.is_lrf_src() == is_lrf_src);
	  interactions.push_back(msfl_s2s_interaction);
	} // s2s weights loop
	
	surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *) surfel_interaction + surfel_interaction->size());	
      } // src sfl loop
    }
  }
  
}

VOID write_interactions_to_file(std::ofstream& ofs,
				sMSFL* msfl,
				std::vector<sMSFL_sMSFL_INTERACTION>& interactions,
				std::vector<sMSFL*> unique_sfls,
				const std::string& msg ) {

  ofs << "===================================================================\n";
  ofs << msg << ", MSFL:: " << msfl->id() << "\n";
  int count = 0;
  for (auto interaction : interactions) {
    ofs << count << ". ";
    ofs << "(MSFL:" << unique_sfls[interaction.m_msfl_index]->id()
	<< ",P:" << int(interaction.even_odd_mask())
        << ",LRF:" << int(interaction.is_lrf_src())
	<< ",L:" << int(interaction.m_latvec)
	<< ",SSFL:" << int(interaction.m_src_sfl_index)
	<< ",DSFL:" << int(interaction.m_dst_sfl_index)
	<< ", W:" << interaction.m_weight
        << ")\n";
    count += 1;
  }
  ofs << "\n";
}

VOID init_msfl_s2s_interactions(sMSFL* msfl,
                                const std::array<sSURFEL*,N_SFLS_PER_MSFL>& sfls_to_pack,
                                size_t n_sfls_packed,
                                std::vector<sMSFL*> msfls,
                                const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map) {
  std::vector<sMSFL_sMSFL_INTERACTION> interactions;
  std::vector<sMSFL*> unique_sfls;
  
  init_msfl_s2s_interactions(msfl, sfls_to_pack,
                             n_sfls_packed, msfls,
                             child_sfl_to_msfl_map,
                             interactions, unique_sfls);

#ifdef DEBUG_MEGASURFEL_INTERACTIONS_INIT
  if (msfl->id() == 199) {
    std::ofstream ofs("s2s_interactions.txt", std::ofstream::out);
    //std::ofstream ofs("s2s_interactions.txt", std::ios_base::app);
    write_interactions_to_file(ofs, msfl, interactions, unique_sfls,  "Before sorting");
    ofs.close();
  }
#endif
  
  unsigned num_unique_sfls = unique_sfls.size();
  

  //s2s_lp_peers only needed for repeatable reduction
  bool allocate_s2s_lp_peers = GPU::g_use_repeatable_reduction;

  //If the surfel is an LRF we always need s2s_peers to comptue ustar0,
  //see <PHYSICS>/gpu_surfel_advect_phy.cu::process_s2s_interaction
  //
  //For other surfels that interact with LRF sources, we only need s2s_peers
  //for repeatable reduction
  bool any_src_s2s_msfls_are_lrf = std::any_of(unique_sfls.begin(),
                                               unique_sfls.end(),
                                               [] (const sMSFL* s) {
                                                 return s->is_lrf();
                                               });  
  bool allocate_s2s_peers = msfl->is_lrf() || (any_src_s2s_msfls_are_lrf && GPU::g_use_repeatable_reduction);
  unsigned num_interactions = interactions.size();

  std::sort(interactions.begin(),
	    interactions.end(),
	    compare_s2s_interactions);

  for(size_t warp_sort_index = 0;
      warp_sort_index < num_interactions;
      warp_sort_index += N_SFLS_PER_MSFL) {

    size_t warp_sort_index_end = warp_sort_index + N_SFLS_PER_MSFL;
    warp_sort_index_end = (warp_sort_index_end < num_interactions)? warp_sort_index_end : num_interactions;
	
    std::sort(interactions.begin() + warp_sort_index,
	      interactions.begin() + warp_sort_index_end,
	      compare_s2s_interactions_warp);
	
    group_peer_dst_sfl_interactions(interactions, warp_sort_index, warp_sort_index_end);
  }

#ifdef DEBUG_MEGASURFEL_INTERACTIONS_INIT
  if (msfl->id() == 199) {
    std::ofstream ofs("s2s_interactions.txt", std::ios_base::app);
    write_interactions_to_file(ofs, msfl, interactions, unique_sfls,  "After sorting");
    ofs.close();
  }
#endif
  
  unsigned n_component_elements[sMSFL_sMSFL_INTERACTIONS::N_COMPONENTS] = {
    num_unique_sfls, //unique_sfls
    num_interactions,//sMSFL_MSFL_INTERACTION
    allocate_s2s_lp_peers? num_interactions: 0,//s2s_lp_peers
    allocate_s2s_peers? num_interactions : 0 //s2s_peers
  };

  size_t n_bytes = sMSFL_sMSFL_INTERACTIONS::size(n_component_elements);
  sMSFL_sMSFL_INTERACTIONS*& s2s_interactions = msfl->s2s_advect_data()->m_surfel_interactions;
  int ierr = posix_memalign((void**) &s2s_interactions,
			    GPU::MINIMUM_CUDA_ALIGNMENT, n_bytes);
  memset(s2s_interactions, 0, n_bytes);
  s2s_interactions->set_n_component_elements(n_component_elements);

  memcpy(s2s_interactions->get<MSFL_MSFL_INT_COMPONENTS::INTERACTIONS>(),
	 &interactions[0], sizeof(sMSFL_sMSFL_INTERACTION) * num_interactions);

  memcpy(s2s_interactions->get<MSFL_MSFL_INT_COMPONENTS::UNIQUE_SFLS>(),
	 &unique_sfls[0], sizeof(sMSFL*) * num_unique_sfls);

  if (allocate_s2s_lp_peers || allocate_s2s_peers) {
    mark_s2s_peers(interactions, allocate_s2s_lp_peers, allocate_s2s_peers,
                   s2s_interactions->get<MSFL_MSFL_INT_COMPONENTS::S2S_LP_PEERS>(),
                   s2s_interactions->get<MSFL_MSFL_INT_COMPONENTS::S2S_PEERS>());
  }
}

VOID build_s2s_data_for_msfl_fset(SURFEL_GROUP_TYPE group_type,
				  SURFEL_FSET sfl_group_fset,
				  MSFL_FSET  msfl_group_fset,
				  std::vector<sMSFL*> msfls,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
				  size_t& msfl_id) {


  for (int scale = 0; scale < sim.num_scales; scale++) {
    const auto& set_of_sfl_groups = sfl_group_fset->get_groups_of_scale(scale);

    for (auto sfl_group : set_of_sfl_groups) {

      auto sequence = cSFL_SEQUENCE_FOR_MSFL_BUILD(sfl_group->begin(),
                                                   sfl_group->end());

      while(!sequence.empty()) {

        std::array<sSURFEL*, N_SFLS_PER_MSFL> sfls_to_pack{};
        int n_sfls_packed = sequence.set_next_sequence(sfls_to_pack);
        sMSFL* msfl = msfls[msfl_id];
        //sanity check
        for (int s = 0; s < n_sfls_packed; s++) {
          assert(child_sfl_to_msfl_map.at(sfls_to_pack[s]->id()).first == msfl_id);
          assert(child_sfl_to_msfl_map.at(sfls_to_pack[s]->id()).second == s);
        }

        if (msfl->is_s2s_destination()) {
          init_msfl_s2s_interactions(msfl, sfls_to_pack, n_sfls_packed,
                                     msfls, child_sfl_to_msfl_map);
        }

        msfl_id++;
      }
    }
  }
}

VOID prepare_s2s_s2v_data_on_host(std::vector<sMSFL*> msfls,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                  size_t batch_size) {

  size_t msfl_id = 0;
  for (int group_type = 0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {    

    MSFL_FSET msfl_group_fset = g_msfl_groups[group_type];
    SURFEL_FSET sfl_group_fset = g_surfel_groups[group_type];

    if (msfl_group_fset && msfl_group_fset->n_groups()) {
      
      build_s2s_data_for_msfl_fset((SURFEL_GROUP_TYPE) group_type,
				   sfl_group_fset,
				   msfl_group_fset,
				   msfls,
				   child_sfl_to_msfl_map,
				   msfl_id);
      
      build_s2v_data_for_msfl_fset((SURFEL_GROUP_TYPE) group_type, msfl_group_fset, batch_size);
    } 

  } //group_type-loop 
}

