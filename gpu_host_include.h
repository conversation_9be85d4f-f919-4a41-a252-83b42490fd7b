#ifndef SIMENG_GPU_HOST_INCLUDE_H
#define SIMENG_GPU_HOST_INCLUDE_H
  
#include "ublk_surfel_offset_table.h"
#include "shob_groups.h"
#include "box_advect.h"
#include "gpu_meas.h"
#include "gpu_ptr.h"

#if BUILD_GPU
//Forward decls
struct sSIM_INFO;
template<typename> struct tSHOB_SIZES_TABLE;
using sMSFL_SIZES_TABLE = tSHOB_SIZES_TABLE<sMSFL>;
struct sUBLK_TABLE;
struct sFLUID_DYN_DCACHE;
namespace GPU {
template<typename T> class Ptr;
using sDEV_PTR = Ptr<void>;
}

struct sH2D_EXPORT {

  const sGLOBALS* m_g;
  const sSIM_INFO* m_sim_info;

  const std::vector<sHMBLK*>* m_mblk_table;

  const std::vector<::sMSFL*>* m_msfl_table;

  const sMSFL_SIZES_TABLE* m_msfl_sizes_table;

  const uINT16 (*m_ublk_data_offset_table)[N_UBLK_TYPES][N_UBLK_DATA_TYPES];

  const asINT32 (*m_surfel_data_offset_table)[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];

  constexpr static size_t size_of_ublk_data_offset_table = sizeof(*m_ublk_data_offset_table);
  constexpr static size_t size_of_surfel_data_offset_table = sizeof(*m_surfel_data_offset_table);

  const uINT16 (*m_ublk_dynamics_data_size)[N_MAX_UBLK_PHYSICS_TYPES];

  constexpr static size_t size_of_ublk_dynamics_data_table = sizeof(*m_ublk_dynamics_data_size);

  MBLK_FSET (*m_ublk_groups) [N_UBLK_GROUP_TYPES];
  MSFL_FSET (*m_surfel_groups) [N_SURFEL_GROUP_TYPES];

  const sFLUID_DYN_DCACHE* m_dcache;
  const sMEAS_WINDOW_COLLECTION* m_meas_windows;

  sH2D_EXPORT(const sGLOBALS *g,
	      const sSIM_INFO* sim_info,
	      const std::vector<sHMBLK*>* h_mblk_table,
	      const std::vector<::sMSFL*>* h_msfl_table,
	      const sMSFL_SIZES_TABLE* msfl_sizes_table,
	      const uINT16 (*ublk_data_offset_table)[N_UBLK_TYPES][N_UBLK_DATA_TYPES],
	      const asINT32 (*surfel_data_offset_table)[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES],
	      const uINT16 (*ublk_dynamics_data_size)[N_MAX_UBLK_PHYSICS_TYPES],
	      MBLK_FSET (*ublk_groups) [N_UBLK_GROUP_TYPES],
	      MSFL_FSET (*surfel_groups) [N_SURFEL_GROUP_TYPES],
	      const sFLUID_DYN_DCACHE* dcache,
	      const sMEAS_WINDOW_COLLECTION* meas_windows):
      m_g(g),
      m_sim_info(sim_info),
      m_mblk_table(h_mblk_table),
      m_msfl_table(h_msfl_table),
      m_msfl_sizes_table(msfl_sizes_table),
      m_ublk_data_offset_table(ublk_data_offset_table),
      m_surfel_data_offset_table(surfel_data_offset_table),
      m_ublk_dynamics_data_size(ublk_dynamics_data_size),
      m_ublk_groups(ublk_groups),
      m_surfel_groups(surfel_groups),
      m_dcache(dcache),
      m_meas_windows(meas_windows) { }

};

VOID export_host_data_to_device(const sH2D_EXPORT& h_data);
VOID run_test_kernel();

extern std::vector<std::array<sUBLK*,8>> g_mblk_children;
size_t get_max_number_of_ublks_in_groups();
size_t get_max_number_of_surfels_in_groups();

namespace GPU 
{
class cMBLK_DYN_AND_MEAS;
extern cMBLK_DYN_AND_MEAS g_ublk_dyn_and_meas;
extern int my_device_id;
extern SHOB_ID print_ublk_parent_id;
extern SHOB_ID print_mblk_children_id;
extern SHOB_ID print_sfl_parent_id;
extern SHOB_ID print_msfl_children_id;
void set_device_comm_thread();
[[nodiscard]] sSP_GPU_INFO init_device_and_context();
extern std::vector<sHMBLK*> g_host_mblks;
sMBLK* copy_ublk_from_d2h(const sHMBLK* h_ublk);
void init_batches(size_t max_ublks, size_t max_surfels);
size_t msfl_batch_size();
VOID initialize_msfl_group_ranges();

enum REDUCTION_MODE : bool {
  NON_REPEATABLE = false,
  REPEATABLE = true
};

extern REDUCTION_MODE g_use_repeatable_reduction;
extern REDUCTION_MODE g_use_repeatable_meas;
extern uINT32 g_max_buffer_size;

std::array<size_t,2> get_ublk_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp);
std::set<int> get_ublk_group_dest_sp(UBLK_GROUP_TYPE group_type, int scale);
bool ublk_group_has_measurements(UBLK_GROUP_TYPE group_type, int scale, int dest_sp);

std::array<size_t,2> get_vr_coarse_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp);
std::array<size_t,2> get_surfel_group_range(SURFEL_GROUP_TYPE group_type, int scale, int dest_sp);
std::set<int> get_surfel_group_dest_sp(SURFEL_GROUP_TYPE group_type, int scale);
template <typename T>
inline BOOLEAN is_empty_group(const T& range)  { return range[0] == range[1]; }

void do_send_surfel_bc_types();
};

VOID print_mblk(const sHMBLK* h_ublk,
                     int child_ublk_index,
                     int state_index, // 0 - current, -1 - prev
                     const char* msg,
                     std::ostream& os);

VOID print_all_mblks_in_group(MBLK_GROUP group,
                                   int state_index, // 0 - current, -1 - prev
                                   const char* msg);

VOID print_select_mblks_in_group(MBLK_GROUP group,
                                      int state_index, // 0 - current, -1 - prev
                                      const char* msg,
                                      std::vector<STP_SHOB_ID> print_ids);

sHMBLK* find_mblk_in_group(MBLK_GROUP group, STP_SHOB_ID debug_ublk_id);

/*==============================================================================
 * @struct sHMBLK_HEADER
 * All sHMBLKs are really allocated as sHMBLK_HEADER's, with size information and
 * the device mblk hanging off the tail end of the sHMBLK object
 *
 * sHMBLK is a compressed version of a MUBLK, with just the base MUBLK information
 * and limited solver block data that is required for host side initialization.
 *
 * Most parts of the code base deal with sHMBLKs and not sHMBLK_HEADERs since
 * its much more convenient, and other aliases will be based off sHMBLK making it
 * nicer to read in compiler error messages. When size information is needed, a
 * sHMBLK* ptr can be case safely with the "as_header(sHMBLK*)" function
 *============================================================================*/
struct sHMBLK_HEADER : public sHMBLK {
  
public:
  
  sHMBLK_HEADER(sHMBLK::sUBLK_ATTRS u,
                   size_t size_of_dublk,
                   size_t size_of_dyn_data):
    m_device_mblk(nullptr),
    m_size_of_device_mblk(size_of_dublk),
    m_size_of_dyn_data(size_of_dyn_data)
  {
    BLOCK_LAYOUT::init(u, size_of_dyn_data);
  }

  sHMBLK_HEADER():m_device_mblk(nullptr),
                     m_size_of_device_mblk(0) {}

  void copy(const sHMBLK_HEADER& other) {
    memcpy(this, &other, sizeof(sHMBLK_HEADER));
  }

  //Non trivial types contained inside tUBLK dont have copy constructor/assignment
  //defined, thus we cannot use default compiler implementations.
  // Hence for now just copy as a bunch of bytes
  sHMBLK_HEADER(const sHMBLK_HEADER& other) { copy(other); }
  sHMBLK_HEADER& operator=(const sHMBLK_HEADER& other) {
    if (this != &other) { copy(other); }
    return *this;
  }
  
  static size_t size() { return sizeof(sHMBLK_HEADER); }
  void update_size_of_device_mblk(size_t s) { m_size_of_device_mblk = s; }
  void set_device_mblk(sMBLK* d_mblk) { m_device_mblk = d_mblk; }
  __HOST__DEVICE__ size_t size_of_device_mblk() const { return m_size_of_device_mblk; }
  __HOST__DEVICE__ size_t size_of_dyn_data() const { return m_size_of_dyn_data; }  
  __HOST__DEVICE__ sMBLK* get_device_mblk() { return m_device_mblk; }
  
private:
  sMBLK* m_device_mblk;
  size_t m_size_of_device_mblk;
  size_t m_size_of_dyn_data;
};

__HOST__DEVICE__ INLINE sHMBLK_HEADER* as_header(sHMBLK* h) {
  return reinterpret_cast<sHMBLK_HEADER*>(h);
}

__HOST__DEVICE__ INLINE const sHMBLK_HEADER* as_header(const sHMBLK* h) {
  return as_header(const_cast<sHMBLK*>(h));
}

std::vector<sHMBLK::UBLK_BOX> init_mblk_boxes();

std::vector<sHMBLK*> build_mblks(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                                        std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                        std::vector<std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER>>& device_windows);
template<class T_M_SET, class T_SET, class T_M_RECV_GROUP, class T_RECV_GROUP>
void build_send_group(T_M_SET& mblk_recv_fset, 
                      const T_SET& recv_fset, 
                      const std::vector<sHMBLK*> mblks,
                      const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                      bool communicate_turb_data);
void build_mblks_send_recv_groups(const std::vector<sHMBLK*> mblks,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                  bool communicate_turb_data);
void build_msfls_send_recv_groups(const std::vector<sMSFL*> megasfls,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                  bool communicate_turb_data);
void clear_mblks_send_recv_groups();

std::vector<sMSFL*> build_msfls(const std::vector<sHMBLK*>& mblks,
             const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
             std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
             std::vector<std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER>>& device_windows);

VOID set_nearblock_closest_surfels(std::vector<sHMBLK*>& mblks,
				   const std::vector<sMSFL*>& h_msfl_table,
           const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map);

void gpu_mark_fringe_coarse_voxels_that_abut_interior(const std::vector<sHMBLK*> mblks,
                                                      const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map);
void set_vr_coarse_and_fine_info(const std::vector<sHMBLK*>& mblk,
                                 const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map);

#endif // BUILD_GPU
#endif
