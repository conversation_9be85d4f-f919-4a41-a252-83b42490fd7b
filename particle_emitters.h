#ifndef _PARTICLE_EMITTERS_H_
#define _PARTICLE_EMITTERS_H_

#include "common_sp.h"
#include "eqns.h"
#include "ublk.h"
#include "surfel.h"
#include "particle_solver_data.h"
#include "shob_octree.h"

#include "particle_random_properties.h"
#include "particle_materials.h"
#include "particle_emitter_geometry.h"
#include "particle_emitter_configurations.h"

BOOLEAN construct_rotation_xform(sCSYS &m_csys, const sG3_VEC &m_direction);
BOOLEAN construct_rotation_xform(sCSYS &m_csys, const sG3_VEC &m_dir0, const sG3_VEC &m_dir1);

typedef class sEMISSION_DIRECTION {
 private:
  std::string m_direction_variable_names[3], m_major_ellipse_direction_variable_names[3];

  BOOLEAN m_needs_surface_normal;
  sG3_VEC m_direction;
  sCSYS m_csys;



 public:
 sEMISSION_DIRECTION(std::vector<std::string> &direction_variable_names, 
                     dFLOAT direction_coords[3],
                     dFLOAT major_ellipse_direction[3],
                     BOOLEAN elliptical_nozzle,
                     asINT32 direction_csys_id,
                     BOOLEAN needs_surface_normal = FALSE);

 sG3_VEC m_major_ellipse_direction;
 BOOLEAN m_elliptical_nozzle;
 
  //transform from cone csys to emitter csys
 VOID transform_velocity_from_cone_to_lattic_csys(sPARTICLE_VAR velocity_direction[3]);
 VOID compute_csys_transformations();
 

} *EMISSION_DIRECTION;


//----- Emitter Classes -----
typedef LGI::eLGI_EMISSION_RATE_TYPE eEMISSION_RATE_TYPE;


typedef class sPARTICLE_EMITTER_BASE {
 public :
  typedef enum {
    FIXED_POINT_SURFACE_EMITTER_TYPE,
    RANDOM_POINT_SURFACE_EMITTER_TYPE,
    FIXED_POINT_BOX_EMITTER_TYPE,
    RANDOM_POINT_BOX_EMITTER_TYPE,
    FIXED_POINT_CYLINDER_EMITTER_TYPE,
    RANDOM_POINT_CYLINDER_EMITTER_TYPE,
    POINT_EMITTER_TYPE,
    RAIN_CYLINDER_EMITTER_TYPE,
    RAIN_BOX_EMITTER_TYPE,
    TIRE_EMITTER_TYPE,
    NUM_EMITTERS_EMITTER_TYPE,
    UNDEFINED_EMITTER_TYPE
  }eEMITTER_TYPES;

 protected:
  std::string m_name;
  eEMITTER_TYPES m_type;
  BOOLEAN m_is_rain_emitter;
  asINT32 m_id;
  asINT32 m_cdi_id;
  EMISSION_GEOMETRY m_emission_geometry;

  asINT32 m_start_time;
  asINT32 m_end_time;
  asINT32 m_duration;
  //sPARTICLE_VAR m_fraction_eligible_for_measurement; //removed 2/28/17 to support changes for cdi 3.2 (wanderer).

  //For PR41963, emission rates should be distributed over all geometries. This fraction is computed based on the 
  //relative volumes of each emitter geometry.
  sPARTICLE_VAR m_emission_fraction; 

 private:

  static asINT32 m_num_total_emitters;
  static asINT32 m_parcel_id_counter; //shared across all emitters
  asINT32 m_particles_per_parcel;
  BOOLEAN m_subject_to_dispersion_box;
  BOOLEAN m_subject_to_gravity;
  asINT32 m_max_allowed_particle_age;
  sdFLOAT m_min_particle_velocity;
  BOOLEAN m_fixed_release_points;
  asINT32 m_max_allowed_reflections;
  sPARTICLE_VAR m_min_allowed_particle_velocity;
  BOOLEAN m_show_decorations;

 protected:
  sPARTICLE_VAR m_emission_deficit;

 public:
  static std::vector<asINT32> m_sim_to_cdi_emitter_id;
  sPARTICLE_EMITTER_BASE(sLGI_EMITTER_BASE &lgi_record,
                         asINT32 m_cdi_id,
                         std::string &name,
                         EMISSION_GEOMETRY emission_geometry,
                         sPARTICLE_VAR emission_fraction,
                         BOOLEAN fixed_release_points);


  std::string name() {return m_name;}
  asINT32 cdi_id() { return m_cdi_id;}
  eEMITTER_TYPES emitter_type() {return m_type;}
  BOOLEAN is_rain_emitter() {return m_is_rain_emitter;}
  asINT32 max_allowed_reflections() {return m_max_allowed_reflections;}
  asINT32 max_allowed_age() {return m_max_allowed_particle_age;}
  //dFLOAT fraction_eligible_for_measurement() {return m_fraction_eligible_for_measurement;} //removed 2/28/17 to support changes for cdi 3.2 (wanderer).
  BOOLEAN fixed_release_points(){return m_fixed_release_points;}
  BOOLEAN subject_to_dispersion_box(){return m_subject_to_dispersion_box;}
  dFLOAT particles_per_parcel() {return m_particles_per_parcel;}
  VOID reset_parcel_id_counter(asINT32 new_count) { m_parcel_id_counter = new_count; }
  VOID set_start_time(TIMESTEP start);
  asINT32 start_time() {return m_start_time;}
  asINT32 get_next_parcel_id() {return m_parcel_id_counter++;}
  asINT32 get_emitter_id() {return m_id;}
  virtual dFLOAT emission_rate() = 0;                    //This returns the emission rate in the, users chosen dimensions, from the emitter configuration.
  virtual eEMISSION_RATE_TYPE emission_rate_type() = 0;  //This returns the dimensions of the emitter confguration's release rate.


  virtual VOID update_space_varying_parameters(STP_GEOM_VARIABLE point[3],
                                               STP_GEOM_VARIABLE normal[3],
                                               asINT32 timestep,
                                               sFLOAT powertherm_time) = 0;

  //These three are used to convert various release rate types for various emitters into an overall fequency release rate (particles per timestep).
  virtual dFLOAT expected_material_density() = 0;
  virtual dFLOAT expected_particle_volume() = 0;
  virtual dFLOAT particle_emission_rate(); //This returns the release rate in particles per parcel.

  EMISSION_GEOMETRY emission_geometry() {return m_emission_geometry;}
  virtual PARTICLE_MATERIAL material() = 0;
  virtual sdFLOAT characteristic_emitter_size() = 0;
  virtual BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR pos[3], UBLK &ublk, sINT8 &voxel) = 0;
  virtual BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) = 0;


  virtual PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration() = 0;
  virtual asINT32 sample_local_air_properites_inside_rain_emitter(PARTICLE_VAR values) {return -1;}


  virtual VOID emit_parcels() = 0;

  VOID write_ckpt();
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);

}*PARTICLE_EMITTER_BASE;

template < typename sEMITTER_CONFIG_TYPE >
class tCONFIGURABLE_EMITTER_BASE : public sPARTICLE_EMITTER_BASE
{
 protected:
  sEMITTER_CONFIG_TYPE* m_emitter_configuration;
 public:
 tCONFIGURABLE_EMITTER_BASE(sLGI_EMITTER_BASE &lgi_record,
                            asINT32 m_cdi_id,
                            std::string &name,
                            EMISSION_GEOMETRY emission_geometry,
                            sPARTICLE_VAR emission_fraction,
                            BOOLEAN fixed_release_points,
                            PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  sPARTICLE_EMITTER_BASE(lgi_record,
                         m_cdi_id,
                         name,
                         emission_geometry,
                         emission_fraction,
                         fixed_release_points)
    {
      this->m_type = UNDEFINED_EMITTER_TYPE;
      m_emitter_configuration = (sEMITTER_CONFIG_TYPE*)emitter_configuration;
    }

  PARTICLE_MATERIAL material() {return emitter_configuration()->material(); }
  //These quantities are needed to compute frequency release rates from other types of release rates.
  dFLOAT expected_particle_volume() {return m_emitter_configuration->expected_particle_volume(); }
  dFLOAT expected_material_density() {return m_emitter_configuration->material()->expected_material_density(); }
  dFLOAT emission_rate() {return m_emitter_configuration->emission_rate();}
  eEMISSION_RATE_TYPE emission_rate_type() {return m_emitter_configuration->emission_rate_type();}
  sEMITTER_CONFIG_TYPE* emitter_configuration() {return m_emitter_configuration;}
  virtual VOID emit_parcels();
  VOID update_space_varying_parameters(STP_GEOM_VARIABLE point[3],
                                       STP_GEOM_VARIABLE normal[3],
                                       asINT32 timestep,
                                       sFLOAT powertherm_time) {
    m_emitter_configuration->eval_space_varying_physics_variables(point, normal, timestep, powertherm_time);
  }
};

//----- Surface Emitters ----

template < typename sEMITTER_CONFIG_TYPE>
class tSURFACE_EMITTER_BASE : public tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 protected:

  BOOLEAN m_emit_towards_surface_normal;
  sEMISSION_DIRECTION m_emission_direction;

 public:
 tSURFACE_EMITTER_BASE(sLGI_SURFACE_EMITTER_REC &lgi_record,
                       asINT32 cdi_id,
                       std::string &name,
                       EMISSION_SURFACE emission_surface,
                       BOOLEAN fixed_release_points,
                       PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration,
                       std::vector<std::string> &spray_direction_variable_names) :
  tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                     cdi_id, name,
                                                     emission_surface,
                                                     1.0, //emission fraction
                                                     fixed_release_points,
                                                     emitter_configuration) ,
    m_emission_direction(spray_direction_variable_names,
                         lgi_record.spray_direction_constants,
                         lgi_record.major_ellipse_direction,
                         lgi_record.elliptical_nozzle,
                         lgi_record.spray_direction_csys_id,
                         lgi_record.emitt_along_surface_normal)
      {
        this->m_type = sPARTICLE_EMITTER_BASE::UNDEFINED_EMITTER_TYPE;
        m_emit_towards_surface_normal = lgi_record.emitt_along_surface_normal;
      }

  //VOID emit_parcels();
  sdFLOAT emitter_volume(){ return 0.0; }
  sdFLOAT emitter_area(){ return emission_surface()->area(); }
  sdFLOAT characteristic_emitter_size() {return emitter_area(); }
  EMISSION_SURFACE emission_surface() {return (EMISSION_SURFACE)this->m_emission_geometry; }

  BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR position[3], UBLK &ublk, sINT8 &voxel) {
    //Pick a new initial position from the emission geometry.
    ublk = NULL;
    voxel = -1;
    sPARTICLE_VAR no_care[3];
    this->m_emission_geometry->sample_position(position, this->fixed_release_points(), no_care);
    return(this->m_emission_geometry->find_voxel(position[0], position[1], position[2], ublk, voxel));
  }
  BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]);
 

};


template <typename sEMITTER_CONFIG_TYPE>
class tRANDOM_POINT_SURFACE_EMITTER : public tSURFACE_EMITTER_BASE<sEMITTER_CONFIG_TYPE> {
 private:
 public:

 tRANDOM_POINT_SURFACE_EMITTER(sLGI_SURFACE_EMITTER_REC &lgi_record,
                               asINT32 cdi_id,
                               std::string &name,
                               EMISSION_SURFACE emission_surface,
                               PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration,
                               std::vector<std::string> &spray_direction_variable_names) :
  tSURFACE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                cdi_id,
                                                name,
                                                emission_surface,
                                                FALSE,
                                                emitter_configuration,
                                                spray_direction_variable_names)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::RANDOM_POINT_SURFACE_EMITTER_TYPE;
    }
  //BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR pos[3], UBLK &ublk, sINT8 &voxel);

};

template <typename sEMITTER_CONFIG_TYPE>
class tFIXED_POINT_SURFACE_EMITTER : public tSURFACE_EMITTER_BASE<sEMITTER_CONFIG_TYPE> {
 private:
 public:

 tFIXED_POINT_SURFACE_EMITTER(sLGI_SURFACE_EMITTER_REC &lgi_record,
                              asINT32 cdi_id,
                              std::string &name,
                              EMISSION_SURFACE emission_surface,
                              PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration,
                              std::vector<std::string> &spray_direction_variable_names) :
  tSURFACE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                cdi_id,
                                                name,
                                                emission_surface,
                                                TRUE, //fixed release points
                                                emitter_configuration,
                                                spray_direction_variable_names)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::FIXED_POINT_SURFACE_EMITTER_TYPE;
    }

  //BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR pos[3], UBLK &ublk, sINT8 &voxel);

};


//----- Volume Emitters -----
template <typename sEMITTER_CONFIG_TYPE>
class tVOLUME_EMITTER_BASE : public tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >{

 protected:
  sEMISSION_DIRECTION m_emission_direction;
  sPARTICLE_VAR m_release_spacing[N_SPACE_DIMS];

 public:

tVOLUME_EMITTER_BASE(sLGI_VOLUME_EMITTER_REC &lgi_record,
                     asINT32 cdi_id,
                     std::string &name,
                     EMISSION_VOLUME emission_volume,
                     sPARTICLE_VAR emission_fraction,
                     BOOLEAN fixed_release_points,
                     PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration,
                     std::vector<std::string> &spray_direction_variable_names);


  virtual dFLOAT emitter_volume() = 0;
  //VOID emit_parcels();

  sdFLOAT characteristic_emitter_size() {return emitter_volume();}
  BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR position[3], UBLK &ublk, sINT8 &voxel) {
    //Pick a new initial position from the emission geometry.
    ublk = NULL;
    voxel = -1;
    this->m_emission_geometry->sample_position(position, this->fixed_release_points(), m_release_spacing);
    return(this->m_emission_geometry->find_voxel(position[0], position[1], position[2], ublk, voxel));
  }
  BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) {
    this->m_emitter_configuration->sample_spray_direction(velocity); //The velocity magnitude and spray direction are assumed to be statistically independend in this spray model.
    m_emission_direction.transform_velocity_from_cone_to_lattic_csys(velocity);
    sPARTICLE_VAR velocity_magnitude = this->m_emitter_configuration->sample_spray_velocity_magnitude();
    vmul(velocity, velocity_magnitude);
    return TRUE;
  }
};

template <typename sEMITTER_CONFIG_TYPE>
class tBOX_EMITTER_BASE : public tVOLUME_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 protected:
 public:
  tBOX_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(sLGI_VOLUME_EMITTER_REC &lgi_record,
                                            asINT32 cdi_id,
                                            std::string &name,
                                            std::vector<std::string> &spray_direction_variable_names,
                                            EMISSION_BOX box,
                                            sPARTICLE_VAR emission_fraction,
                                            BOOLEAN fixed_release_points,
                                            PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tVOLUME_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                               cdi_id,
                                               name,
                                               box,
                                               emission_fraction,
                                               fixed_release_points,
                                               emitter_configuration,
                                               spray_direction_variable_names)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::UNDEFINED_EMITTER_TYPE;
    }

  dFLOAT emitter_volume(){return this->m_emission_geometry->volume();}

};

template <typename sEMITTER_CONFIG_TYPE>
class tRANDOM_POINT_BOX_EMITTER : public tBOX_EMITTER_BASE<sEMITTER_CONFIG_TYPE> {
 private:
 public:
 tRANDOM_POINT_BOX_EMITTER(sLGI_VOLUME_EMITTER_REC &lgi_record,
                           asINT32 cdi_id,
                           std::string &name,
                           std::vector<std::string> &spray_direction_variable_names,
                           EMISSION_BOX box,
                           sPARTICLE_VAR emission_fraction,
                           PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tBOX_EMITTER_BASE<sEMITTER_CONFIG_TYPE>(lgi_record,
                                          cdi_id,
                                          name,
                                          spray_direction_variable_names,
                                          box,
                                          emission_fraction,
                                          FALSE,
                                          emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::RANDOM_POINT_BOX_EMITTER_TYPE;
    }
};

template <typename sEMITTER_CONFIG_TYPE>
class tFIXED_POINT_BOX_EMITTER : public tBOX_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 private:

 public:

 tFIXED_POINT_BOX_EMITTER(sLGI_VOLUME_EMITTER_REC &lgi_record,
                          asINT32 cdi_id,
                          std::string &name,
                          std::vector<std::string> &spray_direction_variable_names,
                          EMISSION_BOX box,
                          sPARTICLE_VAR emission_fraction,
                          PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tBOX_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                            cdi_id,
                                            name,
                                            spray_direction_variable_names,
                                            box,
                                            emission_fraction,
                                            TRUE,
                                            emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::FIXED_POINT_BOX_EMITTER_TYPE;
    }
};

template <typename sEMITTER_CONFIG_TYPE>
class tCYLINDER_EMITTER_BASE : public tVOLUME_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {

 protected:

 public:
 tCYLINDER_EMITTER_BASE(sLGI_VOLUME_EMITTER_REC &lgi_record,
                        asINT32 cdi_id,
                        std::string &name,
                        std::vector<std::string> &spray_direction_variable_names,
                        EMISSION_CYLINDER cylinder,
                        sPARTICLE_VAR emission_fraction,
                        BOOLEAN fixed_release_points,
                        PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tVOLUME_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                               cdi_id,
                                               name,
                                               cylinder,
                                               emission_fraction,
                                               fixed_release_points,
                                               emitter_configuration,
                                               spray_direction_variable_names)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::UNDEFINED_EMITTER_TYPE;
    }

  dFLOAT emitter_volume() {return this->m_emission_geometry->volume();}

};

template <typename sEMITTER_CONFIG_TYPE>
class tRANDOM_POINT_CYLINDER_EMITTER : public tCYLINDER_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 private:
 public:

 tRANDOM_POINT_CYLINDER_EMITTER(sLGI_VOLUME_EMITTER_REC &lgi_record,
                                asINT32 cdi_id,
                                std::string &name,
                                std::vector<std::string> &spray_direction_variable_names,
                                EMISSION_CYLINDER cylinder,
                                sPARTICLE_VAR emission_fraction,
                                PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tCYLINDER_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                 cdi_id,
                                                 name,
                                                 spray_direction_variable_names,
                                                 cylinder,
                                                 emission_fraction,
                                                 FALSE,
                                                 emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::RANDOM_POINT_CYLINDER_EMITTER_TYPE;
    }
};

template <typename sEMITTER_CONFIG_TYPE>
class tFIXED_POINT_CYLINDER_EMITTER : public tCYLINDER_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 private:
 public:

 tFIXED_POINT_CYLINDER_EMITTER(sLGI_VOLUME_EMITTER_REC &lgi_record,
                               asINT32 cdi_id,
                               std::string &name,
                               std::vector<std::string> &spray_direction_variable_names,
                               EMISSION_CYLINDER cylinder,
                               sPARTICLE_VAR emission_fraction,
                               PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tCYLINDER_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                 cdi_id,
                                                 name,
                                                 spray_direction_variable_names,
                                                 cylinder,
                                                 emission_fraction,
                                                 TRUE,
                                                 emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::FIXED_POINT_CYLINDER_EMITTER_TYPE;
    }

};

//----- Point Emitters -----
template < typename sEMITTER_CONFIG_TYPE >
class tPOINT_EMITTER : public tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 private:
  std::vector<dFLOAT> points;
  sEMISSION_DIRECTION m_emission_direction;


 public:
 tPOINT_EMITTER(sLGI_POINT_EMITTER_REC &lgi_record,
                asINT32 cdi_id,
                std::string &name,
                std::vector<std::string> &spray_direction_variable_names,
                EMISSION_POINTS emission_points,
                PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration):
  tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                                     cdi_id, name,
                                                     emission_points,
                                                     1.0, //emission_fraction
                                                     TRUE,
                                                     emitter_configuration),
    m_emission_direction(spray_direction_variable_names,
                         lgi_record.spray_direction_constants,
                         lgi_record.major_ellipse_direction,
                         lgi_record.elliptical_nozzle,
                         lgi_record.spray_direction_csys_id)
      {
        this->m_type = sPARTICLE_EMITTER_BASE::POINT_EMITTER_TYPE;
      }
  dFLOAT emitter_volume() {return 0.0;}
  //VOID emit_parcels();
  sdFLOAT characteristic_emitter_size() {return(emitter_volume());}

  BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR position[3], UBLK &ublk, sINT8 &voxel) {
    //Pick a new initial position from the emission geometry.
    ublk = NULL;
    voxel = -1;
    sPARTICLE_VAR no_care[3];
    this->m_emission_geometry->sample_position(position, this->fixed_release_points(), no_care);
    return(this->m_emission_geometry->find_voxel(position[0], position[1], position[2], ublk, voxel));
  }

  BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) {
    this->m_emitter_configuration->sample_spray_direction(velocity); //The velocity magnitude and spray direction are assumed to be statistically independend in this spray model.
    this->m_emission_direction.transform_velocity_from_cone_to_lattic_csys(velocity);
    sPARTICLE_VAR velocity_magnitude = this->m_emitter_configuration->sample_spray_velocity_magnitude();
    vmul(velocity, velocity_magnitude);
    return TRUE;
  }
};



// ---- Rain Emitters -----
template <typename sEMITTER_CONFIG_TYPE>
class tRAIN_EMITTER_BASE : public tCONFIGURABLE_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {

 private:
  //sPARTICLE_VAR m_release_spacing[N_SPACE_DIMS];
  sdFLOAT m_rain_emission_rate;
  sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER * m_emission_rate_calculator;

 public:
 tRAIN_EMITTER_BASE(sLGI_RAIN_EMITTER_REC &lgi_record,
                    asINT32 cdi_id,
                    std::string &name,
                    EMISSION_VOLUME emission_volume,
                    sPARTICLE_VAR emission_fraction,
                    PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration);
  //VOID emit_parcels();
  VOID set_rain_emitter_particle_emission_rate(double rain_emission_rate) { m_rain_emission_rate = rain_emission_rate;}
  dFLOAT particle_emission_rate(); //Override the base class as part of a fix for PR38377.
  virtual sdFLOAT emitter_volume() = 0;
  sdFLOAT characteristic_emitter_size() {return(emitter_volume());}
  BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR position[3], UBLK &ublk, sINT8 &voxel) {
    //Pick a new initial position from the emission geometry.
    ublk = NULL;
    voxel = -1;
    sPARTICLE_VAR no_care[3];
    this->m_emission_geometry->sample_position(position, this->fixed_release_points(), no_care);
    return(this->m_emission_geometry->find_voxel(position[0], position[1], position[2], ublk, voxel));
  }

  BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) { return TRUE;}
  SIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER emission_rate_calculator(){return m_emission_rate_calculator;}  

};

template < typename sEMITTER_CONFIG_TYPE >
class tRAIN_CYLINDER_EMITTER : public tRAIN_EMITTER_BASE< sEMITTER_CONFIG_TYPE > {
 private:
  sLGI_CYLINDER_REC m_cylinder;
  std::string cylinder_name;
 public:
 tRAIN_CYLINDER_EMITTER(sLGI_RAIN_EMITTER_REC &lgi_record,
                        asINT32 cdi_id,
                        std::string &name,
                        EMISSION_CYLINDER cylinder,
                        sPARTICLE_VAR emission_fraction,
                        PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration ) :
  tRAIN_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                             cdi_id,
                                             name,
                                             cylinder,
                                             emission_fraction,
                                             emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::RAIN_CYLINDER_EMITTER_TYPE;
    }

  sdFLOAT emitter_volume(){return this->m_emission_geometry->volume();}
  asINT32 sample_local_air_properites_inside_rain_emitter(PARTICLE_VAR values) {
    //Rain emitters, which may specify a release rate interms of a depth rain rate, need
    //to sample the local wind speed and air density to compute a release rate.
    //The values at the center of the emission geometry are used for this purpose and this
    //sampling is performed here.  Elsewhere, these samples are shared among all SPs so
    //each part of the emitter uses the same parameters

    //The above strategy showed issues since the prescribed initial velocity field may be 
    //far from statistically steady, so now the characteristic values for the case setup is used.

    //return values
    //0 required emitter exists, but the point is not in the SP
    //1 required emitter exists and the center is in the SP
    vzero(values);
    values[3] = 0.0;
    sPARTICLE_VAR center[3];
    this->m_emission_geometry->geometric_center(center);
    UBLK ublk;
    sINT8 voxel;
    if(this->m_emission_geometry->find_voxel(center[0], center[1], center[2], ublk, voxel))
      {
        asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(ublk->scale());
        sPARTICLE_VAR fluid_vel[3];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[0][voxel];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[1][voxel];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[2][voxel];	
        sPARTICLE_VAR fluid_density = ublk->lb_data()->m_lb_data[prior_index].density[voxel] * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR; //voxel fluid density
#if BUILD_D19_LATTICE
	if(sim.is_pf_model) {
          fluid_vel[0] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[0][voxel];
          fluid_vel[1] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[1][voxel];
          fluid_vel[2] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[2][voxel];
          sPARTICLE_VAR phi = ublk->uds_data(0)->uds[prior_index][voxel];
          fluid_density = phi2rho_capped(phi) * g_density_scale_factor * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR;
        }
#endif
        vcopy(values, fluid_vel);
        values[3] = fluid_density;
        return 1;
      }
    return 0;
  }
};

template < typename sEMITTER_CONFIG_TYPE >
class tRAIN_BOX_EMITTER: public tRAIN_EMITTER_BASE<sEMITTER_CONFIG_TYPE > {

 private:

  sLGI_BOX_REC box;
  std::string box_name;


 public:
 tRAIN_BOX_EMITTER(sLGI_RAIN_EMITTER_REC &lgi_record,
                   asINT32 cdi_id,
                   std::string &name,
                   EMISSION_BOX &box,
                   sPARTICLE_VAR emission_fraction,
                   PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration) :
  tRAIN_EMITTER_BASE< sEMITTER_CONFIG_TYPE >(lgi_record,
                                             cdi_id,
                                             name,
                                             box,
                                             emission_fraction,
                                             emitter_configuration)
    {
      this->m_type = sPARTICLE_EMITTER_BASE::RAIN_BOX_EMITTER_TYPE;
    }

  sdFLOAT emitter_volume(){return this->m_emission_geometry->volume();}
  asINT32 sample_local_air_properites_inside_rain_emitter(PARTICLE_VAR values) {
    //Rain emitters, which may specify a release rate interms of a depth rain rate, need
    //to sample the local wind speed and air density to compute a release rate.
    //The values at the center of the emission geometry are used for this purpose and this
    //smapling is performed here.  Elsewhere, these samples are shared among all SPs so
    //each part of the emitter uses the same parameters


    //return values
    //0 required emitter exists, but the point is not in the SP
    //1 required emitter exists and the center is in the SP


    vzero(values); values[3] = 0.0;
    sPARTICLE_VAR center[3];
    this->m_emission_geometry->geometric_center(center);
    UBLK ublk;
    sINT8 voxel;
    if(this->m_emission_geometry->find_voxel(center[0], center[1], center[2], ublk, voxel))
      {
        asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(ublk->scale());
        sPARTICLE_VAR fluid_vel[3];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[0][voxel];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[1][voxel];
        fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[2][voxel];
        sPARTICLE_VAR fluid_density = ublk->lb_data()->m_lb_data[prior_index].density[voxel] * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR; //voxel fluid density
#if BUILD_D19_LATTICE
	if(sim.is_pf_model) {
          fluid_vel[0] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[0][voxel];
          fluid_vel[1] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[1][voxel];
          fluid_vel[2] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[2][voxel];
          sPARTICLE_VAR phi = ublk->uds_data(0)->uds[prior_index][voxel];
          fluid_density = phi2rho_capped(phi) * g_density_scale_factor * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR;
        }
#endif
        vcopy(values, fluid_vel);
        values[3] = fluid_density;
        return 1;
      }
    return 0;
  }
};


// ---- Tire Emitter -----
class sTIRE_EMITTER : public sPARTICLE_EMITTER_BASE {


 private:
  sTIRE_EMITTER_CONFIGURATION* m_emitter_configuration;

  sdFLOAT m_zero_angle_direction[3];
  sdFLOAT m_rotation_axis[3];
  asINT32 m_zero_angle_csys_id;  //in which csys the zero angle direction and rotation axis are given

  CSYS m_zero_angle_csys;

  sdFLOAT m_lower_angle;
  sdFLOAT m_lower_stretch_factor;
  sdFLOAT m_lower_offset_angle;

  sdFLOAT m_upper_angle;
  sdFLOAT m_upper_stretch_factor;
  sdFLOAT m_upper_offset_angle;
  int m_cylinder_id;

 public:
  sTIRE_EMITTER(sLGI_TIRE_EMITTER_REC &lgi_record,
                asINT32 cdi_id,
                std::string &name,
                EMISSION_CYLINDER emission_cylinder,
                sPARTICLE_VAR emission_fraction,
                PARTICLE_EMITTER_CONFIGURATION_BASE emitter_configuration);

 
  dFLOAT particle_emission_rate(); //Override the base class as part of a fix for PR38377.
  asINT32 geometry_id() {return this->cylinder_id();}
  asINT32 cylinder_id() {return this->m_cylinder_id;}

  sdFLOAT emitter_area(){return 0.0;}
  sdFLOAT characteristic_emitter_size() {return emitter_area(); }

  VOID emit_parcels();
  PARTICLE_MATERIAL material() {return emitter_configuration()->material(); }
  dFLOAT expected_particle_volume() {return m_emitter_configuration->expected_particle_volume(); }
  dFLOAT expected_material_density() {return m_emitter_configuration->material()->expected_material_density(); }
  dFLOAT emission_rate() {return m_emitter_configuration->emission_rate();}
  eEMISSION_RATE_TYPE emission_rate_type() {return m_emitter_configuration->emission_rate_type();}
  sTIRE_EMITTER_CONFIGURATION* emitter_configuration() {return m_emitter_configuration;}

  VOID update_space_varying_parameters(STP_GEOM_VARIABLE point[3],
                                       STP_GEOM_VARIABLE normal[3],
                                       asINT32 timestep,
                                       sFLOAT powertherm_time) {
    m_emitter_configuration->eval_space_varying_physics_variables(point, normal, timestep, powertherm_time);
  }

  BOOLEAN pick_a_position_on_this_sp(sPARTICLE_VAR position[3], UBLK &ublk, sINT8 &voxel) {
    ublk = NULL;
    voxel = -1;
    sPARTICLE_VAR no_care[3];
    this->m_emission_geometry->sample_position(position, this->fixed_release_points(), no_care);
    return(this->m_emission_geometry->find_voxel(position[0], position[1], position[2], ublk, voxel));
  }

  BOOLEAN pick_a_velocity_vector(sPARTICLE_VAR velocity[3]) { return TRUE;}
};



// These templates represent the full set of supported emitter types. They are
// instantiated in particle_emitters.cc 
extern template class tCONFIGURABLE_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tCONFIGURABLE_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tCONFIGURABLE_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
extern template class tCONFIGURABLE_EMITTER_BASE<sRAIN_EMITTER_CONFIGURATION>;

extern template class tSURFACE_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tSURFACE_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tSURFACE_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

extern template class tFIXED_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tFIXED_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tFIXED_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
extern template class tRANDOM_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tRANDOM_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tRANDOM_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

extern template class tVOLUME_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tVOLUME_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tVOLUME_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

extern template class tCYLINDER_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tCYLINDER_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tCYLINDER_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;
extern template class tBOX_EMITTER_BASE<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tBOX_EMITTER_BASE<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tBOX_EMITTER_BASE<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;

extern template class tRAIN_EMITTER_BASE<sRAIN_EMITTER_CONFIGURATION>;
extern template class tRAIN_BOX_EMITTER<sRAIN_EMITTER_CONFIGURATION>;
extern template class tRAIN_CYLINDER_EMITTER<sRAIN_EMITTER_CONFIGURATION>;

extern template class tPOINT_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION>;
extern template class tPOINT_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION>;
extern template class tPOINT_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION>;


//Typedef the full set of supported emitter types.
typedef tRANDOM_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_RANDOM_POINT_SURFACE_EMITTER, *FULL_CONE_RANDOM_POINT_SURFACE_EMITTER;
typedef tFIXED_POINT_SURFACE_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_FIXED_POINT_SURFACE_EMITTER, *FULL_CONE_FIXED_POINT_SURFACE_EMITTER;
typedef tRANDOM_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_RANDOM_POINT_SURFACE_EMITTER, *HOLLOW_CONE_RANDOM_POINT_SURFACE_EMITTER;
typedef tFIXED_POINT_SURFACE_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_FIXED_POINT_SURFACE_EMITTER, *HOLLOW_CONE_FIXED_POINT_SURFACE_EMITTER;
typedef tRANDOM_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_RANDOM_POINT_SURFACE_EMITTER, *ELLIPTICAL_CONE_RANDOM_POINT_SURFACE_EMITTER;
typedef tFIXED_POINT_SURFACE_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_FIXED_POINT_SURFACE_EMITTER, *ELLIPTICAL_CONE_FIXED_POINT_SURFACE_EMITTER;

typedef tRANDOM_POINT_BOX_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_RANDOM_POINT_BOX_EMITTER, *FULL_CONE_RANDOM_POINT_BOX_EMITTER;
typedef tFIXED_POINT_BOX_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_FIXED_POINT_BOX_EMITTER, *FULL_CONE_FIXED_POINT_BOX_EMITTER;
typedef tRANDOM_POINT_BOX_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_RANDOM_POINT_BOX_EMITTER, *HOLLOW_CONE_RANDOM_POINT_BOX_EMITTER;
typedef tFIXED_POINT_BOX_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_FIXED_POINT_BOX_EMITTER, *HOLLOW_CONE_FIXED_POINT_BOX_EMITTER;
typedef tRANDOM_POINT_BOX_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_RANDOM_POINT_BOX_EMITTER, *ELLIPTICAL_CONE_RANDOM_POINT_BOX_EMITTER;
typedef tFIXED_POINT_BOX_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_FIXED_POINT_BOX_EMITTER, *ELLIPTICAL_CONE_FIXED_POINT_BOX_EMITTER;

typedef tRANDOM_POINT_CYLINDER_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_RANDOM_POINT_CYLINDER_EMITTER, *FULL_CONE_RANDOM_POINT_CYLINDER_EMITTER;
typedef tFIXED_POINT_CYLINDER_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_FIXED_POINT_CYLINDER_EMITTER, *FULL_CONE_FIXED_POINT_CYLINDER_EMITTER;
typedef tRANDOM_POINT_CYLINDER_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_RANDOM_POINT_CYLINDER_EMITTER, *HOLLOW_CONE_RANDOM_POINT_CYLINDER_EMITTER;
typedef tFIXED_POINT_CYLINDER_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_FIXED_POINT_CYLINDER_EMITTER, *HOLLOW_CONE_FIXED_POINT_CYLINDER_EMITTER;
typedef tRANDOM_POINT_CYLINDER_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_RANDOM_POINT_CYLINDER_EMITTER, *ELLIPTICAL_CONE_RANDOM_POINT_CYLINDER_EMITTER;
typedef tFIXED_POINT_CYLINDER_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_FIXED_POINT_CYLINDER_EMITTER, *ELLIPTICAL_CONE_FIXED_POINT_CYLINDER_EMITTER;

typedef tPOINT_EMITTER<sFULL_CONE_NOZZLE_CONFIGURATION> sFULL_CONE_POINT_EMITTER, *FULL_CONE_POINT_EMITTER;
typedef tPOINT_EMITTER<sHOLLOW_CONE_NOZZLE_CONFIGURATION> sHOLLOW_CONE_POINT_EMITTER, *HOLLOW_CONE_POINT_EMITTER;
typedef tPOINT_EMITTER<sELLIPTICAL_CONE_NOZZLE_CONFIGURATION> sELLIPTICAL_CONE_POINT_EMITTER, *ELLIPTICAL_CONE_POINT_EMITTER;

typedef tRAIN_EMITTER_BASE<sRAIN_EMITTER_CONFIGURATION> sRAIN_EMITTER_BASE, *RAIN_EMITTER_BASE;
typedef tRAIN_BOX_EMITTER<sRAIN_EMITTER_CONFIGURATION> sRAIN_BOX_EMITTER, *RAIN_BOX_EMITTER;
typedef tRAIN_CYLINDER_EMITTER<sRAIN_EMITTER_CONFIGURATION> sRAIN_CYLINDER_EMITTER, *RAIN_CYLINDER_EMITTER;
typedef sTIRE_EMITTER* TIRE_EMITTER;


#endif
