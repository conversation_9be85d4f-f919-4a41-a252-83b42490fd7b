#ifndef _EXA_SIMENG_GPU_HOST_INIT_H_
#define _EXA_SIMENG_GPU_HOST_INIT_H_

#include "simulator_namespace.h"
#include <type_traits>
#include "gpu_ptr.h"

#ifdef BUILD_GPU

template<typename T>
inline void init_mblk(const T (&ublk_data)[N_VOXELS_8], T (&mega_data)[N_VOXELS_64], size_t child_ublk)
{
  cassert(child_ublk < N_VOXELS_8);
  size_t offset = child_ublk*N_VOXELS_8;
  std::copy(ublk_data, ublk_data+N_VOXELS_8, mega_data+offset);
}


inline void init_mblk(const VOXEL_MASK_8& ublk, VOXEL_MASK_64& mega, size_t child_ublk)
{
  cassert(child_ublk < N_VOXELS_8);
  uINT64 u = ublk.get();
  u <<= (child_ublk*N_VOXELS_8);
  mega |= VOXEL_MASK_64{u};
}

inline void init_mblk(const tBITSET<2*N_VOXELS_8>& ublk, tBITSET<2*N_VOXELS_64>& mega, size_t child_ublk)
{
  cassert(child_ublk < N_VOXELS_8);
  for(int midx = 2*N_VOXELS_8*child_ublk, uidx=0; uidx < 2*N_VOXELS_8; midx++, uidx++) {
    mega.set_or_reset(midx, ublk.test(uidx));
  }
}

template<typename T, size_t N>
struct tSCALAR_OR_ARRAY;

template<typename T, size_t N>
using tSFL_VAR = tSCALAR_OR_ARRAY<T, N>;

template<typename T>
inline void init_msfl(const tSFL_VAR<T,1>& sfl_data, tSFL_VAR<T, N_SFLS_PER_MSFL>& mega, size_t child_sfl) {
  assert(child_sfl < N_VOXELS_64);
  mega[child_sfl] = sfl_data;
}

inline void init_msfl(const uINT64& sfl_data, MSFL_MASK& mega, size_t child_sfl) {
  assert(child_sfl < N_VOXELS_64);
  uINT64 s = sfl_data << child_sfl;
  mega |= MSFL_MASK(s);
}

/*=========================================
 * Functions and macros to simplify copying types to/from mblks
 *=========================================*/

template<typename T> struct to_ref
{
  static const T& convert (const T& ref) {
    return ref;
  }
  static T& convert (T& ref) {
    return ref;
  }
};

template<typename T> struct to_ref<T&>
{
  static T& convert (T& ref) {
    return ref;
  }
};

template<typename T> struct to_ref<T*>
{
  static T& convert (T* ptr) {
    return *ptr;
  }
};

#define INIT_MBLK(name) \
  inline void init_mblk(t##name<UBLK_SDFLOAT_TYPE_TAG>& ublk, t##name<HMBLK_SDFLOAT_TYPE_TAG>& mega, int child_ublk)

#define COPY_UBLK_TO_MBLK(var) ::init_mblk(to_ref<decltype(ublk.var)>::convert(ublk.var), to_ref<decltype(mega.var)>::convert(mega.var), child_ublk);

#define COPY2_8_TO_64(var1, var2) ::init_mblk(to_ref<decltype(ublk.var1)>::convert(ublk.var1), to_ref<decltype(mega.var2)>::convert(mega.var2), child_ublk);


#define INIT_MSFL(tNAME) \
  void init_msfl(const tNAME<SFL_SDFLOAT_TYPE_TAG>& sfl, tNAME<MSFL_SDFLOAT_TYPE_TAG>& mega, int child_sfl)

#define COPY_SFL_TO_MSFL(var) ::init_msfl(to_ref<decltype(sfl.var)>::convert(sfl.var), to_ref<decltype(mega.var)>::convert(mega.var), child_sfl);

#define VEC_COPY_UBLK_TO_MBLK(var, count) \
  for(size_t i=0; i<(count); i++) { \
    COPY_UBLK_TO_MBLK(var[i]);\
  }

#define VEC2_COPY_UBLK_TO_MBLK(var, count1, count2) \
  for(size_t i=0; i<(count1); i++) { \
    for(size_t j=0; j<(count2); j++) { \
      COPY_UBLK_TO_MBLK(var[i][j]);\
    }\
  }

#define VEC_COPY_SFL_TO_MSFL(var, count) \
  for (size_t i = 0; i < count; i++) {\
    COPY_SFL_TO_MSFL(var[i]); \
  }

#define VEC2_COPY_SFL_TO_MSFL(var, count1, count2) \
  for(size_t i=0; i<(count1); i++) { \
    for(size_t j=0; j<(count2); j++) { \
      COPY_SFL_TO_MSFL(var[i][j]);\
    }\
  }


/*==============================================================================
 * @enum HD_SLAB_ALLOCATOR_TYPES
 * There are different types of hd_slab_allocators to allow for proximity of
 * allocations to benefit performance. 
 * Any host device memory allocated with the TMP_INIT_DATA allocator is freed
 * immediately after shob initialization on the device
 *============================================================================*/
enum class HD_SLAB_ALLOCATOR_TYPES : unsigned {
  TMP_INIT_DATA,
  MBLK_SOLVER_DATA,
  MBLK_BOX,
  N_TYPES
};

/*==============================================================================
 * @struct tHD_SLAB_ALLOCATOR
 * An allocator specifically designed to minimize the use of memcopies from HOST to
 * DEVICE during initialization. Clients will not use this struct directly but
 * will instead use the conveience API hd_slab_malloc
 *
 * This allocator allocates both host and device memory simultaneously in chunks
 * of SLAB_SIZE. Clients can directly modify host side memory and swap host pointers
 * with device counterparts as necessary, as host slabs will be memcopied to the 
 * device finally with a call to copy_all_slabs_h2d()
 *============================================================================*/
struct sHD_SLAB_ALLOCATOR {

  struct TAG;
  
public:

  sHD_SLAB_ALLOCATOR(HD_SLAB_ALLOCATOR_TYPES type): m_pos_in_slab(0), m_type(type) {}
  ~sHD_SLAB_ALLOCATOR();
  sHD_SLAB_ALLOCATOR(const sHD_SLAB_ALLOCATOR&) = delete;
  sHD_SLAB_ALLOCATOR& operator=(const sHD_SLAB_ALLOCATOR&) = delete;
  void* malloc(size_t n_bytes, size_t align);  
  void copy_all_slabs_h2d();

public:
  template<typename T>
  static GPU::Ptr<T> get_device_ptr(T* h_ptr);
  
private:
  GPU::Ptr<void> get_device_ptr_impl(void* h_ptr);
  void add_new_hd_slabs();
  
private:
  constexpr static size_t SLAB_SIZE = 1024 * 1024 * 8; //8MB
  using BUFFER_TYPE = std::array<char, SLAB_SIZE>;
  std::vector<BUFFER_TYPE*> m_host_slabs;
  std::vector<BUFFER_TYPE*> m_device_slabs;
  size_t m_pos_in_slab;
  HD_SLAB_ALLOCATOR_TYPES m_type;
};

/*==============================================================================
 * @struct sHD_SLAB_ALLOCATOR::TAG
 * An 8 byte sized type that encodes the type of the allocator, the index of the
 * current slab in the vector of slabs, and offset into the slab where the
 * allocation was posititoned
 *============================================================================*/
struct sHD_SLAB_ALLOCATOR::TAG {
  
  explicit TAG(HD_SLAB_ALLOCATOR_TYPES type,
               size_t slab_index,
               size_t offset): m_type((uint64_t) type),
                               m_slab_index(slab_index),
                               m_offset(offset) {}

  size_t slab_index() const { return m_slab_index; }
  size_t slab_offset() const { return m_offset; }
  static size_t size() { return sizeof(TAG); }
  HD_SLAB_ALLOCATOR_TYPES type() const { return (HD_SLAB_ALLOCATOR_TYPES) m_type; }  
  static const TAG* tag_from_h_ptr(void* h_ptr);
  
private:
  uint64_t m_type : 4;
  uint64_t m_slab_index : 12;
  uint64_t m_offset : 48;
};

/*==============================================================================
 * @struct sHD_SLAB_ALLOCATOR_FACTORY
 *============================================================================*/
struct sHD_SLAB_ALLOCATOR_FACTORY {
  //Creates a new sHD_SLAB_ALLOCATOR if there doesn't exist one for a requested
  //type. There is one allocator instance for each HD_SLAB_ALLOCATOR_TYPE
  static sHD_SLAB_ALLOCATOR* get_allocator(HD_SLAB_ALLOCATOR_TYPES type);

  //Clears host and device memory associated with the allocator type  
  static VOID delete_allocator(HD_SLAB_ALLOCATOR_TYPES type);
  
  static BOOLEAN has_allocator(HD_SLAB_ALLOCATOR_TYPES type) { return m_hd_slab_allocators[(int) type] != nullptr; }
private:
  //One allocator per type
  static std::array<sHD_SLAB_ALLOCATOR*, (int) HD_SLAB_ALLOCATOR_TYPES::N_TYPES> m_hd_slab_allocators;
  
};

/*==============================================================================
 * @fcn hd_slab_malloc
 * Convenience function that returns 0 initialized chunk of bytes from a HD slab allocator
 * Note that the bytes returned are already 0'ed during the creation of the slab
 *============================================================================*/
template<typename T>
INLINE T* hd_slab_malloc(HD_SLAB_ALLOCATOR_TYPES type) {
  constexpr auto align = T::ALIGNMENT;
  auto p = (T*) sHD_SLAB_ALLOCATOR_FACTORY::get_allocator(type)->malloc(sizeof(T), align);
  return p;
}

INLINE void* hd_slab_malloc(HD_SLAB_ALLOCATOR_TYPES type,
                            size_t n_bytes,
                            size_t align = 8) {
  void* p = sHD_SLAB_ALLOCATOR_FACTORY::get_allocator(type)->malloc(n_bytes, align);
  return p;
}

//@fcn sHD_SLAB_ALLOCATOR::get_device_ptr
//First figures out which allocator by reading the tag, then delegates the translation
//to the specific allocator
template<typename T>
INLINE GPU::Ptr<T> sHD_SLAB_ALLOCATOR::get_device_ptr(T* h_ptr) {
  void* hv_ptr = (void*) h_ptr;
  const TAG* tag = sHD_SLAB_ALLOCATOR::TAG::tag_from_h_ptr(hv_ptr);
  auto d_ptr = sHD_SLAB_ALLOCATOR_FACTORY::get_allocator(tag->type())->get_device_ptr_impl(hv_ptr);
  return GPU::Ptr<T>((T*) d_ptr.get());
}

//Fwd decl for sort
template<typename SFL_TYPE_TAG> class tTAGGED_MLRF_SURFEL;
using TAGGED_MLRF_SURFEL = tTAGGED_MLRF_SURFEL<SFL_SDFLOAT_TYPE_TAG>;

/*==============================================================================
 * @struct MSFL_SORT_CATEGORIES
 * Specify relation between different surfel types for purposes of creating
 * MSFLs. For surfels of the same scale, sort criteria is based on:
 * - dynamics type
 * - if the surfel interacts with vr ublks or not
 * - Value of even odd mask
 *============================================================================*/
template<typename SFL_TYPE_TAG>
struct tMSFL_SORT_CATEGORIES {

  using sSURFEL = tSURFEL<SFL_TYPE_TAG>;
  
  static bool equiv(const sSURFEL& h_sfl1,
                    const sSURFEL& h_sfl2) {
    return equiv(&h_sfl1, &h_sfl2);
  }

  static bool equiv(const sSURFEL* h_sfl1,
                    const sSURFEL* h_sfl2) {
    return !comp(h_sfl1, h_sfl2) && !comp(h_sfl2, h_sfl1);
  }  

  static bool comp(const sSURFEL* const_h_sfl1,
                   const sSURFEL* const_h_sfl2);
};

using sMSFL_SORT_CATEGORIES = tMSFL_SORT_CATEGORIES<SFL_SDFLOAT_TYPE_TAG>;

//Fwd decl for cSFL_SEQUENCE_FOR_MSFL_BUILD
template<typename SURFEL_TYPE>
struct tSURFEL_GROUP;

using SURFEL_GROUP = tSURFEL_GROUP<sSURFEL>*;

/*==============================================================================
 * @struct cSFL_SEQUENCE_FOR_MSFL_BUILD
 * Given a start surfel to a group of surfels that are pre-sorted, this class
 * helps generate a sequence of surfels of the same type to build a MSFL
 *============================================================================*/
template<typename SFL_ITERATOR>
class cSFL_SEQUENCE_FOR_MSFL_BUILD {
  
  using pointer = typename SFL_ITERATOR::pointer;
  static_assert(std::is_same<pointer, sSURFEL*>::value ||
                std::is_same<pointer, sSURFEL**>::value ||
                std::is_same<pointer, TAGGED_MLRF_SURFEL*>::value,
                "Surfel iterator must either have a sSURFEL* or sSURFEL** pointer type");

  static sSURFEL* sfl_iter_to_ptr(SFL_ITERATOR it) {
    if constexpr(std::is_same<pointer, sSURFEL**>::value) {
      return *it;
    }
    else if constexpr(std::is_same<pointer, TAGGED_MLRF_SURFEL*>::value) {
      return it->mlrf_surfel();
    }
    else {
      return it.operator->(); //use this instead of dereference to avoid nullptr
    }
  }
  
  SFL_ITERATOR m_start;
  SFL_ITERATOR m_end;
  size_t m_n_total_sfls_packed;
public:
  explicit cSFL_SEQUENCE_FOR_MSFL_BUILD(SFL_ITERATOR start,
                                        SFL_ITERATOR end);
  
  bool empty() const { return m_start == m_end; }
  size_t total_sfls_packed() const { return m_n_total_sfls_packed; }
  //Returns no of sfls packed
  int set_next_sequence(std::array<sSURFEL*, N_SFLS_PER_MSFL>& sfls_to_pack);
};

namespace GPU {
sMSFL* map_H2D(sMSFL* h);
sMBLK* map_H2D(sHMBLK* h);
}

#endif // BUILD_GPU
#endif //_EXA_SIMENG_GPU_HOST_INIT_H_
