/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Strands that operate on sliding surfels and nearblks
 *
 * Vinit Gupta, Exa Corporation
 * Created Fri Sep 6, 2017
 *--------------------------------------------------------------------------*/


#include PHYSTYPES_H
#include PHYSICS_H
#include "shob_groups.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "sleep.h"
#include "surfel_process_control.h"
#include "surfel_dyn_sp.h"

VOID print_surfel_in_states(char *, sSURFEL_V2S_LB_DATA *, SURFEL);
VOID print_surfel_out_flux(sSURFEL_V2S_LB_DATA *v2s_lb_data, SURFEL surfel);

static inline asINT32 compute_all_solver_v2s_phase_masks(SOLVER_PHASE_MASKS phase_masks) {
  asINT32 all_solver_phase_mask = phase_masks >> (N_SOLVERS * STP_N_PHASES);
  all_solver_phase_mask &= ~(STP_S2V_PHASE_MASK);
  return all_solver_phase_mask;
}

static inline asINT32 compute_all_solver_s2v_phase_masks(SOLVER_PHASE_MASKS phase_masks) {
  asINT32 all_solver_phase_mask = phase_masks >> (N_SOLVERS * STP_N_PHASES);
  all_solver_phase_mask &= ~(STP_V2S_PHASE_MASK);
  return all_solver_phase_mask;
}
// Sliding mesh dynamics, delta mass, etc, - group level stuff  moved here from physics/lrf_surfel_dyn.cc

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
static
VOID mlrf_surfel_s2s_v2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control, SURFEL surfel, ACTIVE_SOLVER_MASK active_solver_mask)
{
  // CLEAR & S2S
  ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? surfel_process_control->m_even_active_solver_mask : surfel_process_control->m_active_solver_mask;
  surfel_clear_and_s2s_flow<IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(surfel_process_control, surfel, NULL, s2s_active_solver_mask);
  // This can happen for odd mlrf surfels that only interact with coarse
  // voxels and coarse voxels only do v2s on even time steps.
  if (surfel->is_odd()) {
    if (surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
      sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
      surfel->pre_advect_init_copy_even_to_odd(surfel_process_control->m_active_solver_mask, even_surfel);  //no effect and no need on conduction surfels
    }
  } else if(surfel->is_even() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
    msg_internal_error("Even MLRF flow surfel %d has clone with no v2s weights", surfel->id());
  }
  ACTIVE_SOLVER_MASK even_odd_v2s_active_solver_mask = surfel->is_even() ? surfel_process_control->m_even_active_solver_mask :
                                                       (surfel_process_control->m_even_active_solver_mask | surfel_process_control->m_odd_active_solver_mask);
  // V2S (simplified version of surfel_v2s / even_odd_surfel_v2s used for regular surfels)
  if (surfel_process_control->is_any_flow_solver_active()) {
    if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
      surfel_v2s_advect<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, TRUE, IS_ALL_SOLVER_TS_SAME>
          (surfel, surfel_process_control->m_full_phase_masks, even_odd_v2s_active_solver_mask,
          0, surfel_process_control->m_prior_solver_index_mask, NULL);

    } else  { // even/odd surfel or interacting with VR
      if ((surfel->m_vr_phase_mask & compute_all_solver_v2s_phase_masks(surfel_process_control->m_solver_phase_masks)) != 0) {


        surfel_v2s_advect<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, TRUE, IS_ALL_SOLVER_TS_SAME>
            (surfel, surfel_process_control->m_solver_phase_masks, even_odd_v2s_active_solver_mask,
            surfel_process_control->m_allowed_v2s_scale_diff, surfel_process_control->m_prior_solver_index_mask, NULL);
      }
    }
  }
}

static
VOID mlrf_surfel_s2s_v2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control, SURFEL surfel, ACTIVE_SOLVER_MASK active_solver_mask)
{
  // CLEAR & S2S
  ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? surfel_process_control->m_even_active_solver_mask : surfel_process_control->m_active_solver_mask;
  surfel_clear_and_s2s_conduction(surfel_process_control, surfel, s2s_active_solver_mask);
  // This can happen for odd mlrf surfels that only interact with coarse
  // voxels and coarse voxels only do v2s on even time steps.
  if (surfel->is_odd()) {
    if (surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
      sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
      surfel->pre_advect_init_copy_even_to_odd(surfel_process_control->m_active_solver_mask, even_surfel);  //no effect and no need on conduction surfels
    }
  } else if(surfel->is_even() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
    msg_internal_error("Even MLRF cond surfel %d has clone with no v2s weights", surfel->id());
  }

  // V2S
  if (surfel_process_control->is_conduction_active() && surfel->is_conduction_surface()) { //conduction open shells do not participate in v2s
    if (!surfel->is_even_or_odd()) {
      if (!surfel->interacts_with_vr_ublks()) {
        asINT32 allowed_v2s_phase_mask = FULL_PHASE_MASK & (~(STP_S2V_PHASE_MASK));
        conduction_surfel_v2s_sample<TRUE, TRUE>(surfel, allowed_v2s_phase_mask, active_solver_mask,
            0, surfel_process_control->m_prior_solver_index_mask);
      } else {
        asINT32 allowed_v2s_phase_mask = !surfel_process_control->is_conduction_time_step_odd()
	                                       ? STP_EVEN_V2S_PHASE_MASK
                                         : STP_ODD_V2S_PHASE_MASK;
        conduction_surfel_v2s_sample<FALSE, TRUE>(surfel, allowed_v2s_phase_mask, active_solver_mask, 
						surfel_process_control->m_allowed_v2s_scale_diff, surfel_process_control->m_prior_solver_index_mask);
      }
    } else { // even/odd surfel
      asINT32 allowed_v2s_phase_mask = !surfel_process_control->is_conduction_time_step_odd()
                                       ? STP_EVEN_V2S_PHASE_MASK
                                       : STP_ODD_V2S_PHASE_MASK;
      ACTIVE_SOLVER_MASK active_solver_mask_to_use = !surfel->is_odd() 
	                                                   ? surfel_process_control->m_even_active_solver_mask 
                                                     : (surfel_process_control->m_even_active_solver_mask | surfel_process_control->m_odd_active_solver_mask);
      if (is_conduction_active_in_mask(active_solver_mask_to_use)) {
        conduction_surfel_v2s_sample<FALSE, TRUE>(surfel, allowed_v2s_phase_mask, active_solver_mask_to_use,
            surfel_process_control->m_allowed_v2s_scale_diff, surfel_process_control->m_prior_solver_index_mask);
      }
    }
  }
}

template<>
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sMLRF_SURFEL_GROUP::reset_s2s_v2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{

  WITH_TIMER(SP_MLRF_S2S_V2S_TIMER) {
    DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
      if(!tagged_surfel.is_surfel_weightless()) {
        SURFEL surfel = tagged_surfel.mlrf_surfel();
        if (surfel->is_odd() && !surfel_process_control->is_time_step_odd())
          continue; // odd groups are skipped on even timesteps, so this should never happen
        ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
        mlrf_surfel_s2s_v2s_flow<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>(surfel_process_control, surfel, active_solver_mask);
        // odd surfel goes immediately after even surfel for caching benefit
        if (surfel->is_even()) {
          SURFEL odd_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
          mlrf_surfel_s2s_v2s_flow<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>(surfel_process_control, odd_surfel, active_solver_mask);
        }
      }
    }
  }
}

template<>
VOID sMLRF_SURFEL_GROUP::reset_s2s_v2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{
  WITH_TIMER(SP_MLRF_S2S_V2S_TIMER) {
    DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
      if(!tagged_surfel.is_surfel_weightless()) {
        SURFEL surfel = tagged_surfel.mlrf_surfel();
        if (surfel->is_odd() && !surfel_process_control->is_time_step_odd())
          continue; // odd groups are skipped on even timesteps, so this should never happen
        ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
        mlrf_surfel_s2s_v2s_conduction(surfel_process_control, surfel, active_solver_mask);
        //no need for conduction odd surfel because it copies all contents from the even surfel
      }
    }
  }
}

#if BUILD_GPU

template<>
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::reset_s2s_v2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{
  if constexpr(IS_LB_ACTIVE && !IS_T_S_LB_SOLVER_ON && IS_ALL_SOLVER_TS_SAME) {
    WITH_TIMER(SP_MLRF_S2S_V2S_TIMER) {

      size_t range[2];
      GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, m_even_odd, m_lrf_physics_desc);
      GPU::mlrf_reset_s2s_v2s_flow<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON>(surfel_process_control, range);

      if (m_even_odd == STP_PROCESS_ON_EVEN_TIMES) {
        GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, STP_PROCESS_ON_ODD_TIMES, m_lrf_physics_desc);
        GPU::mlrf_reset_s2s_v2s_flow<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON>(surfel_process_control, range);
      }
    }
  } else {
    msg_internal_error("This version of GPU MLRF reset_s2s_v2s_flow not yet supported");
  }
}

template<>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::reset_s2s_v2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{
  msg_internal_error("This version of GPU MLRF reset_s2s_v2s_cond not yet supported");
}
#endif

static
VOID do_mlrf_reset_s2s_v2s(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE

  BOOLEAN is_T_S_lb_scalar_solver_on =
    surfel_process_control->is_temp_active() && sim.is_T_S_solver_type_lb();
  
  BOOLEAN is_uds_lb_solver_on = surfel_process_control->is_uds_active() && (sim.uds_solver_type == LB_UDS);
  
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group, surfel_process_control->m_scale) {
    BOOLEAN group_even_and_odd = (mlrf_group->m_even_odd == STP_PROCESS_ON_ALL_TIMES);
    BOOLEAN group_only_odd = (mlrf_group->m_even_odd == STP_PROCESS_ON_ODD_TIMES);
    BOOLEAN group_only_even = (mlrf_group->m_even_odd == STP_PROCESS_ON_EVEN_TIMES);
    ACTIVE_SOLVER_MASK active_solver_mask = mlrf_group->set_active_solver_mask(surfel_process_control);
    BOOLEAN is_lb_active = active_solver_mask & LB_ACTIVE;
    BOOLEAN is_temp_active = active_solver_mask & T_PDE_ACTIVE;
    
    if (mlrf_group->is_timestep_valid(surfel_process_control->is_time_step_odd(), surfel_process_control->is_time_step_even())) {
      if (mlrf_group->m_realm == STP_FLOW_REALM) {
        if(g_timescale.m_all_solvers_have_same_timestep) {
          if (!is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, FALSE, TRUE, TRUE>(surfel_process_control);
          } else if (!is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, FALSE, FALSE, TRUE>(surfel_process_control);
          } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, TRUE, TRUE, TRUE>(surfel_process_control);
          } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, TRUE, FALSE, TRUE>(surfel_process_control);
          } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, FALSE, TRUE, TRUE>(surfel_process_control);
          } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, FALSE, FALSE, TRUE>(surfel_process_control);
          } else if(is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, TRUE, TRUE, TRUE>(surfel_process_control);
          } else if(is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, TRUE, FALSE, TRUE>(surfel_process_control);
          }
        }
        else {
          if (!is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, FALSE, TRUE, FALSE>(surfel_process_control);
          } else if (!is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, FALSE, FALSE, FALSE>(surfel_process_control);
          } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, TRUE, TRUE, FALSE>(surfel_process_control);
          } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<FALSE, TRUE, FALSE, FALSE>(surfel_process_control);
          } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, FALSE, TRUE, FALSE>(surfel_process_control);
          } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, FALSE, FALSE, FALSE>(surfel_process_control);
          } else if(is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, TRUE, TRUE, FALSE>(surfel_process_control);
          } else if(is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
            mlrf_group->reset_s2s_v2s_flow<TRUE, TRUE, FALSE, FALSE>(surfel_process_control);
          } 
        }
      } else { //mlrf_group->m_realm == STP_COND_REALM
        mlrf_group->reset_s2s_v2s_conduction(surfel_process_control);
      }
    }
  }
#endif
}

template<>
VOID sMLRF_SURFEL_GROUP::dynamics_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control, SOLVER_INDEX_MASK time_step_index_mask, BOOLEAN is_seed)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  if(is_seed)
    active_solver_mask = surfel_process_control->m_active_solver_mask;
  BOOLEAN is_lb_active = active_solver_mask & LB_ACTIVE;
  BOOLEAN is_temp_active = active_solver_mask & T_PDE_ACTIVE;
  BOOLEAN is_turb_active = active_solver_mask & KE_PDE_ACTIVE;
  BOOLEAN is_uds_active = active_solver_mask & UDS_PDE_ACTIVE;

  asINT32 group_scale = m_scale;
  asINT32 n_surfels = n_quantums();
  dFLOAT one_over_delta_t = 1.0F / scale_to_delta_t(m_scale);

  WITH_ITEM_TIMER(SP_MLRF_SURFEL_DYN_TIMER, n_surfels) {
    TAGGED_MLRF_SURFEL *mlrf_surfels = &this->quantums()[0];
    LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
    dFLOAT angle_rotated = lrf->angle_rotated;

    ccDOTIMES(nth_ring, n_rings()) {

      MLRF_RING current_ring = ring(nth_ring);
      MLRF_RING_SEGMENT segments = current_ring->ring_segments();
      asINT32 n_surfels_in_ring  = current_ring->m_n_surfels_per_frame_in_ring;
      asINT32 n_surfels_per_frame_on_proc = current_ring->m_n_surfels_per_frame_on_proc;
      asINT32 highest_segment_number = current_ring->n_ring_segments() - 1;
      asINT32 n_ring_segments_on_proc = current_ring->n_ring_segments_on_proc();
      asINT32 *ring_segments_on_proc = current_ring->ring_segments_on_proc();
      asINT32 n_depots_per_frame = n_surfels_per_frame_on_proc + n_ring_segments_on_proc;
      MLRF_PRE_DYNAMICS_DEPOT exterior_depots = current_ring->m_pre_dynamics_recv_buffer[MLRF_EXTERNAL];
      MLRF_PRE_DYNAMICS_DEPOT interior_depots = current_ring->m_pre_dynamics_recv_buffer[MLRF_INTERNAL];

      //EXTERIOR
      asINT32 nth_ring_segment_on_proc = 0;
      asINT32 segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
      asINT32 n_surfels_in_segment = (segment_number < highest_segment_number)
                               ? segments[segment_number + 1].offset - segments[segment_number].offset
                               : n_surfels_in_ring - segments[segment_number].offset;
      asINT32 nth_surfel_in_segment = 0;

      dFLOAT surfel_overlapping_ratio[2];
      calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, TRUE, surfel_overlapping_ratio);

      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        mlrf_surfel_dynamics_flow(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                  mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                  exterior_depots, lrf, group_scale,
                                  surfel_overlapping_ratio, one_over_delta_t,
                                  TRUE, is_lb_active, is_turb_active, is_temp_active, is_uds_active,
                                  nth_surfel, n_surfels_per_frame_on_proc, nth_ring,
                                  current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL], time_step_index_mask, is_seed);
        nth_surfel_in_segment++;
        if(nth_surfel_in_segment == n_surfels_in_segment) {
          nth_ring_segment_on_proc++;
          exterior_depots += 2;
          if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
            break;
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;
        }
        else {
          exterior_depots++;
        }
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;

      //INTERIOR
      nth_ring_segment_on_proc = 0;
      segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
      n_surfels_in_segment = (segment_number < highest_segment_number)
        ? segments[segment_number + 1].offset - segments[segment_number].offset
        : n_surfels_in_ring - segments[segment_number].offset;
      nth_surfel_in_segment = 0;

      calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, FALSE, surfel_overlapping_ratio);
      
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        mlrf_surfel_dynamics_flow(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                  mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                  interior_depots, lrf, group_scale,
                                  surfel_overlapping_ratio, one_over_delta_t,
                                  FALSE, is_lb_active, is_turb_active, is_temp_active, is_uds_active, 
                                  nth_surfel, n_surfels_per_frame_on_proc, nth_ring,
                                  current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL], time_step_index_mask, is_seed);

        nth_surfel_in_segment++;
        if (nth_surfel_in_segment == n_surfels_in_segment) {
          nth_ring_segment_on_proc++;
          interior_depots += 2;
          if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
            break;
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;
        }
        else
          interior_depots++;
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;
    }
  }
#endif
}

template<>
VOID sMLRF_SURFEL_GROUP::dynamics_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control, BOOLEAN is_seed)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  if(is_seed)
    active_solver_mask = surfel_process_control->m_active_solver_mask;
  BOOLEAN is_conduction_active = active_solver_mask & CONDUCTION_PDE_ACTIVE;
  
  asINT32 group_scale = m_scale;
  asINT32 n_surfels = n_quantums();
  
  WITH_ITEM_TIMER(SP_MLRF_SURFEL_DYN_TIMER, n_surfels) {
    TAGGED_MLRF_SURFEL *mlrf_surfels = &this->quantums()[0];
    LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
    dFLOAT angle_rotated = lrf->angle_rotated;

    ccDOTIMES(nth_ring, n_rings()) {

      MLRF_RING current_ring = ring(nth_ring);
      MLRF_RING_SEGMENT segments = current_ring->ring_segments();
      asINT32 n_surfels_in_ring  = current_ring->m_n_surfels_per_frame_in_ring;
      asINT32 n_surfels_per_frame_on_proc = current_ring->m_n_surfels_per_frame_on_proc;
      asINT32 highest_segment_number = current_ring->n_ring_segments() - 1;
      asINT32 n_ring_segments_on_proc = current_ring->n_ring_segments_on_proc();
      asINT32 *ring_segments_on_proc = current_ring->ring_segments_on_proc();
      asINT32 n_depots_per_frame = n_surfels_per_frame_on_proc + n_ring_segments_on_proc;
      MLRF_PRE_DYNAMICS_DEPOT exterior_depots = current_ring->m_pre_dynamics_recv_buffer[MLRF_EXTERNAL];
      MLRF_PRE_DYNAMICS_DEPOT interior_depots = current_ring->m_pre_dynamics_recv_buffer[MLRF_INTERNAL];

      //EXTERIOR
      asINT32 nth_ring_segment_on_proc = 0;
      asINT32 segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
      asINT32 n_surfels_in_segment = (segment_number < highest_segment_number)
                                     ? segments[segment_number + 1].offset - segments[segment_number].offset
                                     : n_surfels_in_ring - segments[segment_number].offset;
      asINT32 nth_surfel_in_segment = 0;

      dFLOAT surfel_overlapping_ratio[2];
      calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, TRUE, surfel_overlapping_ratio);

      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        mlrf_surfel_dynamics_conduction(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                        mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                        exterior_depots, group_scale, surfel_overlapping_ratio, TRUE, 
                                        is_conduction_active, nth_ring, is_seed);
        nth_surfel_in_segment++;
        if(nth_surfel_in_segment == n_surfels_in_segment) {
          nth_ring_segment_on_proc++;
          exterior_depots += 2;
          if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
            break;
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;
        }
        else {
          exterior_depots++;
        }
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;

      //INTERIOR
      nth_ring_segment_on_proc = 0;
      segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
      n_surfels_in_segment = (segment_number < highest_segment_number)
                             ? segments[segment_number + 1].offset - segments[segment_number].offset
                             : n_surfels_in_ring - segments[segment_number].offset;
      nth_surfel_in_segment = 0;

      calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, FALSE, surfel_overlapping_ratio);
      
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        mlrf_surfel_dynamics_conduction(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                        mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                        interior_depots, group_scale, surfel_overlapping_ratio, FALSE, 
                                        is_conduction_active, nth_ring, is_seed);
        nth_surfel_in_segment++;
        if (nth_surfel_in_segment == n_surfels_in_segment) {
          nth_ring_segment_on_proc++;
          interior_depots += 2;
          if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
            break;
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;
        }
        else
          interior_depots++;
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;
    }
  }
#endif
}

#if BUILD_GPU

template<>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::dynamics_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                                              SOLVER_INDEX_MASK time_step_index_mask,
                                                              BOOLEAN is_seed)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  if(is_seed) {
    active_solver_mask = surfel_process_control->m_active_solver_mask;
  }

  size_t range[2];
  GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, m_even_odd, m_lrf_physics_desc);
  GPU::do_mlrf_surfel_dynamics_flow(surfel_process_control,
                                    active_solver_mask,
                                    time_step_index_mask,
                                    m_lrf_physics_desc,
                                    is_seed, m_scale,
                                    this, range);
#endif
}

template<>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::dynamics_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                                                    BOOLEAN is_seed)
{
  printf("Implementation of dynamics_conduction is not available in GPU simulations\n");
  assert(false);
}
#endif

static VOID do_mlrf_surfel_dynamics(sSURFEL_PROCESS_CONTROL *surfel_process_control)
{

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group, surfel_process_control->m_scale) {
    BOOLEAN group_even_and_odd = (mlrf_group->m_even_odd == STP_PROCESS_ON_ALL_TIMES);
    BOOLEAN is_time_step_odd = surfel_process_control->is_time_step_odd();

    asINT32 coarse_time_step_index_mask = surfel_process_control->m_coarse_time_step_index_mask;
    SOLVER_INDEX_MASK time_step_index_mask =  group_even_and_odd ? surfel_process_control->m_time_step_index_mask : coarse_time_step_index_mask;

    if (mlrf_group->is_timestep_valid(surfel_process_control->is_time_step_odd(), surfel_process_control->is_time_step_even())) {
      if (mlrf_group->m_realm == STP_FLOW_REALM) {
        mlrf_group->dynamics_flow(surfel_process_control, time_step_index_mask);
      } else { //mlrf_group->m_realm == STP_COND_REALM
        mlrf_group->dynamics_conduction(surfel_process_control);
      }
    }
  }
#endif
} 

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
static VOID mlrf_surfel_s2v_advect_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                        TAGGED_MLRF_SURFEL mlrf_surfel,
                                        ACTIVE_SOLVER_MASK active_solver_mask)
{
  SURFEL surfel = mlrf_surfel.mlrf_surfel();
  ACTIVE_SOLVER_MASK even_odd_s2v_active_solver_mask = surfel->is_odd() ? surfel_process_control->m_odd_active_solver_mask :
                                                       (surfel_process_control->m_even_active_solver_mask | surfel_process_control->m_odd_active_solver_mask);

  uINT32 allowed_S2V_scale_diff = surfel_process_control->m_allowed_s2v_scale_diff;
	asINT32 allowed_S2V_phase_mask = surfel_process_control->m_solver_phase_masks;
	asINT32 full_phase_mask = surfel_process_control->m_full_phase_masks;
	SOLVER_INDEX_MASK prior_solver_index_mask = surfel_process_control->m_prior_solver_index_mask;
  if (!mlrf_surfel.is_surfel_weightless() && surfel_process_control->is_any_flow_solver_active()) {
    if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
      uINT32 allowed_scale_diff = 0;
      surfel_s2v_advect<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, TRUE, IS_ALL_SOLVER_TS_SAME>
          (surfel, full_phase_mask, active_solver_mask, allowed_scale_diff, prior_solver_index_mask, NULL);
    } else { // even/odd surfel or interacting with VR
      if ((surfel->m_vr_phase_mask & compute_all_solver_s2v_phase_masks(allowed_S2V_phase_mask)) != 0) {
        surfel_s2v_advect<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, TRUE, IS_ALL_SOLVER_TS_SAME>
            (surfel, allowed_S2V_phase_mask, even_odd_s2v_active_solver_mask, allowed_S2V_scale_diff, prior_solver_index_mask, NULL);
      }
    }
    // even surfel goes immediately after odd surfel for caching benefit
    if (surfel->is_odd()) {
      SURFEL even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
      if ((even_surfel->m_vr_phase_mask & compute_all_solver_s2v_phase_masks(allowed_S2V_phase_mask)) != 0) {
        surfel_s2v_advect<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, TRUE, IS_ALL_SOLVER_TS_SAME>
            (even_surfel, allowed_S2V_phase_mask, even_odd_s2v_active_solver_mask, allowed_S2V_scale_diff, prior_solver_index_mask, NULL);
      }
    }
  }
}

static VOID mlrf_surfel_s2v_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                       TAGGED_MLRF_SURFEL mlrf_surfel,
                                       ACTIVE_SOLVER_MASK active_solver_mask)
{
  SURFEL surfel = mlrf_surfel.mlrf_surfel();
  uINT32 allowed_S2V_scale_diff = surfel_process_control->m_allowed_s2v_scale_diff;
  SOLVER_INDEX_MASK prior_solver_index_mask = surfel_process_control->m_prior_solver_index_mask;
  if (!mlrf_surfel.is_surfel_weightless() && surfel_process_control->is_conduction_active() && surfel->is_conduction_surface()) {
    if (!surfel->is_even_or_odd()) {
      if (!surfel->interacts_with_vr_ublks()) {
        uINT32 allowed_scale_diff = 0;
        asINT32 allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
        conduction_surfel_s2v_distribute<TRUE, TRUE>(surfel,
                                                     allowed_phase_mask, active_solver_mask, allowed_scale_diff,
                                                     prior_solver_index_mask);
      } else {
        asINT32 allowed_phase_mask = !surfel_process_control->is_conduction_time_step_odd()
                                     ? STP_EVEN_S2V_PHASE_MASK
                                     : STP_ODD_S2V_PHASE_MASK;
        conduction_surfel_s2v_distribute<FALSE, TRUE>(surfel,
                                                      allowed_phase_mask, active_solver_mask, allowed_S2V_scale_diff,
                                                      prior_solver_index_mask);
      }
    } else { // even/odd conduction surfel
	    asINT32 allowed_phase_mask = !surfel_process_control->is_conduction_time_step_odd()
                                   ? STP_EVEN_S2V_PHASE_MASK
                                   : STP_ODD_S2V_PHASE_MASK;
      ACTIVE_SOLVER_MASK active_solver_mask_to_use = !surfel->is_odd() 
                                                     ? (surfel_process_control->m_even_active_solver_mask | surfel_process_control->m_odd_active_solver_mask)
                                                     : surfel_process_control->m_odd_active_solver_mask;
      if (is_conduction_active_in_mask(active_solver_mask_to_use)) {
        conduction_surfel_s2v_distribute<FALSE, TRUE>(surfel,
                                                      allowed_phase_mask, 
                                                      active_solver_mask_to_use,
                                                      allowed_S2V_scale_diff, 
                                                      prior_solver_index_mask);
      }
    }
  }
}

void maybe_copy_outflux(TAGGED_MLRF_SURFEL mlrf_surfel,
                        SOLVER_INDEX_MASK out_flux_index_mask,
                        BOOLEAN is_temp_active,
                        BOOLEAN is_uds_active);

// Delta mass correction functions
template<>
VOID sMLRF_SURFEL_GROUP::surfel_dyn_delta_mass(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                               SOLVER_INDEX_MASK out_flux_index_mask, BOOLEAN is_seed)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  if(is_seed)
    active_solver_mask = surfel_process_control->m_active_solver_mask;
  BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ;
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE);
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE);
  BOOLEAN is_turb_active = (active_solver_mask & KE_PDE_ACTIVE);
  BOOLEAN is_T_S_lb_scalar_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);
  BOOLEAN is_lb_entropy_solver_on = is_temp_active && (sim.T_solver_type == LB_ENTROPY);
  BOOLEAN is_lb_energy_solver_on = is_temp_active && (sim.T_solver_type == LB_ENERGY);
  BOOLEAN is_pf_solver_on = is_uds_active  && sim.is_pf_model;

  if (is_lb_active
#if !BUILD_5X_SOLVER
    || is_lb_entropy_solver_on
    || is_lb_energy_solver_on
#endif
      ) {
    asINT32 n_surfels = n_quantums();

    // WARNING: The MLRF delta mass timer is not accurate for cases in which a VR boundary intersects
    // the sliding mesh region. This leads to circumstances in which MLRF surfels are counted but not
    // processed. In addition, there is only one count, the overall surfel count, associated with the timer,
    // even though one of the phases being timed is S2V, which we usually regard as being dependent on
    // 4 counts.

    WITH_ITEM_TIMER(SP_MLRF_SURFEL_DMASS_TIMER, n_surfels) {
      TAGGED_MLRF_SURFEL *mlrf_surfels = &this->quantums()[0];      
      LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
      dFLOAT angle_rotated = lrf->angle_rotated;

      ccDOTIMES(nth_ring, n_rings()) {
        MLRF_RING current_ring = ring(nth_ring);
        MLRF_RING_SEGMENT segments = current_ring->ring_segments();
        asINT32 n_surfels_in_ring = current_ring->m_n_surfels_per_frame_in_ring;
        asINT32 n_surfels_per_frame_on_proc = current_ring->m_n_surfels_per_frame_on_proc;
        asINT32 highest_segment_number = current_ring->n_ring_segments() - 1;
        asINT32 n_ring_segments_on_proc = current_ring->n_ring_segments_on_proc();
        asINT32 *ring_segments_on_proc = current_ring->ring_segments_on_proc();
        asINT32 n_depots_per_frame = n_surfels_per_frame_on_proc + n_ring_segments_on_proc;
        MLRF_POST_DYNAMICS_DEPOT exterior_depots = current_ring->m_post_dynamics_recv_buffer[MLRF_EXTERNAL];
        MLRF_POST_DYNAMICS_DEPOT interior_depots = current_ring->m_post_dynamics_recv_buffer[MLRF_INTERNAL];

        asINT32 nth_ring_segment_on_proc = 0;
        asINT32 segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
        asINT32 n_surfels_in_segment = (segment_number < highest_segment_number)
          ? segments[segment_number + 1].offset - segments[segment_number].offset
          : n_surfels_in_ring - segments[segment_number].offset;
        asINT32 nth_surfel_in_segment = 0;

        {
          dFLOAT surfel_overlapping_ratio[2];
          calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, TRUE, surfel_overlapping_ratio);

          ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
#if BUILD_D39_LATTICE
            dFLOAT my_delta_mass[N_NONZERO_ENERGIES];
            ccDOTIMES(j, N_NONZERO_ENERGIES) {
              my_delta_mass[j] = current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL][nth_surfel].delta_mass[j];
            }
#else
            dFLOAT my_delta_mass = current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL][nth_surfel].delta_mass;
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
	    dFLOAT my_delta_energy = current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL][nth_surfel].delta_energy;
#endif
	    
#if BUILD_D19_LATTICE
	    dFLOAT pf_delta_mass[2] = {0.0};            
            if(is_pf_solver_on){
              pf_delta_mass[0] = current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL][nth_surfel].pf_delta_mass[0];
              pf_delta_mass[1] = current_ring->m_post_dynamics_send_buffer[MLRF_EXTERNAL][nth_surfel].pf_delta_mass[1];              
            }
#endif

            mlrf_surfel_dyn_delta_mass(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                       mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                       exterior_depots,
                                       surfel_overlapping_ratio, my_delta_mass,
#if BUILD_D19_LATTICE
				       pf_delta_mass,
#endif
                                       TRUE,
#if !BUILD_5X_SOLVER
				       my_delta_energy,
                                       is_lb_active, is_lb_entropy_solver_on, 
				       is_lb_energy_solver_on, is_temp_active, is_uds_active,
#if BUILD_D39_LATTICE
                                       lrf->has_transonic_flow,
#endif                                 
#endif
                                       nth_surfel, nth_ring);
            maybe_copy_outflux(mlrf_surfels[nth_surfel], out_flux_index_mask, is_temp_active, is_uds_active);
            nth_surfel_in_segment++;
            if(nth_surfel_in_segment == n_surfels_in_segment) {
              nth_ring_segment_on_proc++;
              exterior_depots += 2;
              if(nth_ring_segment_on_proc == n_ring_segments_on_proc)
                break;
              segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
              n_surfels_in_segment = (segment_number < highest_segment_number)
                ? segments[segment_number + 1].offset - segments[segment_number].offset
                : n_surfels_in_ring - segments[segment_number].offset;
              nth_surfel_in_segment = 0;
            }
            else {
              exterior_depots++;
            }
          }
          mlrf_surfels += n_surfels_per_frame_on_proc;

          nth_ring_segment_on_proc = 0;
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;

          calculate_mlrf_rotating_surfel_position(angle_rotated, n_surfels_in_ring, FALSE, surfel_overlapping_ratio);

          ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
#if BUILD_D39_LATTICE
            dFLOAT my_delta_mass[N_NONZERO_ENERGIES];
            ccDOTIMES(j, N_NONZERO_ENERGIES) {
              my_delta_mass[j] = current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL][nth_surfel].delta_mass[j];
            }
#else
            dFLOAT my_delta_mass = current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL][nth_surfel].delta_mass;
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
	    dFLOAT my_delta_energy = current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL][nth_surfel].delta_energy;
#endif
	    
#if BUILD_D19_LATTICE
	    dFLOAT pf_delta_mass[2] = {0.0};            
            if(is_pf_solver_on){
              pf_delta_mass[0] = current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL][nth_surfel].pf_delta_mass[0];
              pf_delta_mass[1] = current_ring->m_post_dynamics_send_buffer[MLRF_INTERNAL][nth_surfel].pf_delta_mass[1];              
            }
#endif
            mlrf_surfel_dyn_delta_mass(mlrf_surfels[nth_surfel].mlrf_surfel(),
                                       mlrf_surfels[nth_surfel].is_surfel_weightless(),
                                       interior_depots,
                                       surfel_overlapping_ratio, my_delta_mass,
#if BUILD_D19_LATTICE
				       pf_delta_mass, 
#endif
                                       FALSE, 
#if !BUILD_5X_SOLVER
				       my_delta_energy,
				       is_lb_active, is_lb_entropy_solver_on, 
				       is_lb_energy_solver_on, is_temp_active, is_uds_active,
#if BUILD_D39_LATTICE
                                       lrf->has_transonic_flow,
#endif                                 
#endif
                                       nth_surfel, nth_ring);

            maybe_copy_outflux(mlrf_surfels[nth_surfel], out_flux_index_mask, is_temp_active, is_uds_active);
            nth_surfel_in_segment++;
            if (nth_surfel_in_segment == n_surfels_in_segment) {
              nth_ring_segment_on_proc++;
              interior_depots += 2;
              if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
                break;
              segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
              n_surfels_in_segment = (segment_number < highest_segment_number)
                ? segments[segment_number + 1].offset - segments[segment_number].offset
                : n_surfels_in_ring - segments[segment_number].offset;
              nth_surfel_in_segment = 0;
            }
            else
              interior_depots++;
          }
          mlrf_surfels -= n_surfels_per_frame_on_proc;
        }
        mlrf_surfels += 2 * n_surfels_per_frame_on_proc;
      }
    }
  }
#endif
}

#if BUILD_GPU

template<>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::surfel_dyn_delta_mass(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                                                      SOLVER_INDEX_MASK out_flux_index_mask, BOOLEAN is_seed)
{
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  if(is_seed)
    active_solver_mask = surfel_process_control->m_active_solver_mask;

  size_t range[2];
  GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, m_even_odd, m_lrf_physics_desc);
  GPU::mlrf_surfel_dyn_delta_mass(surfel_process_control,
                                  active_solver_mask,
                                  out_flux_index_mask,
                                  m_lrf_physics_desc,
                                  is_seed, m_scale,
                                  this, range);
}
#endif

template<>
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sMLRF_SURFEL_GROUP::s2v_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control) {
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  WITH_TIMER(SP_MLRF_S2V_TIMER) {
    DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
	    mlrf_surfel_s2v_advect_flow<IS_LB_ACTIVE, IS_T_S_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>
          (surfel_process_control, tagged_surfel, active_solver_mask);
	  }
  }
#endif
}

template<>
VOID sMLRF_SURFEL_GROUP::s2v_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control) {
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  WITH_TIMER(SP_MLRF_S2V_TIMER) {
    DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
	    mlrf_surfel_s2v_conduction(surfel_process_control, tagged_surfel, active_solver_mask);
	  }
  }
#endif
}

#if BUILD_GPU
template<>
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::s2v_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control) {
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  // ACTIVE_SOLVER_MASK active_solver_mask = set_active_solver_mask(surfel_process_control);
  WITH_TIMER(SP_MLRF_S2V_TIMER) {
    if constexpr (IS_LB_ACTIVE && !IS_T_S_LB_SOLVER_ON && IS_ALL_SOLVER_TS_SAME) {
      size_t range[2];
      GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, m_even_odd, m_lrf_physics_desc);
      GPU::mlrf_surfel_s2v_advect_flow<TRUE, FALSE, TRUE>(surfel_process_control,
                                                          range, m_even_odd,
                                                          m_lrf_physics_desc);
      if (m_even_odd == STP_PROCESS_ON_ODD_TIMES) {
        GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, STP_PROCESS_ON_EVEN_TIMES, m_lrf_physics_desc);
        GPU::mlrf_surfel_s2v_advect_flow<TRUE, FALSE, TRUE>(surfel_process_control,
                                                            range, STP_PROCESS_ON_EVEN_TIMES,
                                                            m_lrf_physics_desc);
      }
	  } else {
      msg_internal_error("Unsupported MLRF S2V template version");
	  }
  }
#endif
}

template<>
VOID tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>::s2v_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control) {
  printf("Implementation of s2v_conduction is not available in GPU simulations\n");
  assert(false);
}
#endif

static VOID emit_lrf_parcels(SCALE scale) {
  DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
    //Only meaningful for flow mlrf surfels
    if (group->m_realm == STP_FLOW_REALM) {
      group->emit_lrf_parcels();
    }
  }
}

static VOID do_mlrf_delta_mass_correction_and_s2v(sSURFEL_PROCESS_CONTROL *surfel_process_control, BOOLEAN do_s2v)
{  
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE

  BOOLEAN is_time_step_odd = surfel_process_control->is_time_step_odd();
  BOOLEAN is_time_step_even = surfel_process_control->is_time_step_even();

  DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(mlrf_group, surfel_process_control->m_scale) {
    if (mlrf_group->is_timestep_valid(is_time_step_odd, is_time_step_even)) {
      BOOLEAN group_even_and_odd = (mlrf_group->m_even_odd == STP_PROCESS_ON_ALL_TIMES);

      SOLVER_INDEX_MASK coarse_time_step_index_mask = surfel_process_control->m_coarse_time_step_index_mask;
      SOLVER_INDEX_MASK time_step_index_mask = group_even_and_odd ? surfel_process_control->m_time_step_index_mask : coarse_time_step_index_mask;
      //delta mass correction only meaningful for flow surfels across which mass can move
      if (mlrf_group->m_realm == STP_FLOW_REALM) {
        mlrf_group->surfel_dyn_delta_mass(surfel_process_control, time_step_index_mask);
      }

      if (do_s2v) {
        if (mlrf_group->m_realm == STP_FLOW_REALM) {
          ACTIVE_SOLVER_MASK active_solver_mask = mlrf_group->set_active_solver_mask(surfel_process_control);
          BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
          BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
          BOOLEAN is_T_S_lb_scalar_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
          BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
          BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);
          if(g_timescale.m_all_solvers_have_same_timestep) {
            if (!is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, FALSE, TRUE, TRUE>(surfel_process_control);
            } else if (!is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, FALSE, FALSE, TRUE>(surfel_process_control);
            } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, TRUE, TRUE, TRUE>(surfel_process_control);
            } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, TRUE, FALSE, TRUE>(surfel_process_control);
            } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, FALSE, TRUE, TRUE>(surfel_process_control);
            } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, FALSE, FALSE, TRUE>(surfel_process_control);
            } else if(is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, TRUE, TRUE, TRUE>(surfel_process_control);
            } else if(is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, TRUE, FALSE, TRUE>(surfel_process_control);
            }
          } else {
            if (!is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, FALSE, TRUE, FALSE>(surfel_process_control);
            } else if (!is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, FALSE, FALSE, FALSE>(surfel_process_control);
            } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, TRUE, TRUE, FALSE>(surfel_process_control);
            } else if(!is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<FALSE, TRUE, FALSE, FALSE>(surfel_process_control);
            } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, FALSE, TRUE, FALSE>(surfel_process_control);
            } else if(is_lb_active && !is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, FALSE, FALSE, FALSE>(surfel_process_control);
            } else if(is_lb_active && is_T_S_lb_scalar_solver_on && is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, TRUE, TRUE, FALSE>(surfel_process_control);
            } else if(is_lb_active && is_T_S_lb_scalar_solver_on && !is_uds_lb_solver_on) {
              mlrf_group->s2v_flow<TRUE, TRUE, FALSE, FALSE>(surfel_process_control);
            } 
          }
        } else { //mlrf_group->m_realm == STP_COND_REALM
          mlrf_group->s2v_conduction(surfel_process_control);
        }
      }
    }
  }
  g_strand_mgr.add_surfel_groups_of_scale_to_send_queue(surfel_process_control->m_scale,
                                                        surfel_process_control->m_active_solver_mask);
#endif
}



// For seeding
template<>
VOID sMLRF_SURFEL_GROUP::pre_seed_init(BOOLEAN is_full_checkpoint_restore,
                                       SOLVER_INDEX_MASK seed_solver_index_mask)
{
  ACTIVE_SOLVER_MASK active_solver_mask = sim.init_solver_mask;
  BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_turb_active = (active_solver_mask & KE_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
  DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
    if(tagged_surfel.is_surfel_weightless())
      continue;
    SURFEL surfel = tagged_surfel.mlrf_surfel();

    if (!is_full_checkpoint_restore) {
      if (is_lb_active) {
        surfel->v2s_lb_data()->reset();
      }
      if (is_turb_active) {
        surfel->v2s_turb_data()->reset();
        surfel->turb_data()->s_mag = 0.0;
        surfel->turb_data()->gamma_swirl = 0.0;
      }
      if (is_temp_active) {
        surfel->v2s_t_data()->reset();
        surfel->t_data()->temp_sample = 0.0;
      }
      if (is_uds_active) {
        ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  surfel->v2s_uds_data(nth_uds)->reset();
          surfel->uds_data(nth_uds)->uds_value = 0.0;
	}
#if BUILD_D19_LATTICE
	if(sim.is_pf_model){
          surfel->v2s_pf_data()->reset();
        }
#endif
      }
      surfel_v2s_advect_for_seed(surfel, active_solver_mask, NULL, seed_solver_index_mask);
    }
     BOOLEAN is_high_mach_surfel = FALSE;
#if BUILD_D39_LATTICE
    if (sim.use_hybrid_ts_hs_solver) {
      asINT32 ref_frame_index = surfel->ref_frame_index();
      LRF_PHYSICS_DESCRIPTOR lrf = ref_frame_index >= 0 ? &sim.lrf_physics_descs[ref_frame_index] : NULL;
      is_high_mach_surfel = (lrf != NULL && lrf->has_transonic_flow);
    } else {
      is_high_mach_surfel = TRUE;
    }
#endif      
    pre_seed_init_surfel(surfel, is_full_checkpoint_restore, is_high_mach_surfel, NULL, TRUE);
  }
}

#if BUILD_GPU
template<>
VOID sMLRF_MSFL_GROUP::pre_seed_init(BOOLEAN is_full_checkpoint_restore,
                                     SOLVER_INDEX_MASK seed_solver_index_mask)
{
  size_t range[2];
  GPU::set_mlrf_msfl_group_range_for_scale(range, m_scale, m_even_odd, m_lrf_physics_desc);
  GPU::seed_mlrf_surfels(range, sim.init_solver_mask,
                         seed_solver_index_mask, is_full_checkpoint_restore);
  
  //this->print_depot_data_for_ring(102);
}
#endif

#if !BUILD_5G_LATTICE
template<>
VOID sMLRF_SURFEL_GROUP::seed_T_scalar_solver() {
  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(m_scale);
  asINT32 prior_T_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 next_T_index = prior_T_index ^ 1;
  DO_MLRF_SURFEL_GROUP_SURFELS(tagged_surfel, this) {
    if(tagged_surfel.is_surfel_weightless())
      continue;
    SURFEL surfel = tagged_surfel.mlrf_surfel();
    seed_surfel_out_flux_t(surfel);
  }
}
#endif

// Sliding surfels strands

VOID sSLIDING_SURFELS_A_STRAND::run() {
  if (is_sliding_mesh_present()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
      strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_surfel_process_control.init(scale);
      do_mlrf_reset_s2s_v2s(&m_surfel_process_control);
      g_strand_mgr.update_consumer_counts(scale);
    }
    g_strand_mgr.m_mlrf_comm.request_pre_dyn_sends();
    g_sync_threads.wake_up_comm_thread();
  }
  g_strand_mgr.complete_timestep();
}

VOID sSLIDING_SURFELS_B_STRAND::run() {
  if (is_sliding_mesh_present()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    if(strand_coarsest_active_scale == (STP_MAX_SCALES + 1))
      strand_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_surfel_process_control.init(scale);
      do_mlrf_surfel_dynamics(&m_surfel_process_control);
    }
    g_strand_mgr.m_mlrf_comm.request_post_dyn_sends();
    g_sync_threads.wake_up_comm_thread();
  }
  g_strand_mgr.complete_timestep();
}

VOID sSLIDING_SURFELS_C_STRAND::run() {
  if (is_sliding_mesh_present()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_surfel_process_control.init(scale);
      do_mlrf_delta_mass_correction_and_s2v(&m_surfel_process_control, TRUE);
      if(sim.is_particle_model) {
#if !BUILD_GPU        
        WITH_TIMER(SP_PARTICLE_MLRF_EMISSION_TIMER) {
          emit_lrf_parcels(scale);
        }
#else
        msg_error("Particle modelling is not supported with LRFs on GPUs.");
#endif        
      }
    }
  }
  g_strand_mgr.complete_timestep();
}

VOID sSLIDING_NEARBLKS_A_STRAND::run() {
  if (is_sliding_mesh_present()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.advect_all_vr_fine_ublks(m_index, VRFINE_SLIDING_NEARBLK_GROUP_TYPE);
      m_ublk_process_control.process_all_ublks(m_index, m_ublk_group_type);
    }
  }
  g_strand_mgr.complete_timestep();
}

VOID sSLIDING_NEARBLKS_B_STRAND::run() {
  if (is_sliding_mesh_present() && sim.is_mme_comm_needed()) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.process_all_ublks_dyn(m_index, m_ublk_group_type);
    }
  }
  g_strand_mgr.complete_timestep();
}

#if BUILD_5G_LATTICE
VOID sSLIDING_NEARBLKS_C_STRAND::run() {
  if (is_sliding_mesh_present() && sim.is_large_pore) {
    SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      m_ublk_process_control.init(scale);
      m_ublk_process_control.construct_all_ublks_adv_states(m_index, m_ublk_group_type);
    }
  }
  g_strand_mgr.complete_timestep();
}
#endif
