#ifndef PARCEL_LIST_H
#define PARCEL_LIST_H


typedef struct sPARCEL_STATE *PARCEL_STATE;
#if 1

#include <list>

//list of parcel pointers built from a stl list
typedef class sPARCEL_LIST : public std::list<PARCEL_STATE>{
 private:
  std::list<PARCEL_STATE>::iterator list_pos; 
 public:
  VOID reset() {
    list_pos = begin();
  }
  BOOLEAN exhausted() {
    return( list_pos == end() );
  }
  PARCEL_STATE data() {
    return(*list_pos);
  }
  VOID remove() {
    list_pos = erase(list_pos);
  }
  VOID add(PARCEL_STATE parcel) {
    push_front(parcel);
    reset();
  }

  VOID add_to_end(PARCEL_STATE parcel) {
    push_back(parcel);
    reset();
  }

  VOID next() {
    list_pos++;
  }

 }*PARCEL_LIST;


#else
//this is an alternate implementation based on jamies pseudocode listed below

template <typename LIST_ELEMENT_DATA>
struct tLIST_NODE
{
  tLIST_NODE *next;
  LIST_ELEMENT_DATA data;
};

template <typename LIST_ELEMENT_DATA> 
struct tLIST:
{

  typedef tLIST_NODE<LIST_ELEMENT_DATA> sNODE, *NODE;
  NODE m_head;

  VOID remove(NODE prev, NODE current)
  {
    if (prev == NULL)
      m_head = current->next;
    else
      prev->next = current->next;
    push_node_on_free_list(current); // nodes are allocated a block at a time
  }

  NODE head() { return m_head; };

  NODE push(LIST_ELEMENT_DATA &data)   // push node on front of list
  {
    
  }
};

#define DO_LIST_X(prev, current, LIST_TYPE, list)           \
  for (LIST_TYPE::NODE current = list->head(), prev = NULL; \
       current != NULL;                                     \
       prev = current, current = current->next)

#define DO_LIST(node, LIST_TYPE, list)
   DO_LIST_X(___(prev), node, LIST_TYPE, list)


#endif



//==========
//this code was provided by Jamie in an email from august 2011 and is listed here for reference.
#if 0



template <typename LIST_ELEMENT_DATA>
struct tLIST_NODE {
  tLIST_NODE *next;
  LIST_ELEMENT_DATA data; // note that this is not a pointer to LIST_ELEMENT_DATA - more general
};

template <typename LIST_ELEMENT_DATA>
struct tLIST {

  typedef tLIST_NODE<LIST_ELEMENT_DATA> sNODE, *NODE;
  NODE m_head;

  VOID remove(NODE prev, NODE current)
  {
    if (prev == NULL)
      m_head = current->next;
    else
      prev->next = current->next;
    push_node_on_free_list(current); // nodes are allocated a block at a time
  }
  NODE head() { return m_head; };
  NODE push(LIST_ELEMENT_DATA &data);   // push node on front of list
};


// Concrete list types can define their own DO_XXX_LIST macros as wrappers on DO_LIST
// so that end users don't always have to pass in LIST_TYPE.
//
// To remove a node from the list while inside DO_LIST_X, use this sequence:
//
//   remove(prev, current);
//   current = prev;


#define DO_LIST_X(prev, current, LIST_TYPE, list)           \
  for (LIST_TYPE::NODE current = list->head(), prev = NULL; \
       current != NULL;                                     \
       prev = current, current = current->next)

#define DO_LIST(node, LIST_TYPE, list)
   DO_LIST_X(___(prev), node, LIST_TYPE, list)

#endif

//Note the comment about pooled storage for nodes. 
//I suspect there is a big performance will[sic] if we reduce the number of times we hit the heap.


#endif
