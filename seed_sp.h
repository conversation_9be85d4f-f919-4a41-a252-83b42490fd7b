/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk and surfel seeding
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SEED_H
#define _SIMENG_SEED_H

#include "common_sp.h"
#include "eqns.h"
#include "ublk.h"
#include <unordered_set>

VOID fluid_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
			     cSTRING cdi_desc_var_name, vFLOAT value,
			     vFLOAT aux_value,	// min or max
			     STP_GEOM_VARIABLE surfel_centroid[3]);

struct tSEED_VAR_SPEC_BASE {

  static std::vector<std::string> fluid_seed_var_names;
  static std::vector<std::string> boundary_seed_var_names;

};

// This is a simplified version of SEED_VAR_SPEC on CP
template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS>
struct tSEED_VAR_SPEC : tSEED_VAR_SPEC_BASE {
  sINT32 n_vars;
  //REALM seed_realm;
  sINT32 seed_controller;
  //sINT8 seed_scaling_type;
  //dFLOAT seed_data_mks_vel_scaling;
  //sINT8 seed_sim_was_incompressible;
  //dFLOAT seed_data_char_density;
  //dFLOAT seed_data_char_vel;
  //dFLOAT seed_data_char_temp;
  //dFLOAT lattice_gas_constant;
  //cBOOLEAN seed_via_dimless_properties; // pressure and vel
  //cBOOLEAN seed_via_mks;
  //asINT32 *active_seed_var_index; //Index in active seed variables
  SEED_VAR_TYPE *seed_var_types;       // seed_var_types[n_vars]
  cBOOLEAN is_var_seeded[N_SEED_VARS]; // is_var_seeded[DGF_N_SEED_VARS]
  //UDS
  sINT32 n_uds_vars;
  sINT16 *seed_uds_var_indices;
  cBOOLEAN is_uds_var_seeded[MAX_N_USER_DEFINED_SCALARS][DGF_N_SEED_UDS_VARS];

  // These are only for rotating seed velocity and are specified by the user
  // using the option -seed_rotate_vel "<coord_system>,<x|y|z>,<angle>"
  // Currently, we do NOT support region-by-region AOA seeding simulations,
  // although it could be implemented if desired...
  asINT32   csys_index;
  asINT32   axis;       // rotates about x, y, or z axis of csys csys_index
  sdFLOAT   rotation_angle;
  sG3_XFORM rotation_xform;

  tSEED_VAR_SPEC(sSEED_CONTROL &seed_control, uINT8 n_spec_vars, uINT8 n_uds_vars_) {
    n_vars = n_spec_vars;
    n_uds_vars = n_uds_vars_;

    if (n_spec_vars > 0) {
      seed_var_types = cnew SEED_VAR_TYPE[n_vars];
    } else {
      seed_var_types = nullptr;
    }

    memset(is_var_seeded, 0, sizeof(is_var_seeded)); 

    if (n_uds_vars > 0) {
      seed_uds_var_indices = cnew sINT16[n_uds_vars];
    } else {
      seed_uds_var_indices = NULL;
    }

    memset(is_uds_var_seeded, 0, sizeof(is_uds_var_seeded));
     
    csys_index = -1;
    axis = -1;
    rotation_angle = -1;
    rotation_xform = g3_xform_make_unit(); // initialized

    if constexpr (std::is_same<SEED_VAR_TYPE, DGF_SEED_VAR_TYPE>::value) {
      csys_index = seed_control.seed_rotate_vel_csys_index;
      axis = seed_control.seed_rotate_vel_axis;
      rotation_angle = seed_control.seed_rotate_vel_angle;

      // calculate the xform matrix to rotate seed velocity
      sdFLOAT rotation_angle_radians = rotation_angle * (PI/180.0);
      sG3_XFORM axis_xform;
      switch(axis)
      {
        case 0:
          axis_xform = g3_xform_make_x_rotate(rotation_angle_radians);
          break;
        case 1:
          axis_xform = g3_xform_make_y_rotate(rotation_angle_radians);
          break;
        case 2:
          axis_xform = g3_xform_make_z_rotate(rotation_angle_radians);
          break;
        default:
          msg_error("Invalid rotation axis. Choose x, y or z.");
      }
      if (is_csys_index_non_default(csys_index)) {
        CSYS csys = &sim.csys_table[csys_index];
        rotation_xform = g3_xform_composition(csys->g_to_l_xform, g3_xform_composition(axis_xform, csys->l_to_g_xform));
      }
      else {
        rotation_xform = axis_xform;
      }
    }
  }

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const tSEED_VAR_SPEC<SEED_VAR_TYPE, N_SEED_VARS> *h_ptr,
                                      size_t n_seed_specs);

  VOID print(std::ostream &stream) const {
    stream << "SEED_VAR_SPEC" << std::endl;
    stream << "  n_vars: " << n_vars << std::endl;
    stream << "  seed_controller: " << seed_controller << std::endl;
    stream << "  seed_var_types: " << std::endl;
    for (int n = 0; n < n_vars; n++) {
      stream << fluid_seed_var_names[seed_var_types[n]] << ": " << seed_var_types[n] << "(is_var_seeded: " << (is_var_seeded[n] ? "true" : "false") << ")" << std::endl;
    }
  }
};

typedef tSEED_VAR_SPEC< DGF_SEED_VAR_TYPE, DGF_N_SEED_VARS >           
        sFLUID_SEED_VAR_SPEC,    *FLUID_SEED_VAR_SPEC;
typedef tSEED_VAR_SPEC< DGF_BOUNDARY_SEED_VAR_TYPE, DGF_N_BOUNDARY_SEED_VARS >  
        sBOUNDARY_SEED_VAR_SPEC, *BOUNDARY_SEED_VAR_SPEC;

extern std::vector<sFLUID_SEED_VAR_SPEC>    g_fluid_seed_var_specs;    // fluid region seed var specs
extern std::vector<sBOUNDARY_SEED_VAR_SPEC> g_boundary_seed_var_specs; // boundary face seed var specs 

namespace GPU {
  extern __DEVICE__ sFLUID_SEED_VAR_SPEC*    g_fluid_seed_var_specs;    // fluid region seed var specs
  extern __DEVICE__ sBOUNDARY_SEED_VAR_SPEC* g_boundary_seed_var_specs; // boundary face seed var specs  
}

__HOST__DEVICE__ INLINE
auto& get_fluid_seed_var_spec(asINT32 spec_index) { return HD_NAMESPACE::g_fluid_seed_var_specs[spec_index]; }

__HOST__DEVICE__ INLINE
auto& get_boundary_seed_var_spec(asINT32 spec_index) { return HD_NAMESPACE::g_boundary_seed_var_specs[spec_index]; }

typedef struct sVOXEL_SEED_DATA {
  STP_PHYS_VARIABLE density;
  STP_PHYS_VARIABLE velocity[3];
  STP_PHYS_VARIABLE lb_temp;               // Lattice Boltzman temperature
  STP_PHYS_VARIABLE density_pfld;
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE density_mc;
  STP_PHYS_VARIABLE grad_p[3];
  STP_PHYS_VARIABLE potential[MAXIMUM_NUM_COMPONENTS];
  STP_PHYS_VARIABLE dynamic_scalar_multiplier;
  STP_PHYS_VARIABLE porosity;
  STP_PHYS_VARIABLE pm_mode_flag;    //large_pore
  STP_PHYS_VARIABLE rock_type_index; //large pore with mp_pm_rable_table = TRUE
  STP_PHYS_VARIABLE contact_angle;   //large pore && g_is_multi_component
  STP_PHYS_VARIABLE wall_potential[MAXIMUM_NUM_COMPONENTS];   //large pore && g_is_multi_component
#elif BUILD_D19_LATTICE
  STP_PHYS_VARIABLE ustar_lb;    
  STP_PHYS_VARIABLE coarsen_region_identifier; 
  STP_PHYS_VARIABLE density_frozen;
  STP_PHYS_VARIABLE vel_frozen[3];
  STP_PHYS_VARIABLE grad_p[3];
  STP_PHYS_VARIABLE pressure_from_laplace;
#endif  
  STP_PHYS_VARIABLE water_vapor_mass_fraction;  //only serve water vapor solver
  STP_PHYS_VARIABLE relative_humidity;          //only serve water vapor solver 
  STP_PHYS_VARIABLE passive_scalar_temp;
  STP_PHYS_VARIABLE uds_value[MAX_N_USER_DEFINED_SCALARS];  //LB_UDS
} *VOXEL_SEED_DATA;

typedef struct sVOXEL_LES_SEED_DATA : public sVOXEL_SEED_DATA {
  STP_PHYS_VARIABLE turb_kinetic_energy;
  STP_PHYS_VARIABLE turb_dissipation;
  STP_PHYS_VARIABLE stress_ten_mag;
} *VOXEL_LES_SEED_DATA;

typedef struct sVOXEL_CONDUCTION_SEED_DATA {
  STP_PHYS_VARIABLE density;
  STP_PHYS_VARIABLE passive_scalar_temp;
} *VOXEL_CONDUCTION_SEED_DATA;

typedef struct sUBLK_LES_SEED_DATA {
  STP_VOXEL_MASK  empty_mask;
  STP_VOXEL_MASK  unseeded_mask; /* which voxels have yet to provide seed data */
  LRF_PHYSICS_DESCRIPTOR lrf_physics_desc;
  sVOXEL_LES_SEED_DATA voxel_seed_data[8];
} *UBLK_LES_SEED_DATA;

typedef struct sFLUID_UDS_INITIAL_CONDITIONS {
  sPHYSICS_VARIABLE m_uds_value;

  VOID check_consistency(asINT32 n_initial_conditions,CDI_PHYS_TYPE_DESCRIPTOR ptd) {
    if (n_initial_conditions > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uptd = find_uds_physics(ptd->cdi_physics_type);
      if (uptd->initial_condition_var[0]->id != CDI_VAR_ID_UDS_FLUID_INITIAL_CONDITION)
	msg_internal_error("FLUID_UDS_INITIAL_CONDITIONS is not consistent with CDI UDS physics type %d.", uptd->uds_physics_type);
    }
  }

  static constexpr asINT32 num_of_vars() {
    return sizeof(sFLUID_UDS_INITIAL_CONDITIONS) / sizeof(sPHYSICS_VARIABLE);
  }

  __HOST__DEVICE__ dFLOAT& uds_value() {return m_uds_value.value;}
    
} *FLUID_UDS_INITIAL_CONDITIONS;

typedef struct sFLUID_INITIAL_CONDITIONS {
#if BUILD_5G_LATTICE
  sPHYSICS_VARIABLE m_component_ratio_type;
  sPHYSICS_VARIABLE m_comp0_density;
  sPHYSICS_VARIABLE m_comp1_density;
  sPHYSICS_VARIABLE m_pressure;
  sPHYSICS_VARIABLE m_total_density;
  sPHYSICS_VARIABLE m_comp0_vol_frac;
  sPHYSICS_VARIABLE m_comp1_vol_frac;
  sPHYSICS_VARIABLE m_phase_index;
#else
  sPHYSICS_VARIABLE m_pressure;
#endif
  sPHYSICS_VARIABLE m_velocity[3];
  sPHYSICS_VARIABLE m_temperature;
#if !BUILD_5G_LATTICE    
  sPHYSICS_VARIABLE m_water_mass_fraction;  //only serve water vapor solver (PDE_UDS)
  sPHYSICS_VARIABLE m_relative_humidity;    //only serve water vapor solver (PDE_UDS)
#endif

  VOID check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd) {
#if BUILD_5G_LATTICE
    if ((ptd->initial_condition_var[0]->id != CDI_VAR_ID_COMP_RATIO_TYPE)
	|| (ptd->initial_condition_var[1]->id != CDI_VAR_ID_COMP0_DENSITY)
	|| (ptd->initial_condition_var[2]->id != CDI_VAR_ID_COMP1_DENSITY)
	|| (ptd->initial_condition_var[3]->id != CDI_VAR_ID_NEW_PRESSURE)
	|| (ptd->initial_condition_var[4]->id != CDI_VAR_ID_DENSITY_T0)
	|| (ptd->initial_condition_var[5]->id != CDI_VAR_ID_COMP0_VOL_FRAC)
	|| (ptd->initial_condition_var[6]->id != CDI_VAR_ID_COMP1_VOL_FRAC)
	|| (ptd->initial_condition_var[7]->id != CDI_VAR_ID_STATE_OF_MATTER)
        || (ptd->initial_condition_var[8]->id != CDI_VAR_ID_VEL_X)
        || (ptd->initial_condition_var[9]->id != CDI_VAR_ID_VEL_Y)
        || (ptd->initial_condition_var[10]->id != CDI_VAR_ID_VEL_Z)
        || (ptd->initial_condition_var[11]->id != CDI_VAR_ID_TEMP))
      msg_internal_error("FLUID_INITIAL_CONDITIONS is not consistent with CDI physics type %d.", ptd->cdi_physics_type);
#else
    if ((ptd->initial_condition_var[0]->id != CDI_VAR_ID_NEW_PRESSURE)
        || (ptd->initial_condition_var[1]->id != CDI_VAR_ID_VEL_X)
        || (ptd->initial_condition_var[2]->id != CDI_VAR_ID_VEL_Y)
        || (ptd->initial_condition_var[3]->id != CDI_VAR_ID_VEL_Z)
        || (ptd->initial_condition_var[4]->id != CDI_VAR_ID_TEMP)
	|| (ptd->initial_condition_var[5]->id != CDI_VAR_ID_WATER_MASS_FRACTION)
	|| (ptd->initial_condition_var[6]->id != CDI_VAR_ID_RELATIVE_HUMIDITY))
      msg_internal_error("FLUID_INITIAL_CONDITIONS is not consistent with CDI physics type %d.", ptd->cdi_physics_type);
#endif
  }

  asINT32 num_of_vars() {
    return sizeof(this) / sizeof(sPHYSICS_VARIABLE);
  }

#if BUILD_5G_LATTICE
  __HOST__DEVICE__ dFLOAT& component_ratio_type() { return m_component_ratio_type.value; }
  __HOST__DEVICE__ dFLOAT& comp0_density() { return m_comp0_density.value; }
  __HOST__DEVICE__ dFLOAT& comp1_density() { return m_comp1_density.value; }
  __HOST__DEVICE__ dFLOAT& total_density() { return m_total_density.value; }
  __HOST__DEVICE__ dFLOAT& comp0_vol_frac() { return m_comp0_vol_frac.value; }
  __HOST__DEVICE__ dFLOAT& comp1_vol_frac() { return m_comp1_vol_frac.value; }
  __HOST__DEVICE__ dFLOAT& phase_index() { return m_phase_index.value; }
#endif
  __HOST__DEVICE__ dFLOAT& pressure() { return m_pressure.value; }
  __HOST__DEVICE__ dFLOAT& velocity(int index) { return m_velocity[index].value; }
  __HOST__DEVICE__ dFLOAT& temperature() { return m_temperature.value; }

#if !BUILD_5G_LATTICE    
  __HOST__DEVICE__ dFLOAT& water_mass_fraction() { return m_water_mass_fraction.value; }
  __HOST__DEVICE__ dFLOAT& relative_humidity() { return m_relative_humidity.value; }
#endif
  
} *FLUID_INITIAL_CONDITIONS;

typedef struct sLES_FLUID_INITIAL_CONDITIONS : sFLUID_INITIAL_CONDITIONS {
  sPHYSICS_VARIABLE m_turb_profile_via;   // ICs specified either as intensity/length scale or k and epsilon
  sPHYSICS_VARIABLE m_turb_intensity;
  sPHYSICS_VARIABLE m_turb_length_scale;
  sPHYSICS_VARIABLE m_turb_kinetic_energy;
  sPHYSICS_VARIABLE m_turb_dissipation;

  __HOST__DEVICE__ dFLOAT& turb_profile_via() { return m_turb_profile_via.value; }
  __HOST__DEVICE__ dFLOAT& turb_intensity() { return m_turb_intensity.value; }
  __HOST__DEVICE__ dFLOAT& turb_length_scale() { return m_turb_length_scale.value; }
  __HOST__DEVICE__ dFLOAT& turb_kinetic_energy() { return m_turb_kinetic_energy.value; }
  __HOST__DEVICE__ dFLOAT& turb_dissipation() { return m_turb_dissipation.value; }
  
  VOID check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd) {
    sFLUID_INITIAL_CONDITIONS::check_consistency(ptd);
    asINT32 index = sFLUID_INITIAL_CONDITIONS::num_of_vars();
    if ((ptd->initial_condition_var[index++]->id != CDI_VAR_ID_TURB_VIA)
        || (ptd->initial_condition_var[index++]->id != CDI_VAR_ID_TURB_INTENSITY)
        || (ptd->initial_condition_var[index++]->id != CDI_VAR_ID_TURB_LENGTH_SCALE)
        || (ptd->initial_condition_var[index++]->id != CDI_VAR_ID_TURB_KE)
        || (ptd->initial_condition_var[index++]->id != CDI_VAR_ID_TURB_DISSIPATION))
      msg_internal_error("LES_FLUID_INITIAL_CONDITIONS is not consistent with CDI physics type %d.",
                         ptd->cdi_physics_type);
  }

  static constexpr asINT32 num_of_vars() {
    return sizeof(sLES_FLUID_INITIAL_CONDITIONS) / sizeof(sPHYSICS_VARIABLE);
  }  
} *LES_FLUID_INITIAL_CONDITIONS;

/*==============================================================================
 * @class LES_FLUID_INITIAL_CONDITIONS_VALUES
 * With the advent of the GPU simulator, les ic values, especially those that are
 * space or time varying cannot be stored in the physics descriptor since it is
 * a global object shared between UBLKs. Use this as a store hold of IC values
 *============================================================================*/
class LES_FLUID_INITIAL_CONDITIONS_VALUES {
  
private:  
  // This must match the order in sFLUID_INITIAL_CONDITIONS and sLES_FLUID_INITIAL_CONDITIONS
  enum eLES_VARIABLE : int {
#if BUILD_5G_LATTICE
    COMPONENT_RATIO_TYPE,
    COMP0_DENSITY,
    COMP1_DENSITY,
    PRESSURE,
    TOTAL_DENSITY,
    COMP0_VOL_FRAC,
    COMP1_VOL_FRAC,
    PHASE_INDEX,
#else
    PRESSURE,
#endif
    VELOCITY_X,
    VELOCITY_Y,
    VELOCITY_Z,
    TEMPERATURE,
#if !BUILD_5G_LATTICE
    WATER_MASS_FRACTION,
    RELATIVE_HUMIDITY,    
#endif    
    TURB_PROFILE_VIA,
    TURB_INTENSITY,
    TURB_LENGTH_SCALE,
    TURB_KINETIC_ENERGY,
    TURB_DISSIPATION,
    N_VARS
  };

  static_assert(LES_FLUID_INITIAL_CONDITIONS_VALUES::N_VARS == sLES_FLUID_INITIAL_CONDITIONS::num_of_vars(),
                "The variable layout of host and device LES conditions must be identical");

  sPHYSICS_DESCRIPTOR* m_pd;
  dFLOAT m_vars[N_VARS];

  //LB_UDS
  enum eUDS_VARIABLE : int {
     UDS_VALUE,
     N_UDS_VARS
  };
  static_assert(LES_FLUID_INITIAL_CONDITIONS_VALUES::N_UDS_VARS == sFLUID_UDS_INITIAL_CONDITIONS::num_of_vars(),
                "The variable layout of host and device UDS initial conditions must be identical");
  dFLOAT m_uds_vars[MAX_N_USER_DEFINED_SCALARS][N_UDS_VARS];
  
  
public:

  __HOST__DEVICE__ dFLOAT pressure() const             { return m_vars[PRESSURE]; }
  __HOST__DEVICE__ dFLOAT velocity(int index) const    { return m_vars[VELOCITY_X + index]; }
  __HOST__DEVICE__ dFLOAT& velocity(int index)         { return m_vars[VELOCITY_X + index]; }
  __HOST__DEVICE__ dFLOAT temperature() const          { return m_vars[TEMPERATURE]; }
  __HOST__DEVICE__ dFLOAT turb_profile_via() const     { return m_vars[TURB_PROFILE_VIA]; }
  __HOST__DEVICE__ dFLOAT turb_intensity() const       { return m_vars[TURB_INTENSITY]; }
  __HOST__DEVICE__ dFLOAT turb_length_scale() const    { return m_vars[TURB_LENGTH_SCALE]; }
  __HOST__DEVICE__ dFLOAT turb_kinetic_energy() const  { return m_vars[TURB_KINETIC_ENERGY]; }
  __HOST__DEVICE__ dFLOAT turb_dissipation() const     { return m_vars[TURB_DISSIPATION]; }
#if BUILD_5G_LATTICE
  __HOST__DEVICE__ dFLOAT component_ratio_type() const { return m_vars[COMPONENT_RATIO_TYPE]; }
  __HOST__DEVICE__ dFLOAT comp0_density() const        { return m_vars[COMP0_DENSITY]; }
  __HOST__DEVICE__ dFLOAT comp1_density() const        { return m_vars[COMP1_DENSITY]; }
  __HOST__DEVICE__ dFLOAT total_density() const        { return m_vars[TOTAL_DENSITY]; }
  __HOST__DEVICE__ dFLOAT comp0_vol_frac() const       { return m_vars[COMP0_VOL_FRAC]; }
  __HOST__DEVICE__ dFLOAT comp1_vol_frac() const       { return m_vars[COMP1_VOL_FRAC]; }
  __HOST__DEVICE__ dFLOAT phase_index() const          { return m_vars[PHASE_INDEX]; }
#else
  __HOST__DEVICE__ dFLOAT water_mass_fraction() const  { return m_vars[WATER_MASS_FRACTION]; }
  __HOST__DEVICE__ dFLOAT relative_humidity() const    { return m_vars[RELATIVE_HUMIDITY]; }  
#endif

  __HOST__DEVICE__ dFLOAT uds_value(asINT32 nth_uds) const {return m_uds_vars[nth_uds][UDS_VALUE]; }

  __DEVICE__ VOID read_exprlang_device_data(int group_id,
                                            int voxor);
  
  __HOST__DEVICE__ dFLOAT get_pd_variable(sLES_FLUID_INITIAL_CONDITIONS* f,
                                          eLES_VARIABLE var) {

    switch(var) {
      case PRESSURE: return f->pressure();
      case VELOCITY_X: return f->velocity(0);
      case VELOCITY_Y: return f->velocity(1);
      case VELOCITY_Z: return f->velocity(2);      
      case TEMPERATURE: return f->temperature();
#if !BUILD_5G_LATTICE      
      case WATER_MASS_FRACTION: return f->water_mass_fraction();
      case RELATIVE_HUMIDITY: return f->relative_humidity();
#endif      
      case TURB_PROFILE_VIA: return f->turb_profile_via();
      case TURB_INTENSITY: return f->turb_intensity();
      case TURB_LENGTH_SCALE: return f->turb_length_scale();
      case TURB_KINETIC_ENERGY: return f->turb_kinetic_energy();
      case TURB_DISSIPATION: return f->turb_dissipation();
#if BUILD_5G_LATTICE
      case COMPONENT_RATIO_TYPE: return f->component_ratio_type();
      case COMP0_DENSITY: return f->comp0_density();
      case COMP1_DENSITY: return f->comp1_density();
      case TOTAL_DENSITY: return f->total_density();
      case COMP0_VOL_FRAC: return f->comp0_vol_frac();
      case COMP1_VOL_FRAC: return f->comp1_vol_frac();
      case PHASE_INDEX: return f->phase_index();
#endif      
      default: { assert(false); return -1; }
    }
  }

   __HOST__DEVICE__ dFLOAT get_pd_uds_variable(sFLUID_UDS_INITIAL_CONDITIONS* f,
					       eUDS_VARIABLE var)					       
  {
     switch(var) {
       case UDS_VALUE: return f->uds_value();
       default: { assert(false); return -1; }
     }     
   }
  
  __HOST__DEVICE__ VOID copy_vars_from_pd() {
    auto ic = (LES_FLUID_INITIAL_CONDITIONS) m_pd->initial_conditions();

    if (m_pd->n_initial_conditions > N_VARS) {
#if DEVICE_COMPILATION_MODE
      assert(m_pd->n_initial_conditions <= N_VARS);
#else
      msg_internal_error("Unable to seed initial conditions");
#endif
    }

    for (int i = 0; i < m_pd->n_initial_conditions; i++) {
      m_vars[i] = get_pd_variable(ic, (eLES_VARIABLE) i);
    }

    if (m_pd->n_uds_initial_conditions > 0) {
      const auto& simc = get_simc_ref();
      FLUID_UDS_INITIAL_CONDITIONS uic = (FLUID_UDS_INITIAL_CONDITIONS)m_pd->uds_initial_conditions();
      for (int nth_uds = 0; nth_uds < simc.n_user_defined_scalars; nth_uds++)  {
	for (int j = 0; j < N_UDS_VARS; j++)
	  m_uds_vars[nth_uds][j] = get_pd_uds_variable(uic,  (eUDS_VARIABLE) j);

	uic++;
      }
    }
  }

  __HOST__DEVICE__ VOID read_exprlang_data(STP_GEOM_VARIABLE voxel_centroid[3]) {
#if DEVICE_COMPILATION_MODE
    auto voxor = get_dyn_voxor();
    this->read_exprlang_device_data(blockIdx.x, voxor);
#else
    m_pd->eval_initial_condition_program(voxel_centroid, NULL, fluid_eqn_error_handler);  //include uds initial conditions
    copy_vars_from_pd();
#endif
  }

    __HOST__DEVICE__ LES_FLUID_INITIAL_CONDITIONS_VALUES(sPHYSICS_DESCRIPTOR* pd): m_pd(pd) {    
      copy_vars_from_pd();
  }

};

typedef struct sSOURCE_PARAMETERS : public sFLUID_INITIAL_CONDITIONS {
} *SOURCE_PARAMETERS;

typedef struct sSOURCE_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {

  // INITIAL_CONDITIONS is a copy of PARAMETERS
  SOURCE_PARAMETERS parameters() { return (SOURCE_PARAMETERS)_parameters; }
  SOURCE_PARAMETERS initial_conditions() { return (SOURCE_PARAMETERS)_initial_conditions; }

  VOID check_consistency() {
    parameters()->check_consistency(phys_type_desc);
  }

} *SOURCE_PHYSICS_DESCRIPTOR;

typedef struct sCONDUCTION_INITIAL_CONDITIONS {
  sPHYSICS_VARIABLE temperature;

  asINT32 num_of_vars() {
    return sizeof(this) / sizeof(sPHYSICS_VARIABLE);
  }

  //TODO: Implement check consistency
  VOID check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd) {
  }

} *CONDUCTION_INITIAL_CONDITIONS;


__HOST__DEVICE__
VOID convert_fluid_initial_conditions_to_seed_data(LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_conditions,
                                                   VOXEL_LES_SEED_DATA seed_data,
                                                   asINT32 csys_index,
                                                   BOOLEAN is_turb,
                                                   asINT32 vel_ref_frame_index,
                                                   asINT32 voxel_ref_frame_index,
                                                   STP_GEOM_VARIABLE point[3],
                                                   asINT32 scale,
                                                   LRF_PHYSICS_DESCRIPTOR lrf);

VOID convert_conduction_initial_conditions_to_seed_data(CONDUCTION_INITIAL_CONDITIONS initial_conditions,
                                                        VOXEL_CONDUCTION_SEED_DATA seed_data,
                                                        asINT32 csys_index,
                                                        asINT32 vel_ref_frame_index,
                                                        asINT32 voxel_ref_frame_index,
                                                        STP_GEOM_VARIABLE point[3],
                                                        asINT32 scale,
                                                        LRF_PHYSICS_DESCRIPTOR lrf);

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID convert_smart_seed_vars_to_seed_data(UBLK_TYPE* ublk,
                                          const asINT32 voxel, 
                                          const LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_conditions,
                                          VOXEL_LES_SEED_DATA voxel_seed_data, 
                                          const uINT8 seed_var_spec_index,
                                          LRF_PHYSICS_DESCRIPTOR lrf);

VOID convert_smart_seed_vars_to_seed_data(const UBLK ublk,
                                          const asINT32 voxel, 
                                          const CONDUCTION_INITIAL_CONDITIONS initial_conditions,
                                          VOXEL_CONDUCTION_SEED_DATA voxel_seed_data, 
                                          const uINT8 seed_var_spec_index,
                                          LRF_PHYSICS_DESCRIPTOR lrf);

VOID compute_curved_axes(UBLK ublk,
                         asINT32 voxel,
                         asINT32 scale,
                         STP_GEOM_VARIABLE curved_x_axis[3],
                         BOOLEAN is_2d,
                         BOOLEAN compute_y_axis_p, 
                         BOOLEAN maybe_issue_warning_p,
                         STP_GEOM_VARIABLE porous_y_axis[3], 
                         STP_GEOM_VARIABLE curved_y_axis[3], 
                         STP_GEOM_VARIABLE curved_z_axis[3]);

VOID convert_voxel_vel_from_curved_coords_to_global_coords(LES_FLUID_INITIAL_CONDITIONS_VALUES& initial_conditions, 
                                                           STP_GEOM_VARIABLE curved_x_axis[3], 
                                                           STP_GEOM_VARIABLE curved_y_axis[3], 
                                                           STP_GEOM_VARIABLE curved_z_axis[3]);

__HOST__DEVICE__
BOOLEAN are_fluid_initial_conditions_constant(LES_FLUID_INITIAL_CONDITIONS initial_conditions,
					      FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions,
                                              BOOLEAN is_turb);

BOOLEAN are_conduction_initial_conditions_constant(CONDUCTION_INITIAL_CONDITIONS initial_conditions);

VOID add_ublk_to_seed_extrapolate_list(UBLK ublk);

VOID seed_dyn_surfel_T_lb_scalar_solver();

extern std::unordered_set<SHOB_ID> g_first_vr_fine_ublks;

class cSEEDER {
protected:
  cSEEDER(TIMESTEP start_time);
  TIMESTEP m_seed_time;
  SOLVER_INDEX_MASK m_seed_prior_solver_index_masks[STP_MAX_SCALES];
public:
  virtual void process() = 0;
};

class cSHOBS_SEEDER : public cSEEDER {
public:
  cSHOBS_SEEDER() : cSEEDER(0) {}
  cSHOBS_SEEDER(TIMESTEP start_time);
  void update_all_time_varying_parameters();
  virtual void seed_all_dynblks();
  void reflect_states_from_dynblk_to_mirror(SCALE scale);
  void reflect_states_from_vrblk_to_mirror(SCALE scale);
  void reflect_mme_solver_data_from_vrblk_to_mirror(SCALE scale);
  virtual void do_dynblk_explode_init(SCALE fine_scale);
  virtual void do_seed_all_surfels(BOOLEAN is_full_checkpoint_restore);
  virtual void process() override;
#if !BUILD_5G_LATTICE
  void copy_temperatures_on_conduction_solid_ghost_voxels();
  void seed_conduction_solid_voxel_gradients();
  VOID seed_shell_conduction_surfel_temperatures();
  void seed_shell_conduction_surfel_gradients();
  VOID copy_shell_conduction_surfel_gradients();
  VOID seed_shell_conduction_surfels();
  VOID seed_s2s_all_conduction_surfels();
  VOID seed_v2s_all_conduction_surfels();
  VOID seed_s2v_all_conduction_surfels();
#endif
  VOID reset_conduction_surfels_accumulated_heat_flux();
};

#if BUILD_GPU
/* For GPU builds we have two SHOB SEEDERs

 * The HOST_SHOBS_SEEDER does a small amount of work to get host side objects ready for copy to device.
 * This includes updating descriptors, accumulating V2S and S2S weights etc.

 * The DEVICE_SHOB_SEEDER does bulk of the actual seeding once SHOBs are availale on the device.
 */
class cHOST_SHOBS_SEEDER : public cSHOBS_SEEDER {
public:
  cHOST_SHOBS_SEEDER(BASETIME start_time) : cSHOBS_SEEDER(start_time){};
  virtual void process() override;
  VOID determine_if_ublks_are_simple();
};

class cDEVICE_SHOBS_SEEDER : public cSHOBS_SEEDER {
public:
  cDEVICE_SHOBS_SEEDER(TIMESTEP start_time) : cSHOBS_SEEDER(start_time){};
  virtual void process() override;
  virtual void seed_all_dynblks() override;
  virtual void do_dynblk_explode_init(SCALE fine_scale) override;
  virtual void do_seed_all_surfels(BOOLEAN is_full_checkpoint_restore) override;  
};

#else //BUILD_GPU
using cHOST_SHOBS_SEEDER = cSHOBS_SEEDER;
#endif //BUILD_GPU

class cCALIBRATION_RUN_SEEDER : public cSHOBS_SEEDER {
private:
  cBOOLEAN m_ublk_comm_ready;
  cBOOLEAN m_ublk_comm_done;
  cBOOLEAN m_surfel_comm_ready;
  cBOOLEAN m_surfel_comm_done;

public:
  cBOOLEAN m_do_calib_seed_comm;
  cCALIBRATION_RUN_SEEDER() {
    m_ublk_comm_ready        = FALSE;
    m_ublk_comm_done         = FALSE;
    m_surfel_comm_ready      = FALSE;
    m_surfel_comm_done       = FALSE;
    m_do_calib_seed_comm     = FALSE;
  }
  virtual void process() override;
  void comm_shobs();
  void wait_for_init_ublk_ready();
  void wait_for_init_ublk_done();
  void wait_for_ublk_comm_ready();
  void wait_for_ublk_comm_done();
  void wait_for_surfel_type_comm_ready();
  void wait_for_surfel_type_comm_done();
  void wait_for_surfel_comm_ready();
  void wait_for_surfel_comm_done();
  void mark_init_ublk_ready();
  void mark_init_ublk_done();
  void mark_ublk_comm_ready();
  void mark_ublk_comm_done();
  void mark_surfel_type_comm_ready();
  void mark_surfel_type_comm_done();
  void mark_surfel_comm_ready();
  void mark_surfel_comm_done();
};

extern cCALIBRATION_RUN_SEEDER g_seed_calibration_iteration;

template<typename UBLK_TYPE>
__HOST__DEVICE__
VOID update_closest_surfel_BC_based_on_smart_seed_markers(UBLK_TYPE* ublk);

template<typename SFL_TYPE_TAG>
__HOST__DEVICE__ VOID copy_surfel_outflux_for_seed(tSURFEL<SFL_TYPE_TAG>* surfel,
                                                   tSURFEL_V2S_DATA<SFL_TYPE_TAG>* surfel_v2s_data);

VOID seed_mlrf_surfels(SOLVER_INDEX_MASK seed_prior_solver_index_mask,
                       STP_SCALE scale,
                       BOOLEAN is_full_checkpoint_restore);
#endif
