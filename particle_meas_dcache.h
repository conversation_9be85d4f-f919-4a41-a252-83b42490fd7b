#ifndef _SIMENG_PARTICLE_COMPONENT_MEAS_H_
#define _SIMENG_PARTICLE_COMPONENT_MEAS_H_

#include <utility>
#include <set>
#include <map>
#include <vector>

#include SRI_H
#include "ublk.h"
#include "particle_solver_data.h"

//An emitter id and material id together define the identity of a
//particle component which can have its various particle modeling
//quantities measured separately.  A particle is of a given component
//when its emitter ID and material ID match that of the component
//(although -1's are used as wildcards for "composite" components).
class sPARTICLE_MEAS_COMPONENT {
 public:
  int m_emitter_index;
  int m_material_index;

  sPARTICLE_MEAS_COMPONENT(int emitter_index, int material_index);
  sPARTICLE_MEAS_COMPONENT(int composite_id);  //Construct a component from the windows component id space which only reads the composited values.

  static int compute_id(int cdi_emitter_id, int material_id);
  static int compute_composite_id(int cdi_emitter_id, int material_id);

  int id() { return compute_id(m_emitter_index, m_material_index); }
  int compoite_id() { return compute_composite_id(m_emitter_index, m_material_index); }
  bool is_composite() { return is_emitter_composite() || is_material_composite(); }
  bool is_global_composite() { return is_emitter_composite() && is_material_composite(); }
  bool is_emitter_composite() { return m_emitter_index == -1; }
  bool is_material_composite() { return m_material_index == -1; }

  bool operator <(const sPARTICLE_MEAS_COMPONENT &other) const {
    if(m_emitter_index < other.m_emitter_index) return true;
    if(m_emitter_index > other.m_emitter_index) return false;
    if(m_material_index < other.m_material_index) return true;
    return false;
  }
};

//Fluid measurements require that all components of all var types for all voxels in a
//ublk be computed in advanced of writing to meas cells.  The following class does that.

template <typename MEAS_DATATYPE, typename ACCUM_DATATYPE, typename UBLK_TYPE>
  class tPARTICLE_MEAS_DCACHE : public sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES {

 public:
  //Use this method to compute and cache the stuff needed to be
  //measured for particle modeling in a given ublk.  This method does
  //the calculation using a single pass through the parcel lists.
  void compute(UBLK_TYPE* ublk, VOXEL_MASK_8 voxel_mask);

  tPARTICLE_MEAS_DCACHE(); //Intended to be constructed on the heap once per sim.
  void setup_for_window(int window_index, bool should_transform_to_grf); //Setup the var index map for the specified window and maybe transform vectors to the grf csys.
  MEAS_DATATYPE* get_voxel_values(int window_var_index, int composite_component_index); //Get an array of all 8 voxel's values for the nth vartype of a window and for the specifed component.
  MEAS_DATATYPE get_voxel_value(int voxel, int window_var_index, int composite_component_index); //Retrieve the value for just one voxel.

 private:

  int m_num_voxels;

  int m_num_emitters;
  int m_num_materials;

  int m_num_components;
  int m_num_composite_components;

  sPARTICLE_MEAS_VARTYPE_BITSET* m_required_meas_variables;
  int m_num_var_types_per_voxel;

  //Storage for the data being cached with shape of [m_num_components][m_num_var_types_per_voxel][m_num_voxels].
  ACCUM_DATATYPE* m_values;
  int m_num_values;
  int m_num_bytes;
  int get_value_index (int component_id, int var_index, int voxel) {
    return voxel + var_index * m_num_voxels + component_id * m_num_var_types_per_voxel * m_num_voxels;
  }

  //Storage for the composited components.
  MEAS_DATATYPE* m_composite_values; //[m_num_emitters + m_num_materials +1 ][m_num_var_types_per_voxel][m_num_voxels].
  int m_num_composite_values;
  int m_num_composite_bytes;

  //Storage for the transformed counterparts of the m_composite_values from above.
  MEAS_DATATYPE* m_non_grf_composite_values;
  MEAS_DATATYPE* m_grf_composite_values;

  //This map is used (at initialization only) to record how to access
  //the m_values array given an SRI var type. This maps an SRI var
  //type to the nth variable in the dcache.
  std::unordered_map<SRI_VARIABLE_TYPE, int> m_var_type_index_map;

  //The above will be used (at initialization only) to create the following
  //directly addressible lookup tables converting a window's var_index to
  //the dcache var index (avoiding a hash table search).
  int m_num_windows;
  sINT16** m_var_index_tables; //has shape [num window in combination][nth_window->n_variables]

  //For each var type that represents the components of a vector in 3D
  //space, the vector may need to be transformed into the ground reference
  //frame (GRF).  The following holds the var index to the vector's
  //first component. It's assumed that the components are contigious.
  std::vector<int> m_vector_var_indices;
  std::vector<bool> m_vector_is_population_scaled;

  //The following members are updated depedning on what ublk is being measure
  VOXEL_MASK_8 m_voxel_mask;
  UBLK_TYPE* m_ublk;
  ACCUM_DATATYPE m_particle_population_scale_factor; //The scaling operation that uses this maybe belongs on the CP.

  //The following members are updated depending on what window is sampling the cached values.
  sINT16* m_current_var_index_table; //Holds a reference into the m_var_index_table for the current window, the element for the nth variable of the window yeilds the nth variable index in the dcahce.
  bool m_rotate_vectors_to_grf; //Changes depending on the ublk and window thats currently being measured.
  bool m_grf_transformation_complete; //True if the transformation was already performed for another window.

  //Method for transformaing a vector to the ground reference frame.
  void transform_vectors_to_grf();
  void copy_vector_values(MEAS_DATATYPE* dest, MEAS_DATATYPE* source);

};

typedef tPARTICLE_MEAS_DCACHE<sdFLOAT, sPARTICLE_VAR, tUBLK<UBLK_UBFLOAT_TYPE_TAG> > sPARTICLE_MEAS_DCACHE, *PARTICLE_MEAS_DCACHE;

extern sPARTICLE_MEAS_DCACHE* g_particle_meas_dcache; //For each possible combination of winows a ublk is in, these objects will compute particle modeling var types.

#endif

//other items from Dalon:
// Dont use the at method of a stl vector.
//  Use a single linked stl list for the PARCEL_LIST implementatino
