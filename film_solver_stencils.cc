/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "film_solver_stencils.h"
#include "surfel.h"
#include "surfel_table.h"
#include "comm_groups.h"
#include "parse_shob_descs.h"
#include "particle_sim_info.h"
#include "film_comm.h"

#define USE_FILM_STENCIL_CUTPOINTS
#define FILM_STENCIL_CUTPOINT_ANGLE 60.0  //degrees
#define DO_SECOND_ROUND_FO_GHOST_CREATION

std::vector<sSTENCIL_SIM_ERROR_DATA> g_stencil_sim_error_buffer;

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
bool sFILM_STENCIL_INIT_INFO_PREDICATE::operator()(sFILM_STENCIL_INIT_INFO const &a, sFILM_STENCIL_INIT_INFO const &b) const {
  sPARTICLE_VAR r[N_SPACE_DIMS];

  vsub(r, a.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_a = vdot(r, r);

  vsub(r, b.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_b = vdot(r, r);

  return dist_sqr_a < dist_sqr_b;
}
#endif
INLINE bool sFILM_STENCIL_INIT_INFO_PREDICATE::operator()(sSURFEL_STENCIL_INIT_INFO const &a, sSURFEL_STENCIL_INIT_INFO const &b) const {
  sPARTICLE_VAR r[N_SPACE_DIMS];

  vsub(r, a.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_a = vdot(r, r);

  vsub(r, b.surfel->centroid, reference_surfel->centroid);
  sPARTICLE_VAR dist_sqr_b = vdot(r, r);

  return dist_sqr_a < dist_sqr_b;
}

VOID sSURFELS_ID_SET::construct_set_of_ids() {
  clear_set_of_ids();
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      insert_new_id(surfel);
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      insert_new_id(isurfel_pair->m_interior_surfel);
      insert_new_id(isurfel_pair->m_exterior_surfel);
    }
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL fo_surfel = quantum.m_surfel;
        insert_new_id(fo_surfel);
      }
    }
  }
}

sSURFELS_ID_SET g_surfel_ids;

INLINE BOOLEAN surfel_is_stencil_cutpoint(SURFEL home_surfel,SURFEL surfel_to_check) {
#ifdef USE_FILM_STENCIL_CUTPOINTS
  //Check if the angle between two surfels is extreme.
  sPARTICLE_VAR cos_angle_between_surfels;
  cos_angle_between_surfels = vdot(home_surfel->normal, surfel_to_check->normal);
  BOOLEAN is_cutpoint = cos_angle_between_surfels < cos(M_PI/180.0 * FILM_STENCIL_CUTPOINT_ANGLE);
  return is_cutpoint;
#else
  return FALSE;
#endif
}

BOOLEAN surfel_should_be_added_to_initial_stencil(SURFEL home_surfel, SURFEL surfel_to_check,
                                                  sFILM_STENCIL_INIT_INFO_VECTOR &stencil_info,
                                                  sPARTICLE_VAR radius)
{

#if 0
  //Never include even surfels, the film solver should only operate on odd surfels near VR transistions.
  if(surfel_to_check->is_even())
    return FALSE;
#endif

  //check that the surfel is not an inlet or an outlet
  if (!surfel_to_check->is_inlet_or_outlet()) {
    //check that the surfel in question is not already in home_surfel's stencil.
    if (*(surfel_to_check->p_data()->s.was_added_to_stencil()) != home_surfel->id()) {

      //if not, check that the distance is within the stencil radius
      sPARTICLE_VAR r[N_SPACE_DIMS];
      vsub(r, surfel_to_check->centroid, home_surfel->centroid);
      sPARTICLE_VAR distance_sqr = vdot(r,r);
      return distance_sqr < radius * radius;
    }
  }
  return FALSE;
}

long int measure_film_solver_stencil_memory_usage() {
  long int stencil_count = 0;
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      FILM_STENCIL_SURFEL_VECTOR interacting_surfels = surfel->p_data()->nearby_surfels;
      stencil_count += interacting_surfels->size();
    }
  }
  return stencil_count * (sizeof(SURFEL) + sizeof(sPARTICLE_VAR));
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID compile_nearby_surfels(sPARTICLE_VAR radius, sFILM_STENCIL_INIT_INFO_VECTOR &init_info_vector, asINT32 pass);
VOID refine_one_surfel_stencil(sPARTICLE_VAR max_radius, SURFEL surfel, sFILM_STENCIL_INIT_INFO_VECTOR &init_info_vector);
static VOID init_surfel_stencil_vectors(sPARTICLE_VAR max_stencil_radius, sFILM_STENCIL_INIT_INFO_VECTOR &init_info_vector)
{

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      *(surfel->p_data()->s.was_added_to_stencil()) = -1;
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      SURFEL int_surfel = isurfel_pair->m_interior_surfel;
      *(int_surfel->p_data()->s.was_added_to_stencil()) = -1;

      SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
      *(ext_surfel->p_data()->s.was_added_to_stencil()) = -1;
    }
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL fo_surfel = quantum.m_surfel;
        *((fo_surfel)->p_data()->s.was_added_to_stencil()) = -1;
      }
    }
  }

  //Make one pass to initialize each surfel's stencils to contain at least itself.
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      init_info_vector.resize(1);
      init_info_vector[0].surfel = surfel;
      init_info_vector[0].sharp_edge_count_to_center = 0;
      *((surfel)->p_data()->s.was_added_to_stencil()) = surfel->id(); //use the center surfel's ID as the magic value for having already been added to the init info vector.
      compile_nearby_surfels(max_stencil_radius, init_info_vector, 0);
      surfel->p_data()->nearby_surfels->resize(0); //discard any old stencils
      surfel->p_data()->nearby_areas->resize(0);
      refine_one_surfel_stencil(max_stencil_radius, surfel, init_info_vector);
    }
    DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
      SURFEL int_surfel = isurfel_pair->m_interior_surfel;
      init_info_vector.resize(1);
      init_info_vector[0].surfel = int_surfel;
      init_info_vector[0].sharp_edge_count_to_center = 0;
      *((int_surfel)->p_data()->s.was_added_to_stencil()) = int_surfel->id(); //use the center surfel's ID as the magic value for having already been added to the init info vector.
      compile_nearby_surfels(max_stencil_radius, init_info_vector, 0);
      int_surfel->p_data()->nearby_surfels->resize(0); //discard any old stencils
      int_surfel->p_data()->nearby_areas->resize(0);
      refine_one_surfel_stencil(max_stencil_radius, int_surfel, init_info_vector);

      SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
      init_info_vector.resize(1);
      init_info_vector[0].surfel = ext_surfel;
      init_info_vector[0].sharp_edge_count_to_center = 0;
      *((ext_surfel)->p_data()->s.was_added_to_stencil()) = ext_surfel->id(); //use the center surfel's ID as the magic value for having already been added to the init info vector.
      compile_nearby_surfels(max_stencil_radius, init_info_vector, 0);
      ext_surfel->p_data()->nearby_surfels->resize(0); //discard any old stencils
      ext_surfel->p_data()->nearby_areas->resize(0);
      refine_one_surfel_stencil(max_stencil_radius, ext_surfel, init_info_vector);
    }

    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      for (const auto& quantum: group->quantums()) {
        SURFEL fo_surfel = quantum.m_surfel;
        init_info_vector.resize(1);
        init_info_vector[0].surfel = fo_surfel;
        init_info_vector[0].sharp_edge_count_to_center = 0;
        *(fo_surfel->p_data()->s.was_added_to_stencil()) = fo_surfel->id(); //use the center surfel's ID as the magic value for having already been added to the init info vector.
        compile_nearby_surfels(max_stencil_radius, init_info_vector, 0);
        fo_surfel->p_data()->nearby_surfels->resize(0); //discard any old stencils
        fo_surfel->p_data()->nearby_areas->resize(0);
        refine_one_surfel_stencil(max_stencil_radius, fo_surfel, init_info_vector);
      }
    }
  }
}

VOID compile_nearby_surfels(sPARTICLE_VAR radius, sFILM_STENCIL_INIT_INFO_VECTOR &init_info_vector, asINT32 pass)
{
  //This routine works with the following algorithm to form a vector of surfels
  //that are within a certain physical distance of the given surfel.    \
  //The surfel vector is expected to be initialized with the surfel of interest
  if (init_info_vector.size() != 0)  {

    SURFEL home_surfel = init_info_vector[0].surfel;

    sPARTICLE_VAR position[3];
    vcopy(position, home_surfel->centroid);

    if (!particle_sim.position_is_inside_dispersion_box(NULL, position)) {
      return;
    }

    asINT32 surface_material_id = home_surfel->p_data()->s.surface_material_id < 0 ? 0 : home_surfel->p_data()->s.surface_material_id;
    SURFACE_INTERACTION_PROPERTY surface_interaction_params =
      g_particle_sim_info.surface_interaction_matrix[surface_material_id][0];

    //printf("m_disable_film_solver: %s\n", surface_interaction_params->m_disable_film_solver ? "true" : "false");
    if (surface_interaction_params->m_disable_film_solver) {
      return;
    }
    
    //For each surfel in the vector, starting from the beginning
    //  iterate over its neighbors...
    //    if the neighbor is already in the list, skip it
    //      otherwise, if its centroid is within radius of the centroid of the first surfel
    //        then add the surfel to the vector of surfels.
    //  continue until the end of the vector is reached which will eventually happen when no new surfels can be added

    for(asINT32 i=0; (i < init_info_vector.size()) && (init_info_vector.size() <= g_max_surfels_in_a_film_solver_stencil) ; i++) { //size() is expected to increase as new surfels are added
      sSURFEL* surfel = init_info_vector[i].surfel; //This cast should be legal because no sampling surfels should appear in the stencil
      asINT32 edges_to_center_from_parent = init_info_vector[i].sharp_edge_count_to_center;

      asINT32 n_vertices = surfel->p_data()->n_vertices;
      asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;

      //Iterate over all the edges of this surfel.
      ccDOTIMES(vertex_num, n_vertices) {
        SIM_VERTEX tail_vertex = surfel_vertex_from_global_index(first_vertex_index + vertex_num);
        SIM_VERTEX head_vertex = nullptr;
        DGF_VERTEX_INDEX head_vertex_index = -1;
        if (vertex_num == n_vertices - 1) {
          head_vertex = surfel_vertex_from_global_index(first_vertex_index);
          head_vertex_index = vertex_local_index_from_global(first_vertex_index);
        } else {
          head_vertex = surfel_vertex_from_global_index(first_vertex_index + vertex_num + 1);
          head_vertex_index = vertex_local_index_from_global(first_vertex_index + vertex_num + 1);
        }

        DGF_VERTEX_INDEX tail_vertex_index = vertex_local_index_from_global(first_vertex_index + vertex_num);
        DGF_VERTEX_INDEX first_vertex_surfel_index = g_surfel_vertices_info.vertex_first_surfel_index(tail_vertex_index);

        dFLOAT vector_tail_to_surfel_centroid[3];
        vsub(vector_tail_to_surfel_centroid, surfel->centroid, tail_vertex->coord); 

        dFLOAT vector_tail_to_head[3];

        if (sim.is_3d()) {
          vsub(vector_tail_to_head, head_vertex->coord, tail_vertex->coord);
        } else {
          // For 2D cases, the tail to head vector is either the +Z or -Z axis
          // This can be determined by a cross of (edge to surfel centroid) and (surfel normal)
          // Further, for 2D cases, tail vertex is the same as the edge since edge is a point
          dFLOAT unit_vector_edge_to_surfel_centroid[3];
          vcopy(unit_vector_edge_to_surfel_centroid, vector_tail_to_surfel_centroid);
          vunitize(unit_vector_edge_to_surfel_centroid);
          vcross(vector_tail_to_head, unit_vector_edge_to_surfel_centroid, surfel->normal);
        }

        dFLOAT unit_vector_tail_to_head[3];
        vcopy(unit_vector_tail_to_head, vector_tail_to_head);
        vunitize(unit_vector_tail_to_head);

        dFLOAT vector_tail_to_projection[3];
        dFLOAT scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_surfel_centroid);
        vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

        sdFLOAT vector_edge_to_surfel_centroid[3];
        vsub(vector_edge_to_surfel_centroid, vector_tail_to_surfel_centroid, vector_tail_to_projection);

        sdFLOAT unit_vector_edge_to_surfel_centroid[3];
        vcopy(unit_vector_edge_to_surfel_centroid, vector_edge_to_surfel_centroid);
        vunitize(unit_vector_edge_to_surfel_centroid);

        SURFACE_SHOB smallest_angle_surfel = NULL;
        dFLOAT pi = acos(-1.0);
        dFLOAT smallest_angle = 2.0 * pi;


        ///Iterate over all the other surfels that share this edge's tail vertex
        ccDOTIMES(s, g_surfel_vertices_info.m_vertex_n_surfels[tail_vertex_index]) {
          sSURFEL_VERTEX_PAIR *vertex_surfel = &(g_surfel_vertices_info.m_vertex_surfels[first_vertex_surfel_index + s]);
          sSURFEL* shob_surfel = (sSURFEL*)(vertex_surfel->surfel);
          DGF_VERTEX_INDEX shob_surfel_head_vertex = vertex_surfel->head_vertex;
          DGF_VERTEX_INDEX shob_surfel_opposite_head_vertex = vertex_surfel->opposite_head_vertex;
          std::size_t surfel_shared_current_edge = 0;
          // test if the candidate surfel shares an edge with home surfel.
          if((shob_surfel_head_vertex == head_vertex_index || shob_surfel_opposite_head_vertex == head_vertex_index)
              //if (shob_surfel_opposite_head_vertex == head_vertex_index
              && surfel->id() != shob_surfel->id()
              && !shob_surfel->is_inlet_or_outlet()
              && shob_surfel->is_wall()  //should at least exclude sampling surfels
              && surfel != shob_surfel->clone_surfel()
              && (
                  surfel->is_backside() == shob_surfel->is_backside()
                  || surfel->m_face_index != shob_surfel->m_face_index
                  )
            ) {

            dFLOAT angle = 2.0 * pi;
            if(shob_surfel!=surfel->m_opposite_surfel){
              dFLOAT vector_tail_to_candidate_surfel_centroid[3];
              vsub(vector_tail_to_candidate_surfel_centroid, shob_surfel->centroid, tail_vertex->coord);

              scale_mag = vdot(unit_vector_tail_to_head, vector_tail_to_candidate_surfel_centroid);
              vscale(vector_tail_to_projection, scale_mag, unit_vector_tail_to_head);

              dFLOAT unit_vector_edge_to_candidate_surfel_centroid[3];
              vsub(unit_vector_edge_to_candidate_surfel_centroid, vector_tail_to_candidate_surfel_centroid, vector_tail_to_projection);
              vunitize(unit_vector_edge_to_candidate_surfel_centroid);

              // Use temporary edge vectors to eliminate non-feasible surfels
              dFLOAT surfel_edge_vector[3];
              vcross(surfel_edge_vector, unit_vector_edge_to_surfel_centroid, surfel->normal);
              dFLOAT candidate_surfel_edge_vector[3];
              vcross(candidate_surfel_edge_vector, unit_vector_edge_to_candidate_surfel_centroid, shob_surfel->normal);

              // All valid candidates will have opposite directed edge vectors
              if (vdot(surfel_edge_vector, candidate_surfel_edge_vector) < -0.95) {
                // Cross product of the edge to centroid vectors tells us if the angle is in the
                // first or second half. If the cross product is same direction as the edge
                // vector, it is 1/2 quadrants, if not, it is 3/4 quadrants
                dFLOAT cross_edge_to_centroid_vectors[3];
                vcross(cross_edge_to_centroid_vectors, unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);

                // The dot product of the two edge to centroid vectors is used to compute the angle
                dFLOAT dot_edge_to_centroid_vectors = vdot(unit_vector_edge_to_surfel_centroid, unit_vector_edge_to_candidate_surfel_centroid);
                // protection for numerical precision
                dot_edge_to_centroid_vectors = std::min(dot_edge_to_centroid_vectors, 1.0);
                dot_edge_to_centroid_vectors = std::max(dot_edge_to_centroid_vectors, -1.0);

                if (vdot(cross_edge_to_centroid_vectors, unit_vector_tail_to_head) > 0.0) { // 1/2 quadrants
                  angle = acos(dot_edge_to_centroid_vectors);
                } else { // 3/4 quadrants
                  angle = 2.0 * PI - acos(dot_edge_to_centroid_vectors);
                }
              }
            }

            if (angle <= smallest_angle){
              smallest_angle = angle;
              smallest_angle_surfel = shob_surfel;
              surfel_shared_current_edge += 1;
            }
          }
        }

        if (smallest_angle_surfel != NULL ) { //&& !shob_is_sampling_surfel(smallest_angle_surfel)) { //TO_BE_DONE
          sSURFEL* neighbor_surfel = (sSURFEL*)smallest_angle_surfel;
          if (surfel_should_be_added_to_initial_stencil(home_surfel,  neighbor_surfel, init_info_vector, radius)) {

            //add a new surfel to the top of the pile
            asINT32 edges_from_neighbor_to_center = edges_to_center_from_parent + (surfel_is_stencil_cutpoint(surfel, neighbor_surfel) ? 1 : 0);
            sFILM_STENCIL_INIT_INFO new_record(neighbor_surfel, edges_from_neighbor_to_center);
            init_info_vector.push_back(new_record);
            *(neighbor_surfel->p_data()->s.was_added_to_stencil()) = home_surfel->id(); //mark the surfel to signify its inclusion

#if 0 //dont add clone surfels to the stencil
            SURFEL clone_surfel = neighbor_surfel->clone_surfel();

            if ( clone_surfel != NULL) {
              if (surfel_should_be_added_to_initial_stencil(home_surfel, clone_surfel, init_info_vector, radius)) {
                //Make sure both of an even-odd pair make it into the stencil.
                sFILM_STENCIL_INIT_INFO new_record(neighbor_surfel, edges_from_neighbor_to_center);
                init_info_vector.push_back(new_record);
                *(clone_surfel->p_data()->s.was_added_to_stencil()) = home_surfel->id(); //mark the surfel to signify its inclusion
              }
            }
#endif
          } //if (surfel_should_be_added_to_initial_stencil)
        } //if (neighbor_surfel != NULL && shob_is_dynamics_surfel(neighbor_surfel))
      } //ccDOTIMES(vertex_num, n_vertices)
    } //for i

#ifdef DEBUG_SURFEL_STENCIL_CONSTRUCTION
    LOG_MSG("SURFEL_STENCILS", LOG_TS).printf("%d -> %d", home_surfel->id(), init_info_vector.size());
    // for (asINT32 i = 0; i < init_info_vector->v.size(); i++) {
    //   SURFEL surfel = init_info_vector[i].surfel;
    //   LOG_MSG_IF(home_surfel->id() == 3087, "SURFEL_STENCILS", LOG_TS).printf("%d[%d] -> %d",
    //       home_surfel->id(), i, surfel->id());
    // }
#endif

    //keep a vector of surfels which had to have their stencils truncated so simerrs can be reported to the CP.
    //The simerrs cant be created here immediately due to MPI_Barrier calls used in the stencil creation routines that interact with simerr mpi messages.
    if (init_info_vector.size() > g_max_surfels_in_a_film_solver_stencil) {
      if (g_stencil_sim_error_buffer.size() < MAX_STENCIL_ERROR_LIMIT) {
        sSTENCIL_SIM_ERROR_DATA error_data;
        error_data.error_code = SP_EER_FULL_FILM_STENCIL;
        error_data.scale = home_surfel->scale();
        vcopy(error_data.centroid, home_surfel->centroid);
        error_data.comment = "";
        g_stencil_sim_error_buffer.push_back(error_data);
      }
    }

  }
}

//This routine culls the stencils if they span sharp corners...but only to a point where the
//resulting stencil area is not too small.
VOID refine_one_surfel_stencil(sPARTICLE_VAR max_radius, SURFEL surfel, sFILM_STENCIL_INIT_INFO_VECTOR &init_info_vector) {

  //if (!shob_is_sampling_surfel(surfel))  //TO_BE_DONE
  SCALE min_scale = FINEST_SCALE, max_scale = COARSEST_SCALE;

  SURFEL_PARTICLE_DATA p_data = surfel->p_data();
  FILM_STENCIL_SURFEL_VECTOR nearby_surfels = p_data->nearby_surfels;

  //Sort the init info records by distance to centroid so that closest surfels are first.
  std::sort(init_info_vector.begin(), init_info_vector.end(), sFILM_STENCIL_INIT_INFO_PREDICATE(surfel));

  //Find the max of sharp_edge_count_to_center for any of the surfels available for this surfel's stencil.
  asINT32 max_edge_transists_to_center = 0;
  ccDOTIMES(index, init_info_vector.size()) {
    sFILM_STENCIL_INIT_INFO &init_info = init_info_vector[index];
    max_edge_transists_to_center = MAX(max_edge_transists_to_center, init_info.sharp_edge_count_to_center);

    SURFEL stencil_surfel = init_info.surfel;
    *(stencil_surfel->p_data()->s.was_area_accumulated()) = 0;

#if 1
    SURFEL clone_surfel = stencil_surfel->clone_surfel();

    if (clone_surfel != NULL)
      *(clone_surfel->p_data()->s.was_area_accumulated()) = 0;
#endif
  }

  //Now make several passes through the available surfels, each pass accepting a greater number
  //of sharp_edge_count_to_centers, and add the closest surfels first (they've previously
  //been sorted by distance from center).  Stop when cummulative_area grows to a sufficent amount
  sPARTICLE_VAR cummulative_area = 0;
  for(asINT32 number_of_compromises = 0;
      number_of_compromises < max_edge_transists_to_center + 1;
      number_of_compromises++){

    sPARTICLE_VAR min_allowed_area =  number_of_compromises!=0  ?  0.5 * max_radius * max_radius * M_PI : 1e6; //add everything in the stencil that is smoothly connected and then up to a certain area if not smoothly connected

    for(asINT32 index = 0 ; (index < init_info_vector.size()) && (cummulative_area < min_allowed_area) ; index++) {

      sFILM_STENCIL_INIT_INFO &init_info = init_info_vector[index];
      SURFEL stencil_surfel = init_info.surfel;
      //        if (init_info.sharp_edge_count_to_center <= number_of_compromises) 
      if (init_info.sharp_edge_count_to_center <= number_of_compromises && *(stencil_surfel->p_data()->s.was_area_accumulated()) == 0) {
        
        SURFEL surfel_to_add = stencil_surfel;
        if(stencil_surfel->is_even()) {
          surfel_to_add = stencil_surfel->clone_surfel();
        }

        if(surfel_to_add == NULL)
          continue;
         
        //if the stencil surfel is not even
        surfel->p_data()->nearby_surfels->push_back(surfel_to_add);
        min_scale = std::min(min_scale, (SCALE)surfel_to_add->scale());
        max_scale = std::max(max_scale, (SCALE)surfel_to_add->scale());
        
        asINT32 log2_voxel_size = (sim.num_dims - 1) * scale_to_log2_voxel_size(surfel_to_add->scale());
        sPARTICLE_VAR surfel_area = surfel_to_add->area * (sPARTICLE_VAR)( 1 << log2_voxel_size);
        cummulative_area += surfel_area;
        surfel->p_data()->nearby_areas->push_back(cummulative_area);
        
        *(surfel_to_add->p_data()->s.was_area_accumulated()) = 1;
       

# if 0          
          SURFEL clone_surfel = stencil_surfel->clone_surfel();
          
          if ( clone_surfel != NULL) {
            //if (*(clone_surfel->p_data()->s.was_added_to_stencil()) != surfel->id()) { //surfels that have already been used have this flag set to -1
            //*(clone_surfel->p_data()->s.was_added_to_stencil()) = surfel->id();
            
            surfel->p_data()->nearby_surfels->push_back(clone_surfel);
            min_scale = std::min(min_scale, (SCALE)clone_surfel->scale());
            max_scale = std::max(max_scale, (SCALE)clone_surfel->scale());
            
            surfel->p_data()->nearby_areas->push_back(cummulative_area);
            
            *(clone_surfel->p_data()->s.was_area_accumulated()) = 1;
           //}
	  }
#endif
      }
      if (cummulative_area >= min_allowed_area)
        break;
    }
  }

  ccDOTIMES(index, init_info_vector.size()) {
    sFILM_STENCIL_INIT_INFO &init_info = init_info_vector[index];

    SURFEL stencil_surfel = init_info.surfel;
    *(stencil_surfel->p_data()->s.was_area_accumulated()) = 0;

#if 1
    SURFEL clone_surfel = stencil_surfel->clone_surfel();

    if (clone_surfel != NULL)
      *(clone_surfel->p_data()->s.was_area_accumulated()) = 0;
#endif
  }

  surfel->p_data()->scale_range[0] = min_scale;
  surfel->p_data()->scale_range[1] = max_scale;
  //}
}
#endif

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID append_connectivity_with_additional_vertices(FO_SURFEL_INIT_DATA_COLLECTION data,
                                                  SURFEL film_only_ghost_surfel) {

  //mark the vertices as used
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
  film_only_ghost_surfel->p_data()->first_vertex_index = g_surfel_vertices_info.n_surfel_vertex_global_indices();
  asINT32 num_vertices = data->vertices.size();
  ccDOTIMES(vertex_num, num_vertices) {
    asINT32 global_index = data->vertices[vertex_num].global_index;
    g_surfel_vertices_info.set_is_surfel_vertex_used(global_index);
    g_surfel_vertices_info.add_vertex_global_index(global_index);
  }
#endif
}
#else
VOID append_connectivity_with_additional_vertices(FO_SURFEL_INIT_DATA_COLLECTION data,
                                                  SURFEL film_only_ghost_surfel) {

  //mark the vertices as used
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
  film_only_ghost_surfel->stencil()->first_vertex_index = g_surfel_vertices_info.n_surfel_vertex_global_indices();
  asINT32 num_vertices = data->vertices.size();
  ccDOTIMES(vertex_num, num_vertices) {
    asINT32 global_index = data->vertices[vertex_num].global_index;
    g_surfel_vertices_info.set_is_surfel_vertex_used(global_index);
    g_surfel_vertices_info.add_vertex_global_index(global_index);
  }
#endif
}
#endif

static VOID assign_home_sp_for_ghost_surfels()
{
  DO_SCALE_FROM_FINE_TO_COARSE(scale, FINEST_SCALE, 0) {
    DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
      asINT32 sp = group->m_source_sp.rank();
      for (auto& quantum: group->quantums()) {
        assert(quantum.m_surfel->m_home_sp == sp);
        //quantum.m_surfel->m_home_sp = sp;
      }
    }
  }
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID sPARTICLE_SIM::compute_film_solver_stencils_and_create_film_only_ghosts()
{
  g_stencil_sim_error_buffer.reserve(MAX_STENCIL_ERROR_LIMIT);

  //Initialize clone pointers between even/odd surfel pairs. This is needed for building film stencil on VR interfaces

  assign_home_sp_for_ghost_surfels();

  //Build film solver stencils used for film scalar field accumulation.
//g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges();
  //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
  g_surfel_vertices_info.m_surfel_vertex_indices.clear();
  delete[] g_surfel_vertices_info.m_surfel_half_edges;
  g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
  g_surfel_vertices_info.read_surfel_vertices();
  g_surfel_vertices_info.create_half_edges_for_old_stencil_construction();
  sFILM_STENCIL_INIT_INFO_VECTOR stencil_init_info;
  stencil_init_info.reserve(1<<16);
  init_surfel_stencil_vectors(g_particle_sim_info.max_radius_of_film_stencil, stencil_init_info);

  create_surfel_film_send_and_receive_groups();
  //The surfels near SP boundaries have their stencils truncated
  //so some extra ghost surfels that penetrate further into the remote
  //sp need to be generated. Only the film solver uses these new ghost surfels.
  retrieve_ghost_side_interface_stencils(0);
  retrieve_dynamics_side_interface_stencils(0);

  //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
  g_surfel_vertices_info.m_surfel_vertex_indices.clear();
  delete[] g_surfel_vertices_info.m_surfel_half_edges;
  g_surfel_vertices_info.delete_vertex_surfels_arrays();
  delete[] g_surfel_vertices_info.m_surfel_vertices;
  g_surfel_vertices_info.m_surfel_vertices = nullptr;
//g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges();
  g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
  g_surfel_vertices_info.read_surfel_vertices();
  g_surfel_vertices_info.create_half_edges_for_old_stencil_construction();
  //Rebuilding the initial stencils using the connectivity of film only ghost surfels.
  init_surfel_stencil_vectors(g_particle_sim_info.max_radius_of_film_stencil, stencil_init_info);

#ifdef DO_SECOND_ROUND_FO_GHOST_CREATION
  //Do another round of reconciling stencils that cross domain boundaries.
  retrieve_ghost_side_interface_stencils(1);
  //Delete the old connectivity map and build a new one that includes the newly created film only ghost surfels.
  g_surfel_vertices_info.m_surfel_vertex_indices.clear();
  delete[] g_surfel_vertices_info.m_surfel_half_edges;
  g_surfel_vertices_info.delete_vertex_surfels_arrays();
  delete[] g_surfel_vertices_info.m_surfel_vertices;
  g_surfel_vertices_info.m_surfel_vertices = nullptr;
//g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges();
  g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
  g_surfel_vertices_info.read_surfel_vertices();
  g_surfel_vertices_info.create_half_edges_for_old_stencil_construction();
  init_surfel_stencil_vectors(g_particle_sim_info.max_radius_of_film_stencil, stencil_init_info);

  retrieve_dynamics_side_interface_stencils(1);

  //Delete the old connectivity map and build a new one that including the newly created film only ghost surfels.
  g_surfel_vertices_info.m_surfel_vertex_indices.clear();
  delete[] g_surfel_vertices_info.m_surfel_half_edges;
  g_surfel_vertices_info.delete_vertex_surfels_arrays();
  delete[] g_surfel_vertices_info.m_surfel_vertices;
  g_surfel_vertices_info.m_surfel_vertices = nullptr;
//g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges();
  g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
  g_surfel_vertices_info.read_surfel_vertices();
  g_surfel_vertices_info.create_half_edges_for_old_stencil_construction();
  g_surfel_vertices_info.m_surfel_vertex_global_indices.clear(); //only need the local id's at this point now that the above has remapped all IDs.

  init_surfel_stencil_vectors(g_particle_sim_info.max_radius_of_film_stencil, stencil_init_info);

  delete g_surfel_vertices_info.m_is_surfel_vertex_used;
#endif

  g_surfel_vertices_info.delete_vertex_surfels_arrays();
}
#endif

//------------------------------------------------
//The following functions are used for calculating the
//surfel curvature tensor.

//define struct for storing vertex normal
typedef struct sVERTEX_NORMAL_INIT_INFO{
  sPARTICLE_VAR total_surfel_area_weighted_normal[3];
  sPARTICLE_VAR vertex_unit_normal[3];
  bool          used_in_stencil;

  sVERTEX_NORMAL_INIT_INFO(){
    total_surfel_area_weighted_normal[0] = 0.0;
    total_surfel_area_weighted_normal[1] = 0.0;
    total_surfel_area_weighted_normal[2] = 0.0;
    vertex_unit_normal[0] = 0.0;
    vertex_unit_normal[1] = 0.0;
    vertex_unit_normal[2] = 0.0;
    used_in_stencil = false;
  }

  VOID calc_vertex_normal(){
    if (used_in_stencil && (total_surfel_area_weighted_normal[0] != 0.0 |
                            total_surfel_area_weighted_normal[1] != 0.0 |
                            total_surfel_area_weighted_normal[2] != 0.0 )){
      vunitize(total_surfel_area_weighted_normal);
      vertex_unit_normal[0] = total_surfel_area_weighted_normal[0];
      vertex_unit_normal[1] = total_surfel_area_weighted_normal[1];
      vertex_unit_normal[2] = total_surfel_area_weighted_normal[2];
    }
  }

} * VERTEX_NORMAL_INIT_INFO;

// define edge vector struct
struct sSURFEL_EDGE_VECTOR_CURVATURE{
  double edge_vector[3];
  double normal_vector_difference[3];

  sSURFEL_EDGE_VECTOR_CURVATURE(){
    edge_vector[0] = 0.0;
    edge_vector[1] = 0.0;
    edge_vector[2] = 0.0;
    normal_vector_difference[0] = 0.0;
    normal_vector_difference[1] = 0.0;
    normal_vector_difference[2] = 0.0;
  }
};

// calculate the normal at each vertex.
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID compute_vertex_normal(SURFEL surfel, VERTEX_NORMAL_INIT_INFO g_vertex_normal_init_info) {
  typedef DGF_VERTEX_INDEX VI;

  FILM_STENCIL_SURFEL_VECTOR stencil_surfels = surfel->p_data()->nearby_surfels;
  asINT32 num_surfels_to_include = stencil_surfels->size();

  ccDOTIMES(nth_surfel, num_surfels_to_include){
    SURFEL stencil_surfel = (*stencil_surfels)[nth_surfel];
    std::size_t n_vertices = stencil_surfel->p_data()->n_vertices;
    VI first_vertex_index_table_index = stencil_surfel->p_data()->first_vertex_index;
    // At each surfel, loop all the vertex. Adding the area weighted surfel normal to each vertex.
    ccDOTIMES(current_vertex, n_vertices){
      asINT32 log2_voxel_size = (sim.num_dims - 1) * scale_to_log2_voxel_size(stencil_surfel->scale());
      sPARTICLE_VAR surfel_area = stencil_surfel->area * (sPARTICLE_VAR)( 1 << log2_voxel_size);
      DGF_VERTEX_INDEX vertex_index = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      sSIM_VERTEX *vertex_normal = surfel_vertex_from_global_index(first_vertex_index_table_index + current_vertex);

      dFLOAT vertex_centroid_dist[3] = {0};

      ccDOTIMES(i,3){
        vertex_centroid_dist[i] = vertex_normal->coord[i] - stencil_surfel->centroid[i];
      }

      dFLOAT distance = std::sqrt(vertex_centroid_dist[0] * vertex_centroid_dist[0] +
                                  vertex_centroid_dist[1] * vertex_centroid_dist[1] +
                                  vertex_centroid_dist[2] * vertex_centroid_dist[2]);
      g_vertex_normal_init_info[vertex_index].used_in_stencil = true;
      ccDOTIMES(i,3){
        g_vertex_normal_init_info[vertex_index].total_surfel_area_weighted_normal[i] +=
          1.0/distance * stencil_surfel->normal[i];
      }
    }
  }

  //After the area weighted surfel normal is added for each vertex from all the surfel connected to it,
  //calculate the unit vertex normal.
  ccDOTIMES(current_vertex, g_surfel_vertices_info.m_n_surfel_vertices){
    g_vertex_normal_init_info[current_vertex].calc_vertex_normal();
  }
}
#else
VOID compute_vertex_normal(SURFEL surfel, VERTEX_NORMAL_INIT_INFO g_vertex_normal_init_info) {
  typedef DGF_VERTEX_INDEX VI;

  FILM_STENCIL_SURFEL_VECTOR stencil_surfels = surfel->p_data()->nearby_surfels;
  asINT32 num_surfels_to_include = stencil_surfels->size();

  ccDOTIMES(nth_surfel, num_surfels_to_include){
    SURFEL stencil_surfel = (*stencil_surfels)[nth_surfel].get_surfel();
    std::size_t n_vertices = stencil_surfel->stencil()->n_vertices;
    VI first_vertex_index_table_index = stencil_surfel->stencil()->first_vertex_index;
    // At each surfel, loop all the vertex. Adding the area weighted surfel normal to each vertex.
    ccDOTIMES(current_vertex, n_vertices){
      asINT32 log2_voxel_size = (sim.num_dims - 1) * scale_to_log2_voxel_size(stencil_surfel->scale());
      sPARTICLE_VAR surfel_area = stencil_surfel->area * (sPARTICLE_VAR)( 1 << log2_voxel_size);
      DGF_VERTEX_INDEX vertex_index = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      sSIM_VERTEX *vertex_normal = surfel_vertex_from_global_index(first_vertex_index_table_index + current_vertex);

      dFLOAT vertex_centroid_dist[3] = {0};

      ccDOTIMES(i,3){
        vertex_centroid_dist[i] = vertex_normal->coord[i] - stencil_surfel->centroid[i];
      }

      dFLOAT distance = std::sqrt(vertex_centroid_dist[0] * vertex_centroid_dist[0] +
                                  vertex_centroid_dist[1] * vertex_centroid_dist[1] +
                                  vertex_centroid_dist[2] * vertex_centroid_dist[2]);
      g_vertex_normal_init_info[vertex_index].used_in_stencil = true;
      ccDOTIMES(i,3){
        g_vertex_normal_init_info[vertex_index].total_surfel_area_weighted_normal[i] +=
          1.0/distance * stencil_surfel->normal[i];
      }
    }
  }

  //After the area weighted surfel normal is added for each vertex from all the surfel connected to it,
  //calculate the unit vertex normal.
  ccDOTIMES(current_vertex, g_surfel_vertices_info.m_n_surfel_vertices){
    g_vertex_normal_init_info[current_vertex].calc_vertex_normal();
  }
}
#endif

//------------------------------------------------
// 3x3 matrix multiplication
VOID rank3_matrix_multiplication(dFLOAT R[3][3], dFLOAT A[3][3], dFLOAT B[3][3]){
  ccDOTIMES(i,3) {
    ccDOTIMES(j,3) {
      R[i][j] = A[i][0] * B[0][j] + A[i][1] * B[1][j] + A[i][2] * B[2][j];
    }
  }
}
//------------------------------------------------

VOID compute_second_fundamental_form(sSURFEL_EDGE_VECTOR_CURVATURE * surfel_edge_vector_curvature,
                                     sPARTICLE_VAR                   surfel_coordinate_along_surfel_u[3],
                                     sPARTICLE_VAR                   surfel_coordinate_along_surfel_v[3],
                                     VERTEX_NORMAL_INIT_INFO         g_vertex_normal_init_info,
                                     std::size_t                     total_num_of_vertex_stencil,
                                     SURFEL                          surfel) {

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  SURFEL_PARTICLE_DATA_BASE  p_data = surfel->p_data();
  typedef                    DGF_VERTEX_INDEX VI;
  std::size_t                n_vertices = p_data->n_vertices;
  VI                         first_vertex_index_table_index = p_data->first_vertex_index;
#else
  SURFEL_PARTICLE_DATA_BASE  p_data = surfel->p_data();
  typedef                    DGF_VERTEX_INDEX VI;
  std::size_t                n_vertices = surfel->stencil()->n_vertices;
  VI                         first_vertex_index_table_index = surfel->stencil()->first_vertex_index;
#endif
  // Formulate the A and b in Ax=b
  //dFLOAT A_coefficient_matrix[6*n_vertices];
  //dFLOAT B_coefficient_matrix[2*n_vertices];
  dFLOAT A_coefficient_matrix[6*total_num_of_vertex_stencil];
  dFLOAT B_coefficient_matrix[2*total_num_of_vertex_stencil];
  ccDOTIMES(current_edge, total_num_of_vertex_stencil){
    dFLOAT local_edge_vector[3];
    dFLOAT normal_vector_difference[3];
    // formulate A
    local_edge_vector[0] = surfel_edge_vector_curvature[current_edge].edge_vector[0];
    local_edge_vector[1] = surfel_edge_vector_curvature[current_edge].edge_vector[1];
    local_edge_vector[2] = surfel_edge_vector_curvature[current_edge].edge_vector[2];

    A_coefficient_matrix[6 * current_edge]     = vdot(local_edge_vector, surfel_coordinate_along_surfel_u);
    A_coefficient_matrix[6 * current_edge + 1] = vdot(local_edge_vector, surfel_coordinate_along_surfel_v);
    A_coefficient_matrix[6 * current_edge + 2] = 0.0;
    A_coefficient_matrix[6 * current_edge + 3] = 0.0;
    A_coefficient_matrix[6 * current_edge + 4] = A_coefficient_matrix[6 * current_edge];
    A_coefficient_matrix[6 * current_edge + 5] = A_coefficient_matrix[6 * current_edge + 1];

    // formulate B
    normal_vector_difference[0] =  surfel_edge_vector_curvature[current_edge].normal_vector_difference[0];
    normal_vector_difference[1] =  surfel_edge_vector_curvature[current_edge].normal_vector_difference[1];
    normal_vector_difference[2] =  surfel_edge_vector_curvature[current_edge].normal_vector_difference[2];

    B_coefficient_matrix[2 * current_edge]     = vdot(normal_vector_difference, surfel_coordinate_along_surfel_u);
    B_coefficient_matrix[2 * current_edge + 1] = vdot(normal_vector_difference, surfel_coordinate_along_surfel_v);
  }

  //Formulate the 3x3 A'Ax=A'B
  dFLOAT ATA_coefficient_matrix[3][3] = {0};
  dFLOAT ATB_coefficient_matrix[3]    = {0};
  ccDOTIMES(i_row, 3){
    ccDOTIMES(i_col, 3){
      //    ccDOTIMES(i_term, 2 * n_vertices){
      ccDOTIMES(i_term, 2 * total_num_of_vertex_stencil){
        ATA_coefficient_matrix[i_row][i_col] += A_coefficient_matrix[i_row + i_term * 3] *
          A_coefficient_matrix[i_col + i_term * 3];
      }
    }

    //  ccDOTIMES(i_term, 2 * n_vertices){
    ccDOTIMES(i_term, 2 * total_num_of_vertex_stencil){
      ATB_coefficient_matrix[i_row] += A_coefficient_matrix[i_row + i_term * 3] *
        B_coefficient_matrix[i_term];
    }
  }

  //Invert ATA
  dFLOAT ATA_invert_matrix[3][3] = {0};
  invert_rank3_matrix(ATA_coefficient_matrix, ATA_invert_matrix);

  //The second fundamental form in the 2D surface coord
  dFLOAT second_fundamental_form_surfel_coord_2D[3] = {0};
  ccDOTIMES(i_row, 3){
    ccDOTIMES(i_term, 3){
      second_fundamental_form_surfel_coord_2D[i_row] += ATA_invert_matrix[i_row][i_term] *
        ATB_coefficient_matrix[i_term];
    }
  }

  //The second fundamental form in the 3D X'Y'Z' coordinate:
  //X' is surfel_coordinate_along_surfel_u,
  //Y' is surfel_coordinate_along_surfel_v,
  //Z' is surfel->normal.
  dFLOAT second_fundamental_form_surfel_coord_3D[3][3] = {0};
  second_fundamental_form_surfel_coord_3D[0][0] = second_fundamental_form_surfel_coord_2D[0];
  second_fundamental_form_surfel_coord_3D[0][1] = second_fundamental_form_surfel_coord_2D[1];
  second_fundamental_form_surfel_coord_3D[1][0] = second_fundamental_form_surfel_coord_2D[1];
  second_fundamental_form_surfel_coord_3D[1][1] = second_fundamental_form_surfel_coord_2D[2];

  //Transform second fundamental form (SFF) from X'Y'Z' to global XYZ.
  //Transform matrix Q_transfrom_global_to_local from XYZ to X'Y'Z'.
  dFLOAT Q_transform_global_to_local[3][3] = {0};
  ccDOTIMES(i_column,3){
    Q_transform_global_to_local[0][i_column] = surfel_coordinate_along_surfel_u[i_column];
    Q_transform_global_to_local[1][i_column] = surfel_coordinate_along_surfel_v[i_column];
    Q_transform_global_to_local[2][i_column] = surfel->normal[i_column];
  }
  //The tensor in XYZ can be obtained by SFF_global = Q'.SFF_local.Q.
  dFLOAT Q_transform_global_to_local_transpose[3][3] = {0};
  ccDOTIMES(i_row, 3){
    ccDOTIMES(i_col, 3){
      Q_transform_global_to_local_transpose[i_row][i_col] = Q_transform_global_to_local[i_col][i_row];
    }
  }
  dFLOAT Q_transpose_dot_SFF_local[3][3] = {0};
  rank3_matrix_multiplication(Q_transpose_dot_SFF_local, Q_transform_global_to_local_transpose,
                              second_fundamental_form_surfel_coord_3D);

  dFLOAT second_fundamental_form_global[3][3] = {0};
  rank3_matrix_multiplication(second_fundamental_form_global, Q_transpose_dot_SFF_local,
                              Q_transform_global_to_local);

  //store SFF to p_data().
  ccDOTIMES(i_row, 3){
    ccDOTIMES(i_col, 3){
      ((SURFEL)surfel)->p_data()->s.surfel_curvature[i_row * 3 + i_col] = second_fundamental_form_global[i_row][i_col];
    }
  }
}

VOID sPARTICLE_SIM::compute_curvature_of_surfels() {

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      compute_curvature(surfel);
    }
  }
}
// compute surfel curvature
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID sPARTICLE_SIM::compute_curvature(SURFEL surfel) {
  typedef DGF_VERTEX_INDEX VI;
  //compute_vertex_normal();
  //compute edge vectors for each surfel
  //DO_DYN_SURFELS(shob_var){
  //  SURFEL surfel_pointer = (SURFEL)shob_var;
  //    if (surfel_pointer->centroid[0] > 152 && surfel_pointer->centroid[0] < 167
  //    && surfel_pointer->centroid[1] > 152 && surfel_pointer->centroid[1] < 167
  //    && surfel_pointer->centroid[2] > 150 && surfel_pointer->centroid[2] < 154){
  //      printf("In compute_surfel_curva_do_dyn_surfels\n");
  //      printf("surfel_centroid: %g, %g, %g \n",surfel_pointer->centroid[0],surfel_pointer->centroid[1],surfel_pointer->centroid[2]);
  //    }
  //}
  static dFLOAT area_for_curvature_stencil =
    3.14159265358 * g_dynamic_reentrainment_model_curvature_radius_meter *
    g_dynamic_reentrainment_model_curvature_radius_meter / (sim.meters_per_cell * sim.meters_per_cell);

  FILM_STENCIL_SURFEL_VECTOR  stencil_surfels = surfel->p_data()->nearby_surfels;
  FILM_STENCIL_WEIGHTS_VECTOR stencil_cummulative_area = surfel->p_data()->nearby_areas;
  auINT32 max_num_surfels_to_include = stencil_surfels->size();
  auINT32 total_num_of_vertex_stencil_include = 0;
  auINT32 num_surfels_to_include = 0;
  dFLOAT  total_area_include = 0.0;

  sVERTEX_NORMAL_INIT_INFO *l_vertex_normal_init_info;
  l_vertex_normal_init_info = xnew sVERTEX_NORMAL_INIT_INFO[g_surfel_vertices_info.m_n_surfel_vertices];
  compute_vertex_normal(surfel, l_vertex_normal_init_info);

  //  if (surfel_pointer->centroid[0] > 19.5 && surfel_pointer->centroid[0] < 20.5
  //    && surfel_pointer->centroid[1] > 100 && surfel_pointer->centroid[1] < 118
  //    && surfel_pointer->centroid[1] > 117 && surfel_pointer->centroid[1] < 118
  //    && surfel_pointer->centroid[2] > 88 && surfel_pointer->centroid[2] < 89){
  //    printf("In compute_surfel_curvature_in_id_order\n");
  //    printf("surfel_centroid: %g, %g, %g , %d\n",surfel_pointer->centroid[0],surfel_pointer->centroid[1],surfel_pointer->centroid[2], num_surfels_to_include);
  //    ccDOTIMES(nth_surfel, num_surfels_to_include){
  //      SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel];
  //      printf("%d\n", nth_surfel);
  //      std::ostringstream ss;
  //      ss << surfel_pointer->id();
  //      simerr_report_error_code(SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, stencil_surfel->scale(), stencil_surfel->centroid, ss.str());
  //    }
  //  }

  ccDOTIMES(nth_surfel, max_num_surfels_to_include){
    SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel];
    asINT32 n_vertices = stencil_surfel->p_data()->n_vertices;
    total_area_include = (*stencil_cummulative_area)[nth_surfel];
    total_num_of_vertex_stencil_include += n_vertices;
    num_surfels_to_include = nth_surfel + 1;
    if (total_area_include > area_for_curvature_stencil && nth_surfel > 4)
      break;
  }

  sSURFEL_EDGE_VECTOR_CURVATURE * surfel_edge_vector_curvature = xnew sSURFEL_EDGE_VECTOR_CURVATURE[total_num_of_vertex_stencil_include];
  auINT32 current_index_in_total_num_of_vertex = 0;
  ccDOTIMES(nth_surfel, num_surfels_to_include){
    SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel];
    std::size_t n_vertices = stencil_surfel->p_data()->n_vertices;
    VI first_vertex_index_table_index = stencil_surfel->p_data()->first_vertex_index;
    ccDOTIMES(current_vertex, n_vertices){
      VI head_vertex, tail_vertex;
      if (current_vertex == n_vertices-1){
        head_vertex = vertex_local_index_from_global(first_vertex_index_table_index);
        tail_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      } else {
        head_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex + 1);
        tail_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      }

      sSIM_VERTEX &head = g_surfel_vertices_info.m_surfel_vertices[head_vertex];
      sSIM_VERTEX &tail = g_surfel_vertices_info.m_surfel_vertices[tail_vertex];
      ccDOTIMES(i, 3){
        surfel_edge_vector_curvature[current_index_in_total_num_of_vertex + current_vertex].edge_vector[i] =
          head.coord[i] - tail.coord[i];
        surfel_edge_vector_curvature[current_index_in_total_num_of_vertex + current_vertex].normal_vector_difference[i] =
          l_vertex_normal_init_info[head_vertex].vertex_unit_normal[i] -
          l_vertex_normal_init_info[tail_vertex].vertex_unit_normal[i];
      }
    }
    current_index_in_total_num_of_vertex += n_vertices;
  }

  //set surface 2D coordinate along surface
  sPARTICLE_VAR surfel_coordinate_along_surfel_u[3];
  sPARTICLE_VAR surfel_coordinate_along_surfel_v[3];
  surfel_coordinate_along_surfel_u[0] = surfel_edge_vector_curvature[0].edge_vector[0];
  surfel_coordinate_along_surfel_u[1] = surfel_edge_vector_curvature[0].edge_vector[1];
  surfel_coordinate_along_surfel_u[2] = surfel_edge_vector_curvature[0].edge_vector[2];
  vunitize(surfel_coordinate_along_surfel_u);
  vcross(surfel_coordinate_along_surfel_v, surfel->normal, surfel_coordinate_along_surfel_u);

  //compute second fundamental form
  compute_second_fundamental_form(surfel_edge_vector_curvature,surfel_coordinate_along_surfel_u,
                                  surfel_coordinate_along_surfel_v, l_vertex_normal_init_info,
                                  total_num_of_vertex_stencil_include, surfel);
  //compute_eigenvalue_for_symmetric_3x3_matrix(surfel_pointer);
  delete[] surfel_edge_vector_curvature;
  delete[] l_vertex_normal_init_info;
}
#else
VOID sPARTICLE_SIM::compute_curvature(SURFEL surfel) {
  typedef DGF_VERTEX_INDEX VI;
  //compute_vertex_normal();
  //compute edge vectors for each surfel
  //DO_DYN_SURFELS(shob_var){
  //  SURFEL surfel_pointer = (SURFEL)shob_var;
  //    if (surfel_pointer->centroid[0] > 152 && surfel_pointer->centroid[0] < 167
  //    && surfel_pointer->centroid[1] > 152 && surfel_pointer->centroid[1] < 167
  //    && surfel_pointer->centroid[2] > 150 && surfel_pointer->centroid[2] < 154){
  //      printf("In compute_surfel_curva_do_dyn_surfels\n");
  //      printf("surfel_centroid: %g, %g, %g \n",surfel_pointer->centroid[0],surfel_pointer->centroid[1],surfel_pointer->centroid[2]);
  //    }
  //}
  static dFLOAT area_for_curvature_stencil =
    3.14159265358 * g_dynamic_reentrainment_model_curvature_radius_meter *
    g_dynamic_reentrainment_model_curvature_radius_meter / (sim.meters_per_cell * sim.meters_per_cell);

  FILM_STENCIL_SURFEL_VECTOR  stencil_surfels = surfel->p_data()->nearby_surfels;
  FILM_STENCIL_WEIGHTS_VECTOR stencil_cummulative_area = surfel->p_data()->nearby_areas;
  auINT32 max_num_surfels_to_include = stencil_surfels->size();
  auINT32 total_num_of_vertex_stencil_include = 0;
  auINT32 num_surfels_to_include = 0;
  dFLOAT  total_area_include = 0.0;

  sVERTEX_NORMAL_INIT_INFO *l_vertex_normal_init_info;
  l_vertex_normal_init_info = xnew sVERTEX_NORMAL_INIT_INFO[g_surfel_vertices_info.m_n_surfel_vertices];
  compute_vertex_normal(surfel, l_vertex_normal_init_info);

  //  if (surfel_pointer->centroid[0] > 19.5 && surfel_pointer->centroid[0] < 20.5
  //    && surfel_pointer->centroid[1] > 100 && surfel_pointer->centroid[1] < 118
  //    && surfel_pointer->centroid[1] > 117 && surfel_pointer->centroid[1] < 118
  //    && surfel_pointer->centroid[2] > 88 && surfel_pointer->centroid[2] < 89){
  //    printf("In compute_surfel_curvature_in_id_order\n");
  //    printf("surfel_centroid: %g, %g, %g , %d\n",surfel_pointer->centroid[0],surfel_pointer->centroid[1],surfel_pointer->centroid[2], num_surfels_to_include);
  //    ccDOTIMES(nth_surfel, num_surfels_to_include){
  //      SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel];
  //      printf("%d\n", nth_surfel);
  //      std::ostringstream ss;
  //      ss << surfel_pointer->id();
  //      simerr_report_error_code(SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, stencil_surfel->scale(), stencil_surfel->centroid, ss.str());
  //    }
  //  }

  ccDOTIMES(nth_surfel, max_num_surfels_to_include){
    SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel].get_surfel();
    asINT32 n_vertices = stencil_surfel->stencil()->n_vertices;
    total_area_include = (*stencil_cummulative_area)[nth_surfel];
    total_num_of_vertex_stencil_include += n_vertices;
    num_surfels_to_include = nth_surfel + 1;
    if (total_area_include > area_for_curvature_stencil && nth_surfel > 4)
      break;
  }

  sSURFEL_EDGE_VECTOR_CURVATURE * surfel_edge_vector_curvature = xnew sSURFEL_EDGE_VECTOR_CURVATURE[total_num_of_vertex_stencil_include];
  auINT32 current_index_in_total_num_of_vertex = 0;
  ccDOTIMES(nth_surfel, num_surfels_to_include){
    SURFEL  stencil_surfel = (*stencil_surfels)[nth_surfel].get_surfel();
    std::size_t n_vertices = stencil_surfel->stencil()->n_vertices;
    VI first_vertex_index_table_index = stencil_surfel->stencil()->first_vertex_index;
    ccDOTIMES(current_vertex, n_vertices){
      VI head_vertex, tail_vertex;
      if (current_vertex == n_vertices-1){
        head_vertex = vertex_local_index_from_global(first_vertex_index_table_index);
        tail_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      } else {
        head_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex + 1);
        tail_vertex = vertex_local_index_from_global(first_vertex_index_table_index + current_vertex);
      }

      sSIM_VERTEX &head = g_surfel_vertices_info.m_surfel_vertices[head_vertex];
      sSIM_VERTEX &tail = g_surfel_vertices_info.m_surfel_vertices[tail_vertex];
      ccDOTIMES(i, 3){
        surfel_edge_vector_curvature[current_index_in_total_num_of_vertex + current_vertex].edge_vector[i] =
          head.coord[i] - tail.coord[i];
        surfel_edge_vector_curvature[current_index_in_total_num_of_vertex + current_vertex].normal_vector_difference[i] =
          l_vertex_normal_init_info[head_vertex].vertex_unit_normal[i] -
          l_vertex_normal_init_info[tail_vertex].vertex_unit_normal[i];
      }
    }
    current_index_in_total_num_of_vertex += n_vertices;
  }

  //set surface 2D coordinate along surface
  sPARTICLE_VAR surfel_coordinate_along_surfel_u[3];
  sPARTICLE_VAR surfel_coordinate_along_surfel_v[3];
  surfel_coordinate_along_surfel_u[0] = surfel_edge_vector_curvature[0].edge_vector[0];
  surfel_coordinate_along_surfel_u[1] = surfel_edge_vector_curvature[0].edge_vector[1];
  surfel_coordinate_along_surfel_u[2] = surfel_edge_vector_curvature[0].edge_vector[2];
  vunitize(surfel_coordinate_along_surfel_u);
  vcross(surfel_coordinate_along_surfel_v, surfel->normal, surfel_coordinate_along_surfel_u);

  //compute second fundamental form
  compute_second_fundamental_form(surfel_edge_vector_curvature,surfel_coordinate_along_surfel_u,
                                  surfel_coordinate_along_surfel_v, l_vertex_normal_init_info,
                                  total_num_of_vertex_stencil_include, surfel);
  //compute_eigenvalue_for_symmetric_3x3_matrix(surfel_pointer);
  delete[] surfel_edge_vector_curvature;
  delete[] l_vertex_normal_init_info;
}
#endif


VOID sPARTICLE_SIM::send_queued_stencil_sim_errors() {
  //send simerrs to the cp for surfels that had too many entries in their stencil.
  //The errors can't be sent as they are detected due to the use of MPI_Barrier calls in the stencil building routines that lock up simerr mpi calls.

  ccDOTIMES(error_index, g_stencil_sim_error_buffer.size()) {
    sSTENCIL_SIM_ERROR_DATA error_data = g_stencil_sim_error_buffer[error_index];
    simerr_report_error_code(error_data.error_code, error_data.scale, error_data.centroid, error_data.comment.c_str());
  }


  if (g_stencil_sim_error_buffer.size() >= MAX_STENCIL_ERROR_LIMIT ) {
    msg_warn("There were more than %d surfels who's stencils contained more than %d elements.\n", MAX_STENCIL_ERROR_LIMIT, g_max_surfels_in_a_film_solver_stencil);
  }

  g_stencil_sim_error_buffer.clear();
  std::vector<sSTENCIL_SIM_ERROR_DATA>().swap(g_stencil_sim_error_buffer); //force capacity to zero.
}

/* Adding a consistency test that checks for surfel that have a null reference to one or more of their edge neighbor */
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID check_surfel_edge_neighbor(SURFEL surfel){

  if(surfel->is_ghost() || surfel->is_film_only_ghost() || surfel->is_inlet_or_outlet())
    return;

  asINT32 n_vertices = surfel->p_data()->n_vertices;
  asINT32 first_half_edge_index = surfel->p_data()->first_half_edge_index();
  asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;

  ccDOTIMES(edge_num, n_vertices) {
    SURFEL_HALF_EDGE edge = surfel_half_edge(first_half_edge_index + edge_num);
    SIM_VERTEX vertex_1 = surfel_vertex_from_global_index(first_vertex_index + edge_num);
    SIM_VERTEX vertex_2 = surfel_vertex_from_global_index(first_vertex_index + ((edge_num + 1) % n_vertices)); //the last edge closes the loop
    sPARTICLE_VAR edge_midpoint[3];
    vadd(edge_midpoint, vertex_1->coord, vertex_2->coord);
    vmul(edge_midpoint, 0.5);

    if (edge == NULL) {
      std::ostringstream error_string;
      error_string << "surfel's  edge " << edge_num << " has no edge object";
      simerr_report_error_code(SP_EER_SURFEL_NEIGHBOR_MISSING, 
                               surfel->scale(), 
                               cast_as_regular_array(surfel->centroid), 
                               error_string.str().c_str(),
                               edge_midpoint[0],
                               edge_midpoint[1],
                               edge_midpoint[2]);
      continue;  //Other edges of this surfel may not be null, we should check them.
    }

    if (edge->neighbor_surfel == NULL) {
      // Test if this edge is on a simvol wall
      bool edge_on_simvol = false;
      ccDOTIMES(axis, N_AXES){
        if((vertex_1->coord[axis] > (-1e-7) && vertex_1->coord[axis] < (1e-7) && vertex_2->coord[axis] > (-1e-7) && vertex_2->coord[axis] < (1e-7)) 
            ||(vertex_1->coord[axis] > (sim.simvol_size[axis] - 1e-7) && vertex_1->coord[axis] < (sim.simvol_size[axis] + 1e-7)
            && vertex_2->coord[axis] > (sim.simvol_size[axis] - 1e-7) && vertex_2->coord[axis] < (sim.simvol_size[axis] + 1e-7))){
          edge_on_simvol = true;
          break;
        }
      }
      
      if(edge_on_simvol) continue;

      std::ostringstream error_string;
      error_string << "surfel's edge " << edge_num << " has null neighbor reference";
      simerr_report_error_code(
                                 SP_EER_SURFEL_NEIGHBOR_MISSING, 
                                 surfel->scale(), 
                                 cast_as_regular_array(surfel->centroid), 
                                 error_string.str().c_str(),
                                 edge_midpoint[0],
                                 edge_midpoint[1],
                                 edge_midpoint[2]);
    }
  }
}
#else
VOID check_surfel_edge_neighbor(SURFEL surfel){
  if(surfel->is_ghost() || surfel->is_film_only_ghost() || surfel->is_inlet_or_outlet())
    return;

  asINT32 n_vertices = surfel->stencil()->n_vertices;
  asINT32 first_vertex_index = surfel->stencil()->first_vertex_index;
  asINT32 first_half_edge_index = surfel->stencil()->first_half_edge_index();

  ccDOTIMES(edge_num, n_vertices) {
    SURFEL_HALF_EDGE edge = surfel_half_edge(first_half_edge_index + edge_num);
    SIM_VERTEX vertex_1 = surfel_vertex_from_global_index(first_vertex_index + edge_num);
    SIM_VERTEX vertex_2 = surfel_vertex_from_global_index(first_vertex_index + ((edge_num + 1) % n_vertices)); //the last edge closes the loop
    sPARTICLE_VAR edge_midpoint[3];
    vadd(edge_midpoint, vertex_1->coord, vertex_2->coord);
    vmul(edge_midpoint, 0.5);

    if (edge == NULL) {
      std::ostringstream error_string;
      error_string << "surfel's  edge " << edge_num << " has no edge object";
      simerr_report_error_code(SP_EER_SURFEL_NEIGHBOR_MISSING, 
                               surfel->scale(), 
                               surfel->centroid, 
                               error_string.str().c_str(),
                               edge_midpoint[0],
                               edge_midpoint[1],
                               edge_midpoint[2]);
      continue;  //Other edges of this surfel may not be null, we should check them.
    }

    if (edge->neighbor_surfel == NULL) {
      // Test if this edge is on a simvol wall
      bool edge_on_simvol = false;
      ccDOTIMES(axis, N_AXES){
        if((vertex_1->coord[axis] > (-1e-7) && vertex_1->coord[axis] < (1e-7) && vertex_2->coord[axis] > (-1e-7) && vertex_2->coord[axis] < (1e-7)) 
            ||(vertex_1->coord[axis] > (sim.simvol_size[axis] - 1e-7) && vertex_1->coord[axis] < (sim.simvol_size[axis] + 1e-7)
            && vertex_2->coord[axis] > (sim.simvol_size[axis] - 1e-7) && vertex_2->coord[axis] < (sim.simvol_size[axis] + 1e-7))){
          edge_on_simvol = true;
          break;
        }
      }
      
      if(edge_on_simvol) continue;

      std::ostringstream error_string;
      error_string << "surfel's edge " << edge_num << " has null neighbor reference";
      simerr_report_error_code(
                                 SP_EER_SURFEL_NEIGHBOR_MISSING, 
                                 surfel->scale(), 
                                 surfel->centroid, 
                                 error_string.str().c_str(),
                                 edge_midpoint[0],
                                 edge_midpoint[1],
                                 edge_midpoint[2]);
    }
  }
}
#endif

VOID check_surfel_neighbor_ublk(SURFEL surfel){

  if(surfel->is_ghost() || surfel->is_film_only_ghost() || surfel->is_inlet_or_outlet())
    return;

  if (surfel->p_data()->neighbor_ublks.size() == 0) {
    std::ostringstream error_string;
    error_string << "surfel p_data neighbor_ublks is null";
    simerr_report_error_code(SP_EER_SURFEL_NEIGHBOR_UBLK_MISSING,
                             surfel->scale(),
                             cast_as_regular_array(surfel->centroid),
                             error_string.str().c_str());
  }
}

VOID sPARTICLE_SIM::check_surfel_neighbor(){
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      check_surfel_edge_neighbor(surfel);
      check_surfel_neighbor_ublk(surfel);
    }
  }
}


