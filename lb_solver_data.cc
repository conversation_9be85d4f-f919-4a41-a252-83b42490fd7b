/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#include "sim.h"
#include "lb_solver_data.h"
#include "ublk.h"
#include "shob.h"
#include "surfel.h"
#include "sampling_surfel.h"

template<>
VOID sSURFEL_LB_DATA::pre_advect_init_copy_even_to_odd(SURFEL_LB_DATA even_surfel_lb_data, 
                                                       STP_PHYS_VARIABLE even_density,
                                                       STP_SURFEL_WEIGHT mme_weight_inverse, 
                                                       STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) 
{
#if SURFEL_LOCAL_DATA
  momentum[0]    = even_surfel_lb_data->momentum[0];
  momentum[1]    = even_surfel_lb_data->momentum[1];
  momentum[2]    = even_surfel_lb_data->momentum[2];
  cf_n           = even_surfel_lb_data->cf_n * mme_weight_inverse;
  grads.gradp[0] = even_surfel_lb_data->grads.gradp[0] * mme_weight_inverse;
  grads.gradp[1] = even_surfel_lb_data->grads.gradp[1] * mme_weight_inverse;
  grads.gradp[2] = even_surfel_lb_data->grads.gradp[2] * mme_weight_inverse;
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  delp_factor    = even_surfel_lb_data->delp_factor * mme_weight_inverse;
#endif

  // visc is only used for LRF surfels. Perhaps we should have a separate type of surfel.
  visc           = even_surfel_lb_data->visc; 
  // u_bar is not copied because it is irrelevant for even and odd surfels. Specifically,
  // the weight applied to u_bar in surfel dynamics (sSURFEL::in_states_voxel_weight) is 
  // 0 by construction.
#endif
}

template<>
VOID sSURFEL_LB_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_LB_DATA mirror_lb_data, 
                                                                STP_LATVEC_MASK latvec_state_mask,
                                                                //STP_STATE_INDEX reflected_states[N_STATES],
                                                                STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES], //new
                                                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
}

template<>
VOID sSURFEL_LB_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_LB_DATA source_lb_data, 
                                                                STP_LATVEC_MASK latvec_state_mask,
                                                                //STP_STATE_INDEX reflected_states[N_STATES],
                                                                STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES], //new
                                                                STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  ustar_0 = source_lb_data->ustar_0;
  ustar_0_pair = source_lb_data->ustar_0_pair;

  u_bar_ratio[0] = source_lb_data->u_bar_ratio[0];
  u_bar_ratio[1] = source_lb_data->u_bar_ratio[1];
  
#if BUILD_D39_LATTICE || BUILD_6X_SOLVER
  u_bar_ratio[2] = source_lb_data->u_bar_ratio[2];
  u_bar_ratio[3] = source_lb_data->u_bar_ratio[3];
#elif BUILD_5G_LATTICE
  rho_bar_ratio = source_lb_data->rho_bar_ratio;
  //potential = source_lb_data->potential;
#endif
}

template<>
VOID sSURFEL_FROZEN_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_FROZEN_DATA source_frozen_data,
                                                                    STP_LATVEC_MASK latvec_state_mask,
                                                                    STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES], //new
                                                                    STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  ustar_lb = source_frozen_data->ustar_lb;
  density_frozen = source_frozen_data->density_frozen;
  ccDOTIMES(i, N_AXES)
    vel_frozen[i] = source_frozen_data->vel_frozen[i] * velocity_mirror_sign_factor[i];
}

VOID sSAMPLING_SURFEL_LB_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_LB_DATA even_surfel_lb_data)
{
  memcpy(this, even_surfel_lb_data, sizeof(*this));
}

#ifdef BUILD_GPU
inline namespace
SIMULATOR_NAMESPACE {

VOID copy_beta_pbl_pde_factors(sNEAR_UBLK_GEOM_DATA& ublk_data,
			       tNEARBLK_GEOM_DATA<HMBLK_SDFLOAT_TYPE_TAG>& mblk_data,
			       int child_ublk) {

  if (ublk_data.beta_factors_present) {
    if (!mblk_data.beta_factors_present.any()) {
      mblk_data.beta_factors =
        hd_slab_malloc<sHMBLK::sNEARBLK_GEOM_DATA::sV2S_BETA_FACTORS>(HD_SLAB_ALLOCATOR_TYPES::MBLK_SOLVER_DATA);
    }

    mblk_data.beta_factors_present.set(child_ublk);
    
    int voxel_offset = child_ublk * 8;
    for (int v = 0; v < 8; v++) {
      for (int l = 0; l < N_LATTICE_VECTORS; l++) {
	mblk_data.beta_factors->beta[v + voxel_offset][l] = ublk_data.beta_factors->beta[v][l];
      }
    }
  }

  if (ublk_data.pbl_factors_2_present) {
    if (!mblk_data.pbl_factors_2_present.any()) {
      mblk_data.pbl_factors_2 =
        hd_slab_malloc<sHMBLK::sNEARBLK_GEOM_DATA::sV2S_PBL_FACTORS>(HD_SLAB_ALLOCATOR_TYPES::MBLK_SOLVER_DATA);
    }

    mblk_data.pbl_factors_2_present.set(child_ublk);
    
    int voxel_offset = child_ublk * 8;
    for (int v = 0; v < 8; v++) {
      for (int l = 0; l < N_FACES; l++) {
	mblk_data.pbl_factors_2->alpha[v + voxel_offset][l] = ublk_data.pbl_factors_2->alpha[v][l];
      }
    }
  }

  if (ublk_data.pde_scale_factors_present) {
    if (!mblk_data.pde_scale_factors_present.any()) {
      mblk_data.pde_scale_factors = 
        hd_slab_malloc<sHMBLK::sNEARBLK_GEOM_DATA::sVR_PDE_S2V_SCALE_FACTORS>(HD_SLAB_ALLOCATOR_TYPES::MBLK_SOLVER_DATA);
    }

    mblk_data.pde_scale_factors_present.set(child_ublk);
    
    int voxel_offset = child_ublk * 8;
    for (int v = 0; v < 8; v++) {
      for (int l = 0; l < N_FACES; l++) {
	mblk_data.pde_scale_factors->alpha[v + voxel_offset][l] = ublk_data.pde_scale_factors->alpha[v][l];
      }
    }
  }  
}

INIT_MSFL(tSURFEL_V2S_LB_DATA) {
  VEC_COPY_SFL_TO_MSFL(m_grads.gradp, 3);
  COPY_SFL_TO_MSFL(m_density);
  VEC_COPY_SFL_TO_MSFL(m_momentum, 3);
  VEC_COPY_SFL_TO_MSFL(m_in_state_scale_factors_lb, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(m_in_states, N_SURFEL_PGRAM_VOLUMES);
  COPY_SFL_TO_MSFL(m_visc);  
  COPY_SFL_TO_MSFL(m_cf_n);

#if BUILD_5G_LATTICE || BUILD_D39_LATTICE || BUILD_6X_SOLVER
  VEC_COPY_SFL_TO_MSFL(m_u_bar, N_SURFEL_PGRAM_VOLUMES); // V2S and S2V
#else
  VEC2_COPY_SFL_TO_MSFL(m_u_bar, N_SURFEL_PGRAM_VOLUMES, 3); // V2S and S2V
#endif
#if BUILD_5G_LATTICE
  VEC_COPY_SFL_TO_MSFL(m_rho_bar, N_SURFEL_PGRAM_VOLUMES);
#endif

  VEC_COPY_SFL_TO_MSFL(m_out_flux, N_SURFEL_PGRAM_VOLUMES);
}


INIT_MSFL(tSURFEL_LB_DATA)
{
  COPY_SFL_TO_MSFL(density_prime);
#if BUILD_D39_LATTICE || BUILD_6X_SOLVER
  VEC_COPY_SFL_TO_MSFL(u_bar_ratio, 4);  
#else
  VEC_COPY_SFL_TO_MSFL(u_bar_ratio, 2);
#endif

#if BUILD_5G_LATTICE
  COPY_SFL_TO_MSFL(potential);
  COPY_SFL_TO_MSFL(rho_bar_ratio);
#endif
  VEC_COPY_SFL_TO_MSFL(pgram_volumes, N_SURFEL_PGRAM_VOLUMES);
  
  COPY_SFL_TO_MSFL(ustar_0);
  COPY_SFL_TO_MSFL(ustar_0_pair);

  COPY_SFL_TO_MSFL(inverse_tot_pgvm);
  COPY_SFL_TO_MSFL(boundary_condition_type);

  COPY_SFL_TO_MSFL(m_attribs.m_home_voxel_requires_free_slip);
  COPY_SFL_TO_MSFL(m_attribs.m_is_mlrf_surfel_in_ring);
  COPY_SFL_TO_MSFL(m_attribs.m_is_coupling_meas_surfel);
#if BUILD_5G_LATTICE || BUILD_6X_SOLVER
  COPY_SFL_TO_MSFL(m_attribs.m_is_interacting_io_surfel);
#endif
}

INIT_MSFL(tSURFEL_S2S_LB_DATA) {
  VEC2_COPY_SFL_TO_MSFL(m_out_flux, N_TIME_INDICES, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(ustar_0, N_TIME_INDICES);
}

INIT_MSFL(tSURFEL_FROZEN_DATA) {
  COPY_SFL_TO_MSFL(ustar_lb);
  COPY_SFL_TO_MSFL(density_frozen);
  VEC_COPY_SFL_TO_MSFL(vel_frozen, 3);  
}
}//inline SIMULATOR_NAMESPACE
#endif
