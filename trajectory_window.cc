/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "common_sp.h"
#include "trajectory_window.h"
#include "trajectory_meas.h"
#include "particle_emitters.h"

#define CONSISTENCY_CHECKS 1

std::vector<TRAJECTORY_WINDOW> g_trajectory_windows;
asINT32 sTRAJECTORY_WINDOW::trajectory_window_count = 0;

static std::vector<sINT32> g_index_in_timestep;  // g_index_in_timestep[trajectory_window][emitter]

VOID reset_parcel_index_in_timestep()
{
  g_index_in_timestep.resize(g_trajectory_windows.size() * g_trajectory_id_map.total_pri_emitters());
  g_index_in_timestep.assign(g_index_in_timestep.size(), 0);
}

static inline sINT32 &index_in_timestep(asINT32 trajectory_window, asINT32 emitter)
{ return g_index_in_timestep[ trajectory_window * g_trajectory_id_map.total_pri_emitters() + emitter]; }


auINT32 abstract_emitter_id(PARCEL_STATE parcel)
{
  asINT32 sim_emitter_id  = parcel->emitter_id;
  asINT32 cdi_emitter_id = sPARTICLE_EMITTER_BASE::m_sim_to_cdi_emitter_id[sim_emitter_id];
  // Do we need to use the special PRI "merge" emitter?
  if(parcel->aggl_applied == 0) {
    return cdi_emitter_id;
  } else {
#if ENABLE_CONSISTENCY_CHECKS
    if (!sim.enable_film_trajectory_measurement) {
      asINT32 this_state_index = parcel->relative_timestep_to_state_index(0);
      msg_print("Parcel %d:%d at %.10g %.10g %.10g with state %d:%s is being measure as a merged parcel at time %ld.",
                parcel->originating_sp,
                parcel->id,
                parcel->x[this_state_index][0],
                parcel->x[this_state_index][1],
                parcel->x[this_state_index][2],
                parcel->dynamics_type,
                parcel->dynamics_name(),
                g_timescale.m_time);
    }
#endif
    return g_trajectory_id_map.total_pri_emitters() - 1;
  }
}

VOID assign_parcel_a_remapable_id(PARCEL_STATE parcel, 
                                  asINT32 trajectory_window) // name space that only include trajectory windows
{
  auINT32 emitter_id = abstract_emitter_id(parcel); //account for the ficticious MERGED emitter PRI uses for particles
                                                    //that have aglomerated.
  parcel->first_measured_sp = my_proc_id;
  parcel->first_measured_ts = g_timescale.time_flow();
  parcel->order_in_measured_ts = index_in_timestep(trajectory_window, emitter_id) ++;
}


sTRAJECTORY_WINDOW::sTRAJECTORY_WINDOW(asINT32 window_index, cDGF_MEAS_WINDOW* lgi_record, std::string name) {
  if(trajectory_window_count > 64) {
    msg_error("Too many trajectory measurement windows are defined for this case.  "
              "Maximum allowed number of trajectory measurment windows is 64.");
  }
  index = window_index;
  m_window_index = window_index;
  meas_window_type = LGI_TRAJECTORY_WINDOW;
  m_trajectory_window_index = trajectory_window_count++;
  m_name = name;
  m_is_particle_trajectory_window = TRUE;
  n_global_stationary_meas_cells = 0;

  n_stationary_meas_cells = 0;
  n_global_moving_meas_cells = 0;


  n_global_stationary_meas_cells = lgi_record->num_meas_cells - lgi_record->num_moving_meas_cells;
  n_global_moving_meas_cells = lgi_record->num_moving_meas_cells;

  //Further options must be set with a call to the set_options(LGI_TRAJECTORY_WINDOW_OPTIONS_REC&) memeber function.
}

VOID sTRAJECTORY_WINDOW::record_startpoint(PARCEL_STATE parcel, 
                                           PARTICLE_EVENT_TYPE event_type, 
                                           PARCEL_STATE parent_parcel) {
#if CONSISTENCY_CHECKS
  if (parent_parcel && parent_parcel->first_measured_ts >= 0 && g_trajectory_id_map.is_timestep_before_map_range(parent_parcel->first_measured_ts)) {
    // Somehow the local ID for this particle was not converted to a global ID
#if ENABLE_CONSISTENCY_CHECKS
    msg_warn("Dropped a startpoint parent at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
             g_timescale.m_time, parent_parcel->first_measured_ts, parent_parcel->first_measured_sp, parent_parcel->order_in_measured_ts, abstract_emitter_id(parent_parcel));
#endif
    parent_parcel = NULL;
  }
#endif

  sTRAJECTORY_STARTPOINT startpoint;
  startpoint.window_index = m_window_index;
  startpoint.sp_index = parcel->first_measured_sp;
  startpoint.ts_index = parcel->first_measured_ts;
  startpoint.order_in_ts = parcel->order_in_measured_ts;
  startpoint.timestep = g_timescale.time_flow();
  startpoint.emitter_id = abstract_emitter_id(parcel);
  startpoint.diameter = parcel->d;
  startpoint.density = parcel->rho;
  startpoint.birth_time = parcel->creation_timestep;
  startpoint.event_type = event_type;
  startpoint.num_subrecords = parent_parcel == NULL ? 0 : 1;
  sp_trajectory_startpoint_manager.record(startpoint);

  //This is only usable for when there is only one parent from the same emitter id (e.g. REFLECTION but not for MERGE )
  if(parent_parcel != NULL) {
    sTRAJECTORY_STARTPOINT parent_info;
    parent_info.window_index = m_window_index;
    parent_info.sp_index = parent_parcel->first_measured_sp;
    parent_info.ts_index = parent_parcel->first_measured_ts;
    parent_info.order_in_ts = parent_parcel->order_in_measured_ts;
    parent_info.timestep = startpoint.timestep;
    parent_info.emitter_id = abstract_emitter_id(parcel); //This must be the same as the emitter ID of the new parcel
    parent_info.event_type = event_type;  //It is important to set this, because if this is an EVENT_MERGE, then the CP
                                          //will decide to update the state of the parent when the child's startpoint
                                          //is received.  Otherwise, pri states are only updated when hitpoints are 
                                          //received.  For merge events, the parents state needs to be updated when 
                                          //the child's startpoint is received becasue no hitpoints are generated for 
                                          //the parent.  If this is set improperly, the CP will create two state updates 
                                          //for the same particle resulting in an inconsisten PMR file.
    parent_info.num_subrecords = 0;
    parent_info.birth_time = parent_parcel->creation_timestep;

    // The following should contain the emitter id of the parent parcel (for merge events it can be diffrent however 
    // record_multiple_parents() is used in that case).
    parent_info.reference_emitter_id = abstract_emitter_id(parent_parcel);
    sp_trajectory_startpoint_manager.record(parent_info);
  }

}

VOID sTRAJECTORY_WINDOW::record_vertex(PARCEL_STATE parcel, 
                                       sPARTICLE_VAR position[N_SPACE_DIMS], //transformed in case of LRF
                                       sPARTICLE_VAR velocity[N_SPACE_DIMS],
                                       PARTICLE_EVENT_TYPE event_type ) {

#if CONSISTENCY_CHECKS
  if (parcel->first_measured_ts >= 0 && g_trajectory_id_map.is_timestep_before_map_range(parcel->first_measured_ts)) {
    // Somehow the local ID for this particle was not converted to a global ID
#if ENABLE_CONSISTENCY_CHECKS
    msg_warn("Dropped a vertex at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d, parcel: %d:%d",
             g_timescale.m_time, 
             parcel->first_measured_ts, 
             parcel->first_measured_sp, 
             parcel->order_in_measured_ts, 
             abstract_emitter_id(parcel), 
             parcel->originating_sp,
             parcel->id);
#endif
    return;
  }
#endif

  //Populate a vertex struct and queue it up for transmission to the CP.
  sTRAJECTORY_VERTEX vertex;
  //asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
  vertex.window_index = m_window_index;
  //vcopy(vertex.x, parcel->x[this_timestep_index]);
  //vcopy(vertex.v, parcel->v[this_timestep_index]);
  vcopy(vertex.x, position);
  vcopy(vertex.v, velocity);
  vertex.sp_index = parcel->first_measured_sp;;
  vertex.ts_index = parcel->first_measured_ts;
  vertex.order_in_ts = parcel->order_in_measured_ts;
  vertex.emitter_id = abstract_emitter_id(parcel);
  vertex.event_type = event_type;
  vertex.timestep = g_timescale.time_flow();
  vertex.diameter = parcel->d;
  vertex.temperature = parcel->temperature;
  sp_trajectory_vertex_manager.record(vertex);
}

VOID sTRAJECTORY_WINDOW::record_hitpoint(PARCEL_STATE parcel,
                                         sPARTICLE_VAR position[N_SPACE_DIMS], //Transformed for LRFs.
                                         sPARTICLE_VAR fractional_time_of_impact,
                                         asINT32 surface_index,
                                         sPARTICLE_VAR inbound_velocity[N_SPACE_DIMS],
                                         sPARTICLE_VAR outbound_velocity[N_SPACE_DIMS],
                                         STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                         PARTICLE_EVENT_TYPE event_type)
{
#if CONSISTENCY_CHECKS
  if (parcel->first_measured_ts >= 0 && g_trajectory_id_map.is_timestep_before_map_range(parcel->first_measured_ts)) {
    // Somehow the local ID for this particle was not converted to a global ID
#if ENABLE_CONSISTENCY_CHECKS
    msg_warn("Dropped a hitpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
             g_timescale.m_time, parcel->first_measured_ts, parcel->first_measured_sp, parcel->order_in_measured_ts, abstract_emitter_id(parcel));
#endif
    return;
  }
#endif

  //Create a hitpoint record for this parcel
  sTRAJECTORY_HITPOINT hitpoint;
  hitpoint.window_index = m_window_index;
  hitpoint.sp_index = parcel->first_measured_sp;
  hitpoint.ts_index = parcel->first_measured_ts;
  hitpoint.order_in_ts = parcel->order_in_measured_ts;
  hitpoint.timestep = g_timescale.time_flow();

  hitpoint.emitter_id = abstract_emitter_id(parcel);
  hitpoint.time_of_impact = g_timescale.time_flow() + fractional_time_of_impact + 1; //One is added because collision's were detected against the parcel state computed for the next timesteps initial condition.
  //asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
  vcopy(hitpoint.x, position);
  hitpoint.surface_index = surface_index;
  vcopy(hitpoint.impact_velocity, inbound_velocity);
  vcopy(hitpoint.normal, surface_normal);
  hitpoint.event_type = event_type;
  //Queue it up to be sent to the CP.
  sp_trajectory_hitpoint_manager.record(hitpoint);
}

VOID sTRAJECTORY_WINDOW::flag_record_for_parent_child_data(sTRAJECTORY_PARENT_CHILD_DATA_BASE &control_record,
                                                           PARCEL_STATE parcel,
                                                           asINT32 num_subrecords,
                                                           asINT32 abstract_source_emitter_id) //"source" refers to the side of the bifurcation with one element
{
  control_record.window_index = m_window_index;
  control_record.sp_index = parcel->first_measured_sp;
  control_record.ts_index = parcel->first_measured_ts;
  control_record.order_in_ts = parcel->order_in_measured_ts;
  control_record.timestep = g_timescale.time_flow();
  //The following must have the emitter id of the particle on the singular side of the bifurcation so it is sorted to 
  //the correct buffer on the CP.  Also it might have the fictiotious MERGE emitter id.
  control_record.emitter_id = abstract_source_emitter_id;

  //The CP uses the following to get the emitter id of multiple children or multiple parents.
  control_record.reference_emitter_id = abstract_emitter_id(parcel);
  control_record.num_subrecords = num_subrecords;
}

//This routine is needed due to splash and agglomeration algorithms and works by sending specially flagged hitpoint
//records.
VOID sTRAJECTORY_WINDOW::record_hitpoint_with_children(PARCEL_STATE parent_parcel,
                                                       sPARTICLE_VAR position[N_SPACE_DIMS], //transformed in case of LRF
                                                       sPARTICLE_VAR fractional_time_of_impact,
                                                       asINT32 surface_index,
                                                       sPARTICLE_VAR inbound_velocity[N_SPACE_DIMS],
                                                       sPARTICLE_VAR outbound_velocity[N_SPACE_DIMS],
                                                       STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                                       PARTICLE_EVENT_TYPE event_type,
                                                       std::vector<PARCEL_STATE> &child_parcels,
                                                       std::vector<PARTICLE_EVENT_TYPE> &child_source_flags)
{

  if(child_parcels.size() == 0) {
    //PR41946:  this can happen if surface trajectories are disabled and particles 
    //don't hit with enough energy enough to splash in which case the only child would
    //be the film remnant that is excluded from the child parcel list.
    record_hitpoint(parent_parcel,
                    position,
                    fractional_time_of_impact,
                    surface_index,
                    inbound_velocity,
                    outbound_velocity,
                    surface_normal,
                    event_type);
    
    return;
  }
  
#if CONSISTENCY_CHECKS
  if (parent_parcel->first_measured_ts >= 0 && g_trajectory_id_map.is_timestep_before_map_range(parent_parcel->first_measured_ts)) {
    // Somehow the local ID for this particle was not converted to a global ID
#if ENABLE_CONSISTENCY_CHECKS
    msg_warn("Dropped a multi-child hitpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
             g_timescale.m_time, parent_parcel->first_measured_ts, parent_parcel->first_measured_sp, parent_parcel->order_in_measured_ts, abstract_emitter_id(parent_parcel));
#endif
    return;
  }  
#endif

  //Create a hitpoint record for this parcel
  sTRAJECTORY_HITPOINT hitpoint;
  hitpoint.window_index = m_window_index;
  hitpoint.sp_index = parent_parcel->first_measured_sp;
  hitpoint.ts_index = parent_parcel->first_measured_ts;
  hitpoint.timestep = g_timescale.time_flow();
  hitpoint.order_in_ts = parent_parcel->order_in_measured_ts;
  hitpoint.emitter_id = abstract_emitter_id(parent_parcel);
  hitpoint.time_of_impact = g_timescale.time_flow() + fractional_time_of_impact + 1;  //One is added because collision's were detected against the parcel state computed for the next timesteps initial condition.
  //asINT32 this_timestep_index = parent_parcel->relative_timestep_to_state_index(0);
  vcopy(hitpoint.x, position);
  hitpoint.surface_index = surface_index;
  vcopy(hitpoint.impact_velocity, inbound_velocity);
  vcopy(hitpoint.normal, surface_normal);
  hitpoint.event_type = event_type;

#if CONSISTENCY_CHECKS
  asINT32 num_good_children = 0;
  ccDOTIMES(child_index, child_parcels.size()) {
    PARCEL_STATE child_parcel = child_parcels[child_index];
    if (child_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(child_parcel->first_measured_ts))
      num_good_children++;
    else {
#if ENABLE_CONSISTENCY_CHECKS
      msg_warn("Dropped a child from a hitpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
               g_timescale.m_time, child_parcel->first_measured_ts, child_parcel->first_measured_sp, child_parcel->order_in_measured_ts, abstract_emitter_id(child_parcel));
#endif
    }
  }

  if (num_good_children == 0)
    return;
#else
  asINT32 num_good_children = child_parcels.size();
#endif

  //set the hitpoint record to indicate subsequent records with children should be expected.
  flag_record_for_parent_child_data(hitpoint, parent_parcel, num_good_children, abstract_emitter_id(parent_parcel));

  //Queue up the hitpoint record to be sent to the CP.
  sp_trajectory_hitpoint_manager.record(hitpoint); 


  //Create "subrecord(s)" with each child's ID and source flags.  The CP will scan for the above control records and 
  //know how many subrecords to expect from the num_subrecords field.
  ccDOTIMES(child_index, child_parcels.size()) {
    PARCEL_STATE child_parcel = child_parcels[child_index];
#if CONSISTENCY_CHECKS
    if (child_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(child_parcel->first_measured_ts)) 
#endif
    {
      sTRAJECTORY_HITPOINT subrecord;
      flag_record_for_parent_child_data(subrecord, child_parcel, 0, abstract_emitter_id(parent_parcel));
      subrecord.event_type = child_source_flags[child_index];  //These end up being used for the particle's source flags 
      //which are included in the pmr file.
      sp_trajectory_hitpoint_manager.record(subrecord);
    }
  }
}


VOID sTRAJECTORY_WINDOW::record_multiple_children(PARCEL_STATE parent_parcel,
                                                  PARTICLE_EVENT_TYPE event_type,
                                                  std::vector<PARCEL_STATE> &child_parcels,
                                                  std::vector<PARTICLE_EVENT_TYPE> &child_source_flags)
{

  if(child_parcels.size() == 0) return;

#if CONSISTENCY_CHECKS
  if (parent_parcel->first_measured_ts >= 0 && g_trajectory_id_map.is_timestep_before_map_range(parent_parcel->first_measured_ts)) {
    // Somehow the local ID for this particle was not converted to a global ID
#if ENABLE_CONSISTENCY_CHECKS
    msg_warn("Dropped a multi-child hitpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
             g_timescale.m_time, parent_parcel->first_measured_ts, parent_parcel->first_measured_sp, parent_parcel->order_in_measured_ts, abstract_emitter_id(parent_parcel));
#endif
    return;
  }

  asINT32 num_good_children = 0;
  ccDOTIMES(child_index, child_parcels.size()) {
    PARCEL_STATE child_parcel = child_parcels[child_index];
    if (child_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(child_parcel->first_measured_ts))
      num_good_children++;
    else
      msg_print("Dropped a child from a hitpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
                g_timescale.m_time, child_parcel->first_measured_ts, child_parcel->first_measured_sp, child_parcel->order_in_measured_ts, abstract_emitter_id(child_parcel));
  }

  if (num_good_children == 0)
    return;
#else
  asINT32 num_good_children = child_parcels.size();
#endif

  //Create the "control record" with the parent parcel's ID
  sTRAJECTORY_HITPOINT control_record;
  flag_record_for_parent_child_data(control_record, parent_parcel, num_good_children, abstract_emitter_id(parent_parcel));
  control_record.event_type = event_type;
  sp_trajectory_hitpoint_manager.record(control_record);

  //Create a "subrecord" with each child's ID and source flags.  The CP will scan for the above control records and 
  //know how many subrecords to expect from the num_subrecords field.
  ccDOTIMES(child_index, child_parcels.size()) {
    PARCEL_STATE child_parcel = child_parcels[child_index];
#if CONSISTENCY_CHECKS
    if (child_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(child_parcel->first_measured_ts)) 
#endif
    {
      sTRAJECTORY_HITPOINT subrecord;
      flag_record_for_parent_child_data(subrecord, child_parcel, 0, abstract_emitter_id(parent_parcel));
      //The followingend up being used for the particles source flags written to the pmr file.
      subrecord.event_type = child_source_flags[child_index];
      sp_trajectory_hitpoint_manager.record(subrecord);
    }
  }
}

//This routine uses specially flagged startpoint records to describe when a child has multiple parents.
//This situation is expected to occur only due to the surface parcel agglomeraton algorithm and the event type should
//always be MERGE.
VOID sTRAJECTORY_WINDOW::record_multiple_parents(PARCEL_STATE child_parcel,
                                                 PARTICLE_EVENT_TYPE event_type,
                                                 std::vector<PARCEL_STATE> &parent_parcels)
{

  if(event_type != EVENT_MERGE) {
    msg_error("Tried to record multiple parents for an event other than MERGE.\n");
  }

  //Create a startpoint record with the child parcel's ID and be sure to mark it as a MERGE event.
  sTRAJECTORY_STARTPOINT control_record;
  control_record.window_index = m_window_index;
  //The following lets the CP know there may be more than one subrecord for parents with different emitter IDs.
  control_record.event_type = EVENT_MERGE;

#if CONSISTENCY_CHECKS
  asINT32 num_good_parents = 0;
  ccDOTIMES(parent_index, parent_parcels.size()) {
    PARCEL_STATE parent_parcel = parent_parcels[parent_index];
    if (parent_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(parent_parcel->first_measured_ts))
      num_good_parents++;
    else {
#if ENABLE_CONSISTENCY_CHECKS
      msg_print("Dropped a parent from startpoint at t = %ld: first meas ts: %d, first meas sp: %d, local ID: %d, emitter: %d",
                g_timescale.m_time, parent_parcel->first_measured_ts, parent_parcel->first_measured_sp, parent_parcel->order_in_measured_ts, abstract_emitter_id(parent_parcel));
#endif
    }
  }
#else
  asINT32 num_good_parents = parent_parcels.size();
#endif

  flag_record_for_parent_child_data(control_record, child_parcel, num_good_parents, abstract_emitter_id(child_parcel));
  control_record.diameter = child_parcel->d;
  control_record.density = child_parcel->rho;
  control_record.birth_time = child_parcel->creation_timestep;
  sp_trajectory_startpoint_manager.record(control_record);

  //For each parent parcel, send a "subrecord" with the parents IDs. There can be any number of them and they may come
  //from different emitters
  ccDOTIMES(parent_index, parent_parcels.size()) {
    PARCEL_STATE parent_parcel = parent_parcels[parent_index];
#if CONSISTENCY_CHECKS
    if (parent_parcel->first_measured_ts < 0 || !g_trajectory_id_map.is_timestep_before_map_range(parent_parcel->first_measured_ts)) 
#endif
    {
      sTRAJECTORY_STARTPOINT subrecord;
      flag_record_for_parent_child_data(subrecord, parent_parcel, 0, abstract_emitter_id(child_parcel));
      subrecord.event_type = EVENT_MERGE; 
      sp_trajectory_startpoint_manager.record(subrecord);
    }
  }
}
