/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "gpu_shobs.hcu"
#include "gpu_globals.hcu"
#include "shob_groups.h"
#include "box_advect.h"
#include "gpu_swap_advect.hcu"

#define DEBUG_GPU_ADVECTION 0

namespace GPU {

//Re-use this constant in swap advection
namespace GATHER_ADVECT {
extern __constant__ int8_t g_state_velocities[N_STATES][3];    
}

using GATHER_ADVECT::g_state_velocities;

_ALWAYS_INLINE_
__device__ void swap_voxel_states(const int dest_voxel,
                                  const int src_voxel,
                                  VOXEL_STATE (*states) [sMBLK::N_VOXELS],
                                  VOXEL_STATE (*src_states) [sMBLK::N_VOXELS],
                                  int latvec,
                                  BOOLEAN src_ublk_has_two_copies) {

  VOXEL_STATE tmp;
  asINT32 parity = state_parity(latvec);
  tmp = src_states[latvec][src_voxel];
  if (!src_ublk_has_two_copies) {
    src_states[latvec][src_voxel] = states[parity][dest_voxel];
  }
  states[parity][dest_voxel] = tmp;
}

constexpr size_t ADVECT_GRID_DIM_X = 6;
constexpr size_t ADVECT_GRID_DIM_Y = 6;
constexpr size_t ADVECT_GRID_DIM_Z = 6;
using THREAD_ADVECT_GRID =
  float[ADVECT_GRID_DIM_X][ADVECT_GRID_DIM_Y][ADVECT_GRID_DIM_Z];

using TWO_COPY_NBR_GRID =
  uINT8[ADVECT_GRID_DIM_X][ADVECT_GRID_DIM_Y][ADVECT_GRID_DIM_Z];

using HALO_CHOICE = enum {
  INCLUDE_HALO,
  EXCLUDE_HALO
};

__device__
INLINE bool is_nbr_within_box_boundary_along_latvec(int latvec,
                                                    int pt_x,
                                                    int pt_y,
                                                    int pt_z,
                                                    HALO_CHOICE halo_choice) {

  const int8_t* offsets = g_state_velocities[latvec];
  
  int pt_x_nbr = pt_x + offsets[0];
  int pt_y_nbr = pt_y + offsets[1];
  int pt_z_nbr = pt_z + offsets[2];

  if (halo_choice == EXCLUDE_HALO) {
      
    return \
      (pt_x_nbr > 0) && (pt_x_nbr < ADVECT_GRID_DIM_X - 1) &&
      (pt_y_nbr > 0) && (pt_y_nbr < ADVECT_GRID_DIM_Y - 1) &&
      (pt_z_nbr > 0) && (pt_z_nbr < ADVECT_GRID_DIM_Z - 1);
      
  } else if (halo_choice == INCLUDE_HALO) {
      
    return \
      (pt_x_nbr >= 0) && (pt_x_nbr < ADVECT_GRID_DIM_X) &&
      (pt_y_nbr >= 0) && (pt_y_nbr < ADVECT_GRID_DIM_Y) &&
      (pt_z_nbr >= 0) && (pt_z_nbr < ADVECT_GRID_DIM_Z);
      
  } else {
    return false;
  }
}

__device__
INLINE bool is_interior_grid_pt(int pt_x,
                                int pt_y,
                                int pt_z) {

  return  \
    (pt_x > 0) && (pt_x < ADVECT_GRID_DIM_X - 1) &&
    (pt_y > 0) && (pt_y < ADVECT_GRID_DIM_Y - 1) &&
    (pt_z > 0) && (pt_z < ADVECT_GRID_DIM_Z - 1);
}
  
struct sGRID_PT {
  int m_x;
  int m_y;
  int m_z;
  explicit __device__ sGRID_PT(int x, int y, int z) : m_x(x), m_y(y), m_z(z) {}
};

_INLINE_
__device__ sGRID_PT get_grid_pt_for_offset_voxel(sINT16* offsets,
                                                 int voxel) {

  constexpr int coordinate_offset = 3;

#if DEBUG_GPU_ADVECTION
  assert(voxel >=0 && voxel < 64);
#endif
  //Offset + 1, to account for new origin at neighbor (-1, -1, -1)
  int mblk_x = (int) offsets[0] + 1;
  int mblk_y = (int) offsets[1] + 1;
  int mblk_z = (int) offsets[2] + 1;
  //These deltas take care of shift in co-ordinate system from the
  //original origin at voxel 0, grid pt (0, 0, 0) of neighbor (-1, -1, -1) 
  //to voxel 63 of neighbor (-1, -1, -1), grid pt (3, 3, 3)
  int delta_x = (mblk_x << 2) - coordinate_offset;
  int delta_y = (mblk_y << 2) - coordinate_offset;
  int delta_z = (mblk_z << 2) - coordinate_offset;

#if DEBUG_GPU_ADVECTION
  assert(delta_x > -4 && delta_x < 6);
  assert(delta_y > -4 && delta_y < 6);
  assert(delta_z > -4 && delta_z < 6);
#endif

  int ublk_x = (voxel >> 5) & 0x1;
  int voxel_x = (voxel >> 2) & 0x1;
  int ublk_y = (voxel >> 4) & 0x1;
  int voxel_y = (voxel >> 1) & 0x1;
  int ublk_z = (voxel >> 3) & 0x1;
  int voxel_z = voxel & 0x1;

  int x = (ublk_x << 1) + voxel_x + delta_x;
  int y = (ublk_y << 1) + voxel_y + delta_y;
  int z = (ublk_z << 1) + voxel_z + delta_z;

#if DEBUG_GPU_ADVECTION
  if (x < 0 || x > 6) {

    printf("This is thread %d, block %d,"
           "ox %d, oy %d, oz %d,"
           "ublk_x %d, ublk_y %d, ublk_z %d,"
           "vx %d, vy %d, vz %d,"
           "d_x %d, d_y %d, d_z %d\n",
           threadIdx.x, blockIdx.x,
           offsets[0], offsets[1], offsets[2],
           ublk_x, ublk_y, ublk_z,
           voxel_x, voxel_y, voxel_z,
           delta_x, delta_y, delta_z);
  }
  assert(x >=0 && x < 6);
  assert(y >=0 && y < 6);
  assert(z >=0 && z < 6);
#endif
  return sGRID_PT(x, y, z);
}
  
inline __device__  void fill_shared_mem_ext_d(sMBLK* thread_ublk,			       
                                              int latvec_pair,
                                              sGRID_PT& pt,
                                              int thread_voxel,
                                              TWO_COPY_NBR_GRID& shared_mem_nbrs,
                                              THREAD_ADVECT_GRID& shared_mem_latvec,
                                              THREAD_ADVECT_GRID& shared_mem_parity,
                                              SOLVER_INDEX_MASK prior_solver_index_mask) {
    
  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  auto lb_index = thread_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY; 
  int latvec = 2*latvec_pair;
  int parity = latvec + 1;

  shared_mem_nbrs[pt.m_x][pt.m_y][pt.m_z] = thread_ublk->has_two_copies();
  shared_mem_latvec[pt.m_x][pt.m_y][pt.m_z] = thread_ublk->lb_states(lb_index)->m_states[latvec][thread_voxel];
  shared_mem_parity[pt.m_x][pt.m_y][pt.m_z] = thread_ublk->lb_states(lb_index)->m_states[parity][thread_voxel];
}

inline __device__  void swap_shared_mem_ext_d(int latvec_pair,
                                              sGRID_PT& pt,
                                              int voxel,
                                              TWO_COPY_NBR_GRID& shared_mem_nbrs,
                                              THREAD_ADVECT_GRID& shared_mem_latvec,
                                              THREAD_ADVECT_GRID& shared_mem_parity) {

  int latvec = 2*latvec_pair;
  const int8_t* offsets = g_state_velocities[latvec];
    
  float tmp = shared_mem_latvec[pt.m_x][pt.m_y][pt.m_z];
  
  int pt_x_nbr = pt.m_x + offsets[0];
  int pt_y_nbr = pt.m_y + offsets[1];
  int pt_z_nbr = pt.m_z + offsets[2];

  bool is_interior = is_interior_grid_pt(pt.m_x, pt.m_y, pt.m_z);

  if (is_interior){
    bool has_two_copy_latvec_nbr = shared_mem_nbrs[pt_x_nbr][pt_y_nbr][pt_z_nbr];    
    //Perform optimized swap if neighbor is not a two copy neighbor
    //Two copy neighbors push states into the parity state of a swap MBLK first
    //because INFBLK2 strand always goes first before INFBLK1
    //Swapping here a second time would put the states in the wrong slots
    if (!has_two_copy_latvec_nbr) {
      shared_mem_latvec[pt.m_x][pt.m_y][pt.m_z] = shared_mem_parity[pt_x_nbr][pt_y_nbr][pt_z_nbr];
      shared_mem_parity[pt_x_nbr][pt_y_nbr][pt_z_nbr] = tmp;
    }
  }
}

inline __device__  void write_back_shared_mem_ext_d(sMBLK* thread_ublk,			       
                                                    int latvec_pair,
                                                    sGRID_PT& pt,
                                                    int thread_voxel,
                                                    THREAD_ADVECT_GRID& shared_mem_latvec,
                                                    THREAD_ADVECT_GRID& shared_mem_parity) {

  int latvec = 2*latvec_pair;
  int parity = latvec + 1;

  //Threads on the boundary might pick up two copy neighbors of the advecting swap MBLK
  //These MBLKs don't perform swap advection but are involved only for the purposes of
  //filling state data for swap MBLK neighbors
  if (thread_ublk->has_two_copies()) {
    return;
  }
  
  bool is_interior = is_interior_grid_pt(pt.m_x, pt.m_y, pt.m_z);
  
  bool has_valid_parity_nbr = is_nbr_within_box_boundary_along_latvec(parity,
                                                                      pt.m_x, pt.m_y, pt.m_z,
                                                                      EXCLUDE_HALO);

  if (is_interior && has_valid_parity_nbr) {
    //Swap and write back
    thread_ublk->lb_states(ONLY_ONE_COPY)->m_states[latvec][thread_voxel] =
      shared_mem_parity[pt.m_x][pt.m_y][pt.m_z];
    thread_ublk->lb_states(ONLY_ONE_COPY)->m_states[parity][thread_voxel] =
      shared_mem_latvec[pt.m_x][pt.m_y][pt.m_z];
  }
  else {
    if (is_interior) {
      thread_ublk->lb_states(ONLY_ONE_COPY)->m_states[latvec][thread_voxel] =
        shared_mem_latvec[pt.m_x][pt.m_y][pt.m_z];
    }
  
    if (has_valid_parity_nbr) {
      thread_ublk->lb_states(ONLY_ONE_COPY)->m_states[parity][thread_voxel] =
        shared_mem_parity[pt.m_x][pt.m_y][pt.m_z];
    }
  }  
}

inline __device__  void advect_mblk_box_ext_d(sMBLK* advect_ublk,
                                              int latvec_pair,
                                              int interaction,
                                              int thread_voxel,
                                              int child_ublk_offset,
                                              SOLVER_INDEX_MASK prior_solver_index_mask) {  
  
  __shared__ THREAD_ADVECT_GRID shared_mem_latvec;
  __shared__ THREAD_ADVECT_GRID shared_mem_parity;
  __shared__ TWO_COPY_NBR_GRID shared_mem_nbrs;
  
  sMBLK* thread_ublk = advect_ublk;
  sINT16* interaction_offsets = g_nbr_offsets[interaction];

  if (interaction) {
    thread_ublk = advect_ublk->box_access(child_ublk_offset).neighbor_ublk(interaction_offsets).ublk();
  }
    
  sGRID_PT pt = get_grid_pt_for_offset_voxel(interaction_offsets, thread_voxel);    

  fill_shared_mem_ext_d(thread_ublk, latvec_pair, pt, thread_voxel,
                        shared_mem_nbrs, shared_mem_latvec, shared_mem_parity,
                        prior_solver_index_mask);
  __syncthreads();

  swap_shared_mem_ext_d(latvec_pair, pt, thread_voxel,
                        shared_mem_nbrs, shared_mem_latvec, shared_mem_parity);
  __syncthreads();

  write_back_shared_mem_ext_d(thread_ublk, latvec_pair, pt, thread_voxel,
                              shared_mem_latvec, shared_mem_parity);
  __syncthreads();
}  


template<int LP>
__global__ void swap_advect_mblks_ext(UBLK_GROUP_TYPE group_type,
                                      size_t offset,
                                      SOLVER_INDEX_MASK prior_solver_index_mask) {

  size_t ublk_index = offset + blockIdx.x;
  int interaction = MBLK_ADVECT_INTERACTION<LP>::get_interaction(threadIdx.x);
  int voxel = MBLK_ADVECT_INTERACTION<LP>::get_interaction_voxel(threadIdx.x);
  int child_ublk_offset = MBLK_ADVECT_INTERACTION<LP>::get_child_ublk_offset(threadIdx.x);    
  MBLK mblk = g_ublk_groups[group_type].m_ublks[ublk_index];
  int latvec_pair = LP;
#ifdef DEBUG_SWAP_ADVECT
  if (mblk->id() == 44600 && latvec_pair == 1 && voxel == 50) {
    auto l1 = latvec_pair_first_latvec(latvec_pair);
    auto l2 = latvec_pair_second_latvec(latvec_pair);
    printf("Before Swap Ext:: TS %d, mblk %d, v %, lp %d, l1 %5.7e, l2 %5.7e\n",
           get_timescale_ref().m_time, mblk->id(), voxel, latvec_pair,
           mblk->lb_states(0)->m_states[l1][voxel],
           mblk->lb_states(0)->m_states[l2][voxel]);
  }
#endif  
  advect_mblk_box_ext_d(mblk, latvec_pair,
                        interaction, voxel, child_ublk_offset,
                        prior_solver_index_mask);
#ifdef DEBUG_SWAP_ADVECT
  if (mblk->id() == 44600 && latvec_pair == 1 && voxel == 50) {
    auto l1 = latvec_pair_first_latvec(latvec_pair);
    auto l2 = latvec_pair_second_latvec(latvec_pair);
    printf("After Swap Ext:: TS %d, mblk %d, v %, lp %d, l1 %5.7e, l2 %5.7e\n",
           get_timescale_ref().m_time, mblk->id(), voxel, latvec_pair,
           mblk->lb_states(0)->m_states[l1][voxel],
           mblk->lb_states(0)->m_states[l2][voxel]);
  }
#endif
}


_INLINE_  __device__  void swap_mblks_int_d(sMBLK* mblk) {
  
  int thread_voxel = threadIdx.x & 0b111111;
  int latvec_pair = threadIdx.x >> 6;
  sINT16 offsets[3] = {0, 0, 0};

  int latvec = 2*latvec_pair;
  int parity = latvec + 1;
  float tmp = mblk->lb_states(ONLY_ONE_COPY)->m_states[latvec][thread_voxel];
  sGRID_PT pt = get_grid_pt_for_offset_voxel(offsets, thread_voxel);
  
  bool has_valid_parity_nbr = is_nbr_within_box_boundary_along_latvec(parity,
                                                                      pt.m_x, pt.m_y, pt.m_z,
                                                                      EXCLUDE_HALO);

  if (!has_valid_parity_nbr) {
    mblk->lb_states(ONLY_ONE_COPY)->m_states[latvec][thread_voxel] = mblk->lb_states(ONLY_ONE_COPY)->m_states[parity][thread_voxel];
    mblk->lb_states(ONLY_ONE_COPY)->m_states[parity][thread_voxel] = tmp;
  }

#ifdef DEBUG_SWAP_ADVECT
  if (mblk->id() == 44600 && latvec_pair == 1 && thread_voxel == 50) {
    printf("After Swap Int:: TS %d, mblk %d, v %, lp %d, l1 %5.7e, l2 %5.7e\n",
           get_timescale_ref().m_time, mblk->id(), thread_voxel, latvec_pair,
           mblk->lb_states(0)->m_states[latvec][thread_voxel],
           mblk->lb_states(0)->m_states[parity][thread_voxel]);
  }
#endif  
}

__global__ void swap_advect_mblks_int(UBLK_GROUP_TYPE group_type,
                                        size_t offset){

  size_t ublk_index = offset + blockIdx.x;
      
  UBLK mblk = g_ublk_groups[group_type].m_ublks[ublk_index];      
  
  swap_mblks_int_d(mblk);
    
}

  
/*=============================================================
 * @advect_mblk
 * Performs swap advection on a MEGA UBLK with single copy of states
 *=============================================================*/
template<int LP>
using MUAI = MBLK_ADVECT_INTERACTION<LP>;

VOID swap_advect(UBLK_GROUP_TYPE group_type,
                 BOOLEAN is_T_S_lb_solver_on,
		 BOOLEAN is_UDS_lb_solver_on,
                 const size_t range[2],
                 SOLVER_INDEX_MASK prior_solver_index_mask) {
  
  size_t num_advect_ublks = range[1] - range[0];

  assert(num_advect_ublks > 0);

  constexpr unsigned int NUM_LATVEC_PAIRS = N_MOVING_STATES/2;
    
  swap_advect_mblks_ext<0><<<num_advect_ublks, MUAI<0>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<1><<<num_advect_ublks, MUAI<1>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<2><<<num_advect_ublks, MUAI<2>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);
  
  swap_advect_mblks_ext<3><<<num_advect_ublks, MUAI<3>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<4><<<num_advect_ublks, MUAI<4>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

    
  swap_advect_mblks_ext<5><<<num_advect_ublks, MUAI<5>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<6><<<num_advect_ublks, MUAI<6>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<7><<<num_advect_ublks, MUAI<7>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  swap_advect_mblks_ext<8><<<num_advect_ublks, MUAI<8>::num_threads(), NO_DYN_SHMEM, g_stream>>>(
                                                                                                 group_type,
                                                                                                 range[0],
                                                                                                 prior_solver_index_mask);

  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(g_stream) );

  constexpr unsigned int num_swap_threads = sMBLK::N_VOXELS * NUM_LATVEC_PAIRS;
  swap_advect_mblks_int<<<num_advect_ublks, num_swap_threads, NO_DYN_SHMEM, g_stream>>>(group_type,
                                                                                        range[0]);

  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(g_stream) );
}//advect_mblk

}//NAMESPACE GPU



