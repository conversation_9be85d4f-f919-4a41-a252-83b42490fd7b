/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("cp.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Control Process                                   ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1994-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

//----------------------------------------------------------------------------
// James F. Kelly                                             Aug 11, 2013
//----------------------------------------------------------------------------
#ifndef _SIMENG_SP_SCALAR_H
#define _SIMENG_SP_SCALAR_H


#include "common_sp.h"
#include "ublk.h"
#include "surfel.h"
#include "scalar_data.h"


typedef struct sSP_UDS_BOUNDARY_CONDITION {

public:
  cBOOLEAN is_flux;
  asINT32 boundary_condition_type;
  //dFLOAT flux_or_value;
  sPHYSICS_VARIABLE flux_or_value;
  asINT32 face_index;
  sPHYSICS_DESCRIPTOR physics_desc;  // bs physics descriptor
  
  VOID evaluate_uds_boundary_condition(SURFEL surfel, SURFEL_UDS_DATA surfel_uds_data, asINT32 timestep);

} *SP_UDS_BOUNDARY_CONDITION;


typedef struct sSP_UDS_BOUNDARY_REGION {

public:
  asINT32 n_regions;     
  asINT32 *rgn_list;
  asINT32 n_scalars;     
  asINT32 *uds_list;
 
 
} *SP_UDS_BOUNDARY_REGION;



typedef struct sSP_UDS {

public:
//cSTRING name;
  cSTRING unit_class;
  asINT32 n_regions;     
  asINT32 *rgn_list;

  asINT32 first_sri_var;
  asINT32 standard_mask;
  asINT32 start_time;
  asINT32 end_time; 
  dFLOAT  maximum_value;
  asINT32 n_fluid_regions;  // number of uds fluid regions = number of fluid regions - number
                                   // of uds boundary regions that are active for this uds

  sPHYSICS_VARIABLE *initial_conditions;  //initial_condition[n_uds_fluid_regions]; 
  //std :: vector<sSP_UDS_BOUNDARY_CONDITION> boundary_conditions;

  dFLOAT diffusion_coefficient;
  dFLOAT turbulent_schmidt_number;  // turbulent schmidt number (variable)
  //dFLOAT source_term;
  sPHYSICS_VARIABLE source_term;

  sSP_UDS(asINT32 uds_index);

  //extracts booleans from the standard mask
  cBOOLEAN allow_negative_values() {return ((standard_mask >> CDI_UDS_MASK_INDEX_ALLOW_NEGATIVE_VALUES) & 1);}
  cBOOLEAN is_max_value() {return ((standard_mask >> CDI_UDS_MASK_INDEX_IS_MAX_VALUE) & 1);}
  cBOOLEAN is_pde_solver() {return ((standard_mask >> CDI_UDS_MASK_INDEX_IS_PDE_SOLVER) & 1);}
  cBOOLEAN is_constant_density() {return ((standard_mask >> CDI_UDS_MASK_INDEX_IS_CONSTANT_DENSITY) & 1);}
  // For uds mass fraction, turb_schmidt number is the turbulent Prandtl number
  cBOOLEAN is_turb_schmidt_number_specified() {return ((standard_mask >> CDI_UDS_MASK_INDEX_IS_TURB_PRANDTL_NUMBER) & 1);}
  //asINT32 n_uds_bcs() {return boundary_conditions.size();}



  // replace is_uds_active is surfel_dyn, etc.
  cBOOLEAN is_uds_active() {return (sim.time >= start_time && sim.time <= end_time);};

  private:
  

  // inline functions for collection of uds eqn meas vars
  public:
  SRI_VARIABLE_TYPE uds_var_sri_var() {
      return (SRI_VARIABLE_TYPE) (first_sri_var + SRI_UDS_EQN_UDS_VAR_ID_OFFSET);}
  SRI_VARIABLE_TYPE source_term_sri_var() {return 
      (SRI_VARIABLE_TYPE) (first_sri_var + SRI_UDS_EQN_SOURCE_TERM_VAR_ID_OFFSET);}
  SRI_VARIABLE_TYPE uds_flux_sri_var() {return 
      (SRI_VARIABLE_TYPE) (first_sri_var + SRI_UDS_EQN_UDS_FLUX_VAR_ID_OFFSET);} 
  SRI_VARIABLE_TYPE accumulation_sri_var() {return 
      (SRI_VARIABLE_TYPE) (first_sri_var + SRI_UDS_EQN_ACCUMULATION_VAR_ID_OFFSET);}
  SRI_VARIABLE_TYPE concentration_sri_var() {return 
      (SRI_VARIABLE_TYPE) (first_sri_var + SRI_UDS_EQN_CONCENTRATION_VAR_ID_OFFSET);}

  VOID seed_uds_simple_ublk_datablock(UBLK ublk, UBLK_UDS_DATA uds_data, asINT32 fluid_region);
  VOID seed_uds_real_ublk_datablock(UBLK ublk, asINT32 voxel, UBLK_UDS_DATA uds_data, asINT32 fluid_region); 
  VOID evaluate_source_term(UBLK ublk, UBLK_UDS_DATA ublk_uds_data,  uINT8 voxel_mask);


} *SP_UDS;


#endif
