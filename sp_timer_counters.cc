/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * SP Timer definitions
 *
 * Vinit Gupta, Exa Corporation 
 * Created Fri Nov 2 2018
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "shob_dyn.h"
#include "surfel_dyn_sp.h"
#include "sim.h"
#include "sp_timers.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "mirror.h"
#include PHYSICS_H

#if 0
enum {
  BASIC_FLUID_LOAD_TYPE,
  FAN_FLUID_LOAD_TYPE,
  POROUS_FLUID_LOAD_TYPE,
  CONDUCTION_SOLID_LOAD_TYPE,
  MIRROR_LOAD_TYPE,
  NUM_FLUID_LOAD_TYPES,
  INVALID_LOAD_TYPE
};
#endif

asINT32 get_load_type_from_ublk_fluid_type(asINT32 fluid_type)
{
  switch(fluid_type) {
  case MIRROR_TYPE:
    return MIRROR_LOAD_TYPE;
    break;
  case BASIC_FLUID_TYPE:
    return BASIC_FLUID_LOAD_TYPE;
    break;
  case FAN_FLUID_TYPE:
  case TABLE_FAN_FLUID_TYPE:
    return FAN_FLUID_LOAD_TYPE;
    break;
  case POROUS_FLUID_TYPE:
  case CURVED_POROUS_FLUID_TYPE:
  case CURVED_HX_POROUS_FLUID_TYPE:
    return POROUS_FLUID_LOAD_TYPE;
    break;
  case CONDUCTION_SOLID_TYPE:
    return CONDUCTION_SOLID_LOAD_TYPE;
  default:
    return INVALID_LOAD_TYPE;
    break;
  }
}

static VOID update_counters_for_ublk(TIMER_COUNTERS& counters, UBLK ublk, asINT32 scale)
{
  BOOLEAN is_pde_advect_ublk = ublk->has_two_copies();
  BOOLEAN is_near_surface = ublk->is_near_surface();
  BOOLEAN is_split = ublk->is_split();
  BOOLEAN is_fringe = ublk->is_fringe();
  BOOLEAN is_fringe2 = ublk->is_fringe2();
  BOOLEAN has_two_copies = ublk->has_two_copies();
  BOOLEAN is_farblk2 = has_two_copies && !is_fringe;
  
  VOXEL_MASK_8 basic_fluid_voxel_mask = ublk->basic_fluid_voxel_mask;
  VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
  sUBLK_DYNAMICS_DATA *dynamics_data = ublk->dynamics_data();
  auINT32 fluid_type = dynamics_data->fluid_type();
  asINT32 ublk_load_type = get_load_type_from_ublk_fluid_type(fluid_type);
  
  // Fluid load type priority: if a ublk contains porous, it is considered porous;
  // otherwise if it contains fan, it is fan; otherwise it is basic fluid

  if (basic_fluid_voxel_mask.any()) {
    dynamics_voxel_mask &= ~basic_fluid_voxel_mask;
    dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
  }

  //Special dynamics blocks
  while (dynamics_voxel_mask.any() && (ublk_load_type != POROUS_FLUID_LOAD_TYPE)) {
    auINT32 new_fluid_type = dynamics_data->fluid_type();
    asINT32 new_ublk_load_type = get_load_type_from_ublk_fluid_type(new_fluid_type);
    switch(new_ublk_load_type) {
    case POROUS_FLUID_LOAD_TYPE:
      ublk_load_type = new_ublk_load_type;
      break;
    case FAN_FLUID_LOAD_TYPE:
      if(ublk_load_type != POROUS_FLUID_LOAD_TYPE) {
        ublk_load_type = new_ublk_load_type;
      }
      break;
    case BASIC_FLUID_LOAD_TYPE:
      if(ublk_load_type != POROUS_FLUID_LOAD_TYPE && ublk_load_type != FAN_FLUID_LOAD_TYPE) {
        ublk_load_type = new_ublk_load_type;
      }
      break;
    }
    SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = static_cast<SPECIAL_UBLK_DYNAMICS_DATA>(dynamics_data);
    VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
    //Are there voxels that have other special dyn blocks, not accounted in previous loops
    dynamics_voxel_mask &= ~special_fluid_voxel_mask;
    dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (dynamics_data->next());
  }
    

  if (ublk->fluid_like_voxel_mask.any()) {
    if (is_pde_advect_ublk) {
      if (is_near_surface) {
        if (ublk->m_are_any_neighbors_split.any()) {
	  const auto& box_access = ublk->m_box_access;
	  const sSPLIT_ADVECT_INFO* split_advect_info = ublk->get_split_advect_info();
          asINT32 split_instance_index = -1;
#if !BUILD_D39_LATTICE
          if (split_advect_info == NULL) {
            split_instance_index = ublk->m_split_tagged_instance.get();
          } else if (split_advect_info->m_interacts_with_single_instance) {
            split_instance_index = split_advect_info->m_split_instance_index;
          }
#endif
          if (split_instance_index == -1 || sim.is_2d()) {
            if(is_fringe) {
              counters.FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][ublk_load_type]++;
            }
            else if(is_fringe2) {
              counters.FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][ublk_load_type]++;
            }
            else {
              counters.PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][ublk_load_type]++;
            }
          } else {
            counters.PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][ublk_load_type]++;
          }
        } else  {
          if(is_fringe) {
            counters.FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][ublk_load_type]++;
          }
          else if(is_fringe2) {
            counters.FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][ublk_load_type]++;
          }
          else {
            counters.PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][ublk_load_type]++;
          }
        }
      } else { // farblks
        if(is_fringe) {
          counters.FRINGE_PDE_FAR_N_UBLKS[scale][ublk_load_type]++;
        }
        else if(is_farblk2) {
          counters.PDE_FAR2_N_UBLKS[scale][ublk_load_type]++;
        }
        else {
          msg_internal_error("Unexpected farblk type in update_counters_for_ublk");
        }
      }
    } else {
      counters.FAR1_N_UBLKS[scale][ublk_load_type]++;
    }
  }
}

static VOID update_counters_for_vr_fine_ublk(TIMER_COUNTERS& counters, UBLK fine_ublk,
                                             asINT32 scale, std::set<SHOB_ID>& vrcoarse_exploded_ublks)
{
  BOOLEAN is_near_surface = fine_ublk->is_near_surface();

  // VR fine ublks do NOT do dynamics. They only do gather advection and VR coarse ublk explode
  // Do NOT distinguish different physics types for VR fine ublks.
  if (is_near_surface)
    counters.VRFINE_NEAR_N_UBLKS[scale]++;
  else
    counters.VRFINE_FAR_N_UBLKS[scale]++;

#ifdef EXPLODE_TIMER_COUNT_VRCOARSE
  // This code can be used if we want to count coarse ublks to do a 2-parameter fit.
  // tThe voxel counts apear redundant; wouldn't they just be the same as the vrfine counts?

  // VR coarse ublks which are exploded
  sVR_FINE_INTERFACE_DATA *vr_fine_data = (sVR_FINE_INTERFACE_DATA *) fine_ublk->vr_data();
  UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk();
  BOOLEAN is_coarse_ublk_near_surface = coarse_ublk->is_near_surface();
  if (vrcoarse_exploded_ublks.find(coarse_ublk->id()) == vrcoarse_exploded_ublks.end()) {
    if (is_coarse_ublk_near_surface) {
      counters.VRCOARSE_NEAR_EXPLODE_N_UBLKS[scale]++;
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data =
          (sVR_COARSE_INTERFACE_DATA *) coarse_ublk->vr_data();
      BOOLEAN is_2d = sim.is_2d();
      asINT32 n_fine_blks = N_VOXELS;

      /* In 2D, there are only 4 underlying VR fine blks for each coarse ublk.
       * Hence (via coalesce) each underlying VR fine blk defines 2 coarse voxels.
       */
      for (asINT32 coarse_voxel = 0; coarse_voxel < n_fine_blks; coarse_voxel++) {
        asINT32 fine_blk_idx = coarse_voxel;
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx);
        if (fine_ublk != NULL)
          counters.VRCOARSE_NEAR_EXPLODE_N_VOXELS[scale]++;
      }
    }
    else {
      counters.VRCOARSE_FAR_EXPLODE_N_UBLKS[scale]++;

      sVR_COARSE_INTERFACE_DATA *vr_coarse_data =
          (sVR_COARSE_INTERFACE_DATA *) coarse_ublk->vr_data();
      BOOLEAN is_2d = sim.is_2d();
      asINT32 n_fine_blks = N_VOXELS;

      /* In 2D, there are only 4 underlying VR fine blks for each coarse ublk.
       * Hence (via coalesce) each underlying VR fine blk defines 2 coarse voxels.
       */
      for (asINT32 coarse_voxel = 0; coarse_voxel < n_fine_blks; coarse_voxel++) {
        asINT32 fine_blk_idx = coarse_voxel;
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx);
        if (fine_ublk != NULL)
          counters.VRCOARSE_FAR_EXPLODE_N_VOXELS[scale]++;
      }
    }
    vrcoarse_exploded_ublks.insert(coarse_ublk->id());
  }
#endif
}



static VOID update_counters_for_sampling_surfel(TIMER_COUNTERS& counters, SAMPLING_SURFEL sampling_surfel,
                                                asINT32 scale)
{
  // regular surfel is neither is_even nor is_odd
  BOOLEAN is_even = sampling_surfel->is_even();
  BOOLEAN is_odd = sampling_surfel->is_odd();

  if (!is_odd)
    counters.SAMPLING_SURF_DYN_N_SURFELS[scale][0]++;
  if (!is_even)
    counters.SAMPLING_SURF_DYN_N_SURFELS[scale][1]++;
}

static VOID update_counters_for_surfel(TIMER_COUNTERS& counters, SURFEL surfel,
                                       asINT32 scale, BOOLEAN is_mirror,
                                       BOOLEAN is_slrf, BOOLEAN is_mlrf,
                                       SURFEL_PAIR surfel_pair, BOOLEAN is_apm)
{
  // Note that mlrf surfels could be fringe, so the sanity check is not needed.
  if (!surfel->is_sliding_rf())
    dassert(!surfel->is_ghost());

  // regular surfel is neither is_even nor is_odd
  BOOLEAN is_even = surfel->is_even();
  BOOLEAN is_odd = surfel->is_odd();
  BOOLEAN is_fringe = surfel->is_fringe();
  BOOLEAN is_fringe2 = surfel->is_fringe2();

  // mirror surfels do not do surf_dyn
  // slrf surfels do not contribute to the SURF_DYN counters in simeng
  // while they do contribute to V2S and S2V counters
  //
  // mlrf surfels do not contribute to SURFEL_DYN counters and not to V2S and S2V counters as well
  if (!is_mirror && !is_slrf && !is_mlrf && !is_apm) {
    asINT32 surfel_timer_index = surfel->timer() - SP_STANDARD_WALL_SURFEL_DYN_TIMER; 
    if (!is_odd) {
      counters.SURF_DYN_N_SURFELS[scale][0]++;
      counters.SURF_TYPES_DYN_N_SURFELS[scale][0][surfel_timer_index]++;
    }
    if (!is_even) {
      counters.SURF_DYN_N_SURFELS[scale][1]++;
      counters.SURF_TYPES_DYN_N_SURFELS[scale][1][surfel_timer_index]++;
    }
  }

  if (is_slrf) {
    if (!is_odd)
      counters.SLRF_SURF_DYN_N_SURFELS[scale][0]++;
    if (!is_even)
      counters.SLRF_SURF_DYN_N_SURFELS[scale][1]++; 
  }

  if (is_mlrf) {
    if (!is_odd)
      counters.MLRF_SURF_DYN_N_SURFELS[scale][0]++;
    if (!is_even)
      counters.MLRF_SURF_DYN_N_SURFELS[scale][1]++;
 
  }

  if (is_apm) {
    // TODO: make this a member function of sSURFEL_PAIR
    SP_TIMER_TYPE surfel_pair_timer = SP_INVALID_TIMER;
    switch (surfel_pair->dynamics_type()) {                                                              \
        case APM_NOSLIP_ISURFEL_TYPE:
        case APM_LINEAR_NOSLIP_ISURFEL_TYPE:
        case APM_ANGULAR_NOSLIP_ISURFEL_TYPE:
          surfel_pair_timer =  SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
          break;
        case APM_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_STANDARD_ISURFEL_DYN_TIMER;
          break;
        case APM_LINEAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_LINEAR_ISURFEL_DYN_TIMER;
          break;
        case APM_ANGULAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_ANGULAR_ISURFEL_DYN_TIMER;
          break;
        default:
          msg_internal_error("Invalid apm isurfel dynamics type (%d)", surfel_pair->dynamics_type());
          break;
        }

    asINT32 surfel_pair_timer_index = surfel_pair_timer - SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
    if (!is_odd) {
      counters.ISURF_DYN_N_ISURFELS[scale][0]++;
      counters.ISURF_TYPES_DYN_N_ISURFELS[scale][0][surfel_pair_timer_index]++;
    }
    if (!is_even) {
      counters.ISURF_DYN_N_ISURFELS[scale][1]++;
      counters.ISURF_TYPES_DYN_N_ISURFELS[scale][1][surfel_pair_timer_index]++;
    }
  }

  // mlrf surfels do not contribute to S2S, V2S and S2V counters since these operations are not timed
  if (is_mlrf)
    return;

  if (surfel->is_s2s_destination()) {
    S2S_ADVECT_DATA s2s_advect_data = surfel->s2s_advect_data();
    SURFEL_SURFEL_INTERACTION surfel_interaction = s2s_advect_data->m_surfel_interactions;
    asINT32 n_src_surfels = s2s_advect_data->m_n_src_surfels;

    if(is_fringe) {
      counters.FRINGE_S2S_N_DEST_SURFELS[scale]++;
    }
    else if(is_fringe2) {
      counters.FRINGE2_S2S_N_DEST_SURFELS[scale]++;
    }
    else {
      counters.S2S_N_DEST_SURFELS[scale]++;
    }
    ccDOTIMES(nth_src_surfel, n_src_surfels) {
      asINT32 n_weights = surfel_interaction->m_n_weights;

      auINT32 interaction_even_odd_mask = surfel_interaction->m_even_odd_mask;
      // Initialize the counters

      if (interaction_even_odd_mask & STP_PROCESS_ON_EVEN_TIMES) {
        if(is_fringe) {
          counters.FRINGE_S2S_N_SRC_SURFELS[scale][0]++;
          counters.FRINGE_S2S_N_WEIGHTS[scale][0] += n_weights;
        }
        else if (is_fringe2) {
          counters.FRINGE2_S2S_N_SRC_SURFELS[scale][0]++;
          counters.FRINGE2_S2S_N_WEIGHTS[scale][0] += n_weights;
        }
        else {
          counters.S2S_N_SRC_SURFELS[scale][0]++;
          counters.S2S_N_WEIGHTS[scale][0] += n_weights;
        }
      }
      if (interaction_even_odd_mask & STP_PROCESS_ON_ODD_TIMES) {
        counters.S2S_N_SRC_SURFELS[scale][1]++;
        counters.S2S_N_WEIGHTS[scale][1] += n_weights; 
      }
      surfel_interaction = (SURFEL_SURFEL_INTERACTION) ((char *)surfel_interaction + surfel_interaction->size());
    }
  }

  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks = surfel->m_n_ublk_interactions;

  /* From stp.h:
   * STP_EVEN_V2S_PHASE 0
   * STP_EVEN_S2V_PHASE 1
   * STP_ODD_V2S_PHASE  2
   * STP_ODD_S2V_PHASE  3
   */
  
  if(is_fringe) {
    counters.FRINGE_V2S_S2V_N_SURFELS[scale][STP_EVEN_V2S_PHASE]++;
  }
  else if(is_fringe2) {
    counters.FRINGE2_V2S_S2V_N_SURFELS[scale][STP_EVEN_V2S_PHASE]++;
  }
  else {
    counters.V2S_S2V_N_SURFELS[scale][STP_EVEN_V2S_PHASE]++;
  }

  // even surfels not active in OV2S
  if (!is_even) {
    if(is_fringe) {
      counters.FRINGE_V2S_S2V_N_SURFELS[scale][STP_ODD_V2S_PHASE]++;
    }
    else if(is_fringe2) {
      counters.FRINGE2_V2S_S2V_N_SURFELS[scale][STP_ODD_V2S_PHASE]++;
    }
    else {
      counters.V2S_S2V_N_SURFELS[scale][STP_ODD_V2S_PHASE]++;
    }
  }
  // odd surfels not active in ES2V
  if (!is_odd) {
    if(is_fringe) {
      counters.FRINGE_V2S_S2V_N_SURFELS[scale][STP_EVEN_S2V_PHASE]++;
    }
    else if(is_fringe2) {
      counters.FRINGE2_V2S_S2V_N_SURFELS[scale][STP_EVEN_S2V_PHASE]++;
    }
    else {
      counters.V2S_S2V_N_SURFELS[scale][STP_EVEN_S2V_PHASE]++;
    }
  }

  if(is_fringe) {
    counters.FRINGE_V2S_S2V_N_SURFELS[scale][STP_ODD_S2V_PHASE]++;
  }
  else if(is_fringe2) {
    counters.FRINGE2_V2S_S2V_N_SURFELS[scale][STP_ODD_S2V_PHASE]++;
  }
  else {
    counters.V2S_S2V_N_SURFELS[scale][STP_ODD_S2V_PHASE]++;
  }
  

  asINT32 surfelID = surfel->id();

  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    BOOLEAN is_vr_fine = ublk->is_vr_fine();
    BOOLEAN is_vr_coarse = ublk->is_vr_coarse();

    if (is_vr_fine) {
      // Only active if the ublk and the surfel are at the same scale
      if (ublk->scale() == surfel->scale()) {
        if (!is_odd) {
          if(is_fringe) {
            counters.FRINGE_V2S_S2V_N_UBLKS[scale][STP_EVEN_S2V_PHASE]++;
          }
          else if(is_fringe2) {
            counters.FRINGE2_V2S_S2V_N_UBLKS[scale][STP_EVEN_S2V_PHASE]++;
          }
          else {
            counters.V2S_S2V_N_UBLKS[scale][STP_EVEN_S2V_PHASE]++;
          }
        }
        if (!is_even) {
          if(is_fringe) {
            counters.FRINGE_V2S_S2V_N_UBLKS[scale][STP_ODD_V2S_PHASE]++;
          }
          else if(is_fringe2) {
            counters.FRINGE2_V2S_S2V_N_UBLKS[scale][STP_ODD_V2S_PHASE]++;
          }
          else {
            counters.V2S_S2V_N_UBLKS[scale][STP_ODD_V2S_PHASE]++;
          }
        }
      } else { 
        continue;
      }
    } else if (is_vr_coarse && (ublk->scale() == (surfel->scale() - 1))) {
      // it is possible that ublk is vr_coarse but its scale is the same as surfel, in that case it is treated the same as non-VR
      if(is_fringe) {
        counters.FRINGE_V2S_S2V_N_UBLKS[scale][STP_EVEN_V2S_PHASE]++;
        counters.FRINGE_V2S_S2V_N_UBLKS[scale][STP_ODD_S2V_PHASE]++;
      }
      else if(is_fringe2) {
        counters.FRINGE2_V2S_S2V_N_UBLKS[scale][STP_EVEN_V2S_PHASE]++;
        counters.FRINGE2_V2S_S2V_N_UBLKS[scale][STP_ODD_S2V_PHASE]++;
      }
      else {
        counters.V2S_S2V_N_UBLKS[scale][STP_EVEN_V2S_PHASE]++;
        counters.V2S_S2V_N_UBLKS[scale][STP_ODD_S2V_PHASE]++;
      }
    } else { // non-VR or (scale_diff == 0 && is_vr_coarse) or (scale_diff == 2 && is_vr_coarse)
      // We ignore the (scale_diff == 2 && is_vr_coarse && weights_pde_only) surfel ublk interactions (Need further examination)
      // non-VR ublks are always looped except even surfel in OV2S and odd surfel in ES2V
      BOOLEAN weights_pde_only = TRUE;

      // check if all the weights for this ublk are pde only. If so and ublk is 2 scale coarser than the surfel, this interaction
      // should be ignored
      sSURFEL_VOXEL_INTERACTION s_v_interaction;

      uINT8 *voxel_info = ublk_interaction->voxel_info();
      auINT32 n_weight_sets = ublk_interaction->m_n_weight_sets;

      ccDOTIMES (iv, n_weight_sets) {
        voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
        asINT32 phase_mask          = s_v_interaction.phase_mask();
        asINT32 voxel               = s_v_interaction.voxel_id();
        asINT32 n_voxel_weights     = s_v_interaction.n_voxel_weights();
        asINT32 n_weights_pde_only  = s_v_interaction.n_voxel_weights_pde_only();
        if (n_voxel_weights != n_weights_pde_only)
          weights_pde_only = FALSE;
        voxel_info += n_voxel_weights;
      }                                          

      if (is_vr_coarse && ublk->scale() == (surfel->scale() - 2) && weights_pde_only) {
        // ignore the current ublk
        continue;
      } else {
        counters.V2S_S2V_N_UBLKS[scale][STP_EVEN_V2S_PHASE]++;
        if (!is_odd) {
          counters.V2S_S2V_N_UBLKS[scale][STP_EVEN_S2V_PHASE]++;
        }
        if (!is_even)
          counters.V2S_S2V_N_UBLKS[scale][STP_ODD_V2S_PHASE]++;
        counters.V2S_S2V_N_UBLKS[scale][STP_ODD_S2V_PHASE]++;
      }
    }

    // check voxels
    uINT8 *voxel_info = ublk_interaction->voxel_info();
    sSURFEL_VOXEL_INTERACTION s_v_interaction;

    auINT32 n_weight_sets = ublk_interaction->m_n_weight_sets;
    VOXEL_MASK_8 voxel_advect_lb_states_mask = ublk_interaction->m_voxels_advect_to_surfel_mask;

    ccDOTIMES (iv, n_weight_sets) {
      voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
      asINT32 phase_mask          = s_v_interaction.phase_mask();
      asINT32 voxel               = s_v_interaction.voxel_id();
      asINT32 n_voxel_weights     = s_v_interaction.n_voxel_weights();
      asINT32 n_weights_pde_only  = s_v_interaction.n_voxel_weights_pde_only();

      ccDOTIMES(i, 2) {
        // We check is_voxel_in_mask for V2S(phase 0, 2), but not for S2V(phase 1, 3)
        asINT32 v2s_phase = 2*i;
        if (phase_mask & (1 << v2s_phase) && is_voxel_in_mask(voxel, voxel_advect_lb_states_mask)) {
          counters.V2S_S2V_N_VOXELS[scale][v2s_phase]++;
          counters.V2S_S2V_N_WEIGHTS[scale][v2s_phase] += n_voxel_weights;        
        }

        asINT32 s2v_phase = 2*i + 1;
        if (phase_mask & (1 << s2v_phase)) {
          counters.V2S_S2V_N_VOXELS[scale][s2v_phase]++;
          counters.V2S_S2V_N_WEIGHTS[scale][s2v_phase] += n_voxel_weights;
        }
      }
      voxel_info += n_voxel_weights;
    }
  }
}

// The timer counters collected here should be consistent with the real counters collected during simulation
VOID collect_sp_timer_counters()
{
  g_timer_counters.initialize();

  DO_SCALES_FINE_TO_COARSE(scale) {
    { DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
        update_counters_for_surfel(g_timer_counters, surfel, scale, FALSE, FALSE, FALSE, NULL, FALSE);
    } }
    { DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
        update_counters_for_surfel(g_timer_counters, surfel, scale, FALSE, FALSE, FALSE, NULL, FALSE);
    } }
    { DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
        update_counters_for_surfel(g_timer_counters, wsurfel_conduction, scale, FALSE, FALSE, FALSE, NULL, FALSE);
    } }
    { DO_WSURFELS_FLOW_OF_SCALE(wsurfel_flow, scale) {
        update_counters_for_surfel(g_timer_counters, wsurfel_flow, scale, FALSE, FALSE, FALSE, NULL, FALSE);
    } }
    { DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
        update_counters_for_surfel(g_timer_counters, contact_surfel, scale, FALSE, FALSE, FALSE, NULL, FALSE);
    } }
    

    //// Does not support mirror surfel counters yet
    //{
    //  DO_MIRROR_SURFELS_OF_SCALE(surfel, scale) {
    //    update_counters_for_surfel(g_timer_counters, surfel, scale, TRUE, FALSE, FALSE);
    //  }
    //}
    // Note that the simulator timers for S2S, V2S, S2V are put in surfel_s2s(v2s,s2v)_advect(), which 
    // is called by both regular surfels and surfel_pairs
    {
      DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
        if (!slrf_surfel_pair->is_fringe() && !slrf_surfel_pair->is_fringe2()) {
          SURFEL ext_surfel = slrf_surfel_pair->m_exterior_surfel;
          SURFEL int_surfel = slrf_surfel_pair->m_interior_surfel;
          update_counters_for_surfel(g_timer_counters, int_surfel, scale, FALSE, TRUE, FALSE, NULL, FALSE);
          update_counters_for_surfel(g_timer_counters, ext_surfel, scale, FALSE, TRUE, FALSE, NULL, FALSE);
        }
      }
    }
     
    {
      DO_ISURFEL_PAIRS_OF_SCALE(isurfel_pair, scale) {
        if (!isurfel_pair->is_fringe() && !isurfel_pair->is_fringe2()) {
          SURFEL ext_surfel = isurfel_pair->m_exterior_surfel;
          SURFEL int_surfel = isurfel_pair->m_interior_surfel;
          update_counters_for_surfel(g_timer_counters, int_surfel, scale, FALSE, FALSE, FALSE, isurfel_pair, TRUE);
          update_counters_for_surfel(g_timer_counters, ext_surfel, scale, FALSE, FALSE, FALSE, isurfel_pair, TRUE);
        }
      }
    }

    // Sampling surfels only contribute to sampling_surf_dyn_n_surfels, not S2S and V2S
    {
      DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
        // sampling surfels cannot be fringe2.
        if (!sampling_surfel->is_fringe())
          update_counters_for_sampling_surfel(g_timer_counters, sampling_surfel, scale);
      }
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(mlrf_surfel_group, scale) {
      DO_MLRF_SURFEL_GROUP_SURFELS(tagged_mlrf_surfel, mlrf_surfel_group) {
        if (!tagged_mlrf_surfel.is_surfel_weightless()) {
          SURFEL surfel = tagged_mlrf_surfel.mlrf_surfel();
          // mlrf surfels could be fringe. Since mlrf surfels are timed all together in the sliding mesh
          // strand, we should consider their contribution to the counters.
          if ((!surfel->is_fringe() && !surfel->is_fringe2()) || surfel->is_sliding_rf())
            update_counters_for_surfel(g_timer_counters, surfel, scale, FALSE, FALSE, TRUE, NULL, FALSE);
        }
      }
    }
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    {
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_NEARBLK_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(nearblk, group) {
          update_counters_for_ublk(g_timer_counters, nearblk, scale);
        }
      }
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK1_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(ublk, group) {
          update_counters_for_ublk(g_timer_counters, ublk, scale);
        }
      }
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, INTERIOR_FARBLK2_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(farblk, group) {
          update_counters_for_ublk(g_timer_counters, farblk, scale);
        }
      }
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE_FARBLK_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(ublk, group) {
          update_counters_for_ublk(g_timer_counters, ublk, scale);
        }
      }
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE_NEARBLK_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(ublk, group) {
          update_counters_for_ublk(g_timer_counters, ublk, scale);
        }
      }
      DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, FRINGE2_NEARBLK_GROUP_TYPE) {
        DO_UBLKS_OF_GROUP(ublk, group) {
          update_counters_for_ublk(g_timer_counters, ublk, scale);
        }
      }
    }
 
    // Use set to keep track of VR coarse ublks which are exploded
    // We have one set for each scale
    std::set<SHOB_ID> vrcoarse_exploded_ublks;

    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrnearblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrnearblk, scale, vrcoarse_exploded_ublks);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrfarblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrfarblk, scale, vrcoarse_exploded_ublks);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrnearblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrnearblk, scale, vrcoarse_exploded_ublks);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE_FARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrfarblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrfarblk, scale, vrcoarse_exploded_ublks);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE2_NEARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrnearblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrnearblk, scale, vrcoarse_exploded_ublks);
      }
    }
    DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, VRFINE_FRINGE2_FARBLK_GROUP_TYPE) {
      DO_UBLKS_OF_GROUP(vrfarblk, group) {
        update_counters_for_vr_fine_ublk(g_timer_counters, vrfarblk, scale, vrcoarse_exploded_ublks);
      }
    }
  }
#if DEBUG_SP_COUNTERS
  // All counters are updated
   g_timer_counters.print();
#endif
}

