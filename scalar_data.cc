/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("cp.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("cp.copyright", "78") */ 

//----------------------------------------------------------------------------
// James F. Kelly                                             Aug 11, 2013
//----------------------------------------------------------------------------

#include "scalar_data.h"
#include "ublk.h"
#include "sim.h"
#include "surfel.h"
#include "sampling_surfel.h"


template<>
VOID sSURFEL_UDS_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_UDS_DATA mirror_uds_data, 
								 STP_LATVEC_MASK latvec_state_mask,
								 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
								 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  uds_value += mirror_uds_data->uds_value;
  //uds_thickness += mirror_uds_data->uds_thickness;
  //diffusion_coef += mirror_uds_data->diffusion_coef;
}

template<>
VOID sSURFEL_UDS_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_UDS_DATA source_uds_data, 
								 STP_LATVEC_MASK latvec_state_mask,
								 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
								 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  uds_value_bc = source_uds_data->uds_value_bc;
  uds_flux_bc = source_uds_data->uds_flux_bc;
  uds_value = source_uds_data->uds_value;
  //uds_thickness = source_uds_data->uds_thickness;
  uds_flux = source_uds_data->uds_flux; 
  //diffusion_coef = source_uds_data->diffusion_coef;
}


template<>
VOID sSURFEL_UDS_DATA::pre_advect_init() {
  uds_value = 0.0;        
}


VOID sSAMPLING_SURFEL_UDS_DATA::pre_advect_init() {
  uds_value = 0.0;        
}


VOID sSAMPLING_SURFEL_UDS_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_UDS_DATA even_surfel_uds_data)
{
  memcpy(this, even_surfel_uds_data, sizeof(*this));
}

#if BUILD_GPU
inline namespace SIMULATOR_NAMESPACE {
INIT_MSFL(tSURFEL_UDS_DATA) {
  COPY_SFL_TO_MSFL(is_flux_bc);
  COPY_SFL_TO_MSFL(is_active_for_uds); 
  COPY_SFL_TO_MSFL(is_outlet_bc);
  COPY_SFL_TO_MSFL(is_condensable);

  COPY_SFL_TO_MSFL(uds_value_bc);
  COPY_SFL_TO_MSFL(uds_flux_bc);
  
  COPY_SFL_TO_MSFL(uds_value); 
  COPY_SFL_TO_MSFL(uds_thickness);
  COPY_SFL_TO_MSFL(defrost_time);
  COPY_SFL_TO_MSFL(uds_flux); 
  COPY_SFL_TO_MSFL(uds_value_prime);
  //COPY_SFL_TO_MSFL(diffusion_coef);

  //COPY_SFL_TO_MSFL(q0);
  //COPY_SFL_TO_MSFL(q_flux_cross_lrf);  
}

INIT_MSFL(tSURFEL_S2S_UDS_DATA) {
  VEC2_COPY_SFL_TO_MSFL(m_out_flux_uds, N_S2S_TIME_INDICES, N_SURFEL_PGRAM_VOLUMES);
}

INIT_MSFL(tSURFEL_V2S_UDS_DATA) {
  VEC_COPY_SFL_TO_MSFL(m_uds_bar, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(m_in_states_uds, N_SURFEL_PGRAM_VOLUMES); 
  COPY_SFL_TO_MSFL(m_diffusion_coef);
  COPY_SFL_TO_MSFL(m_uds0);    // Used by PDE_UDS
  COPY_SFL_TO_MSFL(m_uds_flux_cross_lrf);
  VEC_COPY_SFL_TO_MSFL(m_out_flux_uds, N_SURFEL_PGRAM_VOLUMES);  
}

}
#endif


/////////////////////////
// phase field
/////////////////////////
template<>
VOID sSURFEL_PF_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_PF_DATA mirror_pf_data, 
								STP_LATVEC_MASK latvec_state_mask,
								STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
								STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{}

template<>
VOID sSURFEL_PF_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_PF_DATA source_pf_data, 
								STP_LATVEC_MASK latvec_state_mask,
								STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
								STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  rho_bar_ratio_pfld[0] = source_pf_data->rho_bar_ratio_pfld[0];
  rho_bar_ratio_pfld[1] = source_pf_data->rho_bar_ratio_pfld[1];
  u_bar_ratio_pfld[0] = source_pf_data->u_bar_ratio_pfld[0];
  u_bar_ratio_pfld[1] = source_pf_data->u_bar_ratio_pfld[1];
  u_bar_ratio_pfld[2] = source_pf_data->u_bar_ratio_pfld[2];
  u_bar_ratio_pfld[3] = source_pf_data->u_bar_ratio_pfld[3];
  pressure_pfld_ur = source_pf_data->pressure_pfld_ur;
}

#if BUILD_GPU
inline namespace SIMULATOR_NAMESPACE {
INIT_MSFL(tSURFEL_V2S_PF_DATA) {
  COPY_SFL_TO_MSFL(m_pressure_pfld);
  COPY_SFL_TO_MSFL(m_cf_n);
  VEC_COPY_SFL_TO_MSFL(m_grad_chem, 3);
  VEC_COPY_SFL_TO_MSFL(m_grad_orderp, 4);
  VEC_COPY_SFL_TO_MSFL(m_vel_pfld, 3);
  VEC_COPY_SFL_TO_MSFL(m_u_bar_pfld, N_SURFEL_PGRAM_VOLUMES); 
  
}

INIT_MSFL(tSURFEL_PF_QTM) {
  COPY_SFL_TO_MSFL(pressure_ur);
  VEC_COPY_SFL_TO_MSFL(u_n_ur, 3);
  VEC_COPY_SFL_TO_MSFL(u_avg, 3);
  VEC_COPY_SFL_TO_MSFL(u_pre, 3);
  COPY_SFL_TO_MSFL(ustar_n);
  COPY_SFL_TO_MSFL(ustar_exact_n);
  COPY_SFL_TO_MSFL(ustar_n_minus_1);
  COPY_SFL_TO_MSFL(flag_init);
}

INIT_MSFL(tSURFEL_PF_DATA) {
  VEC_COPY_SFL_TO_MSFL(rho_bar_ratio_pfld, 2);
  VEC_COPY_SFL_TO_MSFL(u_bar_ratio_pfld, 4);
  COPY_SFL_TO_MSFL(pressure_pfld_ur);
  COPY_SFL_TO_MSFL(pf_qtm.pressure_ur);
  VEC_COPY_SFL_TO_MSFL(pf_qtm.u_n_ur, 3);
  VEC_COPY_SFL_TO_MSFL(pf_qtm.u_avg, 3);
  VEC_COPY_SFL_TO_MSFL(pf_qtm.u_pre, 3);
  COPY_SFL_TO_MSFL(pf_qtm.ustar_n);
  COPY_SFL_TO_MSFL(pf_qtm.ustar_exact_n);
  COPY_SFL_TO_MSFL(pf_qtm.ustar_n_minus_1);
  COPY_SFL_TO_MSFL(pf_qtm.flag_init); 
}
}
#endif
