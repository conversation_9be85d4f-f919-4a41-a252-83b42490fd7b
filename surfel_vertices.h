#ifndef _SIMENG_SURFEL_VERTICES_H
#define _SIMENG_SURFEL_VERTICES_H

#include VMEM_VECTOR_H
#include "common_sp.h"
#include "surface_shob.h"


typedef struct sSURFEL_VERTEX_PAIR {
  // The type must be SURFACE_SHOB because that is the common base class of regular surfels and sampling surfels
  SURFACE_SHOB surfel;    
  DGF_VERTEX_INDEX head_vertex; // head vertex in edge half
  DGF_VERTEX_INDEX opposite_head_vertex; // opposite head vertex in edge half
} *SURFEL_VERTEX_PAIR;


struct sSURFEL_PARTICLE_DATA_BASE;

class cSURFEL_VERTICES_INFO {
public:
  // Surfel vertices are stored on the SPs if particle modeling is on.
  DGF_VERTEX_INDEX m_n_surfel_vertices;        // number of vertices used by surfels on this SP
  DGF_VERTEX_INDEX m_n_global_surfel_vertices; // total number of surfel vertices

  // Each surfel has a vertex count and an index into this array, which identifies the
  // indices of the vertices used by the surfel.
  VMEM_VECTOR<DGF_VERTEX_INDEX> m_surfel_vertex_indices;

  //Also need a temporary copy of the global indices for surfels on this SP for the creation of film only ghost surfels
  VMEM_VECTOR<DGF_VERTEX_INDEX> m_surfel_vertex_global_indices;

  sSIM_VERTEX *m_surfel_vertices;
  // This is used during initialization to track which vertices are used by surfels on this SP
  std::vector<bool> *m_is_surfel_vertex_used;

  // The number of half edges is the same as the size of m_surfel_vertex_indices. A surfel's
  // half edges in m_surfel_half_edges appear at exactly the same offset as its vertex indices
  // in m_surfel_vertex_indices.
  sSURFEL_HALF_EDGE *m_surfel_half_edges;

  uINT16 *m_vertex_n_surfels;
  sSURFEL_VERTEX_PAIR *m_vertex_surfels;
  DGF_VERTEX_INDEX *m_vertex_first_surfel_indices;

  // Maps local vertex index to global index (found in lgi file). Used when writing out shell stencil information.
  std::vector<DGF_VERTEX_INDEX> *m_local_to_global_map;

public:
  cSURFEL_VERTICES_INFO() {
    m_n_surfel_vertices = 0;
    m_n_global_surfel_vertices = 0;
    m_surfel_vertices = nullptr;
    m_is_surfel_vertex_used = nullptr;
    m_surfel_half_edges = nullptr;
    m_vertex_n_surfels = nullptr;
    m_vertex_surfels = nullptr;
    m_vertex_first_surfel_indices = nullptr;
    m_local_to_global_map = nullptr;
  }
  template <typename SURFEL_TYPE>
  VOID remap_surfel_vertex_indices(SURFEL_TYPE surfel);
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  template <typename SURFEL_TYPE>
  VOID remap_surfel_vertex_indices_for_old_stencil_construction(SURFEL_TYPE surfel);
#endif
  VOID remap_surfel_vertex_indices_from_global_to_local_new();
  VOID create_surfel_half_edges();
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  // VOID remap_surfel_vertex_indices_from_global_to_local_and_create_half_edges();
  VOID remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
  VOID create_half_edges_for_old_stencil_construction();
  VOID compute_surfel_half_edge_length(sSURFEL_PARTICLE_DATA_BASE *p_data, bool is_right_hand_rule);
#endif
  VOID compute_surfel_half_edge_lengths();
  VOID read_surfel_vertices();
  VOID delete_vertex_surfels_arrays();
  template <typename SURFEL_TYPE>
  VOID assign_half_edge_neighbors(SURFEL_TYPE surfel);
  template <typename SURFEL_TYPE>
  VOID compute_half_edge_info(SURFEL_TYPE surfel);

  VOID allocate_is_surfel_vertex_used(DGF_VERTEX_INDEX num_verts) {
    m_is_surfel_vertex_used = xnew std::vector<bool> (num_verts, false);
  }
  VOID reserve_surfel_vertex_global_indices(asINT32 count) {
    m_surfel_vertex_global_indices.reserve(count);
  }
  DGF_VERTEX_INDEX surfel_vertex_index(DGF_VERTEX_INDEX global_index) {
    return m_surfel_vertex_indices[global_index];
  }
  sSIM_VERTEX *surfel_vertex(DGF_VERTEX_INDEX global_index) {
    cassert(m_surfel_vertices);
    return &m_surfel_vertices[m_surfel_vertex_indices[global_index]];
  }
  sSURFEL_HALF_EDGE *surfel_half_edge(asINT32 edge_num) {
    cassert(m_surfel_half_edges);
    return &m_surfel_half_edges[edge_num];
  }
  VOID add_vertex_global_index(DGF_VERTEX_INDEX vertex_index) {
    m_surfel_vertex_global_indices.push_back(vertex_index);
  }
  VOID set_is_surfel_vertex_used(DGF_VERTEX_INDEX vertex_index) {
    m_is_surfel_vertex_used->at(vertex_index) = true;
  }
  asINT32 n_surfel_vertex_global_indices() {
    return m_surfel_vertex_global_indices.size();
  }
  DGF_VERTEX_INDEX global_from_local_index(DGF_VERTEX_INDEX local_index) {
    return m_surfel_vertex_global_indices[local_index];
  }
  DGF_VERTEX_INDEX map_local_to_global_index(DGF_VERTEX_INDEX local_index) {
    return (m_local_to_global_map == NULL) ? 0 : m_local_to_global_map->operator[](local_index);
  }
  DGF_VERTEX_INDEX vertex_first_surfel_index(DGF_VERTEX_INDEX vertex_index) {
    cassert(m_vertex_first_surfel_indices);
    return m_vertex_first_surfel_indices[vertex_index];
  }
  SURFEL_HALF_EDGE get_edge_from_surfel_and_tail_vertex(const SURFACE_SHOB surfel, const SIM_VERTEX tail_vertex);
};

VOID compute_off_plane_tolerances();

#if !BUILD_FOR_SIMSIZES
extern cSURFEL_VERTICES_INFO g_surfel_vertices_info;

inline DGF_VERTEX_INDEX vertex_local_index_from_global(DGF_VERTEX_INDEX global_index) {
  return g_surfel_vertices_info.surfel_vertex_index(global_index);
}

inline sSIM_VERTEX *surfel_vertex_from_global_index(DGF_VERTEX_INDEX global_index) {
  return g_surfel_vertices_info.surfel_vertex(global_index);
}

inline sSURFEL_HALF_EDGE *surfel_half_edge(asINT32 edge_num) {
  return g_surfel_vertices_info.surfel_half_edge(edge_num);
}

template <typename SURFEL_TYPE>
sPARTICLE_VAR *nth_surfel_vertex(SURFEL_TYPE surfel, asINT32 n) {
  asINT32 first_vertex_index = surfel->stencil()->first_vertex_index;
  asINT32 n_vertices = surfel->stencil()->n_vertices;
  SIM_VERTEX random_vertex = surfel_vertex_from_global_index(first_vertex_index + n%n_vertices);
  return(random_vertex->coord);
}
#endif
#endif
