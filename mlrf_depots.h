/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Sam Watson, Exa Corporation 
 * Created May 28, 2009
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_MLRF_DEPOTS_H
#define _SIMENG_MLRF_DEPOTS_H

#include "common_sp.h"
// A tag may optionally be added to each depot as a debug aid.
//  This is useful for identifying its source surfel, or possibly
// for some other purpose.

#if MLRF_TAG_MESSAGES
#define MLRF_DEPOT_TAGS 1
#else
#define MLRF_DEPOT_TAGS 0
#endif

typedef struct sMLRF_PRE_DYNAMICS_DEPOT {
  STP_PHYS_VARIABLE  mass;
  STP_PHYS_VARIABLE  mom[3];
  STP_PHYS_VARIABLE  temp;
  STP_PHYS_VARIABLE  visc;
  STP_PHYS_VARIABLE  heat_flux;
  STP_PHYS_VARIABLE  turb_ke;
  STP_PHYS_VARIABLE  turb_df;
  STP_PHYS_VARIABLE  uds_value[MAX_N_USER_DEFINED_SCALARS];
  STP_PHYS_VARIABLE  uds_flux[MAX_N_USER_DEFINED_SCALARS];
  STP_PHYS_VARIABLE  uds_diffusion_coef[MAX_N_USER_DEFINED_SCALARS];
#if BUILD_6X_SOLVER
  STP_PHYS_VARIABLE  pf_pressure;
  STP_PHYS_VARIABLE  pf_vel[3];
  STP_PHYS_VARIABLE  pf_grad_chem[3];
  STP_PHYS_VARIABLE  pf_grad_order[4];
#endif
  BOOLEAN            is_conduction_surface;
  STP_PHYS_VARIABLE  conductivity_sample;
  STP_PHYS_VARIABLE  dist;
  STP_PHYS_VARIABLE  ustar_0;
  STP_PHYS_VARIABLE  lrf_s2s_factor;
  STP_PHYS_VARIABLE  lrf_v2s_dist;
  STP_PHYS_VARIABLE  lrf_v2s_scale_diff; 
  STP_PHYS_VARIABLE  nu;
#if BUILD_6X_SOLVER
  STP_PHYS_VARIABLE  den_in;
  STP_PHYS_VARIABLE  Nin_t_sum;
  STP_PHYS_VARIABLE  kappa; 
  STP_PHYS_VARIABLE  entropy; 
#else  //5X, 39s, or 5g
#if BUILD_D39_LATTICE
  STP_PHYS_VARIABLE  entropy; //required for both pde_entropy and lb_energy
#endif
  union {
    struct { //for T lb_scalar solver
      STP_PHYS_VARIABLE  den_in;
      STP_PHYS_VARIABLE  Nin_t_sum;
    };
    struct {
      STP_PHYS_VARIABLE  kappa; //for T pde solver
#if BUILD_D39_LATTICE
      STP_PHYS_VARIABLE hyb_force[3];
#else
      STP_PHYS_VARIABLE  entropy; //used for entropy just placing here to reduce memory requirement
#endif
    };
  };
#endif
  STP_PHYS_VARIABLE  Nin_uds_sum[MAX_N_USER_DEFINED_SCALARS];

#if MLRF_DEPOT_TAGS
  asINT32 tag;
  asINT32 tag_timestep;
#endif
  VOID print();

} *MLRF_PRE_DYNAMICS_DEPOT;

#define PRE_DYN_DEPOT_FLOAT_SIZE (sizeof(sMLRF_PRE_DYNAMICS_DEPOT) / sizeof(STP_PHYS_VARIABLE))

typedef struct sMLRF_POST_DYNAMICS_DEPOT {
#if BUILD_D39_LATTICE
  STP_PHYS_VARIABLE delta_mass[N_NONZERO_ENERGIES];
#else
  STP_PHYS_VARIABLE delta_mass;
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  STP_PHYS_VARIABLE delta_energy;    //lb_energy
#endif
#if BUILD_6X_SOLVER
  STP_PHYS_VARIABLE pf_delta_mass[MAX_N_USER_DEFINED_SCALARS];
  //STP_PHYS_VARIABLE pf_mass_out[MAX_N_USER_DEFINED_SCALARS];
  //STP_PHYS_VARIABLE inverse_sum_volume;
#endif

#if MLRF_DEPOT_TAGS
  asINT32 tag;
  asINT32 tag_timestep;
#endif
  VOID print();
} *MLRF_POST_DYNAMICS_DEPOT;


#define POST_DYN_DEPOT_FLOAT_SIZE (sizeof(sMLRF_POST_DYNAMICS_DEPOT) / sizeof(STP_PHYS_VARIABLE))

#endif // _SIMENG_MLRF_DEPOTS_H
