#ifndef EXA_SIMENG_ATOMIC_REF_H
#define EXA_SIMENG_ATOMIC_REF_H

#include <atomic>

template<typename T>
class tATOMIC_REF
{

  T& m_ref;

public:

  static_assert( __atomic_always_lock_free(sizeof(T),0), "This type doesn't fit in an atomic ref!");

  tATOMIC_REF(T& ref) : m_ref(ref) {}

  void store( T desired, std::memory_order order = std::memory_order_seq_cst ) const noexcept
  {
    __atomic_store(&m_ref, &desired, order);
  }

  T load(std::memory_order order = std::memory_order_seq_cst) const noexcept
  {
    return __atomic_load_n(&m_ref, order);
  }

};

#endif
