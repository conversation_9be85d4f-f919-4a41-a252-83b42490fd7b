/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include "event_queue_event.h"

#ifndef	_SIMENG_ASYNC_EVENTS_H
#define	_SIMENG_ASYNC_EVENTS_H

/** Synchronize the main/comm thread when receiving an event from the CP.
 *
 * Async Events work in this way: 
 * 1. The CP sends an event Request to all SPs
 * 2. Each SP (comm thread) replies with the soonest timestep that it can do
 *    that event.  
 * 3. The comm thread on the SP places an sEVENT_EVENT_REQ object
 * in the event queue, with a timestep equal to the soonest timestep.  
 * 4. The CP reduces the "soonest timestep" from all the SPs and finds the 
 *    maximum value.  
 * 5. The CP sends out the event, with the maximum timestep value.  
 * 6. When the sEVENT_EVENT_REQ is processed by the SP's main thread, it will
 *    pause and wait for the comm thread to receive the actual event from the CP.
 * 7. The SP's comm thread then receives the new event from the CP, processes
 *    it, and then signals the main thread to finish.
 */
namespace AsyncEventQueuePendingRequest { 
  void init(); 
  void set_event_req_pending(); 
  void wait_for_req_finish(); 
  void finish_req(); 
  bool is_event_req_pending(); 
};

// process method returns a flag indicating whether this event has been processed or not
#define	ASYNC_EVENT(_atype, _aid)                \
typedef 				         \
   class s##_atype : public sEVENT_QUEUE_EVENT { \
	public: s##_atype () { id = _aid; }      \
      BOOLEAN process();                         \
      VOID print();                              \
      BOOLEAN unique();                          \
   } * _atype 





ASYNC_EVENT(EVENT_EVENT_REQ, EVENT_ID_EVENT_REQ);
ASYNC_EVENT(EVENT_MME_CKPT, EVENT_ID_MME_CKPT);
ASYNC_EVENT(EVENT_MME_CKPT_EXIT, EVENT_ID_MME_CKPT_EXIT);
ASYNC_EVENT(EVENT_FULL_CKPT, EVENT_ID_FULL_CKPT);
ASYNC_EVENT(EVENT_FULL_CKPT_EXIT, EVENT_ID_FULL_CKPT_EXIT);
ASYNC_EVENT(EVENT_CKPT_INTERVAL, EVENT_ID_CKPT_INTERVAL);
ASYNC_EVENT(EVENT_THERMAL_ACCEL_ON, EVENT_ID_THERMAL_ACCEL_ON);
ASYNC_EVENT(EVENT_THERMAL_ACCEL_OFF, EVENT_ID_THERMAL_ACCEL_OFF);
ASYNC_EVENT(EVENT_TIMERS_ON, EVENT_ID_TIMERS_ON);
ASYNC_EVENT(EVENT_TIMERS_OFF, EVENT_ID_TIMERS_OFF);
ASYNC_EVENT(EVENT_DIAGS_ON, EVENT_ID_DIAGS_ON);
ASYNC_EVENT(EVENT_DIAGS_OFF, EVENT_ID_DIAGS_OFF);
ASYNC_EVENT(EVENT_MAXVEL, EVENT_ID_MAXVEL);
ASYNC_EVENT(EVENT_MAXTEMP, EVENT_ID_MAXTEMP);
ASYNC_EVENT(EVENT_MINTEMP, EVENT_ID_MINTEMP);
ASYNC_EVENT(EVENT_DELAY_VEL_WARNINGS, EVENT_ID_DELAY_VEL_WARNINGS);
ASYNC_EVENT(EVENT_DELAY_TEMP_WARNINGS, EVENT_ID_DELAY_TEMP_WARNINGS);
ASYNC_EVENT(EVENT_EXIT, EVENT_ID_EXIT);
ASYNC_EVENT(EVENT_HALT, EVENT_ID_HALT);
ASYNC_EVENT(EVENT_ENABLE_WARNING, EVENT_ID_ENABLE_WARNING);
ASYNC_EVENT(EVENT_DISABLE_WARNING, EVENT_ID_DISABLE_WARNING);
ASYNC_EVENT(EVENT_READ_TABLE, EVENT_ID_READ_TABLE);
ASYNC_EVENT(EVENT_READ_COUPLING_DATA, EVENT_ID_READ_COUPLING_DATA);
ASYNC_EVENT(EVENT_READ_DSM_FILE, EVENT_ID_READ_DSM_FILE);
ASYNC_EVENT(EVENT_ROTATIONAL_DYNAMICS, EVENT_ID_ROTATIONAL_DYNAMICS);
ASYNC_EVENT(EVENT_CLEAR_AVG_MME_DATA, EVENT_ID_CLEAR_AVG_MME_DATA);
ASYNC_EVENT(EVENT_AVG_MME_CKPT, EVENT_ID_AVG_MME_CKPT);
ASYNC_EVENT(EVENT_STOP_AVG_MME_CKPT, EVENT_ID_STOP_AVG_MME_CKPT);
ASYNC_EVENT(EVENT_RESCHEDULE_AVG_MME_CKPT, EVENT_ID_RESCHEDULE_AVG_MME_CKPT);
ASYNC_EVENT(EVENT_START_EMITTER, EVENT_ID_START_EMITTER);
ASYNC_EVENT(EVENT_START_WIPER, EVENT_ID_START_WIPER);
ASYNC_EVENT(EVENT_T_PDE_SOLVER_ON, EVENT_ID_T_PDE_SOLVER_ON);
ASYNC_EVENT(EVENT_T_LB_SOLVER_ON, EVENT_ID_T_LB_SOLVER_ON);
ASYNC_EVENT(EVENT_UNPACK_TBS_DATA, EVENT_ID_UNPACK_TBS_DATA);
ASYNC_EVENT(EVENT_CALIBRATION_RUN, EVENT_ID_CALIBRATION_RUN);
ASYNC_EVENT(EVENT_DELAY_UDS_WARNINGS, EVENT_ID_DELAY_UDS_WARNINGS);
VOID sim_finalize_coupling();
VOID sim_finalize_tbs_data();

/*
typedef class sEVENT_INVALID	    : sEVENT_QUEUE_EVENT {id = (EVENT_ID) EVENT_ID_EVENT_INVALID;}
*/

std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, dFLOAT arg, BASETIME timestep);
std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, dFLOAT arg, BASETIME timestep, sINT32 recur_period, BASETIME recur_end);
std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, 
                                              dFLOAT arg, 
                                              TIMESTEP realm_timestep, 
                                              sINT32 recur_period, 
                                              TIMESTEP recur_realm_end, 
                                              REALM realm);


#endif	/* #ifndef	_SIMENG_ASYNC_EVENTS_H */

