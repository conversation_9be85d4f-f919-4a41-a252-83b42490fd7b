/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*
 * turb_synth_info.cc
 *
 *  Created on: Feb 22, 2016
 *      Author: chona
 */

#include "dgf_reader_sp.h"
#include "turb_synth_info.h"

#define MAX_IO_CHUNK (32) // Maximum number of words to read from IO buffer

#define DOUBLESZ sizeof(dFLOAT)
#define FLOATSZ  sizeof(sFLOAT)
#define DCOMPLEXSZ sizeof(dCOMPLEX)
#define UINTSZ sizeof(uINT32)

//#define record_nbytes_in_window(NAME)                                                \
//  record.n_bytes_turb_synth_info_state += 3 * floatSz;                               \
//  uINT32 nVSpectrum = m_vturb_synth[i]->GetWindow(## NAME ##)->m_fourierVelX.size(); \
//  record.n_bytes_turb_synth_info_state += uintSz;                                    \
//  record.n_bytes_turb_synth_info_state += 4 * nVSpectrum *dcomplexSz;

static uINT32 nbytes_in_window(const cFFT_WINDOW *win)
{
  uINT32 nbytes = 0;
  if (win->IsValid()) {
    nbytes += 3 * sizeof(sFLOAT);
    nbytes += sizeof(BOOLEAN);
    uINT32 nVSpectrum = win->m_fourierVelX.size();
    uINT32 nSpatialModes = win->m_nSpatialModes;
    nbytes += 2 * sizeof(uINT32);
    nbytes += 4 * nVSpectrum * sizeof(dCOMPLEX);
    nbytes += 3 * nVSpectrum * nSpatialModes * sizeof(dCOMPLEX);
  }
  return nbytes;
}


size_t sTURB_SYNTH_INFO::ckpt_len() {


  size_t len = sizeof(size_t);
  uINT32 nInlets = m_vturb_synth.size();
  if (nInlets > 0) {
    len += UINTSZ;
    ccDOTIMES(i, nInlets) {
      if (m_vturb_synth[i] != NULL) {
        len += 4 * FLOATSZ;
        len += UINTSZ;
        len +=
            m_vturb_synth[i]->GetNumSpatialModes() * 3 * DOUBLESZ;
        len += 2 * sizeof(BOOLEAN);
        len += DOUBLESZ;
        const cFFT_WINDOW *win = m_vturb_synth[i]->GetWindow(CENTER_WINDOW_TYPE);
        len += nbytes_in_window(win);

        win = m_vturb_synth[i]->GetWindow(LEFT_WINDOW_TYPE);
        len += nbytes_in_window(win);

        win = m_vturb_synth[i]->GetWindow(RIGHT_WINDOW_TYPE);
        len += nbytes_in_window(win);
        len += 6 * DOUBLESZ;
      }
    }
  }
  return len;
}


inline VOID lgi_read_vector(std::vector<dCOMPLEX> *vft, uINT32 nF, uINT32 nChunk)
{
  if (vft->size() == 0)
    vft->resize(nF, 0);
  std::vector<dCOMPLEX> vctemp(nChunk, 0);
  uINT32 nOffset = 0;
  ccDOTIMES(n, nF / nChunk) {
    read_lgi(vctemp.data(), nChunk * sizeof(dCOMPLEX));
    std::copy(vctemp.begin(), vctemp.end(), vft->begin() + nOffset);
    nOffset += nChunk;
  }
}

inline VOID ckpt_window(LGI_TURB_SYNTH_INFO_STATE &record, cTURB_SYNTH_SIM *turb_synth,
                        WINDOW_TYPE winType)
{
  // Increment record size
  //record.n_bytes_turb_synth_info_state += 3 * sizeof(sFLOAT); // freqMin, freqMax, dFreq
  //record.n_bytes_turb_synth_info_state += sizeof(uINT32);     // nF

  FFT_WINDOW win = turb_synth->AccessWindow(winType);
  write_ckpt_lgi(&win->m_freqMin, sizeof(sFLOAT));
  write_ckpt_lgi(&win->m_freqMax, sizeof(sFLOAT));
  write_ckpt_lgi(&win->m_dFreq, sizeof(sFLOAT));
  write_ckpt_lgi(&win->m_isEvenNumTransform, sizeof(BOOLEAN));
  uINT32 nF = win->m_fourierVelX.size();
  write_ckpt_lgi(&nF, sizeof(uINT32));
  uINT32 nSpatialModes = win->m_nSpatialModes;
  write_ckpt_lgi(&nSpatialModes, sizeof(uINT32));

  // Increment by size of complex arrays
  record.n_bytes_turb_synth_info_state += nF * 4 * DCOMPLEXSZ +
                                          nF * 3 * DCOMPLEXSZ * nSpatialModes;

  ccDOTIMES(k, nF)
    write_ckpt_lgi(&win->m_fourierVelX[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    write_ckpt_lgi(&win->m_fourierVelY[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    write_ckpt_lgi(&win->m_fourierVelZ[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    write_ckpt_lgi(&win->m_fourierVmag[k], DCOMPLEXSZ);

  ccDOTIMES(k, nF * nSpatialModes)
    write_ckpt_lgi(&win->m_Ax[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF * nSpatialModes)
    write_ckpt_lgi(&win->m_Ay[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF * nSpatialModes)
    write_ckpt_lgi(&win->m_Az[k], DCOMPLEXSZ);
}

inline VOID ckpt_window(sCKPT_BUFFER& pio_ckpt_buff,cTURB_SYNTH_SIM *turb_synth,
                        WINDOW_TYPE winType)
{
  FFT_WINDOW win = turb_synth->AccessWindow(winType);
  pio_ckpt_buff.write(&win->m_freqMin);
  pio_ckpt_buff.write(&win->m_freqMax);
  pio_ckpt_buff.write(&win->m_dFreq);
  pio_ckpt_buff.write(&win->m_isEvenNumTransform);
  uINT32 nF = win->m_fourierVelX.size();
  pio_ckpt_buff.write(&nF);
  uINT32 nSpatialModes = win->m_nSpatialModes;
  pio_ckpt_buff.write(&nSpatialModes);

  ccDOTIMES(k, nF)
    pio_ckpt_buff.write(&win->m_fourierVelX[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    pio_ckpt_buff.write(&win->m_fourierVelY[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    pio_ckpt_buff.write(&win->m_fourierVelZ[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF)
    pio_ckpt_buff.write(&win->m_fourierVmag[k], DCOMPLEXSZ);

  ccDOTIMES(k, nF * nSpatialModes)
    pio_ckpt_buff.write(&win->m_Ax[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF * nSpatialModes)
    pio_ckpt_buff.write(&win->m_Ay[k], DCOMPLEXSZ);
  ccDOTIMES(k, nF * nSpatialModes)
    pio_ckpt_buff.write(&win->m_Az[k], DCOMPLEXSZ);
}

inline VOID read_ckpt_window(cTURB_SYNTH_SIM *turb_synth, WINDOW_TYPE winType)
{
  sFLOAT freqMin, freqMax, dFreq;
  BOOLEAN evenNumTransform;
  read_lgi(freqMin);
  read_lgi(freqMax);
  read_lgi(dFreq);
  read_lgi(evenNumTransform);
  uINT32 nF;
  read_lgi(nF);
  uINT32 nSpatialModes;
  read_lgi(nSpatialModes);

  FFT_WINDOW win = turb_synth->AccessWindow(winType);
  win->m_dFreq = dFreq;
  win->m_freqMax = freqMax;
  win->m_freqMin = freqMin;
  win->m_isEvenNumTransform = evenNumTransform;
  win->m_nSpatialModes = nSpatialModes;

  uINT32 nChunk = 1;
  while (nF % nChunk == 0 && nChunk <= MAX_IO_CHUNK)
    nChunk *= 2;
  nChunk /= 2;

  lgi_read_vector(&win->m_fourierVelX, nF, nChunk);
  lgi_read_vector(&win->m_fourierVelY, nF, nChunk);
  lgi_read_vector(&win->m_fourierVelZ, nF, nChunk);
  lgi_read_vector(&win->m_fourierVmag, nF, nChunk);

  lgi_read_vector(&win->m_Ax, nF*nSpatialModes, nChunk);
  lgi_read_vector(&win->m_Ay, nF*nSpatialModes, nChunk);
  lgi_read_vector(&win->m_Az, nF*nSpatialModes, nChunk);
  win->m_winType = winType;

}

VOID sTURB_SYNTH_INFO::ckpt_turb_synth_info_state() {

  uINT32 nInlets = m_vturb_synth.size();
  if (nInlets == 0) {
    return;
  }

  LGI_TURB_SYNTH_INFO_STATE record;
  record.n_bytes_turb_synth_info_state  = 0;
  // Write number of inlet BCs
  record.n_bytes_turb_synth_info_state += UINTSZ;
  // Save cTURB_SYNTH_SIM objects:
  //        Original m_freqMin, m_freqMax (from data)
  //        Extended m_freqMin, m_freqMax
  //        m_nSpatialModes random directions m_unit_normals
  //        2 BOOLEANs: m_winLeft->IsValid() and m_winRight->IsValid()
  //        m_W = maximum length scale
  //        Center, left and right FFT_windows
  //        m_turbIntens
  //        m_lenScales
  ccDOTIMES(i, nInlets) {
    if (m_vturb_synth[i] != NULL) {
      record.n_bytes_turb_synth_info_state += 4 * FLOATSZ;
      record.n_bytes_turb_synth_info_state += UINTSZ;
      record.n_bytes_turb_synth_info_state +=
          m_vturb_synth[i]->GetNumSpatialModes() * 3 * DOUBLESZ;
      record.n_bytes_turb_synth_info_state += 2 * sizeof(BOOLEAN);
      record.n_bytes_turb_synth_info_state += DOUBLESZ;
      const cFFT_WINDOW *win = m_vturb_synth[i]->GetWindow(CENTER_WINDOW_TYPE);
      uINT32 nbytes = nbytes_in_window(win);
      record.n_bytes_turb_synth_info_state += nbytes;

      win = m_vturb_synth[i]->GetWindow(LEFT_WINDOW_TYPE);
      nbytes = nbytes_in_window(win);
      record.n_bytes_turb_synth_info_state += nbytes;

      win = m_vturb_synth[i]->GetWindow(RIGHT_WINDOW_TYPE);
      nbytes = nbytes_in_window(win);
      record.n_bytes_turb_synth_info_state += nbytes;
      record.n_bytes_turb_synth_info_state += 6 * sizeof(dFLOAT);
    }
  }

  if (record.n_bytes_turb_synth_info_state > 0) {
    asINT32 record_length = sizeof(record) + record.n_bytes_turb_synth_info_state;
    lgi_write_init_tag (&record, LGI_TURB_SYNTH_INFO_STATE_TAG, record_length);
    write_ckpt_lgi_head(record);
    // Number of inlet BCs
    write_ckpt_lgi(&nInlets, UINTSZ);
    ccDOTIMES(i, nInlets) {
      if (m_vturb_synth[i] != NULL) {
        cTURB_SYNTH_SIM *turb_synth = m_vturb_synth[i];
        // m_win0.m_freqMin, m_win0.m_freqMax, m_freqMin, m_freqMax
        sFLOAT freqMin = turb_synth->GetWindow(ORIG_WINDOW_TYPE)->m_freqMin;
        sFLOAT freqMax = turb_synth->GetWindow(ORIG_WINDOW_TYPE)->m_freqMax;
        write_ckpt_lgi(&freqMin, FLOATSZ);
        write_ckpt_lgi(&freqMax, FLOATSZ);
        freqMin = turb_synth->GetMinFrequency();
        freqMax = turb_synth->GetMaxFrequency();
        write_ckpt_lgi(&freqMin, FLOATSZ);
        write_ckpt_lgi(&freqMax, FLOATSZ);
        // Number of spatial modes
        uINT32 nSpatialModes = turb_synth->GetNumSpatialModes();
        write_ckpt_lgi(&nSpatialModes, UINTSZ);
        // m_unit_normals
        dFLOAT *pUnitNormals = turb_synth->GetUnitNormals().data();
        write_ckpt_lgi(pUnitNormals, 3 * nSpatialModes * DOUBLESZ);
        // Is left window valid?
        BOOLEAN isValid = turb_synth->GetWindow(LEFT_WINDOW_TYPE)->IsValid();
        write_ckpt_lgi(isValid);
        // Is right window valid?
        isValid = turb_synth->GetWindow(RIGHT_WINDOW_TYPE)->IsValid();
        write_ckpt_lgi(isValid);
        // Write m_W
        dFLOAT W = turb_synth->GetW();
        write_ckpt_lgi(W);
        // Center window
        ckpt_window(record, turb_synth, CENTER_WINDOW_TYPE);
        if (turb_synth->GetWindow(LEFT_WINDOW_TYPE)->IsValid()) {
          ckpt_window(record, turb_synth, LEFT_WINDOW_TYPE);
        }
        if (turb_synth->GetWindow(RIGHT_WINDOW_TYPE)->IsValid()) {
          ckpt_window(record, turb_synth, RIGHT_WINDOW_TYPE);
        }
        // m_turbIntensities
        dFLOAT tbuf[3];
        const dFLOAT *turbIntens = turb_synth->GetTurbulentIntensities();
        std::copy(turbIntens, turbIntens + 3, tbuf);
        write_ckpt_lgi(tbuf, 3 * DOUBLESZ);
        // m_turbIntensities
        const dFLOAT *turbLen = turb_synth->GetTurbulentLengthScales();
        std::copy(turbLen, turbLen + 3, tbuf);
        write_ckpt_lgi(tbuf, 3 * DOUBLESZ);
      }
    }
  }
}

VOID sTURB_SYNTH_INFO::ckpt_turb_synth_info_state(sCKPT_BUFFER& pio_ckpt_buff) {

  uINT32 nInlets = m_vturb_synth.size();
  if (nInlets == 0) {
    return;
  }

  size_t len = ckpt_len();
  pio_ckpt_buff.write(&len);
  // Number of inlet BCs
  pio_ckpt_buff.write(&nInlets, UINTSZ);
  ccDOTIMES(i, nInlets) {
    if (m_vturb_synth[i] != NULL) {
      cTURB_SYNTH_SIM *turb_synth = m_vturb_synth[i];
      // m_win0.m_freqMin, m_win0.m_freqMax, m_freqMin, m_freqMax
      sFLOAT freqMin = turb_synth->GetWindow(ORIG_WINDOW_TYPE)->m_freqMin;
      sFLOAT freqMax = turb_synth->GetWindow(ORIG_WINDOW_TYPE)->m_freqMax;
      pio_ckpt_buff.write(&freqMin);
      pio_ckpt_buff.write(&freqMax);
      freqMin = turb_synth->GetMinFrequency();
      freqMax = turb_synth->GetMaxFrequency();
      pio_ckpt_buff.write(&freqMin);
      pio_ckpt_buff.write(&freqMax);
      // Number of spatial modes
      uINT32 nSpatialModes = turb_synth->GetNumSpatialModes();
      pio_ckpt_buff.write(&nSpatialModes);
      // m_unit_normals
      dFLOAT *pUnitNormals = turb_synth->GetUnitNormals().data();
      pio_ckpt_buff.write(pUnitNormals, 3 * nSpatialModes * DOUBLESZ);
      // Is left window valid?
      BOOLEAN isValid = turb_synth->GetWindow(LEFT_WINDOW_TYPE)->IsValid();
      pio_ckpt_buff.write(&isValid);
      // Is right window valid?
      isValid = turb_synth->GetWindow(RIGHT_WINDOW_TYPE)->IsValid();
      pio_ckpt_buff.write(&isValid);
      // Write m_W
      dFLOAT W = turb_synth->GetW();
      pio_ckpt_buff.write(&W);
      // Center window
      ckpt_window( pio_ckpt_buff,turb_synth, CENTER_WINDOW_TYPE);
      if (turb_synth->GetWindow(LEFT_WINDOW_TYPE)->IsValid()) {
        ckpt_window( pio_ckpt_buff,turb_synth, LEFT_WINDOW_TYPE);
      }
      if (turb_synth->GetWindow(RIGHT_WINDOW_TYPE)->IsValid()) {
        ckpt_window( pio_ckpt_buff, turb_synth, RIGHT_WINDOW_TYPE);
      }
      // m_turbIntensities
      dFLOAT tbuf[3];
      const dFLOAT *turbIntens = turb_synth->GetTurbulentIntensities();
      pio_ckpt_buff.write(turbIntens, 3 * DOUBLESZ);
      // m_turbIntensities
      const dFLOAT *turbLen = turb_synth->GetTurbulentLengthScales();
      pio_ckpt_buff.write(turbLen, 3 * DOUBLESZ);
    }
  }
}

VOID sTURB_SYNTH_INFO::read_ckpt_turb_synth_info_state() {

  LGI_TURB_SYNTH_INFO_STATE record;
  read_lgi_head(record);

  // Decrement this as records are read
  asINT32 nbytes_to_read = record.n_bytes_turb_synth_info_state;

  uINT32 nInlets;
  read_lgi(nInlets);

  ccDOTIMES(i, nInlets) {
    cTURB_SYNTH_SIM *turb_synth = new cTURB_SYNTH_SIM();
    sFLOAT freqMin, freqMax, dFreq;
    read_lgi(freqMin);
    read_lgi(freqMax);
    FFT_WINDOW win0 = turb_synth->AccessWindow(ORIG_WINDOW_TYPE);
    win0->m_freqMin = freqMin;
    win0->m_freqMax = freqMax;
    win0->m_dFreq = freqMin;
    read_lgi(freqMin);
    read_lgi(freqMax);
    turb_synth->SetFrequencyRange(freqMin, freqMax);
    uINT32 nSpatialModes;
    read_lgi(nSpatialModes);
    uINT32 nOffset = 0;
    uINT32 nChunk = 1;
    // Increase chunk size while it is a divisor of nSpatialModes or until nChunk < max chunk size
    while (nSpatialModes % nChunk == 0 && nChunk <= MAX_IO_CHUNK)
      nChunk *= 2;
    nChunk /= 2;
    std::vector<dFLOAT> vtemp(3 * nChunk, 0);
    turb_synth->SetNumSpatialModes(nSpatialModes);
    ccDOTIMES(n, nSpatialModes / nChunk) {
      read_lgi(vtemp.data(), 3 * nChunk * DOUBLESZ);
      turb_synth->SetUnitNormals(nSpatialModes, vtemp.data(), nOffset, 3 * nChunk);
      nOffset += 3 * nChunk;
    }
    BOOLEAN isLeftValid, isRightValid;
    read_lgi(isLeftValid);
    read_lgi(isRightValid);
    dFLOAT W;
    read_lgi(W);
    turb_synth->SetW(W);

    // Center window
    read_ckpt_window(turb_synth, CENTER_WINDOW_TYPE);
    // Left window
    if (isLeftValid) {
      read_ckpt_window(turb_synth, LEFT_WINDOW_TYPE);
    }
    // Right window
    if (isRightValid) {
      read_ckpt_window(turb_synth, RIGHT_WINDOW_TYPE);
    }

    // Read turbulent intensities
    dFLOAT turbIntens[3];
    read_lgi(turbIntens, 3 * DOUBLESZ);
    turb_synth->SetTurbulentIntensities(turbIntens);

    // Read turbulent length scales
    dFLOAT turbLen[3];
    read_lgi(turbLen, 3 * DOUBLESZ);
    turb_synth->SetTurbulentLengthScales(turbLen);

    // Add turb_synth object to vector
    m_vturb_synth.push_back(turb_synth);
  }

};
