/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Americas Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "surfel_table.h"
#include "sim.h"

sSURFEL_TABLE g_surfel_table[STP_N_REALMS];
#if BUILD_GPU
sMSFL_SIZES_TABLE g_msfl_sizes_table;
#endif

//----------------------------------------------------------------------------
// sSURFEL_TABLE::surfel_from_id
//----------------------------------------------------------------------------
SURFACE_SHOB sSURFEL_TABLE::surfel_from_id(SHOB_ID id)
{
  // binary search
  SHOB_ID_ORDER id_order;
  auto item  = std::lower_bound( m_surfels.begin(), m_surfels.end() , id, id_order );

  if (item == m_surfels.end())
    return NULL;

  SURFACE_SHOB surfel    = *item;

  if(surfel != NULL && surfel->id() != id)
    surfel = NULL;

  return surfel;
}


