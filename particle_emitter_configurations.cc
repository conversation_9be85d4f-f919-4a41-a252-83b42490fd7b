/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_emitter_configurations.h"
#include "eqns.h"

asINT32 sPARTICLE_EMITTER_CONFIGURATION_BASE::m_id_count;

sPARTICLE_VAR sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE::sample_spray_velocity_magnitude() {
  sPARTICLE_VAR velocity_magnitude = 0.0;
  do {
    velocity_magnitude = g_random_particle_properties->random_number(velocity_magnitude_distribution_param1(), velocity_magnitude_distribution_param2(), m_velocity_magnitude_distribution);
  } while( velocity_magnitude < 0.0);
  return velocity_magnitude;
}

dFLOAT sNOZZLE_EMITTER_CONFIGURATION_BASE::expected_particle_diameter(){
  return g_random_particle_properties->expected_mean(diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}

dFLOAT sNOZZLE_EMITTER_CONFIGURATION_BASE::expected_particle_diameter_stddev(){
  return g_random_particle_properties->expected_stddev(diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}

SIMUTILS::ePDF_TYPES convert_pdfs_pdf_type_to_simutils_pdf_type(sPDF::ePDF_TYPE pdfs_pdf_type);
dFLOAT sNOZZLE_EMITTER_CONFIGURATION_BASE::expected_particle_volume() {

  sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER simutils_helper(this->simutils_emission_rate_type(),
                                           this->emission_rate(),
                                           convert_pdfs_pdf_type_to_simutils_pdf_type(m_diameter_distribution),
                                           diameter_distribution_param1(),
                                           diameter_distribution_param2(),
                                           m_diameter_min,
                                           m_diameter_max,
                                           (SIMUTILS::ePDF_TYPES)this->material()->density_distribution(),
                                           this->material()->density_distribution_param1(),
                                           this->material()->density_distribution_param2());

  return simutils_helper.expected_particle_volume();
}

dFLOAT sRAIN_EMITTER_CONFIGURATION::expected_particle_diameter(){
  return g_random_particle_properties->expected_mean(diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}

dFLOAT sRAIN_EMITTER_CONFIGURATION::expected_particle_diameter_stddev(){
  return g_random_particle_properties->expected_stddev(diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}

dFLOAT sRAIN_EMITTER_CONFIGURATION::expected_particle_volume() {
  return M_PI / 6.0 * g_random_particle_properties->third_order_moment(diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}

dFLOAT sRAIN_EMITTER_CONFIGURATION::evaluate_particle_diameter_pdf(sPARTICLE_VAR diameter) {
  return g_random_particle_properties->pdf(diameter, diameter_distribution_param1(), diameter_distribution_param2(), m_diameter_distribution);
}


sPARTICLE_VAR sKNOWN_DIAMETER_SPRAY_DATA_BASE::sample_spray_droplet_size(){

  dFLOAT diameter_sample;
  if(m_limit_diameter) { //Liquid particles materials have diameters that must be greater than zero and less than six_mm.
    //generate a diameter
    asINT32 percent_failure = 0;
    do{
      diameter_sample = g_random_particle_properties->random_number(diameter_distribution_param1(),
                                                                    diameter_distribution_param2(),
                                                                    diameter_distribution());
      percent_failure ++;
    } while( ( (diameter_sample <= m_diameter_min) ||
               (diameter_sample > m_diameter_max) ) &&
             (percent_failure < 100)); //added dmin and dmax limit on 7/28/21

    //Don't let the simulation hang becasue of the inability to find a diameter that satisfies the 6mm limit.
    if(percent_failure > 99) {
      sPARTICLE_VAR expected_diameter_mean = g_random_particle_properties->expected_mean(diameter_distribution_param1(),
                                                                                         diameter_distribution_param2(),
                                                                                         diameter_distribution());
      sPARTICLE_VAR expected_diameter_stddev = g_random_particle_properties->expected_stddev(diameter_distribution_param1(),
                                                                                             diameter_distribution_param2(),
                                                                                             diameter_distribution());

#ifdef MERGE_VINIT
      msg_error("An emitter failed at least 99%% of the time to find a diameter on the "
                "interval (%gmm, %gmm) that conforms to a mean of %g(mm) and stddev of %g(mm) (dist type %s).  "
                "To avoid poor performance, check the emitter configuration properties.",
                expected_diameter_mean * LENGTH_TO_MKS_SCALE_FACTOR * 1000.0,
                expected_diameter_stddev * LENGTH_TO_MKS_SCALE_FACTOR * 1000.0,
                sPDF::pdfs_distribution_names[diameter_distribution()].c_str());
#endif
    }
  } else { //solid particles need to have diameters greater than zero (massless tracers may have any diameter)
    BOOLEAN massless_tracer = this->material()->is_massless_tracer();
    asINT32 percent_failure = 0;
    do {
      diameter_sample = g_random_particle_properties->random_number(diameter_distribution_param1(),
                                                                    diameter_distribution_param2(),
                                                                    diameter_distribution());
      percent_failure++;
    } while( (diameter_sample <= 0 && !massless_tracer) &&
             percent_failure < 100); //Case check should protect against too many itterations (PR41213 indicates it is not for massless tracers though).

    if(percent_failure > 99) {
      sPARTICLE_VAR expected_diameter_mean = g_random_particle_properties->expected_mean(diameter_distribution_param1(),
                                                                                         diameter_distribution_param2(),
                                                                                         diameter_distribution());
      sPARTICLE_VAR expected_diameter_stddev = g_random_particle_properties->expected_stddev(diameter_distribution_param1(),
                                                                                             diameter_distribution_param2(),
                                                                                             diameter_distribution());
#ifdef MERGE_VINIT
      msg_error("An emitter failed at least 99%% of the time to find a diameter on the "
                "interval (0, Inf)mm that conforms to a mean of %g(mm) and stddev of %g(mm) (dist type %s).  "
                "To avoid poor performance, check the emitter configuration properties.",
                expected_diameter_mean * LENGTH_TO_MKS_SCALE_FACTOR * 1000.0,
                expected_diameter_stddev * LENGTH_TO_MKS_SCALE_FACTOR * 1000.0,
                sPDF::pdfs_distribution_names[diameter_distribution()].c_str());
#endif
    }
  }
  return(diameter_sample);
}

#define ELLIPSE_ANGLE_TOL  1.0e-5

VOID elliptic_direction_with_angles(sPARTICLE_VAR direction[3], sPARTICLE_VAR x, sPARTICLE_VAR y)
{
  sdFLOAT r = sqrt(cos(y)*cos(y) + sin(y)*sin(y)*cos(x)*cos(x));

  if(r < ELLIPSE_ANGLE_TOL)
    {
      direction[0] = 0.0;
      direction[1] = 0.0;
      direction[2] = sin(y); //cos(y) = 0.0 -> sin(y) = 1 or -1
    }
  else
    {
      direction[0] = cos(x) * cos(y) / r;
      direction[1] = sin(x) * cos(y) / r;
      direction[2] = cos(x) * sin(y) / r;
    }
}

//Because we consider 2d distribution in this function, we can not use the pdfs functions directly
//direction[1] has half cone angle axis_len1
//direction[2] has half cone angle axis_len2
VOID sample_elliptic_direction(sPARTICLE_VAR direction[3], sPARTICLE_VAR axis_len1, sPARTICLE_VAR axis_len2, sPDF::ePDF_TYPE m_distribution, sPARTICLE_VAR limit1, sPARTICLE_VAR limit2) //limits added for support of CDI/513
{
  //p(x,y) != p(x)*p(y), use integration on a ellipse
  if(m_distribution == sPDF::UNIFORM_DISTRIBUTION)
    {
      sdFLOAT angle_tol = 1.0e-4;
      sdFLOAT cdf = 0.5*(g_random_particle_properties->uniform() + 1.0);

      sdFLOAT bot = 0, top = M_PI;
      sdFLOAT mid = (bot + top) / 2.0;

      while(top - bot > angle_tol)
        {
          sdFLOAT val_mid = (mid + sin(mid)) / M_PI;

          if(cdf > val_mid) bot = mid;
          else top = mid;

          mid = (bot + top) / 2;
        }

      mid /= 2.0;

      sdFLOAT x = axis_len1 * sin(mid);  //>= 0
      if(g_random_particle_properties->uniform() < 0.0) x = -x;

      sdFLOAT y = g_random_particle_properties->uniform() * axis_len2 * cos(mid);

      elliptic_direction_with_angles(direction, x, y);
    }
  else
    {
      //p(x, y) = p(x)*p(y)

      sdFLOAT x, y, r;

      if(axis_len1 < ELLIPSE_ANGLE_TOL && axis_len2 < ELLIPSE_ANGLE_TOL)
        {
          direction[0] = 1.0;
          direction[1] = 0.0;
          direction[2] = 0.0;
        }
      else if(axis_len1 < ELLIPSE_ANGLE_TOL)
        {
          x = 0.0;
          y = g_random_particle_properties->random_number(0.0, axis_len2, m_distribution);

          direction[0] = cos(y);
          direction[1] = 0.0;
          direction[2] = sin(y);
        }
      else if(axis_len2 < ELLIPSE_ANGLE_TOL)
        {
          x = g_random_particle_properties->random_number(0.0, axis_len1, m_distribution);
          y = 0.0;

          direction[0] = cos(x);
          direction[1] = sin(x);
          direction[2] = 0.0;
        }
      else
        {
          x = g_random_particle_properties->random_number(0.0, axis_len1, m_distribution);
          y = g_random_particle_properties->random_number(0.0, axis_len2, m_distribution);

          elliptic_direction_with_angles(direction, x, y);
        }
    }
}


VOID sFULL_CONE_NOZZLE_CONFIGURATION::sample_spray_direction(sPARTICLE_VAR velocity_direction[3])
{
  if(m_cone_half_angle_distribution == sPDF::UNIFORM_DISTRIBUTION)
    {
      //use uniform on a disk method
      sdFLOAT arg_min = 1.0; //cos(M_PI/180.0 * (0.0)/2.0);
      sdFLOAT arg_max = cos(m_cone_half_angle.value);  //powercase provides the half angle in the cdi file
      sdFLOAT alpha = (arg_min - arg_max) * (0.5 * (g_random_particle_properties->uniform() + 1.0)) + arg_max;
      sdFLOAT phi = acos(alpha);
      sdFLOAT theta = M_PI * (g_random_particle_properties->uniform() + 1.0);

      velocity_direction[0] = cos(phi);
      velocity_direction[1] = sin(phi) * cos(theta);
      velocity_direction[2] = sin(phi) * sin(theta);
    }
  else
    {
      //In the full cone case, the mean angle from the major axis is zero and the envelope should be enclosed within +- the cones half angle.
      //So for a uniform distribution, the overal range is 2 * the half angle.  For gaussian, the stddev is 2x the half angle.
      sdFLOAT phi = m_cone_angle_generator->random();
      phi = std::fabs(phi);
      sdFLOAT theta = M_PI * (g_random_particle_properties->uniform() + 1.0);

      velocity_direction[0] = cos(phi);
      velocity_direction[1] = sin(phi) * cos(theta);
      velocity_direction[2] = sin(phi) * sin(theta);
    }
}

VOID sHOLLOW_CONE_NOZZLE_CONFIGURATION::sample_spray_direction(sPARTICLE_VAR velocity_direction[3])
{
  if(m_angle_distribution == sPDF::UNIFORM_DISTRIBUTION)
    {
      //use uniform on a disk method
      sdFLOAT arg_min = cos(m_mean_angle.value - m_angle_stddev.value/2.0);  //m_angle_stddev.value is the range
      sdFLOAT arg_max = cos(m_mean_angle.value + m_angle_stddev.value/2.0);  //m_angle_stddev.value is the range
      sdFLOAT alpha = (arg_min-arg_max) * (0.5 * (g_random_particle_properties->uniform() + 1.0)) + arg_max;
      sdFLOAT phi = acos(alpha);
      sdFLOAT theta = M_PI * (g_random_particle_properties->uniform() + 1.0);

      velocity_direction[0] = cos(phi);
      velocity_direction[1] = sin(phi) * cos(theta);
      velocity_direction[2] = sin(phi) * sin(theta);
    }
  else
    {
      sdFLOAT phi;
      if(m_angle_distribution == sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION) {
        do {
          phi = m_cone_angle_generator->random();
        } while ( phi < m_inner_half_angle_limit.value ); //pick another sample if this one is too small
      } else
        phi = m_cone_angle_generator->random();

      sdFLOAT theta = M_PI * (g_random_particle_properties->uniform() + 1.0);
      velocity_direction[0] = cos(phi);   //the convention used here is that phi = 0 is in the direction of e_1 for all theta, and theta = 0 lies in the x-y plane for all phi
      velocity_direction[1] = sin(phi) * cos(theta);
      velocity_direction[2] = sin(phi) * sin(theta);
    }
}

VOID sELLIPTICAL_CONE_NOZZLE_CONFIGURATION::sample_spray_direction(sPARTICLE_VAR velocity_direction[3])
{
  sample_elliptic_direction(velocity_direction,
                            m_major_cone_half_angle_distribution_param1.value,
                            m_minor_cone_half_angle_distribution_param1.value,
                            m_major_cone_half_angle_distribution,
                            m_major_half_angle_limit.value,
                            m_minor_half_angle_limit.value);
}



VOID sRAIN_EMITTER_CONFIGURATION::sample_spray_direction(sPARTICLE_VAR velocity_direction[3]){
}

//Tire emitter functions
asINT32 sTIRE_EMITTER_CONFIGURATION::find_interpolation_neighbor_nodes(sPARTICLE_VAR angle) {
  //Return the index to the first interpolation node whose interval this angle is in.
  //This routine assumes that the m_configuration_interpolation_nodes vector has been sorted with increasing tire_arc_positions.
  asINT32 first_interpolation_node_index = -1;
  asINT32 num_nodes = m_configuration_interpolation_nodes.size();
  ccDOTIMES(station_index, num_nodes - 1) {
    if(angle > m_configuration_interpolation_nodes[station_index].m_tire_arc_position.value &&
       angle <= m_configuration_interpolation_nodes[station_index + 1].m_tire_arc_position.value) {
      first_interpolation_node_index = station_index;
      break;
    }
  }
  if(first_interpolation_node_index == -1) {
    //The provided angle was outside all interpolation intervals so parameters will be extrapolated insted of interpolated (also this should never happen except maybe due to machine precision).
    if (angle <= m_configuration_interpolation_nodes[0].m_tire_arc_position.value)
      first_interpolation_node_index = 0;  //the angle is before the first station
    else
      first_interpolation_node_index = num_nodes - 2; //the angle is after the last station
  }
  return first_interpolation_node_index;
}


sPARTICLE_VAR sTIRE_EMITTER_CONFIGURATION::sample_arc_position() {
  sPARTICLE_VAR angle;
  do {
    angle = g_random_particle_properties->random_number(m_pdfs_angular_emission_dist_param1,
                                                        m_pdfs_angular_emission_dist_param2,
                                                        m_pdfs_angular_emission_dist_param3,
                                                        m_angular_emission_distribution_type);
    //When using the truncated gaussian distribution, which is non zero on [mu-C, mu+C], samples less than mu need to be discarded.
    //This is an improvment over using an untruncated gaussian disribution where samples less than mu and greater than mu+C need to be discarded.
    //Alternativly, the mean could me set to zero, the absolute value coulde me taken, and then shifted back to the origional mean but there is no way to indicate that scheme in simutils functions.
  } while ((angle < m_start) || (angle > m_end)); //For all other distributions, this loop will only have one itteration.
  return(angle);
}

sPARTICLE_VAR sTIRE_EMITTER_CONFIGURATION::sample_spray_velocity_magnitude(sPARTICLE_VAR angle){
  sPARTICLE_VAR velocity_magnitude_sample = sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(diameter_distribution_param1(angle),
                                                                                         diameter_distribution_param2(angle),
                                                                                         diameter_distribution(),
                                                                                         diameter_min(angle),
                                                                                         diameter_max(angle),
                                                                                         m_material,
                                                                                         m_material->is_liquid(), //limits diameter to 6mm when true
                                                                                         velocity_magnitude_distribution_param1(angle),
                                                                                         velocity_magnitude_distribution_param1(angle),
                                                                                         velocity_magnitude_distribution()).sample_spray_velocity_magnitude();
  return velocity_magnitude_sample;
}

sPARTICLE_VAR sTIRE_EMITTER_CONFIGURATION::sample_spray_droplet_size(sPARTICLE_VAR angle){
  sPARTICLE_VAR diameter_sample = sKNOWN_DIAMETER_SPRAY_DATA_BASE(diameter_distribution_param1(angle),
                                                                  diameter_distribution_param2(angle),
                                                                  diameter_distribution(),
                                                                  diameter_min(angle),
                                                                  diameter_max(angle),
                                                                  m_material,
                                                                  m_material->is_liquid()).sample_spray_droplet_size();
  return diameter_sample;
}

VOID sTIRE_EMITTER_CONFIGURATION::sample_spray_direction(sPARTICLE_VAR angle, sPARTICLE_VAR velocity_direction[3]) {
  sPARTICLE_VAR local_half_angle = cone_half_angle(angle);
  sPARTICLE_VAR local_stretch_factor = stretch_factor(angle);
  sample_elliptic_direction(velocity_direction,
                            local_half_angle,
                            local_half_angle * local_stretch_factor,
                            cone_half_angle_distribution(),
                            -1,-1); //Major and minor limits are not available for a tire emitter.
}

//Constructors:

sKNOWN_DIAMETER_SPRAY_DATA_BASE::sKNOWN_DIAMETER_SPRAY_DATA_BASE(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                 std::string &diameter_distribution_param1_variable_name,
                                                                 std::string &diameter_distribution_param2_variable_name,
                                                                 PARTICLE_MATERIAL material,
                                                                 BOOLEAN limit_diameter)
{

  //Set the variables that are common to all lgi nozzle types.
  m_diameter_distribution_param1.value = nozzle_record.diameter_distribution_param1_constant;
  m_diameter_distribution_param1.name = NULL;
  if(nozzle_record.diameter_distribution_param1_variable_name_length !=0) {
    m_diameter_distribution_param1.name = cnew char[nozzle_record.diameter_distribution_param1_variable_name_length + 1];
    strncpy((char*)m_diameter_distribution_param1.name, diameter_distribution_param1_variable_name.c_str(),  nozzle_record.diameter_distribution_param1_variable_name_length);
  }

  m_diameter_distribution_param2.value = nozzle_record.diameter_distribution_param2_constant;
  m_diameter_distribution_param2.name = NULL;
  if(nozzle_record.diameter_distribution_param2_variable_name_length !=0) {
    m_diameter_distribution_param2.name = cnew char[nozzle_record.diameter_distribution_param2_variable_name_length + 1];
    strncpy((char*)m_diameter_distribution_param2.name, diameter_distribution_param2_variable_name.c_str(),  nozzle_record.diameter_distribution_param1_variable_name_length);
  }

  m_diameter_min = nozzle_record.diameter_min;
  m_diameter_max = nozzle_record.diameter_max;
  
  if (m_diameter_max == -1) {
    const sPARTICLE_VAR six_mm = 0.006 / LENGTH_TO_MKS_SCALE_FACTOR;
    m_diameter_max = six_mm;
  }

  m_diameter_distribution = lgi_to_pdfs_distribution(nozzle_record.diameter_distribution);
 
  m_limit_diameter = limit_diameter && !g_override_emitter_diameter_limit;
  m_material_index = material->id();
  m_material = material;

}



sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE::sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                                           std::string &diameter_distribution_param1_variable_name,
                                                                                           std::string &diameter_distribution_param2_variable_name,
                                                                                           PARTICLE_MATERIAL material,
                                                                                           BOOLEAN limit_diameter,
                                                                                           std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                                           std::string &velocity_magnitude_distribution_param2_variable_name) :
  sKNOWN_DIAMETER_SPRAY_DATA_BASE(nozzle_record,
                                  diameter_distribution_param1_variable_name,
                                  diameter_distribution_param2_variable_name,
                                  material,
                                  limit_diameter)
{


  //Set the parameters from the lgi record that are nozzle type specific.
  switch(nozzle_record.nozzle_type) {
  case FULL_CONE_NOZZLE:
    m_velocity_magnitude_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.full_cone_params.velocity_distribution);

    m_velocity_magnitude_distribution_param1.value = nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_constant;
    m_velocity_magnitude_distribution_param1.name = nullptr;
    if(nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length +1];
      strncpy((char*)m_velocity_magnitude_distribution_param1.name, velocity_magnitude_distribution_param1_variable_name.c_str(), nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    }

    m_velocity_magnitude_distribution_param2.value = nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_constant;
    m_velocity_magnitude_distribution_param2.name = nullptr;
    if(nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param2.name, velocity_magnitude_distribution_param2_variable_name.c_str(), nozzle_record.specialized_parameters.full_cone_params.velocity_magnitude_distribution_param2_variable_name_length);
    }

    break;
  case HOLLOW_CONE_NOZZLE:
    m_velocity_magnitude_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.hollow_cone_params.velocity_distribution);

    m_velocity_magnitude_distribution_param1.value = nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_constant;
    m_velocity_magnitude_distribution_param1.name = nullptr;
    if(nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param1.name, velocity_magnitude_distribution_param1_variable_name.c_str(), nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    }

    m_velocity_magnitude_distribution_param2.value = nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_constant;
    m_velocity_magnitude_distribution_param2.name = nullptr;
    if(nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param2.name, velocity_magnitude_distribution_param2_variable_name.c_str(), nozzle_record.specialized_parameters.hollow_cone_params.velocity_magnitude_distribution_param2_variable_name_length);
    }

    break;
  case ELLIPTICAL_CONE_NOZZLE:
    m_velocity_magnitude_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.elliptical_cone_params.velocity_distribution);

    m_velocity_magnitude_distribution_param1.value = nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_constant;
    m_velocity_magnitude_distribution_param1.name = nullptr;
    if(nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param1.name, velocity_magnitude_distribution_param1_variable_name.c_str(), nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param1_variable_name_length);
    }

    m_velocity_magnitude_distribution_param2.value = nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_constant;
    m_velocity_magnitude_distribution_param2.name = nullptr;
    if(nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param2.name, velocity_magnitude_distribution_param2_variable_name.c_str(), nozzle_record.specialized_parameters.elliptical_cone_params.velocity_magnitude_distribution_param2_variable_name_length);
    }

    break;
  case TIRE_NOZZLE:
    m_velocity_magnitude_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.tire_params.velocity_distribution);

    m_velocity_magnitude_distribution_param1.value = nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param1_constant;
    m_velocity_magnitude_distribution_param1.name = nullptr;
    if(nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param1.name, velocity_magnitude_distribution_param1_variable_name.c_str(), nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param1_variable_name_length);
    }

    m_velocity_magnitude_distribution_param2.value = nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param2_constant;
    m_velocity_magnitude_distribution_param2.name = nullptr;
    if(nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length !=0) {
      m_velocity_magnitude_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length + 1];
      strncpy((char*)m_velocity_magnitude_distribution_param2.name, velocity_magnitude_distribution_param2_variable_name.c_str(), nozzle_record.specialized_parameters.tire_params.velocity_magnitude_distribution_param2_variable_name_length);
    }
    break;
  case RAIN_DATA:
  default:
    msg_error("Attempt to construct a nozzle emitter base with the wrong lgi nozzle record type.");
  }

}

sNOZZLE_EMITTER_CONFIGURATION_BASE::sNOZZLE_EMITTER_CONFIGURATION_BASE(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                                                       std::string &name,
                                                                       PARTICLE_MATERIAL material,
                                                                       std::string &emission_rate_variable_name,
                                                                       sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                       std::string &diameter_distribution_param1_variable_name,
                                                                       std::string &diameter_distribution_param2_variable_name,
                                                                       std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                       std::string &velocity_magnitude_distribution_param2_variable_name) :
  sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(nozzle_record,
                                               diameter_distribution_param1_variable_name,
                                               diameter_distribution_param2_variable_name,
                                               material,
                                               material->is_liquid(), //if the material is liquid, limit the maximum diameter (to 6mm)
                                               velocity_magnitude_distribution_param1_variable_name,
                                               velocity_magnitude_distribution_param2_variable_name) ,
  sPARTICLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                       name,
                                       emission_rate_variable_name)
{
  //Nothing special needs to be done here.
}



sFULL_CONE_NOZZLE_CONFIGURATION::sFULL_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                                                 sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                 std::string &name,
                                                                 PARTICLE_MATERIAL material,
                                                                 std::string &emission_rate_variable_name,
                                                                 std::string &diameter_distribution_param1_variable_name,
                                                                 std::string &diameter_distribution_param2_variable_name,
                                                                 std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                 std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                 std::string &cone_half_angle_variable_name,
                                                                 std::string &outer_half_angle_limit_variable_name) :


  sNOZZLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                     name,
                                     material,
                                     emission_rate_variable_name,
                                     nozzle_record,
                                     diameter_distribution_param1_variable_name,
                                     diameter_distribution_param2_variable_name,
                                     velocity_magnitude_distribution_param1_variable_name,
                                     velocity_magnitude_distribution_param2_variable_name)

{
  m_cone_half_angle_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.full_cone_params.cone_half_angle_distribution);

  m_cone_half_angle.value = nozzle_record.specialized_parameters.full_cone_params.cone_half_angle_constant;
  m_cone_half_angle.name = nullptr;
  if (nozzle_record.specialized_parameters.full_cone_params.cone_half_angle_variable_name_length != 0) {
    m_cone_half_angle.name = cnew char[nozzle_record.specialized_parameters.full_cone_params.cone_half_angle_variable_name_length + 1];
    strncpy((char*)m_cone_half_angle.name,
            cone_half_angle_variable_name.c_str(),
            nozzle_record.specialized_parameters.full_cone_params.cone_half_angle_variable_name_length);
  }

  m_outer_half_angle_limit.value = nozzle_record.specialized_parameters.full_cone_params.outer_half_angle_limit_constant;
  m_outer_half_angle_limit.name = nullptr;
  if (nozzle_record.specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length != 0) {
    m_outer_half_angle_limit.name = cnew char[nozzle_record.specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length + 1];
    strncpy((char*)m_outer_half_angle_limit.name,
            outer_half_angle_limit_variable_name.c_str(),
            nozzle_record.specialized_parameters.full_cone_params.outer_half_angle_limit_variable_name_length);
  }

  //If a gaussian disribution is used, switch the distribution from gaussian to truncated gaussian so the outer cone angle limit can be applied.
  if (m_cone_half_angle_distribution == sPDF::GAUSSIAN_DISTRIBUTION ) {
    m_cone_half_angle_distribution = sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION;
  }

  m_cone_angle_generator = cnew tPDF<sPARTICLE_VAR>(m_cone_half_angle_distribution);

  if(m_cone_half_angle_distribution == sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION)
    m_cone_angle_generator->set_params(0.0,  m_cone_half_angle.value, m_outer_half_angle_limit.value);
  else
    m_cone_angle_generator->set_params(0.0,  m_cone_half_angle.value);
}



sHOLLOW_CONE_NOZZLE_CONFIGURATION::sHOLLOW_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                                                     sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                     std::string &name,
                                                                     PARTICLE_MATERIAL material,
                                                                     std::string &emission_rate_variable_name,
                                                                     std::string &diameter_distribution_param1_variable_name,
                                                                     std::string &diameter_distribution_param2_variable_name,
                                                                     std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                     std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                     std::string &mean_angle_variable_name,
                                                                     std::string &angle_stddev_variable_name,
                                                                     std::string &outer_half_angle_limit_variable_name,
                                                                     std::string &inner_half_angle_limit_variable_name) :
  sNOZZLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                     name,
                                     material,
                                     emission_rate_variable_name,
                                     nozzle_record,
                                     diameter_distribution_param1_variable_name,
                                     diameter_distribution_param2_variable_name,
                                     velocity_magnitude_distribution_param1_variable_name,
                                     velocity_magnitude_distribution_param2_variable_name)

{
  m_mean_angle.value = nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param1_constant;
  m_mean_angle.name = nullptr;
  if(nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length !=0 ){
    m_mean_angle.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length + 1];
    strncpy((char*)m_mean_angle.name,
            mean_angle_variable_name.c_str(),
            nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length);
  }

  m_angle_stddev.value = nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param2_constant;
  m_angle_stddev.name = nullptr;
  if(nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param2_variable_name_length !=0 ){
    m_angle_stddev.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param2_variable_name_length + 1];
    strncpy((char*)m_angle_stddev.name,
            angle_stddev_variable_name.c_str(),
            nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution_param1_variable_name_length);
  }

  m_angle_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.hollow_cone_params.angle_distribution);


  m_outer_half_angle_limit.value = nozzle_record.specialized_parameters.hollow_cone_params.outer_half_angle_limit_constant;
  m_outer_half_angle_limit.name = nullptr;
  if (nozzle_record.specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length != 0) {
    m_outer_half_angle_limit.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length + 1];
    strncpy((char*)m_outer_half_angle_limit.name,
            outer_half_angle_limit_variable_name.c_str(),
            nozzle_record.specialized_parameters.hollow_cone_params.outer_half_angle_limit_variable_name_length);
  }

  m_inner_half_angle_limit.value = nozzle_record.specialized_parameters.hollow_cone_params.inner_half_angle_limit_constant;
  m_inner_half_angle_limit.name = nullptr;
  if (nozzle_record.specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length != 0) {
    m_inner_half_angle_limit.name = cnew char[nozzle_record.specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length + 1];
    strncpy((char*)m_inner_half_angle_limit.name,
            inner_half_angle_limit_variable_name.c_str(),
            nozzle_record.specialized_parameters.hollow_cone_params.inner_half_angle_limit_variable_name_length);
  }

  //If an outer cone angle limit was specified with a gaussian disribution, switch the distribution from gaussian to truncated gaussian
  if(m_angle_distribution == sPDF::GAUSSIAN_DISTRIBUTION) {
    m_angle_distribution = sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION;
  }

  m_cone_angle_generator = cnew tPDF<sPARTICLE_VAR>(m_angle_distribution);

  if(m_angle_distribution == sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION)
    m_cone_angle_generator->set_params(m_mean_angle.value,  m_angle_stddev.value, m_outer_half_angle_limit.value);
  else
    m_cone_angle_generator->set_params(m_mean_angle.value,  m_angle_stddev.value);


}



sELLIPTICAL_CONE_NOZZLE_CONFIGURATION::sELLIPTICAL_CONE_NOZZLE_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                                                             sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                                                             std::string &name,
                                                                             PARTICLE_MATERIAL material,
                                                                             std::string &emission_rate_variable_name,
                                                                             std::string &diameter_distribution_param1_variable_name,
                                                                             std::string &diameter_distribution_param2_variable_name,
                                                                             std::string &velocity_magnitude_distribution_param1_variable_name,
                                                                             std::string &velocity_magnitude_distribution_param2_variable_name,
                                                                             std::string &major_cone_half_angle_distribution_param1_variable_name,
                                                                             std::string &major_cone_half_angle_distribution_param2_variable_name,
                                                                             std::string &minor_cone_half_angle_distribution_param1_variable_name,
                                                                             std::string &minor_cone_half_angle_distribution_param2_variable_name,
                                                                             std::string &major_outer_half_angle_limit_variable_name,
                                                                             std::string &minor_outer_half_angle_limit_variable_name) :

  sNOZZLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                     name,
                                     material,
                                     emission_rate_variable_name,
                                     nozzle_record,
                                     diameter_distribution_param1_variable_name,
                                     diameter_distribution_param2_variable_name,
                                     velocity_magnitude_distribution_param1_variable_name,
                                     velocity_magnitude_distribution_param2_variable_name)

{
  m_major_cone_half_angle_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution);

  m_major_cone_half_angle_distribution_param1.value = nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_constant;
  m_major_cone_half_angle_distribution_param1.name = nullptr;
  if(nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length !=0 ){
    m_major_cone_half_angle_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length + 1];
    strncpy((char*)m_major_cone_half_angle_distribution_param1.name,
            major_cone_half_angle_distribution_param1_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param1_variable_name_length);
  }

  m_major_cone_half_angle_distribution_param2.value = nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_constant;
  m_major_cone_half_angle_distribution_param2.name = nullptr;
  if(nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length !=0 ){
    m_major_cone_half_angle_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length + 1];
    strncpy((char*)m_major_cone_half_angle_distribution_param2.name,
            major_cone_half_angle_distribution_param2_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.major_cone_half_angle_distribution_param2_variable_name_length);
  }


  m_minor_cone_half_angle_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution);

  m_minor_cone_half_angle_distribution_param1.value = nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_constant;
  m_minor_cone_half_angle_distribution_param1.name = nullptr;
  if(nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length !=0 ){
    m_minor_cone_half_angle_distribution_param1.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length + 1];
    strncpy((char*)m_minor_cone_half_angle_distribution_param1.name,
            minor_cone_half_angle_distribution_param1_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param1_variable_name_length);
  }

  m_minor_cone_half_angle_distribution_param2.value = nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_constant;
  m_minor_cone_half_angle_distribution_param2.name = nullptr;
  if(nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_variable_name_length !=0 ){
    m_minor_cone_half_angle_distribution_param2.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_variable_name_length + 1];
    strncpy((char*)m_minor_cone_half_angle_distribution_param2.name,
            minor_cone_half_angle_distribution_param2_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.minor_cone_half_angle_distribution_param2_variable_name_length);
  }


  m_major_half_angle_limit.value = nozzle_record.specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_constant;
  m_major_half_angle_limit.name = nullptr;
  if (nozzle_record.specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length != 0) {
    m_major_half_angle_limit.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length + 1];
    strncpy((char*)m_major_half_angle_limit.name,
            major_outer_half_angle_limit_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.major_outer_half_angle_limit_variable_name_length);
  }

  m_minor_half_angle_limit.value = nozzle_record.specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_constant;
  m_minor_half_angle_limit.name = nullptr;
  if (nozzle_record.specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length != 0) {
    m_minor_half_angle_limit.name = cnew char[nozzle_record.specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length + 1];
    strncpy((char*)m_minor_half_angle_limit.name,
            minor_outer_half_angle_limit_variable_name.c_str(),
            nozzle_record.specialized_parameters.elliptical_cone_params.minor_outer_half_angle_limit_variable_name_length);
  }



}


sTIRE_ARC_STATION_DATA::sTIRE_ARC_STATION_DATA(sLGI_NOZZLE_CONFIGURATION_REC &nozzle_record,
                                               std::string &diameter_distribution_param1_variable_name,
                                               std::string &diameter_distribution_param2_variable_name,
                                               PARTICLE_MATERIAL material,
                                               BOOLEAN is_liquid_not_solid,
                                               std::string &velocity_magnitude_distribution_param1_variable_name,
                                               std::string &velocity_magnitude_distribution_param2_variable_name,
                                               std::string &tire_arc_position_variable_name,
                                               std::string &emission_offset_angle_variable_name,
                                               std::string &cone_half_angle_variable_name,
                                               std::string &stretch_factor_variable_name) :
  sKNOWN_VELOCITY_AND_DIAMETER_SPRAY_DATA_BASE(nozzle_record,
                                               diameter_distribution_param1_variable_name,
                                               diameter_distribution_param2_variable_name,
                                               material,
                                               is_liquid_not_solid,
                                               velocity_magnitude_distribution_param1_variable_name,
                                               velocity_magnitude_distribution_param2_variable_name)
{
  m_tire_arc_position.value = nozzle_record.specialized_parameters.tire_params.tire_arc_position_constant;
  m_tire_arc_position.name = nullptr;
  if(nozzle_record.specialized_parameters.tire_params.tire_arc_position_variable_name_length != 0) {
    m_tire_arc_position.name = cnew char[nozzle_record.specialized_parameters.tire_params.tire_arc_position_variable_name_length + 1];
    strncpy((char*)m_tire_arc_position.name,
            tire_arc_position_variable_name.c_str(),
            nozzle_record.specialized_parameters.tire_params.tire_arc_position_variable_name_length );
  }

  m_offset_angle.value = nozzle_record.specialized_parameters.tire_params.emission_offset_angle_constant;
  m_offset_angle.name = nullptr;
  if(nozzle_record.specialized_parameters.tire_params.emission_offset_angle_variable_name_length != 0) {
    m_offset_angle.name = cnew char[nozzle_record.specialized_parameters.tire_params.emission_offset_angle_variable_name_length + 1];
    strncpy((char*)m_offset_angle.name,
            emission_offset_angle_variable_name.c_str(),
            nozzle_record.specialized_parameters.tire_params.emission_offset_angle_variable_name_length );
  }

  m_offset_angle_distribution = lgi_to_pdfs_distribution(nozzle_record.specialized_parameters.tire_params.offset_angle_distribution);

  m_cone_half_angle.value = nozzle_record.specialized_parameters.tire_params.cone_half_angle_constant;
  m_cone_half_angle.name = nullptr;
  if(nozzle_record.specialized_parameters.tire_params.cone_half_angle_variable_name_length != 0) {
    m_cone_half_angle.name = cnew char[nozzle_record.specialized_parameters.tire_params.cone_half_angle_variable_name_length + 1];
    strncpy((char*)m_cone_half_angle.name,
            cone_half_angle_variable_name.c_str(),
            nozzle_record.specialized_parameters.tire_params.cone_half_angle_variable_name_length );
  }

  m_stretch_factor.value = nozzle_record.specialized_parameters.tire_params.stretch_factor_constant;
  m_stretch_factor.name = nullptr;
  if(nozzle_record.specialized_parameters.tire_params.stretch_factor_variable_name_length != 0) {
    m_stretch_factor.name = cnew char[nozzle_record.specialized_parameters.tire_params.stretch_factor_variable_name_length + 1];
    strncpy((char*)m_stretch_factor.name,
            stretch_factor_variable_name.c_str(),
            nozzle_record.specialized_parameters.tire_params.stretch_factor_variable_name_length );
  }
}

//SIMUTILS::ePDF_TYPES convert_pdfs_pdf_type_to_simutils_pdf_type(sPDF::ePDF_TYPE pdfs_pdf_type);
sTIRE_EMITTER_CONFIGURATION::sTIRE_EMITTER_CONFIGURATION(sLGI_PARTICLE_EMITTER_CONFIGURATION_REC &lgi_record,
                                                         std::vector<sLGI_NOZZLE_CONFIGURATION_REC_READER> &nozzle_records,
                                                         std::string &name,
                                                         PARTICLE_MATERIAL material,
                                                         std::string &emission_rate_variable_name,
                                                         std::string &cdi_angular_emission_distribution_parameter_variable_name,
                                                         std::string &cdi_angular_emission_distribution_ratio_variable_name,
                                                         std::vector<std::string> &diameter_distribution_param1_variable_names,
                                                         std::vector<std::string> &diameter_distribution_param2_variable_names,
                                                         std::vector<std::string> &velocity_magnitude_distribution_param1_variable_names,
                                                         std::vector<std::string> &velocity_magnitude_distribution_param2_variable_names,
                                                         std::vector<std::string> &tire_arc_position_variable_names,
                                                         std::vector<std::string> &emission_offset_angle_variable_names,
                                                         std::vector<std::string> &cone_half_angle_variable_names,
                                                         std::vector<std::string> &stretch_factor_variable_names ) :
  sPARTICLE_EMITTER_CONFIGURATION_BASE(lgi_record,
                                       name,
                                       emission_rate_variable_name)
{

  m_material = material;
  m_material_index = lgi_record.material_index;


  m_angular_emission_distribution_type = lgi_to_pdfs_distribution(lgi_record.specialized_parameters.tire_params.angular_emission_distribution_type);
  if(m_angular_emission_distribution_type == sPDF::GAUSSIAN_DISTRIBUTION)
    m_angular_emission_distribution_type = sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION;

  m_cdi_angular_emission_distribution_parameter.value = lgi_record.specialized_parameters.tire_params.angular_emission_distribution_parameter_constant;
  m_cdi_angular_emission_distribution_parameter.name = nullptr;
  if(lgi_record.specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length != 0) {
    m_cdi_angular_emission_distribution_parameter.name = cnew char[lgi_record.specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length + 1];
    strncpy((char*)m_cdi_angular_emission_distribution_parameter.name,
            cdi_angular_emission_distribution_parameter_variable_name.c_str(),
            lgi_record.specialized_parameters.tire_params.angular_emission_distribution_parameter_variable_name_length);
  }

  m_cdi_angular_emission_distribution_ratio.value = lgi_record.specialized_parameters.tire_params.angular_emission_distribution_ratio_constant;
  m_cdi_angular_emission_distribution_ratio.name = nullptr;
  if(lgi_record.specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length != 0) {
    m_cdi_angular_emission_distribution_ratio.name = cnew char[lgi_record.specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length + 1];
    strncpy((char*)m_cdi_angular_emission_distribution_ratio.name,
            cdi_angular_emission_distribution_ratio_variable_name.c_str(),
            lgi_record.specialized_parameters.tire_params.angular_emission_distribution_ratio_variable_name_length);
  }

  m_configuration_interpolation_nodes.reserve(lgi_record.num_nozzle_configs);
  ccDOTIMES(interpolation_node_index, lgi_record.num_nozzle_configs) {
    m_configuration_interpolation_nodes.push_back(sTIRE_ARC_STATION_DATA(nozzle_records[interpolation_node_index],
                                                                         diameter_distribution_param1_variable_names[interpolation_node_index],
                                                                         diameter_distribution_param2_variable_names[interpolation_node_index],
                                                                         material,
                                                                         material->is_liquid(),
                                                                         velocity_magnitude_distribution_param1_variable_names[interpolation_node_index],
                                                                         velocity_magnitude_distribution_param2_variable_names[interpolation_node_index],
                                                                         tire_arc_position_variable_names[interpolation_node_index],
                                                                         emission_offset_angle_variable_names[interpolation_node_index],
                                                                         cone_half_angle_variable_names[interpolation_node_index],
                                                                         stretch_factor_variable_names[interpolation_node_index]));
  }

  //Sort the interpolation nodes in order of increasing tire_arc_position.
  std::sort(m_configuration_interpolation_nodes.begin(),
            m_configuration_interpolation_nodes.end(),
            sTIRE_ARC_STATION_DATA::sPREDICATE());


  //Save the start and end angles
  asINT32 num_nodes = m_configuration_interpolation_nodes.size();
  m_start = m_configuration_interpolation_nodes[0].m_tire_arc_position.value;
  m_end   = m_configuration_interpolation_nodes[num_nodes - 1].m_tire_arc_position.value;


  //If there is only one station defined, switch the angular distribution pdf type to a constant
  if(num_nodes == 1) {
    m_angular_emission_distribution_type = sPDF::CONSTANT_NUMBER;
  }

  //Use the code in SIMUTILS to compute the expected volume of particles released from the tire emitters using this configuration.
  //Also this code maps the spatial distribution parameters provided by CDI to the values used by a PDFS' RNG.


  std::vector<double> diameter_distribution_parameters_1(num_nodes, 0.0);
  std::vector<double> diameter_distribution_parameters_2(num_nodes, 0.0);
  std::vector<double> station_locations(num_nodes,0.0);
  std::vector<double> minimum_diameters(num_nodes,0.0);
  std::vector<double> maximum_diameters(num_nodes,0.0);
  ccDOTIMES(station_index, num_nodes) {
    diameter_distribution_parameters_1[station_index] = m_configuration_interpolation_nodes[station_index].diameter_distribution_param1();
    diameter_distribution_parameters_2[station_index] = m_configuration_interpolation_nodes[station_index].diameter_distribution_param2();
    station_locations[station_index] = m_configuration_interpolation_nodes[station_index].m_tire_arc_position.value;
    minimum_diameters[station_index] = m_configuration_interpolation_nodes[station_index].diameter_min();
    maximum_diameters[station_index] = m_configuration_interpolation_nodes[station_index].diameter_max();
  }

  //The following object numerically integrates various quantities around the wheel arc upon construction.
  sSIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER simutils_helper((SIMUTILS::eEMISSION_RATE_TYPES)this->emission_rate_type(),
                                                             this->emission_rate(),
                                                             (SIMUTILS::ePDF_TYPES)m_angular_emission_distribution_type,
                                                             m_cdi_angular_emission_distribution_parameter.value,
                                                             m_cdi_angular_emission_distribution_ratio.value,
                                                             station_locations,
                                                             (SIMUTILS::ePDF_TYPES)m_configuration_interpolation_nodes[0].diameter_distribution(),
                                                             diameter_distribution_parameters_1,
                                                             diameter_distribution_parameters_2,
                                                             minimum_diameters,
                                                             maximum_diameters,
                                                             (SIMUTILS::ePDF_TYPES)this->material()->density_distribution(),
                                                             this->material()->density_distribution_param1(),
                                                             this->material()->density_distribution_param2());

  m_average_particle_diameter = simutils_helper.expected_particle_diameter();
  m_average_particle_diameter_stddev = simutils_helper.expected_particle_diameter_stddev();
  m_average_particle_volume = simutils_helper.expected_particle_volume();

  //Also use SIMUTILS to remap the spatial emission distribution parameters provided by CDI to ones accepted by PDFS RNGs.
  simutils_helper.map_cdi_spatial_distribution_parameters_to_pdfs_distribution_parameters(m_pdfs_angular_emission_dist_param1,
                                                                                          m_pdfs_angular_emission_dist_param2,
                                                                                          m_pdfs_angular_emission_dist_param3,
                                                                                          convert_pdfs_pdf_type_to_simutils_pdf_type(m_angular_emission_distribution_type),
                                                                                          m_cdi_angular_emission_distribution_parameter.value,
                                                                                          m_cdi_angular_emission_distribution_ratio.value);

}


SIMUTILS::ePDF_TYPES convert_pdfs_pdf_type_to_simutils_pdf_type(sPDF::ePDF_TYPE pdfs_pdf_type) {
  SIMUTILS::ePDF_TYPES pdf_type = SIMUTILS::NUM_DISTRIBUTION_TYPES;
  switch(pdfs_pdf_type) {
  case sPDF::UNIFORM_DISTRIBUTION:
    pdf_type = SIMUTILS::UNIFORM_DISTRIBUTION;
    break;
  case sPDF::GAUSSIAN_DISTRIBUTION:
    pdf_type = SIMUTILS::GAUSSIAN_DISTRIBUTION;
    break;
  case sPDF::TRUNCATED_GAUSSIAN_DISTRIBUTION:
    pdf_type = SIMUTILS::TRUNCATED_GAUSSIAN_DISTRIBUTION;
    break;
  case sPDF::GAMMA_DISTRIBUTION:
    pdf_type = SIMUTILS::GAMMA_DISTRIBUTION;
    break;
  case sPDF::CONSTANT_NUMBER:
    pdf_type = SIMUTILS::CONSTANT_NUMBER;
    break;
  case sPDF::ROSIN_RAMMLER_DISTRIBUTION:
    pdf_type = SIMUTILS::ROSIN_RAMMLER_DISTRIBUTION;
    break;
  case sPDF::ROSIN_RAMMLER_VOLUME_FRACTION_DISTRIBUTION:
    pdf_type = SIMUTILS::ROSIN_RAMMLER_VOLUME_FRACTION_DISTRIBUTION;
    break;
  case sPDF::LOG_NORMAL_DISTRIBUTION:
    pdf_type = SIMUTILS::LOG_NORMAL_DISTRIBUTION;
    break;
  case sPDF::LINEAR_DISTRIBUTION:
    pdf_type = SIMUTILS::LINEAR_DISTRIBUTION;
    break;
  case sPDF::EXPONENTIAL_DISTRIBUTION:
    pdf_type = SIMUTILS::EXPONENTIAL_DISTRIBUTION;
    break;
  case sPDF::HALF_COSINE_DISTRIBUTION:
    pdf_type = SIMUTILS::HALF_COSINE_DISTRIBUTION;
    break;
  case sPDF::POISSON_DISTRIBUTION:
    pdf_type = SIMUTILS::POISSON_DISTRIBUTION;
    break;
  default:
    pdf_type = SIMUTILS::NUM_DISTRIBUTION_TYPES;
  }
  return pdf_type;
}
