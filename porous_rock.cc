/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "sim.h"
#include "porous_rock.h"

#include <algorithm>

#define USE_BINARY_SEARCH 1

cPOROUS_ROCK cPOROUS_ROCK::read_from_cp(LGI_STREAM stream)
{
  cPOROUS_ROCK rock;
  sLGI_POROUS_ROCK lgi_rock;
  std::vector<POROUS_ROCK_VAR> table_values;

  lgi_read_next_head(stream, lgi_rock);

  if ( lgi_rock.tag.id != LGI_POROUS_ROCK_TAG ) {
    msg_internal_error("Error reading rock");
  }

  rock.m_index = lgi_rock.rock_id;
  /*ccDOTIMES(i,3) {
    rock.m_principal_dir[i] = lgi_rock.principal_dir[i];
    }*/

  rock.m_num_K0_rows = lgi_rock.num_K0_rows;
  rock.m_num_K0_cols = lgi_rock.num_K0_cols == 0 ? 0 : lgi_rock.num_K0_cols-1; // separate out the first col

  if ( rock.m_num_K0_cols && rock.m_num_K0_cols != NUM_K0_COLS ) {
    msg_internal_error("Incorrect number of K0 columns!");
  }

  rock.m_num_Pc_Kr_rows = lgi_rock.num_Pc_Kr_rows;
  //rock.m_num_Pc_Kr_cols = lgi_rock.num_Pc_Kr_cols == 0 ? 0 : lgi_rock.num_Pc_Kr_cols-1; // separate out the first col
  rock.m_num_Pc_Kr_cols = lgi_rock.num_Pc_Kr_cols == 0 ? 0 : lgi_rock.num_Pc_Kr_cols; // not separate out the first col in 
                                                                                      // preparation for P-2-Sw look-up

  if ( rock.m_num_Pc_Kr_cols != 0 && rock.m_num_Pc_Kr_cols != NUM_Pc_Kr_COLS ) {
    msg_internal_error("Incorrect number of Pc_Kr_columns!");
  }

  if (lgi_rock.num_info_rows !=0 && lgi_rock.num_info_rows != NUM_INFO_ROWS)  //may not necessary because it is checked in CP
    msg_internal_error("Incorrect number of info_rows!");

  if (lgi_rock.num_info_cols !=0 && lgi_rock.num_info_cols != NUM_INFO_COLS)  //may not necessary because it is checked in CP
    msg_internal_error("Incorrect number of info_columns!");

  rock.m_K0_porosity = new POROUS_ROCK_VAR [ rock.m_num_K0_rows ];
  rock.m_K0_values = new POROUS_ROCK_VAR [ rock.m_num_K0_rows * rock.m_num_K0_cols ];

  rock.m_Pc_Kr_saturation = new POROUS_ROCK_VAR [ rock.m_num_Pc_Kr_rows ];
  rock.m_Pc_Kr_pressure = new POROUS_ROCK_VAR [ rock.m_num_Pc_Kr_rows ];
  rock.m_Pc_Kr_values = new POROUS_ROCK_VAR [ rock.m_num_Pc_Kr_rows * rock.m_num_Pc_Kr_cols ];

  table_values.resize( lgi_rock.num_K0_rows * lgi_rock.num_K0_cols );
  lgi_read(stream, table_values.data(), table_values.size()*sizeof(POROUS_ROCK_VAR));

  uINT32 idx = 0;
  uINT32 val_idx = 0;
  ccDOTIMES(row, lgi_rock.num_K0_rows) {
    ccDOTIMES(col, lgi_rock.num_K0_cols) {
      if ( col == 0 ) {
        rock.m_K0_porosity[row] = table_values[idx];
      }
      else {
        rock.m_K0_values[val_idx] = table_values[idx];
        val_idx++;
      }
      idx++;
    }
  }

  table_values.resize( lgi_rock.num_Pc_Kr_rows * lgi_rock.num_Pc_Kr_cols );
  lgi_read(stream, table_values.data(), table_values.size()*sizeof(POROUS_ROCK_VAR));

  idx = 0;
  val_idx = 0;
  ccDOTIMES(row, lgi_rock.num_Pc_Kr_rows) {
    ccDOTIMES(col, lgi_rock.num_Pc_Kr_cols) {
      if ( col == 0 ) {
        rock.m_Pc_Kr_saturation[row] = table_values[idx];
      }
      else if ( col == 1 ) {
        rock.m_Pc_Kr_pressure[row] = table_values[idx];
      }
      rock.m_Pc_Kr_values[val_idx] = table_values[ idx ];
      val_idx++;
      idx++;
    }
  }

  if (lgi_rock.num_info_rows > 0) {
    table_values.resize( lgi_rock.num_info_rows * lgi_rock.num_info_cols );
    if ((table_values.size()*sizeof(POROUS_ROCK_VAR)) != sizeof(cPOROUS_ROCK::sInfo))
      msg_internal_error("Incorrect number of info values!");
    lgi_read(stream, &(rock.sInfo), table_values.size()*sizeof(POROUS_ROCK_VAR));
  } else {
    rock.sInfo.m_porosity_Pc = g_mp_pm_pc_smpld_prsty;
    rock.sInfo.m_contact_angle_Pc = g_mp_pm_contactangle_inputpc;
    rock.sInfo.m_surface_tension_Pc = g_mp_pm_surftens_dyncm_inputpc;
    rock.sInfo.m_principal_dir[0] = 1.0;
    rock.sInfo.m_principal_dir[1] = 1.0;
    rock.sInfo.m_principal_dir[2] = 1.0;
  }

  //do_debug
  //printf("Info: rock=%d, phi_pc=%e, theta_pc=%e, sigma=%e, dir(%e,%e,%e)\n", rock.m_index,rock.sInfo.m_porosity_Pc,rock.sInfo.m_contact_angle_Pc,rock.sInfo.m_surface_tension_Pc,rock.sInfo.m_principal_dir[0],rock.sInfo.m_principal_dir[1],rock.sInfo.m_principal_dir[2]);

  //get m_pm_kr_resw and m_pm_kr_resa
  sdFLOAT kr = 0.0F;
  sdFLOAT Sw = 0.0F;
  asINT32 counter=0;
  sdFLOAT table_delta=0.001F;
  sPc_Kr table;
    
  sdFLOAT pm_kr_resw = g_mp_pm_kr_resw; 
  while(kr<=0.0F&&counter<2000){//finding the last Sw that gives zero krw
    Sw += table_delta; 
    table = rock.get_Pc_Kr(Sw); 
    kr = table.Krw_imb[0];
    pm_kr_resw = Sw;
    counter += 1;
  }
  rock.m_pm_kr_resw = pm_kr_resw;

  Sw=0.0F; kr=1.0F; counter=0;
  sdFLOAT pm_kr_resa = g_mp_pm_kr_resa;
  while(kr>0.0F&&counter<2000){//finding the last Sw that gives non-zero kro
    pm_kr_resa = 1.0F-Sw;
    Sw += table_delta; 
    table = rock.get_Pc_Kr(Sw); 
    kr = table.Kro_imb[0];
    counter += 1;
  }
  rock.m_pm_kr_resa = pm_kr_resa;

  //printf("rock_type=%d: p:%e, krw:%e, kro:%e, Sww:%e, Swo:%e \n", rock.m_index, table.Pc_imb[0],table.Krw_imb[0],table.Kro_imb[0],pm_kr_resw,pm_kr_resa);

  return rock;
}

void read_porous_rock_tables(LGI_STREAM stream)
{
  LGI_TAG tag = lgi_peek_tag(stream);
  if (tag.id != LGI_POROUS_ROCK_COUNT_TAG) {
    return;
  }

  sLGI_POROUS_ROCK_COUNT header;
  lgi_read_next_head(stream, header);

  sim.num_rock_types = header.num_rock_types;
  sim.rock_types = new cPOROUS_ROCK [ header.num_rock_types ];
  ccDOTIMES(i, header.num_rock_types) {
    cPOROUS_ROCK rock = cPOROUS_ROCK::read_from_cp(stream);
    sINT32 index = rock.index();
    sim.rock_types[index] = rock;  //sim.rock_types are sorted by m_index
  }
}

template<typename T, size_t NUM_COLS, size_t NUM_BYTES = sizeof(T)>
  static T lerp(POROUS_ROCK_VAR x, const POROUS_ROCK_VAR * xarray, const POROUS_ROCK_VAR * yarray, sINT32 num_rows)
  {
    static_assert( sizeof(POROUS_ROCK_VAR)*NUM_COLS == NUM_BYTES , "Struct size does not match number of columns");
    POROUS_ROCK_VAR const * const xarray_end = xarray + num_rows;
    T y;

#define yrow(row_idx) &yarray[ (row_idx)*NUM_COLS ]

    const POROUS_ROCK_VAR * ptr;
    if ( USE_BINARY_SEARCH ) {
      ptr = std::upper_bound(xarray, xarray_end, x);
    }
    else {
      ptr = xarray_end;
      ccDOTIMES(i, num_rows) {
        if ( xarray[i] > x ) {
          ptr = &xarray[i];
          break;
        }
      }
    }

    // We do not extrapolate, we just use the end rows
    if (ptr == xarray_end) {
      std::memcpy(&y, yrow(num_rows-1), NUM_BYTES);
    }
    else if (ptr == xarray) {
      std::memcpy(&y, yrow(0), NUM_BYTES);
    }
    else {
      POROUS_ROCK_VAR vars[NUM_COLS];
      size_t row_idx = ptr - xarray;
      POROUS_ROCK_VAR x0 = *(ptr-1);
      POROUS_ROCK_VAR x1 = *ptr;
      const POROUS_ROCK_VAR * row0 = yrow(row_idx-1);
      const POROUS_ROCK_VAR * row1 = yrow(row_idx);
      POROUS_ROCK_VAR c = (x - x0)/(x1 - x0);
      POROUS_ROCK_VAR cm = POROUS_ROCK_VAR(1.0) - c;
      ccDOTIMES(i, NUM_COLS) {
        vars[i] = row0[i]*cm + row1[i]*c;
      }
      std::memcpy(&y, vars, NUM_BYTES);
    }

    return y;
#undef yrow
  }

template<typename T, size_t NUM_COLS, size_t NUM_BYTES = sizeof(T)>
  static T lerp_descending(POROUS_ROCK_VAR x, const POROUS_ROCK_VAR * xarray, const POROUS_ROCK_VAR * yarray, sINT32 num_rows)
  {
    static_assert( sizeof(POROUS_ROCK_VAR)*NUM_COLS == NUM_BYTES , "Struct size does not match number of columns");
    POROUS_ROCK_VAR const * const xarray_end = xarray + num_rows;
    T y;

#define yrow(row_idx) &yarray[ (row_idx)*NUM_COLS ]

    const POROUS_ROCK_VAR * ptr;
    if ( USE_BINARY_SEARCH ) {
      ptr = std::lower_bound(xarray, xarray_end, x, std::greater<POROUS_ROCK_VAR>());
    }
    else {
      ptr = xarray_end;
      ccDOTIMES(i, num_rows) {
        if ( xarray[i] < x ) {
          ptr = &xarray[i];
          break;
        }
      }
    }

    // We do not extrapolate, we just use the end rows
    if (ptr == xarray_end) {
      std::memcpy(&y, yrow(num_rows-1), NUM_BYTES);
    }
    else if (ptr == xarray) {
      std::memcpy(&y, yrow(0), NUM_BYTES);
    }
    else {
      POROUS_ROCK_VAR vars[NUM_COLS];
      size_t row_idx = ptr - xarray;
      POROUS_ROCK_VAR x0 = *(ptr-1);
      POROUS_ROCK_VAR x1 = *ptr;
      const POROUS_ROCK_VAR * row0 = yrow(row_idx-1);
      const POROUS_ROCK_VAR * row1 = yrow(row_idx);
      POROUS_ROCK_VAR c = (x - x0)/(x1 - x0);
      POROUS_ROCK_VAR cm = POROUS_ROCK_VAR(1.0) - c;
      ccDOTIMES(i, NUM_COLS) {
        vars[i] = row0[i]*cm + row1[i]*c;
      }
      std::memcpy(&y, vars, NUM_BYTES);
    }

    return y;
#undef yrow
  }

cPOROUS_ROCK::sK0 cPOROUS_ROCK::get_K0(POROUS_ROCK_VAR porosity) const
{
  return lerp<sK0,NUM_K0_COLS>(porosity, m_K0_porosity, m_K0_values, m_num_K0_rows);
}

cPOROUS_ROCK::sPc_Kr cPOROUS_ROCK::get_Pc_Kr(POROUS_ROCK_VAR saturation) const
{
  return lerp<sPc_Kr,NUM_Pc_Kr_COLS>(saturation, m_Pc_Kr_saturation, m_Pc_Kr_values, m_num_Pc_Kr_rows);
}

cPOROUS_ROCK::sPc_Kr cPOROUS_ROCK::get_Sw(POROUS_ROCK_VAR pc) const
{
  return lerp_descending<sPc_Kr,NUM_Pc_Kr_COLS>(pc, m_Pc_Kr_pressure, m_Pc_Kr_values, m_num_Pc_Kr_rows);
}


