/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2022, 1993-2021 Dassault Systemes Simulia Corp.         ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Simulia Corp.                                                         ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef ADVECT_INTERACTIONS_H
#define ADVECT_INTERACTIONS_H
#include "common_sp.h"
#include "lattice.h"
#include "vectorization_support.h"
#include "gpu_shobs.hcu"
#include "gpu_globals.hcu"
#include <cstdint>

namespace GPU {
namespace GATHER_ADVECT {  

using STATE_PTR = VOXEL_STATE (*) [N_VOXELS_64];
constexpr size_t N_UBLK_ADVECT_INTERACTIONS_2D = 32;
constexpr size_t N_UBLK_ADVECT_INTERACTIONS = 72;

extern __constant__ uint8_t g_advect_neighbor_ublk_indices[N_UBLK_ADVECT_INTERACTIONS];
extern __constant__ uint8_t g_thread_split_factor_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D];
extern __constant__ uint8_t g_advect_src_voxels_2D[N_UBLK_ADVECT_INTERACTIONS_2D];
extern __constant__ uint8_t g_advect_dst_voxels_2D[N_UBLK_ADVECT_INTERACTIONS_2D];
extern __constant__ uint8_t g_advect_velocity_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D];

extern __constant__ uint8_t g_thread_split_factor_indices[N_UBLK_ADVECT_INTERACTIONS];
extern __constant__ uint8_t g_advect_src_voxels[N_UBLK_ADVECT_INTERACTIONS];
extern __constant__ uint8_t g_advect_dst_voxels[N_UBLK_ADVECT_INTERACTIONS];
extern __constant__ uint8_t g_advect_velocity_indices[N_UBLK_ADVECT_INTERACTIONS];
extern __constant__ uint8_t g_advect_neighbor_ublk_indices_2D[N_UBLK_ADVECT_INTERACTIONS_2D];

extern __constant__ int8_t g_advect_neighbor_offsets[10][3];
extern __constant__ int8_t g_state_velocities[N_STATES][3];


template<BOOLEAN IS_2D>
struct INTERACTION_TRAITS {
  static __DEVICE__ uint8_t neighbor_ublk_index(int interaction) {
    return IS_2D? g_advect_neighbor_ublk_indices_2D[interaction] :
                  g_advect_neighbor_ublk_indices[interaction];
  }

  static __DEVICE__ uint8_t velocity_index(int interaction) {
    return IS_2D? g_advect_velocity_indices_2D[interaction] :
                  g_advect_velocity_indices[interaction];
  }

  static __DEVICE__ uint8_t split_factor_index(int interaction) {
    return IS_2D? g_thread_split_factor_indices_2D[interaction] :
                  g_thread_split_factor_indices[interaction];
  }  

  static __DEVICE__ uint8_t src_voxel(int interaction) {
    return IS_2D? g_advect_src_voxels_2D[interaction] :
                  g_advect_src_voxels[interaction];
  }

  static __DEVICE__ uint8_t dst_voxel(int interaction) {
    return IS_2D? g_advect_dst_voxels_2D[interaction] :
                  g_advect_dst_voxels[interaction];
  }

  static __DEVICE__ std::tuple<int, int> get_thread_adv_interaction_id_and_child_ublk() {
    constexpr int n_interactions = static_cast<int>(n_advect_interactions());
    int interaction = threadIdx.x % n_interactions;
    int child_ublk = threadIdx.x / n_interactions;
    return {interaction, child_ublk};
  }

  constexpr static __HOST__DEVICE__ size_t n_advect_interactions() {
    return IS_2D? N_UBLK_ADVECT_INTERACTIONS_2D : N_UBLK_ADVECT_INTERACTIONS;
  }
};

__HOST__ INLINE size_t n_advect_interactions_threads_per_block() {
  return N_UBLKS_PER_MBLK *
    (sim.is_2d()? N_UBLK_ADVECT_INTERACTIONS_2D : N_UBLK_ADVECT_INTERACTIONS);
}

template<BOOLEAN IS_2D, BOOLEAN IS_PARITY>
__DEVICE__ INLINE VOID set_offsets_for_interaction(STP_STATE_VEL (&offsets) [3],
                                                   int interaction) {

  using TRAITS = INTERACTION_TRAITS<IS_2D>;
  const uint8_t neighbor_index = TRAITS::neighbor_ublk_index(interaction);

  ccDOTIMES(i, 3) {
    offsets[i] = g_advect_neighbor_offsets[neighbor_index][i];
  }

  if constexpr (IS_PARITY) { offsets[0] *= -1; offsets[1] *= -1; offsets[2] *= -1; }
}

}//namespace GATHER_ADVECT
} //namespace GPU
#endif
