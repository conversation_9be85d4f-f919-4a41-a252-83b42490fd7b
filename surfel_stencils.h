#ifndef _SIMENG_SURFEL_STENCILS_H
#define _SIMENG_SURFEL_STENCILS_H

#include "common_sp.h"
#include <vector>

template<typename SFL_TYPE_TAG> struct tSURFACE_SHOB;
using sSURFACE_SHOB = tSURFACE_SHOB<SFL_SDFLOAT_TYPE_TAG>;

struct sSAMPLING_SURFEL;

typedef struct sSURFEL_HALF_EDGE {
  sSURFACE_SHOB* neighbor_surfel;
  sSURFEL_HALF_EDGE *neighbor_edge;
  dFLOAT edge_length; // could be computed on the fly from the vertices
  dFLOAT edge_dir[3];
  sdFLOAT normal[3]; // CONDUCTION-CHECK: Can compute this as surfel->normal cross edge_dir, is it worth storing?
  sdFLOAT normal_dist; // CONDUCTION-CHECK: This can be computed on the fly, is it worth storing?

  sSURFEL *neighbor_regular_surfel()  { return (sSURFEL*)neighbor_surfel; }
  sSAMPLING_SURFEL *neighbor_sampling_surfel() { return (sSAMPLING_SURFEL*)neighbor_surfel; }
  VOID get_centroid(dFLOAT (&edge_centroid)[3], sSURFEL* surfel, asINT32 edge_num = -1) const;
  VOID get_tail_and_head_vertex(SIM_VERTEX &tail_vertex, SIM_VERTEX &head_vertex,
      sSURFEL* surfel, sINT32 edge_num = -1) const;
  asINT32 get_edge_num(sSURFEL* surfel) const;
} *SURFEL_HALF_EDGE;

#define DO_SURFEL_EDGES(surfel, edge, edge_num) \
  const asINT32 _first_half_edge_index = surfel->stencil()->first_half_edge_index(); \
  asINT32 edge_num; SURFEL_HALF_EDGE edge; \
  for (edge_num = 0, \
      edge = surfel_half_edge(_first_half_edge_index + edge_num); \
      edge_num < surfel->stencil()->n_edges(); \
      edge_num++, \
      edge = surfel_half_edge(_first_half_edge_index + edge_num))

/*******************************************************************************
 * sSURFEL_HE_VERTEX_INFO
 * ----------------------
 ******************************************************************************/
typedef struct sSURFEL_HE_VERTEX_INFO {
private:
  sSURFACE_SHOB* _surfel;
  asINT32 _first_vertex_index;
  asINT32 _edge_num;
  DGF_VERTEX_INDEX _head_vertex_index;
  DGF_VERTEX_INDEX _tail_vertex_index;

public:
  sSURFEL_HE_VERTEX_INFO (sSURFACE_SHOB* s);

  asINT32 edge_num_from_vertex_num (const asINT32 vertex_num);

  VOID update_from_edge_num(const asINT32 edge_num);

  VOID update_from_vertex_num(const asINT32 vertex_num) {
    update_from_edge_num(edge_num_from_vertex_num(vertex_num));
  }

  SURFEL_HALF_EDGE get_ptr () const;
  asINT32 head_vertex_index() const { return _head_vertex_index; }
  asINT32 tail_vertex_index() const { return _tail_vertex_index; }
  SIM_VERTEX head_vertex () const;
  SIM_VERTEX tail_vertex () const;
} *SURFEL_HE_VERTEX_INFO;

/*******************************************************************************
 * sSTENCIL_SURFEL
 * ---------------
 * Rather than using a vector of surfels directly, we define a general
 * struct (which here houses only a surfel pointer) who vector is defined
 * subsequently. This allows particular solvers to derive from this struct if
 * they like to store additional information for every surfel in the stencil.
 ******************************************************************************/
struct sSTENCIL_SURFEL {
private:
  sSURFEL* s;
public:
  sSTENCIL_SURFEL() {}
  sSTENCIL_SURFEL (sSURFEL* surfel) : s(surfel) {}
  sSURFEL* get_surfel() const { return s; }
  bool operator == (const sSTENCIL_SURFEL &another_stencil_surfel) const {
    if (another_stencil_surfel.get_surfel() == s)
      return true;
    else
      return false;
  }
};

typedef std::vector<sSTENCIL_SURFEL> sSURFEL_STENCIL_VECTOR;
typedef sSURFEL_STENCIL_VECTOR* SURFEL_STENCIL_VECTOR;

/*******************************************************************************
 * sSTENCIL_INFO
 * -------------
 * 
 ******************************************************************************/
typedef struct sSTENCIL_INFO {
  sINT16 n_vertices;
  DGF_VERTEX_INDEX first_vertex_index; // index in g_surfel_vertex_indices

  asINT32 n_edges() const { return n_vertices; }
  DGF_VERTEX_INDEX first_half_edge_index() const { return first_vertex_index; }
} *STENCIL_INFO;

/*******************************************************************************
 * @brief   General structure with information required by all solvers
 *
 * @details Different solvers can have different information requirements
 *          from stencil surfels. As the stencil surfel lists are being
 *          constructed, we will temporarily save information required
 *          across solvers. Individual solvers can later keep relevant
 *          data and discard the rest
 ******************************************************************************/
typedef struct sSURFEL_STENCIL_INIT_INFO {
  //SS-CHECK for FILM_STENCIL_INIT_INFO
  asINT32 sharp_edge_count_to_center;
  sSURFEL* surfel;
  sSURFEL_HALF_EDGE* shared_half_edge;

  sSURFEL_STENCIL_INIT_INFO () {}
  sSURFEL_STENCIL_INIT_INFO (sSURFEL *s) : surfel(s) {}
  sSURFEL_STENCIL_INIT_INFO (sSURFEL *s, asINT32 init_edge_count) 
      : surfel(s), sharp_edge_count_to_center(init_edge_count), shared_half_edge(NULL) {}
  sSURFEL_STENCIL_INIT_INFO (sSURFEL *s, sSURFEL_HALF_EDGE *e, asINT32 neighbor_number) 
      : surfel(s), sharp_edge_count_to_center(neighbor_number),
        shared_half_edge((s == e->neighbor_regular_surfel()) ? e->neighbor_edge : e) {}

} *SURFEL_STENCIL_INIT_INFO;

typedef struct sSURFEL_STENCIL_INIT_INFO_VECTOR {
  std::vector<sSURFEL_STENCIL_INIT_INFO> v;
  std::unordered_map<asINT32, asINT32> last_added_tags_map;

  // CONDUCTION-CHECK: Make v private, and define public functions such as size(), resize() etc.
  // to access individual elements?
  sSURFEL* home_surfel() const { return v[0].surfel; }
  VOID reset_last_added_tags();
  VOID tag_stencil_addition(sSURFEL* surfel);
  VOID add_surfel(sSURFEL* surfel, sSURFEL_HALF_EDGE* edge, asINT32 neighbor_order);
  VOID add_surfel(sSURFEL* surfel, asINT32 edges_to_center);

  template <typename STENCIL_TYPE, typename STENCIL_VECTOR>
  VOID finalize();

  BOOLEAN is_candidate_surfel_within_stencil_radius(sSURFEL* candidate_surfel) const;
  virtual BOOLEAN build_stencil_for_surfel(sSURFEL *surfel) = 0;
  virtual STP_DGEOM_VARIABLE max_radius() const = 0; // CONDUCTION-CHECK: a sdFLOAT is sufficient here?
  virtual asINT32 max_surfels_allowed() const = 0;
  //virtual const BOOLEAN is_neighbor_surfel_valid_candidate(sSURFEL* root_surfel, sSURFEL* neighbor_surfel) const = 0;
  virtual BOOLEAN should_surfel_be_added_to_stencil(sSURFEL* neighbor_surfel, const asINT32 neighbor_order) const = 0;
  virtual SURFEL_STENCIL_VECTOR get_home_surfel_stencil_vector_ptr() const = 0;
  BOOLEAN is_candidate_surfel_already_added(sSURFEL* candidate_surfel) const;
  // CONDUCTION-TODO: Convert should_wrap_around_at_interface_boundaries as a constexpr,
  // this might be possible using a TRAIT hierarchy
  virtual BOOLEAN should_wrap_around_at_interface_boundaries() const = 0;
  virtual BOOLEAN even_odd_filter(sSURFEL* surfel) const = 0;

  sSURFEL_STENCIL_INIT_INFO_VECTOR () {
    v.reserve(1<<16);
    reset_last_added_tags();
  }

  ~sSURFEL_STENCIL_INIT_INFO_VECTOR () {
    last_added_tags_map.clear();
  }
} *SURFEL_STENCIL_INIT_INFO_VECTOR;

VOID surfel_stencils_initialize();

typedef struct sFILM_SOLVER_STENCIL_INIT_INFO_VECTOR : sSURFEL_STENCIL_INIT_INFO_VECTOR {
  BOOLEAN build_stencil_for_surfel(sSURFEL *surfel);
  STP_DGEOM_VARIABLE max_radius() const;
  asINT32 max_surfels_allowed() const { return g_pfc_max_surfels_allowed_in_stencil; }
  //const BOOLEAN is_neighbor_surfel_valid_candidate(sSURFEL* root_surfel, sSURFEL* neighbor_surfel) const;
  BOOLEAN should_surfel_be_added_to_stencil(sSURFEL* neighbor_surfel, const asINT32 neighbor_order) const;
  SURFEL_STENCIL_VECTOR get_home_surfel_stencil_vector_ptr() const;
  BOOLEAN should_wrap_around_at_interface_boundaries() const { return TRUE; }
  BOOLEAN even_odd_filter(sSURFEL* surfel) const;
} *FILM_SOLVER_STENCIL_INIT_INFO_VECTOR;

#endif
