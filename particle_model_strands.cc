/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Strands that operate on farblks
 *
 * Vinit Gupta, Exa Corporation
 * Created Fri Sep 4, 2014
 *--------------------------------------------------------------------------*/

#include "shob_groups.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "strands.h"
#include "sleep.h"
#include "status.h"
#include "bsurfel_comm.h"
#include "ublk_process_control.h"
#include "trajectory_window.h"
#include "film_comm.h"
#include PHYSICS_H

#define DEBUG_ASM 0

#include "sp_timers.h"

#include "particle_sim_info.h"

VOID sFILM_ACCUMULATION_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();

  WITH_TIMER(SP_FILM_ACCUMULATION_TIMER) {
  if(sim.is_film_solver) {
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      // Clear measurement accumulators
      // Clear film accumulators
      
      particle_sim.clear_film(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.clear_film(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.clear_film(m_surfel_base_group_type, scale);
      //particle_sim.clear_ghost_film(scale);
    }
  }

  // Accumulate film
  if(sim.is_film_solver)
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      particle_sim.accumulate_film(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.accumulate_film(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.accumulate_film(m_surfel_base_group_type, scale);
    }
  }

  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      cassert(group->quantums().size() > 0);
      g_strand_mgr.m_send_queue->add_sp_entry(group, sim.init_solver_mask, FALSE);
    }
    g_strand_mgr.update_consumer_counts(scale);
  }
  g_strand_mgr.complete_timestep();
}

VOID sFILM_KINEMATICS_STRAND::run() {
  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  
  //msg_print("Executing film dynamics strand at T %ld", g_timescale.m_time);

  WITH_TIMER(SP_FILM_DYNAMICS_TIMER) {
    if(sim.is_film_solver)
      for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
        // Calculate surface tension forces ** NOT DONE YET
        // Distribute surface tension forces ** NOT DONE YET
        // Perform film dynamics (includes film_dynamics, and surface parcel_kinematics in that order)
        particle_sim.film_dynamics(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
        particle_sim.film_dynamics(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
        particle_sim.film_dynamics(m_surfel_base_group_type, scale);
      }
  }
  WITH_TIMER(SP_FILM_MEASUREMENTS_TIMER) {
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      // Take film measurement on various surfels.
      particle_sim.film_measurements(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.film_measurements(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
      particle_sim.film_measurements(m_surfel_base_group_type, scale);
    }
  }
  WITH_TIMER(SP_FILM_REENTRAINMENT_TIMER) {
    if(sim.is_film_solver) {
      if (particle_sim.is_timestep_before_accumulation()) {
        SCALE next_timestep_coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep + 1, FINEST_SCALE);
        for (asINT32 scale = FINEST_SCALE; scale >= next_timestep_coarsest_active_scale; --scale) {
          // Reentrain parcels.
          particle_sim.film_breakup(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
          particle_sim.film_breakup(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
          particle_sim.film_breakup(m_surfel_base_group_type, scale);
        }
      }
    }
  }
  WITH_TIMER(SP_SURFACE_PARTICLE_KINEMATICS_TIMER) {
    for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
      // Aggregate and move parcel on vaious surfelsl.
      if(sim.is_film_solver) {
        particle_sim.film_kinematics(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
        particle_sim.film_kinematics(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
        particle_sim.film_kinematics(m_surfel_base_group_type, scale);
      }
      particle_sim.ghost_sampling_surface_measurement(scale);
      g_strand_mgr.update_consumer_counts(scale);
    }
  }

  g_strand_mgr.complete_timestep();
}
