/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*/

#ifndef _SIMENG_WSURFEL_COMM_H
#define _SIMENG_WSURFEL_COMM_H

#include "common_sp.h"
#include "fset.h"
#include "comm_groups.h"
#include "conduction_data.h"
#include "strand_mgr.h"

//Conduction interface data exchanged by wsurfels and contact surfels, each one through different recv and send channels

// Send groups and fsets

class sCONDUCTION_INTERFACE_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>> {

 protected:
  //conduction interface data reside in surfels
  VMEM_VECTOR <SURFEL> m_surfels;

 public:
  SURFEL    *surfels()              { return &m_surfels[0]; }
  SURFEL    *surfel(asINT32 i)      { return &m_surfels[i]; }
  SHOB_ID   n_surfels()             { return m_surfels.size(); }

  VOID add_surfel(SURFEL surfel) {
    m_surfels.push_back(surfel);
  }
     
  VOID allocate_conduction_interface() {
    asINT32 send_count = n_surfels();
    SURFEL *s = surfels();
    m_sendsize = 0;
    ccDOTIMES(nth_surfel, send_count) {
      SURFEL surfel = *s;
      if(surfel->is_conduction_surface()) {
        m_sendsize += sCONDUCTION_INTERFACE_SOLID_DATA::send_size();
        if(surfel->is_conduction_shell()) {
          m_sendsize += surfel->shell_conduction_data()->wsurfel_send_size();
        } 
      } else if (surfel->is_conduction_shell()) {
        m_sendsize += sCONDUCTION_INTERFACE_OPEN_SHELL_DATA::send_size() + surfel->shell_conduction_data()->wsurfel_send_size();
      } else {
        m_sendsize += surfel->conduction_interface_fluid_data()->send_size();
      }
      s++;
    }
  }
     
  inline VOID fill_conduction_interface(sdFLOAT*& send_buffer) {
    SURFEL *s = surfels();
    asINT32 send_count = n_surfels();
    ccDOTIMES(nth_surfel, send_count) {
      SURFEL surfel = *s;
      if(surfel->is_conduction_surface()) {
        surfel->conduction_interface_solid_data()->fill_send_buffer(send_buffer);
        if(surfel->is_conduction_shell()) {
          surfel->shell_conduction_data()->fill_wsurfel_send_buffer(send_buffer);
        }
      } else if (surfel->is_conduction_shell()) {
        surfel->conduction_interface_open_shell_data()->fill_send_buffer(send_buffer);
        surfel->shell_conduction_data()->fill_wsurfel_send_buffer(send_buffer);
      } else {
        surfel->conduction_interface_fluid_data()->fill_send_buffer(send_buffer);
      }
      s++;
    }
  }

  inline VOID send_msg(ACTIVE_SOLVER_MASK active_solver_mask, sdFLOAT* send_buffer) {
    asINT32 true_sendsize = send_buffer - m_send_msg.buffer();
    int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::WSURFEL>(m_scale);
    LOG_MSG("STRAND_SWITCHING").printf( "Sending conduction interface scale %d size %d tag %x to SP %d", m_scale, true_sendsize, tag, dest_rank());
    m_send_msg.set_nelems(true_sendsize);
    m_send_msg.settag(tag);
    g_exa_sp_cp_comm.isend(m_send_msg);
    m_solver_timestep_index_mask ^= active_solver_mask;
  }

};


typedef class sWSURFEL_SEND_GROUP : public sCONDUCTION_INTERFACE_SEND_GROUP {
 public: 
  VOID allocate_send_buffer() { 
    allocate_conduction_interface();
    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for wsurfel scale %d count %d size %d to SP %d", m_scale, n_surfels(), m_sendsize, dest_rank());
    m_send_msg.allocateBuffer(m_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask) { 
    // complete the previous send if any
    complete_send();
    // fill buffer conduction interface data from surfels
    sdFLOAT *send_buffer = (sdFLOAT *)m_send_msg.buffer();
    fill_conduction_interface(send_buffer);
    // finalizes sending the message
    send_msg(active_solver_mask, send_buffer);
  }

} *WSURFEL_SEND_GROUP;

typedef class sCONTACT_SEND_GROUP : public sCONDUCTION_INTERFACE_SEND_GROUP {
 public: 
  //conduction interface send groups contain an accumulator, so finer scales can accumulate energy
  //transferred to coarser surfels in other SPs
  std::vector<dFLOAT> m_secondary_accumulated_interface_heat;  //accumulator

  VOID allocate_send_buffer() { 
    allocate_conduction_interface();
    //adds the space needed to send the accumulated energy
    m_sendsize += m_secondary_accumulated_interface_heat.size() * sizeof(dFLOAT) / sizeof(sdFLOAT);
    LOG_MSG("STRAND_SWITCHING").printf( "allocating send buffer for contact scale %d count %d size %d to SP %d", m_scale, n_surfels(), m_sendsize, dest_rank());
    m_send_msg.allocateBuffer(m_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask) { 
    // complete the previous send if any
    complete_send();
    // fill buffer conduction interface data from surfels
    sdFLOAT *send_buffer = (sdFLOAT *)m_send_msg.buffer();
    fill_conduction_interface(send_buffer);
    // fills accumulated energy data
    dFLOAT* accum_buffer = reinterpret_cast<dFLOAT*>(send_buffer);
    for (dFLOAT& accumulated_heat : m_secondary_accumulated_interface_heat) {
      memcpy(accum_buffer, &accumulated_heat, sizeof(dFLOAT));
      accumulated_heat = 0.0;
      accum_buffer++;
    }
    send_buffer = reinterpret_cast<sdFLOAT*>(accum_buffer);
    // finalizes sending the message
    send_msg(active_solver_mask, send_buffer);
  }
} *CONTACT_SEND_GROUP;
       
// sSURFEL_SEND_GROUP_ORDER works here; no need for a WSURFEL / CONTACT version

typedef struct sWSURFEL_SEND_FSET : public tSP_FSET < WSURFEL_SEND_GROUP, sSURFEL_SEND_GROUP_ORDER > {
 public:
  WSURFEL_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp);
} *WSURFEL_SEND_FSET;

typedef struct sCONTACT_SEND_FSET : public tSP_FSET < CONTACT_SEND_GROUP, sSURFEL_SEND_GROUP_ORDER > {
 public:
  CONTACT_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp);
} *CONTACT_SEND_FSET;

extern sWSURFEL_SEND_FSET g_wsurfel_send_fset;
extern sCONTACT_SEND_FSET g_contact_send_fset;

#define DO_WSURFEL_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sWSURFEL_SEND_FSET, g_wsurfel_send_fset, group_var)

#define DO_CONTACT_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sCONTACT_SEND_FSET, g_contact_send_fset, group_var)

#define DO_WSURFEL_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sWSURFEL_SEND_FSET, g_wsurfel_send_fset, group_var, scale)

#define DO_CONTACT_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sCONTACT_SEND_FSET, g_contact_send_fset, group_var, scale)


// Receive groups and fsets

class sCONDUCTION_INTERFACE_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>> {

 protected:
  //conduction interface data reside in surfels
  VMEM_VECTOR <SURFEL> m_surfels;

 public:
  SURFEL   *surfels()              { return &m_surfels[0]; }
  SURFEL   *surfel(asINT32 i)      { return &m_surfels[i]; }
  SHOB_ID  n_surfels()             { return m_surfels.size(); }

  VOID add_surfel(SURFEL surfel) {
    m_surfels.push_back(surfel);
  }
        
  VOID post_recv() {
    int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::WSURFEL>(m_scale);
    LOG_MSG("STRAND_SWITCHING").printf( "Posting conduction interface recv: scale %d tag %x, size %d src %d buf %p", m_scale, tag, m_recvsize, source_rank(), fmt::ptr(m_recv_msg.buffer()));
    m_recv_msg.set_nelems(m_recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  VOID allocate_recv_conduction_interface() {
    asINT32 recv_count = n_surfels();
    SURFEL *s = surfels();
    m_recvsize = 0;
    ccDOTIMES(nth_surfel, recv_count) {
      SURFEL surfel = *s;
      if(surfel->is_conduction_surface()) {
        m_recvsize += sCONDUCTION_INTERFACE_SOLID_DATA::send_size();
        if(surfel->is_conduction_shell()) {
          m_recvsize += surfel->shell_conduction_data()->wsurfel_send_size();
        }
      } else if (surfel->is_conduction_shell()) {
        m_recvsize += sCONDUCTION_INTERFACE_OPEN_SHELL_DATA::send_size() + surfel->shell_conduction_data()->wsurfel_send_size();
      } else {
        m_recvsize += surfel->conduction_interface_fluid_data()->send_size();
      }
      s++;
    }
  }
         
  inline VOID unpack_conduction_interface(sdFLOAT* &recv_buffer) {
    SURFEL *s = surfels();
    asINT32 recv_count = n_surfels();
    ccDOTIMES(nth_surfel, recv_count) {
      SURFEL surfel = *s;
      if(surfel->is_conduction_surface()) {
        surfel->conduction_interface_solid_data()->expand_recv_buffer(recv_buffer);
        if(surfel->is_conduction_shell()) {
          surfel->shell_conduction_data()->expand_wsurfel_recv_buffer(recv_buffer);
        }
      } else if (surfel->is_conduction_shell()) {
        surfel->conduction_interface_open_shell_data()->expand_recv_buffer(recv_buffer);
        surfel->shell_conduction_data()->expand_wsurfel_recv_buffer(recv_buffer);
      } else {
        surfel->conduction_interface_fluid_data()->expand_recv_buffer(recv_buffer);
      }
      s++;
    }
  }
};

typedef class sWSURFEL_RECV_GROUP : public sCONDUCTION_INTERFACE_RECV_GROUP {
 public:
  VOID allocate_recv_buffer() {
    allocate_recv_conduction_interface();
    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for conduction interface scale %d size %d to SP %d", m_scale, m_recvsize, source_rank());
    m_recv_msg.allocateBuffer(m_recvsize);
  }

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) {
    sdFLOAT* recv_buffer = (sdFLOAT *)m_recv_msg.buffer();
    unpack_conduction_interface(recv_buffer);
    LOG_MSG("STRAND_SWITCHING").printf( "Unpacked %d conduction interface scale %d SP %d", n_surfels(), m_scale, source_rank());
    set_unpacked();
  }

  // The timestep_index is not used; it merely serves to reserve this version of unpack, in which m_unpacked is
  // not modified (i.e. set_unpacked() not invoked), for use during initialization.
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) {
    sdFLOAT* recv_buffer = (sdFLOAT *)m_recv_msg.buffer();
    unpack_conduction_interface(recv_buffer);
    LOG_MSG("STRAND_SWITCHING").printf( "Unpacked %d conduction interface scale %d SP %d", n_surfels(), m_scale, source_rank());
  }
} *WSURFEL_RECV_GROUP;

typedef class sCONTACT_RECV_GROUP : public sCONDUCTION_INTERFACE_RECV_GROUP {
 public:
  //contact recv includes secondary accumulated data in finer scales that needs to be unpacked to the accumulators
  //within the contact surfel group
  std::vector<dFLOAT*> m_secondary_accumulated_interface_heat_ptr;

  VOID allocate_recv_buffer() {
    allocate_recv_conduction_interface();
    //adds the accumulation data expected
    m_recvsize += m_secondary_accumulated_interface_heat_ptr.size() * sizeof(dFLOAT) / sizeof(sdFLOAT);
    LOG_MSG("STRAND_SWITCHING").printf( "allocating recv buffer for conduction interface scale %d size %d to SP %d", m_scale, m_recvsize, source_rank());
    m_recv_msg.allocateBuffer(m_recvsize);
  }

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) {
    sdFLOAT* recv_buffer = (sdFLOAT *)m_recv_msg.buffer();
    unpack_conduction_interface(recv_buffer);
    unpack_accumulated_data(recv_buffer);
    LOG_MSG("STRAND_SWITCHING").printf( "Unpacked %d conduction interface scale %d SP %d", n_surfels(), m_scale, source_rank());
    set_unpacked();
  }

  // The timestep_index is not used; it merely serves to reserve this version of unpack, in which m_unpacked is
  // not modified (i.e. set_unpacked() not invoked), for use during initialization.
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) {
    sdFLOAT* recv_buffer = (sdFLOAT *)m_recv_msg.buffer();
    unpack_conduction_interface(recv_buffer);
    unpack_accumulated_data(recv_buffer);
    LOG_MSG("STRAND_SWITCHING").printf( "Unpacked %d conduction interface scale %d SP %d", n_surfels(), m_scale, source_rank());
  }
 
 private: 
  inline VOID unpack_accumulated_data(sdFLOAT* &recv_buffer) {
    // fills accumulated energy data, directly adding to the accumulator in the contact surfel group
    dFLOAT* accum_buffer = reinterpret_cast<dFLOAT*>(recv_buffer);
    for (dFLOAT*& accumulated_heat : m_secondary_accumulated_interface_heat_ptr) {
      // memcpy(&accumulated_heat, accum_buffer, sizeof(dFLOAT));
      *accumulated_heat += *accum_buffer;
      accum_buffer++;
    }
    recv_buffer = reinterpret_cast<sdFLOAT*>(accum_buffer);
  }
} *CONTACT_RECV_GROUP;

typedef struct sWSURFEL_RECV_FSET : public tSP_FSET < WSURFEL_RECV_GROUP, sSP_RECV_GROUP_ORDER> {
 public:
  WSURFEL_RECV_GROUP create_group (asINT32 scale, cNEIGHBOR_SP source_sp);
} *WSURFEL_RECV_FSET;

typedef struct sCONTACT_RECV_FSET : public tSP_FSET < CONTACT_RECV_GROUP, sSP_RECV_GROUP_ORDER> {
 public:
  CONTACT_RECV_GROUP create_group (asINT32 scale, cNEIGHBOR_SP source_sp);
} *CONTACT_RECV_FSET;

extern sWSURFEL_RECV_FSET g_wsurfel_recv_fset; 
extern sCONTACT_RECV_FSET g_contact_recv_fset; 

#define DO_WSURFEL_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sWSURFEL_RECV_FSET, g_wsurfel_recv_fset, group_var)

#define DO_CONTACT_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sCONTACT_RECV_FSET, g_contact_recv_fset, group_var)

#define DO_WSURFEL_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sWSURFEL_RECV_FSET, g_wsurfel_recv_fset, group_var, scale)

#define DO_CONTACT_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sCONTACT_RECV_FSET, g_contact_recv_fset, group_var, scale)

#define DO_GHOST_WSURFELS_OF_SCALE(shob_var, scale)                                       \
  FSET_FOREACH_GROUP_OF_SCALE(sWSURFEL_RECV_FSET, g_wsurfel_recv_fset, group_var, scale)  \
    for (auto& shob_var: group_var->surfels())

#define DO_GHOST_CONTACT_SURFELS_OF_SCALE(shob_var, scale)                                \
  FSET_FOREACH_GROUP_OF_SCALE(sCONTACT_RECV_FSET, g_contact_recv_fset, group_var, scale)  \
    for (auto& shob_var: group_var->surfels())

VOID do_preliminary_wsurfel_comm();

#endif // _SIMENG_WSURFEL_COMM_H
