#ifndef UBLK_SMART_SEED_DATA_H
#define UBLK_SMART_SEED_DATA_H
#include "simulator_namespace.h"
#include LGI_H

/*==============================================================================
 * @struct tUBLK_SMART_SEED_DATA
 * This struct is designed to overlap the states field of a ublk/mblk (with INLINE block layout)
 * such that the data for a particular voxel overlays that voxel's states (and no other voxel's states). 
 * This allows each voxel to be seeded independently, extracting its seed data and 
 * depositing initial state values. It also ensures that the states of advect only voxels 
 * are left zero'ed. The one exception to this rule is the "next" pointer, used to build 
 * a linked list of ublks that are in need of seed data extrapolation. When a
 * ublk is finally removed from that list, we make sure that the "next" ptr is zero'ed.
 *
 * For UBLKs without an inline block layout, the smart seed data does not overlap
 * any other UBLK data blocks and is allocated separately.
 *============================================================================*/

// Base UBLK seed data has 4 * N_VOXELS * sizeof(VOXEL_STATE) worth of data
// We have only N_MOVING_STATES - 4 available slots before seed data spills over the smaller of
// LB_STATE data and Nearblock post advect scale factors
constexpr static size_t N_AVAILABLE_DGF_SEED_SLOTS = N_MOVING_STATES - 4;

INLINE __HOST__DEVICE__ asINT32 get_dgf_active_seed_var_index(DGF_SEED_VAR_TYPE var, uINT8 controller_index) {  
  auto& sim = get_sim_ref();

  asINT32 index = sim.m_seed_control[controller_index].active_seed_var_index[var];

  //If you hit this assertion, you probably tried accessing a variable that was not
  //seeded
  // cassert(index >= 0 && index < N_AVAILABLE_DGF_SEED_SLOTS);
  if (!(index >= 0 && index < N_AVAILABLE_DGF_SEED_SLOTS)) {
    HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT("Bad seed var %d index %d", var, index);
  }

  return index;
}

template<typename UBLK_TYPE_TAG>
struct tUBLK_SMART_SEED_DATA;

//UBLK smart seed data
template<>
struct tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG> {
  
  using UBLK_TYPE = tUBLK<UBLK_SDFLOAT_TYPE_TAG>;
  // Leave one state available for scratch for other initialization activities. For one example of this,
  // see initialize_diagonal_advect_span_root_masks.
  VOXEL_STATE unused[N_VOXELS_8];
  union {
    UBLK_TYPE* next;  // next in extrapolate list
    VOXEL_STATE unused[N_VOXELS_8];
  } u;
  union {
    struct {
      uINT16 iter_count;
      uINT8  missing_seed_data;
      uINT8  needs_seed_data;
    } s;
    VOXEL_STATE unused;
  } v[N_VOXELS_8];
  union {
    uINT8 seed_var_spec_index;
    VOXEL_STATE unused;
  } w[N_VOXELS_8];          

  UBLK_TYPE* &next() { return u.next; }

  uINT16 &iter_count(asINT32 voxel)        { return v[voxel].s.iter_count; }
  uINT8  &missing_seed_data(asINT32 voxel) { return v[voxel].s.missing_seed_data; }
  uINT8  &needs_seed_data(asINT32 voxel)   { return v[voxel].s.needs_seed_data; }

  __HOST__DEVICE__ VOXEL_STATE& vars(DGF_SEED_VAR_TYPE var, asINT32 voxel, uINT8 controller_index) {    
    return m_vars[get_dgf_active_seed_var_index(var, controller_index)][voxel];
  }

  __HOST__DEVICE__ VOXEL_STATE& vars(asINT32 var, asINT32 voxel, uINT8 controller_index) {
    return vars(static_cast<DGF_SEED_VAR_TYPE>(var), voxel, controller_index);
  }  

  __HOST__DEVICE__ VOXEL_STATE vars(DGF_SEED_VAR_TYPE var, asINT32 voxel, uINT8 controller_index) const {
    return const_cast<tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG>*>(this)->vars(var, voxel, controller_index);
  }

  __HOST__DEVICE__ VOXEL_STATE vars(asINT32 var, asINT32 voxel, uINT8 controller_index) const {
    return vars(static_cast<DGF_SEED_VAR_TYPE>(var), voxel, controller_index);
  }
  
private:
  // some vars provided by seed file, some not  
  VOXEL_STATE m_vars[N_AVAILABLE_DGF_SEED_SLOTS][N_VOXELS_8];
};

static_assert(sizeof(tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG>) ==
              (N_MOVING_STATES * N_VOXELS_8 * sizeof(sdFLOAT)),
              "Smart seed data cannot exceed the size of single set of moving states");

//MBLK smart seed data
template<typename UBLK_TYPE_TAG>
struct tUBLK_SMART_SEED_DATA {

  union {
    uINT8 seed_var_spec_index;
    VOXEL_STATE unused;
  } w[UBLK_TYPE_TAG::N_VOXELS];
  

  template<typename UBLK_TYPE_TAG_OTHER>
  __HOST__DEVICE__ VOID copy(const tUBLK_SMART_SEED_DATA<UBLK_TYPE_TAG_OTHER>& other,
                             int v, uINT8 this_controller_index, uINT8 other_controller_index) {
    
    static_assert(UBLK_TYPE_TAG::N_VOXELS == UBLK_TYPE_TAG_OTHER::N_VOXELS,
                  "This function only makes sense for UBLKs with the same voxel count");
    
    this->w[v].seed_var_spec_index = other.w[v].seed_var_spec_index;
    auto& sim = get_sim_ref();
    ccDOTIMES(var, DGF_N_SEED_VARS) {
      if (sim.m_seed_control[this_controller_index].is_var_seeded[var]) {
        this->vars(var, v, this_controller_index) = other.vars(var, v, other_controller_index);
      }
    }
  }

  __HOST__DEVICE__ VOXEL_STATE& vars(DGF_SEED_VAR_TYPE var, asINT32 voxel, uINT8 controller_index) {
    return m_vars[get_dgf_active_seed_var_index(var, controller_index)][voxel];
  }

  __HOST__DEVICE__ VOXEL_STATE& vars(asINT32 var, asINT32 voxel, uINT8 controller_index) {
    return vars(static_cast<DGF_SEED_VAR_TYPE>(var), voxel, controller_index);
  }
  
  __HOST__DEVICE__ VOXEL_STATE vars(DGF_SEED_VAR_TYPE var, asINT32 voxel, uINT8 controller_index) const {
    return const_cast<tUBLK_SMART_SEED_DATA<UBLK_TYPE_TAG>*>(this)->vars(var, voxel, controller_index);
  }

  __HOST__DEVICE__ VOXEL_STATE vars(asINT32 var, asINT32 voxel, uINT8 controller_index) const {
    return vars(static_cast<DGF_SEED_VAR_TYPE>(var), voxel, controller_index);
  }  
private:
  VOXEL_STATE m_vars[N_AVAILABLE_DGF_SEED_SLOTS][UBLK_TYPE_TAG::N_VOXELS];
};

using sUBLK_SMART_SEED_DATA = tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG>;
using UBLK_SMART_SEED_DATA = sUBLK_SMART_SEED_DATA*;

//UDS data
template<typename UBLK_TYPE_TAG>
struct tUBLK_SMART_SEED_UDS_DATA {
  __HOST__DEVICE__ VOXEL_STATE& vars(asINT32 var, asINT32 voxel) {
    return m_uds_vars[var][voxel];
  }

  __HOST__DEVICE__ VOXEL_STATE& vars(DGF_SEED_UDS_VAR_TYPE var, asINT32 voxel) {
    return vars(static_cast<int>(var), voxel);
  }

  __HOST__DEVICE__ VOXEL_STATE vars(asINT32 var, asINT32 voxel) const {
    return vars(var, voxel);
  }
  
  __HOST__DEVICE__ VOXEL_STATE vars(DGF_SEED_UDS_VAR_TYPE var, asINT32 voxel) const {
    return const_cast<tUBLK_SMART_SEED_UDS_DATA<UBLK_TYPE_TAG>*>(this)->vars(var, voxel);
  }
  
  private:
  VOXEL_STATE m_uds_vars[DGF_N_SEED_UDS_VARS][UBLK_TYPE_TAG::N_VOXELS];
};

using sUBLK_SMART_SEED_UDS_DATA = tUBLK_SMART_SEED_UDS_DATA<UBLK_SDFLOAT_TYPE_TAG>;
using UBLK_SMART_SEED_UDS_DATA = sUBLK_SMART_SEED_UDS_DATA*;

#if BUILD_GPU

static_assert(sizeof(tUBLK_SMART_SEED_DATA<MBLK_SDFLOAT_TYPE_TAG>) <=
              (N_MOVING_STATES * MBLK_SDFLOAT_TYPE_TAG::N_VOXELS * sizeof(sdFLOAT)),
              "Smart seed data cannot exceed the size of single set of moving states");

VOID init_mblk(tUBLK_SMART_SEED_DATA<UBLK_SDFLOAT_TYPE_TAG>& child_smart_seed_data,
               tUBLK_SMART_SEED_DATA<HMBLK_SDFLOAT_TYPE_TAG>& mblk_smart_seed_data,
               int child_ublk);
#endif

#endif
