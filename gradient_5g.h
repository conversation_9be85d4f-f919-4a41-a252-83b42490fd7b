/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2001, 1995-2000 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Public header of the gradient operator
 *
 *--------------------------------------------------------------------------*/
#ifndef	_SIMENG_GRADIENT_H
#define	_SIMENG_GRADIENT_H


#include "lattice.h"
#include "vectorization_support.h"

class iGRADIENT_OP
{
public:

  static asINT32 s_ublk_index[N_MOVING_STATES][ubFLOAT::N_VOXELS];
  static asINT32 s_voxel_index[N_MOVING_STATES][ubFLOAT::N_VOXELS];

  static iGRADIENT_OP *create (asINT32 num_dims, asINT32 mp_gradient_level, sdFLOAT e2);

  virtual VOID neighbor (asINT32 x, asINT32 y, asINT32 z, ubFLOAT& field, ubFLOAT gradient[]) = 0;
  virtual sdFLOAT get_scale() = 0;
        
};

static INLINE asINT32 dir_index(asINT32 cx, asINT32 cy, asINT32 cz)
{
  return ((cx + 1) * 9 + (cy + 1) *3 + (cz + 1));
}


#endif
