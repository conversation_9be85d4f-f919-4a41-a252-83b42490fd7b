/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Support for interprocessor ublk and surfel communication
 *
 * Samuel Watson, Exa Corporation 
 * Created Jan 14 2015
 *--------------------------------------------------------------------------*/

#include "shob_groups.h"
#include "comm_groups.h"
#include "bsurfel_comm.h"
#include "particle_comm.h"
#include "wsurfel_comm.h"
#include "sleep.h"
#include "thread_run.h"
#include "vr.h"
#include "film_comm.h"

sFARBLK_RECV_FSET g_farblk_recv_fset;
sNEARBLK_RECV_FSET g_nearblk_recv_fset;
sSURFEL_RECV_FSET g_surfel_recv_fset;
sSAMPLING_SURFEL_RECV_FSET g_sampling_surfel_recv_fset;

SURFEL_RECV_GROUP sSURFEL_RECV_FSET::create_group (asINT32 scale, cNEIGHBOR_SP source_sp) {
  static sSURFEL_RECV_GROUP signature;
  signature.m_scale = scale;
  signature.m_source_sp = source_sp;
  SURFEL_RECV_GROUP group = find_group(&signature);
  if (NULL == group) {
    group = xnew sSURFEL_RECV_GROUP;
    group->m_scale = scale;
    group->m_source_sp = source_sp;
    group->m_recv_msg.init(group->source_rank(), my_proc_id);
    group->m_recv_type = SURFEL_RECV_TYPE;
    add_group(group);
  }
  return group;
}

// There is an asymmetry between ublk send groups and ublk recv groups
// nearblk and farblk groups belong to the same ublk fset
sUBLK_SEND_FSET g_ublk_send_fset;
sSURFEL_SEND_FSET g_surfel_send_fset;
sSAMPLING_SURFEL_SEND_FSET g_sampling_surfel_send_fset;

SURFEL_SEND_GROUP sSURFEL_SEND_FSET::create_group (asINT32 scale, cNEIGHBOR_SP dest_sp) {
  static sSURFEL_SEND_GROUP signature;
  signature.m_scale = scale;
  signature.m_dest_sp = dest_sp;

  SURFEL_SEND_GROUP group = find_group(&signature);
  if (NULL == group) {
    group = xnew sSURFEL_SEND_GROUP;
    group->m_scale = scale;
    group->m_solver_timestep_index_mask = sim.init_solver_mask;
    group->m_dest_sp = dest_sp;
    group->m_send_msg.init(my_proc_id, group->dest_rank());
    add_group(group);
  }
  return group;
}

VOID sFARBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}

VOID sFARBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  sUBLK_RECV_DATA_INFO recv_data_info;
  recv_data_info.recv_buffer = m_recv_msg.buffer();
  recv_data_info.t_solver_type = t_solver_type;
  
  BOOLEAN is_2d = sim.is_2d();  
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(recv_data_info.recv_buffer);
  m_solver_timestep_index_mask = *solver_index_mask;
  solver_index_mask++;
  recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  for(const auto& quantum: m_quantums) {
    quantum.m_ublk->expand_recv_buffer_for_farblk(quantum.m_nmi, sim.init_solver_mask, recv_data_info,  m_solver_timestep_index_mask);
    if(quantum.m_ublk->is_vr_coarse()) {
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data = quantum.m_ublk->vr_coarse_data();
      asINT32 n_fine_blks = ubFLOAT::N_VOXELS;
      for (asINT32 coarse_voxel = 0; coarse_voxel < n_fine_blks; coarse_voxel++) {
        asINT32 fine_blk_idx = coarse_voxel;
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
        if (fine_ublk == NULL)
          continue;
        fine_ublk->unset_exploded();
      }
    }
  }
}

VOID sFARBLK_RECV_GROUP::unpack_init_info()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  UBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_recv_buffer =
    reinterpret_cast<UBLK_INIT_INFO_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_ublk->unpack_recv_init_info_buffer(ghostblk_init_recv_buffer);
    ghostblk_init_recv_buffer++;
  }
}

#if BUILD_5G_LATTICE
VOID sFARBLK_RECV_GROUP::unpack_init_pore_data()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  for(const auto& quantum: m_quantums) {
    //if (!quantum.m_ublk->is_vr_fine())  //no vr in 5G
      quantum.m_ublk->expand_pore_recv_buffer(recv_buffer);
  }
}
#endif

VOID sNEARBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}

VOID sNEARBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  sUBLK_RECV_DATA_INFO recv_data_info;
  recv_data_info.recv_buffer = m_recv_msg.buffer();
  recv_data_info.t_solver_type = t_solver_type;
  BOOLEAN is_2d = sim.is_2d();  
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(recv_data_info.recv_buffer);
  m_solver_timestep_index_mask = *solver_index_mask;
  solver_index_mask++;
  recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);

  for(const auto& quantum: m_quantums) {
    UBLK gblk = quantum.m_ublk;
    gblk->expand_recv_buffer_for_nearblk(quantum.m_nmi, active_solver_mask,
                                         recv_data_info, m_solver_timestep_index_mask);

    if (gblk->is_vr_coarse()) {
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data = quantum.m_ublk->vr_coarse_data();
      asINT32 n_fine_blks = ubFLOAT::N_VOXELS;
      for (asINT32 coarse_voxel = 0; coarse_voxel < n_fine_blks; coarse_voxel++) {
        asINT32 fine_blk_idx = coarse_voxel;
        UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(fine_blk_idx).ublk();
        if (fine_ublk == NULL)
          continue;
        fine_ublk->unset_exploded();
      }
    }
  }
}

VOID sUBLK_MME_RECV_GROUP::process_quantum(const sUBLK_COMM_QUANTUM& quantum, sdFLOAT * &recv_buffer, ACTIVE_SOLVER_MASK active_solver_mask)
{
  static BOOLEAN is_2d = sim.is_2d();
  UBLK gblk = quantum.m_ublk;
  gblk->expand_mme_recv_buffer(active_solver_mask, recv_buffer, m_solver_timestep_index_mask);
  LOG_MSG_IF(gblk->id() == 28662,"BSURFEL_CKPT","timestep index",m_solver_timestep_index_mask) << "======== sUBLK_MME_RECV_GROUP =============";
  if (gblk->is_vr_coarse()) {
    execute_explode_mme(gblk, m_solver_timestep_index_mask, active_solver_mask);
  }
  // if (gblk->id() == 28662) {
  //   print_voxel_states("After MME Unpacking",gblk->id(),6,0,-1);
  // }
}


VOID sUBLK_MME_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{

  LOG_MSG("RECV_DEPEND").printf("Unpacking ublks MME scale %d TI %d near %d size %d SSP %d",
                                m_scale, m_solver_timestep_index_mask, this->m_is_near_surface, this->m_recvsize,
                                this->m_source_sp.rank());
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(recv_buffer);
  m_solver_timestep_index_mask = *solver_index_mask;
  solver_index_mask++;
  recv_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  for(const auto& quantum: m_quantums) {
    if (!quantum.m_ublk->is_vr_fine())
      process_quantum(quantum, recv_buffer, active_solver_mask);
  }
  set_unpacked();
}

#if BUILD_5G_LATTICE
VOID sUBLK_PORE_RECV_GROUP::process_quantum(const sUBLK_COMM_QUANTUM& quantum, sdFLOAT * &recv_buffer, ACTIVE_SOLVER_MASK active_solver_mask)
{
  //static BOOLEAN is_2d = sim.is_2d();
  UBLK gblk = quantum.m_ublk;
  gblk->expand_pore_recv_buffer(recv_buffer);
  LOG_MSG_IF(gblk->id() == 28662,"BSURFEL_CKPT","timestep index", m_solver_timestep_index_mask) << "======== sUBLK_PORE_RECV_GROUP =============";

  /* Since 5G does not support VR, no need to explode pore data for vr coarse ublk */
  /*if (gblk->is_vr_coarse()) {
    execute_explode_pore(gblk, m_timestep_index, active_solver_mask); //no such a function so far, bur can be added once 5G supports VR
    }*/
}


VOID sUBLK_PORE_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  LOG_MSG("RECV_DEPEND").printf("Unpacking ublks PORE scale %d TI %d near %d size %d SSP %d",
                                m_scale, m_solver_timestep_index_mask, this->m_is_near_surface, this->m_recvsize,
                                this->m_source_sp.rank());

  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  for(const auto& quantum: m_quantums) {
    //if (!quantum.m_ublk->is_vr_fine())  //no vr in 5G
    process_quantum(quantum, recv_buffer, active_solver_mask);
  }
  set_unpacked();
}
#endif

VOID sNEARBLK_RECV_GROUP::unpack_init_info()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  NEARBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_recv_buffer =
    reinterpret_cast<NEARBLK_INIT_INFO_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_ublk->unpack_recv_init_info_for_nearblk(ghostblk_init_recv_buffer++);
  }
}

VOID sNEARBLK_RECV_GROUP::unpack_conduction_init_info()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  COND_NEARBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_recv_buffer =
    reinterpret_cast<COND_NEARBLK_INIT_INFO_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_ublk->unpack_recv_init_info_for_cond_nearblk(ghostblk_init_recv_buffer++);
  }
}

#if BUILD_5G_LATTICE
VOID sNEARBLK_RECV_GROUP::unpack_init_pore_data()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  for(const auto& quantum: m_quantums) {
    UBLK gblk = quantum.m_ublk;
    //if (!gblk->is_vr_fine())  //no vr in 5G
    gblk->expand_pore_recv_buffer(recv_buffer);
  }
}
#endif

VOID sSURFEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}

// The timestep_index is not used; it merely serves to reserve this version of unpack, in which m_unpacked is
// not modified, for use during initialization.

VOID sSURFEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  sSURFEL_RECV_DATA_INFO recv_data_info;
  recv_data_info.recv_buffer = m_recv_msg.buffer();

  recv_data_info.t_solver_type = t_solver_type;
  
  for(const auto& quantum: m_quantums) {
    quantum.m_surfel->expand_recv_buffer(active_solver_mask, recv_data_info);
  }

}

VOID sSURFEL_RECV_GROUP::unpack_init_info()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  SURFEL_INIT_INFO_SEND_ELEMENT ghost_surfel_init_recv_buffer =
    reinterpret_cast<SURFEL_INIT_INFO_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_recv_init_info_buffer(ghost_surfel_init_recv_buffer);
    ghost_surfel_init_recv_buffer++;
  }
}

VOID sSURFEL_RECV_GROUP::unpack_surfel_type()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  SURFEL_INIT_INFO_SEND_ELEMENT ghost_surfel_init_recv_buffer =
    reinterpret_cast<SURFEL_INIT_INFO_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_recv_surfel_type_buffer(ghost_surfel_init_recv_buffer);
    ghost_surfel_init_recv_buffer++;
  }
}

VOID sSURFEL_RECV_GROUP::unpack_bc_types()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  SURFEL_BCTYPE_SEND_ELEMENT ghost_surfel_bctype_recv_buffer =
    reinterpret_cast<SURFEL_BCTYPE_SEND_ELEMENT>(recv_buffer);
  for(const auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_recv_bc_type_buffer(ghost_surfel_bctype_recv_buffer);
    ghost_surfel_bctype_recv_buffer++;
  }
}

VOID sSURFEL_RECV_GROUP::recv_and_unpack_neighbor_ublks()
{
  // receive the message containing the data size
  m_recv_neigbor_ublks_data_size.init(source_rank(), my_proc_id);
  m_recv_neigbor_ublks_data_size.set_nelems(1);
  m_recv_neigbor_ublks_data_size.settag(eMPI_SURFEL_NEIGHBORS_BYTES_TAG);
  g_exa_sp_cp_comm.recv(m_recv_neigbor_ublks_data_size);

  complete_recv();

  // receive the neighboring ublk data
  uINT32 recvsize = *m_recv_neigbor_ublks_data_size.buffer();
  m_recv_msg.settag(eMPI_SURFEL_NEIGHBORS_TAG);
  m_recv_msg.set_nelems(recvsize);
  if (recvsize*sizeof(sdFLOAT) > m_recv_msg.allocatedsize()) {
    m_recv_msg.reallocateBuffer(recvsize);
  }

  g_exa_sp_cp_comm.recv(m_recv_msg);
  uINT8 *buffer = reinterpret_cast<uINT8 *> (m_recv_msg.buffer());
  for (auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_neighbor_ublks(buffer);
  }
}

VOID sSAMPLING_SURFEL_RECV_GROUP::recv_and_unpack_neighbor_ublks()
{
  // receive the message containing the data size
  m_recv_neighbor_ublks_data_size.init(source_rank(), my_proc_id);
  m_recv_neighbor_ublks_data_size.set_nelems(1);
  m_recv_neighbor_ublks_data_size.settag(eMPI_SURFEL_NEIGHBORS_BYTES_TAG);
  g_exa_sp_cp_comm.recv(m_recv_neighbor_ublks_data_size);

  allocate_recv_buffer(*m_recv_neighbor_ublks_data_size.buffer());
  complete_recv();

  // receive the neighboring ublk data
  uINT32 recvsize = *m_recv_neighbor_ublks_data_size.buffer();
  m_recv_msg.settag(eMPI_SURFEL_NEIGHBORS_TAG);
  m_recv_msg.set_nelems(recvsize);
  g_exa_sp_cp_comm.recv(m_recv_msg);
  uINT8 *buffer = reinterpret_cast<uINT8 *> (m_recv_msg.buffer());
  for (auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_neighbor_ublks(buffer);
  }
}

VOID sSURFEL_SEND_GROUP::pack_and_send_neighbor_ublks() {
  // TODO: wrap up with ExaMsg. Should avoid direct MPI calls
  complete_mpi_request_while_processing_cp_messages(&m_send_neigbor_ublks_data_size.m_request, MPI_SLEEP_LONG);

  // pack data in buffer for m_send_message
  m_send_neigbor_ublks_data_size.init(my_proc_id, dest_rank());
  uINT32 num_bytes = 0;

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->count_neighbor_ublks_bytes(num_bytes);
  }

  if (num_bytes > m_send_msg.allocatedsize()) {
    m_send_msg.reallocateBuffer(num_floats_from_num_bytes(num_bytes));
  }

  uINT8 *buffer = reinterpret_cast<uINT8 *> (m_send_msg.buffer());

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_neighbor_ublks(buffer);
  }

  // compute data size in floats
  asINT32 bytesize = buffer - reinterpret_cast<uINT8 *> (m_send_msg.buffer());
  uINT32 send_size = num_floats_from_num_bytes(bytesize) ;

  // send data size
  uINT32 *size_buffer = m_send_neigbor_ublks_data_size.buffer();
  *size_buffer = send_size;
  m_send_neigbor_ublks_data_size.set_nelems(1);
  m_send_neigbor_ublks_data_size.settag(eMPI_SURFEL_NEIGHBORS_BYTES_TAG);
  g_exa_sp_cp_comm.isend(m_send_neigbor_ublks_data_size);

  // send neighbor ublks data
  complete_send();
  m_send_msg.set_nelems(send_size);
  m_send_msg.settag(eMPI_SURFEL_NEIGHBORS_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);
}

VOID sSAMPLING_SURFEL_SEND_GROUP::allocate_send_buffer() {
  uINT32 num_bytes = 0;
  for (auto& quantum: m_quantums) {
    quantum.m_surfel->count_neighbor_ublks_bytes(num_bytes);
  }
  uINT32 send_size = num_floats_from_num_bytes(num_bytes);
  m_send_msg.allocateBuffer(send_size);
}

VOID sSAMPLING_SURFEL_SEND_GROUP::pack_and_send_neighbor_ublks() {
  allocate_send_buffer();
  // TODO: wrap up with ExaMsg. Should avoid direct MPI calls
  complete_mpi_request_while_processing_cp_messages(&m_send_neigbor_ublks_data_size.m_request, MPI_SLEEP_LONG);

  // pack data in buffer for m_send_message
  m_send_neigbor_ublks_data_size.init(my_proc_id, dest_rank());
  uINT8 *buffer = reinterpret_cast<uINT8 *> (m_send_msg.buffer());

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_neighbor_ublks(buffer);
  }

  // compute data size in floats
  asINT32 bytesize = buffer - reinterpret_cast<uINT8 *> (m_send_msg.buffer());
  uINT32 send_size = num_floats_from_num_bytes(bytesize) ;

  // send data size
  uINT32 *size_buffer = m_send_neigbor_ublks_data_size.buffer();
  *size_buffer = send_size;
  m_send_neigbor_ublks_data_size.set_nelems(1);
  m_send_neigbor_ublks_data_size.settag(eMPI_SURFEL_NEIGHBORS_BYTES_TAG);
  g_exa_sp_cp_comm.isend(m_send_neigbor_ublks_data_size);

  // send neighbor ublks data
  complete_send();
  m_send_msg.set_nelems(send_size);
  m_send_msg.settag(eMPI_SURFEL_NEIGHBORS_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);
}

VOID sNEARBLK_SEND_GROUP::send_init_info()
{
  NEARBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_send_buffer =
    reinterpret_cast<NEARBLK_INIT_INFO_SEND_ELEMENT>( m_send_msg.buffer());
  NEARBLK_INIT_INFO_SEND_ELEMENT buf_ptr = ghostblk_init_send_buffer;

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_ublk->fill_send_init_info_for_nearblk(buf_ptr++);
  }

  asINT32 init_bytesize = sizeof(sNEARBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
  uINT32 floatsize = num_floats_from_num_bytes(init_bytesize) ;

  m_send_msg.set_nelems(floatsize) ;
  m_send_msg.settag(eMPI_GHOSTBLK_INIT_TAG);

  // There appears to be a bug in recent versions of Intel MPI that causes some
  // simulations to hange here if the total number of messages is large. Using
  // synchronous send makes it better-behaved.

  g_exa_sp_cp_comm.isend(m_send_msg);

}

VOID sNEARBLK_SEND_GROUP::send_conduction_init_info()
{
  COND_NEARBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_send_buffer =
    reinterpret_cast<COND_NEARBLK_INIT_INFO_SEND_ELEMENT>(m_send_msg.buffer());
  COND_NEARBLK_INIT_INFO_SEND_ELEMENT buf_ptr = ghostblk_init_send_buffer;

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_ublk->fill_send_init_info_for_cond_nearblk(buf_ptr++);
  }

  asINT32 init_bytesize = sizeof(sCOND_NEARBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
  uINT32 floatsize = num_floats_from_num_bytes(init_bytesize);

  m_send_msg.set_nelems(floatsize) ;
  m_send_msg.settag(eMPI_COND_GHOSTBLK_INIT_TAG);

  // There appears to be a bug in recent versions of Intel MPI that causes some
  // simulations to hange here if the total number of messages is large. Using
  // synchronous send makes it better-behaved.

  g_exa_sp_cp_comm.isend(m_send_msg);
}

#if BUILD_5G_LATTICE
VOID sNEARBLK_SEND_GROUP::send_init_pore_data()
{
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_PORE>(m_scale);

  // complete the previous send if any
  complete_send();
  
  for (auto& quantum: m_quantums) {
    dassert(quantum.m_ublk->is_near_surface());
    //if (!quantum.m_ublk->is_vr_fine())   //no VR in 5G
      quantum.m_ublk->fill_pore_send_buffer(send_buffer);    
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
}
#endif

VOID sFARBLK_SEND_GROUP::send_init_info()
{
  UBLK_INIT_INFO_SEND_ELEMENT ghostblk_init_send_buffer = 
    reinterpret_cast<UBLK_INIT_INFO_SEND_ELEMENT>(m_send_msg.buffer());
  UBLK_INIT_INFO_SEND_ELEMENT buf_ptr = ghostblk_init_send_buffer;

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_ublk->fill_send_init_info_buffer(buf_ptr++);
  }
  asINT32 init_bytesize = sizeof(sUBLK_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
  uINT32 floatsize = num_floats_from_num_bytes(init_bytesize) ;

  m_send_msg.set_nelems(floatsize);
  m_send_msg.settag(eMPI_GHOSTBLK_INIT_TAG);
#if defined(_EXA_IMPI)
  g_exa_sp_cp_comm.issend(m_send_msg);
#else
  g_exa_sp_cp_comm.isend(m_send_msg);
#endif

}

#if BUILD_5G_LATTICE
VOID sFARBLK_SEND_GROUP::send_init_pore_data()
{
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_PORE>(m_scale);

  // complete the previous send if any
  complete_send();
  
  for (auto& quantum: m_quantums) {
    //if (!quantum.m_ublk->is_vr_fine())   //no VR in 5G
      quantum.m_ublk->fill_pore_send_buffer(send_buffer);
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
}
#endif

VOID sNEARBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  send(active_solver_mask,m_solver_timestep_index_mask);
}

VOID sNEARBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  sUBLK_SEND_DATA_INFO send_data_info;
  send_data_info.send_buffer = m_send_msg.buffer();
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK>(m_scale);
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;

  // complete the previous send if any
  complete_send();

  send_data_info.t_solver_type = t_solver_type;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(send_data_info.send_buffer);
  *solver_index_mask = solver_ts_index_mask;
  solver_index_mask++;
  send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);

  for (auto& quantum: m_quantums) {
    cassert(quantum.m_ublk->is_near_surface());
    quantum.m_ublk->fill_send_buffer_for_nearblk(quantum.m_nmi, sim.init_solver_mask, send_data_info, solver_ts_index_mask);
  }

  asINT32 true_sendsize = send_data_info.send_buffer - m_send_msg.buffer();
  LOG_MSG("STRAND_SWITCHING").printf("Sending nearblks size %d scale %d to SP %d tag %d", true_sendsize, m_scale, dest_rank(), tag);
  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);

  m_solver_timestep_index_mask = solver_ts_index_mask^active_solver_mask;
}

VOID sFARBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  send(active_solver_mask,m_solver_timestep_index_mask);
}

VOID sFARBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  sUBLK_SEND_DATA_INFO send_data_info;
  send_data_info.send_buffer = m_send_msg.buffer();
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK>(m_scale);
  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;

  // complete the previous send if any
  complete_send();
  
  send_data_info.t_solver_type = t_solver_type;
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(send_data_info.send_buffer);
  *solver_index_mask = solver_ts_index_mask;
  solver_index_mask++;
  send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  LOG_MSG("STRAND_SWITCHING").printf( "Sending farblks scale %d to SP %d tag %x", m_scale, dest_rank(), tag);
  for (auto& quantum: m_quantums) {
    quantum.m_ublk->fill_send_buffer_for_farblk(quantum.m_nmi, sim.init_solver_mask, send_data_info, solver_ts_index_mask);
  }
  LOG_MSG("STRAND_SWITCHING").printf( "finished sending farblks scale %d tag %x to SP%d", m_scale, tag, dest_rank());

  asINT32 true_sendsize = send_data_info.send_buffer - m_send_msg.buffer();
  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);

  m_solver_timestep_index_mask = solver_ts_index_mask^active_solver_mask;
}


VOID sUBLK_MME_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = 0;
  if (m_is_near_surface) {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_MME>(m_scale);
  }
  else {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_MME>(m_scale);
  }

  typedef sFLOAT_TO_INT<sdFLOAT>::type INT_TYPE;
  // complete the previous send if any
  complete_send();
  INT_TYPE *solver_index_mask = reinterpret_cast<INT_TYPE*>(send_buffer);
  *solver_index_mask = m_solver_timestep_index_mask;
  solver_index_mask++;
  send_buffer = reinterpret_cast<sdFLOAT*>(solver_index_mask);
  for (auto& quantum: m_quantums) {
    UBLK ublk = quantum.m_ublk;
    if (!ublk->is_vr_fine())
      ublk->fill_mme_send_buffer(sim.init_solver_mask, send_buffer, m_solver_timestep_index_mask);
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();

  LOG_MSG("STRAND_SWITCHING").printf( "sending ublks mme scale %d size %d tag %x to SP%d near %d", m_scale, true_sendsize, tag, dest_rank(), m_is_near_surface);

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
  m_solver_timestep_index_mask ^= active_solver_mask;

}

VOID sPHASE_FIELD_COLLECTIVE_GROUP::collective_call()
{
  MPI_Allreduce(MPI_IN_PLACE, &g_pf_sum_err_p, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_comm);
  MPI_Allreduce(MPI_IN_PLACE, &g_pf_sum_pfluid, 1, MPI_DOUBLE, MPI_SUM, eMPI_sp_comm);
  MPI_Allreduce(MPI_IN_PLACE, &g_pf_is_open_sys, 1, MPI_INT, MPI_SUM, eMPI_sp_comm);
  g_pf_err_p = g_pf_sum_err_p/g_pf_sum_pfluid/sim_scale_to_voxel_size(0);
  if(g_pf_is_open_sys > 0) g_pf_is_open_sys = 1;

  if(g_pf_int_dummy1 == 11 || (g_pf_is_open_sys && g_pf_int_dummy1 != 12))
    g_pf_err_p = 0.0;
  if(g_pf_ur_omega_p_err >= 0.0){
    g_pf_err_p_ur = g_pf_err_p + g_pf_err_p_ur * g_pf_ur_omega_p_err;
  }

  //if(my_proc_id == 0) printf("after MPI_allreduce. time: %ld, my_proc_id: %d, g_pf_sum_err_p: %.16e, g_pf_sum_pfluid: %.16e, g_pf_err_p: %.16e\n", g_timescale.m_time, my_proc_id, g_pf_sum_err_p, g_pf_sum_pfluid, g_pf_err_p);
  g_pf_sum_err_p = 0.0;
  g_pf_sum_pfluid = 0.0;

  m_collective_called = true;
 
  pthread_mutex_lock(&g_strand_mgr.m_time_update_mutex);

  this->reset_collective_called();

  g_strand_mgr.collective_update_dcntr(m_collective_type);

  // if (!g_sync_threads.is_compute_thread_awake()) {
  if (g_strand_mgr.m_ready_mask.load(std::memory_order_relaxed).any()) {
    g_sync_threads.wake_up_compute_thread();
  }
  // }

  pthread_mutex_unlock(&g_strand_mgr.m_time_update_mutex);
 
  LOG_ON("PHASE_FIELD")<<"collective_call";
  
}


#if BUILD_5G_LATTICE
VOID sUBLK_PORE_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  sdFLOAT *send_buffer = m_send_msg.buffer();
  int tag = 0;
  if (m_is_near_surface) {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_PORE>(m_scale);
  }
  else {
    tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_PORE>(m_scale);
  }

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    UBLK ublk = quantum.m_ublk;
    //if (!ublk->is_vr_fine())  //no VR in 5G
      ublk->fill_pore_send_buffer(send_buffer);
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();
  
  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
}
#endif

VOID sSURFEL_SEND_GROUP::send_init_info()
{

  SURFEL_INIT_INFO_SEND_ELEMENT buf_ptr = 
    reinterpret_cast<SURFEL_INIT_INFO_SEND_ELEMENT> (m_send_msg.buffer());

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_send_init_info_buffer(buf_ptr++);
  }
  asINT32 init_bytesize = sizeof(sSURFEL_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
  uINT32 floatsize = num_floats_from_num_bytes(init_bytesize) ;

  m_send_msg.set_nelems(floatsize);
  m_send_msg.settag(eMPI_SURFEL_PRELIM_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);


}
VOID sSURFEL_SEND_GROUP::send_surfel_type()
{

  SURFEL_INIT_INFO_SEND_ELEMENT buf_ptr =
    reinterpret_cast<SURFEL_INIT_INFO_SEND_ELEMENT> (m_send_msg.buffer());

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_send_surfel_type_buffer(buf_ptr++);
  }
  asINT32 init_bytesize = sizeof(sSURFEL_INIT_INFO_SEND_ELEMENT) * m_quantums.size();
  uINT32 floatsize = num_floats_from_num_bytes(init_bytesize) ;

  m_send_msg.set_nelems(floatsize);
  m_send_msg.settag(eMPI_SURFEL_PRELIM_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);
}

VOID sSURFEL_SEND_GROUP::send_bc_type()
{

  SURFEL_BCTYPE_SEND_ELEMENT buf_ptr = 
    reinterpret_cast<SURFEL_BCTYPE_SEND_ELEMENT> (m_send_msg.buffer());

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_send_bc_type_buffer(buf_ptr++);
  }

  asINT32 bytesize = sizeof(sSURFEL_BCTYPE_SEND_ELEMENT) * m_quantums.size();
  uINT32 send_size = num_floats_from_num_bytes(bytesize) ;

  m_send_msg.set_nelems(send_size);
  m_send_msg.settag(eMPI_SURFEL_BCTYPE_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);

}

VOID sSURFEL_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  BOOLEAN is_T_pde_on = (sim.T_solver_type == PDE_TEMPERATURE);
  // ENTROPY SOLVER not implemented yet
  // BOOLEAN is_entropy_on = (sim.T_solver_type == ENTROPY);

  sSURFEL_SEND_DATA_INFO send_data_info;
  send_data_info.send_buffer = (sdFLOAT *)m_send_msg.buffer();
  // complete the previous send if any
  complete_send();

  send_data_info.t_solver_type = t_solver_type;

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_send_buffer(sim.init_solver_mask, send_data_info);
  }

  asINT32 true_sendsize = send_data_info.send_buffer - m_send_msg.buffer();
  
  int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL>(m_scale);

  LOG_MSG("STRAND_SWITCHING").printf( "sending surfels scale %d size %d tag %x to SP %d", m_scale, true_sendsize, tag, dest_rank());

  m_send_msg.set_nelems(true_sendsize);
  m_send_msg.settag(tag);
  g_exa_sp_cp_comm.isend(m_send_msg);
  m_solver_timestep_index_mask ^= active_solver_mask;
}

ACTIVE_SOLVER_MASK prior_timestep_index_mask(asINT32 scale, asINT32 timestep, BOOLEAN is_seed) {
  ACTIVE_SOLVER_MASK timestep_index_mask = 0;
  if(is_seed)
    return timestep_index_mask;
  else {
    timestep_index_mask |= sim_prior_timestep_index(scale, g_timescale.m_lb_tm.m_time) << LB_SOLVER;
    timestep_index_mask |= sim_prior_timestep_index(scale, g_timescale.m_t_pde_tm.m_time) << T_SOLVER;
    timestep_index_mask |= sim_prior_timestep_index(scale, g_timescale.m_ke_pde_tm.m_time) << TURB_SOLVER;
    timestep_index_mask |= sim_prior_timestep_index(scale, g_timescale.m_uds_pde_tm.m_time) << UDS_SOLVER;
    timestep_index_mask |= sim_prior_timestep_index(scale, g_timescale.m_conduction_pde_tm.m_time) << CONDUCTION_SOLVER;
  }
  return timestep_index_mask;
}

VOID do_preliminary_ublk_comm(asINT32 timestep, BOOLEAN next_index, BOOLEAN is_seed)
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_post_recv_group, scale) {
     far_post_recv_group->post_recv();
     far_post_recv_group->t_solver_type = sim.T_solver_type;
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_post_recv_group, scale) {
      near_post_recv_group->post_recv();
      near_post_recv_group->t_solver_type = sim.T_solver_type;
    }

//    asINT32 prior_timestep_index = sim_prior_timestep_index(scale, timestep);
    ACTIVE_SOLVER_MASK active_solver_mask = sim.init_solver_mask;
//    ACTIVE_SOLVER_MASK last_active_solver_mask = g_timescale.m_last_active_solver_masks[scale];
    asINT32 prior_timestep_index = sim_prior_timestep_index_mask(scale, timestep);
    if(!g_timescale.m_all_solvers_have_same_timestep)
      prior_timestep_index = prior_timestep_index_mask(scale, timestep, is_seed);
    if (next_index)
      prior_timestep_index ^= active_solver_mask;
    DO_UBLK_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->t_solver_type = sim.T_solver_type;
      post_send_group->send(sim.init_solver_mask, prior_timestep_index);
    }
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_complete_recv_group, scale) {
      far_complete_recv_group->complete_recv();
      far_complete_recv_group->unpack(sim.init_solver_mask, prior_timestep_index);
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_complete_recv_group, scale) {
      near_complete_recv_group->complete_recv();
      near_complete_recv_group->unpack(sim.init_solver_mask, prior_timestep_index);
    }
  }
}

#if BUILD_5G_LATTICE
VOID do_preliminary_ublk_pore_comm()
{
  dassert(sim.is_large_pore);

  DO_SCALES_COARSE_TO_FINE(scale) {
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_post_recv_group, scale) {
      far_post_recv_group->post_init_pore_data_recv();
    }

    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_post_recv_group, scale) {
      near_post_recv_group->post_init_pore_data_recv();
    }

    DO_UBLK_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->send_init_pore_data();
    }

    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_complete_recv_group, scale) {
      far_complete_recv_group->complete_recv();
      far_complete_recv_group->unpack_init_pore_data();
    }

    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_complete_recv_group, scale) {
      near_complete_recv_group->complete_recv();
      near_complete_recv_group->unpack_init_pore_data();
    }
  
  }
}
#endif

VOID do_calibration_ublk_comm(asINT32 timestep, BOOLEAN next_index)
{
  DO_SCALES_COARSE_TO_FINE(scale) {

    asINT32 prior_timestep_index = sim_prior_timestep_index(scale, timestep);

    if (next_index)
      prior_timestep_index ^= 1;
    DO_UBLK_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->t_solver_type = sim.T_solver_type;
      post_send_group->send(sim.init_solver_mask, post_send_group->m_solver_timestep_index_mask);

    }
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_complete_recv_group, scale) {
      far_complete_recv_group->complete_recv();
      g_strand_mgr.add_to_unpacking_list(far_complete_recv_group);
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_complete_recv_group, scale) {
      near_complete_recv_group->complete_recv();
      g_strand_mgr.add_to_unpacking_list(near_complete_recv_group);
    }
  }

  g_strand_mgr.process_farblk_unpacking_list();
  g_strand_mgr.process_nearblk_unpacking_list();

  // Since we have completed all the ublk recvs, we should move
  // m_scale_sequence_timestep. This represents the time step for which the
  // posted receives are being compeleted in process_posted_receives.
  cRECV_CHANNEL *rchannel =  &g_strand_mgr.m_recv_channel[0];
  rchannel->progress_scale_sequence();
  rchannel =  &g_strand_mgr.m_recv_channel[1];
  rchannel->progress_scale_sequence();
}

static VOID do_prelim_surfel_comm_post_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->post_init_info_recv();
    group->t_solver_type = sim.T_solver_type;
  }
}

static VOID do_prelim_surfel_comm_send(SCALE scale)
{
  DO_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->t_solver_type = sim.T_solver_type;
    group->send_init_info();
  }
}

static VOID do_prelim_surfel_type_comm_send(SCALE scale)
{
  DO_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->send_surfel_type();

  }
}

static VOID do_prelim_surfel_comm_complete_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->complete_recv();
    group->unpack_init_info();
  }
}
static VOID do_prelim_surfel_type_comm_complete_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->complete_recv();
    group->unpack_surfel_type();
  }
}
VOID do_preliminary_surfel_comm()
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    // When resuming from ckpt, need to copy fringe surfel s2s data to v2s data before
    // sending the v2s data so that remote SPs receive correct v2s data for ghost surfels.
    BOOLEAN is_temp_active = (sim.init_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
    BOOLEAN is_T_S_lb_solver_on = is_temp_active && sim.is_T_S_solver_type_lb();
    BOOLEAN is_uds_active = (sim.init_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
    BOOLEAN is_uds_lb_solver_on = is_uds_active && (sim.uds_solver_type == LB_UDS);
    
    LOCAL_SFL_V2S_DATA local_v2s_data;
    auto surfel_v2s_data = &local_v2s_data.v2s_data();
        
    if (sim.is_full_checkpoint_restore) {
      DO_SURFEL_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
        BOOLEAN is_time_step_odd = sim_is_timestep_odd(scale, g_timescale.m_timestep)? TRUE : FALSE;

        BOOLEAN is_coarse_time_step_odd;
        if (scale != 0) { 
          is_coarse_time_step_odd = sim_is_timestep_odd(coarsen_scale(scale), g_timescale.m_timestep)? TRUE : FALSE;
        }

      for (const auto& quantum: post_send_group->quantums()) {
          SURFEL surfel = quantum.m_surfel;
          if (surfel->is_fringe() && surfel->has_two_copies_of_outflux()) {
            asINT32 timestep_index = -1;
            if (surfel->is_even_or_odd()) {
              if (surfel->is_even())
                timestep_index = is_time_step_odd ^ is_coarse_time_step_odd;
              else
                timestep_index = is_coarse_time_step_odd;
            } else {
              timestep_index = is_time_step_odd;
            }
            sSURFEL_V2S_LB_DATA *v2s_lb_data = surfel_v2s_data->v2s_lb_data();
            if (surfel->has_v2s_data() && !surfel->is_mirror()) {
              v2s_lb_data = surfel->v2s_lb_data();
            }
            memcpy(v2s_lb_data->m_out_flux, surfel->curr_outflux(timestep_index), sSURFEL::outflux_size);
            if (is_T_S_lb_solver_on) {
              sSURFEL_V2S_T_DATA *v2s_t_data = surfel_v2s_data->v2s_t_data();
              if (surfel->has_v2s_data() && !surfel->is_mirror()) {
                v2s_t_data = surfel->v2s_t_data();
              }
              memcpy(v2s_t_data->m_out_flux_t, surfel->curr_outflux_t(timestep_index), sSURFEL::outflux_size);
            }
	    if (is_uds_lb_solver_on) {
	      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
		sSURFEL_V2S_UDS_DATA *v2s_uds_data = surfel_v2s_data->v2s_uds_data(nth_uds);
		if (surfel->has_v2s_data() && !surfel->is_mirror()) {
		  v2s_uds_data = surfel->v2s_uds_data(nth_uds);
		}
		memcpy(v2s_uds_data->m_out_flux_uds, surfel->curr_outflux_uds(timestep_index,nth_uds), sSURFEL::outflux_size);
              }
	    }
#if BUILD_5G_LATTICE
	    if (g_is_multi_component) {
	      sSURFEL_MC_DATA *mc_data = surfel->mc_data();
	      memcpy(mc_data->out_flux, surfel->curr_outflux_mc(timestep_index), sSURFEL::outflux_size);
	    }
#endif
          }
        }
      }
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(post_recv_group, scale) {
      post_recv_group->post_recv();

    }
    DO_SURFEL_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->t_solver_type = sim.T_solver_type;
      post_send_group->send(sim.init_solver_mask);

    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(complete_recv_group, scale) {
      complete_recv_group->complete_recv();
      complete_recv_group->unpack(sim.init_solver_mask, 0);
    }
    do_prelim_surfel_comm_post_recv(scale);
    do_prelim_surfel_comm_send(scale);
    do_prelim_surfel_comm_complete_recv(scale);
  }
}

VOID do_preliminary_surfel_type_comm()
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    do_prelim_surfel_comm_post_recv(scale);
    do_prelim_surfel_type_comm_send(scale);
    do_prelim_surfel_type_comm_complete_recv(scale);
  }
}
  
static VOID pack_and_send_neighbor_ublks(SCALE scale) {
  DO_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->pack_and_send_neighbor_ublks();
  }
  DO_SAMPLING_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->pack_and_send_neighbor_ublks();
  }
}

static VOID recv_and_unpack_neighbor_ublks(SCALE scale) {
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->recv_and_unpack_neighbor_ublks();
  }
  DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->recv_and_unpack_neighbor_ublks();
  }
}

VOID do_calibration_surfel_comm()
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    DO_SURFEL_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->t_solver_type = sim.T_solver_type;
      post_send_group->send(sim.init_solver_mask);

    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(complete_recv_group, scale) {
      complete_recv_group->complete_recv();
      complete_recv_group->unpack(sim.init_solver_mask);
      complete_recv_group->reset_unpacked();
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(post_recv_group, scale) {
      post_recv_group->post_recv();
    }
  }
}

VOID comm_neighbor_ublks_for_surfels() {
  DO_SCALES_COARSE_TO_FINE(scale) {
    pack_and_send_neighbor_ublks(scale);
    recv_and_unpack_neighbor_ublks(scale);
  }
}

#if BUILD_5G_LATTICE

VOID do_surfel_potential_post_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->post_potential_recv();
  }
}


VOID sSURFEL_SEND_GROUP::send_potential() {

  SURFEL_POTENTIAL_SEND_ELEMENT buf_ptr =
      reinterpret_cast<SURFEL_POTENTIAL_SEND_ELEMENT> (m_send_msg.buffer());

  // complete the previous send if any
  complete_send();

  for (auto& quantum: m_quantums) {
    quantum.m_surfel->fill_send_potential_buffer(buf_ptr++);
  }
  asINT32 bytesize = sizeof(sSURFEL_POTENTIAL_SEND_ELEMENT) * m_quantums.size();
  uINT32 send_size = num_floats_from_num_bytes(bytesize) ;

  m_send_msg.set_nelems(send_size);
  m_send_msg.settag(eMPI_SURFEL_POTENTIAL_TAG);
  g_exa_sp_cp_comm.isend(m_send_msg);
}

VOID do_surfel_potential_send(SCALE scale)
{
  DO_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->send_potential();
  }
}

VOID sSURFEL_RECV_GROUP::unpack_potential()
{
  sdFLOAT *recv_buffer = m_recv_msg.buffer();
  SURFEL_POTENTIAL_SEND_ELEMENT ghost_surfel_init_recv_buffer =
    reinterpret_cast<SURFEL_POTENTIAL_SEND_ELEMENT>(m_recv_msg.buffer());
  for(const auto& quantum: m_quantums) {
    quantum.m_surfel->unpack_recv_potential_buffer(ghost_surfel_init_recv_buffer);
    ghost_surfel_init_recv_buffer++;
  }
}

VOID do_surfel_potential_complete_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->complete_recv();
    group->unpack_potential();
  }
}

VOID do_surfel_potential_comm()
{

  DO_SCALES_COARSE_TO_FINE(scale) {
    do_surfel_potential_post_recv(scale);
    do_surfel_potential_send(scale);
    do_surfel_potential_complete_recv(scale);
  }
}
#endif

// Order must be observed here, because for init info we use the same tag for both nearblks
// and farblks, and for all scales. For sends we do not distinguish group types, but the fset
// GROUP_ORDER is far/near.

VOID send_init_ublk_info()
{
  ccDOTIMES(scale, sim.num_scales) {
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_post_recv_group, scale) {
      far_post_recv_group->post_init_info_recv();
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_post_recv_group, scale) {
      near_post_recv_group->post_init_info_recv();
    }
  }

  ccDOTIMES(scale, sim.num_scales) {
    DO_UBLK_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->send_init_info();
    }
  }
  ccDOTIMES(scale, sim.num_scales) {
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_complete_recv_group, scale) {
      far_complete_recv_group->complete_recv();
      far_complete_recv_group->unpack_init_info();
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_complete_recv_group, scale) {
      near_complete_recv_group->complete_recv();
      near_complete_recv_group->unpack_init_info();
    }
  }

  DO_UBLK_SEND_GROUPS(complete_send_group) {
    complete_send_group->complete_send();
  }

}


VOID send_conduction_init_ublk_info()
{
  ccDOTIMES(scale, sim.num_scales) {
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_post_recv_group, scale) {
      // This if statement should be unnecessary, but leaving for now as this is called during initialization
      // and wont affect performance
      if (near_post_recv_group->m_recv_type == NEARBLK_RECV_TYPE) {
        near_post_recv_group->post_conduction_init_info_recv();
      }
    }
  }

  ccDOTIMES(scale, sim.num_scales) {
    DO_UBLK_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      if (post_send_group->m_send_type == NEARBLK_SEND_TYPE) {
        post_send_group->send_conduction_init_info();
      }
    }
  }
  ccDOTIMES(scale, sim.num_scales) {
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_complete_recv_group, scale) {
      // This if statement should be unnecessary, but leaving for now as this is called during initialization
      // and wont affect performance
      if (near_complete_recv_group->m_recv_type == NEARBLK_RECV_TYPE) {
        near_complete_recv_group->complete_recv();
        near_complete_recv_group->unpack_conduction_init_info();
      }
    }
  }

  DO_UBLK_SEND_GROUPS(complete_send_group) {
    if (complete_send_group->m_send_type == NEARBLK_SEND_TYPE) {
      complete_send_group->complete_send();
    }
  }

}

static VOID do_surfel_bc_type_send(SCALE scale)
{
  DO_SURFEL_SEND_GROUPS_OF_SCALE(group, scale) {
    group->send_bc_type();
  }
}

static VOID do_surfel_bc_type_post_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->post_bc_type_recv();

  }
}

static VOID do_surfel_bc_type_complete_recv(SCALE scale)
{
  DO_SURFEL_RECV_GROUPS_OF_SCALE(group, scale) {
    group->complete_recv();
    group->unpack_bc_types();
  }
}

VOID do_send_surfel_bc_types()
{

  DO_SCALES_COARSE_TO_FINE(scale) {
    do_surfel_bc_type_post_recv(scale);
    do_surfel_bc_type_send(scale);
    do_surfel_bc_type_complete_recv(scale);
  }

}

#if 0
// The idea here is that for a given scale there will be from 1 to 3 groups of base type
// FRINGE_SURFEL_BASE_GROUP_TYPE, and all of them share a send group; only the 
// last should have a valid send group pointer.

VOID fixup_surfel_send_group_pointers()
{
  msg_internal_error("fixup_surfel_send_group_pointers no longer used");
  ccDOTIMES(scale,sim.num_scales) {
    SURFEL_GROUP_BASE last_group = NULL;
    DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(sgroup, scale, FRINGE_SURFEL_BASE_GROUP_TYPE) {
      if (last_group != NULL && last_group->m_dest_sp.is_valid() && sgroup->m_dest_sp.is_valid()) {
        if (last_group->m_dest_sp == sgroup->m_dest_sp) {
          if (last_group->m_send_group != sgroup->m_send_group) {
            msg_internal_error("fixup_send_group_pointers: inconsistency in send group pointers");
          } else {
            last_group->m_send_group = NULL;
          }
        }
      }
      last_group = sgroup;
    }
  }
}
#endif

static sUBLK_MME_SEND_GROUP* allocate_mme_send_group(sSEND_GROUP_BASE* base_send_group)
{
  auto ublk_send_group = dynamic_cast<sUBLK_SEND_GROUP*>(base_send_group);
  cassert(ublk_send_group);
  if ( sim.is_movb_sim() ) {
    return xnew sUBLK_DYNAMIC_MME_SEND_GROUP(ublk_send_group);
  }
  else {
    return xnew sUBLK_MME_SEND_GROUP(ublk_send_group);
  }
}

asINT32 create_radiation_send_groups() {
  asINT32 n_send_groups = 0;
  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    for (auto* group: g_radiation_patches_fset.groups_of_scale(scale)) {
      group->allocate_send_buffer();
      n_send_groups++;
    }
  }
  n_send_groups++; // This is for the solve info send group
  return n_send_groups;
}

asINT32 create_mme_send_groups() {
  asINT32 nSendGroups = 0;
  if ( sim.is_mme_comm_needed() ) {
    for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
      for(auto group_type : {FRINGE_FARBLK_GROUP_TYPE,FRINGE_NEARBLK_GROUP_TYPE}) {
        DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
          if (group->m_send_group) {
            auto* mme_send_group = allocate_mme_send_group(group->m_send_group);
            group->m_mme_send_group = mme_send_group;
            mme_send_group->allocate_send_buffer();
            nSendGroups++;
          }
        }
      }
    }
  }
  return nSendGroups;
}

#if BUILD_5G_LATTICE
static sUBLK_PORE_SEND_GROUP* allocate_pore_send_group(sSEND_GROUP_BASE* base_send_group)
{
  auto ublk_send_group = dynamic_cast<sUBLK_SEND_GROUP*>(base_send_group);
  cassert(ublk_send_group);
  //not consider bsurfel cases
  return xnew sUBLK_PORE_SEND_GROUP(ublk_send_group);
}

asINT32 create_pore_send_groups() {
  asINT32 nSendGroups = 0;
  if (sim.is_large_pore) {
    for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
      for(auto group_type : {FRINGE_FARBLK_GROUP_TYPE,FRINGE_NEARBLK_GROUP_TYPE}) {
        DO_UBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
          if (group->m_send_group) {
            auto* pore_send_group = allocate_pore_send_group(group->m_send_group);
            group->m_pore_send_group = pore_send_group;
            pore_send_group->allocate_send_buffer();
            nSendGroups++;
          }
        }
      }
    }
  }
  return nSendGroups;
}
#endif

size_t allocate_send_buffers()
{

  size_t nSendGroups = 0;

  DO_UBLK_SEND_GROUPS(ugroup) {
    ugroup->allocate_send_buffer();
    nSendGroups++;
  }
  DO_SURFEL_SEND_GROUPS(sgroup) {
    sgroup->allocate_send_buffer();
    nSendGroups++;
  }
  DO_WSURFEL_SEND_GROUPS(wsgroup) {
    wsgroup->allocate_send_buffer();
    nSendGroups++;
  }
  DO_CONTACT_SEND_GROUPS(cgroup) {
    cgroup->allocate_send_buffer();
    nSendGroups++;
  }

  nSendGroups += create_mme_send_groups();
  nSendGroups += create_bsurfel_send_groups();
  nSendGroups += create_ublk_ib_bf_send_groups();

  nSendGroups += create_parcel_send_groups();

#if BUILD_5G_LATTICE
  nSendGroups += create_pore_send_groups();
#endif

  if (sim.is_radiation_model) {
    nSendGroups += create_radiation_send_groups();
  }

  return nSendGroups;
}

VOID allocate_recv_buffers()
{

  DO_FARBLK_RECV_GROUPS(fgroup) {
    fgroup->allocate_recv_buffer();
  }
  DO_NEARBLK_RECV_GROUPS(ngroup) {
    ngroup->allocate_recv_buffer();
  }
  DO_SURFEL_RECV_GROUPS(sgroup) {
    sgroup->allocate_recv_buffer();
  }
  DO_WSURFEL_RECV_GROUPS(wsgroup) {
    wsgroup->allocate_recv_buffer();
  }
  DO_CONTACT_RECV_GROUPS(cgroup) {
    cgroup->allocate_recv_buffer();
  }

  if (sim.is_radiation_model) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      for (auto* rp_group: g_radiation_patches_fset.groups_of_scale(scale)) {
        rp_group->allocate_recv_buffer();
      }
    }
  }
}
   
VOID sim_finalize_shob_comm() {
  DO_FARBLK_RECV_GROUPS(far_group) {
    far_group->cancel_pending_recv();
  }
  DO_NEARBLK_RECV_GROUPS(near_group) {
    near_group->cancel_pending_recv();
  }
  DO_SURFEL_RECV_GROUPS(surf_group) {
    surf_group->cancel_pending_recv();
  }
  DO_BSURFEL_RECV_GROUPS(bsurfel_group) {
    bsurfel_group->cancel_pending_recv();
  }
  DO_UBLK_IB_BF_RECV_GROUPS(ib_bf_group) {
    ib_bf_group->cancel_pending_recv();
  }
  DO_UBLK_IB_BF_BCAST_RECV_GROUPS(ib_bf_bcast_group) {
    ib_bf_bcast_group->cancel_pending_recv();
  }
  DO_PARCEL_RECV_GROUPS(parcel_group) {
    parcel_group->cancel_pending_recv();
  }
  DO_SURFEL_FILM_RECV_GROUPS(film_group) {
    film_group->cancel_pending_recv();
  }
  DO_WSURFEL_RECV_GROUPS(wsurfel_group) {
    wsurfel_group->cancel_pending_recv();
  }
  DO_CONTACT_RECV_GROUPS(contact_group) {
    contact_group->cancel_pending_recv();
  }
}

VOID print_send_recv_fsets() {
  std::string ofname("recv_send_grps.");
  ofname += std::to_string(my_proc_id) + ".txt";
  std::ofstream ofs(ofname.c_str());
  for( SCALE scale = 0; scale <sim.num_scales; scale++) {

    if(g_surfel_recv_fset.n_groups_of_scale(scale)>0) {
      for(sSURFEL_RECV_GROUP *const cpu_group : g_surfel_recv_fset.groups_of_scale(scale)) {
        assert(cpu_group);
        ofs << "recv group:: scale " << scale
            << ", recv_type " << cpu_group->m_recv_type
            << ", src rank " << cpu_group->m_source_sp.rank() << "\n";
        for(const sSURFEL_COMM_QUANTUM& quantum : cpu_group->quantums()) {
          ofs << "Gh sfl " << quantum.m_surfel->id() << "\n";
        }
      }
    }

    if(g_surfel_send_fset.n_groups_of_scale(scale)>0) {
      for(sSURFEL_SEND_GROUP *const cpu_group : g_surfel_send_fset.groups_of_scale(scale)) {
        assert(cpu_group);
        ofs << "send group:: scale " << scale
            << ", send_type " << cpu_group->m_send_type
            << ", dst rank " << cpu_group->m_dest_sp.rank() << "\n";
        for(const sSURFEL_COMM_QUANTUM& quantum : cpu_group->quantums()) {
          ofs << "Fr sfl " << quantum.m_surfel->id() << "\n";
        }
      }
    }
  }
  ofs.close();
}
