/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("physics.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */ 
#ifndef _SIMENG_RANDOM_H
#define _SIMENG_RANDOM_H


#include XRAND_H
#include "common_sp.h"

//This is a wrapper class for an sXRAND_GENERATOR that adds support for reading and writing checkpoints.
typedef class sRANDOM_NUM_GENERATOR : public sXRAND_GENERATOR
{
private:
public:
  sRANDOM_NUM_GENERATOR(uINT64 the_seed = 0) : sXRAND_GENERATOR(the_seed) {}
  VOID ckpt();
  VOID ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();

} *RANDOM_NUM_GENERATOR;


extern sRANDOM_NUM_GENERATOR g_random_num_generator;

#endif
