/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Voxel dynamics (functional sets and groups)
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_VOXEL_DYN_H
#define _SIMENG_VOXEL_DYN_H

#include "common_sp.h"
#include "quantum.h"
#include "group.h"
#include "fset.h"
#include "shob_dyn.h"
#include "seed_sp.h"

/* Meas cell references
 *
 * Each ublk in a voxel dyn group has an associated set of meas cell
 * references. A meas cell reference consists of the following fields:
 *
 *    meas_window
 *    meas_cell_index
 *    meas_voxel_mask
 *
 * The meas_voxel_mask indicates which voxels contribute to the measurement
 * window. For per-voxel measurements (i.e. user selected 1 voxel building
 * block for the meas window and size of voxels in dyn group are not smaller
 * than meas window cell size), the voxels in meas_voxel_mask deposit their
 * results in a sequence of meas cells, with the first identified by
 * meas_cell_index.
 *
 * For non-per-voxel meas windows (including composite meas windows), the
 * voxels in meas_voxel_mask deposit their results in a single meas cell
 * identified by meas_cell_index.
 *
 * For per-voxel meas windows, a ublk can have at most 1 meas cell reference
 * for each meas window. For other meas windows, the ublk can have multiple
 * meas cell references for a given meas window (although this is rare). For
 * a composite window, this will only occur if the voxels comprising the
 * ublk lie in different fluid regions. This will not occur if the voxels
 * lie in different porous media or fan regions because in that case, the
 * ublk will be partitioned across multiple voxel dyn groups. For
 * non-composite, non-per-voxel meas windows, this will only occur if a ublk
 * is split by a thin wall, but none of its voxels are split by the thin
 * wall.
 */

struct sFLUID_PHYSICS_DESCRIPTOR;
struct sLRF_PHYSICS_DESCRIPTOR;
/*--------------------------------------------------------------------------*
 * VOXEL_DYN_GROUP abstract type definition
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// sVOXEL_DYN_GROUP
//----------------------------------------------------------------------------
enum {
  MIRROR_TYPE,
  BASIC_FLUID_TYPE,
  SPECIAL_FLUID_TYPE,
  FAN_FLUID_TYPE,
  TABLE_FAN_FLUID_TYPE,
  POROUS_FLUID_TYPE,
  CURVED_POROUS_FLUID_TYPE,
  CURVED_HX_POROUS_FLUID_TYPE,
  CONDUCTION_SOLID_TYPE,
  INSULATOR_SOLID_TYPE,
  NUM_FLUID_TYPES,
  INVALID_FLUID_TYPE
};

typedef struct sVOXEL_DYN_GROUP : public sSHOB_DYN_GROUP {

private:
  VMEM_VECTOR < STP_VOXEL_MASK >  m_meas_voxel_masks;

protected:
  virtual UBLK_QUANTUM _quantum(asINT32 i) = 0;

public:
  // M_MEAS_WINDOW_PTRS is a vector of pointers to meas windows
  std::vector < MEAS_WINDOW_PTR >  m_meas_window_ptrs;

  cBOOLEAN is_near_surface;

  SHOB_ID total_voxels;

  STP_PHYS_VARIABLE omega_c;  

  MEAS_WINDOW_PTR *meas_window_ptrs()   { return &m_meas_window_ptrs.front(); }
  asINT32         n_meas_windows()      { return m_meas_window_ptrs.size(); }

  VOID resolve_meas_cell_ptrs();

  virtual VOID trim() { 
    sSHOB_DYN_GROUP::trim();
    m_meas_voxel_masks.trim(); 
  }
  
  UBLK_QUANTUM quantums()         { return (UBLK_QUANTUM)_quantum(0); }
  UBLK_QUANTUM quantum(asINT32 i) { return (UBLK_QUANTUM)_quantum(i); }

  // The following fields must be filled before the init method is called:
  //   physics_desc, lrf_physics_desc, scale, is_near_surface, type
  virtual VOID init() = 0;

  virtual VOID add_quantum(DGF_UBLK_BASE_DESC ublk_desc, // simple or real ublk desc
                           BOOLEAN is_real_ublk_desc,  
                           UBLK ublk, uINT8 voxel_mask) = 0;

  virtual VOID dynamics(ACTIVE_SOLVER_MASK active_solver_mask) = 0; // Dynamics function
  virtual VOID seed(BOOLEAN is_smart_seed) = 0;
  virtual VOID post_seed_init()            = 0;	// Called after seeding
  virtual BOOLEAN is_basic_fluid()         = 0;

#if BUILD_D19_LATTICE
  virtual VOID seed_T_scalar_solver()      = 0;
  virtual VOID seed_T_pde_solver()         = 0;
#endif

  sVOXEL_DYN_GROUP() : sSHOB_DYN_GROUP()
  {
    is_near_surface          = FALSE;
    total_voxels             = 0;

    if (!g_no_reserve_addr_space)
      m_meas_voxel_masks.reserve(128 * 1024);
  }

} *VOXEL_DYN_GROUP;

// These 2 loops will the walk the quantums of any voxel dyn group
#define DOTIMES_VOXEL_DYN_GROUP_QUANTUMS(quantum_var, i, group)                         \
  UBLK_QUANTUM quantum_var = (group)->quantums();                                       \
  asINT32 ___(_sizeof_quantum) = (group)->quantum_size();                               \
  asINT32 ___(_n_quantums) = (group)->n_quantums();                                     \
  asINT32 i;                                                                            \
  for (i = 0;                                                                           \
       i < ___(_n_quantums);                                                            \
       i++,                                                                             \
	 quantum_var = (UBLK_QUANTUM)((char *)quantum_var + ___(_sizeof_quantum)))

#define DO_VOXEL_DYN_GROUP_QUANTUMS(quantum_var, group)		\
  DOTIMES_VOXEL_DYN_GROUP_QUANTUMS(quantum_var, ___(i), group)

// DO_UBLK_MEAS_CELLS: iterating over the meas cell ptrs associated with a ublk in a
//                     voxel dyn group
//
// Arguments:
//
//   MEAS_CELL_VAR *meas_cell:        pointer to meas cell (i.e. a dFLOAT ptr)
//   uINT8          meas_voxel_mask:  meas cell ref voxel mask
//   MEAS_CELL_PTR *meas_cell_ptrs:   pointer to vector of pointers to meas cells
//   uINT8         *meas_voxel_masks: pointer to vector of meas cell ref voxel masks

#define DO_UBLK_MEAS_CELLS(meas_cell, meas_voxel_mask,       /* Bound by macro */       \
                           meas_cell_ptr,                    /* Bound by macro */       \
                           meas_cell_ptrs, meas_voxel_masks)  /* Inputs to macro */     \
  MEAS_CELL_VAR *meas_cell;                                                             \
  MEAS_CELL_PTR meas_cell_ptr;                                                          \
  auINT32 meas_voxel_mask;                                                              \
  BOOLEAN ___(is_another) = TRUE;                                                       \
  for ( ;                                                                               \
       ___(is_another)                                                                  \
           && (meas_cell_ptr = *meas_cell_ptrs++,                                       \
               meas_cell = meas_cell_ptr.variables(),                                   \
               meas_voxel_mask  = *meas_voxel_masks++,                                  \
               ___(is_another) = meas_cell_ptr.is_another_for_window(),                 \
               TRUE);                                                                   \
       )
  
#define DO_UBLK_MEAS_CELL_PTRS(meas_cell_ptr,   /* Bound by macro */    \
                               meas_cell_ptrs)  /* Inputs to macro */   \
  MEAS_CELL_PTR *meas_cell_ptr;                                         \
  BOOLEAN ___(is_another) = TRUE;                                       \
  for ( ;                                                               \
       ___(is_another)                                                  \
           && (meas_cell_ptr = meas_cell_ptrs++,                        \
               ___(is_another) = meas_cell_ptr->is_another_for_window(),\
               TRUE);                                                   \
       )
  
#define DO_UBLK_MEAS_CELL_PTRS_REVERSE(meas_cell_ptr,   /* Bound by macro */                            \
                                       meas_cell_ptrs,  /* Inputs to macro  - both decremented */       \
                                       /* n_meas_cell_ptrs prevents walking off front of array */       \
                                       n_meas_cell_ptrs)                                                \
  MEAS_CELL_PTR *meas_cell_ptr;                                                                         \
  BOOLEAN ___(is_another) = TRUE;                                                                       \
  for ( ;                                                                                               \
       ___(is_another)                                                                                  \
           && (meas_cell_ptr = meas_cell_ptrs--,                                                        \
               n_meas_cell_ptrs--,                                                                      \
               ___(is_another) = (n_meas_cell_ptrs > 0)                                                 \
                                 && meas_cell_ptrs->is_another_for_window(),                            \
               TRUE);                                                                                   \
       )

//----------------------------------------------------------------------------
// sVOXEL_DYN_FSET
//----------------------------------------------------------------------------

struct VOXEL_DYN_GROUP_ORDER
{
  BOOLEAN operator()(VOXEL_DYN_GROUP a, VOXEL_DYN_GROUP b) 
  {
    // This method must account for all members considered to be part of the
    // signature of a VOXEL_DYN_GROUP except for scale (which is handled specially 
    // by tSP_FSET).

    if(a->type() != b->type())
      return a->type() < b->type();

    if(a->physics_descriptor() != b->physics_descriptor())
      return a->physics_descriptor() < b->physics_descriptor();

    if (a->lrf_physics_desc != b->lrf_physics_desc)
      return a->lrf_physics_desc < b->lrf_physics_desc;

    if (a->is_near_surface != b->is_near_surface)
      return a->is_near_surface < b->is_near_surface;

    asINT32 n_windows1 = a->n_meas_windows();
    asINT32 n_windows2 = b->n_meas_windows();

    if (n_windows1 != n_windows2)
      return n_windows1 < n_windows2;

    MEAS_WINDOW_PTR *w1 = a->meas_window_ptrs();
    MEAS_WINDOW_PTR *w2 = b->meas_window_ptrs();

    for(asINT32 i=0; i<n_windows1; i++) {
      if (w1->window() != w2->window())
        return w1->window() < w2->window(); // just compare pointers
      w1++;
      w2++;
    }
    return FALSE;
  }
};

typedef class sVOXEL_DYN_FSET : public tSP_FSET < VOXEL_DYN_GROUP, VOXEL_DYN_GROUP_ORDER > {
  
public:
  // The meas_cell_refs argument to create_group must be sorted by meas window index
  VOXEL_DYN_GROUP create_group(asINT32 scale, 
                               asINT32 lrf_index, 
                               PHYSICS_DESCRIPTOR physics_desc, 
                               STP_PHYSTYPE_TYPE sim_phys_type,
                               BOOLEAN is_near_surface,
                               std::vector < MEAS_WINDOW_PTR > &meas_window_ptrs);
} *VOXEL_DYN_FSET;

extern sVOXEL_DYN_FSET g_pre_comm_voxel_dyn_fset;
extern sVOXEL_DYN_FSET g_post_comm_voxel_dyn_fset;

#define DO_PRE_COMM_VOXEL_DYN_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sVOXEL_DYN_FSET, g_pre_comm_voxel_dyn_fset, group_var)

#define DO_PRE_COMM_VOXEL_DYN_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sVOXEL_DYN_FSET, g_pre_comm_voxel_dyn_fset, group_var, scale)

#define DO_POST_COMM_VOXEL_DYN_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sVOXEL_DYN_FSET, g_post_comm_voxel_dyn_fset, group_var)

#define DO_POST_COMM_VOXEL_DYN_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sVOXEL_DYN_FSET, g_post_comm_voxel_dyn_fset, group_var, scale)


// Be careful since this macro actually expands to a pair of nested loops
#define DO_VOXEL_DYN_GROUPS(group_var) \
  printf("voxel dyn group should be replaced\n"); \
  FSET_PAIR_FOREACH_GROUP(sVOXEL_DYN_FSET, g_pre_comm_voxel_dyn_fset, g_post_comm_voxel_dyn_fset, group_var)



//----------------------------------------------------------------------------
// sFLUID_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sFLUID_PARAMETERS {
#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE nu_over_t;
#endif
  sPHYSICS_VARIABLE csys_index;
  sPHYSICS_VARIABLE ref_frame;

#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE viscous_damping_factor;
  sPHYSICS_VARIABLE water_vapor_source_term;
#endif  

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "FLUID_PARAMETERS") {
    asINT32 i = 0;
#if !BUILD_5G_LATTICE
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NU_ON_T)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
#else
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
#endif
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_FRAME)
#if !BUILD_5G_LATTICE
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_VISCOUS_DAMPING_FACTOR)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM)
#endif
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }

#if !BUILD_5G_LATTICE
  __HOST__DEVICE__ BOOLEAN has_time_varying_only_viscous_damping_factor() {return viscous_damping_factor.is_time_varying_only(); }
  __HOST__DEVICE__ BOOLEAN has_time_varying_viscous_damping_factor() {return viscous_damping_factor.is_time_varying; }
  __HOST__DEVICE__ BOOLEAN has_space_varying_viscous_damping_factor() {return viscous_damping_factor.is_space_varying; }
  __HOST__DEVICE__ BOOLEAN has_constant_viscous_damping_factor() {return viscous_damping_factor.is_constant(); }
  __HOST__DEVICE__ BOOLEAN has_sharable_viscous_damping_factor() {return viscous_damping_factor.is_sharable(); }

  __HOST__DEVICE__ VOID remap_viscous_damping_factor_to_nu_over_t() {
      dFLOAT vdf = viscous_damping_factor.value;
      if (vdf >= 0.0 && vdf <= 1.0) {
        nu_over_t.value = MAX(0.005, vdf * 0.5);
      }
  }
#endif

} *FLUID_PARAMETERS;

//----------------------------------------------------------------------------
// sFLUID_UDS_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sFLUID_UDS_PARAMETERS {
  sPHYSICS_VARIABLE uds_source_term;

  asINT32 check_consistency(asINT32 n_parameters, CDI_PHYS_TYPE_DESCRIPTOR ptd,cSTRING name = "FLUID_UDS_PARAMETERS") {
    if (n_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uptd = find_uds_physics(ptd->cdi_physics_type);
      asINT32 i = 0;
      if ((uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_FLUID_SOURCE_TERM)
	  || uptd->n_parameters < i)
	msg_internal_error("%s is not consistent with CDI UDS physics type %d.", name, uptd->uds_physics_type);
      return i;
    } else {
      return 0;
    }
  }  

} *FLUID_UDS_PARAMETERS;
  
//----------------------------------------------------------------------------
// sFLUID_PHYSICS_DESCRIPTOR
//----------------------------------------------------------------------------
typedef struct sFLUID_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  
  __HOST__DEVICE__ FLUID_PARAMETERS parameters() { return (FLUID_PARAMETERS)_parameters; }
  __HOST__DEVICE__ FLUID_INITIAL_CONDITIONS initial_conditions() { return (FLUID_INITIAL_CONDITIONS)_initial_conditions; }


  //LB_UDS
  __HOST__DEVICE__ FLUID_UDS_PARAMETERS uds_parameters() { return (FLUID_UDS_PARAMETERS)_uds_parameters; }
  __HOST__DEVICE__ FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions() { return (FLUID_UDS_INITIAL_CONDITIONS)_uds_initial_conditions; }
  __HOST__DEVICE__ FLUID_UDS_PARAMETERS uds_parameters(asINT32 nth_uds) { return (FLUID_UDS_PARAMETERS)_uds_parameters + nth_uds; }
  __HOST__DEVICE__ FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions(asINT32 nth_uds) { return (FLUID_UDS_INITIAL_CONDITIONS)_uds_initial_conditions + nth_uds; }
                  
  VOID check_consistency() {
    parameters()->check_consistency(phys_type_desc);
    initial_conditions()->check_consistency(phys_type_desc);
    uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);
    uds_initial_conditions()->check_consistency(n_uds_initial_conditions, phys_type_desc);    
  }

} *FLUID_PHYSICS_DESCRIPTOR;

typedef struct sLES_FLUID_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  
  __HOST__DEVICE__ FLUID_PARAMETERS parameters() { return (FLUID_PARAMETERS)_parameters; }
  __HOST__DEVICE__ LES_FLUID_INITIAL_CONDITIONS initial_conditions() { return (LES_FLUID_INITIAL_CONDITIONS)_initial_conditions; }

  
  //LB_UDS
  __HOST__DEVICE__ FLUID_UDS_PARAMETERS uds_parameters() { return (FLUID_UDS_PARAMETERS)_uds_parameters; }
  __HOST__DEVICE__ FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions() { return (FLUID_UDS_INITIAL_CONDITIONS)_uds_initial_conditions; }
    
  VOID check_consistency() {
    parameters()->check_consistency(phys_type_desc);
    initial_conditions()->check_consistency(phys_type_desc);
    uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);
    uds_initial_conditions()->check_consistency(n_uds_initial_conditions, phys_type_desc); 
  }

} *LES_FLUID_PHYSICS_DESCRIPTOR;

//----------------------------------------------------------------------------
// sSPECIAL_FLUID_UDS_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sSPECIAL_FLUID_UDS_PARAMETERS {
  sPHYSICS_VARIABLE uds_source_term;
  sPHYSICS_VARIABLE effective_diffusivity;

  asINT32 check_consistency(asINT32 n_parameters, CDI_PHYS_TYPE_DESCRIPTOR ptd,cSTRING name = "SPECIAL_FLUID_UDS_PARAMETERS") {
    if (n_parameters > 0) {
      CDI_UDS_PHYS_TYPE_DESCRIPTOR uptd = find_uds_physics(ptd->cdi_physics_type);
      asINT32 i = 0;
      if ((uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_FLUID_SOURCE_TERM)
	  || (uptd->parameter_var[i++]->id != CDI_VAR_ID_UDS_FLUID_DIFFUSIVITY)
	  || uptd->n_parameters < i)
	msg_internal_error("%s is not consistent with CDI UDS physics type %d.", name, uptd->uds_physics_type);
      return i;
    } else {
      return 0;
    }
  }    
  
} *SPECIAL_FLUID_UDS_PARAMETERS;
  
  
typedef struct sPOROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE nu_over_t;
  sPHYSICS_VARIABLE is_curved;
  sPHYSICS_VARIABLE raxis[3];
  sPHYSICS_VARIABLE gaxis[3];
  sPHYSICS_VARIABLE csys_index;
  sPHYSICS_VARIABLE ref_frame;
  sPHYSICS_VARIABLE resist_ref_temp;
  sPHYSICS_VARIABLE r0[3];
  sPHYSICS_VARIABLE r1[3];
#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE water_vapor_source_term;
#endif
  sPHYSICS_VARIABLE is_pm_leakage_model;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "POROUS_MEDIA_PARAMETERS") {
    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NU_ON_T)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CURVED_POROUS_MEDIUM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_X)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Y)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Z)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_FRAME)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RESISTANCE_REF_TEMP)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES0_R)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES0_G)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES0_B)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES1_R)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES1_G)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RES1_B)
#if !BUILD_5G_LATTICE
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM)
#endif
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_PM_LEAKAGE_RESOLUTION)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }
#if !BUILD_5G_LATTICE
  __HOST__DEVICE__ BOOLEAN has_time_varying_only_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_time_varying_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_space_varying_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_constant_viscous_damping_factor() {return TRUE; }
  __HOST__DEVICE__ BOOLEAN has_sharable_viscous_damping_factor() {return TRUE; }
  __HOST__DEVICE__ VOID remap_viscous_damping_factor_to_nu_over_t() { }
#endif

} *POROUS_MEDIA_PARAMETERS;

#define define_physics_descriptor(NAME, LES)			                 \
  typedef struct s ## LES ## NAME ## _PHYSICS_DESCRIPTOR :                       \
                        public sPHYSICS_DESCRIPTOR {                             \
    									         \
    __HOST__DEVICE__ NAME ## _PARAMETERS parameters()			         \
    { return (NAME ## _PARAMETERS)_parameters; }			         \
    __HOST__DEVICE__ LES ## FLUID_INITIAL_CONDITIONS initial_conditions()        \
    { return (LES ## FLUID_INITIAL_CONDITIONS)_initial_conditions; }             \
    									         \
    __HOST__DEVICE__ SPECIAL_FLUID_UDS_PARAMETERS uds_parameters()               \
    { return (SPECIAL_FLUID_UDS_PARAMETERS)_uds_parameters; }                    \
    __HOST__DEVICE__ FLUID_UDS_INITIAL_CONDITIONS uds_initial_conditions()       \
    { return (FLUID_UDS_INITIAL_CONDITIONS)_uds_initial_conditions; }            \
                                                                                 \
    VOID check_consistency()  {						         \
      parameters()->check_consistency(phys_type_desc);                           \
      initial_conditions()->check_consistency(phys_type_desc);                   \
      uds_parameters()->check_consistency(n_uds_parameters, phys_type_desc);                      \
      uds_initial_conditions()->check_consistency(n_uds_initial_conditions, phys_type_desc);      \
    }		                                                                 \
} * LES ## NAME ## _PHYSICS_DESCRIPTOR

define_physics_descriptor(POROUS_MEDIA, );
define_physics_descriptor(POROUS_MEDIA, LES_);


typedef struct sFIXED_TEMP_POROUS_MEDIA_PARAMETERS : sPOROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE ref_temp;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "FIXED_TEMP_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sPOROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);
 
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_REF_TEMP
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);	
    return i;
  }

} *FIXED_TEMP_POROUS_MEDIA_PARAMETERS; 

define_physics_descriptor(FIXED_TEMP_POROUS_MEDIA, );
define_physics_descriptor(FIXED_TEMP_POROUS_MEDIA, LES_);

typedef struct sHEAT_FLUX_POROUS_MEDIA_PARAMETERS : sPOROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE heating_coeff;		// Volumetric heating coefficient 
  sPHYSICS_VARIABLE ref_temp;
  sPHYSICS_VARIABLE q0;				// Constant volumetric heat source
  sPHYSICS_VARIABLE is_dyn_heating_rate;
  sPHYSICS_VARIABLE Kc;
  sPHYSICS_VARIABLE tdh;
  sPHYSICS_VARIABLE Dh;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "HEAT_FLUX_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sPOROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);
 
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_HEAT_COEF)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_REF_TEMP)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_Q0)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_USE_DYN_HEAT_RATE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF1)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF2)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);	
    return i;
  }

} *HEAT_FLUX_POROUS_MEDIA_PARAMETERS; 

define_physics_descriptor(HEAT_FLUX_POROUS_MEDIA, );
define_physics_descriptor(HEAT_FLUX_POROUS_MEDIA, LES_);

typedef struct sCURVED_HX_HEAT_FLUX_POROUS_MEDIA_PARAMETERS : sHEAT_FLUX_POROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE curved_hx_table_index;
  sPHYSICS_VARIABLE meas_window_index;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CURVED_HX_HEAT_FLUX_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sHEAT_FLUX_POROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);

#if BUILD_GPU
    msg_error("Curved HX are not supported on GPU");
#endif
 
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CURVED_HX_TABLE_INDEX)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CURVED_HX_MEAS_WINDOW_INDEX)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);	
    return i;
  }

} *CURVED_HX_HEAT_FLUX_POROUS_MEDIA_PARAMETERS; 

define_physics_descriptor(CURVED_HX_HEAT_FLUX_POROUS_MEDIA, );
define_physics_descriptor(CURVED_HX_HEAT_FLUX_POROUS_MEDIA, LES_);

//
// ACOUSTIC POROUS MEDIA PARAMETERS
typedef struct sACOUSTIC_POROUS_MEDIA_PARAMETERS : sPOROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE porosity;
  sPHYSICS_VARIABLE tortuosity;
  sPHYSICS_VARIABLE use_adv_fluid;
  sPHYSICS_VARIABLE viscous_res;
  sPHYSICS_VARIABLE inertial_res;
  sPHYSICS_VARIABLE reactance_coeff;
  sPHYSICS_VARIABLE surface_porosity;
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "ACOUSTIC_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sPOROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_POROSITY)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_TORTUOSITY)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_USE_ADV_FLUID)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_VISCOUS_RES)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_INERTIAL_RES)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_REACTANCE_COEFF)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_APM_SURFACE_POROSITY)
        || (ptd->n_continuous_dp < i))
      msg_internal_error("%s is not consistent with CDI physics type %d.", name, ptd->cdi_physics_type);
    return i;
  }
} *ACOUSTIC_POROUS_MEDIA_PARAMETERS;

define_physics_descriptor(ACOUSTIC_POROUS_MEDIA, );
define_physics_descriptor(ACOUSTIC_POROUS_MEDIA, LES_);

#ifdef APM_WITH_HEAT_TRANSFER
typedef struct sFIXED_TEMP_ACOUSTIC_POROUS_MEDIA_PARAMETERS : sACOUSTIC_POROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE ref_temp;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "FIXED_TEMP_ACOUSTIC_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sACOUSTIC_POROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);
 
    if (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_REF_TEMP
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);	
    return i;
  }

} *FIXED_TEMP_ACOUSTIC_POROUS_MEDIA_PARAMETERS; 

define_physics_descriptor(FIXED_TEMP_ACOUSTIC_POROUS_MEDIA, );
define_physics_descriptor(FIXED_TEMP_ACOUSTIC_POROUS_MEDIA, LES_);

typedef struct sHEAT_FLUX_ACOUSTIC_POROUS_MEDIA_PARAMETERS : sACOUSTIC_POROUS_MEDIA_PARAMETERS {
  sPHYSICS_VARIABLE heating_coeff;		// Volumetric heating coefficient 
  sPHYSICS_VARIABLE ref_temp;
  sPHYSICS_VARIABLE q0;				// Constant volumetric heat source
  sPHYSICS_VARIABLE is_dyn_heating_rate;
  sPHYSICS_VARIABLE Kc;
  sPHYSICS_VARIABLE tdh;
  sPHYSICS_VARIABLE Dh;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "HEAT_FLUX_ACOUSTIC_POROUS_MEDIA_PARAMETERS") {
    asINT32 i = sACOUSTIC_POROUS_MEDIA_PARAMETERS::check_consistency(ptd, name);
 
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_HEAT_COEF)
      	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_REF_TEMP)
      	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POROUS_Q0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_USE_DYN_HEAT_RATE)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF0)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF1)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DYN_HEAT_RATE_COEFF2)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.", 
			 name, ptd->cdi_physics_type);	
    return i;
  }

} *HEAT_FLUX_ACOUSTIC_POROUS_MEDIA_PARAMETERS; 

define_physics_descriptor(HEAT_FLUX_ACOUSTIC_POROUS_MEDIA, );
define_physics_descriptor(HEAT_FLUX_ACOUSTIC_POROUS_MEDIA, LES_);
#endif
//
// FAN PHYSICS DESCRIPTOR
// 
typedef struct sBASE_FAN_PARAMETERS {
  sPHYSICS_VARIABLE nu_over_t;
  sPHYSICS_VARIABLE csys_index;
  sPHYSICS_VARIABLE ref_frame;
#if !BUILD_5G_LATTICE
  sPHYSICS_VARIABLE water_vapor_source_term;
#endif

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "BASE_FAN_PARAMETERS") {
    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_NU_ON_T)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_REF_FRAME)
#if !BUILD_5G_LATTICE
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_WATER_VAPOR_SOURCE_TERM)
#endif
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.",
			 name, ptd->cdi_physics_type);
    return i;
  }

#if !BUILD_5G_LATTICE
  __HOST__DEVICE__ BOOLEAN has_time_varying_only_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_time_varying_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_space_varying_viscous_damping_factor() {return FALSE; }
  __HOST__DEVICE__ BOOLEAN has_constant_viscous_damping_factor() {return TRUE; }
  __HOST__DEVICE__ BOOLEAN has_sharable_viscous_damping_factor() {return TRUE; }
  __HOST__DEVICE__ VOID remap_viscous_damping_factor_to_nu_over_t() { }
#endif

} *BASE_FAN_PARAMETERS;


typedef struct sFAN_PARAMETERS : public sBASE_FAN_PARAMETERS {
  sPHYSICS_VARIABLE axial_force_coeffs[4];
  sPHYSICS_VARIABLE tangential_vel_coeffs[4];

  /* Some old CDI files lack a coord system in the fan descriptor */
  sPHYSICS_VARIABLE origin[3];
  sPHYSICS_VARIABLE raxis[3];
  sPHYSICS_VARIABLE gaxis[3];

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "FAN_PARAMETERS") {
    asINT32 i = sBASE_FAN_PARAMETERS::check_consistency(ptd, name);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_R_FORCE_COEFF0)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_R_FORCE_COEFF1)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_R_FORCE_COEFF2)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_R_FORCE_COEFF3)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_U_THETA_COEFF0)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_U_THETA_COEFF1)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_U_THETA_COEFF2)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_U_THETA_COEFF3)
	
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_POINT_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_RAXIS_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Z)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.",
			 name, ptd->cdi_physics_type);
    return i;
  }

} *FAN_PARAMETERS;

define_physics_descriptor(FAN, );
define_physics_descriptor(FAN, LES_);

typedef struct sTABLE_FAN_PARAMETERS : public sBASE_FAN_PARAMETERS {
  sPHYSICS_VARIABLE mean_density;
  sPHYSICS_VARIABLE fan_length;
  sPHYSICS_VARIABLE tang_vel_model;
  sPHYSICS_VARIABLE fan_radius;
  sPHYSICS_VARIABLE hub_radius;
  sPHYSICS_VARIABLE angular_vel;
  sPHYSICS_VARIABLE tang_vel_coeff0;
  sPHYSICS_VARIABLE tang_vel_thrust_coeff;

  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "TABLE_FAN_PARAMETERS") {
    asINT32 i = sBASE_FAN_PARAMETERS::check_consistency(ptd, name);
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_DENSITY_T0)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FAN_LENGTH)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TANG_VEL_MODEL)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FAN_RADIUS)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_FAN_HUB_RADIUS)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANG_VEL)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TANG_VEL_COEFF0)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_TANG_VEL_THRUST_COEFF)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.",
			 name, ptd->cdi_physics_type);
    return i;
  }

} *TABLE_FAN_PARAMETERS;

define_physics_descriptor(TABLE_FAN, );
define_physics_descriptor(TABLE_FAN, LES_);

//----------------------------------------------------------------------------
// sCONDUCTION_SOLID_PARAMETERS
//----------------------------------------------------------------------------
typedef struct sCONDUCTION_SOLID_PARAMETERS {
  sPHYSICS_VARIABLE material_index;
  sPHYSICS_VARIABLE csys_index;
  sPHYSICS_VARIABLE anisotropy_use_part_axis;
  sPHYSICS_VARIABLE axis[3];
  sPHYSICS_VARIABLE imposed_heat; 
  sPHYSICS_VARIABLE compute_radiation;
  sPHYSICS_VARIABLE min_design_temp;
  sPHYSICS_VARIABLE max_design_temp;
  sPHYSICS_VARIABLE specify_heat_via_power_density;
  sPHYSICS_VARIABLE imposed_heat_in_power;
  // These two fields below are not in CDI
  sPHYSICS_VARIABLE total_simulated_volume;
  sPHYSICS_VARIABLE scaled_volume_fraction;
  asINT32 check_consistency(CDI_PHYS_TYPE_DESCRIPTOR ptd, cSTRING name = "CONDUCTION_SOLID_PARAMETERS") {
    asINT32 i = 0;
    if ((ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SOLID_MATERIAL)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_COORD_SYSTEM)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_ANISOTROPY_USE_PART_AXIS)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_X)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Y)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_GAXIS_Z)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SOLID_COMPUTE_RADIATION)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MIN_DESIGN_TEMP)
	|| (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_MAX_DESIGN_TEMP)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_SPECIFY_HEAT_VIA_POWER_DENSITY)
        || (ptd->continuous_dp_var[i++]->id != CDI_VAR_ID_CONDUCTOR_IMPOSED_HEAT_IN_POWER)
        || ptd->n_continuous_dp < i)
      msg_internal_error("%s is not consistent with CDI physics type %d.",
			 name, ptd->cdi_physics_type);
    return i;
  }

} *CONDUCTION_SOLID_PARAMETERS;

//----------------------------------------------------------------------------
// sCONDUCTION_PHYSICS_DESCRIPTOR
//----------------------------------------------------------------------------
typedef struct sCONDUCTION_PHYSICS_DESCRIPTOR : public sPHYSICS_DESCRIPTOR {
  CONDUCTION_SOLID_PARAMETERS parameters() { return (CONDUCTION_SOLID_PARAMETERS)_parameters; }
  CONDUCTION_INITIAL_CONDITIONS initial_conditions() { return (CONDUCTION_INITIAL_CONDITIONS)_initial_conditions; }
  
  //CHECK: This might be a problem - cannot check consistency till a new descriptor is defined in CDI
  VOID check_consistency() {
    parameters()->check_consistency(phys_type_desc);
    initial_conditions()->check_consistency(phys_type_desc);
  }

} *CONDUCTION_PHYSICS_DESCRIPTOR;

typedef enum {
  MEAN_AXIAL_VEL_POLYNOMIAL_FAN,
  LOCAL_AXIAL_VEL_POLYNOMIAL_FAN,
  MEAN_AXIAL_VEL_TABLE_FAN,
  POROUS_MEDIA,
  BASIC_FLUID
} BODY_FORCE_VOXEL_TYPE;

typedef enum {
  ADIABATIC_POROUS_MEDIA = 0,
  FIXED_TEMP_POROUS_MEDIA,
  HEAT_FLUX_POROUS_MEDIA,
  ADIABATIC_ACOUSTIC_POROUS_MEDIA,
  FIXED_TEMP_ACOUSTIC_POROUS_MEDIA,
  HEAT_FLUX_ACOUSTIC_POROUS_MEDIA,
  CURVED_HX_HEAT_FLUX_POROUS_MEDIA
} POROUS_MEDIA_SUBTYPE;

typedef enum {
  ISOTROPIC_CONDUCTION = 0,
  ANISOTROPIC_CONDUCTION
} CONDUCTION_SUBTYPE;

typedef struct sRGB_COORD_SYS {
  /* Primary coordinate system in which to apply body force (e.g. within porous media). 
   * The 3 axis are enumerated as RGB. */
  STP_GEOM_VARIABLE			raxis[3];
  STP_GEOM_VARIABLE			gaxis[3];
  STP_GEOM_VARIABLE			baxis[3];
} *RGB_COORD_SYS;

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/
VOID finish_init_of_voxel_dyn_groups(VOID);
VOID compute_ublk_ls_grad_coefficients(VOID);

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/
VOID post_seed_init_for_all_ublks();

VOID swap_states_for_pde_advect_ublks(SCALE scale);

inline VOID compute_fan_local_theta(STP_GEOM_VARIABLE voxel_centroid[3],
                                    dFLOAT fan_origin[3],
                                    dFLOAT raxis[3],
                                    dFLOAT gaxis[3],
                                    STP_GEOM_VARIABLE &local_sin_th_return,	
                                    STP_GEOM_VARIABLE &local_cos_th_return,
				    STP_GEOM_VARIABLE &dist_to_axis)
{
  dFLOAT e[3];
  dFLOAT b[3];
  dFLOAT a[3];
  dFLOAT baxis[3];
      
  // e is vector from fan origin to voxel centroid
  e[0] = voxel_centroid[0] - fan_origin[0];
  e[1] = voxel_centroid[1] - fan_origin[1];
  e[2] = voxel_centroid[2] - fan_origin[2];

  // b is the projection of e on the axis of the fan
  dFLOAT e_dot_raxis = vdot(e, raxis);
  b[0] = e_dot_raxis * raxis[0];
  b[1] = e_dot_raxis * raxis[1];
  b[2] = e_dot_raxis * raxis[2];

  // a is the shortest vector from fan axis to voxel centroid
  a[0] = e[0] - b[0];
  a[1] = e[1] - b[1];
  a[2] = e[2] - b[2];

  if ((a[0] == 0) && (a[1] == 0) && (a[2] == 0)) {
    // We're on the fan axis, so theta is undefined. We drop in 10 as the sin and
    // cos values so that the simulator will know to do something special.
    local_cos_th_return = 10;
    local_sin_th_return = 10;

    dist_to_axis = 0;
  } 
  else {
    vcross(baxis, raxis,gaxis);
    dFLOAT theta = atan2(vdot(a,baxis), vdot(a,gaxis));

    dFLOAT cos_th = cos(theta);
    dFLOAT sin_th = sin(theta);

    local_cos_th_return = cos_th;
    local_sin_th_return = sin_th;

    dist_to_axis = sqrt(vdot(a,a));
  }
}

//The namespace mangling is needed since these functions are used in SimSizes
inline namespace SIMULATOR_NAMESPACE {
asINT32 dynamics_data_type_from_cdi_type(STP_PHYSTYPE_TYPE phys_type);  
STP_PHYSTYPE_TYPE proxy_cdi_type_from_dynamics_data_type(asINT32 fluid_dynamics_type);  
}

VOID reinitialize_dynamics_data_for_dependent_ublks(PHYSICS_DESCRIPTOR pd);
  
#endif	/* #ifndef _SIMENG_VOXEL_DYN_H */



