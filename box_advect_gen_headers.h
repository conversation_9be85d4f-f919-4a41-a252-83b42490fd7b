/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Mukul Rao, Exa Corporation                      Created April, 2018
//----------------------------------------------------------------------------
//
#ifndef SIMENG_BOX_ADVECT_GEN_HEADERS_H_
#define SIMENG_BOX_ADVECT_GEN_HEADERS_H_
#include "common_sp.h"
#include <vector>
#include <tuple>

//=============================================================================
// For a given neighboring UBLK, advection contributions can be captured as
// a unique combination of source and destination voxel along a certain
// lattice direction. This data is stored in a list for every ublk offset
typedef struct {
  asINT32 latvec;
  asINT32 src_voxel;
  asINT32 dst_voxel;
} sNEIGHBOR_CONTRIBUTION;

typedef std::tuple<asINT32,asINT32,asINT32> UBLK_OFFSET_TUPLE;

//Each neighbor ublk along an offset contributes to an advecting
//block along more than one lattice direction. These contributions
//are captured as a map of ublock offsets as keys, and a vector of
//contribution elements as values


typedef std::map<UBLK_OFFSET_TUPLE,std::vector<sNEIGHBOR_CONTRIBUTION>> UBLK_OFFSET_CONTRIBUTIONS_MAP;

VOID compute_ublk_neighbor_contributions(UBLK_OFFSET_CONTRIBUTIONS_MAP& ublk_offset_map,
                                         BOOLEAN is_2D,
                                         STP_LATTICE_TYPE ltype);

INLINE
VOID walk_neighbor_offset_contributions_to_next_latvec(std::vector<sNEIGHBOR_CONTRIBUTION>::iterator& start_iterator,
                                                       std::vector<sNEIGHBOR_CONTRIBUTION>::iterator& next_iterator,\
                                                       const std::vector<sNEIGHBOR_CONTRIBUTION>::iterator& end_iterator) {
  while ((next_iterator != end_iterator) && (next_iterator->latvec == start_iterator->latvec)) {
    next_iterator++;
  }
}

#define FILL_PREV_STATES_FOR_NEIGHBOR_UBLK                                                                       \
  src_states_from_tagged_ublk<ADVECT_TEMP, ADVECT_UDS>(pde_advect_ublk, src_tagged_ublk, (uINT64 *) (&src_states),	\
						       (uINT64 *) (&src_states_t), (uINT64 *) (&src_states_mc), src_states_uds,  is_timestep_even, \
						       prior_solver_index_mask, active_solver_mask); \

#define PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES                                                              \
if (src_tagged_ublk_has_two_copies){                                                                             \
  src_states_from_tagged_ublk<ADVECT_TEMP, ADVECT_UDS>(pde_advect_ublk, src_tagged_ublk, \
						       (uINT64 *)(&curr_src_states), \
						       (uINT64 *)(&curr_src_states_t), \
						       (uINT64 *)(&curr_src_states_mc),	\
						       curr_src_states_uds, \
						       is_timestep_even, prior_solver_index_mask, \
						       active_solver_mask, \
						       box_advect_states_index::curr_src_states); \
                                                                                                                 \
                                                                                                                 \
  push_states_onto_neighbor_with_two_copies<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels, src_voxels, dest_voxels, \
								     curr_src_states, curr_src_states_t, curr_src_states_mc, curr_src_states_uds,\
								     states, states_t, states_mc, states_uds,\
								     latvec); \
}

#define APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC                                                             \
  append_v2v_contribution<IS_VR_FINE, ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, pde_advect_ublk, dest_voxels, src_voxels, \
									states, states_t, states_mc, states_uds, src_states, src_states_t, \
									src_states_mc, src_states_uds, p_states, vrf_pas_factors, pas_factors, \
									latvec, use_vrf_pas); \

#define BOX_ADVECT_TEMPLATE_PARAMETERS  BOOLEAN IS_VR_FINE, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V
#define SWAP_ADVECT_TEMPLATE_PARAMETERS BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_TWO_COPY_NBR

#define COLLECT_ADVECT_STATES_PARAMS sUBLK *pde_advect_ublk, \
                                     UBLK (&advect_nbrs) [N_MOVING_STATES],\
                                     BOOLEAN is_timestep_even, \
                                     VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS],\
                                     VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],\
                                     const sBOX_ACCESS& box_access,\
                                     SOLVER_INDEX_MASK prior_solver_index_mask,\
                                     ACTIVE_SOLVER_MASK active_solver_mask,\
                                     STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],\
                                     STP_GEOM_VARIABLE pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],\
                                     BOOLEAN use_vrf_pas

#define SWAP_ADVECT_STATES_PARAMS   sUBLK *pde_advect_ublk, \
                                    UBLK (&advect_nbrs) [N_MOVING_STATES],\
                                    BOOLEAN is_timestep_even, \
                                    VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],\
                                    VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],\
                                    VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],\
                                    VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],\
                                    const sBOX_ACCESS& box_access,\
                                    SOLVER_INDEX_MASK prior_solver_index_mask,\
                                    ACTIVE_SOLVER_MASK active_solver_mask

#define PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS                                               \
if (src_tagged_ublk.ublk()->does_advect_through_swap()){                                   \
  push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels, \
							       p_states,p_states_t,p_states_mc,	p_states_uds,\
							       src_states,src_states_t,src_states_mc, src_states_uds,\
							       latvec);	\
}

#define PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT                                               \
if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){                                   \
  push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels, \
							       p_states,p_states_t,p_states_mc,p_states_uds,\
							       src_states,src_states_t,src_states_mc, src_states_uds,\
							       latvec);	\
}


#define SWAP_VOXEL_STATES_WITH_NEIGHBOR                                                    \
  swap_voxel_states<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels, dest_voxels,		\
					     src_voxels, states, states_t, states_mc, states_uds,\
					     src_states, src_states_t, src_states_mc, src_states_uds,\
					     latvec,src_tagged_ublk_has_two_copies);


#define SPLIT_ADVECT_TEMPLATE_PARAMETERS BOOLEAN IS_VR_FINE, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V

#define SPLIT_ADVECT_STATES_PARAMS sUBLK *pde_advect_ublk, \
                                   const sSPLIT_ADVECT_FACTORS* split_advect_factor,\
                                   VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS], \
                                   VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS],\
                                   VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],\
                                   const sBOX_ACCESS& box_access,\
                                   SOLVER_INDEX_MASK prior_solver_index_mask,\
                                   ACTIVE_SOLVER_MASK active_solver_mask,\
                                   STP_GEOM_VARIABLE vrf_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],\
                                   STP_GEOM_VARIABLE pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],\
                                   BOOLEAN use_vrf_pas,\
                                   BOOLEAN is_timestep_even

#define APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC \
append_v2v_split_contribution<IS_VR_FINE, ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(pde_advect_ublk, src_tagged_ublk, split_advect_factor, \
									    latvec, dest_voxels, src_voxels, n_src_voxels, \
									    prior_solver_index_mask, active_solver_mask, is_timestep_even, \
									    states, states_t, states_mc, states_uds, p_states, \
									    vrf_pas_factors, pas_factors, use_vrf_pas);


#define SWAP_V2V_VECTORIZED \
  swap_voxel_states_v<latvec, src_voxel_mask, dst_voxel_mask, ADVECT_TEMP, ADVECT_UDS, HAS_TWO_COPY_NBR> \
  (src_ublk,pde_advect_ublk, \
   src_perm_ind,dst_perm_ind, \
   voxor, \
   prev_lb_index,prev_t_index, prev_uds_index,\
   src_tagged_ublk_has_two_copies);

#define APPEND_V2V_VECTORIZED \
  append_voxel_states_v<latvec, src_voxel_mask, dst_voxel_mask, IS_VR_FINE, ADVECT_TEMP, ADVECT_UDS, HAS_S2V> \
  (src_ublk,pde_advect_ublk, \
   src_perm_ind,dst_perm_ind, \
   voxor, prev_lb_index,prev_t_index, prev_uds_index,\
   src_tagged_ublk_has_two_copies, use_vrf_pas, vrf_pas_factors, pas_factors);

#endif /* SIMENG_BOX_ADVECT_GEN_HEADERS_H_ */
