/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#include "t_solver_data.h"
#include "ublk.h"
#include "sim.h"
#include "surfel.h"
#include "sampling_surfel.h"

template<>
VOID sSURFEL_T_DATA::pre_advect_init() {
  temp_sample = 0;
  if (sim.is_T_S_solver_type_lb()) {
    entropy_sample = 0;
  }
}

template<>
VOID sSURFEL_T_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_T_DATA mirror_t_data, 
                                                               STP_LATVEC_MASK latvec_state_mask,
                                                               //STP_STATE_INDEX reflected_states[N_STATES],
                                                               STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                               STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  temp_sample += mirror_t_data->temp_sample;
  if (sim.T_solver_type == PDE_ENTROPY) {
    entropy_sample += mirror_t_data->entropy_sample;
#if BUILD_D39_LATTICE
    ccDOTIMES(axis, N_AXES) {
      hyb_force[axis] += mirror_t_data->hyb_force[axis] * velocity_mirror_sign_factor[axis];
    }
#endif // BUILD_D39_LATTICE
  }
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  else if (sim.T_solver_type == LB_ENTROPY || sim.T_solver_type == LB_ENERGY) {
    entropy_sample += mirror_t_data->entropy_sample;
  }
#endif
}

template<>
VOID sSURFEL_T_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_T_DATA source_t_data, 
                                                               STP_LATVEC_MASK latvec_state_mask,
                                                               //STP_STATE_INDEX reflected_states[N_STATES],
                                                               STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                               STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES])
{
  heat_bc = source_t_data->heat_bc;
  temp_bc = source_t_data->temp_bc;
  temp_sample = source_t_data->temp_sample;
  turb_heat_flux = source_t_data->turb_heat_flux;
  heat_flux = source_t_data->heat_flux;
  heat_flux_prime = source_t_data->heat_flux_prime;
  if (sim.T_solver_type == PDE_ENTROPY) {
    entropy_sample = source_t_data->entropy_sample;
#if BUILD_D39_LATTICE
    ccDOTIMES(axis, N_AXES) {
      hyb_force[axis] = source_t_data->hyb_force[axis] * velocity_mirror_sign_factor[axis];
    }
#endif // BUILD_D39_LATTICE
  }
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
  else if (sim.T_solver_type == LB_ENTROPY || sim.T_solver_type == LB_ENERGY) {
    entropy_sample = source_t_data->entropy_sample;
  }
#endif
}

VOID sSAMPLING_SURFEL_T_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_T_DATA even_surfel_t_data)
{
  memcpy(this, even_surfel_t_data, sizeof(*this));
}


#ifdef BUILD_GPU

inline namespace SIMULATOR_NAMESPACE {

INIT_MSFL(tSURFEL_V2S_T_DATA) {
#if BUILD_6X_SOLVER
  VEC_COPY_SFL_TO_MSFL(m_T_bar, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(m_in_states_t, N_SURFEL_PGRAM_VOLUMES); 
  COPY_SFL_TO_MSFL(m_kappa);
  COPY_SFL_TO_MSFL(m_T0);    // Used by PDE_T, PDE_entropy, LB_entropy solvers
  COPY_SFL_TO_MSFL(m_S0);    // Used by LB_entropy solver
#else   //5X, 39s, and 5g
  VEC_COPY_SFL_TO_MSFL(m_T_bar, N_SURFEL_PGRAM_VOLUMES);
  VEC_COPY_SFL_TO_MSFL(m_in_states_t, N_SURFEL_PGRAM_VOLUMES); 
#endif

  COPY_SFL_TO_MSFL(m_heat_flux_cross_lrf);
  VEC_COPY_SFL_TO_MSFL(m_out_flux_t, N_SURFEL_PGRAM_VOLUMES);  
}

INIT_MSFL(tSURFEL_T_DATA) {
  COPY_SFL_TO_MSFL(turb_heat_flux);
  COPY_SFL_TO_MSFL(temp_sample);
  COPY_SFL_TO_MSFL(entropy_sample);
  COPY_SFL_TO_MSFL(heat_bc);
  COPY_SFL_TO_MSFL(temp_bc);
  COPY_SFL_TO_MSFL(temp_sample_prime);
#if BUILD_6X_SOLVER
  COPY_SFL_TO_MSFL(entropy_sample_prime);
#endif
  COPY_SFL_TO_MSFL(un_wall_temp);
#if BUILD_6X_SOLVER
  COPY_SFL_TO_MSFL(un_t_p);
#else
  COPY_SFL_TO_MSFL(un_rho_p);
#endif

#if BUILD_D39_LATTICE
  VEC_COPY_SFL_TO_MSFL(hyb_force, 3); // hybrid force sampled from the voxels
#endif // BUILD_D39_LATTICE

  COPY_SFL_TO_MSFL(init_ht); //momentum freeze
  COPY_SFL_TO_MSFL(init_mt); //momentum freeze
 
  //for new TWF of 6X MF
#if BUILD_6X_SOLVER
  COPY_SFL_TO_MSFL(init_ht5);
  COPY_SFL_TO_MSFL(init_rh); 
  COPY_SFL_TO_MSFL(init_tke);
#endif  
}

INIT_MSFL(tSURFEL_S2S_T_DATA) {
  VEC2_COPY_SFL_TO_MSFL(m_out_flux_t, N_TIME_INDICES, N_SURFEL_PGRAM_VOLUMES);
}
}//inline namespace SIMULTOR_NAMESPACE
#endif
