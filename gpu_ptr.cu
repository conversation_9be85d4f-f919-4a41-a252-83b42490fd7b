/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include <cuda.h>
#include "helper_cuda.h"
#include "common_sp.h"
#include "gpu_host_include.h"
#include "gpu_ptr.h"

static std::unordered_map<std::string, std::unordered_map<void*, size_t>> mem_usage;

__host__
void GPU::output_mem_usage() 
{
  double total = 0;
  for(auto elements: mem_usage) {
    double mb = 0.0;
    for (auto element : elements.second) {
      mb += static_cast<double>(element.second)/1.e6;
      total += element.second;
    }
    msg_print("%s: %f MB",elements.first.c_str(), mb);
  }
  double mb = total/1.e6;
  msg_print("total: %f MB", mb);
}

__host__
GPU::Ptr<void> GPU::malloc(size_t nbytes, const std::string& tag) 
{
  // printf("MPI(%d): GPU::malloc(nbytes=%lu,tag=%s)\n", my_proc_id, nbytes, tag.c_str());
  void * ptr;
  cudaError_t err = cudaMalloc(&ptr, nbytes);

  if (err == cudaErrorMemoryAllocation) {
    output_mem_usage();
    msg_error("GPU %d ran out of memory. Tried to allocate %lu bytes with tag '%s'",
              my_device_id, 
              nbytes, 
              tag.c_str());
  } else {
    checkCudaErrors(err);
  }

  LOG_MSG("GPU_MEM",LOG_ATTR(tag),LOG_ATTR(nbytes));
  mem_usage[tag][ptr] = nbytes;
  return GPU::Ptr<void>(ptr);
}

__host__
void GPU::free(GPU::Ptr<void>& ptr, const std::string& tag)
{
  auto it = mem_usage.find(tag);
  if (it != mem_usage.end()) {
    it->second.at(ptr.get()) = 0;
  } else {
    msg_error("Could not find tag '%s' in GPU mem usage map.", tag.c_str());
  }
  checkCudaErrors( cudaFree(ptr.get()) );  
  ptr = nullptr;
}

void * GPU::malloc_host_page_locked(size_t nbytes)
{
  void * ptr;
  checkCudaErrors( cudaMallocHost(&ptr, nbytes) );

  LOG_MSG("GPU_MEM","host","page locked mem",LOG_ATTR(nbytes));

  return ptr;
}

__host__
void GPU::memcpy_d2h(void * h, Ptr<const void> d, size_t nbytes)
{
  checkCudaErrors( cudaMemcpy(h, d.get(), nbytes, cudaMemcpyDeviceToHost) );
}

__host__
void GPU::memcpy_h2d(Ptr<void> d, const void * h, size_t nbytes)
{
  checkCudaErrors( cudaMemcpy(d.get(), h, nbytes, cudaMemcpyHostToDevice) );
}

__host__
void GPU::memcpy_d2d(Ptr<void> dst, Ptr<const void> src, size_t nbytes)
{
  checkCudaErrors( cudaMemcpy(dst.get(), src.get(), nbytes, cudaMemcpyDeviceToDevice) );
}

__host__
void GPU::memset(Ptr<void> d, int value, size_t nbytes)
{
  checkCudaErrors( cudaMemset(d.get(), value, nbytes) );
}

__host__
void GPU::memset_async(Ptr<void> d, int value, size_t nbytes, cudaStream_t stream)
{
  checkCudaErrors( cudaMemsetAsync(d.get(), value, nbytes, stream) );
}

__host__
void GPU::memcpy_d2h_async(void * h, Ptr<const void> d, size_t nbytes, cudaStream_t stream)
{
  checkCudaErrors( cudaMemcpyAsync(h, d.get(), nbytes, cudaMemcpyDeviceToHost, stream) );
}

__host__
void GPU::memcpy_h2d_async(Ptr<void> d, const void * h, size_t nbytes, cudaStream_t stream)
{
  checkCudaErrors( cudaMemcpyAsync(d.get(), h, nbytes, cudaMemcpyHostToDevice, stream) );
}

__host__
void GPU::memcpy_d2d_async(Ptr<void> dst, Ptr<const void> src, size_t nbytes, cudaStream_t stream)
{
  checkCudaErrors( cudaMemcpyAsync(dst.get(), src.get(), nbytes, cudaMemcpyDeviceToDevice, stream) );
}
