/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_SURFACE_SHOB_H
#define _SIMENG_SURFACE_SHOB_H

#include "shob.h"
#include "gpu_surfel_interactions.h"
#include "surfel_stencils.h"

struct sCDI_FACE_INDEX_TO_PHYSICS_INFO {
  // sINT16 m_coupling_model_index;
  // sINT8 m_init_pf_bc_coupling_p;
  sINT8 m_boundary_seed_var_spec_index; /* -1 if not subject to boundary seeding */
  sCDI_FACE_INDEX_TO_PHYSICS_INFO():
    // m_coupling_model_index(-1),
    // m_init_pf_bc_coupling_p(-1),
    m_boundary_seed_var_spec_index(-1) {
  }

  sCDI_FACE_INDEX_TO_PHYSICS_INFO(const LGI_FACE_ID_TO_SURFACE_PHYSICS& lgi_data) {
    // m_coupling_model_index = lgi_data.coupling_model_index;
    // m_init_pf_bc_coupling_p = lgi_data.init_pf_bc_coupling_p;
    m_boundary_seed_var_spec_index = lgi_data.boundary_seed_var_spec_index;
  }

  sCDI_FACE_INDEX_TO_PHYSICS_INFO(const sCDI_FACE_INDEX_TO_PHYSICS_INFO&) = default;
  sCDI_FACE_INDEX_TO_PHYSICS_INFO& operator=(const sCDI_FACE_INDEX_TO_PHYSICS_INFO&) = default;
};

extern std::vector<sCDI_FACE_INDEX_TO_PHYSICS_INFO> g_cdi_face_index_to_flow_physics_info;

#if GPU_COMPILER
namespace GPU {
  extern __CONSTANT__ __DEVICE__ sCDI_FACE_INDEX_TO_PHYSICS_INFO* g_cdi_face_index_to_flow_physics_info;  
}
#endif

/*==============================================================================
 * @fcn def_child_surfel_index
 * We want some of the SURFEL functions to accept a child surfel index argument
 * for MSFLS, and want to mandate call sites to provide it. On the other hand
 * client code dealing with regular surfels should not have to specify an
 * index for these access functions, and it can default to a trivial child ublk index of 0.
 * 
 * The template function def_child_ublk_index thus returns a 0 for regular
 * SURFEL but triggers a compilation error for MSFL methods unless an index argument
 * is specified, which is in contrast just using a default index argument of 0
 * ============================================================================*/
template<size_t N>
__HOST__DEVICE__ INLINE constexpr int def_child_sfl_index() {
  static_assert(N == 1, //This method can only be called  for CPU sfls
                "This static assertion goes off because a mega-surfel method was "
                "called without specifying the child surfel index");
  return -1;
}

template<>
__HOST__DEVICE__ INLINE constexpr int def_child_sfl_index<1>() { return 0; }

#define REQUIRE_INDEX_IF_MSFL(index) \
  asINT32 index = def_child_sfl_index<N>()

//----------------------------------------------------------------------------
// sSURFACE_SHOB:  Base class for surfels and sampling surfels
//----------------------------------------------------------------------------

/*--------------------------------------------------------------------------*
 * surfel ublk interaction 
 * -------------------------------------------------------------------------*/
const asINT32 N_PHASE_MASK_BITS = 4;
const asINT32 FULL_PHASE_MASK   = (1 << N_PHASE_MASK_BITS) - 1;
const asINT32 N_SCALE_DIFF_BITS = 2;
const asINT32 SCALE_DIFF_MASK   = (1 << N_SCALE_DIFF_BITS) - 1;

// CONDUCTION-CHECK: Where is this enum used? Do we need to add the vsurfel_ratio here?
enum VOXEL_INTERACTION_DATA {
  VOXEL_ID_PHASE_MASK,
  N_WEIGHTS_SCALE_DIFF,
  N_WEIGHTS_PDE_ONLY,
  PADDING,
  N_INTERACTION_DATA
};

// We need to find someway to separate these 2 things: vsurfel_ratio and
// approximate_weight are only used for conduction surfels. It's a waste to
// have them on flow surfels too.

// We have to do this mess with the buffers to avoid alignment issues.  I'm not
// sure of the best way to fix this. sSURFEL_VOXEL_INTERACTION can only have
// uINT8s in it currently, because of how it is packed into the
// sSURFEL_UBLK_INTERACTION data structure. Computing the size becomes a mess
// once we have an alignment > 1;

typedef struct sSURFEL_VOXEL_INTERACTION {

private:

  struct sINTERNAL {
#if !BUILD_GPU
    uINT8 m_vsurfel_ratio_buf[sizeof(STP_VSURFEL_WEIGHT_RATIO)];
    uINT8 m_approximate_weight_buf[sizeof(sdFLOAT)];
#endif
    uINT8 m_voxel_id_phase_mask;
    uINT8 m_n_weights_scale_diff;
    uINT8 m_n_weights_pde_only;
  } i;

public:

  static constexpr size_t SIZE = sizeof(sINTERNAL);

  sSURFEL_VOXEL_INTERACTION () { 
    memset(this,0,sizeof(sSURFEL_VOXEL_INTERACTION));
  }

  void set_voxel_interaction(sSURFEL_VOXEL_INTERACTION voxel_info) {
    *this = voxel_info;
  }

  sSURFEL_VOXEL_INTERACTION voxel_interaction() {
    sSURFEL_VOXEL_INTERACTION voxel_interaction = *this;
    return voxel_interaction;
  }

  void set_voxel_id_phase_mask (asINT32 voxel, asINT32 phase_mask) {
    if (voxel > 7)
      msg_internal_error("voxel %d cannot be greater than 7\n", voxel);
    i.m_voxel_id_phase_mask = voxel << N_PHASE_MASK_BITS | phase_mask;
  }

  // Currently lgi file contains the phase value not the mask
  void set_voxel_id_phase_mask (uINT8 voxel_id_phase_mask) {
    asINT32 voxel_id = voxel_id_phase_mask >> (N_PHASE_MASK_BITS-1);
    if (voxel_id > 7)
      msg_internal_error("voxel %d cannot be greater than 7\n", voxel_id);
    voxel_id = voxel_id << N_PHASE_MASK_BITS;
    asINT32 phase = voxel_id_phase_mask & (FULL_PHASE_MASK >> 1);
    // @@ create #define for phase = 4 in lgi
    if (phase == 4) {
      i.m_voxel_id_phase_mask = FULL_PHASE_MASK | voxel_id; 
    } else {
      i.m_voxel_id_phase_mask = (1 << phase) | voxel_id; 
    }
  }

  void set_scale_diff(asINT32 scale_diff) {
    i.m_n_weights_scale_diff |= scale_diff;
  }

  void set_n_voxel_weights_scale_diff(uINT8 n_weights, uINT8 scale_diff) {
    if (n_weights > ((1 << (8 - N_SCALE_DIFF_BITS)) -1))
      msg_internal_error("Number of weights %d is greater than %d \n", n_weights,
                         ((1 << (8 - N_SCALE_DIFF_BITS)) -1) );
    i.m_n_weights_scale_diff = (n_weights << N_SCALE_DIFF_BITS) | scale_diff;
  }

  void set_n_voxel_weights_pde_only(uINT8 n_weights) {
    if (n_weights > ((1 << 8) -1))
      msg_internal_error("Number of pde_only weights %d is greater than %d\n", n_weights,
                         ((1 << 8) -1));
    i.m_n_weights_pde_only = n_weights;
  }

  void set_vsurfel_ratio(STP_VSURFEL_WEIGHT_RATIO v) {
#if !BUILD_GPU
    memcpy(i.m_vsurfel_ratio_buf, &v, sizeof(STP_VSURFEL_WEIGHT_RATIO));
#endif
  }

  void set_approximate_weight(sdFLOAT w) {
#if !BUILD_GPU
    memcpy(i.m_approximate_weight_buf, &w, sizeof(sdFLOAT));
#endif
  }

  asINT32 voxel_id () {
    return (i.m_voxel_id_phase_mask >> N_PHASE_MASK_BITS);
  }

  asINT32 phase_mask() {
    return (i.m_voxel_id_phase_mask & FULL_PHASE_MASK);
  }

  asINT32 scale_diff () {
    return (i.m_n_weights_scale_diff & SCALE_DIFF_MASK);
  }

  asINT32 n_voxel_weights () {
    return (i.m_n_weights_scale_diff >> N_SCALE_DIFF_BITS);
  }

  asINT32 n_voxel_weights_pde_only () {
    return i.m_n_weights_pde_only;
  }

  STP_VSURFEL_WEIGHT_RATIO vsurfel_ratio() const {
#if !BUILD_GPU
    STP_VSURFEL_WEIGHT_RATIO v;
    memcpy(&v, i.m_vsurfel_ratio_buf, sizeof(STP_VSURFEL_WEIGHT_RATIO));
    return v;
#else
    return 0.0;
#endif
  }

  sdFLOAT approximate_weight() const {
#if !BUILD_GPU
    sdFLOAT w;
    memcpy(&w, i.m_approximate_weight_buf, sizeof(sdFLOAT));
    return w;
#else
    return 0.0;
#endif
  }

} *SURFEL_VOXEL_INTERACTION;


typedef class sSURFEL_UBLK_INTERACTION {
  static_assert(alignof(sSURFEL_VOXEL_INTERACTION) == 1, "Alignment of sSURFEL_VOXEL_INTERACTION must be 1, unless you plan on updating the various methods of sSURFEL_UBLK_INTERACTION appropriately.");
private:
  union {
    UBLK             m_ublk;
    POINTER_SIZE_INT m_ublk_id;
  };
public:
  sINT16 m_total_weights;
  uINT8 m_n_weight_sets;
  // this is a mask of voxels that advect lb states
  VOXEL_MASK_8 m_voxels_advect_to_surfel_mask;
private:
  STP_SURFEL_WEIGHT m_first_weight;

public:
  POINTER_SIZE_INT ublk_id() {
    return m_ublk_id;
  }

  VOID set_ublk_id(uINT32 ublk_id) {
    m_ublk_id = ublk_id;
  }
  
  UBLK ublk() {
#if EXA_USE_AVX
    static_assert(N_PHASE_MASK_BITS <= 5); // ublk pointer is aligned to 32 byte boundary for AVX
#elif EXA_USE_SSE
    static_assert(N_PHASE_MASK_BITS <= 4); // ublk pointer is aligned to 32 byte boundary for SSE
#endif
    return (sUBLK *)scalar_mask_pointer(m_ublk, ~FULL_PHASE_MASK);
  }

  VOID set_ublk(UBLK ublk) {
    m_ublk = ublk;
  }

  uINT8 vr_phase_mask() {
    uINT8 phase_mask = static_cast<uINT8>((size_t)m_ublk & FULL_PHASE_MASK);
    return phase_mask;
  }

  VOID set_vr_phase_mask(uINT8 vr_phase_mask) {
    vr_phase_mask &= FULL_PHASE_MASK;
    m_ublk = (UBLK) scalar_or_pointer(m_ublk, vr_phase_mask);
  }

  uINT8 *first_byte_after_weights() {
    return (uINT8 *)&m_first_weight + (m_total_weights) * sizeof(STP_SURFEL_WEIGHT);
  }

  STP_SURFEL_WEIGHT *weights() {
    return &m_first_weight;
  }

  uINT8 *voxel_info() {
    return first_byte_after_weights();
  }

  uINT8 *voxel_info(asINT32 n_total_weights) {
    return (uINT8 *)&m_first_weight + (n_total_weights) * sizeof(STP_SURFEL_WEIGHT);
  }

  uINT8 *set_voxel_interaction(uINT8 *next_int,
                               SURFEL_VOXEL_INTERACTION voxel_info) {
    sSURFEL_VOXEL_INTERACTION voxel_interaction = voxel_info->voxel_interaction();
    memcpy(next_int, ((uINT8 *) &voxel_interaction), sSURFEL_VOXEL_INTERACTION::SIZE);
    return (next_int + sSURFEL_VOXEL_INTERACTION::SIZE);
  }

  uINT8 *voxel_interaction(uINT8 *next_uint8,
                           SURFEL_VOXEL_INTERACTION voxel_info) {
    sSURFEL_VOXEL_INTERACTION next_interaction;
    memcpy(&next_interaction, next_uint8, sSURFEL_VOXEL_INTERACTION::SIZE);
    voxel_info->set_voxel_interaction(next_interaction);
    return (next_uint8 + sSURFEL_VOXEL_INTERACTION::SIZE);
  }

  // Surfel ublk interaction information is split into two pieces
  // First piece contains the weights associated with surfel-voxel
  // interaction.
  // Second piece contains phase_mask and n_weights per surfel-voxel
  // interaction and latvecs for each surfel-voxel interaction. 
  static size_t size(asINT32 total_weights, uINT8 n_weight_sets) {
    size_t voxel_interaction_info_size = sSURFEL_VOXEL_INTERACTION::SIZE * n_weight_sets + total_weights;
    size_t total_size = sizeof(STP_SURFEL_WEIGHT) * (total_weights-1)
                        + voxel_interaction_info_size 
                        + sizeof(sSURFEL_UBLK_INTERACTION);
    // The end point will be aligned at the 8 byte boundary.
    return get_byte_aligned(total_size, sizeof(UBLK));
  }

  size_t size() {
    size_t voxel_interaction_info_size = sSURFEL_VOXEL_INTERACTION::SIZE * m_n_weight_sets + m_total_weights;
    size_t total_size = sizeof(STP_SURFEL_WEIGHT) * (m_total_weights-1)
                        + voxel_interaction_info_size 
                        + sizeof(sSURFEL_UBLK_INTERACTION);
    // The end point will be aligned at the 8 byte boundary.
    return get_byte_aligned(total_size, sizeof(UBLK));
  }

  sSURFEL_UBLK_INTERACTION *next() {
    return ((sSURFEL_UBLK_INTERACTION *) ((uINT8 *) this + this->size()));
  }

} *SURFEL_UBLK_INTERACTION;

VOID add_ublk_interactions_internal(DGF_SURFEL_DESC surfel_desc, 
                                    uINT16 *p_n_ublk_interactions,
                                    sSURFEL_UBLK_INTERACTION **p_ublk_interactions);

// The order of data bits should be consistent with bit wise fields in m_surfel_type.
enum SURFEL_DATA_TYPE_BIT {
  TWO_SURFEL_STATES_DATA_BIT,
  S2S_ADVECT_DATA_BIT,
  SURFEL_MIRROR_DATA_BIT,
  SURFEL_MLRF_DATA_BIT,
  SURFEL_EVEN_ODD_DATA_BIT,
  SURFEL_SLRF_DATA_BIT,
  SURFEL_INTERFACE_DATA_BIT,
  SURFEL_V2S_DATA_BIT,
#if !BUILD_GPU
  SURFEL_CONDUCTION_DATA_BIT,
  SURFEL_CONDUCTION_INTERFACE_DATA_BIT,
  SURFEL_SHELL_CONDUCTION_DATA_BIT,
  SURFEL_RADIATION_DATA_BIT,
#endif
  N_SURFEL_DATA_TYPE_BITS
};

constexpr static asINT32 SURFEL_DATA_TYPE_MASK = (1 << N_SURFEL_DATA_TYPE_BITS) - 1;

template<size_t N>
struct tSURFEL_ATTRS;

template<>
struct tSURFEL_ATTRS<1> {

  __HOST__DEVICE__ uINT32 surfel_type() const {
    return m_surfel_type & SURFEL_DATA_TYPE_MASK;
  }

  // Static bits - These bits are set during initialization and
  // never change during the simulation
  union {
    
    uINT32 m_surfel_type;
  
    struct {
      uINT32 m_has_two_copies_of_outflux:1;
      uINT32 m_is_s2s_destination:1;
      uINT32 m_has_mirror:1;
      uINT32 m_is_mlrf:1;
      uINT32 m_is_even_or_odd:1;
      uINT32 m_is_slrf:1;
      uINT32 m_is_isurfel:1;
      uINT32 m_has_v2s_data:1;
      uINT32 m_interacts_with_conduction_solid:1;
      uINT32 m_is_conduction_interface:1;
      uINT32 m_is_conduction_shell:1;
      uINT32 m_is_radiation:1;
      // Bits above determine memory usage
      uINT32 m_is_wall:1;
      uINT32 m_is_not_free_slip_wall:1;
      uINT32 m_interacts_with_vr_ublks:1;
      uINT32 m_is_ghost:1;
      uINT32 m_is_fringe:1;
      uINT32 m_is_interior_lrf:1;
      uINT32 m_is_mirror:1;
      uINT32 m_is_inlet_or_outlet:1;
      uINT32 m_is_fringe2:1;
      uINT32 m_is_film_only_ghost:1;
      uINT32 m_is_frozen:1;
      uINT32 m_is_backside:1;
      uINT32 m_is_slip_surfel:1;
      uINT32 m_is_noslip_surfel:1;
      uINT32 m_is_moving:1;
      uINT32 m_is_sampling_surfel:1;
      uINT32 m_is_film_only_fringe:1;
      uINT32 m_is_weightless_mlrf:1;
      uINT32 m_has_distinct_back_bc:1;
    };
  };

  // This bits are used by the compute thread to keep track of the state of the surfel,
  // and should not be referenced by the comm thread
  union {
    uINT32 m_dynamic_bits;

    struct {
      uINT32 m_is_outflux_copied:1;
    };
  };
};

template<>
struct tSURFEL_ATTRS<N_SFLS_PER_MSFL> : public tSURFEL_ATTRS<1> {
  tBITSET<N_SFLS_PER_MSFL, unsigned long long> m_is_valid_child;
  tBITSET<N_SFLS_PER_MSFL, unsigned long long> m_child_is_not_free_slip_wall;
  tBITSET<N_SFLS_PER_MSFL, unsigned long long> m_child_interacts_with_vr_ublks;
  tBITSET<N_SFLS_PER_MSFL, unsigned long long> m_child_is_weightless_mlrf;
  tBITSET<N_SFLS_PER_MSFL, unsigned long long> m_child_is_inlet_or_outlet;
};

#ifndef BUILD_GPU
struct sMSFL_INTERACTIONS;
#endif

template<typename SFL_TYPE_TAG>
struct tSURFACE_SHOB : public sSHOB {
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<uINT16, N> m_face_index;
  tSURFEL_ATTRS<N> m_surfel_attributes;
  tSFL_VAR<STP_GEOM_VARIABLE, N>  centroid[3];
  tSFL_VAR<STP_GEOM_VARIABLE, N>  normal[3];
  tSFL_VAR<STP_GEOM_VARIABLE, N>  area;
  STP_EVEN_ODD m_even_odd_mask;
  //Purposely setting N to 1 here since mirror and particle specific data
  //should not be here. Seems wasteful to be spending so much per mega surfel
  //for non-mirror non-particle simulations
  tSFL_VAR<dFLOAT, 1> m_off_plane_tolerance;
  tSFL_VAR<tSURFACE_SHOB<SFL_TYPE_TAG>*, 1> m_opposite_surfel;  
  tSFL_VAR<STP_LATVEC_MASK, N> incoming_latvec_mask;
  
  union {    
    struct {      
      uINT16 m_n_ublk_interactions;
      uINT16 m_n_interacting_ublks_pde_only;
      SURFEL_UBLK_INTERACTION m_ublk_interactions;
    };
    
    sMSFL_INTERACTIONS* m_interactions;
  };

  sSTENCIL_INFO            m_stencil_info;
  
  /******************************************************************************/
  // sfl_index based attribute set/get methods
  /******************************************************************************/  
  __HOST__DEVICE__ uINT16 face_index(REQUIRE_INDEX_IF_MSFL(index)) const {
    return m_face_index[index];
  }

  __HOST__DEVICE__ sSTENCIL_INFO* stencil() {
    if constexpr(is_msfl()) {
      // Not supported on gpu
      assert(false); return nullptr;
    } else {
      return &m_stencil_info;
    }
  }

  __HOST__DEVICE__ tSURFACE_SHOB<SFL_TYPE_TAG>* opposite_surfel(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      //Mirror not yet supported
      assert(false); return nullptr;
    } else {
      return m_opposite_surfel[index];
    }
  }

  __HOST__DEVICE__ dFLOAT off_plane_tolerance(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      //particles not yet supported
      assert(false); return 0.0;
    } else {    
      return m_off_plane_tolerance[index];
    }
  }  
  
  __HOST__DEVICE__ sINT8 boundary_seed_var_spec_index(REQUIRE_INDEX_IF_MSFL(index)) const 
  {
#if DEVICE_COMPILATION_MODE
    cassert(is_valid_child_surfel(index));
    cassert(face_index(index) != (uINT16) -1);
    return GPU::g_cdi_face_index_to_flow_physics_info[face_index(index)].m_boundary_seed_var_spec_index;
#else
    return g_cdi_face_index_to_flow_physics_info.at(face_index(index)).m_boundary_seed_var_spec_index;
#endif
  }

  __HOST__ VOID set_even_odd_mask(STP_EVEN_ODD mask) {
    m_even_odd_mask = mask;
  }
  
  __HOST__DEVICE__ auINT32 even_odd_mask() const {
    return m_even_odd_mask;
  }

  __HOST__DEVICE__ BOOLEAN is_not_free_slip_wall(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      return m_surfel_attributes.m_child_is_not_free_slip_wall.test(index);
    } else {
      return m_surfel_attributes.m_is_not_free_slip_wall;
    }
  }
  
  __HOST__DEVICE__ VOID set_not_free_slip_wall(BOOLEAN value, REQUIRE_INDEX_IF_MSFL(index)) {
    if constexpr(is_msfl()) {
#if DEVICE_COMPILATION_MODE      
      m_surfel_attributes.m_child_is_not_free_slip_wall.gpu_atomic_set_or_reset(index, value);
#else
      m_surfel_attributes.m_child_is_not_free_slip_wall.set_or_reset(index, value);
#endif      
    }
    else {
      m_surfel_attributes.m_is_not_free_slip_wall = value? 1 : 0;
    }
  }
  
  __HOST__ VOID set_interacts_with_vr_ublks(REQUIRE_INDEX_IF_MSFL(index)) {
    if constexpr(is_msfl()) {
      m_surfel_attributes.m_child_interacts_with_vr_ublks.set(index);
    } else {
      m_surfel_attributes.m_interacts_with_vr_ublks = 1;
    }
  }

  __HOST__DEVICE__ BOOLEAN interacts_with_vr_ublks(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      return m_surfel_attributes.m_child_interacts_with_vr_ublks.test(index);
    } else {
      return m_surfel_attributes.m_interacts_with_vr_ublks;
    }
  }

  __HOST__DEVICE__ BOOLEAN has_no_vr_interactions(REQUIRE_INDEX_IF_MSFL(index)) const {
    return !interacts_with_vr_ublks(index);
  }

  __HOST__ VOID set_is_even_or_odd() {
      m_surfel_attributes.m_is_even_or_odd = 1;
  }
  
  __HOST__DEVICE__ BOOLEAN is_even_or_odd() const {
    return m_surfel_attributes.m_is_even_or_odd;
  }

  __HOST__DEVICE__ constexpr static BOOLEAN is_msfl() { return N > 1; }
  
  __HOST__ VOID set_is_valid_child_surfel(REQUIRE_INDEX_IF_MSFL(index)) {
    if constexpr(is_msfl()) {
      m_surfel_attributes.m_is_valid_child.set(index);
    }
  }
  
  __HOST__DEVICE__ BOOLEAN is_valid_child_surfel(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      return m_surfel_attributes.m_is_valid_child.test(index);
    } else {
      return TRUE;
    }
  }

  __HOST__ VOID set_weightless_mlrf(BOOLEAN value, REQUIRE_INDEX_IF_MSFL(index)) {
    if constexpr(is_msfl()) {
      if (value) {
        m_surfel_attributes.m_child_is_weightless_mlrf.set(index);
      }
    } else {
      m_surfel_attributes.m_is_weightless_mlrf = value > 0? 1 : 0;
    }
  }
  
  __HOST__DEVICE__ BOOLEAN is_weightless_mlrf(REQUIRE_INDEX_IF_MSFL(index)) const {
    if constexpr(is_msfl()) {
      return m_surfel_attributes.m_child_is_weightless_mlrf.test(index);
    } else {
      return m_surfel_attributes.m_is_weightless_mlrf;
    }
  }  
  
  __HOST__DEVICE__ BOOLEAN is_even() {
    return (m_even_odd_mask == STP_PROCESS_ON_EVEN_TIMES);
  }
  __HOST__DEVICE__ BOOLEAN is_odd() {
    return (m_even_odd_mask == STP_PROCESS_ON_ODD_TIMES);
  }
  __HOST__DEVICE__ BOOLEAN is_regular() {
    return (m_even_odd_mask == STP_PROCESS_ON_ALL_TIMES);
  }

  inline STP_DGEOM_VARIABLE compute_normal_distance_to_centroid(STP_DGEOM_VARIABLE x[3]){
    sPARTICLE_VAR r[N_SPACE_DIMS];
    vsub(r, x, centroid);
    return vdot(r, normal);
  }
  
  /******************************************************************************/
  // Non sfl_index based attribute set/get methods
  /******************************************************************************/
  __HOST__DEVICE__ BOOLEAN has_two_copies_of_outflux() const {
    return m_surfel_attributes.m_has_two_copies_of_outflux;
  }
  __HOST__DEVICE__ BOOLEAN is_s2s_destination() const {
    return m_surfel_attributes.m_is_s2s_destination;
  }
  __HOST__DEVICE__ BOOLEAN is_lrf() const {
    return m_surfel_attributes.m_is_mlrf || m_surfel_attributes.m_is_slrf;
  }
  __HOST__DEVICE__ BOOLEAN is_stationary_rf() const {
    return m_surfel_attributes.m_is_slrf;
  }
   __HOST__DEVICE__ BOOLEAN is_sliding_rf() const {
    return m_surfel_attributes.m_is_mlrf;
  }
  __HOST__DEVICE__ BOOLEAN is_isurfel() const {
    return m_surfel_attributes.m_is_isurfel;
  }
  __HOST__DEVICE__ BOOLEAN has_mirror() const {
    return m_surfel_attributes.m_has_mirror;
  }
  __HOST__DEVICE__ BOOLEAN is_mirror() const {
    return m_surfel_attributes.m_is_mirror;
  }
  VOID set_mirror(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_mirror = 1;
    else
      m_surfel_attributes.m_is_mirror = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_outflux_copied_given_even_time(BOOLEAN is_time_step_even) {
    return !(m_surfel_attributes.m_is_outflux_copied ^ is_time_step_even);
  }
  __HOST__DEVICE__ BOOLEAN is_outflux_copied() const {
    return m_surfel_attributes.m_is_outflux_copied;
  }
  __HOST__DEVICE__ VOID set_outflux_copied(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_outflux_copied = 1;
    else
      m_surfel_attributes.m_is_outflux_copied = 0;
  }
  __HOST__DEVICE__ BOOLEAN has_v2s_data() const {
    return m_surfel_attributes.m_has_v2s_data;
  }
  __HOST__DEVICE__ BOOLEAN is_wall() const {
    return m_surfel_attributes.m_is_wall;
  }
  VOID set_wall(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_wall = 1;
    else
      m_surfel_attributes.m_is_wall = 0;
  }

  __HOST__DEVICE__ BOOLEAN is_ghost() const {
    return m_surfel_attributes.m_is_ghost;
  }

  VOID set_ghost(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_ghost = 1;
    else
      m_surfel_attributes.m_is_ghost = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_film_only_ghost() const {
    return m_surfel_attributes.m_is_film_only_ghost;
  }
  VOID set_film_only_ghost(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_film_only_ghost = 1;
    else
      m_surfel_attributes.m_is_film_only_ghost = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_film_only_fringe() const {
    return m_surfel_attributes.m_is_film_only_fringe;
  }
  VOID set_film_only_fringe(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_film_only_fringe = 1;
    else
      m_surfel_attributes.m_is_film_only_fringe = 0;
  }

  __HOST__DEVICE__ BOOLEAN has_distinct_back_bc() const {
    return m_surfel_attributes.m_has_distinct_back_bc;
  }

  VOID set_has_distinct_back_bc(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_has_distinct_back_bc = 1;
    else
      m_surfel_attributes.m_has_distinct_back_bc = 0;
  }
    

  __HOST__DEVICE__ BOOLEAN is_fringe() const {
    return m_surfel_attributes.m_is_fringe;
  }
  VOID set_fringe(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_fringe = 1;
    else
      m_surfel_attributes.m_is_fringe = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_fringe2() const {
    return m_surfel_attributes.m_is_fringe2;
  }
  VOID set_fringe2(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_fringe2 = 1;
    else
      m_surfel_attributes.m_is_fringe2 = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_interior_lrf () const {
    return m_surfel_attributes.m_is_interior_lrf;
  }
  VOID set_interior_lrf(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_interior_lrf = 1;
    else
      m_surfel_attributes.m_is_interior_lrf = 0;
  }
  __HOST__DEVICE__ BOOLEAN has_inlet_or_outlet() const {
    return m_surfel_attributes.m_is_inlet_or_outlet;
  }
  __HOST__DEVICE__ BOOLEAN is_inlet_or_outlet(REQUIRE_INDEX_IF_MSFL(sfl_idx)) const {
    if constexpr(is_msfl())
      return m_surfel_attributes.m_child_is_inlet_or_outlet.test(sfl_idx);
    else
      return m_surfel_attributes.m_is_inlet_or_outlet;
  }
  VOID set_inlet_or_outlet(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_inlet_or_outlet = 1;
    else
      m_surfel_attributes.m_is_inlet_or_outlet = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_frozen() const {
    return m_surfel_attributes.m_is_frozen;
  }
  __HOST__DEVICE__ VOID set_frozen(BOOLEAN value) {
    if (value)
      m_surfel_attributes.m_is_frozen = 1;
    else
      m_surfel_attributes.m_is_frozen = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_backside() const {
    return m_surfel_attributes.m_is_backside;
  }
  VOID set_backside(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_backside = 1;
    else
      m_surfel_attributes.m_is_backside = 0;
  }
  // CONDUCTION-TODO: Change name to something more appropriate
  // JEH Change to interacts_with_conduction_solid()
  __HOST__DEVICE__ BOOLEAN is_conduction_surface() {
    return m_surfel_attributes.m_interacts_with_conduction_solid;
  }

  __HOST__DEVICE__ BOOLEAN is_radiation() {
    return m_surfel_attributes.m_is_radiation;
  }

  __HOST__DEVICE__ BOOLEAN is_conduction_shell() {
    return m_surfel_attributes.m_is_conduction_shell;
  }

  __HOST__DEVICE__ BOOLEAN is_conduction_open_shell() {
    return (m_surfel_attributes.m_is_conduction_shell && !m_surfel_attributes.m_interacts_with_conduction_solid);
  }

  __HOST__DEVICE__ BOOLEAN is_conduction_open_shell_boundary() {
    return (m_surfel_attributes.m_interacts_with_conduction_solid &&
        !m_surfel_attributes.m_is_conduction_shell && m_n_ublk_interactions == 0);
  }

  // JEH Add this since it is our everyday terminology
  __HOST__DEVICE__ BOOLEAN is_conduction_surfel() const {
    return m_surfel_attributes.m_is_conduction_shell || m_surfel_attributes.m_interacts_with_conduction_solid;
  }
  __HOST__DEVICE__ BOOLEAN is_conduction_interface() {
    return m_surfel_attributes.m_is_conduction_interface;
  }

  __HOST__DEVICE__ BOOLEAN is_conduction_interface_sampled() {
    // is_conduction_interface && !is_conduction_open_shell()
    return m_surfel_attributes.m_is_conduction_interface &&
      !(m_surfel_attributes.m_is_conduction_shell && !m_surfel_attributes.m_interacts_with_conduction_solid);
  }

  STP_REALM realm() {
    return is_conduction_surfel() ? STP_COND_REALM : STP_FLOW_REALM;
  }

  __HOST__DEVICE__ VOID set_conduction_interface(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_conduction_interface = 1;
    else
      m_surfel_attributes.m_is_conduction_interface = 0;
  } 

  __HOST__DEVICE__ BOOLEAN is_conduction_lrf() {
    return is_lrf() && (is_conduction_surfel() || m_opposite_surfel->is_conduction_surfel());
  }

  __HOST__DEVICE__ BOOLEAN is_conduction_mlrf() {
    return is_sliding_rf() && (is_conduction_surfel() || m_opposite_surfel->is_conduction_surfel());
  }

  __HOST__DEVICE__ BOOLEAN is_slip_surfel() const {return m_surfel_attributes.m_is_slip_surfel;}
  VOID set_slip_surfel(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_slip_surfel = 1;
    else
      m_surfel_attributes.m_is_slip_surfel = 0;
  }
  __HOST__DEVICE__ BOOLEAN is_noslip_surfel() const {return m_surfel_attributes.m_is_noslip_surfel;}
  VOID set_noslip_surfel(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_noslip_surfel = 1;
    else
      m_surfel_attributes.m_is_noslip_surfel = 0;
  }  
  __HOST__DEVICE__ BOOLEAN is_moving()  {return m_surfel_attributes.m_is_moving;}
  VOID set_moving(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_moving = 1;
    else
      m_surfel_attributes.m_is_moving = 0;
  }
 __HOST__DEVICE__ BOOLEAN is_sampling_surfel() const {return m_surfel_attributes.m_is_sampling_surfel;}
 VOID set_sampling_surfel(BOOLEAN value) {
    if(value)
      m_surfel_attributes.m_is_sampling_surfel = 1;
    else
      m_surfel_attributes.m_is_sampling_surfel = 0;
  }  
};

using sSURFACE_SHOB = tSURFACE_SHOB<SFL_SDFLOAT_TYPE_TAG>;
using SURFACE_SHOB = sSURFACE_SHOB*;

#endif
