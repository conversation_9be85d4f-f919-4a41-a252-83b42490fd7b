/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_TURB_SOLVER_DATA_H
#define _SIMENG_TURB_SOLVER_DATA_H

#include "common_sp.h"
#include "vectorization_support.h"
#include "shob.h"
#include "ckpt.h"
#include "dgf_reader_sp.h"
#include "meas.h"
#include "debug_print_spec.h"
#include "gpu_host_init.h"

// forward declarations specific to this solver data-block
inline namespace SIMULATOR_NAMESPACE {

template <typename TYPE_TAG> class tTURB_DATA;
template <typename UBLK_TYPE_TAG> class tUBLK_TURB_DATA;
template <typename UBLK_TYPE_TAG> class tNEARBLK_TURB_DATA;

template <typename SFL_TYPE_TAG> struct tSURFEL_TURB_DATA;
template<typename SFL_TYPE_TAG> struct tSURFEL_V2S_TURB_DATA;
	 
typedef tTURB_DATA<UBLK_SDFLOAT_TYPE_TAG>  sTURB_DATA, *TURB_DATA;
typedef tTURB_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBFLOAT_TURB_DATA, *UBFLOAT_TURB_DATA;
typedef tUBLK_TURB_DATA<UBLK_SDFLOAT_TYPE_TAG>  sUBLK_TURB_DATA, *UBLK_TURB_DATA;
typedef tUBLK_TURB_DATA<UBLK_UBFLOAT_TYPE_TAG>  sUBLK_UBFLOAT_TURB_DATA, *UBLK_UBFLOAT_TURB_DATA;
typedef tNEARBLK_TURB_DATA<UBLK_SDFLOAT_TYPE_TAG> sNEAR_UBLK_TURB_DATA, *NEAR_UBLK_TURB_DATA;
typedef tNEARBLK_TURB_DATA<UBLK_UBFLOAT_TYPE_TAG> sNEAR_UBLK_UBFLOAT_TURB_DATA, *NEAR_UBLK_UBFLOAT_TURB_DATA;

#ifdef BUILD_GPU
typedef tUBLK_TURB_DATA<MBLK_SDFLOAT_TYPE_TAG>  sMBLK_TURB_DATA, *MBLK_TURB_DATA;
typedef tUBLK_TURB_DATA<MBLK_UBFLOAT_TYPE_TAG>  sMBLK_UBFLOAT_TURB_DATA, *MBLK_UBFLOAT_TURB_DATA;
typedef tNEARBLK_TURB_DATA<MBLK_SDFLOAT_TYPE_TAG> sNEAR_MBLK_TURB_DATA, *NEAR_MBLK_TURB_DATA;
typedef tNEARBLK_TURB_DATA<MBLK_UBFLOAT_TYPE_TAG> sNEAR_MBLK_UBFLOAT_TURB_DATA, *NEAR_MBLK_UBFLOAT_TURB_DATA;

typedef tSURFEL_TURB_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_TURB_DATA, *MSFL_TURB_DATA;
typedef tSURFEL_V2S_TURB_DATA<MSFL_SDFLOAT_TYPE_TAG> sMSFL_V2S_TURB_DATA, *MSFL_V2S_TURB_DATA;	 
#endif


template<size_t N_VOXELS>
struct tNEARBLK_TURB_SEND_FIELD
{
  sVOXEL_GRADS m_voxel_grads[N_VOXELS];
};

//------------------------------------------------------------------------------
// sUBLK_TURB_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tTURB_DATA {
  EXTRACT_UBLK_TRAITS

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
  ALIGNED_UBFLOAT eddy_visc;
  ALIGNED_UBFLOAT turb_ke;
  ALIGNED_UBFLOAT turb_df;
  ALIGNED_UBFLOAT grad_k[3];
  ALIGNED_UBFLOAT grad_e[3];

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print<N_VOXELS>(os, "turb_ke", turb_ke, print_voxel);
    sim_print<N_VOXELS>(os, "turb_df", turb_df, print_voxel);
    sim_print<N_VOXELS>(os, "eddy_visc", eddy_visc, print_voxel);
    sim_print<3, N_VOXELS>(os, "grad_k", grad_k, loop_limits(0, 2), print_voxel);
    sim_print<3, N_VOXELS>(os, "grad_e", grad_e, loop_limits(0, 2), print_voxel);
  }

#undef ALIGNED_UBFLOAT
};

template<size_t N_VOXELS>
struct tUBLK_TURB_SEND_FIELD
{
  // The send buffer field may not be 16/32 byte aligned.
  //tTURB_DATA<sdFLOAT[8]>  m_turb_data;
  STP_PHYS_VARIABLE eddy_visc[N_VOXELS];
  STP_PHYS_VARIABLE turb_ke[N_VOXELS];
  STP_PHYS_VARIABLE turb_df[N_VOXELS];
  STP_PHYS_VARIABLE grad_k[3][N_VOXELS];
  STP_PHYS_VARIABLE grad_e[3][N_VOXELS];
  STP_PHYS_VARIABLE m_turb_str_mag[N_VOXELS];
  STP_PHYS_VARIABLE m_gamma_swirl[N_VOXELS];
};

template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tUBLK_TURB_DATA
{
  EXTRACT_UBLK_TRAITS
  using sTURB_DATA = tTURB_DATA<UBLK_TYPE_TAG>;

#define ALIGNED_UBFLOAT ALIGN_VECTOR T
/*  ALIGNED_UBFLOAT eddy_visc[2];
  ALIGNED_UBFLOAT turb_ke[2];
  ALIGNED_UBFLOAT turb_df[2];
  ALIGNED_UBFLOAT grad_k[2][3];
  ALIGNED_UBFLOAT grad_e[2][3];*/
  sTURB_DATA   m_turb_data[2];
  ALIGNED_UBFLOAT turb_str_mag;
  ALIGNED_UBFLOAT turb_tau;      //relaxation time in turbulent case
  ALIGNED_UBFLOAT S_mag_sij;
  ALIGNED_UBFLOAT gamma_swirl;
#if BUILD_D39_LATTICE
  ALIGNED_UBFLOAT eps_vish;
#endif

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;
    sim_print_data_header(os,"UBLK_TURB_DATA");
    sim_print<N_VOXELS>(os, "turb_str_mag", turb_str_mag, print_voxel);
    sim_print<N_VOXELS>(os, "turb_tau", turb_tau, print_voxel);
    sim_print<N_VOXELS>(os, "S_mag_sij", S_mag_sij, print_voxel);
    sim_print<N_VOXELS>(os, "gamma_swirl", gamma_swirl, print_voxel);
    sim_print_vs(os);

    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "TURB_DATA", t_step);
      m_turb_data[t_step].print_voxel_data(os, print_voxel);
      sim_print_vs(os);
    }

  }

  VOID copy_voxel_for_gradient_calculation(tUBLK_TURB_DATA *dest_ublk_turb_data, asINT32 dest_voxel, asINT32 source_voxel)
  {
    tUBLK_TURB_DATA* d = dest_ublk_turb_data;
    ccDOTIMES(timestep, 2) {
      d->m_turb_data[timestep].turb_ke  [dest_voxel] = m_turb_data[timestep].turb_ke  [source_voxel];
      d->m_turb_data[timestep].turb_df  [dest_voxel] = m_turb_data[timestep].turb_df  [source_voxel];
      d->m_turb_data[timestep].eddy_visc[dest_voxel] = m_turb_data[timestep].eddy_visc[source_voxel];
      for (asINT32 axis = 0; axis < N_AXES; axis++) {
        d->m_turb_data[timestep].grad_k[axis][dest_voxel] = m_turb_data[timestep].grad_k[axis][source_voxel];
        d->m_turb_data[timestep].grad_e[axis][dest_voxel] = m_turb_data[timestep].grad_e[axis][source_voxel];
      }
    }
    d->turb_str_mag [dest_voxel] = turb_str_mag [source_voxel];
  }

  __DEVICE__
  VOID explode_voxel(tUBLK_TURB_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_turb_data,
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask, 
                     asINT32 gpu_fine_voxor) {
    asINT32 prior_timestep_index = (prior_solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
    auto * f = fine_ublk_turb_data;

    // The compiler isn't smart enough to pull the vxFLOAT loads out of the
    // loops, so we have to preload all of the coarse ublk data before setting
    // the values in the vr fine ublk.

    vxFLOAT coarse_turb_ke = m_turb_data[prior_timestep_index].turb_ke[voxel_to_explode];
    vxFLOAT coarse_turb_df = m_turb_data[prior_timestep_index].turb_df[voxel_to_explode];
    vxFLOAT coarse_eddy_visc = m_turb_data[prior_timestep_index].eddy_visc[voxel_to_explode];
    vxFLOAT coarse_grad_k0 = m_turb_data[prior_timestep_index].grad_k[0][voxel_to_explode];
    vxFLOAT coarse_grad_k1 = m_turb_data[prior_timestep_index].grad_k[1][voxel_to_explode];
    vxFLOAT coarse_grad_k2 = m_turb_data[prior_timestep_index].grad_k[2][voxel_to_explode];
    vxFLOAT coarse_grad_e0 = m_turb_data[prior_timestep_index].grad_e[0][voxel_to_explode];
    vxFLOAT coarse_grad_e1 = m_turb_data[prior_timestep_index].grad_e[1][voxel_to_explode];
    vxFLOAT coarse_grad_e2 = m_turb_data[prior_timestep_index].grad_e[2][voxel_to_explode];
    vxFLOAT coarse_turb_str_mag = turb_str_mag[voxel_to_explode];

    ccDOTIMES(timestep, N_TIME_INDICES) {
      ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
        f->m_turb_data[timestep].turb_ke[voxor] = coarse_turb_ke;
        f->m_turb_data[timestep].turb_df[voxor] = coarse_turb_df;
        f->m_turb_data[timestep].eddy_visc[voxor] = coarse_eddy_visc;
        f->m_turb_data[timestep].grad_k[0][voxor] = coarse_grad_k0;
        f->m_turb_data[timestep].grad_k[1][voxor] = coarse_grad_k1;
        f->m_turb_data[timestep].grad_k[2][voxor] = coarse_grad_k2;
        f->m_turb_data[timestep].grad_e[0][voxor] = coarse_grad_e0;
        f->m_turb_data[timestep].grad_e[1][voxor] = coarse_grad_e1;
        f->m_turb_data[timestep].grad_e[2][voxor] = coarse_grad_e2;
      }
    }
    ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) {
      f->turb_str_mag[voxor] = coarse_turb_str_mag;
    }
  }

  VOID copy_voxel_mme_data(tUBLK_TURB_DATA *dest_ublk_turb_data, asINT32 dest_voxel, asINT32 source_voxel) { }

  VOID explode_voxel_mme(tUBLK_TURB_DATA<tUBLK_UBFLOAT_TYPE_TAG<UBLK_TYPE_TAG::N_VOXELS>> *fine_ublk_turb_data,
                         asINT32 voxel_to_explode, asINT32 coarse_timestep_index) { }

  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }

  __DEVICE__
  VOID write_mme_ckpt(VOXEL_MASK voxel_mask, asINT32 scale, cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR *& meas)
  {
    auto& timescale = get_timescale_ref();
    asINT32 prior_index = timescale.m_ke_pde_tm.prior_timestep_index(scale);

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = m_turb_data[prior_index].turb_ke[voxel];
    }
    meas++;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      meas->var[voxel] = m_turb_data[prior_index].turb_df[voxel];
    }
    meas++;

    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      // Stress tensor mag is not scaled by mass in meas files
      meas->var[voxel] = turb_str_mag[voxel];
    }
    meas++;

  }
  
  VOID init() {
    memset(this, 0, sizeof(*this));
  }


  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(tUBLK_TURB_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(tUBLK_TURB_SEND_FIELD<N_VOXELS>) / sizeof(sdFLOAT));
  }


  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
    auto field = reinterpret_cast<tUBLK_TURB_SEND_FIELD<N_VOXELS>*>(send_data_info.send_buffer);
    memcpy(field, &(m_turb_data[timestep_index]), sizeof(sTURB_DATA));
    memcpy(field->m_turb_str_mag, turb_str_mag,      sizeof(field->m_turb_str_mag));
    memcpy(field->m_gamma_swirl, gamma_swirl,      sizeof(field->m_gamma_swirl));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    asINT32 timestep_index = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
    auto field = reinterpret_cast<tUBLK_TURB_SEND_FIELD<N_VOXELS>*>(recv_data_info.recv_buffer);
    memcpy(&(m_turb_data[timestep_index]), field, sizeof(sTURB_DATA));
    memcpy(turb_str_mag, field->m_turb_str_mag, sizeof(field->m_turb_str_mag));
    memcpy(gamma_swirl, field->m_gamma_swirl, sizeof(field->m_gamma_swirl));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID fill_mme_send_buffer(sdFLOAT* &send_buffer, asINT32 timestep_index)   { }
  VOID expand_mme_recv_buffer(sdFLOAT* &recv_buffer, asINT32 timestep_index) { }


#undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(TURB_DATA)
{
  COPY_UBLK_TO_MBLK(eddy_visc);
  COPY_UBLK_TO_MBLK(turb_ke);
  COPY_UBLK_TO_MBLK(turb_df);
  VEC_COPY_UBLK_TO_MBLK(grad_k,3);
  VEC_COPY_UBLK_TO_MBLK(grad_e,3);
}

INIT_MBLK(UBLK_TURB_DATA)
{
  VEC_COPY_UBLK_TO_MBLK(m_turb_data,2);
  COPY_UBLK_TO_MBLK (turb_str_mag);
  COPY_UBLK_TO_MBLK (turb_tau);      //relaxation time in turbulent case
  COPY_UBLK_TO_MBLK (S_mag_sij);
  COPY_UBLK_TO_MBLK (gamma_swirl);
#if BUILD_D39_LATTICE
  COPY_UBLK_TO_MBLK (eps_vish);
#endif

}
#endif

//------------------------------------------------------------------------------
// sNEARBLK_TURB_DATA
//------------------------------------------------------------------------------
template <typename UBLK_TYPE_TAG>
struct ALIGN_VECTOR tNEARBLK_TURB_DATA 
{
  EXTRACT_UBLK_TRAITS
  using sNEARBLK_TURB_SEND_FIELD = tNEARBLK_TURB_SEND_FIELD<N_VOXELS>;

  // @@@ This should only be part of ghostblocks. Doing so would require creation of a special data block
  // @@@ at the end of a ghostblock after all the data blocks common to all ublks.
  sVOXEL_GRADS voxel_grads[N_VOXELS];

#define ALIGNED_UBFLOAT ALIGN_VECTOR T

  //for sliding mesh
  ALIGNED_UBFLOAT K_flux_wall;
  ALIGNED_UBFLOAT E_flux_wall;
  ALIGNED_UBFLOAT K_lrf[2][3];
  ALIGNED_UBFLOAT E_lrf[2][3];
  //for flux limiter
  ALIGNED_UBFLOAT k_pde_1[2][3];
  ALIGNED_UBFLOAT k_pde_2[2][3];
  ALIGNED_UBFLOAT e_pde_1[2][3];
  ALIGNED_UBFLOAT e_pde_2[2][3];

  ALIGNED_UBFLOAT ltt_index;

  VOID print_voxel_data(std::ostream& os,
                        asINT32 print_voxel){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "UBLK_SURF_TURB_DATA");
    sim_print<N_VOXELS>(os, "Voxel Grads", voxel_grads, print_voxel);
    sim_print<N_VOXELS>(os, "K_flux_wall", K_flux_wall, print_voxel);
    sim_print<N_VOXELS>(os, "E_flux_wall", E_flux_wall, print_voxel);
    sim_print_vs(os);
    ccDOTIMES(t_step,N_TIME_INDICES)
    {
      sim_print_loop_header(os, "NBLK_TURB_DATA", t_step);
      sim_print<2, 3, N_VOXELS>(os, "K_lrf", K_lrf, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "E_lrf", E_lrf, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "k_pde_1", k_pde_1, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "k_pde_2", k_pde_2, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "e_pde_1", e_pde_1, t_step, loop_limits(0, 2), print_voxel);
      sim_print<2, 3, N_VOXELS>(os, "e_pde_2", e_pde_2, t_step, loop_limits(0, 2), print_voxel);
      sim_print_vs(os);
    }
  }

  // @@@ vectorize this
  // GPU: 8 contiguous threads enter this function.
  __DEVICE__
  VOID explode_voxel(tNEARBLK_TURB_DATA<tUBLK_UBFLOAT_TYPE_TAG<N_VOXELS>> *fine_ublk_turb_data, 
                     asINT32 voxel_to_explode, 
                     SOLVER_INDEX_MASK prior_solver_ts_index_mask, 
                     asINT32 gpu_fine_voxor) {
    // In the surfel centric world, surfel interacts with vr fine voxels and
    // cannot use any information from underlying coarse voxel
    // Also the voxel_grad is scaled by a factor of 1/2
    auto *d = fine_ublk_turb_data;
#if DEVICE_COMPILATION_MODE
    ccDOTIMES(axis, 3) {
      d->voxel_grads[gpu_fine_voxor].gradp[axis] = 0.5*voxel_grads[voxel_to_explode].gradp[axis];
    }
#else
    ccDOTIMES(voxel, N_VOXELS) {
      ccDOTIMES(axis, 3) {
        d->voxel_grads[voxel].gradp[axis] = 0.5*voxel_grads[voxel_to_explode].gradp[axis];
      }
    }
#endif
  }

  
  uINT64 ckpt_len() { return struct_field_size(tNEARBLK_TURB_DATA *, voxel_grads); }
  
  VOID write_ckpt() 	{
    write_ckpt_lgi(voxel_grads, sizeof(voxel_grads));
  }
  
    
  VOID write_ckpt(sCKPT_BUFFER& pio_ckpt_buff){
    pio_ckpt_buff.write(voxel_grads, sizeof(voxel_grads));
  }

  VOID read_ckpt() {
    read_lgi(voxel_grads, sizeof(voxel_grads));
  }

  VOID read_ckpt(u_char* buff, size_t& readSz) {
    size_t size = sizeof(voxel_grads);
    std::memcpy(&voxel_grads, buff+readSz, size);
    readSz+=size;
  }
  VOID init() {
    memset(this, 0, sizeof(*this));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sNEARBLK_TURB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sNEARBLK_TURB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sUBLK_SEND_DATA_INFO &send_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    sNEARBLK_TURB_SEND_FIELD* field = reinterpret_cast<sNEARBLK_TURB_SEND_FIELD*>(send_data_info.send_buffer);
    memcpy(field->m_voxel_grads, voxel_grads, sizeof(field->m_voxel_grads));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID  expand_recv_buffer(sUBLK_RECV_DATA_INFO &recv_data_info, SOLVER_INDEX_MASK solver_ts_index_mask) {
    sNEARBLK_TURB_SEND_FIELD* field = reinterpret_cast<sNEARBLK_TURB_SEND_FIELD*>(recv_data_info.recv_buffer);
    memcpy(voxel_grads, field->m_voxel_grads, sizeof(field->m_voxel_grads));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  #undef ALIGNED_UBFLOAT    
};

#ifdef BUILD_GPU
INIT_MBLK(NEARBLK_TURB_DATA)
{
  COPY_UBLK_TO_MBLK(voxel_grads);
  //for sliding mesh
  COPY_UBLK_TO_MBLK(K_flux_wall);
  COPY_UBLK_TO_MBLK(E_flux_wall);
  VEC2_COPY_UBLK_TO_MBLK(K_lrf,2,3);
  VEC2_COPY_UBLK_TO_MBLK(E_lrf,2,3);
  //for flux limiter
  VEC2_COPY_UBLK_TO_MBLK(k_pde_1,2,3);
  VEC2_COPY_UBLK_TO_MBLK(k_pde_2,2,3);
  VEC2_COPY_UBLK_TO_MBLK(e_pde_1,2,3);
  VEC2_COPY_UBLK_TO_MBLK(e_pde_2,2,3);

  COPY_UBLK_TO_MBLK(ltt_index);
}
#endif

typedef struct sSURFEL_TURB_SEND_FIELD
{
  STP_PHYS_VARIABLE       m_ltt_index;
} *SURFEL_TURB_SEND_FIELD;

typedef struct sSURFEL_V2S_TURB_SEND_FIELD
{
  STP_PHYS_VARIABLE       m_nu;
  STP_PHYS_VARIABLE       m_tke_pde;
  STP_PHYS_VARIABLE       m_eps_pde;
} *SURFEL_V2S_TURB_SEND_FIELD;

//------------------------------------------------------------------------------
// sSURFEL_TURB_DATA
//------------------------------------------------------------------------------
template<typename SFL_TYPE_TAG>
struct tSURFEL_V2S_TURB_DATA {
  EXTRACT_SURFEL_TRAITS
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_nu;
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_tke_pde;
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_eps_pde;
  tSFL_VAR<STP_PHYS_VARIABLE, N> m_tke_sample;

  VOID print_surfel_data(std::ostream& os){
    using namespace UBLK_SURFEL_PRINT_UTILS;
    sim_print_data_header(os, "SURFEL_V2S_TURB_DATA");
    sim_print(os, "m_nu", m_nu);
    sim_print(os, "m_tke_pde", m_tke_pde);
    sim_print(os, "m_eps_pde", m_eps_pde);
    sim_print(os, "m_tke_sample", m_tke_sample);
  }

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID read_ckpt()  { read_lgi(*this);       }
  VOID write_ckpt() { write_ckpt_lgi(*this); }

  VOID reset() {
    memset(this, 0, sizeof(tSURFEL_V2S_TURB_DATA));
  }

  __DEVICE__ VOID reset(int soxor,
                        BOOLEAN is_even_or_odd,
                        BOOLEAN is_timestep_even) {
    if (!is_even_or_odd || is_timestep_even) {
      m_nu[soxor] = 0.0F;
      m_tke_pde[soxor] = 0.0F;
      m_eps_pde[soxor] = 0.0F;
      m_tke_sample[soxor] = 0.0F;
    }
  }

  VOID reflect_to_real_surfel(tSURFEL_V2S_TURB_DATA *mirror_v2s_turb_data) {
    m_nu      += mirror_v2s_turb_data->m_nu;
    m_tke_pde += mirror_v2s_turb_data->m_tke_pde;
    m_eps_pde += mirror_v2s_turb_data->m_eps_pde;
#if BUILD_6X_SOLVER
    m_tke_sample += mirror_v2s_turb_data->m_tke_sample;
#endif
  }

  VOID reflect_to_mirror_surfel(tSURFEL_V2S_TURB_DATA *real_v2s_turb_data) {
    m_nu      = real_v2s_turb_data->m_nu;
    m_tke_pde = real_v2s_turb_data->m_tke_pde;
    m_eps_pde = real_v2s_turb_data->m_eps_pde;
  }

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_V2S_TURB_DATA *even_v2s_turb_data,
                             asINT32 esoxor, asINT8 osoxor) {
  }

  __HOST__DEVICE__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_TURB_DATA *even_v2s_turb_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse,
                                        asINT32 osoxor, asINT32 esoxor)
  {
    m_tke_pde[osoxor] = even_v2s_turb_data->m_tke_pde[esoxor] * mme_weight_inverse;
    m_eps_pde[osoxor] = even_v2s_turb_data->m_eps_pde[esoxor] * mme_weight_inverse;
    m_tke_sample[osoxor]  = even_v2s_turb_data->m_tke_sample[esoxor] *
                            (g_use_lrf_s2s_sampling_turb ? s2s_sampling_weight_inverse : mme_weight_inverse);

    auto& simc = get_simc_ref();
    if(simc.thermal_feedback_is_on)
      m_nu[osoxor] = even_v2s_turb_data->m_nu[esoxor] * mme_weight_inverse * mme_weight_inverse / even_density;
    else
      m_nu[osoxor] = even_v2s_turb_data->m_nu[esoxor] * mme_weight_inverse;
  }

  __HOST__
  VOID pre_advect_init_copy_even_to_odd(tSURFEL_V2S_TURB_DATA *even_v2s_turb_data,
                                        STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse,
                                        STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);
  
  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_V2S_TURB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_V2S_TURB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_V2S_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_TURB_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(&field->m_nu, &m_nu, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_nu));
    memcpy(&field->m_tke_pde, &m_tke_pde, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_tke_pde));
    memcpy(&field->m_eps_pde, &m_eps_pde, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_eps_pde));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_V2S_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_V2S_TURB_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(&m_nu, &field->m_nu, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_nu));
    memcpy(&m_tke_pde, &field->m_tke_pde, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_tke_pde));
    memcpy(&m_eps_pde, &field->m_eps_pde, struct_field_size(SURFEL_V2S_TURB_SEND_FIELD,m_eps_pde));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};

using sSURFEL_V2S_TURB_DATA = tSURFEL_V2S_TURB_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_V2S_TURB_DATA = sSURFEL_V2S_TURB_DATA*;

template<>
__HOST__ INLINE
VOID tSURFEL_V2S_TURB_DATA<SFL_SDFLOAT_TYPE_TAG>::pre_advect_init_copy_even_to_odd(tSURFEL_V2S_TURB_DATA<SFL_SDFLOAT_TYPE_TAG> *even_v2s_turb_data,
                                                                STP_PHYS_VARIABLE even_density,
                                                                STP_SURFEL_WEIGHT mme_weight_inverse,
                                                                STP_SURFEL_WEIGHT s2s_sampling_weight_inverse) {
  this->pre_advect_init_copy_even_to_odd(even_v2s_turb_data, even_density, mme_weight_inverse, s2s_sampling_weight_inverse, 0, 0);
}

#if BUILD_GPU
INIT_MSFL(tSURFEL_V2S_TURB_DATA);
#endif


// Although we use turb_intensity and turb_length_scale here, it also works for
// turb_kinetic_energy and turb_dissipation since they are within a union
typedef struct sSURFEL_BC_TURB_SEND_FIELD
{
  STP_PHYS_VARIABLE turb_intensity;
  STP_PHYS_VARIABLE turb_length_scale;
} *SURFEL_BC_TURB_SEND_FIELD;

template<typename SFL_TYPE_TAG>
struct tBC_TURB_DATA {
  EXTRACT_SURFEL_TRAITS
  union
  {
    struct
    {
      tSFL_VAR<STP_PHYS_VARIABLE, N> turb_kinetic_energy;
      tSFL_VAR<STP_PHYS_VARIABLE, N> turb_dissipation;
    } k;
    struct
    {
      tSFL_VAR<STP_PHYS_VARIABLE, N> turb_intensity;
      tSFL_VAR<STP_PHYS_VARIABLE, N> turb_length_scale;
    } i;
  } u;

  VOID reset() {
    memset(this, 0, sizeof(tBC_TURB_DATA));
  }

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_BC_TURB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_BC_TURB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_BC_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_BC_TURB_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(&field->turb_intensity, &u.i.turb_intensity, struct_field_size(SURFEL_BC_TURB_SEND_FIELD, turb_intensity));
    memcpy(&field->turb_length_scale, &u.i.turb_length_scale, struct_field_size(SURFEL_BC_TURB_SEND_FIELD, turb_length_scale));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_BC_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_BC_TURB_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(&u.i.turb_intensity, &field->turb_intensity, struct_field_size(SURFEL_BC_TURB_SEND_FIELD, turb_intensity));
    memcpy(&u.i.turb_length_scale, &field->turb_length_scale, struct_field_size(SURFEL_BC_TURB_SEND_FIELD, turb_length_scale));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }
};

using sBC_TURB_DATA = tBC_TURB_DATA<SFL_SDFLOAT_TYPE_TAG>;

#if BUILD_GPU
using sMSFL_BC_TURB_DATA = tBC_TURB_DATA<MSFL_SDFLOAT_TYPE_TAG>;
INIT_MSFL(tBC_TURB_DATA);
#endif

template<typename SFL_TYPE_TAG>
struct tSURFEL_TURB_DATA
{
  EXTRACT_SURFEL_TRAITS
  // The following elements must be in the same order as the send field to
  // workaround a bug in Intel MPI (see PR22689)
  //STP_PHYS_VARIABLE nu;
  tSFL_VAR<STP_PHYS_VARIABLE, N> ltt_index;

  //for flux-limiter
  //STP_PHYS_VARIABLE tke_pde;
  //STP_PHYS_VARIABLE eps_pde;

  //STP_PHYS_VARIABLE tke_sample;
  tSFL_VAR<STP_PHYS_VARIABLE, N> s_mag;  // s_mag and gamma_swirl must be in turb_data since they are not always populated in v2s due to subcycling
  tSFL_VAR<STP_PHYS_VARIABLE, N> uns;
  tSFL_VAR<STP_PHYS_VARIABLE, N> gamma_swirl;
  
  tSFL_VAR<STP_PHYS_VARIABLE, N> dpds_ur;
  tSFL_VAR<STP_PHYS_VARIABLE, N> tke_sample_prime;

  tSFL_VAR<STP_PHYS_VARIABLE, N> s_mag_prime;
  tSFL_VAR<STP_PHYS_VARIABLE, N> gamma_swirl_prime;

  tBC_TURB_DATA<SFL_TYPE_TAG> m_bc_turb_data;

  VOID print_surfel_data(std::ostream& os){

    using namespace UBLK_SURFEL_PRINT_UTILS;

    sim_print_data_header(os, "SURFEL_TURB_DATA");
    sim_print(os, "s_mag", s_mag);
    sim_print(os, "uns", uns);
    sim_print(os, "gamma_swirl", gamma_swirl);
    sim_print(os, "dpds_ur", dpds_ur);
    sim_print(os, "tke_sample_prime", tke_sample_prime);
  }

  VOID pre_advect_init() {
    msg_internal_error("Never should be called."); 
  }

  VOID pre_advect_init_copy_even_to_odd(tSURFEL_TURB_DATA *even_surfel_turb_data, 
					STP_PHYS_VARIABLE even_density,
                                        STP_SURFEL_WEIGHT mme_weight_inverse, 
					STP_SURFEL_WEIGHT s2s_sampling_weight_inverse);

  __HOST__DEVICE__
  VOID seed_copy_even_to_odd(tSURFEL_TURB_DATA *even_surfel_turb_data,
                             asINT32 esoxor, asINT32 osoxor) { } 


  VOID init(DGF_SURFEL_DESC surfel_desc) {
    memset(this, 0, sizeof(*this));
    dpds_ur          = 0;
    ltt_index        = 1.0;
  } 

  uINT64 ckpt_len() { return sizeof(*this);  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }

  VOID reflect_from_mirror_surfel_to_real_surfel(tSURFEL_TURB_DATA *mirror_turb_data,
                                                 STP_LATVEC_MASK latvec_state_mask,
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);
  VOID reflect_from_real_surfel_to_mirror_surfel(tSURFEL_TURB_DATA *source_turb_data, 
                                                 STP_LATVEC_MASK latvec_state_mask,
						 STP_STATE_INDEX reflected_latvec_pair[N_SURFEL_PGRAM_VOLUMES],
                                                 STP_PHYS_VARIABLE velocity_mirror_sign_factor[N_AXES]);

  static VOID add_send_size(asINT32 &send_size, asINT32 &tpde_send_size) {
    send_size += (sizeof(sSURFEL_TURB_SEND_FIELD) / sizeof(sdFLOAT));
    tpde_send_size += (sizeof(sSURFEL_TURB_SEND_FIELD) / sizeof(sdFLOAT));
  }

  VOID fill_send_buffer(sSURFEL_SEND_DATA_INFO &send_data_info)
  {
    SURFEL_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_TURB_SEND_FIELD>(send_data_info.send_buffer);
    memcpy(&field->m_ltt_index, &ltt_index, struct_field_size(SURFEL_TURB_SEND_FIELD,m_ltt_index));
    field++;
    send_data_info.send_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

  VOID expand_recv_buffer(sSURFEL_RECV_DATA_INFO &recv_data_info)
  {
    SURFEL_TURB_SEND_FIELD field = reinterpret_cast<SURFEL_TURB_SEND_FIELD>(recv_data_info.recv_buffer);
    memcpy(&ltt_index, &field->m_ltt_index, struct_field_size(SURFEL_TURB_SEND_FIELD,m_ltt_index));
    field++;
    recv_data_info.recv_buffer = reinterpret_cast<sdFLOAT*>(field);
  }

};

using sSURFEL_TURB_DATA = tSURFEL_TURB_DATA<SFL_SDFLOAT_TYPE_TAG>;
using SURFEL_TURB_DATA = sSURFEL_TURB_DATA*;

#if BUILD_GPU
INIT_MSFL(tSURFEL_TURB_DATA);
#endif
static const asINT32 SURFEL_TURB_DATA_CKPT_SIZE = sizeof(sSURFEL_TURB_DATA);

//------------------------------------------------------------------------------
// sSAMPLING_SURFEL_TURB_DATA
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL_TURB_DATA
{
  STP_PHYS_VARIABLE turb_ke;
  STP_PHYS_VARIABLE turb_df;
  STP_PHYS_VARIABLE eddy_visc;
  STP_PHYS_VARIABLE strain_rate_mag;
  
  VOID pre_advect_init() {
    memset(this, 0, sizeof(*this));
  }
  VOID pre_advect_init_copy_even_to_odd(sSAMPLING_SURFEL_TURB_DATA *even_surfel_turb_data);
  VOID init() {
    memset(this, 0, sizeof(*this));
  }
  VOID write_ckpt() { write_ckpt_lgi(*this); }
  VOID read_ckpt()  { read_lgi(*this);       }
  uINT64 ckpt_len() { return sizeof(*this);  }
} *SAMPLING_SURFEL_TURB_DATA;

} //inline namespace SIMULATOR_NAMESPACE
#endif //_SIMENG_TURB_SOLVER_DATA_H
