/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "film_solver_stencils.h"
#include "surfel.h"
#include "surfel_table.h"
#include "comm_groups.h"
#include "parse_shob_descs.h"
#include "particle_sim_info.h"
#include "film_comm.h"
#include "strand_mgr.h"
#include "comm_utils.h"

sSURFEL_FILM_RECV_FSET g_surfel_film_recv_fset;
sSURFEL_FILM_SEND_FSET g_surfel_film_send_fset;

sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT::sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT(DGF_VERTEX_INDEX local_id) {
  global_index = g_surfel_vertices_info.global_from_local_index(local_id);
}

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
VOID sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT::copy_to_surfel(sSURFEL* surfel) {
  surfel->set_id(this->id);
  surfel->set_scale(this->scale);
  surfel->m_home_sp = this->source_sp;
  surfel->set_even_odd_mask(this->even_odd);
  vcopy(surfel->centroid, this->centroid);
  vcopy(surfel->normal, this->normal);
  surfel->area = this->area;
  surfel->p_data()->n_vertices =  this->n_vertices;
  surfel->set_backside(this->is_backside);
  surfel->m_face_index = this->face_index;
  surfel->p_data()->s.surface_material_id = this->surface_material_id;
  if (g_surfel_ids.find(this->clone_surfel_id)) {
    cassert(surfel->even_odd_data());
    SURFEL clone_surfel = g_surfel_ids.find(this->clone_surfel_id);
    cassert(clone_surfel);
    surfel->even_odd_data()->m_clone_surfel.set_clone_surfel(clone_surfel);
    cassert(clone_surfel->is_even_or_odd());
    clone_surfel->even_odd_data()->m_clone_surfel.set_clone_surfel(surfel);
  }
  if (g_surfel_ids.find(this->opposite_surfel_id)) {
    SURFEL opp_surfel = g_surfel_ids.find(this->opposite_surfel_id);
    cassert(opp_surfel);
    surfel->m_opposite_surfel = opp_surfel;
    opp_surfel->m_opposite_surfel = surfel;
  } else
    surfel->m_opposite_surfel = nullptr;
}
#else
VOID sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT::copy_to_surfel(sSURFEL* surfel) {
  surfel->set_id(this->id);
  surfel->set_scale(this->scale);
  surfel->m_home_sp = this->source_sp;
  surfel->m_even_odd = this->even_odd;
  vcopy(surfel->centroid, this->centroid);
  vcopy(surfel->normal, this->normal);
  surfel->area = this->area;
  surfel->stencil()->n_vertices=  this->n_vertices;
  surfel->set_backside(this->is_backside);
  surfel->m_face_index = this->face_index;
  if (g_surfel_ids.find(this->clone_surfel_id)) {
    cassert(surfel->even_odd_data());
    SURFEL clone_surfel = g_surfel_ids.find(this->clone_surfel_id);
    cassert(clone_surfel);
    surfel->even_odd_data()->m_clone_surfel.set_clone_surfel(clone_surfel);
    cassert(clone_surfel->is_even_or_odd());
    clone_surfel->even_odd_data()->m_clone_surfel.set_clone_surfel(surfel);
  }
  if (g_surfel_ids.find(this->opposite_surfel_id)) {
    SURFEL opp_surfel = g_surfel_ids.find(this->opposite_surfel_id);
    cassert(opp_surfel);
    surfel->m_opposite_surfel = opp_surfel;
    opp_surfel->m_opposite_surfel = surfel;
  } else
    surfel->m_opposite_surfel = nullptr;
}
#endif

inline namespace SIMULATOR_NAMESPACE
{
asINT32 surfel_type_from_desc(BOOLEAN is_S2S_destination,
			      STP_EVEN_ODD even_odd,
			      auINT32 flags,
			      BOOLEAN is_ghost,
			      BOOLEAN has_ghosts,
			      BOOLEAN is_fringe,
                              BOOLEAN is_conduction_shell,
                              BOOLEAN is_conduction_interface,
                              BOOLEAN is_radiation);
}

SURFEL add_film_only_ghost_surfel(sFO_SURFEL_INIT_DATA_COLLECTION &data) {
  // Allocate a surfel
  asINT32 surfel_type = surfel_type_from_desc(false, data.even_odd, 0,
					      false, false, false, false, false, false);


#ifdef VINIT_TO_DO
  // Cheating with MLRF_TYPE to avoid allocating dynamics data
#endif
  /* This surfel has to be necessarily non-conduction i.e., does not
   * interact with conduction volume */
  constexpr BOOLEAN interacts_with_conduction_volume = FALSE;
  SURFEL surfel = (SURFEL) malloc(sSURFEL::size(surfel_type, false, STP_MLRF_SURFEL_TYPE));

#ifdef VINIT_TODO
  We need to get the dynamics type from the real surfel
#endif
  surfel->m_surfel_attributes.m_surfel_type = surfel_type;
  surfel->m_surfel_attributes.m_dynamic_bits = 0;
  surfel->set_wall(true);
  surfel->set_inlet_or_outlet(false);
  surfel->set_scale(data.scale);
  surfel->set_film_only_ghost(true);
  surfel->p_data()->init(nullptr);
  data.copy_to_surfel(surfel);  //Initialize the new surfel's properties (including is_backside and face index)
  // Table is vector of surfels in ID order. Cannot add to it at this stage.
  //g_surfel_table.add(surfel);
  g_surfel_ids.insert_new_id(surfel);

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  *(surfel->p_data()->s.was_added_to_stencil()) = -1;
#endif
  return surfel;
}

bool compare(const sFO_SURFEL_INIT_DATA_COLLECTION& first, const sFO_SURFEL_INIT_DATA_COLLECTION& second)
{
  return (first.id < second.id);
}

bool is_equal(const sFO_SURFEL_INIT_DATA_COLLECTION& first, const sFO_SURFEL_INIT_DATA_COLLECTION& second) {
  return (first.id == second.id);
}
VOID process_second_hand_comm_surfels(std::list<sFO_SURFEL_INIT_DATA_COLLECTION> *second_hand_comm_surfel_list, asINT32 pass)
{
  //This function creates ghost surfels and comm groups for surfels that this SP became aware of by being notified
  //from a third party SP (one that did not own the corresponding dynamics surfels).
  //A new pair of send and receive comm groups are needed to handle such surfels and they are created here.

  //Remove duplicate surfels in the list and add the unique surfel IDs to g_surfel_ids.
  { //Restrict the scope of the iterator so its name can be reused.
    std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = second_hand_comm_surfel_list->begin();
    while(i != second_hand_comm_surfel_list->end()) {
      sFO_SURFEL_INIT_DATA_COLLECTION data = *i;
      if (g_surfel_ids.is_id_in_set(data.id)) {
        i = second_hand_comm_surfel_list->erase(i);
      } else {
        ++i;
      }
    }
  }
  second_hand_comm_surfel_list->sort(compare);
  second_hand_comm_surfel_list->unique(is_equal);

  //Determine how many unique SP's will be sources to this set of new ghosts.
  asINT32 num_unique_sps = 0;
  std::list<asINT32> unique_sps_list;
  {
    std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = second_hand_comm_surfel_list->begin();
    while(i != second_hand_comm_surfel_list->end()) {
      sFO_SURFEL_INIT_DATA_COLLECTION data = *i;
      asINT32 source_sp = data.source_sp;
      if (find(unique_sps_list.begin(),unique_sps_list.end(),source_sp) == unique_sps_list.end() ) {
        unique_sps_list.push_back(source_sp);
        num_unique_sps++;
      }
      i++;
    }
  }

  //Copy the list of unique SPs to a vector now that the size is known.
  std::vector<asINT32> unique_sps;
  unique_sps.reserve(num_unique_sps);
  unique_sps.insert(unique_sps.begin(), unique_sps_list.begin(), unique_sps_list.end());

  //Count how many ghost surfels will be sourced from each sp.
  std::vector<sINT32> num_surfels_per_sp_to_send(total_sps, 0);
  {
    std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = second_hand_comm_surfel_list->begin();
    while(i != second_hand_comm_surfel_list->end()) {
      sFO_SURFEL_INIT_DATA_COLLECTION data = *i;
      asINT32 source_sp = data.source_sp;
      num_surfels_per_sp_to_send[source_sp]++;
      i++;
    }
  }

  //For each sp that will be sourcing these ghost surfels, create a vector of
  //surfel IDs to use as a send buffer so the source SP can be informed of which surfels it
  //needs to form a matching comm send group with.
  std::vector<sINT32> *source_surfel_ids_send_buffers = nullptr;
  if (num_unique_sps > 0)
    source_surfel_ids_send_buffers = new std::vector<sINT32>[num_unique_sps];

  //Iterate through the input list and populate the above send buffers with surfel IDs.
  //Also create the film only ghost surfels and populate comm receive groups with them.
  {
    std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = second_hand_comm_surfel_list->begin();
    while(i != second_hand_comm_surfel_list->end()) {
      sFO_SURFEL_INIT_DATA_COLLECTION data = *i;
      asINT32 source_sp = data.source_sp;
      asINT32 source_sp_index = find(unique_sps.begin(), unique_sps.end(), source_sp) - unique_sps.begin();
      source_surfel_ids_send_buffers[source_sp_index].push_back(data.id); //The correct capacity was already reserved.

      //The following call will create a new film-only ghost surfel group if it
      //doesn't already exist and return a pointer to a new fo-surfel object.  It
      //will also create a new receive group for film-only ghost surfel comm if one does not already exist.
      if (!g_strand_mgr.m_neighbor_sp_map.is_rank_present(data.source_sp)) {
        g_strand_mgr.m_neighbor_sp_map.add_nsp(data.source_sp);
      }
      cNEIGHBOR_SP source_neigh_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp(data.source_sp);

      SURFEL film_only_ghost_surfel = add_film_only_ghost_surfel(data);
      SURFEL_FILM_SEND_GROUP sgroup = g_surfel_film_send_fset.create_group(data.scale, source_neigh_sp);
      sgroup->add_quantum(film_only_ghost_surfel);

#ifdef ENABLE_CONNECTIVITY_INIT_DATA
      //Do something with the connectivity info.
      append_connectivity_with_additional_vertices(&data, film_only_ghost_surfel);
#endif
      i = second_hand_comm_surfel_list->erase(i);
    }
  }

  //Post send requests to each SP for the surfel counts, under a tag of
  //eMPI_FILM_ONLY_GHOST_SURFEL_COMM_GROUP_TAG.
  std::vector<MPI_Request> surfel_count_send_requests(total_sps, MPI_REQUEST_NULL); //Every SP gets a count
  ccDOTIMES(source_sp, total_sps) {
    if (source_sp != my_proc_id) {
      //Send a count to every SP
#if defined(_EXA_IMPI)
      MPI_Issend(&(num_surfels_per_sp_to_send[source_sp]),
                1, eMPI_sINT32, source_sp,
                eMPI_FILM_ONLY_GHOST_SURFEL_COMM_GROUP_TAG,
                eMPI_sp_cp_comm,
                &(surfel_count_send_requests[source_sp]));
#else
      MPI_Isend(&(num_surfels_per_sp_to_send[source_sp]),
                1, eMPI_sINT32, source_sp,
                eMPI_FILM_ONLY_GHOST_SURFEL_COMM_GROUP_TAG,
                eMPI_sp_cp_comm,
                &(surfel_count_send_requests[source_sp]));
#endif
    }
  }

  //Receive the surfel count from every remote sp (even if they will not be sending any surfel IDs).
  std::vector<sINT32> num_ids_to_expect(total_sps, 0);
  ccDOTIMES(remote_sp, total_sps) {
    if (remote_sp != my_proc_id) {
      MPI_Status status;
      MPI_Recv(&(num_ids_to_expect[remote_sp]), //Blocking receive good enough here.
               1,
               eMPI_sINT32,
               remote_sp,
               eMPI_FILM_ONLY_GHOST_SURFEL_COMM_GROUP_TAG,
               eMPI_sp_cp_comm,
               &status);
    }
  }

  ccDOTIMES(source_sp, total_sps) {
    if (source_sp != my_proc_id)
      complete_mpi_request_while_processing_cp_messages(&(surfel_count_send_requests[source_sp]), MPI_SLEEP_LONG);
  }

  //Count how many SPs will be sending IDs.
  asINT32 num_surfel_id_messages_to_receive = 0;
  ccDOTIMES(remote_sp, total_sps) {
    if (num_ids_to_expect[remote_sp] > 0)
      num_surfel_id_messages_to_receive++;
  }

  //For each remote SP that sent a non zero surfel count, post a receive request for the vector of surfel ids.
  std::vector<MPI_Request> surfel_ids_receive_requests(num_surfel_id_messages_to_receive, MPI_REQUEST_NULL);
  std::vector<sINT32>* surfel_ids_receive_buffers = nullptr;
  if (num_surfel_id_messages_to_receive > 0)
    surfel_ids_receive_buffers = new std::vector<sINT32>[num_surfel_id_messages_to_receive];
  { //Restrict the scope of message_index.
    asINT32 message_index = 0;
    ccDOTIMES(remote_sp, total_sps) {
      if (remote_sp != my_proc_id)
        if (num_ids_to_expect[remote_sp] > 0) {
          surfel_ids_receive_buffers[message_index].resize(num_ids_to_expect[remote_sp]);
          MPI_Irecv(surfel_ids_receive_buffers[message_index].data(),
                    num_ids_to_expect[remote_sp],
                    eMPI_sINT32,
                    remote_sp,
                    eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
                    eMPI_sp_cp_comm,
                    &(surfel_ids_receive_requests[message_index]));
          message_index++;
        }
    }
  }

  std::vector<MPI_Request> surfel_ids_send_requests(num_unique_sps, MPI_REQUEST_NULL); //Only some SPs get sent IDs
  ccDOTIMES(source_sp, total_sps) {
    if (source_sp != my_proc_id) {
      //Only send the IDs to SPs that need them
      if (num_surfels_per_sp_to_send[source_sp] > 0) {
        asINT32 source_sp_index = find(unique_sps.begin(), unique_sps.end(), source_sp) - unique_sps.begin();
#if defined(_EXA_IMPI)
        MPI_Issend(&source_surfel_ids_send_buffers[source_sp_index][0],
                  num_surfels_per_sp_to_send[source_sp],
                  eMPI_sINT32,
                  source_sp,
                  eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
                  eMPI_sp_cp_comm,
                  &(surfel_ids_send_requests[source_sp_index]));
#else
        MPI_Isend(&source_surfel_ids_send_buffers[source_sp_index][0],
                  num_surfels_per_sp_to_send[source_sp],
                  eMPI_sINT32,
                  source_sp,
                  eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
                  eMPI_sp_cp_comm,
                  &(surfel_ids_send_requests[source_sp_index]));
#endif
      }
    }
  }


  //Complete the surfel ID send requests.
  ccDOTIMES(message_index, num_unique_sps) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_send_requests[message_index]), MPI_SLEEP_LONG);
  }

  //Complete the surfel ID receive request.
  ccDOTIMES(message_index, num_surfel_id_messages_to_receive) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_receive_requests[message_index]), MPI_SLEEP_LONG);
  }
  //Create the send groups (The receive groups were created above as new film only ghost surfels were allocated).
  {//Restrict the scope of message_index.
    asINT32 message_index = 0;
    ccDOTIMES(remote_sp, total_sps) {
      if (num_ids_to_expect[remote_sp] > 0) {
        ccDOTIMES(nth_element, num_ids_to_expect[remote_sp]) {
          SHOB_ID surfel_id = surfel_ids_receive_buffers[message_index].at(nth_element);
          if (!g_strand_mgr.m_neighbor_sp_map.is_rank_present(remote_sp)) {
            g_strand_mgr.m_neighbor_sp_map.add_nsp(remote_sp);
          }
          cNEIGHBOR_SP dest_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp(remote_sp);

          sSURFEL *dynamics_surfel = regular_surfel_from_id(surfel_id);
          if (dynamics_surfel == NULL) {
            msg_internal_error("Surfel %d could not be found on SP%d while computing film solver stencils second hand comm.", surfel_id, my_proc_id);
          }
          SURFEL_FILM_RECV_GROUP film_only_recv_group =
            g_surfel_film_recv_fset.create_group(dynamics_surfel->scale(), dest_sp);
          if (film_only_recv_group->m_set_of_surfel_ids.find(surfel_id) ==
              film_only_recv_group->m_set_of_surfel_ids.end()) {
            dynamics_surfel->set_film_only_fringe(TRUE);
            film_only_recv_group->add_quantum(dynamics_surfel);
            film_only_recv_group->m_set_of_surfel_ids.insert(surfel_id);
          }
        }
        message_index++;
      }
    }
  }
  // memory leak of buffers
  delete [] surfel_ids_receive_buffers;
  delete [] source_surfel_ids_send_buffers;
}

template <typename GROUP_TYPE>
static sFILM_STENCIL_SURFEL_VECTOR fill_nearby_surfels(GROUP_TYPE rgroup, bool SKIP_FILM_ONLY_GHOST_SURFELS = false) {
    sFILM_STENCIL_SURFEL_VECTOR nearby_surfel_references;
    for (auto& quantum: rgroup->quantums()) {
      SURFEL surfel = quantum.m_surfel;
      if (SKIP_FILM_ONLY_GHOST_SURFELS && surfel->is_film_only_ghost())
        continue;


      //Compile a list of surfels in this comm group's surfel's stencil.
      //Don't add the first surfel in a dynamics surfel's stencil list since it is
      //the same surfel and the other side already has a corresponding ghost surfel for it (hence the -1 and  +1 below).
      ccDOTIMES(nth_stencil_surfel, surfel->p_data()->nearby_surfels->size() - 1 ) {
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
        SURFEL neighbor_surfel = surfel->p_data()->nearby_surfels->at(nth_stencil_surfel + 1);

        if (std::find(nearby_surfel_references.begin(), nearby_surfel_references.end(), neighbor_surfel) == nearby_surfel_references.end()) {

          //also don't add other ghost surfels as they already have remote dynamics surfels
	  if (TRUE) {
            nearby_surfel_references.push_back(neighbor_surfel);
          }
        }

        //if the surfel has an odd_even pair, add it too
        SURFEL neighbor_clone = neighbor_surfel->clone_surfel();

        if (neighbor_clone != NULL)
          if (std::find(nearby_surfel_references.begin(), nearby_surfel_references.end(), neighbor_clone) == nearby_surfel_references.end()) {
            //also don't add other ghost surfels as they already have remote dynamics surfels
	    if (TRUE) {
              nearby_surfel_references.push_back(neighbor_clone);
#if 0

              printf("Added clone for surfel %d at %g %g %g (clone id %d).\n",neighbor_surfel->id(), neighbor_clone->centroid[0], neighbor_clone->centroid[1], neighbor_clone->centroid[2], neighbor_clone->id());
              //mark any film only ghost surfels that are created...although the simerr may be disabled after too many
              //theres an MPI barrier that causes a hang if a simerr is issued at this stage of initialization.
              //if you need this you must exasignal -kill after the hang occurs.
              //simerr_report_error_code( SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, neighbor_surfel->scale(), neighbor_surfel->centroid,"forward");
#endif
            }
          }
#else
	sSTENCIL_SURFEL neighbor_surfel = surfel->p_data()->nearby_surfels->at(nth_stencil_surfel + 1);
        
	if (std::find(nearby_surfel_references.begin(), nearby_surfel_references.end(), neighbor_surfel) == nearby_surfel_references.end()) {

          //also don't add other ghost surfels as they already have remote dynamics surfels
	  if (TRUE) {
            nearby_surfel_references.push_back(neighbor_surfel);
          }
        }

        //if the surfel has an odd_even pair, add it too
        SURFEL neighbor_clone_surfel = neighbor_surfel.get_surfel()->clone_surfel();

        if (neighbor_clone_surfel != NULL){
          sSTENCIL_SURFEL neighbor_clone = sSTENCIL_SURFEL(neighbor_clone_surfel);
          if (std::find(nearby_surfel_references.begin(), nearby_surfel_references.end(), neighbor_clone) == nearby_surfel_references.end()) {
            //also don't add other ghost surfels as they already have remote dynamics surfels
	    if (TRUE) {
              nearby_surfel_references.push_back(neighbor_clone);
#if 0

              printf("Added clone for surfel %d at %g %g %g (clone id %d).\n",neighbor_surfel->id(), neighbor_clone->centroid[0], neighbor_clone->centroid[1], neighbor_clone->centroid[2], neighbor_clone->id());
              //mark any film only ghost surfels that are created...although the simerr may be disabled after too many
              //theres an MPI barrier that causes a hang if a simerr is issued at this stage of initialization.
              //if you need this you must exasignal -kill after the hang occurs.
              //simerr_report_error_code( SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, neighbor_surfel->scale(), neighbor_surfel->centroid,"forward");
#endif
            }
          }
	}
#endif
      }
    }
    return nearby_surfel_references;
}

SURFEL_FILM_SEND_GROUP find_group(asINT32 rank, SCALE scale,
                                  std::vector<SURFEL_FILM_SEND_GROUP> &send_groups) {
  for (SURFEL_FILM_SEND_GROUP group:  send_groups) {
    if ((group->scale() == scale) &&
        (group->m_dest_sp.rank() == rank)) {
      return group;
    }
  }
  return nullptr;
}

//The following retrieve_ghost_side_interface_stencils routine creates an additional layer of ghost surfels that are needed by the film solver.
//It works by, for each film recv group(surfel send group), informing the complement SP about a list of surfels that are "nearby" to the
//dynamics surfels already in the comm group.  With this information, additional comm groups (film only comm groups) are created.
//What constitutes nearby is determined by initializing each surfel's film solver stencil from the pool of locally available surfels.
//After this routine is complete, the surfel's film solver stencils are recomputed from all surfels including the new film only ghost surfels.

//There is also a compliment routine after the following that computes the set of surfels nearby to the ghost surfels in a
//comm RECV group (instead of dynamics surfels in the comm SEND groups).  This ensures that both sides of
//the domain interface have the required film only ghost surfels.

VOID retrieve_ghost_side_interface_stencils(asINT32 pass) {
  //To build the additional layer of ghost surfels on each comm-receive interface:
  //1) Receive from the dynamics surfel, a list of surfels in its stencil.
  //2) Filter the list of surfels for ones which already have local ghosts.
  //3) Create film-only ghost surfels for the remainder and the associated film_only_comm_recv_group.
  //4) Report back which surfels were used and create a film_only_comm_send_group.

  g_surfel_ids.construct_set_of_ids();
  std::list<sFO_SURFEL_INIT_DATA_COLLECTION> second_hand_comm_surfel_list;

  DO_SURFEL_FILM_RECV_GROUPS(rgroup)  {
    //collect all unique surfels in the stencil of surfels in this comm group.
    sFILM_STENCIL_SURFEL_VECTOR nearby_surfel_references = fill_nearby_surfels(rgroup, false);

    rgroup->fill_outbound_surfels(nearby_surfel_references);
    //post send requests for the send buffer size
#if defined(_EXA_IMPI)
    MPI_Issend(&rgroup->outbound_surfel_count, 1,  MPI_INT, rgroup->m_source_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &rgroup->outbound_surfel_count_send_request);
#else
    MPI_Isend(&rgroup->outbound_surfel_count, 1,  MPI_INT, rgroup->m_source_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &rgroup->outbound_surfel_count_send_request);
#endif
    //post send of surfel send elements
#if defined(_EXA_IMPI)
    MPI_Issend(rgroup->outbound_surfel_data.data(), rgroup->outbound_surfel_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->outbound_surfel_data_send_request);
#else
    MPI_Isend(rgroup->outbound_surfel_data.data(), rgroup->outbound_surfel_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->outbound_surfel_data_send_request);
#endif

#ifdef ENABLE_CONNECTIVITY_INIT_DATA
    rgroup->fill_outbound_vertices(nearby_surfel_references);

    //Send the buffer of vertex data (global index and coordinates).
#if defined(_EXA_IMPI)
    MPI_Issend(rgroup->outbound_surfel_vertex_data.data(),
              rgroup->outbound_surfel_vertex_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->outbound_surfel_vertex_data_send_request);
#else
    MPI_Isend(rgroup->outbound_surfel_vertex_data.data(),
              rgroup->outbound_surfel_vertex_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->outbound_surfel_vertex_data_send_request);
#endif
#endif

  }

  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    //post receive of the incoming surfel count
    MPI_Irecv(&sgroup->inbound_surfel_count, 1, MPI_INT, sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &sgroup->inbound_surfel_count_recv_request);
  }

  //----complete receives for surfel counts and post receive requests for the surfel info-----
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->inbound_surfel_count_recv_request, MPI_SLEEP_LONG);

    //allocate a buffer for receiving the surfel data
    sgroup->inbound_surfel_data.resize(sgroup->inbound_surfel_count);

    //post receive request for surfel data
    MPI_Irecv(sgroup->inbound_surfel_data.data(), sgroup->inbound_surfel_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->inbound_surfel_data_recv_request);
  }

  //--------complete sends of the surfel counts-----
  DO_SURFEL_FILM_RECV_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->outbound_surfel_count_send_request, MPI_SLEEP_LONG);
  }

  //-----complete receives of the surfel data--------
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->inbound_surfel_data_recv_request, MPI_SLEEP_LONG);
  }

  //----complete sends of the surfel data-----
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    complete_mpi_request_while_processing_cp_messages(&rgroup->outbound_surfel_data_send_request, MPI_SLEEP_LONG);
  }

#ifdef ENABLE_CONNECTIVITY_INIT_DATA
  //----receive vertex data-------
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    //count the number of vertices to receive
    sgroup->allocate_inbound_vertices() ;

    //post receive request for surfel vertex data
    MPI_Irecv(sgroup->inbound_surfel_vertex_data.data(),
              sgroup->inbound_surfel_vertex_data_size(),
              MPI_BYTE,
              sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->inbound_surfel_vertex_data_recv_request);

  }

  //----complete receive of the vertex buffers----
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->inbound_surfel_vertex_data_recv_request, MPI_SLEEP_LONG);
  }
  //complete sends of the vertex buffers
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    complete_mpi_request_while_processing_cp_messages(&rgroup->outbound_surfel_vertex_data_send_request, MPI_SLEEP_LONG);
  }
#endif

  std::vector<SURFEL_FILM_SEND_GROUP> new_send_groups;
  //Now each surfel RECV group has a buffer with the surfels from the source SP's stencils
  //(it also has an array of vertex records for vertices in the surfels).
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    //Some of these surfels already have local ghosts; detect and remove the redundant elements.
    asINT32 vertex_bookmark = 0;
    ccDOTIMES(nth_element, sgroup->inbound_surfel_count) {
      sFO_SURFEL_INIT_DATA_COLLECTION data(&sgroup->inbound_surfel_data[nth_element]);
      if (g_surfel_ids.is_id_in_set(data.id) || (data.source_sp == my_proc_id)) {
        vertex_bookmark += data.n_vertices;
        continue;
      }
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
      ccDOTIMES(vertex_num, data.n_vertices) {
        data.vertices.push_back(sgroup->inbound_surfel_vertex_data[vertex_bookmark++]);
      }
#endif
      if (data.source_sp != sgroup->m_dest_sp.rank()) {
        second_hand_comm_surfel_list.push_back(data); //move the record to a list that will be handled separately
      } else {
        sgroup->culled_surfel_list.push_back(data);
      }
    }

    //Now all redundant surfels have been filtered from the receive buffer, create
    //ghost surfels for the remainder and include them in a film only comm_recv group.
    for(std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = sgroup->culled_surfel_list.begin(); i != sgroup->culled_surfel_list.end(); i++) {
      FO_SURFEL_INIT_DATA_COLLECTION data = &(*i);

      cNEIGHBOR_SP source_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp(data->source_sp);
      SURFEL film_only_ghost_surfel = add_film_only_ghost_surfel(*data);
      SURFEL_FILM_SEND_GROUP ngroup = g_surfel_film_send_fset.find_send_group(data->scale, source_sp);
      if (ngroup == nullptr) {
        SURFEL_FILM_SEND_GROUP vgroup = find_group(source_sp.rank(), data->scale, new_send_groups);
        if (vgroup == nullptr) {
          ngroup = new sSURFEL_FILM_SEND_GROUP(data->scale, source_sp);
          new_send_groups.push_back(ngroup);
        } else {
          ngroup = vgroup;
        }
      }
      ngroup->add_quantum(film_only_ghost_surfel);

#if 0
      //mark any film only ghost surfels that are created...although the simerr may be disabled after too many
      simerr_report_error_code( SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, sgroup->scale, film_only_ghost_surfel->centroid,"forward");
#endif
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
      //Do something with the connectivity info.
      append_connectivity_with_additional_vertices(data, film_only_ghost_surfel);
#endif
    }
    //Now the correct film only comm SEND group needs to be created on the source sp.
    //This is accomplished by reporting back the surfel id's that were included in the film only receive group just created above.

    //Fill the groups outbound_surfel_data vector with the compacted set of surfels then post sends for it and its size.
    //The entire surfel record is sent for simplicity even though only its id will be needed by the receiver.
    sgroup->fill_outbound_surfels();

    //Post send requests for the count of ID's being sent
#if defined(_EXA_IMPI)
    MPI_Issend(&sgroup->outbound_surfel_count, 1, MPI_INT, sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_count_send_request);
#else
    MPI_Isend(&sgroup->outbound_surfel_count, 1, MPI_INT, sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_count_send_request);
#endif
    //Post send request for the array of ID's (actually more than the ID is being sent as the buffer of init elements (and MPI type) is reused.
#if defined(_EXA_IMPI)
    MPI_Issend(sgroup->outbound_surfel_data.data(), sgroup->outbound_surfel_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_data_send_request);
#else
    MPI_Isend(sgroup->outbound_surfel_data.data(), sgroup->outbound_surfel_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_data_send_request);
#endif
  }


  ccDOTIMES(i, new_send_groups.size()) {
    //create a separate surfel comm group for film-only ghost surfels
    g_surfel_film_send_fset.add_group(new_send_groups[i]);
  }
  //----Finish building the film only comm SEND groups----


  //post receive requests for the remote SP's lists of surfels to include in the film only comm send group
  DO_SURFEL_FILM_RECV_GROUPS(sgroup) {
    //post receive of the incoming surfel count
    MPI_Irecv(&sgroup->inbound_surfel_count, 1, MPI_INT, sgroup->m_source_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG,
              eMPI_sp_cp_comm, &sgroup->inbound_surfel_count_recv_request);
  }

  //----complete receives for surfel counts and post receive requests for the surfel info-----
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    complete_mpi_request_while_processing_cp_messages(&rgroup->inbound_surfel_count_recv_request, MPI_SLEEP_LONG);
    //allocate a buffer for receiving the surfel data
    rgroup->inbound_surfel_data.resize(rgroup->inbound_surfel_count);
    //post receive request for surfel data

    MPI_Irecv(rgroup->inbound_surfel_data.data(), rgroup->inbound_surfel_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->inbound_surfel_data_recv_request);
  }

  //--------complete sends of the surfel counts-----
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->outbound_surfel_count_send_request, MPI_SLEEP_LONG);
  }

  //-----complete receives of the surfel data--------
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    complete_mpi_request_while_processing_cp_messages(&rgroup->inbound_surfel_data_recv_request, MPI_SLEEP_LONG);
  }

  //----complete sends of the surfel data-----
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    complete_mpi_request_while_processing_cp_messages(&sgroup->outbound_surfel_data_send_request, MPI_SLEEP_LONG);

  }

  //Now each comm send group has received back a reply from the remote sp listing the
  // surfels it needs to include in a film only comm send group.
  //Create the group here.
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    ccDOTIMES(nth_element,rgroup->inbound_surfel_count) {
      FILM_ONLY_GHOST_SURFEL_INIT_ELEMENT remote_surfel_record = &rgroup->inbound_surfel_data[nth_element];
      SURFEL_FILM_RECV_GROUP film_only_recv_group =
          g_surfel_film_recv_fset.create_group(remote_surfel_record->scale, rgroup->m_source_sp);
      if (remote_surfel_record->source_sp != my_proc_id) {
        msg_print("Surfel %d was received from SP %d during pass %d but is not owned by SP %d Element %d of %d",
                  remote_surfel_record->id, rgroup->m_source_sp.rank(), pass, my_proc_id,
                  nth_element, rgroup->inbound_surfel_count);
        continue;
      }
      SHOB_ID surfel_id = remote_surfel_record->id;
      if (film_only_recv_group->m_set_of_surfel_ids.find(surfel_id) ==
          film_only_recv_group->m_set_of_surfel_ids.end()) {
        //match the local surfel with the id provided in the response from the remote SP
        sSURFEL * dynamics_surfel = regular_surfel_from_id(surfel_id);
        if (dynamics_surfel == NULL) {
          msg_internal_error("Surfel %d could not be found on SP%d for ghost side film solver stencils for pass %d from SP%d.",
                             surfel_id, my_proc_id, pass, rgroup->m_source_sp.rank());
        }
        dynamics_surfel->set_film_only_fringe(TRUE);
        film_only_recv_group->add_quantum(dynamics_surfel);
        film_only_recv_group->m_set_of_surfel_ids.insert(surfel_id);
      }
    }
  }

  //MPI_Barrier(eMPI_sp_cp_comm); //This barrier isn't really needed anymore but it allows the CP to print some relevant status messages.
  process_second_hand_comm_surfels(&second_hand_comm_surfel_list, pass);
}
// The following routine builds the stencils by passing surfel information across the interface in the "reverse direction"
// and creates film-only ghost surfels (and corresponding comm groups) for surfels in a ghost surfel's stencil.

//This routine is almost identical to the above routine so some way to consolidate the two would be preferable.
//The difference is that this informs the compliment SP of surfels in its ghost surfels's film stencils
//whereas the above routine informs the compliment SP of surfels in the surfel stencils of the dynamics surfels.
//Also there is one additional fragment of code that removes redundant surfels received that already have
//corresponding film only surfels created in the above routine which runs first.

VOID retrieve_dynamics_side_interface_stencils(asINT32 pass) {
  g_surfel_ids.construct_set_of_ids();

  std::list<sFO_SURFEL_INIT_DATA_COLLECTION> second_hand_comm_surfel_list;
  asINT32 group_num = 0;
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    //collect all unique surfels in the stencil of surfels in this comm group.
    sFILM_STENCIL_SURFEL_VECTOR nearby_surfel_references = fill_nearby_surfels(sgroup, true);

    //Also count the number of vertices that will need to be sent
    sgroup->fill_outbound_surfels(nearby_surfel_references);

    //post send requests for the send buffer size
#if defined(_EXA_IMPI)
    MPI_Issend(&sgroup->outbound_surfel_count, 1, MPI_INT, sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &sgroup->outbound_surfel_count_send_request);
#else
    MPI_Isend(&sgroup->outbound_surfel_count, 1, MPI_INT, sgroup->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &sgroup->outbound_surfel_count_send_request);
#endif
    //post send of surfel send elements
#if defined(_EXA_IMPI)
    MPI_Issend(sgroup->outbound_surfel_data.data(), sgroup->outbound_surfel_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_data_send_request);
#else
    MPI_Isend(sgroup->outbound_surfel_data.data(), sgroup->outbound_surfel_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_data_send_request);
#endif

#ifdef ENABLE_CONNECTIVITY_INIT_DATA
    sgroup->fill_outbound_vertices(nearby_surfel_references);

    //Send the buffer of vertex data (global index and coordinates).
#if defined(_EXA_IMPI)
    MPI_Issend(sgroup->outbound_surfel_vertex_data.data(), sgroup->outbound_surfel_vertex_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_vertex_data_send_request);
#else
    MPI_Isend(sgroup->outbound_surfel_vertex_data.data(), sgroup->outbound_surfel_vertex_data_size(),
              MPI_BYTE, sgroup->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &sgroup->outbound_surfel_vertex_data_send_request);
#endif
#endif
  }

  DO_SURFEL_FILM_RECV_GROUPS(group) {
    //post receive of the incoming surfel count
    MPI_Irecv(&group->inbound_surfel_count, 1, MPI_INT, group->m_source_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &group->inbound_surfel_count_recv_request);
  }

  //----complete receives for surfel counts and post receive requests for the surfel info-----
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->inbound_surfel_count_recv_request, MPI_SLEEP_LONG);
    //allocate a buffer for receiving the surfel data
    group->inbound_surfel_data.resize(group->inbound_surfel_count);
    //post receive request for surfel data

    MPI_Irecv(group->inbound_surfel_data.data(), group->inbound_surfel_data_size(),
              MPI_BYTE, group->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &group->inbound_surfel_data_recv_request);
  }

  //--------complete sends of the surfel counts-----
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->outbound_surfel_count_send_request, MPI_SLEEP_LONG);
  }

  //-----complete receives of the surfel data--------
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->inbound_surfel_data_recv_request, MPI_SLEEP_LONG);
  }

  //----complete sends of the surfel data-----
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->outbound_surfel_data_send_request, MPI_SLEEP_LONG);
  }



#ifdef ENABLE_CONNECTIVITY_INIT_DATA
  //----receive vertex data-------
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    //count the number of vertices to receive and
    rgroup->allocate_inbound_vertices() ;

    //post receive request for surfel vertex data
    MPI_Irecv(rgroup->inbound_surfel_vertex_data.data(), rgroup->inbound_surfel_vertex_data_size(),
              MPI_BYTE, rgroup->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_VERTEX_COMM_TAG,
              eMPI_sp_cp_comm, &rgroup->inbound_surfel_vertex_data_recv_request);

  }

  //----complete receive of the vertex buffers----
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->inbound_surfel_vertex_data_recv_request, MPI_SLEEP_LONG);
  }
  //complete sends of the vertex buffers
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->outbound_surfel_vertex_data_send_request, MPI_SLEEP_LONG);
  }
#endif




  std::vector<SURFEL_FILM_SEND_GROUP> new_send_groups;
  //Now each surfel SEND group has a buffer with the the surfels from the remote SP's ghost surfel stencils
  //However some of these surfels already have local dynamics surfels which are already commed; detect and
  //remove the redundant elements.
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    //move the received data buffer to a list to facilitate culling the redundant surfels
    //std::list<sFO_SURFEL_INIT_DATA_COLLECTION> culled_surfel_list;
    asINT32 vertex_bookmark = 0;
    ccDOTIMES(nth_element, group->inbound_surfel_count) {
      sFO_SURFEL_INIT_DATA_COLLECTION data(&group->inbound_surfel_data[nth_element]);
      if (g_surfel_ids.is_id_in_set(data.id) || (data.source_sp == my_proc_id)) {
        vertex_bookmark += data.n_vertices;
        continue;
      }
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
      ccDOTIMES(vertex_num, data.n_vertices) {
        data.vertices.push_back(group->inbound_surfel_vertex_data[vertex_bookmark++]);
      }
#endif
      if (data.source_sp != group->m_source_sp.rank()) {
        second_hand_comm_surfel_list.push_back(data); //move the record to a list that will be handled seperatly
      } else {
        group->culled_surfel_list.push_back(data);
      }
    }


    //Now all redundant surfels have been filtered from the receive buffer, create film-only
    //ghost surfels for the remainder and include them in a film only comm_rev group.
    for(std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = group->culled_surfel_list.begin(); i != group->culled_surfel_list.end(); i++) {
      //The following function call should allocate a new film-only surfel object and return its pointer,
      //and add a reference to it within the film only ghost surfel collection,
      //and create a film-only comm recv group for it if it has a unique signature,
      //and lastly, points the comm recv group's surfel vector to the collection's surfel vector.
      FO_SURFEL_INIT_DATA_COLLECTION data = &(*i);

      if (g_surfel_ids.is_id_in_set(data->id))
        msg_internal_error("Id %d is already present in the set", data->id);
      cNEIGHBOR_SP source_sp = g_strand_mgr.m_neighbor_sp_map.get_nsp(data->source_sp);
      SURFEL film_only_ghost_surfel = add_film_only_ghost_surfel(*data);
      SURFEL_FILM_SEND_GROUP sgroup = g_surfel_film_send_fset.find_send_group(data->scale, source_sp);
      if (sgroup == nullptr) {
        SURFEL_FILM_SEND_GROUP vgroup = find_group(source_sp.rank(), data->scale, new_send_groups);
        if (vgroup == nullptr) {
          sgroup = new sSURFEL_FILM_SEND_GROUP(data->scale, source_sp);
          new_send_groups.push_back(sgroup);
        } else {
          sgroup = vgroup;
        }
      }
      sgroup->add_quantum(film_only_ghost_surfel);

#if 0
      //mark any film only ghost surfels that are created...although the simerr may be disabled after too many
      simerr_report_error_code( SP_EER_FILM_ONLY_GHOST_SURFEL_CREATED, group->scale, film_only_ghost_surfel->centroid,"reverse");
#endif
#ifdef ENABLE_CONNECTIVITY_INIT_DATA
      //Do something with the connectivity info if it's enabled.
      append_connectivity_with_additional_vertices(data, film_only_ghost_surfel);
#endif
    }

    //Now the correct film only comm RECV group needs to be created on the source sp.
    //This is accomplished by reporting back the surfel records that were included in the film only receive group just created above.

    //Fill the groups outbound_surfel_data vector with the compacted set of surfels then post sends for it and its size.
    //The entire surfel record is sent for simplicity even though only its id will be needed by the receiver.
    group->fill_outbound_surfels();

    //Post send requests for the count of IDs being sent
#if defined(_EXA_IMPI)
    MPI_Issend(&group->outbound_surfel_count, 1, MPI_INT,
              group->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &group->outbound_surfel_count_send_request);
#else
    MPI_Isend(&group->outbound_surfel_count, 1, MPI_INT,
              group->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &group->outbound_surfel_count_send_request);
#endif
    //Post send request for the array of ID's (actually more than the ID is being sent as the buffer of init elements (and MPI type) is reused.
#if defined(_EXA_IMPI)
    MPI_Issend(group->outbound_surfel_data.data(), group->outbound_surfel_data_size(),
              MPI_BYTE, group->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &group->outbound_surfel_data_send_request);
#else
    MPI_Isend(group->outbound_surfel_data.data(), group->outbound_surfel_data_size(),
              MPI_BYTE, group->m_source_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &group->outbound_surfel_data_send_request);
#endif
    group_num++;
  }

  //----Finish building the film only comm RECV groups----


  //post receive requests for the remote SP's lists of surfels to include in the film only comm send group
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    //post receive of the incoming surfel count
    MPI_Irecv(&group->inbound_surfel_count, 1, MPI_INT, group->m_dest_sp.rank(),
              eMPI_FILM_ONLY_GHOST_SURFEL_COUNT_TAG, eMPI_sp_cp_comm,
              &group->inbound_surfel_count_recv_request);
  }

  //----complete receives for surfel counts and post receive requests for the surfel info-----
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->inbound_surfel_count_recv_request, MPI_SLEEP_LONG);
    //allocate a buffer for receiving the surfel data
    group->inbound_surfel_data.resize(group->inbound_surfel_count);

    //post receive request for surfel data
    MPI_Irecv(group->inbound_surfel_data.data(), group->inbound_surfel_data_size(),
              MPI_BYTE, group->m_dest_sp.rank(), eMPI_FILM_ONLY_GHOST_SURFEL_COMM_TAG,
              eMPI_sp_cp_comm, &group->inbound_surfel_data_recv_request);
  }

  //--------complete sends of the surfel counts-----
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->outbound_surfel_count_send_request, MPI_SLEEP_LONG);
  }

  //-----complete receives of the surfel data--------
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->inbound_surfel_data_recv_request, MPI_SLEEP_LONG);
  }

  //----complete sends of the surfel data-----
  DO_SURFEL_FILM_RECV_GROUPS(group) {
    complete_mpi_request_while_processing_cp_messages(&group->outbound_surfel_data_send_request, MPI_SLEEP_LONG);

  }

  ccDOTIMES(i, new_send_groups.size()) {
    //create a separate surfel comm group for film-only ghost surfels
    g_surfel_film_send_fset.add_group(new_send_groups[i]);
  }
  //Now each comm send group has received back a reply from the remote sp listing the surfels it needs to include in a film only comm send group.
  //Create the group here.
  DO_SURFEL_FILM_SEND_GROUPS(group) {
    ccDOTIMES(nth_element,group->inbound_surfel_count) {
      FILM_ONLY_GHOST_SURFEL_INIT_ELEMENT remote_surfel_record = &group->inbound_surfel_data[nth_element];
      if (remote_surfel_record->source_sp != my_proc_id) {
        msg_print("Surfel %d was received from SP %d during pass %d but is not owned by SP %d Element %d of %d",
                  remote_surfel_record->id, group->m_dest_sp.rank(), pass, my_proc_id,
                  nth_element, group->inbound_surfel_count);
        continue;
      }
      SURFEL_FILM_RECV_GROUP film_only_recv_group =
        g_surfel_film_recv_fset.create_group(remote_surfel_record->scale, group->m_dest_sp);
      SHOB_ID surfel_id = remote_surfel_record->id;
      if (film_only_recv_group->m_set_of_surfel_ids.find(surfel_id) ==
          film_only_recv_group->m_set_of_surfel_ids.end()) {

        //match the local surfel with the id provided in the response from the remote SP
        sSURFEL *dynamics_surfel = regular_surfel_from_id(surfel_id);
        if (dynamics_surfel == NULL) {
          msg_internal_error("Surfel %d could not be found on SP%d for dynamics side film solver stencils for pass %d from SP%d.",
                             surfel_id, my_proc_id, pass, group->m_dest_sp.rank());
        }
        dynamics_surfel->set_film_only_fringe(TRUE);
        film_only_recv_group->add_quantum(dynamics_surfel);
        film_only_recv_group->m_set_of_surfel_ids.insert(surfel_id);
      }
    }
  }

  //MPI_Barrier(eMPI_sp_cp_comm); //This barrier isn't really needed anymore but it allows the CP to print some relevant status messages.
  process_second_hand_comm_surfels(&second_hand_comm_surfel_list, pass);
}

VOID create_surfel_film_send_and_receive_groups() {
  DO_SURFEL_SEND_GROUPS(group) {
    SURFEL_FILM_RECV_GROUP rgroup = g_surfel_film_recv_fset.create_group(group->scale(), group->m_dest_sp);
    rgroup->copy_quantums(group);
  }
  DO_SURFEL_RECV_GROUPS(group) {
    SURFEL_FILM_SEND_GROUP sgroup = g_surfel_film_send_fset.create_group(group->scale(), group->m_source_sp);
    sgroup->copy_quantums(group);
  }
}

VOID sSURFEL_FILM_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  sdFLOAT *send_buffer = m_send_msg.buffer();
  // complete the previous send if any
  complete_mpi_request_while_processing_cp_messages(&m_send_msg.m_request, MPI_SLEEP_LONG);

  for (auto& quantum: m_quantums) {
    SURFEL fo_surfel = quantum.m_surfel;

    cassert(fo_surfel->is_film_only_ghost() || fo_surfel->is_ghost());
    if(sim.is_film_solver && !fo_surfel->is_inlet_or_outlet()){
      fo_surfel->fill_film_data(send_buffer);
    }else{
      fo_surfel->fill_solid_parcel_data(send_buffer);
    }
    fo_surfel->p_data()->s.accretion_volume = 0.0;
    fo_surfel->p_data()->s.clear_film_accumulators();
    fo_surfel->p_data()->s.clear_meas_accumulators();
    fo_surfel->p_data()->s.clear_reentrainment_meas_accumulators();
  }

  asINT32 true_sendsize = send_buffer - m_send_msg.buffer();
  LOG_MSG("STRAND_SWITCHING").printf( "sending surfels scale %d size %d tag %x to SP %d", m_scale, true_sendsize, m_send_msg.tag(), dest_rank());
  m_send_msg.set_nelems(true_sendsize);
  g_exa_sp_cp_comm.isend(m_send_msg);

  m_solver_timestep_index_mask ^= active_solver_mask;
}

VOID init_surfel_film_send_and_recv_groups() {
  DO_SURFEL_FILM_SEND_GROUPS(sgroup) {
    sgroup->allocate_film_send_buffer();
    g_strand_mgr.m_n_send_groups++;
  }
  g_strand_mgr.m_recv_channel[FILM_RECV_TYPE].update_allocations(g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps());
  DO_SURFEL_FILM_RECV_GROUPS(rgroup) {
    rgroup->allocate_film_recv_buffer();
    g_strand_mgr.m_recv_channel[FILM_RECV_TYPE].add_recv_group(rgroup);
  }
  g_strand_mgr.m_recv_channel[FILM_RECV_TYPE].update_source_sps(g_strand_mgr.m_neighbor_sp_map.get_n_neighbor_sps());
  // dependency counters should be updated for strands that depend on film receive
  g_strand_mgr.initialize_dependency_counters();
}
