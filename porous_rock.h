#ifndef SIMENG_POROUS_ROCK_H
#define SIMENG_POROUS_ROCK_H

#include "common_sp.h"

using POROUS_ROCK_INDEX = int32_t;
using POROUS_ROCK_VAR = sFLOAT;

void read_porous_rock_tables(LGI_STREAM stream);

class cPOROUS_ROCK
{
  static const size_t NUM_K0_COLS = 3;
  static const size_t NUM_Pc_Kr_COLS = 19; //18(Pc, Kr) + 1(Sw)
  static const size_t NUM_INFO_COLS = 6; //porosity, contact_angle, surface_tension, principal_dir[3]
  static const size_t NUM_INFO_ROWS = 1;
  POROUS_ROCK_INDEX m_index;
  //dFLOAT m_principal_dir[3];

  sINT32 m_num_K0_rows;
  sINT32 m_num_K0_cols;
  POROUS_ROCK_VAR * m_K0_porosity;
  POROUS_ROCK_VAR * m_K0_values;

  sINT32 m_num_Pc_Kr_rows;
  sINT32 m_num_Pc_Kr_cols;
  POROUS_ROCK_VAR * m_Pc_Kr_saturation;
  POROUS_ROCK_VAR * m_Pc_Kr_pressure; //the name is ambiguous, because
                                      //it can not be decided in simeng which pressure value
                                      //i.e. Pc_imb[0] or Pc_drn[1] will be used
                                      //as a key to inversely look up Sw
  POROUS_ROCK_VAR * m_Pc_Kr_values;

  struct {  //floating point precision must be consistent with that used in CP (m_info_values)
    POROUS_ROCK_VAR m_porosity_Pc;
    POROUS_ROCK_VAR m_contact_angle_Pc;
    POROUS_ROCK_VAR m_surface_tension_Pc;
    POROUS_ROCK_VAR m_principal_dir[3];
  } sInfo;

  sdFLOAT m_pm_kr_resw;
  sdFLOAT m_pm_kr_resa;

  cPOROUS_ROCK(POROUS_ROCK_INDEX index);

public:

  cPOROUS_ROCK() {}
  static cPOROUS_ROCK read_from_cp(LGI_STREAM stream);

  sINT32 index() {return m_index; }
  VOID set_pm_kr_resw(sdFLOAT w) {m_pm_kr_resw = w; }
  VOID set_pm_kr_resa(sdFLOAT a) {m_pm_kr_resa = a; }
  sdFLOAT get_pm_kr_resw() {return m_pm_kr_resw; }
  sdFLOAT get_pm_kr_resa() {return m_pm_kr_resa; }

  sdFLOAT get_porosity_Pc() {return sInfo.m_porosity_Pc; }
  sdFLOAT get_surface_tension_Pc() {return sInfo.m_surface_tension_Pc; }
  sdFLOAT get_contact_angle_Pc() {return sInfo.m_contact_angle_Pc; }
  sdFLOAT get_principal_dir(asINT32 axis) {return sInfo.m_principal_dir[axis]; }

  struct sK0;
  struct sPc_Kr;

  sK0 get_K0(POROUS_ROCK_VAR porosity) const;
  sPc_Kr get_Pc_Kr(POROUS_ROCK_VAR saturation) const;
  sPc_Kr get_Sw(POROUS_ROCK_VAR pressure) const;
};

struct cPOROUS_ROCK::sK0
{
  POROUS_ROCK_VAR estimated_K0[3];
};

struct cPOROUS_ROCK::sPc_Kr
{
  POROUS_ROCK_VAR saturation;
  POROUS_ROCK_VAR Pc_imb[3];
  POROUS_ROCK_VAR Pc_drn[3];
  POROUS_ROCK_VAR Krw_imb[3];
  POROUS_ROCK_VAR Kro_imb[3];
  POROUS_ROCK_VAR Krw_drn[3];
  POROUS_ROCK_VAR Kro_drn[3];
};

#endif
