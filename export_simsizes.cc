#include <iostream>
#include "export_simsizes.h"
#include "simulator_namespace.h"
#include SIMSIZES_SHARED_H

/*
 * The primary purpose of this test file is to ensure that any
 * modifications to parse_shob_descs.cc or its auxillary code
 * does not cause unresolved symbols to be shipped with the simsizes
 * export libraries.
 *
 * By building this executable, we ensure that SIMENG_SIZES_LIB
 * is self-sufficient and all global symbols are resolved within
 * this archive.This ensures that the build of dependent clients
 * such as the decomposer and discretizer do not fail during the
 * linking phase.
 */
int main(void){

  SIM_SIZES::SIM_OPTIONS opts;
  
  SIMULATOR_NAMESPACE::SIMENG_SIZES_INTERFACE_IMPL export_obj(opts);

  std::cout << "Size of sSurfel : " << export_obj.size_of_sSURFEL()
            << std::endl
            << "Size of sUBLK : " << export_obj.size_of_sUBLK()
            << std::endl
            << "Size of NBLK_SEND_FIELD : " << export_obj.size_of_nearblk_send_field()
            << std::endl
            << "Size of FBLK_SEND_FIELD : " << export_obj.size_of_farblk_send_field()
	    <<std::endl;

  return 0;
}
