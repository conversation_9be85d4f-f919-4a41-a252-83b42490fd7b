/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Hall, Exa Corporation 
 * Created May 22, 2008
 *--------------------------------------------------------------------------*/
#include "ublk_neighbors.h"
#include "ublk.h"


// flattens out the state direction in Z direction
asINT32 neighbor_voxel_along_state_direction_2D(asINT32 voxel, asINT32 latvec) {

  sINT16 v_offsets[3], c_offsets[3];

#if BUILD_D39_LATTICE
  c_offsets[0] = state_vx(latvec) > 1 ? 0 : state_vx(latvec);
  c_offsets[1] = state_vy(latvec) > 1 ? 0 : state_vy(latvec);
  if (state_vx(latvec) && state_vy(latvec)) {
    c_offsets[2] = 0;
  } else {
    c_offsets[2] = state_vz(latvec) > 1 ? 0 : state_vz(latvec);
  }
#else
  c_offsets[0] = state_vx(latvec);
  c_offsets[1] = state_vy(latvec);
  c_offsets[2] = state_vz(latvec);
#endif

  v_offsets[0] = ((voxel >> 2) & 1) + c_offsets[0] + 1;
  v_offsets[1] = ((voxel >> 1) & 1) + c_offsets[1] + 1;
  v_offsets[2] = ((voxel     ) & 1) + c_offsets[2] + 1;
  asINT32 neighbor_voxel = (((v_offsets[0] + 1) & 1) << 2) +
                           (((v_offsets[1] + 1) & 1) << 1) +
                           ((v_offsets[2] + 1) & 1);
  return neighbor_voxel;
}


// neighbors of split ublks, fluid_like_voxel_mask is not verified. This is
// done in the calling location. During advection, only those connected are
// stored in split_advect_factors.
VOID voxel_neighbors_along_states_2(UBLK dest_ublk, asINT32 voxel,
                                    TAGGED_UBLK neighbor_ublks[N_LATTICE_VECTORS_D25])
{
  const sBOX_ACCESS& box_access = dest_ublk->box_access();

  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();
  CONNECT_MASK fluid_connect_mask = dest_ublk->voxel_fluid_connect_mask(voxel);
  TAGGED_UBLK tagged_neighbor = NULL;
  asINT32 latvec;
  for (latvec = N_MOVING_STATES; latvec < N_LATTICE_VECTORS_D25; latvec++) {
    if (sim.is_2d() && (state_vz(SPEED_2_TO_SPEED_1(latvec)) != 0)) {
      continue;
    }
    asINT32 diag_voxel = voxel;
    if (fluid_connect_mask.test(latvec)) {

      sINT16 u_offsets[3] = {0};
      u_offsets[0] = state_vx(SPEED_2_TO_SPEED_1(latvec));
      u_offsets[1] = state_vy(SPEED_2_TO_SPEED_1(latvec));
      u_offsets[2] = state_vz(SPEED_2_TO_SPEED_1(latvec));
      tagged_neighbor = box_access.neighbor_ublk(u_offsets);
      if (tagged_neighbor.is_ublk_split()) {
        neighbor_ublks[latvec]  = tagged_neighbor;
      } else if (tagged_neighbor.is_ublk() &&
          !tagged_neighbor.is_ublk_scale_interface()) {
        UBLK neighbor_ublk = tagged_neighbor.ublk();
        // Since the connect_mask is valid, there should be no need to
        // verify the fluid_like_voxel_mask and vr_fine ublks have a full
        // fluid_like_voxel_mask
        if (!neighbor_ublk->fluid_like_voxel_mask.test(diag_voxel)) {
          msg_internal_error("Voxel %d of Ublk %d at [%d %d %d] is connected along latvec %d \
                              but neighbor Ublk %d with fluidmask %#02x does not any fluid in voxel %d",
                             voxel, dest_ublk->id(), dest_ublk->location(0), dest_ublk->location(1),
                             dest_ublk->location(2), latvec,
                             neighbor_ublk->id(), neighbor_ublk->fluid_like_voxel_mask.get(), diag_voxel);
        }
        neighbor_ublks[latvec]  = tagged_neighbor;
      } else {
        neighbor_ublks[latvec]  = tagged_neighbor;
      }
    }
  }
}

// This is called during post_seed_init to verify the consistency of neighbors
// and whether a ublk has simple neighbors.
// For split backward or forward ublk, a NULL pointer is returned which forces
// the ublk to be labeled an not simple.
// If a neighbor is scale interface and overlapping fine ublk is split, the
// first split instance is returned.
VOID ublk_neighbors_on_axis(asINT32 axis, UBLK ublk,
                            asINT32 voxel, asINT32 &neighbor_voxel,
                            asINT32 &forward_face, asINT32 &backward_face,
                            UBLK &forward_ublk,
                            UBLK &backward_ublk,
                            BOOLEAN &is_forward_neighbor_different_scale,
                            BOOLEAN &is_backward_neighbor_different_scale,
                            BOOLEAN &is_forward_neighbor_finer_scale,
                            BOOLEAN &is_backward_neighbor_finer_scale,
                            BOOLEAN &is_forward_neighbor_2_different_scale,
                            BOOLEAN &is_backward_neighbor_2_different_scale)
{
  const auto& box_access = ublk->box_access();
  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();
  neighbor_voxel = neighbor_voxel_along_axis(voxel, axis);
  forward_face  = stp_axis_to_pos_face(axis);
  backward_face = stp_axis_to_neg_face(axis);
  //uINT8 *fluid_connect_masks = ublk->fluid_connect_masks;
  CONNECT_MASK voxel_fluid_connect_mask    = ublk->voxel_fluid_connect_mask(voxel);
  CONNECT_MASK neighbor_fluid_connect_mask = ublk->voxel_fluid_connect_mask(neighbor_voxel);

  sINT16 offsets_f[3] = {0}; offsets_f[axis] =  1;
  sINT16 offsets_b[3] = {0}; offsets_b[axis] = -1;
  forward_ublk = NULL;
  is_forward_neighbor_different_scale = FALSE;
  is_forward_neighbor_2_different_scale = FALSE;
  is_forward_neighbor_finer_scale = FALSE;

  backward_ublk = NULL;
  is_backward_neighbor_different_scale = FALSE;
  is_backward_neighbor_finer_scale = FALSE;
  is_backward_neighbor_2_different_scale = FALSE;

  if (neighbor_voxel < voxel) {
    /* crossed into a new ublk on forward face, but not backward face */
    if (voxel_fluid_connect_mask.test(forward_face)) {
      TAGGED_UBLK forward_neighbor       = box_access.forward_neighbor(axis);
      if (!forward_neighbor.is_ublk_split()) {
        is_forward_neighbor_finer_scale     = forward_neighbor.is_ublk_scale_interface();
        is_forward_neighbor_different_scale = is_forward_neighbor_finer_scale;
        if (is_forward_neighbor_finer_scale) {
          TAGGED_UBLK fine_forward_neighbor = forward_neighbor.interface()->m_fine_ublks[neighbor_voxel];
          if (fine_forward_neighbor.is_ublk_split()) {
            // return the first split neighbor
            sUBLK_VECTOR *fine_split_ublks = fine_forward_neighbor.split_ublks();
            fine_forward_neighbor = fine_split_ublks->m_tagged_ublks[0];
            forward_ublk = fine_forward_neighbor.ublk();
          } else if (fine_forward_neighbor.is_ublk()) {
            forward_ublk = fine_forward_neighbor.ublk();
          }
        } else if (forward_neighbor.is_ublk()) {
          forward_ublk = forward_neighbor.ublk();
          is_forward_neighbor_different_scale = (forward_ublk->is_vr_fine() == 1);
        }
        is_forward_neighbor_2_different_scale = is_forward_neighbor_different_scale;
      }
    } 

    is_backward_neighbor_different_scale = FALSE;
    is_backward_neighbor_finer_scale = FALSE;
    is_backward_neighbor_2_different_scale = FALSE;
    if (voxel_fluid_connect_mask.test(backward_face)) {
      if (neighbor_fluid_connect_mask.test(forward_face)) {
        backward_ublk = ublk;
        TAGGED_UBLK backward_neighbor_2 = box_access.backward_neighbor(axis);
        if (!backward_neighbor_2.is_ublk_split()) {
          is_backward_neighbor_2_different_scale = 
            backward_neighbor_2.is_ublk_scale_interface() ||
            (backward_neighbor_2.is_ublk() && (backward_neighbor_2.ublk()->is_vr_fine() == 1));
        }
      }
    }

  } else {
    // crossed into a new ublk on backward face, but not forward face
    is_forward_neighbor_different_scale = FALSE;
    is_forward_neighbor_finer_scale = FALSE;
    is_forward_neighbor_2_different_scale = FALSE;
    if (voxel_fluid_connect_mask.test(forward_face)) {
      if (neighbor_fluid_connect_mask.test(backward_face)) {
        forward_ublk = ublk;
        TAGGED_UBLK forward_neighbor_2 = box_access.forward_neighbor(axis);
        if (!forward_neighbor_2.is_ublk_split()) {
          is_forward_neighbor_2_different_scale = 
            forward_neighbor_2.is_ublk_scale_interface() ||
            (forward_neighbor_2.is_ublk() && (forward_neighbor_2.ublk()->is_vr_fine() == 1));
        }
      }
    }

    if (voxel_fluid_connect_mask.test(backward_face)) {
      TAGGED_UBLK backward_neighbor = box_access.backward_neighbor(axis);
      if (!backward_neighbor.is_ublk_split()) {
        is_backward_neighbor_finer_scale     = backward_neighbor.is_ublk_scale_interface();
        is_backward_neighbor_different_scale = is_backward_neighbor_finer_scale;
      
        if (is_backward_neighbor_finer_scale) {
          TAGGED_UBLK fine_backward_neighbor = backward_neighbor.interface()->m_fine_ublks[neighbor_voxel];
          if (fine_backward_neighbor.is_ublk_split()) {
            // return the first split neighbor
            sUBLK_VECTOR *fine_split_ublks = fine_backward_neighbor.split_ublks();
            fine_backward_neighbor = fine_split_ublks->m_tagged_ublks[0];
            backward_ublk = fine_backward_neighbor.ublk();
          } else if (fine_backward_neighbor.is_ublk()) {
            backward_ublk = fine_backward_neighbor.ublk();
          }
        } else if (backward_neighbor.is_ublk()) {
          backward_ublk = backward_neighbor.ublk();
          is_backward_neighbor_different_scale = (backward_ublk->is_vr_fine() == 1);
        }
        is_backward_neighbor_2_different_scale = is_backward_neighbor_different_scale;
      }
    }
  }
}

// This is called from compute_div_u ( divergence of velocity)
// and collect_neighbor_values for ublk with simple neighbors (all neighbors
// present and are same scale and not split). All ublks with
// split neighbors do not fall in this category, since we returned NULL for
// neighbors during a call to ublk_neigbors_on_axis by post_seed_init.
VOID same_scale_voxel_neighbors_on_axis(asINT32 axis, UBLK ublk,
                                        asINT32 voxel, asINT32 &neighbor_voxel,
                                        TAGGED_UBLK &forward_neighbor,
                                        TAGGED_UBLK &backward_neighbor)
{
  const auto& box_access = ublk->box_access();

  neighbor_voxel = neighbor_voxel_along_axis(voxel, axis);

  if (neighbor_voxel < voxel) {
    /* crossed into new ublk on forward face, but not backward face */
    forward_neighbor = box_access.forward_neighbor(axis);
    backward_neighbor = box_access.stagged_ublk();
  } else {
    /* Crossed into new ublk on backward face, but not forward face */
    forward_neighbor = box_access.stagged_ublk();
    backward_neighbor = box_access.backward_neighbor(axis);
  }
}

/* Called from following locations
 *  surfel_advect_phy.cc
             <<does_voxel_lack_neighbor_on_any_face>>
   vvfluid_div_u_d19.cc <<compute_div_u>>
   vvfluid_turb_omega_c_d19.cc <<compute_pavg_and_sij>>
   vvfluid_turb_omega_c_d19.cc <<compute_turb_omega_c>>
   gradient.h <<collect_neighbor_values>>
   seed.cc <<extrapolate_ublk_data>>
   ublk_neighbors.cc <<voxel_neighbors_on_axis>>
   vr.cc <<execute_explode_rule>>
*/
