/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * SP Timing definitions
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Mar  4 1994
 *--------------------------------------------------------------------------*/

#ifndef __SP_TIMERS_H
#define __SP_TIMERS_H

#include "common_sp.h"
#include "sim.h"
#include "strand_global.h"

#define NUM_SURFEL_LOAD_TYPES 13    // One to one correspondence to surfel dyn timers
#define NUM_ISURFEL_LOAD_TYPES 4   // One to one correspondence to isurfel dyn timers

/*--------------------------------------------------------------------------*
 * SP Timers
 *--------------------------------------------------------------------------*/

/* The order of the timers defined here is the order they are reported.
 * It is intended that this order reflect the order of events in the 
 * main loop, where possible. It begins at the beginning of the "skewed
 * timestep", that is, the point at which we begin processing the resolution
 * scales appropriate for the current timestep, rather than those appropriate
 * for the previous timestep.
 */

/* The timers must be zero based so that they can be used as simple indices into
 * an array.
 */
/* Note that the surfel dyn timers must be contiguous in order for timer counters
 * to work properly. The same requirement is needed for isurfel dyn timers. The 
 * number of surfel dyn timers is limited to 12 and the number of isurfel dyn timers 
 * is limited to 4. If more timers are to be added, the constants NUM_SURFEL_LOAD_TYPES
 * and NUM_ISURFEL_LOAD_TYPES above should be modified accordingly. 
*/
enum SP_TIMER_TYPE {
  // Always keep SP_STANDARD_WALL_SURFEL_DYN_TIMER to be the first surfel dyn timer.
  SP_STANDARD_WALL_SURFEL_DYN_TIMER = 0,
  SP_FRINGE_SURFEL_DYN_TIMER,
  SP_FRINGE2_SURFEL_DYN_TIMER,
  SP_LINEAR_WALL_SURFEL_DYN_TIMER,
  SP_VEL_WALL_SURFEL_DYN_TIMER,
  SP_ANGULAR_WALL_SURFEL_DYN_TIMER,      
  SP_FIXED_VEL_SURFEL_DYN_TIMER,
  SP_PRESSURE_SURFEL_DYN_TIMER,
  SP_MASS_FLUX_SURFEL_DYN_TIMER,
  SP_SOURCE_SURFEL_DYN_TIMER,
  SP_SAMPLING_SURFEL_DYN_TIMER,
  SP_NOSLIP_SURFEL_DYN_TIMER,
  SP_CONDUCTION_SURFEL_DYN_TIMER,
   
  SP_MLRF_SURFEL_DYN_TIMER,
  SP_MLRF_SURFEL_DMASS_TIMER,
  SP_MLRF_S2S_V2S_TIMER,
  SP_MLRF_S2V_TIMER,
  SP_SLRF_SURFEL_DYN_TIMER,

  // Always keep SP_APM_NOSLIP_ISURFEL_DYN_TIMER to be the first isurfel dyn timer.
  SP_APM_NOSLIP_ISURFEL_DYN_TIMER,
  SP_APM_STANDARD_ISURFEL_DYN_TIMER,
  SP_APM_ANGULAR_ISURFEL_DYN_TIMER,
  SP_APM_LINEAR_ISURFEL_DYN_TIMER,
  
  SP_S2S_TIMER,
  SP_V2S_TIMER,
  SP_S2V_TIMER,
  SP_FRINGE_S2S_TIMER,
  SP_FRINGE_V2S_TIMER,
  SP_FRINGE_S2V_TIMER,
  SP_FRINGE2_S2S_TIMER,
  SP_FRINGE2_V2S_TIMER,
  SP_FRINGE2_S2V_TIMER,

  SP_S2S_SAMPLING_TIMER,
  SP_V2S_SAMPLING_TIMER,

  SP_BSURFEL_DYN_TIMER,
  SP_BSURFEL_MOVE_TIMER,

  // Here the timers are actually for advect + dynamics.
  SP_FARBLK1_DYN_TIMER,
  SP_FARBLK2_DYN_TIMER,
  SP_FRINGE_FARBLK_DYN_TIMER,

  SP_NEARBLK_DYN_TIMER,
  SP_FRINGE_NEARBLK_DYN_TIMER,
  SP_FRINGE2_NEARBLK_DYN_TIMER,

  SP_PARTICLE_VOXEL_DYN_TIMER, // Time how long it takes to update the position and internal states of particles in the fluid.

  SP_VRFINE_FAR_UBLKS_ADVECT_TIMER,  // advect + VR coarse explode
  SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER, // advect + VR coarse explode
  SP_FRINGE_VRFINE_FAR_UBLKS_ADVECT_TIMER,  // advect + VR coarse explode
  SP_FRINGE_VRFINE_NEAR_UBLKS_ADVECT_TIMER, // advect + VR coarse explode
  SP_FRINGE2_VRFINE_FAR_UBLKS_ADVECT_TIMER,  // advect + VR coarse explode
  SP_FRINGE2_VRFINE_NEAR_UBLKS_ADVECT_TIMER, // advect + VR coarse explode

  SP_PARTICLE_MLRF_EMISSION_TIMER, // Process particles received across a moving LRF.
  SP_PARTICLE_EMISSION_TIMER, // Generate new particles when they are emitted from the various types of emitters.
  SP_FILM_ACCUMULATION_TIMER, // Compute each surfel's film properties (e.g. thickness, velocity, ...) from the particles in the vicinity.
  SP_FILM_REENTRAINMENT_TIMER, // Apply the reentrainment model that moves surface particles back to the fluid when the film is in an unstable state.
  SP_FILM_DYNAMICS_TIMER, // Update the film velocity on each surfel.
  SP_FILM_MEASUREMENTS_TIMER, // Measure film quantites.
  SP_SURFACE_PARTICLE_KINEMATICS_TIMER, // Advect surface particles according to the film velocity field. 
  //From time update strand:
  SP_PARTICLE_COLLISION_DETECTION_TIMER, // Time how long it takes to detect collisions between particles in the fluid and various types of surfels.
  SP_PARTICLE_CONTAINER_UPDATE_TIMER, // Time how long it takes to shift parcel object refrences from one voxel's parcel lists to the next once the particle crosses a voxel face.
  SP_PARTICLE_SLRF_EMISSION_TIMER, // Process particles received across a static LRF.
  SP_IMPLICIT_SOLVER_TIMER, // Time how long it takes to solve the linear system.
  SP_IMPLICIT_MATRIX_ASSEMBLY_TIMER, // Time how long it takes to assemble the implicit system matrix.
  /* Accumulated timer using the timer for different operations */
  SP_ACCUM_TIMER,

  /* Always the last timer */
  SP_TOTAL_RUN_TIMER,



  NUM_TIMERS,
  /* invalid timer used as function return value */
  SP_INVALID_TIMER
};

// Use FLUID_LOAD_TYPES to distinguish it from FLUID_TYPES which are defined in "voxel_dyn_sp.h"
// VVFLUID, POROUS MEDIA, FAN
//#define NUM_FLUID_LOAD_TYPES 3

enum FLUID_LOAD_TYPE {
  BASIC_FLUID_LOAD_TYPE = 0,
  FAN_FLUID_LOAD_TYPE,
  POROUS_FLUID_LOAD_TYPE,
  CONDUCTION_SOLID_LOAD_TYPE,
  NUM_FLUID_LOAD_TYPES,
  INVALID_LOAD_TYPE
};

// Sometimes useful to treat mirror load as a fluid load type
#define MIRROR_LOAD_TYPE NUM_FLUID_LOAD_TYPES
  
extern sTIMER sp_timers[NUM_TIMERS];

VOID sim_initialize_timers(VOID);

#define sp_timer_clear_all()	timer_clear_multiple(sp_timers, NUM_TIMERS)
#define sp_timer_start_all()	timer_start_multiple(sp_timers, NUM_TIMERS)
#define sp_timer_stop_all()	timer_stop_multiple(sp_timers, NUM_TIMERS)

#define sp_timer_report_all_verbose(units) \
  timer_report_multiple(sp_timers, NUM_TIMERS, units)

#define sp_timer_clear(ID)	timer_clear(sp_timers + (ID))
#define sp_timer_start(ID)	timer_start(sp_timers + (ID))
#define sp_timer_stop(ID)	timer_stop(sp_timers + (ID))
#define sp_timer_report(ID, units)	timer_report(sp_timers + (ID), units)


/* Helper macros */

inline VOID accum_timer()
{
  ccDOTIMES(i, SP_ACCUM_TIMER) {
    sp_timers[SP_ACCUM_TIMER].total_cpu_time  += sp_timers[i].total_cpu_time;
    sp_timers[SP_ACCUM_TIMER].total_wallclock += sp_timers[i].total_wallclock;
  }
}

inline VOID maybe_start_timer(asINT32 timer_id)
{ if (sim.async_event_flags.timers_on_p) timer_start(sp_timers + timer_id); }

inline VOID maybe_stop_timer(asINT32 timer_id)
{ if (sim.async_event_flags.timers_on_p) timer_stop(sp_timers + timer_id); }

inline VOID maybe_start_item_timer(asINT32 timer_id, asINT32 count)
{ 
  if (sim.async_event_flags.timers_on_p) {
    timer_start(sp_timers + timer_id);
    (sp_timers + timer_id)->item_count[0] += count;
  }
}

inline VOID increment_item_timer_count(asINT32 timer_id)
{
    (sp_timers + timer_id)->item_count[0] += 1;
}

inline VOID maybe_start_2_item_timer(asINT32 timer_id, asINT32 count1, asINT32 count2)
{ 
  if (sim.async_event_flags.timers_on_p) {
    timer_start(sp_timers + timer_id);
    (sp_timers + timer_id)->item_count[0] += count1;
    (sp_timers + timer_id)->item_count[1] += count2;
  }
}

inline VOID maybe_start_3_item_timer(asINT32 timer_id, asINT32 count1, asINT32 count2, asINT32 count3)
{ 
  if (sim.async_event_flags.timers_on_p) {
    timer_start(sp_timers + timer_id);
    (sp_timers + timer_id)->item_count[0] += count1;
    (sp_timers + timer_id)->item_count[1] += count2;
    (sp_timers + timer_id)->item_count[2] += count3;
  }
}

inline VOID maybe_start_4_item_timer(asINT32 timer_id, asINT32 count1, asINT32 count2, 
				     asINT32 count3, asINT32 count4)
{ 
  if (sim.async_event_flags.timers_on_p) {
    timer_start(sp_timers + timer_id);
    (sp_timers + timer_id)->item_count[0] += count1;
    (sp_timers + timer_id)->item_count[1] += count2;
    (sp_timers + timer_id)->item_count[2] += count3;
    (sp_timers + timer_id)->item_count[3] += count4;
  }
}

#define WITH_TIMER(timer_id)                                           \
  BOOLEAN ___(b);										\
  for(___(b)=1, maybe_start_timer(timer_id); ___(b); maybe_stop_timer(timer_id), ___(b)=0)

#define MAYBE_WITH_TIMER(is_active, timer_id)                                           \
  BOOLEAN ___(b);                                                                       \
  for(___(b) = 1, maybe_start_timer(is_active? timer_id : SP_INVALID_TIMER); ___(b); maybe_stop_timer(is_active? timer_id : SP_INVALID_TIMER), ___(b)=0)

#define WITH_ITEM_TIMER(timer_id, count)									\
  BOOLEAN ___(b);												\
  for(___(b)=1, maybe_start_item_timer(timer_id, count); ___(b); maybe_stop_timer(timer_id), ___(b)=0)

#define WITH_2_ITEM_TIMER(timer_id, count1, count2)  \
  BOOLEAN ___(b);												\
  for(___(b)=1, maybe_start_2_item_timer(timer_id, count1, count2); ___(b); maybe_stop_timer(timer_id), ___(b)=0)

#define WITH_3_ITEM_TIMER(timer_id, count1, count2, count3)									\
  BOOLEAN ___(b);												\
  for(___(b)=1, maybe_start_3_item_timer(timer_id, count1, count2, count3); ___(b); maybe_stop_timer(timer_id), ___(b)=0)

#define WITH_4_ITEM_TIMER(timer_id, count1, count2, count3, count4)									\
  BOOLEAN ___(b);												\
  for(___(b)=1, maybe_start_4_item_timer(timer_id, count1, count2, count3, count4); ___(b); maybe_stop_timer(timer_id), ___(b)=0)


enum PARITY
{
  EVEN,
  ODD
};

enum S2S_TIMER_COUNTERS {
  N_DEST_SURFELS,
  N_SRC_SURFELS,
  N_S2S_WEIGHTS
};

enum SURF_DYN_TIMER_COUNTERS {
  N_SURF_DYN_SURFELS
};

enum V2S_TIMER_COUNTERS {
  N_V2S_SURFELS,
  N_V2S_UBLKS,
  N_V2S_VOXELS,
  N_V2S_WEIGHTS
};

enum S2V_TIMER_COUNTERS {
  N_S2V_SURFELS,
  N_S2V_UBLKS,
  N_S2V_VOXELS,
  N_S2V_WEIGHTS
};


// For accumulating the TIMER counters
inline void timer_accum_counters(asINT32 timer_id, asINT32 item_id, asINT32 count)
{
  if (sim.async_event_flags.timers_on_p)
    (sp_timers + timer_id)->item_count[item_id] += count;
}


// counters for SP timers
struct TIMER_COUNTERS {
  asINT32 **V2S_S2V_N_SURFELS;
  asINT32 **V2S_S2V_N_UBLKS;
  asINT32 **V2S_S2V_N_VOXELS;
  asINT32 **V2S_S2V_N_WEIGHTS;
  asINT32 *S2S_N_DEST_SURFELS;
  asINT32 **S2S_N_SRC_SURFELS;
  asINT32 **S2S_N_WEIGHTS;
  asINT32 **SURF_DYN_N_SURFELS;
  asINT32 **FRINGE_V2S_S2V_N_SURFELS;
  asINT32 **FRINGE_V2S_S2V_N_UBLKS;
  asINT32 **FRINGE_V2S_S2V_N_VOXELS;
  asINT32 **FRINGE_V2S_S2V_N_WEIGHTS;
  asINT32 *FRINGE_S2S_N_DEST_SURFELS;
  asINT32 **FRINGE_S2S_N_SRC_SURFELS;
  asINT32 **FRINGE_S2S_N_WEIGHTS;
  asINT32 **FRINGE2_V2S_S2V_N_SURFELS;
  asINT32 **FRINGE2_V2S_S2V_N_UBLKS;
  asINT32 **FRINGE2_V2S_S2V_N_VOXELS;
  asINT32 **FRINGE2_V2S_S2V_N_WEIGHTS;
  asINT32 *FRINGE2_S2S_N_DEST_SURFELS;
  asINT32 **FRINGE2_S2S_N_SRC_SURFELS;
  asINT32 **FRINGE2_S2S_N_WEIGHTS;

  asINT32 ***SURF_TYPES_DYN_N_SURFELS;

  asINT32 **SLRF_SURF_DYN_N_SURFELS;
  asINT32 **MLRF_SURF_DYN_N_SURFELS;

  asINT32 **ISURF_DYN_N_ISURFELS;
  asINT32 ***ISURF_TYPES_DYN_N_ISURFELS;

  asINT32 **FAR1_N_UBLKS;
  asINT32 **FAR1_N_FAN;
  asINT32 **FAR1_N_POROUS;
  asINT32 **PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS;
  asINT32 **PDE_NEAR_UNSPLIT_NEIGHBORS_N_FAN;
  asINT32 **PDE_NEAR_UNSPLIT_NEIGHBORS_N_POROUS;
  asINT32 **PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **PDE_SAME_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **PDE_SAME_SPLIT_NEIGHBOR_N_POROUS;
  asINT32 **PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **PDE_MANY_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **PDE_MANY_SPLIT_NEIGHBOR_N_POROUS;

  asINT32 **FRINGE_PDE_FAR_N_UBLKS;
  asINT32 **FRINGE_PDE_FAR_N_FAN;
  asINT32 **FRINGE_PDE_FAR_N_POROUS;
  asINT32 **FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS;
  asINT32 **FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_FAN;
  asINT32 **FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_POROUS;
  asINT32 **FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_POROUS;
  asINT32 **FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_POROUS;
  
  asINT32 **PDE_FAR2_N_UBLKS;
  asINT32 **PDE_FAR2_N_FAN;
  asINT32 **PDE_FAR2_N_POROUS;

  asINT32 **FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS;
  asINT32 **FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_FAN;
  asINT32 **FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_POROUS;
  asINT32 **FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_POROUS;
  asINT32 **FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS;
  asINT32 **FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_FAN;
  asINT32 **FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_POROUS;

  // Counters for the VRBLKS (vr fine) groups
  // Do NOT distinguish different physics types for VR ublks

  asINT32 *VRFINE_NEAR_N_UBLKS;
  asINT32 *VRFINE_FAR_N_UBLKS;
  asINT32 *FRINGE_VRFINE_NEAR_N_UBLKS;
  asINT32 *FRINGE_VRFINE_FAR_N_UBLKS;
  asINT32 *FRINGE2_VRFINE_NEAR_N_UBLKS;
  asINT32 *FRINGE2_VRFINE_FAR_N_UBLKS;

  asINT32 **SAMPLING_SURF_DYN_N_SURFELS;

  TIMER_COUNTERS() { }
  VOID initialize();
  VOID print();
  ~TIMER_COUNTERS();
};

// Number of surfels, ublks, voxels and weights at each timestep
// Eventually we should assign the numbers to SP timer counters directly
struct PER_TIMESTEP_TIMER_COUNTERS {
  asINT32 s2s_n_dest_surfels;
  asINT32 s2s_n_src_surfels;
  asINT32 s2s_n_weights;
  asINT32 fringe_s2s_n_dest_surfels;
  asINT32 fringe_s2s_n_src_surfels;
  asINT32 fringe_s2s_n_weights;
  asINT32 fringe2_s2s_n_dest_surfels;
  asINT32 fringe2_s2s_n_src_surfels;
  asINT32 fringe2_s2s_n_weights;
  
  asINT32 surf_types_dyn_n_surfels[10]; // 10 types of surfel load
  asINT32 surf_dyn_n_surfels;
  asINT32 slrf_surf_dyn_n_surfels;
  asINT32 mlrf_surf_dyn_n_surfels;

  asINT32 isurf_types_dyn_n_isurfels[4]; // 4 types of isurfel load
  asINT32 isurf_dyn_n_isurfels;
  
  asINT32 v2s_n_surfels;
  asINT32 v2s_n_ublks;
  asINT32 v2s_n_voxels;
  asINT32 v2s_n_weights;
  asINT32 s2v_n_surfels;
  asINT32 s2v_n_ublks;
  asINT32 s2v_n_voxels;
  asINT32 s2v_n_weights;

  asINT32 fringe_v2s_n_surfels;
  asINT32 fringe_v2s_n_ublks;
  asINT32 fringe_v2s_n_voxels;
  asINT32 fringe_v2s_n_weights;
  asINT32 fringe_s2v_n_surfels;
  asINT32 fringe_s2v_n_ublks;
  asINT32 fringe_s2v_n_voxels;
  asINT32 fringe_s2v_n_weights;

  asINT32 fringe2_v2s_n_surfels;
  asINT32 fringe2_v2s_n_ublks;
  asINT32 fringe2_v2s_n_voxels;
  asINT32 fringe2_v2s_n_weights;
  asINT32 fringe2_s2v_n_surfels;
  asINT32 fringe2_s2v_n_ublks;
  asINT32 fringe2_s2v_n_voxels;
  asINT32 fringe2_s2v_n_weights;

  //asINT32 pde_n_ublks;
  //asINT32 pde_n_voxels;

  // pde_far_n_ublks and pde_near_n_ublks are actually available from the ublk groups
  
  asINT32 far1_n_ublks[3];
  asINT32 far1_n_fan[3];
  asINT32 far1_n_porous[3];
  asINT32 pde_near_unsplit_neighbors_n_ublks[3];
  asINT32 pde_near_unsplit_neighbors_n_fan[3];
  asINT32 pde_near_unsplit_neighbors_n_porous[3];
  asINT32 pde_same_split_neighbor_n_ublks[3];
  asINT32 pde_same_split_neighbor_n_fan[3];
  asINT32 pde_same_split_neighbor_n_porous[3];
  asINT32 pde_many_split_neighbor_n_ublks[3];
  asINT32 pde_many_split_neighbor_n_fan[3];
  asINT32 pde_many_split_neighbor_n_porous[3];

  asINT32 fringe_pde_far_n_ublks[3];
  asINT32 fringe_pde_far_n_fan[3];
  asINT32 fringe_pde_far_n_porous[3];
  asINT32 fringe_pde_near_unsplit_neighbors_n_ublks[3];
  asINT32 fringe_pde_near_unsplit_neighbors_n_fan[3];
  asINT32 fringe_pde_near_unsplit_neighbors_n_porous[3];
  asINT32 fringe_pde_same_split_neighbor_n_ublks[3];
  asINT32 fringe_pde_same_split_neighbor_n_fan[3];
  asINT32 fringe_pde_same_split_neighbor_n_porous[3];
  asINT32 fringe_pde_many_split_neighbor_n_ublks[3];
  asINT32 fringe_pde_many_split_neighbor_n_fan[3];
  asINT32 fringe_pde_many_split_neighbor_n_porous[3];

  asINT32 pde_far2_n_ublks[3];
  asINT32 pde_far2_n_fan[3];
  asINT32 pde_far2_n_porous[3];
  asINT32 fringe2_pde_near_unsplit_neighbors_n_ublks[3];
  asINT32 fringe2_pde_near_unsplit_neighbors_n_fan[3];
  asINT32 fringe2_pde_near_unsplit_neighbors_n_porous[3];
  asINT32 fringe2_pde_same_split_neighbor_n_ublks[3];
  asINT32 fringe2_pde_same_split_neighbor_n_fan[3];
  asINT32 fringe2_pde_same_split_neighbor_n_porous[3];
  asINT32 fringe2_pde_many_split_neighbor_n_ublks[3];
  asINT32 fringe2_pde_many_split_neighbor_n_fan[3];
  asINT32 fringe2_pde_many_split_neighbor_n_porous[3];

  // VRFINE ublk is always PDE advect ublk. 

  asINT32 vrfine_near_n_ublks;
  asINT32 vrfine_far_n_ublks;
  asINT32 fringe_vrfine_near_n_ublks;
  asINT32 fringe_vrfine_far_n_ublks;
  asINT32 fringe2_vrfine_near_n_ublks;
  asINT32 fringe2_vrfine_far_n_ublks;

  asINT32 sampling_surf_dyn_n_surfels;

  VOID clear();
  VOID print();
  VOID calculate(asINT32 scale, BOOLEAN is_timestep_even, TIMER_COUNTERS& timer_counters);
  VOID validate();
  VOID copy_to_sp_timer();
};

SP_TIMER_TYPE ublk_dyn_timer_from_fluid_type(BOOLEAN is_pde_advect_ublk, BOOLEAN is_split, BOOLEAN is_near_surface, auINT32 fluid_type);
VOID add_initial_timers_to_event_queue();

extern TIMER_COUNTERS g_timer_counters;
extern PER_TIMESTEP_TIMER_COUNTERS g_per_timestep_timer_counters;

#endif /* __SP_TIMERS_H */
