#ifndef EXA_SIMENG_GPU_COPY2DEVICE_H
#define EXA_SIMENG_GPU_COPY2DEVICE_H

/* This function is a hack to check the type of the pointer supplied to the
 * macro "get_class_member_ptr"" It allows us to limit the macro to a single
 * statement, yet have multiple checks for the argument d_addr */
template<typename T>
size_t check_class_member_ptr(T* d_addr) {
  static_assert(std::is_class<T>::value,"Device pointer must point to a class type");
  static_assert(!std::is_polymorphic<T>::value, "Device pointer must point to a non polymorphic type");
  return 1;
}

#define get_class_member_ptr(d_addr, member) \
  GPU::Ptr<void>( ((void*) ((char*) d_addr + check_class_member_ptr(d_addr) * offsetof(typename std::remove_pointer<decltype(d_addr)>::type , member))) )

struct sVR_CONFIG;
extern sVR_CONFIG* g_device_vr_config_array;

namespace GPU {

  template<typename T>
    using INTEGRAL_TYPE = std::integral_constant<bool, std::is_pointer<T>::value || std::is_integral<T>::value >;
  using IS_NOT_INTEGRAL_TYPE = std::integral_constant<bool, false>;
  using IS_INTEGRAL_TYPE = std::integral_constant<bool, true>;

  class cCOPY_TO_DEVICE
  {
  public:
    template<typename T, typename... ARGS>
      static __host__  void allocate(sDEV_PTR& d_ptr,
                                     const std::string& mem_tag,
                                     size_t n_bytes,
                                     const T* h_ptr,
                                     ARGS&&... args) {

        d_ptr = GPU::malloc(n_bytes,mem_tag);

        //When NVCC supports c++17, or if this code is compiled with the CLANG front end
        //we can perhaps switch the tagged dispatch to if constexpr(is_integral<T>)
        INTEGRAL_TYPE<T> TAG;
        cCOPY_TO_DEVICE::copy_to_device(TAG, d_ptr, n_bytes, h_ptr, std::forward<ARGS>(args)...);

      }

    /*=================================================================================================
     * @func cCOPY_TO_DEVICE::allocate overload for sDEV_PTR_TO_DEV_PTR
     *================================================================================================*/
    template<typename T, typename... ARGS>
      static __host__  sDEV_PTR  allocate(sDEV_PTR_TO_DEV_PTR& d_ptr_to_ptr,
                                          const std::string& mem_tag,
                                          size_t n_bytes,
                                          const T* h_ptr,
                                          ARGS&&... args) {

        //We must allocate new block of memory and set the value of d_ptr_to_ptr to
        //the pointer returned from cuda Malloc.
        sDEV_PTR d_ptr_to_new_mem_blk = GPU::malloc(n_bytes,mem_tag);
        checkCudaErrors( cudaMemcpy(d_ptr_to_ptr.get(), &d_ptr_to_new_mem_blk, sizeof(T*), cudaMemcpyHostToDevice) );

        //When NVCC supports c++17, or if this code is compiled with the CLANG front end
        //we can perhaps switch the tagged dispatch to if constexpr(is_integral<T>)      
        INTEGRAL_TYPE<T> TAG;
        cCOPY_TO_DEVICE::copy_to_device(TAG, d_ptr_to_new_mem_blk, n_bytes, h_ptr, std::forward<ARGS>(args)...);
        return d_ptr_to_new_mem_blk;
      }


    template<typename T>
      __host__ static VOID trivial_copy(sDEV_PTR& d_ptr,
                                        size_t n_bytes,
                                        const T* h_ptr) {
        GPU::memcpy_h2d(d_ptr, h_ptr, n_bytes);
      }

    template<typename T>
      __host__ static VOID trivial_copy(sDEV_PTR_TO_DEV_PTR& d_ptr_to_ptr,
                                        const T* d_ptr_on_host) {
        GPU::memcpy_h2d(d_ptr_to_ptr, &d_ptr_on_host, sizeof(T*));
      }    

    // The method below is used to copy trivial types such as bytes of char
    template<typename T, typename... ARGS>
      __HOST__ static VOID copy_to_device(std::integral_constant<bool, true> TAG,
                                          sDEV_PTR& d_ptr,
                                          size_t n_bytes,
                                          const T* h_ptr,
                                          ARGS&&... args) {

        trivial_copy(d_ptr, n_bytes, h_ptr);
      }

    //The method below delegates the actual copy to specialization of copy_to_device for type T
    template<typename T, typename... ARGS>
      __HOST__ static VOID copy_to_device(std::integral_constant<bool, false> TAG,
                                          sDEV_PTR& d_ptr,
                                          size_t n_bytes,
                                          const T* h_ptr,
                                          ARGS&&... args) {

        T::copy_to_device(d_ptr, n_bytes, h_ptr, std::forward<ARGS>(args)...);
      }
  };

}

VOID change_ptrs_h2d(sMBLK::sUBLK_BOX& d_box,
                     const sHMBLK::sUBLK_BOX& h_box);

VOID change_ptrs_h2d(sHMBLK& d_ptr,
                     const sHMBLK& h_ptr,
                     const std::unordered_map<sHMBLK::UBLK_BOX, sMBLK::UBLK_BOX>& hd_boxes_map,
                     const sMEAS_WINDOW_COLLECTION& windows);

VOID copy_boundary_seeding_counts_d2h();
#endif


