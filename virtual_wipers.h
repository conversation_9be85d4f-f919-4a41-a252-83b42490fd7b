#ifndef _VIRTUAL_WIPERS_H_
#define _VIRTUAL_WIPERS_H_


#include "common_sp.h"
//#include "sim.h"
#include "particle_solver_data.h"

typedef class sVIRTUAL_WIPER {

  std::string m_name;
  std::vector<asINT32> wiped_surfaces;
  std::vector<asINT32> wiper_parts;
  std::vector<asINT32> monitors;

  sPARTICLE_VAR range_of_influence();
  LGI_VIRTUAL_WIPER_REC m_parameters;
  BOOLEAN m_start_requested;

  sVIRTUAL_WIPER() {
    m_start_requested = FALSE;
  }

  //The following are are initialized form the above parameters.
  sPARTICLE_VAR m_arm_radius[N_SPACE_DIMS];
  sPARTICLE_VAR m_blade_span_direction[N_SPACE_DIMS];
  sPARTICLE_VAR m_blade_height_direction[N_SPACE_DIMS];
  sPARTICLE_VAR m_blade_length;
  sPARTICLE_VAR m_align_to_rotation_axis_transformation[N_SPACE_DIMS][N_SPACE_DIMS];

  sPARTICLE_VAR m_blade_normal[N_SPACE_DIMS]; //now this is computed from other items in the parameters struct.  Previously, it was provided by the user.

  //These are updated each timestep.
  sPARTICLE_VAR m_current_arm_radius[N_SPACE_DIMS];
  sPARTICLE_VAR m_current_blade_normal[N_SPACE_DIMS];
  sPARTICLE_VAR m_current_blade_span_direction[N_SPACE_DIMS];
  sPARTICLE_VAR m_current_blade_height_direction[N_SPACE_DIMS];
  sPARTICLE_VAR m_current_blade_position[N_SPACE_DIMS];
  sPARTICLE_VAR m_current_angle;
  sPARTICLE_VAR m_previous_angle;

  VOID initialize();
  friend class sDGF_READER;

 public:
  BOOLEAN surfel_is_near_virtual_wiper(
                                       sSURFEL *surfel,
                                       PARTICLE_VAR blade_to_voxel_centroid_dist, //If a wiper is nearby, these items are set.
                                       PARTICLE_VAR blade_normal_direction,
                                       PARTICLE_VAR local_blade_velocity,
                                       PARTICLE_VAR blade_midpoint);
  VOID set_start_time(TIMESTEP timestep) {
    if(!m_start_requested) {
      m_start_requested = TRUE;
      m_parameters.initial_delay = timestep;
    }
  }
  VOID update_position();
  VOID flag_surfels_wiped_by_virtual_wiper();

} *VIRTUAL_WIPER;

#endif



