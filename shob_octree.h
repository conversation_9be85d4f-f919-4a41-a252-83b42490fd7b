#ifndef _SHOB_OCTREE_H
#define _SHOB_OCTREE_H

#include "ublk.h"


// These 2 definitions belong in CP_SP_LIB/lattice.h
inline asINT32 scale_to_sri_scale(asINT32 scale) { return sim_num_scales() - 1 - scale; }
inline asINT32 scale_to_sim_scale(asINT32 scale) { return scale_to_sri_scale(scale); }

class sOCTREE_NODE; // forward declaration so type can be used in sOCTREE_NODE_CHILD

union OCTREE_NODE_CHILD {
private:
  sUBLK          *m_ublk;
  sOCTREE_NODE     *m_node;
  // sDYNBLK_LIST_ELT *m_ublk_list; // support for split voxels

public:
  BOOLEAN is_ublk()    { return scalar_mask_pointer_to_int(m_ublk, 1); }
  UBLK  ublk()         { return (UBLK)scalar_mask_pointer(m_ublk, ~3); }
  OCTREE_NODE_CHILD(UBLK ublk) { m_ublk = (UBLK)scalar_or_pointer(ublk, 1); }

  BOOLEAN is_node()    { return scalar_mask_pointer_to_int(m_node, 2); }
  sOCTREE_NODE *node() { return (sOCTREE_NODE *)scalar_mask_pointer(m_node, ~3); }
  OCTREE_NODE_CHILD(sOCTREE_NODE *node) { m_node = (sOCTREE_NODE *)scalar_or_pointer(node, 2); }

  BOOLEAN is_null()    { return ((UBLK)scalar_mask_pointer(m_ublk, ~3)) == NULL; }
  OCTREE_NODE_CHILD()  { m_ublk = NULL; }
};

typedef struct sOCTREE_NODE {
  OCTREE_NODE_CHILD m_children[8];

  sOCTREE_NODE() { } // constructor - sets m_children to NULL
} *OCTREE_NODE;

// OCTREE is an octree with a build process that is convenient if all an SP's ublks are to
// be entered in the octree. Usage:
//
// 1. Call init(min, max, coarsest_voxel_scale, n_ublks_in_octree) to initialize the octree.
// 2. Enter all ublks into the octree via enter_ublk().
// 3. Call find() to locate the voxel that contains a particular 3D point.
// 4. Delete the octree if desired.
typedef class sOCTREE {
protected:
  STP_COORD m_min[3];
  STP_COORD m_max[3];
  UBLK_ID   m_n_ublks_per_dim[3];
  STP_SCALE m_coarsest_scale;
  STP_SCALE m_sri_coarsest_ublk_scale;

  // octree nodes are allocated from an EARRAY so that the octree can be easily "deleted"
  tEARRAY< sOCTREE_NODE, 
           8 /* initial page table size */, 
           64 /* page size */ > octree_nodes;

  OCTREE_NODE_CHILD *top_level_grid; // 3D array

  VOID clear()
  {
    if (top_level_grid) {
      delete [] top_level_grid;
      top_level_grid = NULL;
    }
  }

  asINT32 compute_top_level_index(STP_COORD x, STP_COORD y, STP_COORD z)
  {
    STP_COORD zz = (z - m_min[2]) >> m_sri_coarsest_ublk_scale;
    STP_COORD yy = (y - m_min[1]) >> m_sri_coarsest_ublk_scale;
    STP_COORD xx = (x - m_min[0]) >> m_sri_coarsest_ublk_scale;
    return (zz
            + (m_n_ublks_per_dim[2] * (yy
                                       + (m_n_ublks_per_dim[1] * xx))));
  }

  VOID build_top_level_grid(STP_UBLK_ID n_ublks_in_octree)
  {
    // Adjust m_min and m_max to coarsest ublk boundaries
    asINT32 coarsest_ublk_scale = coarsen_scale(m_coarsest_scale); // convert from voxel to ublk scale
    m_sri_coarsest_ublk_scale = scale_to_sri_scale(coarsest_ublk_scale);

    UBLK_ID n_top_level;
    while(1) {
      STP_COORD coarsest_ublk_size = 1 << m_sri_coarsest_ublk_scale; /////scale_to_voxel_size(coarsest_ublk_scale);
      STP_COORD mask = ~(coarsest_ublk_size - 1); // mask used to achieve coarsest ublk alignment
      
      n_top_level = 1;
      ccDOTIMES(i, 3) {
        m_min[i] = m_min[i] & mask;
        m_max[i] = (m_max[i] + (coarsest_ublk_size - 1)) & mask;
        m_n_ublks_per_dim[i] = (m_max[i] - m_min[i]) >> m_sri_coarsest_ublk_scale;
        n_top_level *= m_n_ublks_per_dim[i];
      }

      // Ensure that the top-level grid does not have an excessive number of entries. If the top level  
      // grid is too sparse, coarsen it one level. The factor of 2 here is just a reasonable threshold.
      if (n_top_level <= 2 * n_ublks_in_octree)  //changed < to <= to account for the case when n_ublks_in_octree = 0 (wanderer 9/27/12)
        break;
      m_sri_coarsest_ublk_scale++; // one level coarser
    }

    top_level_grid = new OCTREE_NODE_CHILD[n_top_level]; // all entries set to NULL
  }

public:
  VOID init(STP_COORD min[3], STP_COORD max[3], asINT32 coarsest_voxel_scale, STP_UBLK_ID n_ublks_in_octree)
  {
    ccDOTIMES(i, 3) {
      m_min[i] = min[i];
      m_max[i] = max[i];
    }
    m_coarsest_scale = coarsest_voxel_scale;

    build_top_level_grid(n_ublks_in_octree);
  }

  ~sOCTREE() { clear(); }

  VOID enter_ublk(UBLK ublk)
  {
    asINT32 ublk_scale = 1 + scale_to_sri_scale(ublk->scale()); // convert from voxel to ublk scale

    auto ublk_location = ublk->m_location;
    asINT32 top_level_index = compute_top_level_index(ublk_location[0], ublk_location[1], ublk_location[2]);
                         
    if (m_sri_coarsest_ublk_scale == ublk_scale) {
      // This octree currently ignores all voxels cut by surfels - see discussion in find() method.
      // if (!top_level_grid[top_level_index].is_null())
      //   msg_internal_error("Have not yet added support for split ublks to octree");
      top_level_grid[top_level_index] = OCTREE_NODE_CHILD(ublk);
    } else {
      // adjust coords to be relative to the lower-left-rear of the top-level cube
      STP_COORD mask = (1 << m_sri_coarsest_ublk_scale) - 1;
      STP_COORD x = ublk_location[0] & mask;
      STP_COORD y = ublk_location[1] & mask;
      STP_COORD z = ublk_location[2] & mask;

      OCTREE_NODE_CHILD child = top_level_grid[top_level_index];
      if (child.is_null())
        child = top_level_grid[top_level_index] = OCTREE_NODE_CHILD( octree_nodes.append() );

      asINT32 scale = m_sri_coarsest_ublk_scale - 1; // one level finer
      while (1) {
        auINT32 scale_mask = 1 << scale;
        asINT32 child_index = ((  ((x & scale_mask) >> scale) << 2)
                               | (((y & scale_mask) >> scale) << 1)
                               | (((z & scale_mask) >> scale) << 0));
        if (ublk_scale == scale) {
          // This octree currently ignores all voxels cut by surfels - see discussion in find() method.
          // if (!child.node()->m_children[child_index].is_null())
          //   msg_internal_error("Have not yet added support for split ublks to octree");
          child.node()->m_children[child_index] = OCTREE_NODE_CHILD(ublk);
          break;
        }
        OCTREE_NODE_CHILD grandchild = child.node()->m_children[child_index];
        if (grandchild.is_null())
          child = child.node()->m_children[child_index] = OCTREE_NODE_CHILD( octree_nodes.append() );
        else
          child = grandchild;
        scale--;  // one level finer
      }
    }
  }

  // This method can be called with x, y, and z as floats. The method args are declared as integers
  // because we immediately want to convert to integral finest voxel coordinates. This does
  // not compromise our ability to find the voxel that contains an arbitrary point.
  //
  // If a voxel is cut by surfels, we can't easily determine whether the point (x,y,z) lies above
  // the surface. Thus this routine does not consider such voxels (i.e. it won't return a voxel cut
  // by surfels). This problem becomes even more pronounced in the presence of split voxels. In the
  // future, one way around this limitation is to shoot a ray from (x,y,z) and find the first
  // surfel it crosses. If the surfel is backward-facing, the point is under the surface. For this
  // to work properly, we must consider not just surfels, but the complete polyhedron that defines
  // the voxel. This scheme extends naturally to split voxels, where we recognize that a given
  // surfel bounds one and only one voxel instance within a cube.
  UBLK find(STP_COORD x, STP_COORD y, STP_COORD z, sINT8 &voxel_return)
  {
    if (x < m_min[0] || y < m_min[1] || z < m_min[2]
        || x >= m_max[0] || y >= m_max[1] || z >= m_max[2]) // must be >= (not >) - see comment above
      return NULL;

    UBLK_ID top_level_index = compute_top_level_index(x, y, z);

    // adjust coords to be relative to the lower-left-rear of the top-level cube
    STP_COORD mask = (1 << m_sri_coarsest_ublk_scale) - 1;
    x &= mask;
    y &= mask;
    z &= mask;
    
    OCTREE_NODE_CHILD child = top_level_grid[top_level_index];

    asINT32 scale = m_sri_coarsest_ublk_scale - 1;
    while (!child.is_null()) {
      auINT32 scale_mask = 1 << scale;
      if (child.is_ublk()) {
        voxel_return = voxel_to_num((x & scale_mask) >> scale, 
                                    (y & scale_mask) >> scale, 
                                    (z & scale_mask) >> scale);
        UBLK ublk = child.ublk();
     
#if 0
        // Do not return a voxel that is cut by surfels.
        if (ublk->is_near_surface()
            && ((NEARBLK)ublk)->surf_geom_data()->pfluids[voxel_return] < 1)
          return NULL;
        else
          return ublk;
#else
        //Do return a voxel that is cut by a surfel but hope for the best (-wanderer 9/5/13)
        //This may be acceptable as long as the voxel is not also a split voxel.
        //changed in response to PR 28561
        return ublk;
#endif
      } else {
        asINT32 child_index = ((  ((x & scale_mask) >> scale) << 2)
                               | (((y & scale_mask) >> scale) << 1)
                               | (((z & scale_mask) >> scale) << 0));
        scale--;
        OCTREE_NODE node = child.node();
        child = node->m_children[child_index];
      } 
    }
    
    return NULL;
  }
} *OCTREE;  

// SUB_OCTREE is an octree with a build process designed for the situation where the octree
// spans a subset of the ublks owned by an SP. Other than the build process, it is identical
// to OCTREE. Usage:
//
// 1. Call init() to initialize the octree.
// 2. Register all ublks included in the octree via register_ublk().
// 3. Call build_octree() to actually build the octree.
// 4. Call find() to locate the voxel that contains a particular 3D point.
// 5. Delete the octree if desired.
typedef class sSUB_OCTREE : public sOCTREE {
private:
  VMEM_VECTOR<UBLK> dynblks;

public:
  VOID init() {
    vcopy(m_min, sim.simvol_size);
    vzero(m_max);
    m_coarsest_scale = FINEST_SCALE;

    vzero(m_n_ublks_per_dim);
    m_sri_coarsest_ublk_scale = 0;
    top_level_grid = NULL;
  }

  ~sSUB_OCTREE() { /* The destructors of the base class and child classes suffice */ }

  VOID register_ublk(UBLK ublk) {
    dynblks.push_back(ublk);

    if (is_scale_coarser(ublk->scale(), m_coarsest_scale))
      m_coarsest_scale = ublk->scale();

    auto ublk_location = ublk->m_location;
    ccDOTIMES(i, 3) {
      if (ublk_location[i] < m_min[i]) {
        m_min[i] = ublk_location[i];
      }
      STP_COORD max = ublk_location[i] + sim_scale_to_ublk_size(ublk->scale());
      if (max > m_max[i])
        m_max[i] = max;
    }
  }

  VOID build_octree() 
  {
    build_top_level_grid(dynblks.size());
    ccDOTIMES(j, dynblks.size()) {
      UBLK ublk = dynblks[j];
      enter_ublk(ublk);
    }

    // free storage associated with dynblks vector
    dynblks.resize(0);
    dynblks.trim();
    
  }

} *SUB_OCTREE;


#endif
