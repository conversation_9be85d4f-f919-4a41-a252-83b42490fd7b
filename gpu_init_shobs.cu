/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include <cuda.h>
#include <cuda_runtime.h>
#include "iostream"
#include <cassert>
#include "common_sp.h"
#include "ublk.h"
#include "gpu_globals.hcu"
#include "ublk_table.h"
#include "surfel.h"
#include "gpu_surfel_interactions.h"
#include "gpu_copy_to_device.h"
#include "shob_groups.h"
#include "seed_sp.h"

#include PHYSICS_H

namespace GPU {

  static
  __DEVICE__ VOID copy_base_ublk_data(sMBLK& dst,
                                      sHMBLK& src)
  {
  
    if(threadIdx.x == 0) {
      (sSHOB&) dst = (sSHOB&) src;
      dst.m_split_tagged_instance = src.m_split_tagged_instance;

      //Box has deleted constructor/copy assignment
      memcpy(&dst.m_box_access, &src.m_box_access, sizeof(sMBLK::sBOX_ACCESS));
    
      dst.fluid_like_voxel_mask = src.fluid_like_voxel_mask;
      dst.basic_fluid_voxel_mask = src.basic_fluid_voxel_mask;
      dst.m_split_info = src.m_split_info;
      dst.m_ublk_attributes = src.m_ublk_attributes;

      dst.lrf_surfels_interaction_mask = src.lrf_surfels_interaction_mask;
      dst.non_lrf_surfels_interaction_mask = src.non_lrf_surfels_interaction_mask;
      dst.lb_interaction_voxel_mask = src.lb_interaction_voxel_mask;
      dst.pde_2_interaction_voxel_mask = src.pde_2_interaction_voxel_mask;
      dst.m_are_any_neighbors_different_scale = src.m_are_any_neighbors_different_scale;
      dst.m_are_any_neighbors_split = src.m_are_any_neighbors_split;
      dst.m_vr_scale_diff = src.m_vr_scale_diff;
      dst.voxel_smart_seed_mask = src.voxel_smart_seed_mask;
      dst.all_neighbors_voxel_mask = src.all_neighbors_voxel_mask;
    
      ccDOTIMES(i, 6) {
        dst.neighbor_2_diff_scale_mask[i] = src.neighbor_2_diff_scale_mask[i] ;
      }

      memcpy(&dst.m_location, &src.m_location, sizeof(src.m_location));
      memcpy(&dst.m_vr_topology, &src.m_vr_topology, sizeof(src.m_vr_topology));
      dst.m_send_ib_bf_data = src.m_send_ib_bf_data;
      dst.m_is_bsurfel_interacting = src.m_is_bsurfel_interacting;
      dst.m_ib_bf = (sMBLK::sBSURFEL_BODY_FORCE *) src.m_ib_bf;
    }

    dst.m_fluid_connect_masks[threadIdx.x] = src.m_fluid_connect_masks[threadIdx.x];
    dst.m_path_connect_masks[threadIdx.x] = src.m_path_connect_masks[threadIdx.x];

    if (!src.is_ghost()) {
      dst.m_dynamics_data_types[threadIdx.x] = src.m_dynamics_data_types[threadIdx.x];
    }

    ccDOTIMES(i, 3) {
      dst.centroids(threadIdx.x, i) = src.centroids(threadIdx.x, i);
    }
  }  

  __global__ VOID prelimiary_init_mblks(sHMBLK_HEADER* mblk_headers) {
    sHMBLK_HEADER& h = mblk_headers[blockIdx.x];
    sMBLK& mblk = *h.get_device_mblk();
    copy_base_ublk_data(mblk, h);
    GPU_SYNC_THREADS();
    if (threadIdx.x == 0) {
      if(!h.is_vr_fine() && !h.is_solid() && !h.is_ghost())
      { //Dyn data
        auto d_dyn_data = mblk.dynamics_data();
        auto h_dyn_data = h.dynamics_data();
        memcpy(d_dyn_data, h_dyn_data, h.size_of_dyn_data());
      }
      if (h.is_near_surface()) {
        auto d_near_lb_data = mblk.surf_lb_data();
        auto h_near_lb_data = h.surf_lb_data();
        memcpy(d_near_lb_data, h_near_lb_data, sizeof(sMBLK::sNEARBLK_LB_DATA));

        auto d_near_geom_data = mblk.surf_geom_data();
        auto h_near_geom_data = h.surf_geom_data();
        memcpy(d_near_geom_data, h_near_geom_data, sizeof(sMBLK::sNEARBLK_GEOM_DATA));
      }
      if (h.is_vr_fine()) {
        auto d_vr_fine_data = mblk.vr_fine_data();
        auto h_vr_fine_data = h.vr_fine_data();
        memcpy(d_vr_fine_data, h_vr_fine_data, sizeof(sMBLK::sVR_FINE_INTERFACE_DATA));
      } else if (h.is_vr_coarse()) {
        auto d_vr_coarse_data = mblk.vr_coarse_data();
        auto h_vr_coarse_data = h.vr_coarse_data();
        memcpy(d_vr_coarse_data, h_vr_coarse_data, sizeof(sMBLK::sVR_COARSE_INTERFACE_DATA));
      }
    }

    auto& simc = get_simc_ref();
    if (simc.do_smart_seed && !h.is_solid()) {
      auto d_smart_seed_data = mblk.smart_seed_data();
      auto h_smart_seed_data = h.smart_seed_data();
      uINT8 d_seed_var_spec_index = d_smart_seed_data->w[threadIdx.x].seed_var_spec_index;
      uINT8 h_seed_var_spec_index = h_smart_seed_data->w[threadIdx.x].seed_var_spec_index;
      uINT8 d_controller_index = g_fluid_seed_var_specs[d_seed_var_spec_index].seed_controller;
      uINT8 h_controller_index = g_fluid_seed_var_specs[h_seed_var_spec_index].seed_controller;
      d_smart_seed_data->copy(*h_smart_seed_data, threadIdx.x, d_controller_index, h_controller_index);
    }
  }

  __host__ VOID sUBLK_TABLE::set_capacity(size_t n_ublks) {
    m_device_ublks.resize(n_ublks);
  }

  __host__ VOID sUBLK_TABLE::add(UBLK d_ublk, SHOB_ID id) {
    m_device_ublks[id] = d_ublk;
  }

  __host__ UBLK sUBLK_TABLE::map_H2D(const sHMBLK*const h_ublk) {
    UBLK ublk = m_device_ublks.at(h_ublk->id());
    assert(ublk);
    return ublk;
  }

  __host__ UBLK* sUBLK_TABLE::copy_ublk_array_to_dev() {
    sDEV_PTR dev_ublk_array(nullptr);
    cCOPY_TO_DEVICE::allocate(dev_ublk_array, "ublk ptrs",
                              m_device_ublks.size() * sizeof(GPU::sUBLK*),
                              &m_device_ublks[0]);

    return (UBLK*) dev_ublk_array.get();
  }

  __host__ VOID sUBLK_TABLE::allocate_ublks(const sH2D_EXPORT& h_data) {

    const std::vector<sHMBLK*>& h_mblk_table = *h_data.m_mblk_table;
    
    size_t n_ublks = h_mblk_table.size();

    set_capacity(n_ublks);

    for ( size_t i = 0; i < n_ublks; i++ ) {
      sHMBLK_HEADER* h_ublk = as_header(h_mblk_table[i]);
      size_t d_ublk_size  = h_ublk->size_of_device_mblk();
      GPU::Ptr<sMBLK> d_mblk((sMBLK*) GPU::malloc(d_ublk_size, "ublk").get());
      GPU::memset(d_mblk, 0, d_ublk_size);
      add(d_mblk.get(), h_ublk->id());
      h_ublk->set_device_mblk(d_mblk.get());
    }    
  }

  __host__ VOID sUBLK_TABLE::init_mblks_on_device(const std::vector<sHMBLK*>& h_mblk_table,
                                                   const sMEAS_WINDOW_COLLECTION& windows) {

    size_t n_ublks = h_mblk_table.size();
    auto device_boxes = assign_device_boxes(g_mega_boxes);
    std::unordered_map<sHMBLK::UBLK_BOX, sMBLK::UBLK_BOX> hd_boxes_map;
    for (int i = 0; i < g_mega_boxes.size(); i++)  {
      hd_boxes_map[g_mega_boxes[i]] = device_boxes[i];
    }

    //These copies of headers will be modified to point to device
    //specific information where applicable before they are copied to the GPU
    std::vector<sHMBLK_HEADER> d_mblk_headers_for_copy(n_ublks);
    for (size_t i = 0; i < n_ublks; i++) {
      sHMBLK_HEADER* h_mblk_header = as_header(h_mblk_table[i]);
      d_mblk_headers_for_copy[i] = *(h_mblk_header);
      change_ptrs_h2d(d_mblk_headers_for_copy[i],
                      *h_mblk_header,
                      hd_boxes_map,
                      windows);
    }

    for (int i = 0; i < (int) HD_SLAB_ALLOCATOR_TYPES::N_TYPES; i++) {
      auto type = (HD_SLAB_ALLOCATOR_TYPES) i;
      if (sHD_SLAB_ALLOCATOR_FACTORY::has_allocator(type)) {
        sHD_SLAB_ALLOCATOR_FACTORY::get_allocator(type)->copy_all_slabs_h2d();
      }
    }
    
    auto d_mblk_solver_blocks_mgr = copy_mblk_solver_blocks_h2d(g_host_mblk_solver_blocks);

    GPU::Ptr<sHMBLK_HEADER> d_mblk_headers = GPU::malloc<sHMBLK_HEADER>(n_ublks, "gpu_mblk_headers");
    GPU::copy_h2d_async(d_mblk_headers, d_mblk_headers_for_copy.data(), n_ublks, GPU::g_stream);

    prelimiary_init_mblks<<<n_ublks, N_VOXELS_64, NO_DYN_SHMEM, GPU::g_stream>>>(d_mblk_headers.get());
    checkCudaErrors( cudaPeekAtLastError() );
    cudaStreamSynchronize(g_stream);
    GPU::free(d_mblk_headers, "gpu_mblk_headers");
    free_mblk_solver_blocks(g_host_mblk_solver_blocks, d_mblk_solver_blocks_mgr);
    sHD_SLAB_ALLOCATOR_FACTORY::delete_allocator(HD_SLAB_ALLOCATOR_TYPES::TMP_INIT_DATA);
  }

  __host__ tVR_UBLK_PTR<GPU::sMBLK> sUBLK_TABLE::translate_vr_ublk_H2D(tVR_UBLK_PTR<sHMBLK> h_vr_ublk)
  {
    tVR_UBLK_PTR<GPU::sMBLK> d_vr_ublk;
    sHMBLK* h_ublk = h_vr_ublk.ublk();
    uint8_t child_ublk_offset = h_vr_ublk.offset();
    GPU::UBLK d_ublk = map_H2D(h_ublk);
    d_vr_ublk.set_mblk(d_ublk, child_ublk_offset);
    return d_vr_ublk;
  }
  
  __host__ VOID sUBLK_TABLE::build_vr_coarse_explode_groups() 
  {
    size_t offset=0;
    std::unordered_set<GPU::sMBLK*> vr_coarse_mblks;
    std::vector<GPU::MBLK> vr_coarse_mblks_contig;
    DO_SCALES_COARSE_TO_FINE(scale) {
      for(const auto group_type : nSHOB_CATEGORIES::VRFINE_TYPES) {
        DO_MBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
          vr_coarse_mblks.clear();

          DO_UBLKS_OF_GROUP(vr_fine_mblk, group) {
            auto fine_data = vr_fine_mblk->vr_fine_data();
            ccDOTIMES(vr_fine_child_ublk, N_VOXELS_8) {
              auto vr_coarse_ptr = fine_data->vr_coarse_ublk(vr_fine_child_ublk);
              if (vr_coarse_ptr) {
                auto d_vr_coarse_ptr = translate_vr_ublk_H2D(vr_coarse_ptr);
                auto * d_vr_coarse_mblk = d_vr_coarse_ptr.ublk();

                vr_coarse_mblks.insert(d_vr_coarse_mblk);
              }
            }
          }

          size_t count = vr_coarse_mblks.size();
          const auto [it,success] = m_vr_group_properties.emplace(Key{group_type,scale,group->m_dest_sp.nsp()}, VRGroupProperties{offset, count});
          cassert(success);
          offset += count;
          for(auto&& vr_coarse_mblk : vr_coarse_mblks) {
            vr_coarse_mblks_contig.push_back(vr_coarse_mblk);
          }
        }
      }
    }
    using GPU::Ptr;
    Ptr<GPU::sMBLK*> d_vr_coarse_mblk_ptrs = GPU::malloc<GPU::sMBLK*>(vr_coarse_mblks_contig.size());

    GPU::copy_h2d(d_vr_coarse_mblk_ptrs, vr_coarse_mblks_contig.data(), vr_coarse_mblks_contig.size());

    checkCudaErrors( cudaMemcpyToSymbol(GPU::g_vr_coarse_explode_mblks, &d_vr_coarse_mblk_ptrs, sizeof(void*), 0, cudaMemcpyHostToDevice));

  }
  
  __host__ std::vector<sMBLK::UBLK_BOX> sUBLK_TABLE::assign_device_boxes(std::vector<sHMBLK::UBLK_BOX> &mega_boxes) {
    std::vector<sMBLK::UBLK_BOX> device_boxes;
    for (auto h_box : mega_boxes) {
      sMBLK::sUBLK_BOX d_box_to_copy(h_box);
      change_ptrs_h2d(d_box_to_copy, *h_box);
      auto d_ptr = GPU::malloc<sMBLK::sUBLK_BOX>(1, "box");
      GPU::copy_h2d_async(d_ptr, &d_box_to_copy, 1, GPU::g_stream);
      device_boxes.push_back(d_ptr.get());
    }
    return device_boxes;
  }

  __host__ VOID sUBLK_TABLE::fill_dev_ublk_vector_from_host_fset(std::vector<GPU::UBLK>& device_ublks_in_group,
                                                                 MBLK_FSET group_fset,
                                                                 UBLK_GROUP_TYPE group_type,
                                                                 int n_scales) {
    // if(group_type== FRINGE_FARBLK_GROUP_TYPE)
    //   printf("MPI(%d) : fill_dev_ublk_vector_from_host_fset() for Fringe Far Block group type\n", my_proc_id);
    device_ublks_in_group.clear();
    size_t ublk_offset = 0;

    for (int scale = 0; scale < n_scales; scale++) {

      const auto& set_of_ublk_groups = group_fset->get_groups_of_scale(scale);
      std::set<int> dest_sp_set;
      for(MBLK_GROUP group : set_of_ublk_groups) {
        if(group->n_shob_ptrs()>0) 
        {
        const int group_dest_sp = group->m_dest_sp.nsp();
        // printf("MPI(%d) : fill_dev_ublk_vector_from_host_fset() for group type %d, dest_sp =%d\n", my_proc_id, group_type, group_dest_sp);
        // cassert(!dest_sp_set.contains(group->dest_sp));
        {
          const auto [it,success] = dest_sp_set.insert(group_dest_sp);
          cassert(success);
        }
        bool has_meas = false;

         for(HMBLK h_ublk = group->shob_ptrs();
            h_ublk != nullptr;
            h_ublk = h_ublk ? h_ublk->m_next : nullptr) {
          if (!has_meas && as_header(h_ublk)->size_of_dyn_data()) {
            auto dyn_data = h_ublk->dynamics_data();
            asINT32 n_windows = dyn_data->n_meas_windows();
            MBLK_VOXEL_MASK dynamics_voxel_mask = h_ublk->fluid_like_voxel_mask;

            has_meas = has_meas || n_windows > 0;

            if (h_ublk->basic_fluid_voxel_mask.any()) {
              dynamics_voxel_mask &= ~h_ublk->basic_fluid_voxel_mask;
              dyn_data = (sHMBLK::sUBLK_DYNAMICS_DATA*) dyn_data->next();
            }
            
            while(dynamics_voxel_mask.any()) {
              auto special_dyn_data = (sHMBLK::sSPECIAL_UBLK_DYNAMICS_DATA*) dyn_data;
              asINT32 n_windows = dyn_data->n_meas_windows();
              MBLK_VOXEL_MASK special_fluid_voxel_mask = special_dyn_data->voxel_mask();

              has_meas = has_meas || n_windows > 0;

              dynamics_voxel_mask &= ~special_fluid_voxel_mask;
              dyn_data = (sHMBLK::sUBLK_DYNAMICS_DATA*) dyn_data->next();
            }
          }
          device_ublks_in_group.push_back(map_H2D(h_ublk));

        } //group loop

        const auto [it,success] = m_group_properties.emplace(Key{group_type,scale,group_dest_sp}, GroupProperties{ublk_offset, group->n_shob_ptrs(), has_meas});
        cassert(success);
        ublk_offset += group->n_shob_ptrs();
        }
      } //set of groups loop
      const auto [it,success] = m_group_dest_sp.emplace(std::make_pair(group_type,scale),dest_sp_set);
      cassert(success);
      // if(my_proc_id==0)
      //   printf("MPI(%d) : fill_dev_ublk_vector_from_host_fset() for group type %d, dest_sp_set.size()=%lu\n", my_proc_id, group_type, dest_sp_set.size());
    } //scale loop
  }

  __host__ VOID sUBLK_TABLE::create_ublk_groups(MBLK_FSET (*h_ublk_groups) [N_UBLK_GROUP_TYPES],
                                                int n_scales) 
  {

    std::vector<GPU::UBLK> device_ublks_in_group;

    for (int group_type = 0; group_type < N_UBLK_GROUP_TYPES; group_type++) {

      MBLK_FSET group_fset = (*h_ublk_groups)[group_type];
      GPU::sUBLK_GROUP for_copy;

      if (group_fset && group_fset->n_groups() > 0) {
        fill_dev_ublk_vector_from_host_fset(device_ublks_in_group, group_fset,
                                            (UBLK_GROUP_TYPE) group_type, n_scales);
        for_copy.m_count  = device_ublks_in_group.size();
        for_copy.m_ublks  = &device_ublks_in_group[0];
      } //group_fset->n_groups > 0
      else {
        for_copy.m_count = 0;
        for_copy.m_ublks = nullptr;
      }

      sDEV_PTR d_ublk_group(nullptr);

      cCOPY_TO_DEVICE::allocate(d_ublk_group, "ublk group",
                                sizeof(GPU::sUBLK_GROUP),
                                &for_copy);


      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_ublk_groups,
                                          d_ublk_group.get(),
                                          sizeof(GPU::sUBLK_GROUP),
                                          sizeof(GPU::sUBLK_GROUP) * group_type,
                                          cudaMemcpyHostToDevice) );      

    }
  }
  

  /* @fcn copy_ublk_from_D2H
   * Useful for verification, one can call this method to check GPU computed values
   * and dump them back on the host ublk. This is a brute force method till we figure
   * out measurements
   */
  __host__ sMBLK* copy_ublk_from_d2h(const sHMBLK* h_ublk) {
    
    static std::map<STP_SHOB_ID, sMBLK*> d_ublk_copy_on_host_map;
    GPU::UBLK d_ublk = GPU::g_ublk_table.map_H2D(const_cast<sHMBLK*>(h_ublk));
    
    bool allocate_space_for_this_hublk = d_ublk_copy_on_host_map.find(h_ublk->id()) == d_ublk_copy_on_host_map.end();
    size_t n_bytes = as_header(h_ublk)->size_of_device_mblk();
    if (allocate_space_for_this_hublk) {
      d_ublk_copy_on_host_map[h_ublk->id()] = (sMBLK*) ::malloc(n_bytes);
    }
    auto d_ublk_copy_on_host = d_ublk_copy_on_host_map[h_ublk->id()];
    GPU::memcpy_d2h(d_ublk_copy_on_host, sDEV_PTR(d_ublk), n_bytes);
    return d_ublk_copy_on_host;
  }  

  __host__ VOID sSURFEL_TABLE::set_capacity(size_t n_surfels) {
    m_device_surfels.resize(n_surfels);
  }

  __host__ VOID sSURFEL_TABLE::add(SURFEL d_sfl, SHOB_ID id) {
    m_device_surfels[id] = d_sfl;
  }

  __host__ SURFEL sSURFEL_TABLE::map_H2D(const ::sMSFL*const h_sfl) {
    assert(m_device_surfels[h_sfl->id()]);
    return m_device_surfels[h_sfl->id()];
  }

  __host__ SURFEL* sSURFEL_TABLE::copy_surfel_array_to_dev() {
    sDEV_PTR dev_surfel_array(nullptr);
    cCOPY_TO_DEVICE::allocate(dev_surfel_array, "surfel ptrs",
                              m_device_surfels.size() * sizeof(GPU::sSURFEL*),
                              &m_device_surfels[0]);

    return (SURFEL*) dev_surfel_array.get();
  }

  __host__ VOID copy_msfl_from_D2H(::sMSFL* h_sfl) {

    auto d_sfl = GPU::g_surfel_table.map_H2D(h_sfl);
    auto& sfl_size_pair = g_msfl_sizes_table[h_sfl->id()];
    assert(sfl_size_pair.first->id() == h_sfl->id());
    size_t n_bytes = sfl_size_pair.second;
    checkCudaErrors(cudaMemcpy(h_sfl, d_sfl, n_bytes, cudaMemcpyDeviceToHost));
  }

  __host__ VOID sSURFEL_TABLE::allocate_surfels(const sMSFL_SIZES_TABLE& host_surfel_sizes_table,
                                                const sMEAS_WINDOW_COLLECTION& windows) {

    size_t n_sfls = host_surfel_sizes_table.size();

    set_capacity(n_sfls);

    for ( size_t i = 0; i < n_sfls; i++ ) {
      auto& sfl_size_pair = host_surfel_sizes_table[i];
      ::sMSFL* h_surfel = sfl_size_pair.first;
      size_t h_surfel_size  = sfl_size_pair.second;
      sDEV_PTR d_surfel(nullptr);
      cCOPY_TO_DEVICE::allocate(d_surfel, "surfel",
                                h_surfel_size, 
                                h_surfel, 
                                windows);
      add(static_cast<GPU::SURFEL>(d_surfel.get()), h_surfel->id());
    }

    //Some surfel data has cross references to other surfels. It can only be initialized
    //once all surfels are created on the device
    for (size_t i = 0; i < n_sfls; i++) {

      auto& sfl_size_pair = host_surfel_sizes_table[i];
      ::sMSFL* h_surfel = sfl_size_pair.first;
      sDEV_PTR d_ptr( map_H2D(h_surfel) );
      
      if (h_surfel->is_even_or_odd()) {
        auto d_sfl = (GPU::SURFEL) d_ptr.get();
        size_t offset = reinterpret_cast<char*>(h_surfel->even_odd_data()) - reinterpret_cast<char*>(h_surfel);
        sDEV_PTR d_ptr_to_even_odd_data(reinterpret_cast<char*>(d_sfl) + offset);
        cCOPY_TO_DEVICE::copy_to_device(IS_NOT_INTEGRAL_TYPE(),
                                        d_ptr_to_even_odd_data,
                                        sizeof(tSURFEL_EVEN_ODD_DATA<MSFL_SDFLOAT_TYPE_TAG>),
                                        h_surfel->even_odd_data());
      }     
    }
  }

  __host__ VOID sSURFEL_TABLE::allocate_surfel_interactions(const sMSFL_SIZES_TABLE& host_surfel_sizes_table)   {
    size_t n_sfls = host_surfel_sizes_table.size();
    for (size_t i = 0; i < n_sfls; i++) {

      auto& sfl_size_pair = host_surfel_sizes_table[i];
      ::sMSFL* h_surfel = sfl_size_pair.first;
      sDEV_PTR d_ptr( map_H2D(h_surfel) );      
      GPU::SURFEL d_sfl = static_cast<GPU::SURFEL>(d_ptr.get());
      if (h_surfel->m_interactions) {
        sDEV_PTR_TO_DEV_PTR ptr_to_interactions( get_class_member_ptr(d_sfl, m_interactions) );
        size_t n_bytes = h_surfel->m_interactions->size();
        cCOPY_TO_DEVICE::allocate(ptr_to_interactions, "surfel interaction",
                                  n_bytes,
                                  h_surfel->m_interactions);
      }
      
      if (h_surfel->is_s2s_destination()) {
        size_t offset = reinterpret_cast<char*>(h_surfel->s2s_advect_data()) - reinterpret_cast<char*>(h_surfel);
        sDEV_PTR ptr_to_s2s_advect_data = d_ptr.static_ptr_cast<char>() + offset;
        size_t n_bytes = sizeof(tS2S_ADVECT_DATA<MSFL_SDFLOAT_TYPE_TAG>);
        sMSFL_S2S_ADVECT_DATA::copy_to_device(ptr_to_s2s_advect_data,
                                            n_bytes,
                                            h_surfel->s2s_advect_data());    
      }      

    }  
  }

  __host__ VOID sSURFEL_TABLE::initialize_msfl_group_ranges() {
    
    for (int group_type = 0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {

      MSFL_FSET group_fset = g_msfl_groups[group_type];

      if (group_fset && group_fset->n_groups() > 0) {

        if (group_type != MLRF_SURFEL_GROUP_TYPE) {
          size_t sfl_offset = 0;
          for (STP_SCALE scale = 0; scale < sim.num_scales; scale++) {
            const auto& set_of_sfl_groups = group_fset->get_groups_of_scale(scale);
            for(MSFL_GROUP group : set_of_sfl_groups) {
              const int group_dest_sp = group->m_dest_sp.nsp();
              const auto [it,success] = m_group_properties.emplace(KEY{SURFEL_GROUP_TYPE(group_type), scale, group_dest_sp},
                                                                   RANGE_DESCRIPTOR{sfl_offset, group->n_shob_ptrs()});
              assert(success);
              sfl_offset += group->n_shob_ptrs();
            } //groups of scale loop
          } //scale loop
        } //non MLRF_GROUP
        else {

          size_t prev_sfl_offset = 0;

          for (int scale = 0; scale < sim.num_scales; scale++) {

            const auto& set_of_sfl_groups = g_mlrf_msfl_fset.get_groups_of_scale(scale);

            for (auto group : set_of_sfl_groups) {
              size_t count = 0;
              int last_id = -1;
              for (auto q : group->quantums()) {
                auto h_msfl = q.mlrf_surfel();
                if (!q.is_surfel_weightless() && !q.is_sentinel() && h_msfl->id() != last_id) {
                  count++;
                  last_id = h_msfl->id();
                }
              }
              const auto [it,success] = m_group_properties.emplace(
                                                                   KEY{MLRF_SURFEL_GROUP_TYPE, scale,
                                                                       0 /*dsp*/, group->m_even_odd,
                                                                       group->m_lrf_physics_desc},
                                                                   RANGE_DESCRIPTOR{prev_sfl_offset, count}
                                                                   );
              prev_sfl_offset += count;
            } //groups of scale loop
          } // scale loop
        }  // MLRF_GROUP
      } //group_fset->n_groups > 0
    } 

#ifdef DEBUG_MLRF_GROUP_RANGE    
    //sanity check
    for (int scale = 0; scale < n_scales; scale++) {
      for (int even_odd_mask = 0; even_odd_mask < STP_N_PHASES; even_odd_mask++){
        for (int i = 0; i < sim.n_lrf_physics_descs; i++) {
          size_t range[2];
          auto lrf = sim.ref_frame_index_to_lrf(i);
          set_mlrf_group_range_for_scale(range, scale, even_odd_mask, lrf);
          printf("Range for scale %d, even_odd_mask %d, (%lu, %lu), lrf %p\n",
                 scale, even_odd_mask, range[0], range[1], lrf);
          assert(range[1] >= range[0]);          
        }
      }
    }
#endif        
  }

  __host__ VOID sSURFEL_TABLE::fill_dev_surfel_vector_from_host_fset(std::vector<GPU::SURFEL>& device_sfls_in_group,
                                                                     MSFL_FSET group_fset,
                                                                     SURFEL_GROUP_TYPE group_type,
                                                                     int n_scales) {
    device_sfls_in_group.clear();
    size_t sfl_offset = 0;

    for (STP_SCALE scale = 0; scale < n_scales; scale++) {
      
      const auto& set_of_sfl_groups = group_fset->get_groups_of_scale(scale);
      std::set<int> dest_sp_set;

      for(MSFL_GROUP group : set_of_sfl_groups) {
      if(group->n_shob_ptrs()>0) {
        const int group_dest_sp = group->m_dest_sp.nsp();
        // cassert(!dest_sp_set.contains(group->dest_sp));
        {
          const auto [it,success] = dest_sp_set.insert(group_dest_sp);
          cassert(success);
        }
        
        for(::MSFL h_sfl = group->shob_ptrs();
            h_sfl != nullptr;
            h_sfl = h_sfl ? h_sfl->m_next : nullptr) {

          device_sfls_in_group.push_back(map_H2D(h_sfl));

        } //group loop        

        //Now this is done when we initialize the surfel ranges
        //const auto [it,success] = m_group_properties.emplace(sSURFEL_TABLE::KEY{group_type,scale,group_dest_sp}, sSURFEL_TABLE::RANGE_DESCRIPTOR{sfl_offset, group->n_shob_ptrs()});
        //cassert(success);
        sfl_offset += group->n_shob_ptrs();
      } 
      } //set of groups loop      
      const auto [it,success] = m_group_dest_sp.emplace(std::make_pair(group_type,scale),dest_sp_set);
      cassert(success);
    } //scale loop
  }


  __host__ VOID sSURFEL_TABLE::create_surfel_groups(MSFL_FSET (*h_sfl_groups) [N_SURFEL_GROUP_TYPES],
                                                    int n_scales) 
  {

    std::vector<GPU::SURFEL> device_sfls_in_group;

    for (int group_type = 0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {

      MSFL_FSET group_fset = (*h_sfl_groups)[group_type];
      GPU::sSURFEL_GROUP for_copy;

      if (group_fset && group_fset->n_groups() > 0) {
        
        fill_dev_surfel_vector_from_host_fset(device_sfls_in_group, group_fset,
                                              (SURFEL_GROUP_TYPE) group_type, n_scales);

        for_copy.m_count  = device_sfls_in_group.size();
        for_copy.m_surfels  = &device_sfls_in_group[0];

      } //group_fset->n_groups > 0
      else {

        for_copy.m_count = 0;
        for_copy.m_surfels = nullptr;

      }

      sDEV_PTR d_sfl_group(nullptr);

      cCOPY_TO_DEVICE::allocate(d_sfl_group, "surfel group",
                                sizeof(GPU::sSURFEL_GROUP),
                                &for_copy);
        
      LOG_MSG("GPU_INIT", LOG_FUNC).printf( "group type %d size of vector to to GPU %d", group_type, device_sfls_in_group.size());


      checkCudaErrors( cudaMemcpyToSymbol(GPU::g_surfel_groups,
                                          d_sfl_group.get(),
                                          sizeof(GPU::sSURFEL_GROUP),
                                          sizeof(GPU::sSURFEL_GROUP) * group_type,
                                          cudaMemcpyHostToDevice) );

    }
  }

  /*===================================================================================
   *
   *==================================================================================*/
  VOID sSURFEL_TABLE::prepare_mlrf_tagged_pointers_on_device() {

    auto msfl_group_fset = &::g_mlrf_msfl_fset;

    for (int scale = 0; scale < ::sim.num_scales; scale++) {

      const auto& set_of_msfl_groups = msfl_group_fset->get_groups_of_scale(scale);

      for(auto msfl_group : set_of_msfl_groups) {
        for (size_t i = 0; i < msfl_group->n_quantums(); i++) {
          auto& quantum =  msfl_group->quantums()[i];
          auto msfl = quantum.mlrf_surfel();
          auto replacement = TAGGED_MLRF_MSFL(map_H2D(msfl),
                                              quantum.is_surfel_weightless(),
                                              quantum.child_sfl());
          if (quantum.is_sentinel()) {
            replacement.mark_as_sentinel();
          }
          
          msfl_group->quantums()[i] = replacement;
        }

        GPU::Ptr<TAGGED_MLRF_MSFL> device_tagged_mlrf_msfls = \
          GPU::malloc<TAGGED_MLRF_MSFL>(msfl_group->n_quantums(), "Tagged MLRF megasurfels");

        GPU::copy_h2d(device_tagged_mlrf_msfls, msfl_group->quantums().data(), msfl_group->n_quantums());

        msfl_group->set_device_tagged_mlrf_surfels(device_tagged_mlrf_msfls);
      }
    }
  }

  /*===================================================================================
   *
   *==================================================================================*/
  void replace_s2v_ublk_pointers_on_device(const S2V_PER_GROUP_BATCH_INFO* h_s2v_info_per_scale,
                                           S2V_PER_GROUP_BATCH_INFO* d_s2v_info_per_scale) {

    unsigned num_unique_ublks = h_s2v_info_per_scale->get_n_elements<S2V_COMPONENTS::UNIQUE_UBLKS>();
    const sHMBLK* const * h_unique_s2v_ublks_of_scale = h_s2v_info_per_scale->get<S2V_COMPONENTS::UNIQUE_UBLKS>();
    std::vector<GPU::UBLK> d_unique_s2v_ublks_of_scale;
    d_unique_s2v_ublks_of_scale.resize(num_unique_ublks);

    for (unsigned i = 0; i < num_unique_ublks; i++) {
      d_unique_s2v_ublks_of_scale[i] = (::sMBLK*) g_ublk_table.map_H2D(h_unique_s2v_ublks_of_scale[i]);
    }

    size_t offset = h_s2v_info_per_scale->offset<S2V_COMPONENTS::UNIQUE_UBLKS>();

    void* d_address = (void*) ((char*) d_s2v_info_per_scale + offset);

    checkCudaErrors( cudaMemcpy(d_address, &d_unique_s2v_ublks_of_scale[0],
                                sizeof(sMBLK*) * num_unique_ublks, cudaMemcpyHostToDevice) );
  }

  GPU::Ptr<S2V_PER_GROUP_BATCH_INFO> init_s2v_device_data_from_host(const S2V_PER_GROUP_BATCH_INFO* h_s2v_info_per_group) {
    size_t bytes = h_s2v_info_per_group->size();
    S2V_PER_GROUP_BATCH_INFO* d_s2v_info_per_group;
    d_s2v_info_per_group = (S2V_PER_GROUP_BATCH_INFO*) GPU::malloc(bytes).get();
    checkCudaErrors( cudaMemcpy(d_s2v_info_per_group, &h_s2v_info_per_group[0],
                                bytes, cudaMemcpyHostToDevice) );

    replace_s2v_ublk_pointers_on_device(h_s2v_info_per_group, d_s2v_info_per_group);

    return GPU::Ptr<S2V_PER_GROUP_BATCH_INFO>(d_s2v_info_per_group);
  }



  __host__ VOID init_device_shobs(const sH2D_EXPORT& h_data) {
    
    //UBLK
    g_ublk_table.allocate_ublks(h_data);

    //UBLK ARRAY
    GPU::UBLK* ptr_to_d_ublk_array = g_ublk_table.copy_ublk_array_to_dev();

    checkCudaErrors( cudaMemcpyToSymbol(g_ublk_array, &ptr_to_d_ublk_array,
                                        sizeof(GPU::UBLK*), 0, cudaMemcpyHostToDevice) );

    // has to happen before updating the vr pointers
    g_ublk_table.build_vr_coarse_explode_groups();

    g_ublk_table.create_ublk_groups(h_data.m_ublk_groups,
                                    h_data.m_sim_info->num_scales);    

    //SURFEL
    const sMSFL_SIZES_TABLE& h_surfel_sizes_table = *h_data.m_msfl_sizes_table;

    g_surfel_table.allocate_surfels(h_surfel_sizes_table, *h_data.m_meas_windows);

    //SURFEL GROUPS
    g_surfel_table.create_surfel_groups(h_data.m_surfel_groups,
                                        h_data.m_sim_info->num_scales);

    //SURFEL ARRAY
    if (!g_surfel_table.is_empty()) {
      GPU::SURFEL* ptr_to_d_sfl_array = g_surfel_table.copy_surfel_array_to_dev();
      checkCudaErrors( cudaMemcpyToSymbol(g_surfel_array, &ptr_to_d_sfl_array,
                                          sizeof(GPU::SURFEL*), 0, cudaMemcpyHostToDevice) );

      MSFL_S2V_BATCH_DATA::get_instance()->init_msfl_s2v_device_data_from_host();
    }

    g_surfel_table.prepare_mlrf_tagged_pointers_on_device();
    
    g_ublk_table.init_mblks_on_device(*h_data.m_mblk_table, *h_data.m_meas_windows);

    g_surfel_table.allocate_surfel_interactions(h_surfel_sizes_table);
  }


  std::array<size_t,2> get_ublk_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) {
    return g_ublk_table.get_group_range(group_type, scale, dest_sp);
  }  
  VOID set_mlrf_msfl_group_range_for_scale(size_t *range, int scale, STP_EVEN_ODD even_odd_mask, LRF_PHYSICS_DESCRIPTOR lrf) {
    g_surfel_table.set_mlrf_group_range_for_scale(range, scale, even_odd_mask, lrf);
  }
  std::set<int> get_ublk_group_dest_sp(UBLK_GROUP_TYPE group_type, int scale) {
    return g_ublk_table.get_group_dest_sp(group_type,scale);
  }
  bool ublk_group_has_measurements(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) {
    return g_ublk_table.group_has_measurements(group_type, scale, dest_sp);
  }
  std::array<size_t,2> get_vr_coarse_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) {
    return g_ublk_table.get_vr_group_range(group_type, scale, dest_sp);
  }  
  std::array<size_t,2> get_surfel_group_range(SURFEL_GROUP_TYPE group_type, int scale, int dest_sp) {
    return g_surfel_table.get_group_range(group_type, scale, dest_sp);
  }  
  std::set<int> get_surfel_group_dest_sp(SURFEL_GROUP_TYPE group_type, int scale) {
    return g_surfel_table.get_group_dest_sp(group_type,scale);
  }
} //namespace GPU

/*===================================================================================
 * @fcn init_surfel_s2v_data()
 * Walk through host versions of s2v data prepared in gpu_surfel_interactions.cc and
 * create equivalent ones on the device
 *==================================================================================*/
VOID MSFL_S2V_BATCH_DATA::init_msfl_s2v_device_data_from_host() {
  for (auto key_s2v_data_pair : h_s2v_per_group_batch_info) {
    auto key = key_s2v_data_pair.first;
    auto h_s2v_data = key_s2v_data_pair.second;
    add_group_d_s2v_data(key,
                         GPU::init_s2v_device_data_from_host(h_s2v_data));
  }
}

template<>
void sMSFL_V2S_DATA_MEM_POOL::allocate_pool(size_t n_elems) {
  tSURFEL_V2S_DATA<MSFL_SDFLOAT_TYPE_TAG>::build_offset_table();
  m_n_elems = n_elems;
  m_buff = GPU::malloc<std::byte>(size_of_allocated_elems(n_elems), "surfel v2s buffer").get();
}

template<>
void sMSFL_V2S_DATA_MEM_POOL::clear(size_t n_elems) {
  GPU::zero_async(GPU::Ptr<std::byte>(this->m_buff), size_of_allocated_elems(n_elems), GPU::g_stream);
}
