/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2022, 1993-2021 Dassault Systemes Simulia Corp.         ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Simulia Corp.                                                         ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*/

#include "wsurfel_comm.h"

sWSURFEL_SEND_FSET g_wsurfel_send_fset;
sCONTACT_SEND_FSET g_contact_send_fset;

sWSURFEL_RECV_FSET g_wsurfel_recv_fset;
sCONTACT_RECV_FSET g_contact_recv_fset;

template <typename CI_SEND_FSET, typename CI_SEND_GROUP, SEND_TYPE CI_SEND_TYPE>
CI_SEND_GROUP* create_ci_send_group (CI_SEND_FSET fset, asINT32 scale, cNEIGHBOR_SP dest_sp) 
{
#if ENABLE_CONSISTENCY_CHECKS
  if (CI_SEND_TYPE != WSURFEL_SEND_TYPE && CI_SEND_TYPE != CONTACT_SEND_TYPE) {
    msg_internal_error("Send type %d not valid for sCONDUCTION_INTERFACE_SEND_GROUP", CI_SEND_TYPE);
  }
#endif
  static CI_SEND_GROUP signature;
  signature.m_scale = scale;
  signature.m_dest_sp = dest_sp;

  CI_SEND_GROUP* group = fset->find_group(&signature);
  if (NULL == group) {
    group = xnew CI_SEND_GROUP;
    group->m_scale = scale;
    group->m_solver_timestep_index_mask = sim.init_solver_mask;
    group->m_dest_sp = dest_sp;
    group->m_send_msg.init(my_proc_id, group->dest_rank());
    group->m_send_type = CI_SEND_TYPE;
    fset->add_group(group);
  }
  return group;
}

template <typename CI_RECV_FSET, typename CI_RECV_GROUP, RECV_TYPE CI_RECV_TYPE>
CI_RECV_GROUP* create_ci_recv_group (CI_RECV_FSET fset, asINT32 scale, cNEIGHBOR_SP source_sp) 
{
#if ENABLE_CONSISTENCY_CHECKS
  if (CI_RECV_TYPE != WSURFEL_RECV_TYPE && CI_RECV_TYPE != CONTACT_RECV_TYPE) {
    msg_internal_error("Recv type %d not valid for sCONDUCTION_INTERFACE_RECV_GROUP", CI_RECV_TYPE);
  }
#endif
  static CI_RECV_GROUP signature;
  signature.m_scale = scale;
  signature.m_source_sp = source_sp;
  CI_RECV_GROUP* group = fset->find_group(&signature);
  if (NULL == group) {
    group = xnew CI_RECV_GROUP;
    group->m_scale = scale;
    group->m_source_sp = source_sp;
    group->m_recv_msg.init(group->source_rank(), my_proc_id);
    group->m_recv_type = CI_RECV_TYPE;
    fset->add_group(group);
  }
  return group;
}

WSURFEL_SEND_GROUP sWSURFEL_SEND_FSET::create_group (asINT32 scale, cNEIGHBOR_SP dest_sp) {
  return create_ci_send_group<WSURFEL_SEND_FSET, sWSURFEL_SEND_GROUP, WSURFEL_SEND_TYPE>(this, scale, dest_sp);
}
CONTACT_SEND_GROUP sCONTACT_SEND_FSET::create_group (asINT32 scale, cNEIGHBOR_SP dest_sp) {
  return create_ci_send_group<CONTACT_SEND_FSET, sCONTACT_SEND_GROUP, CONTACT_SEND_TYPE>(this, scale, dest_sp);
}

WSURFEL_RECV_GROUP sWSURFEL_RECV_FSET::create_group (asINT32 scale, cNEIGHBOR_SP source_sp) {
  return create_ci_recv_group<WSURFEL_RECV_FSET, sWSURFEL_RECV_GROUP, WSURFEL_RECV_TYPE>(this, scale, source_sp);
}
CONTACT_RECV_GROUP sCONTACT_RECV_FSET::create_group (asINT32 scale, cNEIGHBOR_SP source_sp) {
  return create_ci_recv_group<CONTACT_RECV_FSET, sCONTACT_RECV_GROUP, CONTACT_RECV_TYPE>(this, scale, source_sp);
}

VOID do_preliminary_wsurfel_comm() {
  DO_SCALES_COARSE_TO_FINE(scale) {
    //cross-realm (wsurfels)
    DO_WSURFEL_RECV_GROUPS_OF_SCALE(post_recv_group, scale) {
      post_recv_group->post_recv();
    }
    DO_WSURFEL_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->send(sim.init_solver_mask);
    }
    DO_WSURFEL_RECV_GROUPS_OF_SCALE(complete_recv_group, scale) {
      complete_recv_group->complete_recv();
      complete_recv_group->unpack(sim.init_solver_mask, 0);
    }
    //same-realm (contact)
    DO_CONTACT_RECV_GROUPS_OF_SCALE(post_recv_group, scale) {
      post_recv_group->post_recv();
    }
    DO_CONTACT_SEND_GROUPS_OF_SCALE(post_send_group, scale) {
      post_send_group->send(sim.init_solver_mask);
    }
    DO_CONTACT_RECV_GROUPS_OF_SCALE(complete_recv_group, scale) {
      complete_recv_group->complete_recv();
      complete_recv_group->unpack(sim.init_solver_mask, 0);
    }
  }
}
