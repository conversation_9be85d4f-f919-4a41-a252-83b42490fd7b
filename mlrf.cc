/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Sam Watson, Exa Corporation 
 * Created March 10, 2009
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "sim.h"
#include "surfel_dyn_sp.h"
#include "mlrf.h"
#include "sp_timers.h"
#include <algorithm>
#include "thread_run.h"
#include "surfel_table.h"
#include "comm_groups.h"
#include "vr.h"
#include "particle_mlrf.h"
#if BUILD_GPU
#include "gpu_globals.hcu"
#include "gpu_array_of_structs_io.h"
#endif
#include PHYSICS_H

// The mainline basis for the MLRF code, particularly the particle dynamics sections,  is registry 222226, simeng 1760

// **************** Sliding mesh inter-thread data dependencies ************
//
// This comment describes dependencies between the sim and comm threads for MLRF
// comm. It does not cover strand-to-strand dependencies, which are handled by the
// usual mechanism. Functional dependencies, such as the dependency of the completion of a
// receive on the posting of that receive, are not shown.
//
// Sim thread dependencies. These are implemented using the dcntr mechanism.
//  
// SSA
//    - writes pre-dyn send buffers, so depends on completion of previous pre-dyn sends.
//        These are complete by implication, because othe SP's must have received the pre-dyn
//        data in order to complete SSB and post the sends necessary for this SP to go on to SSC.
//
//  SSB 
//    - reads from pre-dyn depots, so depends on SID receives
//    - writes surfel data, so depends on copying of previous data to pre-dyn send buffers
//    - writes post-dyn send buffers (in dynamics), so depends on completion of previous post-dyn sends.
//        Also complete by implication; other SP's needed the post-dyn data to get to SSC,
//        and from there to SSA to send the pre-dyn data on which SSB depends.
//
//  SSC 
//    - reads from post-dyn depots, so depends on MCD receives
//    - no write dependency, because writing to post-dyn send buffers is done during dynamics in sim thread in SSB
//
// Comm thread dependencies. These are implemented by means of MLRF_COMM_REQUEST structs in the strand manager.
//
//  copy to pre-dyn send buffer,
//    - reads surfel data, so depends on SSA
//    - writes pre-dyn send buffers, so depends on completion of previous pre-dyn sends
//
//  post pre-dyn sends
//    - reads pre-dyn send buffers, so depends on copy to pre-dyn send buffer
//
//  post pre-dyn receives
//    - writes pre-dyn depots, so depends on SSB
//    
//  post post-dyn sends
//    - reads post-dyn send buffers, so depends on SSB
//
//  post post-dyn receives
//    - write post-dyn depots, so depends on SSC
//
//*******************************************************************************



asINT32 g_total_predyn_receives_posted = 0;
asINT32 g_total_postdyn_receives_posted = 0;
asINT32 g_total_predyn_receives_completed = 0;
asINT32 g_total_postdyn_receives_completed = 0;

asINT32 g_total_predyn_sends_posted = 0;
asINT32 g_total_postdyn_sends_posted = 0;
asINT32 g_total_predyn_sends_completed = 0;
asINT32 g_total_postdyn_sends_completed = 0;

VOID sMLRF_PRE_DYNAMICS_DEPOT::print() {
  ACTIVE_SOLVER_MASK active_solver_mask = sim.init_solver_mask;
  BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_turb_active = (active_solver_mask & KE_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
   
  printf("Pre-dynamics depot TS%ld\n", g_timescale.m_time);
  if (is_lb_active) {
    printf(" mass: %10.2f\n", mass);
    printf(" momentum: {%8.3f, %8.3f, %8.3f}\n", mom[0], mom[1], mom[2]);  
    printf(" lrf_s2s_factor: %5f\n", lrf_s2s_factor);
    printf(" lrf_v2s_dist: %5f\n", lrf_v2s_dist);
    printf(" lrf_v2s_scale_diff: %5f\n", lrf_v2s_scale_diff);
    printf(" visc: %5f\n",visc);
  }
  if (is_turb_active) {
    printf(" turb_ke: %5f\n", turb_ke);
    printf(" turb_df: %5f\n", turb_df);
    printf(" nu: %5f\n", nu);
    printf(" ustar_0: %5f\n", ustar_0);
  }
  if (is_temp_active) {
    printf(" temp: %5f\n",temp);
    printf(" heat_flux: %5f\n", heat_flux);
    if (sim.is_T_S_solver_type_lb()) {
      printf(" den_in: %5f\n", den_in);
      printf(" Nin_t_sum: %5f\n", Nin_t_sum);
    } else if (sim.T_solver_type == PDE_TEMPERATURE) {
      printf(" kappa: %5f\n", kappa);
    } else if (sim.T_solver_type == PDE_ENTROPY) {
      printf(" entropy: %5f\n", entropy);
    }
  }
  if (is_uds_active) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      printf(" uds[%d]: %5f\n",nth_uds, uds_value[nth_uds]);
      printf(" uds_flux[%d]: %5f\n", nth_uds, uds_flux[nth_uds]);
      if (sim.uds_solver_type == LB_UDS) {
	printf(" den_in: %5f\n", den_in);
	printf(" Nin_uds_sum[%d]: %5f\n", nth_uds, Nin_uds_sum[nth_uds]);
      } else { //PDE_UDS
	printf(" uds_diffusion[%d]: %5f\n", nth_uds, uds_diffusion_coef[nth_uds]);
      }
    }
  }
}
     
VOID sMLRF_POST_DYNAMICS_DEPOT::print() {
#if BUILD_D19_LATTICE
  printf("Post-dynamics depot TS%ld\n",g_timescale.m_time);
  printf("  delta_mass: %10f\n",delta_mass);
#endif
}

template<typename SFL_TYPE_TAG>
MLRF_RING  tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::add_ring() {
  MLRF_RING r;
  if( sim.is_particle_model ) 
    r = xnew sMLRF_PARCEL_RING();
  else 
    r = xnew sMLRF_RING();
  m_rings.push_back(r);
  return m_rings.back();
}

template<typename SFL_TYPE_TAG>
struct tMLRF_MPI_PROXY{

  template <typename T, int N>
  static VOID irecv(cExaMsg<T, N> &msg) {
    g_exa_sp_cp_comm.irecv(msg);
  }

  template <typename T, int N>
  static VOID isend(cExaMsg<T, N> &msg) {
    g_exa_sp_cp_comm.isend(msg);
  }

  template <typename T, int N>
  static VOID MPI_Test(cExaMsg<T, N> &msg, int *flag, MPI_Status *status) {
    ::MPI_Test(&msg.m_request, flag, status);
  }  
};

template<typename MSG>
inline bool is_exa_msg_to_self(const MSG& msg) {
  return msg.source() == msg.dest();
}

#if defined(_EXA_GPU_HPMPI) //HpMPI single gpu
template<>
struct tMLRF_MPI_PROXY<MSFL_SDFLOAT_TYPE_TAG> {

  template <typename T, int N>

  static VOID irecv(cExaMsg<T, N> &msg) {}

  template <typename T, int N>
  static VOID isend(cExaMsg<T, N> &msg) {}

  template <typename T, int N>
  static VOID MPI_Test(cExaMsg<T, N> &msg, int *flag, MPI_Status *status) {
    *flag = 1;
  }
};
#endif

#if defined(_EXA_GPU_OPENMPI)
constexpr static bool DEBUG_LRF_PROGRESSION = 0;
constexpr static bool USE_CUDE_MEMCPY_FOR_SELF_COMM = 1;

template<>
struct tMLRF_MPI_PROXY<MSFL_SDFLOAT_TYPE_TAG>{

  template <typename T, int N>
  static VOID irecv(cExaMsg<T, N> &msg) {
    if (!is_exa_msg_to_self(msg)) {
      g_exa_sp_cp_comm.irecv(msg);
    }
  }

  template <typename T, int N>
  static VOID isend(cExaMsg<T, N> &msg) {
    if (!is_exa_msg_to_self(msg)) {
      g_exa_sp_cp_comm.isend(msg);
    }
  }

  template <typename T, int N>
  static VOID MPI_Test(cExaMsg<T, N> &msg, int *flag, MPI_Status *status) {
    if (!is_exa_msg_to_self(msg)) {
      ::MPI_Test(&msg.m_request, flag, status);
    } else {
      *flag = 1;
    }
  }
};

template<typename DEPOT_TYPE>
class LRF_RING_MSG_PROGRESSION {
public:
  LRF_RING_MSG_PROGRESSION(cExaMsg<DEPOT_TYPE>* msgs):
    m_msgs(msgs),
    m_start(0),
    m_end(0)
  {
  }

  ~LRF_RING_MSG_PROGRESSION() {
    progress_existing_sends();
  }

  size_t num_pending_msgs() const {
    return (m_end  - m_start);
  }

  void maybe_block_till_pending_msgs_are_complete(size_t curr_counter) {
    m_end = curr_counter;
    progress_once();
    if (block()) {
      if constexpr(DEBUG_LRF_PROGRESSION) {
        msg_print("LRF_RING_MSG_PROGRESSION:: Blocking at TS %ld, num_msgs %lu",
                  g_timescale.m_time, num_pending_msgs());
      }
      progress_existing_sends();
    }
  }

private:

  size_t block() const {
    //If a ring messages
    return (2 * num_pending_msgs()) > sim_run_info.gpu_lrf_max_isends;
  }

  void progress_existing_sends() {
    //Since clock rate of modern processors O(GHz) and IPC tends to be in the same
    //order, a 1e6 number of attempts per pending msg might imply spending
    //O(milliseconds) per msg on average
    size_t max_attempts = num_pending_msgs() * 1e6;
    for(size_t attempts = 0; !progress_once(); attempts++) {
      if (attempts > max_attempts) {
        static bool issue_warning_once = true;
#if !DEBUG
        if (issue_warning_once) {
          msg_warn("LRF_RING_MSG_PROGRESSION:: Could not progress LRF comm at TS %ld",
                   g_timescale.m_time);
          issue_warning_once = false;
        }
#endif
        break;
      }
    }
  }

  bool progress_once() {
    if (m_start < m_end) {
      int flag;
      MPI_Status status;
      if (USE_CUDE_MEMCPY_FOR_SELF_COMM &&
          is_exa_msg_to_self(m_msgs[m_start]))
      {
        flag = 1;
      }
      else {
        ::MPI_Test(&m_msgs[m_start].m_request, &flag, &status);
      }
      m_start += (flag != 0);
    }
    return m_start == m_end;
  }

private:
  cExaMsg<DEPOT_TYPE>* m_msgs;
  size_t m_start;
  size_t m_end;
};

template<typename DEPOT_TYPE>
class LRF_SEND_MSG_PROGRESSION {

public:
  using ELEMENT_TYPE = LRF_RING_MSG_PROGRESSION<DEPOT_TYPE>;

  size_t num_pending_msgs() const {
    size_t total_msgs = 0;
    for (const auto& e : m_deque) {
      total_msgs += e.num_pending_msgs();
    }
    return total_msgs;
  }

  void add_entry(cExaMsg<DEPOT_TYPE>* msgs) {
    maybe_flush();
    m_deque.emplace_back(msgs);
  }

  auto& back() { return m_deque.back(); }

private:
  void maybe_flush() {
    if (!m_deque.empty()) {
      if (num_pending_msgs() > sim_run_info.gpu_lrf_max_isends) {
        if constexpr(DEBUG_LRF_PROGRESSION) {
          msg_print("LRF_SEND_MSG_PROGRESSION:: blocking at TS %ld, num_elems %lu", g_timescale.m_time, num_pending_msgs());
        }
        while (!m_deque.empty()) {
          m_deque.pop_front();
        }
      } else {
        //If we've already flushed pending messages from previous entries,
        //get rid of them. Just because the last entry in the list might have
        //communication completed does not mean other entries would have because
        //they might correspond to a different {SP, comm tag} pair
        while (!m_deque.empty() && m_deque.back().num_pending_msgs() == 0) {
          m_deque.pop_back();
        }
      }
    }
  }
private:
  std::deque<ELEMENT_TYPE> m_deque;
};

#else

template<typename DEPOT_TYPE>
class LRF_SEND_MSG_PROGRESSION {
public:
  void maybe_block_till_pending_msgs_are_complete(size_t curr_counter)
  {
  }

  void add_entry(cExaMsg<DEPOT_TYPE>* msgs)
  {
  }

  auto& back() { return *this; }
};
#endif


#if DEBUG
VMEM_VECTOR <SURFEL> g_mlrf_surfel_vector;
#endif

sMLRF_SURFEL_FSET g_mlrf_surfel_fset;
#if BUILD_GPU
sMLRF_MSFL_FSET g_mlrf_msfl_fset;
#endif

// This used to complete MLRF send requests. We always expect these to be 
// complete by implication before we need to overwrite the buffer, so we
// do not test at that point, only when we want to reuse the request.
template<typename SFL_TYPE_TAG, typename MSG>
inline VOID mlrf_validate_completion(MSG& msg, BOOLEAN is_pre_dyn) {
  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;
  int flag = 0;
  MPI_Status status;
  MPI_PROXY::MPI_Test(msg,&flag,&status);
  if(!flag) {
    msg_internal_error("Unexpected uncompleted MPI request");
  }
  if(is_pre_dyn) {
    g_total_predyn_sends_completed++;
  } else {
    g_total_postdyn_sends_completed++;
  }
}

VOID sMLRF_RING::max_messages(asINT32& max_send_messages, asINT32& max_recv_messages)
{

  // Computes the one-way message count for every rotation of the ring and returns the maximum

  max_send_messages = 0;
  max_recv_messages = 0;
  if (n_ring_segments_on_proc() > 0) {

    ccDOTIMES(ring_offset, m_n_surfels_per_frame_in_ring ) {

      dFLOAT surfel_angle = ring_offset;
      asINT32 n_send_messages = 0;

      {
        MLRF_DO_SEND_MESSAGES(message_length, send_buffer_offset, remote_proc, this, surfel_angle) {
          n_send_messages++;
        }
        max_send_messages = (max_send_messages < n_send_messages) ? n_send_messages : max_send_messages;
      }
      asINT32 n_recv_messages = 0;
      {
        MLRF_DO_RECV_MESSAGES(message_length, send_buffer_offset, remote_proc, this, surfel_angle) {
          n_recv_messages++;
        }
      }
      max_recv_messages = (max_recv_messages < n_recv_messages) ? n_recv_messages : max_recv_messages;
    }
  }
}


template<typename SFL_TYPE_TAG>
__DEVICE__
VOID tSURFEL<SFL_TYPE_TAG>::copy_active_data_for_mlrf_surfels(MLRF_PRE_DYNAMICS_DEPOT pre_dynamics_depot,
                                                              ACTIVE_SOLVER_MASK active_solver_mask,
                                                              asINT32 soxor)
{
  BOOLEAN is_lb_active = (active_solver_mask & LB_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_temp_active = (active_solver_mask & T_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_turb_active = (active_solver_mask & KE_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_uds_active = (active_solver_mask & UDS_PDE_ACTIVE) ? TRUE: FALSE;
  BOOLEAN is_conduction_active = (active_solver_mask & CONDUCTION_PDE_ACTIVE) ? TRUE: FALSE;
  STP_SURFEL_WEIGHT accum_scale = this->mme_weight[soxor];
  STP_SURFEL_WEIGHT accum_scale1 = this->s2s_sampling_weight[soxor];
  auto& simc = get_simc_ref();

  if (!this->is_sliding_rf()) {
    HOST_MSG_INTERNAL_ERROR_OR_DEVICE_ASSERT("Surfel %d is not part of sliding reference frame\n", this->id());
  }

  if (this->is_conduction_surface()){
    pre_dynamics_depot->is_conduction_surface = TRUE;  //for sanity check
    if (is_conduction_active) {
#ifdef CONDUCTION_NORMALIZE_SAMPLES_IN_SDYN
      sdFLOAT scale = this->conduction_data()->sampling_weight_without_interface_s2s_inv[soxor];
      pre_dynamics_depot->temp = scale * this->conduction_data()->temp_sample[soxor];
      pre_dynamics_depot->conductivity_sample = scale * this->conduction_data()->conductivity_nn_sample[soxor];
#else
      pre_dynamics_depot->temp = this->conduction_data()->temp_sample;
      pre_dynamics_depot->conductivity_sample = this->conduction_data()->conductivity_nn_sample;
#endif
      pre_dynamics_depot->dist = this->y_sample[soxor];
#if CONDUCTION_ENABLE_PTHRU_ACROSS_LRF
    pre_dynamics_depot->heat_flux = this->conduction_data()->q_passthrough[soxor];
#endif
    }
    return;  

  } else {
    pre_dynamics_depot->is_conduction_surface = FALSE;
  }
  
    
  if(is_lb_active) {
    auto& simc = get_simc_ref();
    if (accum_scale1 < SFLOAT_EPSILON)
      pre_dynamics_depot->mass = simc.char_density;
    else
      pre_dynamics_depot->mass = accum_scale1 * this->v2s_lb_data()->m_density[soxor];

    ccDOTIMES(axis, 3) {
      pre_dynamics_depot->mom[axis] = accum_scale1 * this->v2s_lb_data()->m_momentum[axis][soxor];
    }
    pre_dynamics_depot->lrf_s2s_factor = this->lrf_data()->lrf_s2s_factor[soxor];
    pre_dynamics_depot->lrf_v2s_dist = this->lrf_data()->lrf_v2s_dist[soxor];
    pre_dynamics_depot->lrf_v2s_scale_diff = this->lrf_data()->lrf_v2s_scale_diff[soxor];
    pre_dynamics_depot->visc = accum_scale * this->v2s_lb_data()->m_visc[soxor];
  }
  if( is_turb_active) {
    pre_dynamics_depot->turb_ke = accum_scale * this->v2s_turb_data()->m_tke_pde[soxor];
    pre_dynamics_depot->turb_df = accum_scale * this->v2s_turb_data()->m_eps_pde[soxor];
    pre_dynamics_depot->nu = accum_scale * this->v2s_turb_data()->m_nu[soxor];
    pre_dynamics_depot->ustar_0 = this->lb_data()->ustar_0[soxor];
  }
  if (is_temp_active) {
    pre_dynamics_depot->temp = accum_scale1 * this->t_data()->temp_sample[soxor];
    if (simc.T_solver_type == PDE_ENTROPY) {
      pre_dynamics_depot->entropy = accum_scale1 * this->t_data()->entropy_sample[soxor];
#if BUILD_D39_LATTICE
      ccDOTIMES(axis, N_AXES) {
        pre_dynamics_depot->hyb_force[axis] = accum_scale1 * this->t_data()->hyb_force[axis][soxor];
      }
#endif
    }
    pre_dynamics_depot->heat_flux = this->v2s_t_data()->m_heat_flux_cross_lrf[soxor];

    if (simc.is_T_S_solver_type_lb()) {
#if BUILD_6X_SOLVER
      if (simc.T_solver_type ==LB_ENTROPY) {
        pre_dynamics_depot->entropy = accum_scale1 * this->t_data()->entropy_sample[soxor];
        pre_dynamics_depot->kappa = accum_scale * this->v2s_t_data()->m_kappa[soxor];  
      } else
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
        if (simc.T_solver_type ==LB_ENERGY) {
          pre_dynamics_depot->entropy = accum_scale1 * this->t_data()->entropy_sample[soxor];
        }
#endif
      SURFEL_STATE in_states_t[N_SURFEL_PGRAM_VOLUMES];
      SURFEL_STATE in_states[N_SURFEL_PGRAM_VOLUMES];  //Momentum and T-solver should be sync'ed
      dFLOAT den_in = 0.0;
      dFLOAT Nin_t_sum = 0.0;
      STP_LATVEC_MASK  inward_dir_mask = this->incoming_latvec_mask[soxor];
      if (g_in_state_scale_up_on) {
        ccDOTIMES(ip, N_SURFEL_PGRAM_VOLUMES) {
          //if (stp_is_state_inward(inward_dir_mask, ip)) {
          //asINT32 ip_pair = state_latvec_pair(ip);
          STP_PHYS_VARIABLE one_over_scale = 1.0;
          if (this->v2s_lb_data()->m_in_state_scale_factors_lb[ip][soxor] != 0) {
            one_over_scale = 1.0 / this->v2s_lb_data()->m_in_state_scale_factors_lb[ip][soxor];
          }
          
          in_states[ip] = this->v2s_lb_data()->m_in_states[ip][soxor] * one_over_scale;
          in_states_t[ip] = this->v2s_t_data()->m_in_states_t[ip][soxor] * one_over_scale;

#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
          if (simc.T_solver_type == LB_ENTROPY || simc.T_solver_type == LB_ENERGY)
            Nin_t_sum += in_states_t[ip] * this->lb_data()->pgram_volumes[ip][soxor];
          else
#endif
            {
              STP_PHYS_VARIABLE den_flux = in_states[ip] * this->lb_data()->pgram_volumes[ip][soxor];
              den_in += den_flux;
              Nin_t_sum += in_states_t[ip] * den_flux;
            }
        }
      } else {
        ccDOTIMES(ip, N_SURFEL_PGRAM_VOLUMES) {
          in_states_t[ip] = this->v2s_t_data()->m_in_states_t[ip][soxor];
          in_states[ip] = this->v2s_lb_data()->m_in_states[ip][soxor];
#ifdef DEBUG_SS
          if (id() == 765048 && ip == 1) {
            msg_print("TS %ld ip %d in_states_t %e", g_timescale.m_time, ip, in_states_t[ip]);
          }
#endif
#if BUILD_6X_SOLVER || BUILD_D39_LATTICE
          if (simc.T_solver_type == LB_ENTROPY || simc.T_solver_type == LB_ENERGY)
            Nin_t_sum += in_states_t[ip] * this->lb_data()->pgram_volumes[ip][soxor];
          else 
#endif
            {
              STP_PHYS_VARIABLE den_flux = in_states[ip] * this->lb_data()->pgram_volumes[ip][soxor];
              den_in += den_flux;
              Nin_t_sum += in_states_t[ip] * den_flux;
            }
        }
      }

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      LOG_MSG_IF(this->id() != -25017, "CONDUCTION_SOLVER", LOG_BASE_TS).printf(
          "%d depot, Nin_t_sum: %.17g", this->id(), Nin_t_sum);
#endif
      pre_dynamics_depot->den_in = den_in;
      pre_dynamics_depot->Nin_t_sum = Nin_t_sum;
#ifdef DEBUG_SS
      if(id() == 765048)
        msg_print("TS %ld surfel %d Nin_t_sum %e", g_timescale.m_time, id(), Nin_t_sum);
#endif
    } else { //T_pde solver or S_pde
      pre_dynamics_depot->kappa = accum_scale * this->v2s_t_data()->m_kappa[soxor]; //pde solver
    }
  }
    if (is_uds_active) {
#if !GPU_COMPILER    
    DO_ACTIVE_SURFEL_AND_V2S_UDS_DATA(nth_uds,sim.n_user_defined_scalars,this,surfel_uds_data, v2s_uds_data) {
      pre_dynamics_depot->uds_value[nth_uds] = accum_scale1 *surfel_uds_data->uds_value;
      pre_dynamics_depot->uds_flux[nth_uds] = v2s_uds_data->m_uds_flux_cross_lrf;
      
      if (sim.uds_solver_type == LB_UDS) {
	SURFEL_STATE in_states_uds[N_SURFEL_PGRAM_VOLUMES];
	SURFEL_STATE in_states[N_SURFEL_PGRAM_VOLUMES]; 
	dFLOAT den_in = 0.0;
	dFLOAT Nin_uds_sum = 0.0;
	STP_LATVEC_MASK  inward_dir_mask = this->incoming_latvec_mask;
	if (g_in_state_scale_up_on) {
	  ccDOTIMES(ip, N_SURFEL_PGRAM_VOLUMES) {
	    STP_PHYS_VARIABLE one_over_scale = 1.0;
	    if (this->v2s_lb_data()->m_in_state_scale_factors_lb[ip] != 0) {
	      one_over_scale = 1.0 / this->v2s_lb_data()->m_in_state_scale_factors_lb[ip];
	    }
	    in_states[ip] = this->v2s_lb_data()->m_in_states[ip] * one_over_scale;
	    in_states_uds[ip] = v2s_uds_data->m_in_states_uds[ip] * one_over_scale;
	    
	    STP_PHYS_VARIABLE den_flux = in_states[ip] * this->lb_data()->pgram_volumes[ip];
	    den_in += den_flux;
	    Nin_uds_sum += in_states_uds[ip] * den_flux;
	    
	  }
	} else {
	  ccDOTIMES(ip, N_SURFEL_PGRAM_VOLUMES) {
	    in_states_uds[ip] = v2s_uds_data->m_in_states_uds[ip];
	    in_states[ip] = this->v2s_lb_data()->m_in_states[ip];

	    STP_PHYS_VARIABLE den_flux = in_states[ip] * this->lb_data()->pgram_volumes[ip];
	    den_in += den_flux;
	    Nin_uds_sum += in_states_uds[ip] * den_flux;
	    
#ifdef DEBUG_LB_UDS
	    if (this->id()==241) msg_print("T %ld ip %d in_states_uds %g, Nin_uds_sum %g",g_timescale.m_time, ip, in_states_uds[ip], Nin_uds_sum);
#endif
          }
	}

	pre_dynamics_depot->den_in = den_in;
	pre_dynamics_depot->Nin_uds_sum[nth_uds] = Nin_uds_sum;
      
#ifdef DEBUG_LB_UDS
	msg_print("T %ld S %d depot_Nin_uds_sum %g",g_timescale.m_time, this->id(), Nin_uds_sum);
#endif      
      } else { //UDS_PDE solver
        pre_dynamics_depot->uds_diffusion_coef[nth_uds] = accum_scale *v2s_uds_data->m_diffusion_coef;
      }
    }
#if BUILD_6X_SOLVER
    if(sim.is_pf_model){
      sdFLOAT pressure = accum_scale1 *this->v2s_pf_data()->m_pressure_pfld;
      sdFLOAT pressure_ur = this->pf_data()->pressure_pfld_ur;
      if(pressure_ur != INVALID_PRESSURE_UR){
        pressure = (1.0f - g_pf_ur_omega_p) * pressure_ur + g_pf_ur_omega_p * pressure;
      }
      pre_dynamics_depot->pf_pressure = pressure; 
      ccDOTIMES(axis, 3) {
        pre_dynamics_depot->pf_vel[axis] = accum_scale1 * this->v2s_pf_data()->m_vel_pfld[axis];
        pre_dynamics_depot->pf_grad_chem[axis] = accum_scale1 * this->v2s_pf_data()->m_grad_chem[axis];
        pre_dynamics_depot->pf_grad_order[axis] = accum_scale1 * this->v2s_pf_data()->m_grad_orderp[axis];
      }
      pre_dynamics_depot->pf_grad_order[3] = accum_scale1 * this->v2s_pf_data()->m_grad_orderp[3];
    }
#endif
#else
      assert(false);
#endif        
  }//is_uds_active
}



#if !BUILD_GPU
// Pre-dynamics phase
INLINE VOID copy_pre_dynamics(ACTIVE_SOLVER_MASK active_solver_mask,
                              MLRF_SURFEL_GROUP group)
{
  TAGGED_MLRF_SURFEL *first_surfel_on_ring = &group->quantums()[0];
  ccDOTIMES(nth_ring, group->n_rings()) {
    MLRF_RING ring = group->ring(nth_ring);
    asINT32 n_surfels_per_frame_on_proc = ring->m_n_surfels_per_frame_on_proc;
    TAGGED_MLRF_SURFEL *ring_surfels[MLRF_N_ORIENTATIONS];
    ring_surfels[MLRF_EXTERNAL] = first_surfel_on_ring;
    ring_surfels[MLRF_INTERNAL] = first_surfel_on_ring + n_surfels_per_frame_on_proc;

    ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc + 1) {
        asINT32 surfel_index = (nth_surfel == n_surfels_per_frame_on_proc) ? 0 : nth_surfel;
        MLRF_PRE_DYNAMICS_DEPOT send_buffer = &ring->m_pre_dynamics_send_buffer[orientation][surfel_index];
        TAGGED_MLRF_SURFEL tagged_surfel = ring_surfels[orientation][surfel_index];
        // Do not skip the copying of data even for weightless surfels. For a ring, a surfel might be present multiple times
        // with some of them being weightless. Weightless surfel will interact with weighted counterpart of that surfel.
        tagged_surfel.mlrf_surfel()->copy_active_data_for_mlrf_surfels(send_buffer, active_solver_mask);
      }
    }
    first_surfel_on_ring += (2 * n_surfels_per_frame_on_proc); // Next ring
  }
}
#else

INLINE VOID copy_pre_dynamics(ACTIVE_SOLVER_MASK active_solver_mask,
                              MLRF_SURFEL_GROUP group)
{
  msg_internal_error("This version of copy_pre_dynamics should not be called for GPU simulations");
}

__GLOBAL__ VOID copy_pre_dynamics_kernel(ACTIVE_SOLVER_MASK active_solver_mask,
                                         MLRF_PRE_DYNAMICS_DEPOT sbuff,
                                         STP_EVEN_ODD group_even_odd_mask,
                                         TAGGED_MLRF_MSFL* tagged_mlrf_msfls,
                                         size_t n_tagged_msfls) {

  size_t surfel_index = (blockIdx.x * N_SFLS_PER_MSFL) + threadIdx.x;
  
  if (surfel_index < n_tagged_msfls) {
    auto tagged_mlrf_surfel = tagged_mlrf_msfls[surfel_index];
    auto mlrf_surfel = tagged_mlrf_surfel.mlrf_surfel();
    asINT32 soxor = tagged_mlrf_surfel.child_sfl();
    cassert(mlrf_surfel->is_valid_child_surfel(soxor));
    if (!tagged_mlrf_surfel.is_sentinel()) {
      //Because tagged sfls are sequenced in the same order as the depot memory, and
      //the depots are allocated contiguously for an entire group, across rings, the
      //tagged surfel_index is the natural index to the correct depot
      MLRF_PRE_DYNAMICS_DEPOT depot = sbuff + surfel_index;
      mlrf_surfel->copy_active_data_for_mlrf_surfels(depot, active_solver_mask, soxor);

    }
  }
}

INLINE VOID copy_pre_dynamics(ACTIVE_SOLVER_MASK active_solver_mask,
                              sMLRF_MSFL_GROUP* group)
{

  size_t num_thread_blocks = std::ceil(group->n_quantums() / float(N_SFLS_PER_MSFL));

  //No explicit sync needed here since we have already synced in mlrf_reset_and_v2s
  //before handing over control to the comm-thread which runs this function

  copy_pre_dynamics_kernel<<<num_thread_blocks, N_SFLS_PER_MSFL, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(active_solver_mask,
                                                                                                     group->get_start_of_gpu_pre_dyn_sbuff().get(),
                                                                                                     group->m_even_odd,
                                                                                                     group->get_device_tagged_mlrf_surfels().get(),
                                                                                                     group->n_quantums());

  //Sync needed before we hand over control back to compute thread to run
  //mlrf_dynamics
  checkCudaErrors(cudaPeekAtLastError());
  checkCudaErrors(cudaStreamSynchronize(GPU::g_stream));
}

#endif //BUILD_GPU

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::copy_to_pre_dynamics_send_buffers(ACTIVE_SOLVER_MASK active_solver_mask)
{
  copy_pre_dynamics(active_solver_mask, this);
}

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::post_pre_dynamics_sends(BOOLEAN is_seeding)
{

  LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
  // If this is the initial send during seeding, should use lrf->angle_rotated since pre_dyn_send_angle_rotated is not initialized yet.
  dFLOAT angle_rotated = is_seeding? lrf->angle_rotated : pre_dyn_send_angle_rotated;

  LOG_MSG("RECV_DEPEND").printf("TS %ld angle rotated %f in pre dynamics sends", g_timescale.m_time, angle_rotated);

  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;  

  LRF_SEND_MSG_PROGRESSION<sMLRF_PRE_DYNAMICS_DEPOT> send_progression;
  
  ccDOTIMES(nth_ring, n_rings()) {

    MLRF_RING current_ring = ring(nth_ring);
    if(current_ring->n_ring_segments_on_proc() > 0) {

      dFLOAT angle_in_surfels = angle_rotated * current_ring->m_one_over_angle_per_surfel; // angle_rotated in decimal-surfel units

      asINT32 offsets[MLRF_N_ORIENTATIONS];
      offsets[MLRF_INTERNAL] = angle_in_surfels;
      offsets[MLRF_EXTERNAL] = ceil(current_ring->m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);

      int send_tags[MLRF_N_ORIENTATIONS];
      send_tags[MLRF_INTERNAL] = eMPI_MLRF_PRE_DYN_ITOE_TAG;
      send_tags[MLRF_EXTERNAL] = eMPI_MLRF_PRE_DYN_ETOI_TAG;
      
      // Do exterior first so sends and receives are in same order; not necessary, but probably beneficial
      ccDOTIMES(inverse_orientation, MLRF_N_ORIENTATIONS) {
        int orientation = (inverse_orientation == MLRF_INTERNAL) ? MLRF_EXTERNAL : MLRF_INTERNAL;
        asINT32 n_pre_dynamics_messages = 0;
        send_progression.add_entry(current_ring->m_pre_dynamics_send_msg[orientation]);
        // note that the internal offset is used for ETOI and vice-versa
        MLRF_DO_SEND_MESSAGES(message_length, send_buffer_offset, remote_proc, current_ring, offsets[inverse_orientation]) {
          asINT32 &n_message = n_pre_dynamics_messages;
          current_ring->m_pre_dynamics_send_msg[orientation][n_message].setBuffer(&current_ring->m_pre_dynamics_send_buffer[orientation][send_buffer_offset]);
          mlrf_validate_completion<SFL_TYPE_TAG>(current_ring->m_pre_dynamics_send_msg[orientation][n_message], TRUE);
          g_total_predyn_sends_posted++;
          current_ring->m_pre_dynamics_send_msg[orientation][n_message].init(my_proc_id, remote_proc);
          current_ring->m_pre_dynamics_send_msg[orientation][n_message].set_nelems(message_length);
          current_ring->m_pre_dynamics_send_msg[orientation][n_message].settag(send_tags[orientation]);
          MPI_PROXY::isend(current_ring->m_pre_dynamics_send_msg[orientation][n_message++]);
          send_progression.back().maybe_block_till_pending_msgs_are_complete(n_message);
        }
      }
    }
  }

#if BUILD_GPU
  //This should go away once we have CUDA aware MPI
  this->m_sync_single_gpu_comm.mark_sends_as_posted(MLRF_PREDYN);
#endif  
}

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::post_pre_dynamics_receives()
{
  int recv_tags[MLRF_N_ORIENTATIONS];
  recv_tags[MLRF_INTERNAL] = eMPI_MLRF_PRE_DYN_ETOI_TAG;
  recv_tags[MLRF_EXTERNAL] = eMPI_MLRF_PRE_DYN_ITOE_TAG;

  LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
  dFLOAT angle_rotated = lrf->angle_rotated;

  m_all_rings_complete[MLRF_PREDYN] = FALSE;
  m_n_rings_complete[MLRF_PREDYN] = 0;

  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;
  
  ccDOTIMES(nth_ring, n_rings()) {
 
    MLRF_RING current_ring = ring(nth_ring);
    ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
      current_ring->m_n_messages_received[MLRF_PREDYN][i] = 0;
    }
    current_ring->m_all_messages_received[MLRF_PREDYN] = FALSE;
    if(current_ring->n_ring_segments_on_proc() > 0) {

      dFLOAT angle_in_surfels = angle_rotated * current_ring->m_one_over_angle_per_surfel; // angle_rotated in decimal-surfel units

      asINT32 offsets[MLRF_N_ORIENTATIONS];
      offsets[MLRF_INTERNAL] = angle_in_surfels;
      offsets[MLRF_EXTERNAL] = ceil(current_ring->m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);

      asINT32 n_depots_per_frame =  current_ring->m_n_surfels_per_frame_on_proc + current_ring->n_ring_segments_on_proc();

      ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
        current_ring->m_n_messages[MLRF_PREDYN][orientation] = 0;
        MLRF_DO_RECV_MESSAGES(message_length, depot_offset, remote_proc, current_ring, offsets[orientation]) {

          g_total_predyn_receives_posted++;
          asINT32 &n_message = current_ring->m_n_messages[MLRF_PREDYN][orientation];
          current_ring->m_pre_dynamics_recv_msg[orientation][n_message].setBuffer(&current_ring->m_pre_dynamics_recv_buffer[orientation][depot_offset]);
          current_ring->m_pre_dynamics_recv_msg[orientation][n_message].init(remote_proc, my_proc_id);
          current_ring->m_pre_dynamics_recv_msg[orientation][n_message].set_nelems(message_length);
          current_ring->m_pre_dynamics_recv_msg[orientation][n_message].settag(recv_tags[orientation]);
          MPI_PROXY::irecv(current_ring->m_pre_dynamics_recv_msg[orientation][n_message++]);
        }
      }
    }
  }
}

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::cancel_receives()
{
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_RING current_ring = ring(nth_ring);
    ccDOTIMES(nth_recv_request, current_ring->m_n_receive_requests ) {
      ccDOTIMES(orientation,MLRF_N_ORIENTATIONS) {
        if(current_ring->m_pre_dynamics_recv_msg[orientation][nth_recv_request].m_request != MPI_REQUEST_NULL) {
          MPI_Cancel(&current_ring->m_pre_dynamics_recv_msg[orientation][nth_recv_request].m_request);
        }
        if(current_ring->m_post_dynamics_recv_msg[orientation][nth_recv_request].m_request != MPI_REQUEST_NULL) {
          MPI_Cancel(&current_ring->m_post_dynamics_recv_msg[orientation][nth_recv_request].m_request);
        }
      }
    }

    if (sim.is_particle_model) {
      MLRF_PARCEL_RING parcel_ring = static_cast<MLRF_PARCEL_RING>(current_ring);
      parcel_ring->cancel_receives();
    }

  }
}

#if BUILD_GPU
template<typename DEPOT_TYPE>
size_t complete_single_gpu_msg(cExaMsg<DEPOT_TYPE>& recv,
                               cExaMsg<DEPOT_TYPE>* send_msgs,
                               size_t n_send_msgs,
                               size_t send_msgs_start_index) {
  DEPOT_TYPE* send_buff;
  //Rather than do an std::find_if, for total_sps == 1, this branch
  //is an optimization based on comm structure of sliding mesh
  if (total_sps == 1)  {
    auto& send_msg = send_msgs[send_msgs_start_index];
    //assert(recv.source() == msg.dest());
    assert(recv.tag() == send_msg.tag());
    assert(recv.n_elems() == send_msg.n_elems());
    send_msgs_start_index += 1;
    send_buff = send_msg.buffer();
  } else {
    auto it = std::find_if(send_msgs + send_msgs_start_index,
                           send_msgs + n_send_msgs,
                           [&] (const cExaMsg<DEPOT_TYPE>& msg) {
                             return \
                               recv.source() == msg.dest() &&
                               recv.tag() == msg.tag() &&
                               recv.n_elems() == msg.n_elems();
                           });
    send_msgs_start_index = it - send_msgs + 1;
    assert(it != send_msgs + n_send_msgs);
    auto& send_msg = *it;
    send_buff = send_msg.buffer();
  }
  
  GPU::Ptr<const DEPOT_TYPE> src(send_buff);
  GPU::Ptr<DEPOT_TYPE> dst(recv.buffer());
  GPU::copy_d2d_async(dst, src, recv.n_elems(), GPU::g_stream);    
  return send_msgs_start_index;
}

VOID complete_single_gpu_comm(sMLRF_RING* ring,
                              MLRF_PHASE phase) {
  if (phase == MLRF_PREDYN) {
    ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
      //start_search_index is used to reduce the array of messages to traverse to
      //find a matching message. Rather than traverse the entire ring message array
      //from the beginning, we take advantage of the fact that sends and receives
      //are posted in order and after finding a match, the next match occurs after
      //the current match      
      size_t start_search_index = 0;
      ccDOTIMES(nth_message, ring->m_n_messages[phase][i]) {
        auto& recv = ring->m_pre_dynamics_recv_msg[i][nth_message];
        if (is_exa_msg_to_self(recv)) {
          //i ^ 1 since internal msg maps to external frame
          start_search_index = complete_single_gpu_msg(recv,
                                                       ring->m_pre_dynamics_send_msg[i^1],
                                                       ring->m_n_messages[phase][i^1],
                                                       start_search_index);
        }
      }
    }
  } else {
    ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
      size_t start_search_index = 0;
      ccDOTIMES(nth_message, ring->m_n_messages[phase][i]) {
        auto& recv = ring->m_post_dynamics_recv_msg[i][nth_message];
        if (is_exa_msg_to_self(recv)) {
          //i ^ 1 since internal msg maps to external frame
          start_search_index = complete_single_gpu_msg(recv,
                                                       ring->m_post_dynamics_send_msg[i^1],
                                                       ring->m_n_messages[phase][i^1],
                                                       start_search_index);
        }
      }
    }    
  }
}
#else
VOID complete_single_gpu_comm(sMLRF_RING* ring,
                              MLRF_PHASE phase) {}
#endif

template<typename SFL_TYPE_TAG>
BOOLEAN sMLRF_RING::complete_receives(MLRF_PHASE phase) {
  
  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;
  
  if(m_all_messages_received[phase])
    return TRUE;
  
  ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
    ccDO_FROM_BELOW(nth_message, m_n_messages_received[phase][i], m_n_messages[phase][i]) {
      int flag = 0;
      MPI_Status status;
      if(phase == MLRF_PREDYN) {
        MPI_PROXY::MPI_Test(m_pre_dynamics_recv_msg[i][nth_message],&flag,&status);
      } else {
        MPI_PROXY::MPI_Test(m_post_dynamics_recv_msg[i][nth_message],&flag,&status);
      }
      if(flag) {
        m_n_messages_received[phase][i]++;
        g_total_predyn_receives_completed++;
      } else
        return FALSE;
    }
  }
  BOOLEAN all_received = TRUE;
  ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
    if(m_n_messages_received[phase][i] != m_n_messages[phase][i]) {
      all_received = FALSE;
    }
  }
  if(all_received) {
    ccDOTIMES(i, MLRF_N_ORIENTATIONS) {
      m_n_messages_received[phase][i] = 0;
    }
    m_all_messages_received[phase] = TRUE;
  }

  if constexpr(SFL_TYPE_TAG::is_msfl()) {
    if (m_all_messages_received[phase]) {
      complete_single_gpu_comm(this, phase);
    }
  }
  
  return(m_all_messages_received[phase]);
}

template<typename SFL_TYPE_TAG>
BOOLEAN tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::complete_receives(MLRF_PHASE phase) {
  
  if(m_all_rings_complete[phase]) {
    return TRUE;
  }

#if BUILD_GPU
  if constexpr(SFL_TYPE_TAG::is_msfl()) {
    //This call should go away when we have CUDA aware MPI. since MPI_Test
    //will perform this implicit sync for us
    if (!this->m_sync_single_gpu_comm.are_sends_ready(phase)) {
      return FALSE;
    }
    //No stream sync needed since all syncs should have been completed on the compute
    //thread before handing control back to the comm-thread to complete the comm
  }
#endif
  
  ccDO_FROM_BELOW(nth_ring, m_n_rings_complete[phase], n_rings()) {
    MLRF_RING current_ring = ring(nth_ring);
    if(current_ring->complete_receives<SFL_TYPE_TAG>(phase))
      m_n_rings_complete[phase]++;
    else {
      return FALSE;
    }
  }
  
  if(m_n_rings_complete[phase] == n_rings()) {
#if BUILD_GPU
    //Synchronize so we are sure we have all the async memcpys complete before we proceed.
    //and handover control to the compute-thread.
    //The rings themselves use async memcpys so that we don't wait for the completion of
    //individial memcpys for each orientation/message count/ring combo
    if constexpr(SFL_TYPE_TAG::is_msfl()) {
      checkCudaErrors(cudaStreamSynchronize(GPU::g_stream));
      this->m_sync_single_gpu_comm.unmark_sends_as_posted(phase);
    }
#endif 
    m_all_rings_complete[phase] = TRUE;
    m_n_rings_complete[phase] = 0;
  }
  
  return(m_all_rings_complete[phase]);
}
 

// Post-dynamics phase
template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::post_post_dynamics_sends(BOOLEAN is_seeding)
{
  LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
  // If this is the initial send during seeding, should use lrf->angle_rotated since pre_dyn_send_angle_rotated is not initialized yet.
  dFLOAT angle_rotated = is_seeding? lrf->angle_rotated : post_dyn_send_angle_rotated;
  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;
  LRF_SEND_MSG_PROGRESSION<sMLRF_POST_DYNAMICS_DEPOT> send_progression;
  
  ccDOTIMES(nth_ring, n_rings()) {

    MLRF_RING current_ring = ring(nth_ring);
    if(current_ring->n_ring_segments_on_proc() > 0) {

      dFLOAT angle_in_surfels = angle_rotated * current_ring->m_one_over_angle_per_surfel; // angle_rotated in decimal-surfel units

      asINT32 offsets[MLRF_N_ORIENTATIONS];
      offsets[MLRF_INTERNAL] = angle_in_surfels;
      offsets[MLRF_EXTERNAL] = ceil(current_ring->m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);
      int send_tags[MLRF_N_ORIENTATIONS];
      send_tags[MLRF_INTERNAL] = eMPI_MLRF_POST_DYN_ITOE_TAG;
      send_tags[MLRF_EXTERNAL] = eMPI_MLRF_POST_DYN_ETOI_TAG;


      // Do exterior first
      ccDOTIMES(inverse_orientation, MLRF_N_ORIENTATIONS) {
        int orientation = (inverse_orientation == MLRF_INTERNAL) ? MLRF_EXTERNAL : MLRF_INTERNAL;
        send_progression.add_entry(current_ring->m_post_dynamics_send_msg[orientation]);
        asINT32 n_post_dynamics_messages = 0;
        // note that the internal offset is used for ETOI and vice-versa
        MLRF_DO_SEND_MESSAGES(message_length, send_buffer_offset, remote_proc, current_ring, offsets[inverse_orientation]) {
         asINT32 &n_message = n_post_dynamics_messages;
         current_ring->m_post_dynamics_send_msg[orientation][n_message].setBuffer(&current_ring->m_post_dynamics_send_buffer[orientation][send_buffer_offset]);
         mlrf_validate_completion<SFL_TYPE_TAG>(current_ring->m_post_dynamics_send_msg[orientation][n_message], FALSE);
         g_total_postdyn_sends_posted++;
         current_ring->m_post_dynamics_send_msg[orientation][n_message].init(my_proc_id, remote_proc);
         current_ring->m_post_dynamics_send_msg[orientation][n_message].set_nelems(message_length);
         current_ring->m_post_dynamics_send_msg[orientation][n_message].settag(send_tags[orientation]);
         MPI_PROXY::isend(current_ring->m_post_dynamics_send_msg[orientation][n_message++]);
         send_progression.back().maybe_block_till_pending_msgs_are_complete(n_message);
        }
      }
    }
  }

#if BUILD_GPU
  //This call should go away when we have CUDA aware MPI
  this->m_sync_single_gpu_comm.mark_sends_as_posted(MLRF_POSTDYN);
#endif  
}

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::post_post_dynamics_receives()
{

  MLRF_POST_DYNAMICS_DEPOT depots[MLRF_N_ORIENTATIONS];
  int recv_tags[MLRF_N_ORIENTATIONS];
  recv_tags[MLRF_INTERNAL] = eMPI_MLRF_POST_DYN_ETOI_TAG;
  recv_tags[MLRF_EXTERNAL] = eMPI_MLRF_POST_DYN_ITOE_TAG;
  using MPI_PROXY = tMLRF_MPI_PROXY<SFL_TYPE_TAG>;
  LRF_PHYSICS_DESCRIPTOR lrf = m_lrf_physics_desc;
  dFLOAT angle_rotated = lrf->angle_rotated;

  m_all_rings_complete[MLRF_POSTDYN] = FALSE;
  m_n_rings_complete[MLRF_POSTDYN] = 0;
  
  ccDOTIMES(nth_ring, n_rings()) {

    MLRF_RING current_ring = ring(nth_ring);
    current_ring->m_all_messages_received[MLRF_POSTDYN] = FALSE;
    if(current_ring->n_ring_segments_on_proc() > 0) {

      dFLOAT angle_in_surfels = angle_rotated * current_ring->m_one_over_angle_per_surfel; // angle_rotated in decimal-surfel units


      asINT32 offsets[MLRF_N_ORIENTATIONS];
      offsets[MLRF_INTERNAL] = angle_in_surfels;
      offsets[MLRF_EXTERNAL] = ceil(current_ring->m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);

      asINT32 n_depots_per_frame =  current_ring->m_n_surfels_per_frame_on_proc + current_ring->n_ring_segments_on_proc();

      ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
        current_ring->m_n_messages[MLRF_POSTDYN][orientation] = 0;
        MLRF_DO_RECV_MESSAGES(message_length, depot_offset, remote_proc, current_ring, offsets[orientation]) {
          
          // Post receive of message from external frame to internal frame

          g_total_postdyn_receives_posted++;
          asINT32 &n_message = current_ring->m_n_messages[MLRF_POSTDYN][orientation];
          current_ring->m_post_dynamics_recv_msg[orientation][n_message].setBuffer(&current_ring->m_post_dynamics_recv_buffer[orientation][depot_offset]);
          current_ring->m_post_dynamics_recv_msg[orientation][n_message].init(remote_proc, my_proc_id);
          current_ring->m_post_dynamics_recv_msg[orientation][n_message].set_nelems(message_length);
          current_ring->m_post_dynamics_recv_msg[orientation][n_message].settag(recv_tags[orientation]);
          MPI_PROXY::irecv(current_ring->m_post_dynamics_recv_msg[orientation][n_message++]);
        }
      }
    }
  }
}

template<typename SFL_TYPE_TAG>
tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>* tMLRF_SURFEL_FSET<SFL_TYPE_TAG>::create_group(asINT32 scale, 
                                                                                asINT32 lrf_index,
                                                                                STP_EVEN_ODD even_odd,
                                                                                STP_REALM realm
#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
                                                                                ,std::vector < MEAS_WINDOW > &meas_windows
#endif
                                                                                )
{

  if (lrf_index < 0) {
    msg_internal_error("Cannot create LRF surfel group for non-lrf surfels");
  }

  // Create a group with matching sort criteria
  tMLRF_SURFEL_GROUP<SFL_TYPE_TAG> signature;
  signature.m_realm = realm;
  signature.m_scale = scale;
  signature.m_lrf_physics_desc = &sim.lrf_physics_descs[lrf_index];
  signature.m_even_odd = even_odd;
  signature.pre_dyn_send_angle_rotated = signature.m_lrf_physics_desc->angle_rotated;
  signature.post_dyn_send_angle_rotated = signature.m_lrf_physics_desc->angle_rotated;
#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
  // temporarily swap meas_window_ptrs into signature
  signature.m_meas_windows.swap(meas_windows);
#endif

  // Get a group of this type if it exists. If not, add a new one to the fset.
  auto group = this->find_group( &signature );

#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
  // restore meas_window_ptrs
  meas_windows.swap(signature.m_meas_windows);
#endif

  if(group == NULL) {
    group = xnew tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>;
    group->m_scale = scale;
    group->m_realm = realm;
    group->m_lrf_physics_desc = &sim.lrf_physics_descs[lrf_index];
    group->m_even_odd = even_odd;
    group->pre_dyn_send_angle_rotated = group->m_lrf_physics_desc->angle_rotated;
    group->post_dyn_send_angle_rotated = group->m_lrf_physics_desc->angle_rotated;

#if REF_FRAME_SURFELS_COLLECT_MEASUREMENTS
    group->m_meas_windows = meas_windows; // copy vector of meas windows
#endif

    if (group->m_lrf_physics_desc)
      group->m_lrf_physics_desc->is_active = TRUE; // used by some voxels or surfels

    this->add_group(group);
  }

  return group;
}

/*--------------------------------------------------------------------------*
 * MLRF Surfels
 *--------------------------------------------------------------------------*/

static VOID compute_surfel_inv_sum_pgram_volumes(SURFEL surfel,
						 dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES]
#if BUILD_D39_LATTICE 
						 ,dFLOAT& inv_total_pgram_volume_d39,
						 dFLOAT& inv_total_pgram_volume_d19
#endif
						 )
{
  ccDOTIMES(j, N_NONZERO_ENERGIES)
    inv_sum_pgram_volumes[j] = 0;

#if BUILD_D39_LATTICE 
  inv_total_pgram_volume_d39 = 0;
  inv_total_pgram_volume_d19 = 0;
  static sdFLOAT weight_d19[2] = {2.0, 1.0};
  static sdFLOAT weight_d39_dmass[N_NONZERO_ENERGIES] = {16.0, 8.0, 2.0, 1.0};
#endif

  // Compute the pgram volumes in double precision to ensure maximum accuracy
  FOREACH_MOVING_STATE_PAIR(i, cx, cy, cz, energy, weight, {
    asINT32 latvec_pair = state_latvec_pair(i);
    dFLOAT volume = (dFLOAT)weight * surfel->m_dp_pgram_volumes[latvec_pair];
    inv_sum_pgram_volumes[energy - 1] += volume; 
#if BUILD_D39_LATTICE 
    inv_total_pgram_volume_d39 += weight_d39_dmass[energy-1] * volume;
    if (energy < 3) {
      inv_total_pgram_volume_d19 += (weight_d19[energy-1] * volume); 
    }
#endif
  });

  ccDOTIMES(j, N_NONZERO_ENERGIES) 
    inv_sum_pgram_volumes[j] = 1.0/inv_sum_pgram_volumes[j];

#if BUILD_D39_LATTICE 
  inv_total_pgram_volume_d39 = 1.0 / inv_total_pgram_volume_d39;
  inv_total_pgram_volume_d19 = 1.0 / inv_total_pgram_volume_d19;
#endif
}

VOID read_lrf_containment(LGI_STREAM stream)
{
  cDGF_LRF_CONTAINMENT lrf_containment;
  lrf_containment.read(stream);
  cDGF_LRF_CONTAINMENT_INDEX containment_index;
  ccDOTIMES (index, lrf_containment.num_lrfs) {
    containment_index.read(stream);
    asINT32 lrf_index = containment_index.lrf_index;
    LRF_PHYSICS_DESCRIPTOR lrf_physics_desc = &sim.lrf_physics_descs[lrf_index];
    lrf_physics_desc->containing_lrf_index = containment_index.containing_lrf_index;
  }
}



VOID read_mlrf_ring_set(LGI_STREAM stream, STP_REALM realm)
{
  cDGF_MLRF_RING_SET ring_set;
  ring_set.read(stream);
  cDGF_MLRF_RING ring;
  ccDOTIMES (rindex, ring_set.n_rings) {
    ring.read(stream);
    asINT32 n_ring_surfels_per_frame = ring.n_surfels_in_ring;

    //Only an invalid surfel ID is sent if no surfel falls is in this SP. Thus, read the first surfel before entering in
    //the loop to read the rest of the surfels
    cDGF_MLRF_RING_SURFEL ring_surfel;
    ring_surfel.read(stream);
    if (ring_surfel.surfel_id == (STP_SURFEL_ID)-1)  //Cast to avoid compiler warning
      continue;

    asINT32 n_surfels_read_for_ring = 0;
    MLRF_SURFEL_GROUP group = g_mlrf_surfel_fset.create_group(ring.scale, ring_set.lrf_index, ring.even_odd, realm);
    asINT32 n_to_read = 2*n_ring_surfels_per_frame + 1; //max number of surfels that could be read from the ring
    ccDOTIMES(surfel_index, n_to_read) {
      cDGF_RING_SURFEL_INDEX ring_index;
      ring_index.read(stream);
      SURFEL surfel = regular_surfel_from_id(ring_surfel.surfel_id, realm);
      if (surfel == NULL)
        msg_internal_error("Surfel %d is not present in the surfel table", ring_surfel.surfel_id);
#if DEBUG
      surfel->lb_data()->m_attribs.set_is_mlrf_surfel_in_ring();
#endif
      dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES];
#if BUILD_D39_LATTICE
      dFLOAT inv_total_pgram_volume_d39;
      dFLOAT inv_total_pgram_volume_d19;
      compute_surfel_inv_sum_pgram_volumes(surfel, inv_sum_pgram_volumes, inv_total_pgram_volume_d39, inv_total_pgram_volume_d19); 
#else
      compute_surfel_inv_sum_pgram_volumes(surfel, inv_sum_pgram_volumes);
#endif
      add_mlrf_surfel(group, surfel, ring_index.index_in_ring, ring_surfel.is_weightless,
#if BUILD_D39_LATTICE
                      inv_total_pgram_volume_d39, inv_total_pgram_volume_d19,
#endif
                      inv_sum_pgram_volumes);
      n_surfels_read_for_ring++;
      // time to move to the next surfel
      ring_surfel.read(stream);
      if (ring_surfel.surfel_id == (STP_SURFEL_ID)-1)  //Cast to avoid compiler warning
        break;
    }

    // add the ring to the group
    MLRF_RING ring = group->add_ring();
    ring->m_n_surfels_per_frame_in_ring = n_ring_surfels_per_frame; 
    ring->m_n_surfels_per_frame_on_proc = (n_surfels_read_for_ring / 2); // number of surfel pairs
    ring->m_one_over_angle_per_surfel = n_ring_surfels_per_frame / (2.0 * PI);

    cDGF_RING_NUM_SEGMENTS segment_size;
    segment_size.read(stream);

    LGI_MLRF_RING_SEGMENT ring_segment;
    auINT32 segment_offset = 0;
    ccDOTIMES(nth_segment, segment_size.n_segments) {
      ring_segment.read(stream);
      sMLRF_RING_SEGMENT mlrf_ring_segment;
      mlrf_ring_segment.proc = ring_segment.proc_id;
      if (ring_segment.proc_id == my_proc_id) {
        ring->add_ring_segment_on_proc(nth_segment);
      }
      mlrf_ring_segment.offset = segment_offset;
      segment_offset += ring_segment.n_surfels;

      ring->add_ring_segment(mlrf_ring_segment);
    }

    // Allocate send buffers

    ring->max_messages(ring->m_n_send_requests,ring->m_n_receive_requests); 

    ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
#if !BUILD_GPU        
      ring->m_pre_dynamics_send_buffer[orientation] = xnew sMLRF_PRE_DYNAMICS_DEPOT [ ring->m_n_surfels_per_frame_on_proc + 1 ];
      ring->m_post_dynamics_send_buffer[orientation] = xnew sMLRF_POST_DYNAMICS_DEPOT [ ring->m_n_surfels_per_frame_on_proc + 1 ];
#endif
      ring->m_pre_dynamics_send_msg[orientation] = xnew cExaMsg<sMLRF_PRE_DYNAMICS_DEPOT> [ring->m_n_send_requests];
      ring->m_post_dynamics_send_msg[orientation] = xnew cExaMsg<sMLRF_POST_DYNAMICS_DEPOT> [ring->m_n_send_requests];

      ring->m_pre_dynamics_recv_msg[orientation] = xnew cExaMsg<sMLRF_PRE_DYNAMICS_DEPOT> [ring->m_n_receive_requests];
      ring->m_post_dynamics_recv_msg[orientation] = xnew cExaMsg<sMLRF_POST_DYNAMICS_DEPOT> [ring->m_n_receive_requests];
    }
  } // n_rings
}


#if !BUILD_GPU
VOID finish_init_of_mlrf_surfel_groups()
{
  DO_MLRF_SURFEL_GROUPS(group) {
    MLRF_PRE_DYNAMICS_DEPOT pre_dynamics_depots = 
      xnew sMLRF_PRE_DYNAMICS_DEPOT [ group->n_quantums() + (2*group->n_total_ring_segments_on_proc()) ];

    MLRF_POST_DYNAMICS_DEPOT post_dynamics_depots = 
      xnew sMLRF_POST_DYNAMICS_DEPOT [ group->n_quantums() + (2*group->n_total_ring_segments_on_proc()) ];

    MLRF_PRE_DYNAMICS_DEPOT pre_dynamics_depots_by_orientation[MLRF_N_ORIENTATIONS];
    MLRF_POST_DYNAMICS_DEPOT post_dynamics_depots_by_orientation[MLRF_N_ORIENTATIONS];
    pre_dynamics_depots_by_orientation[MLRF_EXTERNAL] = pre_dynamics_depots;
    post_dynamics_depots_by_orientation[MLRF_EXTERNAL] = post_dynamics_depots;
    
    ccDOTIMES(nth_ring, group->n_rings()) { 
      MLRF_RING current_ring = group->ring(nth_ring);
      asINT32 n_depots_per_frame = current_ring->n_recv_depots_per_frame();

      // Jump over exterior depots
      pre_dynamics_depots_by_orientation[MLRF_INTERNAL] = \
        pre_dynamics_depots_by_orientation[MLRF_EXTERNAL] + n_depots_per_frame;

      post_dynamics_depots_by_orientation[MLRF_INTERNAL] = \
        post_dynamics_depots_by_orientation[MLRF_EXTERNAL] + n_depots_per_frame;
      
      ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
        current_ring->m_pre_dynamics_recv_buffer[orientation] = pre_dynamics_depots_by_orientation[orientation];
        current_ring->m_post_dynamics_recv_buffer[orientation] = post_dynamics_depots_by_orientation[orientation];
      }

      // Jump over the exterior and interior depots to the next ring
      pre_dynamics_depots_by_orientation[MLRF_EXTERNAL] += 2 * n_depots_per_frame;
      post_dynamics_depots_by_orientation[MLRF_EXTERNAL] += 2 * n_depots_per_frame;
    }
  }
}
#else
VOID finish_init_of_mlrf_surfel_groups() {}
#endif

#if DEBUG
VOID check_if_all_sliding_mesh_surfels_in_rings()
{

  ccDOTIMES(i, g_mlrf_surfel_vector.size()) {
    SURFEL mlrf_surfel = g_mlrf_surfel_vector[i];
    if (!mlrf_surfel->lb_data()->m_attribs.get_is_mlrf_surfel_in_ring()) {
      msg_internal_error("Sliding mesh surfel ID %d is not assigned to any ring", mlrf_surfel->id());
    }
  }
  g_mlrf_surfel_vector.resize(0);
  g_mlrf_surfel_vector.trim(); // free all the storage

}
#endif

template<>
VOID sMLRF_SURFEL_GROUP::promote_interacting_ublks_to_sliding_nearblks() {

  TAGGED_MLRF_SURFEL *mlrf_surfels = &this->quantums()[0];
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_RING current_ring = ring(nth_ring);
    asINT32 n_surfels_per_frame_on_proc = current_ring->m_n_surfels_per_frame_on_proc;

    ccDOTIMES(side, 2) { // exterior and interior rings
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        if(mlrf_surfels[nth_surfel].is_surfel_weightless())
          continue;
        SURFEL surfel = mlrf_surfels[nth_surfel].mlrf_surfel();

        // mark all interacting ublks sliding if they are interior
        SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
        asINT32 n_ublks                          = surfel->m_n_ublk_interactions;

        for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
          UBLK ublk = ublk_interaction->ublk();
          // Mirror ublks are in the mirror group. Do not change their group type
          // We only want to upgrade interior ublks
          if (ublk->is_mirror() || ublk->is_fringe() ||
              ublk->is_mlrf_surfel_interacting() || ublk->is_ghost())
            continue;
          if (ublk->is_vr_fine()) { // The vr coarse and all vr fine ublks must be fringe2
            sVR_FINE_INTERFACE_DATA *vr_fine_data =  ublk->vr_fine_data();
            UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
            move_ublk_to_nearblk_group(coarse_ublk, SLIDING_NEARBLK_GROUP_TYPE);
            coarse_ublk->set_mlrf_surfel_interacting();
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data =  coarse_ublk->vr_coarse_data();
            ccDOTIMES(i, N_VOXELS_8) {
              UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
              if (fine_ublk == NULL)
                continue;
              move_ublk_to_nearblk_group(fine_ublk, VRFINE_SLIDING_NEARBLK_GROUP_TYPE);
              fine_ublk->set_mlrf_surfel_interacting();
            }
          } else if (ublk->is_vr_coarse()) {  // All the vr fine ublks must be fringe2
            sVR_COARSE_INTERFACE_DATA *vr_coarse_data = ublk->vr_coarse_data();
            ccDOTIMES(i, N_VOXELS_8) {
              UBLK fine_ublk = vr_coarse_data->vr_fine_ublk(i).ublk();
              if (fine_ublk == NULL)
                continue;
              move_ublk_to_nearblk_group(fine_ublk, VRFINE_SLIDING_NEARBLK_GROUP_TYPE);
              fine_ublk->set_mlrf_surfel_interacting();
            }
            // Move the vr coarse ublk to fringe2
            move_ublk_to_nearblk_group(ublk, SLIDING_NEARBLK_GROUP_TYPE);
            ublk->set_mlrf_surfel_interacting();
          } else {
            move_ublk_to_nearblk_group(ublk, SLIDING_NEARBLK_GROUP_TYPE);
            ublk->set_mlrf_surfel_interacting();
          }

        }
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;
    }
  }
}

template<>
VOID sMLRF_SURFEL_GROUP::sanity_check_for_sliding_surfels() {

  TAGGED_MLRF_SURFEL *mlrf_surfels = &this->quantums()[0];
  ccDOTIMES(nth_ring, n_rings()) {
    MLRF_RING current_ring = ring(nth_ring);
    asINT32 n_surfels_per_frame_on_proc = current_ring->m_n_surfels_per_frame_on_proc;

    ccDOTIMES(side, 2) { // exterior and interior rings
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        if(mlrf_surfels[nth_surfel].is_surfel_weightless())
          continue;
        SURFEL surfel = mlrf_surfels[nth_surfel].mlrf_surfel();
        SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
        asINT32 n_ublks                          = surfel->m_n_ublk_interactions;
        for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
          UBLK ublk = ublk_interaction->ublk();
          // Mirror ublks are in the mirror group. Do not change their group type
          // We only want to upgrade interior ublks
          if (!(ublk->is_mirror() || ublk->is_fringe() ||
                ublk->is_mlrf_surfel_interacting() || ublk->is_ghost())) {
            msg_internal_error("Sliding mesh surfel %d should not interact with interior ublk %d",
                               surfel->id(), ublk->id());
          }
        }
      }
      mlrf_surfels += n_surfels_per_frame_on_proc;
    }
  }
}

template<typename SFL_TYPE_TAG>
asINT32 tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::get_lrf_index() const {
  for (int i = 0; i < ::sim.n_lrf_physics_descs; i++) {
    if (m_lrf_physics_desc == &::sim.lrf_physics_descs[i]) {
      return i;
    }
  }
  return SRI_GLOBAL_REF_FRAME_INDEX;
}

#if BUILD_D19_LATTICE
//This is very useful for debugging and comparing entire CPU and GPU rings, please
//DO NOT DELETE even if unsued
template<typename RING_TYPE>
__DEVICE__ VOID print_depot_data_for_ring(RING_TYPE* ring,
                                          asINT32 ring_index) {

  asINT32 no_of_recv_depots = ring->n_recv_depots_per_frame();
  asINT32 no_of_send_depots = ring->n_send_depots_per_frame();

  for (auto orientation : {MLRF_INTERNAL, MLRF_EXTERNAL}) {
    auto pre_dyn_recv_buff = ring->pre_dynamics_recv_buff(orientation);
    auto pre_dyn_send_buff = ring->pre_dynamics_send_buff(orientation);
    auto post_dyn_recv_buff = ring->post_dynamics_recv_buff(orientation);
    auto post_dyn_send_buff = ring->post_dynamics_send_buff(orientation);

    for (int i = 0; i < no_of_recv_depots; i++) {
      printf("Ring %d, orient %d, depot %d, predyn rmass %5.7e, rmom %5.7e, %5.7e, %5.7e, postdyn rdeltamass %5.7e\n",
             ring_index, orientation, i, pre_dyn_recv_buff[i].mass,
             pre_dyn_recv_buff[i].mom[0], pre_dyn_recv_buff[i].mom[1], pre_dyn_recv_buff[i].mom[2],
             post_dyn_recv_buff[i].delta_mass);
    }

    for (int i = 0; i < no_of_send_depots; i++) {
      printf("Ring %d, orient %d, depot %d, predyn smass %5.7e, smom %5.7e, %5.7e, %5.7e, postdyn sdeltamass %5.7e\n",
             ring_index, orientation, i, pre_dyn_send_buff[i].mass,
             pre_dyn_send_buff[i].mom[0], pre_dyn_send_buff[i].mom[1], pre_dyn_send_buff[i].mom[2],
             post_dyn_send_buff[i].delta_mass);
    }
  }
}
#else
template<typename RING_TYPE>
__DEVICE__ VOID print_depot_data_for_ring(RING_TYPE* ring,
                                          asINT32 ring_index)
{
  assert(false);
}
#endif
#if BUILD_GPU
__GLOBAL__ VOID print_ring_content(cDEVICE_MLRF_RING* ring,
                                   asINT32 ring_index) {
  print_depot_data_for_ring(ring + ring_index, ring_index);
}
#endif

template<typename SFL_TYPE_TAG>
VOID tMLRF_SURFEL_GROUP<SFL_TYPE_TAG>::print_depot_data_for_ring(asINT32 ring_index) {
#if !BUILD_GPU
  auto ring = this->ring(ring_index);
  ::print_depot_data_for_ring(ring, ring_index);
#else
  if (m_scale == 9) {
    if (ring_index < this->n_rings()) {
      print_ring_content<<<1, 1>>>(this->get_device_rings().get(), ring_index);
      checkCudaErrors(cudaDeviceSynchronize());
    } else {
      msg_internal_error("Invalid ring index");
    }
  }
#endif
}

template class tMLRF_SURFEL_GROUP<SFL_SDFLOAT_TYPE_TAG>;
template class tMLRF_SURFEL_FSET<SFL_SDFLOAT_TYPE_TAG>;
#if BUILD_GPU
template class tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>;
template class tMLRF_SURFEL_FSET<MSFL_SDFLOAT_TYPE_TAG>;
#endif

VOID tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>::print_surfel_data(std::ostream& os) {

  using namespace UBLK_SURFEL_PRINT_UTILS;

  sim_print_data_header(os, "SURFEL_MLRF_DATA");
  sim_print(os, "index_in_ring", m_index_in_ring);
  sim_print_vs(os);
  sim_print<N_NONZERO_ENERGIES>(os, "inverse_sum_volumes", m_inverse_sum_volumes);

#if BUILD_D39_LATTICE
  sim_print_vs(os);
  sim_print(os, "inverse_total_volume_d39", m_inverse_total_volume_d39);
  sim_print(os, "inverse_total_volume_d19", m_inverse_total_volume_d19);
#endif
}

VOID tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>::init(asINT32 index_in_ring,
#if BUILD_D39_LATTICE
                                                   dFLOAT inverse_total_volume_d39,
                                                   dFLOAT inverse_total_volume_d19,
#endif
                                                   dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES],
                                                   BOOLEAN is_weightless)
{
  //Only capture the real surfel index in ring and not the proxy
  //this function gets called more than once for same sfl
  if (!is_weightless) {
    m_index_in_ring = index_in_ring;
  }
  ccDOTIMES(i, N_NONZERO_ENERGIES) {
    m_inverse_sum_volumes[i] = inv_sum_pgram_volumes[i];
  }
#if BUILD_D39_LATTICE
  m_inverse_total_volume_d39 = inverse_total_volume_d39;
  m_inverse_total_volume_d19 = inverse_total_volume_d19;
#endif
}

#if BUILD_GPU
VOID tSURFEL_MLRF_DATA<MSFL_SDFLOAT_TYPE_TAG>::init(tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>* other,
                                                    uINT32 ring_set_index,
                                                    asINT32 soxor,
                                                    BOOLEAN is_weightless,
                                                    uINT32 nth_surfel_in_ring_on_proc,
                                                    uINT32 recv_depot_index) {
  //Only capture the real surfel index in ring and not the proxy,
  //this function gets called more than once for same sfl
  //Note that proxys with the same SFL id can span rings
  if (!is_weightless) {
    m_index_in_ring[soxor] = nth_surfel_in_ring_on_proc;
    m_recv_depot_index[soxor] = recv_depot_index;
    m_ring_set_index[soxor] = ring_set_index;
  }
  ccDOTIMES(i, N_NONZERO_ENERGIES) {
    m_inverse_sum_volumes[i][soxor] = other->m_inverse_sum_volumes[i];
  }
#if BUILD_D39_LATTICE
  m_inverse_total_volume_d39[soxor] = other->m_inverse_total_volume_d39;
  m_inverse_total_volume_d19[soxor] = other->m_inverse_total_volume_d19;
#endif
}
#endif

VOID add_mlrf_surfel(tMLRF_SURFEL_GROUP<SFL_SDFLOAT_TYPE_TAG>* group,
                     tSURFEL<SFL_SDFLOAT_TYPE_TAG>* surfel,
                     asINT32 index_in_ring,
                     BOOLEAN is_weightless,
#if BUILD_D39_LATTICE
                     dFLOAT inverse_total_volume_d39,
                     dFLOAT inverse_total_volume_d19,
#endif
                     dFLOAT inv_sum_pgram_volumes[N_NONZERO_ENERGIES]) {

  auto mlrf_data = surfel->mlrf_data();
  mlrf_data->init(index_in_ring,
#if BUILD_D39_LATTICE
                  inverse_total_volume_d39,
                  inverse_total_volume_d19,
#endif
                  inv_sum_pgram_volumes,
                  is_weightless);

  // Voxels should not consider LRF surfels as boundaries for PDEs
  surfel->lb_data()->boundary_condition_type = BOUNDARY_CONDITION_LRF;
  surfel->set_weightless_mlrf(is_weightless);
  group->add_tagged_sfl(TAGGED_MLRF_SURFEL(surfel, is_weightless, 0));
}

#if BUILD_GPU
VOID add_mlrf_surfel(tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>* group,
                     tSURFEL<MSFL_SDFLOAT_TYPE_TAG>* msfl,
                     tSURFEL_MLRF_DATA<SFL_SDFLOAT_TYPE_TAG>* mlrf_sfl_data,
                     BOOLEAN is_weightless,
                     uINT32 ring_set_index,
                     asINT32 soxor,
                     uINT32 nth_surfel_in_ring_on_proc,
                     uINT32 recv_depot_index) {
  msfl->mlrf_data()->init(mlrf_sfl_data,
                          ring_set_index,
                          soxor, is_weightless,
                          nth_surfel_in_ring_on_proc,
                          recv_depot_index);
  msfl->lb_data()->boundary_condition_type[soxor] = BOUNDARY_CONDITION_LRF;
  group->add_tagged_sfl(tTAGGED_MLRF_SURFEL<MSFL_SDFLOAT_TYPE_TAG>(msfl, is_weightless, soxor));
}

INIT_MSFL(tSURFEL_MLRF_DATA) {
  COPY_SFL_TO_MSFL(m_index_in_ring);
  // Inverse of the sum of the parallelogram volumes
  VEC_COPY_SFL_TO_MSFL(m_inverse_sum_volumes, N_NONZERO_ENERGIES);

#if BUILD_D39_LATTICE
  COPY_SFL_TO_MSFL(m_inverse_total_volume_d39);
  COPY_SFL_TO_MSFL(m_inverse_total_volume_d19);
#endif  
}

#endif //BUILD_GPU
