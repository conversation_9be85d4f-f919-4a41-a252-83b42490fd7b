/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Vinit Gupta, Exa Corporation                      Created Fri, Dec 14, 2012
//----------------------------------------------------------------------------
//
#include "box_advect.h"
#include "comm_groups.h"
#include "gather_advect.h"
template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
static INLINE
void get_src_states(TAGGED_UBLK src_neighbor, int dest_instance,
                    int prev_lb_index, int prev_t_index, int prev_uds_index,
                    BOOLEAN is_timestep_even,
                    SOLVER_INDEX_MASK prior_solver_index_mask,
                    ACTIVE_SOLVER_MASK active_solver_mask,
                    uINT64 *src_states, uINT64 *src_states_t,
                    uINT64 *src_states_mc, VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS]) {

  UBLK src_ublk = NULL;
  if (!src_neighbor.is_ublk_scale_interface()) {
    if (src_neighbor.is_ublk_split()) {
      sUBLK_VECTOR *split_ublks = src_neighbor.split_ublks();
      if (dest_instance < split_ublks->m_n_ublks) {
        TAGGED_UBLK src_tagged = split_ublks->m_tagged_ublks[dest_instance];
        if (is_ublk_valid_for_gather_advect(src_tagged, is_timestep_even))
          src_ublk = src_tagged.ublk();
      }
    } else if (src_neighbor.is_ublk()) {
      if (is_ublk_valid_for_gather_advect(src_neighbor, is_timestep_even))
        src_ublk = src_neighbor.ublk();
    }
  }
  if (src_ublk) {
    if (src_ublk->has_two_copies()) {
      // explode is done on even timesteps
      explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      *src_states = (uINT64) src_ublk->lb_states(prev_lb_index)->m_states;
      if (ADVECT_TEMP)
        *src_states_t = (uINT64) src_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t;
      if (ADVECT_UDS) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  src_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS])(src_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds);
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component)
        *src_states_mc = (uINT64) src_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc;
#endif
    } else {
      *src_states = (uINT64) src_ublk->lb_states(ONLY_ONE_COPY)->m_states;
      if (ADVECT_TEMP)
        *src_states_t = (uINT64) src_ublk->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t;
      if (ADVECT_UDS) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  src_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (src_ublk->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds);
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component)
        *src_states_mc = (uINT64) src_ublk->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc;
#endif
    }
  } else {
    *src_states = 0;
    if (ADVECT_TEMP)
      *src_states_t = 0;
    if (ADVECT_UDS) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	src_states_uds[nth_uds] = 0;
    }
#if BUILD_5G_LATTICE
    if (g_is_multi_component)
      *src_states_mc = 0;
#endif
  }
}

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN HAS_S2V>
static INLINE
VOID collect_split_contributions(int n_src_voxels, const int src_voxels[],
                                 const int dest_voxels[], UBLK dest_ublk,
                                 VOXEL_STATE (*states)[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS],
				 VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS],
				 VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                 VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS],
                                 STP_GEOM_VARIABLE pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS],
                                 int latvec) {
  sdFLOAT fractional_factor = (1.0f - g_adv_fraction) * g_one_over_adv_fraction;
  ccDOTIMES(v, n_src_voxels) {
    CONNECT_MASK fluid_connect_mask = dest_ublk->voxel_fluid_connect_mask(dest_voxels[v]);
    // fluid connect mask is for outward pointing latvecs
    if (fluid_connect_mask.test(state_parity(latvec))) {
      if (HAS_S2V) {
        states[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states[latvec][src_voxels[v]];
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
         states_t[latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]] += pas_factors[latvec][dest_voxels[v]] * src_states_uds[nth_uds][latvec][src_voxels[v]];
	}

      } else {
        states[latvec][dest_voxels[v]]  = fractional_factor * p_states[latvec][dest_voxels[v]] +
                                          src_states[latvec][src_voxels[v]];
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          states_mc[latvec][dest_voxels[v]] = src_states_mc[latvec][src_voxels[v]];
        }
#else
        if (ADVECT_TEMP) {
          states_t[latvec][dest_voxels[v]]  = src_states_t[latvec][src_voxels[v]];
        }
#endif
	if (ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	    states_uds[nth_uds][latvec][dest_voxels[v]]  = src_states_uds[nth_uds][latvec][src_voxels[v]];
	}
      }
    }
  }
}

#if BUILD_D39_LATTICE
static STP_GEOM_VARIABLE unity_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS] =
    {
     //0
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //1
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //2
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //3
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //4
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //5
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //6
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     //7
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
    };
#else
static STP_GEOM_VARIABLE unity_pas_factors[N_MOVING_STATES][ubFLOAT::N_VOXELS] =
    {
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0,
    };
#endif

// This function doesn't even USE voxel_mask!
template <BOOLEAN ADVECT_TEMP,BOOLEAN ADVECT_UDS,  BOOLEAN HAS_S2V>
VOID gather_advect_split(sUBLK *pde_advect_ublk,
                         VOXEL_MASK_8 voxel_mask,
                         asINT32 split_instance_index,
                         BOOLEAN is_timestep_even,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         ACTIVE_SOLVER_MASK active_solver_mask)
{  
  const auto& box_access = pde_advect_ublk->m_box_access;

  STP_GEOM_VARIABLE (*pas_factors)[ubFLOAT::N_VOXELS];
  if (pde_advect_ublk->is_near_surface()) {
    pas_factors = (STP_GEOM_VARIABLE (*)[ubFLOAT::N_VOXELS])
                  pde_advect_ublk->surf_lb_data()->post_advect_scale_factors;
  } else {
    pas_factors = unity_pas_factors;
  }

  asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_lb_index = (1 ^ prev_lb_index);

  asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_t_index = (1 ^ prev_t_index);

  asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);
  asINT32 curr_uds_index = (1 ^ prev_uds_index);

  VOXEL_STATE (*states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(curr_lb_index)->m_states);
  VOXEL_STATE (*p_states)[ubFLOAT::N_VOXELS] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->lb_states(prev_lb_index)->m_states);
  VOXEL_STATE (*states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  VOXEL_STATE (*p_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  if (ADVECT_TEMP) {
    p_states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t);
    states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(curr_t_index)->m_states_t);
  }
  if (ADVECT_UDS) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
      p_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds);
      states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(curr_uds_index)->m_states_uds);
    }
  }

  VOXEL_STATE (*states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*p_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  if (g_is_multi_component) {
    p_states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc);
    states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(curr_lb_index)->m_states_mc);
  }

  TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();

  { // 3D

    asINT32 dest_ublk_instance = split_instance_index;
    VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;
    VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;
    VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
    VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};

    {
      TAGGED_UBLK src_tagged_ublk = tagged_ublk;
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
      {
        asINT32 latvec = 0;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {0, 1, 2, 3};
        const asINT32 dest_voxels[] = {4, 5, 6, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
								      states, states_t, states_mc,states_uds,
								      src_states, src_states_t, src_states_mc, src_states_uds,
								      p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 1;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {4, 5, 6, 7};
        const asINT32 dest_voxels[] = {0, 1, 2, 3};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 2;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {0, 1, 4, 5};
        const asINT32 dest_voxels[] = {2, 3, 6, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 3;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {2, 3, 6, 7};
        const asINT32 dest_voxels[] = {0, 1, 4, 5};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 4;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {0, 2, 4, 6};
        const asINT32 dest_voxels[] = {1, 3, 5, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 5;
        const asINT32 n_src_voxels  = 4;
        const asINT32 src_voxels[]  = {1, 3, 5, 7};
        const asINT32 dest_voxels[] = {0, 2, 4, 6};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 6;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {2, 3};
        const asINT32 dest_voxels[] = {4, 5};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 7;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {4, 5};
        const asINT32 dest_voxels[] = {2, 3};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 8;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {0, 1};
        const asINT32 dest_voxels[] = {6, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 9;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {6, 7};
        const asINT32 dest_voxels[] = {0, 1};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 10;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {4, 6};
        const asINT32 dest_voxels[] = {1, 3};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc, states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 11;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {1, 3};
        const asINT32 dest_voxels[] = {4, 6};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 12;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {5, 7};
        const asINT32 dest_voxels[] = {0, 2};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 13;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {0, 2};
        const asINT32 dest_voxels[] = {5, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 14;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {2, 6};
        const asINT32 dest_voxels[] = {1, 5};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 15;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {1, 5};
        const asINT32 dest_voxels[] = {2, 6};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 16;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {0, 4};
        const asINT32 dest_voxels[] = {3, 7};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
      {
        asINT32 latvec = 17;
        const asINT32 n_src_voxels  = 2;
        const asINT32 src_voxels[]  = {3, 7};
        const asINT32 dest_voxels[] = {0, 4};
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                 states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
      }
    }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(0);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);

      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 0;
          const asINT32 n_src_voxels  = 4;
          const asINT32 src_voxels[]  = {4, 5, 6, 7};
          const asINT32 dest_voxels[] = {0, 1, 2, 3};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                   src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 6;
          const asINT32 n_src_voxels  = 2;
          const asINT32 src_voxels[]  = {6, 7};
          const asINT32 dest_voxels[] = {0, 1};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 8;
          const asINT32 n_src_voxels  = 2;
          const asINT32 src_voxels[]  = {4, 5};
          const asINT32 dest_voxels[] = {2, 3};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 11;
          const asINT32 n_src_voxels  = 2;
          const asINT32 src_voxels[]  = {5, 7};
          const asINT32 dest_voxels[] = {0, 2};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 13;
          const asINT32 n_src_voxels  = 2;
          const asINT32 src_voxels[]  = {4, 6};
          const asINT32 dest_voxels[] = {1, 3};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                 src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(0);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 1;
          const asINT32 n_src_voxels  = 4;
          const asINT32 src_voxels[]  = {0, 1, 2, 3};
          const asINT32 dest_voxels[] = {4, 5, 6, 7};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                   src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 7;
          const asINT32 n_src_voxels  = 2;
          const asINT32 src_voxels[]  = {0, 1};
          const asINT32 dest_voxels[] = {6, 7};
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk,
                                                   states, states_t, states_mc,states_uds,
                                                   src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 9;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 2, 3 };
          const asINT32 dest_voxels[] = { 4, 5 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 10;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 0, 2 };
          const asINT32 dest_voxels[] = { 5, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 12;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 1, 3 };
          const asINT32 dest_voxels[] = { 4, 6 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(1);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 2;
          const asINT32 n_src_voxels = 4;
          const asINT32 src_voxels[] = { 2, 3, 6, 7 };
          const asINT32 dest_voxels[] = { 0, 1, 4, 5 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 7;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 6, 7 };
          const asINT32 dest_voxels[] = { 0, 1 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 8;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 2, 3 };
          const asINT32 dest_voxels[] = { 4, 5 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 15;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 3, 7 };
          const asINT32 dest_voxels[] = { 0, 4 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 16;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 2, 6 };
          const asINT32 dest_voxels[] = { 1, 5 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(1);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);

      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 3;
          const asINT32 n_src_voxels = 4;
          const asINT32 src_voxels[] = { 0, 1, 4, 5 };
          const asINT32 dest_voxels[] = { 2, 3, 6, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 6;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 0, 1 };
          const asINT32 dest_voxels[] = { 6, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 9;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 4, 5 };
          const asINT32 dest_voxels[] = { 2, 3 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 14;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 0, 4 };
          const asINT32 dest_voxels[] = { 3, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 17;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 1, 5 };
          const asINT32 dest_voxels[] = { 2, 6 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(2);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 4;
          const asINT32 n_src_voxels = 4;
          const asINT32 src_voxels[] = { 1, 3, 5, 7 };
          const asINT32 dest_voxels[] = { 0, 2, 4, 6 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 10;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 5, 7 };
          const asINT32 dest_voxels[] = { 0, 2 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 13;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 1, 3 };
          const asINT32 dest_voxels[] = { 4, 6 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 14;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 3, 7 };
          const asINT32 dest_voxels[] = { 0, 4 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 16;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 1, 5 };
          const asINT32 dest_voxels[] = { 2, 6 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(2);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        BOOLEAN does_src_ublk_swap_advect = !src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap();
        {
          asINT32 latvec = 5;
          const asINT32 n_src_voxels = 4;
          const asINT32 src_voxels[] = { 0, 2, 4, 6 };
          const asINT32 dest_voxels[] = { 1, 3, 5, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 11;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 0, 2 };
          const asINT32 dest_voxels[] = { 5, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 12;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 4, 6 };
          const asINT32 dest_voxels[] = { 1, 3 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 15;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 0, 4 };
          const asINT32 dest_voxels[] = { 3, 7 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
        {
          asINT32 latvec = 17;
          const asINT32 n_src_voxels = 2;
          const asINT32 src_voxels[] = { 2, 6 };
          const asINT32 dest_voxels[] = { 1, 5 };
          collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                   states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                   p_states, pas_factors, latvec);
          if (does_src_ublk_swap_advect){
            push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
									 p_states,p_states_t,p_states_mc,p_states_uds,
									 src_states,src_states_t,src_states_mc,src_states_uds,
									 latvec);
          }
        }
      }
    }
    {
      asINT32 latvec = 6;
      sINT16 offsets[3] = { -1, 1, 0 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 4, 5 };
        const asINT32 dest_voxels[] = { 2, 3 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 7;
      sINT16 offsets[3] = { 1, -1, 0 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 2, 3 };
        const asINT32 dest_voxels[] = { 4, 5 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 8;
      sINT16 offsets[3] = { -1, -1, 0 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 6, 7 };
        const asINT32 dest_voxels[] = { 0, 1 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 9;
      sINT16 offsets[3] = { 1, 1, 0 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 0, 1 };
        const asINT32 dest_voxels[] = { 6, 7 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 10;
      sINT16 offsets[3] = { 1, 0, -1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 1, 3 };
        const asINT32 dest_voxels[] = { 4, 6 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 11;
      sINT16 offsets[3] = { -1, 0, 1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 4, 6 };
        const asINT32 dest_voxels[] = { 1, 3 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 12;
      sINT16 offsets[3] = { 1, 0, 1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 0, 2 };
        const asINT32 dest_voxels[] = { 5, 7 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 13;
      sINT16 offsets[3] = { -1, 0, -1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 5, 7 };
        const asINT32 dest_voxels[] = { 0, 2 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
								       p_states,p_states_t,p_states_mc,p_states_uds,
								       src_states,src_states_t,src_states_mc,src_states_uds,
								       latvec);
        }
      }
    }
    {
      asINT32 latvec = 14;
      sINT16 offsets[3] = { 0, 1, -1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 1, 5 };
        const asINT32 dest_voxels[] = { 2, 6 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
                                                             p_states,p_states_t,p_states_mc,p_states_uds,
                                                             src_states,src_states_t,src_states_mc,src_states_uds,
                                                             latvec);
        }
      }
    }
    {
      asINT32 latvec = 15;
      sINT16 offsets[3] = { 0, -1, 1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 2, 6 };
        const asINT32 dest_voxels[] = { 1, 5 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
                                                             p_states,p_states_t,p_states_mc,p_states_uds,
                                                             src_states,src_states_t,src_states_mc,src_states_uds,
                                                             latvec);
        }
      }
    }
    {
      asINT32 latvec = 16;
      sINT16 offsets[3] = { 0, -1, -1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 3, 7 };
        const asINT32 dest_voxels[] = { 0, 4 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
                                                             p_states,p_states_t,p_states_mc,p_states_uds,
                                                             src_states,src_states_t,src_states_mc,src_states_uds,
                                                             latvec);
        }
      }
    }
    {
      asINT32 latvec = 17;
      sINT16 offsets[3] = { 0, 1, 1 };
      TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
      get_src_states<ADVECT_TEMP, ADVECT_UDS>(src_tagged_ublk, dest_ublk_instance,
					      prev_lb_index, prev_t_index, prev_uds_index,
					      is_timestep_even,
					      prior_solver_index_mask,
					      active_solver_mask,
					      (uINT64*) &src_states,
					      (uINT64*) &src_states_t,
					      (uINT64*) &src_states_mc,
					      src_states_uds);
      if (src_states) {
        const asINT32 n_src_voxels = 2;
        const asINT32 src_voxels[] = { 0, 4 };
        const asINT32 dest_voxels[] = { 3, 7 };
        collect_split_contributions<ADVECT_TEMP, ADVECT_UDS, HAS_S2V>(n_src_voxels, src_voxels, dest_voxels, pde_advect_ublk, states,
                                                 states_t, states_mc, states_uds, src_states, src_states_t, src_states_mc, src_states_uds,
                                                 p_states, pas_factors, latvec);
        if (!src_tagged_ublk.is_ublk_split() && src_tagged_ublk.ublk()->does_advect_through_swap()){
          push_states_to_swap_advect_neighbor<ADVECT_TEMP, ADVECT_UDS>(n_src_voxels,dest_voxels,src_voxels,
                                                             p_states,p_states_t,p_states_mc,p_states_uds,
                                                             src_states,src_states_t,src_states_mc,src_states_uds,
                                                             latvec);
        }
      }
    }
  }

  // The following is only done for farblks since states_clear is set for nearblks in S2V
  if (!HAS_S2V) {
    if (ADVECT_TEMP) {
      p_states_t = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->t_data()->lb_t_data(prev_t_index)->m_states_t);
    }
    if (ADVECT_UDS) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	p_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(prev_uds_index)->m_states_uds);
    }
#if BUILD_5G_LATTICE
    if (g_is_multi_component) {
      p_states_mc = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS]) (pde_advect_ublk->mc_data()->mc_states_data(prev_lb_index)->m_states_mc);
    }
#endif

    ccDOTIMES(v, ubFLOAT::N_VOXELS) {
      states[V_0_0_0][v] = p_states[V_0_0_0][v];
      if (ADVECT_TEMP) {
        states_t[V_0_0_0][v] = p_states_t[V_0_0_0][v];
      }
      if (ADVECT_UDS) {
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  states_uds[nth_uds][V_0_0_0][v] = p_states_uds[nth_uds][V_0_0_0][v];
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        states_mc[V_0_0_0][v] = p_states_mc[V_0_0_0][v];
      }
#endif
    }
    pde_advect_ublk->set_states_clear();  // Actually not needed since it will be unset after dynamics and the flag is never used 
                                          // before that.
  }

  pde_advect_ublk->toggle_advected();
}

template VOID gather_advect_split<TRUE, TRUE, TRUE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<TRUE, FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<FALSE, TRUE, TRUE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<FALSE, FALSE, TRUE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<TRUE, TRUE, FALSE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<TRUE, FALSE, FALSE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<FALSE,TRUE,FALSE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
template VOID gather_advect_split<FALSE,FALSE,FALSE>(sUBLK *pde_advect_ublk,
                                        VOXEL_MASK_8 voxel_mask,
                                        asINT32 split_instance_index,
                                        BOOLEAN is_timestep_even,
                                        SOLVER_INDEX_MASK prior_solver_index_mask,
                                        ACTIVE_SOLVER_MASK active_solver_mask);
