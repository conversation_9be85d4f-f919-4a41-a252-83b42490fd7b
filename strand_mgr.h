/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_STRAND_MGR_H
#define _SIMENG_STRAND_MGR_H
#include "comm_utils.h"
#include "sync_threads.h"
#include "strand_enum.h"
#include <atomic>

// All the strands that process ublks have been split into two categories (A and B).
// Category A strand will perform the following operations
// D19 : Advection + dynamics
// D33 : Advection + On-time MME
// 5G  : Advection + On-time MME
//
// Category B strand will perform the following operations
// D19 : Nothing
// D33 : Dynamics
// 5G  : Dynamics
//
// Category C strand is only for 5G large_pore. It will perform the following operations
// 5G:   Contruct adv_states using porosity

// Strands that process wsurfels are split into two phases, SAMPLING and HFC.
// HFC stand for Heat Flux Calculation. The division between the two phases is where
// interaction between the FLOW and CONDUCTION realms occurs, in the form of
// a communication from each real to the other. In the SAMPLING phase the quanties from
// one realm required for the heat flux calculation in the other are sampled and sent.
// In the HFC phase they are received, and the HHF is performed.
//
// Sampling :  S2S + V2S + DYN-A + Sampling
// HFC      :  HFC + DYN-B + S2V

inline constexpr STRAND_MASK SLIDING_SURF_A_STMASK     = STRAND_MASK().set(SLIDING_SURF_A_STRAND);
inline constexpr STRAND_MASK SLIDING_SURF_B_STMASK     = STRAND_MASK().set(SLIDING_SURF_B_STRAND);
inline constexpr STRAND_MASK SLIDING_SURF_C_STMASK     = STRAND_MASK().set(SLIDING_SURF_C_STRAND);
inline constexpr STRAND_MASK INTERIOR_FILM_ACCUMULATION_STMASK = STRAND_MASK().set(INTERIOR_FILM_ACCUMULATION_STRAND);
inline constexpr STRAND_MASK INTERIOR_FILM_KINEMATICS_STMASK   = STRAND_MASK().set(INTERIOR_FILM_KINEMATICS_STRAND);
inline constexpr STRAND_MASK RADIATION_STMASK     = STRAND_MASK().set(RADIATION_STRAND);
inline constexpr STRAND_MASK FRINGE2_SURF_STMASK       = STRAND_MASK().set(FRINGE2_SURF_STRAND);
inline constexpr STRAND_MASK FRINGE_WSURF_SAMPLING_STMASK   = STRAND_MASK().set(FRINGE_WSURF_SAMPLING_STRAND);
inline constexpr STRAND_MASK INTERIOR_WSURF_SAMPLING_STMASK = STRAND_MASK().set(INTERIOR_WSURF_SAMPLING_STRAND);
inline constexpr STRAND_MASK FRINGE2_WSURF_HFC_STMASK       = STRAND_MASK().set(FRINGE2_WSURF_HFC_STRAND);
inline constexpr STRAND_MASK FRINGE_SURF_STMASK        = STRAND_MASK().set(FRINGE_SURF_STRAND);
inline constexpr STRAND_MASK FRINGE_NEARBLK_A_STMASK   = STRAND_MASK().set(FRINGE_NEARBLK_A_STRAND);
inline constexpr STRAND_MASK FRINGE_FARBLK_A_STMASK    = STRAND_MASK().set(FRINGE_FARBLK_A_STRAND);
inline constexpr STRAND_MASK FRINGE2_NEARBLK_A_STMASK  = STRAND_MASK().set(FRINGE2_NEARBLK_A_STRAND);
inline constexpr STRAND_MASK SLIDING_NEARBLK_A_STMASK  = STRAND_MASK().set(SLIDING_NEARBLK_A_STRAND);
inline constexpr STRAND_MASK INTERIOR_NS_A_STMASK      = STRAND_MASK().set(INTERIOR_NS_A_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK2_A_STMASK = STRAND_MASK().set(INTERIOR_FARBLK2_A_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK1_A_STMASK = STRAND_MASK().set(INTERIOR_FARBLK1_A_STRAND);
inline constexpr STRAND_MASK FRINGE2_BSURFELS_STMASK   = STRAND_MASK().set(FRINGE2_BSURFELS_STRAND);
inline constexpr STRAND_MASK FRINGE_BSURFELS_STMASK    = STRAND_MASK().set(FRINGE_BSURFELS_STRAND);
inline constexpr STRAND_MASK INTERIOR_BSURFELS_STMASK  = STRAND_MASK().set(INTERIOR_BSURFELS_STRAND);
inline constexpr STRAND_MASK FRINGE_NEARBLK_B_STMASK   = STRAND_MASK().set(FRINGE_NEARBLK_B_STRAND);
inline constexpr STRAND_MASK FRINGE_FARBLK_B_STMASK    = STRAND_MASK().set(FRINGE_FARBLK_B_STRAND);
inline constexpr STRAND_MASK FRINGE2_NEARBLK_B_STMASK  = STRAND_MASK().set(FRINGE2_NEARBLK_B_STRAND);
inline constexpr STRAND_MASK SLIDING_NEARBLK_B_STMASK  = STRAND_MASK().set(SLIDING_NEARBLK_B_STRAND);
inline constexpr STRAND_MASK INTERIOR_NS_B_STMASK      = STRAND_MASK().set(INTERIOR_NS_B_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK2_B_STMASK = STRAND_MASK().set(INTERIOR_FARBLK2_B_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK1_B_STMASK = STRAND_MASK().set(INTERIOR_FARBLK1_B_STRAND);
#if BUILD_5G_LATTICE
inline constexpr STRAND_MASK FRINGE_NEARBLK_C_STMASK   = STRAND_MASK().set(FRINGE_NEARBLK_C_STRAND);
inline constexpr STRAND_MASK FRINGE_FARBLK_C_STMASK    = STRAND_MASK().set(FRINGE_FARBLK_C_STRAND);
inline constexpr STRAND_MASK FRINGE2_NEARBLK_C_STMASK  = STRAND_MASK().set(FRINGE2_NEARBLK_C_STRAND);
inline constexpr STRAND_MASK SLIDING_NEARBLK_C_STMASK  = STRAND_MASK().set(SLIDING_NEARBLK_C_STRAND);
inline constexpr STRAND_MASK INTERIOR_NS_C_STMASK      = STRAND_MASK().set(INTERIOR_NS_C_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK2_C_STMASK = STRAND_MASK().set(INTERIOR_FARBLK2_C_STRAND);
inline constexpr STRAND_MASK INTERIOR_FARBLK1_C_STMASK = STRAND_MASK().set(INTERIOR_FARBLK1_C_STRAND);
#endif

inline constexpr STRAND_MASK TIME_UPDATE_STMASK        = STRAND_MASK().set(TIMESTEP_UPDATE_STRAND);

inline constexpr STRAND_MASK ALL_STRANDS_STMASK        = STRAND_MASK().set_all();


/*cSTRAND_MGR_PMASK is a wrapper class around the array m_masks
* The array m_masks is initialized using the constexpr constructor
* at compile time.
*
* m_masks[strand] includes all the strands that are higher priority
* than the indexing strand
*/
class cSTRAND_MGR_PMASK {
public:
  constexpr cSTRAND_MGR_PMASK();
  STRAND_MASK operator[](asINT32 strand_index) const;
private:
  STRAND_MASK m_masks[N_STRANDS];
};

// The strand manager class. Only one of these exists.
class cSTRAND_MGR {

public:

  sSTRAND *m_strands[N_STRANDS];

  BOOLEAN m_comm_thread_started;

  std::atomic<bool>    m_timestep_last_events_unlocked;
  std::atomic<bool>    m_received_sim_finished_msg;
  bool    m_exit;                               // Exit at the end of the current timestep
  bool    m_halt;                               // Exit event in event queue is actually a halt event

  bool    m_full_ckpt_done;                     // Performed full checkpoint this timestep

  ATOMIC_STRAND_MASK m_runstate_mask;                  // A bitmask indicating the run state of each strand. The bit should be set when the
                                                // strand is launched, and cleared by the strand before it exits.

  BOOLEAN m_is_comm_thread_done;
  std::atomic<bool> m_timestep_last_events_processed;     // Tells if sim thread has finished processing ckpt events scheduled for BASETIME_LAST

  STRAND_MASK m_slist[N_STRANDS];               // Per-strand successor list. Implemented as an array of bit masks indexed by STRAND.
                                                // Each bit in a mask represents a successor strand. Could be const; see below 

  STRAND_MASK m_plist[N_STRANDS];               // Per-strand predecessor list
  STRAND_MASK m_slist_hardcoded[N_STRANDS];     // Used to test the equivalence of translated slists with hardcoded slists

  STRAND_MASK m_dlist[N_COMM_TYPES];            // Per-receive-type dependent strand list. Implemented as an array of bit masks indexed by RECV_TYPE.
                                                // Each bit in a mask represents a dependent strand. Could be const; see below 

  STRAND_MASK m_clist[N_COMM_TYPES];            // Per-receive-type consumer strand list. Implemented as an array of bit masks indexed by RECV_TYPE.
                                                // Each bit in a mask represents a dependent strand. Could be const; see below 

  STRAND_MASK m_coll_list[N_COLL_TYPES];        // Collective list for the current timestep. When a collective operation is placed on the collective_queue,
                                                // The value in m_dlist is copied here, and then used in count_receive_dependencies()

  asINT32 m_dstatic[N_STRANDS];                 // Total number of static dependencies for each strand, i.e. dependencies on other strands.
                                                // Does not include receive dependencies, which are dynamic and must be calculated on the fly. Could be const,
                                                // if there is no possibility of degenerate dependencies, e.g. the FRINGE_NEARBLK_A_STRAND
                                                // on a SP with no ghost surfels. In that case the strand probably doesn't have any
                                                // nearblks either, so it's part of a broader question of how to deal with degenerate cases
                                                // that may imply degenerate strands.

  asINT32 m_n_mlrf_deps[N_STRANDS];             // Total number of sliding-mesh comm dependencies for each strand. Unlike regular receive
                                                // dependencies, these are static.

  asINT32 m_dcntr[N_STRANDS];                   // Dependency counter per strand. When it goes to 0, the strand is added to the ready pool
                                                // by setting its m_ready flag.

  const asINT32 m_dcntr_invalid = -9;           // Strand counters are marked invalid as soon as they go ready, and are reset to a valid number at TU completion

  asINT32 m_rcntr[N_STRANDS];                   // Receive dependencies computed for debugging


  ATOMIC_STRAND_MASK m_ready_mask;              // ready pool. A strand is ready if its corresponding bit is set.
  

  uINT16 *m_active_consumers_count[N_RECV_TYPES];  // Per-recv-type per-scale count
                                                   // of the number of strands still actively consuming the set
                                                   // of ghostblocks of that type and scale.
  pthread_mutex_t m_active_consumers_mutex;

  uINT16 m_total_consumer_count[N_COMM_TYPES];     // Per-recv-type count of the total
                                                   // number of strands that consume that type
                                                   // of ghostblock.

  // Per-recv-type per-scale list of
  // receive groups with full-but-not-yet-unpacked receive buffers. 
  // List is threaded through the groups.
  SP_RECV_GROUP_BASE *m_unpacking_list_head[N_RECV_TYPES];
  // typedef std::vector<std::vector<std::list<sRECV_GROUP_BASE*>>> RecvtypeScaleList;
  // RecvtypeScaleList m_unpacking_list;

  COLLECTIVE_GROUP *m_collective_list_head;

  uINT16 *m_collective_consumers_count;

 public:

  cCOLLECTIVE_QUEUE *m_collective_queue;
  cSEND_QUEUE *m_send_queue;
  asINT32 m_n_send_groups;

  cMEAS_SEND_QUEUE *m_meas_send_queue;

  cMEAS_COMPLETION_QUEUE m_meas_completion_queue;
  cMEAS_PENDING_SEND_QUEUE m_meas_pending_queue;

  cNEIGHBOR_SP_MAP m_neighbor_sp_map;

// This mask includes all the strands that are higher priority than the indexing strand
  const static cSTRAND_MGR_PMASK m_pmask;

  cRECV_CHANNEL m_recv_channel[N_RECV_TYPES];
  sCOLLECTIVE_GROUP *m_collective_group[N_COLL_TYPES];

  cSEND_CHANNEL m_surfel_send_channel;
  cSEND_CHANNEL m_wsurfel_send_channel;
  cSEND_CHANNEL m_contact_send_channel;

  cRP_RECV_CHANNEL m_rp_recv_channel;

  struct timespec sp_start_timespec;
  struct timespec main_thread_start_timespec;
  struct timespec comm_thread_start_timespec;
  struct timespec sp_stop_timespec;
  struct timespec main_thread_stop_timespec;
  struct timespec comm_thread_stop_timespec;
  asINT64 last_timestep_main_thread_stop_ns;

  pthread_mutex_t m_dcntr_mutex;
  pthread_mutex_t m_time_update_mutex;

  // sliding mesh communication
  cMLRF_COMM m_mlrf_comm;

  // fan communication
  cFAN_COMM m_fan_comm;

// This is for strands that surrender and need to save state
  VOID complete_timestep(ACTIVE_SOLVER_MASK finest_active_solver_mask = 0x0);                                        // Called by a strand to indicate it's done and start next ready strand, if any
  BOOLEAN consider_surrender(asINT32 calling_strand);                 // Called by a strand when it's done with a work chunk to possibly surrender
                                                                   // control to a higher priority ready strand.
 
  VOID update_consumer_counts(STP_SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask = 0x0);        // Called by a ghost-utilizing strand to indicate that it is done with the indicated scale
  VOID update_wsurfel_consumer_counts(STP_SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask = 0x0);        // Called by time update strand to decrement the counter if the wsurfel commed data
                                                                                                            // will be consumed in this timestep

  VOID add_to_unpacking_list(SP_RECV_GROUP_BASE rgroup);                                                                      
  VOID process_unpacking_list();
  VOID process_farblk_unpacking_list();
  VOID process_nearblk_unpacking_list();
  VOID process_surfel_unpacking_list();
  VOID process_unpacking_list(RECV_TYPE recv_type, cRECV_CHANNEL* rchannel);

  VOID recv_update_dcntr(RECV_TYPE type);

  VOID mlrf_recv_update_dcntr(MLRF_COMM_TYPE type, TIMESTEP t_step);
  VOID mlrf_send_update_dcntr(MLRF_COMM_TYPE type, TIMESTEP t_step);

  VOID add_surfel_groups_of_scale_to_send_queue(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask);

  VOID post_initial_recvs();
  VOID cancel_pending_recvs();

  asINT32 count_receive_dependencies(asINT32 strand, BOOLEAN is_wsurfel_comm);
  asINT32 count_collective_dependencies(asINT32 strand);
  // asINT32 count_initial_receive_dependencies(asINT32 strand, TIMESTEP dependent_ts);
  VOID initialize_dependency_counters();

  VOID init_send_queues(asINT32 n_meas_windows, asINT32 n_send_groups);

  VOID start_runstate(STRAND strand) { m_runstate_mask.set(strand); }
  VOID stop_runstate(STRAND strand)  { m_runstate_mask.reset(strand); }
  VOID init();
  VOID finish_init();
  bool are_wsurfels_commed(ACTIVE_SOLVER_MASK active_solver_mask);

  VOID collective_update_dcntr(COLLECTIVE_TYPE ctype);
  VOID add_collective_to_queue(COLLECTIVE_TYPE ctype);
  VOID clear_collective_counters();
  VOID init_collectives();
  VOID add_to_collective_list(COLLECTIVE_GROUP cgroup);
  VOID update_collective_consumer_counts();
  VOID process_collective_list();
  VOID process_collective_list(COLLECTIVE_TYPE ctype);

  void allocate_recv_channels();

 private:

  VOID allocate_strands();

  VOID init_recv_dependency_list();
  VOID init_recv_consumer_list();
  VOID init_recv_consumer_counts();
  VOID init_total_static_strand_dep();
  VOID init_mutexs_and_event_flags();
  VOID init_unpacking_lists();
  VOID init_collective_lists();
  VOID reset_collective_consumers_count(COLLECTIVE_TYPE ctype);
  VOID init_collective_consumer_counts();

  //Populate successor strands from hard coded predecessor strands
  VOID init_slists_from_plists();
  VOID translate_plists_to_slists();
  VOID verify_translated_plists();

  //Identify predecessor strands with hard coded values
  VOID init_plists();
  VOID init_plists_for_strict_scheduling_with_mme_comm();
  VOID init_plists_for_dynamic_scheduling_with_mme_comm();
  VOID init_plists_for_strict_scheduling_without_mme_comm();
  VOID init_plists_for_dynamic_scheduling_without_mme_comm();

  VOID reset_m_n_mlrf_deps(BOOLEAN init_all_strands = TRUE);
  VOID reset_dcntrs_for_next_timestep(ACTIVE_SOLVER_MASK finest_active_solver_mask = 0x0);
  // The sole function that modifies dcntrs.
  VOID decr_and_maybe_invalidate_dcntr(STRAND strand);
  VOID reset_active_consumers_count(RECV_TYPE rtype,SCALE scale);

 private:
  friend class cRECV_CHANNEL;
  VOID recv_channel_record_completion(RECV_TYPE rtype, SCALE scale);
};

//=============================================================================
// UTILITY FUNCTIONS
VOID PRINT_STRANDS(pthread_mutex_t& dcntr_mutex);
BOOLEAN does_recv_satisfy_same_timestep_dep(RECV_TYPE rtype);
BOOLEAN does_recv_satisfy_cond_flow_sp_comm(RECV_TYPE rtype);
VOID initialize_recv_group_arrays();
VOID initialize_recv_group_arrays_on_CPU();
VOID initialize_receive_channels();
VOID post_initial_receives();
VOID set_running_strand(STRAND new_strand, BOOLEAN new_timestep = TRUE);
BOOLEAN consider_surrender(asINT32 calling_strand);
#endif   /* #ifndef_SIMENG_STRAND_MGR_H */
