auINT32 compute_voxel_mask_for_real_ublk(DGF_REAL_UBLK_DESC ublk_desc)
{
  auINT32            voxel_mask;
  dFLOAT  pfluid; // not used - but find_voxel_dominant_fluid_region wants to update it

  DO_ACTIVE_VOXELS(voxel) {
    DGF_VOXEL_FLAGS voxel_flags = ublk_desc->voxel_flags[voxel].voxel_flags;
     if (voxel_flags & DGF_VOXEL_SIMPLE) {
      DGF_SIMPLE_VOXEL_DESC simple_voxel_desc = &ublk_desc->simple_voxels[voxel];
      fluid_region_index  = simple_voxel_desc->v.part_index;
    } else if (voxel_flags & DGF_VOXEL_GENERAL) {
      DGF_GENERAL_VOXEL_DESC general_voxel_desc = &ublk_desc->general_voxels[voxel];
      fluid_region_index = find_voxel_dominant_fluid_region(general_voxel_desc, pfluid);
     } else {
      fluid_region_index = -1;
    }
     if (fluid_region_index >= 0) {
       voxel_mask  |= 1 << voxel;
     }
  }
  return voxel_mask;
}
  
