/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */
/*****************************************************************************
 *** ExaSIM Physics Modules                                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1995-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */

/*--------------------------------------------------------------------------*
 * Implementation for ublk box sp tree 
 *
 * Abhishek Jain
 * Created Sep 30, 2024
 *--------------------------------------------------------------------------*/

#include "ublk_box_tree.h"
#include "ublk_tree.h"
#include "strand_mgr.h"

std::vector<sUBLK_BOX_HEADER> g_ublk_box_header;
cSHARED_MEMORY* smem_box_headers;
int g_n_ublk_box_header;
sBOX_TREE g_box_tree;
sUBLK_TREE g_ublk_tree;
    
//To comm bsurfels to any SP, and comming the box headers to all SPs
VOID add_all_procs_to_sp_map()
{
  int n_sps;
  MPI_Comm_size(eMPI_sp_comm, &n_sps);

  ccDOTIMES(i, n_sps) {
    g_strand_mgr.m_neighbor_sp_map.add_nsp(i);
  }
}

// Function that build the sp map
VOID build_box_spmap_for_bsurfels() 
{
  
  sBOX_SPMAP_BUILDER spmap_builder;
  
  spmap_builder.create_box_info_headers();

  spmap_builder.gather_to_master_sp();

  spmap_builder.put_box_headers_in_smem();
  
  spmap_builder.allgather_to_master_sps();

  spmap_builder.free_dyn_mem();

  g_box_tree.build_tree();

  g_ublk_tree.build_tree();
}

sBOX_SPMAP_BUILDER::sBOX_SPMAP_BUILDER() 
{
    
    MPI_Comm_size(eMPI_sp_node_comm, &n_sps_node);
    MPI_Comm_rank(eMPI_sp_node_comm, &rank_on_node);

    MPI_Comm_size(eMPI_mastersp_node_comm, &n_mastersp);

    is_master = false;

    if (rank_on_node == master_rank)
      is_master = true;

}


VOID sBOX_SPMAP_BUILDER::create_box_info_headers() 
{
  DO_UBLK_BOXES(ublk_box) {
    sUBLK_BOX_HEADER new_header(ublk_box);
    ublk_box_header_sp.push_back(new_header);
  }
}

VOID sBOX_SPMAP_BUILDER::gather_to_master_sp()
{

  //gathering boxes
  nboxes_to_send_sp = ublk_box_header_sp.size();
  if(is_master)
    recv_nboxes_sp = (int*) malloc(sizeof(int) * n_sps_node);
 
      //gather nboxes 
  MPI_Gather(&nboxes_to_send_sp, 1, MPI_INT, recv_nboxes_sp, 1,  MPI_INT, master_rank, eMPI_sp_node_comm);
  
  if (is_master) {
    ccDOTIMES(n, n_sps_node) {
      nboxes_on_master += recv_nboxes_sp[n];
      recv_nboxes_sp[n] *= sizeof(sUBLK_BOX_HEADER_BASE);
    }
    recv_boxes_sp = (sUBLK_BOX_HEADER_BASE*) malloc(sizeof(sUBLK_BOX_HEADER_BASE) * nboxes_on_master);
    
    displs_boxes_sp = (int*) malloc(sizeof(int) * n_sps_node);
    displs_boxes_sp[0] = 0;
    for (int i = 1; i < n_sps_node; i++) {
      displs_boxes_sp[i] = displs_boxes_sp[i-1] + recv_nboxes_sp[i-1];
    }
  }

  sUBLK_BOX_HEADER_BASE send_boxes[nboxes_to_send_sp];
  ccDOTIMES(n, nboxes_to_send_sp) {
    sUBLK_BOX_HEADER_BASE lite_header(ublk_box_header_sp.at(n));
    send_boxes[n] = lite_header;
  }
 
      //gather boxes 
  MPI_Gatherv(&send_boxes, sizeof(sUBLK_BOX_HEADER_BASE)*nboxes_to_send_sp, MPI_BYTE,
    recv_boxes_sp, recv_nboxes_sp, displs_boxes_sp, MPI_BYTE, master_rank, eMPI_sp_node_comm);

  //gathering ublks
  
  ccDOTIMES(n, nboxes_to_send_sp)
   nublks_to_send_sp += ublk_box_header_sp.at(n).ublk_bitset.size(); 
  
  uINT8 send_ublks[nublks_to_send_sp];

  int i_ublk = 0;
  ccDOTIMES(n, nboxes_to_send_sp) {
    sUBLK_BOX_HEADER* box_header = &ublk_box_header_sp.at(n);
    ccDOTIMES(i, box_header->ublk_bitset.size()) {
      send_ublks[i_ublk] = box_header->ublk_bitset.at(i);
      i_ublk++;
    }
  }

  if (is_master) {
    recv_nublks_sp = (int*) malloc(sizeof(int) * n_sps_node);
    int n_box_start = 0;
    ccDOTIMES(n, n_sps_node) {
      int n_box_end = n_box_start + recv_nboxes_sp[n]/sizeof(sUBLK_BOX_HEADER_BASE);
      recv_nublks_sp[n] = 0;
      for (int i_box = n_box_start; i_box < n_box_end; i_box++) {
        sUBLK_BOX_HEADER_BASE* lite_header = &recv_boxes_sp[i_box];
        int num_ublks = lite_header->m_size[0] * lite_header->m_size[1] * lite_header->m_size[2];
        recv_nublks_sp[n] += num_ublks;
        nublks_on_master += num_ublks; 
      }
      recv_nublks_sp[n] *= sizeof(uINT8);
      n_box_start = n_box_end;
    }
    recv_ublks_sp = (uINT8*) malloc(sizeof(uINT8) * nublks_on_master);

    displs_ublks_sp = (int*) malloc(sizeof(int) * n_sps_node);
    displs_ublks_sp[0] = 0;
    for (int i = 1; i < n_sps_node; i++) {
      displs_ublks_sp[i] = displs_ublks_sp[i-1] + recv_nublks_sp[i-1];
    }
  }
  
  MPI_Gatherv(&send_ublks, sizeof(uINT8)*nublks_to_send_sp, MPI_BYTE,
    recv_ublks_sp, recv_nublks_sp, displs_ublks_sp, MPI_BYTE, master_rank, eMPI_sp_node_comm);


}

VOID sBOX_SPMAP_BUILDER::put_box_headers_in_smem()
{
  MPI_Barrier(eMPI_sp_node_comm);

  char* smem_startPtr = nullptr;
  char* smem_currentPtr = nullptr;

  int n_ublk_box_header = 0;
  int master_pid = 0;
  int bytes_allocate = 0;

  int offset_dynamic = 0;

  if (is_master) { 
    master_pid = ::getpid();
    std::string smem_name( std::string("box_header") + std::to_string( master_pid ) );

    int offset_boxes = 0;
    int offset_ublks = 0;

    if (n_mastersp > 1) {
      recv_nboxes_master = (int*) malloc(sizeof(int) * n_mastersp);
      recv_nublks_master = (int*) malloc(sizeof(int) * n_mastersp);
          
      MPI_Allgather(&nboxes_on_master, 1, MPI_INT, recv_nboxes_master, 1,  MPI_INT, eMPI_mastersp_node_comm);
      
      MPI_Allgather(&nublks_on_master, 1, MPI_INT, recv_nublks_master, 1,  MPI_INT, eMPI_mastersp_node_comm);

      ccDOTIMES(n, n_mastersp) {
        tot_nboxes_dom += recv_nboxes_master[n];
        tot_nublks_dom += recv_nublks_master[n];
      }
     
      int rank_on_master;
      MPI_Comm_rank(eMPI_mastersp_node_comm, &rank_on_master); 
      ccDOTIMES(n, rank_on_master) {
        offset_boxes += recv_nboxes_master[n];
        offset_ublks += recv_nublks_master[n];
      }
    } else {
      tot_nboxes_dom = nboxes_on_master;
      tot_nublks_dom = nublks_on_master;
    } 

    g_n_ublk_box_header = tot_nboxes_dom;
    bytes_allocate = sizeof(sUBLK_BOX_HEADER_BASE) * tot_nboxes_dom;
    bytes_allocate += sizeof(uINT8) * tot_nublks_dom;
    smem_box_headers = new cSHARED_MEMORY(smem_name, bytes_allocate, cSHARED_MEMORY::MEM_ADDR_TYPE_RELATIVE, g_sp_node_comm.nprocs());

    smem_startPtr = (char*) smem_box_headers->GetData();
    offset_dynamic  += sizeof(sUBLK_BOX_HEADER_BASE) * tot_nboxes_dom + sizeof(uINT8) * offset_ublks - sizeof(sUBLK_BOX_HEADER_BASE) * offset_boxes;

    ccDOTIMES(i, nboxes_on_master) {
      sUBLK_BOX_HEADER_BASE* lite_box_header = &recv_boxes_sp[i];
      lite_box_header->offset_dynData = offset_dynamic;
      offset_dynamic  += sizeof(uINT8) * (lite_box_header->m_size[0] * lite_box_header->m_size[1] * lite_box_header->m_size[2])  - sizeof(sUBLK_BOX_HEADER_BASE);
    }
 
    smem_currentPtr = smem_startPtr + sizeof(sUBLK_BOX_HEADER_BASE) * offset_boxes;
    memcpy(smem_currentPtr, recv_boxes_sp, sizeof(sUBLK_BOX_HEADER_BASE) * nboxes_on_master);

    smem_currentPtr = smem_startPtr + sizeof(sUBLK_BOX_HEADER_BASE) * tot_nboxes_dom + sizeof(uINT8) * offset_ublks;

    memcpy(smem_currentPtr, recv_ublks_sp, sizeof(uINT8) * nublks_on_master);

  }

  MPI_Bcast(&g_n_ublk_box_header, 1, MPI_INT, master_rank, eMPI_sp_node_comm);
  MPI_Bcast(&master_pid         , 1, MPI_INT, master_rank, eMPI_sp_node_comm);
  MPI_Bcast(&bytes_allocate     , 1, MPI_INT, master_rank, eMPI_sp_node_comm);

  if (!is_master) {
    std::string smem_name( std::string("box_header") + std::to_string( master_pid ) );
    smem_box_headers = new cSHARED_MEMORY(smem_name, bytes_allocate, cSHARED_MEMORY::MEM_ADDR_TYPE_RELATIVE, g_sp_node_comm.nprocs());
  }

}

VOID sBOX_SPMAP_BUILDER::free_dyn_mem()
{
  free(recv_nboxes_sp);
  free(recv_boxes_sp);
  free(displs_boxes_sp);
  
  free(recv_nublks_sp);
  free(recv_ublks_sp);
  free(displs_ublks_sp);
}

VOID sBOX_SPMAP_BUILDER::allgather_to_master_sps()
{
  if (!is_master)
    return;

  if (n_mastersp == 1)
    return;
  
  ccDOTIMES(n, n_mastersp) {
    recv_nboxes_master[n] *= sizeof(sUBLK_BOX_HEADER_BASE);
    recv_nublks_master[n] *= sizeof(uINT8);
  }

  displs_boxes_master = (int*) malloc(sizeof(int) * n_mastersp);
  displs_boxes_master[0] = 0;
  for (int i = 1; i < n_mastersp; i++) {
    displs_boxes_master[i] = displs_boxes_master[i-1] + recv_nboxes_master[i-1];
  }

  sUBLK_BOX_HEADER_BASE* smem_startPtr = nullptr;
  smem_startPtr = (sUBLK_BOX_HEADER_BASE*) smem_box_headers->GetData();

  MPI_Allgatherv(MPI_IN_PLACE, sizeof(sUBLK_BOX_HEADER_BASE) * nboxes_on_master, MPI_BYTE, smem_startPtr, recv_nboxes_master, displs_boxes_master, MPI_BYTE, eMPI_mastersp_node_comm);
  
  displs_ublks_master = (int*) malloc(sizeof(int) * n_mastersp);
  displs_ublks_master[0] = 0;
  for (int i = 1; i < n_mastersp; i++) {
    displs_ublks_master[i] = displs_ublks_master[i-1] + recv_nublks_master[i-1];
  }

  uINT8* smem_startPtr_ublk = nullptr;
  smem_startPtr_ublk = (uINT8*) smem_box_headers->GetData();

  MPI_Allgatherv(MPI_IN_PLACE, sizeof(uINT8) * nublks_on_master, MPI_BYTE, smem_startPtr_ublk, recv_nublks_master, displs_ublks_master, MPI_BYTE, eMPI_mastersp_node_comm);
  
  //free dyn memory
  free(recv_nboxes_master);
  free(recv_nublks_master);
  free(displs_boxes_master);
  free(displs_ublks_master);

}


VOID sBOX_TREE::create_root_node() 
{
  //1. now fill the vector of ublk_box_header pointers.
  sBOX_TREE_NODE first_node;
  first_node.m_index = 0;
  first_node.m_level = 0;

  char* smem_ptr = nullptr;
  smem_ptr = (char*) smem_box_headers->GetData();
  
  ccDOTIMES(box_header, g_n_ublk_box_header) {
    first_node.m_ublk_boxes.push_back( (sUBLK_BOX_HEADER_BASE*) smem_ptr );
    smem_ptr += sizeof(sUBLK_BOX_HEADER_BASE);
  }

  ccDOTIMES(axis, 3) {
    first_node.m_min[axis] = std::numeric_limits<STP_COORD>::max() ;
    first_node.m_max[axis] = -std::numeric_limits<STP_COORD>::max() ;
  }

  //2. run through all ublk_box_headers, find out min and max
  ccDOTIMES(i, first_node.m_ublk_boxes.size() ) {
    sUBLK_BOX_HEADER_BASE* box_header = first_node.m_ublk_boxes.at(i);
    dFLOAT voxel_dx = sim_scale_to_voxel_size(box_header->m_scale);
    dFLOAT ivdx = 1.0 / voxel_dx;
    dFLOAT ublk_distance = 2.0 * voxel_dx;

    ccDOTIMES(axis, 3) {
      STP_COORD box_min = box_header->m_location[axis];
      STP_COORD box_max = box_header->m_location[axis] + ublk_distance * box_header->m_size[axis];
      first_node.m_min[axis] = std::min ( first_node.m_min[axis], box_min );
      first_node.m_max[axis] = std::max ( first_node.m_max[axis], box_max );
    }
  }

  //3. put root_node in the tree node vector, and update the pointer to the root_node
  m_nodes.push_back(first_node);
  root_node = &m_nodes.back();

}

    
VOID sBOX_TREE::propogate_branch_until_leaf (sBOX_TREE_NODE current_node) 
{
  //0. check if current_node->m_ublk_boxes.size()<=8? return. Leaf node.
  if (current_node.m_ublk_boxes.size() <= 10 || current_node.m_level > 4)
    return;

  //1. divide up the domain into 8 and create 8 children nodes. put in the min, max, index. put the pointers of each children in the current_node->m_children 
  ccDOTIMES(z, 2) {
    ccDOTIMES(y, 2) {
      ccDOTIMES(x, 2) {
        uINT8 linear_index = z*4 + y*2 + x;
        sBOX_TREE_NODE new_child_node;
        new_child_node.m_index = m_nodes.size();
        new_child_node.m_level = current_node.m_level + 1;

        new_child_node.m_min[0] = current_node.m_min[0] +     x * 0.5 * ( current_node.m_max[0] - current_node.m_min[0] );
        new_child_node.m_min[1] = current_node.m_min[1] +     y * 0.5 * ( current_node.m_max[1] - current_node.m_min[1] );
        new_child_node.m_min[2] = current_node.m_min[2] +     z * 0.5 * ( current_node.m_max[2] - current_node.m_min[2] );

        new_child_node.m_max[0] = current_node.m_max[0] - (1-x) * 0.5 * ( current_node.m_max[0] - current_node.m_min[0] );
        new_child_node.m_max[1] = current_node.m_max[1] - (1-y) * 0.5 * ( current_node.m_max[1] - current_node.m_min[1] );
        new_child_node.m_max[2] = current_node.m_max[2] - (1-z) * 0.5 * ( current_node.m_max[2] - current_node.m_min[2] );

        m_nodes.push_back(new_child_node);
        current_node.m_children[linear_index] = m_nodes.back().m_index;
        m_nodes.at(current_node.m_index).m_children[linear_index] = m_nodes.back().m_index;
      }
    }
  }

  //2. run through all boxes in the vector current_node->m_ublk_boxes and copy them into the child node's m_ublk_boxes. At the end resize the current_node->m_ublk_boxes to zero.
  ccDOTIMES(i, current_node.m_ublk_boxes.size()) {
    sUBLK_BOX_HEADER_BASE* box_header = current_node.m_ublk_boxes.at(i);
    dFLOAT voxel_dx = sim_scale_to_voxel_size(box_header->m_scale);
    dFLOAT ivdx = 1.0 / voxel_dx;
    dFLOAT ublk_distance = 2.0 * ivdx;

    STP_COORD box_min[3];
    STP_COORD box_max[3];
    ccDOTIMES(axis, 3) {
      box_min[axis] = box_header->m_location[axis];
      box_max[axis] = box_header->m_location[axis] + ublk_distance * box_header->m_size[axis];
    }

    ccDOTIMES(child, 8) {
      sBOX_TREE_NODE child_node = m_nodes.at(current_node.m_children[child]);
      STP_COORD child_min[3];
      STP_COORD child_max[3];
      ccDOTIMES(axis, 3) {
        child_min[axis] = child_node.m_min[axis];
        child_max[axis] = child_node.m_max[axis];
      }

      bool is_inside = check_if_box_is_inside_child_domain(box_min, box_max, child_min, child_max);
      
      if (is_inside)
        m_nodes.at(child_node.m_index).m_ublk_boxes.push_back(box_header);
    }
  }

  //swap the m_ublk_boxes vector 
  std::vector<sUBLK_BOX_HEADER_BASE*>().swap(current_node.m_ublk_boxes);
  std::vector<sUBLK_BOX_HEADER_BASE*>().swap(m_nodes.at(current_node.m_index).m_ublk_boxes);

  //3. loop over each child node and call this function using the pointer to a child node
  ccDOTIMES(child, 8) {
    propogate_branch_until_leaf( m_nodes.at(current_node.m_children[child]) );
  }
    
}

BOOLEAN check_if_box_is_inside_child_domain(STP_COORD box_min[3], 
                                              STP_COORD box_max[3], 
                                              STP_COORD child_min[3], 
                                              STP_COORD child_max[3]) 
{
  bool found_inside = true;
  ccDOTIMES(axis, 3) {
    STP_COORD box_size[2]    = {box_min[axis], box_max[axis]};
    STP_COORD child_size[2]  = {child_min[axis], child_max[axis]};
    if ( !is_interval_overlapping(box_size, child_size)) {
      found_inside = false;
      break;
    }
  }

  return found_inside;
     
}

BOOLEAN is_interval_overlapping (STP_COORD box_size[2], STP_COORD domain_size[2]) 
{
  
  bool found_inside = (box_size[1] >= domain_size[0] && domain_size[1] >= box_size[0]); 

  return found_inside;
}

BOOLEAN is_point_inside_domain (dFLOAT loc[3], STP_COORD domain_min[3], STP_COORD domain_max[3]) 
{
  bool found_inside = true;
  ccDOTIMES(axis, 3) {
    if (loc[axis] > domain_max[axis] || loc[axis] < domain_min[axis]) {
      found_inside = false;
      break;
    }
  }
  
  return found_inside;
}

sBOX_TREE_NODE* sBOX_TREE::find_internal (dFLOAT loc[3], sBOX_TREE_NODE* current_node) 
{
  //0. is the size > 0, then it is the leaf node. can also check m_children
  if (current_node->m_ublk_boxes.size() > 0)
    return current_node;

  sBOX_TREE_NODE* leaf_node = nullptr;
  //1. search through the child_node domains and see where the loc is 
  ccDOTIMES(child, 8) {
    sBOX_TREE_NODE* child_node = &m_nodes.at(current_node->m_children[child]);
    bool found_inside = is_point_inside_domain (loc, child_node->m_min, child_node->m_max);
    if (found_inside) {
      leaf_node = find_internal(loc, child_node);
      break;
    }
  }

  return leaf_node;
}
