/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef UBLK_NEIGHBORS_H_
#define UBLK_NEIGHBORS_H_

#include "common_sp.h"
#include "shob.h"


template <class UBLK> struct sSCALE_INTERFACE_3D {
  sSCALE_INTERFACE_3D() {
    ccDOTIMES(i, 4) { face_neighbors[i] = NULL; } 
  }
  UBLK   face_neighbors[4];    /* by canonical order */ 
};


const asINT32 UBLK_NEIGHBOR_DEFAULT_UBLK_MASK    = 0;
const asINT32 UBLK_NEIGHBOR_SCALE_INTERFACE_MASK = 1;
const asINT32 UBLK_NEIGHBOR_VR_FINE_UBLK_MASK    = 2;
const asINT32 UBLK_NEIGHBOR_UBLK_ID_MASK         = 4;
const asINT32 UBLK_NEIGHBOR_ALL_MASK             = 7;

const asINT32 UBLK_NEIGHBOR_N_MASK_BITS          = 3;

/* VR_TOPOLOGY indicates the scale of the neighbor ublks that abut each face of
 * a particular ublk. It also indicates the voxel overlaid by the ublk at the
 * next coarser scale.
 */
typedef class __PACKED__ sVR_TOPOLOGY 
{
private:
  STP_FACE_MASK        mask;    /* Indicates which faces abut a different scale */
  STP_FACE_MASK        get_finer_p; /* Indicates which faces abut a finer scale */

public:
  sVR_TOPOLOGY() : mask(0), get_finer_p(0) {}

  auINT32 face_abuts_different_scale_mask()             { return mask; }
  auINT32 face_abuts_finer_scale_mask()                 { return get_finer_p; }

  BOOLEAN face_abuts_different_scale(asINT32 face)      { return (mask >> face) & 1; }
  BOOLEAN face_abuts_finer_scale(asINT32 face)          { return (get_finer_p >> face) & 1; }

  VOID add_face_to_different_scale_mask(asINT32 face)   { mask |= stp_face_to_mask(face); }
  VOID add_face_to_finer_scale_mask(asINT32 face)       { get_finer_p |= stp_face_to_mask(face); }

  /* COARSE_VOXEL indicates the voxel number for a particular ublk if that ublk is 
   * considered a voxel on the next coarser scale. It is not yet implemented. When
   * we do implement it, we probably want to pack the 2 6-bit masks above and a 3-bit
   * coarse voxel number into a single 16-bit word.
   */
  asINT32 coarse_voxel() 
    { msg_internal_error("vr_topology->coarse_voxel() not yet supported."); 
      return -1; 
    }


} *VR_TOPOLOGY;

INLINE __HOST__DEVICE__ asINT32 neighbor_voxel_along_state_direction(asINT32 voxel, asINT32 latvec)
{
  sINT16 v_offsets[3], c_offsets[3];

#if BUILD_D39_LATTICE
  c_offsets[0] = state_vx(latvec) > 1 ? 0 : state_vx(latvec);
  c_offsets[1] = state_vy(latvec) > 1 ? 0 : state_vy(latvec);
  c_offsets[2] = state_vz(latvec) > 1 ? 0 : state_vz(latvec);
#else
  c_offsets[0] = state_vx(latvec);
  c_offsets[1] = state_vy(latvec);
  c_offsets[2] = state_vz(latvec);
#endif

  v_offsets[0] = ((voxel >> 2) & 1) + c_offsets[0] + 1;
  v_offsets[1] = ((voxel >> 1) & 1) + c_offsets[1] + 1;
  v_offsets[2] = ((voxel     ) & 1) + c_offsets[2] + 1;
  asINT32 neighbor_voxel = (((v_offsets[0] + 1) & 1) << 2) +
                           (((v_offsets[1] + 1) & 1) << 1) +
                           ((v_offsets[2] + 1) & 1);
  return neighbor_voxel;
}
#endif /*UBLK_NEIGHBORS_H_*/
