/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "gpu_shobs.hcu"
#include "gpu_globals.hcu"
#include "shob_groups.h"
#include <cuda.h>
#include <cuda_runtime.h>

#include PHYSICS_H

#include <chrono>

using Clock = std::chrono::steady_clock;
using std::chrono::time_point;
using std::chrono::duration_cast;
using std::chrono::nanoseconds;

namespace GPU {

bool can_launch_optimized_ublk_dyn_kernel()
{
  return \
    //Incompressible (IC) branches in original fluid_dyn_ublk_1 deactivated
    !::sim.is_incompressible_solver &&
    //fractional advection related branches disabled
    g_adv_fraction == 1 &&
    //No acoustic porous media (tortuosity) or alt_temp_coupling to hijack states_pre slot in dcache
    //for storing N_eq_2. Otherwise states_pre slot is used in voxel_moments.h
    !(::sim.thermal_feedback_is_on && g_use_directional_grad) &&
    //No laplace solver support in fluid dynamics
    !g_laplace_solver_d19 &&
    //No laplace solver support
    !g_meas_seed_pres_d19 &&
    //No particle modelling
    !::sim.is_particle_model &&
    //Hydrogen eos branches disabled that are not under IS_HT template param
    !::sim.is_hydrogen_eos &&
    //dyn based visc assumption in optimized turb solver
    g_is_dyn_base_visc_on &&
    //melting solver branches not under a template param removed in
    //optimized turb solver
    !::sim.use_melting_solver &&
    //Super cycling related branches removed in optimized turb solver
    !::sim.is_ke_super_cycling &&
    //pfluid scaled vel gradients assumed in optimized turb solver
    g_use_pfluid_in_grad_vel &&
    //No RT gamma swirl assumed in optimized turb solver
    !g_RT_gamma_swirl &&
    //standard rng assumed in optimized turb solver
    g_standard_rng &&
    //no menter limiter in optimized turb solver
    !g_menter_pk_limiter &&
    //ke damping assumed in optimized turb solver
    g_use_ke_damping &&
    //helicity assumed
    g_is_thr_hel;
}

cSHOB_DYN_AND_MEAS_RUNNER::cSHOB_DYN_AND_MEAS_RUNNER(size_t max_shobs_per_kernel_call,
                                                     size_t start_shob_idx,
                                                     size_t n_shobs_to_run) :
  m_max_shobs_per_kernel_call(max_shobs_per_kernel_call),
  m_start_shob_idx(start_shob_idx),
  m_n_shobs_to_run(n_shobs_to_run),
  m_current_batch(0)
{
  m_n_batches = (m_n_shobs_to_run / m_max_shobs_per_kernel_call);
  if ( m_n_shobs_to_run % m_max_shobs_per_kernel_call != 0) {
    m_n_batches++;
  }
}

sBATCH_INFO cSHOB_DYN_AND_MEAS_RUNNER::next_batch() {
  if (m_current_batch == m_n_batches) {
    msg_internal_error("Tried to process too many batchs");
  }

  const size_t shob_offset = m_start_shob_idx + m_current_batch*m_max_shobs_per_kernel_call;
  const size_t n_shobs = [&]() {
    // this is the last block to run
    if (m_current_batch == m_n_batches-1) {
      size_t end_idx = m_start_shob_idx + m_n_shobs_to_run;
      return end_idx - shob_offset;
    }
    else {
      return m_max_shobs_per_kernel_call;
    }
  }();

  auto cur_batch = m_current_batch;
  m_current_batch++;

  return sBATCH_INFO{cur_batch, n_shobs, shob_offset};
}

VOID ublk_dynamics_no_ht_no_ic(UBLK_GROUP_TYPE group_type,
                               const sBATCH_INFO& info,
                               FLUID_DYN_DCACHE h_dcache,
                               bool has_special_fluid);

void do_ublk_measurements(UBLK_GROUP_TYPE group_type, const sBATCH_INFO& batch);

/* @fcn ublk_dynamics
 * This a light wrapper over different dynamics kernel implementations
 * The NVCC compiler seems to rely heavily on the kernel launch code and
 * the kernel source code being present in the same translation unit, to
 * efficiently allocate registers and stack memory. Therefore the kernels
 * are launched in the PHYSICS component and not here in SIMENG
 */
VOID ublk_dynamics(UBLK_GROUP_TYPE group_type,
                   STP_SCALE scale,
                   const size_t* range,
                   FLUID_DYN_DCACHE h_dcache,
                   bool doMeasurements,
                   bool has_special_fluid) {

  /* Before we call the kernel's we must copy the host's version of the
   * dcache per scale data to GPU::g_dcache_per_scale in constant memory.
   * Despite the 'Async' name, the solvers will not run until this copy 
   * is finished because they are using the same 'stream'
   */
  checkCudaErrors( cudaMemcpyToSymbolAsync(GPU::g_dcache_per_scale, &::g_dcache_per_scale,
                                           sizeof(sFLUID_DYN_DCACHE_PER_SCALE_DATA), 0 /*offset*/,
                                           cudaMemcpyHostToDevice,
                                           g_stream) );

  size_t n_dyn_ublks = range[1] - range[0];

  auto runner = GPU::g_shob_dyn_and_meas.ublk_runner(range[0],n_dyn_ublks);
  size_t n_batches = runner.n_batches();

  ccDOTIMES(i, n_batches) {
    const sBATCH_INFO batch = runner.next_batch();
    BOOLEAN is_body_force = FALSE;
    if (::sim.is_heat_transfer || ::sim.is_scalar_model) {
      msg_error("This simulation type is currently not supported on GPUs.");
    } else if (!is_body_force && !h_dcache->c()->is_incompressible_solver) {
      ublk_dynamics_no_ht_no_ic(group_type, batch, h_dcache, has_special_fluid);
    } else { 
      msg_error("This simulation type is currently not supported on GPUs.");
    }  

      if (doMeasurements) {
         do_ublk_measurements(group_type, batch);
    }
  }
}


template<typename DCACHE_TYPE>
__HOST__ static _ALWAYS_INLINE_
VOID launch_ublk_dyn_kernel_with_solver_mask(const sBATCH_INFO& batch,
                                             UBLK_GROUP_TYPE group_type)
{

  ACTIVE_SOLVER_MASK active_solver_mask = ::g_dcache_per_scale.active_solver_mask;
  size_t n_dyn_ublks = batch.n_shobs;

  switch (active_solver_mask) {
  case LB_ACTIVE | KE_PDE_ACTIVE:
    {
      constexpr auto SOLVER_MASK = LB_ACTIVE | KE_PDE_ACTIVE;
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case KE_PDE_ACTIVE:
    {
      constexpr auto SOLVER_MASK = LB_ACTIVE | KE_PDE_ACTIVE;
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = LB_ACTIVE | KE_PDE_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case LB_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = LB_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case LB_ACTIVE | KE_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = LB_ACTIVE | KE_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case T_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = T_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case KE_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = KE_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  case KE_PDE_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS):
    {
      constexpr auto SOLVER_MASK = KE_PDE_ACTIVE | T_PDE_ACTIVE | (1 << N_SOLVERS);
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  default:
    {
      constexpr auto SOLVER_MASK = DEFAULT_ACTIVE_SOLVER_MASK;
      launch_ublk_dyn_kernel_with_solver_mask_and_dcache_type<SOLVER_MASK, DCACHE_TYPE>
        (group_type, n_dyn_ublks, batch.shob_offset);
      break;
    }
  }
}

__HOST__ static _ALWAYS_INLINE_
VOID launch_ublk_dyn_kernel(const sBATCH_INFO& batch,
                            UBLK_GROUP_TYPE group_type,
                            bool group_has_special_fluid)
{

  using ::sim;
  auto& simc = *sim.c();

  BOOLEAN is_entropy_on = (simc.T_solver_type == PDE_ENTROPY) || (simc.T_solver_type == LB_ENTROPY);
  BOOLEAN is_uds_lb_on = (simc.uds_solver_type == LB_UDS);


  if (simc.enable_dcache_pm_storage && group_has_special_fluid) {
    if (!is_uds_lb_on) {
      if (is_entropy_on) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_ENTROPY_PM>(batch, group_type);
      } else if (simc.is_turb_model) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_TURB_PM>(batch, group_type);
      } else {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_DNS_PM>(batch, group_type);
      }
    } else { //if (is_uds_lb_on)
      if (is_entropy_on) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_ENTROPY_UDS_PM>(batch, group_type);
      } else if (simc.is_turb_model) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_UDS_TURB_PM>(batch, group_type);
      } else {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_UDS_DNS_PM>(batch, group_type);
      }
    }
  } else {
    if (!is_uds_lb_on) {
      if (is_entropy_on) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_ENTROPY_NO_PM>(batch, group_type);
      } else if (simc.is_turb_model) {
        if (simc.is_heat_transfer) {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_TURB>(batch, group_type);
        } else {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_NO_HT_TURB>(batch, group_type);
        }
      } else {
        if (simc.is_heat_transfer) {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_DNS>(batch, group_type);
        } else {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_NO_HT_DNS>(batch, group_type);
        }
      }
    } else { //if (is_uds_lb_on)
      if (is_entropy_on) {
        launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_ENTROPY_UDS_NO_PM>(batch, group_type);
      } else if (simc.is_turb_model) {
        if (simc.is_heat_transfer) {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_UDS_TURB>(batch, group_type);
        } else {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_NO_HT_UDS_TURB>(batch, group_type);
        }
      } else {
        if (simc.is_heat_transfer) {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_HT_UDS_DNS>(batch, group_type);
        } else {
          launch_ublk_dyn_kernel_with_solver_mask<sFLUID_DYN_DCACHE_NO_HT_UDS_DNS>(batch, group_type);
        }
      }
    }
  }
}

VOID ublk_dynamics_no_ht_no_ic(UBLK_GROUP_TYPE group_type,
                               const sBATCH_INFO& batch,
                               FLUID_DYN_DCACHE h_dcache,
                               bool has_special_fluid) {
  /* Before we call the kernel's we must copy the host's version of the
   * dcache per scale data to GPU::g_dcache_per_scale in constant memory
   */
  launch_ublk_dyn_kernel(batch,group_type, has_special_fluid);

  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(g_stream) );
}

}//namespace GPU
