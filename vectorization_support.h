/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("physics.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Physics Modules                                   ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1995-2000 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~- boxcomment("physics.copyright", "78") */ 

#ifndef _SIMENG_SSE_H
#define _SIMENG_SSE_H

#include "common_sp.h"
#include "lattice.h"

#if BUILD_DOUBLE_PRECISION
#define ffrec(func) func 
#else
#define ffrec(func) func## f
#endif

#if EXA_USE_AVX
#include <immintrin.h>
#elif EXA_USE_SSE
#include <xmmintrin.h>
#include <emmintrin.h>
#endif

#if defined(__clang__) || defined(__GNUC__)
// This is part of the intel immintrin defines
#define __ICL_INTRINNCC
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
__m256d _mm256_log_pd(__m256d a);
__m256d _mm256_exp_pd(__m256d a);
__m256d _mm256_acos_pd(__m256d a);
__m256d _mm256_cos_pd(__m256d a);
__m256d _mm256_asin_pd(__m256d a);
__m256d _mm256_tan_pd(__m256d a);
__m256d _mm256_tanh_pd(__m256d a);
__m256d _mm256_sin_pd(__m256d a);
__m256d _mm256_pow_pd(__m256d a, __m256d b);

extern "C" {
  __m256d __ICL_INTRINNCC __svml_log4(__m256d);
  __m256d __ICL_INTRINNCC __svml_exp4(__m256d);
  __m256d __ICL_INTRINNCC __svml_acos4(__m256d);
  __m256d __ICL_INTRINNCC __svml_cos4(__m256d);
  __m256d __ICL_INTRINNCC __svml_asin4(__m256d);
  __m256d __ICL_INTRINNCC __svml_tan4(__m256d);
  __m256d __ICL_INTRINNCC __svml_tanh4(__m256d);
  __m256d __ICL_INTRINNCC __svml_sin4(__m256d);
  __m256d __ICL_INTRINNCC __svml_sqrt4(__m256d);
  __m256d __ICL_INTRINNCC __svml_pow4(__m256d, __m256d);
}
#else
__m256 _mm256_log_ps(__m256 a);
__m256 _mm256_exp_ps(__m256 a);
__m256 _mm256_acos_ps(__m256 a);
__m256 _mm256_cos_ps(__m256 a);
__m256 _mm256_asin_ps(__m256 a);
__m256 _mm256_tan_ps(__m256 a);
__m256 _mm256_tanh_ps(__m256 a);
__m256 _mm256_sin_ps(__m256 a);
__m256 _mm256_pow_ps(__m256 a, __m256 b);

extern "C" {
  __m256 __ICL_INTRINNCC __svml_logf8(__m256);
  __m256 __ICL_INTRINNCC __svml_expf8(__m256);
  __m256 __ICL_INTRINNCC __svml_acosf8(__m256);
  __m256 __ICL_INTRINNCC __svml_cosf8(__m256);
  __m256 __ICL_INTRINNCC __svml_asinf8(__m256);
  __m256 __ICL_INTRINNCC __svml_tanf8(__m256);
  __m256 __ICL_INTRINNCC __svml_tanhf8(__m256);
  __m256 __ICL_INTRINNCC __svml_sinf8(__m256);
  __m256 __ICL_INTRINNCC __svml_sqrtf8(__m256);
  __m256 __ICL_INTRINNCC __svml_powf8(__m256, __m256);
}
#endif
#elif EXA_USE_SSE    
#if BUILD_DOUBLE_PRECISION
__m128d _mm_log_pd(__m128d a);
__m128d _mm_exp_pd(__m128d a);
__m128d _mm_acos_pd(__m128d a);
__m128d _mm_cos_pd(__m128d a);
__m128d _mm_asin_pd(__m128d a);
__m128d _mm_tan_pd(__m128d a);
__m128d _mm_tanh_pd(__m128d a);
__m128d _mm_sin_pd(__m128d a);
__m128d _mm_pow_pd(__m128d a, __m128d b);
extern "C" {
  __m128d __ICL_INTRINNCC __svml_log2(__m128d);
  __m128d __ICL_INTRINNCC __svml_exp2(__m128d);
  __m128d __ICL_INTRINNCC __svml_acos2(__m128d);
  __m128d __ICL_INTRINNCC __svml_cos2(__m128d);
  __m128d __ICL_INTRINNCC __svml_asin2(__m128d);
  __m128d __ICL_INTRINNCC __svml_tan2(__m128d);
  __m128d __ICL_INTRINNCC __svml_tanh2(__m128d);
  __m128d __ICL_INTRINNCC __svml_sin2(__m128d);
  __m128d __ICL_INTRINNCC __svml_sqrt2(__m128d);
  __m128d __ICL_INTRINNCC __svml_pow2(__m128d, __m128d);
}
#else
__m128 _mm_log_ps(__m128 a);
__m128 _mm_exp_ps(__m128 a);
__m128 _mm_acos_ps(__m128 a);
__m128 _mm_cos_ps(__m128 a);
__m128 _mm_asin_ps(__m128 a);
__m128 _mm_tan_ps(__m128 a);
__m128 _mm_tanh_ps(__m128 a);
__m128 _mm_sin_ps(__m128 a);
__m128 _mm_pow_ps(__m128 a, __m128 b);
extern "C" {
  __m128 __ICL_INTRINNCC __svml_logf4(__m128);
  __m128 __ICL_INTRINNCC __svml_expf4(__m128);
  __m128 __ICL_INTRINNCC __svml_acosf4(__m128);
  __m128 __ICL_INTRINNCC __svml_cosf4(__m128);
  __m128 __ICL_INTRINNCC __svml_asinf4(__m128);
  __m128 __ICL_INTRINNCC __svml_tanf4(__m128);
  __m128 __ICL_INTRINNCC __svml_tanhf4(__m128);
  __m128 __ICL_INTRINNCC __svml_sinf4(__m128);
  __m128 __ICL_INTRINNCC __svml_sqrtf4(__m128);
  __m128 __ICL_INTRINNCC __svml_powf4(__m128, __m128);
}
#endif
#endif
#endif

/** Defines constants for a specific size ublk. */
template<size_t N_VOXELS_> struct ubFLOAT_TRAITS;

#define EXA_USE_VEC ((EXA_USE_AVX || EXA_USE_SSE) && !(DEVICE_COMPILATION_MODE))

#if BUILD_GPU && GPU_COMPILER

#define M_DATA_TYPE sdFLOAT
#define pprec(func) func
#define N_VOXOR_VOXELS 1
#define N_VOXOR_VOXEL_BITS 0
#define VOXOR_VOXEL_MASK 0x63

template<>
struct ubFLOAT_TRAITS<1> {
  enum {
    N_VOXELS = 1,
    N_VOXELS_2D = 1,
    N_VOXELS_1D = 1,
    N_UBLKS = 0,
    N_UBLKS_1D = 0,
    VOXOR_MASK = 0x1,
    NEIGHBOR_VOXOR_MASK = 0x1,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};

//All though this trait is never really used in GPU code
//it is still needed to instantiate templates in header files
//that are releveant to CPU code.
template<>
struct ubFLOAT_TRAITS<N_VOXELS_8> {
  enum {
    N_VOXELS = 8,
    N_VOXELS_2D = 4,
    N_VOXELS_1D = 2,
    N_UBLKS = 1,
    N_UBLKS_1D = 1,
    VOXOR_MASK = 0x1,
    NEIGHBOR_VOXOR_MASK = 0x4,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};

template<>
struct ubFLOAT_TRAITS<N_VOXELS_64> {
  enum {
    N_VOXELS = 64,
    N_VOXELS_2D = 16,
    N_VOXELS_1D = 4,
    N_UBLKS = 8,
    N_UBLKS_1D = 2,
    VOXOR_MASK = 0x1,
    //NEIGHBOR_VOXOR_MASK is used in the context of looking up child UBLK neighboring
    //voxors in vx_neighbor_voxor_along_axis, and since N_VOXOR_VOXELS is 1 for GPUs, the mask
    //be identical to that used in the scalar CPU code
    NEIGHBOR_VOXOR_MASK = 0x4,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};

#elif EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
#define M_DATA_TYPE __m256d
#define pprec(func) _mm256_## func## _pd

#define N_VOXOR_VOXELS 4
#define N_VOXOR_VOXEL_BITS 2
#define VOXOR_VOXEL_MASK 0x3
// #define VOXOR_MASK 0x0F
// #define VOXOR_POS_X_MASK VOXOR_MASK
// #define VOXOR_NEG_X_MASK VOXOR_MASK
// #define VOXOR_POS_Y_MASK 0x0C
// #define VOXOR_NEG_Y_MASK 0x03
// #define VOXOR_POS_Z_MASK 0x0A
// #define VOXOR_NEG_Z_MASK 0x05
// #define NEIGHBOR_VOXOR_MASK 0x01  

// Need Sam's help to generalize this I believe we will have to replace most of
// these masks and hardcoded values with functions, because with Mublks, the
// masks are no longer symmetric

template<>
struct ubFLOAT_TRAITS<N_VOXELS_8>
{
  enum {
    N_VOXELS = 8,
    N_VOXELS_2D = 4,
    N_VOXELS_1D = 2,
    N_UBLKS = 1,
    N_UBLKS_1D = 1,
    VOXOR_MASK = 0x0F,
    VOXOR_POS_X_MASK = VOXOR_MASK,
    VOXOR_NEG_X_MASK = VOXOR_MASK,
    VOXOR_POS_Y_MASK = 0x0C,
    VOXOR_NEG_Y_MASK = 0x03,
    VOXOR_POS_Z_MASK = 0x0A,
    VOXOR_NEG_Z_MASK = 0x05,
    NEIGHBOR_VOXOR_MASK = 0x01,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};

#else

#define M_DATA_TYPE __m256
#define pprec(func) _mm256_## func## _ps

#define N_VOXOR_VOXELS 8
#define N_VOXOR_VOXEL_BITS 3
#define VOXOR_VOXEL_MASK 0x7
#define M_DATA_TYPE __m256
// #define VOXOR_MASK 0xFF
// #define VOXOR_POS_X_MASK 0xF0
// #define VOXOR_NEG_X_MASK 0x0F
// #define VOXOR_POS_Y_MASK 0xCC
// #define VOXOR_NEG_Y_MASK 0x33
// #define VOXOR_POS_Z_MASK 0xAA
// #define VOXOR_NEG_Z_MASK 0x55
// #define NEIGHBOR_VOXOR_MASK 0x00 

template<>
struct ubFLOAT_TRAITS<N_VOXELS_8>
{
  enum {
    N_VOXELS = 8,
    N_VOXELS_2D = 4,
    N_VOXELS_1D = 2,
    N_UBLKS = 1,
    N_UBLKS_1D = 1,
    VOXOR_MASK=  0xFF,
    VOXOR_POS_X_MASK=  0xF0,
    VOXOR_NEG_X_MASK=  0x0F,
    VOXOR_POS_Y_MASK=  0xCC,
    VOXOR_NEG_Y_MASK=  0x33,
    VOXOR_POS_Z_MASK=  0xAA,
    VOXOR_NEG_Z_MASK=  0x55,
    NEIGHBOR_VOXOR_MASK=  0x00,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};

template<>
struct ubFLOAT_TRAITS<N_VOXELS_64>
{
  enum {
    N_VOXELS = 64,
    N_VOXELS_2D = 16,
    N_VOXELS_1D = 4,
    N_UBLKS = 8,
    N_UBLKS_1D = 2,
    VOXOR_MASK = 0x0F,
    VOXOR_POS_X_MASK = VOXOR_MASK,
    VOXOR_NEG_X_MASK = VOXOR_MASK,
    VOXOR_POS_Y_MASK = 0x0C,
    VOXOR_NEG_Y_MASK = 0x03,
    VOXOR_POS_Z_MASK = 0x0A,
    VOXOR_NEG_Z_MASK = 0x05,
    NEIGHBOR_VOXOR_MASK = 0x01,
    N_VOXORS = N_VOXELS / N_VOXOR_VOXELS
  };
};
#endif
#elif EXA_USE_SSE    
#if BUILD_DOUBLE_PRECISION
#define N_VOXOR_VOXELS 2
#define N_VOXOR_VOXEL_BITS 1
#define VOXOR_VOXEL_MASK 0x1
#define pprec(func)  _mm_## func## _pd
#define M_DATA_TYPE __m128d
#define VOXOR_MASK 0x3
#define VOXOR_POS_X_MASK VOXOR_MASK
#define VOXOR_NEG_X_MASK VOXOR_MASK
#define VOXOR_POS_Y_MASK VOXOR_MASK
#define VOXOR_NEG_Y_MASK VOXOR_MASK
#define VOXOR_POS_Z_MASK 0x2
#define VOXOR_NEG_Z_MASK 0x1
#define NEIGHBOR_VOXOR_MASK 0x2
#else
#define N_VOXOR_VOXELS 4
#define N_VOXOR_VOXEL_BITS 2
#define VOXOR_VOXEL_MASK 0x3
#define pprec(func)  _mm_## func## _ps
#define M_DATA_TYPE __m128
#define VOXOR_MASK 0xF
#define VOXOR_POS_X_MASK VOXOR_MASK
#define VOXOR_NEG_X_MASK VOXOR_MASK
#define VOXOR_POS_Y_MASK 0xC
#define VOXOR_NEG_Y_MASK 0x3
#define VOXOR_POS_Z_MASK 0xA
#define VOXOR_NEG_Z_MASK 0x5
#define NEIGHBOR_VOXOR_MASK 0x1
#endif
#else       // no vectorization 
#define N_VOXOR_VOXELS 1
#define VOXOR_VOXEL_MASK 0x7
#define NEIGHBOR_VOXOR_MASK 0x4
#endif

#if EXA_USE_VEC

class vxBOOLEAN;
class vxFLOAT;

class ALIGN_VECTOR vxFLOAT_BASE {
 protected:
  M_DATA_TYPE m_4f;
  
 public:
  M_DATA_TYPE get() {
    return(m_4f);
  }

  vxFLOAT_BASE& operator = (const vxFLOAT& source);

  sdFLOAT& operator[] (int index) {
    return (((sdFLOAT *)&m_4f)[index]);
  }

  const sdFLOAT& operator[] (int index) const {
    return (((const sdFLOAT *)&m_4f)[index]);
  }

  vxFLOAT_BASE operator += (vxFLOAT_BASE addend) {
    m_4f = pprec(add)(m_4f, addend.m_4f);
    return(*this);
  }

  vxFLOAT_BASE operator += (sdFLOAT addend) {
    m_4f = pprec(add)(m_4f, pprec(set1)(addend));
    return(*this);
  }

  vxFLOAT_BASE operator -= (vxFLOAT_BASE subtrahend) {
    m_4f = pprec(sub)(m_4f, subtrahend.m_4f);
    return(*this);
  }
  vxFLOAT_BASE operator -= (sdFLOAT subtrahend) {
    m_4f = pprec(sub)(m_4f, pprec(set1)(subtrahend));
    return(*this);
  }

  vxFLOAT_BASE operator *= (vxFLOAT_BASE multiplier) {
    m_4f = pprec(mul)(m_4f, multiplier.m_4f);
    return(*this);
  }

  vxFLOAT_BASE operator *= (sdFLOAT multiplier) {
    m_4f = pprec(mul)(m_4f, pprec(set1)(multiplier));
    return(*this);
  }

  vxFLOAT_BASE operator /= (vxFLOAT_BASE divisor) {
    m_4f = pprec(div)(m_4f, divisor.m_4f);
    return(*this);
  }

  vxFLOAT_BASE operator /= (sdFLOAT divisor) {
    m_4f = pprec(div)(m_4f, pprec(set1)(divisor));
    return(*this);
  }

  friend VOID ubstore(vxFLOAT_BASE &dest, vxFLOAT source, auINT32 vmask);
  friend VOID cstore(vxFLOAT_BASE &dest, vxFLOAT source, auINT32 vmask, vxBOOLEAN mask);
  friend VOID cstore(vxFLOAT_BASE &dest, vxFLOAT source, vxBOOLEAN mask);
  friend VOID cset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN mask);
  friend VOID tset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN test);
  friend VOID blend(vxFLOAT_BASE &target, vxFLOAT source, auINT32 mask);
   
  friend vxFLOAT_BASE vx_forward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);
  friend vxFLOAT_BASE vx_backward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);
  friend vxFLOAT_BASE vx_forward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);
  friend vxFLOAT_BASE vx_backward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);
  friend vxFLOAT_BASE vx_forward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);
  friend vxFLOAT_BASE vx_backward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent);

  INLINE friend std::ostream& operator << (std::ostream& os, const vxFLOAT_BASE& f) {
    const auto* array = reinterpret_cast<const sdFLOAT*>(&(f.m_4f));   
    for(int i=0; i<N_VOXOR_VOXELS; i++) {
      os << array[i] << ' ';
    }
    return os;
  }
};



class ALIGN_VECTOR vxFLOAT : public vxFLOAT_BASE {
 public:

  vxFLOAT() {
    // Do nothing - we don't expect numeric types to be initialized
  }

  vxFLOAT(const M_DATA_TYPE &finit) {
    m_4f = pprec(load)((sdFLOAT *)&finit);
  }

  vxFLOAT(sdFLOAT source) {
    m_4f = pprec(set1)(source);
  }

  vxFLOAT(vxFLOAT_BASE source) {
    //m_4f = pprec(load)((sdFLOAT *)&source);
    m_4f = source.get();
  }

  // NB - this constructor uses an aligned load and will take a bus error if called with an unaligned address

  vxFLOAT(sdFLOAT *source) {
    m_4f = pprec(load)(source);
  }

  friend vxBOOLEAN operator == (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN operator != (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN operator > (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN operator >= (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN operator < (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN operator <= (vxFLOAT a, vxFLOAT b);
 
  friend vxFLOAT vxand (vxFLOAT a, vxBOOLEAN b);
  friend vxFLOAT vxor (vxFLOAT a, vxFLOAT b);
  friend vxBOOLEAN vxnot (vxBOOLEAN a);

  friend VOID ubstore(sdFLOAT &dest, vxFLOAT source, auINT32 vmask);
  friend VOID ubstore(sdFLOAT *dest, vxFLOAT source, auINT32 vmask);
  friend VOID ubstore(vxFLOAT_BASE &dest, vxFLOAT source, auINT32 vmask);
  friend VOID cstore(vxFLOAT_BASE &dest, vxFLOAT source, auINT32 vmask, vxBOOLEAN mask);
  friend VOID cstore(vxFLOAT_BASE &dest, vxFLOAT source, vxBOOLEAN mask);

  friend VOID cset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN mask);
  friend VOID tset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN test);
  //friend VOID blend(vxFLOAT_BASE &target, vxFLOAT source, auINT32 mask);
  
  friend vxFLOAT sqrt(vxFLOAT a);
  friend vxFLOAT vxexp(vxFLOAT a);
  friend vxFLOAT vxlog(vxFLOAT a);
  friend vxFLOAT vxsin(vxFLOAT a);
  friend vxFLOAT vxcos(vxFLOAT a);
  friend vxFLOAT vxtanh(vxFLOAT a);
  friend vxFLOAT vxacos(vxFLOAT a);
  friend vxFLOAT vxpow(vxFLOAT a, sdFLOAT b);
  friend vxFLOAT vxmax(vxFLOAT a,vxFLOAT b);
  friend vxFLOAT vxmin(vxFLOAT a,vxFLOAT b);
  friend vxFLOAT vxfabs(vxFLOAT a);
  friend vxBOOLEAN is_vxfloat_nan(vxFLOAT a);
  friend vxBOOLEAN isfinite (vxFLOAT a);
};

_INLINE_ vxFLOAT_BASE& vxFLOAT_BASE::operator = (const vxFLOAT& source) {
  //pprec(store)((sdFLOAT *)&m_4f, source.m_4f);
  m_4f = source.m_4f;
  return (*this);   // Return a reference to myself.
}

class ALIGN_VECTOR vxBOOLEAN : public vxFLOAT {
 public:

  vxBOOLEAN() : vxFLOAT() {}

  vxBOOLEAN(const M_DATA_TYPE &finit) : vxFLOAT(finit){}

  vxBOOLEAN(const sdFLOAT* finit) : vxFLOAT(const_cast<sdFLOAT*>(finit)){}

  vxBOOLEAN(sdFLOAT source) : vxFLOAT(source) {}
  
  vxBOOLEAN(vxFLOAT source) : vxFLOAT(source) {}

 friend auINT32 vx_mask_test(vxBOOLEAN vx_predicate, auINT32 mask);
 friend VOID cset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN mask);
 friend VOID tset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN test);
 friend vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask);
 friend vxBOOLEAN operator || (vxBOOLEAN a, vxBOOLEAN b);
 friend vxBOOLEAN operator && (vxBOOLEAN a, vxBOOLEAN b);
 friend auINT32 vx2intmask(vxBOOLEAN a);
};

template<size_t N_VOXELS_>
class tubFLOAT {

public:
  using sTRAITS = ubFLOAT_TRAITS<N_VOXELS_>;
  enum {
    N_VOXELS = sTRAITS::N_VOXELS,
    N_UBLKS  = sTRAITS::N_UBLKS,
    N_VOXELS_2D = sTRAITS::N_VOXELS_2D,
    N_VOXELS_1D = sTRAITS::N_VOXELS_1D,
    N_UBLKS_1D = sTRAITS::N_UBLKS_1D,
    VOXOR_MASK = sTRAITS::VOXOR_MASK,
    VOXOR_POS_X_MASK = sTRAITS::VOXOR_POS_X_MASK,
    VOXOR_NEG_X_MASK = sTRAITS::VOXOR_NEG_X_MASK,
    VOXOR_POS_Y_MASK = sTRAITS::VOXOR_POS_Y_MASK,
    VOXOR_NEG_Y_MASK = sTRAITS::VOXOR_NEG_Y_MASK,
    VOXOR_POS_Z_MASK = sTRAITS::VOXOR_POS_Z_MASK,
    VOXOR_NEG_Z_MASK = sTRAITS::VOXOR_NEG_Z_MASK,
    NEIGHBOR_VOXOR_MASK = sTRAITS::NEIGHBOR_VOXOR_MASK,
    N_VOXORS = sTRAITS::N_VOXORS
  };

  using SCALAR_TYPE = sdFLOAT[N_VOXELS];

 private:
 
  vxFLOAT_BASE m_v[N_VOXORS];
  
 public:

  vxFLOAT_BASE& operator[] (asINT32 index) {
    return(m_v[index]);
  }

  const vxFLOAT_BASE& operator[] (asINT32 index) const {
    return(m_v[index]);
  }
	      
  // C++ function call operator overloading ! Don't try this at home, kids !

  sdFLOAT& operator() (asINT32 voxel) {
    asINT32 voxor = voxel >> N_VOXOR_VOXEL_BITS;
    asINT32 voxor_voxel = voxel & VOXOR_VOXEL_MASK;
    return(m_v[voxor][voxor_voxel]);
  }

  const sdFLOAT& operator() (asINT32 voxel) const {
    asINT32 voxor = voxel >> N_VOXOR_VOXEL_BITS;
    asINT32 voxor_voxel = voxel & VOXOR_VOXEL_MASK;
    return(m_v[voxor][voxor_voxel]);
  }

  const sdFLOAT * as_scalar() const {
    return reinterpret_cast<const sdFLOAT *>(this);
  }

};

using ubFLOAT = tubFLOAT<N_VOXELS_8>;

_INLINE_ vxFLOAT operator + (vxFLOAT augend, vxFLOAT addend)
{
  vxFLOAT sum = augend;
  return sum += addend;
}

_INLINE_ vxFLOAT operator + (sdFLOAT augend, vxFLOAT addend)
{
  vxFLOAT sum = augend;
  return sum += addend;
}

_INLINE_ vxFLOAT operator + (vxFLOAT augend, sdFLOAT addend)
{
  vxFLOAT sum = augend;
  return sum += addend;
} 

_INLINE_ vxFLOAT operator - (vxFLOAT minuend, vxFLOAT subtrahend)
{
  vxFLOAT difference = minuend;
  return difference -= subtrahend;
}

_INLINE_ vxFLOAT operator - (sdFLOAT minuend, vxFLOAT subtrahend)
{
  vxFLOAT difference = minuend;
  return difference -= subtrahend;
}

_INLINE_ vxFLOAT operator - (vxFLOAT minuend, sdFLOAT subtrahend)
{
  vxFLOAT difference = minuend;
  return difference -= subtrahend;
} 

_INLINE_ vxFLOAT operator * (vxFLOAT multiplicand, vxFLOAT multiplier)
{
  vxFLOAT product = multiplicand;
  return product *= multiplier;
}

_INLINE_ vxFLOAT operator * (vxFLOAT multiplicand, sdFLOAT multiplier)
{
  vxFLOAT product = multiplicand;
  return product *= multiplier;
}

_INLINE_ vxFLOAT operator * (sdFLOAT multiplicand, vxFLOAT multiplier)
{
  vxFLOAT product = multiplicand;
  return product *= multiplier;
}

/* The following is useful in FOREACH macros */

_INLINE_ vxFLOAT operator * (int multiplicand, vxFLOAT multiplier)
{
  if(multiplicand == 0) {
    return 0.0; 
  }
  else if(multiplicand == 1)                                              
    return(multiplier);
  else if(multiplicand == -1) 
    return(0.0 - multiplier);
  else if(multiplicand == 2)                                              
    return(multiplier + multiplier);
  else if(multiplicand == -2)                                              
    return(0.0 - multiplier - multiplier);
  else if(multiplicand >= -9 && multiplicand <= 9)
    return multiplier * (sdFLOAT)multiplicand;
  else {
    msg_internal_error("vxFLOAT operator * (int a, vxFLOAT b) used with an unsupported int constant");
    return 0.0;
  }
}

_INLINE_ vxFLOAT operator / (vxFLOAT dividend, vxFLOAT divisor)
{
  vxFLOAT quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxFLOAT operator / (sdFLOAT dividend, vxFLOAT divisor)
{
  vxFLOAT quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxFLOAT operator / (vxFLOAT dividend, sdFLOAT divisor)
{
  vxFLOAT quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxBOOLEAN operator == (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_EQ_OS);
  #else
  return pprec(cmpeq)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator != (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_NEQ_OS);
  #else
  return pprec(cmpneq)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator > (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_GT_OS);
  #else
  return pprec(cmpgt)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator >= (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_GE_OS);
  #else
  return pprec(cmpge)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator < (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_LT_OS);
  #else
  return pprec(cmplt)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator <= (vxFLOAT a, vxFLOAT b)
{
  #if EXA_USE_AVX
  return pprec(cmp)(a.m_4f, b.m_4f, _CMP_LE_OS);
  #else
  return pprec(cmple)(a.m_4f, b.m_4f);
  #endif
}

_INLINE_ vxBOOLEAN operator || (vxBOOLEAN a, vxBOOLEAN b)
{
  return pprec(or)(a.m_4f, b.m_4f);
}

_INLINE_ vxBOOLEAN operator && (vxBOOLEAN a, vxBOOLEAN b)
{
  return pprec(and)(a.m_4f, b.m_4f);
}

_INLINE_ vxFLOAT vxand (vxFLOAT a, vxBOOLEAN b)
{
  return pprec(and)(a.m_4f, b.m_4f);
}

_INLINE_ vxFLOAT vxor (vxFLOAT a, vxFLOAT b)
{
  return pprec(or)(a.m_4f, b.m_4f);
}

_INLINE_ vxBOOLEAN vxsignbit(vxFLOAT or_states)
{
  return or_states;
}

_INLINE_ vxBOOLEAN vxnot (vxBOOLEAN a)
{
  vxBOOLEAN b = (a == a);  // a == a is the way to yield all 1 bits
  return b;
}

class vxV3 {

 private:

  vxFLOAT m_v4f[3];

 public:

  vxV3() {
    //Do nothing - we don't expect numeric types to be initialized
  }

  vxV3(sdFLOAT source) {
    m_v4f[0] = source;
    m_v4f[1] = source;
    m_v4f[2] = source;
  }

  vxFLOAT& operator[] (int index) {
    return(m_v4f[index]);
  }

  vxV3 operator *=  (vxV3 multiplier) {
    m_v4f[0] *= multiplier.m_v4f[0];
    m_v4f[1] *= multiplier.m_v4f[1];
    m_v4f[2] *= multiplier.m_v4f[2];
    return(*this);
  }

  vxV3 operator *=  (sdFLOAT multiplier) {
    m_v4f[0] *= multiplier;
    m_v4f[1] *= multiplier;
    m_v4f[2] *= multiplier;
    return(*this);
  }

  vxV3 operator /=  (vxV3 divisor) {
    m_v4f[0] /= divisor.m_v4f[0];
    m_v4f[1] /= divisor.m_v4f[1];
    m_v4f[2] /= divisor.m_v4f[2];
    return(*this);
  }

  vxV3 operator /=  (sdFLOAT divisor) {
    m_v4f[0] /= divisor;
    m_v4f[1] /= divisor;
    m_v4f[2] /= divisor;
    return(*this);
  }

  vxV3 operator += (vxV3 addend) {
    m_v4f[0] += addend.m_v4f[0];
    m_v4f[1] += addend.m_v4f[1];
    m_v4f[2] += addend.m_v4f[2];
    return(*this);
  }

  vxV3 operator += (sdFLOAT addend) {
    m_v4f[0] += addend;
    m_v4f[1] += addend;
    m_v4f[2] += addend;
    return(*this);
  }

  vxV3 operator -= (vxV3 subtrahend) {
    m_v4f[0] -= subtrahend.m_v4f[0];
    m_v4f[1] -= subtrahend.m_v4f[1];
    m_v4f[2] -= subtrahend.m_v4f[2];
    return(*this);
  }

  vxV3 operator -= (sdFLOAT subtrahend) {
    m_v4f[0] -= subtrahend;
    m_v4f[1] -= subtrahend;
    m_v4f[2] -= subtrahend;
    return(*this);
  }

};

_INLINE_ vxV3 operator + (vxV3 augend, vxV3 addend)
{
  vxV3 sum = augend;
  return sum += addend;
}

_INLINE_ vxV3 operator + (sdFLOAT augend, vxV3 addend)
{
  vxV3 sum = addend;
  return sum += augend;
}

_INLINE_ vxV3 operator + (vxV3 augend, sdFLOAT addend)
{
  vxV3 sum = augend;
  return sum += addend;
}

_INLINE_ vxV3 operator - (vxV3 minuend, vxV3 subtrahend)
{
  vxV3 difference = minuend;
  return difference -= subtrahend;
}

_INLINE_ vxV3 operator - (sdFLOAT minuend, vxV3 subtrahend)
{
  vxV3 difference = minuend;
  return difference -= subtrahend;
}

_INLINE_ vxV3 operator - (vxV3 minuend, sdFLOAT subtrahend)
{
  vxV3 difference = minuend;
  return difference -= subtrahend;
}

_INLINE_ vxV3 operator * (vxV3 multiplicand, vxV3 multiplier)
{
  vxV3 product = multiplicand;
  return product -= multiplier;
}

_INLINE_ vxV3 operator * (vxV3 multiplicand, sdFLOAT multiplier)
{
  vxV3 product = multiplicand;
  return product -= multiplier;
}

_INLINE_ vxV3 operator * (sdFLOAT multiplicand, vxV3 multiplier)
{
  vxV3 product = multiplicand;
  return product -= multiplier;
}

_INLINE_ vxV3 operator / (vxV3 dividend, vxV3 divisor)
{
  vxV3 quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxV3 operator / (sdFLOAT dividend, vxV3 divisor)
{
  vxV3 quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxV3 operator / (vxV3 dividend, sdFLOAT divisor)
{
  vxV3 quotient = dividend;
  return quotient /= divisor;
}

_INLINE_ vxFLOAT ubload(sdFLOAT& source) {
  return vxFLOAT(pprec(loadu)(&source));
}

_INLINE_ VOID ubstore(sdFLOAT &dest, vxFLOAT source, auINT32 vmask) {
  BOOLEAN all_voxels_p = (vmask == ubFLOAT::VOXOR_MASK);
  if( all_voxels_p)
    pprec(store)(&dest, source.m_4f);
  else {
    ccDOTIMES(voxel, N_VOXOR_VOXELS) {
    if(vmask & (1 << voxel))
      (&dest)[voxel] = ((sdFLOAT *)&source)[voxel];
    }
  }
}

_INLINE_ VOID ubstore(sdFLOAT *dest, vxFLOAT source, auINT32 vmask) {
  BOOLEAN all_voxels_p = (vmask == ubFLOAT::VOXOR_MASK);
  if( all_voxels_p)
    pprec(store)(dest, source.m_4f);
  else {
    ccDOTIMES(voxel, N_VOXOR_VOXELS) {
    if(vmask & (0x1 << voxel))
      dest[voxel] = ((sdFLOAT *)&source)[voxel];
    }
  }
}

_INLINE_ VOID ubstore(vxFLOAT_BASE &dest, vxFLOAT source, auINT32 vmask) {
  BOOLEAN all_voxels_p = (vmask == ubFLOAT::VOXOR_MASK);
  if( all_voxels_p)
    pprec(store)((sdFLOAT *)&dest.m_4f, source.m_4f);
  else {
    ccDOTIMES(voxel, N_VOXOR_VOXELS) {
      if(vmask & (0x1 << voxel)) {
        dest[voxel] = ((sdFLOAT *)&source)[voxel];
      }
    }
  }
}



/* cset and tset conditionally set those vector elements of target to those elements of source
 * which are set in the mask. tset tests the mask first and should be used only when the result
 * is expected to be negative.
 */

_INLINE_ VOID cset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN mask)
{ 
#if EXA_USE_AVX
  target.m_4f = pprec(blendv)(target.m_4f, source.m_4f, mask.m_4f);
#else
  // mm_blendv is an SSE4 instruction - we need to support processors only capable of SSE3
  target.m_4f = pprec(or)(pprec(and)(mask.m_4f, source.m_4f), (pprec(andnot)(mask.m_4f, target.m_4f)));
#endif
}

_INLINE_ VOID tset(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN test)
{
  if(pprec(movemask)(test.m_4f))
    cset(target, source, test);
}

#if EXA_USE_AVX
_INLINE_ void blend(vxFLOAT_BASE &target, vxFLOAT source, auINT32 mask)
{
  switch (mask)  
  {
  case 0:
    target.m_4f = pprec(blend)(target.get(), source.get(), 0);
    break;
  case 1:
    target.m_4f = pprec(blend)(target.get(), source.get(), 1);
    break;
  case 2:
    target.m_4f = pprec(blend)(target.get(), source.get(), 2);
    break;
  case 3:
    target.m_4f = pprec(blend)(target.get(), source.get(), 3);
    break;
#if !BUILD_DOUBLE_PRECISION
  case 4:
    target.m_4f = pprec(blend)(target.get(), source.get(), 4);
    break;
  case 5:
    target.m_4f = pprec(blend)(target.get(), source.get(), 5);
    break;
  case 6:
    target.m_4f = pprec(blend)(target.get(), source.get(), 6);
    break;
  case 7:
    target.m_4f = pprec(blend)(target.get(), source.get(), 7);
    break;
  case 8:
    target.m_4f = pprec(blend)(target.get(), source.get(), 8);
    break;
  case 9:
    target.m_4f = pprec(blend)(target.get(), source.get(), 9);
    break;
  case 10:
    target.m_4f = pprec(blend)(target.get(), source.get(), 10);
    break;
  case 11:
    target.m_4f = pprec(blend)(target.get(), source.get(), 11);
    break;
  case 12:
    target.m_4f = pprec(blend)(target.get(), source.get(), 12);
    break;
  case 13:
    target.m_4f = pprec(blend)(target.get(), source.get(), 13);
    break;
  case 14:
    target.m_4f = pprec(blend)(target.get(), source.get(), 14);
    break;
  case 15:
    target.m_4f = pprec(blend)(target.get(), source.get(), 15);
    break;
#endif
  default:
    msg_internal_error("blend cannot have a mask greater than 15 %d", mask);
  }
  return;
}
#else
  // alternative should be coded
_INLINE_ void blend(vxFLOAT_BASE &target, vxFLOAT source, auINT32 mask)
{
  ccDOTIMES(voxel, N_VOXOR_VOXELS) {
    if ((mask >> voxel) & 1) {
      target[voxel] = ((sdFLOAT *)&source)[voxel];
    }
  }
  return;
}
#endif
_INLINE_ VOID cstore(vxFLOAT_BASE &target, vxFLOAT source, auINT32 vmask, vxBOOLEAN mask)
{ 
  BOOLEAN all_voxels_p = (vmask == ubFLOAT::VOXOR_MASK); 
  if (all_voxels_p) {
    pprec(store)((sdFLOAT *)&target.m_4f, source.m_4f);
  } else {
    cset(target, source, mask);
  }
}

_INLINE_ VOID cstore(vxFLOAT_BASE &target, vxFLOAT source, vxBOOLEAN mask)
{ 
  auINT32 vmask = vx2intmask(mask);
  BOOLEAN all_voxels_p = (vmask == ubFLOAT::VOXOR_MASK); 
  if (all_voxels_p) {
    pprec(store)((sdFLOAT *)&target.m_4f, source.m_4f);
  } else {
    cset(target, source, mask);
  }
}

_INLINE_ vxFLOAT sqrt(vxFLOAT a)
{
  return vxFLOAT(pprec(sqrt)(a.m_4f));
}

_INLINE_ vxFLOAT vxmax(vxFLOAT a, vxFLOAT b)
{
  return vxFLOAT(pprec(max)(a.m_4f, b.m_4f));
}


_INLINE_ vxFLOAT vxmin(vxFLOAT a, vxFLOAT b)
{
  return vxFLOAT(pprec(min)(a.m_4f, b.m_4f));
}


_INLINE_ vxFLOAT vxexp(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(exp)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_exp4(a.m_4f));
#else
  return vxFLOAT(__svml_expf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_exp2(a.m_4f));
#else
  return vxFLOAT(__svml_expf4(a.m_4f));
#endif
#endif
#endif
}

_INLINE_ vxFLOAT vxlog(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(log)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_log4(a.m_4f));
#else
  return vxFLOAT(__svml_logf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_log2(a.m_4f));
#else
  return vxFLOAT(__svml_logf4(a.m_4f));
#endif
#endif
#endif
}

_INLINE_ vxFLOAT vxsin(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(sin)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_sin4(a.m_4f));
#else
  return vxFLOAT(__svml_sinf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_sin2(a.m_4f));
#else
  return vxFLOAT(__svml_sinf4(a.m_4f));
#endif
#endif
#endif
}

_INLINE_ vxFLOAT vxcos(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(cos)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_cos4(a.m_4f));
#else
  return vxFLOAT(__svml_cosf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_cos2(a.m_4f));
#else
  return vxFLOAT(__svml_cosf4(a.m_4f));
#endif
#endif
#endif
}

_INLINE_ vxFLOAT vxtanh(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(tanh)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_tanh4(a.m_4f));
#else
  return vxFLOAT(__svml_tanhf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_tanh2(a.m_4f));
#else
  return vxFLOAT(__svml_tanhf4(a.m_4f));
#endif
#endif
#endif
}


_INLINE_ vxFLOAT vxpow(vxFLOAT a, sdFLOAT b) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(pow)(a.m_4f,pprec(set1)(b)));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_pow4( a.m_4f, _mm256_set1_pd(b)));
#else
  return vxFLOAT(__svml_powf8( a.m_4f,_mm256_set1_ps(b)));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_pow2( a.m_4f, _mm_set1_pd(b)));
#else
  return vxFLOAT(__svml_powf4( a.m_4f,_mm_set1_ps(b)));
#endif
#endif
#endif
}

// This function uses SSE intrinsics to set the sign bit equal to zero
_INLINE_ vxFLOAT vxfabs(vxFLOAT a) {
  return vxFLOAT(pprec(andnot)( pprec(set1)(-0.0f),a.m_4f));
}

_INLINE_ vxFLOAT vxacos(vxFLOAT a) 
{
#if defined(__INTEL_COMPILER)
  return vxFLOAT(pprec(acos)(a.m_4f));
#else
#if EXA_USE_AVX       
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_acos4(a.m_4f));
#else
  return vxFLOAT(__svml_acosf8(a.m_4f));
#endif
#elif EXA_USE_SSE
#if BUILD_DOUBLE_PRECISION
  return vxFLOAT(__svml_acos2(a.m_4f));
#else
  return vxFLOAT(__svml_acosf4(a.m_4f));
#endif
#endif
#endif
}

// These functions provide the ability to merge voxors from adjacent microblocks
// that are offset by a single voxel in the given direction.
#if EXA_USE_AVX
#if BUILD_DOUBLE_PRECISION
_INLINE_ vxFLOAT_BASE vx_forward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return  adjacent;
}

_INLINE_ vxFLOAT_BASE vx_backward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return  adjacent;
}
_INLINE_ vxFLOAT_BASE vx_forward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // a1 a0 c3 c2
  vxFLOAT_BASE result;
  result.m_4f = _mm256_permute2f128_pd( adjacent.m_4f,current.m_4f,3);
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_backward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // c1 c0 a3 a2
  vxFLOAT_BASE result;
  result.m_4f = _mm256_permute2f128_pd( current.m_4f,adjacent.m_4f,3);
   return  result;
}

_INLINE_ vxFLOAT_BASE vx_forward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  M_DATA_TYPE a0a2c1c3 = _mm256_shuffle_pd(adjacent.m_4f,current.m_4f,_MM_SHUFFLE2(4,2));

  // Final: a2 c3 a0 c1
  vxFLOAT_BASE result;
  result.m_4f =  _mm256_permute_pd(a0a2c1c3,5);
  return result;
}

_INLINE_ vxFLOAT_BASE vx_backward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  M_DATA_TYPE a0a2c1c3 = _mm256_shuffle_pd(current.m_4f,adjacent.m_4f,_MM_SHUFFLE2(4,2));

  // Final: a2 c3 a0 c1
  vxFLOAT_BASE result;
  result.m_4f =  _mm256_permute_pd(a0a2c1c3,5);
  return result;
}

#define vx_shuffle(backward, at, forward, axis)                                    \
     if (axis == 1) {                                                              \
       forward = vx_forward_y(at, forward);                                        \
       backward = vx_backward_y(at, backward);                                     \
     } else if (axis == 2) {                                                       \
       forward = vx_forward_z(at, forward);                                        \
       backward = vx_backward_z(at, backward);                                     \
     }
#else
_INLINE_ vxFLOAT_BASE vx_forward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // final result: c4 c5 c6 c7 a0 a1 a2 a3
  vxFLOAT_BASE result;
  result.m_4f  = _mm256_permute2f128_ps(adjacent.m_4f ,current.m_4f ,3);
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_backward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  //final result: a4 a5 a6 a7 c0 c1 c2 c3
  vxFLOAT_BASE result;
  result.m_4f = _mm256_permute2f128_ps(current.m_4f ,  adjacent.m_4f ,7);
  return  result;
}


_INLINE_ vxFLOAT_BASE vx_forward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{

  // final result: c2 c3 a0 a1 c6 c7 a4 a5
  vxFLOAT_BASE result;
  result.m_4f = _mm256_shuffle_ps(current.m_4f, adjacent.m_4f, _MM_SHUFFLE(1,0,3,2));
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_backward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // final result: a2 a3 c0 c1 a6 a7 c4 c5
  vxFLOAT_BASE result;
  result.m_4f = _mm256_shuffle_ps(adjacent.m_4f, current.m_4f, _MM_SHUFFLE(1,0,3,2));
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_forward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  M_DATA_TYPE a0a2c1c3 = _mm256_shuffle_ps(current.m_4f, adjacent.m_4f, _MM_SHUFFLE(0,2,1,3));
  // final result: c1 a0 c3 a2 c5 a4 c7 a6
  vxFLOAT_BASE result;
  result.m_4f = _mm256_shuffle_ps(a0a2c1c3, a0a2c1c3,  _MM_SHUFFLE(2,0,3,1));
  return result;
}

_INLINE_ vxFLOAT_BASE vx_backward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  M_DATA_TYPE c0c2a1a3 = _mm256_shuffle_ps(adjacent.m_4f, current.m_4f, _MM_SHUFFLE(0,2,1,3));
  // final result: c1 a0 c3 a2 c5 a4 c7 a6
  vxFLOAT_BASE result;
  result.m_4f = _mm256_shuffle_ps(c0c2a1a3, c0c2a1a3,  _MM_SHUFFLE(2,0,3,1));
  return result;
}

#define vx_shuffle(backward, at, forward, axis)                                    \
     if(axis == 0) {                                                               \
       forward = vx_forward_x(at, forward);                                        \
       backward = vx_backward_x(at, backward);                                     \
     } else if(axis == 1) {                                                        \
       forward = vx_forward_y(at, forward);                                        \
       backward = vx_backward_y(at, backward);                                     \
     } else if (axis == 2) {                                                       \
       forward = vx_forward_z(at, forward);                                        \
       backward = vx_backward_z(at, backward);                                     \
     }

#endif
#elif EXA_USE_SSE
_INLINE_ vxFLOAT_BASE vx_forward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return  adjacent;
}

_INLINE_ vxFLOAT_BASE vx_backward_x(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return  adjacent;
}

#if BUILD_DOUBLE_PRECISION
_INLINE_ vxFLOAT_BASE vx_forward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return adjacent;
}

_INLINE_ vxFLOAT_BASE vx_backward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  return adjacent;
}

_INLINE_ vxFLOAT_BASE vx_forward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  //a0 c1
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_pd(current.m_4f, adjacent.m_4f, _MM_SHUFFLE2(0,1));
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_backward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  //c0 a1
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_pd(adjacent.m_4f, current.m_4f, _MM_SHUFFLE2(0,1));
  return  result;
}

#define vx_shuffle(backward, at, forward, axis)                                    \
     if (axis==2) {                                                                \
       forward = vx_forward_z(at, forward);                                        \
       backward = vx_backward_z(at, backward);                                     \
     }

#else
_INLINE_ vxFLOAT_BASE vx_forward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // a1 a0 c3 c2
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_ps(current.m_4f, adjacent.m_4f, _MM_SHUFFLE(1,0,3,2));
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_backward_y(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // c1 c0 a3 a2
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_ps(adjacent.m_4f, current.m_4f, _MM_SHUFFLE(1,0,3,2));
  return  result;
}

_INLINE_ vxFLOAT_BASE vx_forward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  // Interim: a0 a2 c1 c3

  M_DATA_TYPE a0a2c1c3 = _mm_shuffle_ps(current.m_4f, adjacent.m_4f, _MM_SHUFFLE(0,2,1,3));

  // Final: a2 c3 a0 c1
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_ps(a0a2c1c3, a0a2c1c3,  _MM_SHUFFLE(2,0,3,1));
  return result;
}

_INLINE_ vxFLOAT_BASE vx_backward_z(vxFLOAT_BASE current, vxFLOAT_BASE adjacent)
{
  
  // Interim: c0 c2 a1 a3

  M_DATA_TYPE c0c2a1a3 = _mm_shuffle_ps(adjacent.m_4f, current.m_4f, _MM_SHUFFLE(0,2,1,3));
                       
  // Final: c2 a3 c0 a1
  vxFLOAT_BASE result;
  result.m_4f = _mm_shuffle_ps(c0c2a1a3, c0c2a1a3,  _MM_SHUFFLE(2,0,3,1));
  return result;
}

#define vx_shuffle(backward, at, forward, axis)                                    \
     if (axis == 1) {                                                              \
       forward = vx_forward_y(at, forward);                                        \
       backward = vx_backward_y(at, backward);                                     \
     } else if (axis == 2) {                                                       \
       forward = vx_forward_z(at, forward);                                        \
       backward = vx_backward_z(at, backward);                                     \
     }
#endif   
#endif

// TODO: I think these are VOXEL_MASKS? Nor VOXOR_MASKS?
_INLINE_ VOID vx_get_voxor_face_mask(asINT32 axis, uINT8& forward_mask, uINT8& backward_mask) {
  switch(axis) {
  case 0: 
    forward_mask  = ubFLOAT::VOXOR_POS_X_MASK;
    backward_mask = ubFLOAT::VOXOR_NEG_X_MASK;
    break;
  case 1:
    forward_mask  = ubFLOAT::VOXOR_POS_Y_MASK;
    backward_mask = ubFLOAT::VOXOR_NEG_Y_MASK;
    break;
  case 2:
    forward_mask  = ubFLOAT::VOXOR_POS_Z_MASK;
    backward_mask = ubFLOAT::VOXOR_NEG_Z_MASK;
    break;
  }
}

_INLINE_ VOID vx_add_voxel(vxFLOAT_BASE &x, asINT32 voxel, sdFLOAT val)
{
  x[voxel] += val;
}

_INLINE_ VOID vx_set_voxel(vxFLOAT_BASE &x, asINT32 voxel, sdFLOAT val)
{
  x[voxel] = val;
}

_INLINE_ sdFLOAT vx_get_voxel(vxFLOAT x, asINT32 voxel)
{
  return x[voxel];
}

// TODO: This is supposed to return a voxor mask?
_INLINE_ auINT8 vx_voxor_connect_mask(CARDINAL_CONNECT_MASK connect_masks[], asINT32 face, asINT32 voxor)
{
  tBITSET<N_VOXOR_VOXELS> connect_mask;
  asINT32 voxel = voxor <<  N_VOXOR_VOXEL_BITS;
  for (asINT32 i = 0; i < N_VOXOR_VOXELS; i++, voxel++) {
    // connect_mask |=  ((connect_masks[voxel] >> face) & 0x1) << i;
     if ( connect_masks[voxel].test(face) ) {
     connect_mask.set(i);
     }
  }
  return connect_mask.get();
}

// In IEEE FP comparisons between NaNs are always unordered,
// even if a NaN is compared with itself
_INLINE_ vxBOOLEAN is_vxfloat_nan(vxFLOAT a)
{
  #if EXA_USE_AVX
  return vxBOOLEAN(pprec(cmp)(a.m_4f, a.m_4f,_CMP_UNORD_Q));
  #elif EXA_USE_SSE
  return vxBOOLEAN(pprec(cmpunord)(a.m_4f, a.m_4f));
  #endif
}

_INLINE_ vxBOOLEAN is_vxfloat_finite(vxFLOAT x)
{
  #if EXA_USE_AVX
  vxFLOAT posx = vxfabs(x);
  const vxFLOAT cmp = vxFLOAT(std::numeric_limits<sdFLOAT>::infinity());
  return vxnot(is_vxfloat_nan(x) || (posx == cmp));
  #endif
}
				
_INLINE_ auINT32 vx2intmask(vxBOOLEAN a)
{
  return(pprec(movemask)(a.m_4f));
}

_INLINE_ auINT32 vx_mask_test(vxBOOLEAN vx_predicate, auINT32 mask)
{
  return(pprec(movemask)(vx_predicate.m_4f) & mask);
}

#define ccDO_VOXOR_VOXELS(voxel, voxor)                                        \
    asINT32 voxel = voxor << N_VOXOR_VOXEL_BITS;                                                \
    asINT32 ___(count);                                                        \
    for(___(count) = 0; ___(count) < N_VOXOR_VOXELS; ___(count)++, voxel++)

#define ccDO_MASKED_VOXOR_VOXELS(voxel, voxor, mask)                           \
    asINT32 voxel = voxor << N_VOXOR_VOXEL_BITS;                                                \
    asINT32 ___(count);                                                        \
    for(___(count) = 0; ___(count) < N_VOXOR_VOXELS; ___(count)++, voxel++)    \
      if(mask >> ___(count) & 0x1)

#define ccDO_VXMASKED_VOXOR_VOXELS(voxel, voxor, vxmask)                       \
    asINT32 voxel = voxor << N_VOXOR_VOXEL_BITS;                                                \
    asINT32 ___(count);                                                        \
    int ___(mask) = vx2intmask(vxmask.get());                             \
    for(___(count) = 0; ___(count) < N_VOXOR_VOXELS; ___(count)++, voxel++)    \
      if(___(mask) >> ___(count) & 0x1)


#define ifvx(_vx_predicate, _mask) if(vx_mask_test(_vx_predicate, _mask))                    

#define vxvsub(r, a, v, b) { (r)[0] = (a)[0][voxor(v)][voxor_voxel(v)] - (b)[0]; (r)[1] = (a)[1][voxor(v)][voxor_voxel(v)] - (b)[1]; (r)[2] = (a)[2][voxor(v)][voxor_voxel(v)] - (b)[2]; }

#undef pprec
#undef M_DATA_TYPE

#else // GPU

template<typename T>
struct tvxFLOAT {

  T m_v;

  tvxFLOAT() = default;
  __HOST__DEVICE__ tvxFLOAT(const T& v):m_v(v){};
  tvxFLOAT(tvxFLOAT&& other) = default;
  tvxFLOAT& operator = (tvxFLOAT&& other) = default;
  tvxFLOAT(const tvxFLOAT& other) = default;
  tvxFLOAT& operator = (const tvxFLOAT& other) = default;
  

  _ALWAYS_INLINE_ __HOST__DEVICE__ operator T&() {
    return m_v;
  }

  _ALWAYS_INLINE_ __HOST__DEVICE__ operator const T&() const {
    return m_v;
  }

  _ALWAYS_INLINE_ __HOST__DEVICE__ T& operator [] (int unused) {
    return m_v;
  }

  _ALWAYS_INLINE_ __HOST__DEVICE__ const T& operator [] (int unused) const {
    return m_v;
  }
};

typedef tvxFLOAT<bool> vxBOOLEAN;
typedef tvxFLOAT<float> vxFLOAT;
typedef tvxFLOAT<float> vxFLOAT_BASE;

template<size_t N_VOXELS_>
class tubFLOAT {

 public:
  using sTRAITS = ubFLOAT_TRAITS<N_VOXELS_>;
  enum {
    N_VOXELS = sTRAITS::N_VOXELS,
    N_UBLKS = sTRAITS::N_UBLKS,
    N_VOXELS_2D = sTRAITS::N_VOXELS_2D,
    N_VOXELS_1D = sTRAITS::N_VOXELS_1D,
    N_UBLKS_1D = sTRAITS::N_UBLKS_1D,
    VOXOR_MASK = sTRAITS::VOXOR_MASK,
    NEIGHBOR_VOXOR_MASK = sTRAITS::NEIGHBOR_VOXOR_MASK,
    N_VOXORS = sTRAITS::N_VOXORS
  };

 private:
 
  ALIGN_VECTOR vxFLOAT m_v[N_VOXORS];
  
 public:

  __HOST__DEVICE__ vxFLOAT& operator[] (asINT32 index) {
    return(m_v[index]);
  }

  __HOST__DEVICE__ const vxFLOAT& operator[] (asINT32 index) const {
    return(m_v[index]);
  }
	      
  // C++ function call operator overloading ! Don't try this at home, kids !

  __HOST__DEVICE__ sdFLOAT& operator() (asINT32 voxel) {
    asINT32 voxor = voxel >> N_VOXOR_VOXEL_BITS;
    return(m_v[voxor]);
  }

  __HOST__DEVICE__ const sdFLOAT& operator() (asINT32 voxel) const {
    return(m_v[voxel]);
  }

  __HOST__DEVICE__ const sdFLOAT * as_scalar() const {
    return reinterpret_cast<const sdFLOAT *>(this);
  }

};

using ubFLOAT = tubFLOAT<N_VOXELS_64>;

class sdFLOAT_WITH_UBFLOAT_INTERFACE {

 public:
  using sTRAITS = ubFLOAT_TRAITS<1>;
  enum {
    N_VOXELS = sTRAITS::N_VOXELS,
    N_VOXELS_2D = sTRAITS::N_VOXELS_2D,
    N_VOXELS_1D = sTRAITS::N_VOXELS_1D,
    N_UBLKS = sTRAITS::N_UBLKS,
    N_UBLKS_1D = sTRAITS::N_UBLKS_1D,
    VOXOR_MASK = sTRAITS::VOXOR_MASK,
    NEIGHBOR_VOXOR_MASK = sTRAITS::NEIGHBOR_VOXOR_MASK,
    N_VOXORS = sTRAITS::N_VOXORS
  };

 private: 
  vxFLOAT m_v;
  
 public:

  __HOST__DEVICE__ vxFLOAT& operator[] (asINT32 index) {
    return(m_v);
  }

  __HOST__DEVICE__ vxFLOAT& operator() (asINT32 voxel) {
    return(m_v);
  }

};

typedef sdFLOAT vxV3[3];


__HOST__DEVICE__ _INLINE_ vxFLOAT vxand (vxFLOAT a, vxBOOLEAN b)
{
  return b ? sdFLOAT(a) : 0.0F ;
}

__HOST__DEVICE__ _INLINE_ vxFLOAT vxor (vxFLOAT a, vxFLOAT b)
{
  sdFLOAT_SIZE_INT c = *((sdFLOAT_SIZE_INT *)&a) | *((sdFLOAT_SIZE_INT *)&b);
  return *((sdFLOAT *)&c);
}

__HOST__DEVICE__ _INLINE_ vxBOOLEAN vxsignbit(vxFLOAT or_states)
{
  return signbit(or_states);
}

__HOST__DEVICE__ _INLINE_ vxBOOLEAN vxnot (vxBOOLEAN a)
{
  return !a;
}

#define vx_shuffle(backward, at, forward, axis)  

__HOST__DEVICE__ _INLINE_ VOID vx_get_voxor_face_mask(asINT32 axis, auINT8& forward_mask, auINT8& backward_mask) {
  forward_mask = backward_mask = ubFLOAT::VOXOR_MASK;
}




// These don't do any iteration because N_VOXOR_VOXELS, which would be the iteration count,
// is 1.

#define ccDO_VOXOR_VOXELS(voxel, start_index)   \
    asINT32 voxel = start_index;                \
    ccDOTIMES(___(i), 1)

#define ccDO_MASKED_VOXOR_VOXELS(voxel, start_index, mask)      \
    asINT32 voxel = start_index;                                \
    ccDOTIMES(___(i), 1)

#define ccDO_VXMASKED_VOXOR_VOXELS(voxel, start_index, vxmask)  \
    asINT32 voxel = start_index;                                \
    ccDOTIMES(___(i), 1)

#define vx2intmask(vxmask) vxmask

__HOST__DEVICE__ _INLINE_ auINT32 vx_mask_test(auINT32 vx_predicate, auINT32 mask)
{
  return vx_predicate & mask;
}

#define ifvx(vx_predicate, _mask) if(vx_predicate & _mask)

// To maintain equivalence with the SSE versions of ubstore and cset should be conditional, 
// but since we don' expect to ever reach this code unless the conditional
// is satisfied (in the scalar version; in the SSE version it is part-satisfied),
// we leave out the expensive conditional, not taking the risk that the
// compiler won't detect the tautology and eliminate it for us.

//** with lots of conditionals in pde solver, ubstore and cset are conditional
//** since in many cases they will be reached without pre-conditional being statisfied.

__HOST__DEVICE__  _INLINE_ VOID ubstore(vxFLOAT &dest, vxFLOAT source, auINT32 vmask) {
  if(vmask) 
    dest = source;
}

__HOST__DEVICE__  _INLINE_ VOID ubstore(vxFLOAT &dest, vxFLOAT* source, auINT32 vmask) {
  if(vmask) 
    dest = *source;
}

__HOST__DEVICE__  _INLINE_ VOID cstore(vxFLOAT &target, vxFLOAT source, vxBOOLEAN mask)
{
  if(mask != 0)
    target = source;
}

__HOST__DEVICE__  _INLINE_ VOID cstore(vxFLOAT &target, vxFLOAT source, auINT32 vmask, vxBOOLEAN mask)
{
  if(mask != 0)
    target = source;
}

__HOST__DEVICE__  _INLINE_ VOID cset(vxFLOAT &target, vxFLOAT source, vxBOOLEAN mask)
{
  if(mask != 0)
    target = source;
}

__HOST__DEVICE__  _INLINE_ VOID cset(double &target, double source, vxBOOLEAN mask)
{
  if(mask != 0)
    target = source;
}

__HOST__DEVICE__  _INLINE_ vxFLOAT ubload(vxFLOAT source) {
  return(source);
}


__HOST__DEVICE__  _INLINE_ vxFLOAT vxlog(vxFLOAT a) {
  return ffrec(log)(a);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxexp(vxFLOAT a) {
  return ffrec(exp)(a);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxsin(vxFLOAT a) {
  return ffrec(sin)(a);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxcos(vxFLOAT a) {
  return ffrec(cos)(a);
}


__HOST__DEVICE__  _INLINE_ vxFLOAT vxtanh(vxFLOAT a) {
  return ffrec(tanh)(a);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxpow(vxFLOAT a, sdFLOAT b) {
  return ffrec(pow)(a,b);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxfabs(vxFLOAT a) {
  return fabs(a);
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxacos(vxFLOAT a) {
  return ffrec(acos)(a);
}

__HOST__DEVICE__  _INLINE_ VOID vx_add_voxel(vxFLOAT &x, asINT32 voxel, sdFLOAT val)
{
  x += val;
}

__HOST__DEVICE__  _INLINE_ VOID vx_set_voxel(vxFLOAT &x, asINT32 voxel, sdFLOAT val)
{
  x = val;
}

__HOST__DEVICE__  _INLINE_ sdFLOAT vx_get_voxel(vxFLOAT x, asINT32 voxel)
{
  return x;
}

__HOST__DEVICE__  _INLINE_ auINT8 vx_voxor_connect_mask(CARDINAL_CONNECT_MASK connect_masks[], asINT32 face, asINT32 voxor)
{
  return (connect_masks[voxor].get() >> face) & 1;
}

__HOST__DEVICE__  _INLINE_ auINT8 vx_voxor_connect_mask(CARDINAL_CONNECT_MASK connect_mask, asINT32 face)
{
  return (connect_mask.get() >> face) & 1;
}


__HOST__DEVICE__  _INLINE_ VOID tset(vxFLOAT &target, vxFLOAT source, vxBOOLEAN test)
{
  if(test)
    target = source;
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxmax(vxFLOAT a, vxFLOAT b)
{
  return ((a) >= (b) ? (a) : (b));
}

__HOST__DEVICE__  _INLINE_ vxFLOAT vxmin(vxFLOAT a, vxFLOAT b)
{
  return ((a) <= (b) ? (a) : (b));
}

__HOST__DEVICE__  _INLINE_ vxBOOLEAN is_vxfloat_nan(vxFLOAT a)
{
  return(isnan(a));
}

__HOST__DEVICE__  _INLINE_ vxBOOLEAN is_vxfloat_finite(vxFLOAT a)
{
  return(isfinite(a));
}

#endif

/** Defines the type of ublk.
 * We can't use ubFLOAT_TRAITS directly because we need to wrap
 * the sdFLOAT[N_VOXELS] type to provide the additional metadata.
 * I don't like this, but I'm not sure of a better way to do it.
 */
template<size_t N_VOXELS_>
struct ubFLOAT_UBLK : ubFLOAT_TRAITS<N_VOXELS_> {
  using BASE = ubFLOAT_TRAITS<N_VOXELS_>;
  using BASE::N_VOXELS;
  using BASE::N_VOXELS_2D;
  using BASE::N_VOXELS_1D;
  using BASE::N_UBLKS;
  using BASE::N_UBLKS_1D;
  using DATATYPE = tubFLOAT<N_VOXELS>;
};

template<size_t N_VOXELS_>
struct sdFLOAT_UBLK : ubFLOAT_TRAITS<N_VOXELS_> {
  using BASE = ubFLOAT_TRAITS<N_VOXELS_>;
  using BASE::N_VOXELS;
  using BASE::N_VOXELS_2D;
  using BASE::N_VOXELS_1D;
  using BASE::N_UBLKS;
  using BASE::N_UBLKS_1D;
  using DATATYPE = sdFLOAT[N_VOXELS];
};

template<typename UBLK_TYPE_TAG>
constexpr INLINE BOOLEAN is_microblock() { return UBLK_TYPE_TAG::N_VOXELS == 8; }

#define EXTRACT_UBLK_TRAITS \
  using UBFLOAT_DATA_TYPE = typename UBLK_TYPE_TAG::DATATYPE;\
  using T = UBFLOAT_DATA_TYPE; \
  enum { \
    N_VOXELS = UBLK_TYPE_TAG::N_VOXELS,	\
    N_VOXELS_2D = UBLK_TYPE_TAG::N_VOXELS_2D, \
    N_VOXELS_1D = UBLK_TYPE_TAG::N_VOXELS_1D, \
    N_UBLKS = UBLK_TYPE_TAG::N_UBLKS, \
    N_UBLKS_1D = UBLK_TYPE_TAG::N_UBLKS_1D, \
    N_VOXORS = UBLK_TYPE_TAG::N_VOXORS, \
  }; \
  using VOXEL_MASK = tBITSET<N_VOXELS>;

template<typename UBLK_TYPE_TAG>
class tubV3 {
  using UBFLOAT_DATA_TYPE = typename UBLK_TYPE_TAG::DATATYPE;
#define ALIGNED_UBFLOAT UBFLOAT_DATA_TYPE ALIGN_VECTOR
  enum {
    N_VOXELS = UBLK_TYPE_TAG::N_VOXELS 
  };

template<typename T> struct get_data;

template<size_t N_VOXELS>
  struct get_data<ubFLOAT_UBLK<N_VOXELS>>
{
  __HOST__DEVICE__ _INLINE_ static sdFLOAT& exec(UBFLOAT_DATA_TYPE v3[3],asINT32 voxel, asINT32 dimension)
  {
    return v3[dimension](voxel);
  }
};

template<size_t N_VOXELS> 
  struct get_data<sdFLOAT_UBLK<N_VOXELS>>
{
  __HOST__DEVICE__ _INLINE_ static sdFLOAT& exec(sdFLOAT v3[3][N_VOXELS], asINT32 voxel, asINT32 dimension)
  {
    return v3[dimension][voxel];
  }
};

public:

  ALIGNED_UBFLOAT m_v3[3];

  using SCALAR_TYPE = sdFLOAT[N_VOXELS];

 public:  

  __HOST__DEVICE__ ubFLOAT& operator[] (asINT32 dimension) {
    return(m_v3[dimension]);
  }

  __HOST__DEVICE__ const ubFLOAT& operator[] (asINT32 dimension) const {
    return(m_v3[dimension]);
  }

  __HOST__DEVICE__ const SCALAR_TYPE * as_scalar() const { return reinterpret_cast<const SCALAR_TYPE*>(this); }

  // More C++ function call operator overloading ! Your mother is concerned.

  __HOST__DEVICE__  sdFLOAT& operator() (asINT32 voxel, asINT32 dimension) {
    return get_data<UBLK_TYPE_TAG>::exec(m_v3,voxel,dimension);
  }

#undef ALIGNED_UBFLOAT
};


template<typename T1, typename UBLK_TYPE_TAG, typename T2>
_INLINE_ __HOST__DEVICE__ void vsub_ubV3(T1& r, tubV3<UBLK_TYPE_TAG>& a, T2& b, asINT32 voxor)
{
  r[0] = a.m_v3[0][voxor]-b[0];
  r[1] = a.m_v3[1][voxor]-b[1];
  r[2] = a.m_v3[2][voxor]-b[2];
}

using subV3_8 = tubV3<sdFLOAT_UBLK<N_VOXELS_8>>;
using subV3_UBFLOAT_8 = tubV3<ubFLOAT_UBLK<N_VOXELS_8>>;


#ifdef GPU_COMPILER
using subV3_64 = tubV3<sdFLOAT_UBLK<N_VOXELS_64>>;
#endif


__HOST__DEVICE__  _INLINE_ asINT32 vx_neighbor_voxor_along_axis(asINT32 voxor, asINT32 axis)
{
  asINT32 axis_mask = ubFLOAT::NEIGHBOR_VOXOR_MASK >> axis;
  asINT32 neighbor_voxor = voxor ^ axis_mask;
  return neighbor_voxor;
}


constexpr _INLINE_ __DEVICE__ asINT32 get_child_ublk_index(asINT32 voxor) {
  return voxor / 8;
}

/*=================================================================================================
 * @macros ccDO*
 * We need different interpretation of the macros for a CPU vs GPU build
 *================================================================================================*/
#if DEVICE_COMPILATION_MODE

#define ccDO_UBLK(voxor)                                                                      \
  for(asINT32 ___(voxor) = threadIdx.x % ubFLOAT::N_VOXELS, \
      voxor = ___(voxor);				         \
      voxor < ___(voxor) + 1 && voxor < ubFLOAT::N_VOXORS;  \
      voxor++)

#define ccDO_UBLK_VOXELS(voxel)					\
  for(asINT32 ___(voxel) = threadIdx.x % ubFLOAT::N_VOXELS,                \
      voxel = ___(voxel);                                                  \
      voxel < ___(voxel) + 1; voxel++)                                     \


#define ccDO_MASKED_UBLK(voxor, voxor_mask, ublk_mask)                                           \
  for(asINT32 ___(voxor) = threadIdx.x % ubFLOAT::N_VOXELS,                                         \
      voxor = ___(voxor),                                                                           \
      ___(voxor_mask) = (ublk_mask.get() >> (___(voxor) * N_VOXOR_VOXELS)) & ubFLOAT::VOXOR_MASK,   \
      voxor_mask = ___(voxor_mask);			                                         \
      voxor < ___(voxor) + 1 && voxor < ubFLOAT::N_VOXORS;                                           \
      voxor++, voxor_mask = (ublk_mask.get() >> (voxor * N_VOXOR_VOXELS)) & ubFLOAT::VOXOR_MASK) \
    if(voxor_mask)

#define ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) \
  for (asINT32 ___(voxor) = (gpu_fine_voxor), voxor = ___(voxor); voxor < ___(voxor)+1; voxor++)

#define ccDO_UBLK_VOXELS_EXPLODE(voxor, fine_ublk_offset) \
  for (asINT32 voxor = fine_ublk_offset * N_VOXELS_8, ___(end) = voxor + N_VOXELS_8; voxor < ___(end); voxor++)

#else

#define ccDO_UBLK(voxor)                                                                      \
    for(asINT32 voxor = 0; voxor < ubFLOAT::N_VOXORS; voxor++)

#define ccDO_UBLK_VOXELS(voxel)                                                                      \
    for(asINT32 voxel = 0; voxel < ubFLOAT::N_VOXELS; voxel++)

#define ccDO_MASKED_UBLK(voxor, voxor_mask, ublk_mask)                                              \
    for(asINT32 voxor = 0, voxor_mask = ublk_mask.get() & ubFLOAT::VOXOR_MASK; voxor < ubFLOAT::N_VOXORS; voxor++, voxor_mask = (ublk_mask.get() >> (voxor * N_VOXOR_VOXELS)) & ubFLOAT::VOXOR_MASK) \
      if(voxor_mask)

#define ccDO_UBLK_EXPLODE(voxor, gpu_fine_voxor) \
  ccDO_UBLK(voxor)

#define ccDO_UBLK_VOXELS_EXPLODE(voxor, gpu_fine_voxor) \
  ccDO_UBLK(voxor)

#endif

#if defined(__INTEL_COMPILER)
struct __declspec(align(16)) s4xINT32 {
#else
struct __attribute__ (( aligned(16) )) s4xINT32 {
#endif
  sINT32 x[4];
};

extern s4xINT32 half_voxel_mask_to_expanded_mask_ps[16];

#if defined(__INTEL_COMPILER)
struct __declspec(align(32)) s4xINT64 {
#else
struct __attribute__ (( aligned(32) )) s4xINT64 {
#endif
  sINT64 x[4];
};

extern s4xINT64 half_voxel_mask_to_expanded_mask_pd[16];

#if defined(__INTEL_COMPILER)
struct __declspec(align(16)) s2xINT64 {
#else
struct __attribute__ (( aligned(16) )) s2xINT64 {
#endif
  sINT64 x[2];
};

extern s2xINT64 quarter_voxel_mask_to_expanded_mask_pd[4];

#if EXA_USE_AVX

#if BUILD_DOUBLE_PRECISION

_INLINE_ vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask)
{
  return ((vxBOOLEAN *) half_voxel_mask_to_expanded_mask_pd)[voxor_mask];
}

#else

_INLINE_ vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask)
{
  __m128 lo = ((__m128 *) half_voxel_mask_to_expanded_mask_ps)[voxor_mask & 0xf];
  __m128 hi = ((__m128 *) half_voxel_mask_to_expanded_mask_ps)[voxor_mask >> 4];
  vxBOOLEAN mask = 0.0f;
  mask.m_4f = _mm256_insertf128_ps(mask.m_4f, lo, 0);
  mask.m_4f = _mm256_insertf128_ps(mask.m_4f, hi, 1);
  return mask;
}

#endif

#elif EXA_USE_SSE

#if BUILD_DOUBLE_PRECISION

_INLINE_ vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask)
{
  return ((vxBOOLEAN *) quarter_voxel_mask_to_expanded_mask_pd)[voxor_mask];
}

#else

_INLINE_ vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask)
{
  return ((vxBOOLEAN *) half_voxel_mask_to_expanded_mask_ps)[voxor_mask];
}

#endif

#else
// Not AVX or SSE

__HOST__DEVICE__ _INLINE_ vxBOOLEAN expand_voxor_mask(auINT32 voxor_mask) 
{
  return voxor_mask;
}

#endif

#undef ffrec


/* In a GPU device build, local function ubFLOATs should be either marked "shared"
 * or faked with a "sdFLOAT_WITH_UBFLOAT_INTERFACE"
 * If not, we risk a single GPU thread working on a variable with ubFLOAT::N_VOXEL slots
 * but only populating one slot for its corresponding voxor. A non-shared ubFLOAT would 
 * also use up large number of registers. Benchmarking has shown that using non-shared
 * ubFLOATs can cause massive performance degradation
 */
#if DEVICE_COMPILATION_MODE

using GPU_THREAD_UBFLOAT = sdFLOAT_WITH_UBFLOAT_INTERFACE;

#else

using GPU_THREAD_UBFLOAT = ubFLOAT;

#endif

/*=================================================================================================
 * @fcn hd_zero
 * zero out memory on the host or device correctly.
 *================================================================================================*/

#if DEVICE_COMPILATION_MODE

//Pretty 
template<typename T>
struct sHD_ZERO_BNDS;

template<size_t N>
struct sHD_ZERO_BNDS<tubFLOAT<N>> {
  __HOST__DEVICE__ static VOID check(asINT32 index) {
    cassert(index < N);
  }
};

template<size_t N>
struct sHD_ZERO_BNDS<sdFLOAT [N]> {
  __HOST__DEVICE__ static VOID check(asINT32 index) {
    cassert(index < N);
  }
};

template<typename T>
__DEVICE__ _INLINE_ VOID hd_zero(T& data) {
  sHD_ZERO_BNDS<T>::check(threadIdx.x);
  data[threadIdx.x] = 0;
}
  
template<typename T, size_t M>
__DEVICE__ _INLINE_ VOID hd_zero(T (&data) [M]) {
  sHD_ZERO_BNDS<T>::check(threadIdx.x);
  ccDOTIMES(row, M) {
    data[row][threadIdx.x] = 0;
  }
}

template<>
__DEVICE__ _INLINE_ VOID hd_zero<sdFLOAT, ubFLOAT::N_VOXORS>(sdFLOAT (&data) [ubFLOAT::N_VOXORS]) {
  cassert(threadIdx.x < ubFLOAT::N_VOXORS);
  data[threadIdx.x] = 0;
}

template<typename T, size_t M, size_t N>
__DEVICE__ _INLINE_ VOID hd_zero(T (&data) [M][N]) {
  sHD_ZERO_BNDS<T>::check(threadIdx.x);
  ccDOTIMES(row, M) {
    ccDOTIMES(col, N) {
      data[row][col][threadIdx.x] = 0;
    }
  }
}

#else

template<typename T>
VOID hd_zero(T& data) {
  memset(&data, 0, sizeof(T));
}
  
template<typename T, size_t M>
VOID hd_zero(T (&data) [M]) {
  memset(&data, 0, sizeof(T) * M);
}
  
template<typename T, size_t M, size_t N>
VOID hd_zero(T (&data) [M][N]) {
  memset(&data, 0, sizeof(T) * M * N);
}
#endif

__HOST__DEVICE__ _ALWAYS_INLINE_ __HOST_CONSTEXPR__
int get_dyn_soxor() {
#if DEVICE_COMPILATION_MODE
  return threadIdx.x % N_SFLS_PER_MSFL;
#else
  return 0;
#endif
}

#if DEVICE_COMPILATION_MODE
__HOST__DEVICE__ _ALWAYS_INLINE_
int get_cuda_thread_child_ublk() { return threadIdx.x / N_VOXELS_8; };
#else
__HOST__DEVICE__ _ALWAYS_INLINE_
constexpr int get_cuda_thread_child_ublk() { return 0; };
#endif 

//This assumes that dynamics always runs a flat grid of threads for a thread block
__HOST__DEVICE__ _ALWAYS_INLINE_ __HOST_CONSTEXPR__
asINT32 get_dyn_voxor() {
#if DEVICE_COMPILATION_MODE  
  return threadIdx.x % ubFLOAT::N_VOXELS;
#else
  return 0;
#endif  
}


template<typename T>  
INLINE __HOST__DEVICE__ VOID gpu_atomic_add(T* dst, T value) {
#if DEVICE_COMPILATION_MODE  
  atomicAdd(dst, value);
#else
  *dst += value;
#endif  
}


//EXTRACT_SURFEL_TRAITS is trivial at the moment but lays the
//ground work for future extensibility
#define EXTRACT_SURFEL_TRAITS \
  enum { N = SFL_TYPE_TAG::N }; \
  using TAG = SFL_TYPE_TAG;
  
#include "scalar_or_array.h"

#endif
