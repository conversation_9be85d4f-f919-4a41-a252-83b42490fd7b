/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

// Many sources assert that locking and unlocking a mutex are very cheap operations if the lock is uncontested,
// since Linux implementations use something called a futex (fast user-space mutex) to keep the whole thing
// in user space and avoid context switches, but that they are quite costly if contested. 
// In this design under normal circumstances only the comm thread and one strand are simultaneously active.
// Thus strands can lock and unlock a mutex with abandon if that mutex is only rarely needed by the comm
// thread, and vice versa. When a mutex is shared between strands and the comm thread a convention should be 
// established which minimizes contention. This has been applied to the m_ready_mutex, which the comm thread 
// should hold only when strictly necessary.


#include "status.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include "timescale.h"
#include "thread_run.h"
#include "mlrf.h"
#include "bsurfel_comm.h"
#include "particle_comm.h"
#include "particle_sim_info.h"
#include "film_comm.h"
#include "seed_sp.h"
#include "wsurfel_comm.h"
#include "atomic_ref.h"
#ifdef BUILD_GPU
#include "gpu_comm_groups.h"
#endif

constexpr cSTRAND_MGR_PMASK::cSTRAND_MGR_PMASK():m_masks(){

//Code below initializes the m_masks array to the following
// sequence

//  { 0x0, 0x1, 0x3, 0x7,
//    0xf, 0x1f, 0x3f, 0x7f,
//    0xff, 0x1ff, 0x3ff, 0x7ff,
//    0xfff, 0x1fff, 0x3fff, 0x7fff,
//    0xffff, 0x1ffff, 0x3ffff, 0x7ffff,
//    0xfffff, 0x1fffff, 0x3fffff .... }
  STRAND_MASK pmask;
  ccDOTIMES(strand, N_STRANDS) {
    m_masks[strand] = pmask;
    pmask.set(strand);
  }
}

STRAND_MASK cSTRAND_MGR_PMASK::operator[](asINT32 strand_index) const {
  return m_masks[strand_index];
}

const cSTRAND_MGR_PMASK cSTRAND_MGR::m_pmask;

// Global variables
//These variables are declared extern in strand_global.h
cSTRAND_MGR g_strand_mgr;
sSYNC_THREADS g_sync_threads;
asINT32 g_running_strand       = 0;
asINT32 g_surrender_count      = 0;


/*=================================================================================================
 * STRAND_MGR::INIT
 *================================================================================================*/
VOID cSTRAND_MGR::init()
{

  allocate_strands();
  m_comm_thread_started = FALSE;
  m_runstate_mask.reset_all();
  m_timestep_last_events_unlocked = false;
  m_received_sim_finished_msg = false;
  m_exit = false;
  m_halt = false;
  m_full_ckpt_done = false;
  g_running_strand = NO_STRAND;

  init_recv_dependency_list();
  init_recv_consumer_list();
  init_recv_consumer_counts();
  init_slists_from_plists();
  reset_m_n_mlrf_deps(TRUE);
  init_unpacking_lists();
  init_collectives();
  init_collective_lists();
  init_collective_consumer_counts();

  // These should be regarded as completed until the first set of receives has been posted.
  m_mlrf_comm.init();
  m_fan_comm.init();
  
  init_mutexs_and_event_flags();

}

VOID cSTRAND_MGR::init_collectives() {
#if BUILD_D19_LATTICE
  if (sim.is_pf_model) {
    ccDOTIMES(cg, N_COLL_TYPES) {
      m_coll_list[cg].reset_all();
      switch(cg + N_RECV_TYPES) {
      case PHASE_FIELD_PRESSURE_REDUCTION_TYPE:
        m_collective_group[cg] = xnew sPHASE_FIELD_COLLECTIVE_GROUP();
        break;
      default:
        msg_internal_error("Unknown collective type");
      }
    }
  }
#endif
}

/*=================================================================================================
 * INIT RECV DEPENDENCY LISTS
 * Note: The FRINGE_NEARBLK and FRINGE_FARBLK dependencies for NEARBLK_RECV_TYPE are redundant,
 * since both those strands have FRINGE_SURF as a predecessor.
 *================================================================================================*/
VOID cSTRAND_MGR::init_recv_dependency_list(){
  // When conduction is active, the dependency of FRINGE_SURF on NEARBLK_RECV 
  // moves to FRINGE_WSURF_SAMPLING. See "if (sim.is_conduction_model)" section.
  // 
  if (is_sliding_mesh_present()) {
    m_dlist[NEARBLK_RECV_TYPE]   = SLIDING_SURF_A_STMASK | FRINGE_SURF_STMASK |
                                   FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  } else {
    m_dlist[NEARBLK_RECV_TYPE]   = FRINGE_SURF_STMASK | FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  }
  m_dlist[FARBLK_RECV_TYPE]      = FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  m_dlist[SURFEL_RECV_TYPE]      = FRINGE_NEARBLK_A_STMASK;
  if (sim.is_mme_comm_needed()) {
    m_dlist[NEARBLK_MME_RECV_TYPE] = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK;
    m_dlist[FARBLK_MME_RECV_TYPE]  = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK;
  }

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    m_dlist[NEARBLK_PORE_RECV_TYPE] = FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK;
    m_dlist[FARBLK_PORE_RECV_TYPE]  = FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK; 
  }
#endif

  if (sim.is_movb_sim()) {
    m_dlist[NEARBLK_MME_RECV_TYPE] |= FRINGE_BSURFELS_STMASK;
    m_dlist[FARBLK_MME_RECV_TYPE] |= FRINGE_BSURFELS_STMASK;

    m_dlist[BSURFEL_RECV_TYPE] = FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
    m_dlist[NEARBLK_IB_BF_RECV_TYPE] = FRINGE_NEARBLK_B_STMASK;
    m_dlist[FARBLK_IB_BF_RECV_TYPE] = FRINGE_FARBLK_B_STMASK;
    m_dlist[NEARBLK_IB_BF_BCAST_RECV_TYPE] = TIME_UPDATE_STMASK;
    m_dlist[FARBLK_IB_BF_BCAST_RECV_TYPE] = TIME_UPDATE_STMASK;
  }

  if (sim.is_particle_model) {
    m_dlist[PARCEL_RECV_TYPE] = INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK |
                                FRINGE_SURF_STMASK | FRINGE_NEARBLK_A_STMASK |
                                FRINGE_FARBLK_A_STMASK | TIME_UPDATE_STMASK;
    if (is_sliding_mesh_present()) 
      m_dlist[PARCEL_RECV_TYPE] |= SLIDING_SURF_A_STMASK;
        
    m_dlist[FILM_RECV_TYPE] = INTERIOR_FILM_KINEMATICS_STMASK;
  }

  if (sim.is_pf_model) {
    m_dlist[PHASE_FIELD_PRESSURE_REDUCTION_TYPE] = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK |
                                                   FRINGE2_NEARBLK_B_STMASK | INTERIOR_NS_B_STMASK |
						   INTERIOR_FARBLK2_B_STMASK | INTERIOR_FARBLK1_B_STMASK;//add all B strand
  }

  if (sim.is_conduction_model) {
    // The dependency of FRINGE_SURF on NEARBLK_RECV moves to FRINGE_WSURF_SAMPLING
    m_dlist[NEARBLK_RECV_TYPE] ^=  FRINGE_SURF_STMASK;
    m_dlist[NEARBLK_RECV_TYPE] |=  FRINGE_WSURF_SAMPLING_STMASK;
   if (sim.is_particle_model) {
      m_dlist[PARCEL_RECV_TYPE] |= FRINGE_WSURF_SAMPLING_STMASK;
    }

    m_dlist[WSURFEL_RECV_TYPE]     = FRINGE2_WSURF_HFC_STMASK | FRINGE_SURF_STMASK | INTERIOR_NS_A_STMASK;
    m_dlist[CONTACT_RECV_TYPE]     = FRINGE2_WSURF_HFC_STMASK | FRINGE_SURF_STMASK | INTERIOR_NS_A_STMASK;

  }
}

/*=================================================================================================
 * INIT RECEIVE CONSUMER LIST
 * Receive consumer lists - i.e. dependency lists before elimination of redundancies.
 *================================================================================================*/

VOID cSTRAND_MGR::init_recv_consumer_list(){
  m_clist[NEARBLK_RECV_TYPE]     = FRINGE_SURF_STMASK | FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  if (is_sliding_mesh_present())
    m_clist[NEARBLK_RECV_TYPE]  |= SLIDING_SURF_A_STMASK;

  m_clist[FARBLK_RECV_TYPE]      = FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  m_clist[SURFEL_RECV_TYPE]      = FRINGE_SURF_STMASK | FRINGE_NEARBLK_A_STMASK;
  if (sim.is_mme_comm_needed()) {
    // Cannot unpack ublk dyn data until fringe nearblk and farblk B strands have
    // used density and velocities received from ublk post advect mme data
    m_clist[NEARBLK_RECV_TYPE]    |= FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK;
    m_clist[FARBLK_RECV_TYPE]     |= FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK;

    // Cannot unpack mme data from nearblks until fringe nearblk A strand, fringe
    // farblk A strand and fringe surfel strand has used density and velocities received
    // from ublk post dyn data. Also need fringe farblk B and fringe nearblk B strands here
    // so that mme data is only unpacked once per timestep.
    m_clist[NEARBLK_MME_RECV_TYPE] = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK | FRINGE_SURF_STMASK
                                     | FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
    m_clist[FARBLK_MME_RECV_TYPE]  = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK
                                     | FRINGE_NEARBLK_A_STMASK | FRINGE_FARBLK_A_STMASK;
  }

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    m_clist[NEARBLK_PORE_RECV_TYPE] = FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK | FRINGE_SURF_STMASK;
    m_clist[FARBLK_PORE_RECV_TYPE]  = FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK;
  }
#endif

  if (sim.is_movb_sim()) {
    m_clist[NEARBLK_MME_RECV_TYPE] |= FRINGE_BSURFELS_STMASK;
    m_clist[FARBLK_MME_RECV_TYPE] |= FRINGE_BSURFELS_STMASK;

    m_clist[BSURFEL_RECV_TYPE] = FRINGE_NEARBLK_B_STMASK | FRINGE_FARBLK_B_STMASK | FRINGE_BSURFELS_STMASK | TIME_UPDATE_STMASK;
    m_clist[NEARBLK_IB_BF_RECV_TYPE] = FRINGE_NEARBLK_B_STMASK  | FRINGE_BSURFELS_STMASK;
    m_clist[FARBLK_IB_BF_RECV_TYPE] = FRINGE_FARBLK_B_STMASK  | FRINGE_BSURFELS_STMASK;
    m_clist[NEARBLK_IB_BF_BCAST_RECV_TYPE] = FRINGE_BSURFELS_STMASK | FRINGE_NEARBLK_B_STMASK | TIME_UPDATE_STMASK;
    m_clist[FARBLK_IB_BF_BCAST_RECV_TYPE] = FRINGE_BSURFELS_STMASK | FRINGE_FARBLK_B_STMASK | TIME_UPDATE_STMASK;

    m_clist[FARBLK_RECV_TYPE] |= TIME_UPDATE_STMASK;
    m_clist[NEARBLK_RECV_TYPE] |= TIME_UPDATE_STMASK;
  }

  if (sim.is_particle_model) {
    m_clist[PARCEL_RECV_TYPE] = INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK |
                                FRINGE_SURF_STMASK | FRINGE_NEARBLK_A_STMASK |
                                FRINGE_FARBLK_A_STMASK | TIME_UPDATE_STMASK;
    m_clist[FILM_RECV_TYPE] = INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK;
  }

  if (sim.is_pf_model) {
    m_clist[PHASE_FIELD_PRESSURE_REDUCTION_TYPE] = TIME_UPDATE_STMASK;
  }

  if (sim.is_conduction_model) {

    m_clist[NEARBLK_RECV_TYPE] |= FRINGE_WSURF_SAMPLING_STMASK;
    m_clist[SURFEL_RECV_TYPE] |= FRINGE_WSURF_SAMPLING_STMASK;
    if (sim.is_mme_comm_needed()) {
        m_clist[NEARBLK_MME_RECV_TYPE] |= FRINGE_WSURF_SAMPLING_STMASK; 
    }
#if BUILD_5G_LATTICE
    if (sim.is_large_pore) {
      m_clist[NEARBLK_PORE_RECV_TYPE] |= FRINGE_WSURF_SAMPLING_STMASK;
    }
#endif
    if (sim.is_particle_model) {
       m_clist[PARCEL_RECV_TYPE]  |= FRINGE_WSURF_SAMPLING_STMASK;
    }

    m_clist[WSURFEL_RECV_TYPE]     = FRINGE2_WSURF_HFC_STMASK | FRINGE_SURF_STMASK | INTERIOR_NS_A_STMASK;
    m_clist[CONTACT_RECV_TYPE]     = FRINGE2_WSURF_HFC_STMASK | FRINGE_SURF_STMASK | INTERIOR_NS_A_STMASK;

  }

}

/*=================================================================================================
 * INIT RECEIVE CONSUMER COUNTS
 * Determine number of active consumers of receives
 *================================================================================================*/
VOID cSTRAND_MGR::init_recv_consumer_counts(){
  uINT16 total_dependency_count[N_RECV_TYPES];

  ccDOTIMES(rtype, N_RECV_TYPES) {
    m_total_consumer_count[rtype] = 0;
    total_dependency_count[rtype] = 0;
    ccDOTIMES(strand, N_STRANDS) {
      if (m_clist[rtype].test(strand)) {
        m_total_consumer_count[rtype]++;
      }
      if (m_dlist[rtype].test(strand)) {
        total_dependency_count[rtype]++;
      }
    }
    m_active_consumers_count[rtype] = xnew uINT16[sim.num_scales];
    // For timestep 0, we subtract the dependency count from the consumer count
    // to ensure all strands fire off correctly
    ccDOTIMES(scale, sim.num_scales) {
      // The FRINGE_NEARBLK strand consumes ghost surfels in the same timestep
      // they are received; it is not included in the initial count, because it will be included
      // when the count is reset to the total midway through the first timestep
      // FRINGE_NEARBLK_B and FRINGE_FARBLK_B consume current MME data. Consumer count
      // will set to 2 when the MME data is received.
      // FRINGE_NEARBLK_B and FRINGE_FARBLK_B consume current IB_BF data. Consumer count
      // will set to 2 when the IB_BF data is received.
      if ( does_recv_satisfy_same_timestep_dep((RECV_TYPE)rtype) ) {
        m_active_consumers_count[rtype][scale] = m_total_consumer_count[rtype] - total_dependency_count[rtype];
        //assuming that the simulation restart from a synchronized flow-conduction timestep
        bool is_cond_and_flow_sim = sim.is_conduction_model && sim.is_flow;
        if (rtype == WSURFEL_RECV_TYPE && ( (g_timescale.is_this_sp_realm_subcycled(sim.is_conduction_sp, is_cond_and_flow_sim) ||
                                           scale < compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE))
				           && !(sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore)  )) {
          //Adds additional consumer to block the count in those time-steps where only one of the two realms are active,
          //which happens only in the realm with smaller time steps (subcycled)
          m_active_consumers_count[rtype][scale]++;
        }

      } else {
        m_active_consumers_count[rtype][scale] = m_total_consumer_count[rtype];
      }
        LOG_MSG_IF(rtype == WSURFEL_RECV_TYPE, "RECV_DEPEND",LOG_FUNC,LOG_ATTR(rtype),LOG_ATTR(scale)).format( "m_total_consumer_count {} total_dependency_count {} active cc {}",m_total_consumer_count[rtype], total_dependency_count[rtype],
            m_active_consumers_count[rtype][scale]);
    }
  }
}

/*=================================================================================================
 * INIT collective CONSUMER COUNTS
 * Determine number of active consumers of collectives
 *================================================================================================*/
VOID cSTRAND_MGR::init_collective_consumer_counts(){
#if BUILD_D19_LATTICE
  if (sim.is_pf_model) {
    m_collective_consumers_count = xnew uINT16[N_COLL_TYPES];
    
    ccDOTIMES(ctype, N_COLL_TYPES) {
      m_total_consumer_count[ctype + N_RECV_TYPES] = 0;
      ccDOTIMES(strand, N_STRANDS) {
	if (m_clist[ctype + N_RECV_TYPES].test(strand))
	  m_total_consumer_count[ctype + N_RECV_TYPES]++;
      }
      m_collective_consumers_count[ctype] = m_total_consumer_count[ctype + N_RECV_TYPES];
    }
  }
#endif
}

/*=================================================================================================
 * RESET MLRF DEPENDENCIES
 * Called once during cSTRAND_MGR initialization and after during complete_timestep
 *================================================================================================*/
VOID cSTRAND_MGR::reset_m_n_mlrf_deps(BOOLEAN init_all_strands){

  if( init_all_strands ) {
    ccDOTIMES(successor_strand, N_STRANDS) {
      m_n_mlrf_deps[successor_strand] = 0;
    }
  }

  if (is_sliding_mesh_present()) {
    m_n_mlrf_deps[SLIDING_SURF_B_STRAND] = 2;
    m_n_mlrf_deps[SLIDING_SURF_C_STRAND] = 1;
  }
}

/*=================================================================================================
 * ALLOCATE_STRANDS
 *================================================================================================*/
VOID cSTRAND_MGR::allocate_strands(){
  m_strands[SLIDING_SURF_A_STRAND]     = new sSLIDING_SURFELS_A_STRAND(SLIDING_SURF_A_STRAND);
  m_strands[SLIDING_SURF_B_STRAND]     = new sSLIDING_SURFELS_B_STRAND(SLIDING_SURF_B_STRAND);
  m_strands[FRINGE2_SURF_STRAND]       = new sFRINGE2_SURFELS_STRAND(FRINGE2_SURF_STRAND,
                                                                     FRINGE2_SURFEL_BASE_GROUP_TYPE);
  m_strands[FRINGE_SURF_STRAND]        = new sFRINGE_SURFELS_STRAND(FRINGE_SURF_STRAND,
                                                                    FRINGE_SURFEL_BASE_GROUP_TYPE);
  m_strands[SLIDING_SURF_C_STRAND]     = new sSLIDING_SURFELS_C_STRAND(SLIDING_SURF_C_STRAND);
  m_strands[FRINGE_NEARBLK_A_STRAND]   = new sFRINGE_NEARBLKS_A_STRAND(FRINGE_NEARBLK_A_STRAND,
                                                                       FRINGE_NEARBLK_GROUP_TYPE);
  m_strands[FRINGE_FARBLK_A_STRAND]    = new sFRINGE_FARBLKS_A_STRAND(FRINGE_FARBLK_A_STRAND,
                                                                      FRINGE_FARBLK_GROUP_TYPE);
  m_strands[SLIDING_NEARBLK_A_STRAND]  = new sSLIDING_NEARBLKS_A_STRAND(SLIDING_NEARBLK_A_STRAND,
                                                                        SLIDING_NEARBLK_GROUP_TYPE);
  m_strands[FRINGE2_NEARBLK_A_STRAND]  = new sFRINGE2_NEARBLKS_A_STRAND(FRINGE2_NEARBLK_A_STRAND,
                                                                        FRINGE2_NEARBLK_GROUP_TYPE);
  m_strands[INTERIOR_NS_A_STRAND]      = new sINTERIOR_NEAR_SHOBS_A_STRAND(INTERIOR_NS_A_STRAND,
                                                                           INTERIOR_NEARBLK_GROUP_TYPE,
                                                                           INTERIOR_SURFEL_BASE_GROUP_TYPE);
  m_strands[INTERIOR_FARBLK2_A_STRAND] = new sINTERIOR_FARBLKS2_A_STRAND(INTERIOR_FARBLK2_A_STRAND,
                                                                         INTERIOR_FARBLK2_GROUP_TYPE);
  m_strands[INTERIOR_FARBLK1_A_STRAND] = new sINTERIOR_FARBLKS1_A_STRAND(INTERIOR_FARBLK1_A_STRAND,
                                                                         INTERIOR_FARBLK1_GROUP_TYPE);

  if (sim.is_radiation_model) {
    m_strands[RADIATION_STRAND] = new sRADIATION_STRAND();
  }

  if (sim.is_movb_sim()) {
    m_strands[FRINGE_BSURFELS_STRAND]    = new sFRINGE_BSURFELS_STRAND(FRINGE_BSURFELS_STRAND);
    m_strands[FRINGE2_BSURFELS_STRAND]   = new sFRINGE2_BSURFELS_STRAND(FRINGE2_BSURFELS_STRAND);
    m_strands[INTERIOR_BSURFELS_STRAND]  = new sINTERIOR_BSURFELS_STRAND(INTERIOR_BSURFELS_STRAND);
  }

  if (sim.is_mme_comm_needed()) {
    m_strands[FRINGE_NEARBLK_B_STRAND]   = new sFRINGE_NEARBLKS_B_STRAND(FRINGE_NEARBLK_B_STRAND,
                                                                         FRINGE_NEARBLK_GROUP_TYPE);
    m_strands[FRINGE_FARBLK_B_STRAND]    = new sFRINGE_FARBLKS_B_STRAND(FRINGE_FARBLK_B_STRAND,
                                                                        FRINGE_FARBLK_GROUP_TYPE);
    m_strands[SLIDING_NEARBLK_B_STRAND]  = new sSLIDING_NEARBLKS_B_STRAND(SLIDING_NEARBLK_B_STRAND,
                                                                          SLIDING_NEARBLK_GROUP_TYPE);
    m_strands[FRINGE2_NEARBLK_B_STRAND]  = new sFRINGE2_NEARBLKS_B_STRAND(FRINGE2_NEARBLK_B_STRAND,
                                                                          FRINGE2_NEARBLK_GROUP_TYPE);
    m_strands[INTERIOR_NS_B_STRAND]      = new sINTERIOR_NEAR_SHOBS_B_STRAND(INTERIOR_NS_B_STRAND,
                                                                             INTERIOR_NEARBLK_GROUP_TYPE,
                                                                             INTERIOR_SURFEL_BASE_GROUP_TYPE);
    m_strands[INTERIOR_FARBLK2_B_STRAND] = new sINTERIOR_FARBLKS2_B_STRAND(INTERIOR_FARBLK2_B_STRAND,
                                                                           INTERIOR_FARBLK2_GROUP_TYPE);
    m_strands[INTERIOR_FARBLK1_B_STRAND] = new sINTERIOR_FARBLKS1_B_STRAND(INTERIOR_FARBLK1_B_STRAND,
                                                                           INTERIOR_FARBLK1_GROUP_TYPE);
  }
  if (sim.is_particle_model) {
    m_strands[INTERIOR_FILM_ACCUMULATION_STRAND]       = new sFILM_ACCUMULATION_STRAND(INTERIOR_FILM_ACCUMULATION_STRAND,
                                                                                   INTERIOR_SURFEL_BASE_GROUP_TYPE);
    m_strands[INTERIOR_FILM_KINEMATICS_STRAND]         = new sFILM_KINEMATICS_STRAND(INTERIOR_FILM_KINEMATICS_STRAND,
                                                                                 INTERIOR_SURFEL_BASE_GROUP_TYPE);
  }
  if (sim.is_conduction_model) {
    m_strands[FRINGE2_WSURF_HFC_STRAND]            = new sFRINGE2_WSURFELS_HFC_STRAND(FRINGE2_WSURF_HFC_STRAND,
                                                                     FRINGE2_SURFEL_BASE_GROUP_TYPE);
    m_strands[FRINGE_WSURF_SAMPLING_STRAND]       = new sFRINGE_WSURFELS_SAMPLING_STRAND(FRINGE_WSURF_SAMPLING_STRAND,
                                                                     FRINGE_SURFEL_BASE_GROUP_TYPE);
    m_strands[INTERIOR_WSURF_SAMPLING_STRAND]       = new sINTERIOR_WSURFELS_SAMPLING_STRAND(INTERIOR_WSURF_SAMPLING_STRAND,
                                                                     INTERIOR_SURFEL_BASE_GROUP_TYPE);
  }

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    m_strands[FRINGE_NEARBLK_C_STRAND]   = new sFRINGE_NEARBLKS_C_STRAND(FRINGE_NEARBLK_C_STRAND,
                                                                         FRINGE_NEARBLK_GROUP_TYPE);
    m_strands[FRINGE_FARBLK_C_STRAND]    = new sFRINGE_FARBLKS_C_STRAND(FRINGE_FARBLK_C_STRAND,
                                                                        FRINGE_FARBLK_GROUP_TYPE);
    m_strands[SLIDING_NEARBLK_C_STRAND]  = new sSLIDING_NEARBLKS_C_STRAND(SLIDING_NEARBLK_C_STRAND,
                                                                          SLIDING_NEARBLK_GROUP_TYPE);
    m_strands[FRINGE2_NEARBLK_C_STRAND]  = new sFRINGE2_NEARBLKS_C_STRAND(FRINGE2_NEARBLK_C_STRAND,
                                                                          FRINGE2_NEARBLK_GROUP_TYPE);
    m_strands[INTERIOR_NS_C_STRAND]      = new sINTERIOR_NEAR_SHOBS_C_STRAND(INTERIOR_NS_C_STRAND,
                                                                             INTERIOR_NEARBLK_GROUP_TYPE,
                                                                             INTERIOR_SURFEL_BASE_GROUP_TYPE);
    m_strands[INTERIOR_FARBLK2_C_STRAND] = new sINTERIOR_FARBLKS2_C_STRAND(INTERIOR_FARBLK2_C_STRAND,
                                                                           INTERIOR_FARBLK2_GROUP_TYPE);
    m_strands[INTERIOR_FARBLK1_C_STRAND] = new sINTERIOR_FARBLKS1_C_STRAND(INTERIOR_FARBLK1_C_STRAND,
                                                                           INTERIOR_FARBLK1_GROUP_TYPE);
  }
#endif
  m_strands[TIMESTEP_UPDATE_STRAND]    = new sTIMESTEP_UPDATE_STRAND(TIMESTEP_UPDATE_STRAND);
}

/*=================================================================================================
 * INIT UNPACKING LISTS
 * Ghost updates that cannot be processed immediately are pushed to this list
 *================================================================================================*/
VOID cSTRAND_MGR::init_unpacking_lists(){
  //m_unpacking_list.resize(N_RECV_TYPES, std::vector<std::list<sRECV_GROUP_BASE*>>(sim.num_scales));
  // Unpacking lists
  ccDOTIMES(rtype, N_RECV_TYPES) {
    m_unpacking_list_head[rtype] = xnew SP_RECV_GROUP_BASE[sim.num_scales];
    ccDOTIMES(scale, sim.num_scales) {
      m_unpacking_list_head[rtype][scale] = NULL;
    }
  }
}

/*=================================================================================================
 * INIT collective LISTS
 * Ghost updates that cannot be processed immediately are pushed to this list
 *================================================================================================*/
VOID cSTRAND_MGR::init_collective_lists(){
#if BUILD_D19_LATTICE
  if (sim.is_pf_model) {
    // collective lists
    m_collective_list_head = xnew COLLECTIVE_GROUP[N_COLL_TYPES];
    ccDOTIMES(ctype, N_COLL_TYPES) {
      m_collective_list_head[ctype] = NULL;
    }
  }
#endif
}

/*=================================================================================================
 * INIT MUTEXES and EVENT flags
 *================================================================================================*/
VOID cSTRAND_MGR::init_mutexs_and_event_flags(){
  // Mutexes, condition variables, etc.
  pthread_mutex_init(&m_time_update_mutex,NULL);
  pthread_mutex_init(&m_dcntr_mutex,NULL);
  pthread_mutex_init(&m_active_consumers_mutex,NULL);
  AsyncEventQueuePendingRequest::init();

}

/*=================================================================================================
 * FINISH INIT
 *================================================================================================*/
VOID cSTRAND_MGR::finish_init()
{

}

/*=================================================================================================
 * INIT SLISTS FROM PLISTS
 * Computes successor lists based on hardcoded predecssor lists
 *================================================================================================*/
VOID cSTRAND_MGR::init_slists_from_plists(){
  init_plists();
  translate_plists_to_slists();
}

VOID cSTRAND_MGR::translate_plists_to_slists(){
  // Populate m_slist values with with hardcoded pList values
  ccDOTIMES(pStrand, N_STRANDS) {
    // Assume current strand has no successors to begin with
    m_slist[pStrand].reset_all();

    // Loop through all strands and add to successor list if pStrand is a contributor
    ccDOTIMES(strand, N_STRANDS) {
      if ( strand == pStrand ) continue;
      //Is pStrand a predecessor of strand? If yes, then add strand to its successor;
      if ( m_plist[strand].test(pStrand) ){
        m_slist[pStrand].set(strand);
      }
    }
  }
}

VOID cSTRAND_MGR::init_plists(){
  if (sim.is_mme_comm_needed()) {
    // strict scheduling for transonic and 5g with bsurfels
    if (getenv("EXA_USE_STRICT_SCHEDULING")) {
      init_plists_for_strict_scheduling_with_mme_comm();
    }
    else {
      init_plists_for_dynamic_scheduling_with_mme_comm();
    }
  }
  else {
    if (getenv("EXA_USE_STRICT_SCHEDULING")) {
      init_plists_for_strict_scheduling_without_mme_comm();
    }
    else {
      init_plists_for_dynamic_scheduling_without_mme_comm();
    }
  }
}

VOID cSTRAND_MGR::init_plists_for_strict_scheduling_with_mme_comm(){
  if (my_proc_id == 0) {
    msg_print_no_prefix("Using mme strict scheduling of sim strands");
  }

  // Consider a strand, say SSA. If for strict scheduling this strand
  // has all active strands as its successor, then in essence, SSA is a predecessor
  // for every other active strand. Following this logic, every strand other than
  // SSA is also its predecessor.
  STRAND_MASK ACTIVE_STRANDS_STMASK =  ALL_STRANDS_STMASK;

  if (!sim.is_radiation_model) {
    ACTIVE_STRANDS_STMASK ^= RADIATION_STMASK;
  }

  if (!sim.is_movb_sim() ) {
    //Active strands are all strands except the FringeBSurfelsStrand,
    //Fringe2BSurfelsStrand and the InteriorBSurferlsStrand
    ACTIVE_STRANDS_STMASK ^= (FRINGE_BSURFELS_STMASK | FRINGE2_BSURFELS_STMASK | INTERIOR_BSURFELS_STMASK);
  }

  if (!sim.is_particle_model) {
    ACTIVE_STRANDS_STMASK ^= (INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK);
  }
  if (!sim.is_conduction_model) {
    ACTIVE_STRANDS_STMASK ^= (FRINGE2_WSURF_HFC_STMASK | FRINGE_WSURF_SAMPLING_STMASK | INTERIOR_WSURF_SAMPLING_STMASK);
  }

  
#if BUILD_5G_LATTICE
  if (!sim.is_large_pore) { //no C_STRAND
    ACTIVE_STRANDS_STMASK ^= (FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK
			      | SLIDING_NEARBLK_C_STMASK | FRINGE2_NEARBLK_C_STMASK | INTERIOR_NS_C_STMASK
			      | INTERIOR_FARBLK2_C_STMASK | INTERIOR_FARBLK1_C_STMASK);
  }
#endif

  m_plist[SLIDING_SURF_A_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_A_STMASK;
  m_plist[SLIDING_SURF_B_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_B_STMASK;
  if (sim.is_particle_model) {
    m_plist[INTERIOR_FILM_ACCUMULATION_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_ACCUMULATION_STMASK;
    m_plist[INTERIOR_FILM_KINEMATICS_STRAND]   = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_KINEMATICS_STMASK;
  }
  m_plist[FRINGE_SURF_STRAND]        = ACTIVE_STRANDS_STMASK ^ FRINGE_SURF_STMASK;
  m_plist[SLIDING_SURF_C_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_C_STMASK;
  m_plist[FRINGE_NEARBLK_A_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_NEARBLK_A_STMASK;
  m_plist[FRINGE_FARBLK_A_STRAND]    = ACTIVE_STRANDS_STMASK ^ FRINGE_FARBLK_A_STMASK;
  m_plist[SLIDING_NEARBLK_A_STRAND]  = ACTIVE_STRANDS_STMASK ^ SLIDING_NEARBLK_A_STMASK;
  m_plist[FRINGE2_NEARBLK_A_STRAND]  = ACTIVE_STRANDS_STMASK ^ FRINGE2_NEARBLK_A_STMASK;
  m_plist[INTERIOR_NS_A_STRAND]      = ACTIVE_STRANDS_STMASK ^ INTERIOR_NS_A_STMASK;

  if (sim.is_radiation_model) {
    m_plist[RADIATION_STRAND]   = ACTIVE_STRANDS_STMASK ^ RADIATION_STMASK;
  }

  m_plist[INTERIOR_FARBLK2_A_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK2_A_STMASK;
  m_plist[INTERIOR_FARBLK1_A_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK1_A_STMASK;
  if (sim.is_movb_sim()) {
    m_plist[FRINGE2_BSURFELS_STRAND] = ACTIVE_STRANDS_STMASK ^ FRINGE2_BSURFELS_STMASK;
    m_plist[FRINGE_BSURFELS_STRAND]  = ACTIVE_STRANDS_STMASK ^ FRINGE_BSURFELS_STMASK;
    m_plist[INTERIOR_BSURFELS_STRAND]= ACTIVE_STRANDS_STMASK ^ INTERIOR_BSURFELS_STMASK;
  }
  m_plist[FRINGE_NEARBLK_B_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_NEARBLK_B_STMASK;
  m_plist[FRINGE_FARBLK_B_STRAND]    = ACTIVE_STRANDS_STMASK ^ FRINGE_FARBLK_B_STMASK;
  m_plist[SLIDING_NEARBLK_B_STRAND]  = ACTIVE_STRANDS_STMASK ^ SLIDING_NEARBLK_B_STMASK;
  m_plist[FRINGE2_NEARBLK_B_STRAND]  = ACTIVE_STRANDS_STMASK ^ FRINGE2_NEARBLK_B_STMASK;
  m_plist[INTERIOR_NS_B_STRAND]      = ACTIVE_STRANDS_STMASK ^ INTERIOR_NS_B_STMASK;
  m_plist[INTERIOR_FARBLK2_B_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK2_B_STMASK;
  m_plist[INTERIOR_FARBLK1_B_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK1_B_STMASK;

  if (sim.is_conduction_model) {
    m_plist[FRINGE2_WSURF_HFC_STRAND]       = ACTIVE_STRANDS_STMASK ^ FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE_WSURF_SAMPLING_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_WSURF_SAMPLING_STMASK;
    m_plist[INTERIOR_WSURF_SAMPLING_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_WSURF_SAMPLING_STMASK;
  }

  if (sim.is_radiation_model) {
    // Cannot run surfel dynamics until old radiation values have been sent
    m_plist[RADIATION_STRAND]  |= ACTIVE_STRANDS_STMASK ^ RADIATION_STMASK;
  }

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    m_plist[FRINGE_NEARBLK_C_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_NEARBLK_C_STMASK;
    m_plist[FRINGE_FARBLK_C_STRAND]    = ACTIVE_STRANDS_STMASK ^ FRINGE_FARBLK_C_STMASK;
    m_plist[SLIDING_NEARBLK_C_STRAND]  = ACTIVE_STRANDS_STMASK ^ SLIDING_NEARBLK_C_STMASK;
    m_plist[FRINGE2_NEARBLK_C_STRAND]  = ACTIVE_STRANDS_STMASK ^ FRINGE2_NEARBLK_C_STMASK;
    m_plist[INTERIOR_NS_C_STRAND]      = ACTIVE_STRANDS_STMASK ^ INTERIOR_NS_C_STMASK;
    m_plist[INTERIOR_FARBLK2_C_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK2_C_STMASK;
    m_plist[INTERIOR_FARBLK1_C_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK1_C_STMASK;
  }
#endif
  m_plist[TIMESTEP_UPDATE_STRAND]    = ACTIVE_STRANDS_STMASK ^ TIME_UPDATE_STMASK;
}

VOID cSTRAND_MGR::init_plists_for_dynamic_scheduling_with_mme_comm(){
  if (my_proc_id == 0) {
    msg_print_no_prefix("Using mme dynamic scheduling of sim strands");
  }
  STRAND_MASK ACTIVE_STRANDS_STMASK = ALL_STRANDS_STMASK;

  if (!sim.is_radiation_model) {
    ACTIVE_STRANDS_STMASK ^= RADIATION_STMASK;
  }

  if (!sim.is_movb_sim() ) {
    ACTIVE_STRANDS_STMASK ^= (FRINGE_BSURFELS_STMASK | FRINGE2_BSURFELS_STMASK | INTERIOR_BSURFELS_STMASK);
  }

  if (!sim.is_particle_model) {
    ACTIVE_STRANDS_STMASK ^= (INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK);
  }

  if (!sim.is_conduction_model) {
    ACTIVE_STRANDS_STMASK ^= (FRINGE2_WSURF_HFC_STMASK | FRINGE_WSURF_SAMPLING_STMASK | INTERIOR_WSURF_SAMPLING_STMASK);
  }

#if BUILD_5G_LATTICE
  if (!sim.is_large_pore) {
    ACTIVE_STRANDS_STMASK  ^= (FRINGE_NEARBLK_C_STMASK | FRINGE_FARBLK_C_STMASK | SLIDING_NEARBLK_C_STMASK
			       | FRINGE2_NEARBLK_C_STMASK | INTERIOR_NS_C_STMASK | INTERIOR_FARBLK2_C_STMASK | INTERIOR_FARBLK1_C_STMASK);
  }
#endif

  m_plist[SLIDING_SURF_A_STRAND]     = TIME_UPDATE_STMASK;
  m_plist[SLIDING_SURF_B_STRAND]     = TIME_UPDATE_STMASK | SLIDING_SURF_A_STMASK;
  m_plist[FRINGE2_SURF_STRAND]       = TIME_UPDATE_STMASK;
  m_plist[FRINGE_SURF_STRAND]        = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;

  m_plist[SLIDING_SURF_C_STRAND]     = TIME_UPDATE_STMASK | FRINGE_SURF_STMASK |
                                       SLIDING_SURF_B_STMASK;

  m_plist[FRINGE_NEARBLK_A_STRAND]   = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK |
                                       FRINGE_SURF_STMASK | SLIDING_SURF_C_STMASK;

  m_plist[FRINGE_FARBLK_A_STRAND]    = TIME_UPDATE_STMASK;

  m_plist[SLIDING_NEARBLK_A_STRAND]  = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK |
                                       FRINGE_SURF_STMASK | SLIDING_SURF_C_STMASK;

  m_plist[FRINGE2_NEARBLK_A_STRAND]  = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK |
                                       FRINGE_SURF_STMASK | SLIDING_SURF_C_STMASK;

  m_plist[INTERIOR_NS_A_STRAND]      = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;

  if (sim.is_radiation_model) {

    // Techically, the radiation strands don't really depend on the surfel
    // strands. However, the strand_mgr currently doesn't have a good notion of
    // when the radiation recvs are done, so we don't want it to start the
    // radiation recv strand too early.


    m_plist[RADIATION_STRAND] = TIME_UPDATE_STMASK | RADIATION_STMASK | INTERIOR_NS_A_STMASK | FRINGE_SURF_STMASK | FRINGE2_SURF_STMASK;
  }

  m_plist[INTERIOR_FARBLK2_A_STRAND] = TIME_UPDATE_STMASK;
  m_plist[INTERIOR_FARBLK1_A_STRAND] = TIME_UPDATE_STMASK | INTERIOR_FARBLK2_A_STMASK | INTERIOR_NS_A_STMASK;

  if ( sim.is_movb_sim() ) {
    m_plist[FRINGE_BSURFELS_STRAND]   = TIME_UPDATE_STMASK  | FRINGE_NEARBLK_A_STMASK  | FRINGE_FARBLK_A_STMASK
                                                            | FRINGE2_BSURFELS_STMASK; // added for repeatability
    m_plist[FRINGE2_BSURFELS_STRAND]  = TIME_UPDATE_STMASK | FRINGE_NEARBLK_A_STMASK | FRINGE2_NEARBLK_A_STMASK
                                                          | FRINGE_FARBLK_A_STMASK  | INTERIOR_FARBLK2_A_STMASK;
    m_plist[INTERIOR_BSURFELS_STRAND] = TIME_UPDATE_STMASK | FRINGE2_NEARBLK_A_STMASK | INTERIOR_FARBLK2_A_STMASK
                                                           | INTERIOR_NS_A_STMASK | INTERIOR_FARBLK1_A_STMASK
                                                           | FRINGE2_BSURFELS_STMASK; // added for repeatability
  }

  m_plist[FRINGE_NEARBLK_B_STRAND]   = TIME_UPDATE_STMASK | FRINGE_FARBLK_A_STMASK |
                                       FRINGE_NEARBLK_A_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       INTERIOR_NS_A_STMASK;

  m_plist[FRINGE_FARBLK_B_STRAND]    = TIME_UPDATE_STMASK | FRINGE_FARBLK_A_STMASK |
                                       FRINGE_NEARBLK_A_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       INTERIOR_NS_A_STMASK;

  m_plist[SLIDING_NEARBLK_B_STRAND]  = TIME_UPDATE_STMASK | FRINGE_FARBLK_A_STMASK |
                                       FRINGE_NEARBLK_A_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       INTERIOR_NS_A_STMASK;

  m_plist[FRINGE2_NEARBLK_B_STRAND]  = TIME_UPDATE_STMASK | FRINGE_FARBLK_A_STMASK |
                                       FRINGE_NEARBLK_A_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK | INTERIOR_NS_A_STMASK;

  m_plist[INTERIOR_NS_B_STRAND]      = TIME_UPDATE_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       INTERIOR_NS_A_STMASK | FRINGE_FARBLK_A_STMASK | FRINGE_NEARBLK_A_STMASK;

  m_plist[INTERIOR_FARBLK2_B_STRAND] = TIME_UPDATE_STMASK | FRINGE2_NEARBLK_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       INTERIOR_NS_A_STMASK | INTERIOR_FARBLK1_A_STMASK |
                                       FRINGE_FARBLK_A_STMASK | FRINGE_NEARBLK_A_STMASK;

  //Swap ublks can be adjacent to Fringe, therefore MME comm has to complete before these
  //ublks can undergo dynamics. Hence they have the same dependency as INTERIOR_FARBLK2_B_STRAND
  m_plist[INTERIOR_FARBLK1_B_STRAND] = m_plist[INTERIOR_FARBLK2_B_STRAND];

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    m_plist[FRINGE_NEARBLK_C_STRAND]   = TIME_UPDATE_STMASK | FRINGE_FARBLK_B_STMASK |
                                         FRINGE_NEARBLK_B_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK |
                                         INTERIOR_NS_B_STMASK;

    m_plist[FRINGE_FARBLK_C_STRAND]    = TIME_UPDATE_STMASK | FRINGE_FARBLK_B_STMASK |
                                         FRINGE_NEARBLK_B_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK |
                                         INTERIOR_NS_B_STMASK;

    m_plist[SLIDING_NEARBLK_C_STRAND]  = TIME_UPDATE_STMASK | FRINGE_FARBLK_B_STMASK |
                                         FRINGE_NEARBLK_B_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK |
                                         INTERIOR_NS_B_STMASK;

    m_plist[FRINGE2_NEARBLK_C_STRAND]  = TIME_UPDATE_STMASK | FRINGE_FARBLK_B_STMASK |
                                         FRINGE_NEARBLK_B_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK | INTERIOR_NS_B_STMASK;

    m_plist[INTERIOR_NS_C_STRAND]      = TIME_UPDATE_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK |
                                         INTERIOR_NS_B_STMASK | FRINGE_FARBLK_B_STMASK | FRINGE_NEARBLK_B_STMASK;

    m_plist[INTERIOR_FARBLK2_C_STRAND] = TIME_UPDATE_STMASK | FRINGE2_NEARBLK_B_STMASK |
                                         INTERIOR_FARBLK2_B_STMASK | SLIDING_NEARBLK_B_STMASK |
                                         INTERIOR_NS_B_STMASK | INTERIOR_FARBLK1_B_STMASK |
                                         FRINGE_FARBLK_B_STMASK | FRINGE_NEARBLK_B_STMASK;

    m_plist[INTERIOR_FARBLK1_C_STRAND] = m_plist[INTERIOR_FARBLK2_C_STRAND];
    /* m_plist[INTERIOR_FARBLK1_C_STRAND] = TIME_UPDATE_STMASK | INTERIOR_FARBLK1_B_STMASK |
       INTERIOR_FARBLK2_B_STMASK | INTERIOR_NS_B_STMASK; */

  }
#endif

  if (sim.is_particle_model) {

    if (sim.is_film_solver ) {
      //Particle dynamics, performed from within these seven strands,
      //must never go before the reentrianment model in the film
      //kinematics strand.
      m_plist[FRINGE_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[FRINGE_SURF_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;   // Shear stress is calculated during surfel dynamics and is used during film kinematics.
      m_plist[FRINGE2_SURF_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;  // Surfel dynamics for wall surfels is performed in _SURF strands, Thus kinematics strand should be a predecessor of theses strands. 
      m_plist[FRINGE_FARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[SLIDING_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[FRINGE2_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_NS_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_FARBLK2_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_FARBLK1_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
    }
    
    m_plist[INTERIOR_FILM_ACCUMULATION_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_ACCUMULATION_STMASK;
    m_plist[INTERIOR_FILM_KINEMATICS_STRAND]   = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_KINEMATICS_STMASK;
  }


  m_plist[TIMESTEP_UPDATE_STRAND]    = ACTIVE_STRANDS_STMASK ^ TIME_UPDATE_STMASK;

  if (sim.is_radiation_model) {
    // Cannot run surfel dynamics until old radiation values have been sent
    // m_plist[FRINGE2_SURF_STRAND]  |= RADIATION_STMASK;
    // m_plist[FRINGE_SURF_STRAND]   |= RADIATION_STMASK;
    // m_plist[INTERIOR_NS_A_STRAND] |= RADIATION_STMASK;
  }

  if ( sim.is_movb_sim() ) {
    m_plist[FRINGE_NEARBLK_B_STRAND]   |= FRINGE_BSURFELS_STMASK;
    m_plist[FRINGE2_NEARBLK_B_STRAND]  |= FRINGE2_BSURFELS_STMASK | INTERIOR_BSURFELS_STMASK;
    m_plist[INTERIOR_FARBLK2_B_STRAND] |= FRINGE2_BSURFELS_STMASK | INTERIOR_BSURFELS_STMASK;
    m_plist[INTERIOR_NS_B_STRAND]      |= INTERIOR_BSURFELS_STMASK;
    m_plist[INTERIOR_FARBLK1_B_STRAND] = m_plist[INTERIOR_FARBLK2_B_STRAND];
  }
  
  if ( sim.is_conduction_model) {
    m_plist[FRINGE_WSURF_SAMPLING_STRAND]     = TIME_UPDATE_STMASK; 
    m_plist[INTERIOR_WSURF_SAMPLING_STRAND]   = TIME_UPDATE_STMASK;
    m_plist[FRINGE2_WSURF_HFC_STRAND]         = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;

    m_plist[FRINGE_SURF_STRAND]        |= (FRINGE_WSURF_SAMPLING_STMASK | FRINGE2_WSURF_HFC_STMASK);
    m_plist[INTERIOR_NS_A_STRAND]      |= (INTERIOR_WSURF_SAMPLING_STMASK | FRINGE2_WSURF_HFC_STMASK);

    m_plist[SLIDING_SURF_C_STRAND]     |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE_NEARBLK_A_STRAND]   |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[SLIDING_NEARBLK_A_STRAND]  |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE2_NEARBLK_A_STRAND]  |= FRINGE2_WSURF_HFC_STMASK;
  }
}

VOID cSTRAND_MGR::init_plists_for_strict_scheduling_without_mme_comm(){
  if (my_proc_id == 0) {
    msg_print_no_prefix("Using strict scheduling of sim strands");
  }

  // Consider a strand, say SSA. If for strict scheduling this strand
  // has all active strands as its successor, than in essence, SSA is a predecessor
  // for every other active strand. Following this logic, every strand other than
  // SSA is also its predecessor.
  STRAND_MASK ACTIVE_STRANDS_STMASK  = SLIDING_SURF_A_STMASK | SLIDING_SURF_B_STMASK |
                                       FRINGE2_SURF_STMASK   | FRINGE_SURF_STMASK |
                                       SLIDING_SURF_C_STMASK | FRINGE_NEARBLK_A_STMASK |
                                       FRINGE_FARBLK_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       FRINGE2_NEARBLK_A_STMASK | INTERIOR_NS_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK |INTERIOR_FARBLK1_A_STMASK |
                                       TIME_UPDATE_STMASK;
  if (sim.is_particle_model) {
    ACTIVE_STRANDS_STMASK |= INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK;
    m_plist[INTERIOR_FILM_ACCUMULATION_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_ACCUMULATION_STMASK;
    m_plist[INTERIOR_FILM_KINEMATICS_STRAND]   = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_KINEMATICS_STMASK;
  }

  if (sim.is_conduction_model) {
    ACTIVE_STRANDS_STMASK |= (FRINGE2_WSURF_HFC_STMASK | FRINGE_WSURF_SAMPLING_STMASK | INTERIOR_WSURF_SAMPLING_STMASK);
  }
  
  m_plist[SLIDING_SURF_A_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_A_STMASK;
  m_plist[SLIDING_SURF_B_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_B_STMASK;
  m_plist[FRINGE2_SURF_STRAND]       = ACTIVE_STRANDS_STMASK ^ FRINGE2_SURF_STMASK;
  m_plist[FRINGE_SURF_STRAND]        = ACTIVE_STRANDS_STMASK ^ FRINGE_SURF_STMASK;
  m_plist[SLIDING_SURF_C_STRAND]     = ACTIVE_STRANDS_STMASK ^ SLIDING_SURF_C_STMASK;
  m_plist[FRINGE_NEARBLK_A_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_NEARBLK_A_STMASK;
  m_plist[FRINGE_FARBLK_A_STRAND]    = ACTIVE_STRANDS_STMASK ^ FRINGE_FARBLK_A_STMASK;
  m_plist[SLIDING_NEARBLK_A_STRAND]  = ACTIVE_STRANDS_STMASK ^ SLIDING_NEARBLK_A_STMASK;
  m_plist[FRINGE2_NEARBLK_A_STRAND]  = ACTIVE_STRANDS_STMASK ^ FRINGE2_NEARBLK_A_STMASK;
  m_plist[INTERIOR_NS_A_STRAND]      = ACTIVE_STRANDS_STMASK ^ INTERIOR_NS_A_STMASK;
  m_plist[INTERIOR_FARBLK2_A_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK2_A_STMASK;
  m_plist[INTERIOR_FARBLK1_A_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FARBLK1_A_STMASK;
  m_plist[TIMESTEP_UPDATE_STRAND]    = ACTIVE_STRANDS_STMASK ^ TIME_UPDATE_STMASK;
  if (sim.is_conduction_model) {
    m_plist[FRINGE2_WSURF_HFC_STRAND]       = ACTIVE_STRANDS_STMASK ^ FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE_WSURF_SAMPLING_STRAND]   = ACTIVE_STRANDS_STMASK ^ FRINGE_WSURF_SAMPLING_STMASK;
    m_plist[INTERIOR_WSURF_SAMPLING_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_WSURF_SAMPLING_STMASK;
  }

  if (sim.is_radiation_model) {
    // Cannot run surfel dynamics until old radiation values have been sent
    m_plist[RADIATION_STRAND]  |= ACTIVE_STRANDS_STMASK ^ RADIATION_STMASK;
  }
}

VOID cSTRAND_MGR::init_plists_for_dynamic_scheduling_without_mme_comm(){
  if (my_proc_id == 0){
    msg_print_no_prefix("Using dynamic scheduling of sim strands");
  }

  STRAND_MASK ACTIVE_STRANDS_STMASK  = SLIDING_SURF_A_STMASK | SLIDING_SURF_B_STMASK |
                                       FRINGE2_SURF_STMASK   | FRINGE_SURF_STMASK |
                                       SLIDING_SURF_C_STMASK | FRINGE_NEARBLK_A_STMASK |
                                       FRINGE_FARBLK_A_STMASK | SLIDING_NEARBLK_A_STMASK |
                                       FRINGE2_NEARBLK_A_STMASK | INTERIOR_NS_A_STMASK |
                                       INTERIOR_FARBLK2_A_STMASK |INTERIOR_FARBLK1_A_STMASK |
                                       TIME_UPDATE_STMASK;

  if (sim.is_conduction_model) {
    ACTIVE_STRANDS_STMASK |= (FRINGE2_WSURF_HFC_STMASK | FRINGE_WSURF_SAMPLING_STMASK | INTERIOR_WSURF_SAMPLING_STMASK);
  }

  m_plist[SLIDING_SURF_A_STRAND]     = TIME_UPDATE_STMASK;
  m_plist[SLIDING_SURF_B_STRAND]     = TIME_UPDATE_STMASK | SLIDING_SURF_A_STMASK;
  m_plist[FRINGE2_SURF_STRAND]       = TIME_UPDATE_STMASK;
  m_plist[FRINGE_SURF_STRAND]        = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;
  m_plist[SLIDING_SURF_C_STRAND]     = TIME_UPDATE_STMASK | FRINGE_SURF_STMASK |
                                       SLIDING_SURF_B_STMASK;
  m_plist[FRINGE_NEARBLK_A_STRAND]   = TIME_UPDATE_STMASK | SLIDING_SURF_C_STMASK;
  m_plist[FRINGE_FARBLK_A_STRAND]    = TIME_UPDATE_STMASK;

  m_plist[SLIDING_NEARBLK_A_STRAND]  = TIME_UPDATE_STMASK | SLIDING_SURF_C_STMASK;

  m_plist[FRINGE2_NEARBLK_A_STRAND]  = TIME_UPDATE_STMASK | SLIDING_SURF_C_STMASK |
                                       FRINGE_SURF_STMASK | FRINGE2_SURF_STMASK;

  m_plist[INTERIOR_NS_A_STRAND]      = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;
  m_plist[INTERIOR_FARBLK2_A_STRAND] = TIME_UPDATE_STMASK;
  m_plist[INTERIOR_FARBLK1_A_STRAND] = TIME_UPDATE_STMASK | INTERIOR_FARBLK2_A_STMASK | INTERIOR_NS_A_STMASK;

  if (sim.is_particle_model) {
    if (sim.is_film_solver) {
      //Particle dynamics, performed from within these seven strands,
      //must never go before the reentrianment model in the film
      //kinematics strand.
      m_plist[FRINGE_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[FRINGE_SURF_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;   // Shear stress is calculated during surfel dynamics and is used during film kinematics.
      m_plist[FRINGE2_SURF_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;  // Surfel dynamics for wall surfels is performed in _SURF strands, Thus kinematics strand should be a predecessor of theses strands. 
      m_plist[FRINGE_FARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[SLIDING_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[FRINGE2_NEARBLK_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_NS_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_FARBLK2_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
      m_plist[INTERIOR_FARBLK1_A_STRAND] |= INTERIOR_FILM_KINEMATICS_STMASK;
    }

    ACTIVE_STRANDS_STMASK |= INTERIOR_FILM_ACCUMULATION_STMASK | INTERIOR_FILM_KINEMATICS_STMASK;
    m_plist[INTERIOR_FILM_ACCUMULATION_STRAND] = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_ACCUMULATION_STMASK;
    m_plist[INTERIOR_FILM_KINEMATICS_STRAND]   = ACTIVE_STRANDS_STMASK ^ INTERIOR_FILM_KINEMATICS_STMASK;
  }

  if (sim.is_radiation_model) {

    // Techically, the radiation strands don't really depend on the surfel
    // strands. However, the strand_mgr currently doesn't have a good notion of
    // when the radiation recvs are done, so we don't want it to start the
    // radiation recv strand too early.

    m_plist[RADIATION_STRAND] = TIME_UPDATE_STMASK | RADIATION_STMASK | INTERIOR_NS_A_STMASK | FRINGE_SURF_STMASK | FRINGE2_SURF_STMASK;

    // Cannot run surfel dynamics until old radiation values have been sent
    // m_plist[FRINGE2_SURF_STRAND]  |= RADIATION_STMASK;
    // m_plist[FRINGE_SURF_STRAND]   |= RADIATION_STMASK;
    // m_plist[INTERIOR_NS_A_STRAND] |= RADIATION_STMASK;
  }

  if ( sim.is_conduction_model) {
    m_plist[FRINGE_WSURF_SAMPLING_STRAND]     = TIME_UPDATE_STMASK; 
    m_plist[INTERIOR_WSURF_SAMPLING_STRAND]   = TIME_UPDATE_STMASK;
    m_plist[FRINGE2_WSURF_HFC_STRAND]         = TIME_UPDATE_STMASK | FRINGE2_SURF_STMASK;

    m_plist[FRINGE_SURF_STRAND]        |= (FRINGE_WSURF_SAMPLING_STMASK | FRINGE2_WSURF_HFC_STMASK);
    m_plist[INTERIOR_NS_A_STRAND]      |= (INTERIOR_WSURF_SAMPLING_STMASK | FRINGE2_WSURF_HFC_STMASK);

    m_plist[SLIDING_SURF_C_STRAND]     |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE_NEARBLK_A_STRAND]   |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[SLIDING_NEARBLK_A_STRAND]  |= FRINGE2_WSURF_HFC_STMASK;
    m_plist[FRINGE2_NEARBLK_A_STRAND]  |= FRINGE2_WSURF_HFC_STMASK;
  }

  m_plist[TIMESTEP_UPDATE_STRAND]    = ACTIVE_STRANDS_STMASK ^ TIME_UPDATE_STMASK;
}


/*=================================================================================================
 * INIT DEPENDENCY COUNTERS
 * Dependency counters determine when a strand is ready to run. The strand is ready when all its
 * static dependencies on predecessor strands, its receive dependencies and MLRF dependencies are
 * satisfied
 *================================================================================================*/
VOID cSTRAND_MGR::initialize_dependency_counters() {

  
  // For the first timestep, the initial value of the dependency counter for each strand 
  // is the total number of strand dependencies excluding dependencies from earlier 
  // timesteps, which are treated as already satisfied. It is assumed that these are 
  // equivalent to higher-numbered strands.

  m_ready_mask.reset_all();

  ccDOTIMES(successor_strand, N_STRANDS) {
    // Initialize total static dependencies
    asINT32  first_ts_predecessors = 0;    
    ccDOTIMES(predecessor_strand, successor_strand) {
      if ( m_slist[predecessor_strand].test(successor_strand) )  {
        first_ts_predecessors++;
      }
    }
    m_dstatic[successor_strand] = first_ts_predecessors;

    // When a strand is enabled to begin it first dependent timestep, it is placed on the ready list,
    // its strand_timestep_cntr is incremented, and then count_receive_dependencies is called to
    // compute the dependencies for first_dependent_ts

    if (first_ts_predecessors == 0) {
      m_ready_mask.set(successor_strand);
      m_rcntr[successor_strand] = 0;
      m_dcntr[successor_strand] = m_dcntr_invalid;
    }
    else {
      //wsurfel comm active at first time-step of the realm with the larger time step
      //when restoring from a ckpt file, the wsurfel comm is active at the first timestep
      //assuming that the simulation restart from a synchronized flow-conduction timestep
      bool is_cond_and_flow_sim = sim.is_conduction_model && sim.is_flow;
      BOOLEAN is_wsurfel_comm_active = (!g_timescale.is_this_sp_realm_subcycled(sim.is_conduction_sp, is_cond_and_flow_sim) || sim.is_full_checkpoint_restore || sim.is_mme_checkpoint_restore);
      asINT32 first_ts_rdeps = count_receive_dependencies(successor_strand, is_wsurfel_comm_active);
      m_rcntr[successor_strand] = first_ts_rdeps ;
      m_dcntr[successor_strand] = first_ts_predecessors +  first_ts_rdeps + m_n_mlrf_deps[successor_strand];
    }
  }
#if DEBUG_STRAND_SWITCHING
  {
    cLOG_MSG msg = main_log().msg("STRAND_SWITCHING",LOG_FUNC);
    msg.printf( "\nIDC: d_cntr %d",g_timescale.m_timestep);
    ccDOTIMES(strand, N_STRANDS) {
      msg.printf("%d ", m_dcntr[strand]);
    }
    msg.printf("\nm_ready %x", m_ready_mask.load().get());
  }
#endif
  
}
/*=================================================================================================
 * WSURFEL COMM TIMESTEP
 * wsurfel comm only happens at the timesteps when both conduction and flow solver are active
 *================================================================================================*/
bool cSTRAND_MGR::are_wsurfels_commed(ACTIVE_SOLVER_MASK active_solver_mask) {

  return (((active_solver_mask & LB_ACTIVE) != 0) && ((active_solver_mask & CONDUCTION_PDE_ACTIVE) != 0));
}

/*=================================================================================================
 * UPDATE CONSUMER COUNTS
 * Called by various strands to let the strand manager know that they have consumed the appropirate
 * data. When the consumer count for a certain receive type and scale drops to zero, new data is
 * unpacked into the receive buffer by the comm thread.
 *================================================================================================*/
VOID cSTRAND_MGR::update_consumer_counts(STP_SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask) {

  // If this is called for all strands, the mutex lock/unlock is wasteful, since most strands are not consumers.
  // However if it is called only for consumers, locking once for all receive types is preferable to locking for each on
  // which the strand has a dependency.
  pthread_mutex_lock(&m_active_consumers_mutex);
  ccDOTIMES(rtype,  N_RECV_TYPES) {
    if (m_clist[rtype].test(g_running_strand)) {
      cRECV_CHANNEL *rchannel =  &m_recv_channel[rtype];
      if (rchannel->m_n_expected_scale[scale] > 0) {

        bool decrement_wsurfel_cntr = false;
        if(rtype == WSURFEL_RECV_TYPE) {
          if (are_wsurfels_commed(active_solver_mask)) {
            decrement_wsurfel_cntr = true;
            m_active_consumers_count[rtype][scale]--;
          }
        } else {
          if (m_active_consumers_count[rtype][scale] == 0) {
            msg_internal_error("UpdateConsumerCounts attempted to decrement a 0-count for rtype %d scale %d at TS %d by strand %d", rtype, scale, g_timescale.m_timestep, g_running_strand);
          }
          m_active_consumers_count[rtype][scale]--;

        }
        LOG_MSG("RECV_DEPEND",LOG_FUNC).format("decrement cntr {} consumer count {} rtype {}",decrement_wsurfel_cntr, m_active_consumers_count[rtype][scale], rtype);
        LOG_MSG("RECV_DEPEND").format("Decrementing consumer count: rtype {} scale {} consumers {} current time {}",
                    rtype, scale,
                    m_active_consumers_count[rtype][scale],
                    g_timescale.m_time);


        LOG_MSG_IF(rtype == BSURFEL_RECV_TYPE, "RECV_DEPEND",LOG_FUNC,LOG_ATTR(g_running_strand),LOG_ATTR(rtype),LOG_ATTR(scale)).format( "m_active_consumers_count {}", m_active_consumers_count[rtype][scale]);
      }
    }
  }
  pthread_mutex_unlock(&m_active_consumers_mutex);          
}
/*=================================================================================================
 * UPDATE WSURFEL CONSUMER COUNTS
 * Called by time update strand to decrement the counter so that the unpacking can happen at the
 * correct timesteps.
 *================================================================================================*/
VOID cSTRAND_MGR::update_wsurfel_consumer_counts(STP_SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask) {
  pthread_mutex_lock(&m_active_consumers_mutex);
  asINT32 rtype = WSURFEL_RECV_TYPE;
  cRECV_CHANNEL *rchannel =  &m_recv_channel[rtype];
  if (rchannel->m_n_expected_scale[scale] > 0 && m_active_consumers_count[rtype][scale] > 0) {
    if (are_wsurfels_commed(active_solver_mask)) {
      m_active_consumers_count[rtype][scale]--;
      LOG_MSG("RECV_DEPEND",LOG_FUNC).format("decrement wsurfel cntr {} consumer count {}", true, m_active_consumers_count[rtype][scale]);
      LOG_MSG("RECV_DEPEND").format("Decrementing consumer count: rtype {} scale {} consumers {} current time {}",
                  rtype, scale,
                  m_active_consumers_count[rtype][scale],
                  g_timescale.m_time);
    }
  }
  pthread_mutex_unlock(&m_active_consumers_mutex);
}
/*=================================================================================================
 * UPDATE CONSUMER COUNTS
 * Called by various strands to let the strand manager know that they have consumed the appropirate
 * data. When the consumer count for a certain receive type and scale drops to zero, new data is
 * unpacked into the receive buffer by the comm thread.
 *================================================================================================*/
VOID cSTRAND_MGR::update_collective_consumer_counts() {

  // If this is called for all strands, the mutex lock/unlock is wasteful, since most strands are not consumers.
  // However if it is called only for consumers, locking once for all receive types is preferable to locking for each on
  // which the strand has a dependency.
  pthread_mutex_lock(&m_active_consumers_mutex);
  ccDOTIMES(ctype,  N_COLL_TYPES) {
    if (m_clist[ctype + N_RECV_TYPES].test(g_running_strand)) {
        if (m_collective_consumers_count[ctype] == 0) {
          msg_internal_error("UpdateConsumerCounts attempted to decrement a 0-count for ctype %d", ctype + N_RECV_TYPES);
        }
        m_collective_consumers_count[ctype]--;
    }
  }
  pthread_mutex_unlock(&m_active_consumers_mutex);          
}


/*=================================================================================================
 * ADD TO UNPACKING LIST
 *================================================================================================*/
VOID cSTRAND_MGR::add_to_unpacking_list(SP_RECV_GROUP_BASE rgroup) {
  RECV_TYPE rtype = rgroup->get_recv_type();
  //rgroup->set_next_group_in_unpacking_list(m_unpacking_list_head[rtype][rgroup->m_scale]);
  rgroup->m_next_in_unpacking_list = m_unpacking_list_head[rtype][rgroup->m_scale];
  //m_unpacking_list[rgroup->get_recv_type()][rgroup->m_scale].push_back(rgroup);
  m_unpacking_list_head[rtype][rgroup->m_scale] = rgroup;
  LOG_MSG("RECV_DEPEND",LOG_FUNC).format("Adding recv type {} scale {} to unpacking list", rtype, rgroup->m_scale);
}

/*=================================================================================================
 * ADD TO collective LIST
 *================================================================================================*/
VOID cSTRAND_MGR::add_to_collective_list(COLLECTIVE_GROUP cgroup) {
  COLLECTIVE_TYPE ctype = cgroup->m_collective_type;
  cgroup->m_next_in_collective_list = m_collective_list_head[ctype - N_RECV_TYPES];
  m_collective_list_head[ctype - N_RECV_TYPES] = cgroup;
}

/*=================================================================================================
 * COUNT RECEIVE DEPENDENCIES
 * For a given receive type and timestep, the dependency for that type is satisfied when two sets of receives
 * of messages of that type have been completed; those expected for the finest scale, and those expected
 * for coarsen(coarsest_active_scale); if that scale does not exist (i.e. coarsest_active == coarsest), only
 * the finest scale is required. The number of receives expected for each scale is a static function of the
 * decomposition, and is computed during initialization and recorded in the m_total_expected array.
*================================================================================================*/
asINT32 cSTRAND_MGR::count_receive_dependencies(asINT32 strand, BOOLEAN is_wsurfel_comm_active) {
  asINT32 n_dependencies = 0;
  ccDOTIMES(rtype, N_RECV_TYPES) {
    if (m_dlist[rtype].test(strand)) {
      
      cRECV_CHANNEL *rchannel =  &m_recv_channel[rtype];
      asINT32 n_expected = 0;
      SCALE coarsest_active_scale;

      if (does_recv_satisfy_same_timestep_dep((RECV_TYPE) rtype)) {
        // These are same-timestep dependencies; none are implicitly satisfied.
        // We need to include all the required scales that haven't already been
        // received. Since surfels do not participate in explode,
        // coarsen(coarsest_active_scale) is not required.
        TIMESTEP timestep_ctr;
        if(rchannel->m_type == WSURFEL_RECV_TYPE) {
          if (!is_wsurfel_comm_active) {
            continue; //recv not active, so no dependencies associated with it and no need for further check
          }
          coarsest_active_scale = g_timescale.compute_wsurfel_comm_coarsest_active_scale(FINEST_SCALE);
          timestep_ctr = g_timescale.m_wsurfel_comm_cntr;
        } else {
          coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
          timestep_ctr = g_timescale.m_timestep;
        }
        DO_SCALE_FROM_FINE_TO_COARSE(surfel_receive_scale, FINEST_SCALE, coarsest_active_scale) {
          // Do not include dependencies that have already been satisfied
          if (rchannel->m_scale_timestep[surfel_receive_scale] < timestep_ctr) {
            n_expected += (rchannel->m_n_expected_scale[surfel_receive_scale] > 0) ? 1 : 0;
          }
        }
      } else {

        // These are prior-timestep dependencies.
        coarsest_active_scale = compute_coarsest_active_scale(g_timescale.m_timestep, FINEST_SCALE);
 
        // Do not include dependencies that have already been satisfied
        if (rchannel->m_scale_timestep[FINEST_SCALE] < g_timescale.m_timestep) {
          n_expected += (rchannel->m_n_expected_scale[FINEST_SCALE] > 0) ? 1 : 0;
        }
        if (coarsest_active_scale != COARSEST_SCALE) {
          STP_SCALE coarse_scale = coarsen_scale(coarsest_active_scale);

          LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("CRD: strand %d: timestep_cntr[%d] coarse_scale %d scale_timestep F %d C %d", 
                strand, g_timescale.m_timestep,
                coarse_scale, rchannel->m_scale_timestep[FINEST_SCALE],
                rchannel->m_scale_timestep[coarse_scale]);
          // Do not include dependencies that have already been satisfied       
          if (rchannel->m_scale_timestep[coarse_scale] <  g_timescale.m_timestep) {
            n_expected += (rchannel->m_n_expected_scale[coarse_scale] > 0) ? 1 : 0;
          }
        }
      }
      n_dependencies += n_expected;
    }
  }
  LOG_MSG("STRAND_SWITCHING", LOG_FUNC).format("strand = {} n_dependencies= {}", strand, n_dependencies);	   
  return(n_dependencies + count_collective_dependencies(strand));
}

asINT32 cSTRAND_MGR::count_collective_dependencies(asINT32 strand)
{
#if BUILD_D19_LATTICE
  if (sim.is_pf_model) {
    asINT32 n_deps = 0;
    ccDOTIMES(ctype, N_COLL_TYPES) {
      if (m_coll_list[ctype].test(strand)) {
	n_deps++;
      }
    }
    return n_deps;
  } else
#endif
  {
    return 0;
  }

}


/*=================================================================================================
 * Called by process_unpacking list 
*================================================================================================*/
VOID cSTRAND_MGR::reset_active_consumers_count(RECV_TYPE rtype,SCALE scale){
  m_active_consumers_count[rtype][scale] = m_total_consumer_count[rtype];
  if(rtype == WSURFEL_RECV_TYPE) {
    m_active_consumers_count[rtype][scale]++;
  }
  LOG_MSG_IF(rtype == WSURFEL_RECV_TYPE, "RECV_DEPEND",LOG_TS,LOG_FUNC,LOG_ATTR(rtype),LOG_ATTR(scale)).format( "m_active_consumers_count {}", m_active_consumers_count[rtype][scale]);
}

VOID cSTRAND_MGR::process_unpacking_list(RECV_TYPE recv_type, cRECV_CHANNEL *rchannel) {
  ccDOTIMES(scale, sim.num_scales) {
#if 0
    if(recv_type == 0) {
      msg_print("TS %ld RS %d ccount = %d",g_timescale.m_time, g_running_strand,m_active_consumers_count[recv_type][scale]);
    } 
#endif
    if (m_active_consumers_count[recv_type][scale] == 0) {
      SP_RECV_GROUP_BASE recv_group = m_unpacking_list_head[recv_type][scale];
      SP_RECV_GROUP_BASE previous_group = NULL; // Non-null only if some group has been skipped
      while(recv_group != NULL) {
        SP_RECV_GROUP_BASE next_group = recv_group->m_next_in_unpacking_list;
        if (recv_group->is_ready_to_be_unpacked()) {
          asINT32 solver_switch_ts = rchannel->m_ssp_timestep[recv_group->source_sp()][scale];
          BOOLEAN switch_solver_p = g_timescale.switch_recv_group_solver_type(recv_group->m_recv_type == SURFEL_RECV_TYPE, solver_switch_ts, scale);

          // For wsurfel recv groups, no solver switch considerations are needed
          // For surfel recv groups, immediately before the solver switch, we should unpack since 
          // surfel send is before the solver switch and received within the same timestep
          // For ublk recv groups, switch the solver first, and then unpack since the ublk send
          // is after the solver switch and received across timesteps
          RECV_TYPE group_recv_type = recv_group->get_recv_type();
          if (group_recv_type == WSURFEL_RECV_TYPE || group_recv_type == CONTACT_RECV_TYPE) {
            recv_group->unpack(sim.init_solver_mask);
          } else if (group_recv_type == SURFEL_RECV_TYPE) {
            recv_group->unpack(sim.init_solver_mask);
            if(switch_solver_p) {
              g_timescale.switch_T_solver(recv_group->t_solver_type);
            }
          } else {
            if(switch_solver_p) {
              g_timescale.switch_T_solver(recv_group->t_solver_type);
            }
            recv_group->unpack(sim.init_solver_mask);
          }
          recv_group->post_recv();

          LOG_MSG("RECV_DEPEND",LOG_FUNC).format("Unpacking recv type {} scale {} source {}", recv_group->m_recv_type,recv_group->m_scale, recv_group->m_source_sp);

          // Update the Receive Channel

          BOOLEAN scale_receives_complete = rchannel->update(recv_group->source_sp(), scale);
          if (scale_receives_complete) {
            reset_active_consumers_count(recv_type,scale);
          }
          recv_group->m_next_in_unpacking_list = NULL;
          if (previous_group != NULL) {
            previous_group->m_next_in_unpacking_list = next_group;
          } else {
            m_unpacking_list_head[recv_type][scale] = next_group;
          }
        } else { // Leave group on list
          LOG_MSG("RECV_DEPEND",LOG_FUNC).format("Recv type {} not raedy for unpacking",recv_group->m_recv_type);
          previous_group = recv_group;
        }
        recv_group = next_group;
      }
    }//scales
  } //N_RECV_TYPES
}

VOID cSTRAND_MGR::reset_collective_consumers_count(COLLECTIVE_TYPE ctype){
  g_strand_mgr.m_collective_consumers_count[ctype - N_RECV_TYPES] = g_strand_mgr.m_total_consumer_count[ctype];
}
/*=================================================================================================
 * PROCESS collective LISTS
 * Called by the comm thread to see if there's new data to collective call
*================================================================================================*/

VOID cSTRAND_MGR::process_collective_list(COLLECTIVE_TYPE ctype) {

  if (m_collective_consumers_count[ctype - N_RECV_TYPES] == 0) {
    COLLECTIVE_GROUP group = m_collective_list_head[ctype - N_RECV_TYPES];
    COLLECTIVE_GROUP previous_group = NULL; // Non-null only if some group has been skipped
    while(group != NULL) {
      COLLECTIVE_GROUP next_group = group->m_next_in_collective_list;
      if (group->is_ready_to_be_called()) {
	
	group->collective_call();
	
	reset_collective_consumers_count(ctype);
	
	group->m_next_in_collective_list = NULL;
	if (previous_group != NULL) {
	  previous_group->m_next_in_collective_list = next_group;
	} else {
	  m_collective_list_head[ctype - N_RECV_TYPES] = next_group;
	}
      } else { // Leave group on list
	previous_group = group;
      }
      group = next_group;
    }
  }
}


/*=================================================================================================
 * PROCESS UNPACKING LISTS
 * Called by the comm thread to see if there's new data to unpack
*================================================================================================*/
VOID cSTRAND_MGR::process_unpacking_list() {

  // We need to protect the m_active_consumers_count with a mutex, because otherwise a
  // consumer could decrement it between the time we test it for 0 below, and the time we
  // reset it to m_total_consumer_count. This update would be lost, which would lead to
  // a hang.
  pthread_mutex_lock(&m_active_consumers_mutex);

  ccDOTIMES(recv_type, N_RECV_TYPES) {
    cRECV_CHANNEL *rchannel =  &m_recv_channel[recv_type];
    process_unpacking_list((RECV_TYPE) recv_type, rchannel);
  }
  pthread_mutex_unlock(&m_active_consumers_mutex);
}

VOID cSTRAND_MGR::process_farblk_unpacking_list() {

  pthread_mutex_lock(&m_active_consumers_mutex);

  cRECV_CHANNEL *rchannel =  &m_recv_channel[FARBLK_RECV_TYPE];
  process_unpacking_list((RECV_TYPE) FARBLK_RECV_TYPE, rchannel);
  pthread_mutex_unlock(&m_active_consumers_mutex);
}
VOID cSTRAND_MGR::process_nearblk_unpacking_list() {

  pthread_mutex_lock(&m_active_consumers_mutex);

  cRECV_CHANNEL *rchannel =  &m_recv_channel[NEARBLK_RECV_TYPE];
  process_unpacking_list((RECV_TYPE) NEARBLK_RECV_TYPE, rchannel);
  pthread_mutex_unlock(&m_active_consumers_mutex);
}
VOID cSTRAND_MGR::process_surfel_unpacking_list() {

  pthread_mutex_lock(&m_active_consumers_mutex);

  cRECV_CHANNEL *rchannel =  &m_recv_channel[SURFEL_RECV_TYPE];
  process_unpacking_list((RECV_TYPE) SURFEL_RECV_TYPE, rchannel);
  pthread_mutex_unlock(&m_active_consumers_mutex);
  }
VOID cSTRAND_MGR::process_collective_list() {
  
  pthread_mutex_lock(&m_active_consumers_mutex);

  ccDOTIMES(ctype, N_COLL_TYPES) {
    process_collective_list((COLLECTIVE_TYPE) (ctype + N_RECV_TYPES));
  }

  pthread_mutex_unlock(&m_active_consumers_mutex);
} 
/*=================================================================================================
 * COMPLETE TIMESTEP
 * Called by the compute thread at the completion of a strand run.
 * For the case that this function is called by the TIME_UPDATE_STRAND, we reset the dcntrs for
 * the next timestep
*================================================================================================*/
VOID cSTRAND_MGR::complete_timestep(ACTIVE_SOLVER_MASK finest_active_solver_mask )
{
  STRAND_MASK slist = m_slist[g_running_strand];

  LOG_MSG("STRAND_SWITCHING").printf("CT before updating state: Calling strand %d TS %d P %d", g_running_strand, g_timescale.m_timestep, g_timescale.time_parity());
  PRINT_STRANDS(m_dcntr_mutex);

  if ( g_running_strand == TIMESTEP_UPDATE_STRAND ){
    reset_dcntrs_for_next_timestep(finest_active_solver_mask);
    // Only after the dcntrs are updated do we request the mlrf receives. This is to avoid running
    // into the scenario where a receive completes before the dcounters are reset, in which case
    // the number of mlrf depencies counted will be incorrect. Furthermore, these updates would
    // attempt to reduce the dcntrs when they are in an invalid state
    if (is_sliding_mesh_present()) {
      // Post receives for the next timestep
      m_mlrf_comm.request_pre_dyn_recvs();
      m_mlrf_comm.request_post_dyn_recvs();
    }
  }

  // Decrement dcntrs of strands whose static deps are satisfied by the current running strand
  // All strands that are processed before the current strand cannot be its successor
  slist  >>= g_running_strand;
  ccDO_BITSET(successor_strand, slist) {
    decr_and_maybe_invalidate_dcntr((STRAND) (successor_strand + g_running_strand));
  }

  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf("CT after updating state: Calling strand %d TS %ld P %d ", g_running_strand, g_timescale.m_time, g_timescale.time_parity());
  PRINT_STRANDS(m_dcntr_mutex);
 
  if (m_exit) {
    stop_runstate((STRAND)g_running_strand);
  }
}

/*=================================================================================================
 * RESET DCOUNTERS FOR NEXT TIMESTEP
 * Strand dcounters are in an invalid state before they are reset at TU completion
*================================================================================================*/
VOID cSTRAND_MGR::reset_dcntrs_for_next_timestep(ACTIVE_SOLVER_MASK finest_active_solver_mask){
  // Only at the end of the TIME_UPDATE_STRANDs timestep can we update the m_dcntr with receive dependencies
  // required for for the next timestep. This is because m_dcntrs cannot account for receives for future
  // timesteps when the current timestep is being executed. This is a consequence of removing parity
  cassert( g_running_strand == TIMESTEP_UPDATE_STRAND );
  STRAND_MASK new_ready_strands;
  pthread_mutex_lock(&m_time_update_mutex);

  //All strands move to the next dependent timestep
  g_timescale.m_timestep++ ;

  //Cross-realm surfel comm counter only updated if comm is active
  BOOLEAN is_wsurfel_comm_active = are_wsurfels_commed(finest_active_solver_mask);
  if(is_wsurfel_comm_active) g_timescale.m_wsurfel_comm_cntr++;

  ccDOTIMES(strand,N_STRANDS) {
    asINT32 rdeps = m_rcntr[strand] = count_receive_dependencies(strand, is_wsurfel_comm_active);

    m_dcntr[strand] = m_dstatic[strand] + rdeps + m_n_mlrf_deps[strand];

    // If strands are ready to go, mark them as ready and invalidate the dcntrs
    if (m_dcntr[strand] == 0){
      new_ready_strands.set(strand);
      m_dcntr[strand] = m_dcntr_invalid;
    }
  } //cDOTTIMES

  reset_m_n_mlrf_deps(FALSE);
#if BUILD_D19_LATTICE
  if (sim.is_pf_model)
    clear_collective_counters();
#endif
  pthread_mutex_unlock(&m_time_update_mutex);
  // This function is only called by the compute thread, and the comm thread
  // really doesn't need to synchronize  on this variable, so we can
  // make this update be relaxed
  m_ready_mask.fetch_or(new_ready_strands, std::memory_order_relaxed);
}

/*=================================================================================================
 * DECREMENT AND MAYBE INVALIDATE DCOUNTERS
 * Called when various strand dependencies are satisfied. A strand become ready when its dcntr drops to 0
*================================================================================================*/
VOID cSTRAND_MGR::decr_and_maybe_invalidate_dcntr(STRAND strand) {

  STRAND_MASK new_ready_strands;
  pthread_mutex_lock(&m_dcntr_mutex);

  if (m_dcntr[strand] != m_dcntr_invalid){
    if (m_dcntr[strand] <= 0) {
      msg_internal_error("Attempted to decrement dependency counter for strand [%d], but it was already %d",
                         strand,m_dcntr[strand]);
    } else {
      m_dcntr[strand]--;
      if (m_dcntr[strand] == 0) {
        new_ready_strands.set(strand);
        m_dcntr[strand] = m_dcntr_invalid; //Only at TU completion can we make the dependencies active again
      }
    }
  }

  pthread_mutex_unlock(&m_dcntr_mutex);

  // We want the compute thread to see that the dcntrs are have changed, so
  // we release these changes. Because this is a RMW operation, this is naturally
  // sequenced after the mutex is unlocked. 
  m_ready_mask.fetch_or(new_ready_strands, std::memory_order_release);
}

/*=================================================================================================
 * MLRF receive dependencies are simple, because there are only two strands with MLRF
 * receive dependencies, and each has exactly one dependency on a unique type.
*================================================================================================*/
VOID cSTRAND_MGR::mlrf_recv_update_dcntr(MLRF_COMM_TYPE type, TIMESTEP t_step)
{
  //It doesn't make sense to get a comm for an older timestep
  cassert(t_step >= g_timescale.m_timestep);

  STRAND strand = m_mlrf_comm.recv_comm_type_to_dependent_strand(type);
  if (t_step > g_timescale.m_timestep){
    if (m_n_mlrf_deps[strand] == 0){
      msg_error("Attempt to decrement an mlrf_dep for strand %d, that is already zero\n",strand);
    }
    m_n_mlrf_deps[strand]--;
  }
  else {
    decr_and_maybe_invalidate_dcntr(strand);
  }
}

/*=================================================================================================
 * For sliding mesh, there is a send dependency as well as a receive dependency. This is because we
 * copy from MLRF surfels into the pre-dyn send buffers in the comm thread, and must have some way to indicate
 * that it is OK to update the surfels. The cleanest way to do this is to add a dependency to the dcntr.
 * There is no equivalent for the post-dyn send buffers, because that copying is done in the sim thread.
*================================================================================================*/
VOID cSTRAND_MGR::mlrf_send_update_dcntr(MLRF_COMM_TYPE type, TIMESTEP t_step)
{

   //It doesn't make sense to get a comm for an older timestep
   cassert(t_step >= g_timescale.m_timestep);

   STRAND strand = m_mlrf_comm.send_comm_type_to_dependent_strand(type);

   if (t_step > g_timescale.m_timestep){
     if (m_n_mlrf_deps[strand] == 0){
       msg_error("Attempt to decrement an mlrf_dep for strand %d, that is already zero\n",strand);
     }
     m_n_mlrf_deps[strand]--;
   }
   else {
     decr_and_maybe_invalidate_dcntr(strand);
   }
}

/*=================================================================================================
 * Called by a receive channel when it records a receive completion
 * See : cRECV_CHANNEL::update
*================================================================================================*/
VOID cSTRAND_MGR::recv_channel_record_completion(RECV_TYPE rtype, SCALE scale){
  cRECV_CHANNEL *recv_channel = &m_recv_channel[rtype];
  TIMESTEP strand_timestep = (rtype == WSURFEL_RECV_TYPE) ? g_timescale.m_wsurfel_comm_cntr : g_timescale.m_timestep;
  if (recv_channel->m_scale_timestep[scale] == strand_timestep) {
    recv_update_dcntr(rtype);
    if (m_ready_mask.load(std::memory_order_relaxed).any()) {
      g_sync_threads.wake_up_compute_thread();
    }
  }
}

/*=================================================================================================
 * RECV UPDATE DCOUNTERS
 * Drop dcounters if a strand is dependent on a certain receive type
*================================================================================================*/
VOID cSTRAND_MGR::recv_update_dcntr(RECV_TYPE rtype)
{
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC,LOG_ATTR(rtype));
  PRINT_STRANDS(m_dcntr_mutex);
  STRAND_MASK dlist = m_dlist[rtype];
  asINT32 strand = 0;
  ccDO_BITSET(strand, dlist) {
    decr_and_maybe_invalidate_dcntr((STRAND)strand);
  }
  PRINT_STRANDS(m_dcntr_mutex);

}

VOID cSTRAND_MGR::collective_update_dcntr(COLLECTIVE_TYPE ctype)
{
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC,LOG_ATTR(ctype));
  PRINT_STRANDS(m_dcntr_mutex);
  STRAND_MASK dlist = m_dlist[ctype];
  asINT32 strand = 0;
  ccDO_BITSET(strand, dlist) {
    decr_and_maybe_invalidate_dcntr((STRAND)strand);
  }  
  PRINT_STRANDS(m_dcntr_mutex);

}

/*=================================================================================================
 * INIT SEND QUEUES
 * Do we really need this?
*================================================================================================*/
VOID cSTRAND_MGR::init_send_queues(asINT32 n_meas_windows, asINT32 n_send_groups)
{
  m_send_queue = xnew cSEND_QUEUE(n_send_groups);
  m_meas_send_queue = xnew cMEAS_SEND_QUEUE(n_meas_windows);
}

/*=================================================================================================
 * CANCEL PENDING RECEIVES
 * Called in exa_sim_sp.cc
*================================================================================================*/ VOID cSTRAND_MGR::cancel_pending_recvs() {

  RECV_TYPE rtype = FARBLK_MME_RECV_TYPE;
  cRECV_CHANNEL *recv_channel = &m_recv_channel[rtype];
  recv_channel->cancel_pending_recvs();

  rtype = NEARBLK_MME_RECV_TYPE;
  recv_channel = &m_recv_channel[rtype];
  recv_channel->cancel_pending_recvs();

#if BUILD_5G_LATTICE
  rtype = FARBLK_PORE_RECV_TYPE;
  recv_channel = &m_recv_channel[rtype];
  recv_channel->cancel_pending_recvs();

  rtype = NEARBLK_PORE_RECV_TYPE;
  recv_channel = &m_recv_channel[rtype];
  recv_channel->cancel_pending_recvs();
#endif

  m_rp_recv_channel.cancel_pending_recvs();

  // Usually, we don't need to cancel sends, but for some reason we have to cancel
  // these sends or else we get notifications about unused buffered messages.
  // I'm not sure why. Perhaps it's because these sends span multiple timesteps,
  // and the simulation can end before the send is complete?
  radiation_cancel_pending_sends();

}

/*=================================================================================================
 * POST INITIAL RECEIVES
 * Called by the comm thread, see comm_thread.cc
*================================================================================================*/
VOID cSTRAND_MGR::post_initial_recvs() {

  RECV_TYPE rtype = FARBLK_MME_RECV_TYPE;
  cRECV_CHANNEL *recv_channel = &m_recv_channel[rtype];
  recv_channel->post_initial_recvs();

  rtype = NEARBLK_MME_RECV_TYPE;
  recv_channel = &m_recv_channel[rtype];
  recv_channel->post_initial_recvs();

#if BUILD_5G_LATTICE
  if (sim.is_large_pore) {
    rtype = FARBLK_PORE_RECV_TYPE;
    recv_channel = &m_recv_channel[rtype];
    recv_channel->post_initial_recvs();
  
    rtype = NEARBLK_PORE_RECV_TYPE;
    recv_channel = &m_recv_channel[rtype];
    recv_channel->post_initial_recvs();
  }
#endif

}


template<typename T>
inline T* smart_ptr_to_ptr(T* ptr) { return ptr; };

template<typename T, typename Deleter = std::default_delete<T>>
inline T* smart_ptr_to_ptr(const std::unique_ptr<T>& ptr) { return ptr.get(); };

/*=================================================================================================
 * ADD SURFEL GROUPS TO SEND QUEUE
 *
 * If sliding mesh is present in a case, the initiation of surfel sends is moved from the surfel groups
 * to the do_mlrf_delta_mass_correction_and_s2v in the SLIDING_SURF_C_STRAND. Why this is necessary
 * is not documented; it was added by Vinit as part of the nextgen sliding mesh implementation. 
 * A reconstruction (six years later) of what appears to have been the reasoning follows.
 *
 * MLRF surfels interact with dynamics surfels and ublks in their own reference frame, and may be ghosted, 
 * but they are not added to dynamics surfel groups, only to MLRF surfel groups. They are processed in the 
 * SLIDING_SURFELS_A, B, and C strands (A: reset, s2s, v2s; B: dynamics; C: delta_mass_correction, s2v), 
 * which are not divided into FRINGE, FRINGE2, and INTERIOR. MLRF surfels that are ghosted need to
 * be sent, but there are no send groups associated with MLRF_SURFEL_GROUPs; instead they are added
 * to the surfel send groups that are associated with FRINGE_SURFEL_GROUPS. If they had their own send groups,
 * a new comm type/recv channel would have been required. Since both MLRF surfels and dynamics surfels were
 * in the same send groups, the actual send had to be deferred until both were complete.
 *
 * Note that this creates a dependency of SLIDING_SURF_C_STRAND on FRINGE_SURF_STRAND, and delays the
 * initiation of regular surfel comm until the completion of all sliding-mesh surfel procesing.
 * This should be eliminated if at all possible.
 *
 * The procedure is this: during LGI parsing, if there are MLRF ring set records, the method 
 * disable_surfel_send_requests_by_surfel_groups is called. This sets all the send group pointers in 
 * surfel groups (i.e. fringe surfel groups) to NULL. The method below, add_surfel_groups_of_scale_to_send_queue, 
 * is called at the end of do_mlrf_delta_mass_correction_and_s2v.
*================================================================================================*/
VOID cSTRAND_MGR::add_surfel_groups_of_scale_to_send_queue(SCALE scale,
                                                            ACTIVE_SOLVER_MASK active_solver_mask) {

  for (auto& send_group : get_send_group_fset_by_build_type().get_groups_of_scale(scale)) {
    send_group->wait_for_copying_of_send_data();
    m_send_queue->add_sp_entry(smart_ptr_to_ptr(send_group), active_solver_mask, TRUE);
  }
}

/*=================================================================================================
 * ADD COLLECTIVE TO QUEUE
*================================================================================================*/
VOID cSTRAND_MGR::add_collective_to_queue(COLLECTIVE_TYPE ctype) {
  m_coll_list[ctype - N_RECV_TYPES] = m_dlist[ctype];
  m_collective_queue->add_entry(m_collective_group[ctype - N_RECV_TYPES]);
}

VOID cSTRAND_MGR::clear_collective_counters() {
  ccDOTIMES(ctype, N_COLL_TYPES) {
    m_coll_list[ctype].reset_all();
  }
}

/*=================================================================================================
 * CONSIDER SURRENDER
*================================================================================================*/

#if !BUILD_GPU
#define ENABLE_STRAND_SURRENDER
#endif

#ifdef ENABLE_STRAND_SURRENDER
BOOLEAN cSTRAND_MGR::consider_surrender(asINT32 strand_index) {
  // We want the compute thread to pick up new changes to m_ready_mask from the comm thread,
  // So we use acquire to sync with the comm thread
  STRAND_MASK surrender_mask = m_ready_mask.load(std::memory_order_acquire) & m_runstate_mask.load(std::memory_order_relaxed) & m_pmask[strand_index];
  if(surrender_mask.any()) {
    unsigned pos = surrender_mask.find_first_set();
    // pthread_mutex_lock(&m_dcntr_mutex);
    // Reset the ready flag so that this strand is still ready and will come back
    m_ready_mask.set(strand_index, std::memory_order_relaxed);
    LOG_MSG("STRAND_SWITCHING").printf("Surrendering strand %d (%s) to %d (%s)",strand_index, print_strand_name(strand_index), pos, print_strand_name(pos));
    set_running_strand((STRAND)pos, TRUE);  // Second argument should be TRUE since this is the first time to launch this strand within this timestep
    m_ready_mask.reset(pos, std::memory_order_relaxed);
    // pthread_mutex_unlock(&m_dcntr_mutex);

    m_strands[pos]->run();

    // go back to the parent strand
    // pthread_mutex_lock(&m_dcntr_mutex);
    m_ready_mask.reset(strand_index, std::memory_order_relaxed);
    set_running_strand((STRAND)strand_index, FALSE);  // Second argument is FALSE since this is not the first time to launch this strand within this timestep
    // pthread_mutex_unlock(&m_dcntr_mutex);
    return TRUE;
  }
  return FALSE;
}

BOOLEAN consider_surrender(asINT32 calling_strand) {
  return g_strand_mgr.consider_surrender(calling_strand);
}
#else
BOOLEAN cSTRAND_MGR::consider_surrender(asINT32 strand_index) {
  return FALSE;
}
BOOLEAN consider_surrender(asINT32 calling_strand) {
  return FALSE;
}
#endif

/*=================================================================================================
 * ALLOCATE MME RECV GROUP
 * Called below in initialize_recv_group_arrays
*================================================================================================*/
static sUBLK_MME_RECV_GROUP* allocate_mme_recv_group(sUBLK_RECV_GROUP* template_group)
{
  if ( sim.is_movb_sim() ) {
    return xnew sUBLK_DYNAMIC_MME_RECV_GROUP(template_group);
  }
  else {
    return xnew sUBLK_MME_RECV_GROUP(template_group);
  }
}

#if BUILD_5G_LATTICE
static sUBLK_PORE_RECV_GROUP* allocate_pore_recv_group(sUBLK_RECV_GROUP* template_group)
{
  return xnew sUBLK_PORE_RECV_GROUP(template_group);
}
#endif

/*=================================================================================================
 * INITIALIZE RECEIVE GROUP ARRAYS
 * Called in exa_sim_sp.cc after receive buffers are initialized
*================================================================================================*/
void cSTRAND_MGR::allocate_recv_channels() {
  ccDOTIMES(rc, N_RECV_TYPES) {
    RECV_TYPE rtype =  static_cast<RECV_TYPE>(rc);
    m_recv_channel[rc].allocate_arrays(rtype, sim.num_scales, m_neighbor_sp_map.get_n_neighbor_sps());
  }
}


VOID initialize_recv_group_arrays_on_CPU() {
  g_strand_mgr.allocate_recv_channels();

  if ( sim.is_movb_sim() ) {
    sUBLK_IB_BF_RECV_GROUP::init_static_arrays(sim_num_scales());
    sBSURFEL_RECV_GROUP::init_static_arrays(sim_num_scales());
  }

  ccDOTIMES(scale, sim.num_scales) {
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(ngroup, scale) {

      g_strand_mgr.m_recv_channel[NEARBLK_RECV_TYPE].add_recv_group(ngroup);

      if ( sim.is_mme_comm_needed() ) {
        sUBLK_MME_RECV_GROUP *nmmegroup = allocate_mme_recv_group(ngroup);
        nmmegroup->allocate_recv_buffer();
        g_strand_mgr.m_recv_channel[NEARBLK_MME_RECV_TYPE].add_recv_group(nmmegroup);

      }
#if BUILD_5G_LATTICE
      if (sim.is_large_pore) {
	sUBLK_PORE_RECV_GROUP *nstategroup = allocate_pore_recv_group(ngroup);
        nstategroup->allocate_recv_buffer();
        g_strand_mgr.m_recv_channel[NEARBLK_PORE_RECV_TYPE].add_recv_group(nstategroup);
      }
#endif
    }

    DO_FARBLK_RECV_GROUPS_OF_SCALE(ngroup, scale) {
      g_strand_mgr.m_recv_channel[FARBLK_RECV_TYPE].add_recv_group(ngroup);

      if ( sim.is_mme_comm_needed() ) {
        sUBLK_MME_RECV_GROUP *fmmegroup = allocate_mme_recv_group(ngroup);
        fmmegroup->allocate_recv_buffer();

        g_strand_mgr.m_recv_channel[FARBLK_MME_RECV_TYPE].add_recv_group(fmmegroup);
      }
#if BUILD_5G_LATTICE
      if (sim.is_large_pore) {
	sUBLK_PORE_RECV_GROUP *fstategroup = allocate_pore_recv_group(ngroup);
        fstategroup->allocate_recv_buffer();

        g_strand_mgr.m_recv_channel[FARBLK_PORE_RECV_TYPE].add_recv_group(fstategroup);
      }
#endif
    }

    if ( sim.is_movb_sim() ) {
      // UBLK_IB_BF_RECV_GROUPs are based off UBLK_SEND_GROUPs because the
      // communication patterns are reversed
      DO_UBLK_SEND_GROUPS_OF_SCALE(send_group, scale) {
        sUBLK_IB_BF_RECV_GROUP* ublk_ib_bf_recv_group = g_ublk_ib_bf_recv_fset.find_or_create_group(send_group);
        ublk_ib_bf_recv_group->init();
        ublk_ib_bf_recv_group->allocate_recv_buffer();
        g_strand_mgr.m_recv_channel[ublk_ib_bf_recv_group->type()].add_recv_group(ublk_ib_bf_recv_group);
      }

      DO_NEARBLK_RECV_GROUPS_OF_SCALE(ngroup, scale) {
        sUBLK_IB_BF_BCAST_RECV_GROUP* ublk_ib_bf_bcast_recv_group = g_ublk_ib_bf_bcast_recv_fset.find_or_create_group(ngroup);
        ublk_ib_bf_bcast_recv_group->allocate_recv_buffer();
        g_strand_mgr.m_recv_channel[NEARBLK_IB_BF_BCAST_RECV_TYPE].add_recv_group(ublk_ib_bf_bcast_recv_group);
      }

      DO_FARBLK_RECV_GROUPS_OF_SCALE(fgroup, scale) {
        sUBLK_IB_BF_BCAST_RECV_GROUP* ublk_ib_bf_bcast_recv_group = g_ublk_ib_bf_bcast_recv_fset.find_or_create_group(fgroup);
        ublk_ib_bf_bcast_recv_group->allocate_recv_buffer();
        g_strand_mgr.m_recv_channel[FARBLK_IB_BF_BCAST_RECV_TYPE].add_recv_group(ublk_ib_bf_bcast_recv_group);
      }
    }

    DO_SURFEL_RECV_GROUPS_OF_SCALE(ngroup, scale) {
      g_strand_mgr.m_recv_channel[SURFEL_RECV_TYPE].add_recv_group(ngroup);
    }
    DO_WSURFEL_RECV_GROUPS_OF_SCALE(wsgroup, scale) {
      g_strand_mgr.m_recv_channel[WSURFEL_RECV_TYPE].add_recv_group(wsgroup);
    }
    DO_CONTACT_RECV_GROUPS_OF_SCALE(cgroup, scale) {
      g_strand_mgr.m_recv_channel[CONTACT_RECV_TYPE].add_recv_group(cgroup);
    }
  }

  if ( sim.is_movb_sim() ) {
    // bsurfel migration receives
    // we expect 1 message from each neighboring sp, indicating the incoming msg size count
    // all bsurfel receives go in the finest scale so they are processed each timestep
    for(auto nsp : g_strand_mgr.m_neighbor_sp_map) {
      sBSURFEL_RECV_GROUP* bsurfel_recv_group = g_bsurfel_comm_recv_fset.find_or_create_group(nsp);
      bsurfel_recv_group->init();
      g_strand_mgr.m_recv_channel[BSURFEL_RECV_TYPE].add_recv_group(bsurfel_recv_group);
    }
  }
  if (sim.is_particle_model) {
    create_parcel_recv_groups();
    DO_PARCEL_RECV_GROUPS_OF_SCALE(parcel_recv_group, FINEST_SCALE) {
      g_strand_mgr.m_recv_channel[PARCEL_RECV_TYPE].add_recv_group(parcel_recv_group);
    }
  }
}
#ifndef BUILD_GPU
VOID initialize_recv_group_arrays()
{
  initialize_recv_group_arrays_on_CPU();
}
#else
VOID initialize_recv_group_arrays() {
  g_strand_mgr.allocate_recv_channels();
  //setting up the receive channels with GPU receive groups is delayed until GPU mblks are created (see build_mblks_send_recv_groups() )
}
#endif

/*=================================================================================================
 * INITIALIZE RECEIVE CHANNELS
 * Called in exa_sim_sp.cc
*================================================================================================*/
VOID initialize_receive_channels() {
  ccDOTIMES(rc, N_RECV_TYPES) {
    g_strand_mgr.m_recv_channel[rc].init();
  }

  build_rp_recv_channel();

  // initialize_dependency_counters calls count_receive_dependencies, which depends on the above.
  // This is the earliest point at which it can be called.
  g_strand_mgr.initialize_dependency_counters();
}

/*=================================================================================================
 * POST INITIAL RECEIVES
 * Called by compute thread, see run_threads.cc
*================================================================================================*/
// To be called after initialization but before strands are created
#ifndef BUILD_GPU
VOID post_initial_receives()
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    DO_FARBLK_RECV_GROUPS_OF_SCALE(far_post_recv_group, scale) {
      far_post_recv_group->post_recv();
      // g_strand_mgr.AddToPostedReceivesList(far_post_recv_group);
    }
    DO_NEARBLK_RECV_GROUPS_OF_SCALE(near_post_recv_group, scale) {
      near_post_recv_group->post_recv();
      // g_strand_mgr.AddToPostedReceivesList(near_post_recv_group);
      LOG_MSG("RECV_DEPEND").format("Post initial recv type {} scale {} source {}", near_post_recv_group->m_recv_type,
            near_post_recv_group->m_scale, near_post_recv_group->m_source_sp);
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(post_recv_group, scale) {
      post_recv_group->post_recv();
      // g_strand_mgr.AddToPostedReceivesList(post_recv_group);
    }
  }

  if(sim.is_conduction_model) {
    DO_SCALES_COARSE_TO_FINE(scale) {
      DO_WSURFEL_RECV_GROUPS_OF_SCALE(wsurfel_recv_group, scale) {
        wsurfel_recv_group->post_recv();
      }
      DO_CONTACT_RECV_GROUPS_OF_SCALE(contact_recv_group, scale) {
        contact_recv_group->post_recv();
      }
    }
  }

  if (sim.is_particle_model) {
    DO_SCALES_COARSE_TO_FINE(scale) {
      DO_SURFEL_FILM_RECV_GROUPS_OF_SCALE(rgroup, scale)  {
        rgroup->post_recv();
      }
    }
  }

  if ( sim.is_movb_sim() ) {
    DO_SCALES_COARSE_TO_FINE(scale) {
      DO_UBLK_IB_BF_RECV_GROUPS_OF_SCALE(group_var,scale) {
        group_var->post_recv();
      }

      DO_UBLK_IB_BF_BCAST_RECV_GROUPS_OF_SCALE(group_var,scale) {
        group_var->post_recv();
      }
    }

    DO_BSURFEL_RECV_GROUPS(group_var) {
      group_var->post_recv();
    }
  }

  if ( sim.is_particle_model ) {
    DO_PARCEL_RECV_GROUPS(group_var) {
      group_var->post_recv();
    }
  }

  TIMESTEP start_timestep_cntr = g_timescale.start_timestep_cntr();
  SCALE mlrf_coarsest_active_scale = compute_coarsest_active_scale(start_timestep_cntr,  FINEST_SCALE);
  for(asINT32 mlrf_scale = FINEST_SCALE; mlrf_scale >= mlrf_coarsest_active_scale; --mlrf_scale) {
    auINT32 even_odd_mask = (sim_is_timestep_odd(mlrf_scale, start_timestep_cntr) ?
                             STP_PROCESS_ON_ODD_TIMES
                             : STP_PROCESS_ON_EVEN_TIMES);
    DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, mlrf_scale) {
      if (group->m_even_odd & even_odd_mask) {
        group->post_pre_dynamics_receives();
        group->post_post_dynamics_receives();
      }

      if (sim.is_particle_model) {
        group->post_parcel_count_receives();
      }

    }
  }

  if ( is_sliding_mesh_present() ) {
    g_strand_mgr.m_mlrf_comm.init_pre_dyn_recv();
    g_strand_mgr.m_mlrf_comm.init_post_dyn_recv();
  }

}
#else
void post_initial_receives()
{
  DO_SCALES_COARSE_TO_FINE(scale) {
    for(auto& recv_group : g_far_mblk_recv_fset.m_sets[scale]) {
      recv_group->post_recv();
    }
    
    for(auto& recv_group : g_near_mblk_recv_fset.m_sets[scale]) {
      recv_group->post_recv();
    }
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) {
      recv_group->post_recv();
    }    
  }

  TIMESTEP start_timestep_cntr = g_timescale.start_timestep_cntr();
 SCALE mlrf_coarsest_active_scale = compute_coarsest_active_scale(start_timestep_cntr,  FINEST_SCALE);
  for(asINT32 mlrf_scale = FINEST_SCALE; mlrf_scale >= mlrf_coarsest_active_scale; --mlrf_scale) {
    auINT32 even_odd_mask = (sim_is_timestep_odd(mlrf_scale, start_timestep_cntr) ?
                             STP_PROCESS_ON_ODD_TIMES
                             : STP_PROCESS_ON_EVEN_TIMES);
    DO_MLRF_SFL_OR_MSFL_GROUPS_OF_SCALE(group, mlrf_scale) {
      if (group->m_even_odd & even_odd_mask) {
        group->post_pre_dynamics_receives();
        group->post_post_dynamics_receives();
      }

      if (sim.is_particle_model) {
        group->post_parcel_count_receives();
      }

    }
  }

  if ( is_sliding_mesh_present() ) {
    g_strand_mgr.m_mlrf_comm.init_pre_dyn_recv();
    g_strand_mgr.m_mlrf_comm.init_post_dyn_recv();
  }  
}
#endif

/*=================================================================================================
 * PRINT_STRANDS
 * Used for logging strand states
*================================================================================================*/
#if ENABLE_PRINT_STRANDS
VOID PRINT_STRANDS(pthread_mutex_t& dcntr_mutex)
{
  pthread_mutex_lock(&dcntr_mutex);
  cLOG_MSG msg = get_log().on("PRINT_STRAND","strand",g_running_strand,"strand_time", g_timescale.m_timestep);

  msg.printf( "\nCalling strand %d strand_time %d\n", g_running_strand, g_timescale.m_timestep);

  // STRAND NUM   ==========================================================
  msg.printf( " Sim strand:     |"); \
  ccDOTIMES(strand, N_STRANDS) {
    msg.printf( "  %-2d |", strand);
  }
  msg.printf( "\n");

  // DCNTR VALUES  =========================================================
  msg.printf( "                 ------------------------------------\n");
  msg.printf( "    dcntr   :    |");
  ccDOTIMES(strand, N_STRANDS) {
    if ( g_strand_mgr.m_dcntr[strand] == g_strand_mgr.m_dcntr_invalid ) {
      msg.printf( "  IN |");
    } else {
      msg.printf( "  %-2d |", g_strand_mgr.m_dcntr[strand]);
    }
  }
  msg.printf( "\n");

  // Ready State ===========================================================
  msg.printf( "       ready:    |");
  ccDOTIMES(strand, N_STRANDS) {
    msg.printf( "  %-2d |", g_strand_mgr.m_ready_mask.test(strand));
  }
  msg.printf( "\n");

  // Run  State ===========================================================
  msg.printf( "    runstate:    |");
  ccDOTIMES(strand, N_STRANDS) {
    msg.printf( "  %-2d |", g_strand_mgr.m_runstate_mask.test(strand));
  }
  msg.printf( "\n");

  // Receive  Dep ===========================================================
  msg.printf( "    recv dep:    |");
  ccDOTIMES(strand, N_STRANDS) {
    msg.printf( "  %-2d |", g_strand_mgr.m_rcntr[strand]);
  }
  msg.printf( "\n");
  msg.end_msg();
  pthread_mutex_unlock(&dcntr_mutex);
}
#else
VOID PRINT_STRANDS(pthread_mutex_t& dcntr_mutex){}
#endif

#if ENABLE_PRINT_SENDQ
#define PRINT_SENDQ() \
{                                              \
  cLOG_MSG msg = comm_log().on("PRINT_SENDQ",LOG_FUNC,LOG_FILE,"strand",g_running_strand,"strand_time",g_timescale.m_time); \
  msg.printf(" Capacity %d head %d tail %d entries %d",  \
              g_strand_mgr.m_send_queue->m_capacity, g_strand_mgr.m_send_queue->m_head, \
              g_strand_mgr.m_send_queue->m_tail, g_strand_mgr.m_send_queue->n_entries()); \
}
#else
#define PRINT_SENDQ()
#endif

/*=================================================================================================
 * Does receive satisfy same timestep dependencies?
*================================================================================================*/
BOOLEAN does_recv_satisfy_same_timestep_dep(RECV_TYPE rtype){
  return(rtype == SURFEL_RECV_TYPE) ||
        (rtype == WSURFEL_RECV_TYPE) ||
        (rtype == CONTACT_RECV_TYPE) ||
        (rtype == FILM_RECV_TYPE) ||
        (rtype == FARBLK_MME_RECV_TYPE) ||
        (rtype == NEARBLK_MME_RECV_TYPE) ||
#if BUILD_5G_LATTICE
        (rtype == FARBLK_PORE_RECV_TYPE) ||
        (rtype == NEARBLK_PORE_RECV_TYPE) ||
#endif
        (rtype == FARBLK_IB_BF_RECV_TYPE) ||
        (rtype == NEARBLK_IB_BF_RECV_TYPE) ||
        (rtype == FARBLK_IB_BF_BCAST_RECV_TYPE) ||
        (rtype == NEARBLK_IB_BF_BCAST_RECV_TYPE);
}

/*=================================================================================================
 * Does receive satisfy cond and flow SP comm?
*================================================================================================*/
BOOLEAN does_recv_satisfy_cond_flow_sp_comm(RECV_TYPE rtype){
  return (rtype == WSURFEL_RECV_TYPE);
}
/*=================================================================================================
* Without surrendering, this is called once per strand per timestep.
* However with surrendering it may be called multiple times for the same strand.
*================================================================================================*/
VOID set_running_strand(STRAND new_strand, BOOLEAN new_timestep) {
  // We do this to communicate with the comm thread if the compute thread
  // is busy or not. Memory ordering doesn't matter for this purpose.
  tATOMIC_REF(g_running_strand).store(new_strand, std::memory_order_relaxed);
}

