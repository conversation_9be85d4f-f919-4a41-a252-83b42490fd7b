/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Checkpoints
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "ckpt.h"
#include "sim.h"
#include "event_queue.h"
#include "random.h"
#include "meas.h"
#include "fan.h"
#include "bsurfel_dyn_sp.h"
#include "shob_groups.h"
#include "strand_mgr.h"
#include LGI_INTERFACE_H

#ifndef _EXA_HPMPI
#include "MPI_mapping.h"
#include "hdf5_writer.h"
#include "utilities.h"
#include <chrono>
#include "timer.h"

std::unique_ptr<hpc_io::HDF5_writer> writer = nullptr;
hpc_io::utils::Timer timer;
hpc_io::utils::Timer mapsTimer;
#endif

#include PHYSICS_H

LGI_STREAM g_ckpt_lgi_stream = NULL;
LGI_STREAM g_cond_ckpt_lgi_stream = NULL;

cBOOLEAN g_is_equilibrium_ckpt = FALSE;

sCKPT_GROUP g_ckpt_group;

VOID determine_if_equilibrium_ckpt()
{
  g_is_equilibrium_ckpt = vr_equilibrium_timestep_p(g_timescale.m_timestep, COARSEST_SCALE);
}

VOID add_initial_checkpoints_to_event_queue()
{
  if (sim_run_info.mme_chkpnt_at_end) {
    add_event(new_event(EVENT_ID_MME_CKPT, 0, BASETIME_LAST));
  }

  if (sim_run_info.full_chkpnt_at_end) {
    add_event(new_event(EVENT_ID_FULL_CKPT, 0, BASETIME_LAST));
  }

  // @@@ The CP alone should be handling the calc of the first checkpoint time.
  BASETIME start = sim_run_info.initial_checkpoint;
  BASETIME period = sim_run_info.next_checkpoint; // @@@ Talk about name abuse
  if ((start >= 0) &&
      (period > 0)) {
    BASETIME min_start = (sim_run_info.allow_immediate_checkpoints
			  ? g_timescale.m_start_time
			  : g_timescale.m_start_time + 1);
    while (start < min_start)
      start += period;

    add_event(new_event(sim_run_info.run_options & SIM_FULL_CKPTS ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT,
				     0, start, period, BASETIME_LAST));
  }
}

static VOID write_ublk_ckpt_data()
{
  DO_REALMS(realm) {
    auINT64 n_ublks = g_ckpt_group.m_ckpt_ublks[realm].size();
    ccDOTIMES(nu, n_ublks) {
      UBLK ublk = g_ckpt_group.m_ckpt_ublks[realm][nu];
      if (ublk->has_real_ckpt_data()) {
        DGF_SHOB_CKPT_LEN len = (DGF_SHOB_CKPT_LEN) (ublk->ckpt_len());
        write_ckpt_shob_header(ublk->id(), len);
        //msg_print("Writing ublk ckpt header:realm %d id %d, len %d",realm,ublk->id(), len);
        ublk->write_ckpt(len);
      } else {
        DGF_SHOB_CKPT_LEN len = 0;
        write_ckpt_shob_header(ublk->id(), len);
      }
    }
  }
}

static VOID write_surfel_ckpt_data()
{
  DO_REALMS(realm) {
    auINT64 n_surfels = g_ckpt_group.m_ckpt_surfels[realm].size();
    ccDOTIMES(ns, n_surfels) {
      if (g_ckpt_group.is_sampling_surfel(ns, realm)) {
        sSAMPLING_SURFEL *sampling_surfel = g_ckpt_group.get_sampling_surfel(ns, realm);
        DGF_SHOB_CKPT_LEN len = (DGF_SHOB_CKPT_LEN) sampling_surfel->ckpt_len();
        write_ckpt_shob_header(sampling_surfel->id(), len);
        sampling_surfel->write_ckpt();
      } else {
        SURFEL surfel = g_ckpt_group.m_ckpt_surfels[realm][ns];
        DGF_SHOB_CKPT_LEN len = (DGF_SHOB_CKPT_LEN) surfel->ckpt_len();
        // LOG_ON("CKPT").printf("ckpt surfel %d %u", surfel->id(), len);
        write_ckpt_shob_header(surfel->id(), len);
        surfel->write_ckpt();
      }
    }
  }
}

static VOID write_eqn_rand_state()
{
  LGI_RANDOM_SEED_REC record;
  asINT32 rand_state_size = exprlang_rand_state_size();
  asINT32 record_length = sizeof(record) + 2 * rand_state_size;
  lgi_write_init_tag (&record, LGI_CKPT_EQN_RANDOM_SEED_TAG, record_length);
  record.n_bytes_rand_state = rand_state_size;

  write_ckpt_lgi_head(record);

  char *rand_state = exprlang_rand_state();
  write_ckpt_lgi(rand_state, rand_state_size);

  char *randt_state = exprlang_randt_state();
  write_ckpt_lgi(randt_state, rand_state_size);
}
// called by Comm thread
static VOID write_full_ckpt_to_lgi()
{

  // Header stuff - written once
  //TODO Vinit: IS it needed ?
  //determine_if_equilibrium_ckpt();
  if (sim.is_particle_model) {
    // The parcels should be sent and received before full checkpoint can happen
    particle_sim.send_parcels_before_ckpt();
    particle_sim.complete_parcel_recvs();
  }

  lgi_write_header(g_ckpt_lgi_stream, LGI_CHECKPOINT_VERSION);

  // Random number generators
  g_random_num_generator.ckpt();

  write_eqn_rand_state();

  // Checkpoint turb_info if synthesizing turbulent velocity time histories
  if (my_proc_id == g_turb_info.m_checkpointer) {
    if (g_turb_info.m_vturb_synth.size() > 0) {
      g_turb_info.ckpt_turb_synth_info_state();
    }
  }

#if !BUILD_GPU
  if (sim.is_radiation_model) {
    // send 1 flow sp and 1 conduction sp as representatives
    bool send_radiation_tm = false;
    if (my_proc_id == 0) {
      send_radiation_tm = true;
    } 

    if (sim.is_flow && sim.is_conduction_model && sim.is_conduction_sp && (my_proc_id == total_sps/2)) {
      send_radiation_tm = true;
    } 

    if (send_radiation_tm) {
      g_timescale.m_radiation_tm.write_ckpt(g_timescale.m_lb_tm, g_timescale.m_conduction_pde_tm);
    }
  }
#endif

  //  Write ublks to be consistent with LGI read
  LGI_TAG tag;
  tag.id = LGI_SHOB_STATE_TAG;
  tag.length = 0; // variable length
  write_ckpt_lgi_head(tag);
  write_ublk_ckpt_data();

  //  Write surfels to be consistent with LGI read
  tag.id = LGI_SHOB_STATE_TAG;
  tag.length = 0; // variable length
  write_ckpt_lgi_head(tag);
  write_surfel_ckpt_data();

  // write_bsurfel_ckpt_data();

  // Write all meas window data
  tag.id = DGF_CKPT_MEAS_WINDOW_TAG;
  tag.length = 0; // variable length
  write_ckpt_lgi_head(tag);
  TIMESTEP num_timesteps_done = g_timescale.solver_time(sim.is_conduction_sp);
  DO_MEAS_WINDOWS(meas_window) {
    if (meas_window->is_average_mme)
      continue;
    meas_window->write_ckpt(num_timesteps_done);
  }
  if (my_proc_id == 0) {
    // Reference frame data should be identical for all SPs, so only SP 0 writes ckpt data
    sim.ckpt_grf_state();
    sim.ckpt_lrf_state();
    sim.ckpt_movb_state();
#if BUILD_D19_LATTICE
    // thermal accel data should be identical for all SPs, so only SP 0 writes
    // ckpt data. This is a vector of size two. One entry will be written for
    // each call. Note that they will have to be restored separately as well.
    sim.ckpt_thermal_accel_state();
#endif
  }
  ckpt_fan_descs();

  // Write averaged averaged contact data
  if (g_thermal_averaged_contacts.is_enabled()) {
    g_thermal_averaged_contacts.write_ckpt_data();
  }

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  particle_sim.write_ckpt(); //checkpoint particle emitter related states
//#endif
}

#ifndef _EXA_HPMPI
using map_idx_t = MPI_Map_hdf5::indexes_t;
using map_sz_t = MPI_Map_hdf5::sizes_t;

void fill_ublks_map_arrays(size_t& local_sz, STP_REALM realm, size_t n_shobs, map_idx_t *const shob_idxs, map_sz_t * const shob_sizes)
{
  for (auINT64 nn = 0; nn < n_shobs; ++nn)
  {
    UBLK & ublk = g_ckpt_group.m_ckpt_ublks[realm][nn];
    shob_idxs[nn] = ublk->id();
    shob_sizes[nn] = ublk->ckpt_len()+sizeof(cDGF_CKPT_SHOB_HEADER);
    local_sz += shob_sizes[nn];
  }
}

void fill_surfels_map_arrays( size_t& local_sz, STP_REALM realm, size_t n_shobs, map_idx_t *const shob_idxs, map_sz_t * const shob_sizes)
{
  for (auINT64 nn = 0; nn < n_shobs; ++nn)
  {
    if (g_ckpt_group.is_sampling_surfel(nn, realm))
    {
      sSAMPLING_SURFEL *sampling_surfel = g_ckpt_group.get_sampling_surfel(nn, realm);
      shob_idxs[nn] = sampling_surfel->id() ;
      shob_sizes[nn] = sampling_surfel->ckpt_len()+sizeof(cDGF_CKPT_SHOB_HEADER);
    }
    else
    {
      SURFEL surfel = g_ckpt_group.m_ckpt_surfels[realm][nn];
      shob_idxs[nn] = surfel->id() ;
      shob_sizes[nn] = surfel->ckpt_len()+sizeof(cDGF_CKPT_SHOB_HEADER);
    }
    local_sz += shob_sizes[nn];
  }
}

size_t count_num_meas_win_chunks(size_t n_windows)
{
  size_t num_chunks = n_windows;
  for( size_t w_idx = 0; w_idx< n_windows; w_idx++)
    if (g_meas_windows[w_idx]->m_is_master_sp)
      num_chunks++;
  return num_chunks;
}

/*
void fill_meas_windows_map_arrays(size_t& local_sz, size_t n_windows, map_idx_t *const shob_idxs, map_sz_t * const shob_sizes)
{
  size_t s_idx = 0;
  for( size_t w_idx = 0; w_idx< n_windows; w_idx++)
  {
    if (g_meas_windows[w_idx]->m_is_master_sp)
    {
      // Master data
      shob_idxs[s_idx] = w_idx * (total_sps + 1);
      shob_sizes[s_idx] = g_meas_windows[w_idx]->ckpt_master_len(g_timescale.m_timestep);
      local_sz += shob_sizes[s_idx];
      s_idx++;
    }

    // Ordinary data
    shob_idxs[s_idx] = w_idx * (total_sps + 1) + my_proc_id + 1;
    shob_sizes[s_idx] = g_meas_windows[w_idx]->ckpt_ordinary_len(g_timescale.m_timestep);
    local_sz += shob_sizes[s_idx];
    s_idx++;
  }
}
*/

template<hpc_io::shob_t::type SHOB_TYPE>
void build_shob_id_map_data(MPI_Map_hdf5 & shobsMapSrc, std::string& shob_name)
{
  // Create the communicator of maps
  MPI_MapComm_hdf5 mapComm(eMPI_sp_comm);
  hpc_io::utils::Timer debTimer;
  debTimer.restart();
  // Compute the map on destination
  mapComm.calculateMapsInAll(&shobsMapSrc);
  // hpc_io::utils::master_sync_print<hpc_io::utils::SP>(eMPI_sp_comm, my_proc_id, "calculateMapsInAll %s map took %f seconds \n  ", shob_name.c_str(),debTimer.getTime());
  debTimer.restart();

  // Get map on remote
  const MPI_Map_hdf5 & shobsMapDst = mapComm.localDstMap();
  // Saving data related with maps
  // debugging purposes
  const char* saveMaps = getenv("SAVE_CKPT_MAPS");
  if (saveMaps)
  {
    timer.stop();
    size_t * const mapSizes = new size_t[total_sps];
    size_t * const mapOffsets = new size_t[total_sps + 1];

    constexpr int mapLengthSize = sizeof(MPI_Map_hdf5::length_t);
    const auto mapLength = shobsMapDst.length();
    MPI_Allgather((void*) &mapLength, mapLengthSize, MPI_BYTE, mapSizes, mapLengthSize, MPI_BYTE, eMPI_sp_comm);

    // Open binary file to save the data of the maps in parallel
    MPI_File fid;
    {
      char fileName[FILENAME_MAXLEN];
      sprintf(fileName, "%s_%s", shob_name.c_str(),"mapsData.bin");
      // Open file if it not exists
      int err = MPI_File_open(eMPI_sp_comm, fileName,
                              MPI_MODE_CREATE | MPI_MODE_EXCL |
                              MPI_MODE_WRONLY, MPI_INFO_NULL, &fid);

      // If the file already exists...
      if (err != MPI_SUCCESS)
      {
        // Delete existing verion
        if (my_proc_id == 0)
          MPI_File_delete(fileName, MPI_INFO_NULL);
        // Then create and open an empty file
        MPI_File_open(eMPI_sp_comm, fileName,
                      MPI_MODE_CREATE | MPI_MODE_EXCL | MPI_MODE_WRONLY,
                      MPI_INFO_NULL, &fid);
      }
    }

    // Computing offsets
    mapOffsets[0] = sizeof(int) + sizeof(size_t) * total_sps + 4 * sizeof(int);
    for (int nn = 1; nn < total_sps + 1; ++nn)
      mapOffsets[nn] = mapOffsets[nn - 1] + mapSizes[nn - 1] * (MPI_Map_hdf5::structSize() + sizeof(MPI_Map_hdf5::offsets_t));

    // Request to synchronize with writting taskes
    MPI_Request allwritingRequests[8];

    // Write number of threads and sizes
    if (my_proc_id == 0)
    {
      MPI_File_iwrite_at(fid, 0, &total_sps, sizeof(int), MPI_BYTE, allwritingRequests);
      MPI_File_iwrite_at(fid, sizeof(int), mapSizes, sizeof(size_t) * total_sps, MPI_BYTE, allwritingRequests + 1);
      // Data lengths
      int dataSizes[4] = {sizeof(MPI_Map_hdf5::indexes_t),
                          sizeof(MPI_Map_hdf5::lengths_t),
                          sizeof(MPI_Map_hdf5::offsets_t),
                          sizeof(MPI_Map_hdf5::sizes_t  )};
      MPI_File_iwrite_at(fid, sizeof(int) + sizeof(size_t) * total_sps, dataSizes, sizeof(size_t) * 4, MPI_BYTE, allwritingRequests + 2);
    }

    // Saving data of current src and dst map
    {
      // Computing memory offsets for each array of data
      size_t dataOffsets[5];
      dataOffsets[0] = mapOffsets[my_proc_id];
      dataOffsets[1] = dataOffsets[0] + sizeof(MPI_Map_hdf5::indexes_t) * mapLength;
      dataOffsets[2] = dataOffsets[1] + sizeof(MPI_Map_hdf5::lengths_t) * mapLength;
      dataOffsets[3] = dataOffsets[2] + sizeof(MPI_Map_hdf5::offsets_t) * mapLength;
      dataOffsets[4] = dataOffsets[3] + sizeof(MPI_Map_hdf5::offsets_t) * mapLength;

      // Saving data
      MPI_File_iwrite_at(fid, dataOffsets[0], shobsMapDst.indexes(), sizeof(MPI_Map_hdf5::indexes_t) * mapLength, MPI_BYTE, allwritingRequests + 3);
      MPI_File_iwrite_at(fid, dataOffsets[1], shobsMapDst.lengths(), sizeof(MPI_Map_hdf5::lengths_t) * mapLength, MPI_BYTE, allwritingRequests + 4);
      MPI_File_iwrite_at(fid, dataOffsets[2], shobsMapDst.offsets(), sizeof(MPI_Map_hdf5::offsets_t) * mapLength, MPI_BYTE, allwritingRequests + 5);
      MPI_File_iwrite_at(fid, dataOffsets[3], shobsMapDst.offsets(), sizeof(MPI_Map_hdf5::offsets_t) * mapLength, MPI_BYTE, allwritingRequests + 6);
      MPI_File_iwrite_at(fid, dataOffsets[4], shobsMapDst.sizes  (), sizeof(MPI_Map_hdf5::sizes_t  ) * mapLength, MPI_BYTE, allwritingRequests + 7);
    }

    // Wating for saving
    MPI_Waitall(my_proc_id == 0 ? 8 : 5, allwritingRequests + ( my_proc_id == 0 ? 0 : 3), MPI_STATUSES_IGNORE);

    // Free memory
    delete [] mapOffsets;
    delete [] mapSizes;

    // Close file
    MPI_check( MPI_File_close(&fid) );
    timer.resume();
  }

  writer->appropriate_map_data<SHOB_TYPE>(shobsMapSrc.length(), shobsMapDst.offsets(), 
    shobsMapDst.sizes(), shobsMapDst.lengths(), mapComm.getGlobalMemoryRange());
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Finished Building %s map num_chunks=%zu LmemRange=%zu GmemRange=%zu\n",
                                     shob_name.c_str(), shobsMapSrc.length(),shobsMapSrc.memoryRange(), mapComm.getGlobalMemoryRange());
}

template<hpc_io::shob_t::type SHOB_TYPE>
void build_shob_id_map_data(std::function<void(size_t&, STP_REALM, size_t, map_idx_t *const, map_sz_t * const)> fill_map_arrays, STP_REALM realm,
                            size_t n_shobs, size_t& local_size, std::string& shob_name)
{

  // hpc_io::utils::master_sync_print<hpc_io::utils::SP>(eMPI_sp_comm, my_proc_id, "Building %s map with n_shobs %lu\n",shob_name.c_str(), n_shobs);
  // Creating two arrays, one for the indexes and other for the sizes of each uBlock
  // Note: this can be also done without the intermediate state of filling this arrays
  map_idx_t * const shob_idxs = new map_idx_t[n_shobs];
  map_sz_t * const shob_sizes = new map_sz_t[n_shobs];

  // Fill the arrays with the size and index of each uBlock
  fill_map_arrays(local_size, realm, n_shobs, shob_idxs, shob_sizes);
  // Create the map from the arrays for current buffer
  MPI_Map_hdf5 shobsMapSrc;
  shobsMapSrc.setMaxChunkSize(writer->buff_sz());
  MPI_Map_hdf5::generateMapFromElements(n_shobs, shob_idxs, shob_sizes, &shobsMapSrc);

    // Free memory
  delete [] shob_idxs;
  delete [] shob_sizes;
  
  // Build map
  build_shob_id_map_data<SHOB_TYPE>(shobsMapSrc, shob_name);  
}

size_t copy_ublk_to_buffer(sCKPT_BUFFER& pio_ckpt_buff, STP_REALM realm, int idx0, int idx1)
{
  size_t copied_size = (sizeof(SHOB_ID) + sizeof(DGF_SHOB_CKPT_LEN)) * (idx1 - idx0);
  for (int ublk_idx = idx0; ublk_idx < idx1; ++ublk_idx)
  {
    UBLK ublk = g_ckpt_group.m_ckpt_ublks[realm].at(ublk_idx);

    const SHOB_ID id = ublk->id();
    pio_ckpt_buff.write(&id);

    const DGF_SHOB_CKPT_LEN len = static_cast<DGF_SHOB_CKPT_LEN>(ublk->ckpt_len());
    pio_ckpt_buff.write(&len);

    if (ublk->has_real_ckpt_data()) {
      copied_size += ublk->write_ckpt(pio_ckpt_buff);
    }
  }
  return copied_size;
}

size_t copy_surfel_to_buffer(sCKPT_BUFFER& pio_ckpt_buff, STP_REALM realm, int idx0, int idx1)
{
  size_t copied_size = (sizeof(SHOB_ID) + sizeof(DGF_SHOB_CKPT_LEN)) * (idx1 - idx0);
  for (int surf_idx = idx0; surf_idx < idx1; ++surf_idx)
  {
    if (g_ckpt_group.is_sampling_surfel(surf_idx, realm)) {
      sSAMPLING_SURFEL *sampling_surfel = g_ckpt_group.get_sampling_surfel(surf_idx, realm);

      const SHOB_ID id = sampling_surfel->id();
      pio_ckpt_buff.write(&id);

      const DGF_SHOB_CKPT_LEN len = sampling_surfel->ckpt_len();
      pio_ckpt_buff.write(&len);

      copied_size += sampling_surfel->write_ckpt(pio_ckpt_buff);

    } else {
      SURFEL surfel = g_ckpt_group.m_ckpt_surfels[realm][surf_idx];

      const SHOB_ID id = surfel->id();
      pio_ckpt_buff.write(&id);

      const DGF_SHOB_CKPT_LEN len = surfel->ckpt_len();
      pio_ckpt_buff.write(&len);

      copied_size += surfel->write_ckpt(pio_ckpt_buff);
    }
  }
  return copied_size;//ublks return sizes of copied data
}

template<hpc_io::shob_t::type SHOB_TYPE>
void write_mapped_data_to_hdf5(sCKPT_BUFFER& pio_ckpt_buff, std::function<size_t(sCKPT_BUFFER& pio_ckpt_buff, STP_REALM, int, int)> copy_shob_to_buffer, STP_REALM realm, size_t n_shobs, size_t local_sz)
{
  // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Started write_mapped_data_to_hdf5\n");
  hpc_io::utils::Timer writtingTimer;
  const auto map = writer->shobs_map<SHOB_TYPE>();
  writer->init_multiple_writes<SHOB_TYPE>();
  bool select_set = true;
  size_t start_shob_idx = 0;
  size_t accum_sz = 0;
  size_t end_shob_idx = 0;
  size_t overall_accum_sz = 0;
  /* Needed to log progress from CP */
  MPI_Request written_shobs_request = MPI_REQUEST_NULL;
  int  written_shobs_request_flag = false;
  uint64_t written_shobs=0;

  for (size_t chunk = 0; chunk < map->num_chunks(); chunk++)
  {
    if constexpr ( SHOB_TYPE != hpc_io::shob_t::MWINS)
      MPI_Test(&written_shobs_request, &written_shobs_request_flag, MPI_STATUS_IGNORE); // This tells MPI to keep processing messages.
    writer->set_filedataspace_slab<SHOB_TYPE>(chunk, select_set);
    select_set = false;
    accum_sz += map->size(chunk);
    end_shob_idx += map->length(chunk);
    // printf("\e[38;5;081m[   INFO  ] chunk from %lu to %lu\e[0m\n", start_shob_idx, end_shob_idx);
    if (writer->buffer_needs_writing<SHOB_TYPE>(chunk, accum_sz))
    {
      size_t copied_sz=0;
      pio_ckpt_buff.allocate(accum_sz);
      copied_sz += copy_shob_to_buffer(pio_ckpt_buff, realm, start_shob_idx, end_shob_idx);
      cassert(copied_sz == accum_sz && "accum_sz must match copied_sz");
      writer->write_shobs_partial(pio_ckpt_buff.m_buffer, accum_sz);
      if constexpr ( SHOB_TYPE != hpc_io::shob_t::MWINS)
      {
        MPI_Wait(&written_shobs_request, MPI_STATUS_IGNORE);

        written_shobs += end_shob_idx - start_shob_idx; // Don’t modify written_shobs until after MPI_Wait()!

        MPI_Ireduce(&written_shobs, nullptr, 1, MPI_UINT64_T, MPI_SUM, eMPI_sp_cp_rank(), eMPI_sp_cp_comm, 
                      &written_shobs_request);
      }

      start_shob_idx = end_shob_idx;
      overall_accum_sz += accum_sz;
      // printf("\e[38;5;208m[   INFO  ] overall_accum_sz: %lu.   accum_sz: %lu\e[0m\n", overall_accum_sz, accum_sz);
      select_set = true;
      pio_ckpt_buff.deallocate();
      accum_sz = 0;
    }
  }
  //we need this for the last Ireduce
  if constexpr (SHOB_TYPE != hpc_io::shob_t::MWINS)
    MPI_Wait(&written_shobs_request, MPI_STATUS_IGNORE);
  //We have to call the same number to H5DWrite from all SPs in collective Mode
  writer->finish_partial_writes<SHOB_TYPE>(eMPI_sp_cp_rank(), written_shobs);


  if constexpr (SHOB_TYPE == hpc_io::shob_t::MWINS)
    writer->close_meas_window();
  else
  {
    //we need this for the last Ireduce
    writer->close_shobs_hdls();
  }

  cassert(overall_accum_sz == local_sz && "overall_accum_sz must match local_sz");
  cassert(end_shob_idx == n_shobs && "end_shob_idx must match n_shobs");
  // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Finished write_mapped_data_to_hdf5 in %f seconds\n", writtingTimer.getTime());

}

static VOID write_ublk_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Starting write_ublk_ckpt() at ckpt.cc\n");

  timer.restart();
  static size_t local_sz= 0; //amount of memory used by all local ublks
  writer->create_ublks_group();
  std::string shob_name("Ublk");
  STP_REALM realm = sim.is_conduction_sp ? STP_COND_REALM : STP_FLOW_REALM;
  if(writer->shobs_map<hpc_io::shob_t::UBLKS>() == nullptr)
  {
    mapsTimer.restart();
    build_shob_id_map_data<hpc_io::shob_t::UBLKS>(fill_ublks_map_arrays, realm, g_ckpt_group.m_ckpt_ublks[realm].size(), local_sz, shob_name);
    size_t memory_map = writer->shobs_map<hpc_io::shob_t::UBLKS>()->get_map_memory_consumption();
    hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "%s Maps took %f seconds consuming %s \n", 
      shob_name.c_str(), mapsTimer.getTime(), hpc_io::utils::bytesToHuman(memory_map).c_str());
  }
  
  // Not needed until SPs are able to read
  // writer->write_map<hpc_io::shob_t::UBLKS>();


  write_mapped_data_to_hdf5<hpc_io::shob_t::UBLKS>(pio_ckpt_buff, copy_ublk_to_buffer, realm, g_ckpt_group.m_ckpt_ublks[realm].size(), local_sz);
  // MPI_Barrier(eMPI_sp_comm);
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "write_ublk_ckpt() with localSize of %s with buffsize %s at ckpt.cc in %f seconds.\n",
    hpc_io::utils::bytesToHuman(local_sz).c_str(), hpc_io::utils::bytesToHuman(writer->buff_sz()).c_str(), timer.getTime());
}

static VOID write_surfel_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Starting write_surfel_ckpt_data() at ckpt.cc\n");
  timer.restart();
  static size_t local_sz= 0; //amount of memory used by all local surfels
  writer->create_surfels_group();
  std::string shob_name("Surfel");
  STP_REALM realm = sim.is_conduction_sp ? STP_COND_REALM : STP_FLOW_REALM;
  if(writer->shobs_map<hpc_io::shob_t::SURFELS>() == nullptr)
  {
    mapsTimer.restart();
    build_shob_id_map_data<hpc_io::shob_t::SURFELS>(fill_surfels_map_arrays, realm, g_ckpt_group.m_ckpt_surfels[realm].size(), local_sz, shob_name);
    size_t memory_map = writer->shobs_map<hpc_io::shob_t::SURFELS>()->get_map_memory_consumption();
    hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "%s Maps took %f seconds consuming %s \n", 
    shob_name.c_str(), mapsTimer.getTime(), hpc_io::utils::bytesToHuman(memory_map).c_str());
  }
  // Not needed until SPs are able to read
  // writer->write_map<hpc_io::shob_t::SURFELS>();

  write_mapped_data_to_hdf5<hpc_io::shob_t::SURFELS>(pio_ckpt_buff, copy_surfel_to_buffer, realm, g_ckpt_group.m_ckpt_surfels[realm].size(), local_sz);
  // MPI_Barrier(eMPI_sp_comm);
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "write_surfel_ckpt_data() with localSize of %s with buffsize %s at ckpt.cc in %f seconds.\n",
    hpc_io::utils::bytesToHuman(local_sz).c_str(), hpc_io::utils::bytesToHuman(writer->buff_sz()).c_str(), timer.getTime());
}

static VOID write_meas_windows_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Starting write_meas_windows_ckpt() at ckpt.cc\n");
  timer.restart();
  // Create space in HDF5 file
  writer->create_sp_meas_windows_group();

  // For each measurement window
  const int n_win = g_n_meas_windows();
  size_t total_local_sz=0;
  for (int win_idx = 0; win_idx < n_win; ++win_idx)
  {
    hpc_io::utils::Timer winTimer;
    hpc_io::utils::Timer subTimer;
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "Init window loop: %i/%i n_stationary_meas_cells=%d n_global_moving_meas_cells=%d\n", 
    //                                           win_idx, n_win-1, g_meas_windows[win_idx]->n_stationary_meas_cells, g_meas_windows[win_idx]->n_global_moving_meas_cells);
    // Create source map (empty)
    MPI_Map_hdf5 srcMap;
    bool has_meas_cells = g_meas_windows[win_idx]->has_meas_cells();
    size_t local_sz=0;
    TIMESTEP current_timestep = g_timescale.solver_time(sim.is_conduction_sp);
    // Next if there is no data to save
    if (g_meas_windows[win_idx]->avoid_ckpt())
    {
      // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "avoid_ckpt: %i\n",win_idx);
      continue;
    }
    if(has_meas_cells)
    {
      // Open communication to send and receive essential data requiered to compute the maps
      // ToDo: move to other place to overlap communications
      subTimer.restart();
      g_meas_windows[win_idx]->open_inter_comm(current_timestep);
      g_meas_windows[win_idx]->open_comm_with_cp(current_timestep);
      
      // This function calculates the total size of the memory to be saved and opens the communication
      local_sz = g_meas_windows[win_idx]->ckpt_len_mapped(current_timestep, &srcMap);
      // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "DEBUG ckpt_len_mapped %i took %f seconds\n",win_idx, subTimer.getTime());
      total_local_sz += local_sz;
    }
    // If there is no data to be saved from the SPs then pass to the next window
    bool map_free = srcMap.isFree();
    bool all_map_free = false;
    MPI_Allreduce(&map_free, &all_map_free, 1, MPI_C_BOOL, MPI_LAND, eMPI_sp_comm);
    if(all_map_free)
    {
      // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "srcMap.isFree(): %i\n",win_idx);
      continue;
    }
    else if(!has_meas_cells)
    {
      srcMap.allocate(1024);
    }
    // Create group for current window
    {
      std::stringstream ss;
      ss << "Win_" << g_meas_windows[win_idx]->index;
      // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, " Saving_window %s\n", ss.str().c_str());
      writer->create_meas_window(ss.str());
    }

    // Solving maps
    std::string shob_name("Measwin");
    subTimer.restart();
    build_shob_id_map_data<hpc_io::shob_t::MWINS>(srcMap, shob_name);

    // Summation of sizes for every chunk
    MPI_Map_hdf5::sizes_t num_elements_in_map = srcMap.sumChunkLengths();
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "MWprof build_shob_id_map_data: %i with num_elems: %zu took %f seconds\n",win_idx, num_elements_in_map, subTimer.getTime());

    if(has_meas_cells)
    {   
      
      // Saving data
      if (g_meas_windows[win_idx]->m_is_master_sp)
      {
        // Lambda function to save data
        auto meas_win_to_buffer_master = [&](sCKPT_BUFFER& buffer, STP_REALM, int idx0, int idx1)
        {
          // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "Running master: %i\n", win_idx);
          return g_meas_windows[win_idx]->write_ckpt_mapped_master(buffer, current_timestep, idx0, idx1);
        };
        subTimer.restart();
        write_mapped_data_to_hdf5<hpc_io::shob_t::MWINS>(pio_ckpt_buff, meas_win_to_buffer_master, STP_INVALID_REALM, num_elements_in_map, local_sz);
        // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "MWprof write_mapped_data_to_hdf5_master: %i took %f seconds\n",win_idx, subTimer.getTime());

      }
      else
      {
        // Lambda function to save data
        auto meas_win_to_buffer_ordinary = [&](sCKPT_BUFFER& buffer, STP_REALM, int idx0, int idx1)
        {
          // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "Running ordinary: %i\n", win_idx);
          return g_meas_windows[win_idx]->write_ckpt_mapped_ordinary(buffer, current_timestep, idx0, idx1);
        };
        subTimer.restart();
        write_mapped_data_to_hdf5<hpc_io::shob_t::MWINS>(pio_ckpt_buff, meas_win_to_buffer_ordinary, STP_INVALID_REALM, num_elements_in_map, local_sz);
        // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "MWprof write_mapped_data_to_hdf5_ordinary: %i took %f seconds\n",win_idx, subTimer.getTime());
      }
    }
    else
    {
      //dummy write:
      auto no_data_to_buffer = [&]( [[maybe_unused]] sCKPT_BUFFER& buffer, STP_REALM, [[maybe_unused]] int idx0, [[maybe_unused]] int idx1)
        {
          // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "Running no_data_to_buffer: %i\n", win_idx);
          return 0;
        };
        subTimer.restart();
        write_mapped_data_to_hdf5<hpc_io::shob_t::MWINS>(pio_ckpt_buff, no_data_to_buffer, STP_INVALID_REALM, num_elements_in_map, local_sz);
        // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "MWprof write_mapped_data_to_hdf5_ordinary: %i took %f seconds\n",win_idx, subTimer.getTime());
    }
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "Saved window: %i g_meas_windows[win_idx]->index %i in %f seconds\n", win_idx, g_meas_windows[win_idx]->index, winTimer.getTime());
  }
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "write_meas_windows_ckpt() with total local size of %s with buffsize %s at ckpt.cc in %f seconds.\n",
                hpc_io::utils::bytesToHuman(total_local_sz).c_str(), hpc_io::utils::bytesToHuman(writer->buff_sz()).c_str(), timer.getTime());
  // Close HDF5 SP meas windows group
  writer->close_meas_windown_parent_group();
}

static VOID write_eqn_rand_state(sCKPT_BUFFER& pio_ckpt_buff)
{
  asINT32 rand_state_size = exprlang_rand_state_size();
  size_t ckpt_len = 2 * rand_state_size + sizeof(size_t);
  pio_ckpt_buff.write(&ckpt_len);
  char *rand_state = exprlang_rand_state();
  pio_ckpt_buff.write(rand_state, rand_state_size);

  char *randt_state = exprlang_randt_state();
  pio_ckpt_buff.write(randt_state, rand_state_size);
}
void write_light_data_grouped(sCKPT_BUFFER& pio_ckpt_buff)
{
  timer.restart();
  unsigned long long size = sizeof(unsigned long long)
              + g_random_num_generator.state_size() + sizeof(size_t)
              + 2 * exprlang_rand_state_size() + sizeof(size_t)
              + ckpt_fan_descs_len()
              + particle_sim.ckpt_len()
              + (my_proc_id == g_turb_info.m_checkpointer ?
                  g_turb_info.ckpt_len()
                  : sizeof(size_t));
  if (my_proc_id == 0) {
    // Reference frame data should be identical for all SPs, so only SP 0 writes ckpt data
    size += sim.ckpt_state_len<sGRF>();
    size += sim.ckpt_state_len<sLRF_PHYSICS_DESCRIPTOR>();
    size += sim.ckpt_state_len<MOVB_PHYSICS_DESCRIPTOR>();
    // sim.ckpt_grf_state();
    // sim.ckpt_lrf_state();
    // sim.ckpt_movb_state();
#if BUILD_D19_LATTICE
    // thermal accel data should be identical for all SPs, so only SP 0 writes
    // ckpt data. This is a vector of size two. One entry will be written for
    // each call. Note that they will have to be restored separately as well.
    size += sim.ckpt_state_len<sTHERMAL_ACCEL_INFO>();
    // sim.ckpt_thermal_accel_state();
#else
    size += sizeof(size_t);
#endif
  }
  else
    size += 4 * sizeof(size_t);

  pio_ckpt_buff.allocate(size);

  //write combined size
  //check if H5Dget_storage_size() can be used when reading to avoid this
  pio_ckpt_buff.write(&size);
  // Random number generators
  g_random_num_generator.ckpt(pio_ckpt_buff);
  write_eqn_rand_state(pio_ckpt_buff);

  // Lambda function when saving empty data
  auto save_empty = [&pio_ckpt_buff]()
  {
    size_t local_size = sizeof(size_t);
    pio_ckpt_buff.write(&local_size);
  };

  // Checkpoint turb_info if synthesizing turbulent velocity time histories
  if ((my_proc_id == g_turb_info.m_checkpointer) && (g_turb_info.m_vturb_synth.size() > 0))
    g_turb_info.ckpt_turb_synth_info_state(pio_ckpt_buff);
  else
    save_empty();

  if (my_proc_id == 0) {
  // Reference frame data should be identical for all SPs, so only SP 0 writes ckpt data
    sim.ckpt_grf_state(pio_ckpt_buff);
    sim.ckpt_lrf_state(pio_ckpt_buff);
    sim.ckpt_movb_state(pio_ckpt_buff);
#if BUILD_D19_LATTICE
  // thermal accel data should be identical for all SPs, so only SP 0 writes
  // ckpt data. This is a vector of size two. One entry will be written for
  // each call. Note that they will have to be restored separately as well.
    sim.ckpt_thermal_accel_state(pio_ckpt_buff);
#else
    save_empty(); // sim.ckpt_thermal_accel_state
#endif
  }
  else
  {
    save_empty(); // sim.ckpt_grf_state
    save_empty(); // sim.ckpt_lrf_state
    save_empty(); // sim.ckpt_movb_state
    save_empty(); // sim.ckpt_thermal_accel_state
  }

  ckpt_fan_descs(pio_ckpt_buff);

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  particle_sim.write_ckpt(pio_ckpt_buff); //checkpoint particle emitter related states
//#endif

  writer->create_sps_light_data_group();
  writer->write_data_combined(pio_ckpt_buff.m_buffer, size);
  pio_ckpt_buff.deallocate();
  // MPI_Barrier(eMPI_sp_comm);
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "write_light_data_grouped() in mode combined with buffsize %s at ckpt.cc in %f seconds\n", 
    hpc_io::utils::bytesToHuman(writer->buff_sz()).c_str(), timer.getTime());

}

/*
void wait_for_meas_window_clear_step_sends_finish()
{
  DO_MEAS_WINDOWS(meas_window) {
    if (meas_window->clear_step_request != MPI_REQUEST_NULL)
      MPI_Wait(&meas_window->clear_step_request, MPI_STATUS_IGNORE);
  }
}
*/

// called by Comm thread
static VOID write_full_ckpt_to_hdf5()
{
  
  char tmp_filename[FILENAME_MAXLEN];              /* Temporary ckpt file */
  MPI_Bcast(tmp_filename, FILENAME_MAXLEN, MPI_CHAR, eMPI_sp_cp_rank(), eMPI_sp_cp_comm);
  if(writer == nullptr)
  { 
    bool collectiveIO=true;
    const char* user_io_mode = getenv("INDEPENDENT_IO");
    if(user_io_mode != NULL) {
      collectiveIO = false;
      hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Setting IO mode to INDEPENDENT\n");
    }
    writer = std::make_unique<hpc_io::HDF5_writer>(eMPI_sp_comm, sim_run_info.parallel_io_max_buffer_size, collectiveIO);
  }  
  writer->initialize();
  writer->create_file(tmp_filename);

  STP_REALM realm = sim.is_conduction_sp ? STP_COND_REALM : STP_FLOW_REALM;

  sCKPT_BUFFER pio_ckpt_buff;
  writer->write_file_attrs(g_timescale.m_time);

  // Header stuff - written once
  //TODO Vinit: IS it needed ?
  //determine_if_equilibrium_ckpt();
  if (sim.is_particle_model) {
    // The parcels should be sent and received before full checkpoint can happen
    particle_sim.send_parcels_before_ckpt();
    particle_sim.complete_parcel_recvs();
  }

  write_light_data_grouped(pio_ckpt_buff);

  auto sim_has_elems = []( size_t num_elem)
  {
    timer.restart();
    MPI_Allreduce(MPI_IN_PLACE, &num_elem, 1, MPI_INT, MPI_SUM, eMPI_sp_comm);
    // hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "MPI_Allreduce timer stopped at %f seconds \n", timer.getTime());
    return (num_elem > 0);
  };

  if( sim_has_elems(g_n_meas_windows()) )
    write_meas_windows_ckpt(pio_ckpt_buff);

  if( sim_has_elems(g_ckpt_group.m_ckpt_ublks[realm].size()) )
    write_ublk_ckpt(pio_ckpt_buff);

  if( sim_has_elems(g_ckpt_group.m_ckpt_surfels[realm].size()) )
    write_surfel_ckpt(pio_ckpt_buff);

  pio_ckpt_buff.deallocate();
  writer->close();
  hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "SP writers closed\n");

  timer.restart();
}
#endif

VOID sim_checkpoint(BOOLEAN full, BOOLEAN is_avg_mme)
{
  if (full) {
#if !BUILD_GPU
    if (!sim_run_info.parallel_io)
    {
      g_ckpt_lgi_stream = sp_lgi_open_cp_output_stream();
      write_full_ckpt_to_lgi();
      lgi_close_stream(g_ckpt_lgi_stream);
    }
    else
    {
#ifndef _EXA_HPMPI
      unsigned long long avail_mem = 0;
      unsigned long long max_available_buffer_size_mb = hpc_io::utils::get_max_available_buffer_size(eMPI_sp_comm, sim_run_info.parallel_io_max_buffer_size, avail_mem) >> 20;
      if(max_available_buffer_size_mb != sim_run_info.parallel_io_max_buffer_size)
      {
        msg_warn("Memory available %s is not enough to use parallel_io_max_buffer_size=%u, simulation is using %llu MB per SP.\n", hpc_io::utils::bytesToHuman(avail_mem).c_str(), sim_run_info.parallel_io_max_buffer_size, max_available_buffer_size_mb);
        sim_run_info.parallel_io_max_buffer_size = max_available_buffer_size_mb;
      }
      hpc_io::utils::Timer global_timer;
      write_full_ckpt_to_hdf5();
      hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "SPs at barrier after write_full_ckpt_to_hdf5()\n");
      MPI_Barrier(eMPI_sp_cp_comm); //matches barrier in full_ckpt before openning cp hdf5 writer
      hpc_io::utils::master_print<hpc_io::utils::SP>(my_proc_id, "Ckpt timer stopped at %f seconds \n", global_timer.getTime());
#endif
    }
#endif
  } else {
        g_ckpt_lgi_stream = sp_lgi_open_cp_output_stream();
        cMME_CKPT ckpt;

        ckpt.write_mme_ckpt(is_avg_mme);

        if (is_avg_mme) {
          sim_fluid_dyn_dcache->c()->is_mme_accumulation_on = FALSE;
        }

        lgi_close_stream(g_ckpt_lgi_stream);
  }
}

VOID clear_avg_mme_checkpoint_data()
{
  DO_REALMS(realm) {
    auINT64 n_ublks = g_ckpt_group.m_ckpt_ublks[realm].size();
    ccDOTIMES(nu, n_ublks) {
      UBLK ublk = g_ckpt_group.m_ckpt_ublks[realm][nu];
      if (!ublk->is_solid()) { // mme data does not get allocated for non vr solid ublks 
        clear_mme_ckpt_data(ublk);
      }
    }
  }
}

VOID sCKPT_GROUP::add(sUBLK *ublk, REALM realm) {
  m_ckpt_ublks[realm].push_back(ublk);
}

VOID sCKPT_GROUP::add(sSURFEL *surfel, REALM realm) {
  m_ckpt_surfels[realm].push_back(surfel);
}

VOID sCKPT_GROUP::add(sSAMPLING_SURFEL *surfel, REALM realm) {
  m_ckpt_surfels[realm].push_back((sSURFEL *) ((uINT64) surfel | 1));
}
