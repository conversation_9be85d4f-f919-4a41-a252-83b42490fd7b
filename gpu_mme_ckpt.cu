#include PHYSICS_H

#include "gpu_globals.hcu"
#include "gpu_ptr.h"
#include "mme_ckpt.h"

namespace GPU {

__global__ void write_voxel_mme_ckpt_data_kernel(bool is_avg_mme, GPU::Ptr<GPU::UBLK> mblks, GPU::Ptr<cMME_CKPT> buf)
{
 cMME_CKPT& mme_ckpt = buf[blockIdx.x];
 sMBLK* ublk = mblks[blockIdx.x];

 write_flow_ublk_mme_ckpt(is_avg_mme, ublk, mme_ckpt);
}

struct sMME_CKPT {
  static void do_mme_mblk_chunk(bool is_avg_mme, 
                                GPU::Ptr<GPU::UBLK> d_ublk_ptrs, 
                                const GPU::UBLK* h_ublk_ptrs, 
                                GPU::Ptr<cMME_CKPT> d_buf,
                                cMME_CKPT * h_buf,
                                size_t n_mblks)
  {

    GPU::copy_h2d_async(d_ublk_ptrs, h_ublk_ptrs, n_mblks, GPU::g_stream);

    GPU::write_voxel_mme_ckpt_data_kernel<<<n_mblks, N_VOXELS_64, GPU::NO_DYN_SHMEM, GPU::g_stream>>>
      (is_avg_mme, d_ublk_ptrs, d_buf);

    checkCudaErrors( cudaPeekAtLastError() );

    GPU::copy_d2h_async(h_buf, d_buf.add_const(), n_mblks, GPU::g_stream);
    checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );

    int c = 0;
    ccDOTIMES(mblk_idx, n_mblks) {
      cMME_CKPT& h = h_buf[mblk_idx];

      write_ckpt_lgi(h.mask);

      if (h.mask.fluid_like_voxel_mask > 0) {
        write_ckpt_lgi(h.header);

        if (h.header.is_near_surface) {
          write_ckpt_lgi(h.surface);
        }

        if (h.num_ckpt_vars() > 0) {
          write_ckpt_lgi(h.meas, sizeof(cMME_CKPT::cDGF_MME_CKPT_MEAS_VAR)*h.num_ckpt_vars());
        }
      }
    }

  }
};
} // end namespace GPU

void cMME_CKPT::write_voxel_mme_ckpt_data(bool is_avg_mme)
{
  static constexpr size_t N_MBLKS = 1024;

  std::vector<cMME_CKPT> h_buf(N_MBLKS);

  auto d_ublk_ptrs = GPU::malloc<GPU::UBLK>(N_MBLKS, "mme ckpt");
  auto d_buf = GPU::malloc<cMME_CKPT>(N_MBLKS, "mme ckpt");

  const std::vector<GPU::UBLK>& h_ublk_ptrs = GPU::g_ublk_table.device_ublks();
  size_t n_mblks = h_ublk_ptrs.size();

  cDGF_MME_NUM_UBLKS lgi_num_ublks;
  lgi_num_ublks.num_ublks = n_mblks;

  write_ckpt_lgi(lgi_num_ublks);

  size_t idx = 0;
  while (idx < n_mblks) {
    size_t left = n_mblks - idx;
    size_t count = (left >= N_MBLKS) ? N_MBLKS : left;
    GPU::sMME_CKPT::do_mme_mblk_chunk(is_avg_mme, d_ublk_ptrs, &h_ublk_ptrs[idx], d_buf, h_buf.data(), count);
    idx += count;
  }

  GPU::free(d_buf, "mme ckpt");

}
