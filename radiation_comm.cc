
#include "radiation_comm.h"
#include "radiation_phy.h"
#include "strand_mgr.h"

#include PHYSICS_H

sRADIATION_PATCH_FSET g_radiation_patches_fset;
cRADIATION_WAIT_TIME g_radiation_wait_time;

void sRADIATION_PATCH_GROUP::add_patch(std::vector<sRAD_PATCH_SURFEL_PTR> surfels,
                                       bool emissivity_is_time_varying)
{
#if !BUILD_GPU
  m_patch_surfel_count.push_back( surfels.size() );

  m_patch_surfels.insert( m_patch_surfels.end(),
                          surfels.begin(),
                          surfels.end() );

  m_num_time_varying_emissivity_patches += emissivity_is_time_varying;
#endif
}

void sRADIATION_PATCH_GROUP::allocate_send_buffer()
{
#if !BUILD_GPU
  size_t n_patches = m_patch_surfel_count.size();
  size_t n_elements = n_patches + m_num_time_varying_emissivity_patches;
  m_send_msg.init(my_proc_id, m_dest_rp);
  m_send_msg.allocateBuffer(n_elements*sizeof(dFLOAT));
  m_send_msg.settag( make_mpi_shob_tag<eMPI_SHOB_TAG::RADIATION_PATCH_BB>(m_scale) );
  m_send_msg.set_nelems(n_elements*sizeof(dFLOAT));
#endif
}

void sRADIATION_PATCH_GROUP::allocate_recv_buffer()
{
#if !BUILD_GPU
  size_t n_patches = m_patch_surfel_count.size();
  m_recv_group.allocate_recv_buffer( n_patches ); // receive patch q - patch bb_power
#endif
}

void sRADIATION_PATCH_GROUP::send(ACTIVE_SOLVER_MASK)
{
#if !BUILD_GPU
  complete_mpi_request_while_processing_cp_messages(m_send_msg, MPI_SLEEP_LONG);

  g_exa_mpi_comm.isend(m_send_msg);
#endif
}

void sRADIATION_PATCH_GROUP::cancel_send()
{
#if !BUILD_GPU
  g_exa_mpi_comm.cancel(m_send_msg);
#endif
}

void sRP_RECV_GROUP::allocate_recv_buffer(size_t n_patches)
{
#if !BUILD_GPU
  m_recv_patches = n_patches;
  m_recv_msg.init(m_src_rp, my_proc_id);
  m_recv_msg.allocateBuffer(n_patches * sizeof(dFLOAT));
  m_recv_msg.settag( make_mpi_shob_tag<eMPI_SHOB_TAG::RADIATION_PATCH_Q>(m_scale) );
  m_recv_msg.set_nelems( n_patches * sizeof(dFLOAT) );
#endif
}

void sRP_RECV_GROUP::post_recv()
{
#if !BUILD_GPU
#endif
  g_exa_mpi_comm.irecv(m_recv_msg);
}

bool sRP_RECV_GROUP::is_recv_request_null()
{
#if !BUILD_GPU
  return m_recv_msg.m_request == MPI_REQUEST_NULL;
#else
  return true;
#endif
}

void sRP_RECV_GROUP::cancel_pending_recv() 
{ 
#if !BUILD_GPU
  if (!is_recv_request_null()) {
    g_exa_mpi_comm.cancel(m_recv_msg);
  }
#endif
}

bool sRP_RECV_GROUP::is_recv_ready() 
{ 
#if !BUILD_GPU
  g_exa_mpi_comm.test(m_recv_msg);
  return m_recv_msg.m_flag;
#else
  return true;
#endif
}

void cRP_RECV_CHANNEL::init(std::vector<sRP_RECV_GROUP*>&& recv_groups)
{
#if !BUILD_GPU
  pthread_mutex_init(&m_rp_mutex,NULL);
  pthread_cond_init(&m_rp_cond,NULL);

  m_recvs_left = recv_groups.size();
  m_recv_groups = std::move(recv_groups);
  m_state = eSTATE::POSTING;
  m_total_wait_time = Duration{0};
  m_wait_count = 0;
#endif
}

cRP_RECV_CHANNEL::Duration cRP_RECV_CHANNEL::wait_for_receives()
{
#if !BUILD_GPU
  cRP_RECV_CHANNEL::Duration duration;
  Time start = Clock::now();
  bool had_to_wait = false;
  pthread_mutex_lock(&m_rp_mutex);
  while(m_state != eSTATE::UNPACKING)
  {
    had_to_wait = true;
    pthread_cond_wait(&m_rp_cond, &m_rp_mutex);
  }
  pthread_mutex_unlock(&m_rp_mutex);
  duration = Clock::now() - start;
  m_total_wait_time += duration;
  m_wait_count++;

  if (!had_to_wait) {
    duration = Duration::zero();
  }

  return duration;
#else
  return Duration::zero();
#endif
}

void cRP_RECV_CHANNEL::set_post_receives()
{
#if !BUILD_GPU
  pthread_mutex_lock(&m_rp_mutex);
  m_state = eSTATE::POSTING;
  pthread_mutex_unlock(&m_rp_mutex);
#endif
}

// This is different because the main thread does 
// the actual unpacking. The new receive cannot be posted
// until it is done unpacking, so we use the m_post_receives variable
// to tell the comm thread that it can post the new receives
bool cRP_RECV_CHANNEL::process_posted_receives()
{
#if !BUILD_GPU
  bool some_receive_completed = false;
  pthread_mutex_lock(&m_rp_mutex);
  if (m_state == eSTATE::RECEIVING) {
    for (size_t i = m_recv_groups.size() - m_recvs_left; i < m_recv_groups.size(); i++) {
      if (m_recv_groups[i]->is_recv_ready()) { 
        m_recvs_left--;
        some_receive_completed = true;
      } else {
        break;
      }
    }

    if (m_recvs_left == 0) {
      m_state = eSTATE::UNPACKING;
      m_recvs_left = m_recv_groups.size();
      pthread_cond_signal(&m_rp_cond);
    }
  } else if (m_state == eSTATE::UNPACKING) {
    some_receive_completed = false;
  } else if (m_state == eSTATE::POSTING) {
    for (sRP_RECV_GROUP* recv_group: m_recv_groups) {
      recv_group->post_recv();
    }
    m_state = eSTATE::RECEIVING;
    some_receive_completed = true;
  } else {
    msg_internal_error("Invalid state!");
  }

  pthread_mutex_unlock(&m_rp_mutex);
  return some_receive_completed;
#else
  return true;
#endif
}

void cRP_RECV_CHANNEL::cancel_pending_recvs()
{
#if !BUILD_GPU
  for(auto* rp_group: m_recv_groups) {
    rp_group->cancel_pending_recv();
  }
#endif
}

void radiation_cancel_pending_sends()
{
#if !BUILD_GPU
  DO_SCALES_COARSE_TO_FINE(scale) {
    // fsets have null pointers, so we have to check this first before using it! Really annoying.
    if ( g_radiation_patches_fset.n_groups_of_scale(scale) > 0) {
      for ( sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
        group->cancel_send();
      }
    }
  }
#endif
}

void sRP_SEND_SOLVE_INFO::send(ACTIVE_SOLVER_MASK)
{
#if !BUILD_GPU
  int tag = make_mpi_misc_tag<eMPI_MISC_TAG::SP_RP_SOLVE_INFO>();
  LOG_MSG("RAD").printf("Sending start msg at ts %d", g_timescale.m_base_time);
  for (STP_PROC rp = total_sps; rp < total_sps + total_rps; rp++) {
    MPI_Send(&values[0], N_VALUES, MPI_INT64_T, rp, tag, MPI_COMM_WORLD);
  }

#endif
}

void build_rp_recv_channel()
{
#if !BUILD_GPU
  if (sim.is_radiation_model) {
    std::vector<sRP_RECV_GROUP*> rp_groups;
    rp_groups.reserve( g_radiation_patches_fset.n_groups() );

    DO_SCALES_FINE_TO_COARSE(scale) {
      for (auto* rp_group: g_radiation_patches_fset.groups_of_scale(scale)) {
        rp_groups.push_back(rp_group->recv_group());
      }
    }

    g_strand_mgr.m_rp_recv_channel.init(std::move(rp_groups));
  }
#endif
}

static void accumulate_radiation_patch_initial_emissive_power(sRADIATION_PATCH_GROUP* group, 
                                                              bool restart_for_ckpt_restore)
{
#if !BUILD_GPU
  auto& timescale = get_timescale_ref();
  auto surfel_it = group->surfels().begin();
  auto surfel_it_end = group->surfels().end();
  dFLOAT * patch_emissive_power = group->patch_bb_power();

  dFLOAT scale_factor = sim.stefan_boltzmann_const;

  asINT32 prior_rad_index = timescale.m_radiation_tm.prior_timestep_index();
  asINT32 next_rad_index = prior_rad_index ^ 1;

  dFLOAT area_scaling = sim_scale_to_voxel_size(group->scale());
  area_scaling *= area_scaling;

  // The RPs don't know what the emissivity is, so we have to send that now that
  // everything has been seeded. We will reuse the patch_bb_power buffer in the group.
  for (auto patch_surfel_count: group->patch_surfel_counts()) {
    cassert(surfel_it != surfel_it_end);
    *patch_emissive_power = 0.0;

    for (size_t i=0; i < patch_surfel_count; i++) {
      sRAD_PATCH_SURFEL_PTR surfel = *surfel_it;
      cassert(surfel.ptr()->is_radiation());

      auto* rad_data = surfel.ptr()->radiation_data();

      if (surfel.get<sRAD_PATCH_SURFEL_PTR_TRAITS::BACKSIDE>()) {
        rad_data++;
      }

      if (restart_for_ckpt_restore) {
        *patch_emissive_power += rad_data->emissivity_power[prior_rad_index] * surfel.ptr()->area * area_scaling;
      } else {
        *patch_emissive_power += rad_data->emissivity * radiation_t_fourth_power(rad_data->temp_prev) * scale_factor * surfel.ptr()->area * area_scaling;
      }
      ++surfel_it;
    }

    ++patch_emissive_power;

  }
#endif

}

void do_initial_radiation_sends()
{
#if !BUILD_GPU
  auto& timescale = get_timescale_ref();
  asINT32 prior_rad_index = timescale.m_radiation_tm.prior_timestep_index();
  asINT32 next_rad_index = prior_rad_index ^ 1;


  // first send the patch emissivities
  DO_SCALES_COARSE_TO_FINE(scale) {
    for (sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
      accumulate_radiation_patch_initial_emissive_power(group, sim.is_full_checkpoint_restore);
      group->send(0);
    }
  }

  DO_SCALES_COARSE_TO_FINE(scale) {

    for (sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
      // complete the previous send before messing with the send buffer again!
      group->complete_send();
      do_radiation_patch_group(group,
                               next_rad_index,
                               false,
                               true,
                               1,
                               1,
                               sim.is_full_checkpoint_restore);
      group->send(0);
    }
  }
#endif
}

void do_initial_radiation_recvs()
{
#if !BUILD_GPU
  auto& timescale = get_timescale_ref();
  asINT32 prior_rad_index = timescale.m_radiation_tm.prior_timestep_index();
  asINT32 next_rad_index = prior_rad_index ^ 1;

  DO_SCALES_COARSE_TO_FINE(scale) {
    for (sRADIATION_PATCH_GROUP* group: g_radiation_patches_fset.groups_of_scale(scale) ) {
      group->post_recv();
      group->complete_recv();
      // accumulate_radiation_patch_net_rad_flux(group,
                                              // prior_rad_index);
      do_radiation_patch_group(group,
                               prior_rad_index,
                               true,
                               false, 1, 1);
      TIMESTEP dt_scale = solver_scale_ts_size(get_solver_tm(group->realm()), group->scale());
      for (auto surfel: group->surfels()) {
        auto * rad_data = surfel.ptr()->radiation_data();
        if (surfel.get<sRAD_PATCH_SURFEL_PTR_TRAITS::BACKSIDE>()) {
          rad_data++;
        }
        rad_data->net_rad_flux_accum = rad_data->net_rad_flux_prev * dt_scale;
        rad_data->irradiation_accum = rad_data->irradiation_prev * dt_scale;
        if (timescale.m_radiation_tm.next_send() == 0) {
          rad_data->blackbody_power[next_rad_index] = radiation_t_fourth_power(rad_data->temp_prev);
          rad_data->emissivity_power[next_rad_index] = rad_data->emissivity * radiation_t_fourth_power(rad_data->temp_prev);
        } else {
          rad_data->blackbody_power[next_rad_index] = 0;
          rad_data->emissivity_power[next_rad_index] = 0;
        }
      }
    }
  }
#endif
}

void cRADIATION_WAIT_TIME::report_wait(TIMESTEP flow_timestep, TIMESTEP cond_timestep, dFLOAT wait_time)
{
  if (m_please_send_msg_to_cp.load(std::memory_order_acquire)) {
    // Previous message hasn't been sent yet, so skip this update
    return;
  }

  m_status.flow_timestep = flow_timestep;
  m_status.cond_timestep = cond_timestep;
  m_status.solution_time = wait_time;

  m_please_send_msg_to_cp.store(true, std::memory_order_release);
}

void cRADIATION_WAIT_TIME::maybe_send_msg_to_cp()
{
  int prev_msg_finished = false;
  if (m_req != MPI_REQUEST_NULL) {
    MPI_Test(&m_req, &prev_msg_finished, MPI_STATUS_IGNORE);
  }

  // This lets the compute thread know it can update a new wait time
  if (prev_msg_finished) {
    m_please_send_msg_to_cp.store(false, std::memory_order_release);
    return;
  }

  if (!m_please_send_msg_to_cp.load(std::memory_order_acquire)) {
    return;
  }

  MPI_Isend(&m_status,
            sizeof(m_status),
            MPI_BYTE,
            eMPI_sp_cp_rank(),
            eMPI_RP_STATUS_TAG,
            eMPI_sp_cp_comm,
            &m_req);
}

