/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef GPU_SIM_H
#define GPU_SIM_H

#if GPU_COMPILER
/* The sim struct for the GPU has been moved to a separate header
 * rather than be defined in gpu_globals.hcu so that it can be
 * included as a standalone piece and avoid circular header includes
 */
namespace GPU {
  
  extern __constant__ __device__ sSIM_INFO_CONSTANTS g_constant_sim_info;
  
  //Some parts of the global sim info CPU structure cannot be held
  //in constant memory. Therefore we hold a constant pointer to memory
  //allocated in global memory
  struct sSIM_INFO {
    
    CSYS csys_table; // vector of coord systems;
    sFLOAT **seed_from_meas_scale_factors;    
    CALIBRATION_PARAMS_INFO calibration_params;
    sTHERMAL_ACCEL_INFO   thermal_accel;
    sSEED_CONTROL *m_seed_control;
    PHYSICS_DESCRIPTOR    volume_physics_descs;
    PHYSICS_DESCRIPTOR    fluid_physics_descs;
    PHYSICS_DESCRIPTOR    flow_surface_physics_descs;
    PHYSICS_DESCRIPTOR    thermal_surface_physics_descs;
    sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs;
    sFREEZE_MOMENTUM_PARMS m_freeze_momentum_parms;
    sGRF                  grf;
    SHOB_ID                n_boundary_surfels; // inlet/outlet - used for initial print msg
    SHOB_ID                n_seeded_boundary_surfels;
    int n_seed_controllers;
    
    __device__ bool is_momentum_freeze_on() const {
      return m_freeze_momentum_parms.m_is_on;
    }
    __device__ const sSIM_INFO_CONSTANTS* c() const {
      return &GPU::g_constant_sim_info;
    }    

    __device__ LRF_PHYSICS_DESCRIPTOR ref_frame_index_to_lrf(asINT32 ref_frame_index) {
      return &lrf_physics_descs[ref_frame_index];
    }    
  };

  extern __constant__  __device__ sSIM_INFO* g_sim_info;  
}
#endif

static _INLINE_ __HOST__DEVICE__ auto& get_sim_ref() {
#if DEVICE_COMPILATION_MODE
  return *GPU::g_sim_info;
#else
  return ::sim;
#endif  
}

static  _INLINE_ __HOST__DEVICE__ const sSIM_INFO_CONSTANTS& get_simc_ref() {
#if DEVICE_COMPILATION_MODE
  return GPU::g_constant_sim_info;
#else
  return ::sim;
#endif  
}

//Alias
constexpr auto simc = get_simc_ref;

__HOST__DEVICE__ inline asINT32 sim_num_scales() {
#if DEVICE_COMPILATION_MODE
  return (GPU::g_constant_sim_info.num_scales);
#else
  return ::sim.num_scales;
#endif
}

#endif
