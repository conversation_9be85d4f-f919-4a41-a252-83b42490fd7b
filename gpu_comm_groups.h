#pragma once

#include "comm_groups.h"
#include "gpu_shobs.hcu"

template <class T>
class cGpuExaMsg : public cExaMsgBase
{
public:
  cGpuExaMsg() = default;
  cGpuExaMsg(size_t size);
  [[nodiscard]] GPU::Ptr<T> data() { return m_buffer; }
  void resize(size_t new_size);
  [[nodiscard]] size_t size() const { return m_size; }
  [[nodiscard]] size_t commsize() const { return m_size * sizeof(T); }
  void copyRanksAndTag(const cExaMsg<T>& cpu_msg);
protected:
  //TODO : device unique pointer
  GPU::Ptr<T> m_buffer;
  //number of elements
  size_t m_size = 0UL;
};
template <class T>
void cGpuExaMsg<T>::copyRanksAndTag(const cExaMsg<T>& cpu_msg) {
  m_source = cpu_msg.source();
  m_destination = cpu_msg.dest();
  m_tag = cpu_msg.tag();
}

template <class T>
cGpuExaMsg<T>::cGpuExaMsg(size_t size) :
  m_buffer(GPU::malloc<T>(size, "exa_msg")), m_size(size)
{}

template <class T>
void cGpuExaMsg<T>::resize(size_t new_size) {
  if(m_buffer)
    GPU::free(m_buffer, "exa_msg");
  m_buffer = GPU::malloc<T>(new_size, "exa_msg");
  m_size = new_size;
}

struct sMBLK_COMM_QUANTUM
{
public:
  sMBLK_COMM_QUANTUM(const sHMBLK& h_mblk, uint8_t offset, NEIGHBOR_MASK_INDEX nmi);
  const sHMBLK* cpu_mblk() const { return h_mblk;}
  __DEVICE__
  [[nodiscard]] const GPU::sUBLK& mublk() const { return *m_ptr; }
  __DEVICE__
  [[nodiscard]] GPU::sUBLK& mublk() { return *m_ptr; }
  __DEVICE__
  [[nodiscard]] uint8_t offset() const { return m_offset; }
  __DEVICE__
  [[nodiscard]] NEIGHBOR_MASK_INDEX nmi() const { return m_nmi; }
protected:
  //pointer to gpu mblk
  GPU::sUBLK * const m_ptr;
  //offset of ublk within mblk
  const uint8_t m_offset;
  //neighbor mask index (for states compression)
  const NEIGHBOR_MASK_INDEX m_nmi;
  //handy to print the mblk's with print_mega_ublk
  const sHMBLK* h_mblk;
};
struct sMSFL_COMM_QUANTUM
{
public:
  sMSFL_COMM_QUANTUM(const sMSFL& h_msfl, uint8_t offset);
  __DEVICE__
  [[nodiscard]] const GPU::sSURFEL& msfl() const { return *m_ptr; }
  __DEVICE__
  [[nodiscard]] GPU::sSURFEL& msfl() { return *m_ptr; }
  __DEVICE__
  [[nodiscard]] uint8_t offset() const { return m_offset; }
private:
  //pointer to gpu surfel
  GPU::sSURFEL * const m_ptr;
  //offset of surfel within mega surfel
  const uint8_t m_offset;
};


class cMBLK_RECV_GROUP : public tSP_RECV_GROUP<cGpuExaMsg<sdFLOAT>>
{
public:
  //Construct a GPU recv group from a CPU recv group
  cMBLK_RECV_GROUP(const sUBLK_RECV_GROUP& cpu_group, 
                   const std::vector<sHMBLK*>& megaublks,
                   const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map);
  ~cMBLK_RECV_GROUP();
  // virtual void unpack(ACTIVE_SOLVER_MASK active_solver_mask) = 0;
  // virtual void unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) = 0;
  size_t n_quantums() const { return m_n_quantums; }
  const sHMBLK* find_mublk_in_quantum(STP_SHOB_ID id);
protected:
  // Keeping a copy of the quantums on the CPU is handy for calculating offsets
  std::vector<sMBLK_COMM_QUANTUM> m_quantums_cpu;
  //TODO: device vector of mblk comm quantum
  GPU::Ptr<sMBLK_COMM_QUANTUM> m_quantums;
  size_t m_n_quantums;
  //Offsets for reading / writing in the comm buffer
  GPU::Ptr<size_t> m_offsets;
};

class cFAR_MBLK_RECV_GROUP : public cMBLK_RECV_GROUP
{
public:
  //Construct a GPU recv group from a CPU recv group
  cFAR_MBLK_RECV_GROUP(const sFARBLK_RECV_GROUP& cpu_group, 
                       const std::vector<sHMBLK*>& megaublks,
                       const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       bool communicate_turb_data);
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
  void post_recv() override;
private:
  const bool m_comm_turb_data;
  //Compute offsets for reading from the buffer, based on the amount of data per mblk
  //returns the size of the buffer (in numbers of float)
  size_t computeOffsets();
};

template <typename Key, typename Compare = std::less<Key>>
struct VectorOfSet
{
  std::vector<std::set<Key, Compare>> m_sets;
  auto& get_groups_of_scale(STP_SCALE scale) {
    return m_sets.at(scale);
  }
};

class cNEAR_MBLK_RECV_GROUP : public cMBLK_RECV_GROUP
{
public:
  //Construct a GPU recv group from a CPU recv group
  cNEAR_MBLK_RECV_GROUP(const sNEARBLK_RECV_GROUP& cpu_group, 
                       const std::vector<sHMBLK*>& megaublks,
                       const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       bool communicate_turb_data);
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
  void post_recv() override;
private:
  const bool m_comm_turb_data;
  //Compute offsets for reading from the buffer, based on the amount of data per mblk
  //returns the size of the buffer (in numbers of float)
  size_t computeOffsets();
};

struct sMBLK_RECV_GROUP_ORDER
{
  bool operator()(const std::unique_ptr<cMBLK_RECV_GROUP>& a,
                  const std::unique_ptr<cMBLK_RECV_GROUP>& b) const
  {
    return (a->m_source_sp < b->m_source_sp);
  }
  //TODO: how to avoid duplication with above?
  bool operator()(const std::unique_ptr<cFAR_MBLK_RECV_GROUP>& a,
                  const std::unique_ptr<cFAR_MBLK_RECV_GROUP>& b) const
  {
    return (a->m_source_sp < b->m_source_sp);
  }
  //TODO: how to avoid duplication with above?
  bool operator()(const std::unique_ptr<cNEAR_MBLK_RECV_GROUP>& a,
                  const std::unique_ptr<cNEAR_MBLK_RECV_GROUP>& b) const
  {
    return (a->m_source_sp < b->m_source_sp);
  }
};

typedef VectorOfSet<std::unique_ptr<cFAR_MBLK_RECV_GROUP>, sMBLK_RECV_GROUP_ORDER> cFAR_MBLK_RECV_FSET;
typedef VectorOfSet<std::unique_ptr<cNEAR_MBLK_RECV_GROUP>, sMBLK_RECV_GROUP_ORDER> cNEAR_MBLK_RECV_FSET;

extern cFAR_MBLK_RECV_FSET g_far_mblk_recv_fset;
extern cNEAR_MBLK_RECV_FSET g_near_mblk_recv_fset;

class cMBLK_SEND_GROUP: public tSP_SEND_GROUP<cGpuExaMsg<sdFLOAT>>
{
public:
  //Construct a GPU send group from a CPU send group
  cMBLK_SEND_GROUP(const sUBLK_SEND_GROUP& cpu_group, 
                   const std::vector<sHMBLK*>& megaublks,
                   const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map);
  ~cMBLK_SEND_GROUP();
  cMBLK_SEND_GROUP( const cMBLK_SEND_GROUP& other) = delete;
  virtual void send(ACTIVE_SOLVER_MASK active_solver_mask) = 0;
  virtual void send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) = 0;
  [[nodiscard]] bool is_near_surface() const { return m_is_near_surface; }
  size_t n_quantums() const { return m_n_quantums; }
protected:
  // Keeping a copy of the quantums on the CPU is handy for calculating offsets
  std::vector<sMBLK_COMM_QUANTUM> m_quantums_cpu;
  //TODO: device vector of mblk comm quantum
  GPU::Ptr<sMBLK_COMM_QUANTUM> m_quantums;
  size_t m_n_quantums;
  //Offsets for reading / writing in the comm buffer
  GPU::Ptr<size_t> m_offsets;

  bool       m_is_near_surface;
};

struct sMBLK_SEND_GROUP_ORDER
{
  bool operator()(const std::unique_ptr<cMBLK_SEND_GROUP>& a,
                  const std::unique_ptr<cMBLK_SEND_GROUP>& b) const
  {
    cNEIGHBOR_SP dest_sp1 = a->m_dest_sp;
    cNEIGHBOR_SP dest_sp2 = b->m_dest_sp;

    if (dest_sp1 != dest_sp2) {
      return (dest_sp1 < dest_sp2);
    }

    return (a->is_near_surface() < b->is_near_surface());
  }
};

typedef VectorOfSet<std::unique_ptr<cMBLK_SEND_GROUP>, sMBLK_SEND_GROUP_ORDER> cMBLK_SEND_FSET;
extern cMBLK_SEND_FSET g_mblk_send_fset;

//TODO: send and receive are reciprocal => Merge into a single class with customization points 
class cFAR_MBLK_SEND_GROUP :  public cMBLK_SEND_GROUP
{
public:
  //Construct a GPU send group from a CPU send group
  cFAR_MBLK_SEND_GROUP(const sFARBLK_SEND_GROUP& cpu_group, 
                       const std::vector<sHMBLK*>& megaublks,
                       const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       bool communicate_turb_data);
  void send(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
private:
  const bool m_comm_turb_data;
  //Compute offsets for writing into the buffer, based on the amount of data per mblk
  //returns the size of the buffer (in numbers of float)
  size_t computeOffsets();
};

class cNEAR_MBLK_SEND_GROUP :  public cMBLK_SEND_GROUP
{
public:
  //Construct a GPU send group from a CPU send group
  cNEAR_MBLK_SEND_GROUP(const sNEARBLK_SEND_GROUP& cpu_group, 
                       const std::vector<sHMBLK*>& megaublks,
                       const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       bool communicate_turb_data);
  void send(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void send(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
private:
  const bool m_comm_turb_data;
  //Compute offsets for writing into the buffer, based on the amount of data per mblk
  //returns the size of the buffer (in numbers of float)
  size_t computeOffsets();
};

class cMSFL_SEND_GROUP : public tSP_SEND_GROUP<cGpuExaMsg<sdFLOAT>>
{
public:
  //Construct a GPU send group from a CPU send group
  cMSFL_SEND_GROUP(const sSURFEL_SEND_GROUP& cpu_group,
                   const std::vector<sMSFL*>& h_mega_surfels,
                   const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                   bool communicate_turb_data);
  ~cMSFL_SEND_GROUP();
  void send(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void send_bc_type();
  void send_surfel_type();
  void send_init_info(ACTIVE_SOLVER_MASK active_solver_mask);
private:
  const bool m_comm_turb_data;
  // Keeping a copy of the quantums on the CPU is handy for calculating offsets
  std::vector<sMSFL_COMM_QUANTUM> m_quantums_cpu;
  //TODO: use device vector 
  GPU::Ptr<sMSFL_COMM_QUANTUM> m_quantums;
  size_t m_n_quantums;
  //Offsets for reading / writing in the comm buffer
  GPU::Ptr<size_t> m_offsets;
  GPU::Ptr<size_t> m_offsets_init_info;
};

struct sMSFL_SEND_GROUP_ORDER
{
  bool operator()(const std::unique_ptr<cMSFL_SEND_GROUP>& a,
                  const std::unique_ptr<cMSFL_SEND_GROUP>& b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};
typedef VectorOfSet<std::unique_ptr<cMSFL_SEND_GROUP>, sMSFL_SEND_GROUP_ORDER> cMSFL_SEND_FSET;
extern cMSFL_SEND_FSET g_msfl_send_fset;

class cMSFL_RECV_GROUP : public tSP_RECV_GROUP<cGpuExaMsg<sdFLOAT>>
{
public:
  //Construct a GPU recv group from a CPU recv group
  cMSFL_RECV_GROUP(const sSURFEL_RECV_GROUP& cpu_group,
                   const std::vector<sMSFL*>& h_mega_surfels,
                   const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                   bool communicate_turb_data);
  ~cMSFL_RECV_GROUP();
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  void unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) override;
  void post_recv() override;
  void post_bc_type_recv();
  void post_init_info_recv();
  void unpack_bc_types();
  void unpack_surfel_type();
  void unpack_init_info(ACTIVE_SOLVER_MASK active_solver_mask);
private:
  const bool m_comm_turb_data;
  // Keeping a copy of the quantums on the CPU is handy for calculating offsets
  std::vector<sMSFL_COMM_QUANTUM> m_quantums_cpu;
  //TODO: use device vector 
  GPU::Ptr<sMSFL_COMM_QUANTUM> m_quantums;
  size_t m_n_quantums;
  //Offsets for reading / writing in the comm buffer
  GPU::Ptr<size_t> m_offsets;
  GPU::Ptr<size_t> m_offsets_init_info;
};
struct sMSFL_RECV_GROUP_ORDER
{
  bool operator()(const std::unique_ptr<cMSFL_RECV_GROUP>& a,
                  const std::unique_ptr<cMSFL_RECV_GROUP>& b) const
  {
    return (a->m_source_sp < b->m_source_sp);
  }
};
typedef VectorOfSet<std::unique_ptr<cMSFL_RECV_GROUP>, sMSFL_RECV_GROUP_ORDER> cMSFL_RECV_FSET;
extern cMSFL_RECV_FSET g_msfl_recv_fset;

namespace GPU
{
void do_send_surfel_bc_types();
}

#if BUILD_GPU
INLINE auto& get_send_group_fset_by_build_type() {
  return g_msfl_send_fset;
}
#endif
