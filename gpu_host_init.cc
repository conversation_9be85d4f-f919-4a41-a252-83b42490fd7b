/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "box_advect.h"
#include "gpu_comm_groups.h"
#include "gpu_host_include.h"
#include "gpu_shobs.hcu"
#include "gpu_surfel_interactions.h"
#include "strand_mgr.h"

#include <unordered_set>
#include <array>
#include "mlrf.h"

#include PHYSICS_H

#ifdef BUILD_GPU

sVR_CONFIG* g_device_vr_config_array;
std::vector<std::array<sUBLK*,N_UBLKS_PER_MBLK>> g_mblk_children;

inline void init_mblk(tubV3<sdFLOAT_UBLK<N_UBLKS_PER_MBLK>>& ublk,
                      tubV3<sdFLOAT_UBLK<N_VOXELS_64>>& mega,
                      int child_ublk)
{
  VEC_COPY_UBLK_TO_MBLK(m_v3,3);
}

struct sMBLK_DYNAMICS_INIT_DATA 
{
  asINT32 m_n_phys_types;
  std::array<sUBLK_DYNAMICS_ATTR_DATA, N_UBLKS_PER_MBLK>* m_attribute_table;
  STP_PHYSTYPE_TYPE* m_phys_types;
  MBLK_VOXEL_MASK* m_phys_dyn_masks;

  sMBLK_DYNAMICS_INIT_DATA( asINT32 n_phys_types,
                            std::array<sUBLK_DYNAMICS_ATTR_DATA, N_UBLKS_PER_MBLK>* attribute_table,
                            STP_PHYSTYPE_TYPE* phys_types,
                            MBLK_VOXEL_MASK* phys_dyn_masks):
    m_n_phys_types(n_phys_types),
    m_attribute_table(attribute_table),
    m_phys_types(phys_types),
    m_phys_dyn_masks(phys_dyn_masks){
  }
};

static void set_mblk_meas_cell_ptrs(sHMBLK::sUBLK_DYNAMICS_DATA* dyn_data, const std::vector<sMBLK_MEAS_CELL_PTR>& mblk_meas_cell_ptrs)
{
  size_t n_mblk_meas_cell_ptrs = mblk_meas_cell_ptrs.size();
  if ( n_mblk_meas_cell_ptrs != 0 ) {
    sMBLK_MEAS_CELL_PTR *tmp = new sMBLK_MEAS_CELL_PTR[n_mblk_meas_cell_ptrs];
    ccDOTIMES(i, n_mblk_meas_cell_ptrs) {
      tmp[i] = mblk_meas_cell_ptrs[i];
    }
    dyn_data->set_mblk_meas_cell_ptrs(n_mblk_meas_cell_ptrs, tmp);
  }
  else {
    dyn_data->set_mblk_meas_cell_ptrs(0, nullptr);
  }
}


static void push_child_ublk_meas_cell_ptrs(asINT32 child_ublk,
                                           ::sUBLK& ublk,
                                           sUBLK_DYNAMICS_DATA* child_dyn_data,
                                           LRF_PHYSICS_DESCRIPTOR lrf,
                                           std::set<sINT32>& windows_touched,
                                           std::vector<sMBLK_MEAS_CELL_PTR>& mblk_meas_cell_ptrs,
                                           cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{
  uINT32 n_child_ublk_meas_cell_ptrs = child_dyn_data->n_meas_windows();
  sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr = child_dyn_data->meas_cell_ptrs();
  sUBLK_MEAS_CELL_PTR *end =  ublk_meas_cell_ptr + n_child_ublk_meas_cell_ptrs;
  for(int i = 0; i < n_child_ublk_meas_cell_ptrs; /* empty on purpose */ ) {
    windows_touched.insert(ublk_meas_cell_ptr->window_index());
    auto& window = windows[ublk_meas_cell_ptr->window_index()];
    int ublk_meas_cell_ptrs_processed = window->add_ublk_to_device_meas_cells(child_ublk, ublk.scale(), ublk_meas_cell_ptr, end, lrf_is_rotating(lrf));
    ublk_meas_cell_ptr += ublk_meas_cell_ptrs_processed;
    i += ublk_meas_cell_ptrs_processed;
    assert(i <= n_child_ublk_meas_cell_ptrs);
  }
}

static void build_mblk_meas_cell_ptrs(MBLK_VOXEL_MASK voxel_mask, 
                                      asINT32 mblk_fluid_type, 
                                      sHMBLK::sUBLK_DYNAMICS_DATA * mblk_dyn_data, 
                                      LRF_PHYSICS_DESCRIPTOR lrf, 
                                      cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows,
                                      const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack, 
                                      int n_ublks_to_pack)
{
  std::set<sINT32> windows_touched;
  std::vector<sMBLK_MEAS_CELL_PTR> mblk_meas_cell_ptrs;

  for (int child_ublk = 0; child_ublk < n_ublks_to_pack; child_ublk++) {
    UBLK ublk = ublks_to_pack[child_ublk];
    VOXEL_MASK_8 ublk_dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
    auto ublk_dynamics_data = ublk->dynamics_data();

    if (ublk->basic_fluid_voxel_mask.any()) {
      auINT32 ublk_fluid_type = ublk_dynamics_data->fluid_type();
      if (ublk_fluid_type == mblk_fluid_type) {
        push_child_ublk_meas_cell_ptrs(child_ublk, *ublk, ublk_dynamics_data, lrf, windows_touched, mblk_meas_cell_ptrs, windows);
      }
      ublk_dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (ublk_dynamics_data->next());
      ublk_dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;//Are there voxels that have special dyn blocks
    }

    //Special dynamics blocks
    while (ublk_dynamics_voxel_mask.any()) {
      auINT32 ublk_fluid_type = ublk_dynamics_data->fluid_type();
      VOXEL_MASK_8 special_fluid_voxel_mask = static_cast<SPECIAL_UBLK_DYNAMICS_DATA>(ublk_dynamics_data)->voxel_mask();
      if (ublk_fluid_type == mblk_fluid_type) {
        MBLK_VOXEL_MASK mblk_dyn_voxel_mask = static_cast<sHMBLK::sSPECIAL_UBLK_DYNAMICS_DATA*>(mblk_dyn_data)->voxel_mask();
        if ( child_ublk_mask(mblk_dyn_voxel_mask, child_ublk) == special_fluid_voxel_mask ) {
          push_child_ublk_meas_cell_ptrs(child_ublk, *ublk, ublk_dynamics_data, lrf, windows_touched, mblk_meas_cell_ptrs, windows);
        }
      }
      ublk_dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (ublk_dynamics_data->next());
    }
  }

  for(auto window_id : windows_touched) {
    windows[window_id]->append_mblk_meas_cells(mblk_meas_cell_ptrs); 
  }

  std::sort(mblk_meas_cell_ptrs.begin(), mblk_meas_cell_ptrs.end());

  if (mblk_meas_cell_ptrs.size() > 0) {
    set_mblk_meas_cell_ptrs(mblk_dyn_data, mblk_meas_cell_ptrs);
  }
}

static VOID fill_dynamics_data(const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack,
                               int n_ublks_to_pack,
                               sHMBLK& mega,
                               const sMBLK_DYNAMICS_INIT_DATA& mega_dyn_init_data,
                               LRF_PHYSICS_DESCRIPTOR lrf,
                               cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{

  auto dyn_data = mega.dynamics_data();
  asINT32 n_phys_types = mega_dyn_init_data.m_n_phys_types;
  std::array<sUBLK_DYNAMICS_ATTR_DATA, N_UBLKS_PER_MBLK>* attribute_table = mega_dyn_init_data.m_attribute_table;
  STP_PHYSTYPE_TYPE* phys_types = mega_dyn_init_data.m_phys_types;
  MBLK_VOXEL_MASK* phys_dyn_masks = mega_dyn_init_data.m_phys_dyn_masks;  
  
  ccDOTIMES(n, n_phys_types) {
    STP_PHYSTYPE_TYPE phys_type = phys_types[n];
    asINT32 fluid_type = dynamics_data_type_from_cdi_type(phys_type);
    auto& ublk_attributes = attribute_table[n];
    MBLK_VOXEL_MASK voxel_mask = phys_dyn_masks[n];

    switch (phys_type) {
    case STP_VVFLUID_TYPE:
    case STP_NEAR_SURFACE_VVFLUID_TYPE:
      {
        dyn_data->init(voxel_mask, fluid_type, &mega, ublk_attributes);
        break;
      }
    case STP_POROUS_VVFLUID_TYPE:
      {
        auto porous_dyn_data = (sHMBLK::sPOROUS_UBLK_DYNAMICS_DATA*) (dyn_data);
        porous_dyn_data->init(voxel_mask, fluid_type, &mega, ublk_attributes,
                              ublks_to_pack, n_ublks_to_pack);
        break;
      }
    case STP_CURVED_POROUS_VVFLUID_TYPE:
    case STP_CURVED_HX_POROUS_VVFLUID_TYPE:
    case STP_FAN_VVFLUID_TYPE:
    case STP_TABLE_FAN_VVFLUID_TYPE:
    default:
      {
        msg_internal_error("Dynamics data cdi type not yet supported on GPU: %d", phys_type);
      }
    }

    build_mblk_meas_cell_ptrs(voxel_mask, fluid_type, dyn_data, lrf, windows, ublks_to_pack, n_ublks_to_pack);

    dyn_data = (typename sHMBLK::sUBLK_DYNAMICS_DATA*) dyn_data->next();
  } 
}

static
void init_mblk(const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack, int n_ublks_to_pack,
               sHMBLK::sUBLK_ATTRS mega_attrs, sHMBLK& mega, int id, int scale,
               cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows,
               const sMBLK_DYNAMICS_INIT_DATA& mega_dyn_init_data)
{
  mega.m_ublk_attributes = mega_attrs;
  mega.setid(id);
  mega.setscale(scale);
  mega.set_ref_frame_index(ublks_to_pack[0]->ref_frame_index());

  LRF_PHYSICS_DESCRIPTOR lrf = mega.ref_frame_index() >= 0 ? &sim.lrf_physics_descs[mega.ref_frame_index()] : nullptr;

  if (mega.id() == GPU::print_mblk_children_id) {
    msg_print("mblk %d has %d child ublks", mega.id(), n_ublks_to_pack);
    ccDOTIMES(child_ublk, n_ublks_to_pack) {
      msg_print("\t%d: %d", child_ublk, ublks_to_pack[child_ublk]->id());
    }
  }
  
  ccDOTIMES(child_ublk, n_ublks_to_pack) {
    sUBLK& ublk = *ublks_to_pack[child_ublk];
    assert(ublk.ref_frame_index() == mega.ref_frame_index());    
    // std::string name = std::string("CHILD UBLK ") + std::to_string(child_ublk);
    //tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,name.c_str(),-1,0,-1);
    //ublk.print_ublk_content(std::cout,opts);

    // m_split_tagged_instance
    mega.m_split_tagged_instance.set(child_ublk, ublk.m_split_tagged_instance.get());
    // m_ublk_box
    // m_box_indices

    if (ublk.id() == GPU::print_ublk_parent_id) {
      msg_print("ublk %d is in mblk %d child ublk %d", ublk.id(), mega.id(), child_ublk);
    }

    COPY_UBLK_TO_MBLK(fluid_like_voxel_mask);
    COPY_UBLK_TO_MBLK(basic_fluid_voxel_mask);

    // m_ublk_attributes taken care of above
    // m_box_1D_index

    COPY_UBLK_TO_MBLK(m_fluid_connect_masks);
    COPY_UBLK_TO_MBLK(m_path_connect_masks);
    COPY_UBLK_TO_MBLK(lrf_surfels_interaction_mask);      
    COPY_UBLK_TO_MBLK(non_lrf_surfels_interaction_mask);  
    COPY_UBLK_TO_MBLK(lb_interaction_voxel_mask);         
    COPY_UBLK_TO_MBLK(pde_2_interaction_voxel_mask);     

    COPY_UBLK_TO_MBLK(m_are_any_neighbors_different_scale);
    COPY_UBLK_TO_MBLK(m_are_any_neighbors_split);

    COPY_UBLK_TO_MBLK(m_vr_scale_diff);

    for(int i=0; i<3; i++) {
      mega.m_location[i][child_ublk] = ublk.m_location[i];
    }    

    COPY_UBLK_TO_MBLK(m_dynamics_data_types);

    mega.m_vr_topology[child_ublk] = ublk.m_vr_topology;

    COPY_UBLK_TO_MBLK(voxel_smart_seed_mask);
    COPY_UBLK_TO_MBLK(all_neighbors_voxel_mask);

    // m_split_tagged_instance_in_lgi

    VEC_COPY_UBLK_TO_MBLK(neighbor_2_diff_scale_mask,6);

    COPY_UBLK_TO_MBLK(centroids);

    // m_ib_bf
    // m_next
    // m_prev
    // m_group
    
    /*if (ublk.is_solid() || ublk.is_ghost()) {
    // For solid vr_fine and vr_coarse ublks, we allocate space for different
    // solver blocks but they are essentially a bunch of zeros. Hence no point
    // going through all the copy methods below
    // We initialize VR data for solid mublks later
    continue;
    }*/

    if (ublk.is_solid()) {
      // For solid vr_fine and vr_coarse ublks, we allocate space for different
      // solver blocks but they are essentially a bunch of zeros. Hence no point
      // going through all the copy methods below
      // We initialize VR data for solid mblks later
      continue;
    }

    if (ublk.is_near_surface()) {
      COPY_UBLK_TO_MBLK(surf_lb_data());
      
      if (ublk.are_states_clear()) {
        mega.surf_geom_data()->s2v_clear_data.set_states_clear(child_ublk);
      }
      
      COPY_UBLK_TO_MBLK(surf_geom_data());
    }

    if (ublk.is_ghost()) {
      continue;
    }

    if (sim.do_smart_seed) {
      init_mblk(*ublk.smart_seed_data(),
                *mega.smart_seed_data(),
                child_ublk);
    }
    
  } //child_ublk loop

  // Split data
  if (mega.is_split() || mega.are_any_neighbors_split()) {
    init_mblk_split_data(ublks_to_pack, n_ublks_to_pack, mega);
  }

  // Dynamics data
  if (!mega.is_vr_fine() && !mega.is_solid() && !mega.is_ghost()) {
    fill_dynamics_data(ublks_to_pack, n_ublks_to_pack, mega, mega_dyn_init_data, lrf, windows);
  }

  // tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,"MEGAMEGAMEGA",-1,0,-1);
  // mega.print_ublk_content(std::cout,opts);
}

using CHILD_UBLK_ATTRIBUTES = std::array<sUBLK_DYNAMICS_ATTR_DATA, N_UBLKS_PER_MBLK>;
using MBLK_ATTRIBUTE_TABLE = std::vector<CHILD_UBLK_ATTRIBUTES>;

static VOID assemble_mblk_attribute_data(asINT32 fluid_type,
                                         asINT32 physics_type_index,
                                         asINT32 child_ublk,
                                         UBLK_DYNAMICS_DATA dyn_data,
                                         MBLK_ATTRIBUTE_TABLE& attribute_table) {
  
  sUBLK_DYNAMICS_ATTR_DATA& child_ublk_data = attribute_table[physics_type_index][child_ublk];
  child_ublk_data.fluid_phys_desc = dyn_data->physics_descriptor();
  child_ublk_data.body_force_desc = dyn_data->get_body_force_desc();
  child_ublk_data.is_basic_fluid = fluid_type == BASIC_FLUID_TYPE;
  
  if (fluid_type == POROUS_FLUID_TYPE ||
      fluid_type == CURVED_POROUS_FLUID_TYPE ||
      fluid_type == CURVED_HX_POROUS_FLUID_TYPE) {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR) child_ublk_data.fluid_phys_desc;
    POROUS_MEDIA_PARAMETERS parms = pd->parameters();
    asINT32 cdi_phys_type = pd->phys_type_desc->cdi_physics_type;
    child_ublk_data.is_adiabatic_apm = (cdi_phys_type == CDI_PHYS_TYPE_ACOUSTIC_POROUS_FLUID ||
                                        cdi_phys_type == CDI_PHYS_TYPE_ACOUSTIC_POROUS_LES_FLUID);
  }
}

/*==============================================================================
 * @fcn assemble_mblk_attribute_data
 * Walk through each child ublk, and peek at its dynamics data blocks to identify number
 * of physics types (sim_phys_types) that the mblk is associated with.
 *
 * All fluid voxels of a child UBLK are associated with the same_fluid physics
 * descriptor. Therefore it is possible to create ONE mblk fluid dynamics data block
 * that can capture the fluid dynamics attributes of its composing child ublks.
 *
 * For other types of physics, voxels of a child UBLK can be associated with multiple
 * physics descriptors, each with its own corresponding sUBLK_DYNAMICS_ATTR_DATA. 
 * Therefore, it might not be possible to have a single MBLK dynamics block that
 * represents the physics of a special fluid type (porous, fan etc)
 *============================================================================*/
static
VOID assemble_mblk_attribute_data(const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack,
                                  int n_ublks_to_pack,
                                  asINT32& n_phys_types,
                                  std::vector<STP_PHYSTYPE_TYPE>& sim_phys_types,
                                  std::vector<MBLK_VOXEL_MASK>& fluid_dyn_masks,
                                  MBLK_ATTRIBUTE_TABLE& attribute_table) {

  std::map<sPHYSICS_DESCRIPTOR*, std::tuple<size_t, STP_PHYSTYPE_TYPE>> unique_special_phys_types;
  fluid_dyn_masks.emplace_back(MBLK_VOXEL_MASK(0));
  attribute_table.emplace_back(CHILD_UBLK_ATTRIBUTES());
  constexpr asINT32 basic_fluid_physics_type_index = 0; //basic fluid physics is always first
  asINT32 mblk_has_basic_fluid = std::any_of(ublks_to_pack.begin(),
                                             ublks_to_pack.begin() + n_ublks_to_pack,
                                             [] (const sUBLK* ublk) { return ublk->basic_fluid_voxel_mask.any(); });
  for(int i=0; i<n_ublks_to_pack; i++) {
    sUBLK* ublk = ublks_to_pack[i];
    VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
    auto dynamics_data = ublk->dynamics_data();
    if (ublk->basic_fluid_voxel_mask.any()) {
      MBLK_VOXEL_MASK& mega_fluid_like_voxel_mask = fluid_dyn_masks[basic_fluid_physics_type_index];
      init_mblk(ublk->basic_fluid_voxel_mask, mega_fluid_like_voxel_mask, i);
      assemble_mblk_attribute_data(BASIC_FLUID_TYPE, basic_fluid_physics_type_index,
                                   i, dynamics_data, attribute_table);
      dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (dynamics_data->next());
      dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;//Are there voxels that have special dyn blocks
    }

    //Special dynamics blocks
    while (dynamics_voxel_mask.any()){
      
      auINT32 fluid_type = dynamics_data->fluid_type();
      auto pd = dynamics_data->physics_descriptor();
      asINT32 physics_type_index;
      auto pd_it = unique_special_phys_types.find(pd);
      if (pd_it != unique_special_phys_types.end()){
        physics_type_index = std::get<0>(pd_it->second);
      } else {
        physics_type_index = unique_special_phys_types.size() + mblk_has_basic_fluid /*basic fluid is 0*/;
        STP_PHYSTYPE_TYPE proxy_phys_type = proxy_cdi_type_from_dynamics_data_type(fluid_type);
        unique_special_phys_types[pd] = std::tuple<size_t, STP_PHYSTYPE_TYPE>(physics_type_index, proxy_phys_type);
        fluid_dyn_masks.emplace_back(MBLK_VOXEL_MASK(0));
        attribute_table.emplace_back(CHILD_UBLK_ATTRIBUTES());
      }

      MBLK_VOXEL_MASK& mega_fluid_like_voxel_mask = fluid_dyn_masks[physics_type_index];
      assemble_mblk_attribute_data(fluid_type, physics_type_index, i, dynamics_data, attribute_table);
      auto special_dyn_data = static_cast<SPECIAL_UBLK_DYNAMICS_DATA>(dynamics_data);
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      init_mblk(special_fluid_voxel_mask, mega_fluid_like_voxel_mask, i);
      //Are there voxels that have other special dyn blocks, not accounted in previous loops
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (dynamics_data->next());
    }
  }

  n_phys_types = unique_special_phys_types.size() + mblk_has_basic_fluid /*basic fluid*/;

  sim_phys_types.resize(n_phys_types);
  if (mblk_has_basic_fluid) { sim_phys_types[0] = STP_VVFLUID_TYPE; }

  for (auto pairs : unique_special_phys_types) {
    auto& v = pairs.second;
    sim_phys_types[std::get<0>(v)] = std::get<1>(v);
  }
}

static
sHMBLK* create_mblk(const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack, 
                    int n_ublks_to_pack, 
                    int id,
                    int scale,
                    bool is_ghost,
                    bool is_solid_mblk, 
                    bool is_spatially_coherent,
                    cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{
  sMBLK::sUBLK_ATTRS mblk_attrs;

  sUBLK_DYNAMICS_ATTR_DATA dynamics_attr_data[64];
  std::vector<sUBLK_MEAS_CELL_PTR> mblk_meas_cells;

  ccDOTIMES(i, n_ublks_to_pack) {
    sUBLK* ublk = ublks_to_pack[i];
    mblk_attrs.set_from_child_ublk_attributes(ublk->m_ublk_attributes, i);
    /*
      for (int u = 0; u < 8; u++)
      {
      if ((abs(ublk->centroids(u, 0) - 100) < 1e-5) && (abs(ublk->centroids(u, 1) - 13) < 1e-5) && (abs(ublk->centroids(u, 2) - 44) < 1e-5))
      {
      printf("MPI(%d) scale=%d mega_ublk_id %d ublk_id %d index_i %d voxel pos in ublk %d is ghost %d, is solid %d ***********************\n", 
      my_proc_id, ublk->scale(), id, ublk->id(), i, u, is_ghost, is_solid_mblk);
      mblk_has_blk = true;
      }
      }
    */
  }

  //If this a solid mblk, there's no point in it having two copies of states
  //If this MBLK is not spatially coherent, it has to perform PDE advection with
  //2 copies of states
  mblk_attrs.set_is_spatially_coherent(is_spatially_coherent);
  
  if (is_solid_mblk || (is_ghost&&!mblk_attrs.is_vr_fine())) {
    mblk_attrs.set_has_two_copies_of_states(0);
  } else if (!is_spatially_coherent) {
    mblk_attrs.set_has_two_copies_of_states(1);
  }

  size_t mblk_size;
  size_t mblk_dyn_size;

  //number of physics types equal to the count of dynamics blocks associated with the mblk
  asINT32 n_sim_phys_types = 0;

  //array of child ublk attributes per physics type
  MBLK_ATTRIBUTE_TABLE attribute_table;

  //stp physics type associated with each mblk dynamics block
  //Note that certain physics types such as STP_POROUS_VVFLUID_TYPE can occur
  //multiple times if multiple dynamics blocks are needed
  std::vector<STP_PHYSTYPE_TYPE> sim_phys_types;

  //special dyn voxel mask indicating which voxels of a MBLK are associated with
  //each physics type that is collected in assemble_mblk_attribute_data
  std::vector<MBLK_VOXEL_MASK> phys_dyn_masks; 

  if (!is_solid_mblk && !is_ghost && !mblk_attrs.is_vr_fine()) {
    //assemble_mblk_attribute_data can end up in an infine loop
    //for vr fine ublks that have solid voxels. Therefore we skip
    //this step for vr fine ublks, since in the end, the attribute
    //data is unused.
    assemble_mblk_attribute_data(ublks_to_pack, n_ublks_to_pack,
                                 n_sim_phys_types, sim_phys_types,
                                 phys_dyn_masks,
                                 attribute_table);
  }

  if ( is_solid_mblk && !(mblk_attrs.is_vr_fine() || mblk_attrs.is_vr_coarse()) ) {
    mblk_size = sizeof(sMBLK);
    mblk_dyn_size = 0;
  } else {    
    mblk_size = sMBLK::size(mblk_attrs, n_sim_phys_types, &sim_phys_types[0], &attribute_table[0]);
    mblk_dyn_size = sMBLK::size_of_dyn_data(mblk_attrs, n_sim_phys_types, &sim_phys_types[0], &attribute_table[0]);
  }

  size_t host_mblk_size = sHMBLK_HEADER::size();
  sHMBLK_HEADER* mblk;
  int ierr = posix_memalign((void**) &mblk, GPU::MINIMUM_CUDA_ALIGNMENT, host_mblk_size);
  memset(mblk, 0, host_mblk_size);
  mblk = new (mblk) sHMBLK_HEADER(mblk_attrs, mblk_size, mblk_dyn_size);

  init_mblk(ublks_to_pack, n_ublks_to_pack, mblk_attrs, *mblk, id, scale, windows,
            sMBLK_DYNAMICS_INIT_DATA(n_sim_phys_types, &attribute_table[0],
                                     &sim_phys_types[0], &phys_dyn_masks[0]));

  return mblk;

}

static sHMBLK* demote_to_two_copy_mblk(sHMBLK* one_copy_mblk)
{
  assert(!one_copy_mblk->has_two_copies());
  one_copy_mblk->m_ublk_attributes.set_has_two_copies_of_states(1);

  //The new mblk, has the same dynamics attributes and data, and only differs in having a single
  //copy of states where applicable (mostly LB_STATES and T_STATES. Therefore the part that changes
  //is accounted for by the offset table, and the dynamics data remains the same.
  size_t mblk_size = sMBLK::get_ublk_data_offset(one_copy_mblk->ublk_type(), UBLK_DYN_DATA_TYPE)
    + one_copy_mblk->size_of_dyn_data();

  as_header(one_copy_mblk)->update_size_of_device_mblk(mblk_size);

  return one_copy_mblk;
}

static void init_tagged_mblk(TAGGED_UBLK& tagged_ublk,
                             sHMBLK::sTAGGED_UBLK& mega_tagged_ublk,
                             std::vector<sHMBLK*>& mblks,
                             const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) {
  auto ublk_id = tagged_ublk.ublk()->id();
  auto [mblk_id,offset] = child_ublk_to_mblk_map.at(ublk_id);
  sHMBLK* mblk = mblks.at(mblk_id);
  mega_tagged_ublk.set_ublk(mblk);
  mega_tagged_ublk.set_offset_in_mega_block(offset);
  if (tagged_ublk.is_ublk_last()) {
    mega_tagged_ublk.set_ublk_as_last();
  }  
}

static void init_split_tagged_mblk(TAGGED_UBLK& tagged_ublk,
                                   sHMBLK::sTAGGED_UBLK& mega_tagged_ublk,
                                   std::vector<sHMBLK*>& mblks,
                                   const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) {

  cassert(tagged_ublk.is_ublk_split());
  sUBLK_VECTOR* split_vector = tagged_ublk.split_ublks();
  TAGGED_UBLK* tagged_ublks = split_vector->tagged_ublks();
  uINT8 num_split_ublks = split_vector->num_split_ublks();
  auto split_vector_64 = sSHOB_ALLOCATOR<sHMBLK::sUBLK_VECTOR>::malloc(sizeof(sHMBLK::sUBLK_VECTOR));
  split_vector_64->m_n_ublks = num_split_ublks;
  split_vector_64->m_tagged_ublks = cnew sHMBLK::sTAGGED_UBLK[num_split_ublks];
  mega_tagged_ublk.set_ublk_as_split(split_vector_64);
  for (int i = 0; i < num_split_ublks; i++) {
    TAGGED_UBLK split_tagged_ublk = tagged_ublks[i];
    sHMBLK::sTAGGED_UBLK& split_tagged_mblk = split_vector_64->m_tagged_ublks[i];
    init_tagged_mblk(split_tagged_ublk, split_tagged_mblk, mblks,
                     child_ublk_to_mblk_map);
  }
}

sHMBLK::UBLK_BOX find_matching_mega_box(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                                        asINT32 scale,
                                        uINT16 box_index,
                                        sINT8 lrf_index) {
  for (auto mega_box : mega_boxes) {
    if (mega_box->m_scale == scale &&
        mega_box->m_box_index == box_index &&
        mega_box->m_lrf_index == lrf_index) {
      return mega_box;
    }
  }
  return nullptr;
}

/*=================================================================================================
 * @fcn fill_overlap_regions
 * Box association only populates tagged ublks that are not part of the overlap region
 * This logic walks through all the tagged ublks in the UBLK box and makes sure
 * its corresponding counterpart in the mega ublk box is also populated
 *=================================================================================================*/
void fill_overlap_regions(std::vector<sHMBLK*>& mblks,
                          std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                          const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) {

  DO_UBLK_BOXES(ublk_box) {

    auto mblk_box = find_matching_mega_box(mega_boxes,
                                           ublk_box->m_scale,
                                           ublk_box->m_box_index,
                                           ublk_box->m_lrf_index);

    if (!mblk_box) {
      throw std::runtime_error("No matching mega ublk box found.");
    }
    
    size_t linear_index = 0;

    for (TAGGED_UBLK tagged_ublk : ublk_box->m_ublks) {
      sHMBLK::sTAGGED_UBLK& mega_tagged_ublk = mblk_box->m_ublks[linear_index];
      if (tagged_ublk.is_ublk() && (!mega_tagged_ublk.is_ublk())) {
        if (tagged_ublk.is_ublk_split()) {
          init_split_tagged_mblk(tagged_ublk, mega_tagged_ublk,
                                 mblks, child_ublk_to_mblk_map);
        }
        else if ( tagged_ublk.is_ublk_scale_interface() ) {
          const sSCALE_BOX_INTERFACE * scale_box_interface = tagged_ublk.interface();
          auto *mega_scale_box_interface =
            sSHOB_ALLOCATOR<sHMBLK::sSCALE_BOX_INTERFACE>::malloc(sizeof(sHMBLK::sSCALE_BOX_INTERFACE));
          memset(mega_scale_box_interface, 0, sizeof(sHMBLK::sSCALE_BOX_INTERFACE));

          ccDOTIMES(fine_ublk, N_VOXELS_8) {
            TAGGED_UBLK tagged_fine_ublk = scale_box_interface->m_fine_ublks[fine_ublk];
            if (!tagged_fine_ublk.is_null()) {
              sHMBLK::sTAGGED_UBLK& mega_fine_tagged_ublk = mega_scale_box_interface->m_fine_ublks[fine_ublk];
              if (tagged_fine_ublk.is_ublk_split()) {
                init_split_tagged_mblk(tagged_fine_ublk, mega_fine_tagged_ublk,
                                       mblks, child_ublk_to_mblk_map);
              } else {
                init_tagged_mblk(tagged_fine_ublk, mega_fine_tagged_ublk,
                                 mblks, child_ublk_to_mblk_map);
              }
            } else {
              mega_scale_box_interface->m_fine_ublks[fine_ublk] = nullptr;
            }
          }
          mega_tagged_ublk.set_scale_interface(mega_scale_box_interface);
        }
        else {
          init_tagged_mblk(tagged_ublk, mega_tagged_ublk,
                           mblks, child_ublk_to_mblk_map);
        }
      }
      linear_index += 1;
    }
  }
}

static void init_split_vector_64(UBLK child_ublk,
                                 HMBLK mblk,
                                 sUBLK_VECTOR* split_vector,
                                 sHMBLK::sUBLK_VECTOR* split_vector_64,
                                 int child_ublk_offset) {

  TAGGED_UBLK* tagged_ublks = split_vector->tagged_ublks();  
  int match_found = false;
  for (int i = 0; i < split_vector->num_split_ublks(); i++) {
    if (tagged_ublks[i].ublk() == child_ublk) {
      match_found = true;
      split_vector_64->m_tagged_ublks[i].set_ublk(mblk);
      split_vector_64->m_tagged_ublks[i].set_offset_in_mega_block(child_ublk_offset);
      break;
    }
  }

  //Some times you can have a solid split ublk that the simulator optimizes out,
  //avoid this missing check for these
  if (!child_ublk->is_solid() && !match_found) {
    msg_internal_error("No matching split child UBLK found for MBLK %d, child UBLK %d, offset %d",
                       mblk->id(), child_ublk->id(), child_ublk_offset);
  }  
}

static void init_tagged_mblk(UBLK child_ublk,
                             HMBLK mblk,
                             TAGGED_UBLK tagged_ublk,
                             sHMBLK::sTAGGED_UBLK& tagged_mblk,
                             int child_ublk_offset) {
  // If tagged_mblk is empty, it implies that this is the first (mega ublk, child ublk)
  // pair that is forcing its initialization.
  if (!tagged_mblk.is_ublk()) {
    if (tagged_ublk.is_ublk_split()) {      
      sUBLK_VECTOR* split_vector = tagged_ublk.split_ublks();
      uINT8 num_split_ublks = split_vector->num_split_ublks();
      auto split_vector_64 = sSHOB_ALLOCATOR<sHMBLK::sUBLK_VECTOR>::malloc(sizeof(sHMBLK::sUBLK_VECTOR));
      split_vector_64->m_n_ublks = num_split_ublks;
      split_vector_64->m_tagged_ublks = cnew sHMBLK::sTAGGED_UBLK[num_split_ublks];
      tagged_mblk.set_ublk_as_split(split_vector_64);
      init_split_vector_64(child_ublk, mblk, split_vector, split_vector_64, child_ublk_offset);
    }
    // The simulator optimizes away a pair of split ublks  if one is pure solid and the other containing all the fluid voxels.
    // In that case, the tagged_ublk in the UBLK box is set to the fluid split instance
    else if (tagged_ublk.is_simple_fluid_ublk() && !child_ublk->is_solid()) {
      tagged_mblk.set_ublk(mblk);
      tagged_mblk.set_offset_in_mega_block(child_ublk_offset);

      //Make sure everything looks good
      cassert(tagged_mblk.is_ublk());
      cassert(tagged_mblk.ublk() == mblk);	
      cassert(tagged_mblk.get_offset_in_mega_block() == child_ublk_offset);
    }
    else if (tagged_ublk.is_ublk_scale_interface()) {
      // Not sure if we can ever end up here, but adding an assert just in case
      assert(false);
    }
  }
  //If tagged mblk is not empty, then it implies that is has been initialized before,
  //for instance in a split situation
  else {
    if (tagged_ublk.is_ublk_split()) {
      sUBLK_VECTOR* split_vector = tagged_ublk.split_ublks();
      sHMBLK::sUBLK_VECTOR* split_vector_64 = tagged_mblk.split_ublks();
      assert(split_vector_64 && split_vector_64->tagged_ublks());
      init_split_vector_64(child_ublk, mblk, split_vector, split_vector_64, child_ublk_offset);
    } else if (child_ublk->is_solid()) {
      //Do nothing
      //We might end up here when a split UBLK has two instances, in which one is completely solid
      //In that scenario we mark these UBLK instances as NOT split, but they share the same location
      //in the box. The tagged ublk corresponds to the fluid ublk instance
      assert(tagged_ublk.is_simple_fluid_ublk());
    } else {
      msg_internal_error("TAGGED UBLK already set for MBLK %d, child UBLK %d, offset %d",
                         mblk->id(), child_ublk->id(), child_ublk_offset);
    }
  }
}

/*=================================================================================================
 * @fcn associate_with_mblk_box
 * MBLK boxes have the same box dimenstions as UBLK boxes. The only difference is that TAGGED_UBLKs now
 * contain a pointer to a 256 byte aligned MBLK, and an offset to the child UBLK stored in bits 6 - 8.
 * This structure helps us maintain spatial coherence via the box, but gives us the freedom to have MBLKs 
 * formed from non-spatially coherent UBLKs of the same type
 *=================================================================================================*/

static void associate_with_mblk_box(const std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks, HMBLK mblk,
                                    std::vector<sHMBLK::UBLK_BOX> &mega_boxes) 
{

  const int INVALID = (1 << 15);

  ccDOTIMES(j, N_UBLKS_PER_MBLK) {

    if (ublks[j]) {

      uINT16 box_index = ublks[j]->get_ublk_box()->m_box_index;
      int scale = ublks[j]->scale();
      sINT8 lrf_index = ublks[j]->ref_frame_index();
      
      sHMBLK::UBLK_BOX mega_box = find_matching_mega_box(mega_boxes, scale, box_index, lrf_index);

      if (!mega_box) {
        msg_internal_error("No matching mega box found for mblk %d, child ublk %d, ublk id %d,"
			   "scale %d, box_index %u", mblk->id(), j, ublks[j]->id(), scale, box_index);
      }
	
      auto& child_ublk_access = mblk->m_box_access.m_ublk_box_access[j];
      child_ublk_access.m_ublk_box = mega_box;
      child_ublk_access.m_box_indices[0] = ublks[j]->m_box_access.m_box_indices[0];
      child_ublk_access.m_box_indices[1] = ublks[j]->m_box_access.m_box_indices[1];
      child_ublk_access.m_box_indices[2] = ublks[j]->m_box_access.m_box_indices[2];

      uINT32 index = mega_box->m_size[2] *
	(mega_box->m_size[1] * child_ublk_access.m_box_indices[0] + child_ublk_access.m_box_indices[1]) +
        child_ublk_access.m_box_indices[2];


      child_ublk_access.m_box_1D_index = index;
      
      cassert(index < (1 << 31));

      sHMBLK::sTAGGED_UBLK& tagged_mblk = mega_box->m_ublks[index];
      TAGGED_UBLK tagged_ublk = ublks[j]->m_box_access.stagged_ublk();
      init_tagged_mblk(ublks[j], mblk, tagged_ublk, tagged_mblk, j);
    }
  }   
}

/*=================================================================================================
 * @fcn init_mblk_boxes
 * MEGA UBLK boxes have same resolution as UBLK boxes
 *=================================================================================================*/
std::vector<sHMBLK::UBLK_BOX> init_mblk_boxes() 
{

  std::vector<sHMBLK::UBLK_BOX> mega_boxes;
  DO_UBLK_BOXES(ublk_box) {
    auto mega_box = new sHMBLK::sUBLK_BOX(ublk_box);
    mega_boxes.push_back(mega_box);
  }
  return mega_boxes;
}

/*=================================================================================================
 * @class UBLK_SEQUENCE_SIGNATURE
 * Helps group UBLKs of the same signature into a sequence. The signature is based
 * on two UBLK properties:
 * - ublk_type
 * - ref_frame_index
 *================================================================================================*/
struct UBLK_SEQUENCE_SIGNATURE {

  UBLK_SEQUENCE_SIGNATURE(asINT32 ref_frame_index,
                          size_t ublk_type):
    m_ref_frame_index(ref_frame_index),
    m_ublk_type(ublk_type)
  {
    cassert(ref_frame_index < ::sim.n_lrf_physics_descs);
    cassert(ublk_type < n_ublk_types());
  }

  UBLK_SEQUENCE_SIGNATURE(const sUBLK* ublk):
    m_ref_frame_index(ublk->ref_frame_index()),
    m_ublk_type(ublk->ublk_type())
  {
  }

  //compare two signatures
  bool operator==(const UBLK_SEQUENCE_SIGNATURE& other) {
    return \
      m_ref_frame_index == other.m_ref_frame_index &&
      m_ublk_type == other.m_ublk_type;
  }

  bool operator!=(const UBLK_SEQUENCE_SIGNATURE& other) {
    return !(*this == other);
  }

  //is signature valid?
  operator bool() const {
    return (m_ublk_type < n_ublk_types()) &&
      (m_ref_frame_index < ::sim.n_lrf_physics_descs);
  }

  asINT32 ref_frame_index() const {
    //bump up by one to account for ref_frame_index starting at -1
    return m_ref_frame_index - DEFAULT_REF_FRAME_INDEX;
  }

  size_t ublk_type() const {
    return m_ublk_type;
  }

  //Increment signature by incrementing fastest varying dimension first, i.e,
  //ublk_type, and then ref_frame_index
  void operator++(int) {
    m_ublk_type++;
    if (m_ublk_type == n_ublk_types()) {
      if (m_ref_frame_index < ::sim.n_lrf_physics_descs - 1) {
        m_ref_frame_index++;
        m_ublk_type = 0;
      }
    }
  }

  constexpr static asINT32 n_ublk_types() {
    return N_UBLK_DATA_TYPES;
  }

private:
  asINT32 m_ref_frame_index;
  size_t m_ublk_type;
};

inline bool operator < (const UBLK_SEQUENCE_SIGNATURE& s1, const UBLK_SEQUENCE_SIGNATURE& s2) {
  if (s1.ublk_type() < s2.ublk_type()) { return false; }
  if (s1.ublk_type() > s2.ublk_type()) { return true; }
  if (s1.ref_frame_index() < s2.ref_frame_index()) { return false; }
  if (s1.ref_frame_index() > s2.ref_frame_index()) { return true; }
  return false;
}

/*=================================================================================================
 * @class cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE
 *================================================================================================*/
class cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE {
  
public:
  cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE():
    m_active_signature(DEFAULT_REF_FRAME_INDEX, 0 /*start ublk type*/),
    m_cur_ublk_idx(0),
    m_num_ublks_packed(0),
    m_num_ublks(0)
  {
    //Add one to include standard default reference frame of -1
    m_ublk_vec_by_signature.resize(::sim.n_lrf_physics_descs + 1);
    for (auto& v : m_ublk_vec_by_signature) {
      v.resize(UBLK_SEQUENCE_SIGNATURE::n_ublk_types());
    }
  };

  auto& ublk_vector_by_signature(const UBLK_SEQUENCE_SIGNATURE& s) {
    if (s.ublk_type() == 33) {
      breakpoint();
    }
    return m_ublk_vec_by_signature.at(s.ref_frame_index()).at(s.ublk_type());
  }
  
  void add_ublk(sUBLK* ublk) {
    auto& vec = ublk_vector_by_signature(ublk);
    vec.push_back(ublk);
    m_num_ublks++;
  }

  size_t fill_sequence(std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack) 
  {
    size_t n_ublks_packed = 0;

    // find the next non-empty non_coherent vector or move to the next vector
    // if we're at the end of one. Both conditions can be tested by matching
    // m_cur_ublk_idx with the size of the vector
    while (m_active_signature &&
           m_cur_ublk_idx == ublk_vector_by_signature(m_active_signature).size()) {
      m_active_signature++;
      m_cur_ublk_idx = 0;
    }

    if (m_active_signature) {
      auto& non_coherent_ublks = ublk_vector_by_signature(m_active_signature);
      cassert(m_cur_ublk_idx < non_coherent_ublks.size());

      for (int i = m_cur_ublk_idx, j = 0;
           j < N_UBLKS_PER_MBLK && i < non_coherent_ublks.size();
           i++, j++) {
        n_ublks_packed++;
        ublks_to_pack[j] = non_coherent_ublks[i];
      }

      m_num_ublks_packed += n_ublks_packed;
      m_cur_ublk_idx += n_ublks_packed;
    }
    
    return n_ublks_packed;
  }

  size_t num_ublks_packed() const { return m_num_ublks_packed; }

  bool no_more_ublks_to_pack() const
  {
    return m_num_ublks_packed == m_num_ublks;
  }  
  
private:   
  using UBLK_VECTOR = std::vector<sUBLK*>;
  using UBLK_VECTOR_BY_SIGNATURE = std::vector<std::vector<UBLK_VECTOR>>;
  // Store sequences of UBLKs in vectors. For now we can get away with having
  // several layers of vectors since the signature has only two degrees of freedom
  // (ref_frame_index, ublk_type). If these increase in the future, the underlying
  // container can potentially be replaced by a std map to make things more manageable
  UBLK_VECTOR_BY_SIGNATURE m_ublk_vec_by_signature;
  UBLK_SEQUENCE_SIGNATURE m_active_signature;

  //keep track of where we are in a vector given a type
  size_t m_cur_ublk_idx;
  size_t m_num_ublks_packed;
  size_t m_num_ublks;
  
};

/*=================================================================================================
 * @class SPATIALLY_COHERENT_UBLK_SEQUENCE
 * This class helps figure out sequences of spatially coherent UBLKs in a UBLK_GROUP.
 * UBLKs that do not form a sequence are pushed into a non_coherent_ublk_sequence
 *================================================================================================*/
class cSPATIALLY_COHERENT_UBLK_SEQUENCE 
{
private:
  sUBLK* m_start;
  sUBLK* m_end;
  size_t m_num_coherent_ublks_packed;
  
public:

  cSPATIALLY_COHERENT_UBLK_SEQUENCE(sUBLK* start_ublk) :
    m_end(nullptr),
    m_start(start_ublk),
    m_num_coherent_ublks_packed(0)
  { }

  size_t num_ublks_packed() { return m_num_coherent_ublks_packed;  }

  VOID move_to_nearest_spatially_coherent_ublk_sequence(cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE& ns) 
  {
    if (m_start) {

      m_start = m_end? m_end->m_next : m_start;
      m_end   = m_start? get_end_of_sequence_ublk(m_start) : nullptr;

      while(m_start && m_end) {

        if (are_start_end_ublks_a_coherent_sequence(m_start, m_end)) {
          return; 
        }

        //If this start ublk didn't form a sequence add it to our vector of non-coherent ublks
        ns.add_ublk(m_start);

        m_start = m_start->m_next;
        m_end   = m_end->m_next;
      }

      //Move the remaining UBLKs to the non coherent UBLK vector
      while(m_start != m_end) {
        ns.add_ublk(m_start);
        m_start = m_start? m_start->m_next : nullptr;
      }
    }
  }

  bool no_more_ublks_to_pack()
  {
    return m_start == m_end;
  }

  size_t fill_sequence(std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack) {
    int j = 0;
    sUBLK* ublk = m_start;

    while (ublk && j < N_UBLKS_PER_MBLK) {
      ublks_to_pack[j] = ublk;
      ublk = ublk ? ublk->m_next : nullptr;
      j++;
    }

    if (m_end) {
      cassert(ublk == m_end->m_next);
      //The code below is needed so that the next call to empty() returns true
      //for no_more_coherent_ublks_to_pack()
      if (!m_end->m_next) { m_end = m_start = nullptr; }
    }

    m_num_coherent_ublks_packed += j;
    return j;
  }  
private:
  static sUBLK* get_end_of_sequence_ublk(sUBLK* start_ublk) {
    cassert(start_ublk);
    sUBLK* last_ublk_in_sequence = start_ublk;
    for (int i = 0;
         i < N_UBLKS_PER_MBLK - 1
           && last_ublk_in_sequence->m_next;
         i++) {
      last_ublk_in_sequence = last_ublk_in_sequence->m_next;
    }
    return last_ublk_in_sequence;
  }

  bool is_sequence_same_type(sUBLK* start_ublk, sUBLK* end_ublk) 
  {
    auto test_ublk_type = UBLK_SEQUENCE_SIGNATURE(start_ublk);
    sUBLK * ublk = start_ublk;

    while(true) {
      if ( UBLK_SEQUENCE_SIGNATURE(ublk) != test_ublk_type ) {
        return false;
      }
      if (ublk == end_ublk) {
        break;
      }
      else {
        ublk = ublk->m_next;
      }
    }
    return true;
  }

  bool are_start_end_ublks_a_coherent_sequence(sUBLK* start_ublk,
                                               sUBLK* end_ublk) {
    cassert(start_ublk->scale() == end_ublk->scale());
    STP_SCALE scale = start_ublk->scale();
    size_t ublk_size = scale_to_voxel_size(scale) * 2;    
    bool is_possibly_coherent =
      (end_ublk->location(0) == (start_ublk->location(0) + ublk_size)) &&
      (end_ublk->location(1) == (start_ublk->location(1) + ublk_size)) &&
      (end_ublk->location(2) == (start_ublk->location(2) + ublk_size));

    if (is_possibly_coherent) {
      return is_sequence_same_type(start_ublk, end_ublk);
    }
    else {
      return is_possibly_coherent;
    }
  }  
};

/*==============================================================================
 * @class cUBLK_SEQUENCE 
 * This class helps figure out sequences of spatially coherent UBLKs in a UBLK_GROUP.
 * UBLKs that do not form a sequence are pushed into a non_coherent_ublk_sequence
 *==============================================================================*/
class cUBLK_SEQUENCE 
{

  cSPATIALLY_COHERENT_UBLK_SEQUENCE m_coherent_seq;
  cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE m_non_coherent_seq;
  
public:

  cUBLK_SEQUENCE(sUBLK* start_ublk):
    m_coherent_seq(start_ublk)
  {
  }

  size_t num_ublks_packed() {
    return m_coherent_seq.num_ublks_packed() +
      m_non_coherent_seq.num_ublks_packed();
  }

  size_t set_next_sequence(std::array<sUBLK*,N_UBLKS_PER_MBLK>& ublks_to_pack, BOOLEAN& is_spatially_coherent) 
  {
    size_t n_ublks_packed;

    m_coherent_seq.move_to_nearest_spatially_coherent_ublk_sequence(m_non_coherent_seq);

    if (!m_coherent_seq.no_more_ublks_to_pack()) {
      is_spatially_coherent = TRUE;
      n_ublks_packed = m_coherent_seq.fill_sequence(ublks_to_pack);
    } else {
      is_spatially_coherent = FALSE;      
      n_ublks_packed = m_non_coherent_seq.fill_sequence(ublks_to_pack);
    }

    return n_ublks_packed;
  }

  bool empty() {
    return m_coherent_seq.no_more_ublks_to_pack() && \
      m_non_coherent_seq.no_more_ublks_to_pack();
  }
};

inline void add_entry_to_ublk_to_mblk_map(std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                          sUBLK* ublk,
                                          STP_SHOB_ID mblk_id,
                                          uINT8 child_ublk_index) {

  auto [it, success] = child_ublk_to_mblk_map.insert(std::make_pair(ublk->id(),
                                                                    std::make_pair(mblk_id, child_ublk_index)));

  if (!success) {
    if (it != child_ublk_to_mblk_map.end()) {
      msg_warn("UBLK %d has already been assigned to MBLK %d, child_index %d, attempt to overwrite this"
               " with  MBLK %d, child_index %d\n"
               "UBLK %d - (is_near %d, is_ghost %d, is_fringe %d, is_fringe2 %d, is_solid %d)",
               ublk->id(), it->second.first, it->second.second,
               mblk_id, child_ublk_index,
               ublk->id(), ublk->is_near_surface(),
               ublk->is_ghost(), ublk->is_fringe(), ublk->is_fringe2(),
               ublk->is_solid());
    } else {
      msg_internal_error("Failed to insert child_ublk_to_mblk_map entry for UBLK %d", ublk->id());
    }
  }
}

/*=================================================================================================
 * @fcn build_mblks_from_ublk_fset
 * For each group type fset, create analogous MBLK groups for dest_sp and scale
 *================================================================================================*/
void build_mblks_from_ublk_fset(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                                std::vector<sHMBLK*>& mblks,
                                UBLK_FSET ublk_group_fset,
                                MBLK_FSET  mblk_group_fset,
                                std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{

  for (int scale = 0; scale < sim.num_scales; scale++) {

    const auto& set_of_ublk_groups = ublk_group_fset->get_groups_of_scale(scale);

    int groupId = 0;

    for (UBLK_GROUP ublk_group : set_of_ublk_groups) {

      MBLK_GROUP mblk_group = mblk_group_fset->create_group(scale, ublk_group->m_dest_sp);
      const auto n_ublks = ublk_group->n_shob_ptrs();
      // printf("MPI %d, n_ublks = %d\n", my_proc_id, n_ublks);
      sUBLK*const h_ublk = ublk_group->shob_ptrs();
      auto sequence = cUBLK_SEQUENCE(h_ublk);

      while(!sequence.empty()) {

        std::array<sUBLK*,N_VOXELS_8> ublks_to_pack{};
        BOOLEAN is_spatially_coherent;
        const int n_ublks_packed = sequence.set_next_sequence(ublks_to_pack, is_spatially_coherent);
        const int mblk_id = mblks.size();
        sHMBLK* const mblk = create_mblk(ublks_to_pack, n_ublks_packed, mblk_id, scale,
                                         FALSE /*not ghost*/,
                                         FALSE /*not solid*/,
                                         is_spatially_coherent,
                                         windows);

        for(int i = 0; i < n_ublks_packed; i++) {
          auto ublk = ublks_to_pack[i];
          //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          //LOG_MSG("GPU_INIT").printf("MPI %d, ublk_id = %d, mega_ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, mega_ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          add_entry_to_ublk_to_mblk_map(child_ublk_to_mblk_map, ublk, mblk_id, i);
        }
	
        mblk_group->add_shob_to_group(mblk);

        mblks.push_back(mblk);
        associate_with_mblk_box(ublks_to_pack, mblks.back(), mega_boxes);
      }

      //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, n_ublks = %d num_ublks_packed %d dest sp %d scale %d group type %d", my_proc_id, n_ublks, sequence.num_ublks_packed(), ublk_group->m_dest_sp, scale, group_type);
      assert(n_ublks == sequence.num_ublks_packed());
      groupId ++;
    } //ublk_group
  }//scale
}

template<class T_SET, class T_RECV_GROUP>
void create_mblks_based_on_sequence(T_SET& ublk_group_fset, std::vector<sHMBLK::UBLK_BOX> &mega_boxes, std::vector<sHMBLK*>& mblks, cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows, std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map, bool is_ghost, int scale)
{
  int groupId = 0;

  const auto& set_of_ublk_groups = ublk_group_fset.groups_of_scale(scale);

  for(T_RECV_GROUP *const recv_ublk_group : ublk_group_fset.groups_of_scale(scale))
    {
      //Conversion to UBLK_GROUP to use classes based on UBLK_GROUP....
      UBLK_GROUP ublk_group = new sUBLK_GROUP(scale, recv_ublk_group->m_source_sp);

      for (const sUBLK_COMM_QUANTUM& quantum : recv_ublk_group->m_quantums) 
        {         
          ublk_group->add_shob_to_group(quantum.m_ublk);
        }

      const auto n_ublks = ublk_group->n_shob_ptrs();
      // printf("MPI %d, n_ublks = %d\n", my_proc_id, n_ublks);
      sUBLK*const h_ublk = ublk_group->shob_ptrs();
      auto sequence = cUBLK_SEQUENCE(h_ublk);

      while(!sequence.empty()) {

        std::array<sUBLK*,N_VOXELS_8> ublks_to_pack{};
        BOOLEAN is_spatially_coherent;
        const int n_ublks_packed = sequence.set_next_sequence(ublks_to_pack, is_spatially_coherent);
        const int mblk_id = mblks.size();
        sHMBLK* const mblk = create_mblk(ublks_to_pack, n_ublks_packed, mblk_id, scale,
                                         is_ghost,
                                         FALSE /*not solid*/,
                                         is_spatially_coherent,
                                         windows);

        //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d spatialy coherent sequence %d", my_proc_id, is_spatially_coherent);
        for(int i = 0; i < n_ublks_packed; i++) {
          auto ublk = ublks_to_pack[i];
          //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          //LOG_MSG("RECV_DEPEND").printf("MPI %d, ublk_id = %d, mega_ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, mega_ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          add_entry_to_ublk_to_mblk_map(child_ublk_to_mblk_map, ublk, mblk_id, i);
        }

        mblks.push_back(mblk);
        associate_with_mblk_box(ublks_to_pack, mblks.back(), mega_boxes);
      }

      //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, n_ublks = %d num_ublks_packed %d dest sp %d scale %d group type %d", my_proc_id, n_ublks, sequence.num_ublks_packed(), ublk_group->m_dest_sp, scale, group_type);
      assert(n_ublks == sequence.num_ublks_packed());
      groupId ++;
    } //ublk_group
}

bool use_ublk_for_ghost_mblks(const UBLK& ublk)
{
  return ublk->is_ghost() && !ublk->is_vr_fine();
}

void build_ghost_mblks(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                       std::vector<sHMBLK*>& mblks,
                       std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{
  std::vector<std::vector<UBLK>> ghost_ublks_per_scale(sim.num_scales);
  for (const UBLK& ublk : g_ublk_table[STP_FLOW_REALM].ublks()) {
    if(use_ublk_for_ghost_mblks(ublk))
      ghost_ublks_per_scale[ublk->scale()].push_back(ublk);
  }
  for(const std::vector<UBLK> ghost_ublks : ghost_ublks_per_scale) {
    if(ghost_ublks.size()>0) {
      int groupId = 0;
      const int scale = ghost_ublks.front()->scale();

      //Conversion to UBLK_GROUP to use classes based on UBLK_GROUP....
      //sUBLK_GROUP ublk_group = new sUBLK_GROUP(scale, );
      //UBLK_GROUP ublk_group = new sUBLK_GROUP(scale);
      tSHOB_GROUP<sUBLK> ublk_group;// = new tSHOB_GROUP<sUBLK>();
        
      for (auto ublk : ghost_ublks) 
        {         
          ublk_group.add_shob_to_group(ublk);
        }

      const auto n_ublks = ublk_group.n_shob_ptrs();
      // printf("MPI %d, n_ublks = %d\n", my_proc_id, n_ublks);
      sUBLK*const h_ublk = ublk_group.shob_ptrs();
      auto sequence = cUBLK_SEQUENCE(h_ublk);

      while(!sequence.empty()) {

        std::array<sUBLK*,N_VOXELS_8> ublks_to_pack{};
        BOOLEAN is_spatially_coherent;
        const int n_ublks_packed = sequence.set_next_sequence(ublks_to_pack, is_spatially_coherent);
        const int mblk_id = mblks.size();
        const bool is_solid = std::all_of(ublks_to_pack.begin(), ublks_to_pack.begin() + n_ublks_packed, [](sUBLK* ublk){ return ublk->is_solid();});
        sHMBLK* const mblk = create_mblk(ublks_to_pack, n_ublks_packed, mblk_id, scale,
                                         TRUE,
                                         is_solid,
                                         is_spatially_coherent,
                                         windows);

        //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d spatialy coherent sequence %d", my_proc_id, is_spatially_coherent);
        for(int i = 0; i < n_ublks_packed; i++) {
          auto ublk = ublks_to_pack[i];
          //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          //LOG_MSG("RECV_DEPEND").printf("MPI %d, ublk_id = %d, mega_ublk_id = %d, pos = %d,%d,%d", my_proc_id, ublk_id, mega_ublk_id, ublks_to_pack[i]->location(0), ublks_to_pack[i]->location(1), ublks_to_pack[i]->location(2));
          add_entry_to_ublk_to_mblk_map(child_ublk_to_mblk_map, ublk, mblk_id, i);
        }

        mblks.push_back(mblk);
        associate_with_mblk_box(ublks_to_pack, mblks.back(), mega_boxes);
      }

      //LOG_MSG("GPU_INIT",LOG_FUNC).printf("MPI %d, n_ublks = %d num_ublks_packed %d dest sp %d scale %d group type %d", my_proc_id, n_ublks, sequence.num_ublks_packed(), ublk_group->m_dest_sp, scale, group_type);
      assert(n_ublks == sequence.num_ublks_packed());
      groupId ++;
    }

  }
}


void build_mblks_send_groups(
                             const UBLK_GROUP_TYPE group_type,
                             const sUBLK_FSET& ublk_group_fset,
                             sMBLK_FSET&  mblk_group_fset,
                             const std::vector<sHMBLK*>& mblks,
                             const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                             bool communicate_turb_data
                             )
{
  g_mblk_send_fset.m_sets.resize(sim.num_scales);
  for (int scale = 0; scale < sim.num_scales; scale++) {
    const auto& set_of_ublk_groups = ublk_group_fset.groups_of_scale(scale);
    for (UBLK_GROUP ublk_group : set_of_ublk_groups) {
      auto* cpu_send_group = ublk_group->m_send_group;
      if(cpu_send_group)
        {
          sMBLK_GROUP signature(scale, ublk_group->m_dest_sp);
          sMBLK_GROUP* group = mblk_group_fset.find_group(&signature);
          assert(group != nullptr);

          switch(group_type)
            {
            case FRINGE_FARBLK_GROUP_TYPE:
              {
                auto far_ublk_group = dynamic_cast<sFARBLK_SEND_GROUP*>(cpu_send_group);
                assert(far_ublk_group);
                auto gpu_group = std::make_unique<cFAR_MBLK_SEND_GROUP>(*far_ublk_group, mblks, child_ublk_to_mblk_map, communicate_turb_data);
                group->m_send_group = gpu_group.get();
                g_mblk_send_fset.m_sets[scale].insert(std::move(gpu_group));
                g_strand_mgr.m_n_send_groups++;
                break;
              }
            case FRINGE_NEARBLK_GROUP_TYPE:
              {
                auto near_ublk_group = dynamic_cast<sNEARBLK_SEND_GROUP*>(cpu_send_group);
                assert(near_ublk_group);
                auto gpu_group = std::make_unique<cNEAR_MBLK_SEND_GROUP>(*near_ublk_group, mblks, child_ublk_to_mblk_map, communicate_turb_data);
                //TOD: REMOVE THIS COPY-PASTE
                group->m_send_group = gpu_group.get();
                g_mblk_send_fset.m_sets[scale].insert(std::move(gpu_group));
                g_strand_mgr.m_n_send_groups++;
                break;
              }
            default:
              LOG_MSG("GPU_INIT").printf( "Unsupported ublk communication group");
              break;
            }
        }
    }
  }
}
void clear_mblks_send_recv_groups()
{
  for(auto& set : g_mblk_send_fset.m_sets)
    set.clear();
  //TODO : recv groups
}
/*=================================================================================================
 * @fcn build_solid_mblks
 * Solid UBLKs are not part of standard UBLK groups. Hence forming MBLKs needs special treatment.
 * For most purposes, it would not be egregious to assume that these solid MBLKs
 * are not spatially coherent.
 * The discretizer can create solid UBLKs with PDE_LIKE_ADVECTION_FLAG set to true. This would mean
 * that the MBLK would also be set to have two copies of states, which is wasteful. Hence we explicitly
 * mark the solid MBLK has having only a single copy of states
 *================================================================================================*/
void build_solid_mblks(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                       std::vector<sHMBLK*>& mblks,
                       std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                       cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{
  
  size_t n_ublks = g_ublk_table[STP_FLOW_REALM].n_ublks();
  const std::vector<UBLK>& ublks = g_ublk_table[STP_FLOW_REALM].ublks();

  using SOLID_UBLKS_PER_SCALE = std::vector<std::vector<sUBLK*>>;
  SOLID_UBLKS_PER_SCALE solid_ublks_per_scale(sim.num_scales);

  auto is_vr_ublk_that_was_promoted = [](const UBLK ublk) -> bool {
    return (ublk->is_vr_fine() || ublk->is_vr_coarse()) && ublk->is_fringe2();
  };
    
  for (size_t i = 0; i < n_ublks; i ++) {
    UBLK ublk = ublks[i];

    // Avoid assigning solid ublks multiple times if they got moved to other groups
    if( ublk->is_solid() &&
        (!use_ublk_for_ghost_mblks(ublk)) &&
        (!is_vr_ublk_that_was_promoted(ublk))) {
      
      solid_ublks_per_scale[ublk->scale()].push_back(ublk);
      
    }
  }

  for (asINT32 scale = 0; scale < sim.num_scales; scale++) {
    
    auto& solid_ublks = solid_ublks_per_scale[scale];
    //Not all scales have solid UBLKs
    if (solid_ublks.empty()) {
      continue;
    }
    
    //The sort is not really needed, except to potentially make the addition of
    //UBLKs to the non coherent sequence class more efficient
    //Could be useful for debugging as well
    std::stable_sort(solid_ublks.begin(), solid_ublks.end(),
                     [] (const sUBLK* u1, const sUBLK* u2) {
                       return UBLK_SEQUENCE_SIGNATURE(u1) > UBLK_SEQUENCE_SIGNATURE(u2);
                     });

    auto sequence = cSPATIALLY_NON_COHERENT_UBLK_SEQUENCE();
    for (auto ublk : solid_ublks) {
      sequence.add_ublk(ublk);
    }
    
    while(!sequence.no_more_ublks_to_pack()) {
      std::array<sUBLK*,N_VOXELS_8> ublks_to_pack{};
      int n_ublks_packed = sequence.fill_sequence(ublks_to_pack);
      
      int mblk_id = mblks.size();
      sHMBLK* mblk = create_mblk(ublks_to_pack, n_ublks_packed,
                                 mblk_id, scale,
                                 FALSE, /*not ghost*/
                                 TRUE, /*is_solid_ublk*/
                                 FALSE /*not coherent*/,
                                 windows);
      
      for(int j = 0; j < n_ublks_packed; j++) {
        auto ublk = ublks_to_pack[j];
        add_entry_to_ublk_to_mblk_map(child_ublk_to_mblk_map, ublk, mblk_id, j);
      }

      mblks.push_back(mblk);
      associate_with_mblk_box(ublks_to_pack, mblks.back(), mega_boxes);
    } //sequence loop
    assert(sequence.num_ublks_packed() == solid_ublks.size());
  }//scale loop
}

/*=================================================================================================
 * @fcn can_mblk_perform_optimized_swap_advect
 * MBLKs that are eligible for optimized swap advection using shared memory have to be spatially
 * coherent and also have spatially coherent neighbors. For now we live with a sub-optimal check
 * for coherence of the entire neighbor MBLK. In reality, only the faces that abut the advecting
 * MBLK have to be coherent.
 *================================================================================================*/
BOOLEAN can_mblk_perform_optimized_swap_advect(sHMBLK* mblk) 
{
  if (!mblk->is_spatially_coherent()) {
    return FALSE;
  } else {
    ccDOTIMES(child_ublk, N_UBLKS_PER_MBLK) {
      const auto& box_access = mblk->box_access(child_ublk);
      for (int latvec = 0; latvec < N_MOVING_STATES; latvec++) {
	sINT16 offsets[] = {state_vx(latvec), state_vy(latvec), state_vz(latvec)};
	auto neighbor_tagged_ublk = box_access.neighbor_ublk(offsets);
	if (neighbor_tagged_ublk.is_ublk() &&
	    (!neighbor_tagged_ublk.ublk()->is_spatially_coherent())) {
	  return FALSE;
	}
      }
    }
  }
  return TRUE;
}

VOID move_infblk_1_mblks_with_two_copies_to_infblk2(MBLK_GROUP mblk_group_1,
                                                    MBLK_FSET  mblk_group_fset_2) 
{
  size_t  n_infblk_1_mblks = mblk_group_1->n_shob_ptrs();
  sHMBLK* infblk1_mblk   = mblk_group_1->shob_ptrs();

  //The reason we don't just create a group unless really necessary is that
  //strands invoke the ublk_adv_and_dyn methods if they are incharge of non-null groups
  //It's annoying to check that the group has non-zero elements everywhere
  MBLK_GROUP mblk_group_2 = mblk_group_fset_2->find_group(mblk_group_1);

  while(infblk1_mblk) {

    sHMBLK* next_infblk1_mblk = infblk1_mblk->m_next;

    if (!can_mblk_perform_optimized_swap_advect(infblk1_mblk)) {

      if (mblk_group_2 == nullptr) {
        mblk_group_2 = mblk_group_fset_2->create_group(mblk_group_1->m_scale,
                                                       mblk_group_1->m_dest_sp);	
      }

      mblk_group_1->remove_shob_from_group(infblk1_mblk);

      if (!infblk1_mblk->has_two_copies()) {
        auto infblk1_mblk_with_two_copies = demote_to_two_copy_mblk(infblk1_mblk);
        mblk_group_2->add_shob_to_group(infblk1_mblk_with_two_copies);
      } else {
        mblk_group_2->add_shob_to_group(infblk1_mblk);
      }
    }
    infblk1_mblk = next_infblk1_mblk;
  } //while infblk1_mblk
}

VOID verify_interior_farblk_1_nbrs() {
  
  MBLK_FSET mblk_group_fset = g_mblk_groups[INTERIOR_FARBLK1_GROUP_TYPE];

  if (mblk_group_fset && mblk_group_fset->n_groups()) {

    for (int scale = 0; scale < sim.num_scales; scale++) {

      const auto& set_of_mblk_groups_1   = mblk_group_fset->get_groups_of_scale(scale);

      for (MBLK_GROUP mblk_group_1 : set_of_mblk_groups_1) {
	size_t  n_infblk_1_mblks = mblk_group_1->n_shob_ptrs();
	sHMBLK* mblk = mblk_group_1->shob_ptrs();
	while(mblk) {
	  ccDOTIMES(child_ublk, N_UBLKS_PER_MBLK) {
	    const auto& box_access = mblk->box_access(child_ublk);
	    for (int latvec = 0; latvec < N_MOVING_STATES; latvec++) {
	      sINT16 offsets[] = {state_vx(latvec), state_vy(latvec), state_vz(latvec)};
	      auto neighbor_tagged_ublk = box_access.neighbor_ublk(offsets);
	      assert(neighbor_tagged_ublk.is_ublk() && neighbor_tagged_ublk.ublk());
	    }
	  }
	  mblk = mblk->m_next;
	} // while
	 
      } //group loop      
    } //scale loop
  } //fset loop
} 

VOID move_infblk_1_mblks_with_two_copies_to_infblk2() 
{
  
  MBLK_FSET mblk_group_fset = g_mblk_groups[INTERIOR_FARBLK1_GROUP_TYPE];
  MBLK_FSET mblk_group_fset_2 = g_mblk_groups[INTERIOR_FARBLK2_GROUP_TYPE];

  if (mblk_group_fset && mblk_group_fset->n_groups()) {

    for (int scale = 0; scale < sim.num_scales; scale++) {

      const auto& set_of_mblk_groups_1   = mblk_group_fset->get_groups_of_scale(scale);
      const auto& set_of_mblk_groups_2 = mblk_group_fset_2->get_groups_of_scale(scale);

      std::vector<MBLK_GROUP> to_del;
      for (MBLK_GROUP mblk_group_1 : set_of_mblk_groups_1) {
        move_infblk_1_mblks_with_two_copies_to_infblk2(mblk_group_1, mblk_group_fset_2);
        if (mblk_group_1->n_shob_ptrs() == 0) {
          to_del.push_back(mblk_group_1);
        }
      }

      for (MBLK_GROUP mblk_group_1 : to_del) {
        mblk_group_fset->delete_group(mblk_group_1);
      }
    }
  }
}

/*=================================================================================================
 * @fcn mark_mblk_groups_with_special_fluid
 * Mark groups that have one or more MBLKs with special fluid in them.
 * Groups that have no special fluid MBLKs perform dynamics with the simplified/optimized
 * ublk dynamics kernel in <physics>/gpu_fluid_dyn_ublk.cc
 *================================================================================================*/
static void mark_mblk_groups_with_special_fluid()
{

  for (int group_type = 0; group_type < N_UBLK_GROUP_TYPES; group_type++) {

    MBLK_FSET mblk_group_fset = g_mblk_groups[group_type];

    for (int scale = 0; scale < sim.num_scales; scale++) {

      const auto& set_of_mblk_groups = mblk_group_fset->get_groups_of_scale(scale);

      for (auto mblk_group : set_of_mblk_groups) {
        mblk_group->set_group_has_mblks_with_special_fluid();
      } //ublk_group

    }//scale

  }//group_type
}

/*=================================================================================================
 * @fcn build_mblks
 *================================================================================================*/
std::vector<sHMBLK*> build_mblks(std::vector<sHMBLK::UBLK_BOX> &mega_boxes,
                                 std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                 cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows)
{

  std::vector<sHMBLK*> mblks;
  
  for (int group_type = 0; group_type < N_UBLK_GROUP_TYPES; group_type++) {    

    UBLK_FSET ublk_group_fset = g_ublk_groups[group_type];
    MBLK_FSET mblk_group_fset = g_mblk_groups[group_type];
    assert(ublk_group_fset);
    assert(mblk_group_fset);

    if (ublk_group_fset && ublk_group_fset->n_groups()) {
      build_mblks_from_ublk_fset(mega_boxes, mblks,
                                 ublk_group_fset, mblk_group_fset,
                                 child_ublk_to_mblk_map,
                                 windows);
    }    

  } //group_type-loop


  build_ghost_mblks(mega_boxes, mblks, child_ublk_to_mblk_map, windows);

  build_solid_mblks(mega_boxes, mblks, child_ublk_to_mblk_map, windows);

  fill_overlap_regions(mblks, mega_boxes, child_ublk_to_mblk_map);

  move_infblk_1_mblks_with_two_copies_to_infblk2();

  mark_mblk_groups_with_special_fluid();
  
  return mblks;
}

template<class T_M_SET, class T_SET, class T_M_RECV_GROUP, class T_RECV_GROUP>
void build_mblks_rcv_group(T_M_SET& mblk_recv_fset, 
                           T_SET& recv_fset, 
                           const std::vector<sHMBLK*> mblks,
                           const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                           bool communicate_turb_data,
                           const RECV_TYPE recv_type)
{
  mblk_recv_fset.m_sets.resize(sim.num_scales);
  for( SCALE scale = 0; scale <sim.num_scales; scale++) {
    if(recv_fset.n_groups_of_scale(scale)>0) {
      for(T_RECV_GROUP *const cpu_group : recv_fset.groups_of_scale(scale)) 
        {
          assert(cpu_group);
          LOG_MSG("GPU_INIT").printf( "CPU: Source receive group from SP %d", cpu_group->m_source_sp.nsp());
          auto gpu_group = std::make_unique<T_M_RECV_GROUP>(*cpu_group, mblks, child_ublk_to_mblk_map, communicate_turb_data);
          g_strand_mgr.m_recv_channel[recv_type].add_recv_group(gpu_group.get());
          mblk_recv_fset.m_sets[scale].insert(std::move(gpu_group));
        }
    }
  }
}

void build_mblks_send_recv_groups(const std::vector<sHMBLK*> mblks,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                  bool communicate_turb_data) {
  for (int group_type = 0; group_type < N_UBLK_GROUP_TYPES; group_type++) {    
    sUBLK_FSET* ublk_group_fset = g_ublk_groups[group_type];
    if(ublk_group_fset && ublk_group_fset->n_groups()) {
      build_mblks_send_groups(UBLK_GROUP_TYPE(group_type), *ublk_group_fset, *g_mblk_groups[group_type], mblks, child_ublk_to_mblk_map, communicate_turb_data);
    }
  }
  build_mblks_rcv_group<cFAR_MBLK_RECV_FSET, sFARBLK_RECV_FSET, cFAR_MBLK_RECV_GROUP, sFARBLK_RECV_GROUP> (g_far_mblk_recv_fset, g_farblk_recv_fset, mblks, child_ublk_to_mblk_map, communicate_turb_data, FARBLK_RECV_TYPE);
  build_mblks_rcv_group<cNEAR_MBLK_RECV_FSET, sNEARBLK_RECV_FSET, cNEAR_MBLK_RECV_GROUP, sNEARBLK_RECV_GROUP> (g_near_mblk_recv_fset, g_nearblk_recv_fset, mblks, child_ublk_to_mblk_map, communicate_turb_data, NEARBLK_RECV_TYPE);
}


void gpu_mark_fringe_coarse_voxels_that_abut_interior(sHMBLK::sTAGGED_UBLK& tagged_ublk) {
  if (tagged_ublk.is_null()) {
    return;
  } else if (tagged_ublk.is_ublk_split()) {
    auto split_vector = tagged_ublk.split_ublks();
    auto split_tagged_ublks = split_vector->tagged_ublks();
    int num_split_ublks = split_vector->num_split_ublks();
    for (int i = 0; i < num_split_ublks; i++) {
      auto split_tagged_ublk = split_tagged_ublks[i];
      auto split_ublk = split_tagged_ublk.ublk();
      auto split_offset = split_tagged_ublk.get_offset_in_mega_block();
      if (split_ublk->is_vr_fine()) {
        auto coarse_ptr = split_ublk->vr_fine_data()->vr_coarse_ublk(split_offset);
        auto coarse_ublk = coarse_ptr.ublk();
        auto coarse_offset = coarse_ptr.offset();
        if (coarse_ublk && coarse_ublk->is_fringe() && !coarse_ublk->is_solid(coarse_offset)) {
          if (are_any_cardinal_neighbors_ghost(coarse_ublk, sim.num_dims, coarse_offset)) {
            //msg_internal_error("Attempt to mark coarse voxels as eligible for explode on demand, but they have ghost neighbors");
          }
          coarse_ublk->vr_coarse_data()->set_explode_on_demand(coarse_offset);
        }
      }
    }
  } else if (tagged_ublk.is_ublk_scale_interface()) {
    auto scale_interface = tagged_ublk.interface();
    for (int voxel = 0; voxel < N_VOXELS_8; voxel++) {
      gpu_mark_fringe_coarse_voxels_that_abut_interior(scale_interface->m_fine_ublks[voxel]);
    }
  } else if (tagged_ublk.is_ublk()) {
    auto ublk = tagged_ublk.ublk();
    auto offset = tagged_ublk.get_offset_in_mega_block();
    if (ublk->is_vr_fine()) {
      auto coarse_ptr = ublk->vr_fine_data()->vr_coarse_ublk(offset);
      auto coarse_ublk = coarse_ptr.ublk();
      auto coarse_offset = coarse_ptr.offset();
      if (coarse_ublk && coarse_ublk->is_fringe() && !coarse_ublk->is_solid(coarse_offset)) {
        if (are_any_cardinal_neighbors_ghost(coarse_ublk, sim.num_dims, coarse_offset)) {
          //msg_internal_error("Attempt to mark coarse voxels as eligible for explode on demand, but they have ghost neighbors");
        }
        coarse_ublk->vr_coarse_data()->set_explode_on_demand(coarse_offset);
      }
    }
  }
}

/*=================================================================================================
 * Explode of fringe vr fine ublk can indirectly access ghost data during the computation of gradients
 * for the parent coarse voxel. Therefore the simulator builds a layer around these fringe vrfine ublks
 * and promotes regular interior vr-fine and vr-coarse ublks to also be fringe. See:
 *
 * <simeng>/shob_groups.cc:promote_fringe_vr_fine_neighbor_ublks_to_fringe
 *
 * However the latter vr-fine/coarse ublks can abut interior voxels and have to be exploded as a priori, before
 * advection of interior voxels can proceed. This function marks those voxels in vrcoarse fringe
 * mblks, that only abut the interior and are safe to explode upfront rather than on demand like
 * the CPU
 *================================================================================================*/
void gpu_mark_fringe_coarse_voxels_that_abut_interior(const std::vector<sHMBLK*> mblks,
                                                      const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map) {

  UBLK_GROUP_TYPE group_type_abutting_vr_fine_fringe[] = {
    SLIDING_NEARBLK_GROUP_TYPE,
    INTERIOR_FARBLK2_GROUP_TYPE,
    INTERIOR_NEARBLK_GROUP_TYPE,
    VRFINE_SLIDING_NEARBLK_GROUP_TYPE,
    VRFINE_FARBLK_GROUP_TYPE,
    VRFINE_NEARBLK_GROUP_TYPE,
    MIRROR_UBLK_GROUP_TYPE
  };

  for (auto group_type : group_type_abutting_vr_fine_fringe) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_MBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
        for (auto& mblk : *group) {
          for (asINT32 child_ublk = 0; child_ublk < N_UBLKS_PER_MBLK; child_ublk++) {
            if (!mblk.is_solid(child_ublk)) {
              auto& box_access = mblk.box_access(child_ublk);
              auto stagged_ublk = box_access.stagged_ublk();
              gpu_mark_fringe_coarse_voxels_that_abut_interior(stagged_ublk);
              for (int latvec = 0; latvec < N_MOVING_STATES; latvec++) {
                sINT16 c_offsets[3] = {state_vx(latvec),
                                       state_vy(latvec),
                                       state_vz(latvec)};
                if (sim.is_2d() && c_offsets[2] != 0) {
                  continue;
                }
                auto tagged_ublk = box_access.neighbor_ublk(c_offsets);
                gpu_mark_fringe_coarse_voxels_that_abut_interior(tagged_ublk);
              }
            }
          }
        }
      }
    }
  }
}

void build_surfel_send_groups(const std::vector<sMSFL*>& megasfls,
                              const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                              bool communicate_turb_data)
{  
  for (int scale = 0; scale < sim.num_scales; scale++) {

    FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_SEND_FSET, g_surfel_send_fset, cpu_send_group, scale) {
      auto sfl_group = dynamic_cast<sSURFEL_SEND_GROUP*>(cpu_send_group);
      assert(sfl_group);
      auto gpu_group = std::make_unique<cMSFL_SEND_GROUP>(*sfl_group, megasfls, child_sfl_to_msfl_map, communicate_turb_data);
      g_msfl_send_fset.m_sets[scale].insert(std::move(gpu_group));
      g_strand_mgr.m_n_send_groups++;
    }

    // For cases with sliding mesh it appears that the sliding mesh strand
    // is incharge of dumping all send groups on to the send queue after delta
    // mass correction. This is done in strand_mgr.cc:add_surfel_groups_of_scale_to_send_queue
    // that is called in strand_mgr.cc:do_mlrf_surfel_delta_mass_correction
    if (!is_sliding_mesh_present()) {
      auto msfl_group_fset = g_msfl_groups[FRINGE_SURFEL_BASE_GROUP_TYPE];
      if(msfl_group_fset && msfl_group_fset->n_groups()) {
        const auto& set_of_msfl_groups = msfl_group_fset->groups_of_scale(scale);
        for (sSURFEL_GROUP_BASE* msfl_group : set_of_msfl_groups) {
          {
            for (auto& elem : g_msfl_send_fset.m_sets[scale]) {
              if (elem->m_dest_sp == msfl_group->m_dest_sp) {
                msfl_group->m_send_group = elem.get();
                break;
              }
            }
          }
        }
      } //msfl_group_fset
    } //is_sliding_mesh_present
  }
}
void build_surfel_recv_groups(
                              cMSFL_RECV_FSET& msfl_recv_fset,
                              const sSURFEL_RECV_FSET& sfl_recv_fset,
                              const std::vector<sMSFL*>& megasfls,
                              const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                              bool communicate_turb_data)
{
  msfl_recv_fset.m_sets.resize(sim.num_scales);
  for( SCALE scale = 0; scale <sim.num_scales; scale++) {
    if(sfl_recv_fset.n_groups_of_scale(scale)>0) {
      for(const sSURFEL_RECV_GROUP *const cpu_group : sfl_recv_fset.groups_of_scale(scale)) 
        {
          assert(cpu_group);
          auto gpu_group = std::make_unique<cMSFL_RECV_GROUP>(*cpu_group, megasfls, child_sfl_to_msfl_map, communicate_turb_data);
          g_strand_mgr.m_recv_channel[SURFEL_RECV_TYPE].add_recv_group(gpu_group.get());
          msfl_recv_fset.m_sets[scale].insert(std::move(gpu_group));
        }
    }
  }
}

void build_msfls_send_recv_groups(const std::vector<sMSFL*> megasfls,
                                  const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                  bool communicate_turb_data) {
  g_msfl_send_fset.m_sets.resize(sim.num_scales);
  build_surfel_send_groups(megasfls, child_sfl_to_msfl_map, communicate_turb_data);
  build_surfel_recv_groups(g_msfl_recv_fset, g_surfel_recv_fset, megasfls, child_sfl_to_msfl_map, communicate_turb_data);
}

/*=================================================================================================
 * 
 *================================================================================================*/
template<typename SFL_TYPE_TAG>
struct tDUMMY_SFL_DYN_FOR_GPU_COPY;
using sDUMMY_SFL_DYN_FOR_GPU_COPY = tDUMMY_SFL_DYN_FOR_GPU_COPY<SFL_SDFLOAT_TYPE_TAG>;
  
template<eDYN_SURFEL_TYPE e>
struct tDYN_SURFEL_COPY_TRAITS {
  template<typename SFL_TYPE_TAG>
  using DYN_DATA_TYPE = tDUMMY_SFL_DYN_FOR_GPU_COPY<SFL_TYPE_TAG>;
};

#define MAKE_DYN_SURFEL_COPY_TRAITS(type)                       \
  template<>                                                    \
  struct tDYN_SURFEL_COPY_TRAITS<type## _TYPE> {                \
    template<typename SFL_TYPE_TAG>                             \
    using DYN_DATA_TYPE = t ## type ## _DATA<SFL_TYPE_TAG>;     \
  };

template<eDYN_SURFEL_TYPE e>
VOID copy(const typename sSURFEL::sSURFEL_DYNAMICS_DATA* sdyn,
	  typename sMSFL::sSURFEL_DYNAMICS_DATA* msdyn,
	  int child_sfl) {
  
  using CHILD_SFL_DATA_TYPE = typename tDYN_SURFEL_COPY_TRAITS<e>::template DYN_DATA_TYPE<SFL_SDFLOAT_TYPE_TAG>;
  using MSFL_DATA_TYPE = typename tDYN_SURFEL_COPY_TRAITS<e>::template DYN_DATA_TYPE<MSFL_SDFLOAT_TYPE_TAG>;

  if constexpr (std::is_same<CHILD_SFL_DATA_TYPE, sDUMMY_SFL_DYN_FOR_GPU_COPY>::value) {
    msg_internal_error("Copying of dyn data for surfel type %d, has not been implemented", (int) e);  
  } else {  
    init_msfl((const CHILD_SFL_DATA_TYPE&) *sdyn,
              (MSFL_DATA_TYPE&) *msdyn,
              child_sfl);
  }
}

struct sCOPY_MSFL_DYN_DATA {
  template<int index>
  static void eval(const typename  sSURFEL::sSURFEL_DYNAMICS_DATA* sdyn,
		   typename sMSFL::sSURFEL_DYNAMICS_DATA* msdyn,
		   int child_sfl) {
    if (sdyn->m_dynamics_type == index) {
      copy<static_cast<eDYN_SURFEL_TYPE>(index)>(sdyn, msdyn, child_sfl);
    } else if (index == INVALID_SURFEL_TYPE) {
      msg_internal_error("Unknown surfel dynamics type");
    }
  }  
};

MAKE_DYN_SURFEL_COPY_TRAITS(SLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(STATIC_PRESSURE_FREE_DIR_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(STATIC_PRESSURE_FIXED_DIR_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(STAG_PRESSURE_FREE_DIR_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(STAG_PRESSURE_FIXED_DIR_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(FIXED_VEL_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(NOSLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(LINEAR_NOSLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(ANGULAR_NOSLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(MASS_FLUX_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(MASS_FLOW_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(SOURCE_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(LINEAR_SLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(ANGULAR_SLIP_SURFEL);
MAKE_DYN_SURFEL_COPY_TRAITS(VEL_SLIP_SURFEL);

static
void copy_msfl_dyn_data(sSURFEL&  sfl,
                        sMSFL& mega,
                        int child_sfl) {

  auto sdyn = sfl.dynamics_data();
  auto sdyn_64 = mega.dynamics_data();

  if (sdyn_64->m_surface_physics_desc &&
      sdyn_64->m_dynamics_type != sdyn->m_dynamics_type) {
    msg_internal_error("Attempt to form a msfl with surfels of inconsistent dynamics type");
  } else {
    sdyn_64->m_surface_physics_desc = sdyn->m_surface_physics_desc;
    sdyn_64->m_dynamics_type = sdyn->m_dynamics_type;
  }
  COMPILE_TIME_LOOP<SLIP_SURFEL_TYPE, N_SURFEL_DYN_TYPES, sCOPY_MSFL_DYN_DATA>::iterate(sdyn, sdyn_64, child_sfl);

}

/*=================================================================================================
 * @fcn init_msfl
 *================================================================================================*/
static
void init_msfl(const std::array<sSURFEL*,N_SFLS_PER_MSFL>& sfls_to_pack,
               int n_sfls_to_pack, tSURFEL_ATTRS<N_SFLS_PER_MSFL> mega_attrs,
               cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows,
               sMSFL& mega, STP_SHOB_ID id, STP_SCALE scale,
               bool is_ghost)
{
  //SURFACE SHOB ATTRIBUTES
  mega.m_surfel_attributes = mega_attrs;
  mega.set_id(id);
  mega.set_scale(scale);
  mega.set_ref_frame_index(sfls_to_pack[0]->ref_frame_index());

  ccDOTIMES(i,N_SFLS_PER_MSFL) {
    mega.m_face_index[i] = (uINT16) -1;
  }

  LRF_PHYSICS_DESCRIPTOR lrf = mega.ref_frame_index() >= 0 ? &sim.lrf_physics_descs[mega.ref_frame_index()] : nullptr;

  std::vector<sMSFL_MEAS_CELL_PTR> msfl_meas_cell_ptrs;
  std::set<sINT32> windows_touched;

  if (mega.id() == GPU::print_msfl_children_id) {
    msg_print("mblk %d has %d child sfls", mega.id(), n_sfls_to_pack);
    ccDOTIMES(child_sfl, n_sfls_to_pack) {
      msg_print("\t%d: %d", child_sfl, sfls_to_pack[child_sfl]->id());
    }
  }

  for(int child_sfl = 0; child_sfl < n_sfls_to_pack; child_sfl++) {
    mega.m_face_index[child_sfl] = sfls_to_pack[child_sfl]->m_face_index;
    
    sSURFEL& sfl = *sfls_to_pack[child_sfl];

    if (sfl.id() == GPU::print_sfl_parent_id) {
      msg_print("sfl %d is in msfl %d child sfl %d", sfl.id(), mega.id(), child_sfl);
    }

    assert(sfl.ref_frame_index() == mega.ref_frame_index());    

    //SURFACE SHOB
    mega.set_is_valid_child_surfel(child_sfl);
    mega.area[child_sfl] = sfl.area;
    mega.m_off_plane_tolerance[child_sfl] = sfl.m_off_plane_tolerance;
    mega.incoming_latvec_mask[child_sfl] = sfl.incoming_latvec_mask;
    if (sfl.is_weightless_mlrf()) {
      mega.set_weightless_mlrf(TRUE, child_sfl);
    }

    if (sfl.is_not_free_slip_wall()) {
      mega.set_not_free_slip_wall(true, child_sfl);
    }
    
    if (sfl.interacts_with_vr_ublks()) {
      mega.set_interacts_with_vr_ublks(child_sfl);
    }

    // Even odd data
    if (sfl.is_even_or_odd()) {
      auto msfl_even_odd_data = mega.even_odd_data();
      auto sfl_even_odd_data = sfl.even_odd_data();
      //Temporarily stash clone surfel ptr, and resolve it later once all MSFLs are formed
      //This is done since the MSFL that the clone surfel belongs to might not have been created
      msfl_even_odd_data->m_clone_surfel[child_sfl].set_masked_ptr(sfl_even_odd_data->m_clone_surfel.masked_ptr());
    }

    if (child_sfl == 0) {
      mega.set_even_odd_mask(sfl.even_odd_mask());
    } else {
      assert(mega.even_odd_mask() == sfl.even_odd_mask());
    }
    
    for(int i=0; i<3; i++) {
      mega.centroid[i][child_sfl] = sfl.centroid[i];
      mega.normal[i][child_sfl] = sfl.normal[i];
    }

    //Actual SURFEL
    VEC_COPY_SFL_TO_MSFL(in_state_scale_factors, N_LATTICE_VECTOR_PAIRS);
    COPY_SFL_TO_MSFL(voxel_surfel_weight_total);
    COPY_SFL_TO_MSFL(y_sample);
    COPY_SFL_TO_MSFL(percent_v2s);
    COPY_SFL_TO_MSFL(mme_weight);
    COPY_SFL_TO_MSFL(s2s_sampling_weight);
    COPY_SFL_TO_MSFL(m_vr_phase_mask);
    VEC_COPY_SFL_TO_MSFL(in_states_voxel_weight, N_LATTICE_VECTOR_PAIRS);
    VEC_COPY_SFL_TO_MSFL(in_states_voxel_weight2, N_LATTICE_VECTOR_PAIRS);
    //SFL data blocks
    COPY_SFL_TO_MSFL(lb_data());

    if (sfl.has_two_copies_of_outflux()) {
      COPY_SFL_TO_MSFL(s2s_lb_data());
      if (sim.is_heat_transfer) {
	COPY_SFL_TO_MSFL(s2s_t_data());
      }
      if (sim.is_scalar_model && sim.uds_solver_type == LB_UDS) {
	for(int i=0; i<sim.n_user_defined_scalars; i++) {
	  COPY_SFL_TO_MSFL(s2s_uds_data(i));
	}
      }
    }
    
    if (sfl.has_v2s_data()) {
      COPY_SFL_TO_MSFL(v2s_lb_data());
      
      if (sim.is_turb_model) {
        COPY_SFL_TO_MSFL(v2s_turb_data());
      }
      
      if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
        COPY_SFL_TO_MSFL(v2s_t_data());
      }

      if (sim.is_scalar_model) {
	for(int i=0; i<sim.n_user_defined_scalars; i++) {
	  COPY_SFL_TO_MSFL(v2s_uds_data(i));
	}
      }
    }

    if (sfl.is_lrf()) {
      COPY_SFL_TO_MSFL(lrf_data());
    }
    
    if (sim.is_turb_model) {
      COPY_SFL_TO_MSFL(turb_data());
    }

    if (sim.is_heat_transfer) {
      COPY_SFL_TO_MSFL(t_data());
    }

    if (g.is_multi_component) {
      COPY_SFL_TO_MSFL(mc_data());
    }

    if (sim.is_scalar_model) {
      for(int i=0; i<sim.n_user_defined_scalars; i++) {
        COPY_SFL_TO_MSFL(uds_data(i));
      }
    }

    if ( sim.is_particle_model ) {
      msg_internal_error("Particle data is not supported for MSFLs.");
    }

    if (!is_ghost && mega.has_dynamics_data()) {
      copy_msfl_dyn_data(sfl, mega, child_sfl);
      sSURFEL_DYNAMICS_DATA * child_dyn_data = sfl.dynamics_data();
    uINT32 n_child_sfl_meas_cell_ptrs = child_dyn_data->n_meas_cell_ptrs();
      sSURFEL_MEAS_CELL_PTR * sfl_meas_cell_ptr = child_dyn_data->surfel_meas_cell_ptrs();
      sSURFEL_MEAS_CELL_PTR * end = sfl_meas_cell_ptr + n_child_sfl_meas_cell_ptrs;

      for(int i = 0; i < n_child_sfl_meas_cell_ptrs; /* empty on purpose */) {
        windows_touched.insert(sfl_meas_cell_ptr->window_index());
        auto& window = windows[sfl_meas_cell_ptr->window_index()];
        int n_meas_cell_pointers_processed = window->add_sfl_to_device_meas_cells(child_sfl, sfl_meas_cell_ptr, end, lrf_is_rotating(lrf));
        sfl_meas_cell_ptr += n_meas_cell_pointers_processed;
        i += n_meas_cell_pointers_processed;
        assert(i <= n_child_sfl_meas_cell_ptrs);
      }
    }
  } // child sfl loop

  // Meas
  for(auto window_id : windows_touched) {
    windows[window_id]->append_msfl_meas_cells(msfl_meas_cell_ptrs); 
  }

  std::sort(msfl_meas_cell_ptrs.begin(), msfl_meas_cell_ptrs.end());

  if (msfl_meas_cell_ptrs.size() > 0) {
    set_msfl_meas_cell_ptrs(mega, msfl_meas_cell_ptrs);
  }
}

static
STP_PHYSTYPE_TYPE get_msfl_phys_type(const std::array<sSURFEL*, N_SFLS_PER_MSFL>& sfls_to_pack,
                                     int n_sfls_packed) {
  auto first_surfel = sfls_to_pack[0];
  bool is_backside = first_surfel->is_backside() && !first_surfel->is_isurfel();
  auto desc = surface_physics_desc_from_face_index(first_surfel->m_face_index, is_backside, false);
  STP_PHYSTYPE_TYPE msfl_phys_type = STP_INVALID_PHYSTYPE_TYPE;
  if (desc) {
    msfl_phys_type = surfel_sim_type_from_cdi_type(desc);
    auto first_dyn_type = first_surfel->dynamics_data()->m_dynamics_type;
    //All surfels must have same dynamics type
    cassert(std::all_of(sfls_to_pack.begin(),
                        sfls_to_pack.begin() + n_sfls_packed,
                        [&](const sSURFEL* sfl) {
                          return !sfl->is_conduction_surfel() && sfl->has_dynamics_data() &&
                            ((sSURFEL*)sfl)->dynamics_data()->m_dynamics_type == first_dyn_type;
                        }));
  } else {
    if (first_surfel->is_lrf()) {
      msfl_phys_type = STP_MLRF_SURFEL_TYPE;
      assert(std::all_of(sfls_to_pack.begin(),
                         sfls_to_pack.begin() + n_sfls_packed,
                         [&](const sSURFEL* sfl) {
                           return sfl->is_lrf();
                         }));
    }
  }
  return msfl_phys_type;
}

/*=================================================================================================
 * @fcn create_msfl
 *================================================================================================*/
static
sMSFL* create_msfl(const std::array<sSURFEL*, N_SFLS_PER_MSFL>& sfls_to_pack, 
                   int n_sfls_packed,
                   STP_SHOB_ID id,
                   STP_SCALE scale,
                   cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows,
                   bool is_ghost)
{
  tSURFEL_ATTRS<N_SFLS_PER_MSFL> msfl_attributes;
  msfl_attributes.m_surfel_type = 0;

  auto msfl_phys_type = get_msfl_phys_type(sfls_to_pack, n_sfls_packed);
  

  for(int i=0; i<n_sfls_packed; i++) {    
    sSURFEL* sfl = sfls_to_pack[i];
    msfl_attributes.m_surfel_type |= sfl->m_surfel_attributes.m_surfel_type;
    msfl_attributes.m_is_inlet_or_outlet |= sfl->m_surfel_attributes.m_is_inlet_or_outlet; //TODO: check with Mukul
    msfl_attributes.m_child_is_inlet_or_outlet.set_or_reset(i, sfl->m_surfel_attributes.m_is_inlet_or_outlet); //TODO: check with Mukul   
  }

  size_t msfl_size = sMSFL::size(msfl_attributes.surfel_type(),
                                 msfl_attributes.m_is_mirror,
                                 msfl_phys_type);

  sMSFL* msfl;
  int ierr = posix_memalign((void**) &msfl, sMSFL::ALIGNMENT, msfl_size);
  memset(msfl, 0, msfl_size);

  msfl_attributes.m_is_ghost = is_ghost;
  if (is_ghost)
    msfl_attributes.m_has_v2s_data = true;

  init_msfl(sfls_to_pack, n_sfls_packed, msfl_attributes, windows, *msfl, id, scale, is_ghost);
  g_msfl_sizes_table.add(msfl, msfl_size);

  return msfl;

}

template<typename SFL_ITERATOR>
cSFL_SEQUENCE_FOR_MSFL_BUILD<SFL_ITERATOR>::cSFL_SEQUENCE_FOR_MSFL_BUILD(SFL_ITERATOR start,
                                                                         SFL_ITERATOR end):
  m_start(start),
  m_end(end),
  m_n_total_sfls_packed(0) {
}

template<typename SFL_ITERATOR>
int cSFL_SEQUENCE_FOR_MSFL_BUILD<SFL_ITERATOR>::set_next_sequence(std::array<sSURFEL*, N_SFLS_PER_MSFL>& sfls_to_pack) {

  int n_sfls_packed = 0;
  auto prev_h_sfl = m_start;
  for (int s = 0;
       s < N_SFLS_PER_MSFL && (m_start != m_end) &&
         sMSFL_SORT_CATEGORIES::equiv(sfl_iter_to_ptr(prev_h_sfl),
                                      sfl_iter_to_ptr(m_start));
       s++, n_sfls_packed++) {
    sfls_to_pack[s] = sfl_iter_to_ptr(m_start);
    prev_h_sfl = m_start;
    m_start++;
  }
  m_n_total_sfls_packed += n_sfls_packed;
  return n_sfls_packed;
}

template class cSFL_SEQUENCE_FOR_MSFL_BUILD<tSHOB_GROUP_ITERATOR<sSURFEL>>;


/*=================================================================================================
 * @fcn build_msfls_from_sfl_group
 * Implementation for non-sliding mesh surfels
 *================================================================================================*/
void build_msfls_from_sfl_group(std::vector<sMSFL*>& msfls,
                                int scale,
                                SURFEL_GROUP sfl_group,
                                MSFL_FSET  msfl_group_fset,
                                std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                const std::vector<sHMBLK*>& mblks,
                                const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows) 
{
  size_t n_surfels = sfl_group->n_shob_ptrs();

  // WARNING!
  // Surfel groups can be empty but are needed to associate send groups with missing parent shob groups
  // The simulator creates dummy empty shob groups so that sends are triggered at the end of ublk/surfel process control
  // If we skip mirroring these dummy groups in GPU world, send groups are orphaned in build_surfel_send_groups
  // leading to hangs
  // if(n_surfels == 0) {
  //   msg_print("GPU INIT, n_surfels 0 for group_type %d, scale %d, dest_sp %d\n",
  //             sfl_group->m_supertype, scale, sfl_group->m_supertype, sfl_group->m_dest_sp.nsp());
  // }

  auto msfl_group = msfl_group_fset->create_group(scale,
                                                  sfl_group->m_dest_sp,
                                                  sfl_group->m_supertype,
                                                  sfl_group->m_realm);

  
  auto sequence = cSFL_SEQUENCE_FOR_MSFL_BUILD(sfl_group->begin(),
                                               sfl_group->end());

  //This loop iterates through a group of sfls, of the same sort type
  //to form MSFLs
  while(!sequence.empty()) {
    std::array<sSURFEL*, N_SFLS_PER_MSFL> sfls_to_pack{};
    int n_sfls_packed = sequence.set_next_sequence(sfls_to_pack);

    STP_SHOB_ID id = msfls.size();
    sMSFL* msfl = create_msfl(sfls_to_pack, n_sfls_packed, id, scale, windows, false);

    for(int i = 0; i < n_sfls_packed; i++) {
      auto sfl_id = sfls_to_pack[i]->id();
      // if (sfl_id == 17103) {
      //   printf("Proc %d, MSFL %d, sfl %d, child %d\n", my_proc_id, msfl->id(), sfl_id, i);
      // }                
      child_sfl_to_msfl_map[sfl_id] = std::make_pair<STP_SHOB_ID, uINT8>(msfl->id(),i);
    }

    msfl_group->add_shob_to_group(msfl);

    msfls.push_back(msfl);

    init_msfl_interactions(msfl, sfls_to_pack, n_sfls_packed,
                           mblks, child_ublk_to_mblk_map);

  }
  assert(n_surfels == sequence.total_sfls_packed());
}

static
auto create_regular_surfel_groups_for_sliding_mesh_surfels(int scale) {
  
  auto msfl_group = g_msfl_groups[MLRF_SURFEL_GROUP_TYPE]->create_group(scale, cNEIGHBOR_SP(0),
                                                                        MLRF_SURFEL_GROUP_SUPERTYPE, 
                                                                        STP_FLOW_REALM);

  //CPU surfel groups have create_group method missing :(
  sSURFEL_GROUP signature(scale, cNEIGHBOR_SP(0), MLRF_SURFEL_GROUP_SUPERTYPE, STP_FLOW_REALM);
  auto sfl_group = g_surfel_groups[MLRF_SURFEL_GROUP_TYPE]->find_group(&signature);
  if (sfl_group == nullptr) {
    sfl_group = xnew sSURFEL_GROUP(scale, cNEIGHBOR_SP(0), MLRF_SURFEL_GROUP_SUPERTYPE, STP_FLOW_REALM);
    g_surfel_groups[MLRF_SURFEL_GROUP_TYPE]->add_group(sfl_group);
  }
  return std::tuple<SURFEL_GROUP, MSFL_GROUP>({sfl_group, msfl_group});
}

/*=================================================================================================
 * After the tagged mlrf surfels array in the group are sorted, we want to ensure linear indexing the
 * send depot array with the array index, matches what you'd expect by accessing the depot indirectly
 * with the MLRF msfl's mlrf ring data. This check is very important to ensure we don't get garbage
 * results in the copy_pre_dynamics_kernel which is based of the tagged mlrf surfels to handle
 * weightless surfels
 *================================================================================================*/
static
VOID check_tagged_msfl_and_gpu_depots_consistency(const std::vector<sMSFL*>& msfls,
                                                  int msfl_start_index_for_check,
                                                  tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>* msfl_group) {
  int quantum = 0;
  for (int j = msfl_start_index_for_check; j < msfls.size(); j++) {
    auto mlrf_msfl = msfls[j];
    for (int soxor = 0; soxor < N_SFLS_PER_MSFL; soxor++) {
      if (mlrf_msfl->is_valid_child_surfel(soxor)) {
        
        auto mlrf_data = mlrf_msfl->mlrf_data();
        asINT32 nth_ring = mlrf_data->m_ring_set_index[soxor];
        asINT32 nth_surfel_in_ring = mlrf_data->m_index_in_ring[soxor];
        MLRF_ORIENTATION orientation = mlrf_msfl->is_interior_lrf()? MLRF_INTERNAL : MLRF_EXTERNAL;

        //Skip weightless surfels potentially padding the real surfel to the left
        while(quantum < msfl_group->n_quantums() &&
              msfl_group->quantums()[quantum].is_surfel_weightless()) {
          quantum++;
        }

        auto tagged_mlrf_sfl = msfl_group->quantums()[quantum];
        //We still need this check since previous while loop could terminate when we've
        //reached the end of the quantum vector, not necessarily because we've reached a
        //weightless surfel
        if (!tagged_mlrf_sfl.is_surfel_weightless()) {
          STP_SHOB_ID id = tagged_mlrf_sfl.mlrf_surfel()->id();
          assert(id == mlrf_msfl->id());
          assert(tagged_mlrf_sfl.child_sfl() == soxor);          
          MLRF_PRE_DYNAMICS_DEPOT send_depot = msfl_group->ring(nth_ring)->m_pre_dynamics_send_buffer[orientation] + nth_surfel_in_ring;
          MLRF_PRE_DYNAMICS_DEPOT test_send_depot = msfl_group->ring(0)->m_pre_dynamics_send_buffer[1] + quantum;
          assert(send_depot == test_send_depot);
          // MLRF_PRE_DYNAMICS_DEPOT recv_depot = msfl_group->ring(nth_ring)->m_pre_dynamics_recv_buffer[orientation] + nth_surfel_in_ring;
          // MLRF_PRE_DYNAMICS_DEPOT test_recv_depot = msfl_group->ring(0)->m_pre_dynamics_recv_buffer[1] + i;          
          //assert(recv_depot == test_recv_depot);
          quantum++;
        }
        
        //Skip weightless surfels that pad real surfel to the right
        while(quantum < msfl_group->n_quantums() &&
              msfl_group->quantums()[quantum].is_surfel_weightless()) {
          quantum++;
        }
      }
      //Skip sentinel surfel
      if (quantum < msfl_group->n_quantums() &&
          msfl_group->quantums()[quantum].is_sentinel()) {
        quantum++;
      }
    }
  }
  assert(quantum == msfl_group->no_of_send_depots());  
}

static
VOID create_tagged_msfls_and_gpu_depots(const std::vector<sMSFL*>& msfls,
                                        MLRF_SURFEL_GROUP sfl_group,
                                        tMLRF_SURFEL_GROUP<MSFL_SDFLOAT_TYPE_TAG>* msfl_group,
                                        const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map) {

  GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> pre_dynamics_recv_depots = \
    GPU::malloc<sMLRF_PRE_DYNAMICS_DEPOT>(sfl_group->no_of_recv_depots(), "MLRF recv depots");

  GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> post_dynamics_recv_depots = \
    GPU::malloc<sMLRF_POST_DYNAMICS_DEPOT>(sfl_group->no_of_recv_depots(), "MLRF recv depots");;
  
  GPU::Ptr<sMLRF_PRE_DYNAMICS_DEPOT> pre_dynamics_send_depots = \
    GPU::malloc<sMLRF_PRE_DYNAMICS_DEPOT>(sfl_group->no_of_send_depots(), "MLRF send depots");

  GPU::Ptr<sMLRF_POST_DYNAMICS_DEPOT> post_dynamics_send_depots = \
    GPU::malloc<sMLRF_POST_DYNAMICS_DEPOT>(sfl_group->no_of_send_depots(), "MLRF send depots");

  GPU::zero(pre_dynamics_recv_depots, sfl_group->no_of_recv_depots());
  GPU::zero(post_dynamics_recv_depots, sfl_group->no_of_recv_depots());  
  GPU::zero(pre_dynamics_send_depots, sfl_group->no_of_send_depots());  
  GPU::zero(post_dynamics_send_depots, sfl_group->no_of_send_depots());

  asINT32 n_total_recv_depots_per_frame = 0;
  asINT32 n_total_send_depots_per_frame = 0;

  std::vector<cDEVICE_MLRF_RING> device_rings;
  ccDOTIMES(nth_ring, sfl_group->n_rings()) {
    MLRF_RING ring = sfl_group->ring(nth_ring);
    device_rings.emplace_back(ring);
  }

  //Sorting of sliding mesh surfels has all the exterior surfels appear first
  for(auto orientation : {MLRF_EXTERNAL, MLRF_INTERNAL}) {
    ccDOTIMES(nth_ring, sfl_group->n_rings()) {
      MLRF_RING ring = sfl_group->ring(nth_ring);
      auto device_ring = &device_rings[nth_ring];
      //recv depots
      {
        ring->m_pre_dynamics_recv_buffer[orientation] =
          pre_dynamics_recv_depots.get() + n_total_recv_depots_per_frame;

        device_ring->set_pre_dynamics_recv_buffer(pre_dynamics_recv_depots + \
                                                  n_total_recv_depots_per_frame,
                                                  orientation);

        ring->m_post_dynamics_recv_buffer[orientation] =
          post_dynamics_recv_depots.get() + n_total_recv_depots_per_frame;

        device_ring->set_post_dynamics_recv_buffer(post_dynamics_recv_depots + \
                                                   n_total_recv_depots_per_frame,
                                                   orientation);
        asINT32 n_recv_depots_per_frame = ring->n_recv_depots_per_frame();
        n_total_recv_depots_per_frame += n_recv_depots_per_frame;
      }
      //send depots
      {

        ring->m_pre_dynamics_send_buffer[orientation] =
          pre_dynamics_send_depots.get() + n_total_send_depots_per_frame;

        device_ring->set_pre_dynamics_send_buffer(pre_dynamics_send_depots + \
                                                  n_total_send_depots_per_frame,
                                                  orientation);

        ring->m_post_dynamics_send_buffer[orientation] =
          post_dynamics_send_depots.get() + n_total_send_depots_per_frame;

        device_ring->set_post_dynamics_send_buffer(post_dynamics_send_depots + \
                                                   n_total_send_depots_per_frame,
                                                   orientation);
        asINT32 n_send_depots_per_frame = ring->n_send_depots_per_frame();
        n_total_send_depots_per_frame += n_send_depots_per_frame;
      }
    } //nth_ring
  } // orientation

  assert(n_total_recv_depots_per_frame == sfl_group->no_of_recv_depots());
  assert(n_total_send_depots_per_frame == sfl_group->no_of_send_depots());

  auto device_allocated_rings = GPU::malloc<cDEVICE_MLRF_RING>(device_rings.size(), "Device MLRF rings");
  GPU::copy_h2d(device_allocated_rings, &device_rings[0], device_rings.size());
  msfl_group->set_device_rings(device_allocated_rings);

  //Create clone vector of host MLRF rings on MSFL_MLRF_GROUP for comm
  auto& msfl_host_rings = msfl_group->ref_to_rings();
  auto& sfl_host_rings = sfl_group->ref_to_rings();
  msfl_host_rings.reserve(sfl_group->n_rings());
  std::copy(sfl_host_rings.begin(), sfl_host_rings.end(),
            std::back_inserter(msfl_host_rings));
  
  //Recreate vector of tagged mlrf surfels
  TAGGED_MLRF_SURFEL *first_surfel_on_ring = &sfl_group->quantums()[0];
  ccDOTIMES(nth_ring, sfl_group->n_rings()) {
    MLRF_RING ring = sfl_group->ring(nth_ring);
    asINT32 n_surfels_per_frame_on_proc = ring->m_n_surfels_per_frame_on_proc;
    TAGGED_MLRF_SURFEL *ring_surfels[MLRF_N_ORIENTATIONS];
    ring_surfels[MLRF_EXTERNAL] = first_surfel_on_ring;
    ring_surfels[MLRF_INTERNAL] = first_surfel_on_ring + n_surfels_per_frame_on_proc;

    ccDOTIMES(orientation, MLRF_N_ORIENTATIONS) {
      MLRF_RING_SEGMENT segments = ring->ring_segments();
      asINT32 n_surfels_in_ring = ring->m_n_surfels_per_frame_in_ring;
      asINT32 highest_segment_number = ring->n_ring_segments() - 1;
      asINT32 n_ring_segments_on_proc = ring->n_ring_segments_on_proc();
      asINT32 *ring_segments_on_proc = ring->ring_segments_on_proc();
      asINT32 nth_ring_segment_on_proc = 0;
      asINT32 segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
      asINT32 n_surfels_in_segment = (segment_number < highest_segment_number)
                               ? segments[segment_number + 1].offset - segments[segment_number].offset
                               : n_surfels_in_ring - segments[segment_number].offset;
      asINT32 nth_surfel_in_segment = 0;
      
      ccDOTIMES(nth_surfel, n_surfels_per_frame_on_proc) {
        asINT32 inverse_orientation = orientation ^ 0x1;
        TAGGED_MLRF_SURFEL tagged_mlrf_sfl = ring_surfels[inverse_orientation][nth_surfel];
        auto mlrf_sfl = tagged_mlrf_sfl.mlrf_surfel();
        auto sfl_id = mlrf_sfl->id();
        BOOLEAN is_weightless = tagged_mlrf_sfl.is_surfel_weightless();
        auto [msfl_id, soxor] = child_sfl_to_msfl_map.at(sfl_id);
        auto mlrf_msfl = msfls.at(msfl_id);
        auto mlrf_sfl_data = mlrf_sfl->mlrf_data();
        add_mlrf_surfel(msfl_group, mlrf_msfl,
                        mlrf_sfl_data,
                        is_weightless,
                        nth_ring,
                        soxor,
                        nth_surfel,
                        static_cast<uINT32>(nth_surfel + nth_ring_segment_on_proc));
        
        nth_surfel_in_segment++;
        if(nth_surfel_in_segment == n_surfels_in_segment) {
          nth_ring_segment_on_proc++;
          //msfl_group->add_sentinel_tagged_sfl();
          if (nth_ring_segment_on_proc == n_ring_segments_on_proc)
          {
            break;
          }
          segment_number = ring_segments_on_proc[nth_ring_segment_on_proc];
          n_surfels_in_segment = (segment_number < highest_segment_number)
            ? segments[segment_number + 1].offset - segments[segment_number].offset
            : n_surfels_in_ring - segments[segment_number].offset;
          nth_surfel_in_segment = 0;
        }        
      }
      msfl_group->add_sentinel_tagged_sfl();
    }
    first_surfel_on_ring += (2 * n_surfels_per_frame_on_proc); // Next ring
  }

  assert(msfl_group->n_quantums() == sfl_group->no_of_send_depots());
 
  //Sort tagged sfls to be same order as depots in ring
  std::stable_sort(msfl_group->quantums().begin(),
                   msfl_group->quantums().end(),
                   tMSFL_SORT_CATEGORIES<MSFL_SDFLOAT_TYPE_TAG>::comp);
  
}

/*=================================================================================================
 * @fcn build_msfls_from_sfl_group
 * Implementation for sliding mesh surfels
 *================================================================================================*/
void build_msfls_from_sfl_group(std::vector<sMSFL*>& msfls,
                                int scale,
                                MLRF_SURFEL_GROUP sfl_group,
                                MLRF_MSFL_FSET  msfl_group_fset,
                                std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                const std::vector<sHMBLK*>& mblks,
                                const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,                                
                                cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows) {
  //For whatever reason, empty sliding mesh groups are created on the CPU
  if (sfl_group->n_quantums() == 0) {
    return;
  }

  std::vector<sSURFEL*> sorted_mlrf_surfels;
  sorted_mlrf_surfels.reserve(sfl_group->n_quantums());
  for (auto tagged_mlrf_sfl : sfl_group->quantums()) {
    auto mlrf_sfl = tagged_mlrf_sfl.mlrf_surfel();
    if (!tagged_mlrf_sfl.is_surfel_weightless()) {
      sorted_mlrf_surfels.push_back(mlrf_sfl);
    }
  }

  auto msfl_group = msfl_group_fset->create_group(scale,
                                                  sfl_group->get_lrf_index(),
                                                  sfl_group->m_even_odd,
                                                  sfl_group->m_realm);

  auto [sorted_sfl_dyn_group, msfl_dyn_group] = create_regular_surfel_groups_for_sliding_mesh_surfels(scale);
  
  std::stable_sort(sorted_mlrf_surfels.begin(),
                   sorted_mlrf_surfels.end(),
                   sMSFL_SORT_CATEGORIES::comp);

  auto sequence = cSFL_SEQUENCE_FOR_MSFL_BUILD(sorted_mlrf_surfels.begin(),
                                               sorted_mlrf_surfels.end());

  STP_SHOB_ID consistency_check_start_index = msfls.size();
  
  //This loop iterates through a group of sfls, of the same sort type
  //to form MSFLs
  while(!sequence.empty()) {
    std::array<sSURFEL*, N_SFLS_PER_MSFL> sfls_to_pack{};
    int n_sfls_packed = sequence.set_next_sequence(sfls_to_pack);

    STP_SHOB_ID id = msfls.size();
    sMSFL* msfl = create_msfl(sfls_to_pack, n_sfls_packed, id, scale, windows, false);

    for(int i = 0; i < n_sfls_packed; i++) {
      auto sfl_id = sfls_to_pack[i]->id();
      // if (sfl_id == 17103) {
      //   printf("Proc %d, MSFL %d, sfl %d, child %d\n", my_proc_id, msfl->id(), sfl_id, i);
      // }                  
      child_sfl_to_msfl_map[sfl_id] = std::make_pair<STP_SHOB_ID, uINT8>(msfl->id(),i);
      
      sorted_sfl_dyn_group->add_shob_to_group(sfls_to_pack[i]);
    }

    msfl_dyn_group->add_shob_to_group(msfl);

    msfls.push_back(msfl);

    init_msfl_interactions(msfl, sfls_to_pack, n_sfls_packed,
                           mblks, child_ublk_to_mblk_map);

  }
  assert(sorted_mlrf_surfels.size() == sequence.total_sfls_packed());

  create_tagged_msfls_and_gpu_depots(msfls, sfl_group,
                                     msfl_group, child_sfl_to_msfl_map);
  #if ENABLE_CONSISTENCY_CHECKS
  check_tagged_msfl_and_gpu_depots_consistency(msfls,
                                               consistency_check_start_index,
                                               msfl_group);
  #endif
}

/*=================================================================================================
 * @fcn build_megaasfls_from_sfl_fset
 * For each group type fset, create analogous MSFL groups for dest_sp and scale
 *================================================================================================*/
template<typename SFL_FSET_TYPE, typename MSFL_FSET_TYPE>
void build_msfls_from_sfl_fset(std::vector<sMSFL*>& msfls,
                               int sfl_group_type,
                               SFL_FSET_TYPE sfl_group_fset,
                               MSFL_FSET_TYPE  msfl_group_fset,
                               std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                               const std::vector<sHMBLK*>& mblks,
                               const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                               cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows) 
{  
  for (int scale = 0; scale < sim.num_scales; scale++) {
    const auto& set_of_sfl_groups = sfl_group_fset->get_groups_of_scale(scale);
    for (auto sfl_group : set_of_sfl_groups) {
      build_msfls_from_sfl_group(msfls, scale, sfl_group,
                                 msfl_group_fset,
                                 child_sfl_to_msfl_map,
                                 mblks,
                                 child_ublk_to_mblk_map,
                                 windows);
    } //surfel_group
  }//scale
}

/*=================================================================================================
 * @fcn resolve_msfl_even_odd_clone_data
 * When we initialize msfls, we stash child clone surfel pointers in the even-odd data.
 * This function translates these pointers to the corresponding msfl pointer with offset
 * information encoded
 *================================================================================================*/
static VOID resolve_msfl_even_odd_clone_data(std::vector<sMSFL*> msfls,
                                             std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map)
{

  for (sMSFL* msfl : msfls) {
    if (msfl->is_even_or_odd()) {
      auto msfl_even_odd_data = msfl->even_odd_data();
      for (int s = 0; s < N_SFLS_PER_MSFL; s++) {
        if (msfl->is_valid_child_surfel(s) && msfl->is_even_or_odd()) {
          tCLONE_SURFEL<SFL_SDFLOAT_TYPE_TAG> child_clone_surfel;
          if (msfl_even_odd_data->m_clone_surfel[s].masked_ptr())
            {
              child_clone_surfel.set_masked_ptr(msfl_even_odd_data->m_clone_surfel[s].masked_ptr());
              SURFEL child_surfel = child_clone_surfel.clone_surfel();
              BOOLEAN has_no_v2s_weights = child_clone_surfel.has_no_v2s_weights();
              auto [msfl_id, offset] = child_sfl_to_msfl_map.at(child_surfel->id());
              auto mega_clone_surfel = msfls[msfl_id];
              msfl_even_odd_data->m_clone_surfel[s].set_clone_surfel(mega_clone_surfel);
              msfl_even_odd_data->m_clone_surfel[s].set_clone_surfel_offset(offset);
              if (has_no_v2s_weights) {
                msfl_even_odd_data->m_clone_surfel[s].set_has_no_v2s_weights();
              }
              cassert(msfl_even_odd_data->m_clone_surfel[s].clone_surfel() == mega_clone_surfel);
              cassert(msfl_even_odd_data->m_clone_surfel[s].get_offset_in_msfl() == offset);
              cassert(msfl_even_odd_data->m_clone_surfel[s].has_no_v2s_weights() == has_no_v2s_weights);
            }
        }
      }
    }
  }
}

/*=================================================================================================
 * @fcn build_msfls
 *================================================================================================*/
std::vector<sMSFL*> build_msfls(const std::vector<sHMBLK*>& mblks,
                                const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
                                std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
                                cDEVICE_MEAS_WINDOW_BUILDER_VEC& windows) 
{
  
  std::vector<sMSFL*> msfls;

  //We'll keep this simple and assign a MBLK_VOXEL_MASK for all mblks
  g_mblk_adjust_v2s_dist_masks.resize(mblks.size());

  for (int group_type = 0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {    
    if (group_type != MLRF_SURFEL_GROUP_TYPE) {
      SURFEL_FSET sfl_group_fset = g_surfel_groups[group_type];
      MSFL_FSET msfl_group_fset = g_msfl_groups[group_type];    
      //printf("MPI(%d) : Build  mega surfels for group %d ====================================== \n", my_proc_id, group_type);
      if (sfl_group_fset && sfl_group_fset->n_groups()) {
        LOG_MSG("GPU_INIT", LOG_FUNC).printf( "group type %d n_groups %d", group_type, sfl_group_fset->n_groups());
        
        build_msfls_from_sfl_fset(msfls, group_type,
                                  sfl_group_fset, msfl_group_fset,
                                  child_sfl_to_msfl_map,
                                  mblks,
                                  child_ublk_to_mblk_map,
                                  windows);
      }
    }
    else {
      //The current initialization scheme has MLRF surfels collected in a separate
      //FSET (g_mlrf_fset), not part of the array of regular surfel FSETs "g_surfel_fset"
      //Thus we have to write this special piece of logic to accomodate initialization of
      //MSFL MLRF groups. Unlike the CPU simulator, this initialization will also create
      //regular surfel and msfl groups for sliding mesh surfels, so that downstream logic
      //does not have to treat them as a special group as we do here.
      auto sfl_group_fset = &g_mlrf_surfel_fset;
      auto msfl_group_fset = &g_mlrf_msfl_fset;
      if (sfl_group_fset->n_groups()) {
        build_msfls_from_sfl_fset(msfls, group_type,
                                  sfl_group_fset, msfl_group_fset,
                                  child_sfl_to_msfl_map,
                                  mblks,
                                  child_ublk_to_mblk_map,
                                  windows);
      }      
    }
  } //group_type-loop

  //Build ghost mega surfels
  //printf("MPI(%d) : Build ghost mega surfels ====================================== \n", my_proc_id);
  MSFL_FSET mega_sfl_group_fset_ghost = g_msfl_groups[GHOST_SURFEL_GROUP_TYPE]; //Alternative to g_msfl_recv_fset so I can use the current functions based on this old group type, probably we need to remove it in the future and leave just one group for ghost surfels
  for( SCALE scale = 0; scale <sim.num_scales; scale++) {
    if(g_surfel_recv_fset.n_groups_of_scale(scale)>0) {
      for(sSURFEL_RECV_GROUP *const cpu_group : g_surfel_recv_fset.groups_of_scale(scale)) {
        assert(cpu_group);
        std::array<sSURFEL*, 64> sfls_to_pack{};
        size_t sfl_counter = 0;
        for(const sSURFEL_COMM_QUANTUM& quantum : cpu_group->quantums()) {
          sfls_to_pack[sfl_counter++] = quantum.m_surfel;
          //We need to create homogeneous surfels, at this point surfels are suppose to be sorted by even and odd for example
          const sSURFEL_COMM_QUANTUM& next_quantum = *(&quantum + 1);
          bool next_surfel_same_kind = (&quantum != &cpu_group->quantums().back()) ? 
            sMSFL_SORT_CATEGORIES::equiv(quantum.m_surfel, next_quantum.m_surfel) : false;
          if(sfl_counter == 64 || &quantum == &cpu_group->quantums().back() || !next_surfel_same_kind)
            {
              const size_t n_sfls_packed = sfl_counter;
              sfl_counter = 0;
              sMSFL* msfl = create_msfl(sfls_to_pack, n_sfls_packed, msfls.size(), scale, windows, true);
              for(int i = 0; i < n_sfls_packed; i++) {                
                auto sfl_id = sfls_to_pack[i]->id();
                // if (sfl_id == 17103) {
                //   printf("Proc %d, MSFL %d, sfl %d, child %d\n", my_proc_id, msfl->id(), sfl_id, i);
                // }                            
                child_sfl_to_msfl_map[sfl_id] = std::make_pair<STP_SHOB_ID, uINT8>(msfl->id(),i);
              }
              msfls.push_back(msfl);
              init_msfl_interactions(msfl, sfls_to_pack, n_sfls_packed,
                                     mblks, child_ublk_to_mblk_map);
              MSFL_GROUP mega_sfl_group = mega_sfl_group_fset_ghost->create_group(scale,
                                                                                  cpu_group->m_source_sp, // I know this is wrong but the dest_sp in this group shouldn't be used ever
                                                                                  FLOW_DYN_SURFEL_GROUP_SUPERTYPE, STP_FLOW_REALM);
              mega_sfl_group->add_shob_to_group(msfl);
            }
          
        }
      }
    }
  }

  GPU::initialize_msfl_group_ranges();

  resolve_msfl_even_odd_clone_data(msfls, child_sfl_to_msfl_map);

  GPU::init_batches(get_max_number_of_ublks_in_groups(), get_max_number_of_surfels_in_groups());

  prepare_s2s_s2v_data_on_host(msfls, child_sfl_to_msfl_map, GPU::msfl_batch_size());
  
  return msfls;
}

VOID set_nearblock_closest_surfels(std::vector<sHMBLK*>& mblks,
				   const std::vector<sMSFL*>& h_msfl_table,
                                   const std::unordered_map<STP_SHOB_ID, std::pair<STP_SHOB_ID, uINT8>>& child_sfl_to_msfl_map) {

  for (sHMBLK* h_mblk : mblks) {
    if (h_mblk->is_near_surface()) {
      for (int mega_v = 0; mega_v < N_VOXELS_64; mega_v++) {
        if (h_mblk->surf_geom_data()->closest_surfels[mega_v].is_valid()) {
          //Closest surfel was stashed in mblk nearblk-lb_data during initialization of the header,
          //which allowed for us to clear child ublk nearblk-lb_data
          //Use TAGGED_SURFEL::get_ptr instead of TAGGED_SURFEL::get_surfel, because the latter involves
          //masking operation specific to mega_surfels, and would mask out sSURFEL bits incorrectly
          sSURFEL* closest_surfel = (sSURFEL*) h_mblk->surf_geom_data()->closest_surfels[mega_v].get_ptr();
          h_mblk->surf_geom_data()->closest_surfels[mega_v].clear();          
          auto [parent_msfl_id, sfl_offset] = child_sfl_to_msfl_map.at(closest_surfel->id());
          auto parent_msfl = h_msfl_table[parent_msfl_id];

          h_mblk->surf_geom_data()->closest_surfels[mega_v].set_surfel(parent_msfl);
          h_mblk->surf_geom_data()->closest_surfels[mega_v].set_offset(sfl_offset);

          //sanity check
          cassert(h_mblk->surf_geom_data()->closest_surfels[mega_v].get_surfel() == parent_msfl);
          cassert(h_mblk->surf_geom_data()->closest_surfels[mega_v].get_offset() == sfl_offset);
        } else {
          h_mblk->surf_geom_data()->closest_surfels[mega_v].set_surfel(nullptr);
        }
      } //voxel-loop
    }
  } //ublk-loop
}

void set_vr_coarse_and_fine_info(const std::vector<sHMBLK*>& mblks,
                                 const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map)
{
  for(sHMBLK* mblk : mblks)
    if(mblk->is_vr_coarse())
      mblk->vr_coarse_data()->init();
  auto set_pointers = [&mblks, &child_ublk_to_mblk_map](sUBLK* fine_ublk) {
    auto [fine_mblk_id ,fine_mblk_offset ] = child_ublk_to_mblk_map.at(fine_ublk->id());
    HMBLK fine_mblk = mblks[fine_mblk_id];//TODO: use at()
    auto * mblk_vr_fine_data = fine_mblk->vr_fine_data();
    auto * ublk_vr_fine_data = fine_ublk->vr_fine_data();

    UBLK coarse_ublk = ublk_vr_fine_data->vr_coarse_ublk().ublk();

    auto [coarse_mblk_id ,coarse_mblk_offset ] = child_ublk_to_mblk_map.at(coarse_ublk->id());
    HMBLK coarse_mblk = mblks[coarse_mblk_id];//TODO: use at()
    auto * mblk_vr_coarse_data = coarse_mblk->vr_coarse_data();
    auto * ublk_vr_coarse_data = coarse_ublk->vr_coarse_data();

    mblk_vr_coarse_data->init( ublk_vr_coarse_data->vr_config(), coarse_mblk_offset );


    int ublk_vr_coarse_to_fine_idx = -1;
    ccDOTIMES(voxel, N_VOXELS_8) {
      if ( fine_ublk == ublk_vr_coarse_data->vr_fine_ublk(voxel).ublk() ) {
        ublk_vr_coarse_to_fine_idx = voxel;
        break;
      }
    }
    assert(ublk_vr_coarse_to_fine_idx != -1);

    int mblk_vr_coarse_to_fine_idx = coarse_mblk_offset*N_VOXELS_8 + ublk_vr_coarse_to_fine_idx;

    mblk_vr_coarse_data->set_vr_fine_ublk(fine_mblk, fine_mblk_offset, mblk_vr_coarse_to_fine_idx);
    mblk_vr_fine_data->init(coarse_mblk, coarse_mblk_offset, fine_mblk_offset);

    ccDOTIMES(i, N_CONNECT_MASK_BITS) {
      ::init_mblk(ublk_vr_fine_data->m_even_s2v_weights_mask[i], mblk_vr_fine_data->m_even_s2v_weights_mask[i], fine_mblk_offset);
    }
  };

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      set_pointers(fine_ublk);
    }
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    for (auto vrfine_ghost_type: nSHOB_CATEGORIES::VRFINE_GHOST_TYPES) {
      UBLK_FSET ublk_fset = g_ublk_groups[vrfine_ghost_type];
      if (ublk_fset->groups_per_scale()) {
        for (auto* group: ublk_fset->groups_of_scale(scale)) {
          for (auto* vr_fine = group->shob_ptrs(); vr_fine != nullptr; vr_fine = vr_fine ? vr_fine->m_next : nullptr) {
            set_pointers(vr_fine);
          }
        }
      }
    }
  }
}
    

size_t get_max_number_of_ublks_in_groups()
{
  size_t max = 0;

  for(size_t group_type=0; group_type < N_UBLK_GROUP_TYPES; group_type++) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_MBLK_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
        max = group->n_shob_ptrs() > max ? group->n_shob_ptrs() : max;
      }
    }
  }

  return max;
}

size_t get_max_number_of_surfels_in_groups()
{
  size_t max = 0;

  for(size_t group_type=0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {
    DO_SCALES_FINE_TO_COARSE(scale) {
      DO_MSFL_GROUPS_OF_SCALE_TYPE(group, scale, group_type) {
        max = group->n_shob_ptrs() > max ? group->n_shob_ptrs() : max;
      }
    }
  }

  return max;
}

template<typename SFL_TYPE_TAG>
bool tMSFL_SORT_CATEGORIES<SFL_TYPE_TAG>::comp(const tSURFEL<SFL_TYPE_TAG>* const_h_sfl1,
                                               const tSURFEL<SFL_TYPE_TAG>* const_h_sfl2) {

  auto h_sfl1 = const_cast<tSURFEL<SFL_TYPE_TAG>*>(const_h_sfl1);
  auto h_sfl2 = const_cast<tSURFEL<SFL_TYPE_TAG>*>(const_h_sfl2);
  assert(h_sfl1->scale() == h_sfl2->scale());

  if (const_h_sfl1->ref_frame_index() < const_h_sfl2->ref_frame_index()) { return true; }
  if (const_h_sfl1->ref_frame_index() > const_h_sfl2->ref_frame_index()) { return false; }
  
  auto dyn1 = h_sfl1->has_dynamics_data()? h_sfl1->dynamics_data() : nullptr;
  auto dyn2 = h_sfl2->has_dynamics_data()? h_sfl2->dynamics_data() : nullptr;

  if (dyn1 && !dyn2) { return true; }
  if (dyn2 && !dyn1) { return false; }

  if (dyn1 && dyn2) {
    if (dyn1->m_dynamics_type < dyn2->m_dynamics_type) { return true; }
    if (dyn1->m_dynamics_type > dyn2->m_dynamics_type) { return false; }

    //If we have identical dynamics types, still sort based on descriptor because
    //parameters might contain different reference frame indices and potentially other
    //differences
    if constexpr(!SFL_TYPE_TAG::is_msfl()) {
      uINT16 face1 = const_h_sfl1->m_face_index;
      uINT16 face2 = const_h_sfl2->m_face_index;
      bool backside1 = const_h_sfl1->is_backside() && !const_h_sfl1->is_isurfel();
      bool backside2 = const_h_sfl2->is_backside() && !const_h_sfl2->is_isurfel();
      auto pd1 = surface_physics_desc_from_face_index(static_cast<int>(face1), backside1, false);
      auto pd2 = surface_physics_desc_from_face_index(static_cast<int>(face2), backside2, false);    
      //Only bother sorting by face index if physics descriptors that these face indices map to
      //are different
      if (pd1 != pd2) {
        if (const_h_sfl1->m_face_index < const_h_sfl2->m_face_index) { return true; }
        if (const_h_sfl1->m_face_index > const_h_sfl2->m_face_index) { return false; }
      }
    }
  }

  if (h_sfl1->even_odd_mask() < h_sfl2->even_odd_mask()) { return true; }
  if (h_sfl1->even_odd_mask() > h_sfl2->even_odd_mask()) { return false; }  
  
  return false;
}

VOID sort_surfel_fset_for_msfl_build(SURFEL_FSET sfl_group_fset) {
  
  for (int scale = 0; scale < sim.num_scales; scale++) {

    const auto& set_of_sfl_groups = sfl_group_fset->get_groups_of_scale(scale);

    std::unordered_map<asINT32, std::vector<SURFEL>> dyn_type_to_sfl_vector_map;

    //First sort surfel groups by dynamics type into a standard vector
    //Unfortunately current groups don't use a standard list, so we can't
    //sort them directly
    int num_groups = 0;
    for (SURFEL_GROUP sfl_group : set_of_sfl_groups) {
      sSURFEL* h_sfl = sfl_group->shob_ptrs();      
      asINT32 n_shob_ptrs = sfl_group->n_shob_ptrs();

      LOG_MSG("GPU_INIT", LOG_FUNC).printf( "group number in sfl_group %d: sfl_group->n_shob_ptrs() = %d, n_shob_ptrs = %d", num_groups, sfl_group->n_shob_ptrs(), n_shob_ptrs);
      num_groups++;
      
      int surfel_nr = 0;
      dyn_type_to_sfl_vector_map.clear();
      while(h_sfl) {
        asINT32 dyn_type = h_sfl->dynamics_data()->m_dynamics_type;
        dyn_type_to_sfl_vector_map[dyn_type].push_back(h_sfl);
        auto next_sfl = h_sfl->m_next;
        sfl_group->remove_shob_from_group(h_sfl);
        //LOG_MSG("GPU_INIT", LOG_FUNC).printf( "while loop %d dyn type %d surfel id %d", surfel_nr, dyn_type, h_sfl->id());
        h_sfl = next_sfl;        
        surfel_nr++;
      }

      sfl_group->reset();
      surfel_nr = 0;

      for (auto& dyn_type_to_sfl_vector_pair : dyn_type_to_sfl_vector_map) {
        auto& sfl_dyn_type_vec = dyn_type_to_sfl_vector_pair.second;
        std::stable_sort(sfl_dyn_type_vec.begin(),
                         sfl_dyn_type_vec.end(),
                         sMSFL_SORT_CATEGORIES::comp);
        for (SURFEL sfl : sfl_dyn_type_vec) {
          sfl_group->add_shob_to_group(sfl);
        }
      }

      LOG_MSG("GPU_INIT", LOG_FUNC).printf( "n_shob_ptrs %d sfl_group->n_shob_ptrs() %d", n_shob_ptrs, sfl_group->n_shob_ptrs());
      assert(sfl_group->n_shob_ptrs() == n_shob_ptrs);
    }
  }
}

//This is almost a copy of the previous function as SURFEL_FSET and SURFEL_RECV_FSET have different interfaces...
VOID sort_ghost_surfel_fset_for_msfl_build(SURFEL_RECV_FSET sfl_recv_group_fset,
                                           SURFEL_SEND_FSET sfl_send_group_fset) {
  
  for (int scale = 0; scale < sim.num_scales; scale++)
    {
      const auto& set_of_recv_sfl_groups = sfl_recv_group_fset->get_groups_of_scale(scale);
      for (SURFEL_RECV_GROUP sfl_group : set_of_recv_sfl_groups) 
        {
          std::stable_sort(sfl_group->quantums().begin(),
                           sfl_group->quantums().end(),                         
                           [](const sSURFEL_COMM_QUANTUM& quantum1, const sSURFEL_COMM_QUANTUM& quantum2) -> bool
                           {
                             return  sMSFL_SORT_CATEGORIES::comp(quantum1.m_surfel, quantum2.m_surfel);
                           });    
        }

      const auto& set_of_send_sfl_groups = sfl_send_group_fset->get_groups_of_scale(scale);
      for (SURFEL_SEND_GROUP sfl_group : set_of_send_sfl_groups) 
        {
          std::stable_sort(sfl_group->quantums().begin(),
                           sfl_group->quantums().end(),                         
                           [](const sSURFEL_COMM_QUANTUM& quantum1, const sSURFEL_COMM_QUANTUM& quantum2) -> bool
                           {
                             return  sMSFL_SORT_CATEGORIES::comp(quantum1.m_surfel, quantum2.m_surfel);
                           });    
        }      
    }
}

VOID print_send_recv_fsets();

VOID sort_surfel_groups_for_msfl_build() {
  
  for (int group_type = 0; group_type < N_SURFEL_GROUP_TYPES; group_type++) {    

    SURFEL_FSET sfl_group_fset = g_surfel_groups[group_type];
    //printf("MPI(%d) sort_surfel_groups_by_dynamics_type() group type %d\n", my_proc_id, group_type);    
    if (sfl_group_fset && sfl_group_fset->n_groups()) {
      sort_surfel_fset_for_msfl_build(sfl_group_fset);
    }    

  } //group_type-loop

  sort_ghost_surfel_fset_for_msfl_build(&g_surfel_recv_fset,
                                        &g_surfel_send_fset);

  //print_send_recv_fsets();
}

/*==============================================================================
 * @fcn sHD_SLAB_ALLOCATOR::TAG::tag_from_h_ptr
 * Create tag by peeking at TAG::size() bytes ahead of h_ptr
 *============================================================================*/
const sHD_SLAB_ALLOCATOR::TAG* sHD_SLAB_ALLOCATOR::TAG::tag_from_h_ptr(void* h_ptr) {
  char* tag_start = (char*) h_ptr - TAG::size();
  const TAG* tag = reinterpret_cast<TAG*>(tag_start);
  cassert(tag->m_type < (int) HD_SLAB_ALLOCATOR_TYPES::N_TYPES);
  return tag;
}

/*==============================================================================
 * @fcn sHD_SLAB_ALLOCATOR::add_new_hd_slabs
 * Adds a chunk of memory on both host and device simulataneously which allows
 * for device memory counterpart to be referenced immediately with get_device_ptr
 *============================================================================*/
void sHD_SLAB_ALLOCATOR::add_new_hd_slabs() {
  m_pos_in_slab = 0;
  m_host_slabs.push_back(new (std::align_val_t(GPU::MINIMUM_CUDA_ALIGNMENT)) BUFFER_TYPE{0});
  //Add shadow device slab
  GPU::Ptr<BUFFER_TYPE> d_slab = GPU::malloc<BUFFER_TYPE>(1, "hd_slab_alloc");
  GPU::zero(d_slab, 1);
  m_device_slabs.push_back(d_slab.get());
}

sHD_SLAB_ALLOCATOR::~sHD_SLAB_ALLOCATOR() {
  for (int s = 0; s < m_host_slabs.size(); s++) {
    delete m_host_slabs[s];
    auto d_ptr_for_delete = GPU::Ptr<void>((void*) m_device_slabs[s]);
    GPU::free(d_ptr_for_delete, "hd_slab_alloc");
    m_host_slabs[s] = nullptr;    
    m_device_slabs[s] = nullptr;
  }
  m_pos_in_slab = 0;
  m_host_slabs.clear();
  m_device_slabs.clear();
}

/*==============================================================================
 * @fcn sHD_SLAB_ALLOCATOR::malloc
 * Aligned malloc implementation using pre-allocated slabs of memory
 * A TAG is inserted in every allocation to know the offset from the start of
 * the slab.
 *============================================================================*/
void* sHD_SLAB_ALLOCATOR::malloc(size_t n_bytes, size_t align) {

  if (n_bytes + align >= SLAB_SIZE) {
    msg_internal_error("Requested allocation %zu bytes with alignment %zu, is "
                       "greater than slab capacity %zu bytes\n", n_bytes, align, SLAB_SIZE);
  }
  
  if (m_host_slabs.empty()) { add_new_hd_slabs(); }

  auto get_aligned_start_pos = [&] () {
    return get_byte_aligned(m_pos_in_slab + TAG::size(), align);
  };

  size_t aligned_start_pos = get_aligned_start_pos();
    
  if (aligned_start_pos + n_bytes >= SLAB_SIZE) {
    add_new_hd_slabs();
    aligned_start_pos = get_aligned_start_pos();    
  }

  cassert(aligned_start_pos + n_bytes < SLAB_SIZE);

  //Write tag header just before aligned start position
  TAG tag(m_type, m_host_slabs.size() - 1, aligned_start_pos);
  size_t tag_pos = aligned_start_pos - TAG::size();
  
  memcpy(m_host_slabs.back()->data() + tag_pos, &tag, TAG::size());

  //New pos after write would be
  m_pos_in_slab = aligned_start_pos + n_bytes;
  return (m_host_slabs.back()->data() + aligned_start_pos);
}

GPU::Ptr<void> sHD_SLAB_ALLOCATOR::get_device_ptr_impl(void* h_ptr) {

  //Get tag, should be 8 bytes ahead of h_ptr
  const TAG* tag = TAG::tag_from_h_ptr(h_ptr);

  //Consistency checks
  cassert(tag->type() == m_type);
  cassert(tag->slab_index() < m_device_slabs.size());
  cassert(tag->slab_offset() < SLAB_SIZE);
  
  char* h_slab_mem_start = m_host_slabs[tag->slab_index()]->data();
  void* tag_based_h_ptr = (void*) (h_slab_mem_start + tag->slab_offset());
  
  if (tag_based_h_ptr != h_ptr) {
    msg_internal_error("sHD_SLAB_ALLOCATOR:: host address computed from TAG offset %p is not consistent "
                       "with input host pointer %p. This likely means that the TAG is corrupt, or that this "
                       "host pointer does not reference memory allocated with this allocator.",
                       tag_based_h_ptr, h_ptr);
  }

  //Since the device slabs are a shadow of what is contained in host slabs
  //we get the corresponding device location add an offset to the start of device slab
  char* d_slab_mem_start = m_device_slabs[tag->slab_index()]->data();
  return GPU::Ptr<void>(d_slab_mem_start + tag->slab_offset());
}

std::array<sHD_SLAB_ALLOCATOR*, (int) HD_SLAB_ALLOCATOR_TYPES::N_TYPES>
sHD_SLAB_ALLOCATOR_FACTORY::m_hd_slab_allocators = {nullptr};
  
VOID sHD_SLAB_ALLOCATOR_FACTORY::delete_allocator(HD_SLAB_ALLOCATOR_TYPES type) {
  int type_index = static_cast<int>(type);
  auto p = m_hd_slab_allocators[type_index];
  if (p) {
    delete p;
    m_hd_slab_allocators[type_index] = nullptr;
  }
}
  
sHD_SLAB_ALLOCATOR* sHD_SLAB_ALLOCATOR_FACTORY::get_allocator(HD_SLAB_ALLOCATOR_TYPES type) {
  int type_index = static_cast<int>(type);
  auto p = m_hd_slab_allocators[type_index];
  if (!p) (m_hd_slab_allocators[type_index] = new sHD_SLAB_ALLOCATOR(type));
  return m_hd_slab_allocators[type_index];
}

#endif // ifdef BUILD_GPU
