/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_solver_data.h"
#include "ublk.h"
#include "sim.h"
#include "surfel.h"
#include "sampling_surfel.h"
#include "surfel_table.h"
#include "particle_sim_info.h"
#include "comm_utils.h"
#include "particle_sim.h"
#include SRI_H

#define ENABLE_SURFACE_TENSION_MODEL_SURFEL_COMM

extern sSIMUTILS_PARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE g_terminal_velocity_table;

const SRI_VARIABLE_TYPE sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES::m_all_supported_sri_fluid_vartypes[sPARTICLE_FLUID_MEAS_DCACHE_VARTYPES::NUM_SUPPORTED_VARTYPES] = {
  SRI_VARIABLE_PRTCL_XFORCE, //The components of vector types must be consecutive in this table and all entries match the eSUPPORTED_VAR_TYPES enum.
  SRI_VARIABLE_PRTCL_YFORCE,
  SRI_VARIABLE_PRTCL_ZFORCE,
  SRI_VARIABLE_PRTCL_EVAPORATION_RATE,
  SRI_VARIABLE_PRTCL_NUMBER,
  SRI_VARIABLE_PRTCL_MEAN_DENSITY,
  SRI_VARIABLE_PRTCL_MEAN_DIAMETER,
  SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA,
  SRI_VARIABLE_PRTCL_MEAN_VOLUME,
  SRI_VARIABLE_PRTCL_MEAN_MASS,
  SRI_VARIABLE_PRTCL_MEAN_XVEL,
  SRI_VARIABLE_PRTCL_MEAN_YVEL,
  SRI_VARIABLE_PRTCL_MEAN_ZVEL,
  SRI_VARIABLE_PRTCL_MEAN_VEL_MAG,
  SRI_VARIABLE_PRTCL_MEAN_TEMP,
  SRI_VARIABLE_PRTCL_COMPOSITION_1,
  SRI_VARIABLE_PRTCL_COMPOSITION_2,
  SRI_VARIABLE_PRTCL_COMPOSITION_3,
  };


const char* parcel_dynamics_type_names[NUM_DYNAMICS_TYPES] = { "in_fluid",
                                                               "on_surface",
                                                               "reentrained_into_ghostblk",
                                                               "just_emitted",
                                                               "just_reentrained",
                                                               "collided_with_ghost_mlrf_surfel_from_fluid",
                                                               "collided_with_ghost_mlrf_surfel_from_film",
                                                               "to_be_deleted"};

sPARTICLE_VAR sPARCEL_STATE::mass_fraction(int species_index) {
  if(species_index)
    return m_mass_fractions[species_index - 1];
  sPARTICLE_VAR mass_fraction_0 = 1.0;
  ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
    mass_fraction_0 -= m_mass_fractions[i];
  }
  return mass_fraction_0;
}


VOID sSURFEL_PARTICLE_DATA::init(DGF_SURFEL_DESC surfel_desc) {
  s.accretion_volume = 0.0;
  intersecting_surfels = nullptr;
  surface_parcel_list = xnew sPARCEL_LIST;
  nearby_surfels = xnew sFILM_STENCIL_SURFEL_VECTOR;
  nearby_areas = xnew sFILM_STENCIL_WEIGHTS_VECTOR;
  m_surfel_is_wipeable = FALSE;
  s.clear_reentrainment_meas_accumulators();
  s.clear_meas_accumulators(true);
  s.clear_film_accumulators();
};

VOID sSURFEL_PARTICLE_DATA::accumulate_film_data(sdFLOAT * &recv_buffer) {

  SURFEL_FILM_COMM_ELEMENT field = reinterpret_cast<SURFEL_FILM_COMM_ELEMENT>(recv_buffer);
  //This function accumulates film information received from remote ghost surfels
  //to the local dynamics surfels before the film ODE is timesteped.

  //Local and remote film measurements are accumulated here:
  accumulate(s.num, field->num, NUM_PARCEL_STATES);
  accumulate(s.mass, field->mass, NUM_PARCEL_STATES);
  accumulate(s.sum_density, field->sum_density, NUM_PARCEL_STATES);
  accumulate(s.sum_diameter, field->sum_diameter, NUM_PARCEL_STATES);
  accumulate(s.sum_particle_impulse, field->sum_particle_impulse, N_SPACE_DIMS);
  accumulate(s.reentrained_film_thickness, field->reentrained_film_thickness);

  //Remote and local film viscosity are mixed here using a mass weighted average:
  //This could potentially leave artifacts on the boundary due to order dependence
  //if there is more than one remote source. Needs to be investigated.
  if(s.film_mass > 0.0 ) {
    s.film_viscosity *= s.film_mass;
    s.film_viscosity += field->film_viscosity * field->film_mass;
    s.film_viscosity /= (s.film_mass + field->film_mass);
  } else {
    s.film_viscosity = field->film_viscosity;
  }
  //Local and remote values of other film quantities are mixed here:
  accumulate(s.film_thickness, field->film_thickness);
  accumulate(s.accretion_volume, field->accretion_volume);
  accumulate(s.film_momentum, field->film_momentum, 3);
  if(field->film_mass + s.film_mass > 0.0 ) {
    ccDOTIMES(i, 3) {
      s.film_acceleration[i] = (s.film_acceleration[i] * s.film_mass + field->film_acceleration[i] * field->film_mass) /
        (s.film_mass + field->film_mass);
    }
  } else {
    vzero(s.film_acceleration);
  }

  s.film_dynamics_timescale = MIN(s.film_dynamics_timescale, field->film_dynamics_timescale);

  accumulate(s.film_mass, field->film_mass);
  if(s.is_wetted == 0.0) {
    s.is_wetted = field->is_wetted;
  }

  //for surface tension model
  //use the max Fuchs number
  if (field->Fuchs_number > s.Fuchs_number )
    s.Fuchs_number = field->Fuchs_number;

  //check with alex about this one
  if( s.the_rivulet_edge_is_here == 0 )
    s.the_rivulet_edge_is_here = field->the_rivulet_edge_is_here;

  //check with alex about this one
  if (s.Heaviside_function == 0 )
    s.Heaviside_function = field->Heaviside_function;

#if 0
  ccDOTIMES(i,N_SPACE_DIMS) {
    s.surface_tension[i] += field->surface_tension[i];
    s.corrected_surface_tension[i] += field->corrected_surface_tension[i];
  }
#endif

  //take the maximum
  if( field->effective_capillari_length > s.effective_capillari_length )
    s.effective_capillari_length = field->effective_capillari_length;

  //take the max
  if (field->deGennes_length > s.deGennes_length )
    s.deGennes_length = field->deGennes_length;

  if (field->t_star > s.t_star)
    s.t_star = field->t_star;

  if( field->t_st_life > s.t_st_life )
    s.t_st_life = field->t_st_life;

  recv_buffer += sizeof(sSURFEL_FILM_COMM_ELEMENT)/sizeof(sdFLOAT);
}


VOID sSURFEL_PARTICLE_DATA::fill_film_data(sdFLOAT * &send_buffer) {
  //This copies surfel p_data needed by film solver from ghost surfels to send buffers
  //which are sent to remote SPs and accumulated to dynamics surfels
  SURFEL_FILM_COMM_ELEMENT field = reinterpret_cast<SURFEL_FILM_COMM_ELEMENT>(send_buffer);
  copy(field->num,  s.num, NUM_PARCEL_STATES);
  copy(field->mass, s.mass, NUM_PARCEL_STATES);
  copy(field->sum_density, s.sum_density, NUM_PARCEL_STATES);
  copy(field->sum_diameter, s.sum_diameter, NUM_PARCEL_STATES);
  copy(field->sum_particle_impulse, s.sum_particle_impulse, N_SPACE_DIMS);
  copy(&field->film_thickness, &s.film_thickness);
  copy(&field->accretion_volume, &s.accretion_volume);
  copy(&field->reentrained_film_thickness, &s.reentrained_film_thickness);
  copy(field->film_momentum, s.film_momentum, N_SPACE_DIMS);
  copy(field->film_acceleration, s.film_acceleration, N_SPACE_DIMS);
  copy(&field->film_dynamics_timescale, &s.film_dynamics_timescale);
  copy(&field->film_mass, &s.film_mass);
  copy(&field->film_viscosity, &s.film_viscosity);
  //needed for surface tension model
  copy(&field->the_rivulet_edge_is_here, &s.the_rivulet_edge_is_here);
  copy(&field->Heaviside_function, &s.Heaviside_function);
  copy(field->surface_tension, s.surface_tension, N_SPACE_DIMS);
  copy(&field->Fuchs_number, &s.Fuchs_number);
  copy(&field->is_wetted, &s.is_wetted);
  copy(field->corrected_surface_tension, s.corrected_surface_tension, N_SPACE_DIMS);
  copy(&field->effective_capillari_length, &s.effective_capillari_length);
  copy(&field->deGennes_length, &s.deGennes_length);
  copy(&field->t_star, &s.t_star);
  copy(&field->t_st_life, &s.t_st_life);
  send_buffer += sizeof(sSURFEL_FILM_COMM_ELEMENT)/sizeof(sdFLOAT);
}

VOID sSURFEL_PARTICLE_DATA::accumulate_solid_parcel_data(sdFLOAT * &recv_buffer) {
  //This function is called when film solver is not on.
  SURFEL_SOLID_PARCEL_COMM_ELEMENT field = reinterpret_cast<SURFEL_SOLID_PARCEL_COMM_ELEMENT>(recv_buffer);
  accumulate(s.accretion_volume, field->accretion_volume);
  accumulate(s.num, field->num, NUM_PARCEL_STATES);
  accumulate(s.mass, field->mass, NUM_PARCEL_STATES);
  accumulate(s.sum_density, field->sum_density, NUM_PARCEL_STATES);
  accumulate(s.sum_diameter, field->sum_diameter, NUM_PARCEL_STATES);
  accumulate(s.sum_particle_impulse, field->sum_particle_impulse, N_SPACE_DIMS);
  recv_buffer += sizeof(sSURFEL_SOLID_PARCEL_COMM_ELEMENT) / sizeof(sdFLOAT);
}

VOID sSURFEL_PARTICLE_DATA::fill_solid_parcel_data(sdFLOAT * &send_buffer) {
  //This copies surfel p_data needed by solid parcel from ghost surfels to send buffers
  //which are sent to remote SPs and accumulated to dynamics surfels
  SURFEL_SOLID_PARCEL_COMM_ELEMENT field = reinterpret_cast<SURFEL_SOLID_PARCEL_COMM_ELEMENT>(send_buffer);
  copy(&field->accretion_volume, &s.accretion_volume);
  copy(field->num,  s.num, NUM_PARCEL_STATES);
  copy(field->mass, s.mass, NUM_PARCEL_STATES);
  copy(field->sum_density, s.sum_density, NUM_PARCEL_STATES);
  copy(field->sum_diameter, s.sum_diameter, NUM_PARCEL_STATES);
  copy(field->sum_particle_impulse, s.sum_particle_impulse, N_SPACE_DIMS);
  send_buffer += sizeof(sSURFEL_SOLID_PARCEL_COMM_ELEMENT) / sizeof(sdFLOAT);
}

uINT64 sSURFEL_PARTICLE_DATA::ckpt_len() {
  uINT64 len = 0;
  len += sizeof(sSURFEL_PARTICLE_DATA::sFIXED_SIZE_SURFEL_PARTICLE_DATA);
  if (this->surface_parcel_list !=  nullptr) {
    this->surface_parcel_list->reset();
    while(!this->surface_parcel_list->exhausted()) {
      PARCEL_STATE parcel = this->surface_parcel_list->data();
      len += parcel->ckpt_len();
      this->surface_parcel_list->next();
    }
  }
  return len;
}

VOID sSURFEL_PARTICLE_DATA::write_ckpt(){
  this->s.num_parcels = 0;
  if (this->surface_parcel_list !=  nullptr)
    this->s.num_parcels = this->surface_parcel_list->size();

  write_ckpt_lgi(&this->s,sizeof(sSURFEL_PARTICLE_DATA::sFIXED_SIZE_SURFEL_PARTICLE_DATA));
  if (this->surface_parcel_list !=  NULL ) {
    this->surface_parcel_list->reset();
    while(!this->surface_parcel_list->exhausted()) {
      PARCEL_STATE parcel = this->surface_parcel_list->data();
      parcel->write_ckpt();
      this->surface_parcel_list->next();
    }
  }
}

VOID sSURFEL_PARTICLE_DATA::write_ckpt(sCKPT_BUFFER& pio_buffer){
  this->s.num_parcels = 0;
  if (this->surface_parcel_list !=  nullptr)
    this->s.num_parcels = this->surface_parcel_list->size();

  pio_buffer.write(&this->s);
  
  if (this->surface_parcel_list !=  NULL ) {
    this->surface_parcel_list->reset();
    while(!this->surface_parcel_list->exhausted()) {
      PARCEL_STATE parcel = this->surface_parcel_list->data();
      pio_buffer.write(parcel);
      this->surface_parcel_list->next();
    }
  }
}

VOID sSURFEL_PARTICLE_DATA::read_ckpt() {
  read_lgi(this->s);
  if (this->surface_parcel_list ==  nullptr)
    this->surface_parcel_list = xnew sPARCEL_LIST;
  ccDOTIMES(particle_num, this->s.num_parcels) {
    PARCEL_STATE parcel = xnew sPARCEL_STATE;
    parcel->read_ckpt();
    this->surface_parcel_list->add_to_end(parcel);
  }
}

extern sPARTICLE_VAR g_particle_lattice_time_correction;
extern sPARTICLE_VAR g_particle_one_over_lattice_time_correction;
extern sPARTICLE_SIM_INFO g_particle_sim_info;
sPARCEL_STATE::sPARCEL_STATE(sINT32 new_id,
                             auINT32 new_creation_timestep,
                             auINT32 new_originating_sp,
                             auINT32 new_emitter_id,
                             sPARTICLE_VAR new_num_particles,
                             auINT32 new_reflection_count,
                             eDYNAMICS_TYPE new_dynamics_type,
                             BOOLEAN new_particles_may_breakup,
                             sPARTICLE_VAR new_y_breakup,
                             sPARTICLE_VAR new_dydt,
                             auINT32 new_no_drag_steps_number,
                             sPARTICLE_VAR new_density,
                             sPARTICLE_VAR new_kinematic_viscosity,
                             sPARTICLE_VAR new_surface_tension,
                             sPARTICLE_VAR new_diameter,
                             sPARTICLE_VAR new_position[N_SPACE_DIMS],
                             sPARTICLE_VAR new_velocity[N_SPACE_DIMS],
                             asINT32 new_lrf_index)
{
  id = new_id;
  creation_timestep = new_creation_timestep;
  //last_update_timestep = new_creation_timestep - 1; //This line has been changed to the following for PR46501.
  last_update_timestep = new_creation_timestep;
  last_buffer_index = 0;

  originating_sp = new_originating_sp;
  emitter_id = new_emitter_id;
  num_particles = new_num_particles;
  reflection_count = new_reflection_count;

  dynamics_type = new_dynamics_type;
  particles_may_breakup = new_particles_may_breakup;

  //These are used exclusivly by the breakup model
  ccDOTIMES(step, NUM_PREVIOUS_STORED_TIMESTEPS) {
    y_breakup[step] = new_y_breakup;
    dydt[step] = new_dydt;
  }

#ifdef COMPILE_SURFACE_TENSION_MODEL
  ccDOTIMES(step, NUM_PREVIOUS_STORED_TIMESTEPS) {
    vzero(v_st[step]);
    vzero(r_st[step]);
    time_st[step] = 0.0;
  }
#endif

  no_drag_steps_number = new_no_drag_steps_number;

  rho = new_density;
  kinematic_viscosity = new_kinematic_viscosity;
  temperature = g_initial_particle_temperature;
  ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
    m_mass_fractions[i] = g_initial_particle_mass_fractions[i + 1]; //the first mass fraction is not stored and contrained by the others.
  }
  surface_tension = new_surface_tension;

  d = new_diameter;
  //We must guarentee that new parcels are added initially added to the correct container.  For emitters, this is true
  //because we search the emitters octree for the correct container.  For child particles created from splash, special care
  //is required.
  ccDOTIMES(step, NUM_PREVIOUS_STORED_TIMESTEPS) {
    vcopy(x[step], new_position);
    vcopy(v[step], new_velocity);
  }

  m_updated_container_this_timestep = false;

  parcel_mass = d * d * d * M_PI/6.0 * rho * num_particles;
  unevaporated_mass = parcel_mass;
  

  vcopy(last_measured_x, x[0]);
  vcopy(last_measured_v, v[0]);

  last_measured_t = g_timescale.time_flow();
  parcel_is_measurable_for_trajectory_window = 0;
  visited_trajectory_windows = 0;

  first_measured_ts = -1;
  order_in_measured_ts = -1;
  aggl_applied = 0;
  lrf_index = new_lrf_index;

  m_home_shob_id = INVALID_SHOB_ID;
  m_home_voxel = -1;
}

sPARCEL_STATE::sPARCEL_STATE(const sPARCEL_STATE &parcel) {

  memcpy(this, &parcel, sizeof(sPARCEL_STATE));

#if 0

  id = g_particle_sim_info.emitters[parcel.emitter_id]->get_next_parcel_id();
  creation_timestep = g_timescale.time_flow();
  last_update_timestep = g_timescale.time_flow();
  last_buffer_index = 0;

  originating_sp   = my_proc_id;
  emitter_id       = parcel.emitter_id;
  num_particles    = parcel.num_particles;
  reflection_count = parcel.reflection_count;

  dynamics_type = parcel.dynamics_type;
  particles_may_breakup = parcel.particles_may_breakup;

  no_drag_steps_number = parcel.no_drag_steps_number;

  rho = parcel.rho;
  kinematic_viscosity = parcel.kinematic_viscosity;
  temperature = parcel.temperature;

  ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
    m_mass_fractions[i] = parcel.mass_fractions[i];
  }

  unevaporated_mass = parcel.unevaporated_mass;
  surface_tension = parcel.surface_tension;

  d = parcel.d;

  ccDOTIMES(i, NUM_PREVIOUS_STORED_TIMESTEPS) {
    asINT32 timestep_index = this->relative_timestep_to_state_index(i);
    asINT32 source_timestep_index = parcel.relative_timestep_to_state_index(i);
    vcopy(x[timestep_index], parcel.x[source_timestep_index]);
    vcopy(v[timestep_index], parcel.v[source_timestep_index]);
  }

  parcel_mass = d * d * d * M_PI/6.0 * rho * num_particles;

  //for measurements
  vcopy(last_measured_x, x[0]);
  vcopy(last_measured_v, v[0]);

  last_measured_t = g_timescale.time_flow();
  parcel_is_measurable_for_trajectory_window = 0;
  visited_trajectory_windows = 0;

  first_measured_sp = -1;
  first_measured_ts = -1;
  order_in_measured_ts = -1;
  aggl_applied = 0;
  lrf_index = parcel.lrf_index;
  m_updated_container_this_timestep = false;
  m_home_voxel = -1;
  m_home_shob_id = INVALID_SHOB_ID;
#endif
}

VOID sPARCEL_STATE::fill_send_buffer(std::vector<unsigned char> &send_buffer) {
#ifdef PARTICLE_DEBUG
  if(PARTICLE_DEBUG_CONDITION(this)) {
    msg_print("Packing parcel %d at timestep %ld for home_shob %d.\n",
              this->id,
              g_timescale.m_time,
              this->m_home_shob_id);
  }
#endif

  pack(send_buffer, x, NUM_PREVIOUS_STORED_TIMESTEPS);
  pack(send_buffer, v, NUM_PREVIOUS_STORED_TIMESTEPS);
  //added for dynamics trajectory decimation
  pack(send_buffer, &parcel_is_measurable_for_trajectory_window);
  pack(send_buffer, last_measured_x);
  pack(send_buffer, last_measured_v);
  pack(send_buffer, &last_measured_t);
  pack(send_buffer, &visited_trajectory_windows);
  pack(send_buffer, &first_measured_sp);
  pack(send_buffer, &first_measured_ts);

  pack(send_buffer, &order_in_measured_ts);
  pack(send_buffer, &lrf_index);
  pack(send_buffer, &aggl_applied);

  //for surface tension model
#ifdef COMPILE_SURFACE_TENSION_MODEL
  memcpy(this->r_st, parcel->r_st, NUM_PREVIOUS_STORED_TIMESTEPS * N_SPACE_DIMS * sizeof(sPARTICLE_VAR));
  memcpy(this->v_st, parcel->v_st, NUM_PREVIOUS_STORED_TIMESTEPS * N_SPACE_DIMS * sizeof(sPARTICLE_VAR));
  memcpy(this->time_st, parcel->time_st, NUM_PREVIOUS_STORED_TIMESTEPS * sizeof(sPARTICLE_VAR));
  this->surface_tension_on = parcel->surface_tension_on;
#endif

  pack(send_buffer, &d);
  pack(send_buffer, &rho);
  pack(send_buffer, &surface_tension);
  pack(send_buffer, &kinematic_viscosity);
  pack(send_buffer, &temperature);
  ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
    pack(send_buffer, m_mass_fractions + i);
  }

  pack(send_buffer, &unevaporated_mass);
  pack(send_buffer, &parcel_mass);
  pack(send_buffer, &num_particles);
  //this->terminal_velocity_mag = parcel->terminal_velocity_mag;
  pack(send_buffer, &particles_may_breakup);
  pack(send_buffer, &no_drag_steps_number);
  //this->interacted_with_wiper = parcel->interacted_with_wiper;
  pack(send_buffer, &y_breakup);
  pack(send_buffer, &dydt);
  pack(send_buffer, &dynamics_type);
  pack(send_buffer, &creation_timestep);
  pack(send_buffer, &last_update_timestep);
  pack(send_buffer, &last_buffer_index);
  pack(send_buffer, &id);
  pack(send_buffer, &originating_sp);
  pack(send_buffer, &emitter_id);
  pack(send_buffer, &m_home_shob_id);
  pack(send_buffer, &m_home_voxel);
  pack(send_buffer, &m_updated_container_this_timestep);
#ifdef TO_BE_DONE
  this->m_home_surfel_id = parcel->m_home_surfel_id;
#endif
}

VOID sPARCEL_STATE::init(std::vector<unsigned char>::iterator &recv_buffer) {

  unpack(recv_buffer, x, NUM_PREVIOUS_STORED_TIMESTEPS);
  unpack(recv_buffer, v, NUM_PREVIOUS_STORED_TIMESTEPS);
  //added for dynamics trajectory decimation
  unpack(recv_buffer, &parcel_is_measurable_for_trajectory_window);
  unpack(recv_buffer, last_measured_x);
  unpack(recv_buffer, last_measured_v);
  unpack(recv_buffer, &last_measured_t);
  unpack(recv_buffer, &visited_trajectory_windows);
  unpack(recv_buffer, &first_measured_sp);
  unpack(recv_buffer, &first_measured_ts);

  unpack(recv_buffer, &order_in_measured_ts);
  unpack(recv_buffer, &lrf_index);
  unpack(recv_buffer, &aggl_applied);

  //for surface tension model
#ifdef COMPILE_SURFACE_TENSION_MODEL
  memcpy(this->r_st, parcel->r_st, NUM_PREVIOUS_STORED_TIMESTEPS * N_SPACE_DIMS * sizeof(sPARTICLE_VAR));
  memcpy(this->v_st, parcel->v_st, NUM_PREVIOUS_STORED_TIMESTEPS * N_SPACE_DIMS * sizeof(sPARTICLE_VAR));
  memcpy(this->time_st, parcel->time_st, NUM_PREVIOUS_STORED_TIMESTEPS * sizeof(sPARTICLE_VAR));
  this->surface_tension_on = parcel->surface_tension_on;
#endif

  unpack(recv_buffer, &d);
  unpack(recv_buffer, &rho);
  unpack(recv_buffer, &surface_tension);
  unpack(recv_buffer, &kinematic_viscosity);
  unpack(recv_buffer, &temperature);
  ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
    unpack(recv_buffer, m_mass_fractions + i);
  }

  unpack(recv_buffer, &unevaporated_mass);
  unpack(recv_buffer, &parcel_mass);
  unpack(recv_buffer, &num_particles);
  //this->terminal_velocity_mag = parcel->terminal_velocity_mag;
  unpack(recv_buffer, &particles_may_breakup);
  unpack(recv_buffer, &no_drag_steps_number);
  //this->interacted_with_wiper = parcel->interacted_with_wiper;
  unpack(recv_buffer, &y_breakup);
  unpack(recv_buffer, &dydt);
  unpack(recv_buffer, &dynamics_type);
  unpack(recv_buffer, &creation_timestep);
  unpack(recv_buffer, &last_update_timestep);
  unpack(recv_buffer, &last_buffer_index);
  unpack(recv_buffer, &id);
  unpack(recv_buffer, &originating_sp);
  unpack(recv_buffer, &emitter_id);
  unpack(recv_buffer, &m_home_shob_id);
  unpack(recv_buffer, &m_home_voxel);
  unpack(recv_buffer, &m_updated_container_this_timestep);
#ifdef TO_BE_DONE
  this->m_home_surfel_id = parcel->m_home_surfel_id;
#endif
}

template<>
uINT64 sUBLK_PARTICLE_DATA::ckpt_len() {

  uINT64 len = 0;
  len += sizeof(sUBLK_PARTICLE_DATA::sSTATIC_PDATA);
  ccDOTIMES(voxel, N_VOXELS) {
    if (this->fluid_parcel_list[voxel] !=  NULL ) {
      this->fluid_parcel_list[voxel]->reset();
      while(!this->fluid_parcel_list[voxel]->exhausted()) {
        PARCEL_STATE parcel = this->fluid_parcel_list[voxel]->data();
        if (parcel->m_home_voxel == voxel) {
          len += parcel->ckpt_len();
        }
        this->fluid_parcel_list[voxel]->next();
      }
    }
  }
  return len;
}

template <>
VOID sUBLK_PARTICLE_DATA::write_ckpt() {

  //store the number of parcel states to be expected in the checkpoint data for each voxel
  ccDOTIMES(voxel, N_VOXELS) {
    if ( this->fluid_parcel_list[voxel] !=  NULL ) {
      this->fluid_parcel_list[voxel]->reset();
      asINT32 num_parcels_written = 0;
      while(!this->fluid_parcel_list[voxel]->exhausted()) {
        PARCEL_STATE parcel = this->fluid_parcel_list[voxel]->data();
        if (parcel->m_home_voxel != voxel) {
          msg_warn("Home voxel for Parcel %d should be %d but found %d \
                   Dynamics type %d Location [%e %e %e] [%e %e %e] ", parcel->id, voxel,
                   parcel->m_home_voxel, parcel->dynamics_type,
                   parcel->x[0][0], parcel->x[0][1], parcel->x[0][2],
                   parcel->x[1][0], parcel->x[1][1], parcel->x[1][2]);
        } else {
          num_parcels_written++;
        }
        this->fluid_parcel_list[voxel]->next();
      }
      this->s.num_parcels[voxel] = num_parcels_written;
    }
  }

  //write the static part of the datablock
  write_ckpt_lgi(&this->s,sizeof(sUBLK_PARTICLE_DATA::sSTATIC_PDATA));

  //call write_ckpt for each fluid parcel;
  ccDOTIMES(voxel, N_VOXELS) {
    if ( this->fluid_parcel_list[voxel] !=  NULL ) {
      this->fluid_parcel_list[voxel]->reset();
      while(!this->fluid_parcel_list[voxel]->exhausted()) {
        PARCEL_STATE parcel = this->fluid_parcel_list[voxel]->data();
        if (parcel->m_home_voxel == voxel) {
          parcel->write_ckpt();
        }
        this->fluid_parcel_list[voxel]->next();
      }
    }
  }
}

template <>
VOID sUBLK_PARTICLE_DATA::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff) {

  //store the number of parcel states to be expected in the checkpoint data for each voxel
  ccDOTIMES(voxel, N_VOXELS) {
    if ( this->fluid_parcel_list[voxel] !=  NULL ) {
      this->fluid_parcel_list[voxel]->reset();
      asINT32 num_parcels_written = 0;
      while(!this->fluid_parcel_list[voxel]->exhausted()) {
        PARCEL_STATE parcel = this->fluid_parcel_list[voxel]->data();
        if (parcel->m_home_voxel != voxel) {
          msg_warn("Home voxel for Parcel %d should be %d but found %d \
                   Dynamics type %d Location [%e %e %e] [%e %e %e] ", parcel->id, voxel,
                   parcel->m_home_voxel, parcel->dynamics_type,
                   parcel->x[0][0], parcel->x[0][1], parcel->x[0][2],
                   parcel->x[1][0], parcel->x[1][1], parcel->x[1][2]);
        } else {
          num_parcels_written++;
        }
        this->fluid_parcel_list[voxel]->next();
      }
      this->s.num_parcels[voxel] = num_parcels_written;
    }
  }

  //write the static part of the datablock
  pio_ckpt_buff.write(&this->s,sizeof(sUBLK_PARTICLE_DATA::sSTATIC_PDATA));

  //call write_ckpt for each fluid parcel;
  ccDOTIMES(voxel, N_VOXELS) {
    if ( this->fluid_parcel_list[voxel] !=  NULL ) {
      this->fluid_parcel_list[voxel]->reset();
      while(!this->fluid_parcel_list[voxel]->exhausted()) {
        PARCEL_STATE parcel = this->fluid_parcel_list[voxel]->data();
        if (parcel->m_home_voxel == voxel) {
          pio_ckpt_buff.write(parcel);
        }
        this->fluid_parcel_list[voxel]->next();
      }
    }
  }
}

template <>
VOID sUBLK_PARTICLE_DATA::read_ckpt() {
  //read a static part of the datablock
  read_lgi(this->s); //,sizeof(sUBLK_PARTICLE_DATA::sSTATIC_PDATA));
  //for each voxel, allocate and add the right number of particles to each voxels list
  ccDOTIMES(voxel, N_VOXELS) {
    if(this->s.num_parcels[voxel] > 0 ) {
      if(this->fluid_parcel_list[voxel] == NULL) {
        this->fluid_parcel_list[voxel] = xnew sPARCEL_LIST;
      }
      ccDOTIMES(particle_num, this->s.num_parcels[voxel]) {
        PARCEL_STATE parcel = xnew sPARCEL_STATE;
        parcel->read_ckpt();
        if (parcel->m_home_voxel != voxel) {
          msg_warn("Home voxel for Parcel %d should be %d but found %d", parcel->id,
                   voxel, parcel->m_home_voxel);
        } else {
          this->fluid_parcel_list[voxel]->add_to_end(parcel);
        }
      }
    }
  }
}


#if 0
template <typename T>
VOID tNEARBLK_PARTICLE_DATA<T>::write_ckpt() {
}

template <typename T>
VOID tNEARBLK_PARTICLE_DATA<T>::read_ckpt() {
}
#endif

VOID sPARCEL_STATE::write_ckpt() {
  write_ckpt_lgi(*this);
}

VOID sPARCEL_STATE::read_ckpt() {
  read_lgi(*this);
}

VOID sSURFEL_PARTICLE_DATA::sFIXED_SIZE_SURFEL_PARTICLE_DATA::clear_meas_accumulators(bool force) {

  //accretion_volume = 0.0;

  //Since we only measure on accumulation timesteps, don't clear the
  //accumulators unless measurements were just recorded.
  //  if(particle_sim.is_timestep_for_accumulation() || force) {
  ccDOTIMES(i, NUM_PARCEL_STATES) {
    //Since we only measure on accumulation timesteps, dont clear the
    //accumulators unless measurements were just recorded.
    
    if( i == REENTRAINED  && !particle_sim.is_timestep_for_accumulation() )
      continue;
    
    num[i] = 0;
    mass[i] = 0.0;
    sum_density[i] = 0.0;
    sum_diameter[i] = 0.0;
  }
  // }
  
  ccDOTIMES(i, N_SPACE_DIMS) {
    sum_particle_impulse[i] = 0.0;
  }
}
