/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#include "shob_groups.h"
#include "comm_groups.h"
#include "voxel_dyn_sp.h"
#include "conduction_solver_stencils.h"
#include "conduction_shell_mesh.cc"


#define NO_UNFOLDING_ITERATIONS

/**
 @brief Global variable holding mesh information for stencil building
 
 @details Shell stencil info is stored in a global variable to allow access to brep facet/half_edge
          operations (i.e. HalfEdgeVec, HalfEdgeMidPoint, GetFacetUnitNormal, etc.) without the
          need to pass it between multiple functions needing to pass. After stencils and edges are built,
          it can be deleted to free up space since the brep information will no longer be needed.
 */
SHELL_STENCIL_INFO g_shell_stencils_info = NULL;

/**
 @brief Returns BREP mesh object stored in g_shell_stencils_info 
 
 @details Useful for accessing functions that create bg objects from BREP objects.
 */
cSHELL_MESH* get_shell_mesh() {
  stencil_assert(g_shell_stencils_info != NULL);
  return g_shell_stencils_info->get_shell_mesh_ptr();
}

/**
 @brief Returns a BG_POINT2 containing the data provided in vec_global rotated to the facet's local 2D csys

 @details Uses the csys transformation class stored in facet object, which contains double precision values.
 */
cSHELL_MESH::sBG_POINT2 rotate_to_surfel_csys(const cSHELL_MESH::sBG_VECTOR3& vec_global, cSHELL_MESH::FACET facet) {
  SURFEL_SHELL_CONDUCTION_DATA surfel_shell_data = facet->get_surfel()->shell_conduction_data();
  
  cSHELL_MESH::sBG_VECTOR3 rotated3 = facet->getTransform().Transform(vec_global);
  return cSHELL_MESH::sBG_POINT2(rotated3[0],rotated3[1]);
}

/**
 @brief Constructs cSURFEL_UNFOLDER for calculating unfolded distance between two facet centroids.

 @details dir_point defines the initial direction of the cut made along the surface. When nbr_facet
          shares an edge with root_facet, this should be any point on the shared edge. When it is a vertex
          neighbor, it should be the location of the shared vertex. This information ensures that the unfolding
          process walks surface in the correct direction regardless of its complexity.

          If error_on_fail is FALSE, the unfolding process will stop without throwing sim errors
          if an issues arise. This avoids simulation failure when additional methods for unfolding can
          be used or if the neighbor can be excluded from the LSQ stencil.
 */
cSURFEL_UNFOLDER::cSURFEL_UNFOLDER(FACET root_facet, FACET nbr_facet, POINT3 dir_point, BOOLEAN error_on_fail)
    : m_root_facet(root_facet), m_nbr_facet(nbr_facet), m_error_on_fail(error_on_fail),
      m_root_face_id(root_facet->surfel_face_id()), m_nbr_face_id(nbr_facet->surfel_face_id()) {
  surfel_unfolder_stencil_assert(m_root_facet->is_facet_shell());
  surfel_unfolder_stencil_assert(m_nbr_facet->is_facet_shell());

  SURFEL root_surfel = root_facet->get_surfel();
  POINT3 root_centroid = get_shell_mesh()->GetFacetCentroid(root_facet->GetIndex());

  SURFEL nbr_surfel = nbr_facet->get_surfel();
  m_final_cut_point = get_shell_mesh()->GetFacetCentroid(nbr_facet->GetIndex());

  if (sim.is_2d()) {
    root_centroid[2] = 0.;
    m_final_cut_point[2] = 0.;
  }

  HALF_EDGE cut_edge = NULL;
  CUT_LOCATION_FLAG cut_loc;
  if (sim.is_2d()) {
    VECTOR2 vec_to_edge = POINT2(EXPAND_FLOAT_VEC2(dir_point))
                        - POINT2(EXPAND_FLOAT_VEC2(root_centroid));
    m_unfolded_centroid[0] =  vec_to_edge.Length();
    set_2d_scale_and_direction(vec_to_edge);
    cut_edge = get_initial_cut_edge_2d(dir_point);
    cut_loc = make_next_2d_cut(cut_edge);
    find_cut_points(cut_edge, cut_loc);
  } else {
    m_vec_to_nbr = m_final_cut_point - root_centroid;
    m_vec_to_nbr.Normalize();

    VECTOR3 owner_normal = get_shell_mesh()->GetFacetUnitNormal(root_facet->GetIndex());
    VECTOR3 nbr_normal = get_shell_mesh()->GetFacetUnitNormal(nbr_facet->GetIndex());
    VECTOR3 plane_tan2 = owner_normal*root_surfel->area + nbr_normal*nbr_surfel->area;
    plane_tan2.Normalize();

    VECTOR3 plane_normal = BgCross(m_vec_to_nbr, plane_tan2);
    m_cut_plane = PLANE3(root_centroid, plane_normal);

    m_cut_points.push_back(root_centroid);
    cut_loc = make_first_cut(cut_edge, dir_point);
    find_cut_points(cut_edge, cut_loc);
    // dir_point is not always a good representation of the direction to first cut when the surfels are highly skewed
    // or there edges with large dihedral angles, therefore try again with opposite direction for dir_point if first
    // attempt fails
    if (!m_cut_successful) {
      m_cut_points.resize(1);
      dir_point *= -1.;
      cut_loc = make_first_cut(cut_edge, dir_point);
      find_cut_points(cut_edge, cut_loc);
    }
  }
  
}


/**
 @brief Constructs cSURFEL_UNFOLDER for calculating unfolded distance between two facet centroids.

 @details planeTan provides one of the tangents vectors used to define the cutting plane (with the vector
          between centroids being the other). Ideally, multiple unfoldings should be performed while varying the
          planeTan vector in order to obtain the minimum unfolded length. For now, the vector between unfolded
          centroids when the nbr_facet is rotated along the shared edge into the root facet's plane is used. This
          appears to provide sufficient accuracy as long as the surface is not too complex.

          If error_on_fail is FALSE, the unfolding process will stop without throwing sim errors
          if an issues arise. This avoids simulation failure when additional methods for unfolding can
          be used or if the neighbor can be excluded from the LSQ stencil.
 */
cSURFEL_UNFOLDER::cSURFEL_UNFOLDER(FACET root_facet, FACET nbr_facet, VECTOR3 planeTan, BOOLEAN error_on_fail)
    : m_root_facet(root_facet), m_nbr_facet(nbr_facet), m_error_on_fail(error_on_fail),
      m_root_face_id(root_facet->surfel_face_id()), m_nbr_face_id(nbr_facet->surfel_face_id()) {
  surfel_unfolder_stencil_assert(m_root_facet->is_facet_shell());
  surfel_unfolder_stencil_assert(m_nbr_facet->is_facet_shell());

  SURFEL root_surfel = root_facet->get_surfel();
  POINT3 root_centroid = get_shell_mesh()->GetFacetCentroid(root_facet->GetIndex());
  POINT3 dir_point = root_centroid + planeTan;
  
  SURFEL nbr_surfel = nbr_facet->get_surfel();
  m_final_cut_point = get_shell_mesh()->GetFacetCentroid(nbr_facet->GetIndex());

  if (sim.is_2d()) {
    root_centroid[2] = 0.;
    m_final_cut_point[2] = 0.;
  }

  HALF_EDGE cut_edge = NULL;
  CUT_LOCATION_FLAG cut_loc;
  if (sim.is_2d()) {
    VECTOR2 vec_to_edge = POINT2(EXPAND_FLOAT_VEC2(dir_point))
                        - POINT2(EXPAND_FLOAT_VEC2(root_centroid));
    m_unfolded_centroid[0] =  vec_to_edge.Length();
    set_2d_scale_and_direction(vec_to_edge);
    cut_edge = get_initial_cut_edge_2d(dir_point);
    cut_loc = make_next_2d_cut(cut_edge);
  } else {
    m_vec_to_nbr = m_final_cut_point - root_centroid;
    m_vec_to_nbr.Normalize();

    planeTan.Normalize();
    auto tangentDotProd = planeTan*m_vec_to_nbr;
    // If the two surfels lie in the same plane, the tangent vector provided by the unfold_across_edge function 
    // is the same as m_vec_to_nbr, so the orientation of the cut plane is undefined. If this is the case,
    // use the surfel's normal vector as the other tangential vector
    if (fabs((planeTan*m_vec_to_nbr) - 1.0) < 1e-4) {
      planeTan = get_shell_mesh()->GetFacetUnitNormal(root_facet->GetIndex());
    }
    VECTOR3 plane_normal = BgCross(m_vec_to_nbr, planeTan);
    m_cut_plane = PLANE3(root_centroid, plane_normal);

    m_cut_points.push_back(root_centroid);
    cut_loc = make_first_cut(cut_edge, dir_point);
  }
  find_cut_points(cut_edge, cut_loc);
}


/**
 @brief Constructs cSURFEL_UNFOLDER for calculating unfolded distance between a facet and edge centroids.

 @details use_nbr_edge_midpoint defines whether the centroid of the nbr_edge is defined by its midpoint
          the location it is intersected by a vector located at the surfel centroid and oriented in the
          edge normal direction.
          
          See description above for information about dir_point and error_on_fail
 */
cSURFEL_UNFOLDER::cSURFEL_UNFOLDER(FACET root_facet, HALF_EDGE nbr_edge, POINT3 dir_point,
  BOOLEAN use_nbr_edge_midpoint, BOOLEAN error_on_fail) : m_root_facet(root_facet),
    m_nbr_facet(nbr_edge->GetFacet()), m_nbr_edge(nbr_edge), m_error_on_fail(error_on_fail),
    m_root_face_id(root_facet->surfel_face_id()), m_nbr_face_id(nbr_edge->GetFacet()->surfel_face_id()) {

  surfel_unfolder_stencil_assert(m_root_facet->is_facet_shell());
  surfel_unfolder_stencil_assert(m_nbr_facet->is_facet_shell());

  SURFEL root_surfel = root_facet->get_surfel();
  POINT3 root_centroid = get_shell_mesh()->GetFacetCentroid(root_facet->GetIndex());

  SURFEL nbr_surfel = m_nbr_facet->get_surfel();
  m_final_cut_point = cSURFEL_UNFOLDER::get_edge_point_location(nbr_edge, use_nbr_edge_midpoint);

  if (sim.is_2d()) {
    root_centroid[2] = 0.;
  }

  HALF_EDGE cut_edge = NULL;
  CUT_LOCATION_FLAG cut_loc;
  if (sim.is_2d()) {
    VECTOR2 vec_to_edge = POINT2(EXPAND_FLOAT_VEC2(dir_point))
                        - POINT2(EXPAND_FLOAT_VEC2(root_centroid));
    m_unfolded_centroid[0] =  vec_to_edge.Length();
    set_2d_scale_and_direction(vec_to_edge);
    cut_edge = get_initial_cut_edge_2d(dir_point);
    cut_loc = make_next_2d_cut(cut_edge);
    find_cut_points(cut_edge, cut_loc);
  } else {
    m_vec_to_nbr = m_final_cut_point - root_centroid;
    m_vec_to_nbr.Normalize();

    VECTOR3 owner_normal = get_shell_mesh()->GetFacetUnitNormal(root_facet->GetIndex());
    VECTOR3 plane_normal = BgCross(m_vec_to_nbr, owner_normal);
    m_cut_plane = PLANE3(root_centroid, plane_normal);

    m_cut_points.push_back(root_centroid);
    cut_loc = make_first_cut(cut_edge, dir_point);
    find_cut_points(cut_edge, cut_loc);

    // dir_point is not always a good representation of the direction to first cut when the surfels are highly skewed
    // or there edges with large dihedral angles, therefore try again with opposite direction for dir_point if first
    // attempt fails
    if (!m_cut_successful) {
      m_cut_points.resize(1);
      dir_point *= -1.;
      cut_loc = make_first_cut(cut_edge, dir_point);
      find_cut_points(cut_edge, cut_loc);
    }
  }

}

/**
 @brief Returns the centroid location on half_edge based on BOOLEAN use_midpoint.

 @details See constructor descriptions for an explanation of the two possible centroid locations.
          Method is static to allow for dir_point to be calculated from an edge to pass to constructor.
 */
cSURFEL_UNFOLDER::POINT3 cSURFEL_UNFOLDER::get_edge_point_location(HALF_EDGE half_edge, BOOLEAN use_midpoint) {
  // Edge is 1D in xy-plane so return tail vertex point regardless of use_centroid value
  if (sim.is_2d()) {
    return half_edge->GetTailVertex()->GetPoint();
  }

  POINT3 surfel_centroid = get_shell_mesh()->GetFacetCentroid(half_edge->GetFacet()->GetIndex());
  VECTOR3 edge_vec(get_shell_mesh()->HalfEdgeVec(half_edge));
  VECTOR3 surf_normal = get_shell_mesh()->GetFacetUnitNormal(half_edge->GetFacet()->GetIndex());

  VECTOR3 edge_normal = BgCross(edge_vec, surf_normal);
  edge_normal.Normalize();

  VECTOR3 centroid_to_tail_vec = half_edge->GetVertex()->GetPoint() - surfel_centroid;
  // if ((edge_normal * centroid_to_tail_vec) < 0.0) {
  //   edge_normal *= -1.0;
  // }

  // centroid of edge given by HalfEdgeMidPoint function
  if(use_midpoint) {
    POINT3 midPt = get_shell_mesh()->HalfEdgeMidPoint(half_edge);
    VECTOR3 cToMid = midPt - surfel_centroid;
    if ((cToMid * edge_normal) > 0.) {
      return midPt;
    }
  }

  return surfel_centroid + edge_normal * (centroid_to_tail_vec*edge_normal);
}


/**
 @brief Calculates initial segment of intersection between surface and cut plane (surfel centroid
        to first HALF_EDGE).

 @details Facet of initial cut is known, but plane will intersect two edges/vertices. Walks through edges
          and checks if they are intersected by cut plane. If it is, and the vector from the centroid
          and the intersection is in the same direction as the vector from the centroid to dir_point,
          this is the correct intersection. Otherwise the function continues to walk through the edges.
          This function is also called by make_first_cut_from_edge which determines the first cut
          when a root edge is provided.
 */
cSURFEL_UNFOLDER::CUT_LOCATION_FLAG cSURFEL_UNFOLDER::make_first_cut(HALF_EDGE& cut_edge, POINT3& dir_point) {
  // use dir_point to get vector for testing direction
  VECTOR3 dir_vec = dir_point - m_cut_points[0];
  BREP_FACET_DO_HALF_EDGES(half_edge, m_root_facet, cSHELL_MESH) {
    // This avoids checking m_root_edge for an intersection when this is called
    // by make_first_cut_from_edge (otherwise, m_root_edge = NULL)
    if (m_root_edge == half_edge) { continue; }
    SEGMENT3 segment = get_shell_mesh()->HalfEdgeSegment(half_edge);
    SEGMENT3 int_segment;
    POINT3 int_point;
    eBG_RETURN_TYPE int_type = BgIntersection(m_cut_plane, segment, int_point, int_segment);
    // Intersection on root surfel should always be a point (cannot be a segment) 
    // since the cut plane is perpendicular to this surfel.
    if (int_type == eBG_RETURN_TYPE::BG_RT_POINT3) {
      VECTOR3 vec_to_cut(int_point.X()-m_cut_points[0].X(),
                         int_point.Y()-m_cut_points[0].Y(),
                         int_point.Z()-m_cut_points[0].Z());

      // make sure cut point is in the correct direction
      // using info provided by dir_point
      if ((vec_to_cut * dir_vec) < 0.0) { continue; }

      // add cut point to m_cut_points and set cut_edge to this
      // half edge and return cut location.
      m_cut_points.push_back(int_point);
      cut_edge = half_edge;

      if (segment.Source().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_TAIL;
      } else if(segment.Target().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_HEAD;
      } else {
        return CUT_LOCATION_FLAG::CUT_ON_EDGE;
      }
    }
  }
  return CUT_LOCATION_FLAG::CUT_FAILURE;
}


/**
 @brief Calculates initial segment of intersection between surface and cut plane (HALF_EDGE centroid
        to first HALF_EDGE).

 @details Unlike when starting from a facet centroid, the intersection of the cut plane and the surface
          can either be a point on an edge or half of the starting edge. Method first checks if the
          root_edge or its opposite if check_opposite is TRUE correspond to the nbr facet. If so, no cuts
          need to be made. It then checks for the root edge being located on the cut plane
          before calling make_first_cut to check for an intersection in the direction
          of dir_point. Lastly, if no intersections have been found and check_opposite is TRUE it calls
          make_first_cut with the opposite of the root edge. This method is utilized for unfolding
          around an edge, which is needed for tLSQ_GRAD_SOLVER if utilized.
 */
cSURFEL_UNFOLDER::CUT_LOCATION_FLAG cSURFEL_UNFOLDER::make_first_cut_from_edge(HALF_EDGE& cut_edge,
    POINT3& dir_point, BOOLEAN check_opposite) {
  // First check if the owner facet is the nbr facet. If so, there is no unfolding needed.
  if (m_root_facet == m_nbr_facet) {
    cut_edge = m_root_edge;
    return CUT_LOCATION_FLAG::CUT_FINISHED; 
  }

  HALF_EDGE opp_edge = NULL;
  if (check_opposite) {
    // get opposite edge and corresponding facet
    opp_edge = get_opposite_edge(m_root_edge);
  
    // if opp_edge facet is nbr_facet, again, no unfolding is needed
    if (opp_edge->GetFacet() == m_nbr_facet) {
      cut_edge = opp_edge;
      return CUT_LOCATION_FLAG::CUT_FINISHED;
    }
  }

  // use dir_point to get vector for testing direction
  VECTOR3 dir_vec = dir_point - m_cut_points[0];

  // It is possible that the plane and edge are coincident, so this is checked first.
  SEGMENT3 segment = get_shell_mesh()->HalfEdgeSegment(m_root_edge);
  SEGMENT3 int_segment;
  POINT3 int_point;
  eBG_RETURN_TYPE int_type = BgIntersection(m_cut_plane, segment, int_point, int_segment);
  if (int_type == eBG_RETURN_TYPE::BG_RT_SEGMENT3) {
    cut_edge = m_root_edge;

    VECTOR3 vec_to_head = m_root_edge->GetHeadVertex()->GetPoint() - m_cut_points[0];
    VECTOR3 vec_to_tail = m_root_edge->GetTailVertex()->GetPoint() - m_cut_points[0];
    if ((vec_to_head * dir_vec) > 0.0) {
      m_cut_points.push_back(m_root_edge->GetHeadVertex()->GetPoint());
      return CUT_LOCATION_FLAG::CUT_ON_HEAD;
    } else {
      m_cut_points.push_back(m_root_edge->GetTailVertex()->GetPoint());
      return CUT_LOCATION_FLAG::CUT_ON_TAIL;
    }
  }

  // check if m_root_facet has additional edge cut by the plane in the direction
  // of dir_point. If this call returns anything other than CUT_FAILURE, return cut_loc.
  CUT_LOCATION_FLAG cut_loc = make_first_cut(cut_edge, dir_point);
  if (cut_loc != CUT_LOCATION_FLAG::CUT_FAILURE) {
    return cut_loc;
  }

  // Otherwise, it should be contained in the opposite edge facet so repeat call to make_first_cut
  // with the opposite edge set to m_root_edge and return these results, which could also be CUT_FAILURE
  if (check_opposite) {
    m_root_facet = opp_edge->GetFacet();
    m_root_edge = opp_edge;
    return make_first_cut(cut_edge, dir_point);
  } else {
    return CUT_LOCATION_FLAG::CUT_FAILURE;
  }
}

/**
 @brief Main loop walking across facets of surface to find where it intersects HALF_EDGE.

 @details After the initial calls to make_first_cut and make_first_cut_at_edge called by the constructors,
          the two functions make_next_cut_from_vertex and make_next_cut_from_edge can find the
          remaining intersection locations. This function repeatedly calls these two functions based on the
          value returned on previous cuts receiving CUT_LOCATION_FLAG::CUT_FINISHED. If all the cuts
          made without a CUT_FAILURE value being returned, the m_cut_successful variable will be set to
          TRUE, indicating that an unfolded centroid vector has been constructed.
 */
VOID cSURFEL_UNFOLDER::find_cut_points(HALF_EDGE& cut_edge, CUT_LOCATION_FLAG cut_loc) {
  surfel_unfolder_stencil_assert((cut_edge != NULL) || (cut_loc == CUT_LOCATION_FLAG::CUT_FAILURE));
  asINT32 num_cuts = 0;
  while (TRUE) {
    if (cut_loc == CUT_LOCATION_FLAG::CUT_FAILURE || num_cuts > 10) {
      if (m_error_on_fail) {
        msg_internal_error("Failure unfolding surfel %d for owner surfel %d after %d cuts",
          m_nbr_facet->get_surfel()->id(), m_root_facet->get_surfel()->id(), num_cuts);
      } else {
        return;
      }
    } else if (cut_loc == CUT_LOCATION_FLAG::CUT_FINISHED) {
      break;
    }

    if (sim.is_2d()) {
      cut_loc = make_next_2d_cut(cut_edge);
    } else {
      if (cut_loc == CUT_LOCATION_FLAG::CUT_ON_EDGE) {
        cut_loc = make_next_cut_from_edge(cut_edge);
      } else {
        cut_loc = make_next_cut_from_vertex(cut_edge, cut_loc);
      }
    }
    num_cuts++;
  }

  // in 3d, the final cut may not be at m_final_cut_point, so need to check
  // if it should be added to m_cut_points
  if (sim.is_3d()) {
    // m_final_cut_point needs to be added if either conditions is met:
    //    1) Constructor was provided a nbr facet (final loc is centroid)
    //    2) Constructor was provided a nbr edge and the last cut edge
    //        is not in the ring containing this cut edge.
    if (m_nbr_edge == NULL) {
      m_cut_points.push_back(m_final_cut_point);
    } else {
    if (!(m_nbr_edge->is_edge_in_ring(cut_edge))) {
        m_cut_points.push_back(m_final_cut_point);
      }
    }
  }
  m_cut_successful = TRUE;
}

/**
 @brief Gets opposite edge of half_edge in edge ring. Prints error information in event that no edge is found.
 */
cSURFEL_UNFOLDER::HALF_EDGE cSURFEL_UNFOLDER::get_opposite_edge(HALF_EDGE half_edge) {
  if (!half_edge->is_internal_edge()) {
    if (m_error_on_fail) {
      msg_print("%s",half_edge->print_detailed_ring_info().c_str());
      msg_internal_error("Error in unfolding facet %d (%d) for root facet %d (%d): Attempting "
        "to walk non manifold edge", m_nbr_facet->GetIndex(), m_nbr_facet->get_surfel()->id(),
        m_root_facet->GetIndex(), m_root_facet->get_surfel()->id());
    }
    return NULL;
  }
  return half_edge->get_internal_edge_nbr();
}

/**
 @brief Walks through edges in next facet until finding second intersection between plane and facet
 */
cSURFEL_UNFOLDER::CUT_LOCATION_FLAG cSURFEL_UNFOLDER::make_next_cut_from_edge(HALF_EDGE& cut_edge) {
  // get opposite edge and corresponding facet
  HALF_EDGE opp_edge = get_opposite_edge(cut_edge);

  if (opp_edge == NULL) {
    return CUT_LOCATION_FLAG::CUT_FAILURE;
  }

  FACET next_facet = opp_edge->GetFacet();

  // if this is m_nbr_facet, return CUT_FINISHED
  if (next_facet == m_nbr_facet) {
    cut_edge = opp_edge;
    return CUT_LOCATION_FLAG::CUT_FINISHED;
  }

  BREP_FACET_DO_HALF_EDGES(half_edge, next_facet, cSHELL_MESH) {
    if (opp_edge == half_edge) { continue; }

    SEGMENT3 segment = get_shell_mesh()->HalfEdgeSegment(half_edge);
    SEGMENT3 int_segment;
    POINT3 int_point;
    eBG_RETURN_TYPE int_type = BgIntersection(m_cut_plane, segment, int_point, int_segment);

    // Segment resulting from intersection can only occur when previous
    // cut intersected the edge vertices. Check left for debugging
    if (int_type == eBG_RETURN_TYPE::BG_RT_POINT3) {
      // add intersection pt to m_cut_points, and return needed info
      m_cut_points.push_back(int_point);
      cut_edge = half_edge;
      if (segment.Source().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_TAIL;
      } else if(segment.Target().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_HEAD;
      } else {
        return CUT_LOCATION_FLAG::CUT_ON_EDGE;
      }
    } else if (int_type == eBG_RETURN_TYPE::BG_RT_SEGMENT3) {
      surfel_unfolder_stencil_assert(false);//("Error: Intersection of plane and segment returned unexpected segment");
    }
  }
  return CUT_LOCATION_FLAG::CUT_FAILURE;
}

/**
 @brief Walks through facets sharing a vertex to identify a the next facet intersected by the cut plane.

 @details Any facet that has the same number of layers as the root facet is tested to see if
          the cut plane lies between the facet half edges sharing this vertex. Returns facet when found
          otherwise, returns NULL.
 */
cSURFEL_UNFOLDER::FACET cSURFEL_UNFOLDER::get_next_facet_from_vertex(HALF_EDGE cut_edge, VERTEX cut_vertex) {
  BREP_VERTEX_DO_FACETS(neigh_facet, cut_vertex, cSHELL_MESH) {
    // skip facet that was just traversed, or any that are not shells with the same
    // number of layers as the root_facet
    if ((neigh_facet == cut_edge->GetFacet()) || !(neigh_facet->is_facet_shell())
        || (neigh_facet->num_layers() != m_root_facet->num_layers())) {
      continue;
    }

    // Get half edge in facet with the tail at the intersection vertex
    HALF_EDGE half_edge1 = neigh_facet->GetHalfEdgeIntoV(cut_vertex->GetIndex());

    // If plane crosses into facet, the product of the signed distance values on the cut plane
    // of the head of half_edge1 and tail of the preceeding half edge will be negative (or exactly
    // zero in the case of either half edges being coincident with the cut plane).
    const POINT3& point_1 = half_edge1->GetTailVertex()->GetPoint();
    const POINT3& point_2 = half_edge1->GetNext()->GetHeadVertex()->GetPoint();
    NT dist1 = m_cut_plane.SignedDistance(point_1);
    NT dist2 = m_cut_plane.SignedDistance(point_2);

    if ((dist1*dist2) <= 0.0) {
      return neigh_facet;
    }
  }
  if (m_error_on_fail) {
    msg_internal_error("Error: could not find next facet along cut plane");
  }

  return NULL;
}

/**
 @brief Walks through edges of next facet intersected by cut plane to find next segment in unfolded centroid.
 */
cSURFEL_UNFOLDER::CUT_LOCATION_FLAG cSURFEL_UNFOLDER::make_next_cut_from_vertex(
    HALF_EDGE& cut_edge, CUT_LOCATION_FLAG cut_loc) {

  VERTEX cut_vertex = (cut_loc == CUT_LOCATION_FLAG::CUT_ON_HEAD)
                        ? cut_edge->GetHeadVertex()
                        : cut_edge->GetTailVertex();

  // Get next facet that will be cut
  FACET next_facet = get_next_facet_from_vertex(cut_edge, cut_vertex);
  if (next_facet == NULL) {
    return CUT_LOCATION_FLAG::CUT_FAILURE;
  }
  VECTOR3 last_vec = m_cut_points[m_cut_points.size()-1] 
    - m_cut_points[m_cut_points.size()-2];

  // if the next facet is m_nbr_facet, return CUT_FINISHED
  if (next_facet == m_nbr_facet) {
    return CUT_LOCATION_FLAG::CUT_FINISHED;
  }

  BREP_FACET_DO_HALF_EDGES(half_edge, next_facet, cSHELL_MESH) {
    SEGMENT3 segment = get_shell_mesh()->HalfEdgeSegment(half_edge);
    SEGMENT3 int_segment;
    POINT3 int_point;
    eBG_RETURN_TYPE int_type = BgIntersection(m_cut_plane, segment, int_point, int_segment);

    // Intersection can be a point or segment, so need to check for either
    if (int_type == eBG_RETURN_TYPE::BG_RT_POINT3) {
      // need to make sure this isnt the same location as cut_vertex
      if (int_point.Equals(cut_vertex->GetPoint())) { continue; }

      // add 3d cut point
      m_cut_points.push_back(int_point);
      cut_edge = half_edge;

      if (segment.Source().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_TAIL;
      } else if(segment.Target().Equals(int_point)) {
        return CUT_LOCATION_FLAG::CUT_ON_HEAD;
      } else {
        return CUT_LOCATION_FLAG::CUT_ON_EDGE;
      }
    } else if (int_type == eBG_RETURN_TYPE::BG_RT_SEGMENT3) {
      // next point will be other point in cut_edge that isn't cut_vertex
      POINT3 int_vertex_point = (cut_loc == CUT_LOCATION_FLAG::CUT_ON_HEAD)
                                  ? cut_edge->GetTailVertex()->GetPoint()
                                  : cut_edge->GetHeadVertex()->GetPoint();

      // add 3d cut point
      m_cut_points.push_back(int_vertex_point);
      cut_edge = half_edge;

      // return opposite of cut_loc
      return (cut_loc == CUT_LOCATION_FLAG::CUT_ON_HEAD)
                ? CUT_LOCATION_FLAG::CUT_ON_TAIL
                : CUT_LOCATION_FLAG::CUT_ON_HEAD;
    }
  }
  return CUT_LOCATION_FLAG::CUT_FAILURE;
}

/**
 @brief Gets next intersection between facet and cut plane.

 @details Due to 2D facets only having a max of two (non-border) edges, the next edge intersected by the
          cut plane is always the opposite edge in the facet. This function uses this fact to simplify
          the surface walking process.
 */
cSURFEL_UNFOLDER::CUT_LOCATION_FLAG cSURFEL_UNFOLDER::make_next_2d_cut(HALF_EDGE& cut_edge) {
  // get opposite edge and corresponding facet
  HALF_EDGE opp_edge = get_opposite_edge(cut_edge);

  if (opp_edge == NULL) {
    return CUT_LOCATION_FLAG::CUT_FAILURE;
  } else if (opp_edge == m_nbr_edge) {
    // No more walking to get to nbr_edge, so dont
    // add to m_unfolded_centroid[0] and return CUT_FINISHED
    return CUT_LOCATION_FLAG::CUT_FINISHED;
  }

  if (opp_edge->GetFacet() == m_nbr_facet) {
    // reached nbr_facet. If final cut location was on
    // this edge, the previous if statement would
    // have caught it, so this must be at other edge in
    // nbr facet. Therefore, the final section of the unfolded
    // vector must be from here to m_final_cut_point.

    // adding assert for initial implementation just in case
    surfel_unfolder_stencil_assert(opp_edge != m_nbr_edge);
    POINT3 opp_edge_pt = cSURFEL_UNFOLDER::get_edge_point_location(opp_edge);
    VECTOR2 vec_to_edge(m_final_cut_point[0]- opp_edge_pt[0],
                        m_final_cut_point[1]- opp_edge_pt[1]);
    m_unfolded_centroid[0] += vec_to_edge.Length();
    return CUT_LOCATION_FLAG::CUT_FINISHED;
  }

  // update cut edge and add to m_unfolded_centroid[0]
  cut_edge = opp_edge->get_opp_in_2d_facet();
  POINT3 opp_edge_pt = cSURFEL_UNFOLDER::get_edge_point_location(opp_edge);
  POINT3 cut_edge_pt = cSURFEL_UNFOLDER::get_edge_point_location(cut_edge);
  VECTOR2 vec_to_edge(cut_edge_pt[0] - opp_edge_pt[0], cut_edge_pt[1]- opp_edge_pt[1]);
  m_unfolded_centroid[0] += vec_to_edge.Length();
  return CUT_LOCATION_FLAG::CUT_ON_EDGE;
}

/**
 @brief Calculates the scaled length of the resulting unfolded vector between centroids.

 @details The unfolded vector length is equal to the total length between the cut points in m_cut_points
 */
dFLOAT cSURFEL_UNFOLDER::get_unfolded_vec_length() {
  // This unfolded vector has a length equal to the total distance traveled
  // between cut point and its direction is defined by the first two points
  // in m_cut_points (rotated to the surfels local coordinate system)
  dFLOAT unfolded_mag = 0.;
  ccDOTIMES(i, m_cut_points.size()-1) {
    VECTOR3 pvec = m_cut_points[i+1] - m_cut_points[i];
    unfolded_mag += pvec.Length();
  }
  dFLOAT twoLam = scale_to_voxel_size(m_root_facet->get_surfel()->scale());
  return unfolded_mag / twoLam;
}

/**
 @brief Copies the vector between unfolded centroids into the provided dFLOAT array, returning the magnitude of the vector

 @details For 2D simulations, the 1D vector is calculated from the length stored in m_unfolded_centroid[0]
          and m_scale_direction which contains the direction information and length scale factor for the root
          facet surfel and written to unfolded_vec[0]
          
          For 3D simulation, the 2D vector is calculated from the total length of segments between points
          stored in m_cut_points, while the direction of the vector is determined by rotating the initial
          cut to the root facet surfel's local 2D csys. The vector is also scaled to the root surfels
          local scale.
 */
dFLOAT cSURFEL_UNFOLDER::get_unfolded_centroid_vec(dFLOAT unfolded_vec[2]) {
  surfel_unfolder_stencil_assert(m_cut_successful);
  if (sim.is_2d()) {
    unfolded_vec[0] = m_unfolded_centroid[0]*m_scale_direction;
    unfolded_vec[1] = 0.0;
    return fabs(unfolded_vec[0]);
  }

  dFLOAT twoLam = scale_to_voxel_size(m_root_facet->get_surfel()->scale());
  NT unfolded_mag_scaled = get_unfolded_vec_length();

  VECTOR3 unfolded_vec_global = m_cut_points[1] - m_cut_points[0];
  unfolded_vec_global.Normalize();
  VECTOR3 unfolded_vec3 = m_root_facet->getTransform().Transform(unfolded_vec_global);
  // scale unfolded_vec3 and store first two elements in unfolded_vec (unfolded_vec3[2] = 0)
  vscale2(unfolded_vec, unfolded_mag_scaled, unfolded_vec3);

  // store unscaled vector in m_unfolded_centroid
  vscale2(m_unfolded_centroid, twoLam, unfolded_vec);
  return unfolded_mag_scaled;
}

dFLOAT cSURFEL_UNFOLDER::get_relative_rotation() {
  if (sim.is_2d()) {
    return 0.;
  }

  surfel_unfolder_stencil_assert(m_cut_successful);

  // vector from root centroid to first cut point
  VECTOR3 vecInRoot = m_cut_points[1] - m_cut_points[0];
  vecInRoot.Normalize();
  asINT32 nPts = m_cut_points.size();
  // vector from nbr centroid to last cut point 
  VECTOR3 vecInNbr = m_cut_points[nPts-1] - m_cut_points[nPts-2];
  vecInNbr.Normalize();

  // rotate vectors to their surfel's local csys
  VECTOR3 vecInRootCsys = m_root_facet->getTransform().Transform(vecInRoot);
  VECTOR3 vecInNbrCsys = m_nbr_facet->getTransform().Transform(vecInNbr);

  // Angle between segments provides relative rotation needed to transform nbr csys to root csys
  return atan2(vcross2(vecInNbrCsys, vecInRootCsys), vdot2(vecInNbrCsys, vecInRootCsys));
}

/**
 @brief Calculates the direction of a 2D unfolded vector and the scale factor associated with the root
        surfel's scale.

 @details rotates vector into local 2D csys and determines if unfolded vector is in +x or -x direction.
 */
VOID cSURFEL_UNFOLDER::set_2d_scale_and_direction(VECTOR2& first_segment) {
  SURFEL_SHELL_CONDUCTION_DATA surfel_shell_data = m_root_facet->get_surfel()->shell_conduction_data();
  VECTOR2 rotated_segment(vdot2(surfel_shell_data->local_rotation_mat[0],first_segment),
                          vdot2(surfel_shell_data->local_rotation_mat[1],first_segment));
  surfel_unfolder_stencil_assert(fabs(rotated_segment[1]) < 1e-5);
  dFLOAT twoLam = scale_to_voxel_size(m_root_facet->get_surfel()->scale());
  m_scale_direction = (rotated_segment[0] < 0) ? -1.0/twoLam : 1.0/twoLam;
}


/**
 @brief Calculates relative rotation and unfolded vector between centroids for internal edge initialization

 @details Only suitable for internal edges.
 */
cITERATIVE_UNFOLDER::cITERATIVE_UNFOLDER(cSHELL_MESH::HALF_EDGE root_edge, cSHELL_MESH::HALF_EDGE nbr_edge)
    : m_root_facet(root_edge->GetFacet()), m_nbr_facet(nbr_edge->GetFacet()), m_min_length(1.0e10),
      m_unfolding_successful(FALSE) {

  // No iteration necessary for 2d simulations, so calculate unfolded vec and return 
  if (sim.is_2d()) {
    cSURFEL_UNFOLDER unfolder(m_root_facet, m_nbr_facet, 
      cSURFEL_UNFOLDER::get_edge_point_location(root_edge, FALSE), TRUE);
    unfolder.get_unfolded_centroid_vec(m_unfolded_min);
    // No rotation necessary for 2D facets
    m_rel_rotation = 0.;
    m_unfolding_successful = TRUE;
    return;
  }

  BOOLEAN unfoldSuccess = unfold_across_edge(root_edge);

  // // Commented code below checks rotation angle for correctness
  // const sdFLOAT* nbr_norm = nbr_edge->local_normal();
  // sdFLOAT rot_nbr_edge[2];
  // vrotate2(rot_nbr_edge, m_rel_rotation, nbr_norm);
  // vmul2(rot_nbr_edge,-1.);
  // sdFLOAT diff_edge_norms[2];
  // vsub2(diff_edge_norms, rot_nbr_edge, root_edge->local_normal());
  // sdFLOAT diffMag = vlength2(diff_edge_norms);
  // if (diffMag >= 1e-6) {
  //   msg_print("Diff = %1.4e for surfels %d and %d", diffMag, root_edge->get_surfel()->id(),
  //       nbr_edge->get_surfel()->id());
  // }

  // if simple unfolding unsuccessful, perform interation using cSURFEL_UNFOLDER
  if (!unfoldSuccess) {
    unfold_with_known_start_point();
  }

  // If the shortest length was found using cSURFEL_UNFOLDER, recalculate the angle using the instance that
  // contains the shortest unfolded length
  if (m_shortest_length_unfolding.get() != NULL) {
    m_rel_rotation = m_shortest_length_unfolding->get_relative_rotation();
  }

  // This method should always provide an unfolded vector, so make sure m_unfolding_successful is set to TRUE
  m_unfolding_successful = TRUE;
}

/**
 @brief Calculates relative rotation and unfolded vector between centroids for vertex neighbor initialization

 @details Starts with the unfolding using the surfel normals to define the cut plane. The derivative is
          calculated at this angle and the next set of cuts used to calculate the derivative is made based on
          this initial derivative (always angle1 +- 2 degrees). This information is then passed to the iteration
          function to iterate until finding the minimum.
 */
cITERATIVE_UNFOLDER::cITERATIVE_UNFOLDER(FACET root_facet, FACET nbr_facet, cSHELL_MESH::VERTEX vertex)
    : m_root_facet(root_facet), m_nbr_facet(nbr_facet), m_min_length(1.0e10),
      m_unfolding_successful(FALSE) {

  stencil_assert(!sim.is_2d());
  // Start with cutting plane defined by surfel normals. If it fails, this nbr_surfel will be excluded from the
  // set of vertex neighbors
  cSURFEL_UNFOLDER unfolder(m_root_facet, m_nbr_facet, vertex->GetPoint(), FALSE);
  if (unfolder.unfolding_failed()) {
    return;
  }

  unfolder.get_unfolded_centroid_vec(m_unfolded_min);
  dFLOAT vlength_1 = vlength2(m_unfolded_min);
  // Very small surfels can show the unfolding passed, but return a unfolded vec = (0,0). In this case, we will 
  // calculate the unfolded vec length from the sum of the lengths of vectors between the vertex and the centroids and
  // the direction will be determined from the vector between the root_centroid and the nbr centroid projected onto the
  // root facets plane. This is done in handle_very_small_surfels function
  if (vlength_1 <= 1e-16) {
    // handle_very_small_surfels(vertex);
    return;
  }

  // At this point we have a minimum unfolded distance, so if subsequent unfoldings fail, the current minimum will be
  // used
  m_unfolding_successful = TRUE;

  m_min_length = vlength_1;
  // Get the initial angle from the first segment of the cut line in unfolder
  cSHELL_MESH::sBG_VECTOR3 first_cut_segment = unfolder.get_first_cut_segment();
  first_cut_segment.Normalize();
  // transform to the root facet's local csys to obtain angle.
  cSHELL_MESH::sBG_VECTOR3 first_cut_segment_local = m_root_facet->getTransform().Transform(first_cut_segment);
  dFLOAT start_angle = atan2(first_cut_segment_local[1], first_cut_segment_local[0]);
  m_min_first_cut = first_cut_segment;

#ifdef NO_UNFOLDING_ITERATIONS
  return;
#endif

  dFLOAT angle2 = start_angle + 0.1*M_PI/180.;
  // make new cut to obtain a gradient, returning if the cut fails
  m_iterNum = 1;
  dFLOAT vlength2 = calculate_unfolded_vector_at_angle(angle2);
  if (vlength2 < 0.) {
    return;
  }

  // Use first two unfoldings to calculate the derivative, returning if it is already sufficiently small
  dFLOAT derivative_im2 = (m_min_length - vlength2) / (start_angle - angle2);
  dFLOAT angle_im2 = start_angle;

  if (fabs(derivative_im2) <= m_absTol) {
    return;
  }
  
  // Get next angle to test based on the derivative (assuming that the unfolded vec length vs angle is approximately
  // parabolid)
  dFLOAT angle_im1 = angle_im2 + ((derivative_im2 < 0.) ? (5.0) : (-5.0)) * (M_PI/180.);
  dFLOAT derivative_im1;
  m_iterNum = 2;

  if (!calculate_derivative_at_angle(angle_im1, derivative_im1, vlength2)) {
    return;
  }

  iterate_with_unfolding_algorithm(angle_im2, derivative_im2, angle_im1, derivative_im1);
}

VOID cITERATIVE_UNFOLDER::handle_very_small_surfels(cSHELL_MESH::VERTEX vertex) {
  cSHELL_MESH::sBG_POINT3 root_centroid = get_shell_mesh()->GetFacetCentroid(m_root_facet->GetIndex());
  cSHELL_MESH::sBG_VECTOR3 root_normal = get_shell_mesh()->GetFacetUnitNormal(m_root_facet->GetIndex());
  cSHELL_MESH::sBG_PLANE3 root_plane(root_centroid, root_normal);
  cSHELL_MESH::sBG_POINT3 nbr_centroid = get_shell_mesh()->GetFacetCentroid(m_nbr_facet->GetIndex());
  cSHELL_MESH::sBG_POINT3 proj_nbr_centroid = root_plane.Projection(nbr_centroid);
  cSHELL_MESH::sBG_VECTOR3 root_to_proj_nbr = proj_nbr_centroid - root_centroid;
  cSHELL_MESH::sBG_POINT2 proj_nbr_in_root_csys = rotate_to_surfel_csys(root_to_proj_nbr, m_root_facet);
  cSHELL_MESH::sBG_VECTOR2 unfoldVector(proj_nbr_in_root_csys[0],proj_nbr_in_root_csys[1]);
  unfoldVector.Normalize();

  if (root_to_proj_nbr.Length() < 1e-16) {
    return;
  }

  cSHELL_MESH::sBG_VECTOR3 root_to_vertex = vertex->GetPoint() - root_centroid;
  cSHELL_MESH::sBG_VECTOR3 vertex_to_nbr = nbr_centroid - vertex->GetPoint();
  cSHELL_MESH::NT distToNbr = root_to_vertex.Length() + vertex_to_nbr.Length();  
  if (distToNbr < 1e-16) {
    return;
  }
  vscale2(m_unfolded_min, distToNbr, unfoldVector);
  m_unfolding_successful = TRUE;
}


VOID cITERATIVE_UNFOLDER::unfold_with_known_start_point() {
  // Reset min unfolded vector info since it isn't correct, but keep unfolded vector calculated incase the
  // unfolder fails and we need to fall back to use this slighly incorrect value.
  dFLOAT failed_unfold_vec[2];
  vcopy2(failed_unfold_vec, m_unfolded_min);
  dFLOAT min_possible_length = m_min_length;
  m_min_length = 1.0e10;


  dFLOAT angle_im2 = atan2(m_unfolded_min[1], m_unfolded_min[0]);

#ifdef NO_UNFOLDING_ITERATIONS
  dFLOAT vlen1 = calculate_unfolded_vector_at_angle(angle_im2);
  if (vlen1 < 0) {
    vcopy2(m_unfolded_min, failed_unfold_vec);
  }
#endif

  dFLOAT derivative_im2, vlength_im2;
  m_iterNum = 1;
  if (!calculate_derivative_at_angle(angle_im2, derivative_im2, vlength_im2)) {
    // if m_unfolding_successful is still FALSE, we will keep using the originally calculated unfolded vector
    // stored in failed_unfold_vec.
    if (!m_unfolding_successful) {
      vcopy2(m_unfolded_min, failed_unfold_vec);
      return;
    }
    // if the first call to cSURFEL_UNFOLDER was successful when calculating the derivative,
    // that means the rotation angle needs to be recalculated based on the 
    return;
  }
  auto initialCutPtStr = m_shortest_length_unfolding->getCutPointsString();

  if (fabs(derivative_im2) < m_absTol) {
    return;
  }

  vlength_im2 -= min_possible_length;
  dFLOAT dAngle = vlength_im2 / derivative_im2;
  dAngle = MAX(-5.*M_PI/180.,MIN(dAngle, 5.*M_PI/180.));
  dFLOAT angle_im1 = angle_im2 - dAngle;
  dFLOAT derivative_im1;
  m_iterNum = 2;
  // no longer need vlength_im2, so can just pass it again instead of creating a new variable.
  if (!calculate_derivative_at_angle(angle_im1, derivative_im1, vlength_im2)) {
    return;
  }

  iterate_with_unfolding_algorithm(angle_im2, derivative_im2, angle_im1, derivative_im1);
}


dFLOAT cITERATIVE_UNFOLDER::calculate_unfolded_vector_at_angle(dFLOAT angle) {
  dFLOAT local_vec[2] = {cos(angle), sin(angle)};
  dFLOAT global_vec[3];
  m_root_facet->get_surfel()->shell_conduction_data()->rotate_to_global_coordinates(global_vec,local_vec);
  cSHELL_MESH::sBG_VECTOR3 globalVec(EXPAND_FLOAT_VEC3(global_vec));
  std::unique_ptr<cSURFEL_UNFOLDER> cur_unfolding(
    std::make_unique<cSURFEL_UNFOLDER>(m_root_facet, m_nbr_facet, globalVec, FALSE));
  
  if (cur_unfolding->unfolding_failed()) {
    return -1.0;
  }

  dFLOAT unfoldedTmp[2];
  dFLOAT vLen = cur_unfolding->get_unfolded_centroid_vec(unfoldedTmp);
  if (vLen < m_min_length) {
    m_min_length = vLen;
    vcopy2(m_unfolded_min, unfoldedTmp);
    m_unfolding_successful = TRUE;
    m_min_first_cut = cur_unfolding->get_first_cut_segment();
    m_shortest_length_unfolding.swap(cur_unfolding);
  }
  return vLen;
}


BOOLEAN cITERATIVE_UNFOLDER::calculate_derivative_at_angle(dFLOAT angle1, dFLOAT& derivative, dFLOAT& vlength) {
  dFLOAT vlen1 = calculate_unfolded_vector_at_angle(angle1);
  if (vlen1 < 0) {
    return FALSE;
  }
  dFLOAT angle2 = angle1 + (0.1*M_PI/180.);
  dFLOAT vlen2 = calculate_unfolded_vector_at_angle(angle2);
  if (vlen2 < 0.) {
    return FALSE;
  }

  derivative = (vlen1 - vlen2) / (angle1 - angle2);
  vlength = MIN(vlen1, vlen2);
  return TRUE;
}


VOID cITERATIVE_UNFOLDER::iterate_with_unfolding_algorithm(dFLOAT angle_im2, dFLOAT dLda_im2,
                                                              dFLOAT angle_im1, dFLOAT dLda_im1) {
  dFLOAT vlen;
  while((fabs(dLda_im1) > m_absTol) && (fabs(dLda_im1-dLda_im2) > m_relTol) && (m_iterNum < m_maxIters)) {
    dFLOAT next_angle = (angle_im2*dLda_im1 - angle_im1*dLda_im2)
                      / (dLda_im1 - dLda_im2);
    angle_im2 = angle_im1;
    angle_im1 = next_angle;
    dLda_im2 = dLda_im1;

    if (!calculate_derivative_at_angle(angle_im1, dLda_im1, vlen)) {
      break;
    }
    m_iterNum++;
  }
}

/**
 @brief Simplified algorithm to compute vector between unfolded centroids of two edge neighbors.

 @details Algorithm rotates and translates the shared half edge in the neighbor surfels local csys
          so that it is aligned with the half edge in the root surfel's local csys. The final location
          of the centroid is then the vector to between the unfolded centroids since the root surfel's
          centroid is at (0,0).

          This method is accurate as long as the unfolded vector intersects the edge. Otherwise, the
          cSURFEL_UNFOLDER implementation will provide a more accurate calculation. Currently, all internal
          edges will first attempt this method. If the resulting unfolder does not cross the edge, it will
          fall back to using cSURFEL_UNFOLDER. If that fails again, it will use the original results from
          this implementation even though they suffer from accuracy issues.
 */
BOOLEAN cITERATIVE_UNFOLDER::unfold_across_edge(cSHELL_MESH::HALF_EDGE root_edge) {

  cSHELL_MESH::sBG_VECTOR3 he_vec = get_shell_mesh()->HalfEdgeVec(root_edge);
  cSHELL_MESH::sBG_VECTOR3 he_vec_root = m_root_facet->getTransform().Transform(he_vec);
  cSHELL_MESH::sBG_VECTOR3 he_vec_nbr = m_nbr_facet->getTransform().Transform(he_vec);
  m_rel_rotation = atan2(vcross2(he_vec_nbr, he_vec_root), vdot2(he_vec_nbr, he_vec_root));

  cSHELL_MESH::sBG_POINT3 edge_midpt = get_shell_mesh()->HalfEdgeMidPoint(root_edge);
  cSHELL_MESH::sBG_POINT3 edge_midpt_root = m_root_facet->getTransform().Transform(edge_midpt);
  cSHELL_MESH::sBG_POINT3 edge_midpt_nbr = m_nbr_facet->getTransform().Transform(edge_midpt);

  cSHELL_MESH::sBG_VECTOR3 midpt_to_nbr;
  vrotate2(midpt_to_nbr, m_rel_rotation, edge_midpt_nbr);

  cSHELL_MESH::sBG_POINT3 nbr_unfolded_centroid3 = edge_midpt_root - midpt_to_nbr;
  cSHELL_MESH::sBG_POINT2 nbr_unfolded_centroid(nbr_unfolded_centroid3[0],nbr_unfolded_centroid3[1]);

  cSHELL_MESH::sBG_POINT3 tail_root = m_root_facet->getTransform().Transform(root_edge->GetTailVertex()->GetPoint());
  cSHELL_MESH::sBG_POINT3 head_root = m_root_facet->getTransform().Transform(root_edge->GetHeadVertex()->GetPoint());

  dFLOAT twoLamInv = 1./scale_to_voxel_size(m_root_facet->get_surfel()->scale());
  vscale2(m_unfolded_min, twoLamInv, nbr_unfolded_centroid);

  m_min_length = vlength2(m_unfolded_min);
  cSHELL_MESH::sBG_POINT2 tail_root2(tail_root[0],tail_root[1]);
  cSHELL_MESH::sBG_POINT2 head_root2(head_root[0],head_root[1]);
  cSHELL_MESH::sBG_SEGMENT2 edge_segment(tail_root2, head_root2);
  cSHELL_MESH::sBG_SEGMENT2 unfolded_segment(cSHELL_MESH::sBG_POINT2(0.,0.), nbr_unfolded_centroid);
  cSHELL_MESH::sBG_SEGMENT2 int_segment;
  cSHELL_MESH::sBG_POINT2 int_pt;
  eBG_RETURN_TYPE int_type = BgIntersection(unfolded_segment, edge_segment, int_segment, int_pt);
  if (int_type != eBG_RETURN_TYPE::BG_RT_POINT2) {
    return FALSE;
  }

  return TRUE;
}

/**
 @brief Main function building shell stencils and conduction edges.

 @details Calls all functions necessary to identify vertex neighbors and edge types for each
          shell surfel. This is used to allocate cSHELL_CONDUCTION_STENCIL_NEIGHBOR and 
          cCONDUCTION_EDGE_BASE objects and add them to the global cSHELL_CONDUCTION_STENCIL_INFO
          object g_shell_conduction_edges.
 */
VOID cSHELL_STENCIL_INFO::build_shell_stencils() {
  snprintf(g_assert_backtrace_path, 64, "SP%d_assert_backtrace.txt", my_proc_id);

  if (!sim.enable_tangential_shell_conduction) {
    return;
  }

  if (!sim.is_conduction_sp) {
    return;
  }

  if (g_pfc_print_shell_mesh_connectivity) {
    m_face_connectivity_map = std::make_unique<FACE_CONNECTIVITY_MAP>();
  }
  if (g_pfc_output_shell_mesh_info) {
    m_mesh_to_vtk_info = std::make_unique<cMESH_TO_VTK_INFO>();
  }
  if (g_pfc_print_shell_mesh_statistics || this->outputVTKInfo()) {
    m_mesh_statistics = std::make_unique<cMESH_STATISTICS>();
  }
  if (g_pfc_output_shell_stencil_details) {
    m_surfel_stencil_details = std::make_unique<cSTENCIL_DETAILED_INFO>();
  }


  // Build initial cSHELL_MESH
  init_shell_mesh();

  // Identify all facets to keepassociated with or neighboring those associated with shell surfels
  BOOLEAN shell_found = mark_facets_to_keep();

  if (sim.use_implicit_shell_solver) {
    m_ghost_surfel_sps.resize(total_csps);
  }

  BOOLEAN proc_has_shells = FALSE;

  // We need to call comm functions even if this CSP doesnt have any shell surfels to avoid hangs related to MPI comms
  if (!shell_found) {
    // There are two sets of comms calls in m_shell_mesh
    m_shell_mesh->perform_dummy_comms();

    // if implicit solver, there are comms calls in comm_ghost_surfel_info
    if (sim.use_implicit_shell_solver) {
      comm_ghost_surfel_info();
    }

    // If mesh statistics are being calculated there is another set of comms calls for both the implicit and
    // explicit solvers
    if (g_pfc_print_shell_mesh_statistics) {
      m_mesh_statistics->finalizeStats();
    }
  } else {
    // Create new cSHELL_MESH object containing a cSHELL_MESH mesh
    simplify_facets();

    // Calculate the local 2D csys info for each shell surfel
    compute_local_csys_for_shells();

    m_shell_mesh->identify_facet_edge_types();

    // Dump facet info (for debugging)
    // m_shell_mesh->dump_all_facet_info();
    // m_shell_mesh->dump_edge_ring_info();

    calculate_int_edge_geometry_for_facets();

    // Build stencils for each shell surfel facet
    build_stencils_for_facets();

    // Write info about edge types between faces in simulation to log file.
    if (this->storeFaceConnectivity()) {
      dump_face_connectivity_info();
    }

    if (g_pfc_print_shell_mesh_statistics) {
      m_mesh_statistics->finalizeStats();
    }

    if (this->outputVTKInfo()) {
      m_mesh_statistics->writeStatsFile();
    }

    if (this->outputStencilDetails()) {
      m_surfel_stencil_details->addNbrFacets();
    }
    proc_has_shells = TRUE;
  }
  asINT32 sum_procs = (proc_has_shells) ? 1 : 0;
  MPI_Allreduce(MPI_IN_PLACE, &sum_procs, 1, MPI_INT, MPI_SUM, eMPI_csp_comm);
  g_shell_conduction_edges.set_num_procs(sum_procs);
  g_shell_conduction_edges.set_proc_has_shells(proc_has_shells);
}

/**
 @brief Destructor which calls clear function to delete data stored in unique_ptr<cSHELL_MESH_INFO>.
 */
cSHELL_STENCIL_INFO::~cSHELL_STENCIL_INFO() {
  this->clear();
}

/**
 @brief Builds initial cSHELL_MESH.

 @details Allocates cSHELL_MESH_INFO object and stores it in m_shell_mesh_info. Adds all vertices and
          surfels in conduction realm to mesh
 */
VOID cSHELL_STENCIL_INFO::init_shell_mesh() {
  // create cSHELL_MESH object and store in unique_ptr
  // the constructor will add all vertices and faces (referred to
  // as shells in the brep implementation)
  m_shell_mesh = std::make_unique<cSHELL_MESH>();
  m_shell_mesh->add_faces();

  // add any surfels in conduction realm to brep object
  // Some of these loops may not contain any surfels in the
  // conduction realm, but this code is not performance sentitive.
  // Any conduction surfels that are not needed for the shell solver
  // stencils will not be kept in the final simplified mesh. 
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      m_shell_mesh->add_surfel(surfel);
    }

    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        if (surfel->realm() == STP_COND_REALM) {
          m_shell_mesh->add_surfel(surfel);
        }
      }
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      if (slrf_surfel_pair->m_exterior_surfel->realm() == STP_COND_REALM) {
        m_shell_mesh->add_surfel(slrf_surfel_pair->m_exterior_surfel);
      }
      if (slrf_surfel_pair->m_interior_surfel->realm() == STP_COND_REALM) {
        m_shell_mesh->add_surfel(slrf_surfel_pair->m_interior_surfel);
      }
    }

    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum: surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        if (surfel->realm() == STP_COND_REALM) {
          m_shell_mesh->add_surfel(surfel);
        }
      }
    }
#if !BUILD_5G_LATTICE
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      // if (wsurfel_conduction->is_conduction_shell()) {
        m_shell_mesh->add_surfel(wsurfel_conduction);
      // }
    }

    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      // if (contact_surfel->is_conduction_shell()) {
        m_shell_mesh->add_surfel(contact_surfel);
      // }
    }
#endif
  }
}

/**
 @brief Calculates the local 2D csys rotation matrix for each shell surfel and computes all geometric
        information of half edges associated with the surfel
 */
VOID cSHELL_STENCIL_INFO::compute_local_csys_for_shells() {
  BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
    if (facet->is_facet_shell()) {
      // compute local rotation info for shell surfel
      compute_local_rotation_info(facet);
      // using local rotation info, calculate geometric info for half edges
      BREP_FACET_DO_HALF_EDGES(half_edge, facet, cSHELL_MESH) {
        half_edge->compute_geometric_info();
      }
    }
  }
}

/**
 @brief Computes unfolded vector between centroids and relative rotation between surfels used in internal edges

  @details Simplified 2D implementation can be used when simulation is 2D.
 */
VOID cSHELL_STENCIL_INFO::calculate_int_edge_geometry_for_facets() {
  BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
    // internal edges can only be between shell surfels
    if (!facet->is_facet_shell()) { continue; }

    // For explicit solver - only needs to be called for non-ghosted surfels
    // For implicit solver - needs to be called for any non-ghosted surfel, or any ghosted surfel with no-ghosted
    //                       edge neighbor.
    BOOLEAN build_for_facet = (sim.use_implicit_shell_solver) 
                            ? facet->is_facet_shell_or_shell_neighbor(TRUE) : !facet->get_surfel()->is_ghost();
    if (build_for_facet) {
      if (sim.is_2d()) {
        calculate_int_edge_geometry_for_2d_facet(facet);
      } else {
        calculate_int_edge_geometry_for_facet(facet);
      }
    }
  }

  m_shell_mesh->comm_ghost_int_edge_geom_info();
}

/**
 @brief Computes unfolded vector between centroids and relative rotation between surfels in 2D used in internal edges
 */
VOID cSHELL_STENCIL_INFO::calculate_int_edge_geometry_for_facet(cSHELL_MESH::FACET root_facet) {
  if (!root_facet->has_edge_neighbors()) {
    return;
  }

  SURFEL root_surfel = root_facet->get_surfel();
  BOOLEAN root_is_ghost = root_surfel->is_ghost();

  BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
    // some misshapen surfels have length ~= 0 edges. This will skip them because they often
    // fail to calculate normal correctly
    if (half_edge->length() < DFLOAT_EPSILON) { continue; }

    // Skip if the edge is not internal
    if (!half_edge->is_internal_edge()) { continue; }

    cSHELL_MESH::HALF_EDGE other_edge = half_edge->get_internal_edge_nbr();
    SURFEL other_surfel = other_edge->get_surfel();

    // root surfel is the owner of the edge
    if (root_surfel->id() < other_surfel->id()) {
      if (root_is_ghost) {
        STP_PROC homeCSP = half_edge->get_surfel()->home_sp() - total_fsps;
        m_shell_mesh->get_ghost_half_edge_info_for_sp(homeCSP).push_back(half_edge);
      } else {
        dFLOAT unfolded_vec[2];
        cITERATIVE_UNFOLDER internalEdgeUnfolder(half_edge, other_edge);
        stencil_assert(internalEdgeUnfolder.unfoldingSuccessful());
        dFLOAT rel_rotation = internalEdgeUnfolder.relativeRotation();
        internalEdgeUnfolder.unfoldedVector(unfolded_vec);
        half_edge->set_int_edge_unfold_vec(unfolded_vec);
        half_edge->set_int_edge_rotation(rel_rotation);
      }
    } else {
      // In the event that owner surfel will not have this function called on this SP, we will need to retrieve
      // this information from the owner surfel's home SP. The conditions in which this happens is different based
      // on whether or not the explicit or implicit solver are in use:
      //    Implicit Solver - the root surfel is both a ghost and the nbr surfel in the internal edge, and the
      //                      the other surfel (which must be the owner) is ghosted and not an edge neighbor of a
      //                      non-ghosted surfel (e.g. owner_facet->is_facet_shell_or_shell_neighbor(TRUE) == FALSE)
      //    Explicit Solver - the root surfel is the nbr surfel in the internal edge and the other surfel (which
      //                      must be the owner) is ghosted on this SP.
      BOOLEAN owner_not_updated = (sim.use_implicit_shell_solver) 
                                ? (root_is_ghost && !(other_edge->GetFacet()->is_facet_shell_or_shell_neighbor(TRUE)))
                                : other_edge->get_surfel()->is_ghost();
      if (owner_not_updated) {
        // Because the simplification process for a neighbor of a ghost may not produce the same results as the same process
        // on that surfel's home SP, we cannot index other_edge properly for the comm process (half edges indexed by
        // surfel ID and index of half edge in facet starting at facet->GetHalfEdge()). Therefore, we will
        // still use half_edge's index info, but add it to other_edge's home sp in the vector of half_edges needing
        // data comm'd. The functions know to check and switch to the half_edge associated with the smaller indexed surfel
        // if necessary, so it will be handled properly.
        STP_PROC homeCSP = other_edge->get_surfel()->home_sp() - total_fsps;
        m_shell_mesh->get_ghost_half_edge_info_for_sp(homeCSP).push_back(half_edge);
      }
    }
  }
}

/**
 @brief Computes unfolded vector between centroids and relative rotation between surfels in 2D used in internal edges
 */
VOID cSHELL_STENCIL_INFO::calculate_int_edge_geometry_for_2d_facet(cSHELL_MESH::FACET root_facet) {
  if (!root_facet->has_edge_neighbors()) {
    return;
  }

  SURFEL root_surfel = root_facet->get_surfel();
  BOOLEAN root_is_ghost = root_surfel->is_ghost();

  BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
    // Skip if the edge is not internal
    if (!half_edge->is_internal_edge()) { continue; }

    cSHELL_MESH::HALF_EDGE other_edge = half_edge->get_internal_edge_nbr();
    SURFEL other_surfel = other_edge->get_surfel();

    // root surfel is the owner of the edge
    if (root_surfel->id() < other_surfel->id()) {
      if (root_is_ghost) {
        STP_PROC homeCSP = half_edge->get_surfel()->home_sp() - total_fsps;
        m_shell_mesh->get_ghost_half_edge_info_for_sp(homeCSP).push_back(half_edge);
      } else {
        dFLOAT unfolded_vec[2];
        cITERATIVE_UNFOLDER internalEdgeUnfolder(half_edge, other_edge);
        stencil_assert(internalEdgeUnfolder.unfoldingSuccessful());
        dFLOAT rel_rotation = internalEdgeUnfolder.relativeRotation();
        internalEdgeUnfolder.unfoldedVector(unfolded_vec);
        half_edge->set_int_edge_unfold_vec(unfolded_vec);
        half_edge->set_int_edge_rotation(rel_rotation);
      }
    } else {
      // In the event that owner surfel will not have this function called on this SP, we will need to retrieve
      // this information from the owner surfel's home SP. The conditions in which this happens is different based
      // on whether or not the explicit or implicit solver are in use:
      //    Implicit Solver - the root surfel is both a ghost and the nbr surfel in the internal edge, and the
      //                      the other surfel (which must be the owner) is ghosted and not an edge neighbor of a
      //                      non-ghosted surfel (e.g. owner_facet->is_facet_shell_or_shell_neighbor(TRUE) == FALSE)
      //    Explicit Solver - the root surfel is the nbr surfel in the internal edge and the other surfel (which
      //                      must be the owner) is ghosted on this SP.
      BOOLEAN owner_not_updated = (sim.use_implicit_shell_solver) 
                                ? (root_is_ghost && !(other_edge->GetFacet()->is_facet_shell_or_shell_neighbor(TRUE)))
                                : other_edge->get_surfel()->is_ghost();
      if (owner_not_updated) {
        // Because the simplification process for a neighbor of a ghost may not produce the same results as the same process
        // on that surfel's home SP, we cannot index other_edge properly for the comm process (half edges indexed by
        // surfel ID and index of half edge in facet starting at facet->GetHalfEdge()). Therefore, we will
        // still use half_edge's index info, but add it to other_edge's home sp in the vector of half_edges needing
        // data comm'd. The functions know to check and switch to the half_edge associated with the smaller indexed surfel
        // if necessary, so it will be handled properly.
        STP_PROC homeCSP = other_edge->get_surfel()->home_sp() - total_fsps;
        m_shell_mesh->get_ghost_half_edge_info_for_sp(homeCSP).push_back(half_edge);
      }
    }
  }
}



/**
 @brief Identifies facet's surfel vertex neighbors and edge types and creates stencil and conduction edge from this
        information.

  @details Simplified 2D implementation can be used when simulation is 2D.
 */
VOID cSHELL_STENCIL_INFO::build_stencils_for_facets() {
  {
    BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
      // only want to build stencils for shell surfels
      if (!facet->is_facet_shell()) { continue; }

      // If using explicit solver, we only want to build stencil for non-ghosted shell surfels
      // If using implicit solver, we want to build stencil if this surfel is not a ghost or has neighbors that are not
      // ghosted.
      BOOLEAN build_for_facet = (sim.use_implicit_shell_solver) 
                              ? facet->is_facet_shell_or_shell_neighbor(TRUE) : !facet->get_surfel()->is_ghost();

      if (build_for_facet) {
        // if not a ghost surfel, build facet now. Otherwise wait until receiving stencil info from ghost surfel's home SP
        if (!facet->get_surfel()->is_ghost()) {
          if (sim.is_2d()) {
            build_stencil_for_2d_facet(facet);
          } else {
            build_stencil_for_facet(facet);
          }
        } else {
          STP_PROC homeCSP = facet->get_surfel()->home_sp() - total_fsps;
          m_ghost_surfel_sps[homeCSP].push_back(facet);
        }
      }
    }
  }

  // Only implicit solver needs to comm ghost surfel stencil info
  if (sim.use_implicit_shell_solver) {
    comm_ghost_surfel_info();
    for (auto& ghost_info : m_ghost_stencil_info) {
      if (sim.is_2d()) {
        build_stencil_for_2d_ghost_facet(ghost_info);
      } else {
        build_stencil_for_ghost_facet(ghost_info);
      }
    }
  }

  // Need to calculate the non-orthogonality for internal edges after all stencils are build since the edge class
  // will not have all info necessary to calculate value when nbr surfel initializes edge (the info will not be
  // available until owner surfel updates edge with unfolded vector)
  if (this->calculateMeshStats() || this->outputVTKInfo() || this->outputStencilDetails()) {
    BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
      if (!facet->is_facet_shell()) { continue; }

      BOOLEAN build_for_facet = (sim.use_implicit_shell_solver) 
                              ? facet->is_facet_shell_or_shell_neighbor(TRUE) : !facet->get_surfel()->is_ghost();
      if (build_for_facet) {
        if (this->outputVTKInfo()) {
          m_mesh_to_vtk_info->addFacet(facet);
        }
      
        if (this->outputStencilDetails()) {
          m_surfel_stencil_details->addFacet(facet);
        }

        if (this->calculateMeshStats()) {
          m_mesh_statistics->addInternalEdgeNonOrthogonality(facet);
        }
      }
    }
  }
}


asINT32 cSHELL_STENCIL_INFO::send_and_receive_counts_to_csps(std::vector<asINT32>& counts_to_send,
                                                             std::vector<asINT32>& counts_to_recv) {
  std::vector<MPI_Request> count_send_requests(total_csps, MPI_REQUEST_NULL);
  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id) {
#if defined(_EXA_IMPI)
      MPI_Issend(&(counts_to_send[source_csp]),
                1,
                eMPI_asINT32,
                source_csp,
                eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                eMPI_csp_comm,
                &(count_send_requests[source_csp]));
#else
      MPI_Isend(&(counts_to_send[source_csp]),
                1, 
                eMPI_asINT32,
                source_csp,
                eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                eMPI_csp_comm,
                &(count_send_requests[source_csp]));
#endif
    }
  }

  asINT32 num_non_zero_counts_recvd = 0;
  ccDOTIMES(remote_csp, total_csps) {
    if (remote_csp != my_csp_proc_id) {
      MPI_Status status;
      MPI_Recv(&(counts_to_recv[remote_csp]), //Blocking receive good enough here.
               1,
               eMPI_asINT32,
               remote_csp,
               eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
               eMPI_csp_comm,
               &status);
      if (counts_to_recv[remote_csp]) {
        num_non_zero_counts_recvd++;
      }
    }
  }

  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id)
      complete_mpi_request_while_processing_cp_messages(&(count_send_requests[source_csp]), MPI_SLEEP_LONG);
  }
  return num_non_zero_counts_recvd;
}

/**
 @brief Processes m_ghost_surfel_sps and sends list of surfel ids ghosted on this CSP to neighbor CSPs

 @details Performs two send/recieve steps:
      1) Sends the number of ghost surfel ids to other CSPs which are the home SP of those surfels
         Recieves that number from each CSP and allocates storage for next send/recieve of actual surfel ids 
      2) Sends the ghost surfel ids to the other CSPs
         Receives the ghosted surfel ids from each CSP
 */
VOID cSHELL_STENCIL_INFO::get_ghost_surfel_ids(asINT32& num_ghost_surfels_total,
                                               asINT32& num_csp_being_sent_ghost_ids,
                                               std::vector<asINT32>& num_ghost_ids_to_send_to_csp,
                                               std::vector<std::vector<uINT32>>& ghost_ids_to_send_to_csp,
                                               asINT32& num_csp_recv_ghost_ids_from,
                                               std::vector<asINT32>& num_ghost_ids_to_recv_from_csp,
                                               std::vector<std::vector<uINT32>>& ghost_ids_recv_from_csp) {
  // Step 1:
  //    - Calculate the number surfels from each CSP that are ghosted here
  //    - Send/Recieve information between CSPs
  num_csp_being_sent_ghost_ids = 0;        // number of different CSPs that this CSPs ghost surfels have homes at 
  num_ghost_surfels_total = 0;       // total number of ghost surfels (total elements in m_ghost_surfel_sps)
  num_ghost_ids_to_send_to_csp.resize(total_csps);
  num_ghost_ids_to_recv_from_csp.resize(total_csps);


  ccDOTIMES(i, total_csps) {
    asINT32 n_ghosts_from_csp = m_ghost_surfel_sps[i].size();
    num_ghost_ids_to_send_to_csp[i] = n_ghosts_from_csp;
    num_ghost_surfels_total += num_ghost_ids_to_send_to_csp[i];
    if (n_ghosts_from_csp) {
      num_csp_being_sent_ghost_ids++;
    }
  }

  if (num_csp_being_sent_ghost_ids > 0) {
    ghost_ids_to_send_to_csp.resize(num_csp_being_sent_ghost_ids);
  }

  asINT32 cur_ind = 0;
  ccDOTIMES(i, total_csps) {
    if (num_ghost_ids_to_send_to_csp[i]) {
      // home_csps_for_ghosts.push_back(i);
      ghost_ids_to_send_to_csp[cur_ind].reserve(num_ghost_ids_to_send_to_csp[i]);
      for (auto facet : m_ghost_surfel_sps[i]) {
        ghost_ids_to_send_to_csp[cur_ind].push_back(facet->get_surfel()->id());
      }
      cur_ind++;
    }
  }
 
  num_csp_recv_ghost_ids_from = send_and_receive_counts_to_csps(num_ghost_ids_to_send_to_csp,
                                                                num_ghost_ids_to_recv_from_csp);

  // Step 2:
  //    - Send/Receive ghost surfel ids from each CSP that needs stencil info about

  if (num_csp_recv_ghost_ids_from)
    ghost_ids_recv_from_csp.resize(num_csp_recv_ghost_ids_from);

  // For each remote SP that sent a non zero surfel count, post a receive request for the vector of surfel ids.
  std::vector<MPI_Request> surfel_ids_receive_requests(num_csp_recv_ghost_ids_from, MPI_REQUEST_NULL);
  
  cur_ind = 0;
  ccDOTIMES(remote_csp, total_csps) {
    if (remote_csp != my_csp_proc_id) {
      if (num_ghost_ids_to_recv_from_csp[remote_csp] > 0) {
        ghost_ids_recv_from_csp[cur_ind].resize(num_ghost_ids_to_recv_from_csp[remote_csp]);
        MPI_Irecv(&ghost_ids_recv_from_csp[cur_ind][0],
                  num_ghost_ids_to_recv_from_csp[remote_csp],
                  eMPI_asINT32,
                  remote_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(surfel_ids_receive_requests[cur_ind]));
        cur_ind++;
      }
    }
  }

  std::vector<MPI_Request> surfel_ids_send_requests(num_csp_being_sent_ghost_ids, MPI_REQUEST_NULL); //Only some SPs get sent IDs
  cur_ind = 0;
  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id) {
      if (num_ghost_ids_to_send_to_csp[source_csp] > 0) {
#if defined(_EXA_IMPI)
        MPI_Issend(&ghost_ids_to_send_to_csp[cur_ind][0],
                   num_ghost_ids_to_send_to_csp[source_csp],
                   eMPI_sINT32,
                   source_csp,
                   eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                   eMPI_csp_comm,
                   &(surfel_ids_send_requests[cur_ind]));
#else
        MPI_Isend(&ghost_ids_to_send_to_csp[cur_ind][0],
                  num_ghost_ids_to_send_to_csp[source_csp],
                  eMPI_asINT32,
                  source_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(surfel_ids_send_requests[cur_ind]));
#endif
        cur_ind++;
      }
    }
  }

  //Complete the surfel ID send requests.
  ccDOTIMES(message_index, num_csp_being_sent_ghost_ids) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_send_requests[message_index]), MPI_SLEEP_LONG);
  }

  //Complete the surfel ID receive request.
  ccDOTIMES(message_index, num_csp_recv_ghost_ids_from) {
    complete_mpi_request_while_processing_cp_messages(&(surfel_ids_receive_requests[message_index]), MPI_SLEEP_LONG);
  }
}


VOID cSHELL_STENCIL_INFO::comm_ghost_surfel_info() {
  asINT32 num_ghost_surfels_total;
  asINT32 num_csp_to_recv_ghost_stencil_info_from;
  asINT32 num_csp_to_send_ghost_stencil_info_to;
  std::vector<asINT32> num_ghost_stencil_info_to_recv_from_csp;
  std::vector<asINT32> num_ghost_stencil_info_to_send_to_csp;
  std::vector<std::vector<uINT32>> ids_of_ghosted_stencils_to_recv;
  std::vector<std::vector<uINT32>> ids_of_ghosted_stencils_to_send;
  
  get_ghost_surfel_ids(num_ghost_surfels_total, num_csp_to_recv_ghost_stencil_info_from, 
    num_ghost_stencil_info_to_recv_from_csp, ids_of_ghosted_stencils_to_recv,
    num_csp_to_send_ghost_stencil_info_to, num_ghost_stencil_info_to_send_to_csp,
    ids_of_ghosted_stencils_to_send);

  // Now each CSP has a list of its surfels that it needs to comm it the stencil info for
  // It is in the form of an array of vectors where each vector corresponds to a CSP that needs the info
  // and the elements in the vector being the surfel ids to transfer stencil info for
  
  // The next step is to create a sGHOST_SURFEL_STENCIL_INFO for each ghosted surfel and send it to each CSP that
  // contains the ghosted surfel

  std::vector<sFLOAT*> ghost_stencil_info_to_send;
  if (num_csp_to_send_ghost_stencil_info_to > 0)
    ghost_stencil_info_to_send.resize(num_csp_to_send_ghost_stencil_info_to,NULL);

  std::vector<asINT32> size_of_packet_to_send_to_csp(total_csps,0);

  asINT32 cur_ind = 0;
  ccDOTIMES(i, total_csps) {
    if (num_ghost_stencil_info_to_send_to_csp[i]) {
      std::vector<sGHOST_SURFEL_STENCIL_INFO> ghost_surfel_info_tmp;
      ghost_surfel_info_tmp.reserve(num_ghost_stencil_info_to_send_to_csp[i]);
      asINT32 total_size = 0;
      for (auto& surf_id : ids_of_ghosted_stencils_to_send[cur_ind]) {
        ghost_surfel_info_tmp.emplace_back(m_shell_mesh->get_facet_from_surfel_id(surf_id)->get_surfel());
        ghost_surfel_info_tmp.back().add_send_size(total_size);
      }
      
      size_of_packet_to_send_to_csp[i] = total_size;
      sFLOAT *ptrToData = new sFLOAT[total_size];
      ghost_stencil_info_to_send[cur_ind++] = ptrToData;
      for (auto& ghost_info : ghost_surfel_info_tmp) {
        ghost_info.fill_send_buffer(ptrToData);
      }
    }
  }

  std::vector<asINT32> size_of_packet_to_recv_from_csp(total_csps,0);
  asINT32 num_csp_to_recv_ghost_stencil_info_from_check = send_and_receive_counts_to_csps(size_of_packet_to_send_to_csp,
                                                                                      size_of_packet_to_recv_from_csp);

  stencil_assert(num_csp_to_recv_ghost_stencil_info_from_check == num_csp_to_recv_ghost_stencil_info_from);

  std::vector<sFLOAT*> ghost_stencil_recv_buffers;
  if (num_csp_to_recv_ghost_stencil_info_from) {
    ghost_stencil_recv_buffers.resize(num_csp_to_recv_ghost_stencil_info_from, NULL);
  }

  // For each remote SP that sent a non zero surfel count, post a receive request for the vector of surfel ids.
  std::vector<MPI_Request> stencil_info_receive_requests(num_csp_to_recv_ghost_stencil_info_from, MPI_REQUEST_NULL);
  
  cur_ind = 0;
  ccDOTIMES(remote_csp, total_csps) {
    if (remote_csp != my_csp_proc_id) {
      if (size_of_packet_to_recv_from_csp[remote_csp] > 0) {
        ghost_stencil_recv_buffers[cur_ind] = new sFLOAT[size_of_packet_to_recv_from_csp[remote_csp]];
        MPI_Irecv(ghost_stencil_recv_buffers[cur_ind],
                  size_of_packet_to_recv_from_csp[remote_csp],
                  eMPI_sFLOAT,
                  remote_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(stencil_info_receive_requests[cur_ind]));
        cur_ind++;
      }
    }
  }

  std::vector<MPI_Request> stencil_info_send_requests(num_csp_to_send_ghost_stencil_info_to, MPI_REQUEST_NULL); 
  cur_ind = 0;
  ccDOTIMES(source_csp, total_csps) {
    if (source_csp != my_csp_proc_id) {
      if (size_of_packet_to_send_to_csp[source_csp] > 0) {
#if defined(_EXA_IMPI)
        MPI_Issend(ghost_stencil_info_to_send[cur_ind],
                   size_of_packet_to_send_to_csp[source_csp],
                   eMPI_sFLOAT,
                   source_csp,
                   eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                   eMPI_csp_comm,
                   &(stencil_info_send_requests[cur_ind]));
#else
        MPI_Isend(ghost_stencil_info_to_send[cur_ind],
                  size_of_packet_to_send_to_csp[source_csp],
                  eMPI_sFLOAT,
                  source_csp,
                  eMPI_SHELL_STENCIL_GHOST_COMM_TAG,
                  eMPI_csp_comm,
                  &(stencil_info_send_requests[cur_ind]));
#endif
        cur_ind++;
      }
    }
  }

  //Complete the surfel ID send requests.
  ccDOTIMES(message_index, num_csp_to_send_ghost_stencil_info_to) {
    complete_mpi_request_while_processing_cp_messages(&(stencil_info_send_requests[message_index]), MPI_SLEEP_LONG);
  }

  //Complete the surfel ID receive request.
  ccDOTIMES(message_index, num_csp_to_recv_ghost_stencil_info_from) {
    complete_mpi_request_while_processing_cp_messages(&(stencil_info_receive_requests[message_index]), MPI_SLEEP_LONG);
  }

  ccDOTIMES(i, num_csp_to_send_ghost_stencil_info_to) {
    delete[] ghost_stencil_info_to_send[i];
  }


  if (num_csp_to_recv_ghost_stencil_info_from) {
    m_ghost_stencil_info.resize(num_ghost_surfels_total);
    asINT32 cur_stencil_ind = 0;
    cur_ind = 0;
    ccDOTIMES(cur_csp, total_csps) {
      if ((cur_csp != my_csp_proc_id) && num_ghost_stencil_info_to_recv_from_csp[cur_csp]) {
        sFLOAT* curBufferLoc = ghost_stencil_recv_buffers[cur_ind];
        ccDOTIMES(j, num_ghost_stencil_info_to_recv_from_csp[cur_csp]) {
          m_ghost_stencil_info[cur_stencil_ind++].expand_recv_buffer(curBufferLoc);
        }
        delete[] ghost_stencil_recv_buffers[cur_ind];
        cur_ind++;
      }
    }
  }
}


/**
 @brief Iterates through facets in cSHELL_MESH and identifies and marks which will be included in
        simplified mesh.

 @details All surfels containing shell layers or neighboring a surfel with shell layers are kept.
          Returns TRUE if shell surfel is found and FALSE otherwise. This ensures that the code will
          not try to simplify a mesh that doesnt contain any shell surfel (undefined behavior).
 */
BOOLEAN cSHELL_STENCIL_INFO::mark_facets_to_keep() {
  // will only be keeping facets associated with shell surfels and their edge neighbors
  // we will therefore set facet's flag to 1 for only those surfels and the rest to 0.
  BOOLEAN shell_found = FALSE;
  BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
    if (facet->is_facet_shell_or_shell_neighbor()) {
      facet->add_flag(FACET_FLAGS::KEEP_FLAG);
      shell_found = TRUE;
    } else {
      facet->add_flag(FACET_FLAGS::DISCARD_FLAG);
    }
  }
  return shell_found;
}

/**
 @brief Allocates new cSHELL_MESH_INFO object and adds facets to keep to it.
 
 @details Facets are added using a subset of the original facet's vertices, which avoids two
          neighboring facets sharing multiple edges without altering the facet shapes. Newly allocated
          cSHELL_MESH_INFO object is stored in the unique_ptr object holding the full mesh. The new mesh
          will contain a subset of the faces in the simulation and as a result the Shell indices will
          no longer correspond to face indices. After all facets are added. BuildShells is used to
          build the mesh.
 */
VOID cSHELL_STENCIL_INFO::simplify_facets() {
  // All edges that are unnecessarily cut will be merged.
  std::unique_ptr<cSHELL_MESH> simplified_mesh = std::make_unique<cSHELL_MESH>();

  iBREP_BODY iBody = simplified_mesh->NewBody();
  cSHELL_MESH::BODY m_body = simplified_mesh->GetBody(iBody);

  std::vector<BOOLEAN> has_facets_in_shell(g_num_faces,FALSE);
  BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
    if (facet->marked_to_keep()) {
      has_facets_in_shell[facet->get_surfel()->m_face_index] = TRUE;
    }
  }

  ccDOTIMES(i,g_num_faces) {
    if (!has_facets_in_shell[i]) {
      continue;
    }

    iBREP_SHELL iShell = simplified_mesh->NewShell(BREP_INVALID_IFACET);
    BREP_DO_FACETS(facet, m_shell_mesh, cSHELL_MESH) {
      if (facet->get_surfel()->m_face_index == i) {
        reduce_facet_edges(facet, simplified_mesh.get(), iShell);
      }
    }
    cSHELL_MESH::SHELL shell = simplified_mesh->GetShell(iShell);
    m_body->AddShell(shell);
  }

  m_shell_mesh.swap(simplified_mesh);
}

/**
 @brief Identifies the subset of vertices used to construct the facet in the simplified mesh.
 
 @details A vector of these vertex indices are passed to the cSHELL_MESH object to add the facet
          to the mesh.
 */
VOID cSHELL_STENCIL_INFO::reduce_facet_edges(cSHELL_MESH::FACET facet, cSHELL_MESH* stencil_mesh,
    iBREP_SHELL shell_index) {
  // if the facet isnt a shell surfel or neighboring one no need to keep it
  if (!(facet->marked_to_keep())) {
    return;
  }

  std::vector<iBREP_VERTEX> reduced_vertices;

  // do not attempt to reduce any edges on facets that are isolated from surface (this is likely the result
  // of a disc error and can produce errors if simplified using the method implemented)
  if (!facet->has_edge_neighbors()) {
    BREP_FACET_DO_VERTICES(vertex, facet, cSHELL_MESH) {
      reduced_vertices.push_back(vertex->GetIndex());
    }

    // We dont want to set this facet as the seed since it's isolated from the rest of the surface
    stencil_mesh->add_surfel(facet->get_surfel(), reduced_vertices, shell_index);
    // if this was supposed to be the seed facet, return true so that the next facet will be
    return;
  }

  cSHELL_MESH::HALF_EDGE start_edge = facet->GetHalfEdge();
  cSHELL_MESH::HALF_EDGE prev_edge = start_edge->GetPrev();

  while (prev_edge->can_edges_be_combined(start_edge)) {
    start_edge = prev_edge;
    prev_edge = start_edge->GetPrev();
  }

  cSHELL_MESH::HALF_EDGE cur_edge = start_edge;
  while (TRUE) {
    cSHELL_MESH::HALF_EDGE next_edge = cur_edge->GetNext();
    while (cur_edge->can_edges_be_combined(next_edge)) {
      next_edge = next_edge->GetNext();
    }
    // add tail vertex of cur_edge to reduced vertex list
    reduced_vertices.push_back(cur_edge->GetTailVertex()->GetIndex());
    cur_edge = next_edge;
    if (cur_edge == start_edge) {
      break;
    }
  }
  stencil_mesh->add_surfel(facet->get_surfel(), reduced_vertices, shell_index);
}

/**
 @brief Finalizes the stencil for a shell surfel that only contains boundary edges.
 
 @details When a facet only contains boundary edges (including intersection) and no internal edges,
          there is no need for a gradient to be calculated, so this will set all lsq coefficients to 0
          and skip the addition of vertex neighbors to the stencil (if using cell centered gradients).
          A simerr will also be reported with infomation about the surfel and its edges.
 */
VOID finalize_shell_surfel_no_internal_edges(cSHELL_MESH::FACET facet, BOOLEAN print_simerr = TRUE) {
  SURFEL surfel = facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = surfel->shell_conduction_data();
  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());

  // Iterate through stencil neighbors and set lsq coeff to {0,0} (if using cell centered gradients
  // and count edge types for simerr report
  const asINT32 start_index = root_shell_data->get_stencil_start_index();
  const asINT32 end_index = root_shell_data->stencil_info_end_index;
  asINT32 adiabatic_edges = 0;
  asINT32 fixed_t_q_edges = 0;
  asINT32 intersection_edges = 0;
  for (asINT32 stencil_i = start_index; stencil_i < end_index; stencil_i++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = g_shell_conduction_edges.get_nbr_stencil(stencil_i);
    uINT8 edge_type = stencil_surf->stencil_type();
    if (edge_type == CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE) {
      adiabatic_edges++;
    } else if (edge_type == CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE) {
      intersection_edges++;
    } else {
      fixed_t_q_edges++;
    }
    dFLOAT lsq_coeff[2] = {0.,0.};
    stencil_surf->set_lsq_coeff(lsq_coeff);
  }

  sdFLOAT centroid[3];
  vcopy(centroid,surfel->centroid);
  sdFLOAT facet_area = (sdFLOAT)get_shell_mesh()->GetFacetArea(facet->GetIndex());
  simerr_report_error_code(SP_EER_ISOLATED_SHELL_SURFEL, surfel->scale(), centroid,
                           surfel->id(),
                           facet_area,
                           surfel->m_face_index,
                           adiabatic_edges,
                           fixed_t_q_edges,
                           intersection_edges,
                           "");
}


/**
 @brief Computes lsq weight information for a shell stencil surfel from the unfolded centroid vector.

 @details Used when creating stencils from cSHELL_CONDUCTION_STENCIL_NEIGHBOR objects for a cell centered
          LSQ implementation (not an edge centered one).
 */
VOID compute_lsq_weight_info(SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil,
    dFLOAT &weight, dFLOAT dxy_vec[2]) {
  // unfolded centroid distance is already in root surfel's scale and local csys
  // so there is no need to rotate or scale it
  const sdFLOAT* vec_btw_centroids = stencil->get_unfolded_centroid();
  dFLOAT dist = vlength2(vec_btw_centroids);
#if BUILD_DOUBLE_PRECISION
  weight = 1.0 / (dist + DFLOAT_EPSILON);
#else
  weight = 1.0 / (dist + SFLOAT_EPSILON);
#endif
  vcopy2(dxy_vec, vec_btw_centroids);
}

/**
 @brief Adds stencil info to lsq matrix stored as a symmetric matrix (only 3 elements)
 */
VOID add_to_lsq_matrix(dFLOAT lsq_mat[3], dFLOAT weight, dFLOAT dxy_vec[2]) {
  lsq_mat[0] += weight * dxy_vec[0] * dxy_vec[0];
  lsq_mat[1] += weight * dxy_vec[0] * dxy_vec[1];
  lsq_mat[2] += weight * dxy_vec[1] * dxy_vec[1];
}

/**
 @brief Sets reports simerr when LSQ matrix is degenerate.
 */
VOID report_lsq_inversion_failure(cSHELL_MESH::FACET root_facet, const dFLOAT* a_mat, const dFLOAT determinant) {
  SURFEL surfel = root_facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = surfel->shell_conduction_data();

  // Report simerr
  asINT32 n_int_neighbors = root_shell_data->num_internal_edges();
  asINT32 n_boundary_edges = root_shell_data->num_boundary_edges();
  asINT32 n_vert_neighbors = root_shell_data->num_vertex_neighbors();

  sdFLOAT centroid[3];
  vcopy(centroid,surfel->centroid);
  sdFLOAT facet_area = (sdFLOAT)get_shell_mesh()->GetFacetArea(root_facet->GetIndex());
  if (sim.is_2d()) {
    simerr_report_error_code(SP_EER_SHELL_LEAST_SQUARES_INVERSION_FAILED, surfel->scale(), centroid,
                            surfel->id(),
                            facet_area,
                            surfel->m_face_index,
                            n_int_neighbors,
                            n_boundary_edges,
                            n_vert_neighbors,
                            determinant,
                            ((sdFLOAT)(a_mat[0])),
                            0.,
                            0.,
                            0.,
                            "1D 1x1 matrix for 2D sim");
  } else {
    simerr_report_error_code(SP_EER_SHELL_LEAST_SQUARES_INVERSION_FAILED, surfel->scale(), centroid,
                            surfel->id(),
                            facet_area,
                            surfel->m_face_index,
                            n_int_neighbors,
                            n_boundary_edges,
                            n_vert_neighbors,
                            determinant,
                            ((sdFLOAT)(a_mat[0])),
                            ((sdFLOAT)(a_mat[1])),
                            ((sdFLOAT)(a_mat[1])),
                            ((sdFLOAT)(a_mat[2])),
                            "");
  }
}

/**
 @brief Builds stencil for isolated shell surfel that does not have any edge neighbors.

 @details Resulting stencil will be empty ensuring a gradient of zero and only quasi1D solver contributing
          to the change in temperature. Any facets without edge neighbors will use this function rather than
          build_stencil_for_facet to avoid any issues that may arrise with degenerate matrices. This situation
          should be uncommon and most likely will be the result of either a failure in the disc, or a
          user error.
*/
VOID cSHELL_STENCIL_INFO::build_stencil_for_isolated_facet(cSHELL_MESH::FACET root_facet, BOOLEAN print_simerr) {
  SURFEL surfel = root_facet->get_surfel();
  sdFLOAT centroid[3];
  vcopy(centroid, surfel->centroid);
  sdFLOAT facet_area = (sdFLOAT)get_shell_mesh()->GetFacetArea(root_facet->GetIndex());
  if (print_simerr) {
    simerr_report_error_code(SP_EER_ISOLATED_SHELL_SURFEL, surfel->scale(), centroid,
                             surfel->id(),
                             facet_area,
                             surfel->m_face_index,
                             (sim.is_2d() ? 2 : root_facet->NumVertices()),
                             0,
                             0,
                             "Shell has no shared edges, initializing empty stencil");
  }

  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = surfel->shell_conduction_data();
  // Shell surfel will not have any edge neighbors or vertex neighbors, resulting in a gradient of (0.,0.), and
  // the only energy contribution coming from the quasi-1D solver
  root_shell_data->set_stencil_info_internal_edge_index(g_shell_conduction_edges.total_num_stencils());
  root_shell_data->set_stencil_info_boundary_edge_index(g_shell_conduction_edges.total_num_stencils());
  root_shell_data->set_stencil_info_vertex_nbr_index(g_shell_conduction_edges.total_num_stencils());
  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());
}

uINT32 get_boundary_edge_info_from_half_edge(cSHELL_MESH::HALF_EDGE root_edge, HALF_EDGE_VECTOR& nbr_half_edges) {
  if (root_edge->is_adiabatic_boundary()) {
    return CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE;
  } else if (root_edge->is_dyn_data_boundary()) {
    nbr_half_edges.push_back(root_edge->get_boundary_or_intersection_edge_nbr());
    return CONDUCTION_STENCIL_FLAGS::DYN_DATA_BOUNDARY_EDGE;
  } else if (root_edge->is_fixed_temp_boundary()) {
    nbr_half_edges.push_back(root_edge->get_boundary_or_intersection_edge_nbr());
    if (root_edge->nbr_he_is_pair()) {
      nbr_half_edges.push_back(root_edge->get_second_edge_nbr_in_pair());
    }
    return CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE;
  }
  // else it is an intersection edge
  cSHELL_MESH::HALF_EDGE loopingHe = root_edge->get_boundary_or_intersection_edge_nbr();
  do {
    nbr_half_edges.push_back(loopingHe);
    loopingHe = loopingHe->get_boundary_or_intersection_edge_nbr();
  } while (loopingHe != root_edge);

  return CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE;
}

/**
 @brief Identifies vertex neighbors and edge types for a facet's surfel and allocates 
        cSHELL_CONDUCTION_STENCIL_NEIGHBOR and cCONDUCTION_EDGE_BASE objects to represent them.

 @details This implementation is only used for 3D simulations. First identifies internal edges,
          followed by boundary edges and lastly will search for vertex neighbors not sharing
          an edge with the root facet. A cSHELL_CONDUCTION_STENCIL_NEIGHBOR object
          is allocated for all vertex neighbor to store stencil information while a cCONDUCTION_EDGE_BASE
          object is allocated for any edge neighbors as well. A pointer to this cCONDUCTION_EDGE_BASE
          is stored in the cSHELL_CONDUCTION_STENCIL_NEIGHBOR object allowing for simplified
          access to to a cCONDUCTION_EDGE_BASE shared between multiple shell surfels (internal and
          intersection edges specifically).
 */
VOID cSHELL_STENCIL_INFO::build_stencil_for_facet(cSHELL_MESH::FACET root_facet) {
  if (this->calculateMeshStats()) {
    this->startNewFacetForStats(root_facet);
  }

  if (!root_facet->has_edge_neighbors()) {
    return build_stencil_for_isolated_facet(root_facet);
  }

  SURFEL root_surfel = root_facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = root_surfel->shell_conduction_data();
  std::vector<cSHELL_MESH::FACET> facets_in_stencil;
  facets_in_stencil.push_back(root_facet);

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
  HALF_EDGE_VECTOR half_edges_in_facet;
#endif  

  dFLOAT mat_a[3];
  memset(mat_a, 0.0, sizeof(mat_a));
  dFLOAT root_area = root_surfel->area;

  root_shell_data->set_stencil_info_internal_edge_index(g_shell_conduction_edges.total_num_stencils());

  // start with internal edge neighbors first. These will also be used when looping through
  // faces to calculate fluxes between shell layers. This will be followed by boundary edges and lastly
  // vertex neighbors.
  {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is not internal
      if (!half_edge->is_internal_edge()) { continue; }

      cSHELL_MESH::HALF_EDGE int_nbr_edge = half_edge->get_internal_edge_nbr();
      BOOLEAN is_switched = !half_edge->are_layers_aligned(int_nbr_edge);

      // Check if the edge is highly non-orthogonal, prints simerr if this surfel is the owner and not a ghost, and
      // continue to exclude it from the simulation
      cSHELL_MESH::HALF_EDGE int_edge_owner_he = (root_surfel->id() < int_nbr_edge->get_surfel()->id())
                                                ? half_edge : int_nbr_edge;
      const dFLOAT *unfolded_vec = int_edge_owner_he->get_int_edge_unfold_vec();
      dFLOAT dotProd = vdot2(unfolded_vec, int_edge_owner_he->local_normal());
      dotProd /= vlength2(unfolded_vec);
      if (dotProd < DFLOAT_EPSILON) {
        if (half_edge == int_edge_owner_he && !root_surfel->is_ghost()) {
          SURFEL owner_surfel = int_edge_owner_he->get_surfel();
          SURFEL nbr_surfel = (owner_surfel == root_surfel) ? int_nbr_edge->get_surfel() : root_surfel;
          sdFLOAT nonOrthoAngle = acos(MIN(dotProd,1.0))*180./M_PI;
          sdFLOAT centroid[3];
          vcopy(centroid, owner_surfel->centroid);
          cSHELL_MESH::sBG_VECTOR3 he_vec = m_shell_mesh->HalfEdgeVec(int_edge_owner_he);
          auto heLen = he_vec.Length();
          simerr_report_error_code(SP_EER_HIGHLY_NON_ORTHOGONAL_SHELL_EDGE, owner_surfel->scale(), centroid,
                                   owner_surfel->id(), nbr_surfel->id(), heLen, nonOrthoAngle);
        }
        half_edge->set_edge_to_ignored();
        continue;
      }


      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_internal_edge(half_edge, int_nbr_edge, is_switched);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, int_nbr_edge, CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
      }
 
#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
      half_edges_in_facet.push_back(half_edge);
#endif  

      dFLOAT weight, dxy_vec[2];
      compute_lsq_weight_info(stencil_surf, weight, dxy_vec);
      add_to_lsq_matrix(mat_a, weight, dxy_vec);

      vmul2(dxy_vec, weight);
      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(dxy_vec);

      facets_in_stencil.push_back(int_nbr_edge->GetFacet());
    }
  }

  root_shell_data->set_stencil_info_boundary_edge_index(g_shell_conduction_edges.total_num_stencils());

  {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is internal
      if (half_edge->is_internal_edge()) { continue; }

      HALF_EDGE_VECTOR nbr_half_edges;
      uINT32 edge_type = get_boundary_edge_info_from_half_edge(half_edge, nbr_half_edges);

      // the vector to centroid for boundary edges is just the half_edge->m_normal * half_edge->m_normal_dist;
      dFLOAT unfolded_vec[2];
      vscale2(unfolded_vec, half_edge->normal_distance(), half_edge->local_normal());

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_boundary_edge(half_edge, nbr_half_edges, 
                                                                            edge_type, unfolded_vec);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, nbr_half_edges, (uINT32)(stencil_surf->stencil_type()));
      }

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
      half_edges_in_facet.push_back(half_edge);
#endif  

      dFLOAT weight, dxy_vec[2];
      compute_lsq_weight_info(stencil_surf, weight, dxy_vec);
      add_to_lsq_matrix(mat_a, weight, dxy_vec);
      vmul2(dxy_vec, weight);
      if (edge_type == CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE) {
        // if adiabatic, the b vector will always be zero in the lsq matrix, so set it to zero
        // before storing vector
        vzero2(dxy_vec);
      }
      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(dxy_vec);
      
      // Add any intersection neighbors that have the same number of layers to ensure
      // they are not added as vertex neighbors
      if (edge_type & CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE) {
        ccDOTIMES(nbr_i, nbr_half_edges.size()) {
          if (nbr_half_edges[nbr_i]->num_layers_at_edge() == half_edge->num_layers_at_edge()) {
            facets_in_stencil.push_back(nbr_half_edges[nbr_i]->GetFacet());
          }
        }
      }
    }
  }
  
  root_shell_data->set_stencil_info_vertex_nbr_index(g_shell_conduction_edges.total_num_stencils());

  // If the stencil does not contain any internal edges, a simerr will be reported with the information
  // about the neighbors it has. In this case, there is no need for the surfel to calculate a gradient,
  // so the lsq coefficients will be set to zero and no vertex neighbors will be included in its stencil
  if (root_shell_data->num_internal_edges() == 0) {
    finalize_shell_surfel_no_internal_edges(root_facet);
#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
    root_facet->dump_edge_verification_info(half_edges_in_facet);
#endif
    return;
  }

  // To identify all vertex neighbors, we will iterate through all facets sharing a vertex with the root facet.
  // If the facet has the same number of layers, and isn't an edge neighbor, we will attempt to identify
  // the unfolded vector between their centroids using the cSURFEL_UNFOLDER class. If it is able to calculate the
  // unfolded centroid without crossing any non-internal edges, this facet will be added as a vertex neighbor
  {
    asINT32 numLayersRoot = root_facet->num_layers();
    // BREP_FACET_DO_VERTICES(vertex, root_facet, cSHELL_MESH) {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      cSHELL_MESH::VERTEX vertex = half_edge->GetVertex();
      BREP_VERTEX_DO_FACETS(nbr_facet, vertex, cSHELL_MESH) {
        // check if nbr_facet is a shell with the same number of layers and not already included in the stencil and
        // skip if it has
        if (!nbr_facet->is_facet_shell())
          continue;
        if (nbr_facet->num_layers() != numLayersRoot)
          continue;
        if (std::find(facets_in_stencil.begin(), facets_in_stencil.end(), nbr_facet) != facets_in_stencil.end())
          continue; 
        
        // Attempt to unfold surfel. If it fails, skip this vertex neighbor
        dFLOAT unfolded_vec[2];
        cITERATIVE_UNFOLDER unfolder(root_facet, nbr_facet, vertex);
        if (!unfolder.unfoldingSuccessful()) { continue; }
        unfolder.unfoldedVector(unfolded_vec);
        facets_in_stencil.push_back(nbr_facet);
        SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_vertex_neighbor_stencil(nbr_facet->get_surfel(),
                                                                                        unfolded_vec);

        dFLOAT weight, dxy_vec[2];
        compute_lsq_weight_info(stencil_surf, weight, dxy_vec);
        add_to_lsq_matrix(mat_a, weight, dxy_vec);

        vmul2(dxy_vec, weight);

        // store b vector in stencil's lsq coefficient variable.
        stencil_surf->set_lsq_coeff(dxy_vec);
      }
    }
  }
  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());

  // invert a matrix and calculate lsq_coefficients using b vectors stored in each cSHELL_CONDUCTION_STENCIL_NEIGHBOR
  dFLOAT mat_a_full[2][2] = {{mat_a[0], mat_a[1]}, {mat_a[1], mat_a[2]}};
  dFLOAT mat_a_inv[2][2];
  std::pair<BOOLEAN, dFLOAT> is_invertible_and_determinant = invert_full_matrix<2>(mat_a_full, mat_a_inv);

  if (!is_invertible_and_determinant.first) {
    report_lsq_inversion_failure(root_facet, mat_a, is_invertible_and_determinant.second);
    memset(mat_a_inv, 0.0, sizeof(mat_a_inv));
  }

  // calculate lsq_coeff from mat_a_inv * b_vec and store in cSHELL_CONDUCTION_STENCIL_NEIGHBOR
  const asINT32 start_index = root_shell_data->get_stencil_start_index();
  const asINT32 end_index = root_shell_data->stencil_info_end_index;
  for (asINT32 stencil_i = start_index; stencil_i < end_index; stencil_i++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = g_shell_conduction_edges.get_nbr_stencil(stencil_i);
    const sdFLOAT* mat_b = stencil_surf->get_lsq_coeff();
    dFLOAT lsq_coeff[2] = {mat_a_inv[0][0]*((dFLOAT)mat_b[0]) + mat_a_inv[0][1]*((dFLOAT)mat_b[1]),
                            mat_a_inv[1][0]*((dFLOAT)mat_b[0]) + mat_a_inv[1][1]*((dFLOAT)mat_b[1])};
    stencil_surf->set_lsq_coeff(lsq_coeff);
  }

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
  root_facet->dump_edge_verification_info(half_edges_in_facet);
#endif
}

/**
 @brief Identifies vertex neighbors and edge types for a 2D facet's surfel and allocates 
        cSHELL_CONDUCTION_STENCIL_NEIGHBOR and cCONDUCTION_EDGE_BASE objects to represent them.

 @details Simplified stencil building implementation for 2D facets. Uses all next-neighbors of edges
          in LSQ stencil, which results in stencils with up to 4 neighbors.
 */
VOID cSHELL_STENCIL_INFO::build_stencil_for_2d_facet(cSHELL_MESH::FACET root_facet) {
  if (this->calculateMeshStats()) {
    this->startNewFacetForStats(root_facet);
  }

  if (!root_facet->has_edge_neighbors()) {
    return build_stencil_for_isolated_facet(root_facet);
  }

  SURFEL root_surfel = root_facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = root_surfel->shell_conduction_data();
  typedef std::pair<cSHELL_MESH::HALF_EDGE,cSHELL_MESH::HALF_EDGE> INTERNAL_EDGE_PAIR; 
  // std::vector<INTERNAL_EDGE_PAIR> internal_edges_in_stencil;

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
  HALF_EDGE_VECTOR half_edges_in_facet;
#endif  

  dFLOAT mat_a = 0.0;
  dFLOAT root_area = root_surfel->area;

  root_shell_data->set_stencil_info_internal_edge_index(g_shell_conduction_edges.total_num_stencils());
  // start with internal edge neighbors first. These will also be used when looping through
  // faces to calculate fluxes between shell layers. This will be followed by boundary edges and lastly
  // vertex neighbors.
  {
    BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // Skip if the edge is not internal
      if (!half_edge->is_internal_edge()) { continue; }
    
      cSHELL_MESH::HALF_EDGE int_nbr_edge = half_edge->get_internal_edge_nbr();
      BOOLEAN is_switched = !half_edge->are_layers_aligned(int_nbr_edge);

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_internal_edge(half_edge, int_nbr_edge, is_switched);
    
      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, int_nbr_edge, CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
      }
     
#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
      half_edges_in_facet.push_back(half_edge);
#endif  

      dFLOAT weight, dxy_vec[2];
      compute_lsq_weight_info(stencil_surf, weight, dxy_vec);
      mat_a += weight * dxy_vec[0] * dxy_vec[0];
      vmul2(dxy_vec, weight);
      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(dxy_vec);
    }
  }

  root_shell_data->set_stencil_info_boundary_edge_index(g_shell_conduction_edges.total_num_stencils());

  {
    BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // Skip if the edge is internal
      if (half_edge->is_internal_edge()) { continue; }

      HALF_EDGE_VECTOR nbr_half_edges;
      uINT32 edge_type = get_boundary_edge_info_from_half_edge(half_edge, nbr_half_edges);

      // the vector to centroid for boundary edges is just the half_edge->m_normal * half_edge->m_normal_dist;
      dFLOAT unfolded_vec[2];
      vscale2(unfolded_vec, half_edge->normal_distance(), half_edge->local_normal());

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_boundary_edge(
        half_edge, nbr_half_edges, edge_type, unfolded_vec);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, nbr_half_edges, (uINT32)(stencil_surf->stencil_type()));
      }

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
      half_edges_in_facet.push_back(half_edge);
#endif

      dFLOAT weight, dxy_vec[2];
      compute_lsq_weight_info(stencil_surf, weight, dxy_vec);
      mat_a += weight * dxy_vec[0] * dxy_vec[0];

      vmul2(dxy_vec, weight);
      // if adiabatic, the b vector will always be zero in the lsq matrix, so set it to zero before storing vector
      if (edge_type == CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE) {
        vzero2(dxy_vec)
      }
      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(dxy_vec);
    }
  }

  root_shell_data->set_stencil_info_vertex_nbr_index(g_shell_conduction_edges.total_num_stencils());

  // If stencil does not contain any internal edges, a simerr will be reported with the information
  // about the neighbors it has. In this case, there is no need for the surfel to calculate a gradient,
  // so the lsq coefficients will be set to zero and no vertex neighbors will be included in its stencil
  if (root_shell_data->num_internal_edges() == 0) {
    finalize_shell_surfel_no_internal_edges(root_facet);
#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
    root_facet->dump_edge_verification_info(half_edges_in_facet);
#endif
    return;
  }

  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());

  // invert a matrix and calculate lsq_coefficients using b vectors stored in each cSHELL_CONDUCTION_STENCIL_NEIGHBOR
  dFLOAT mat_a_inv = 0.;

  if (fabs(mat_a) < g_matrix_determinant_threshold) {
    dFLOAT determinant = (fabs(mat_a) > DFLOAT_EPSILON) ? 1./mat_a : 0.0;
    report_lsq_inversion_failure(root_facet, &mat_a, determinant);
  } else {
    mat_a_inv = 1./mat_a;
  }

  // calculate lsq_coeff from mat_a_inv * b_vec and store in cSHELL_CONDUCTION_STENCIL_NEIGHBOR
  const asINT32 start_index = root_shell_data->get_stencil_start_index();
  const asINT32 end_index = root_shell_data->stencil_info_end_index;
  for (asINT32 stencil_i = start_index; stencil_i < end_index; stencil_i++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = g_shell_conduction_edges.get_nbr_stencil(stencil_i);
    const sdFLOAT* mat_b = stencil_surf->get_lsq_coeff();
    stencil_assert(fabs(mat_b[1]) < SDFLOAT_EPSILON);
    dFLOAT lsq_coeff[2] = {mat_a_inv*((dFLOAT)mat_b[0]), 0.0};
    stencil_surf->set_lsq_coeff(lsq_coeff);
  }

#if (ENABLE_LOGGING && DEBUG_SHELL_SURFEL_VERIFICATION)
  root_facet->dump_edge_verification_info(half_edges_in_facet);
#endif
}

/**
 @brief Builds stencil for ghosted shell surfels when the implicit shell solver is in use

 @details This ensures that the stencil for ghosted surfels is equalivalent to that of its non-ghosted
          counterpart. This is done by first building the stencils for the non-ghosted surfels, comming the
          stencil info to all CSPs containing ghosts of any of those surfels and then using this function
          to build the stencil for the ghosted surfels on this CSP using the stencil information
          received from the ghosted surfel's home CSP.
*/
VOID cSHELL_STENCIL_INFO::build_stencil_for_ghost_facet(sGHOST_SURFEL_STENCIL_INFO& ghost_info) {
  uINT32 surfel_id = ghost_info.ghost_surfel_id;

  cSHELL_MESH::FACET root_facet = m_shell_mesh->get_facet_from_surfel_id(surfel_id);
 
  // CSP with ghosted facet has all info necessary to identify facets without edge neighbors, so there is no
  // need for ghost_info when calling build_stencil_for_isolated_facet in this case.  
  if (!root_facet->has_edge_neighbors()) {
    return build_stencil_for_isolated_facet(root_facet, FALSE);
  }

  SURFEL root_surfel = root_facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = root_surfel->shell_conduction_data();

  if (this->calculateMeshStats()) {
    this->startNewFacetForStats(root_facet);
  }

  root_shell_data->set_stencil_info_internal_edge_index(g_shell_conduction_edges.total_num_stencils());

  asINT32 cur_ghost_info_ind = 0;

  // start with internal edge neighbors first. These will also be used when looping through
  // faces to calculate fluxes between shell layers. This will be followed by boundary edges and lastly
  // vertex neighbors.
  {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is not internal
      if (!half_edge->is_internal_edge()) { continue; }

      cSHELL_MESH::HALF_EDGE int_nbr_edge = half_edge->get_internal_edge_nbr();

      sGHOST_SURFEL_STENCIL_INFO_ELEMENT& ghost_element = ghost_info.stencil_nbrs[cur_ghost_info_ind];

      // Its possible that this edge was skipped if it is highly non-orthogonal, so reset the edge to nothing and
      // continue on to the next edge if that is the case
      if (int_nbr_edge->get_surfel()->id() != ghost_element.m_nbr_surfel_id) {
        half_edge->set_edge_to_ignored();
        continue;
      }
      cur_ghost_info_ind++;
      stencil_assert(ghost_element.m_stencil_type & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);

      BOOLEAN is_switched = !half_edge->are_layers_aligned(int_nbr_edge);

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_internal_edge(half_edge, int_nbr_edge, is_switched);
      sdFLOAT diffBtwUnfolded[2];
      vsub2(diffBtwUnfolded, ghost_element.m_unfolded_centroid, stencil_surf->get_unfolded_centroid());
      sdFLOAT magDiffVec = vlength2(diffBtwUnfolded);
      stencil_assert(magDiffVec < 1e-6);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, int_nbr_edge, CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
      }

      stencil_surf->set_lsq_coeff(ghost_element.m_lsq_coeff);
    }
  }

  root_shell_data->set_stencil_info_boundary_edge_index(g_shell_conduction_edges.total_num_stencils());

  {
    BREP_FACET_DO_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is internal
      if (half_edge->is_internal_edge()) { continue; }

      HALF_EDGE_VECTOR nbr_half_edges;
      uINT32 edge_type = get_boundary_edge_info_from_half_edge(half_edge, nbr_half_edges);

      sGHOST_SURFEL_STENCIL_INFO_ELEMENT& ghost_element = ghost_info.stencil_nbrs[cur_ghost_info_ind++];
      stencil_assert(ghost_element.m_stencil_type & edge_type);

      // the vector to centroid for boundary edges is just the half_edge->m_normal * half_edge->m_normal_dist;
      dFLOAT unfolded_vec[2];
      vcopy2(unfolded_vec, ghost_element.m_unfolded_centroid);

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_boundary_edge(half_edge, nbr_half_edges, 
                                                                            edge_type, unfolded_vec);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, nbr_half_edges, (uINT32)(stencil_surf->stencil_type()));
      }

      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(ghost_element.m_lsq_coeff);
    }
  }
  
  root_shell_data->set_stencil_info_vertex_nbr_index(g_shell_conduction_edges.total_num_stencils());

  // If the stencil does not contain any internal edges, the CSP with the non-ghost surfel will print a simerr and
  // set the lsq coeffs to 0. For the CSPs with the ghosted surfels, we just set the set the end index of the stencil
  // neighbors and return.
  if (root_shell_data->num_internal_edges() == 0) {
    root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());
    return;
  }

  while (cur_ghost_info_ind < ghost_info.stencil_nbrs.size()) {
    sGHOST_SURFEL_STENCIL_INFO_ELEMENT& ghost_element = ghost_info.stencil_nbrs[cur_ghost_info_ind++];
    stencil_assert(ghost_element.m_stencil_type == CONDUCTION_STENCIL_FLAGS::VERTEX_NEIGHBOR);

    dFLOAT unfolded_vec[2];
    vcopy2(unfolded_vec, ghost_element.m_unfolded_centroid);
    cSHELL_MESH::FACET nbr_facet = m_shell_mesh->get_facet_from_surfel_id(ghost_element.m_nbr_surfel_id);
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_vertex_neighbor_stencil(nbr_facet->get_surfel(),
                                                                                    unfolded_vec);

    stencil_surf->set_lsq_coeff(ghost_element.m_lsq_coeff);
  }

  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());
}

/**
 @brief Same as build_stencil_for_ghost_facet above, but has special handling for 2D BREP structures
*/
VOID cSHELL_STENCIL_INFO::build_stencil_for_2d_ghost_facet(sGHOST_SURFEL_STENCIL_INFO& ghost_info) {
  uINT32 surfel_id = ghost_info.ghost_surfel_id;
  cSHELL_MESH::FACET root_facet = m_shell_mesh->get_facet_from_surfel_id(surfel_id);
 
  // CSP with ghosted facet has all info necessary to identify facets without edge neighbors, so there is no
  // need for ghost_info when calling build_stencil_for_isolated_facet in this case.  
  if (!root_facet->has_edge_neighbors()) {
    return build_stencil_for_isolated_facet(root_facet, FALSE);
  }

  SURFEL root_surfel = root_facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA root_shell_data = root_surfel->shell_conduction_data();

  if (this->calculateMeshStats()) {
    this->startNewFacetForStats(root_facet);
  }

  root_shell_data->set_stencil_info_internal_edge_index(g_shell_conduction_edges.total_num_stencils());

  asINT32 cur_ghost_info_ind = 0;

  // start with internal edge neighbors first. These will also be used when looping through
  // faces to calculate fluxes between shell layers. This will be followed by boundary edges and lastly
  // vertex neighbors.
  {
    BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is not internal
      if (!half_edge->is_internal_edge()) { continue; }

      cSHELL_MESH::HALF_EDGE int_nbr_edge = half_edge->get_internal_edge_nbr();
      sGHOST_SURFEL_STENCIL_INFO_ELEMENT& ghost_element = ghost_info.stencil_nbrs[cur_ghost_info_ind++];
      stencil_assert((int_nbr_edge->get_surfel()->id() == ghost_element.m_nbr_surfel_id)
                  && (ghost_element.m_stencil_type & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE));

      BOOLEAN is_switched = !half_edge->are_layers_aligned(int_nbr_edge);

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_internal_edge(half_edge, int_nbr_edge, is_switched);
      sdFLOAT unfoldedDiff = ghost_element.m_unfolded_centroid[0] - stencil_surf->get_unfolded_centroid()[0];
      stencil_assert(fabs(unfoldedDiff) < 1e-6);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, int_nbr_edge, CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
      }
 
      stencil_surf->set_lsq_coeff(ghost_element.m_lsq_coeff);
    }
  }

  root_shell_data->set_stencil_info_boundary_edge_index(g_shell_conduction_edges.total_num_stencils());

  {
    BREP_FACET_DO_2D_HALF_EDGES(half_edge, root_facet, cSHELL_MESH) {
      // some misshapen surfels have length ~= 0 edges. This will skip them because they often
      // fail to calculate normal correctly
      if (half_edge->length() < DFLOAT_EPSILON) { continue; }

      // Skip if the edge is internal
      if (half_edge->is_internal_edge()) { continue; }

      HALF_EDGE_VECTOR nbr_half_edges;
      uINT32 edge_type = get_boundary_edge_info_from_half_edge(half_edge, nbr_half_edges);

      sGHOST_SURFEL_STENCIL_INFO_ELEMENT& ghost_element = ghost_info.stencil_nbrs[cur_ghost_info_ind++];
      stencil_assert(ghost_element.m_stencil_type & edge_type);

      // the vector to centroid for boundary edges is just the half_edge->m_normal * half_edge->m_normal_dist;
      dFLOAT unfolded_vec[2];
      vcopy2(unfolded_vec, ghost_element.m_unfolded_centroid);

      SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil_surf = create_boundary_edge(half_edge, nbr_half_edges, 
                                                                            edge_type, unfolded_vec);

      if (this->storeFaceConnectivity()) {
        add_face_connectivity_info(root_facet, nbr_half_edges, (uINT32)(stencil_surf->stencil_type()));
      }

      // store b vector in stencil's lsq coefficient variable.
      stencil_surf->set_lsq_coeff(ghost_element.m_lsq_coeff);
    }
  }
  
  root_shell_data->set_stencil_info_vertex_nbr_index(g_shell_conduction_edges.total_num_stencils());

  // If the stencil does not contain any internal edges, the CSP with the non-ghost surfel will print a simerr and
  // set the lsq coeffs to 0. For the CSPs with the ghosted surfels, we just set the set the end index of the stencil
  // neighbors and return.
  if (root_shell_data->num_internal_edges() == 0) {
    root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());
    return;
  }

  root_shell_data->set_stencil_info_end_index(g_shell_conduction_edges.total_num_stencils());
}

/**
 @brief   Compute and store rotation matrix for surfel

 @details This function calculates rotation matrix to transform coordinate
          system such that the normal direction defines the z' axis and
          the tangential directions are located in the x'y' plane. This
          reduces the tangential update to a 2D problem.
*/
VOID cSHELL_STENCIL_INFO::compute_local_rotation_info(cSHELL_MESH::FACET facet) {
  SURFEL root_surfel = facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA surfel_shell_conduction_data = root_surfel->shell_conduction_data();

  if (sim.is_3d()) {
    // In 3D, we define the local coordinate system using the surfel normal
    // (which is z') and the vector from the surfel centroid to the first vertex
    // (which is x'). y' is obtained from the z' .cross. x'.

    // basis_3 is the surfel normal (to ensure the surfel rotates to 2d)
    cSHELL_MESH::sBG_VECTOR3 basis_3 = get_shell_mesh()->GetFacetUnitNormal(facet->GetIndex());
    cSHELL_MESH::sBG_POINT3 centroid = get_shell_mesh()->GetFacetCentroid(facet->GetIndex());
    // basis_1 is the vector from surfel centroid to its first vertex
    cSHELL_MESH::sBG_POINT3 first_vertex = facet->GetHalfEdge()->GetVertex()->GetPoint();
    cSHELL_MESH::sBG_VECTOR3 basis_1 = first_vertex - centroid;

    // should be perpendicular to normal, but project onto plane defined
    // by normal anyway just in case
    basis_1 -= basis_3 * (basis_1*basis_3);
    basis_1.Normalize();

    // basis_2 is the basis_3 ^ basis_1 (will give [0,1,0] if basis_1 = [1,0,0] and basis_3 = [0,0,1])
    cSHELL_MESH::sBG_VECTOR3 basis_2 = BgCross(basis_3, basis_1);
    basis_2.Normalize();
    // Store rotation matrix in shell conduction data
    ccDOTIMES(index,3) {
      surfel_shell_conduction_data->local_rotation_mat[0][index] = basis_1[index];
      surfel_shell_conduction_data->local_rotation_mat[1][index] = basis_2[index];
      surfel_shell_conduction_data->local_rotation_mat[2][index] = basis_3[index];
    }

    // Store double precision transform in facet to be used for remainder of geometric operations
    facet->getTransform() = sBG_CSYS3d(centroid, basis_1, basis_2, basis_3).WorldToCSys();
  } else {
    // in 2D, surfel normal is will be y' and problem is reduced to 1D.
    cSHELL_MESH::sBG_VECTOR3 basis_2 = get_shell_mesh()->GetFacetUnitNormal(facet->GetIndex());
    basis_2[2] = 0.;
    basis_2.Normalize();

    // Use normal .cross. (0.,0.,1.) = {normal_y, -normal_x, 0} to calculate x' instead
    // of using vector from centroid to vertex. This will ensure that all surfels have
    // same orientation (i.e. rotation angle = 0)
    cSHELL_MESH::sBG_VECTOR3 basis_1(basis_2[1], -basis_2[0],0.);

    cSHELL_MESH::sBG_VECTOR3 basis_3(0.,0.,1.);
    ccDOTIMES(index,3) {
      surfel_shell_conduction_data->local_rotation_mat[0][index] = basis_1[index];
      surfel_shell_conduction_data->local_rotation_mat[1][index] = basis_2[index];
      surfel_shell_conduction_data->local_rotation_mat[2][index] = basis_3[index];
    }
    // Store double precision transform in facet to be used for remainder of geometric operations
    cSHELL_MESH::sBG_POINT3 centroid = get_shell_mesh()->GetFacetCentroid(facet->GetIndex());
    facet->getTransform() = sBG_CSYS3d(centroid, basis_1, basis_2, basis_3).WorldToCSys();
  }
}

// Macro for simplifying calls to cCONDUCTION_EDGE_BASE constuctors all of which
// share the same first 4 arguments.
#define EXPAND_EDGE_BASE_ARGS(_he)                                            \
_he->get_surfel(), _he->length(), _he->normal_distance(), _he->local_normal()

// Macro for simplifying allocation of cCONDUCTION_EDGE_BASE objects
#define ALLOCATE_CONDUCTION_EDGE(_name, eClass, ...)                      \
  cCONDUCTION_EDGE_BASE* _name = xnew eClass(__VA_ARGS__);                \
  g_shell_conduction_edges.add_edge_to_vector(_name);

// Macro for simplifying allocation of cSHELL_CONDUCTION_STENCIL_NEIGHBOR objects
#define ALLOCATE_STENCIL_NEIGHBOR(_name, ...)                             \
  cSHELL_CONDUCTION_STENCIL_NEIGHBOR* _name =                             \
      xnew cSHELL_CONDUCTION_STENCIL_NEIGHBOR(__VA_ARGS__);               \
  g_shell_conduction_edges.add_stencil_to_vector(_name);

/**
 @brief   Returns TRUE if a cCONDUCTION_EDGE_BASE point points to an internal edge

 @details Used to verify the edge stored in a cSHELL_MESH::HALF_EDGE is the expected cCONDUCTION_EDGE_INTERNAL
          type
*/
BOOLEAN is_internal_edge(CONDUCTION_EDGE_BASE test) {
  return (dynamic_cast<cCONDUCTION_EDGE_INTERNAL*>(test) != nullptr);
}

/**
 @brief   Returns TRUE if a cCONDUCTION_EDGE_BASE point points to an intersection edge

 @details Used to verify the edge stored in a cSHELL_MESH::HALF_EDGE is the expected cCONDUCTION_EDGE_INTERSECTION
          type
*/
BOOLEAN is_intersection_edge(CONDUCTION_EDGE_BASE test) {
  return (dynamic_cast<cCONDUCTION_EDGE_INTERSECTION*>(test) != nullptr);
}

/**
 @brief Creates and stencil and internal conduction edge to represent the given half edge and returns the stencil

 @details Allocates and stores a SHELL_CONDUCTION_STENCIL_NEIGHBOR representing the surfel associated
          with other_edge for the surfel associated with half_edge's LSQ stencil. If the internal
          edge between these two surfels was created previously and stored in other_edge, its pointer
          will be stored in the newly created SHELL_CONDUCTION_STENCIL_NEIGHBOR. Otherwise, a new 
          CONDUCTION_EDGE_INTERNAL representing the edge will be allocated and stored in both
          the half_edge and other_edge objects. Both the allocated objects will be added
          to vectors contained in g_shell_conduction_edges.
 */
SHELL_CONDUCTION_STENCIL_NEIGHBOR cSHELL_STENCIL_INFO::create_internal_edge(cSHELL_MESH::HALF_EDGE half_edge,
      cSHELL_MESH::HALF_EDGE other_edge, BOOLEAN is_switched) {

  uINT8 stencil_flag = (half_edge->get_surfel()->scale() == other_edge->get_surfel()->scale())
                        ? CONDUCTION_STENCIL_FLAGS::NO_VR_INTERNAL_EDGE
                        : CONDUCTION_STENCIL_FLAGS::VR_INTERNAL_EDGE;

  BOOLEAN this_is_owner = (half_edge->get_surfel()->id() < other_edge->get_surfel()->id());

  // First check to see if the edge has already been created
  CONDUCTION_EDGE_BASE nbr_cond = other_edge->get_conduction_edge();
  CONDUCTION_EDGE_INTERNAL int_edge = NULL;

  // internal edge already exists, so no need to create it
  if (nbr_cond != NULL) {
    assert(is_internal_edge(nbr_cond));
    int_edge = dynamic_cast<CONDUCTION_EDGE_INTERNAL>(nbr_cond);
  } else {
    if (this_is_owner) {
      ALLOCATE_CONDUCTION_EDGE(internal_edge_base, cCONDUCTION_EDGE_INTERNAL, EXPAND_EDGE_BASE_ARGS(half_edge),
        other_edge->get_surfel(), is_switched);
      int_edge = dynamic_cast<CONDUCTION_EDGE_INTERNAL>(internal_edge_base);
      int_edge->set_unfolded_vec_and_rotation(half_edge->get_int_edge_unfold_vec(), half_edge->get_int_edge_rotation());
      nbr_cond = internal_edge_base;
    } else {
      ALLOCATE_CONDUCTION_EDGE(internal_edge_base, cCONDUCTION_EDGE_INTERNAL, EXPAND_EDGE_BASE_ARGS(other_edge),
        half_edge->get_surfel(), is_switched);
      int_edge = dynamic_cast<CONDUCTION_EDGE_INTERNAL>(internal_edge_base);
      int_edge->set_unfolded_vec_and_rotation(other_edge->get_int_edge_unfold_vec(),
                                              other_edge->get_int_edge_rotation());
      nbr_cond = internal_edge_base;
    }
    // store pointer to edge in half_edges
    half_edge->set_conduction_edge(nbr_cond);
    other_edge->set_conduction_edge(nbr_cond);
  }

  // Build stencil using unfolded vec between centroids stored in internal edge object
  dFLOAT unfold_centroid[2];
  if (this_is_owner) {
    vcopy2(unfold_centroid, half_edge->get_int_edge_unfold_vec());
  } else {
    dFLOAT rotation_angle = other_edge->get_int_edge_rotation();
    vrotate2(unfold_centroid, -rotation_angle, other_edge->get_int_edge_unfold_vec());
    vneg2(unfold_centroid, unfold_centroid);
    // if at different scales, need to scale vector from owner to nbr scale
    if (stencil_flag == CONDUCTION_STENCIL_FLAGS::VR_INTERNAL_EDGE) {
      const asINT32 scale_diff = other_edge->get_surfel()->scale() - half_edge->get_surfel()->scale();
      dFLOAT len_scale_factor = (scale_diff >= 0) ? (1.0 / (1 << scale_diff)) : (1 << -scale_diff);
      vmul2(unfold_centroid, len_scale_factor);
    }
  }
  
  if (this->calculateMeshStats()) {
    this->addInternalEdgeStats(half_edge, int_edge, unfold_centroid);
  }

  ALLOCATE_STENCIL_NEIGHBOR(stencil_nbr, other_edge->get_surfel(), nbr_cond, unfold_centroid, stencil_flag);
  return stencil_nbr;
}

SHELL_CONDUCTION_STENCIL_NEIGHBOR cSHELL_STENCIL_INFO::create_dyn_data_boundary_edge(cSHELL_MESH::HALF_EDGE half_edge,
    HALF_EDGE_VECTOR& nbr_half_edges, const uINT32 edge_type, const dFLOAT unfold_centroid[2]) {
  stencil_assert(nbr_half_edges.size() == 1);

  cSHELL_MESH::HALF_EDGE nbr_edge = nbr_half_edges.front();
  asINT32 dyn_type = nbr_edge->get_back_dynamics_type();
  switch (dyn_type) {
    case CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
    {
      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge),
      nbr_half_edges[0]->get_surfel());
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid,
                                CONDUCTION_STENCIL_FLAGS::DYN_TEMP_BOUNDARY_EDGE);
      half_edge->set_conduction_edge(new_edge);
      return new_stencil;
    }
    case CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
    {
      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge),
      nbr_half_edges[0]->get_surfel());
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid,
                                CONDUCTION_STENCIL_FLAGS::DYN_HTC_BOUNDARY_EDGE);
      half_edge->set_conduction_edge(new_edge);
      return new_stencil;
    }
    case CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
    {
      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge),
      nbr_half_edges[0]->get_surfel());
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid,
                                CONDUCTION_STENCIL_FLAGS::DYN_FLUX_BOUNDARY_EDGE);
      half_edge->set_conduction_edge(new_edge);
      return new_stencil;
    }
    default:
      msg_internal_error("Edge dyn data boundary can not be created from surfel with dynamics type = %d",dyn_type);
  }
  return NULL;
}

/**
 @brief Returns the surfel whose temperature defines the temp BC for fixed temperature conduction edges.

 @details For now, this will always return the surfel that nbr_edge belongs to.
 */
SURFEL get_surfel_defining_temp(cSHELL_MESH::HALF_EDGE shell_edge, cSHELL_MESH::HALF_EDGE nbr_edge) {
  return nbr_edge->get_surfel();
}


/**
 @brief Creates and stencil and a boundary conduction edge to represent the given half edge based
        on the value of edge_type and returns the stencil.

 @details Allocates and stores a SHELL_CONDUCTION_STENCIL_NEIGHBOR representing the boundary associated
          with nbr_half_edge (unlike for internal edges where the stencil object represents the neighboring
          surfel) and adds it to the stencil of the surfel associated with half_edge. One of several conduction
          edge types is allocated based on the value of edge_type: CONDUCTION_EDGE_ADIABATIC_BOUNDARY,
          CONDUCTION_EDGE_FLUX_BOUNDARY, CONDUCTION_EDGE_TEMP_BOUNDARY, CONDUCTION_EDGE_MULTI_FLUX_BOUNDARY,
          CONDUCTION_EDGE_MULTI_TEMP_BOUNDARY or CONDUCTION_EDGE_INTERSECTION. For intersection edges,
          nbr_half_edges should contain one or more HALF_EDGES of shell surfels only, while for temp/flux
          boundaries it should contain one or more HALF_EDGEs of non-shell surfels only. 
          
          For all edge types other than intersection edges, a new edge must be allocated and stored in the
          stencil object (each is unique even if other shell surfels share this edge). Intersection edges,
          like internal edges are shared between shell surfels, and therefore half_edge is checked for
          an existing intersection edge that was created for another surfel in the intersection.
          
          For each conduction edge created, the pointer is stored in half_edge. This allows for checks
          to be performed later, but is not necessary. For intersection edges, the pointer is also
          stored in each of the HALF_EDGEs in nbr_half_edges. This provides access to the shared edge so
          it can be stored in the stencils of the other surfels in the intersecton created later.
          for later  for other surfels in the This is required so that later.
 */
SHELL_CONDUCTION_STENCIL_NEIGHBOR cSHELL_STENCIL_INFO::create_boundary_edge(cSHELL_MESH::HALF_EDGE half_edge,
    HALF_EDGE_VECTOR& nbr_half_edges, const uINT32 edge_type, const dFLOAT unfold_centroid[2]) {

  assert((edge_type & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE) == 0);
  assert((edge_type & CONDUCTION_STENCIL_FLAGS::UNKNOWN_EDGE_TYPE) == 0);
  
  if (this->calculateMeshStats()) {
    this->addBoundaryEdgeStats(half_edge);
  }
  
  if (edge_type & CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE) {
    ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_ADIABATIC_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge));
    ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid, CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE);
    half_edge->set_conduction_edge(new_edge);
    return new_stencil;
  } else if (edge_type & CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE) {
    if (nbr_half_edges.size() == 1) {
      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_TEMP_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge),
          get_surfel_defining_temp(half_edge, nbr_half_edges[0]));
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid, CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE);
      half_edge->set_conduction_edge(new_edge);
      return new_stencil;
    } else {
      std::vector<SURFEL> nbr_surfels;
      ccDOTIMES(i, nbr_half_edges.size()) {
        nbr_surfels.push_back(get_surfel_defining_temp(half_edge, nbr_half_edges[i]));
      }
      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY, EXPAND_EDGE_BASE_ARGS(half_edge),
          nbr_surfels);
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid, CONDUCTION_STENCIL_FLAGS::MULTI_TEMP_EDGE);
      half_edge->set_conduction_edge(new_edge);
      return new_stencil;
    }
  } else if (edge_type & CONDUCTION_STENCIL_FLAGS::DYN_DATA_BOUNDARY_EDGE) {
    return create_dyn_data_boundary_edge(half_edge, nbr_half_edges, edge_type, unfold_centroid);
  } else if (edge_type & CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE) {
    assert(nbr_half_edges.size());
    CONDUCTION_EDGE_BASE existing_edge = (CONDUCTION_EDGE_BASE)(half_edge->get_conduction_edge());
    if (existing_edge == NULL) {
      // Need to sort half_edge bys their associated surfel's index so that the edge is consistent across SPs
      // if any of these surfels are ghosted
      nbr_half_edges.push_back(half_edge);
      std::sort(nbr_half_edges.begin(), nbr_half_edges.end(), [](cSHELL_MESH::HALF_EDGE s1, cSHELL_MESH::HALF_EDGE s2) {
        return s1->get_surfel()->id() < s2->get_surfel()->id(); });

      std::vector<SURFEL> nbr_surfels(nbr_half_edges.size());
      std::vector<sdFLOAT> nbr_dists(nbr_half_edges.size());
      std::vector<sdFLOAT> nbr_rotations(nbr_half_edges.size());
      
      // Surfel with smallest index is always the owner surfel, so set owner_edge to nbr_half_edges[0];
      cSHELL_MESH::HALF_EDGE owner_edge = nbr_half_edges[0];
      const sdFLOAT* owner_normal = owner_edge->local_normal();
      nbr_surfels[0] = owner_edge->get_surfel();
      nbr_dists[0] = owner_edge->normal_distance();
      nbr_rotations[0] = 0.;
      stencil_assert(nbr_half_edges[0]->get_conduction_edge() == NULL);
      for (asINT32 i = 1; i < nbr_half_edges.size(); i++) {
        nbr_surfels[i] = nbr_half_edges[i]->get_surfel();
        nbr_dists[i] = nbr_half_edges[i]->normal_distance();
        const sdFLOAT* nbr_normal = nbr_half_edges[i]->local_normal();
        nbr_rotations[i] = atan2(vcross2(owner_normal, nbr_normal), vdot2(owner_normal,nbr_normal));
        stencil_assert(nbr_half_edges[i]->get_conduction_edge() == NULL);
      }

      ALLOCATE_CONDUCTION_EDGE(new_edge, cCONDUCTION_EDGE_INTERSECTION, EXPAND_EDGE_BASE_ARGS(owner_edge), nbr_surfels,
        nbr_dists, nbr_rotations);
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, new_edge, unfold_centroid, CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE);
      ccDOTIMES(i, nbr_half_edges.size()) {
        nbr_half_edges[i]->set_conduction_edge(new_edge);
      }
      return new_stencil;
    } else {
      // make sure all of the neighboring half edges are all assigned to this same intersection edge
      ccDOTIMES(i, nbr_half_edges.size()) {
        CONDUCTION_EDGE_BASE nbr_existing_edge = (CONDUCTION_EDGE_BASE)(nbr_half_edges[i]->get_conduction_edge());
        stencil_assert(nbr_existing_edge != NULL && nbr_existing_edge == existing_edge);
      }
      ALLOCATE_STENCIL_NEIGHBOR(new_stencil, existing_edge, unfold_centroid, CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE);
      return new_stencil;
    } 
  }
  
  msg_internal_error("Type of half edge not known %04x", edge_type);
  return NULL;
}

/**
 @brief Creates and stencil to represent the given nbr_surfel and returns it

 @details Allocates and stores a SHELL_CONDUCTION_STENCIL_NEIGHBOR representing the vertex neighbor nbr_surfel.
          No conduction edge is created for these neighbors.
 */
SHELL_CONDUCTION_STENCIL_NEIGHBOR cSHELL_STENCIL_INFO::create_vertex_neighbor_stencil(
    sSURFEL* nbr_surfel, const dFLOAT unfold_centroid[2]) {

  ALLOCATE_STENCIL_NEIGHBOR(stencil_nbr, nbr_surfel, unfold_centroid);
  return stencil_nbr;
}

#undef EXPAND_EDGE_BASE_ARGS
#undef ALLOCATE_CONDUCTION_EDGE
#undef ALLOCATE_STENCIL_NEIGHBOR

/**
 @brief Stores edge type between face ids of HALF_EDGEs in nbr_edges and root_facet.

 @details Edge type information is stored in the cFACE_CONNECTIVITY_INFO object m_face_connectivity_map
          and writes it to a log file. This is only done if g_pfc_print_shell_mesh_connectivity = 1.
 */
VOID cSHELL_STENCIL_INFO::add_face_connectivity_info(cSHELL_MESH::FACET root_facet,
    HALF_EDGE_VECTOR& nbr_edges, uINT32 e_type) {
  // Skipping ghosted surfels to avoid them being included in connectivity info of two different SPs
  if (root_facet->get_surfel()->is_ghost()) { return; }

  asINT32 root_face_id = root_facet->get_surfel()->m_face_index; 
  if (!(m_face_connectivity_map->count(root_face_id))) {
    m_face_connectivity_map->operator[](root_face_id) = new cFACE_CONNECTIVITY_INFO(root_face_id);
  }
  std::vector<asINT32> face_ids;
  ccDOTIMES(i, nbr_edges.size()) {
    asINT32 face_id = nbr_edges[i]->get_surfel()->m_face_index;
    if (std::find(face_ids.begin(), face_ids.end(), face_id) == face_ids.end()) {
      face_ids.push_back(face_id);
    }
  }
  std::sort(face_ids.begin(), face_ids.end());
  FACE_CONNECTIVITY_INFO face_connect = m_face_connectivity_map->operator[](root_face_id);
  face_connect->add_face_connectivity(face_ids, e_type);
}

VOID cSHELL_STENCIL_INFO::add_face_connectivity_info(cSHELL_MESH::FACET root_facet,
    cSHELL_MESH::HALF_EDGE nbr_edge, uINT32 e_type) {
  // Skipping ghosted surfels to avoid them being included in connectivity info of two different SPs
  if (root_facet->get_surfel()->is_ghost()) { return; }

  asINT32 root_face_id = root_facet->get_surfel()->m_face_index; 
  if (!(m_face_connectivity_map->count(root_face_id))) {
    m_face_connectivity_map->operator[](root_face_id) = new cFACE_CONNECTIVITY_INFO(root_face_id);
  }
  std::vector<asINT32> face_ids;
  asINT32 face_id = nbr_edge->get_surfel()->m_face_index;
  if (std::find(face_ids.begin(), face_ids.end(), face_id) == face_ids.end()) {
    face_ids.push_back(face_id);
  }
  std::sort(face_ids.begin(), face_ids.end());
  FACE_CONNECTIVITY_INFO face_connect = m_face_connectivity_map->operator[](root_face_id);
  face_connect->add_face_connectivity(face_ids, e_type);
}

/**
 @brief Writes face connectivity information to the log file
 */
VOID cSHELL_STENCIL_INFO::dump_face_connectivity_info() {
  std::string fileName = fmt::format("shell_mesh_connectivity_CSP{}.txt", my_csp_proc_id);
  std::ofstream connectFile(fileName.c_str());
  BOOLEAN openSuccess = connectFile.is_open();
  if (!openSuccess) {
    msg_print("Error opening file %s for printing connection information", fileName.c_str());
  }
  FACE_CONNECTIVITY_MAP::iterator iter;
  for (iter = m_face_connectivity_map->begin(); iter != m_face_connectivity_map->end(); iter++) {
    if (openSuccess) {
      iter->second->print_connectivity(connectFile);
    }
    delete iter->second;
  }
  if (openSuccess) {
    connectFile.close();
  }
}

VOID cSHELL_STENCIL_INFO::startNewFacetForStats(cSHELL_MESH::FACET facet) {
  // Skipping ghosted surfels to avoid them being included in stats from two different SPs
  // if (facet->get_surfel()->is_ghost()) { return; }
  m_mesh_statistics->startNewFacet(facet);
}

VOID cSHELL_STENCIL_INFO::addInternalEdgeStats(cSHELL_MESH::HALF_EDGE he, cCONDUCTION_EDGE_INTERNAL* int_edge,
                                               const dFLOAT unfold_vec[2]) {
  // Skipping ghosted surfels to avoid them being included in stats from two different SPs
  // if (he->get_surfel()->is_ghost()) { return; }
  m_mesh_statistics->addInternalEdge(he, he->skewness(unfold_vec), int_edge->cellSizeRatio());
}

VOID cSHELL_STENCIL_INFO::addBoundaryEdgeStats(cSHELL_MESH::HALF_EDGE he) {
  // Skipping ghosted surfels to avoid them being included in stats from two different SPs
  // if (he->get_surfel()->is_ghost()) { return; }
  m_mesh_statistics->addBoundaryEdge(he, he->boundarySkewness());
}


VOID sGHOST_SURFEL_STENCIL_INFO_ELEMENT::update_from_stencil(cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr) {
  m_stencil_type = (uINT32)nbr->stencil_type();
  memcpy(&m_lsq_coeff, nbr->get_lsq_coeff(), sizeof(m_lsq_coeff));
  memcpy(&m_unfolded_centroid, nbr->get_unfolded_centroid(), sizeof(m_unfolded_centroid));
  if ((m_stencil_type & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE)
     || (m_stencil_type == CONDUCTION_STENCIL_FLAGS::VERTEX_NEIGHBOR)) {
    m_nbr_surfel_id = nbr->surfel()->id();
  }
}


sGHOST_SURFEL_STENCIL_INFO::sGHOST_SURFEL_STENCIL_INFO(sSURFEL* surfel) {
  ghost_surfel_id = surfel->id();
  SURFEL_SHELL_CONDUCTION_DATA surfel_shell_data = surfel->shell_conduction_data();
  asINT32 start_nbr_index = surfel_shell_data->stencil_info_internal_edge_index;
  asINT32 end_nbr_index = surfel_shell_data->stencil_info_end_index;

  num_stencil_nbrs = end_nbr_index - start_nbr_index;
  asINT32 curInd = 0;
  stencil_nbrs.resize(num_stencil_nbrs);
  for (int i = 0; i < num_stencil_nbrs; i++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(i+start_nbr_index);
    stencil_nbrs[curInd++].update_from_stencil(nbr_stencil);
  }
}



/**
 @brief Returns string containing name of boundary associated with the value of eType.
 */
std::string cFACE_CONNECTIVITY_INFO::get_edge_type_string(uINT32 eType) {
  if (eType & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE) { return std::string("Internal Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::DYN_TEMP_BOUNDARY_EDGE) { return std::string("Dyn Temp Boundary Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::DYN_HTC_BOUNDARY_EDGE) { return std::string("Dyn HTC Boundary Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::DYN_FLUX_BOUNDARY_EDGE) { return std::string("Dyn Flux Boundary Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE) { return std::string("Fixed Temp Boundary Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::MULTI_TEMP_EDGE) { return std::string("Multi-Temp Boundary Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE) { return std::string("Adiabatic Edge"); }
  else if (eType == CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE) { return std::string("Intersection Edge"); }
  msg_internal_error("Edge type = %d unknown",eType);
  return std::string("");
}

VOID cFACE_CONNECTIVITY_INFO::add_face_connectivity(std::vector<asINT32>& new_face_ids, uINT32 new_e_type) {
  // Check to see if the e type has already been added
  ccDOTIMES(i, m_connection_nbrs.size()) {
    if (new_e_type == m_connection_types[i]) {
      std::vector<std::vector<asINT32>>& connection_nbr_set = m_connection_nbrs[i];
      // check to see if set of neighbors already exists
      ccDOTIMES(j, connection_nbr_set.size()) {
        std::vector<asINT32>& connection_nbrs = connection_nbr_set[j];
        if (new_face_ids == connection_nbrs) {
          // if it exists, return without adding
          return;
        }
      }
      // if e type has been added, but this set of face_ids is new, append it to the list
      connection_nbr_set.push_back(new_face_ids);
      // return after adding
      return;
    }
  }
  // if the e type has not been added yet, add it and a vector of vectors containing new_face_ids
  std::vector<std::vector<asINT32>> new_face_id_vec;
  new_face_id_vec.push_back(new_face_ids);

  m_connection_nbrs.push_back(new_face_id_vec);
  m_connection_types.push_back(new_e_type);
}

VOID cFACE_CONNECTIVITY_INFO::nbr_list_to_str(std::vector<asINT32>& connection_nbrs, std::ostringstream& oss) {
  oss << "(";
  if (!connection_nbrs.empty()) {
    std::copy(connection_nbrs.begin(), connection_nbrs.end()-1,
      std::ostream_iterator<asINT32>(oss, ", "));
    oss << connection_nbrs.back();
  }
  oss << ")";
}


std::string cFACE_CONNECTIVITY_INFO::nbr_set_list_to_str(std::vector<std::vector<asINT32>>& connection_nbr_set) {
  if (!connection_nbr_set.empty()) {
    std::ostringstream oss;
    ccDOTIMES(i, connection_nbr_set.size()) {
      nbr_list_to_str(connection_nbr_set[i], oss);
      if (i < (connection_nbr_set.size() - 1)) {
        oss << ", ";
      }
    }
    return oss.str();
  }
  return std::string("None");
}


VOID cFACE_CONNECTIVITY_INFO::print_connectivity(std::ofstream& connectFile) {
  connectFile << fmt::format("Face {} Connection Info:\n",m_face_id);

  uINT32 conn_types[7] = {CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE, CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE,
                          CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE, CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE,
                          CONDUCTION_STENCIL_FLAGS::DYN_TEMP_BOUNDARY_EDGE, CONDUCTION_STENCIL_FLAGS::DYN_FLUX_BOUNDARY_EDGE,
                          CONDUCTION_STENCIL_FLAGS::DYN_HTC_BOUNDARY_EDGE};
  ccDOTIMES(i, 7) {
    print_edge_connections(conn_types[i], connectFile);
  }
}

VOID cFACE_CONNECTIVITY_INFO::print_edge_connections(uINT32 etype_to_print, std::ofstream& connectFile) {
  BOOLEAN header_printed = FALSE;
  ccDOTIMES(i, m_connection_nbrs.size()) {
    BOOLEAN print_this = (etype_to_print & (0x13))
                      ? (m_connection_types[i] & etype_to_print)
                      : (m_connection_types[i] == etype_to_print);
    if (print_this) {
      if (!header_printed) {
        std::string edge_name = get_edge_type_string(etype_to_print);
        connectFile << "  " << edge_name << " Connections:\n";
        header_printed = TRUE;
      }
      std::string connection_nbrs_str = nbr_set_list_to_str(m_connection_nbrs[i]);
      connectFile << "    Connected Faces: " << connection_nbrs_str << "\n";
    }
  }
}

VOID cFACET_MESH_STATS::writeFacetStats(cSHELL_MESH::FACET facet, std::ofstream& statsFile) {
  SURFEL surfel = facet->get_surfel();
  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  asINT32 curInternalEdgeInd = shell_conduction_data->stencil_info_internal_edge_index;
  asINT32 curBoundaryEdgeInd = shell_conduction_data->stencil_info_boundary_edge_index;
  asINT32 surfelId = surfel->id();
  if (surfel->is_ghost()) {
    surfelId = -1 - surfelId;
  }
  statsFile << fmt::format("Surfel {}; aspectRatio {}\n", surfelId, this->m_aspect_ratio);
  asINT32 index = 0;
  cSHELL_MESH::HALF_EDGE startHe = facet->GetHalfEdge();
  cSHELL_MESH::HALF_EDGE loopingHe = startHe;
  do {
    if (auto heStats = m_edge_stats.find(loopingHe); heStats != m_edge_stats.end()) {
      if (heStats->second->isBoundary()) {
        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(curBoundaryEdgeInd++);
        sdFLOAT unfolded_centroid_angle = nbr_stencil->get_unfolded_centroid_angle();
        statsFile << fmt::format("Edge {}; skewness {}; unfoldedAngle {}\n", index, heStats->second->faceSkewness(),
          unfolded_centroid_angle);
      } else {
        sdFLOAT skew = heStats->second->faceSkewness();
        sdFLOAT nonOrtho = heStats->second->nonOrthogonality();
        sdFLOAT cellRatio = heStats->second->cellSizeRatio();

        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(curInternalEdgeInd++);
        sdFLOAT unfolded_centroid_angle = nbr_stencil->get_unfolded_centroid_angle();

        statsFile << fmt::format("Edge {}; skewness {}; nonOrthogonality {}; cellRatio {}; unfoldedAngle {}\n", 
          index, skew, nonOrtho, cellRatio, unfolded_centroid_angle);
      }
    } else {
      statsFile << fmt::format("Edge {}; NoData\n", index);
    }
    index++;
    loopingHe = loopingHe->GetNext();
  } while (loopingHe != startHe);

  asINT32 vertexStartInd = shell_conduction_data->stencil_info_vertex_nbr_index;
  asINT32 vertexEndInd = shell_conduction_data->stencil_info_end_index;
  for (asINT32 stencilInd = vertexStartInd; stencilInd < vertexEndInd; stencilInd++) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR vert_stencil = g_shell_conduction_edges.get_nbr_stencil(stencilInd);
    sdFLOAT unfolded_centroid_angle = vert_stencil->get_unfolded_centroid_angle();
    if (stencilInd == vertexStartInd) {
      statsFile << fmt::format("VertUnfoldedAngles [{}", unfolded_centroid_angle);
    } else {
      statsFile << fmt::format(",{}", unfolded_centroid_angle);
    }
    if (stencilInd == (vertexEndInd-1)) {
      statsFile << "]\n";
    }
  }
} 

VOID cFACET_MESH_STATS::finalizeStats(dFLOAT& sumNonOrtho, dFLOAT& sumSkewness, dFLOAT& sumBndSkewness,
                                      dFLOAT& sumCellRatio, dFLOAT& sumAspectRatio, int& nCells, int& nIntEdges,
                                      int& nBndEdges, sFLOAT& maxNonOrtho, sFLOAT& maxSkewness,
                                      sFLOAT& maxBndSkewness, sFLOAT& minCellRatio, sFLOAT& maxAspectRatio,
                                      int& nSevereNonOrthog, int& nSevereSkew,int& nSevereBndSkew) {
  for (auto& heStatsPair : m_edge_stats) {
    EDGE_MESH_STATS heStats = heStatsPair.second;
    if (heStats->isBoundary()) {
      sdFLOAT skew = heStats->faceSkewness();
      if (skew > g_pfc_shell_skewness_threshold) {
        nSevereBndSkew++;
      }
      sumBndSkewness += (dFLOAT)skew;
      maxBndSkewness = MAX(maxBndSkewness, (sFLOAT)skew);
      nBndEdges++;
    } else {
      sdFLOAT skew = heStats->faceSkewness();
      sdFLOAT nonOrtho = heStats->nonOrthogonality();
      sdFLOAT cellRatio = heStats->cellSizeRatio();
      if (nonOrtho > g_pfc_shell_non_orthogonality_threshold) {
        nSevereNonOrthog++;
      }
      if (skew > g_pfc_shell_skewness_threshold) {
        nSevereSkew++;
      }
      sumSkewness += (dFLOAT)skew;
      sumNonOrtho += (dFLOAT)nonOrtho;
      sumCellRatio += (dFLOAT)cellRatio;
      maxSkewness = MAX(maxSkewness, (sFLOAT)skew);
      maxNonOrtho = MAX(maxNonOrtho, (sFLOAT)nonOrtho);
      minCellRatio = MIN(minCellRatio, (sFLOAT)cellRatio);
      nIntEdges++;
    }
  }
  nCells++;
  sumAspectRatio += m_aspect_ratio;
  maxAspectRatio = MAX(maxAspectRatio, (sFLOAT)m_aspect_ratio);
}

VOID cMESH_STATISTICS::startNewFacet(cSHELL_MESH::FACET facet) {
  m_cur_facet = facet;
  m_facet_stats[facet] = new cFACET_MESH_STATS();
  m_cur_stats = m_facet_stats[facet];
  // m_facet_stats.emplace_back(cFACET_MESH_STATS());
  dFLOAT longestEdge = -10000.;
  dFLOAT shortestEdge = 10000.;
  BREP_FACET_DO_HALF_EDGES(half_edge, facet, cSHELL_MESH) {
    cSHELL_MESH::sBG_VECTOR3 he_vec = get_shell_mesh()->HalfEdgeVec(half_edge);
    dFLOAT heLen = he_vec.Length();
    if (heLen < DFLOAT_EPSILON) { continue; }
    longestEdge = MAX(heLen, longestEdge);
    shortestEdge = MIN(heLen, shortestEdge);
  }
  m_cur_stats->addAspectRatio((longestEdge/shortestEdge));
}

VOID cMESH_STATISTICS::writeStatsFile() {
  std::string fileName = fmt::format("shell_mesh_stats_CSP{}.txt", my_csp_proc_id);
  std::ofstream statsFile(fileName.c_str());
  BOOLEAN openSuccess = statsFile.is_open();
  if (!openSuccess) {
    msg_print("Error opening file %s for printing mesh statistics", fileName.c_str());
    return;
  }

  for (auto& facetStats : m_facet_stats) {
    facetStats.second->writeFacetStats(facetStats.first, statsFile);
  }

  if (openSuccess) {
    statsFile.close();
  }
}

VOID cMESH_STATISTICS::addInternalEdgeNonOrthogonality(cSHELL_MESH::FACET facet) {
  FACET_MESH_STATS facet_stats = m_facet_stats[facet];
  BREP_FACET_DO_HALF_EDGES(half_edge, facet, cSHELL_MESH) {
    if (half_edge->is_internal_edge() && !half_edge->is_edge_ignored()) {
      CONDUCTION_EDGE_BASE cond_edge = half_edge->get_conduction_edge();
      CONDUCTION_EDGE_INTERNAL int_edge = dynamic_cast<CONDUCTION_EDGE_INTERNAL>(cond_edge);
      facet_stats->setNonOrthogonality(half_edge, int_edge->nonOrthogonality());
    }
  }
}

VOID cMESH_STATISTICS::finalizeStats() {
  dFLOAT sumNonOrtho = 0.;
  dFLOAT sumSkewness = 0.;
  dFLOAT sumBndSkewness = 0.;
  dFLOAT sumCellRatio = 0.;
  dFLOAT sumAspectRatios = 0.;
  int nIntEdges = 0;
  int nBndEdges = 0;
  int nCells = 0;
  int nSevereNonOrthog = 0;
  int nSevereSkew = 0;
  int nSevereBndSkew = 0;

  sFLOAT maxNonOrtho = -1000.;
  sFLOAT maxSkewness = -1000.;
  sFLOAT maxBndSkewness = -1000.;
  sFLOAT minCellRatio = 100000.;
  sFLOAT maxAspectRatio = -1000.;

  for (auto& facetStats : m_facet_stats) {
    if (facetStats.first->get_surfel()->is_ghost()) { continue; }
    facetStats.second->finalizeStats(sumNonOrtho, sumSkewness, sumBndSkewness, sumCellRatio, sumAspectRatios, nCells,
                                     nIntEdges, nBndEdges, maxNonOrtho, maxSkewness, maxBndSkewness, minCellRatio,
                                     maxAspectRatio, nSevereNonOrthog, nSevereSkew, nSevereBndSkew);
  }
  dFLOAT doublesToSum[5] = {sumNonOrtho, sumSkewness, sumBndSkewness, sumCellRatio, sumAspectRatios};
  int intsToSum[6] = {nIntEdges, nBndEdges, nCells, nSevereNonOrthog, nSevereSkew, nSevereBndSkew};
  sFLOAT floatsToMax[4] = {maxNonOrtho, maxSkewness, maxBndSkewness, maxAspectRatio};

  MPI_Allreduce(MPI_IN_PLACE, doublesToSum, 5, MPI_DOUBLE, MPI_SUM, eMPI_csp_comm);
  MPI_Allreduce(MPI_IN_PLACE, intsToSum, 6, MPI_INT, MPI_SUM, eMPI_csp_comm);
  MPI_Allreduce(MPI_IN_PLACE, floatsToMax, 4, MPI_FLOAT, MPI_MAX, eMPI_csp_comm);
  MPI_Allreduce(MPI_IN_PLACE, &minCellRatio, 1, MPI_FLOAT, MPI_MIN, eMPI_csp_comm);
  
  if (my_csp_proc_id == 0) {
    dFLOAT nIntEdgesDbl = ((dFLOAT)intsToSum[0]);
    dFLOAT nBndEdgesDbl = ((dFLOAT)intsToSum[1]);

    dFLOAT avgNonOrtho = doublesToSum[0] / nIntEdgesDbl;
    dFLOAT avgSkew = doublesToSum[1] / nIntEdgesDbl;
    dFLOAT avgBndSkew = doublesToSum[2] / nBndEdgesDbl;
    dFLOAT avgCellRatio = doublesToSum[3] / nIntEdgesDbl;
    dFLOAT avgAspectRatio = doublesToSum[4] / ((dFLOAT)intsToSum[2]);

    maxNonOrtho = floatsToMax[0];
    maxSkewness = floatsToMax[1];
    maxBndSkewness = floatsToMax[2];
    maxAspectRatio = floatsToMax[3];

    nSevereNonOrthog = intsToSum[3];
    nSevereSkew = intsToSum[4];
    nSevereBndSkew = intsToSum[5];

    dFLOAT pctSevereNonOrthog = (dFLOAT)nSevereNonOrthog / nIntEdgesDbl * 100.;
    dFLOAT pctSevereSkew = (dFLOAT)nSevereSkew / nIntEdgesDbl * 100.;
    dFLOAT pctSevereBndSkew = (dFLOAT)nSevereBndSkew / nBndEdgesDbl * 100.;

    auto StatsString = fmt::format("Shell Mesh Statistics:\n  Number of:\n    Shell Surfels: {}\n",intsToSum[2]);
    StatsString += fmt::format("    Internal Edges: {}\n    Bnd Edges: {}\n", intsToSum[0],intsToSum[1]);
    StatsString += fmt::format("  NonOrthogonality:\n    Average: {:.1f}\n    Max: {:.1f}\n", avgNonOrtho, maxNonOrtho);
    StatsString += fmt::format("    Edges with nonOrthogonality > {}: {} ({:3.1f}%)\n",
      g_pfc_shell_non_orthogonality_threshold, nSevereNonOrthog, pctSevereNonOrthog); 
    StatsString += fmt::format("  Internal Edge Skew:\n    Average: {}\n    Max: {}\n", avgSkew, maxSkewness);
    StatsString += fmt::format("    Edges with skew > {}: {} ({:3.1f}%)\n", g_pfc_shell_skewness_threshold,
      nSevereSkew, pctSevereSkew);
    StatsString += fmt::format("  Boundary Edge Skew:\n    Average: {}\n    Max: {}\n", avgBndSkew, maxBndSkewness);
    StatsString += fmt::format("    Edges with skew > {}: {} ({:3.1f}%)\n", g_pfc_shell_skewness_threshold,
      nSevereBndSkew, pctSevereBndSkew);
    StatsString += fmt::format("  Cell Size Ratio:\n    Average: {}\n    Min: {}\n",
      avgCellRatio, minCellRatio);
  
    StatsString += fmt::format("  Aspect Ratio:\n    Average: {}\n    Max: {}\n",
      avgAspectRatio, maxAspectRatio);

    msg_print("%s",StatsString.c_str());
  }
}


cMESH_TO_VTK_INFO::cMESH_TO_VTK_INFO() {
  std::string fileName = fmt::format("shell_mesh_vtk_info_CSP{}.txt", my_csp_proc_id);
  m_facet_file.open(fileName.c_str());
}

cMESH_TO_VTK_INFO::~cMESH_TO_VTK_INFO() {
  m_facet_file.close();
}

VOID cMESH_TO_VTK_INFO::addBoundaryEdgeInfo(cSHELL_MESH::HALF_EDGE he) {
  if (he->is_adiabatic_boundary()) {
    m_facet_file << "; Type Adiabatic\n";
  } else if (he->is_dyn_data_boundary()) {
    m_facet_file << fmt::format("; Type DynDataBC; FaceId {}\n",
      he->get_boundary_or_intersection_edge_nbr()->surfel_face_id());
  } else if (he->is_fixed_temp_boundary()) {
    cSHELL_MESH::HALF_EDGE nbr1 = he->get_boundary_or_intersection_edge_nbr();
    if (he->nbr_he_is_pair()) {
      cSHELL_MESH::HALF_EDGE nbr2 = he->get_second_edge_nbr_in_pair();
      m_facet_file << fmt::format("; Type FixedTemp; Nbrs [{}, {}]\n", nbr1->get_surfel()->id(),
                                                                       nbr2->get_surfel()->id());
    } else {
      m_facet_file << fmt::format("; Type FixedTemp; Nbr {}\n", nbr1->get_surfel()->id());
    }
  } else {
    msg_internal_error("Edge type has not been identified yet");
  }
}

VOID cMESH_TO_VTK_INFO::addEdgeInfo(cSHELL_MESH::HALF_EDGE he) {
  DGF_VERTEX_INDEX vInd1, vInd2;
  he->global_vertex_indices(vInd1, vInd2);
  m_facet_file << fmt::format("  Edge [{}, {}]", vInd1, vInd2);

  if (he->is_internal_edge()) {
    sSURFEL* s1 = he->get_surfel();
    sSURFEL* s2 = he->get_internal_edge_nbr()->get_surfel();
    m_facet_file << fmt::format("; Type Internal; Owner {}; Nbr {}\n",
                      MIN(s1->id(), s2->id()), MAX(s1->id(), s2->id()));
  } else if (he->is_intersection_edge()) {
    m_facet_file << fmt::format("; Type Intersection; Surfels [{}", he->get_surfel()->id());
    cSHELL_MESH::HALF_EDGE loopingHe = he->get_boundary_or_intersection_edge_nbr();
    do {
      m_facet_file << fmt::format(", {}", loopingHe->get_surfel()->id());
      loopingHe = loopingHe->get_boundary_or_intersection_edge_nbr();
    } while (loopingHe != he);
    m_facet_file << "]\n";
  } else {
    return addBoundaryEdgeInfo(he);
  }
}

VOID cMESH_TO_VTK_INFO::addVertexIndices(asINT32 startInd, asINT32 nVerts) {
  ccDOTIMES(i,nVerts) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR stencil = g_shell_conduction_edges.get_nbr_stencil(startInd+i);
    if (i == 0) {
      m_facet_file << fmt::format("  VertexNbrs [{}", stencil->surfel()->id());
    } else {
      m_facet_file << fmt::format(", {}", stencil->surfel()->id());
    }
  }
  if (nVerts)
    m_facet_file << "]\n";  
}

VOID cMESH_TO_VTK_INFO::addFacet(cSHELL_MESH::FACET facet) {
  // Skipping ghosted surfels to avoid them being included in two different vtk info files
  // if (facet->get_surfel()->is_ghost()) { return; }

  if (!facet->has_edge_neighbors()) {
    return addIsolatedFacet(facet);
  }

  SURFEL surfel = facet->get_surfel();
  asINT32 start_int_edge_index = surfel->shell_conduction_data()->stencil_info_internal_edge_index;
  asINT32 start_boundary_edge_index = surfel->shell_conduction_data()->stencil_info_boundary_edge_index;
  asINT32 start_vert_nbr_index = surfel->shell_conduction_data()->stencil_info_vertex_nbr_index;
  asINT32 end_nbr_index = surfel->shell_conduction_data()->stencil_info_end_index;

  asINT32 n_int_edge_nbrs = start_boundary_edge_index - start_int_edge_index;
  asINT32 n_bnd_edge_nbrs = start_vert_nbr_index - start_boundary_edge_index;
  asINT32 n_vert_nbrs = end_nbr_index - start_vert_nbr_index;
  asINT32 n_edge_nbrs = n_bnd_edge_nbrs + n_int_edge_nbrs;

  asINT32 surfelId = surfel->id();
  if (surfel->is_ghost()) {
    surfelId = -1 - surfelId;
  }
  m_facet_file << fmt::format("Surfel {}: FaceId {}; Area {:1.6e}; EdgeNbrs {}; VertNbrs {}\n", surfelId,
    surfel->m_face_index, surfel->area(), facet->NumVertices(), n_vert_nbrs);
  
  cSHELL_MESH::HALF_EDGE startHe = facet->GetHalfEdge();
  cSHELL_MESH::HALF_EDGE loopingHe = startHe;
  do {
    addEdgeInfo(loopingHe);
    loopingHe = loopingHe->GetNext();
  } while (loopingHe != startHe);

  addVertexIndices(start_vert_nbr_index, n_vert_nbrs);
}

VOID cMESH_TO_VTK_INFO::addIsolatedFacet(cSHELL_MESH::FACET facet) {
  // Skipping ghosted surfels to avoid them being included in two different vtk info files
  // if (facet->get_surfel()->is_ghost()) { return; }

  SURFEL surfel = facet->get_surfel();
  cSHELL_MESH::HALF_EDGE startHe = facet->GetHalfEdge();
  cSHELL_MESH::HALF_EDGE loopingHe = startHe;

  asINT32 surfelId = surfel->id();
  if (surfel->is_ghost()) {
    surfelId = -1 - surfelId;
  }

  m_facet_file << fmt::format("IsolatedSurfel {}: FaceId {}; Area {:1.6e}; EdgeNbrs {}; VertNbrs 0\n", surfelId,
    surfel->m_face_index, surfel->area(), facet->NumVertices());

  do {
    DGF_VERTEX_INDEX vInd1, vInd2;
    loopingHe->global_vertex_indices(vInd1, vInd2);
    m_facet_file << fmt::format("  Edge [{}, {}]; Type Adiabatic\n", vInd1, vInd2);
    loopingHe = loopingHe->GetNext();
  } while (loopingHe != startHe);
}



cSTENCIL_DETAILED_INFO::cSTENCIL_DETAILED_INFO() {
  std::string fileName = fmt::format("shell_stencil_details_CSP{}.txt", my_csp_proc_id);
  m_facet_file.open(fileName.c_str());
  readSurfelList();
}

cSTENCIL_DETAILED_INFO::~cSTENCIL_DETAILED_INFO() {
  m_facet_file.close();
}

VOID cSTENCIL_DETAILED_INFO::readSurfelList() {
  std::ifstream infile("shell_stencils_to_output.txt");
    
  if (!infile.is_open()) {
    if (my_csp_proc_id == 0) {
      msg_print("Writing detailed stencil info for all surfels");
    }
    return;
  }

  std::string line;
  while (std::getline(infile, line)) {
    std::stringstream ss(line);
    std::string cell;
    while (std::getline(ss, cell, ',')) {
      m_surfels_to_process.push_back(std::stoi(cell));
    }
  }

  if (my_csp_proc_id == 0) {
    int ind = 0;
    std::string writeStr = "Writing detailed stencil info for surfels:";
    while (ind < m_surfels_to_process.size()) {
      writeStr += fmt::format("\n  {}", m_surfels_to_process[ind++]);

      int nToPrint = MIN(m_surfels_to_process.size()-ind, 5);
      ccDOTIMES(i,nToPrint) {
        writeStr += fmt::format(", {}", m_surfels_to_process[ind++]);
      }
    }
    msg_print("%s", writeStr.c_str());
  }

  infile.close();
}


BOOLEAN cSTENCIL_DETAILED_INFO::isSurfelInList(sSURFEL* surfel) {
  if (m_surfels_to_process.size() == 0) {
    return TRUE;
  }

  return (std::find(m_surfels_to_process.begin(), m_surfels_to_process.end(),surfel->id()) 
       != m_surfels_to_process.end());
}

VOID cSTENCIL_DETAILED_INFO::checkAndStoreNbrFacet(cSHELL_MESH::FACET facet) {
  // dont need to store csys info for surfels that are included in list to write detailed info for
  if (isSurfelInList(facet->get_surfel())) { return; }

  if (std::find(m_nbr_facets.begin(), m_nbr_facets.end(), facet) == m_nbr_facets.end()) {
    m_nbr_facets.push_back(facet);
  }
}

std::string normalCentroidAndFirstVertexStr(cSHELL_MESH::FACET facet) {

  cSHELL_MESH::sBG_VECTOR3 normal = get_shell_mesh()->GetFacetUnitNormal(facet->GetIndex());
  cSHELL_MESH::sBG_POINT3 centroid = get_shell_mesh()->GetFacetCentroid(facet->GetIndex());
  const cSHELL_MESH::sBG_POINT3& first_vertex = facet->GetHalfEdge()->GetVertex()->GetPoint();

  cSHELL_MESH::sBG_VECTOR3 basis_1 = first_vertex - centroid;

  basis_1 -= normal * (basis_1*normal);
  basis_1.Normalize();

  std::string normalStr = fmt::format("Normal [{:1.6e},{:1.6e},{:1.6e}]", EXPAND_FLOAT_VEC3(normal));
  std::string centroidStr = fmt::format("; Centroid [{:1.6e},{:1.6e},{:1.6e}]", EXPAND_FLOAT_VEC3(centroid));
  std::string xBasisStr = fmt::format("; xBasis [{:1.6e},{:1.6e},{:1.6e}]", EXPAND_FLOAT_VEC3(basis_1));
  return normalStr + centroidStr + xBasisStr;
}

cSHELL_MESH::HALF_EDGE getHalfEdgeFromCondEdge(cSHELL_MESH::FACET facet, CONDUCTION_EDGE_BASE condEdge) {
  BREP_FACET_DO_HALF_EDGES(half_edge, facet, cSHELL_MESH) {
    if (half_edge->get_conduction_edge() == condEdge) {
      return half_edge;
    }
  }
  msg_internal_error("Unable to find half edge associated with conduction edge");
  return NULL;
}

cSHELL_MESH::FACET getVertexNbrFacet(cSHELL_MESH::FACET facet, SURFEL surfel) {
  BREP_FACET_DO_VERTICES(vertex, facet, cSHELL_MESH) {
    BREP_VERTEX_DO_FACETS(nbrFacet, vertex, cSHELL_MESH) {
      if (nbrFacet->get_surfel() == surfel) {
        return nbrFacet;
      }
    }
  }
  msg_internal_error("Unable to find surfel associated with the vertex nbr");
  return NULL;
}


std::string unfoldedVecAndLSQInfoStr(SHELL_CONDUCTION_STENCIL_NEIGHBOR nbrStencil) {
  const sdFLOAT* lsqCoeff = nbrStencil->get_lsq_coeff();
  const sdFLOAT* unfolded = nbrStencil->get_unfolded_centroid();
  std::string unfoldStr = fmt::format("UnfoldVec [{:1.6e},{:1.6e}]", EXPAND_FLOAT_VEC2(unfolded));
  std::string lsqStr = fmt::format("; LSQCoeff [{:1.6e},{:1.6e}]", EXPAND_FLOAT_VEC2(lsqCoeff));
  return unfoldStr+lsqStr;
}

std::string edgeInfoStr(cSHELL_MESH::HALF_EDGE halfEdge, SHELL_CONDUCTION_STENCIL_NEIGHBOR nbrStencil) {
  std::string unfoldAndLsq = unfoldedVecAndLSQInfoStr(nbrStencil);
  DGF_VERTEX_INDEX vInd1, vInd2;
  halfEdge->global_vertex_indices(vInd1, vInd2);
  const sdFLOAT* eNormal = halfEdge->local_normal();
  std::string edgeInfo = fmt::format("vInds [{},{}]; edgeNorm [{:1.6e},{:1.6e}]; ", vInd1, vInd2, eNormal[0], eNormal[1]);
  return edgeInfo + unfoldAndLsq;
}

std::string internalEdgeInfoStr(cSHELL_MESH::HALF_EDGE halfEdge, SHELL_CONDUCTION_STENCIL_NEIGHBOR nbrStencil) {
  CONDUCTION_EDGE_INTERNAL intEdge = nbrStencil->get_internal_edge();
  sdFLOAT rotation = intEdge->get_unfolding_rotation();
  const sdFLOAT* unfolded_centroid = intEdge->get_unfolded_centroid();
  // if (intEdge->get_nbr_surfel() == halfEdge->get_surfel()) {
  //   rotation *= -1.;
  // }
  asINT32 nbrId = halfEdge->get_internal_edge_nbr()->get_surfel()->id();
  std::string idStr = "  Type 100; ";
  std::string intInfoStr = fmt::format("; nbr {}; rot {:1.6e}; intUnfoldedVec [{:1.6e},{:1.6e}]", nbrId, rotation,
                           unfolded_centroid[0],unfolded_centroid[1]);
  return idStr + edgeInfoStr(halfEdge, nbrStencil) + intInfoStr;
}

std::string bndEdgeInfoStr(cSHELL_MESH::HALF_EDGE halfEdge, SHELL_CONDUCTION_STENCIL_NEIGHBOR nbrStencil) {
  asINT32 typeId;
  if (halfEdge->is_adiabatic_boundary()) {
    typeId = 500;
  } else if (halfEdge->is_dyn_data_boundary()) {
    asINT32 nbrFaceId = halfEdge->get_boundary_or_intersection_edge_nbr()->surfel_face_id();
    typeId = 300+nbrFaceId;
  } else if (halfEdge->is_fixed_temp_boundary()) {
    typeId = 400;
  } else if (halfEdge->is_intersection_edge()) {
    typeId = 200;
  } else {
    msg_internal_error("Edge type has not been identified yet");
  }
  std::string idStr = fmt::format("  Type {}; ", typeId);
  return idStr += edgeInfoStr(halfEdge, nbrStencil);
}

std::string vertNbrInfoStr(SHELL_CONDUCTION_STENCIL_NEIGHBOR nbrStencil) {
  asINT32 nbrId = nbrStencil->surfel()->id();
  std::string idStr = "  Type 600; ";
  std::string nbrStr = fmt::format("; nbr {}", nbrId);
  return idStr + unfoldedVecAndLSQInfoStr(nbrStencil) + nbrStr;
}

std::string surfelVertexStr(cSHELL_MESH::FACET facet) {
  std::vector<DGF_VERTEX_INDEX> vertexInds;
  facet->globalVertexIndices(vertexInds);
  return fmt::format("  SurfelVertices [{}]", fmt::join(vertexInds,","));
}

VOID cSTENCIL_DETAILED_INFO::addFacet(cSHELL_MESH::FACET facet) {
  SURFEL surfel = facet->get_surfel();

  // Skipping ghosted surfels to avoid them being included in two different vtk info files
  // if (surfel->is_ghost()) { return; }

  if (!isSurfelInList(surfel)) { return; }

  asINT32 start_int_edge_index = surfel->shell_conduction_data()->stencil_info_internal_edge_index;
  asINT32 start_boundary_edge_index = surfel->shell_conduction_data()->stencil_info_boundary_edge_index;
  asINT32 start_vert_nbr_index = surfel->shell_conduction_data()->stencil_info_vertex_nbr_index;
  asINT32 end_nbr_index = surfel->shell_conduction_data()->stencil_info_end_index;

  asINT32 n_int_edge_nbrs = start_boundary_edge_index - start_int_edge_index;
  asINT32 n_bnd_edge_nbrs = start_vert_nbr_index - start_boundary_edge_index;
  asINT32 n_vert_nbrs = end_nbr_index - start_vert_nbr_index;
  asINT32 n_edge_nbrs = n_bnd_edge_nbrs + n_int_edge_nbrs;

  asINT32 surfelId = surfel->id();
  if (surfel->is_ghost()) {
    surfelId = -1-surfelId;
  }

  m_facet_file << fmt::format("Surfel {}: FaceId {}; Area {:1.6e}; IntEdgeNbrs {}; BndEdgeNbrs {}; VertNbrs {}", surfelId,
    surfel->m_face_index, surfel->area(), n_int_edge_nbrs, n_bnd_edge_nbrs, n_vert_nbrs) << std::endl;
  m_facet_file << "  CsysInfo: " << normalCentroidAndFirstVertexStr(facet) << std::endl;
  m_facet_file << surfelVertexStr(facet) << std::endl;
  
  ccDOTIMES(i, n_int_edge_nbrs) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR intNbr = g_shell_conduction_edges.get_nbr_stencil(start_int_edge_index+i);
    cSHELL_MESH::HALF_EDGE he = getHalfEdgeFromCondEdge(facet, intNbr->get_edge());
    m_facet_file << internalEdgeInfoStr(he, intNbr) << std::endl;
    checkAndStoreNbrFacet(he->get_internal_edge_nbr()->GetFacet());
  }

  ccDOTIMES(i, n_bnd_edge_nbrs) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bndNbr = g_shell_conduction_edges.get_nbr_stencil(start_boundary_edge_index+i);
    cSHELL_MESH::HALF_EDGE he = getHalfEdgeFromCondEdge(facet, bndNbr->get_edge());
    m_facet_file << bndEdgeInfoStr(he, bndNbr) << std::endl;
  }

  ccDOTIMES(i, n_vert_nbrs) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR vertNbr = g_shell_conduction_edges.get_nbr_stencil(start_vert_nbr_index+i);
    m_facet_file << vertNbrInfoStr(vertNbr) << std::endl;
    checkAndStoreNbrFacet(getVertexNbrFacet(facet, vertNbr->surfel()));
  }
}


VOID cSTENCIL_DETAILED_INFO::addNbrFacets() {
  m_facet_file << "Neighbor Csys Info:\n";
  for (cSHELL_MESH::FACET facet : m_nbr_facets) {
    m_facet_file << fmt::format("Surfel {}: ",facet->get_surfel()->id()) << normalCentroidAndFirstVertexStr(facet)
                 << "\n" << surfelVertexStr(facet) << std::endl;
  }
}
