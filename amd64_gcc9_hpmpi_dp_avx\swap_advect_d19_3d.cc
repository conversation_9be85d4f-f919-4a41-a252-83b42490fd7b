#include "../common_sp.h"
#include "../box_advect_gen_headers.h"
#include "../swap_advect.h"

template <SWAP_ADVECT_TEMPLATE_PARAMETERS>
VOID d19_swap_advect_states_3D(SWAP_ADVECT_STATES_PARAMS) { 

  VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
  VOXEL_STATE (*curr_src_states)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*curr_src_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*curr_src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*curr_src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    { //SELF
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,2,3};
          constexpr asINT32 dest_voxels[] = {4,5,6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,4,5};
          constexpr asINT32 dest_voxels[] = {2,3,6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 4;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,2,4,6};
          constexpr asINT32 dest_voxels[] = {1,3,5,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[0] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 1;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,2,3};
          constexpr asINT32 dest_voxels[] = {4,5,6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[1] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {4,5,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,2,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[2] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 3;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,4,5};
          constexpr asINT32 dest_voxels[] = {2,3,6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[3] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {2,3,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,4,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[4] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 5;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,2,4,6};
          constexpr asINT32 dest_voxels[] = {1,3,5,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[5] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 4;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {1,3,5,7};
          constexpr asINT32 dest_voxels[] = {0,2,4,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[6] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[7] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[8] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[9] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[10] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[11] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[12] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[13] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[14] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[15] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[16] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  curr_src_states = curr_src_states_t = curr_src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = curr_src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[17] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
      BOOLEAN src_tagged_ublk_has_two_copies = src_tagged_ublk.ublk()->has_two_copies();
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          PUSH_STATES_IF_NEIGHBOR_UBLK_HAS_TWO_COPIES 
          SWAP_VOXEL_STATES_WITH_NEIGHBOR 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,-1}; 

swap_along_latvec_pairs<ADVECT_TEMP, ADVECT_UDS>(states,states_t,states_mc, states_uds);
}

