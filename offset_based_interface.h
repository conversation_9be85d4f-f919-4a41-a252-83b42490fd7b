#ifndef OFFSET_BASED_INTERFACE_H
#define OFFSET_BASED_INTERFACE_H
#include "common_sp.h"

/*===================================================================================
 * @struct tOFFSET_BASED_INTERFACE
 * This class attempts to generalize the concept of assigning a chunk of memory based
 * on a table of components, number of elements per component and the corresponding 
 * element sizes indirectly specified with the component type. 
 *
 * a) The components are described by a scoped enum "SCOPED_ENUM_COMPONENTS".
 *    The scoped enum MUST have an entry N_COMPONENTS that describes how long the enum list is.
 * 
 * b) For each scoped enum entry, the interface expects a corresponding type that describes 
 *    how much storage each element of that component requires. By default it assumed that
 *    the expected storage is sizeof(DEFAULT_COMPONENT_ELEMENT_TYPE). To specify a different type
 *    clients can specialize the COMPONENT_TRAITS struct for a component. For examples, see
 *    <simeng>/gpu_surfel_interactions.h
 *
 * c) The interface keeps track of how many elements there are for each 
 *    component/enum entry with member m_n_component_elements. With the size 
 *    and number of elements available for each enum entry, this interface can 
 *    compute an offset into a sequence of bytes and return the memory location 
 *    corresponding to a table entry/component.
 * 
 * d) The private static method offset() requires knowledge of the derived class
 *    size so the variable chunk of memory is assigned at the end of the derived
 *    class members, and doesn't end up trampling that piece of memory. Hence we
 *    adopt a CRTP approach where the DERIVED class passes its type to tOFFSET_BASED_INTERFACE
 *==================================================================================*/
template<typename SCOPED_ENUM_COMPONENTS,
         typename DEFAULT_COMPONENT_ELEMENT_TYPE,
         typename DERIVED>
struct tOFFSET_BASED_INTERFACE {


  using OFFSET_INTERFACE_TYPE = tOFFSET_BASED_INTERFACE<SCOPED_ENUM_COMPONENTS, DEFAULT_COMPONENT_ELEMENT_TYPE, DERIVED>;

  /* @struct COMPONENT_TRAITS
   * Describes the element type for each scoped_enum entry/component
   */
  template<SCOPED_ENUM_COMPONENTS component>
  struct COMPONENT_TRAITS { using type = DEFAULT_COMPONENT_ELEMENT_TYPE; };
  
  static constexpr int N_COMPONENTS = static_cast<int>(SCOPED_ENUM_COMPONENTS::N_COMPONENTS);

protected:  
  unsigned m_n_component_elements[N_COMPONENTS];
  
private:
  template<SCOPED_ENUM_COMPONENTS component>
  __HOST__DEVICE__ static __inline__ 
  size_t offset(const unsigned (&n_component_elements) [N_COMPONENTS]) {

    constexpr auto PREDECESSOR_COMPONENT = static_cast<SCOPED_ENUM_COMPONENTS>(static_cast<int>(component) - 1);
    constexpr size_t sizeof_comp_elem = sizeof_component_element<component>();
    constexpr size_t alignment = alignof_component_element<component>();
    
    if constexpr((int) component != 0) {
      constexpr size_t sizeof_predecessor_comp_elem = sizeof_component_element<PREDECESSOR_COMPONENT>();
    
      size_t last_byte_after_predecessor =
        offset<PREDECESSOR_COMPONENT>(n_component_elements) +
        n_component_elements[(int) PREDECESSOR_COMPONENT] * sizeof_predecessor_comp_elem;
    
      return get_byte_aligned(last_byte_after_predecessor, alignment);
    } else {
      return get_byte_aligned(sizeof(DERIVED), alignment);
    }
  }

  template<SCOPED_ENUM_COMPONENTS component>
  constexpr static __inline__  __HOST__DEVICE__ size_t sizeof_component_element() {
    return sizeof(typename COMPONENT_TRAITS<component>::type);
  }

  template<SCOPED_ENUM_COMPONENTS component>
  constexpr static __inline__  __HOST__DEVICE__ size_t alignof_component_element() {
    return get_alignment_of<typename COMPONENT_TRAITS<component>::type>();
  }  
public:

  __HOST__ void set_n_component_elements(const unsigned (&n_component_elements) [N_COMPONENTS]) {
    for (int c = 0; c < N_COMPONENTS; c++) {      
      m_n_component_elements[c] = n_component_elements[c];
    }
  }    

  template<SCOPED_ENUM_COMPONENTS component>
  __HOST__DEVICE__ __inline__ typename COMPONENT_TRAITS<component>::type* get() {
    return get_n_elements<component>()?
      reinterpret_cast<typename COMPONENT_TRAITS<component>::type*>(reinterpret_cast<char*>(this) +
                                                                    offset<component>(m_n_component_elements)): nullptr;
  }

  template<SCOPED_ENUM_COMPONENTS component>
  __HOST__DEVICE__ __inline__ const typename COMPONENT_TRAITS<component>::type* get() const {
    return (const_cast<tOFFSET_BASED_INTERFACE*>(this))->get<component>();
  }

  template<SCOPED_ENUM_COMPONENTS component>
  __HOST__DEVICE__ __inline__ unsigned get_n_elements() const {
    return m_n_component_elements[(int) component];
  }

  __HOST__DEVICE__ __inline__ unsigned size() const {
    return offset<SCOPED_ENUM_COMPONENTS::N_COMPONENTS>(m_n_component_elements);
  }  

  __HOST__DEVICE__ __inline__ static unsigned size(const unsigned (&n_component_elements) [N_COMPONENTS]) {
    return offset<SCOPED_ENUM_COMPONENTS::N_COMPONENTS>(n_component_elements);
  }

  template<SCOPED_ENUM_COMPONENTS component>
  __HOST__DEVICE__ __inline__ 
  size_t offset() const  {
    return tOFFSET_BASED_INTERFACE::offset<component>(this->m_n_component_elements);
  }  
};

#endif
