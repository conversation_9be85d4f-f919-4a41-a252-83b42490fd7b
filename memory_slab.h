/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Slab memory allocator
 *
 * Dalon Work
 * Created Apr 09, 2019 
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MEMORY_SLAB_H
#define _SIMENG_MEMORY_SLAB_H

#include "common_sp.h"

/** A simple slab allocator which allocates large blocks of memory, and then
 * hands out smaller allocations from those blocks upon request. It is not
 * possible to free this memory directly. The only way to free it is to destroy
 * the cMEMORY_SLAB object itself.
 */
class cMEMORY_SLAB
{
  private:
  std::vector<void*> m_blocks;
  uint8_t * m_cur_ptr;
  uint8_t * m_end;
  const size_t m_block_size;

  public:
  cMEMORY_SLAB(const cMEMORY_SLAB&) = delete;
  cMEMORY_SLAB(cMEMORY_SLAB&&) = delete;

  /** Create a slab allocator.
   *
   * @param block_size The size in bytes to allocate for each block. An optimal
   * size depends on how much memory you are willing to waste.  In the worst
   * case, only a small part of the block will be used, wasting the rest.  The
   * class will allocate more blocks as necessary.
   */
  cMEMORY_SLAB(size_t block_size) : m_blocks(4),
                                    m_cur_ptr(nullptr),
                                    m_end(nullptr),
                                    m_block_size(block_size)
  {
    // we always start out with one block to avoid a nullptr check in allocate()
    allocate_new_block();
  };

  ~cMEMORY_SLAB()
  {
    for (typename std::vector<void*>::iterator it = m_blocks.begin(); it != m_blocks.end(); it++ ) {
      std::free(*it);
    }
  }

  /** Request a chunk of memory from the slab allocator. 
   *
   * @param size_in_bytes must be < block_size. We could just allocate a new block and
   * return it, but this hides the fact that this class is not being used in
   * its intended manner
   * 
   * @param alignment Alignment of the requested allocation
   */
  void * malloc(size_t size_in_bytes, size_t alignment=16)
  { 
#if ENABLE_CONSISTENCY_CHECKS
    bool power_of_two = (alignment != 0) && ((alignment & (alignment-1) ) == 0);
    cassert(power_of_two);
#endif
    cassert(size_in_bytes < m_block_size);

    size_t aligned_size;
    uint8_t * aligned_ptr = compute_aligned_ptr_and_size(alignment, size_in_bytes, aligned_size);

    if (aligned_size >= (m_end - m_cur_ptr)) {
      // allocate new block moves m_cur_ptr and m_end to a new empty block,
      // so we have to recompute the new alignment and total allocation size
      allocate_new_block(); 
      aligned_ptr = compute_aligned_ptr_and_size(alignment, size_in_bytes, aligned_size);
    }

    void * ptr = aligned_ptr;
    m_cur_ptr += aligned_size;
    return ptr;
  }

  private:
  void allocate_new_block()
  {
    m_cur_ptr = (uint8_t*) std::malloc(m_block_size);
    m_end = m_cur_ptr + m_block_size;
    m_blocks.push_back(m_cur_ptr);
  }

  uint8_t * compute_aligned_ptr_and_size(size_t alignment, size_t size_in_bytes, size_t& aligned_size)
  {
    uintptr_t offset = alignment-1;
    uint8_t* aligned_ptr    = (uint8_t*) (((uintptr_t) m_cur_ptr + offset) & ~offset);
    uint8_t* next_ptr = aligned_ptr + size_in_bytes;
    aligned_size = next_ptr - m_cur_ptr;
    return aligned_ptr;
  }


};

#endif 
