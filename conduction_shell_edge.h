/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 *
 * Zachary Mills, Dassault Systemes
 * Created September 1, 2023
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_CONDUCTION_SHELL_EDGE_H
#define _SIMENG_CONDUCTION_SHELL_EDGE_H

#define DEBUG_BREP_STENCIL


// This header file is included in the files for the physics components. Therefore,
// in an attempt to avoid the need to include BREP related components in when building
// the physics without figuring out how to forward declare the cSHELL_MESH types using
// the BREP macros, no functions in this file rely on cSHELL_MESH types. As a result,
// all of the allocation of cCONDUCTION_EDGE_BASE objects which rely on information
// calculated using or stored in these types is handled by member functions of the
// SHELL_STENCIL_INFO class defined in conduction_solver_stencils. The pointers to these
// objects are then passed to g_shell_conduction_edges to be stored in the appropriate
// vectors.

typedef std::vector<sSURFEL*> sEDGE_SURFEL_VECTOR, *EDGE_SURFEL_VECTOR;

// Forward declaration of surfel physics descriptors
struct sCONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR;
struct sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR;
struct sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR;

inline namespace SIMULATOR_NAMESPACE {
// forward declaration of types in conduction_data.h
struct sSHELL_LAYER_DATA;

// forward declaration of stencil class define later in file
class cSHELL_CONDUCTION_STENCIL_NEIGHBOR;

/**
 @brief   Class which provides access to physics descriptor for dyn data boundary conduction edges

 @details Holds pointer to either a sCONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR,
          sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR, or 
          sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR physics descriptors for the dyn data boundary
          edges. Constructor calculates the edge location in the global csys for updating variables in the
          physics descriptor. 

          update_varying_physics_params will update any variable params and return TRUE if they are to
          indicate that the conduction edge class should update their stored value of that variable.

          Note: It is likely that none of the parameters in these physics descriptors will need the edge normal,
          so its possible that this variable can be removed and a dummy variable passed in its place when
          calling the update function.
 */
template <typename sPHYSICS_DESCRIPTOR_TYPE>
class cEDGE_BOUNDARY_DYNAMICS_DATA {
public:
  
  cEDGE_BOUNDARY_DYNAMICS_DATA(sSURFEL* owner_surfel, sSURFEL* nbr_surfel,
                               const sdFLOAT scaled_norm_dist, const sdFLOAT* norm_vec_local);


  sPHYSICS_DESCRIPTOR_TYPE* physics_descriptor() {
    return m_edge_physics_desc;
  }

  BOOLEAN update_varying_physics_params();

  VOID init_physics_params();

private:
  sPHYSICS_DESCRIPTOR_TYPE* m_edge_physics_desc;

  // this is edge info in global csys
  sdFLOAT m_edge_center[3];
  // not sure if any dyn data parameters actually depend on this, so could probably remove this variable and pass
  // m_edge_center to update_time_and_space_varying_surfel_physics_parms twice
  sdFLOAT m_edge_normal[3];
};

using cEDGE_TEMP_BOUNDARY_DYN_DATA = cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_TEMP_SURFEL_PHYSICS_DESCRIPTOR>;
using cEDGE_HTC_BOUNDARY_DYN_DATA = cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR>;
using cEDGE_FLUX_BOUNDARY_DYN_DATA = cEDGE_BOUNDARY_DYNAMICS_DATA<sCONDUCTION_FIXED_HEAT_FLUX_SURFEL_PHYSICS_DESCRIPTOR>;

/**
 @brief   Conduction Edge Base Class 

 @details Base class to represent edges (i.e. faces) between shell surfels and at boundaries.
          Classes derived from this will implement the necessary methods for calculating the flux
          passing through the edge.

          This base class stores a pointer to the surfel owning the edge, geometric data for the edge
          in the owner surfel's scale and local 2d csys as well as information used to track when the edge
          was updated. The tracking of its update allows for any shell surfel sharing the edge to update
          information and calculate fluxes. This avoids the need to ensure that the owner surfel is always
          the first surfel sharing the edge to be updated. As a result, the first surfel sharing it to perform
          its dynamics step will update the edge information and calculate the flux that is accumulated in
          all shell layers sharing the edge. Because of this, when an edge is shared by surfels at different
          VR levels, the edge is updated every timestep of the finer surfel. This requires the flux to
          be scaled before it is accumulated in the coarse surfel layers to account for the half timestep
          it represents at the coarser VR level.

          In the event that an edge is shared with a ghost surfel, the flux is only accumulated in layers
          of non-ghost surfel(s) sharing the edge. The ghosted surfels will have their own copy of the edge
          on their owner SP which will calculate and accumulate the flux for those surfel(s). This avoids the
          need to communicate flux information between SPs. One situation that can arise because of this is
          when the non-ghost surfel(s) in a shared edge are all at a coarser VR than the finest VR level of
          surfels sharing an edge. This can lead to the edge shared by the non-ghosted surfels only updating
          every other timestep that the ghosted surfels update their duplicate edge. Special functions are
          included in the derived classes representing internal and edge intersections to handle this situation.
          In these functions, the data at timestep n-1 is used to recreate that missed update and calculation step
          each time the update and flux calculations are performed. This is done instead of performing
          a single timestep at the coarser resolution to maintain conservation.

          When allocated, the edges are stored in a vector in g_shell_conduction_edges. Access to these
          edges by the shell surfels is provided through a pointer stored in the SHELL_CONDUCTION_STENCIL_NEIGHBOR
          object representing the neighbor at the corresponding edge (either another shell surfel or
          a boundary).

          The function `boundary_temperature` provides the temperature at boundarys for the passthrough's
          min/max temperatures and the LSQ calculations. The function must be provided the layer number
          to allow for calculation of the temperature in the case of flux boundaries. Otherwise it returns
          the fixed temp applied to all layers. This should not be used for internal edges (stencils with
          boundary edges are always located after those containing internal edge neighbors so they can
          be processed in a separate loop).

          No virtual functions are used in this implementation. Instead, the SHELL_CONDUCTION_STENCIL_NEIGHBOR
          containing the pointer to this object also stores indicating what type to cast to when
          calling any member functions.
 */
typedef class cCONDUCTION_EDGE_BASE {
public:
  // for writing edge info to stdout when debugging
  friend std::ostream & operator << (std::ostream &out, cCONDUCTION_EDGE_BASE* c);

  cCONDUCTION_EDGE_BASE(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, const sdFLOAT* norm_vec);

  virtual ~cCONDUCTION_EDGE_BASE() {}

/**
  @brief   Computes face fluxes and adds dE to owner (and neighbor) layers

  @details Classes derived from this will implement the necessary methods for calculating the flux
            Instances should alway be cast to their correct derived type before this function is called.
*/
  VOID compute_face_fluxes(const asINT32 time_index) {
    msg_internal_error("compute_face_fluxes should not be called for cCONDUCTION_EDGE_BASE object");
  };

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    msg_internal_error("compute_implicit_solver_face_fluxes should not be called for cCONDUCTION_EDGE_BASE object");
  };

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {
    msg_internal_error("assemble_implicit_solver_grad_coeffs should not be called for cCONDUCTION_EDGE_BASE object");
  };

  VOID assemble_rhs(sSURFEL* root_surfel) {
    msg_internal_error("assemble_rhs should not be called for cCONDUCTION_EDGE_BASE object");
  }

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    msg_internal_error("implicit_solver_boundary_contribution should not be called for cCONDUCTION_EDGE_BASE object");
  }

  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {
    msg_internal_error("implicit_solver_determine_if_cell_is_bad should not be called for cCONDUCTION_EDGE_BASE object");
  };

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    msg_internal_error("implicit_solver_get_edge_intersection_nbr_index should not be called for cCONDUCTION_EDGE_BASE object");
  }

/**
  @brief   Returns temperature of boundary in layer layer_ind at time index time_index

  @details Classes derived from this will implement the necessary methods to calculate and return the temperature
*/
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    msg_internal_error("boundary_temperature should not be called for cCONDUCTION_EDGE_BASE object");
  }

/**
   @brief   Returns length scaled to the owner surfel's VR level
*/
  dFLOAT length() { return m_length; }

  sdFLOAT get_edge_length_finest_scale(sSURFEL* surfel, asINT32 root_surfel_scale, asINT32 nbr_surfel_scale) {

    asINT32 scale_diff;
    if (surfel == m_owner_surfel) {
      scale_diff = -scale_to_log2_voxel_size(root_surfel_scale);
    } else {
      scale_diff = -scale_to_log2_voxel_size(nbr_surfel_scale);
    }

    sdFLOAT scale_factor = vr_length_scale_factor(scale_diff);
    sdFLOAT scaled_edge_length = scale_factor * m_length;

    return scaled_edge_length;
  }

/**
  @brief   Returns normal distance from surfel centroid to edge scaled to the owner surfel's VR level
*/
  sdFLOAT normal_distance() { return m_normal_dist; }

/**
  @brief   Returns normal vector in owner surfels local 2D csys
*/
  const sdFLOAT* local_normal() { return m_normal; }

  const sdFLOAT* local_normal(sSURFEL* surfel) { 
    return (surfel == m_owner_surfel ? m_normal : m_nbr_normal);
  }

/**
  @brief   Returns the actual edge length
*/
  dFLOAT unscaled_length();

/**
  @brief   Returns the actual normal distance
*/
  sdFLOAT unscaled_normal_distance();

/**
  @brief   Returns pointer to the surfel owning the edge
*/
  sSURFEL* get_owner_surfel() { return m_owner_surfel; }

protected:
/**
  @brief   Returns TRUE if the edge fluxes have not been calculated an accumulated on this timestep

  @details Update frequency is based on finest surfel scale in all cases except when the fine surfel(s) are
           ghosts (as discussed in this class's description). Those cases are handled directly in the
           derived classes where they may occur.
*/
  BOOLEAN needs_update() const {
    // update frequency based on finest surfel scale in all cases except when the fine surfel(s) are ghosts
    // Those cases are handled directly in the derived classes
    return (g_timescale.m_conduction_pde_tm.prior_timestep_index(m_update_scale) != m_last_update);
  }

/**
  @brief   Returns FALSE if the edge fluxes have not been calculated an accumulated on this timestep
*/
  BOOLEAN is_updated() const {
    return !(this->needs_update());
  }

/**
  @brief   Stores the current time index of the finest scale shell surfel sharing the edge.
*/
  VOID store_update_time() {
    m_last_update = g_timescale.m_conduction_pde_tm.prior_timestep_index(m_update_scale);
    // m_update_time = g_timescale.m_conduction_pde_tm.m_time;
  }

/**
  @brief   Sets the value of the m_update_scale, which is used to determine update frequency and scaling
*/
  VOID set_update_scale(const asINT32 scale) { m_update_scale = scale; }

/**
  @brief   Print edge info to stdout for debugging.

  @details May want to change this to write to a log to improve debugging in multi-SP
           simulations.
*/
  virtual std::ostream& print_edge_info(std::ostream &out) {
    out << "EdgeLength " << m_length << ", NormalDistance " << m_normal_dist
        << ", EdgeNormal (" << m_normal[0] << " " << m_normal[1] << ")";
    return out;
  }

  sSURFEL* m_owner_surfel;

  dFLOAT m_length;
  sdFLOAT m_normal_dist;
  // this is the normal direction in the owning surfels local coordinate system
  sdFLOAT m_normal[2];
  sdFLOAT m_nbr_normal[2];

  // This stores the temp for intersection, and temp boundary edges and the flux for flux boundary edges.
  // No direct access is provided for to this variable in the base class but derived classes may
  // provide access if the variable is know to store temp or flux in that class.
  sdFLOAT m_temp_or_flux;
  sdFLOAT m_total_edge_resistance; // Only used for the implicit shell solver. Computes the resistance in the resistance network for edge intersections. This resistance enters as an element in the flux matrix.

  // used for tracking if edge has already been updated this timestep
  asINT32 m_update_scale;
  asINT32 m_last_update;
} *CONDUCTION_EDGE_BASE;

/**
  @brief   Overload of << operator for cCONDUCTION_EDGE_BASE objects.

  @details If switching to writing to log files, may want to remove this. Could get confusing if
           cout << edge_object << "Some Text" << endl; writes the edge data to a log file and
           the rest to stdout.
*/
INLINE std::ostream & operator << (std::ostream &out, cCONDUCTION_EDGE_BASE* c)
{
  return c->print_edge_info(out);
}


/**
 @brief   Internal Conduction Edge

 @details Class representing edge between two shell surfels containing the same number of layers.
          The shell surfels sharing this edge do not need to be from the same face, at the same VR level
          or have layers aligned as long as the number of layers is equal. In the case of a ghost surfel
          at the edge, an identical edge is created on that ghost surfel's SP and updated separately.

          This class contains the additional member variables m_nbr_surfel, m_owner_stencil, m_rotation_angle,
          and m_is_switched. m_nbr_surfel is a pointer to the other surfel not owning the edge.
          m_owner_stencil is a pointer to the owner surfel's stencil representing this edge neighbor
          which provides access to the unfolded vector between centroids. m_rotation_angle is a float
          defining the relative rotation between the two surfel's csys used to transform the nbr surfel's
          gradient and conductivity tensor. m_is_switched is a BOOLEAN indicating
          that the layer ordering is switched between surfels, so if m_is_switched is TRUE, layer 0
          will be neighbors with layer n-1, etc.

          The surfel with the lowest id should always be assigned as the owner surfel. This needs to be
          handled in the `cSHELL_STENCIL_INFO::create_internal_edge` with the constructor asserting that
          this is always true. This will ensure consistency across SPs, avoiding errors associated with
          numerical precision.

          In the event that a coarser surfel has a finer ghost neighbor, the function compute_face_flux_from_fine_ghost
          is used to calculate the flux through the edge on the missed half step. This only occurs
          when the ghosted surfel is at a finer VR level.

          Along with the edge flux calculation, the passthrough flux is also accumulated in the surfels
          as long as they are at the same VR level. This can be extended to be performed for all edges
          regardless of VR level in the future. 
*/
typedef class cCONDUCTION_EDGE_INTERNAL : public cCONDUCTION_EDGE_BASE {
public:

  cCONDUCTION_EDGE_INTERNAL(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
      const sdFLOAT* norm_vec, sSURFEL* nbr_surfel, BOOLEAN is_switched = FALSE);

  VOID set_unfolded_vec_and_rotation(const dFLOAT* owner_unfold_centroid, const dFLOAT rotation_angle);

  VOID compute_face_fluxes(const asINT32 time_index);
  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel);
  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel);

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    msg_internal_error("This should not be called for internal edges");
  }

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {
    msg_internal_error("assemble_implicit_solver_grad_coeffs should not be called for internal edges");
  };

/**
  @brief   Internal edge does not have a known temperature.
*/
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    msg_internal_error("This should not be called for internal edges");
  }

  sdFLOAT get_unfolding_rotation() { return m_rotation_angle; }

  VOID get_edge_normal_for_passthrough(sdFLOAT norm[2], sSURFEL* nbr_surfel);

  asINT32 neighbor_layer_index(const asINT32 nth_layer);

/**
  @brief   Returns pointer to nbr surfel. Used to verify edge contains correct surfels.
*/
  sSURFEL* get_nbr_surfel() { return m_nbr_surfel; }

  sdFLOAT cellSizeRatio();

  sdFLOAT nonOrthogonality();

/**
  @brief   Returns unfolded centroid stored in class.
*/
  const sdFLOAT* get_unfolded_centroid() {
    return m_unfolded_centroid;
  }

protected:
  std::ostream& print_edge_info(std::ostream &out);

private:
  template <asINT32 SCALE_DIFF>
  VOID compute_face_fluxes_func(const asINT32 time_index);

  VOID compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel);

  template <asINT32 SCALE_DIFF>
  VOID compute_face_flux_from_fine_ghost(const asINT32 time_index, const asINT32 nbr_time_index);

  sdFLOAT calculate_sn_grad(const sdFLOAT grad_t_edge[2], const sdFLOAT T_owner, const sdFLOAT T_nbr);

  template <asINT32 SCALE_DIFF>
  sdFLOAT compute_layer_edge_flux(sSHELL_LAYER_DATA* owner_layer, sSHELL_LAYER_DATA* nbr_layer,
    const sdFLOAT *kappa_edge, const sdFLOAT nbr_area, const asINT32 time_index, const asINT32 nbr_time_index);

  sdFLOAT compute_implicit_solver_layer_edge_flux(sSHELL_LAYER_DATA* owner_layer, sSHELL_LAYER_DATA* nbr_layer, 
      const sdFLOAT *kappa_edge, const sdFLOAT owner_area, const sdFLOAT nbr_area, const asINT32 time_index, 
      const asINT32 nbr_time_index, STATE_VECTOR_INDEX column_index, asINT32 owner_surfel_scale, BOOLEAN do_nbr_layer, 
      sdFLOAT flux_correction_coeff, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT rotation_angle, 
      sdFLOAT root_scale_factor, sdFLOAT nbr_scale_factor, sdFLOAT edge_length_finest_scale);

  sdFLOAT implicit_solver_flux_correction_coeff(sSURFEL* root_surfel, sSURFEL* nbr_surfel, sSHELL_LAYER_DATA* root_layer, sSHELL_LAYER_DATA* nbr_layer, 
    const sdFLOAT *kappa_edge, asINT32 root_surfel_scale, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT root_scale_factor, 
    sdFLOAT nbr_scale_factor, sdFLOAT edge_length_finest_scale, bool cell_is_bad, bool cell_has_anti_diffusion, bool cell_is_tiny);

  sdFLOAT implicit_solver_assemble_rhs_from_grad_stencil(sSHELL_LAYER_DATA* owner_layer, sSHELL_LAYER_DATA* nbr_layer, 
      cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr_stencil, const sdFLOAT *kappa_edge, const sdFLOAT owner_area, const sdFLOAT nbr_area, 
      const asINT32 time_index, const asINT32 nbr_time_index, asINT32 owner_surfel_scale, BOOLEAN do_nbr_layer, 
      sdFLOAT flux_correction_coeff, const sdFLOAT* edge_normal, sdFLOAT* unfolded_centroid_finest_scale, sdFLOAT rotation_angle, 
      sdFLOAT root_scale_factor, sdFLOAT nbr_scale_factor, sdFLOAT edge_length_finest_scale);


  const sdFLOAT* get_unfolded_centroid(sSURFEL* surfel) {
    return (surfel == m_owner_surfel ? m_unfolded_centroid : m_nbr_unfolded_centroid);
  }

  VOID get_unfolded_centroid_finest_scale(sSURFEL* surfel, asINT32 root_surfel_scale, asINT32 nbr_surfel_scale, sdFLOAT unfolded_centroid_finest_scale[2]) {

    if (surfel == m_owner_surfel) {
      asINT32 scale_diff = -scale_to_log2_voxel_size(root_surfel_scale);
      sdFLOAT scale_factor = vr_length_scale_factor(scale_diff);
      vscale2(unfolded_centroid_finest_scale, scale_factor, m_unfolded_centroid);
    } else {
      asINT32 scale_diff = -scale_to_log2_voxel_size(nbr_surfel_scale);
      sdFLOAT scale_factor = vr_length_scale_factor(scale_diff);
      vscale2(unfolded_centroid_finest_scale, scale_factor, m_nbr_unfolded_centroid);
    }

  }

  sSURFEL* nbr_surfel(sSURFEL* root_surfel) {
    assert(root_surfel == m_owner_surfel || root_surfel == m_nbr_surfel);
    return (root_surfel == m_owner_surfel ? m_nbr_surfel : m_owner_surfel);
  }

  VOID compute_edge_conductivity(sdFLOAT kappa_edge[3], const sdFLOAT *kappa_at, const sdFLOAT* kappa_nbr,
      const sdFLOAT nbr_area);

  VOID compute_implicit_solver_edge_conductivity(sdFLOAT kappa_edge[3], const sdFLOAT *kappa_at, const sdFLOAT* kappa_nbr,
      const sdFLOAT owner_area, const sdFLOAT nbr_area);

/**
  @brief   Returns scale factor based on scale difference between surfels
*/
  template <asINT32 SCALE_DIFF>
  static constexpr sdFLOAT twoLambdaRatio() {
    if constexpr(SCALE_DIFF == 0) {
      return 1.0;
    } else if constexpr(SCALE_DIFF == -1) {
      return 2.0;
    } else {
      return 0.5;
    }
  }

  template <asINT32 SCALE_DIFF>
  sdFLOAT scaled_nbr_area();

  sSHELL_LAYER_DATA* get_nbr_layer(const asINT32 nth_layer);

  VOID apply_passthrough(sSHELL_LAYER_DATA* owner_layer, sSHELL_LAYER_DATA* nbr_layer,
    const sdFLOAT *conductivity_at, const sdFLOAT *conductivity_nbr, const asINT32 time_index);

  sSURFEL* m_nbr_surfel;
  sdFLOAT m_rotation_angle;

  sdFLOAT get_rotation_angle(sSURFEL* root_surfel);

  sdFLOAT m_unfolded_centroid[2];
  sdFLOAT m_nbr_unfolded_centroid[2];

  const BOOLEAN m_is_switched;
} *CONDUCTION_EDGE_INTERNAL;

/**
 @brief   Intersection Conduction Edge

 @details Class representing edge shared by 2 shell surfels with differing number of layers, or 3+
          shell surfels regardless of the number of layers. This edge type is treated as a boundary
          with an edge temperature solved for each timestep and used to calculate the flux
          applied to each layer sharing the edge.

          The vector m_nbr_surfels contains pointers to the surfels sharing the edge (including m_owner_surfel).
          The vector m_nbr_dists, and m_rot_angles contains sdFLOAT with the normal distance between the
          corresponding surfel centroid and the edge, and the relative rotation between the surfels csys
          and the owner's csys. This rotation is used to rotate the normal vector which is in m_owner_surfels
          csys to that of the corresponding surfel.

          Comparing the finest scale of all surfels sharing the edge (m_finest_scale) and the finest scale of
          all surfels sharing the edge on a given SP (m_update_scale) is used to identify when the edge 
          must account for missing steps resulting from all finer surfels being ghosted. When it 
          m_update_scale < m_finest_scale, a call to `compute_prev_step_face_fluxes` is used to reconstruct
          the missed half step.

          In the current implementation, it can only handle a difference 2 VR levels between surfels
          sharing the edge. Not sure if the situation can arise where the difference exceeds 2 but this
          will require special handling since the previous temperatures of finer surfels will be overwritten
          before the missed steps can be reconstructed.
*/
typedef class cCONDUCTION_EDGE_INTERSECTION : public cCONDUCTION_EDGE_BASE {
public:
  cCONDUCTION_EDGE_INTERSECTION(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
      const sdFLOAT* norm_vec, std::vector<sSURFEL*>& nbr_surfels, std::vector<sdFLOAT>& nbr_dists,
      std::vector<sdFLOAT>& nbr_rotations);

  ~cCONDUCTION_EDGE_INTERSECTION() {
    delete m_nbr_surfels;
    delete[] m_nbr_dists;
    delete[] m_rot_angles;
  }

/**
  @brief   Checks to see if fluxes at face have been calculated, calling calculation function if they havent.
  
  @details When m_update_scale < m_finest_scale, `compute_prev_step_face_fluxes` will be called to
           recreate missed half step.
*/

  VOID compute_face_fluxes(const asINT32 time_index) {
    if (needs_update()) {
      if (m_update_scale < m_finest_scale) {
        this->compute_prev_step_face_fluxes();
      }
      this->compute_edge_temperature();
      ccDOTIMES(n, m_nbr_surfels->size()) {
        this->update_shell_surfel(n);
      }
      this->store_update_time();
    }
  }

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index();

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    // This doesn't get hit since these edges are included in the boundary edges. Keeping around for the time-being for
    // posterity. There will potentially be a code reorg and things will get streamlined more at that time.
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    this->implicit_solver_compute_edge_resistance();
    this->assemble_rhs_func(root_surfel);
  }

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor);

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    // Apply the updated edge temperature as a fixed temperature BC at the edge intersection in the least squares
    // gradient calculation 
    this->implicit_solver_compute_edge_temperature();
    return this->implicit_solver_boundary_contribution_func(layer_data, time_index, edge_normal, scale_factor);
  }

  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

/**
  @brief   Returns the boundary temperature stored in m_temp_or_flux
*/
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    return this->m_temp_or_flux;
  }

  const EDGE_SURFEL_VECTOR& get_nbr_surfels() {
    return m_nbr_surfels;
  }

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

private:
/**
  @brief   Recreates missed half step for coarse surfels
*/
  VOID compute_prev_step_face_fluxes() {
    this->compute_prev_edge_temperature();
    ccDOTIMES(n, m_nbr_surfels->size()) {
      this->update_shell_surfel_missed_step(n);
    }
  }

  VOID compute_edge_temperature();

  VOID compute_prev_edge_temperature();

  VOID update_shell_surfel(const asINT32 surfel_ind);

  VOID update_shell_surfel_missed_step(const asINT32 surfel_ind);

  sdFLOAT local_timestep(const asINT32 surfel_scale);

  VOID nbr_normal_vec(sdFLOAT nbr_normal[2], const asINT32 nbr_index) {
    vrotate2(nbr_normal, m_rot_angles[nbr_index], this->local_normal());
  }

  // Methods for implicit shell solver
  VOID compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel) {}; // Contributions only felt on RHS
  VOID implicit_solver_compute_edge_temperature();
  VOID implicit_solver_compute_edge_resistance();
  VOID assemble_rhs_func(sSURFEL* root_surfel);
  sdFLOAT implicit_solver_boundary_contribution_func(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    return this->m_temp_or_flux; // This is the edge temperature computed in implicit_solver_compute_edge_temperature()
  }

  VOID root_local_normal(sdFLOAT root_normal[2], sSURFEL* surfel) { 
    if (surfel == m_owner_surfel) {
      vcopy2(root_normal, this->local_normal());
    } else {
      // Find the index of the surfel
      auto surfel_iterator = std::find(m_nbr_surfels->begin(), m_nbr_surfels->end(), surfel);
      auto surfel_index = std::distance(m_nbr_surfels->begin(), surfel_iterator);
      // Get the rotation angle
      sdFLOAT angle = -m_rot_angles[surfel_index]; // Negate the rotation angle
      // Rotate this->local_normal() by the negative of that rotation angle
      vrotate2(root_normal, angle, this->local_normal()); 
      // Negate the resulting vector
      vneg2(root_normal, root_normal);
    }
  }

  EDGE_SURFEL_VECTOR m_nbr_surfels;
  sdFLOAT* m_nbr_dists;
  sdFLOAT* m_rot_angles;
  asINT32 m_finest_scale;
} *CONDUCTION_EDGE_INTERSECTION;


/**
  @brief   Adiabatic Boundary Conduction Edge
 
  @details Class representing adiabatic boundary edge. This class currently does not perform
           any calculations. Its implementation of `boundary_temperature` will return
           the current shell centroid temperature.
*/
typedef class cCONDUCTION_EDGE_ADIABATIC_BOUNDARY : public cCONDUCTION_EDGE_BASE {
public:

  cCONDUCTION_EDGE_ADIABATIC_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
    const sdFLOAT* norm_vec) : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec) {}

/**
  @brief   Does nothing as no flux will pass through edge.
*/
  VOID compute_face_fluxes(const asINT32 time_index) {}
  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {}

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    return std::vector<int>();
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {}; // Nothing goes on the RHS for these

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {}

  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index);
  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {return 0.0;} // Nothing to do here
  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

private:

} *CONDUCTION_EDGE_ADIABATIC_BOUNDARY;

/**
 @brief   Base class for boundary edges that will apply flux/htc BCs using dynamics data from nbr surfel

 @details Access to data in physics descriptor provided by m_dyn_data object

          Derived classes will use m_dyn_data to update boundary data if necessary. This data will be used
          to calculate edge flux/temperature used by the solver and LSQ grad implementations.

          This class should not be used directly.

          The cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY does not derive from this class. It derives from the
          cCONDUCTION_EDGE_TEMP_BOUNDARY class instead.
*/
template <class EDGE_DYN_DATA_TYPE>
class cCONDUCTION_EDGE_DYN_DATA_BOUNDARY : public cCONDUCTION_EDGE_BASE {
public:
  
  cCONDUCTION_EDGE_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
                                     const sdFLOAT* norm_vec, sSURFEL* bc_surfel);
  
  /**
   @brief   Updates m_temp_or_flux and applies flux to shell layers.
  */
  VOID compute_face_fluxes(const asINT32 time_index) {
    msg_internal_error("compute_face_fluxes should not be called for cCONDUCTION_EDGE_DYN_DATA_BOUNDARY object");
  }
  
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    msg_internal_error("compute_face_fluxes should not be called for cCONDUCTION_EDGE_DYN_DATA_BOUNDARY object");
  }

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    return std::vector<int>();
  }
  
protected:

  EDGE_DYN_DATA_TYPE m_dyn_data;
};

/**
 @brief   Flux Boundary Conduction Edge Defined By Data in Physics Descriptor

 @details Derives from cCONDUCTION_EDGE_DYN_DATA_BOUNDARY with boundary flux information found in cEDGE_FLUX_DYN_DATA
          object.

          Because this edge is not shared between shell surfels it does not need to check if another surfel
          has already caused it to update and calculate fluxes.
*/
typedef class cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY : public cCONDUCTION_EDGE_DYN_DATA_BOUNDARY<cEDGE_FLUX_BOUNDARY_DYN_DATA> {
  public:
  cCONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist,
    const sdFLOAT* norm_vec, sSURFEL* bc_surfel);

/**
 @brief   Updates m_temp_or_flux and applies flux to shell layers.
*/
  VOID compute_face_fluxes(const asINT32 time_index) {
    this->update_edge_flux();
    this->compute_face_fluxes_func(time_index);
  }

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    this->update_edge_flux();
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    this->update_edge_flux();
    this->assemble_rhs_func(root_surfel);
  }

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA owner_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {}

  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index);

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    this->update_edge_flux();
    sdFLOAT flux_boundary_contribution = this->implicit_solver_boundary_contribution_func(layer_data, time_index, edge_normal, scale_factor);
    return flux_boundary_contribution;
  }

  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

  VOID compute_face_fluxes_func(const asINT32 time_index);
  VOID compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel) {}; // Contributions only felt on RHS
  VOID assemble_rhs_func(sSURFEL* root_surfel);
  sdFLOAT implicit_solver_boundary_contribution_func(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor);

private:
  VOID update_edge_flux();
  BOOLEAN m_uses_pd_flux1;

} *CONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY;


/**
 @brief   HTC Boundary Conduction Edge Defined By Data in Physics Descriptor

 @details Derives from cCONDUCTION_EDGE_DYN_DATA_BOUNDARY with boundary flux information found in
          cEDGE_HTC_BOUNDARY_DYN_DATA object.

          See comments for sCONDUCTION_SURFEL_BC_THERMAL_RESISTANCE::compute_heat_flux function for description
          of flux/temp calculation.

          Currently does not work with implicit solver
*/
typedef class cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY : public cCONDUCTION_EDGE_DYN_DATA_BOUNDARY<cEDGE_HTC_BOUNDARY_DYN_DATA> {
public:
  cCONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist,
                                          const sdFLOAT* norm_vec, sSURFEL* bc_surfel);
    
  /**
   @brief   Updates m_temp_or_flux and applies flux to shell layers.
  */  
  VOID compute_face_fluxes(const asINT32 time_index) {
    this->update_htc_params();
    this->compute_face_fluxes_func(time_index);
  }
  
  /**
   @brief   Returns boundary temperature stored in m_temp_or_flux
  */
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index);

  // The implicit solver loops over boundary edges separately from internal edges and calls assemble_rhs so this function is never hit. 
  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    this->update_htc_params();
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    if (g_shell_implicit_solver_make_edge_htc_bc_adiabatic) {
      return;
    }
    this->update_htc_params();
    this->assemble_rhs_func(root_surfel);
  }

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA owner_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {}

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    // Treating this part at adiabitic. This is inconsistent with the full RHS treatment, but it simplifies things
    // substantially and the contribution is expected to be negligible, especially in the secondary gradients where this
    // term appears.
    return 0.0;
  }

  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

  protected:
  virtual std::ostream& print_edge_info(std::ostream &out);
  VOID compute_face_fluxes_func(const asINT32 time_index);
  VOID compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel) {}; // The implicit solver loops over boundary edges separately from internal edges and calls assemble_rhs so this function is never hit. 
  VOID assemble_rhs_func(sSURFEL* root_surfel);

  private:
  VOID update_htc_params();
  sdFLOAT m_heat_xfer_coeff, m_ambient_temp;

} *CONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY;

/**
 @brief   Temperature Boundary Conduction Edge Defined By A Single Neighbor Surfel

 @details Class representing a temperature boundary defined by a single non-shell conduction surfel.

          An additional pointer to the neighbor surfel defining the temperature is stored in m_boundary_surfel.

          This serves as the base class for cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY and
          cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY differing only by the method in which the edge temperature
          is obtained: From a single surfel, from the average of multiple surfels and from the physics
          descriptor of the neighboring surfel for this, the MULTI_TEMP and the TEMP_DYN_DATA classes,
          respectively.

          Because this edge is not shared between shell surfels it does not need to check if another surfel
          has already caused it to update and calculate fluxes.
*/
typedef class cCONDUCTION_EDGE_TEMP_BOUNDARY : public cCONDUCTION_EDGE_BASE {
public:

  cCONDUCTION_EDGE_TEMP_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
    const sdFLOAT* norm_vec) : cCONDUCTION_EDGE_BASE(owner_surfel, e_length, norm_dist, norm_vec) {}

  cCONDUCTION_EDGE_TEMP_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
    const sdFLOAT* norm_vec, sSURFEL* bc_surfel);

  ~cCONDUCTION_EDGE_TEMP_BOUNDARY() {}

/**
 @brief   Updates m_temp_or_flux and applies flux to shell layers.
*/  
  VOID compute_face_fluxes(const asINT32 time_index) {
    this->update_edge_temp();
    this->compute_face_fluxes_func(time_index);
  }

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    return std::vector<int>();
  }

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    this->update_edge_temp();
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    this->update_edge_temp();
    this->assemble_rhs_func(root_surfel);
  }

/**
 @brief   Returns boundary temperature stored in m_temp_or_flux
*/
  sdFLOAT boundary_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    return this->m_temp_or_flux;
  }

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    this->update_edge_temp();
    return this->implicit_solver_boundary_contribution_func(layer_data, time_index, edge_normal, scale_factor);
  }

  VOID assemble_implicit_solver_grad_coeffs(SHELL_LAYER_DATA root_surfel_nth_layer, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor);
  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

  VOID compute_face_fluxes_func(const asINT32 time_index);
  VOID compute_implicit_solver_face_fluxes_func(sSURFEL* root_surfel);
  VOID assemble_rhs_func(sSURFEL* root_surfel);
  sdFLOAT implicit_solver_boundary_contribution_func(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    return this->m_temp_or_flux;
  }

  VOID get_sampled_temp_near_edge(std::vector<sdFLOAT>& edge_temp_sample, sdFLOAT& dist_sample);

private:
  VOID update_edge_temp();

  sSURFEL* m_boundary_surfel;

} *CONDUCTION_EDGE_TEMP_BOUNDARY;

/**
 @brief   Temperature Boundary Conduction Edge Defined By Multiple Neighbor Surfels

 @details Class representing a temperature boundary defined by 2+ non-shell conduction surfels.

          Derived from cCONDUCTION_EDGE_TEMP_BOUNDARY. Stores a vector of surfels containing the temperature
          information. The `update_average_temp` function is used to calculate an average temperature from
          the neighboring surfels and stores it in m_temp_or_flux to be used in cCONDUCTION_EDGE_TEMP_BOUNDARY's
          `compute_face_fluxes_func`.

          Because this edge is not shared between shell surfels it does not need to check if another surfel
          has already caused it to update and calculate fluxes.
*/
typedef class cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY : public cCONDUCTION_EDGE_TEMP_BOUNDARY {
public:

  cCONDUCTION_EDGE_MULTI_TEMP_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
    const sdFLOAT* norm_vec, std::vector<sSURFEL*>& bc_surfels);

/**
 @brief   Updates m_temp_or_flux and applies calculates flux to apply to shell layers.
*/
  VOID compute_face_fluxes(const asINT32 time_index) {
    this->update_average_temp();
    this->compute_face_fluxes_func(time_index);
  }

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    return std::vector<int>();
  }

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    this->update_average_temp();
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    this->update_average_temp();
    this->assemble_rhs_func(root_surfel);
  }

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    this->update_average_temp();
    return this->implicit_solver_boundary_contribution_func(layer_data, time_index, edge_normal, scale_factor);
  }

  VOID implicit_solver_determine_if_cell_is_bad(sSURFEL* root_surfel) {};

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

private:
  virtual VOID update_average_temp();

  sSURFEL* m_boundary_surfel_1;
  sSURFEL* m_boundary_surfel_2;

} *CONDUCTION_EDGE_MULTI_TEMP_BOUNDARY;

/**
 @brief   Temperature Boundary Conduction Edge Defined By Dynamics Data

 @details Applies temperature BC at edge with temperature obtained from physics descriptor of neighboring
          conduction surfel.

          Derives from cCONDUCTION_EDGE_TEMP_BOUNDARY instead of cCONDUCTION_EDGE_DYN_DATA_BOUNDARY to
          allow it to reuse most of cCONDUCTION_EDGE_TEMP_BOUNDARY methods (did not want to deal
          with complexity of multiple inheritance).

          Nearly identical to cCONDUCTION_EDGE_TEMP_BOUNDARY implementation with only difference being
          that the boundary temperature is obtained from neighbor conduction surfel's physics descriptor
          rather than directly from the surfel data. This allows for the exact location to be used when calculating
          spatially varying temperatures and also avoids any issue resulting from neighbor surfels being on
          different SPs.

          Because this edge is not shared between shell surfels it does not need to check if another surfel
          has already caused it to update and calculate fluxes.
*/
typedef class cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY : public cCONDUCTION_EDGE_TEMP_BOUNDARY {
  public:
  cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY(sSURFEL* owner_surfel, dFLOAT e_length, sdFLOAT norm_dist, 
                                         const sdFLOAT* norm_vec, sSURFEL* bc_surfel);
  
  ~cCONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY() {}
  
/**
 @brief   Updates m_temp_or_flux and applies calculates flux to apply to shell layers.
*/
  VOID compute_face_fluxes(const asINT32 time_index) {
    this->update_dyn_data_temp();
    this->compute_face_fluxes_func(time_index);
  }

  std::vector<int> implicit_solver_get_edge_intersection_nbr_index() {
    return std::vector<int>();
  }

  VOID compute_implicit_solver_face_fluxes(sSURFEL* root_surfel) {
    this->update_dyn_data_temp();
    this->compute_implicit_solver_face_fluxes_func(root_surfel);
  }

  VOID assemble_rhs(sSURFEL* root_surfel) {
    this->update_dyn_data_temp();
    this->assemble_rhs_func(root_surfel);
  }

  sdFLOAT implicit_solver_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index,
                                                const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    this->update_dyn_data_temp();
    return this->implicit_solver_boundary_contribution_func(layer_data, time_index, edge_normal, scale_factor);
  }

protected:
  virtual std::ostream& print_edge_info(std::ostream &out);

private:
  virtual VOID update_dyn_data_temp();

  // Object providing access to physics descriptor with boundary data
  cEDGE_TEMP_BOUNDARY_DYN_DATA m_dyn_data;
  sSURFEL* m_bnd_surfel;
  
} *CONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY;

// Macro that casts cCONDUCTION_EDGE_BASE* pointers to the
// correct edge type class and calls the specified function
#define CALL_EDGE_CLASS_FUNC(_edge_class_type, _fname, ...)               \
  _edge_class_type int_edge = reinterpret_cast<_edge_class_type>(m_edge); \
  int_edge->_fname(__VA_ARGS__);                                          \
  break;

// Macro that casts cCONDUCTION_EDGE_BASE* pointers to the
// correct edge type class, calls the specified function
// and returns the value returned by the function
#define CALL_EDGE_CLASS_RET_FUNC(_edge_class_type, _fname, ...)           \
  _edge_class_type int_edge = reinterpret_cast<_edge_class_type>(m_edge); \
  return int_edge->_fname(__VA_ARGS__);

// Macro to select the correct edge class based on type and call a function
#define CALL_EDGE_CLASS_FUNC_SWITCH(_mname, _fname, ...)                             \
  switch(m_stencil_info) {                                                           \
    case CONDUCTION_STENCIL_FLAGS::NO_VR_INTERNAL_EDGE:                              \
    case CONDUCTION_STENCIL_FLAGS::VR_INTERNAL_EDGE: {                               \
      _mname(CONDUCTION_EDGE_INTERNAL, _fname, __VA_ARGS__);                         \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::DYN_FLUX_BOUNDARY_EDGE: {                         \
      _mname(CONDUCTION_EDGE_FLUX_DYN_DATA_BOUNDARY, _fname, __VA_ARGS__);           \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::DYN_TEMP_BOUNDARY_EDGE: {                         \
      _mname(CONDUCTION_EDGE_TEMP_DYN_DATA_BOUNDARY, _fname, __VA_ARGS__);           \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::DYN_HTC_BOUNDARY_EDGE: {                          \
      _mname(CONDUCTION_EDGE_HTC_DYN_DATA_BOUNDARY, _fname, __VA_ARGS__);            \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::TEMP_BOUNDARY_EDGE: {                             \
      _mname(CONDUCTION_EDGE_TEMP_BOUNDARY, _fname, __VA_ARGS__);                    \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::MULTI_TEMP_EDGE: {                                \
      _mname(CONDUCTION_EDGE_MULTI_TEMP_BOUNDARY, _fname, __VA_ARGS__);              \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE: {                              \
      _mname(CONDUCTION_EDGE_INTERSECTION, _fname, __VA_ARGS__);                     \
    }                                                                                \
    case CONDUCTION_STENCIL_FLAGS::ADIABATIC_EDGE: {                                 \
      _mname(CONDUCTION_EDGE_ADIABATIC_BOUNDARY, _fname, __VA_ARGS__);               \
    }                                                                                \
  }

/**
 @brief   Enumeration of edge properties to define the edge class stored in a stencil surfel

 @details Defines the stencil type for the shell solver. Because intersection edges calculate an edge temperature
          and applies it like a temp BC, it is treated as a boundary edge and neighboring surfels are not stored
          in the in the stencil
*/
typedef enum {
  // These are used by cSHELL_CONDUCTION_STENCIL_NEIGHBOR for
  // tracking stencil/edge info (will fit in uINT8)
  VERTEX_NEIGHBOR           = 0x0,
  NO_VR_INTERNAL_EDGE       = 0x01,
  VR_INTERNAL_EDGE          = 0x02,
  INTERSECTION_EDGE         = 0x04,
  ADIABATIC_EDGE            = 0x08,
  TEMP_BOUNDARY_EDGE        = 0x10,
  DYN_TEMP_BOUNDARY_EDGE    = 0x20,
  MULTI_TEMP_EDGE           = (TEMP_BOUNDARY_EDGE | DYN_TEMP_BOUNDARY_EDGE), 
  DYN_FLUX_BOUNDARY_EDGE    = 0x40,
  DYN_HTC_BOUNDARY_EDGE     = 0x80,
  DYN_DATA_BOUNDARY_EDGE    = (DYN_TEMP_BOUNDARY_EDGE | DYN_FLUX_BOUNDARY_EDGE | DYN_HTC_BOUNDARY_EDGE),
  BOUNDARY_EDGE             = (INTERSECTION_EDGE | ADIABATIC_EDGE | TEMP_BOUNDARY_EDGE | DYN_DATA_BOUNDARY_EDGE),
  INTERNAL_EDGE             = (NO_VR_INTERNAL_EDGE | VR_INTERNAL_EDGE),
  // These are used for tracking additional details when determining
  // the edge type
  UNKNOWN_EDGE_TYPE         = 0x100,
} CONDUCTION_STENCIL_FLAGS;


/**
 @brief   Class representing a shell surfel's edge and vertex neighbors in its stencil.

 @details Represents both edge and vertex neighbors and stores variables used in the LSQ gradient reconstruction
          as well as edge flux calculations.
          
          For vertex neighbors, only the pointer to the corresponding shell surfel, a vector between unfolded
          centroid and a LSQ coefficient are initialized. This provides all of the information needed
          for the calculation of the gradient with the LSQ method. Additionally, the value of m_stencil_info
          is set to CONDUCTION_STENCIL_FLAGS::VERTEX_NEIGHBOR to indicate it is a vertex neighbor without a
          corresponding conduction edge object.
          
          For edge neighbors, the a corresponding conduction edge exists which is pointed to by the
          m_edge pointer. As with the vertex neighbors, the lsq coefficients and unfolded vector
          between centroids are stored in m_lsq_coeff and m_unfolded_centroid, respectively. The pointer in 
          m_surfel is only guarenteed to be non-NULL when the edge is internal. For boundary neighbors
          there may be no or multiple surfels containing the boundary data, so the temperature information
          used in the LSQ calculation is obtained using the function `get_boundary_edge_temperature`
          which obtains it from m_edge. The function `compute_edge_fluxes` will use the m_edge object
          to update the edge information and calculate the flux into surfels associated with the edge (as
          long as another surfel at the edge has no already called the function).

          The value of m_stencil_info provides information about the edge type to be used when casting
          it to one of the derived types as well as when checking it the edge has a known temperature or
          if it can accept passthrough.

          Each shell surfel in the domain will have its own set of unique cSHELL_CONDUCTION_STENCIL_NEIGHBOR
          objects to represent its neighbors in the LSQ stencil. The conduction_edges they contain however can
          be shared between these stencil objects.
*/
typedef class cSHELL_CONDUCTION_STENCIL_NEIGHBOR {
public:
  // To overload the << operator for writing information to stdout.
  friend std::ostream & operator << (std::ostream &out, cSHELL_CONDUCTION_STENCIL_NEIGHBOR* c);
/**
 @brief   Constructor to initialize stencils representing vertex neighbors.

 @details Vertex neighbors do not have a conduction edge associated with them and therefore will
          need to use this constructor.

          When initialized, the unfolded centroid will be known, so it should be passed to the constructor.
*/
  cSHELL_CONDUCTION_STENCIL_NEIGHBOR(sSURFEL* s, const dFLOAT unfold[2]) 
      : m_surfel(s), m_edge(NULL), m_lsq_coeff{-1e10,-1e10},
        m_stencil_info(CONDUCTION_STENCIL_FLAGS::VERTEX_NEIGHBOR) {
    vcopy2(m_unfolded_centroid, unfold);
  }

/**
 @brief   Constructor to initialize stencils of internal edges

 @details Must pass the neighboring surfel, the internal edge (as a CONDUCTION_EDGE_BASE), the unfolded centroid
          and a uINT8 defining the type of edge neighbor it represents.

          When initialized, the unfolded centroid will be known, so it should be passed to the constructor.
*/
  cSHELL_CONDUCTION_STENCIL_NEIGHBOR(sSURFEL* s, CONDUCTION_EDGE_BASE edge,
    const dFLOAT unfold[2], uINT8 stencil_info) 
      : m_surfel(s), m_edge(edge), m_lsq_coeff{-1e10,-1e10}, m_stencil_info(stencil_info) {
    vcopy2(m_unfolded_centroid, unfold);
    cassert(stencil_info & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
  }

/**
 @brief   Constructor to initialize stencils of boundary edges.

 @details In this case, m_surfel will remain NULL, but all neighbor info will be available in the
          conduction edge object. The type of edge this stencil represents must also be passed so
          that the stencil knows what type to cast m_edge to.

          When initialized, the unfolded centroid will be known, so it should be passed to the constructor.
*/
  cSHELL_CONDUCTION_STENCIL_NEIGHBOR(CONDUCTION_EDGE_BASE edge, const dFLOAT unfold[2],
    const uINT8 stencil_info) 
      : m_surfel(NULL), m_edge(edge), m_lsq_coeff{-1e10,-1e10}, m_stencil_info(stencil_info) {
    vcopy2(m_unfolded_centroid, unfold);
    cassert(m_stencil_info & CONDUCTION_STENCIL_FLAGS::BOUNDARY_EDGE);
  }

/**
 @brief   Sets the values in the lsq coefficient vector
*/
  template <typename FLOAT>
  VOID set_lsq_coeff(const FLOAT lsqcoeff[2]) {
    vcopy2(m_lsq_coeff, lsqcoeff);
  }

/**
 @brief   Returns the unfolded centroid vector
*/
  const sdFLOAT* get_unfolded_centroid() const {
    cassert(m_unfolded_centroid[0] > -1e9);
    return m_unfolded_centroid;
  }

/**
 @brief   Returns the lsq coefficient vector
*/
  const sdFLOAT* get_lsq_coeff() const {
    cassert(m_lsq_coeff[0] > -1e9);
    return m_lsq_coeff;
  }

/**
 @brief   Returns the angle of the unfolded centroid vector
*/
  sdFLOAT get_unfolded_centroid_angle() const {
    cassert(m_unfolded_centroid[0] > -1e9);
    sdFLOAT unfolded_vec[2];
    vcopy2(unfolded_vec, m_unfolded_centroid);
    vunitize2(unfolded_vec);
    return atan2(unfolded_vec[1],unfolded_vec[0]);
  }

/**
 @brief   Returns the pointer to the conduction edge object.

 @details Warning: Due to the lack of virtual functions, the pointer to the base class should
          not be used to access anything other than the edge geometry without first casting it
          to the actual type.
*/
  CONDUCTION_EDGE_BASE get_edge() {
    return m_edge;
  }

/**
 @brief   Returns a pointer to the edge casted as a CONDUCTION_EDGE_INTERNAL object

 @details Will throw error if the flags are not set to one of the internal edge types
*/
  CONDUCTION_EDGE_INTERNAL get_internal_edge() {
    cassert(m_stencil_info & CONDUCTION_STENCIL_FLAGS::INTERNAL_EDGE);
    return reinterpret_cast<CONDUCTION_EDGE_INTERNAL>(m_edge);
  }

/**
 @brief   Returns TRUE if the edge can accept passthrough.
*/
  BOOLEAN edge_accepts_passthrough() {
    return (m_stencil_info & (CONDUCTION_STENCIL_FLAGS::NO_VR_INTERNAL_EDGE
                            | CONDUCTION_STENCIL_FLAGS::MULTI_TEMP_EDGE));
  }

/**
 @brief   Returns TRUE if the edge has a known or calculable temperature.
*/
  BOOLEAN edge_has_temperature() {
    return (m_stencil_info & (CONDUCTION_STENCIL_FLAGS::MULTI_TEMP_EDGE
                            | CONDUCTION_STENCIL_FLAGS::INTERSECTION_EDGE));
  }

/**
 @brief   Calls the flux calculation function in the corresponding conduction edge object
*/
  VOID compute_edge_fluxes(const asINT32 time_index) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_FUNC, compute_face_fluxes, time_index);
  }

/**
 @brief   Implicit solver: calls the flux calculation function in the corresponding conduction edge object
*/
  VOID compute_implicit_solver_edge_fluxes(sSURFEL* root_surfel) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_FUNC, compute_implicit_solver_face_fluxes, root_surfel);
  }

  VOID assemble_layer_boundary_edge_gradient_coefficients(sSHELL_LAYER_DATA* layer_data, const sdFLOAT* stencil_surfel_lsq_coeff, STATE_VECTOR_INDEX column_index, sdFLOAT scale_factor) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_FUNC, assemble_implicit_solver_grad_coeffs, layer_data, stencil_surfel_lsq_coeff, column_index, scale_factor); 
  }

  VOID implicit_solver_assemble_rhs(sSURFEL* root_surfel) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_FUNC, assemble_rhs, root_surfel);
  }

  sdFLOAT implicit_solver_get_boundary_contribution(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index, const sdFLOAT* edge_normal, sdFLOAT scale_factor) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_RET_FUNC, implicit_solver_boundary_contribution, layer_data, time_index, edge_normal, scale_factor);
    return 0.;
  }

  VOID implicit_solver_determine_bad_cells(sSURFEL* root_surfel) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_FUNC, implicit_solver_determine_if_cell_is_bad, root_surfel);
  }


  std::vector<int> implicit_solver_edge_intersection_nbr_index() {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_RET_FUNC, implicit_solver_get_edge_intersection_nbr_index);
    return std::vector<int>();
  }
/**
 @brief   Returns the boundary edge temperature from the corresponding conduction edge object
*/
  sdFLOAT get_boundary_edge_temperature(sSHELL_LAYER_DATA* layer_data, const asINT32 time_index) {
    CALL_EDGE_CLASS_FUNC_SWITCH(CALL_EDGE_CLASS_RET_FUNC, boundary_temperature, layer_data, time_index);
    return 0.;
  }

/**
 @brief   Returns a pointer to the surfel

 @details This does not check for the pointer being NULL before returning it, so use with caution
*/
  sSURFEL* surfel() { return m_surfel; }

/**
 @brief   Returns the value of m_stencil_info defining the type of stencil/edge
*/
  uINT8 stencil_type() { return m_stencil_info; }

  std::ostream& print_edge_info(std::ostream &out);

private:
  sSURFEL* m_surfel;
  CONDUCTION_EDGE_BASE m_edge;
  // if the edge implementation of LSQ is used, the unfolded centroid is only needed at internal edges and
  // therefore stores it directly in the edge object. No lsq coeffs are needed as the edge LSQ object
  // stores that information and it is only stored in CONDUCTION_EDGE_BASE types that need it.
  sdFLOAT m_lsq_coeff[2];
  sdFLOAT m_unfolded_centroid[2];
  uINT8 m_stencil_info;
} *SHELL_CONDUCTION_STENCIL_NEIGHBOR;

/**
  @brief   Overload of << operator for cSHELL_CONDUCTION_STENCIL_NEIGHBOR objects.

  @details If switching to writing to log files, may want to remove this. Could get confusing if
           cout << stencil_object << "Some Text" << endl; writes the stencil data to a log file and
           the rest to stdout.
*/
INLINE std::ostream & operator << (std::ostream &out, cSHELL_CONDUCTION_STENCIL_NEIGHBOR* c) {
  return c->print_edge_info(out);
}


/**
 @brief   Structure to hold stencil information for all shell conduction surfels

 @details This structure has two vectors: one that stores cSHELL_CONDUCTION_STENCIL_NEIGHBOR objects
          providing neighbor and edge information for each shell surfel and another that stores
          CONDUCTION_EDGE_BASE objects that provides the methods for calculating fluxes across
          edges and stores some edge information as well.
          
          Each shell surfel in on the SP has its own unique set of stencils representing its edge and vertex
          neighbors. Because of this, multiple stencil objects can represent the same neighboring shell
          surfel or boundary edge with each instance corresponding to a different root shell surfel. 
          These stencils are stored in a 1D vector with grouped by the root surfel the correspond to.
          Within the group, the shell edge neighbors are always first (which have internal edge types),
          followed by boundary edge neighbors (all other edge types) shell vertex neighbors are last.
          The root shell surfel class stores the indices of the first shell edge neighbor
          (stencil_info_internal_edge_index), the first boundary edge neighbor (stencil_info_boundary_edge_index),
          the first vertex edge neighbor (stencil_info_vertex_nbr_index) and and the next surfel's first
          shell edge neighbor (stencil_info_end_index) which it uses to access the stencils associated
          with it in the vector. Macros are provided in conduction_data.h to loop through these
          different neighbor types.

          The ordering of CONDUCTION_EDGE_BASE objects in m_edges is dependent on the order that the edge
          was first created and since CONDUCTION_EDGE_BASE objects can be shared between shell surfels,
          there is no guarentee that the a given shell surfel will have all edges grouped together in
          the vector. Access to these is therefore provided through the cSHELL_CONDUCTION_STENCIL_NEIGHBOR
          objects which contain pointers to the CONDUCTION_EDGE_BASE they correspond to.

          The stencil and edge objects can be obtained using the `get_nbr_stencil`, `get_edge`, and
          `get_internal_edge` functions. 
*/
typedef class cSHELL_CONDUCTION_STENCIL_INFO {
public:

  cSHELL_CONDUCTION_STENCIL_INFO() {}

  ~cSHELL_CONDUCTION_STENCIL_INFO() {
    ccDOTIMES(i, m_edges.size()) {
      delete m_edges[i];
    }

    ccDOTIMES(i, m_stencil_nbrs.size()) {
      delete m_stencil_nbrs[i];
    }

    m_edges.clear();
    m_stencil_nbrs.clear();
  }

/**
 @brief   Returns the SHELL_CONDUCTION_STENCIL_NEIGHBOR object at index n
*/
  SHELL_CONDUCTION_STENCIL_NEIGHBOR get_nbr_stencil(const asINT32 n) {
    return m_stencil_nbrs[n];
  }

/**
 @brief   Returns the CONDUCTION_EDGE_BASE object stored in the SHELL_CONDUCTION_STENCIL_NEIGHBOR object at index n
*/
  CONDUCTION_EDGE_BASE get_edge(const asINT32 n) {
    return m_stencil_nbrs[n]->get_edge();
  }

/**
 @brief   Returns the CONDUCTION_EDGE_INTERNAL object stored in the SHELL_CONDUCTION_STENCIL_NEIGHBOR object at index n

 @details Warning, this will cast any edge to a CONDUCTION_EDGE_INTERNAL without checking and should only
          be used when the edge is known to be internal.
*/
  CONDUCTION_EDGE_INTERNAL get_internal_edge(const asINT32 n) {
    return reinterpret_cast<CONDUCTION_EDGE_INTERNAL>(m_stencil_nbrs[n]->get_edge());
  }

/**
 @brief   Returns the nth SHELL_CONDUCTION_STENCIL_NEIGHBOR object of shell surfel root_surfel
*/
  SHELL_CONDUCTION_STENCIL_NEIGHBOR get_nbr_stencil(sSURFEL* root_surfel, const asINT32 n) {
    return m_stencil_nbrs[(get_root_surfel_start_index(root_surfel) + n)];
  }

/**
 @brief   Returns the CONDUCTION_EDGE_BASE stored in the nth SHELL_CONDUCTION_STENCIL_NEIGHBOR object
          of shell surfel root_surfel
*/
  CONDUCTION_EDGE_BASE get_edge(sSURFEL* root_surfel, const asINT32 n) {
    return m_stencil_nbrs[(get_root_surfel_start_index(root_surfel) + n)]->get_edge();
  }

/**
 @brief   Stores the edge object provided in m_edges.

 @details This class is responsible for deallocating the memory
*/
  VOID add_edge_to_vector(CONDUCTION_EDGE_BASE new_edge) {
    m_edges.push_back(new_edge);
  }

/**
 @brief   Stores the edge stencil object provided in m_stencil_nbrs.

 @details This class is responsible for deallocating the memory
*/
  VOID add_stencil_to_vector(SHELL_CONDUCTION_STENCIL_NEIGHBOR new_stencil) {
    m_stencil_nbrs.push_back(new_stencil);
  }

/**
 @brief   Stores the edge stencil object provided in m_stencil_nbrs.

 @details Used when setting indices stored in the shell conduction data of the surfel
*/
  asINT32 total_num_stencils() {
    return m_stencil_nbrs.size();
  }

  VOID set_proc_has_shells(BOOLEAN prochasshells) {
    m_proc_has_shells = prochasshells;
  }

  VOID set_num_procs(asINT32 nprocs) {
    m_num_procs = nprocs;
  }

  asINT32 num_procs() { return m_num_procs; }

  BOOLEAN proc_has_shells() { return m_proc_has_shells; }
private:
  asINT32 get_root_surfel_start_index(sSURFEL* root_surfel);

  std::vector<CONDUCTION_EDGE_BASE> m_edges;
  std::vector<SHELL_CONDUCTION_STENCIL_NEIGHBOR> m_stencil_nbrs;
  asINT32 m_num_procs;
  BOOLEAN m_proc_has_shells;
} *SHELL_CONDUCTION_STENCIL_INFO;

extern cSHELL_CONDUCTION_STENCIL_INFO g_shell_conduction_edges;

}

#undef DEBUG_BREP_STENCIL

#endif
