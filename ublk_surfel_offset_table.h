#ifndef UBLK_SURFEL_OFFSET_TABLE_H
#define UBLK_SURFEL_OFFSET_TABLE_H
#include "common_sp.h"
#include "sim.h"

// If we use the flow + conduction numbers, we run out of __constant__ gpu memory,
// so we have to go back to the 'no-conduction' numbers here
#if BUILD_GPU

#define N_MAX_SURFEL_DATA_TYPES 32
#define N_MAX_SURFEL_TYPES 256
#define N_MAX_SURFEL_PHYSICS_TYPES 128

#define N_UBLK_DATA_TYPES 32
#define N_UBLK_TYPES 24
#define N_MAX_UBLK_PHYSICS_TYPES 8

#else

#define N_MAX_SURFEL_DATA_TYPES 64
// CONDUCTION-TODO: Reduce the number to 544 when surfel memory is optimized
#define N_MAX_SURFEL_TYPES 4096
#define N_MAX_SURFEL_PHYSICS_TYPES 128

#define N_UBLK_DATA_TYPES 32
#define N_UBLK_TYPES 48
#define N_MAX_UBLK_PHYSICS_TYPES 9

#endif

struct sUBLK_DYNAMICS_ATTR_DATA;
struct DATA_OFFSET_TABLE_OPTS;

inline namespace
SIMULATOR_NAMESPACE {

  template<typename UBLK_TYPE_TAG, typename ATTRIBUTE_SPEC>
  size_t compute_ublk_dynamics_data_size(asINT32 n_phys_types,
					 STP_PHYSTYPE_TYPE* phys_types,
					 ATTRIBUTE_SPEC attribute_data);  

  //SURFEL TABLE
 size_t surfel_dynamics_data_size(STP_PHYSTYPE_TYPE phys_type);

 template<typename tSURFEL>
 VOID initialize_surfel_data_offset_table(const DATA_OFFSET_TABLE_OPTS&);
  
 VOID save_surfel_table_to_file(BOOLEAN);
 VOID save_ublk_table_to_file(BOOLEAN);


 //UBLK TABLE
 template<typename tUBLK>
 VOID initialize_ublk_data_offset_table(const DATA_OFFSET_TABLE_OPTS&);

 extern uINT16 g_ublk_data_offset_table_8[N_UBLK_TYPES][N_UBLK_DATA_TYPES];
 extern uINT16 g_ublk_dynamics_data_size_8[N_MAX_UBLK_PHYSICS_TYPES];  
 extern asINT32 g_surfel_data_offset_table[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];
	 
#ifdef BUILD_GPU
 extern uINT16 g_ublk_data_offset_table_64[N_UBLK_TYPES][N_UBLK_DATA_TYPES];
 extern uINT16 g_ublk_dynamics_data_size_64[N_MAX_UBLK_PHYSICS_TYPES];  
 extern asINT32 g_surfel_data_offset_table_64[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];
#endif

 extern asINT32 g_surfel_dynamics_data_sizes[N_MAX_SURFEL_PHYSICS_TYPES];

 template<typename UBLK_TYPE_TAG, BOOLEAN IS_MIXED_TYPE>
 size_t get_dynamic_attribute_size(STP_PHYSTYPE_TYPE sim_phys_type, sUBLK_DYNAMICS_ATTR_DATA *attribute_data);
} // end simulator namespace

template<size_t N_VOXELS> uINT16 get_ublk_data_offset(asINT32 ublk_type, asINT32 data_type);

template<size_t N_VOXELS> uINT16& set_ublk_data_offset(asINT32 ublk_type, asINT32 data_type);

template<size_t N_VOXELS> uINT16 get_ublk_dynamics_data_size(asINT32 data_type);

template<size_t N_VOXELS> uINT16& set_ublk_dynamics_data_size(asINT32 data_type);

template<size_t N_VOXELS> asINT32 get_surfel_data_offset(asINT32 surfel_type, asINT32 data_type);

template<size_t N_VOXELS> asINT32& set_surfel_data_offset(asINT32 surfel_type, asINT32 data_type);

template<>
__HOST__DEVICE__ INLINE uINT16 get_ublk_data_offset<8>(asINT32 ublk_type, asINT32 data_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_ublk_data_offset_table_8[ublk_type][data_type];
#else
 assert(false);
 return 0;
#endif
}

template<>
__HOST__DEVICE__ INLINE uINT16 get_ublk_dynamics_data_size<8>(asINT32 phys_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_8[phys_type];
#else
 assert(false);
 return 0;
#endif
}

template<>
__HOST__DEVICE__ INLINE asINT32 get_surfel_data_offset<1>(asINT32 surfel_type, asINT32 data_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_surfel_data_offset_table[surfel_type][data_type];
#else
 assert(false);
 return 0;
#endif
}

template<>
__HOST__ INLINE uINT16& set_ublk_data_offset<8>(asINT32 ublk_type, asINT32 data_type) {
 return SIMULATOR_NAMESPACE::g_ublk_data_offset_table_8[ublk_type][data_type];
}

template<>
__HOST__ INLINE uINT16& set_ublk_dynamics_data_size<8>(asINT32 phys_type) {
 return SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_8[phys_type];
}

template<>
__HOST__ INLINE asINT32& set_surfel_data_offset<1>(asINT32 surfel_type, asINT16 data_type) {
  return SIMULATOR_NAMESPACE::g_surfel_data_offset_table[surfel_type][data_type];
}

#ifdef BUILD_GPU

#if GPU_COMPILER
namespace GPU {
  //GPU equivalents of offset tables
  extern __constant__ __device__ uINT16 g_ublk_data_offset_table_64[N_UBLK_TYPES][N_UBLK_DATA_TYPES];
  extern __constant__ __device__ uINT16 g_ublk_dynamics_data_size_64[N_MAX_UBLK_PHYSICS_TYPES];
  extern __constant__ __device__ asINT32 g_surfel_data_offset_table_64[N_MAX_SURFEL_TYPES][N_MAX_SURFEL_DATA_TYPES];  
}
#endif

template<>
__HOST__DEVICE__ INLINE uINT16 get_ublk_data_offset<64>(asINT32 ublk_type, asINT32 data_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_ublk_data_offset_table_64[ublk_type][data_type];
#else
 return GPU::g_ublk_data_offset_table_64[ublk_type][data_type];
#endif
}

template<>
__HOST__DEVICE__ INLINE uINT16 get_ublk_dynamics_data_size<64>(asINT32 data_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_64[data_type];
#else
 return GPU::g_ublk_dynamics_data_size_64[data_type];
#endif
}

template<>
__HOST__DEVICE__ INLINE asINT32 get_surfel_data_offset<N_SFLS_PER_MSFL>(asINT32 surfel_type, asINT32 data_type) {
#if !DEVICE_COMPILATION_MODE
 return SIMULATOR_NAMESPACE::g_surfel_data_offset_table_64[surfel_type][data_type];
#else
 return GPU::g_surfel_data_offset_table_64[surfel_type][data_type];
#endif
}

template<>
__HOST__ INLINE uINT16& set_ublk_data_offset<64>(asINT32 ublk_type, asINT32 data_type) {
 return SIMULATOR_NAMESPACE::g_ublk_data_offset_table_64[ublk_type][data_type];
}

template<>
__HOST__ INLINE uINT16& set_ublk_dynamics_data_size<64>(asINT32 data_type) {
 return SIMULATOR_NAMESPACE::g_ublk_dynamics_data_size_64[data_type];
}

template<>
__HOST__ INLINE asINT32& set_surfel_data_offset<N_SFLS_PER_MSFL>(asINT32 surfel_type, asINT32 data_type) {
 return SIMULATOR_NAMESPACE::g_surfel_data_offset_table_64[surfel_type][data_type];
}

#endif //BUILD_GPU

#endif
