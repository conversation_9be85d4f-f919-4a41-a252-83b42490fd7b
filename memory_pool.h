/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Memory pool definition
 *
 * Anush Krishnan, Exa Corporation 
 * Created Mon Apr 17, 2017
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_MEMORY_POOL_H
#define _SIMENG_MEMORY_POOL_H

#include <vector>
#include <cstdlib>
#include <assert.h>

/**
 *  tMEMORY_POOL
 *  ========================
 */

namespace _internal_
{

template<int Int> struct CompileTimeInt;

template<>
struct CompileTimeInt<-1>
{
  private:
  int m_int;
  public:
  CompileTimeInt(int _int) : m_int(_int) {};

  int get() const { return m_int; }
};

template<int Int>
struct CompileTimeInt
{
  public:
  CompileTimeInt(int _int) {};

  static constexpr int get() { return Int; }
};

};

template<typename T, int ARRAY_SIZE=-1, int BLOCK_SIZE=-1>
class tMEMORY_POOL
{
  static const size_t T_SIZE = sizeof(T);
  private:
  union FreeList 
  {
    FreeList* next;
    unsigned char dummy[T_SIZE];
  };

  std::vector<FreeList*> m_blocks;
  const _internal_::CompileTimeInt<ARRAY_SIZE> m_array_size;
  const _internal_::CompileTimeInt<BLOCK_SIZE> m_block_size;
  FreeList *m_head;

  tMEMORY_POOL(const tMEMORY_POOL<T>& other); // no copying

  public:

  tMEMORY_POOL(int array_size=ARRAY_SIZE, int block_size=BLOCK_SIZE) : 
    m_array_size(array_size), m_block_size(block_size), m_head(NULL)
  {
    assert( m_block_size.get() > 0 );
    assert( m_array_size.get() > 0 );
    assert( T_SIZE >= sizeof(FreeList*) );
  };

  ~tMEMORY_POOL()
  {
    for (typename std::vector<FreeList*>::iterator it = m_blocks.begin(); it != m_blocks.end(); it++ ) {
      std::free(*it);
    }
  }

  T * pop()
  {
    if (!m_head) {
      allocate_new_block();
    }

    FreeList * old_head = m_head;
    m_head = m_head->next;
    return reinterpret_cast<T*>(old_head);
  }

  void push(void* dead_elem)
  {
    FreeList * old_elem = (FreeList*) dead_elem;
    old_elem->next = m_head;
    m_head = old_elem;
  }

  private:
  void allocate_new_block()
  {
    size_t block_bytes = sizeof(FreeList) * m_array_size.get() * m_block_size.get();
    FreeList* new_block = (FreeList*) std::malloc(block_bytes);
    init_new_block(new_block);
    m_blocks.push_back(new_block);
  }

  void init_new_block(FreeList * new_block)
  {
    FreeList * prev = new_block;
    FreeList * curr = prev+m_array_size.get();

    for(int i=0;i<m_block_size.get()-1;i++) {
      prev->next = curr;
      prev = curr;
      curr += m_array_size.get();
    }
    prev->next = m_head;
    m_head = new_block;
  }

  public:

  size_t count_free_objects()
  {
    size_t count = 0;
    FreeList * tmp = m_head;
    while (tmp) {
      count++;
      tmp = tmp->next;
    }
    return count;
  }

  size_t n_bytes_per_array() const { return T_SIZE*m_array_size.get(); }

  size_t array_size() const { return m_array_size.get(); }
  size_t block_size() const { return m_block_size.get(); }

  size_t n_blocks() const { return m_blocks.size(); }

};

#endif /* _SIMENG_MEMORY_POOL_H */
