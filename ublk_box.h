#ifndef UBLK_BOX_H
#define UBLK_BOX_H

#include "tagged_ublk.h"
#include "vectorization_support.h"

// The ublks are arranged the same way as the voxels 
// U0 : (0, 0, 0)
// U1 : (0, 0, 1)
// U2 : (0, 1, 0)
// U3 : (0, 1, 1)
// U4 : (1, 0, 0)
// U5 : (1, 0, 1)
// U6 : (1, 1, 0)
// U7 : (1, 1, 1)
//
template<typename UBLK_TYPE_TAG>
class ALIGN_VECTOR tSCALE_BOX_INTERFACE 
{
public:
#if BUILD_GPU
 static const size_t ALIGNMENT = 256;
#else
#if EXA_USE_SSE
 static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
 static const size_t ALIGNMENT = 32;
#endif
#endif
    using TAGGED_UBLK_TYPE = tTAGGED_UBLK<UBLK_TYPE_TAG>;
    static constexpr size_t N_VOXELS = N_VOXELS_8;

  TAGGED_UBLK_TYPE m_fine_ublks[N_VOXELS_8];

  tSCALE_BOX_INTERFACE () 
  {
    ccDOTIMES (voxel, N_VOXELS_8) {
      m_fine_ublks[voxel] = nullptr;
    }
  }

  tSCALE_BOX_INTERFACE(tSCALE_BOX_INTERFACE &interface) {
    ccDOTIMES (voxel, N_VOXELS_8) {
      m_fine_ublks[voxel] = interface.m_fine_ublks[voxel];
    }
  }
};

using sSCALE_BOX_INTERFACE = tSCALE_BOX_INTERFACE<UBLK_SDFLOAT_TYPE_TAG>;
using SCALE_BOX_INTERFACE = sSCALE_BOX_INTERFACE*;
using sSCALE_BOX_INTERFACE_64 = tSCALE_BOX_INTERFACE<MBLK_SDFLOAT_TYPE_TAG>;

// This structure isolates the elements needed to divide boxes during
// initialization.
//
typedef class sUBLK_BOX_FILL_INFO_FOR_DIVISION {
  public:
  // bit array to store whether a ublk slot is filled
  cPAGED_BITMAP *m_are_slots_filled;
  std::vector <uINT32> m_n_filled_slots_per_plane[3];
  uINT32 m_n_filled_slots;
  uINT16 m_filled_array_size[3];

  // constructor
  sUBLK_BOX_FILL_INFO_FOR_DIVISION (STP_COORD box_size[3],
                                    sUBLK_BOX_FILL_INFO_FOR_DIVISION *parent_info) {

    m_n_filled_slots = 0;

    // Allocation of the bit array will only be done for the original box,
    // later this bit array will be used all the sub divided boxes.
    if (parent_info == NULL) {

      uINT64 n_ublks = box_size[0] * box_size[1] * box_size[2];

      m_are_slots_filled     = cnew cPAGED_BITMAP(n_ublks);
      m_filled_array_size[0] = box_size[0];
      m_filled_array_size[1] = box_size[1];
      m_filled_array_size[2] = box_size[2];
    } else {
      m_are_slots_filled     = parent_info->m_are_slots_filled;
      m_filled_array_size[0] = parent_info->m_filled_array_size[0];
      m_filled_array_size[1] = parent_info->m_filled_array_size[1];
      m_filled_array_size[2] = parent_info->m_filled_array_size[2];
    }

    m_n_filled_slots_per_plane[0].resize(box_size[0]);
    m_n_filled_slots_per_plane[1].resize(box_size[1]);
    m_n_filled_slots_per_plane[2].resize(box_size[2]);
  }

  ~sUBLK_BOX_FILL_INFO_FOR_DIVISION() {
    m_n_filled_slots_per_plane[0].erase(m_n_filled_slots_per_plane[0].begin(),
                                        m_n_filled_slots_per_plane[0].end());
    m_n_filled_slots_per_plane[1].erase(m_n_filled_slots_per_plane[1].begin(),
                                        m_n_filled_slots_per_plane[1].end());
    m_n_filled_slots_per_plane[2].erase(m_n_filled_slots_per_plane[2].begin(),
                                        m_n_filled_slots_per_plane[2].end());
  }

  BOOLEAN is_plane_empty(sINT32 axis, sINT32 index) {
    return (m_n_filled_slots_per_plane[axis][index] == 0);
  }

  uINT32 n_ublk_slots(STP_COORD m_size[3]) {
    uINT32 n_slots = (m_size[0]-2) * (m_size[1]-2) * (m_size[2]-2);
    if (sim.num_dims == 2) {
      n_slots = (m_size[0]-2) * (m_size[1]-2);
    }
    return n_slots;
  }

  BOOLEAN is_division_required(STP_COORD m_size[3]) {
    // MIN_BOX_DIMENSION is increased by 2, since there is always a
    // one layer overlap on each side. Also box division algorithm
    // ignores the extra two overlapping layers.
    if (m_size[0] < MIN_BOX_DIMENSION + 2) {
      return FALSE;
    }
    if (m_size[1] < MIN_BOX_DIMENSION + 2) {
      return FALSE;
    }
    if ((sim.num_dims == 3) && (m_size[2] < MIN_BOX_DIMENSION + 2)) {
      return FALSE;
    }
    return (m_n_filled_slots < (uINT32)(MIN_FILLED_SLOTS_FRACTION * n_ublk_slots(m_size)));
  }

  BOOLEAN is_ublk_present_in_slot(uINT16 indices[3]) {

    asINT32 index = m_filled_array_size[2] * (m_filled_array_size[1] * indices[0]
                                              + indices[1]) + indices[2];
    return (m_are_slots_filled->GetBit(index));
  }

  VOID mark_ublk_present_in_slot(uINT16 indices[3]) {

    asINT32 index = m_filled_array_size[2] * (m_filled_array_size[1] * indices[0]
                                              + indices[1]) + indices[2];
    m_are_slots_filled->SetBit(index);
  }

  VOID increment_ublks_filled_count(uINT16 indices[3]) {
    m_n_filled_slots_per_plane[0][indices[0]]++;
    m_n_filled_slots_per_plane[1][indices[1]]++;
    m_n_filled_slots_per_plane[2][indices[2]]++;
    m_n_filled_slots++;
  }

  BOOLEAN is_maximum_occupancy_sensible(sFLOAT occupancy_ratio) {
    return (occupancy_ratio > BISECT_BELOW_RATIO);
  }

  VOID adjust_filled_ublks_per_plane(uINT16 start_index[3], uINT16 end_index[3],
                                     STP_COORD box_size[3], BOOLEAN reset);
  const static uINT32 MIN_BOX_DIMENSION  = 16;
  private:
  CONST static float MIN_FILLED_SLOTS_FRACTION = 0.6;
  // ratio of maximum to mean occupancy
  CONST static float  BISECT_BELOW_RATIO = 1.2;
} *UBLK_BOX_FILL_INFO_FOR_DIVISION;

const asINT32 csys_dir1[3] = {1, 2, 0};
const asINT32 csys_dir2[3] = {2, 0, 1};

template<typename UBLK_TYPE_TAG>
class tUBLK_BOX {
 public:
   STP_SCALE                 m_scale;
   REALM                     m_realm;
   sINT8                     m_lrf_index;
   uINT8                     m_isolated_domain;
   // m_box_index will be set to 0 while parsing_ublks, but will be later set
   // to higher values if a ublk box has to be split in case of too many empty
   // ublk slots.
   uINT16                    m_box_index;
   STP_COORD                 m_n_ublks;
   STP_COORD                 m_size[3]; // wasting 16 bits
   STP_COORD                 m_location[3];
   uINT32                    m_offsets[3];   
   
  // information needed for splitting ublk box into multiple boxes
  // The splitting is done to save memory in case the occupancy is too sparse.
  sUBLK_BOX_FILL_INFO_FOR_DIVISION *m_fill_info;

  uINT16 m_start_indices[3]; // These can be overlapped with m_ublks

  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using UBLK_TYPE = sUBLK_TYPE*;
  using TAGGED_UBLK_TYPE = tTAGGED_UBLK<UBLK_TYPE_TAG>;
  
  public:
  
  typename std::conditional<!GPU_COMPILER,
                            std::vector<TAGGED_UBLK_TYPE>,
                            TAGGED_UBLK_TYPE*>::type m_ublks;

  template<typename UBLK_TYPE_TAG_OTHER>
  void copy(const tUBLK_BOX<UBLK_TYPE_TAG_OTHER>* other) {    
    this->m_size[0] = other->m_size[0];
    this->m_size[1] = other->m_size[1];
    this->m_size[2] = other->m_size[2];
    this->m_location[0] = other->m_location[0];
    this->m_location[1] = other->m_location[1];
    this->m_location[2] = other->m_location[2];
    this->m_scale     = other->m_scale;
    this->m_box_index = other->m_box_index;
    this->m_lrf_index = other->m_lrf_index;
    this->m_isolated_domain = other->m_isolated_domain;
    this->allocate_ublks();
  }

  tUBLK_BOX(){};
  
  template<typename UBLK_TYPE_TAG_OTHER>
  tUBLK_BOX(const tUBLK_BOX<UBLK_TYPE_TAG_OTHER>* other) {
    this->init();
    copy(other);
  }

  template<typename UBLK_TYPE_TAG_OTHER>
  tUBLK_BOX& operator=(const tUBLK_BOX<UBLK_TYPE_TAG_OTHER>* other) {
    if ((void*) this != (void*) other) {
      this->init();
      copy(other);
    }
    return *this;
  }
  
  VOID init() {
    m_n_ublks     = 0;
    m_size[0]     = 0;
    m_size[1]     = 0;
    m_size[2]     = 0;    
    // Maximum possible value, last bit is sign bit
    m_location[0] = (STP_COORD)((uINT32)(1<<30)-1);
    m_location[1] = (STP_COORD)((uINT32)(1<<30)-1);
    m_location[2] = (STP_COORD)((uINT32)(1<<30)-1);

    m_fill_info = NULL;
    m_start_indices[0]    = 0;
    m_start_indices[1]    = 0;
    m_start_indices[2]    = 0;
  }

  uINT32 n_ublks() const { return m_size[0] * m_size[1] * m_size[2]; }
  
  VOID allocate_ublks() {
    uINT32 n_ublks = m_size[0] * m_size[1] * m_size[2];
    m_n_ublks = n_ublks;
    m_offsets[0] = m_size[1] * m_size[2];
    m_offsets[1] = m_size[2];
    m_offsets[2] = 1;
#if !GPU_COMPILER
    m_ublks.reserve(n_ublks);
    m_ublks.resize(n_ublks);
#else
    size_t n_bytes = sizeof(TAGGED_UBLK_TYPE) * n_ublks;
    m_ublks = (TAGGED_UBLK_TYPE*) malloc(n_bytes);
    memset(m_ublks, 0, n_bytes);
#endif    
  }


  TAGGED_UBLK_TYPE *assign_ublk(UBLK_TYPE ublk);

  VOID divide_ublk_box();
  VOID find_min_empty_planes(asINT32 &axis, asINT32 &location);
  VOID find_largest_occupancy_splitting_plane(asINT32 &axis, asINT32 &location);
  VOID split_ublk_box(asINT32 axis, asINT32 split_index);
  VOID remove_empty_layers(asINT32 split_axis, uINT16 start_index[3], uINT16 end_index[3]);

  VOID update_box_dimensions(STP_COORD ublk_location[3]) {
    m_location[0] = MIN(m_location[0], ublk_location[0]);
    m_location[1] = MIN(m_location[1], ublk_location[1]);
    m_location[2] = MIN(m_location[2], ublk_location[2]);
    m_size[0] = MAX(m_size[0], ublk_location[0]);
    m_size[1] = MAX(m_size[1], ublk_location[1]);
    m_size[2] = MAX(m_size[2], ublk_location[2]);
    m_n_ublks = m_size[0] * m_size[1] * m_size[2];
  }

  VOID assign_scale_interface(sSCALE_BOX_INTERFACE *scale_interface, uINT16 index) {
    m_ublks[index].set_scale_interface(scale_interface);
  }

  asINT32 ublk_size() {
    return scale_to_voxel_size(this->m_scale) * sUBLK_TYPE::N_VOXELS_1D;
  }

  __HOST__DEVICE__ VOID get_indices(STP_COORD ublk_location[3], uINT16 indices[3]) {
    if (ublk_location[0] < this->m_location[0]) {
      msg_internal_error("X box location %d ublk location %d", this->m_location[0], ublk_location[0]);
    }
    if (ublk_location[1] < this->m_location[1]) {
      msg_internal_error("Y box location %d ublk location %d", this->m_location[1], ublk_location[1]);
    }
    if (ublk_location[2] < this->m_location[2]) {
      msg_internal_error("Z box location %d ublk location %d", this->m_location[2], ublk_location[2]);
    }
    indices[0] = (ublk_location[0] - this->m_location[0]) / ublk_size();
    indices[1] = (ublk_location[1] - this->m_location[1]) / ublk_size();
    indices[2] = (ublk_location[2] - this->m_location[2]) / ublk_size();
  }

  __HOST__DEVICE__ VOID get_indices(tSCALAR_OR_ARRAY<STP_COORD, 1> (&ublk_location)[3], uINT16 indices[3]) {
    get_indices(cast_as_regular_array(ublk_location), indices);
  }
  
  __HOST__DEVICE__ TAGGED_UBLK_TYPE *tagged_ublk(uINT16 indices[3]) {
    return get_tagged_ublk_ptr(indices[0], indices[1], indices[2]);
  }

  __HOST__DEVICE__ TAGGED_UBLK_TYPE stagged_ublk(uINT16 indices[3]) const {
    return get_tagged_ublk(indices[0], indices[1], indices[2]);
  }
  
  __HOST__DEVICE__ TAGGED_UBLK_TYPE *tagged_ublk(STP_COORD x, STP_COORD y, STP_COORD z) {
    asINT32 x_index = (x - this->m_location[0]) / ublk_size();
    asINT32 y_index = (y - this->m_location[1]) / ublk_size();
    asINT32 z_index = (z - this->m_location[2]) / ublk_size();
    return get_tagged_ublk_ptr(x_index, y_index, z_index);
  }
  
  template<typename T>  
  __HOST__DEVICE__ asINT32 linear_index(T offset[3]) const {
    return linear_index(offset[0], offset[1], offset[2]);
  }

  template<typename T>  
  __HOST__DEVICE__ asINT32 linear_index(T x, T y, T z) const {
    return (m_size[2] * (m_size[1] * x + y) + z);
  }    

  template<typename T>
  __HOST__DEVICE__ TAGGED_UBLK_TYPE get_tagged_ublk(T indices[3]) const {
    return (m_ublks[linear_index(indices)]);
  }  

  template<typename T>
  __HOST__DEVICE__ TAGGED_UBLK_TYPE get_tagged_ublk(T x, T y, T z) const {
    return (m_ublks[linear_index(x, y, z)]);
  }

  template<typename T>
  __HOST__DEVICE__ TAGGED_UBLK_TYPE* get_tagged_ublk_ptr(T x, T y, T z) {
    return (&m_ublks[linear_index(x, y, z)]);
  }  
  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
			              size_t n_bytes,
			              const tUBLK_BOX<UBLK_TYPE_TAG>* h_box);

  VOID get_location(uINT16 indices[3], STP_COORD ublk_location[3]) const;
  void print_contents(std::ostream& os) const;
  
 };

using sUBLK_BOX = tUBLK_BOX<UBLK_SDFLOAT_TYPE_TAG>;
using UBLK_BOX = sUBLK_BOX*;

using sMBLK_BOX = tUBLK_BOX<MBLK_SDFLOAT_TYPE_TAG>;
using MBLK_BOX = sMBLK_BOX*;

//Specializations so that implementations are declared only for the CPU version
template<> VOID sUBLK_BOX::divide_ublk_box();
template<> VOID sUBLK_BOX::find_min_empty_planes(asINT32 &axis, asINT32 &location);
template<> VOID sUBLK_BOX::find_largest_occupancy_splitting_plane(asINT32 &axis, asINT32 &location);
template<> VOID sUBLK_BOX::split_ublk_box(asINT32 axis, asINT32 split_index);
template<> VOID sUBLK_BOX::remove_empty_layers(asINT32 split_axis, uINT16 start_index[3], uINT16 end_index[3]);


//template<> VOID sUBLK_BOX::allocate_ublks();
template<> VOID sMBLK_BOX::copy_to_device(GPU::sDEV_PTR& d_ptr,
					     size_t n_bytes,
					     const sMBLK_BOX* h_box);

template<typename UBLK_TYPE_TAG>
struct tUBLK_BOX_ACCESS {

  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using TAGGED_UBLK_TYPE = tTAGGED_UBLK<UBLK_TYPE_TAG>;
  using BOX_TYPE = tUBLK_BOX<UBLK_TYPE_TAG>*;

  uINT32 m_box_1D_index;
  uINT16 m_box_indices[3];
  BOX_TYPE m_ublk_box;  
  
  __HOST__DEVICE__ tUBLK_BOX_ACCESS(BOX_TYPE ublk_box,
				    uINT32 box_1D_index,
				    const uINT16 (&box_indices) [3]):
    m_box_1D_index(box_1D_index),
    m_box_indices{box_indices[0], box_indices[1], box_indices[2]},
    m_ublk_box(ublk_box) {
  }

  tUBLK_BOX_ACCESS() = default;

  // Non-copyable and non-movable by intention
  // To avoid triggering these, use
  // auto& b = ublk->box_access()
  // const auto& b = ublk->box_access()
  // auto b = &ublk->box_access()
  // const auto b = &ublk->box_access()  
  tUBLK_BOX_ACCESS(const tUBLK_BOX_ACCESS& other) = delete;
  tUBLK_BOX_ACCESS& operator=(const tUBLK_BOX_ACCESS& other) = delete;
  tUBLK_BOX_ACCESS(tUBLK_BOX_ACCESS&& other) = delete;
  tUBLK_BOX_ACCESS& operator=(tUBLK_BOX_ACCESS&& other) = delete;  
  
  __HOST__DEVICE__ tUBLK_BOX_ACCESS& get_ublk_box_partition(asINT32 unused = -1) {
    return *this;
  }

  __HOST__DEVICE__ TAGGED_UBLK_TYPE *ublk(sINT8 dirs[3], uINT16 a_index, uINT16 n1_index, uINT16 n2_index)  const {
    uINT16 indices[3] = {0};
    indices[dirs[0]] = a_index;
    indices[dirs[1]] = n1_index;
    indices[dirs[2]] = n2_index;
    return this->m_ublk_box->get_tagged_ublk_ptr(indices);
  }
  

  __HOST__DEVICE__ TAGGED_UBLK_TYPE *tagged_ublk() {
    return(&this->m_ublk_box->m_ublks[m_box_1D_index]);
  }

  __HOST__DEVICE__ INLINE TAGGED_UBLK_TYPE stagged_ublk() const {
    return(this->m_ublk_box->m_ublks[m_box_1D_index]);
  }

  __HOST__DEVICE__ INLINE TAGGED_UBLK_TYPE forward_neighbor(asINT32 axis,
							    bool include_coarse_ublk = false) const {
    uINT32 index = this->m_box_1D_index + this->m_ublk_box->m_offsets[axis];
    auto t = this->m_ublk_box->m_ublks[index];    
    if(include_coarse_ublk || !t.is_ublk_coarse()){
      return t;
    }else{
      return NULL;
    }
  }

  __HOST__DEVICE__ INLINE TAGGED_UBLK_TYPE backward_neighbor(asINT32 axis,
							     bool include_coarse_ublk = false) const {
    uINT32 index = this->m_box_1D_index - this->m_ublk_box->m_offsets[axis];
    auto t = this->m_ublk_box->m_ublks[index];
    if(include_coarse_ublk || !t.is_ublk_coarse()){
      return t;
    }else{
      return NULL;
    }
  }

  template<typename T>
  __HOST__DEVICE__ INLINE TAGGED_UBLK_TYPE neighbor_ublk(const T offset[3],
							 bool include_coarse_ublk = false) const {
    //Tri-diagonal ublk is added to the box, and should only be used by particle modeling.
    //Thus, only particle modeling should call this function with include_coarse_ublk = true. 
    asINT32 x_index = this->m_box_indices[0] + offset[0];
    asINT32 y_index = this->m_box_indices[1] + offset[1];
    asINT32 z_index = this->m_box_indices[2] + offset[2];
    if ((x_index < 0) || (y_index < 0) || (z_index < 0))
      return NULL;
    else if ((x_index >= this->m_ublk_box->m_size[0]) ||
	     (y_index >= this->m_ublk_box->m_size[1]) ||
	     (z_index >= this->m_ublk_box->m_size[2]))
      return NULL;
    else {
      auto t = m_ublk_box->get_tagged_ublk(x_index, y_index, z_index);
      if (include_coarse_ublk || !t.is_ublk_coarse()){
        return t;
      } else{
        return NULL;
      }
    }
  }

  __HOST__DEVICE__ INLINE TAGGED_UBLK_TYPE * r_neighbor_ublk(const sINT16 offset[3]) {
    asINT32 x_index = this->m_box_indices[0] + offset[0];
    asINT32 y_index = this->m_box_indices[1] + offset[1];
    asINT32 z_index = this->m_box_indices[2] + offset[2];
    if ((x_index < 0) || (y_index < 0) || (z_index < 0))
      return NULL;
    else if ((x_index >= this->m_ublk_box->m_size[0]) ||
	     (y_index >= this->m_ublk_box->m_size[1]) ||
	     (z_index >= this->m_ublk_box->m_size[2]))
      return NULL;
    else {
      return m_ublk_box->get_tagged_ublk_ptr(x_index, y_index, z_index);
    }
  }

  INLINE TAGGED_UBLK_TYPE neighbor_ublk(const std::tuple<asINT32,asINT32,asINT32>& offset_tuple)  const  {
    sINT16 offset[3];
    offset[0] = std::get<0>(offset_tuple);
    offset[1] = std::get<1>(offset_tuple);
    offset[2] = std::get<2>(offset_tuple);

    return neighbor_ublk(offset);
  }

  __HOST__DEVICE__ TAGGED_UBLK_TYPE *checked_neighbor_ublk(TAGGED_UBLK_TYPE *tagged_ublk, sINT16 offset[3]) {
    asINT32 offset_index = m_ublk_box->linear_index(offset);
    TAGGED_UBLK * start = &this->m_ublk_box->m_ublks[0];
    TAGGED_UBLK * end  = start + this->m_ublk_box->m_ublks.size();
    TAGGED_UBLK * ret = tagged_ublk + offset_index;
    if ( (ret < start) || (ret >= end) ) {
      return nullptr;
    }
    return (tagged_ublk + offset_index);
  }
};


/*@ class tBOX_ACCESS
 */
template<typename UBLK_TYPE_TAG>
struct tBOX_ACCESS;

template<>
struct tBOX_ACCESS<UBLK_SDFLOAT_TYPE_TAG> : public tUBLK_BOX_ACCESS<UBLK_SDFLOAT_TYPE_TAG> {};

template<typename UBLK_TYPE_TAG>
struct tMEGA_BOX_ACCESS;

template<typename UBLK_TYPE_TAG>
struct tBOX_ACCESS {

  enum {
    N_VOXELS = UBLK_TYPE_TAG::N_VOXELS,
    N_UBLKS = UBLK_TYPE_TAG::N_UBLKS
  };

  tUBLK_BOX_ACCESS<UBLK_TYPE_TAG> m_ublk_box_access[N_UBLKS];
  
  tBOX_ACCESS() = default;

  // Non-copyable and non-movable by intention
  // To avoid triggering these, use
  // auto& b = ublk->box_access()
  // const auto& b = ublk->box_access()
  // auto b = &ublk->box_access()
  // const auto b = &ublk->box_access()  
  tBOX_ACCESS(const tBOX_ACCESS& other) = delete;
  tBOX_ACCESS(tBOX_ACCESS&& other) = delete;
  tBOX_ACCESS& operator=(tBOX_ACCESS&& other) = delete;
  tBOX_ACCESS& operator=(const tBOX_ACCESS& other) = delete;

  __HOST__DEVICE__
  tUBLK_BOX_ACCESS<UBLK_TYPE_TAG>& get_ublk_box_partition(asINT32 child_ublk_index)  {
    cassert(m_ublk_box_access[child_ublk_index].m_ublk_box);
    return m_ublk_box_access[child_ublk_index];
  }


  __HOST__DEVICE__ tMEGA_BOX_ACCESS<UBLK_TYPE_TAG>* reinterpret_at_true_resolution() {
    return reinterpret_cast<tMEGA_BOX_ACCESS<UBLK_TYPE_TAG>*>(this);
  }

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
				      size_t n_bytes,
				      const tBOX_ACCESS* h_box);  
};

template<typename UBLK_TYPE_TAG>
struct tMEGA_BOX_ACCESS : public tBOX_ACCESS<UBLK_TYPE_TAG> {

  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using TAGGED_UBLK_TYPE = tTAGGED_UBLK<UBLK_TYPE_TAG>;
  
  tMEGA_BOX_ACCESS() = delete;
  
  __HOST__DEVICE__ TAGGED_UBLK_TYPE forward_neighbor(asINT32 axis) const {
    const auto& box_access = this->m_ublk_box_access[0];
    auto ublk_box = box_access.m_ublk_box;
    STP_COORD index = box_access.m_box_1D_index + (ublk_box->m_offsets[axis] * sUBLK_TYPE::N_UBLKS_1D);
    //Correct if we're out of bounds
    index = std::min(index, ublk_box->m_n_ublks);
    return(ublk_box->m_ublks[index]);
    
  }

  __HOST__DEVICE__ TAGGED_UBLK_TYPE backward_neighbor(asINT32 axis) const {    
    const auto& box_access = this->m_ublk_box_access[0];
    auto ublk_box = box_access.m_ublk_box;
    STP_COORD index = box_access.m_box_1D_index - (ublk_box->m_offsets[axis] * sUBLK_TYPE::N_UBLKS_1D);
    //Correct if we're out of bounds
    index = std::max(0, index);
    return(ublk_box->m_ublks[index]);
  }

  __HOST__DEVICE__ TAGGED_UBLK_TYPE neighbor_ublk(const sINT16 offset[3]) const {
    //Tri-diagonal ublk is added to the box, and should only be used by particle modeling.
    //Thus, only particle modeling should call this function with include_coarse_ublk = true.
    const auto& box_access = this->m_ublk_box_access[0];
    auto ublk_box = box_access.m_ublk_box;
    asINT32 x_index = box_access.m_box_indices[0] + (offset[0] * sUBLK_TYPE::N_UBLKS_1D);
    asINT32 y_index = box_access.m_box_indices[1] + (offset[1] * sUBLK_TYPE::N_UBLKS_1D);
    asINT32 z_index = box_access.m_box_indices[2] + (offset[2] * sUBLK_TYPE::N_UBLKS_1D);

    //Non divergent correction in case we exceed bounds
    x_index -= ((x_index < 0) || (x_index >= ublk_box->m_size[0])) * offset[0];
    y_index -= ((y_index < 0) || (y_index >= ublk_box->m_size[1])) * offset[1];
    z_index -= ((z_index < 0) || (z_index >= ublk_box->m_size[2])) * offset[2];    
    
    asINT32 index = ublk_box->m_size[2] *
      (ublk_box->m_size[1] * x_index + y_index) + z_index;
    
    dassert(index < ublk_box->m_n_ublks);
    return(ublk_box->m_ublks[index]);
  }    
};

using sBOX_ACCESS = tUBLK_BOX_ACCESS<UBLK_SDFLOAT_TYPE_TAG>;

#if GPU_COMPILER
using sBOX_ACCESS_64 = tBOX_ACCESS<MBLK_SDFLOAT_TYPE_TAG>;
#endif

#endif //UBLK_BOX_H
