/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Functional Groups
 *
 * James Hoch, Exa Corporation 
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "lattice.h"
#include "group.h"

/*--------------------------------------------------------------------------*
 * group_cmp_by_scale
 *
 * Intended for use as the compare function passed to the sort_array routine
 * when the intent is to sort groups by scale (finest first). It is declared 
 * with "C" linkage because the SORT component is created with the C compiler.
 *--------------------------------------------------------------------------*/
extern "C" int group_cmp_by_scale(const VOID *arg1, const VOID *arg2, const VOID *free) 
{
  GROUP group1 = (GROUP)arg1;
  GROUP group2 = (GROUP)arg2;
    
  if (sim_is_scale_finer(group1->m_scale, group2->m_scale))
    return -1;
  else if (sim_is_scale_coarser(group1->m_scale, group2->m_scale))
    return 1;
  else
    return 0;
}
