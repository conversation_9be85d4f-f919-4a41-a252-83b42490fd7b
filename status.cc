/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Sim status
 *--------------------------------------------------------------------------*/

#include "status.h"
#include "thread_run.h"
#include "timescale.h"
#include "comm_groups.h"

#define DEBUG_STATUS_TREE 0

namespace StatusTree {

static sSIM_SOLVER_STATUS g_last_status_sent(SIM_STATUS_INIT, REALM_STATUS_INIT);
static sSIM_SOLVER_STATUS g_status_child_status[MAX_SIM_STATUS_TREE_DEGREE];

static STP_PROC g_status_n_child_sps = -1;     // how many SPs send me status messages
static STP_PROC g_status_parent_rank = -1;     // SP or CP that I send status to
static STP_PROC g_status_source_index = -1;    // index amongst the SPs sending status to g_status_parent_rank

VOID process_status_msg(const MPI_Status &probed_mpi_status)
{
  sSP_SIM_STATUS child_sim_status;
  RECV_EXA_SIM_MSG<sSP_SIM_STATUS, 1> sim_status(eMPI_SP_STATUS_TAG, probed_mpi_status.MPI_SOURCE);
  g_exa_sp_cp_comm.recv(sim_status.mpi_msg);
  child_sim_status = *sim_status.return_buffer();
  g_status_child_status[child_sim_status.source_index] = child_sim_status.status;
}

VOID receive_status_updates() 
{
   MPI_Status status;
   int flag;
   MPI_Iprobe(MPI_ANY_SOURCE, eMPI_SP_STATUS_TAG, eMPI_sp_cp_comm, &flag, &status);
   while(flag) {
     process_status_msg(status);
     MPI_Iprobe(MPI_ANY_SOURCE, eMPI_SP_STATUS_TAG, eMPI_sp_cp_comm, &flag, &status);
   }
}

// If the simulation is running, status should equal the current timestep.
// If the simulation is finished, then status should equal SIM_STATUS_DONE,
// which is greater than BASETIME_LAST and BASETIME_MAX.
VOID maybe_send_status(sSIM_SOLVER_STATUS status)
{
  if ( g_last_status_sent.status == SIM_STATUS_DONE ) return;

  if ( g_status_n_child_sps > 0 ) {
    SIM_STATUS min_child_status = std::min_element(g_status_child_status,
                                              g_status_child_status + g_status_n_child_sps,
                                              [](const sSIM_SOLVER_STATUS &lhs, const sSIM_SOLVER_STATUS &rhs) {return lhs.status < rhs.status;})->status;
    REALM_STATUS min_child_flow_status = std::min_element(g_status_child_status,
                                              g_status_child_status + g_status_n_child_sps,
                                              [](const sSIM_SOLVER_STATUS &lhs, const sSIM_SOLVER_STATUS &rhs) {return lhs.flow_status < rhs.flow_status;})->flow_status;
    REALM_STATUS min_child_cond_status = std::min_element(g_status_child_status,
                                              g_status_child_status + g_status_n_child_sps,
                                              [](const sSIM_SOLVER_STATUS &lhs, const sSIM_SOLVER_STATUS &rhs) {return lhs.cond_status < rhs.cond_status;})->cond_status;

    status.status = std::min(min_child_status, status.status);
    status.flow_status = std::min(min_child_flow_status, status.flow_status);
    status.cond_status = std::min(min_child_cond_status, status.cond_status);
  }

#if ENABLE_CONSISTENCY_CHECKS
  if (status.status < g_last_status_sent.status || 
      status.flow_status < g_last_status_sent.flow_status || 
      status.cond_status < g_last_status_sent.cond_status) {
    msg_warn("Timestep decreased %ld -> %ld, %d -> %d, %d -> %d", 
        g_last_status_sent.status, status.status,
        g_last_status_sent.flow_status, status.flow_status,
        g_last_status_sent.cond_status, status.cond_status);
  }
#endif

  if (status.status > g_last_status_sent.status || 
      status.flow_status > g_last_status_sent.flow_status || 
      status.cond_status > g_last_status_sent.cond_status) {
    static MPI_Request send_request = MPI_REQUEST_NULL;
    static sSP_SIM_STATUS status_msg;

    int flag;
    MPI_Status mpi_status;
    MPI_Test(&send_request, &flag, &mpi_status);
    if (!flag) {
      return;
    }

    // Flush all simerrs to the CP before sending a status msg because the CP uses the arrival
    // of a t=X status msg to indicate that all simerrs for timestep X-1 have already been sent. 
  
    while (simerr_mpi_process()) { }
    
    status_msg.status = status;
    status_msg.source_index = g_status_source_index;
    MPI_Isend(&status_msg, sizeof(status_msg), MPI_BYTE, g_status_parent_rank, eMPI_SP_STATUS_TAG, eMPI_sp_cp_comm, &send_request);
    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS,"status_msg",status_msg.status.status);
    g_last_status_sent = status;
  }
}


VOID initialize_status_tree()
{
  sCP_TO_TREE_NODE_INIT_MSG msg;
  RECV_EXA_SIM_MSG<sCP_TO_TREE_NODE_INIT_MSG, 1> recv_msg(eMPI_SP_STATUS_TAG, eMPI_sp_cp_rank());
  g_exa_sp_cp_comm.recv(recv_msg.mpi_msg);
  msg = *recv_msg.return_buffer();
  g_status_parent_rank  = msg.parent_rank;
  g_status_n_child_sps  = msg.n_children;
  g_status_source_index = msg.source_index;


  ccDOTIMES(i, g_status_n_child_sps)
    g_status_child_status[i].init(SIM_STATUS_INIT, REALM_STATUS_INIT);

#if DEBUG_STATUS_TREE
  msg_print("# children = %d, source index = %d, parent rank = %d",
            g_status_n_child_sps, g_status_source_index, g_status_parent_rank);
#endif
}

} // end namespace StatusTree

