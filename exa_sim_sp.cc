/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * This is the main SP program that runs on the ARM
 *
 * Jim Salem, Exa Corporation 
 * Created Thu May  5 1994
 *--------------------------------------------------------------------------*/

#include <execinfo.h>
#include <sys/stat.h>

#include <math.h>
#include "common_sp.h"
#include "sim.h"

#include "sp_timers.h"
#include "random.h"
#include "sim.h"
#include "timescale.h"
#include "shob.h"
#include "simerr.h"
#include "ckpt.h"
#include "eqns.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"
#include "slrf.h"
#include "mlrf.h"
#include "isurfel_dyn_sp.h"
#include "mirror.h"
#include "surfel_advect_sp.h"
#include "vr.h"
#include "phys_type_map.h"
#include "status.h"
#include "strand_mgr.h"
#include "thread_run.h"
#include "dgf_reader_sp.h"
#include "shob_groups.h"
#include "advect.h"
#include "box_advect.h"
#include "comm_compression.h"
#include "scalar_data.h"
#include "bsurfel_dyn_sp.h"
#include "parse_shob_descs.h"
#include "gpu_host_include.h"
#include "ublk_box_tree.h"
#include "surfel_stencils.h"
#ifndef _EXA_HPMPI
#include "utilities.h"
#endif
/**** Turn on REPORT_MALLOC_INFO to get a detailed report of each allocated
***** data structure immediately after initialization.  For this to work,
***** you must also include the -malloc_check option on the command line.
*/

#if     defined(__sgi) || defined(__sun__)
#include <unistd.h>
#endif

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_sim.h"
#include "trajectory_meas.h"
#include "particle_sim_info.h"
//#endif

#include "conduction_data.h"
#include "implicit_shell_solver.h"
#include "implicit_solid_solver.h"
#include <ios>
#include <iostream>
#include <fstream>
#include <string>
void process_mem_usage(double& vm_usage, double& resident_set)
{
   using std::ios_base;
   using std::ifstream;
   using std::string;

   vm_usage     = 0.0;
   resident_set = 0.0;

   // 'file' stat seems to give the most reliable results
   //
   ifstream stat_stream("/proc/self/stat",ios_base::in);

   // dummy vars for leading entries in stat that we don't care about
   //
   string pid, comm, state, ppid, pgrp, session, tty_nr;
   string tpgid, flags, minflt, cminflt, majflt, cmajflt;
   string utime, stime, cutime, cstime, priority, nice;
   string O, itrealvalue, starttime;

   // the two fields we want
   //
   unsigned long vsize;
   long rss;

   stat_stream >> pid >> comm >> state >> ppid >> pgrp >> session >> tty_nr
               >> tpgid >> flags >> minflt >> cminflt >> majflt >> cmajflt
               >> utime >> stime >> cutime >> cstime >> priority >> nice
               >> O >> itrealvalue >> starttime >> vsize >> rss; // don't care about the rest

   stat_stream.close();

   long page_size = sysconf(_SC_PAGE_SIZE); // in case x86-64 is configured to use 2MB pages
   vm_usage     = vsize;
   resident_set = rss * page_size;
}


/*--------------------------------------------------------------------------*
 * Exit functions
 *--------------------------------------------------------------------------*/ 

// This allows us to return a special error code on
// Alpha MPI runs for improved termination behavior

asINT32 exit_failure_code(VOID)
{
#if defined(__alpha)
  return(123);     // GLOBAL ERROR exit code; see prun man page
#else
  return(EXIT_FAILURE);
#endif
}

// This overrides the definition of err_exit in MSGERR for improved termination
// behavior on HP/Quadrics MPI. We rely on the link order in master.mak to make sure
// we get this version.

VOID err_exit(VOID)
{
  exit(exit_failure_code());
}

static VOID send_ref_frame_info()
{       
  // SP 0 has this job
  if (my_proc_id == 0) {
    // Check for sim.grf_physics_desc rather than checking for sim.grf.is_defined
    // being TRUE because if all parameters of sim.grf_physics_desc are 0, 
    // sim.grf.is_defined is set to FALSE.
    if (sim.grf_physics_desc != NULL) {
 
      sGRF_SP_TO_CP_INIT_MSG g;
      vcopy(g.return_buffer()->ref_point, sim.grf.ref_point);

      // If the user defined the ref point motion via an acceleration, we
      // assume that the ref point velocity is not constant.
      g.return_buffer()->is_ref_point_vel_constant = ( !sim.grf_physics_desc->is_motion_via_accel
						       && !sim.grf_physics_desc->is_ref_vel_time_varying );
      vcopy(g.return_buffer()->ref_point_vel, sim.grf.ref_pt_vel);
      
      if (sim.grf.is_rotation) {
        g.return_buffer()->is_angular_vel_constant = !sim.grf_physics_desc->is_angular_vel_time_varying;
        vcopy(g.return_buffer()->angular_vel, sim.grf.angular_vel);
        // The initial GRF rotation angle at t=0 is always 0, but sim.grf.angle_rotated
        // may not be zero after a checkpoint restore.
        g.return_buffer()->angle_rotated = 0; // sim.grf.angle_rotated;
        vcopy(g.return_buffer()->point, sim.grf.last_pt_on_axis);
      } else {
        g.return_buffer()->is_angular_vel_constant = TRUE;
      }
      g_exa_sp_cp_comm.send(g.send_msg);
    }

    if (sim.n_lrf_physics_descs > 0) {
      LRF_PHYSICS_DESCRIPTOR lrf = sim.lrf_physics_descs;
      ccDOTIMES(i, sim.n_lrf_physics_descs) {
        sLRF_SP_TO_CP_INIT_MSG l;
        l.return_buffer()->ref_frame_index = i;
        vcopy(l.return_buffer()->axis, lrf->axis);
        vcopy(l.return_buffer()->point, lrf->point);
        l.return_buffer()->is_angular_vel_constant = !(lrf->is_angular_vel_time_varying || lrf->is_rotational_dynamics_on);
        l.return_buffer()->angular_vel_mag = lrf->omega;
        // The initial LRF rotation may not be zero if the simulation is seeded
        l.return_buffer()->angle_rotated = lrf->initial_angle_rotated;
        l.return_buffer()->n_revolutions = lrf->initial_n_revolutions;
        g_exa_sp_cp_comm.send(l.send_msg);
        lrf++;
      }
    }
  }
}

template<typename PHYSICS_DESCRIPTOR_TYPE>
static VOID set_mbc_angular_velocity_data(sMBC_SP_TO_CP_INIT_MSG& mbc_info,
                                          PHYSICS_DESCRIPTOR_TYPE pd) {
  
  static_assert(std::is_same<PHYSICS_DESCRIPTOR_TYPE, ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR>::value ||
#if BUILD_5G_LATTICE                
                std::is_same<PHYSICS_DESCRIPTOR_TYPE, ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR>::value,
#else
                std::is_same<PHYSICS_DESCRIPTOR_TYPE, ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR>::value,
#endif                
                "This function is only applicable to ANGULAR SLIP or NOSLIP physics descriptors");

  asINT32 ref_frame_index = pd->parameters()->ref_frame.value;
  mbc_info.return_buffer()->ref_frame_index = ref_frame_index;

  //For rotating walls that have their parameters set indirectly with a reference frame, we use
  //the reference frame to fill MBC data - see PR 50491
  //
  //The check (sim.n_lrf_physics_descs > 0) is to address PR-53067, some CDI files such as those generated for
  //turbulence calibration have the LRF chunk missing. In which case, we use this hack to skip reading from
  //the reference frame and use the default values from the physics descriptors chunk data
  if ((sim.n_lrf_physics_descs > 0) && pd->parameters()->angular_spec.with_respect_to_ref_frame.value) {
 
    if (ref_frame_index >=0 && ref_frame_index < sim.n_lrf_physics_descs) {
      //based on powercase options, if rotation is defined with respect to reference frame
      //it appears that only LRFs are permitted
      //assert(ref_frame_index >=0 && ref_frame_index < sim.n_lrf_physics_descs);
      auto lrf = &sim.lrf_physics_descs[ref_frame_index];    
      ccDOTIMES(i, 3) {
        mbc_info.return_buffer()->point[i] = lrf->point[i];
        mbc_info.return_buffer()->axis[i] = lrf->axis[i];
      }
      // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
      mbc_info.return_buffer()->is_vel_space_varying = false;
      mbc_info.return_buffer()->is_vel_constant = !lrf->is_angular_vel_time_varying;
      mbc_info.return_buffer()->angular_vel_mag = vlength(lrf->angular_vel);   
    } else if (ref_frame_index == -1) {
      //in case the LRF chosen to define the rotation of the boundary is disabled, CASE chooses to replace 
      //the LRF with the GRF (PR-55145). in this case we take the parameters from the PD.
      ccDOTIMES(i, 3) {
        mbc_info.return_buffer()->point[i] = pd->parameters()->angular_spec.point[i].value;
        mbc_info.return_buffer()->axis[i] = pd->parameters()->angular_spec.unit_vec[i].value;
      }    
      // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
      mbc_info.return_buffer()->is_vel_space_varying = pd->parameters()->angular_spec.angular_velocity.is_space_varying;
      mbc_info.return_buffer()->is_vel_constant = (!pd->parameters()->angular_spec.angular_velocity.is_time_varying
                                                   || mbc_info.return_buffer()->is_vel_space_varying);
      mbc_info.return_buffer()->angular_vel_mag = pd->parameters()->angular_spec.angular_velocity.value;
    }    
  } else {
    ccDOTIMES(i, 3) {
      mbc_info.return_buffer()->point[i] = pd->parameters()->angular_spec.point[i].value;
      mbc_info.return_buffer()->axis[i] = pd->parameters()->angular_spec.unit_vec[i].value;
    }    
    // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
    mbc_info.return_buffer()->is_vel_space_varying = pd->parameters()->angular_spec.angular_velocity.is_space_varying;
    mbc_info.return_buffer()->is_vel_constant = (!pd->parameters()->angular_spec.angular_velocity.is_time_varying
                                                 || mbc_info.return_buffer()->is_vel_space_varying);
    mbc_info.return_buffer()->angular_vel_mag = pd->parameters()->angular_spec.angular_velocity.value;
  }
}

// This routine should be in sync with find_time_varying_moving_bcs() in meas.cc.
static VOID send_mbc_info()
{       
  // The last SP has this job - do not use the same MPI tag as send_ref_frame_info
  if (my_proc_id == total_sps - 1) {
    asINT32 tag = eMPI_MBC_INFO_TAG;

    MPI_Datatype eMPI_MBC_SP_TO_CP_INIT_MSG;
    
    sINT32 *surface_physics_desc_mbc_indices = xnew sINT32[sim.n_flow_surface_physics_descs];
    ccDOTIMES(i, sim.n_flow_surface_physics_descs)
      surface_physics_desc_mbc_indices[i] = SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX;
    
    cBOOLEAN is_user_defined_ref_frame = sim.n_lrf_physics_descs > 0 || sim.grf.is_defined;

    asINT32 n_ref_frames;
    sINT32 *fixed_wall_mbc_index;  // per ref frame
    if (is_user_defined_ref_frame) {
      n_ref_frames = sim.n_lrf_physics_descs - SRI_MIN_REF_FRAME_INDEX;
      fixed_wall_mbc_index = xnew sINT32[n_ref_frames];
      ccDOTIMES(i, n_ref_frames)
        fixed_wall_mbc_index[i] = SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX;
    } else {
      n_ref_frames = 0;
      fixed_wall_mbc_index = NULL;
    }

    BOOLEAN is_slip = FALSE;
    SEND_EXA_SIM_MSG<sINT32, 1> n_mbcs(tag);
    *n_mbcs.return_buffer() = 0;
    //    sINT32 n_mbcs = 0;
    // Process the rotating BCs before the sliding BCs because that is the order used by the SPs
    // for meas window related sends.
    PHYSICS_DESCRIPTOR surface_physics_desc = sim.flow_surface_physics_descs;
    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
      case SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
        is_slip = TRUE;
        // yes, fall into next case
      case NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        if (n_ref_frames == 0)
          break;
        asINT32 zero_base_ref_frame_index;
        if (is_slip) {
          SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
          zero_base_ref_frame_index = pd->parameters()->ref_frame.value - SRI_MIN_REF_FRAME_INDEX;
          is_slip = FALSE;
        } else {
#if BUILD_5G_LATTICE
	  NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
          NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
          zero_base_ref_frame_index = pd->parameters()->ref_frame.value - SRI_MIN_REF_FRAME_INDEX;
        }
        if (fixed_wall_mbc_index[zero_base_ref_frame_index] == SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX) {
          fixed_wall_mbc_index[zero_base_ref_frame_index] = *n_mbcs.return_buffer();
          (*n_mbcs.return_buffer())++;
        }
        surface_physics_desc_mbc_indices[i] = fixed_wall_mbc_index[zero_base_ref_frame_index];
        break;  
      }     
      case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: 
      case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
        surface_physics_desc_mbc_indices[i] = *n_mbcs.return_buffer();
        (*n_mbcs.return_buffer())++;
        break;        
      default:
        break;
      }
      surface_physics_desc++;
    }

    surface_physics_desc = sim.flow_surface_physics_descs;
    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
      case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: 
      case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
        surface_physics_desc_mbc_indices[i] = *n_mbcs.return_buffer();
        (*n_mbcs.return_buffer())++;
        break;        
      default:
        break;
      }
      surface_physics_desc++;
    }
    g_exa_sp_cp_comm.send(n_mbcs.mpi_msg);

    // g_num_faces may include extra discretizer generated faces - use sim.control_record.num_cdi_faces
    asINT32 n_sri_faces = sim.control_record.num_cdi_faces;
    sINT32 *face_mbc_indices = xnew sINT32[n_sri_faces];
    ccDOTIMES(f, n_sri_faces) {
      BOOLEAN is_back_side = is_back_side_only_from_face_index(f);
      sINT32 phys_desc_index = flow_phys_desc_index_from_face_index(f, is_back_side);
      if (phys_desc_index >= 0)
        face_mbc_indices[f] = surface_physics_desc_mbc_indices[phys_desc_index];
      else
        face_mbc_indices[f] = SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX;
    }
    SEND_EXA_SIM_MSG<sINT32> fmbc_indices(tag, n_sri_faces, face_mbc_indices);
    g_exa_sp_cp_comm.send(fmbc_indices.mpi_msg);
    delete[] face_mbc_indices;
    delete[] surface_physics_desc_mbc_indices;
    
    // Send the rotating BCs before the sliding BCs because that is the order used by the SPs
    // for meas window related sends. Thus the MBCs may not be in the same order as in
    // sim.flow_surface_physics_descs.
    sMBC_SP_TO_CP_INIT_MSG mbc_info;
    surface_physics_desc = sim.flow_surface_physics_descs;
    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
      case SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE:
        is_slip = TRUE;
        // yes, fall into next case
      case NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        if (n_ref_frames == 0)
          break;
        asINT32 ref_frame_index;
        if (is_slip) {
          SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
          ref_frame_index = pd->parameters()->ref_frame.value;
          is_slip = FALSE;
        } else {
#if BUILD_5G_LATTICE
	  NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
          NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
          ref_frame_index = pd->parameters()->ref_frame.value;
        }            
        asINT32 zero_base_ref_frame_index = ref_frame_index - SRI_MIN_REF_FRAME_INDEX;
        if (fixed_wall_mbc_index[zero_base_ref_frame_index] == SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX)
          break; // Share MBC across all "Fixed in XXXX" surface physics descs
        fixed_wall_mbc_index[zero_base_ref_frame_index] = SRI_NO_MOVING_BOUNDARY_CONDITION_INDEX;
          
        mbc_info.return_buffer()->type = SRI_MBC_FIXED;
        char name[4096]={0};
        sprintf(name, "Fixed in %s", 
                ref_frame_index == SRI_GLOBAL_REF_FRAME_INDEX 
                ? "Global Body-Fixed" 
                : (ref_frame_index == SRI_GROUND_REF_FRAME_INDEX 
                   ? "Ground-Fixed" 
                   : sim.lrf_physics_descs[ref_frame_index].name));
        mbc_info.return_buffer()->name_length = strlen(name) + 1;
        mbc_info.return_buffer()->ref_frame_index = ref_frame_index;

        mbc_info.return_buffer()->point[0] = mbc_info.return_buffer()->point[1] = mbc_info.return_buffer()->point[2] = 0;
        mbc_info.return_buffer()->axis[0]  = mbc_info.return_buffer()->axis[1]  = mbc_info.return_buffer()->axis[2] = 0;
        // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
        mbc_info.return_buffer()->is_vel_space_varying = FALSE;
        mbc_info.return_buffer()->is_vel_constant = TRUE;
        mbc_info.return_buffer()->angular_vel_mag = 0;
        mbc_info.return_buffer()->linear_vel[0] = mbc_info.return_buffer()->linear_vel[1] = mbc_info.return_buffer()->linear_vel[2] = 0;
        g_exa_sp_cp_comm.send(mbc_info.send_msg);
        SEND_EXA_SIM_MSG<char> send_name(tag, mbc_info.return_buffer()->name_length, name);
        g_exa_sp_cp_comm.send(send_name.mpi_msg);
        break;
      }
      case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
        
        mbc_info.return_buffer()->type = SRI_MBC_ROTATING;
        mbc_info.return_buffer()->name_length = strlen(pd->name) + 1;
        set_mbc_angular_velocity_data(mbc_info, pd);
        mbc_info.return_buffer()->linear_vel[0] = 0;
        mbc_info.return_buffer()->linear_vel[1] = 0;
        mbc_info.return_buffer()->linear_vel[2] = 0;
        g_exa_sp_cp_comm.send(mbc_info.send_msg);
        SEND_EXA_SIM_MSG<char> send_pd_name(tag, mbc_info.return_buffer()->name_length, const_cast<char*>(pd->name));
        g_exa_sp_cp_comm.send(send_pd_name.mpi_msg);
        break;
      }
      case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
	ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
        ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
        
        mbc_info.return_buffer()->type = SRI_MBC_ROTATING;
        mbc_info.return_buffer()->name_length = strlen(pd->name) + 1;
        set_mbc_angular_velocity_data(mbc_info, pd);
        mbc_info.return_buffer()->linear_vel[0] = 0;
        mbc_info.return_buffer()->linear_vel[1] = 0;
        mbc_info.return_buffer()->linear_vel[2] = 0;
        g_exa_sp_cp_comm.send(mbc_info.send_msg);
        SEND_EXA_SIM_MSG<char> send_pd_name(tag, mbc_info.return_buffer()->name_length, const_cast<char*>(pd->name));
        g_exa_sp_cp_comm.send(send_pd_name.mpi_msg);
        break;
      }
      default:
        break;
      }
      surface_physics_desc++;
    }

    surface_physics_desc = sim.flow_surface_physics_descs;
    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
      case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
        
        mbc_info.return_buffer()->type = SRI_MBC_SLIDING;
        mbc_info.return_buffer()->name_length = strlen(pd->name) + 1;
        asINT32 ref_frame_index = pd->parameters()->ref_frame.value;
        mbc_info.return_buffer()->ref_frame_index = ref_frame_index;

        mbc_info.return_buffer()->point[0] = 0;
        mbc_info.return_buffer()->point[1] = 0;
        mbc_info.return_buffer()->point[2] = 0;
        mbc_info.return_buffer()->axis[0] = 0;
        mbc_info.return_buffer()->axis[1] = 0;
        mbc_info.return_buffer()->axis[2] = 0;
        // PowerVIZ does not support space-varying velocity
        mbc_info.return_buffer()->is_vel_space_varying = (pd->parameters()->velocity[0].is_space_varying
							  || pd->parameters()->velocity[1].is_space_varying
							  || pd->parameters()->velocity[2].is_space_varying);
        mbc_info.return_buffer()->is_vel_constant = ((!pd->parameters()->velocity[0].is_time_varying
						      && !pd->parameters()->velocity[1].is_time_varying
						      && !pd->parameters()->velocity[2].is_time_varying)
						     || mbc_info.return_buffer()->is_vel_space_varying);
        mbc_info.return_buffer()->angular_vel_mag = 0;
        mbc_info.return_buffer()->linear_vel[0] = pd->parameters()->velocity[0].value;
        mbc_info.return_buffer()->linear_vel[1] = pd->parameters()->velocity[1].value;
        mbc_info.return_buffer()->linear_vel[2] = pd->parameters()->velocity[2].value;
        g_exa_sp_cp_comm.send(mbc_info.send_msg);
        SEND_EXA_SIM_MSG<char> send_pd_name(tag, mbc_info.return_buffer()->name_length, const_cast<char*>(pd->name));
        g_exa_sp_cp_comm.send(send_pd_name.mpi_msg);
        break;
      }
      case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
	LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
        LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
          
        mbc_info.return_buffer()->type = SRI_MBC_SLIDING;
        mbc_info.return_buffer()->name_length = strlen(pd->name) + 1;
        asINT32 ref_frame_index = pd->parameters()->ref_frame.value;
        mbc_info.return_buffer()->ref_frame_index = ref_frame_index;

        mbc_info.return_buffer()->point[0] = 0;
        mbc_info.return_buffer()->point[1] = 0;
        mbc_info.return_buffer()->point[2] = 0;
        mbc_info.return_buffer()->axis[0] = 0;
        mbc_info.return_buffer()->axis[1] = 0;
        mbc_info.return_buffer()->axis[2] = 0;
        // PowerVIZ does not support space-varying velocity
        mbc_info.return_buffer()->is_vel_space_varying = (pd->parameters()->velocity[0].is_space_varying
							  || pd->parameters()->velocity[1].is_space_varying
							  || pd->parameters()->velocity[2].is_space_varying);
        mbc_info.return_buffer()->is_vel_constant = ((!pd->parameters()->velocity[0].is_time_varying
						      && !pd->parameters()->velocity[1].is_time_varying
						      && !pd->parameters()->velocity[2].is_time_varying)
						     || mbc_info.return_buffer()->is_vel_space_varying);
        mbc_info.return_buffer()->angular_vel_mag = 0;
        mbc_info.return_buffer()->linear_vel[0] = pd->parameters()->velocity[0].value;
        mbc_info.return_buffer()->linear_vel[1] = pd->parameters()->velocity[1].value;
        mbc_info.return_buffer()->linear_vel[2] = pd->parameters()->velocity[2].value;
        g_exa_sp_cp_comm.send(mbc_info.send_msg);
        SEND_EXA_SIM_MSG<char> send_pd_name(tag, mbc_info.return_buffer()->name_length, const_cast<char*>(pd->name));
        g_exa_sp_cp_comm.send(send_pd_name.mpi_msg);
        break;
      }
      default:
        break;
      }
      surface_physics_desc++;
    }
  }
}

static VOID send_movb_info() {       
  // SP 0 has this job
  if (my_proc_id == 0) {
    asINT32 tag = eMPI_MOVB_SP_TO_CP_INIT_TAG; 

    SEND_EXA_SIM_MSG<asINT32> n_movb_physics_descs_msg(tag,1,&sim.n_movb_physics_descs);

   g_exa_sp_cp_comm.send(n_movb_physics_descs_msg.mpi_msg);

    MOVB_PHYSICS_DESCRIPTOR movb = sim.movb_physics_descs;
    ccDOTIMES(i, sim.n_movb_physics_descs) {
      sMOVB_SP_TO_CP_INIT_MSG l;
      ccDOTIMES(j,3) {
        l.axis_origin[j] = movb->parameters()->axis_point[j].value;
        l.axis_direction[j] = movb->parameters()->axis_unit_vec[j].value;
      }

      // TODO: This will need to be updated with wiper support (see cp_cdi_reader.cc:read_movb_chunk_phys_subchunk)
      l.motion_type = SRI_ROTATION_MOTION;
      l.has_constant_velocity = !movb->some_parameter_time_varying;
      l.initial_angle_rotated   = movb->initial_angle_rotated;
      l.angle_rotated   = movb->angle_rotated;
      l.constant_angular_vel_mag = movb->parameters()->angular_vel.value;

      SEND_EXA_SIM_MSG<sMOVB_SP_TO_CP_INIT_MSG> l_msg(eMPI_MOVB_SP_TO_CP_INIT_TAG, 1, &l);
      g_exa_sp_cp_comm.send(l_msg.mpi_msg);
      movb++;
    }
  }
}
      
static VOID exchange_sp_coupling_info(VOID)
{
  // receive from the CP any surface coupling related information, and send
  // the group->surfel id mapping + address for receiving coupling data
  // (for shmem)
  
  if (sim.n_coupling_models <= 0 || sim.is_conduction_sp)
    return;

  sim.coupling_data_buf = NULL;
  sINT32 nmax_coupdata = 0;
  // send surfel_ids to CP for each coupling model
  ccDOTIMES(model_index,sim.n_coupling_models) {
    COUPLING_MODEL coupling_model = sim.coupling_models + model_index;
    SEND_EXA_SIM_MSG<asINT32, 1> nsurfels(eMPI_COUPLING_DATA_SIZE_TAG);
    *nsurfels.return_buffer() = fill_coupling_model_surfel_ids(coupling_model);
    SEND_EXA_SIM_MSG<STP_SURFEL_ID> send_coupling_model_surfel_id(eMPI_COUPLING_SURFEL_IDS_TAG, *nsurfels.return_buffer(), coupling_model->surfel_ids);
    g_exa_sp_cp_comm.send(nsurfels.mpi_msg);
    if (*nsurfels.return_buffer() > 0) {
      g_exa_sp_cp_comm.send(send_coupling_model_surfel_id.mpi_msg);
    }
    nmax_coupdata = MAX(nmax_coupdata,*(nsurfels.return_buffer())*coupling_model->n_vars);
  }

  if (nmax_coupdata > 0) {
    sim.coupling_data_buf = cnew sFLOAT [ nmax_coupdata ];
  }

  free_coupling_model_surfel_ids();

  // now cp and sps should have enough information for exchanging coupling
  // data during periodic coupling updates like for tables.

}

extern "C" void terminate_without_message_handler(int sig)
{
  exit(exit_failure_code());
}

static void terminate_handler_sp(int signal)
{
  msg_print("Terminated at timestep %ld: %s", g_timescale.m_time, jobctl_sim_signal_description(signal));
  fflush(stdout);
  exit(exit_failure_code());
}

#if (defined(__alpha))

extern "C" void fpe_handler(int signal)
{
  static asINT32 n_reported = 0;
  if (n_reported < 16) {
    msg_print("Floating point exception at timestep %d. Continuing.", sim.time);
    n_reported++;
    if (n_reported == 16)
      msg_print("Too many floating point exceptions. Reporting disabled.");
  }
}

#endif

static char g_backtrace_path[64];
extern "C" void segfault_handler(int signal)
{

  if (sim.use_implicit_shell_solver) {
#if !GPU_COMPILER && !BUILD_GPU
    implicit_shell_solver_destroy_solver_objects();
#endif
  }
  if (sim.use_implicit_solid_solver) {
#if !GPU_COMPILER && !BUILD_GPU
#if !BUILD_5G_LATTICE
    implicit_solid_solver_destroy_solver_objects();
#endif
#endif
  }

  //Attempt to log a backtrace.
  void* return_addresses[128];
  int stack_depth = backtrace(return_addresses, 128);
  int backtrace_log_fd = open(g_backtrace_path, O_WRONLY | O_CREAT | O_TRUNC, 0660);
  dprintf(backtrace_log_fd, "A segfault occured at timestep %ld with the following backtrace:\n", g_timescale.m_time);
  backtrace_symbols_fd(return_addresses, stack_depth, backtrace_log_fd);
  dprintf(backtrace_log_fd, "Use \"addr2line -fCe <executable> <address>\" to retrieve function names and line numbers (when possible).\n"); 
  close(backtrace_log_fd);
  msg_print("A segfault was encountered at timestep %ld. The backtrace has been written to %s.", g_timescale.m_time, g_backtrace_path);
  exit(1);
}

static VOID install_signal_handlers(void)
{
  /** Catch various signals which we might get if the simulation
  *** terminates for one reason or another.  We catch them just
  *** to call exit, in case there are any atexit routines
  *** registered.
  */

  jobctl_server_start();	// Ignore SIGUSR1 and SIGUSR2
  jobctl_server_sigset_die_signals(terminate_handler_sp);

#if (defined(__alpha))
  // @@@ This may have some use on future platforms
  /** On Alpha we occasionally see a FPE. We want to report it and
  *** continue.
  */
  jobctl_server_sigset(SIGFPE, fpe_handler);	/* Override one of the die signals */
#endif

#if ENABLE_CONSISTENCY_CHECKS
  snprintf(g_backtrace_path, 64, "SP%d_backtrace.txt", my_proc_id);
  jobctl_server_sigset(SIGSEGV, segfault_handler);
  jobctl_server_sigset(SIGILL, segfault_handler);
#endif

}

/* EXA_SIM_SP_CLEANUP is called on exit */
static VOID exa_sim_sp_cleanup(VOID)
{
  return;
}

/*--------------------------------------------------------------------------*
 * Out of memory error handling.
 *--------------------------------------------------------------------------*/

#define OUT_OF_MEMORY_TEXT						\
  "Error: Memory allocation failure.  This case is too large to be\n"	\
  "       simulated on your computer right now.\n"			\
  "       The following procedures are recommended to make this case fit:\n" \
  "       \n"								\
  "       - Quit or kill other large applications, if any.\n"		\
  "       - Delete large files in EXA_TMPDIR (default is /tmp).\n"	\
  "       - Reduce resolution (use fewer voxels and surfels).\n"	\
  "       - Use coarser facetization (use fewer surfels).\n"		\
  "       - Use smaller or less detailed measurement windows.\n"	\
  "       - Use explicit decomposition (-decompose option to exaqsub).\n" \
  "       - Check and increase, if possible, system and shell memory limits.\n"	\
  "       - Install more memory."

static VOID *simeng_malloc_error_handler(size_t size, cSTRING error_str)
{
  /** Avoid msg_error because it might call malloc. */
  fprintf(stderr, "%s\n", OUT_OF_MEMORY_TEXT);
  fflush(stderr);

  /** Try to save the error_str into a hidden file, but don't worry if it fails. */
  {
    FILE *hidden_file_stream = fopen(".exa_error", "w");
    if (hidden_file_stream != NULL) {
      fprintf(hidden_file_stream, "%s\n", error_str);
      fprint_memory_limits(hidden_file_stream);
      fflush(hidden_file_stream);
      fclose(hidden_file_stream);
    }
  }
  err_exit();
  return NULL;			/* To make the compiler happy, but in fact we never return. */
}

#include "exa_sim_parse.cc"

static VOID initialize_event_queue()
{
  add_initial_checkpoints_to_event_queue();
  add_initial_table_reads_to_event_queue();
  add_initial_couplings_to_event_queue();
  add_initial_rotational_dynamics_to_event_queue();
  add_initial_timers_to_event_queue();
  add_event(new_event(EVENT_ID_EXIT, 0, g_timescale.m_end_time));
}

static VOID send_late_sim_run_info()
{
  if (my_proc_id == 0) {
    //    BOOLEAN is_some_bc_time_varying = FALSE;
    SEND_EXA_SIM_MSG<BOOLEAN, 1> is_some_bc_time_varying(eMPI_LATE_SIM_RUN_INFO_TAG);
    *is_some_bc_time_varying.return_buffer() = FALSE;
    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      if (sim.flow_surface_physics_descs[i].some_parameter_time_varying) {
        *is_some_bc_time_varying.return_buffer() = TRUE;
        break;
      }
    }
    g_exa_sp_cp_comm.send(is_some_bc_time_varying.mpi_msg);


    // send information that will be used for rotational dynamics on the CP
    if (sim.n_rotational_dynamics_descs > 0) {
      //      dFLOAT rotdyn_globals[2] = {1.0 / adv_fraction, sim.user_max_vel};
      SEND_EXA_SIM_MSG<dFLOAT, 2> rotdyn_globals(eMPI_ROTDYN_GLOBALS_TAG);
      rotdyn_globals.return_buffer()[0] =  1.0 / g_adv_fraction;
      rotdyn_globals.return_buffer()[1] =  sim.user_max_vel;
      g_exa_sp_cp_comm.send(rotdyn_globals.mpi_msg);
    }
  }
}

static VOID receive_late_sim_run_info()
{
  RECV_EXA_SIM_MSG<sCP_LATE_SIM_RUN_INFO, 1> recv_sim_run_info(eMPI_LATE_SIM_RUN_INFO_TAG, total_sps);
  g_exa_sp_cp_comm.recv(recv_sim_run_info.mpi_msg);
  sim_run_info = *recv_sim_run_info.return_buffer();
}

static VOID send_early_sim_run_info()
{
  // Stashing the double precision flag at the end
  if (my_proc_id == 0) {
    CHARACTER simeng_version[MAX_VERSION_STRING];
    SEND_EXA_SIM_MSG<CHARACTER, MAX_VERSION_STRING + 1> early_info_from_SP0(eMPI_SIMENG_VERSION_TAG);
    sprintf(simeng_version,"%s", SP_VERSION);
    strncpy(early_info_from_SP0.return_buffer(), simeng_version, MAX_VERSION_STRING);
    early_info_from_SP0.return_buffer()[MAX_VERSION_STRING] = (CHARACTER)(sim.is_double_precision);
    g_exa_sp_cp_comm.send(early_info_from_SP0.mpi_msg);
  }
}

static VOID receive_early_sim_run_info()
{
  RECV_EXA_SIM_MSG<sCP_EARLY_SIM_RUN_INFO, 1> recv_early_sim_run_info(eMPI_CP_SIM_RUN_INFO_TAG, total_sps);
  g_exa_sp_cp_comm.recv(recv_early_sim_run_info.mpi_msg);
  early_sim_run_info = *recv_early_sim_run_info.return_buffer();
  simerr_init(early_sim_run_info.sp_initial_error_count);

  sim.dns_nu_over_t_floor = early_sim_run_info.dns_nu_over_t_floor;
  sim.major_flow_direction = early_sim_run_info.major_flow_direction ;
}

static void initialize_turb_model_parameters()
{
  if (g_use_rng_model) {
    g_C_MU    = 0.085;
    g_sigma_k = 0.719;
    g_sigma_e = 0.719;
    g_C_EPS1  = 1.42;
    g_C_EPS2  = 1.68;
    g_C_ETA0  = 0.228310502;
    g_C_BETA  = 0.012;
    if (g_use_rng_model == 2) {
      g_C_BETA  = 0.00924;
    }
  }
  else {
    g_C_MU    = 0.09;
    g_sigma_k = 1.0;
    g_sigma_e = 1.22;
    g_C_EPS1  = 1.44;
    g_C_EPS2  = 1.92;
  }
    
  g_over_sigk = 1./g_sigma_k;
  g_over_sige = 1./g_sigma_e;
  g_reci_c_mu = 1./g_C_MU;
  g_sqrt_c_mu = sqrt(g_C_MU);
  g_reci_sqrt_c_mu = sqrt(g_reci_c_mu);
  g_c_mu_pow_075 = pow((double)g_C_MU,(double)0.75);
  g_Nusselt_power_plus_one = g_Nusselt_power + 1.0;
  g_Nusselt_power_sqrt_Cmu = g_Nusselt_power * sqrt(g_C_MU);
  g_one_over_sqrt_C_MU = 1.0 / sqrt(g_C_MU);
}

void static check_for_debugging_flags()
{
  // Temporary kludge to get line buffering on stderr
  static char stderr_buf[BUFSIZ];
  
  cSTRING debug_malloc_mode = getenv("EXA_DEBUG_MALLOC_MODE");
  if(debug_malloc_mode != NULL) {
    sim.debug_malloc_p = TRUE;
    if((strcmp(debug_malloc_mode, "CHECK_PTR") == 0)
       || (strcmp(debug_malloc_mode, "check_ptr") == 0))
      exa_enable_malloc_debug(EXA_MALLOC_CHECK_PTR);
    else if((strcmp(debug_malloc_mode, "VERIFY_HEAP") == 0)
            || (strcmp(debug_malloc_mode, "verify_heap") == 0))
      exa_enable_malloc_debug(EXA_MALLOC_VERIFY_HEAP);
    else
      msg_error("Setting of EXA_DEBUG_MALLOC_MODE (%s) is invalid", debug_malloc_mode);
  } else
    sim.debug_malloc_p = FALSE;

  exa_malloc_set_error_handler(simeng_malloc_error_handler);
  setvbuf(stderr, stderr_buf, _IOLBF, BUFSIZ);
}

static void print_version_information()
{
  cSTRING product_version = jobctl_get_distname();
  if (product_version == NULL) product_version = "DIST";

  if (my_proc_id == 0) {
    asINT32 processors;

    if(total_fsps == total_csps) {
      processors = total_fsps;
    } else {
      if(total_fsps == 0) {
        processors = total_csps;
      } else if(total_csps == 0) {
        processors = total_fsps;
      } else {
        processors = 0;
      }
    }

    if(processors == 0) {
      msg_internal_error("Inconsistency in numbers of FSP's (%d) and CSP's (%d)",total_fsps,total_csps);
    } else if(processors == 1) { 
      // This was moved here from the CP. As there is no msg_warn_no_prefix, its behavior is emulated here.
      msg_print_no_prefix("Warning: Simulation will run on 1 processor.");
    } else {
      msg_print_no_prefix("Simulation will run on %d processors.", processors);
    } 

    msg_print_no_prefix("Simulation Engine Version %s.%s", product_version , SP_VERSION);
    msg_print_no_prefix("Physics Version %s.%s",  product_version, PHYSICS_VERSION);
  }
}

void evaluate_sim_run_info()
{
  if (sim_run_info.ablm_cap >= 0) {
    g_ref_lmin = sim_run_info.ablm_cap;
    msg_print("ABLM cap set to %f.", g_ref_lmin);
  }

  if(sim_run_info.num_sps != total_sps)
    msg_error("A %d processor simulation was requested, but the MPI file indicated %d SP's",
              sim_run_info.num_sps, total_sps); 

  if (sim_run_info.run_options & (SIM_REPORT_TIME | SIM_REPORT_TIME_VERBOSE))
    sim.async_event_flags.timers_on_p = TRUE;
  if (sim_run_info.run_options & SIM_REPORT_DIAGNOSTICS)
    sim.async_event_flags.diags_on_p = TRUE;

  if (sim_run_info.run_options & SIM_FULL_CKPTS)
    sim.create_full_ckpts = TRUE;


  if (sim_run_info.next_checkpoint > 0) {
    sim.current_ckpt_time = sim_run_info.initial_checkpoint;
    sim.next_ckpt_time = sim_run_info.next_checkpoint;
  } else {
    sim.current_ckpt_time = 
      sim.next_ckpt_time = BASETIME_INVALID;
  }

  if ((sim_run_info.run_options & SIM_REPORT_MEMORY) != 0)
    exa_malloc_set_frag_msg_level(1000000);

  if ((sim_run_info.run_options & SIM_NO_TIMEOUT) != 0)
    sp_disable_synchronization_timeout();
  else 
    sp_enable_synchronization_timeout();

  /* This seems weird but it mimics the logic that used to be in cp_sp_lib */
#if 0
  exa_enable_malloc_debug(((sim_run_info.run_options & SIM_DEBUG_MALLOC) != 0) ? 
			  EXA_MALLOC_NONE : EXA_MALLOC_CHECK_PTR);
#endif
  if(sim_run_info.num_sps != total_sps)
    msg_error("MPI indicates %d SP's, but the CP says there are %d",
              total_sps, sim_run_info.num_sps); 
  if ((sim_run_info.run_options & SIM_DISABLE_RANDOM) != 0)
    sp_is_rand_disabled = TRUE;
  else
    sp_is_rand_disabled = FALSE;

#if BUILD_GPU
  if (sim_run_info.gpu_repeatable) {
    GPU::g_use_repeatable_reduction = GPU::REDUCTION_MODE::REPEATABLE;
    GPU::g_use_repeatable_meas = GPU::REDUCTION_MODE::REPEATABLE;
  } else {
    GPU::g_use_repeatable_reduction = GPU::REDUCTION_MODE::NON_REPEATABLE;
    GPU::g_use_repeatable_meas = GPU::REDUCTION_MODE::NON_REPEATABLE;
  }

  GPU::g_max_buffer_size = sim_run_info.gpu_max_buffer_size;
#endif
}

void prevent_mmap_from_blocking_growth_of_the_heap()
{
#if (defined(__alpha))
  // Prevent page allocated via mmap from blocking the growth of the heap
  __sbrk_override = 1;
#endif
}

void start_ri_diagnostics()
{
  /* ri_diagnostics can not be on in the mainline registries. */
#if RI_DIAG_WINS
  msg_warn ("ri_diagnostics may be on.\n");
  ri_diagnostics = sim_ri_diagnostics();
#else
  g_ri_diagnostics = sim_ri_diagnostics();
  if (g_ri_diagnostics) {
    msg_warn ("Mainline registries can not have  ri_diagnostics as TRUE.\n"
	      "This is being set to FALSE.\n");
    g_ri_diagnostics = FALSE;
  }
#endif
}

void print_debug_memory_report()
{
  if(sim.debug_malloc_p) {
    ccDOTIMES(p, total_sps) {
        sp_synchronize();
        if (p == my_proc_id) {
          msg_print("Memory report prior to `execution (SP: %d)'", p);
          exa_malloc_debug_report(TRUE);
        }
    };
    sp_synchronize();
  }
}

void report_memory_usage()
{
#ifdef NOT_DEFINED
#if REPORT_MALLOC_INFO
#if defined(__sgi) || defined(__sun__)
  if (sim_run_info.run_options & SIM_REPORT_TOTAL_MEMORY) {
    struct mallinfo info;
    info = mallinfo();
    msg_print ("Memory use information:");
    msg_print ("\ttotal size of arena: %d", info.arena);
    msg_print ("\tnumber of ordinary blocks: %d", info.ordblks);
    msg_print ("\tnumber of small blocks: %d", info.smblks);
    msg_print ("\tnumber of holding block: %d", info.hblks);
    msg_print ("\tspace in holding block headers: %d", info.hblkhd);
    msg_print ("\tspace in small blocks in use: %d", info.usmblks);
    msg_print ("\tspace in free small blocks: %d", info.fsmblks);
    msg_print ("\tspace in ordinary blocks in use: %d", info.uordblks);
    msg_print ("\tspace in free ordinary blocks: %d", info.fordblks);
  }
#endif
#endif
#endif
  if (sim_run_info.run_options & SIM_REPORT_TOTAL_MEMORY) 
    msg_print ("Total dynamically allocated bytes: %u", total_malloced_bytes ());
}

#if defined(_EXA_HPMPI)
// Dummy MP_Irecv to work around hanging problem in HP-MPI - see PR 19489
static MPI_Request hpmpi_dummy_request;
#endif

void finalize()
{
#if defined(_EXA_HPMPI)
  // Complete dummy MPI_Irecv - see PR 19489
  MPI_Status dummy_status;
  MPI_Wait(&hpmpi_dummy_request, &dummy_status);
  if(my_proc_id == 0) {
    //    asINT32 dummy_message = 0x012345678;
    SEND_EXA_SIM_MSG<asINT32, 1> dummy_message(eMPI_DEBUG_DUMMY_TAG);
    *dummy_message.return_buffer() = 0x012345678;
    g_exa_sp_cp_comm.send(dummy_message.mpi_msg);
  }
#endif

#if BUILD_GPU
  //Free GPU memory of global objects
  clear_mblks_send_recv_groups();
#endif

  // Consume any messages already sent by the CP
  simerr_finalize();
  sim_finalize_meas();
  sim_finalize_coupling();
  sim_finalize_tbs_data();
  sim_finalize_shob_comm();
  g_strand_mgr.cancel_pending_recvs();
  if (has_trajectory_window()) {
    g_trajectory_id_map.cancel_pending_recv();
  }
  if (sim.use_implicit_shell_solver) {
#if !GPU_COMPILER && !BUILD_GPU
     implicit_shell_solver_destroy_solver_objects();
#endif
  }
  if (sim.use_implicit_solid_solver) {
#if !GPU_COMPILER && !BUILD_GPU
#if !BUILD_5G_LATTICE
     implicit_solid_solver_destroy_solver_objects();
#endif
#endif
  }
#if !defined(_EXA_LAM_MPI)
  if(sim_run_info.barrier_instead_of_finalize) {
    sigset (SIGTERM,SIG_IGN); 
    MPI_Barrier(eMPI_sp_cp_comm);
  } else {
    MPI_Finalize();
  }
#endif
}

void assign_exit_cleanup_handler()
{
  if (atexit(exa_sim_sp_cleanup) != 0) 
    msg_error ("Unable to establish exa_sim_sp_cleanup handler");
}

static VOID exchange_early_sim_run_info()
{
  receive_early_sim_run_info();
  send_early_sim_run_info();
}

static VOID exchange_late_sim_run_info()
{
  receive_late_sim_run_info();
  send_late_sim_run_info();
}

static VOID validate_solver_version()
{
  cSTRING run_time_solver_version_str = getenv("EXA_POWERFLOW_SOLVER_VERSION");
  if (my_proc_id == 0
      && run_time_solver_version_str == NULL)
    msg_internal_error("Environment variable EXA_POWERFLOW_SOLVER_VERSION not defined.");
  asINT32 run_time_solver_version = atoi(run_time_solver_version_str);
  asINT32 compile_time_solver_version = SOLVER_VERSION; // compile time constant
  if (my_proc_id == 0
      && run_time_solver_version != compile_time_solver_version)
    msg_internal_error("Simulator was built with solver version %d, but is running in a"
                       " PowerFLOW distribution that uses solver version %d.",
                       compile_time_solver_version, run_time_solver_version);
}

static void print_simulation_info() {

#if BUILD_DOUBLE_PRECISION
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using double precision");
  }
#else 
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using single precision");
  }
#endif
#if BUILD_D39_LATTICE
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using transonic solver");
  }
#endif
#if BUILD_5G_LATTICE
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using 5G solver");
  }
#endif

// #if defined(BUILD_GPU)
//   if (my_proc_id == 0) {
//     msg_print_no_prefix("Simulation is using GPU acceleration");
//   }
// #endif
  
#if EXA_USE_AVX
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using AVX vectorization");
  }
#elif EXA_USE_SSE
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is using SSE vectorization");
  }
#else
  if (my_proc_id == 0) {
    msg_print_no_prefix("Simulation is not using vectorization");
  }
#endif

  if (my_proc_id == 0) {
    if (g_use_avx2_advection) {
      msg_print_no_prefix("Simulation is using vectorized advection with AVX2 instructions");
    } else {
      msg_print_no_prefix("Simulation is using scalar advection");
    }
  }
  
#if ENABLE_CONSISTENCY_CHECKS
  if (my_proc_id == 0)
    msg_print_no_prefix("CONSISTENCY CHECKS ARE ENABLED");
#endif

#if ENABLE_LOGGING
  if (my_proc_id == 0)
    msg_print_no_prefix("SIMENG LOGGING IS ENABLED");
#endif

#ifdef DEBUG   
  if (my_proc_id == 0)
    msg_print_no_prefix("SIMULATOR IS COMPILED DEBUG");
#endif

}

static VOID add_calibration_run_events() {
  //  aligning end time with multiple of number of calibration iterations.
  //  sim.end_time = sim.calibration_params.iterations * (sim.end_time / sim.calibration_params.iterations + 1);
  asINT32 period = g_timescale.m_end_time / sim.calibration_params.iterations;
  BASETIME start_time = sim.calibration_params.calib_period * (g_timescale.m_time / sim.calibration_params.calib_period);
  g_async_event_queue.add_entry(new_event(EVENT_ID_CALIBRATION_RUN, 0, start_time + period - 1, period, g_timescale.m_end_time - period ));
  sim.prepare_calibration_run = FALSE;
}

BOOLEAN g_use_avx2_advection;

struct sFLUID_DYN_DCACHE;
extern sFLUID_DYN_DCACHE* sim_fluid_dyn_dcache;

VOID init_dcache();
VOID sort_surfel_groups_for_msfl_build();

/*--------------------------------------------------------------------------*
 * Main routine
 *--------------------------------------------------------------------------*/
#if BUILD_GPU
std::vector<sHMBLK::UBLK_BOX> g_mega_boxes;

namespace GPU {
static void send_gpu_info(const sSP_GPU_INFO& gpu_info) 
{
  // temporarily reusing this tag until after initial release
  SEND_EXA_SIM_MSG<sSP_GPU_INFO,1> send_msg(eMPI_CP_SIM_RUN_INFO_TAG);
  *send_msg.mpi_msg.buffer() = gpu_info;
  g_exa_sp_cp_comm.send(send_msg.mpi_msg);
}
}
#endif

static void main_internal(int argc, char *argv[])
{
  read_turb_parm_init();
  init_sim_constant_overrides();
  initialize_turb_model_parameters();
  
  sp_initialize(argc, argv, sim.is_conduction_sp);
  eMPI_sp_init_sp_node_comm();
  eMPI_sp_init_mastersp_node_comm();

  validate_solver_version();


  prevent_mmap_from_blocking_growth_of_the_heap();
  check_for_debugging_flags();
  set_fp_underflow_flush_to_zero();
  assign_exit_cleanup_handler();
  install_signal_handlers();

  initialize_loggers(my_proc_id);
  g_exa_sp_cp_comm.initialize_log("MPI-sp_cp-" + std::to_string(my_proc_id) + ".log");
  g_sp_node_comm.initialize_log("null");
  g_mastersp_node_comm.initialize_log("null");
#if not DEBUG_MPI_MESSAGES
  g_exa_sp_cp_comm.log().turn_off();
#endif

  eMPI_wait_on_startup(argv[0], eMPI_PROCESS::SP);

#if BUILD_GPU
  sSP_GPU_INFO sp_gpu_info = GPU::init_device_and_context();
  GPU::send_gpu_info(sp_gpu_info);
#endif

  if (getenv("MYWAIT")) {
    printf("MYWAIT\n");
    int temp=0;
    while(temp==0) {/*printf("MYWAIT\n");*/}
  }
    
#if defined(_EXA_HPMPI)
  // Dummy MP_Irecv to work around hanging problem in HP-MPI - see PR 19489
  asINT32 dummy_recv_buffer;
  eMPI::irecv(&dummy_recv_buffer, 1, eMPI_asINT32, eMPI_sp_cp_rank(), eMPI_DEBUG_DUMMY_TAG, eMPI_sp_cp_comm, &hpmpi_dummy_request);
#endif

  g_random_particle_properties = cnew sRANDOM_PARTICLE_PROPERTIES;
  
  StatusTree::initialize_status_tree();

  exchange_early_sim_run_info();
  
  sim_initialize_timers();

  if (sp_is_rand_disabled)
    g_random_num_generator.disable();

  g_dgf_reader.read_records_from_stream();
  // g_dgf_reader.read_records_from_stream(bufer );
  allocate_host_v2s_buffer();

  if (sim.is_conduction_model)
    convert_conduction_sim_info_units();

  // Needs to be after read_records_from_stream so we know if conduction is enabled
  print_version_information();

  if (sim.is_movb_sim())
    add_all_procs_to_sp_map();
  
  // Deferred initialization of space varying attributes until after the SP has read 
  // through the UBLK and SURFEL LGI records. See PR 48816 for more details  
  // Does this need to be done fore both realms?

  ccDOTIMES(realm, STP_N_REALMS) {
    g_ublk_table[realm].complete_ublk_space_varying_attribute_initialization();
  }

  //Decide whether to enable certain particle modeling features.
  if(sim.is_particle_model) {
    particle_sim.set_film_solver_status();
    particle_sim.set_thermal_particle_solver_status();
  }
  
  // after all records are read, so the time for full checkpoint restore
  // will be correct.
  g_strand_mgr.init();

  sim_initialize_nested_lrfs();
  g_strand_mgr.finish_init();
  
  //We need the CPU to start some communication for the seeding, for example we need communication in the CPU data before we do accumulate_all_v2s_and_s2s_weights, that function is run in CPU not in GPU 
  g_radiation_patches.do_initial_rp_comm();
  g_radiation_patches.build_radiation_fset(g_radiation_patches_fset);

  //Non-conformal contact accumulators reside in the contact send/recv groups, need to be sized before we can allocate the send/recv buffers
  allocate_contact_send_recv_accumulators();
  
  asINT32 n_send_buffers = allocate_send_buffers();

  g_strand_mgr.m_n_send_groups = n_send_buffers;
  g_strand_mgr.m_surfel_send_channel.allocate(SURFEL_SEND_TYPE);
  g_strand_mgr.m_wsurfel_send_channel.allocate(WSURFEL_SEND_TYPE);
  g_strand_mgr.m_contact_send_channel.allocate(CONTACT_SEND_TYPE);

  allocate_recv_buffers();
// #ifndef BUILD_GPU
//   initialize_recv_group_arrays(); //postponed in the GPU
// #else
//   initialize_recv_group_arrays_on_CPU(); 
// #endif
  initialize_recv_group_arrays_on_CPU();  //TODO: cleanup, equivalent to the above
  initialize_receive_channels();


  initialize_meas_reduction_trees(); // also allocates meas window send buffers
#if DEBUG
  //check_if_all_sliding_mesh_surfels_in_rings();
#endif

  adjust_size_and_location_of_boxes();
  divide_ublk_boxes();
  ccDOTIMES(realm, STP_N_REALMS) {
    g_ublk_table[realm].assign_ublks_to_boxes();
  }

  create_overlap_on_boxes_boundary();

  ccDOTIMES(realm, STP_N_REALMS) {
    g_ublk_table[realm].update_split_ublk_instance(realm);
    g_ublk_table[realm].mark_same_split_instance_neighbors();
    g_ublk_table[realm].append_split_neighbor_info();
  }


  assign_scale_interface();

  //for bsurfels, exchange information about boxes and locations
  if (sim.is_movb_sim())
    build_box_spmap_for_bsurfels();

  // compute post advect scale factors and other weights
  // Also translate surfel IDs to surfel pointers for
  // source surfels in S2S advect
  // odd surfels in even odd surfels
  // sampling surfels not implemented yet.
  update_surfel_ublk_interactions();
  
  update_shob_group_types();
  

  // initialize_send_channel must be called after update_shob_group_types
  // 
  g_strand_mgr.m_surfel_send_channel.initialize_send_channel();
  g_strand_mgr.m_wsurfel_send_channel.initialize_send_channel();
  g_strand_mgr.m_contact_send_channel.initialize_send_channel();

  compute_dependencies_from_tables_to_physics_descs();
  compute_dependencies_from_coupling_to_physics_descs();

  // initialize ghost surfel bc types, which will be used later in
  // dist_to_surf calculation

  do_send_surfel_bc_types(); //In the GPU case we are using the CPU to send the surfel bc types just before creating the megasurfels


  finish_init_of_mlrf_surfel_groups();

  // exchange sim run info between SP and CP
  exchange_late_sim_run_info();

  //Allow vectorized advection only if this is an AVX build and simulation
  //uses single precision
#if EXA_USE_AVX  && !(BUILD_DOUBLE_PRECISION)
  BOOLEAN avx2_disabled = (sim_run_info.run_options & SIM_NO_AVX2) ? TRUE:FALSE;
  BOOLEAN avx2_supported =  __builtin_cpu_supports("avx2") ? TRUE:FALSE;
  g_use_avx2_advection = avx2_supported && !avx2_disabled;
#else
  g_use_avx2_advection = FALSE;
#endif

  print_simulation_info();


  initialize_event_queue();
  
  evaluate_sim_run_info();

  // Collect counters for SP timers here
  // At this point, we have the ublk pointers updated in surfel interactions
  // so that we know the ublk info including the scale. We also need to have the
  // event queue initialized and the sim run info to be evaluated for timers_on_p
  // to be correct.

#if 0  
  if (sim.async_event_flags.timers_on_p) {
    collect_sp_timer_counters();
  }
#else
  collect_sp_timer_counters();
#endif

  send_ref_frame_info();
  send_mbc_info();
  send_movb_info();

  exchange_sp_coupling_info();


  if(sim.is_meas_prescribed && my_proc_id == 0)
    msg_print_no_prefix("EXA_MEAS_PRESCRIBED: Data written to measurement files would be the prescribed data.");
  if(sim.prepare_calibration_run)
    add_calibration_run_events();
  recv_initial_transient_boundary_seed_data();
  add_transient_boundary_seed_data_comm_events();
  //////////////////////////////

  // This prediction of grf linear acceleration should not be done 
  // full checkpoint restores.
  if (sim.grf_physics_desc != NULL && !sim.is_full_checkpoint_restore) {
    asINT32 scale_p1 = finen_scale(COARSEST_SCALE);
    if (FINEST_SCALE == COARSEST_SCALE)
      scale_p1 = COARSEST_SCALE;
    sim_predict_global_ref_frame(scale_p1, COARSEST_SCALE, g_timescale.time());
  }
  //////////////////////////////

  /* We have to initialize surfel stencils prior to seeding for shell conduction solver
   * since it needs to compute surfel grad_t during seeding */
  if (sim.is_shell_conduction_model && sim.is_particle_model) {
    msg_internal_error("Shell conduction cannot work with particle solver yet");
  }

#ifdef CONDUCTION_ENFORCE_SURFEL_STENCILS_INITIALIZE_DUE_TO_HARDCODE_IN_CP
  surfel_stencils_initialize();
#else
  const BOOLEAN construct_surfel_stencils = sim.is_particle_model || sim.is_shell_conduction_model;

  if (construct_surfel_stencils) {
    surfel_stencils_initialize();
  }
#endif

  cHOST_SHOBS_SEEDER shobs_seeder(g_timescale.m_timestep);
  shobs_seeder.process();

  finish_init_of_bsurfels();

  ccDOTIMES(realm, STP_N_REALMS) {
    g_ublk_table[realm].trim();   // trim ublk table
    g_surfel_table[realm].trim(); // trim surfel table
  }

  print_debug_memory_report();
  
  start_ri_diagnostics();
  if (sim.is_particle_model) {
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
    g_surfel_vertices_info.remap_surfel_vertex_indices_from_global_to_local_for_old_stencil_construction();
    g_surfel_vertices_info.read_surfel_vertices();
    g_surfel_vertices_info.create_half_edges_for_old_stencil_construction();
    g_surfel_vertices_info.delete_vertex_surfels_arrays();
    delete[] g_surfel_vertices_info.m_surfel_vertices;
    g_surfel_vertices_info.m_surfel_vertices = nullptr;
#endif

    particle_sim.initialize();
    if (has_trajectory_window()) {
      g_trajectory_id_map.initialize(total_sps);
      g_trajectory_id_map.post_map_recv();
      sp_trajectory_vertex_manager.initialize();
      sp_trajectory_hitpoint_manager.initialize();
      sp_trajectory_startpoint_manager.initialize();
    }

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
    if (!sim.is_film_solver) {
      g_surfel_vertices_info.read_surfel_vertices();
    }
#endif

    compute_rain_emitter_emission_rates();

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
    g_surfel_vertices_info.compute_surfel_half_edge_lengths();
#endif
    //compute surfel curvature
    if (sim.is_film_solver ) {
      particle_sim.compute_curvature_of_surfels();
    }
    particle_sim.send_queued_stencil_sim_errors();

    compute_off_plane_tolerances(); //compute surfel tolerance used in collision detection.

    
    if (sim.is_film_solver ) {
      if (g_enable_sample_surface_film_meas)
        particle_sim.find_surfels_that_intersect_sampling_surfaces();
      if (g_enable_film_mlrf)
        particle_sim.find_surfels_that_intersect_mlrf_interfaces();
    }


#if ENABLE_CONSISTENCY_CHECKS
#if 0
    //Having this on all the time is problematic for QA testing until PR 46903 is fixed (the order of the warnings in the sim.o are not repeatable)
    particle_sim.init_parcel_ublk_check();
    particle_sim.check_surfel_neighbor();
#endif
#endif
  }

  init_dcache();
  
#ifdef BUILD_GPU
  g_strand_mgr.m_n_send_groups = 0; //actual amount computed in build_send_groups()
  g_host_ublk_solver_blocks.clear_data_of_type<NEARBLK_DP_PASS_FACTORS_DATA_TYPE>();
  sort_surfel_groups_for_msfl_build();

  // GPU::set_global_reduction_mode();
  SIMULATOR_NAMESPACE::initialize_ublk_data_offset_table<sMBLK>(get_sim_ublk_surfel_offset_table_opts());
  SIMULATOR_NAMESPACE::initialize_surfel_data_offset_table<sMSFL>(get_sim_ublk_surfel_offset_table_opts());

  std::vector<std::unique_ptr<cDEVICE_MEAS_WINDOW_BUILDER>> gpu_meas_window_builders;
  for(MEAS_WINDOW window : g_meas_windows) {
    gpu_meas_window_builders.emplace_back( window->create_device_window_builder() );
  }

  g_mega_boxes = init_mblk_boxes();
  if (g_ublk_table[STP_COND_REALM].n_ublks() > 0 || g_surfel_table[STP_COND_REALM].n_surfels() > 0) {
    msg_internal_error("GPU does not support processing of conduction ublks yet");
  }
  
  // Store for each ublk id, its corresponding mega ublk id and offset within
  std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>> child_ublk_to_mblk_map;
  
  GPU::g_host_mblks = build_mblks(g_mega_boxes,
                                  child_ublk_to_mblk_map,
                                  gpu_meas_window_builders);


  set_vr_coarse_and_fine_info(GPU::g_host_mblks, child_ublk_to_mblk_map);
  gpu_mark_fringe_coarse_voxels_that_abut_interior(GPU::g_host_mblks, child_ublk_to_mblk_map);
  g_host_ublk_solver_blocks.clear_all_data();

  size_t n_sfls = g_surfel_table[STP_FLOW_REALM].n_surfels();
  std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>> child_sfl_to_msfl_map;
  std::vector<sMSFL*> h_msfl_table =  build_msfls(GPU::g_host_mblks,
                                                                 child_ublk_to_mblk_map,
                                                                 child_sfl_to_msfl_map,
                                                                 gpu_meas_window_builders);

  set_nearblock_closest_surfels(GPU::g_host_mblks, h_msfl_table,
				child_sfl_to_msfl_map);
  
  for(MEAS_WINDOW window : g_meas_windows) {
    gpu_meas_window_builders.at(window->index)->compute_host_side_info();
  }

  sH2D_EXPORT h_data(&g, &sim, &GPU::g_host_mblks,
		     &h_msfl_table,
		     &g_msfl_sizes_table,
		     &g_ublk_data_offset_table_64,
		     &g_surfel_data_offset_table_64,
		     &g_ublk_dynamics_data_size_64,
		     &g_mblk_groups,
		     &g_msfl_groups,
		     sim_fluid_dyn_dcache,
		     &g_meas_windows);
  
  export_host_data_to_device(h_data);

  initialize_recv_group_arrays(); //postponed compared to CPU
  build_mblks_send_recv_groups(GPU::g_host_mblks, child_ublk_to_mblk_map, is_solver_active(TURB_SOLVER, sim.init_solver_mask));
  build_msfls_send_recv_groups(h_msfl_table, child_sfl_to_msfl_map, is_solver_active(TURB_SOLVER, sim.init_solver_mask));
  initialize_receive_channels(); //postponed compared to CPU

  //GPU::do_send_surfel_bc_types();

  g_host_mblk_solver_blocks.clear_all_data();

  cDEVICE_SHOBS_SEEDER d_shobs_seeder(g_timescale.m_timestep);
  d_shobs_seeder.process();
  // GPU::output_mem_usage();
#endif

#ifndef _EXA_HPMPI
  if(sim_run_info.parallel_io)
  {  //Check if parallel_io_max_buffer_size fits in available memory or shrink it 
    unsigned long long avail_mem = 0;
    unsigned long long max_available_buffer_size_mb = hpc_io::utils::get_max_available_buffer_size(eMPI_sp_comm, sim_run_info.parallel_io_max_buffer_size, avail_mem) >> 20;
    if(max_available_buffer_size_mb != sim_run_info.parallel_io_max_buffer_size)
    {
      msg_warn("Memory available %s is not enough to use parallel_io_max_buffer_size=%u, simulation will use %llu MB per SP.\n", hpc_io::utils::bytesToHuman(avail_mem).c_str(), sim_run_info.parallel_io_max_buffer_size, max_available_buffer_size_mb);
      sim_run_info.parallel_io_max_buffer_size = max_available_buffer_size_mb;
    }
  }
#endif
  sim_run();  // Start the simulation 
  
  report_memory_usage();

  finalize(); // Done !
}

int main(int argc, char *argv[])
{
  exa_malloc_set_error_handler(xnew_malloc_error_handler);

  try {
    main_internal(argc, argv);
  }
  catch (std::bad_alloc& ba) { // bad_alloc is a class
    fprintf(stderr, "SP%d> Error: memory allocation failure: %s\n", my_proc_id, ba.what());
    fflush(stderr);

    // Try to save the error_str into a hidden file, but don't worry if it fails. */
    FILE *hidden_file = fopen(".exa_error", "w");
    if (hidden_file != NULL) {
      fprintf(hidden_file, "SP%d> Error: memory allocation failure: %s\n", my_proc_id, ba.what());
      fprint_memory_limits(hidden_file);
      fflush(hidden_file);
      fclose(hidden_file);

      err_exit();
      return 1; // exit with error code (in case err_exit doesn't do its job)
    }
  }
  return 0;
}

