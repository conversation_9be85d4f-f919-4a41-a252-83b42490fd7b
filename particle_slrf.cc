/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "particle_sim.h"
#include "particle_slrf.h"

namespace PARTICLE_SLRF_DIRECTIONS {
  typedef enum {
    I_TO_E,
    E_TO_I
  } PARTICLE_SLRF_DIRECTION;
}

static VOID exchange_slrf_parcels(sSURFEL_PAIR *quantum);
static VOID emit_lrf_parcels(SURFEL slrf_surfel, 
                             PARTICLE_SLRF_DIRECTIONS::PARTICLE_SLRF_DIRECTION direction,
                             LRF_PHYSICS_DESCRIPTOR lrf);
static VOID transform_parcel_state_to_new_reference_frame(
                                                          PARCEL_STATE parcel,
                                                          PARTICLE_SLRF_DIRECTIONS::PARTICLE_SLRF_DIRECTION direction,
                                                          LRF_PHYSICS_DESCRIPTOR lrf);

static BOOLEAN emit_lrf_parcel(SURFEL slrf_surfel, PARCEL_STATE parcel);

VOID process_slrf_parcels() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      exchange_slrf_parcels(slrf_surfel_pair);
    }
  }
}

static VOID exchange_slrf_parcels(sSURFEL_PAIR *quantum) {
  PARCEL_LIST temp;
  temp = quantum->m_exterior_surfel->p_data()->surface_parcel_list;
  quantum->m_exterior_surfel->p_data()->surface_parcel_list = quantum->m_interior_surfel->p_data()->surface_parcel_list;
  quantum->m_interior_surfel->p_data()->surface_parcel_list = temp;
  LRF_PHYSICS_DESCRIPTOR interior_lrf = quantum->m_interior_surfel->lrf_physics_descriptor();
  emit_lrf_parcels(quantum->m_exterior_surfel, PARTICLE_SLRF_DIRECTIONS::I_TO_E, interior_lrf);
  emit_lrf_parcels(quantum->m_interior_surfel, PARTICLE_SLRF_DIRECTIONS::E_TO_I, interior_lrf);
}

static VOID emit_lrf_parcels(SURFEL slrf_surfel,
                             PARTICLE_SLRF_DIRECTIONS::PARTICLE_SLRF_DIRECTION direction,
                             LRF_PHYSICS_DESCRIPTOR lrf) {
  PARCEL_LIST parcel_list = slrf_surfel->p_data()->surface_parcel_list;

  asINT32 parcel_count = 0;
  parcel_list->reset();
  while(!parcel_list->exhausted()) {

    parcel_count++;

    PARCEL_STATE parcel = parcel_list->data();
    parcel_list->remove();
    transform_parcel_state_to_new_reference_frame(parcel, direction, lrf);
    if(!emit_lrf_parcel(slrf_surfel, parcel)) {
      std::ostringstream error_string;
      error_string << " parcel " << parcel->id;
      error_string << ":" << parcel->originating_sp;
      error_string << " could not be emitter from slrf surfel " << (int)slrf_surfel->id();
      sdFLOAT point[3];
      asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
      vcopy(point, parcel->x[new_state_index]);
      simerr_report_error_code(SP_EER_PARCEL_CANT_BE_EMITTED_FROM_LRF_SURFEL,
                               slrf_surfel->scale(),
                               point,
                               error_string.str().c_str(),
                               slrf_surfel->centroid[0],
                               slrf_surfel->centroid[1],
                               slrf_surfel->centroid[2]);
      particle_sim.destroy_parcel(parcel, TRUE); //TRUE means count the mass as inexplicably lost.
    }
  }
  timer_accum_counters(SP_PARTICLE_SLRF_EMISSION_TIMER, 0, parcel_count);
}


static VOID transform_parcel_state_to_new_reference_frame(
                                                          PARCEL_STATE parcel,
                                                          PARTICLE_SLRF_DIRECTIONS::PARTICLE_SLRF_DIRECTION direction,
                                                          LRF_PHYSICS_DESCRIPTOR lrf ) {
  //Transform the parcel's velocity vector but not it's position since this is used with static lrf only
  ccDOTIMES(state_index, NUM_PREVIOUS_STORED_TIMESTEPS) {
    sPARTICLE_VAR radius[N_SPACE_DIMS];
    sdFLOAT ref_frame_vel[3] = {0.0, 0.0, 0.0};
    sPARTICLE_VAR new_parcel_velocity[N_SPACE_DIMS];
    vsub(radius, parcel->x[state_index], lrf->point);
    vcross(ref_frame_vel, radius, lrf->axis);
    vmul(ref_frame_vel, lrf->omega);
    switch(direction) {
    case PARTICLE_SLRF_DIRECTIONS::E_TO_I:
      vsub(new_parcel_velocity, parcel->v[state_index], ref_frame_vel); //get the velocity relative to the new moving ref frame but in the outer csys
      rotate_vector(new_parcel_velocity, parcel->v[state_index], lrf->containing_to_local_rotation_matrix, TRUE); //transform the csys to the inner csys
      break;
    case PARTICLE_SLRF_DIRECTIONS::I_TO_E:
      vadd(new_parcel_velocity, parcel->v[state_index], ref_frame_vel);  //add the reference frame vel to the parcel vel to get the velocity in the outer ref frame but realtive to inner csys
      rotate_vector(new_parcel_velocity, parcel->v[state_index], lrf->containing_to_local_rotation_matrix, FALSE); //transform to the outer csys
    default:
      break;
    }
  }
}



#define TOL_WITHIN_SURFEL_PERIMETER_PHY  0.02
static BOOLEAN emit_lrf_parcel(SURFEL slrf_surfel, PARCEL_STATE parcel) {
  asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
  if(parcel->dynamics_type != ON_SURFACE) {
    //Find the ublk that this parcel belongs in.
    int voxel;
    UBLK ublk = particle_sim.find_containing_neighbor_ublk(parcel->x[new_state_index], slrf_surfel, voxel);
    if(ublk == NULL)
      return FALSE;
    if(ublk->p_data()->fluid_parcel_list[voxel] == NULL)
      return FALSE;
    ublk->p_data()->fluid_parcel_list[voxel]->add(parcel);
    return TRUE;
  } else {
    //Compute the point on the sliding mesh surfel this parcel came out of.
    asINT32 previous_state_index = parcel->relative_timestep_to_state_index(-1); //index for last timestep
    asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);

    sPARTICLE_VAR post_wall_norm_coord = particle_sim.compute_normal_distance_to_centroid(parcel->x[new_state_index], slrf_surfel);

    sPARTICLE_VAR v_norm = vdot(parcel->v[new_state_index], slrf_surfel->normal);
    sPARTICLE_VAR t_minus = -post_wall_norm_coord / v_norm;

    sPARTICLE_VAR crossing_point[N_SPACE_DIMS];
    parcel->extrapolate_trajectory(new_state_index, t_minus, crossing_point);

    sPARTICLE_VAR radius[N_SPACE_DIMS];
    vsub(radius, parcel->x[new_state_index], crossing_point);

    sPARTICLE_VAR radius_direction[N_SPACE_DIMS];
    vcopy(radius_direction, radius);
    vunitize(radius_direction);

    //For each film-carying surfel, rotate the segment of the particle
    //trajectory that extends outside the sliding mesh surfel into its
    //plane.  If that rotated position is within the surfel's
    //perimeter, then add the parcel to that surfel.

    assert(slrf_surfel->p_data()->intersecting_surfels);
    BOOLEAN found_a_surfel = FALSE;
    ccDOTIMES(nth_surfel, slrf_surfel->p_data()->intersecting_surfels->size()) {
      SURFEL surfel =  slrf_surfel->p_data()->intersecting_surfels->at(nth_surfel);

      sPARTICLE_VAR rotation_angle = acos(vdot(radius_direction, surfel->normal)) - M_PI/2.0;

      sPARTICLE_VAR axis_of_rotation[N_SPACE_DIMS];
      vcross(axis_of_rotation, radius_direction, surfel->normal);

      sPARTICLE_VAR rotated_radius[N_SPACE_DIMS];
      vscale(rotated_radius, cos(rotation_angle), radius);

      sPARTICLE_VAR axis_cross_radius[N_SPACE_DIMS];
      vcross(axis_cross_radius, axis_of_rotation, radius);
      vmac(rotated_radius, sin(rotation_angle), axis_cross_radius);

      sPARTICLE_VAR axis_dot_radius = vdot(axis_of_rotation, radius);
      vmac(rotated_radius, axis_dot_radius * (1- cos(rotation_angle)), axis_of_rotation);

      sPARTICLE_VAR new_position[N_SPACE_DIMS];
      vadd(new_position, rotated_radius, crossing_point);

      if(particle_sim.position_is_within_surfel_perimeter(surfel, new_position) + TOL_WITHIN_SURFEL_PERIMETER_PHY > 0.0) {
        found_a_surfel = TRUE;

        surfel->p_data()->surface_parcel_list->add(parcel);
        vcopy(parcel->x[new_state_index], new_position);
        //Also rotate the parcels velocity vector to lie in the plane of the new surfel
        sPARTICLE_VAR rotated_velocity[N_SPACE_DIMS];
        vscale(rotated_velocity, cos(rotation_angle), parcel->v[new_state_index]);

        sPARTICLE_VAR axis_cross_velocity[N_SPACE_DIMS];
        vcross(axis_cross_velocity, axis_of_rotation, parcel->v[new_state_index]);
        vmac(rotated_velocity, sin(rotation_angle), axis_cross_velocity);

        sPARTICLE_VAR axis_dot_velocity = vdot(axis_of_rotation, parcel->v[new_state_index]);
        vmac(rotated_velocity, axis_dot_velocity * (1- cos(rotation_angle)), axis_of_rotation);

        vcopy(parcel->v[new_state_index], rotated_velocity);
        break;
      }
    }
    return found_a_surfel;
  }
  return FALSE;
}
