/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * SIM data structure
 *
 * The SIM data structure houses low-level data global to the simulation.
 *
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SIM_H
#define _SIMENG_SIM_H

#include <vector>
#include "common_sp.h"
#include "lattice.h"
#include "shob.h"

#include "eqns.h"
#include "simerr.h"

#include "eos.h"
#include "gradient_5g.h"
#include TURB_SYNTH_SIM_H
#include "turb_synth_info.h"
#include "timescale.h"
#include "transient_boundary_seeding.h"
#include <memory>
#include "porous_rock.h"
#include "data_offset_table_opts.h"
#include "radiation.h"

#if BUILD_5G_LATTICE
struct sFLUID_PROPERTIES;
extern sFLUID_PROPERTIES *g_mc_types;
extern iGRADIENT_OP      *g_gradient_operator;
#endif

#if BUILD_D19_LATTICE
extern sCDI_LIQUID_PARAMS *g_lpms; //liquid parameters, determined by liquid type
#endif

struct sSCALAR_MATERIAL;
extern sSCALAR_MATERIAL *g_scalar_materials;

/*--------------------------------------------------------------------------*
 * Simulation State Structure
 *--------------------------------------------------------------------------*/

// This structure holds ALL the information necessary to describe the
// state of the simulation on a particular SP.

typedef struct sASYNC_EVENT_FLAGS {

  BOOLEAN   timers_on_p;
  BOOLEAN   diags_on_p;
  sINT32    diag_avg;
  BOOLEAN   exit_p;
} *ASYNC_EVENT_FLAGS;

VOID sim_compute_lrf_angular_vel_terms(LRF_PHYSICS_DESCRIPTOR lrf, const BASETIME &time);

VOID sim_update_all_containing_lrf_axes_old(LRF_PHYSICS_DESCRIPTOR lrf, const BASETIME &time);

VOID sim_initialize_nested_lrfs();
__HOST__DEVICE__
VOID calculate_point_on_rotation_axis(dFLOAT ref_pt[3],
                                      dFLOAT ref_pt_vel[3], dFLOAT axis[3], dFLOAT angular_vel[3], 
				      dFLOAT point[3],           // result 
				      dFLOAT ref_pt_vel_par[3]); // result (component of ref pt vel parallel to angular vel)

VOID sim_update_local_ref_frames(asINT32 last_coarsest_active_scale, 
                                 asINT32 coarsest_active_scale,
                                 BOOLEAN single_pass,
                                 asINT32 time_inc,
                                 dFLOAT time_rate_factor,
                                 cBOOLEAN is_initial_update_after_smart_seed);

// TODO: This source file may not be the best place for general matrix-matrix multiply
template <typename FLOAT_TYPE_1, typename FLOAT_TYPE_2, typename FLOAT_TYPE_3, typename FLOAT_TYPE_4>
VOID calculate_matrix_matrix_product(FLOAT_TYPE_1 C[3][3], const FLOAT_TYPE_2 A[3][3], const FLOAT_TYPE_3 B[3][3]);

template<typename FLOAT_TYPE>
VOID rotate_velocity_between_csys(FLOAT_TYPE input_vel[3], 
                                  FLOAT_TYPE output_vel[3], 
                                  LRF_PHYSICS_DESCRIPTOR lrf,
                                  BOOLEAN is_inverse);



template<typename FLOAT_TYPE>
__HOST__DEVICE__
VOID convert_vel_from_local_to_global_frame(FLOAT_TYPE local_vel[3],
                                            FLOAT_TYPE surfel_centroid[3],
                                            FLOAT_TYPE global_vel[3],
                                            LRF_PHYSICS_DESCRIPTOR lrf);

template<typename FLOAT_TYPE>
__HOST__DEVICE__
VOID convert_vel_from_global_to_local_frame(FLOAT_TYPE global_vel[3],
                                            FLOAT_TYPE surfel_centroid[3],
                                            FLOAT_TYPE local_vel[3],
                                            LRF_PHYSICS_DESCRIPTOR lrf,
                                            cBOOLEAN is_global_vel_in_local_csys = FALSE);


const BOOLEAN PRIOR_TIMESTEP = 0;
const BOOLEAN NEXT_TIMESTEP = 1;


// The entire contents of this struct is checkpointed so no pointers allowed
typedef struct sGRF {
  cBOOLEAN              is_defined;
  cBOOLEAN              is_time_varying;
  cBOOLEAN		is_rotation;
  sINT8                 axis_change_mark;           /* Alternates between 0 and 1 if axis changes */
  sINT8                 ref_pt_vel_dir_change_mark; /* Alternates between 0 and 1 if vel dir changes */

  dFLOAT 		ref_point[3];

  // Per-scale global ref frame time derivitive of angular_vel averaged over appropriate 
  // number of fine timesteps.
  dFLOAT		domega[STP_MAX_SCALES][3];	/* time derivitive of angular_vel */
  dFLOAT		linear_accel[3]; 	/* LatticeVelocity/timestep */
  dFLOAT		angular_vel[3];	/* rad/timestep */
  ///////////////////
  dFLOAT		delta_u_of_scale[STP_MAX_SCALES][3];	/* vel increment of scale per timestep */
  dFLOAT		mean_delta_u_of_scale[STP_MAX_SCALES][3];	/* vel increment of scale per timestep */
  dFLOAT		delta_u_of_scale_prior[STP_MAX_SCALES][3];	/* vel increment of scale per timestep */
  dFLOAT		mean_delta_u_of_scale_prior[STP_MAX_SCALES][3];	/* vel increment of scale per timestep */
  dFLOAT		linear_accel0[3]; 	/* dU/dt at time t=0, LatticeVelocity/timestep */
  dFLOAT		linear_accel_m1[3]; 	/* dU/dt at previous timestep, LatticeVelocity/timestep */
  ///////////////////
  dFLOAT 		ref_pt_vel[3];	/* vel at time t (LatticeVelocity) */
  dFLOAT 		ref_pt_vel1[3];	/* vel at time t+1 (LatticeVelocity) */
  dFLOAT 		angular_vel1[3];	/* angular vel at time t+1 (rad/timestep) */

  dFLOAT 		linear_accel1[3];	/* dU/dt at time t+1 (LatticeVelocity/timestep) */
  dFLOAT                last_axis[3];
  dFLOAT                last_pt_on_axis[3];
  dFLOAT                last_ref_vel_dir[3];
  dFLOAT                angle_rotated;      /* angle rotated about axis - only relevant if axis is fixed */

  /*	This quaternion describes the cumulative rotation of the ground-fixed frame
   *	relative to the body-fixed frame, which is fixed in simulation.
   */
  dFLOAT 		quaternion[4];
  dFLOAT 		quaternion_inverse[4];

  sBG_TRANSFORM3d       ground_to_global_xform;
  
  /* accumulated shift due to linear vel at time t (relative to global frame but ground csys) */
  dFLOAT                ground_to_global_translation[3]; 

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr, size_t n_bytes,
				      const sGRF* h_ptr);
  
} *GRF;

VOID sim_update_global_ref_frame(asINT32 last_coarsest_active_scale,
                                 asINT32 coarsest_active_scale,
                                 GRF grf,
                                 BASETIME sim_time);

VOID sim_predict_global_ref_frame(asINT32 last_coarsest_active_scale,
                                 asINT32 coarsest_active_scale,
                                 BASETIME sim_time);

// The entire contents of this struct is checkpointed so no pointers allowed
typedef struct sTHERMAL_ACCEL_INFO {
  TIMESTEP start;
  TIMESTEP period;
  TIMESTEP repeat;
  TIMESTEP interval;
  cBOOLEAN is_hacked;      //TRUE if set theraml accel vars through exasignal
  TIMESTEP hacked_start;   //the start_time setted from exasignal
  TIMESTEP hacked_period;  //the period setted from exasignal
  TIMESTEP hacked_stop;    //the stop_time setted from exasignal
  cBOOLEAN acc_on;
  cBOOLEAN do_T_solver_switch;
  TIMESTEP T_solver_switch_time_for_surfel_recv_group;
  TIMESTEP T_solver_switch_time_for_ublk_recv_group;

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr, size_t n_bytes,
				      const sTHERMAL_ACCEL_INFO* h_ptr);

  TIMESTEP end_time() {
    return (start + (dFLOAT)(period*repeat));
  }

  BOOLEAN switch_back_to_lb_solver() {
    return (period > interval);
  }

  BOOLEAN timestep_within_thermal_acc(TIMESTEP timestep) {
    return (timestep >= start && timestep < end_time());
  }

  BOOLEAN switch_recv_group_solver(const BOOLEAN is_surfel_recv, const TIMESTEP prev_timestep, const TIMESTEP next_timestep) {
    // Check if the next timestep for a scale is outside thermal accelaration interval.
    return is_surfel_recv ? 
      (prev_timestep < T_solver_switch_time_for_surfel_recv_group && next_timestep >= T_solver_switch_time_for_surfel_recv_group)
      :
      (prev_timestep < T_solver_switch_time_for_ublk_recv_group && next_timestep >= T_solver_switch_time_for_ublk_recv_group);
  }

} *THERMAL_ACCEL_INFO;

struct sFREEZE_MOMENTUM_PARMS {
  cBOOLEAN  m_is_on;
  BASETIME  m_start_time;
  dFLOAT    m_thermal_timestep_ratio;

  sFREEZE_MOMENTUM_PARMS() {
    m_is_on = FALSE;

    m_start_time = -1;
    m_thermal_timestep_ratio = 1.0;
  }

    /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr, size_t n_bytes,
				      const sFREEZE_MOMENTUM_PARMS* h_ptr);
};
struct sSURFEL_TBS_INFO {
  asINT16  meas_index;
  cBOOLEAN is_tbs;
};

typedef struct sCALIBRATION_PARAMS_INFO {
  sINT32 face_index;
  sINT32 meas_window_index;
  sINT32 iterations;
  sINT32 iteration_number;
  sINT32 calib_period;
  sINT32 meas_period;
  BASETIME next_iteration_timestep;
  cBOOLEAN cancel_pressure_fluctuations;
  cBOOLEAN subtract_mean_velocity;
  cBOOLEAN reset_initial_condition;

  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr, size_t n_bytes,
				      const sCALIBRATION_PARAMS_INFO* h_ptr);
  
  sCALIBRATION_PARAMS_INFO() : iteration_number(-1) {}
  void init(DGF_RW_CALIBRATION_PARAMETERS_REC &record) {
    face_index = record.face_index;
    meas_window_index = record.meas_window_index;
    iterations = record.calibration_iterations;
    iteration_number = 1;
    meas_period = record.meas_interval;
    cancel_pressure_fluctuations = record.cancel_pressure_fluctuations;
    subtract_mean_velocity = record.subtract_mean_velocity;
    reset_initial_condition = record.reset_initial_condition;
    next_iteration_timestep = -10;
  }
  asINT32 time_history_index(asINT32 timestep, cBOOLEAN coarsest_even_odd) {
    if(coarsest_even_odd)
      return (timestep % calib_period) / (2 * meas_period);
    else
      return(timestep % calib_period) / meas_period;
  }
  asINT32 calib_size() {
    if(meas_period > 0)
      return calib_period/meas_period;
    else
      return 1;
  }

} CALIBRATION_PARAMS_INFO;

#ifdef CONDUCTION_HACKED_STAGGERED_COUPLING_FRAMEWORK
/** @brief Data structure owning all time coupling phases info */
typedef struct sTIME_COUPLING_INFO {
  std::vector<sTIME_COUPLING_PHASE> m_phases;
  BASETIME m_averaging_start_basetime;
  BASETIME m_averaging_end_basetime;
  SCALE m_coarsest_coupled_scale;
  
  VOID init(uINT32 num_phases, STP_SCALE coarsest_coupled_scale);

  const sTIME_COUPLING_PHASE& active_phase() { return m_phases[m_idx_active_phase]; }
  
  inline cBOOLEAN is_staggered() const { return m_is_staggered; }
  inline cBOOLEAN is_staggered(size_t idx_phase) const { 
    return (m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::Stagger); 
  }
  inline cBOOLEAN is_one_solver_frozen() const { return m_is_one_solver_frozen; }
  inline cBOOLEAN is_one_solver_frozen(size_t idx_phase) { 
    return ((m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::FreezeOne) || 
            (m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::Stagger));
  }
  inline cBOOLEAN is_flow_solver_frozen() const { return m_is_flow_solver_frozen; }
  inline cBOOLEAN is_flow_solver_frozen(size_t idx_phase) {
    return (is_one_solver_frozen() && (m_phases[idx_phase].frozen_solver == eCOUPLED_SOLVER::FlowSolver));
  }
  inline cBOOLEAN is_advancing_at_different_rate() const { return m_is_advancing_at_different_rate; }
  inline cBOOLEAN is_advancing_at_different_rate(size_t idx_phase) const { 
    return ((m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::DifferentRate) ||
            (m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::DifferentRateConservative)); 
  }
  inline cBOOLEAN is_conservative_across_realms() const { return m_is_conservative_across_realms; }
  inline cBOOLEAN is_conservative_across_realms(size_t idx_phase) { 
    return ((m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::SameRate) || 
            (m_phases[idx_phase].time_coupling == eTIME_COUPLING_SCHEME::DifferentRateConservative));
  }
  
  size_t idx_this_phase() { return m_idx_active_phase; }
  size_t idx_next_phase() { return m_idx_next_phase; }
  sTIME_COUPLING_PHASE* next_phase() { 
    return (m_idx_next_phase == m_phases.size()) ? nullptr : &m_phases[m_idx_next_phase]; 
  }
  sTIME_COUPLING_PHASE* next_unfrozen_phase();
  cBOOLEAN maybe_update_time_coupling_phase();
  BASETIME next_phase_start(BASETIME timestep_last);
  VOID update_phase_averaging_start_end();
  VOID update_staggered_averaging_start_end();

  cBOOLEAN compute_average(const asINT32 scale) const;

  VOID set_phase_flags();

 private:
  size_t m_idx_active_phase;
  size_t m_idx_next_phase;

  //To improve performance, cash the phase type flags since these checks are done per surfel in each timestep
  BOOLEAN m_is_staggered;
  BOOLEAN m_is_one_solver_frozen;
  BOOLEAN m_is_flow_solver_frozen;
  BOOLEAN m_is_advancing_at_different_rate;
  BOOLEAN m_is_conservative_across_realms;

} *TIME_COUPLING_INFO;
#endif

class sTAGGED_SPLIT_FACTOR;

/*=================================================================================================
 * @struct sSIM_INFO_CONSTANTS
 * This struct MUST contain trivial datatypes to be directly portable to GPU constant memory
 * This portion of the simulation info is common to both the CPU and GPU codebase
 *================================================================================================*/
struct sSIM_INFO_CONSTANTS {
  sINT32		cdi_major_version;
  sINT32		cdi_minor_version;
  cBOOLEAN		initialization_complete_p;

  BASETIME              timers_on_timestep;
  BASETIME              timers_off_timestep;

  BASETIME	        next_ckpt_time;
  
  cBOOLEAN              event_reply_pending_p;

  /*    Regularly scheduled checkpoints */
  /*    These elements describe when the next regularly scheduled 
   *    checkpoint should occur.  When the SP has sent a checkpoint to the
   *    CP, the SP copies next_ckpt_time to current_ckpt_time and sets 
   *    next_ckpt_time to BASETIME_INVALID. The CP updates next_ckpt_time
   *    after the checkpoint  is output to a file. */
  BASETIME		current_ckpt_time;

  /**** Scale and Dimensionality Information ****/
  /*    Number of scales of resolution in the simulation. */
  /*    Finest scale of resolution is equal to this number (coarsest scale */
  /*    is always equal to 0) */ 
  SCALE 		num_scales;

  STP_PROC              n_dest_sps;

  /*    2D vs 3D */
  sINT8			num_dims;
  ACTIVE_SOLVER_MASK    init_solver_mask;

  cBOOLEAN              is_lb_model;
  cBOOLEAN              is_flow;      // is_flow and is_lb_model should probably be folded together
  cBOOLEAN              is_turb_model;
  cBOOLEAN              is_heat_transfer;
  cBOOLEAN              is_double_precision;
  cBOOLEAN              is_scalar_model;
  cBOOLEAN              is_pf_model;
  cBOOLEAN              is_conduction_model;
  cBOOLEAN              is_radiation_model;
  cBOOLEAN              is_shell_conduction_model;
  cBOOLEAN              has_avg_mme_window;
  cBOOLEAN              is_ke_super_cycling;
  BASETIME              avg_mme_period;
  BASETIME              avg_mme_interval;
  BASETIME              avg_mme_duration;
  sINT32                n_user_defined_scalars;
  sINT32                sri_vars_per_uds;
  sINT32                first_uds_sri_variable;
  sINT32                last_uds_sri_variable;
  cBOOLEAN              is_high_subsonic_mach_regime;
  cBOOLEAN              is_high_subsonic_HT_off;
  cBOOLEAN              is_HSExt_solver;
  cBOOLEAN              is_hydrogen_eos;
  dFLOAT                hydrogen_beta_fac;
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  cBOOLEAN              is_accretion_simulation;
  cBOOLEAN              is_particle_model;
  cBOOLEAN              is_film_solver;
  cBOOLEAN              is_thermal_particle_solver;
  cBOOLEAN              is_particle_feedback;
  cBOOLEAN              is_particle_breakup;
  cBOOLEAN              is_particle_splash;
  cBOOLEAN              enable_film_trajectory_measurement;
//#endif  
  cBOOLEAN              is_large_pore; //5G large_pore
  cBOOLEAN              enable_dcache_pm_storage;
  cBOOLEAN		is_t_timestep_smaller_than_lb_timestep;
  cBOOLEAN		accum_energy_meas_in_t_solver; /* sim.is_t_timestep_smaller_than_lb_timestep && sim.thermal_feedback_is_on */
  cBOOLEAN		is_internal_flow;
  cBOOLEAN              debug_malloc_p;
  cBOOLEAN		do_smart_seed;
  cBOOLEAN		extrapolate_seed;
  cBOOLEAN		smart_seed_boundaries;
  cBOOLEAN		smart_seed_contact_angle;
  cBOOLEAN		local_vel_freeze;
  cBOOLEAN              need_to_scale_viscosity;
  cBOOLEAN		store_frozen_vars;
  cBOOLEAN		fixed_temp_walls;		/* Fixed temp walls for a "isothermal" sim ? */
  cBOOLEAN		thermal_feedback_is_on;
  cBOOLEAN		create_full_ckpts;		/* As opposed to MME ckpts */
  cBOOLEAN		no_swirl_model;
  cBOOLEAN		is_fluid_turb_solver_1;
  cBOOLEAN		is_some_htc_meas_window;	/* If so, collect eddy visc onto surfels */
  cBOOLEAN		is_some_div_u_meas_window;	/* If so, collect a few items in fluid */
  cBOOLEAN		user_specified_dns_nu_over_t;
  cBOOLEAN		is_gravity;
  cBOOLEAN		is_buoyancy;
  cBOOLEAN              is_global_body_force;
  cBOOLEAN		is_user_max_vel_default;
  cBOOLEAN		use_local_vel_fan_model;
  cBOOLEAN              is_incompressible_solver;
  cBOOLEAN              is_5g_sim;
  cBOOLEAN              is_transonic_sim;
  cBOOLEAN              is_liquid;
  cBOOLEAN              is_temp_dependent_liquid;
  cBOOLEAN              use_temp_dep_gamma;
  cBOOLEAN              is_mean_vel_added;
  asINT32               n_seed_from_meas_descs; /* boundary seeding via sample surface measurement */

  //uds
  cBOOLEAN              disable_scalar_diffusivity_bound;

  //cooling air leakage model for Porous Media
  sdFLOAT               pm_force_factor;
  STP_COORD             simvol_size[3];

  dFLOAT		gravity[3];
  dFLOAT		buoyancy_ref_temp;
  dFLOAT		buoyancy_thermal_exp_coeff;

  ssize_t		total_fluid_like_voxels;

  BASETIME		ignore_vel_n_timesteps;
  BASETIME		ignore_temp_n_timesteps;
  BASETIME		ignore_uds_n_timesteps;
  STP_PHYS_VARIABLE      user_max_vel;
  STP_PHYS_VARIABLE      user_max_vel_sqrd;
  STP_PHYS_VARIABLE      user_max_temp;
  STP_PHYS_VARIABLE      user_min_temp;
  STP_PHYS_VARIABLE	 sim_max_temp;
  STP_PHYS_VARIABLE      sim_min_temp;
#if BUILD_5G_LATTICE
  STP_PHYS_VARIABLE	 sim_max_uds;
  STP_PHYS_VARIABLE      sim_min_uds;
#endif
  STP_PHYS_VARIABLE      max_surfel_bc_vel_default;
  STP_PHYS_VARIABLE      max_surfel_bc_vel_default_sqrd;
  STP_PHYS_VARIABLE      default_max_vel;
  STP_PHYS_VARIABLE      max_surfel_bc_vel_sqrd;
  STP_PHYS_VARIABLE      max_bc_mass_flux;
  STP_PHYS_VARIABLE      max_bc_mass_flux_sqrd;
  STP_PHYS_VARIABLE      max_moving_surface_vel;
  STP_PHYS_VARIABLE      max_moving_surface_vel_sqrd;
  STP_PHYS_VARIABLE      min_bc_pressure;
  sdFLOAT                char_vel_over_char_len;
  sdFLOAT                one_over_mean_temp;
  sdFLOAT                one_over_char_temp;
  sdFLOAT                char_density_f;
  sdFLOAT                char_temp_f;
  sdFLOAT                char_uds_f;
  sdFLOAT                char_pressure_f;
  sdFLOAT                one_over_char_vel;
  sdFLOAT		 one_over_300k_lat;
  sdFLOAT		 one_over_273k_lat;
  sdFLOAT		 one_over_1k_lat;
  sdFLOAT		 one_k_lat;
  sdFLOAT                rho_kg_m3;
  sdFLOAT                one_over_rho_water;

  sdFLOAT                B_new_twf;
  sdFLOAT                c_temp;
  sdFLOAT                cold_fluid_Prandtl;
  sdFLOAT		one_over_gc;
  sdFLOAT		fluid_Prandtl_number;
  dFLOAT		char_pressure;
  dFLOAT		char_uds;
  dFLOAT		char_temp;
  dFLOAT                mean_temp;
  dFLOAT		lb_char_temp;
  dFLOAT		char_density;
  dFLOAT                char_vel;
  dFLOAT		sqrt_4_char_vel;
  dFLOAT		char_length;
  dFLOAT                reynolds_number;
  dFLOAT		turb_Prandtl_number;
  cBOOLEAN              switch_acous_during_simulation;
  cBOOLEAN              is_full_checkpoint_restore;
  cBOOLEAN              is_mme_checkpoint_restore;
  dFLOAT		meters_per_cell;
  dFLOAT                mks_vel_scaling;
  dFLOAT                mks_density_scaling;
  dFLOAT                one_mps_lattice;
  dFLOAT		one_over_mach_0_sqrd;
  dFLOAT		one_over_mach_0_4th;
  dFLOAT		dns_nu_over_t_floor;
  sINT32                major_flow_direction;

  //conductivity scale from W/mK to LatticeThermCond
  dFLOAT		scale_conductivity;

  //use hs lb_entropy solver
  cBOOLEAN              use_hs_lb_entropy_solver;

  //use lb_energy solver
  cBOOLEAN              use_lb_energy_solver;
  cBOOLEAN              is_legacy_energy_solver;

  //hybrid ts-hs solver
  cBOOLEAN              use_hybrid_ts_hs_solver;

//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  dFLOAT               kilos_per_particle;
//#endif
  dFLOAT                char_entropy;
  dFLOAT                C_p;
  dFLOAT                C_v;
  dFLOAT                one_over_C_p;

  TIMESTEP              T_lb_scalar_solver_start_time;
  cBOOLEAN              is_prescribed_T_solver_switch;
  cBOOLEAN              prepare_calibration_run;
  T_SOLVER_TYPE         T_solver_type;
  UDS_SOLVER_TYPE	uds_solver_type;
  dFLOAT                lattice_gamma;
  dFLOAT                lattice_gas_const;
  dFLOAT                one_over_lattice_gas_const;
  dFLOAT                one_minus_lattice_gas_const;
  dFLOAT                stefan_boltzmann_const;
#if BUILD_5G_LATTICE
  sINT32                num_components;
  cBOOLEAN              is_non_ideal_gas;
  UDS_CARRIER           uds_carriers[MAX_N_USER_DEFINED_SCALARS];
#endif

#if BUILD_D19_LATTICE
  //liquid
  sdFLOAT                nu_0;
  sdFLOAT                nu_300;
#endif
  
  // water vapor transport
  cBOOLEAN              is_water_vapor_transport;
  dFLOAT                film_acceleration_factor;

  dFLOAT                 case_origin[3];
  
  sINT32                 n_csys;


  // for rotational dynamics
  sINT32                 n_rotational_dynamics_descs;  

  // total counts for all subtypes of each shob, including ghosts
  UBLK_ID               n_total_ublks[STP_N_REALMS];
  SURFEL_ID             n_total_surfels[STP_N_REALMS];
  UBLK_ID               n_nonghost_ublks[STP_N_REALMS];
  BSURFEL_ID            n_total_bsurfels;
  
  // output prescribed quantities in surface measurement files
  cBOOLEAN              is_meas_prescribed;
  cBOOLEAN              is_rwnc_calibration_test_run; // If true then the last iteration of realistic wind calibration run is with mean added.
  asINT32               n_transient_seeded_boundary_surfels;
  sINT32	        n_movb_physics_descs;
  sINT16                n_volume_physics_descs;
  sINT16                n_fluid_physics_descs;
  sINT16                n_flow_surface_physics_descs;
  sINT16                n_thermal_surface_physics_descs;
  sINT16                n_shell_config_physics_descs;

  //melting solver
  cBOOLEAN              use_melting_solver;
  cBOOLEAN              is_temp_dependent_AdBlue;
  sdFLOAT               cp_liq_over_cp_sol;
  sdFLOAT               cp_sol_over_cp_liq;
  dFLOAT                T_melt;
  dFLOAT                T_melt_up;
  dFLOAT                T_boil;
  sdFLOAT               A_coef_melt;
  
  cBOOLEAN              enable_tangential_shell_conduction;
  cBOOLEAN              is_conduction_sp;
  cBOOLEAN              use_implicit_shell_solver;
  cBOOLEAN              constant_implicit_shell_solver_matrix;
  cBOOLEAN              use_implicit_solid_solver;

  /* CONST METHODS APPLICABLE TO BOTH CPU AND GPU
  * Note that this sSIM_INFO_BASE  is allocated in constant memory on GPUs
  * and therefore any methods that will modify a data member will lead to
  * a segfault. All methods have to be CONST.*/
  
  __HOST__DEVICE__  BOOLEAN is_2d() const { return num_dims == 2; }
  __HOST__DEVICE__  BOOLEAN is_3d() const { return num_dims == 3; }
  
  __HOST__DEVICE__ bool realm_has_no_ublks_or_surfels(STP_REALM realm) 
  {
    return n_total_ublks[realm] == 0 && n_total_surfels[realm] == 0;
  }
  
  __HOST__DEVICE__ bool has_no_ublks_or_surfels()
  {
    return realm_has_no_ublks_or_surfels(STP_FLOW_REALM) && realm_has_no_ublks_or_surfels(STP_COND_REALM);
  }

  __HOST__DEVICE__ asINT32 smallest_scale_number(VOID) const  { return COARSEST_SCALE; }
  __HOST__DEVICE__ asINT32 largest_scale_number(VOID) const { return num_scales - 1; }
  __HOST__DEVICE__ asINT32 coarsest_scale(VOID) const { return COARSEST_SCALE; }
  __HOST__DEVICE__ asINT32 finest_scale(VOID)  const { return num_scales - 1; }

  __HOST__DEVICE__ BOOLEAN is_scale_in_range(asINT32 scale) const
  {
    return (scale >= COARSEST_SCALE) && (scale < num_scales);
  }

  __HOST__DEVICE__ bool is_T_S_solver_type_lb() const {
    return (T_solver_type == LB_TEMPERATURE || T_solver_type == LB_ENTROPY || T_solver_type == LB_ENERGY);
  }  

  __HOST__DEVICE__ bool is_movb_sim() const {
    return (n_movb_physics_descs > 0);
  }

  __HOST__DEVICE__ bool is_mme_comm_needed() const {
    return (is_5g_sim || is_transonic_sim || is_movb_sim() || is_hydrogen_eos || is_HSExt_solver || is_pf_model);
  }
  
};

typedef struct sSIM_INFO : public sSIM_INFO_CONSTANTS {

  /* Constructor (at program startup) simply clear SIM structure */
  sSIM_INFO();
  sASYNC_EVENT_FLAGS    async_event_flags;
  sFLOAT                **seed_from_meas_scale_factors;
  CSYS                   csys_table; // vector of coord systems
  sCALIBRATION_PARAMS_INFO calibration_params;
  sTHERMAL_ACCEL_INFO   thermal_accel;
  sGRF                  grf;
  sLRF_PHYSICS_DESCRIPTOR* lrf_physics_descs;
  sINT32 		n_lrf_physics_descs;  
  SRI_CUSTOM_VAR_ID_HELPER cvid_helper;       //custom_var_id_helper
  cDGF_CONTROL          control_record;
  sSEED_CONTROL         *m_seed_from_meas_controls;
  std::vector<sSP_TRANSIENT_BOUNDARY_SEEDING> transient_boundary_seeding;
  // related to surface coupling
  sINT32                 n_coupling_models; // number of coupling models
  COUPLING_MODEL         coupling_models; // vector of surface coupling models
  // Turbulence quantities were already scaled by dimless properties in the CP. The SP is
  // responsible for seeding pressure and velocity by dimless properties. 
  std::vector<sSEED_CONTROL> m_seed_control;

  // Pointers to the contained physics descriptors are retained throughout the simulator, so do not use
  // std::vector because it may move the contained objects.
  PHYSICS_DESCRIPTOR    volume_physics_descs;
  PHYSICS_DESCRIPTOR    flow_surface_physics_descs;
  PHYSICS_DESCRIPTOR    thermal_surface_physics_descs;
  sSHELL_CONFIG_PHYSICS_DESCRIPTOR   *shell_config_physics_descs;

  std::vector<sRADIATION_SURFACE_CONDITION> radiation_surface_conditions;

  // for 5G large_pore
  sINT32 num_rock_types;
  cPOROUS_ROCK * rock_types;

  sMOVB_PHYSICS_DESCRIPTOR*  movb_physics_descs;
  sSURFEL_TBS_INFO     surfel_tbs_info;
  sSIM_TIRE* tires;
  sINT32 n_tires;

  GRAVITY_BUOYANCY_PHYSICS_DESCRIPTOR gravity_buoyancy_desc;
  GRF_PHYSICS_DESCRIPTOR              grf_physics_desc;
  BODY_FORCE_PHYSICS_DESCRIPTOR       global_body_force_desc;

  BODY_FORCE_PHYSICS_DESCRIPTOR       body_force_descs;
  sINT32                              n_body_force_descs;
  
  //momentum freeze solver
  sFREEZE_MOMENTUM_PARMS m_freeze_momentum_parms;

  // For powertherm time information
  cBOOLEAN              is_ptherm_time_input_to_programs;
  sPT_TIME_INFO         m_pt_time_info;
  sPT_TIME_INFO         m_new_pt_time_info;
  
  bool is_momentum_freeze_on() const {
    return m_freeze_momentum_parms.m_is_on;
  }    
  // buffer to store the data for the largest coupling model
  // max(nvars*nsurfels) over all coupling models
  sFLOAT*                coupling_data_buf;
  
  ROTATIONAL_DYNAMICS_DESC rotational_dynamics_descs;

  TABLE                  tables;			// vector of data tables
  sINT32                 n_tables;  

  SHOB_ID                n_boundary_surfels; // inlet/outlet - used for initial print msg
  SHOB_ID                n_seeded_boundary_surfels;
  
  /* ### PLEASE READ ###
   * If you add any new members that are not integral types, make
   * sure to look at the implementation of the copy method below
   * and make necessary updates to avoid illegal memory accesses on
   * the GPU
   */  
  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr, size_t n_bytes, const sSIM_INFO* h_ptr);  
    
  LRF_PHYSICS_DESCRIPTOR ref_frame_index_to_lrf(asINT32 ref_frame_index) {
    return &lrf_physics_descs[ref_frame_index];
  }

  sSIM_TIRE* maybe_get_deforming_tire(sINT32 deforming_tire_index) {
    cassert(deforming_tire_index >= -1);
    cassert(deforming_tire_index < n_tires);
    return deforming_tire_index == -1 ? nullptr : &this->tires[deforming_tire_index];
  }

#ifdef CONDUCTION_HACKED_STAGGERED_COUPLING_FRAMEWORK
  sTIME_COUPLING_INFO time_coupling_info;
#endif
  sREALM_PHASE_TIME_INFO realm_phase_time_info[STP_N_REALMS];

  VOID init(asINT32 n_scales, STP_COORD simv_size[3]) 
  {
    num_scales = n_scales;
    ccDOTIMES(i, 3)
      simvol_size[i] = simv_size[i];

    /* Miscellaneous initializations */
    current_ckpt_time = BASETIME_INVALID;
    switch_acous_during_simulation = FALSE;

    next_ckpt_time = BASETIME_INVALID;

    async_event_flags.timers_on_p = FALSE;
    async_event_flags.diag_avg = 10000000;
    async_event_flags.diags_on_p = FALSE;

    event_reply_pending_p = FALSE;

    n_seed_from_meas_descs = 0;
    seed_from_meas_scale_factors = NULL;
  }

  VOID set_acous_switch();

#if BUILD_D19_LATTICE
  VOID set_T_solver_attributes();
#endif

  VOID adjust_uds_solver_parameters();

  VOID set_user_max_vel(dFLOAT maxvel, BOOLEAN is_default) {
    if (is_default) {
      user_max_vel = maxvel;
      is_user_max_vel_default = TRUE;
      max_surfel_bc_vel_sqrd = max_surfel_bc_vel_default_sqrd;
    } else {
      user_max_vel = maxvel;
      is_user_max_vel_default = FALSE;
      max_surfel_bc_vel_sqrd = MIN(max_surfel_bc_vel_default, user_max_vel);
      max_surfel_bc_vel_sqrd = max_surfel_bc_vel_sqrd * max_surfel_bc_vel_sqrd;
    }
    user_max_vel_sqrd = user_max_vel * user_max_vel;
  }
    
  VOID init_lattice();

  VOID update_ref_frames(asINT32 last_coarsest_active_scale, asINT32 coarsest_active_scale);

  VOID update_movb_xforms();

  size_t ckpt_lrf_state_len();
  template< class S >
  size_t ckpt_state_len()
  { 
    size_t len = sizeof(size_t);
    
    if constexpr (std::is_same<S, MOVB_PHYSICS_DESCRIPTOR>())
    {
      if (n_movb_physics_descs > 0) {
        MOVB_PHYSICS_DESCRIPTOR movb = movb_physics_descs;
        ccDOTIMES(i, n_movb_physics_descs) {
        len += sizeof(movb->initial_angle_rotated);
        len += sizeof(movb->angle_rotated);
        len += sizeof(movb->motion_xform);
        movb++;
        }
      }
    }
    else if constexpr (std::is_same<S, sGRF>())
    {
      if (grf.is_defined)  len += sizeof(sGRF);
    }
    else if constexpr (std::is_same<S, sLRF_PHYSICS_DESCRIPTOR>())
    {
      len += ckpt_lrf_state_len();
    }
    else if constexpr (std::is_same<S, sTHERMAL_ACCEL_INFO>())
    {
      if (is_heat_transfer) len += sizeof(sTHERMAL_ACCEL_INFO);
    }
    return len;
  }

  VOID ckpt_movb_state();
  VOID ckpt_movb_state(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt_movb_state();

  VOID ckpt_grf_state();
  VOID ckpt_grf_state(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt_grf_state();

  VOID ckpt_lrf_state();
  VOID ckpt_lrf_state(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt_lrf_state();

  VOID ckpt_thermal_accel_state();
  VOID ckpt_thermal_accel_state(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt_thermal_accel_state();

  VOID ckpt_rotdyn_state();
  VOID read_ckpt_rotdyn_state();
  
  const sSIM_INFO_CONSTANTS* c() const {
    return this;
  }

  sSIM_INFO_CONSTANTS* c() {
    return this;
  }  

  cBOOLEAN use_implicit_shell_solver;
  cBOOLEAN use_implicit_solid_solver;
  sINT64 num_implicit_shell_solver_states;
  sINT64 num_implicit_solid_solver_states;
  std::vector<int> implicit_shell_solver_state_index_offset;
  std::vector<int> implicit_solid_solver_state_index_offset;

} *SIM_INFO;


DATA_OFFSET_TABLE_OPTS get_sim_ublk_surfel_offset_table_opts();


DATA_OFFSET_TABLE_OPTS get_sim_ublk_surfel_offset_table_opts();

/*--------------------------------------------------------------------------*
 * Global Simulation State
 *--------------------------------------------------------------------------*/

/* This one global variable describes the entire state of the simulation. */
extern sSIM_INFO sim;

#include "gpu_sim.hcu"

#define make_g3_vec_from_vec(v) g3_vec_make(v[0], v[1], v[2])
#define make_g3_point_from_point(v) g3_point_make(v[0], v[1], v[2])

__HOST__DEVICE__
inline VOID eqn_transform_vector(dFLOAT input_vector[3],
				 dFLOAT csys_index,
				 sdFLOAT output_vector[3])
{
  auto& sim = get_sim_ref();
  // Assume that CSYS 0 is the default csys, which has no rotation.
  // This is verified during initialization.
  if (is_csys_index_non_default(csys_index)) {
    sG3_VEC prescribed_vector = make_g3_vec_from_vec(input_vector);
    CSYS csys = &sim.csys_table[(asINT32)csys_index];
    prescribed_vector = g3_xform_vec(prescribed_vector,
                                     csys->l_to_g_xform);
    output_vector[0] = prescribed_vector.vcoord[0];
    output_vector[1] = prescribed_vector.vcoord[1];
    output_vector[2] = prescribed_vector.vcoord[2];
  } else {
    output_vector[0] = input_vector[0];
    output_vector[1] = input_vector[1];
    output_vector[2] = input_vector[2];
  }
}

__HOST__DEVICE__
inline VOID eqn_transform_vector(sPHYSICS_VARIABLE input_var[3],
				 PHYSICS_VARIABLE coord_sys,
				 sdFLOAT output_vector[3])
{
  dFLOAT input_vector[3];
  input_vector[0] = input_var[0].value;
  input_vector[1] = input_var[1].value;
  input_vector[2] = input_var[2].value;
  eqn_transform_vector(input_vector, coord_sys->value, output_vector);
}

template<typename T, size_t N>
__HOST__DEVICE__
inline VOID eqn_transform_vector(dFLOAT input_vector[3],
				 dFLOAT coord_sys,
				 tSCALAR_OR_ARRAY<T, N> (&output_array) [3],
                                 asINT32 index) {
#if !GPU_COMPILER
    eqn_transform_vector(input_vector, coord_sys, cast_as_regular_array(output_array));    
#else
    sdFLOAT output[3];
    v_copy(output, output_array, index);
    eqn_transform_vector(input_vector, coord_sys, output);
    v_copy(output_array, index, output);
#endif
}

template<typename T, size_t N>
__HOST__DEVICE__
inline VOID eqn_transform_vector(sPHYSICS_VARIABLE input_var[3],
				 PHYSICS_VARIABLE coord_sys,
				 tSCALAR_OR_ARRAY<T, N> (&output_array) [3],
                                 asINT32 index) {
  dFLOAT input_vector[3];
  input_vector[0] = input_var[0].value;
  input_vector[1] = input_var[1].value;
  input_vector[2] = input_var[2].value;
  eqn_transform_vector(input_vector, coord_sys->value, output_array, index);
}

__HOST__DEVICE__
inline VOID normal_velocity_to_components(dFLOAT normal_vel,
                                          STP_PHYS_VARIABLE velocity[3],
                                          STP_GEOM_VARIABLE normal[3])
{
  velocity[0] = normal_vel * normal[0];
  velocity[1] = normal_vel * normal[1];
  velocity[2] = normal_vel * normal[2];
}

__HOST__DEVICE__
inline VOID normal_velocity_to_components(sPHYSICS_VARIABLE normal_vel,
                                          STP_PHYS_VARIABLE velocity[3],
                                          STP_GEOM_VARIABLE normal[3])
{
  normal_velocity_to_components(normal_vel.value, velocity, normal);
}

template<typename T, size_t N>
__HOST__DEVICE__
inline VOID normal_velocity_to_components(dFLOAT normal_vel,
                                          tSFL_VAR<T, N> (&velocity) [3],
                                          const tSFL_VAR<T, N> (&normal)[3],
                                          asINT32 soxor)
{
  velocity[0][soxor] = normal_vel * normal[0][soxor];
  velocity[1][soxor] = normal_vel * normal[1][soxor];
  velocity[2][soxor] = normal_vel * normal[2][soxor];
}

template<typename T, size_t N>
__HOST__DEVICE__
inline VOID normal_velocity_to_components(sPHYSICS_VARIABLE normal_vel,
                                          tSFL_VAR<T, N> (&velocity) [3],
                                          const tSFL_VAR<T, N> (&normal)[3],
                                          asINT32 soxor)
{
  normal_velocity_to_components(normal_vel.value, velocity, normal,soxor);
}

// In the simulator, we want the local ref frames indices to be based at 0.
// CDI_GLOBAL_REF_FRAME_INDEX = 0  and CDI_GROUND_REF_FRAME_INDEX = 1 
// SRI_GLOBAL_REF_FRAME_INDEX = -1 and SRI_GROUND_REF_FRAME_INDEX = -2
// switching them to be consistent from this point on
INLINE sINT8 translate_cdi_ref_frame_index(sINT8 cdi_ref_frame_index) {
  sINT8 ref_frame_index = cdi_ref_frame_index - cdi_local_ref_frame_index(0);
  if (ref_frame_index < 0)
    return (SRI_GLOBAL_REF_FRAME_INDEX + SRI_GROUND_REF_FRAME_INDEX - ref_frame_index);
  else 
    return ref_frame_index;
}


#define is_ref_frame_local(ref_frame_index) ((ref_frame_index) >= 0)

// LRF index must fit in 8-bit signed int in ublk
#define MAX_LOCAL_REF_FRAMES   128

__HOST__DEVICE__
VOID vconvert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3], 
                                     subV3_8& point, 
                                     asINT32 voxel,
                                     STP_VEC_FLOAT output_vel[3],
                                     asINT32 from_ref_frame_index,
                                     asINT32 to_ref_frame_index);

__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    STP_VEC_FLOAT point[3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    cBOOLEAN is_input_vel_in_local_csys = FALSE);

template<size_t N>
__HOST__DEVICE__
VOID convert_vel_between_ref_frames(STP_VEC_FLOAT input_vel[3],
                                    tSFL_VAR<STP_VEC_FLOAT, N> (&point) [3],
                                    STP_VEC_FLOAT output_vel[3],
                                    asINT32 from_ref_frame_index,
                                    asINT32 to_ref_frame_index,
                                    asINT32 soxor,
                                    cBOOLEAN is_input_vel_in_local_csys = FALSE);

inline VOID convert_mflux_between_ref_frames(STP_VEC_FLOAT input_mass_flux[3], 
					     STP_VEC_FLOAT surfel_centroid[3], 
					     STP_VEC_FLOAT output_mass_flux[3], 
					     STP_PHYS_VARIABLE density,
					     asINT32 vel_ref_frame_index,
					     asINT32 surfel_ref_frame_index)
{
  STP_VEC_FLOAT input_vel[3];
  STP_VEC_FLOAT one_over_density = 1.0F / density;
  vscale(input_vel, one_over_density, input_mass_flux);

  convert_vel_between_ref_frames(input_vel, surfel_centroid, output_mass_flux, vel_ref_frame_index, surfel_ref_frame_index);
  vscale(output_mass_flux, density, output_mass_flux);
}

template <typename FLOAT_TYPE1, typename FLOAT_TYPE2, typename FLOAT_TYPE3>
__HOST__DEVICE__
VOID get_rotation_matrix_from_axis_angle(FLOAT_TYPE1 axis[3], FLOAT_TYPE2 angle_rotated,
                                         FLOAT_TYPE3 trans_matrix[3][3]);

typedef struct sNIRF_PARAMETERS {
  BOOLEAN is_rotation;

  dFLOAT ref_point[3];
  dFLOAT linear_accel[3];
  dFLOAT angular_vel[3];
  dFLOAT domega[3];
} *NIRF_PARAMETERS;


template <typename FLOAT_TYPE1, typename FLOAT_TYPE2, typename FLOAT_TYPE3>
__HOST__DEVICE__
VOID calculate_matrix_matrix_product(FLOAT_TYPE1 mat_out[3][3], FLOAT_TYPE2 A[3][3], FLOAT_TYPE3 B[3][3])
{
  dFLOAT C[3][3];
  memset(C, 0, sizeof(C));
  ccDOTIMES(i, 3) {
    ccDOTIMES(j, 3) {
      ccDOTIMES(k, 3) {
        C[i][j] += A[i][k] * B[k][j];
      }
    }
  }
  ccDOTIMES(i, 3) {
    ccDOTIMES(j, 3) {
      mat_out[i][j] = C[i][j];
    }
  }
}

// Consider reference frames L < C < B < G, where G is the global reference frame (assume inertial).
// Given frames/csys L, C, and G, we are given angular velocities: omega_LC^L, omega_CB^C and omega_BG^B.  We need to
// calculate omega_LG^L = omega_LC^L + omega_CB^L + omega_BG^L, which is the non-inertial angular velocity in local cys.
// Hence, we first need to construct omega_CB^L = R_CL omega_CB^C and omega_BG^L = R_CL R_BC omeag_BG^B.
// The final term omega_BG^L is not being correctly computed (see first rotate_vector).
__HOST__DEVICE__
inline VOID update_containing_lrfs_angular_vel(LRF_PHYSICS_DESCRIPTOR lrf,
                                               dFLOAT *angular_vel, dFLOAT *angular_vel1,
                                               dFLOAT *ref_point, dFLOAT *linear_accel ) {

  // Move reference point to a point on the axis of the local reference frame.
  // The reference point is the reference point of the innermost LRF
  vcopy(ref_point, lrf->point);
  vcopy(angular_vel, lrf->omega_total);
  vcopy(angular_vel1, lrf->omega_total1);
  vcopy(linear_accel, lrf->linear_accel);
}

__HOST__DEVICE__
inline VOID update_containing_lrfs_domega(LRF_PHYSICS_DESCRIPTOR lrf, dFLOAT *lrf_domega, asINT32 scale)
{
  ccDOTIMES(i, 3) {
    lrf_domega[i] = lrf->domega_total[3 * scale + i];
  }
}

__HOST__DEVICE__
inline VOID find_cumulative_nirf_parameters(LRF_PHYSICS_DESCRIPTOR lrf,
					    NIRF_PARAMETERS nirf,
					    asINT32 scale,
					    asINT32 group_scale,
					    asINT32 option)
{
  dFLOAT *ref_point = nirf->ref_point;
  dFLOAT *linear_accel = nirf->linear_accel;
  dFLOAT *angular_vel = nirf->angular_vel;
  dFLOAT *domega = nirf->domega;
  dFLOAT angular_vel1[3];
  BOOLEAN is_rotation = FALSE;
  auto& sim = get_sim_ref();
  
  if (sim.grf.is_defined) {
    dFLOAT grf_linear_accel_used[3] = {0.};
    switch (option) {
    case 1:
      vscale(grf_linear_accel_used, 2.0, sim.grf.mean_delta_u_of_scale_prior[group_scale]);
      break;
    case 2:
      vscale(grf_linear_accel_used, 2.0, sim.grf.mean_delta_u_of_scale[group_scale]);
      break;
    case 3:
      vadd(grf_linear_accel_used, sim.grf.mean_delta_u_of_scale_prior[group_scale], sim.grf.mean_delta_u_of_scale[group_scale]);
      break;
    case 4:
      vsub(grf_linear_accel_used, sim.grf.delta_u_of_scale_prior[group_scale], sim.grf.mean_delta_u_of_scale_prior[group_scale]);
      vscale(grf_linear_accel_used, 2.0, grf_linear_accel_used);
      break;
    default :
      vcopy(grf_linear_accel_used, sim.grf.linear_accel);
    }
    
    if (lrf == NULL) {
      is_rotation = sim.grf.is_rotation;
      vcopy(linear_accel, grf_linear_accel_used);
      vcopy(angular_vel,  sim.grf.angular_vel);
      vcopy(ref_point,    sim.grf.ref_point);
      // Copy in values averaged over the prior delta_t fine timesteps, where delta_t is
      // specific to scale.
      vcopy(domega, sim.grf.domega[scale]);
    } else {
      is_rotation = TRUE;
     
      update_containing_lrfs_angular_vel(lrf, angular_vel, angular_vel1, ref_point, linear_accel);
      vadd(angular_vel, sim.grf.angular_vel, lrf->angular_vel);

      if (lrf->domega) {
        dFLOAT lrf_domega[3]; vscale(lrf_domega, lrf->domega[scale], lrf->axis);
        update_containing_lrfs_domega(lrf, lrf_domega, scale);
        vadd(domega, sim.grf.domega[scale], lrf_domega);
        // If nested LRF axes are not collocated, then there are additional acceleration terms due to time-varying omega
        // and LRF reference point offsets
        dFLOAT *domega_linear_accel = &lrf->domega_linear_accel[scale];
        vadd(linear_accel, linear_accel, domega_linear_accel);
      } else {
      // Copy in values averaged over the prior delta_t fine timesteps, where delta_t is
      // specific to scale.
        vcopy(domega, sim.grf.domega[scale]);
      }

      // Move reference point to a point on the axis of the local reference frame.
      // Then adjust the linear acceleration to be consistent.
      vcopy(ref_point, lrf->point);

      // uref_local = uref_global + omega X (point_on_axis - global_ref_point)
      dFLOAT r[3];      vsub(r, lrf->point, sim.grf.ref_point);
      dFLOAT cross1[3]; vcross(cross1, sim.grf.angular_vel, r);
      dFLOAT ulocal[3]; vadd(ulocal, sim.grf.ref_pt_vel, cross1);
    
      // aref_local = aref_global + (uref_global - uref_local) X omega
      dFLOAT udiff[3];  vsub(udiff, sim.grf.ref_pt_vel, ulocal);
      dFLOAT cross2[3]; vcross(cross2, udiff, sim.grf.angular_vel);
      vadd(linear_accel, grf_linear_accel_used, cross2);
      /* global to lrf [0], lrf to global [1]. rotate vector to corresponding coordinates*/
      rotate_vector(linear_accel, linear_accel, lrf->containing_to_local_rotation_matrix);
    }
  }
  else if (lrf != NULL) {
    is_rotation = TRUE;
    // If we reach here, there is no global ref frame
    vcopy(angular_vel, lrf->angular_vel);
    vcopy(angular_vel1, lrf->angular_vel);
    vcopy(ref_point, lrf->point);
    vzero(linear_accel);
    update_containing_lrfs_angular_vel(lrf, angular_vel, angular_vel1, ref_point, linear_accel);
 

    vzero(domega);
    if (lrf->containing_lrf_index != SRI_GLOBAL_REF_FRAME_INDEX) {    // nested LRF may have time-dependent omega_grf
      dFLOAT lat_time_inc_per_timestep = g_adv_fraction;
      ccDOTIMES(i,3)
        domega[i] = (angular_vel1[i] - angular_vel[i]) / lat_time_inc_per_timestep;
    }

    if (lrf->domega) {
      vscale(domega, lrf->domega[scale], lrf->axis);
      update_containing_lrfs_domega(lrf, domega, scale);
    }
  }

  nirf->is_rotation = is_rotation;
}  

typedef struct sSCALAR_MATERIAL
{
  sdFLOAT diffusivity_mol;
  sdFLOAT turb_schmidt_number;
  sdFLOAT uds_max_value;
  sdFLOAT uds_min_value;  
  bool    allow_negative_values;
  char    name[LGI_SCALAR_NAME_LEN];
} *SCALAR_MATERIAL;

#if BUILD_5G_LATTICE
typedef struct sFLUID_PROPERTIES
{
  iEOS       *equation_of_state;
  sdFLOAT    *interaction_strengthes;
  
  sdFLOAT    molecular_mass;
  sdFLOAT    viscosity;
  sdFLOAT    molecular_charge;
  // TODO:
  // these should be wall parameters eventually, an array of values per
  // component.
  //sdFLOAT      wall_potential[MAXIMUM_WALL_IDS];
  
  void initialize (asINT32 c, EOS_TYPE type, sdFLOAT visc, sdFLOAT mol_weight);
  void update (asINT32 c);
} *FLUID_PROPERTIES;


sdFLOAT mc_pressure(const sdFLOAT densities[], sdFLOAT temperature);

VOID compute_mc_densities_from_pressure (sdFLOAT densities[2],
					 sdFLOAT p,
					 sdFLOAT comp0_percentage,
					 sdFLOAT phase_index);

sdFLOAT contact_angle_to_potential(sdFLOAT angle, sdFLOAT nu_over_t);

#endif  //BUILD_5G_LATTICE

SOLVER_INDEX_MASK compute_solver_index_mask(asINT32 scale);
SOLVER_INDEX_MASK compute_seed_solver_index_mask(asINT32 scale, TIMESTEP timestep);
BOOLEAN is_conduction_active_in_mask(const ACTIVE_SOLVER_MASK mask);
BOOLEAN is_any_flow_solver_active_in_mask(const ACTIVE_SOLVER_MASK mask);

//
// Compute the product of two quaternions, q1 and q2.
// The result q cannot be identical to either q1 or q2.
// 
// On Sun, this method cannot be static because it is called inside a templated
// function below.
__HOST__DEVICE__  _INLINE_ VOID quaternion_multiply(dFLOAT q[4], dFLOAT q1[4], dFLOAT q2[4])
{
  // q1 = a + r, where a is a scalar and r is a vector
  // q2 = b + s, where b is a scalar and s is a vector
  //
  // q1 * q2 = (ab - r dot s) + (as + br + r cross s)

  q[0] = q1[0] * q2[0] - vdot(q1+1, q2+1);

  dFLOAT v1[3], v2[3];

  vscale(v1, q1[0], q2+1);
  vscale(v2, q2[0], q1+1);

  vadd(q+1, v1, v2);

  vcross(v1, q1+1, q2+1);
  vadd(q+1, q+1, v1);
}

template<typename INPUT_FLOAT_TYPE, typename OUTPUT_FLOAT_TYPE>
__HOST__DEVICE__ VOID rotate_vector_from_ground_to_global_coord_sys(INPUT_FLOAT_TYPE in[3], OUTPUT_FLOAT_TYPE out[3])
{
  auto& sim = get_sim_ref();  
  dFLOAT q[4];
  dFLOAT q1[4];
  q[0] = 0;
  q[1] = in[0]; q[2] = in[1]; q[3] = in[2]; 
  quaternion_multiply(q1, q, sim.grf.quaternion_inverse);
  quaternion_multiply(q, sim.grf.quaternion, q1);

  out[0] = q[1];
  out[1] = q[2];
  out[2] = q[3];
}  

template<typename INPUT_FLOAT_TYPE, typename OUTPUT_FLOAT_TYPE>
__HOST__DEVICE__ _INLINE_ VOID rotate_vector_from_global_to_ground_coord_sys(INPUT_FLOAT_TYPE in[3], OUTPUT_FLOAT_TYPE out[3])
{
  auto& sim = get_sim_ref();
  dFLOAT q[4];
  dFLOAT q1[4];
  q[0] = 0;
  q[1] = in[0]; q[2] = in[1]; q[3] = in[2]; 
  quaternion_multiply(q1, q, sim.grf.quaternion);
  quaternion_multiply(q, sim.grf.quaternion_inverse, q1);

  out[0] = q[1];
  out[1] = q[2];
  out[2] = q[3];
}


//These timescale functions have been moved here because they depend on get_simc_ref
__HOST__DEVICE__ INLINE BOOLEAN sTIMESTEP_MANAGER::is_timestep_odd(asINT32 scale) {
  auto& simc = get_simc_ref();
  return (((1 << ((simc.num_scales - 1) - scale)) & m_time) != 0);
}

__HOST__DEVICE__ INLINE asINT32 sim_is_timestep_odd(STP_SCALE scale, STP_TIMESTEP timestep) {
  auto& simc = get_simc_ref();
  return ((1 << ((simc.num_scales - 1) - (scale))) & (timestep));
}

#define sim_prior_timestep_index(scale, timestep) \
  (sim_is_timestep_odd(scale, timestep) ? 1 : 0)

__HOST__DEVICE__ INLINE asINT32 sTIMESTEP_MANAGER::prior_timestep_index(asINT32 scale, TIMESTEP timestep) {
  return sim_is_timestep_odd(scale, timestep) ? 1 : 0;
}

__HOST__DEVICE__ INLINE BOOLEAN sTIMESCALE_INFO::is_timestep_odd(asINT32 scale) {
  auto& simc = get_simc_ref();
  return (((1 << ((simc.num_scales - 1) - scale)) & m_timestep) != 0);
}

/*------------------------------------------------------------------------------*
 * Scale to a voxel or ublk size
 *------------------------------------------------------------------------------*/

__HOST__DEVICE__ INLINE asINT32 sim_scale_to_voxel_size(STP_SCALE s) {
  auto& simc = get_simc_ref();
  return (1 << ((simc.num_scales - 1) - (s)));
}

__HOST__DEVICE__ INLINE asINT32 sim_scale_to_ublk_size(STP_SCALE s) {
  return (2 * sim_scale_to_voxel_size(s));
}

__HOST__DEVICE__ INLINE asINT32 sim_scale_to_cube_size(STP_SCALE s) {
  auto& simc = get_simc_ref();
  return (1 << (simc.num_scales - 1 - (s)));
}

__HOST__DEVICE__ INLINE asINT32 sim_scale_to_cube_volume(STP_SCALE s) {
  auto& simc = get_simc_ref();  
  return (1 << (simc.num_dims * (simc.num_scales - 1 - (s))));
}

__HOST__DEVICE__ INLINE BASETIME solver_scale_ts_size(const sTIMESTEP_MANAGER& solver, SCALE scale) { return sim_scale_to_voxel_size(scale) * solver.m_n_base_steps; }

#endif	/* #ifndef _SIMENG_SIM_H */


