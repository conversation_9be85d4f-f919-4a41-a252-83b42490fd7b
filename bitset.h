#ifndef EXA_SIMENG_BITSET_H_
#define EXA_SIMENG_BITSET_H_

#include <cstddef>
#include <cstdint>
#include <climits>
#include <type_traits>
#include <array>
#include <cassert>
#include <cstring>

#include "common_sp.h"

/** @brief Computes the default type to use for tBITSET. */
template<size_t N>
struct tBITSET_DEFAULT_TYPE
{
  using type = std::conditional_t< N <= 8, uint8_t,
                  std::conditional_t< N <= 16, uint16_t,
                     std::conditional_t< N <= 32, uint32_t, uint64_t>>>;
};

/** @brief Computes the number of elements to use for N bits and an internal type T. */
template<size_t N, typename T>
struct tBITSET_COMPUTE_N_ELEMENTS
{
  enum {
    N_TYPE_BITS = sizeof(T)*CHAR_BIT,
    N_ELEMENTS = N/N_TYPE_BITS + (N % N_TYPE_BITS == 0 ? 0 : 1)
  };
};

/** @brief Sanitizes (sets to 0) any unused bits in tBITSET */
template<typename T, size_t SANITIZE_BITS>
struct tBITSET_SANITIZE
{
// You may think that all these static_casts are excessive, and you may be right,
// but I found when implementing the all() method that an implicit cast to int
// was screwing everything up. So, to be safe, I'm being extremely explicit,
// and casting everything after every operation.
  __HOST__DEVICE__ constexpr static void sanitize(T& m) 
  {
    m &= static_cast<T>(~(static_cast<T>(static_cast<T>(~static_cast<T>(0)) << SANITIZE_BITS)));
  }
};

template<typename T>
struct tBITSET_SANITIZE<T,0>
{
  __HOST__DEVICE__  constexpr static void sanitize(T&) {}
};

/** Calls the correct compiler intrinsic popcount depending on the type T. */
template<typename T>
struct tBITSET_POPCOUNT
{
  __HOST__ constexpr static int popcount(T m) noexcept {
    return __builtin_popcount(m);
  }
};

template <>
struct tBITSET_POPCOUNT<unsigned long>
{
  __HOST__ constexpr static int popcount(unsigned long m) noexcept {
    return __builtin_popcountl(m);
  }
};

template <>
struct tBITSET_POPCOUNT<unsigned long long>
{
  __HOST__ constexpr static int popcount(unsigned long long m) noexcept {
    return __builtin_popcountll(m);
  }
};

/** @brief Calls the correct compiler intrinsic version of find-first-set.

@return Returns 1 plus the index of the least significant 1-bit of x, or if x is zero, returns zero. (According to the gcc/clang documentation.)
*/
template<typename T>
struct tBITSET_FFS
{
  constexpr static int ffs(T m) noexcept {
    return __builtin_ffs(m);
  }
};

template <>
struct tBITSET_FFS<long>
{
  constexpr static int ffs(long m) noexcept {
    return __builtin_ffsl(m);
  }
};

template <>
struct tBITSET_FFS<long long>
{
  constexpr static int ffs(long long m) noexcept {
    return __builtin_ffsll(m);
  }
};

/** @brief Implementation for tBITSET_BASE with N_ELEMENTS > 1.  

  @warning This class is the implementation class for tBITSET. Do not use this class directly!

  This specialization allows for a bitset to be spread across multiple integral elements,
  whether for alignment or memory-saving purposes. However, this generally is slower performance
  wise. Benchmark!

  All member functions behave the same as the the N_ELEMENTS == 1 specialization. Some functions
  may not be implemented in this generic version yet because they haven't been needed.
 */
template<size_t N, typename INTERNAL_TYPE, size_t N_ELEMENTS>
class tBITSET_BASE 
{

protected:

  using LARGEST_TYPE = typename tBITSET_DEFAULT_TYPE<N>::type;

private:

  enum {
    INTERNAL_TYPE_BITS = sizeof(INTERNAL_TYPE)*CHAR_BIT,
    INTERNAL_TYPE_SIZE = sizeof(INTERNAL_TYPE)*N_ELEMENTS,
    SANITIZE_BITS = N % INTERNAL_TYPE_BITS,
    EXTRA_BITS = INTERNAL_TYPE_BITS*N_ELEMENTS - N,
    LARGEST_TYPE_N_ELEMENTS = tBITSET_COMPUTE_N_ELEMENTS<N,LARGEST_TYPE>::N_ELEMENTS,
    LARGEST_TYPE_SIZE = sizeof(LARGEST_TYPE)*LARGEST_TYPE_N_ELEMENTS,
    COPY_SIZE = INTERNAL_TYPE_SIZE < LARGEST_TYPE_SIZE ? INTERNAL_TYPE_SIZE : LARGEST_TYPE_SIZE
  };

  using MASK_TYPE = INTERNAL_TYPE[N_ELEMENTS];

  MASK_TYPE m_mask;

  __HOST__DEVICE__ constexpr void sanitize() 
  {
    tBITSET_SANITIZE<INTERNAL_TYPE,SANITIZE_BITS>::sanitize(m_mask[N_ELEMENTS-1]);
  }

public:

  /** @brief Initialize all bits to 0. */
  __HOST__DEVICE__ constexpr tBITSET_BASE() noexcept : m_mask{0} {}

  /** @brief Initialize with an array of integers.
   *
   * This is explicit on purpose to prevent silly errors.
   */
  __HOST__DEVICE__ constexpr explicit tBITSET_BASE(const MASK_TYPE& m) noexcept
  {
    for (int i=0; i<N_ELEMENTS; i++) {
      m_mask[i] = m[i];
    }
    sanitize();
  }

  /** @brief Initialize with a single integer large enough to fit in the array.
   *
   * This is explicit on purpose to prevent silly errors.
   */
  explicit tBITSET_BASE(LARGEST_TYPE m) noexcept 
  {
    static_assert( sizeof(LARGEST_TYPE)*CHAR_BIT >= N,"Cannot represent tBITSET as integer");
    memcpy(&m_mask[0], &m, COPY_SIZE);
    sanitize();
  }

  /** @brief Set bit n to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& set(size_t n) noexcept {
    cassert(n < N);
    size_t byte = n / INTERNAL_TYPE_BITS;
    size_t bit = n % INTERNAL_TYPE_BITS;
    m_mask[byte] |= static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << bit);
    return *this;
  }

  /** @brief Reset bit n to 0. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& reset(size_t n) noexcept {
    cassert(n < N);
    size_t byte = n / INTERNAL_TYPE_BITS;
    size_t bit = n % INTERNAL_TYPE_BITS;
    m_mask[byte] &= static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << bit));
    return *this;
   }

  __HOST__DEVICE__ constexpr tBITSET_BASE& set_or_reset(size_t n, bool value) noexcept {
    if (value) {
      return set(n);
    } else {
      return reset(n);
    }
  }

  /** @brief Negate bit n. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& flip(size_t n) noexcept {
    cassert(n < N);
    size_t byte = n / INTERNAL_TYPE_BITS;
    size_t bit = n % INTERNAL_TYPE_BITS;
    m_mask[byte] ^= static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << bit);
    sanitize();
    return *this;
  }

  /** @brief Set all bits to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& set_all() noexcept {
    for(size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] = static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(0));
    }
    sanitize();
    return *this;
  }

  /** @brief Set all bits to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& reset_all() noexcept {
    for(size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] = static_cast<INTERNAL_TYPE>(0);
    }
    return *this;
  }

  /** @brief Flip all bits. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& flip_all() noexcept {
    for(size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] = static_cast<INTERNAL_TYPE>(~m_mask[i]);
    }
    sanitize();
    return *this;
  }

  /** @brief Test if bit n is set to 1. */
  __HOST__DEVICE__ constexpr bool test(size_t n) const noexcept {
    cassert(n < N);
    size_t byte = n / INTERNAL_TYPE_BITS;
    size_t bit = n % INTERNAL_TYPE_BITS;
    return m_mask[byte] & static_cast<INTERNAL_TYPE>( static_cast<INTERNAL_TYPE>(1) << bit );
  }

  /** @brief Count the number of bits set to 1. */
  __HOST__ constexpr int count() const noexcept {
    int c = 0;
    for (size_t i=0; i < N_ELEMENTS; i++) {
      c += tBITSET_POPCOUNT<INTERNAL_TYPE>::popcount(m_mask[i]);
    }
    return c;
  }

  /** @brief Find the first bit set to 1. 
   * @return Returns the index of the first bit set to 1, or -1 if no bits are set.
   */
   constexpr int find_first_set() const noexcept {
     int c = 0;
     for (size_t i=0; i < N_ELEMENTS; i++) {
       int tmp = tBITSET_FFS<typename std::make_signed<INTERNAL_TYPE>::type>::ffs(m_mask[i]);
       if ( tmp > 0 ) {
         return c + tmp - 1;
       }
       else {
         c += INTERNAL_TYPE_BITS;
       }
     }
     return -1;
   }

  /** @brief Or this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator |= (const tBITSET_BASE& b)
  {
    for (size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] |= b.m_mask[i];
    }
    return *this;
  }

  /** @brief And this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator &= (const tBITSET_BASE& b)
  {
    for (size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] &= b.m_mask[i];
    }
    return *this;
  }

  /** @brief Xor this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator ^= (const tBITSET_BASE& b)
  {
    for (size_t i=0; i < N_ELEMENTS; i++) {
      m_mask[i] ^= b.m_mask[i];
    }
    return *this;
  }
  
  /** @brief Returns a copy of the bitset as a single integer. 
    
   This integer may be larger than INTERNAL_TYPE.
  */
  __HOST__DEVICE__ LARGEST_TYPE get() const noexcept
  {
    static_assert( sizeof(LARGEST_TYPE)*CHAR_BIT >= N,"Cannot represent tBITSET as integer");
    LARGEST_TYPE val{0};
    memcpy(&val, &m_mask[0], COPY_SIZE);
    return val;
  }

  /** @brief Test is any bit is set to 1.
   
   Use this in place of a bool test, which is left out to prevent silly errors with implicit integer conversions.
   */
  __HOST__DEVICE__ constexpr bool any() const noexcept {
    for (size_t i=0; i < N_ELEMENTS; i++) {
      if (m_mask[i] != static_cast<INTERNAL_TYPE>(0)) {
        return true;
      }
    }
    return false;
  }

  /** @brief Test if no bits are set to 1. */
  __HOST__DEVICE__ constexpr bool none() const noexcept {
    for (size_t i=0; i < N_ELEMENTS; i++) {
      if (m_mask[i] != static_cast<INTERNAL_TYPE>(0)) {
        return false;
      }
    }
    return true;
  }

  /** @brief Test if all bits are set to 1. */
  __HOST__DEVICE__ constexpr bool all() const noexcept {
    for (size_t i=0; i < N_ELEMENTS-1; i++) {
      if (m_mask[i] != static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(0))) {
        return false;
      }
    }
    return m_mask[N_ELEMENTS-1] == static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(0)) >> EXTRA_BITS);
  }

  /** @brief Test if two bitsets are equal. */
  __HOST__DEVICE__ constexpr bool operator == (const tBITSET_BASE& b) const noexcept {
    for(size_t i=0; i < N_ELEMENTS; i++) {
      if ( m_mask[i] != b.m_mask[i] ) {
        return false;
      }
    }
    return true;
  }

  /** @brief Test if two bitsets are not equal. */
  __HOST__DEVICE__ constexpr bool operator != (const tBITSET_BASE& b) const noexcept {
    return !( this->operator == (b) );
  }

};

/** @brief Implementation for tBITSET_BASE with `N_ELEMENTS = 1`.  

  @warning This class is the implementation class for tBITSET. Do not use this class directly!

  Prefer to use this version over N_ELEMENTS > 1 if you can, because this allows
  for greater performance and usefulness. However, memory size, alignment, and
  wasted bits are dictated by INTERNAL_TYPE.

  Unlike std::bitset, the constructor is marked explicit; this is a deliberate
  design choice, as it prevents errors in initialization. Also, we do not provide
  an empty set() or flip() functions for setting/flipping all the bits.  To be
  more explicit and prevent silly errors, the set_all()/flip_all() functions are
  provided instead.

 */
template<size_t N, typename INTERNAL_TYPE> 
class tBITSET_BASE<N, INTERNAL_TYPE, 1> 
{

protected:

  using LARGEST_TYPE = INTERNAL_TYPE;

private:

  enum {
    INTERNAL_TYPE_BITS = sizeof(INTERNAL_TYPE)*CHAR_BIT,
    SANITIZE_BITS = N % INTERNAL_TYPE_BITS,
    EXTRA_BITS = INTERNAL_TYPE_BITS - N
  };

  INTERNAL_TYPE m_mask;

  __HOST__DEVICE__ constexpr void sanitize() {
    tBITSET_SANITIZE<INTERNAL_TYPE,SANITIZE_BITS>::sanitize(m_mask);
  }

public:

  /** @brief Initialize all bits to 0. */
  __HOST__DEVICE__ constexpr tBITSET_BASE() noexcept : m_mask{0} {}

  /** @brief Initialize with integer m.
   *
   * This is explicit on purpose to prevent silly errors.
   */
  explicit __HOST__DEVICE__ constexpr tBITSET_BASE(INTERNAL_TYPE m) noexcept : m_mask{m}
  {
    sanitize();
  }

  /** @brief Set bit n to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& set(size_t n) noexcept {
    cassert(n < N);
    m_mask |= static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << n);
    return *this;
  }

  /** @brief Reset bit n to 0.

      The cuda compiler is really stupid about overload resolution, so it can't
      find the correct overload for the atomicOr function, unless we do these casts
      This function will only work for 64-bit masks
  */
  __DEVICE__ constexpr void gpu_atomic_set(size_t n) noexcept {
    cassert(n < N);
#if DEVICE_COMPILATION_MODE
    static_assert(sizeof(INTERNAL_TYPE) == sizeof(unsigned long long), "INTERNAL TYPE is the wrong size");
    atomicOr( reinterpret_cast<unsigned long long*>(&m_mask), (unsigned long long) 1 << n );
#else
    set(n);
#endif
  }

  __DEVICE__ constexpr void gpu_atomic_reset(size_t n) noexcept {
    cassert(n < N);
#if DEVICE_COMPILATION_MODE
    static_assert(sizeof(INTERNAL_TYPE) == sizeof(unsigned long long), "INTERNAL TYPE is the wrong size");
    atomicAnd( reinterpret_cast<unsigned long long*>(&m_mask), ~((unsigned long long) 1 << n) );
#else
    reset(n);
#endif
  }

  __DEVICE__ constexpr void gpu_atomic_set_or_reset(size_t n, bool value) noexcept {
    if (value) {
      return gpu_atomic_set(n);
    } else {
      return gpu_atomic_reset(n);
    }
  }  

  __HOST__DEVICE__ constexpr tBITSET_BASE& reset(size_t n) noexcept {
    cassert(n < N);
    m_mask &= static_cast<INTERNAL_TYPE>(~(static_cast<INTERNAL_TYPE>(1) << n));
    return *this;
   }

  __HOST__DEVICE__ constexpr tBITSET_BASE& set_or_reset(size_t n, bool value) noexcept {
    if (value) {
      return set(n);
    } else {
      return reset(n);
    }
  }

  /** @brief Negate bit n. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& flip(size_t n) noexcept {
    cassert(n < N);
    m_mask ^= static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << n);
    return *this;
  }

  /** @brief Set all bits to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& set_all() noexcept {
    m_mask = static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(0));
    sanitize();
    return *this;
  }

  /** @brief Set all bits to 1. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& reset_all() noexcept {
    m_mask = static_cast<INTERNAL_TYPE>(0);
    return *this;
  }

  /** @brief Flip all bits. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& flip_all() noexcept {
    m_mask = ~m_mask;
    sanitize();
    return *this;
  }

  /** @brief Test if bit n is set to 1. */
  __HOST__DEVICE__ constexpr bool test(size_t n) const noexcept {
    cassert(n < N);
    return m_mask & static_cast<INTERNAL_TYPE>( static_cast<INTERNAL_TYPE>(1) << n );
  }

  /** @brief Returns a copy of the underlying integer. */
  __HOST__DEVICE__ constexpr INTERNAL_TYPE get() const noexcept {
    return m_mask;
  }

  /** @brief Count the number of bits set to 1. */
  __HOST__ constexpr int count() const noexcept {
    return tBITSET_POPCOUNT<INTERNAL_TYPE>::popcount(m_mask);
  }

  __DEVICE__ int gpu_count() const noexcept {
#if DEVICE_COMPILATION_MODE
    static_assert(sizeof(INTERNAL_TYPE) <= sizeof(unsigned long long int), "INTERNAL TYPE is the wrong size");
    return __popcll(m_mask);
#else
      return tBITSET_POPCOUNT<INTERNAL_TYPE>::popcount(m_mask);
#endif
  }

  /** @brief Find the first bit set to 1. 
   * @return Returns the index of the first bit set to 1, or -1 if no bits are set.
   */
  __HOST__DEVICE__ constexpr int find_first_set() const noexcept {
    return tBITSET_FFS<typename std::make_signed<INTERNAL_TYPE>::type>::ffs(m_mask) - 1;
  }

  /** @brief Or this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator |= (const tBITSET_BASE& b) noexcept
  {
    m_mask |= b.m_mask;
    return *this;
  }

  /** @brief And this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator &= (const tBITSET_BASE& b) noexcept
  {
    m_mask &= b.m_mask;
    return *this;
  }

  /** @brief Xor this with another bitset. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator ^= (const tBITSET_BASE& b) noexcept
  {
    m_mask ^= b.m_mask;
    return *this;
  }

  /** @brief Right shift by the given number of bits. */
  __HOST__DEVICE__ constexpr tBITSET_BASE& operator >>= (size_t n) noexcept
  {
    m_mask >>= n;
    // No sanitize necessary here
    return *this;
  }

  /** @brief Left shift by the given number of bits. */
  constexpr tBITSET_BASE& operator <<= (size_t n) noexcept
  {
    m_mask <<= n;
    sanitize();
    return *this;
  }

  /** @brief Right shift by n bits */
  constexpr tBITSET_BASE operator >> (size_t n) noexcept
  {
    tBITSET_BASE c(*this);
    c >>= n;
    return c;
  }

  /** @brief Left shift by n bits. 
     @return A copy of *this with its bits left shifted.
  */
  constexpr tBITSET_BASE operator << (size_t n) noexcept
  {
    tBITSET_BASE c(*this);
    c <<= n;
    return c;
  }

  /** @brief Negate all bits. 
   @return A copy of *this with its bits negated. 
  */
  constexpr tBITSET_BASE operator ~ () const noexcept 
  {
    tBITSET_BASE b(*this);
    b.flip_all();
    return b;
  }

  /** @brief Test is any bit is set to 1.
   
   Use this in place of a bool test, which is left out to prevent silly errors with implicit integer conversions.
   */
  __HOST__DEVICE__ constexpr bool any() const noexcept {
    return m_mask != static_cast<INTERNAL_TYPE>(0);
  }

  /** @brief Test if all bits are set to 1. */
  __HOST__DEVICE__ constexpr bool all() const noexcept {
    return m_mask == static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(~static_cast<INTERNAL_TYPE>(0)) >> EXTRA_BITS);
  }

  /** @brief Test if no bits are set to 1. */
  __HOST__DEVICE__ constexpr bool none() const noexcept {
    return m_mask == static_cast<INTERNAL_TYPE>(0);
  }

  /** @brief Test if two bitsets are equal. */
  __HOST__DEVICE__ constexpr bool operator == (const tBITSET_BASE& b) const noexcept {
    return m_mask == b.m_mask;
  }

  /** @brief Test if two bitsets are not equal. */
  __HOST__DEVICE__ constexpr bool operator != (const tBITSET_BASE& b) const noexcept {
    return !( this->operator == (b) );
  }

};

/** @brief A memory saving bit mask.

This class is similar to `std::bitset`. Unfortunately, the size of
`std::bitset` is not guaranteed to be a specific size. We need to control the
size and alignment, hence this class.

The implementation allows the user to make a tradeoff between size and speed.
The default usage, for any N under 64 bits, it will choose an internal type that
minimizes the number of unused bits. However, this is user customizable. If
you want the mask to be represented as an array of unsigned chars, you can
do that as: `tBITSET<19,uint8_t>`. This gives 19 bits, represented as an
array of bytes. The default, `tBITSET<19>`, would normally select `uint32_t` as
the internal type.

Any unused bits are guaranteed to always be 0.

`tBITSET` is completely constexpr.

Most member functions that modify the bitset return a reference to `this`,
allowing chaining of the various operations. For example:

@code
    constexpr auto bits = tBITSET<19>().set(3).set(5).flip_all();
@endcode

Most of the standard bitwise arithmetic operators are overloaded, so the class
behaves as if it is an integer.  However, implicit conversion to bool is not
supported, in order to prevent errors with conversions to/from integers. The
same behavior can be found by using the `.any()` member function.

The constructor is marked `explicit` to prevent silly implicit conversion
errors.

For further documentation, see the tBITSET_BASE class.

@warning This class obeys all the rules of bitwise arithmetic and undefined behavior from
         the C++ standard. Please be aware of how you use this type.

@tparam N The number of bits needed.
@tparam INTERNAL_TYPE The unsigned integer type to be used to store the bits.
                      By default it will use the smallest type that fits N bits.
 */
template<size_t N, typename INTERNAL_TYPE = typename tBITSET_DEFAULT_TYPE<N>::type>
class tBITSET : public tBITSET_BASE<N, INTERNAL_TYPE, tBITSET_COMPUTE_N_ELEMENTS<N,INTERNAL_TYPE>::N_ELEMENTS>
{
  using BASE = tBITSET_BASE<N,INTERNAL_TYPE, tBITSET_COMPUTE_N_ELEMENTS<N,INTERNAL_TYPE>::N_ELEMENTS>;
  static_assert( std::is_integral<INTERNAL_TYPE>::value , "Type for bitset must be an unsigned integer type" );
  static_assert( std::is_unsigned<INTERNAL_TYPE>::value && !std::is_same<bool,INTERNAL_TYPE>::value, "Type for bitset must be an unsigned integer type" );

public:
  using BASE::BASE;
  using TYPE = typename BASE::LARGEST_TYPE;

  /** Return the number of bits used. */
  __HOST__DEVICE__ constexpr static size_t size() {
    return N;
  }
  /** @brief Implicit conversion from BASE.
   
    This is safe because we know the base class is unique to this derived class.
  */
  __HOST__DEVICE__ constexpr tBITSET(const BASE& base) : BASE(base) {}

  /** @brief And 2 bitsets together */
  __HOST__DEVICE__ friend constexpr tBITSET operator & (tBITSET a, const tBITSET& b) {
    a &= b;
    return a;
  }

  /** @brief Or 2 bitsets together */
  __HOST__DEVICE__ friend constexpr tBITSET operator | (tBITSET a, const tBITSET& b) {
    a |= b;
    return a;
  }

  /** @brief Xor 2 bitsets together */
  __HOST__DEVICE__ friend constexpr tBITSET operator ^ (tBITSET a, const tBITSET& b) 
  {
    a ^= b;
    return a;
  }

  /** @brief Negate a single bitset */
  __HOST__DEVICE__ friend constexpr tBITSET operator ~ (tBITSET a) 
  {
    a.flip_all();
    return a;
  }

};


/** @brief Iterates over all the set bits in a bitset, returning the index of each bit */
#define ccDO_BITSET(bit, bitset) \
  auto ___(bs) = bitset; \
  for (int bit = ___(bs).find_first_set(); bit != -1; ___(bs).reset(bit), bit = ___(bs).find_first_set())


template<size_t N, typename INTERNAL_TYPE = typename tBITSET_DEFAULT_TYPE<N>::type>
class tATOMIC_BITSET
{
  static_assert( tBITSET_COMPUTE_N_ELEMENTS<N,INTERNAL_TYPE>::N_ELEMENTS == 1, "Atomic bitset must have 1 element" );
  static_assert( std::is_integral<INTERNAL_TYPE>::value , "Type for bitset must be an unsigned integer type" );
  static_assert( std::is_unsigned<INTERNAL_TYPE>::value && !std::is_same<bool,INTERNAL_TYPE>::value, "Type for bitset must be an unsigned integer type" );
  static_assert( std::atomic<INTERNAL_TYPE>::is_always_lock_free, "Not lock-free!" );

  std::atomic<INTERNAL_TYPE> m_mask;

public:
  using TYPE = std::atomic<INTERNAL_TYPE>;
  using NON_ATOMIC_BITSET_TYPE = tBITSET<N,INTERNAL_TYPE>;

  tATOMIC_BITSET() noexcept : m_mask(0) {};

  void store(const NON_ATOMIC_BITSET_TYPE& b, std::memory_order order = std::memory_order_seq_cst) const noexcept 
  {
    m_mask.store(b.get(), order);
  }

  NON_ATOMIC_BITSET_TYPE load(std::memory_order order = std::memory_order_seq_cst) const noexcept 
  {
    return NON_ATOMIC_BITSET_TYPE( m_mask.load(order) );
  }

  NON_ATOMIC_BITSET_TYPE fetch_or( const tBITSET<N,INTERNAL_TYPE>& b, std::memory_order order = std::memory_order_seq_cst ) noexcept 
  {
     return NON_ATOMIC_BITSET_TYPE( m_mask.fetch_or( b.get(), order ) );
  }

  NON_ATOMIC_BITSET_TYPE fetch_and( const tBITSET<N,INTERNAL_TYPE>& b, std::memory_order order = std::memory_order_seq_cst ) noexcept 
  {
    return NON_ATOMIC_BITSET_TYPE( m_mask.fetch_and( b.get(), order ) );
  }

  NON_ATOMIC_BITSET_TYPE fetch_xor( const tBITSET<N,INTERNAL_TYPE>& b, std::memory_order order = std::memory_order_seq_cst ) noexcept 
  {
    return NON_ATOMIC_BITSET_TYPE( m_mask.fetch_and( b.get(), order ) );
  }

  tATOMIC_BITSET& set(size_t n, std::memory_order order = std::memory_order_seq_cst) noexcept 
  {
    cassert(n < N);
    m_mask.fetch_or(static_cast<INTERNAL_TYPE>(static_cast<INTERNAL_TYPE>(1) << n), order);
    return *this;
  }

  tATOMIC_BITSET& reset(size_t n, std::memory_order order = std::memory_order_seq_cst) noexcept 
  {
    cassert(n < N);
    m_mask.fetch_and(static_cast<INTERNAL_TYPE>(~(static_cast<INTERNAL_TYPE>(1) << n)), order);
    return *this;
  }

  bool test(size_t n, std::memory_order order = std::memory_order_seq_cst) const noexcept 
  {
    cassert(n < N);
    return m_mask.load(order) & static_cast<INTERNAL_TYPE>( static_cast<INTERNAL_TYPE>(1) << n);
  }

  tATOMIC_BITSET& reset_all(std::memory_order order = std::memory_order_seq_cst) noexcept 
  {
    m_mask.store(static_cast<INTERNAL_TYPE>(0), order);
    return *this;
  }

};

#endif
