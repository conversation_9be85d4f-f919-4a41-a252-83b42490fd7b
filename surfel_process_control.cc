/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Main Simulation Loop
 *
 * Vinit Gupta, Exa Corporation
 * Created Tue Sep 5 2017
 *--------------------------------------------------------------------------*/
#include "surfel_process_control.h"
#include "strand_mgr.h"
#include "mirror.h"
#include "wsurfel_comm.h"
#include "implicit_shell_solver.h"
#include "conduction_contact_averaging.h"
#include PHYSICS_H

/*****************************************************************************
 *                      S2S
 ****************************************************************************/
template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID surfel_clear_and_s2s_flow(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                               SURFEL surfel,
                               sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask)
{
  if (surfel_v2s_data)
    surfel_v2s_data->clear();
  if (surfel->is_even_or_odd()) {
    // even or odd active_solver_mask is either 0 or same as active_solver_mask
    // Clear the states only on even time steps for even and odd surfels
    // Odd surfels should store incoming stuff accumulated during V2S phase
    // Even surfels should store outgoing stuff distributed during S2V phase
#ifdef DEBUG_NEXTGEN
    if ( surfel->id() == 6314150) {
      print_surfel_states("BC&S2S",surfel_v2s_data,surfel);
    }
#endif

      // copy outgoing S2S stuff from odd surfels to surfel_v2s_data on even time steps
      if (surfel->is_odd()) { // Odd surfel
        if (surfel_process_control->is_lb_time_step_even()) {
          surfel->v2s_lb_data()->reset_except_outflux();
        }
        if (IS_T_LB_SOLVER_ON && surfel_process_control->is_temp_time_step_even()) {
          surfel->v2s_t_data()->reset_except_outflux_t();
        }
	if (IS_UDS_LB_SOLVER_ON && surfel_process_control->is_uds_time_step_even()) {
	  DO_SURFEL_V2S_UDS_DATA(nth_uds, sim.n_user_defined_scalars, surfel, v2s_uds_data)
            v2s_uds_data->reset_except_outflux_uds();
#if BUILD_D19_LATTICE
	  if(sim.is_pf_model) {
            surfel->v2s_pf_data()->reset();
          }
#endif
        }
      } else { // Even surfel
        if (surfel_process_control->is_lb_time_step_even()) {
          surfel->v2s_lb_data()->reset();
        }
        if (IS_T_LB_SOLVER_ON && surfel_process_control->is_temp_time_step_even()) {
          surfel->v2s_t_data()->reset();
        }
	if (IS_UDS_LB_SOLVER_ON && surfel_process_control->is_uds_time_step_even()) {
	  DO_SURFEL_V2S_UDS_DATA(nth_uds, sim.n_user_defined_scalars, surfel, v2s_uds_data)
            v2s_uds_data->reset();
#if BUILD_D19_LATTICE
	  if(sim.is_pf_model){
            surfel->v2s_pf_data()->reset();
          }
#endif
        }
      }
      if (surfel_process_control->is_turb_time_step_even()) {
        surfel->turb_data()->s_mag = 0.0;
        surfel->turb_data()->gamma_swirl = 0.0;
        surfel->v2s_turb_data()->reset();
      }
      if (surfel_process_control->is_temp_time_step_even()) {
        // m_kappa and m_heat_flux_cross_lrf needs to be reset for lrf surfels with PDE_T, PDE_entropy and LB_entropy solvers 
        // since they are accumulated in V2S and used in lrf surfel dyn.
        // No need to set m_T0 and m_S0 since they are not being accumulated.
        if (!IS_T_LB_SOLVER_ON && surfel->is_lrf()) {
          surfel->v2s_t_data()->m_kappa = 0.0;
          surfel->v2s_t_data()->m_heat_flux_cross_lrf = 0.0;
        }

        surfel->t_data()->temp_sample = 0.0;
        if (sim.T_solver_type == PDE_ENTROPY ||
            sim.T_solver_type == LB_ENTROPY  ||
	    sim.T_solver_type == LB_ENERGY) {
          surfel->t_data()->entropy_sample = 0.0;
        }
      }
      if (surfel_process_control->is_uds_time_step_even()) {
	DO_SURFEL_UDS_DATA(nth_uds, sim.n_user_defined_scalars, surfel, surfel_uds_data){ 
	  SURFEL_V2S_UDS_DATA v2s_uds_data = surfel->v2s_uds_data(nth_uds);
	  surfel_uds_data->uds_value = 0;
	  if (!IS_UDS_LB_SOLVER_ON) {
	    v2s_uds_data->m_diffusion_coef = 0;
	    if (surfel->is_lrf())
	      v2s_uds_data->m_uds_flux_cross_lrf = 0.0;
	  }
	}
      }
#if BUILD_5G_LATTICE
      msg_internal_error("No VRs in 5G");
      if (surfel_process_control->is_lb_active()) {
        if (g_is_multi_component) {
          surfel->mc_data()->density = 0.0;
          surfel->mc_data()->momentum[0] = 0.0;
          surfel->mc_data()->momentum[1] = 0.0;
          surfel->mc_data()->momentum[2] = 0.0;
          ccDOTIMES(i, N_LATTICE_VECTOR_PAIRS) {
            surfel->mc_data()->in_states[i] = 0.0;
          }
        }
      }
#endif
      // zero out incoming stuff for even and odd surfels on even time steps
  } else {
    if (surfel->has_v2s_data())  {
      if (surfel_process_control->is_lb_active()) {
        surfel->v2s_lb_data()->reset();
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            if (mirror_surfel->has_v2s_data()) mirror_surfel->v2s_lb_data()->reset();
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
      }
      if (surfel_process_control->is_temp_active()) {
        surfel->v2s_t_data()->reset();
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            if (mirror_surfel->has_v2s_data()) mirror_surfel->v2s_t_data()->reset();
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
      }
      if (surfel_process_control->is_uds_active()) {
        DO_SURFEL_V2S_UDS_DATA(nth_uds, sim.n_user_defined_scalars, surfel, v2s_uds_data) {
	        v2s_uds_data->reset();
	        if (surfel->has_mirror()) {
            sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              if (mirror_surfel->has_v2s_data()) mirror_surfel->v2s_uds_data(nth_uds)->reset();
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
#if BUILD_D19_LATTICE
        if (sim.is_pf_model) {
          surfel->v2s_pf_data()->reset();
          if (surfel->has_mirror()) {
            sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
            while (mirror_data) {
              SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
              if (mirror_surfel->has_v2s_data()) mirror_surfel->v2s_pf_data()->reset();
              mirror_data = mirror_data->m_next_mirror_data;
            }
          }
        }
#endif
      }
      if (surfel_process_control->is_turb_active()) {
        surfel->v2s_turb_data()->reset();
        if (surfel->has_mirror()) {
          sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
          while (mirror_data) {
            SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
            if (mirror_surfel->has_v2s_data()) mirror_surfel->v2s_turb_data()->reset();
            mirror_data = mirror_data->m_next_mirror_data;
          }
        }
      }
    }
    if (surfel_process_control->is_turb_active()) {
      surfel->turb_data()->s_mag = 0.0;
      surfel->turb_data()->gamma_swirl = 0.0;
    }

    if (surfel_process_control->is_temp_active()) {
      surfel->t_data()->temp_sample = 0.0;
      if (sim.T_solver_type == PDE_ENTROPY ||
          sim.T_solver_type == LB_ENTROPY  ||
	  sim.T_solver_type == LB_ENERGY) {
        surfel->t_data()->entropy_sample = 0.0;
      }
    }

    if (surfel_process_control->is_uds_active()) {
      DO_SURFEL_UDS_DATA(nth_uds, sim.n_user_defined_scalars, surfel, surfel_uds_data) {
        surfel_uds_data->uds_value = 0;  //diffusion_coef is cleaned in v2s_uds_data->reset()
      }
    }    

#if BUILD_5G_LATTICE
    if (surfel_process_control->is_lb_active()) {
      if (g_is_multi_component) {
        surfel->mc_data()->density = 0.0;
        surfel->mc_data()->momentum[0] = 0.0;
        surfel->mc_data()->momentum[1] = 0.0;
        surfel->mc_data()->momentum[2] = 0.0;
        ccDOTIMES(i, N_LATTICE_VECTOR_PAIRS) {
          surfel->mc_data()->rho_bar[i] = 0.0;
          surfel->mc_data()->in_states[i] = 0.0;
        }
      }
    }
#endif

  }

  // TODO Even surfels on even (from fine odd and fine surfels) time steps,
  // but Odd surfels on both  even (from coarse surfels) and odd
  // (from even and fine surfels) time steps.
  // The phase can be changed in the discretizer for even odd surfels to eliminate s2s,
  // v2s and s2v for even surfels on odd time steps and odd surfels on even time steps.
  // SS-CYCLING: Even surfels perform receive s2s only on even time-steps;
  // odd and regular surfels receive s2s on both even and odd time-steps
  // Since m_active_solver_mask == m_even_active_solver_mask | m_odd_active_solver_mask
  // we can use the same mask for odd and regular surfels
//  ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? surfel_process_control->m_even_active_solver_mask : surfel_process_control->m_odd_active_solver_mask;
  if (surfel->is_s2s_destination() && is_any_flow_solver_active_in_mask(s2s_active_solver_mask)) {
    surfel_s2s_advect(surfel, s2s_active_solver_mask,
                      surfel_v2s_data,
                      surfel_process_control->m_odd_active_solver_mask);
  }
#ifdef DEBUG_NEXTGEN
    if ( surfel->id() == 6314150) {
      print_surfel_states("AC&S2S",surfel_v2s_data,surfel);
    }
#endif
}

template VOID surfel_clear_and_s2s_flow<FALSE, TRUE>(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                          SURFEL surfel,
                                          sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask);
template VOID surfel_clear_and_s2s_flow<FALSE, FALSE>(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                          SURFEL surfel,
                                          sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask);
template VOID surfel_clear_and_s2s_flow<TRUE, TRUE>(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                         SURFEL surfel,
                                         sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask);
template VOID surfel_clear_and_s2s_flow<TRUE, FALSE>(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                         SURFEL surfel,
                                         sSURFEL_V2S_DATA *surfel_v2s_data, ACTIVE_SOLVER_MASK s2s_active_solver_mask);


 
VOID surfel_clear_and_s2s_conduction(sSURFEL_PROCESS_CONTROL *surfel_process_control,
                                     SURFEL surfel,
                                     ACTIVE_SOLVER_MASK s2s_active_solver_mask)
{
  if (surfel->is_even_or_odd()) {
    // even or odd active_solver_mask is either 0 or same as active_solver_mask
    
    // zero out incoming stuff for even and odd surfels on even time steps
    // CONDUCTION-TODO: Move this to a generic function which both regular and even/odd surfels can call
    if (surfel->is_conduction_surface() && surfel_process_control->is_conduction_time_step_even()) {
      surfel->conduction_data()->clear_sampled_quantities();
    }
  } else {
    // CONDUCTION-TODO: A general way to re-init following fields together rather than hard-coding in
    // surfel_process_control
    // CONDUCTION-CHECK: If we do this here, when is pre_advect_init called?
    if (surfel_process_control->is_conduction_active() && surfel->is_conduction_surface()) {
      surfel->conduction_data()->clear_sampled_quantities();
      if (surfel->has_mirror()) {
        sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
        while (mirror_data) {
          SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
          mirror_surfel->conduction_data()->clear_sampled_quantities();
          mirror_data = mirror_data->m_next_mirror_data;
        }
      }
    }
  }

  // TODO Even surfels on even (from fine odd and fine surfels) time steps,
  // but Odd surfels on both  even (from coarse surfels) and odd
  // (from even and fine surfels) time steps.
  // The phase can be changed in the discretizer for even odd surfels to eliminate s2s,
  // v2s and s2v for even surfels on odd time steps and odd surfels on even time steps.
  // SS-CYCLING: Even surfels perform receive s2s only on even time-steps;
  // odd and regular surfels receive s2s on both even and odd time-steps
  // Since m_active_solver_mask == m_even_active_solver_mask | m_odd_active_solver_mask
  // we can use the same mask for odd and regular surfels
//  ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? surfel_process_control->m_even_active_solver_mask : surfel_process_control->m_odd_active_solver_mask;
  conduction_surfel_s2s_sample(surfel, s2s_active_solver_mask, surfel_process_control->is_conduction_time_step_odd());
}

/*****************************************************************************
 *                      V2S
 ****************************************************************************/
template <BOOLEAN HAS_NO_VR_INTERACTIONS, BOOLEAN IS_LRF>
#if DEBUG
static
#else
static INLINE
#endif
VOID conduction_surfel_v2s(SURFEL surfel,
                           BOOLEAN is_time_step_odd,
                           ACTIVE_SOLVER_MASK active_solver_mask,
                           uINT32 allowed_v2s_scale_diff,
                           asINT32 allowed_v2s_phase_mask,
                           SOLVER_INDEX_MASK prior_solver_index_mask)
{
  conduction_surfel_v2s_sample<HAS_NO_VR_INTERACTIONS, IS_LRF>(surfel,
      allowed_v2s_phase_mask, active_solver_mask, allowed_v2s_scale_diff,
      prior_solver_index_mask);

  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;

      mirror_surfel->conduction_data()->clear_sampled_quantities();

      conduction_surfel_s2s_sample(mirror_surfel, active_solver_mask, is_time_step_odd);
      conduction_surfel_v2s<HAS_NO_VR_INTERACTIONS, IS_LRF>(mirror_surfel, is_time_step_odd,
                                                            active_solver_mask,
                                                            allowed_v2s_scale_diff,
                                                            allowed_v2s_phase_mask,
                                                            prior_solver_index_mask);
      
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
      conduction_mirror_to_real_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                    latvec_mask, active_solver_mask);
      
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN HAS_NO_VR_INTERACTIONS, BOOLEAN IS_LRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
#if DEBUG
static
#else
static INLINE
#endif
VOID surfel_v2s(SURFEL surfel, sSURFEL_V2S_DATA *surfel_v2s_data,
                ACTIVE_SOLVER_MASK odd_active_solver_mask,
                ACTIVE_SOLVER_MASK active_solver_mask,
                uINT32 allowed_v2s_scale_diff,
                asINT32 allowed_v2s_phase_mask,
                SOLVER_INDEX_MASK prior_solver_index_mask)
{

  surfel_v2s_advect<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, HAS_NO_VR_INTERACTIONS, IS_LRF, IS_ALL_SOLVER_TS_SAME>
                   (surfel, allowed_v2s_phase_mask, active_solver_mask, allowed_v2s_scale_diff,
                    prior_solver_index_mask, surfel_v2s_data);
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      LOCAL_SFL_V2S_DATA local_v2s_data;
      auto mirror_surfel_v2s_data = &local_v2s_data.v2s_data();
      mirror_surfel_v2s_data->clear();
      if (active_solver_mask & KE_PDE_ACTIVE) {
        mirror_surfel->turb_data()->s_mag = 0.0;
        mirror_surfel->turb_data()->gamma_swirl = 0.0;
      }

      if (active_solver_mask & T_PDE_ACTIVE) {
        mirror_surfel->t_data()->temp_sample = 0.0;
        if (sim.T_solver_type == PDE_ENTROPY ||
	    sim.T_solver_type == LB_ENTROPY  ||
	    sim.T_solver_type == LB_ENERGY) {
          mirror_surfel->t_data()->entropy_sample = 0.0;
        }
      }

      if (active_solver_mask & UDS_PDE_ACTIVE) {
        ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  mirror_surfel->uds_data(nth_uds)->uds_value = 0.0;
      }

      if (mirror_surfel->is_s2s_destination()) {
        surfel_s2s_advect(mirror_surfel, active_solver_mask, mirror_surfel_v2s_data, odd_active_solver_mask);
      }
      surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, HAS_NO_VR_INTERACTIONS, IS_LRF, IS_ALL_SOLVER_TS_SAME>(
                                                                                  mirror_surfel, mirror_surfel_v2s_data, odd_active_solver_mask, active_solver_mask,
                                                                                  allowed_v2s_scale_diff, allowed_v2s_phase_mask, prior_solver_index_mask);
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
      mirror_to_real_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                    latvec_mask, active_solver_mask,
                                    surfel_v2s_data, mirror_surfel_v2s_data);
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
    if (surfel->id()==128) {
      sSURFEL_V2S_T_DATA *surfel_v2s_t_data = surfel->has_v2s_data() ? surfel->v2s_t_data() : surfel_v2s_data->v2s_t_data();
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("after v2s mirror in_states_t %g temp_sample %g ", surfel_v2s_t_data->m_in_states_t[4], surfel->t_data()->temp_sample);
    }
#endif
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

template<BOOLEAN IS_LRF>
#if DEBUG
static
#else
static INLINE
#endif
VOID even_odd_conduction_surfel_v2s(SURFEL surfel, BOOLEAN is_time_step_odd,
                                    ACTIVE_SOLVER_MASK even_active_solver_mask,
                                    ACTIVE_SOLVER_MASK odd_active_solver_mask,
                                    uINT32 allowed_v2s_scale_diff,
                                    asINT32 allowed_v2s_phase_mask,
                                    SOLVER_INDEX_MASK prior_solver_index_mask,
                                    BOOLEAN is_surfel_mirror) {
  cassert(surfel->is_even_or_odd());

  ACTIVE_SOLVER_MASK active_solver_mask_to_use =
    !surfel->is_odd() ? even_active_solver_mask : even_active_solver_mask | odd_active_solver_mask;

  if (is_conduction_active_in_mask(active_solver_mask_to_use)) {
    conduction_surfel_v2s_sample<FALSE, IS_LRF>(surfel,
        allowed_v2s_phase_mask, active_solver_mask_to_use,
        allowed_v2s_scale_diff, prior_solver_index_mask);
  }

  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      
      mirror_surfel->conduction_data()->clear_sampled_quantities();
      
      // TODO Even surfels on even (from fine odd and fine surfels) time steps,
      // but Odd surfels on both  even (from coarse surfels) and odd
      // (from even and fine surfels) time steps.
      // The phase can be changed in the discretizer for even odd surfels to eliminate s2s,
      // v2s and s2v for even surfels on odd time steps and odd surfels on even time steps.
      conduction_surfel_s2s_sample(mirror_surfel, active_solver_mask_to_use, is_time_step_odd);
      // Even surfels do not participate in V2S on odd time steps
      if (is_conduction_active_in_mask(active_solver_mask_to_use)) {
        conduction_surfel_v2s_sample<FALSE, IS_LRF>(mirror_surfel,
            allowed_v2s_phase_mask, active_solver_mask_to_use,
            allowed_v2s_scale_diff, prior_solver_index_mask);
      }
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
      conduction_mirror_to_real_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                    latvec_mask, active_solver_mask_to_use);
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

template<BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_LRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
#if DEBUG
static
#else
static INLINE
#endif
VOID even_odd_surfel_v2s(SURFEL surfel, sSURFEL_V2S_DATA *surfel_v2s_data,
                         BOOLEAN is_surfel_odd,
                         ACTIVE_SOLVER_MASK odd_active_solver_mask,
                         ACTIVE_SOLVER_MASK even_odd_active_solver_mask,
                         uINT32 allowed_v2s_scale_diff,
                         SOLVER_PHASE_MASKS allowed_v2s_phase_mask,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         BOOLEAN is_surfel_mirror)
{
  BOOLEAN is_lb_active = even_odd_active_solver_mask & LB_ACTIVE;
  // Even surfels do not participate in V2S on odd time steps

  // SS-CYCLING: Without sub-/super-cycling, it is easy enough
  // to do this check using: !is_time_step_odd || is_surfel_odd
  // When sub-/super-cycling is enabled, a time-step being
  // even or odd depends on the solver, so some solvers
  // might need v2s and some might not. We have to
  // check whether a given TS is even/odd for THAT solver which is
  // given by the even/odd_active_solver_mask.
  //
  // Furthermore, for even surfels on odd LB time-steps,
  // IS_LB_ACTIVE will be TRUE (since odd surfels
  // need to perform LB_v2s). These even surfels may still need to
  // perform v2s other solvers as stated above.
  // We will have to explicitly check whether LB_v2s is needed and
  // pass the appropriate parameter to surfel_v2s_advect.
  if (is_any_flow_solver_active_in_mask(even_odd_active_solver_mask)) {
    if (is_lb_active) {
      surfel_v2s_advect<TRUE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(surfel, allowed_v2s_phase_mask, even_odd_active_solver_mask,
								allowed_v2s_scale_diff, prior_solver_index_mask, surfel_v2s_data);
    } else {
      surfel_v2s_advect<FALSE, FALSE, FALSE, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(surfel, allowed_v2s_phase_mask, even_odd_active_solver_mask,
						     allowed_v2s_scale_diff, prior_solver_index_mask, surfel_v2s_data);

    }
  }
#ifdef DEBUG_NEXTGEN
    if ( surfel->id() == 6314150) {
      print_surfel_states("AV2S",surfel_v2s_data,surfel);
    }
#endif
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      LOCAL_SFL_V2S_DATA local_v2s_data;
      auto mirror_surfel_v2s_data = &local_v2s_data.v2s_data();
      mirror_surfel_v2s_data->clear();
      if (even_odd_active_solver_mask & KE_PDE_ACTIVE) {
        mirror_surfel->turb_data()->s_mag = 0.0;
        mirror_surfel->turb_data()->gamma_swirl = 0.0;
      }
      if (even_odd_active_solver_mask & T_PDE_ACTIVE) {
        mirror_surfel->t_data()->temp_sample = 0.0;
        if (sim.T_solver_type == PDE_ENTROPY ||
	    sim.T_solver_type == LB_ENTROPY  ||
	    sim.T_solver_type == LB_ENERGY) {
          mirror_surfel->t_data()->entropy_sample = 0.0;
        }
      }

      if (even_odd_active_solver_mask & UDS_PDE_ACTIVE) {
        ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  mirror_surfel->uds_data(nth_uds)->uds_value = 0.0;
      }

      // TODO Even surfels on even (from fine odd and fine surfels) time steps,
      // but Odd surfels on both  even (from coarse surfels) and odd
      // (from even and fine surfels) time steps.
      // The phase can be changed in the discretizer for even odd surfels to eliminate s2s,
      // v2s and s2v for even surfels on odd time steps and odd surfels on even time steps.
      if (mirror_surfel->is_s2s_destination() && is_any_flow_solver_active_in_mask(even_odd_active_solver_mask)) {
        surfel_s2s_advect(mirror_surfel, even_odd_active_solver_mask, mirror_surfel_v2s_data, odd_active_solver_mask);
      }
      // Even surfels do not participate in V2S on odd time steps
      if (is_any_flow_solver_active_in_mask(even_odd_active_solver_mask)) {
	if (is_lb_active) {
	  surfel_v2s_advect<TRUE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(mirror_surfel, allowed_v2s_phase_mask, even_odd_active_solver_mask,
								    allowed_v2s_scale_diff, prior_solver_index_mask, mirror_surfel_v2s_data);
	} else {
	  surfel_v2s_advect<FALSE, FALSE, FALSE, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(mirror_surfel, allowed_v2s_phase_mask, even_odd_active_solver_mask,
							 allowed_v2s_scale_diff, prior_solver_index_mask, mirror_surfel_v2s_data);

	}
      }
//      if (!is_time_step_odd || is_surfel_odd) {
//        surfel_v2s_advect<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, FALSE, IS_LRF>(
//              mirror_surfel, allowed_v2s_phase_mask, active_solver_mask,
//              allowed_v2s_scale_diff, prior_solver_index_mask, mirror_surfel_v2s_data);
//      }
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;
      mirror_to_real_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                    latvec_mask, even_odd_active_solver_mask,
                                    surfel_v2s_data, mirror_surfel_v2s_data);
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
}

/*****************************************************************************
 *                      S2V
 ****************************************************************************/
template<BOOLEAN HAS_NO_VR_INTERACTIONS, BOOLEAN IS_LRF>
#if DEBUG
static
#else
static INLINE
#endif
VOID conduction_surfel_s2v(SURFEL surfel,
                           ACTIVE_SOLVER_MASK active_solver_mask,
                           uINT32 allowed_s2v_scale_diff, asINT32 allowed_s2v_phase_mask,
                           SOLVER_INDEX_MASK prior_solver_index_mask) {

  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;

      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;

      // Do the reflection is the real and mirror surfels are home surfels, or if they are ghosted on this SP
      // It is possible that the surfel is ghosted on this SP but the mirror surfel is not ghosted. In that
      // case, do not reflect.
      if (!surfel->is_ghost() || (surfel->is_ghost() && mirror_surfel)) {
        conduction_real_to_mirror_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                      latvec_mask, active_solver_mask);
        conduction_surfel_s2v<HAS_NO_VR_INTERACTIONS, IS_LRF>(mirror_surfel,
            active_solver_mask, allowed_s2v_scale_diff,
            allowed_s2v_phase_mask, prior_solver_index_mask);
      }
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
  conduction_surfel_s2v_distribute<HAS_NO_VR_INTERACTIONS, IS_LRF>(surfel,
      allowed_s2v_phase_mask, active_solver_mask, allowed_s2v_scale_diff,
      prior_solver_index_mask);

}

template<BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN HAS_NO_VR_INTERACTIONS, BOOLEAN IS_LRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
#if DEBUG
static
#else
static INLINE
#endif
VOID surfel_s2v(SURFEL surfel, sSURFEL_V2S_DATA *surfel_v2s_data,
                ACTIVE_SOLVER_MASK active_solver_mask,
                uINT32 allowed_s2v_scale_diff, asINT32 allowed_s2v_phase_mask,
                SOLVER_INDEX_MASK prior_solver_index_mask) {
  // This function is only called for surfels active on even and odd steps
  if (surfel->has_two_copies_of_outflux()) {
    BOOLEAN is_lb_timestep_odd = is_index_odd(lb_index_from_mask(prior_solver_index_mask));
    BOOLEAN is_temp_timestep_odd = is_index_odd(t_index_from_mask(prior_solver_index_mask));
    BOOLEAN is_uds_timestep_odd = is_index_odd(uds_index_from_mask(prior_solver_index_mask));

    sSURFEL_V2S_LB_DATA *v2s_lb_data = surfel_v2s_data->v2s_lb_data();
    if (surfel->has_v2s_data() && !surfel->is_mirror()) {
      v2s_lb_data = surfel->v2s_lb_data();
    }
    ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
      surfel->curr_outflux(!is_lb_timestep_odd)[l] =  v2s_lb_data->m_out_flux[l];
    }
    surfel->curr_ustar_0()[!is_lb_timestep_odd] = surfel->lb_data()->ustar_0;
    if (IS_T_LB_SOLVER_ON) {
      sSURFEL_V2S_T_DATA *v2s_t_data = surfel_v2s_data->v2s_t_data();
      if (surfel->has_v2s_data() && !surfel->is_mirror()) {
        v2s_t_data = surfel->v2s_t_data();
      }
      ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
        surfel->curr_outflux_t(!is_temp_timestep_odd)[l] =  v2s_t_data->m_out_flux_t[l];
      }
    }
     if (IS_UDS_LB_SOLVER_ON) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *v2s_uds_data = surfel_v2s_data->v2s_uds_data(nth_uds);
	if (surfel->has_v2s_data() && !surfel->is_mirror()) {
	  v2s_uds_data = surfel->v2s_uds_data(nth_uds);
	}
	ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
	  surfel->curr_outflux_uds(!is_uds_timestep_odd, nth_uds)[l] =  v2s_uds_data->m_out_flux_uds[l];
	}
      }
    }
#if BUILD_5G_LATTICE
    if (g_is_multi_component) {
      ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
	surfel->curr_outflux_mc(!is_lb_timestep_odd)[l] =  surfel->mc_data()->out_flux[l];
      }
    }
#endif
  }
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      LOCAL_SFL_V2S_DATA local_v2s_data;
      auto mirror_surfel_v2s_data = &local_v2s_data.v2s_data();
      mirror_surfel_v2s_data->clear();
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;

      // Do the reflection is the real and mirror surfels are home surfels, or if they are ghosted on this SP
      // It is possible that the surfel is ghosted on this SP but the mirror surfel is not ghosted. In that
      // case, do not reflect.
      if (!surfel->is_ghost() || (surfel->is_ghost() && mirror_surfel)) {
        real_to_mirror_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                      latvec_mask, active_solver_mask,
                                      surfel_v2s_data, mirror_surfel_v2s_data);
        surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, HAS_NO_VR_INTERACTIONS, IS_LRF, IS_ALL_SOLVER_TS_SAME>(
             mirror_surfel, mirror_surfel_v2s_data, active_solver_mask,
             allowed_s2v_scale_diff, allowed_s2v_phase_mask, prior_solver_index_mask);
      }
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
  surfel_s2v_advect<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, HAS_NO_VR_INTERACTIONS, IS_LRF, IS_ALL_SOLVER_TS_SAME>(
      surfel, allowed_s2v_phase_mask, active_solver_mask, allowed_s2v_scale_diff,
      prior_solver_index_mask, surfel_v2s_data);

}

template<BOOLEAN IS_LRF>
#if DEBUG
static
#else
static INLINE
#endif
VOID even_odd_conduction_surfel_s2v(SURFEL surfel,
                                    ACTIVE_SOLVER_MASK even_active_solver_mask,
                                    ACTIVE_SOLVER_MASK odd_active_solver_mask,
                                    uINT32 allowed_s2v_scale_diff,
                                    asINT32 allowed_s2v_phase_mask,
                                    SOLVER_INDEX_MASK prior_solver_index_mask,
                                    asINT32 time_step_index) {

  ACTIVE_SOLVER_MASK active_solver_mask_to_use =
    !surfel->is_odd() ? even_active_solver_mask | odd_active_solver_mask : odd_active_solver_mask;

  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;

      // Do the reflection is the real and mirror surfels are home surfels, or if they are ghosted on this SP
      // It is possible that the surfel is ghosted on this SP but the mirror surfel is not ghosted. In that
      // case, do not reflect.
      if (!surfel->is_ghost() || (surfel->is_ghost() && mirror_surfel)) {
        conduction_real_to_mirror_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                      latvec_mask, active_solver_mask_to_use);

        even_odd_conduction_surfel_s2v<IS_LRF>(mirror_surfel,
            even_active_solver_mask, odd_active_solver_mask, allowed_s2v_scale_diff,
            allowed_s2v_phase_mask, prior_solver_index_mask, time_step_index);
      }
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }

  cassert(surfel->is_even_or_odd());

  if (is_conduction_active_in_mask(active_solver_mask_to_use)) {
    conduction_surfel_s2v_distribute<FALSE, IS_LRF>(surfel,
        allowed_s2v_phase_mask, active_solver_mask_to_use,
        allowed_s2v_scale_diff, prior_solver_index_mask);
  }
}

template<BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_LRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
#if DEBUG
static
#else
static INLINE
#endif
VOID even_odd_surfel_s2v(SURFEL surfel, sSURFEL_V2S_DATA *surfel_v2s_data,
                         BOOLEAN is_surfel_odd,
                         ACTIVE_SOLVER_MASK even_odd_active_solver_mask,
                         uINT32 allowed_s2v_scale_diff,
                         SOLVER_PHASE_MASKS allowed_s2v_phase_mask,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         SOLVER_INDEX_MASK time_step_index_mask) {
  BOOLEAN is_lb_active = even_odd_active_solver_mask & LB_ACTIVE;
  if (surfel->has_mirror()) {
    sSURFEL_MIRROR_DATA *mirror_data = surfel->mirror_data();
    while (mirror_data) {
      SURFEL mirror_surfel = mirror_data->m_mirror_surfel;
      LOCAL_SFL_V2S_DATA local_v2s_data;
      auto mirror_surfel_v2s_data = &local_v2s_data.v2s_data();
      mirror_surfel_v2s_data->clear();
      SURFEL_MIRROR_CONFIG mirror_config = mirror_data->m_surfel_mirror_config;
      STP_LATVEC_MASK latvec_mask = mirror_data->m_latvec_state_mask;

      // Do the reflection is the real and mirror surfels are home surfels, or if they are ghosted on this SP
      // It is possible that the surfel is ghosted on this SP but the mirror surfel is not ghosted. In that
      // case, do not reflect.
      if (!surfel->is_ghost() || (surfel->is_ghost() && mirror_surfel)) {
        real_to_mirror_surfel_reflect(surfel, mirror_surfel, mirror_config,
                                      latvec_mask, even_odd_active_solver_mask,
                                      surfel_v2s_data, mirror_surfel_v2s_data);
        even_odd_surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_LRF, IS_ALL_SOLVER_TS_SAME>(
            mirror_surfel, mirror_surfel_v2s_data, is_surfel_odd,
            even_odd_active_solver_mask, allowed_s2v_scale_diff, allowed_s2v_phase_mask,
            prior_solver_index_mask, time_step_index_mask);
      }
      mirror_data = mirror_data->m_next_mirror_data;
    }
  }
  // SS-CYCLING: Following if clause was originally:
  //  surfel->has_two_copies_of_outflux() &&
  //  ( !surfel->is_even_or_odd() ||
  //   ( is_time_step_odd && surfel->is_odd()) ||
  //   (!is_time_step_odd && surfel->is_even()))
  // This function is never called for regular surfels (not even/odd),
  // so that check can be removed. As a precaution, adding an assert
  // here for now.
  cassert(surfel->is_even_or_odd());

  // This function is only called for surfels active on even and odd steps
  // SS-CYCLING: Add additional solver checks and use the even/odd_active_solver_mask
  // values. Following will work only when LB_base_steps = 1.
  if (surfel->has_two_copies_of_outflux()) {

    const BOOLEAN is_lb_timestep_odd = is_index_odd(lb_index_from_mask(prior_solver_index_mask));
    const BOOLEAN is_temp_timestep_odd = is_index_odd(t_index_from_mask(prior_solver_index_mask));

    if ( (is_lb_timestep_odd && surfel->is_odd())
        || (!is_lb_timestep_odd && surfel->is_even()) ) {

      sSURFEL_V2S_LB_DATA *v2s_lb_data = surfel->v2s_lb_data();
      if (surfel->has_v2s_data() && !surfel->is_mirror()) {
        v2s_lb_data = surfel->v2s_lb_data();
      }
      asINT32 lb_index = (time_step_index_mask & LB_ACTIVE) >> LB_SOLVER;
      ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
        surfel->curr_outflux(lb_index)[l] =  v2s_lb_data->m_out_flux[l];
      }

      surfel->curr_ustar_0()[lb_index] = surfel->lb_data()->ustar_0;
    }

    if (IS_T_LB_SOLVER_ON
        && ((is_temp_timestep_odd && surfel->is_odd())
          || (!is_temp_timestep_odd && surfel->is_even()))) {
      sSURFEL_V2S_T_DATA *v2s_t_data = surfel->v2s_t_data();
      if (surfel->has_v2s_data() && !surfel->is_mirror()) {
        v2s_t_data = surfel->v2s_t_data();
      }
    asINT32 temp_index = (time_step_index_mask & T_PDE_ACTIVE) >> T_SOLVER;
      ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
        surfel->curr_outflux_t(temp_index)[l] =  v2s_t_data->m_out_flux_t[l];
      }
    }

    if (IS_UDS_LB_SOLVER_ON) {
      asINT32 uds_index = (time_step_index_mask & UDS_PDE_ACTIVE) >> UDS_SOLVER;
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	sSURFEL_V2S_UDS_DATA *v2s_uds_data = surfel->v2s_uds_data(nth_uds);
	if (surfel->has_v2s_data() && !surfel->is_mirror()) {
	  v2s_uds_data = surfel->v2s_uds_data(nth_uds);
	}
	ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
	  surfel->curr_outflux_uds(uds_index,nth_uds)[l] =  v2s_uds_data->m_out_flux_uds[l];
	}
      }
    }
    
#if BUILD_5G_LATTICE
    if (g_is_multi_component) {
      asINT32 lb_index = (time_step_index_mask & LB_ACTIVE) >> LB_SOLVER;
      ccDOTIMES(l, N_SURFEL_PGRAM_VOLUMES) {
	surfel->curr_outflux_mc(lb_index)[l] =  surfel->mc_data()->out_flux[l];
      }
    }
#endif
  }

  if (is_any_flow_solver_active_in_mask(even_odd_active_solver_mask)) {
    if (is_lb_active) {
      surfel_s2v_advect<TRUE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(surfel, allowed_s2v_phase_mask,
								even_odd_active_solver_mask,
								allowed_s2v_scale_diff,
								prior_solver_index_mask, surfel_v2s_data);
    } else {
      surfel_s2v_advect<FALSE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_LRF, IS_ALL_SOLVER_TS_SAME>(surfel, allowed_s2v_phase_mask, 
						     even_odd_active_solver_mask,
						     allowed_s2v_scale_diff,
						     prior_solver_index_mask, surfel_v2s_data);
      // Currently if LB is not active, T_LB_SOLVER cannot be ON either
    }
  }
}


/* DYN SURFELS*/
#define DO_ALL_SURFELS_THIS_PASS(surfel)                                                          \
  SURFEL surfel = m_first_surfel_this_pass;                                                       \
  for (asINT32 n_surfels_processed = 0;                                                           \
       surfel != NULL && n_surfels_processed < m_n_surfels_this_pass;                             \
       surfel = surfel->m_next, n_surfels_processed++)

#define DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair)                                                \
  SURFEL_PAIR surfel_pair = m_first_surfel_pair_this_pass;                                        \
  for (asINT32 n_surfel_pairs_processed = 0;                                                      \
       surfel_pair != NULL && n_surfel_pairs_processed < m_n_surfel_pairs_this_pass;              \
       surfel_pair = surfel_pair->m_next, n_surfel_pairs_processed++)

#define DO_ALL_SAMPLING_SURFELS_THIS_PASS(sampling_surfel)                                        \
  SAMPLING_SURFEL sampling_surfel = m_first_sampling_surfel_this_pass;                            \
  for (asINT32 n_sampling_surfels_processed = 0;                                                  \
       sampling_surfel != NULL && n_sampling_surfels_processed < m_n_sampling_surfels_this_pass;  \
       sampling_surfel = sampling_surfel->m_next, n_sampling_surfels_processed++)


template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID sSURFEL_PROCESS_CONTROL::surfels_clear_and_s2s_flow(  SURFEL_GROUP surfel_group,
                                                     sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? this->m_even_active_solver_mask : this->m_active_solver_mask;
    surfel_clear_and_s2s_flow<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(this, surfel, surfel_v2s_data, s2s_active_solver_mask);
    if (surfel->is_even())
      cassert(!surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights());
    if (surfel->is_odd() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
      sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
      surfel->pre_advect_init_copy_even_to_odd(m_active_solver_mask, even_surfel);
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::surfels_clear_and_s2s_conduction(SURFEL_GROUP surfel_group,
                                             sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? this->m_even_active_solver_mask : this->m_active_solver_mask;
    surfel_clear_and_s2s_conduction(this, surfel, s2s_active_solver_mask);
    if (surfel->is_even())
      cassert(!surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights());
    //DFG-TODO: Do we need to init_copy_even_to_odd(...) for conduction?
    if (surfel->is_odd() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
      sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
      surfel->pre_advect_init_copy_even_to_odd(m_active_solver_mask, even_surfel);
    }
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::do_surfels_v2s_flow(SURFEL_GROUP surfel_group,
                                             sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    if (is_any_flow_solver_active()) {
    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
      uINT32 allowed_scale_diff = 0;
      asINT32 allowed_phase_mask = m_full_phase_masks;
      surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, FALSE, TRUE>(
           surfel, surfel_v2s_data, is_time_step_odd(), m_active_solver_mask,
           allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
    } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
      surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, FALSE, IS_ALL_SOLVER_TS_SAME>(
           surfel, surfel_v2s_data, is_time_step_odd(), m_active_solver_mask,
           m_allowed_v2s_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask);
    } else { // even/odd surfel
      ACTIVE_SOLVER_MASK even_odd_v2s_active_solver_mask = !surfel->is_odd() ? m_even_active_solver_mask :
							   (m_even_active_solver_mask | m_odd_active_solver_mask);
      even_odd_surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_ALL_SOLVER_TS_SAME>(
          surfel, NULL, surfel->is_odd(), is_time_step_odd(), even_odd_v2s_active_solver_mask,
          m_allowed_v2s_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask, FALSE);
    }
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_surfels_v2s_conduction(SURFEL_GROUP surfel_group) {
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    if (is_conduction_active() && surfel->is_conduction_surface()) { //conduction open shells do not participate in v2s
      if (!surfel->is_even_or_odd()) {
        if (!surfel->interacts_with_vr_ublks()) {
          uINT32 allowed_scale_diff = 0;
          asINT32 allowed_phase_mask = FULL_PHASE_MASK & (~(STP_S2V_PHASE_MASK));
          conduction_surfel_v2s<TRUE, FALSE>(surfel, is_conduction_time_step_odd(),
              m_active_solver_mask, allowed_scale_diff, allowed_phase_mask,
              m_prior_solver_index_mask);
        } else {
          asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                      ? STP_EVEN_V2S_PHASE_MASK
                                      : STP_ODD_V2S_PHASE_MASK;
          conduction_surfel_v2s<FALSE, FALSE>(surfel, is_conduction_time_step_odd(),
              m_active_solver_mask, m_allowed_v2s_scale_diff, allowed_phase_mask,
              m_prior_solver_index_mask);
        }
      } else { // even/odd surfel
        asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                     ? STP_EVEN_V2S_PHASE_MASK
                                     : STP_ODD_V2S_PHASE_MASK;
        even_odd_conduction_surfel_v2s<FALSE>(surfel, is_conduction_time_step_odd(),
            m_even_active_solver_mask, m_odd_active_solver_mask, m_allowed_v2s_scale_diff,
            allowed_phase_mask, m_prior_solver_index_mask, FALSE);
      }
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_surfels_dyn_flow(STP_GEOM_VARIABLE group_voxel_size,
                                                  STP_GEOM_VARIABLE meas_scale_factor,
                                                  SURFEL_GROUP surfel_group,
                                             sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
#ifdef DEBUG_NEXTGEN
    if (surfel->id() == 6314150) {
      printf("PROC ID  %d\n", my_proc_id);
      print_surfel_states("BSDY", surfel_v2s_data, surfel);
    }
#endif
    if (!surfel->is_even_or_odd()) {

#if ENABLE_SIM_COUNTERS
      if (!surfel->is_fringe() && !surfel->is_fringe2()) {
        // increment the surfel counters for that physics type
        timer_accum_counters(surfel->timer(), 0, 1);
      }
#endif
      flow_surfel_dynamics(surfel, m_active_solver_mask, m_active_solver_mask,
                      group_voxel_size, meas_scale_factor, surfel_v2s_data);
    } else { // even/odd surfel
      BOOLEAN is_surfel_odd = surfel->is_odd();

      // Even surfels on even time steps and Odd surfels on odd time steps
      // SS-CHECKING: For sub-/super-cycled cases, even/odd time-step
      // is solver dependent. We can eliminate unnecessary call using 
      // m_(even|odd)_active_solver_mask
      if (is_surfel_odd) {

#if ENABLE_SIM_COUNTERS
      if (!surfel->is_fringe() && !surfel->is_fringe2()) {
        timer_accum_counters(surfel->timer(), 0, 1);
      }
#endif
        if (m_odd_active_solver_mask) {
          flow_surfel_dynamics(surfel, m_odd_active_solver_mask, m_active_solver_mask,
                               group_voxel_size, meas_scale_factor, NULL);
        }
      } else {

#if ENABLE_SIM_COUNTERS
      if (!surfel->is_fringe() && !surfel->is_fringe2()) {
        timer_accum_counters(surfel->timer(), 0, 1);
      }
#endif
        if (m_even_active_solver_mask) {
          flow_surfel_dynamics(surfel, m_even_active_solver_mask, m_active_solver_mask,
                               group_voxel_size, meas_scale_factor, NULL);
        }
      }
    }
#ifdef DEBUG_NEXTGEN
    if (surfel->id() == 6314150) {
      print_surfel_states("ASDY", surfel_v2s_data, surfel);
    }
#endif
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_surfels_dyn_conduction(STP_GEOM_VARIABLE group_voxel_size,
                                                        STP_GEOM_VARIABLE meas_scale_factor,
                                                        SURFEL_GROUP surfel_group,
                                                        sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    SURFEL_V2S_DATA surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
#if ENABLE_SIM_COUNTERS
      if (!surfel->is_fringe() && !surfel->is_fringe2()) {
        // increment the surfel counters for that physics type
        timer_accum_counters(surfel->timer(), 0, 1);
      }
#endif
    if (!surfel->is_even_or_odd()) {
      conduction_surfel_dynamics(surfel, m_active_solver_mask, m_active_solver_mask, surfel_v2s_data, 
                                 group_voxel_size, meas_scale_factor, FALSE);
    } else { // even/odd surfel
      BOOLEAN is_surfel_odd = surfel->is_odd();
      // Even surfels on even time steps and Odd surfels on odd time steps
      // SS-CHECKING: For sub-/super-cycled cases, even/odd time-step
      // is solver dependent. We can eliminate unnecessary call using 
      // m_(even|odd)_active_solver_mask
      if (is_surfel_odd) {
        if (m_odd_active_solver_mask) {
          conduction_surfel_dynamics(surfel, m_odd_active_solver_mask, m_active_solver_mask, NULL,
                                     group_voxel_size, meas_scale_factor, FALSE);
        }
      } else {
        if (m_even_active_solver_mask) {
          conduction_surfel_dynamics(surfel, m_even_active_solver_mask, m_active_solver_mask, NULL,
                                     group_voxel_size, meas_scale_factor, FALSE);
        }
      }
    }
  }
#endif
}

// Only the last operation in this pass updates first_surfel_next_pass
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::do_surfels_s2v_flow(SURFEL_GROUP surfel_group,
                                             sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                             SURFEL& next_surfel_to_process) //first surfel to process in the next pass
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    if (is_any_flow_solver_active()) {
    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
      uINT32 allowed_scale_diff = 0;
      asINT32 allowed_phase_mask = m_full_phase_masks;

        surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, FALSE, TRUE>(
            surfel, surfel_v2s_data, m_active_solver_mask,
            allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);

      } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {

        surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, FALSE, IS_ALL_SOLVER_TS_SAME>(
            surfel, surfel_v2s_data, m_active_solver_mask,
            m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask);

      } else { // even/odd surfel
        BOOLEAN is_surfel_odd = surfel->is_odd();
        ACTIVE_SOLVER_MASK even_odd_s2v_active_solver_mask = surfel->is_odd() ? m_odd_active_solver_mask : (m_even_active_solver_mask | m_odd_active_solver_mask);
        even_odd_surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_ALL_SOLVER_TS_SAME>(
            surfel, NULL, is_surfel_odd, even_odd_s2v_active_solver_mask,
            m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask,
            m_coarse_time_step_index_mask);
      }
    }
  }
  next_surfel_to_process = surfel;
}

// Only the last operation in this pass updates first_surfel_next_pass
VOID sSURFEL_PROCESS_CONTROL::do_surfels_s2v_conduction(SURFEL_GROUP surfel_group,
                                                        SURFEL& next_surfel_to_process) //first surfel to process in the next pass
{
  DO_ALL_SURFELS_THIS_PASS(surfel) {
    if (is_conduction_active() && surfel->is_conduction_surface()) { //conduction open shells do not participate in s2v
      if (!surfel->is_even_or_odd()) {
        if (!surfel->interacts_with_vr_ublks()) {
          uINT32 allowed_scale_diff = 0;
          asINT32 allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
          conduction_surfel_s2v<TRUE, FALSE>(surfel, m_active_solver_mask,
              allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        } else {
          asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                      ? STP_EVEN_S2V_PHASE_MASK
                                      : STP_ODD_S2V_PHASE_MASK;
          conduction_surfel_s2v<FALSE, FALSE>(surfel, m_active_solver_mask,
              m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        }
      } else { // even/odd surfel
        asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                     ? STP_EVEN_S2V_PHASE_MASK
                                     : STP_ODD_S2V_PHASE_MASK;
        even_odd_conduction_surfel_s2v<FALSE>(surfel,
            m_even_active_solver_mask, m_odd_active_solver_mask,
            m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask,
            m_coarse_time_step_index_mask);
      }
    }
  }
  next_surfel_to_process = surfel;
}

#if !BUILD_GPU
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::process_all_dyn_surfels_flow(SURFEL_GROUP surfel_group,
                                                           STRAND strand_type)
{
  STP_SCALE scale = surfel_group->m_scale;

  STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
  STP_GEOM_VARIABLE meas_scale_factor = (STP_GEOM_VARIABLE)
                                        ((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1)));

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();
  asINT32 n_surfels = surfel_group->n_shob_ptrs();
  //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);

  m_first_surfel_this_pass = surfel_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfels, N_SURFELS_IN_A_PASS);
  asINT32 m_n_surfels_left = n_surfels;
  SURFEL first_surfel_next_pass = NULL;

  // Only INTERIOR_NS strand is dealing with interior surfels
  BOOLEAN is_interior_surfel_base_group = (strand_type == INTERIOR_NS_A_STRAND ||
                                           strand_type == INTERIOR_NS_B_STRAND); 
  while (m_first_surfel_this_pass != NULL) {  

    SP_TIMER_TYPE s2s_timer_type;
    SP_TIMER_TYPE v2s_timer_type;
    SP_TIMER_TYPE s2v_timer_type;
    SP_TIMER_TYPE sdyn_timer_type;

    if(is_interior_surfel_base_group) {
      s2s_timer_type = SP_S2S_TIMER;
      v2s_timer_type = SP_V2S_TIMER;
      s2v_timer_type = SP_S2V_TIMER;
      sdyn_timer_type = (surfel_group->shob_ptrs())->timer();
    } else if (strand_type == FRINGE_SURF_STRAND) {
      s2s_timer_type = SP_FRINGE_S2S_TIMER;
      v2s_timer_type = SP_FRINGE_V2S_TIMER;
      s2v_timer_type = SP_FRINGE_S2V_TIMER;
      sdyn_timer_type = SP_FRINGE_SURFEL_DYN_TIMER;
    } else if (strand_type == FRINGE2_SURF_STRAND) {
      s2s_timer_type = SP_FRINGE2_S2S_TIMER;
      v2s_timer_type = SP_FRINGE2_V2S_TIMER;
      s2v_timer_type = SP_FRINGE2_S2V_TIMER;
      sdyn_timer_type = SP_FRINGE2_SURFEL_DYN_TIMER;
    } else {
      s2s_timer_type = SP_INVALID_TIMER;
      v2s_timer_type = SP_INVALID_TIMER;
      s2v_timer_type = SP_INVALID_TIMER;
      sdyn_timer_type = (surfel_group->shob_ptrs())->timer();
      msg_internal_error("Unexpected surfel strand type %d", strand_type);
    } 
      
    MAYBE_WITH_TIMER(TRUE,s2s_timer_type) {
      surfels_clear_and_s2s_flow<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(surfel_group, &v2s_mem_pool);
    }

    MAYBE_WITH_TIMER(TRUE, v2s_timer_type) {
      do_surfels_v2s_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>(surfel_group, &v2s_mem_pool);
    }
    // When using surfel dyn timers, we assume that all the surfels are of the same physics type
    // Hence it is OK to get the timer using the physics type of an arbitrary surfel
    MAYBE_WITH_TIMER(surfel_group->shob_ptrs() != NULL, sdyn_timer_type)
    {
      do_surfels_dyn_flow(group_voxel_size, meas_scale_factor,
                          surfel_group, &v2s_mem_pool);
    }

    MAYBE_WITH_TIMER(TRUE,s2v_timer_type) {
      do_surfels_s2v_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>(surfel_group, &v2s_mem_pool, first_surfel_next_pass);

    }
    //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    m_n_surfels_left -= m_n_surfels_this_pass;
    m_first_surfel_this_pass = first_surfel_next_pass;
    m_n_surfels_this_pass = MIN(m_n_surfels_left, N_SURFELS_IN_A_PASS);
    //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    consider_surrender(strand_type);
  }
}
#endif

VOID sSURFEL_PROCESS_CONTROL::process_all_dyn_surfels_conduction(SURFEL_GROUP surfel_group,
                                                                 STRAND strand_type)
{
  asINT32 n_surfels = surfel_group->n_shob_ptrs();
  //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
  if (n_surfels == 0) { 
    return;
  }

  STP_SCALE scale = surfel_group->m_scale;
  STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
  STP_GEOM_VARIABLE meas_scale_factor = (STP_GEOM_VARIABLE)
                                        ((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1)));

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();

  m_first_surfel_this_pass = surfel_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfels, N_SURFELS_IN_A_PASS);
  asINT32 m_n_surfels_left = n_surfels;
  SURFEL first_surfel_next_pass = NULL;

  // Only INTERIOR_NS strand is dealing with interior surfels
  BOOLEAN is_interior_surfel_base_group = (strand_type == INTERIOR_NS_A_STRAND ||
                                           strand_type == INTERIOR_NS_B_STRAND); 
  SP_TIMER_TYPE s2s_timer_type;
  SP_TIMER_TYPE v2s_timer_type;
  SP_TIMER_TYPE s2v_timer_type;
  SP_TIMER_TYPE sdyn_timer_type;

  if(is_interior_surfel_base_group) {
    s2s_timer_type = SP_S2S_TIMER;
    v2s_timer_type = SP_V2S_TIMER;
    s2v_timer_type = SP_S2V_TIMER;
    sdyn_timer_type = (surfel_group->shob_ptrs())->timer();
  } else if (strand_type == FRINGE_SURF_STRAND) {
    s2s_timer_type = SP_FRINGE_S2S_TIMER;
    v2s_timer_type = SP_FRINGE_V2S_TIMER;
    s2v_timer_type = SP_FRINGE_S2V_TIMER;
    sdyn_timer_type = SP_FRINGE_SURFEL_DYN_TIMER;
  } else if (strand_type == FRINGE2_SURF_STRAND) {
    s2s_timer_type = SP_FRINGE2_S2S_TIMER;
    v2s_timer_type = SP_FRINGE2_V2S_TIMER;
    s2v_timer_type = SP_FRINGE2_S2V_TIMER;
    sdyn_timer_type = SP_FRINGE2_SURFEL_DYN_TIMER;
  } else {
    s2s_timer_type = SP_INVALID_TIMER;
    v2s_timer_type = SP_INVALID_TIMER;
    s2v_timer_type = SP_INVALID_TIMER;
    sdyn_timer_type = (surfel_group->shob_ptrs())->timer();
    msg_internal_error("Unexpected surfel strand type %d", strand_type);
  } 
  
  while (m_first_surfel_this_pass != NULL) {

    MAYBE_WITH_TIMER(TRUE,s2s_timer_type) {
      //memset(g_surfel_v2s_datasets, 0, m_n_surfels_this_pass * sizeof(sSURFEL_V2S_DATA));
      v2s_mem_pool.clear(m_n_surfels_this_pass);
      surfels_clear_and_s2s_conduction(surfel_group, &v2s_mem_pool);
    }

    MAYBE_WITH_TIMER(TRUE, v2s_timer_type) {
      do_surfels_v2s_conduction(surfel_group);
    }
    // When using surfel dyn timers, we assume that all the surfels are of the same physics type
    // Hence it is OK to get the timer using the physics type of an arbitrary surfel
    MAYBE_WITH_TIMER(surfel_group->shob_ptrs() != NULL, sdyn_timer_type)
    {
      do_surfels_dyn_conduction(group_voxel_size, meas_scale_factor, surfel_group, &v2s_mem_pool);
    }

    MAYBE_WITH_TIMER(TRUE,s2v_timer_type) {
      if(is_conduction_active())
        do_surfels_s2v_conduction(surfel_group, first_surfel_next_pass);
    }
    //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    m_n_surfels_left -= m_n_surfels_this_pass;
    m_first_surfel_this_pass = first_surfel_next_pass;
    m_n_surfels_this_pass = MIN(m_n_surfels_left, N_SURFELS_IN_A_PASS);
    //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    consider_surrender(strand_type);
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_wsurfels_and_contact_surfels_sampling_and_dynA(SURFEL_GROUP surfel_group,
                                                                                STP_GEOM_VARIABLE group_voxel_size,
                                                                                sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                                                                SURFEL& next_surfel_to_process,
                                                                                sAVERAGING_LIMITS& averaging) 
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  //Checks if dealing with a contact surfel group, which might require averaging
  sCONTACT_ACCUMULATION* accumulation = nullptr;
  sCONTACT_SURFEL_GROUP* contact_surfel_group = (averaging.m_maybe_average) 
                                                ? static_cast<CONTACT_SURFEL_GROUP>(surfel_group)
                                                : nullptr;
  
  DO_ALL_SURFELS_THIS_PASS(surfel) {

    SURFEL_V2S_DATA surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);

#if ENABLE_SIM_COUNTERS
    if (!surfel->is_fringe() && !surfel->is_fringe2()) {
      // increment the surfel counters for that physics type
      timer_accum_counters(surfel->timer(), 0, 1);
    }
#endif

    if (averaging.m_maybe_average) {
      //checks if averaging is needed (those marked between primary_averaged and secondary averaged)
      if (surfel == averaging.m_begin) {
        averaging.m_fill_average = TRUE;
      } else if (surfel == averaging.m_end) {
        averaging.m_maybe_average = FALSE;
        averaging.m_fill_average = FALSE;
      }
      //updates averaging pointer if active
      accumulation = (averaging.m_fill_average) ? contact_surfel_group->next_accumulation() : nullptr;
    }

    if (!surfel->is_even_or_odd()) {
      wsurfel_and_conduction_surfel_sampling_and_dynA(surfel, m_active_solver_mask, surfel_v2s_data, accumulation, group_voxel_size, FALSE);
    } 
    else { 
      BOOLEAN is_surfel_odd = surfel->is_odd();
      // SS-CHECKING: See comment in do_surfels_dyn_flow
      if (is_surfel_odd) {
        if (m_odd_active_solver_mask) {
          wsurfel_and_conduction_surfel_sampling_and_dynA(surfel, m_odd_active_solver_mask, surfel_v2s_data, accumulation, group_voxel_size, FALSE);
        }
      } 
      else {
        if (m_even_active_solver_mask) {
          wsurfel_and_conduction_surfel_sampling_and_dynA(surfel, m_even_active_solver_mask, surfel_v2s_data, accumulation, group_voxel_size, FALSE);
        }
      }
    }

  }
  next_surfel_to_process = surfel;
#endif
}

VOID sSURFEL_PROCESS_CONTROL::do_update_averaged_contact_counters(CONTACT_SURFEL_GROUP contact_surfel_group) {
  //Loops through the accumulators in the group, and updates the counter
  for (auto& averaging_accumulation : contact_surfel_group->m_averaged_accumulation) {
    size_t idx_avg = averaging_accumulation.averager_index();
    //Accumulate related physical quantities for averaging, but only when accumulation is active
    if (g_thermal_averaged_contacts[idx_avg].is_averaging()) {
      g_thermal_averaged_contacts.decrease_count();
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_wsurfels_hfc_and_dynB(STP_GEOM_VARIABLE group_voxel_size,
                                                       STP_GEOM_VARIABLE meas_scale_factor,
                                                       sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool)
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  DO_ALL_SURFELS_THIS_PASS(surfel) {

    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    
#if ENABLE_SIM_COUNTERS
    if (!surfel->is_fringe() && !surfel->is_fringe2()) {
      // increment the surfel counters for that physics type
      timer_accum_counters(surfel->timer(), 0, 1);
    }
#endif

    if (!surfel->is_even_or_odd()) {
      wsurfel_and_contact_surfels_hfc_and_dynB(surfel, CONTACT_WSURFEL, m_active_solver_mask, m_active_solver_mask, 
                                               surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE);
    } 
    else {
      BOOLEAN is_surfel_odd = surfel->is_odd();
      // SS-CHECKING: See comment in do_surfels_dyn_flow
      if (is_surfel_odd) {
        if (m_odd_active_solver_mask) {
          wsurfel_and_contact_surfels_hfc_and_dynB(surfel, CONTACT_WSURFEL, m_odd_active_solver_mask, m_active_solver_mask, 
                                                   surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE); 
        }
      } 
      else {
        if (m_even_active_solver_mask) {
          wsurfel_and_contact_surfels_hfc_and_dynB(surfel, CONTACT_WSURFEL, m_even_active_solver_mask, m_active_solver_mask, 
                                                   surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE); 
        }
      }
    }
  }
#endif
}

VOID sSURFEL_PROCESS_CONTROL::do_contact_surfels_hfc_and_dynB(STP_GEOM_VARIABLE group_voxel_size,
                                                              STP_GEOM_VARIABLE meas_scale_factor,
                                                              CONTACT_SURFEL_GROUP contact_surfel_group,
                                                              CONTACT_SURFEL_TYPE &contact_surfel_type,
                                                              sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                                              SURFEL &tail_surfel_this_type) 
{
#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  DO_ALL_SURFELS_THIS_PASS(surfel) {

    sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(n_surfels_processed);
    
#if ENABLE_SIM_COUNTERS
      if (!surfel->is_fringe() && !surfel->is_fringe2()) {
        // increment the surfel counters for that physics type
        timer_accum_counters(surfel->timer(), 0, 1);
      }
#endif
    
    if (!surfel->is_even_or_odd()) {
      wsurfel_and_contact_surfels_hfc_and_dynB(surfel, contact_surfel_type, m_active_solver_mask, m_active_solver_mask, 
                                               surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE);
    } 
    else { // even/odd surfel
      BOOLEAN is_surfel_odd = surfel->is_odd();
      // SS-CHECKING: For sub-/super-cycled cases, even/odd time-step
      // is solver dependent. We can eliminate unnecessary call using 
      // m_(even|odd)_active_solver_mask
      if (is_surfel_odd) {
        if (m_odd_active_solver_mask) {
          wsurfel_and_contact_surfels_hfc_and_dynB(surfel, contact_surfel_type, m_odd_active_solver_mask, m_active_solver_mask, 
                                                   surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE);  
        } else {
          contact_surfel_group->skip_contact_surfel(contact_surfel_type, surfel); //updates indexes within the group
        }
      } 
      else {
        if (m_even_active_solver_mask) {
          wsurfel_and_contact_surfels_hfc_and_dynB(surfel, contact_surfel_type, m_even_active_solver_mask, m_active_solver_mask, 
                                                   surfel_v2s_data, group_voxel_size, meas_scale_factor, FALSE); 
        } else {
          contact_surfel_group->skip_contact_surfel(contact_surfel_type, surfel); //updates indexes within the group
        }
      }
    }
    //If reached the tail of this type, moves to the next type. We might have reached the last surfel of the 
    //group, so account for the case wheen there is no next
    if (surfel == tail_surfel_this_type) {
      for (int type = (int)contact_surfel_type+1; type<NUM_CONTACT_SURFEL_TYPES; type++) {
        contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(type);
        if (contact_surfel_group->shob_head_ptr(contact_surfel_type) != NULL) {
          tail_surfel_this_type = contact_surfel_group->shob_tail_ptr(contact_surfel_type);
          break;
        };
      }
    }
  }
#endif
}


sSURFEL_PROCESS_CONTROL::sAVERAGING_LIMITS::sAVERAGING_LIMITS(SURFEL_GROUP surfel_group) {
  m_maybe_average = (surfel_group->m_supertype == CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE);
  m_fill_average = FALSE; //set when processing the starting surfel
  if (m_maybe_average) {
    sCONTACT_SURFEL_GROUP* contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(surfel_group);
    contact_surfel_group->get_averaged_head_tail_ptrs(m_begin, m_end);
    if (m_end != NULL) {
      contact_surfel_group->reset_next_averaged_contact_index();
      m_end = m_end->m_next;
    } else {
      m_maybe_average = FALSE;
    }
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::process_all_wsurfels_and_contact_surfels_A_internal(sSURFEL_BASE_FSET::GROUP group,
                                                                                  STRAND strand_type)
{
  
  SURFEL_GROUP surfel_group = static_cast<SURFEL_GROUP>(group);
  asINT32 n_surfels = surfel_group->n_shob_ptrs();
  //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
  if (n_surfels == 0) { 
    return;
  }

  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d supertype %d n_wsurfels %d", g_timescale.m_time, g_running_strand, surfel_group->m_supertype, n_surfels);
  STP_SCALE scale = surfel_group->m_scale;
  STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();
  
  m_first_surfel_this_pass = surfel_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfels, N_SURFELS_IN_A_PASS);
  m_n_surfels_left = n_surfels;
  SURFEL first_surfel_next_pass = NULL;
  
  SP_TIMER_TYPE s2s_timer_type;
  SP_TIMER_TYPE v2s_timer_type;
  SP_TIMER_TYPE sdyn_timer_type;
  if (strand_type == INTERIOR_WSURF_SAMPLING_STRAND || strand_type == INTERIOR_NS_A_STRAND) {
    s2s_timer_type = SP_S2S_TIMER;
    v2s_timer_type = SP_V2S_TIMER;
    sdyn_timer_type = m_first_surfel_this_pass->timer();
  } else if (strand_type == FRINGE_WSURF_SAMPLING_STRAND || strand_type == FRINGE_SURF_STRAND) {
    s2s_timer_type = SP_FRINGE_S2S_TIMER;
    v2s_timer_type = SP_FRINGE_V2S_TIMER;
    sdyn_timer_type = SP_FRINGE_SURFEL_DYN_TIMER;
  } else if (strand_type == FRINGE2_SURF_STRAND) {
    s2s_timer_type = SP_FRINGE2_S2S_TIMER;
    v2s_timer_type = SP_FRINGE2_V2S_TIMER;
    sdyn_timer_type = SP_FRINGE2_SURFEL_DYN_TIMER;
  } else {
    s2s_timer_type = SP_INVALID_TIMER;
    v2s_timer_type = SP_INVALID_TIMER;
    sdyn_timer_type = SP_INVALID_TIMER;
    msg_internal_error("Unexpected surfel strand type %d", strand_type);
  }

  //Fill the averaging limits, which checks if dealing with a contact surfel group that involve require averaging
  sAVERAGING_LIMITS averaging_limits(surfel_group);

  // TODO: Get UDS solver working with wsurfels
  static constexpr BOOLEAN UDS_LB_SOLVER_FALSE = FALSE;
    
  while (m_first_surfel_this_pass != NULL) {
    MAYBE_WITH_TIMER(TRUE, s2s_timer_type) {
      // memset(g_surfel_v2s_datasets, 0, m_n_surfels_this_pass * sizeof(sSURFEL_V2S_DATA));
      v2s_mem_pool.clear(m_n_surfels_this_pass);
      if (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
        surfels_clear_and_s2s_flow<IS_T_LB_SOLVER_ON, UDS_LB_SOLVER_FALSE>(surfel_group, &v2s_mem_pool);
      } else {
        surfels_clear_and_s2s_conduction(surfel_group, &v2s_mem_pool);
      }
    }
    MAYBE_WITH_TIMER(TRUE, v2s_timer_type) {
      if (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
        do_surfels_v2s_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, UDS_LB_SOLVER_FALSE, IS_ALL_SOLVER_TS_SAME>(surfel_group, &v2s_mem_pool);
      } else {
        do_surfels_v2s_conduction(surfel_group);
      }
    }
    MAYBE_WITH_TIMER(TRUE, sdyn_timer_type) {
      do_wsurfels_and_contact_surfels_sampling_and_dynA(surfel_group, group_voxel_size, &v2s_mem_pool, 
                                                        first_surfel_next_pass, averaging_limits);
    }
    //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    m_n_surfels_left -= m_n_surfels_this_pass;
    m_first_surfel_this_pass = first_surfel_next_pass;
    m_n_surfels_this_pass = MIN(m_n_surfels_left, N_SURFELS_IN_A_PASS);
    //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    consider_surrender(strand_type);
  }
  //Done with all surfels, update counters for averaged contact
  if (group->m_supertype == CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE) {
    CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(surfel_group);
    do_update_averaged_contact_counters(contact_surfel_group);
  } 
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME, BOOLEAN IS_WSURFEL>
VOID sSURFEL_PROCESS_CONTROL::process_all_wsurfels_and_contact_surfels_B_internal(sSURFEL_BASE_FSET::GROUP group,
                                                                                  STRAND strand_type)
{
  SURFEL_GROUP surfel_group = static_cast<SURFEL_GROUP>(group);
  asINT32 n_surfels = surfel_group->n_shob_ptrs();
  //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
  if (n_surfels == 0) { 
    return;
  }

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();
  
  STP_SCALE scale = surfel_group->m_scale;
  STP_GEOM_VARIABLE group_voxel_size = sim_scale_to_voxel_size(scale);
  STP_GEOM_VARIABLE meas_scale_factor = (STP_GEOM_VARIABLE)
                                        ((sINT64)1 << (sim.num_dims * (sim.num_scales - scale - 1)));

  m_first_surfel_this_pass = surfel_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfels, N_SURFELS_IN_A_PASS);
  m_n_surfels_left = n_surfels;
  SURFEL first_surfel_next_pass = NULL;
  
  SP_TIMER_TYPE sdyn_timer_type;
  SP_TIMER_TYPE s2v_timer_type;
  if (strand_type == INTERIOR_NS_A_STRAND) {
    sdyn_timer_type = m_first_surfel_this_pass->timer();
    s2v_timer_type = SP_S2V_TIMER;
  } else if (strand_type == FRINGE_SURF_STRAND) {
    sdyn_timer_type = SP_FRINGE_SURFEL_DYN_TIMER;
    s2v_timer_type = SP_FRINGE_S2V_TIMER;
  } else if (strand_type == FRINGE2_WSURF_HFC_STRAND || strand_type == FRINGE2_SURF_STRAND) {
    sdyn_timer_type = SP_FRINGE2_SURFEL_DYN_TIMER;
    s2v_timer_type = SP_FRINGE2_S2V_TIMER;
  } else {
    sdyn_timer_type = SP_INVALID_TIMER;
    s2v_timer_type = SP_INVALID_TIMER;
    msg_internal_error("Unexpected surfel strand type %d", strand_type);
  } 

  if constexpr (IS_WSURFEL) {
    // TODO: Get UDS solver working with wsurfels
    static constexpr BOOLEAN UDS_LB_SOLVER_FALSE = FALSE;
    while (m_first_surfel_this_pass != NULL) {
      MAYBE_WITH_TIMER(TRUE, sdyn_timer_type) {
        do_wsurfels_hfc_and_dynB(group_voxel_size, meas_scale_factor, &v2s_mem_pool);
      }
      MAYBE_WITH_TIMER(TRUE,s2v_timer_type) {
        if (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE) {
          do_surfels_s2v_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, UDS_LB_SOLVER_FALSE, IS_ALL_SOLVER_TS_SAME>(surfel_group, &v2s_mem_pool, first_surfel_next_pass);
        } else {
          if(is_conduction_active())
            do_surfels_s2v_conduction(surfel_group, first_surfel_next_pass);
        }
      }
      
      //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
      m_n_surfels_left -= m_n_surfels_this_pass;
      m_first_surfel_this_pass = first_surfel_next_pass;
      m_n_surfels_this_pass = MIN(m_n_surfels_left, N_SURFELS_IN_A_PASS);
      //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
      consider_surrender(strand_type);
    }
  }
  else { //CONTACT_SURFEL
    CONTACT_SURFEL_GROUP contact_surfel_group = static_cast<CONTACT_SURFEL_GROUP>(surfel_group);
    //first, needs to identify the surfel type corresponding to the first one processed
    CONTACT_SURFEL_TYPE contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>(0);
    while(contact_surfel_group->shob_head_ptr(contact_surfel_type)==NULL) {
      contact_surfel_type = static_cast<CONTACT_SURFEL_TYPE>((int)contact_surfel_type + 1);
    }
    SURFEL tail_surfel_this_type = contact_surfel_group->shob_tail_ptr(contact_surfel_type);
    //initializes the local index to loop through contact weights
    contact_surfel_group->reset_processed_contact_counters();
    //ready to process all surfels in the group
    while (m_first_surfel_this_pass != NULL) {
      MAYBE_WITH_TIMER(TRUE, sdyn_timer_type) {
        do_contact_surfels_hfc_and_dynB(group_voxel_size, meas_scale_factor, contact_surfel_group, contact_surfel_type,
                                        &v2s_mem_pool, tail_surfel_this_type);
      }
      MAYBE_WITH_TIMER(TRUE,s2v_timer_type) {
        if(is_conduction_active())
          do_surfels_s2v_conduction(surfel_group, first_surfel_next_pass);
      }
      //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
      m_n_surfels_left -= m_n_surfels_this_pass;
      m_first_surfel_this_pass = first_surfel_next_pass;
      m_n_surfels_this_pass = MIN(m_n_surfels_left, N_SURFELS_IN_A_PASS);
      //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
      consider_surrender(strand_type);
    }
  }
}

template <BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_s2s_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                                       sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool) {
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {
#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus s2s counters could increase in surfel_s2s_advect() even if we don't want to. Because we only 
    // time interior surfel pairs (meaning both surfels are interior), we should do the same for the 
    // counters, meaning that we should disable the counter for interior surfels in fringe surfel pairs. 
    // It is hard to get information of another surfel in the surfel pair in surfel_s2s_advect(), so we 
    // do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
      sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(surfel_index);
      ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? this->m_even_active_solver_mask : this->m_active_solver_mask;
      surfel_clear_and_s2s_flow<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(this, surfel, surfel_v2s_data, s2s_active_solver_mask);

      if (surfel->is_even())
        cassert(!surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights());
      if (surfel->is_odd() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
        sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
        surfel->pre_advect_init_copy_even_to_odd(m_active_solver_mask, even_surfel);
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_s2s_conduction(SURFEL_PAIR_GROUP surfel_pair_group) {
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {
#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus s2s counters could increase in surfel_s2s_advect() even if we don't want to. Because we only 
    // time interior surfel pairs (meaning both surfels are interior), we should do the same for the 
    // counters, meaning that we should disable the counter for interior surfels in fringe surfel pairs. 
    // It is hard to get information of another surfel in the surfel pair in surfel_s2s_advect(), so we 
    // do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);
      ACTIVE_SOLVER_MASK s2s_active_solver_mask = surfel->is_even() ? this->m_even_active_solver_mask : this->m_active_solver_mask;
      surfel_clear_and_s2s_conduction(this, surfel, s2s_active_solver_mask);

      if (surfel->is_even())
        cassert(!surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights());
      if (surfel->is_odd() && surfel->even_odd_data()->m_clone_surfel.has_no_v2s_weights()) {
        sSURFEL *even_surfel = surfel->even_odd_data()->m_clone_surfel.clone_surfel();
        surfel->pre_advect_init_copy_even_to_odd(m_active_solver_mask, even_surfel);
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_v2s_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                                       sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool) {
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {

#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus v2s counters could increase in surfel_v2s_advect() even if we don't want to. Because we only 
    // time interior surfel pairs (meaning both surfels are interior), we should do the same for the 
    // counters, meaning that we should disable the counter for interior surfels in fringe surfel pairs. 
    // It is hard to get information of another surfel in the surfel pair in surfel_v2s_advect(), so we 
    // do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);

      if (is_any_flow_solver_active()) {
        //LETSPLAYHIDEANDSEEK
        sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(surfel_index);
        if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
          uINT32 allow_scale_diff = 0;
          asINT32 allow_phase_mask = m_full_phase_masks;
          surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, IS_SLRF, TRUE>(surfel, surfel_v2s_data,
            is_time_step_odd(), m_active_solver_mask, allow_scale_diff, allow_phase_mask, m_prior_solver_index_mask);
        } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
          surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_SLRF, IS_ALL_SOLVER_TS_SAME>(
            surfel, surfel_v2s_data, is_time_step_odd(), m_active_solver_mask, m_allowed_v2s_scale_diff,
            m_solver_phase_masks, m_prior_solver_index_mask);
        } else { // even/odd surfel
	        ACTIVE_SOLVER_MASK even_odd_v2s_active_solver_mask = !surfel->is_odd()
                                                               ? m_even_active_solver_mask
                                                               : (m_even_active_solver_mask | m_odd_active_solver_mask);
          even_odd_surfel_v2s<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_SLRF, IS_ALL_SOLVER_TS_SAME>(
            surfel, NULL, surfel->is_odd(), is_time_step_odd(), even_odd_v2s_active_solver_mask,
            m_allowed_v2s_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask, FALSE);
        }
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
}

template <BOOLEAN IS_SLRF>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_v2s_conduction(SURFEL_PAIR_GROUP surfel_pair_group) {
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {

#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus v2s counters could increase in surfel_v2s_advect() even if we don't want to. Because we only 
    // time interior surfel pairs (meaning both surfels are interior), we should do the same for the 
    // counters, meaning that we should disable the counter for interior surfels in fringe surfel pairs. 
    // It is hard to get information of another surfel in the surfel pair in surfel_v2s_advect(), so we 
    // do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);

      if (is_conduction_active() && surfel->is_conduction_surface()) { //conduction open shells do not participate in v2s
        if (!surfel->is_even_or_odd()) {
          if (!surfel->interacts_with_vr_ublks()) {
            uINT32 allowed_scale_diff = 0;
            asINT32 allowed_phase_mask = FULL_PHASE_MASK & (~(STP_S2V_PHASE_MASK));
            conduction_surfel_v2s<TRUE, IS_SLRF>(surfel, is_conduction_time_step_odd(), m_active_solver_mask,
                                               allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
          } else {
            asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                       ? STP_EVEN_V2S_PHASE_MASK
                                       : STP_ODD_V2S_PHASE_MASK;
            conduction_surfel_v2s<FALSE, IS_SLRF>(surfel, is_conduction_time_step_odd(), m_active_solver_mask,
              m_allowed_v2s_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
          }
        } else { // even/odd surfel
          asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                       ? STP_EVEN_V2S_PHASE_MASK
                                       : STP_ODD_V2S_PHASE_MASK;
          even_odd_conduction_surfel_v2s<IS_SLRF>(surfel, is_conduction_time_step_odd(), m_even_active_solver_mask,
            m_odd_active_solver_mask, m_allowed_v2s_scale_diff, allowed_phase_mask, m_prior_solver_index_mask, FALSE);
        }
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_SLRF>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_dyn_flow(SCALE scale,
                                                       dFLOAT one_over_delta_t,
                                                       SURFEL_PAIR_GROUP surfel_pair_group,
                                                       sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                                       SP_TIMER_TYPE surfel_pair_timer) {

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {

#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus surf dyn counters could increase in flow_surfel_dynamics() even if we don't want to. 
    // Because we only time interior surfel pairs (meaning both surfels are interior), we should do the 
    // same for the counters, meaning that we should disable the counter for interior surfels in fringe 
    // surfel pairs. It is hard to get information of another surfel of the surfel pair in 
    // flow_surfel_dynamics(), so we do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    asINT32 surfel_index = 2 * n_surfel_pairs_processed;
    SFL_V2S_DATA_PAIR_PTR surfel_pair_v2s_data(v2s_mem_pool->v2s_data(surfel_index));
    SURFEL int_surfel = surfel_pair->m_interior_surfel;
    SURFEL ext_surfel = surfel_pair->m_exterior_surfel;
    if (int_surfel->has_mirror()) {
      //msg_internal_error("surfel pair mirror not implemented");
    }
    if (ext_surfel->has_mirror()) {
      //msg_internal_error("surfel pair mirror not implemented");
    }

    if (!int_surfel->is_even_or_odd()) {
#if ENABLE_SIM_COUNTERS
      if (!surfel_pair->is_fringe() && !surfel_pair->is_fringe2())
        timer_accum_counters(surfel_pair_timer, 0 , 1);
#endif
      if constexpr(IS_SLRF) {
        slrf_surfel_dynamics_flow(surfel_pair, &sim.lrf_physics_descs[int_surfel->ref_frame_index()], scale,
                                  one_over_delta_t, m_active_solver_mask, surfel_pair_v2s_data, m_time_step_index_mask);
      } else {
        isurfel_dynamics(surfel_pair, m_active_solver_mask, m_active_solver_mask, surfel_pair_v2s_data);
      }
    } else {
      // SS-CYCLING: See comment in do_surfels_dyn_flow
      BOOLEAN is_surfel_odd = int_surfel->is_odd();

      // SS-CYCLING: SLRF surfel dynamics calls will need to be
      // changed similarly to how other surfel dynamics calls 
      // (rather the active solver mask passed) were modified.
      // For example, IS_LB_ACTIVE may not be correct representation
      // of whether lb is active or not. Similarly for other solvers.
      if ((is_time_step_odd() && is_surfel_odd) || (is_time_step_even() && !is_surfel_odd)) {
        ACTIVE_SOLVER_MASK solver_mask = is_surfel_odd ? m_odd_active_solver_mask : m_even_active_solver_mask;
#if ENABLE_SIM_COUNTERS
        if (!surfel_pair->is_fringe() && !surfel_pair->is_fringe2())
          timer_accum_counters(surfel_pair_timer, 0 , 1);
#endif
        if constexpr(IS_SLRF) {
          slrf_surfel_dynamics_flow(surfel_pair, &sim.lrf_physics_descs[int_surfel->ref_frame_index()], scale,
                                  one_over_delta_t, solver_mask, surfel_pair_v2s_data, m_coarse_time_step_index_mask);
        } else {
          isurfel_dynamics(surfel_pair, solver_mask, m_active_solver_mask, surfel_pair_v2s_data);
        }
      }
    }

#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
#endif
}

template <BOOLEAN IS_SLRF>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_dyn_conduction(SCALE scale,
                                                             SURFEL_PAIR_GROUP surfel_pair_group,
                                                             SP_TIMER_TYPE surfel_pair_timer) {

#if BUILD_D19_LATTICE || BUILD_D39_LATTICE
  if constexpr(!IS_SLRF) {
    msg_error("isurfel_dynamics not implemented for conduction surfels");
  }
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {
#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus surf dyn counters could increase in flow_surfel_dynamics() even if we don't want to. 
    // Because we only time interior surfel pairs (meaning both surfels are interior), we should do the 
    // same for the counters, meaning that we should disable the counter for interior surfels in fringe 
    // surfel pairs. It is hard to get information of another surfel of the surfel pair in 
    // flow_surfel_dynamics(), so we do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif

    asINT32 surfel_index = 2 * n_surfel_pairs_processed;
    SURFEL int_surfel = surfel_pair->m_interior_surfel;
    if (int_surfel->has_mirror()) {
      //msg_internal_error("surfel pair mirror not implemented");
    }
    if (surfel_pair->m_exterior_surfel->has_mirror()) {
      //msg_internal_error("surfel pair mirror not implemented");
    }

    if (!int_surfel->is_even_or_odd()) {
#if ENABLE_SIM_COUNTERS
      if (!surfel_pair->is_fringe() && !surfel_pair->is_fringe2())
        timer_accum_counters(surfel_pair_timer, 0 , 1);
#endif
      slrf_surfel_dynamics_conduction(surfel_pair, &sim.lrf_physics_descs[int_surfel->ref_frame_index()], scale,
                                      m_active_solver_mask, FALSE);
    } else {
      BOOLEAN is_surfel_odd = int_surfel->is_odd();
      if ((is_time_step_odd() && is_surfel_odd) || (is_time_step_even() && !is_surfel_odd)) {
        ACTIVE_SOLVER_MASK solver_mask = is_surfel_odd ? m_odd_active_solver_mask : m_even_active_solver_mask;
#if ENABLE_SIM_COUNTERS
        if (!surfel_pair->is_fringe() && !surfel_pair->is_fringe2())
          timer_accum_counters(surfel_pair_timer, 0 , 1);
#endif
        slrf_surfel_dynamics_conduction(surfel_pair, &sim.lrf_physics_descs[int_surfel->ref_frame_index()], scale,
                                  solver_mask, FALSE);
      }
    }

#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
#endif
}

// Only the last operation within this pass updates first_surfel_pair_next_pass
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_s2v_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                                  sSURFEL_V2S_DATA_MEM_POOL* v2s_mem_pool,
                                                  SURFEL_PAIR& next_surfel_pair_to_process) { // first surfel pair to process in the next pass
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {
#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus surf s2v counters could increase in surfel_s2v_advect() even if we don't want to. 
    // Because we only time interior surfel pairs (meaning both surfels are interior), we should do the 
    // same for the counters, meaning that we should disable the counter for interior surfels in fringe 
    // surfel pairs. It is hard to get information of another surfel of the surfel pair in 
    // surfel_s2v_advect(), so we do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif
    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);

      if (is_any_flow_solver_active()) {
        sSURFEL_V2S_DATA* surfel_v2s_data = v2s_mem_pool->v2s_data(surfel_index);

        if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
          uINT32 allowed_scale_diff = 0;
          asINT32 allowed_phase_mask = m_full_phase_masks;
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, IS_SLRF, TRUE>(surfel, surfel_v2s_data,
            m_active_solver_mask, allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_SLRF, IS_ALL_SOLVER_TS_SAME>(
            surfel, surfel_v2s_data, m_active_solver_mask, m_allowed_s2v_scale_diff, m_solver_phase_masks,
            m_prior_solver_index_mask);
        } else { // even/odd flow surfel
        	ACTIVE_SOLVER_MASK even_odd_s2v_active_solver_mask = surfel->is_odd() 
                                                             ? m_odd_active_solver_mask
                                                             : (m_even_active_solver_mask | m_odd_active_solver_mask);
          even_odd_surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_SLRF, IS_ALL_SOLVER_TS_SAME>(
              surfel, NULL, surfel->is_odd(), even_odd_s2v_active_solver_mask, m_allowed_s2v_scale_diff,
              m_solver_phase_masks, m_prior_solver_index_mask, m_coarse_time_step_index_mask);
        }
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
  next_surfel_pair_to_process = surfel_pair;
}

// Only the last operation within this pass updates first_surfel_pair_next_pass
template <BOOLEAN IS_SLRF>
VOID sSURFEL_PROCESS_CONTROL::do_surfel_pairs_s2v_conduction(SURFEL_PAIR_GROUP surfel_pair_group,
                                                             SURFEL_PAIR& next_surfel_pair_to_process) {
  DO_ALL_SURFEL_PAIRS_THIS_PASS(surfel_pair) {
#if ENABLE_SIM_COUNTERS
    // For fringe surfel pairs mark both surfels fringe first and restore the fringe flags after. 
    // This step is necessary since it is possible that one surfel is fringe while another is interior. 
    // Thus surf s2v counters could increase in surfel_s2v_advect() even if we don't want to. 
    // Because we only time interior surfel pairs (meaning both surfels are interior), we should do the 
    // same for the counters, meaning that we should disable the counter for interior surfels in fringe 
    // surfel pairs. It is hard to get information of another surfel of the surfel pair in 
    // surfel_s2v_advect(), so we do the hack here.
    BOOLEAN is_int_surfel_fringe = surfel_pair->m_interior_surfel->is_fringe();
    BOOLEAN is_ext_surfel_fringe = surfel_pair->m_exterior_surfel->is_fringe();
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(TRUE);
      surfel_pair->m_exterior_surfel->set_fringe(TRUE);
    }
#endif
    ccDOTIMES(s, 2) {
      asINT32 surfel_index = 2 * n_surfel_pairs_processed + s;
      SURFEL surfel = ((s == 0) ? surfel_pair->m_exterior_surfel : surfel_pair->m_interior_surfel);

      if (is_conduction_active() && surfel->is_conduction_surface()) { //conduction open shells do not participate in s2v
        if (!surfel->is_even_or_odd()) {
          if (!surfel->interacts_with_vr_ublks()) {
            uINT32 allowed_scale_diff = 0;
            asINT32 allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
            conduction_surfel_s2v<TRUE, IS_SLRF>(surfel, m_active_solver_mask, allowed_scale_diff,
                                                 allowed_phase_mask, m_prior_solver_index_mask);
          } else {
            asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                         ? STP_EVEN_S2V_PHASE_MASK
                                         : STP_ODD_S2V_PHASE_MASK;
            conduction_surfel_s2v<FALSE, IS_SLRF>(surfel, m_active_solver_mask, m_allowed_s2v_scale_diff,
                                                  allowed_phase_mask, m_prior_solver_index_mask);
          }
        } else { // even/odd surfel
          asINT32 allowed_phase_mask = !is_conduction_time_step_odd()
                                       ? STP_EVEN_S2V_PHASE_MASK
                                       : STP_ODD_S2V_PHASE_MASK;
          even_odd_conduction_surfel_s2v<IS_SLRF>(surfel, m_even_active_solver_mask, m_odd_active_solver_mask,
                                                m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask,
                                                m_coarse_time_step_index_mask);
        }
      }
    }
#if ENABLE_SIM_COUNTERS
    if (surfel_pair->is_fringe()) { // At least one surfel is fringe
      surfel_pair->m_interior_surfel->set_fringe(is_int_surfel_fringe);
      surfel_pair->m_exterior_surfel->set_fringe(is_ext_surfel_fringe);
    }
#endif
  }
  next_surfel_pair_to_process = surfel_pair;
}

template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_SLRF, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::process_all_surfel_pairs_flow(SURFEL_PAIR_GROUP surfel_pair_group,
                                                            STRAND strand_type) {
  STP_SCALE scale = surfel_pair_group->m_scale;

  dFLOAT one_over_delta_t = 1.0F / scale_to_delta_t(scale);

  asINT32 n_surfel_pairs = surfel_pair_group->n_shob_ptrs();
  m_first_surfel_pair_this_pass = surfel_pair_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfel_pairs, N_SURFEL_PAIRS_IN_A_PASS);
  m_n_surfel_pairs_left = n_surfel_pairs;
  SURFEL_PAIR first_surfel_pair_next_pass = NULL;

  // Only INTERIOR_NS strand is dealing with interior surfel pairs
  BOOLEAN is_interior_surfel_base_group = (strand_type == INTERIOR_NS_A_STRAND ||
                                           strand_type == INTERIOR_NS_B_STRAND);

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();
  
  while (m_first_surfel_pair_this_pass != NULL) {

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_S2S_TIMER) {
      //memset(g_surfel_v2s_datasets, 0, m_n_surfels_this_pass * 2 * sizeof(sSURFEL_V2S_DATA));
      v2s_mem_pool.clear(m_n_surfels_this_pass * 2);
      do_surfel_pairs_s2s_flow<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(surfel_pair_group, &v2s_mem_pool);
    }

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_V2S_TIMER) {
      do_surfel_pairs_v2s_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_SLRF, IS_ALL_SOLVER_TS_SAME>
                         (surfel_pair_group, &v2s_mem_pool);
    }

    // TODO: Add a member function timer() for surfel pair
    SP_TIMER_TYPE surfel_pair_timer;    
    if constexpr(IS_SLRF) {
      surfel_pair_timer = SP_SLRF_SURFEL_DYN_TIMER;
    } else {
      // APM surfel pair group could contain different types (APM_NOSLIP, APM_ANGULAR_SLIP, etc.)
      // of surfel pairs and we can't time their dynamics separately since they are done in the same loop.
      // We choose to use the timer of the first surfel pair here. The case for collecting APM surfel pair
      // load factors should contain a single phys type so that there is no ambiguity.
      SURFEL_PAIR surfel_pair = surfel_pair_group->shob_ptrs();
      if (surfel_pair) {
        switch (surfel_pair->dynamics_type()) {                                                              \
        case APM_NOSLIP_ISURFEL_TYPE:
        case APM_LINEAR_NOSLIP_ISURFEL_TYPE:
        case APM_ANGULAR_NOSLIP_ISURFEL_TYPE:
          surfel_pair_timer =  SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
          break;
        case APM_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_STANDARD_ISURFEL_DYN_TIMER;
          break;
        case APM_LINEAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_LINEAR_ISURFEL_DYN_TIMER;
          break;
        case APM_ANGULAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_ANGULAR_ISURFEL_DYN_TIMER;
          break;
        default:
          msg_internal_error("Invalid apm isurfel dynamics type (%d)", surfel_pair->dynamics_type());
          break;
        }
      } else {
        surfel_pair_timer = SP_INVALID_TIMER; 
      }
    }
    MAYBE_WITH_TIMER(is_interior_surfel_base_group, surfel_pair_timer) {
      do_surfel_pairs_dyn_flow<IS_LB_ACTIVE, IS_SLRF>(scale, one_over_delta_t, surfel_pair_group,
                                                 &v2s_mem_pool, surfel_pair_timer);
    }

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_S2V_TIMER) {
      do_surfel_pairs_s2v_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_SLRF, IS_ALL_SOLVER_TS_SAME>
                         (surfel_pair_group, &v2s_mem_pool, first_surfel_pair_next_pass);
    }

    m_n_surfel_pairs_left -= m_n_surfels_this_pass;
    m_first_surfel_pair_this_pass = first_surfel_pair_next_pass;
    m_n_surfels_this_pass = MIN(m_n_surfel_pairs_left, N_SURFEL_PAIRS_IN_A_PASS);
    consider_surrender(strand_type);
  }
}

template <BOOLEAN IS_SLRF>
VOID sSURFEL_PROCESS_CONTROL::process_all_surfel_pairs_conduction(SURFEL_PAIR_GROUP surfel_pair_group,
                                                                  STRAND strand_type) {

  STP_SCALE scale = surfel_pair_group->m_scale;

  asINT32 n_surfel_pairs = surfel_pair_group->n_shob_ptrs();
  m_first_surfel_pair_this_pass = surfel_pair_group->shob_ptrs();
  m_n_surfels_this_pass = MIN(n_surfel_pairs, N_SURFEL_PAIRS_IN_A_PASS);
  m_n_surfel_pairs_left = n_surfel_pairs;
  SURFEL_PAIR first_surfel_pair_next_pass = NULL;

  // Only INTERIOR_NS strand is dealing with interior surfel pairs
  BOOLEAN is_interior_surfel_base_group = (strand_type == INTERIOR_NS_A_STRAND ||
                                           strand_type == INTERIOR_NS_B_STRAND);
 
  while (m_first_surfel_pair_this_pass != NULL) {

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_S2S_TIMER) {
      //memset(g_surfel_v2s_datasets, 0, m_n_surfels_this_pass * 2 * sizeof(sSURFEL_V2S_DATA));
      do_surfel_pairs_s2s_conduction(surfel_pair_group);
    }

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_V2S_TIMER) {
      do_surfel_pairs_v2s_conduction<IS_SLRF>(surfel_pair_group);
    }

    // TODO: Add a member function timer() for surfel pair
    SP_TIMER_TYPE surfel_pair_timer;    
    if constexpr(IS_SLRF) {
      surfel_pair_timer = SP_SLRF_SURFEL_DYN_TIMER;
    } else {
      // APM surfel pair group could contain different types (APM_NOSLIP, APM_ANGULAR_SLIP, etc.)
      // of surfel pairs and we can't time their dynamics separately since they are done in the same loop.
      // We choose to use the timer of the first surfel pair here. The case for collecting APM surfel pair
      // load factors should contain a single phys type so that there is no ambiguity.
      SURFEL_PAIR surfel_pair = surfel_pair_group->shob_ptrs();
      if (surfel_pair) {
        switch (surfel_pair->dynamics_type()) {                                                              \
        case APM_NOSLIP_ISURFEL_TYPE:
        case APM_LINEAR_NOSLIP_ISURFEL_TYPE:
        case APM_ANGULAR_NOSLIP_ISURFEL_TYPE:
          surfel_pair_timer =  SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
          break;
        case APM_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_STANDARD_ISURFEL_DYN_TIMER;
          break;
        case APM_LINEAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_LINEAR_ISURFEL_DYN_TIMER;
          break;
        case APM_ANGULAR_SLIP_ISURFEL_TYPE:
          surfel_pair_timer = SP_APM_ANGULAR_ISURFEL_DYN_TIMER;
          break;
        default:
          msg_internal_error("Invalid apm isurfel dynamics type (%d)", surfel_pair->dynamics_type());
          break;
        }
      } else {
        surfel_pair_timer = SP_INVALID_TIMER; 
      }
    }
    MAYBE_WITH_TIMER(is_interior_surfel_base_group, surfel_pair_timer) {
      do_surfel_pairs_dyn_conduction<IS_SLRF>(scale, surfel_pair_group, surfel_pair_timer);
    }

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_S2V_TIMER) {
      do_surfel_pairs_s2v_conduction<IS_SLRF>(surfel_pair_group, first_surfel_pair_next_pass);
    }

    m_n_surfel_pairs_left -= m_n_surfels_this_pass;
    m_first_surfel_pair_this_pass = first_surfel_pair_next_pass;
    m_n_surfels_this_pass = MIN(m_n_surfel_pairs_left, N_SURFEL_PAIRS_IN_A_PASS);
    consider_surrender(strand_type);
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_sampling_surfels_s2s(sSAMPLING_SURFEL_GROUP *surfel_group)
{
  DO_ALL_SAMPLING_SURFELS_THIS_PASS(sampling_surfel) {
    if (!sampling_surfel->is_even_or_odd()) {
      sampling_surfel->pre_advect_init(m_active_solver_mask);
      if (sampling_surfel->is_s2s_destination()) {
        surfel_s2s_meas(sampling_surfel, is_time_step_odd(),
                        sampling_surfel->even_odd_mask());
      }
    } else { // even/odd surfel
      if (m_even_active_solver_mask) {
        sampling_surfel->pre_advect_init(m_even_active_solver_mask);
      }
      if (sampling_surfel->is_s2s_destination()) {
        surfel_s2s_meas(sampling_surfel, is_time_step_odd(),
                        sampling_surfel->even_odd_mask());
      }
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_sampling_surfels_v2s(sSAMPLING_SURFEL_GROUP *surfel_group)
{
  DO_ALL_SAMPLING_SURFELS_THIS_PASS(sampling_surfel) {
    if (!sampling_surfel->is_even_or_odd()) {
      SOLVER_PHASE_MASKS allowed_phase_mask = m_full_phase_masks;
      surfel_v2s_meas(sampling_surfel, allowed_phase_mask, m_active_solver_mask);
    } else { // even/odd surfel
	surfel_v2s_meas(sampling_surfel, m_solver_phase_masks, m_active_solver_mask);
    }
  }
}

// Only the last operation within this pass updates first_sampling_surfel_next_pass
VOID sSURFEL_PROCESS_CONTROL::do_sampling_surfels_dyn(sSAMPLING_SURFEL_GROUP *surfel_group,
                                                      SAMPLING_SURFEL& next_sampling_surfel_to_process)
{
  DO_ALL_SAMPLING_SURFELS_THIS_PASS(sampling_surfel) {
    if (!sampling_surfel->is_even_or_odd()) {

#if ENABLE_SIM_COUNTERS
      // Sampling surfels could not be fringe2
      if (!sampling_surfel->is_fringe())
        timer_accum_counters(sampling_surfel->timer(), 0, 1);
#endif
      sampling_surfel_dynamics(sampling_surfel, m_active_solver_mask,
                               m_active_solver_mask, NULL);
    } else { // even/odd surfel
      BOOLEAN is_surfel_odd = sampling_surfel->is_odd();
      if (is_time_step_odd() && is_surfel_odd) {

#if ENABLE_SIM_COUNTERS
      if (!sampling_surfel->is_fringe())
        timer_accum_counters(sampling_surfel->timer(), 0, 1);
#endif
        sampling_surfel_dynamics(sampling_surfel, m_odd_active_solver_mask,
                                 m_active_solver_mask, NULL);
      }
      if (is_time_step_even() && !is_surfel_odd) {

#if ENABLE_SIM_COUNTERS
      if (!sampling_surfel->is_fringe())
        timer_accum_counters(sampling_surfel->timer(), 0, 1);
#endif
        sampling_surfel_dynamics(sampling_surfel, m_even_active_solver_mask,
                                 m_active_solver_mask, NULL);
      }
    }
  }
  next_sampling_surfel_to_process = sampling_surfel;
}

VOID sSURFEL_PROCESS_CONTROL::process_all_sampling_surfels(SAMPLING_SURFEL_GROUP surfel_group,
                                                           STRAND strand_type)
{

  STP_SCALE scale = surfel_group->m_scale;

  asINT32 n_sampling_surfels = surfel_group->n_shob_ptrs();
  m_first_sampling_surfel_this_pass = surfel_group->shob_ptrs();
  m_n_sampling_surfels_this_pass = MIN(n_sampling_surfels, N_SURFELS_IN_A_PASS);
  m_n_sampling_surfels_left = n_sampling_surfels;
  SAMPLING_SURFEL first_sampling_surfel_next_pass = NULL;
  //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);

  // Only INTERIOR_NS strand is dealing with interior surfels
  BOOLEAN is_interior_surfel_base_group = (strand_type == INTERIOR_NS_A_STRAND ||
                                           strand_type == INTERIOR_NS_B_STRAND);

  auto& v2s_mem_pool = sSURFEL_V2S_DATA_MEM_POOL::get_instance();
  while (m_first_sampling_surfel_this_pass != NULL) {

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_S2S_SAMPLING_TIMER) {
      v2s_mem_pool.clear(m_n_surfels_this_pass);
      do_sampling_surfels_s2s(surfel_group);
    }

    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_V2S_SAMPLING_TIMER) {
      do_sampling_surfels_v2s(surfel_group);
    }

    // When using surfel dyn timers, we assume that all the surfels are of the same physics type
    // Hence it is OK to get the timer using the physics type of an arbitrary surfel
    MAYBE_WITH_TIMER(is_interior_surfel_base_group, SP_SAMPLING_SURFEL_DYN_TIMER) {
      do_sampling_surfels_dyn(surfel_group, first_sampling_surfel_next_pass);
    }

    //maybe_start_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    m_n_sampling_surfels_left -= m_n_sampling_surfels_this_pass;
    m_first_sampling_surfel_this_pass = first_sampling_surfel_next_pass;
    m_n_sampling_surfels_this_pass = MIN(m_n_sampling_surfels_left, N_SURFELS_IN_A_PASS);
    //maybe_stop_timer(SP_V2S_DYN_S2V_OTHER_TIMER);
    consider_surrender(strand_type);
  }
  // sampling surfels do not send any information
}

#if !BUILD_GPU
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::process_all_surfels_internal(sSURFEL_BASE_FSET::GROUP group,
                                                           STRAND strand_type)
{
  switch (group->m_supertype) {
  case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
  {
    auto sgroup = static_cast<SURFEL_GROUP>(group);
    process_all_dyn_surfels_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME> (sgroup, strand_type);
    break;
  }
  case CONDUCTION_DYN_SURFEL_GROUP_SUPERTYPE:
  {
    SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
    process_all_dyn_surfels_conduction(sgroup, strand_type);
    break;
  }
  case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
  {
    auto slrf_group = static_cast<SURFEL_PAIR_GROUP>(group);
    if (slrf_group->m_realm == STP_FLOW_REALM)
      process_all_surfel_pairs_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, IS_ALL_SOLVER_TS_SAME> (slrf_group, strand_type);
    else
      process_all_surfel_pairs_conduction<TRUE>(slrf_group, strand_type);
    break;
  }
  case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
  {
    auto apm_group = static_cast<SURFEL_PAIR_GROUP>(group);
    if (apm_group->m_realm == STP_FLOW_REALM)
      process_all_surfel_pairs_flow<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_ALL_SOLVER_TS_SAME> (apm_group, strand_type);
    else
      process_all_surfel_pairs_conduction<FALSE>(apm_group, strand_type);
    break;
  }
  case SAMPLING_SURFEL_GROUP_SUPERTYPE:
  {
    auto sgroup = static_cast<SAMPLING_SURFEL_GROUP>(group);
    process_all_sampling_surfels(sgroup, strand_type);
    break;
  }
  case CONTACT_SURFEL_LOCAL_GROUP_SUPERTYPE:
    //Local contact don't have any dependency from other realm and all surfels involved in contact are within the 
    //same SP, so are processed in the same strand that regular surfels.
    process_all_wsurfels_and_contact_surfels_A_internal<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME>(group, strand_type);
    process_all_wsurfels_and_contact_surfels_B_internal<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_ALL_SOLVER_TS_SAME, FALSE>(group, strand_type);
    break;
  // Wsurfel & global contact surfel strands should not call this method, throw an error if so
  // case CONDUCTION_WSURFEL_GROUP_SUPERTYPE:
  // case FLOW_WSURFEL_GROUP_SUPERTYPE:
  // case CONTACT_SURFEL_GLOBAL_GROUP_SUPERTYPE:
  default:
    msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
    break;
  }
}

VOID sSURFEL_PROCESS_CONTROL::process_all_surfels_wsurfels_and_contact_surfels_B(STRAND strand_type,
                                                                                 SURFEL_BASE_GROUP_TYPE group_type) {
  BOOLEAN is_lb_on = is_lb_active();
  BOOLEAN is_T_lb_solver_on = is_temp_active() &&  sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);

  DO_WSURFEL_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_CONTACT_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    if (group->m_send_group != NULL) {
      group->m_send_group->wait_for_copying_of_send_data();
    }
    if (group->m_is_interface) {
      BOOLEAN supertype_is_wsurfel = (group->m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE) || 
                                     (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE);
      if(g_timescale.m_all_solvers_have_same_timestep) {
        if(supertype_is_wsurfel) {
          if (is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, TRUE, TRUE>(group, strand_type);
          } else if (is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, TRUE, TRUE>(group, strand_type);
          } else if (!is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, TRUE, TRUE>(group, strand_type);
          } else if (!is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, TRUE, TRUE>(group, strand_type);
          }
        } else {
          if (is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, TRUE, FALSE>(group, strand_type);
          } else if (is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, TRUE, FALSE>(group, strand_type);
          } else if (!is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, TRUE, FALSE>(group, strand_type);
          } else if (!is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, TRUE, FALSE>(group, strand_type);
          }
        }
      } else {
        if(supertype_is_wsurfel) {
          if (is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, FALSE, TRUE>(group, strand_type);
          } else if (is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, FALSE, TRUE>(group, strand_type);
          } else if (!is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, FALSE, TRUE>(group, strand_type);
          } else if (!is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, FALSE, TRUE>(group, strand_type);
          }
        } else {
          if (is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, FALSE, FALSE>(group, strand_type);
          } else if (is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, FALSE, FALSE>(group, strand_type);
          } else if (!is_lb_on && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, FALSE, FALSE>(group, strand_type);
          } else if (!is_lb_on && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, FALSE, FALSE>(group, strand_type);
          }
        }
      }
    } else {
      if(g_timescale.m_all_solvers_have_same_timestep) {
        if (is_lb_on && is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, TRUE, TRUE, TRUE>(group, strand_type);
        } else if (is_lb_on && is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, TRUE, FALSE, TRUE>(group, strand_type);
        } else if (is_lb_on && !is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, FALSE, TRUE, TRUE>(group, strand_type);
        } else if (is_lb_on && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, FALSE, FALSE, TRUE>(group, strand_type);
        } else if (!is_lb_on && is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, TRUE, TRUE, TRUE>(group, strand_type);
        } else if (!is_lb_on && is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, TRUE, FALSE, TRUE>(group, strand_type);
        } else if (!is_lb_on && !is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, FALSE, TRUE, TRUE>(group, strand_type);
        } else if (!is_lb_on && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, FALSE, FALSE, TRUE>(group, strand_type);
        }
      } else {
        if (is_lb_on && is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, TRUE, TRUE, FALSE>(group, strand_type);
        } else if (is_lb_on && is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, TRUE, FALSE, FALSE>(group, strand_type);
        } else if (is_lb_on && !is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, FALSE, TRUE, FALSE>(group, strand_type);
        } else if (is_lb_on && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<TRUE, FALSE, FALSE, FALSE>(group, strand_type);
        } else if (!is_lb_on && is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, TRUE, TRUE, FALSE>(group, strand_type);
        } else if (!is_lb_on && is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, TRUE, FALSE, FALSE>(group, strand_type);
        } else if (!is_lb_on && !is_T_lb_solver_on && is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, FALSE, TRUE, FALSE>(group, strand_type);
        } else if (!is_lb_on && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
          process_all_surfels_internal<FALSE, FALSE, FALSE, FALSE>(group, strand_type);
        }
      }
    }
    if(group->m_dest_sp.is_valid()) {
      if (group->m_send_group != NULL) {
        LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding contribution to send queue", g_timescale.m_time, g_running_strand);
        g_strand_mgr.m_surfel_send_channel.contribute_to_send_queue(m_scale, group->dest_nsp(), m_active_solver_mask);
      }
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::process_all_surfels(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type)
{

  BOOLEAN is_T_lb_solver_on = is_temp_active() &&  sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    if (group->m_is_interface) {
      continue;
    }
    if (group->m_send_group != NULL) {
      group->m_send_group->wait_for_copying_of_send_data();
    }
    if(g_timescale.m_all_solvers_have_same_timestep) {
      if (is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, TRUE, TRUE, TRUE>(group, strand_type);
      } else if (is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, TRUE, FALSE, TRUE>(group, strand_type);
      } else if (is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, FALSE, TRUE, TRUE>(group, strand_type);
      } else if (is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, FALSE, FALSE, TRUE>(group, strand_type);
      } else if (!is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, TRUE, TRUE, TRUE>(group, strand_type);
      } else if (!is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, TRUE, FALSE, TRUE>(group, strand_type);
      } else if (!is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, FALSE, TRUE, TRUE>(group, strand_type);
      } else if (!is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, FALSE, FALSE, TRUE>(group, strand_type);
      }
    } else {
      if (is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, TRUE, TRUE, FALSE>(group, strand_type);
      } else if (is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, TRUE, FALSE, FALSE>(group, strand_type);
      } else if (is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, FALSE, TRUE, FALSE>(group, strand_type);
      } else if (is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<TRUE, FALSE, FALSE, FALSE>(group, strand_type);
      } else if (!is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, TRUE, TRUE, FALSE>(group, strand_type);
      } else if (!is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, TRUE, FALSE, FALSE>(group, strand_type);
      } else if (!is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, FALSE, TRUE, FALSE>(group, strand_type);
      } else if (!is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	process_all_surfels_internal<FALSE, FALSE, FALSE, FALSE>(group, strand_type);
      }
    }

    if(group->m_dest_sp.is_valid()) {
#if 0
      if (group->m_send_group == NULL) {

        LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding contribution to send queue", g_timescale.m_time, g_running_strand);

        //g_strand_mgr.m_send_queue->add_entry(group->m_send_group, m_active_solver_mask, TRUE);
        g_strand_mgr.m_surfel_send_channel.contribute_to_send_queue(m_scale, group->dest_nsp(), m_active_solver_mask);
      } else {
        msg_internal_error("process_all_surfels: send group pointer was not moved");
      }
#else

      if (group->m_send_group != NULL) {
        LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding contribution to send queue", g_timescale.m_time, g_running_strand);

        g_strand_mgr.m_surfel_send_channel.contribute_to_send_queue(m_scale, group->dest_nsp(), m_active_solver_mask);
      }
#endif
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::process_all_wsurfels_and_contact_surfels_A(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type)
{

  BOOLEAN is_T_lb_solver_on = is_temp_active() &&  sim.is_T_S_solver_type_lb();

  // Ensure that previous data has been copied to the send buffer, and can be overwritten
  // This is probably known by construction, so this never actually waits. Eliminate
  // if possible.

  DO_WSURFEL_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_CONTACT_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    if (group->m_is_interface) {
      if (group->m_send_group != NULL) {
        group->m_send_group->wait_for_copying_of_send_data();
      }
      if(g_timescale.m_all_solvers_have_same_timestep) {
        if (is_lb_active() && is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<TRUE, TRUE, TRUE>(group, strand_type);
        } else if (is_lb_active() && !is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<TRUE, FALSE, TRUE>(group, strand_type);
        } else if (!is_lb_active() && is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<FALSE, TRUE, TRUE>(group, strand_type);
        } else if (!is_lb_active() && !is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<FALSE, FALSE, TRUE>(group, strand_type);
        }
      } else {
        if (is_lb_active() && is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<TRUE, TRUE, FALSE>(group, strand_type);
        } else if (is_lb_active() && !is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<TRUE, FALSE, FALSE>(group, strand_type);
        } else if (!is_lb_active() && is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<FALSE, TRUE, FALSE>(group, strand_type);
        } else if (!is_lb_active() && !is_T_lb_solver_on) {
          process_all_wsurfels_and_contact_surfels_A_internal<FALSE, FALSE, FALSE>(group, strand_type);
        }
      }
    }
  }
  
  LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: Just before sending wsurfel data solver_mask %d", g_timescale.m_time, g_running_strand, m_active_solver_mask);
  DO_WSURFEL_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    if(g_strand_mgr.are_wsurfels_commed(m_active_solver_mask)) {
      LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding wsurfel contribution to send queue", g_timescale.m_time, g_running_strand);
      g_strand_mgr.m_wsurfel_send_channel.contribute_to_send_queue(m_scale, send_group->dest_nsp(), m_active_solver_mask);
    }
  }
  DO_CONTACT_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding contact contribution to send queue", g_timescale.m_time, g_running_strand);
    g_strand_mgr.m_contact_send_channel.contribute_to_send_queue(m_scale, send_group->dest_nsp(), m_active_solver_mask);
  }
  
}

VOID sSURFEL_PROCESS_CONTROL::process_all_wsurfels_and_contact_surfels_B(STRAND strand_type, SURFEL_BASE_GROUP_TYPE group_type)
{
  BOOLEAN is_T_lb_solver_on = is_temp_active() &&  sim.is_T_S_solver_type_lb();
  DO_WSURFEL_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_CONTACT_SEND_GROUPS_OF_SCALE(send_group, m_scale) {
    send_group->wait_for_copying_of_send_data();
  }
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, m_scale, group_type) {
    if(group->m_is_interface) {
      if (group->m_send_group != NULL) {
        group->m_send_group->wait_for_copying_of_send_data();
      }
      BOOLEAN supertype_is_wsurfel = (group->m_supertype == CONDUCTION_WSURFEL_GROUP_SUPERTYPE) || 
                                     (group->m_supertype == FLOW_WSURFEL_GROUP_SUPERTYPE);
      if(g_timescale.m_all_solvers_have_same_timestep) {
        if(supertype_is_wsurfel) {
          if (is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, TRUE, TRUE>(group, strand_type);
          } else if (is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, TRUE, TRUE>(group, strand_type);
          } else if (!is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, TRUE, TRUE>(group, strand_type);
          } else if (!is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, TRUE, TRUE>(group, strand_type);
          }
        } else {
          if (is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, TRUE, FALSE>(group, strand_type);
          } else if (is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, TRUE, FALSE>(group, strand_type);
          } else if (!is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, TRUE, FALSE>(group, strand_type);
          } else if (!is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, TRUE, FALSE>(group, strand_type);
          }
        }
      } else {
        if(supertype_is_wsurfel) {
          if (is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, FALSE, TRUE>(group, strand_type);
          } else if (is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, FALSE, TRUE>(group, strand_type);
          } else if (!is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, FALSE, TRUE>(group, strand_type);
          } else if (!is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, FALSE, TRUE>(group, strand_type);
          }
        } else {
          if (is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, TRUE, FALSE, FALSE>(group, strand_type);
          } else if (is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<TRUE, FALSE, FALSE, FALSE>(group, strand_type);
          } else if (!is_lb_active() && is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, TRUE, FALSE, FALSE>(group, strand_type);
          } else if (!is_lb_active() && !is_T_lb_solver_on) {
            process_all_wsurfels_and_contact_surfels_B_internal<FALSE, FALSE, FALSE, FALSE>(group, strand_type);
          }
        }
      }
      if(group->m_dest_sp.is_valid()) {
        if (group->m_send_group != NULL) {
          LOG_MSG("STRAND_SWITCHING",LOG_FUNC).printf( "TS %ld Strand %d: adding contribution to send queue", g_timescale.m_time, g_running_strand);
          g_strand_mgr.m_surfel_send_channel.contribute_to_send_queue(m_scale, group->dest_nsp(), m_active_solver_mask);
        }
      }
    }
  }
}

/* GHOST SURFELS */
template <BOOLEAN IS_LB_ACTIVE, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON, BOOLEAN IS_ALL_SOLVER_TS_SAME>
VOID sSURFEL_PROCESS_CONTROL::do_all_ghost_surfels_s2v_internal(SURFEL_RECV_GROUP surfel_recv_group)

{
  uINT32 allowed_scale_diff = 0;

  for (const auto& quantum: surfel_recv_group->quantums()) {
    asINT32 allowed_phase_mask = m_full_phase_masks;

    SURFEL surfel = quantum.m_surfel;

    // mirror surfels perform S2V along with the real counterpart. However they
    // still to be part of the group, because we want to receive data from
    // the sender SP.
    if (surfel->is_mirror())
      continue;

    if (surfel->is_conduction_surface() && is_conduction_active()) {
      if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
	allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
	if (surfel->is_lrf())
          conduction_surfel_s2v<TRUE, TRUE>(surfel, m_active_solver_mask,
              allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        else
          conduction_surfel_s2v<TRUE, FALSE>(surfel, m_active_solver_mask,
              allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
      } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
        allowed_phase_mask = !is_conduction_time_step_odd()
                             ? STP_EVEN_S2V_PHASE_MASK
                             : STP_ODD_S2V_PHASE_MASK;
        if (surfel->is_lrf())
          conduction_surfel_s2v<FALSE, TRUE>(surfel, m_active_solver_mask,
              m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        else
          conduction_surfel_s2v<FALSE, FALSE>(surfel, m_active_solver_mask,
              m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);

      } else { // even/odd surfel
        allowed_phase_mask = !is_conduction_time_step_odd()
                             ? STP_EVEN_S2V_PHASE_MASK
                             : STP_ODD_S2V_PHASE_MASK;

        if (surfel->is_lrf())
          even_odd_conduction_surfel_s2v< TRUE>(surfel,
              m_even_active_solver_mask, m_odd_active_solver_mask,
              m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask,
              conduction_coarse_time_step_index());
        else
          even_odd_conduction_surfel_s2v< FALSE>(surfel,
              m_even_active_solver_mask, m_odd_active_solver_mask,
              m_allowed_s2v_scale_diff, allowed_phase_mask, m_prior_solver_index_mask,
              conduction_coarse_time_step_index());
      }
    } else if (!surfel->is_conduction_surfel() && is_any_flow_solver_active()) {
      if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
        if (surfel->is_lrf())
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, TRUE, TRUE>(
              surfel, NULL, m_active_solver_mask,
              allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);
        else
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, FALSE, TRUE>(
              surfel, NULL, m_active_solver_mask,
              allowed_scale_diff, allowed_phase_mask, m_prior_solver_index_mask);

      } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
        if (surfel->is_lrf())
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, TRUE, IS_ALL_SOLVER_TS_SAME>(
              surfel, NULL, m_active_solver_mask,
              m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask);
        else
          surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, FALSE, IS_ALL_SOLVER_TS_SAME>(
              surfel, NULL, m_active_solver_mask,
              m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask);

      } else { // even/odd surfel

        BOOLEAN is_surfel_odd = surfel->is_odd();
        ACTIVE_SOLVER_MASK even_odd_s2v_active_solver_mask = surfel->is_odd() ? m_odd_active_solver_mask : (m_even_active_solver_mask | m_odd_active_solver_mask);
        if (surfel->is_lrf())
          even_odd_surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE, IS_ALL_SOLVER_TS_SAME>(
              surfel, NULL, is_surfel_odd, even_odd_s2v_active_solver_mask,
              m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask,
              m_coarse_time_step_index_mask);
        else
          even_odd_surfel_s2v<IS_LB_ACTIVE, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE, IS_ALL_SOLVER_TS_SAME>(
              surfel, NULL, is_surfel_odd, even_odd_s2v_active_solver_mask,
              m_allowed_s2v_scale_diff, m_solver_phase_masks, m_prior_solver_index_mask,
              m_coarse_time_step_index_mask);
      }
    }
  }
}

VOID sSURFEL_PROCESS_CONTROL::do_all_ghost_surfels_s2v(STRAND strand_type)
{
  BOOLEAN is_T_lb_solver_on = is_temp_active() && sim.is_T_S_solver_type_lb();
  BOOLEAN is_uds_lb_solver_on = is_uds_active() && (sim.uds_solver_type == LB_UDS);
  DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, m_scale) {
    if(g_timescale.m_all_solvers_have_same_timestep) {
      if (!is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, FALSE, TRUE, TRUE>(surfel_recv_group);
      } 
      else if (!is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, FALSE, FALSE, TRUE>(surfel_recv_group);
      } 
      else if (!is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, TRUE, TRUE, TRUE>(surfel_recv_group);
      }
      else if (!is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, TRUE, FALSE, TRUE>(surfel_recv_group);
      }
      else if (is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, FALSE, TRUE, TRUE>(surfel_recv_group);
      }
      else if (is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, FALSE, FALSE, TRUE>(surfel_recv_group);
      }
      else if (is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, TRUE, TRUE, TRUE>(surfel_recv_group);
      }
      else if (is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, TRUE, FALSE, TRUE>(surfel_recv_group);
      }
    } else {
      if (!is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, FALSE, TRUE, FALSE>(surfel_recv_group);
      }
      else if (!is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, FALSE, FALSE, FALSE>(surfel_recv_group);
      }
      else if (!is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, TRUE, TRUE, FALSE>(surfel_recv_group);
      }
      else if (!is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<FALSE, TRUE, FALSE, FALSE>(surfel_recv_group);
      }
      else if (is_lb_active() && !is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, FALSE, TRUE, FALSE>(surfel_recv_group);
      }
      else if (is_lb_active() && !is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, FALSE, FALSE, FALSE>(surfel_recv_group);
      }
      else if (is_lb_active() && is_T_lb_solver_on && is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, TRUE, TRUE, FALSE>(surfel_recv_group);
      }
      else if (is_lb_active() && is_T_lb_solver_on && !is_uds_lb_solver_on) {
	do_all_ghost_surfels_s2v_internal<TRUE, TRUE, FALSE, FALSE>(surfel_recv_group);
      }
    }
  }
}

VOID seed_conduction_s2v(SURFEL surfel) {
  if (!surfel->is_conduction_surface()) return; //open shells do not participate in s2v 
  
  const asINT32 scale = surfel->scale();
  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale);
  uINT32 allowed_scale_diff = 0;
  asINT32 allowed_phase_mask = FULL_PHASE_MASK;

  if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
    if (surfel->is_lrf())
      conduction_surfel_s2v<TRUE, TRUE>(surfel, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask);
    else
      conduction_surfel_s2v<TRUE, FALSE>(surfel, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask);
  } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
    if (surfel->is_lrf())
      conduction_surfel_s2v<FALSE, TRUE>(surfel, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask);
    else
      conduction_surfel_s2v<FALSE, FALSE>(surfel, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask);
  } else { // even/odd surfel
    if (surfel->is_lrf())
      even_odd_conduction_surfel_s2v< TRUE>(surfel,
          CONDUCTION_PDE_ACTIVE, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask, 0);
    else
      even_odd_conduction_surfel_s2v< FALSE>(surfel,
          CONDUCTION_PDE_ACTIVE, CONDUCTION_PDE_ACTIVE,
          allowed_scale_diff, allowed_phase_mask, prior_solver_index_mask, 0);
  }
}

VOID seed_conduction_v2s(SURFEL surfel) {
  if (!surfel->is_conduction_surface()) return; //open shells do not participate in v2s
  
  const asINT32 scale = surfel->scale();
  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale);
  uINT32 allowed_scale_diff = 0;
  asINT32 allowed_phase_mask = FULL_PHASE_MASK;

  if (!surfel->is_even_or_odd() && !surfel->interacts_with_vr_ublks()) {
    conduction_surfel_v2s<TRUE, FALSE>(surfel, FALSE,
        CONDUCTION_PDE_ACTIVE, allowed_scale_diff, allowed_phase_mask,
        prior_solver_index_mask);
  } else if (!surfel->is_even_or_odd() && surfel->interacts_with_vr_ublks()) {
    conduction_surfel_v2s<FALSE, FALSE>(surfel, FALSE,
        CONDUCTION_PDE_ACTIVE, allowed_scale_diff, allowed_phase_mask,
        prior_solver_index_mask);
  } else { // even/odd surfel
    even_odd_conduction_surfel_v2s<FALSE>(surfel, FALSE,
        CONDUCTION_PDE_ACTIVE, CONDUCTION_PDE_ACTIVE, allowed_scale_diff,
        allowed_phase_mask, prior_solver_index_mask, FALSE);
  }
}
#endif
