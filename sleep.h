/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * 
 * Sleeping
 *
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_SLEEP_H
#define _SIMENG_SLEEP_H

#include "common_sp.h"

#define DEFAULT_SP_LONG_THREAD_SLEEP_NS 10000000
//This value is better for real cases but makes smoke tests very slow
#define DEFAULT_SP_SHORT_THREAD_SLEEP_NS 250000
//#define DEFAULT_SP_SHORT_THREAD_SLEEP_NS 1000
#define DEFAULT_SP_LONG_MPI_SLEEP_NS 5000000
#define DEFAULT_SP_SHORT_MPI_SLEEP_NS 100

extern BOOLEAN g_short_mpi_thread_sleep;
extern asINT32 g_sp_sleep_ns[4]; 

typedef enum {
  THREAD_SLEEP_LONG = 0,
  THREAD_SLEEP_SHORT,
  MPI_SLEEP_LONG,
  MPI_SLEEP_SHORT
} SP_SLEEP_INDEX;


VOID sp_thread_sleep(SP_SLEEP_INDEX sIndex);
VOID initialize_sleep_times();

#endif /* #ifndef _SIMENG_SLEEP_H  */
