/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

//TO_BE_DONE items need to be addressed
// Need to segregate sample surface surfels by the window they were generated for.
// Need to segregate sample surface surfels by the particle screen they were generated for.
// Eliminate the need for calls to initialize_split_voxel_window_masks(), perhaps integrate that function to a better place.


#include <map>
#include "common_sp.h"
#include "surfel_advect_sp.h"
#include "surfel_dyn_sp.h"
#include "sim.h"
#include "particle_sim.h"
#include "particle_solver_data.h"
#include "vr.h"
#include "advect.h"
#include "voxel_dyn_sp.h"
#include "parse_shob_descs.h"
#include "lattice.h"
#include "mirror.h"
#include "sp_timers.h"
#include "film_solver_stencils.h"
#include "trajectory_window.h"
#include "trajectory_meas.h"
#include "particle_sim_info.h"
#include "bsurfel_dyn_sp.h"
#include "bsurfel_util.h"
#include "particle_comm.h"
#include "film_comm.h"
#include "particle_meas_dcache.h"

#include PHYSICS_H

#define ENABLE_TRAJECTORY_MEAS

sPARTICLE_SIM particle_sim;
sPARTICLE_SUBCYCLING g_particle_subcycling_info;
sSIMUTILS_PARTICLE_TERMINAL_VELOCITY_LOOKUP_TABLE g_terminal_velocity_table;
sPARTICLE_VAR g_sp_lost_particle_mass = 0;
sPARTICLE_VAR g_sp_emitted_particle_mass = 0;
sPARTICLE_VAR g_sp_evaporated_particle_mass = 0;
asINT32 g_sp_evaporated_particles = 0;
asINT32 g_num_allowed_msg_prints_per_sp = 5;

asINT32 g_num_thermal_timesteps = 0;
asINT32 g_num_momentum_timesteps = 0;

#if DEBUG_PARTICLE_SUMMARY
asINT32 g_emitted_parcel_count = 0;
asINT32 g_parcel_dynamics_count = 0;
asINT32 g_parcel_massless_tracer_dynamics_count = 0;
asINT32 g_accumulated_parcel_count = 0;
asINT32 g_film_dynamics_count = 0;
asINT32 g_reentrained_parcel_count = 0;
asINT32 g_surface_parcel_dynamics_count = 0;
asINT32 g_numerically_reentrained_parcel_count = 0;
asINT32 g_parcel_mlrf_film_collision_count = 0;
asINT32 g_parcel_collision_count = 0;
asINT32 g_parcel_sample_surface_collision_count = 0;
asINT32 g_parcel_sample_surface_film_collision_count = 0;
asINT32 g_parcel_screen_collision_count = 0;
asINT32 g_parcel_mlrf_collision_count = 0;
asINT32 g_parcel_bsurfel_collision_count = 0;
asINT32 g_surface_parcel_send_count = 0;
asINT32 g_fluid_parcel_send_count = 0;
asINT32 g_surface_parcel_receive_count = 0;
asINT32 g_fluid_parcel_receive_count = 0;
#endif

sPARTICLE_VAR g_particle_lattice_time_correction;
sPARTICLE_VAR g_particle_one_over_lattice_time_correction;
sPARTICLE_VAR g_particle_density_scale;

sPARTICLE_VAR SURFACE_EROSION_PARAMS::erosion_ratio(
                                                    sPARTICLE_VAR U,
                                                    sPARTICLE_VAR theta) { //The angle between the surface and incident trajectory.
  sPARTICLE_VAR vol_c = 0;
  if( tan(theta) <= m_K) {
    vol_c = U * U * sin(theta) * ( 2.0 * m_K * cos(theta) - sin(theta) ) / (2.0 * m_K *m_K);
  } else {
    vol_c = std::pow(U * cos(theta), 2) / 2.0;
  }
  sPARTICLE_VAR vol_d = 0;
  sPARTICLE_VAR vol_d_arg = U * sin(theta) - m_U_tsh;
  if(vol_d_arg > 0.0)
    vol_d = 0.5 *std::pow(vol_d_arg, 2);
  sPARTICLE_VAR erosion_ratio  = m_F_s * (m_C_c * vol_c + m_C_d * vol_d);
  return erosion_ratio;
}

VOID sPARTICLE_SIM::set_film_solver_status() {
  PARTICLE_SIM_INFO parameters = &g_particle_sim_info;
  ccDOTIMES(material_index, g_particle_sim_info.num_materials) {
    PARTICLE_MATERIAL material = parameters->particle_materials[material_index];

    BOOLEAN material_is_not_for_accretion = true;
    if(sim.is_accretion_simulation) {
      material_is_not_for_accretion = parameters->ice_accretion_parameters.accretion_material_index != material_index;
    }
    
    BOOLEAN material_is_for_film_solver =  
      material->is_liquid()  && 
      (material_is_not_for_accretion || g_do_accretion_filtering_with_film_stencil);

    sim.is_film_solver |= material_is_for_film_solver; //Enable the film sovler depending on if any particle materials require it.

  }
}

VOID sPARTICLE_SIM::set_thermal_particle_solver_status() {
  sim.is_thermal_particle_solver = FALSE;
  //This will be repalced once CDI support is ready.  The thermal
  //particle solver would be activated if any particle materials have
  //thermodynamics enabled
  if(!g_use_particle_evaporation_model)
    return;

  sim.is_thermal_particle_solver = TRUE;

  //Convert some user parameters from MKS to Lattice for
  //this solver while it is still controlled by an init file.
  double user_value;
  //Convert particle temperature supplied in Kelvin to LatticeTemperature.
  user_value = g_initial_particle_temperature;
  g_initial_particle_temperature = convert_dimensional_parameter(g_initial_particle_temperature, "degK", "LatticeTemperature");
  if(my_proc_id == 0)
    msg_print("Using an initial particle temperature of %g degK (%g LatticeTemperature).",
              user_value,
              g_initial_particle_temperature);

  //This can be removed when CDI support is ready.

#if 0
  //Convert the the particle HTC provided in watt / m^2 / degK to LatticeHeatTransferCoeff
  user_value = g_fixed_particle_htc;
  g_fixed_particle_htc = convert_dimensional_parameter(g_fixed_particle_htc, "watt / m^2 / degK", "LatticeHeatTransferCoeff");
  if(my_proc_id == 0)
    msg_print("Using a fixed particle HTC of %g W / m^2 / degK (%g LatticeHeatTransferCoeff).",
              user_value,
              g_fixed_particle_htc);
#endif

  //Convert the particle specific heat to to lattice units.
  user_value =  g_particle_cp;
  g_particle_cp = convert_dimensional_parameter( g_particle_cp, " J / kg / degK", "LatticeSpecificEntropy");
  if(my_proc_id == 0)
    msg_print("Using a particle specific heat of %g J / kg / degK (%g LatticeSpecificEntropy).",
              user_value,
              g_particle_cp);

#if 0
  //Convert the fixed particle evaporation rate to lattice units
  user_value = g_fixed_particle_evaporation_rate;
  g_fixed_particle_evaporation_rate = convert_dimensional_parameter(g_fixed_particle_evaporation_rate, "kg / s / m^2", "LatticeMassFlux");
  if(my_proc_id == 0)
    msg_print("Using a fixed particle evaporation rate of %g kg/s/m^2 (%g LatticeMassFlux).",
              user_value,
              g_fixed_particle_evaporation_rate);
#endif
  //Convert the particle vapor diffusivity to lattice units.
  user_value =  g_particle_vapor_diffusivity;
  g_particle_vapor_diffusivity = convert_dimensional_parameter( g_particle_vapor_diffusivity, "m^2 / s", "LatticeKinematicViscosity");
  if(my_proc_id == 0)
    msg_print("Using a particle vapor diffusivity of %g m^2 / s (%g LatticeKinematicViscosity).",
              user_value,
              g_particle_vapor_diffusivity);

  //Convert the particle specific heat of vaporization to lattice units.
  user_value =  g_particle_specific_heat_of_vaporization;
  g_particle_specific_heat_of_vaporization= convert_dimensional_parameter( g_particle_specific_heat_of_vaporization, "J / kg", "LatticeSpecificEnthalpy");
  if(my_proc_id == 0)
    msg_print("Using a particle specific heat of vaporization of %g J / kg (%g LatticeSpecificEnthalpy).",
              user_value,
              g_particle_specific_heat_of_vaporization);

  //Convert the particle min diameter to lattice units.
  user_value =  g_particle_min_diameter;
  g_particle_min_diameter= convert_dimensional_parameter( g_particle_min_diameter, "m", "LatticeLength");
  if(my_proc_id == 0)
    msg_print("Using a particle minimum diameter of %g m (%g LatticeLength).",
              user_value,
              g_particle_min_diameter);

  //Print some sample calculations for a sanity check
#if 0
  if(my_proc_id == 0){

    double ambient_air_density = sim.char_density * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR;
    double ambient_air_temperature = sim.char_temp;
    double ambient_air_density_mks = particle_sim.convert_dimensional_parameter(ambient_air_density, "LatticeDensity", "kg / m^3");
    double ambient_air_heat_capacity_mks = particle_sim.convert_dimensional_parameter(sim.C_p, "LatticeSpecificEntropy", "J / kg / degK");
    double ambient_air_temperature_mks =  particle_sim.convert_dimensional_parameter(ambient_air_temperature, "LatticeTemperature", "degK");
    double air_thermal_diffusivity_mks = particle_sim.convert_dimensional_parameter(g_nu_molecular / g_particle_Prandtl_number, "LatticeKinematicViscosity", "m^2 / s");
    double vapor_mass_diffusivity_mks = particle_sim.convert_dimensional_parameter(g_particle_vapor_diffusivity, "LatticeKinematicViscosity", "m^2 / s");

    //Compute the ambient particle vapor phase saturation partial pressure
    //Compute the ambient particle vapor phase saturation density

    double ambient_vapor_saturation_density = evaluate_particle_vapor_phase_saturation_density(ambient_air_temperature);
    double ambient_vapor_saturation_density_mks = particle_sim.convert_dimensional_parameter(ambient_vapor_saturation_density, "LatticeDensity", "kg / m^3");
    double ambient_vapor_saturation_pressure_mks = ambient_vapor_saturation_density_mks * g_particle_vapor_gas_constant * ambient_air_temperature_mks;

    //Compute the ambient particle vapor phase saturation partial pressure
    //Compute the ambient particle vapor phase saturation density
    //Compute the ambient particle vapor phase mass fraction
    double ambient_vapor_density = ambient_vapor_saturation_density * g_particle_evaporation_relative_humidity / 100;
    double ambient_vapor_density_mks = particle_sim.convert_dimensional_parameter(ambient_vapor_density, "LatticeDensity", "kg / m^3");
    double ambient_vapor_pressure_mks = ambient_vapor_density_mks * g_particle_vapor_gas_constant * ambient_air_temperature_mks;
    double ambient_vapor_mass_fraction = ambient_vapor_density_mks / ambient_air_density_mks;


    //Compute the particle's initial vapor phase partial pressure at its surface
    //Compute the particle's initial vapor phase density at its surface
    double surface_temperature_mks = convert_dimensional_parameter(g_initial_particle_temperature, "LatticeTemperature", "degK");
    double surface_vapor_density = evaluate_particle_vapor_phase_saturation_density(g_initial_particle_temperature);
    double surface_vapor_density_mks = particle_sim.convert_dimensional_parameter(surface_vapor_density, "LatticeDensity", "kg / m^3");
    double surface_vapor_pressure_mks = surface_vapor_density_mks * g_particle_vapor_gas_constant * surface_temperature_mks ;

    //Compute the evaporation rate for a 1micron drop
    double particle_diameter_mks = 1.0e-6; //meters
    double particle_diameter = particle_sim.convert_dimensional_parameter(particle_diameter_mks, "m", "LatticeLength");


    double water_mass_fraction = 1.0;
    sPARTICLE_VAR mass_fractions[NUM_PARTICLE_MATERIAL_SPECIES  - 1];
    ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES  - 1) {
      water_mass_fraction -= g_initial_particle_mass_fractions[i + 1];
      mass_fractions[i] = g_initial_particle_mass_fractions[i + 1];
    }

    double evaporation_rate = compute_local_evaporation_rate(
                                                             0,
                                                             mass_fractions,
                                                             particle_diameter,
                                                             g_initial_particle_temperature,
                                                             ambient_air_temperature,
                                                             ambient_vapor_density);

    double evaporation_rate_mks = particle_sim.convert_dimensional_parameter(evaporation_rate, "LatticeMassFlux", "kg / s / m^2");


    std::stringstream initial_particle_species;
    initial_particle_species << water_mass_fraction << ", ";
    ccDOTIMES(i, NUM_PARTICLE_MATERIAL_SPECIES - 1) {
      initial_particle_species << g_initial_particle_mass_fractions[ i + 1];
      if(i < NUM_PARTICLE_MATERIAL_SPECIES - 2 )
        initial_particle_species << ", ";
    }

    printf("\n----Particle thermal models are enabled with the following parameters----\n\n"
           "Ambient air conditions:\n"
           "Particle vapor mass diffusivity %g (m^2/s)\n"
           "Water activity curve slope %g\n"
           "Ambient air thermal diffusivity %g (m^2/s) (Prandtl %g, nu %g (m^2/s))\n"
           "Ambient air density %g (kg/m^3)\n"
           "Ambient air heat capacity %g (J/kg/degK)\n"
           "Ambient air temperature %g (degK)\n\n"
           "Gas constant for particle vapor phase is %g (J/kg / degK)\n"
           "Ambient conditions of particle vapor phase at %g%% RH:\n"
           "Ambient vapor saturation density %g (kg/m^3) (100 %%RH, %g degK ) \n"
           "Ambient vapor saturation pressure %g (Pa) (100 %%RH, %g degK ) \n"
           "Ambient vapor pressure %g (Pa) (%g %%RH, %g degK ) \n"
           "Ambient vapor density %g (kg/m^3) (%g %%RH at %g degK ) \n"
           "Ambient vapor mass fraction %g\n\n"
           "Initial particle surface conditions:\n"
           "Initial particle surface temperature %g (degK)\n"
           "Initial particle constituent mass fractions %s\n"
           "Initial vapor pressure at surface %g (Pa) (100 %%RH, %g degK ) \n"
           "Initial vapor density at surface %g (kg/m^3) (100 %%RH, %g degK ) \n"
           "Initial evaporation rate for a particle with diameter %g (m) and temperature %g (degK) would be %g (kg/s / m^2 )\n\n",
           vapor_mass_diffusivity_mks,
           g_particle_water_activity_slope,
           air_thermal_diffusivity_mks,
           //g_nu_molecular / g_particle_Prandtl_number,
           g_particle_Prandtl_number,
           particle_sim.convert_dimensional_parameter(g_nu_molecular, "LatticeKinematicViscosity", "m^2 / s"),
           ambient_air_density_mks,
           ambient_air_heat_capacity_mks,
           ambient_air_temperature_mks,
           g_particle_vapor_gas_constant,
           g_particle_evaporation_relative_humidity,
           ambient_vapor_saturation_density_mks, ambient_air_temperature_mks,
           ambient_vapor_saturation_pressure_mks, ambient_air_temperature_mks,
           ambient_vapor_pressure_mks, g_particle_evaporation_relative_humidity, ambient_air_temperature_mks,
           ambient_vapor_density_mks, g_particle_evaporation_relative_humidity, ambient_air_temperature_mks,
           ambient_vapor_mass_fraction,
           surface_temperature_mks,
           initial_particle_species.str().c_str(),
           surface_vapor_pressure_mks, surface_temperature_mks,
           surface_vapor_density_mks, surface_temperature_mks,
           particle_diameter_mks,
           surface_temperature_mks,
           evaporation_rate_mks);
  }
#endif

  //Print the thermal timescale and RK stability limit in physical units
  sPARTICLE_VAR air_density = sim.char_density * SIMULATOR_DENSITY_TO_LATTICE_DENSITY_SCALE_FACTOR;
  sPARTICLE_VAR air_thermal_diffusivity  = g_nu_molecular  / g_particle_Prandtl_number;
  sPARTICLE_VAR min_particle_diameter = particle_sim.convert_dimensional_parameter(2e-6, "m", "LatticeLength");
  sPARTICLE_VAR water_density = particle_sim.convert_dimensional_parameter(1000, "kg/m/m/m", "LatticeDensity");
  sPARTICLE_VAR tau_thermal = min_particle_diameter * min_particle_diameter * water_density * g_particle_cp  / (g_particle_Nusselt_number * sim.C_p * air_density * air_thermal_diffusivity) / 6.0;
  sPARTICLE_VAR tau_RK = 5.0;


  sPARTICLE_VAR tau_thermal_mks = particle_sim.convert_dimensional_parameter(tau_thermal, "LatticeTime", "sec"); 
  sPARTICLE_VAR tau_RK_mks = particle_sim.convert_dimensional_parameter(tau_RK, "LatticeTime", "sec"); 
  
  int num_subcycles = ceil( tau_RK / tau_thermal);

  printf("\nParticle thermal timescale based on characteristic properties and 2um water droplet:\n");
  printf("Tau thermal = D^2 * rho_part * CP_part / (6.0 * Nusselt_part * Cp_air * rho_air * air_thermal_diffusivity)\n");
  printf("Tau_thermal = %g LatticeTime (%g sec)\n", tau_thermal, tau_thermal_mks);
  printf("Tau_RK_min = %g dt (%g sec in finest scale)\n", tau_RK, tau_RK_mks); 
  printf("Finest scale subcycles = %d ( ceil(Tau_RK_min / Tau_Thermal) )\n\n", num_subcycles);

}

double sPARTICLE_SIM::convert_dimensional_parameter(double input_value, std::string input_unit_string, std::string output_unit_string) {
  UNITS_UNIT input_units;
  UNITS_UNIT output_units;
  BOOLEAN status_ok = TRUE;
  status_ok &= UNITS_STATUS_OK == units_parse_unit(units_db, input_unit_string.c_str(), &input_units);
  if(!status_ok) 
    msg_internal_error("Unrecognized unit %s.", input_unit_string.c_str());

  status_ok &= UNITS_STATUS_OK == units_parse_unit(units_db, output_unit_string.c_str(), &output_units);
  if(!status_ok) 
    msg_internal_error("Unrecognized unit %s.", output_unit_string.c_str());

  udFLOAT output_value = -1.0;
  status_ok &= UNITS_STATUS_OK == units_convert(units_db, input_value, input_units, output_units, &output_value);
  if(!status_ok)
    msg_internal_error("Error converting units.\n");
  return output_value;
}

VOID sPARTICLE_SIM::compute_lattice_time_correction_factor(UNITS_DB units_db) {
  UNITS_UNIT sLatticeTime;
  UNITS_UNIT sLatticeTimeInc;
  units_parse_unit(units_db, "LatticeTime", &sLatticeTime);
  units_parse_unit(units_db, "LatticeTimeInc", &sLatticeTimeInc);
  dFLOAT conversion_slope;
  dFLOAT conversion_offset;
  units_conversion_coefficients(units_db, sLatticeTime, sLatticeTimeInc, &conversion_slope, &conversion_offset);
  g_particle_lattice_time_correction = conversion_slope;
  g_particle_one_over_lattice_time_correction = 1.0 / g_particle_lattice_time_correction;

  //Convert erosion parameters from mks to LU.
  if(g_is_erosion_simulation) {
    g_particle_sim_info.erosion_parameters.m_start_time =
      convert_dimensional_parameter(
                                    g_erosion_start_time,
                                    "s",
                                    "LatticeTime");

    g_particle_sim_info.erosion_parameters.m_surface_material_density =
      convert_dimensional_parameter(
                                    g_erosion_surface_material_density,
                                    "kg / m^3",
                                    "LatticeDensity");
    g_particle_sim_info.erosion_parameters.m_F_s = g_erosion_F_s;
    g_particle_sim_info.erosion_parameters.m_C_c =
      convert_dimensional_parameter(
                                    g_erosion_C_c,
                                    "s^2 / m^2",
                                    "1 / LatticeVelocity^2");
    g_particle_sim_info.erosion_parameters.m_C_d =
      convert_dimensional_parameter(
                                    g_erosion_C_d,
                                    "s^2 / m^2",
                                    "1 / LatticeVelocity^2");
    g_particle_sim_info.erosion_parameters.m_K = g_erosion_K;
    g_particle_sim_info.erosion_parameters.m_U_tsh =
      convert_dimensional_parameter(
                                    g_erosion_U_tsh,
                                    "m / s",
                                    "LatticeVelocity");
    if(my_proc_id == 0) {
      msg_print("Starting erosion at time %g s (%g LatticeTime) in erosion model.",
                g_erosion_start_time,
                g_particle_sim_info.erosion_parameters.m_start_time);
      msg_print("Using surface material density of %g kg/m^3 (%g LatticeDensity) in erosion model.",
                g_erosion_surface_material_density,
                g_particle_sim_info.erosion_parameters.m_surface_material_density);
      msg_print("Using C_c of %g s^2/m^2 (%g LatticeVelocity^-2) in erosion model.",
                g_erosion_C_c,
                g_particle_sim_info.erosion_parameters.m_C_c);
      msg_print("Using C_d of %g s^2/m^2 (%g LatticeVelocity^-2) in erosion model.",
                g_erosion_C_d,
                g_particle_sim_info.erosion_parameters.m_C_d);
      msg_print("Using U_tsh of %g m/s (%g LatticeDensity) in erosion model.",
                g_erosion_U_tsh,
                g_particle_sim_info.erosion_parameters.m_U_tsh);
    }
  }

  //Find the scaling for lattice density needed for saturation vapor pressure calculations
  UNITS_UNIT sLatticeDensity;
  UNITS_UNIT sDensity;
  units_parse_unit(units_db, "LatticeDensity", &sLatticeDensity);
  units_parse_unit(units_db, "kg/m^3", &sDensity);
  dFLOAT density_scale;
  dFLOAT density_offset;
  units_conversion_coefficients(units_db, sDensity, sLatticeDensity, &density_scale, &density_offset);
  g_particle_density_scale = density_scale;

}

sPARTICLE_SIM::~sPARTICLE_SIM(VOID) {}
sPARTICLE_SIM::sPARTICLE_SIM(VOID) {}


VOID sPARTICLE_SIM::compute_ref_frame_accel(LRF_PHYSICS_DESCRIPTOR lrf,
                                            asINT32 scale,
                                            sPARTICLE_VAR at_position[3],
                                            sPARTICLE_VAR local_velocity[3],
                                            sPARTICLE_VAR ref_frame_acceleration[3]) {


  vzero(ref_frame_acceleration);
  if (!lrf && !sim.grf.is_defined)
    return;

  //Compute the reference frame acceleration at the given position.
  sNIRF_PARAMETERS nirf;
  find_cumulative_nirf_parameters(lrf, &nirf, scale, scale, 3);

  if (nirf.is_rotation) {
    //compute  -2 Omega X v - omega X (omega X r) - (d omega/dt X r)

    sPARTICLE_VAR radius[3];
    vsub(radius, at_position, nirf.ref_point);

    sPARTICLE_VAR ref_frame_vel[3] = {0.0, 0.0, 0.0};  //omega X r
    vcross(ref_frame_vel, nirf.angular_vel, radius);

    sPARTICLE_VAR coriolis_acceleration[3] = {0.0, 0.0, 0.0}; //first term

    vcross(coriolis_acceleration, nirf.angular_vel, local_velocity);
    vmul(coriolis_acceleration, -2.0);


    sPARTICLE_VAR centrifugal_acceleration[3] = {0.0,0.0,0.0}; //second term

    vcross(centrifugal_acceleration, nirf.angular_vel, ref_frame_vel);
    vmul(centrifugal_acceleration, -1.0);


    sPARTICLE_VAR euler_acceleration[3] = {0.0, 0.0, 0.0};  //third term

    vcross(euler_acceleration, nirf.domega, radius);
    vmul(euler_acceleration, -1.0);

    ccDOTIMES(i,N_SPACE_DIMS) {
      ref_frame_acceleration[i] = -nirf.linear_accel[i] + coriolis_acceleration[i] + centrifugal_acceleration[i] + euler_acceleration[i];
    }
  } else {
    vneg(ref_frame_acceleration, nirf.linear_accel);
  }
}

static int locate_voxel(const STP_DGEOM_VARIABLE ublk_offset[3]) {
  uINT8 voxel = 0;
  if (ublk_offset[0] >= 0)
    voxel |= 0b100;
  if (ublk_offset[1] >= 0)
    voxel |= 0b010;
  if (ublk_offset[2] >= 0)
    voxel |= 0b001;
  return voxel;
}



template <typename FLOAT_TYPE>
bool sPARTICLE_SIM::is_position_inside_ublk(FLOAT_TYPE position[3], UBLK ublk, asINT32 &voxel) {
  sPARTICLE_VAR voxel_size = scale_to_voxel_size(ublk->scale());
  voxel = 0;
  ccDOTIMES(axis, 3) {
    voxel <<= 1;
    sPARTICLE_VAR ublk_centroid = ublk->location(axis) + voxel_size;
    sPARTICLE_VAR ublk_offset = position[axis] - ublk_centroid;
    if (std::fabs(ublk_offset) > voxel_size) {
      voxel = -1;
      return false;
    }
    if(ublk_offset > 0)
      voxel |= 0x1;
  }
  return true;

  // The code below is not quite consistent with what the home ublk
  // locator considers inside.  This assumes a position x is inside if
  // 0 <= x < 2*voxel size.  bsurfel_utils assumes a position is inside if
  // 0 <= x <= 2*voxel size.

#if 0
  sPARTICLE_VAR voxel_size = scale_to_voxel_size(ublk->scale());
  sPARTICLE_VAR ublk_size = 2 * voxel_size;
  STP_COORD voxel_location[3];
  sPARTICLE_VAR r[N_SPACE_DIMS];
  ublk->get_voxel_location(voxel, voxel_location);
  r[0] = position[0] - (sPARTICLE_VAR)ublk->location(0);
  r[1] = position[1] - (sPARTICLE_VAR)ublk->location(1);
  r[2] = position[2] - (sPARTICLE_VAR)ublk->location(2);
  if (!R_IS_OUTSIDE_UBLK(r, 0, ublk_size)) { //if the surfel centroid is inside this voxel, return true
    voxel = voxel_to_num(r[0] > voxel_size, r[1] > voxel_size, r[2] > voxel_size);
    return true;
  }
  voxel = -1;
  return false;
#endif
}

template bool sPARTICLE_SIM::is_position_inside_ublk(dFLOAT position[3], UBLK ublk, asINT32 &voxel);
template bool sPARTICLE_SIM::is_position_inside_ublk(sFLOAT position[3], UBLK ublk, asINT32 &voxel);


template <typename FLOAT_TYPE>
UBLK sPARTICLE_SIM::find_containing_neighbor_ublk(FLOAT_TYPE position[3], SURFEL surfel, asINT32 &home_voxel) {
  ccDOTIMES(i, surfel->p_data()->neighbor_ublks.size()) {
    UBLK ublk = surfel->p_data()->neighbor_ublks[i];
    asINT32 voxel;
    //voxel is set if the position is in this ublk.
    if (is_position_inside_ublk(position, ublk, voxel))  {
      if (ublk->fluid_like_voxel_mask.test(voxel)) {
        if (surfel->is_lrf() || surfel->p_data()->neighbor_voxel_masks[i].test(voxel) ) { //for LRF surfels, check more neighbor voxels when possible.
          cassert(ublk->p_data()->fluid_parcel_list[voxel]);
          home_voxel = voxel;
          return ublk;
        }
      }
    }
  }
  home_voxel = -1;
  return nullptr;
}

template UBLK sPARTICLE_SIM::find_containing_neighbor_ublk(double position[3], SURFEL surfel, asINT32 &home_voxel);
template UBLK sPARTICLE_SIM::find_containing_neighbor_ublk(float position[3], SURFEL surfel, asINT32 &home_voxel);

VOID sPARTICLE_SIM::move_parcels_to_new_container() {
  for (asINT32 scale = FINEST_SCALE; scale >= g_timescale.coarsest_active_scale(); --scale) {
    asINT32 voxel_count = 0;
    asINT32 moved_parcel_count = 0;
    WITH_TIMER(SP_PARTICLE_CONTAINER_UPDATE_TIMER) {
      DO_FARBLKS_OF_SCALE(farblk, scale) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        if (farblk->fluid_like_voxel_mask.test(voxel) ) {
            asINT32 parcel_count = move_parcels_to_new_container(farblk, voxel);
            moved_parcel_count += parcel_count;
            voxel_count += parcel_count != 0;
          }
        }
      }
      DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        if (nearblk->fluid_like_voxel_mask.test(voxel) ) {
            voxel_count++;
            asINT32 parcel_count = move_parcels_to_new_container(nearblk, voxel);
            moved_parcel_count += parcel_count;
            voxel_count += parcel_count != 0;
          }
        }
      }
    }
    timer_accum_counters(SP_PARTICLE_CONTAINER_UPDATE_TIMER, 0, voxel_count != 0); //Count how many voxels had to have particles moved out of.
    timer_accum_counters(SP_PARTICLE_CONTAINER_UPDATE_TIMER, 1, moved_parcel_count); //Count total number of particles.
  }
}

asINT32 sPARTICLE_SIM::particle_dynamics(UBLK ublk) {
  UBLK_PARTICLE_DATA p_data = ublk->p_data();

  // Initialize particle force. Need this here b/c will need to average of the voxels in the ublk
  // to prevent an LB instability from developing when momentum coupling is on.
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    p_data->s.particle_force[0][voxel] = 0.0;
    p_data->s.particle_force[1][voxel] = 0.0;
    p_data->s.particle_force[2][voxel] = 0.0;
  }

  // Check for particles in the voxels
  BOOLEAN ublk_is_empty = true;
  ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
    if (ublk->fluid_like_voxel_mask.test(voxel) ) {
      sPARCEL_LIST* parcel_list = p_data->fluid_parcel_list[voxel];
      parcel_list->reset();
      if (!parcel_list->exhausted()) {
        ublk_is_empty = false;
        break;
      }
    }
  }  

  // No particles? Don't bother doing the particle dynamics.
  if (ublk_is_empty) {
    return 0;
  }

  sPARTICLE_VAR min_part_diam = g_particle_sim_info.min_diameter_for_drag_calculation;
  asINT32 parcel_count = 0;  

  if (!sim.is_turb_model) {
    // DNS mode
    sVOXEL_GRADS grads[ubFLOAT::N_VOXELS] = {0};
    if (g_use_particle_pressure_force) {
      asINT32 prior_solver_index_mask = compute_solver_index_mask(ublk->scale());
      asINT32 voxel_grad_timestep_index = lb_index_from_mask(prior_solver_index_mask);
      compute_pressure_gradients(ublk, ublk->fluid_like_voxel_mask, grads, voxel_grad_timestep_index); // compute pressure gradient if there is a particle in the voxel
    }
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      if (ublk->fluid_like_voxel_mask.test(voxel)) {
        ccDOTIMES(axis, N_SPACE_DIMS) {
          grads[voxel].gradp[axis] = grads[voxel].gradp[axis] / g_density_scale_factor;
        }
        parcel_count += parcel_dynamics(ublk, voxel, &grads[voxel], min_part_diam);
        parcel_breakup(ublk, voxel);
      }
    }
  } else {
    // Turbulence model mode
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      if (ublk->fluid_like_voxel_mask.test(voxel)) {
        sVOXEL_GRADS grads;
        if (g_use_particle_pressure_force) {
          asINT32 voxel_index = voxel & VOXOR_VOXEL_MASK;
          asINT32 voxor_index = voxel >= N_VOXOR_VOXELS;
          ccDOTIMES(axis, N_SPACE_DIMS) {
            grads.gradp[axis] = p_data->s.grad_p[axis][voxor_index][voxel_index];
          }
        }
        parcel_count += parcel_dynamics(ublk, voxel, &grads, min_part_diam);
        parcel_breakup(ublk, voxel);
      }
    }
  }

  timer_accum_counters(SP_PARTICLE_VOXEL_DYN_TIMER, 0, parcel_count != 0); //Count ublks with particles in them.
  timer_accum_counters(SP_PARTICLE_VOXEL_DYN_TIMER, 1, parcel_count); //And count total number of particles.
  return parcel_count;
}

static VOID reflect_from_sym_plane(UBLK source_ublk, uINT8 source_voxel, UBLK dest_mirror_ublk,
                                   sPARCEL_STATE* parcel) {
  //always update based on the newest completed ode state
  asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
  asINT32 previous_state_index = parcel->relative_timestep_to_state_index(1); 

  UBLK real_ublk = dest_mirror_ublk->mirror_data()->m_mirror_ublk;
  sUBLK_MIRROR_DATA *mirror_data = real_ublk->mirror_data();
  // Looping over all mirror ublks, 1 for 1 symm plane, 3 for 2 symm planes, 7 for 3 symm planes
  // if the destination matches one of the mirror ublks, the parcel is reflected and done
  while (mirror_data) {
    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    if (mirror_ublk == dest_mirror_ublk) {
      UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
      ccDOTIMES(dim, N_SPACE_DIMS) {
        //reflect velocity
        parcel->v[new_state_index][dim] *= mirror_config->velocity_mirror_sign_factor[dim];
        STP_COORD source_loc = real_ublk->location(dim);
        STP_COORD mirror_loc = mirror_ublk->location(dim);
        if (source_loc != mirror_loc) {
          STP_COORD symplane_loc;
          if (source_loc > mirror_loc)
            symplane_loc = source_loc;
          else
            symplane_loc = mirror_loc;
          //reflect the position.
          parcel->x[new_state_index][dim] = 2 * symplane_loc - parcel->x[new_state_index][dim];
        }
      }
      return;
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
  msg_internal_error("none of the mirror ublks matched for Source ublk %d", source_ublk->id());
#ifdef OLD_IMPLEMENTATION
  sUBLK_MIRROR_DATA *mirror_data = source_ublk->mirror_data();
  // Looping over all mirror ublks, 1 for 1 symm plane, 3 for 2 symm planes, 7 for 3 symm planes
  // if the destination matches one of the mirror ublks, the parcel is reflected and done
  while (mirror_data) {
    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    if (mirror_ublk == dest_mirror_ublk) {
      UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
      //reflect the velocity.
      ccDOTIMES(dim, N_SPACE_DIMS) {
        parcel->v[new_state_index][dim] *= mirror_config->velocity_mirror_sign_factor[dim];
      }
      ccDOTIMES(dim, N_SPACE_DIMS) {
        STP_COORD source_loc = source_ublk->location(dim);
        STP_COORD mirror_loc = mirror_ublk->location(dim);
        if (source_loc != mirror_loc) {
          STP_COORD symplane_loc;
          if (source_loc > mirror_loc)
            symplane_loc = source_loc;
          else
            symplane_loc = mirror_loc;
          //reflect the position.
          parcel->x[new_state_index][dim] = 2 * symplane_loc - parcel->x[new_state_index][dim];
        }
      }
      return;
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }

  // if the destination does not match any mirror ublk, the destination may be
  // mirror of a neighbor. Find out the symm plane that was crossed and then reflect.
  mirror_data = source_ublk->mirror_data();
  // Looping over all symmetry planes
  while (mirror_data) {
    UBLK_MIRROR_CONFIG mirror_config = mirror_data->m_ublk_mirror_config;
    UBLK mirror_ublk = mirror_data->m_mirror_ublk;
    STP_COORD symplane_loc = -1, symplane_dir = -1;
    ccDOTIMES(dim, N_SPACE_DIMS) {
      STP_COORD source_loc = source_ublk->location(dim);
      STP_COORD mirror_loc = mirror_ublk->location(dim);
      if (source_loc != mirror_loc) {
        if (source_loc > mirror_loc)
          symplane_loc = source_loc;
        else
          symplane_loc = mirror_loc;
        symplane_dir = dim;
      }
    }
    if (symplane_dir == -1)
      msg_internal_error("No symmetry plane found");

    // Was the symmetry plane crossed?
    if ((parcel->x[    new_state_index][symplane_dir] - symplane_loc) *
        //(parcel->x[1 - new_state_index][symplane_dir] - symplane_loc) <= 0.0) {
        (parcel->x[previous_state_index][symplane_dir] - symplane_loc) <= 0.0) {
      //reflect the velocity.
      ccDOTIMES(dim, N_SPACE_DIMS) {
        parcel->v[new_state_index][dim] *= mirror_config->velocity_mirror_sign_factor[dim];
      }
      //reflect the position.
      parcel->x[new_state_index][symplane_dir] = 2 * symplane_loc - parcel->x[new_state_index][symplane_dir];
      return;
    }
    mirror_data = mirror_data->m_next_mirror_data;
  }
  msg_internal_error("none of the mirror ublks matched for Source ublk %d", source_ublk->id());
#endif
}

std::string sPARTICLE_SIM::truncate_error_message(std::string &err_msg) {
  // Attempting to truncate the string in case it is too long. At this time, we don't 
  // know what value to truncate at.
  int max_str_length = SP_EER_MAXPRMSTR; //- at least 35;
  if (err_msg.length() > max_str_length) {
    return err_msg.substr(0,max_str_length-3).append("...");
  }
  return err_msg;
}

BOOLEAN sPARTICLE_SIM::update_parcel_container(sPARCEL_LIST *init_parcel_list,
                                               UBLK init_ublk,
                                               asINT32 init_voxel,
                                               UBLK &final_ublk,
                                               asINT32 &final_voxel) {

  sPARCEL_STATE* parcel = init_parcel_list->data();
  BOOLEAN parcel_was_removed_from_init_list = FALSE;
  asINT32 new_state_index = parcel->relative_timestep_to_state_index(0); //always update based on the newest completed ode state
  asINT32 previous_state_index = parcel->relative_timestep_to_state_index(-1); 

  //If the parcel already had its container updated:
  if (parcel->m_updated_container_this_timestep) {
    final_ublk = init_ublk;
    final_voxel = init_voxel;
    return false;
  }

  parcel->m_updated_container_this_timestep = true;
  parcel->m_collision_done_this_timestep = false;

  try {
    //sHOME_UBLK_LOCATOR finder(init_ublk, init_voxel, parcel->x[new_state_index], false, parcel->x[1^new_state_index]);
    sHOME_UBLK_LOCATOR finder(init_ublk, init_voxel, parcel->x[new_state_index], false, parcel->x[previous_state_index]);
    finder.locate();
    final_ublk = finder.ublk();
    final_voxel = finder.voxel();
    if (finder.parcel_location_modified()){
      sPARTICLE_VAR old_location[N_SPACE_DIMS];
      vcopy (old_location, parcel->x[new_state_index]);
      vcopy(parcel->x[new_state_index], finder.location());
      //vcopy(parcel->x[1^new_state_index], finder.p_location());
      vcopy(parcel->x[previous_state_index], finder.p_location());
      particle_sim.interrupt_trace_for_parcel_crossing_a_periodic_bc(init_ublk, parcel, old_location);
    }
  }
  catch(sHOME_UBLK_LOCATOR::SearchError& error_message) { //If we can't find the right ublk, give up, drop the parcel, and count the parcel's mass as lost.
    init_parcel_list->remove();
    parcel_was_removed_from_init_list = TRUE;
    STP_COORD voxel_lattice_location[3];
    init_ublk->get_voxel_location(init_voxel, voxel_lattice_location);
    STP_GEOM_VARIABLE voxel_location[3];
    vcopy(voxel_location, voxel_lattice_location);

    if (g_track_lost_particle_mass) {
      // Re-enable this eventually once error message length issue is resolved.
      //error_message << " mass " << parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;
      //error_message << " id " << parcel->originating_sp << ":" << parcel->id;
      //error_message << " d " << parcel->d * LENGTH_TO_MKS_SCALE_FACTOR;
      //error_message << " velocity " << parcel->v[new_state_index][0] << " " << parcel->v[new_state_index][1] << " " << parcel->v[new_state_index][2];
      //error_message << " old " << parcel->x[previous_state_index][0] << " " << parcel->x[previous_state_index][1] << " " << parcel->x[previous_state_index][2];
      //simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, init_ublk->scale(), voxel_location, truncate_error_message(error_message.m_msg).c_str(), //Report all the details.
      //                         (dFLOAT)parcel->x[new_state_index][0],
      //                         (dFLOAT)parcel->x[new_state_index][1],
      //                         (dFLOAT)parcel->x[new_state_index][2]);
      std::stringstream debug_message;
      debug_message << "parcel mass " << parcel->parcel_mass; //Only report the amout of mass lost.
      std::string error_string;
      error_string = debug_message.str();
      simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, init_ublk->scale(), voxel_location, truncate_error_message(error_string).c_str(),
                               (dFLOAT)parcel->x[new_state_index][0],
                               (dFLOAT)parcel->x[new_state_index][1],
                               (dFLOAT)parcel->x[new_state_index][2]);
#if 0
      if(nullptr != strstr(error_message.what(), "expected intersection with face polygon not found")) {
        msg_error("Stoping after first occurance of your problem with parcel %d", parcel->id);
      }
#endif
    } else {
      std::stringstream debug_message;
      debug_message << "parcel mass " << parcel->parcel_mass; //Only report the amout of mass lost.
      std::string error_string;
      error_string = debug_message.str();
      simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, init_ublk->scale(), voxel_location, truncate_error_message(error_string).c_str(),
                               (dFLOAT)parcel->x[new_state_index][0],
                               (dFLOAT)parcel->x[new_state_index][1],
                               (dFLOAT)parcel->x[new_state_index][2]);
    }
    destroy_parcel(init_ublk, parcel, FALSE, TRUE); //True means the particle is being destroyed for a non physical reason and its mass should be counted as lost.
    return parcel_was_removed_from_init_list;
  }

  //If the parcel stayed in the same voxel then we're done.
  if(final_ublk == init_ublk && final_voxel == init_voxel)
    return parcel_was_removed_from_init_list; //return false

  //If the parcel moved into a mirror block, reflect it, put it back onto the inital parcel list, and try again to find
  //the new container for the reflected position.
  if (final_ublk->is_mirror()) {
    cassert(final_ublk->mirror_data());
    reflect_from_sym_plane(init_ublk, init_voxel, final_ublk, parcel);
    parcel->m_updated_container_this_timestep = false;
    return update_parcel_container(init_parcel_list, init_ublk, init_voxel,
                                   final_ublk, final_voxel);
  }

  //Since the parcel has changed ublks or voxels:
  //Removed the parcel from the init ublk/voxel list here and add it to a different list later on
  init_parcel_list->remove();
  parcel_was_removed_from_init_list = TRUE;

  //If the new ublk is a ghost, set the home shob and voxel in prep for addition to the send group by the calling function
  parcel->m_home_shob_id = final_ublk->id();
  parcel->m_home_voxel = final_voxel;
  if(final_ublk->is_ghost()) {
    return parcel_was_removed_from_init_list;
  }

  //If the voxel we want to add the parcel to is solid, drop the parcel
  if ( ! final_ublk->fluid_like_voxel_mask.test(final_voxel) ) {
    STP_COORD voxel_lattice_location[3];
    final_ublk->get_voxel_location(final_voxel, voxel_lattice_location);
    STP_GEOM_VARIABLE voxel_location[3];
    vcopy(voxel_location, voxel_lattice_location);
    std::stringstream debug_message;
    debug_message << "home ublk was found for parcel " << parcel->id << ":" << parcel->originating_sp;
    debug_message << " with parcel mass " << parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;
    debug_message << " but the home voxel " << final_voxel << " is solid";
    std::string error_string;
    error_string = debug_message.str();
    simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, final_ublk->scale(), voxel_location, truncate_error_message(error_string).c_str(),
                             (dFLOAT)parcel->x[new_state_index][0],
                             (dFLOAT)parcel->x[new_state_index][1],
                             (dFLOAT)parcel->x[new_state_index][2]);
    destroy_parcel(final_ublk, parcel, FALSE, TRUE); //True means the particle is being destroyed for a non physical reason and its mass is counted as lost.
    final_ublk = nullptr;
    return parcel_was_removed_from_init_list;
  }

  //Finally, for all other situations, attempt to add the parcel to the new continer's list
  sPARCEL_LIST *new_parcel_list  = final_ublk->p_data()->fluid_parcel_list[final_voxel];
  if (new_parcel_list == NULL) {
    STP_COORD voxel_lattice_location[3];
    final_ublk->get_voxel_location(final_voxel, voxel_lattice_location);
    STP_GEOM_VARIABLE voxel_location[3];
    vcopy(voxel_location, voxel_lattice_location);
    std::stringstream debug_message;
    debug_message << "home ublk and voxel were found for parcel " << parcel->id << ":" << parcel->originating_sp;
    debug_message << " with parcel mass " << parcel->parcel_mass * MASS_TO_MKS_SCALE_FACTOR;
    debug_message << " but the parcel list is null";
    std::string error_string;
    error_string = debug_message.str();
    simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, final_ublk->scale(), voxel_location, truncate_error_message(error_string).c_str(),
                             (dFLOAT)parcel->x[new_state_index][0],
                             (dFLOAT)parcel->x[new_state_index][1],
                             (dFLOAT)parcel->x[new_state_index][2]);
    destroy_parcel(final_ublk, parcel, FALSE, TRUE); //True means the particle is being destroyed for a non physical reason and its mass is counted as lost.
    return parcel_was_removed_from_init_list;
  }
  new_parcel_list->add(parcel);
  return parcel_was_removed_from_init_list;
}

asINT32 sPARTICLE_SIM::move_parcels_to_new_container(UBLK ublk, asINT32 voxel)
{
  asINT32 moved_parcel_count = 0;
  LRF_PHYSICS_DESCRIPTOR lrf = nullptr;
  sPARCEL_LIST* parcel_list = ublk->p_data()->fluid_parcel_list[voxel];
  parcel_list->reset();
  if (parcel_list->exhausted())
    return 0;
  while(!parcel_list->exhausted()) {
    BOOLEAN parcel_was_removed_from_list = FALSE;
    PARCEL_STATE parcel = parcel_list->data();
    if (parcel == NULL) {
      msg_internal_error("A parcel list contained a NULL reference.");
    } else {

#if ENABLE_CONSISTENCY_CHECKS
#if 1
      if(g_num_allowed_msg_prints_per_sp > 0) {
        if(parcel->time_since_update() > 0) {
          g_num_allowed_msg_prints_per_sp--;
          msg_print("Parcel %d:%d with dynamics type %d:%s was not update on timestep %ld.",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    g_timescale.m_time);
          if(g_num_allowed_msg_prints_per_sp == 0)
            msg_print("Further messages of this type are disabled.");

        }
      }
#endif
#endif


#if 1
      if(PARCEL_IS_INTERESTING(parcel)) {
        asINT32 current_state_index = parcel->relative_timestep_to_state_index(0);
        msg_print("Parcel %d:%d (measurable %d, visited windows %d aggl %d) at %.15g %.15g %.15g encountered in move_parcels_to_new_container from ublk %d (trajectory mask %d) at time %ld.",
                  parcel->originating_sp,
                  parcel->id,
                  (int)parcel->parcel_is_measurable_for_trajectory_window,
                  (int)parcel->visited_trajectory_windows,
                  parcel->aggl_applied,
                  parcel->x[current_state_index][0],
                  parcel->x[current_state_index][1],
                  parcel->x[current_state_index][2],
                  ublk->id(),
                  (int)ublk->p_data()->s.window_mask,
                  g_timescale.m_time);
      }
#endif

#if ENABLE_CONSISTENCY_CHECKS
      //   if (parcel->m_home_voxel != voxel) {
      //     msg_warn("Home voxel for Parcel %d should be %d but found %d \
      //              Dynamics type %d Location [%e %e %e] [%e %e %e] ", parcel->id, voxel,
      //              parcel->m_home_voxel, parcel->dynamics_type,
      //              parcel->x[0][0], parcel->x[0][1], parcel->x[0][2],
      //              parcel->x[1][0], parcel->x[1][1], parcel->x[1][2]);
      //   }
#endif
      //Check the new position against a user specified global bounding box and drop the particle if it goes outside.
      if(g_particle_sim_info.emitters[parcel->emitter_id]->subject_to_dispersion_box()) {
        asINT32 current_state_index = parcel->relative_timestep_to_state_index(0);
        if (position_is_outside_dispersion_box(lrf, parcel->x[current_state_index])) {
          destroy_parcel_on_list(ublk, parcel_list, FALSE, FALSE);
          parcel_was_removed_from_list = TRUE;
          continue;
        }
      }

      //If the parcel was just emitted at the beginning of this timestep and no collision was detected, change the
      //dynamics type to IN_FLUID:
      if(parcel->dynamics_type == JUST_EMITTED_MAYBE_IN_FLUID) {
        BOOLEAN parcel_is_massless_tracer = g_particle_sim_info.emitters[parcel->emitter_id]->material()->is_massless_tracer();
        if(parcel_is_massless_tracer)
          parcel->change_dynamics_type(MASSLESS_TRACER_PARTICLE);
        else
          parcel->change_dynamics_type(IN_FLUID);
      }

      //If the parcel was reentrained from a timestep not preceeding film reaccumulation, this dynamics type may be set.
      //Reset it if film will be reaccumulate on the following timestep
      //dynamics type to IN_FLUID:
      if(is_timestep_before_accumulation())
        if(parcel->dynamics_type == JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT)
          parcel->change_dynamics_type(IN_FLUID);

      UBLK new_ublk = nullptr;
      asINT32 new_voxel;
      parcel_was_removed_from_list = update_parcel_container(parcel_list, ublk, voxel, new_ublk, new_voxel);

      moved_parcel_count += parcel_was_removed_from_list;

      if (new_ublk == nullptr) {
        continue; //update_parcel_container removed the parcel from the parcel list, counted the mass as lost, and
        //generated one of the possible sim errors due to this fault. Move on to the next parcel which
        //should now be on the top of the list (ok to bypass parcel_list->next()).
      }

      if(PARCEL_IS_INTERESTING(parcel)) {
        if(new_ublk != ublk || new_voxel != voxel )
          msg_print("parcel %d:%d with dynamics type %d:%s is moving from ublk %d (is ghost %d) at %d %d %d, voxel %d to ublk %d (is ghost %d) at %d %d %d voxel %d at timestep %ld.",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    ublk->id(),
                    ublk->is_ghost(),
                    ublk->location(0),
                    ublk->location(1),
                    ublk->location(2),
                    voxel,
                    new_ublk->id(),
                    new_ublk->is_ghost(),
                    new_ublk->location(0),
                    new_ublk->location(1),
                    new_ublk->location(2),
                    new_voxel,
                    g_timescale.m_time);
      }

      if (new_ublk->is_ghost()) {
        uINT32 home_sp = new_ublk->home_sp();
        cassert(parcel_was_removed_from_list);
        add_parcel_to_send_group(parcel, home_sp);
      } else {

#if ENABLE_CONSISTENCY_CHECKS
        if(parcel->aggl_applied) {
          asINT32 this_state_index = parcel->relative_timestep_to_state_index(0);
          msg_print("Parcel %d:%d at %.10g %.10g %.10g with state %d:%s has aggl_aplied at time %ld.",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->x[this_state_index][0],
                    parcel->x[this_state_index][1],
                    parcel->x[this_state_index][2],
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    g_timescale.m_time);
        }
#endif
        if(parcel->time_since_update() == 0)
          measure_trajectory_vertex(new_ublk, parcel, EVENT_VERTEX, FALSE);
      }
    }

    if (parcel_was_removed_from_list)
      continue;
    parcel_list->next();
  }
  return moved_parcel_count;
}

#if 0 //This appears to be obsolete and not used for anything
UBLK sPARTICLE_SIM::traverse_lattice(UBLK start_ublk,
                                     sPARTICLE_VAR *displacement,
                                     asINT32 recursion_depth) {

  const auto& box_access = start_ublk->m_box_access;

  sINT16 offsets[3];
  vcopy(offsets, displacement);
  UBLK neighbor_ublk = box_access.neighbor_ublk(offsets).ublk();
  return neighbor_ublk;
}
#endif

VOID sPARTICLE_SIM::update_all_virtual_wiper_positions() {
  ccDOTIMES(wiper_num,g_particle_sim_info.wipers.size()) {
    g_particle_sim_info.wipers[wiper_num]->update_position();
  }
}

VOID sPARTICLE_SIM::flag_surfels_wiped_by_virtual_wiper_models() {
  ccDOTIMES(wiper_num,g_particle_sim_info.wipers.size()) {
    g_particle_sim_info.wipers[wiper_num]->flag_surfels_wiped_by_virtual_wiper();
  }
}

template <typename SURFEL_TYPE>
bool sPARTICLE_SIM::find_home_ublk(SURFEL_TYPE surfel, UBLK &home_ublk, sINT32 &home_voxel) {
  assert(!surfel->is_ghost());
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks = surfel->m_n_ublk_interactions;
  if (n_ublks == 0)
    return false;

  static std::vector<bool> is_candidate_ublk;

  if (is_candidate_ublk.size() < n_ublks)
    is_candidate_ublk.resize(n_ublks);
  is_candidate_ublk.clear();

  // First find the ublks that contains the surfel centroid
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    sINT32 ublk_size = scale_to_voxel_size(ublk->scale())*2;
    if (surfel->is_home_voxel(cast_as_regular_array(ublk->m_location), ublk_size)) {
      is_candidate_ublk[iu] = true;
    } else {
      is_candidate_ublk[iu] = false;
    }
  }

  STP_LATVEC_MASK MOVING_STATES_MASK = (static_cast<STP_LATVEC_MASK>(1) << N_MOVING_STATES) - 1;
  ublk_interaction = surfel->m_ublk_interactions;
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk = ublk_interaction->ublk();
    if (!is_candidate_ublk[iu])
      continue;

    uINT8 *voxel_info          = ublk_interaction->voxel_info();
    auINT32 n_weight_sets      = ublk_interaction->m_n_weight_sets;
    STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();

    ccDOTIMES (iv, n_weight_sets) {
      sSURFEL_VOXEL_INTERACTION s_v_interaction;
      voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
      asINT32 n_voxel_weights    = s_v_interaction.n_voxel_weights();
      asINT32 voxel              = s_v_interaction.voxel_id();
      STP_LATVEC_MASK latvec_mask = surfel->incoming_latvec_mask;
      ccDOTIMES (iw, n_voxel_weights) {
        STP_LATVEC_INDEX latvec = *voxel_info++;
        latvec_mask &= ~ (1 << latvec);
      }
      weights += n_voxel_weights;
      if ((latvec_mask & MOVING_STATES_MASK) == 0) {
        sINT32 voxel_size = scale_to_voxel_size(ublk->scale());
        STP_COORD voxel_loc[3];
        voxel_loc[0] = ublk->location(0) +  num_to_voxel_x(voxel)*voxel_size;
        voxel_loc[1] = ublk->location(1) +  num_to_voxel_y(voxel)*voxel_size;
        voxel_loc[2] = ublk->location(2) +  num_to_voxel_z(voxel)*voxel_size;
        if (surfel->is_home_voxel(voxel_loc, voxel_size)) {
          if ( ublk->is_vr_fine() ) {
            STP_COORD vr_fine_ublk_location[3];
            vcopy(vr_fine_ublk_location, ublk->m_location);

            sVR_FINE_INTERFACE_DATA * vr_data = ublk->vr_fine_data();
            ublk = vr_data->vr_coarse_ublk().ublk();
            voxel_size = scale_to_voxel_size(ublk->scale());

            vsub(voxel_loc, vr_fine_ublk_location, ublk->m_location);
            vmul(voxel_loc, 1.0/voxel_size);

            asINT32 vr_fine_rel_loc[N_SPACE_DIMS];
            ccDOTIMES(axis, N_SPACE_DIMS){
              vr_fine_rel_loc[axis] = static_cast <int> (std::floor(voxel_loc[axis]));
            }
            voxel = voxel_to_num(vr_fine_rel_loc[0],vr_fine_rel_loc[1],vr_fine_rel_loc[2]);
          }
          cassert(!ublk->is_mirror());
          if( ublk->ref_frame_index() == surfel->ref_frame_index() ) {
            home_ublk = ublk;
            home_voxel = voxel;
            return true;
          }
        }
      }
    }
  }
  return false;
}

template <typename SURFEL_TYPE>
VOID sPARTICLE_SIM::assign_neighbor_ublks(SURFEL_TYPE surfel) {
  UBLK home_ublk = nullptr;
  sINT32 home_voxel = -1;
  bool found_ublk = false;
  if (surfel->is_sampling_surfel() && !surfel->is_even() && surfel->clone_surfel() != nullptr){
    found_ublk = find_home_ublk(surfel->clone_surfel(), home_ublk, home_voxel);
  } else {
    found_ublk = find_home_ublk(surfel, home_ublk, home_voxel);
  }

  if (found_ublk) {
    surfel->p_data()->neighbor_ublks.push_back(home_ublk);
    surfel->p_data()->neighbor_voxel_masks.push_back(home_ublk->fluid_like_voxel_mask);
    //surfel->p_data()->neighbor_voxel_masks.push_back( 0x1 << home_voxel);
    sSURFEL_NEIGHBOR_UBLK_LOCATOR<SURFEL_TYPE> locator(surfel, home_ublk, home_voxel);
    locator.add_neighboring_ublks();
  }
}

VOID sPARTICLE_SIM::clear_ghost_film(SCALE scale) {
  if (is_timestep_after_accumulation()) {
    DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group, scale) {
      group->clear_ghost_surfel_film_accumulators();
      group->clear_ghost_surfel_meas_accumulators();
    }
  }
}

VOID sPARTICLE_SIM::clear_film(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  if (!is_timestep_for_accumulation())
    return;
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          if(!surfel->is_wall())
            continue;
          if(surfel->is_even())
            continue;
          surfel->p_data()->s.clear_film_accumulators();
        }
        break;
      }
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}

VOID sPARTICLE_SIM::accumulate_film(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  if (!is_timestep_for_accumulation())
    return;
  BOOLEAN first_accumulation_of_interest = TRUE;

  BOOLEAN is_time_step_odd =  (g_timescale.m_lb_tm.is_timestep_odd(scale) != 0);
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {

          if(surfel->is_even())
            continue;

          if (!surfel->is_wall())
            continue;

          if(FILM_SURFEL_IS_INTERESTING(surfel)) {
            if(first_accumulation_of_interest) {
              msg_print("Accumulating to the stencils of surfels of type %s at timestep %ld.",
                        g_surfel_group_type_names[surfel_group_type],
                        g_timescale.m_time);
              first_accumulation_of_interest = FALSE;
            }
            msg_print("Accumulating to stencil of surfel %d at timestep %ld.", surfel->id(), g_timescale.m_time);
          }

          asINT32 parcel_count = accumulate_film(surfel);
          timer_accum_counters(SP_FILM_ACCUMULATION_TIMER, 0, parcel_count != 0); //Count surfels with particles on them.
          timer_accum_counters(SP_FILM_ACCUMULATION_TIMER, 1, parcel_count); //Count total number of particles.
        }
        break;
      }

    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}


VOID sPARTICLE_SIM::ghost_sampling_surface_measurement(SCALE scale) {

  DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
    for (const auto& quantum: surfel_recv_group->quantums()) {
      SAMPLING_SURFEL sampling_surfel = quantum.m_surfel;
      record_surface_measurements(sampling_surfel, scale);
    }
  }
}


VOID sPARTICLE_SIM::film_dynamics(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(scale);
  BOOLEAN is_time_step_odd =  (g_timescale.m_lb_tm.is_timestep_odd(scale) != 0);
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        asINT32 num_surfels_with_film = 0;
        DO_SURFELS_OF_GROUP(surfel, sgroup) {

          if(surfel->is_even())
            continue;

          if (!surfel->is_wall())
            continue;

          num_surfels_with_film += film_dynamics(surfel);
        }
        timer_accum_counters(SP_FILM_DYNAMICS_TIMER, 0, num_surfels_with_film); //Count surfels with film
        break;
      }

    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}

VOID sPARTICLE_SIM::film_breakup(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {


  if (!is_timestep_before_accumulation())
    return;

  asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(scale);
  BOOLEAN is_time_step_odd =  (g_timescale.m_lb_tm.is_timestep_odd(scale) != 0);
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {

          if(surfel->is_even())
            continue;

          if (!surfel->is_wall())
            continue;

          asINT32 num_parcels = film_breakup(surfel);
          timer_accum_counters(SP_FILM_REENTRAINMENT_TIMER, 0, num_parcels != 0); //Count surfels from which parcels were reentrianed.
          timer_accum_counters(SP_FILM_REENTRAINMENT_TIMER, 1, num_parcels); //Count total number of particles reentrained.
        }
        break;
      }

    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}



VOID sPARTICLE_SIM::film_measurements(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  TIMESTEP_PARITY running_parity = g_timescale.time_parity();
  ACTIVE_SOLVER_MASK full_active_solver_mask = g_timescale.m_active_solver_masks[running_parity][scale];
  bool particle_solver_is_active = full_active_solver_mask  & PARTICLE_ACTIVE;
  if(!particle_solver_is_active)
    return;



  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        BOOLEAN timestep_is_even = !g_timescale.m_lb_tm.is_timestep_odd(scale);
        BOOLEAN timestep_is_odd = g_timescale.m_lb_tm.is_timestep_odd(scale);
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        asINT32 surfel_count = 0;
        DO_SURFELS_OF_GROUP(surfel, sgroup) {

          if(surfel->is_odd() && timestep_is_even)
            continue;
          if(surfel->is_even() && timestep_is_odd)
            continue;
          record_surface_measurements(surfel, scale);
          surfel_count++;
        }
        timer_accum_counters(SP_FILM_MEASUREMENTS_TIMER, 0, surfel_count); //Count surfels with particles on them.
        break;
      }
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      break;
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      {
        SAMPLING_SURFEL_GROUP sgroup = static_cast<SAMPLING_SURFEL_GROUP>(group);
        asINT32 surfel_count = 0;
        DO_SAMPLING_SURFELS_OF_GROUP(sampling_surfel, sgroup) {
          record_surface_measurements(sampling_surfel, scale);
          surfel_count++;
        }
        timer_accum_counters(SP_FILM_MEASUREMENTS_TIMER, 0, surfel_count); //Count surfels with particles on them.
        break;
      }
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}

VOID sPARTICLE_SIM::film_kinematics(SURFEL_BASE_GROUP_TYPE surfel_group_type, SCALE scale) {
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        asINT32 parcel_count = 0;
        asINT32 surfel_count = 0;
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          if(surfel->is_even())
            continue;
          if (!surfel->is_wall())
            continue;
          parcel_count += move_parcels_on_surface(surfel);
          if (g_use_agglomeration)
            aggregate_surface_parcels(surfel);
          surfel_count++;
        }
        timer_accum_counters(SP_SURFACE_PARTICLE_KINEMATICS_TIMER, 0, surfel_count); //Count surfels with particles on them.
        timer_accum_counters(SP_SURFACE_PARTICLE_KINEMATICS_TIMER, 1, parcel_count); //Count total number of parcels moved.
        break;
      }
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
}


VOID sPARTICLE_SIM::compute_film_contact_line_forces() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      surfel_compute_film_contact_line_forces(surfel);
    }
  }
}

VOID sPARTICLE_SIM::redistribute_film_contact_line_forces() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      surfel_redistribute_film_contact_line_forces(surfel);
    }
  }
}

VOID sPARTICLE_SIM::assign_neighbor_ublks() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {
      assign_neighbor_ublks(surfel);
    }
    DO_SAMPLING_SURFELS_OF_SCALE(sampling_surfel, scale) {
      if(sampling_surfel->is_even()){
        continue;
      }
      assign_neighbor_ublks(sampling_surfel);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        if(group->quantum(nth_surfel)->is_surfel_weightless()) {
          // Only one "weightless" surfel exists but its reference appears multiple times in the quantums.
          // Skip it so it doesn't collect redundant neighbor ublk.
          continue;
        }
        SURFEL mlrf_surfel = group->quantum(nth_surfel)->mlrf_surfel();
        assign_neighbor_ublks(mlrf_surfel);
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      assign_neighbor_ublks(slrf_surfel_pair->m_exterior_surfel);
      assign_neighbor_ublks(slrf_surfel_pair->m_interior_surfel);
    }
  }
}

VOID sPARTICLE_SIM::surface_collision() {

  SCALE strand_coarsest_active_scale = g_timescale.coarsest_active_scale();
  for (asINT32 scale = FINEST_SCALE; scale >= strand_coarsest_active_scale; --scale) {
    WITH_TIMER(SP_PARTICLE_COLLISION_DETECTION_TIMER) {
      // sampling surfels should be accounted for first before the parcels get
      // deposited on the real surfels.
      sampling_surface_collision(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
      sampling_surface_collision(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
      sampling_surface_collision(INTERIOR_SURFEL_BASE_GROUP_TYPE, scale);
      ghost_sampling_surface_collision(scale);

      surface_collision(FRINGE_SURFEL_BASE_GROUP_TYPE, scale);
      surface_collision(FRINGE2_SURFEL_BASE_GROUP_TYPE, scale);
      surface_collision(INTERIOR_SURFEL_BASE_GROUP_TYPE, scale);
      mlrf_surface_collision(scale);
      ghost_surface_collision(scale);
      bsurfel_surface_collision(scale);
    }
  }
}

VOID sPARTICLE_SIM::mlrf_surface_collision(SCALE scale) {
  asINT32 surfel_count = 0;
  DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
    surfel_count += group->n_quantums();
    ccDOTIMES(nth_surfel, group->n_quantums()) {
      SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
      if(surfel->is_weightless_mlrf())
        continue;
      lrf_surface_collision(surfel);
    }
  }
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}

VOID sPARTICLE_SIM::ghost_surface_collision(SCALE scale) {
  asINT32 surfel_count =0;
  DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
    surfel_count += surfel_recv_group->quantums().size();
    for (const auto& quantum: surfel_recv_group->quantums()) {
      SURFEL surfel = quantum.m_surfel;
      if(surfel->is_weightless_mlrf())
        continue;
      if(surfel->is_lrf())
        lrf_surface_collision(surfel);
      else
        surface_collision(surfel);
    }
  }
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}

VOID sPARTICLE_SIM::ghost_sampling_surface_collision(SCALE scale) {
  asINT32 surfel_count = 0;
  DO_SAMPLING_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
    surfel_count += surfel_recv_group->quantums().size();
    for (const auto& quantum: surfel_recv_group->quantums()) {
      SAMPLING_SURFEL sampling_surfel = quantum.m_surfel;
      // The order is important here. We want to measure before screening
#ifdef TO_BE_DONE
      // We should unify these two functions into one.
#endif
      surface_collision(sampling_surfel);
      screen_collision(sampling_surfel);
    }
  }
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}

VOID sPARTICLE_SIM::sampling_surface_collision(SURFEL_BASE_GROUP_TYPE surfel_group_type,
                                               SCALE scale) {
  asINT32 surfel_count = 0;
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    if (group->m_supertype == SAMPLING_SURFEL_GROUP_SUPERTYPE) {
      SAMPLING_SURFEL_GROUP sgroup = static_cast<SAMPLING_SURFEL_GROUP>(group);
      DO_SAMPLING_SURFELS_OF_GROUP(surfel, sgroup) {
        // The order is important here. We want to measure before screening
#ifdef TO_BE_DONE
        // We should unify these two functions into one.
#endif
        if(surfel->is_even())
          continue;
        surface_collision(surfel);
        screen_collision(surfel);
        surfel_count++;
      }
    }
  }
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}

VOID sPARTICLE_SIM::bsurfel_surface_collision(SCALE scale) {
  asINT32 surfel_count = 0;
  sBSURFEL_PROCESS_CONTROL bsurfel_process_control;
  bsurfel_process_control.init(scale);
  surfel_count += bsurfel_process_control.surface_collision(FRINGE_BSURFEL_GROUP_TYPE);
  surfel_count += bsurfel_process_control.surface_collision(FRINGE2_BSURFEL_GROUP_TYPE);
  surfel_count += bsurfel_process_control.surface_collision(INTERIOR_BSURFEL_GROUP_TYPE);
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}


VOID sPARTICLE_SIM::surface_collision(SURFEL_BASE_GROUP_TYPE surfel_group_type,
                                      SCALE scale) {
  asINT32 surfel_count = 0;
  DO_SURFEL_BASE_GROUPS_OF_SCALE_TYPE(group, scale, surfel_group_type) {
    switch (group->m_supertype) {
    case FLOW_DYN_SURFEL_GROUP_SUPERTYPE:
      {
        SURFEL_GROUP sgroup = static_cast<SURFEL_GROUP>(group);
        BOOLEAN is_time_step_odd =  (g_timescale.m_lb_tm.is_timestep_odd(scale) != 0);
        DO_SURFELS_OF_GROUP(surfel, sgroup) {
          if(surfel->is_even())
            continue;
          surface_collision(surfel);
          surfel_count++;
        }
        break;
      }
    case SLRF_SURFEL_PAIR_GROUP_SUPERTYPE:
      {
        SURFEL_PAIR_GROUP slrf_group = static_cast<SURFEL_PAIR_GROUP>(group);
        DO_SURFEL_PAIRS_OF_GROUP(surfel_pair, slrf_group) {
          lrf_surface_collision(surfel_pair->m_exterior_surfel);
          lrf_surface_collision(surfel_pair->m_interior_surfel);
          surfel_count += 2;
        }
        break;
      }
    case APM_SURFEL_PAIR_GROUP_SUPERTYPE:
      break;
    case SAMPLING_SURFEL_GROUP_SUPERTYPE:
      // Skipped since screening and sampling is done in a prior loop
      break;
    default:
      msg_internal_error("Unexpected fringe surfel group type %d",group->m_supertype);
      break;
    }
  }
  timer_accum_counters(SP_PARTICLE_COLLISION_DETECTION_TIMER, 0, surfel_count); //Count surfels that did collision detection.
}

size_t sPARTICLE_SIM::ckpt_len()
{
   return sizeof(size_t)                                         // This length
        + sizeof(asINT32)                                        // Number of emitters
        + g_particle_sim_info.emitters.size() * sizeof(asINT32)  // Parcel Ids (In legacy, asINT64)
        + g_random_particle_properties->ckpt_len();              // Random state
}

VOID sPARTICLE_SIM::write_ckpt()
{
  LGI_CKPT_PARTICLE_EMITTERS_HEADER header;
  asINT32 record_length = sizeof(header) + g_particle_sim_info.emitters.size() * sizeof(LGI_CKPT_PARTICLE_EMITTER_REC);
  lgi_write_init_tag (&header, LGI_CKPT_PARTICLE_EMITTERS_TAG , record_length);

  header.n_emitters = g_particle_sim_info.emitters.size();
  write_ckpt_lgi_head(header);

  ccDOTIMES(i,g_particle_sim_info.emitters.size()) {
    g_particle_sim_info.emitters[i]->write_ckpt();
  }

  //Checkpoint RNGs used for generating particle properties.
  g_random_particle_properties->ckpt();

}

VOID sPARTICLE_SIM::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t len = ckpt_len();
  pio_ckpt_buff.write(&len);

  asINT32 n_emitters = g_particle_sim_info.emitters.size();
  pio_ckpt_buff.write(&n_emitters);

  ccDOTIMES(i,g_particle_sim_info.emitters.size()) {
    g_particle_sim_info.emitters[i]->write_ckpt(pio_ckpt_buff);
  }

  //Checkpoint RNGs used for generating particle properties.
  g_random_particle_properties->ckpt(pio_ckpt_buff);

}

VOID sPARTICLE_SIM::release_parcels()
{
  //Update all time varying parameters for all emitter configurations
  //Spatially varying parameters cannot be evaluated until a new initial particle position has been generated.
  STP_GEOM_VARIABLE point[3] = {0.0, 0.0, 0.0};
  STP_GEOM_VARIABLE normal[3] = {0.0, 0.0, 0.0};

  //Iterate over all emitters, update their parameters and recompute the release rate if needed, and then emit the parcels.
  ccDOTIMES(emitter_index, g_particle_sim_info.emitters.size()) {
    PARTICLE_EMITTER_BASE emitter = g_particle_sim_info.emitters[emitter_index];
    BOOLEAN some_parameters_changed = emitter->emitter_configuration()->eval_time_only_varying_physics_variables(g_timescale.m_time, g_timescale.m_powertherm_time);
    if(some_parameters_changed) {
      if(emitter->is_rain_emitter()) {

        //Pass the new air velocity to the rate calculator and recompute the release rate.
        sPARTICLE_VAR air_velocity[3];
        ((RAIN_EMITTER_CONFIGURATION)emitter->emitter_configuration())->free_stream_velocity(air_velocity);

        RAIN_EMITTER_BASE rain_emitter=static_cast<RAIN_EMITTER_BASE>(emitter);
        SIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER rate_calculator = rain_emitter->emission_rate_calculator();
        rate_calculator->recompute_rain_emitter_release_rate(
                                                             emitter->emitter_configuration()->simutils_emission_rate_type(),
                                                             emitter->emitter_configuration()->emission_rate(),
                                                             g_particle_sim_info.gravity,
                                                             air_velocity);
        if(rate_calculator->error_code()) {
#ifdef WHY_WONT_THIS_LINK
          msg_error("Problem encountered computing a rain emitter release rate: %s. %s",
                    rate_calculator->error_code_string().c_str(),
                    rate_calculator->error_code_explanation().c_str());
#else
          msg_error("Problem encountered computing a rain emitter release rate.");
#endif
        }
        rain_emitter->set_rain_emitter_particle_emission_rate(rate_calculator->particles_per_timestep());
      } else {
        //Need to handle time varying diameters here, currently only time varying release rate is handeled if not a rain emitter.
      }
    }
    emitter->emit_parcels();
  }
}

VOID sPARTICLE_SIM::destroy_parcel(PARCEL_STATE parcel_to_delete, BOOLEAN deleted_unexpectedly) {
  if(deleted_unexpectedly) {
    if(!std::isnan(parcel_to_delete->parcel_mass)) //If we accumulate a nan, for whatever reason, it supresses the CP reporting.
      g_sp_lost_particle_mass += parcel_to_delete->parcel_mass * MASS_TO_MKS_SCALE_FACTOR; //Accumulate the sum with units of kg so the CP doesn't have to convert.
/*
    printf("parcel %d:%d deleted from position %g %g %g.\n",
           parcel_to_delete->id,
           parcel_to_delete->originating_sp,
           parcel_to_delete->x[parcel_to_delete->relative_timestep_to_state_index(0)][0],
           parcel_to_delete->x[parcel_to_delete->relative_timestep_to_state_index(0)][1],
           parcel_to_delete->x[parcel_to_delete->relative_timestep_to_state_index(0)][2]);
*/
  }
  
  parcel_to_delete->change_dynamics_type(ABOUT_TO_BE_DELETED);
  delete parcel_to_delete;
}

BOOLEAN sPARTICLE_SIM::destroy_parcel(
                                      UBLK ublk,
                                      PARCEL_STATE parcel_to_delete,
                                      BOOLEAN has_already_had_pmr_handled,
                                      BOOLEAN deleted_unexpectedly)
{
  //If the parcel was being measured, but is dying unexpectedly, a state update needs to be recorded for pmr files
  //to stop PowerVIZ from continuing the trace in time according to the last know trajectory.
  particle_sim.maybe_send_final_vertex(ublk, parcel_to_delete, has_already_had_pmr_handled);
  //free the particle's state and node from the particle list
  if (parcel_to_delete != NULL) {
    destroy_parcel(parcel_to_delete, deleted_unexpectedly);
    return(FALSE);
  } else {
    msg_warn("SP%d: !tried to destroy a particle from a NULL reference at ts=%ld.\n", my_proc_id, g_timescale.m_time);
    return(TRUE);
  }
}

BOOLEAN sPARTICLE_SIM::destroy_parcel(
                                      SURFEL surfel,
                                      PARCEL_STATE parcel_to_delete,
                                      BOOLEAN has_already_had_pmr_handled,
                                      BOOLEAN deleted_unexpectedly)
{

#ifdef TO_BE_DONE
  //If the parcel was being measured, but is dying unexpectedly, a state update needs to be recorded for pmr files
  //to stop PowerVIZ from continuing the trace in time according to the last know trajectory.
  particle_sim.maybe_send_final_vertex(, parcel_to_delete, has_already_had_pmr_handled);
#endif

  //free the particle's state and node from the particle list
  if (parcel_to_delete != NULL) {
    destroy_parcel(parcel_to_delete, deleted_unexpectedly);
    return(FALSE);
  } else {
    msg_warn("SP%d: !tried to destroy a particle from a NULL reference at ts=%ld.\n", my_proc_id, g_timescale.m_time);
    return(TRUE);
  }
}

BOOLEAN sPARTICLE_SIM::destroy_parcel_on_list(
                                              SURFEL surfel,
                                              PARCEL_LIST parcel_list_to_delete_from,
                                              BOOLEAN has_already_had_pmr_handled,
                                              BOOLEAN deleted_unexpectedly)
{
  //Free the particle's state and node from the particle list.
  if (!parcel_list_to_delete_from->exhausted()) {
    PARCEL_STATE parcel_to_delete = parcel_list_to_delete_from->data();
    if (parcel_to_delete!=NULL) {
      parcel_list_to_delete_from->remove();
      return destroy_parcel(surfel, parcel_to_delete, has_already_had_pmr_handled, deleted_unexpectedly);
    } else {
      msg_warn("SP%d: !tried to destroy a particle with NULL reference at ts=%ld.\n", my_proc_id, g_timescale.m_time);
      parcel_list_to_delete_from->remove(); //at least delete the list node
      return(TRUE);
    }
  }
  return(FALSE);
}

BOOLEAN sPARTICLE_SIM::destroy_parcel_on_list(
                                              UBLK ublk,
                                              PARCEL_LIST parcel_list_to_delete_from,
                                              BOOLEAN has_already_had_pmr_handled,
                                              BOOLEAN deleted_unexpectedly)
{
  //Free the particle's state and node from the particle list.
  if (!parcel_list_to_delete_from->exhausted()) {
    PARCEL_STATE parcel_to_delete = parcel_list_to_delete_from->data();
    if (parcel_to_delete!=NULL) {
      parcel_list_to_delete_from->remove();
      return destroy_parcel(ublk, parcel_to_delete, has_already_had_pmr_handled, deleted_unexpectedly);
    } else {
      msg_warn("SP%d: !tried to destroy a particle with NULL reference at ts=%ld.\n", my_proc_id, g_timescale.m_time);
      parcel_list_to_delete_from->remove(); //at least delete the list node
      return(TRUE);
    }
  }
  return(FALSE);
}

PARCEL_STATE sPARTICLE_SIM::add_parcel(const sPARCEL_STATE &parcel, SURFEL surfel) {
  PARCEL_STATE new_parcel = new sPARCEL_STATE(parcel);
  surfel->p_data()->surface_parcel_list->add(new_parcel);
  new_parcel->change_dynamics_type(ON_SURFACE);
  return new_parcel;
}

PARCEL_STATE sPARTICLE_SIM::add_parcel(asINT32 emitter_id,
                                       asINT32 parent_parcel_id,
                                       sPARTICLE_VAR num_particles_in_new_parcel,
                                       PARTICLE_VAR x,
                                       PARTICLE_VAR v,
                                       sPARTICLE_VAR diameter,
                                       sPARTICLE_VAR particle_density,
                                       sPARTICLE_VAR unevaporated_mass,
                                       //sPARTICLE_VAR particle_temperature,
                                       sPARTICLE_VAR particle_surface_tension,
                                       sPARTICLE_VAR particle_kinematic_viscosity,
                                       asINT32 num_initial_reflections,
                                       BOOLEAN interpret_velocity_as_drift,
                                       asINT32 particle_aggl_applied,
                                       eDYNAMICS_TYPE dynamics_type,
                                       UBLK ublk,
                                       asINT32 voxel) {
  PARCEL_STATE new_parcel = NULL;
  asINT32 x_ublk[N_SPACE_DIMS];
  sPARTICLE_VAR r[N_SPACE_DIMS];
  if ( ublk!=NULL  ) {
    if ( ublk->fluid_like_voxel_mask.test(voxel) ) {

      new_parcel = xnew sPARCEL_STATE(g_particle_sim_info.emitters[emitter_id]->get_next_parcel_id(),
                                      g_timescale.time_flow(),
                                      my_proc_id,
                                      emitter_id,
                                      num_particles_in_new_parcel,
                                      num_initial_reflections,
                                      dynamics_type,
                                      g_particle_sim_info.emitters[emitter_id]->material()->breakup_allowed(),
                                      (sPARTICLE_VAR)-0.266,//y_breakup: Assumed the droplet was born "tear-shaped" or "snowcone-shaped"
                                      (sPARTICLE_VAR)0.0,   // dydt: Assumed the parts of the droplet do not move relative each other at birth (arguable!)
                                      (auINT32)0,           //include drag from inception
                                      particle_density,
                                      particle_kinematic_viscosity,
                                      particle_surface_tension,
                                      diameter,
                                      x,
                                      v,
                                      ublk->ref_frame_index());


      if (new_parcel==NULL) {
        return(NULL); //could not allocate new parcel
      }

      new_parcel->m_home_voxel = voxel;
      new_parcel->unevaporated_mass = unevaporated_mass;

      if (interpret_velocity_as_drift) {
        asINT32 prior_index = g_timescale.m_lb_tm.prior_timestep_index(ublk->scale());
        sPARTICLE_VAR local_fluid_vel[N_SPACE_DIMS];
        local_fluid_vel[0] = ublk->lb_data()->m_lb_data[prior_index].vel[0][voxel];
        local_fluid_vel[1] = ublk->lb_data()->m_lb_data[prior_index].vel[1][voxel];
        local_fluid_vel[2] = ublk->lb_data()->m_lb_data[prior_index].vel[2][voxel];
#if BUILD_D19_LATTICE
	if(sim.is_pf_model) {
          local_fluid_vel[0] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[0][voxel];
          local_fluid_vel[1] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[1][voxel];
          local_fluid_vel[2] = ublk->pf_data()->m_pf_data[prior_index].vel_pfld[2][voxel];
        }
#endif
        vinc(new_parcel->v[0],local_fluid_vel);
      }

      UBLK_PARTICLE_DATA p_data = ublk->p_data();
      PARCEL_LIST  parcel_list = p_data->fluid_parcel_list[voxel];

      if (dynamics_type == IN_FLUID) {
        parcel_list->add(new_parcel);
      } else {
        msg_internal_error("A parcel was added to a ublk with the wrong dynamics type.");
      }

      //Set states needed for the surface tension model
#ifdef COMPILE_SURFACE_TENSION_MODEL
      vzero(new_parcel->v_st[0]);
      vzero(new_parcel->r_st[0]);
      new_parcel->time_st[0] = 0.0;
#endif

      new_parcel->aggl_applied = particle_aggl_applied;

      if(PARCEL_IS_INTERESTING(new_parcel)) {
        msg_print("Parcel %d %g, %g, %g (%d) added to ublk %d (%d, %d, %d) voxel %d at time %ld with velocity %g %g %g.",
                  new_parcel->id,
                  new_parcel->x[new_parcel->relative_timestep_to_state_index(0)][0],
                  new_parcel->x[new_parcel->relative_timestep_to_state_index(0)][1],
                  new_parcel->x[new_parcel->relative_timestep_to_state_index(0)][2],
                  new_parcel->relative_timestep_to_state_index(0),
                  ublk->id(),
                  ublk->location(0),
                  ublk->location(1),
                  ublk->location(2),
                  voxel,
                  g_timescale.m_time,
                  new_parcel->v[new_parcel->relative_timestep_to_state_index(0)][0],
                  new_parcel->v[new_parcel->relative_timestep_to_state_index(0)][1],
                  new_parcel->v[new_parcel->relative_timestep_to_state_index(0)][2]);
      }
    }
  }
  return new_parcel; //return a pointer to the new particle on success and NULL on fail
}

asINT32 sPARTICLE_SIM::initialize(VOID) {
  //g_has_trajectory_windows = g_trajectory_windows.size() > 0; //moved to meas.cc for fix to PR40289.  This needs to set whether particle modeling is enabled or not.

  //Emitters will have to transform initial velocites differently depending wether the ublk it seeds at is in an LRF or not.
  //A reference to the LRF that a ublk resides in is therefor added to the ublk particle
  //solver data block and is initialized here.
#ifdef TO_BE_DONE_LRF
  init_lrf_reference_for_particle_emitters(); //maybe this can be eliminated.
#endif
  //initialize the physics variables in the emitter configurations
  ccDOTIMES(emitter_configuration_index, g_particle_sim_info.emitter_configurations.size()) {
    g_particle_sim_info.emitter_configurations[emitter_configuration_index]->initialize_physics_variables();
  }

  // Set minimum particle diameter for drag calculation
  g_particle_sim_info.min_diameter_for_drag_calculation = particle_sim.convert_dimensional_parameter(1.0e-06, "m", "LatticeLength"); // One micron in lattice units

  //Scan for the minimum re-entrainment length.
  g_particle_sim_info.m_min_reentrainment_length = 1e6; //infinity
  ccDOTIMES(surface_material_index, g_particle_sim_info.num_surface_properties) {
    ccDOTIMES(parcel_material_index, g_particle_sim_info.num_materials) {
      SURFACE_INTERACTION_PROPERTY surface_interaction_params = g_particle_sim_info.surface_interaction_matrix[surface_material_index][parcel_material_index];
      if (surface_interaction_params->reentrainment_allowed()){
        if (surface_interaction_params->reentrainment_length() < g_particle_sim_info.m_min_reentrainment_length) {
          g_particle_sim_info.m_min_reentrainment_length = surface_interaction_params->reentrainment_length();
        }
      }
    }
  }
  //msg_warn("Minimum re-entrainment length is %g",g_particle_sim_info.m_min_reentrainment_length);


  //Build an octree for each piece of geometry any of the emitters will use.
  //points:
  ccDOTIMES(emission_geometry_index, g_particle_sim_info.emission_points.size()) {
    sEMISSION_POINTS &points = g_particle_sim_info.emission_points[emission_geometry_index];
    points.precompute_geometry_chars();
  }

  //boxes:
  ccDOTIMES(emission_geometry_index, g_particle_sim_info.emission_boxes.size()) {
    sEMISSION_BOX &box = g_particle_sim_info.emission_boxes[emission_geometry_index];
    box.precompute_geometry_chars();
  }

  //cylinders:
  ccDOTIMES(emission_geometry_index, g_particle_sim_info.emission_cylinders.size()) {
    sEMISSION_CYLINDER &cylinder = g_particle_sim_info.emission_cylinders[emission_geometry_index];
    cylinder.precompute_geometry_chars();
  }

  //surfaces:
  ccDOTIMES(emission_geometry_index, g_particle_sim_info.emission_surfaces.size()) {
    sEMISSION_SURFACE &surface = g_particle_sim_info.emission_surfaces[emission_geometry_index];
    surface.precompute_geometry_chars();

  }

  //initialize the collision detection extrapolation parameter
  g_particle_sim_info.collision_detection_projection_time = 1.0;

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
  if (sim.is_film_solver) {
    compute_film_solver_stencils_and_create_film_only_ghosts();
  } else {
    create_surfel_film_send_and_receive_groups();
  }
  init_surfel_film_send_and_recv_groups();
#endif


  // Neighbor ublks of surfels are assigned in these two functions. The
  // neighbors are sources of parcels that can reach this surfel.
  assign_neighbor_ublks();
  comm_neighbor_ublks_for_surfels();

  //Mark surfels that are set as wipeable.
  flag_surfels_wiped_by_virtual_wiper_models();

  //Initialize mlrf groups for parcel support.
  DO_MLRF_SURFEL_GROUPS(group) {
    group->initialize_for_parcels();
  }

  
  //Convert each ublk's window combination index to a bitset indicating which fluid variables need to be computed for measurement.
  g_meas_window_combinations.compute_bitsets();
  DO_SCALES_FINE_TO_COARSE(scale) {
    {
      DO_NEARBLKS_OF_SCALE(ublk, scale) {
        int combo_index = ublk->p_data()->s.m_window_data.m_window_combination_index;
        ublk->p_data()->s.m_window_data.m_required_meas_variables = g_meas_window_combinations.meas_var_bitset(combo_index);
      }
    }
    {
      DO_FARBLKS_OF_SCALE(ublk, scale) {
        int combo_index = ublk->p_data()->s.m_window_data.m_window_combination_index;
        ublk->p_data()->s.m_window_data.m_required_meas_variables = g_meas_window_combinations.meas_var_bitset(combo_index);
      }
    }
  }


  //Initiialize a sPARTICLE_MEAS_DCACHE object 
  g_particle_meas_dcache = xnew sPARTICLE_MEAS_DCACHE();

  //sPARCEL_STATE::dynamics_name() might be needed for debugging so make at least one call to it here to ensure that it
  //is linked into the executable.
  sPARCEL_STATE dummy_parcel;
  dummy_parcel.change_dynamics_type(ABOUT_TO_BE_DELETED); //keep the destructor from complaining.
  const char* dummy_string = dummy_parcel.dynamics_name();

#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).
    printf(
           "emitted parcel count, "
           "fluid parcel count, "
           "massless tracer count, "
           "surface parcel accumulation count, "
           "film dynamics count, "
           "reentrained parcel count, "
           "surface parcel dynamics count, "
           "numerically reentrained count, "
           "mlrf film collision count, "
           "fluid parcel collision count, "
           "sample surface collision count, "
           "sample surface film collision count, "
           "screen collision count, "
           "mlrf collision count, "
           "bsurfel ccollision count, "
           "surface parcel send count, "
           "fluid parcel send count, "
           "surface parcel receive count, "
           "fluid parcel receive count");
#endif



  return 0;
}


dFLOAT get_emitter_projection_factor(dFLOAT normal[], dFLOAT gravity[], dFLOAT velocity[], BOOLEAN err_flag)
{
  if (sqrt(vdot(velocity, velocity)) < 1e-6) {
    if (err_flag)
      msg_error("initial velocity of the rain emitter is too small");
    return 0.0;
  } else {
    vunitize(velocity);

    //particle velocity is parallel to the floor or the emitter face, invalid
    if (vdot(velocity, gravity) < 1e-4) {
      if (err_flag)
        msg_error("initial velocity of the rain emitter parallel to the ground");
      return 0.0;
    } else if (fabs(vdot(velocity, normal)) < 1e-4) {
      if (err_flag)
        msg_error("initial velocity of the rain emitter is parallel to the emitter plane");
      return 0.0;
    }
    return fabs(vdot(velocity, normal)/vdot(velocity, gravity));
  }
}

VOID compute_rain_emitter_emission_rates()
{
  ccDOTIMES(emitter_index, g_particle_sim_info.emitters.size()) {
    PARTICLE_EMITTER_BASE emitter = g_particle_sim_info.emitters[emitter_index];
    if(emitter->is_rain_emitter()) {
      sPARTICLE_VAR rain_emitter_air_velocity[N_SPACE_DIMS];
      ((RAIN_EMITTER_CONFIGURATION)emitter->emitter_configuration())->free_stream_velocity(rain_emitter_air_velocity);

      RAIN_EMITTER_BASE rain_emitter=static_cast<RAIN_EMITTER_BASE>(emitter);
      SIMUTILS_PARTICLE_EMISSION_RATE_CONVERTER rate_calculator = rain_emitter->emission_rate_calculator();

      rate_calculator->recompute_rain_emitter_release_rate(
                                                           emitter->emitter_configuration()->simutils_emission_rate_type(),
                                                           emitter->emitter_configuration()->emission_rate(),
                                                           g_particle_sim_info.gravity,
                                                           rain_emitter_air_velocity);

      if(rate_calculator->error_code()) {
#ifdef WHY_WONT_THIS_LINK
        msg_error("Problem encountered computing a rain emitter release rate: %s. %s",
                  rate_calculator->error_code_string().c_str(),
                  rate_calculator->error_code_explanation().c_str());
#else
        msg_error("Problem encountered computing a rain emitter release rate.");
#endif
      }
      rain_emitter->set_rain_emitter_particle_emission_rate(rate_calculator->particles_per_timestep());

#if 0
      msg_print("Rain emitter emission rate %g.\n", rate_calculator->particles_per_timestep());
#endif
    }
  }
}


VOID sPARTICLE_SIM::terminal_velocity(PARTICLE_VAR local_gravity_vector,
                                      sPARTICLE_VAR rho_air,
                                      sPARTICLE_VAR kinematic_viscosity_of_air,
                                      sPARTICLE_VAR rho_particle,
                                      sPARTICLE_VAR particle_diameter,
                                      PARTICLE_VAR terminal_velocity_vector) {

  sPARTICLE_VAR gravity_magnitude = std::sqrt(vdot(local_gravity_vector, local_gravity_vector));
  sPARTICLE_VAR terminal_velocity_magnitude =
    g_terminal_velocity_table.compute_terminal_velocity_magnitude(gravity_magnitude,
                                                                  rho_air,
                                                                  kinematic_viscosity_of_air,
                                                                  rho_particle,
                                                                  particle_diameter);
  if (gravity_magnitude < 1e-9) {
    vzero(terminal_velocity_vector);
  } else {
    vscale(terminal_velocity_vector, terminal_velocity_magnitude / gravity_magnitude, local_gravity_vector);
  }
}


asINT32 sPARTICLE_SIM::uninitialize(VOID)
{
  return 0;
}


sPARTICLE_VAR sPARTICLE_SIM::limit_delta_t_in_range(sPARTICLE_VAR delta_t, SCALE scale) {
  if (delta_t < 0)
    delta_t = 0;
  else if (delta_t > scale_to_voxel_size(scale))
    delta_t = scale_to_voxel_size(scale);
  return delta_t;
}

/* This function checks all dynamic ublk as if it has a parcel inside each voxel
 * The parcel will try to travel all 27 locations.
 */
void sPARTICLE_SIM::init_parcel_ublk_check(){
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(farblk, scale) {
      init_parcel_ublk_check(farblk);
    }
  }
}

void sPARTICLE_SIM::init_parcel_ublk_check(UBLK ublk){
  dFLOAT voxel_dx = sim_scale_to_voxel_size(ublk->scale());
  ccDOTIMES(ith_voxel, 8){
    STP_LOCATION voxel_location;
    ublk->get_voxel_location(ith_voxel, voxel_location);
    ccDOTIMES(ith, 3){
      ccDOTIMES(jth, 3){
        ccDOTIMES(kth, 3){
          dFLOAT dummy_parcel_old_location[3];
          dFLOAT dummy_parcel_new_location[3];
          dummy_parcel_old_location[0] = voxel_location[0] + 0.5 * voxel_dx + (ith - 1.0) * 0.1 * voxel_dx ;
          dummy_parcel_old_location[1] = voxel_location[1] + 0.5 * voxel_dx + (jth - 1.0) * 0.1 * voxel_dx ;
          dummy_parcel_old_location[2] = voxel_location[2] + 0.5 * voxel_dx + (kth - 1.0) * 0.1 * voxel_dx ;
          dummy_parcel_new_location[0] = dummy_parcel_old_location[0] + (ith - 1.0) * 0.8 * voxel_dx;
          dummy_parcel_new_location[1] = dummy_parcel_old_location[1] + (jth - 1.0) * 0.8 * voxel_dx;
          dummy_parcel_new_location[2] = dummy_parcel_old_location[2] + (kth - 1.0) * 0.8 * voxel_dx;

          try {
            sHOME_UBLK_LOCATOR finder(ublk, ith_voxel, dummy_parcel_new_location, false, dummy_parcel_old_location);
            finder.locate();

          }
          catch(sHOME_UBLK_LOCATOR::SearchError& error_message) {
            sdFLOAT temp_location[3];
            vcopy(temp_location, dummy_parcel_old_location);
            simerr_report_error_code(SP_EER_UBLK_NEIGHBOR_MISSING, ublk->scale(), temp_location, error_message.what(), //For internal users, use something of value.
                                     (dFLOAT)dummy_parcel_new_location[0] - (dFLOAT)dummy_parcel_old_location[0],
                                     (dFLOAT)dummy_parcel_new_location[1] - (dFLOAT)dummy_parcel_old_location[1],
                                     (dFLOAT)dummy_parcel_new_location[2] - (dFLOAT)dummy_parcel_old_location[2]);
          }
        }
      }
    }
  }
}



template <typename SURFEL_A_TYPE, typename SURFEL_B_TYPE>
BOOLEAN check_edges_for_interesection(
                                      SURFEL_A_TYPE surfel_a,
                                      SURFEL_B_TYPE surfel_b,
                                      BOOLEAN &some_edges_spanned_plane_of_surfel_b,
                                      sPARTICLE_VAR tolerance) {
  //Return TRUE if an edge of surfel_a intersects surfel_b.  Also set
  //some_edges_spanned_plane_of_surfel_b to true if an edge spanned
  //the plane of surfel b but didn't necessarily intersect within b's perimeter.

  //Assume the caller will not pass two parallel surfels to this function.
  some_edges_spanned_plane_of_surfel_b = FALSE;
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  asINT32 n_vertices = surfel_a->p_data()->n_vertices;
  asINT32 first_vertex_index = surfel_a->p_data()->first_vertex_index;
#else
  asINT32 n_vertices = surfel_a->stencil()->n_vertices;
  asINT32 first_vertex_index = surfel_a->stencil()->first_vertex_index;
#endif
  asINT32 which_side;
  SIM_VERTEX previous_vertex = nullptr;
  ccDOTIMES(vertex_num, n_vertices + 1) {
    //Check if all vertices of surfel_a are on the same side of the
    //surfel b, if one edge spans the plane of the surfel_b check it
    //the intersection point is within the perimeter of surfel b.
    SIM_VERTEX vertex = surfel_vertex_from_global_index(first_vertex_index + vertex_num % n_vertices );
    sPARTICLE_VAR vertex_displacement[N_SPACE_DIMS];
    vsub(vertex_displacement, vertex->coord, surfel_b->centroid);
    sPARTICLE_VAR normal_coordinate = vdot(vertex_displacement, surfel_b->normal);
    if(vertex_num ==0 ) {
      which_side = std::copysign(1, normal_coordinate); //first vertex checked
    } else {
      if(which_side != std::copysign(1, normal_coordinate)) {
        some_edges_spanned_plane_of_surfel_b = TRUE;
        which_side = std::copysign(1, normal_coordinate);
        //Find the position this edge of surfel a crossed the plane of surfel b.
        sPARTICLE_VAR edge_vector[N_SPACE_DIMS];
        vsub(edge_vector, vertex->coord, previous_vertex->coord);
        sPARTICLE_VAR position[N_SPACE_DIMS];
        sPARTICLE_VAR edge_fraction =  - normal_coordinate / vdot(edge_vector, surfel_b->normal);
        vcopy(position, previous_vertex->coord);
        vmac(position, edge_fraction, edge_vector);
        //Check if that position is within the film surfel's perimeter.
        if(particle_sim.position_is_within_surfel_perimeter(surfel_b, position) + tolerance > 0.0)
          return TRUE;
      }
    }
    previous_vertex = vertex;
  }
  return FALSE;
}

#if 1
template <typename SURFEL_TYPE>
static BOOLEAN surfel_intersects_surfel(SURFEL_TYPE surfel, sSURFEL* film_carrying_surfel, sPARTICLE_VAR tolerance) {
  //This function is used to decide if a given sample surfel (or mlrf
  //surfel) should be linked to a film carying dyn surfel. This is needed
  //for measuring film fluxes on sample surfels and for transiting
  //film across sliding mesh interfaces

  if(!film_carrying_surfel->is_wall())
    return FALSE;

  sPARTICLE_VAR planes_intersect_somewhere = vdot(surfel->normal, film_carrying_surfel->normal);
  if(planes_intersect_somewhere != 0.0 )
    return FALSE;

  //If any of surfel's edges intersect the film_carrying_surfel, then they definitely intersect.
  BOOLEAN vertices_span_surfel_plane = FALSE;
  if(check_edges_for_interesection(surfel, film_carrying_surfel, vertices_span_surfel_plane, tolerance))
    return TRUE;

  //If all of surfel's vertices are on the same side of the film-carrying surfel , then the surfels cannot intersect.
  if(!vertices_span_surfel_plane)
    return FALSE;

  //If any of surfel's edges spanned the plane of the film-carrying
  //surfel but none of the points it crossed at were within the
  //film-carrying surfel's perimeter (e.g. surfel encompases the
  //film-carrying surfel), we still need to check if any of the film
  //carrying surfels edges crossed through the perimeter of the
  //surfel.
  if(check_edges_for_interesection(film_carrying_surfel, surfel, vertices_span_surfel_plane, tolerance))
    return TRUE;

  //The surfels definitely do not intersect.
  return FALSE;
}
#else

template <typename SURFEL_TYPE>
static BOOLEAN surfel_intersects_surfel(SURFEL_TYPE surfel, sSURFEL* film_surfel, sPARTICLE_VAR tolerance) {
  //This function is used to decide if a given sample surfel (or mlrf
  //surfel) should be linked to a film carying dyn surfel. This is needed
  //for measuring film fluxes on sample surfels and for transiting
  //film across sliding mesh interfaces

  if(!film_surfel->is_wall())
    return FALSE;

  sPARTICLE_VAR planes_intersect_somewhere = vdot(surfel->normal, film_surfel->normal);
  if(planes_intersect_somewhere != 0 )
    return FALSE;

  {   //Check if all vertices of surfel are on the same side of the film carying surfel.
    BOOLEAN vertices_span_surfel_plane = FALSE;
    asINT32 which_side;
    asINT32 n_vertices = surfel->p_data()->n_vertices;
    asINT32 first_vertex_index = surfel->p_data()->first_vertex_index;
    ccDOTIMES(vertex_num, n_vertices) {
      SIM_VERTEX vertex = surfel_vertex_from_global_index(first_vertex_index + vertex_num);
      sPARTICLE_VAR vertex_displacement[N_SPACE_DIMS];
      vsub(vertex_displacement, vertex->coord, film_surfel->centroid );
      sPARTICLE_VAR normal_distance = vdot(vertex_displacement, film_surfel->normal);

      if(vertex_num ==0 ) {
        which_side = std::copysign(1, normal_distance); //first vertex checked
      } else {
        if(which_side != std::copysign( 1, normal_distance)) {
          vertices_span_surfel_plane = TRUE;
          break;
        }
      }
    }
    if(!vertices_span_surfel_plane)
      return FALSE; //if all of surfel A's vertices are on the same side of surfel B, they cannot intersect
  }

  {   //Next check if all vertices of the film carying surfel are on the same side of the surflel.
    BOOLEAN vertices_span_surfel_plane = FALSE;
    asINT32 which_side;
    asINT32 n_vertices = film_surfel->p_data()->n_vertices;
    asINT32 first_vertex_index = film_surfel->p_data()->first_vertex_index;
    ccDOTIMES(vertex_num, n_vertices) {
      SIM_VERTEX vertex = surfel_vertex_from_global_index(first_vertex_index + vertex_num);
      sPARTICLE_VAR vertex_displacement[N_SPACE_DIMS];
      vsub(vertex_displacement, vertex->coord, surfel->centroid);
      sPARTICLE_VAR normal_distance = vdot(vertex_displacement, surfel->normal);

      if(vertex_num == 0) {
        which_side = std::copysign(1, normal_distance);
      } else {
        if(which_side != std::copysign( 1, normal_distance)) {
          vertices_span_surfel_plane = TRUE;
          break;
        }
      }
    }
    if(!vertices_span_surfel_plane)
      return FALSE; //if all of surfel B's vertices are on the same side of surfel A, then they cannot intersect (even if some of A's spanned B's plane)
  }

  //The above calculation still needs improvement, each surfel's vertices can cross the other surfel's plane and the surfels still not intersect (two perpindicular squares centered on the x-y orgin but one translated on z).
  //But for a simple case, the above check took the number of surfel pairs from ~5k to ~500

  sPARTICLE_VAR centroid_displacement[N_SPACE_DIMS];
  vsub(centroid_displacement, surfel->centroid, film_surfel->centroid);
  sPARTICLE_VAR distance_sqr = vdot(centroid_displacement, centroid_displacement);
  if(distance_sqr > 5.0 ) {
    return FALSE;
  }

  return TRUE;
}
#endif

//Tolerance for deciding wether a mlrf or sample surfel intersects a film-carrying surfel.
#define MLRF_SURFEL_INTERSECTION_TOLERANCE 0.3
//#define MLRF_SURFEL_INTERSECTION_TOLERANCE 0.5
#define SAMPLE_SURFEL_INTERSECTION_TOLERANCE 0.02

VOID sPARTICLE_SIM::find_surfels_that_intersect_sampling_surfaces() {
#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).printf("Started generating surfel/sample surfel pairs.");
#endif
  asINT32 surfel_pairs_found = 0;
  DO_SCALES_FINE_TO_COARSE(sample_surface_scale) {
    DO_SAMPLING_SURFELS_OF_SCALE(sample_surfel, sample_surface_scale) {
      DO_SCALES_FINE_TO_COARSE(dyn_surfel_scale) {
        DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, dyn_surfel_scale) {
          if(surfel_intersects_surfel(sample_surfel, surfel, SAMPLE_SURFEL_INTERSECTION_TOLERANCE)) {

            if(sample_surfel->p_data()->intersecting_surfels == nullptr)
              sample_surfel->p_data()->intersecting_surfels = xnew std::vector<sSURFEL*>;

            //Give the sampeling sufels a reference to the film carrying surfels.
            sample_surfel->p_data()->intersecting_surfels->push_back(surfel);
            surfel_pairs_found++;
          }
        }
      }
    }
  }
#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).
    printf("Finished generating surfel/sample surfel pairs. Found %d pairs.",
           surfel_pairs_found);
#endif
}

static VOID add_mlrf_surfel_pair(SURFEL mlrf_surfel, SURFEL film_carrying_surfel) {

  if(film_carrying_surfel->p_data()->intersecting_surfels == nullptr)
    film_carrying_surfel->p_data()->intersecting_surfels = xnew std::vector<sSURFEL*>;

  film_carrying_surfel->p_data()->intersecting_surfels->push_back(mlrf_surfel);

  //Also, Give the mlrf surfel a reference to the film-carrying surfel so it can re-emit surface parcels.
  //However if it is a ghost, it shouldn't need to emitt parcels, only their corresponding dynamics mlrf surfel should.
  if(mlrf_surfel->is_ghost())
    return;

  if(mlrf_surfel->p_data()->intersecting_surfels == nullptr)
    mlrf_surfel->p_data()->intersecting_surfels = xnew std::vector<sSURFEL*>;

  mlrf_surfel->p_data()->intersecting_surfels->push_back(film_carrying_surfel);
}


VOID sPARTICLE_SIM::find_surfels_that_intersect_mlrf_interfaces() {
#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).printf("Started generating surfel/mlrf surfel pairs.");
#endif
  asINT32 surfel_pairs_found = 0;
  DO_SCALES_FINE_TO_COARSE(mlrf_surfel_scale) {
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, mlrf_surfel_scale) {
      for (auto& quantum: group->quantums()) {
        SURFEL mlrf_surfel = quantum.mlrf_surfel();
        DO_SCALES_FINE_TO_COARSE(dyn_surfel_scale) {
          DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, dyn_surfel_scale) {
            if(surfel_intersects_surfel(mlrf_surfel, surfel, MLRF_SURFEL_INTERSECTION_TOLERANCE)) {
              //Give the film-carrying surfel a reference to to the sliding mesh surfel if they're in the same frame.
              if(surfel->ref_frame_index() == mlrf_surfel->ref_frame_index()) {
                add_mlrf_surfel_pair(mlrf_surfel, surfel);
                surfel_pairs_found++;
              }
            }
          }
        }
      }
    }

    //repeat for MLRF ghost surfels
    DO_SURFEL_RECV_GROUPS_OF_SCALE(group, mlrf_surfel_scale) {
      for (const auto& quantum: group->quantums()) {
        if(!quantum.m_surfel->is_lrf())
          continue;
        SURFEL mlrf_surfel = quantum.m_surfel;
        DO_SCALES_FINE_TO_COARSE(dyn_surfel_scale) {
          DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, dyn_surfel_scale) {
            if(surfel_intersects_surfel(mlrf_surfel, surfel, MLRF_SURFEL_INTERSECTION_TOLERANCE)) {
              //Give the film-carrying surfel a reference to to the sliding mesh surfel if they're in the same frame.
              if(surfel->ref_frame_index() == mlrf_surfel->ref_frame_index()) {
                add_mlrf_surfel_pair(mlrf_surfel, surfel);
                surfel_pairs_found++;
              }
            }
          }
        }
      }
    }
  }


#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).
    printf("Finished generating surfel/mlrf surfel pairs. Found %d pairs.",
           surfel_pairs_found);
#endif
}

VOID sPARTICLE_SIM::log_debug_summary() {
#if DEBUG_PARTICLE_SUMMARY
  LOG_MSG("PARTICLE_SUMMARY", LOG_TIME).
    printf(
           "%d " //emitted parcel count
           "%d " //fluid parcel count
           "%d " //massless tracer count
           "%d " //surface parcel accumulation count
           "%d " //film dynamics count
           "%d " //reentrained parcel count
           "%d " //surface parcel dynamics count
           "%d " //numerically reentrained count
           "%d " //mlrf film collision count
           "%d " //fluid parcel collision count
           "%d " //sample surface collision count
           "%d " //sample surface film collision count
           "%d " //screen collision count
           "%d " //mlrf collision count
           "%d " //bsurfel ccollision count
           "%d " //surface parcel send count
           "%d " //fluid parcel send count
           "%d " //surface parcel receive count
           "%d", //fluid parcel receive count
           g_emitted_parcel_count,
           g_parcel_dynamics_count,
           g_parcel_massless_tracer_dynamics_count,
           g_accumulated_parcel_count,
           g_film_dynamics_count,
           g_reentrained_parcel_count,
           g_surface_parcel_dynamics_count,
           g_numerically_reentrained_parcel_count,
           g_parcel_mlrf_film_collision_count,
           g_parcel_collision_count,
           g_parcel_sample_surface_collision_count,
           g_parcel_sample_surface_film_collision_count,
           g_parcel_screen_collision_count,
           g_parcel_mlrf_collision_count,
           g_parcel_bsurfel_collision_count,

           g_surface_parcel_send_count,
           g_fluid_parcel_send_count,
           g_surface_parcel_receive_count,
           g_fluid_parcel_receive_count);

  g_parcel_collision_count = 0;
  g_parcel_collision_count = 0;
  g_parcel_sample_surface_collision_count = 0;
  g_parcel_sample_surface_film_collision_count = 0;
  g_parcel_screen_collision_count = 0;
  g_parcel_mlrf_collision_count = 0;
  g_parcel_bsurfel_collision_count = 0;
  g_parcel_dynamics_count = 0;
  g_parcel_massless_tracer_dynamics_count = 0;
  g_film_dynamics_count = 0;
  g_accumulated_parcel_count = 0;
  g_film_dynamics_count = 0;
  g_reentrained_parcel_count = 0;
  g_parcel_mlrf_film_collision_count = 0;
  g_surface_parcel_dynamics_count = 0;
  g_numerically_reentrained_parcel_count = 0;
  g_surface_parcel_send_count = 0;
  g_fluid_parcel_send_count = 0;
  g_surface_parcel_receive_count = 0;
  g_fluid_parcel_receive_count = 0;
#endif
}



template <typename SURFEL_TYPE>
sPARTICLE_VAR sPARTICLE_SIM::compute_normal_distance_to_centroid(sPARTICLE_VAR x[3], SURFEL_TYPE surfel) {
  sPARTICLE_VAR r[N_SPACE_DIMS];
  vsub(r, x, surfel->centroid);
  return vdot(r, surfel->normal);
}

//Specialized the above for bsurfels until sSURFEL_BASE conforms to the style guide (m_normal instead of normal, m_centroid instead of centroid, ..etc)
//This also means that this template can't be made static to give it a storage class of this file.
template <>
sPARTICLE_VAR sPARTICLE_SIM::compute_normal_distance_to_centroid(sPARTICLE_VAR x[3], sBSURFEL* bsurfel) {
  sPARTICLE_VAR r[N_SPACE_DIMS];
  vsub(r, x, bsurfel->centroid());
  return vdot(r, bsurfel->normal());
}

template sPARTICLE_VAR sPARTICLE_SIM::compute_normal_distance_to_centroid(sPARTICLE_VAR x[3], SURFEL surfel);
template sPARTICLE_VAR sPARTICLE_SIM::compute_normal_distance_to_centroid(sPARTICLE_VAR x[3], SAMPLING_SURFEL surfel);
