/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Nagendra Krishnamurthy, Dassault Systemes
 * Created March 4, 2019
 *--------------------------------------------------------------------------*/

#include "conduction_data.h"
#include "ublk.h"
#include "surfel.h"
#include "conduction_shell_edge.h"
#include PHYSICS_H

inline namespace SIMULATOR_NAMESPACE {
sCONDUCTION_SIM_INFO g_conduction_sim_info;
sSURFELS_SHELL_LAYER_DATA g_surfels_shell_layer_data;
std::vector<sIMPLICIT_SOLVER_STENCIL_ERROR> g_implicit_shell_solver_simerrs;
}

LAYER_RAD_DATA sSHELL_LAYER_DATA::layer_rad_data() {
  return &g_surfels_shell_layer_data.layer_rad_data.at(layer_rad_data_index);
}

// template VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, sFLOAT>(vxFLOAT vec[3], const sFLOAT voxel_size);
// template VOID maybe_apply_periodicity_correction_to_vector<vxFLOAT, dFLOAT>(vxFLOAT vec[3], const dFLOAT voxel_size);

VOID update_vrcoarse_nearblk_passthrough_nbr_sum(UBLK dyn_ublk) {
  const sdFLOAT vol_scaling = 1./((sdFLOAT)(1 << sim.num_dims));
  sVR_COARSE_INTERFACE_DATA* vr_coarse_data = (sVR_COARSE_INTERFACE_DATA*)dyn_ublk->vr_data();
  DO_VOXELS_IN_MASK(vrcoarse_voxel, dyn_ublk->fluid_like_voxel_mask) {
    UBLK vrfine_ublk = vr_coarse_data->vr_fine_ublk(vrcoarse_voxel).ublk();
    if (vr_fine_ublk_is_invalid(vrfine_ublk,0)) {
      continue;
    }
      
    sdFLOAT VRCoarseVoxelPfluidSum = 0.;
    DO_VOXELS_IN_MASK(vrfine_voxel, vrfine_ublk->fluid_like_voxel_mask) {
      // this is the sum of the vrFine voxel's fine neighbors' pfluids
      sdFLOAT VRFineVoxelPfluidSum = vrfine_ublk->surf_conduction_data()->passthrough_summ_inv[vrfine_voxel];

      // This function will not be called for the vrFine ublks, so invert the sum of neighbor pfluids for this vrfine voxel.
      // If the sum is 0, set passthrough_summ_inv to 0. This is expected for any vrFine voxels not in contact with
      // any fine neighbor voxels
      vrfine_ublk->surf_conduction_data()->passthrough_summ_inv[vrfine_voxel] = 
            (VRFineVoxelPfluidSum > 0.) ? (1./VRFineVoxelPfluidSum) : 0.; 

      // Add vrFine voxels pfluid sum to VRCoarseVoxelPfluidSum
      VRCoarseVoxelPfluidSum += VRFineVoxelPfluidSum;
    }
    // add scaled VRCoarseVoxelPfluidSum to vrCoarse voxel's passthrough_summ_inv
    dyn_ublk->surf_conduction_data()->passthrough_summ_inv[vrcoarse_voxel] += VRCoarseVoxelPfluidSum*vol_scaling;
  }
}


VOID finalize_conduction_nearblk_passthrough_weights(UBLK dyn_ublk) {
  cassert(dyn_ublk->is_near_surface());

  VOXEL_MASK_8 voxel_mask = dyn_ublk->fluid_like_voxel_mask;

#ifdef CONDUCTION_ENABLE_PTHRU_COARSE_TO_FINE
  if (dyn_ublk->is_vr_coarse()) {
    update_vrcoarse_nearblk_passthrough_nbr_sum(dyn_ublk);
  }
#endif

  DO_VOXELS_IN_MASK(voxel, voxel_mask) {
    sdFLOAT sumPfluid = dyn_ublk->surf_conduction_data()->passthrough_summ_inv[voxel];
    if (sumPfluid > 0.) {
      dyn_ublk->surf_conduction_data()->passthrough_summ_inv[voxel] = 1./sumPfluid;
      dyn_ublk->surf_conduction_data()->passthrough_summ_inv[voxel] = 1./sumPfluid;
    } else {
      STP_LOCATION voxel_loc;
      dyn_ublk->get_voxel_location(voxel, voxel_loc);
      STP_GEOM_VARIABLE voxel_loc_f[3];
      voxel_loc_f[0] = voxel_loc[0];
      voxel_loc_f[1] = voxel_loc[1];
      voxel_loc_f[2] = voxel_loc[2];
      sdFLOAT pfluidVal = dyn_ublk->is_near_surface() ? dyn_ublk->surf_geom_data()->pfluids[voxel] : 1.0;

      simerr_report_error_code(SP_EER_SOLID_CONDUCTION_PASSTHROUGH_DISABLED, dyn_ublk->scale(), voxel_loc_f,
          dyn_ublk->id(), voxel, dyn_ublk->is_vr_fine(), dyn_ublk->is_vr_coarse(), pfluidVal);
      dyn_ublk->surf_conduction_data()->passthrough_summ_inv[voxel] = 0.;
      dyn_ublk->surf_conduction_data()->passthrough_summ_inv[voxel] = 0.;
    }
  }
}

/**
 @brief Function computes the surface contribution to components of Matrix \f$ \textbf B \f$. The surface information is a combination of Dirichlet and Neumann information. Currently we are mostly using Neumann (grad_T at surfaces)
 @details Weighting function
 \f[ 
 W_S(\alpha) = \frac{A_\alpha}{V(x_0)} \hat{W}(r)\\
 r = \frac{d}{d_{outer}}
 \f]
 where \f$ \hat{W}(r) \f$ 
 is computed in ::ls_dist_wt_quartic as
 \copydetails ::ls_dist_wt_quartic
 
 The Neumann weight \f$ W_{S,N} = \beta W_S(\alpha) \f$ and the Dirichlet weight \f$ W_{S,D} = (1-\beta)W_S(\alpha) \f$, where the factor \f$ \beta \f$ is determined according to different surface type. In most cases, \f$ \beta = 1 \f$.

 @note 
 \f$ W_S(\alpha) \f$ factors in the surfel area \f$ A_{\alpha} \f$ 
 considering tiny surfels (as well as tiny voxels) may have much larger unwanted oscillations. 
 The weights assigned to these surfels should be relatively small. 
 However the operation \f$ A_{\alpha} / V(x_0) \f$ might introduce 
 a very large weight if the base voxel \f$ x_0 \f$ is tiny. 
 Since the weighting function for voxel \f$ W_V(x)\f$ also has a \f$ V(x) / V(x_0)\f$ factor, 
 this might not be an issue as all the weights are scaled by the same volume 
 (in fact we could even remove \f$ V(X_0) \f$). 
 The best practice might be normalizing this factor inside the stencil.

 The moment matrix \f$ \textbf P \f$ is formulated as
 \f[
  \textbf{P}_N = \textbf{P} = {\left[ \begin{matrix}
   1 & x_1-x_0 & y_1-y_0 & z_1-z_0  \\
   1 & x_2-x_0 & y_2-y_0 & z_2-z_0  \\
   \vdots & \vdots & \vdots & \vdots    \\
   1 & x_{\alpha}-x_0 & y_{\alpha}-y_0 & z_{\alpha}-z_0  \\
\end{matrix} \right]}  
\f]
\f[
  \textbf{P}_D = {\left[ \begin{matrix}
   0 & \textbf{n}_{x1} & \textbf{n}_{y1} & \textbf{n}_{z1}  \\
   0 & \textbf{n}_{x2} & \textbf{n}_{y2} & \textbf{n}_{z2}  \\
   \vdots & \vdots & \vdots & \vdots    \\
   0 & \textbf{n}_{x\alpha} & \textbf{n}_{y\alpha} & \textbf{n}_{z\alpha}  \\
\end{matrix} \right]}  
 \f]
 */
VOID compute_ls_surfel_weights(UBLK ublk, asINT32 voxel, sSURFEL *surfel,
                               sdFLOAT &weight_dirichlet, sdFLOAT &weight_neumann,
                               sdFLOAT (&term_dirichlet)[4], sdFLOAT (&term_neumann)[4])
{
  dFLOAT voxel_cen[3];
  ccDOTIMES (c, 3)
    voxel_cen[c] = ublk->centroids(voxel, c);

  asINT32 voxel_size = scale_to_voxel_size(ublk->scale());

  sdFLOAT voxel_pfluid = ublk->is_near_surface() ? ublk->surf_geom_data()->pfluids[voxel] : 1.0;

  dFLOAT rel_surf_cen[3] = {  surfel->centroid[0] - voxel_cen[0],
    surfel->centroid[1] - voxel_cen[1],
    surfel->centroid[2] - voxel_cen[2] };

  ccDOTIMES (axis, 3) {
    if (rel_surf_cen[axis] > 2.0 * voxel_size)
      rel_surf_cen[axis] -= sim.simvol_size[axis];
    else if (rel_surf_cen[axis] < -2.0 * voxel_size)
      rel_surf_cen[axis] += sim.simvol_size[axis];
  }

  vmul(rel_surf_cen, 1.0 / voxel_size);

  sdFLOAT area_scaling = get_area_scaling(surfel->scale() - ublk->scale());

  dFLOAT dist = vlength(rel_surf_cen);
  sdFLOAT w_dist = dist / g_pfc_ls_dist_outer;
  sdFLOAT weight = 1.0 * ls_dist_wt_quartic(w_dist) * surfel->area * area_scaling / voxel_pfluid;

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  if (weight > 0.0 && ((ublk->id() == 1 && voxel == 2 && sim.num_scales == 2)
        || (ublk->id() == 141 && voxel == 0 && sim.num_scales == 1))) {
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%d(%d), s: %d normal: %g %g %g",
        ublk->id(), voxel, surfel->id(), surfel->normal[0], surfel->normal[1], surfel->normal[2]);
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("s_cen: %.10g %.10g %.10g",
        surfel->centroid[0], surfel->centroid[1], surfel->centroid[2]);
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("v_cen: %.10g %.10g %.10g",
        voxel_cen[0], voxel_cen[1], voxel_cen[2]);
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("rel cen: %.10g %.10g %.10g, wt: %g",
        rel_surf_cen[0], rel_surf_cen[1], rel_surf_cen[2], weight);
  }
#endif

  auto * surfel_conduction = surfel->conduction_data();
  auto * ublk_conduction = ublk->conduction_data();

  BOOLEAN is_interface_surfel = surfel->is_conduction_interface() || (surfel_conduction->bc_type == INTERFACE_BC); 
  BOOLEAN is_lrf_surfel = surfel->is_conduction_lrf();

  /* (1) Regular surfels, temp_bc > 0.0 implies prescribed temp
   * (2) Regular surfels, temp_bc <= 0.0 implies (prescribed flux / thermal resistance / adiabatic)
   * (3) Interface surfels, double-sided surfels that separate two materials
   *
   * (2) is always treated as neumann.
   * (1) and (3) are treated can be treated as neumann depending on parameter value */
  const BOOLEAN treat_like_neumann = (g_pfc_surface_adiabatic_treatment == 1)
                                     || (is_interface_surfel && g_pfc_interface_ls_contribution_like_q_bc == 1)
                                     || (is_lrf_surfel && g_pfc_lrf_ls_contribution_like_q_bc == 1)
                                     || (surfel_conduction->temp_bc > 0.0 && g_pfc_temp_bc_ls_contribution_like_q_bc == 1)
                                     || (surfel_conduction->temp_bc <= 0.0);

  if (g_pfc_ls_surfel_info_dirichlet_factor > 0.0)
    msg_internal_error("Partial Neumann treatment not implemented in LS surfel contribution");

  if (treat_like_neumann) {
    sdFLOAT factor = 0.0;
    if (is_interface_surfel) {
      factor = g_pfc_ls_neumann_weight_interface_surfel_factor;
    } else if(is_lrf_surfel) {
      factor = g_pfc_ls_neumann_weight_lrf_surfel_factor;  //must be "1.0", because no wall temp is calculated for lrf surfels
    } else {
      if (surfel_conduction->is_prescribed_temp())
        factor = g_pfc_ls_neumann_weight_temp_bc_surfel_factor;
      else if (surfel_conduction->is_prescribed_flux())
        factor = g_pfc_ls_neumann_weight_flux_bc_surfel_factor;
      else if (surfel_conduction->is_thermal_resistance())
        factor = g_pfc_ls_neumann_weight_thermal_resistance_bc_surfel_factor;
      else {
        msg_print("Conduction surfel BC types are: \"INVALID_BC\" (-1), \"PRESCRIBED_TEMP_BC\" (%d),"
                  " \"PRESCRIBED_FLUX_BC\" (%d), \"THERMAL_RESISTANCE_BC\" (%d), \"INTERFACE_BC\" (%d),"
                  " \"LRF_BC\" (%d)", CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_TEMP_BC,
                  CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_FLUX_BC, CONDUCTION_SURFEL_BC_TYPE::THERMAL_RESISTANCE_BC,
                  CONDUCTION_SURFEL_BC_TYPE::INTERFACE_BC, CONDUCTION_SURFEL_BC_TYPE::LRF_BC);
        msg_internal_error("Conduction surfel dynamics has to be type %d, %d, or %d. Surfel %d is type %d.",
            CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_TEMP_BC, CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_FLUX_BC,
            CONDUCTION_SURFEL_BC_TYPE::THERMAL_RESISTANCE_BC, surfel->id(), surfel_conduction->bc_type);
      }
    }
    weight_dirichlet = (1.0 - factor) * weight;
  } else {
    weight_dirichlet = 1.0 * weight;
  }

  weight_neumann = weight - weight_dirichlet;

  term_dirichlet[0] = 1.0;
  term_dirichlet[1] = rel_surf_cen[0];
  term_dirichlet[2] = rel_surf_cen[1];
  term_dirichlet[3] = rel_surf_cen[2];
  term_neumann[0] = 0.0;
  term_neumann[1] = surfel->normal[0];
  term_neumann[2] = surfel->normal[1];
  term_neumann[3] = surfel->normal[2];

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  if (weight > 0.0 && ((ublk->id() == 1 && voxel == 2 && sim.num_scales == 2)
        || (ublk->id() == 26 && voxel == 4 && sim.num_scales == 1))) {
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("%d -> %d(%d)", surfel->id(), ublk->id(), voxel);
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("dirichlet %.17g, terms: %.17g %.17g %.17g %.17g",
        weight_dirichlet, term_dirichlet[0], term_dirichlet[1], term_dirichlet[2], term_dirichlet[3]);
    LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("neumann %.17g, terms: %.17g %.17g %.17g %.17g",
        weight_neumann, term_neumann[0], term_neumann[1], term_neumann[2], term_neumann[3]);
  }
#endif
}

template<>
VOID sSURFEL_CONDUCTION_DATA::set_bc_type_from_pd(sSURFEL* surfel, PHYSICS_DESCRIPTOR pd, STP_PHYSTYPE_TYPE phystype) {
  //STP_PHYSTYPE_TYPE phystype = surfel_sim_type_from_cdi_type(pd);
  //if (phystype == STP_INVALID_PHYSTYPE_TYPE)
  //  phystype = surfel_sim_type_from_cdi_type(pd);
  CONDUCTION_SURFEL_BC_TYPE bc;

  // Flow-types are added here because in a flow-conduction case, surfaces
  // get the flow types
  switch(phystype) {
    case STP_CONDUCTION_FIXED_TEMP_SURFEL_TYPE:
      bc = PRESCRIBED_TEMP_BC;
      break;
    case STP_CONDUCTION_ADIABATIC_SURFEL_TYPE:
      bc = ADIABATIC_BC;
      break;
    case STP_CONDUCTION_FIXED_HEAT_FLUX_SURFEL_TYPE:
      bc = PRESCRIBED_FLUX_BC;
      break;
    case STP_CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_TYPE:
      bc = THERMAL_RESISTANCE_BC;
      break;
    case STP_CONDUCTION_COUPLED_THERMAL_SURFEL_TYPE:
    case STP_CONDUCTION_CONTACT_SURFEL_TYPE:
      bc = INTERFACE_BC;
      break;
    case STP_MLRF_SURFEL_TYPE:
    case STP_SLRF_SURFEL_TYPE:
      bc = LRF_BC;
      break;  
    default:
      msg_internal_error("Unhandled BC type: %d", phystype);
  }

  if (bc == INTERFACE_BC && !surfel->is_conduction_interface()) {
    simerr_report_error_code(SP_EER_MISSING_COND_INTERFACE_NBR, surfel->scale(), cast_as_regular_array(surfel->centroid),
                             surfel->id(), surfel->m_face_index, "");
  }

  bc_type = bc;
}

template<>
VOID sSURFEL_CONDUCTION_DATA::pre_advect_init_copy_even_to_odd(SURFEL_CONDUCTION_DATA even_surfel_conduction) {
  memcpy(this, even_surfel_conduction, sizeof(*this));
}

template<>
VOID sSURFEL_CONDUCTION_DATA::seed_copy_even_to_odd(SURFEL_CONDUCTION_DATA even_surfel_conduction) {
  memcpy(this, even_surfel_conduction, sizeof(*this));
}

template<>
VOID sSURFEL_CONDUCTION_DATA::reflect_from_mirror_surfel_to_real_surfel(SURFEL_CONDUCTION_DATA mirror_conduction_data){
  temp_sample += mirror_conduction_data->temp_sample;
  rho_C_p_sample += mirror_conduction_data->rho_C_p_sample;
  grad_t_dot_n += mirror_conduction_data->grad_t_dot_n;
  q_anisotropic += mirror_conduction_data->q_anisotropic;
  conductivity_nn_sample += mirror_conduction_data->conductivity_nn_sample;
}

template<>
VOID sSURFEL_CONDUCTION_DATA::reflect_from_real_surfel_to_mirror_surfel(SURFEL_CONDUCTION_DATA source_conduction_data)
{
  heat_bc = source_conduction_data->heat_bc;
  heat_flux = source_conduction_data->heat_flux;
  heat_flux_prime = source_conduction_data->heat_flux_prime;
  grad_t_dot_n = source_conduction_data->grad_t_dot_n;
  grad_t_dot_n_prime = source_conduction_data->grad_t_dot_n_prime;
  temp_bc = source_conduction_data->temp_bc;
  wall_temp = source_conduction_data->wall_temp;
  temp_sample = source_conduction_data->temp_sample;
  conductivity_nn_sample = source_conduction_data->conductivity_nn_sample;
  conductivity_nn_sample_prime = source_conduction_data->conductivity_nn_sample_prime;
  q_anisotropic = source_conduction_data->q_anisotropic;
  rho_C_p_sample = source_conduction_data->rho_C_p_sample; 
}

VOID sSAMPLING_SURFEL_CONDUCTION_DATA::pre_advect_init_copy_even_to_odd(SAMPLING_SURFEL_CONDUCTION_DATA even_surfel_conduction_data)
{
  memcpy(this, even_surfel_conduction_data, sizeof(*this));
}

/*******************************************************************************
 * SHELL CONDUCTION RELATED DEFINITIONS
 ******************************************************************************/
#if DEBUG
constexpr asINT32 D_SURFEL_ID_1 = 375;
constexpr asINT32 D_SURFEL_ID_2 = 570;
#endif

/*******************************************************************************
 * @brief   Initialize shell conduction data structure
 ******************************************************************************/
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::init() {
  if (!sim.enable_tangential_shell_conduction) {
    stencil_info_internal_edge_index = 0;
    stencil_info_boundary_edge_index = 0;
    stencil_info_vertex_nbr_index = 0;
    stencil_info_end_index = 0;
  }
}

/*******************************************************************************
 * @brief   Copy seeded grad_t values from index 1 to 0
 ******************************************************************************/
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::copy_seed_grad_t() {
#if !BUILD_5G_LATTICE

  /** Copy grad_t values from index 0 to 1 */
  ccDOTIMES (n, this->num_layers()) {
    SHELL_LAYER_DATA nth_layer = this->layer(n);
    vcopy2(nth_layer->grad_t[0], nth_layer->grad_t[1]);
  }
#endif
}

/*******************************************************************************
 * @brief   Reset shell conduction values for new time-step
 ******************************************************************************/
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::reset_grad_t(const asINT32 prior_index) {
  const asINT32 next_index = prior_index ^ 1;

  /** Re-initialize all grad_t values to 0 */
  ccDOTIMES (n, this->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(n);
    vzero2(root_surfel_nth_layer->grad_t[next_index]);
  }
}

template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::add_vertex_nbr_lsq_contribution(SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil, const asINT32 prior_index) {
  const asINT32 nbr_prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(nbr_stencil->surfel()->scale());
  const sdFLOAT* stencil_surfel_lsq_coeff = nbr_stencil->get_lsq_coeff();
  SURFEL_SHELL_CONDUCTION_DATA nbr_shell_data = nbr_stencil->surfel()->shell_conduction_data();
  ccDOTIMES (n, this->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(n);
    const sdFLOAT temp_diff = nbr_shell_data->layer(n)->temp(nbr_prior_index) - root_surfel_nth_layer->temp(prior_index);
    vmac2(root_surfel_nth_layer->grad_t[prior_index ^ 1], temp_diff, stencil_surfel_lsq_coeff);
  }
}

template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::add_internal_edge_lsq_contribution(SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil,
    const asINT32 prior_index, std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max) {
  const asINT32 nbr_prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(nbr_stencil->surfel()->scale());
  const sdFLOAT* stencil_surfel_lsq_coeff = nbr_stencil->get_lsq_coeff();

  CONDUCTION_EDGE_INTERNAL nbr_edge = nbr_stencil->get_internal_edge();
  SURFEL_SHELL_CONDUCTION_DATA nbr_shell_data = nbr_stencil->surfel()->shell_conduction_data();
  ccDOTIMES (n, this->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(n);
    // in case the layers are flipped, we use the internal edge object to obtain the correct neighboring layer
    const sdFLOAT nbr_temp = nbr_shell_data->layer(nbr_edge->neighbor_layer_index(n))->temp(nbr_prior_index);
    temp_cap_min[n] = MIN(temp_cap_min[n], nbr_temp);
    temp_cap_max[n] = MAX(temp_cap_max[n], nbr_temp);
    const sdFLOAT temp_diff = nbr_temp - root_surfel_nth_layer->temp(prior_index);
    vmac2(root_surfel_nth_layer->grad_t[prior_index ^ 1], temp_diff, stencil_surfel_lsq_coeff);
  }
}

template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::add_boundary_edge_lsq_contribution(SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_stencil,
    const asINT32 prior_index, std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max) {
  const sdFLOAT* stencil_surfel_lsq_coeff = nbr_stencil->get_lsq_coeff();
  ccDOTIMES (n, this->num_layers()) {
    SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(n);
    const sdFLOAT nbr_temp = nbr_stencil->get_boundary_edge_temperature(root_surfel_nth_layer, prior_index);
    temp_cap_min[n] = MIN(temp_cap_min[n], nbr_temp);
    temp_cap_max[n] = MAX(temp_cap_max[n], nbr_temp);
    const sdFLOAT temp_diff = nbr_temp - root_surfel_nth_layer->temp(prior_index);
    vmac2(root_surfel_nth_layer->grad_t[prior_index ^ 1], temp_diff, stencil_surfel_lsq_coeff);
  }
}

#if !GPU_COMPILER && !BUILD_GPU
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::assemble_internal_edge_gradient_coefficients(sSURFEL* surfel) {

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();

  // Get the scale factor for the gradient coefficients
  asINT32 scale_diff = -scale_to_log2_voxel_size(surfel->scale());
  sdFLOAT scale_factor = vr_inverse_length_scale_factor(scale_diff);
  DO_INTERNAL_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR int_edge_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);

    const sdFLOAT* stencil_surfel_lsq_coeff = int_edge_stencil->get_lsq_coeff();
    PetscInt nbr_column_index = int_edge_stencil->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index;

    ccDOTIMES(nth_layer, this->num_layers()) {
      // Assuming that all layers match up
      SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(nth_layer);
  
      // off-diagonal terms
      STATE_VECTOR_INDEX column_index = nbr_column_index + nth_layer;
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[0][column_index] = scale_factor * stencil_surfel_lsq_coeff[0];
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[1][column_index] = scale_factor * stencil_surfel_lsq_coeff[1];

      // diagonal terms
      column_index = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
      ccDOTIMES(i, 2) {
        if (root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i].count(column_index) == 0) {
          root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] = 0.0;
        }
        root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] -= scale_factor * stencil_surfel_lsq_coeff[i];
      }
    }
  }
}

template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::assemble_vertex_nbr_gradient_coefficients(sSURFEL* surfel) {

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();

  // Get the scale factor for the gradient coefficients
  asINT32 scale_diff = -scale_to_log2_voxel_size(surfel->scale());
  sdFLOAT scale_factor = vr_inverse_length_scale_factor(scale_diff);
  DO_VERTEX_NEIGHBOR_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR vertex_nbr_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);

    const sdFLOAT* stencil_surfel_lsq_coeff = vertex_nbr_stencil->get_lsq_coeff();
    PetscInt nbr_column_index = vertex_nbr_stencil->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index;

    ccDOTIMES(nth_layer, this->num_layers()) {
      // Assuming that all layers match up
      SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(nth_layer);

      // off-diagonal terms
      STATE_VECTOR_INDEX column_index = nbr_column_index + nth_layer;
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[0][column_index] = scale_factor * stencil_surfel_lsq_coeff[0];
      root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[1][column_index] = scale_factor * stencil_surfel_lsq_coeff[1];

      // diagonal terms
      column_index = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
      ccDOTIMES(i, 2) {
        if (root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i].count(column_index) == 0) {
          root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] = 0.0;
        }
        root_surfel_nth_layer->layer_matrix_data.stencil_coeffs[i][column_index] -= scale_factor * stencil_surfel_lsq_coeff[i];
      }
    }
  }
}

template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::assemble_boundary_edge_gradient_coefficients(sSURFEL* surfel) {

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();

  // Get the scale factor for the gradient coefficients
  asINT32 scale_diff = -scale_to_log2_voxel_size(surfel->scale());
  sdFLOAT scale_factor = vr_inverse_length_scale_factor(scale_diff);

  DO_BOUNDARY_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_edge_stencil = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    const sdFLOAT* stencil_surfel_lsq_coeff = bnd_edge_stencil->get_lsq_coeff();
    ccDOTIMES (nth_layer, this->num_layers()) {
      SHELL_LAYER_DATA root_surfel_nth_layer = this->layer(nth_layer);
      STATE_VECTOR_INDEX column_index = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
      bnd_edge_stencil->assemble_layer_boundary_edge_gradient_coefficients(root_surfel_nth_layer, stencil_surfel_lsq_coeff, column_index, scale_factor);
    }
  }
}
#endif

/**
  @brief   Accumulate stencil surfel contribution to shell LS gradient
 
  @details This function primarily does two operations:
           (1) utilize prior_index temperature at stencil surfels and update
               their contribution directly to root surfel's gradient
 */
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::gather_grad_t_from_stencil_surfels(const asINT32 prior_index,
    std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max) {
  /** Loop over all surfels in root surfel's stencil */
  DO_INTERNAL_EDGE_STENCILS(this, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR int_edge_stencil =
        g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    add_internal_edge_lsq_contribution(int_edge_stencil, prior_index, temp_cap_min, temp_cap_max);
  }

  DO_BOUNDARY_EDGE_STENCILS(this, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_edge_stencil =
        g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    add_boundary_edge_lsq_contribution(bnd_edge_stencil, prior_index, temp_cap_min, temp_cap_max);
  }

  DO_VERTEX_NEIGHBOR_STENCILS(this, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR vert_stencil =
        g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    add_vertex_nbr_lsq_contribution(vert_stencil, prior_index);
  }
  // printf("T1 = %f, dT1 = (%e, %e), T2 = %f, dT2 = (%e, %e), T3 = %f, dT3 = (%e, %e)\n",
  //   this->layer(0)->temp(prior_index),this->layer(0)->grad_t[prior_index^1][0],this->layer(0)->grad_t[prior_index^1][1],
  //   this->layer(1)->temp(prior_index),this->layer(1)->grad_t[prior_index^1][0],this->layer(1)->grad_t[prior_index^1][1],
  //   this->layer(2)->temp(prior_index),this->layer(2)->grad_t[prior_index^1][0],this->layer(2)->grad_t[prior_index^1][1]);
}

/**
@brief   Compute and store LS gradient contributions

@details This function accumulates grad_t contributions from all surfels 
         and BC edges in this surfel's stencil. All shell and boundary
         neighbors used in the calculation are stored in the root surfel's
         stencil.
*/
template<>
VOID sSURFEL_SHELL_CONDUCTION_DATA::compute_grad_t(const asINT32 prior_index, std::vector<sdFLOAT>& temp_cap_min, std::vector<sdFLOAT>& temp_cap_max)
{
  reset_grad_t(prior_index);

  /** Accumulate information from stencil surfels */
  gather_grad_t_from_stencil_surfels(prior_index, temp_cap_min, temp_cap_max);
}

template<>
__HOST__DEVICE__
VOID sSURFEL_CONDUCTION_DATA::update_underrelaxed_heat_flux(const BOOLEAN perform_underrelaxation,
    const sdFLOAT heat_flux_value) {
  /* Under-relaxation is not needed during seeding. For
   * non-seeding calls, only one side of a double-sided
   * interface needs it. The other side passes the already
   * under-relaxed value as the second parameter. This is
   * taken care of in the perform_underrelaxation parameter at call site */
  if (perform_underrelaxation) {
    heat_flux_prime =
      g_pfc_heat_flux_underrelaxation_factor * heat_flux_value
      + (1.0 - g_pfc_heat_flux_underrelaxation_factor) * heat_flux_prime;
  } else {
    heat_flux_prime = heat_flux_value;
  }
}

inline namespace SIMULATOR_NAMESPACE {
std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>> g_ckpt_conduction_interface_accumulations_map;
std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>> g_ckpt_shell_layer_accumulations_map;

VOID fill_ckpt_shell_layer_accumulations() {
#if !BUILD_5G_LATTICE
  for (auto &it : g_ckpt_shell_layer_accumulations_map) {
    //header with surfel ID
    sSURFEL* surfel = regular_surfel_from_id (it.first.first, it.first.second);
    std::vector<dFLOAT> &accumulations = it.second;

    if (surfel!=NULL) {
      SURFEL_SHELL_CONDUCTION_DATA shell_data = surfel->shell_conduction_data();
      ccDOTIMES(n, shell_data->num_layers()) {
        SHELL_LAYER_DATA nth_layer = shell_data->layer(n);
        nth_layer->accumulated_dT = accumulations.at(2*n);
        nth_layer->dT_prime = accumulations.at(2*n+1);
      }   
    }   
  }
#endif
  //Frees the memory used by the global map since it is no longer needed
  std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>>().swap(g_ckpt_shell_layer_accumulations_map);
}

VOID fill_ckpt_conduction_interface_accumulations() {
#if !BUILD_5G_LATTICE
  for (auto &it : g_ckpt_conduction_interface_accumulations_map) {
    //header with surfel ID
    sSURFEL* surfel = regular_surfel_from_id(it.first.first, it.first.second);
    std::vector<dFLOAT> &accumulations = it.second;
   
    if (surfel!=NULL) {
      //accumulated heat flux 
      if (surfel->is_conduction_surface()) {
        surfel->conduction_interface_solid_data()->accumulated_heat_flux += accumulations[0];
        surfel->conduction_interface_solid_data()->s2v_heat_flux_prime = accumulations[1];
      } else if (!surfel->is_conduction_shell() && surfel->is_conduction_interface()) {
        //(for flow surfels, checks that is actually coupled since open shells might be coupled only to back)
        surfel->conduction_interface_fluid_data()->accumulated_heat_flux += accumulations[0];
        surfel->conduction_interface_fluid_data()->s2v_heat_flux_prime = accumulations[1];
      }
      
      //additionally:
      // - flow surfels with valid opposite surfel are part of a conduction open shell and need to load the accumulated
      //   data of the back
      if (!surfel->is_conduction_shell() && !surfel->is_conduction_surfel()) {
        sSURFEL* opp_surfel = static_cast<sSURFEL*>(surfel->opposite_surfel());
        if (opp_surfel) {
          if (opp_surfel->is_conduction_interface()) {
            opp_surfel->conduction_interface_fluid_data()->accumulated_heat_flux += accumulations[2];
            opp_surfel->conduction_interface_fluid_data()->s2v_heat_flux_prime = accumulations[3];
          }
        }
      }
    }
  }
#endif
  //Frees the memory used by the global map since it is no longer needed
  std::map<std::pair<SHOB_ID, STP_REALM>, std::vector<dFLOAT>>().swap(g_ckpt_conduction_interface_accumulations_map);
}
}

#if !BUILD_5G_LATTICE
template<>
VOID sCONDUCTION_INTERFACE_BASE_DATA::init(sSURFEL *surfel, DGF_SURFEL_DESC surfel_desc) {
  if (surfel_desc->s.coupled_index > -1) {
    m_coupled_surfel_id = surfel_desc->s.coupled_index;
  } else {
    m_coupled_surfel_id = INVALID_SHOB_ID;
  }

  // JEH This code assumes a conduction surfel has an LB data block, which is nonsense
  surfel->lb_data()->boundary_condition_type = BOUNDARY_CONDITION_CONDUCTION_INTERFACE;
    
  // DFG-TODO For open shells, which is the volume physic descriptor?
  // sINT32 this_region = surfel_desc->s.fluid_region_index;
  // PHYSICS_DESCRIPTOR phys_desc = volume_physics_desc_from_part_index(this_region);

  // Even shell surfels perform all their shell conduction operations via the odd clones
  if (!surfel->is_even() && (surfel->is_conduction_shell())) {
    // sCONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR *pd = (sCONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR *)phys_desc;
    sCONDUCTION_ADIABATIC_SURFEL_PHYSICS_DESCRIPTOR *pd = NULL; // JEH See what crashes with this set to NULL
    dFLOAT sum_pgram_volumes[N_NONZERO_ENERGIES] = {};
    ((CONDUCTION_ADIABATIC_SURFEL_DATA)surfel->dynamics_data())->init(pd, surfel, sum_pgram_volumes);
  }
}

template<>
VOID sCONDUCTION_INTERFACE_FLUID_DATA::accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel, 
                                                                                        BOOLEAN is_seeding) {
  if (is_seeding) {
    avg_htc = eff_htc;
    avg_temp_near_wall = temp;
    htc_accum = 0.0;
    temp_near_wall_accum = 0.0;
  } else {
    // add current value to the accumulator:
    htc_accum += eff_htc;
    temp_near_wall_accum += temp;
    
    asINT32 scale = (surfel->is_even_or_odd()) ? coarsen_scale(surfel->scale()) : surfel->scale();
    if (sim.time_coupling_info.compute_average(scale)) {
      const asINT32 surfel_update_freq = scale_to_delta_t(scale);
      const sTIME_COUPLING_PHASE& active_phase = sim.time_coupling_info.active_phase();
      avg_htc = htc_accum * surfel_update_freq / active_phase.flow_avg_interval;
      avg_temp_near_wall = temp_near_wall_accum * surfel_update_freq / active_phase.flow_avg_interval;
      // resets accumulator to zero
      htc_accum = 0.0;
      temp_near_wall_accum = 0.0;
    }
  }
}

template<>
VOID sCONDUCTION_INTERFACE_SOLID_DATA::accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel,
                                                                                        BOOLEAN is_seeding) {
  // add current value to the accumulator:
  if (surfel->is_conduction_shell()) {
    asINT32 conduction_prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
    if (g_timescale.is_conduction_supercycled()) {
      //accumulated_dT from the flow intermediate calculations not received yet, uses instead previous timestep
      conduction_prior_index ^= 1;
    }
    temp_accum += surfel->shell_conduction_data()->layer(0)->temp(conduction_prior_index);
  } else {
    temp_accum += temp; 
  }

  if (is_seeding) {
    avg_temp = temp_accum;
    temp_accum = 0.0;
  } else {
    asINT32 scale = (surfel->is_even_or_odd()) ? coarsen_scale(surfel->scale()) : surfel->scale();
    if (sim.time_coupling_info.compute_average(scale)) {
      const asINT32 surfel_update_freq = scale_to_delta_t(scale);
      const sTIME_COUPLING_PHASE& active_phase = sim.time_coupling_info.active_phase();
      avg_temp = temp_accum * surfel_update_freq / active_phase.conduction_avg_interval;
      // resets accumulator to zero
      temp_accum = 0.0;
    }
  }
}

template<>
VOID sCONDUCTION_INTERFACE_OPEN_SHELL_DATA::accumulate_and_maybe_average_coupling_parameters(sSURFEL *surfel,
                                                                                             BOOLEAN is_seeding) {
  // add current value to the accumulator:
  sSURFEL_SHELL_CONDUCTION_DATA *shell_conduction_data = surfel->shell_conduction_data();
  asINT32 num_layers = shell_conduction_data->num_layers();
  asINT32 conduction_prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
  if (g_timescale.is_conduction_supercycled()) {
    //accumulated_dT from the flow intermediate calculations not received yet, uses instead previous timestep
    conduction_prior_index ^= 1;
  }
  temp_accum_front += shell_conduction_data->layer(0)->temp(conduction_prior_index);
  temp_accum_back += shell_conduction_data->layer(num_layers-1)->temp(conduction_prior_index);
  
  if (is_seeding) {
    avg_temp_front = temp_accum_front;
    avg_temp_back = temp_accum_back;
    temp_accum_front = 0.0;
    temp_accum_back = 0.0;
  } else {
    asINT32 scale = (surfel->is_even_or_odd()) ? coarsen_scale(surfel->scale()) : surfel->scale();
    if (sim.time_coupling_info.compute_average(scale)) {
      const asINT32 surfel_update_freq = scale_to_delta_t(scale);
      const sTIME_COUPLING_PHASE& active_phase = sim.time_coupling_info.active_phase();
      avg_temp_front = temp_accum_front * surfel_update_freq / active_phase.conduction_avg_interval;
      avg_temp_back = temp_accum_back * surfel_update_freq / active_phase.conduction_avg_interval;
      // resets accumulator to zero
      temp_accum_front = 0.0;
      temp_accum_back = 0.0;
    } 
  }
}

#endif

double sDATA_CURVES::interpolate(const std::string& prop_name, const double temp_value) const {

  // This assumes the material data is sorted in ascending order by temperature in the first column
  const auto& curve_data = mt_data.at(prop_name);

  const auto& first_entry = curve_data.front();
  const auto& last_entry = curve_data.back();

  if (temp_value <= first_entry.first) {
    return first_entry.second;
  }

  if (temp_value >= last_entry.first) {
    return last_entry.second;
  }

  auto upper = std::upper_bound(curve_data.begin(), curve_data.end(), std::make_pair(temp_value, 0.0),
                            [](const auto& a, const auto& b) { return a.first < b.first; });
  auto lower = std::prev(upper);

  double temp1 = lower->first;
  double prop_value1 = lower->second;
  double temp2 = upper->first;
  double prop_value2 = upper->second;

  return prop_value1 + (temp_value - temp1) * (prop_value2 - prop_value1) / (temp2 - temp1);

}
