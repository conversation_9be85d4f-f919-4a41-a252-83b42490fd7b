#include "implicit_solid_solver.h"
#include "sim.h"
#include PHYSICS_H

#if !GPU_COMPILER && !BUILD_GPU
#if !BUILD_5G_LATTICE
inline namespace SIMULATOR_NAMESPACE {
Mat g_solid_conduction_coeff_mat;
Vec g_solid_conduction_rhs_vec;
Vec g_solid_conduction_rhs_bc_vec;
Vec g_solid_conduction_source_vec;
Vec g_solid_conduction_solution_vec;
Vec g_solid_conduction_rho_cp_vec;
Vec g_solid_conduction_rho_cp_solution_vec;
KSP g_solid_ksp;
}

bool g_implicit_solid_solver_active = false;
pthread_mutex_t g_implicit_solid_solver_mutex;
pthread_cond_t g_implicit_solid_solver_cond;

bool g_implicit_solid_solver_setup_active = false;
bool g_implicit_solid_solver_setup_complete = false;
pthread_mutex_t g_implicit_solid_solver_setup_mutex;
pthread_cond_t g_implicit_solid_solver_setup_cond;

VOID setup_implicit_solid_solver() {
  if (!g_implicit_solid_solver_setup_complete) {
    pthread_mutex_lock(&g_implicit_solid_solver_setup_mutex);
    // Set up the mapping array for the matrix indices 
    int num_states = set_solid_solver_state_indices_on_sps();
    
    assemble_surfel_contributions();
    
    // Get the contributions of the neighboring voxel to the matrix
    assemble_voxel_coefficients<TRUE>();

    // Set up the linear system
    create_linear_system_for_solid_solver(num_states);
    
    g_implicit_solid_solver_setup_active = false;
    g_implicit_solid_solver_setup_complete = true;
    pthread_cond_signal(&g_implicit_solid_solver_setup_cond);
    pthread_mutex_unlock(&g_implicit_solid_solver_setup_mutex);
    
  }
}

int set_solid_solver_state_indices_on_sps() {
  int num_states = 0;
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        num_states += set_state_indices_on_sps_internal(ublk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        num_states += set_state_indices_on_sps_internal(nearblk);
      }
    }
#ifdef IMPLICIT_SOLVER_PROGRESS_VR
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        num_states += set_state_indices_on_sps_internal(fine_ublk);
      }
    }
#endif
  }
  return num_states;
}

int set_state_indices_on_sps_internal(UBLK ublk) {
  auto *dynamics_data = ublk->dynamics_data();
  CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
  VOXEL_MASK_8 conduction_solid_voxel_mask = conduction_dyn_data->voxel_mask();
  
  UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA ublk_implicit_solver_data = ublk->conduction_implicit_solver_data();
  DO_VOXELS_IN_MASK(voxel, conduction_solid_voxel_mask){
    //DEBUG_IMPLICIT
    //if (ublk->id()==213) 
    //  msg_print("set state U %d V %d local index %d", ublk->id(), voxel, ublk_implicit_solver_data->implicit_solid_state_index[voxel]);
    ublk_implicit_solver_data->implicit_solid_state_index[voxel] = ublk_implicit_solver_data->implicit_solid_state_index[voxel] 
                                                                 + sim.implicit_solid_solver_state_index_offset[my_proc_id];
    //DEBUG_IMPLICIT
    //if (ublk->id()==213) 
    //  msg_print("global index %d offset[%d] %d", ublk_implicit_solver_data->implicit_solid_state_index[voxel],my_proc_id, sim.implicit_solid_solver_state_index_offset[my_proc_id]);
  }
  
  return conduction_solid_voxel_mask.count();
}

VOID assemble_surfel_contributions() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      accumulate_surfel_contributions(surfel);
      //if (surfel->has_mirror()){
      //reflect TODO
      //}
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        accumulate_surfel_contributions(surfel);
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      //Skipping assembly for these guys for now
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      accumulate_surfel_contributions(wsurfel_conduction);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      accumulate_surfel_contributions(contact_surfel);
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum : surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        accumulate_surfel_contributions(surfel);
      }
    }
  }
}

//TODO similar to s2v maybe move to physics
/**
 @brief Compute the surface contributions to the diagonal terms and RHS constant terms.
 @details For Dirichlet surfels, the term
 \f[
 \Delta t \sum_{\alpha \in N(i)}\frac{k_{\alpha} A_{\alpha}w_\alpha}{\Delta x_{i\alpha}}
 \f]
 is accumulated here for each interacting voxel i and surfel \alpha and added to the diagonal term.
 
 Also the term
 \f[
 \sum_{\alpha}\frac{k_{\alpha}A_{\alpha}w_\alpha}{\Delta x_{i\alpha}}T_\alpha \Delta t
 \f]
 is accumulated here and added to the RHS as the constant term. 
 \f$T_\alpha\f$ represents the fixed temperature value at the surface.

 For Neumann surfels, there is no contribution to the diagonal term, only the term 
 \f[
 \sum_{\alpha} q_{\alpha}A_{i\alpha}w_\alpha\Delta t
 \f]
 is accumulated here and added to the RHS.

 Other types of surface coming soon...
 */
VOID accumulate_surfel_contributions(SURFEL surfel) {
  if (!surfel->is_conduction_surface()) {
    return;
  }
  if (surfel->is_even()) {
    return;
  }
  
  asINT32 allowed_phase_mask;
  //BOOLEAN HAS_NO_VR_INTERACTIONS = TRUE;
  //BOOLEAN is_conduction_time_step_odd = g_timescale.m_conduction_pde_tm.is_timestep_odd(surfel->scale());
  //if (!surfel->interacts_with_vr_ublks()) {
  //  allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
  //} else {
  //  HAS_NO_VR_INTERACTIONS = FALSE;
  //  allowed_phase_mask = !is_conduction_time_step_odd
  //                              ? STP_EVEN_S2V_PHASE_MASK
  //                              : STP_ODD_S2V_PHASE_MASK;
  //}
  
  allowed_phase_mask = FULL_PHASE_MASK & (~(STP_V2S_PHASE_MASK));
  
  SURFEL_UBLK_INTERACTION ublk_interaction = surfel->m_ublk_interactions;
  asINT32 n_ublks                          = surfel->m_n_ublk_interactions;
  
  for (asINT32 iu = 0; iu < n_ublks; iu++, ublk_interaction = ublk_interaction->next()) {
    UBLK ublk                  = ublk_interaction->ublk();
    
    if (ublk->is_ghost())
      continue;
    
    STP_SURFEL_WEIGHT *weights = ublk_interaction->weights();
    uINT8 *voxel_info          = ublk_interaction->voxel_info();
    auINT32 n_weight_sets      = ublk_interaction->m_n_weight_sets;
    uINT32  ublk_scale_diff    = surfel->scale() - ublk->scale();

    UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA ublk_implicit_solver_data = NULL;
    //if (is_conduction_active)
    ublk_implicit_solver_data = ublk->conduction_implicit_solver_data();
    
    ccDOTIMES (iv, n_weight_sets) {

      sSURFEL_VOXEL_INTERACTION s_v_interaction;
      voxel_info = ublk_interaction->voxel_interaction(voxel_info, &s_v_interaction);
      asINT32 n_voxel_weights = s_v_interaction.n_voxel_weights();
      asINT32 phase_mask      = s_v_interaction.phase_mask();
      STP_VSURFEL_WEIGHT_RATIO vsurfel_ratio = s_v_interaction.vsurfel_ratio();
    
      if (allowed_phase_mask & phase_mask) {
        
        asINT32 voxel           = s_v_interaction.voxel_id();
        sdFLOAT vector_to_voxel_centroid[3] = { ublk->centroids (voxel, 0) - surfel->centroid[0],
                                                ublk->centroids (voxel, 1) - surfel->centroid[1],
                                                ublk->centroids (voxel, 2) - surfel->centroid[2] };
        sdFLOAT normal_dist_to_centroid = ABS(vdot(vector_to_voxel_centroid, surfel->normal));
        
        auto* surfel_conduction = surfel->conduction_data();
        const asINT32 prior_conduction_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
        const asINT32 next_conduction_index = prior_conduction_index ^ 1;
        sdFLOAT surfel_area = surfel->area;
#ifndef IMPLICIT_SOLVER_PROGRESS_VR 
        asINT32 lambda = sim_num_scales() - surfel->scale() - 1;
        surfel_area *= 1 << (lambda * (sim.num_dims - 1));  //Scaling up area excluding interface, is it right? 
#endif
        
        if (surfel->is_conduction_interface() 
            || surfel_conduction->bc_type == INTERFACE_BC 
            || surfel_conduction->bc_type == LRF_BC) {
          //LM: explicitly added for now. heat_flux_prime is calculated based on local scale, so here surfel area should be used at local scale
            //uINT32 scale_diff = surfel->scale() - ublk->scale();
            //sdFLOAT q_bc = surfel_conduction->heat_flux_prime * get_area_scaling(scale_diff);  //surfel is at finer scale
            //ublk_implicit_solver_data->rhs_bc[voxel] += surfel->area * vsurfel_ratio * q_bc * g_timescale.conduction_delta_t(); 
            ublk_implicit_solver_data->rhs_bc[voxel] += surfel->area * vsurfel_ratio * surfel_conduction->heat_flux_prime * g_timescale.conduction_delta_t(); 
        } else if (surfel_conduction->is_prescribed_temp()){
          sdFLOAT surfel_coefficient_diagonal = vsurfel_ratio != 0 ? surfel_conduction->normal_conductivity() * surfel_area * vsurfel_ratio / normal_dist_to_centroid : 0; //ImplicitSolver-Check conductivity
          //DEBUG_IMPLICIT catch division by zero and protect it
          if (std::isinf(surfel_coefficient_diagonal) || std::isnan(surfel_coefficient_diagonal)){ 
            msg_warn("surfel coefficient is %g: U %d V %d S %d dist %g area %g vs %g. Setting it to zero.",surfel_coefficient_diagonal, ublk->id(), voxel, surfel->id(), normal_dist_to_centroid, surfel->area[0], vsurfel_ratio);
            surfel_coefficient_diagonal = 0.0;
          }
          ublk_implicit_solver_data->rhs_bc[voxel] += surfel_coefficient_diagonal * surfel_conduction->temp_bc * g_timescale.conduction_delta_t(); 
          ublk_implicit_solver_data->implicit_matrix_A_diagonal[voxel] += surfel_coefficient_diagonal * g_timescale.conduction_delta_t();
        } else if (surfel_conduction->is_prescribed_flux()){
          ublk_implicit_solver_data->rhs_bc[voxel] += surfel_area * vsurfel_ratio * surfel_conduction->heat_bc * g_timescale.conduction_delta_t(); 
          //msg_print("TS %d U %d V %d S %d u scale %d scale_diff %d heat_bc %g, rhs_bc = %g",g_timescale.m_time, ublk->id(), voxel, surfel->id(),ublk->scale(), ublk_scale_diff, surfel_conduction->heat_bc, ublk_implicit_solver_data->rhs_bc[voxel]);
        } else if (surfel_conduction->is_thermal_resistance()) { 
          CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA dyn_data = (CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_DATA)(surfel->dynamics_data());
          CONDUCTION_FIXED_HTC_AMBIENT_TEMP_SURFEL_PHYSICS_DESCRIPTOR pd = dyn_data->physics_descriptor();
          sdFLOAT htc = pd->parameters()->ext_heat_xfer_coeff.value * g_density_scale_factor;
          sdFLOAT ambient_temp = pd->parameters()->ambient_temp.value;
          sdFLOAT surfel_coefficient_diagonal = htc * surfel_area * vsurfel_ratio;
          ublk_implicit_solver_data->rhs_bc[voxel] += surfel_coefficient_diagonal * ambient_temp * g_timescale.conduction_delta_t(); 
          ublk_implicit_solver_data->implicit_matrix_A_diagonal[voxel] += surfel_coefficient_diagonal * g_timescale.conduction_delta_t();
            
          //DEBUG_IMPLICIT
          //if (ublk->id()==0 && voxel == 0) {
          //  msg_print("TS %d U %d V %d S %d ambient temp %g, htc %g, rhs_bc = %g Aii = %g",g_timescale.m_time, ublk->id(), voxel, surfel->id(), ambient_temp, htc, ublk_implicit_solver_data->rhs_bc[voxel], ublk_implicit_solver_data->implicit_matrix_A_diagonal[voxel]);
          //  msg_print("ublk ghost? %d surfel ghost ? %d", ublk->is_ghost(), surfel->is_ghost());
          //}

        } else { // what else could it be?
          msg_print("Conduction surfel BC types are: \"INVALID_BC\" (-1), \"PRESCRIBED_TEMP_BC\" (%d),"
                    " \"PRESCRIBED_FLUX_BC\" (%d), \"THERMAL_RESISTANCE_BC\" (%d), \"INTERFACE_BC\" (%d),"
                    " \"LRF_BC\" (%d)", CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_TEMP_BC,
                    CONDUCTION_SURFEL_BC_TYPE::PRESCRIBED_FLUX_BC, CONDUCTION_SURFEL_BC_TYPE::THERMAL_RESISTANCE_BC,
                    CONDUCTION_SURFEL_BC_TYPE::INTERFACE_BC, CONDUCTION_SURFEL_BC_TYPE::LRF_BC);
          msg_internal_error("BC type %d not supported in implicit solver yet", surfel_conduction->bc_type);
        }
      }
      voxel_info += n_voxel_weights;
      weights += n_voxel_weights;
    } 
  }
}

template <BOOLEAN IS_FIRST_SOLVE>
VOID assemble_voxel_coefficients() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        assemble_voxel_coefficients_internal<IS_FIRST_SOLVE>(ublk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        assemble_voxel_coefficients_internal<IS_FIRST_SOLVE>(nearblk);
      }
    }
#ifdef IMPLICIT_SOLVER_PROGRESS_VR
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        assemble_voxel_coefficients_internal<IS_FIRST_SOLVE>(fine_ublk);
      }
    }
#endif
  }
}
template VOID assemble_voxel_coefficients<FALSE>();
template VOID assemble_voxel_coefficients<TRUE>();


VOID create_linear_system_for_solid_solver(int num_states) {

  initialize_linear_system_for_solid_solver(num_states);
  preallocate_matrix_for_solid_solver();
  
  assemble_linear_system_for_solid_solver<TRUE>();
  finalize_assembly_for_solid_solver<TRUE>();
  create_solver_objects_for_solid_solver();

  bool save_system_as_txt = false;

  if (save_system_as_txt) {
    write_solid_linear_system_to_txt();
  }

}

VOID initialize_linear_system_for_solid_solver(int num_states) {
  PETSC_COMM_WORLD = eMPI_csp_comm;

  // Get the contributions of the flux to the matrix
  PetscMPIInt size;
  const char * help = "Implicit solid solver.\n\n"; //nullptr;
  //const std::filesystem::path& options_file = "implicit_shell_solver_options.txt";
  //check( PetscOptionsSetValue(nullptr, "-no_signal_handler", nullptr) );
  //check(PetscInitialize(nullptr, nullptr, options_file.c_str(), help)); // filename and help are optional
  check(PetscInitialize(nullptr, nullptr, nullptr, help)); // filename and help are optional
  check( MPI_Comm_size(eMPI_csp_comm, &size) );

  // Set up vectors for the system
  check( VecCreate(eMPI_csp_comm, &g_solid_conduction_solution_vec)); // Create an empty vector object
  check( PetscObjectSetName((PetscObject)g_solid_conduction_solution_vec, "Solution") ); // Not strictly necessary, but nice to have control over the name
  check( VecSetType(g_solid_conduction_solution_vec, VECSTANDARD) ); // seq on one process and mpi on multiple
  check( VecSetSizes(g_solid_conduction_solution_vec, num_states, sim.num_implicit_solid_solver_states) ); // Tells Petsc how big x is
  check( VecDuplicate(g_solid_conduction_solution_vec, &g_solid_conduction_rhs_vec) ); // Create b so that it's the same type as x. This way we don't need to repeat all the code above
  check( VecDuplicate(g_solid_conduction_solution_vec, &g_solid_conduction_rhs_bc_vec) ); // A vector to hold the boundary condition contributions to the RHS
  check( VecDuplicate(g_solid_conduction_solution_vec, &g_solid_conduction_source_vec) ); // A vector to hold the source term contributions to the RHS
  check( VecDuplicate(g_solid_conduction_solution_vec, &g_solid_conduction_rho_cp_vec) ); // Create b so that it's the same type as x. This way we don't need to repeat all the code above
  check( VecDuplicate(g_solid_conduction_solution_vec, &g_solid_conduction_rho_cp_solution_vec) ); // Create b so that it's the same type as x. This way we don't need to repeat all the code above

  // Set up system matrix
  check( MatCreate(eMPI_csp_comm, &g_solid_conduction_coeff_mat) );
  check( MatSetType(g_solid_conduction_coeff_mat, MATMPIAIJ) );
  check( MatSetSizes(g_solid_conduction_coeff_mat, num_states, num_states, sim.num_implicit_solid_solver_states, sim.num_implicit_solid_solver_states) );
  check( MatSetUp(g_solid_conduction_coeff_mat) ); // Sets up the internal matrix data structures for later use. 
                                                   // Need this here so we can access ownership range in the preallocation stage.
                                                   // The documentation said users rarely need to call it, but the code didn't work without it.
}

VOID preallocate_matrix_for_solid_solver() {

  PetscInt first_local_row_global_index;
  PetscInt last_local_row_global_index; // actually 1 more than this, but the name was getting too long
  check( MatGetOwnershipRange(g_solid_conduction_coeff_mat, &first_local_row_global_index, &last_local_row_global_index) );

  PetscInt num_states = last_local_row_global_index - first_local_row_global_index;

  std::vector<PetscInt> num_nonzeros_in_diagonal_block(num_states);
  std::vector<PetscInt> num_nonzeros_in_offdiagonal_block(num_states);

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        preallocate_matrix_internal(ublk, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        preallocate_matrix_internal(nearblk, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      }
    }
#ifdef IMPLICIT_SOLVER_PROGRESS_VR
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        preallocate_matrix_internal(fine_ublk, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      }
    }
#endif
  }

  //for (PetscInt i = 0; i < num_states; i++) {
  //  std::cout << "SP " << my_proc_id << " num_nonzeros_in_diagonal_block[" << i << "] = " << num_nonzeros_in_diagonal_block[i] << std::endl;
  //  std::cout << "SP " << my_proc_id << " num_nonzeros_in_offdiagonal_block[" << i << "] = " << num_nonzeros_in_offdiagonal_block[i] << std::endl;
  //}

  check( MatMPIAIJSetPreallocation(g_solid_conduction_coeff_mat, 0, num_nonzeros_in_diagonal_block.data(), 0, num_nonzeros_in_offdiagonal_block.data()) );
}

VOID preallocate_matrix_internal(UBLK ublk, 
                                 PetscInt first_local_row_global_index, 
                                 PetscInt last_local_row_global_index, 
                                 std::vector<PetscInt> &num_nonzeros_in_diagonal_block, 
                                 std::vector<PetscInt> &num_nonzeros_in_offdiagonal_block) {

  auto *dynamics_data = ublk->dynamics_data();
  CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
  VOXEL_MASK_8 conduction_solid_voxel_mask = conduction_dyn_data->voxel_mask();

  std::vector<std::unordered_set<PetscInt>> diagonal_neighbors(conduction_solid_voxel_mask.count());
  std::vector<std::unordered_set<PetscInt>> offdiagonal_neighbors(conduction_solid_voxel_mask.count());

  int nth_voxel_in_mask = 0;  // This is different from voxel id
  DO_VOXELS_IN_MASK(voxel, conduction_solid_voxel_mask){
    //int root_state_index = ublk->conduction_implicit_solver_data(voxel)->implicit_solid_state_index;
    std::vector<sINT32> nbr_state_index_vector = ublk->conduction_implicit_solver_data()->nbr_state_index_vector[voxel];
    if (nbr_state_index_vector.size() == 0) {
      msg_warn("Preallocating U %d V %d but nothing in its nbr_state_index_vector. Skip because this voxel does not have any neighbor.", ublk->id(), voxel);
    } else {
      for (sINT32 nbr_state_index : nbr_state_index_vector){
        //msg_print("Trying to insert nbr_state_index %d first %d last %d ", nbr_state_index, first_local_row_global_index, last_local_row_global_index);
        if (nbr_state_index >= first_local_row_global_index && nbr_state_index < last_local_row_global_index) {
          diagonal_neighbors[nth_voxel_in_mask].insert(nbr_state_index); // diagonal block
        } else {
          offdiagonal_neighbors[nth_voxel_in_mask].insert(nbr_state_index); // offdiagonal block
        }
      }
    }
    nth_voxel_in_mask ++;
  }

  // This assumes that the matrix storage is ublk_id + non-empty voxels
  nth_voxel_in_mask = 0;
  DO_VOXELS_IN_MASK(voxel, conduction_solid_voxel_mask){
    // Get the index for this dof on this proc. We need to subtract out the offset because the nonzeros vectors only
    // have size equal to the number of dofs on the processor so we don't want to offset the index of those vectors.
    cassert(ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] != -1); //all voxels in mask should have been assigned a valid index
    int state_index = ublk->conduction_implicit_solver_data()->implicit_solid_state_index[voxel] - sim.implicit_solid_solver_state_index_offset[my_proc_id];
    num_nonzeros_in_diagonal_block[state_index] = diagonal_neighbors[nth_voxel_in_mask].size() + 1;
    num_nonzeros_in_offdiagonal_block[state_index] = offdiagonal_neighbors[nth_voxel_in_mask].size();
    nth_voxel_in_mask ++;
  }
}

template <BOOLEAN FIRST_SOLVE>
VOID assemble_linear_system_for_solid_solver() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        assemble_linear_system_internal<FIRST_SOLVE>(ublk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        assemble_linear_system_internal<FIRST_SOLVE>(nearblk);
      }
    }
#ifdef IMPLICIT_SOLVER_PROGRESS_VR
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        assemble_linear_system_internal<FIRST_SOLVE>(fine_ublk);
      }
    }
#endif
  }
}
template VOID assemble_linear_system_for_solid_solver<FALSE>();
template VOID assemble_linear_system_for_solid_solver<TRUE>();

template <BOOLEAN FIRST_SOLVE>
VOID assemble_linear_system_internal(UBLK ublk) {

  PetscReal identity = 1.0; // Kind of silly, but ok for now.

  UBLK_CONDUCTION_DATA ublk_conduction_data = ublk->conduction_data();
  UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA ublk_implicit_solver_data = ublk->conduction_implicit_solver_data();

  //sdFLOAT dt = (sdFLOAT)g_timescale.m_n_conduction_pde_base_steps / (sdFLOAT)g_timescale.m_n_lb_base_steps; // Hardcode the timestep


  // Assemble diagonal matrix
  auto *dynamics_data = ublk->dynamics_data();
  CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
  VOXEL_MASK_8 voxel_mask = conduction_dyn_data->voxel_mask();
  DO_VOXELS_IN_MASK(voxel, voxel_mask){
    PetscInt global_row_idx = ublk_implicit_solver_data->implicit_solid_state_index[voxel];
    PetscReal diagonal = ublk_implicit_solver_data->implicit_matrix_A_diagonal[voxel];
    //DEBUG_IMPLICIT
    //if (ublk->id()==20 || ublk->id()==18){
    ////if (ublk->id()==80 || ublk->id()==74
     //msg_print("T %d U %d V %d", g_timescale.m_time, ublk->id(), voxel);
     //msg_print("Aii %d: %g", global_row_idx, diagonal);
    //}
    
    //asINT32 conduction_dt = (asINT32)g_timescale.conduction_delta_t();
    //SCALE coarsest_active_scale = g_timescale.coarsest_active_scale(); 
     
     //msg_print("T %d FIRST? %d U %d V %d u scale %d, active %d", g_timescale.m_time, FIRST_SOLVE, ublk->id(), voxel, ublk->scale(), coarsest_active_scale);
    
    check( MatSetValues(g_solid_conduction_coeff_mat, 
                        1, 
                        &global_row_idx, 
                        1, 
                        &global_row_idx, 
                        &diagonal,
                        INSERT_VALUES) );
    
    //reset Aii preparing for next timestep's accumulation 
    ublk_implicit_solver_data->implicit_matrix_A_diagonal[voxel] = 0.0; 
      
    //insert off diagonal terms of this row
    std::vector<PetscReal> offdiagonal_term_vector = ublk_implicit_solver_data->nbr_offdiagonal_term_vector[voxel];
    std::vector<PetscInt> nbr_state_index_vector = ublk->conduction_implicit_solver_data()->nbr_state_index_vector[voxel];
    if (offdiagonal_term_vector.size() != nbr_state_index_vector.size()) {
      //if (offdiagonal_term_vector.size() != 0)
        msg_error("At T %ld UBLK %d VOXEL %d nbr_offdiagonal_term_vector size (%zu) not the same as nbr_state_index_vector (%zu)", g_timescale.m_time, ublk->id(), voxel, offdiagonal_term_vector.size(), nbr_state_index_vector.size());
    }

    ublk_implicit_solver_data->nbr_offdiagonal_term_vector[voxel].clear();
    
    PetscInt ncols = nbr_state_index_vector.size();
    if (ncols > 0) {
      check( MatSetValues(g_solid_conduction_coeff_mat,
                          1, 
                          &global_row_idx, 
                          ncols, 
                          nbr_state_index_vector.data(), 
                          offdiagonal_term_vector.data(),
                          INSERT_VALUES) );
    }

    const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(ublk->scale());
   
    // Assemble RHS
    if (FIRST_SOLVE) {
      PetscReal temp_init = ublk_conduction_data->solid_temp[prior_index][voxel];
      check( VecSetValues(g_solid_conduction_solution_vec, 1, &global_row_idx, &temp_init, INSERT_VALUES) );
    }
    
    PetscReal rhs_bc = ublk_implicit_solver_data->rhs_bc[voxel]; 
    
    //if (FIRST_SOLVE || ublk->scale() >= coarsest_active_scale){ 
      check( VecSetValues(g_solid_conduction_rhs_bc_vec, 1, &global_row_idx, &rhs_bc, INSERT_VALUES) );
      ublk_implicit_solver_data->rhs_bc[voxel] = 0.0; //reset rhs_bc
    //}
    
    sdFLOAT voxel_pfluid = 1.0; 
    if (ublk->is_near_surface())
      voxel_pfluid = ublk->surf_geom_data()->pfluids[voxel]; 
#ifndef IMPLICIT_SOLVER_PROGRESS_VR 
    asINT32 lambda = sim_num_scales() - ublk->scale() - 1;
    voxel_pfluid *= 1 << (lambda * sim.num_dims);  //Scaling up Vi 
#endif
    PetscReal source = ublk->conduction_data()->volumetric_source[voxel] * voxel_pfluid * g_timescale.conduction_delta_t(); 
    //DEBUG_IMPLICIT
    //  msg_print("assemble U %d V %d source %g", ublk->id(), voxel, ublk->conduction_data()->volumetric_source[voxel]);
    
    check( VecSetValues(g_solid_conduction_source_vec, 1, &global_row_idx, &source, INSERT_VALUES) );
    
    PetscReal rho_cp_Vi = ublk_implicit_solver_data->implicit_rho_cp_vec[voxel];
    check( VecSetValues(g_solid_conduction_rho_cp_vec, 1, &global_row_idx, &rho_cp_Vi, INSERT_VALUES) );
    
    //msg_print("T %d U %d V %d", g_timescale.m_time, ublk->id(), voxel);
    //msg_print("rho_cp_Vi %g bc %g", rho_cp_Vi, rhs_bc);
    
  }
}
template VOID assemble_linear_system_internal<FALSE>(UBLK ublk);
template VOID assemble_linear_system_internal<TRUE>(UBLK ublk);

template <BOOLEAN FIRST_SOLVE>
VOID finalize_assembly_for_solid_solver() {
  if (FIRST_SOLVE) {
    // Assemble initial condition
    check( VecAssemblyBegin(g_solid_conduction_solution_vec) );
    check( VecAssemblyEnd(g_solid_conduction_solution_vec) );

    // Assemble BC vector
    check( VecAssemblyBegin(g_solid_conduction_rhs_bc_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rhs_bc_vec) );
    
    // Assemble source vector
    check( VecAssemblyBegin(g_solid_conduction_source_vec) );
    check( VecAssemblyEnd(g_solid_conduction_source_vec) );
    
    // Assemble rho_cp_Vi vector
    check( VecAssemblyBegin(g_solid_conduction_rho_cp_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rho_cp_vec) );

    // Multiply the solution vec by rho_cp_Vi
    check( VecPointwiseMult(g_solid_conduction_rho_cp_solution_vec, g_solid_conduction_rho_cp_vec, g_solid_conduction_solution_vec));// Computes element-wise w = v T
    check( VecAssemblyBegin(g_solid_conduction_rho_cp_solution_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rho_cp_solution_vec) );
    
    check( VecWAXPY(g_solid_conduction_rhs_vec, 1.0 , g_solid_conduction_rho_cp_solution_vec, g_solid_conduction_rhs_bc_vec) ); // Computes w = alpha x + y
    // Add source
    check( VecAXPY(g_solid_conduction_rhs_vec, 1.0, g_solid_conduction_source_vec) ); // Computes w += alpha z

    // Assemble RHS
    check( VecAssemblyBegin(g_solid_conduction_rhs_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rhs_vec) );

    //check( VecAssemblyBegin(g_shell_conduction_source_vec) );
    //check( VecAssemblyEnd(g_shell_conduction_source_vec) );

    // Assemble diagonal matrix (as a vector)
    //check( VecAssemblyBegin(g_shell_conduction_rho_cp_vec) );
    //check( VecAssemblyEnd(g_shell_conduction_rho_cp_vec) );

    // Create LHS matrix = rho_cp * I + flux_matrix
    //check( MatDiagonalSet(g_shell_conduction_coeff_mat, g_shell_conduction_rho_cp_vec, ADD_VALUES) ); // Computes Y = Y + D, where D is a diagonal matrix that is represented as a vector
    check( MatAssemblyBegin(g_solid_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatAssemblyEnd(g_solid_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );

    bool conservation_check = false;
    if (g_solid_implicit_solver_verbosity > 3) {
      conservation_check = true;
    }
    if (conservation_check) {
      // Check if matrix is conservative
      PetscInt num_rows, num_cols;
      check( MatGetSize(g_solid_conduction_coeff_mat, &num_rows, &num_cols) );
      PetscReal colsums[num_cols];
      check( MatGetColumnSumsRealPart(g_solid_conduction_coeff_mat, colsums) );

      check( PetscPrintf(eMPI_csp_comm, "Column sums (should be 0)\n") );
      check( PetscPrintf(eMPI_csp_comm, "=========================\n") );
      for (int i = 0; i < num_cols; i++) {
        PetscReal flux_col_sum;
        flux_col_sum = colsums[i] - 1.0;
        //check( PetscPrintf(eMPI_csp_comm, "  col %d sum: %14.12e\n", i, flux_col_sum) );
        if (std::abs(flux_col_sum) >= 10*SDFLOAT_EPSILON) {
          check( PetscPrintf(eMPI_csp_comm, "    Sum nonzero for column %d is %14.12e\n", i, flux_col_sum) );
        }
      }
    }
    
    //**Check diagonal zero entries
    if (g_solid_implicit_solver_verbosity > 2) {
      Vec x;
      PetscInt local_size;
      check( MatGetLocalSize(g_solid_conduction_coeff_mat, &local_size, NULL));
      check( VecCreate(eMPI_csp_comm, &x));
      check( VecSetSizes(x, local_size, PETSC_DECIDE));
      check( VecSetFromOptions(x));

      check( MatGetDiagonal(g_solid_conduction_coeff_mat, x)); 
      PetscReal min_diag;
      check( VecMin(x, NULL, &min_diag));
      check( PetscPrintf(eMPI_csp_comm, "Min diagonal = %g\n", min_diag));

      check( VecDestroy(&x));

      PetscInt n_local;
      check( MatGetLocalSize(g_solid_conduction_coeff_mat, &n_local, NULL));

      for (PetscInt i = 0; i < n_local; i++) {
          PetscInt ncols;
          const PetscInt *cols;
          const PetscScalar *vals;
          check( MatGetRow(g_solid_conduction_coeff_mat, i, &ncols, &cols, &vals));
          if (ncols == 0) {
              check( PetscPrintf(eMPI_csp_comm, "Zero row at local index %d\n", i));
          }
          check( MatRestoreRow(g_solid_conduction_coeff_mat, i, &ncols, &cols, &vals));
      }
    } //**
  } else{
    // re-assemble BCs
    check( VecAssemblyBegin(g_solid_conduction_rhs_bc_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rhs_bc_vec) );
    
    // re-assemble source vector
    check( VecAssemblyBegin(g_solid_conduction_source_vec) );
    check( VecAssemblyEnd(g_solid_conduction_source_vec) );

    // Assemble RHS
    // Copy the solution to RHS vector b/c it is now the previous solution
    // Multiply the solution vec by rho_cp_Vi
    check( VecPointwiseMult(g_solid_conduction_rho_cp_solution_vec, g_solid_conduction_rho_cp_vec, g_solid_conduction_solution_vec));// Computes element-wise w = v T
    check( VecWAXPY(g_solid_conduction_rhs_vec, 1.0, g_solid_conduction_rho_cp_solution_vec, g_solid_conduction_rhs_bc_vec) ); // Computes w = alpha x + y
    // Add source
    check( VecAXPY(g_solid_conduction_rhs_vec, 1.0, g_solid_conduction_source_vec) ); // Computes w += alpha z

    check( VecAssemblyBegin(g_solid_conduction_rhs_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rhs_vec) );

    check( MatAssemblyBegin(g_solid_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatAssemblyEnd(g_solid_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    //check( MatShift(g_shell_conduction_coeff_mat, 1.0) ); // Add one to the diagonal of the flux matrix (Y = Y + aI). Has to be done after final assembly.
  
  }
}

template VOID finalize_assembly_for_solid_solver<FALSE>();
template VOID finalize_assembly_for_solid_solver<TRUE>();

VOID create_solver_objects_for_solid_solver() {
  check( KSPCreate(eMPI_csp_comm, &g_solid_ksp) ); // Just creates a KSP context and sets a bunch of default solver parameters.
 
  // Set operators (e.g. preconditioners)
  check( KSPSetOperators(g_solid_ksp, g_solid_conduction_coeff_mat, g_solid_conduction_coeff_mat) );

  // Set some optional arguments
  PC pc;
  check( KSPGetPC(g_solid_ksp, &pc) ); // grab the preconditioner context to set preconditioner options on it if we want to
  check( PCSetType(pc, PCGAMG) ); // AMG preconditioner
  check( PCGAMGSetNSmooths(pc, 0) );
  
  //PetscReal threshold[1] = {0.02};
  //check( PCGAMGSetThreshold(pc, threshold, 1));
  //check( PCGAMGSetNSmooths(pc, 1) );
  //check( PCGAMGSetAggressiveLevels(pc, 1));
  //check( PCGAMGSetCoarseEqLim(pc, 1000));
  //check( KSPSetDiagonalScale(g_solid_ksp, PETSC_TRUE) );
  //check( KSPSetDiagonalScaleFix(g_solid_ksp, PETSC_TRUE) );
  
  check( KSPSetType(g_solid_ksp, KSPGMRES) ); // Set the solver type
  PetscReal rtol = g_solid_implicit_solver_relative_tolerance; //1.0e-09;
  check( KSPSetTolerances(g_solid_ksp, rtol, PETSC_DEFAULT, PETSC_DEFAULT, PETSC_DEFAULT) );
  check( KSPGMRESSetRestart(g_solid_ksp, 60) );
  check( KSPSetFromOptions(g_solid_ksp) ); // Override the previous options with options specified in an options file

  check( KSPSetInitialGuessNonzero(g_solid_ksp, PETSC_TRUE) );

  check( KSPMonitorSet(g_solid_ksp, MySolidKSPMonitor, NULL, NULL) );

  if (g_solid_implicit_solver_verbosity > 0) {
    check( KSPView(g_solid_ksp, PETSC_VIEWER_STDOUT_WORLD) );
  }
}

// Happening in the comm thread now
VOID implicit_solid_solver_evolve() {
  if (g_implicit_solid_solver_active && g_implicit_solid_solver_setup_complete) {
    pthread_mutex_lock(&g_implicit_solid_solver_mutex);
    
    //DEBUG_IMPLICIT
    //msg_print("TS %d solver evolve", g_timescale.m_time);
    
    assemble_surfel_contributions(); 
    
    assemble_voxel_coefficients<FALSE>();  
    //update_rhs_surfel_and_volumetric_source_contributions();
    
    assemble_linear_system_for_solid_solver<FALSE>(); 
    
    finalize_assembly_for_solid_solver<FALSE>();
    //msg_print("TS %d solve linear system", g_timescale.m_time);
    solid_solver_solve_linear_system();
    implicit_solid_solver_update_voxel_temperature();

    g_implicit_solid_solver_active = false;
    pthread_cond_signal(&g_implicit_solid_solver_cond);
    pthread_mutex_unlock(&g_implicit_solid_solver_mutex);
  }
}

VOID solid_solver_solve_linear_system() {
  // For diagnostics get the system size
  PetscInt num_rows, num_cols;
  check( MatGetSize(g_solid_conduction_coeff_mat, &num_rows, &num_cols) );

  // Solve matrix system
  PetscInt iterations;
  KSPConvergedReason converged;
  PetscReal rnorm;
  if (g_solid_implicit_solver_verbosity > 1) {
    int residual_monitor_freq = 100;
    if (g_solid_implicit_solver_verbosity > 2) {
      residual_monitor_freq = 1;
    }
    if (g_timescale.m_time % residual_monitor_freq == 0) {
      check( PetscPrintf(eMPI_csp_comm, "TS %ld\n", g_timescale.m_time) );
    }
  }
  

  WITH_TIMER(SP_IMPLICIT_SOLVER_TIMER) {
    check( KSPSetInitialGuessNonzero(g_solid_ksp, PETSC_TRUE) );
    //NOTE: Need to reassemble after adding in new values and before the solve. Just shoved it in here for now, but will
    //probably need to move to more sane place at a later date. This will be important when we update the matrix
    //assembly for nonlinear problems. Not sure why everything was fine for single proc case, but maybe it's just
    //because we didn't need to communicate anything across procs during the assembly process.
    check( VecAssemblyBegin(g_solid_conduction_rhs_vec) );
    check( VecAssemblyEnd(g_solid_conduction_rhs_vec) );
    check( KSPSolve(g_solid_ksp, g_solid_conduction_rhs_vec, g_solid_conduction_solution_vec) );
    check( KSPGetConvergedReason(g_solid_ksp, &converged) );
    check( KSPGetIterationNumber(g_solid_ksp, &iterations) );
    check( KSPGetResidualNorm(g_solid_ksp, &rnorm) );
  }
  timer_accum_counters(SP_IMPLICIT_SOLVER_TIMER, 0, iterations);
  timer_accum_counters(SP_IMPLICIT_SOLVER_TIMER, 1, num_rows); // square matrix, so nrows = ncols

  if (converged < 0) {
    check( PetscPrintf(eMPI_csp_comm, "KSPConvergedReason: %s\n", KSPConvergedReasons[converged]) );
    msg_error("Implicit solid solver did not converge!");
  }
}

// Happens in the compute thread
VOID start_and_wait_for_implicit_solid_solver() {
  pthread_mutex_lock(&g_implicit_solid_solver_mutex);
  g_implicit_solid_solver_active = true; // being protected by the lock
  while (g_implicit_solid_solver_active)
  {
    pthread_cond_wait(&g_implicit_solid_solver_cond, &g_implicit_solid_solver_mutex); // guarantees that the mutex will be unlocked after you enter and then locks it again after it returns
  }
  pthread_mutex_unlock(&g_implicit_solid_solver_mutex);
}

VOID start_and_wait_for_implicit_solid_solver_setup() {
  pthread_mutex_lock(&g_implicit_solid_solver_setup_mutex);
  g_implicit_solid_solver_setup_active = true; // being protected by the lock
  while (g_implicit_solid_solver_setup_active)
  {
    pthread_cond_wait(&g_implicit_solid_solver_setup_cond, &g_implicit_solid_solver_setup_mutex); // guarantees that the mutex will be unlocked after you enter and then locks it again after it returns
  }
  pthread_mutex_unlock(&g_implicit_solid_solver_setup_mutex);
}

VOID implicit_solid_solver_update_voxel_temperature() {

  bool write_solution_to_txt = false;
  //if (g_timescale.m_time > 500 || g_timescale.m_time < 5) write_solution_to_txt = true;
  if (write_solution_to_txt) {
    PetscViewer sol_viewer;
    std::string solution_file = "solution" + std::to_string(g_timescale.m_time) + ".txt";
    check( PetscViewerASCIIOpen(eMPI_csp_comm, solution_file.c_str(), &sol_viewer) );
    check( PetscViewerPushFormat(sol_viewer, PETSC_VIEWER_ASCII_DENSE) );
    check( VecView(g_solid_conduction_solution_vec, sol_viewer) );
    check( PetscViewerPopFormat(sol_viewer) );
    check( PetscViewerDestroy(&sol_viewer) );
  }
 
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_FARBLKS_OF_SCALE(ublk, scale) {
      if (ublk->is_conduction_solid()) {
        update_voxel_temperature_internal(ublk);
      }
    }
    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
      if (nearblk->is_conduction_solid()) {
        update_voxel_temperature_internal(nearblk);
      }
    }
#ifdef IMPLICIT_SOLVER_PROGRESS_VR
    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
      if (fine_ublk->is_conduction_solid()) {
        update_voxel_temperature_internal(fine_ublk);
      }
    }
#endif
  }

}

VOID update_voxel_temperature_internal(UBLK ublk) {
  auto *dynamics_data = ublk->dynamics_data();
  CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
  VOXEL_MASK_8 voxel_mask = conduction_dyn_data->voxel_mask();
  
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(ublk->scale());
  const asINT32 current_index = prior_index ^ 1;
  UBLK_CONDUCTION_DATA ublk_conduction_data = NULL;
  UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA ublk_implicit_solver_data = NULL;
  ublk_conduction_data = ublk->conduction_data();
  ublk_implicit_solver_data = ublk->conduction_implicit_solver_data();
  DO_VOXELS_IN_MASK(voxel, voxel_mask){
    PetscInt global_idx = ublk_implicit_solver_data->implicit_solid_state_index[voxel];
    PetscReal temp_new; // temperature at timestep n+1. 
    check( VecGetValues(g_solid_conduction_solution_vec, 1, &global_idx, &temp_new) );
    ublk_conduction_data->solid_temp[current_index][voxel] = temp_new;
    //DEBUG_IMPLICIT
    //if (temp_new < 0)
    //  msg_warn("Temperature becomes negative at T %d UBLK(%d) VOXEL(%d) temperature %g", g_timescale.m_time, ublk->id(), voxel, temp_new);
  }
}

//VOID update_rhs_surfel_and_volumetric_source_contributions() { 
//  DO_SCALES_FINE_TO_COARSE(scale) {
//    DO_FARBLKS_OF_SCALE(ublk, scale) {
//      if (ublk->is_conduction_solid()) {
//        update_rhs_surfel_and_volumetric_source_contributions_internal(ublk);
//      }
//    }
//    DO_NEARBLKS_OF_SCALE(nearblk, scale) {
//      if (nearblk->is_conduction_solid()) {
//        update_rhs_surfel_and_volumetric_source_contributions_internal(nearblk);
//      }
//    }
//#ifdef IMPLICIT_SOLVER_PROGRESS_VR
//    DO_VRBLKS_OF_SCALE(fine_ublk, scale) {
//      if (fine_ublk->is_conduction_solid()) {
//        update_rhs_surfel_and_volumetric_source_contributions_internal(fine_ublk);
//      }
//    }
//#endif
//  }
//}
//
//VOID update_rhs_surfel_and_volumetric_source_contributions_internal(UBLK ublk) {
//  
//  UBLK_CONDUCTION_DATA ublk_conduction_data = ublk->conduction_data();
//  UBLK_CONDUCTION_IMPLICIT_SOLVER_DATA ublk_implicit_solver_data = ublk->conduction_implicit_solver_data();
//
//  auto *dynamics_data = ublk->dynamics_data();
//  CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
//  VOXEL_MASK_8 voxel_mask = conduction_dyn_data->voxel_mask();
//  DO_VOXELS_IN_MASK(voxel, voxel_mask){
//    PetscInt global_row_idx = ublk_implicit_solver_data->implicit_solid_state_index[voxel];
//    const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(ublk->scale());
//   
//    // Assemble RHS
//    
//    PetscReal rhs_bc = ublk_implicit_solver_data->rhs_bc[voxel];
//    
//    check( VecSetValues(g_solid_conduction_rhs_bc_vec, 1, &global_row_idx, &rhs_bc, INSERT_VALUES) );
//    
//    //DEBUG_IMPLICIT
//    //if (ublk->id() == 315)
//    //msg_print("T %d Update RHS U %d V %d rhs_bc %g", g_timescale.m_time, ublk->id(), voxel, rhs_bc);
//    
//    //update source term
//    sdFLOAT voxel_pfluid = 1.0; 
//    if (ublk->is_near_surface())
//      voxel_pfluid = ublk->surf_geom_data()->pfluids[voxel]; 
//#ifndef IMPLICIT_SOLVER_PROGRESS_VR 
//    asINT32 lambda = sim_num_scales() - ublk->scale() - 1;
//    voxel_pfluid *= 1 << (lambda * sim.num_dims);  //Scaling up Vi 
//#endif
//    PetscReal source = ublk->conduction_data()->volumetric_source[voxel] * voxel_pfluid * g_timescale.conduction_delta_t(); 
//    
//    check( VecSetValues(g_solid_conduction_source_vec, 1, &global_row_idx, &source, INSERT_VALUES) );
//    
//    ublk_implicit_solver_data->rhs_bc[voxel] = 0.0; //reset rhs_bc
//  }
//}


VOID write_solid_linear_system_to_txt() {
  PetscViewer T_init_viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "T_init.txt", &T_init_viewer) );
  check( PetscViewerPushFormat(T_init_viewer, PETSC_VIEWER_ASCII_DENSE) );
  check( VecView(g_solid_conduction_solution_vec, T_init_viewer) );
  check( PetscViewerPopFormat(T_init_viewer) );
  check( PetscViewerDestroy(&T_init_viewer) );

  PetscViewer rhs_viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "rhs.txt", &rhs_viewer) );
  check( PetscViewerPushFormat(rhs_viewer, PETSC_VIEWER_ASCII_DENSE) );
  check( VecView(g_solid_conduction_rhs_vec, rhs_viewer) );
  check( PetscViewerPopFormat(rhs_viewer) );
  check( PetscViewerDestroy(&rhs_viewer) );

  PetscViewer source_viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "source.txt", &source_viewer) );
  check( PetscViewerPushFormat(source_viewer, PETSC_VIEWER_ASCII_DENSE) );
  check( VecView(g_solid_conduction_source_vec, source_viewer) );
  check( PetscViewerPopFormat(source_viewer) );
  check( PetscViewerDestroy(&source_viewer) );

  PetscViewer rhs_bc_viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "rhs_bc.txt", &rhs_bc_viewer) );
  check( PetscViewerPushFormat(rhs_bc_viewer, PETSC_VIEWER_ASCII_DENSE) );
  check( VecView(g_solid_conduction_rhs_bc_vec, rhs_bc_viewer) );
  check( PetscViewerPopFormat(rhs_bc_viewer) );
  check( PetscViewerDestroy(&rhs_bc_viewer) );
  
  PetscViewer viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "Amat.txt", &viewer) );
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "flux_mat.txt", &viewer) );
  check( PetscViewerPushFormat(viewer, PETSC_VIEWER_ASCII_DENSE) );
  check (MatView(g_solid_conduction_coeff_mat, viewer) );
  check( PetscViewerPopFormat(viewer) );
  check( PetscViewerDestroy(&viewer) );
}

VOID implicit_solid_solver_destroy_solver_objects() {
  check( KSPDestroy(&g_solid_ksp) );
  check( VecDestroy(&g_solid_conduction_rhs_vec) );
  check( VecDestroy(&g_solid_conduction_rhs_bc_vec) );
  check( VecDestroy(&g_solid_conduction_source_vec) );
  check( VecDestroy(&g_solid_conduction_solution_vec) );
  check( VecDestroy(&g_solid_conduction_rho_cp_vec) );
  check( MatDestroy(&g_solid_conduction_coeff_mat) );
  //check( PetscFinalize() );
  //if (g_solid_implicit_solver_verbosity > 1) {
  //  PetscOptions options;
  //  const std::filesystem::path& options_file = "implicit_solid_solver_options.txt";
  //  check( PetscOptionsCreate(&options) );
  //  //check( PetscOptionsInsert(options, nullptr, nullptr, options_file.c_str()) );
  //  check( PetscOptionsInsert(options, nullptr, nullptr, nullptr) );
  //  check( PetscOptionsView(options, PETSC_VIEWER_STDOUT_WORLD) );
  //  check( PetscOptionsDestroy(&options) );
  //}
}

PetscErrorCode MySolidKSPMonitor(KSP ksp, PetscInt it, PetscReal rnorm, void *ctx) {

  PetscFunctionBeginUser;

  int iteration_print_freq = 1;
  if (it > 20) {
    iteration_print_freq = 10;
  }

  if (g_solid_implicit_solver_verbosity > 1) {
    int residual_monitor_freq = 100;
    if (g_solid_implicit_solver_verbosity > 2) {
     residual_monitor_freq = 1;
    }
    if (g_timescale.m_time % residual_monitor_freq == 0) {
      if (it % iteration_print_freq == 0) {
        check( PetscPrintf(eMPI_csp_comm, "    iteration %" PetscInt_FMT " KSP Residual norm %14.12e\n", it, (double)rnorm) );
      }
    }
  }

  PetscFunctionReturn(0);
}
#endif
#endif
