/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 ***  PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2018, 1993-2018 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

#ifndef _SIMENG_CONDUCTION_SOLVER_STENCILS_H
#define _SIMENG_CONDUCTION_SOLVER_STENCILS_H

#include "common_sp.h"
#include "surfel_stencils.h"

#include BG_H
#include BREP_H
#include BAGS_H
#include TEARRAY_H
#include FGEOM_H

// Forward declaration of cSHELL_CONDUCTION_STENCIL_NEIGHBOR and cCONDUCTION_EDGE_BASE Objects
inline namespace SIMULATOR_NAMESPACE {
 class cSHELL_CONDUCTION_STENCIL_NEIGHBOR;
 class cCONDUCTION_EDGE_BASE;
}
typedef SIMULATOR_NAMESPACE::cSHELL_CONDUCTION_STENCIL_NEIGHBOR *SHELL_CONDUCTION_STENCIL_NEIGHBOR;
typedef SIMULATOR_NAMESPACE::cCONDUCTION_EDGE_BASE *CONDUCTION_EDGE_BASE;

#include "conduction_shell_mesh.h"

cSHELL_MESH* get_shell_mesh();

cSHELL_MESH::sBG_POINT2 rotate_to_surfel_csys(const cSHELL_MESH::sBG_VECTOR3& vec_global, cSHELL_MESH::FACET facet);

/**
 @brief Class to calculate unfolded distance between centroids

 @details This class is used to perform the unfolding of shell surfels
          onto a 2D plane to calculate the geodesic path along the surface
          between a root facet/edge and a neighbor facet/edge (depending on the constructor
          used).

          A specification detailing this algorithm will be created and stored in the spec directory.
 */
class cSURFEL_UNFOLDER {
  // Flags to indicate the previous intersection type, failures and completion of unfolding process
  enum CUT_LOCATION_FLAG {
    CUT_FAILURE=-1,
    CUT_FINISHED,
    CUT_ON_TAIL,
    CUT_ON_EDGE,
    CUT_ON_HEAD
  };

public:
  typedef cSHELL_MESH::sBG_POINT3 POINT3;
  typedef cSHELL_MESH::sBG_POINT2 POINT2;
  typedef cSHELL_MESH::sBG_VECTOR3 VECTOR3;
  typedef cSHELL_MESH::sBG_VECTOR2 VECTOR2;
  typedef cSHELL_MESH::sBG_PLANE3 PLANE3;
  typedef cSHELL_MESH::sBG_SEGMENT3 SEGMENT3;
  typedef cSHELL_MESH::NT NT;
  typedef cSHELL_MESH::HALF_EDGE HALF_EDGE;
  typedef cSHELL_MESH::FACET FACET;
  typedef cSHELL_MESH::VERTEX VERTEX;
  typedef cSHELL_MESH::sBG_TRANSFORM3 TRANSFORM;


// Constructors for unfolding used by cell centered LSQ implementation
  cSURFEL_UNFOLDER(FACET root_facet, FACET nbr_facet, POINT3 dir_point,
      BOOLEAN error_on_fail=FALSE);

  cSURFEL_UNFOLDER(FACET root_facet, FACET nbr_facet, VECTOR3 planeTan,
      BOOLEAN error_on_fail=FALSE);
  
  cSURFEL_UNFOLDER(FACET root_facet, HALF_EDGE nbr_edge, POINT3 dir_point,
      BOOLEAN use_nbr_edge_midpoint, BOOLEAN error_on_fail=FALSE);


  // Constructors for unfolding used by edge centered LSQ implementation
  cSURFEL_UNFOLDER(HALF_EDGE root_edge, FACET nbr_facet, POINT3 dir_point,
      BOOLEAN use_root_edge_centroid, BOOLEAN check_opp_edge, BOOLEAN error_on_fail=FALSE);
  cSURFEL_UNFOLDER(HALF_EDGE root_edge, HALF_EDGE nbr_edge, POINT3 dir_point,
      BOOLEAN use_root_edge_midpoint, BOOLEAN use_nbr_edge_midpoint, BOOLEAN check_opp_edge,
      BOOLEAN error_on_fail=FALSE);

  dFLOAT get_unfolded_centroid_vec(dFLOAT unfolded_vec[2]);

  BOOLEAN unfolding_failed() { return !m_cut_successful; }

  static cSURFEL_UNFOLDER::POINT3 get_edge_point_location(HALF_EDGE half_edge, BOOLEAN use_midpoint = TRUE);

  dFLOAT get_unfolded_vec_length();

  dFLOAT get_relative_rotation();

  VECTOR3 get_first_cut_segment() {
    return (m_cut_points[1] - m_cut_points[0]);
  }

  std::string getCutPointsString() {
    auto cutPtsStr = fmt::format(" = np.array([[{:1.10e}, {:1.10e}, {:1.10e}],", EXPAND_FLOAT_VEC3(m_cut_points[0]));
    for (int i = 1; i < this->m_cut_points.size(); i++) {
      cutPtsStr += fmt::format("\n              [{:1.10e}, {:1.10e}, {:1.10e}],", EXPAND_FLOAT_VEC3(m_cut_points[i]));
    }
    cutPtsStr += "])";
    return cutPtsStr;
  }

private:
  VOID find_cut_points(HALF_EDGE& cut_edge, CUT_LOCATION_FLAG cut_loc);

  CUT_LOCATION_FLAG make_first_cut(HALF_EDGE& cut_edge, POINT3& dir_point);

  CUT_LOCATION_FLAG make_first_cut_from_edge(HALF_EDGE& cut_edge, POINT3& dir_point, BOOLEAN check_opposite);

  HALF_EDGE get_opposite_edge(HALF_EDGE half_edge);

  CUT_LOCATION_FLAG make_next_cut_from_edge(HALF_EDGE& cut_edge);

  FACET get_next_facet_from_vertex(HALF_EDGE cut_edge, VERTEX cut_vertex);

  CUT_LOCATION_FLAG make_next_cut_from_vertex(HALF_EDGE& cut_edge, CUT_LOCATION_FLAG cut_loc);

// Functions specific to 2d simulation
  VOID set_2d_scale_and_direction(VECTOR2& first_segment);

  CUT_LOCATION_FLAG make_next_2d_cut(HALF_EDGE& cut_edge);

  HALF_EDGE get_initial_cut_edge_2d(POINT3& dir_point) {
    HALF_EDGE next_cut_edge = m_root_facet->get_2d_half_edge();
    POINT3 cut_edge_pt = get_edge_point_location(next_cut_edge);
    VECTOR3 dist_to_edge = dir_point - cut_edge_pt;
    if (dist_to_edge.Length() > SFLOAT_EPSILON) {
       next_cut_edge = next_cut_edge->get_opp_in_2d_facet();
    }
    return next_cut_edge;
  }

  asINT32 m_root_face_id = -1;
  asINT32 m_nbr_face_id = -1;
  FACET m_root_facet = NULL;
  FACET m_nbr_facet = NULL;
  HALF_EDGE m_nbr_edge = NULL;
  HALF_EDGE m_root_edge = NULL;
  POINT3 m_final_cut_point;
  PLANE3 m_cut_plane;
  VECTOR3 m_vec_to_nbr;
  dFLOAT m_unfolded_centroid[2] = {-1e6, -1e6};
  dFLOAT m_scale_direction;
  std::vector<POINT3> m_cut_points;
  BOOLEAN m_error_on_fail;
  BOOLEAN m_cut_successful = FALSE;
};

/**
 @brief Class to used to interatively call cSURFEL_UNFOLDER in order to determine geodesic between cell centroids

 @details The #define NO_UNFOLDING_ITERATIONS statement at the top of conduction_solver_stencils.cc causes class
          to return first iteration of unfolding. This is currently the default because it is inclear which approach
          should be utilized
*/
class cITERATIVE_UNFOLDER {
public:
  typedef cSHELL_MESH::HALF_EDGE HALF_EDGE;
  typedef cSHELL_MESH::FACET FACET;

  cITERATIVE_UNFOLDER(HALF_EDGE root_edge, HALF_EDGE nbr_edge);
  
  cITERATIVE_UNFOLDER(FACET root_facet, FACET nbr_facet, cSHELL_MESH::VERTEX vertex);

  dFLOAT relativeRotation() {
    return m_rel_rotation;
  }

  VOID unfoldedVector(dFLOAT unfolded_vec[2]) {
    vcopy2(unfolded_vec, m_unfolded_min);
  }

  BOOLEAN unfoldingSuccessful() {
    return m_unfolding_successful;
  }

  dFLOAT angleOfFirstSegment() {
    dFLOAT angle = atan2(m_unfolded_min[1],m_unfolded_min[0]);
    return angle;
  }
private:
  BOOLEAN unfold_across_edge(HALF_EDGE root_edge);

  VOID handle_very_small_surfels(cSHELL_MESH::VERTEX vertex);

  dFLOAT calculate_unfolded_vector_at_angle(dFLOAT angle);

  BOOLEAN calculate_derivative_at_angle(dFLOAT angle1, dFLOAT& derivative, dFLOAT& vlength);

  VOID iterate_with_unfolding_algorithm(dFLOAT angle_im2, dFLOAT dLda_im2,
                                        dFLOAT angle_im1, dFLOAT dLda_im1);

  VOID unfold_with_known_start_point();

  FACET m_root_facet, m_nbr_facet;
  dFLOAT m_unfolded_min[2];
  dFLOAT m_min_length;
  cSHELL_MESH::sBG_VECTOR3 m_min_first_cut;
  dFLOAT m_rel_rotation;
  BOOLEAN m_unfolding_successful;
  asINT32 m_iterNum;
  static constexpr asINT32 m_maxIters = 7;
  static constexpr dFLOAT m_absTol = 1e-6;
  static constexpr dFLOAT m_relTol = 1e-3;  
  std::unique_ptr<cSURFEL_UNFOLDER> m_shortest_length_unfolding;
};

struct sGHOST_SURFEL_STENCIL_INFO;
class cFACE_CONNECTIVITY_INFO;
typedef std::vector<cSHELL_MESH::HALF_EDGE> HALF_EDGE_VECTOR;
class cMESH_STATISTICS;
class cFACET_MESH_STATS;
class cEDGE_MESH_STATS;
class cMESH_TO_VTK_INFO;
class cSTENCIL_DETAILED_INFO;

/**
 @brief Class used to build shell stencils

 @details This class is used to build the stencils used in the shell solver
          for least squares and face flux calculations at the edges. It stores
          a unique_ptr to a cSHELL_MESH_INFO object that contains all of the BREP
          data and provides functions to add surfels to the BREP object. The global
          variable g_shell_stencils_info stores a pointer to this class, which is
          allocated and used to build the stencil information when the simulation contains
          shells and is deleted once the stencils building is completed (The use of a global
          variable allows for access to BREP objects without the need to pass the
          pointer between functions).

          The stencil building has 5 steps:
          1) A cSHELL_MESH BREP object is created and all conduction surfels are added to
              it. 
          2) All facets corresponding to shell surfels or conduction surfels sharing an edge with a
              shell surfel are identified and flagged to be kept.
          3) A second cSHELL_MESH BREP object is created and only facets flagged in step 2 are
              added to it. When facets are being added, any colinear half_edges that contain identical
              neighboring facets are combined to ensure two neighboring facets do not have multiple edges
              between them. The original BREP object stored in the unique_ptr is replaced with this
              simplified BREP mesh.
          4) Rotation information for the shell surfels' local 2D csys is calculated and the
              local geometric information of the cSHELL_MESH::HALF_EDGES their facets contain are calculated.
          5) Using the simplified BREP information, SHELL_CONDUCTION_STENCIL_NEIGHBOR and CONDUCTION_EDGE_BASE
              objects are created for each shell surfel.

          Additionally, a map of cFACE_CONNECTIVITY_INFO objects can be generated and written to a
          log file for debugging the types of connections between edges.
 */
typedef class cSHELL_STENCIL_INFO {
public:
  typedef std::map<asINT32, cFACE_CONNECTIVITY_INFO*> FACE_CONNECTIVITY_MAP;

  ~cSHELL_STENCIL_INFO();

  cSHELL_MESH* get_shell_mesh_ptr() {
    return m_shell_mesh.get();
  }

  VOID build_shell_stencils();

  VOID dump_face_connectivity_info();

  static asINT32 send_and_receive_counts_to_csps(std::vector<asINT32>& counts_to_send,
                                                 std::vector<asINT32>& counts_to_recv);

private:
  VOID clear() {
    m_shell_mesh.release();
  }

  VOID add_face_connectivity_info(cSHELL_MESH::FACET root_facet, HALF_EDGE_VECTOR& nbr_edges, uINT32 e_type);

  VOID add_face_connectivity_info(cSHELL_MESH::FACET root_facet, cSHELL_MESH::HALF_EDGE nbr_edge, uINT32 e_type);

  VOID init_shell_mesh();

  VOID simplify_facets();

  BOOLEAN mark_facets_to_keep();

  VOID compute_local_csys_for_shells();

  VOID calculate_int_edge_geometry_for_facets();

  VOID calculate_int_edge_geometry_for_facet(cSHELL_MESH::FACET root_facet);

  VOID calculate_int_edge_geometry_for_2d_facet(cSHELL_MESH::FACET root_facet);

  VOID build_stencils_for_facets();

  VOID build_stencil_for_isolated_facet(cSHELL_MESH::FACET root_facet, BOOLEAN print_simerr = TRUE);

  VOID build_stencil_for_facet(cSHELL_MESH::FACET root_facet);

  VOID build_stencil_for_2d_facet(cSHELL_MESH::FACET root_facet);

  VOID build_stencil_for_ghost_facet(sGHOST_SURFEL_STENCIL_INFO& ghost_info);

  VOID build_stencil_for_2d_ghost_facet(sGHOST_SURFEL_STENCIL_INFO& ghost_info);

  VOID compute_local_rotation_info(cSHELL_MESH::FACET facet);

  VOID get_ghost_surfel_ids(asINT32& num_ghost_surfels_total, asINT32& num_csp_being_sent_ghost_ids,
                            std::vector<asINT32>& num_ghost_ids_to_send_to_csp,
                            std::vector<std::vector<uINT32>>& ghost_ids_to_send_to_csp,
                            asINT32& num_csp_recv_ghost_ids_from, std::vector<asINT32>& num_ghost_ids_to_recv_from_csp,
                            std::vector<std::vector<uINT32>>& ghost_ids_recv_from_csp);

  VOID comm_ghost_surfel_info();

  VOID reduce_facet_edges(cSHELL_MESH::FACET facet, cSHELL_MESH* stencil_mesh, iBREP_SHELL shell_index);

  SHELL_CONDUCTION_STENCIL_NEIGHBOR create_internal_edge(cSHELL_MESH::HALF_EDGE half_edge,
      cSHELL_MESH::HALF_EDGE nbr_half_edge, BOOLEAN is_switched);

  SHELL_CONDUCTION_STENCIL_NEIGHBOR create_boundary_edge(
    cSHELL_MESH::HALF_EDGE half_edge, std::vector<cSHELL_MESH::HALF_EDGE>& nbr_half_edges,
    const uINT32 edge_type, const dFLOAT unfold_centroid[2]);

  SHELL_CONDUCTION_STENCIL_NEIGHBOR create_dyn_data_boundary_edge(
    cSHELL_MESH::HALF_EDGE half_edge, std::vector<cSHELL_MESH::HALF_EDGE>& nbr_half_edges,
    const uINT32 edge_type, const dFLOAT unfold_centroid[2]);
    
  SHELL_CONDUCTION_STENCIL_NEIGHBOR create_vertex_neighbor_stencil(
    sSURFEL* nbr_surfel, const dFLOAT unfold_centroid[2]);

  BOOLEAN storeFaceConnectivity() {
    return (m_face_connectivity_map.get() != NULL);
  }

  BOOLEAN calculateMeshStats() {
    return (m_mesh_statistics.get() != NULL);
  }

  BOOLEAN outputVTKInfo() {
    return (m_mesh_to_vtk_info.get() != NULL);
  }

  BOOLEAN outputStencilDetails() {
    return (m_surfel_stencil_details.get() != NULL);
  }

  VOID startNewFacetForStats(cSHELL_MESH::FACET facet);

  VOID addInternalEdgeStats(cSHELL_MESH::HALF_EDGE he, cCONDUCTION_EDGE_INTERNAL* int_edge, const dFLOAT unfold_vec[2]);

  VOID addBoundaryEdgeStats(cSHELL_MESH::HALF_EDGE he);

  std::vector<std::vector<cSHELL_MESH::FACET>> m_ghost_surfel_sps;
  std::vector<sGHOST_SURFEL_STENCIL_INFO> m_ghost_stencil_info;

  std::unique_ptr<cSHELL_MESH> m_shell_mesh;
  std::unique_ptr<FACE_CONNECTIVITY_MAP> m_face_connectivity_map;
  std::unique_ptr<cMESH_STATISTICS> m_mesh_statistics;
  std::unique_ptr<cMESH_TO_VTK_INFO> m_mesh_to_vtk_info;
  std::unique_ptr<cSTENCIL_DETAILED_INFO> m_surfel_stencil_details;
} *SHELL_STENCIL_INFO;

struct sGHOST_SURFEL_STENCIL_INFO_ELEMENT_SEND_FIELD {
  uINT32 m_stencil_type;
  sdFLOAT m_lsq_coeff[2];
  sdFLOAT m_unfolded_centroid[2];
  SHOB_ID m_nbr_surfel_id;
};

struct sGHOST_SURFEL_STENCIL_INFO_ELEMENT {
  uINT32 m_stencil_type;
  sdFLOAT m_lsq_coeff[2];
  sdFLOAT m_unfolded_centroid[2];
  SHOB_ID m_nbr_surfel_id;

  VOID add_send_size(asINT32 &tot_send_size) {
    tot_send_size += send_size();
  }

  VOID update_from_stencil(cSHELL_CONDUCTION_STENCIL_NEIGHBOR* nbr);

  asINT32 send_size() {
    return sizeof(sGHOST_SURFEL_STENCIL_INFO_ELEMENT_SEND_FIELD) / sizeof(sFLOAT);
  }

  VOID fill_send_buffer(sFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sGHOST_SURFEL_STENCIL_INFO_ELEMENT_SEND_FIELD*>(send_buffer);
    memcpy(&(field->m_stencil_type),       &m_stencil_type,         sizeof(field->m_stencil_type));
    memcpy(field->m_lsq_coeff,             m_lsq_coeff,             sizeof(field->m_lsq_coeff));
    memcpy(field->m_unfolded_centroid,     m_unfolded_centroid,     sizeof(field->m_unfolded_centroid));
    memcpy(&(field->m_nbr_surfel_id),      &m_nbr_surfel_id,        sizeof(field->m_nbr_surfel_id));
    field++;
    send_buffer = reinterpret_cast<sFLOAT*>(field);
  }

  VOID expand_recv_buffer(sFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sGHOST_SURFEL_STENCIL_INFO_ELEMENT_SEND_FIELD*>(recv_buffer);
    memcpy(&m_stencil_type,         &(field->m_stencil_type),       sizeof(field->m_stencil_type));
    memcpy(m_lsq_coeff,             field->m_lsq_coeff,             sizeof(field->m_lsq_coeff));
    memcpy(m_unfolded_centroid,     field->m_unfolded_centroid,     sizeof(field->m_unfolded_centroid));
    memcpy(&m_nbr_surfel_id,        &(field->m_nbr_surfel_id),      sizeof(field->m_nbr_surfel_id));
    field++;
    recv_buffer = reinterpret_cast<sFLOAT*>(field);
  }
};

struct sGHOST_SURFEL_STENCIL_INFO_SEND_FIELD {
  SHOB_ID m_ghost_surfel_id;
  asINT32 m_num_stencil_nbrs;
};

typedef struct sGHOST_SURFEL_STENCIL_INFO {
  SHOB_ID ghost_surfel_id;
  asINT32 num_stencil_nbrs;
  std::vector<sGHOST_SURFEL_STENCIL_INFO_ELEMENT> stencil_nbrs;

  sGHOST_SURFEL_STENCIL_INFO(sSURFEL* surfel);

  sGHOST_SURFEL_STENCIL_INFO() {}

  VOID add_send_size(asINT32 &tot_send_size) {
    tot_send_size += send_size();
  }

  asINT32 send_size() {
    asINT32 this_send_size = sizeof(sGHOST_SURFEL_STENCIL_INFO_SEND_FIELD) / sizeof(sFLOAT);
    for (auto& stencil_nbr : stencil_nbrs) {
      stencil_nbr.add_send_size(this_send_size);
    }
    return this_send_size;
  }

  VOID fill_send_buffer(sFLOAT* &send_buffer) {
    auto field = reinterpret_cast<sGHOST_SURFEL_STENCIL_INFO_SEND_FIELD*>(send_buffer);
    memcpy(&(field->m_ghost_surfel_id),      &ghost_surfel_id,      sizeof(field->m_ghost_surfel_id));
    memcpy(&(field->m_num_stencil_nbrs),     &num_stencil_nbrs,     sizeof(field->m_num_stencil_nbrs));
    field++;
    send_buffer = reinterpret_cast<sFLOAT*>(field);
    for (auto& stencil_nbr : stencil_nbrs) {
      stencil_nbr.fill_send_buffer(send_buffer);
    }
  }

  VOID expand_recv_buffer(sFLOAT* &recv_buffer) {
    auto field = reinterpret_cast<sGHOST_SURFEL_STENCIL_INFO_SEND_FIELD*>(recv_buffer);
    memcpy(&ghost_surfel_id,      &(field->m_ghost_surfel_id),      sizeof(field->m_ghost_surfel_id));
    memcpy(&num_stencil_nbrs,     &(field->m_num_stencil_nbrs),     sizeof(field->m_num_stencil_nbrs));
    field++;
    recv_buffer = reinterpret_cast<sFLOAT*>(field);
    stencil_nbrs.resize(num_stencil_nbrs);
    for (auto& stencil_nbr : stencil_nbrs) {
      stencil_nbr.expand_recv_buffer(recv_buffer);
    }
  }

} *GHOST_SURFEL_STENCIL_INFO;


/**
 @brief Class to store connection information between faces in the simulation.
  
  @details This class will store all types of edges that are created between the face
          it represents and all other faces in the domain. This data will be written
          to the a file when the stencil building completes. To enable this,
          set pfc_print_shell_mesh_connectivity = 1 in the exa_turb_d19.init file.
  */
typedef class cFACE_CONNECTIVITY_INFO {
public:
  cFACE_CONNECTIVITY_INFO() = default;
  cFACE_CONNECTIVITY_INFO(asINT32 face_id) : m_face_id(face_id) {}
  VOID set_face_id(asINT32 face_id) { m_face_id = face_id; }

  VOID add_face_connectivity(std::vector<asINT32>& new_face_ids, uINT32 new_e_type);

  VOID nbr_list_to_str(std::vector<asINT32>& connection_nbrs, std::ostringstream& oss);

  std::string nbr_set_list_to_str(std::vector<std::vector<asINT32>>& connection_nbr_set);

  VOID print_connectivity(std::ofstream& connectFile);

  VOID print_edge_connections(uINT32 etype_to_print, std::ofstream& connectFile);

private:
  std::string get_edge_type_string(uINT32 eType);

  asINT32 m_face_id;
  std::vector<std::vector<std::vector<asINT32>>> m_connection_nbrs;
  std::vector<uINT32> m_connection_types;
} *FACE_CONNECTIVITY_INFO;


typedef class cEDGE_MESH_STATS {
public:
  cEDGE_MESH_STATS(sdFLOAT skew, sdFLOAT cellRat)
      : m_nonOrthog(-2.), m_faceSkew(skew), m_cellSizeRatio(cellRat), m_is_bnd(FALSE) {}

  cEDGE_MESH_STATS(sdFLOAT skew)
    : m_nonOrthog(-1), m_faceSkew(skew), m_cellSizeRatio(-1), m_is_bnd(TRUE) {}
  
  sdFLOAT nonOrthogonality() {
    assert(!m_is_bnd);
    return m_nonOrthog;
  }

  sdFLOAT faceSkewness() { return m_faceSkew; }

  sdFLOAT cellSizeRatio() {
    assert(!m_is_bnd);
    return m_cellSizeRatio;
  }

  BOOLEAN isBoundary() { return m_is_bnd; }

  sdFLOAT m_nonOrthog;
  sdFLOAT m_faceSkew;
  sdFLOAT m_cellSizeRatio;
  BOOLEAN m_is_bnd;
} *EDGE_MESH_STATS;
  

typedef class cFACET_MESH_STATS {
public:
  cFACET_MESH_STATS() {}
      
  ~cFACET_MESH_STATS() {
    for (auto& edgeStats : m_edge_stats) {
      delete edgeStats.second;
    }
  }

  VOID addStats(cSHELL_MESH::HALF_EDGE he, sdFLOAT skew, sdFLOAT cellRatio) {
    m_edge_stats[he] = new cEDGE_MESH_STATS(skew, cellRatio);
    // m_edge_stats.emplace_back(nonOrtho, skew, cellRatio);
  }

  VOID addStats(cSHELL_MESH::HALF_EDGE he, sdFLOAT skew) {
    m_edge_stats[he] = new cEDGE_MESH_STATS(skew);
    // m_edge_stats.emplace_back(cEDGE_MESH_STATS(skew));
  }

  VOID addAspectRatio(dFLOAT aratio) {
    m_aspect_ratio = aratio;
  }

  dFLOAT getAspectRatio() {
    return m_aspect_ratio;
  }

  VOID writeFacetStats(cSHELL_MESH::FACET facet, std::ofstream& statsFile);

  VOID setNonOrthogonality(cSHELL_MESH::HALF_EDGE he, sdFLOAT nonOrtho) {
    m_edge_stats[he]->m_nonOrthog = nonOrtho*180./M_PI;
  }

  cEDGE_MESH_STATS* getStats(cSHELL_MESH::HALF_EDGE he) {
    return m_edge_stats[he];
  }
  sdFLOAT nonOrthogonality(cSHELL_MESH::HALF_EDGE he) {
    return m_edge_stats[he]->nonOrthogonality();
  }
  sdFLOAT faceSkewness(cSHELL_MESH::HALF_EDGE he) {
    return m_edge_stats[he]->faceSkewness();
  }
  sdFLOAT cellSizeRatio(cSHELL_MESH::HALF_EDGE he) {
    return m_edge_stats[he]->cellSizeRatio();
  }
  BOOLEAN isBoundary(cSHELL_MESH::HALF_EDGE he) {
    return m_edge_stats[he]->isBoundary();
  }

  VOID finalizeStats(dFLOAT& sumNonOrtho, dFLOAT& sumSkewness, dFLOAT& sumBndSkewness, dFLOAT& sumCellRatio,
                      dFLOAT& sumAspectRatio, int& nCells, int& nIntEdges, int& nBndEdges, sFLOAT& maxNonOrtho, 
                      sFLOAT& maxSkewness, sFLOAT& maxBndSkewness, sFLOAT& minCellRatio, sFLOAT& maxAspectRatio,
                      int& nSevereNonOrthog, int& nSevereSkew,int& nSevereBndSkew);

  std::map<cSHELL_MESH::HALF_EDGE,EDGE_MESH_STATS> m_edge_stats;
  dFLOAT m_aspect_ratio;
} *FACET_MESH_STATS;

class cMESH_STATISTICS {
public:
  cMESH_STATISTICS() : m_cur_facet(NULL), m_cur_stats(NULL) {}

  ~cMESH_STATISTICS() {
    for (auto& facetStats : m_facet_stats) {
      delete facetStats.second;
    }
  }

  VOID writeStatsFile();

  VOID startNewFacet(cSHELL_MESH::FACET facet);

  VOID addInternalEdge(cSHELL_MESH::HALF_EDGE he, sdFLOAT skew, sdFLOAT cellRatio) {
    m_cur_stats->addStats(he, skew, cellRatio);
  }

  VOID addInternalEdgeNonOrthogonality(cSHELL_MESH::FACET facet);

  VOID addBoundaryEdge(cSHELL_MESH::HALF_EDGE he, sdFLOAT skew) {
    m_cur_stats->addStats(he, skew);
  }

  VOID finalizeStats();

  cSHELL_MESH::FACET m_cur_facet;
  cFACET_MESH_STATS* m_cur_stats;
  // std::vector<cFACET_MESH_STATS> m_facet_stats;
  std::map<cSHELL_MESH::FACET,FACET_MESH_STATS> m_facet_stats;
};


class cMESH_TO_VTK_INFO {
public:
  cMESH_TO_VTK_INFO();

  ~cMESH_TO_VTK_INFO();

  VOID addBoundaryEdgeInfo(cSHELL_MESH::HALF_EDGE he);

  VOID addEdgeInfo(cSHELL_MESH::HALF_EDGE he);

  VOID addVertexIndices(asINT32 startInd, asINT32 nVerts);

  VOID addFacet(cSHELL_MESH::FACET facet);

  VOID addIsolatedFacet(cSHELL_MESH::FACET facet);

  std::ofstream m_facet_file;
};

class cSTENCIL_DETAILED_INFO {
public:
  cSTENCIL_DETAILED_INFO();
  
  ~cSTENCIL_DETAILED_INFO();

  VOID readSurfelList();

  BOOLEAN isSurfelInList(sSURFEL* surfel);

  VOID checkAndStoreNbrFacet(cSHELL_MESH::FACET facet);

  VOID addFacet(cSHELL_MESH::FACET facet);

  VOID addNbrFacets();

  std::ofstream m_facet_file;
  std::vector<asINT32> m_surfels_to_process;
  std::vector<cSHELL_MESH::FACET> m_nbr_facets;
};

extern SHELL_STENCIL_INFO g_shell_stencils_info;

#endif