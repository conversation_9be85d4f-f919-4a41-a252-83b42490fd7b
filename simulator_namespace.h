/*
 * common_mem.h
 *
 *  Created on: Jun 18, 2018
 *      Author: mrao
 */

#ifndef SIMENG_SIMULATOR_NAMESPACE_H_
#define SIMENG_SIMULATOR_NAMESPACE_H_

#include PHYSTYPES_H

//======Simulator Namespace Macro Def============
#if BUILD_D19_LATTICE

  #if BUILD_DOUBLE_PRECISION
    #define SIMULATOR_NAMESPACE Simulator_D19_DP
  #else
    #define SIMULATOR_NAMESPACE Simulator_D19_SP
  #endif

#elif BUILD_D39_LATTICE

  #if BUILD_DOUBLE_PRECISION
    #define SIMULATOR_NAMESPACE Simulator_D39_DP
  #else
    #define SIMULATOR_NAMESPACE Simulator_D39_SP
  #endif

#elif BUILD_5G_LATTICE

  #if BUILD_DOUBLE_PRECISION
    #define SIMULATOR_NAMESPACE Simulator_D5G_DP
  #else
    #define SIMULATOR_NAMESPACE Simulator_D5G_SP
  #endif

#endif

//==============================================
// Define sdfloat
#if BUILD_DOUBLE_PRECISION
typedef dFLOAT sdFLOAT;
typedef sINT64 sdFLOAT_SIZE_INT;
typedef dFLOAT vFLOAT;

// This looks like it should be a typedef, but a MPI_Datatype isn't really a type
#define eMPI_sdFLOAT eMPI_dFLOAT

#define sdFLOAT_MAX DFLOAT_MAX
#define sdFLOAT_MIN DFLOAT_MIN

#else
typedef sFLOAT sdFLOAT;
typedef sINT32 sdFLOAT_SIZE_INT;
typedef sFLOAT vFLOAT;

// See above
#define eMPI_sdFLOAT eMPI_sFLOAT


#define sdFLOAT_MAX SFLOAT_MAX
#define sdFLOAT_MIN SFLOAT_MIN
#endif

//==============================================
// forward declarations;
template<size_t N_VOXELS> struct sdFLOAT_UBLK;
template<size_t N_VOXELS> struct ubFLOAT_UBLK;
template<size_t N_VOXELS> struct tINLINE_BLOCK_LAYOUT;
template<size_t N_VOXELS> struct tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT;

template<typename SOLVER_BLOCK_LAYOUT>
constexpr BOOLEAN is_inline_layout();

template<size_t N_VOXELS_,
         template <size_t> class tUBFLOAT_DATA_TYPE,
         template <size_t> class tBLOCK_LAYOUT>
struct tUBLK_TYPE_TAG;

#ifdef BUILD_GPU
using UBLK_SDFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_8, sdFLOAT_UBLK, tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT>;
using UBLK_UBFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_8, ubFLOAT_UBLK, tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT>;
#else
using UBLK_SDFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_8, sdFLOAT_UBLK, tINLINE_BLOCK_LAYOUT>;
using UBLK_UBFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_8, ubFLOAT_UBLK, tINLINE_BLOCK_LAYOUT>;
#endif

using MBLK_SDFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_64, sdFLOAT_UBLK, tINLINE_BLOCK_LAYOUT>;
using MBLK_UBFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_64, ubFLOAT_UBLK, tINLINE_BLOCK_LAYOUT>;
using HMBLK_SDFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_64, sdFLOAT_UBLK, tHOST_COMPRESSED_SOLVER_BLOCK_LAYOUT>;

template<size_t N_VOXELS>
using tUBLK_SDFLOAT_TYPE_TAG = typename std::conditional<N_VOXELS == N_VOXELS_8,
                                                         UBLK_SDFLOAT_TYPE_TAG,
                                                         MBLK_SDFLOAT_TYPE_TAG>::type;

template<size_t N_VOXELS>
using tUBLK_UBFLOAT_TYPE_TAG = typename std::conditional<N_VOXELS == N_VOXELS_8,
                                                         UBLK_UBFLOAT_TYPE_TAG,
                                                         MBLK_UBFLOAT_TYPE_TAG>::type;

template<size_t N_>
struct tSFL_TYPE_TAG {
  enum {
    N = N_
  };

  __HOST__DEVICE__ constexpr static size_t is_msfl() {
    return N_ > 1;
  }
};

constexpr unsigned N_SFLS_PER_MSFL = 64;
using SFL_SDFLOAT_TYPE_TAG = tSFL_TYPE_TAG<1>;
using MSFL_SDFLOAT_TYPE_TAG = tSFL_TYPE_TAG<N_SFLS_PER_MSFL>; 

inline namespace SIMULATOR_NAMESPACE {

template<typename TAG>
class tSURFEL;
  
using sSURFEL = tSURFEL<SFL_SDFLOAT_TYPE_TAG>;
using sMSFL = tSURFEL<MSFL_SDFLOAT_TYPE_TAG>;
  
template<typename UBLK_TYPE_TAG>
struct tUBLK;

class sBSURFEL;
}

typedef SIMULATOR_NAMESPACE::sSURFEL sSURFEL;

typedef SIMULATOR_NAMESPACE::tUBLK<UBLK_SDFLOAT_TYPE_TAG> sUBLK, *UBLK;
#ifdef BUILD_GPU
typedef SIMULATOR_NAMESPACE::tUBLK<MBLK_SDFLOAT_TYPE_TAG> sMBLK, *MBLK;
typedef SIMULATOR_NAMESPACE::tUBLK<HMBLK_SDFLOAT_TYPE_TAG> sHMBLK, *HMBLK;
#endif

typedef SIMULATOR_NAMESPACE::sBSURFEL sBSURFEL;

namespace GPU {

  template<typename T> class Ptr;
  using sDEV_PTR = Ptr<void>;
  typedef tUBLK<MBLK_SDFLOAT_TYPE_TAG> sMBLK, *MBLK;
  constexpr size_t MINIMUM_CUDA_ALIGNMENT = 256;  
}

template<size_t N_VOXELS_,
         template <size_t> class tUBFLOAT_DATA_TYPE,
         template <size_t> class tBLOCK_LAYOUT>
struct tUBLK_TYPE_TAG {
  
  using UBFLOAT_CONFIG_TYPE = tUBFLOAT_DATA_TYPE<N_VOXELS_>;
  using DATATYPE = typename UBFLOAT_CONFIG_TYPE::DATATYPE;
  using BLOCK_LAYOUT = tBLOCK_LAYOUT<N_VOXELS_>;

  //The UBLK_TYPE_TAG is the best place to accurately define the ubFLOAT and
  //sdFLOAT TAG variants, because we can easily preserve the other template parameters
  //i.e voxel count and layout type
  using SDFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_, sdFLOAT_UBLK, tBLOCK_LAYOUT>;
  using UBFLOAT_TYPE_TAG = tUBLK_TYPE_TAG<N_VOXELS_, ubFLOAT_UBLK, tBLOCK_LAYOUT>;
    
  enum { 
    N_VOXELS = UBFLOAT_CONFIG_TYPE::N_VOXELS,	
    N_VOXELS_2D = UBFLOAT_CONFIG_TYPE::N_VOXELS_2D, 
    N_VOXELS_1D = UBFLOAT_CONFIG_TYPE::N_VOXELS_1D, 
    N_UBLKS = UBFLOAT_CONFIG_TYPE::N_UBLKS, 
    N_UBLKS_1D = UBFLOAT_CONFIG_TYPE::N_UBLKS_1D,
    N_VOXORS = UBFLOAT_CONFIG_TYPE::N_VOXORS
  };
  
  constexpr static BOOLEAN is_inline_layout() { return ::is_inline_layout<BLOCK_LAYOUT>(); }

  __HOST__DEVICE__ constexpr static size_t is_mblk() {
    return N_VOXELS > N_VOXELS_8;
  }
};
#endif /* SIMENG_SIMULATOR_NAMESPACE_H_ */
