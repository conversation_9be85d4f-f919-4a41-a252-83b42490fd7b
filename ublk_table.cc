/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "ublk_table.h"
#include "sim.h"
#include "advect.h"
#include "box_advect.h"
#include "vr.h"
#include PHYSICS_H

sUBLK_TABLE g_ublk_table[STP_N_REALMS];

//----------------------------------------------------------------------------
// sUBLK_TABLE::sort_by_coordinate
//----------------------------------------------------------------------------
VOID sUBLK_TABLE::sort_by_coordinate(asINT32 axis)
{
  COORD_ORDER order(axis);
  sort( m_ublks.begin(), m_ublks.end(), order );
}

//----------------------------------------------------------------------------
// sUBLK_TABLE::sort_by_id
//----------------------------------------------------------------------------
VOID sUBLK_TABLE::sort_by_id()
{
  SHOB_ID_ORDER id_order;
  sort( m_ublks.begin(), m_ublks.end(), id_order );
}

//----------------------------------------------------------------------------
// sUBLK_TABLE::ublk_from_id
//----------------------------------------------------------------------------
UBLK sUBLK_TABLE::ublk_from_id(SHOB_ID id)
{
  // binary search
  SHOB_ID_ORDER id_order;
  UBLK ublk      = NULL;
  auto iter  = lower_bound( m_ublks.begin(), m_ublks.end() , id, id_order );

  if (iter == m_ublks.end())
    return NULL;

  ublk = *iter;

  if (ublk != NULL && ublk->id() != id)
    ublk = NULL;

  return ublk;
}

UBLK sUBLK_TABLE::ublk(SHOB_ID index) 
{ 
  return index >= m_ublks.size()? NULL : m_ublks[index]; 
}

static VOID isolate_split_ublks_into_boxes(asINT32 nsplits, std::vector<uINT64> &split_ublks) 
{
  static std::vector < uINT64 > matched_ublks;
  //uINT64 matched_ublks[MAX_SPLIT_UBLKS];
  asINT32 nsp1, nsp2;
  for (nsp1 = 0; nsp1 < nsplits; nsp1++) {
    if (split_ublks[nsp1] == 0)
      continue;
    UBLK sp_ublk = (UBLK)split_ublks[nsp1];

    // Solid VR fine ublks should not be assigned to boxes
    if (sp_ublk->is_solid() && (sp_ublk->is_vr_fine() || sp_ublk->is_mirror())) {
      continue;
    }

    UBLK_BOX split_box1 = sp_ublk->get_ublk_box();

    asINT32 nsplit_ublks = 0;
    for (nsp2 = nsp1; nsp2 < nsplits; nsp2++) {
      if (split_ublks[nsp2] == 0)
        continue;
      UBLK_BOX split_box2 = ((UBLK) split_ublks[nsp2])->get_ublk_box();
      UBLK ublk2 = (UBLK) split_ublks[nsp2];
      // solid vr coarse ublks will have solid vr fine ublk underneath which
      // are needed for traversal of bsurfels through these solid vr coarse
      // ublks
      if ((split_box1 == split_box2) && (ublk2->is_vr_coarse() || !ublk2->is_solid())) {
        matched_ublks.push_back(split_ublks[nsp2]);
        nsplit_ublks++;
        split_ublks[nsp2] = 0;
      }
    }

    // The original split instance index may be modified here, as a result
    // split advect factors which were not reported in the lgi file will have
    // to be created since the advection between different instance indices
    // may be triggered now.
    if (nsplit_ublks > 1) {
      uINT16 split_indices[3];
      sUBLK_VECTOR *split_ublks_vector = sSHOB_ALLOCATOR<sUBLK_VECTOR>::malloc(sizeof(sUBLK_VECTOR));
      split_ublks_vector->m_n_ublks = nsplit_ublks;
      split_ublks_vector->m_tagged_ublks = new TAGGED_UBLK[nsplit_ublks];
      UBLK first_split_ublk = (UBLK) matched_ublks[0];
      BOOLEAN is_ghost = first_split_ublk->is_ghost();
      ccDOTIMES(nsp, nsplit_ublks) {
        UBLK split_ublk = (UBLK) matched_ublks[nsp];
        if (split_ublk->is_ghost() != is_ghost) {
          msg_internal_error("Inconsistency in isolate_split_ublks_into_boxes: both ghosts and non-ghosts in a split ublk");
        }
        if (!is_ghost && !split_ublk->has_two_copies()) {
          msg_internal_error("Inconsistency in isolate_split_ublks_into_boxes: non-ghost split ublk has only one copy");
        }
        split_ublk->m_split_tagged_instance.set(nsp);
        split_ublks_vector->m_tagged_ublks[nsp].set_ublk_and_type(split_ublk);
        //printf("UBLK %d loc [%d %d %d] instance %d is marked as split\n", split_ublk->id(),
        //       split_ublk->location(0), split_ublk->location(1), split_ublk->location(2), nsp);
      }
      split_box1->get_indices(((UBLK) matched_ublks[0])->m_location, split_indices);
      TAGGED_UBLK *tagged_ublk = split_box1->tagged_ublk(split_indices);
      tagged_ublk->set_ublk_as_split(split_ublks_vector);
      if (!is_ghost) {
        tagged_ublk->set_ublk_has_two_copies();
      }
    } else if (nsplit_ublks == 1) {
      TAGGED_UBLK *tagged_ublk = split_box1->assign_ublk((UBLK)matched_ublks[0]);
      tagged_ublk->set_ublk_and_type((UBLK)matched_ublks[0]);
      ((UBLK)matched_ublks[0])->unset_split();
    }
    else {
      // Solid split ublks that have been cutoff from their split friends
      // need to be marked as being very lonely.
      sp_ublk->unset_split();
    }
    matched_ublks.clear();
  }
}


VOID sUBLK_TABLE::mark_ublks_presence_in_boxes() 
{
  for (UBLK ublk: m_ublks) {
    // ublk box for mirror ublks is assigned later
    if (ublk->is_mirror())
      continue;

    UBLK_BOX ublk_box = ublk->get_ublk_box();

    if (ublk_box == NULL)
      msg_internal_error("Ublk %d of type %d is not assigned to a box", ublk->id(), ublk->ublk_type());

    uINT16 indices[3];
    ublk_box->get_indices(ublk->m_location, indices);

    ublk_box->m_fill_info->mark_ublk_present_in_slot(indices);
    ublk_box->m_fill_info->increment_ublks_filled_count(indices);
  }
}

// Perhaps create a template with is_2D as a boolean template parameter.
static UBLK_BOX assign_ublk_box_after_division(UBLK ublk) {
  UBLK_BOX new_ublk_box = ublk->get_ublk_box();
  while (new_ublk_box) {
    if ((ublk->location(0) > new_ublk_box->m_location[0]) &&
        (ublk->location(1) > new_ublk_box->m_location[1]) &&
        ((sim.num_dims == 2) || 
         (ublk->location(2) > new_ublk_box->m_location[2]))) {
      uINT16 indices[3];
      new_ublk_box->get_indices(ublk->m_location, indices);
      if ((indices[0] < new_ublk_box->m_size[0] - 1) && 
          (indices[1] < new_ublk_box->m_size[1] - 1) &&
          ((sim.num_dims == 2) ||
           (indices[2] < new_ublk_box->m_size[2] - 1))) {
        ublk->set_box_indices(indices, new_ublk_box);
	ublk->set_ublk_box(new_ublk_box);
        return new_ublk_box;
      }
    }
    new_ublk_box = g_ublk_box_fset.find_next_ublk_box(new_ublk_box->m_scale,
                                                      new_ublk_box->m_realm,
                                                      new_ublk_box->m_lrf_index,
                                                      new_ublk_box->m_isolated_domain,
                                                      new_ublk_box->m_box_index);
  }
  msg_internal_error("Did not find a matching ublk box for ublk %d at %d %d %d", ublk->id(),
      ublk->location(0), ublk->location(1), ublk->location(2));
  return NULL;
}

static UBLK_BOX assign_ublk_box_for_mirror_ublk(UBLK ublk) {
  UBLK real_ublk = ublk->mirror_data()->m_mirror_ublk;
  ublk->set_ublk_box(real_ublk->get_ublk_box());
  TAGGED_UBLK tagged_real_ublk = real_ublk->m_box_access.stagged_ublk();
  uINT16 indices[3];
  sUBLK_BOX* ublk_box = ublk->get_ublk_box();
  ublk_box->get_indices(ublk->m_location, indices);
  ublk->set_box_indices(indices, ublk_box);
  return ublk_box;
}

#ifdef _EXA_CLANG
#pragma clang optimize off
#endif
VOID sUBLK_TABLE::assign_ublks_to_boxes()
{
  //uINT64 split_ublks[MAX_SPLIT_UBLKS];
  static std::vector < uINT64 > split_ublks;
  STP_COORD split_location[3] = {-1, -1, -1};
  STP_COORD split_VRF_location[ubFLOAT::N_VOXELS][3] = {-1, -1, -1};
  //uINT64 split_VRF_ublks[ubFLOAT::N_VOXELS][MAX_SPLIT_UBLKS];
  static std::vector < uINT64> split_VRF_ublks[ubFLOAT::N_VOXELS];
  asINT32 n_vr_splits[ubFLOAT::N_VOXELS] = {0};
  asINT32 nrf = 0;
  asINT32 nsplits = 0;
  BOOLEAN is_coarse_ublk_split = false;

  for (UBLK ublk: m_ublks) {
    UBLK_BOX ublk_box = nullptr;
    // ublk boxes for mirror ublks are assigned to be the same real
    // counterpart. Solid mirror ublks do not contain mirror data.
    if (ublk->is_mirror()) {
      if (!ublk->is_solid()) {
        ublk_box = assign_ublk_box_for_mirror_ublk(ublk);
      }
    } else {
      //update to the appropriate divided ublk box
      ublk_box = assign_ublk_box_after_division(ublk);
    }

    TAGGED_UBLK *tagged_ublk = NULL;
    if (is_coarse_ublk_split) {
      if (ublk->is_vr_fine()) {
        if (ublk->is_split()) {
          // since the location of all the split VR coarse ublks is the same,
          // any ublk will work
          asINT32 nrf = vr_fine_ublk_index_within_coarse_parent(ublk, ((UBLK) split_ublks[0]));
          ublk->m_split_tagged_instance.set_lgi_instance(n_vr_splits[nrf]);
          split_VRF_ublks[nrf].push_back((uINT64)ublk);
          n_vr_splits[nrf]++;
        }
      } else if ((nsplits != 0)  &&
          ((split_location[0] != ublk->location(0)) ||
           (split_location[1] != ublk->location(1)) ||
           (split_location[2] != ublk->location(2)) )) {
        ccDOTIMES(nv, ubFLOAT::N_VOXELS) {
          if (n_vr_splits[nv] > 0) {
            isolate_split_ublks_into_boxes(n_vr_splits[nv], split_VRF_ublks[nv]);
            split_VRF_ublks[nv].clear();
            n_vr_splits[nv] = 0;
          }
        }
      }
    }
    if ((nsplits != 0)  && (!ublk->is_vr_fine()) &&
        ((split_location[0] != ublk->location(0)) ||
         (split_location[1] != ublk->location(1)) ||
         (split_location[2] != ublk->location(2)) )) {
      // Collect ublks within the same box since ublks in different boxes do
      // not interact directly.
      isolate_split_ublks_into_boxes(nsplits, split_ublks);
      split_ublks.clear();
      nsplits = 0;
    }

    BOOLEAN is_solid_and_mirror = ublk->is_mirror() && ublk->is_solid();
    if (ublk->is_split()) {
      // skip VR fine ublks since they have dealt with above
      if (!ublk->is_vr_fine()) {
        split_location[0] = ublk->location(0);
        split_location[1] = ublk->location(1);
        split_location[2] = ublk->location(2);
        ublk->m_split_tagged_instance.set_lgi_instance(nsplits);
        split_ublks.push_back((uINT64) ublk);
        nsplits++;
        if (ublk->is_vr_coarse()) {
          is_coarse_ublk_split = true;
        } else {
          is_coarse_ublk_split = false;
        }
      }
    } else {
      if (!is_solid_and_mirror) {
      tagged_ublk = ublk_box->assign_ublk(ublk);
      tagged_ublk->set_ublk_and_type(ublk);
      }
      // Since all the fine ublks may not be split we should not reset
      // this boolean flag.
      if (!ublk->is_vr_fine()) {
        is_coarse_ublk_split = false;
      }
    }
  }

  // if the last ublk was a vr fine split ublk, the last set of vr fine
  // split ublks need to be processed here.
  if (is_coarse_ublk_split) {
    ccDOTIMES(nv, ubFLOAT::N_VOXELS) {
      if (n_vr_splits[nv] > 0) {
        isolate_split_ublks_into_boxes(n_vr_splits[nv], split_VRF_ublks[nv]);
        split_VRF_ublks[nv].clear();
        n_vr_splits[nv] = 0;
      }
    }
  }
  // if the last ublk was a split ublk, the last set of split ublks need
  // to be processed here.
  if (nsplits != 0) {
    // Collect ublks within the same box since ublks in different boxes do
    // not interact directly.
    isolate_split_ublks_into_boxes(nsplits, split_ublks);
    split_ublks.clear();
    nsplits = 0;
  }
}
#ifdef _EXA_CLANG
#pragma clang optimize on
#endif

uINT32 new_split_ublk_instance(sUBLK_VECTOR * split_ublks, uINT32 old_ublk_instance,
                               sUBLK *dest_ublk, asINT32 voxel, asINT32 latvec) {
  asINT32 nsplit_ublks = split_ublks->m_n_ublks;
  TAGGED_UBLK *tagged_ublks = split_ublks->m_tagged_ublks;
  ccDOTIMES(nsp, nsplit_ublks) {
    if (tagged_ublks[nsp].is_ublk()) {
      UBLK ublk = tagged_ublks[nsp].ublk();
      if (ublk->m_split_tagged_instance.get_lgi_instance() == old_ublk_instance) {
        return ublk->m_split_tagged_instance.get();
        //return nsp;
      }
    }
  }
  if (nsplit_ublks > 0) {
  msg_internal_error("Voxel %d of dest ublk %d at [%d %d %d] is fluid connected along out ward pointing latvec %d (%d %d %d). Looking for ublk instance %d, but there are only %d ublk instances at [%d %d %d]",
                     voxel, dest_ublk->id(),
                     dest_ublk->location(0),
                     dest_ublk->location(1),
                     dest_ublk->location(2),
                     latvec,
                     state_vx(latvec),
                     state_vy(latvec),
                     state_vz(latvec),
                     old_ublk_instance, nsplit_ublks,
                     tagged_ublks[0].ublk()->location(0),
                     tagged_ublks[0].ublk()->location(1),
                     tagged_ublks[0].ublk()->location(2));
  } else {
    msg_internal_error("Voxel %d of dest ublk %d at [%d %d %d] is fluid connected along out ward pointing latvec %d (%d %d %d). Looking for ublk instance %d, but there are only %d ublk instances",
                     voxel, dest_ublk->id(),
                     dest_ublk->location(0),
                     dest_ublk->location(1),
                     dest_ublk->location(2),
                     latvec,
                     state_vx(latvec),
                     state_vy(latvec),
                     state_vz(latvec),
                     old_ublk_instance, nsplit_ublks);

  }

  return 0;
}

VOID sUBLK_TABLE::mark_same_split_instance_neighbors() {
  const static sINT16 ublk_voxel_offset_map[6] = {-1, -1, 0, 0, 1, 1};
  BOOLEAN is_2D = sim.is_2d();

  asINT32 n_same_instance_ublks = 0, n_different_instance_ublks = 0;
  for (UBLK ublk: m_ublks) {
    if (ublk->is_mirror())
      continue;
    if (ublk->advect_from_split_ublk()) {
      auto& box_access = ublk->m_box_access;
      SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

      TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();

      bool interacts_with_different_instances = false;
      uINT8 split_instance_index = 255;
      ccDOTIMES(v, ubFLOAT::N_VOXELS) {
        if (!ublk->fluid_like_voxel_mask.test(v))
          continue;
        CONNECT_MASK fluid_connect_mask = ublk->voxel_fluid_connect_mask(v);
        auINT16 voxel_cs[3];
        voxel_cs[0] = ((v >> 2) & 1) + 2;
        voxel_cs[1] = ((v >> 1) & 1) + 2;
        voxel_cs[2] = ((v     ) & 1) + 2;
        ccDOTIMES(latvec, N_MOVING_STATES) {
          asINT32 parity = state_parity(latvec);
          TAGGED_UBLK neighbor;
          if (is_2D && (state_vz(latvec) != 0)) {
            if (split_advect_info) {
              if ((split_advect_info->m_advect_one_instance_masks[v] >> latvec) & 1) {
                if (split_advect_info->m_src_ublk_instances[v][latvec] == 255) {
                  split_advect_info->m_src_ublk_instances[v][latvec] = ublk->m_split_tagged_instance.get();
                }
              }
            }
            continue;
          }

          if (fluid_connect_mask.test(parity)) {
            sINT16 v_offsets[3] = {0};
            v_offsets[0] = state_vx(parity) + voxel_cs[0];
            v_offsets[1] = state_vy(parity) + voxel_cs[1];
            v_offsets[2] = state_vz(parity) + voxel_cs[2];

            if ((v_offsets[0] < 2) || (v_offsets[0] > 3) ||
                (v_offsets[1] < 2) || (v_offsets[1] > 3) ||
                (v_offsets[2] < 2) || (v_offsets[2] > 3) ) {
              sINT16 u_offsets[3] = {0};
              u_offsets[0] = ublk_voxel_offset_map[v_offsets[0]];
              u_offsets[1] = ublk_voxel_offset_map[v_offsets[1]];
              u_offsets[2] = ublk_voxel_offset_map[v_offsets[2]];
              if (is_2D && (u_offsets[2] < 0)) {
                continue;
              }
              neighbor = (box_access.neighbor_ublk(u_offsets));
            } else {
              neighbor = tagged_ublk;
            }
            if (neighbor.is_ublk_scale_interface()) {
              continue;
            } else if (neighbor.is_ublk_split()) {
              if (split_advect_info) {
                if ((split_advect_info->m_advect_one_instance_masks[v] >> latvec) & 1) {
                  if (split_advect_info->m_src_ublk_instances[v][latvec] != 255) {
                    if (split_instance_index == 255) {
                      split_instance_index = split_advect_info->m_src_ublk_instances[v][latvec];
                    } else if (split_instance_index != split_advect_info->m_src_ublk_instances[v][latvec]) {
                      interacts_with_different_instances = true;
                    }
                  } else {
                    split_advect_info->m_src_ublk_instances[v][latvec] = ublk->m_split_tagged_instance.get();
                    if (split_instance_index == 255) {
                      split_instance_index = ublk->m_split_tagged_instance.get();
                    } else {
                      interacts_with_different_instances = true;
                    }
                  }
                } else {
                  interacts_with_different_instances = true;
                }
              }
            }
          }
        }
      }
      if (!interacts_with_different_instances) {
        if (split_advect_info) {
          split_advect_info->m_interacts_with_single_instance = 1;
          split_advect_info->m_split_instance_index = split_instance_index;
        }
        n_same_instance_ublks++;
      } else {
        n_different_instance_ublks++;
      }
    }
  }
  //msg_print("Number of ublks that interact with same instance = %d", n_same_instance_ublks);
  //msg_print("Number of ublks that interact with different instance = %d", n_different_instance_ublks);

}

VOID sUBLK_TABLE::update_split_ublk_instance(REALM realm) {
  sim.n_nonghost_ublks[realm] = 0;

#if BUILD_D39_LATTICE
  const uINT32 N_LATVECS = N_LATTICE_VECTORS_D39;
#else
#if BUILD_D19_LATTICE
  const uINT32 N_LATVECS = N_LATTICE_VECTORS_D25;
#else // 5G LATTICE
  const uINT32 N_LATVECS = N_LATTICE_VECTORS_D19;
#endif
#endif
  const static sINT16 ublk_voxel_offset_map[6] = {-1, -1, 0, 0, 1, 1};

  // This loop is for updating the src_ublk_instance for ublks which are a
  // destination for advection from split ublks
  // Also advect scale factors have to be created for neighbors of some split ublks
  for (UBLK ublk: m_ublks) {

    if (!ublk->is_ghost())
      sim.n_nonghost_ublks[realm]++;

    if (ublk->fluid_like_voxel_mask.none() || ublk->is_mirror()) {
      continue;
    }

    auto& box_access = ublk->m_box_access;
    SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();

    TAGGED_UBLK tagged_ublk = box_access.stagged_ublk();

    // This loop is for filling m_are_any_neighbors_split which contains an active bit for each voxel
    // that has a split neighbor regardless of fluid connectivity. It looks at 18 state neighbors only.
    if (ublk->is_split()) {
      ublk->m_are_any_neighbors_split.set_all();
    } else {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        // During span advect, even voxels with no fluid are advected into,
        // so fluid_like_voxel_mask has to be ignored or else 
        // m_are_any_neighbors_split will be incomplete.
        //if ((ublk->fluid_like_voxel_mask >> voxel & 1) == 0)
        //  continue;
        auINT16 voxel_cs[3];
        voxel_cs[0] = ((voxel >> 2) & 1) + 2;
        voxel_cs[1] = ((voxel >> 1) & 1) + 2;
        voxel_cs[2] = ((voxel     ) & 1) + 2;
        ccDOTIMES(latvec, N_LATVECS) {
          if (sim.is_2d() && (state_vz(latvec) != 0)) {
            continue;
          }
          sINT16 v_offsets[3] = {0};
          v_offsets[0] = state_vx(latvec) + voxel_cs[0];
          v_offsets[1] = state_vy(latvec) + voxel_cs[1];
          v_offsets[2] = state_vz(latvec) + voxel_cs[2];

          if ((v_offsets[0] < 2) || (v_offsets[0] > 3) ||
              (v_offsets[1] < 2) || (v_offsets[1] > 3) ||
              (v_offsets[2] < 2) || (v_offsets[2] > 3) ) {
            sINT16 u_offsets[3] = {0};
            u_offsets[0] = ublk_voxel_offset_map[v_offsets[0]];
            u_offsets[1] = ublk_voxel_offset_map[v_offsets[1]];
            u_offsets[2] = ublk_voxel_offset_map[v_offsets[2]];
            TAGGED_UBLK tagged_neighbor = box_access.neighbor_ublk(u_offsets);
            if ((ublk->m_box_access.m_box_indices[2] + u_offsets[2]) < 0)
              continue;
            if (tagged_neighbor.is_ublk_split()) {
              ublk->m_are_any_neighbors_split.set(voxel);
              break;
            }
          }
        }
      }
    }
    
    //We want all ublks with split neighbors to have a BOX_PLUS, since
    // although they might have a null split_advect_info, they have
    // non empty split advect factors
    if (ublk->m_are_any_neighbors_split.any() && ublk->has_empty_split_info()){
      ublk->m_split_info = cnew sSPLIT_INFO;      
      ublk->m_split_info->m_split_advection_info = NULL;
      auto& box_access = ublk->m_box_access;
      split_advect_info = ublk->get_split_advect_info();  
    }

    // If any of the 18 neighbors is split, the split ublk instances should be re-evaluated
    if (ublk->advect_from_split_ublk() || ublk->is_split() || ublk->m_are_any_neighbors_split.any() )
    {
      asINT32 nfac = 0;

      if (!ublk->is_ghost() && !tagged_ublk.does_ublk_have_two_copies()) {
        msg_internal_error("Destination UBLK %d type %d for split advect does not have two copies", ublk->id(),
                           ublk->ublk_type());
      }

      // Discretizer only reports split advect scale factors if the split ublk
      // instance indices of the split ublks are different. However the split
      // ublk instance index may have been modified when split ublks fall into
      // different ublk boxes, in which case these interactions will have to be
      // added to the split advect scale factors. 
      // Also if a neighbor is no longer split, split advect scale factors
      // should be deleted.

      uINT32 total_advect_scale_factors = 0;
      uINT32 new_tot_advect_scale_factors[ubFLOAT::N_VOXELS] = {0};
      static std::vector <sADVECT_SCALE_FACTOR_INFO> new_advect_scale_factor_info;
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        new_tot_advect_scale_factors[voxel] = total_advect_scale_factors;
        //if (((ublk->m_advect_from_split_mask >> voxel) & 1) == 0)
        //  continue;

#if BUILD_D39_LATTICE
        TAGGED_UBLK neighbors[N_LATTICE_VECTORS_D39];
#else
        TAGGED_UBLK neighbors[N_LATTICE_VECTORS_D25];
#endif
        BOOLEAN for_advect = TRUE;
        if (sim.is_2d()) {
          voxel_neighbors_along_state_directions<TRUE, TRUE >(ublk, voxel, neighbors, ublk->m_box_access, tagged_ublk);
        } else {
          voxel_neighbors_along_state_directions<TRUE, FALSE>(ublk, voxel, neighbors, ublk->m_box_access, tagged_ublk);
        }
#if !BUILD_D39_LATTICE
        voxel_neighbors_along_states_2(ublk, voxel, neighbors);
#endif

        // split advection factors for this voxel
        asINT32 n_advect_factors = 0;
        if (split_advect_info) {
          n_advect_factors = split_advect_info->m_tot_advect_scale_factors[voxel];
        }
        uINT64 split_advect_latvec_mask = 0; //latvecs that have split advect factors
        uINT32 MAX_SPLIT_VOXELS = 256;
#if BUILD_D39_LATTICE
        uINT32 old_ublk_instances[MAX_SPLIT_VOXELS][N_LATTICE_VECTORS_D39];
        sdFLOAT advect_scale_factors[MAX_SPLIT_VOXELS][N_LATTICE_VECTORS_D39];
        uINT32 n_advect_factors_per_latvec[N_LATTICE_VECTORS_D39] = {0};
#else
        uINT32 old_ublk_instances[MAX_SPLIT_VOXELS][N_LATTICE_VECTORS_D25];
        sdFLOAT advect_scale_factors[MAX_SPLIT_VOXELS][N_LATTICE_VECTORS_D25];
        uINT32 n_advect_factors_per_latvec[N_LATTICE_VECTORS_D25] = {0};
#endif

        ccDOTIMES(n, n_advect_factors) {
          sADVECT_SCALE_FACTOR_INFO *adv_factor_info = 
            &(split_advect_info->m_advect_scale_factor_info[nfac]);
          asINT32 latvec = adv_factor_info->latvec;
          asINT32 parity = state_parity(latvec);
          // advect scale factors points from source to destination and neighbor
          // ublk is the source and ublk is the destination
          split_advect_latvec_mask |= (1 << parity);
          cassert(n_advect_factors_per_latvec[parity] < MAX_SPLIT_VOXELS);
          old_ublk_instances[n_advect_factors_per_latvec[parity]][parity]   =
            adv_factor_info->src_ublk_instance;
          advect_scale_factors[n_advect_factors_per_latvec[parity]][parity] =
            adv_factor_info->advect_scale_factor;
          n_advect_factors_per_latvec[parity]++;
          nfac++;
        }

        ccDOTIMES(latvec, N_LATVECS) {
          asINT32 parity = state_parity(latvec);
          if (neighbors[latvec].is_ublk_split()) {
            sUBLK_VECTOR *split_ublks = neighbors[latvec].split_ublks();
            sADVECT_SCALE_FACTOR_INFO new_adv_factor_info;
            if ((split_advect_latvec_mask >> latvec) & 1) { // advect scale factors found
              ccDOTIMES(n, n_advect_factors_per_latvec[latvec]) {
                uINT32 old_ublk_instance = old_ublk_instances[n][latvec];
                // find the matching split ublk and use its new split instance index
                uINT32 new_ublk_instance = new_split_ublk_instance(split_ublks, old_ublk_instance,
                    ublk, voxel, latvec);
                new_adv_factor_info.src_ublk_instance   = new_ublk_instance;
                new_adv_factor_info.latvec              = parity;
                new_adv_factor_info.advect_scale_factor = advect_scale_factors[n][latvec];
                new_advect_scale_factor_info.push_back(new_adv_factor_info);
                total_advect_scale_factors++;
              }
            } else { // no advect scale factors implies the split of ublk and its neighbor is same
              // There are two possibilities
              // ublks split instance in the lgi file is different from that in the simulator
              // neighbors split instance in the lgi file is different from that in the simulator
              uINT32 old_ublk_instance = ublk->m_split_tagged_instance.get_lgi_instance();
              // find the matching split ublk and use its new split instance index
              uINT32 new_ublk_instance = new_split_ublk_instance(split_ublks, old_ublk_instance,
                                                                 ublk, voxel, latvec);
              if (new_ublk_instance != ublk->m_split_tagged_instance.get()) {

                new_adv_factor_info.src_ublk_instance   = new_ublk_instance;
                new_adv_factor_info.latvec              = parity;
                new_adv_factor_info.advect_scale_factor = 1.0;
                new_advect_scale_factor_info.push_back(new_adv_factor_info);
                total_advect_scale_factors++;
                // new split factor added
                ublk->set_advect_from_split_mask(voxel);
                ublk->set_advect_from_split_ublk();
              }
            }
          }
        }
      }
      if (total_advect_scale_factors) {
        if (split_advect_info == NULL) {
          split_advect_info = cnew sSPLIT_ADVECT_INFO;
          if (ublk->has_empty_split_info()) {
            ublk->m_split_info = cnew sSPLIT_INFO;
            ublk->m_split_info->m_split_advection_info = split_advect_info;
          } else {
            ublk->m_split_info->m_split_advection_info = split_advect_info;
          }
        }

        split_advect_info->m_total_advect_scale_factors = total_advect_scale_factors;
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          split_advect_info->m_tot_advect_scale_factors[voxel] =
            new_tot_advect_scale_factors[voxel]; 
        }
        split_advect_info->m_advect_scale_factor_info.clear();
        int prev_latvec = -1, dest_voxel = 0, prev_dest_voxel = -1;
        ccDOTIMES(n, total_advect_scale_factors) {
          split_advect_info->m_advect_scale_factor_info.push_back(new_advect_scale_factor_info[n]);

          while ((dest_voxel < ubFLOAT::N_VOXELS-1) &&
                 (n >= split_advect_info->m_tot_advect_scale_factors[dest_voxel+1])) {
            dest_voxel++;
            prev_latvec = -1;
          }
          if (prev_dest_voxel != dest_voxel) {
            split_advect_info->m_advect_one_instance_masks[dest_voxel] = 0xFFFFFFFF;
            ccDOTIMES(l, N_LATVECS) {
              split_advect_info->m_src_ublk_instances[dest_voxel][l] = 255;
            }
          }

          int latvec = new_advect_scale_factor_info[n].latvec;
          split_advect_info->m_src_ublk_instances[dest_voxel][latvec] =
             new_advect_scale_factor_info[n].src_ublk_instance;
          if (prev_latvec == latvec && prev_dest_voxel == dest_voxel) {
            split_advect_info->m_advect_one_instance_masks[dest_voxel] &= ~(1 << prev_latvec);
          }
          prev_latvec = latvec;
          prev_dest_voxel = dest_voxel;
        }
        new_advect_scale_factor_info.clear();
      } else if (split_advect_info != NULL) {
        //delete((void *) split_advect_info, 0, __FILE__, __LINE__);
        delete split_advect_info;

        //We want all ublks with split neighbors to have a BOX_PLUS, since
        // although they might have a null split_advect_info, they have
        // non empty split advect factors
        if (ublk->m_are_any_neighbors_split.none()) {
	  if (!ublk->m_split_info->has_split_neighbor_info()) {
	    delete ublk->m_split_info;
	    ublk->m_split_info = nullptr;
	  } else {
	    ublk->m_split_info->m_split_advection_info = nullptr;
	  }
        } else {
          ublk->m_split_info->m_split_advection_info = nullptr;
        }
        //delete((void *) ublk_box_plus, 0, __FILE__, __LINE__);
        ublk->unset_advect_from_split_mask();
      }
    }
  }

  update_split_advect_factors();
}

VOID sUBLK_TABLE::append_split_neighbor_info() {

  if (!sim.is_particle_model)
    return;
  for (UBLK ublk: m_ublks) {
    if (ublk->is_mirror() || ublk->is_solid())
      continue;

    // ghost ublks may be split
    sSPLIT_NEIGHBOR_INFO *split_neighbor_info = ublk->split_neighbor_info();
    // update ublk instances across faces to simulator numbers from discretizer numbers
    if (split_neighbor_info)
      split_neighbor_info->update_split_ublk_instances_across_faces(ublk);

    if (ublk->m_are_any_neighbors_split.any() && (split_neighbor_info == nullptr)) {
      split_neighbor_info = ublk->allocate_split_neighbor_info();
      split_neighbor_info->init(ublk);
      // The ublk instances assigned in this function should be correct, since
      // they are computed from sas factors which contain updated instance
      // indices
      split_neighbor_info->update_face_connectivity(ublk);
      ublk->set_split_neighbor_info(split_neighbor_info);
    }
    if (split_neighbor_info) {
      // The ublk instances assigned in this function should be correct, since
      // they are computed from sas factors which contain updated instance
      // indices
      split_neighbor_info->set_face_single_ublk_instance(ublk);
    }
  }

  for (UBLK ublk: m_ublks) {
    if (ublk->is_mirror() || ublk->is_solid() || ublk->is_ghost())
      continue;

    sSPLIT_NEIGHBOR_INFO *split_neighbor_info = ublk->split_neighbor_info();
    if (split_neighbor_info)
      split_neighbor_info->update_edge_connectivity(ublk);
  }
}

#include "box_advect_gen_headers.h"

static VOID add_split_factors_for_tagged_neighbor_instances(UBLK ublk,
                                                           UBLK_OFFSET_CONTRIBUTIONS_MAP& neighbor_contributions,
                                                           UBLK_OFFSET_TUPLE ublk_offset,
                                                           TAGGED_UBLK neighbor_tagged_ublk,
                                                           SPLIT_ADVECT_INFO split_advect_info,
                                                           sSPLIT_ADVECT_FACTORS* split_advect_factors){

  sUBLK_VECTOR *split_ublks = neighbor_tagged_ublk.split_ublks();
  std::vector<sNEIGHBOR_CONTRIBUTION>& neighbor_contributions_for_offset = neighbor_contributions[ublk_offset];

  //In the advection code (see box_split_advect_d*.cc, and gather_advect.h),
  // we look at contributions for each latvec, and then loop over instances and process src-dst pairs
  //We must initialize the split factors in this order

  auto start_latvec_iterator = neighbor_contributions_for_offset.begin();
  auto end_latvec_iterator   = start_latvec_iterator;

  while (start_latvec_iterator != neighbor_contributions_for_offset.end()) {

    walk_neighbor_offset_contributions_to_next_latvec(start_latvec_iterator,
                                                      end_latvec_iterator,
                                                      neighbor_contributions_for_offset.end());

    auto start_latvec_iterator_tmp = start_latvec_iterator;

    for (asINT32 neighbor_instance_index = 0;
        neighbor_instance_index < split_ublks->num_split_ublks();
        neighbor_instance_index++) {

      TAGGED_UBLK tagged_neighbor = split_ublks->m_tagged_ublks[neighbor_instance_index];

      start_latvec_iterator = start_latvec_iterator_tmp;

      while ( start_latvec_iterator != end_latvec_iterator ){

        const sNEIGHBOR_CONTRIBUTION& src_dst_lat = *start_latvec_iterator; start_latvec_iterator++;

        asINT32 latvec    = src_dst_lat.latvec; //This is from source to destination
        asINT32 parity    = state_parity(latvec);
        asINT32 src_voxel = src_dst_lat.src_voxel;
        asINT32 dst_voxel = src_dst_lat.dst_voxel;

        sTAGGED_SPLIT_FACTOR tagged_split_factors[MAX_SPLIT_UBLKS];

        asINT32 n_split_ublks = 0;

        if ( ublk->fluid_like_voxel_mask.test(dst_voxel) && ublk->voxel_fluid_connect_mask(dst_voxel).test(parity)) {
          //We use parity in tagged_neighbor_from_split_ublk_site, because it expects latvec
          //from destination to source.
          n_split_ublks = tagged_neighbor_from_split_ublk_site(neighbor_tagged_ublk,
                                                               ublk->advect_from_split_mask(),
                                                               dst_voxel, parity,
                                                               split_advect_info,
                                                               ublk->m_split_tagged_instance.get(),
                                                               tagged_split_factors);
        }

        BOOLEAN tagged_instance_contributes = FALSE;

        ccDOTIMES(split_ublk_index,n_split_ublks) {
          if (tagged_neighbor.ublk() == tagged_split_factors[split_ublk_index].tagged_neighbor.ublk()){
              tagged_instance_contributes = TRUE;
              sFLOAT nbr_split_factor = tagged_split_factors[split_ublk_index].neighbor_split_factor;
              if (ublk->is_conduction_solid()) {
                nbr_split_factor = MAX(nbr_split_factor, std::numeric_limits<sFLOAT>::min());
              }
#if !DEBUG_SPLIT_ADVECT_FACTORS
              split_advect_factors->get<SPEED1>().add(nbr_split_factor);
#else

              split_advect_factors->get<SPEED1>().add(neighbor_instance_index,
                                                    latvec,
                                                    src_voxel,
                                                    dst_voxel,
                                                    nbr_split_factor);
#endif
          }
        }

        if(!tagged_instance_contributes){
#if !DEBUG_SPLIT_ADVECT_FACTORS
          split_advect_factors->get<SPEED1>().add(0.0F);
#else
          split_advect_factors->get<SPEED1>().add(neighbor_instance_index,
                                                latvec,
                                                src_voxel,
                                                dst_voxel,
                                                0.0F);
#endif
        }
      }
    }
  }
}

static VOID  add_speed2_split_factors_for_ublk_neighbor(UBLK ublk,
                                                        TAGGED_UBLK neighbor_tagged_ublk,
                                                        asINT32 latvec_2,
                                                        UBLK_BOX ublk_box,
                                                        SPLIT_ADVECT_INFO split_advect_info,
                                                        sSPLIT_ADVECT_FACTORS* split_advect_factors){

  sTAGGED_SPLIT_FACTOR split_factors_2[MAX_SPLIT_UBLKS];

  sUBLK_VECTOR *split_ublks = neighbor_tagged_ublk.split_ublks();

  for (asINT32 neighbor_instance_index = 0;
      neighbor_instance_index < split_ublks->num_split_ublks();
      neighbor_instance_index++) {

    TAGGED_UBLK tagged_neighbor_instance = split_ublks->m_tagged_ublks[neighbor_instance_index];

    //We're going to write split factors for all voxels, the client is responsible for reading
    //all of them in order.
    ccDOTIMES(dst_voxel,ubFLOAT::N_VOXELS) {
      asINT32 n_splits_2 = 0;

      if ( ublk->fluid_like_voxel_mask.test(dst_voxel) && ublk->voxel_fluid_connect_mask(dst_voxel).test(latvec_2)) {
        n_splits_2 = tagged_neighbor_from_split_ublk_site(neighbor_tagged_ublk,
                                                          ublk->advect_from_split_mask(),
                                                          dst_voxel,
                                                          latvec_2,
                                                          split_advect_info,
                                                          ublk->m_split_tagged_instance.get(),
                                                          split_factors_2);
      }

      BOOLEAN tagged_instance_contributes = FALSE;
      ccDOTIMES(split_ublk_index, n_splits_2){
        if (tagged_neighbor_instance.ublk() == split_factors_2[split_ublk_index].tagged_neighbor.ublk()){
             tagged_instance_contributes = TRUE;
#if DEBUG_SPLIT_ADVECT_FACTORS
             split_advect_factors->get<SPEED2>().add(neighbor_instance_index,
                                                     latvec_2,
                                                     dst_voxel, //src and dst same
                                                     dst_voxel,
                                                     split_factors_2[split_ublk_index].neighbor_split_factor);
#else
             split_advect_factors->get<SPEED2>().add(split_factors_2[split_ublk_index].neighbor_split_factor);
#endif
         }
      }

      if(!tagged_instance_contributes){
#if DEBUG_SPLIT_ADVECT_FACTORS
        split_advect_factors->get<SPEED2>().add(neighbor_instance_index,
                                                latvec_2,
                                                dst_voxel, //src and dst same
                                                dst_voxel,
                                                0.0F);
#else
        split_advect_factors->get<SPEED2>().add(0.0F);
#endif
      }
    }
  }
}

static VOID  add_speed2_split_factors_for_ublk(UBLK ublk,
                                               UBLK_BOX ublk_box,
                                               SPLIT_ADVECT_INFO split_advect_info,
                                               sSPLIT_ADVECT_FACTORS* split_advect_factors){

  asINT32 start_index = 0;

  auto& box_access = ublk->box_access();

  for ( asINT32 axis = 0 ; axis < sim.num_dims; axis++ ) {

    TAGGED_UBLK forward_neighbor = box_access.forward_neighbor(axis);
    TAGGED_UBLK backward_neighbor = box_access.backward_neighbor(axis);

    if (forward_neighbor.is_ublk() && forward_neighbor.is_ublk_split()) {

      split_advect_factors->get<SPEED2>().set_start_index_for_latvec(face_index(axis,1),start_index);

      add_speed2_split_factors_for_ublk_neighbor(ublk, forward_neighbor,
                                                 SPEED_1_TO_SPEED_2(STATE_P(axis)),
                                                 ublk_box, split_advect_info,
                                                 split_advect_factors);

      start_index += ubFLOAT::N_VOXELS * forward_neighbor.split_ublks()->num_split_ublks();

    }

    if (backward_neighbor.is_ublk() && backward_neighbor.is_ublk_split()) {

      split_advect_factors->get<SPEED2>().set_start_index_for_latvec(face_index(axis,-1),start_index);

      add_speed2_split_factors_for_ublk_neighbor(ublk, backward_neighbor,
                                                 SPEED_1_TO_SPEED_2(STATE_N(axis)),
                                                 ublk_box, split_advect_info,
                                                 split_advect_factors);

      start_index += ubFLOAT::N_VOXELS * backward_neighbor.split_ublks()->num_split_ublks();
    }

  }
}

static VOID add_split_factors_for_tagged_neighbors(UBLK ublk,
                                                   UBLK_OFFSET_CONTRIBUTIONS_MAP& neighbor_contributions){

  auto& box_access = ublk->m_box_access;
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();
  sSPLIT_ADVECT_FACTORS* split_advect_factors = ublk->get_split_advect_factors();  

  sINT16 factor_start_index = 0;
  asINT32 state_direction = 0;

  //We start with the non moving state, and work our way in
  //ascending order of moving states, hence the awkward initialization
  //of loop variable i
  for ( int i = -1; i < N_MOVING_STATES; i++) {

    UBLK_OFFSET_TUPLE ublk_offset;

    if ( i == -1 ) { //Non-moving states
      ublk_offset = UBLK_OFFSET_TUPLE(0, 0, 0);
      state_direction = V_0_0_0;
    } else {
      ublk_offset = UBLK_OFFSET_TUPLE(state_vx(i), state_vy(i), state_vz(i));
      state_direction = i;
    }

    //We can loop over all lattice directions, but not all offsets are keys
    //for example in 2D, not all ublk offsets are valid
    if (neighbor_contributions.count(ublk_offset)) {

      TAGGED_UBLK neighbor_tagged_ublk = box_access.neighbor_ublk(ublk_offset);

      if ( neighbor_tagged_ublk.is_ublk_scale_interface() ||
           (!neighbor_tagged_ublk.is_ublk()) ||
          (!neighbor_tagged_ublk.is_ublk_split())) {
        continue;
      };

      split_advect_factors->get<SPEED1>().set_start_index_for_latvec(state_direction,factor_start_index);

      factor_start_index +=
          neighbor_tagged_ublk.split_ublks()->num_split_ublks() * neighbor_contributions[ublk_offset].size();


      add_split_factors_for_tagged_neighbor_instances(ublk,neighbor_contributions,
                                                     ublk_offset,neighbor_tagged_ublk,
                                                     split_advect_info,split_advect_factors);

    }

  }

  add_speed2_split_factors_for_ublk(ublk, ublk->get_ublk_box(),
                                    split_advect_info,split_advect_factors);

}


VOID sUBLK_TABLE::update_split_advect_factors(){

  /*Make sure physics and simeng are compiled with the same definitions of
   *sSPLIT_ADVECT_FACTOR class
   *All through the assert makes this appear as a run time check, this is
   *a link time check as well. The global variables are NOT DEFINED in physics
   *if it was compiled in a different mode.*/
#if DEBUG_SPLIT_ADVECT_FACTORS
  extern volatile sSPLIT_ADVECT_FACTOR_COMPILE_MODE g_split_factor_link_time_consistency_check_debug;
  assert( g_split_factor_link_time_consistency_check_debug == sSPLIT_ADVECT_FACTOR_COMPILE_MODE::debug);
#else
  extern volatile sSPLIT_ADVECT_FACTOR_COMPILE_MODE g_split_factor_link_time_consistency_check_release;
  assert( g_split_factor_link_time_consistency_check_release == sSPLIT_ADVECT_FACTOR_COMPILE_MODE::release);
#endif
  
  //Initialize advection map
  UBLK_OFFSET_CONTRIBUTIONS_MAP neighbor_contributions;

#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
  compute_ublk_neighbor_contributions(neighbor_contributions,
                                      sim.is_2d(),
                                      STP_LATTICE_D19);

#elif BUILD_D39_LATTICE
  compute_ublk_neighbor_contributions(neighbor_contributions,
                                      sim.is_2d(),
                                      STP_LATTICE_D39);
#endif

  for (UBLK ublk: m_ublks) {

    if (ublk->fluid_like_voxel_mask.none() || ublk->is_mirror() || (ublk->is_ghost() && !ublk->is_vr_coarse())) {
      continue;
    }

    add_split_factors_for_tagged_neighbors(ublk,neighbor_contributions);

  }
}

static asINT32 limit_dsm(sFLOAT &mult) 
{
  if ( mult < -3 ) {
    //mult = 0;
    return 1;
  }
  else if ( mult > 3 ) {
    //mult = 1;
    return 1;
  }
  else {
    return 0;
  }
}


VOID sUBLK_TABLE::set_dynamics_scalar_multipliers()
{
  sINT64 i_ublk = 0;
  sFLOAT *mult = multipliers;
  BOOLEAN is_per_voxel = *mult++; // the first element is 1 if the dsms are per voxel, 0 otherwise
  asINT32 dsm_meas_window_index = *mult++; // the second element is the measurement window index;
  asINT32 n_limited_dsms = 0;
  asINT32 first_limited_ublk = -1;

  for (UBLK ublk: m_ublks) {
    if (!ublk->is_ghost() && ublk->fluid_like_voxel_mask.any()) {
      if (ublk->fluid_like_voxel_mask != ublk->basic_fluid_voxel_mask)
        msg_internal_error("5G SOLVER DOES NOT SUPPORT POROSITY");

      asINT32 n_meas_windows = ublk->dynamics_data()->n_meas_windows();
      UBLK_MEAS_CELL_PTR ublk_meas_cell_ptr = ublk->dynamics_data()->meas_cell_ptrs();
      VOXEL_MASK_8 ublk_meas_cell_voxel_mask{0};

      // find the correct measurement window in the ublk and pull out voxel mask
      // If the ublk is not in the measurement window or voxel mask == 0, skip it
      ccDOTIMES(win,n_meas_windows) {
        if (ublk_meas_cell_ptr[win].window_index() == dsm_meas_window_index) {
          ublk_meas_cell_voxel_mask = ublk_meas_cell_ptr[win].voxel_mask();
          break;
        }
      }
      if (ublk_meas_cell_voxel_mask.none()) {
        continue; // this ublk is not in the dsm meas window, skip it
      }

      if (is_per_voxel) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          if (ublk_meas_cell_voxel_mask.test(voxel)) {
            n_limited_dsms += limit_dsm(*mult);
            ublk->mc_data()->dynamic_scalar_multiplier[voxel] = *mult++;
          }
        }
      } 
      else {
        n_limited_dsms += limit_dsm(*mult);
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          ublk->mc_data()->dynamic_scalar_multiplier[voxel] = *mult;
        }
        mult++;
      }
      i_ublk++;

      if (first_limited_ublk < 0 && n_limited_dsms > 0 ) {
        first_limited_ublk = ublk->id();
      }
    }
  }
  // This method is called only by the STP_FLOW_REALM table
  if (i_ublk > sim.n_nonghost_ublks[STP_FLOW_REALM]) {
    msg_internal_error("Number of ublks %ld in the dsm measurement window is greater than the number of non ghost ublks %d",
                       i_ublk, sim.n_nonghost_ublks[STP_FLOW_REALM]);
  }

  if (n_limited_dsms > 0) {
    msg_warn("%d DSMs were limited to range [0,1]. First limited ublk: %d", n_limited_dsms, first_limited_ublk);
  }
  dsm_ready = FALSE;
}


//REALM_ASSUMPTION: Ublk is FLOW
VOID print_voxel_states(const char *print_s,
                        int ublk_id,
                        int voxel_id,
                        int timestep,
                        int set,
                        std::ostream& os)
{
  const UBLK prblk = ublk_from_id(ublk_id);
  if ( prblk ){
    tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,print_s,voxel_id,timestep,set);
    prblk->print_ublk_content(os,opts);
  }
}

VOID init_space_varying_dynamics_attributes(UBLK ublk);

VOID sUBLK_TABLE::complete_ublk_space_varying_attribute_initialization() {

  for (UBLK ublk: m_ublks) {

    //Skip ublks that don't have dynamics data
    if (ublk->has_no_dynamics_data()) {
      continue;
    }   

    init_space_varying_dynamics_attributes(ublk);
  }
}
