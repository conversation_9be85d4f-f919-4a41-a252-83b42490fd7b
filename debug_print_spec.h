#ifndef PRINT_SPEC_H
#define PRINT_SPEC_H
#include <iomanip>
#include <string>
#include <iostream>
#include <cassert>

 namespace PRINT_OPTS {

  typedef unsigned long int PRINT_MASK;

   //Format specifiers
   //Made static const for now, which requires rebuilding
   //for every change. This can be changed based on
   //necessity

   //Horizontal Spacing
   const char *const HS = " ";

   //Vertical Spacing
   const char *const VS = "\n";

   //Twice vertical spacing
   const char *const V2S = "\n\n";

   //Equality Symbol
   const char *const ES = " = ";

   //Fill Character - when the number does not fill the stream width
   const char        FC = ' ';

   //MAIN HEADER
   const char *const HL2 = "********************************************************************************\n";

   //SECONDARY HEADER
   const char *const HL = "--------------------------------------------------------------------------------\n";

   //Width of number output
#if BUILD_DOUBLE_PRECISION
   const unsigned int STREAM_WIDTH = 21;
#else
   const unsigned int STREAM_WIDTH = 14;
#endif

   //Precision of number printed
#if BUILD_DOUBLE_PRECISION
   const unsigned int STREAM_PREC  = 14;
#else
   const unsigned int STREAM_PREC  = 7;
#endif

   //Number of array elements printed in a line
   const unsigned int FLOATS_IN_A_LINE = 6;
 };

// tUBLK_PRINT_OPTS
// Class to customize UBLK printing
 struct tUBLK_PRINT_OPTS {
  //Different levels of print verbosity
  enum UBLK_PRINT_MASK {
    LB_DATA,
    LB_STATES_DATA,
    STATES_DATA,
    TURB_DATA,
    T_DATA,
    UDS_DATA,
    PF_DATA,
    MC_DATA,
    SURF_GEOM_DATA,
    SURF_LB_DATA,
    SURF_TURB_DATA,
    SURF_T_DATA,
    SURF_UDS_DATA,
    SURF_PF_DATA,
    SURF_MC_DATA,
    VR_DATA,
    MIRROR_DATA,
    DYN_DATA,
    IB_BF_DATA,
    ALL_DATA,
  };

 public:

  typedef PRINT_OPTS::PRINT_MASK PRINT_MASK;

  const static PRINT_MASK LB_DATA_PRINT_MASK = (0x1 << LB_DATA);
  const static PRINT_MASK LB_STATES_DATA_PRINT_MASK = (0x1 << LB_STATES_DATA);
  const static PRINT_MASK STATES_DATA_PRINT_MASK = (0x1 << STATES_DATA);
  const static PRINT_MASK TURB_DATA_PRINT_MASK = (0x1 << TURB_DATA);
  const static PRINT_MASK T_DATA_PRINT_MASK = (0x1 << T_DATA);
  const static PRINT_MASK UDS_DATA_PRINT_MASK = (0x1 << UDS_DATA);
  const static PRINT_MASK PF_DATA_PRINT_MASK = (0x1 << PF_DATA); 
  const static PRINT_MASK MC_DATA_PRINT_MASK = (0x1 << MC_DATA);
  const static PRINT_MASK SURF_GEOM_DATA_PRINT_MASK = (0x1 << SURF_GEOM_DATA);
  const static PRINT_MASK SURF_LB_DATA_PRINT_MASK = (0x1 << SURF_LB_DATA);
  const static PRINT_MASK SURF_TURB_DATA_PRINT_MASK = (0x1 << SURF_TURB_DATA);
  const static PRINT_MASK SURF_T_DATA_PRINT_MASK = (0x1 << SURF_T_DATA);
  const static PRINT_MASK SURF_UDS_DATA_PRINT_MASK = (0x1 << SURF_UDS_DATA);
  const static PRINT_MASK SURF_PF_DATA_PRINT_MASK = (0x1 << SURF_PF_DATA); 
  const static PRINT_MASK SURF_MC_DATA_PRINT_MASK = (0x1 << SURF_MC_DATA);
  const static PRINT_MASK VR_DATA_PRINT_MASK = (0x1 << VR_DATA);
  const static PRINT_MASK MIRROR_DATA_PRINT_MASK = (0x1 << MIRROR_DATA);
  const static PRINT_MASK DYN_DATA_PRINT_MASK = (0x1 << DYN_DATA);
  const static PRINT_MASK IB_BF_PRINT_MASK = (0x1 << IB_BF_DATA);
  const static PRINT_MASK ALL_DATA_PRINT_MASK = (0x1 << ALL_DATA ) - 1;


  // === Methods ====
  //Default constructor
  tUBLK_PRINT_OPTS():
    data_print_mask(ALL_DATA_PRINT_MASK),
    user_prefix_msg(""),
    voxel_id(-1),
    timestep(0),
    set(-1)
  {

  }

  tUBLK_PRINT_OPTS(PRINT_MASK mask,
                  const std::string& msg,
                  int v_id,
                  int tstep,
                  int set):
    data_print_mask(mask),
    user_prefix_msg(msg),
    voxel_id(v_id),
    timestep(tstep),
    set(set)
  {
  }

  //Before printing calls are dispatched to
  //Ublk data blocks, the stream is initialized
  //with this method
  static void init_stream(std::ostream& os) {
    os << std::setfill(PRINT_OPTS::FC)
       << std::scientific
       << std::setprecision(PRINT_OPTS::STREAM_PREC)
       << std::boolalpha;
  }

   template<typename CENTROIDS_TYPE>
  void print_ublk_header(std::ostream& os,
                        asINT32 print_voxel,
                        uINT32 id,
                        uINT32 timestep,
                        asINT32 scale,
                        bool is_ghost,
                        bool has_two_copies,
                        asINT32 time_index,
                        CENTROIDS_TYPE& centroids
                        ) const {
    //Salutation
    os  << PRINT_OPTS::HL2
        << "User prefix msg : " << user_prefix_msg << PRINT_OPTS::VS
        << "UBLK_ID : " << id << PRINT_OPTS::HS
        << "VOXEL_ID : " << print_voxel << PRINT_OPTS::HS
        << "TIME_STEP : " << timestep << PRINT_OPTS::VS
        << "scale : " << scale << PRINT_OPTS::HS
        << "is_ghost? : " << is_ghost << PRINT_OPTS::VS
        << "has_two_copies? : " << has_two_copies << PRINT_OPTS::VS
        << "current lb index : " << time_index << PRINT_OPTS::VS
        << "centroid : (" << centroids(print_voxel, 0) << ", "
                          << centroids(print_voxel, 1) << ", "
                          << centroids(print_voxel, 2) << ")" 
                          << PRINT_OPTS::VS
        << PRINT_OPTS::HL2 << PRINT_OPTS::VS;
  }

  void print_ublk_footer(std::ostream& os,
                                asINT32 print_voxel,
                                uINT32 id,
                                uINT32 timestep) const{
    os << PRINT_OPTS::HL2
       << "End of print : " << user_prefix_msg << PRINT_OPTS::VS
       << "UBLK_ID : " << id << PRINT_OPTS::HS
       << "VOXEL_ID : " << print_voxel << PRINT_OPTS::HS
       << "TIME_STEP : " << timestep << PRINT_OPTS::VS
       << PRINT_OPTS::HL2
       << PRINT_OPTS::VS;
  }

  // ==== Data Members===
  //Default behavior is to print everything
  PRINT_MASK data_print_mask;

  //For debugging purposes, developer can add a message
  //as a prefix
  std::string user_prefix_msg;

  //
  int voxel_id;
  int timestep;
  int set;
};


 // ============================================================================
 // SURFEL_PRINT_OPTS
 // Class to customize surfel printing
 // ============================================================================
template<typename SFL_TYPE_TAG> struct tSURFEL_V2S_DATA;
using sSURFEL_V2S_DATA = tSURFEL_V2S_DATA<SFL_SDFLOAT_TYPE_TAG>;

 typedef struct SURFEL_PRINT_OPTS {


   //Different levels of print verbosity
   enum SURFEL_PRINT_MASK {
     LB_DATA,
     TURB_DATA,
     OUTFLUX_DATA,
     T_DATA,
     UDS_DATA,
     PF_DATA,
     MC_DATA,
     OUTFLUX_T_DATA,
     LRF_DATA,
     MLRF_DATA,
     EVEN_ODD_DATA,
     ISURFEL_DATA,
     S2S_ADVECT_DATA,
     MIRROR_DATA,
     V2S_LB_DATA,
     V2S_TURB_DATA,
     V2S_T_DATA,
     V2S_UDS_DATA,
     V2S_PF_DATA,
     DYN_DATA,
     ALL_DATA,
   };

   typedef PRINT_OPTS::PRINT_MASK PRINT_MASK;

   const static PRINT_MASK LB_DATA_PRINT_MASK = (0x1 << LB_DATA);
   const static PRINT_MASK TURB_DATA_PRINT_MASK = (0x1 << TURB_DATA);
   const static PRINT_MASK OUTFLUX_DATA_PRINT_MASK = (0x1 << OUTFLUX_DATA);
   const static PRINT_MASK T_DATA_PRINT_MASK = (0x1 << T_DATA);
   const static PRINT_MASK UDS_DATA_PRINT_MASK = (0x1 << UDS_DATA);
   const static PRINT_MASK PF_DATA_PRINT_MASK = (0x1 << PF_DATA);
   const static PRINT_MASK MC_DATA_PRINT_MASK = (0x1 << MC_DATA);
   const static PRINT_MASK LRF_DATA_PRINT_MASK = (0x1 << LRF_DATA);
   const static PRINT_MASK MLRF_DATA_PRINT_MASK = (0x1 << MLRF_DATA);
   const static PRINT_MASK EVEN_ODD_DATA_PRINT_MASK = (0x1 << EVEN_ODD_DATA);
   const static PRINT_MASK ISURFEL_DATA_PRINT_MASK = (0x1 << ISURFEL_DATA);
   const static PRINT_MASK S2S_ADVECT_DATA_PRINT_MASK = (0x1 << S2S_ADVECT_DATA);
   const static PRINT_MASK MIRROR_DATA_PRINT_MASK = (0x1 << MIRROR_DATA);
   const static PRINT_MASK V2S_LB_DATA_PRINT_MASK = (0x1 << V2S_LB_DATA);
   const static PRINT_MASK V2S_TURB_DATA_PRINT_MASK = (0x1 << V2S_TURB_DATA);
   const static PRINT_MASK V2S_T_DATA_PRINT_MASK = (0x1 << V2S_T_DATA);
   const static PRINT_MASK V2S_UDS_DATA_PRINT_MASK = (0x1 << V2S_UDS_DATA);
   const static PRINT_MASK V2S_PF_DATA_PRINT_MASK = (0x1 << V2S_PF_DATA);
   const static PRINT_MASK DYN_DATA_PRINT_MASK = (0x1 << DYN_DATA);
   const static PRINT_MASK ALL_DATA_PRINT_MASK = (0x1 << ALL_DATA ) - 1;

   // === Methods ====
   //Default constructor
   SURFEL_PRINT_OPTS():
     data_print_mask(ALL_DATA_PRINT_MASK),
     user_prefix_msg(""),
     timestep(0),
     s_v2s_data(nullptr)
   {

   }

   SURFEL_PRINT_OPTS(PRINT_MASK mask,
                    const std::string& msg,
                    int tstep,
                    sSURFEL_V2S_DATA* s_v2s_data):
     data_print_mask(mask),
     user_prefix_msg(msg),
     timestep(tstep),
     s_v2s_data(s_v2s_data)
   {
   }

   //Before printing calls are dispatched to
   //Ublk data blocks, the stream is initialized
   //with this method
   static void init_stream(std::ostream& os) {
     os << std::setfill(PRINT_OPTS::FC)
        << std::scientific
        << std::setprecision(PRINT_OPTS::STREAM_PREC)
        << std::boolalpha;
   }

   void print_surfel_header(std::ostream& os,
                            uINT32 id,
                            uINT32 timestep,
                            asINT32 scale,
                            bool   is_ghost
                         ) const{
     //Salutation
     os  << PRINT_OPTS::HL2
         << "User prefix msg : " << user_prefix_msg << PRINT_OPTS::VS
         << "SURFEL_ID : " << id << PRINT_OPTS::HS
         << "TIME_STEP : " << timestep << PRINT_OPTS::VS
         << "scale : " << scale << PRINT_OPTS::HS
         << "is_ghost? : " << is_ghost << PRINT_OPTS::VS
         << PRINT_OPTS::HL2;
   }

   static void print_surfel_footer(std::ostream& os,
                                 uINT32 id,
                                 uINT32 timestep){
     os << PRINT_OPTS::HL2
        << "End of print, "
        << "SURFEL_ID : " << id << PRINT_OPTS::HS
        << "TIME_STEP : " << timestep << PRINT_OPTS::VS
        << PRINT_OPTS::HL2
        << PRINT_OPTS::VS;
   }
   // ==== Data Members===
   //Default behavior is to print everything
   PRINT_MASK data_print_mask;

   //For debugging purposes, developer can add a message
   //as a prefix
   std::string user_prefix_msg;

   int timestep;

   sSURFEL_V2S_DATA* s_v2s_data;
 } SURFEL_PRINT_OPTS;

//MACRO that sets the output width to STREAM_WIDTH
//and left aligns the text
#define PSETWL std::setw(PRINT_OPTS::STREAM_WIDTH) << std::left

//MACRO that sets the output width to STREAM_WIDTH
//and right aligns the text
#define PSETWR std::setw(PRINT_OPTS::STREAM_WIDTH) << std::right

//Default use if left aligned
#define PSETW PSETWL

namespace UBLK_SURFEL_PRINT_UTILS {

inline  VOID sim_print_loop_header(std::ostream& os,
                           const char* name,
                           asINT32 t_step) {
 os << name << "_"<<t_step << PRINT_OPTS::VS;
}

 inline VOID sim_print_data_header(std::ostream& os,
                        const char* block_name) {
    os << PRINT_OPTS::HL
       << block_name << PRINT_OPTS::V2S;
 }

 inline VOID sim_print_vs(std::ostream& os) {
   os << PRINT_OPTS::VS;
 }

typedef struct loop_limits
{
  asINT32 start_index;
  asINT32 end_index;
  //Print within a range
  loop_limits(asINT32 start_, asINT32 end_) :
      start_index(start_), end_index(end_) {
  }
  ;
  //If constructed with one element, print at that index
  loop_limits(asINT32 start) :
      start_index(start), end_index(start) {
  }
  ;

  VOID validate(const asINT32 max_index) const {
    dassert((start_index >= 0) && (end_index < max_index));
  }

  size_t num_elements() const {
    return (end_index - start_index + 1);
  }
} loop_limits; //Reword to loop limits


 // ============================================================================
 // Print scalar
 // ============================================================================
 
/** iostream doesn't print uint8 correctly */
template<typename value_type>
struct uint8_fix_impl
{
  const value_type& m_val;
  uint8_fix_impl(const value_type& val) : m_val(val) {}
  friend std::ostream& operator << (std::ostream& os, const uint8_fix_impl& o)
  {
    os << o.m_val;
    return os;
  }
};

template<>
struct uint8_fix_impl<uINT8>
{
  const uINT8& m_val;
  uint8_fix_impl(const uINT8& val) : m_val(val) {}
  friend std::ostream& operator << (std::ostream& os, const uint8_fix_impl& o)
  {
    os << static_cast<uINT32>(o.m_val);
    return os;
  }
};

template<typename value_type>
uint8_fix_impl<value_type> uint8_fix(const value_type& val)
{
  return uint8_fix_impl<value_type>(val);
}

template<typename value_type>
 void sim_print(std::ostream& os,
                const std::string& name,
                value_type val)
 {
   os << name << " : "  << PSETW
       << uint8_fix(val) << PRINT_OPTS::VS;
 }


 // ============================================================================
 // Print a vector, wont compile unless template parameters match array size
 // Default behavior is to print all elements unless a specific loop_limit is input
 // ============================================================================
template<size_t L1, typename value_type>
 void sim_print(std::ostream& os,
                const std::string& name,
                value_type val[L1],
                const loop_limits& limits1 = loop_limits(0,L1-1))
 {
   limits1.validate(L1);
   os << name << " : ";
   if (limits1.num_elements() > PRINT_OPTS::FLOATS_IN_A_LINE) os << PRINT_OPTS::VS;

   for (int i = limits1.start_index; i < limits1.end_index + 1; i++){
     os << PSETW << uint8_fix(val[i]) << PRINT_OPTS::HS;
     if ( (i+1)%PRINT_OPTS::FLOATS_IN_A_LINE ==0 ) {
       os << PRINT_OPTS::VS;
     }
   }
   os << PRINT_OPTS::VS;
 }

 // ============================================================================
 // Print a 2d array, wont compile unless template parameters match array size
 // Default behavior is to print all elements unless a specific indices are input
 // ============================================================================
template<size_t L1, size_t L2, typename value_type>
 void sim_print(std::ostream& os,
                const std::string& name,
                value_type val[L1][L2],
                const loop_limits& limits1 = loop_limits(0, L1-1),
                const loop_limits& limits2 = loop_limits(0, L2-1))
 {
   limits1.validate(L1);
   limits2.validate(L2);
   size_t n_elements = limits1.num_elements() * limits2.num_elements();
   os << name << " : ";
   if (n_elements > PRINT_OPTS::FLOATS_IN_A_LINE) os << PRINT_OPTS::VS;

   size_t count = 0;
   for (int i = limits1.start_index; i < limits1.end_index + 1; i++){
     for (int j = limits2.start_index; j < limits2.end_index + 1; j++){
       os << PSETW << uint8_fix(val[i][j]) << PRINT_OPTS::HS;
       if ( (1+count)%PRINT_OPTS::FLOATS_IN_A_LINE == 0 ) {
         os << PRINT_OPTS::VS;
       }
       count++;
     }
   }
   os << PRINT_OPTS::VS;
 }

 // ============================================================================
 // Print a 3d array, wont compile unless template parameters match array size
 // Default behavior is to print all elements unless a specific indices are input
 // ============================================================================
 template<size_t L1,size_t L2,size_t L3,typename value_type>
 void sim_print(std::ostream& os,
                const std::string& name,
                value_type val[L1][L2][L3],
                const loop_limits& limits1 = loop_limits(0,L1-1),
                const loop_limits& limits2 = loop_limits(0,L2-1),
                const loop_limits& limits3 = loop_limits(0,L3-1))
 {
   limits1.validate(L1);
   limits2.validate(L2);
   limits3.validate(L3);

   size_t n_elements = limits1.num_elements() * limits2.num_elements() * limits3.num_elements();
   os << name << " : ";
   if (n_elements > PRINT_OPTS::FLOATS_IN_A_LINE) os << PRINT_OPTS::VS;

   size_t count = 0;
   for (int i = limits1.start_index; i < limits1.end_index+1; i++){
     for (int j = limits2.start_index; j < limits2.end_index+1; j++){
       for (int k = limits3.start_index; k < limits3.end_index+1; k++){
         os << PSETW << uint8_fix(val[i][j][k]) << PRINT_OPTS::HS;
         if ( (1+count)%PRINT_OPTS::FLOATS_IN_A_LINE == 0 ) {
           os << PRINT_OPTS::VS;
         }
         count++;
       }
     }
   }
   os << PRINT_OPTS::VS;
 }
 }
#endif
