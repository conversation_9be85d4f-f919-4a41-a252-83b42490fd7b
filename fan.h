/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#ifndef __FAN_H
#define __FAN_H

#include "common_sp.h"

extern asINT32 n_global_fan_descs;

namespace FANS {
  enum {
    U_AXIAL,
    N_FINEST_VOXELS,
    U_TILDA,
    FACTOR,
    N_FAN_DATA
  };
}


typedef struct sFAN_DESCRIPTOR {
  sINT16 physics_descriptor_index;			// index in table
  sINT32 max_voxel_delta_t;	 // max timestep of any voxel in this fan
  dFLOAT n_finest_voxels;
  BASETIME time_of_next_avg;
  STP_PHYS_VARIABLE u_axial_avg;
  dFLOAT u_axial_accum;
  dFLOAT u_tilda_accum;
  dFLOAT factor_accum;

  dFLOAT send_buffer[FANS::N_FAN_DATA];

  STP_PROC max_participating_sp; // identifies SP to issue errors about avg axial vel
  cBOOLEAN is_initialized;

  dFLOAT origin[3];
  dFLOAT raxis[3];
  dFLOAT gaxis[3];

  // Parameters used solely for table based fans
  cFANTABLE *fantable;		// NULL if not table based fan
  STP_PHYS_VARIABLE axial_force;		// Based on u_axial_avg
  STP_PHYS_VARIABLE thrust;		// Based on u_axial_avg

  dFLOAT one_over_density_times_length; // 1 / (mean_density * fan_length)
  cBOOLEAN is_user_defined_tang_vel_model;
  STP_GEOM_VARIABLE one_over_fan_radius;
  STP_PHYS_VARIABLE density_radius_area; 		// mean_density * radius * area

  // Methods
  VOID initialize();
  VOID avg_u_axial();
} *FAN_DESCRIPTOR;

VOID filter_coord_sys_axes(cSTRING region_name, dFLOAT raxis[3], dFLOAT gaxis[3]);

FAN_DESCRIPTOR find_fan_desc(asINT32 fluid_physics_desc_index);

size_t ckpt_fan_descs_len();
VOID ckpt_fan_descs(sCKPT_BUFFER& pio_ckpt_buff);
VOID ckpt_fan_descs();
VOID read_ckpt_fan_descs();

#endif /* __FAN_H */
