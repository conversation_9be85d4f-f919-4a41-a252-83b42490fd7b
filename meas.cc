/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Measurement windows
 *
 * James Hoch, Exa Corporation
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "meas.h"
#include "sim.h"
#include "thread_run.h"
#include "voxel_dyn_sp.h"
#include "surfel_dyn_sp.h"
#include PHYSICS_H
#include <iostream>
#include <fstream>
#include "bsurfel_dyn_meas.h"
#include "strand_mgr.h"
#ifndef _EXA_HPMPI
#include "MPI_mapping.h"
#include "utilities.h"
#include "timer.h"
#endif

#if BUILD_GPU
#include "gpu_host_include.h"
#endif

using namespace std;

#include "trajectory_window.h"
#include "particle_meas_dcache.h"

#include TEARRAY_H

sMEAS_WINDOW_COLLECTION  g_meas_windows;
sMEAS_WINDOW_COMBINATIONS g_meas_window_combinations; //Maps an index to a set of window ids for each particular combination of window a ublk might be in.


/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/

// Meas windows must be added in index order...
MEAS_WINDOW sMEAS_WINDOW_COLLECTION::add_meas_window(cDGF_MEAS_WINDOW *dgf_window)
{
  BOOLEAN is_particle_trajectory_window = dgf_window->meas_type == LGI_TRAJECTORY_WINDOW;
  MEAS_WINDOW window = nullptr;
  if (is_particle_trajectory_window) {

    window = cnew sTRAJECTORY_WINDOW(m_n_meas_windows++, dgf_window, "name");
    m_meas_window_array.push_back(window);
    //*(m_meas_window_array.append()) = window;
    g_trajectory_windows.push_back((TRAJECTORY_WINDOW)window);
    g_has_trajectory_windows |= TRUE;

  } else {

    window = cnew sMEAS_WINDOW();
    m_meas_window_array.push_back(window);
    window->meas_window_type =    dgf_window->meas_type;
    window->is_average_mme      =    (window->meas_window_type == LGI_AVERAGE_MME_WINDOW);
    window->meas_cell_scale  =    dgf_window->meas_cell_scale;
    window->is_composite     =    (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_COMPOSITE) != 0;
    window->is_development   =    (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_IS_DEVELOPMENT) != 0;
    window->per_voxel_meas_p =    !window->is_composite && (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_PER_VOXEL);
    window->n_global_stationary_meas_cells =  window->is_development ? (dgf_window->num_meas_cells - dgf_window->num_moving_meas_cells) :
					      (dgf_window->num_measurements - dgf_window->num_moving_meas_cells);
    window->n_global_moving_meas_cells     =    dgf_window->num_moving_meas_cells;
    window->index               =    m_n_meas_windows++;
    window->m_is_particle_trajectory_window = FALSE;
    window->m_per_emitter_particle_measurements = (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_PER_EMITTER) !=0;
    window->m_per_material_particle_measurements = (dgf_window->meas_window_flags & DGF_MEAS_WINDOW_PER_MATERIAL) !=0;
    if(window->m_per_emitter_particle_measurements  && window->m_per_material_particle_measurements ) {
      msg_error("Window %d requires both per-material and per-emitter particle measurements.", window->index);
    }
    if (!g_no_reserve_addr_space)
      window->m_meas_cell_index_map.reserve(4 * ssize_t(window->n_global_stationary_meas_cells) / total_sps); // reasonable guess

    if ( window->is_development ) {
      ccDOTIMES(axis,3) {
        if (window->meas_window_type == LGI_SURFACE_WINDOW) {
          window->entity_n_segments[axis] = xnew asINT32[sim.control_record.num_cdi_faces];
          window->entity_first_segment[axis] = xnew asINT32[sim.control_record.num_cdi_faces];
          window->entity_first_meas_cell[axis] = xnew asINT32[sim.control_record.num_cdi_faces];
        } else if ( (window->meas_window_type == LGI_FLUID_WINDOW) ||  (window->meas_window_type == LGI_POROUS_WINDOW)) {
          window->entity_n_segments[axis] = xnew asINT32[sim.control_record.num_cdi_regions];
          window->entity_first_segment[axis] = xnew asINT32[sim.control_record.num_cdi_regions];
          window->entity_first_meas_cell[axis] = xnew asINT32[sim.control_record.num_cdi_regions];
        } else {
          msg_error("Incorrect meas_window_type.");
        }
      }
    }

  }
  return window;
}


VOID initialize_meas_reduction_trees()
{
  MPI_Datatype mpi_type;
  sCP_TO_TREE_NODE_INIT_MSG msg;
  msg.mpi_type_init(&mpi_type);
  MPI_Status eMPI_status;

  DO_MEAS_WINDOWS(window) {
    if (window->is_average_mme)
      continue;
    if (window->n_stationary_meas_cells > 0) {
      if (window->use_tree_reduction()) {
        int meas_tag = make_mpi_tag<eMPI_MSG::MWIN>(window->index);
        RECV_EXA_SIM_MSG<sCP_TO_TREE_NODE_INIT_MSG, 1> init_msg(meas_tag, total_sps);
        g_exa_sp_cp_comm.recv(init_msg.mpi_msg);
        msg = *init_msg.return_buffer();
        window->m_parent_rank = msg.parent_rank;
        window->m_n_child_sps = msg.n_children;
        ccDOTIMES(child, msg.n_children) {
          window->m_child_sps[child] = msg.child_rank[child];
        }
      }
      // If this SP has no cells, the CP did not include it in the reduction tree
      window->setup_tree_reduction();
      window->allocate_send_buffer();
    }
  }

  MPI_Type_free(&mpi_type);
}

asINT32              g_n_time_varying_lrfs = 0;
MEAS_WINDOW_LRF_INFO g_time_varying_lrfs; // used by first meas window that needs this vector; a copy is made for others
static sriLRF_INDEX         *g_time_varying_lrf_physics_desc_indices;

// Returns TRUE if this call allocated g_time_varying_lrfs
static BOOLEAN find_time_varying_lrfs()
{
  static cBOOLEAN done = FALSE;
  if (done)
    return FALSE;
  done = TRUE;

  LRF_PHYSICS_DESCRIPTOR lrf_physics_desc = sim.lrf_physics_descs;
  ccDOTIMES(i, sim.n_lrf_physics_descs) {
    if (lrf_physics_desc->is_angular_vel_time_varying ||
        lrf_physics_desc->is_rotational_dynamics_on) {
      g_n_time_varying_lrfs++;
      lrf_physics_desc->is_active = TRUE;
    }
    lrf_physics_desc++;
  }

  if (g_n_time_varying_lrfs > 0) {
    g_time_varying_lrfs = xnew sMEAS_WINDOW_LRF_INFO [g_n_time_varying_lrfs];
    g_time_varying_lrf_physics_desc_indices = xnew sriLRF_INDEX [g_n_time_varying_lrfs];
    MEAS_WINDOW_LRF_INFO lrf = g_time_varying_lrfs;
    sriLRF_INDEX *lrf_physics_desc_index = g_time_varying_lrf_physics_desc_indices;
    LRF_PHYSICS_DESCRIPTOR lrf_physics_desc = sim.lrf_physics_descs;
    ccDOTIMES(i, sim.n_lrf_physics_descs) {
      if (lrf_physics_desc->is_angular_vel_time_varying ||
          lrf_physics_desc->is_rotational_dynamics_on) {
        *lrf_physics_desc_index++ = i;
        lrf++;
      }
      lrf_physics_desc++;
    }
  }
  return TRUE;
}

asINT32 g_n_time_varying_rotating_bcs   = 0;
asINT32 g_n_time_varying_sliding_bcs    = 0;
asINT32 g_n_time_varying_mbcs;

static MEAS_WINDOW_ROTATING_BC_INFO   g_time_varying_rotating_bcs; // used by first meas window; a copy is made for others
static MEAS_WINDOW_SLIDING_BC_INFO    g_time_varying_sliding_bcs;  // used by first meas window; a copy is made for others

static PHYSICS_DESCRIPTOR *g_time_varying_rotating_bc_pds;
static PHYSICS_DESCRIPTOR *g_time_varying_sliding_bc_pds;

template<typename PHYSICS_DESCRIPTOR_TYPE>
static bool is_angular_physics_desc_time_varying(PHYSICS_DESCRIPTOR_TYPE pd)
{
  if (sim.n_lrf_physics_descs > 0 && pd->parameters()->angular_spec.with_respect_to_ref_frame.value) {
    auto lrf = &sim.lrf_physics_descs[(asINT32)pd->parameters()->ref_frame.value];
    return lrf->is_angular_vel_time_varying;
  } else {
      return pd->parameters()->angular_spec.angular_velocity.is_time_varying && !pd->parameters()->angular_spec.angular_velocity.is_space_varying;
  }
}

static BOOLEAN find_time_varying_moving_bcs()
{
  static cBOOLEAN done = FALSE;
  if (done)
    return FALSE;
  done = TRUE;

  sINT32 n_time_varying_rotating_slip_bcs   = 0;
  sINT32 n_time_varying_rotating_noslip_bcs = 0;
  sINT32 n_time_varying_sliding_slip_bcs    = 0;
  sINT32 n_time_varying_sliding_noslip_bcs  = 0;

  PHYSICS_DESCRIPTOR surface_physics_desc = sim.flow_surface_physics_descs;
  ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
    switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
    case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
      ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
      // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
      if (is_angular_physics_desc_time_varying(pd))
        n_time_varying_rotating_slip_bcs++;
      break;
    }
    case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
      ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
      ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
      // PowerVIZ does not support space-varying angular velocity (which probably makes no sense anyway)
      if (is_angular_physics_desc_time_varying(pd))
        n_time_varying_rotating_noslip_bcs++;
      break;
    }
    case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
      LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
      // PowerVIZ does not support space-varying velocity
      if ((pd->parameters()->velocity[0].is_time_varying
           || pd->parameters()->velocity[1].is_time_varying
           || pd->parameters()->velocity[2].is_time_varying)
          && !(pd->parameters()->velocity[0].is_space_varying
               || pd->parameters()->velocity[1].is_space_varying
               || pd->parameters()->velocity[2].is_space_varying))
        n_time_varying_sliding_slip_bcs++;
      break;
    }
    case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
      LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
      LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
      // PowerVIZ does not support space-varying velocity
      if ((pd->parameters()->velocity[0].is_time_varying
           || pd->parameters()->velocity[1].is_time_varying
           || pd->parameters()->velocity[2].is_time_varying)
          && !(pd->parameters()->velocity[0].is_space_varying
               || pd->parameters()->velocity[1].is_space_varying
               || pd->parameters()->velocity[2].is_space_varying))
        n_time_varying_sliding_noslip_bcs++;
      break;
    }
    default:
      break;
    }
    surface_physics_desc++;
  }

  g_n_time_varying_rotating_bcs = n_time_varying_rotating_slip_bcs + n_time_varying_rotating_noslip_bcs;
  g_n_time_varying_sliding_bcs  = n_time_varying_sliding_slip_bcs + n_time_varying_sliding_noslip_bcs;
  g_n_time_varying_mbcs         = g_n_time_varying_rotating_bcs + g_n_time_varying_sliding_bcs;

  if (g_n_time_varying_mbcs > 0) {
    if (g_n_time_varying_rotating_bcs > 0) {
      g_time_varying_rotating_bcs = xnew sMEAS_WINDOW_ROTATING_BC_INFO [g_n_time_varying_rotating_bcs];
      g_time_varying_rotating_bc_pds = xnew PHYSICS_DESCRIPTOR [g_n_time_varying_rotating_bcs];
    }
    if (g_n_time_varying_sliding_bcs > 0) {
      g_time_varying_sliding_bcs = xnew sMEAS_WINDOW_SLIDING_BC_INFO [g_n_time_varying_sliding_bcs];
      g_time_varying_sliding_bc_pds = xnew PHYSICS_DESCRIPTOR [g_n_time_varying_sliding_bcs];
    }

    PHYSICS_DESCRIPTOR           surface_physics_desc = sim.flow_surface_physics_descs;
    MEAS_WINDOW_ROTATING_BC_INFO rotating_bc          = g_time_varying_rotating_bcs;
    MEAS_WINDOW_SLIDING_BC_INFO  sliding_bc           = g_time_varying_sliding_bcs;

    asINT32 ith_rotating_bc = 0;
    asINT32 ith_sliding_bc = 0;

    ccDOTIMES(i, sim.n_flow_surface_physics_descs) {
      switch (surface_physics_desc->phys_type_desc->cdi_physics_type) {
      case ANGULAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
        if (is_angular_physics_desc_time_varying(pd)) {
#if DEBUG
          rotating_bc->surface_physics_desc_index = i;
          rotating_bc->is_slip = TRUE;
#endif
          rotating_bc->angular_vel_var = &pd->parameters()->angular_spec.angular_velocity;
          if (g_timescale.m_time > 0 && pd->dependent_shob_dyn_groups) { // checkpoint restore
            pd->eval_time_varying_only_parameter_program(g_timescale.m_time-1, g_timescale.m_powertherm_time, global_eqn_error_handler);
            rotating_bc->angular_vel = rotating_bc->angular_vel_var->value;
          }

          g_time_varying_rotating_bc_pds[ith_rotating_bc++] = pd;
          rotating_bc++;
        }
        break;
      }
      case ANGULAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
        ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
        ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (ANGULAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
        if (is_angular_physics_desc_time_varying(pd)) {
#if DEBUG
          rotating_bc->surface_physics_desc_index = i;
          rotating_bc->is_slip = TRUE;
#endif
          rotating_bc->angular_vel_var = &pd->parameters()->angular_spec.angular_velocity;
          if (g_timescale.m_time > 0 && pd->dependent_shob_dyn_groups) { // checkpoint restore
            pd->eval_time_varying_only_parameter_program(g_timescale.m_time-1, g_timescale.m_powertherm_time, global_eqn_error_handler);
            rotating_bc->angular_vel = rotating_bc->angular_vel_var->value;
          }
          g_time_varying_rotating_bc_pds[ith_rotating_bc++] = pd;
          rotating_bc++;
        }
        break;
      }
      case LINEAR_SLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
        LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_SLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
        if ((pd->parameters()->velocity[0].is_time_varying
             || pd->parameters()->velocity[1].is_time_varying
             || pd->parameters()->velocity[2].is_time_varying)
            && !(pd->parameters()->velocity[0].is_space_varying
                 || pd->parameters()->velocity[1].is_space_varying
                 || pd->parameters()->velocity[2].is_space_varying)) {
#if DEBUG
          sliding_bc->surface_physics_desc_index = i;
          sliding_bc->is_slip = TRUE;
#endif
          sliding_bc->linear_vel_vars = pd->parameters()->velocity;
          if (g_timescale.m_time > 0 && pd->dependent_shob_dyn_groups) { // checkpoint restore
            pd->eval_time_varying_only_parameter_program(g_timescale.m_time-1, g_timescale.m_powertherm_time, global_eqn_error_handler);
            sliding_bc->linear_vel[0] = sliding_bc->linear_vel_vars[0].value;
            sliding_bc->linear_vel[1] = sliding_bc->linear_vel_vars[1].value;
            sliding_bc->linear_vel[2] = sliding_bc->linear_vel_vars[2].value;
          }
          g_time_varying_sliding_bc_pds[ith_sliding_bc++] = pd;
          sliding_bc++;
        }
        break;
      }
      case LINEAR_NOSLIP_CDI_SURFACE_PHYSICS_TYPE_CASE: {
#if BUILD_5G_LATTICE
        LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_5G_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#else
        LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR pd = (LINEAR_NOSLIP_SURFEL_PHYSICS_DESCRIPTOR)surface_physics_desc;
#endif
        if ((pd->parameters()->velocity[0].is_time_varying
             || pd->parameters()->velocity[1].is_time_varying
             || pd->parameters()->velocity[2].is_time_varying)
            && !(pd->parameters()->velocity[0].is_space_varying
                 || pd->parameters()->velocity[1].is_space_varying
                 || pd->parameters()->velocity[2].is_space_varying)) {
#if DEBUG
          sliding_bc->surface_physics_desc_index = i;
          sliding_bc->is_slip = TRUE;
#endif
          sliding_bc->linear_vel_vars = pd->parameters()->velocity;
          if (g_timescale.m_time > 0 && pd->dependent_shob_dyn_groups) { // checkpoint restore
            pd->eval_time_varying_only_parameter_program(g_timescale.m_time-1, g_timescale.m_powertherm_time, global_eqn_error_handler);
            sliding_bc->linear_vel[0] = sliding_bc->linear_vel_vars[0].value;
            sliding_bc->linear_vel[1] = sliding_bc->linear_vel_vars[1].value;
            sliding_bc->linear_vel[2] = sliding_bc->linear_vel_vars[2].value;
          }

          g_time_varying_sliding_bc_pds[ith_sliding_bc++] = pd;
          sliding_bc++;
        }
        break;
      }
      default:
        break;
      }
      surface_physics_desc++;
    }
  }
  return TRUE;
}

VOID sMEAS_WINDOW::initialize_master_sp(asINT32 master_sp)
{
  // We never set is_ref_frame_master_sp for a probe because the CP does not expect
  // to receive ref frame info.
  if (master_sp == my_proc_id) {
    if ((n_stationary_meas_cells + n_global_moving_meas_cells) <= 0)
      msg_internal_error("SP %d assigned as master for meas window %d even though the window is"
                         " not active on this SP.",
                         my_proc_id, index);

    m_is_master_sp = TRUE;
    if (!is_probe) {
      BOOLEAN first_call_lrfs = find_time_varying_lrfs();
      if (g_n_time_varying_lrfs > 0) {
        if (first_call_lrfs) {
          lrfs = g_time_varying_lrfs;
        } else {
          lrfs = xnew sMEAS_WINDOW_LRF_INFO [g_n_time_varying_lrfs];
          memcpy(lrfs, g_time_varying_lrfs, g_n_time_varying_lrfs * sizeof(sMEAS_WINDOW_LRF_INFO));
        }
      }

      movbs = xnew sMEAS_WINDOW_MOVB_INFO [sim.n_movb_physics_descs];

      BOOLEAN first_call_mbcs = find_time_varying_moving_bcs();
      if (g_n_time_varying_mbcs) {
        if (first_call_mbcs) {
          rotating_bcs = g_time_varying_rotating_bcs;
          sliding_bcs  = g_time_varying_sliding_bcs;
        } else {
          if (g_n_time_varying_rotating_bcs > 0) {
            rotating_bcs = xnew sMEAS_WINDOW_ROTATING_BC_INFO [g_n_time_varying_rotating_bcs];
            memcpy(rotating_bcs, g_time_varying_rotating_bcs,
                   g_n_time_varying_rotating_bcs * sizeof(sMEAS_WINDOW_ROTATING_BC_INFO));
          }
          if (g_n_time_varying_sliding_bcs > 0) {
            sliding_bcs = xnew sMEAS_WINDOW_SLIDING_BC_INFO [g_n_time_varying_sliding_bcs];
            memcpy(sliding_bcs, g_time_varying_sliding_bcs,
                   g_n_time_varying_sliding_bcs * sizeof(sMEAS_WINDOW_SLIDING_BC_INFO));
          }
        }
      }

      if (sim.grf.is_time_varying
          || g_n_time_varying_lrfs > 0
          || g_n_time_varying_mbcs > 0
          || sim.n_movb_physics_descs > 0
          )
        is_ref_frame_master_sp = TRUE;
    }
  }
}

STP_MEAS_CELL_INDEX sMEAS_WINDOW::create_ublk_meas_cell(DGF_UBLK_BASE_DESC ublk_desc,
                                                        STP_MEAS_CELL_INDEX global_meas_cell_index,
                                                        asINT32 voxel_scale,
                                                        BOOLEAN is_split_ublk,
                                                        auINT32 meas_cell_ref_voxel_mask) // only used if per voxel measurements
{
  // The instances of a split ublk (a.k.a. the ublks living in the same ublk cube) appear in
  // a DGF file back-to-back. Currently we assign these instances to the same meas cell
  // unless they are in different reference frames. In the future, the instances will also be
  // assigned to different meas cells if the instances fall in disjoint fluid sections of
  // the associated meas cell cube.
  //
  // If assigned to the same meas cell, the instances of a split ublk will obviously contain
  // the same meas cell references. Given that some global_meas_cell_index values are not
  // registered in the global to local map (see code below), we need to explicitly check if
  // global_meas_cell_index in this call is the same as that passed to the last call to
  // this method.
  //
  // Except for the case of instances of a split ublk assigned to different meas cells,
  // if a global_meas_cell_index is associated with multiple ublks, those ublks will
  // appear back-to-back in the DGF file. Thus there's no need to register a
  // global_meas_cell_index in the global to local map unless associated with a split
  // ublk.

  if (global_meas_cell_index == last_global_meas_cell_index)
    return last_sp_meas_cell_index;

  last_global_meas_cell_index = global_meas_cell_index;

  if (is_per_voxel_for_scale(voxel_scale)) {
    // We truly have per voxel measurements. The allocated meas cell is not
    // shared by other voxels, so there is no need to register it in the global
    // to local meas cell index map.
    //
    // Note that for a split ublk, all ublk instances have the same meas cell
    // ref voxel mask, which is the OR of all the ublk instance voxel masks
    // each of which indicates which voxels of the instance contribute to a
    // meas window. To obtain one of these ublk instance voxel mask, simply
    // AND the meas cell ref voxel mask with the instance's fluid_like_voxel_mask.
    last_sp_meas_cell_index = n_stationary_meas_cells;
    asINT32 n_meas_cells_for_ublk = bitcount8(meas_cell_ref_voxel_mask);
    ccDOTIMES(i, n_meas_cells_for_ublk)
      push_back_global_meas_cell_index(global_meas_cell_index + i, n_stationary_meas_cells + i);
    n_stationary_meas_cells += n_meas_cells_for_ublk;
    return last_sp_meas_cell_index;
  }

  // At this point, we know that the building block for meas cells is the ublk
  // and no single voxel meas cells are allowed.
  if (is_composite || is_development)
    return last_sp_meas_cell_index = create_meas_cell(global_meas_cell_index);

  asINT32 ublk_scale = coarsen_scale(voxel_scale);
  if (sim_is_scale_same_or_finer(meas_cell_scale, ublk_scale) && !is_split_ublk) {
    // If the meas cell is same size or smaller than the ublk, the allocated meas
    // cell is not shared by other ublk cubes. Thus there is no need to register it in
    // the global to local meas cell index map. See comment above about non-split
    // ublks. The (!is_split_ublk) condition is necessary because we currently can
    // have some instances of a split ublk contributing to the same meas cell and
    // some to different meas cells (depends on whether split by a wall or split by
    // an LRF). Even in the future, the instances of a split ublk at the end of a
    // thin wall may contribute to the same meas cell. Thus the (!is_split_ublk)
    // condition will remain necessary.
    last_sp_meas_cell_index = n_stationary_meas_cells;
    push_back_global_meas_cell_index(global_meas_cell_index, n_stationary_meas_cells);
    n_stationary_meas_cells++;
    return last_sp_meas_cell_index;
  } else {
    // Since ublks appear in octtree order in the DGF file, if this is the first
    // ublk in a new meas cell, we can clear the map from global to local indices.
    BOOLEAN is_new_meas_cube = FALSE;
    STP_COORD *ublk_location = ublk_desc->b.location;
    asINT32 n_dims           = sim.num_dims;
    STP_COORD cell_size        = sim_scale_to_cube_size(meas_cell_scale);
    STP_COORD cell_coord_mask  = ~(cell_size - 1);
    ccDOTIMES(axis, n_dims) {
      STP_COORD cell_coord = ublk_location[axis] & cell_coord_mask;
      if (cell_coord != m_current_meas_cube_location[axis]) {
        is_new_meas_cube = TRUE;
        m_current_meas_cube_location[axis] = cell_coord;
      }
    }

    if (is_new_meas_cube && (m_global_to_sp_cell_index != NULL))
      m_global_to_sp_cell_index->Clear();

    return last_sp_meas_cell_index = create_meas_cell(global_meas_cell_index);
  }
}

BOOLEAN sMEAS_WINDOW::read_variables(LGI_STREAM stream)
{
  /* Read the record header */
  LGI_MEAS_WINDOW_VARS record;
  if (!record.read(stream))
    return FALSE;

  MEAS_WINDOW window = this;
  window->contains_std_dev_vars = FALSE;
  window->calc_lambda2 = FALSE;

  asINT32 n_cp_vars          = record.n_variables;
  window->n_cp_variables     = n_cp_vars;

  window->solver_mask        = record.solver_mask;
  window->acous_switch_scale = record.acous_switch_scale;
  window->is_probe           = record.is_probe;
  window->is_output_in_local_csys = record.is_output_in_local_csys;
  // to make sure it is either 0 or 1
  window->is_meas_vars_output_dp  = record.is_meas_vars_output_dp != 0;
  window->calc_htc_for_adb_walls = record.calc_htc_for_adb_walls;

  window->min_pressure       = record.min_pressure * g_density_scale_factor;
  window->max_pressure       = record.max_pressure * g_density_scale_factor;
#if BUILD_D19_LATTICE
  if(sim.is_pf_model){
    window->min_pressure       = record.min_pressure;
    window->max_pressure       = record.max_pressure;
  }
#endif

  if (window->min_pressure < 0)
    window->min_pressure = 0;
  if (window->max_pressure < 0)
    window->max_pressure = SFLOAT_MAX;

  vcopy(window->reference_point, record.reference_point);


  SRI_VARIABLE_TYPE *cp_var_types = cnew SRI_VARIABLE_TYPE [ n_cp_vars ];

  /*#if BUILD_5G_LATTICE
  window->var_5g_types = cnew SRI_VARIABLE_TYPE [ 3 * window->n_cp_variables ];
  window->comp_5g_index = cnew sINT8 [ 3 * window->n_cp_variables ];
  window->phase_5g_index = cnew sINT8 [ 3 * window->n_cp_variables ];
  #endif*/

  LGI_MEAS_WINDOW_VARS_SUBREC subrec;
  for (asINT32 cp_var_index = 0; cp_var_index < n_cp_vars; cp_var_index++) {
    subrec.read(stream);
    SRI_VARIABLE_TYPE var_type = SRI_VARIABLE_TYPE(subrec.var_type);
    cp_var_types[cp_var_index] = var_type;
  }

  // Reserve space for hidden variables if we are collecting std dev variables.
  // Hence allocate 3 * n_variables.
  window->var_types = cnew SRI_VARIABLE_TYPE [ 3 * n_cp_vars ];
#if BUILD_5G_LATTICE
  window->var_5g_types = cnew SRI_VARIABLE_TYPE [ 3 * n_cp_vars ];
  window->comp_5g_index = cnew sINT8 [ 3 * n_cp_vars ];
  window->phase_5g_index = cnew sINT8 [ 3 * n_cp_vars ];
#endif

  //LB_UDS
  if (sim.uds_solver_type == LB_UDS) {
    window->uds_var_base_type = cnew SRI_VARIABLE_TYPE [ 3 * n_cp_vars ];
    window->uds_scalar_index = cnew sINT8 [ 3 * n_cp_vars ];
  } else {
    window->uds_var_base_type = NULL;
    window->uds_scalar_index = NULL;
  }
  
  m_var_component_ids = cnew sINT16[ 3 * n_cp_vars ];
  std::unordered_map<SRI_VARIABLE_TYPE, sINT16> particle_component_counts;
  
  asINT32 sp_var_index = 0;
  for (asINT32 cp_var_index = 0; cp_var_index < n_cp_vars; cp_var_index++) {
    SRI_VARIABLE_TYPE var_type = cp_var_types[cp_var_index];

    /*#if BUILD_5G_LATTICE
    window->var_5g_types[cp_var_index] = sim.cvid_helper->get_5g_var_type(var_type);
    window->comp_5g_index[cp_var_index] = sim.cvid_helper->get_5g_fluid_component_for_var_id(var_type);
    window->phase_5g_index[cp_var_index] = sim.cvid_helper->get_5g_phase_for_var_id(var_type);
    #endif*/

    window->var_types[sp_var_index] = var_type;
#if BUILD_5G_LATTICE
    if (sim.cvid_helper->is_5g_var_id(var_type)) {
      window->var_5g_types[sp_var_index] = sim.cvid_helper->get_5g_var_type(var_type);
      window->comp_5g_index[sp_var_index] = sim.cvid_helper->get_5g_fluid_component_for_var_id(var_type);
      window->phase_5g_index[sp_var_index] = sim.cvid_helper->get_5g_phase_for_var_id(var_type);
    }
#endif

    if (sim.cvid_helper->is_uds_var_id(var_type)) {
      window->uds_var_base_type[sp_var_index] = sim.cvid_helper->get_uds_var_base_type(var_type);
      window->uds_scalar_index[sp_var_index] = sim.cvid_helper->get_uds_scalar_for_var_id(var_type);
    }

    m_var_component_ids[sp_var_index] = -1;
    if(sri_is_particle_tracking_vartype(var_type)) {
        int nth_component = particle_component_counts[var_type]++;
        int emitter_id = m_per_emitter_particle_measurements ? nth_component : -1;
        int material_id = m_per_material_particle_measurements ? nth_component : -1;
        m_var_component_ids[sp_var_index] = sPARTICLE_MEAS_COMPONENT::compute_composite_id(
                                      emitter_id,
                                      material_id);
    }

    // By convention, STD_DEV_HELPER var follows the associated STD_DEV var.
    switch(var_type) {
    case SRI_VARIABLE_STD_DEV_XVEL:
    case SRI_VARIABLE_STD_DEV_YVEL:
    case SRI_VARIABLE_STD_DEV_ZVEL:
    case SRI_VARIABLE_STD_DEV_VEL_MAG:
    case SRI_VARIABLE_STD_DEV_PRESSURE:
    case SRI_VARIABLE_STD_DEV_TEMP:
    case SRI_VARIABLE_STD_DEV_DENSITY:
      window->var_types[++sp_var_index] = SRI_VARIABLE_STD_DEV_HELPER;
      window->var_types[++sp_var_index] = SRI_VARIABLE_STD_DEV_HELPER;
      window->contains_std_dev_vars = TRUE;
      break;
    case SRI_VARIABLE_HTC_CHAR_TEMP:
      sim.is_some_htc_meas_window = TRUE;
      break;
    case SRI_VARIABLE_LAMBDA2:
      window->calc_lambda2 = TRUE;
      break;
    case SRI_VARIABLE_DEFROST_TIME:
      window->var_types[++sp_var_index] = SRI_VARIABLE_DEFROST_TIME_HELPER;
      break;
    default:
      break;
    }

    sp_var_index++;
  }

  window->n_variables = sp_var_index;

  if ( window->n_global_moving_meas_cells ) {
    window->m_moving_meas_cell_mgr = new sMOVING_MEAS_CELL_MGR(window->index,window->n_variables);
  }


  window->validate_vars_supported();

  return TRUE;  /* success */
}

VOID sMEAS_WINDOW::validate_vars_supported()
{
  MEAS_WINDOW window = this;
  /* @@@ This should be recoded as an initial clause handling all the variables
   * supported by all file types followed by a second clause handling all the
   * special cases.
   */
  switch (window->meas_window_type) {
  case LGI_FLUID_WINDOW:
  case LGI_VOLUME_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_DENSITY:
      case SRI_VARIABLE_XVEL:
      case SRI_VARIABLE_YVEL:
      case SRI_VARIABLE_ZVEL:
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_TOTAL_TEMPERATURE:
      case SRI_VARIABLE_TURB_KINETIC_ENERGY:
      case SRI_VARIABLE_TURB_DISSIPATION:
      case SRI_VARIABLE_STRESS_TENSOR_MAG:
      case SRI_VARIABLE_TURBULENT_THERMAL_CONDUCTIVITY:
      case SRI_VARIABLE_VEL_MAG:
      case SRI_VARIABLE_ENERGY:
      case SRI_VARIABLE_LB_ENERGY:
      case SRI_VARIABLE_PRESSURE:
      case SRI_VARIABLE_TIME_DERIV_PRESSURE:
      case SRI_VARIABLE_INTERNAL_ENERGY:
      case SRI_VARIABLE_KINETIC_ENERGY:
      case SRI_VARIABLE_ENTHALPY:
      case SRI_VARIABLE_EDDY_VISCOSITY:
      case SRI_VARIABLE_VISCOSITY:
      case SRI_VARIABLE_DIV_U:
      case SRI_VARIABLE_XMOMENTUM:
      case SRI_VARIABLE_YMOMENTUM:
      case SRI_VARIABLE_ZMOMENTUM:
      case SRI_VARIABLE_MOMENTUM_MAG:
      case SRI_VARIABLE_N_SCREENED_VOXELS:
      case SRI_VARIABLE_SCREENED_VOLUME:
      case SRI_VARIABLE_VOLUME:
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
      case SRI_VARIABLE_XVORTICITY:
      case SRI_VARIABLE_YVORTICITY:
      case SRI_VARIABLE_ZVORTICITY:
      case SRI_VARIABLE_VORTICITY_MAG:
      case SRI_VARIABLE_SWIRL:
      case SRI_VARIABLE_LAMBDA2:
      case SRI_VARIABLE_PRTCL_NUMBER:
      case SRI_VARIABLE_PRTCL_MEAN_MASS:
      case SRI_VARIABLE_PRTCL_MEAN_XVEL:
      case SRI_VARIABLE_PRTCL_MEAN_YVEL:
      case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
      case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
      case SRI_VARIABLE_PRTCL_MEAN_DENSITY:
      case SRI_VARIABLE_PRTCL_MEAN_DIAMETER:
      case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA:
      case SRI_VARIABLE_PRTCL_MEAN_VOLUME:
      case SRI_VARIABLE_PRTCL_XFORCE:
      case SRI_VARIABLE_PRTCL_YFORCE:
      case SRI_VARIABLE_PRTCL_ZFORCE:
      case SRI_VARIABLE_STD_DEV_HELPER:
      case SRI_VARIABLE_WATER_VAPOR_MFRAC:
      case SRI_VARIABLE_WATER_VAPOR_MFLUX:
      case SRI_VARIABLE_LIQUID_FRACTION:
      case SRI_VARIABLE_HEAT_GENERATION:
      case SRI_VARIABLE_PRTCL_MEAN_TEMP:
      case SRI_VARIABLE_PRTCL_EVAPORATION_RATE:
      case SRI_VARIABLE_PRTCL_COMPOSITION_1:
      case SRI_VARIABLE_PRTCL_COMPOSITION_2:
      case SRI_VARIABLE_PRTCL_COMPOSITION_3:
        break;
      default:
        if (sim.is_5g_sim
            && sim.cvid_helper->is_5g_var_id(var_type)
            && sim.cvid_helper->get_5g_var_type(var_type) != SRI_VARIABLE_CONTACT_ANGLE) {
          break;
        } else if(sim.is_particle_model && sim.cvid_helper->is_pm_var_id(var_type)) {
          int offset = var_type - sim.cvid_helper->get_first_pm_var_id();
          switch(offset) {
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::MEAN_PARTICLE_TEMP:
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::EVAPORATION_RATE:
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_1:
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_2:
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::PARTICLE_COMPOSITION_3:
            break;
          default: 
            msg_internal_error("Unhandled particle modeling variable type %d for fluid window id %d",
                               var_type, window->index);
            break;
          }
        } else if (sim.uds_solver_type==LB_UDS && sim.cvid_helper->is_uds_var_id(var_type)) {
	  SRI_VARIABLE_TYPE base_type = sim.cvid_helper->get_uds_var_base_type(var_type);
	  if (base_type == SRI_VARIABLE_UDS_SCALAR_VALUE ||
	      base_type == SRI_VARIABLE_UDS_SOURCE_TERM)
	    break;
	  else {	  	  
	    msg_internal_error("Unhandled uds variable type %d for fluid window id %d",
                               var_type, window->index);
            break;
	  }
        } else if (var_type >= sim.cvid_helper->get_first_unassigned_var_id()) { //"unassigned" in this context means the var type is not in the range of particle modeling, 5g, or UDS solver custome variable IDs.
          int offset = var_type - sim.cvid_helper->get_first_unassigned_var_id(); 
          switch(offset) {
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::CONDUCTION_PASSTHROUGH:
	  case (int)eEXPERIMENTAL_VARIABLE_TYPES::TANGENTIAL_GRAD:
            break;
          default:
            msg_internal_error("Unhandled custom variable type %d for fluid/volume window id %d",
                               var_type, window->index);
            break;
            }
	} else {
          msg_internal_error("Unhandled measurement variable type %d for fluid window id %d",
                             var_type, window->index);
          break;
        }
      }
    }

    break;
  case LGI_POROUS_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_DENSITY:
      case SRI_VARIABLE_XVEL:
      case SRI_VARIABLE_YVEL:
      case SRI_VARIABLE_ZVEL:
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_TOTAL_TEMPERATURE:
      case SRI_VARIABLE_TURB_KINETIC_ENERGY:
      case SRI_VARIABLE_TURB_DISSIPATION:
      case SRI_VARIABLE_STRESS_TENSOR_MAG:
      case SRI_VARIABLE_TURBULENT_THERMAL_CONDUCTIVITY:
      case SRI_VARIABLE_VEL_MAG:
      case SRI_VARIABLE_ENERGY:
      case SRI_VARIABLE_LB_ENERGY:
      case SRI_VARIABLE_PRESSURE:
      case SRI_VARIABLE_TIME_DERIV_PRESSURE:
      case SRI_VARIABLE_INTERNAL_ENERGY:
      case SRI_VARIABLE_KINETIC_ENERGY:
      case SRI_VARIABLE_ENTHALPY:
      case SRI_VARIABLE_EDDY_VISCOSITY:
      case SRI_VARIABLE_VISCOSITY:
      case SRI_VARIABLE_DIV_U:
      case SRI_VARIABLE_XMOMENTUM:
      case SRI_VARIABLE_YMOMENTUM:
      case SRI_VARIABLE_ZMOMENTUM:
      case SRI_VARIABLE_MOMENTUM_MAG:
      case SRI_VARIABLE_HEAT_GENERATION:
      case SRI_VARIABLE_FLUID_XFORCE:
      case SRI_VARIABLE_FLUID_YFORCE:
      case SRI_VARIABLE_FLUID_ZFORCE:
      case SRI_VARIABLE_FLUID_FORCE_MAG:
      case SRI_VARIABLE_FLUID_XTORQUE:
      case SRI_VARIABLE_FLUID_YTORQUE:
      case SRI_VARIABLE_FLUID_ZTORQUE:
      case SRI_VARIABLE_FLUID_TORQUE_MAG:
      case SRI_VARIABLE_N_SCREENED_VOXELS:
      case SRI_VARIABLE_SCREENED_VOLUME:
      case SRI_VARIABLE_VOLUME:
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
      case SRI_VARIABLE_XVORTICITY:
      case SRI_VARIABLE_YVORTICITY:
      case SRI_VARIABLE_ZVORTICITY:
      case SRI_VARIABLE_VORTICITY_MAG:
      case SRI_VARIABLE_SWIRL:
      case SRI_VARIABLE_LAMBDA2:
      case SRI_VARIABLE_PRTCL_NUMBER:
      case SRI_VARIABLE_PRTCL_MEAN_MASS:
      case SRI_VARIABLE_PRTCL_MEAN_XVEL:
      case SRI_VARIABLE_PRTCL_MEAN_YVEL:
      case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
      case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
      case SRI_VARIABLE_PRTCL_MEAN_DENSITY:
      case SRI_VARIABLE_PRTCL_MEAN_DIAMETER:
      case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA:
      case SRI_VARIABLE_PRTCL_MEAN_VOLUME:
      case SRI_VARIABLE_PRTCL_XFORCE:
      case SRI_VARIABLE_PRTCL_YFORCE:
      case SRI_VARIABLE_PRTCL_ZFORCE:
      case SRI_VARIABLE_STD_DEV_HELPER:
      case SRI_VARIABLE_WATER_VAPOR_MFRAC:
      case SRI_VARIABLE_WATER_VAPOR_MFLUX:
      case SRI_VARIABLE_LIQUID_FRACTION:
      case SRI_VARIABLE_PRTCL_MEAN_TEMP:
      case SRI_VARIABLE_PRTCL_EVAPORATION_RATE:
      case SRI_VARIABLE_PRTCL_COMPOSITION_1:
      case SRI_VARIABLE_PRTCL_COMPOSITION_2:
      case SRI_VARIABLE_PRTCL_COMPOSITION_3:
        break;
      default:
        if (sim.is_5g_sim
            && sim.cvid_helper->is_5g_var_id(var_type)
            && sim.cvid_helper->get_5g_var_type(var_type) != SRI_VARIABLE_CONTACT_ANGLE) {
          break;
        } else if (sim.uds_solver_type==LB_UDS && sim.cvid_helper->is_uds_var_id(var_type)) {
	  SRI_VARIABLE_TYPE base_type = sim.cvid_helper->get_uds_var_base_type(var_type);
	  if (base_type == SRI_VARIABLE_UDS_SCALAR_VALUE ||
	      base_type == SRI_VARIABLE_UDS_SOURCE_TERM)
	    break;
	  else {
	    msg_internal_error("Unhandled uds variable type %d for porous media window id %d",
                               var_type, window->index);
            break;
	  }
	} else {
          msg_internal_error("Unhandled measurement variable type %d for porous media window id %d",
                             var_type, window->index);
          break;
        }
      }
    }
    break;
  case LGI_SURFACE_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_DENSITY:
      case SRI_VARIABLE_XVEL:
      case SRI_VARIABLE_YVEL:
      case SRI_VARIABLE_ZVEL:
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_TOTAL_TEMPERATURE:
      case SRI_VARIABLE_TURB_KINETIC_ENERGY:
      case SRI_VARIABLE_TURB_DISSIPATION:
      case SRI_VARIABLE_STRESS_TENSOR_MAG:
      case SRI_VARIABLE_EDDY_VISCOSITY:
      case SRI_VARIABLE_XFORCE:
      case SRI_VARIABLE_YFORCE:
      case SRI_VARIABLE_ZFORCE:
      case SRI_VARIABLE_KINETIC_XFORCE:
      case SRI_VARIABLE_KINETIC_YFORCE:
      case SRI_VARIABLE_KINETIC_ZFORCE:
      case SRI_VARIABLE_TFORCE_MAG:
      case SRI_VARIABLE_XTORQUE:
      case SRI_VARIABLE_YTORQUE:
      case SRI_VARIABLE_ZTORQUE:
      case SRI_VARIABLE_VEL_MAG:
      case SRI_VARIABLE_ENERGY:
      case SRI_VARIABLE_LB_ENERGY:
      case SRI_VARIABLE_PRESSURE:
      case SRI_VARIABLE_TIME_DERIV_PRESSURE:
      case SRI_VARIABLE_XPRESSURE_GRADIENT:
      case SRI_VARIABLE_YPRESSURE_GRADIENT:
      case SRI_VARIABLE_ZPRESSURE_GRADIENT:
      case SRI_VARIABLE_PRESSURE_GRADIENT_MAG:
      case SRI_VARIABLE_USTAR:
      case SRI_VARIABLE_YPLUS:
      case SRI_VARIABLE_THERMAL_YPLUS:
      case SRI_VARIABLE_INTERNAL_ENERGY:
      case SRI_VARIABLE_KINETIC_ENERGY:
      case SRI_VARIABLE_ENTHALPY:
      case SRI_VARIABLE_FORCE_MAG:
      case SRI_VARIABLE_KINETIC_FORCE_MAG:
      case SRI_VARIABLE_N_SCREENED_SURFELS:
      case SRI_VARIABLE_SCREENED_AREA:
      case SRI_VARIABLE_AREA:
      case SRI_VARIABLE_TORQUE_MAG:
      case SRI_VARIABLE_XMOMENTUM:
      case SRI_VARIABLE_YMOMENTUM:
      case SRI_VARIABLE_ZMOMENTUM:
      case SRI_VARIABLE_MOMENTUM_MAG:
      case SRI_VARIABLE_HEAT_FLUX:
      case SRI_VARIABLE_MASS_FLUX:
      case SRI_VARIABLE_NEAR_WALL_TEMP:
      case SRI_VARIABLE_HTC_CHAR_TEMP:
      case SRI_VARIABLE_HTC_NEAR_WALL_TEMP:
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
      case SRI_VARIABLE_STD_DEV_HELPER:
      case SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DIAM_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DENS_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DENS_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND:
      case SRI_VARIABLE_PRTCL_MASS_RATE_OUTBOUND:
      case SRI_VARIABLE_PRTCL_RATE_INBOUND:
      case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
      case SRI_VARIABLE_PRTCL_XIMPULSE:
      case SRI_VARIABLE_PRTCL_YIMPULSE:
      case SRI_VARIABLE_PRTCL_ZIMPULSE:
      case SRI_VARIABLE_FILM_THICKNESS:
      case SRI_VARIABLE_FILM_XVEL:
      case SRI_VARIABLE_FILM_ZVEL:
      case SRI_VARIABLE_FILM_YVEL:
      case SRI_VARIABLE_FILM_VEL_MAG:
      case SRI_VARIABLE_FILM_STRESS:
      case SRI_VARIABLE_FILM_PERSISTENCE:
      case SRI_VARIABLE_WATER_FILM_THICKNESS:
      case SRI_VARIABLE_WATER_VAPOR_MFRAC:
      case SRI_VARIABLE_WATER_VAPOR_MFLUX:
      case SRI_VARIABLE_DEFROST_TIME:
      case SRI_VARIABLE_DEFROST_TIME_HELPER:
      case SRI_VARIABLE_ICE_THICKNESS:
      case SRI_VARIABLE_EROSION_DEPTH:
      case SRI_VARIABLE_IRRADIATION:
      case SRI_VARIABLE_RADIOSITY:
        break;
      default:
        if (sim.is_5g_sim && sim.cvid_helper->is_5g_var_id(var_type)) {
          SRI_VARIABLE_TYPE offset = sim.cvid_helper->get_5g_var_type(var_type);
          if (offset == SRI_VARIABLE_DENSITY         ||
              offset == SRI_VARIABLE_PRESSURE        ||
              offset == SRI_VARIABLE_CONTACT_ANGLE   ||
              offset == SRI_VARIABLE_XVEL            ||
              offset == SRI_VARIABLE_YVEL            ||
              offset == SRI_VARIABLE_ZVEL            ||
              offset == SRI_VARIABLE_VEL_MAG         ||
              offset == SRI_VARIABLE_XFORCE          ||
              offset == SRI_VARIABLE_YFORCE          ||
              offset == SRI_VARIABLE_ZFORCE          ||
	      (sim.is_scalar_model && (offset==SRI_VARIABLE_WATER_VAPOR_MFRAC || offset==SRI_VARIABLE_WATER_VAPOR_MFLUX)))
              
            break;
          else {
            msg_internal_error("Unhandled 5G measurement variable type %d for surface window id %d",
                               var_type, window->index);
            break;
          }
        } else if(sim.is_particle_model && sim.cvid_helper->is_pm_var_id(var_type)) {
          int offset = var_type - sim.cvid_helper->get_first_pm_var_id();
          switch(offset) {
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::ACCRETION_VOLUME: //Accretion thickness is supported for surface windows in accretion simulations.
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::EROSION_VOLUME:
            break;
          default: 
            msg_internal_error("Unhandled particle modeling variable type %d for surface window id %d",
                               var_type, window->index);
            break;
          }
        } else if (sim.uds_solver_type==LB_UDS && sim.cvid_helper->is_uds_var_id(var_type)) {
	  SRI_VARIABLE_TYPE base_type = sim.cvid_helper->get_uds_var_base_type(var_type);
	  if (base_type == SRI_VARIABLE_UDS_SCALAR_VALUE  ||
	      base_type == SRI_VARIABLE_UDS_SCALAR_FLUX)
	    break;
	  else {	  
	    msg_internal_error("Unhandled uds variable type %d for surface window id %d",
                               var_type, window->index);
          }
        } else if (var_type >= sim.cvid_helper->get_first_unassigned_var_id()) { //"unassigned" in this context means the var type is not in the range of particle modeling, 5g, or UDS solver custome variable IDs.
          int offset = var_type - sim.cvid_helper->get_first_unassigned_var_id(); 
          switch(offset) {
          default:
            msg_internal_error("Unhandled custom variable type %d for surface window id %d",
                               var_type, window->index);
            break;
          }
            break;
	  } else {
          msg_internal_error("Unhandled measurement variable type %d for surface window id %d",
                             var_type, window->index);
          break;
        }
      }
    }
    break;
  case LGI_SAMPLING_SURFACE_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_N_SCREENED_SURFELS:
      case SRI_VARIABLE_SCREENED_AREA:
      case SRI_VARIABLE_DENSITY:
      case SRI_VARIABLE_XVEL:
      case SRI_VARIABLE_YVEL:
      case SRI_VARIABLE_ZVEL:
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_TOTAL_TEMPERATURE:
      case SRI_VARIABLE_TURB_KINETIC_ENERGY:
      case SRI_VARIABLE_TURB_DISSIPATION:
      case SRI_VARIABLE_STRESS_TENSOR_MAG:
      case SRI_VARIABLE_EDDY_VISCOSITY:
      case SRI_VARIABLE_VEL_MAG:
      case SRI_VARIABLE_ENERGY:
      case SRI_VARIABLE_LB_ENERGY:
      case SRI_VARIABLE_PRESSURE:
      case SRI_VARIABLE_TIME_DERIV_PRESSURE:
      case SRI_VARIABLE_MASS_FLUX:
      case SRI_VARIABLE_INTERNAL_ENERGY:
      case SRI_VARIABLE_KINETIC_ENERGY:
      case SRI_VARIABLE_ENTHALPY:
      case SRI_VARIABLE_XMOMENTUM:
      case SRI_VARIABLE_YMOMENTUM:
      case SRI_VARIABLE_ZMOMENTUM:
      case SRI_VARIABLE_MOMENTUM_MAG:
      case SRI_VARIABLE_STD_DEV_XVEL:
      case SRI_VARIABLE_STD_DEV_YVEL:
      case SRI_VARIABLE_STD_DEV_ZVEL:
      case SRI_VARIABLE_STD_DEV_VEL_MAG:
      case SRI_VARIABLE_STD_DEV_PRESSURE:
      case SRI_VARIABLE_STD_DEV_TEMP:
      case SRI_VARIABLE_STD_DEV_DENSITY:
      case SRI_VARIABLE_STD_DEV_HELPER:
      case SRI_VARIABLE_WATER_VAPOR_MFRAC:
      case SRI_VARIABLE_WATER_VAPOR_MFLUX:
      case SRI_VARIABLE_PRTCL_NUMBER:
      case SRI_VARIABLE_PRTCL_FLUX:
      case SRI_VARIABLE_PRTCL_MASS_FLUX:
      case SRI_VARIABLE_PRTCL_MEAN_DENSITY:
      case SRI_VARIABLE_PRTCL_MEAN_DIAMETER:
      case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA:
      case SRI_VARIABLE_PRTCL_MEAN_VOLUME:
      case SRI_VARIABLE_PRTCL_MEAN_XVEL:
      case SRI_VARIABLE_PRTCL_MEAN_YVEL:
      case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
      case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
      case SRI_VARIABLE_PRTCL_RATE_INBOUND:
      case SRI_VARIABLE_PRTCL_RATE_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MASS_RATE_INBOUND:
      case SRI_VARIABLE_PRTCL_MASS_RATE_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DENS_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DENS_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DIAM_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_DIAM_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_VOLUME_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_VOLUME_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_XVEL_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_YVEL_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_ZVEL_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_XVEL_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_YVEL_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_ZVEL_INBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA_OUTBOUND:
      case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA_INBOUND:
        break;
      default:
        if (sim.is_5g_sim && sim.cvid_helper->is_5g_var_id(var_type)) {
          SRI_VARIABLE_TYPE offset = sim.cvid_helper->get_5g_var_type(var_type);
          if (offset == SRI_VARIABLE_DENSITY         ||
              offset == SRI_VARIABLE_PRESSURE        ||
              offset == SRI_VARIABLE_MASS_FLUX       ||
              offset == SRI_VARIABLE_XVEL            ||
              offset == SRI_VARIABLE_YVEL            ||
              offset == SRI_VARIABLE_ZVEL            ||
	      (sim.is_scalar_model && (offset==SRI_VARIABLE_WATER_VAPOR_MFRAC || offset==SRI_VARIABLE_WATER_VAPOR_MFLUX)))
            break;
          else {
            msg_internal_error("Unhandled 5G measurement variable type %d for sample surface window id %d",
                               var_type, window->index);
            break;
          }
        } else if (sim.uds_solver_type==LB_UDS && sim.cvid_helper->is_uds_var_id(var_type)) {
	  SRI_VARIABLE_TYPE base_type = sim.cvid_helper->get_uds_var_base_type(var_type);
	  if (base_type == SRI_VARIABLE_UDS_SCALAR_VALUE)
	    break;
	  else {	  
	    msg_internal_error("Unhandled uds variable type %d for sample surface window id %d",
                               var_type, window->index);
            break;
	  }
	} else {
          msg_internal_error("Unhandled measurement variable type %d for sample surface window id %d",
                             var_type, window->index);
          break;
        }
      }
    }
    break;
  case LGI_TRAJECTORY_WINDOW:
    break;
  case LGI_SHELL_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_HEAT_FLUX:
      case SRI_VARIABLE_IRRADIATION:
      case SRI_VARIABLE_RADIOSITY:
        break;
      default:
        if (var_type >= sim.cvid_helper->get_first_unassigned_var_id()) { //"unassigned" in this context means the var type is not in the range of particle modeling, 5g, or UDS solver custome variable IDs.
          int offset = var_type - sim.cvid_helper->get_first_unassigned_var_id(); 
          switch(offset) {
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::CONDUCTION_PASSTHROUGH:
	  case (int)eEXPERIMENTAL_VARIABLE_TYPES::TANGENTIAL_GRAD:
            break;
          default:
            msg_internal_error("Unhandled custom variable type %d for conduction surface window id %d",
                               var_type, window->index);
            break;
          }
        } else {
    	  msg_internal_error("Unhandled measurement variable type %d for conduction surface window id %d",
                             var_type, window->index);
        }
        break;
      }
    }
    break;
  case LGI_SAMPLING_SHELL_WINDOW:
    ccDOTIMES(var_index, window->n_variables) {
      SRI_VARIABLE_TYPE var_type = window->var_types[var_index];
      switch (var_type) {
      case SRI_VARIABLE_TEMP:
      case SRI_VARIABLE_HEAT_FLUX:
      //case SRI_VARIABLE_IRRADIATION:
      //case SRI_VARIABLE_RADIOSITY:
        break;
      default:
        if (var_type >= sim.cvid_helper->get_first_unassigned_var_id()) { //"unassigned" in this context means the var type is not in the range of particle modeling, 5g, or UDS solver custome variable IDs.
          int offset = var_type - sim.cvid_helper->get_first_unassigned_var_id(); 
          switch(offset) {
          case (int)eEXPERIMENTAL_VARIABLE_TYPES::CONDUCTION_PASSTHROUGH:
	  case (int)eEXPERIMENTAL_VARIABLE_TYPES::TANGENTIAL_GRAD:
            break;
          default:
            msg_internal_error("Unhandled custom variable type %d for conduction sample surface window id %d",
                               var_type, window->index);
            break;
          }
        } else {
    	  msg_internal_error("Unhandled measurement variable type %d for conduction sample surface window id %d",
                             var_type, window->index);
        }
        break;
      }
    }
    break;
  default:
    msg_internal_error("Unsupported measurement window type %d for window id %d",
                       window->meas_window_type, window->index);
    break;
  }
}


static dFLOAT meas_scaling[SRI_N_PREDEFINED_VARIABLE_TYPES];
static dFLOAT uds_meas_scaling[SRI_VARIABLE_NUM_UDS_VARS];

static VOID precompute_scaling_for_variables(VOID)
{
  static BOOLEAN scaling_precomputed_p = FALSE;
  if (scaling_precomputed_p)
    return;

  dFLOAT den_scale = 1.0/g_density_scale_factor;
#if BUILD_D19_LATTICE
  if(sim.is_pf_model) { 
    den_scale = 1.0;
  }
#endif

  ccDOTIMES(var, SRI_N_PREDEFINED_VARIABLE_TYPES) {

    switch (var) {
    case SRI_VARIABLE_DENSITY:
    case SRI_VARIABLE_XMOMENTUM:
    case SRI_VARIABLE_YMOMENTUM:
    case SRI_VARIABLE_ZMOMENTUM:
    case SRI_VARIABLE_MOMENTUM_MAG:
    case SRI_VARIABLE_XFORCE:
    case SRI_VARIABLE_YFORCE:
    case SRI_VARIABLE_ZFORCE:
    case SRI_VARIABLE_FORCE_MAG:
    case SRI_VARIABLE_FLUID_XFORCE:
    case SRI_VARIABLE_FLUID_YFORCE:
    case SRI_VARIABLE_FLUID_ZFORCE:
    case SRI_VARIABLE_FLUID_FORCE_MAG:
    case SRI_VARIABLE_XTORQUE:
    case SRI_VARIABLE_YTORQUE:
    case SRI_VARIABLE_ZTORQUE:
    case SRI_VARIABLE_TORQUE_MAG:
    case SRI_VARIABLE_KINETIC_XFORCE:
    case SRI_VARIABLE_KINETIC_YFORCE:
    case SRI_VARIABLE_KINETIC_ZFORCE:
    case SRI_VARIABLE_KINETIC_FORCE_MAG:
    case SRI_VARIABLE_PRESSURE:
    case SRI_VARIABLE_XPRESSURE_GRADIENT:
    case SRI_VARIABLE_YPRESSURE_GRADIENT:
    case SRI_VARIABLE_ZPRESSURE_GRADIENT:
    case SRI_VARIABLE_PRESSURE_GRADIENT_MAG:
    case SRI_VARIABLE_HEAT_FLUX:
    case SRI_VARIABLE_HEAT_GENERATION:
    case SRI_VARIABLE_HTC_CHAR_TEMP:
    case SRI_VARIABLE_HTC_NEAR_WALL_TEMP:
    case SRI_VARIABLE_MASS_FLUX:
    case SRI_VARIABLE_FLUID_XTORQUE:
    case SRI_VARIABLE_FLUID_YTORQUE:
    case SRI_VARIABLE_FLUID_ZTORQUE:
    case SRI_VARIABLE_FLUID_TORQUE_MAG:
    case SRI_VARIABLE_TFORCE_MAG:
    case SRI_VARIABLE_WATER_VAPOR_MFLUX:
    case SRI_VARIABLE_TIME_DERIV_PRESSURE:
      meas_scaling[var] = den_scale;
      break;
    case SRI_VARIABLE_STD_DEV_PRESSURE:
    case SRI_VARIABLE_STD_DEV_DENSITY:
      // scale factor is density^2 because we are actually accumulating variance
      meas_scaling[var] = den_scale * den_scale;
      break;
    case SRI_VARIABLE_N_SCREENED_SURFELS:
    case SRI_VARIABLE_SCREENED_AREA:
    case SRI_VARIABLE_AREA:
    case SRI_VARIABLE_N_SCREENED_VOXELS:
    case SRI_VARIABLE_SCREENED_VOLUME:
    case SRI_VARIABLE_VOLUME:
    case SRI_VARIABLE_STD_DEV_XVEL:
    case SRI_VARIABLE_STD_DEV_YVEL:
    case SRI_VARIABLE_STD_DEV_ZVEL:
    case SRI_VARIABLE_STD_DEV_VEL_MAG:
    case SRI_VARIABLE_STD_DEV_TEMP:
    case SRI_VARIABLE_WATER_VAPOR_MFRAC:
      meas_scaling[var] = 1.0;
      break;
    case SRI_VARIABLE_PRTCL_NUMBER:
    case SRI_VARIABLE_PRTCL_MEAN_MASS:
    case SRI_VARIABLE_PRTCL_MEAN_XVEL:
    case SRI_VARIABLE_PRTCL_MEAN_YVEL:
    case SRI_VARIABLE_PRTCL_MEAN_ZVEL:
    case SRI_VARIABLE_PRTCL_MEAN_VEL_MAG:
    case SRI_VARIABLE_PRTCL_MEAN_XVEL_INBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_YVEL_INBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_ZVEL_INBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_XVEL_OUTBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_YVEL_OUTBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_ZVEL_OUTBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_DENSITY:
    case SRI_VARIABLE_PRTCL_MEAN_DIAMETER:
    case SRI_VARIABLE_PRTCL_XFORCE:
    case SRI_VARIABLE_PRTCL_YFORCE:
    case SRI_VARIABLE_PRTCL_ZFORCE:
      meas_scaling[var] = 1.0;
      break;
    case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA:
    case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA_INBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_SURFACE_AREA_OUTBOUND:
      meas_scaling[var] = M_PI;  //surface area = pi d^2
      break;
    case SRI_VARIABLE_PRTCL_MEAN_VOLUME:
    case SRI_VARIABLE_PRTCL_MEAN_VOLUME_INBOUND:
    case SRI_VARIABLE_PRTCL_MEAN_VOLUME_OUTBOUND:
      meas_scaling[var] = M_PI / 6.0; //volume = pi/6 d^3
      break;
    default:
      meas_scaling[var] = 1.0;
      break;
    }
  }

  if (sim.uds_solver_type == LB_UDS) {
    ccDOTIMES(offset, SRI_VARIABLE_NUM_UDS_VARS) {
      switch (offset) {
	//case SRI_VARIABLE_UDS_SCALAR_OFFSET:
      case SRI_VARIABLE_UDS_SOURCE_TERM_OFFSET:
      case SRI_VARIABLE_UDS_SCALAR_FLUX_OFFSET:
	uds_meas_scaling[offset] = den_scale;
	break;
      default:
	uds_meas_scaling[offset] = 1.0;  //SRI_VARIABLE_UDS_SCALAR_OFFSET
	break;
      }
    }
  }

  scaling_precomputed_p = TRUE;
  return;
}

template <typename MEAS_FLOAT_TYPE>
static VOID convert_meas_vars_for_cp(MEAS_WINDOW window) {

  MEAS_CELL_VAR *sp_cell = window->meas_cells();
  MEAS_FLOAT_TYPE *cp_cell = (MEAS_FLOAT_TYPE *) window->convert_for_cp_buffer();

  // For +inf defrost time
  static const MEAS_FLOAT_TYPE inf = 1.0/0.0;

  if (!window->contains_std_dev_vars) {
    ccDOTIMES(i, window->n_stationary_meas_cells) {
      ccDOTIMES(j, window->n_variables) {
        SRI_VARIABLE_TYPE var_type = window->var_types[j];
        if(sim.cvid_helper->is_pm_var_id(var_type)) {
          *cp_cell++ = *sp_cell++;
          continue;
        } else if (sim.cvid_helper->is_uds_var_id(var_type)) {
	  int offset = (int)(sim.cvid_helper->get_uds_var_type(var_type));
	  *cp_cell++ = *sp_cell++ * uds_meas_scaling[offset];
	  continue;
        } else if(var_type >= sim.cvid_helper->get_first_unassigned_var_id()) { //Handle custom var type IDs that don't belong to the particle modeling, UDS, or 5G ID ranges.
          *cp_cell++ = *sp_cell++;
          continue;
        } else
#if BUILD_5G_LATTICE
        if (sim.cvid_helper->is_5g_var_id( (sriINT) var_type))
          *cp_cell++ = *sp_cell++;
        else
#endif
          if (var_type == SRI_VARIABLE_DEFROST_TIME_HELPER) {
            // If DEFROST_TIME_HELPER is non-zero, set DEFROST_TIME to +Inf
            if (*(sp_cell) != 0)
              *(cp_cell-1) = inf;
            sp_cell++; // This variable is reserved on the Sp to compute defrost time
          } else {
            *cp_cell++ = *sp_cell++ * meas_scaling[var_type];
          }
      }
    }
  } else {
    ccDOTIMES(i, window->n_stationary_meas_cells) {
      ccDOTIMES(j, window->n_variables) {
        SRI_VARIABLE_TYPE var_type = window->var_types[j];
        if(sim.cvid_helper->is_pm_var_id(var_type)) {
          *cp_cell++ = *sp_cell++;
          continue;
        } else if (sim.cvid_helper->is_uds_var_id(var_type)) {
	  int offset = (int)(sim.cvid_helper->get_uds_var_type(var_type));
	  *cp_cell++ = *sp_cell++ * uds_meas_scaling[offset];
	  continue;
	}
        switch (var_type) {
        case SRI_VARIABLE_STD_DEV_HELPER:
          sp_cell++; // This variable is space reserved on the SP to compute correct std dev variables
          break;
        case SRI_VARIABLE_DEFROST_TIME_HELPER:
          // If DEFROST_TIME_HELPER is non-zero, set DEFROST_TIME to +Inf
          if (*(sp_cell) != 0)
            *(cp_cell-1) = inf;
          sp_cell++; // This variable is reserved on the SP to compute defrost time
          break;
        default:
          *cp_cell++ = *sp_cell++ * meas_scaling[var_type];
          break;
        }
      }
      sp_cell++;
    }
  }
}

template<typename FLOAT_TYPE> struct float_type_to_int_type;

template<>
struct float_type_to_int_type<sFLOAT>
{
  typedef uINT32 type;
};

template<>
struct float_type_to_int_type<dFLOAT>
{
  typedef uINT64 type;
};


// main thread
template <typename MEAS_FLOAT_TYPE>
static VOID convert_moving_meas_vars_for_cp(MEAS_WINDOW window) {

  typedef typename float_type_to_int_type<MEAS_FLOAT_TYPE>::type MEAS_INT_TYPE;

  // sp_cell iterates through the buffer in its original
  // form, while cp_cell iterates through in the new form.  As long as the
  // sizeof(type) of cp_cell is <= the sizeof(type) of sp_cell, this is not a problem
  MEAS_FLOAT_TYPE *cp_cell = reinterpret_cast<MEAS_FLOAT_TYPE*> (window->m_moving_send_buffer);

  if (!window->contains_std_dev_vars) {
    tTHASH_DO(keyVar, valueVar, &window->m_moving_meas_cell_mgr->hash_table(), sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE) {
      sMOVING_MEAS_CELL_ID_AND_REF *meas_id_ref = reinterpret_cast<sMOVING_MEAS_CELL_ID_AND_REF*>(valueVar);
      // convert int meas cell index into float or double, converted back into meas
      // cell index on CP end
      *reinterpret_cast<MEAS_INT_TYPE*>(cp_cell) = meas_id_ref->global_meas_index;
      cp_cell++;
      //printf("cell %d: ", meas_id_ref->global_meas_index);
      valueVar++;

      ccDOTIMES(j, window->n_variables) {
        SRI_VARIABLE_TYPE var_type = window->var_types[j];
        //printf("\t%g", *valueVar);
	if (sim.cvid_helper->is_uds_var_id(var_type)) {
	  int offset = (int)(sim.cvid_helper->get_uds_var_type(var_type));
	  *cp_cell++ = *valueVar++ * uds_meas_scaling[offset];
	  continue;
	}
#if BUILD_5G_LATTICE
        if (sim.cvid_helper->is_5g_var_id(var_type))
          *cp_cell++ = *valueVar++;
        else
#endif
          *cp_cell++ = *valueVar++ * meas_scaling[var_type];
      }
      //printf("\n");
    }
  } else {
    tTHASH_DO(keyVar, valueVar, &window->m_moving_meas_cell_mgr->hash_table(), sGLOBAL_TO_MOVING_MEAS_CELL_HASH_TABLE) {
      MOVING_MEAS_CELL_ID_AND_REF meas_id_ref = MOVING_MEAS_CELL_ID_AND_REF(valueVar);
      // convert int meas cell index into float or double, converted back into meas cell index on CP end
      *reinterpret_cast<MEAS_INT_TYPE*>(cp_cell) = meas_id_ref->global_meas_index;
      cp_cell++;
      valueVar++;
      LOG_MSG_IF(window->is_composite,"BSURFEL_MEAS",LOG_TS,"window",window->index)
        << "Global Meas Index " << *reinterpret_cast<MEAS_INT_TYPE*>(cp_cell-1);
      ccDOTIMES(j, window->n_variables) {
        SRI_VARIABLE_TYPE var_type = window->var_types[j];
        switch (var_type) {
        case SRI_VARIABLE_STD_DEV_HELPER:
          valueVar++; // This variable is space reserved on the SP to compute correct std dev variables
          break;
        default:
          *cp_cell++ = *valueVar++ * meas_scaling[var_type];
          break;
        }
      }
    }
  }
}


VOID sMEAS_WINDOW::convert_moving_data_for_cp(VOID)
{
  precompute_scaling_for_variables();
  // use double precision data for tree reduction windows
  if (use_tree_reduction()) {
    convert_moving_meas_vars_for_cp<REDUCTION_MEAS_CELL_VAR>(this);
  }
  else if (is_meas_vars_output_dp) {
    convert_moving_meas_vars_for_cp<dFLOAT>(this);
  } else {
    convert_moving_meas_vars_for_cp<sFLOAT>(this);
  }
}

VOID sMEAS_WINDOW::convert_for_cp(VOID)
{
  precompute_scaling_for_variables();
  // use double precision data for tree reduction windows
  if (use_tree_reduction()) {
    convert_meas_vars_for_cp<REDUCTION_MEAS_CELL_VAR>(this);
  } else if (is_meas_vars_output_dp) {
    convert_meas_vars_for_cp<dFLOAT>(this);
  } else {
    convert_meas_vars_for_cp<sFLOAT>(this);
  }
}

STP_MEAS_CELL_INDEX sMEAS_WINDOW::n_moving_meas_cells()
{
  if (m_moving_meas_cell_mgr)
    return m_moving_meas_cell_mgr->n_moving_meas_cells();
  else
    return 0;
}

VOID sMEAS_WINDOW::allocate_moving_send_buffer() {
  uINT64 n_entries = n_send_moving_meas_cells * (n_cp_variables + 1);
  LOG_MSG_IF(this->is_composite,"BSURFEL_MEAS",LOG_TS,LOG_ATTR(n_entries));
  if (NULL == m_moving_send_buffer && n_entries > 0) {
    m_current_moving_buffer_size = 2 * n_entries;
    allocate_moving_send_buffer(m_current_moving_buffer_size);
  }
  else if ((n_entries > m_current_moving_buffer_size) ||
           (n_entries < (m_current_moving_buffer_size/2))) {
    delete [] m_moving_send_buffer;
    m_current_moving_buffer_size = 2 * n_entries;
    allocate_moving_send_buffer(m_current_moving_buffer_size);
  }
}

VOID sMEAS_WINDOW::allocate_moving_send_buffer(uINT64 n) {
  if (use_tree_reduction())
    m_moving_send_buffer = (CP_MEAS_CELL_VAR *) cnew REDUCTION_MEAS_CELL_VAR [n];
  else if (is_meas_vars_output_dp)
    m_moving_send_buffer = (CP_MEAS_CELL_VAR *) cnew dFLOAT [n];
  else
    m_moving_send_buffer = (CP_MEAS_CELL_VAR *) cnew sFLOAT [n];
}

VOID sMEAS_WINDOW::delete_moving_meas_cell(MOVING_MEAS_CELL_PTR moving_meas_cell) {
  LOG_MSG_IF(this->is_composite,"BSURFEL_MEAS",LOG_TS,"window",this->index) << "deleting meas cell";
  m_moving_meas_cell_mgr->delete_moving_meas_cell(moving_meas_cell);
}

size_t sMEAS_WINDOW::ckpt_len(const TIMESTEP current_timestep)
{
  size_t len = 0;
  // Trajectory windows do not need anything to be checkpointed.
  if(m_is_particle_trajectory_window)
    return 0;

  if ((n_stationary_meas_cells + n_global_moving_meas_cells) <= 0) // cannot be master if n_meas_cells <= 0
    return 0;

  // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
  // or if we have passed the window's end time.
  TIMESTEP clear_time = current_update_time.clear_time;
  if (m_is_master_sp) {
    //len += sizeof(clear_time);
    len += sizeof(nframes_sent);
  }

  if ((clear_time >= 0) && (clear_time < current_timestep)) {
    if (m_is_master_sp)
    {
      len += sizeof(sINT16);
      len += sizeof(sINT16);
    }

    if (is_ref_frame_master_sp) {
      len += sizeof(midpt);
      len += sizeof(avg_interval);
      if (sim.grf.is_defined) {
        len += sizeof(grf);
      }

      MEAS_WINDOW_LRF_INFO lrf = lrfs;
      ccDOTIMES(i, g_n_time_varying_lrfs) {
        len += sizeof(*lrf);
        lrf++;
      }

      MEAS_WINDOW_MOVB_INFO movb = movbs;
      ccDOTIMES(i, sim.n_movb_physics_descs) {
        len += sizeof(*movb);
        movb++;
      }

      MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = rotating_bcs;
      ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
        len += sizeof(rotating_bc->angular_vel);
        rotating_bc++;
      }

      MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = sliding_bcs;
      ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
        len += sizeof(sliding_bc->linear_vel);
        sliding_bc++;
      }
    }


    asINT32 cell_size = n_variables * sizeof(MEAS_CELL_VAR);
    if(contains_std_dev_vars){
    	cell_size += sizeof(MEAS_CELL_VAR);
    }

    if (n_stationary_meas_cells > 0) {
      len += (size_t)n_stationary_meas_cells * cell_size;
    }

    if (n_global_moving_meas_cells > 0) {
      STP_MEAS_CELL_INDEX n = n_moving_meas_cells();
      len += sizeof(n);
      if (n > 0) {
        len += m_moving_meas_cell_mgr->ckpt_len();
      }
    }
  }
  return len;
}

template <write_or_count_t WOC, typename T>
inline size_t write_or_count(sCKPT_BUFFER& pio_ckpt_buff, const T * const data, const size_t size)
{
  if constexpr (WOC == write_or_count_t::WRITE)
    pio_ckpt_buff.write(data, size);
  return size;
}

template <write_or_count_t WOC, typename T>
inline size_t write_or_count(sCKPT_BUFFER& pio_ckpt_buff, const T * const data)
{
  constexpr size_t size = sizeof(T);
  return write_or_count<WOC, T>(pio_ckpt_buff, data, size);
}

template <write_or_count_t WOC, typename T>
inline size_t write_or_count_array(sCKPT_BUFFER& pio_ckpt_buff, const T * const data, size_t num_elements)
{
  if (num_elements > 0)
    return write_or_count<WOC, T>(pio_ckpt_buff, data, sizeof(T) * num_elements);
  else
    return 0;
}

#define autowrite(VARNAME) \
pio_ckpt_buff.write(&VARNAME); \
std::cout << "\e[38;5;081m[   INFO  ] Thread: " << my_proc_id << " Win: " \
          << index << " -- " # VARNAME " =" << VARNAME << "\e[0m" << std::endl;

#ifndef _EXA_HPMPI
template <write_or_count_t WOC>
size_t sMEAS_WINDOW::woc_header(sCKPT_BUFFER& pio_ckpt_buff)
{
  size_t header_size = 0;
  if (is_ref_frame_master_sp)
  {
    header_size += write_or_count<WOC>(pio_ckpt_buff, &midpt);
    header_size += write_or_count<WOC>(pio_ckpt_buff, &avg_interval);
    if (sim.grf.is_defined) {
      header_size += write_or_count<WOC>(pio_ckpt_buff, &grf);
    }

    MEAS_WINDOW_LRF_INFO lrf = lrfs;
    ccDOTIMES(i, g_n_time_varying_lrfs) {
      header_size += write_or_count<WOC>(pio_ckpt_buff, lrf);
      lrf++;
    }

    MEAS_WINDOW_MOVB_INFO movb = movbs;
    ccDOTIMES(i, sim.n_movb_physics_descs) {
      header_size += write_or_count<WOC>(pio_ckpt_buff, movb);
      movb++;
    }

    MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = rotating_bcs;
    ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
      header_size += write_or_count<WOC>(pio_ckpt_buff, &rotating_bc->angular_vel);
      rotating_bc++;
    }

    MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = sliding_bcs;
    ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
      header_size += write_or_count<WOC>(pio_ckpt_buff, &sliding_bc->linear_vel);
      sliding_bc++;
    }
  }
  return header_size;
}

bool sMEAS_WINDOW::avoid_ckpt() const
{
  return is_average_mme || m_is_particle_trajectory_window;
}

bool sMEAS_WINDOW::has_meas_cells() const
{
  return  ( n_stationary_meas_cells + n_global_moving_meas_cells ) > 0;
}
void sMEAS_WINDOW::open_comm_with_cp(const TIMESTEP current_timestep)
{
  // Get current time and 
  hpc_io::utils::Timer debTimer;
  TIMESTEP clear_time = current_update_time.clear_time;
  const bool computeWin = (clear_time >= 0) && (clear_time < current_timestep); 
  
  // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "DEBUG open_comm_with_cp  computeWin=%d clear_time=%d current_tstep=%d\n", computeWin, clear_time, current_timestep);

  if (m_is_master_sp)
  {
    // Initialize essential data
    cp_comm.essential_data_send = {clear_time, nframes_sent, 0, 0};

    if (computeWin)
    {
      sCKPT_BUFFER dummy;
      sINT16 header_len = woc_header<write_or_count_t::COUNT>(dummy);
      sINT16 n_vars = n_variables;
    	if(contains_std_dev_vars){
    		n_vars++;
    	}

      // Including number of variables and the header length
      // into the data to be send to the CP
      cp_comm.essential_data_send.n_sp_variables = n_vars;
      cp_comm.essential_data_send.header_len = header_len;
    }

    // Sending essential data store in master SP
    MPI_Isend(&cp_comm.essential_data_send,
              sizeof(cp_comm.essential_data_send),
              MPI_BYTE,
              eMPI_sp_cp_rank(),
              eMPI_MEAS_WIN_SP_TO_CP_ESS_TAG,
              eMPI_sp_cp_comm,
              &cp_comm.request_send);
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "\033[0;35m DEBUG MPI_Isend eMPI_MEAS_WIN_SP_TO_CP_ESS_TAG\n\033[0m");
  }

  // Open comunications to receive esential data
  if (computeWin)
  {
    // Receiving essential data to process the maps
    MPI_Irecv(&cp_comm.essential_data_recv,
              sizeof(cp_comm.essential_data_recv),
              MPI_BYTE,
              eMPI_sp_cp_rank(),
              eMPI_MEAS_WIN_CP_TO_SP_ESS_TAG,
              eMPI_sp_cp_comm,
              &cp_comm.request_recv);
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "\033[0;31m DEBUG MPI_Irecv eMPI_MEAS_WIN_CP_TO_SP_ESS_TAG\n\033[0m");
  }
  // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "DEBUG open_comm_with_cp ended %f seconds\n", debTimer.getTime());
}

template <bool IS_BLOCKING>
void sMEAS_WINDOW::open_comm_recv_map(void * const srcMap0)
{
  MPI_Map_hdf5 * const srcMap = (MPI_Map_hdf5*) srcMap0;

  // Nothing to do if the map has already been allocated
  if (srcMap->allocated())
      return;

  hpc_io::utils::Timer debTimer;
  if constexpr (IS_BLOCKING)
  {
    // If is blocking, we have to wait until receive cp_comm.essential_data_recv ...
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "DEBUG before open_comm_with_cp MPI_Wait \n" );
    MPI_Wait(&cp_comm.request_recv, MPI_STATUS_IGNORE);
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "MWprof open_comm_with_cp_MPI_Wait took %f seconds\n", debTimer.getTime());

  }
  else
  {
    // If the non-blocking option is selected, a test is done. If the message has
    // not been received yet, there is nothing more to do
    int flag;
    MPI_Test(&cp_comm.request_recv, &flag, MPI_STATUS_IGNORE);
    if (!flag)
      return;
  }

  // Reset request for reseiving essential data from the CP
  cp_comm.request_recv = MPI_REQUEST_NULL;

  assert(cp_comm.essential_data_recv.map_length != 0);
  // Allocate data for the source map
  {
    MPI_Map_hdf5::raw_t * const dataPtr = new MPI_Map_hdf5::raw_t[cp_comm.essential_data_recv.map_size];

    // Assing data to the map
    srcMap->reasignPtr(dataPtr);
    srcMap->makeOwner();
    srcMap->clean();

    debTimer.restart();
    // Receive the map from CP
    MPI_Recv(dataPtr,
             cp_comm.essential_data_recv.map_size,
             MPI_BYTE,
             eMPI_sp_cp_rank(),
             eMPI_MEAS_WIN_CP_TO_SP_MAPS_TAG,
             eMPI_sp_cp_comm,
             MPI_STATUS_IGNORE);
    // hpc_io::utils::print<hpc_io::utils::SP>(my_proc_id, "\033[0;32m MWprof open_comm_with_cp_MPI_Recv eMPI_MEAS_WIN_CP_TO_SP_MAPS_TAG took %f seconds\n\033[0m", debTimer.getTime());
  }
}

void sMEAS_WINDOW::open_inter_comm(const TIMESTEP current_timestep)
{
  // By the moment, only data related moving cells is transferred among SPs
  if (n_global_moving_meas_cells == 0)
    return;
  
  // If the window won't be saved then there is nothing to do
  TIMESTEP clear_time = current_update_time.clear_time;
  if ((clear_time < 0) || (clear_time >= current_timestep))
    return;

  // Amount of data to be saved by each SP
  size_t raw_data_size = m_moving_meas_cell_mgr->ckpt_len();
  // Size in bytes of a single cell
  const size_t cell_size = m_moving_meas_cell_mgr->n_bytes_per_cell();
  // Max buffer size specified by the user (from MiB to bytes)
  const size_t raw_max_chunk_size = sim_run_info.parallel_io_max_buffer_size << 20;
  // Buffer size fit by an integer number of cells
  const size_t max_chunk_size = raw_max_chunk_size - (raw_max_chunk_size % cell_size);
  // Number of chunks to split in
  size_t num_chunks = (raw_data_size + max_chunk_size - cell_size) / max_chunk_size;
  // Header size
  constexpr size_t header_size = sizeof(my_proc_id) + sizeof(STP_MEAS_CELL_INDEX);

  // The first chunk includes also a header. If the header doesn't fit in remaining space
  // of a full chunk then the last cell of each chunk is moved to the next
  if (raw_max_chunk_size - max_chunk_size >= header_size)
    // If the moved cell does not fit into the last chunk, a new one is needed
    if (raw_max_chunk_size - (((raw_data_size + max_chunk_size - 1) % max_chunk_size) + 1) < cell_size)
      ++num_chunks;

  // Include the number of chunks needed to save the moving meas cells into the essential data
  cp_comm.essential_data_inter_send.num_chunks_mov_cells_per_sp = num_chunks;
  // Reshape the vector where to save the data coming from all the other SPs
  cp_comm.essential_data_inter_recv.resize(total_sps);
  // Gather data among all the processors
  assert(cp_comm.request_inter == MPI_REQUEST_NULL);
  MPI_Iallgather(&cp_comm.essential_data_inter_send,
                 sizeof(cp_comm.essential_data_inter_send),
                 MPI_BYTE,
                 cp_comm.essential_data_inter_recv.data(),
                 sizeof(cp_comm.essential_data_inter_send),
                 MPI_BYTE,
                 eMPI_sp_comm,
                 &cp_comm.request_inter);
}

void sMEAS_WINDOW::close_inter_comm(const TIMESTEP current_timestep)
{
  // By the moment, only data related moving cells is transferred among SPs
  if (n_global_moving_meas_cells == 0)
    return;

  // If the window won't be saved then there is nothing to do
  TIMESTEP clear_time = current_update_time.clear_time;
  if ((clear_time < 0) || (clear_time >= current_timestep))
    return;

  // Wait until gather is finished
  assert(cp_comm.request_inter != MPI_REQUEST_NULL);
  MPI_Wait(&cp_comm.request_inter, MPI_STATUS_IGNORE);
}

template <write_or_count_t WOC, bool IS_MASTER>
size_t sMEAS_WINDOW::generate_ckpt_src_map(sCKPT_BUFFER& pio_ckpt_buff,
                                           const TIMESTEP current_timestep,
                                           void * const srcMap0,
                                           int start_element_idx,
                                           int end_element_idx)
{
  // If there is no data to be stored assert
  cassert(!avoid_ckpt());
  
  // Type conversion of the pointer to the map type
  MPI_Map_hdf5 * const srcMap = (MPI_Map_hdf5*) srcMap0;

  // Initializing some variables
  size_t total_size = 0;
  size_t initial_offset = pio_ckpt_buff.m_offset;//deb purposes
  int chunk_idx = 0;

  // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
  // or if we have passed the window's end time.
  TIMESTEP clear_time = current_update_time.clear_time;

  if ((clear_time >= 0) && (clear_time < current_timestep))
  {
    if constexpr (IS_MASTER)
    {
      sINT16 header_len = woc_header<write_or_count_t::COUNT>(pio_ckpt_buff);
      sINT16 n_vars = n_variables;
    	if(contains_std_dev_vars){
    		n_vars++;
    	}

      // Add header to the total size
      total_size += header_len;

      // Write header into buffer. The header is the first index in the master node
      // (condition previously imposed)
      if constexpr (WOC == write_or_count_t::WRITE)
      {
        // Add header to the buffer
        if (start_element_idx == 0)
        {
          woc_header<write_or_count_t::WRITE>(pio_ckpt_buff);
          start_element_idx = 1;
        }

        // Substract one element (the header) to the indexes.
        // Now the indexes points to the elements in the array of the stationary cells.
        --start_element_idx;
        --end_element_idx;
      }
    }

    {
      // Writing data of the cells
      {
        // Number of variables per cell
        const size_t num_var_per_cell = (n_variables + (contains_std_dev_vars ? 1 : 0));
        
        // When calculating the total size, add the size of the full array
        if constexpr (WOC == write_or_count_t::COUNT)
        {
          total_size += write_or_count_array<WOC>(pio_ckpt_buff, meas_cells(),
                                                  num_var_per_cell * n_stationary_meas_cells);
        }
        // When saving data, add the size of the current chunk
        else
        {
          // When there is some stationary mes cell to be saved...
          if (start_element_idx < n_stationary_meas_cells)
          {
            int idx1 = end_element_idx > n_stationary_meas_cells ? n_stationary_meas_cells : end_element_idx;
            total_size += write_or_count_array<WOC>(pio_ckpt_buff,
                                                    meas_cells() + start_element_idx * num_var_per_cell,
                                                    num_var_per_cell * (idx1 - start_element_idx));
            
            // Indexes pointing to the moving meas cells...
            start_element_idx = 0;
            end_element_idx -= n_stationary_meas_cells;

            // If there is no more data to send to the buffer there is nothing more to do
            if (end_element_idx <= 0)
              return total_size;
          }
          else
          {
            start_element_idx -= n_stationary_meas_cells;
            end_element_idx   -= n_stationary_meas_cells;
          }
        }
      }

      // Opening communications to receive the maps (wait if the map data has not been received yet)
      if constexpr (WOC == write_or_count_t::COUNT)
      {
        if(n_stationary_meas_cells>0)
          open_comm_recv_map<true>(srcMap);
      }

      // Expands the map with the data of the moving measurement windows
      if (n_global_moving_meas_cells > 0)
      {
        // Last chunk contains all the data of the moving meas cells. Initilize its size here.
        MPI_Map_hdf5::sizes_t lastChunkSize = 0ul;

        // Get total number of moving cells
        STP_MEAS_CELL_INDEX n = n_moving_meas_cells();

        // When saving, only the first element saves the header of the moving cells:
        //  - Process ID
        //  - Number of moving cells
        if (WOC == write_or_count_t::COUNT || start_element_idx == 0)
        {
          // Saving index of current MPI process
          lastChunkSize += write_or_count<WOC>(pio_ckpt_buff, &my_proc_id);

          // Saving the total number of moving cells into current chunk
          lastChunkSize += write_or_count<WOC>(pio_ckpt_buff, &n);
        }

        if constexpr (WOC == write_or_count_t::WRITE) {
          if (n > 0) {
            // Initialize static variable with 0 when the first chunk that includes
            // the moving cells will be saved
            static THASH_INDEX first_movnig_cell_idx_to_save = -1;
            if (start_element_idx == 0)
              first_movnig_cell_idx_to_save = 0;
            assert(first_movnig_cell_idx_to_save >= 0);
            // Saving data...
            const THASH_INDEX init_idx = first_movnig_cell_idx_to_save;
            m_moving_meas_cell_mgr->write_ckpt(pio_ckpt_buff, first_movnig_cell_idx_to_save);
            lastChunkSize += m_moving_meas_cell_mgr->n_bytes_per_cell() * (first_movnig_cell_idx_to_save - init_idx);
          }
        }

        // Append moving meas cells as an additional chunk into the src map
        if constexpr (WOC == write_or_count_t::COUNT)
        {
          if ( n_stationary_meas_cells == 0)
            assert( cp_comm.essential_data_recv.last_map_idx == 0 );
          
          // Add size of the moving cells
          lastChunkSize += m_moving_meas_cell_mgr->ckpt_len();
          
          // Wait for communications among the SPs
          close_inter_comm(current_timestep);
          
          // Compute the number of chunks that exists in previous SPs (related with moving cells)
          size_t starting_chunk_idx = 0;
          for (int nn = 0; nn < my_proc_id; nn++)
            starting_chunk_idx += cp_comm.essential_data_inter_recv[nn].num_chunks_mov_cells_per_sp;

          // Compute how many chunks are needed to save the data related with moving cells
          size_t idx = cp_comm.essential_data_recv.last_map_idx + starting_chunk_idx;
          srcMap->appendElement<true>(idx, lastChunkSize);
        }

        // Adding size related to the moving measurement windows to the total size of the map
        total_size += lastChunkSize;
      }
    }
  }
    
  return total_size;
}

size_t sMEAS_WINDOW::ckpt_len_mapped(const TIMESTEP current_timestep,
                                     void * const srcMap0)
{
  sCKPT_BUFFER dummy;
  return m_is_master_sp ?
    generate_ckpt_src_map<write_or_count_t::COUNT, true >(dummy, current_timestep, srcMap0, 0, 0) :
    generate_ckpt_src_map<write_or_count_t::COUNT, false>(dummy, current_timestep, srcMap0, 0, 0);
}

size_t sMEAS_WINDOW::write_ckpt_mapped_master(sCKPT_BUFFER& pio_ckpt_buff,
                                              const TIMESTEP current_timestep,
                                              int start_element_idx,
                                              int end_element_idx)
{
  return generate_ckpt_src_map<write_or_count_t::WRITE, true >(pio_ckpt_buff, current_timestep, nullptr, start_element_idx, end_element_idx); 
}

size_t sMEAS_WINDOW::write_ckpt_mapped_ordinary(sCKPT_BUFFER& pio_ckpt_buff,
                                                const TIMESTEP current_timestep,
                                                int start_element_idx,
                                                int end_element_idx)
{
  return generate_ckpt_src_map<write_or_count_t::WRITE, false>(pio_ckpt_buff, current_timestep, nullptr, start_element_idx, end_element_idx);
}
#endif
VOID sMEAS_WINDOW::write_ckpt(const TIMESTEP current_timestep)
{

  // Trajectory windows do not need anything to be checkpointed.
  if(m_is_particle_trajectory_window)
    return;

  if ((n_stationary_meas_cells + n_global_moving_meas_cells) <= 0) // cannot be master if n_meas_cells <= 0
    return;

  // No need to checkpoint this window if the data will simply be cleared after the checkpoint-restore,
  // or if we have passed the window's end time.
  TIMESTEP clear_time = current_update_time.clear_time;
  if (m_is_master_sp) {
    write_ckpt_lgi(clear_time);
    write_ckpt_lgi(nframes_sent);
  }

  if ((clear_time >= 0) && (clear_time < current_timestep)) {
    sINT16 header_len = 0;
    if (is_ref_frame_master_sp) {
      header_len += sizeof(midpt);
      header_len += sizeof(avg_interval);
      if (sim.grf.is_defined)
        header_len += sizeof(grf);
      header_len += g_n_time_varying_lrfs * sizeof(*lrfs);
      header_len += sim.n_movb_physics_descs * sizeof(*movbs);
      header_len += g_n_time_varying_rotating_bcs * sizeof(rotating_bcs->angular_vel);
      header_len += g_n_time_varying_sliding_bcs  * sizeof(sliding_bcs->linear_vel);
    }

    if (m_is_master_sp) {
      sINT16 n_vars = n_variables;
    	if(contains_std_dev_vars){
    		n_vars++;
    	}
      write_ckpt_lgi(n_vars);
      write_ckpt_lgi(header_len);
    }

    if (is_ref_frame_master_sp) {
      write_ckpt_lgi(midpt);
      write_ckpt_lgi(avg_interval);
      if (sim.grf.is_defined) {
        write_ckpt_lgi(grf);
      }

      MEAS_WINDOW_LRF_INFO lrf = lrfs;
      ccDOTIMES(i, g_n_time_varying_lrfs) {
        write_ckpt_lgi(*lrf);
        lrf++;
      }

      MEAS_WINDOW_MOVB_INFO movb = movbs;
      ccDOTIMES(i, sim.n_movb_physics_descs) {
        write_ckpt_lgi(*movb);
        movb++;
      }

      MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = rotating_bcs;
      ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
        write_ckpt_lgi(rotating_bc->angular_vel);
        rotating_bc++;
      }

      MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = sliding_bcs;
      ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
        write_ckpt_lgi(sliding_bc->linear_vel, sizeof(sliding_bc->linear_vel));
        sliding_bc++;
      }
    }


    asINT32 cell_size = n_variables * sizeof(MEAS_CELL_VAR);
    if(contains_std_dev_vars){
    	cell_size += sizeof(MEAS_CELL_VAR);
    }

    if (n_stationary_meas_cells > 0) {
      write_ckpt_lgi(meas_cells(), (sINT64)n_stationary_meas_cells * cell_size);
    }

    if (n_global_moving_meas_cells > 0) {
      STP_MEAS_CELL_INDEX n = n_moving_meas_cells();
      write_ckpt_lgi(n);
      if (n > 0) {
        m_moving_meas_cell_mgr->write_ckpt();
      }
    }
  }
}

VOID sMEAS_WINDOW::read_ckpt()
{
  if(m_is_particle_trajectory_window)
    return;

  if ((n_global_stationary_meas_cells + n_global_moving_meas_cells) > 0) {
    TIMESTEP ckpt_clear_timestep;
    read_lgi(ckpt_clear_timestep);

    if (ckpt_clear_timestep >= 0
        && ckpt_clear_timestep < g_timescale.solver_time(sim.is_conduction_sp)) {
      if (is_ref_frame_master_sp) {
        read_lgi(midpt);
        read_lgi(avg_interval);
        if (sim.grf.is_defined) {
          read_lgi(grf);
          grf.get_examsg().reset();
        }

        MEAS_WINDOW_LRF_INFO lrf = lrfs;
        ccDOTIMES(i, g_n_time_varying_lrfs) {
          read_lgi(*lrf);
          lrf->get_examsg(i).reset();
          lrf++;
        }

        MEAS_WINDOW_MOVB_INFO movb = movbs;
        ccDOTIMES(i, sim.n_movb_physics_descs) {
          read_lgi(*movb);
          movb->get_examsg(i).reset();
          movb++;
        }

        MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = rotating_bcs;
        ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
          read_lgi(rotating_bc->angular_vel);
          rotating_bc->get_examsg(i).reset();
          rotating_bc++;
        }

        MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = sliding_bcs;
        ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
          read_lgi(sliding_bc->linear_vel, sizeof(sliding_bc->linear_vel));
          sliding_bc->get_examsg(i).reset();
          sliding_bc++;
        }
      }

      asINT32 cell_size = n_variables * sizeof(MEAS_CELL_VAR);
      if(contains_std_dev_vars){
      	cell_size += sizeof(MEAS_CELL_VAR);
      }

      if (n_stationary_meas_cells > 0) {
        read_lgi(meas_cells(), (sINT64)n_stationary_meas_cells * cell_size);
      }

      // read moving meas cell data
      if (n_global_moving_meas_cells > 0) {
        std::vector<MEAS_CELL_VAR> sp_meas_cell_buffer(n_variables);
        MEAS_CELL_VAR *sp_meas_cell = &(sp_meas_cell_buffer[0]);
        int64_t global_meas_cell_index;
        cell_size = (n_variables) * sizeof(MEAS_CELL_VAR);
        while (true) {
          read_lgi(&global_meas_cell_index, sizeof(global_meas_cell_index));
          if ( global_meas_cell_index == -1 ) {
            break;
          }

          
          sMOVING_MEAS_CELL_MGR* mgr = this->get_moving_meas_cell_mgr();

          // If the meas cell doesn't exist, then something has gone wrong The
          // meas cells should have been created during initialization of the
          // bsurfels. 
          //
          // Edit: I would like this to be the case, but CP can find the wrong ublk
          // for a bsurfel sometimes (when the centroid lands directly on a
          // ublk/voxel boundary), and the meas cell can go to the wrong SP.
          // This shouldn't happen very often, so we will live with it for now.
          // This can result in a case not being bit-for-bit repeatable upon resume.
          
          bool orphaned = false;
          if ( !mgr->has_moving_meas_cell(global_meas_cell_index) ) {
            orphaned = true;
          }

          sMOVING_MEAS_CELL_SMART_PTR moving_meas_cell_ptr = mgr->get_moving_meas_cell(global_meas_cell_index);


          MEAS_CELL_VAR * moving_meas_cell = moving_meas_cell_ptr.variables();
          read_lgi(sp_meas_cell_buffer.data(), cell_size);

          // accumulate the result onto the meas cell
          for(int i=0; i < n_variables; i++) {
            moving_meas_cell[i+1] += moving_meas_cell[i];
          }

          if (orphaned) {
            mgr->add_orphaned_moving_meas_cell(std::move(moving_meas_cell_ptr));
          }
        }
      }
    }
  }
}

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/

static VOID silent_eqn_error_handler(EQN_ERROR_TYPE type, PHYSICS_VARIABLE physics_var,
                                     cSTRING cdi_desc_var_name, vFLOAT value,
                                     vFLOAT aux_value,         // min or max
                                     vFLOAT _point[3])         // unused
{ }

MEAS_WINDOW sMEAS_WINDOW_COLLECTION::next_output_window(bool do_surface_windows, TIMESTEP num_timesteps_done)
{
  if (!m_output_queue.empty()) {
    MEAS_WINDOW head = m_output_queue.top();
    if ( head->current_update_time.output_time <= num_timesteps_done ) {
      bool same_type = do_surface_windows == is_lgi_meas_window_type_surface(head->meas_window_type);
      if ( same_type ) {
        m_output_queue.pop();
        return head;
      }
    }
  }
  return nullptr;
}

MEAS_WINDOW sMEAS_WINDOW_COLLECTION::next_clear_window(bool do_surface_windows, TIMESTEP num_timesteps_done)
{
  if (!m_clear_queue.empty()) {
    MEAS_WINDOW head = m_clear_queue.top();
    if ( head->current_update_time.clear_time == num_timesteps_done ) {
      bool same_type = do_surface_windows == is_lgi_meas_window_type_surface(head->meas_window_type);
      if (same_type) {
        m_clear_queue.pop();
        return head;
      }
    }
  }
  return nullptr;
}

void sMEAS_WINDOW::wait_for_previous_send_to_complete()
{
  pthread_mutex_lock(&m_previous_send_mutex);
  while(!m_all_send_buffers_ready)
  {
    pthread_cond_wait(&m_previous_send_cv, &m_previous_send_mutex);
  }
  m_all_send_buffers_ready = false;
  pthread_mutex_unlock(&m_previous_send_mutex);
}

void get_window_ready_for_sending(MEAS_WINDOW window)
{
    if (window->is_ref_frame_master_sp
        && (sim.grf.is_time_varying || g_n_time_varying_lrfs > 0 || g_n_time_varying_mbcs > 0 || sim.is_movb_sim() ))
      prepare_meas_window_ref_frame_info(window);

    if (window->n_stationary_meas_cells > 0) {
#if BUILD_GPU
      // On the GPU, for repeatable measurements, we have to call an additional 
      // reduction kernel. The comm thread will copy the results from Device to Host
      // and then run convert_for_cp()
      window->reduce_and_transpose_stationary_meas_cells_on_gpu();
#else 
      window->convert_for_cp();
#endif
    }

    // moving measurement cells
    if (window->n_global_moving_meas_cells > 0) {
      LOG_MSG_IF(window->is_composite,"BSURFEL_MEAS",LOG_TIME,"TS",g_timescale.m_time,"window",window->index) << "MEAS_DO_OUTPUT Moving send buffer ready FALSE";

      window->n_send_moving_meas_cells = window->n_moving_meas_cells();
      LOG_MSG_IF(window->is_composite,"BSURFEL_MEAS",LOG_TS,"window",window->index,LOG_ATTR(window->n_send_moving_meas_cells));
      window->allocate_moving_send_buffer();
      window->convert_moving_data_for_cp();
    }

    if (window->m_is_master_sp)
      window->nframes_sent++;

    LOG_MSG_IF(window->is_composite,"BSURFEL_MEAS","TS",g_timescale.m_time,"window",window->index) << "Adding window to send_queue";

}

// called by the sim thread
VOID meas_do_output(const BOOLEAN do_surface_windows, const TIMESTEP num_timesteps_done)
{
  if (!g_meas_windows.n_meas_windows())
    return;

  while ( MEAS_WINDOW window = g_meas_windows.next_output_window(do_surface_windows, num_timesteps_done) ) 
  {

    TIMESTEP current_output_time = window->current_update_time.output_time;

    // Before we do anything, make sure the previous send has completed
    window->wait_for_previous_send_to_complete();

    if (window->is_average_mme)
      continue;

    // For supporting variable PowerTherm coupling periods, check if the next phase starts
    asINT32 n_meas_phases = window->m_meas_phase_descs.size();
    if (n_meas_phases > 0 && current_output_time > window->end_time)
      continue;

    ccDO_FROM_TO(phase_index, 1, n_meas_phases-1) {
      if (num_timesteps_done == window->m_meas_phase_descs[phase_index].m_output_time) {
        TIMESTEP new_period = window->m_meas_phase_descs[phase_index].m_period;
        // update the measurement phase
        // adjust the next clear and output time so that compute_next_time gets the correct values
        window->m_time_desc.interval = window->m_meas_phase_descs[phase_index].m_interval;
        window->m_time_desc.period = new_period;
        // Note that time_desc.start is the start of the measurement, not the start of the period!
        window->m_time_desc.start = window->m_meas_phase_descs[phase_index].m_output_time + new_period - window->m_time_desc.interval;
        window->m_time_desc.repeat = window->m_meas_phase_descs[phase_index].m_repeat;
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
        msg_print("");
        msg_print("SP: at timestep %ld changed phase to interval %d period %d repeat %d", g_timescale.m_time, window->m_time_desc.interval, new_period,
                  window->m_time_desc.repeat);
        msg_print("");
#endif
        window->next_update_time.output_time = window->current_update_time.output_time + new_period;
        window->next_update_time.clear_time = window->next_update_time.output_time - window->m_time_desc.interval;
      }
    }

    // compute next output and clear times (in base steps)
    window->current_update_time = window->next_update_time;
    if (window->current_update_time.output_time != TIMESTEP_NEVER) {
      TIMESTEP next_output_time = window->next_update_time.output_time;
      STP_REALM realm = lgi_meas_window_type_realm(window->meas_window_type);
      window->next_update_time.output_time = compute_next_time(next_output_time, next_output_time,
                                                               &window->m_time_desc,
                                                               &window->next_update_time.clear_time, NULL);
#if DEBUG_VARIABLE_POWERTHERM_COUPLING
      if (n_meas_phases > 0)
        msg_print("SP: insert meas window %d-%d into queue", window->current_update_time.clear_time, window->current_update_time.output_time);
#endif
      g_meas_windows.insert_in_output_queue(window);
      g_meas_windows.insert_in_clear_queue(window);
    }

    // Get everything ready for the comm thread     
    get_window_ready_for_sending(window);

    g_strand_mgr.m_meas_send_queue->add_entry(window);
  }
}


/*--------------------------------------------------------------------------*
 * meas_do_clear *
 * Clears each meas window that is supposed to be cleared on this timestep.
 *--------------------------------------------------------------------------*/
VOID meas_do_clear(const BOOLEAN do_surface_windows, const TIMESTEP num_timesteps_done)
{
  if (!g_meas_windows.n_meas_windows())
    return;

  MEAS_WINDOW_COLLECTION meas_windows = &g_meas_windows;

  while (MEAS_WINDOW window = g_meas_windows.next_clear_window(do_surface_windows, num_timesteps_done) ) {

    if (window->is_average_mme)
      continue;

    window->clear_stationary_meas_cells();
    window->clear_moving_meas_cells();

    if (window->is_ref_frame_master_sp) {
      window->midpt = (num_timesteps_done + window->current_update_time.output_time) / 2;
      window->avg_interval = window->current_update_time.output_time - num_timesteps_done;

      if (sim.grf.is_time_varying) {
        vzero(window->grf.angular_vel);
        window->grf.axis_change_mark_at_start = sim.grf.axis_change_mark;
        window->grf.axis_changed = FALSE;
        window->grf.angle_at_start = sim.grf.angle_rotated;

        vzero(window->grf.translation);
        window->grf.ref_pt_vel_dir_change_mark_at_start = sim.grf.ref_pt_vel_dir_change_mark;
        window->grf.ref_pt_vel_dir_changed = FALSE;
        vzero(window->grf.ref_pt_vel);
      }

      MEAS_WINDOW_LRF_INFO lrf = window->lrfs;
      sriLRF_INDEX *lrf_physics_desc_index = g_time_varying_lrf_physics_desc_indices;
      ccDOTIMES(i, g_n_time_varying_lrfs) {
        lrf->angular_vel = 0;
        lrf->n_revolutions_at_start = sim.lrf_physics_descs[*lrf_physics_desc_index].n_revolutions;
        lrf->angle_at_start = sim.lrf_physics_descs[*lrf_physics_desc_index++].angle_rotated;
        lrf++;
      }

      MEAS_WINDOW_MOVB_INFO movb = window->movbs;
      ccDOTIMES(i, sim.n_movb_physics_descs) {
        movb->angle_at_start = sim.movb_physics_descs[i].angle_rotated;
        movb->angle_rotated = 0;
        movb++;
      }

      if (g_n_time_varying_mbcs > 0) {
        MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = window->rotating_bcs;
        ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
          rotating_bc->angular_vel = 0;
          rotating_bc++;
        }
        MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = window->sliding_bcs;
        ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
          sliding_bc->linear_vel[0] = 0;
          sliding_bc->linear_vel[1] = 0;
          sliding_bc->linear_vel[2] = 0;
          sliding_bc++;
        }
      }
    }
  }

  // Should only do this once per timestep in the last meas_do_output() call in time update strand
  // Currently the last one is for fluid meas windows, thus the following code should only be done
  // if do_surface_windows is FALSE
  if (!do_surface_windows) {
    // @@@ We should create a vector of meas windows with window->is_ref_frame_master_sp == TRUE
    BOOLEAN first_is_ref_frame_master_sp = TRUE;
    DO_MEAS_WINDOWS(window) {
      if (window->is_ref_frame_master_sp) {
        if (sim.grf.is_time_varying) {
          vinc(window->grf.angular_vel, sim.grf.angular_vel);
          if (window->grf.axis_change_mark_at_start != sim.grf.axis_change_mark)
            window->grf.axis_changed = TRUE;

          vinc(window->grf.translation, sim.grf.ground_to_global_translation);
          if (window->grf.ref_pt_vel_dir_change_mark_at_start != sim.grf.ref_pt_vel_dir_change_mark)
            window->grf.ref_pt_vel_dir_changed = TRUE;

          vinc(window->grf.ref_pt_vel, sim.grf.ref_pt_vel);

          if (window->midpt == num_timesteps_done) {
            ccDOTIMES(i, 4)
              window->grf.quaternion_at_midpt[i] = sim.grf.quaternion_inverse[i];

            vcopy(window->grf.ref_pt_vel_at_midpt, sim.grf.ref_pt_vel);
            vcopy(window->grf.translation_at_midpt, sim.grf.ground_to_global_translation);
          }
        }

        MEAS_WINDOW_LRF_INFO lrf = window->lrfs;
        sriLRF_INDEX *lrf_physics_desc_index = g_time_varying_lrf_physics_desc_indices;
        ccDOTIMES(i, g_n_time_varying_lrfs) {
          lrf->angular_vel += sim.lrf_physics_descs[*lrf_physics_desc_index++].omega;
          lrf++;
        }

        MEAS_WINDOW_MOVB_INFO movb = window->movbs;
        ccDOTIMES(i, sim.n_movb_physics_descs) {
          // explicitly average the angle rotated, since it is not clear how to average a full 4x4 xform
          movb->angle_rotated += sim.movb_physics_descs[i].parameters()->angular_vel.value;
          movb++;
        }

        if (g_n_time_varying_mbcs > 0) {
          MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = window->rotating_bcs;
          ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
            if (first_is_ref_frame_master_sp)
              g_time_varying_rotating_bc_pds[i]->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, silent_eqn_error_handler);
            rotating_bc->angular_vel += rotating_bc->angular_vel_var->value;
            rotating_bc++;
          }
          MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = window->sliding_bcs;
          ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
            if (first_is_ref_frame_master_sp)
              g_time_varying_sliding_bc_pds[i]->eval_time_varying_only_parameter_program(g_timescale.m_time, g_timescale.m_powertherm_time, silent_eqn_error_handler);
            sliding_bc->linear_vel[0] += sliding_bc->linear_vel_vars[0].value;
            sliding_bc->linear_vel[1] += sliding_bc->linear_vel_vars[1].value;
            sliding_bc->linear_vel[2] += sliding_bc->linear_vel_vars[2].value;
            sliding_bc++;
          }
        }
        first_is_ref_frame_master_sp = FALSE;
      }
    }
  }
}

// The order of meas windows in each SP's output queue must match the order of windows
// in the CP's output queue.
bool sMEAS_WINDOW_OUTPUT_TIME_COMPARE::eval(const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const
{
  TIMESTEP lhs_output_time = lhs->current_update_time.output_time;
  TIMESTEP rhs_output_time = rhs->current_update_time.output_time;

  if ( lhs_output_time < rhs_output_time ) {
    return true;
  }
  else if ( lhs_output_time == rhs_output_time ) {
    bool lhs_is_surface = is_lgi_meas_window_type_surface(lhs->meas_window_type);
    bool rhs_is_surface = is_lgi_meas_window_type_surface(rhs->meas_window_type);
    bool same_type = (lhs_is_surface == rhs_is_surface);

    if (same_type) {
      TIMESTEP lhs_next_time = lhs->next_update_time.output_time;
      TIMESTEP rhs_next_time = rhs->next_update_time.output_time;

      // The <= is NOT a typo!
      return lhs_next_time <= rhs_next_time;
    }

    // surface windows come first and break the tie
    if (lhs_is_surface && !rhs_is_surface) {
      return true;
    }
  }

  return false;
}

bool sMEAS_WINDOW_CLEAR_TIME_COMPARE::eval (const sMEAS_WINDOW* lhs, const sMEAS_WINDOW* rhs) const
{
  TIMESTEP lhs_clear_time = lhs->current_update_time.clear_time;
  TIMESTEP rhs_clear_time = rhs->current_update_time.clear_time;

  if ( lhs_clear_time < rhs_clear_time ) {
    return true;
  }
  else if ( lhs_clear_time == rhs_clear_time ) {
    bool lhs_is_surface = is_lgi_meas_window_type_surface(lhs->meas_window_type);
    bool rhs_is_surface = is_lgi_meas_window_type_surface(rhs->meas_window_type);
    bool same_type = (lhs_is_surface == rhs_is_surface);

    if (same_type) {
      return true;
    }

    if (lhs_is_surface && !rhs_is_surface) {
      return true;
    }
  }
  return false;
}

void sMEAS_WINDOW_COLLECTION::insert_in_output_queue(MEAS_WINDOW window)
{
  if ( window->current_update_time.output_time >= 0 ) {
    m_output_queue.push(window);
  }
}

void sMEAS_WINDOW_COLLECTION::insert_in_clear_queue(MEAS_WINDOW window)
{
  if ( window->current_update_time.clear_time >= 0 ) {
    m_clear_queue.push(window);
  }
}

void sMEAS_WINDOW_COLLECTION::insert_in_output_and_clear_queues_if_non_empty(MEAS_WINDOW window)
{
  if ((window->n_stationary_meas_cells + window->n_global_moving_meas_cells) > 0) {
    insert_in_output_queue(window);
    if (window->current_update_time.clear_time >= g_timescale.solver_time(sim.is_conduction_sp)) {
      insert_in_clear_queue(window);
    }
  }
}

VOID sMEAS_WINDOW::clear_stationary_meas_cells() {
#if BUILD_GPU
  clear_stationary_meas_cells_on_gpu();
#else
  ccDOTIMES(n, n_stationary_meas_cells) {
    memset(meas_cell(n), 0, (sINT64)this->meas_cell_size());
  }
#endif
}

VOID sMEAS_WINDOW::clear_moving_meas_cells() {
#if BUILD_GPU
#else
  if (m_moving_meas_cell_mgr) {
    m_moving_meas_cell_mgr->clear_moving_meas_cells();
  }
#endif
}

sMOVING_MEAS_CELL_SMART_PTR sMEAS_WINDOW::get_moving_meas_cell(STP_MEAS_CELL_INDEX global_meas_cell_index, sINT16 face_index, bool init)
{
  if ( !is_development ) {
    return m_moving_meas_cell_mgr->get_moving_meas_cell(global_meas_cell_index);
  }
  else {
    // The CP encodes the global meas surfel index and the axis into global_meas_cell_index
    STP_MEAS_CELL_INDEX global_meas_surfel_index_plus_axis = global_meas_cell_index;
    STP_MEAS_CELL_INDEX global_meas_surfel_index;
    uINT8 axis;
    lgi_meas_cell_index_dev_segment(global_meas_surfel_index_plus_axis, global_meas_surfel_index, axis);

    STP_MEAS_CELL_INDEX meas_cell_index;
    if (init) {
      meas_cell_index = this->create_surfel_meas_cell(global_meas_surfel_index);
    }
    else {
      meas_cell_index = this->find_meas_cell(global_meas_surfel_index);
      // Migrating bsurfels must have a development window meas cell already
      // present on the local SP
      if (meas_cell_index < 0) {
        msg_internal_error("Invalid meas cell index for development window %d", this->index);
      }
    }

    sMOVING_MEAS_CELL_SMART_PTR meas_cell_ref(this->index, global_meas_cell_index);
    meas_cell_ref.set_index(meas_cell_index);
    meas_cell_ref.set_axis(axis);
    meas_cell_ref.set_face(face_index);
    asINT32 n_segments = this->entity_n_segments[axis][face_index];
    for (asINT32 j=1; j < n_segments; j++) {
      this->create_surfel_meas_cell(global_meas_surfel_index + j);
    }

    return meas_cell_ref;
  }
}

VOID sMEAS_WINDOW::append_to_send_queue()
{
  MEAS_WINDOW window = this;
  MEAS_WINDOW_COLLECTION meas_windows = &g_meas_windows;
  window->m_next_in_send_queue = NULL;
  if (meas_windows->m_send_queue == NULL) {
    meas_windows->m_send_queue = meas_windows->m_send_queue_tail = window;
  } else {
    meas_windows->m_send_queue_tail->m_next_in_send_queue = window;
    meas_windows->m_send_queue_tail = window;
  }
}

VOID sim_finalize_meas()
{
  DO_MEAS_WINDOWS(window) {
    ccDOTIMES(i, window->m_n_child_sps) {
      if(window->m_child_receive_requests[i] != MPI_REQUEST_NULL)
        MPI_Cancel(&window->m_child_receive_requests[i]);
    }
  }
}

MPI_Datatype eMPI_GRF_MEAS_FRAME_SP_TO_CP_MSG;
MPI_Datatype eMPI_LRF_MEAS_FRAME_SP_TO_CP_MSG;
MPI_Datatype eMPI_MBC_MEAS_FRAME_SP_TO_CP_MSG;
MPI_Datatype eMPI_MOVB_MEAS_FRAME_SP_TO_CP_MSG;

VOID prepare_meas_window_ref_frame_info(MEAS_WINDOW window)
{
  dFLOAT one_over_avg_interval = 1.0/window->avg_interval;

  static cBOOLEAN mpi_types_initialized_p = FALSE;

  if (!mpi_types_initialized_p) {
    sGRF_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_GRF_MEAS_FRAME_SP_TO_CP_MSG);
    sLRF_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_LRF_MEAS_FRAME_SP_TO_CP_MSG);
    sMBC_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_MBC_MEAS_FRAME_SP_TO_CP_MSG);
    sMOVB_MEAS_FRAME_SP_TO_CP_MSG::mpi_type_init(&eMPI_MOVB_MEAS_FRAME_SP_TO_CP_MSG);
    mpi_types_initialized_p = TRUE;
  }

  // Only send info for the global ref frame if it is defined and time-varying
  if (sim.grf.is_time_varying) {
    sGRF_MEAS_FRAME_SP_TO_CP_MSG *grf_send_msg = window->grf.get_msg();

    // The ref point velocity is only used to transform velocities from global to
    // ground frame (and vice versa).
    if (window->grf.ref_pt_vel_dir_changed) {
      // If the direction of the ref point velocity changed during this meas frame, we use
      // the ref point velocity at the midpoint to ensure the direction corresponds to an
      // actual direction during the meas frame. Using the component-by-component average
      // would not ensure this.
      vcopy(grf_send_msg->ref_point_velocity, window->grf.ref_pt_vel_at_midpt);
    } else {
      vcopy(grf_send_msg->ref_point_velocity, window->grf.ref_pt_vel);
      vscale(grf_send_msg->ref_point_velocity, one_over_avg_interval, grf_send_msg->ref_point_velocity);
    }

    // This predicate needs to be identical to the predicate used below to decide whether to use
    // window->grf.quaternion_at_midpt to determine the axis and angle rotated.
    if (window->grf.ref_pt_vel_dir_changed
        || window->grf.axis_changed) {     // did axis direction or origin change
      // If the ref vel direction changed or there was rotation during this meas frame,
      // we use the translation at the midpoint to ensure it places us on the actual
      // trajectory of the global ref frame.
      vcopy(grf_send_msg->translation, window->grf.translation_at_midpt);
    } else {
      // The trajectory of the global ref frame is a straight line, so we use the average
      // translation relative to the ground frame during the meas frame.
      vcopy(grf_send_msg->translation, window->grf.translation);
      vscale(grf_send_msg->translation, one_over_avg_interval, grf_send_msg->translation);
    }

    if (sim.grf.is_rotation) {
      vcopy(grf_send_msg->angular_vel, window->grf.angular_vel); // accumulated over averaging interval
      vscale(grf_send_msg->angular_vel, one_over_avg_interval, grf_send_msg->angular_vel);

      // This predicate needs to be identical to the predicate used above to decide whether to use
      // window->grf.translation_at_midpt to determine the translation.
      if (window->grf.axis_changed   // did axis direction or origin change
          || window->grf.ref_pt_vel_dir_changed) {
        // Use the global ref frame quaternion at the midpoint of the averaging interval
        // to define the effective axis and angle.
        dFLOAT angle_over_2 = acos(window->grf.quaternion_at_midpt[0]);
        if (angle_over_2 == 0) {
          grf_send_msg->angle_rotated = 0;
          grf_send_msg->axis[0] = 0;
          grf_send_msg->axis[1] = 0;
          grf_send_msg->axis[2] = 1; // z-axis makes sense in both 2D and 3D
        } else {
          grf_send_msg->angle_rotated = 2 * angle_over_2;
          // extract unit axis
          dFLOAT sin_angle_over_2 = sqrt(vdot(&window->grf.quaternion_at_midpt[1],
                                              &window->grf.quaternion_at_midpt[1]));
          vscale(grf_send_msg->axis, 1 / sin_angle_over_2, &window->grf.quaternion_at_midpt[1]);
        }
      } else {
        vcopy(grf_send_msg->axis, sim.grf.last_axis);

        // Convert omega from rad/LatTimeInc to rad/timestep for rotation angle calculation
        dFLOAT lat_time_inc_per_timestep = g_adv_fraction;
        dFLOAT omega = lat_time_inc_per_timestep * sqrt(vdot(window->grf.angular_vel, window->grf.angular_vel));
        grf_send_msg->angle_rotated = window->grf.angle_at_start + omega * 0.5;

        if (grf_send_msg->angle_rotated < 0)
          grf_send_msg->angle_rotated += 2.0 * PI;
        else if (grf_send_msg->angle_rotated >= 2.0 * PI)
          grf_send_msg->angle_rotated -= 2.0 * PI;
      }

      vzero(grf_send_msg->point);

    } else {
      vzero(grf_send_msg->angular_vel);
      vzero(grf_send_msg->axis);
      vzero(grf_send_msg->point);
      grf_send_msg->angle_rotated = 0;
    }
  }

  // Send info for all local ref frames
  sriLRF_INDEX *lrf_physics_desc_index = g_time_varying_lrf_physics_desc_indices;
  MEAS_WINDOW_LRF_INFO lrf = window->lrfs;
  ccDOTIMES(i, g_n_time_varying_lrfs) {
    LRF_MEAS_FRAME_SP_TO_CP_MSG lrf_send_msg = lrf->get_msg(i);

    if (!sim.lrf_physics_descs[*lrf_physics_desc_index++].is_mlrf_on) {
      lrf_send_msg->angle_rotated = 0;
      lrf_send_msg->n_revolutions = 0;
    } else {
      // Convert angular_vel from rad/LatTimeInc to rad/timestep for rotation angle calculation
      dFLOAT lat_time_inc_per_timestep = g_adv_fraction;
      lrf_send_msg->angle_rotated = lrf->angle_at_start + 0.5 * lat_time_inc_per_timestep * lrf->angular_vel;
      lrf_send_msg->n_revolutions = lrf->n_revolutions_at_start;
      if (lrf_send_msg->angle_rotated < 0) {
        lrf_send_msg->angle_rotated += 2.0 * PI;
        lrf_send_msg->n_revolutions --;
      }
      else if (lrf_send_msg->angle_rotated >= 2.0 * PI) {
        lrf_send_msg->angle_rotated -= 2.0 * PI;
        lrf_send_msg->n_revolutions ++;
      }
    }

    lrf_send_msg->angular_vel_mag = lrf->angular_vel * one_over_avg_interval;
    lrf++;
  }

  // Send info for all rotating BCs
  MEAS_WINDOW_ROTATING_BC_INFO rotating_bc = window->rotating_bcs;
  ccDOTIMES(i, g_n_time_varying_rotating_bcs) {
    rotating_bc->get_msg(i)->linear_vel[0] = 0;
    rotating_bc->get_msg(i)->linear_vel[1] = 0;
    rotating_bc->get_msg(i)->linear_vel[2] = 0;
    rotating_bc->get_msg(i)->angular_vel_mag = rotating_bc->angular_vel * one_over_avg_interval;
    rotating_bc++;
  }

  // Send info for all sliding BCs
  MEAS_WINDOW_SLIDING_BC_INFO sliding_bc = window->sliding_bcs;
  ccDOTIMES(i, g_n_time_varying_sliding_bcs) {
    sliding_bc->get_msg(i)->angular_vel_mag = 0;
    sliding_bc->get_msg(i)->linear_vel[0] = sliding_bc->linear_vel[0] * one_over_avg_interval;
    sliding_bc->get_msg(i)->linear_vel[1] = sliding_bc->linear_vel[1] * one_over_avg_interval;
    sliding_bc->get_msg(i)->linear_vel[2] = sliding_bc->linear_vel[2] * one_over_avg_interval;
    sliding_bc++;
  }

  MEAS_WINDOW_MOVB_INFO movb = window->movbs;
  MOVB_PHYSICS_DESCRIPTOR movb_physics_desc = sim.movb_physics_descs;
  ccDOTIMES(n, sim.n_movb_physics_descs) {
    // Convert angular_vel from rad/LatTimeInc to rad/timestep for xform calculation
    dFLOAT lat_time_inc_per_timestep = g_adv_fraction;
    dFLOAT average_angle_rotated = movb->angle_at_start +
      0.5 * lat_time_inc_per_timestep * movb->angle_rotated;
    dFLOAT average_angle_rotated_raw = average_angle_rotated;
    // TODO: For 6-DOF rigid body motion, the rotation and translation should
    // be averaged independently. For rotation, if the axis of rotation is
    // varying with time, then special care would be needed to handle the
    // angle and axis separately while averaging.

    if (average_angle_rotated < 0.0F)
      average_angle_rotated += 2.0F * PI;
    else if (average_angle_rotated >= 2.0F * PI)
      average_angle_rotated -= 2.0F * PI;

    // Implement the xform computation in sim.cc:update_movb_xforms()
    dFLOAT ref_pt[3], unit_vec[3];
    ccDOTIMES(axis, 3) {
      ref_pt[axis] = movb_physics_desc->parameters()->axis_point[axis].value;
      unit_vec[axis] = movb_physics_desc->parameters()->axis_unit_vec[axis].value;
    }

    sBG_VECTOR3d bg_pt_on_axis( ref_pt[0], ref_pt[1], ref_pt[2] );
    sBG_TRANSFORM3d xform_to_origin( BG_TRANSLATION, -bg_pt_on_axis );
    sBG_TRANSFORM3d xform_from_origin( BG_TRANSLATION, bg_pt_on_axis );
    sBG_VECTOR3d axis_vec( unit_vec[0], unit_vec[1], unit_vec[2] );
    sBG_TRANSFORM3d xform_rotate( BG_ROTATION, axis_vec, average_angle_rotated );
    sBG_TRANSFORM3d full_xform = xform_from_origin * xform_rotate * xform_to_origin;
    ccDOTIMES(i, 4) {
      ccDOTIMES(j, 4) {
        movb->get_msg(n)->xform[i][j] = full_xform.M(i,j);
      }
    }
    // Stuff the angle rotated into the unused (3,0) xform matrix entry
    movb->get_msg(n)->xform[3][0] = average_angle_rotated_raw;
  }
}

