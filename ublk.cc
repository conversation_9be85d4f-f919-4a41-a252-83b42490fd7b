/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include PHYSICS_H
#include "lattice.h"
#include "shob.h"
#include "sim.h"
#include "ublk.h"
#include "gather_advect.h"
#include "swap_advect.h"
#include "vr_coalesce_explode.h"

BOOLEAN is_at_loc(UBLK ublk,STP_COORD xcor, STP_COORD ycor, STP_COORD zcor){
  return (ublk->location(0) == xcor) && (ublk->location(1) == ycor) && (ublk->location(2) == zcor);
}

#define UBLK_OF_INTEREST (141)
#define VOXEL_OF_INTEREST (4)
#define DEBUG_UBLK(ublk) (FALSE)
//#define DEBUG_UBLK(ublk) ((ublk)->id() == (UBLK_OF_INTEREST))
#define LATVEC_OF_INTEREST (8)
/*
 *  An offset table for ublks is created. The first dimension describes the
 *  type of a ublk. There are currently 24 types of ublks based on the
 *  following attributes.
 *    HAS_TWO_COPIES_OF_STATES true or false
 *    IS_NEAR_SURFACE true or false
 *    IS_MIRROR       true or false
 *    VR_STATUS       NOT VR, VR COARSE, VR FINE.

 * The second dimension, ublk data type, represents the data blocks present
 * in a ublk. Various data types supported for a ublk are.
 *   UBLK_LB_DATA         mandatory known at compile time
 *   STATES_DATA          mandatory known at compile time
 *   UBLK_TURB_DATA       offset known based on two_copies_of_states
 *   UBLK_T_DATA          optional
 *   NEARBLK_GEOM_DATA  optional
 *   NEARBLK_LB_DATA    optional
 *   NEARBLK_TURB_DATA  optional
 *   NEARBLK_T_DATA     optional
 *   UBLK_VR_DATA         optional
 *   UBLK_MIRROR_DATA     optional
 */

namespace{
typedef struct ublk_deleter {
 void operator() (sUBLK* ublk) {sSHOB_ALLOCATOR<sUBLK>::free(ublk);};
} ublk_deleter;
}

template<typename UBLK_TYPE_TAG>
size_t tUBLK<UBLK_TYPE_TAG>::size_of_dyn_data() const {

  auto dynamics_voxel_mask = fluid_like_voxel_mask;
  auto dynamics_data = const_cast<tUBLK*>(this)->dynamics_data();
  auto start_dynamics_data = dynamics_data;

  //Walk down the list of dynamics data pointers till the very end
  //Use the pointer information to compute the size of the dynamics data chunk
  if (basic_fluid_voxel_mask.any()) {
    dynamics_data = static_cast<tUBLK::sUBLK_DYNAMICS_DATA*> (dynamics_data->next());
    dynamics_voxel_mask &= ~basic_fluid_voxel_mask;//Are there voxels that have special dyn blocks
  }

  //Special dynamics blocks
  while (dynamics_voxel_mask.any()){
    auINT32 fluid_type = dynamics_data->fluid_type();
    auto special_dyn_data = static_cast<tUBLK::sSPECIAL_UBLK_DYNAMICS_DATA*>(dynamics_data);
    auto special_fluid_voxel_mask = special_dyn_data->voxel_mask();
    //Are there voxels that have other special dyn blocks, not accounted in previous loops
    dynamics_voxel_mask &= ~special_fluid_voxel_mask;
    dynamics_data = static_cast<tUBLK::sUBLK_DYNAMICS_DATA*> (dynamics_data->next());
  }

  return (reinterpret_cast<char*>(dynamics_data) - reinterpret_cast<char*> (start_dynamics_data));
}

#if BUILD_GPU
template size_t tUBLK<MBLK_SDFLOAT_TYPE_TAG>::size_of_dyn_data() const;
template size_t tUBLK<HMBLK_SDFLOAT_TYPE_TAG>::size_of_dyn_data() const;
#endif

/* @fcn init_space_varying_dynamics_attributes
 * Deferred initialization of space varying attributes until after the SP has read 
 * through the UBLK and SURFEL LGI records. See PR 48816 for more details
 */
VOID init_space_varying_dynamics_attributes(UBLK ublk) {
  VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
  sUBLK_DYNAMICS_DATA *dynamics_data = ublk->dynamics_data();

  if (ublk->basic_fluid_voxel_mask.any()) {
    dynamics_data->init_space_varying_attributes(ublk, ublk->basic_fluid_voxel_mask);
    dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (dynamics_data->next());
    dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;//Are there voxels that have special dyn blocks
  }

  while (dynamics_voxel_mask.any()) {
    SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
    VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
    dynamics_data->init_space_varying_attributes(ublk, special_fluid_voxel_mask);
    
    switch (dynamics_data->fluid_type()) {
    case POROUS_FLUID_TYPE:
    case CURVED_POROUS_FLUID_TYPE:
    case CURVED_HX_POROUS_FLUID_TYPE:
    case FAN_FLUID_TYPE:
    case TABLE_FAN_FLUID_TYPE:
    case CONDUCTION_SOLID_TYPE:
      break;
    default:
      msg_internal_error("Unknown ublk dynamics fluid type");
    }

    dynamics_voxel_mask &= ~special_fluid_voxel_mask;    
    dynamics_data = static_cast<UBLK_DYNAMICS_DATA> (dynamics_data->next());    
  }
}

//This function makes sense only for INLINE BLOCK LAYOUTs
template<typename sUBLK_TYPE>
VOID memcpy_ublk_contents(sUBLK_TYPE* ublk,uINT8 ublk_type,size_t ublk_size,
                          sUBLK_TYPE* ublk_one_copy_ptr,uINT8 ublk_one_copy_type,size_t ublk_one_copy_size,
                          size_t dynamics_data_size){


  using BLOCK_LAYOUT = typename sUBLK_TYPE::BLOCK_LAYOUT;
  
  //Copy everything upto states data
  memcpy(ublk_one_copy_ptr,ublk,sizeof(sUBLK));
  ublk_one_copy_ptr->m_ublk_attributes.m_has_two_copies_of_states = 0; //Reset needed

  //lb_states
  if (sim.is_lb_model) {
    memcpy(ublk_one_copy_ptr->lb_states(0),ublk->lb_states(0),sizeof(sUBLK_STATES_DATA));
  }

  //Turb data
  if (sim.is_turb_model) {
    memcpy(ublk_one_copy_ptr->turb_data(),ublk->turb_data(),sizeof(sUBLK::sUBLK_TURB_DATA));
  }

  //T data
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
    size_t t_data_size = sUBLK::sUBLK_T_DATA::SIZE(false /*need_two_copies*/);
    memcpy(ublk_one_copy_ptr->t_data(),ublk->t_data(),
           t_data_size);
  }

  //UDS data
  if (sim.is_scalar_model) {
    asINT32 n_uds = sim.n_user_defined_scalars;
    memcpy(ublk_one_copy_ptr->uds_data(),ublk->uds_data(),n_uds * (sUBLK::sUBLK_UDS_DATA::SIZE(false /*has two copies*/)));
#if BUILD_D19_LATTICE
    if (sim.is_pf_model) {
      memcpy(ublk_one_copy_ptr->pf_data(),ublk->pf_data(),sizeof(sUBLK::sUBLK_PF_DATA));
    }
#endif
  }

  //mc data
  if (g_is_multi_component) {
    memcpy(ublk_one_copy_ptr->mc_data(),ublk->mc_data(),sUBLK::sUBLK_MC_DATA::SIZE(false /*has two copies*/));
  }

  if (sim.is_particle_model) {
    memcpy(ublk_one_copy_ptr->p_data(), ublk->p_data(), sizeof(sUBLK_PARTICLE_DATA));
  }
  
  if (sim.is_conduction_model && ublk->is_conduction_solid()) {
    size_t cond_data_size = sUBLK::sUBLK_CONDUCTION_DATA::SIZE();
    memcpy(ublk_one_copy_ptr->conduction_data(), ublk->conduction_data(), cond_data_size);
  }

  //The remaining data blocks have only one copy, even for two copy blocks. Given that
  // the ublk version with one copy has the same attributes, it would suffice to copy
  // the remaining bytes in one chunk
  asINT32 last_data_type = UBLK_DATA_TYPE::NEARBLK_GEOM_DATA_TYPE;
  for ( ; last_data_type < N_UBLK_DATA_TYPES; last_data_type++) {
    if (BLOCK_LAYOUT::get_ublk_data_offset(ublk_type, last_data_type) != 0 ){
      break;
    }
  }

  size_t bytes_remaining = BLOCK_LAYOUT::get_ublk_data_offset(ublk_type, UBLK_DATA_TYPE::UBLK_DYN_DATA_TYPE) -
                           BLOCK_LAYOUT::get_ublk_data_offset(ublk_type, last_data_type);

  memcpy(reinterpret_cast<char*>(ublk_one_copy_ptr) + BLOCK_LAYOUT::get_ublk_data_offset(ublk_one_copy_type,last_data_type),
         reinterpret_cast<char*>(ublk) + BLOCK_LAYOUT::get_ublk_data_offset(ublk_type,last_data_type),
         bytes_remaining);

  //Dynamics data
  memcpy(reinterpret_cast<char*>(ublk_one_copy_ptr) + BLOCK_LAYOUT::get_ublk_data_offset(ublk_one_copy_type, UBLK_DATA_TYPE::UBLK_DYN_DATA_TYPE),
         reinterpret_cast<char*>(ublk) + BLOCK_LAYOUT::get_ublk_data_offset(ublk_type, UBLK_DATA_TYPE::UBLK_DYN_DATA_TYPE),
         dynamics_data_size);

  //Finally copy the ublk_one_copy over to the original
  memcpy(ublk,ublk_one_copy_ptr,ublk_one_copy_size);

  memset(reinterpret_cast<char*>(ublk) + ublk_one_copy_size,0, ublk_size - ublk_one_copy_size);
}

VOID reduce_to_one_copy(sUBLK* ublk) {
  //For GPU builds this should do nothing but might still be called during
  //group promotion
#if !BUILD_GPU
  using BLOCK_LAYOUT = typename sUBLK::BLOCK_LAYOUT;
  cassert(ublk->has_two_copies());
  uINT8 ublk_type = ublk->m_ublk_attributes.ublk_type();

  size_t dynamics_data_size = ublk->size_of_dyn_data();

  //Assign memory for a copy that has the same attributes but
  //only one copy of states
  sUBLK::sUBLK_ATTRS ublk_attrs_for_copy = ublk->m_ublk_attributes;
  ublk_attrs_for_copy.m_has_two_copies_of_states = 0;
  uINT8 ublk_one_copy_type  = ublk_attrs_for_copy.ublk_type();
  size_t ublk_one_copy_size = BLOCK_LAYOUT::get_ublk_data_offset(ublk_one_copy_type,UBLK_DYN_DATA_TYPE) + dynamics_data_size;
  size_t ublk_size          = BLOCK_LAYOUT::get_ublk_data_offset(ublk_type,UBLK_DYN_DATA_TYPE) + dynamics_data_size;
  std::unique_ptr<sUBLK,ublk_deleter> ublk_one_copy(sSHOB_ALLOCATOR<sUBLK>::malloc(ublk_one_copy_size,true));
  UBLK ublk_one_copy_ptr = ublk_one_copy.get();
  memcpy_ublk_contents(ublk,ublk_type,ublk_size,
                       ublk_one_copy_ptr,ublk_one_copy_type,ublk_one_copy_size,
                       dynamics_data_size);
#else
  ublk->m_ublk_attributes.m_has_two_copies_of_states = 0;
#endif
}

template<>
VOID sUBLK::set_box_indices(uINT16 indices[3], sUBLK_BOX *ublk_box) {
  m_box_access.m_box_indices[0] = indices[0];
  m_box_access.m_box_indices[1] = indices[1];
  m_box_access.m_box_indices[2] = indices[2];
  cassert(indices[0] < ublk_box->m_size[0]);
  cassert(indices[1] < ublk_box->m_size[1]);
  cassert(indices[2] < ublk_box->m_size[2]);
  uINT32 index = ublk_box->m_size[2] *
                (ublk_box->m_size[1] * indices[0] + indices[1]) + indices[2];
  cassert(index < (1 << 31));
  m_box_access.m_box_1D_index = index;
}

template<>
VOID sUBLK::process_surfel_interaction_data() {
  if (!is_near_surface()) {
    msg_internal_error("process_surfel_interaction_data should only be called for nearblks");
  }

#if BUILD_5G_LATTICE
  BOOLEAN is_multi_component = g_is_multi_component;
  BOOLEAN calc_potential_overlap = !sim.smart_seed_contact_angle &&
                                   !sim.is_full_checkpoint_restore &&
                                   !g_mp_sample_potential;
#endif

  NEAR_UBLK_LB_DATA surf_lb = surf_lb_data();
  NEAR_UBLK_GEOM_DATA surf_geom = surf_geom_data();

  dFLOAT (*overlaps_non_lrf)[ubFLOAT::N_VOXELS] =
      (dFLOAT (*)[ubFLOAT::N_VOXELS]) surf_lb->overlaps_non_lrf;  

  dFLOAT (*dp_pas_factors)[ubFLOAT::N_VOXELS] = (dFLOAT (*)[ubFLOAT::N_VOXELS]) double_precision_pas_factors();
  VOXEL_MASK_8 surfel_interaction_voxel_mask = lb_interaction_voxel_mask;
  // post advect scale factors for VR fine ublks cannot be calculated using
  // lb_interaction_voxel_mask, since the interactions with the surfels
  // occur at different phases and interactions during a single phase do not
  // provide weights for a complete set of interacting LB states. As a result
  // bogus weights (at EV2S phase) are included in the LGI file for calculation
  // of post advect scale factors.
  // pde_2_interaction_voxel_mask denotes voxels that interact with
  // surfels via these bogus weights. These ublk surfel interactions are
  // dropped after the post advect scale factors have been calculated.
  if (is_vr_fine())
    surfel_interaction_voxel_mask = pde_2_interaction_voxel_mask;
  DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {
    // Many VR fine voxels have pfluid = 0, but fluid_like_voxel_mask is still
    // set for these voxels.
    dFLOAT one_over_pfluid = 1.0;
    if (surf_geom->pfluids[voxel] > 1e-8)  
      one_over_pfluid = 1.0 / ((dFLOAT) surf_geom->pfluids[voxel]);
    if (surfel_interaction_voxel_mask.test(voxel)) {
      if (!is_vr_fine()) {
        dFLOAT max_overlap = 0;
        dFLOAT max_overlap_lrf = 0;
        dFLOAT max_overlap_wall = 0;
        BOOLEAN is_any_face_neighbor_null = FALSE;
        ccDOTIMES(latvec, N_CARTESIAN_SPEED1_LATVECS) {
          // Only consider cardinal lattice vectors
          dFLOAT overlap = dp_pas_factors[latvec][voxel];
          dFLOAT scale_factor = overlap * one_over_pfluid;
          if(fabs(scale_factor - 1.0) > PBL_VECTOR_MISMATCH_THRESHOLD && !is_voxel_connected_along_parity(voxel, latvec)) {
            sNEAR_UBLK_GEOM_DATA::sVR_PDE_S2V_SCALE_FACTORS* pde_scale_factors = surf_geom->pde_scale_factors;
            if(pde_scale_factors == NULL) {
              pde_scale_factors = xnew sNEAR_UBLK_GEOM_DATA::sVR_PDE_S2V_SCALE_FACTORS;
              ccDOTIMES(v, ubFLOAT::N_VOXELS) {
                ccDOTIMES(lv,N_CARTESIAN_SPEED1_LATVECS) {
                  pde_scale_factors->alpha[v][lv] = 1.0F;
                }
              }
              surf_geom->pde_scale_factors = pde_scale_factors;
              surf_geom->pde_scale_factors_present = 1;
            }
            if(scale_factor >= PBL_THRESHOLD)
              pde_scale_factors->alpha[voxel][latvec] = 1.0/scale_factor;
          }
          dFLOAT overlap_wall = overlaps_non_lrf[latvec][voxel];
          dFLOAT overlap_lrf = overlap - overlap_wall;
          if ((non_lrf_surfels_interaction_mask.test(voxel)) &&
              (lrf_surfels_interaction_mask.test(voxel)))
            overlap = overlaps_non_lrf[latvec][voxel];
          max_overlap      = MAX (max_overlap,     overlap);
          max_overlap_lrf  = MAX(max_overlap_lrf,  overlap_lrf);
          max_overlap_wall = MAX(max_overlap_wall, overlap_wall);
          // means no neighbor on this side
          if (!is_voxel_connected_along_parity(voxel, latvec))
            is_any_face_neighbor_null = TRUE;
        }
        surf_geom->percent_boundary_layer[voxel] = MIN(1.0, max_overlap * one_over_pfluid);

        // If any neighbor voxel is missing, set the percent boundary layer to 1
        if ((surf_geom->percent_boundary_layer[voxel] < 1) && (is_any_face_neighbor_null))
          surf_geom->percent_boundary_layer[voxel] = 1;

        dFLOAT pbl_wall = MIN(1.0, max_overlap_wall * one_over_pfluid);

        surf_geom->percent_boundary_layer_overlap[voxel] =
            MIN(1.0, max_overlap_lrf * one_over_pfluid * pbl_wall);

        ccDOTIMES(latvec, N_CARTESIAN_SPEED1_LATVECS) {
          asINT32 iparity = state_parity(latvec);
          if (overlaps_non_lrf[iparity][voxel] == 0.0)
            surf_geom->only_lrf_surfels_mask[latvec].set(voxel);
          overlaps_non_lrf[iparity][voxel] = 0.0;
        }
      }

      cVR_STATES coalesced_states_mask;
      sUBLK *vr_fine_ublk = NULL;
      if (is_vr_coarse()) {
        sVR_COARSE_INTERFACE_DATA *vr_coarse_data = (sVR_COARSE_INTERFACE_DATA *) vr_data();
        VR_CONFIG vr_config = vr_coarse_data->vr_config();
        auINT32 vr_fine_index = voxel;
        vr_fine_ublk = vr_coarse_data->vr_fine_ublk(vr_fine_index).ublk();
        coalesced_states_mask = vr_config->coalesce_states[vr_fine_index];
      }
      // Force post-advect scale factors into the range [0,1]
      ccDOTIMES(i, N_LATTICE_VECTORS) {
        dFLOAT p_into_surf_i = dp_pas_factors[i][voxel];

#ifdef DEBUG_COND
	if (this->id() == 2881 && voxel == 6) {
	  msg_print("dp_pas_factors[%d] %g",i, dp_pas_factors[i][voxel]);
	  msg_print("p_into_surf_%d %g",i, p_into_surf_i);
	}
#endif
        
        if (p_into_surf_i == 0.0)
          dp_pas_factors[i][voxel] = 1.0;
        else
          dp_pas_factors[i][voxel] = 1.0 - one_over_pfluid * p_into_surf_i;

        // Force post-advect scale factors into the range [0,1]
        // If pas_i(x) < 0, then p_into_surf_i(x) exceeds pfluid(x) and we may need a beta_factor.
        // If pas_i(x) > 0 and x is not connected in the i* direction, we may need a beta_factor.

        // set pas(x,i) to zero.
        // if this voxel (call it x) is not connected to x-c_i or
        // pas(x, i) < 0 (which is same as p_into_surf > pfluid)
        if ((dp_pas_factors[i][voxel] < 0) ||
           (!is_voxel_connected_along_parity(voxel, i))) {

          if (p_into_surf_i != 0) {
            // If a coarse VR post-advect scale factor is negative, it is also
            // an indication that there are no voxels in the parity direction,
            // hence no V2V advection contribution.
            //
            // If the state is not obtained by coalescing fine states
            // (and there is no V2V advection contribution), beta factor
            // should be based on
            // 1 / p_info_surf which is also equal to
            // 1 / summation_over_x (weight_i(x) * pgram_volume_i(x))
            //
            // If the state is obtained by coalescing fine states from fine
            // voxels, because only post advect scale factors for fine
            // voxels are used to scale the fine states while coalescing.
            // Post advect scale factors for coarse voxels are not used.
            dFLOAT inverse_pfluid_i = 1.0 / p_into_surf_i;
            if (is_vr_coarse()) {
              if (coalesced_states_mask.test(i)) { // state is coalesced and not fluid connected
                if (vr_fine_ublk) {
                  dFLOAT coarse_pas = 0.0;
                  dFLOAT fine_pfluid_total = 0.0;
                  dFLOAT (*fine_pas)[ubFLOAT::N_VOXELS] = (dFLOAT (*)[ubFLOAT::N_VOXELS]) vr_fine_ublk->double_precision_pas_factors();
                  sdFLOAT *fine_pfluids = vr_fine_ublk->surf_geom_data()->pfluids;
                  ccDOTIMES(fine_voxel, ubFLOAT::N_VOXELS) {
                    coarse_pas += fine_pfluids[fine_voxel] *
                                  fine_pas[i][fine_voxel];
                    fine_pfluid_total += fine_pfluids[fine_voxel];
                  }
                  coarse_pas /= fine_pfluid_total;
                  inverse_pfluid_i *= (1.0 - coarse_pas);
                }
              }
            }
            sdFLOAT inverse_pfluid = surf_geom->inverse_pfluids[voxel];
            sdFLOAT delta_volume = one_over_pfluid == 0 ? inverse_pfluid_i :
                                   fabs(inverse_pfluid_i - inverse_pfluid) /
                                   inverse_pfluid;
            if (delta_volume > PFLUID_WEIGHTS_MISMATCH_THRESHOLD) {
              // pfluids_weights_mismatch = TRUE; allocate beta_factors
              if (surf_geom->beta_factors == NULL) {
                surf_geom->beta_factors = xnew sNEAR_UBLK_GEOM_DATA::sV2S_BETA_FACTORS;
                surf_geom->beta_factors_present = 1;
                for (asINT32 v = 0; v < ubFLOAT::N_VOXELS; v++) {
                  for (asINT32 latv = 0; latv < N_LATTICE_VECTORS; latv++) {
                    surf_geom->beta_factors->beta[v][latv] = surf_geom->inverse_pfluids[v];
                  }
                }
              }
              surf_geom->beta_factors->beta[voxel][i] = inverse_pfluid_i;
            }
          } else if (is_vr_fine()) {
            // If a voxel is not connected along a latvec, there should be
            // some p_into_surf along the parity from the surface. This means
            // that either the connectivity is wrong or the surfel weights are
            // wrong.
            // Ensuring this is hard for VR fine and coarse voxels because it
            // is hard establish connectivity across the scale boundary
            //printf("VR UBLK %d beta[%d][%d]=infinity\n", id(), i, voxel);
          } else if (is_vr_coarse()) {
            // Discretizer should be modified so that this never happens
            //printf("Coarse UBLK %d beta[%d][%d]=infinity\n", id(), i,  voxel);
          } else {
            // p_into_surf_i can only be 0 if the voxel is fluid
            // connected in the opposite direction
            //msg_internal_error("NEARBLK %d at [%d %d %d] has beta[%d][%d]=infinity\n", id(),
            //                   this->location[0], this->location[1], this->location[2], i, voxel);
            //printf("NEARBLK %d at [%d %d %d] has beta[%d][%d]=infinity\n", id(),
            //      this->location[0], this->location[1], this->location[2], i, voxel);
            // TODO we should force fluid connectivity in the parity if there is
            // only one neighboring voxel with fluid in the parity slot.
          }
          dp_pas_factors[i][voxel] = 0.0;
        } else if (dp_pas_factors[i][voxel] > 1.0) {
          dp_pas_factors[i][voxel] = 1.0;
        } else if (p_into_surf_i != 0.0) {
          dFLOAT pas_factor =  dp_pas_factors[i][voxel];
          dFLOAT inverse_pfluid_i = (1.0 - pas_factor) / p_into_surf_i;
          sdFLOAT inverse_pfluid = surf_geom->inverse_pfluids[voxel];
          sdFLOAT delta_volume = one_over_pfluid == 0 ? inverse_pfluid_i :
                                 fabs(inverse_pfluid_i - inverse_pfluid) /
                                 inverse_pfluid;
          if (delta_volume > PFLUID_WEIGHTS_MISMATCH_THRESHOLD) {
            // pfluids_weights_mismatch = TRUE; allocate beta_factors
            if (surf_geom->beta_factors == NULL) {
              surf_geom->beta_factors = xnew sNEAR_UBLK_GEOM_DATA::sV2S_BETA_FACTORS;
              surf_geom->beta_factors_present = 1;
              for (asINT32 v = 0; v < ubFLOAT::N_VOXELS; v++) {
                for (asINT32 latv = 0; latv < N_LATTICE_VECTORS; latv++) {
                  surf_geom->beta_factors->beta[v][latv] = surf_geom->inverse_pfluids[v];
                }
              }
            }
            surf_geom->beta_factors->beta[voxel][i] = inverse_pfluid_i;
          }
        }
      }
      ccDOTIMES(latvec, N_LATTICE_VECTORS) {
        asINT32 energy = state_energy_index(latvec);
        if (energy == 1) {
	  asINT32 iparity = state_parity(latvec);
#if BUILD_5G_LATTICE          
          if (calc_potential_overlap) {
            sdFLOAT beta_factor = surf_geom->beta_factors ?
	      surf_geom->beta_factors->beta[voxel][iparity] :
	      surf_geom->inverse_pfluids[voxel];
            surf_lb_data()->srf_potential[iparity][voxel] *= beta_factor;
            if (is_multi_component)
              surf_mc_data()->srf_potential[iparity][voxel] *= beta_factor;
          }
#endif //BUILD_5G_LATTICE
#if BUILD_D19_LATTICE          
          if (sim.is_pf_model && !sim.is_full_checkpoint_restore) {
            sdFLOAT beta_factor = surf_geom->beta_factors ?
                                  surf_geom->beta_factors->beta[voxel][iparity] :
                                  surf_geom->inverse_pfluids[voxel];
            surf_pf_data()->srf_potential[iparity][voxel] *= beta_factor;
          }
#endif //BUILD_D19_LATTICE
#if BUILD_D39_LATTICE
	  //          if (energy == 4) { // 2-speed cardinal direction 39s lattice vectors
	  //            STP_SURFEL_WEIGHT overlap = voxel_overlaps[latvec];
	  //            if (state_vx(latvec) == -2) {
	  //              overlap_x_p_2 = overlap;
	  //            }
	  //            if (state_vx(latvec) == 2) {
	  //              overlap_x_n_2 = overlap;
	  //            }
	  //            if (state_vy(latvec) == -2) {
	  //              overlap_y_p_2 = overlap;
	  //            }
	  //            if (state_vy(latvec) == 2) {
	  //              overlap_y_n_2 = overlap;
	  //            }
	  //            if (state_vz(latvec) == -2) {
	  //              overlap_z_p_2 = overlap;
	  //            }
	  //            if (state_vz(latvec) == 2) {
	  //              overlap_z_n_2 = overlap;
	  //            }
	  //          }
#endif
        }
      }
    } else {
      // We are computing post_advect_scale_factors for all the voxels that
      // belong to a ublk that advects via lb states. The post advect scale
      // for all voxels that do not advect via lb states should be 1. This is
      // necessary because pbl_?_? are computed on the fly now.
      ccDOTIMES(i, N_LATTICE_VECTORS) {
        if (dp_pas_factors[i][voxel] != 0) {
          msg_internal_error(
			     "post_advect_scale_factor for ublk %d voxel %d state %d should be 0 but is %e",
			     id(), voxel, i, dp_pas_factors[i][voxel]);
        }
        dp_pas_factors[i][voxel] = 1.0;
      }
    }

#if BUILD_D39_LATTICE
    sdFLOAT normal[3];
    ccDOTIMES(axis, N_AXES)
      normal[axis] = surf_geom->normal[axis][voxel];

    sdFLOAT one_over_norm_mag = sqrt(vdot(normal, normal));
    one_over_norm_mag = (one_over_norm_mag == 0) ? 0.0: 1.0 / one_over_norm_mag;
    ccDOTIMES(axis, N_AXES) {
      surf_geom->normal[axis][voxel] *= one_over_norm_mag;
    }
#endif

    // pbl_2 is swapped with its parity
    // Only consider cardinal lattice vectors
    if (!is_vr_fine() && pde_2_interaction_voxel_mask.test(voxel)) {

      // The parity-pair entries in pbl_2 are swapped. The
      // pbl_2 entries are defined the same as p_into_surf:
      //   pbl_2_i(x) = sum_over_alpha { V_i_alpha(x) }
      ccDOTIMES(lp_2, N_CARTESIAN_SPEED1_LATVECS/2)
	{
	  asINT32 latvec_2 = latvec_pair_first_latvec(lp_2);
	  asINT32 iparity_2 = latvec_pair_second_latvec(lp_2);
	  sdFLOAT tmp_pbl_2 =
            MIN(1.0, surf_geom->pbl_2[iparity_2][voxel] * one_over_pfluid);
	  surf_geom->pbl_2[iparity_2][voxel] =
            MIN (1.0, surf_geom->pbl_2[latvec_2][voxel] * one_over_pfluid);
	  surf_geom->pbl_2[latvec_2][voxel] = tmp_pbl_2;
	}
      ccDOTIMES(latvec_2, N_CARTESIAN_SPEED1_LATVECS) {

        // no 2 speed neighbor and interaction > 0 (equivalent of pde_interaction_mask)
        if (!is_voxel_connected_along_latvec(voxel, SPEED_1_TO_SPEED_2(latvec_2)) &&
            (surf_geom->pbl_2[latvec_2][voxel] > 0.0) &&
            (fabs(surf_geom->pbl_2[latvec_2][voxel] - 1.0) > PBL_VECTOR_MISMATCH_THRESHOLD)) {

          // These lines are likely garbage. They only kick in when
	  // pbl_2 is between 0 and 1e-6.
          if (surf_geom->pbl_2[latvec_2][voxel] < PBL_THRESHOLD)
            surf_geom->pbl_2[latvec_2][voxel] = 1; //???hole is still there, not repaired


          sNEAR_UBLK_GEOM_DATA::sV2S_PBL_FACTORS* pbl_factors_2 = surf_geom->pbl_factors_2;
          if (pbl_factors_2 == NULL) {
            surf_geom->pbl_factors_2_present = 1;
            pbl_factors_2 = xnew sNEAR_UBLK_GEOM_DATA::sV2S_PBL_FACTORS;
            for (asINT32 v = 0; v < ubFLOAT::N_VOXELS; v++) {
              for (asINT32 latv = 0; latv < N_CARTESIAN_SPEED1_LATVECS; latv++) {
                // what about voxels with pfluid = 0
                // perhaps mask should be used ??
                pbl_factors_2->alpha[v][latv] = 1.0;
              }
            }
            surf_geom->pbl_factors_2 = pbl_factors_2;
          }
          pbl_factors_2->alpha[voxel][latvec_2] = 1.0 / surf_geom->pbl_2[latvec_2][voxel];
          surf_geom->pbl_2[latvec_2][voxel] = 1.0;
        }
      }
    }
  }  
}

// This has to be done as a separate step after calculating pas factors
// since some overlaps_non_lrf is stored on top of post_advect_scale_Factors
template<>
VOID sUBLK::convert_dp_post_advect_scale_factors_to_sp() {

  if (!is_near_surface()) {
    msg_internal_error("process_surfel_interaction_data should only be called for nearblks");
  }
  // Since the memory overlaps the voxel loop has to be the inner loop
  if (is_vr_fine()) {
    if (are_states_clear() == 1)
      return;
    set_states_clear();
  } else {
    if (are_states_clear() == 0)
      return;
    unset_states_clear();
  }

  // smart seed data and sp_pas_factors are overlaid, so they are copied
  // from sp_pas_factors to temporary location and then to the states.
  sUBLK_SMART_SEED_DATA ublk_smart_seed_data;
  if (sim.do_smart_seed) {
    memcpy(&ublk_smart_seed_data, alt_smart_seed_data(), sizeof(sUBLK_SMART_SEED_DATA));
  }
  NEAR_UBLK_LB_DATA surf_lb = surf_lb_data();
  dFLOAT (*dp_pas_factors)[ubFLOAT::N_VOXELS] = (dFLOAT (*)[ubFLOAT::N_VOXELS]) double_precision_pas_factors();
  ccDOTIMES(latv, N_LATTICE_VECTORS) {
    DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {
      surf_lb->post_advect_scale_factors[latv][voxel] = dp_pas_factors[latv][voxel];
      //dp_pas_factors[latv][voxel] = 0.0;
    }
  }
  if (sim.do_smart_seed) {
    memcpy(smart_seed_data(), &ublk_smart_seed_data, sizeof(sUBLK_SMART_SEED_DATA));
  }

  if (0) {
    DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {
      printf("Bpas U %d voxel %d pf %e\n",id(),voxel,surf_geom_data()->inverse_pfluids[voxel] );
      ccDOTIMES(latv, N_LATTICE_VECTORS) {
        printf("%e ", surf_lb->post_advect_scale_factors[latv][voxel]);
        if ((latv+1)%6 ==0) printf("\n");
      }
      if (surf_geom_data()->beta_factors) {
	ccDOTIMES(latv, N_LATTICE_VECTORS) {
	  printf("%e ", surf_geom_data()->beta_factors->beta[voxel][latv]);
	  if ((latv+1)%6 ==0) printf("\n");
	}
      }
    }
  }
  // setting the post advect scale factors for diagonal states to 0
  // to ensure no V2V contribution from neighboring coarse voxels.
  // This is only done for diagonal states, because post advect factor
  // in the cardinal directions are used to calculate pbl_factors. Also,
  // V2V contribution can only come from a coarse voxel in the diagonal
  // direction for the coalesced states.
#if BUILD_D19_LATTICE
  if(sim.is_pf_model){
    NEAR_UBLK_PF_DATA surf_pf = surf_pf_data();
    ccDOTIMES(latv, N_LATTICE_VECTORS) {
      DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {
        surf_pf->post_advect_scale_factors_pfld[latv][voxel] = surf_lb->post_advect_scale_factors[latv][voxel];
      }
    }
  }
#endif


  if (is_vr_coarse()) {
    reset_post_advect_scale_factors_for_diagonal_coalesced_states(this, surf_lb->post_advect_scale_factors);
  }
}

template<>
VOID sUBLK::fill_dynamics_data(PHYSICS_DESCRIPTOR fluid_phys_desc, 
                               VOXEL_MASK_8 voxel_mask,
                               STP_PHYSTYPE_TYPE sim_phys_type, 
                               UBLK ublk,
                               BOOLEAN is_real_ublk_desc, DGF_UBLK_BASE_DESC ublk_desc,
                               UBLK_DYNAMICS_DATA &dynamics_data,
                               sUBLK_DYNAMICS_ATTR_DATA *attribute_data) {
  switch (sim_phys_type) {
  case STP_VVFLUID_TYPE:
  case STP_NEAR_SURFACE_VVFLUID_TYPE:
  {
    FLUID_PHYSICS_DESCRIPTOR pd = (FLUID_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    dynamics_data->init(pd, voxel_mask, BASIC_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_POROUS_VVFLUID_TYPE:
  {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    POROUS_UBLK_DYNAMICS_DATA dyn_data = (POROUS_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, POROUS_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_CURVED_POROUS_VVFLUID_TYPE:
  {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    CURVED_POROUS_UBLK_DYNAMICS_DATA dyn_data = (CURVED_POROUS_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, CURVED_POROUS_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_CURVED_HX_POROUS_VVFLUID_TYPE:
  {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    CURVED_HX_POROUS_UBLK_DYNAMICS_DATA dyn_data = (CURVED_HX_POROUS_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, CURVED_HX_POROUS_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_FAN_VVFLUID_TYPE:
  {
    FAN_PHYSICS_DESCRIPTOR pd = (FAN_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    FAN_UBLK_DYNAMICS_DATA dyn_data = (FAN_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, FAN_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_TABLE_FAN_VVFLUID_TYPE:
  {
    TABLE_FAN_PHYSICS_DESCRIPTOR pd = (TABLE_FAN_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    TABLE_FAN_UBLK_DYNAMICS_DATA dyn_data = (TABLE_FAN_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, TABLE_FAN_FLUID_TYPE, ublk, is_real_ublk_desc, (DGF_REAL_UBLK_DESC)ublk_desc, attribute_data);
    break;
  }
  case STP_CONDUCTION_SOLID_TYPE:
  {
    CONDUCTION_PHYSICS_DESCRIPTOR pd = (CONDUCTION_PHYSICS_DESCRIPTOR)fluid_phys_desc;
    CONDUCTION_UBLK_DYNAMICS_DATA dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
    dyn_data->init(pd, voxel_mask, CONDUCTION_SOLID_TYPE, ublk, is_real_ublk_desc, ublk_desc, attribute_data);
    break;
  }
  case STP_INSULATOR_SOLID_TYPE:
    break;
  default:
  {
    msg_internal_error("Unknown dynamics data cdi type: %d", sim_phys_type);
  }
  }
}

template<>
VOID sUBLK::fill_uds_data(PHYSICS_DESCRIPTOR fluid_phys_desc,VOXEL_MASK_8 voxel_mask) { //LB_UDS
  assert(sim.uds_solver_type == LB_UDS);

#if BUILD_5G_LATTICE
  ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
    UBLK_UDS_DATA ublk_uds_data = this->uds_data(nth_uds);
    DO_VOXELS_IN_MASK(voxel, voxel_mask ) {
      ublk_uds_data->volume_source_term[voxel] = g_density_scale_factor * g_uds_volume_source_term[nth_uds] / sim.char_density;
    }
  }
#else
  CDI_PHYS_TYPE_DESCRIPTOR phys_type = fluid_phys_desc->phys_type_desc;  
  CDI_UDS_PHYS_TYPE_DESCRIPTOR uds_phys_type_desc = find_uds_physics(phys_type->cdi_physics_type);
  if (uds_phys_type_desc == NULL)
    msg_error("Unknown uds fluid physics description");
  
  if (uds_phys_type_desc->uds_physics_type == CDI_UDS_PHYS_FLUID) {
    FLUID_UDS_PARAMETERS uds_parameters = (FLUID_UDS_PARAMETERS)fluid_phys_desc->uds_parameters();
    if (uds_parameters == NULL) 
      msg_error("Basic fluid ublk (%d) does not have uds parameters", this->id());
    
    if (fluid_phys_desc->all_uds_parameters_sharable) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
	ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
	  if (is_voxel_in_mask(voxel, voxel_mask)) {	  
	    this->uds_data(nth_uds)->volume_source_term[voxel] = uds_parameters->uds_source_term.value * g_density_scale_factor / sim.char_density;
	    this->uds_data(nth_uds)->diffusivity_mol[voxel] = g_scalar_materials[nth_uds].diffusivity_mol;
	  }
	}

	uds_parameters++;
      }
    } else {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
	ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
	  if (is_voxel_in_mask(voxel, voxel_mask)){
	    STP_GEOM_VARIABLE voxel_centroid[3];
	    voxel_centroid[0] = this->centroids(voxel, 0);
	    voxel_centroid[1] = this->centroids(voxel, 1);
	    voxel_centroid[2] = this->centroids(voxel, 2);
	    
	    fluid_phys_desc->eval_space_and_table_varying_uds_parameter_program(voxel_centroid, NULL, fluid_eqn_error_handler);
	    this->uds_data(nth_uds)->volume_source_term[voxel] = uds_parameters->uds_source_term.value * g_density_scale_factor / sim.char_density;
	    this->uds_data(nth_uds)->diffusivity_mol[voxel] = g_scalar_materials[nth_uds].diffusivity_mol;
	  }
	}

	uds_parameters++;
      }
    }
  } else if (uds_phys_type_desc->uds_physics_type == CDI_UDS_PHYS_SPECIAL_FLUID) {
    SPECIAL_FLUID_UDS_PARAMETERS uds_parameters = (SPECIAL_FLUID_UDS_PARAMETERS)fluid_phys_desc->uds_parameters(); //parms are updated in fill_dynamics_data if they are not sharable
    if (uds_parameters == NULL) 
      msg_error("Special fluid ublk (%d) does not have uds parameters", this->id());
       
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
	if (is_voxel_in_mask(voxel, voxel_mask)) {
	  this->uds_data(nth_uds)->volume_source_term[voxel] = uds_parameters->uds_source_term.value * g_density_scale_factor / sim.char_density;
	  this->uds_data(nth_uds)->diffusivity_mol[voxel] = uds_parameters->effective_diffusivity.value;  
	}
      }
      uds_parameters++;
    }      
  } else {
      msg_error("Unknown uds fluid physics tye (%d)",  uds_phys_type_desc->uds_physics_type);
  }
#endif
}

template<>
VOID sUBLK::fill_mirror_data(UBLK mirror_ublk, STP_STATE_INDEX state_index) {

  sUBLK_MIRROR_DATA *mirror_data    = this->mirror_data();
  if (mirror_data->m_mirror_ublk == NULL) {
    mirror_data->m_mirror_ublk        = mirror_ublk;
    mirror_data->m_ublk_mirror_config = find_mirror_ublk_config(state_index);
    mirror_data->m_next_mirror_data   = NULL;
  } else {
    while (mirror_data->m_next_mirror_data) {
      mirror_data = mirror_data->m_next_mirror_data;
    }
    mirror_data->m_next_mirror_data = new sUBLK_MIRROR_DATA;
    sUBLK_MIRROR_DATA *new_mirror_data    = mirror_data->m_next_mirror_data;
    new_mirror_data->m_mirror_ublk        = mirror_ublk;
    new_mirror_data->m_ublk_mirror_config = find_mirror_ublk_config(state_index);
    new_mirror_data->m_next_mirror_data   = NULL;
  }
}

static void assign_meas_cell_ptrs(asINT32 n_windows, 
                                  sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr,
                                  BOOLEAN is_ublk_moving,
                                  VOXEL_MASK_8 voxel_mask,
                                  SCALE voxel_scale)
{
  ccDOTIMES(w, n_windows) {
    MEAS_WINDOW window = g_meas_windows[ublk_meas_cell_ptr->window_index()];
    VOXEL_MASK_8 meas_voxel_mask = ublk_meas_cell_ptr->voxel_mask();
    if(!window->m_is_particle_trajectory_window) {
      STP_MEAS_CELL_INDEX meas_cell_index = window->m_meas_cell_index_map[ublk_meas_cell_ptr->index()].m_new_sp_cell_index;
      // for a dev window and a moving surfel, leave meas_cell_ptr as an index with an axis
      if (window->is_development && is_ublk_moving) {
        asINT32 axis = ublk_meas_cell_ptr->axis();
        asINT32 part_index = ublk_meas_cell_ptr->part();
        ublk_meas_cell_ptr->set_index(meas_cell_index - window->entity_first_segment[axis][part_index]);
        ublk_meas_cell_ptr->mark_should_rotate_vector_to_grf();
      } else {
        MEAS_CELL_VAR *meas_cell = window->meas_cell(meas_cell_index);
        ublk_meas_cell_ptr->set_variables(meas_cell);
        if (window->contains_std_dev_vars) {
          sSTD_CELL_VAR *std_cell = ublk_meas_cell_ptr->std_cell_ptr(window->n_variables);
          // voxel-based windows have several std_cells that need to be updated
          if ( !window->is_composite && !window->is_development && window->is_per_voxel_for_scale(voxel_scale) ) {
            DO_VOXELS_IN_MASK(voxel, meas_voxel_mask) {
              if (voxel_mask.test(voxel)) {
                std_cell->add_shob();
              }
              std_cell += window->n_variables + 1;
            }
          }
          else {
            std_cell->add_shob();
          }
        }
      }
      if (is_ublk_moving) {
        if (!window->is_output_in_local_csys) {
          ublk_meas_cell_ptr->mark_should_rotate_vector_to_grf();
        } else if (window->is_composite) {
          if (!window->meas_cell_output_in_local_csys[meas_cell_index])
            ublk_meas_cell_ptr->mark_should_rotate_vector_to_grf();
        }
      }
    }
    ublk_meas_cell_ptr++;
  }
}

template<>
VOID sUBLK::resolve_meas_cell_ptrs() {

  UBLK_DYNAMICS_DATA ublk_dyn_data = dynamics_data();
  VOXEL_MASK_8 dynamics_voxel_mask = fluid_like_voxel_mask;
  if (basic_fluid_voxel_mask.any()) {
    asINT32 n_windows = ublk_dyn_data->n_meas_windows();
    sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr = ublk_dyn_data->meas_cell_ptrs();
    assign_meas_cell_ptrs(n_windows, ublk_meas_cell_ptr, is_ublk_moving(), basic_fluid_voxel_mask, scale());
    dynamics_voxel_mask &= ~basic_fluid_voxel_mask;
    ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
  }
  while (dynamics_voxel_mask.any()) {
    auINT32 fluid_type = ublk_dyn_data->fluid_type();
    switch (fluid_type) {
    case POROUS_FLUID_TYPE :
    case CURVED_POROUS_FLUID_TYPE :
    case CURVED_HX_POROUS_FLUID_TYPE :
    {
      // reference frame index in physics parameters is at different locations
      POROUS_UBLK_DYNAMICS_DATA dyn_data = (POROUS_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
      asINT32 n_windows = ublk_dyn_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr = ublk_dyn_data->meas_cell_ptrs();
      assign_meas_cell_ptrs(n_windows, ublk_meas_cell_ptr, is_ublk_moving(), special_fluid_voxel_mask, scale());
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) dyn_data->next();
      break;
    }
    case FAN_FLUID_TYPE:
    case TABLE_FAN_FLUID_TYPE:
    {
      FAN_UBLK_DYNAMICS_DATA dyn_data = (FAN_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
      asINT32 n_windows = ublk_dyn_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr = ublk_dyn_data->meas_cell_ptrs();
      assign_meas_cell_ptrs(n_windows, ublk_meas_cell_ptr, is_ublk_moving(), special_fluid_voxel_mask, scale());
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) dyn_data->next();
      break;
    }
    case CONDUCTION_SOLID_TYPE:
    {
      CONDUCTION_UBLK_DYNAMICS_DATA dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 conduction_voxel_mask = dyn_data->voxel_mask();
      asINT32 n_windows = ublk_dyn_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptr = ublk_dyn_data->meas_cell_ptrs();
      assign_meas_cell_ptrs(n_windows, ublk_meas_cell_ptr, is_ublk_moving(), conduction_voxel_mask, scale());
      dynamics_voxel_mask &= ~conduction_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) dyn_data->next();
      break;
    }
    }
  }
}

template<>
sUBLK_MEAS_CELL_PTR *sUBLK::create_meas_cell_ptrs(sUBLK_DYNAMICS_DATA *dyn_data, asINT32 n_meas_cells) {
  return dyn_data->allocate_meas_cell_ptrs(n_meas_cells);
}

template<>
VOID sUBLK::seed_momentum_solver() {
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;

  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale());

  asINT32 prior_lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 prior_ke_index = dcache->c()->KE_prior_index;
  asINT32 next_lb_index = prior_lb_index ^ 1;
  asINT32 next_ke_index = prior_ke_index ^ 1;


  seed_ublk_momentum_vars(this, basic_fluid_voxel_mask, prior_lb_index, prior_ke_index);
}

template<>
VOID sUBLK::seed_T_pde_solver() {
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale());
//  asINT32 curr_T_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 curr_T_index = dcache->c()->T_prior_index;
//  asINT32 curr_KE_index = turb_index_from_mask(prior_solver_index_mask);
  asINT32 curr_KE_index = dcache->c()->KE_prior_index;
  asINT32 curr_LB_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 next_T_index = curr_T_index ^ 1;
  asINT32 next_KE_index = curr_KE_index ^ 1;
  asINT32 next_LB_index = curr_LB_index ^ 1;

  //T_solver switch only happens in 19s with thermal accel
  /*#if BUILD_D39_LATTICE
  LRF_PHYSICS_DESCRIPTOR lrf = this->lrf_physics_descriptor();
  BOOLEAN is_high_mach = FALSE;  //only for 33s
  if (sim.use_hybrid_ts_hs_solver)
    is_high_mach = (lrf != NULL) && lrf->has_transonic_flow;
  else
    is_high_mach = TRUE;
    #endif*/
  seed_ublk_t_pde_vars(this, fluid_like_voxel_mask, next_T_index, next_KE_index, next_LB_index);
}

template<>
VOID sUBLK::seed_T_scalar_solver() {
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale());
  asINT32 curr_T_index = t_index_from_mask(prior_solver_index_mask);
  seed_ublk_states_t(this, fluid_like_voxel_mask, curr_T_index);
}

// Get the timer for the ublk fluid type. If there are several fluid types, use the first one
//uINT8 sUBLK::get_fluid_type()
template<>
SP_TIMER_TYPE sUBLK::get_fluid_dyn_timer()
{
  VOXEL_MASK_8 dynamics_voxel_mask = fluid_like_voxel_mask;
  if (!dynamics_voxel_mask.any())
    //return INVALID_FLUID_TYPE;
    return SP_INVALID_TIMER;
  auto dynamics_data = this->dynamics_data();
  asINT32 fluid_type;
  if (basic_fluid_voxel_mask.any()) {
    //return BASIC_FLUID_TYPE;
    fluid_type = BASIC_FLUID_TYPE;
  } else {
    //return dynamics_data->fluid_type();
    fluid_type = dynamics_data->fluid_type();
  }

  BOOLEAN is_pde_advect_ublk = this->has_two_copies();
  BOOLEAN is_near_surface = this->is_near_surface();
  BOOLEAN is_split = this->is_split();

  return (ublk_dyn_timer_from_fluid_type(is_pde_advect_ublk, is_split, is_near_surface, fluid_type));
}

static INLINE
VOID prefetch_lb_data(UBLK fetch_ublk, asINT32 prior_lb_index) {
  // Need to get 2 cache lines = 128 bytes
  char * fetch_data = (char *) fetch_ublk->lb_data() + prior_lb_index*sizeof(sUBLK_LB_DATA::sLB_DATA);
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
  fetch_data += 64;
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
}

static INLINE
VOID prefetch_turb_data(UBLK fetch_ublk, asINT32 prior_ke_index) {
  // Need to get 4.5 cache lines = 288 bytes
  char *fetch_data = (char *) fetch_ublk->turb_data() + prior_ke_index*sizeof(sUBLK_TURB_DATA::sTURB_DATA);
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
  fetch_data += 64;
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
  fetch_data += 64;
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
  fetch_data += 64;
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
  fetch_data += 64;
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
}

static INLINE
VOID prefetch_t_data(UBLK fetch_ublk, asINT32 prior_t_index) {
  // Need to get 4.5 cache lines = 288 bytes
  char *fetch_data = (char *) fetch_ublk->t_data()->fluid_temp[prior_t_index];
  _mm_prefetch((const char *)fetch_data, _MM_HINT_T2);
} 

static INLINE
VOID prefetch_ublk_dynamics_data(UBLK ublk, 
				 SOLVER_INDEX_MASK prior_solver_index_mask,
                                 ACTIVE_SOLVER_MASK solver_mask,
				 BOOLEAN use_curr_index) {
  asINT32 lb_index = lb_index_from_mask(prior_solver_index_mask);
  asINT32 t_index = t_index_from_mask(prior_solver_index_mask);
  asINT32 ke_index = turb_index_from_mask(prior_solver_index_mask);
  
  if (ublk && !ublk->is_solid()) {
    if (solver_mask & LB_ACTIVE) 
      prefetch_lb_data(ublk, use_curr_index? 1^lb_index : lb_index);
    if (solver_mask & KE_PDE_ACTIVE)
      prefetch_turb_data(ublk, use_curr_index? 1^ke_index : ke_index);
    if (solver_mask & T_PDE_ACTIVE)
      prefetch_t_data(ublk, use_curr_index? 1^t_index : t_index);
  }
}

 template <BOOLEAN is_2D, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
#ifdef DEBUG
static
#else
static INLINE
#endif
VOID ublk_gather_advect(UBLK ublk, BOOLEAN is_timestep_even,
			SOLVER_INDEX_MASK prior_solver_index_mask,
			ACTIVE_SOLVER_MASK active_solver_mask,
			UBLK (&active_nbrs) [N_MOVING_STATES]) {

  if (ublk->m_are_any_neighbors_split.any()) {
    const auto& box_access = ublk->m_box_access;
    const sSPLIT_ADVECT_INFO* split_advect_info = ublk->get_split_advect_info();
    asINT32 split_instance_index = -1;
#if !BUILD_D39_LATTICE
    if (split_advect_info == NULL) {
      split_instance_index = ublk->m_split_tagged_instance.get();
    } else if (split_advect_info->m_interacts_with_single_instance) {
      split_instance_index = split_advect_info->m_split_instance_index;
    }
#endif
    if (split_instance_index == -1 || is_2D) {
      gather_advect<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(ublk, ublk->fluid_like_voxel_mask,
									  is_timestep_even, 
									  prior_solver_index_mask,
									  active_solver_mask);
    } else {
      if (ublk->are_states_clear()) {
        gather_advect_split<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>(ublk, ublk->fluid_like_voxel_mask,
                                                     split_instance_index,
                                                     is_timestep_even,
                                                     prior_solver_index_mask,
                                                     active_solver_mask);
      } else {
        gather_advect_split<IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>(ublk, ublk->fluid_like_voxel_mask,
                                                     split_instance_index,
                                                     is_timestep_even,
                                                     prior_solver_index_mask,
                                                     active_solver_mask);
      }
    }
  } else {
#if EXA_USE_AVX && !(BUILD_DOUBLE_PRECISION)
    if (g_use_avx2_advection) {
      if (ublk->are_states_clear()) {
	ADVECT_VECTORIZED::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>
	  ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
	    is_timestep_even,
	    prior_solver_index_mask, active_solver_mask);

      } else {
	ADVECT_VECTORIZED::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>
	  ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
	    is_timestep_even,
	    prior_solver_index_mask, active_solver_mask);
      }
    } else {
      if (ublk->are_states_clear()) {
	ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>
	  ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
	    is_timestep_even,
	    prior_solver_index_mask, active_solver_mask);

      } else {
	ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>
	  ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
	    is_timestep_even,
	    prior_solver_index_mask, active_solver_mask);
      }
    }
#else
    if (ublk->are_states_clear()) {
      ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>
        ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
          is_timestep_even,
          prior_solver_index_mask, active_solver_mask);

    } else {
      ADVECT_SCALAR::gather_advect_for_unsplit_neighbors<FALSE, is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>
        ( ublk, ublk->fluid_like_voxel_mask, active_nbrs,
          is_timestep_even,
          prior_solver_index_mask, active_solver_mask);
    }
#endif
  }

  if (is_2D) {
    asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    asINT32 curr_lb_index = 1 ^ prev_lb_index;
    if (g_adv_fraction < 1.0f) {
      scale_up_z_only_states(ublk, g_one_over_adv_fraction, curr_lb_index, TRUE, active_solver_mask);
    } else {
      copy_z_only_states(ublk, curr_lb_index, active_solver_mask);
    }
  }
}

#if !BUILD_5G_LATTICE
template <typename PHYSICS_DESCRIPTOR_TYPE>
  static void update_uds_volume_source_term_internal(UBLK ublk, VOXEL_MASK_8 dynamics_voxel_mask, UBLK_DYNAMICS_DATA dynamics_data, FLUID_DYN_DCACHE dcache, BOOLEAN is_basic_fluid, BOOLEAN is_lb_uds)
{
  PHYSICS_DESCRIPTOR_TYPE phys_descriptor = (PHYSICS_DESCRIPTOR_TYPE)dynamics_data->physics_descriptor();
  PHYSICS_VARIABLE source_term = &(phys_descriptor->parameters()->water_vapor_source_term);
  if (is_lb_uds) {     
    if (phys_descriptor->some_uds_parameter_time_varying) {
      if (is_basic_fluid) {
	FLUID_UDS_PARAMETERS uds_parameters = (FLUID_UDS_PARAMETERS)phys_descriptor->uds_parameters();
	ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
	  UBLK_UDS_DATA ublk_uds_data = ublk->uds_data(nth_uds);
	  DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask ) {
	    sdFLOAT voxel_centroid[3];
	    voxel_centroid[0] = ublk->centroids(voxel, 0);
	    voxel_centroid[1] = ublk->centroids(voxel, 1);
	    voxel_centroid[2] = ublk->centroids(voxel, 2);
	    phys_descriptor->eval_time_and_space_varying_uds_parameter_program(voxel_centroid, NULL, g_timescale.m_time, g_timescale.m_powertherm_time, fluid_eqn_error_handler);
	    ublk_uds_data->volume_source_term[voxel] = uds_parameters[nth_uds].uds_source_term.value * g_density_scale_factor  / sim.char_density;
	    ublk_uds_data->diffusivity_mol[voxel] = g_scalar_materials[nth_uds].diffusivity_mol;
	  }
	}
      } else { //special fluid
	SPECIAL_FLUID_UDS_PARAMETERS uds_parameters = (SPECIAL_FLUID_UDS_PARAMETERS)phys_descriptor->uds_parameters();
	ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
	  UBLK_UDS_DATA ublk_uds_data = ublk->uds_data(nth_uds);
	  DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask ) {
	    sdFLOAT voxel_centroid[3];
	    voxel_centroid[0] = ublk->centroids(voxel, 0);
	    voxel_centroid[1] = ublk->centroids(voxel, 1);
	    voxel_centroid[2] = ublk->centroids(voxel, 2);
	    phys_descriptor->eval_time_and_space_varying_uds_parameter_program(voxel_centroid, NULL, g_timescale.m_time, g_timescale.m_powertherm_time, fluid_eqn_error_handler);
	    ublk_uds_data->volume_source_term[voxel] = uds_parameters[nth_uds].uds_source_term.value * g_density_scale_factor  / sim.char_density;
	    ublk_uds_data->diffusivity_mol[voxel] = uds_parameters[nth_uds].effective_diffusivity.value;
	  }
	}
      }
    }
  } else { //PDE_UDS
    PHYSICS_VARIABLE source_term = &(phys_descriptor->parameters()->water_vapor_source_term);
    ccDOTIMES(nth_uds,sim.n_user_defined_scalars){
      UBLK_UDS_DATA ublk_uds_data = ublk->uds_data(nth_uds);
      if( source_term->is_eqn ){
	DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask ) {
	  sdFLOAT voxel_centroid[3];
	  voxel_centroid[0] = ublk->centroids(voxel, 0);
	  voxel_centroid[1] = ublk->centroids(voxel, 1);
	  voxel_centroid[2] = ublk->centroids(voxel, 2);
	  phys_descriptor->eval_time_and_space_varying_parameter_program(voxel_centroid, NULL, g_timescale.m_time, g_timescale.m_powertherm_time, fluid_eqn_error_handler);
	  ublk_uds_data->volume_source_term[voxel] = g_density_scale_factor * source_term->value / sim.char_density;
	}
      } else {
	DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask ) {
	  ublk_uds_data->volume_source_term[voxel] = g_density_scale_factor * source_term->value / sim.char_density;
	}
      }

      if(sim.is_particle_model && g_use_particle_evaporation_model && g_is_defogging && g_enable_particle_evaporation_scalar_solver_coupling) {	
	DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask ) {
	  ublk_uds_data->volume_source_term[voxel] += g_density_scale_factor * ublk->p_data()->s.particle_evaporation_rate[voxel] / sim.char_density;
	}
      }
    }
  }
                
}


static void update_uds_volume_source_term(UBLK ublk, VOXEL_MASK_8 dynamics_voxel_mask, UBLK_DYNAMICS_DATA dynamics_data, auINT32 fluid_type, FLUID_DYN_DCACHE dcache, BOOLEAN is_lb_uds)
{
  switch (fluid_type) {
  case BASIC_FLUID_TYPE:
    update_uds_volume_source_term_internal<FLUID_PHYSICS_DESCRIPTOR>(ublk, dynamics_voxel_mask, dynamics_data, dcache, TRUE, is_lb_uds);
    break;
  case POROUS_FLUID_TYPE:
  case CURVED_POROUS_FLUID_TYPE:
  case CURVED_HX_POROUS_FLUID_TYPE:
    update_uds_volume_source_term_internal<POROUS_MEDIA_PHYSICS_DESCRIPTOR>(ublk, dynamics_voxel_mask, dynamics_data, dcache, FALSE, is_lb_uds);
    break;
  case FAN_FLUID_TYPE:
    update_uds_volume_source_term_internal<FAN_PHYSICS_DESCRIPTOR>(ublk, dynamics_voxel_mask, dynamics_data, dcache, FALSE, is_lb_uds);
    break;
  case TABLE_FAN_FLUID_TYPE:
    update_uds_volume_source_term_internal<TABLE_FAN_PHYSICS_DESCRIPTOR>(ublk, dynamics_voxel_mask, dynamics_data, dcache, FALSE, is_lb_uds);
    break;
  }
}

static
VOID may_be_ublk_uds_solve(UBLK ublk,
			   VOXEL_MASK_8 dynamics_voxel_mask,
			   tUBLK_DYNAMICS_DATA<sFLUID_PHYSICS_DESCRIPTOR, UBLK_SDFLOAT_TYPE_TAG> *dynamics_data,
			   auINT32 fluid_type,
			   FLUID_DYN_DCACHE dcache,
			   ACTIVE_SOLVER_MASK active_solver_mask,
			   UBLK neighbors[6])
{
  //if (dcache->c()->is_UDS_PDE_active) {
  {
    update_uds_volume_source_term(ublk, dynamics_voxel_mask, dynamics_data, fluid_type, dcache, FALSE);
    compute_pde_uds_flux_limiter(dcache, ublk, dynamics_voxel_mask, active_solver_mask, neighbors);
  }
}
#endif

// This function performs the following functions.
// For D19 lattice
// a) Initialization of current states if needed
// b) V2V Advection into current states
// c) Finalization of S2V advected quantities.
// d) Coalesce operation if needed
// e) Voxel dynamics
// f) Scalar solver
// f) Measurement data accumulation

// For 5G and D39 lattice,
// operations e) and f) are replaced by mme calculation from
// advected states. Voxel dynamics and measurements are performed in a
// separate loop over all voxels.

template <BOOLEAN is_2D, BOOLEAN IS_T_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
VOID ublk_adv_and_dyn(UBLK ublk, BOOLEAN is_timestep_even,
                      SOLVER_INDEX_MASK prior_solver_index_mask,
                      ACTIVE_SOLVER_MASK active_solver_mask) 
{
  UBLK active_nbrs[N_MOVING_STATES] = {nullptr};
#ifdef DEBUG_CONDUCTION_SOLVER
  UBLK mirror_ublk = ublk_from_id(2034,STP_COND_REALM);
      BOOLEAN print_debug_msg = FALSE;
      if (mirror_ublk->lb_states(1)->m_states[19][5]!=0)
	print_debug_msg = TRUE;
      if (print_debug_msg){ 
	msg_print("T %ld begin ublk_adv_and_dyn U %d mirrorU %d", g_timescale.m_time, ublk->id(), mirror_ublk->id());
	msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
	msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
      }
#endif
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      if (DEBUG_UBLK(ublk)){
        //VOXEL_GRADS all_voxel_grads = ublk->surf_turb_data()->voxel_grads;
        asINT32 voxel = VOXEL_OF_INTEREST;
        //asINT32 voxel1 = 4;
        asINT32 next_t_index = ublk->has_two_copies()
                                 ? t_index_from_mask(prior_solver_index_mask) ^ 1
                                 : ONLY_ONE_COPY;
        asINT32 next_lb_index = ublk->has_two_copies()
                                 ? lb_index_from_mask(prior_solver_index_mask) ^ 1
				   : ONLY_ONE_COPY;
	LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Begin ublk_adv_and_dyn U %d t_index %d", ublk->id(), next_t_index);
        for (asINT32 latvec = 0; latvec < N_STATES; latvec++){
          //sdFLOAT state = ublk->lb_states(next_lb_index)->m_states[latvec][voxel];
	  sdFLOAT state_t = ublk->t_data()->lb_t_data(next_t_index)->m_states_t[latvec][voxel];
	  //if (std::isnan(state) || std::isnan(state_t)){
	  //  LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("nan ublk %d", ublk->id());
            //LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("state[%d][%d] = %.10g", latvec,voxel,ublk->lb_states(next_lb_index)->m_states[latvec][voxel]);
            LOG_MSG("CONDUCTION_SOLVER",LOG_TS).printf("state_t[%d][%d] = %.10g",
                latvec, voxel,
                ublk->t_data()->lb_t_data(next_t_index)->m_states_t[latvec][voxel]);
          //}
	}
      }
#endif
  sFLUID_DYN_MEAS_DCACHE mcache;

  if (ublk->does_advect_through_swap()) {
    collect_advect_neighbors<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE /*SWAP*/>(ublk,
							   prior_solver_index_mask,
							   is_timestep_even,
							   active_nbrs);
  } else if (!ublk->m_are_any_neighbors_split.any()) {
    
    collect_advect_neighbors<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE /*PDE*/>(ublk,
							    prior_solver_index_mask,
							    is_timestep_even,
							    active_nbrs);
  }
  
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_ublk(ublk, dcache, active_solver_mask);
  BOOLEAN is_near_surface = ublk->is_near_surface();
  BOOLEAN is_near_regular_surface = ublk->is_near_regular_surface();  // Not near sampling surface
  BOOLEAN is_split = ublk->is_split();
  SOLVER_INDEX_MASK curr_solver_index_mask = prior_solver_index_mask ^ active_solver_mask;  
  
#if ENABLE_SIM_COUNTERS
  // increment counters for ublk dyn (dyn for span advect ublks, and adv+dyn for pde advect ublks)
  // basic fluid voxels and special fluid voxels are both being considered. The user should create cases
  // with a single physics type of ublks when calculating the dyn load factors.

  // Should not count fringe or fringe2 ublks since they are not used in timers.
  // Sliding nearblks are not counted since they are not timed.
  if (!ublk->is_fringe() && !ublk->is_fringe2() && !ublk->is_mlrf_surfel_interacting()) {
    BOOLEAN is_pde_advect_ublk = ublk->has_two_copies();

    VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
 
    // If a ublk contains basic fluid, increase the number of ublks for the basic fluid. Otherwise increase the number 
    // of ublks for a special fluid. The number is only incremented once.
    BOOLEAN is_ublk_num_incremented = FALSE;

    auINT32 basic_fluid_voxel_mask = ublk->basic_fluid_voxel_mask;
    auto dynamics_data = ublk->dynamics_data();
    if (basic_fluid_voxel_mask) {
        dynamics_voxel_mask &= ~basic_fluid_voxel_mask;
        dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next() ;
        // increment the number of ublks
        // Use ublk_dyn_timer_from_fluid_type() instead of ublk->get_fluid_dyn_timer() since the latter only uses the first fluid type
        timer_accum_counters(ublk_dyn_timer_from_fluid_type(is_pde_advect_ublk, is_split, is_near_surface, BASIC_FLUID_TYPE), 0, 1);
        is_ublk_num_incremented = TRUE;
    }
    auINT32 num_voxels = bitcount8(basic_fluid_voxel_mask);
    
    while (dynamics_voxel_mask) {
      auINT32 fluid_type = dynamics_data->fluid_type();
      SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      num_voxels += bitcount8(special_fluid_voxel_mask);

      if (!is_ublk_num_incremented) {
        is_ublk_num_incremented = TRUE;
        timer_accum_counters(ublk_dyn_timer_from_fluid_type(is_pde_advect_ublk, is_split, is_near_surface, fluid_type), 0, 1);
      }

      if (ublk->is_near_surface())
        timer_accum_counters(ublk_dyn_timer_from_fluid_type(is_pde_advect_ublk, is_split, is_near_surface, fluid_type), 2, bitcount8(special_fluid_voxel_mask));

      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next() ;     
    }
  }
#endif // if sim counters are used
  
#ifdef DEBUG_NEXTGEN
  if (ublk->id() == 44) {
    print_voxel_states("Before advect", ublk->id(), -1, 0);
  }
#endif

  if (dcache->c()->is_LB_active) {
    if (ublk->does_advect_through_swap()) {   // swap advect
#if BUILD_5G_LATTICE
      if (sim.is_large_pore)
	msg_error("For large pore simulation, ublk (%d) should have two sets of states.", ublk->id());
#endif

#if EXA_USE_AVX && !(BUILD_DOUBLE_PRECISION)      
      if (g_use_avx2_advection) {
        if (ublk->ublk_has_two_copy_neighbor()) {
          ADVECT_VECTORIZED::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                         is_timestep_even, prior_solver_index_mask,active_solver_mask);
        } else {
          ADVECT_VECTORIZED::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                          is_timestep_even, prior_solver_index_mask,active_solver_mask);
        }
      } else { //g_use_avx2_advection
        if (ublk->ublk_has_two_copy_neighbor()) {
          ADVECT_SCALAR::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                     is_timestep_even, prior_solver_index_mask,active_solver_mask);
        } else {
          ADVECT_SCALAR::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                      is_timestep_even, prior_solver_index_mask,active_solver_mask);
        }
      }//g_use_avx2_advection
#else
      if (ublk->ublk_has_two_copy_neighbor()) {
        ADVECT_SCALAR::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, TRUE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                   is_timestep_even, prior_solver_index_mask,active_solver_mask);
      } else {
        ADVECT_SCALAR::swap_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON, FALSE>(ublk, ublk->fluid_like_voxel_mask, active_nbrs,
                                                                    is_timestep_even, prior_solver_index_mask,active_solver_mask);
      }
#endif
    } else {   // gather advect
      asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
      asINT32 curr_lb_index = 1 ^ prev_lb_index;
      asINT32 prev_t_index = t_index_from_mask(prior_solver_index_mask);
      // For ublks which have two sets of states, but do not interact with the surfels
      //ublk->maybe_init_states<IS_T_LB_SOLVER_ON>(prev_lb_index, prev_t_index, sim.is_turb_model);
      //ublk->copy_states<IS_T_LB_SOLVER_ON>(prev_lb_index, prev_t_index, sim.is_turb_model);
      ublk_gather_advect<is_2D, IS_T_LB_SOLVER_ON, IS_UDS_LB_SOLVER_ON>(ublk, is_timestep_even,
									prior_solver_index_mask,
									active_solver_mask,
									active_nbrs);
      // Voxel gradients should be calculated after advection since on-demand explode
      // is done in advection, and a neighbor could be a VR fine ublk and pressure
      // data could be used in the pressure gradients calculation.
      if (sim.is_turb_model && ublk->is_near_surface() ) {		
        VOXEL_GRADS all_voxel_grads = ublk->surf_turb_data()->voxel_grads;
        asINT32 voxel_grad_timestep_index = prev_lb_index;
        if (g_use_avx2_advection){	
          compute_pressure_gradients_avx2(ublk, ublk->lb_interaction_voxel_mask,
                                          all_voxel_grads, voxel_grad_timestep_index);
        } else {
          compute_pressure_gradients(ublk, ublk->lb_interaction_voxel_mask,
                                     all_voxel_grads, voxel_grad_timestep_index);
        }
      }
#ifdef DEBUG_COND
      if (ublk->id()==3912){
        msg_print("T %ld after compute grad p U %d", g_timescale.m_time, ublk->id());
        VOXEL_GRADS all_voxel_grads = ublk->surf_turb_data()->voxel_grads;
        asINT32 voxel = 2;
        VOXEL_GRADS full_voxel_grads = all_voxel_grads + voxel;
        
        msg_print("voxel gradp %.17g, %.17g, %.17g", full_voxel_grads->gradp[0], full_voxel_grads->gradp[1], full_voxel_grads->gradp[2]);
      }
#endif
#ifdef DEBUG_CONDUCTION_SOLVER
      if (print_debug_msg){ 
	msg_print("After compute pressure gradients");
	msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(0)->m_states[19][5]);
	msg_print("mirror_ublk_state[19][5] = %g", mirror_ublk->lb_states(1)->m_states[19][5]);
      }
#endif
    }
#ifdef DEBUG_NEXTGEN
    if (ublk->id() == 44) {
      print_voxel_states("After advect", ublk->id(), -1, 0);
    }
#endif

    BOOLEAN is_near_surface = ublk->is_near_surface();
    if (is_near_surface) {
      ublk->post_advect_finalize(prior_solver_index_mask, active_solver_mask);
    }
    
    if (ublk->is_vr_coarse()) {
      // print_voxel_states("Before Coalesce", ublk->id(), -1, 0);
      // only done to unset is exploded flag for VR fine ublks
      // For VR coarse nearblks interacting only with sampling surfels, the states are not cleared during s2v, and thus we shouldn't accumulate states during coalesce.
      execute_coalesce_rule(ublk, prior_solver_index_mask, active_solver_mask);

#ifdef DEBUG_NEXTGEN
      if (ublk->id() == 44) {
        print_voxel_states("After coalesce", ublk->id(), -1, 0);
      }
#endif
    }

    UBLK neighbors[6] = {NULL};
    find_cardinal_neighbors(ublk, neighbors, dcache->c()->n_dims);
#if BUILD_5G_LATTICE 
    cassert(ublk->basic_fluid_voxel_mask == ublk->fluid_like_voxel_mask);

    compute_ublk_moments_5g(ublk, ublk->basic_fluid_voxel_mask, prior_solver_index_mask);

#elif BUILD_D39_LATTICE 
    compute_voxel_mme_hybrid_ublk(ublk, dcache, neighbors);
    if (ublk->is_vr_coarse()) {     
      execute_explode_mme(ublk, curr_solver_index_mask, active_solver_mask);
    }

    // XDU: Do we need to reflect VR coarse ublks? VR fine ublk mme solver data is reflected in execute_explode_mme();
    if (ublk->has_mirror()) {
      reflect_curr_states_to_mirror_ublk(ublk, prior_solver_index_mask);
      reflect_solver_data_to_mirror_ublk(ublk, active_solver_mask);
    }
#else // D19 lattice 
    if(IS_UDS_LB_SOLVER_ON && sim.is_pf_model){
      // phasefiled
      compute_ublk_moments_phasefield(ublk, dcache, ublk->basic_fluid_voxel_mask);
      if (ublk->is_bsurfel_interacting()) { // do voxel mme and skip the rest
        compute_voxel_mme(ublk, dcache);
        ublk->reset_ib_body_force();
      }
      if (ublk->is_vr_coarse()) {     
        execute_explode_mme(ublk, curr_solver_index_mask, active_solver_mask);
      }

      // XDU: Do we need to reflect VR coarse ublks? VR fine ublk mme solver data is reflected in execute_explode_mme();
      if (ublk->has_mirror()) {
        reflect_curr_states_to_mirror_ublk(ublk, prior_solver_index_mask);
        reflect_solver_data_to_mirror_ublk(ublk, active_solver_mask);
      }

      return;
    }

    if (ublk->is_bsurfel_interacting() || sim.is_HSExt_solver || sim.is_hydrogen_eos) { // do voxel mme and skip the rest
      compute_voxel_mme(ublk, dcache);
      if (ublk->is_vr_coarse()) {     
        execute_explode_mme(ublk, curr_solver_index_mask, active_solver_mask);
      }
      ublk->reset_ib_body_force();

      // XDU: Do we need to reflect VR coarse ublks? VR fine ublk mme solver data is reflected in execute_explode_mme();
      if (ublk->has_mirror()) {
        reflect_curr_states_to_mirror_ublk(ublk, prior_solver_index_mask);
        reflect_solver_data_to_mirror_ublk(ublk, active_solver_mask);
      }

      return;
    }

    VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
    sUBLK_DYNAMICS_DATA *dynamics_data = ublk->dynamics_data();
    if (ublk->basic_fluid_voxel_mask.any()) {

      prefetch_ublk_dynamics_data(ublk,
				  prior_solver_index_mask,
				  active_solver_mask, TRUE);
	
      ccDOTIMES(n, 6) {
	prefetch_ublk_dynamics_data (neighbors[n],
				     prior_solver_index_mask,
				     active_solver_mask, FALSE); 
      }

      if constexpr(IS_UDS_LB_SOLVER_ON) {
	if (dcache->c()->is_UDS_PDE_active){
	  update_uds_volume_source_term(ublk, dynamics_voxel_mask, dynamics_data, BASIC_FLUID_TYPE, dcache, TRUE);
	}
      }

      fluid_dyn_ublk(ublk, dynamics_data, dcache, &mcache, active_solver_mask, ublk->basic_fluid_voxel_mask, neighbors);
      
      //for uds lb solver, no need for surfel source term, because no feedback from LB_UDS to T solver
      if (!IS_UDS_LB_SOLVER_ON && dcache->c()->is_UDS_PDE_active) {  	  
	may_be_ublk_uds_solve(ublk, ublk->basic_fluid_voxel_mask, dynamics_data, BASIC_FLUID_TYPE, dcache, active_solver_mask, neighbors);
      } 

      asINT32 n_windows = dynamics_data->n_meas_windows();

      if(dcache->c()->is_mme_accumulation_on)
        accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

      if (n_windows > 0) {
        sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();
        measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);
      }
      dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next() ;
    }

    while (dynamics_voxel_mask.any()) {
      SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      asINT32 n_windows = dynamics_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();
      STP_PHYS_VARIABLE resistance_scale_factor = scale_to_delta_t(ublk->scale());
      // update_dcache_omega_c_vars(dcache, dynamics_data->omega_c_base());
      dynamics_data->add_dynamic_attributes_to_dcache(special_fluid_voxel_mask, ublk, dcache);

      auINT32 fluid_type = dynamics_data->fluid_type();
      bool is_porous_media = false;

      if constexpr(IS_UDS_LB_SOLVER_ON) {
	if (dcache->c()->is_UDS_PDE_active){
	  update_uds_volume_source_term(ublk, dynamics_voxel_mask, dynamics_data, fluid_type, dcache, TRUE);
	}
      }

      switch (fluid_type) {
        case POROUS_FLUID_TYPE:
          porous_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                               dcache, &mcache, active_solver_mask,
                               neighbors, resistance_scale_factor);
          is_porous_media = true;
          break;
        case CURVED_POROUS_FLUID_TYPE:
          curved_porous_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                                      dcache, &mcache, active_solver_mask,
                                      neighbors, resistance_scale_factor);
          is_porous_media = true;
          break;
        case CURVED_HX_POROUS_FLUID_TYPE:
          curved_hx_ublk_dynamics(ublk, dynamics_data,
                                  special_fluid_voxel_mask, dcache, &mcache,
                                  active_solver_mask, neighbors);
          is_porous_media = true;
          break;
        case FAN_FLUID_TYPE:
          fan_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                            dcache, &mcache, active_solver_mask, neighbors,
                            resistance_scale_factor) ;
          break;
        case TABLE_FAN_FLUID_TYPE:
          table_fan_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                                  dcache, &mcache, active_solver_mask, neighbors);
          break;

      }

      //for uds lb solver, no need for surfel source term, because no feed back from LB_UDS to T solver
      if (!IS_UDS_LB_SOLVER_ON && dcache->c()->is_UDS_PDE_active) {	
	may_be_ublk_uds_solve(ublk, special_fluid_voxel_mask, dynamics_data, fluid_type, dcache, active_solver_mask, neighbors);
      }

      auto dcache_pm = dcache->get_component_or_null<sFLUID_DYN_DCACHE_PM>();
      // Scale down velocity for APM
      if (is_porous_media) {
        dcache_and_mcache_pm_correction(dcache, &mcache, special_fluid_voxel_mask);
      }
      
      if(dcache->c()->is_mme_accumulation_on)
        accumulate_mme_ckpt_data(dcache, &mcache, ublk, special_fluid_voxel_mask);

      measurement_accumulate(ublk, dcache, &mcache, special_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

      // resetting these to default values since they have been used while measurements
      if (is_porous_media) {
        ccDO_UBLK(voxor) {
          mcache.volume_scale_factors[voxor] = 1.0f;
          dcache_pm->APM_porosity[voxor] = 1.0f;
          dcache_pm->APM_tortuosity_coupling[voxor] = 1.0f;
        }
      }
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
    }


#endif
  }
#if BUILD_D19_LATTICE
  else if (sim.is_turb_model) { // LB is not active
    if (dcache->c()->is_KE_PDE_active || dcache->c()->is_T_PDE_active) {
      VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
      auto dynamics_data = ublk->dynamics_data();
      BOOLEAN is_near_surface = ublk->is_near_surface();
      if (is_near_surface) {
        ublk->post_advect_finalize(prior_solver_index_mask, active_solver_mask);
      }
      if (ublk->is_vr_coarse()) {
        execute_coalesce_rule(ublk, prior_solver_index_mask, active_solver_mask);
      }
      UBLK neighbors[6] = {NULL};

      find_cardinal_neighbors(ublk, neighbors, dcache->c()->n_dims);
      if (ublk->basic_fluid_voxel_mask.any()) {
        compute_basic_fluid_turb(dcache, &mcache, ublk, active_solver_mask, neighbors);
	if(dcache->c()->is_UDS_PDE_active && !IS_UDS_LB_SOLVER_ON /*dcache->c()->is_UDS_pde_solver_on*/)
	  may_be_ublk_uds_solve(ublk, ublk->basic_fluid_voxel_mask, dynamics_data, BASIC_FLUID_TYPE, dcache, active_solver_mask, neighbors);
        asINT32 n_windows = dynamics_data->n_meas_windows();
        sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

        if(dcache->c()->is_mme_accumulation_on)
          accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

        measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

        dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;
        dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
      }
      while (dynamics_voxel_mask.any()) {
        auINT32 fluid_type = dynamics_data->fluid_type();
        SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
        VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
        BODY_FORCE_VOXEL_TYPE voxel_type;
        switch (fluid_type) {
          case POROUS_FLUID_TYPE:
          case CURVED_POROUS_FLUID_TYPE:
          case CURVED_HX_POROUS_FLUID_TYPE:
            voxel_type = POROUS_MEDIA;
            break;
          case FAN_FLUID_TYPE:
            voxel_type = (sim.use_local_vel_fan_model
                         ? LOCAL_AXIAL_VEL_POLYNOMIAL_FAN
                         : MEAN_AXIAL_VEL_POLYNOMIAL_FAN);
            break;
          case TABLE_FAN_FLUID_TYPE:
            voxel_type = MEAN_AXIAL_VEL_TABLE_FAN;
            break;
        }

        compute_special_fluid_turb(dcache, &mcache, special_dyn_data, ublk, voxel_type,
                                   special_fluid_voxel_mask,
                                   active_solver_mask, neighbors);
	if(dcache->c()->is_UDS_PDE_active && !IS_UDS_LB_SOLVER_ON /*dcache->c()->is_UDS_pde_solver_on*/)
	  may_be_ublk_uds_solve(ublk, special_fluid_voxel_mask, special_dyn_data, fluid_type, dcache, active_solver_mask, neighbors);
        asINT32 n_windows = dynamics_data->n_meas_windows();
        sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

        if(dcache->c()->is_mme_accumulation_on)
          accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

        measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

        dynamics_voxel_mask &= ~special_fluid_voxel_mask;
        dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
      }
    }
  } else if (dcache->c()->is_T_PDE_active) {  //DNS and heat transfer
    VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
    auto dynamics_data = ublk->dynamics_data();
    UBLK neighbors[6] = {NULL};
    find_cardinal_neighbors(ublk, neighbors, dcache->c()->n_dims);
    if (ublk->basic_fluid_voxel_mask.any()) {
#if !BUILD_GPU      //Avoid linker issues
      compute_basic_fluid_dns_temp(dcache, &mcache, ublk, neighbors);
#endif  
      if(dcache->c()->is_UDS_PDE_active && !IS_UDS_LB_SOLVER_ON /*dcache->c()->is_UDS_pde_solver_on*/)
	may_be_ublk_uds_solve(ublk, ublk->basic_fluid_voxel_mask, dynamics_data, BASIC_FLUID_TYPE, dcache, active_solver_mask, neighbors);
      BOOLEAN is_near_surface = ublk->is_near_surface();
      asINT32 n_windows = dynamics_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

      if(dcache->c()->is_mme_accumulation_on)
          accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

      measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

      dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
    }
    while (dynamics_voxel_mask.any()) {
      auINT32 fluid_type = dynamics_data->fluid_type();
      SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      BODY_FORCE_VOXEL_TYPE voxel_type;
      switch (fluid_type) {
        case POROUS_FLUID_TYPE:
        case CURVED_POROUS_FLUID_TYPE:
        case CURVED_HX_POROUS_FLUID_TYPE:
          voxel_type = POROUS_MEDIA;
          break;
        case FAN_FLUID_TYPE:
          voxel_type = (sim.use_local_vel_fan_model
                       ? LOCAL_AXIAL_VEL_POLYNOMIAL_FAN
                       : MEAN_AXIAL_VEL_POLYNOMIAL_FAN);
          break;
        case TABLE_FAN_FLUID_TYPE:
          voxel_type = MEAN_AXIAL_VEL_TABLE_FAN;
          break;
      }

#if !BUILD_GPU      
      compute_special_fluid_dns_temp(dcache, &mcache, ublk, special_dyn_data, voxel_type, special_fluid_voxel_mask, neighbors);
#endif      
      if(dcache->c()->is_UDS_PDE_active && !IS_UDS_LB_SOLVER_ON /*dcache->c()->is_UDS_pde_solver_on*/)
	may_be_ublk_uds_solve(ublk, special_fluid_voxel_mask, special_dyn_data, fluid_type, dcache, active_solver_mask, neighbors);
      BOOLEAN is_near_surface = ublk->is_near_surface();
      asINT32 n_windows = dynamics_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

      if(dcache->c()->is_mme_accumulation_on)
          accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

      measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
    }
  }
#ifdef DEBUG_NEXTGEN
 if (ublk->id() == 44) {
      print_voxel_states("After dynamics", ublk->id(), -1, 0);
  }
#endif
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  //LOG_MSG("COND", LOG_TS).printf("U %d", ublk->id());
  if (g_timescale.m_time >= 0) {
    if (DEBUG_UBLK(ublk)) {
      asINT32 next_lb_index = lb_index_from_mask(prior_solver_index_mask) ^ 1;
      asINT32 next_t_index = t_index_from_mask(prior_solver_index_mask) ^ 1;

      ccDOTIMES(voxel, 8) {
	//LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	//  printf("End dyn %d(%d) ublk.vel: %.17g %.17g %.17g", ublk->id(), voxel,
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[0][voxel],
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[1][voxel],
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[2][voxel]);
	//LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	//  printf("End dyn %d(%d) d.vel: %.10g %.10g %.10g", ublk->id(), voxel,
	//      dcache->vel[0](voxel),
	//      dcache->vel[1](voxel),
	//      dcache->vel[2](voxel));
	LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	  printf("End dyn %d(%d) temp: %.17g", ublk->id(), voxel,
	      ublk->t_data()->fluid_temp[next_t_index][voxel]);
      }

    }
  }
#endif

#if BUILD_D19_LATTICE
    if(sim.thermal_accel.do_T_solver_switch) {
      if (ublk->has_two_copies()) {
        if (sim.T_solver_type == PDE_TEMPERATURE) {
          ublk->seed_T_scalar_solver();
        } else
          ublk->seed_T_pde_solver();
      }
    }
#endif
    prepare_ublk_for_next_timestep(ublk,
				   prior_solver_index_mask,
				   active_solver_mask,
				   dcache->c()->is_LB_active);
#ifdef DEBUG_NEXTGEN
    // if (ublk->id() == 162830) {
      print_voxel_states("AVDY", ublk->id(), -1, 0);
    // }
#endif

#endif
}

template VOID ublk_adv_and_dyn<TRUE , TRUE, TRUE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						  SOLVER_INDEX_MASK prior_solver_index_mask,
						  ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<TRUE , TRUE, FALSE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						   SOLVER_INDEX_MASK prior_solver_index_mask,
						   ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<FALSE, TRUE, TRUE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						  SOLVER_INDEX_MASK prior_solver_index_mask,
						  ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<FALSE, TRUE, FALSE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						   SOLVER_INDEX_MASK prior_solver_index_mask,
						   ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<TRUE , FALSE, TRUE >(sUBLK *ublk, BOOLEAN is_timestep_even,
						    SOLVER_INDEX_MASK prior_solver_index_mask,
						    ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<TRUE , FALSE, FALSE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						    SOLVER_INDEX_MASK prior_solver_index_mask,
						    ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<FALSE, FALSE, TRUE >(sUBLK *ublk, BOOLEAN is_timestep_even,
						    SOLVER_INDEX_MASK prior_solver_index_mask,
						    ACTIVE_SOLVER_MASK active_solver_mask);

template VOID ublk_adv_and_dyn<FALSE, FALSE, FALSE>(sUBLK *ublk, BOOLEAN is_timestep_even,
						    SOLVER_INDEX_MASK prior_solver_index_mask,
						    ACTIVE_SOLVER_MASK active_solver_mask);

#if !BUILD_5G_LATTICE
static void update_conduction_volume_source_term(UBLK ublk, VOXEL_MASK_8 dynamics_voxel_mask, CONDUCTION_UBLK_DYNAMICS_DATA dynamics_data)
{
  CONDUCTION_PHYSICS_DESCRIPTOR pd = dynamics_data->physics_descriptor();
  CONDUCTION_SOLID_PARAMETERS parms = pd->parameters();
  if (pd->some_parameter_time_varying){
    DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask) {
      sdFLOAT voxel_centroid[3];
      voxel_centroid[0] = ublk->centroids(voxel, 0);
      voxel_centroid[1] = ublk->centroids(voxel, 1);
      voxel_centroid[2] = ublk->centroids(voxel, 2);
      pd->eval_time_and_space_varying_parameter_program(voxel_centroid, NULL, g_timescale.m_time, g_timescale.m_powertherm_time, fluid_eqn_error_handler);
      ublk->conduction_data()->volumetric_source[voxel] = (parms->specify_heat_via_power_density.value > 0.0? parms->imposed_heat.value * g_density_scale_factor: 
                                                           parms->imposed_heat_in_power.value * parms->scaled_volume_fraction.value/parms->total_simulated_volume.value);
    }
  }
}

static void maybe_update_conduction_material_props(FLUID_DYN_DCACHE dcache, UBLK ublk, VOXEL_MASK_8 dynamics_voxel_mask, 
                                                   CONDUCTION_UBLK_DYNAMICS_DATA dynamics_data)
{
  CONDUCTION_PHYSICS_DESCRIPTOR pd = dynamics_data->physics_descriptor();
  CONDUCTION_SOLID_PARAMETERS parms = pd->parameters();
  CONDUCTION_MATERIAL mat = g_conduction_sim_info.conduction_materials[std::round(parms->material_index.value)];
  if (mat->some_parameter_temp_varying) {
    asINT32 prior_conduction_index = dcache->c()->CONDUCTION_prior_index;
    asINT32 next_conduction_index = prior_conduction_index ^ 1;
    DO_VOXELS_IN_MASK(voxel, dynamics_voxel_mask) {
      STP_GEOM_VARIABLE voxel_centroid[3];
      voxel_centroid[0] = ublk->centroids(voxel, 0);
      voxel_centroid[1] = ublk->centroids(voxel, 1);
      voxel_centroid[2] = ublk->centroids(voxel, 2);
      mat->eval_temp_varying_only_parameter(ublk->conduction_data()->solid_temp[prior_conduction_index][voxel], ublk->scale(), voxel_centroid);

      if (mat->density.is_temp_varying) {
        ublk->conduction_data()->density[prior_conduction_index][voxel] = g_density_scale_factor * mat->density.value;
        ublk->conduction_data()->density[next_conduction_index][voxel] = g_density_scale_factor * mat->density.value;
      }

      if (mat->specific_heat.is_temp_varying) {
        ublk->conduction_data()->C_p[prior_conduction_index][voxel] =  mat->specific_heat.value;
        ublk->conduction_data()->C_p[next_conduction_index][voxel] =  mat->specific_heat.value;
      }

      CONDUCTION_SPEC conduction_spec = (CONDUCTION_SPEC) dynamics_data->conduction_specs[voxel];
      if (mat->conductivity[0].is_temp_varying || mat->conductivity[1].is_temp_varying || mat->conductivity[2].is_temp_varying) {
        ccDOTIMES(i,3) {
          conduction_spec->conductivity[i] = g_density_scale_factor * mat->conductivity[i].value;
        }
        asINT32 csys_index = (asINT32) pd->parameters()->csys_index.cvalue();
        STP_GEOM_VARIABLE voxel_centroid[3];
        voxel_centroid[0] = ublk->centroids(voxel, 0);
        voxel_centroid[1] = ublk->centroids(voxel, 1);
        voxel_centroid[2] = ublk->centroids(voxel, 2);
        sdFLOAT transformed_conductivity_tensor[3][3] = {};
        dynamics_data->transform_conductivity_tensor(conduction_spec, csys_index, voxel_centroid, transformed_conductivity_tensor);
        ccDOTIMES(i,3) {
          ccDOTIMES(j,3) {
            ublk->conduction_data()->conductivity[prior_conduction_index][i][j][voxel] = transformed_conductivity_tensor[i][j];
            ublk->conduction_data()->conductivity[next_conduction_index][i][j][voxel] = transformed_conductivity_tensor[i][j];
          }
        }
      }

    } //voxels
  } //some_parameter_temp_varying
}

VOID ublk_conduction_dyn(UBLK ublk,
                         SOLVER_INDEX_MASK prior_solver_index_mask,
                         ACTIVE_SOLVER_MASK active_solver_mask) {
  // CONDUCTION-CHECK: nothing on dcache yet, decide what should go there
  sFLUID_DYN_MEAS_DCACHE mcache;
  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  fluid_dyn_setup_per_ublk(ublk, dcache, active_solver_mask);

  if (dcache->c()->is_CONDUCTION_PDE_active) {
    VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
    auto *dynamics_data = ublk->dynamics_data(); //CONDUCTION-CHECK: change to conduction physics descriptor?

    if (ublk->is_vr_coarse()) {     
      //CONDUCTION-CHECK: we only need to unset the exploded flags on VR fine here. Should it really be buried
      //under the name "execute_coalesce_rule" for the conduction solver?
      execute_coalesce_rule_impl<FALSE, FALSE, FALSE>(ublk, prior_solver_index_mask, active_solver_mask);
    }

    if (ublk->has_mirror()) {
      reflect_solver_data_to_mirror_ublk(ublk, active_solver_mask);
    }

    UBLK neighbors[6] = {NULL};
    find_cardinal_neighbors(ublk, neighbors, dcache->c()->n_dims);

    while (dynamics_voxel_mask.any()) {
      auINT32 conduction_solid_type = dynamics_data->fluid_type();
      CONDUCTION_UBLK_DYNAMICS_DATA conduction_dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 conduction_solid_voxel_mask = conduction_dyn_data->voxel_mask();
      //TODO: Specialization based on isotropic/anisotropic conduction
      // switch (conduction_solid_type) {
      //   case ISOTROPIC_SOLID_TYPE:
      //     break;
      //   case ANISOTROPIC_SOLID_TYPE:
      //     break;
      //   case default:
      //     msg_error("Wrong solid type during dynamics");
      //     break;
      // }
#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
      LOG_MSG("CONDUCTION_SOLVER", LOG_TS).printf("Start dynamics on %d", ublk->id());
#endif
      update_conduction_volume_source_term(ublk, conduction_solid_voxel_mask, conduction_dyn_data);
      maybe_update_conduction_material_props(dcache, ublk, conduction_solid_voxel_mask, conduction_dyn_data);
      
      if (!sim.use_implicit_solid_solver) //disable explicit solver for implicit solver 
        compute_pde_conduction_solver(dcache, ublk, conduction_solid_voxel_mask, neighbors);
      
      BOOLEAN is_near_surface = ublk->is_near_surface();
      asINT32 n_windows = dynamics_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

      if (dcache->c()->is_mme_accumulation_on) {
        accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->fluid_like_voxel_mask); //CHECK: should this fluid_like_voxel_mask?
      }

      measurement_accumulate(ublk, dcache, &mcache, ublk->fluid_like_voxel_mask, n_windows,
                             ublk_meas_cell_ptrs);

      dynamics_voxel_mask &= ~conduction_solid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
    }
  }
  prepare_ublk_for_next_timestep(ublk,
                                 prior_solver_index_mask,
                                 active_solver_mask,
                                 dcache->c()->is_LB_active);
}
#endif

VOID ublk_dyn_and_meas(UBLK ublk, BOOLEAN is_timestep_even,
                       SOLVER_INDEX_MASK prior_solver_index_mask,
                       ACTIVE_SOLVER_MASK active_solver_mask)
{
  VOXEL_MASK_8 dynamics_voxel_mask = ublk->fluid_like_voxel_mask;
  auto dynamics_data = ublk->dynamics_data();

  FLUID_DYN_DCACHE dcache = sim_fluid_dyn_dcache;
  sFLUID_DYN_MEAS_DCACHE mcache;
  fluid_dyn_setup_per_ublk(ublk,dcache,active_solver_mask);
  asINT32 n_windows = dynamics_data->n_meas_windows();
  sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();

  BOOLEAN is_near_surface = ublk->is_near_surface();

#if BUILD_5G_LATTICE
  dFLOAT delta_t = scale_to_delta_t(ublk->scale());
  update_dcache_omega_c_vars_const_5g(dcache, delta_t, TRUE);
  if (g_mp_laplace_solver) {
    laplace_ublk_5g(ublk, dcache, ublk->basic_fluid_voxel_mask);
  } else {
    fluid_dyn_ublk_5g(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask);
  }
#ifdef DEBUG_NEXTGEN
    if ((ublk->id() == 361)) {
      std::cout.flush();
      print_voxel_states("After 5g dynamics",ublk->id(), 0, 0);
    }
#endif

  if(dcache->c()->is_mme_accumulation_on)
    accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

  if (n_windows > 0) {
    measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);
  }

#elif BUILD_D39_LATTICE || BUILD_D19_LATTICE
  UBLK neighbors[6] = {NULL};
  find_cardinal_neighbors(ublk, neighbors, dcache->c()->n_dims);

  if (ublk->basic_fluid_voxel_mask.any()) {
    prefetch_ublk_dynamics_data(ublk,
				prior_solver_index_mask,
				active_solver_mask, TRUE);
      
    ccDOTIMES(n, 6) {
      prefetch_ublk_dynamics_data (neighbors[n],
				   prior_solver_index_mask,
				   active_solver_mask, FALSE); 
    }

    if (dcache->c()->is_UDS_lb_solver_on && dcache->c()->is_UDS_PDE_active){
      update_uds_volume_source_term(ublk, ublk->basic_fluid_voxel_mask, dynamics_data, BASIC_FLUID_TYPE, dcache, TRUE);
    } 
    
#if BUILD_D19_LATTICE
    if(sim.is_pf_model){
      dynamics_data->add_dynamic_attributes_to_dcache(ublk->basic_fluid_voxel_mask, ublk, dcache);
      fluid_dyn_ublk_phasefield(ublk, dcache, ublk->basic_fluid_voxel_mask);
    }
#endif    

    fluid_dyn_ublk(ublk, dynamics_data, dcache, &mcache, active_solver_mask, ublk->basic_fluid_voxel_mask, neighbors);
#if (ENABLE_LOGGING && DEBUG_BSURFEL_COMM)
    if (ublk->id() == 13771) {
      std::ostringstream os;
      os << "\n";
      print_voxel_states("AVDY", ublk->id(), -1, 0, -1, os);
      LOG_MSG("BSURFEL_COMM",LOG_FUNC,"TS",g_timescale.m_time) << os.str();
    }
#endif

    if(dcache->c()->is_mme_accumulation_on)
      accumulate_mme_ckpt_data(dcache, &mcache, ublk, ublk->basic_fluid_voxel_mask);

    if (n_windows > 0) {
      measurement_accumulate(ublk, dcache, &mcache, ublk->basic_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);
    }
    dynamics_voxel_mask &= ~ublk->basic_fluid_voxel_mask;
    dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next() ;
  }

  // This condition has to be altered, Porous medium should be called for all solvers  
#if BUILD_D19_LATTICE 
  if (sim.T_solver_type == LB_ENERGY && (sim.is_HSExt_solver || sim.is_hydrogen_eos))  
#else 
  if (sim.T_solver_type == LB_ENERGY) //only for lb_energy here
#endif 
  {
    while (dynamics_voxel_mask.any()) {
      SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      asINT32 n_windows = dynamics_data->n_meas_windows();
      sUBLK_MEAS_CELL_PTR *ublk_meas_cell_ptrs = dynamics_data->meas_cell_ptrs();
      STP_PHYS_VARIABLE resistance_scale_factor = scale_to_delta_t(ublk->scale());
      // update_dcache_omega_c_vars(dcache, dynamics_data->omega_c_base());
      dynamics_data->add_dynamic_attributes_to_dcache(special_fluid_voxel_mask, ublk, dcache);
      auINT32 fluid_type = dynamics_data->fluid_type();
      bool is_porous_media = false;

      if (dcache->c()->is_UDS_lb_solver_on && dcache->c()->is_UDS_PDE_active){
	update_uds_volume_source_term(ublk, special_fluid_voxel_mask, dynamics_data, fluid_type, dcache, TRUE);
      } 
      
      switch (fluid_type) {
        case POROUS_FLUID_TYPE:
          porous_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                  dcache, &mcache, active_solver_mask,
                  neighbors, resistance_scale_factor);
          is_porous_media = true;
          break;
        case CURVED_POROUS_FLUID_TYPE:
          curved_porous_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                                      dcache, &mcache, active_solver_mask,
                                      neighbors, resistance_scale_factor);
          is_porous_media = true;
          break;
        case CURVED_HX_POROUS_FLUID_TYPE:
          curved_hx_ublk_dynamics(ublk, dynamics_data,
                                  special_fluid_voxel_mask, dcache, &mcache,
                                  active_solver_mask, neighbors);
          is_porous_media = true;
          break;
#if BUILD_D19_LATTICE
        case FAN_FLUID_TYPE:
          fan_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                            dcache, &mcache, active_solver_mask, neighbors,
                            resistance_scale_factor) ;
          break;
        case TABLE_FAN_FLUID_TYPE:
          table_fan_ublk_dynamics(ublk, dynamics_data, special_fluid_voxel_mask,
                                  dcache, &mcache, active_solver_mask, neighbors);
          break;
#endif

      }
#if BUILD_D19_LATTICE
      if(dcache->c()->is_UDS_PDE_active && dcache->c()->is_UDS_pde_solver_on)
	may_be_ublk_uds_solve(ublk, special_fluid_voxel_mask, dynamics_data, fluid_type, dcache, active_solver_mask, neighbors);
#endif
      auto dcache_pm = dcache->get_component_or_null<sFLUID_DYN_DCACHE_PM>();
      // Scale down velocity for APM
      if (is_porous_media) {
        dcache_and_mcache_pm_correction(dcache, &mcache, special_fluid_voxel_mask);
      }
      
      if(dcache->c()->is_mme_accumulation_on)
        accumulate_mme_ckpt_data(dcache, &mcache, ublk, special_fluid_voxel_mask);

      measurement_accumulate(ublk, dcache, &mcache, special_fluid_voxel_mask, n_windows, ublk_meas_cell_ptrs);

      // resetting these to default values since they have been used while measurements
      if (is_porous_media) {
        ccDO_UBLK(voxor) {
          mcache.volume_scale_factors[voxor] = 1.0f;
          dcache_pm->APM_porosity[voxor] = 1.0f;
          dcache_pm->APM_tortuosity_coupling[voxor] = 1.0f;
        }
      }
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next();
    }
    
  }
#endif  //#elif BUILD_D39_LATTICE || BUILD_D19_LATTICE
#ifdef DEBUG_NEXTGEN
  // if (ublk->id() == 10600)
    print_voxel_states("AVDY", ublk->id(), -1, 0);
#endif

#if defined(DEBUG) && defined(DEBUG_CONDUCTION_SOLVER)
  //LOG_MSG("COND", LOG_TS).printf("U %d", ublk->id());
  if (g_timescale.m_time % 100 == 0) {
    //if (DEBUG_UBLK(ublk)) {
      asINT32 next_lb_index = ublk->has_two_copies() 
      				? lb_index_from_mask(prior_solver_index_mask) ^ 1
				: ONLY_ONE_COPY;
      asINT32 next_t_index = ublk->has_two_copies() 
      				? t_index_from_mask(prior_solver_index_mask) ^ 1
				: ONLY_ONE_COPY;
      ccDOTIMES(voxel, 8) {
	//LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	//  printf("End dyn %d(%d) ublk.vel: %.10g %.10g %.10g", ublk->id(), voxel,
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[0][voxel],
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[1][voxel],
	//      ublk->lb_data()->m_lb_data[next_lb_index].vel[2][voxel]);
	//LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	//  printf("End dyn %d(%d) d.vel: %.10g %.10g %.10g", ublk->id(), voxel,
	//      dcache->vel[0](voxel),
	//      dcache->vel[1](voxel),
	//      dcache->vel[2](voxel));
	LOG_MSG("CONDUCTION_SOLVER", "TS", g_timescale.m_time).
	  printf("End dyn meas %d(%d) temperature: %.10g", ublk->id(), voxel,
	      ublk->t_data()->fluid_temp[next_t_index][voxel]);
      }
    }
#endif
  
  prepare_ublk_for_next_timestep(ublk, prior_solver_index_mask, active_solver_mask,
                                 dcache->c()->is_LB_active);
}

#if BUILD_5G_LATTICE
template<BOOLEAN IS_UDS_LB>
VOID ublk_adv_states(UBLK ublk, asINT32 next_index) {
  if (ublk->basic_fluid_voxel_mask.any()) {
    construct_pm_advect_states<IS_UDS_LB>(ublk, next_index, ublk->basic_fluid_voxel_mask); 
    if (ublk->has_mirror()) 
      reflect_adv_states_to_mirror_ublk(ublk, next_index);
  } else {
    msg_error("Large pore solver does not support special fluid regions.");
  }
}

template VOID ublk_adv_states<TRUE>(UBLK ublk, asINT32 next_index);
template VOID ublk_adv_states<FALSE>(UBLK ublk, asINT32 next_index);

VOID compute_eigvec_110(ubFLOAT grad2[],ubFLOAT grad[],sdFLOAT *a,asINT32 voxel){
      //one of the three eigenvalues is zero

      sdFLOAT b1,b2,b3,b4;//reduced 2x2 system
      std::vector<sdFLOAT> vec1,vec2;
      sdFLOAT val1,val2;
      sdFLOAT b[9] = {grad2[0](voxel),grad2[3](voxel),grad2[4](voxel),\
                      grad2[3](voxel),grad2[1](voxel),grad2[5](voxel), \
                      grad2[4](voxel),grad2[5](voxel),grad2[2](voxel)}; //fully populated hessian matrix
      auINT8 ind=0;
      ccDOTIMES(i, 3) {
        if(fabs(grad[i](voxel))<std::numeric_limits<dFLOAT>::epsilon()) ind=i;
      }
      //cross off the ind-th column and row from the 3x3 system
      if(ind==0){
        a[2] = 1.0f;
        b1=b[4];b2=b[5];b3=b[7];b4=b[8];
      }
      else if(ind==1){
        a[5] = 1.0f;
        b1=b[0];b2=b[2];b3=b[6];b4=b[8];
      }
      else{
        a[8] = 1.0f;
        b1=b[0];b2=b[1];b3=b[3];b4=b[4];
      }
      //printf("----:%e\n",b1*b1 + 4*b2*b3 - 2.0f*b1*b4 + b4*b4);
      //printf("ind:%d, b:%e,%e,%e,%e\n",ind,b1,b2,b3,b4);
      if(fabs(b3)<FLT_EPSILON){
        val1=b1;
        val2=b4;
        vec1.push_back(1.0f);
        vec1.push_back(0.0f);
        if(fabs(b1-b4)>FLT_EPSILON)
          vec2.push_back(-b2/(b1-b4));
        else
          vec2.push_back(0.0f);
        vec2.push_back(1.0f);
      }
      else{
        sdFLOAT underSqrt = (b1*b1 + 4*b2*b3 - 2.0f*b1*b4 + b4*b4);
        val1=0.5f*(b1 + b4 - sqrt(underSqrt));
        val2=0.5f*(b1 + b4 + sqrt(underSqrt));
        vec1.push_back(-((-b1 + b4 + sqrt(underSqrt))/(2.0f*b3)));
        vec1.push_back(1.0f);
        vec2.push_back(-((-b1 + b4 - sqrt(underSqrt))/(2.0f*b3)));
        vec2.push_back(1.0f);
      }
      //sort eigvec/eigval descendingly
      if(fabs(val2)>fabs(val1)) vec1.swap(vec2);

      //printf("vec 2x2: %e,%e,%e,%e\n",vec1[0],vec1[1],vec2[0],vec2[1]);
      //adding back values in the ind-th colum
      if(ind==0){
        a[3] = vec1[0]; a[6] = vec1[1]; a[4] = vec2[0]; a[7] = vec2[1];
      }
      else if(ind==1){
        a[0] = vec1[0]; a[6] = vec1[1]; a[1] = vec2[0]; a[7] = vec2[1];
      }
      else if(ind==2){
        a[0] = vec1[0]; a[3] = vec1[1]; a[1] = vec2[0]; a[4] = vec2[1];
      }
}

bool compAbs (sdFLOAT i, sdFLOAT j) { return (fabs(i)>fabs(j)); }

VOID ublk_grad_porosity(UBLK dynublk) {

  VOXEL_MASK_8 dynamics_voxel_mask = dynublk->fluid_like_voxel_mask;
  auto dynamics_data = dynublk->dynamics_data();

  if (dynublk->basic_fluid_voxel_mask.any()) {
  if (g_mp_pm_tensor_k0==1) {

  sdFLOAT scale = g_gradient_operator->get_scale();
  TAGGED_UBLK neighbor_ublks[27];
  BOOLEAN is_near_surface = dynublk->is_near_surface();

  UBLK_UBFLOAT ublk = (UBLK_UBFLOAT) dynublk;
  VOXEL_MASK_8 voxel_mask = ublk->basic_fluid_voxel_mask;
  ubFLOAT gradPor[3];
  memset(gradPor, 0, sizeof(gradPor));
  ubFLOAT hessian[6];
  memset(hessian, 0, sizeof(hessian));
  ubFLOAT laplacian;
  memset(&laplacian, 0, sizeof(laplacian));
  

  const asINT32 itself_index = dir_index(0, 0, 0);
  const auto& box_access = dynublk->box_access();
  SPLIT_ADVECT_INFO split_advect_info = ublk->get_split_advect_info();
  const sSPLIT_ADVECT_FACTORS* split_advect_factors = dynublk->get_split_advect_factors();

  for (asINT32 latvec = 0, cx, cy, cz; 
       (latvec < N_MOVING_STATES) && (cx = state_vx(latvec),
				      cy = state_vy(latvec),
				      cz = state_vz(latvec),
				      TRUE);
       latvec++) {
    asINT32 ublk_index = dir_index(cx, cy, cz);
    sINT16 offsets[3];
    offsets[0] = cx; offsets[1] = cy; offsets[2] = cz;
    neighbor_ublks[ublk_index] = box_access.neighbor_ublk(offsets);
  }
  neighbor_ublks[itself_index] = box_access.stagged_ublk();
 
  FOREACH_MOVING_STATE(i, cx, cy, cz, energy, weight, {
    ubFLOAT neighbor_ublk_potential[MAXIMUM_NUM_COMPONENTS];
    memset(neighbor_ublk_potential, 0, sizeof(neighbor_ublk_potential));
    auINT32 find_neighbor_mask = 0;
    
    ubFLOAT neighbor_ublk_porosity;
    memset(&neighbor_ublk_porosity, 0, sizeof(neighbor_ublk_porosity));
    
    DO_VOXELS_IN_MASK(voxel, voxel_mask) {
      asINT32 ublk_index = g_gradient_operator->s_ublk_index[i][voxel];
      asINT32 voxel_index = g_gradient_operator->s_voxel_index[i][voxel];
      if (neighbor_ublks[ublk_index].is_ublk_scale_interface()) {
        msg_internal_error("scale interface not handled 5G\n");
      }

      //BOOLEAN pm_find_neighbor = FALSE; //large pore and multi-phase

      if (neighbor_ublks[ublk_index].is_ublk_split())  {

        CONNECT_MASK fluid_connect_mask = dynublk->voxel_fluid_connect_mask(voxel);
        if (fluid_connect_mask.test(i)) {
          sTAGGED_SPLIT_FACTOR neighbor_split_factors[MAX_SPLIT_UBLKS];
          asINT32 n_neighbor_splits =
            tagged_neighbor_from_split_ublk_site(neighbor_ublks[ublk_index],
                                                 dynublk->advect_from_split_mask(),
                                                 voxel, i,
                                                 split_advect_info,
                                                 dynublk->m_split_tagged_instance.get(),
                                                 neighbor_split_factors);

          ccDOTIMES( nInt, n_neighbor_splits) {
            sdFLOAT split_fac = neighbor_split_factors[nInt].neighbor_split_factor;
            TAGGED_UBLK tagged_neighbor = neighbor_split_factors[nInt].tagged_neighbor;
            VOXEL_MASK_8 neighbor_fluid_voxel_mask{0};
            UBLK  neighbor_ublk = tagged_neighbor.ublk();
            if (neighbor_ublk) {
              neighbor_fluid_voxel_mask = neighbor_ublk->fluid_like_voxel_mask |
                                          neighbor_ublk->all_neighbors_voxel_mask;
              if (neighbor_fluid_voxel_mask.test(voxel_index) ) {
                neighbor_ublk_porosity(voxel) += split_fac * neighbor_ublk->pore_lb_data()->porosity_input[voxel_index];
              }
            }
          }
        }
      } else {
        UBLK neighbor_ublk = neighbor_ublks[ublk_index].ublk();
        VOXEL_MASK_8 neighbor_fluid_voxel_mask{0};
        if (neighbor_ublk) {
          neighbor_fluid_voxel_mask = neighbor_ublk->fluid_like_voxel_mask |
                                      neighbor_ublk->all_neighbors_voxel_mask;
          if (neighbor_fluid_voxel_mask.test(voxel_index) ) {
            neighbor_ublk_porosity(voxel) = neighbor_ublk->pore_lb_data()->porosity_input[voxel_index];
          }
        }
      }
    }

    //hessian matrix
    sdFLOAT one_over_t = g_lattice_constant*g_lattice_constant;
    ccDO_UBLK(voxor){
      sdFLOAT weight = i<6 ? 2.0f:1.0f;
      laplacian[voxor] +=  2.0f*weight * (neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      gradPor[0][voxor] += cx*weight*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      gradPor[1][voxor] += cy*weight*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      gradPor[2][voxor] += cz*weight*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[0][voxor] += cx*cx*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[1][voxor] += cy*cy*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[2][voxor] += cz*cz*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[3][voxor] += cx*cy*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[4][voxor] += cx*cz*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
      hessian[5][voxor] += cy*cz*weight*one_over_t*(neighbor_ublk_porosity[voxor]-ublk->pore_lb_data()->porosity_input[voxor]);
    }

    /* if(ublk->id()==9394){ */
      // DO_VOXELS_IN_MASK(voxel, voxel_mask) {
          // if(voxel==1){
              // printf("nb por:%e, por:%e, latvec:%d,%d,%d\n",neighbor_ublk_porosity(voxel),\
                      // ublk->pore_lb_data()->porosity_input(voxel), cx,cy,cz);
          // }
      // }
    /* } */

  });


  ccDOTIMES(voxor, ubFLOAT::N_VOXORS) {
    gradPor[0][voxor] /= scale;
    gradPor[1][voxor] /= scale;
    gradPor[2][voxor] /= scale;
    laplacian[voxor] /= scale;
    hessian[0][voxor] /= scale;
    hessian[1][voxor] /= scale;
    hessian[2][voxor] /= scale;
    hessian[3][voxor] /= scale;
    hessian[4][voxor] /= scale;
    hessian[5][voxor] /= scale;
    hessian[0][voxor] -= 0.5f*laplacian[voxor];
    hessian[1][voxor] -= 0.5f*laplacian[voxor];
    hessian[2][voxor] -= 0.5f*laplacian[voxor];
    //ublk->pore_mc_data()->wall_potential[voxor] = gradPor[1][voxor];
    //ublk->pore_lb_data()->wall_potential[voxor] = hessian[1][voxor];
  }



  //eigenvalues and eigenvectors
  sdFLOAT branch_entered;
  asINT32 show_debug=0;
  ubFLOAT k_ij[6];
  memset(k_ij, 0, sizeof(k_ij));
  uINT8 two_eigenvalues_close=0;
  sdFLOAT l[3]={0.0f,0.0f,0.0f};
  sdFLOAT scan_res = sim.meters_per_cell*1e6;
  sdFLOAT unit_factor = 9.869233E-4 / (scan_res*scan_res);  //miliDarcy -> cell^2 

  DO_VOXELS_IN_MASK(voxel, voxel_mask) {
    sdFLOAT a[9]={0.0f,0.0f,0.0f,0.0f,0.0f,0.0f,0.0f,0.0f,0.0f};

    uINT8 n_zero_eig=0;   
    ccDOTIMES(i, 3) {
      if(fabs(gradPor[i](voxel))<std::numeric_limits<dFLOAT>::epsilon()) n_zero_eig += 1;
    }

    sdFLOAT b[9] = {hessian[0](voxel),hessian[3](voxel),hessian[4](voxel),\
                    hessian[3](voxel),hessian[1](voxel),hessian[5](voxel), \
                    hessian[4](voxel),hessian[5](voxel),hessian[2](voxel)}; //fully populated hessian matrix
    uINT8 n_zero_h=0;   
    ccDOTIMES(i, 3) {
      uINT8 nn=0;
        if((fabs(b[3*i])<FLT_EPSILON) \
            &&(fabs(b[3*i+1])<FLT_EPSILON) \
            &&(fabs(b[3*i+2])<FLT_EPSILON) ) n_zero_h += 1;
    }


    asINT32 rock_type_index = ublk->pore_lb_data()->rock_type[voxel];
    std::vector<sdFLOAT> k0; //k_tilda_ij

    if(g_mp_pm_table_input){
      cPOROUS_ROCK::sK0 K0_table = sim.rock_types[rock_type_index].get_K0(ublk->pore_lb_data()->porosity_input(voxel));
      ccDOTIMES(i, 3) {
        k0.push_back(MAX(K0_table.estimated_K0[i]*unit_factor,FLT_EPSILON));
      }
    }
    else{
      sdFLOAT k0f = MAX(compute_k0(ublk->pore_lb_data()->porosity_input(voxel)),FLT_EPSILON);
      ccDOTIMES(i, 3) {
        k0.push_back(k0f);
      }
    }


    //k_ij = A.Lambda.A^T
    //where A=((a1,a2,a3),(a4,a5,a6),(a7,a8,a9))
    //and Lambda=((l1,0,0),(0,l2,0),(0,0,l3))
    //therefore the product is
    // a1^2 l1 + a2^2 l2 + a3^2 l3,    a1 a4 l1 + a2 a5 l2 + a3 a6 l3, a1 a7 l1 + a2 a8 l2 + a3 a9 l3
    // a1 a4 l1 + a2 a5 l2 + a3 a6 l3, a4^2 l1 + a5^2 l2 + a6^2 l3,    a4 a7 l1 + a5 a8 l2 + a6 a9 l3
    // a1 a7 l1 + a2 a8 l2 + a3 a9 l3, a4 a7 l1 + a5 a8 l2 + a6 a9 l3, a7^2 l1 + a8^2 l2 + a9^2 l3
    //
    // in zero-indexing, A matrix is
    // a0 a1 a2
    // a3 a4 a5
    // a6 a7 a8
    
    if(n_zero_h==3){
      branch_entered=1.0f;
      a[0]=1.0f; a[4]=1.0f;a[8]=1.0f;
      k0[0]=k0[2]; k0[1]=k0[2];
    }
    else if(n_zero_h==2){
      branch_entered=2.0f;
      auINT8 ind;
      ccDOTIMES(i, 3) {
        if(fabs(gradPor[i](voxel))>std::numeric_limits<dFLOAT>::epsilon()) ind=i;
      }
      //ind-th element of the 1st eigenvector has non-zero element
      //1st eigenvector corresponds to largest eigenvalue
      if(ind==0){
        a[0]=1.0f; a[4]=1.0f;a[8]=1.0f;
      }
      else if(ind==1){
        a[3]=1.0f; a[1]=1.0f;a[8]=1.0f;
      }
      else if(ind==2){
        a[6]=1.0f; a[4]=1.0f;a[2]=1.0f;
      }
      //take care of multiplicity
      k0[1]=k0[2]; 
    }
    else if(n_zero_h==1){
      branch_entered=3.0f;
      compute_eigvec_110(hessian,gradPor,a,voxel);
      if(show_debug){
        printf("eigvec1: %e,%e,%e\n",a[0],a[3],a[6]);
        printf("eigvec2: %e,%e,%e\n",a[1],a[4],a[7]);
        printf("eigvec3: %e,%e,%e\n",a[2],a[5],a[8]);
      }
    }
    else{
      //generic case
      //first get initial guess of an eigenvalue
      sdFLOAT norm = sqrt(gradPor[0](voxel)*gradPor[0](voxel)+gradPor[1](voxel)*gradPor[1](voxel)+gradPor[2](voxel)*gradPor[2](voxel));
      sdFLOAT invt_norm = norm<FLT_EPSILON ? 0.0f: 1.0f/norm;
      sdFLOAT n[3] = {gradPor[0](voxel)*invt_norm, gradPor[1](voxel)*invt_norm, gradPor[2](voxel)*invt_norm};
      sdFLOAT invt_n[3];
      invt_n[0] = n[0]<FLT_EPSILON ? 0.0f: 1.0f/n[0];
      invt_n[1] = n[1]<FLT_EPSILON ? 0.0f: 1.0f/n[1];
      invt_n[2] = n[2]<FLT_EPSILON ? 0.0f: 1.0f/n[2];

      sdFLOAT lambda_avg = (hessian[0](voxel)*n[0] + hessian[3](voxel)*n[1] + hessian[4](voxel)*n[2])*invt_n[0] \
                         + (hessian[3](voxel)*n[0] + hessian[1](voxel)*n[1] + hessian[5](voxel)*n[2])*invt_n[1] \
                         + (hessian[4](voxel)*n[0] + hessian[5](voxel)*n[1] + hessian[2](voxel)*n[2])*invt_n[2]; 
      lambda_avg /= 3.0f;

      //root finding by newton method
      //x' = x - f(x)/f'(x)
      sdFLOAT ll = lambda_avg;
      sdFLOAT f=1.0f;
      sdFLOAT fp=1.0f;
      auINT32 counter=0;
      sdFLOAT b[9] = {hessian[0](voxel),hessian[3](voxel),hessian[4](voxel),\
                      hessian[3](voxel),hessian[1](voxel),hessian[5](voxel), \
                      hessian[4](voxel),hessian[5](voxel),hessian[2](voxel)}; //fully populated hessian matrix

      while(fabs(f)>FLT_EPSILON || counter<2000){
        f = -ll*ll*ll + ll*ll*(b[0] + b[4] + b[8]) \
                  + ll*(b[1]*b[3] + b[2]*b[6] + b[5]*b[7] - b[4]*b[8] - b[0]*(b[4] + b[8])) \
                  + b[1]*b[5]*b[6] - b[0]*b[5]*b[7] + b[2]*(-b[4]*b[6] + b[3]*b[7])  \
                  - b[1]*b[3]*b[8] + b[0]*b[4]*b[8] ;
        fp = -3.0f*ll*ll + 2.0f*ll*(b[0] + b[4] + b[8]) \
                  + b[1]*b[3] + b[2]*b[6] + b[5]*b[7] - b[4]*b[8] - b[0]*(b[4] + b[8]) ;
        ll = ll - f/fp;
        counter++;
      }
      //find the other 2 eigenvalues
      //the cubic equation from det(A-lII)=0 is a*x^3 + b*x^2 + c*x + d = 0,
      //can be rearranged as
      //(x-ll)(a*x^2 + (a*l+b)x + a*l^2+bl+c)=0
      //roots for the 2nd bracket is [-(a*ll+b)+-sqrt((a*ll+b)^2-4*a*(a*ll^2+b*ll+c))]/2a
      sdFLOAT aa = -1;
      sdFLOAT bb = b[0] + b[4] + b[8];
      sdFLOAT cc = b[1]*b[3] + b[2]*b[6] + b[5]*b[7] - b[4]*b[8] - b[0]*(b[4] + b[8]);
      sdFLOAT dd = b[1]*b[5]*b[6] - b[0]*b[5]*b[7] + b[2]*(-b[4]*b[6] + b[3]*b[7])  \
                     - b[1]*b[3]*b[8] + b[0]*b[4]*b[8] ;
      sdFLOAT underSqrtSign = (aa*ll+bb)*(aa*ll+bb)-4.0f*aa*(aa*ll*ll+bb*ll+cc);
      std::vector<sdFLOAT> eig;
      sdFLOAT tmp,v1,v2;
      if(std::isnan(ll)){
        branch_entered=4.1f;
        a[0]=1.0f; a[4]=1.0f;a[8]=1.0f;
      }
      else if(underSqrtSign>0.0f){
        branch_entered=4.2f;
        eig.push_back((-(aa*ll+bb)+sqrt(underSqrtSign))/(2.0f*aa));
        eig.push_back((-(aa*ll+bb)-sqrt(underSqrtSign))/(2.0f*aa));
        eig.push_back(ll);
        //std::sort(eig.begin(),eig.end(),std::greater<sdFLOAT>());
        std::sort(eig.begin(),eig.end(),compAbs);

        if(show_debug){
          printf("--------\n");
          printf("eig: %e,%e,%e\n",eig[0],eig[1],eig[2]);
        }

        //eigenvectors are not unique
        //fix v3 in v=[v1,v2,v3] to be unity and solve a reduced set of 2-variable equations
        //for example, if row 1 and 3 are not coplanar, after combining A-ll*II
        //|aa bb cc|
        //|dd ee ff|.v = 0, where aa=b[0]-ll, bb=b[1], cc=b[2], dd=b[6], ee=b[7], ff=b[8]-ll 
        //therefore
        //x1=-((-cc*ee + bb*ff)/(bb*dd - aa*ee))
        //x2=-((cc*dd - aa*ff)/(bb*dd - aa*ee))

        if(n_zero_eig==1){
          compute_eigvec_110(hessian,gradPor,a,voxel); //better accuracy when 1 row/column is 
                                                       //predominantly zero
        }
        else{
          auINT8 found_eig=0;
          std::vector<auINT8> found_eig_missing_at {0,1,2};
          std::vector<auINT8> found_eig_at;
          std::vector<auINT8>::iterator it0=found_eig_missing_at.begin();
          ccDOTIMES(i,3){
            ccDOTIMES(j,3){
                std::vector<std::vector<sdFLOAT>> coef {{b[0]-eig[i],b[1],b[2]},{b[3],b[4]-eig[i],b[5]},{b[6],b[7],b[8]-eig[i]}};
                std::vector<std::vector<sdFLOAT>>::iterator it=coef.begin();
                auINT8 k = 2-j; //2,1,0
                coef.erase(it+k); //remove a row and create the 2x3 subsystem

                if(show_debug){
                  printf("--------\n");
                  printf("%e,%e,%e\n",coef[0][0],coef[0][1],coef[0][2]);
                  printf("%e,%e,%e\n",coef[1][0],coef[1][1],coef[1][2]);
                }

                sdFLOAT denom = coef[0][1]*coef[1][0] - coef[0][0]*coef[1][1];
                if(fabs(denom)<FLT_EPSILON){
                  continue; //rank 1 matrix, no unique v1 and v2
                }

                sdFLOAT invtDenom = 1.0f/(coef[0][1]*coef[1][0] - coef[0][0]*coef[1][1]);
                v1=-((-coef[0][2]*coef[1][1] + coef[0][1]*coef[1][2]))*invtDenom;
                v2=-((coef[0][2]*coef[1][0] - coef[0][0]*coef[1][2]))*invtDenom;

                //normalize against element with max magnitude
                sdFLOAT n = std::max(fabs(v1),std::max(fabs(v2),sdFLOAT(1.0f)));
                a[i] = v1/n; a[i+3] = v2/n; a[i+6] = 1.0f/n;

                found_eig += 1;
                found_eig_at.push_back(i);
                found_eig_missing_at.erase(std::remove(found_eig_missing_at.begin(), found_eig_missing_at.end(), i), found_eig_missing_at.end());
                if(fabs(eig[2])*5.0F>fabs(eig[1])&&fabs(eig[1])>0.0F)two_eigenvalues_close=1;
                break;
            }//ccDOTIMES 2x3 system
          }
          //all rows are coplanar for certain eigval; 
          if(found_eig==0){ 
            a[0]=1.0f; a[4]=1.0f;a[8]=1.0f;
            k0[0] = k0[2]; k0[1] = k0[2];
          }
          else if(found_eig==1){ 
            auINT8 id0 = found_eig_at[0];
            auINT8 id1 = found_eig_missing_at[0];
            auINT8 id2 = found_eig_missing_at[1];

            a[id1] = -a[id0+3]; a[id1+3] = a[id0]; a[id1+6] = 0.0f; //eigvec2 = [-eigvec1(2),eigvec1(1),0]
            if(a[id1]==0.0&&a[id1+3]==0.0&&a[id1+6]==0.0)
              a[id1] = 0.0f; a[id1+3] = -a[id0+6]; a[id1+6] = a[id0+3]; //eigvec2 = [0,-eigvec1(3),eigvec1(2)]
                                                                        //avoid eigvec2 becoming null

            a[id2]   = a[id0+3]*a[id1+6]-a[id0+6]*a[id1+3]; 
            a[id2+3] = a[id0+6]*a[id1]-a[id0]*a[id1+6]; 
            a[id2+6] = a[id0]*a[id1+3]-a[id0+3]*a[id1] ; //eigvec3 = eigvec1 x eigvec2 
            // i  j  k
            // a0 a3 a6
            // a1 a4 a7
          }
          else if(found_eig==2){ 
            auINT8 id0 = found_eig_at[0];
            auINT8 id1 = found_eig_at[1];
            auINT8 id2 = found_eig_missing_at[0];
            a[id2]   = a[id0+3]*a[id1+6]-a[id0+6]*a[id1+3]; 
            a[id2+3] = a[id0+6]*a[id1]-a[id0]*a[id1+6]; 
            a[id2+6] = a[id0]*a[id1+3]-a[id0+3]*a[id1] ; //eigvec3 = eigvec1 x eigvec2 
          }
        }//n_eig_zero!=0

      }
      else{
        //imaginary roots found in eigenvalue computation of Hessian of porosity
        branch_entered=4.3;
        auINT8 found_eig=0;
        ccDOTIMES(j,3){
          std::vector<std::vector<sdFLOAT>> coef {{b[0]-ll,b[1],b[2]},{b[3],b[4]-ll,b[5]},{b[6],b[7],b[8]-ll}};
          std::vector<std::vector<sdFLOAT>>::iterator it=coef.begin();
          auINT8 k = 2-j; //2,1,0
          coef.erase(it+k); //remove a row and create the 2x3 subsystem

          sdFLOAT denom = coef[0][1]*coef[1][0] - coef[0][0]*coef[1][1];
          if(fabs(denom)<FLT_EPSILON) continue; //rank 1 matrix, no unique v1 and v2

          sdFLOAT invtDenom = 1.0f/(coef[0][1]*coef[1][0] - coef[0][0]*coef[1][1]);
          //printf("%e,%e,%e,%e\n",ll,invtDenom,-coef[0][2]*coef[1][1], coef[0][1]*coef[1][2]);
          
          v1=-((-coef[0][2]*coef[1][1] + coef[0][1]*coef[1][2]))*invtDenom;
          v2=-((coef[0][2]*coef[1][0] - coef[0][0]*coef[1][2]))*invtDenom;
          a[0] = v1; a[3] = v2; a[6] = 1.0f;
          found_eig=1;
          break;
        }
        if(found_eig){
          a[1] = -v2; a[4] = v1; a[7] = 0.0f; //eigvec2 = [-eigvec1(2),eigvec1(1),0]
          a[2] =a[3]*a[7]-a[6]*a[4] ; a[5] = a[6]*a[1]-a[0]*a[7]; a[8] = a[0]*a[4]-a[3]*a[1] ; //eigvec3 = eigvec1 x eigvec2 
        }
        else{//treat as all 3 eigvals are 0
          a[0]=1.0f; a[4]=1.0f;a[8]=1.0f;
          k0[0] = k0[2]; k0[1] = k0[2];
        }
      }

    }

    //normalize against magnitude
    sdFLOAT magVec;
    ccDOTIMES(i,3){
      magVec = sqrt(a[i]*a[i]+a[i+3]*a[i+3]+a[i+6]*a[i+6]);
      sdFLOAT invt = magVec<FLT_EPSILON ? 0.0f: 1.0f/magVec;
      a[i] *= invt; a[i+3] *= invt; a[i+6] *= invt;
    }

    if(g_mp_pm_tensor_k0_eig_method==1){//use gradPor as v1, 
        sdFLOAT norm = sqrt(gradPor[0](voxel)*gradPor[0](voxel)+gradPor[1](voxel)*gradPor[1](voxel)+gradPor[2](voxel)*gradPor[2](voxel));
        sdFLOAT invt_norm = norm<FLT_EPSILON ? 0.0f: 1.0f/norm;
        a[0] = gradPor[0](voxel)*invt_norm; a[3] = gradPor[1](voxel)*invt_norm; a[6] = gradPor[2](voxel)*invt_norm;
        a[1] = -1.0f*gradPor[1](voxel)*invt_norm; a[4] = gradPor[0](voxel)*invt_norm; a[7] = 0.0f;
        a[2] =a[3]*a[7]-a[6]*a[4] ; a[5] = a[6]*a[1]-a[0]*a[7]; a[8] = a[0]*a[4]-a[3]*a[1]; 
    }

    if(two_eigenvalues_close==1){ // only if two eigenvalues are close
      sdFLOAT denom = sqrt(gradPor[0](voxel)*gradPor[0](voxel)+gradPor[1](voxel)*gradPor[1](voxel)+gradPor[2](voxel)*gradPor[2](voxel));
      sdFLOAT invt_denom = denom<FLT_EPSILON ? 0.0f: 1.0f/denom;
      sdFLOAT norm2=fabs((gradPor[0](voxel)*a[1]+gradPor[1](voxel)*a[4]+gradPor[2](voxel)*a[7])*invt_denom);
      sdFLOAT norm3=fabs((gradPor[0](voxel)*a[2]+gradPor[1](voxel)*a[5]+gradPor[2](voxel)*a[8])*invt_denom);
      sdFLOAT criteria_value=0.5;
      if(norm3>criteria_value&&norm2<criteria_value){ // exchange condition
      // if(norm3>0.5F&&norm2<0.1F) // exchange condition
        sdFLOAT a_st[3]={a[1],a[4],a[7]};
        a[1]=a[2];
        a[4]=a[5];
        a[7]=a[8];
        a[2]=a_st[0];
        a[5]=a_st[1];
        a[8]=a_st[2];
      }      
    }


    ublk->pore_lb_data()->k_ij[0](voxel) = a[0]*a[0]*k0[0] + a[1]*a[1]*k0[1] + a[2]*a[2]*k0[2];
    ublk->pore_lb_data()->k_ij[1](voxel) = a[3]*a[3]*k0[0] + a[4]*a[4]*k0[1] + a[5]*a[5]*k0[2];
    ublk->pore_lb_data()->k_ij[2](voxel) = a[6]*a[6]*k0[0] + a[7]*a[7]*k0[1] + a[8]*a[8]*k0[2];
    ublk->pore_lb_data()->k_ij[3](voxel) = a[0]*a[3]*k0[0] + a[1]*a[4]*k0[1] + a[2]*a[5]*k0[2];
    ublk->pore_lb_data()->k_ij[4](voxel) = a[0]*a[6]*k0[0] + a[1]*a[7]*k0[1] + a[2]*a[8]*k0[2];
    ublk->pore_lb_data()->k_ij[5](voxel) = a[3]*a[6]*k0[0] + a[4]*a[7]*k0[1] + a[5]*a[8]*k0[2];

    if(show_debug){
      //test if there is NaN in resis tensor
      printf("grad por: %e,%e,%e, hessian: %e,%e,%e,%e,%e,%e,\n", gradPor[0](voxel),gradPor[1](voxel),\
        gradPor[2](voxel),hessian[0](voxel),hessian[1](voxel), \
        hessian[2](voxel),hessian[3](voxel),hessian[4](voxel),hessian[5](voxel));
      printf("eigenvectors:\n");
      ccDOTIMES(i,9){
          printf("a[%d]=%e,",i,a[i]);
          assert(a[i]==a[i]);
      }


      printf("branch entered is:%e\n",branch_entered);
      printf("kij:%e,%e,%e,%e,%e,%e\n",ublk->pore_lb_data()->k_ij[0](voxel),\
            ublk->pore_lb_data()->k_ij[1](voxel),ublk->pore_lb_data()->k_ij[2](voxel),\
            ublk->pore_lb_data()->k_ij[3](voxel),ublk->pore_lb_data()->k_ij[4](voxel),\
            ublk->pore_lb_data()->k_ij[5](voxel));

      printf("consistancies of trace, det and orthoganality :\n");

      sdFLOAT k00=ublk->pore_lb_data()->k_ij[0](voxel);
      sdFLOAT k1=ublk->pore_lb_data()->k_ij[1](voxel);
      sdFLOAT k2=ublk->pore_lb_data()->k_ij[2](voxel);
      sdFLOAT k3=ublk->pore_lb_data()->k_ij[3](voxel);
      sdFLOAT k4=ublk->pore_lb_data()->k_ij[4](voxel);
      sdFLOAT k5=ublk->pore_lb_data()->k_ij[5](voxel);
      sdFLOAT k_det = k00*k1*k2 - k2*k3*k3 - k1*k4*k4 + 2.0f*k3*k4*k5 - k00*k5*k5;
      sdFLOAT kt_det = k0[0]*k0[1]*k0[2];
      sdFLOAT k_trc = k00+k1+k2;
      sdFLOAT kt_trc = k0[0]+k0[1]+k0[2];
      if(fabs(k_det-kt_det)/kt_det>1e-7&&kt_det>1e-7)
          printf("determinant k and ktilda: %e,%e, diff: %e\n",k_det,kt_det,(k_det-kt_det)/kt_det);
      if(fabs(k_trc-kt_trc)/k_trc>1e-7)
          printf("trace k and ktilda: %e,%e, diff: %e\n",k_trc,kt_trc,(k_trc-kt_trc)/kt_trc);

      sdFLOAT nvec1 = sqrt(a[0]*a[0]+a[3]*a[3]+a[6]*a[6]);
      sdFLOAT nvec2 = sqrt(a[1]*a[1]+a[4]*a[4]+a[7]*a[7]);
      sdFLOAT nvec3 = sqrt(a[2]*a[2]+a[5]*a[5]+a[8]*a[8]);
      if(fabs(nvec1-1.0F)>1e-5)printf("magnitude of eigvec nvec1: %e\n",nvec1);
      if(fabs(nvec2-1.0F)>1e-5)printf("magnitude of eigvec nvec2: %e\n",nvec2);
      if(fabs(nvec3-1.0F)>1e-5)printf("magnitude of eigvec nvec3: %e\n",nvec3);

      sdFLOAT vv1 = a[0]*a[1]+a[3]*a[4]+a[6]*a[7];
      sdFLOAT vv2 = a[0]*a[2]+a[3]*a[5]+a[6]*a[8];
      sdFLOAT vv3 = a[1]*a[2]+a[4]*a[5]+a[7]*a[8];
      if((fabs(vv1)>1e-5f)||(fabs(vv2)>1e-5f)||(fabs(vv3)>1e-5f))
        printf("ortho test: v1xv2 %e, v1xv3 %e, v2xv3 %e\n",vv1,vv2,vv3);
      }
    }

  }//mp_pm_tensor_k0
  dynamics_voxel_mask &= ~dynublk->basic_fluid_voxel_mask;
  dynamics_data = (UBLK_DYNAMICS_DATA) dynamics_data->next() ;
  }//basic_fluid_voxel_mask
        
  while (dynamics_voxel_mask.any()) { //@@MPF-PowerCase does not support special fluid regions yet
      auINT32 fluid_type = dynamics_data->fluid_type();
      SPECIAL_UBLK_DYNAMICS_DATA special_dyn_data = (SPECIAL_UBLK_DYNAMICS_DATA) dynamics_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = special_dyn_data->voxel_mask();
      msg_internal_error("Invalid special fluid type (%d) for 5G when calculating gradient of porosity.", fluid_type);
  }

}
#endif

struct Shob_len
{
  Shob_len(SHOB_ID id_, bool verbose_):id(id_), verbose(verbose_){}

  template <typename T>
  void add() {
    constexpr size_t size = sizeof(T);
    add<T>(size);
  }
  template <typename T>
  void add(const size_t size) {
    if (verbose)
      printf("Ublk_ID=%d %s adding %zu bytes\n",id, typeid(T).name(), size);
    len += size;
  }

  const SHOB_ID id;
  const bool verbose = false;
  uINT64 len=0;

};

template<>
uINT64 sUBLK::ckpt_len(bool verbose) {


  Shob_len ublk_len(id(), verbose);
  if (has_real_ckpt_data()) {
    ublk_len.add<BOOLEAN>();
    

    // is_bsurfel_interacting
    ublk_len.add<BOOLEAN>();
    if (is_bsurfel_interacting()) {
      ublk_len.add<sBSURFEL_BODY_FORCE>();
    }

#if BUILD_5G_LATTICE
    ublk_len.add<sdFLOAT>( struct_field_size(UBLK, porosity));
    
    if (sim.is_large_pore && sim.is_lb_model)
      ublk_len.add<sUBLK_STATES_DATA>( (1 + has_two_copies())*sizeof(sUBLK_STATES_DATA));
    else
#endif
    {
      // Only store one copy of the states
      ublk_len.add<sUBLK_STATES_DATA>();
    }

    if (sim.is_lb_model)
      ublk_len.add<sUBLK_LB_DATA>( lb_data()->ckpt_len());
    if (sim.is_turb_model)
      ublk_len.add<sUBLK_TURB_DATA>( turb_data()->ckpt_len());
    
    if (sim.is_conduction_model && is_conduction_solid()) {
      ublk_len.add<sUBLK_CONDUCTION_DATA>(conduction_data()->ckpt_len());
    } else if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
      ublk_len.add<sUBLK_T_DATA>( t_data()->ckpt_len(has_two_copies()));
    }
    
    if (sim.is_lb_model && (bool) g_is_multi_component) {
      ublk_len.add<sUBLK_MC_DATA>( mc_data()->ckpt_len());
      if (sim.is_large_pore)
	ublk_len.add<sUBLK::sUBLK_MC_DATA::sUBLK_MC_STATES_DATA>(  (1 + has_two_copies())*sizeof(sUBLK::sUBLK_MC_DATA::sUBLK_MC_STATES_DATA));
      else
	ublk_len.add<sUBLK::sUBLK_MC_DATA::sUBLK_MC_STATES_DATA>();
    }
    if (sim.is_particle_model)
      ublk_len.add<sUBLK_PARTICLE_DATA>( p_data()->ckpt_len());
    if (sim.is_scalar_model) {
      ublk_len.add<sUBLK_UDS_DATA>( sim.n_user_defined_scalars * uds_data()->ckpt_len(has_two_copies()));
#if BUILD_D19_LATTICE
      if (sim.is_pf_model)
        ublk_len.add<sUBLK_PF_DATA>(pf_data()->ckpt_len());
#endif
    }

    // More data for near surface ublks
    if (is_near_surface()) {
      ublk_len.add<sNEARBLK_GEOM_DATA>( surf_geom_data()->ckpt_len());
      if (sim.is_lb_model)
      {
        ublk_len.add<sNEARBLK_LB_DATA>( surf_lb_data()->ckpt_len());
      }
      if (sim.is_turb_model)
      {  
        ublk_len.add<sNEARBLK_TURB_DATA>( surf_turb_data()->ckpt_len());
      }
      
      if (sim.is_conduction_model && is_conduction_solid())
        ublk_len.add<sNEARBLK_CONDUCTION_DATA>(surf_conduction_data()->ckpt_len());
        //TODO, CHECK: checkpointing length for conduction data block
      else if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
        ublk_len.add<sNEARBLK_T_DATA>( surf_t_data()->ckpt_len());

      if (sim.is_lb_model && (bool) g_is_multi_component)
        ublk_len.add<sNEARBLK_MC_DATA>( surf_mc_data()->ckpt_len());
      if (sim.is_scalar_model) {
	      ublk_len.add<sNEARBLK_UDS_DATA>( sim.n_user_defined_scalars * surf_uds_data()->ckpt_len());
#if BUILD_D19_LATTICE
	      if (sim.is_pf_model)
          ublk_len.add<sNEARBLK_PF_DATA>(surf_pf_data()->ckpt_len());
#endif
      }      
    }

    if(sim.has_avg_mme_window) {
      ublk_len.add<sUBLK_MME_TURB_DATA>( avg_mme_data()->ckpt_len()); // value is different for DNS and TURB
      if (sim.is_conduction_model && is_conduction_solid()) {
        ublk_len.add<sUBLK_MME_T_DATA>( avg_mme_conduction_data()->ckpt_len() );
      } else if (sim.is_heat_transfer) {
        ublk_len.add<sUBLK_MME_T_DATA>( avg_mme_t_data()->ckpt_len());
      }
      if (sim.is_scalar_model) {
	ublk_len.add<sUBLK_MME_UDS_DATA>( avg_mme_uds_data()->ckpt_len() * sim.n_user_defined_scalars);
      }
      if (is_near_surface())
        ublk_len.add<sNEARBLK_MME_DATA>( surf_avg_mme_data()->ckpt_len());
    }
#if BUILD_D19_LATTICE
    if (sim.local_vel_freeze)
      ublk_len.add<BOOLEAN>();

    if (sim.store_frozen_vars && is_near_surface())
      ublk_len.add<sNEARBLK_FROZEN_DATA>( surf_frozen_data()->ckpt_len());
#endif

#if BUILD_5G_LATTICE
    if (sim.is_large_pore &&  sim.is_lb_model) {
      ublk_len.add<sUBLK_PORE_LB_DATA>( pore_lb_data()->ckpt_len());
      if (g_is_multi_component)
	ublk_len.add<sUBLK_PORE_MC_DATA>( pore_mc_data()->ckpt_len());
      if (sim.is_scalar_model)
	ublk_len.add<sUBLK_PORE_UDS_DATA>( sim.n_user_defined_scalars * pore_uds_data()->ckpt_len());
    }
    if(g_mp_meas_seed_pres)
      ublk_len.add<sUBLK_CBF_LB_DATA>(cbf_lb_data()->ckpt_len());
#endif
#if BUILD_D19_LATTICE
    if(g_meas_seed_pres_d19)
      ublk_len.add<sUBLK_CBF_LB_DATA>(cbf_lb_data()->ckpt_len());
#endif
  }

  return ublk_len.len;
}

template<>
VOID sUBLK::write_ckpt(uINT64 len) {
#ifdef DEBUG_NEXTGEN
  if (id() == 3423) {
    print_voxel_states("WCKPT",id(),4,0,0);
    print_voxel_states("WCKPT",id(),4,0,-1);
  }
#endif
  BOOLEAN has_two_copies_of_states = has_two_copies();
  write_ckpt_lgi(has_two_copies_of_states);

  // A ublk can become bsurfel interacting through the course of a simulation,
  // so we have to checkpoint this value.
  BOOLEAN is_bsurfel_interacting = this->is_bsurfel_interacting();
  write_ckpt_lgi(is_bsurfel_interacting);

  if (is_bsurfel_interacting) {
    write_ckpt_lgi(m_ib_bf, sizeof(sBSURFEL_BODY_FORCE) );
  }

  asINT32 prev_lb_index = -1;

#if BUILD_5G_LATTICE
  write_ckpt_lgi(porosity);

  if (sim.is_large_pore && sim.is_lb_model)
    write_ckpt_lgi(lb_states(0), (1 + has_two_copies())*sizeof(sUBLK_STATES_DATA));
  else
#endif
  {
    // g_timescale.m_time is already the next timestep at this point, so write prev states in full ckpt.
    //asINT32 prev_lb_index = -1;
    SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale());
    if (has_two_copies()) {
      prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    } else {
      prev_lb_index = ONLY_ONE_COPY;
    }
    write_ckpt_lgi(lb_states(prev_lb_index), sizeof(sUBLK_STATES_DATA));
  }

  if (sim.is_lb_model)
    lb_data()->write_ckpt();
  if (sim.is_turb_model)
    turb_data()->write_ckpt();
  
  if (sim.is_conduction_model && is_conduction_solid())
    conduction_data()->write_ckpt();
  else if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
    t_data()->write_ckpt(has_two_copies());
  
  if (sim.is_lb_model && (bool) g_is_multi_component) {
    mc_data()->write_ckpt();
    if (sim.is_large_pore)
      write_ckpt_lgi(mc_data()->mc_states_data(0), (1 + has_two_copies())*sizeof(sUBLK_MC_STATES_DATA));
    else
      write_ckpt_lgi(mc_data()->mc_states_data(prev_lb_index), sizeof(sUBLK_MC_STATES_DATA));
  }
  if (sim.is_particle_model)
    p_data()->write_ckpt();
  if (sim.is_scalar_model) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
      uds_data(nth_uds)->write_ckpt(has_two_copies());
#if BUILD_D19_LATTICE
    if (sim.is_pf_model)
      pf_data()->write_ckpt();
#endif
  }

  // More data for near surface ublks
  if (is_near_surface()) {
    surf_geom_data()->write_ckpt();
    if (sim.is_lb_model)
      surf_lb_data()->write_ckpt();
    if (sim.is_turb_model)
      surf_turb_data()->write_ckpt();
    
    if (sim.is_conduction_model && is_conduction_solid())
      surf_conduction_data()->write_ckpt();
    //TODO, CHECK: checkpointing for conduction data block
    else if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
      surf_t_data()->write_ckpt();
    
    if (sim.is_lb_model && (bool) g_is_multi_component)
      surf_mc_data()->write_ckpt();
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
	surf_uds_data()->write_ckpt();
#if BUILD_D19_LATTICE
      if (sim.is_pf_model)
        surf_pf_data()->write_ckpt();
#endif
    }
  }

  if(sim.has_avg_mme_window) {
    this->avg_mme_data()->write_ckpt();
    
    if (sim.is_conduction_model && is_conduction_solid())
      this->avg_mme_conduction_data()->write_ckpt();
    else if (sim.is_heat_transfer) {
      this->avg_mme_t_data()->write_ckpt();
    }
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
	this->avg_mme_uds_data(nth_uds)->write_ckpt();
    }
    if (is_near_surface())
      this->surf_avg_mme_data()->write_ckpt();
  }
#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen = this->is_frozen();
    write_ckpt_lgi(is_frozen);
  }

  if (sim.store_frozen_vars && is_near_surface()) {
    this->surf_frozen_data()->write_ckpt();
  }
#endif

#if BUILD_5G_LATTICE  
  if (sim.is_large_pore && sim.is_lb_model) {
    pore_lb_data()->write_ckpt();
    if (g_is_multi_component)
      pore_mc_data()->write_ckpt();
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	pore_uds_data(nth_uds)->write_ckpt();
    }
  }
  if(g_mp_meas_seed_pres)
    cbf_lb_data()->write_ckpt();
#endif
#if BUILD_D19_LATTICE
  if(g_meas_seed_pres_d19)
    cbf_lb_data()->write_ckpt();
#endif
}

template<>
size_t sUBLK::write_ckpt(sCKPT_BUFFER& pio_ckpt_buff)
{
  
  size_t initial_offset = pio_ckpt_buff.m_offset;//deb purposes
  // pio_ckpt_buff.set_verbose(true);
  BOOLEAN has_two_copies_of_states = has_two_copies();
  pio_ckpt_buff.write(&has_two_copies_of_states );

  // A ublk can become bsurfel interacting through the course of a simulation,
  // so we have to checkpoint this value.
  BOOLEAN is_bsurfel_interacting = this->is_bsurfel_interacting();
  pio_ckpt_buff.write(&is_bsurfel_interacting);

  if (is_bsurfel_interacting) {
    pio_ckpt_buff.write( m_ib_bf );
  }

  asINT32 prev_lb_index = -1;

#if BUILD_5G_LATTICE
  pio_ckpt_buff.write( &porosity );

  if (sim.is_large_pore && sim.is_lb_model)
    pio_ckpt_buff.write( lb_states(0), (1 + has_two_copies())*sizeof(sUBLK_STATES_DATA));
  else
#endif
  {
    // g_timescale.m_time is already the next timestep at this point, so write prev states in full ckpt.
    //asINT32 prev_lb_index = -1;
    SOLVER_INDEX_MASK prior_solver_index_mask = compute_solver_index_mask(scale());
    if (has_two_copies()) {
      prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
    } else {
      prev_lb_index = ONLY_ONE_COPY;
    }
    pio_ckpt_buff.write( lb_states(prev_lb_index) );
  }

  if (sim.is_lb_model)
    pio_ckpt_buff.write( lb_data() );
  if (sim.is_turb_model)
    pio_ckpt_buff.write( turb_data() );
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
    pio_ckpt_buff.write( t_data(), sUBLK_T_DATA::SIZE(has_two_copies()));
  if (sim.is_lb_model && (bool) g_is_multi_component) {
    pio_ckpt_buff.write( mc_data() );
    if (sim.is_large_pore)
      pio_ckpt_buff.write( mc_data()->mc_states_data(0), (1 + has_two_copies())*sizeof(sUBLK_MC_STATES_DATA));
    else
      pio_ckpt_buff.write( mc_data()->mc_states_data(prev_lb_index));
  }
  if (sim.is_particle_model)
    p_data()->write_ckpt(pio_ckpt_buff);
  if (sim.is_scalar_model) {
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
      pio_ckpt_buff.write( uds_data(nth_uds), uds_data()->ckpt_len(has_two_copies()));
#if BUILD_D19_LATTICE
    if (sim.is_pf_model)
      pio_ckpt_buff.write(pf_data());
#endif
  }

  // More data for near surface ublks
  if (is_near_surface()) {
    surf_geom_data()->write_ckpt(pio_ckpt_buff);
    if (sim.is_lb_model)
      pio_ckpt_buff.write( surf_lb_data() );
    if (sim.is_turb_model)
      surf_turb_data()->write_ckpt(pio_ckpt_buff);
    // if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
    //   pio_ckpt_buff.write( surf_t_data() ); WRITES NOTHING
    if (sim.is_lb_model && (bool) g_is_multi_component)
      pio_ckpt_buff.write( surf_mc_data() );
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
        surf_uds_data(nth_uds)->write_ckpt(pio_ckpt_buff);
#if BUILD_D19_LATTICE
      if (sim.is_pf_model)
        pio_ckpt_buff.write(surf_pf_data());
#endif
    }
  }

  if(sim.has_avg_mme_window) {
    pio_ckpt_buff.write( this->avg_mme_data() );
    if (sim.is_heat_transfer) {
      pio_ckpt_buff.write( this->avg_mme_t_data() );
    }
    if (sim.is_scalar_model) {
      ccDOTIMES(nth_uds,sim.n_user_defined_scalars)
	pio_ckpt_buff.write( this->avg_mme_uds_data(nth_uds) );
    }
    if (is_near_surface())
     pio_ckpt_buff.write( this->surf_avg_mme_data() );
  }
#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen = this->is_frozen();
    pio_ckpt_buff.write( &is_frozen );
  }

  if (sim.store_frozen_vars && is_near_surface()) {
    pio_ckpt_buff.write( this->surf_frozen_data() );
  }
#endif

#if BUILD_5G_LATTICE  
  if (sim.is_large_pore && sim.is_lb_model) {
    pio_ckpt_buff.write( pore_lb_data() );
    if (g_is_multi_component)
      pio_ckpt_buff.write( pore_mc_data() );
  }
  if(g_mp_meas_seed_pres)
    pio_ckpt_buff.write( cbf_lb_data());
#endif
#if BUILD_D19_LATTICE
  if(g_meas_seed_pres_d19)
    pio_ckpt_buff.write( cbf_lb_data());
#endif

  size_t writtenSz = pio_ckpt_buff.m_offset - initial_offset;

  dassert(writtenSz == this->ckpt_len() && " ublock written size does not match ckpt_len()\n");
  // pio_ckpt_buff.set_verbose(false);
  return writtenSz;
}

template<>
VOID sUBLK::read_ckpt() {
 
  // It is possible that the ublk has two copies of states but there is only one in the full ckpt file because
  // some ublks have two copies reduced to one later. In that case, just read one copy of state.
  BOOLEAN ckpt_has_two_copies;
  read_lgi(ckpt_has_two_copies);

  // Explode and coalesce is not done for mirror vr ublks
  if (!this->is_mirror() && this->is_vr_fine()) {
    if (sim_is_timestep_odd(scale(), g_timescale.m_timestep))
      set_exploded();
    else
      unset_exploded();
  }

  // Bit is_advect is toggled on even and odd timesteps. Need to set it properly since it is not ckpted.
  // It is only used by interior farblk2 ublks and their neighbors.
  if (!is_mirror() && (is_vr_fine() || !is_solid())) {
    if (sim_is_timestep_odd(scale(), g_timescale.m_timestep)) {
      set_ublk_as_advected();
    } else {
      unset_ublk_as_advected();
    }
  }

  BOOLEAN is_bsurfel_interacting;
  read_lgi(is_bsurfel_interacting);
  if (is_bsurfel_interacting) {
    this->set_bsurfel_interacting();
    read_lgi(m_ib_bf,sizeof(sBSURFEL_BODY_FORCE));
  }

#if BUILD_5G_LATTICE
  read_lgi(porosity);
  
  if(sim.is_large_pore && sim.is_lb_model)
    read_lgi(lb_states(0), (1 + has_two_copies())*sizeof(sUBLK_STATES_DATA));
  else
#endif
  {
    if (this->has_two_copies()) {
      SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
      asINT32 prev_lb_index = ckpt_has_two_copies ? lb_index_from_mask(prior_solver_index_mask) : ONLY_ONE_COPY;
      read_lgi(lb_states(prev_lb_index), sizeof(sUBLK_STATES_DATA));
      // Copy to the other set as well since it is possible that the ublk is reduced to one copy later
      // and the other set of states is used.
      memcpy(lb_states(prev_lb_index ^ 1), lb_states(prev_lb_index), sizeof(sUBLK_STATES_DATA));
    } else {
      read_lgi(lb_states(ONLY_ONE_COPY), sizeof(sUBLK_STATES_DATA));
    }
  }

  if (sim.is_lb_model)
    lb_data_no_cassert()->read_ckpt();
  if (sim.is_turb_model)
    turb_data_no_cassert()->read_ckpt();

  BOOLEAN is_T_S_lb_solver_on = sim.is_T_S_solver_type_lb();
  BOOLEAN is_UDS_lb_solver_on = (sim.uds_solver_type == LB_UDS);
  
  if(sim.is_conduction_model && is_conduction_solid()) {
    conduction_data()->read_ckpt();
  }
  else if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
    UBLK_T_DATA ckpt_t_data = sSHOB_ALLOCATOR<sUBLK_T_DATA>::malloc(sUBLK_T_DATA::SIZE(ckpt_has_two_copies));
    size_t ckpt_t_data_size = sUBLK_T_DATA::SIZE(ckpt_has_two_copies);
    size_t t_data_size = sUBLK_T_DATA::SIZE(has_two_copies());
    ckpt_t_data->read_ckpt(ckpt_has_two_copies);
    if (ckpt_has_two_copies) {
      if (has_two_copies()) {
        memcpy(t_data(), ckpt_t_data, t_data_size);
      } else {  // ublk only has one copy, should find the correct copy of states in the ckpt file for LB solver
        if (is_T_S_lb_solver_on) {
          SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
          asINT32 prev_t_index = t_index_from_mask(prior_solver_index_mask); 
          // copy the ublk_t_data
          memcpy(t_data(), ckpt_t_data, sizeof(sUBLK_T_DATA));
          // copy the lb states 
          memcpy(t_data()->lb_t_data(0), ckpt_t_data->lb_t_data(prev_t_index), sizeof(uLB_PDE_DATA));
        } else {
          memcpy(t_data(), ckpt_t_data, t_data_size);
        }
      }
    } else { // ckpt ublk only has one copy of lb states
      if (has_two_copies()) {
        memcpy(t_data(), ckpt_t_data, ckpt_t_data_size);
        if (is_T_S_lb_solver_on) {
          memcpy(t_data()->lb_t_data(1), t_data()->lb_t_data(0), sizeof(uLB_PDE_DATA));
        }
      } else {
        memcpy(t_data(), ckpt_t_data, ckpt_t_data_size);
      }
    }
  }
  if (sim.is_lb_model && (bool) g_is_multi_component) {
    mc_data()->read_ckpt();
    if (sim.is_large_pore)
      read_lgi(mc_data()->mc_states_data(0), (1 + has_two_copies())*sizeof(sUBLK_MC_STATES_DATA));
    else {
      if (this->has_two_copies()) {
	SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
	asINT32 prev_lb_index = ckpt_has_two_copies ? lb_index_from_mask(prior_solver_index_mask) : ONLY_ONE_COPY;
	read_lgi(mc_data()->mc_states_data(prev_lb_index), sizeof(sUBLK_MC_STATES_DATA));
	// Copy to the other set as well since it is possible that the ublk is reduced to one copy later
	// and the other set of states is used.
	memcpy(mc_data()->mc_states_data(prev_lb_index ^ 1), mc_data()->mc_states_data(prev_lb_index), sizeof(sUBLK_MC_STATES_DATA));
      } else {
	read_lgi(mc_data()->mc_states_data(ONLY_ONE_COPY), sizeof(sUBLK_MC_STATES_DATA));
      }
    }
  }

  if (sim.is_particle_model)
    p_data()->read_ckpt();

  if (sim.is_scalar_model){
    UBLK_UDS_DATA first_ckpt_uds_data = sSHOB_ALLOCATOR<sUBLK_UDS_DATA>::malloc(sim.n_user_defined_scalars * sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies));
    size_t ckpt_uds_data_size = sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies);
    size_t uds_data_size = sUBLK_UDS_DATA::SIZE(has_two_copies());
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
      UBLK_UDS_DATA ckpt_uds_data = first_ckpt_uds_data + nth_uds; 
      //uds_data(nth_uds)->read_ckpt();
      //size_t ckpt_uds_data_size = sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies);
      //size_t uds_data_size = sUBLK_UDS_DATA::SIZE(has_two_copies());
      ckpt_uds_data->read_ckpt(ckpt_has_two_copies);
      if (ckpt_has_two_copies) {
	if (has_two_copies()) {
	  memcpy(uds_data(nth_uds), ckpt_uds_data, uds_data_size);
	} else {  // ublk only has one copy, should find the correct copy of states in the ckpt file for LB solver
	  if (is_UDS_lb_solver_on) {
	    SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
	    asINT32 prev_uds_index = uds_index_from_mask(prior_solver_index_mask); 
	    // copy the ublk_uds_data
	    memcpy(uds_data(nth_uds), ckpt_uds_data, sizeof(sUBLK_UDS_DATA));
	    // copy the lb states 
	    memcpy(uds_data(nth_uds)->lb_uds_data(0), ckpt_uds_data->lb_uds_data(prev_uds_index), sizeof(sUBLK_LB_SOLVER_UDS_DATA));
	  } else {
	    memcpy(uds_data(nth_uds), ckpt_uds_data, uds_data_size);
	  }
	}
      } else { // ckpt ublk only has one copy of lb states
	if (has_two_copies()) {
	  memcpy(uds_data(nth_uds), ckpt_uds_data, ckpt_uds_data_size);
	  if (is_UDS_lb_solver_on) {
	    memcpy(uds_data(nth_uds)->lb_uds_data(1), uds_data(nth_uds)->lb_uds_data(0), sizeof(sUBLK_LB_SOLVER_UDS_DATA));
	  }
	} else {
	  memcpy(uds_data(nth_uds), ckpt_uds_data, ckpt_uds_data_size);
	}
      }
    }
#if BUILD_D19_LATTICE
    if(sim.is_pf_model)
      pf_data()->read_ckpt();
#endif
  }
  //TODO, CHECK: checkpointing for conduction data block

  // More data for near surface ublks
  if (is_near_surface()) {
    surf_geom_data()->read_ckpt();
    if (sim.is_lb_model)
      surf_lb_data()->read_ckpt();
    if (sim.is_turb_model)
      surf_turb_data()->read_ckpt();
    
    if(sim.is_conduction_model && is_conduction_solid())
      surf_conduction_data()->read_ckpt();
    //TODO, CHECK: checkpointing for conduction data block
    else if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
      surf_t_data()->read_ckpt();
    
    if (sim.is_lb_model && (bool) g_is_multi_component)
      surf_mc_data()->read_ckpt();
    if (sim.is_scalar_model)  {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      surf_uds_data(nth_uds)->read_ckpt();
#if BUILD_D19_LATTICE
      if(sim.is_pf_model)
        surf_pf_data()->read_ckpt();
#endif
    }
  }
  if (g_full_ckpt_with_avg_mme) {
    if(sim.is_turb_model) {
      static sUBLK_MME_TURB_DATA spare_data;
      if(sim.has_avg_mme_window)
        avg_mme_data()->read_ckpt();
      else
        spare_data.read_ckpt();
    } else {
      static sUBLK_MME_DNS_DATA spare_data;
      if(sim.has_avg_mme_window)
        avg_mme_data()->read_ckpt();
      else
        spare_data.read_ckpt();
    }
    if(sim.is_conduction_model && is_conduction_solid()) {
      static sUBLK_MME_CONDUCTION_DATA spare_data;
      if(sim.has_avg_mme_window)
        avg_mme_conduction_data()->read_ckpt();
      else
        spare_data.read_ckpt();
    } else if(sim.is_heat_transfer) {
      static sUBLK_MME_T_DATA spare_data;
      if(sim.has_avg_mme_window)
        avg_mme_t_data()->read_ckpt();
      else
        spare_data.read_ckpt();
    }
    if (sim.is_scalar_model)  {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {
	static sUBLK_MME_UDS_DATA spare_data;
	if(sim.has_avg_mme_window)
	  avg_mme_uds_data(nth_uds)->read_ckpt();
	else
	  spare_data.read_ckpt();
      }
    }
	
    if (is_near_surface()) {
      static sNEARBLK_MME_DATA spare_data;
      if (sim.has_avg_mme_window)
        surf_avg_mme_data()->read_ckpt();
      else
        spare_data.read_ckpt();
    }
  }
#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen;
    read_lgi(is_frozen);
    set_frozen(is_frozen);
  }

  if (g_full_ckpt_with_frozen_vars && this->is_near_surface()) {
    if (sim.store_frozen_vars) {
      this->surf_frozen_data()->read_ckpt();
    } 
    else {
      static sNEARBLK_FROZEN_DATA frozen_data;
      frozen_data.read_ckpt();
    }
  }
#endif

#if BUILD_5G_LATTICE  
  if (sim.is_large_pore && sim.is_lb_model) {
    pore_lb_data()->read_ckpt();
    if (g_is_multi_component)
      pore_mc_data()->read_ckpt();
    if (sim.is_scalar_model)  {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	pore_uds_data(nth_uds)->read_ckpt();
    }
  }
  if(g_mp_meas_seed_pres)
    cbf_lb_data()->read_ckpt();
#endif
#if BUILD_D19_LATTICE
  if(g_meas_seed_pres_d19)
    cbf_lb_data()->read_ckpt();
#endif

#ifdef DEBUG_NEXTGEN
  if (id() == 3423) {
    auto old = sim.T_solver_type;
    sim.T_solver_type = LB_TEMPERATURE;
    LOG_MSG("BSURFEL_CKPT",LOG_FUNC);
    print_voxel_states("RCKPT",id(),4,0,-1);
    print_voxel_states("RCKPT",id(),4,0,0);
    sim.T_solver_type = old;
  }
#endif

}

template<>
size_t sUBLK::read_ckpt(u_char* buff) {

 size_t readSz = 0;
 auto readFromBuff = [&buff, &readSz](auto* obj)
 {
  //reads obj from buffer and advances offset
  size_t size = sizeof(*obj);
  std::memcpy(obj, buff+readSz, size);
  readSz+=size;
 };
auto readFromBuffSize = [&buff, &readSz](auto* obj, size_t size)
 {
  //reads obj from buffer and advances offset
  std::memcpy(obj, buff+readSz, size);
  readSz +=size;
 };
  // It is possible that the ublk has two copies of states but there is only one in the full ckpt file because
  // some ublks have two copies reduced to one later. In that case, just read one copy of state.
  BOOLEAN ckpt_has_two_copies;
  readFromBuff(&ckpt_has_two_copies);
  // Explode and coalesce is not done for mirror vr ublks
  if (!this->is_mirror() && this->is_vr_fine()) {
    if (sim_is_timestep_odd(scale(), g_timescale.m_timestep))
      set_exploded();
    else
      unset_exploded();
  }

  // Bit is_advect is toggled on even and odd timesteps. Need to set it properly since it is not ckpted.
  // It is only used by interior farblk2 ublks and their neighbors.
  if (!is_mirror() && (is_vr_fine() || !is_solid())) {
    if (sim_is_timestep_odd(scale(), g_timescale.m_timestep)) {
      set_ublk_as_advected();
    } else {
      unset_ublk_as_advected();
    }
  }

  BOOLEAN is_bsurfel_interacting;
  readFromBuff(&is_bsurfel_interacting);
  if (is_bsurfel_interacting) {
    this->set_bsurfel_interacting();
    readFromBuff(&m_ib_bf);
  }

#if BUILD_5G_LATTICE
  readFromBuff(&porosity);
  if(sim.is_large_pore && sim.is_lb_model)
    readFromBuffSize(lb_states(0), (1 + has_two_copies())*sizeof(sUBLK_STATES_DATA));
  else
#endif
  {
    if (this->has_two_copies()) {
      SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
      asINT32 prev_lb_index = ckpt_has_two_copies ? lb_index_from_mask(prior_solver_index_mask) : ONLY_ONE_COPY;
      readFromBuff(lb_states(prev_lb_index));
      // Copy to the other set as well since it is possible that the ublk is reduced to one copy later
      // and the other set of states is used.
      memcpy(lb_states(prev_lb_index ^ 1), lb_states(prev_lb_index), sizeof(sUBLK_STATES_DATA));
    } else {
      readFromBuff(lb_states(ONLY_ONE_COPY));
    }
  }

  if (sim.is_lb_model)
  {
    readFromBuff(lb_data_no_cassert());
  }
  if (sim.is_turb_model)
  {
    readFromBuff(turb_data_no_cassert());
  }
  BOOLEAN is_T_S_lb_solver_on = sim.is_T_S_solver_type_lb();
  BOOLEAN is_UDS_lb_solver_on = (sim.uds_solver_type == LB_UDS);
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation) {
    UBLK_T_DATA ckpt_t_data = sSHOB_ALLOCATOR<sUBLK_T_DATA>::malloc(sUBLK_T_DATA::SIZE(ckpt_has_two_copies));
    size_t ckpt_t_data_size = sUBLK_T_DATA::SIZE(ckpt_has_two_copies);
    size_t t_data_size = sUBLK_T_DATA::SIZE(has_two_copies());
    readFromBuffSize(ckpt_t_data, t_data_size);
    if (ckpt_has_two_copies) {
      if (has_two_copies()) {
        memcpy(t_data(), ckpt_t_data, t_data_size);
      } else {  // ublk only has one copy, should find the correct copy of states in the ckpt file for LB solver
        if (is_T_S_lb_solver_on) {
          SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
          asINT32 prev_t_index = t_index_from_mask(prior_solver_index_mask); 
          // copy the ublk_t_data
          memcpy(t_data(), ckpt_t_data, sizeof(sUBLK_T_DATA));
          // copy the lb states 
          memcpy(t_data()->lb_t_data(0), ckpt_t_data->lb_t_data(prev_t_index), sizeof(uLB_PDE_DATA));
        } else {
          memcpy(t_data(), ckpt_t_data, t_data_size);
        }
      }
    } else { // ckpt ublk only has one copy of lb states
      if (has_two_copies()) {
        memcpy(t_data(), ckpt_t_data, ckpt_t_data_size);
        if (is_T_S_lb_solver_on) {
          memcpy(t_data()->lb_t_data(1), t_data()->lb_t_data(0), sizeof(uLB_PDE_DATA));
        }
      } else {
        memcpy(t_data(), ckpt_t_data, ckpt_t_data_size);
      }
    }
  }
  if (sim.is_lb_model && (bool) g_is_multi_component) {
    readFromBuff( mc_data() );
    if (sim.is_large_pore)
      readFromBuffSize(mc_data()->mc_states_data(0), (1 + has_two_copies())*sizeof(sUBLK_MC_STATES_DATA));
    else {
      if (this->has_two_copies()) {
        SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
	      asINT32 prev_lb_index = ckpt_has_two_copies ? lb_index_from_mask(prior_solver_index_mask) : ONLY_ONE_COPY;
        readFromBuff(mc_data()->mc_states_data(prev_lb_index));
  // Copy to the other set as well since it is possible that the ublk is reduced to one copy later
	// and the other set of states is used.
	      memcpy(mc_data()->mc_states_data(prev_lb_index ^ 1), mc_data()->mc_states_data(prev_lb_index), sizeof(sUBLK_MC_STATES_DATA));
      } else {
        readFromBuff(mc_data()->mc_states_data(ONLY_ONE_COPY));
      }
    }
  }

  if (sim.is_particle_model)
    readFromBuff(p_data());

  if (sim.is_scalar_model)
  {
    UBLK_UDS_DATA first_ckpt_uds_data = sSHOB_ALLOCATOR<sUBLK_UDS_DATA>::malloc(sim.n_user_defined_scalars * sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies));
    size_t ckpt_uds_data_size = sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies);
    size_t uds_data_size = sUBLK_UDS_DATA::SIZE(has_two_copies());
    ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
    {
      UBLK_UDS_DATA ckpt_uds_data = first_ckpt_uds_data + nth_uds; 
      //uds_data(nth_uds)->read_ckpt();
      //size_t ckpt_uds_data_size = sUBLK_UDS_DATA::SIZE(ckpt_has_two_copies);
      //size_t uds_data_size = sUBLK_UDS_DATA::SIZE(has_two_copies());
      readFromBuffSize(ckpt_uds_data, uds_data_size);
      if (ckpt_has_two_copies) 
      {
	      if (has_two_copies()) 
        {
	        memcpy(uds_data(nth_uds), ckpt_uds_data, uds_data_size);
	      } 
        else 
        {  // ublk only has one copy, should find the correct copy of states in the ckpt file for LB solver
	        if (is_UDS_lb_solver_on) 
          {
	          SOLVER_INDEX_MASK prior_solver_index_mask = compute_seed_solver_index_mask(scale(), g_timescale.m_timestep);
	          asINT32 prev_uds_index = uds_index_from_mask(prior_solver_index_mask); 
	          // copy the ublk_uds_data
	          memcpy(uds_data(nth_uds), ckpt_uds_data, sizeof(sUBLK_UDS_DATA));
	          // copy the lb states 
	          memcpy(uds_data(nth_uds)->lb_uds_data(0), ckpt_uds_data->lb_uds_data(prev_uds_index), sizeof(sUBLK_LB_SOLVER_UDS_DATA));
	        } 
          else
          {
	          memcpy(uds_data(nth_uds), ckpt_uds_data, uds_data_size);
	        }
	      }
      } 
      else 
      { // ckpt ublk only has one copy of lb states
	      if (has_two_copies()) 
        {
	        memcpy(uds_data(nth_uds), ckpt_uds_data, ckpt_uds_data_size);
	        if (is_UDS_lb_solver_on) 
          {
	          memcpy(uds_data(nth_uds)->lb_uds_data(1), uds_data(nth_uds)->lb_uds_data(0), sizeof(sUBLK_LB_SOLVER_UDS_DATA));
	        }
	      } 
        else 
        {
	        memcpy(uds_data(nth_uds), ckpt_uds_data, ckpt_uds_data_size);
	      }
      }
    }
  }


  // More data for near surface ublks
  if (is_near_surface()) {
    surf_geom_data()->read_ckpt(buff, readSz);
    if (sim.is_lb_model)
    {
      readFromBuff(surf_lb_data());
    }
    if (sim.is_turb_model)
    {
      surf_turb_data()->read_ckpt(buff, readSz);
    }
    // NOT CHECKPOINTED
    // if (sim.is_heat_transfer || sim.switch_acous_during_simulation)
    // {
    //   readFromBuff(surf_t_data()->read_ckpt_from_buffer(buff, readSz));
    // }
    if (sim.is_lb_model && (bool) g_is_multi_component)
    {
      readFromBuff(surf_mc_data());
    }
    if (sim.is_scalar_model)  {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	      readFromBuffSize(surf_uds_data(nth_uds),sNEARBLK_UDS_DATA::SIZE(has_two_copies()));
    }
  }

    if(sim.has_avg_mme_window) {
      readFromBuff(avg_mme_data());
    if (sim.is_heat_transfer) {
      avg_mme_t_data()->read_ckpt(buff, readSz);      
    }
    if (sim.is_scalar_model)  {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	avg_mme_uds_data(nth_uds)->read_ckpt(buff, readSz);
    }
    if (is_near_surface())
     readFromBuff(surf_avg_mme_data());
  }

#if BUILD_D19_LATTICE
  if (sim.local_vel_freeze) {
    BOOLEAN is_frozen;
    readFromBuff(&is_frozen);
    set_frozen(is_frozen);
  }

  if (sim.store_frozen_vars && this->is_near_surface()) {
    readFromBuff(this->surf_frozen_data());
  }
#endif

#if BUILD_5G_LATTICE  
  if (sim.is_large_pore && sim.is_lb_model) {
    readFromBuff(pore_lb_data());
    if (g_is_multi_component)
      readFromBuff(pore_mc_data());
  }
#endif

  cassert(readSz == this->ckpt_len() && " ublock read size does not match ckpt_len()\n");
  return readSz;
}

template<>
VOID sUBLK::determine_if_ublk_is_simple() {
  /* This routine will process all the simple voxels which have all the six
   * neighbors, so that the k-epsilon calculations can be speeded up.
   * If a dynblk is mentioned in multiple fluid like voxel dyn groups, say
   * because it has some voxels of the basic fluid type, and some voxels of a
   * special fluid type (e.g_, porous media), then it will be processed by this
   * code multiple times. That is just fine, since we will simply recompute the
   * same all_neighbors_simple_mask.
   */
  asINT32 loc_scale = scale();     /* spacing scale of voxels */
  asINT32 n_dims = sim.num_dims; // Indicates 2D or 3D.
  asINT32 must_have_twice_ndims = 2 * n_dims;
  BOOLEAN report_invalid_neighbors = getenv("EXA_VERBOSE_WARNINGS") != NULL;

  VOXEL_MASK_8 all_neighbors_simple_mask{0};
  BOOLEAN is_ublk_simple = TRUE;

  DO_VOXELS_IN_MASK(voxel, fluid_like_voxel_mask) {

    asINT32 n_simple_neighbors = 0;
    BOOLEAN is_voxel_fine_scale = FALSE;
    BOOLEAN is_voxel_coarse_scale = FALSE;
    BOOLEAN all_pbl_zero = TRUE;
    BOOLEAN all_pbl_2_zero = TRUE;
    BOOLEAN is_any_neighboring_voxel_partial = FALSE;

    if (is_near_surface()) {
      NEAR_UBLK_GEOM_DATA surf_geom = surf_geom_data();
      NEAR_UBLK_LB_DATA surf_lb = surf_lb_data();
      all_pbl_zero =
        ((surf_lb->post_advect_scale_factors[V_P1_0_0][voxel] == 1.0)
         && (surf_lb->post_advect_scale_factors[V_N1_0_0][voxel] == 1.0)
         && (surf_lb->post_advect_scale_factors[V_0_P1_0][voxel] == 1.0)
         && (surf_lb->post_advect_scale_factors[V_0_N1_0][voxel] == 1.0)
         && (surf_lb->post_advect_scale_factors[V_0_0_P1][voxel] == 1.0)
         && (surf_lb->post_advect_scale_factors[V_0_0_N1][voxel] == 1.0));
      
      all_pbl_2_zero = ((surf_geom->pbl_2[V_P1_0_0][voxel] == 0)
                        && (surf_geom->pbl_2[V_N1_0_0][voxel] == 0)
                        && (surf_geom->pbl_2[V_0_P1_0][voxel] == 0)
                        && (surf_geom->pbl_2[V_0_N1_0][voxel] == 0)
                        && (surf_geom->pbl_2[V_0_0_P1][voxel] == 0)
                        && (surf_geom->pbl_2[V_0_0_P1][voxel] == 0));
    }

    for (asINT32 axis = 0; axis < n_dims; axis++) {
      asINT32 neighbor_voxel = -1;
      asINT32 forward_face = -1;
      asINT32 backward_face = -1;
      UBLK forward_ublk = NULL;
      UBLK backward_ublk = NULL;
      BOOLEAN is_forward_neighbor_different_scale = false;
      BOOLEAN is_backward_neighbor_different_scale = false;
      BOOLEAN is_forward_neighbor_finer_scale = false;
      BOOLEAN is_backward_neighbor_finer_scale = false;
      BOOLEAN is_forward_neighbor_2_diff_scale = false;
      BOOLEAN is_backward_neighbor_2_diff_scale = false;

      ublk_neighbors_on_axis(axis, this, voxel, neighbor_voxel, forward_face,
                             backward_face, forward_ublk, backward_ublk,
                             is_forward_neighbor_different_scale,
                             is_backward_neighbor_different_scale,
                             is_forward_neighbor_finer_scale,
                             is_backward_neighbor_finer_scale,
                             is_forward_neighbor_2_diff_scale,
                             is_backward_neighbor_2_diff_scale);

      if (is_forward_neighbor_finer_scale) {
        UBLK fine_ublk = forward_ublk;
        if (fine_ublk && (fine_ublk->all_neighbors_voxel_mask.none())
            && (fine_ublk->fluid_like_voxel_mask.none())) {
          if (report_invalid_neighbors)
            msg_warn(
                     "Dynamics ublk %d/%d has invalid forward neighbor %d/%d (fine ublk)",
                     id(), voxel, fine_ublk->id(), neighbor_voxel);
        }
        if (fine_ublk) {   //voxel is on the coarse side
          is_voxel_coarse_scale = TRUE;
        }
      } else {
        if (forward_ublk != NULL) {
          if (!is_forward_neighbor_different_scale) {
            n_simple_neighbors++;
          } else {
            is_voxel_fine_scale = TRUE;
          }
          if (!forward_ublk->fluid_like_voxel_mask.test(neighbor_voxel) && !forward_ublk->all_neighbors_voxel_mask.test(neighbor_voxel) ) {
            if (report_invalid_neighbors)
              msg_warn("Dynamics ublk %d/%d has invalid forward neighbor %d/%d",
                       id(), voxel, forward_ublk->id(), neighbor_voxel);
          }
          if (!is_forward_neighbor_different_scale
              && forward_ublk->is_near_surface()
              && forward_ublk->surf_geom_data()->pfluids[neighbor_voxel] < 1.0) {
            is_any_neighboring_voxel_partial = TRUE;
          }
        }
      }

      if (is_backward_neighbor_finer_scale) {
        UBLK fine_ublk = backward_ublk;
        if (fine_ublk && fine_ublk->all_neighbors_voxel_mask.none() && fine_ublk->fluid_like_voxel_mask.none()) {
          if (report_invalid_neighbors)
            msg_warn(
                     "Dynamics ublk %d/%d has invalid backward neighbor %d/%d (fine ublk)",
                     id(), voxel, fine_ublk->id(), neighbor_voxel);
        }

        if (fine_ublk) {  //voxel is on the coarse side
          is_voxel_coarse_scale = TRUE;
        }
      } else {
        if (backward_ublk != NULL) {
          if (!is_backward_neighbor_different_scale) {
            n_simple_neighbors++;
          } else {
            is_voxel_fine_scale = TRUE;   //voxel is on the fine side
          }

          if (!backward_ublk->fluid_like_voxel_mask.test(neighbor_voxel) && !backward_ublk->all_neighbors_voxel_mask.test(neighbor_voxel)) {
            if (report_invalid_neighbors)
              msg_warn("Dynamics ublk %d/%d has invalid backward neighbor %d/%d",
                       id(), voxel, backward_ublk->id(), neighbor_voxel);
          }
          if (!is_backward_neighbor_different_scale
              && backward_ublk->is_near_surface()
              && backward_ublk->surf_geom_data()->pfluids[neighbor_voxel] < 1.0) {
            is_any_neighboring_voxel_partial = TRUE;
          }
        }
      }

      if (is_forward_neighbor_2_diff_scale)
        neighbor_2_diff_scale_mask[forward_face].set(voxel);
      if (is_backward_neighbor_2_diff_scale)
        neighbor_2_diff_scale_mask[backward_face].set(voxel);
    } // axis loop

      // Check if you found all the neighbors.
    if (all_pbl_zero && (n_simple_neighbors == must_have_twice_ndims)) {
      all_neighbors_simple_mask.set(voxel);
    }

    if (!all_pbl_2_zero
        || (is_conduction_solid() && is_any_neighboring_voxel_partial)
        || (n_simple_neighbors != must_have_twice_ndims)) {
      is_ublk_simple = FALSE;
    }

    if (is_voxel_fine_scale)
      set_neighbor_as_finer_scale(voxel);
    else if (is_voxel_coarse_scale)
      set_neighbor_as_coarser_scale(voxel);

  } // DO_VOXELS_IN_MASK
  if (is_ublk_simple) {
    const auto& box_access = this->box_access();
    ccDOTIMES(dir, 2*n_dims) {
      asINT32 axis = dir >> 1;
      TAGGED_UBLK neighbor;
      if (dir & 1) {
        neighbor = box_access.backward_neighbor(axis);
      } else {
        neighbor = box_access.forward_neighbor(axis);
      }
      if (neighbor.is_ublk_split()) {
        is_ublk_simple = FALSE;
      } else if (neighbor.is_ublk_scale_interface()) {
        is_ublk_simple = FALSE;
      } else if (neighbor.is_ublk()) {
        if ((neighbor.ublk()->is_vr_fine() == 1)) 
          is_ublk_simple = FALSE;
      } else {
        is_ublk_simple = FALSE;
      }
      if (!is_ublk_simple)
        break;
    }
  }

  all_neighbors_voxel_mask |= all_neighbors_simple_mask;
  if (is_ublk_simple)
    set_ublk_simple();  
}

template<>
VOID sUBLK::post_seed_init() {

  this->determine_if_ublk_is_simple();

  update_closest_surfel_BC_based_on_smart_seed_markers(this);
  
  // For each dynamics data block
  UBLK_DYNAMICS_DATA ublk_dyn_data = dynamics_data();
  VOXEL_MASK_8 dynamics_voxel_mask = fluid_like_voxel_mask;
  if (basic_fluid_voxel_mask.any()) {
    dynamics_voxel_mask &= ~basic_fluid_voxel_mask;
    ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
  }
  while (dynamics_voxel_mask.any()) {
    auINT32 fluid_type = ublk_dyn_data->fluid_type();
    switch (fluid_type) {
    case CURVED_HX_POROUS_FLUID_TYPE :
    {
      CURVED_HX_POROUS_UBLK_DYNAMICS_DATA dyn_data = (CURVED_HX_POROUS_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
      dyn_data->post_seed_init(this);
      if (g_timescale.m_time > 0) {
        POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = dyn_data->physics_descriptor();
        dyn_data->reinit_fluid_physics_descriptor(pd, special_fluid_voxel_mask, this);
      }
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) dyn_data->next();
      break;
    }
    case POROUS_FLUID_TYPE :
    case CURVED_POROUS_FLUID_TYPE :
    {
      POROUS_UBLK_DYNAMICS_DATA dyn_data = (POROUS_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
      break;
    }
    case FAN_FLUID_TYPE:
    case TABLE_FAN_FLUID_TYPE:
    {
      FAN_UBLK_DYNAMICS_DATA dyn_data = (FAN_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 special_fluid_voxel_mask = dyn_data->voxel_mask();
      dynamics_voxel_mask &= ~special_fluid_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
      break;
    }
    case CONDUCTION_SOLID_TYPE:
    {
#ifdef CONDUCTION_ENABLE_DEFENSIVE_CHECKS
      if (basic_fluid_voxel_mask != VOXEL_MASK_8(0) || dynamics_voxel_mask != fluid_like_voxel_mask)
        msg_internal_error("Conduction UBLKs should be exclusive and cannot have voxels of different types");
#endif
      CONDUCTION_UBLK_DYNAMICS_DATA dyn_data = (CONDUCTION_UBLK_DYNAMICS_DATA) ublk_dyn_data;
      VOXEL_MASK_8 conduction_voxel_mask = dyn_data->voxel_mask();
      dynamics_voxel_mask &= ~conduction_voxel_mask;
      ublk_dyn_data = (UBLK_DYNAMICS_DATA) ublk_dyn_data->next();
      break;
    }
    }
  }
}

template<>
VOID sUBLK::set_distance_info(DGF_UBLK_BASE_DESC ublk_desc,
                              BOOLEAN is_real_ublk_desc,
                              VOXEL_MASK_8 voxel_mask) {

  if (!is_near_surface())
    return;

  NEAR_UBLK_GEOM_DATA surf_geom = surf_geom_data();
  if (!is_real_ublk_desc) {
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      surf_geom->closest_surfel_ids[voxel] = STP_INVALID_SURFEL_ID;
    }
  } else {
    DGF_REAL_UBLK_DESC real_ublk_desc = (DGF_REAL_UBLK_DESC) ublk_desc;
    ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
      if (voxel_mask.test(voxel) && (real_ublk_desc->voxel_flags[voxel].voxel_flags & DGF_VOXEL_GENERAL)) {
        DGF_GENERAL_VOXEL_DESC voxel_desc = &real_ublk_desc->general_voxels[voxel];
        if (voxel_desc->v.closest_surfel_index == STP_INVALID_SHOB_ID) {
          surf_geom->closest_surfel_ids[voxel] = STP_INVALID_SURFEL_ID;
          surf_geom->dist_to_surface[voxel] = 0;
        } else {
          surf_geom->closest_surfel_ids[voxel] = voxel_desc->v.closest_surfel_index;
          if (surf_geom->closest_surfel_ids[voxel] == STP_INVALID_SURFEL_ID)
            msg_internal_error("Ublk %d voxel %d has invalid closest surfel ID (%d)",
                               id(), voxel, voxel_desc->v.closest_surfel_index);
        }
        // In DGF, the voxel dist_to_surface is defined in finest units, but in the simulator, we need it
        // defined in local units.
        asINT32 log2_voxel_size = scale_to_log2_voxel_size(scale());
        dFLOAT local_v2s_dist = voxel_desc->v.dist_to_surface *
          g_dfloat_inverse_power_of_two[log2_voxel_size];
        surf_lb_data()->v2s_dist[voxel] = local_v2s_dist < 0 ? 0 : local_v2s_dist;
        surf_geom->dist_to_surface[voxel] = (STP_GEOM_VARIABLE) local_v2s_dist;

      } else {
        surf_geom->closest_surfel_ids[voxel] = STP_INVALID_SURFEL_ID;
        surf_geom->dist_to_surface[voxel] = 0;
      }
    }
  }
}

// This is only called on NEARBLKS
// REALM_ASSUMPTION: We are only concerned with surfels in the same realm
template<>
VOID sUBLK::finalize_distance_info()
{
  STP_REALM realm = is_conduction_solid() ? STP_COND_REALM : STP_FLOW_REALM;
  NEAR_UBLK_GEOM_DATA surf_geom = surf_geom_data();
  ccDOTIMES(voxel, sUBLK::N_VOXELS) {

    if ((sim.is_turb_model || sim.is_heat_transfer
         || sim.has_avg_mme_window
#ifdef BUILD_5G_LATTICE
          || g_is_multi_component 
#endif
          ) && 
        surf_geom->closest_surfel_ids[voxel] != STP_INVALID_SURFEL_ID) {
      // Convert surfel ID to pointer
      SHOB_ID shobid_closest_surfel_id =  surf_geom->closest_surfel_ids[voxel];
      POINTER_SIZE_INT pint_closest_surfel_id =  surf_geom->closest_surfel_ids[voxel];
      surf_geom->closest_surfels[voxel] = tTAGGED_SURFEL<SFL_SDFLOAT_TYPE_TAG>(regular_surfel_from_id(shobid_closest_surfel_id, realm));

      // set the appropriate v2s_dist value based on the closest surfel bc type
      auto closest_surfel = surf_geom->closest_surfels[voxel].get_surfel();
      if (closest_surfel->is_wall() &&
          closest_surfel->is_not_free_slip_wall()) {
        // This is set for turbulent wall only since this piece is only
        // executed if sim.is_turb_model
        surf_lb_data()->v2s_dist[voxel] += CLOSEST_SURFEL_IS_SLIP_SURFEL;
      } else if (!closest_surfel->is_lrf()) {
        surf_lb_data()->v2s_dist[voxel] = 0;
      }
    } else {
      surf_geom->closest_surfels[voxel] = tTAGGED_SURFEL<SFL_SDFLOAT_TYPE_TAG>(nullptr);
    }
  }
}

template<>
VOID sUBLK::init(DGF_UBLK_BASE_DESC ublk_desc,
                 size_t dyn_data_size) {


  cDGF_UBLK_BASE *b = &(ublk_desc->b);
  m_id    = b->ublk_id;
  m_scale = b->voxel_scale;

  m_location[0] = b->location[0];
  m_location[1] = b->location[1];
  m_location[2] = b->location[2];

  STP_COORD *simv_size = sim.control_record.simv_size;
  if (m_location[0] == 0) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_neg_face(0));
  }
  if (m_location[1] == 0) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_neg_face(1));
  }
  if ((m_location[2] == 0) && (sim.num_dims == 3)) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_neg_face(2));
  }
  asINT32 ublk_size = scale_to_voxel_size(m_scale) * 2;
  if ((m_location[0] + ublk_size) == simv_size[0]) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_pos_face(0));
  }
  if ((m_location[1] + ublk_size) == simv_size[1]) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_pos_face(1));
  }
  if (((m_location[2] + ublk_size) == simv_size[2]) && (sim.num_dims == 3)) {
    m_ublk_attributes.m_abuts_simvol_boundary |= (1 << stp_axis_to_pos_face(2));
  }
  m_ublk_attributes.m_has_real_ckpt_data = 1;

  this->set_ref_frame_index((b->lrf_index < 0) ? SRI_GLOBAL_REF_FRAME_INDEX : b->lrf_index);

  m_is_bsurfel_interacting = b->ublk_flags & DGF_UBLK_BSURFEL_INTERACTING;
  this->m_ib_bf = nullptr;
  if (m_is_bsurfel_interacting) {
    this->m_ib_bf = sSHOB_ALLOCATOR<sBSURFEL_BODY_FORCE>::malloc(sizeof(sBSURFEL_BODY_FORCE));
    memset(m_ib_bf, 0, sizeof(sBSURFEL_BODY_FORCE));
  }
  m_send_ib_bf_data = false; // gets turned on by bsurfels
  m_is_bsurfel_interacting = false; // gets turned on by bsurfels

  if (m_ublk_attributes.m_is_ghost_vr_fine) {
    ublk_desc->b.ublk_flags |= DGF_UBLK_IS_GHOST_VR_FINE;
  }
  m_prev = NULL;
  m_next = NULL;

  m_are_any_neighbors_different_scale.reset_all();
  m_are_any_neighbors_split.reset_all();
  m_vr_scale_diff.reset_all();
  BLOCK_LAYOUT::init(m_ublk_attributes, dyn_data_size);
}

// This should not be called for full checkpoint resume
template<>
VOID sUBLK::init_surfel_interaction_data() {
    cassert(!sim.is_full_checkpoint_restore);

    lrf_surfels_interaction_mask.reset_all();
    non_lrf_surfels_interaction_mask.reset_all();
    lb_interaction_voxel_mask.reset_all();
    pde_2_interaction_voxel_mask.reset_all();

    if (is_vr_fine()) {
      unset_states_clear();
    } else {
      set_states_clear();
    }
    unset_first_surfel();
    // smart seed data and dp_pas_factors are overlaid on states, so they are copied
    // from states to sp_pas_factors.
    if (sim.do_smart_seed)
      memcpy(alt_smart_seed_data(), smart_seed_data(), sizeof(sUBLK_SMART_SEED_DATA));
    // No need to do this since everything is memset to 0
    dFLOAT (*dp_pas_factors)[ubFLOAT::N_VOXELS] =
        (dFLOAT (*)[ubFLOAT::N_VOXELS]) double_precision_pas_factors();
    ccDOTIMES(i, N_LATTICE_VECTORS) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        dp_pas_factors[i][voxel] = 0.0;
      }
    }
#if BUILD_5G_LATTICE
    BOOLEAN is_multi_component = g_is_multi_component;
    BOOLEAN calc_potential_overlap = !sim.smart_seed_contact_angle &&
                                     !sim.is_full_checkpoint_restore &&
                                     !g_mp_sample_potential;
    if (calc_potential_overlap) {
      ccDOTIMES(i, N_LATTICE_VECTORS) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          surf_lb_data()->srf_potential[i][voxel] = 0.0;
          if (is_multi_component)
            surf_mc_data()->srf_potential[i][voxel] = 0.0;
        }
      }
    }
#endif
#if BUILD_D39_LATTICE
    ccDOTIMES(axis, N_AXES) {
      ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
        surf_geom_data()->normal[axis][voxel] = 0.0;
      }
    }
#endif
#if BUILD_D19_LATTICE
    if (sim.is_pf_model && !sim.is_full_checkpoint_restore) {
      ccDOTIMES(i, N_LATTICE_VECTORS) {
        ccDOTIMES(voxel, ubFLOAT::N_VOXELS) {
          surf_pf_data()->srf_potential[i][voxel] = 0.0;
        }
      }
    }
#endif
}

template<typename UBLK_TYPE_TAG>
VOID tUBLK<UBLK_TYPE_TAG>::print_ublk_voxel_content(std::ostream& os,
                                                    const tUBLK_PRINT_OPTS& opts,
                                                    asINT32 print_voxel)
{  
  asINT32 prev_lb_index = g_timescale.m_lb_tm.prior_timestep_index(scale());
  asINT32 curr_lb_index = 0;

  if ( has_two_copies() ) {
    curr_lb_index = 1 ^ prev_lb_index;
  }

  if (fluid_like_voxel_mask.test(print_voxel) ) {

    bool shouldUsePrevIndex = (opts.set == -1) && has_two_copies();
    asINT32 states_index = (shouldUsePrevIndex)? prev_lb_index:curr_lb_index;

    if ( opts.data_print_mask & opts.LB_DATA_PRINT_MASK ){
      lb_data()->print_voxel_data(os, print_voxel);
    }

    if ( opts.data_print_mask & opts.LB_STATES_DATA_PRINT_MASK ){
      print_ublk_lb_states_content(os, print_voxel,states_index);
    }

    if ( opts.data_print_mask & opts.TURB_DATA_PRINT_MASK){
      if( sim.is_turb_model ) {
        turb_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.T_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::UBLK_T_DATA_TYPE>(this) ){
        t_data()->print_voxel_data(os, print_voxel,states_index);
      }
    }

    if ( opts.data_print_mask & opts.UDS_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::UBLK_UDS_DATA_TYPE>(this) ){
        uds_data()->print_voxel_data(os, print_voxel, states_index);
      }
    }

    if ( opts.data_print_mask & opts.MC_DATA_PRINT_MASK ){
      if (BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::UBLK_MC_DATA_TYPE>(this) ){
        mc_data()->print_voxel_data(os, print_voxel, states_index);
      }
    }

    if ( opts.data_print_mask & opts.SURF_GEOM_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_GEOM_DATA_TYPE>(this) ){
        surf_geom_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.SURF_LB_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_LB_DATA_TYPE>(this) ){
        surf_lb_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.SURF_TURB_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_TURB_DATA_TYPE>(this) ){
        surf_turb_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.SURF_T_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_T_DATA_TYPE>(this) ){
        surf_t_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.SURF_UDS_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_UDS_DATA_TYPE>(this) ){
        surf_uds_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.SURF_MC_DATA_PRINT_MASK ){
      if ( BLOCK_LAYOUT::template does_ublk_have_data_of_type<UBLK_DATA_TYPE::NEARBLK_MC_DATA_TYPE>(this) ){
        surf_mc_data()->print_voxel_data(os, print_voxel);
      }
    }

    if ( opts.data_print_mask & opts.IB_BF_PRINT_MASK ) {
      if ( is_bsurfel_interacting() ) {
        cassert(m_ib_bf);
        m_ib_bf->print_voxel_data(os, print_voxel);
      }
    }
  }//if ( (fluid_like_voxel_mask >> print_voxel)  & 1 )
}

template<typename UBLK_TYPE_TAG>
VOID tUBLK<UBLK_TYPE_TAG>::print_ublk_lb_states_content(std::ostream& os,
                                                        asINT32 print_voxel,
                                                        asINT32 states_index)
{
  using namespace UBLK_SURFEL_PRINT_UTILS;
  sim_print_data_header(os, "UBLK_LB_STATES");
  sim_print<N_LATTICE_VECTORS + 1, N_VOXELS>(os, "lb_states",
                                             lb_states(states_index)->m_states,
                                             loop_limits(0, N_LATTICE_VECTORS), print_voxel);
}

template VOID tUBLK<UBLK_SDFLOAT_TYPE_TAG>::print_ublk_voxel_content(std::ostream& os,
                                                                       const tUBLK_PRINT_OPTS& opts,
                                                                       asINT32 print_voxel);

template VOID tUBLK<UBLK_SDFLOAT_TYPE_TAG>::print_ublk_lb_states_content(std::ostream& os,
                                                                           asINT32 print_voxel,
                                                                           asINT32 states_index);

#if BUILD_GPU
template VOID tUBLK<MBLK_SDFLOAT_TYPE_TAG>::print_ublk_voxel_content(std::ostream& os,
                                                                        const tUBLK_PRINT_OPTS& opts,
                                                                        asINT32 print_voxel);

template VOID tUBLK<MBLK_SDFLOAT_TYPE_TAG>::print_ublk_lb_states_content(std::ostream& os,
                                                                            asINT32 print_voxel,
                                                                            asINT32 states_index);
#endif
