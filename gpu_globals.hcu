/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef GPU_GLOBALS_H
#define GPU_GLOBALS_H

#include "helper_cuda.h"
#include "cuda.h"
#include <cooperative_groups.h>
#include "common_sp.h"
#include "gpu_host_include.h"
#include "gpu_shobs.hcu"
#include "gpu_meas.h"
#include "gpu_ptr.h"
#include "sim.h"

struct sFLUID_DYN_DCACHE_PER_SCALE_DATA;
struct sFLUID_DYN_MEAS_DCACHE;
struct sSURFEL_DYN_MEAS_DCACHE;

template <typename SEED_VAR_TYPE, asINT32 N_SEED_VARS>
struct tSEED_VAR_SPEC;
using sFLUID_SEED_VAR_SPEC = tSEED_VAR_SPEC<DGF_SEED_VAR_TYPE, DGF_N_SEED_VARS>;
using sBOUNDARY_SEED_VAR_SPEC = tSEED_VAR_SPEC<DGF_BOUNDARY_SEED_VAR_TYPE, DGF_N_BOUNDARY_SEED_VARS>;

template<typename SFL_TYPE_TAG> class tMLRF_SURFEL_FSET;
using sMLRF_MSFL_FSET = tMLRF_SURFEL_FSET<MSFL_SDFLOAT_TYPE_TAG>;

namespace GPU {

constexpr int NO_DYN_SHMEM = 0;
constexpr size_t WARP_SIZE = 32;
/// Mininum supported CUDA architecture is 6.0, aka Pascal
constexpr int MIN_CUDA_ARCH = 6;
extern cudaStream_t g_comm_thread_stream;
extern cudaStream_t g_stream;
 
extern __constant__ __device__ sGLOBALS g;

/*=================================================================================================
 * @enum REDUCTION_MODE
 * Designates if certain GPU operations such as surfel reduction will be performed in repeatable or
 * non repeatable modes. It is expected that the default GPU simulator will run in repeatable mode.
 * The global host variable g_use_repeatable_reduction is configured accordingly.
 *================================================================================================*/  
template<REDUCTION_MODE RM>
constexpr INLINE __DEVICE__ BOOLEAN is_repeatable() {
  return RM == REDUCTION_MODE::REPEATABLE;
}

template<REDUCTION_MODE RM>
constexpr INLINE __DEVICE__ BOOLEAN is_not_repeatable() {
  return RM == REDUCTION_MODE::NON_REPEATABLE;
}  

template<REDUCTION_MODE RM>
VOID INLINE __DEVICE__ sync_if_repeatable() {
  if constexpr (RM == REDUCTION_MODE::REPEATABLE) {
    GPU_SYNC_THREADS();
  }    
}

template<REDUCTION_MODE RM>
VOID INLINE __DEVICE__ sync_if_not_repeatable() {
  if constexpr (RM == REDUCTION_MODE::NON_REPEATABLE) {
    GPU_SYNC_THREADS();
  }    
}
  
/*=================================================================================================
 * @struct sUBLK_TABLE
 * This is table of device UBLK pointers. Note that the name can be a little misleading in that
 * all though this table contains a vector of device UBLK pointers, the table itself lives on the host
 *================================================================================================*/  
struct sUBLK_TABLE {
    struct Key
    {
      UBLK_GROUP_TYPE group_type;
      int scale;
      int dest_sp;
      // auto operator<=>(const Key& other) const = default;
      friend bool operator<(const Key& key1, const Key& key2) {
        return std::tie(key1.group_type, key1.scale, key1.dest_sp) < std::tie(key2.group_type, key2.scale, key2.dest_sp);
      }
    };
    struct RangeDescriptor
    {
      RangeDescriptor(size_t _offset, size_t _count) :
        offset(_offset), count(_count) {}
      const uint32_t offset;
      const uint32_t count;
      std::array<size_t,2> range() const { 
        return {offset,offset+count};
      }
    };

    sUBLK_TABLE() = default;

  __host__ VOID set_capacity(size_t n_ublks);
    
  __host__ VOID add(GPU::UBLK d_ublk, SHOB_ID id);
    
  __host__ GPU::UBLK map_H2D(const sHMBLK*const h_ublk);

  __host__ GPU::UBLK* copy_ublk_array_to_dev();

  __host__ VOID init_mblks_on_device(const std::vector<sHMBLK*>& h_mblk_table,
                                     const sMEAS_WINDOW_COLLECTION& windows);
    
  __host__ VOID allocate_ublks(const sH2D_EXPORT&);

  __host__  tVR_UBLK_PTR<GPU::sMBLK> translate_vr_ublk_H2D(::tVR_UBLK_PTR<sHMBLK> h_vr_ublk);

  __host__ VOID build_vr_coarse_explode_groups();

  __host__ std::vector<sMBLK::UBLK_BOX> assign_device_boxes(std::vector<sHMBLK::UBLK_BOX> &mega_boxes);

  __host__ VOID create_ublk_groups(MBLK_FSET (*ublk_groups) [N_UBLK_GROUP_TYPES], int);

    __host__ std::array<size_t,2> get_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) const {
      return get_group_range({group_type,scale,dest_sp});
    }
    __host__ std::array<size_t,2> get_group_range(const Key& key) const {
      return m_group_properties.at(key).range();
    }
  // __host__ VOID set_vr_coarse_explode_group_range_for_scale(size_t range[2], 
  //                                                           UBLK_GROUP_TYPE group_type, 
  //                                                           int scale) {
  //   asINT32 vr_fine_group_index = g_shob_categories.vr_fine_type_index(group_type);
  //   range[0] = m_vr_coarse_explode_offsets_by_scale[vr_fine_group_index][scale];
  //   range[1] = range[0] + m_vr_coarse_explode_counts_by_scale[vr_fine_group_index][scale];
  //   cassert(range[1] >= range[0]);
  // }
    __host__ bool group_has_measurements(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) const {
      return group_has_measurements({group_type,scale,dest_sp});
    }
    __host__ bool group_has_measurements(const Key& key) const {
      return m_group_properties.at(key).has_measurements;
    }
    __host__ std::set<int> get_group_dest_sp(UBLK_GROUP_TYPE group_type, int scale) const {
      if (auto search = m_group_dest_sp.find({group_type,scale}); search != m_group_dest_sp.end())
        return search->second;
      else
        return {};
    }

    __host__ std::array<size_t,2> get_vr_group_range(const Key& key) const {
      return m_vr_group_properties.at(key).range();
    }
  // __host__ bool group_has_measurements(UBLK_GROUP_TYPE group_type, int scale) {
  //   return m_group_has_measurements[group_type][scale];
  // }
    __host__ std::array<size_t,2> get_vr_group_range(UBLK_GROUP_TYPE group_type, int scale, int dest_sp) const {
      return get_vr_group_range({group_type,scale,dest_sp});
    }

  const std::vector<GPU::UBLK>& device_ublks() const { return m_device_ublks; }


  private:
    __host__ VOID fill_dev_ublk_vector_from_host_fset(std::vector<GPU::UBLK>& device_ublks_in_group,
						      MBLK_FSET group_fset,
						      UBLK_GROUP_TYPE group_type, int n_scales);
  private:    
    std::vector<GPU::UBLK> m_device_ublks;
    struct GroupProperties : public RangeDescriptor
    {
      GroupProperties(size_t _offset, size_t _count, bool _has_measurements) : 
        RangeDescriptor(_offset, _count), has_measurements(_has_measurements) {}
      const bool   has_measurements;
    };
    std::map<Key, GroupProperties> m_group_properties;
    // Set of valid dest_sp for a given {group_type,scale} pair
    std::map<std::pair<UBLK_GROUP_TYPE,int>, std::set<int>> m_group_dest_sp;
    using VRGroupProperties = RangeDescriptor;
    std::map<Key, VRGroupProperties> m_vr_group_properties;
    // size_t m_group_offsets_by_scale[N_NON_GHOST_UBLK_GROUP_TYPES][STP_MAX_SCALES];
    // bool m_group_has_measurements[N_NON_GHOST_UBLK_GROUP_TYPES][STP_MAX_SCALES];
    // uINT32 m_vr_coarse_explode_offsets_by_scale[sSHOB_CATEGORIES::N_VRFINE_TYPES][STP_MAX_SCALES];
    // uINT32 m_vr_coarse_explode_counts_by_scale[sSHOB_CATEGORIES::N_VRFINE_TYPES][STP_MAX_SCALES];
    
  };

extern sUBLK_TABLE g_ublk_table;
  
// VOID set_ublk_group_range_for_scale(size_t *range, UBLK_GROUP_TYPE group_type, int scale);
// VOID set_vr_coarse_explode_group_range_for_scale(size_t range[2], UBLK_GROUP_TYPE group_type, SCALE scale);
//Global array of UBLKs
extern __constant__ __device__ GPU::UBLK* g_ublk_array;
extern __constant__ __device__ GPU::UBLK* g_vr_coarse_explode_mblks;

/*=================================================================================================
 * @struct sUBLK_GROUP
 * On the GPU we keep things flat and the host code is in charge of
 * telling the kernel the range of UBLKs it must process. The device
 * UBLKs are ordered exactly as they would appear if we were to iterate
 * through the host UBLK_FSETS
 *================================================================================================*/  
struct sUBLK_GROUP {    

  size_t m_count;
  GPU::UBLK* m_ublks;

  __host__ static VOID copy_to_device(sDEV_PTR& d_ptr,
                                      size_t n_bytes,
                                      const sUBLK_GROUP* h_ptr);

};

extern __constant__ __device__ sON_DEVICE_WINDOW ** g_on_device_window_array;

struct sBATCH_INFO
{
  const size_t index;
  const size_t n_shobs;
  const size_t shob_offset;
};

/** Holds state information on the current chunk of shobs to process for dynamics.
 *
 * Can only be constructed by the cMBLK_DYN_AND_MEAS::runner()
 * function. For each block, call the next_block() function to get information
 * for the next kernel invocation.
 */
class cSHOB_DYN_AND_MEAS_RUNNER
{
  size_t m_max_shobs_per_kernel_call;
  size_t m_start_shob_idx;
  size_t m_n_shobs_to_run;
  size_t m_current_batch;
  size_t m_n_batches;

  cSHOB_DYN_AND_MEAS_RUNNER(size_t max_shobs_per_kernel_call,
                            size_t start_shob_idx,
                            size_t n_shob_to_run);

  friend class cSHOB_DYN_AND_MEAS;

public:

  size_t n_batches() const { return m_n_batches; }

  sBATCH_INFO next_batch();

}; 

/** Manages batching of kernel runs.
 *
 * For measurements and surfels, a lot of auxiliary information is needed.
 * For large cases, this could use a lot of GPU memory, so this provides a
 * mechanism to save on memory by running the shobs in batches, rather than
 * doing ALL the shobs in a single kernel invocation.
 */

class cSHOB_DYN_AND_MEAS {
  size_t m_n_mblks_per_kernel_call;
  size_t m_n_msfls_per_kernel_call;
  size_t m_device_mcache_size;
  size_t m_device_surfel_mcache_size;

  GPU::Ptr<sFLUID_DYN_MEAS_DCACHE> m_device_mcache;
  GPU::Ptr<sSURFEL_DYN_MEAS_DCACHE> m_device_surfel_mcache;

public:

  /** 
   * Compute batch sizes and allocate the auxiliary data buffers
   */
  __host__ void init_batches(size_t max_mblks_per_group, size_t max_msfls_per_group);

  /** Initializes state information for a set of mblks to process. */
  __host__ cSHOB_DYN_AND_MEAS_RUNNER ublk_runner(size_t start_mblk_idx, size_t n_mblks)
  {
    return cSHOB_DYN_AND_MEAS_RUNNER(m_n_mblks_per_kernel_call, start_mblk_idx, n_mblks);
  }

  /** Initializes state information for a set of msfls to process. */
  __host__ cSHOB_DYN_AND_MEAS_RUNNER surfel_runner(size_t start_msfl_idx, size_t n_msfls)
  {
    return cSHOB_DYN_AND_MEAS_RUNNER(m_n_msfls_per_kernel_call, start_msfl_idx, n_msfls);
  }

  __host__ size_t mblk_batch_size() { return m_n_mblks_per_kernel_call; }
  __host__ size_t msfl_batch_size() { return m_n_msfls_per_kernel_call; }
  __host__ void print_mem_usage();
}; 

extern cSHOB_DYN_AND_MEAS g_shob_dyn_and_meas;

extern __constant__ __device__ sFLUID_DYN_MEAS_DCACHE * g_mcache;
extern __constant__ __device__ sSURFEL_DYN_MEAS_DCACHE * g_surfel_mcache;

extern __constant__ __device__ sUBLK_GROUP g_ublk_groups[N_UBLK_GROUP_TYPES];

extern __constant__ __device__ sFLUID_DYN_DCACHE_PER_SCALE_DATA g_dcache_per_scale;

extern __constant__ __device__ sVR_CONFIG* g_vr_config_array;

/*=================================================================================================
 * @struct sSURFEL_TABLE
 * This is table of device SURFEL pointers. Note that the name can be a little misleading in that
 * all though this table contains a vector of device SURFEL pointers, the table itself lives on the host
 *================================================================================================*/  
  struct sSURFEL_TABLE {
    /*struct Key
    {
      SURFEL_GROUP_TYPE group_type;
      int scale;
      int dest_sp;
      // auto operator<=>(const Key& other) const = default;
      friend bool operator<(const Key& key1, const Key& key2) {
        return std::tie(key1.group_type, key1.scale, key1.dest_sp) < std::tie(key2.group_type, key2.scale, key2.dest_sp);
      }
    };*/

    using KEY = GPU::MSFL_GROUP_KEY;
    using RANGE_DESCRIPTOR = GPU::RANGE_DESCRIPTOR; 

    sSURFEL_TABLE() = default;

  __host__ VOID set_capacity(size_t n_surfels);
    
  __host__ VOID add(GPU::SURFEL d_sfl, SHOB_ID id);
    
  __host__ GPU::SURFEL map_H2D(const ::sMSFL*const h_sfl);

  __host__ GPU::SURFEL* copy_surfel_array_to_dev();

  __host__ VOID initialize_msfl_group_ranges();

  __host__ VOID allocate_surfels(const ::sMSFL_SIZES_TABLE& host_surfel_sizes_table, const sMEAS_WINDOW_COLLECTION& windows);

  __host__ VOID allocate_surfel_interactions(const ::sMSFL_SIZES_TABLE& host_surfel_sizes_table);
    
  __host__ VOID create_surfel_groups(MSFL_FSET (*surfel_groups) [N_SURFEL_GROUP_TYPES], int);

    // __host__ VOID set_group_range_for_scale(size_t *range, SURFEL_GROUP_TYPE group_type, int scale) {
    //   //use set_mlrf_group_range_for_scale for mlrf groups
    //   cassert(group_type != MLRF_SURFEL_GROUP_TYPE);
    //   try {
    //     auto map_range = get_group_range({group_type, scale});
    //     range[0] = map_range[0];
    //     range[1] = map_range[1];
    //   } catch (const std::out_of_range& ex) {
    //     range[0] = range[1] = 0;
    //   }
    // }

    // __host__ VOID set_group_range_for_scale(size_t *range, SURFEL_GROUP_TYPE group_type, int scale, int dest_sp) 
    //  {
    //   //use set_mlrf_group_range_for_scale for mlrf groups
    //   cassert(group_type != MLRF_SURFEL_GROUP_TYPE);
    //   try {
    //     auto map_range = get_group_range({group_type, scale, dest_sp});
    //     range[0] = map_range[0];
    //     range[1] = map_range[1];
    //   } catch (const std::out_of_range& ex) {
    //     range[0] = range[1] = 0;
    //   }
    // }

    __host__ std::array<size_t,2> get_group_range(const KEY& key) const {
    return m_group_properties.at(key).range();
  }
  __host__ std::array<size_t,2> get_group_range(SURFEL_GROUP_TYPE group_type,
                                                STP_SCALE scale,
                                                STP_PROC dest_sp,
                                                STP_EVEN_ODD even_odd_mask,
                                                sLRF_PHYSICS_DESCRIPTOR* lrf) const {
    return get_group_range({group_type, scale, dest_sp, even_odd_mask, lrf});
  }
  __host__ std::array<size_t,2> get_group_range(SURFEL_GROUP_TYPE group_type, int scale, int dest_sp) const {
      return get_group_range({group_type,scale,dest_sp});
    }
    __host__ std::set<int> get_group_dest_sp(SURFEL_GROUP_TYPE group_type, int scale) const {
      if (auto search = m_group_dest_sp.find({group_type,scale}); search != m_group_dest_sp.end())
        return search->second;
      else
        return {};
    }

  const std::vector<GPU::SURFEL>& device_surfels() const { return m_device_surfels; }

  __host__ VOID set_mlrf_group_range_for_scale(size_t *range, int scale, STP_EVEN_ODD even_odd_mask, LRF_PHYSICS_DESCRIPTOR lrf) {
    try {
      auto map_range = get_group_range({MLRF_SURFEL_GROUP_TYPE, scale, 0, even_odd_mask, lrf});
      range[0] = map_range[0];
      range[1] = map_range[1];
    } catch (const std::out_of_range& ex) {
      range[0] = range[1] = 0;
    }
  }

  VOID prepare_mlrf_tagged_pointers_on_device();

    BOOLEAN is_empty() const { return m_device_surfels.size() == 0; }
    __host__ VOID fill_dev_surfel_vector_from_host_fset(std::vector<GPU::SURFEL>& device_surfels_in_group,
						                                            MSFL_FSET group_fset,
							SURFEL_GROUP_TYPE group_type, int n_scales);    
  
  private:
    std::vector<GPU::SURFEL> m_device_surfels;
    //using SurfelGroupProperties = sUBLK_TABLE::RangeDescriptor;
    std::map<KEY, RANGE_DESCRIPTOR> m_group_properties;
    // Set of valid dest_sp for a given {group_type,scale} pair
    std::map<std::pair<SURFEL_GROUP_TYPE,int>, std::set<int>> m_group_dest_sp;
};

extern sSURFEL_TABLE g_surfel_table;

  std::array<size_t,2> get_surfel_group_range(SURFEL_GROUP_TYPE group_type, int scale, int dest_sp);
VOID set_mlrf_msfl_group_range_for_scale(size_t *range, int scale,
                                         STP_EVEN_ODD even_odd_mask, LRF_PHYSICS_DESCRIPTOR lrf);

//Global array of SURFELs
extern __constant__ __device__ GPU::SURFEL* g_surfel_array;

struct sSURFEL_GROUP {

  size_t m_count;
  GPU::SURFEL* m_surfels;

  __host__ static VOID copy_to_device(sDEV_PTR& d_ptr,
                                      size_t n_bytes,
                                      const sSURFEL_GROUP* h_ptr);

};  

extern __constant__ __device__ sSURFEL_GROUP g_surfel_groups[N_SURFEL_GROUP_TYPES];

struct sPHYSICS_DESCRIPTOR_H2D_MAP {
private:
  std::unordered_map<sPHYSICS_DESCRIPTOR*, sPHYSICS_DESCRIPTOR*> m_map;
    
public:    
  sPHYSICS_DESCRIPTOR* map_H2D(sPHYSICS_DESCRIPTOR* h_ptr) const {
    return m_map.at(h_ptr);
  }

  VOID insert(sPHYSICS_DESCRIPTOR* h_ptr, sPHYSICS_DESCRIPTOR* d_ptr) {
    assert(h_ptr && d_ptr);
    assert(m_map.find(h_ptr) == m_map.end());
    m_map.emplace(h_ptr, d_ptr);
  }
};

extern sPHYSICS_DESCRIPTOR_H2D_MAP g_phys_desc_h2d_map;
extern __constant__ Inverse_power_of_two_array <2 * STP_MAX_SCALES -1> g_dfloat_inverse_power_of_two;
extern __device__ sFLUID_SEED_VAR_SPEC*    g_fluid_seed_var_specs;    // fluid region seed var specs
extern __device__ sBOUNDARY_SEED_VAR_SPEC* g_boundary_seed_var_specs; // boundary face seed var specs

VOID init_exprlang_mgr();
}//Namespace GPU

static _INLINE_ __device__ sON_DEVICE_WINDOW **& get_meas_window_coll_ref() {
  return GPU::g_on_device_window_array;
}

#endif
