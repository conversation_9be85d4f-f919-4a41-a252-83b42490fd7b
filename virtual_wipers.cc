/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "virtual_wipers.h"
#include "sim.h"
#include "ublk.h"
#include "surfel_table.h"
#include "shob_groups.h"
#include "particle_sim_info.h"

extern sSIM_INFO sim;

sPARTICLE_VAR sVIRTUAL_WIPER::range_of_influence() {
  //return(0.005/ LENGTH_TO_MKS_SCALE_FACTOR);  //5mm hard coded. 
  return g_wiper_range_of_influence;
}


void compute_rotation_transformation(asINT32 axis,sFLOAT theta,sPARTICLE_VAR R[3][3]) {
  sFLOAT cos_theta=cos(theta);
  sFLOAT sin_theta=sin(theta);
  switch(axis) {
  case 0: //rotation about x
    R[0][0]=1.0;        R[0][1]= 0.0;       R[0][2]= 0.0;
    R[1][0]=0.0;        R[1][1]= cos_theta; R[1][2]=-sin_theta;
    R[2][0]=0.0;        R[2][1]= sin_theta; R[2][2]= cos_theta;
    break;
  case 1: //rotation about y
    R[0][0]= cos_theta; R[0][1]= 0.0;       R[0][2]= sin_theta;
    R[1][0]= 0.0;       R[1][1]= 1.0;       R[1][2]= 0.0;
    R[2][0]=-sin_theta; R[2][1]= 0.0;       R[2][2]= cos_theta;
    break;
  case 2: //rotation about z
    R[0][0]= cos_theta; R[0][1]=-sin_theta; R[0][2]= 0.0;
    R[1][0]= sin_theta; R[1][1]= cos_theta; R[1][2]= 0.0;
    R[2][0]= 0.0;       R[2][1]= 0.0;       R[2][2]= 1.0;
    break;
  default: break;
  }
}


#define SQRT_3_OVER_4 0.866025403784

BOOLEAN sVIRTUAL_WIPER::surfel_is_near_virtual_wiper(
                                                     SURFEL surfel, 
                                                     PARTICLE_VAR centroid_to_blade_norm_coord, //If the surfel is near a wiper, these items are set.
                                                     PARTICLE_VAR blade_normal_direction,
                                                     PARTICLE_VAR local_blade_velocity,
                                                     PARTICLE_VAR blade_midpoint) 
{
  if(!surfel->p_data()->surfel_stencil_is_subject_to_simple_wiper_model())
    return FALSE;

  sPARTICLE_VAR y[N_SPACE_DIMS];
  vcopy(y, m_current_blade_position);

  sPARTICLE_VAR x[N_SPACE_DIMS];
  vsub(x, surfel->centroid, m_current_blade_position);

  sPARTICLE_VAR x_dot_n = vdot(x, m_current_blade_normal);

  sPARTICLE_VAR local_voxel_size = scale_to_voxel_size(surfel->scale());
  if(fabs(x_dot_n) < (SQRT_3_OVER_4 * local_voxel_size * range_of_influence())) {
    //If it was, next check if the projection of x onto the virtual wiper plane is within a specified rectangle on that plane
    sPARTICLE_VAR x_dot_span = vdot(x, m_current_blade_span_direction);

    if(fabs(x_dot_span) < 0.5 * m_blade_length) {//the spanwise coordinate relative to the center of the blade must be within one half blade lengths
      sPARTICLE_VAR x_dot_height = 0.0;
      ccDOTIMES(i,N_SPACE_DIMS) {
        x_dot_height += x[i] * m_current_blade_height_direction[i];
      }
      //if(fabs(x_dot_height) < 0.5 * (m_parameters.blade_depth / LENGTH_TO_MKS_SCALE_FACTOR))
      //then this voxel and the surface particles it contains should be affected by this wiper
      //set the parameters the caller needs to modify the surface particle trajectories
      //return the blade normal vector, and the local wiper velocity vector
      vcopy(blade_normal_direction, m_current_blade_normal);

      sPARTICLE_VAR omega[N_SPACE_DIMS] = {0,0,0};
      sPARTICLE_VAR r[N_SPACE_DIMS] = {0,0,0};
      if (m_parameters.rotate_wiper_with_arm) {
        //if the blade rotates with the arm its attached to, the local blade velocity varies with the radius from the local voxel to the pivot point
        vsub(r, surfel->centroid, m_parameters.pivot_point);
      } else {
        //if this is a bus style blade (pantograph), the local blade velocity is constant along the blade and therfore equal to the velocity of the end of the arm it's attached to
        vsub(r, m_current_blade_position,  m_parameters.pivot_point); //the radius from the pivot point to the blade midpoint
      }
      sPARTICLE_VAR dt = 1.0;
      sPARTICLE_VAR angular_speed = (m_current_angle - m_previous_angle) / dt;  //in radians per timestep
      vscale(omega, angular_speed, m_parameters.rotation_axis);
      vcross(local_blade_velocity, omega, r);
      *centroid_to_blade_norm_coord = x_dot_n;
      vcopy(blade_midpoint, m_current_blade_position);
      return TRUE;  //assume only one wiper affects any voxel at a given time, ignore other wipers if they are overlaping at some instant due to "setup problems"
      //} //Blade depth parameter removed since surfaces are now marked as wipeable or not.
    }
  }
  return FALSE;
}




VOID sVIRTUAL_WIPER::update_position() {

  //determine the phase and angle of the wiper rotation
  //    let the stroke period be defined as the time it takes the wiper to rotate from initial angle to final angle
  //    let the delay period be defined as the time the wiper blade sits at the initial angle between a stroke
  //    one complete cycle is composed of a stroke forward, a stroke back, followed by a delay period

  //if the wipers are on, update the rotation angle, otherwise leave the angle unchanged
  //m_previous_angle = m_current_angle; //store the last two angles so that the velocity can be calculated easily even if the user has the wiper turn off mid stroke
  
  if(m_parameters.initial_delay == TIMESTEP_INVALID) {
    if(m_parameters.start_mode != LGI_VIRTUAL_WIPER_REC::START_AFTER_EMITTER)
      return;
    //Check if the wiper has been activated by an emitter (which could have been unpredictibly started by a monitor).
    if(g_particle_sim_info.emitters[m_parameters.emitter_index]->start_time()  + m_parameters.emitter_delay > g_timescale.time_flow()) 
      return;
    m_parameters.initial_delay = g_timescale.time_flow();
#if 0
    msg_print("Wiper \"%s\" was activated at timestep %ld, %g timesteps after emitter \"%s\" was started.", 
              m_name.c_str(), 
              g_timescale.m_time, 
              m_parameters.emitter_delay, 
              g_particle_sim_info.emitters[m_parameters.emitter_index]->name().c_str());
#endif
  }

  //determine how far into a phase the given simulation time is
  if(g_timescale.time_flow() > m_parameters.initial_delay) {
    asINT32 N = (m_parameters.stroke_period + m_parameters.delay_period);  //the number of timesteps in once complete cycle
    asINT32 n_stroke = m_parameters.stroke_period / 2;  //n_s is the number of timesteps in one half stroke (movement from initial angle to final angle)
    //Determine if the wiper should continue with more cycles
    if(m_parameters.num_strokes >=0)
      if(g_timescale.time_flow() - (int)(m_parameters.initial_delay)  > N * m_parameters.num_strokes)
        return;

   //determine which part of the wiper cycle this is
    asINT32 n = ( g_timescale.time_flow() - (int)(m_parameters.initial_delay) ) % N; //n goes linearly from 0 to N every wiper phase
    n = n - n_stroke ;  //n now goes from -n_s to n_s + n_d
    if(n < 0)
      n *= -1;    //now n goes from n_s, to zero (at the end of the forward stroke), back up to n_stroke + n_delay at the end of the delay
    n = n_stroke - n;          //now n starts at 0 at the begining of a new cycle, goes to n_s (by the end of the forward stroke) then back to 0 (by the end of the back stroke) then to -n_d by the end of the delay period
    if(n < 0)
      n = 0;       //now n starts from 0 at the begining of a new cycle, goes to n_s (by the end of the forward stroke) then back to 0 (by the end of the back stroke) then stays at zero until the end of the delay period
    sPARTICLE_VAR phase = n / (sPARTICLE_VAR)n_stroke;  //phase goes linearlly from 0 at no rotation to 1 at full rotation after one stroke period then back to 0 after another stoke period, then remains at zero for one delay period
    sPARTICLE_VAR theta = phase * (m_parameters.final_angle - m_parameters.initial_angle) + m_parameters.initial_angle;
    m_previous_angle = m_current_angle; //store the last two angles so that the velocity can be calculated easily even if the user has the wiper turn off mid stroke
    m_current_angle = theta;
  }



  //Apply the time dependant rotation to the vectors defining the blade geometry;
  sPARTICLE_VAR rotate_about_x[N_SPACE_DIMS][N_SPACE_DIMS];
  compute_rotation_transformation(0, m_current_angle, rotate_about_x);

  sPARTICLE_VAR arm_radius[N_SPACE_DIMS];
  sPARTICLE_VAR blade_normal[N_SPACE_DIMS];
  sPARTICLE_VAR blade_span_direction[N_SPACE_DIMS];
  sPARTICLE_VAR blade_height_direction[N_SPACE_DIMS];

  //    At initialization, all relevant blade geometry inputs were transformed so that the axis of rotation for the wiper is aligned with a new x axis.
  //    Time dependant rotation is then applied about this new x axis, after which the inverse of the origional transfomation is applied to get back to the default coord sys

  ccDOTIMES(i,N_SPACE_DIMS) {
    arm_radius[i] = 0.0;
    blade_normal[i] = 0.0;
    blade_span_direction[i] = 0.0;
    blade_height_direction[i] = 0.0;
    ccDOTIMES(j,N_SPACE_DIMS) {
      arm_radius[i] += rotate_about_x[i][j] * m_arm_radius[j];

      if( m_parameters.rotate_wiper_with_arm ) {  //normaly a wiper will rotate as the arm it is attached to rotates
        blade_normal[i] += rotate_about_x[i][j] * m_blade_normal[j];
        blade_span_direction[i] += rotate_about_x[i][j] * m_blade_span_direction[j];
        blade_height_direction[i] += rotate_about_x[i][j] * m_blade_height_direction[j];
      } else {
        //But if this is a bus style wiper, the orientation of the
        //wiper should stay constant as the blade translates while hinged the end of the rotating wiper arm.
        //In reality, this is accomplised with a second wiper arm parallel to the first would maintain
        //the wiper blade's angle but the kinematics of any particular linkages are not modeled; it's assumed
        //that they are designed in such a way that the wiper won't rotate at all as it sweeps out an arc.
        blade_normal[i] += (i == j ? 1.0 : 0.0) * m_blade_normal[j];
        blade_span_direction[i] += (i == j ? 1.0 : 0.0) * m_blade_span_direction[j];
        blade_height_direction[i] += (i == j ? 1.0 : 0.0) * m_blade_height_direction[j];
      }
    }
  }

  //now transform back to the default coord system
  ccDOTIMES(i,N_SPACE_DIMS) {
    m_current_arm_radius[i] = 0.0;
    m_current_blade_normal[i] = 0.0;
    m_current_blade_span_direction[i] = 0.0;
    m_current_blade_height_direction[i] = 0.0;
    ccDOTIMES(j,N_SPACE_DIMS) {
      //note the transpose is being used to get the inverse transformation (matrix is orthonormal)
      m_current_arm_radius[i] += m_align_to_rotation_axis_transformation[j][i] * arm_radius[j];
      m_current_blade_normal[i] += m_align_to_rotation_axis_transformation[j][i] * blade_normal[j];
      m_current_blade_span_direction[i] += m_align_to_rotation_axis_transformation[j][i] * blade_span_direction[j];
      m_current_blade_height_direction[i] += m_align_to_rotation_axis_transformation[j][i] * blade_height_direction[j];
    }
  } //alternativly, one could calculate a transformation matrix which does both rotations and apply it once to all vectors, instead of in two stages (the later of which already has the transformation matrix computed at initialization).
    //however to calculate the all-at-once transformation requires a matrix-matrix multiply instead of two matrix vector multiplies so the cost is about the same

    //for coordinate vectors, apply the affine part of the transformation
  ccDOTIMES(i, N_SPACE_DIMS)
    m_current_blade_position[i] = m_parameters.pivot_point[i] + m_current_arm_radius[i];
}


VOID sVIRTUAL_WIPER::initialize() {

  //the user specifes initial blade geometry in terms of the following (which are accesible in m_parameters):
  //pivot_point
  //rotation axis
  //blade_normal                                    #this specifes the direction the blade tangent to the direction it wipes
  //blade_edge_1, blade_edge_2                      #this specifies the location of the two blade ends


  //from those, these need to be calculated at initialization:
  //allgn_to_rotation_axis_transformation
  //arm_radius = 0.5*(edge_2 + edge_1) - pivot_point        #this is a vector from the pivot point to the center of the wiper blade (defines one point on the virtual wiper blade plane)
  //blade_span_direction = normalized(edge_2 - edge_1)      #this is a unit vector that points along the wiper blade (assumming it were straight)
  //blade_normal = blade_span_direction cross rotation axis
  //blade_height_direction = blade_normal <cross product> blade_span_direction
  //blade_length = magnitude(edge_2 - edge_1)               #length of the blade

  //All parameters obtained from the CDI file are specified in the lattice csys in lattice units. (5/17/16)

  //Compute some transformations needed to move the wiper.
  dFLOAT rotate_about_y[N_SPACE_DIMS][N_SPACE_DIMS];
  dFLOAT rotate_about_z[N_SPACE_DIMS][N_SPACE_DIMS];
  dFLOAT alpha = atan2(m_parameters.rotation_axis[2],m_parameters.rotation_axis[0]);  //rotation axis has azmith about y of alpha radians from x
  compute_rotation_transformation(1, alpha, rotate_about_y);
  dFLOAT temp[N_SPACE_DIMS];
  ccDOTIMES(i, N_SPACE_DIMS) {
    temp[i] = 0;
    ccDOTIMES(j, N_SPACE_DIMS)
      temp[i] += rotate_about_y[i][j] * m_parameters.rotation_axis[j];
  }
  dFLOAT beta = atan2(temp[1], temp[0]);  //the rotation axis had an elevation of beta radians from the x-z plane
  compute_rotation_transformation(2, -beta, rotate_about_z);
  ccDOTIMES(i, N_SPACE_DIMS){  //do a 3x3 matrix matrix multiplication
    ccDOTIMES(j, N_SPACE_DIMS) {
      m_align_to_rotation_axis_transformation[i][j] = 0.0;
      ccDOTIMES(k, N_SPACE_DIMS)
        m_align_to_rotation_axis_transformation[i][j] += rotate_about_z[i][k] * rotate_about_y[k][j];
    }
  }

  //Compute other quantities:
  ccDOTIMES(i,N_SPACE_DIMS) {
    m_arm_radius[i] = 0.5 * (m_parameters.blade_edge_1[i] + m_parameters.blade_edge_2[i]) - m_parameters.pivot_point[i];
    m_blade_span_direction[i] = m_parameters.blade_edge_2[i] - m_parameters.blade_edge_1[i];
  }

  m_blade_length = std::sqrt(vdot(m_blade_span_direction, m_blade_span_direction));
  vunitize(m_blade_span_direction);   //Normalize the blade span direction.
  vunitize(m_parameters.rotation_axis);   //Make sure the rotation axis is normalized.
  //Compute the blade normal as the vector perpendicular to the blade_span_direction and the axis of rotation
  vcross(m_blade_normal, m_blade_span_direction, m_parameters.rotation_axis );

  //Calculate the cross product of blade_normal and blade_span_direction to get the height direction vector
  //This is represntitive of the wipeable surface normal and will essentially be the same as the rotation axis with this copmutation.
  vcross(m_blade_height_direction, m_blade_normal, m_blade_span_direction);

  //Lastly, use the transformation calcualted previously to rotate all vectors defining the wiper arm geometry to a rotated coord sys which has its x axis aligned with the wiper rotation axis.
  dFLOAT arm_radius[N_SPACE_DIMS] = {0.0,0.0,0.0};
  dFLOAT blade_normal[N_SPACE_DIMS] = {0.0,0.0,0.0};
  dFLOAT blade_span_direction[N_SPACE_DIMS] = {0.0,0.0,0.0};
  dFLOAT blade_height_direction[N_SPACE_DIMS] = {0.0,0.0,0.0};

  ccDOTIMES(i, N_SPACE_DIMS)
    ccDOTIMES(j, N_SPACE_DIMS) {
    arm_radius[i] += m_align_to_rotation_axis_transformation[i][j] * m_arm_radius[j];
    blade_normal[i] += m_align_to_rotation_axis_transformation[i][j] * m_blade_normal[j];
    blade_span_direction[i] += m_align_to_rotation_axis_transformation[i][j] * m_blade_span_direction[j];
    blade_height_direction[i] += m_align_to_rotation_axis_transformation[i][j] * m_blade_height_direction[j];
  }
  ccDOTIMES(i, N_SPACE_DIMS) {
    m_arm_radius[i] = arm_radius[i];
    m_blade_normal[i] = blade_normal[i];
    m_blade_span_direction[i] = blade_span_direction[i];
    m_blade_height_direction[i] = blade_height_direction[i];
  }
  update_position();
  m_previous_angle = m_current_angle;

  //Check if this wiper is started by a monitor. 
  if(m_parameters.start_mode != LGI_VIRTUAL_WIPER_REC::START_AT_TIME) 
    m_parameters.initial_delay = TIMESTEP_INVALID;
}

VOID sVIRTUAL_WIPER::flag_surfels_wiped_by_virtual_wiper() {
  //Go through all the dynamics surfels that might cary film and mark if they are subject to this wiper.
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_FLOW_OF_SCALE(surfel, scale) {

      //For this surfel, check if any of the surfels in its stencil are wipeable.
      for(sFILM_STENCIL_SURFEL_VECTOR_ITERATOR i = surfel->p_data()->nearby_surfels->begin() ;
          i < surfel->p_data()->nearby_surfels->end() ;
          i++) {
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
        sINT16 face_id = (*i)->face_index();
#else
	sINT16 face_id = (*i).get_surfel()->face_index();
#endif
        if(find(wiped_surfaces.begin(), wiped_surfaces.end(), face_id) != wiped_surfaces.end()) {
          surfel->p_data()->mark_surfel_stencil_subject_to_simple_wiper_model();
        }
      }
    }
  }
}
