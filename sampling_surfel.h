/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * James Hoch, Exa Corporation
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_SAMPLING_SURFEL_H
#define _SIMENG_SAMPLING_SURFEL_H

#include "common_sp.h"
#include "lb_solver_data.h"
#include "turb_solver_data.h"
#include "t_solver_data.h"
#include "conduction_data.h"
#include "scalar_data.h"
#include "mc_data_5g.h"
#include "shob.h"
#include "surfel.h"

#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#include "particle_solver_data.h"
#endif

#define SAMPLING_SURFEL_APPLY(method, args)                                     \
  this->lb_data()->method args;                                                 \
  if (sim.is_turb_model)                                                        \
    this->turb_data()->method args;                                             \
  if (sim.is_heat_transfer || sim.switch_acous_during_simulation)               \
    this->t_data()->method args;                                                \
  if (sim.is_conduction_model && this->is_conduction_surface())                 \
    this->conduction_data()->method args;                                       \
  if (sim.is_scalar_model){              					\
    SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = this->uds_data();  \
    for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;       \
         nth_uds++,sampling_surfel_uds_data++)                            \
      sampling_surfel_uds_data->method args;		                     	\
  }										\
  if (g_is_multi_component)                                                     \
    this->mc_data()->method args;\
  if(sim.is_particle_model)\
    this->p_data()->method args;

#define SAMPLING_SURFEL_APPLY_ACTIVE(active_solver_mask, method, args)          \
  if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
    this->lb_data()->method args;                                               \
  if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
    this->turb_data()->method args;                                             \
  if (is_solver_active(T_SOLVER, active_solver_mask))                           \
    this->t_data()->method args;                                                \
  if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask) && this->is_conduction_surface())                 \
    this->conduction_data()->method args;                                       \
  if (is_solver_active(UDS_SOLVER, active_solver_mask)) {                    \
    SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = this->uds_data();  \
    for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;       \
         nth_uds++,sampling_surfel_uds_data++)                            \
      sampling_surfel_uds_data->method args; }                               \
  if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
    this->mc_data()->method args;

// This macro assumes that arg1 has data blocks with the same accessor methods as a sampling surfel.
// Obviously this means that arg1 can be a sampling surfel.
#define SAMPLING_SURFEL_APPLY_ACTIVE_1(active_solver_mask, method, arg1)        \
  if (is_solver_active(LB_SOLVER, active_solver_mask))                          \
    this->lb_data()->method (arg1->lb_data());                                  \
  if (is_solver_active(TURB_SOLVER, active_solver_mask))                        \
    this->turb_data()->method (arg1->turb_data());                              \
  if (is_solver_active(T_SOLVER, active_solver_mask))                           \
    this->t_data()->method (arg1->t_data());                                    \
  if (is_solver_active(CONDUCTION_SOLVER, active_solver_mask) && this->is_conduction_surface())                 \
    this->conduction_data()->method (arg1->conduction_data());                                       \
  if (is_solver_active(UDS_SOLVER, active_solver_mask))    {                 \
    SAMPLING_SURFEL_UDS_DATA sampling_surfel_uds_data = this->uds_data();  \
    SAMPLING_SURFEL_UDS_DATA arg1_uds_data = arg1->uds_data();            \
    for (asINT32 nth_uds = 0; nth_uds < sim.n_user_defined_scalars;       \
         nth_uds++,sampling_surfel_uds_data++,arg1_uds_data++)            \
      sampling_surfel_uds_data->method(arg1_uds_data); }                  \
  if (g_is_multi_component && is_solver_active(LB_SOLVER, active_solver_mask))  \
    this->mc_data()->method (arg1->mc_data());


class sSURFEL_UBLK_INTERACTION;
VOID add_ublk_interactions_internal(DGF_SURFEL_DESC surfel_desc,
                                    uINT16 *p_n_ublk_interactions,
                                    sSURFEL_UBLK_INTERACTION **p_ublk_interactions);

struct sSAMPLING_SURFEL_GROUP;  // forward declaration
//------------------------------------------------------------------------------
// SAMPLING_SURFEL
//------------------------------------------------------------------------------
typedef struct sSAMPLING_SURFEL : sSURFACE_SHOB
{
  sSAMPLING_SURFEL() {
    memset(this, 0, sizeof(sSAMPLING_SURFEL));
    set_wall(FALSE);
    m_surfel_attributes.m_is_sampling_surfel = 1;
    m_surfel_attributes.m_is_backside = 0;

  }

  uINT32 m_sampling_surfel_type;
  STP_SURFEL_WEIGHT sample_weight_sum;

  sSAMPLING_SURFEL_LB_DATA   lb_solver_data;
  sSAMPLING_SURFEL_TURB_DATA turb_solver_data;
  sSAMPLING_SURFEL_T_DATA    t_solver_data;
  sSAMPLING_SURFEL_UDS_DATA  uds_solver_data[MAX_N_USER_DEFINED_SCALARS];
  sSAMPLING_SURFEL_PARTICLE_DATA p_solver_data;
  sSAMPLING_SURFEL_MC_DATA   mc_solver_data;
  sSAMPLING_SURFEL_CONDUCTION_DATA    conduction_solver_data;
  union {
    sSAMPLING_SURFEL         *m_clone_surfel;
    STP_SURFEL_ID            m_clone_surfel_id;
  };

  sSURFEL_MEAS_DATA        m_surfel_meas_data;  // 12 bytes

  sSAMPLING_SURFEL* m_next;
  sSAMPLING_SURFEL* m_prev;
  sSAMPLING_SURFEL_GROUP*  m_group;

  VOID set_group(sSAMPLING_SURFEL_GROUP* group)  { m_group = group; }

  VOID set_clone_surfel_index(STP_SURFEL_ID clone_index) {
    m_clone_surfel_id = clone_index;
  }

  BOOLEAN is_even_or_odd() {
    return (m_clone_surfel!= NULL);
  }

  sSAMPLING_SURFEL * clone_surfel() {
    return m_clone_surfel;
  }

  sSURFEL_MEAS_CELL_PTR *create_meas_cell_ptrs(asINT32 n_meas_cells) {
	  m_surfel_meas_data.m_n_meas_cell_ptrs = n_meas_cells;
	  m_surfel_meas_data.m_surfel_meas_cell_ptrs = cnew sSURFEL_MEAS_CELL_PTR[n_meas_cells];
	  return m_surfel_meas_data.m_surfel_meas_cell_ptrs;
  }

  VOID resolve_meas_cell_ptrs();

  sS2S_ADVECT_DATA           m_s2s_advect_data;
  VOID add_surfel_interactions(DGF_SURFEL_DESC surfel_desc) {
    m_s2s_advect_data.create_surfel_interactions(surfel_desc);
  }

  BOOLEAN is_s2s_destination() {
    return (m_s2s_advect_data.m_n_src_surfels != 0);
  }

#if 0
  void add_ublk_interactions(DGF_SURFEL_DESC surfel_desc) {
    add_ublk_interactions_internal(surfel_desc, &m_n_ublk_interactions, &m_ublk_interactions);
  }
#else
  void add_ublk_interactions(uINT16 n_ublk_interactions, SURFEL_UBLK_INTERACTION ublk_interactions) {
    m_n_ublk_interactions = n_ublk_interactions;
    m_ublk_interactions = ublk_interactions;
  }
#endif

  // Solver data block access methods
  SAMPLING_SURFEL_LB_DATA   lb_data()           { return &lb_solver_data;    }
  SAMPLING_SURFEL_TURB_DATA turb_data()         { return &turb_solver_data;  }
  SAMPLING_SURFEL_T_DATA    t_data()            { return &t_solver_data;     }
  sS2S_ADVECT_DATA *s2s_advect_data()           { return &m_s2s_advect_data; }
  SAMPLING_SURFEL_UDS_DATA  uds_data()    { return &(uds_solver_data[0]);}
  SAMPLING_SURFEL_UDS_DATA  uds_data(asINT32 nth_uds)    { return &(uds_solver_data[nth_uds]);}
//#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
  SAMPLING_SURFEL_PARTICLE_DATA  p_data()       {return(&p_solver_data);           }
//#endif
  SAMPLING_SURFEL_MC_DATA   mc_data()           { return &mc_solver_data;    }
  SAMPLING_SURFEL_CONDUCTION_DATA    conduction_data()            { return &conduction_solver_data;     }

  dFLOAT                    *dp_pgram_volumes() { return NULL; } // required for templated code (see surfel_dyn.h)
  STP_GEOM_VARIABLE         *pgram_volumes()    { return NULL; } // required for templated code (see surfel_dyn.h)
  //virtual sFLOAT*           get_states()        { return (sFLOAT*)&lb_solver_data; }//being used as scratch data
  //virtual SHOB_TYPE         type()              { return SAMPLING_SURFEL_TYPE;     }
  sSAMPLING_SURFEL          *next()             { return (sSAMPLING_SURFEL *)((char*)this + size()); }
  static  sINT16            size()              { return sizeof(sSAMPLING_SURFEL); }
  static  BOOLEAN           uses_shared_mem()   { return FALSE;                    }
  static  asINT32           states_offset()     { return -1; } // there are no "states"
  static  asINT32           states_size()       { return -1; }
  static  VOID              compute_layout()    { } // no dynamic storage allocation yet...
  static  cSTRING           name()              { return "SAMPLING_SURFEL";}
  static  SHOB_TYPE         static_type()       { return SAMPLING_SURFEL_TYPE; }

  VOID pre_advect_init(ACTIVE_SOLVER_MASK mask) {
    SAMPLING_SURFEL_APPLY_ACTIVE(mask, pre_advect_init, ());
  }

  VOID pre_advect_init_copy_even_to_odd(ACTIVE_SOLVER_MASK mask, sSAMPLING_SURFEL *even_surfel) {
    SAMPLING_SURFEL_APPLY_ACTIVE_1(mask, pre_advect_init_copy_even_to_odd, even_surfel);
  }

  uINT64 ckpt_len(bool verbose=false);
  VOID write_ckpt();
  size_t write_ckpt(sCKPT_BUFFER& pio_ckpt_buff);
  VOID read_ckpt();

  // Call the init() method of all solver data blocks
  VOID init(DGF_SURFEL_DESC surfel_desc, bool is_ghost);

  BOOLEAN is_home_voxel(STP_LOCATION voxel_location, asINT32 voxel_size)
  {
    sdFLOAT x_min = voxel_location[0];
    sdFLOAT y_min = voxel_location[1];
    sdFLOAT z_min = voxel_location[2];
    sdFLOAT x_max = x_min + voxel_size;
    sdFLOAT y_max = y_min + voxel_size;
    sdFLOAT z_max = z_min + voxel_size;

    return ((centroid[0] >= x_min) && (centroid[0] <= x_max) &&
            (centroid[1] >= y_min) && (centroid[1] <= y_max) &&
            (centroid[2] >= z_min) && (centroid[2] <= z_max));
  }
  SP_TIMER_TYPE timer() {return SP_SAMPLING_SURFEL_DYN_TIMER;}

  VOID unpack_neighbor_ublks(uINT8 *& buffer);
  VOID fill_neighbor_ublks(uINT8 *& buffer);
  VOID count_neighbor_ublks_bytes(uINT32 &num_bytes);

} *SAMPLING_SURFEL;


#endif  /* _SIMENG_SAMPLING_SURFEL_H */
