/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#include "gpu_comm_groups.h"
#include "gpu_globals.hcu"
#include "mlrf.h"

template<int N> 
struct print_size_as_warning
{ 
   char operator()() { return N + 256; } //deliberately causing overflow
};

cFAR_MBLK_RECV_FSET g_far_mblk_recv_fset;
cNEAR_MBLK_RECV_FSET g_near_mblk_recv_fset;
cMBLK_SEND_FSET g_mblk_send_fset;
cMSFL_RECV_FSET g_msfl_recv_fset;
cMSFL_SEND_FSET g_msfl_send_fset;

sMBLK_COMM_QUANTUM::sMBLK_COMM_QUANTUM(
  const sHMBLK& h_mblk, 
  uint8_t offset,
  NEIGHBOR_MASK_INDEX nmi) :
  m_ptr(GPU::g_ublk_table.map_H2D(&h_mblk)),
  h_mblk(&h_mblk),
  m_offset(offset),
  m_nmi(nmi)
{
  cassert(m_ptr);
}
sMSFL_COMM_QUANTUM::sMSFL_COMM_QUANTUM(
  const sMSFL& h_msfl,
  uint8_t offset) :
  m_ptr(GPU::g_surfel_table.map_H2D(&h_msfl)),
  m_offset(offset)
{
  cassert(m_ptr);
}

template <typename TCpuGroup>
std::tuple<std::vector<sMBLK_COMM_QUANTUM>, GPU::Ptr<sMBLK_COMM_QUANTUM>, GPU::Ptr<size_t>, size_t>
constructGpuQuantumsFromCpu(
  const TCpuGroup& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
  const std::string& group_type)
{
  //Putting results in a flat CPU vector
  std::vector<sMBLK_COMM_QUANTUM> cpu_quantums;
  cpu_quantums.reserve(cpu_group.m_quantums.size());
  for (const sUBLK_COMM_QUANTUM& quantum : cpu_group.m_quantums) {
    auto [gpu_id, offset] = child_ublk_to_mblk_map.at(quantum.m_ublk->id());
    cpu_quantums.emplace_back(*mblks[gpu_id], offset, quantum.m_nmi);
    //LOG_MSG("STRAND_SWITCHING").printf( "GPU: CPU mblks has two copies %d", mblks[gpu_id]->has_two_copies());
  }

  //Uploading to GPU
  //TODO : device vector + in constructor
  auto quantums = GPU::malloc<sMBLK_COMM_QUANTUM>(cpu_quantums.size(), group_type + "_quantums");
  GPU::copy_h2d(quantums, cpu_quantums.data(), cpu_quantums.size());

  //Allocating offsets (no values)
  auto offsets = GPU::malloc<size_t>(cpu_quantums.size(), group_type + "_offsets");

  return std::move(std::make_tuple(cpu_quantums, quantums, offsets, cpu_quantums.size()));
}
//TODO: merge with previous function
template <typename TCpuGroup>
std::tuple<std::vector<sMSFL_COMM_QUANTUM>, GPU::Ptr<sMSFL_COMM_QUANTUM>, GPU::Ptr<size_t>, GPU::Ptr<size_t>, size_t>
constructGpuSurfelQuantumsFromCpu(
  const TCpuGroup& cpu_group, 
  const std::vector<sMSFL*>& megasurfels,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
  const std::string& group_type)
{
  //Putting results in a flat CPU vector
  std::vector<sMSFL_COMM_QUANTUM> cpu_quantums;
  cpu_quantums.reserve(cpu_group.quantums().size());
  for (const sSURFEL_COMM_QUANTUM& quantum : cpu_group.quantums()) {
    auto [gpu_id, offset] = child_sfl_to_msfl_map.at(quantum.m_surfel->id());
    cpu_quantums.emplace_back(*megasurfels[gpu_id], offset);
  }

  //Uploading to GPU
  //TODO : device vector + in constructor
  auto quantums = GPU::malloc<sMSFL_COMM_QUANTUM>(cpu_quantums.size(), group_type+"_quantums");
  GPU::copy_h2d(quantums, cpu_quantums.data(), cpu_quantums.size());

  //Allocating offsets (no values)
  auto offsets = GPU::malloc<size_t>(cpu_quantums.size(), group_type+"_offsets");
  auto offsets_init_info = GPU::malloc<size_t>(cpu_quantums.size(), group_type+"_offsets");

  return std::move(std::make_tuple(cpu_quantums, quantums, offsets, offsets_init_info, cpu_quantums.size()));
}


cMBLK_RECV_GROUP::cMBLK_RECV_GROUP(
  const sUBLK_RECV_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) :
  tSP_RECV_GROUP<cGpuExaMsg<sdFLOAT>>(cpu_group)
{
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: Creating receive group from SP %d", m_source_sp.nsp());
  std::tie(m_quantums_cpu, m_quantums, m_offsets, m_n_quantums) = constructGpuQuantumsFromCpu(cpu_group, mblks, child_ublk_to_mblk_map, "recv_group");
}
cMBLK_RECV_GROUP::~cMBLK_RECV_GROUP()
{
  //TODO
  // if(m_quantums)
  //   GPU::free(m_quantums, "recv_group_quantums");
  // if(m_offsets)
  //   GPU::free(m_offsets, "recv_group_offsets");
}

const sHMBLK* cMBLK_RECV_GROUP::find_mublk_in_quantum(STP_SHOB_ID id)
{
  for (auto& quantum : m_quantums_cpu)
  {
    if (quantum.cpu_mblk()->id() == id)
      return quantum.cpu_mblk();
  }
  return nullptr;
}

std::pair<size_t, std::vector<size_t>> computeOffsetsHelper(const std::vector<sMBLK_COMM_QUANTUM>& quantums, bool communicate_turb_data) {
  constexpr size_t header_size = 1; //one int32 to store the solver index mask
  constexpr size_t n_voxels_per_ublk = 8;
  constexpr size_t ublk_states_size = N_STATES;
  constexpr size_t ublk_visc_sij_size = 1;
  constexpr size_t ublk_lb_data_size = 1 + 1 + 3;
  constexpr size_t ublk_base_size = ublk_states_size + ublk_visc_sij_size + ublk_lb_data_size;
  constexpr size_t ublk_turb_size = 1 + 1 + 1 + 3 + 3 + 1 + 1;
  asINT32 per_quantum_sendsize = 0;
  asINT32 per_tpde_quantum_sendsize = 0;
  //CPU send size, as we are calculating this for CPU ublks
  sUBLK::add_send_size_for_farblk(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
  const size_t ublk_size = n_voxels_per_ublk * ublk_states_size + per_quantum_sendsize;
  //GPU::sUBLK::add_send_size_for_farblk(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
  std::vector<size_t> offsets(quantums.size());
  size_t current_offset = header_size;
  for(size_t i=0; i<quantums.size(); i++) {
    offsets[i] = current_offset;
    current_offset += ublk_size;
  }
  const size_t buffer_size = current_offset;
  return std::make_pair(buffer_size, std::move(offsets));
}

cFAR_MBLK_RECV_GROUP::cFAR_MBLK_RECV_GROUP(
  const sFARBLK_RECV_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
  bool communicate_turb_data) :
  cMBLK_RECV_GROUP(cpu_group, mblks, child_ublk_to_mblk_map),
  m_comm_turb_data(communicate_turb_data)
{
  const size_t buffer_size = computeOffsets();
  m_recv_msg.resize(buffer_size);
  m_recvsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating recv buffer for far mblks scale %d size %d from SP %d", m_scale, buffer_size, source_rank());
  m_recv_msg.copyRanksAndTag(cpu_group.m_recv_msg);
}

size_t cFAR_MBLK_RECV_GROUP::computeOffsets()
{
  const auto [buffer_size, cpu_offsets] = computeOffsetsHelper(m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  return buffer_size;
}

void cFAR_MBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}

//TODO: memcpy on the device does a byte-size for loop and is particularly slow, replace with either
//      - custom thread loop
//      - cudaMemcpyAsync() available in SM >=35
__global__ void unpack_kernel(ACTIVE_SOLVER_MASK active_solver_mask,
                              SOLVER_INDEX_MASK solver_ts_index_mask, 
                              const sdFLOAT*const buffer,
                              const size_t*const offsets,
                              sMBLK_COMM_QUANTUM* quantums,
                              size_t n_quantums,
                              int my_proc_id)
{
  const asINT32 timestep_index_lb_solver = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
  const asINT32 timestep_index_turb_solver = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
  const size_t linearIdx = blockIdx.x * blockDim.x + threadIdx.x;
  const size_t quantumIdx = linearIdx;
  if(quantumIdx < n_quantums)
  {
    sMBLK_COMM_QUANTUM& quantum = quantums[quantumIdx];
    GPU::sUBLK& mublk = quantum.mublk();
    auto lb_data_ptr = mublk.lb_data();
    const sdFLOAT* buf = buffer + offsets[quantumIdx];
    auto copy_from_buffer = [&buf](auto& ref_val, int ublk_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t ublk_data_size = sizeof(ref_val)/8; 
      std::byte* ptr = reinterpret_cast<std::byte*>(&ref_val);
      ptr += ublk_idx * ublk_data_size;
      memcpy(ptr, buf, ublk_data_size);
      buf += ublk_data_size/sizeof(sdFLOAT);
    };
    const int ublk_idx = quantum.offset();
    // {
    //   for(int voxel_idx=ublk_idx*8; voxel_idx<(ublk_idx+1)*8; voxel_idx++)
    //   {
    //     int x = ublk->centroids(voxel_idx,0);
    //     int y = ublk->centroids(voxel_idx,1);
    //     int z = ublk->centroids(voxel_idx,2);
    //     if(x == 275 && y == 125 && z == 28)
    //     {
    //       printf("")
    //     }
    //   }
    // }
    //group of 19*64 sdFLOAT pdfs, i.e. states
    //printf( "GPU: unpack far blk quantum id %d\n", quantumIdx);
    for(int i=0; i<19; i++)
    {
      copy_from_buffer(mublk.lb_states(ONLY_ONE_COPY)->m_states[i], ublk_idx);
    }
    //visc_sij from tUBLK_LB_DATA
    copy_from_buffer(lb_data_ptr->visc_sij, ublk_idx);
    //density, pressure, vel from tLB_DATA
    copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].density, ublk_idx);
    copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].pressure, ublk_idx);
    for(int i=0; i<3; i++)
      copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].vel[i], ublk_idx);
    if(is_solver_active(TURB_SOLVER, active_solver_mask)) {
      auto turb_data_ptr = mublk.turb_data();
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].eddy_visc, ublk_idx);
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_ke  , ublk_idx);
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_df  , ublk_idx);
      for(int i=0; i<3; i++) {
        copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_k[i], ublk_idx);
        copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_e[i], ublk_idx);
      }
      copy_from_buffer(turb_data_ptr->turb_str_mag, ublk_idx);
      copy_from_buffer(turb_data_ptr->gamma_swirl, ublk_idx);
    }
  }
}

template <typename TKernel, typename TQuantum>
void launch_packing_kernel(TKernel kernel,
  ACTIVE_SOLVER_MASK active_solver_mask,
  SOLVER_INDEX_MASK solver_ts_index_mask, 
  cGpuExaMsg<sdFLOAT>& send_msg,
  GPU::Ptr<size_t> offsets,
  GPU::Ptr<TQuantum> quantums,
  size_t n_quantums,
  int my_proc_id)
{
  constexpr size_t blockSize = 64;
  const size_t gridSize = (n_quantums + blockSize - 1) / blockSize;
  cudaDeviceSynchronize(); //TODO: only for debug, remove when working
  kernel<<<gridSize, blockSize, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(
   active_solver_mask, solver_ts_index_mask, send_msg.data().get(), offsets.get(), quantums.get(), n_quantums, my_proc_id);
  cudaDeviceSynchronize(); //TODO: only for debug, remove when working
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
}

template <class T_GROUP_TYPE>
const sHMBLK* find_megaublk_in_recv_group(T_GROUP_TYPE& recv_group, STP_SHOB_ID debug_mublk_id) 
{
  DO_SCALES_COARSE_TO_FINE(scale) 
  {
    for(auto& recv_groups : recv_group.m_sets[scale]) 
    {
      const sHMBLK* h_mblk = recv_groups->find_mublk_in_quantum(debug_mublk_id);
      if (h_mblk)
       return h_mblk;
    }
  }
  return nullptr;
}


void cFAR_MBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK /*solver_ts_index_mask*/)
{
  //fetch m_solver_timestep_index_mask from buffer (TODO: memcpy async)
  GPU::memcpy_d2h(&m_solver_timestep_index_mask, m_recv_msg.data(), sizeof(sdFLOAT));
  //unpack data from the buffer
  launch_packing_kernel(unpack_kernel, active_solver_mask, m_solver_timestep_index_mask, m_recv_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id);
}

void recv_gpu_buffer(int tag, cGpuExaMsg<sdFLOAT>& recv_msg,  cSTP_SCALE scale, STP_PROC source_rank, const std::string& log_msg) {
  recv_msg.set_nelems(recv_msg.size());
  recv_msg.settag(tag);
  if(recv_msg.m_request != MPI_REQUEST_NULL) 
    msg_internal_error("Receive request is not null at dest %d from source %d with tag %x ", recv_msg.dest(), recv_msg.source(), recv_msg.tag());
  int status = MPI_Irecv(recv_msg.data().get(), recv_msg.commsize(), MPI_BYTE, recv_msg.source(), recv_msg.tag(), g_exa_sp_cp_comm.communicator(), &recv_msg.m_request);
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: posting %s recv: source %d scale %d  tag %x, size %d", log_msg, source_rank, scale, recv_msg.tag(),  recv_msg.size());
  recv_msg.n_requests++;
  if (status != MPI_SUCCESS)
    g_exa_sp_cp_comm.error_handler(recv_msg, ( char*)"receiving GPU buffer");
}

void cFAR_MBLK_RECV_GROUP::post_recv()
{
  recv_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK>(m_scale), m_recv_msg, m_scale, source_rank(), "far mblk");

    #ifdef DEBUG_GPU
  int debug_mublk_id = 4;
  int child_ublk_index = 0;
  int state_index = 0;
  const sHMBLK* h_ublk = find_megaublk_in_recv_group<cFAR_MBLK_RECV_FSET>(g_far_mblk_recv_fset, debug_mublk_id);
  if (h_ublk)
  {
      //printf("h_ublk found in far recv group MPI(%d)\n", my_proc_id);
      std::ostringstream os;                                                                                                                                
      os << "\n";                                                                                                                                    
      print_mega_ublk(h_ublk, child_ublk_index, state_index, "Post Receive", os); 
      LOG_MSG("RECV_DEPEND") << os.str();
  }
  /*else
  {
      printf("h_ublk %d NOT found in far recv grou MPI(%d)\n", debug_mublk_id, my_proc_id);
  }*/


  DO_MBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, FRINGE_FARBLK_GROUP_TYPE)
  {
    int debug_mublk_id = (my_proc_id == 0) ? 0 : 4;
  //auto debug_group_type = FRINGE_FARBLK_GROUP_TYPE;//INTERIOR_FARBLK1_GROUP_TYPE;//FRINGE_FARBLK_GROUP_TYPE
  int child_ublk_index = (my_proc_id == 0) ? 0 : 0;
  int state_index = 0;
  sHMBLK* h_ublk = nullptr;

    h_ublk = find_megaublk_in_group(group, debug_mublk_id);
    if (h_ublk)
    {
      std::ostringstream os;                                                                                                                                
      os << "\n";                                                                                                                                    
      print_mega_ublk(h_ublk, child_ublk_index, state_index, "Local megaublocks after receive", os); 
      LOG_MSG("RECV_DEPEND") << os.str();
    }
    /*else
    {
      printf("h_ublk %d NOT found in MPI(%d)\n", debug_mublk_id, my_proc_id);
    }*/
  
  }
   #endif
}

cMBLK_SEND_GROUP::cMBLK_SEND_GROUP(
  const sUBLK_SEND_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map) :
  tSP_SEND_GROUP<cGpuExaMsg<sdFLOAT>>(cpu_group),
  m_is_near_surface(cpu_group.m_is_near_surface)
{
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: Creating send group");
  std::tie(m_quantums_cpu, m_quantums, m_offsets, m_n_quantums) = constructGpuQuantumsFromCpu(cpu_group, mblks, child_ublk_to_mblk_map, "send_group");
}
cMBLK_SEND_GROUP::~cMBLK_SEND_GROUP() {
  // printf("Call to ~cMBLK_SEND_GROUP::cMBLK_SEND_GROUP()\n");
  //TODO
  // if(m_quantums)
  //   GPU::free(m_quantums, "send_group_quantums");
  // if(m_offsets)
  //   GPU::free(m_offsets, "send_group_offsets");
}

cFAR_MBLK_SEND_GROUP::cFAR_MBLK_SEND_GROUP(
  const sFARBLK_SEND_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
  bool communicate_turb_data) :
  cMBLK_SEND_GROUP(cpu_group, mblks, child_ublk_to_mblk_map),
  m_comm_turb_data(communicate_turb_data)
{
  const size_t buffer_size = computeOffsets();
  m_send_msg.resize(buffer_size);
  m_sendsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating send buffer for far mblks scale %d size %d to SP %d", m_scale, buffer_size, dest_rank());
  m_send_msg.copyRanksAndTag(cpu_group.m_send_msg);
}

size_t cFAR_MBLK_SEND_GROUP::computeOffsets()
{
  const auto [buffer_size, cpu_offsets] = computeOffsetsHelper(m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  return buffer_size;
}

void cFAR_MBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  send(active_solver_mask,m_solver_timestep_index_mask);
}

__global__ void pack_kernel(ACTIVE_SOLVER_MASK active_solver_mask,
                            SOLVER_INDEX_MASK solver_ts_index_mask, 
                            sdFLOAT* buffer,
                            const size_t*const offsets,
                            const sMBLK_COMM_QUANTUM*const quantums,
                            size_t n_quantums,
                              int my_proc_id)
{  
  const asINT32 timestep_index_lb_solver = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
  const asINT32 timestep_index_turb_solver = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
  if(blockIdx.x==0 && threadIdx.x == 0) {
    typedef sFLOAT_TO_INT<sdFLOAT>::type asInt;
    asInt * const solver_index_mask = reinterpret_cast<asInt*>(buffer);
    *solver_index_mask = solver_ts_index_mask;
  }
  const size_t linearIdx = blockIdx.x * blockDim.x + threadIdx.x;
  const size_t quantumIdx = linearIdx;
  if(quantumIdx < n_quantums)
  {
    const sMBLK_COMM_QUANTUM& quantum = quantums[quantumIdx];
    const GPU::sUBLK& mublk = quantum.mublk();
    auto lb_data_ptr = mublk.lb_data();
    sdFLOAT* buf = buffer + offsets[quantumIdx];
    auto copy_to_buffer = [&buf](const auto& ref_val, int ublk_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t ublk_data_size = sizeof(ref_val)/8; 
      const std::byte* ptr = reinterpret_cast<const std::byte*>(&ref_val);
      ptr += ublk_idx * ublk_data_size;
      memcpy(buf, ptr, ublk_data_size);
      buf += ublk_data_size/sizeof(sdFLOAT);
    };
    //printf( "GPU: pack far blk quantum id %d\n", quantumIdx);
    const int ublk_idx = quantum.offset();
    //group of 19*64 sdFLOAT pdfs, i.e. states
    for(int i=0; i<19; i++)
    {
      asINT32 states_index = ONLY_ONE_COPY;
      if (mublk.has_two_copies())
      {        
        states_index = !(timestep_index_lb_solver ^ 1); //I think this should be the index for the previous timestep, current index negation
      }
      copy_to_buffer(mublk.lb_states(states_index)->m_states[i], ublk_idx);
    }
    //visc_sij from tUBLK_LB_DATA
    copy_to_buffer(lb_data_ptr->visc_sij, ublk_idx);
    //density, pressure, vel from tLB_DATA
    copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].density, ublk_idx);
    copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].pressure, ublk_idx);
    for(int i=0; i<3; i++)
      copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].vel[i], ublk_idx);
    if(is_solver_active(TURB_SOLVER, active_solver_mask)) {
      auto turb_data_ptr = mublk.turb_data();
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].eddy_visc, ublk_idx);
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_ke  , ublk_idx);
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_df  , ublk_idx);
      for(int i=0; i<3; i++) {
        copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_k[i], ublk_idx);
        copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_e[i], ublk_idx);
      }
      copy_to_buffer(turb_data_ptr->turb_str_mag, ublk_idx);
      copy_to_buffer(turb_data_ptr->gamma_swirl, ublk_idx);
    }
  }
}

//Send the a GPU buffer with MPI
void send_gpu_buffer(int tag, cGpuExaMsg<sdFLOAT>& send_msg, cSTP_SCALE scale, const std::string& log_msg) {
  send_msg.set_nelems(send_msg.size());
  send_msg.settag(tag);
  if(send_msg.m_request != MPI_REQUEST_NULL) 
    msg_internal_error("Send request is not null from source %d to dest %d with tag %x ", send_msg.source(), send_msg.dest(), send_msg.tag());
  int status = MPI_Isend(send_msg.data().get(), send_msg.commsize(), MPI_BYTE, send_msg.dest(), send_msg.tag(), g_exa_sp_cp_comm.communicator(), &send_msg.m_request);
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: posting %s send: dest %d scale %d  tag %x, size %d", log_msg, send_msg.dest(), scale, send_msg.tag(),  send_msg.size());
  send_msg.n_requests++;
  if (status != MPI_SUCCESS)
    g_exa_sp_cp_comm.error_handler(send_msg, ( char*)"sending GPU buffer");
}
void cFAR_MBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  // complete the previous send if any
  complete_send();
  //pack data into buffer
  launch_packing_kernel(pack_kernel, active_solver_mask, solver_ts_index_mask, m_send_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id);
  //Send the buffer with MPI
  send_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK>(m_scale), m_send_msg, m_scale, "far mblk");
  //Update mask
  m_solver_timestep_index_mask = solver_ts_index_mask^active_solver_mask;
}


cNEAR_MBLK_SEND_GROUP::cNEAR_MBLK_SEND_GROUP(
  const sNEARBLK_SEND_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
  bool communicate_turb_data) :
  cMBLK_SEND_GROUP(cpu_group, mblks, child_ublk_to_mblk_map),
  m_comm_turb_data(communicate_turb_data)
{
  const size_t buffer_size = computeOffsets();
  m_send_msg.resize(buffer_size);
  m_sendsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating send buffer for near mblks scale %d size %d to SP %d", m_scale, buffer_size, dest_rank());
  m_send_msg.copyRanksAndTag(cpu_group.m_send_msg);
}


std::pair<size_t, std::vector<size_t>> computeNearOffsetsHelper(const std::vector<sMBLK_COMM_QUANTUM>& quantums, bool communicate_turb_data) {
  static constexpr size_t header_size = 1; //one int32 to store the solver index mask
  static constexpr size_t n_ublk_per_quantum = 8;
  constexpr size_t ublk_states_size = N_STATES;

  asINT32 per_quantum_sendsize = 0;
  asINT32 per_tpde_quantum_sendsize = 0;
  //CPU send size, as we are calculating this for CPU ublks
  sUBLK::add_send_size_for_nearblk(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
  const size_t ublk_size = /*2**/ublk_states_size * n_ublk_per_quantum + per_quantum_sendsize; //TODO When we implement compression for GPU comm, we need to add nmi instead of 2*ublk_states_size
  // printf("Near block states size %d \n", ublk_states_size * n_ublk_per_quantum );
  // printf("Near block add send size %d \n", per_quantum_sendsize);

  // static_assert(sizeof(sTURB_DATA_8) == sizeof(sdFLOAT)*ulkb_turb_size);
  //const size_t quantum_size = ublk_size;
  std::vector<size_t> offsets(quantums.size());
  size_t current_offset = header_size;
  for(size_t i=0; i<quantums.size(); i++){
    offsets[i] = current_offset;
    current_offset += ublk_size;
  }
  const size_t buffer_size = current_offset;
  return std::make_pair(buffer_size, std::move(offsets));
}

size_t cNEAR_MBLK_SEND_GROUP::computeOffsets()
{
  const auto [buffer_size, cpu_offsets] = computeNearOffsetsHelper(m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  return buffer_size;
}

void cNEAR_MBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  send(active_solver_mask,m_solver_timestep_index_mask);
}

__global__ void pack_near_kernel(ACTIVE_SOLVER_MASK active_solver_mask,
                                 SOLVER_INDEX_MASK solver_ts_index_mask, 
                                 sdFLOAT* buffer,
                                 const size_t*const offsets,
                                 sMBLK_COMM_QUANTUM* quantums,
                                 size_t n_quantums,
                              int my_proc_id)
{
  
  const asINT32 timestep_index_lb_solver = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
  const asINT32 timestep_index_turb_solver = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
  if(blockIdx.x==0 && threadIdx.x == 0) {
    typedef sFLOAT_TO_INT<sdFLOAT>::type asInt;
    asInt * const solver_index_mask = reinterpret_cast<asInt*>(buffer);
    *solver_index_mask = solver_ts_index_mask;
  }
  const size_t linearIdx = blockIdx.x * blockDim.x + threadIdx.x;

  const size_t quantumIdx = linearIdx;
  if(quantumIdx < n_quantums)
  {
    sMBLK_COMM_QUANTUM& quantum = quantums[quantumIdx];
    GPU::sUBLK& mublk = quantum.mublk();
    //group of 2*19*64 sdFLOAT pdfs, i.e. states
    // print_size_as_warning<sizeof(lb_states)>()();
    sdFLOAT* buf = buffer + offsets[quantumIdx];
    auto copy_to_buffer = [&buf](const auto& ref_val, int ublk_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t ublk_data_size = sizeof(ref_val)/8; 
      const std::byte* ptr = reinterpret_cast<const std::byte*>(&ref_val);
      ptr += ublk_idx * ublk_data_size;
      memcpy(buf, ptr, ublk_data_size);
      buf += ublk_data_size/sizeof(sdFLOAT);
    };
    const int ublk_idx = quantum.offset();
    for(int i=0; i<19; i++)
    {
       sINT32 states_index = ONLY_ONE_COPY;
       if (mublk.has_two_copies())
       {        
         states_index = !(timestep_index_lb_solver ^ 1); //I think this should be the index for the previous timestep, current index negation
       }
       copy_to_buffer(mublk.lb_states(states_index)->m_states[i], ublk_idx);
    }
 
    if (is_solver_active(LB_SOLVER, active_solver_mask))
    {
      auto lb_data_ptr = mublk.lb_data();
      copy_to_buffer(lb_data_ptr->visc_sij, ublk_idx);
      //density, pressure, vel from tLB_DATA
      copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].density, ublk_idx);
      copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].pressure, ublk_idx);
      for(int i=0; i<3; i++)
        copy_to_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].vel[i], ublk_idx);
    }
    if(is_solver_active(TURB_SOLVER, active_solver_mask))  
    {      
      auto turb_data_ptr = mublk.turb_data();
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].eddy_visc, ublk_idx);
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_ke  , ublk_idx);
      copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_df  , ublk_idx);
      for(int i=0; i<3; i++) {
        copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_k[i], ublk_idx);
        copy_to_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_e[i], ublk_idx);
      }
      copy_to_buffer(turb_data_ptr->turb_str_mag, ublk_idx);
      copy_to_buffer(turb_data_ptr->gamma_swirl, ublk_idx);
    }
    //tNEARBLK_LB_DATA
    if (is_solver_active(LB_SOLVER, active_solver_mask)) 
    {
      auto surf_lb_data_ptr = mublk.surf_lb_data();
      copy_to_buffer(surf_lb_data_ptr->v2s_dist, ublk_idx);     
      #if BUILD_D19_LATTICE || BUILD_D39_LATTICE 
      copy_to_buffer(surf_lb_data_ptr->pre_cf_n, ublk_idx);
      copy_to_buffer(surf_lb_data_ptr->pre_delp_factor  , ublk_idx);
      #endif
    }
    //tNEARBLK_TURB_DATA
    if(is_solver_active(TURB_SOLVER, active_solver_mask))  
    {
        auto surf_turb_data_ptr = mublk.surf_turb_data(); 
        const size_t ublk_data_size = 8*sizeof(surf_turb_data_ptr->voxel_grads[0].gradp);
        memcpy(buf, &(surf_turb_data_ptr->voxel_grads[8*ublk_idx]), ublk_data_size);        
        buf += ublk_data_size/sizeof(sdFLOAT);
    }
  }
}
void cNEAR_MBLK_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask)
{
  // complete the previous send if any
  complete_send();
  //pack data into buffer
  launch_packing_kernel(pack_near_kernel, active_solver_mask, solver_ts_index_mask, m_send_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id); 

  //Send the buffer with MPI
  send_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK>(m_scale), m_send_msg, m_scale, "near mblk");
  //Update mask
  m_solver_timestep_index_mask = solver_ts_index_mask^active_solver_mask;
}


cNEAR_MBLK_RECV_GROUP::cNEAR_MBLK_RECV_GROUP(
  const sNEARBLK_RECV_GROUP& cpu_group, 
  const std::vector<sHMBLK*>& mblks,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_ublk_to_mblk_map,
  bool communicate_turb_data) :
  cMBLK_RECV_GROUP(cpu_group, mblks, child_ublk_to_mblk_map),
  m_comm_turb_data(communicate_turb_data)
{
  const size_t buffer_size = computeOffsets();
  m_recv_msg.resize(buffer_size);
  m_recvsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating recv buffer for near mblks scale %d size %d from SP %d", m_scale, buffer_size, source_rank());
  m_recv_msg.copyRanksAndTag(cpu_group.m_recv_msg);
}

size_t cNEAR_MBLK_RECV_GROUP::computeOffsets()
{
  const auto [buffer_size, cpu_offsets] = computeNearOffsetsHelper(m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  return buffer_size;
}

void cNEAR_MBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}

//TODO: memcpy on the device does a byte-size for loop and is particularly slow, replace with either
//      - custom thread loop
//      - cudaMemcpyAsync() available in SM >=35
__global__ void near_unpack_kernel(ACTIVE_SOLVER_MASK active_solver_mask,
                                   SOLVER_INDEX_MASK solver_ts_index_mask, 
                                   const sdFLOAT*const buffer,
                                   const size_t*const offsets,
                                   sMBLK_COMM_QUANTUM* quantums,
                                   size_t n_quantums,
                                   int my_proc_id)
{
  
  const asINT32 timestep_index_lb_solver = (solver_ts_index_mask & LB_ACTIVE) >> LB_SOLVER;
  const asINT32 timestep_index_turb_solver = (solver_ts_index_mask & KE_PDE_ACTIVE) >> TURB_SOLVER;
  const size_t linearIdx = blockIdx.x * blockDim.x + threadIdx.x;
  //TODO: 8 threads per mblk
  // const size_t mblkIdx = linearIdx / 8;
  // const size_t ublkIdx = linearIdx % 8;
  const size_t quantumIdx = linearIdx;
  if(quantumIdx < n_quantums)
  {
    sMBLK_COMM_QUANTUM& quantum = quantums[quantumIdx];
    GPU::sUBLK& mublk = quantum.mublk();
    //group of 2*19*64 sdFLOAT pdfs, i.e. states
    // print_size_as_warning<sizeof(lb_states)>()();
    const sdFLOAT* buf = buffer + offsets[quantumIdx];
    auto copy_from_buffer = [&buf](auto& ref_val, int ublk_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t ublk_data_size = sizeof(ref_val)/8; 
      std::byte* ptr = reinterpret_cast<std::byte*>(&ref_val);
      ptr += ublk_idx * ublk_data_size;
      memcpy(ptr, buf, ublk_data_size);
      buf += ublk_data_size/sizeof(sdFLOAT);
    };
    const int ublk_idx = quantum.offset();
    for(int i=0; i<19; i++)
    {
       sINT32 states_index = ONLY_ONE_COPY;
       if (mublk.is_vr_fine())
       {        
         states_index = !(timestep_index_lb_solver ^ 1); //I think this should be the index for the previous timestep, current index negation
       }
       copy_from_buffer(mublk.lb_states(states_index)->m_states[i], ublk_idx);
    }
  
    if (is_solver_active(LB_SOLVER, active_solver_mask))
    {
      auto lb_data_ptr = mublk.lb_data();
      copy_from_buffer(lb_data_ptr->visc_sij, ublk_idx);
      //density, pressure, vel from tLB_DATA
      copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].density, ublk_idx);
      copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].pressure, ublk_idx);
      for(int i=0; i<3; i++)
        copy_from_buffer(lb_data_ptr->m_lb_data[timestep_index_lb_solver].vel[i], ublk_idx);
    }
    if(is_solver_active(TURB_SOLVER, active_solver_mask))  
    {      
      auto turb_data_ptr = mublk.turb_data();
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].eddy_visc, ublk_idx);
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_ke  , ublk_idx);
      copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].turb_df  , ublk_idx);
      for(int i=0; i<3; i++) {
        copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_k[i], ublk_idx);
        copy_from_buffer(turb_data_ptr->m_turb_data[timestep_index_turb_solver].grad_e[i], ublk_idx);
      }
      copy_from_buffer(turb_data_ptr->turb_str_mag, ublk_idx);
      copy_from_buffer(turb_data_ptr->gamma_swirl, ublk_idx);
    }
    //tNEARBLK_LB_DATA
    if (is_solver_active(LB_SOLVER, active_solver_mask)) 
    {
      auto surf_lb_data_ptr = mublk.surf_lb_data();
      copy_from_buffer(surf_lb_data_ptr->v2s_dist, ublk_idx);     
      #if BUILD_D19_LATTICE || BUILD_D39_LATTICE 
      copy_from_buffer(surf_lb_data_ptr->pre_cf_n, ublk_idx);
      copy_from_buffer(surf_lb_data_ptr->pre_delp_factor  , ublk_idx);
      #endif
    }
    //tNEARBLK_TURB_DATA
    if(is_solver_active(TURB_SOLVER, active_solver_mask))  
    { 
      auto surf_turb_data_ptr = mublk.surf_turb_data(); 
      const size_t ublk_data_size = 8*sizeof(surf_turb_data_ptr->voxel_grads[0].gradp);
      memcpy(&(surf_turb_data_ptr->voxel_grads[8*ublk_idx]), buf, ublk_data_size);        
      buf += ublk_data_size/sizeof(sdFLOAT);
    }
  }
}

void cNEAR_MBLK_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask)
{
  //fetch m_solver_timestep_index_mask from buffer (TODO: memcpy async)
  GPU::memcpy_d2h(&m_solver_timestep_index_mask, m_recv_msg.data(), sizeof(sdFLOAT));
  //unpack data from the buffer
  launch_packing_kernel(near_unpack_kernel, active_solver_mask, m_solver_timestep_index_mask, m_recv_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id);
}

void cNEAR_MBLK_RECV_GROUP::post_recv()
{
  recv_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK>(m_scale), m_recv_msg, m_scale, source_rank(),"near mblk");
  #ifdef DEBUG_GPU
  int debug_mublk_id = 218;
  int child_ublk_index = 0;
  int state_index = 0;
  const sHMBLK* h_ublk = find_megaublk_in_recv_group<cNEAR_MBLK_RECV_FSET>(g_near_mblk_recv_fset, debug_mublk_id);
  if (h_ublk)
  {
      //printf("h_ublk found in far recv group MPI(%d)\n", my_proc_id);
      std::ostringstream os;                                                                                                                                
      os << "\n";                                                                                                                                    
      print_mega_ublk(h_ublk, child_ublk_index, state_index, "Post Receive", os); 
      LOG_MSG("RECV_DEPEND") << os.str();
  }
  /*else
  {
      printf("h_ublk %d NOT found in far recv grou MPI(%d)\n", debug_mublk_id, my_proc_id);
  }*/


  DO_MBLK_GROUPS_OF_SCALE_TYPE(group, m_scale, FRINGE_NEARBLK_GROUP_TYPE)
  {
    int debug_mublk_id = (my_proc_id == 0) ? 0 : 4;
  //auto debug_group_type = FRINGE_FARBLK_GROUP_TYPE;//INTERIOR_FARBLK1_GROUP_TYPE;//FRINGE_FARBLK_GROUP_TYPE
  int child_ublk_index = (my_proc_id == 0) ? 0 : 0;
  int state_index = 0;
  sHMBLK* h_ublk = nullptr;

    h_ublk = find_megaublk_in_group(group, debug_mublk_id);
    if (h_ublk)
    {
      std::ostringstream os;                                                                                                                                
      os << "\n";                                                                                                                                    
      print_mega_ublk(h_ublk, child_ublk_index, state_index, "Local near megaublocks after receive", os); 
      LOG_MSG("RECV_DEPEND") << os.str();
    }
    /*else
    {
      printf("h_ublk %d NOT found in MPI(%d)\n", debug_mublk_id, my_proc_id);
    }*/
  
  }
   #endif
}

  //Get CPU send size for 1 surfel
auto surfelCommSize() {
  asINT32 per_quantum_sendsize = 0;
  asINT32 per_tpde_quantum_sendsize = 0;
  asINT32 per_quantum_sendsize_with_lrf = 0;
  asINT32 per_tpde_quantum_sendsize_with_lrf = 0;
  sSURFEL::add_send_size_static_flow(sim.init_solver_mask, per_quantum_sendsize, per_tpde_quantum_sendsize);
  //lrf
  {
    per_quantum_sendsize_with_lrf = per_quantum_sendsize;
    per_tpde_quantum_sendsize_with_lrf = per_tpde_quantum_sendsize;
    sMSFL::sSURFEL_LRF_DATA::add_send_size(per_quantum_sendsize_with_lrf, per_tpde_quantum_sendsize_with_lrf);
  }
  return std::make_tuple(per_quantum_sendsize, per_quantum_sendsize_with_lrf);
}
template<typename CPU_COMM_GROUP>
std::tuple<size_t, std::vector<size_t>, std::vector<size_t>> computeSurfelOffsetsHelper(const CPU_COMM_GROUP& cpu_group,
                                                                                        const std::vector<sMSFL_COMM_QUANTUM>& quantums,
                                                                                        bool communicate_turb_data) {
  constexpr size_t header_size = 0; //TODO: check
  const auto [surfel_comm_size, surfel_comm_size_lrf] = surfelCommSize();
  assert(surfel_comm_size_lrf > surfel_comm_size);
  std::vector<size_t> offsets(quantums.size());
  size_t current_offset = header_size;
  for(size_t i=0; i<quantums.size(); i++) {
    offsets[i] = current_offset;
    current_offset += (cpu_group.quantums()[i].m_surfel->is_lrf() ? surfel_comm_size_lrf : surfel_comm_size);
  }
  asINT32 surfel_init_info_bytesize = sizeof(sSURFEL_INIT_INFO_SEND_ELEMENT)/* * quantums.size()*/;
  asINT32 surfel_init_info_sendsize = num_floats_from_num_bytes(surfel_init_info_bytesize) ;
  std::vector<size_t> offsets_init_info(quantums.size());
  size_t current_offset_init_info = 0;
  for(size_t i=0; i<quantums.size(); i++) {
    offsets_init_info[i] = current_offset_init_info;
    current_offset_init_info += surfel_init_info_sendsize;
  }
  
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: current_offset=%d, current_offset_init_info=%d surfel_comm_size=%d", current_offset, current_offset_init_info, surfel_comm_size);
  const size_t buffer_size = MAX(current_offset, current_offset_init_info); 

  return std::move(std::make_tuple(buffer_size, offsets, offsets_init_info));
}

cMSFL_SEND_GROUP::cMSFL_SEND_GROUP(
  const sSURFEL_SEND_GROUP& cpu_group,
  const std::vector<sMSFL*>& h_mega_surfels,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
  bool communicate_turb_data) :
  tSP_SEND_GROUP<cGpuExaMsg<sdFLOAT>>(cpu_group),
  m_comm_turb_data(communicate_turb_data)
{
  std::tie(m_quantums_cpu, m_quantums, m_offsets, m_offsets_init_info, m_n_quantums) = constructGpuSurfelQuantumsFromCpu(cpu_group, h_mega_surfels, child_sfl_to_msfl_map, "send_group");
  const auto [buffer_size, cpu_offsets, cpu_offsets_init_info] = computeSurfelOffsetsHelper(cpu_group, m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  GPU::copy_h2d(m_offsets_init_info, cpu_offsets_init_info.data(), cpu_offsets_init_info.size());
  m_send_msg.resize(buffer_size);
  m_sendsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating send buffer for surfels scale %d, n_surfels=%d, buffer_size=%d from SP %d", m_scale, cpu_group.quantums().size(), buffer_size, dest_rank());
  m_send_msg.copyRanksAndTag(cpu_group.m_send_msg);
}
cMSFL_SEND_GROUP::~cMSFL_SEND_GROUP() {
  // printf("Call to ~cMSFL_SEND_GROUP::cMSFL_SEND_GROUP()\n");
  //TODO
  // if(m_quantums)
  //   GPU::free(m_quantums, "send_group_quantums");
  // if(m_offsets)
  //   GPU::free(m_offsets, "send_group_offsets");
  // if(m_offsets_init_info)
  //   GPU::free(m_offsets_init_info, "send_group_offsets");
}

__host__ __device__ VOID print_debug_centroids(const char* msg, sMSFL* surfel, int s, int my_proc_id) {
  return;
  printf("MPI(%d): TS %d, %s, MSURFEL %d, scale %d, centroid %5.7e %5.7e %5.7e, has_v2s_data %d\n",
  my_proc_id,
          get_timescale_ref().m_time, msg, surfel->id(), surfel->scale(),
          surfel->centroid[0][s], surfel->centroid[1][s], surfel->centroid[2][s], surfel->has_v2s_data());
}
__global__ void pack_surfel_kernel(
  const ACTIVE_SOLVER_MASK active_solver_mask,
  const SOLVER_INDEX_MASK solver_ts_index_mask, 
  sdFLOAT*const buffer,
  const size_t*const offsets,
  sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums,
  int my_proc_id)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    sdFLOAT* buf = buffer + offsets[quantum_idx];
    auto copy_to_buffer = [&buf](const auto& ref_val, int sfl_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      const std::byte* ptr = reinterpret_cast<const std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(buf, ptr, sfl_data_size); //TODO: memcpy is a single float :(
      buf += sfl_data_size/sizeof(sdFLOAT);
    };
    if (is_solver_active(LB_SOLVER, active_solver_mask)) {
      const auto& lb_data = *msfl.lb_data();
      copy_to_buffer(lb_data.u_bar_ratio[0], sfl_idx);
      copy_to_buffer(lb_data.u_bar_ratio[1], sfl_idx);
      copy_to_buffer(lb_data.u_bar_ratio[2], sfl_idx);
      copy_to_buffer(lb_data.u_bar_ratio[3], sfl_idx);
      // print_size_as_warning<sizeof(STP_PHYS_VARIABLE)>()();
      // print_size_as_warning<sizeof(lb_data.u_bar_ratio)>()();
      copy_to_buffer(lb_data.ustar_0, sfl_idx);
      copy_to_buffer(lb_data.ustar_0_pair, sfl_idx);
      const auto& v2s_lb_data = *msfl.v2s_lb_data();
      for(int i=0; i<N_SURFEL_PGRAM_VOLUMES; i++)
        copy_to_buffer(v2s_lb_data.m_out_flux[i], sfl_idx);
      copy_to_buffer(v2s_lb_data.m_cf_n, sfl_idx);
      for(int i=0; i<N_SURFEL_PGRAM_VOLUMES; i++)
        copy_to_buffer(v2s_lb_data.m_u_bar[i], sfl_idx);
      copy_to_buffer(v2s_lb_data.m_delp_factor, sfl_idx);
    }
    if (is_solver_active(TURB_SOLVER, active_solver_mask)) 
    {     
      copy_to_buffer(msfl.turb_data()->ltt_index, sfl_idx);
      const auto& v2s_turb_data = *msfl.v2s_turb_data();
      copy_to_buffer(v2s_turb_data.m_nu, sfl_idx);
      copy_to_buffer(v2s_turb_data.m_tke_pde, sfl_idx);
      copy_to_buffer(v2s_turb_data.m_eps_pde, sfl_idx);
      if (msfl.is_inlet_or_outlet(sfl_idx)) // must be done per surfel
      {//BC turb data gets allocated for all the surfels even though they're not inlet or outlet
        const auto& dyn_turb_data = *msfl.dyn_turb_data();
        copy_to_buffer(dyn_turb_data.u.i.turb_intensity, sfl_idx);
        copy_to_buffer(dyn_turb_data.u.i.turb_length_scale, sfl_idx);
      }
    }

    if (msfl.is_lrf())
    {
      const auto& lrf_data = *msfl.lrf_data();
      copy_to_buffer(lrf_data.lrf_s2s_factor_pair, sfl_idx);
      copy_to_buffer(lrf_data.lrf_v2s_dist_pair, sfl_idx);      
      copy_to_buffer(lrf_data.lrf_v2s_scale_diff_pair, sfl_idx);
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_to_buffer(lrf_data.density_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        for(int j=0; j<N_AXES; j++) {
          copy_to_buffer(lrf_data.momentum_pair[i][j], sfl_idx);
        }
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_to_buffer(lrf_data.K_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_to_buffer(lrf_data.E_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_to_buffer(lrf_data.temp_sample_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_to_buffer(lrf_data.entropy_sample_pair[i], sfl_idx);
      }
      for(int s=0; s<MAX_N_USER_DEFINED_SCALARS; s++) { 
        for(int i=0; i<N_TIME_INDICES; i++) { //UDS is not supported yet in GPU
          copy_to_buffer(lrf_data.uds_value_pair[s][i], sfl_idx);
        }
      }
    }
    // if ((msfl.id() == 2) && sfl_idx == 30) 
    // if(quantum_idx==0)
    //   print_debug_info("Pack mega surfel", &msfl, sfl_idx, nullptr, active_solver_mask, true);
    // if(my_proc_id==0)
    // if(quantum_idx==0)
    // {
    //   for(int i=0; i < n_quantums; i++) {
    //     sMSFL_COMM_QUANTUM& quantum = quantums[i];
    //     GPU::sSURFEL& msfl = quantum.msfl();
    //     const int sfl_idx = quantum.offset();
    //     print_debug_centroids(" Pack mega surfel", &msfl, sfl_idx, my_proc_id);
    //   }
    // }
  }
}

void cMSFL_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask) {
  complete_send();
  launch_packing_kernel(pack_surfel_kernel, active_solver_mask, m_solver_timestep_index_mask, m_send_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id); 
  send_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL>(m_scale), m_send_msg, m_scale, "mega surfels");
  m_solver_timestep_index_mask ^= active_solver_mask;
}

__global__ void pack_surfel_bc_type_kernel(
  uint8_t*const buffer,
  const sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    const sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    const GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    uint8_t* buf = buffer + quantum_idx;
    assert(msfl.lb_data());
    *buf = msfl.lb_data()->boundary_condition_type[sfl_idx];
  }
}

void cMSFL_SEND_GROUP::send_bc_type() {
  complete_send();
  //launch packing kernel
  constexpr size_t blockSize = 64;
  const size_t gridSize = (m_n_quantums + blockSize - 1) / blockSize;
  pack_surfel_bc_type_kernel<<<gridSize, blockSize, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(
   reinterpret_cast<uint8_t*>(m_send_msg.data().get()), m_quantums.get(), m_n_quantums);
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
  //TODO: only send a subset of msg (smaller size)
  send_gpu_buffer(eMPI_SURFEL_BCTYPE_TAG, m_send_msg, m_scale, "surfels bc types");
}

__global__ void pack_surfel_type_kernel(
  uint32_t*const buffer,
  const size_t*const offsets,
  const sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums)
{
    const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    const sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    const GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    uint32_t* buf = buffer + offsets[quantum_idx];

    auto copy_to_buffer = [&buf](const auto& ref_val, int sfl_idx) {
      //static_assert(sizeof(ref_val)==N_SFLS_PER_MSFL*sizeof(uint32_t));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      const std::byte* ptr = reinterpret_cast<const std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(buf, ptr, sfl_data_size); //TODO: memcpy is a single uint32_t :(
      buf += sfl_data_size/sizeof(uint32_t);
    };
    //TODO unpack_recv_surfel_type_buffer HAS AN UPGRADE SURFEL TYPE FUNCTION CHECK BC_TYPE TOO
    copy_to_buffer(msfl.m_surfel_attributes.m_surfel_type, sfl_idx);
    //copy_to_buffer(msfl.m_incoming_latvec_mask, sfl_idx);
  }
}

void cMSFL_SEND_GROUP::send_surfel_type()
{
  /*constexpr size_t blockSize = 64;
  const size_t gridSize = (m_n_quantums + blockSize - 1) / blockSize;
  pack_surfel_type_kernel<<<gridSize, blockSize, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(
   reinterpret_cast<uint32_t*>(m_send_msg.data().get()), m_offsets_init_info, m_quantums.get(), m_n_quantums);
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
  //TODO: only send a subset of msg (smaller size)
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: Sending surfels types");
  send_gpu_buffer(eMPI_SURFEL_PRELIM_TAG, m_send_msg, m_scale, "surfels types");*/
}

__global__ void pack_surfel_init_info_kernel(
  const ACTIVE_SOLVER_MASK active_solver_mask,
  const SOLVER_INDEX_MASK solver_ts_index_mask, 
  sdFLOAT*const buffer,
  const size_t*const offsets,
  sMSFL_COMM_QUANTUM* quantums,
  const size_t n_quantums,
  int my_proc_id)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    sdFLOAT* buf = buffer + offsets[quantum_idx];
    auto copy_to_buffer = [&buf](const auto& ref_val, int sfl_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      const std::byte* ptr = reinterpret_cast<const std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(buf, ptr, sfl_data_size); //TODO: memcpy is a single float :(
      buf += sfl_data_size/sizeof(sdFLOAT);
    };
  
    copy_to_buffer(msfl.normal[0], sfl_idx);
    copy_to_buffer(msfl.normal[1], sfl_idx);
    copy_to_buffer(msfl.normal[2], sfl_idx);

    if (is_solver_active(TURB_SOLVER, active_solver_mask)) 
    {
      if (msfl.is_inlet_or_outlet(sfl_idx)) //must be done per surfel
      {
        auto turb_data = msfl.dyn_turb_data();
        copy_to_buffer(turb_data->u.k.turb_kinetic_energy, sfl_idx); //it could also be turb_intensity as it is in an union...
        copy_to_buffer(turb_data->u.k.turb_dissipation, sfl_idx); //it could also be turb_length_scale as it is in an union...
      }
    }

    for (int i = 0; i < N_SURFEL_PGRAM_VOLUMES; i++)
      copy_to_buffer(msfl.in_states_voxel_weight[i], sfl_idx);
    for (int i = 0; i < N_SURFEL_PGRAM_VOLUMES; i++)
      copy_to_buffer(msfl.in_states_voxel_weight2[i], sfl_idx);

    /*const auto& lrf_data = *msfl.lrf_data();
    copy_to_buffer(lrf_data.lrf_v2s_scale_diff, sfl_idx);*/
    }
}

void cMSFL_SEND_GROUP::send_init_info(ACTIVE_SOLVER_MASK active_solver_mask)
{
  complete_send();
  launch_packing_kernel(pack_surfel_init_info_kernel, active_solver_mask, m_solver_timestep_index_mask, m_send_msg, m_offsets_init_info, m_quantums, m_n_quantums, my_proc_id); 
  send_gpu_buffer(eMPI_SURFEL_PRELIM_TAG, m_send_msg, m_scale, "mega surfels init info");
}

cMSFL_RECV_GROUP::cMSFL_RECV_GROUP(
  const sSURFEL_RECV_GROUP& cpu_group,
  const std::vector<sMSFL*>& h_mega_surfels,
  const std::unordered_map<SHOB_ID, std::pair<SHOB_ID, uINT8>>& child_sfl_to_msfl_map,
  bool communicate_turb_data) :
  tSP_RECV_GROUP<cGpuExaMsg<sdFLOAT>>(cpu_group),
  m_comm_turb_data(communicate_turb_data)
{
  std::tie(m_quantums_cpu, m_quantums, m_offsets, m_offsets_init_info, m_n_quantums) = constructGpuSurfelQuantumsFromCpu(cpu_group, h_mega_surfels, child_sfl_to_msfl_map, "recv_group");
  const auto [buffer_size, cpu_offsets, cpu_offsets_init_info] = computeSurfelOffsetsHelper(cpu_group, m_quantums_cpu, m_comm_turb_data);
  GPU::copy_h2d(m_offsets, cpu_offsets.data(), cpu_offsets.size());
  GPU::copy_h2d(m_offsets_init_info, cpu_offsets_init_info.data(), cpu_offsets_init_info.size());
  m_recv_msg.resize(buffer_size);
  m_recvsize = buffer_size;
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: allocating recv buffer for surfels scale %d, n_surfels=%d, buffer_size=%d from SP %d", m_scale, cpu_group.quantums().size(), buffer_size, source_rank());
  m_recv_msg.copyRanksAndTag(cpu_group.m_recv_msg);
}

cMSFL_RECV_GROUP::~cMSFL_RECV_GROUP() {
  // printf("Call to ~cMSFL_RECV_GROUP::cMSFL_RECV_GROUP()\n");
  //TODO
  // if(m_quantums)
  //   GPU::free(m_quantums, "recv_group_quantums");
  // if(m_offsets)
  //   GPU::free(m_offsets, "recv_group_offsets");
  // if(m_offsets_init_info)
  //   GPU::free(m_offsets_init_info, "recv_group_offsets");
}
void cMSFL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask) {
  unpack(active_solver_mask, m_solver_timestep_index_mask);
  set_unpacked();
}


   
__global__ void unpack_surfel_kernel(
  const ACTIVE_SOLVER_MASK active_solver_mask,
  const SOLVER_INDEX_MASK solver_ts_index_mask, 
  const sdFLOAT*const buffer,
  const size_t*const offsets,
  sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums,
  int my_proc_id)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    const sdFLOAT* buf = buffer + offsets[quantum_idx];
    auto copy_from_buffer = [&buf](auto& ref_val, int sfl_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      std::byte* ptr = reinterpret_cast<std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(ptr, buf, sfl_data_size);//TODO: memcpy is a single float :(
      buf += sfl_data_size/sizeof(sdFLOAT);
    };
    if (is_solver_active(LB_SOLVER, active_solver_mask)) {
      auto& lb_data = *msfl.lb_data();
      copy_from_buffer(lb_data.u_bar_ratio[0], sfl_idx);
      copy_from_buffer(lb_data.u_bar_ratio[1], sfl_idx);
      copy_from_buffer(lb_data.u_bar_ratio[2], sfl_idx);
      copy_from_buffer(lb_data.u_bar_ratio[3], sfl_idx);
      copy_from_buffer(lb_data.ustar_0, sfl_idx);
      copy_from_buffer(lb_data.ustar_0_pair, sfl_idx);
      auto& v2s_lb_data = *msfl.v2s_lb_data();
      for(int i=0; i<N_SURFEL_PGRAM_VOLUMES; i++)
        copy_from_buffer(v2s_lb_data.m_out_flux[i], sfl_idx);
      copy_from_buffer(v2s_lb_data.m_cf_n, sfl_idx);
      for(int i=0; i<N_SURFEL_PGRAM_VOLUMES; i++)
        copy_from_buffer(v2s_lb_data.m_u_bar[i], sfl_idx);
      copy_from_buffer(v2s_lb_data.m_delp_factor, sfl_idx);
    }
    if (is_solver_active(TURB_SOLVER, active_solver_mask)) 
    {     
      copy_from_buffer(msfl.turb_data()->ltt_index, sfl_idx);
      auto& v2s_turb_data = *msfl.v2s_turb_data();
      copy_from_buffer(v2s_turb_data.m_nu, sfl_idx);
      copy_from_buffer(v2s_turb_data.m_tke_pde, sfl_idx);
      copy_from_buffer(v2s_turb_data.m_eps_pde, sfl_idx);
      if (msfl.is_inlet_or_outlet(sfl_idx)) 
      {//BC turb data gets allocated for all the surfels even though they're not inlet or outlet
        auto& dyn_turb_data = *msfl.dyn_turb_data();
        copy_from_buffer(dyn_turb_data.u.i.turb_intensity, sfl_idx);
        copy_from_buffer(dyn_turb_data.u.i.turb_length_scale, sfl_idx);
      }
    }

    if (msfl.is_lrf())
    {
      auto& lrf_data = *msfl.lrf_data();
      copy_from_buffer(lrf_data.lrf_s2s_factor_pair, sfl_idx);
      copy_from_buffer(lrf_data.lrf_v2s_dist_pair, sfl_idx);
      copy_from_buffer(lrf_data.lrf_v2s_scale_diff_pair, sfl_idx);
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_from_buffer(lrf_data.density_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        for(int j=0; j<N_AXES; j++) {
          copy_from_buffer(lrf_data.momentum_pair[i][j], sfl_idx);
        }
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_from_buffer(lrf_data.K_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_from_buffer(lrf_data.E_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_from_buffer(lrf_data.temp_sample_pair[i], sfl_idx);
      }
      for(int i=0; i<N_TIME_INDICES; i++) {
        copy_from_buffer(lrf_data.entropy_sample_pair[i], sfl_idx);
      }
      for(int s=0; s<MAX_N_USER_DEFINED_SCALARS; s++) { 
        for(int i=0; i<N_TIME_INDICES; i++) {  //UDS is not supported yet
          copy_from_buffer(lrf_data.uds_value_pair[s][i], sfl_idx);
        }
      }
    }
    // if ((msfl.id() == 38) && sfl_idx == 30) 
    // if(quantum_idx==0)
    //   print_debug_info("Unpack mega surfel", &msfl, sfl_idx, nullptr, active_solver_mask, true);
    // if(my_proc_id==1)
    // if(quantum_idx==0)
    // {
    //   for(int i=0; i < n_quantums; i++) {
    //     sMSFL_COMM_QUANTUM& quantum = quantums[i];
    //     GPU::sSURFEL& msfl = quantum.msfl();
    //     const int sfl_idx = quantum.offset();
    //     print_debug_centroids(" Unpack mega surfel", &msfl, sfl_idx, my_proc_id);
    //   }
    // }
  }
}
void cMSFL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask,  SOLVER_INDEX_MASK solver_ts_index_mask) {
  launch_packing_kernel(unpack_surfel_kernel, active_solver_mask, solver_ts_index_mask, m_recv_msg, m_offsets, m_quantums, m_n_quantums, my_proc_id); 
}


void cMSFL_RECV_GROUP::post_recv() {
  recv_gpu_buffer(make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL>(m_scale), m_recv_msg, m_scale, source_rank(),"mega surfel");
}

void cMSFL_RECV_GROUP::post_bc_type_recv() {
  //TODO: reduce recv size to match send size
  recv_gpu_buffer(eMPI_SURFEL_BCTYPE_TAG, m_recv_msg, m_scale, source_rank(),"mega surfel bc type");
}

void cMSFL_RECV_GROUP::post_init_info_recv() {
  //TODO: reduce recv size to match send size
  recv_gpu_buffer(eMPI_SURFEL_PRELIM_TAG, m_recv_msg, m_scale, source_rank(),"mega surfel init info");
}

__global__ void unpack_surfel_bc_type_kernel(
  const uint8_t*const buffer,
  sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    const uint8_t*const buf = buffer + quantum_idx;
    assert(msfl.lb_data());
    msfl.lb_data()->boundary_condition_type[sfl_idx] = *buf;
  }
}
void cMSFL_RECV_GROUP::unpack_bc_types() {
  //launch unpacking kernel
  constexpr size_t blockSize = 64;
  const size_t gridSize = (m_n_quantums + blockSize - 1) / blockSize;
  unpack_surfel_bc_type_kernel<<<gridSize, blockSize, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(
   reinterpret_cast<uint8_t*>(m_recv_msg.data().get()), m_quantums.get(), m_n_quantums);
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
}

__global__ void unpack_surfel_type_kernel(
  uint32_t*const buffer,
  const size_t*const offsets,
  const sMSFL_COMM_QUANTUM*const quantums,
  const size_t n_quantums)
{
  /*  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    const sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    const GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    uint32_t* buf = buffer + offsets[quantum_idx];

    auto copy_from_buffer = [&buf](auto& ref_val, int sfl_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(uint32_t));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      std::byte* ptr = reinterpret_cast<std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(ptr, buf, sfl_data_size);//TODO: memcpy is a single float :(
      buf += sfl_data_size/sizeof(uint32_t);
    };

    copy_from_buffer(msfl.m_surfel_attributes.m_surfel_type, sfl_idx);
    copy_from_buffer(msfl.m_incoming_latvec_mask, sfl_idx);
  }*/
}

void cMSFL_RECV_GROUP::unpack_surfel_type()
{
  /*constexpr size_t blockSize = 64;
  const size_t gridSize = (m_n_quantums + blockSize - 1) / blockSize;
  unpack_surfel_type_kernel<<<gridSize, blockSize, GPU::NO_DYN_SHMEM, GPU::g_stream>>>(
   reinterpret_cast<uint32_t*>(m_send_msg.data().get()), m_offsets_init_info, m_quantums.get(), m_n_quantums);
  checkCudaErrors( cudaPeekAtLastError() );
  checkCudaErrors( cudaStreamSynchronize(GPU::g_stream) );
  //TODO: only send a subset of msg (smaller size)
  LOG_MSG("STRAND_SWITCHING").printf( "GPU: Receiving surfels types");
  send_gpu_buffer(eMPI_SURFEL_BCTYPE_TAG, m_send_msg, m_scale, "surfels types");*/
}

__global__ void unpack_surfel_init_info_kernel(
  const ACTIVE_SOLVER_MASK active_solver_mask,
  const SOLVER_INDEX_MASK solver_ts_index_mask, 
  sdFLOAT*const buffer,
  const size_t*const offsets,
  sMSFL_COMM_QUANTUM* quantums,
  const size_t n_quantums,
  int my_proc_id)
{
  const size_t quantum_idx = blockIdx.x * blockDim.x + threadIdx.x;
  if(quantum_idx < n_quantums) {
    sMSFL_COMM_QUANTUM& quantum = quantums[quantum_idx];
    GPU::sSURFEL& msfl = quantum.msfl();
    const int sfl_idx = quantum.offset();
    sdFLOAT* buf = buffer + offsets[quantum_idx];

    auto copy_from_buffer = [&buf](auto& ref_val, int sfl_idx) {
      static_assert(sizeof(ref_val)==64*sizeof(sdFLOAT));
      size_t sfl_data_size = sizeof(ref_val)/N_SFLS_PER_MSFL; 
      std::byte* ptr = reinterpret_cast<std::byte*>(&ref_val);
      ptr += sfl_idx * sfl_data_size;
      memcpy(ptr, buf, sfl_data_size);//TODO: memcpy is a single float :(
      buf += sfl_data_size/sizeof(sdFLOAT);
    };
  
    copy_from_buffer(msfl.normal[0], sfl_idx);
    copy_from_buffer(msfl.normal[1], sfl_idx);
    copy_from_buffer(msfl.normal[2], sfl_idx);
    
    if (is_solver_active(TURB_SOLVER, active_solver_mask)) 
    {
      if (msfl.is_inlet_or_outlet(sfl_idx)) 
      {
        auto turb_data = msfl.dyn_turb_data();
        copy_from_buffer(turb_data->u.k.turb_kinetic_energy, sfl_idx); //it could also be turb_intensity as it is in an union...
        copy_from_buffer(turb_data->u.k.turb_dissipation, sfl_idx); //it could also be turb_length_scale as it is in an union...
      }
    }

    for (int i = 0; i < N_SURFEL_PGRAM_VOLUMES; i++)
      copy_from_buffer(msfl.in_states_voxel_weight[i], sfl_idx);
    for (int i = 0; i < N_SURFEL_PGRAM_VOLUMES; i++)
      copy_from_buffer(msfl.in_states_voxel_weight2[i], sfl_idx);

    /*auto lrf_data = msfl.lrf_data();
    copy_from_buffer(lrf_data->lrf_v2s_scale_diff, sfl_idx);*/
  }
}

void cMSFL_RECV_GROUP::unpack_init_info(ACTIVE_SOLVER_MASK active_solver_mask) {
  launch_packing_kernel(unpack_surfel_init_info_kernel, active_solver_mask, 0, m_recv_msg, m_offsets_init_info, m_quantums, m_n_quantums, my_proc_id); 
}

namespace GPU
{
void do_send_surfel_bc_types()
{
  auto& sfl_recv_fset = g_msfl_recv_fset;
  for( SCALE scale = 0; scale <sim.num_scales; scale++) {
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale])
      recv_group->post_bc_type_recv();
    for(auto& send_group : g_msfl_send_fset.m_sets[scale])
      send_group->send_bc_type();
    for(auto& recv_group : g_msfl_recv_fset.m_sets[scale]) {
      recv_group->complete_recv();
      recv_group->unpack_bc_types();
    }
  }
}
}
