#ifndef SIMENG_GPU_GATHER_ADVECT_H
#define SIMENG_GPU_GATHER_ADVECT_H

#if BUILD_GPU
namespace GPU {

  
  VOID advect_interactions(UBLK_GROUP_TYPE group_type,
			                     BOOLEAN is_T_S_lb_solver_on,
			                     const size_t* range,
                           BOOLEAN is_timestep_even,
			                     SOLVER_INDEX_MASK prior_solver_index_mask,
			                     ACTIVE_SOLVER_MASK active_solver_mask);

  VOID advect_mega_ublk(UBLK_GROUP_TYPE group_type,
			                  BOOLEAN is_T_S_lb_solver_on,
			                  const size_t* range,
			                  SOLVER_INDEX_MASK prior_solver_index_mask);

}
#endif

#endif
