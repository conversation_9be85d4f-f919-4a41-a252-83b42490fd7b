/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                      Created Tue, Mar 3, 2009
//----------------------------------------------------------------------------
#ifndef _SIMENG_BSURFEL_TABLE_H_
#define _SIMENG_BSURFEL_TABLE_H_

#include "common_sp.h"
#include "bsurfel.h"
#include VMEM_VECTOR_H

//----------------------------------------------------------------------------
// BSURFEL_TABLE
//----------------------------------------------------------------------------
typedef struct sBSURFEL_TABLE {
private:
  typedef VMEM_VECTOR< SHOB >::iterator ITERATOR;

public:
  sBSURFEL*         bsurfel_from_id(SHOB_ID id);
  SHOB_ID         n_bsurfels() { return m_bsurfels.size(); }

  VOID    reserve(SHOB_ID n_bsurfels) { m_bsurfels.reserve(n_bsurfels); }
  VOID    trim()                      { m_bsurfels.trim(); }

  VOID add(SHOB bsurfel);

  VOID sort();

protected:
  VMEM_VECTOR< SHOB > m_bsurfels;

}* BSURFEL_TABLE;

#endif /* _SIMENG_BSURFEL_TABLE_H_ */
