/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Main Simulation Loop
 *
 * Sam Watson, Exa Corporation 
 * Created Tues March 17 2015
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_THREAD_RUN_H
#define _SIMENG_THREAD_RUN_H

#include "common_sp.h"
#include "sim.h"
#include "event_queue.h"
#include "sleep.h"

extern cEXA_MPI_COMM g_exa_sp_cp_comm;

extern sEVENT_QUEUE g_async_event_queue;

// TODO: this should never be called, currently it is only used by particle modeling
inline VOID complete_mpi_request(MPI_Request *request, SP_SLEEP_INDEX sleepIndex) {
  int flag = 0;
  MPI_Status status;
  MPI_Test(request,&flag,&status);
  while(!flag) {
    simerr_mpi_process();
    sp_thread_sleep(sleepIndex);
    MPI_Test(request,&flag,&status);
  }
}
inline VOID complete_mpi_request_while_processing_cp_messages(cExaMsgBase &msg, SP_SLEEP_INDEX sleepIndex) {
  g_exa_sp_cp_comm.test(msg);
  while(!msg.m_flag) {
    simerr_mpi_process();
    sp_thread_sleep(sleepIndex);
    g_exa_sp_cp_comm.test(msg);
  }
}

// TODO: This should never be called. Currently it is still used by bsurfels, particle modeling, fan, etc.
inline VOID complete_mpi_request_while_processing_cp_messages(MPI_Request *request, SP_SLEEP_INDEX sleepIndex) {
  int flag = 0;
  MPI_Status status;
  MPI_Test(request, &flag, &status);
  while(!flag) {
    simerr_mpi_process();
    sp_thread_sleep(sleepIndex);
    MPI_Test(request, &flag, &status);
  }
}
/* Runs the current simulation */
VOID sim_run();
BOOLEAN consider_surrender(asINT32 strand_index);
VOID process_full_ckpt_sim_thread();
VOID process_mme_ckpt_sim_thread();
VOID process_avg_mme_ckpt_sim_thread();
VOID process_checkpoints_comm_thread();
VOID fp_exception_handler(unsigned parameters[5], int values[2]);

#endif /* #ifndef _SIMENG_THREAD_RUN_H */

