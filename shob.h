/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------
 * Shared objects
 * James Hoch, Exa Corporation
 * Created Fri Nov 7, 1997
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_SHOB_H
#define _SIMENG_SHOB_H

#include "common_sp.h"
#include "vectorization_support.h"
#include "lattice.h"


//------------------------------------------------------------------------------
// SHOB IDs
//------------------------------------------------------------------------------
typedef STP_SHOB_ID    SHOB_ID;
typedef STP_SURFEL_ID  SURFEL_ID;
typedef STP_BSURFEL_ID BSURFEL_ID;
typedef STP_UBLK_ID    UBLK_ID;

inline constexpr   SHOB_ID    INVALID_SHOB_ID  = STP_INVALID_SHOB_ID;


//------------------------------------------------------------------------------
// SHOB types
//------------------------------------------------------------------------------
enum SHOB_TYPE_ENUM {
 INVALID_SHOB_TYPE      = -1,
 FARBLK_TYPE            = STP_FARBLK_TYPE_ID,
 PDE_ADVECT_FARBLK_TYPE = STP_PDE_ADVECT_FARBLK_TYPE_ID,
 NEARBLK_TYPE           = STP_NEARBLK_TYPE_ID,
 GHOSTBLK_TYPE          = STP_GHOSTBLK_TYPE_ID,
 VRBLK_TYPE             = STP_VRBLK_TYPE_ID,
 MIRROR_UBLK_TYPE       = STP_MIRROR_UBLK_TYPE_ID,
 SURFEL_TYPE            = STP_SURFEL_TYPE_ID,
 DYN_SURFEL_TYPE        = STP_SURFEL_TYPE_ID, // Better name for SURFEL_TYPE
 GHOST_SURFEL_TYPE      = STP_GHOST_SURFEL_TYPE_ID,
 MIRROR_SURFEL_TYPE     = STP_MIRROR_SURFEL_TYPE_ID,
 SAMPLING_SURFEL_TYPE   = STP_SAMPLING_SURFEL_TYPE_ID,
 BSURFEL_TYPE         = STP_BSURFEL_TYPE_ID,

 GHOST_SAMPLING_SURFEL_TYPE  = STP_GHOST_SAMPLING_SURFEL_TYPE_ID,
 FILM_ONLY_GHOST_SURFEL_TYPE = STP_FILM_ONLY_GHOST_SURFEL_TYPE_ID,
 N_SHOB_TYPES
};

extern const char* g_shob_type_names[N_SHOB_TYPES];

// @@@ We should be using the enum type in our code, not the integer type
typedef STP_SHOB_TYPE SHOB_TYPE;

inline BOOLEAN shob_type_is_ghost(SHOB_TYPE type)
{ return (type == GHOSTBLK_TYPE || type == GHOST_SURFEL_TYPE ||
          type == GHOST_SAMPLING_SURFEL_TYPE); }

inline BOOLEAN shob_type_is_ghost_ublk(SHOB_TYPE type)
{ return type == GHOSTBLK_TYPE; }

inline BOOLEAN shob_type_is_dynblk(SHOB_TYPE type)
{ return type <= NEARBLK_TYPE; }

inline BOOLEAN shob_type_is_ublk(SHOB_TYPE type)
{ return type <= MIRROR_UBLK_TYPE; }

inline BOOLEAN shob_type_is_surfel(SHOB_TYPE type)
{ return (type >= SURFEL_TYPE && type < BSURFEL_TYPE) ||
         (type == GHOST_SAMPLING_SURFEL_TYPE)  ; }

inline BOOLEAN shob_type_is_ghost_surfel(SHOB_TYPE type)
{ return (type == GHOST_SURFEL_TYPE ||
          type == GHOST_SAMPLING_SURFEL_TYPE); }

inline BOOLEAN shob_type_is_bsurfel(SHOB_TYPE type)
{ return type == BSURFEL_TYPE; }


// Macros for determining a shob's type from a shob pointer
#define shob_is_farblk(shob)	      ((shob)->type() == FARBLK_TYPE)
#define shob_is_nearblk(shob)	      ((shob)->type() == NEARBLK_TYPE)
#define shob_is_ghostblk(shob)        ((shob)->type() == GHOSTBLK_TYPE)
#define shob_is_vrblk(shob)           ((shob)->type() == VRBLK_TYPE)
#define shob_is_mirror_ublk(shob)     ((shob)->type() == MIRROR_UBLK_TYPE)
#define shob_is_dynamics_surfel(shob) ((shob)->type() == SURFEL_TYPE)
#define shob_is_ghost_surfel(shob)    ((shob)->type() == GHOST_SURFEL_TYPE)
#define shob_is_mirror_surfel(shob)   ((shob)->type() == MIRROR_SURFEL_TYPE)
#define shob_is_sampling_surfel(shob) ((shob)->type() == SAMPLING_SURFEL_TYPE)
#define shob_is_bsurfel(shob)         ((shob)->type() == BSURFEL_TYPE)

#ifdef ENABLE_PARTICLE_SUSPENSION_DYNAMICS
#define shob_is_film_only_ghost_surfel(shob) ((shob)->type() == FILM_ONLY_GHOST_SURFEL_TYPE)
#endif

constexpr static sINT8 DEFAULT_REF_FRAME_INDEX = -1;
//------------------------------------------------------------------------------
// sSHOB : Base class for shared objects (ublks and surfels)
//------------------------------------------------------------------------------
typedef class sSHOB
{
public:
  __HOST__DEVICE__ sSHOB() : m_id(0), m_scale(0), m_ref_frame_index(DEFAULT_REF_FRAME_INDEX) { } // constructor
  sSHOB(const sSHOB& other) = default;
  sSHOB& operator=(const sSHOB& other) = default;
  __HOST__DEVICE__ SHOB_ID   id() const  { return m_id; }
  __HOST__DEVICE__ asINT32   scale() const  { return m_scale; }
  __HOST__DEVICE__ asINT32  ref_frame_index() const { return m_ref_frame_index; }  
  VOID set_scale(sINT32 scale) { m_scale = scale; }
  VOID set_id(SHOB_ID id) { m_id = id; }
  VOID set_ref_frame_index(asINT32 rf_index) { m_ref_frame_index = rf_index; }

  // shobs must be at least aligned on a multiple of 8 so that we can encode
  // data in the low 3 bits of a shob pointer.
  static asINT32 alignment() { return 8; }

protected:
  SHOB_ID   m_id;
  sINT8     m_scale;
  sINT8     m_ref_frame_index;
} *SHOB;

//----------------------------------------------------------------------------
// SHOB_ID_ORDER
//----------------------------------------------------------------------------
struct SHOB_ID_ORDER
{
  BOOLEAN operator()(SHOB a, SHOB b)               { return (    a->id() < b->id()   );} // for sorting
  BOOLEAN operator()(SHOB shob, SHOB_ID target_id) { return ( shob->id() < target_id );} // for lower_bound binary search
};


template<typename SHOB_TYPE>
struct tSHOB_SIZES_TABLE {

  using SHOB_SIZE_PAIR = std::pair<SHOB_TYPE*, size_t>;
  
  tSHOB_SIZES_TABLE():m_current_capacity(m_increment),
                      m_count(0),
                      m_sizes(m_increment, std::make_pair(nullptr, 0))
  {
  }

  VOID raise_capacity(size_t shob_id) {
    size_t n = shob_id / m_increment;    
    m_sizes.resize(2*n*m_increment);
  }

  VOID add(SHOB_TYPE* shob, size_t n_bytes) {

    if (shob->id() >= m_current_capacity) {
      raise_capacity(shob->id());
    }
    
    auto& size_info = m_sizes[shob->id()];
    size_info.first = shob;
    size_info.second = n_bytes;
    m_count++;
  }

  VOID replace(SHOB_TYPE* shob, size_t n_bytes) {

    assert(shob->id() < m_count);
    auto& size_info = m_sizes[shob->id()];
    size_info.first = shob;
    size_info.second = n_bytes;
    
  }  

  size_t size() const { return m_count; }

  SHOB_SIZE_PAIR& operator[] (SHOB_ID id) {
    return m_sizes[id];
  }

  const SHOB_SIZE_PAIR& operator[] (SHOB_ID id) const {
    return m_sizes[id];
  }  
  
private:
  size_t m_current_capacity;
  size_t m_count;
  constexpr static size_t m_increment = 256;
  std::vector<std::pair<SHOB_TYPE*, size_t>> m_sizes;
};

//--------------------------------------------------------------------------
// BOUNDARY_CONDITION_TYPES
//--------------------------------------------------------------------------

// Note that the definition of is_bc_type_wall depends on BOUNDARY_CONDITION_NOSLIP_WALL
// being the last wall type, and on the fact that all the wall types come first.
enum BOUNDARY_CONDITION_TYPE {
  BOUNDARY_CONDITION_SLIP_WALL,
  BOUNDARY_CONDITION_LINEAR_SLIP_WALL,
  BOUNDARY_CONDITION_VEL_SLIP_WALL,
  BOUNDARY_CONDITION_ANGULAR_SLIP_WALL,
  BOUNDARY_CONDITION_FREE_SLIP_WALL,
  BOUNDARY_CONDITION_NOSLIP_WALL,
  // INLET_OUTLET bc type is gone since it is handled by is_inlet_or_outlet surfel type
  //BOUNDARY_CONDITION_INLET_OUTLET,  // dns  inlet/outlet bc
  BOUNDARY_CONDITION_KE,            // turb inlet/outlet bc with specified k and epsilon
  BOUNDARY_CONDITION_INTENSITY,     // turb inlet/outlet bc with specified intensity and length scale
  BOUNDARY_CONDITION_EXTRAPOLATED,  // turb inlet/outlet bc with extrapolated k and epsilon
  BOUNDARY_CONDITION_LRF,           // local reference frame bc
  BOUNDARY_CONDITION_APM_INTERFACE, // acoustic porous medium interface
  BOUNDARY_CONDITION_CONDUCTION_INTERFACE, // conduction interface
  BOUNDARY_CONDITION_PM_INTERFACE,  // Heat exchanger porous medium interface
  BOUNDARY_CONDITION_OUTLET,
  BOUNDARY_CONDITION_FIXED_VEL

};

inline BOOLEAN is_bc_type_wall(int type) { return type <= (int)BOUNDARY_CONDITION_NOSLIP_WALL; }

// voxel grads
template<size_t N>
struct tVOXEL_GRADS
{
  tSFL_VAR<STP_PHYS_VARIABLE, N> gradp[3];  /* pressure grad in each of X, Y, and Z */

  VOID scale(STP_PHYS_VARIABLE scale, int index)
  {
    gradp[0][index] *= scale;
    gradp[1][index] *= scale;
    gradp[2][index] *= scale;
  }

  __HOST__DEVICE__ VOID reset(int soxor) {
    ccDOTIMES(i, 3) {
      gradp[i][soxor] = 0.0F;
    }
  }
};

using sVOXEL_GRADS = tVOXEL_GRADS<1>;
using VOXEL_GRADS = sVOXEL_GRADS*;

_INLINE_
std::ostream& operator<<(std::ostream& os, const sVOXEL_GRADS& g) {
  os << float(g.gradp[0]) << ", "
     << float(g.gradp[1]) << ", "
     << float(g.gradp[2]);
  return os;
}

#if BUILD_GPU
using sVOXEL_GRADS_64 = tVOXEL_GRADS<64>;
using VOXEL_GRADS_64 = sVOXEL_GRADS_64*;
#endif
//--------------------------------------------------------------------------
// Miscellaneous methods
//--------------------------------------------------------------------------

/*--------------------------------------------------------------------------*
 * Topological neighbor info accessor and mutator functions (macro wrappers) 
 *--------------------------------------------------------------------------*/

/* _VOXEL_TO_SCALE_INTERFACE_INDEX converts from a voxel index and a ublk face index
 * into the appropriate ublk index within a scale interface struct. For instance, in
 * 3D, given face 0 (+X) and either voxel 0 or voxel 4, the ublk index within a scale
 * interface struct is 0. This is because a +X scale interface struct would contain
 * pointers to the 4 fine ublks that abut these coarse voxels: 4, 5, 6, and 7.
 */
constexpr auINT8 _voxel_to_scale_interface_index[2][3][N_VOXELS_8] = {
  /* 2D */
  0, 0, 1, 1, 0, 0, 1, 1,	/* +X, -X */

  0, 0, 0, 0, 1, 1, 1, 1,       /* +Y, -Y */

  /* In 2D, there can never be a scale interface struct for the +Z or -Z faces,
   * but we must have an entry in this table anyway.
   */
  0, 0, 0, 0, 1, 1, 1, 1, 	/* +Z, -Z */


  /* 3D */
  0, 1, 2, 3, 0, 1, 2, 3,	/* +X, -X */

  0, 1, 0, 1, 2, 3, 2, 3,	/* +Y, -Y */

  0, 0, 1, 1, 2, 2, 3, 3	/* +Z, -Z */
};

// SIM_VOXELS_ON_UBLK_FACE indicates which voxels are on the 6 faces of a ublk.
/* SIM_VOXELS_ON_UBLK_FACE indicates which voxels are on the 6 faces
 * of a ublk.
 */
constexpr auINT8 sim_voxels_on_ublk_face[6][4] = {
  4, 5, 6, 7,   /* +X */
  0, 1, 2, 3,   /* -X */
  2, 3, 6, 7,   /* +Y */
  0, 1, 4, 5,   /* -Y */
  1, 3, 5, 7,   /* +Z */
  0, 2, 4, 6    /* -Z */
};

constexpr auINT8 sim_voxels_on_ublk_face_2d[4][2] = {
  4, 6,         /* +X */
  0, 2,         /* -X */
  2, 6,         /* +Y */
  0, 4          /* -Y */
};

constexpr VOXEL_MASK_8 sim_voxel_mask_on_ublk_face[6] = {
  VOXEL_MASK_8{240}, /* +X */
  VOXEL_MASK_8{15},  /* -X */
  VOXEL_MASK_8{204}, /* +Y */
  VOXEL_MASK_8{51},  /* -Y */
  VOXEL_MASK_8{170}, /* +Z */
  VOXEL_MASK_8{85}   /* -Z */
};

constexpr VOXEL_MASK_8 sim_voxel_mask_on_ublk_face_2d[4] = {
  VOXEL_MASK_8{80}, /* +X */
  VOXEL_MASK_8{5},  /* -X */
  VOXEL_MASK_8{68}, /* +Y */
  VOXEL_MASK_8{17}  /* -Y */
};

#if GPU_COMPILER
/* This duplication in the GPU namespace is necessary because CUDA-11.2 and
 * earlier versions cannot resolve constexpr arrays correctly and they are
 * treated as host only. We hope to get rid of this when CUDA introduces for
 * constexpr host arrays.
 */

namespace GPU {
  
  inline __CONSTANT__ auINT8 _voxel_to_scale_interface_index[2][3][N_VOXELS_8] = {
    /* 2D */
    0, 0, 1, 1, 0, 0, 1, 1,	/* +X, -X */

    0, 0, 0, 0, 1, 1, 1, 1,       /* +Y, -Y */

    /* In 2D, there can never be a scale interface struct for the +Z or -Z faces,
     * but we must have an entry in this table anyway.
     */
    0, 0, 0, 0, 1, 1, 1, 1, 	/* +Z, -Z */


    /* 3D */
    0, 1, 2, 3, 0, 1, 2, 3,	/* +X, -X */

    0, 1, 0, 1, 2, 3, 2, 3,	/* +Y, -Y */

    0, 0, 1, 1, 2, 2, 3, 3	/* +Z, -Z */
  };

  // SIM_VOXELS_ON_UBLK_FACE indicates which voxels are on the 6 faces of a ublk.
  /* SIM_VOXELS_ON_UBLK_FACE indicates which voxels are on the 6 faces
   * of a ublk.
   */
  inline __CONSTANT__ auINT8 sim_voxels_on_ublk_face[6][4] = {
    4, 5, 6, 7,   /* +X */
    0, 1, 2, 3,   /* -X */
    2, 3, 6, 7,   /* +Y */
    0, 1, 4, 5,   /* -Y */
    1, 3, 5, 7,   /* +Z */
    0, 2, 4, 6    /* -Z */
  };

  inline __CONSTANT__ auINT8 sim_voxels_on_ublk_face_2d[4][2] = {
    4, 6,         /* +X */
    0, 2,         /* -X */
    2, 6,         /* +Y */
    0, 4          /* -Y */
  };

  inline __CONSTANT__ VOXEL_MASK_8 sim_voxel_mask_on_ublk_face[6] = {
    VOXEL_MASK_8{240}, /* +X */
    VOXEL_MASK_8{15},  /* -X */
    VOXEL_MASK_8{204}, /* +Y */
    VOXEL_MASK_8{51},  /* -Y */
    VOXEL_MASK_8{170}, /* +Z */
    VOXEL_MASK_8{85}   /* -Z */
  };

  inline __CONSTANT__ VOXEL_MASK_8 sim_voxel_mask_on_ublk_face_2d[4] = {
    VOXEL_MASK_8{80}, /* +X */
    VOXEL_MASK_8{5},  /* -X */
    VOXEL_MASK_8{68}, /* +Y */
    VOXEL_MASK_8{17}  /* -Y */
  };  
} //namespace GPU
#endif

__HOST__DEVICE__ __HOST_CONSTEXPR__ _ALWAYS_INLINE_
auINT8 voxel_to_scale_interface_index(asINT32 n_dims, asINT32 face_index, asINT32 voxel) {  
  return HD_NAMESPACE::_voxel_to_scale_interface_index[n_dims - 2][face_index >> 1][voxel];  
}

__HOST__DEVICE__  __HOST_CONSTEXPR__ _ALWAYS_INLINE_
VOXEL_MASK_8 get_sim_voxel_mask_on_ublk_face(BOOLEAN is_2D, asINT32 face) {
  if (is_2D) {
    cassert(face < sizeof(sim_voxel_mask_on_ublk_face_2d)/sizeof(VOXEL_MASK_8));
  } else {
    cassert(face < sizeof(sim_voxel_mask_on_ublk_face)/sizeof(VOXEL_MASK_8));
  }

  return is_2D?
    HD_NAMESPACE::sim_voxel_mask_on_ublk_face_2d[face]:
    HD_NAMESPACE::sim_voxel_mask_on_ublk_face[face];
}

#define DO_VOXELS_ON_UBLK_FACE(voxel_var, face_index)         \
  asINT32 ___(i);                 \
  asINT32 voxel_var;                  \
  asINT32 ___(face_index) = (face_index);           \
  for (___(i) = 0;                  \
       (___(i) < 4)                 \
   && ((voxel_var = sim_voxels_on_ublk_face[___(face_index)][___(i)]) || true); \
       ___(i)++)


//--------------------------------------------------------------------------
// Global variables defined in PHYSTYPES/$T/sim_globals.h
//--------------------------------------------------------------------------
#include PHYSTYPES_GLOBALS_H

#if GPU_COMPILER
namespace GPU {
  extern __CONSTANT__ __DEVICE__ sGLOBALS g;
  extern __DEVICE__ STP_PHYS_VARIABLE g_uds_initial_value[MAX_N_USER_DEFINED_SCALARS];
}
#endif


#if BUILD_GPU
//We can't handle initialization of arrays with the auto-generated globals yet for GPU builds. 
//This is a temporary hack
extern STP_CONSTANT g_initial_particle_mass_fractions[NUM_PARTICLE_MATERIAL_SPECIES];
//for phase field solver
const STP_CONSTANT g_pf_field_x[2] = {0., 0.};
const STP_CONSTANT g_pf_field_y[2] = {0., 0.};
const STP_CONSTANT g_pf_field_z[2] = {0., 0.};
const STP_CONSTANT g_pf_grav_freq[3] = {0.0, 0.0, 0.0};
#endif

//for UDS lb sover
#if BUILD_5G_LATTICE
extern STP_CONSTANT g_uds_initial_value[MAX_N_USER_DEFINED_SCALARS];
#if BUILD_GPU
extern STP_CONSTANT g_uds_volume_source_polynomial_coefs[5];
extern STP_CONSTANT g_uds_volume_source_term[MAX_N_USER_DEFINED_SCALARS];
extern STP_CONSTANT g_uds_inlet_value[MAX_N_USER_DEFINED_SCALARS];
extern STP_CONSTANT g_uds_flux_bc_value[MAX_N_USER_DEFINED_SCALARS];
extern STP_CONSTANT g_uds_prescribed_bc[MAX_N_USER_DEFINED_SCALARS];
#endif

__HOST__DEVICE__  INLINE 
auto& get_uds_initial_value(asINT32 index) {
  return HD_NAMESPACE::g_uds_initial_value[index];
}
#endif



#endif  /* #ifndef _SIMENG_SHOB_H */
