#ifndef _SIMENG_PARTICLE_MLRF_H
#define _SIMENG_PARTICLE_MLRF_H

#include "mlrf.h"

typedef class sMLRF_PARCEL_HALF_RING {
  //This class handles moving parcel states from one side of a ring to the other (be it internal to external or vice versa).
  //Two of them are needed to handle particles going both directions across the reference frame interface.

 private:
  asINT32 m_max_segments;

  asINT32 m_num_active_local_segments; //Count of how many segments are currently being sent from.
  std::vector< cExaMsg<int> > m_count_send_msgs; //This is a pile of send requests for the parcel count of each ring segment.
  std::vector< cExaMsg<sPARCEL_STATE> > m_state_send_msgs; //This is a pile of send requests for the parcel buffers of each ring segment.
  std::vector<int> m_segment_send_counts; //send buffer for the parcel counts
  std::vector< std::vector<sPARCEL_STATE> > m_parcel_send_buffers; //These are the send buffers for each segment.
  std::vector<asINT32> m_segment_destination_proc; //destination sp for each send buffer

  asINT32 m_num_active_remote_segments; //Count of how many segments are currently being received to.
  std::vector< cExaMsg<int> > m_count_receive_msgs; //This is a pile of receive requests for the parcel counts of each ring segment.
  std::vector< cExaMsg<sPARCEL_STATE> > m_state_receive_msgs; //receive requests for each parcel state message.
  std::vector<asINT32> m_segment_receive_counts;  //revieve buffer for the parcel counts to expect.
  std::vector< std::vector<sPARCEL_STATE> > m_state_receive_buffers; //Receive buffers for each segments parcel state
  std::vector<asINT32> m_segment_source_proc; //source sp for each receive segment.

  asINT32 m_n_surfels_per_frame_in_ring; //copies from the MLRF_PARCEL_RING. Can probalby be removed now.
  asINT32 m_n_surfels_per_frame_on_proc;

 public:
  typedef enum {
  EXTERNAL_TO_INTERNAL = 0,  
  INTERNAL_TO_EXTERNAL,
  BOTH_DIRECTIONS
  }eINTERFACE_DIRECTIONS;
 static const char* m_interface_direction_to_string[BOTH_DIRECTIONS];

 private:
  eINTERFACE_DIRECTIONS m_direction;

  asINT32 m_ring_index;  //for debugging
 
  int parcel_count_send_mpi_tag() const {
    switch(m_direction) {
    case INTERNAL_TO_EXTERNAL:
      return eMPI_MLRF_ITOE_PARCEL_COUNT_TAG;
    case EXTERNAL_TO_INTERNAL:
      return eMPI_MLRF_ETOI_PARCEL_COUNT_TAG;
    default:;
    }
    return 0;
  }

 int parcel_count_receive_mpi_tag() const {
    switch(m_direction) {
    case INTERNAL_TO_EXTERNAL:
      return eMPI_MLRF_ETOI_PARCEL_COUNT_TAG;   
    case EXTERNAL_TO_INTERNAL:
      return eMPI_MLRF_ITOE_PARCEL_COUNT_TAG;   
    default:;
    }
    return 0;
  }

  int parcel_state_send_mpi_tag() const {
    switch(m_direction) {
    case INTERNAL_TO_EXTERNAL:
      return eMPI_MLRF_ITOE_PARCEL_COMM_TAG;
    case EXTERNAL_TO_INTERNAL:
      return eMPI_MLRF_ETOI_PARCEL_COMM_TAG;
    default:;
    }
    return 0;
  }

 int parcel_state_receive_mpi_tag() const {
    switch(m_direction) {
    case INTERNAL_TO_EXTERNAL:
      return eMPI_MLRF_ETOI_PARCEL_COMM_TAG;
    case EXTERNAL_TO_INTERNAL:
      return eMPI_MLRF_ITOE_PARCEL_COMM_TAG;
    default:;
    }
    return 0;
  }

  asINT32 rotation_offset_for_frame(dFLOAT angle_in_surfels) const {
    switch(m_direction) {
    case EXTERNAL_TO_INTERNAL:  
      return angle_in_surfels;
    case INTERNAL_TO_EXTERNAL:  
      return ceil(m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);
    default:;
    } return 0;
  }

  asINT32 rotation_offset_for_opposite_frame(dFLOAT angle_in_surfels) const {
    switch(m_direction) {
    case EXTERNAL_TO_INTERNAL:  
      return ceil(m_n_surfels_per_frame_in_ring - angle_in_surfels - 1);
    case INTERNAL_TO_EXTERNAL:  
      return angle_in_surfels;
    default:;
    } return 0;
  }




 public:
  sMLRF_PARCEL_HALF_RING() {}

  const char* direction_string() const {//for debugging messages to distingusih between frames.
    return m_interface_direction_to_string[m_direction];
  }

  VOID initialize(
                  asINT32 num_surfels_per_frame_in_ring,
                  asINT32 num_surfels_per_frame_on_proc,
                  eINTERFACE_DIRECTIONS direction,
                  asINT32 max_segments,
                  asINT32 ring_index,
                  TAGGED_MLRF_SURFEL* first_surfel_in_frame);
  
  VOID cancel_receives(BOOLEAN debug);

  TAGGED_MLRF_SURFEL* first_surfel_in_frame(TAGGED_MLRF_SURFEL* first_surfel_in_ring) {
    switch(m_direction) {
    case INTERNAL_TO_EXTERNAL:
      return first_surfel_in_ring + m_n_surfels_per_frame_on_proc;

    case EXTERNAL_TO_INTERNAL:
      return first_surfel_in_ring;
    default:;
    }
    return nullptr;
  }

TAGGED_MLRF_SURFEL* first_surfel_in_opposite_frame(TAGGED_MLRF_SURFEL* first_surfel_in_ring) {
    switch(m_direction) {
    case EXTERNAL_TO_INTERNAL:
      return first_surfel_in_ring + m_n_surfels_per_frame_on_proc;
    case INTERNAL_TO_EXTERNAL:
      return first_surfel_in_ring;
    default:;
    }
    return nullptr;
  }

  VOID count_parcels_and_fill_send_buffers(
                                           TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                           dFLOAT angle_in_surfels,
                                           MLRF_RING ring,
                                           asINT32 debug);

  VOID post_parcel_sends(BOOLEAN debug); //sends both the states and the count messages
  VOID post_count_receives(dFLOAT angle_in_surfels, MLRF_RING ring, BOOLEAN debug); //receives just the count messages
  VOID post_state_receives(dFLOAT angle_in_surfels, MLRF_RING ring, BOOLEAN debug);
  BOOLEAN complete_count_receives(BOOLEAN debug); 
  BOOLEAN complete_state_receives(BOOLEAN debug);
  VOID debug_do_segment_macros(MLRF_RING ring, BOOLEAN debug);
  VOID emit_lrf_parcels(TAGGED_MLRF_SURFEL* first_surfel_in_ring, 
                        dFLOAT angle_in_surfels, 
                        MLRF_RING ring, 
                        LRF_PHYSICS_DESCRIPTOR lrf,
                        BOOLEAN debug);
} *MLRF_PARCEL_HALF_RING;

typedef class sMLRF_PARCEL_RING: public sMLRF_RING {
  //This class extends the sMLRF_RING class to be able to deal with parcels.
 private:
  //Two parcel "half rings" are needed to handle parcels from either frame.
  sMLRF_PARCEL_HALF_RING m_half_rings[sMLRF_PARCEL_HALF_RING::BOTH_DIRECTIONS];
  asINT32 m_ring_index; //for debugging messages only

 public:

 sMLRF_PARCEL_RING() :
  sMLRF_RING() {}
  BOOLEAN particles_enabled() const {return TRUE;}
 
  VOID post_parcel_sends(BOOLEAN debug);
  VOID post_state_receives(dFLOAT angle_in_surfels, BOOLEAN debug);
  VOID post_count_receives(dFLOAT angle_in_surfels, BOOLEAN debug);
  BOOLEAN complete_count_receives(BOOLEAN debug); 
  BOOLEAN complete_state_receives(BOOLEAN debug);

  VOID count_parcels_and_fill_send_buffers(
                                           TAGGED_MLRF_SURFEL* first_surfel_in_ring,
                                           dFLOAT angle_in_surfels,
                                           BOOLEAN debug);


  VOID emit_lrf_parcels(TAGGED_MLRF_SURFEL* first_surfel_in_ring, 
                        dFLOAT angle_in_surfels, 
                        LRF_PHYSICS_DESCRIPTOR lrf, 
                        BOOLEAN debug);
 
  MLRF_PARCEL_HALF_RING half_ring(sMLRF_PARCEL_HALF_RING::eINTERFACE_DIRECTIONS direction) {return half_ring((int)direction);}
  MLRF_PARCEL_HALF_RING half_ring(asINT32 direction) {return &m_half_rings[direction];}

  VOID initialize(asINT32 ring_index, TAGGED_MLRF_SURFEL* first_surfel_in_ring);
    VOID cancel_receives();
  VOID debug_do_segment_macros(BOOLEAN debug);
} *MLRF_PARCEL_RING;


//These macros and helper functions were copied from mlrf.h and then modified to not include the overlaping sections of
//the segments buffers.

#define MLRF_DO_LOCAL_SEGMENTS_WITH_NO_OVERLAP(msg_length, msg_depot_offset, remote_proc,  /* Bound by macro */ \
                                               _ring, _surfel_offset)                      /* Inputs to macro */ \
  MLRF_RING __(ring) = (_ring);                                         \
  asINT32 __(n_surfels_in_ring)       = __(ring)->m_n_surfels_per_frame_in_ring; \
  asINT32 __(surfel_offset) = ringmod(__(n_surfels_in_ring) - _surfel_offset, __(n_surfels_in_ring)); \
  /* __(surfel_offset) used here is the index in the receiving ring that matches surfel zero in the sending ring */ \
  MLRF_RING_SEGMENT __(ring_segments) = __(ring)->ring_segments(); \
  asINT32 __(n_ring_segments)         = __(ring)->n_ring_segments(); \
  sINT32  *__(local_ring_segments)    = __(ring)->ring_segments_on_proc(); /* indices of segments owned by this proc */ \
  asINT32 __(n_local_ring_segments)   = __(ring)->n_ring_segments_on_proc(); \
  asINT32 msg_length; \
  asINT32 msg_depot_offset = 0; /* The offset into the local array of depots */ \
  asINT32 remote_proc; \
  asINT32 __(local_segment_index) = 0; \
  asINT32 __(local_segment)  = __(local_ring_segments)[__(local_segment_index)]; \
  asINT32 __(local_offset)   = __(ring_segments)[__(local_segment)].offset; \
  asINT32 __(remote_offset)  = ringmod(__(local_offset) + __(surfel_offset), __(n_surfels_in_ring)); \
  asINT32 __(remote_segment) = find_segment_from_surfel_offset(__(ring_segments), __(n_ring_segments), __(remote_offset)); \
  asINT32 __(msg_depot_offset_inc); \
  BOOLEAN __(send_one_to_prior_remote_segment) = FALSE; \
  asINT32 __(n_local_ring_segments_to_process); \
  asINT32 __(local_segments_so_far) = 0; \
  if (__(local_segment) == 0) {       \
  if (__(ring_segments)[__(remote_segment)].offset == __(remote_offset)) \
       /* The first segment is owned by this proc and there is a segment boundary in the */ \
       /* rotated ring at offset 0. Thus we need an extra message at the very end that   */ \
       /* contains just the first element of the first segment.                          */ \
   __(n_local_ring_segments_to_process) = __(n_local_ring_segments); \
     else \
       __(n_local_ring_segments_to_process) = __(n_local_ring_segments); \
   } else { \
     __(n_local_ring_segments_to_process) = __(n_local_ring_segments); \
     if (__(ring_segments)[__(remote_segment)].offset == __(remote_offset)) \
       /* The first segment owned by this proc is not segment 0 but is aligned with a segment in the    */ \
       /* rotated ring. The first message to send is a one element message to the prior remote segment. */ \
       __(send_one_to_prior_remote_segment) = TRUE; \
   } \
    \
BOOLEAN keep_going = TRUE; \
for ( ; keep_going && ((keep_going = next_local_segment(msg_length, __(msg_depot_offset_inc), \
                                                        remote_proc, __(send_one_to_prior_remote_segment), \
                                                        __(local_segment_index), \
                                                        __(local_segment), __(remote_segment), \
                                                        __(local_offset),  __(remote_offset), \
                                                        __(surfel_offset), __(local_ring_segments), __(n_local_ring_segments), \
                                                        __(n_local_ring_segments_to_process), __(local_segments_so_far), \
                                                        __(ring_segments), __(n_ring_segments), __(n_surfels_in_ring))), TRUE); \
msg_depot_offset = ringmod(msg_depot_offset + __(msg_depot_offset_inc), __(ring)->m_n_surfels_per_frame_on_proc))


#define MLRF_DO_REMOTE_SEGMENTS_WITH_NO_OVERLAP(msg_length, msg_depot_offset, remote_proc,  /* Bound by macro */ \
                                                _ring, _surfel_offset)                      /* Inputs to macro */ \
  MLRF_RING __(ring) = (_ring);                                         \
  asINT32 __(n_surfels_in_ring) = __(ring)->m_n_surfels_per_frame_in_ring; \
  asINT32 __(surfel_offset) = ringmod(__(n_surfels_in_ring) - _surfel_offset, __(n_surfels_in_ring)); \
  /* __(surfel_offset) used here is the index in the receiving ring that matches surfel zero in the sending ring */ \
  MLRF_RING_SEGMENT __(ring_segments) = __(ring)->ring_segments(); \
  asINT32 __(n_ring_segments)   = __(ring)->n_ring_segments(); \
  sINT32  *__(local_ring_segments) = __(ring)->ring_segments_on_proc(); /* indices of segments owned by this proc */ \
 asINT32 __(n_local_ring_segments)   = __(ring)->n_ring_segments_on_proc(); \
 asINT32 msg_length; \
 asINT32 remote_proc; \
 /* We have to recv messages in the same order they are sent by remote procs. Thus we start at */ \
 /* the first segment owned by this proc that is after __(surfel_offset).                          */ \
 asINT32 __(local_depots_before_surfel_offset); \
 asINT32 __(local_offset); \
 asINT32 __(n_local_depots) = __(ring)->m_n_surfels_per_frame_on_proc + __(n_local_ring_segments); \
 asINT32 __(local_segment_index) = find_local_segment_at_surfel_offset(__(local_ring_segments), __(n_local_ring_segments), \
__(ring_segments), __(n_ring_segments), __(surfel_offset), \
__(n_surfels_in_ring), \
__(local_offset), __(local_depots_before_surfel_offset)); \
asINT32 msg_depot_offset = ringmod(__(local_depots_before_surfel_offset), __(n_local_depots)); \
asINT32 __(local_segment)  = __(local_ring_segments)[__(local_segment_index)]; \
asINT32 __(remote_offset)  = ringnegmod(__(local_offset) - __(surfel_offset), __(n_surfels_in_ring)); \
asINT32 __(remote_segment) = find_segment_from_surfel_offset(__(ring_segments), __(n_ring_segments), __(remote_offset)); \
asINT32 __(msg_depot_offset_inc); \
BOOLEAN __(recv_one_from_next_remote_segment) = FALSE; \
 asINT32 __(n_local_ring_segments_to_process); \
 asINT32 __(local_segments_so_far) = 0; \
asINT32 __(offset_after_front_half_of_first_segment); \
if (__(ring_segments)[__(local_segment)].offset == __(local_offset)) { \
  __(offset_after_front_half_of_first_segment) = 0; /* not actually used */ \
  __(n_local_ring_segments_to_process) = __(n_local_ring_segments); \
} else { \
  /* The first local segment is processed in 2 pieces. The back end is processed first */ \
  /* and the front end is processed last (i.e. as the last segment in the ring).       */ \
  __(offset_after_front_half_of_first_segment) = __(local_offset); \
  __(n_local_ring_segments_to_process) = __(n_local_ring_segments) + 1; \
} \
BOOLEAN keep_going = TRUE; \
for ( ; keep_going \
        && ((keep_going = next_remote_segment(msg_length, __(msg_depot_offset_inc), \
                                              remote_proc, __(recv_one_from_next_remote_segment), \
                                              __(local_segment_index), \
                                              __(local_segment), __(remote_segment), \
                                              __(local_offset),  __(remote_offset), \
                                              __(surfel_offset), __(local_ring_segments), __(n_local_ring_segments), \
                                              __(n_local_ring_segments_to_process), __(local_segments_so_far), \
                                              __(offset_after_front_half_of_first_segment), \
                                              __(ring_segments), __(n_ring_segments), __(n_surfels_in_ring))), \
            TRUE); \
      msg_depot_offset = ringmod(msg_depot_offset + __(msg_depot_offset_inc), __(n_local_depots)))


inline BOOLEAN next_local_segment(asINT32 &msg_length, asINT32 &msg_depot_offset_inc,      //next_send_message
                                  asINT32 &remote_proc, BOOLEAN &send_one_to_prior_remote_segment,
                                  asINT32 &local_segment_index,
                                  asINT32 &local_segment, asINT32 &remote_segment,
                                  asINT32 &local_offset,  asINT32 &remote_offset,
                                  asINT32 surfel_offset, sINT32 *local_ring_segments, asINT32 n_local_ring_segments,
                                  asINT32 n_local_ring_segments_to_process, asINT32 &local_segments_so_far,
                                  MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, asINT32 n_surfels_in_ring)
{
  if (send_one_to_prior_remote_segment) {
    send_one_to_prior_remote_segment = FALSE;
    msg_length = 1;
    msg_depot_offset_inc = 0;
    asINT32 segment_before_remote_segment = ringnegmod(remote_segment - 1, n_ring_segments);
    remote_proc = ring_segments[segment_before_remote_segment].proc;
    return local_segment != 0; // stop if we just cycled back around to the first segment
  }

  remote_proc = ring_segments[remote_segment].proc;

  asINT32 segment_after_local_segment = ringmod(local_segment + 1, n_ring_segments);
  asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);

  asINT32 segment_after_local_segment_offset = ((segment_after_local_segment == 0)
                                                ? n_surfels_in_ring
                                                : ring_segments[segment_after_local_segment].offset);
  asINT32 segment_after_remote_segment_offset = ((segment_after_remote_segment == 0)
                                                 ? n_surfels_in_ring
                                                 : ring_segments[segment_after_remote_segment].offset);

  asINT32 local_segment_remaining_length = segment_after_local_segment_offset - local_offset;
  asINT32 remote_segment_remaining_length = segment_after_remote_segment_offset - remote_offset;

  if (local_segment_remaining_length <= remote_segment_remaining_length) {
    msg_length = local_segment_remaining_length;
    msg_depot_offset_inc = local_segment_remaining_length;
    local_segments_so_far++;
    if (local_segments_so_far < n_local_ring_segments_to_process) {
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringmod(local_offset + surfel_offset, n_surfels_in_ring);
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);

      if (ring_segments[remote_segment].offset == remote_offset)
        send_one_to_prior_remote_segment = FALSE; //TRUE;
      return FALSE; //TRUE;  // keep going
    } else {
      return FALSE; // stop
    }

  } else {
    //msg_length = remote_segment_remaining_length + 1;
    msg_length = remote_segment_remaining_length;
    msg_depot_offset_inc = remote_segment_remaining_length;
    local_offset = local_offset + remote_segment_remaining_length; // mod not needed since we stay in same segment
    remote_offset = ring_segments[segment_after_remote_segment].offset;
    remote_segment = segment_after_remote_segment;
    return TRUE;  // keep going
  }
}

inline BOOLEAN next_remote_segment(asINT32 &msg_length, asINT32 &msg_depot_offset_inc,  //next_recv_message
                                   asINT32 &remote_proc, BOOLEAN &recv_one_from_next_remote_segment,
                                   asINT32 &local_segment_index,
                                   asINT32 &local_segment, asINT32 &remote_segment,
                                   asINT32 &local_offset,  asINT32 &remote_offset,
                                   asINT32 surfel_offset, sINT32 *local_ring_segments, asINT32 n_local_ring_segments,
                                   asINT32 n_local_ring_segments_to_process, asINT32 &local_segments_so_far,
                                   asINT32 offset_after_front_half_of_first_segment,
                                   MLRF_RING_SEGMENT ring_segments, asINT32 n_ring_segments, asINT32 n_surfels_in_ring)
{
  if (recv_one_from_next_remote_segment) {
    recv_one_from_next_remote_segment = FALSE;
    msg_length = 1;
    msg_depot_offset_inc = 1;
    asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);
    remote_proc = ring_segments[segment_after_remote_segment].proc;

    if (local_segments_so_far < n_local_ring_segments_to_process) {
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring);
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);
      return TRUE;
    }

    return FALSE;
  }

  remote_proc = ring_segments[remote_segment].proc;

  asINT32 segment_after_local_segment = ringmod(local_segment + 1, n_ring_segments);
  asINT32 segment_after_remote_segment = ringmod(remote_segment + 1, n_ring_segments);

  asINT32 segment_after_local_segment_offset = ((local_segments_so_far == n_local_ring_segments)
                                                ? offset_after_front_half_of_first_segment
                                                : ((segment_after_local_segment == 0)
                                                   ? n_surfels_in_ring
                                                   : ring_segments[segment_after_local_segment].offset));
  asINT32 segment_after_remote_segment_offset = ((segment_after_remote_segment == 0)
                                                 ? n_surfels_in_ring
                                                 : ring_segments[segment_after_remote_segment].offset);

  asINT32 local_segment_remaining_length = segment_after_local_segment_offset - local_offset;
  asINT32 remote_segment_remaining_length = segment_after_remote_segment_offset - remote_offset;

  if (local_segment_remaining_length < remote_segment_remaining_length) {
    local_segments_so_far++;
    if (local_segments_so_far < n_local_ring_segments_to_process) {
      //msg_length = local_segment_remaining_length + 1;
      msg_length = local_segment_remaining_length;
      msg_depot_offset_inc = local_segment_remaining_length + 1;
      local_segment_index = ringmod(local_segment_index + 1, n_local_ring_segments);
      local_segment = local_ring_segments[local_segment_index];
      local_offset = ring_segments[local_segment].offset;
      remote_offset = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring); // ?????
      remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);
      return TRUE;  // keep going
    } else if (local_segments_so_far == n_local_ring_segments) {
      msg_length = local_segment_remaining_length + 1;
      return FALSE; // stop
    } else {
      // If we reach here, we're processing the front half of the first segment and we don't want to send
      // one extra element
      msg_length = local_segment_remaining_length;
      return FALSE; // stop
    }

  } else if (local_segment_remaining_length == remote_segment_remaining_length) {
    msg_length = remote_segment_remaining_length;
    msg_depot_offset_inc = remote_segment_remaining_length;

    local_segments_so_far++;
    if (local_segments_so_far <= n_local_ring_segments) {
      recv_one_from_next_remote_segment = FALSE;//TRUE;
      return FALSE;
    } else {
      // If we reach here, we're processing the front half of the first segment and we don't want to send
      // one extra element
      return FALSE;
    }
  } else {
    msg_length = remote_segment_remaining_length;
    msg_depot_offset_inc = remote_segment_remaining_length;
    local_offset = local_offset + remote_segment_remaining_length; // mod not needed since we stay in same segment
    remote_offset = ring_segments[segment_after_remote_segment].offset;
    remote_segment = segment_after_remote_segment;
    return TRUE;  // keep going
  }
}



inline VOID mlrf_do_remote_segments_with_no_overlap(   //This is an unmacrofied version of MLRF_DO_SEND_MESSAGES form mlrf.h slightly modified to not include overlaped parts of the message buffer.
                                                    MLRF_RING ring, 
                                                    asINT32 surfel_offset, 
                                                    VOID *(do_something)(asINT32 msg_length, asINT32 msg_depot_offset, asINT32 remote_proc)) {
  asINT32 n_surfels_in_ring = ring->m_n_surfels_per_frame_in_ring;
  surfel_offset = ringmod(n_surfels_in_ring - surfel_offset, n_surfels_in_ring);
  /* surfel_offset used here is the index in the receiving ring that matches surfel zero in the sending ring */
  MLRF_RING_SEGMENT ring_segments = ring->ring_segments();
  asINT32 n_ring_segments = ring->n_ring_segments();
  sINT32  *local_ring_segments = ring->ring_segments_on_proc(); /* indices of segments owned by this proc */
  asINT32 n_local_ring_segments = ring->n_ring_segments_on_proc();
  asINT32 msg_length;
  asINT32 remote_proc;
  /* We have to recv messages in the same order they are sent by remote procs. Thus we start at */
  /* the first segment owned by this proc that is after surfel_offset.                          */
  asINT32 local_depots_before_surfel_offset;
  asINT32 local_offset;
  asINT32 n_local_depots = ring->m_n_surfels_per_frame_on_proc + n_local_ring_segments;
  asINT32 local_segment_index = find_local_segment_at_surfel_offset(
                                                                    local_ring_segments, 
                                                                    n_local_ring_segments,
                                                                    ring_segments, 
                                                                    n_ring_segments, 
                                                                    surfel_offset,
                                                                    n_surfels_in_ring,
                                                                    local_offset, 
                                                                    local_depots_before_surfel_offset);
  asINT32 msg_depot_offset = ringmod(local_depots_before_surfel_offset, n_local_depots);
  asINT32 local_segment  = local_ring_segments[local_segment_index];
  asINT32 remote_offset  = ringnegmod(local_offset - surfel_offset, n_surfels_in_ring);
  asINT32 remote_segment = find_segment_from_surfel_offset(ring_segments, n_ring_segments, remote_offset);
  asINT32 msg_depot_offset_inc;
  BOOLEAN recv_one_from_next_remote_segment = FALSE;
  asINT32 n_local_ring_segments_to_process;
  asINT32 local_segments_so_far = 0;
  asINT32 offset_after_front_half_of_first_segment;
  
  if (ring_segments[local_segment].offset == local_offset) {
    offset_after_front_half_of_first_segment = 0; /* not actually used */
    n_local_ring_segments_to_process = n_local_ring_segments;
  } else {
    /* The first local segment is processed in 2 pieces. The back end is processed first */
    /* and the front end is processed last (i.e. as the last segment in the ring).       */
    offset_after_front_half_of_first_segment = local_offset;
    n_local_ring_segments_to_process = n_local_ring_segments + 1;
  }
  
  BOOLEAN keep_going = TRUE;
  for ( ; keep_going
          && ((keep_going = next_remote_segment(msg_length, msg_depot_offset_inc,
                                                remote_proc, recv_one_from_next_remote_segment,
                                                local_segment_index,
                                                local_segment, remote_segment,
                                                local_offset,  remote_offset,
                                                surfel_offset, local_ring_segments, n_local_ring_segments,
                                                n_local_ring_segments_to_process, local_segments_so_far,
                                                offset_after_front_half_of_first_segment,
                                                ring_segments, n_ring_segments, n_surfels_in_ring)),
              TRUE);
        msg_depot_offset = ringmod(msg_depot_offset + msg_depot_offset_inc, n_local_depots)) {
    do_something(msg_length, msg_depot_offset, remote_proc);
  }
}






#endif
