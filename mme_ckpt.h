#ifndef EXA_SIMENG_MME_CKPT_H
#define EXA_SIMENG_MME_CKPT_H

#include LGI_H

#include "common_sp.h"
#include "sim.h"
#include "gpu_sim.hcu"

namespace GPU {
  struct sMME_CKPT;
}

class cMME_CKPT
{
public:
  static constexpr int N_VOXELS = ubFLOAT::N_VOXELS;
  using VOXEL_MASK = typename std::conditional<N_VOXELS == 8, VOXEL_MASK_8, VOXEL_MASK_64>::type;
  using cDGF_MME_CKPT_UBLK_VOXEL_MASK = tDGF_MME_CKPT_UBLK_VOXEL_MASK<N_VOXELS>;
  using cDGF_MME_CKPT_UBLK_HEADER     = tDGF_MME_CKPT_UBLK_HEADER<N_VOXELS>;
  using cDGF_MME_CKPT_SURFACE_NORMALS = tDGF_MME_CKPT_SURFACE_NORMALS<N_VOXELS>;
  using cDGF_MME_CKPT_MEAS_VAR        = tDGF_MME_CKPT_MEAS_VAR<N_VOXELS>;

  cDGF_MME_CKPT_UBLK_VOXEL_MASK mask;
  cDGF_MME_CKPT_UBLK_HEADER header;
  cDGF_MME_CKPT_SURFACE_NORMALS surface;
  cDGF_MME_CKPT_MEAS_VAR meas[DGF_MAX_TOTAL_MME_CKPT_VARS];

  cMME_CKPT() : m_num_ckpt_vars(0) {}

  void write_mme_ckpt(bool is_avg_mme);

  __DEVICE__
  void set_num_ckpt_vars(sINT32 n)
  {
#if !DEVICE_COMPILATION_MODE
    auto& sim = get_sim_ref();
    auto& simc = *sim.c();
#if !BUILD_5G_LATTICE
    if (simc.uds_solver_type == LB_UDS) {
      if ( n >  DGF_MAX_TOTAL_MME_CKPT_VARS) {
	msg_internal_error("Exceeded maximum number of mme ckpt variables");
      }
    } else
#endif
    if ( n > DGF_MAX_MME_CKPT_VARS ) {
      msg_internal_error("Exceeded maximum number of mme ckpt variables");
    }
  
#endif
    m_num_ckpt_vars = n;
  }

  sINT32 num_ckpt_vars() const { return m_num_ckpt_vars; }

  friend struct GPU::sMME_CKPT;
  
private:
  void write_mme_ckpt_ref_frame_info(bool is_avg_mme);
  void write_mme_ckpt_movb_info(bool is_avg_mme);
  void write_voxel_mme_ckpt_data(bool is_avg_mme);
  sINT32 m_num_ckpt_vars;

};

#endif
