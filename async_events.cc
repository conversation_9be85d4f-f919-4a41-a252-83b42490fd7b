/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include "event_queue_event.h"
#include "event_queue.h"
#include "async_events.h"
#include "sim.h"
#include "shob_dyn.h"
#include "surfel_dyn_sp.h"
#include "thread_run.h"
#include "strand_mgr.h"
#include "atomic_ref.h"

#include TPI_COMMON_H
#include PHYSICS_H
#include SMEM_H

static cSTRING	timestep_to_str(BASETIME timestep) {
  static CHARACTER buf[128];

  if (timestep == BASETIME_LAST) {
    return("last timestep");
  } else {
    sprintf(buf, "timestep %ld", timestep);
    return(buf);
  }
}

namespace AsyncEventQueuePendingRequest
{
  static bool event_req_pending; // comm thread only
  static pthread_mutex_t event_reply_mutex;
  static pthread_cond_t event_reply_cv;
  static bool event_reply_wakeup;

void init() {
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
  pthread_mutex_init(&event_reply_mutex, NULL);
  pthread_cond_init(&event_reply_cv, NULL);
  event_reply_wakeup = false;
  event_req_pending = false;
}

// main thread
void wait_for_req_finish() {
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
  pthread_mutex_lock(&event_reply_mutex);
  while(!event_reply_wakeup) {
    pthread_cond_wait(&event_reply_cv, &event_reply_mutex);
  }
  event_reply_wakeup = false;
  pthread_mutex_unlock(&event_reply_mutex);
}

// comm thread
void set_event_req_pending() {
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
  event_req_pending = true;
}

// comm thread
void finish_req() {
    LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
    pthread_mutex_lock(&event_reply_mutex);
    event_reply_wakeup = true;
    event_req_pending = false;
    pthread_cond_signal(&event_reply_cv);
    pthread_mutex_unlock(&event_reply_mutex);
}

// comm thread
bool is_event_req_pending() {
  return event_req_pending; 
}

} // end namespace AsyncEventQueuePendingRequest

bool sEVENT_QUEUE_EVENT::comm_thread_received_exit_event = false;

bool sEVENT_QUEUE_EVENT::do_not_process()
{
  return comm_thread_received_exit_event || g_strand_mgr.m_exit;
}

void sEVENT_QUEUE_EVENT::set_received_exit_event() 
{ 
  LOG_MSG("ASYNC",LOG_FUNC,LOG_TS);
  comm_thread_received_exit_event = true; 
}

bool sEVENT_QUEUE_EVENT::wait_for_comm_thread_or_exit(cBOOLEAN& flag) 
{
  while (!flag) {
    sp_thread_sleep(THREAD_SLEEP_SHORT);
    if (comm_thread_received_exit_event) {
      LOG_MSG("ASYNC",LOG_FUNC,LOG_TS) << "Bailing";
      return true;
    }
  }
  return false;
}

BOOLEAN	sEVENT_READ_DSM_FILE::process() {

  if (do_not_process())
    return TRUE; // pointless to read DSMs if about to exit

  if ( wait_for_comm_thread_or_exit(g_ublk_table[STP_FLOW_REALM].dsm_ready) ) {
    return TRUE;
  }

  if (my_proc_id == 0) {
    msg_print_no_prefix("Updating DSMs at timestep %ld", g_timescale.m_time);
  }
  g_ublk_table[STP_FLOW_REALM].set_dynamics_scalar_multipliers();
  return (TRUE);
}
VOID sEVENT_READ_DSM_FILE::print() {
  msg_print("DSM File read at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_READ_DSM_FILE::unique() {
  return(TRUE);
}

BOOLEAN	sEVENT_MME_CKPT::process() {
  process_mme_ckpt_sim_thread();
  return(TRUE);
}
VOID	sEVENT_MME_CKPT::print() {
  msg_print("MME CHECKPOINT at timestep %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_MME_CKPT::unique() {return(FALSE);}

static BOOLEAN process_exit() {

  ccDOTIMES(strand_index, N_STRANDS) {
    g_strand_mgr.stop_runstate((STRAND)strand_index);
  }

  tATOMIC_REF(g_strand_mgr.m_exit).store(true,std::memory_order_release);

  return(TRUE);
}

BOOLEAN	sEVENT_MME_CKPT_EXIT::process() {
  process_mme_ckpt_sim_thread();
  return process_exit();
}

VOID	sEVENT_MME_CKPT_EXIT::print() {
  msg_print("MME CHECKPOINT AND EXIT at %s", timestep_to_str(timestep));
}

BOOLEAN	sEVENT_MME_CKPT_EXIT::unique() {return(FALSE);}

BOOLEAN	sEVENT_FULL_CKPT::process() {
  process_full_ckpt_sim_thread();
  return(TRUE);
}

VOID	sEVENT_FULL_CKPT::print() {
  msg_print("FULL CHECKPOINT at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_FULL_CKPT::unique() {return(FALSE);}

BOOLEAN	sEVENT_FULL_CKPT_EXIT::process() {
  process_full_ckpt_sim_thread();
  return process_exit();
}

VOID	sEVENT_FULL_CKPT_EXIT::print() {
  msg_print("FULL CHECKPOINT AND EXIT at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_FULL_CKPT_EXIT::unique() {return(FALSE);}

BOOLEAN	sEVENT_CKPT_INTERVAL::process() {
  EVENT_ID event_id = sim_run_info.run_options & SIM_FULL_CKPTS ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT;
  EVENT_ID ckpt_event_id = g_async_event_queue.remove_recurring_event(event_id);
  if (ckpt_event_id == EVENT_ID_INVALID)
    ckpt_event_id = sim_run_info.run_options & SIM_FULL_CKPTS ? EVENT_ID_FULL_CKPT : EVENT_ID_MME_CKPT;
  TIMESTEP period = arg;
  if (period > 0) {
    BASETIME start = period * (g_timescale.m_time / period + 1);
    g_async_event_queue.add_entry(new_event(ckpt_event_id, 0, start, period, BASETIME_LAST));
    if (my_proc_id == 0)
      msg_print_no_prefix("Periodic checkpoint interval set to %ld timesteps", (long)period);
  } else {
    if (my_proc_id == 0)
      msg_print_no_prefix("Periodic checkpoints disabled");
  }
  return(TRUE);
}

VOID	sEVENT_CKPT_INTERVAL::print() {
  msg_print("SET CHECKPOINT INTERVAL to %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_CKPT_INTERVAL::unique() {return(FALSE);}
// Used to start thermal accelaration via exasignal
BOOLEAN	sEVENT_THERMAL_ACCEL_ON::process()
{ 
  //This type of event is defined within the flow realm, so gets the realm timestep 
  TIMESTEP realm_timestep = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(timestep);
  TIMESTEP start_time;

#if BUILD_D19_LATTICE
  /* Thermal_accel on/off is associated with T_solver switch. Here the event of setting thermal_accel
   * is proceesed one timestep earlier than real action (on/off) to facilitate seeding for T_solver switch.
   */
 
  //First check whether accel is ON or not at current step
  asINT32 acc_stime = sim.thermal_accel.start;
  dFLOAT acc_etime = sim.thermal_accel.end_time();
//  Remove solver switch controlled by T_lb_scalar_solver_start_time
  if(sim.is_prescribed_T_solver_switch && realm_timestep < sim.T_lb_scalar_solver_start_time) {
    g_async_event_queue.remove_recurring_event(EVENT_ID_T_LB_SOLVER_ON);
    sim.is_prescribed_T_solver_switch = FALSE;
  }
  if (sim.thermal_accel.timestep_within_thermal_acc(realm_timestep)){
//    First remove all the recurring thermal solver switch events, as EXASIGNAL takes precedence.
    g_async_event_queue.remove_recurring_event(EVENT_ID_T_PDE_SOLVER_ON);
    if(sim.thermal_accel.switch_back_to_lb_solver()) {
      g_async_event_queue.remove_recurring_event(EVENT_ID_T_LB_SOLVER_ON);
    }
    asINT32 acc_mtime = (realm_timestep - acc_stime)%sim.thermal_accel.period;
    if(acc_mtime < sim.thermal_accel.interval) {
      sim.thermal_accel.start  = realm_timestep;     //no need to turn on accel since it is already ON
    } else { //accel is OFF at current step
      sim.thermal_accel.start  = realm_timestep + 1; //add "1" back to start; 1 timestep delay between process and turn on accel
                                                     //allows the seeding for T_solver switch 
    }
  } else { //accel is OFF at current step
    sim.thermal_accel.start  = realm_timestep + 1; //add "1" back to start; 1 timestep delay between process and turn on accel
                                                   //allows the seeding for  T_solver switch 
  }

  //for message output
  start_time = realm_timestep + 1;

  sim.thermal_accel.is_hacked = TRUE;
  sim.thermal_accel.hacked_start = start_time;
  sim.thermal_accel.hacked_stop = -1;

#else
  sim.thermal_accel.start  = realm_timestep;
  start_time = realm_timestep;

#endif

  sim.thermal_accel.repeat = 1;
  TIMESTEP acc_end = sim.thermal_accel.end_time();
  TIMESTEP period = arg;
  if (period <= 0) {
    sim.thermal_accel.period = TIMESTEP_MAX;
    sim.thermal_accel.interval = TIMESTEP_MAX;
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_PDE_SOLVER_ON, 0, sim.thermal_accel.start - 1, 0, acc_end, STP_FLOW_REALM));
    if (my_proc_id == 0) {
      cSTRING realm_str = (sim.is_conduction_model) ? "flow " : ""; //no need to specify flow if conduction is inactive
      msg_print_no_prefix ("Thermal solver acceleration will turn on at %stimestep %d", realm_str, start_time);
    }
#if BUILD_D19_LATTICE
    sim.thermal_accel.hacked_period = -1;
#endif

  } else {

    sim.thermal_accel.period = period;
    sim.thermal_accel.interval = period;

#if BUILD_D19_LATTICE
    if (sim.thermal_accel.start == realm_timestep){
      sim.thermal_accel.period = period + 1;
      sim.thermal_accel.interval = period + 1;
    }
    sim.thermal_accel.hacked_period = period;
#endif
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_PDE_SOLVER_ON, 0, sim.thermal_accel.start - 1, 0, acc_end, STP_FLOW_REALM));
    g_async_event_queue.add_entry(new_event(EVENT_ID_T_LB_SOLVER_ON, 0, sim.thermal_accel.start + sim.thermal_accel.period - 1, 0, acc_end, STP_FLOW_REALM));
    if (my_proc_id == 0) {
      cSTRING realm_str = (sim.is_conduction_model) ? "flow " : ""; //no need to specify flow if conduction is inactive
      msg_print_no_prefix ("Thermal solver acceleration will turn on at %stimestep %d for %d %stimesteps", 
                            realm_str, start_time, sim.thermal_accel.period, realm_str);
    }
  }

  return(TRUE);
}
VOID	sEVENT_THERMAL_ACCEL_ON::print() {
#if BUILD_D19_LATTICE
  msg_print("THERMAL ACCEL ON at %s", timestep_to_str(timestep+1));
#else
  msg_print("THERMAL ACCEL ON at %s", timestep_to_str(timestep));
#endif
}
BOOLEAN	sEVENT_THERMAL_ACCEL_ON::unique() {return(FALSE);}

//Use to stop thermal acceleration via exasignal
BOOLEAN	sEVENT_THERMAL_ACCEL_OFF::process()
{
  //This type of event is defined within the flow realm, so gets the realm timestep 
  TIMESTEP realm_timestep = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(timestep);
  TIMESTEP stop_time;

#if BUILD_D19_LATTICE
  /* Thermal_accel on/off is associated with T_solver switch. Here the event of resetting thermal_accel
   * is proceesed one timestep earlier than real action (on/off) to facilitate seeding for T_solver switch.
   */

  //First check whether accel is ON or not at current step
  asINT32 acc_stime = sim.thermal_accel.start;
  dFLOAT acc_etime = sim.thermal_accel.end_time();
  //  Remove solver switch controlled by T_lb_scalar_solver_start_time
  if(sim.is_prescribed_T_solver_switch && realm_timestep < sim.T_lb_scalar_solver_start_time) {
    g_async_event_queue.remove_recurring_event(EVENT_ID_T_LB_SOLVER_ON);
    sim.is_prescribed_T_solver_switch = FALSE;
  }
  if (sim.thermal_accel.timestep_within_thermal_acc(realm_timestep)){
//    First remove all the recurring thermal solver switch events, as EXASIGNAL takes precedence.
    g_async_event_queue.remove_recurring_event(EVENT_ID_T_PDE_SOLVER_ON);
    if(sim.thermal_accel.switch_back_to_lb_solver())
      g_async_event_queue.remove_recurring_event(EVENT_ID_T_LB_SOLVER_ON);

    asINT32 acc_mtime = (realm_timestep - acc_stime)%sim.thermal_accel.period;
    if(acc_mtime < sim.thermal_accel.interval) { //accel is ON at current step
      sim.thermal_accel.start = realm_timestep;
      sim.thermal_accel.repeat = 1;
      sim.thermal_accel.period = 1;
      sim.thermal_accel.interval = 1;
      g_async_event_queue.add_entry(new_event(EVENT_ID_T_LB_SOLVER_ON, 0, sim.thermal_accel.start, 0, TIMESTEP_LAST, STP_FLOW_REALM));
    } else { //accel is already OFF
      sim.thermal_accel.start  = -1;
      sim.thermal_accel.repeat = 0;
      sim.thermal_accel.period = 0;
      sim.thermal_accel.interval = 0;
    }
  } else { //accel is already OFF
    sim.thermal_accel.start  = -1;
    sim.thermal_accel.repeat = 0;
    sim.thermal_accel.period = 0;
    sim.thermal_accel.interval = 0;
  }

  //for message output
  stop_time = realm_timestep + 1; //add "1" back to stop_time; 1 timestep delay between process and turn off accel 
                                  //allows the seeding for T_solver switch 

  sim.thermal_accel.is_hacked = TRUE;
  sim.thermal_accel.hacked_start = -1;
  sim.thermal_accel.hacked_period = -1;
  sim.thermal_accel.hacked_stop = stop_time;

#else
  sim.thermal_accel.start  = -1;
  sim.thermal_accel.repeat = 0;
  sim.thermal_accel.period = 0;
  sim.thermal_accel.interval = 0;
  stop_time = realm_timestep;

#endif

  if (my_proc_id == 0) {
    cSTRING realm_str = (sim.is_conduction_model) ? "flow " : ""; //no need to specify flow if conduction is inactive
    msg_print_no_prefix ("Thermal solver acceleration will turn off at %stimestep %d", realm_str, stop_time);
  }
  return(TRUE);
}

VOID	sEVENT_THERMAL_ACCEL_OFF::print() {
#if BUILD_D19_LATTICE
  msg_print("THERMAL ACCEL OFF at %s", timestep_to_str(timestep+1));
#else
  msg_print("THERMAL ACCEL OFF at %s", timestep_to_str(timestep));
#endif
}
BOOLEAN	sEVENT_THERMAL_ACCEL_OFF::unique() {return(FALSE);}

BOOLEAN	sEVENT_TIMERS_ON::process() {
  sim.async_event_flags.timers_on_p = TRUE;
  return(TRUE);
}
VOID	sEVENT_TIMERS_ON::print() {
  msg_print("EVENT TIMERS ON at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_TIMERS_ON::unique() {return(FALSE);}

BOOLEAN	sEVENT_TIMERS_OFF::process() {
  sim.async_event_flags.timers_on_p = FALSE;
  return(TRUE);
}
VOID	sEVENT_TIMERS_OFF::print() {
  msg_print("EVENT TIMERS OFF at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_TIMERS_OFF::unique() {return(FALSE);}

BOOLEAN	sEVENT_DIAGS_ON::process() {
  sim.async_event_flags.diag_avg = arg;
  sim.async_event_flags.diags_on_p = TRUE;
  return(TRUE);
}
VOID	sEVENT_DIAGS_ON::print() {
  msg_print("EVENT DIAGNOSTICS ON at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_DIAGS_ON::unique() {return(FALSE);}

BOOLEAN	sEVENT_DIAGS_OFF::process() {
  sim.async_event_flags.diags_on_p = FALSE;
  return(TRUE);
}
VOID	sEVENT_DIAGS_OFF::print() {
  msg_print("EVENT DIAGNOSTICS OFF at %s", timestep_to_str(timestep));
}
BOOLEAN	sEVENT_DIAGS_OFF::unique() {return(FALSE);}

BOOLEAN	sEVENT_MAXVEL::process() {
  sim.set_user_max_vel(arg, FALSE);
  return(TRUE);
}
VOID	sEVENT_MAXVEL::print() {
  msg_print("EVENT MAXVEL %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_MAXVEL::unique() {return(FALSE);}

BOOLEAN	sEVENT_MAXTEMP::process() {
  sim.user_max_temp = arg;
  return(TRUE);
}
VOID	sEVENT_MAXTEMP::print() {
  msg_print("EVENT MAXTEMP %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_MAXTEMP::unique() {return(FALSE);}

BOOLEAN	sEVENT_MINTEMP::process() {
  sim.user_min_temp = arg;
  return(TRUE);
}
VOID	sEVENT_MINTEMP::print() {
  msg_print("EVENT MINTEMP %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_MINTEMP::unique() {return(FALSE);}

BOOLEAN	sEVENT_DELAY_VEL_WARNINGS::process() {
  sim.ignore_vel_n_timesteps = arg;
  return(TRUE);
}
VOID	sEVENT_DELAY_VEL_WARNINGS::print() {
  msg_print("EVENT DELAY_VEL_WARNINGS %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_DELAY_VEL_WARNINGS::unique() {return(FALSE);}

BOOLEAN	sEVENT_DELAY_TEMP_WARNINGS::process() {
  sim.ignore_temp_n_timesteps = arg;
  return(TRUE);
}

VOID	sEVENT_DELAY_TEMP_WARNINGS::print() {
  msg_print("EVENT DELAY_TEMP_WARNINGS %g at %s", arg, timestep_to_str(timestep));
}

BOOLEAN	sEVENT_DELAY_TEMP_WARNINGS::unique() {return(FALSE);}

BOOLEAN	sEVENT_DELAY_UDS_WARNINGS::process() {
  sim.ignore_uds_n_timesteps = arg;
  return(TRUE);
}
VOID	sEVENT_DELAY_UDS_WARNINGS::print() {
  msg_print("EVENT DELAY_UDS_WARNINGS %g at %s", arg, timestep_to_str(timestep));
}
BOOLEAN	sEVENT_DELAY_UDS_WARNINGS::unique() {return(FALSE);}

BOOLEAN	sEVENT_EXIT::process() {
  return process_exit();
}

VOID	sEVENT_EXIT::print() {
  msg_print("EXIT at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_EXIT::unique(VOID) {return(TRUE);}

// Halt events are never placed in the event queue. Instead an exit event is used together with setting the m_halt flag
// in g_strand_mgr. This ensures that there is a single exit-like event in the event queue.
BOOLEAN	sEVENT_HALT::process() {
  return true;
}

VOID	sEVENT_HALT::print() {
  msg_print("HALT at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_HALT::unique(VOID) {return(TRUE);}

BOOLEAN	sEVENT_EVENT_REQ::process() {
  AsyncEventQueuePendingRequest::wait_for_req_finish();
  return(TRUE);
}

VOID	sEVENT_EVENT_REQ::print() {
  msg_print("EVENT_REQ at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_EVENT_REQ::unique(VOID) {return(TRUE);}



BOOLEAN	sEVENT_ENABLE_WARNING::process() {
  simerr_enable_warning(((SP_EEP_TYPES) arg), TRUE);
  return(TRUE);
}

VOID	sEVENT_ENABLE_WARNING::print() {
  msg_print("ENABLE WARNING TYPE %d at %s", (int)(arg), timestep_to_str(timestep));
}

BOOLEAN sEVENT_ENABLE_WARNING::unique(VOID) {return(FALSE);}

BOOLEAN	sEVENT_DISABLE_WARNING::process() {
  simerr_enable_warning(((SP_EEP_TYPES) arg), FALSE);
  return(TRUE);
}

VOID	sEVENT_DISABLE_WARNING::print() {
  msg_print("DISABLE WARNING TYPE %d at %s", (int)(arg), timestep_to_str(timestep));
}

BOOLEAN sEVENT_DISABLE_WARNING::unique(VOID) {return(FALSE);}

BOOLEAN sEVENT_READ_TABLE::process()
{
  if (do_not_process())
    return TRUE; // pointless to read a table if about to exit

  asINT32 table_index = arg;
  TABLE table = sim.tables + table_index;
  
  // Wait for the comm thread to receive it, it it hasn't done so already
  LOG_MSG("VTABLE",LOG_TIME,LOG_FUNC) << "Waiting";

  if ( wait_for_comm_thread_or_exit(table->ready) ) {
    return TRUE;
  }

  LOG_MSG("VTABLE",LOG_TIME,LOG_FUNC) << "Finished Waiting";
  exprlang_deserialize_v_table_in_place(table->serialized_table, units_db, table->vtable);
  table->add_to_eqns();

  // Update all the shob dyn groups that depend on this table.
  for (DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = table->dependent_physics_descs;
       dependent_pd != NULL; dependent_pd = dependent_pd->next) {
    PHYSICS_DESCRIPTOR pd = dependent_pd->physics_desc;
    if (pd->all_parameters_sharable) {
      STP_GEOM_VARIABLE dummy[3] = { 1, 0, 0 };
      pd->eval_space_and_table_varying_parameter_program(dummy, dummy, 
                                                         (pd->is_surface_physics_descriptor() || pd->is_shell_physics_descriptor())? 
                                                         boundary_eqn_error_handler : fluid_eqn_error_handler);
    }
    reinitialize_dynamics_data_for_dependent_surfels(pd);
    reinitialize_dynamics_data_for_dependent_ublks(pd);
  }

  // Update all the ublks that depend on this curved HX table. If it
  // is not a curved HX table, the list of dependent physics descs will be
  // empty.
  for (DEPENDENT_PHYSICS_DESCRIPTOR dependent_pd = table->physics_descs_dependent_via_curved_hx;
       dependent_pd != NULL;
       dependent_pd = dependent_pd->next) {
    POROUS_MEDIA_PHYSICS_DESCRIPTOR pd = (POROUS_MEDIA_PHYSICS_DESCRIPTOR) dependent_pd->physics_desc;

    ccDOTIMES(scale, sim.num_scales) {
      CURVED_HX_POROUS_SHARED_DATA shared_porous_data = 
        sCURVED_HX_POROUS_UBLK_DYNAMICS_DATA::c_curved_hx_porous_shared_data_map.find_shared_data_in_map(pd, scale);
      if (shared_porous_data != NULL)
        shared_porous_data->eval_T_and_H_at_vertices_for_curved_hx();
    }
    reinitialize_dynamics_data_for_dependent_ublks(pd);
  }

  table->ready = FALSE;
  LOG_MSG("VTABLE",LOG_TIME,LOG_FUNC) << "table->ready = FALSE";
  return TRUE;
}

VOID sEVENT_READ_TABLE::print()
{
  asINT32 table_index = arg;
  TABLE table = sim.tables + table_index;

  msg_print("Read table \"%s\" at timestep %s", 
	    table->name,
	    timestep_to_str(timestep));
}

BOOLEAN	sEVENT_READ_TABLE::unique() {return(FALSE);}

VOID sim_finalize_coupling()
{
  ccDOTIMES(i, sim.n_coupling_models) {
    int tag = make_mpi_tag<eMPI_MSG::COUPLING>(i);
    while (1) {
      MPI_Status status;
      int message_available;
      MPI_Iprobe(eMPI_sp_cp_rank(), tag, eMPI_sp_cp_comm, &message_available, &status);

      if (message_available) {
        COUPLING_MODEL coupling_model = sim.coupling_models + i;
        asINT32 data_size = coupling_model->nsurfels * coupling_model->n_vars;
        RECV_EXA_SIM_MSG<sFLOAT> coupling_data(tag, data_size, sim.coupling_data_buf, eMPI_sp_cp_rank());
        g_exa_sp_cp_comm.recv(coupling_data.mpi_msg);
      } else {
        break;
      }
    }
  }
}

VOID sim_finalize_tbs_data()
{
  ccDOTIMES(i, sim.n_seed_from_meas_descs) {
    if(sim.transient_boundary_seeding[i].m_tbs_msg.m_request != MPI_REQUEST_NULL) {
      g_exa_sp_cp_comm.cancel(sim.transient_boundary_seeding[i].m_tbs_msg);
    }
  }
}

BOOLEAN sEVENT_READ_COUPLING_DATA::process()
{
  if(do_not_process())
    return TRUE; // pointless to read a table if about to exit (see note in run.cc)

  asINT32 coupling_model_index = arg;
  COUPLING_MODEL coupling_model = sim.coupling_models + coupling_model_index;
 
  // If there is no surfels for coupling, no need to process since CP does not send any data to this SP at all.
  if (coupling_model->nsurfels > 0) {

    // Wait for the comm thread to receive it, if it hasn't done so already
    if ( wait_for_comm_thread_or_exit(coupling_model->ready) ) {
      return TRUE;
    }

    sFLOAT *data = sim.coupling_data_buf;

    assign_coupling_temp_to_dependent_surfels(coupling_model, data);
  }

  // For supporting variable PowerTherm coupling periods
  // Check if this is the last coupling before the end of current phase
  TIMESTEP realm_timestep = sim.realm_phase_time_info[STP_FLOW_REALM].global_to_realm_timestep(this->timestep);
  ccDO_FROM_TO(phase_index, 1, coupling_model->m_coupling_phase_descs.size()-1)
  {
    TIMESTEP next_period_start = coupling_model->m_coupling_phase_descs[phase_index].m_coupling_time
                               - coupling_model->m_coupling_phase_descs[phase_index].m_delay;
    if (realm_timestep < next_period_start && (realm_timestep + this->recur_period) >= next_period_start)
    {
      this->recur_period = coupling_model->m_coupling_phase_descs[phase_index].m_period;
      // shift the timestep so that the next timestep computed by compute_next_time is the next coupling time
      realm_timestep = coupling_model->m_coupling_phase_descs[phase_index].m_coupling_time
                       - coupling_model->m_coupling_phase_descs[phase_index].m_period;
      this->timestep = sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(realm_timestep);
      // Not break here since the current phase may not have any period and we need to continue searching for the next phase
    }
  }

  // check the last phase
  if ((realm_timestep + this->recur_period) > coupling_model->end_time)
  {
    this->recur_period = -1; // stop the recurring event
  }

  coupling_model->ready = FALSE;
  return TRUE;
}


VOID sEVENT_READ_COUPLING_DATA::print()
{
  asINT32 coupling_model_index = arg;
  COUPLING_MODEL coupling_model = sim.coupling_models + coupling_model_index;

  msg_print("Read data for coupling model \"%s\" at timestep %s", 
	    coupling_model->name,
	    timestep_to_str(timestep));
}

BOOLEAN	sEVENT_READ_COUPLING_DATA::unique() {return(FALSE);}

BOOLEAN sEVENT_ROTATIONAL_DYNAMICS::process()
{
  // pointless to read angular acceleration if about to exit,
  if (do_not_process())
    return TRUE;

  asINT32 rotdyn_index = arg;
  ROTATIONAL_DYNAMICS_DESC rotdyn_desc = sim.rotational_dynamics_descs + rotdyn_index;
  
  // Wait for the comm thread to receive it, it it hasn't done so already
  if ( wait_for_comm_thread_or_exit(rotdyn_desc->ready) ) {
    return TRUE;
  }

  // Everything but the waiting for the ready flag and clearing it is common to both queues

  // check if the time range for rotational dynamics has been exceeded
  if ((this->timestep + this->recur_period) >= rotdyn_desc->end_time) {
    this->recur_period = -1; // stop the recurring event
    rotdyn_desc->lrf->angular_acceleration = 0.0;
  } else {
    rotdyn_desc->lrf->angular_acceleration = rotdyn_desc->angular_acceleration_buffer;
  }

  // Every NUM_TIME_VARYING_TORQUE_VALUES timesteps, update the CP with torque values

  if (my_proc_id == 0 && (rotdyn_desc->is_resistive_torque_time_varying || rotdyn_desc->is_external_torque_time_varying)) {
    rotdyn_desc->count++;
    if (rotdyn_desc->count == NUM_TIME_VARYING_TORQUE_VALUES) {
      LRF_PARAMETERS parms = rotdyn_desc->lrf->parameters();
      ccDOTIMES(i, NUM_TIME_VARYING_TORQUE_VALUES) {
        BASETIME eqn_base_time = sim.realm_phase_time_info[STP_FLOW_REALM].realm_to_global_timestep(rotdyn_desc->eqn_time);
        rotdyn_desc->lrf->eval_time_varying_only_parameter_program(eqn_base_time, g_timescale.m_powertherm_time, global_eqn_error_handler);
        if (rotdyn_desc->is_resistive_torque_time_varying) rotdyn_desc->resistive_torque[i] = parms->resistive_torque.value;
        if (rotdyn_desc->is_external_torque_time_varying) rotdyn_desc->external_torque[i] = parms->external_torque.value;
        rotdyn_desc->eqn_time += this->recur_period;
      }
      if (rotdyn_desc->is_resistive_torque_time_varying) {
        rotdyn_desc->resistive_torque_request = TRUE;
      }
      if (rotdyn_desc->is_external_torque_time_varying) {
        rotdyn_desc->external_torque_request = TRUE;
      }
      rotdyn_desc->count = 0;
    }
  }

  rotdyn_desc->ready = FALSE;
  return TRUE;
}

VOID sEVENT_ROTATIONAL_DYNAMICS::print()
{
  asINT32 lrf_index = arg;
  LRF_PHYSICS_DESCRIPTOR lrf = &sim.lrf_physics_descs[lrf_index];
  msg_print("Read rotational dynamics data for LRF \"%s\" at timestep %s",
            lrf->name,
            timestep_to_str(timestep));
}

BOOLEAN sEVENT_ROTATIONAL_DYNAMICS::unique() {return(FALSE);}

BOOLEAN	sEVENT_CLEAR_AVG_MME_DATA::process() {
  if (g_timescale.m_time == 0) { // Should clear immediately otherwise it will be cleared at timestep 1
    clear_avg_mme_checkpoint_data();
    return (TRUE);
  }
  //process_clear_avg_mme_ckpt_sim_thread();
  clear_avg_mme_checkpoint_data();
  return TRUE;
}

VOID	sEVENT_CLEAR_AVG_MME_DATA::print() {
  msg_print("CLEAR AVG MME CHECKPOINT at %s", timestep_to_str(timestep));
}

BOOLEAN	sEVENT_CLEAR_AVG_MME_DATA::unique() {return(FALSE);}

BOOLEAN sEVENT_AVG_MME_CKPT::process() {
  process_avg_mme_ckpt_sim_thread();
  return TRUE;
}

VOID    sEVENT_AVG_MME_CKPT::print() {
  msg_print("AVG MME CHECKPOINT at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_AVG_MME_CKPT::unique() {return(FALSE);}

BOOLEAN sEVENT_STOP_AVG_MME_CKPT::process() {
#if DEBUG_AVG_MME
  msg_print("remove avg mme ckpt event at timestep %d", sim.time);
#endif
  EVENT_ID ckpt_event_id = g_async_event_queue.remove_recurring_event(EVENT_ID_CLEAR_AVG_MME_DATA);
  if (ckpt_event_id == EVENT_ID_INVALID)
    msg_print("Failed to remove periodic average mme ckpt event");
  ckpt_event_id = g_async_event_queue.remove_recurring_event(EVENT_ID_AVG_MME_CKPT);
  if (ckpt_event_id == EVENT_ID_INVALID)
    msg_print("Failed to remove periodic average mme ckpt event");
  return(TRUE);
}

VOID    sEVENT_STOP_AVG_MME_CKPT::print() {
  msg_print("STOP MME CHECKPOINT at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_STOP_AVG_MME_CKPT::unique() {return(FALSE);}

BOOLEAN sEVENT_RESCHEDULE_AVG_MME_CKPT::process() {
  EVENT_ID ckpt_event_id = g_async_event_queue.remove_recurring_event(EVENT_ID_CLEAR_AVG_MME_DATA);
  if (ckpt_event_id == EVENT_ID_INVALID)
    msg_print("Failed to remove periodic average mme ckpt event");
  ckpt_event_id = g_async_event_queue.remove_recurring_event(EVENT_ID_AVG_MME_CKPT);
  if (ckpt_event_id == EVENT_ID_INVALID)
    msg_print("Failed to remove periodic average mme ckpt event");

  // Avg mme checkpoints periodicity and intervals defined in realm handled by this realm timesteps
  BASETIME period = sim.avg_mme_period;
  BASETIME coarsest_timestep = 1 << (sim.num_scales - 1);
  // The scheduled time could overlap with the current timestep if both are aligning with coarsest_timestep. Use the 
  // next aligned timestep since it may be too late to use the current one.
  BASETIME scheduled_time      = g_timescale.m_time + g_timescale.lcm_time_inc() * coarsest_timestep; // First avg mme clear time
  dFLOAT   avg_mme_time_d      = (dFLOAT) scheduled_time + period;
  BASETIME avg_mme_time        = MIN(avg_mme_time_d, BASETIME_MAX);
  dFLOAT   stop_avg_mme_time_d = (dFLOAT) scheduled_time + sim.avg_mme_duration;
  BASETIME stop_avg_mme_time   = MIN(stop_avg_mme_time_d, BASETIME_MAX);
#if DEBUG_AVG_MME
  msg_print("At timestep %d insert avg mme ckpt event clear time %d out time %d in queue.", sim.time, scheduled_time, scheduled_time + period);
  msg_print("At timestep %d insert stop avg mme at timestep %d in queue.", sim.time, stop_avg_mme_time);
#endif
  g_async_event_queue.add_entry(new_event(EVENT_ID_CLEAR_AVG_MME_DATA, 0, scheduled_time, period, BASETIME_LAST));
  g_async_event_queue.add_entry(new_event(EVENT_ID_AVG_MME_CKPT, 0, avg_mme_time, period, BASETIME_LAST));
  g_async_event_queue.add_entry(new_event(EVENT_ID_STOP_AVG_MME_CKPT, 0, stop_avg_mme_time, -1, BASETIME_LAST));
  return(TRUE);
}

VOID    sEVENT_RESCHEDULE_AVG_MME_CKPT::print() {
  msg_print("RESCHEDULE MME CHECKPOINT at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_RESCHEDULE_AVG_MME_CKPT::unique() {return(FALSE);}

//Paarticle modeling emitter and wiper event
BOOLEAN sEVENT_START_EMITTER::process() {return TRUE;}
VOID sEVENT_START_EMITTER::print() {msg_print("START EMITTER at %s", timestep_to_str(timestep));}
BOOLEAN sEVENT_START_EMITTER::unique() {return FALSE;}
BOOLEAN sEVENT_START_WIPER::process() {return TRUE;}
VOID sEVENT_START_WIPER::print() {msg_print("START WIPER at %s", timestep_to_str(timestep));}
BOOLEAN sEVENT_START_WIPER::unique() {return FALSE;}

BOOLEAN sEVENT_CALIBRATION_RUN::process() {
  sim.prepare_calibration_run = TRUE;
  sim.calibration_params.next_iteration_timestep = timestep - 1;
  g_timescale.m_start_time = timestep + 1;
  return (TRUE);
}

VOID sEVENT_CALIBRATION_RUN::print() {
  msg_print("Calibration run %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_CALIBRATION_RUN::unique() {return(FALSE);}



BOOLEAN sEVENT_UNPACK_TBS_DATA::process() {
  int flag = 0;
  MPI_Status status;
  if(sim.transient_boundary_seeding[arg].m_params.seed_via_mks)
    sim.transient_boundary_seeding[arg].scale_seed_buffer_via_mks_properties(sim.m_seed_from_meas_controls[((asINT32)arg)]);
  else
    sim.transient_boundary_seeding[arg].scale_seed_buffer_via_dimless_properties(sim.m_seed_from_meas_controls[((asINT32)arg)]);
  sim.transient_boundary_seeding[arg].unpack();
  return TRUE;
}

VOID    sEVENT_UNPACK_TBS_DATA::print() {
  msg_print("Unpacking transient boundary data %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_UNPACK_TBS_DATA::unique() {return(FALSE);}

BOOLEAN sEVENT_T_PDE_SOLVER_ON::process() {
  // It is possible that the solver type is still PDE if the solver switch period=interval
  //assert(sim.T_solver_type != PDE);
  if (!sim.is_full_checkpoint_restore){ //PR51203 thermal_accel info should be read from full ckpt
    sim.thermal_accel.acc_on = TRUE;
    sim.thermal_accel.do_T_solver_switch = TRUE;
    sim.thermal_accel.T_solver_switch_time_for_surfel_recv_group = timestep;
    sim.thermal_accel.T_solver_switch_time_for_ublk_recv_group = timestep + 1;
  }
  return (TRUE);
}

VOID    sEVENT_T_PDE_SOLVER_ON::print() {
  msg_print("Thermal accelaration started at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_T_PDE_SOLVER_ON::unique() {return(FALSE);}

BOOLEAN sEVENT_T_LB_SOLVER_ON::process() {
  // It is possible that the solver type is still LB_SCALAR if the solver switch period=interval
  //assert(sim.T_solver_type != LB_SCALAR);
  if (!sim.is_full_checkpoint_restore){ //PR51203 thermal_accel info should be read from full ckpt
    sim.thermal_accel.acc_on = FALSE;
    sim.thermal_accel.do_T_solver_switch = TRUE;
    sim.thermal_accel.T_solver_switch_time_for_surfel_recv_group = timestep;
    sim.thermal_accel.T_solver_switch_time_for_ublk_recv_group = timestep + 1;
  }
  return (TRUE);
}

VOID    sEVENT_T_LB_SOLVER_ON::print() {
  msg_print("Thermal accelaration stopped at %s", timestep_to_str(timestep));
}

BOOLEAN sEVENT_T_LB_SOLVER_ON::unique() {return(FALSE);}


std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, 
                                              dFLOAT arg, 
                                              BASETIME timestep, 
                                              sINT32 recur_period, 
                                              BASETIME recur_end) 
{
  sEVENT_QUEUE_EVENT * evp = NULL;

  switch(id) {
    case EVENT_ID_EVENT_REQ: {
      LOG_MSG("ASYNC",LOG_FUNC,LOG_TS) << "Add new event request event";
      evp = new sEVENT_EVENT_REQ; 
      AsyncEventQueuePendingRequest::set_event_req_pending();
      break;
    }
    case EVENT_ID_MME_CKPT: {
      evp = new sEVENT_MME_CKPT; break;
    }
    case EVENT_ID_MME_CKPT_EXIT: {
      g_strand_mgr.m_halt = false;
      evp = new sEVENT_MME_CKPT_EXIT; break;
    }
    case EVENT_ID_FULL_CKPT: {
      evp = new sEVENT_FULL_CKPT; break;
    }
    case EVENT_ID_FULL_CKPT_EXIT: {
      g_strand_mgr.m_halt = false;
      evp = new sEVENT_FULL_CKPT_EXIT; break;
    }
    case EVENT_ID_CKPT_INTERVAL: {
      evp = new sEVENT_CKPT_INTERVAL; break;
    }
    case EVENT_ID_THERMAL_ACCEL_ON: {
      evp = new sEVENT_THERMAL_ACCEL_ON; break;
    }
    case EVENT_ID_THERMAL_ACCEL_OFF: {
      evp = new sEVENT_THERMAL_ACCEL_OFF; break;
    }
    case EVENT_ID_TIMERS_ON: {
      evp = new sEVENT_TIMERS_ON; break;
    }
    case EVENT_ID_TIMERS_OFF: {
      evp = new sEVENT_TIMERS_OFF; break;
    }
    case EVENT_ID_DIAGS_ON: {
      evp = new sEVENT_DIAGS_ON; break;
    }
    case EVENT_ID_DIAGS_OFF: {
      evp = new sEVENT_DIAGS_OFF; break;
    }
    case EVENT_ID_MAXVEL: {
      evp = new sEVENT_MAXVEL; break;
    }
    case EVENT_ID_MAXTEMP: {
      evp = new sEVENT_MAXTEMP; break;
    }
    case EVENT_ID_MINTEMP: {
      evp = new sEVENT_MINTEMP; break;
    }
    case EVENT_ID_DELAY_VEL_WARNINGS: {
      evp = new sEVENT_DELAY_VEL_WARNINGS; break;
    }
    case EVENT_ID_DELAY_TEMP_WARNINGS: {
      evp = new sEVENT_DELAY_TEMP_WARNINGS; break;
    }
    case EVENT_ID_DELAY_UDS_WARNINGS: {
      evp = new sEVENT_DELAY_UDS_WARNINGS; break;
    }
    case EVENT_ID_EXIT: {
      g_strand_mgr.m_halt = false;
      evp = new sEVENT_EXIT; break;
    }
    case EVENT_ID_HALT: {
      // A halt is treated exactly like an exit except that end-of-simulation events are not processed.
      g_strand_mgr.m_halt = true;
      evp = new sEVENT_EXIT; break;
    }
    case EVENT_ID_ENABLE_WARNING: {
      evp = new sEVENT_ENABLE_WARNING; break;
    }
    case EVENT_ID_DISABLE_WARNING: {
      evp = new sEVENT_DISABLE_WARNING; break;
    }
    case EVENT_ID_READ_TABLE: {
      evp = new sEVENT_READ_TABLE; break;
    }
    case EVENT_ID_READ_COUPLING_DATA: {
      evp = new sEVENT_READ_COUPLING_DATA; break;
    }
    case EVENT_ID_READ_DSM_FILE: {
      evp = new sEVENT_READ_DSM_FILE; break;
    }
    case EVENT_ID_ROTATIONAL_DYNAMICS: {
      evp = new sEVENT_ROTATIONAL_DYNAMICS; break;
    }
    case EVENT_ID_CLEAR_AVG_MME_DATA: {
      evp = new sEVENT_CLEAR_AVG_MME_DATA; break;
    }
    case EVENT_ID_AVG_MME_CKPT: {
      evp = new sEVENT_AVG_MME_CKPT; break;
    }
    case EVENT_ID_STOP_AVG_MME_CKPT: {
      evp = new sEVENT_STOP_AVG_MME_CKPT; break;
    }
    case EVENT_ID_RESCHEDULE_AVG_MME_CKPT: {
      evp = new sEVENT_RESCHEDULE_AVG_MME_CKPT; break;
    }
    case EVENT_ID_START_EMITTER: {
      evp = new sEVENT_START_EMITTER; break;
    }
   case EVENT_ID_START_WIPER: {
      evp = xnew sEVENT_START_WIPER; break;
    }
    case EVENT_ID_T_PDE_SOLVER_ON: {
      evp = new sEVENT_T_PDE_SOLVER_ON; break;
    }
    case EVENT_ID_T_LB_SOLVER_ON: {
      evp = new sEVENT_T_LB_SOLVER_ON; break;
    }
    case EVENT_ID_UNPACK_TBS_DATA: {
      evp = new sEVENT_UNPACK_TBS_DATA; break;
    }
    case EVENT_ID_CALIBRATION_RUN: {
      evp = new sEVENT_CALIBRATION_RUN; break;
    }
/* WORK-WORK-WORK - Might be better to turn these into a warning message and
an exit event */
    case EVENT_ID_INVALID: {
      msg_internal_error("Attempt to create the explicitly invalid event - %d", id);
      return(NULL);
    };
    default: {
      msg_internal_error("Attempt to create unknown event type - %d", id);
      return(NULL);
    };
  }

  evp->timestep = timestep;
  evp->recur_period = recur_period;
  evp->recur_end = recur_end;
  evp->arg = arg;

  return std::unique_ptr<sEVENT_QUEUE_EVENT>(evp);
}

std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, dFLOAT arg, BASETIME timestep) {

  return(new_event(id, arg, timestep, 0, BASETIME_NEVER));
}

std::unique_ptr<sEVENT_QUEUE_EVENT> new_event(EVENT_ID id, 
                                              dFLOAT arg, 
                                              TIMESTEP realm_timestep, 
                                              sINT32 recur_period, 
                                              TIMESTEP recur_realm_end, 
                                              REALM realm) {
  BASETIME timestep = sim.realm_phase_time_info[realm].realm_to_global_timestep(realm_timestep);
  BASETIME recur_end = sim.realm_phase_time_info[realm].realm_to_global_timestep(recur_realm_end);
  return(new_event(id, arg, timestep, recur_period, recur_end));
} 
