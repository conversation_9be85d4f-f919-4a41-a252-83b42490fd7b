#include "../common_sp.h"
#include "../box_advect_gen_headers.h"
#include "../gather_advect.h"

template <BOX_ADVECT_TEMPLATE_PARAMETERS>
VOID d19_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { 

  VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    { //SELF
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 1;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 3;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {2};
          constexpr asINT32 dest_voxels[] = {4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {4};
          constexpr asINT32 dest_voxels[] = {2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {0};
          constexpr asINT32 dest_voxels[] = {6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {6};
          constexpr asINT32 dest_voxels[] = {0};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[0] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 1;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {0};
          constexpr asINT32 dest_voxels[] = {6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {2};
          constexpr asINT32 dest_voxels[] = {4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {4,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[1] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {6};
          constexpr asINT32 dest_voxels[] = {0};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {4};
          constexpr asINT32 dest_voxels[] = {2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {0,2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[2] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 3;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {0};
          constexpr asINT32 dest_voxels[] = {6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {4};
          constexpr asINT32 dest_voxels[] = {2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {2,6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[3] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {6};
          constexpr asINT32 dest_voxels[] = {0};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {2};
          constexpr asINT32 dest_voxels[] = {4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {0,4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[6] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {2};
          constexpr asINT32 dest_voxels[] = {4};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[7] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {4};
          constexpr asINT32 dest_voxels[] = {2};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[8] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {0};
          constexpr asINT32 dest_voxels[] = {6};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = advect_nbrs[9] != nullptr;
    if (is_src_ublk_valid) {
      FILL_PREV_STATES_FOR_NEIGHBOR_UBLK
        {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 1;
          constexpr asINT32 src_voxels[] = {6};
          constexpr asINT32 dest_voxels[] = {0};
          APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS 
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,-1,0}; 

}

