#ifndef SIMENG_DATA_OFFSET_TABLE_OPTS_H
#define SIMENG_DATA_OFFSET_TABLE_OPTS_H
#include "common_sp.h"

namespace SIM_SIZES {
  struct SIM_OPTIONS;
}

struct sSIM_INFO;

struct DATA_OFFSET_TABLE_OPTS {
  cBOOLEAN              is_lb_model;
  cBOOLEAN              is_turb_model;
  cBOOLEAN              is_heat_transfer;
  cBOOLEAN              is_conduction_model;
  cBOOLEAN              is_radiation_model;
  cBOOLEAN              is_scalar_model;
  cBOOLEAN              is_pf_model;
  cBOOLEAN              is_multi_component;
  cBOOLEAN              is_particle_model;
  cBOOLEAN              switch_acous_during_simulation;
  sINT32                n_user_defined_scalars;
  cBOOLEAN              has_avg_mme_window;
  cBOOLEAN              store_frozen_vars;
  cBOOLEAN              is_large_pore;
  cBOOLEAN              use_uds_lb_solver;
  cBOOLEAN              meas_seed_or_write_pres_d19;
  cBOOLEAN              use_implicit_shell_solver;
  cBOOLEAN              use_implicit_solid_solver;

  //Initialized by Simsizes
  DATA_OFFSET_TABLE_OPTS(const SIM_SIZES::SIM_OPTIONS& opts);
  
  ~DATA_OFFSET_TABLE_OPTS();

  //Initialized by the simulator
  DATA_OFFSET_TABLE_OPTS(const sSIM_INFO& sim,cBOOLEAN is_multi_component,
                         cBOOLEAN write_cbf_pressure);
};
#endif
