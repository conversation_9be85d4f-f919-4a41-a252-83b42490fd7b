/* ~~~COPY<PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include "sim.h"
#include "particle_sim.h"
#include "mirror.h"
#include "strand_mgr.h"
#include "particle_comm.h"

sPARCEL_COMM_SEND_FSET g_parcel_comm_send_fset;
sPARCEL_COMM_RECV_FSET g_parcel_comm_recv_fset;

VOID sPARTICLE_SIM::wait_for_parcels_tobe_copied_to_send_buffer() {
  DO_PARCEL_SEND_GROUPS(group) {
    while (!group->is_copied_to_send_buffer())  {
      sp_thread_sleep(MPI_SLEEP_LONG);
    }
    group->reset_copied_to_send_buffer();
  }
}

VOID sPARTICLE_SIM::complete_parcel_recvs() {
  g_strand_mgr.m_recv_channel[PARCEL_RECV_TYPE].complete_pending_recvs();
}

asINT32 create_parcel_send_groups() {
  asINT32 n_send_buffers = 0;
  if ( sim.is_particle_model) {
    for( cNEIGHBOR_SP nsp : g_strand_mgr.m_neighbor_sp_map ) {
      g_parcel_comm_send_fset.find_or_create_group(nsp);
      n_send_buffers++;
    }
  }
  return n_send_buffers;
}

VOID sPARCEL_SEND_GROUP::send(ACTIVE_SOLVER_MASK active_solver_mask)
{
  cassert(m_copied_to_send_buffer == false);
  // make sure the send buffer and the count is available
  complete_mpi_request_while_processing_cp_messages(&m_send_request, MPI_SLEEP_LONG);
  complete_mpi_request_while_processing_cp_messages(&m_second_send_request, MPI_SLEEP_LONG);

  m_send_buffer.clear();

  int n_parcels = m_parcels_to_send.size();

  ccDOTIMES( i, n_parcels) {
    sPARCEL_STATE * parcel = m_parcels_to_send[i];
    parcel->fill_send_buffer(m_send_buffer);
    parcel->change_dynamics_type(ABOUT_TO_BE_DELETED);
    particle_sim.destroy_parcel(parcel, FALSE);
  }

  m_parcels_to_send.clear();
  m_copied_to_send_buffer = true;
  m_n_send_bytes = m_send_buffer.size();

#if defined(_EXA_IMPI)
  MPI_Issend(&m_n_send_bytes, 1, MPI_INT, dest_rank(), eMPI_PARCEL_COUNT_TAG,
            eMPI_sp_cp_comm, &m_send_request);
#else
  MPI_Isend(&m_n_send_bytes, 1, MPI_INT, dest_rank(), eMPI_PARCEL_COUNT_TAG,
            eMPI_sp_cp_comm, &m_send_request);
#endif

  LOG_MSG("STRAND_SWITCHING","n_parcels",n_parcels,"tag", fmt::hex(eMPI_PARCEL_COUNT_TAG), "dest rank", dest_rank()) << "Sending bsurfel count";

  if ( n_parcels > 0 ) {
#if defined(_EXA_IMPI)
    MPI_Issend(&m_send_buffer[0], m_n_send_bytes, MPI_BYTE, dest_rank(),
              eMPI_PARCEL_COMM_TAG, eMPI_sp_cp_comm, &m_second_send_request);
#else
    MPI_Isend(&m_send_buffer[0], m_n_send_bytes, MPI_BYTE, dest_rank(),
              eMPI_PARCEL_COMM_TAG, eMPI_sp_cp_comm, &m_second_send_request);
#endif
    LOG_MSG("STRAND_SWITCHING","size",m_n_send_bytes,"tag", fmt::hex(eMPI_PARCEL_COMM_TAG), "dest rank", dest_rank()) << "Sending parcels";
  }
}

VOID add_parcel_to_send_group(PARCEL_STATE parcel, uINT32 dest_sp) {
  sPARCEL_SEND_GROUP *send_group =
    g_parcel_comm_send_fset.find_send_group(g_strand_mgr.m_neighbor_sp_map.get_nsp(dest_sp));
  cassert( send_group );
  send_group->add_parcel(parcel);

  if(PARCEL_IS_INTERESTING(parcel)) {
    msg_print("parcel %d:%d with dynamics type %d:%s was added to comm group destine for SP%d.",
              parcel->originating_sp,
              parcel->id,
              parcel->dynamics_type,
              parcel->dynamics_name(),
              send_group->m_dest_sp.rank());
  }

#if DEBUG_PARTICLE_SUMMARY
  if(parcel->is_in_fluid())
    g_fluid_parcel_send_count++;
  else
    g_surface_parcel_send_count++;
#endif
}

VOID sPARTICLE_SIM::queue_parcel_sends()
{
  DO_PARCEL_SEND_GROUPS(group) {
    g_strand_mgr.m_send_queue->add_sp_entry(group,0,FALSE);
  }
}

VOID sPARTICLE_SIM::send_parcels_before_ckpt()
{
  DO_PARCEL_SEND_GROUPS(group) {
    group->send(sim.init_solver_mask);
    // reset the copied send buffer flag which the compute thread would have done.
    group->reset_copied_to_send_buffer();
  }
}

VOID  create_parcel_recv_groups() {
  for(auto nsp : g_strand_mgr.m_neighbor_sp_map) {
    g_parcel_comm_recv_fset.find_or_create_group(nsp);
  }
}

VOID sPARCEL_RECV_GROUP::post_recv()
{
  xMPI_Irecv(&m_recvsize, 1, MPI_INT, source_rank(), eMPI_PARCEL_COUNT_TAG, eMPI_sp_cp_comm, &m_recv_request);
  LOG_MSG("STRAND_SWITCHING","tag", fmt::hex(eMPI_PARCEL_COUNT_TAG), "source rank", source_rank()) << "Posting bsurfel count recv";
}

VOID sPARCEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) {
  unpack(active_solver_mask);
}

VOID sPARCEL_RECV_GROUP::unpack(ACTIVE_SOLVER_MASK active_solver_mask)
{
  if (m_recvsize > 0) {
    LOG_MSG("STRAND_SWITCHING","source rank",source_rank(),"size",m_recvsize) << "Unpacking parcel recv";
    std::vector<unsigned char>::iterator it = m_recv_buffer.begin();
    while (it != m_recv_buffer.end()) {
      sPARCEL_STATE *parcel = new sPARCEL_STATE();
      parcel->init(it);

      if (parcel->is_in_fluid()) {
        UBLK home_ublk = ublk_from_id(parcel->m_home_shob_id);
        if (home_ublk == nullptr)
          msg_internal_error("Ublk %d not found", parcel->m_home_shob_id);

        if(PARCEL_IS_INTERESTING(parcel)) {
          msg_print("parcel %d:%d with dynamics type %d:%s was received from from SP%d and is being placed in ublk %d.",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    m_source_sp.rank(),
                    home_ublk->id());
        }

        // Parcels are moved to non mirror ublks on the sending SP
        cassert(!home_ublk->is_mirror());
        cassert(parcel->m_home_voxel < ubFLOAT::N_VOXELS);
        cassert(parcel->m_home_voxel >= 0 );
        cassert(home_ublk->is_fringe());
        if (!home_ublk->fluid_like_voxel_mask.test(parcel->m_home_voxel) ||
            home_ublk->p_data()->fluid_parcel_list[parcel->m_home_voxel] == nullptr) {
          //So we've received a particle that thinks it belongs in a solid ublk...
          STP_COORD voxel_lattice_location[3];
          home_ublk->get_voxel_location(parcel->m_home_voxel, voxel_lattice_location);
          STP_GEOM_VARIABLE voxel_location[3];
          vcopy(voxel_location, voxel_lattice_location);
          asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
#if ENABLE_CONSISTENCY_CHECKS
          std::ostringstream error_string;
          error_string << "Parcel " << parcel->id;
          error_string << " was received to a solid ublk " << parcel->m_home_shob_id;
          error_string << " voxel " << parcel->m_home_voxel;
          error_string << " with fluid_mask " << (int)home_ublk->fluid_like_voxel_mask.get();
          simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, home_ublk->scale(), voxel_location, error_string.str().c_str(),
                                   (dFLOAT)parcel->x[new_state_index][0],
                                   (dFLOAT)parcel->x[new_state_index][1],
                                   (dFLOAT)parcel->x[new_state_index][2]);


          if(parcel->dynamics_type == JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT){
            std::ostringstream error_string_check_parcel_dynamics_type;
            error_string << "Parcel " << parcel->id;
            error_string << " was unpacking from comm " << parcel->m_home_shob_id;
            error_string << " voxel " << parcel->m_home_voxel;
            error_string << " with dynamis_type of JUST_REENTRAINED_EXCLUDE_FROM_FLUID_MEASUREMENT";
          }
#else
          simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, home_ublk->scale(), voxel_location, "particle_comm",
                                   (dFLOAT)parcel->x[new_state_index][0],
                                   (dFLOAT)parcel->x[new_state_index][1],
                                   (dFLOAT)parcel->x[new_state_index][2]);
#endif
          //We have to drop this parcel so add its mass to the count of lost mass.
          particle_sim.destroy_parcel(parcel, TRUE);
        } else {
          home_ublk->p_data()->fluid_parcel_list[parcel->m_home_voxel]->add(parcel);
#if DEBUG_PARTICLE_SUMMARY
          g_fluid_parcel_receive_count++;
#endif
        }
      } else if (parcel->is_on_surface()) {
        SURFEL home_surfel = regular_surfel_from_id(parcel->m_home_shob_id);
        if (home_surfel == nullptr)
          msg_internal_error("Surfel %d not found", parcel->m_home_shob_id);
        //Case 4623 in the physics validation database exhibits surface
        //parcels traveling onto a film only ghost and not being
        //commed in at least ng6X_R2-090 and earlier.

        if(PARCEL_IS_INTERESTING(parcel)) {
          msg_print("parcel %d:%d with dynamics type %d:%s was received from from SP%d and is being placed on surfel %d (is_lrf %d, is_weightless %d) at t %ld.",
                    parcel->originating_sp,
                    parcel->id,
                    parcel->dynamics_type,
                    parcel->dynamics_name(),
                    m_source_sp.rank(),
                    home_surfel->id(),
                    home_surfel->is_lrf(),
                    home_surfel->is_weightless_mlrf(),
                    g_timescale.m_time);
        }

        if (!(home_surfel->is_fringe() || home_surfel->is_film_only_fringe())) {
          msg_internal_error("Surfel %d is not fringe or film-only fringe but received a remote particle", parcel->m_home_shob_id);
        }
        home_surfel->p_data()->surface_parcel_list->add(parcel);
#if DEBUG_PARTICLE_SUMMARY
        g_surface_parcel_receive_count++;
#endif
        if(parcel->dynamics_type ==  COLLIDED_WITH_GHOST_MLRF_FROM_FLUID) {
          //msg_print("Received a fluid parcel that collided with a remote ghost mlrf surfel.\n");
          parcel->change_dynamics_type(IN_FLUID);
        } else if(parcel->dynamics_type ==  COLLIDED_WITH_GHOST_MLRF_FROM_SURFACE) {
          //msg_print("Received a surface parcel that collided with a remote ghost mlrf surfel.\n");
          parcel->change_dynamics_type(ON_SURFACE);
        }
      } else {
        //If we reach this point, delete the parcel since no
        //acceptable container was found for it given its dynamics
        //type. Since we dont have a ublk or surfel centorid, generate
        //the simerror at the parcels position at scale 0.
        asINT32 new_state_index = parcel->relative_timestep_to_state_index(0);
        STP_GEOM_VARIABLE error_location[3];
        vcopy(error_location, parcel->x[new_state_index]);
#if ENABLE_CONSISTENCY_CHECKS
        std::ostringstream error_string;
        error_string << "Parcel " << parcel->id;
        error_string << " was received with unexpected dynamics type " << parcel->dynamics_type;
        simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, 0, error_location, error_string.str().c_str(),
                                 (dFLOAT)parcel->x[new_state_index][0],
                                 (dFLOAT)parcel->x[new_state_index][1],
                                 (dFLOAT)parcel->x[new_state_index][2]);
        
#else
        simerr_report_error_code(SP_EER_PARCEL_CANT_FIND_UBLK, 0, error_location, "particle_comm",
                                 (dFLOAT)parcel->x[new_state_index][0],
                                 (dFLOAT)parcel->x[new_state_index][1],
                                 (dFLOAT)parcel->x[new_state_index][2]);
#endif
        //We have to drop this parcel so add its mass to the count of lost mass.
        particle_sim.destroy_parcel(parcel, TRUE);
      }
    }
  }
  set_unpacked();
  m_solver_timestep_index_mask ^= active_solver_mask;
}

bool sPARCEL_RECV_GROUP::is_recv_request_null()
{
  if (!m_count_received) {
    return m_recv_request == MPI_REQUEST_NULL;
  }
  else {
    bool second_recv_finished = m_second_recv_request == MPI_REQUEST_NULL;
    if (second_recv_finished) {
      m_count_received = false;
    }
    return second_recv_finished;
  }
}

bool sPARCEL_RECV_GROUP::is_recv_ready()
{
  int flag;
  if(!m_count_received) {
    MPI_Test(&m_recv_request, &flag, MPI_STATUS_IGNORE);
    if (flag) // count has been received
      {
        if ( m_recvsize > 0 ) { // post the data receive
          LOG_MSG("STRAND_SWITCHING", "source_rank", source_rank(), "size", m_recvsize) << "Posting second bsurfel recv";
          m_recv_buffer.resize(m_recvsize);
          MPI_Irecv(&m_recv_buffer[0], m_recvsize, MPI_BYTE, source_rank(),
                    eMPI_PARCEL_COMM_TAG, eMPI_sp_cp_comm, &m_second_recv_request);
          m_count_received = true;
          return false; // tell the world that we aren't ready for unpacking
        }
        else {
          LOG_MSG("STRAND_SWITCHING", "source_rank", source_rank()) << "bsurfel recv complete, 0 bsurfels";
          return true; // nothing to unpack, but signal we are done
        }
      }
    else {
      return false; // count has not been received yet
    }
  }
  else { // check if the data is here yet
    MPI_Test(&m_second_recv_request, &flag, MPI_STATUS_IGNORE);
    if (flag) { // data has arrived
      return true; // ready for unpacking
    }
    else { // data has not arrived yet, not ready for unpacking
      return false;
    }
  }
}
