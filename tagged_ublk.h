/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef SIMENG_TAGGED_UBLK_H
#define SIMENG_TAGGED_UBLK_H
#include "simulator_namespace.h"
#include "tagged_ptr.h"

template<typename UBLK_TYPE_TAG>
struct tTAGGED_UBLK_TRAITS {

  constexpr static auto ALIGNMENT = UBLK_TYPE_TAG::is_mblk()? GPU::MINIMUM_CUDA_ALIGNMENT : 32;
  using PTR = void*;
  
  enum ATTRIBUTES {
    UBLK_IS_LAST = 0,
    UBLK_IS_SPLIT,
    UBLK_IS_SCALE_INTERFACE,
    UBLK_HAS_TWO_SETS_OF_STATES_MASK,
    UBLK_IS_COARSE,
    OFFSET_IN_MBLK,
    N
  };

  __HOST__DEVICE__ constexpr static size_t num_bits_for_attrib(ATTRIBUTES a) {
    switch(a) {
    case UBLK_IS_LAST:
    case UBLK_IS_SPLIT:
    case UBLK_IS_SCALE_INTERFACE:
    case UBLK_HAS_TWO_SETS_OF_STATES_MASK:
    case UBLK_IS_COARSE:
      return 1;
    case OFFSET_IN_MBLK:
      return UBLK_TYPE_TAG::is_mblk()? 3 : 0;
    default:
      return 0;
    }
  }
};


template<typename UBLK_TYPE_TAG> class tSCALE_BOX_INTERFACE; 

template<typename UBLK_TYPE_TAG>
class tTAGGED_UBLK;

using TAGGED_UBLK = tTAGGED_UBLK<UBLK_SDFLOAT_TYPE_TAG>;
using TAGGED_MBLK = tTAGGED_UBLK<MBLK_SDFLOAT_TYPE_TAG>;

template<typename UBLK_TYPE_TAG>
class ALIGN_VECTOR tUBLK_VECTOR {
  
 public:

#if BUILD_GPU
 static const size_t ALIGNMENT = 256;
#else 
#if EXA_USE_SSE
 static const size_t ALIGNMENT = 16;
#elif EXA_USE_AVX
 static const size_t ALIGNMENT = 32;
#endif
#endif
 using TAGGED_UBLK_TYPE = tTAGGED_UBLK<UBLK_TYPE_TAG>;
 
 TAGGED_UBLK_TYPE *m_tagged_ublks;
 uINT8 m_n_ublks; //wasted memory

 tUBLK_VECTOR(): m_n_ublks(0), m_tagged_ublks(nullptr) {}

 __HOST__DEVICE__ TAGGED_UBLK_TYPE *tagged_ublks() { return m_tagged_ublks; }

 __HOST__DEVICE__ const TAGGED_UBLK_TYPE *tagged_ublks() const { return m_tagged_ublks; }
 
 __HOST__DEVICE__ asINT32 num_split_ublks() const { return m_n_ublks; }
};

using sUBLK_VECTOR = tUBLK_VECTOR<UBLK_SDFLOAT_TYPE_TAG>;

#if BUILD_GPU
using sUBLK_VECTOR_64 = tUBLK_VECTOR<MBLK_SDFLOAT_TYPE_TAG>;
#endif

template<typename UBLK_TYPE_TAG>
class tTAGGED_UBLK : public tTAGGED_PTR<tTAGGED_UBLK_TRAITS<UBLK_TYPE_TAG>>
{
public:
  enum { 
    N_VOXELS = UBLK_TYPE_TAG::N_VOXELS
  };

  using sTRAITS = tTAGGED_UBLK_TRAITS<UBLK_TYPE_TAG>;
  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using sSCALE_BOX_INTERFACE = tSCALE_BOX_INTERFACE<UBLK_TYPE_TAG>;
  using sUBLK_VECTOR = tUBLK_VECTOR<UBLK_TYPE_TAG>;
  using sTAGGED_PTR = tTAGGED_PTR<tTAGGED_UBLK_TRAITS<UBLK_TYPE_TAG>>;
  
public:

  __HOST__DEVICE__ tTAGGED_UBLK()
  {
  }
  
  __HOST__DEVICE__ tTAGGED_UBLK(sUBLK_TYPE *ublk)
  {
    this->set_ptr(ublk);
  }

  // sets the ublk pointer without modifying the reserved bits
  tTAGGED_UBLK& operator = (std::nullptr_t)
  {
    sTAGGED_PTR::operator=(nullptr);
    return *this;
  }


  // sets the ublk pointer without modifying the reserved bits
  __HOST__DEVICE__ VOID set_ublk(sUBLK_TYPE *ublk) 
  {
    this->set_ptr(ublk);
  }
  
  // sets the ublk pointer without modifying the reserved bits,
  // then sets the has_two_copies bits if the ublk has two copies of states
  VOID set_ublk_and_type(sUBLK_TYPE *ublk) 
  {
    this->set_ptr(ublk);
    if (ublk->has_two_copies()) {
      this->template set<sTRAITS::UBLK_HAS_TWO_SETS_OF_STATES_MASK>(1);
    }
  }
  
  __HOST__DEVICE__  sUBLK_TYPE* ublk_without_assert() 
  {
    return (sUBLK_TYPE *) this->ptr();
  }
  
  __HOST__DEVICE__  sUBLK_TYPE* ublk()
  {
    cassert(!is_ublk_scale_interface());
    cassert(!is_ublk_split());
    return (sUBLK_TYPE*) this->ptr();
  }

  __HOST__DEVICE__ sUBLK_TYPE * ublk() const 
  {
    return const_cast<tTAGGED_UBLK*>(this)->ublk();
  }  

  VOID set_scale_interface(sSCALE_BOX_INTERFACE *scale_interface) 
  {
    this->set_ptr(scale_interface);
    this->template set<sTRAITS::UBLK_IS_SCALE_INTERFACE>(1);
  }
  
  __HOST__DEVICE__ sSCALE_BOX_INTERFACE *interface()
  {
    cassert(is_ublk_scale_interface());
    return (sSCALE_BOX_INTERFACE *) this->ptr();
  }

  __HOST__DEVICE__ const sSCALE_BOX_INTERFACE * interface() const 
  {
    cassert(is_ublk_scale_interface());
    return (const sSCALE_BOX_INTERFACE *) this->ptr();
  }
  
  __HOST__DEVICE__ sUBLK_VECTOR * split_ublks() 
  {
    cassert(is_ublk_split());
    return (sUBLK_VECTOR *) this->ptr();
  }
  
  __HOST__DEVICE__  bool is_ublk() const 
  {
    return !this->is_null();
  }
  
  VOID set_ublk_as_last() 
  {
    this->template set<sTRAITS::UBLK_IS_LAST>(1);
  }
  
  __HOST__DEVICE__  bool is_ublk_last()  const  
  {
    return this->template get<sTRAITS::UBLK_IS_LAST>();
  }
  
  VOID set_ublk_as_split (sUBLK_VECTOR * split_ublks)  
  {
    this->set_ptr(split_ublks);
    this->template set<sTRAITS::UBLK_IS_SPLIT>(1);
  }
  
  __HOST__DEVICE__ bool is_ublk_split () const 
  {
    return this->template get<sTRAITS::UBLK_IS_SPLIT>();
  }
  
  VOID set_ublk_as_coarse ()  
  {
    this->template set<sTRAITS::UBLK_IS_COARSE>(1);
  }
  
  __HOST__DEVICE__ bool is_ublk_coarse () const 
  {
    return this->template get<sTRAITS::UBLK_IS_COARSE>();
  }
  
  VOID unset_ublk_has_two_copies()  
  {
    this->set<sTRAITS::UBLK_HAS_TWO_SETS_OF_STATES_MASK>(0);
  }
  
  VOID set_ublk_has_two_copies()
  {
    this->template set<sTRAITS::UBLK_HAS_TWO_SETS_OF_STATES_MASK>(1);
  }

  __HOST__DEVICE__  bool does_ublk_have_two_copies() const 
  {
    return (is_ublk_scale_interface() || is_ublk_split()) ?
      this->template get<sTRAITS::UBLK_HAS_TWO_SETS_OF_STATES_MASK>() : ublk()->has_two_copies();
  }

  VOID set_ublk_as_scale_interface() 
  {
    this->template set<sTRAITS::UBLK_IS_SCALE_INTERFACE>(1);
  }
  
  __HOST__DEVICE__ bool is_ublk_scale_interface() const 
  {
    return this->template get<sTRAITS::UBLK_IS_SCALE_INTERFACE>();
  }
  
  __HOST__DEVICE__ bool is_simple_fluid_ublk() const 
  {
    return (is_ublk()
            && !is_scale_interface_or_coarse()
            && ublk()->fluid_like_voxel_mask.any());
  }


  __HOST__DEVICE__ bool is_scale_interface_or_coarse() const {
    return this->pointer_and_mask(this->m_ptr,
                                  this->template attribute_mask<sTRAITS::UBLK_IS_SCALE_INTERFACE>() |
                                  this->template attribute_mask<sTRAITS::UBLK_IS_COARSE>());
  }
  __HOST__DEVICE__ bool is_simple_fluid_ublk(uINT32 child_ublk) const 
  {
    return (is_ublk()
            && !is_scale_interface_or_coarse()
            && !ublk()->is_solid(child_ublk));
  }

  bool is_ublk_ghost();

  asINT32 split_ublk_instance(sUBLK_TYPE* ublk);

  template<typename UBLK_FUNC>
  void apply_to_ublk_or_split_ublks(const UBLK_FUNC& f)
  {
    if  ( this->is_null() || this->is_ublk_scale_interface() ) {
      return;
    }
    else if ( this->is_ublk_split() ) {
        sUBLK_VECTOR * split_ublks = this->split_ublks();
        TAGGED_UBLK * t_ublks = split_ublks->m_tagged_ublks;
        asINT32 N = split_ublks->num_split_ublks();
        ccDOTIMES(i, N) {
          f(t_ublks[i].ublk());
        }
    }
    else {
      f(this->ublk());
    }
  }

  __HOST__DEVICE__ uint8_t get_offset_in_mega_block() const
  {
    return this->template get<sTRAITS::OFFSET_IN_MBLK>();
  }


  __HOST__DEVICE__ BOOLEAN operator==(const tTAGGED_UBLK<UBLK_TYPE_TAG>& other) {
    return sTAGGED_PTR::operator==(other);
  }
  
#if BUILD_GPU

  __HOST__DEVICE__ void set_offset_in_mega_block(uintptr_t index) 
  {
    this->template set<sTRAITS::OFFSET_IN_MBLK>(index);
  }

#endif
};

typedef class sTAGGED_SPLIT_FACTOR
{
  public:
  TAGGED_UBLK tagged_neighbor;
  sFLOAT      neighbor_split_factor;
  asINT32     unused32;
} *TAGGED_SPLIT_FACTOR;
#endif
