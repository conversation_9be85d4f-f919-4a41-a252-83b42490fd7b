#ifndef _PARTICLE_EMITTER_GEOMETRY_H_
#define _PARTICLE_EMITTER_GEOMETRY_H_

#include "shob_octree.h"

#include <vector>

typedef enum {POINT, SURFACE, BOX, CYLINDER, NUM_GEOMETRY_TYPES, ANY_TYPE} eGEOMETRY_TYPES;

//----- Emission Geometry Primatives

typedef class sEMISSION_GEOMETRY
{
 protected:
  eGEOMETRY_TYPES m_type;
  std::string m_name;
  asINT32 m_region_or_face_id;
  asINT32 m_csys_id;      //the index to the csys in which the geometry data is provided.
 public:
  sEMISSION_GEOMETRY(eGEOMETRY_TYPES type, const std::string &name, asINT32 region_or_face_id, asINT32 csys_id){
    m_type = type;
    m_name = name;
    m_region_or_face_id = region_or_face_id;
    m_csys_id = csys_id;
  }

  eGEOMETRY_TYPES geometry_type() {return(m_type);}
  asINT32 region_or_face_id() {return(m_region_or_face_id);}

  typedef class sPREDICATE{ //This is used to verify that duplicate instances for the same LGI gemetry aren't created which
                            //in turn ensures that no redundant octrees are created for sEMISSION_VOLUMES and sEMISSION_SURFACES.
  private:
    eGEOMETRY_TYPES m_type;
    asINT32 m_region_or_face_id;
  public:
    sPREDICATE(eGEOMETRY_TYPES type, asINT32 region_or_face_id){
      m_type = type;
      m_region_or_face_id = region_or_face_id;
    }

    //Identity defintion:
    bool operator()(sEMISSION_GEOMETRY* const a) const {
      return (a->m_type == m_type || m_type == ANY_TYPE) && a->m_region_or_face_id == m_region_or_face_id;
    }
  }*PREDICATE;
  asINT32 csys_id() {return m_csys_id;}
  virtual BOOLEAN ublk_touches_emission_geometry(UBLK ublk) = 0;
  virtual VOID sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_points, sPARTICLE_VAR release_spacing[3]) = 0;
  virtual VOID precompute_geometry_chars() = 0;
  virtual BOOLEAN find_voxel(STP_COORD x, STP_COORD y, STP_COORD z, UBLK &ublk_return, sINT8 &voxel_return) = 0;
  virtual VOID transform_position_from_geometry_to_lattice_csys(sPARTICLE_VAR position[3]);
  virtual VOID transform_position_from_lattice_to_geometry_csys(sPARTICLE_VAR position[3]);
  virtual VOID* return_sample_position_info() { return NULL; }
  virtual VOID geometric_center(sPARTICLE_VAR center[3]) = 0;
  virtual sPARTICLE_VAR volume() { return 0.0; }
  virtual sPARTICLE_VAR area() { return 0.0; }
  virtual VOID principle_direction(dFLOAT principle_direction_vector[3]) {}
}* EMISSION_GEOMETRY;



typedef class sEMISSION_VOLUME : public sEMISSION_GEOMETRY{
 protected:
  SUB_OCTREE m_ublk_octree;
 public:
 sEMISSION_VOLUME(eGEOMETRY_TYPES type, std::string name, asINT32 region_or_face_id, asINT32 csys_id) :
  sEMISSION_GEOMETRY(type, name, region_or_face_id, csys_id)
    {
    }

  BOOLEAN find_voxel(STP_COORD x, STP_COORD y, STP_COORD z,  UBLK &ublk_return, sINT8 &voxel_return) {
    ublk_return = m_ublk_octree->find(x ,y, z, voxel_return);

    if(ublk_return == NULL)
      return(FALSE);

    if(!ublk_return->fluid_like_voxel_mask.test(voxel_return))
      return(FALSE);

    return(TRUE);
  }

}* EMISSION_VOLUME;

typedef struct sFIXED_EMISSION_LOCATION {
  UBLK  m_ublk;
  sINT8   m_voxel;
  sdFLOAT m_point[3];
} *FIXED_EMISSION_LOCATION;

typedef struct sFIXED_EMISSION_POINTS {
  BOOLEAN fixed_release_points;
  dFLOAT m_release_spacing[3];
  asINT32 m_csys_id;  //in which csys the fixed points are given == spray_direction_csys_id

  std::vector<FIXED_EMISSION_LOCATION> m_points;

  sFIXED_EMISSION_POINTS()
  {
    fixed_release_points = FALSE;
  }

  sFIXED_EMISSION_POINTS(dFLOAT spacing[], asINT32 csys_id)
  {
    fixed_release_points = TRUE;
    vcopy(m_release_spacing, spacing);
    m_csys_id = csys_id;
  }

  sFIXED_EMISSION_POINTS(const sFIXED_EMISSION_POINTS &fep)
  {
    fixed_release_points = fep.fixed_release_points;
    vcopy(m_release_spacing, fep.m_release_spacing);
    m_csys_id = fep.m_csys_id;
  }

  sFIXED_EMISSION_POINTS& operator=(const sFIXED_EMISSION_POINTS &fep)
  {
    if(&fep == this)
      return *this;

    fixed_release_points = fep.fixed_release_points;
    vcopy(m_release_spacing, fep.m_release_spacing);
    m_csys_id = fep.m_csys_id;

    return *this;
  }

} *FIXED_EMISSION_POINTS;


typedef class sEMISSION_CYLINDER : public sEMISSION_VOLUME {
 private:

  dFLOAT m_centers[2][3]; //each vector is contigious
  dFLOAT m_radii[2];

  dFLOAT m_cylinder_length;
  dFLOAT m_cylinder_axis[3];
  dFLOAT m_cylinder_center[3];
  dFLOAT m_bounding_box_corners[2][3];
  dFLOAT m_bounding_box_size[3];
  dFLOAT m_volume;
  sCSYS m_body_csys;  //the csys aligned to the cylinder axis of symetry (which generally differes from what the face centers may be specified in)

  sFIXED_EMISSION_POINTS emission_points;

 public:
 sEMISSION_CYLINDER(sLGI_CYLINDER_REC &lgi_record, const std::string &name) :
  sEMISSION_VOLUME(CYLINDER, name, lgi_record.region_index, 0) //value of 0 is for the default csys index, lgi_record.csys_id not available
    {
      vcopy(m_centers[0], lgi_record.end_points[0]);
      vcopy(m_centers[1], lgi_record.end_points[1]);
      m_radii[0] = lgi_record.radii[0];
      m_radii[1] = lgi_record.radii[1];

      //Compute the length of the cylinder, the direction of the major axis of the cylinder, the cylinder length, the cylinder volume, and the center of the major axis.
      m_cylinder_length = 0;
      ccDOTIMES(dim, 3) {
        m_cylinder_axis[dim] = m_centers[1][dim] - m_centers[0][dim];
        m_cylinder_length += m_cylinder_axis[dim] * m_cylinder_axis[dim];
      }
      m_cylinder_length = std::sqrt(m_cylinder_length);
      
      ccDOTIMES(dim, 3) {
        m_cylinder_axis[dim] *= 1.0 / m_cylinder_length; //unitized
      }
      m_volume = M_PI / 3.0 * (m_radii[0] * m_radii[0] + m_radii[0] * m_radii[1] + m_radii[1] * m_radii[1]) * m_cylinder_length;
      
      ccDOTIMES(i, 3) {
        m_cylinder_center[i] = m_centers[0][i] + 0.5 * m_cylinder_axis[i] * m_cylinder_length;
      }
    }

  VOID set_emission_points(sFIXED_EMISSION_POINTS &fixed_emission_points)
  {
    emission_points = fixed_emission_points;
  }

  VOID precompute_geometry_chars();
  VOID init_fixed_emission_points();
  BOOLEAN ublk_touches_emission_geometry(UBLK ublk);
  VOID sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_point_spacing[3]);
  VOID transform_position_from_geometry_to_lattice_csys(sPARTICLE_VAR position[3]);
  VOID transform_position_from_lattice_to_geometry_csys(sPARTICLE_VAR position[3]);
  VOID sample_position_with_vector(sPARTICLE_VAR pos[3], sPARTICLE_VAR csys[3][3], sPARTICLE_VAR vec[3]);
  VOID geometric_center(sPARTICLE_VAR center[3]) {
    center[0] = m_cylinder_length/2.0;
    center[1] = 0.0;
    center[2] = 0.0;
    transform_position_from_geometry_to_lattice_csys(center);
  }
  
  sPARTICLE_VAR length() {return m_cylinder_length;}
  sPARTICLE_VAR area() {return M_PI * (m_radii[0] + m_radii[1]) * (m_radii[0] + m_radii[1]) / 4.0;}
  sPARTICLE_VAR volume() {return m_volume;}
  VOID center(dFLOAT output_center[3], asINT32 point_index) { vcopy(output_center, m_centers[point_index]);}
  VOID radii(dFLOAT output_radii[2]) { output_radii[0] = m_radii[0]; output_radii[1] = m_radii[1];}
  VOID principle_direction(dFLOAT normal[]);

} *EMISSION_CYLINDER;

typedef class sEMISSION_BOX : public sEMISSION_VOLUME {
 private:
  dFLOAT m_corners[2][3]; //each vector is contigious

  dFLOAT m_size[3];
  dFLOAT m_center[3];
  dFLOAT m_volume;

  sFIXED_EMISSION_POINTS emission_points;

 public:
 sEMISSION_BOX(sLGI_BOX_REC &lgi_record, const std::string name) :
  sEMISSION_VOLUME(BOX, name, lgi_record.region_index, lgi_record.csys_index) {
    vcopy(m_corners[0], lgi_record.corners[0]);
    vcopy(m_corners[1], lgi_record.corners[1]);

    //Compute the size, center, and volume of the box.
    ccDOTIMES(i,3) {
      m_size[i] = m_corners[1][i] - m_corners[0][i];
      m_center[i] = 0.5 *  (m_corners[1][i] + m_corners[0][i]);
    }
    m_volume = m_size[0] * m_size[1] * m_size[2];
  }

  VOID set_emission_points(sFIXED_EMISSION_POINTS &fixed_emission_points)
  {
    emission_points = fixed_emission_points;
  }

  VOID precompute_geometry_chars();
  VOID init_fixed_emission_points();
  BOOLEAN ublk_touches_emission_geometry(UBLK ublk);
  VOID sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_point_spacing[3]);
  VOID geometric_center(sPARTICLE_VAR center[3]) {
    vcopy(center, m_center);
    transform_position_from_geometry_to_lattice_csys(center);
  }

  sPARTICLE_VAR volume() {return m_volume;}
  sPARTICLE_VAR area() {return std::max(m_size[0] * m_size[1], std::max(m_size[0] * m_size[2], m_size[1] * m_size[2]));}
  VOID size(dFLOAT output_size[3]) { vcopy(output_size, m_size); } 
  VOID principle_direction(dFLOAT principle_direction_vector[3]);
} *EMISSION_BOX;

typedef class sEMISSION_POINTS : public sEMISSION_GEOMETRY{
 private:
  UBLK  m_ublk;
  sINT8   m_voxel;
  //  sdFLOAT m_point[3];
  std::vector<dFLOAT> m_points;

  static asINT32 emission_points_ids; //need to assign id's to each set of emission points.
  SUB_OCTREE m_ublk_octree;

 public:
 sEMISSION_POINTS(const std::string &name, std::vector<dFLOAT> &points) :
  sEMISSION_GEOMETRY(POINT, name, emission_points_ids++ , 1) //csys_index for default csys
    {
      m_points = points;
      if(m_points.size() / 3 <= 0)
        msg_error("More than one emission point must be given");
    }

  VOID geometric_center(sPARTICLE_VAR center[3])  {
    asINT32 num_points = m_points.size() / 3;
    ccDOTIMES(nth_point, num_points) {
      center[0] += m_points[3 * nth_point];
      center[1] += m_points[3 * nth_point + 1];
      center[2] += m_points[3 * nth_point + 2];
    }
    vmul(center, 1/(double)num_points);
  }

  VOID precompute_geometry_chars();
  BOOLEAN ublk_touches_emission_geometry(UBLK ublk);
  VOID sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_point_spacing[3]);
  BOOLEAN find_voxel(STP_COORD x, STP_COORD y, STP_COORD z, UBLK &ublk_return, sINT8 &voxel_return) {
    ublk_return = m_ublk_octree->find(x ,y, z, voxel_return);

    if(ublk_return == NULL)
      return(FALSE);

    if(!ublk_return->fluid_like_voxel_mask.test(voxel_return))
      return(FALSE);

    return(TRUE);
  }

} *EMISSION_POINTS;



//Emission surfaces:
typedef struct sPFACET {
  std::vector<sG3_POINT> m_vertices;  //the list of the vertices of a facet
  sG3_VEC m_normal;
  sG3_POINT m_centroid;
  sdFLOAT m_weight;
  sdFLOAT m_area;
} *PFACET;

typedef class sEMISSION_SURFACE : public sEMISSION_GEOMETRY {
 private:
  SUB_OCTREE m_ublk_octree;
  std::vector<sPFACET> m_facets;

  sPARTICLE_VAR m_bounding_box[2][3]; //the two corners of the bounding box
  sPARTICLE_VAR m_area;
  asINT32 current_face;

  static asINT32 emission_surfaces_ids;
 public:
 sEMISSION_SURFACE(asINT32 region_or_face_id) :
  sEMISSION_GEOMETRY(SURFACE, "unset", region_or_face_id, 0) {
    current_face = -1;
  }

  std::vector<sPFACET> &facets() {return m_facets;}
  VOID geometric_center(sPARTICLE_VAR center[3]){} //Not needed, not implemented
  VOID set_additional_info(std::string &name, asINT32 csys_id, asINT32 num_facets) {
    //emission surfaces are created before the following information is known.
    m_name = name;
    m_csys_id = csys_id;
    m_facets.resize(num_facets);
  }
  VOID precompute_geometry_chars();
  BOOLEAN ublk_touches_emission_geometry(UBLK ublk);
  VOID sample_position(sPARTICLE_VAR pos[3], BOOLEAN fixed_release_points, sPARTICLE_VAR release_point_spacing[3]);
  BOOLEAN find_voxel(STP_COORD x, STP_COORD y, STP_COORD z, UBLK &ublk_return, sINT8 &voxel_return) {
    ublk_return = m_ublk_octree->find(x ,y, z, voxel_return);

    if(ublk_return == NULL)
      return(FALSE);

    if(!ublk_return->fluid_like_voxel_mask.test(voxel_return))
      return(FALSE);

    return(TRUE);
  }
  VOID* return_sample_position_info()
  {
    if(current_face >= 0 && current_face < m_facets.size())
      return &(m_facets[current_face].m_normal);
    return NULL;
  }

  sPARTICLE_VAR area() { return m_area;}
} *EMISSION_SURFACE;

// ------- Storage for a vector of emission geometries ---
template < typename EMISSION_GEOMETRY_TYPE >
class tEMISSION_GEOMETRY_COLLECTION {
 public:
  typename std::vector<EMISSION_GEOMETRY_TYPE*> m_emission_geometries;
  EMISSION_GEOMETRY_TYPE* get_existing_record(asINT32 region_or_face_id) {
    typename std::vector<EMISSION_GEOMETRY_TYPE*>::iterator element = std::find_if(m_emission_geometries.begin(),
                                                                                   m_emission_geometries.end(),
                                                                                   typename EMISSION_GEOMETRY_TYPE::sPREDICATE(ANY_TYPE, region_or_face_id));
    if(element == m_emission_geometries.end())
      return NULL;
    return *element;
  }

  //If a new emission geometry referenced in the CDI file
  //hasn't already been added, create a new element for it.
  //Otherwise, return the reference to the existing element.
  EMISSION_GEOMETRY_TYPE* add_if_unique(EMISSION_GEOMETRY_TYPE &new_geometry) {
    typename std::vector<EMISSION_GEOMETRY_TYPE*>::iterator  it = std::find_if(m_emission_geometries.begin(), m_emission_geometries.end(),
                                                                               typename EMISSION_GEOMETRY_TYPE::sPREDICATE(new_geometry.geometry_type(), new_geometry.region_or_face_id()));
    if(it == m_emission_geometries.end()) {
        m_emission_geometries.push_back(xnew EMISSION_GEOMETRY_TYPE(new_geometry));
        return m_emission_geometries.back();
      }
    return *it;
  }
  size_t size() {return m_emission_geometries.size();}
  EMISSION_GEOMETRY_TYPE& operator[](size_t index) { return *(m_emission_geometries[index]); }
};

typedef tEMISSION_GEOMETRY_COLLECTION<sEMISSION_CYLINDER> sEMISSION_CYLINDERS;
typedef tEMISSION_GEOMETRY_COLLECTION<sEMISSION_BOX> sEMISSION_BOXES;
typedef tEMISSION_GEOMETRY_COLLECTION<sEMISSION_SURFACE> sEMISSION_SURFACES;
typedef tEMISSION_GEOMETRY_COLLECTION<sEMISSION_POINTS> sEMISSION_POINT_SETS;


#endif
