/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Vinit Gupta, Exa Corporation                      Created Fri, Dec 14, 2012
//----------------------------------------------------------------------------
//
#ifndef BOX_ADVECT_H_
#define BOX_ADVECT_H_
#include "common_sp.h"
#include "ublk.h"
#include "fset.h"
#include "advect.h"
#include "vr.h"
#include "simulator_namespace.h"
#include "sim.h"

template<typename UBLK_TYPE_TAG> class tTAGGED_UBLK;
using TAGGED_UBLK = tTAGGED_UBLK<UBLK_SDFLOAT_TYPE_TAG>;

#include "ublk_box.h"

template<>
INLINE bool TAGGED_UBLK::is_ublk_ghost() {
  cassert(!is_ublk_scale_interface());
  if(is_ublk_split ())  {
    sUBLK_VECTOR *split_ublks = this->split_ublks();
    return split_ublks->tagged_ublks()[0].ublk()->is_ghost();
  } else{
    return ublk()->is_ghost();
  }
}

template<>
INLINE asINT32 TAGGED_UBLK::split_ublk_instance(sUBLK_TYPE * ublk) {
  // The instance of the split ublks should be stored instead of computing it
  if (is_ublk_split()) {
    sUBLK_VECTOR *split_ublks = this->split_ublks();
    ccDOTIMES(ns, split_ublks->num_split_ublks()) {
      if (ublk->id() == split_ublks->tagged_ublks()[ns].ublk()->id()) {
	return ns;
      }
    }
    msg_internal_error("No match for ublk %d found at the split ublk site [%d %d %d]", ublk->id(),
                       ublk->location(0),
                       ublk->location(1),
                       ublk->location(2));
  } else {
    return 0;
  }
}

//----------------------------------------------------------------------------
// sUBLK_BOX_FSET
//----------------------------------------------------------------------------

struct UBLK_BOX_ORDER
{
  BOOLEAN operator()(const UBLK_BOX a, const UBLK_BOX b) const
  {
    if (a->m_realm!= b->m_realm)
      return a->m_realm< b->m_realm;

    if (a->m_lrf_index!= b->m_lrf_index)
      return a->m_lrf_index< b->m_lrf_index;

    if(a->m_isolated_domain != b->m_isolated_domain)
      return a->m_isolated_domain  < b->m_isolated_domain;

    if(a->m_box_index != b->m_box_index)
      return a->m_box_index < b->m_box_index;

    return FALSE;
  }
};

typedef class sUBLK_BOX_FSET : public tSP_FSET < UBLK_BOX, UBLK_BOX_ORDER > {
  public:
  UBLK_BOX create_ublk_box(asINT32 scale, REALM realm, asINT32 lrf_index, asINT32 isolated_domain,
                           asINT32 box_index);
  asINT32 find_availble_box_index(asINT32 scale, REALM realm, asINT32 lrf_index, asINT32 isolated_domain);
  UBLK_BOX find_next_ublk_box(asINT32 scale, REALM realm, asINT32 lrf_index,
                              asINT32 isolated_domain, asINT32 box_index);
} *UBLK_BOX_FSET;

extern sUBLK_BOX_FSET g_ublk_box_fset;

#define DO_UBLK_BOXES(group_var) \
  FSET_FOREACH_GROUP(sUBLK_BOX_FSET, g_ublk_box_fset, group_var)

#define DO_UBLK_BOXES_OF_SCALE(group_var, scale)                       \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_BOX_FSET, g_ublk_box_fset, group_var, scale)

//UBLK_BOX assign_ublk_box_after_division(UBLK_BOX ublk_box, UBLK ublk);
UBLK_BOX find_ublk_box_after_division(STP_COORD ublk_loc[3], STP_SCALE scale, REALM realm,
                                      sINT8 lrf_index, uINT8 isolated_domain);

VOID adjust_size_and_location_of_boxes();
VOID create_overlap_on_boxes_boundary();
VOID create_overlap_on_megaboxes_boundary(std::vector<MBLK_BOX> &ublk_boxes);
VOID allocate_ublks_in_boxes();

template<typename UBLK_TYPE_TAG>
static INLINE __HOST__DEVICE__
VOID voxel_neighbors_on_axis_internal(asINT32 axis, tUBLK<UBLK_TYPE_TAG>* ublk,
                                      asINT32 voxel, asINT32 &neighbor_voxel,
                                      asINT32 &forward_face, asINT32 &backward_face,
                                      tTAGGED_UBLK<UBLK_TYPE_TAG> &forward_neighbor,
                                      tTAGGED_UBLK<UBLK_TYPE_TAG> &backward_neighbor,
                                      BOOLEAN &is_forward_neighbor_different_scale,
                                      BOOLEAN &is_backward_neighbor_different_scale,
                                      BOOLEAN &is_forward_neighbor_finer_scale,
                                      BOOLEAN &is_backward_neighbor_finer_scale)
{
  int child_ublk = get_child_ublk_index(voxel);
  int child_voxel = voxel % N_VOXELS_8;
  const auto& box_access = ublk->box_access(child_ublk);
  auto tagged_ublk = box_access.stagged_ublk();
  CONNECT_MASK fluid_connect_mask = ublk->voxel_fluid_connect_mask(voxel);
  neighbor_voxel = neighbor_voxel_along_axis(child_voxel, axis);
  forward_face  = stp_axis_to_pos_face(axis);
  backward_face = stp_axis_to_neg_face(axis);

  sINT16 offsets_f[3] = {0}; offsets_f[axis] =  1;
  sINT16 offsets_b[3] = {0}; offsets_b[axis] = -1;

  if (neighbor_voxel < child_voxel) {
    /* crossed into new ublk on forward face, but not backward face */
    if (fluid_connect_mask.test(forward_face)) {
      forward_neighbor = box_access.forward_neighbor(axis);
      if (forward_neighbor.is_ublk_split()) {
        auto split_ublks = forward_neighbor.split_ublks();
        // Use the first one to assess if it is vr fine or different scale
        forward_neighbor = split_ublks->tagged_ublks()[0];
      }
      is_forward_neighbor_finer_scale     = forward_neighbor.is_ublk_scale_interface();
      is_forward_neighbor_different_scale =
          is_forward_neighbor_finer_scale ||
          (forward_neighbor.is_ublk() && (forward_neighbor.ublk()->is_vr_fine() == 1));
      //reset the forward neighbor
      forward_neighbor = box_access.forward_neighbor(axis);
    } else {
      forward_neighbor.set_ublk(NULL);
      is_forward_neighbor_different_scale = FALSE;
      is_forward_neighbor_finer_scale = FALSE;
    }

    is_backward_neighbor_different_scale = FALSE;
    is_backward_neighbor_finer_scale = FALSE;
    if (fluid_connect_mask.test(backward_face)) {
      backward_neighbor = tagged_ublk;
    } else {
      backward_neighbor.set_ublk(NULL);
    }
  } else {
    /* crossed into new ublk on backward face, but not forward face */
    is_forward_neighbor_different_scale = FALSE;
    is_forward_neighbor_finer_scale = FALSE;
    if (fluid_connect_mask.test(forward_face)) {
      forward_neighbor = tagged_ublk;
    } else {
      forward_neighbor.set_ublk(NULL);
    }

    if (fluid_connect_mask.test(backward_face)) {
      backward_neighbor = box_access.backward_neighbor(axis);
      if (backward_neighbor.is_ublk_split()) {
        auto split_ublks = backward_neighbor.split_ublks();
        // Use the first one to assess if it is vr fine or different scale
        backward_neighbor = split_ublks->tagged_ublks()[0];
      }
      is_backward_neighbor_finer_scale     =  backward_neighbor.is_ublk_scale_interface();
      is_backward_neighbor_different_scale =
        is_backward_neighbor_finer_scale ||
        (backward_neighbor.is_ublk() && (backward_neighbor.ublk()->is_vr_fine() == 1));
      //reset the backward neighbor
      backward_neighbor = box_access.backward_neighbor(axis);
    } else {
      backward_neighbor.set_ublk(NULL);
      is_backward_neighbor_different_scale = FALSE;
      is_backward_neighbor_finer_scale = FALSE;
    }
  }
}

static INLINE __HOST__DEVICE__
VOID voxel_neighbors_on_axis(asINT32 axis, sUBLK* ublk,
                             asINT32 voxel,
                             asINT32 &neighbor_voxel,
                             asINT32 &forward_face, asINT32 &backward_face,
                             TAGGED_UBLK &forward_neighbor,
                             TAGGED_UBLK &backward_neighbor,
                             BOOLEAN &is_forward_neighbor_different_scale,
                             BOOLEAN &is_backward_neighbor_different_scale,
                             BOOLEAN &is_forward_neighbor_finer_scale,
                             BOOLEAN &is_backward_neighbor_finer_scale)
{

  voxel_neighbors_on_axis_internal(axis, ublk, voxel, neighbor_voxel,
                                   forward_face,
                                   backward_face,
                                   forward_neighbor,
                                   backward_neighbor,
                                   is_forward_neighbor_different_scale,
                                   is_backward_neighbor_different_scale,
                                   is_forward_neighbor_finer_scale,
                                   is_backward_neighbor_finer_scale);
}

template<typename UBLK_TYPE_TAG>
static INLINE __HOST__DEVICE__
VOID voxel_neighbors_on_axis(asINT32 axis, tUBLK<UBLK_TYPE_TAG>* ublk,
                             asINT32 voxel,
                             asINT32 &forward_neighbor_voxel,
                             asINT32 &backward_neighbor_voxel,
                             asINT32 &forward_face, asINT32 &backward_face,
                             tTAGGED_UBLK<UBLK_TYPE_TAG> &forward_neighbor,
                             tTAGGED_UBLK<UBLK_TYPE_TAG> &backward_neighbor,
                             BOOLEAN &is_forward_neighbor_different_scale,
                             BOOLEAN &is_backward_neighbor_different_scale,
                             BOOLEAN &is_forward_neighbor_finer_scale,
                             BOOLEAN &is_backward_neighbor_finer_scale)
{

  asINT32 neighbor_voxel;
  voxel_neighbors_on_axis_internal(axis, ublk,
                                   voxel,
                                   neighbor_voxel,
                                   forward_face,
                                   backward_face,
                                   forward_neighbor,
                                   backward_neighbor,
                                   is_forward_neighbor_different_scale,
                                   is_backward_neighbor_different_scale,
                                   is_forward_neighbor_finer_scale,
                                   is_backward_neighbor_finer_scale);

  forward_neighbor_voxel = backward_neighbor_voxel = neighbor_voxel;
  if constexpr (!is_microblock<UBLK_TYPE_TAG>()) {
    //If the neighbor happens to be a scale interface or a split ublk, nothing should get added
    if (forward_neighbor.is_ublk()) {
      forward_neighbor_voxel += (forward_neighbor.get_offset_in_mega_block() << 3);
    }
    if (backward_neighbor.is_ublk()) {
      backward_neighbor_voxel += (backward_neighbor.get_offset_in_mega_block() << 3);
    }
  }
}

VOID same_scale_voxel_neighbors_on_axis(asINT32 axis, UBLK ublk,
                                        asINT32 voxel, asINT32 &neighbor_voxel,
                                        TAGGED_UBLK &forward_neighbor,
                                        TAGGED_UBLK &backward_neighbor);

VOID do_voxel_diagonal_neighbors(asINT32 axis, TAGGED_UBLK *tagged_ublk,
                                 UBLK_BOX ublk_box, asINT32 voxel,
                                 asINT32 neighbor_voxel,
                                 asINT32 forward_face, asINT32 backward_face,
                                 TAGGED_UBLK forward_neighbor,
                                 TAGGED_UBLK backward_neighbor,
                                 BOOLEAN &are_neighbors_different_scale,
                                 asINT32 diag_voxel[2],
                                 TAGGED_UBLK diag_neighbors[3][2][2]);

VOID voxel_neighbors_along_states_2(UBLK dest_ublk, asINT32 voxel,
                                    TAGGED_UBLK neighbor_ublks[N_LATTICE_VECTORS_D25]);

VOID split_voxel_neighbors_along_state_directions(UBLK ublk, asINT32 voxel,
                                            TAGGED_UBLK neighbor_ublks[N_MOVING_STATES],
                                            BOOLEAN for_advect);

asINT32 neighbor_voxel_along_state_direction(asINT32 voxel, asINT32 latvec);
asINT32 neighbor_voxel_along_state_direction_2D(asINT32 voxel, asINT32 latvec);

VOID ublk_neighbors_on_axis(asINT32 axis, UBLK ublk,
                                   asINT32 voxel, asINT32 &neighbor_voxel,
                                   asINT32 &forward_face, asINT32 &backward_face,
                                   UBLK &forward_ublk,
                                   UBLK &backward_ublk,
                                   BOOLEAN &is_forward_neighbor_different_scale,
                                   BOOLEAN &is_backward_neighbor_different_scale,
                                   BOOLEAN &is_forward_neighbor_finer_scale,
                                   BOOLEAN &is_backward_neighbor_finer_scale,
				   BOOLEAN &is_forward_neighbor_2_different_scale,
				   BOOLEAN &is_backward_neighbor_2_different_scale);
VOID assign_scale_interface();
VOID divide_ublk_boxes();
VOID update_fluid_connect_masks(UBLK_BOX ublk_box, TAGGED_UBLK *tagged_ublk, UBLK ublk);

asINT32 tagged_neighbor_from_split_ublk_site(TAGGED_UBLK tagged_neighbor,
                                             uINT8 advect_from_split_mask,
                                             asINT32 voxel, asINT32 latvec,
                                             const sSPLIT_ADVECT_INFO *split_advect_info,
                                             asINT32 dest_ublk_instance,
                                             TAGGED_SPLIT_FACTOR split_factors);


// neighbors of split ublks, fluid_like_voxel_mask is not verified. This is
// done in the calling location. During advection, only those connected are
// stored in split_advect_factors.
// Called from voxel_grads_d19.cc during pressure gradient
// Called from box_advect.cc during V2V advection
// Called from ublk_table.cc during initialization
// The connect_voxel_only=FALSE is only used by functions check_ublk_neighbors() 
// since we need to check all neighbors even if they are not connected. See
// comments in shob_groups.cc
template <BOOLEAN for_advect, BOOLEAN is_2D>
INLINE
VOID voxel_neighbors_along_state_directions(UBLK dest_ublk, asINT32 voxel,
                                            TAGGED_UBLK neighbor_ublks[N_MOVING_STATES],
                                            const sBOX_ACCESS& box_access, TAGGED_UBLK tagged_ublk,
                                            BOOLEAN connected_voxel_only = TRUE)
{
  const static sINT16 ublk_voxel_offset_map[6] = {-1, -1, 0, 0, 1, 1};
  CONNECT_MASK fluid_connect_mask = dest_ublk->voxel_fluid_connect_mask(voxel);
  // These are coordinates of a voxel in ublk + MAX_STATE
  // This is done to make the smallest value 0, since this is later used as an
  // index in the ublk_voxel_offset_map.
  auINT16 voxel_cs[3];
  voxel_cs[0] = ((voxel >> 2) & 1) + 2;
  voxel_cs[1] = ((voxel >> 1) & 1) + 2;
  voxel_cs[2] = ((voxel     ) & 1) + 2;
  ccDOTIMES (latvec, N_MOVING_STATES) {
    if (is_2D && (state_vz(latvec) != 0)) {
      if (for_advect) {
        if (state_vx(latvec) && state_vy(latvec)) {
          //24, 26 ---> 12
          //25, 27 ---> 13
          //28, 30 ---> 14
          //29, 31 ---> 15
          asINT32 latvec_0;
          switch (latvec) {
          case 24:
          case 26:
            latvec_0 = 12;
            break;
          case 25:
          case 27:
            latvec_0 = 13;
            break;
          case 28:
          case 30:
            latvec_0 = 14;
            break;
          case 29:
          case 31:
            latvec_0 = 15;
            break;
          }
          neighbor_ublks[latvec] = neighbor_ublks[latvec_0];
        } else if (state_vx(latvec)) {
          asINT32 latvec_0 = (1 - state_vx(latvec)) >> 1;
          neighbor_ublks[latvec] = neighbor_ublks[latvec_0];
        }
        else if (state_vy(latvec)) {
          asINT32 latvec_0 = ((1 - state_vy(latvec)) >> 1) + 2;
          neighbor_ublks[latvec] = neighbor_ublks[latvec_0];
        }
      }
      continue;
    }
    asINT32 diag_voxel = neighbor_voxel_along_state_direction(voxel, latvec);
    BOOLEAN is_connected = connected_voxel_only ? fluid_connect_mask.test(latvec) : TRUE;
    if (is_connected) {
      sINT16 v_offsets[3] = {0};
      v_offsets[0] = state_vx(latvec) + voxel_cs[0];
      v_offsets[1] = state_vy(latvec) + voxel_cs[1];
      v_offsets[2] = state_vz(latvec) + voxel_cs[2];

      if ((v_offsets[0] < 2) || (v_offsets[0] > 3) ||
          (v_offsets[1] < 2) || (v_offsets[1] > 3) ||
          (v_offsets[2] < 2) || (v_offsets[2] > 3) ) {
        sINT16 u_offsets[3] = {0};
        u_offsets[0] = ublk_voxel_offset_map[v_offsets[0]];
        u_offsets[1] = ublk_voxel_offset_map[v_offsets[1]];
        u_offsets[2] = ublk_voxel_offset_map[v_offsets[2]];
        if (is_2D && (u_offsets[2] < 0)) {
          continue;
        }
#if DEBUG
        TAGGED_UBLK tagged_neighbor = box_access.neighbor_ublk(u_offsets);
        // mirror ublks do not contain any sensible information other than states
        // So we return NULL
        if (tagged_neighbor.is_ublk_split()) {
          neighbor_ublks[latvec]  = tagged_neighbor;
        } else if (tagged_neighbor.is_ublk() &&
                   !tagged_neighbor.is_ublk_scale_interface()) {
          UBLK neighbor_ublk = tagged_neighbor.ublk();
          // Since the connect_mask is valid, there should be no need to
          // verify the fluid_like_voxel_mask and vr_fine ublks have a full
          // fluid_like_voxel_mask
          if (!(neighbor_ublk->fluid_like_voxel_mask.test(diag_voxel)) && connected_voxel_only) {
            msg_internal_error("nb_ublk %d fluid_mask %x diag_voxel %d latvec %d src_voxel %d", neighbor_ublk->id(),
                               neighbor_ublk->fluid_like_voxel_mask.get(), diag_voxel, latvec, voxel);
          }
          /*if (((neighbor_ublk->fluid_like_voxel_mask >> diag_voxel) & 1) ||
              (neighbor_ublk->is_vr_fine())) {
          }*/
          /*if (!for_advect && neighbor_ublk->is_mirror()) {
            neighbor_ublks[latvec].set_ublk(NULL);
          } else */
          {
            neighbor_ublks[latvec]  = tagged_neighbor;
          }
        } else {
          neighbor_ublks[latvec]  = tagged_neighbor;
        }
#else
        neighbor_ublks[latvec] = box_access.neighbor_ublk(u_offsets);
#endif
      } else {
#if DEBUG
        if (tagged_ublk.is_ublk_split()) {
          neighbor_ublks[latvec]  = tagged_ublk;
        } else {
          // Since the connect_mask is valid, there should be no need to
          // verify the fluid_like_voxel_mask
          if (connected_voxel_only && !dest_ublk->fluid_like_voxel_mask.test(diag_voxel)) {
            msg_warn("ublk %d fluid_mask %x diag_voxel %d latvec %d src_voxel %d", dest_ublk->id(),
                               dest_ublk->fluid_like_voxel_mask.get(), diag_voxel, latvec, voxel);
          }
          neighbor_ublks[latvec]  = tagged_ublk;
        }
#else
        neighbor_ublks[latvec]  = tagged_ublk;
#endif
      }
    }
  }
}


template <BOOLEAN is_2D>
INLINE
VOID collect_pressure_from_state_neighbors(UBLK dest_ublk, asINT32 voxel,
                                           const sBOX_ACCESS& box_access,
                                           asINT32 voxel_grad_timestep_index,
                                           sdFLOAT pressure_at,
                                           sdFLOAT pfluid_neighbors[N_MOVING_STATES],
                                           sdFLOAT pressure_neighbors[N_MOVING_STATES])
{
  const static sINT16 ublk_voxel_offset_map[6] = {-1, -1, 0, 0, 1, 1};
  CONNECT_MASK fluid_connect_mask = dest_ublk->voxel_fluid_connect_mask(voxel);
  // These are coordinates of a voxel in ublk + MAX_STATE
  // This is done to make the smallest value 0, since this is later used as an
  // index in the ublk_voxel_offset_map.
  auINT16 voxel_cs[3];
  voxel_cs[0] = ((voxel >> 2) & 1) + 2;
  voxel_cs[1] = ((voxel >> 1) & 1) + 2;
  voxel_cs[2] = ((voxel     ) & 1) + 2;
  ccDOTIMES (latvec, N_MOVING_STATES) {
    if (is_2D && (state_vz(latvec) != 0)) {
      continue;
    }
    if (fluid_connect_mask.test(latvec)) {
      asINT32 diag_voxel = neighbor_voxel_along_state_direction(voxel, latvec);
      sINT16 v_offsets[3] = {0};
      v_offsets[0] = state_vx(latvec) + voxel_cs[0];
      v_offsets[1] = state_vy(latvec) + voxel_cs[1];
      v_offsets[2] = state_vz(latvec) + voxel_cs[2];

      if ((v_offsets[0] < 2) || (v_offsets[0] > 3) ||
          (v_offsets[1] < 2) || (v_offsets[1] > 3) ||
          (v_offsets[2] < 2) || (v_offsets[2] > 3) ) {
        sINT16 u_offsets[3] = {0};
        u_offsets[0] = ublk_voxel_offset_map[v_offsets[0]];
        u_offsets[1] = ublk_voxel_offset_map[v_offsets[1]];
        u_offsets[2] = ublk_voxel_offset_map[v_offsets[2]];
        if (is_2D && (u_offsets[2] < 0)) {
          continue;
        }
        TAGGED_UBLK tagged_neighbor_ublk = box_access.neighbor_ublk(u_offsets);
        UBLK  neighbor_ublk = tagged_neighbor_ublk.ublk();
        if (neighbor_ublk == NULL) {
          msg_internal_error("neighbor of ublk %d at [%d %d %d] voxel %d latvec %d uoff %d %d %d", dest_ublk->id(), dest_ublk->location(0), dest_ublk->location(1), dest_ublk->location(2),
                             voxel, latvec, u_offsets[0], u_offsets[1], u_offsets[2]);
        }
        {
          if (neighbor_ublk->is_near_surface()) {
            pfluid_neighbors[latvec] = neighbor_ublk->surf_geom_data()->pfluids[diag_voxel];
          } else {
            pfluid_neighbors[latvec] = 1.0;
          }
          pressure_neighbors[latvec] = neighbor_ublk->lb_data()->m_lb_data[voxel_grad_timestep_index].pressure[diag_voxel];
        }
      } else {
        pfluid_neighbors[latvec] = dest_ublk->surf_geom_data()->pfluids[diag_voxel];
        pressure_neighbors[latvec] = dest_ublk->lb_data()->m_lb_data[voxel_grad_timestep_index].pressure[diag_voxel];
      }
    } else {
      pfluid_neighbors[latvec] = 0.0;
      pressure_neighbors[latvec] = pressure_at;
    }
  }
}


INLINE VOID prefetch_states(char *states_begin) {
  // This leads to a 4% improvement for simple duct case
  _mm_prefetch((const char *)states_begin, _MM_HINT_T1);
}

typedef enum class box_advect_states_index:bool{
 curr_src_states = true,
 prev_src_states = false,
} box_advect_states_index;

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
#ifdef DEBUG
static
#else
static INLINE
#endif
VOID src_states_from_tagged_ublk(UBLK dest_ublk, TAGGED_UBLK src_tagged_ublk,
                                 uINT64 *src_states,
                                 uINT64 *src_states_t,
                                 uINT64 *src_states_mc,
				 VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS],
                                 BOOLEAN is_timestep_even,
                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                 ACTIVE_SOLVER_MASK active_solver_mask,
                                 box_advect_states_index states_index = box_advect_states_index::prev_src_states) {
  sUBLK* src_ublk = src_tagged_ublk.ublk();
  if (src_ublk->has_two_copies()) {
    // explode is done on even timesteps
    explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
    if ( states_index == box_advect_states_index::curr_src_states ) {
      *src_states = (uINT64)(src_ublk->lb_states(1^lb_index_from_mask(prior_solver_index_mask))->m_states);
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        *src_states_mc = (uINT64)(src_ublk->mc_data()->mc_states_data(1^lb_index_from_mask(prior_solver_index_mask))->m_states_mc);
      }
#else
      if (ADVECT_TEMP) {
        *src_states_t = (uINT64)(src_ublk->t_data()->lb_t_data(1^t_index_from_mask(prior_solver_index_mask))->m_states_t);
      }      
#endif
      if (ADVECT_UDS) {
	asINT32 uds_index  = 1^uds_index_from_mask(prior_solver_index_mask);
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  src_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS])(src_ublk->uds_data(nth_uds)->lb_uds_data(uds_index)->m_states_uds); 
      }
    } else {
      *src_states = (uINT64)(src_ublk->lb_states(lb_index_from_mask(prior_solver_index_mask))->m_states);
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        *src_states_mc = (uINT64)(src_ublk->mc_data()->mc_states_data(lb_index_from_mask(prior_solver_index_mask))->m_states_mc);
      }
#else
      if (ADVECT_TEMP) {
        *src_states_t = (uINT64)(src_ublk->t_data()->lb_t_data(t_index_from_mask(prior_solver_index_mask))->m_states_t);
      }      
#endif
      if (ADVECT_UDS) {
	asINT32 uds_index  = uds_index_from_mask(prior_solver_index_mask);
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  src_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS])(src_ublk->uds_data(nth_uds)->lb_uds_data(uds_index)->m_states_uds); 
      }
    } //if - for_current_timestep
  } else { //does_ublk_have_two_copies
    *src_states = (uINT64) (src_ublk->lb_states(ONLY_ONE_COPY)->m_states);
#if BUILD_5G_LATTICE
    if (g_is_multi_component) {
      *src_states_mc = (uINT64)(src_ublk->mc_data()->mc_states_data(ONLY_ONE_COPY)->m_states_mc);
    }
#else
    if (ADVECT_TEMP) {
      *src_states_t = (uINT64)(src_ublk->t_data()->lb_t_data(ONLY_ONE_COPY)->m_states_t);
    }    
#endif
    if (ADVECT_UDS) {
      ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	src_states_uds[nth_uds] = (VOXEL_STATE (*)[ubFLOAT::N_VOXELS])(src_ublk->uds_data(nth_uds)->lb_uds_data(ONLY_ONE_COPY)->m_states_uds); 
    }
  }
#ifdef DEBUG_CONDUCTION_SOLVER
    if (dest_ublk->id()==2665){
      msg_print("T %ld src_ublk %d src_states_t[3][0] %g", g_timescale.m_time, src_ublk->id(), src_ublk->t_data()->lb_t_data(t_index_from_mask(prior_solver_index_mask))->m_states_t[3][0]);
    }
#endif
}

INLINE
static BOOLEAN is_ublk_valid_for_gather_advect(TAGGED_UBLK t_ublk, BOOLEAN is_timestep_even)
{
  return (t_ublk.is_simple_fluid_ublk()
          && t_ublk.ublk()->need_to_push_states(is_timestep_even));
};          


/*=================================================================================================
 * @fcn collect_advect_neighbors
 * This function fills an array of UBLK neighbors that participate in advection and 
 * prefetches states that will be advected immediately after setting up the dcache.
 * The idea is that the memory controller will work to have this data in cache by the time
 * we are ready to advect, thus preventing L1 cache misses for state access.
 *
 * Size of prefetch data per state type (LB, TEMP, MC) for the D19 lattice, single precision:
 * ( 18 latvecs * 8 voxels * 4 bytes/float + 6 cart nbrs * 5 latvecs * 8 voxels/latvec * 4 bytes/float
 *  + 12 diag nbrs * 1 latvec * 8 voxels/latvec * 4 bytes/float
 *
 * = 1920 bytes or 1.875 Kb
 *
 * Given that size of L1 cache is about 32 Kb, and size of dcache is much smaller than L1 cache,
 * it seems reasonable to believe that setting up the dcache should not boot prefetched state values
 * out of the cache since there should be other stale data prior to the prefetch that should be evicted
 * first
 *
 * This function has shown to provide an enormous advection performance boost (numbers depend on processor
 * and lattice type, but range from 5 - 50 %)
*================================================================================================*/
#if BUILD_D19_LATTICE || BUILD_5G_LATTICE
template<BOOLEAN IS_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN IS_SWAP_ADVECT, BOOLEAN PREFETCH_ACTIVE = TRUE>
INLINE VOID collect_advect_neighbors(UBLK pde_advect_ublk,
				     SOLVER_INDEX_MASK prior_solver_index_mask,
				     BOOLEAN is_timestep_even,
				     UBLK (&active_nbrs) [N_MOVING_STATES]) {

  const auto& box_access = pde_advect_ublk->m_box_access;

  const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);

  //Same UBLK
  if constexpr(PREFETCH_ACTIVE) {
#if BUILD_DOUBLE_PRECISION
    constexpr asINT32 incr = 1;
#else
    constexpr asINT32 incr = 2;
#endif
    const asINT32 p_lb_index = pde_advect_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
    const asINT32 p_t_index  = pde_advect_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
    const asINT32 p_uds_index  = pde_advect_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
    for(asINT32 latvec = 0; latvec < N_MOVING_STATES; latvec += incr){
      prefetch_states((char *) &pde_advect_ublk->lb_states(p_lb_index)->m_states[latvec][0]);
      if constexpr(ADVECT_TEMP){
        prefetch_states((char *) &pde_advect_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[latvec][0]);
      }
      if constexpr(ADVECT_UDS){
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
	  prefetch_states((char *) &pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[latvec][0]);
      }
#if BUILD_5G_LATTICE
      if (g_is_multi_component) {
        prefetch_states((char *) &pde_advect_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[latvec][0]);
      }
#endif
    }
  }

  //+X
  {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(0);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along X
    if constexpr (IS_SWAP_ADVECT) {
      is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[0] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[1][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[7][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[9][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[10][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);

        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[1][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[7][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[9][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[10][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
        }

	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[1][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[7][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[9][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[10][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
	  }
        }

#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[1][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[7][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[9][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[10][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[12][0]);
        }
#endif
      }
    }
  }

  //-X
  {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(0);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[1] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	//if (pde_advect_ublk->is_near_surface())
          //prefetch_states((char *) src_ublk->lb_data()->m_lb_data[prev_lb_index].pressure);	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[0][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[6][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[8][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[11][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[0][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[6][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[8][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[11][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
        }
	
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[0][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[6][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[8][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[11][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
	  }
        }

#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[0][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[6][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[8][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[11][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[13][0]);
        }
#endif
      }
    }      
  }

  //+Y
  {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(1);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along Y
    if constexpr (IS_SWAP_ADVECT) {
	is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[2] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[3][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[6][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[9][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[3][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[6][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[9][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[3][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[6][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[9][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);
	  }
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[3][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[6][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[9][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[14][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[17][0]);
        }
#endif
      }
    }
  }

  //-Y
  {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(1);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[3] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[2][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[7][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[8][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[2][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[7][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[8][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
        }
	if constexpr(ADVECT_UDS) {
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[2][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[7][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[8][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);
	  }	
	}
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[2][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[7][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[8][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[15][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[16][0]);
        }
#endif
      }
    }
  }

  //+Z
  if constexpr (!IS_2D) {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(2);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along Z
    if constexpr (IS_SWAP_ADVECT) {
	is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[4] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[5][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[11][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[5][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[11][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[5][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[11][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);	
	  }
	}
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[5][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[11][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[12][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[15][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[17][0]);
        }
#endif
      }
    }
  }

  //-Z
  if constexpr (!IS_2D) {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(2);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[5] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[4][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[10][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[4][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[10][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[4][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[10][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);	
	  }
	}  
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[4][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[10][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[13][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[14][0]);
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[16][0]);
        }
#endif
      }
    }
  }

  //+X -Y
  {
    sINT16 offsets[3] = {1,  -1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[6] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[7][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[7][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[7][0]);
	}
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[7][0]);
        }
#endif
      }
    }    
  }  

  //-X +Y
  {
    sINT16 offsets[3] = {-1,  1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[7] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[6][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[6][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[6][0]);
	}
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[6][0]);
        }
#endif
      }
    }    
  }

  //+X +Y
  {
    sINT16 offsets[3] = {1,  1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[8] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[9][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[9][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[9][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[9][0]);
        }
#endif
      }
    }    
  }

  //-X -Y
  {
    sINT16 offsets[3] = {-1,  -1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[9] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[8][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[8][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[8][0]);
	}
	
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[8][0]);
        }
#endif      
      }
    }    
  }

  //-X +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1,  0, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[10] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[11][0]);
    
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[11][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[11][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[11][0]);
        }
#endif
      }
    }    
  }

  //+X -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1,  0, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
        BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[11] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[10][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[10][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[10][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[10][0]);
        }
#endif
      }
    }    
  }

  //-X -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1,  0, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[12] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[13][0]);
        }
#endif
      }
    }    
  }

  //+X +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1,  0, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[13] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[12][0]);
        }
#endif
      }
    }    
  }

  //-Y +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, -1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[14] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[15][0]);
        }
#endif
      }
    }    
  }

  //+Y -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0,  1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[15] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[14][0]);
        }
#endif
      }
    }    
  }  

  //+Y +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, 1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[16] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[14][0]);
        }
#endif
      }
    }    
  }

  //-Y -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, -1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[17] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE){
	  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);
        }
#if BUILD_5G_LATTICE
        if (g_is_multi_component) {
          prefetch_states((char *) &src_ublk->mc_data()->mc_states_data(p_lb_index)->m_states_mc[17][0]);
        }
#endif
      }
    }    
  }   
}

#else //BUILD_D39_LATTICE

template<BOOLEAN IS_2D, BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS, BOOLEAN IS_SWAP_ADVECT, BOOLEAN PREFETCH_ACTIVE = TRUE>
INLINE VOID collect_advect_neighbors(UBLK pde_advect_ublk,
				     SOLVER_INDEX_MASK prior_solver_index_mask,
				     BOOLEAN is_timestep_even,
				     UBLK (&active_nbrs) [N_MOVING_STATES]) {

  const auto& box_access = pde_advect_ublk->m_box_access;

  const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
  const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
  const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);

  //Same UBLK
  if constexpr(PREFETCH_ACTIVE) {
#if BUILD_DOUBLE_PRECISION
    constexpr asINT32 incr = 1;
#else
    constexpr asINT32 incr = 2;
#endif
    const asINT32 p_lb_index = pde_advect_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
    const asINT32 p_t_index  = pde_advect_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
    const asINT32 p_uds_index  = pde_advect_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
    for(asINT32 latvec = 0; latvec < N_MOVING_STATES; latvec += incr){
      prefetch_states((char *) &pde_advect_ublk->lb_states(p_lb_index)->m_states[latvec][0]);
      if constexpr(ADVECT_TEMP){
        prefetch_states((char *) &pde_advect_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[latvec][0]);
      }
      if constexpr(ADVECT_UDS){
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
          prefetch_states((char *) &pde_advect_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[latvec][0]);
      }
    }
  }

  //+X
  {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(0);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along X
    if constexpr (IS_SWAP_ADVECT) {
	is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if( is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[0] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {		  
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[1][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[7][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[19][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);

        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[1][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[7][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[19][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }

	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[1][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[7][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[19][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
          }
	}
      }
    }
  }

  //-X
  {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(0);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[1] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[0][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[6][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[18][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[0][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[6][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[18][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[0][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[6][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[18][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }
  }  

  //+Y
  {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(1);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along Y
    if constexpr (IS_SWAP_ADVECT) {
	is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if( is_src_ublk_valid ){
      sUBLK*  src_ublk = active_nbrs[2] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[3][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[9][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[21][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[23][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[3][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[9][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[21][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[23][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[3][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[9][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[21][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[23][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }
  }

  //-Y
  {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(1);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[3] = src_tagged_ublk.ublk();
      asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
      asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
      asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[2][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[8][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[20][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[22][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
      prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);      
      if constexpr(ADVECT_TEMP){
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[2][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[8][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[20][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[22][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
        prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
      }
      if constexpr(ADVECT_UDS){
	ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[2][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[8][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[20][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[22][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
	  prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
        }
      }
    }
  }  

  //+Z
  if constexpr (!IS_2D) {
    TAGGED_UBLK src_tagged_ublk = box_access.forward_neighbor(2);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);

    //Avoid excessive swapping when domain is 1 UBLK thick along Z
    if constexpr (IS_SWAP_ADVECT) {
	is_src_ublk_valid = is_src_ublk_valid && (src_tagged_ublk.ublk()->id() != pde_advect_ublk->id());
    }
    
    if( is_src_ublk_valid ){
      sUBLK*  src_ublk = active_nbrs[4] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[5][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[11][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[18][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[21][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[22][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[5][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[11][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[18][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[21][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[22][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[5][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[11][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[18][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[21][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[22][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }
  }

  //-Z
  if constexpr (!IS_2D) {
    TAGGED_UBLK src_tagged_ublk = box_access.backward_neighbor(2);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[5] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[4][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[10][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[19][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[20][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[23][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[4][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[10][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[19][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[20][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[23][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[4][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[10][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[19][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[20][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[23][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
          }
	}
      }
    }
  }

  //+X +Y
  {
    sINT16 offsets[3] = {1,  1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[12] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[13][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[13][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[13][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
          }
	}
      }
    }    
  }

  //-X -Y
  {
    sINT16 offsets[3] = {-1,  -1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[13] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[12][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[12][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[12][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
          }
	}
      }
    }    
  }

  //+X -Y
  {
    sINT16 offsets[3] = {1,  -1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[14] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[15][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[15][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[15][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
          }
	}
      }
    }    
  }

  //-X +Y
  {
    sINT16 offsets[3] = {-1,  1, 0};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[15] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[14][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[14][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[14][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }
  }

  //+X +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1,  0, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[16] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[17][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[17][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[17][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
          }
	}
      }
    }    
  }

  //-X -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1,  0, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[17] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[16][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[16][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[16][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
          }
	}
      }
    }    
  }

  //+X -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1,  0, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[18] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[19][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[19][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[19][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
          }
	}
      }
    }    
  }

  //-X +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1,  0, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[19] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[18][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[18][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[18][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }    
  }  

  //+Y +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, 1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[20] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[21][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[21][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[21][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
          }
	}
      }
    }    
  }

  //-Y -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, -1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[21] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[20][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[20][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[20][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
          }
	}
      }
    }    
  }

  //+Y -Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0,  1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[22] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[23][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[23][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[23][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
          }
	}
      }
    }    
  }

  //-Y +Z
  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {0, -1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[23] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[22][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[22][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars){
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[22][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
	    prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
          }
	}
      }
    }    
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1, 1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[24] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[25][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[25][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[25][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1, -1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[25] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[24][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[24][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[24][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1, 1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[26] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[27][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[27][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[27][0]);
        }	
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1, -1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[27] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[26][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[26][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[26][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1, -1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[28] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[29][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[29][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[29][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1, 1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[29] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[28][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[28][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[28][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {1, -1, -1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[30] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[31][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[31][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[31][0]);
        }
      }
    }
  }

  if constexpr (!IS_2D) {
    sINT16 offsets[3] = {-1, 1, 1};
    TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
    BOOLEAN is_src_ublk_valid = IS_SWAP_ADVECT?
                                (!src_tagged_ublk.ublk()->is_advection_done(is_timestep_even)):
                                is_ublk_valid_for_gather_advect(src_tagged_ublk,is_timestep_even);
    if(is_src_ublk_valid){
      sUBLK*  src_ublk = active_nbrs[31] = src_tagged_ublk.ublk();
      if constexpr(PREFETCH_ACTIVE) {
        asINT32 p_lb_index = src_ublk->has_two_copies()? prev_lb_index : ONLY_ONE_COPY;
        asINT32 p_t_index  = src_ublk->has_two_copies()? prev_t_index : ONLY_ONE_COPY;
	asINT32 p_uds_index  = src_ublk->has_two_copies()? prev_uds_index : ONLY_ONE_COPY;
        prefetch_states((char *) &src_ublk->lb_states(p_lb_index)->m_states[30][0]);
        if constexpr(ADVECT_TEMP){
          prefetch_states((char *) &src_ublk->t_data()->lb_t_data(p_t_index)->m_states_t[30][0]);
        }
	if constexpr(ADVECT_UDS){
	  ccDOTIMES(nth_uds, sim.n_user_defined_scalars)
            prefetch_states((char *) &src_ublk->uds_data(nth_uds)->lb_uds_data(p_uds_index)->m_states_uds[30][0]);
        }
      }
    }
  }
}

#endif



#if EXA_USE_AVX && BUILD_AVX2
#define PERM_IND_PS(v7,v6,v5,v4,v3,v2,v1,v0) \
    _mm256_set_epi32(v7,v6,v5,v4,v3,v2,v1,v0)

static INLINE vxFLOAT permutevar(vxFLOAT src, const __m256i& ind) {
  vxFLOAT dst = _mm256_permutevar8x32_ps(src.get(),ind);
  return dst;
}

template<auINT8 mask>
static INLINE vxFLOAT blend_v(vxFLOAT src1, vxFLOAT src2) {
  vxFLOAT dst = _mm256_blend_ps(src1.get(),src2.get(),mask);
  return dst;
}
#endif

/* @namespace ADVECT_NAMESPACE
 * This namespace is used to mangle the names of functions such
 * as swap_advect and gather_advect depending on the build type
 * Files such as swap_advect.cc and gather_advect.cc are compiled
 * twice, once without the AVX2 compiler flag and a second time with
 * the mavx2 flag to support vectorized advection.
 *
 * To differentiate the vectorized and non-vectorized builds of swap
 * and PDE advection, we use a namespace that is unique to the build.
 */
#if EXA_USE_AVX && BUILD_AVX2
#define ADVECT_NAMESPACE ADVECT_VECTORIZED
#else
#define ADVECT_NAMESPACE ADVECT_SCALAR
#endif

uINT32 new_split_ublk_instance(sUBLK_VECTOR * split_ublks, uINT32 old_ublk_instance,
                               sUBLK *dest_ublk, asINT32 voxel, asINT32 latvec);
extern BOOLEAN g_use_avx2_advection;

template<typename UBLK_TYPE>
__DEVICE__
VOID pre_advect_init(UBLK_TYPE* ublk,
		     ACTIVE_SOLVER_MASK active_solver_mask) {

#if PERFORMANCE_TESTING
  if (!ublk->is_near_surface()) {
    msg_internal_error("pre_advect_init should only be called for nearblks");
  }
#endif
  // reset lrf related quantities to 0
  // These are accumulated in s2v_advect
  auto& sim = get_sim_ref();
  auto& simc = *(sim.c());
  ACTIVE_SOLVER_MASK is_lb_active   = active_solver_mask & LB_ACTIVE;
  ACTIVE_SOLVER_MASK is_turb_active = active_solver_mask & KE_PDE_ACTIVE;
  ACTIVE_SOLVER_MASK is_temp_active = active_solver_mask & T_PDE_ACTIVE;
  ACTIVE_SOLVER_MASK is_uds_active = active_solver_mask & UDS_PDE_ACTIVE;
  ACTIVE_SOLVER_MASK is_conduction_active = active_solver_mask & CONDUCTION_PDE_ACTIVE;
  BOOLEAN is_T_S_lb_solver_on = simc.is_T_S_solver_type_lb();
  BOOLEAN is_T_pde_solver_on = is_temp_active && (simc.T_solver_type == PDE_TEMPERATURE);
  BOOLEAN is_S_pde_solver_on = is_temp_active && (simc.T_solver_type == PDE_ENTROPY);
  BOOLEAN is_UDS_lb_solver_on = (simc.uds_solver_type == LB_UDS);
  BOOLEAN is_UDS_pde_solver_on = is_uds_active && (simc.uds_solver_type == PDE_UDS);
  BOOLEAN is_pf_solver_on = simc.is_pf_model;

  auto surf_turb = is_turb_active ? ublk->surf_turb_data() : nullptr;
  auto surf_t = is_temp_active ? ublk->surf_t_data() : nullptr;
  auto surf_lb = is_lb_active ? ublk->surf_lb_data() : nullptr;  
  auto surf_conduction = is_conduction_active && ublk->is_conduction_solid() ? ublk->surf_conduction_data() : nullptr;

  size_t data_size = UBLK_TYPE::voxel_variables_size * N_CARTESIAN_SPEED1_LATVECS;
    
  if (is_turb_active) {
    hd_zero(surf_turb->k_pde_1);
    hd_zero(surf_turb->k_pde_2);
    hd_zero(surf_turb->K_lrf);
    hd_zero(surf_turb->e_pde_1);
    hd_zero(surf_turb->e_pde_2);
    hd_zero(surf_turb->E_lrf);
  }
    
  if (is_uds_active) {
    if (is_UDS_pde_solver_on) {      
      ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
	auto surfel_uds_data = ublk->surf_uds_data(nth_uds);
	hd_zero(surfel_uds_data->pde_uds_data()->uds_pde_1);
	hd_zero(surfel_uds_data->pde_uds_data()->uds_pde_2);
      }
    }    
    ccDOTIMES(nth_uds, simc.n_user_defined_scalars) {
      auto surf_uds = ublk->surf_uds_data(nth_uds);
      hd_zero(surf_uds->uds_lrf);  
      hd_zero(surf_uds->uds_value);
      hd_zero(surf_uds->uds_flux);
      ccDO_UBLK_VOXELS(voxel) {
	if (ublk->lrf_surfels_interaction_mask.test(voxel)) {
	  surf_uds->uds_flux_wall[voxel] = 0.0;  
	}
      }
    }


#if BUILD_D19_LATTICE
    if (is_pf_solver_on) {
      auto ublk_ubfloat = ublk->to_ubfloat_ublk();
      hd_zero(ublk_ubfloat->surf_pf_data()->srf_potential);
    }
#endif
  }
#if BUILD_5G_LATTICE
  hd_zero( surf_lb->srf_potential);
  hd_zero( surf_lb->srf_pressure);
  if (g_is_multi_component) {
    hd_zero( ublk->surf_mc_data()->srf_potential);
  }
#endif
  if (is_T_pde_solver_on && !sim.thermal_accel.do_T_solver_switch) {
    ccDOTIMES(k, N_TIME_INDICES) {
      // Ublks with one copy of lb state only have T_pde_1 defined for the first copy
      cassert(!(k == 1 && !ublk->has_two_copies()));
      hd_zero( ublk->t_data()->pde_t_data(k)->T_pde_1);
      hd_zero( ublk->t_data()->pde_t_data(k)->T_pde_2);
    }
    hd_zero(surf_t->T_lrf);
  } else if (is_S_pde_solver_on) {
    ccDOTIMES(k, N_TIME_INDICES) {
      hd_zero( ublk->t_data()->pde_t_data(k)->S_pde_1);
      hd_zero( ublk->t_data()->pde_t_data(k)->S_pde_2);
    }
    hd_zero(surf_t->S_lrf);
  }

  if (is_lb_active) {
    hd_zero(surf_lb->ustar_0);
  }
  // Setting ltt_index to 0 would not hurt even if it is not used
  if (is_turb_active) {
    hd_zero(surf_turb->ltt_index);
  }
  if (is_temp_active) {
    hd_zero(surf_t->heat_wall);
    hd_zero(surf_t->temp_wall);
  }
  if (is_conduction_active && ublk->is_conduction_solid()) {
    hd_zero(surf_conduction->heat_wall);
  }

  ccDO_UBLK_VOXELS(voxel) {
    if (ublk->lrf_surfels_interaction_mask.test(voxel)) {
      if(is_lb_active) {
        surf_lb->lrf_s2s_factor[voxel] = 0.0;
        surf_lb->lrf_v2s_dist[voxel] = 0.0;
        //surf_lb->lrf_s2v_weight[voxel] = 0.0;
      }
      if (is_temp_active && !is_T_S_lb_solver_on) {
	surf_t->T_flux_wall[voxel] = 0.0;
      }
      if (is_turb_active) {
	surf_turb->K_flux_wall[voxel] = 0.0;
	surf_turb->E_flux_wall[voxel] = 0.0;
	// This should perhaps belong to surf_turb_data()
	surf_lb->ustar_0_pair[voxel] = 0.0;
      }
    }
  }
}


template<typename UBLK_TYPE>
__DEVICE__
VOID prepare_ublk_for_next_timestep(UBLK_TYPE* ublk,
				    SOLVER_INDEX_MASK prior_solver_index_mask,
				    ACTIVE_SOLVER_MASK active_solver_mask,
				    BOOLEAN is_lb_active) {
  if (ublk->is_near_surface()) {
    pre_advect_init(ublk, active_solver_mask);
  }

  // This is done post dynamics, but could also be done before advection if
  // mirror voxels do not participate in V2S
#if !GPU_COMPILER    
  if (ublk->has_mirror()) {
#if BUILD_5G_LATTICE
    if (sim.is_large_pore)
      reflect_full_states_to_mirror_ublk(ublk);
    else
#endif
      reflect_curr_states_to_mirror_ublk(ublk, prior_solver_index_mask);
    reflect_solver_data_to_mirror_ublk(ublk, active_solver_mask);
  }
#endif

  GPU_SINGLE_THREAD(0) {
    if (ublk->has_two_copies()) {
      ublk->unset_states_clear();
    }

#if !GPU_COMPILER  
    ublk->unset_first_surfel();
#else
    if (ublk->is_near_surface()) {
      ublk->surf_geom_data()->s2v_clear_data.unset_states_clear();
    }
#endif
  }
  
}

template<BOOLEAN DO_EXPLODE, typename UBLK_TYPE>
__DEVICE__
VOID prepare_vr_fine_ublk_for_next_timestep(UBLK_TYPE* vr_fine_ublk,                                    
                                            SOLVER_INDEX_MASK prior_solver_index_mask) {

  // This is done post dynamics, but could also be done before advection if
  // mirror voxels do not participate in V2S
#if !GPU_COMPILER
  if (DO_EXPLODE && vr_fine_ublk->has_mirror()) {
    reflect_curr_states_to_mirror_ublk(vr_fine_ublk, prior_solver_index_mask);
  }
#endif

  GPU_SINGLE_THREAD(0) {
    
    vr_fine_ublk->unset_states_clear();

    if (vr_fine_ublk->is_near_surface()) {
      vr_fine_ublk->surf_geom_data()->s2v_clear_data.unset_states_clear();
    }
  }
}

void print_all_ublk_box_contents(std::ostream& os);

#endif /*BOX_ADVECT_H_*/
