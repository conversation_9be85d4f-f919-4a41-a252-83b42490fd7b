/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                      Created Fri, Jan 30, 2009
//----------------------------------------------------------------------------
#ifndef _SIMENG_FSET_H_
#define _SIMENG_FSET_H_

#include <set>

#include "common_sp.h"
#include "group.h"
#include "sim.h"

//----------------------------------------------------------------------------
// SP_FSET
//----------------------------------------------------------------------------
template<typename  GROUP_TYPE>
struct tDEFAULT_GROUP_ORDER
{
  // This method must account for all members of the group that make it unique
  // except for scale (which is handled specially by tSP_FSET). This default
  // method definition says that all groups are equal, which implies there
  // will only be one group per scale.
  BOOLEAN operator()(const GROUP_TYPE a, const GROUP_TYPE b) const
  {
    return FALSE;
  }
};


// @@@ When ORDER is set to tDEFAULT_GROUP_ORDER<GROUP_TYPE>, there is only one group 
// per scale, in which case, there is no need for a std::set per scale. We may want
// to consider a template specialization for this case for efficieny reasons. Getting
// this to work with the loop macros below is left as an exercise for the reader.

template<typename GROUP_TYPE, typename ORDER = tDEFAULT_GROUP_ORDER<GROUP_TYPE> > 
class tSP_FSET
{
  typedef std::set< GROUP_TYPE, ORDER > SET_OF_GROUPS;
  SET_OF_GROUPS *m_sets;     // 1 set of groups per scale
  sINT32        m_n_groups;  // across all scales

public:
  typedef GROUP_TYPE GROUP;

  tSP_FSET() 
  {
    m_sets = NULL;
    m_n_groups = 0;
  }

  // This type and method are only public because they need to be used in the loop macros below.
  typedef typename SET_OF_GROUPS::iterator ITERATOR;
  typedef typename SET_OF_GROUPS::reverse_iterator REVERSE_ITERATOR;

  SET_OF_GROUPS *groups_per_scale() { return m_sets; }

  SET_OF_GROUPS &groups_of_scale(SCALE scale) { return m_sets[scale]; }
  const SET_OF_GROUPS& groups_of_scale(SCALE scale) const { 
    assert(m_sets);
    return m_sets[scale]; 
  }

  SET_OF_GROUPS &get_groups_of_scale(SCALE scale) 
  { 
    if (m_sets == NULL)
      m_sets = xnew SET_OF_GROUPS[sim.num_scales];
    return m_sets[scale]; 
  }

  VOID add_group(GROUP_TYPE group)
  {
    if (group->m_scale < 0 || group->m_scale >= sim.num_scales)
      msg_internal_error("Invalid group scale when adding group to fset");

    get_groups_of_scale(group->m_scale).insert(group);
    m_n_groups++;
  }

  GROUP_TYPE find_group(const GROUP_TYPE target_group) 
  {
    if (groups_per_scale() == NULL)
      return NULL;
    
    SET_OF_GROUPS &groups = groups_of_scale(target_group->m_scale);
    ITERATOR it = groups.find(target_group);

    GROUP_TYPE group;
    if ( it != groups.end() ) 
      group = *it;
    else
      group = NULL;
  
    return group;
  }

  bool delete_group(const GROUP_TYPE target_group)
  {
    bool group_deleted = false;
    if (groups_per_scale() != NULL) {

      SET_OF_GROUPS &groups = groups_of_scale(target_group->m_scale);
      ITERATOR it = groups.find(target_group);

      if ( it != groups.end() ) { 
        groups.erase(it);
        group_deleted = true;
        m_n_groups--;
      }
    }

    return group_deleted;

  }

  asINT32 n_groups()                     const { return m_n_groups; }
  asINT32 n_groups_of_scale(SCALE scale) const { return m_sets ? m_sets[scale].size() : 0; }

};

//----------------------------------------------------------------------------
// LOOP MACROS
//----------------------------------------------------------------------------
#define FSET_FOREACH_GROUP_OF_SCALE(FSET_TYPE, _fset, group, _scale)    \
  FSET_TYPE &___(fset)         = (_fset);                               \
  asINT32 ___(scale)           = (_scale);                              \
  typename FSET_TYPE::ITERATOR ___(it);                                 \
  typename FSET_TYPE::ITERATOR ___(end);                                \
  if (___(fset).groups_per_scale()) {                                   \
    ___(it)  = ___(fset).groups_of_scale(___(scale)).begin();           \
    ___(end) = ___(fset).groups_of_scale(___(scale)).end();             \
  }                                                                     \
  if (___(fset).groups_per_scale())                                     \
    for( FSET_TYPE::GROUP group;                                        \
         ___(it) != ___(end) && (group = *___(it), TRUE);               \
         ___(it)++)

#define FSET_PTR_FOREACH_GROUP_OF_SCALE(FSET_TYPE, _fset_ptr, group, _scale)    \
  FSET_TYPE * ___(fset)         = (_fset_ptr);                          \
  asINT32 ___(scale)           = (_scale);                              \
  typename FSET_TYPE::ITERATOR ___(it);                                 \
  typename FSET_TYPE::ITERATOR ___(end);                                \
  if (___(fset)->groups_per_scale()) {                                  \
    ___(it)  = ___(fset)->groups_of_scale(___(scale)).begin();          \
    ___(end) = ___(fset)->groups_of_scale(___(scale)).end();            \
  }                                                                     \
  if (___(fset)->groups_per_scale())                                    \
    for( FSET_TYPE::GROUP group;                                        \
         ___(it) != ___(end) && (group = *___(it), TRUE);               \
         ___(it)++)

#define FSET_FOREACH_GROUP_OF_SCALE_REVERSE(FSET_TYPE, _fset, group, _scale)    \
  FSET_TYPE &___(fset)         = (_fset);                                       \
  asINT32 ___(scale)           = (_scale);                                      \
  typename FSET_TYPE::REVERSE_ITERATOR ___(it);                                 \
  typename FSET_TYPE::REVERSE_ITERATOR ___(end);                                \
  if (___(fset).groups_per_scale()) {                                           \
    ___(it)  = ___(fset).groups_of_scale(___(scale)).rbegin();                  \
    ___(end) = ___(fset).groups_of_scale(___(scale)).rend();                    \
  }                                                                             \
  if (___(fset).groups_per_scale())                                             \
    for(typename FSET_TYPE::GROUP group;                                        \
         ___(it) != ___(end) && (group = *___(it), TRUE);                       \
         ___(it)++)

// Be careful... This expands to a pair of nested loops so break and continue will not 
// work as expected inside this loop macro.
#define FSET_FOREACH_GROUP(FSET_TYPE, _fset, group)                                     \
  FSET_TYPE &___(fset) = (_fset);                                                       \
  typename FSET_TYPE::ITERATOR ___(it);                                                 \
  typename FSET_TYPE::ITERATOR ___(end);                                                \
  asINT32 ___(scale) = 0;                                                               \
  if (___(fset).groups_per_scale())                                                     \
    for (typename FSET_TYPE::GROUP group; ___(scale)<sim.num_scales; ___(scale)++)      \
      for( ___(it)  = ___(fset).groups_of_scale(___(scale)).begin(),                    \
           ___(end) = ___(fset).groups_of_scale(___(scale)).end();                      \
           ___(it) != ___(end) && (group = *___(it), TRUE);                             \
           ___(it)++)

// Be careful... This expands to 3 nested loops so break and continue will not 
// work as expected inside this loop macro.
#define FSET_PAIR_FOREACH_GROUP(FSET_TYPE, _fset1, _fset2, group)                               \
  FSET_TYPE *___(fset) = &(_fset1);                                                             \
  typename FSET_TYPE::ITERATOR ___(it);                                                         \
  typename FSET_TYPE::ITERATOR ___(end);                                                        \
  asINT32 ___(scale);                                                                           \
  asINT32 ___(i);                                                                               \
  for (___(i) = 0, ___(scale) = 0; ___(i) < 2; ___(i)++, ___(scale) = 0, ___(fset) = &(_fset2)) \
    if (___(fset)->groups_per_scale())                                                          \
      for (typename FSET_TYPE::GROUP group; ___(scale)<sim.num_scales; ___(scale)++)            \
        for( ___(it)  = ___(fset)->groups_of_scale(___(scale)).begin(),                         \
             ___(end) = ___(fset)->groups_of_scale(___(scale)).end();                           \
             ___(it) != ___(end) && (group = *___(it), TRUE);                                   \
             ___(it)++)


#endif // _SIMENG_FSET_H_




