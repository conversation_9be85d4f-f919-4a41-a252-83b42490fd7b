/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * SP Timer definitions
 *
 * Jim Salem, Exa Corporation 
 * Created Fri Sep  9 1994
 *--------------------------------------------------------------------------*/

#include "common_sp.h"
#include "sp_timers.h"

// For using fluid types
#include "voxel_dyn_sp.h"

TIMER_COUNTERS g_timer_counters;
PER_TIMESTEP_TIMER_COUNTERS g_per_timestep_timer_counters;

/*--------------------------------------------------------------------------*
 * HOW TO USE:
 *--------------------------------------------------------------------------*
 * 1) Define a timer by adding it to the following table.
 *
 * 2) Time a region of code with WITH_TIMER()
 *      Example: WITH_TIMER(5, ...body...)
 *
 * 3) Report the results with timer_report
 *      Example: timer_report(5, TIMER_UNITS_SECS)
 *
 * Also) Instead of using WITH_TIMER, you can call timer_clear,
 *       timer_start, and timer_stop directly.
 *
 *--------------------------------------------------------------------------*/


/* There is no need for the timers to be entered in this table in order by ID,
 * since they are arranged properly at initialization time.
 */
sTIMER sp_timers[NUM_TIMERS] = {

  VTIMER_INIT_ITEM(SP_STANDARD_WALL_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: standard wall         ", "surfels"),
  VTIMER_INIT_ITEM(SP_FRINGE_SURFEL_DYN_TIMER,	         "Timer: surfel dyn: fringe                ", "surfels"),
  VTIMER_INIT_ITEM(SP_FRINGE2_SURFEL_DYN_TIMER,	         "Timer: surfel dyn: fringe2               ", "surfels"),
  VTIMER_INIT_ITEM(SP_LINEAR_WALL_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: linear wall           ", "surfels"),
  VTIMER_INIT_ITEM(SP_VEL_WALL_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: velocity wall         ", "surfels"),
  VTIMER_INIT_ITEM(SP_ANGULAR_WALL_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: angular wall          ", "surfels"),
  VTIMER_INIT_ITEM(SP_FIXED_VEL_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: fixed vel             ", "surfels"),
  VTIMER_INIT_ITEM(SP_PRESSURE_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: pressure              ", "surfels"),
  VTIMER_INIT_ITEM(SP_MASS_FLUX_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: mass flux             ", "surfels"),
  VTIMER_INIT_ITEM(SP_SOURCE_SURFEL_DYN_TIMER,		 "Timer: surfel dyn: source                ", "surfels"),
  VTIMER_INIT_ITEM(SP_SAMPLING_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: sampling              ", "surfels"),
  VTIMER_INIT_ITEM(SP_NOSLIP_SURFEL_DYN_TIMER,	         "Timer: surfel dyn: noslip                ", "surfels"),
  VTIMER_INIT_ITEM(SP_CONDUCTION_SURFEL_DYN_TIMER,	 "Timer: surfel dyn: conduction            ", "surfels"),

  VTIMER_INIT_ITEM(SP_MLRF_SURFEL_DYN_TIMER,      	 "Timer: surfel dyn: MLRF                  ", "surfels"),
  VTIMER_INIT_ITEM(SP_MLRF_S2S_V2S_TIMER,      	         "Timer: surfel S2S V2S: MLRF              ", "surfels"),
  VTIMER_INIT_ITEM(SP_MLRF_S2V_TIMER,      	         "Timer: surfel S2V: MLRF                  ", "surfels"),
  VTIMER_INIT_ITEM(SP_MLRF_SURFEL_DMASS_TIMER,      	 "Timer: surfel delta mass: MLRF           ", "surfels"),
  VTIMER_INIT_ITEM(SP_SLRF_SURFEL_DYN_TIMER,      	 "Timer: surfel dyn: SLRF                  ", "surfels"),

  VTIMER_INIT_ITEM(SP_APM_NOSLIP_ISURFEL_DYN_TIMER,      "Timer: surfel dyn: apm noslip            ", "isurfels"),
  VTIMER_INIT_ITEM(SP_APM_STANDARD_ISURFEL_DYN_TIMER,    "Timer: surfel dyn: apm standard          ", "isurfels"),
  VTIMER_INIT_ITEM(SP_APM_ANGULAR_ISURFEL_DYN_TIMER,     "Timer: surfel dyn: apm angular           ", "isurfels"),
  VTIMER_INIT_ITEM(SP_APM_LINEAR_ISURFEL_DYN_TIMER,      "Timer: surfel dyn: apm linear            ", "isurfels"),
  
  VTIMER_INIT_3_ITEM(SP_S2S_TIMER,                       "Timer: surfel S2S                        ", "dest surfels", "src surfels", "weights"),
  VTIMER_INIT_4_ITEM(SP_V2S_TIMER,                       "Timer: surfel V2S                        ", "surfels", "ublks", "voxels", "weights"),
  VTIMER_INIT_4_ITEM(SP_S2V_TIMER,                       "Timer: surfel S2V                        ", "surfels", "ublks", "voxels", "weights"),
  VTIMER_INIT_3_ITEM(SP_FRINGE_S2S_TIMER,                "Timer: fringe S2S                        ", "dest surfels", "src surfels", "weights"),
  VTIMER_INIT_4_ITEM(SP_FRINGE_V2S_TIMER,                "Timer: fringe V2S                        ", "surfels", "ublks", "voxels", "weights"),
  VTIMER_INIT_4_ITEM(SP_FRINGE_S2V_TIMER,                "Timer: fringe S2V                        ", "surfels", "ublks", "voxels", "weights"),
  VTIMER_INIT_3_ITEM(SP_FRINGE2_S2S_TIMER,               "Timer: fringe2 S2S                       ", "dest surfels", "src surfels", "weights"),
  VTIMER_INIT_4_ITEM(SP_FRINGE2_V2S_TIMER,               "Timer: fringe2 V2S                       ", "surfels", "ublks", "voxels", "weights"),
  VTIMER_INIT_4_ITEM(SP_FRINGE2_S2V_TIMER,               "Timer: fringe2 S2V                       ", "surfels", "ublks", "voxels", "weights"),

  VTIMER_INIT_4_ITEM(SP_V2S_SAMPLING_TIMER,		 "Timer: V2S sampling                      ", "ublks", "voxels", "surfels", "weights"),
  VTIMER_INIT_3_ITEM(SP_S2S_SAMPLING_TIMER,		 "Timer: S2S sampling                      ", "dest surfels", "src surfels", "weights"),
  VTIMER_INIT_ITEM  (SP_BSURFEL_DYN_TIMER ,    "Timer: bsurfel dyn                       ", "bsurfels"),
  VTIMER_INIT_ITEM  (SP_BSURFEL_MOVE_TIMER,    "Timer: bsurfel movement                  ", "bsurfels"),

  VTIMER_INIT_3_ITEM(SP_FARBLK1_DYN_TIMER,               "Timer: far voxel dyn: fluid              ", "ublks", "fan ublks", "porous ublks"),
  VTIMER_INIT_3_ITEM(SP_FARBLK2_DYN_TIMER,               "Timer: far2 voxel dyn: fluid             ", "ublks", "fan ublks", "porous ublks"),
  VTIMER_INIT_3_ITEM(SP_FRINGE_FARBLK_DYN_TIMER,         "Timer: fringe far voxel dyn: fluid       ", "ublks", "fan ublks", "porous ublks"),

  VTIMER_INIT_9_ITEM(SP_NEARBLK_DYN_TIMER,	          "Timer: near voxel dyn: fluid             ", 
                     "usu", "1su", "msu", 
                     "usf", "1sf", "msf", 
                     "usp", "1sp", "msp"),

  VTIMER_INIT_9_ITEM(SP_FRINGE_NEARBLK_DYN_TIMER,         "Timer: fringe near voxel dyn: fluid      ",
                     "usu", "1su", "msu", 
                     "usf", "1sf", "msf", 
                     "usp", "1sp", "msp"),
  VTIMER_INIT_9_ITEM(SP_FRINGE2_NEARBLK_DYN_TIMER,        "Timer: fringe2 near voxel dyn: fluid     ",
                     "usu", "1su", "msu", 
                     "usf", "1sf", "msf", 
                     "usp", "1sp", "msp"),

  // Number of VR fine ublks = Number of exploded VR coarse voxels
  VTIMER_INIT_ITEM(SP_VRFINE_FAR_UBLKS_ADVECT_TIMER,            "Timer: vrfine far adv+explode            ", "vr_fine_ublks"),
  VTIMER_INIT_ITEM(SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER,           "Timer: vrfine near adv+explode           ", "vr_fine_ublks"),
  VTIMER_INIT_ITEM(SP_FRINGE_VRFINE_FAR_UBLKS_ADVECT_TIMER,     "Timer: fringe vrfine far adv+explode     ", "fringe_far_vr_fine_ublks"),
  VTIMER_INIT_ITEM(SP_FRINGE_VRFINE_NEAR_UBLKS_ADVECT_TIMER,    "Timer: fringe vrfine near adv+explode    ", "fringe_near_vr_fine_ublks"),
  VTIMER_INIT_ITEM(SP_FRINGE2_VRFINE_FAR_UBLKS_ADVECT_TIMER,    "Timer: fringe2 vrfine far adv+explode    ", "fringe2_far_vr_fine_ublks"),
  VTIMER_INIT_ITEM(SP_FRINGE2_VRFINE_NEAR_UBLKS_ADVECT_TIMER,   "Timer: fringe2 vrfine near adv+explode   ", "fringe2_near_vr_fine_ublks"),

  VTIMER_INIT_ITEM(SP_PARTICLE_MLRF_EMISSION_TIMER,
                   "Timer: particle MLRF emission", "parcels"),
  VTIMER_INIT_2_ITEM(SP_PARTICLE_EMISSION_TIMER,
                   "Timer: particle emission", "emitters", "parcels"),
  VTIMER_INIT_2_ITEM(SP_PARTICLE_VOXEL_DYN_TIMER,
                   "Timer: particle voxel dynamics", "ublks", "parcels"),
  VTIMER_INIT_2_ITEM(SP_FILM_ACCUMULATION_TIMER,
                   "Timer: film accumulation", "surfels", "parcels"),
  VTIMER_INIT_2_ITEM(SP_FILM_REENTRAINMENT_TIMER,
                   "Timer: film reentrainment", "surfels", "parcels"),
  VTIMER_INIT_ITEM(SP_FILM_DYNAMICS_TIMER,
                   "Timer: film dynamics", "surfels"),
  VTIMER_INIT_ITEM(SP_FILM_MEASUREMENTS_TIMER,
                   "Timer: film measurements", "surfels"),
  VTIMER_INIT_2_ITEM(SP_SURFACE_PARTICLE_KINEMATICS_TIMER,
                   "Timer: surface particle kinematics", "surfels", "parcels"),
  VTIMER_INIT_3_ITEM( SP_PARTICLE_COLLISION_DETECTION_TIMER,
                      "Timer: particle collision detection", "surfels", "voxels", "parcels"),
  VTIMER_INIT_2_ITEM( SP_PARTICLE_CONTAINER_UPDATE_TIMER,
                    "Timer: particle container update", "voxels", "parcels"),
  VTIMER_INIT_ITEM(SP_PARTICLE_SLRF_EMISSION_TIMER,
                   "Timer: particle SLRF emission", "parcels"),
  VTIMER_INIT_2_ITEM(SP_IMPLICIT_SOLVER_TIMER,
                   "Timer: implicit solve", "iterations to converge", "dofs"),
  VTIMER_INIT_ITEM(SP_IMPLICIT_MATRIX_ASSEMBLY_TIMER,
                   "Timer: implicit matrix assembly", "dofs"),

  TIMER_INIT(SP_ACCUM_TIMER,                             "Accumulated time                         "),
  /* Always the last timer */
  TIMER_INIT(SP_TOTAL_RUN_TIMER, 		         "Total duration of timestep               "),
};

VOID sim_initialize_timers(VOID)
{
  sTIMER sp_timers_copy[NUM_TIMERS];

  // It is OK to use the same timer for even and odd parities
  BOOLEAN is_timer_defined[NUM_TIMERS] = { FALSE };

  /* Sort the timers into the proper order */
  memcpy(sp_timers_copy, sp_timers, sizeof(sp_timers));
  
  {
    asINT32 i;
    TIMER timer = sp_timers_copy;
      
    for (i=0; i<NUM_TIMERS; i++, timer++) {
      if (timer->id >= NUM_TIMERS) {
        msg_internal_error("Invalid timer ID %d.",timer->id);
      }
      if (is_timer_defined[timer->id]) {
        msg_internal_error("Multiply defined timer - ID = %d.",timer->id);
      }
      is_timer_defined[timer->id] = TRUE;
      memcpy(sp_timers + timer->id, timer, sizeof(sTIMER));
    }
  
    for (i=0; i<NUM_TIMERS; i++) {
      if (!is_timer_defined[i]) {
        msg_internal_error("Timer %d is not defined.", i);
      }
    }
  }
}

void TIMER_COUNTERS::initialize()
{
  S2S_N_DEST_SURFELS = new asINT32[sim.num_scales];
  S2S_N_SRC_SURFELS = new asINT32*[sim.num_scales];
  S2S_N_WEIGHTS = new asINT32*[sim.num_scales];
  FRINGE_S2S_N_DEST_SURFELS = new asINT32[sim.num_scales];
  FRINGE_S2S_N_SRC_SURFELS = new asINT32*[sim.num_scales];
  FRINGE_S2S_N_WEIGHTS = new asINT32*[sim.num_scales];
  FRINGE2_S2S_N_DEST_SURFELS = new asINT32[sim.num_scales];
  FRINGE2_S2S_N_SRC_SURFELS = new asINT32*[sim.num_scales];
  FRINGE2_S2S_N_WEIGHTS = new asINT32*[sim.num_scales];
  
  SURF_TYPES_DYN_N_SURFELS = new asINT32**[sim.num_scales];

  SURF_DYN_N_SURFELS = new asINT32*[sim.num_scales];
  SLRF_SURF_DYN_N_SURFELS = new asINT32*[sim.num_scales];
  MLRF_SURF_DYN_N_SURFELS = new asINT32*[sim.num_scales];
  ISURF_TYPES_DYN_N_ISURFELS = new asINT32**[sim.num_scales];

  ISURF_DYN_N_ISURFELS = new asINT32*[sim.num_scales];
  
  SAMPLING_SURF_DYN_N_SURFELS = new asINT32*[sim.num_scales];

  V2S_S2V_N_SURFELS = new asINT32*[sim.num_scales];
  V2S_S2V_N_UBLKS   = new asINT32*[sim.num_scales];
  V2S_S2V_N_VOXELS  = new asINT32*[sim.num_scales];
  V2S_S2V_N_WEIGHTS = new asINT32*[sim.num_scales];
  FRINGE_V2S_S2V_N_SURFELS = new asINT32*[sim.num_scales];
  FRINGE_V2S_S2V_N_UBLKS   = new asINT32*[sim.num_scales];
  FRINGE_V2S_S2V_N_VOXELS  = new asINT32*[sim.num_scales];
  FRINGE_V2S_S2V_N_WEIGHTS = new asINT32*[sim.num_scales];
  FRINGE2_V2S_S2V_N_SURFELS = new asINT32*[sim.num_scales];
  FRINGE2_V2S_S2V_N_UBLKS   = new asINT32*[sim.num_scales];
  FRINGE2_V2S_S2V_N_VOXELS  = new asINT32*[sim.num_scales];
  FRINGE2_V2S_S2V_N_WEIGHTS = new asINT32*[sim.num_scales];

  FAR1_N_UBLKS          = new asINT32*[sim.num_scales];
  PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS  = new asINT32*[sim.num_scales];
  PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];
  PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];
 
  FRINGE_PDE_FAR_N_UBLKS          = new asINT32*[sim.num_scales];
  FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS  = new asINT32*[sim.num_scales];
  FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];
  FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];

  PDE_FAR2_N_UBLKS          = new asINT32*[sim.num_scales];
  FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS  = new asINT32*[sim.num_scales];
  FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];
  FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS     = new asINT32*[sim.num_scales];

  VRFINE_FAR_N_UBLKS      = new asINT32[sim.num_scales];
  VRFINE_NEAR_N_UBLKS     = new asINT32[sim.num_scales];
  FRINGE_VRFINE_FAR_N_UBLKS      = new asINT32[sim.num_scales];
  FRINGE_VRFINE_NEAR_N_UBLKS     = new asINT32[sim.num_scales];
  FRINGE2_VRFINE_FAR_N_UBLKS      = new asINT32[sim.num_scales];
  FRINGE2_VRFINE_NEAR_N_UBLKS     = new asINT32[sim.num_scales];

  
  ccDOTIMES(i, sim.num_scales) {
    V2S_S2V_N_SURFELS[i]  = new asINT32[4];
    V2S_S2V_N_UBLKS[i]    = new asINT32[4];
    V2S_S2V_N_VOXELS[i]   = new asINT32[4];
    V2S_S2V_N_WEIGHTS[i]  = new asINT32[4];
    FRINGE_V2S_S2V_N_SURFELS[i]  = new asINT32[4];
    FRINGE_V2S_S2V_N_UBLKS[i]    = new asINT32[4];
    FRINGE_V2S_S2V_N_VOXELS[i]   = new asINT32[4];
    FRINGE_V2S_S2V_N_WEIGHTS[i]  = new asINT32[4];
    FRINGE2_V2S_S2V_N_SURFELS[i]  = new asINT32[4];
    FRINGE2_V2S_S2V_N_UBLKS[i]    = new asINT32[4];
    FRINGE2_V2S_S2V_N_VOXELS[i]   = new asINT32[4];
    FRINGE2_V2S_S2V_N_WEIGHTS[i]  = new asINT32[4];

    SURF_TYPES_DYN_N_SURFELS[i]    = new asINT32*[2];
    SURF_TYPES_DYN_N_SURFELS[i][0] = new asINT32[NUM_SURFEL_LOAD_TYPES];
    SURF_TYPES_DYN_N_SURFELS[i][1] = new asINT32[NUM_SURFEL_LOAD_TYPES];
    
    SURF_DYN_N_SURFELS[i]      = new asINT32[2];
    SLRF_SURF_DYN_N_SURFELS[i] = new asINT32[2];
    MLRF_SURF_DYN_N_SURFELS[i] = new asINT32[2];

    ISURF_TYPES_DYN_N_ISURFELS[i]    = new asINT32*[2];
    ISURF_TYPES_DYN_N_ISURFELS[i][0] = new asINT32[NUM_ISURFEL_LOAD_TYPES];
    ISURF_TYPES_DYN_N_ISURFELS[i][1] = new asINT32[NUM_ISURFEL_LOAD_TYPES];
    
    ISURF_DYN_N_ISURFELS[i]      = new asINT32[2];
    
    S2S_N_SRC_SURFELS[i]  = new asINT32[2];
    S2S_N_WEIGHTS[i]      = new asINT32[2];
    FRINGE_S2S_N_SRC_SURFELS[i]  = new asINT32[2];
    FRINGE_S2S_N_WEIGHTS[i]      = new asINT32[2];
    FRINGE2_S2S_N_SRC_SURFELS[i]  = new asINT32[2];
    FRINGE2_S2S_N_WEIGHTS[i]      = new asINT32[2];

    FAR1_N_UBLKS[i]        = new asINT32[NUM_FLUID_LOAD_TYPES];

    PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];

    FRINGE_PDE_FAR_N_UBLKS[i]        = new asINT32[NUM_FLUID_LOAD_TYPES];

    FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];

    PDE_FAR2_N_UBLKS[i]        = new asINT32[NUM_FLUID_LOAD_TYPES];

    FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];
    FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[i]  = new asINT32[NUM_FLUID_LOAD_TYPES];

    SAMPLING_SURF_DYN_N_SURFELS[i] = new asINT32[2];
  }

  ccDOTIMES(scale, sim.num_scales) 
  {
    S2S_N_DEST_SURFELS[scale] = 0;
    FRINGE_S2S_N_DEST_SURFELS[scale] = 0;
    FRINGE2_S2S_N_DEST_SURFELS[scale] = 0;
    
    VRFINE_NEAR_N_UBLKS[scale]             = 0;
    VRFINE_FAR_N_UBLKS[scale]              = 0;
    FRINGE_VRFINE_NEAR_N_UBLKS[scale]      = 0;
    FRINGE_VRFINE_FAR_N_UBLKS[scale]       = 0;
    FRINGE2_VRFINE_NEAR_N_UBLKS[scale]      = 0;
    FRINGE2_VRFINE_FAR_N_UBLKS[scale]       = 0;

    //g_SURF_DYN_N_SURFELS[scale] = 0;
    ccDOTIMES(phase, 4)
    {
      V2S_S2V_N_SURFELS[scale][phase] = 0;
      V2S_S2V_N_UBLKS[scale][phase] = 0;
      V2S_S2V_N_VOXELS[scale][phase] = 0;
      V2S_S2V_N_WEIGHTS[scale][phase] = 0;
      FRINGE_V2S_S2V_N_SURFELS[scale][phase] = 0;
      FRINGE_V2S_S2V_N_UBLKS[scale][phase] = 0;
      FRINGE_V2S_S2V_N_VOXELS[scale][phase] = 0;
      FRINGE_V2S_S2V_N_WEIGHTS[scale][phase] = 0;
      FRINGE2_V2S_S2V_N_SURFELS[scale][phase] = 0;
      FRINGE2_V2S_S2V_N_UBLKS[scale][phase] = 0;
      FRINGE2_V2S_S2V_N_VOXELS[scale][phase] = 0;
      FRINGE2_V2S_S2V_N_WEIGHTS[scale][phase] = 0;
    }

    // for even and odd timesteps
    ccDOTIMES(phase, 2) {
      ccDOTIMES(surfel_type, NUM_SURFEL_LOAD_TYPES) {
        SURF_TYPES_DYN_N_SURFELS[scale][phase][surfel_type] = 0;
      }

      SURF_DYN_N_SURFELS[scale][phase] = 0;
      SLRF_SURF_DYN_N_SURFELS[scale][phase] = 0;
      MLRF_SURF_DYN_N_SURFELS[scale][phase] = 0;

      ccDOTIMES(isurfel_type, NUM_ISURFEL_LOAD_TYPES) {
        ISURF_TYPES_DYN_N_ISURFELS[scale][phase][isurfel_type] = 0;
      }

      ISURF_DYN_N_ISURFELS[scale][phase] = 0;
      
      S2S_N_SRC_SURFELS[scale][phase] = 0;
      S2S_N_WEIGHTS[scale][phase] = 0;
      FRINGE_S2S_N_SRC_SURFELS[scale][phase] = 0;
      FRINGE_S2S_N_WEIGHTS[scale][phase] = 0;
      FRINGE2_S2S_N_SRC_SURFELS[scale][phase] = 0;
      FRINGE2_S2S_N_WEIGHTS[scale][phase] = 0;
      
      SAMPLING_SURF_DYN_N_SURFELS[scale][phase] = 0;
    }

    // for all load types
    ccDOTIMES(fluid_type, NUM_FLUID_LOAD_TYPES)
    {
      FAR1_N_UBLKS[scale][fluid_type]    = 0;

      PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type]  = 0;
      PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;
      PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;

      FRINGE_PDE_FAR_N_UBLKS[scale][fluid_type]    = 0;

      FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type]  = 0;
      FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;
      FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;

      PDE_FAR2_N_UBLKS[scale][fluid_type]    = 0;

      FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type]  = 0;
      FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;
      FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type]     = 0;

    }
  }
}

VOID TIMER_COUNTERS::print()
{
  ccDOTIMES(scale, sim.num_scales) {

    msg_print("scale %d", scale);
    ccDOTIMES(j, 3) { // 3 physics types: fluid, porous media, fan
      msg_print("FAR1_N_UBLKS[%d][%d] = %d", scale, j, FAR1_N_UBLKS[scale][j]);
      
      msg_print("PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[%d][%d] = %d", scale, j, PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][j]);
      msg_print("PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);
      msg_print("PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);

      msg_print("FRINGE_PDE_FAR_N_UBLKS[%d][%d] = %d", scale, j, FRINGE_PDE_FAR_N_UBLKS[scale][j]);
      
      msg_print("FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[%d][%d] = %d", scale, j, FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][j]);
      msg_print("FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);
      msg_print("FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);

      msg_print("PDE_FAR2_N_UBLKS[%d][%d] = %d", scale, j, PDE_FAR2_N_UBLKS[scale][j]);
      
      msg_print("FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[%d][%d] = %d", scale, j, FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][j]);
      msg_print("FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);
      msg_print("FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[%d][%d] = %d", scale, j, FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][j]);

    }

    msg_print("VRFINE_FAR_N_UBLKS[%d] = %d", scale, VRFINE_FAR_N_UBLKS[scale]);
    msg_print("VRFINE_NEAR_N_UBLKS[%d] = %d", scale, VRFINE_NEAR_N_UBLKS[scale]);
    msg_print("FRINGE_VRFINE_FAR_N_UBLKS[%d] = %d", scale, FRINGE_VRFINE_FAR_N_UBLKS[scale]);
    msg_print("FRINGE_VRFINE_NEAR_N_UBLKS[%d] = %d", scale, FRINGE_VRFINE_NEAR_N_UBLKS[scale]);
    msg_print("FRINGE2_VRFINE_FAR_N_UBLKS[%d] = %d", scale, FRINGE2_VRFINE_FAR_N_UBLKS[scale]);
    msg_print("FRINGE2_VRFINE_NEAR_N_UBLKS[%d] = %d", scale, FRINGE2_VRFINE_NEAR_N_UBLKS[scale]);


    msg_print("S2S_N_DEST_SURFELS[%d] = %d", scale, S2S_N_DEST_SURFELS[scale]);
    ccDOTIMES(phase, 2) {

      ccDOTIMES(surfel_type, NUM_SURFEL_LOAD_TYPES) {
        msg_print("SURF_TYPES_DYN_N_SURFELS[%d][%d][%d] = %d", scale, phase, surfel_type, SURF_TYPES_DYN_N_SURFELS[scale][phase][surfel_type]);
      }

      msg_print("S2S_N_SRC_SURFELS[%d][%d] = %d", scale, phase, S2S_N_SRC_SURFELS[scale][phase]);
      msg_print("S2S_N_WEIGHTS[%d][%d] = %d", scale, phase, S2S_N_WEIGHTS[scale][phase]);
      msg_print("FRINGE_S2S_N_SRC_SURFELS[%d][%d] = %d", scale, phase, FRINGE_S2S_N_SRC_SURFELS[scale][phase]);
      msg_print("FRINGE_S2S_N_WEIGHTS[%d][%d] = %d", scale, phase, FRINGE_S2S_N_WEIGHTS[scale][phase]);
      msg_print("FRINGE2_S2S_N_SRC_SURFELS[%d][%d] = %d", scale, phase, FRINGE2_S2S_N_SRC_SURFELS[scale][phase]);
      msg_print("FRINGE2_S2S_N_WEIGHTS[%d][%d] = %d", scale, phase, FRINGE2_S2S_N_WEIGHTS[scale][phase]);
      msg_print("SURF_DYN_N_SURFELS[%d][%d] = %d", scale, phase, SURF_DYN_N_SURFELS[scale][phase]);
      msg_print("SLRF_SURF_DYN_N_SURFELS[%d][%d] = %d", scale, phase, SLRF_SURF_DYN_N_SURFELS[scale][phase]);
      msg_print("MLRF_SURF_DYN_N_SURFELS[%d][%d] = %d", scale, phase, MLRF_SURF_DYN_N_SURFELS[scale][phase]);

      ccDOTIMES(isurfel_type, NUM_ISURFEL_LOAD_TYPES) {
        msg_print("ISURF_TYPES_DYN_N_ISURFELS[%d][%d][%d] = %d", scale, phase, isurfel_type, ISURF_TYPES_DYN_N_ISURFELS[scale][phase][isurfel_type]);
      }
      msg_print("ISURF_DYN_N_ISURFELS[%d][%d] = %d", scale, phase, ISURF_DYN_N_ISURFELS[scale][phase]);
    }
    ccDOTIMES(phase, 4) {
      msg_print("V2S_S2V_N_SURFELS[%d][%d] = %d", scale, phase, V2S_S2V_N_SURFELS[scale][phase]);
      msg_print("V2S_S2V_N_UBLKS[%d][%d] = %d", scale, phase, V2S_S2V_N_UBLKS[scale][phase]);
      msg_print("V2S_S2V_N_VOXELS[%d][%d] = %d", scale, phase, V2S_S2V_N_VOXELS[scale][phase]);
      msg_print("V2S_S2V_N_WEIGHTS[%d][%d] = %d", scale, phase, V2S_S2V_N_WEIGHTS[scale][phase]);
      msg_print("FRINGE_V2S_S2V_N_SURFELS[%d][%d] = %d", scale, phase, FRINGE_V2S_S2V_N_SURFELS[scale][phase]);
      msg_print("FRINGE_V2S_S2V_N_UBLKS[%d][%d] = %d", scale, phase, FRINGE_V2S_S2V_N_UBLKS[scale][phase]);
      msg_print("FRINGE_V2S_S2V_N_VOXELS[%d][%d] = %d", scale, phase, FRINGE_V2S_S2V_N_VOXELS[scale][phase]);
      msg_print("FRINGE_V2S_S2V_N_WEIGHTS[%d][%d] = %d", scale, phase, FRINGE_V2S_S2V_N_WEIGHTS[scale][phase]);
      msg_print("FRINGE2_V2S_S2V_N_SURFELS[%d][%d] = %d", scale, phase, FRINGE2_V2S_S2V_N_SURFELS[scale][phase]);
      msg_print("FRINGE2_V2S_S2V_N_UBLKS[%d][%d] = %d", scale, phase, FRINGE2_V2S_S2V_N_UBLKS[scale][phase]);
      msg_print("FRINGE2_V2S_S2V_N_VOXELS[%d][%d] = %d", scale, phase, FRINGE2_V2S_S2V_N_VOXELS[scale][phase]);
      msg_print("FRINGE2_V2S_S2V_N_WEIGHTS[%d][%d] = %d", scale, phase, FRINGE2_V2S_S2V_N_WEIGHTS[scale][phase]);
    }
  }
}



TIMER_COUNTERS::~TIMER_COUNTERS()
{
  if (!(sim.async_event_flags.timers_on_p))
    return;
  delete[] S2S_N_DEST_SURFELS;
  
  delete[] VRFINE_NEAR_N_UBLKS;
  delete[] VRFINE_FAR_N_UBLKS;
  
  ccDOTIMES(i, sim.num_scales) {
    delete[] V2S_S2V_N_SURFELS[i];
    delete[] V2S_S2V_N_UBLKS[i];
    delete[] V2S_S2V_N_VOXELS[i];
    delete[] V2S_S2V_N_WEIGHTS[i];
    
    delete[] SURF_TYPES_DYN_N_SURFELS[i][0];
    delete[] SURF_TYPES_DYN_N_SURFELS[i][1];
    delete[] SURF_TYPES_DYN_N_SURFELS[i];

    delete[] SURF_DYN_N_SURFELS[i];
    delete[] SLRF_SURF_DYN_N_SURFELS[i];
    delete[] MLRF_SURF_DYN_N_SURFELS[i];
    delete[] ISURF_TYPES_DYN_N_ISURFELS[i][0];
    delete[] ISURF_TYPES_DYN_N_ISURFELS[i][1];
    delete[] ISURF_TYPES_DYN_N_ISURFELS[i];

    delete[] ISURF_DYN_N_ISURFELS[i];
 
    delete[] S2S_N_SRC_SURFELS[i];
    delete[] S2S_N_WEIGHTS[i];

    delete[] FAR1_N_UBLKS[i];
    
    delete[] PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[i];
    delete[] PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[i];
    delete[] PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[i];
 
  }
  delete[] V2S_S2V_N_SURFELS;
  delete[] V2S_S2V_N_UBLKS;
  delete[] V2S_S2V_N_VOXELS;
  delete[] V2S_S2V_N_WEIGHTS;
  delete[] SURF_TYPES_DYN_N_SURFELS;
  delete[] SURF_DYN_N_SURFELS;
  delete[] SLRF_SURF_DYN_N_SURFELS;
  delete[] MLRF_SURF_DYN_N_SURFELS;
  delete[] S2S_N_SRC_SURFELS;
  delete[] S2S_N_WEIGHTS;

  delete[] FAR1_N_UBLKS;

  delete[] PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS;
  delete[] PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS;
  delete[] PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS;
 
  delete[] SAMPLING_SURF_DYN_N_SURFELS;
}


void PER_TIMESTEP_TIMER_COUNTERS::clear()
{
  s2s_n_dest_surfels  = 0;
  s2s_n_src_surfels   = 0;
  s2s_n_weights       = 0;
  fringe_s2s_n_dest_surfels  = 0;
  fringe_s2s_n_src_surfels   = 0;
  fringe_s2s_n_weights       = 0;
  fringe2_s2s_n_dest_surfels  = 0;
  fringe2_s2s_n_src_surfels   = 0;
  fringe2_s2s_n_weights       = 0;

  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    surf_types_dyn_n_surfels[i] = 0;
  }
  surf_dyn_n_surfels  = 0;
  slrf_surf_dyn_n_surfels = 0;
  mlrf_surf_dyn_n_surfels = 0;
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    isurf_types_dyn_n_isurfels[i] = 0;
  }
  isurf_dyn_n_isurfels  = 0;

  v2s_n_surfels       = 0;
  v2s_n_ublks         = 0;
  v2s_n_voxels        = 0;
  v2s_n_weights       = 0;
  fringe_v2s_n_surfels       = 0;
  fringe_v2s_n_ublks         = 0;
  fringe_v2s_n_voxels        = 0;
  fringe_v2s_n_weights       = 0;
  fringe2_v2s_n_surfels       = 0;
  fringe2_v2s_n_ublks         = 0;
  fringe2_v2s_n_voxels        = 0;
  fringe2_v2s_n_weights       = 0;
  s2v_n_surfels       = 0;
  s2v_n_ublks         = 0;
  s2v_n_voxels        = 0;
  s2v_n_weights       = 0;
  fringe_s2v_n_surfels       = 0;
  fringe_s2v_n_ublks         = 0;
  fringe_s2v_n_voxels        = 0;
  fringe_s2v_n_weights       = 0;
  fringe2_s2v_n_surfels       = 0;
  fringe2_s2v_n_ublks         = 0;
  fringe2_s2v_n_voxels        = 0;
  fringe2_s2v_n_weights       = 0;

  // 3 fluid load types: fluid, PM, fan
  ccDOTIMES(fluid_type, NUM_FLUID_LOAD_TYPES) {
    far1_n_ublks[fluid_type]     = 0;
    pde_near_unsplit_neighbors_n_ublks[fluid_type]  = 0;
    pde_same_split_neighbor_n_ublks[fluid_type]  = 0;
    pde_many_split_neighbor_n_ublks[fluid_type]  = 0;
    fringe_pde_far_n_ublks[fluid_type]     = 0;
    fringe_pde_near_unsplit_neighbors_n_ublks[fluid_type]  = 0;
    fringe_pde_same_split_neighbor_n_ublks[fluid_type]  = 0;
    fringe_pde_many_split_neighbor_n_ublks[fluid_type]  = 0;
    pde_far2_n_ublks[fluid_type]     = 0;
    fringe2_pde_near_unsplit_neighbors_n_ublks[fluid_type]  = 0;
    fringe2_pde_same_split_neighbor_n_ublks[fluid_type]  = 0;
    fringe2_pde_many_split_neighbor_n_ublks[fluid_type]  = 0;
  }
  
  vrfine_near_n_ublks    = 0;
  vrfine_far_n_ublks     = 0;
  fringe_vrfine_near_n_ublks    = 0;
  fringe_vrfine_far_n_ublks     = 0;
  fringe2_vrfine_near_n_ublks    = 0;
  fringe2_vrfine_far_n_ublks     = 0;

  sampling_surf_dyn_n_surfels = 0;
}

void PER_TIMESTEP_TIMER_COUNTERS::print()
{
  msg_print("****************** Counters for timestep %ld ***********************", g_timescale.m_time);
  msg_print("S2S_dest_surfels = %d S2S_src_surfels = %d S2S_weights = %d", s2s_n_dest_surfels, 
            s2s_n_src_surfels, s2s_n_weights);
  msg_print("V2S_surfels = %d V2S_ublks = %d V2S_voxels = %d V2S_weights = %d", v2s_n_surfels, v2s_n_ublks, 
            v2s_n_voxels, v2s_n_weights);
  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    msg_print("Surf_types_dyn_surfels[%d] = %d", i, surf_types_dyn_n_surfels[i]);
  }
  msg_print("Surf_dyn_surfels = %d", surf_dyn_n_surfels);
  msg_print("SLRF Surf_dyn_surfels = %d", slrf_surf_dyn_n_surfels);
  msg_print("MLRF Surf_dyn_surfels = %d", mlrf_surf_dyn_n_surfels);
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    msg_print("ISurf_types_dyn_isurfels[%d] = %d", i, isurf_types_dyn_n_isurfels[i]);
  }
  msg_print("ISurf_dyn_isurfels = %d", isurf_dyn_n_isurfels);

  msg_print("S2V_surfels = %d S2V_ublks = %d S2V_voxels = %d S2V_weights = %d", s2v_n_surfels, s2v_n_ublks, 
            s2v_n_voxels, s2v_n_weights);
  msg_print("Sampling_surf_dyn_surfels = %d", sampling_surf_dyn_n_surfels);


  msg_print("FLUID PDE FAR:   ublks = %d fan = %d porous = %d", 
            far1_n_ublks[0], far1_n_ublks[1], far1_n_ublks[2]);
  msg_print("PDE NEAR:     ublks = %d fan = %d porous = %d", 
            pde_near_unsplit_neighbors_n_ublks[0], pde_near_unsplit_neighbors_n_ublks[1],
            pde_near_unsplit_neighbors_n_porous[0]);
  msg_print("PDE SAME SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_same_split_neighbor_n_ublks[0], pde_same_split_neighbor_n_ublks[1],
            pde_same_split_neighbor_n_porous[0]);
  msg_print("PDE MANY SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_many_split_neighbor_n_ublks[0], pde_many_split_neighbor_n_ublks[1],
            pde_many_split_neighbor_n_porous[0]);

  msg_print("PM PDE FAR:      ublks = %d fan = %d porous = %d", 
            far1_n_ublks[1], far1_n_fan[1], far1_n_porous[1]);
  msg_print("PM PDE NEAR:     ublks = %d fan = %d porous = %d", 
            pde_near_unsplit_neighbors_n_ublks[1], pde_near_unsplit_neighbors_n_fan[1],
            pde_near_unsplit_neighbors_n_porous[1]);
  msg_print("PM PDE SAME SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_same_split_neighbor_n_ublks[1], pde_same_split_neighbor_n_fan[1],
            pde_same_split_neighbor_n_porous[1]);
  msg_print("PM PDE MANY SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_many_split_neighbor_n_ublks[1], pde_many_split_neighbor_n_fan[1],
            pde_many_split_neighbor_n_porous[1]);

  msg_print("FAN PDE FAR:     ublks = %d fan = %d porous = %d", 
            far1_n_ublks[2], far1_n_fan[2], far1_n_porous[2]);
  msg_print("FAN PDE NEAR:     ublks = %d fan = %d porous = %d", 
            pde_near_unsplit_neighbors_n_ublks[2], pde_near_unsplit_neighbors_n_fan[2],
            pde_near_unsplit_neighbors_n_porous[2]);
  msg_print("FAN PDE SAME SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_same_split_neighbor_n_ublks[2], pde_same_split_neighbor_n_fan[2],
            pde_same_split_neighbor_n_porous[2]);
  msg_print("FAN PDE MANY SPLIT:     ublks = %d fan = %d porous = %d", 
            pde_many_split_neighbor_n_ublks[2], pde_many_split_neighbor_n_fan[2],
            pde_many_split_neighbor_n_porous[2]);
 
  msg_print("VRFINE_FAR:      ublks = %d", 
            vrfine_far_n_ublks);
  msg_print("VRFINE_NEAR:     ublks = %d", 
            vrfine_near_n_ublks); 

}
#if 0
// compare the counters with the original sp timer counters
void PER_TIMESTEP_TIMER_COUNTERS::validate()
{
  // Check if the simulator timer counters equal the counters collected during initialization
  // surfel timers
  TIMER v2s_timer            = sp_timers + SP_V2S_TIMER;
  TIMER s2v_timer            = sp_timers + SP_S2V_TIMER;

  TIMER surf_types_dyn_timer[NUM_SURFEL_LOAD_TYPES];
  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    surf_types_dyn_timer[i] = sp_timers + i;
  }

  TIMER slrf_surf_dyn_timer  = sp_timers + SP_SLRF_SURFEL_DYN_TIMER;
  TIMER mlrf_surf_dyn_timer  = sp_timers + SP_MLRF_SURFEL_DYN_TIMER;
  TIMER mlrf_s2s_v2s_timer  = sp_timers + SP_MLRF_S2S_V2S_TIMER;
  TIMER mlrf_s2v_timer  = sp_timers + SP_MLRF_S2V_TIMER;

  TIMER isurf_types_dyn_timer[NUM_ISURFEL_LOAD_TYPES];
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    isurf_types_dyn_timer[i] = sp_timers + i + SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
  }

  TIMER s2s_timer            = sp_timers + SP_S2S_TIMER;

  // ublk timers
  TIMER pde_near_fluid_timer = sp_timers + SP_NEARBLK_DYN_TIMER;
  TIMER far1_fluid_timer  = sp_timers + SP_FARBLK1_DYN_TIMER;

  TIMER vrfine_far_timer     = sp_timers + SP_VRFINE_FAR_UBLKS_ADVECT_TIMER;
  TIMER vrfine_near_timer    = sp_timers + SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER;

  TIMER sampling_surf_dyn_timer = sp_timers + SP_SAMPLING_SURFEL_DYN_TIMER;

  if (s2s_timer->item_count[0] != s2s_n_dest_surfels)
    msg_error("s2s_timer n_dest_surfels = %d != pre-sim counter %d", s2s_timer->item_count[0], s2s_n_dest_surfels);
  if (s2s_timer->item_count[1] != s2s_n_src_surfels)
    msg_error("s2s_timer n_src_surfels = %d != pre-sim counter %d", s2s_timer->item_count[1], s2s_n_src_surfels);
  if (s2s_timer->item_count[2] != s2s_n_weights)
    msg_error("s2s_timer n_weights = %d != pre-sim counter %d", s2s_timer->item_count[2], s2s_n_weights);

  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    if (surf_types_dyn_timer[i]->item_count[0] != surf_types_dyn_n_surfels[i])
      msg_error("surf_types_dyn_timer[%d] n_surfels = %d != pre-sim counter %d", i, surf_types_dyn_timer[i]->item_count[0], surf_types_dyn_n_surfels[i]);
  }

  //if (surf_dyn_timer->item_count[0] != surf_dyn_n_surfels)
  //  msg_error("surf_dyn_timer n_surfels = %d != pre-sim counter %d", surf_dyn_timer->item_count[0], surf_dyn_n_surfels);
  if (slrf_surf_dyn_timer->item_count[0] != slrf_surf_dyn_n_surfels)
    msg_error("slrf_surf_dyn_timer n_surfels = %d != pre-sim counter %d", slrf_surf_dyn_timer->item_count[0], slrf_surf_dyn_n_surfels);
  if (mlrf_surf_dyn_timer->item_count[0] != mlrf_surf_dyn_n_surfels)
    msg_error("mlrf_surf_dyn_timer n_surfels = %d != pre-sim counter %d", mlrf_surf_dyn_timer->item_count[0], mlrf_surf_dyn_n_surfels);

  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    if (isurf_types_dyn_timer[i]->item_count[0] != isurf_types_dyn_n_isurfels[i])
      msg_error("isurf_types_dyn_timer[%d] n_isurfels = %d != pre-sim counter %d", i, isurf_types_dyn_timer[i]->item_count[0], isurf_types_dyn_n_isurfels[i]);
  }

  //if (isurf_dyn_timer->item_count[0] != isurf_dyn_n_isurfels)
  //  msg_error("isurf_dyn_timer n_isurfels = %d != pre-sim counter %d", isurf_dyn_timer->item_count[0], isurf_dyn_n_isurfels);


  if (v2s_timer->item_count[0] != v2s_n_surfels)
    msg_error("v2s_timer n_surfels = %d != pre-sim counter %d", v2s_timer->item_count[0], v2s_n_surfels);
  if (v2s_timer->item_count[1] != v2s_n_ublks)
    msg_error("v2s_timer n_ublks = %d != pre-sim counter %d", v2s_timer->item_count[1], v2s_n_ublks);
  if (v2s_timer->item_count[2] != v2s_n_voxels)
    msg_error("v2s_timer n_voxels = %d != pre-sim counter %d", v2s_timer->item_count[2], v2s_n_voxels);
  if (v2s_timer->item_count[3] != v2s_n_weights)
    msg_error("v2s_timer n_weights = %d != pre-sim counter %d", v2s_timer->item_count[3], v2s_n_weights);

  if (s2v_timer->item_count[0] != s2v_n_surfels)
    msg_error("s2v_timer n_surfels = %d != pre-sim counter %d", s2v_timer->item_count[0], s2v_n_surfels);
  if (s2v_timer->item_count[1] != s2v_n_ublks)
    msg_error("s2v_timer n_ublks = %d != pre-sim counter %d", s2v_timer->item_count[1], s2v_n_ublks);
  if (s2v_timer->item_count[2] != s2v_n_voxels)
    msg_error("s2v_timer n_voxels = %d != pre-sim counter %d", s2v_timer->item_count[2], s2v_n_voxels);
  if (s2v_timer->item_count[3] != s2v_n_weights)
    msg_error("s2v_timer n_weights = %d != pre-sim counter %d", s2v_timer->item_count[3], s2v_n_weights);

  if (sampling_surf_dyn_timer->item_count[0] != sampling_surf_dyn_n_surfels)
    msg_error("sampling_surf_dyn_timer n_surfels = %d != pre-sim counter %d", sampling_surf_dyn_timer->item_count[0], sampling_surf_dyn_n_surfels);

  if (pde_near_fluid_timer->item_count[0] != pde_near_unsplit_neighbors_n_ublks[0])
    msg_error("pde_near_fluid_timer n_ublks = %d != pre-sim counter %d", pde_near_fluid_timer->item_count[0],
                                                                         pde_near_unsplit_neighbors_n_ublks[0]);
  if (pde_near_fluid_timer->item_count[3] != pde_near_coalesce_n_voxels[0])
    msg_error("pde_near_fluid_timer coalesce_n_voxels = %d != pre-sim counter %d", pde_near_fluid_timer->item_count[3], pde_near_coalesce_n_voxels[0]);
 
  if (far1_fluid_timer->item_count[0] != far1_n_ublks[0])
    msg_error("far1_fluid_timer n_ublks = %d != pre-sim counter %d", far1_fluid_timer->item_count[0], far1_n_ublks[0]);
  if (far1_fluid_timer->item_count[3] != far1_coalesce_n_voxels[0])
    msg_error("far1_fluid_timer coalesce_n_voxels = %d != pre-sim counter %d", far1_fluid_timer->item_count[3], far1_coalesce_n_voxels[0]);
 

  if (vrfine_near_timer->item_count[0] != vrfine_near_n_ublks)
    msg_error("vrfine_near_timer n_ublks = %d != pre-sim counter %d", vrfine_near_timer->item_count[0], vrfine_near_n_ublks);
  if (vrfine_far_timer->item_count[0] != vrfine_far_n_ublks)
    msg_error("vrfine_far_timer n_ublks = %d != pre-sim counter %d", vrfine_near_timer->item_count[0], vrfine_far_n_ublks);

  msg_print("SP timer counters validated successfully.");
}
#endif

// Copy the counters to sp timers so that we do not need to collect timer counters during simulation 
void PER_TIMESTEP_TIMER_COUNTERS::copy_to_sp_timer()
{
  TIMER v2s_timer = sp_timers + SP_V2S_TIMER;
  TIMER s2v_timer = sp_timers + SP_S2V_TIMER;
  TIMER fringe_v2s_timer = sp_timers + SP_FRINGE_V2S_TIMER;
  TIMER fringe_s2v_timer = sp_timers + SP_FRINGE_S2V_TIMER;
  TIMER fringe2_v2s_timer = sp_timers + SP_FRINGE2_V2S_TIMER;
  TIMER fringe2_s2v_timer = sp_timers + SP_FRINGE2_S2V_TIMER;

  TIMER surf_types_dyn_timer[NUM_SURFEL_LOAD_TYPES];
  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    surf_types_dyn_timer[i] = sp_timers + i + SP_STANDARD_WALL_SURFEL_DYN_TIMER;
  }
  
  TIMER slrf_surf_dyn_timer  = sp_timers + SP_SLRF_SURFEL_DYN_TIMER;
  TIMER mlrf_surf_dyn_timer  = sp_timers + SP_MLRF_SURFEL_DYN_TIMER;
  TIMER mlrf_s2s_v2s_timer  = sp_timers + SP_MLRF_S2S_V2S_TIMER;
  TIMER mlrf_s2v_timer  = sp_timers + SP_MLRF_S2V_TIMER;

  TIMER isurf_types_dyn_timer[NUM_ISURFEL_LOAD_TYPES];
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    isurf_types_dyn_timer[i] = sp_timers + i + SP_APM_NOSLIP_ISURFEL_DYN_TIMER;
  }
  
  TIMER s2s_timer          = sp_timers + SP_S2S_TIMER;
  TIMER fringe_s2s_timer   = sp_timers + SP_FRINGE_S2S_TIMER;
  TIMER fringe2_s2s_timer  = sp_timers + SP_FRINGE2_S2S_TIMER;

  TIMER pde_near_fluid_timer = sp_timers + SP_NEARBLK_DYN_TIMER;
  TIMER far1_fluid_timer = sp_timers + SP_FARBLK1_DYN_TIMER;
  TIMER fringe_pde_near_fluid_timer = sp_timers + SP_FRINGE_NEARBLK_DYN_TIMER;
  TIMER fringe_pde_far_fluid_timer = sp_timers + SP_FRINGE_FARBLK_DYN_TIMER;
  TIMER fringe2_pde_near_fluid_timer = sp_timers + SP_FRINGE2_NEARBLK_DYN_TIMER;
  TIMER pde_far2_fluid_timer = sp_timers + SP_FARBLK2_DYN_TIMER;

  TIMER vrfine_near_timer = sp_timers + SP_VRFINE_NEAR_UBLKS_ADVECT_TIMER;
  TIMER vrfine_far_timer = sp_timers + SP_VRFINE_FAR_UBLKS_ADVECT_TIMER;
  TIMER fringe_vrfine_near_timer = sp_timers + SP_FRINGE_VRFINE_NEAR_UBLKS_ADVECT_TIMER;
  TIMER fringe_vrfine_far_timer = sp_timers + SP_FRINGE_VRFINE_FAR_UBLKS_ADVECT_TIMER;
  TIMER fringe2_vrfine_near_timer = sp_timers + SP_FRINGE2_VRFINE_NEAR_UBLKS_ADVECT_TIMER;
  TIMER fringe2_vrfine_far_timer = sp_timers + SP_FRINGE2_VRFINE_FAR_UBLKS_ADVECT_TIMER;

  TIMER sampling_surf_dyn_timer = sp_timers + SP_SAMPLING_SURFEL_DYN_TIMER;

  s2s_timer->item_count[0] = s2s_n_dest_surfels;
  s2s_timer->item_count[1] = s2s_n_src_surfels;
  s2s_timer->item_count[2] = s2s_n_weights;
  fringe_s2s_timer->item_count[0] = fringe_s2s_n_dest_surfels;
  fringe_s2s_timer->item_count[1] = fringe_s2s_n_src_surfels;
  fringe_s2s_timer->item_count[2] = fringe_s2s_n_weights;
  fringe2_s2s_timer->item_count[0] = fringe2_s2s_n_dest_surfels;
  fringe2_s2s_timer->item_count[1] = fringe2_s2s_n_src_surfels;
  fringe2_s2s_timer->item_count[2] = fringe2_s2s_n_weights;


  ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
    surf_types_dyn_timer[i]->item_count[0] = surf_types_dyn_n_surfels[i];
  }

  slrf_surf_dyn_timer->item_count[0] = slrf_surf_dyn_n_surfels;
  mlrf_surf_dyn_timer->item_count[0] = mlrf_surf_dyn_n_surfels;
  mlrf_s2s_v2s_timer->item_count[0] = mlrf_surf_dyn_n_surfels;
  mlrf_s2v_timer->item_count[0] = mlrf_surf_dyn_n_surfels;
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    isurf_types_dyn_timer[i]->item_count[0] = isurf_types_dyn_n_isurfels[i];
  }


  v2s_timer->item_count[0] = v2s_n_surfels;
  v2s_timer->item_count[1] = v2s_n_ublks;
  v2s_timer->item_count[2] = v2s_n_voxels;
  v2s_timer->item_count[3] = v2s_n_weights;
  fringe_v2s_timer->item_count[0] = fringe_v2s_n_surfels;
  fringe_v2s_timer->item_count[1] = fringe_v2s_n_ublks;
  fringe_v2s_timer->item_count[2] = fringe_v2s_n_voxels;
  fringe_v2s_timer->item_count[3] = fringe_v2s_n_weights;
  fringe2_v2s_timer->item_count[0] = fringe2_v2s_n_surfels;
  fringe2_v2s_timer->item_count[1] = fringe2_v2s_n_ublks;
  fringe2_v2s_timer->item_count[2] = fringe2_v2s_n_voxels;
  fringe2_v2s_timer->item_count[3] = fringe2_v2s_n_weights;

  s2v_timer->item_count[0] = s2v_n_surfels;
  s2v_timer->item_count[1] = s2v_n_ublks;
  s2v_timer->item_count[2] = s2v_n_voxels;
  s2v_timer->item_count[3] = s2v_n_weights;
  fringe_s2v_timer->item_count[0] = fringe_s2v_n_surfels;
  fringe_s2v_timer->item_count[1] = fringe_s2v_n_ublks;
  fringe_s2v_timer->item_count[2] = fringe_s2v_n_voxels;
  fringe_s2v_timer->item_count[3] = fringe_s2v_n_weights;
  fringe2_s2v_timer->item_count[0] = fringe2_s2v_n_surfels;
  fringe2_s2v_timer->item_count[1] = fringe2_s2v_n_ublks;
  fringe2_s2v_timer->item_count[2] = fringe2_s2v_n_voxels;
  fringe2_s2v_timer->item_count[3] = fringe2_s2v_n_weights;

  sampling_surf_dyn_timer->item_count[0] = sampling_surf_dyn_n_surfels;

#if 0
  pde_near_fluid_timer->item_count[0] = pde_near_unsplit_neighbors_n_ublks[0];
  pde_near_fluid_timer->item_count[1] = pde_same_split_neighbor_n_ublks[0];
  pde_near_fluid_timer->item_count[2] = pde_many_split_neighbor_n_ublks[0];
  pde_near_fluid_timer->item_count[3] = pde_near_unsplit_neighbors_n_fan[0];
  pde_near_fluid_timer->item_count[4] = pde_same_split_neighbor_n_fan[0];
  pde_near_fluid_timer->item_count[5] = pde_many_split_neighbor_n_fan[0];
  pde_near_fluid_timer->item_count[6] = pde_near_unsplit_neighbors_n_porous[0];
  pde_near_fluid_timer->item_count[7] = pde_same_split_neighbor_n_porous[0];
  pde_near_fluid_timer->item_count[8] = pde_many_split_neighbor_n_porous[0];

  far1_fluid_timer->item_count[0] = far1_n_ublks[0];
  far1_fluid_timer->item_count[1] = far1_n_fan[0];
  far1_fluid_timer->item_count[2] = far1_n_porous[0];
  fringe_pde_near_fluid_timer->item_count[0] = fringe_pde_near_unsplit_neighbors_n_ublks[0];
  fringe_pde_near_fluid_timer->item_count[1] = fringe_pde_same_split_neighbor_n_ublks[0];
  fringe_pde_near_fluid_timer->item_count[2] = fringe_pde_many_split_neighbor_n_ublks[0];
  fringe_pde_near_fluid_timer->item_count[3] = fringe_pde_near_unsplit_neighbors_n_fan[0];
  fringe_pde_near_fluid_timer->item_count[4] = fringe_pde_same_split_neighbor_n_fan[0];
  fringe_pde_near_fluid_timer->item_count[5] = fringe_pde_many_split_neighbor_n_fan[0];
  fringe_pde_near_fluid_timer->item_count[6] = fringe_pde_near_unsplit_neighbors_n_porous[0];
  fringe_pde_near_fluid_timer->item_count[7] = fringe_pde_same_split_neighbor_n_porous[0];
  fringe_pde_near_fluid_timer->item_count[8] = fringe_pde_many_split_neighbor_n_porous[0];


  fringe_pde_far_fluid_timer->item_count[0] = fringe_pde_far_n_ublks[0];
  fringe_pde_far_fluid_timer->item_count[1] = fringe_pde_far_n_fan[0];
  fringe_pde_far_fluid_timer->item_count[2] = fringe_pde_far_n_porous[0];
  fringe2_pde_near_fluid_timer->item_count[0] = fringe2_pde_near_unsplit_neighbors_n_ublks[0];
  fringe2_pde_near_fluid_timer->item_count[1] = fringe2_pde_same_split_neighbor_n_ublks[0];
  fringe2_pde_near_fluid_timer->item_count[2] = fringe2_pde_many_split_neighbor_n_ublks[0];
  fringe2_pde_near_fluid_timer->item_count[3] = fringe2_pde_near_unsplit_neighbors_n_fan[0];
  fringe2_pde_near_fluid_timer->item_count[4] = fringe2_pde_same_split_neighbor_n_fan[0];
  fringe2_pde_near_fluid_timer->item_count[5] = fringe2_pde_many_split_neighbor_n_fan[0];
  fringe2_pde_near_fluid_timer->item_count[6] = fringe2_pde_near_unsplit_neighbors_n_porous[0];
  fringe2_pde_near_fluid_timer->item_count[7] = fringe2_pde_same_split_neighbor_n_porous[0];
  fringe2_pde_near_fluid_timer->item_count[8] = fringe2_pde_many_split_neighbor_n_porous[0];

  pde_far2_fluid_timer->item_count[0] = pde_far2_n_ublks[0];
  pde_far2_fluid_timer->item_count[1] = pde_far2_n_fan[0];
  pde_far2_fluid_timer->item_count[2] = pde_far2_n_porous[0];

#else
  pde_near_fluid_timer->item_count[0] = pde_near_unsplit_neighbors_n_ublks[BASIC_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[1] = pde_same_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[2] = pde_many_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[3] = pde_near_unsplit_neighbors_n_ublks[FAN_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[4] = pde_same_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[5] = pde_many_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[6] = pde_near_unsplit_neighbors_n_ublks[POROUS_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[7] = pde_same_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];
  pde_near_fluid_timer->item_count[8] = pde_many_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];

  far1_fluid_timer->item_count[0] = far1_n_ublks[BASIC_FLUID_LOAD_TYPE];
  far1_fluid_timer->item_count[1] = far1_n_ublks[FAN_FLUID_LOAD_TYPE];
  far1_fluid_timer->item_count[2] = far1_n_ublks[POROUS_FLUID_LOAD_TYPE];

  fringe_pde_near_fluid_timer->item_count[0] = fringe_pde_near_unsplit_neighbors_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[1] = fringe_pde_same_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[2] = fringe_pde_many_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[3] = fringe_pde_near_unsplit_neighbors_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[4] = fringe_pde_same_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[5] = fringe_pde_many_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[6] = fringe_pde_near_unsplit_neighbors_n_ublks[POROUS_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[7] = fringe_pde_same_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];
  fringe_pde_near_fluid_timer->item_count[8] = fringe_pde_many_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];


  fringe_pde_far_fluid_timer->item_count[0] = fringe_pde_far_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe_pde_far_fluid_timer->item_count[1] = fringe_pde_far_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe_pde_far_fluid_timer->item_count[2] = fringe_pde_far_n_ublks[POROUS_FLUID_LOAD_TYPE];

  fringe2_pde_near_fluid_timer->item_count[0] = fringe2_pde_near_unsplit_neighbors_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[1] = fringe2_pde_same_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[2] = fringe2_pde_many_split_neighbor_n_ublks[BASIC_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[3] = fringe2_pde_near_unsplit_neighbors_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[4] = fringe2_pde_same_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[5] = fringe2_pde_many_split_neighbor_n_ublks[FAN_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[6] = fringe2_pde_near_unsplit_neighbors_n_ublks[POROUS_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[7] = fringe2_pde_same_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];
  fringe2_pde_near_fluid_timer->item_count[8] = fringe2_pde_many_split_neighbor_n_ublks[POROUS_FLUID_LOAD_TYPE];

  pde_far2_fluid_timer->item_count[0] = pde_far2_n_ublks[BASIC_FLUID_LOAD_TYPE];
  pde_far2_fluid_timer->item_count[1] = pde_far2_n_ublks[FAN_FLUID_LOAD_TYPE];
  pde_far2_fluid_timer->item_count[2] = pde_far2_n_ublks[POROUS_FLUID_LOAD_TYPE];
#endif


  vrfine_near_timer->item_count[0] = vrfine_near_n_ublks;
  vrfine_far_timer->item_count[0] = vrfine_far_n_ublks;



  fringe_vrfine_near_timer->item_count[0] = fringe_vrfine_near_n_ublks;
  fringe_vrfine_far_timer->item_count[0] = fringe_vrfine_far_n_ublks;


  fringe2_vrfine_near_timer->item_count[0] = fringe2_vrfine_near_n_ublks;
  fringe2_vrfine_far_timer->item_count[0] = fringe2_vrfine_far_n_ublks;

}

void PER_TIMESTEP_TIMER_COUNTERS::calculate(asINT32 scale, BOOLEAN is_timestep_even, TIMER_COUNTERS& timer_counters)
{

  s2s_n_dest_surfels += timer_counters.S2S_N_DEST_SURFELS[scale];
  fringe_s2s_n_dest_surfels += timer_counters.FRINGE_S2S_N_DEST_SURFELS[scale];
  fringe2_s2s_n_dest_surfels += timer_counters.FRINGE2_S2S_N_DEST_SURFELS[scale];

  
  vrfine_near_n_ublks  += timer_counters.VRFINE_NEAR_N_UBLKS[scale];
  vrfine_far_n_ublks   += timer_counters.VRFINE_FAR_N_UBLKS[scale];
  fringe_vrfine_near_n_ublks  += timer_counters.FRINGE_VRFINE_NEAR_N_UBLKS[scale];
  fringe_vrfine_far_n_ublks   += timer_counters.FRINGE_VRFINE_FAR_N_UBLKS[scale];
  fringe2_vrfine_near_n_ublks  += timer_counters.FRINGE2_VRFINE_NEAR_N_UBLKS[scale];
  fringe2_vrfine_far_n_ublks   += timer_counters.FRINGE2_VRFINE_FAR_N_UBLKS[scale];
  if (is_timestep_even) {
    s2s_n_src_surfels += timer_counters.S2S_N_SRC_SURFELS[scale][0];
    s2s_n_weights     += timer_counters.S2S_N_WEIGHTS[scale][0];
    fringe_s2s_n_src_surfels += timer_counters.FRINGE_S2S_N_SRC_SURFELS[scale][0];
    fringe_s2s_n_weights     += timer_counters.FRINGE_S2S_N_WEIGHTS[scale][0];
    fringe2_s2s_n_src_surfels += timer_counters.FRINGE2_S2S_N_SRC_SURFELS[scale][0];
    fringe2_s2s_n_weights     += timer_counters.FRINGE2_S2S_N_WEIGHTS[scale][0];
    
    v2s_n_surfels += timer_counters.V2S_S2V_N_SURFELS[scale][0];
    v2s_n_ublks   += timer_counters.V2S_S2V_N_UBLKS[scale][0];
    v2s_n_voxels  += timer_counters.V2S_S2V_N_VOXELS[scale][0];
    v2s_n_weights += timer_counters.V2S_S2V_N_WEIGHTS[scale][0];
    fringe_v2s_n_surfels += timer_counters.FRINGE_V2S_S2V_N_SURFELS[scale][0];
    fringe_v2s_n_ublks   += timer_counters.FRINGE_V2S_S2V_N_UBLKS[scale][0];
    fringe_v2s_n_voxels  += timer_counters.FRINGE_V2S_S2V_N_VOXELS[scale][0];
    fringe_v2s_n_weights += timer_counters.FRINGE_V2S_S2V_N_WEIGHTS[scale][0];
    fringe2_v2s_n_surfels += timer_counters.FRINGE2_V2S_S2V_N_SURFELS[scale][0];
    fringe2_v2s_n_ublks   += timer_counters.FRINGE2_V2S_S2V_N_UBLKS[scale][0];
    fringe2_v2s_n_voxels  += timer_counters.FRINGE2_V2S_S2V_N_VOXELS[scale][0];
    fringe2_v2s_n_weights += timer_counters.FRINGE2_V2S_S2V_N_WEIGHTS[scale][0];

    s2v_n_surfels       += timer_counters.V2S_S2V_N_SURFELS[scale][1];
    s2v_n_ublks         += timer_counters.V2S_S2V_N_UBLKS[scale][1];
    s2v_n_voxels        += timer_counters.V2S_S2V_N_VOXELS[scale][1];
    s2v_n_weights       += timer_counters.V2S_S2V_N_WEIGHTS[scale][1];
    fringe_s2v_n_surfels       += timer_counters.FRINGE_V2S_S2V_N_SURFELS[scale][1];
    fringe_s2v_n_ublks         += timer_counters.FRINGE_V2S_S2V_N_UBLKS[scale][1];
    fringe_s2v_n_voxels        += timer_counters.FRINGE_V2S_S2V_N_VOXELS[scale][1];
    fringe_s2v_n_weights       += timer_counters.FRINGE_V2S_S2V_N_WEIGHTS[scale][1];
    fringe2_s2v_n_surfels       += timer_counters.FRINGE2_V2S_S2V_N_SURFELS[scale][1];
    fringe2_s2v_n_ublks         += timer_counters.FRINGE2_V2S_S2V_N_UBLKS[scale][1];
    fringe2_s2v_n_voxels        += timer_counters.FRINGE2_V2S_S2V_N_VOXELS[scale][1];
    fringe2_s2v_n_weights       += timer_counters.FRINGE2_V2S_S2V_N_WEIGHTS[scale][1];
    
    ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
      surf_types_dyn_n_surfels[i]  += timer_counters.SURF_TYPES_DYN_N_SURFELS[scale][0][i];
    }

    surf_dyn_n_surfels  += timer_counters.SURF_DYN_N_SURFELS[scale][0];
    slrf_surf_dyn_n_surfels  += timer_counters.SLRF_SURF_DYN_N_SURFELS[scale][0];
    mlrf_surf_dyn_n_surfels  += timer_counters.MLRF_SURF_DYN_N_SURFELS[scale][0];

    ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
      isurf_types_dyn_n_isurfels[i]  += timer_counters.ISURF_TYPES_DYN_N_ISURFELS[scale][0][i];
    }

    isurf_dyn_n_isurfels  += timer_counters.ISURF_DYN_N_ISURFELS[scale][0];

    sampling_surf_dyn_n_surfels  += timer_counters.SAMPLING_SURF_DYN_N_SURFELS[scale][0];

  } else {
    s2s_n_src_surfels += timer_counters.S2S_N_SRC_SURFELS[scale][1];
    s2s_n_weights     += timer_counters.S2S_N_WEIGHTS[scale][1];
    fringe_s2s_n_src_surfels += timer_counters.FRINGE_S2S_N_SRC_SURFELS[scale][1];
    fringe_s2s_n_weights     += timer_counters.FRINGE_S2S_N_WEIGHTS[scale][1];
    fringe2_s2s_n_src_surfels += timer_counters.FRINGE2_S2S_N_SRC_SURFELS[scale][1];
    fringe2_s2s_n_weights     += timer_counters.FRINGE2_S2S_N_WEIGHTS[scale][1];
    
    v2s_n_surfels += timer_counters.V2S_S2V_N_SURFELS[scale][2];
    v2s_n_ublks   += timer_counters.V2S_S2V_N_UBLKS[scale][2];
    v2s_n_voxels  += timer_counters.V2S_S2V_N_VOXELS[scale][2];
    v2s_n_weights += timer_counters.V2S_S2V_N_WEIGHTS[scale][2];
    fringe_v2s_n_surfels += timer_counters.FRINGE_V2S_S2V_N_SURFELS[scale][2];
    fringe_v2s_n_ublks   += timer_counters.FRINGE_V2S_S2V_N_UBLKS[scale][2];
    fringe_v2s_n_voxels  += timer_counters.FRINGE_V2S_S2V_N_VOXELS[scale][2];
    fringe_v2s_n_weights += timer_counters.FRINGE_V2S_S2V_N_WEIGHTS[scale][2];
    fringe2_v2s_n_surfels += timer_counters.FRINGE2_V2S_S2V_N_SURFELS[scale][2];
    fringe2_v2s_n_ublks   += timer_counters.FRINGE2_V2S_S2V_N_UBLKS[scale][2];
    fringe2_v2s_n_voxels  += timer_counters.FRINGE2_V2S_S2V_N_VOXELS[scale][2];
    fringe2_v2s_n_weights += timer_counters.FRINGE2_V2S_S2V_N_WEIGHTS[scale][2];

    s2v_n_surfels       += timer_counters.V2S_S2V_N_SURFELS[scale][3];
    s2v_n_ublks         += timer_counters.V2S_S2V_N_UBLKS[scale][3];
    s2v_n_voxels        += timer_counters.V2S_S2V_N_VOXELS[scale][3];
    s2v_n_weights       += timer_counters.V2S_S2V_N_WEIGHTS[scale][3];
    fringe_s2v_n_surfels       += timer_counters.FRINGE_V2S_S2V_N_SURFELS[scale][3];
    fringe_s2v_n_ublks         += timer_counters.FRINGE_V2S_S2V_N_UBLKS[scale][3];
    fringe_s2v_n_voxels        += timer_counters.FRINGE_V2S_S2V_N_VOXELS[scale][3];
    fringe_s2v_n_weights       += timer_counters.FRINGE_V2S_S2V_N_WEIGHTS[scale][3];
    fringe2_s2v_n_surfels       += timer_counters.FRINGE2_V2S_S2V_N_SURFELS[scale][3];
    fringe2_s2v_n_ublks         += timer_counters.FRINGE2_V2S_S2V_N_UBLKS[scale][3];
    fringe2_s2v_n_voxels        += timer_counters.FRINGE2_V2S_S2V_N_VOXELS[scale][3];
    fringe2_s2v_n_weights       += timer_counters.FRINGE2_V2S_S2V_N_WEIGHTS[scale][3];
  
    ccDOTIMES(i, NUM_SURFEL_LOAD_TYPES) {
      surf_types_dyn_n_surfels[i]  += timer_counters.SURF_TYPES_DYN_N_SURFELS[scale][1][i];
    }
  
    surf_dyn_n_surfels  += timer_counters.SURF_DYN_N_SURFELS[scale][1];
    slrf_surf_dyn_n_surfels  += timer_counters.SLRF_SURF_DYN_N_SURFELS[scale][1];
    mlrf_surf_dyn_n_surfels  += timer_counters.MLRF_SURF_DYN_N_SURFELS[scale][1];
 
    ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
      isurf_types_dyn_n_isurfels[i]  += timer_counters.ISURF_TYPES_DYN_N_ISURFELS[scale][1][i];
    }
  
    isurf_dyn_n_isurfels  += timer_counters.ISURF_DYN_N_ISURFELS[scale][1];
    
    sampling_surf_dyn_n_surfels  += timer_counters.SAMPLING_SURF_DYN_N_SURFELS[scale][1];
  }

  // Only internal surfels are counted for slrf_surf_n_dyn_surfels in the simulation, so it should be divided by 2
  slrf_surf_dyn_n_surfels /= 2;
  
  // Isurfel pair dyn counters are incremented twice for both interior and exterior surfels. Divide them by 2
  // so that we get the number of isurfel pairs.
  ccDOTIMES(i, NUM_ISURFEL_LOAD_TYPES) {
    isurf_types_dyn_n_isurfels[i] /= 2;
  }
  isurf_dyn_n_isurfels /= 2;
  
  // Assume that all the ublks in this case are of the same load type when collecting ublk counters
  // NUM_FLUID_LOAD_TYPES = 3: fluid, PM, fan.
  ccDOTIMES(fluid_type, NUM_FLUID_LOAD_TYPES) {
    far1_n_ublks[fluid_type]   += timer_counters.FAR1_N_UBLKS[scale][fluid_type];
    pde_near_unsplit_neighbors_n_ublks[fluid_type] += timer_counters.PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type];
    pde_same_split_neighbor_n_ublks[fluid_type]   += timer_counters.PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];
    pde_many_split_neighbor_n_ublks[fluid_type]   += timer_counters.PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];
    fringe_pde_far_n_ublks[fluid_type]   += timer_counters.FRINGE_PDE_FAR_N_UBLKS[scale][fluid_type];
    fringe_pde_near_unsplit_neighbors_n_ublks[fluid_type] += timer_counters.FRINGE_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type];
    fringe_pde_same_split_neighbor_n_ublks[fluid_type]   += timer_counters.FRINGE_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];
    fringe_pde_many_split_neighbor_n_ublks[fluid_type]   += timer_counters.FRINGE_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];
    pde_far2_n_ublks[fluid_type]   += timer_counters.PDE_FAR2_N_UBLKS[scale][fluid_type];
    fringe2_pde_near_unsplit_neighbors_n_ublks[fluid_type] += timer_counters.FRINGE2_PDE_NEAR_UNSPLIT_NEIGHBORS_N_UBLKS[scale][fluid_type];
    fringe2_pde_same_split_neighbor_n_ublks[fluid_type]   += timer_counters.FRINGE2_PDE_SAME_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];
    fringe2_pde_many_split_neighbor_n_ublks[fluid_type]   += timer_counters.FRINGE2_PDE_MANY_SPLIT_NEIGHBOR_N_UBLKS[scale][fluid_type];

  }
}

// get sp timer from the fluid type
SP_TIMER_TYPE ublk_dyn_timer_from_fluid_type(BOOLEAN is_pde_advect_ublk, BOOLEAN is_split, BOOLEAN is_near_surface, auINT32 fluid_type)
{
  switch (fluid_type) {
  // We don't time mirror ublks, so this function should not be called for mirror ublks.
  case MIRROR_TYPE:
    msg_internal_error("There are no timers for mirror ublks.");
    break;
  case BASIC_FLUID_TYPE:
  case FAN_FLUID_TYPE:
  case TABLE_FAN_FLUID_TYPE:  
  case POROUS_FLUID_TYPE:
  case CURVED_POROUS_FLUID_TYPE:
  case CURVED_HX_POROUS_FLUID_TYPE:
  case CONDUCTION_SOLID_TYPE:
    if (is_pde_advect_ublk) {
      return (is_near_surface? SP_NEARBLK_DYN_TIMER : SP_FARBLK2_DYN_TIMER);
    } else {
      return SP_FARBLK1_DYN_TIMER;
    }
    break;
  default:
    msg_internal_error("fluid type %d does NOT exist!", fluid_type);
    break;
  }
  return SP_INVALID_TIMER;
}

VOID add_initial_timers_to_event_queue() {
  if(sim.timers_on_timestep != BASETIME_NEVER) {
    add_event(new_event(EVENT_ID_TIMERS_ON, 0, sim.timers_on_timestep));
  }
  if(sim.timers_off_timestep != BASETIME_NEVER) {
    add_event(new_event(EVENT_ID_TIMERS_OFF, 0, sim.timers_off_timestep));
  }
}
