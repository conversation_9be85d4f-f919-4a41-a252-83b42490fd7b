#define BSURFEL_MAINLINE

#include "gtest/gtest.h"
#include "bsurfel_test_fixture.h"
#include "fake_bsurfel_ublk_neighbor_info.h"
#include "../bsurfel_ibm.h"


TEST_F(BsurfelTestFixture, PackAndUnPack) {

  std::vector<unsigned char> buffer;

  m_bsurfel.fill_send_buffer(buffer);

  sBSURFEL new_bsurfel;

  auto it = buffer.begin();
  new_bsurfel.init(it,
                   m_ublk_table, 
                   m_char_density, 
                   m_lrf_physics_descs, 
                   m_movb_physics_descs, 
                   m_meas_windows);

  EXPECT_EQ(m_bsurfel.m_init_centroid[0] , new_bsurfel.m_init_centroid[0]); 
  EXPECT_EQ(m_bsurfel.m_init_centroid[1] , new_bsurfel.m_init_centroid[1]); 
  EXPECT_EQ(m_bsurfel.m_init_centroid[2] , new_bsurfel.m_init_centroid[2]); 
  EXPECT_EQ(m_bsurfel.m_init_normal[0] , new_bsurfel.m_init_normal[0]);   
  EXPECT_EQ(m_bsurfel.m_init_normal[1] , new_bsurfel.m_init_normal[1]);   
  EXPECT_EQ(m_bsurfel.m_init_normal[2] , new_bsurfel.m_init_normal[2]);   
  EXPECT_EQ(m_bsurfel.m_centroid[0] , new_bsurfel.m_centroid[0]);     
  EXPECT_EQ(m_bsurfel.m_centroid[1] , new_bsurfel.m_centroid[1]);     
  EXPECT_EQ(m_bsurfel.m_centroid[2] , new_bsurfel.m_centroid[2]);     
  EXPECT_EQ(m_bsurfel.m_normal[0] , new_bsurfel.m_normal[0]);       
  EXPECT_EQ(m_bsurfel.m_normal[1] , new_bsurfel.m_normal[1]);       
  EXPECT_EQ(m_bsurfel.m_normal[2] , new_bsurfel.m_normal[2]);       
  EXPECT_EQ(m_bsurfel.m_area , new_bsurfel.m_area);
  EXPECT_EQ(m_bsurfel.m_face_index , new_bsurfel.m_face_index);
  EXPECT_EQ(m_bsurfel.m_group , new_bsurfel.m_group);
  EXPECT_EQ(m_bsurfel.m_next , new_bsurfel.m_next);
  EXPECT_EQ(m_bsurfel.m_prev , new_bsurfel.m_prev);
  EXPECT_EQ(m_bsurfel.m_home_ublk , new_bsurfel.m_home_ublk);
  EXPECT_EQ(m_bsurfel.m_vel[0] , new_bsurfel.m_vel[0]);
  EXPECT_EQ(m_bsurfel.m_vel[1] , new_bsurfel.m_vel[1]);
  EXPECT_EQ(m_bsurfel.m_vel[2] , new_bsurfel.m_vel[2]);
  EXPECT_EQ(m_bsurfel.m_home_voxel , new_bsurfel.m_home_voxel);
  EXPECT_EQ(m_bsurfel.m_is_active , new_bsurfel.m_is_active);
  EXPECT_EQ(m_bsurfel.m_n_meas_cell_refs , new_bsurfel.m_n_meas_cell_refs);
  EXPECT_EQ(m_bsurfel.m_movb_physics_desc , new_bsurfel.m_movb_physics_desc);

}

TEST_F(BsurfelTestFixture, ComputeBodyForce) {

  m_bsurfel.m_sampled_fluid_velocity[0] = 10.0;
  m_bsurfel.m_sampled_fluid_velocity[1] = 4.0;
  m_bsurfel.m_sampled_fluid_velocity[2] = 7.5;

  m_bsurfel.m_vel[0] = 1.2;
  m_bsurfel.m_vel[0] = -3.4;
  m_bsurfel.m_vel[0] = 6.4;

  // This function depends on this global variable
  sim.is_turb_model = false;

  m_bsurfel.compute_body_force();

  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[0], -7.1999998092651367 );
  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[1], -8.0 );
  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[2], -15.0 );

  sim.is_turb_model = true;

  m_bsurfel.compute_body_force();

  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[0], -7.7159999656677227 );
  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[1], -10.287999954223631 );
  EXPECT_DOUBLE_EQ( m_bsurfel.m_computed_body_force[2], -12.859999942779538 );

}

TEST_F(BsurfelTestFixture, InterpolateFluidProperties) {
  // These globals are used in this function
  sim.is_heat_transfer = true;
  sim.is_turb_model = true;
  std::vector<sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO> b_info;
  cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER build{};
  b_info.push_back(
    build.init()
         .interpolation_weight(0.5)
         .lb_next_vel(0.1,0.2,0.3)
         .lb_next_density(0.1)
         .ke_prior_turb_ke(1.5)
         .ke_prior_turb_df(1.5)
         .t_next_fluid_temp(300)
         .done()
    );
  b_info.push_back(
    build.init()
         .interpolation_weight(1.0)
         .lb_next_vel(0.4,0.5,0.6)
         .lb_next_density(0.2)
         .ke_prior_turb_ke(2.5)
         .ke_prior_turb_df(2.5)
         .t_next_fluid_temp(400)
         .done()
    );
  b_info.push_back(
    build.init()
         .interpolation_weight(0.75)
         .lb_next_vel(0.7,0.8,0.9)
         .lb_next_density(0.3)
         .ke_prior_turb_ke(3.5)
         .ke_prior_turb_df(3.5)
         .t_next_fluid_temp(500).done()
    );

  m_bsurfel.interpolate_fluid_properties(b_info);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[0], 0.433333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[1], 0.533333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[2], 0.633333 ,4e-7);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_density, 0.2111111 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_temp, 411.111111 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_tke, 2.6111112 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_eps, 2.6111112 ,4e-7);

  sim.is_heat_transfer = false;
  sim.is_turb_model = false;

  m_bsurfel.interpolate_fluid_properties(b_info);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[0], 0.433333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[1], 0.533333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[2], 0.633333 ,4e-7);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_density, 0.2111111 ,4e-7);
  EXPECT_EQ( m_bsurfel.m_sampled_fluid_temp, 0.0);
  EXPECT_EQ( m_bsurfel.m_sampled_tke, 0.0 );
  EXPECT_EQ( m_bsurfel.m_sampled_eps, 0.0 );

  sim.is_heat_transfer = true;
  sim.is_turb_model = false;

  m_bsurfel.interpolate_fluid_properties(b_info);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[0], 0.433333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[1], 0.533333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[2], 0.633333 ,4e-7);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_density, 0.2111111 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_temp, 411.111111 ,4e-7);
  EXPECT_EQ( m_bsurfel.m_sampled_tke,0.0);
  EXPECT_EQ( m_bsurfel.m_sampled_eps,0.0);

  sim.is_heat_transfer = false;
  sim.is_turb_model = true;

  m_bsurfel.interpolate_fluid_properties(b_info);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[0], 0.433333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[1], 0.533333 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_velocity[2], 0.633333 ,4e-7);

  EXPECT_NEAR( m_bsurfel.m_sampled_fluid_density, 0.2111111 ,4e-7);
  EXPECT_EQ( m_bsurfel.m_sampled_fluid_temp, 0.0);
  EXPECT_NEAR( m_bsurfel.m_sampled_tke, 2.6111112 ,4e-7);
  EXPECT_NEAR( m_bsurfel.m_sampled_eps, 2.6111112 ,4e-7);

}

TEST_F(BsurfelTestFixture, NormalizeDistributedBodyForce) {
  std::vector<sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO> b_info;
  cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER build{};
  b_info.push_back(
    build.init()
         .distribution_weight(0.5)
         .voxel_volume(0.25)
         .lb_next_density(0.1)
         .done()
    );
  b_info.push_back(
    build.init()
         .distribution_weight(1.0)
         .voxel_volume(1.0)
         .lb_next_density(0.2)
         .done()
    );
  b_info.push_back(
    build.init()
         .normalizing_weight(2.5)
         .distribution_weight(0.75)
         .voxel_volume(1.9)
         .pfluid(0.6)
         .lb_next_density(0.3)
         .done()
    );

  dFLOAT sampled_force_per_area[3] = {0};

  m_bsurfel.m_computed_body_force[0] = 1.02;
  m_bsurfel.m_computed_body_force[1] = 0.789;
  m_bsurfel.m_computed_body_force[2] = 4.12;

  m_bsurfel.normalize_distributed_body_force(b_info, sampled_force_per_area);

  EXPECT_DOUBLE_EQ( sampled_force_per_area[0], 0.15635222932140191 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[1], 0.12094304797508441 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[2], 0.63154037725899581 );

  // reset for a new test
  sampled_force_per_area[0] = 0.0;
  sampled_force_per_area[1] = 0.0;
  sampled_force_per_area[2] = 0.0;

  // adding a ublk with 0 normalizing weight shouldn't change the answers
  b_info.push_back(
    build.init()
         .normalizing_weight(0.0) 
         .distribution_weight(0.75)
         .voxel_volume(1.9)
         .pfluid(0.6)
         .lb_next_density(0.3)
         .done()
    );

  m_bsurfel.normalize_distributed_body_force(b_info, sampled_force_per_area);

  EXPECT_DOUBLE_EQ( sampled_force_per_area[0], 0.15635222932140191 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[1], 0.12094304797508441 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[2], 0.63154037725899581 );

  // reset for a new test
  sampled_force_per_area[0] = 0.0;
  sampled_force_per_area[1] = 0.0;
  sampled_force_per_area[2] = 0.0;

  b_info.clear();

  b_info.push_back(
    build.init()
         .voxel_volume(2.0)
         .done()
    );

  m_bsurfel.normalize_distributed_body_force(b_info, sampled_force_per_area);

  EXPECT_DOUBLE_EQ( sampled_force_per_area[0], 0.58889727457341834 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[1], 0.45552936239061481 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[2], 2.3786831090612583 );

  // reset for a new test
  sampled_force_per_area[0] = 0.0;
  sampled_force_per_area[1] = 0.0;
  sampled_force_per_area[2] = 0.0;
  b_info.clear();

  b_info.push_back(
    build.init()
         .voxel_volume(1.0)
         .done()
    );

  m_bsurfel.normalize_distributed_body_force(b_info, sampled_force_per_area);

  EXPECT_DOUBLE_EQ( sampled_force_per_area[0], 0.58889727457341834 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[1], 0.45552936239061481 );
  EXPECT_DOUBLE_EQ( sampled_force_per_area[2], 2.3786831090612583 );


}

// PR43218 revealed that if a bsurfel interacts with a ublk of a different
// scale, then the calculated forces become twice as big. This test ensures
// this functionality works properly
TEST_F(BsurfelTestFixture, NormalizeDistributedBodyForceDifferentScales) {
  std::vector<sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO> b_info;
  cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER build{};

  dFLOAT same_scale_results[3] = {0};
  dFLOAT different_scale_results[3] = {0};

  m_bsurfel.m_computed_body_force[0] = 1.02;
  m_bsurfel.m_computed_body_force[1] = 0.789;
  m_bsurfel.m_computed_body_force[2] = 4.12;

  // the real voxel_volume returns 2^(bsurfel scale - ublk scale), so a value of
  // 1 means that the bsurfel and the ublk are in the same scale.
  b_info.push_back(
    build.init()
         .voxel_volume(1.0) 
         .done()
    );

  m_bsurfel.normalize_distributed_body_force(b_info, same_scale_results);

  b_info.clear();
  b_info.push_back(
    build.init()
         .voxel_volume(2.0) 
         .done()
    );

  m_bsurfel.normalize_distributed_body_force(b_info, different_scale_results);

  EXPECT_DOUBLE_EQ( same_scale_results[0], different_scale_results[0] );
  EXPECT_DOUBLE_EQ( same_scale_results[1], different_scale_results[1] );
  EXPECT_DOUBLE_EQ( same_scale_results[2], different_scale_results[2] );

}

// PR41981 revealed that if a bsurfel is not in the finest scale, then
// the forces are multiplied by a factor of 2^(finest_scale - bsurfel_scale)
// This test ensures the correct behavior
TEST_F(BsurfelTestFixture, NormalizeDistributedBodyForceNotFinestScale) {
  std::vector<sFAKE_BSURFEL_UBLK_NEIGHBOR_INFO> b_info;
  cFAKE_BSURFEL_UBLK_NEIGHBOR_INFO_BUILDER build{};

  dFLOAT finest_scale[3] = {0};
  dFLOAT not_finest_scale[3] = {0};

  b_info.push_back(
    build.init()
         .distribution_weight(2.5)
         .pfluid(0.7)
         .voxel_volume(1.0)
         .done()
    );

  m_bsurfel.m_computed_body_force[0] = 1.02;
  m_bsurfel.m_computed_body_force[1] = 0.789;
  m_bsurfel.m_computed_body_force[2] = 4.12;

  m_bsurfel.set_scale(sim.num_scales - 1);

  m_bsurfel.normalize_distributed_body_force(b_info, finest_scale);

  // Now set the bsurfel scale to something less
  m_bsurfel.set_scale(sim.num_scales-3);

  // When at a lower scale, the computed body force is 2^(2*(finest_scale -
  // bsurfel_scale)) smaller than at the higher scale. The initial 2 is because
  // the "force" (actually a pressure), acts over a 2 dimensional surface
  m_bsurfel.m_computed_body_force[0] /= 16;
  m_bsurfel.m_computed_body_force[1] /= 16;
  m_bsurfel.m_computed_body_force[2] /= 16;

  m_bsurfel.normalize_distributed_body_force(b_info, not_finest_scale);

  EXPECT_DOUBLE_EQ( finest_scale[0], not_finest_scale[0] );
  EXPECT_DOUBLE_EQ( finest_scale[1], not_finest_scale[1] );
  EXPECT_DOUBLE_EQ( finest_scale[2], not_finest_scale[2] );
}


