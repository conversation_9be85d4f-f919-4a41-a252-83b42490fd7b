--- HTML REPORTS -------------------------------------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/vinit/tension/pm_tests/html/results_windows.html
  Linux:   file:///fs/qa/qmtest_rundirs/vinit/tension/pm_tests/html/results_linux.html

--- INTERACTIVE HTML REPORT (Experimental) -----------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/vinit/tension/pm_tests/html/exatest_report.html
  Linux:   file:///fs/qa/qmtest_rundirs/vinit/tension/pm_tests/html/exatest_report.html

--- RUN DATA -----------------------------------------------------------------

  User ID:         vinit
  Host:            laverne.exa.com
  Run Host(s):     laverne.exa.com
  Start Time:      2019/01/16 17:16:11
  End Time:        2019/01/16 22:00:54
  Elapsed Time:    4 hour(s), 44 min(s), 42 sec(s)
  Distribution:    23678-ng6X-016-vinit-01
  Root Directory:  /fs/qa/tests/physics/particle_modeling/048-ng6X-03
  Input Directory: /fs/qa/tests/physics/particle_modeling/048-ng6X-03
  Run Directory:   /fs/qa/qmtest_rundirs/vinit/tension/pm_tests
  Command:         exatest2 run -xk -r /home/<USER>/qa/pm_tests -i "main_cmd: -nprocs 4"

--- STATISTICS ---------------------------------------------------------------

     214        <USER> <GROUP>
     134 ( 63%) tests FAIL
       5 (  2%) tests UNTESTED
      75 ( 35%) tests PASS

--- ERRORS AND FAILURES ------------------------------------------------------

  distributions/surface/hollow/gauss_fixed_points: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  distributions/volume/hollow/gamma_1           : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    sprayer-velocity_magnitude.lgraph

  drops/deferred_prs_bad_tests/bump             : FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 12 output file(s):
      film_thickness_1950.png, film_thickness_2400.png,
      film_thickness_250.png, film_thickness_2700.png,
      film_thickness_2850.png, film_thickness_4000.png, film_yvel_1950.png,
      film_yvel_2400.png, film_yvel_250.png, film_yvel_2700.png,
      film_yvel_2850.png, film_yvel_4000.png

  drops/deferred_prs_bad_tests/entrain_no_splash: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  drops/deferred_prs_bad_tests/entrainment_tiny_length: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  drops/entrain_cone                            : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  drops/plate_vr_offset                         : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s):
    film_thickness_834.png, film_yvel_834.png

  drops/plate_vrs                               : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  drops/rivulets_different                      : FAIL    
    [post_test_cmd] Detected differences in 5 output file(s):
    film_thickness_7.png, film_xvel_7.png, film_yvel_7.png, film_zvel_7.png,
    film_zvel_7_z.png

  drops/rivulets_different_2                    : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s):
    film_thickness_7.png, film_xvel_7.png, film_zvel_7.png,
    film_zvel_7_z.png

  drops/rivulets_equal                          : FAIL    
    [post_test_cmd] Detected differences in 5 output file(s):
    film_thickness_7.png, film_vmag_3.png, film_xvel_7.png, film_yvel_7.png,
    film_zvel_7.png

  rotating_geometry/lrf/nested_no_body          : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/no_body                 : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/no_reflection           : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/no_splash               : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/reflection              : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/splash                  : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/splash_w_surface_images : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/lrf/splash_w_surface_images_lrf_zero: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/rotating_wall_BCs/no_reflection: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/rotating_wall_BCs/no_splash : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/rotating_wall_BCs/reflection: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/rotating_wall_BCs/splash    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/rotating_wall_BCs/splash_w_surface_images: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  rotating_geometry/tire_tread/no_reflection    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      simulator.o, discretizer.o

  rotating_geometry/tire_tread/no_splash        : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      simulator.o, discretizer.o

  rotating_geometry/tire_tread/reflection       : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      simulator.o, discretizer.o

  rotating_geometry/tire_tread/splash           : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      simulator.o, discretizer.o

  rotating_geometry/tire_tread/splash_frozen_lgi: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  screens/liquid/leaky_screen_0_no_splash       : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/liquid/meas_close_to_screen           : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/liquid/screen_0                       : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): influx.lgraph,
    screen.lgraph, diam_step_24.png, diam_step_56.png, diam_step_88.png,
    numden_step_24.png, numden_step_56.png, numden_step_88.png

  screens/liquid/screen_0.5                     : FAIL    
    [main_cmd] Command timed out.

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      stdout.txt

  screens/liquid/screen_0.5_splash              : FAIL    
    [post_test_cmd] Detected differences in 11 output file(s):
    influx.lgraph, outflux.lgraph, screen.lgraph, diam_step_24.png,
    diam_step_56.png, diam_step_88.png, numden_step_24.png,
    numden_step_56.png, numden_step_88.png, x-vel_step_56.png,
    x-vel_step_88.png

  screens/liquid/screen_0.5_splash_turn_off_emitter: FAIL    
    [post_test_cmd] Detected differences in 10 output file(s):
    influx.lgraph, outflux.lgraph, screen.lgraph, diam_step_24.png,
    diam_step_56.png, numden_step_24.png, numden_step_56.png,
    numden_step_88.png, x-vel_step_56.png, x-vel_step_88.png

  screens/liquid/screen_0_splash_pr_40759       : FAIL    
    [post_test_cmd] Detected differences in 11 output file(s):
    influx.lgraph, outflux.lgraph, screen.lgraph, diam_step_24.png,
    diam_step_56.png, diam_step_88.png, numden_step_24.png,
    numden_step_56.png, numden_step_88.png, x-vel_step_56.png,
    x-vel_step_88.png

  screens/liquid/screen_1                       : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/liquid/screen_1_splash                : FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): influx.lgraph,
    outflux.lgraph, screen.lgraph, diam_step_24.png, diam_step_56.png,
    diam_step_88.png, numden_step_24.png, numden_step_56.png,
    numden_step_88.png

  screens/solid/leaky_screen_0_no_reflection    : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/solid/screen_0                        : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/solid/screen_0.5                      : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  screens/solid/screen_0.5_reflection           : FAIL    
    [post_test_cmd] Detected differences in 11 output file(s):
    influx.lgraph, outflux.lgraph, screen.lgraph, diam_step_24.png,
    diam_step_56.png, diam_step_88.png, numden_step_24.png,
    numden_step_56.png, numden_step_88.png, x-vel_step_56.png,
    x-vel_step_88.png

  screens/solid/screen_0_reflection             : FAIL    
    [post_test_cmd] Detected differences in 10 output file(s):
    influx.lgraph, screen.lgraph, diam_step_24.png, diam_step_56.png,
    diam_step_88.png, numden_step_24.png, numden_step_56.png,
    numden_step_88.png, x-vel_step_56.png, x-vel_step_88.png

  screens/solid/screen_1                        : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  screens/solid/screen_1_reflection             : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): influx.lgraph

  sprays/point/full/base_offset                 : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/base_offset_fine_vel_gauss  : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/const_d_breakup             : FAIL    
    [post_test_cmd] Detected differences in 27 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_242_y.png, numden_step_242_z.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_242_y.png, xvel_242_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_242_z.png, yvel_794_z.png, zvel_242_y.png, zvel_794_y.png

  sprays/point/full/const_d_breakup_with_momentum_coupling: FAIL    
    [post_test_cmd] Detected differences in 21 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_yvel.png, flux_in.png, numden_step_794_x.png,
    xvel_242_y.png, xvel_242_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_242_z.png, yvel_794_z.png, zvel_242_y.png, zvel_794_y.png

  sprays/point/full/f_a45_v30_ellipse_flat      : FAIL    
    [post_test_cmd] Detected differences in 11 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_z.png, yvel_794_z.png

  sprays/point/full/f_a45_v30_ellipse_y         : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/f_a45_v30_ellipse_yz        : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/f_a45gauss_v30_d2e-4_splash : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      247

    ExecTest.expected_exit_code:
      0

  sprays/point/full/f_a45gauss_v30_d2e-4_splash_trunc: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/f_a45u_v30_d2e-4u2e-5_deluge: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/f_a45u_v30_d2e-4u2e-5_splash: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/point/full/oblique_a30u_dir-0.2_splash : FAIL    
    [post_test_cmd] Detected differences in 16 output file(s):
    dens_step_1020_y.png, dens_step_1020_z.png, diam_in.png,
    diam_step_1020_y.png, diam_step_1020_z.png, film_thickness.png,
    film_yvel.png, film_zvel.png, flux_in.png, numden_step_1020_x.png,
    numden_step_1020_y.png, numden_step_1020_z.png, xvel_1020_y.png,
    xvel_1020_z.png, yvel_1020_z.png, zvel_1020_y.png

  sprays/point/full/oblique_a45u_dir-0.5        : FAIL    
    [post_test_cmd] Detected differences in 16 output file(s):
    dens_step_1020_y.png, dens_step_1020_z.png, diam_in.png,
    diam_step_1020_y.png, diam_step_1020_z.png, film_thickness.png,
    film_yvel.png, film_zvel.png, flux_in.png, numden_step_1020_x.png,
    numden_step_1020_y.png, numden_step_1020_z.png, xvel_1020_y.png,
    xvel_1020_z.png, yvel_1020_z.png, zvel_1020_y.png

  sprays/point/full/oblique_a45u_dir-0.5_splash : FAIL    
    [post_test_cmd] Detected differences in 14 output file(s):
    dens_step_1020_y.png, dens_step_1020_z.png, diam_in.png,
    diam_step_1020_y.png, diam_step_1020_z.png, film_zvel.png, flux_in.png,
    numden_step_1020_x.png, numden_step_1020_y.png, numden_step_1020_z.png,
    xvel_1020_y.png, xvel_1020_z.png, yvel_1020_z.png, zvel_1020_y.png

  sprays/point/full/symm_plane_reflect_liquid   : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/point/full/symm_plane_reflect_liquid_splash: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/point/full/symm_plane_reflect_solid    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/point/hollow/a20r20_v30                : FAIL    
    [post_test_cmd] Detected differences in 16 output file(s):
    dens_step_938_y.png, dens_step_938_z.png, diam_in.png,
    diam_step_938_y.png, diam_step_938_z.png, film_thickness.png,
    film_yvel.png, film_zvel.png, flux_in.png, numden_step_938_x.png,
    numden_step_938_y.png, numden_step_938_z.png, xvel_938_y.png,
    xvel_938_z.png, yvel_938_z.png, zvel_938_y.png

  sprays/point/hollow/a20r20_v30_periodic       : FAIL    
    [post_test_cmd] Detected differences in 27 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_938_y.png, dens_step_938_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_938_y.png,
    diam_step_938_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_242_y.png, numden_step_242_z.png,
    numden_step_938_x.png, numden_step_938_y.png, numden_step_938_z.png,
    xvel_242_y.png, xvel_242_z.png, xvel_938_y.png, xvel_938_z.png,
    yvel_242_z.png, yvel_938_z.png, zvel_242_y.png, zvel_938_y.png

  sprays/rain/baseline_rain_new                 : FAIL    
    [post_test_cmd] Detected differences in 23 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, film_thickness.png,
    film_yvel.png, film_zvel.png, flux_in.png, numden_step_242_y.png,
    numden_step_242_z.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, vmag_242_z.png, vmag_794_z.png, xvel_242_y.png,
    xvel_242_z.png, xvel_794_y.png, xvel_794_z.png

  sprays/rain/rain_45                           : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/rain/rain_45_more_particles            : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/rain/rain_45_splash                    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/rain/rain_finer                        : FAIL    
    [post_test_cmd] Detected differences in 21 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    film_thickness.png, film_yvel.png, film_zvel.png, flux_in.png,
    numden_step_242_y.png, numden_step_242_z.png, numden_step_794_x.png,
    numden_step_794_y.png, numden_step_794_z.png, vmag_242_z.png,
    vmag_794_z.png, xvel_242_y.png, xvel_242_z.png, xvel_794_y.png,
    xvel_794_z.png

  sprays/rain/rain_more_particles               : FAIL    
    [post_test_cmd] Detected differences in 25 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_242_y.png, numden_step_242_z.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    vmag_242_z.png, vmag_794_z.png, xvel_242_y.png, xvel_242_z.png,
    xvel_794_y.png, xvel_794_z.png

  sprays/rain/rain_splash_split                 : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/rain/rain_stripes_massless             : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/rain/rain_stripes_nozzle               : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/reflection_scatter/angle_gauss_2       : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/angle_no_dist       : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/angle_uniform_10    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/angle_uniform_20    : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/gauss_10            : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/no_dist             : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/reflection_scatter/uniform_20          : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/surface/full/f_a45_v30_spacing_ratio_splash: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/surface/full/f_a45_v30_splash          : FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/volume/full/angle_45_velocity_30_d2e-4ln: FAIL    
    [post_test_cmd] Detected differences in 15 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, xvel_794_y.png, xvel_794_z.png, yvel_794_z.png,
    zvel_794_y.png

  sprays/volume/full/angle_45_velocity_30_d2e-4rr: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/volume/full/angle_45u_velocity_30_d2e-4u2e-5_splash: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/volume/full/const_d_breakup            : FAIL    
    [post_test_cmd] Detected differences in 27 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_242_y.png, numden_step_242_z.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_242_y.png, xvel_242_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_242_z.png, yvel_794_z.png, zvel_242_y.png, zvel_794_y.png

  sprays/volume/full/const_d_breakup_with_momentum_coupling: FAIL    
    [post_test_cmd] Detected differences in 21 output file(s):
    dens_step_242_y.png, dens_step_242_z.png, dens_step_794_y.png,
    dens_step_794_z.png, diam_in.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, xvel_242_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_242_z.png, yvel_794_z.png, zvel_242_y.png, zvel_794_y.png

  sprays/volume/full/const_d_breakup_with_momentum_coupling_2: FAIL    
    [post_test_cmd] Detected differences in 27 output file(s):
    numden_step_436_x.png, dens_step_436_y.png, dens_step_436_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_436_y.png, diam_step_436_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_436_y.png, numden_step_436_z.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_436_y.png, xvel_436_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_436_z.png, yvel_794_z.png, zvel_436_y.png, zvel_794_y.png

  sprays/volume/hollow/hollow_a45r20_v30_high_gravity: FAIL    
    [post_test_cmd] Detected differences in 14 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, film_thickness.png,
    flux_in.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, xvel_794_y.png, xvel_794_z.png, yvel_794_z.png,
    zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/full/const_d_breakup: FAIL    
    [post_test_cmd] Detected differences in 27 output file(s):
    numden_step_242_x.png, dens_step_242_y.png, dens_step_242_z.png,
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_242_y.png, diam_step_242_z.png, diam_step_794_y.png,
    diam_step_794_z.png, film_thickness.png, film_yvel.png, film_zvel.png,
    flux_in.png, numden_step_242_y.png, numden_step_242_z.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_242_y.png, xvel_242_z.png, xvel_794_y.png, xvel_794_z.png,
    yvel_242_z.png, yvel_794_z.png, zvel_242_y.png, zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/full/dispersion_box: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  sprays/volume_with_fixed_release_spacing/full/f_a45_v30: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/full/liquid_splash: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/volume_with_fixed_release_spacing/full/more_particles: FAIL    
    [post_test_cmd] Detected differences in 13 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, flux_in.png,
    numden_step_794_x.png, numden_step_794_y.png, numden_step_794_z.png,
    xvel_794_y.png, xvel_794_z.png, yvel_794_z.png, zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/full/more_particles_longer_period: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/volume_with_fixed_release_spacing/full/solid: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/volume_with_fixed_release_spacing/full/solid_reflection: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/volume_with_fixed_release_spacing/hollow/drops_thru_vr: FAIL    
    [post_test_cmd] Detected differences in 11 output file(s):
    numden_x_0.35_step_302.png, numden_x_0.35_step_702.png,
    numden_x_0_step_702.png, numden_z_02_a_step_302.png,
    numden_z_02_a_step_702.png, numden_z_02_b_step_302.png,
    numden_z_02_b_step_702.png, numden_z_0_a_step_302.png,
    numden_z_0_a_step_702.png, numden_z_0_b_step_302.png,
    numden_z_0_b_step_702.png

  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r10_v30: FAIL    
    [post_test_cmd] Detected differences in 14 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, film_thickness.png,
    flux_in.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, xvel_794_y.png, xvel_794_z.png, yvel_794_z.png,
    zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r10_v30_1600_steps: FAIL    
    [post_test_cmd] Detected differences in 16 output file(s):
    dens_step_1594_y.png, dens_step_1594_z.png, diam_in.png,
    diam_step_1594_y.png, diam_step_1594_z.png, film_thickness.png,
    film_yvel.png, film_zvel.png, flux_in.png, numden_step_1594_x.png,
    numden_step_1594_y.png, numden_step_1594_z.png, xvel_1594_y.png,
    xvel_1594_z.png, yvel_1594_z.png, zvel_1594_y.png

  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30: FAIL    
    [post_test_cmd] Detected differences in 14 output file(s):
    dens_step_794_y.png, dens_step_794_z.png, diam_in.png,
    diam_step_794_y.png, diam_step_794_z.png, film_thickness.png,
    flux_in.png, numden_step_794_x.png, numden_step_794_y.png,
    numden_step_794_z.png, xvel_794_y.png, xvel_794_z.png, yvel_794_z.png,
    zvel_794_y.png

  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30_1600_steps: FAIL    
    [post_test_cmd] Detected differences in 14 output file(s):
    dens_step_1594_y.png, dens_step_1594_z.png, diam_in.png,
    diam_step_1594_y.png, diam_step_1594_z.png, film_thickness.png,
    flux_in.png, numden_step_1594_x.png, numden_step_1594_y.png,
    numden_step_1594_z.png, xvel_1594_y.png, xvel_1594_z.png,
    yvel_1594_z.png, zvel_1594_y.png

  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30_high_gravity_no_dists: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  sprays/volume_with_fixed_release_spacing/hollow/single_drops_thru_vr: FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    numden_z_02_a_step_302.png, numden_z_02_a_step_702.png,
    numden_z_02_b_step_302.png, numden_z_02_b_step_702.png,
    numden_z_0_a_step_302.png, numden_z_0_a_step_702.png,
    numden_z_0_b_step_302.png, numden_z_0_b_step_702.png

  tire_emitters/big_stretch                     : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  tire_emitters/exp_diam_gamma                  : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  tire_emitters/exp_diam_gauss                  : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): dens_x.png,
    dens_y.png, diam_x.png, vmag_x.png, yvel_x.png, zvel_x.png

  tire_emitters/exp_diam_rr                     : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): dens_x.png,
    dens_y.png, diam_x.png, vmag_x.png, yvel_x.png, zvel_x.png

  tire_emitters/exp_diam_rr_freq                : FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): dens_x.png,
    dens_y.png, diam_x.png, numden_x.png, vmag_x.png, yvel_x.png, zvel_x.png

  tire_emitters/gaussian                        : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): dens_x.png,
    dens_y.png, diam_x.png, numden_x.png, yvel_x.png, zvel_x.png

  tire_emitters/linear                          : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): dens_x.png,
    dens_y.png

  tire_emitters/linear_coarser                  : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): dens_x.png,
    dens_y.png, diam_x.png, numden_x.png, numden_y.png, vmag_x.png,
    yvel_x.png, zvel_x.png

  tire_emitters/pi                              : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): dens_x.png,
    dens_y.png, diam_x.png, vmag_x.png, yvel_x.png, zvel_x.png

  tire_emitters/spatial_exp_0.5_diam            : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  trajectories/checkpoint                       : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  trajectories/checkpoint_no_dists              : FAIL    
    [middle_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  trajectories/checkpoint_no_stop               : FAIL    
    [middle_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  trajectories/checkpoint_w_wall                : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

  trajectories/decimation                       : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s):
    vertices.traj1.pmr.txt, vertices.traj3.pmr.txt

  trajectories/decimation_1_window              : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    vertices.traj1.pmr.txt

  trajectories/enter_exit                       : FAIL    
    [post_test_cmd] Detected differences in 3 output file(s):
    num_particles.txt, entered.txt, exited.txt

  trajectories/fraction_0.123                   : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): particles.txt

  trajectories/fraction_1                       : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): particles.txt

  trajectories/hits/liquid_particles/point_emitter_liquid: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): particles.txt

  trajectories/hits/liquid_particles/screen_liquid_splash: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): particles.txt,
    verify.txt

  trajectories/multiple_no_overlap              : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  wipers/init_delay_panto_60                    : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/init_delay_panto_60_delay              : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/init_delay_pivot_60_delay              : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/panto_60                               : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/panto_60_delay                         : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/pivot_60                               : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/pivot_60_delay                         : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

  wipers/pivot_60_long_arm                      : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    film_thickness_f29.png, film_thickness_f35.png, film_vmag_f29.png,
    film_vmag_f35.png, film_xvel_f29.png, film_xvel_f35.png,
    film_yvel_f29.png, film_yvel_f35.png

--- TEST RESULTS -------------------------------------------------------------

  distributions/surface/hollow/gauss_fixed_points                                         : FAIL     (com)
  distributions/volume/hollow/gamma_1                                                     : FAIL     (com)
  drops/deferred_prs_bad_tests/bump                                                       : FAIL     (com)
  drops/deferred_prs_bad_tests/entrain_no_splash                                          : FAIL     (com)
  drops/deferred_prs_bad_tests/entrainment_tiny_length                                    : FAIL     (com)
  drops/entrain_cone                                                                      : FAIL     (com)
  drops/plate_vr_offset                                                                   : FAIL     (com)
  drops/plate_vrs                                                                         : FAIL     (com)
  drops/rivulets_different                                                                : FAIL     (com)
  drops/rivulets_different_2                                                              : FAIL     (com)
  drops/rivulets_equal                                                                    : FAIL     (com)
  rotating_geometry/lrf/nested_no_body                                                    : FAIL     (com)
  rotating_geometry/lrf/no_body                                                           : FAIL     (com)
  rotating_geometry/lrf/no_reflection                                                     : FAIL     (com)
  rotating_geometry/lrf/no_splash                                                         : FAIL     (com)
  rotating_geometry/lrf/reflection                                                        : FAIL     (com)
  rotating_geometry/lrf/splash                                                            : FAIL     (com)
  rotating_geometry/lrf/splash_w_surface_images                                           : FAIL     (com)
  rotating_geometry/lrf/splash_w_surface_images_lrf_zero                                  : FAIL     (com)
  rotating_geometry/rotating_wall_BCs/no_reflection                                       : FAIL     (com)
  rotating_geometry/rotating_wall_BCs/no_splash                                           : FAIL     (com)
  rotating_geometry/rotating_wall_BCs/reflection                                          : FAIL     (com)
  rotating_geometry/rotating_wall_BCs/splash                                              : FAIL     (com)
  rotating_geometry/rotating_wall_BCs/splash_w_surface_images                             : FAIL     (com)
  rotating_geometry/tire_tread/no_reflection                                              : FAIL     (com)
  rotating_geometry/tire_tread/no_splash                                                  : FAIL     (com)
  rotating_geometry/tire_tread/reflection                                                 : FAIL     (com)
  rotating_geometry/tire_tread/splash                                                     : FAIL     (com)
  rotating_geometry/tire_tread/splash_frozen_lgi                                          : FAIL     (com)
  screens/liquid/leaky_screen_0_no_splash                                                 : FAIL     (com)
  screens/liquid/meas_close_to_screen                                                     : FAIL     (com)
  screens/liquid/screen_0                                                                 : FAIL     (com)
  screens/liquid/screen_0.5                                                               : FAIL     (com)
  screens/liquid/screen_0.5_splash                                                        : FAIL     (com)
  screens/liquid/screen_0.5_splash_turn_off_emitter                                       : FAIL     (com)
  screens/liquid/screen_0_splash_pr_40759                                                 : FAIL     (com)
  screens/liquid/screen_1                                                                 : FAIL     (com)
  screens/liquid/screen_1_splash                                                          : FAIL     (com)
  screens/solid/leaky_screen_0_no_reflection                                              : FAIL     (com)
  screens/solid/screen_0                                                                  : FAIL     (com)
  screens/solid/screen_0.5                                                                : FAIL     (com)
  screens/solid/screen_0.5_reflection                                                     : FAIL     (com)
  screens/solid/screen_0_reflection                                                       : FAIL     (com)
  screens/solid/screen_1                                                                  : FAIL     (com)
  screens/solid/screen_1_reflection                                                       : FAIL     (com)
  sprays/point/full/base_offset                                                           : FAIL     (com)
  sprays/point/full/base_offset_fine_vel_gauss                                            : FAIL     (com)
  sprays/point/full/const_d_breakup                                                       : FAIL     (com)
  sprays/point/full/const_d_breakup_with_momentum_coupling                                : FAIL     (com)
  sprays/point/full/f_a45_v30_ellipse_flat                                                : FAIL     (com)
  sprays/point/full/f_a45_v30_ellipse_y                                                   : FAIL     (com)
  sprays/point/full/f_a45_v30_ellipse_yz                                                  : FAIL     (com)
  sprays/point/full/f_a45gauss_v30_d2e-4_splash                                           : FAIL     (com)
  sprays/point/full/f_a45gauss_v30_d2e-4_splash_trunc                                     : FAIL     (com)
  sprays/point/full/f_a45u_v30_d2e-4u2e-5_deluge                                          : FAIL     (com)
  sprays/point/full/f_a45u_v30_d2e-4u2e-5_splash                                          : FAIL     (com)
  sprays/point/full/oblique_a30u_dir-0.2_splash                                           : FAIL     (com)
  sprays/point/full/oblique_a45u_dir-0.5                                                  : FAIL     (com)
  sprays/point/full/oblique_a45u_dir-0.5_splash                                           : FAIL     (com)
  sprays/point/full/symm_plane_reflect_liquid                                             : FAIL     (com)
  sprays/point/full/symm_plane_reflect_liquid_splash                                      : FAIL     (com)
  sprays/point/full/symm_plane_reflect_solid                                              : FAIL     (com)
  sprays/point/hollow/a20r20_v30                                                          : FAIL     (com)
  sprays/point/hollow/a20r20_v30_periodic                                                 : FAIL     (com)
  sprays/rain/baseline_rain_new                                                           : FAIL     (com)
  sprays/rain/rain_45                                                                     : FAIL     (com)
  sprays/rain/rain_45_more_particles                                                      : FAIL     (com)
  sprays/rain/rain_45_splash                                                              : FAIL     (com)
  sprays/rain/rain_finer                                                                  : FAIL     (com)
  sprays/rain/rain_more_particles                                                         : FAIL     (com)
  sprays/rain/rain_splash_split                                                           : FAIL     (com)
  sprays/rain/rain_stripes_massless                                                       : FAIL     (com)
  sprays/rain/rain_stripes_nozzle                                                         : FAIL     (com)
  sprays/reflection_scatter/angle_gauss_2                                                 : FAIL     (com)
  sprays/reflection_scatter/angle_no_dist                                                 : FAIL     (com)
  sprays/reflection_scatter/angle_uniform_10                                              : FAIL     (com)
  sprays/reflection_scatter/angle_uniform_20                                              : FAIL     (com)
  sprays/reflection_scatter/gauss_10                                                      : FAIL     (com)
  sprays/reflection_scatter/no_dist                                                       : FAIL     (com)
  sprays/reflection_scatter/uniform_20                                                    : FAIL     (com)
  sprays/surface/full/f_a45_v30_spacing_ratio_splash                                      : FAIL     (com)
  sprays/surface/full/f_a45_v30_splash                                                    : FAIL     (com)
  sprays/volume/full/angle_45_velocity_30_d2e-4ln                                         : FAIL     (com)
  sprays/volume/full/angle_45_velocity_30_d2e-4rr                                         : FAIL     (com)
  sprays/volume/full/angle_45u_velocity_30_d2e-4u2e-5_splash                              : FAIL     (com)
  sprays/volume/full/const_d_breakup                                                      : FAIL     (com)
  sprays/volume/full/const_d_breakup_with_momentum_coupling                               : FAIL     (com)
  sprays/volume/full/const_d_breakup_with_momentum_coupling_2                             : FAIL     (com)
  sprays/volume/hollow/hollow_a45r20_v30_high_gravity                                     : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/const_d_breakup                           : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/dispersion_box                            : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/f_a45_v30                                 : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/liquid_splash                             : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/more_particles                            : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/more_particles_longer_period              : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/solid                                     : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/full/solid_reflection                          : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/drops_thru_vr                           : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r10_v30                       : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r10_v30_1600_steps            : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30                       : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30_1600_steps            : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/hollow_a45r20_v30_high_gravity_no_dists : FAIL     (com)
  sprays/volume_with_fixed_release_spacing/hollow/single_drops_thru_vr                    : FAIL     (com)
  tire_emitters/big_stretch                                                               : FAIL     (com)
  tire_emitters/exp_diam_gamma                                                            : FAIL     (com)
  tire_emitters/exp_diam_gauss                                                            : FAIL     (com)
  tire_emitters/exp_diam_rr                                                               : FAIL     (com)
  tire_emitters/exp_diam_rr_freq                                                          : FAIL     (com)
  tire_emitters/gaussian                                                                  : FAIL     (com)
  tire_emitters/linear                                                                    : FAIL     (com)
  tire_emitters/linear_coarser                                                            : FAIL     (com)
  tire_emitters/pi                                                                        : FAIL     (com)
  tire_emitters/spatial_exp_0.5_diam                                                      : FAIL     (com)
  trajectories/checkpoint                                                                 : FAIL     (com)
  trajectories/checkpoint_no_dists                                                        : FAIL     (com)
  trajectories/checkpoint_no_stop                                                         : FAIL     (com)
  trajectories/checkpoint_w_wall                                                          : FAIL     (com)
  trajectories/decimation                                                                 : FAIL     (com)
  trajectories/decimation_1_window                                                        : FAIL     (com)
  trajectories/enter_exit                                                                 : FAIL     (com)
  trajectories/fraction_0.123                                                             : FAIL     (com)
  trajectories/fraction_1                                                                 : FAIL     (com)
  trajectories/hits/liquid_particles/point_emitter_liquid                                 : FAIL     (com)
  trajectories/hits/liquid_particles/screen_liquid_splash                                 : FAIL     (com)
  trajectories/multiple_no_overlap                                                        : FAIL     (com)
  wipers/init_delay_panto_60                                                              : FAIL     (com)
  wipers/init_delay_panto_60_delay                                                        : FAIL     (com)
  wipers/init_delay_pivot_60_delay                                                        : FAIL     (com)
  wipers/panto_60                                                                         : FAIL     (com)
  wipers/panto_60_delay                                                                   : FAIL     (com)
  wipers/pivot_60                                                                         : FAIL     (com)
  wipers/pivot_60_delay                                                                   : FAIL     (com)
  wipers/pivot_60_long_arm                                                                : FAIL     (com)

  distributions/point/elliptical/gauss                                                    : PASS     (com)
  distributions/point/elliptical/uniform                                                  : PASS     (com)
  distributions/point/full/gamma_9_neg_x                                                  : PASS     (com)
  distributions/point/full/gauss                                                          : PASS     (com)
  distributions/point/full/gauss_ln                                                       : PASS     (com)
  distributions/point/full/gauss_rr                                                       : PASS     (com)
  distributions/point/full/uniform_-1_1_-1                                                : PASS     (com)
  distributions/point/hollow/gamma_5                                                      : PASS     (com)
  distributions/point/hollow/gamma_9                                                      : PASS     (com)
  distributions/point/hollow/gamma_9_1_-1_-1                                              : PASS     (com)
  distributions/point/hollow/gauss                                                        : PASS     (com)
  distributions/point/hollow/gauss_-1_-1_-1                                               : PASS     (com)
  distributions/point/hollow/gauss_ln                                                     : PASS     (com)
  distributions/point/hollow/gauss_rr                                                     : PASS     (com)
  distributions/point/hollow/uniform                                                      : PASS     (com)
  distributions/point/hollow/uniform_-1_1_-1                                              : PASS     (com)
  distributions/surface/full/gamma_9_neg_x                                                : PASS     (com)
  distributions/surface/full/gauss_ln                                                     : PASS     (com)
  distributions/surface/full/gauss_rr                                                     : PASS     (com)
  distributions/surface/full/uniform_-1_1_-1                                              : PASS     (com)
  distributions/surface/hollow/gamma_5                                                    : PASS     (com)
  distributions/surface/hollow/gamma_9                                                    : PASS     (com)
  distributions/surface/hollow/gamma_9_1_-1_-1                                            : PASS     (com)
  distributions/surface/hollow/gauss                                                      : PASS     (com)
  distributions/surface/hollow/gauss_-1_-1_-1                                             : PASS     (com)
  distributions/surface/hollow/gauss_ln                                                   : PASS     (com)
  distributions/surface/hollow/gauss_mesh_fixed                                           : PASS     (com)
  distributions/surface/hollow/gauss_mesh_surf                                            : PASS     (com)
  distributions/surface/hollow/gauss_rr                                                   : PASS     (com)
  distributions/surface/hollow/uniform                                                    : PASS     (com)
  distributions/surface/hollow/uniform_-1_1_-1                                            : PASS     (com)
  distributions/volume/full/gamma_0.5                                                     : PASS     (com)
  distributions/volume/full/gamma_1                                                       : PASS     (com)
  distributions/volume/full/gamma_9_neg_x                                                 : PASS     (com)
  distributions/volume/full/gauss                                                         : PASS     (com)
  distributions/volume/full/gauss_-1_-1_-1                                                : PASS     (com)
  distributions/volume/full/gauss_fixed_points                                            : PASS     (com)
  distributions/volume/full/gauss_ln                                                      : PASS     (com)
  distributions/volume/full/gauss_rr                                                      : PASS     (com)
  distributions/volume/full/uniform                                                       : PASS     (com)
  distributions/volume/full/uniform_-1_1_-1                                               : PASS     (com)
  distributions/volume/hollow/gamma_5                                                     : PASS     (com)
  distributions/volume/hollow/gamma_9                                                     : PASS     (com)
  distributions/volume/hollow/gamma_9_1_-1_-1                                             : PASS     (com)
  distributions/volume/hollow/gamma_9_neg_x                                               : PASS     (com)
  distributions/volume/hollow/gauss                                                       : PASS     (com)
  distributions/volume/hollow/gauss_-1_-1_-1                                              : PASS     (com)
  distributions/volume/hollow/gauss_fixed_points                                          : PASS     (com)
  distributions/volume/hollow/gauss_ln                                                    : PASS     (com)
  distributions/volume/hollow/gauss_rr                                                    : PASS     (com)
  distributions/volume/hollow/uniform                                                     : PASS     (com)
  distributions/volume/hollow/uniform_-1_1_-1                                             : PASS     (com)
  drops/emitter_45                                                                        : PASS     (com)
  drops/pendulum                                                                          : PASS     (com)
  drops/plate                                                                             : PASS     (com)
  drops/plate_45                                                                          : PASS     (com)
  drops/plate_half_g                                                                      : PASS     (com)
  drops/plate_nonaligned                                                                  : PASS     (com)
  drops/terminal_velocity                                                                 : PASS     (com)
  trajectories/fraction_0                                                                 : PASS     (com)
  trajectories/hits/liquid_particles/liquid                                               : PASS     (com)
  trajectories/hits/liquid_particles/liquid_gravity                                       : PASS     (com)
  trajectories/hits/liquid_particles/liquid_gravity_smaller_traj_window                   : PASS     (com)
  trajectories/hits/liquid_particles/liquid_smaller_trajectory_window                     : PASS     (com)
  trajectories/hits/liquid_particles/liquid_splash                                        : PASS     (com)
  trajectories/hits/liquid_particles/liquid_splash_w_film_traj                            : PASS     (com)
  trajectories/hits/liquid_particles/liquid_w_film_traj                                   : PASS     (com)
  trajectories/hits/liquid_particles/point_emitter_liquid_splash                          : PASS     (com)
  trajectories/hits/liquid_particles/screen_liquid                                        : PASS     (com)
  trajectories/hits/liquid_particles/surface_emitter_liquid                               : PASS     (com)
  trajectories/hits/solid_particles/protected_geometry                                    : PASS     (com)
  trajectories/hits/solid_particles/reflection_solid                                      : PASS     (com)
  trajectories/hits/solid_particles/screen_solid                                          : PASS     (com)
  trajectories/hits/solid_particles/screen_solid_reflection                               : PASS     (com)
  trajectories/hits/solid_particles/solid                                                 : PASS     (com)

  drops/deferred_prs_bad_tests/bump_entrain                                               : UNTESTED (com)
  drops/deferred_prs_bad_tests/no_reentrain_no_splash                                     : UNTESTED (com)
  drops/entrain_cone_no_splash                                                            : UNTESTED (com)
  rotating_geometry/tire_tread/no_splash_no_omega                                         : UNTESTED (com)
  trajectories/nested_windows                                                             : UNTESTED (com)
