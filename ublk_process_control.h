/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Main Simulation Loop
 *
 * Vinit Gupta, Exa Corporation
 * Created Tue Sep 5 2017
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_UBLK_PROCESS_CONTROL_H
#define _SIMENG_UBLK_PROCESS_CONTROL_H

#include "common_sp.h"
#include "lattice.h"
#include "timescale.h"
#include "shob_groups.h"
#include "strand_enum.h"


#if BUILD_GPU

namespace GPU {
template<BOOLEAN IS_T_S_LB_SOLVER_ON, BOOLEAN IS_UDS_LB_SOLVER_ON>
void explode_vr_groups_of_scale(SCALE scale,
                                    SOLVER_INDEX_MASK prior_solver_index_mask,
                                    ACTIVE_SOLVER_MASK active_solver_mask,
                                    UBLK_GROUP_TYPE group_type,
                                    bool process_ghost); 
}

/*=================================================================================================
 * @class cGPU_VR_EXPLODE_CONTROL
 * Unlike the CPU code, during advection, VR FINE neighbors that are part of other STRAND groups
 * cannot be exploded on demand. Depending on STRAND ordering (static or dynamic), it is inadequate 
 * to perform explode ONLY during VR FINE advection. Therefore, we explode all VR FINE MBLKs of a 
 * certain scale when the first group of that scale gets processed.
 * Subsequently, other groups will not trigger an explode for the same timestep since it's already
 * been done once. The explode flag is reset on ODD timesteps.
 *=================================================================================================*/
enum class vr_fine_comm
{
  no_comm,
  near,
  far,
};
constexpr size_t vr_fine_comm_size = 3;

class cGPU_VR_EXPLODE_CONTROL {

private:
  using vr_fine_comm_flags = std::array<std::array<bool,2>,vr_fine_comm_size>;
  std::array<vr_fine_comm_flags,STP_MAX_SCALES> m_vr_fine_flags = {};
public:

  auto is_explode_done(STP_SCALE scale, vr_fine_comm type, bool ghost) {
    return m_vr_fine_flags[scale][static_cast<size_t>(type)][static_cast<size_t>(ghost)];
  }
  void mark_explode_done(STP_SCALE scale, vr_fine_comm type, bool ghost) {
    m_vr_fine_flags[scale][static_cast<size_t>(type)][static_cast<size_t>(ghost)] = true;
  }
  void reset_explode_flag(STP_SCALE scale) {
    for(auto& array : m_vr_fine_flags[scale])
      array.fill(false);
  }
  VOID maybe_explode_vr_fine_groups_of_scale(STP_SCALE scale,
                                             SOLVER_INDEX_MASK prior_solver_index_mask,
                                             ACTIVE_SOLVER_MASK active_solver_mask,
                                             STRAND strand_type);
};  




// @global g_vr_explode_control
// UBLK_PROCESS_CONTROL instances across strand groups share this global
// control for deciding if explode is required to be done or not
extern cGPU_VR_EXPLODE_CONTROL g_vr_explode_control;
#endif
  
class sUBLK_PROCESS_CONTROL {
  
public:
  SCALE m_scale;
  ACTIVE_SOLVER_MASK m_active_solver_mask;
  ACTIVE_SOLVER_MASK m_last_active_solver_mask;
  ACTIVE_SOLVER_MASK m_even_active_solver_mask;
  ACTIVE_SOLVER_MASK m_odd_active_solver_mask;

  BOOLEAN m_is_timestep_even;
  BOOLEAN m_is_2D;
  SOLVER_INDEX_MASK m_prior_solver_index_mask;
  
  sUBLK_PROCESS_CONTROL() {
    m_scale = -1;
    m_active_solver_mask = 0;
    m_last_active_solver_mask = 0;
    m_even_active_solver_mask = 0;
    m_odd_active_solver_mask = 0;

    m_is_timestep_even = TRUE;
    m_is_2D = TRUE;
    m_prior_solver_index_mask = 0;
  }

  VOID init(const SCALE scale, const TIMESTEP_PARITY running_parity = g_timescale.time_parity()) {
    set_scale(scale);
    set_active_solver_masks(scale, running_parity);
    set_prior_solver_index_mask(scale);
    auINT32 odd_timestep_mask = (1 << (sim.num_scales - scale)) - 1;
    m_is_timestep_even = ((g_timescale.m_timestep & odd_timestep_mask) != odd_timestep_mask);
    m_is_2D = sim.is_2d();
  }

  VOID set_scale(const SCALE scale) {
    m_scale = scale;
  }

  VOID  set_active_solver_masks(const SCALE scale, const TIMESTEP_PARITY running_parity) {
    m_active_solver_mask = g_timescale.m_active_solver_masks[running_parity][scale];
    m_even_active_solver_mask = g_timescale.m_even_active_solver_masks[running_parity][scale];
    m_odd_active_solver_mask = g_timescale.m_odd_active_solver_masks[running_parity][scale];
    if (scale > 0) {
      m_last_active_solver_mask = g_timescale.m_last_active_solver_masks[coarsen_scale(scale)];
    } else {
      m_last_active_solver_mask = 0;
    }
  }

  VOID set_prior_solver_index_mask(const SCALE scale){
    m_prior_solver_index_mask = compute_solver_index_mask(scale);
  }

  BOOLEAN is_lb_active() {
    return ((m_active_solver_mask & LB_ACTIVE) != 0);
  }
  BOOLEAN is_turb_active() {
    return ((m_active_solver_mask & KE_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_temp_active() {
    return ((m_active_solver_mask & T_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_uds_active() {
    return ((m_active_solver_mask & UDS_PDE_ACTIVE) != 0);
  }
  BOOLEAN is_time_step_odd() {
    return (m_odd_active_solver_mask != 0);
  }

#if BUILD_GPU  
  VOID process_all_ublks_in_group(MBLK_GROUP group,
				  UBLK_GROUP_TYPE group_type, 
				  STRAND strand_type);
#else
  VOID process_all_ublks_in_group(UBLK_GROUP group,
				  UBLK_GROUP_TYPE group_type, 
				  STRAND strand_type);
#endif
  
  VOID process_all_ublks(STRAND strand_type, UBLK_GROUP_TYPE group_type);
  VOID process_all_ublks_dyn(STRAND strand_type, UBLK_GROUP_TYPE group_type);
  VOID advect_all_vr_fine_ublks(STRAND strand_type, UBLK_GROUP_TYPE group_type); 
#if BUILD_5G_LATTICE
  VOID construct_all_ublks_adv_states(STRAND strand_type, UBLK_GROUP_TYPE group_type);
#endif

#if BUILD_GPU
  VOID maybe_explode_vr_fine_groups_of_scale(STRAND strand_type) {
    if (m_is_timestep_even) {
      g_vr_explode_control.maybe_explode_vr_fine_groups_of_scale(m_scale,
                                                                 m_prior_solver_index_mask,
                                                                 m_active_solver_mask,
                                                                 strand_type);
    } else {
      g_vr_explode_control.reset_explode_flag(m_scale);
    }
  }
#endif
};

#endif /* #ifndef _SIMENG_UBLK_PROCESS_CONTROL_H*/
