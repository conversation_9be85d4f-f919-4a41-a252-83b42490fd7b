/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */

/*--------------------------------------------------------------------------*
 * Generate warning/error to send up-stream to the CP
 *--------------------------------------------------------------------------*/
#ifndef _SIMENG_SIMERR_H
#define _SIMENG_SIMERR_H


// This flag was created to allow errors to be suppressed during the calls to
// to SURFEL_DYN_GROUP::dynamics inside surfel seeding.
extern cBOOLEAN simerr_suppress_errors;

VOID simerr_report_error_code(SP_EEP_TYPES error_code, asINT32 scale, sdFLOAT *point, ...);

VOID simerr_init(asINT32 initial_sp_error_count);

BOOLEAN simerr_enable_warning(SP_EEP_TYPES type, BOOLEAN enable);

BOOLEAN is_simerr_queue_empty();

BOOLEAN simerr_mpi_process(BOOLEAN probe_p = TRUE);

VOID simerr_finalize();

#endif /*  _SIMENG_SIMERR_H */
