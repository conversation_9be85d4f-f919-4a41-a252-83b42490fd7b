--- HTML REPORTS -------------------------------------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/vinit/tension/sim_coll/html/results_windows.html
  Linux:   file:///fs/qa/qmtest_rundirs/vinit/tension/sim_coll/html/results_linux.html

--- INTERACTIVE HTML REPORT (Experimental) -----------------------------------

  Windows: file:///x://fs/qa/qmtest_rundirs/vinit/tension/sim_coll/html/exatest_report.html
  Linux:   file:///fs/qa/qmtest_rundirs/vinit/tension/sim_coll/html/exatest_report.html

--- RUN DATA -----------------------------------------------------------------

  User ID:         vinit
  Host:            substrate
  Run Host(s):     substrate, ambient, apoapsis, emulsion, anabatic, laverne, scarab
  Start Time:      2018/11/17 10:41:29
  End Time:        2018/11/17 15:10:29
  Elapsed Time:    4 hour(s), 28 min(s), 59 sec(s)
  Distribution:    23678-ng6-024-vinit-01
  Root Directory:  /fs/qa/tests/physics/collections/178-ng6-14
  Input Directory: /fs/qa/tests/physics/collections/178-ng6-14
  Run Directory:   /fs/qa/qmtest_rundirs/vinit/tension/sim_coll
  Command:         exatest2 run -xk -r /home/<USER>/qa/sim_coll -i "main_cmd: -nprocs 4" --hosts substrate:ambient:apoapsis:emulsion:anabatic:laverne:scarab

--- STATISTICS ---------------------------------------------------------------

     433        <USER> <GROUP>
     119 ( 27%) tests FAIL
       6 (  1%) tests UNTESTED
     308 ( 71%) tests PASS

--- ERRORS AND FAILURES ------------------------------------------------------

  basicPhysics/APM/tortuosity/tort_1.5          : FAIL    
    [post_test_cmd2] Detected differences in 2 output file(s):
    static_p_f10.png, static_p_f26.png

  basicPhysics/APM/tortuosity/tort_2.0          : FAIL    
    [post_test_cmd2] Detected differences in 1 output file(s):
    static_p_f10.png

  basicPhysics/autostop/cool/condenser/value    : FAIL    
    [main_cmd] Detected differences in 1 output file(s): monitor-1.dat

  basicPhysics/autostop/cool/non_rectangular    : FAIL    
    [main_cmd] Detected differences in 2 output file(s): simulator.o,
    monitor-1.dat

    [post_test_cmd] [from --keep-comparing] Detected differences in 3 output file(s):
      total_pressure_z.png, x-vel_z.png, temperature_time_history.lgraph

  basicPhysics/autostop/cool/spec_heat_reject_four: FAIL    
    [main_cmd] Detected differences in 1 output file(s): monitor-4.dat

  basicPhysics/autostop/surface/rect_mesh       : FAIL    
    [main_cmd] Detected differences in 1 output file(s): monitor-1.dat

  basicPhysics/autostop/therm/exHs_back         : FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

  basicPhysics/autostop/therm/exHs_direct_addVr3_StagNo_numSC1_Standard: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

  basicPhysics/autostop/therm/therm_conf        : FAIL    
    [main_cmd] Detected differences in 2 output file(s): monitor-1.dat,
    exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

    [post_test_cmd] [from --keep-comparing] Detected differences in 5 output file(s):
      exhaust_heatshield.out, heat_shield_z.png, vmag.png, floor_pan.png,
      temperature.png

  basicPhysics/autostop/therm_cool/therm_cool_conf: FAIL    
    [main_cmd] Detected differences in 3 output file(s): monitor-2.dat,
    monitor-1.dat, exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

    [post_test_cmd] [from --keep-comparing] Detected differences in 5 output file(s):
      exhaust_heatshield.out, heat_shield_z.png, vmag.png, floor_pan.png,
      temperature.png

  basicPhysics/autostop/therm_cool/therm_cool_mons_stab_conf_user_spec_it: FAIL    
    [main_cmd] Detected differences in 3 output file(s): monitor-2.dat,
    monitor-1.dat, exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

    [post_test_cmd] [from --keep-comparing] Detected differences in 5 output file(s):
      exhaust_heatshield.out, heat_shield_z.png, vmag.png, floor_pan.png,
      temperature.png

  basicPhysics/autostop/therm_cool/therm_cool_val_conf: FAIL    
    [main_cmd] Detected differences in 3 output file(s): monitor-2.dat,
    monitor-1.dat, exHs_direct_addVr3_StagNo_numSC1_Standard.monitors

    [post_test_cmd] [from --keep-comparing] Detected differences in 5 output file(s):
      exhaust_heatshield.out, heat_shield_z.png, vmag.png, floor_pan.png,
      temperature.png

  basicPhysics/buoyancy                         : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): y-vel.png,
    vmag.png, yvel_space.lgraph, yvel_time.lgraph, temperature_space.lgraph,
    temperature_time.lgraph

  basicPhysics/flowOscillate                    : FAIL    
    [post_test_cmd] Detected differences in 20 output file(s): cp040.png,
    cp035.png, cp100.png, cp010.png, cp020.png, cp025.png, cp061.png,
    cp055.png, cp030.png, cp090.png, cp120.png, cp080.png, cp144.png,
    cp140.png, cp070.png, cp045.png, cp005.png, cp015.png, cp050.png,
    cp018.png

  basicPhysics/mrf/3dAligned                    : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): totPaMEAS.png,
    vmagMEAS.png

  basicPhysics/multiphase/slug_in_sinusoidal_channel: FAIL    
    [post_test_cmd] Detected differences in 3 output file(s):
    comp0_density.png, total_density.png, comp1_density.png

  basicPhysics/smokeTests/LTT/NACA0012_AOA10_ltt: FAIL    
    [post_test_cmd] Detected differences in 8 output file(s):
    SNAP-tdiss-view-all.png, MEAS-tdiss-view-all.png,
    SNAP-sij-view-zoomTE.png, MEAS-tdiss-view-zoomTE.png,
    SNAP-tdiss-view-zoomTE.png, MEAS-sij-view-all.png,
    MEAS-sij-view-zoomTE.png, SNAP-sij-view-all.png

  basicPhysics/smokeTests/autostop/therm/battery_cooling: FAIL    
    [main_cmd] Detected differences in 1 output file(s): monitor-1.dat

  basicPhysics/smokeTests/bit-for-bit/4P/thermalpassive_from_isotherm: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s):
    simvol.fnc.txt, simvol.snc.txt

  basicPhysics/smokeTests/bumps/3D/attached     : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s): total_p.png,
    xvel.png, wake_xvel_time_history.lgraph, xvel.lgraph

  basicPhysics/smokeTests/bumps/3D/separated    : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s): total_p.png,
    xvel.png, wake_xvel_time_history.lgraph, xvel.lgraph

  basicPhysics/smokeTests/defrost/3d_acc_film_devel_w_defrost_time: FAIL    
    [post_test_cmd] Detected differences in 10 output file(s):
    film_thickness.png, heat_flux.png, defrost_time.png,
    water_vapor_mass_flux.png, temperature.png,
    surf_vapor_mass_fraction.png,
    temperature-history_x0.15-y0.044-z0.05.lgraph,
    heat-flux-history_x0.1-y0.05-z0.05.lgraph,
    temperature-history_x0.1-y0.05-z0.05.lgraph,
    UDS-history_x0.15-y0.044-z0.05.lgraph

  basicPhysics/smokeTests/defrost/coupled_compressible: FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): heat_flux.png,
    water_vapor_mass_flux.png, surf_vapor_mass_fraction.png,
    T-history_x0.074-y0.036-z0.05.lgraph,
    Film-thickness-history_x0.074-y0.036-z0.05.lgraph,
    T-history_x0.15-y0.044-z0.052.lgraph,
    Heat-flux-history_x0.074-y0.036-z0.05.lgraph

  basicPhysics/smokeTests/exa_signal_events/MMECkpt/ScheduledEarly: FAIL    
    [sim_cmd] Command timed out.

  basicPhysics/smokeTests/exa_signal_events/SetMaxMinTemp: FAIL    
    [sim_cmd] Detected differences in 1 output file(s): simulator.o

  basicPhysics/smokeTests/forceDev/nested_lrf   : FAIL    
    [post_test_cmd] Detected differences in 6 output file(s):
    xvel_middle.png, yvel_grf.png, yvel_middle.png, yvel_big.png,
    Force-dev_frame10.lgraph, Force-dev_frame20.lgraph

  basicPhysics/smokeTests/forceDev/slidingMeshPM/3D: FAIL    
    [post_test_cmd] Detected differences in 4 output file(s):
    simvol-snap-snc-tdevx.lgraph, simvol-snap-dsnc-tdevx.lgraph,
    simvol-snap-dsnc-tdevz-rotated.lgraph,
    simvol-snap-snc-tdevz-rotated.lgraph

  basicPhysics/smokeTests/highSubsonic/3d-dlr-f4: FAIL    
    [post_test_cmd] Detected differences in 5 output file(s): y-vel_z.png,
    mach_y_0.png, x-vel_z.png, mach_y_0.15.png, mach_z.png

  basicPhysics/smokeTests/measTests/checkpointResume/therm_cool: FAIL    
    [post_test_cmd1] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [post_test_cmd1] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/measTests/checkpointResume/therm_cool_run_past_checkpoint: FAIL    
    [post_test_cmd1] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [post_test_cmd1] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/mrf/3dCoupled         : FAIL    
    [post_test_cmd] Detected differences in 5 output file(s): tempINIin.png,
    tempSNAP.png, tempINI.png, tempMEAS.png, tempMEASout.png

  basicPhysics/smokeTests/mrf/3dInclined        : FAIL    
    [post_test_cmd] Detected differences in 5 output file(s):
    totPaMEASout.png, vmagMEASin.png, vmagMEASout.png, totPaMEASin.png,
    vmagMEAS.png

  basicPhysics/smokeTests/multiphase/slug_between_plates: FAIL    
    [post_test_cmd] Detected differences in 3 output file(s):
    comp0_density.png, total_density.png, comp1_density.png

  basicPhysics/smokeTests/particle_modelling/two_pmr_files_beam: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    trajectory2.pmr.txt

  basicPhysics/smokeTests/particle_modelling/two_pmr_files_gauss: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s):
    trajectory1.pmr.txt, trajectory2.pmr.txt

  basicPhysics/smokeTests/powercool/partition_tests/condenser: FAIL    
    [main_cmd] Command timed out.

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/powercool/partition_tests/radiator: FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

  basicPhysics/smokeTests/rotating_tire_tread/crooked: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

  basicPhysics/smokeTests/rotating_tire_tread/crooked_vr: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      temperature.png

  basicPhysics/smokeTests/rotating_tire_tread/resume_test: FAIL    
    [post_test_cmd1] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      discretizer.o

    [post_test_cmd1] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/rotating_tire_tread/sq_cyl_annular: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

  basicPhysics/smokeTests/rotating_tire_tread/stationary_sq_cyl: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 2 output file(s):
      vmag.png, cyl.lgraph

  basicPhysics/smokeTests/rotating_tire_tread/tread_rotating: FAIL    
    [main_cmd_tires] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      pressure.png

  basicPhysics/smokeTests/rotating_tire_tread/tread_rotating_w_floor: FAIL    
    [main_cmd_tires] Detected differences in 1 output file(s): discretizer.o

  basicPhysics/smokeTests/rotating_tire_tread/two_treads_floor_ramped_velocity: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 7 output file(s):
      vmag.png, x-vel.png, density.png, pressure.png, temperature.png,
      tread_front.lgraph, tire_front.lgraph

  basicPhysics/smokeTests/slidingMesh/nested_LRF/grf_nested_lrf_noflow: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    vmag_containing_y.png

  basicPhysics/smokeTests/slidingMesh/noVR_isothermal_05v_slrf: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): fluid-sij.png,
    surf-htc.png

  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/diff_vr: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): staticp1.png

  basicPhysics/smokeTests/slidingMesh/solidXslrf: FAIL    
    [post_test_cmd] Detected differences in 11 output file(s): surf-csf.png,
    fluid-sij.png, surf-htc.png, fluid-eddy.png, surf-htc-xalign.lgraph,
    surf-htc.lgraph, fluid-tke.lgraph, fluid-eddy.lgraph,
    fluid-tke-xalign.lgraph, fluid-sij-xalign.lgraph,
    fluid-eddy-xalign.lgraph

  basicPhysics/smokeTests/snowconeV3            : FAIL    
    [post_test_cmd] Detected differences in 147 output file(s):
    z-00-f64-xvel.png, z-00-f24-xvel.png, surf_wheel_p_f24_up.png,
    y-m0125-f64-sij.png, y-m045-f88-tke.png, surf_wheel_p_f24_down.png,
    x-00-f88-tke.png, z-035-f64-eddy.png, x-00-f88-vmag.png,
    z-035-f64-tdiss.png, surf_p_f64_y.png, x-1.6-f88-sij.png,
    x-00-f64-xvel.png, x-1.6-f64-sij.png, surf_wheel_vmag_f64_down.png,
    y-m045-f64-tke.png, y-m045-f08-p.png, y-045-f88-t.png, x-00-f88-pt.png,
    z-055-f64-xvel.png, y-m0125-f88-eddy.png, y-m045-f64-tdiss.png,
    x-00-f88-eddy.png, y-m0125-f88-vmag.png, x-00-f88-tdiss.png,
    z-00-f24-tke.png, y-m0125-f88-p.png, x-00-f64-t.png, x-1.6-f64-xvel.png,
    x-1.6-f64-tdiss.png, surf_pt_f64_z.png, z-00-f64-pt.png,
    z-00-f64-sij.png, z-035-f64-tke.png, y-m0125-f64-tdiss.png,
    y-045-f64-sij.png, surf_wheel_vmag_f24_down.png, y-045-f88-eddy.png,
    y-m045-f88-vmag.png, surf_wheel_pt_f64_up.png, x-1.6-f64-t.png,
    x-1.6-f88-vmag.png, x-1.6-f88-tdiss.png, z-00-f64-tdiss.png,
    z-035-f64-pt.png, surf_t_f24_z.png, x-1.6-f88-tke.png, z-035-f64-p.png,
    y-m0125-f64-p.png, z-055-f64-pt.png, x-00-f64-eddy.png,
    x-00-f64-tke.png, surf_t_f64_z.png, z-055-f64-tke.png, z-00-f24-sij.png,
    y-045-f64-zvel.png, y-045-f88-pt.png, y-m0125-f64-pt.png,
    z-00-f64-t.png, z-055-f64-sij.png, y-m0125-f88-tdiss.png,
    x-1.6-f88-t.png, y-m0125-f64-vmag.png, y-045-f88-p.png,
    x-1.6-f64-vmag.png, y-m045-f88-xvel.png, y-m0125-f88-t.png,
    surf_pt_f24_y.png, z-055-f64-tdiss.png, y-m045-f88-tdiss.png,
    y-m0125-f88-pt.png, z-00-f64-vmag.png, y-045-f64-tdiss.png,
    surf_wheel_pt_f24_up.png, surf_wheel_xvel_f64_up.png,
    y-045-f64-vmag.png, y-m045-f64-eddy.png, surf_wheel_p_f64_up.png,
    x-00-f64-pt.png, surf_wheel_xvel_f24_up.png, x-00-f64-vmag.png,
    z-055-f64-p.png, y-m0125-f64-eddy.png, y-m0125-f88-tke.png,
    surf_wheel_pt_f24_down.png, x-1.6-f88-xvel.png, y-045-f88-vmag.png,
    y-m045-f08-pt.png, y-045-f88-sij.png, y-045-f88-tdiss.png,
    x-1.6-f64-pt.png, y-045-f64-pt.png, y-045-f64-tke.png, z-055-f64-t.png,
    y-m0125-f64-tke.png, x-1.6-f64-p.png, y-m0125-f64-t.png,
    z-035-f64-xvel.png, y-m045-f64-t.png, y-m045-f88-pt.png,
    x-00-f88-sij.png, y-m045-f64-sij.png, y-m0125-f88-xvel.png,
    y-m045-f64-p.png, x-00-f64-sij.png, y-m0125-f64-xvel.png,
    x-00-f64-p.png, y-045-f64-eddy.png, z-035-f64-t.png, surf_p_f64_z.png,
    x-1.6-f88-pt.png, z-035-f64-vmag.png, y-m0125-f88-sij.png,
    x-00-f64-tdiss.png, y-m045-f88-eddy.png, z-035-f64-sij.png,
    x-00-f88-t.png, y-m045-f64-pt.png, y-m045-f64-xvel.png,
    x-1.6-f64-tke.png, y-045-f64-t.png, surf_t_f64_x.png, x-00-f88-xvel.png,
    y-m045-f88-t.png, y-m045-f88-sij.png, surf_wheel_pt_f64_down.png,
    surf_t_f24_x.png, x-1.6-f64-eddy.png, z-00-f24-tdiss.png,
    y-045-f88-zvel.png, x-1.6-f88-eddy.png, z-055-f64-eddy.png,
    x-00-f88-p.png, z-00-f24-eddy.png, z-055-f64-vmag.png, x-1.6-f88-p.png,
    y-m045-f64-vmag.png, surf_wheel_p_f64_down.png, y-045-f88-tke.png,
    y-m045-f88-p.png, xvel-bottom.lgraph, p-tail.lgraph, xvel-tail.lgraph,
    pt-bottom.lgraph, p-bottom.lgraph, xvel-top.lgraph, pt-tail.lgraph

  basicPhysics/smokeTests/thermalBCs/2d_bump/accelerate_thermal_solver/always-on: FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): static_p.png,
    density.png, total_p.png, xvel.png, temperature.png,
    near_wall_temp_base.lgraph, base_temp_time_history.lgraph,
    near_wall_temp_side.lgraph

  basicPhysics/smokeTests/thermalBCs/2d_bump/accelerate_thermal_solver/on-off-on: FAIL    
    [post_test_cmd] Detected differences in 3 output file(s): static_p.png,
    total_p.png, base_temp_time_history.lgraph

  basicPhysics/smokeTests/thermalBCs/2d_bump/baseline: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): static_p.png,
    total_p.png

  basicPhysics/smokeTests/thermalBCs/water/15degLB: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): p.png

  basicPhysics/smokeTests/thermalBCs/water/15degPDE: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): p.png

  basicPhysics/smokeTests/thermalBCs/water/15degTA: FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): tdiss.png,
    total_p.png, tke.png, xvel.png, p.png, temperature.png,
    temperature.lgraph

  basicPhysics/smokeTests/transonic/3d-dlr      : FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): y-vel_z.png,
    mach_y_0.png, x-vel_y_0.15.png, x-vel_z.png, mach_y_0.15.png,
    mach_z.png, forces.lgraph

  basicPhysics/smokeTests/transonic/sliding_mesh/high_mach: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/transonic/sliding_mesh/low_mach: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      1

    ExecTest.expected_exit_code:
      0

    [main_cmd] [from --keep-comparing] Detected differences in 1 output file(s):
      simulator.o

  basicPhysics/smokeTests/variableCoupling/var_ptherm_coupling/infer_from_model: FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 4 output file(s):
      vmag_03.txt, vmag_02.txt, vmag_04.txt, vmag_01.txt

  basicPhysics/smokeTests/vortSwirlLambda2/2D   : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    sim-snap-all-bfix-fnc-vortz-y027.lgraph

  basicPhysics/smokeTests/vortSwirlLambda2/3D   : FAIL    
    [post_test_cmd] Detected differences in 16 output file(s):
    lambda2_xm04_bfix.png, lambda2_x04_l2.png, xvort_ym014_l2.png,
    lambda2_x04_all.png, lambda2_xm04_l2.png, vortmag_xm04_all.png,
    lambda2_x075_all.png, xvort_ym014_pviz.png, lambda2_ym014_all.png,
    lambda2_x075_l2.png, lambda2_xm04_all.png, lambda2_x075_bfix.png,
    lambda2_ym014_bfix.png, lambda2_x04_bfix.png, vortmag_xm04_bfix.png,
    lambda2_ym014_l2.png

  basicPhysics/synthetic_turbulence/FrequencyModified/LeftAndRightExtended: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/synthetic_turbulence/FrequencyModified/LeftExtension: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/synthetic_turbulence/FrequencyModified/RightExtension: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/synthetic_turbulence/TIModified/TI5Percent: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/synthetic_turbulence/TIModified/TIXYZ: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/synthetic_turbulence/TLSModified/TLS1m: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/thermalBC/DNS_coupled/2d-concentric-rotating-cyls: FAIL    
    [post_test_cmd] Detected differences in 11 output file(s): vmag.png,
    density.png, temp.png, ptot.png, temp_timehist_x0103.lgraph,
    density_timehist_x0103.lgraph, ptot_timehist_xm0195.lgraph,
    density_timehist_xm0195.lgraph, vmag_timehist_x0103.lgraph,
    ptot_timehist_x0103.lgraph, vmag_timehist_xm0195.lgraph

  basicPhysics/thermalBC/DNS_coupled/channel_freeslipwalls: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag.png

  basicPhysics/thermalBC/DNS_coupled/channel_slidingwalls: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      247

    ExecTest.expected_exit_code:
      0

  basicPhysics/thermalBC/DNS_coupled/channel_standardwalls: FAIL    
    [post_test_cmd] Detected differences in 4 output file(s): vmag.png,
    cp.png, xvel_centerline.lgraph, pressure_centerline.lgraph

  basicPhysics/thermalBC/DNS_uncoupled/2d-concentric-rotating-cyls: FAIL    
    [main_cmd] Command timed out.

  basicPhysics/thermalBC/DNS_uncoupled/channel_slidingwalls: FAIL    
    [main_cmd] Unexpected exit_code.

    ExecTest.exit_code:
      247

    ExecTest.expected_exit_code:
      0

  basicPhysics/thermalBC/turb_coupled/channel_freeslipwalls: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    staticpressure.png

  basicPhysics/timeSpaceVarying/inletFlowTime   : FAIL    
    [post_test_cmd] Detected differences in 12 output file(s): tke.4268.png,
    x-vel.3436.png, x-vel.4268.png, temperature.4268.png, p_tot.3436.png,
    temperature.3436.png, sij.3436.png, tke.3436.png, p_tot.4268.png,
    sij.4268.png, p.3436.png, p.4268.png

  basicPhysics/timeSpaceVarying/inletTurbSpace  : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag.png

  basicPhysics/timeSpaceVarying/nanCheck        : FAIL    
    [main_cmd] Command timed out.

  basicPhysics/timeSpaceVarying/outletFlowTime  : FAIL    
    [post_test_cmd] Detected differences in 10 output file(s): tke.4268.png,
    x-vel.3436.png, x-vel.4268.png, p_tot.3436.png, sij.3436.png,
    tke.3436.png, p_tot.4268.png, sij.4268.png, p.3436.png, p.4268.png

  basicPhysics/timeSpaceVarying/tableInlineFanTime: FAIL    
    [post_test_cmd] Detected differences in 10 output file(s):
    p_tot.2200.png, x-vel.1000.png, tke.2200.png, p.1000.png,
    x-vel.2200.png, p_tot.1000.png, sij.2200.png, tke.1000.png, p.2200.png,
    sij.1000.png

  disc/big/pr18530                              : FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 11 output file(s):
      staticPa.png, vmagZ.png, vmag.png, tdiss.png, density.png, densityZ.png,
      strainZ.png, tke.png, totalPa.png, staticPaZ.png, totalPaZ.png

  disc/small/closeSymmPlanes/slidingLRF         : FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 8 output file(s):
      xslice-density.png, surf-density.png, xslice-z-vel.png,
      yslice-velmag.png, yslice-z-vel.png, xslice-tke.png,
      yslice-temperature.png, surf-vmag.png

  disc/small/closeSymmPlanes/staticLRF          : FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 6 output file(s):
      xslice-density.png, xslice-z-vel.png, yslice-velmag.png,
      yslice-z-vel.png, xslice-tke.png, yslice-temperature.png

  disc/small/closeSymmPlanes/symOffsetCube      : FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 6 output file(s):
      xslice-pressure.png, xslice-density.png, yslice-velmag.png,
      xslice-velmag.png, yslice-y-vel.png, yslice-density.png

  disc/small/closeSymmPlanes/symOffsetCyl       : FAIL    
    [main_cmd] Detected differences in 1 output file(s): simulator.o

    [post_test_cmd] [from --keep-comparing] Detected differences in 6 output file(s):
      xslice-density.png, yslice-velmag.png, yslice-x-vel.png,
      xslice-velmag.png, yslice-density.png, xslice-yvel.png

  multiOffset/multiOffset                       : FAIL    
    [post_test_cmd] Detected differences in 18 output file(s): tke1.png,
    pressure0.png, totalPressure.png, tdiss2.png, strain1.png, strain3.png,
    strain2.png, strain.png, vmag.png, tdiss.png, density.png,
    staticPressure.png, tke.png, pressure4.png, vmag4.png, eddy.png,
    tke4.png, tke3.png

  multiOffset/sideOffset                        : FAIL    
    [post_test_cmd] Detected differences in 21 output file(s): vmag3.png,
    density2.png, tke0.png, pressure0.png, tdiss2.png, density3.png,
    pressure1.png, vmag2.png, strain3.png, eddy4.png, pressure3.png,
    strain4.png, tdiss1.png, eddy3.png, pressure4.png, tke2.png, vmag4.png,
    tdiss4.png, tke4.png, density4.png, tke3.png

  multiOffset/topOffset                         : FAIL    
    [post_test_cmd] Detected differences in 4 output file(s): strain.png,
    tdiss.png, tke.png, eddy.png

  prRegressionTests/pr24645                     : FAIL    
    [main_cmd] Detected differences in 1 output file(s): discretizer.o

  slidingMesh/2D/active/active_05v_mlrf         : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag.lgraph

  slidingMesh/2D/activeVR/multiVR_05v_mlrf      : FAIL    
    [post_test_cmd] Detected differences in 11 output file(s): strain.png,
    vmag.png, tdiss.png, tke.png, evisc.png, vmag.lgraph, tke.lgraph,
    evisc.lgraph, temp.lgraph, xvel.lgraph, density.lgraph

  slidingMesh/2D/activeVR/multiVR_05v_slrf      : FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): strain.png,
    tdiss.png, tke.png, evisc.png, vmag.lgraph, tke.lgraph, evisc.lgraph,
    tdiss.lgraph, xvel.lgraph

  slidingMesh/2D/activeVR/multiVR_20v_mlrf      : FAIL    
    [post_test_cmd] Detected differences in 5 output file(s): tke.png,
    evisc.png, vmag.lgraph, evisc.lgraph, xvel.lgraph

  slidingMesh/2D/activeVR/multiVR_20v_slrf      : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): tdiss.png

  slidingMesh/2D/isothermalVR/isothermalVR_05v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): strain.png,
    vmag.png, tdiss.png, tke.png, evisc.png, vmag.lgraph, tke.lgraph,
    evisc.lgraph, xvel.lgraph

  slidingMesh/2D/isothermalVR/isothermalVR_05v_slrf: FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): strain.png,
    tdiss.png, tke.png, evisc.png, tke.lgraph, evisc.lgraph, xvel.lgraph

  slidingMesh/2D/isothermalVR/isothermalVR_20v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): tdiss.png,
    tke.png, evisc.png, vmag.lgraph, tke.lgraph, evisc.lgraph

  slidingMesh/2D/isothermalVR/isothermalVR_20v_slrf: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): tdiss.png

  slidingMesh/2D/passive/passive_05v_mlrf       : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag.lgraph

  slidingMesh/3D/VR_isothermal/VR_isothermal_05v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): fluid-sij.png,
    fluid-eddy.png, fluid-tke.png, surf-vmag.png, fluid-vmag.png,
    surf-vmag.lgraph, surf-vmag-xalign.lgraph, surf-csf-xalign.lgraph,
    surf-cp-xalign.lgraph

  slidingMesh/3D/VR_isothermal/VR_isothermal_05v_slrf: FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, surf-vmag.png, surf-vmag.lgraph,
    fluid-eddy.lgraph, fluid-tke-xalign.lgraph, fluid-eddy-xalign.lgraph

  slidingMesh/3D/VR_isothermal/VR_isothermal_20v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, fluid-tke.png, fluid-vmag.png,
    fluid-tke.lgraph, fluid-eddy.lgraph

  slidingMesh/3D/VR_isothermal/VR_isothermal_20v_slrf: FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, fluid-tke.png, fluid-vmag.png,
    fluid-tke.lgraph, fluid-sij.lgraph, fluid-eddy.lgraph,
    fluid-eddy-xalign.lgraph

  slidingMesh/3D/VR_thermal/VR_thermal_05v_mlrf : FAIL    
    [post_test_cmd] Detected differences in 12 output file(s):
    fluid-sij.png, fluid-eddy.png, fluid-tke.png, surf-vmag.png,
    fluid-vmag.png, surf-htc-xalign.lgraph, surf-vmag.lgraph,
    surf-htc.lgraph, surf-vmag-xalign.lgraph, surf-csf-xalign.lgraph,
    surf-cp-xalign.lgraph, surf-cp.lgraph

  slidingMesh/3D/VR_thermal/VR_thermal_05v_slrf : FAIL    
    [post_test_cmd] Detected differences in 10 output file(s):
    fluid-sij.png, fluid-eddy.png, surf-vmag.png, surf-vmag.lgraph,
    surf-htc.lgraph, fluid-eddy.lgraph, surf-csf.lgraph,
    fluid-tke-xalign.lgraph, surf-cp-xalign.lgraph, fluid-eddy-xalign.lgraph

  slidingMesh/3D/VR_thermal/VR_thermal_20v_mlrf : FAIL    
    [post_test_cmd] Detected differences in 12 output file(s):
    fluid-sij.png, fluid-eddy.png, fluid-tke.png, surf-vmag.png,
    fluid-vmag.png, surf-htc-xalign.lgraph, surf-vmag.lgraph,
    surf-htc.lgraph, surf-vmag-xalign.lgraph, surf-csf-xalign.lgraph,
    surf-cp-xalign.lgraph, surf-cp.lgraph

  slidingMesh/3D/VR_thermal/VR_thermal_20v_slrf : FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): fluid-sij.png,
    fluid-eddy.png, fluid-tke.png, fluid-vmag.png, fluid-tke.lgraph,
    fluid-eddy.lgraph, fluid-tke-xalign.lgraph, fluid-eddy-xalign.lgraph

  slidingMesh/3D/noVR_isothermal/noVR_isothermal_05v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): fluid-sij.png,
    fluid-eddy.png

  slidingMesh/3D/noVR_isothermal/noVR_isothermal_20v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 8 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, fluid-tke.png, fluid-vmag.png,
    surf-htc-xalign.lgraph, fluid-tke.lgraph, fluid-eddy.lgraph

  slidingMesh/3D/noVR_isothermal/noVR_isothermal_20v_slrf: FAIL    
    [post_test_cmd] Detected differences in 9 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, fluid-tke.png, fluid-vmag.png,
    fluid-tke.lgraph, fluid-sij.lgraph, fluid-eddy.lgraph,
    fluid-eddy-xalign.lgraph

  slidingMesh/3D/noVR_thermal/noVR_thermal_05v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 7 output file(s): fluid-sij.png,
    surf-htc.png, fluid-eddy.png, surf-htc-xalign.lgraph, surf-htc.lgraph,
    surf-cp-xalign.lgraph, surf-cp.lgraph

  slidingMesh/3D/noVR_thermal/noVR_thermal_05v_slrf: FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): fluid-sij.png,
    surf-htc.lgraph

  slidingMesh/3D/noVR_thermal/noVR_thermal_20v_mlrf: FAIL    
    [post_test_cmd] Detected differences in 6 output file(s): fluid-sij.png,
    fluid-eddy.png, fluid-tke.png, fluid-tke.lgraph, fluid-sij.lgraph,
    fluid-eddy.lgraph

  slidingMesh/3D/noVR_thermal/noVR_thermal_20v_slrf: FAIL    
    [post_test_cmd] Detected differences in 3 output file(s): fluid-sij.png,
    fluid-eddy.png, fluid-sij.lgraph

  slidingMesh/grfTranslation                    : FAIL    
    [post_test_cmd] Detected differences in 15 output file(s): sFrame27.png,
    fFrame28.png, fFrame19.png, fFrame0.png, fFrame27.png, sFrame0.png,
    sFrame13.png, fFrame29.png, fFrame26.png, fFrame32.png, fFrame16.png,
    sFrame31.png, fFrame30.png, sFrame17.png, sFrame25.png

  slidingMesh/nested_LRF/double_nested_lrf_flow : FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): v_max.txt

  slidingMesh/nested_LRF/grf_nested_lrf_flow    : FAIL    
    [post_test_cmd] Detected differences in 2 output file(s): vmag_grf.png,
    vmag_grf_y.png

  slidingMesh/nested_LRF/nested_align_not_colloc_flow: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s): vmag_grf_y.png

  slidingMesh/nested_LRF/nested_not_align_not_colloc_flow: FAIL    
    [post_test_cmd] Detected differences in 1 output file(s):
    vmag_inner_x.png

--- TEST RESULTS -------------------------------------------------------------

  basicPhysics/autostop/cool/spec_heat_reject_four                                                                 : FAIL     (apoapsis)
  basicPhysics/autostop/therm/exHs_direct_addVr3_StagNo_numSC1_Standard                                            : FAIL     (apoapsis)
  basicPhysics/autostop/therm_cool/therm_cool_mons_stab_conf_user_spec_it                                          : FAIL     (apoapsis)
  basicPhysics/smokeTests/mrf/3dCoupled                                                                            : FAIL     (apoapsis)
  basicPhysics/smokeTests/particle_modelling/two_pmr_files_gauss                                                   : FAIL     (apoapsis)
  basicPhysics/smokeTests/powercool/partition_tests/condenser                                                      : FAIL     (apoapsis)
  basicPhysics/smokeTests/slidingMesh/nested_LRF/grf_nested_lrf_noflow                                             : FAIL     (apoapsis)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/diff_vr                                           : FAIL     (apoapsis)
  basicPhysics/smokeTests/thermalBCs/2d_bump/accelerate_thermal_solver/on-off-on                                   : FAIL     (apoapsis)
  basicPhysics/smokeTests/transonic/3d-dlr                                                                         : FAIL     (apoapsis)
  basicPhysics/synthetic_turbulence/TLSModified/TLS1m                                                              : FAIL     (apoapsis)
  multiOffset/topOffset                                                                                            : FAIL     (apoapsis)
  slidingMesh/2D/activeVR/multiVR_05v_slrf                                                                         : FAIL     (apoapsis)
  slidingMesh/2D/passive/passive_05v_mlrf                                                                          : FAIL     (apoapsis)
  slidingMesh/3D/VR_isothermal/VR_isothermal_20v_mlrf                                                              : FAIL     (apoapsis)
  slidingMesh/3D/noVR_isothermal/noVR_isothermal_05v_mlrf                                                          : FAIL     (apoapsis)
  slidingMesh/3D/noVR_thermal/noVR_thermal_20v_mlrf                                                                : FAIL     (apoapsis)
  basicPhysics/autostop/surface/rect_mesh                                                                          : FAIL     (substrate)
  basicPhysics/autostop/therm_cool/therm_cool_val_conf                                                             : FAIL     (substrate)
  basicPhysics/flowOscillate                                                                                       : FAIL     (substrate)
  basicPhysics/smokeTests/bumps/3D/separated                                                                       : FAIL     (substrate)
  basicPhysics/smokeTests/powercool/partition_tests/radiator                                                       : FAIL     (substrate)
  basicPhysics/smokeTests/rotating_tire_tread/crooked_vr                                                           : FAIL     (substrate)
  basicPhysics/smokeTests/rotating_tire_tread/two_treads_floor_ramped_velocity                                     : FAIL     (substrate)
  basicPhysics/smokeTests/snowconeV3                                                                               : FAIL     (substrate)
  basicPhysics/synthetic_turbulence/FrequencyModified/RightExtension                                               : FAIL     (substrate)
  disc/big/pr18530                                                                                                 : FAIL     (substrate)
  slidingMesh/2D/active/active_05v_mlrf                                                                            : FAIL     (substrate)
  slidingMesh/2D/activeVR/multiVR_05v_mlrf                                                                         : FAIL     (substrate)
  slidingMesh/3D/VR_thermal/VR_thermal_05v_slrf                                                                    : FAIL     (substrate)
  slidingMesh/3D/noVR_thermal/noVR_thermal_20v_slrf                                                                : FAIL     (substrate)
  slidingMesh/nested_LRF/nested_align_not_colloc_flow                                                              : FAIL     (substrate)
  basicPhysics/APM/tortuosity/tort_2.0                                                                             : FAIL     (scarab)
  basicPhysics/autostop/therm/exHs_back                                                                            : FAIL     (scarab)
  basicPhysics/autostop/therm_cool/therm_cool_conf                                                                 : FAIL     (scarab)
  basicPhysics/buoyancy                                                                                            : FAIL     (scarab)
  basicPhysics/smokeTests/exa_signal_events/SetMaxMinTemp                                                          : FAIL     (scarab)
  basicPhysics/smokeTests/rotating_tire_tread/tread_rotating                                                       : FAIL     (scarab)
  basicPhysics/smokeTests/vortSwirlLambda2/2D                                                                      : FAIL     (scarab)
  basicPhysics/synthetic_turbulence/FrequencyModified/LeftExtension                                                : FAIL     (scarab)
  basicPhysics/thermalBC/DNS_uncoupled/2d-concentric-rotating-cyls                                                 : FAIL     (scarab)
  basicPhysics/timeSpaceVarying/nanCheck                                                                           : FAIL     (scarab)
  slidingMesh/2D/activeVR/multiVR_20v_mlrf                                                                         : FAIL     (scarab)
  slidingMesh/3D/VR_thermal/VR_thermal_20v_slrf                                                                    : FAIL     (scarab)
  slidingMesh/nested_LRF/nested_not_align_not_colloc_flow                                                          : FAIL     (scarab)
  basicPhysics/smokeTests/LTT/NACA0012_AOA10_ltt                                                                   : FAIL     (com)
  basicPhysics/smokeTests/defrost/3d_acc_film_devel_w_defrost_time                                                 : FAIL     (com)
  basicPhysics/smokeTests/defrost/coupled_compressible                                                             : FAIL     (com)
  basicPhysics/smokeTests/measTests/checkpointResume/therm_cool_run_past_checkpoint                                : FAIL     (com)
  basicPhysics/smokeTests/particle_modelling/two_pmr_files_beam                                                    : FAIL     (com)
  basicPhysics/smokeTests/slidingMesh/solidXslrf                                                                   : FAIL     (com)
  basicPhysics/smokeTests/thermalBCs/water/15degTA                                                                 : FAIL     (com)
  basicPhysics/synthetic_turbulence/TIModified/TIXYZ                                                               : FAIL     (com)
  basicPhysics/timeSpaceVarying/inletFlowTime                                                                      : FAIL     (com)
  basicPhysics/timeSpaceVarying/outletFlowTime                                                                     : FAIL     (com)
  disc/small/closeSymmPlanes/slidingLRF                                                                            : FAIL     (com)
  multiOffset/multiOffset                                                                                          : FAIL     (com)
  slidingMesh/2D/isothermalVR/isothermalVR_20v_mlrf                                                                : FAIL     (com)
  slidingMesh/3D/VR_isothermal/VR_isothermal_05v_mlrf                                                              : FAIL     (com)
  slidingMesh/3D/VR_thermal/VR_thermal_20v_mlrf                                                                    : FAIL     (com)
  slidingMesh/3D/noVR_thermal/noVR_thermal_05v_slrf                                                                : FAIL     (com)
  slidingMesh/nested_LRF/grf_nested_lrf_flow                                                                       : FAIL     (com)
  basicPhysics/autostop/therm/therm_conf                                                                           : FAIL     (anabatic)
  basicPhysics/smokeTests/autostop/therm/battery_cooling                                                           : FAIL     (anabatic)
  basicPhysics/smokeTests/exa_signal_events/MMECkpt/ScheduledEarly                                                 : FAIL     (anabatic)
  basicPhysics/smokeTests/highSubsonic/3d-dlr-f4                                                                   : FAIL     (anabatic)
  basicPhysics/smokeTests/rotating_tire_tread/tread_rotating_w_floor                                               : FAIL     (anabatic)
  basicPhysics/smokeTests/slidingMesh/noVR_isothermal_05v_slrf                                                     : FAIL     (anabatic)
  basicPhysics/smokeTests/thermalBCs/2d_bump/accelerate_thermal_solver/always-on                                   : FAIL     (anabatic)
  basicPhysics/thermalBC/DNS_coupled/2d-concentric-rotating-cyls                                                   : FAIL     (anabatic)
  basicPhysics/thermalBC/DNS_coupled/channel_freeslipwalls                                                         : FAIL     (anabatic)
  basicPhysics/thermalBC/DNS_coupled/channel_slidingwalls                                                          : FAIL     (anabatic)
  basicPhysics/timeSpaceVarying/tableInlineFanTime                                                                 : FAIL     (anabatic)
  slidingMesh/2D/isothermalVR/isothermalVR_05v_mlrf                                                                : FAIL     (anabatic)
  slidingMesh/2D/isothermalVR/isothermalVR_20v_slrf                                                                : FAIL     (anabatic)
  slidingMesh/3D/VR_isothermal/VR_isothermal_05v_slrf                                                              : FAIL     (anabatic)
  slidingMesh/3D/noVR_isothermal/noVR_isothermal_20v_mlrf                                                          : FAIL     (anabatic)
  basicPhysics/autostop/cool/non_rectangular                                                                       : FAIL     (com)
  basicPhysics/mrf/3dAligned                                                                                       : FAIL     (com)
  basicPhysics/smokeTests/bumps/3D/attached                                                                        : FAIL     (com)
  basicPhysics/smokeTests/forceDev/nested_lrf                                                                      : FAIL     (com)
  basicPhysics/smokeTests/mrf/3dInclined                                                                           : FAIL     (com)
  basicPhysics/smokeTests/rotating_tire_tread/crooked                                                              : FAIL     (com)
  basicPhysics/smokeTests/rotating_tire_tread/sq_cyl_annular                                                       : FAIL     (com)
  basicPhysics/smokeTests/thermalBCs/water/15degLB                                                                 : FAIL     (com)
  basicPhysics/synthetic_turbulence/FrequencyModified/LeftAndRightExtended                                         : FAIL     (com)
  basicPhysics/thermalBC/DNS_coupled/channel_standardwalls                                                         : FAIL     (com)
  basicPhysics/thermalBC/turb_coupled/channel_freeslipwalls                                                        : FAIL     (com)
  basicPhysics/timeSpaceVarying/inletTurbSpace                                                                     : FAIL     (com)
  disc/small/closeSymmPlanes/symOffsetCube                                                                         : FAIL     (com)
  disc/small/closeSymmPlanes/symOffsetCyl                                                                          : FAIL     (com)
  multiOffset/sideOffset                                                                                           : FAIL     (com)
  slidingMesh/2D/activeVR/multiVR_20v_slrf                                                                         : FAIL     (com)
  slidingMesh/3D/VR_isothermal/VR_isothermal_20v_slrf                                                              : FAIL     (com)
  slidingMesh/3D/noVR_thermal/noVR_thermal_05v_mlrf                                                                : FAIL     (com)
  slidingMesh/nested_LRF/double_nested_lrf_flow                                                                    : FAIL     (com)
  basicPhysics/APM/tortuosity/tort_1.5                                                                             : FAIL     (com)
  basicPhysics/autostop/cool/condenser/value                                                                       : FAIL     (com)
  basicPhysics/multiphase/slug_in_sinusoidal_channel                                                               : FAIL     (com)
  basicPhysics/smokeTests/bit-for-bit/4P/thermalpassive_from_isotherm                                              : FAIL     (com)
  basicPhysics/smokeTests/forceDev/slidingMeshPM/3D                                                                : FAIL     (com)
  basicPhysics/smokeTests/measTests/checkpointResume/therm_cool                                                    : FAIL     (com)
  basicPhysics/smokeTests/multiphase/slug_between_plates                                                           : FAIL     (com)
  basicPhysics/smokeTests/rotating_tire_tread/resume_test                                                          : FAIL     (com)
  basicPhysics/smokeTests/rotating_tire_tread/stationary_sq_cyl                                                    : FAIL     (com)
  basicPhysics/smokeTests/thermalBCs/2d_bump/baseline                                                              : FAIL     (com)
  basicPhysics/smokeTests/thermalBCs/water/15degPDE                                                                : FAIL     (com)
  basicPhysics/smokeTests/transonic/sliding_mesh/high_mach                                                         : FAIL     (com)
  basicPhysics/smokeTests/transonic/sliding_mesh/low_mach                                                          : FAIL     (com)
  basicPhysics/smokeTests/variableCoupling/var_ptherm_coupling/infer_from_model                                    : FAIL     (com)
  basicPhysics/smokeTests/vortSwirlLambda2/3D                                                                      : FAIL     (com)
  basicPhysics/synthetic_turbulence/TIModified/TI5Percent                                                          : FAIL     (com)
  basicPhysics/thermalBC/DNS_uncoupled/channel_slidingwalls                                                        : FAIL     (com)
  disc/small/closeSymmPlanes/staticLRF                                                                             : FAIL     (com)
  prRegressionTests/pr24645                                                                                        : FAIL     (com)
  slidingMesh/2D/isothermalVR/isothermalVR_05v_slrf                                                                : FAIL     (com)
  slidingMesh/3D/VR_thermal/VR_thermal_05v_mlrf                                                                    : FAIL     (com)
  slidingMesh/3D/noVR_isothermal/noVR_isothermal_20v_slrf                                                          : FAIL     (com)
  slidingMesh/grfTranslation                                                                                       : FAIL     (com)

  basicPhysics/2dChannels/Re5_0e3-h40                                                                              : PASS     (apoapsis)
  basicPhysics/autostop/cool/condenser/percent                                                                     : PASS     (apoapsis)
  basicPhysics/autostop/schemes/f1_lift                                                                            : PASS     (apoapsis)
  basicPhysics/autostop/surface/two_plates/include_plates                                                          : PASS     (apoapsis)
  basicPhysics/bodyForce/pr17210/bodyForce3D                                                                       : PASS     (apoapsis)
  basicPhysics/smokeTests/2dAirfoils/11                                                                            : PASS     (apoapsis)
  basicPhysics/smokeTests/autostop/classic/cube_no_stop                                                            : PASS     (apoapsis)
  basicPhysics/smokeTests/autostop/classic/cube_stop_0.005_s_char_time_2000                                        : PASS     (apoapsis)
  basicPhysics/smokeTests/autostop/cool/heat_rejected                                                              : PASS     (apoapsis)
  basicPhysics/smokeTests/autostop/probes/surface_probes                                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/autostop/value/y-vel/cube_user_spec_it                                                   : PASS     (apoapsis)
  basicPhysics/smokeTests/bumps/2D/attached                                                                        : PASS     (apoapsis)
  basicPhysics/smokeTests/defrost/3d_no_acc                                                                        : PASS     (apoapsis)
  basicPhysics/smokeTests/highSubsonic/2d-dns-nozzle/pback-9575                                                    : PASS     (apoapsis)
  basicPhysics/smokeTests/lgi_to_fnc                                                                               : PASS     (apoapsis)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flow_inlet/damping                                               : PASS     (apoapsis)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flow_outlet/no_damping                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flux_outlet/no_damping                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/mass_flow_and_flux/true_mass_flow_inlet/single                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/porous_media/curvedpm_var_heat                                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/PressVelDNS                                            : PASS     (apoapsis)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/TurbPressVel                                           : PASS     (apoapsis)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/TurbVelHT                                              : PASS     (apoapsis)
  basicPhysics/smokeTests/thermalBCs/water/0degPDE                                                                 : PASS     (apoapsis)
  basicPhysics/thermalBC/turb3d_coupled                                                                            : PASS     (apoapsis)
  basicPhysics/thermalBC/turb_uncoupled/2d-concentric-rotating-cyls                                                : PASS     (apoapsis)
  disc/big/pr14733                                                                                                 : PASS     (apoapsis)
  slidingMesh/2D/active/active_05v_slrf                                                                            : PASS     (apoapsis)
  slidingMesh/2D/isothermal/isothermal_05v_slrf                                                                    : PASS     (apoapsis)
  slidingMesh/nested_LRF/double_nested_lrf_noflow_ang_acc                                                          : PASS     (apoapsis)
  vor/radialBlankMLRF                                                                                              : PASS     (apoapsis)
  vor/radialBlankSLRF                                                                                              : PASS     (apoapsis)
  vor/radialBlankWithBoreSLRF                                                                                      : PASS     (apoapsis)
  basicPhysics/2dAirfoils/16                                                                                       : PASS     (substrate)
  basicPhysics/autostop/percent/x-vel/simple_stab_window_2500                                                      : PASS     (substrate)
  basicPhysics/autostop/surface/two_plates/include_all                                                             : PASS     (substrate)
  basicPhysics/autostop/surface/two_plates/plate_two                                                               : PASS     (substrate)
  basicPhysics/smokeTests/2dChannels/Re1_0e2-h40                                                                   : PASS     (substrate)
  basicPhysics/smokeTests/2dUbend                                                                                  : PASS     (substrate)
  basicPhysics/smokeTests/LTT/flat_plate_ltt                                                                       : PASS     (substrate)
  basicPhysics/smokeTests/autostop/classic/cube_two_monitors                                                       : PASS     (substrate)
  basicPhysics/smokeTests/autostop/cool/one_mon                                                                    : PASS     (substrate)
  basicPhysics/smokeTests/autostop/probes/cube_one_probe_one_regular                                               : PASS     (substrate)
  basicPhysics/smokeTests/autostop/value/y-vel/conv_on_third_mon_only                                              : PASS     (substrate)
  basicPhysics/smokeTests/frozenMomentum/base_seed                                                                 : PASS     (substrate)
  basicPhysics/smokeTests/highSubsonic/2d-dns-nozzle/pback-92                                                      : PASS     (substrate)
  basicPhysics/smokeTests/incompressibleSolver                                                                     : PASS     (substrate)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flow_outlet/damping                                              : PASS     (substrate)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flux_inlet/no_damping                                            : PASS     (substrate)
  basicPhysics/smokeTests/mass_flow_and_flux/true_mass_flow_inlet/double                                           : PASS     (substrate)
  basicPhysics/smokeTests/mrf/2dMrfTurb                                                                            : PASS     (substrate)
  basicPhysics/smokeTests/non_air_fluid/user_defined                                                               : PASS     (substrate)
  basicPhysics/smokeTests/porous_media/curvedpm_adiabatic                                                          : PASS     (substrate)
  basicPhysics/smokeTests/porous_media/curvedpm_wo_hTransfer                                                       : PASS     (substrate)
  basicPhysics/smokeTests/powercool/cac/singleHX_direct_Ynot_aligned_CAC_Sandwich_NoSuthCorr_with_heat_reject      : PASS     (substrate)
  basicPhysics/smokeTests/powercool/rad/singleHX_turbulence_111not_aligned_RAD_Sandwich_NoSuthCorr_with_entry_temp : PASS     (substrate)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/pt_inlet_fixed_dir                                       : PASS     (substrate)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/mflux_outlet                                            : PASS     (substrate)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/v_outlet                                                : PASS     (substrate)
  basicPhysics/smokeTests/slidingMesh/annular_lrf                                                                  : PASS     (substrate)
  basicPhysics/smokeTests/slidingMesh/nested_LRF/nested_not_align_not_colloc_noflow                                : PASS     (substrate)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/base_timeaccurate                                        : PASS     (substrate)
  basicPhysics/thermalBC/DNS_uncoupled/channel_freeslipwalls                                                       : PASS     (substrate)
  basicPhysics/thermalBC/turb_coupled/channel_selectablewalls                                                      : PASS     (substrate)
  basicPhysics/thermalBC/turb_uncoupled/channel_standardwalls                                                      : PASS     (substrate)
  basicPhysics/timeSpaceVarying/inletFluxTurb                                                                      : PASS     (substrate)
  basicPhysics/timeSpaceVarying/inletVelocityTime                                                                  : PASS     (substrate)
  basicPhysics/timeSpaceVarying/porousFluid                                                                        : PASS     (substrate)
  basicPhysics/timeSpaceVarying/porousVarHeatTime                                                                  : PASS     (substrate)
  prRegressionTests/pr29196/2D                                                                                     : PASS     (substrate)
  slidingMesh/2D/isothermal/isothermal_20v_mlrf                                                                    : PASS     (substrate)
  slidingMesh/2D/passive/passive_20v_mlrf                                                                          : PASS     (substrate)
  vor/radialBlankPtsOnRotAxisNotAlignedSLRF                                                                        : PASS     (substrate)
  vor/radialBlankWithBoreNotAlignSLRF                                                                              : PASS     (substrate)
  basicPhysics/autostop/disable_enable/disable_all                                                                 : PASS     (scarab)
  basicPhysics/autostop/disable_enable/enable_second_monitor                                                       : PASS     (scarab)
  basicPhysics/autostop/surface/two_plates/all_three_monitors                                                      : PASS     (scarab)
  basicPhysics/bodyForce/pr17210/GravityChannel/BodyForce/OnlyTimeVarying                                          : PASS     (scarab)
  basicPhysics/smokeTests/2dAirfoils/2dGLCsurfVR                                                                   : PASS     (scarab)
  basicPhysics/smokeTests/autostop/classic/cube_stop_0.005_s_char_time_1000                                        : PASS     (scarab)
  basicPhysics/smokeTests/autostop/classic/x-vel_stop_later_it                                                     : PASS     (scarab)
  basicPhysics/smokeTests/autostop/percent/pressure/simple_baseline_dont_stop                                      : PASS     (scarab)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_baseline_4per_2stddev                                      : PASS     (scarab)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_stop_on_second_monitor_only                                : PASS     (scarab)
  basicPhysics/smokeTests/autostop/surface/plate_x-force_def_csys                                                  : PASS     (scarab)
  basicPhysics/smokeTests/autostop/value/y-vel/stop_sim_it_plus_duration                                           : PASS     (scarab)
  basicPhysics/smokeTests/bumps/2D/separated                                                                       : PASS     (scarab)
  basicPhysics/smokeTests/exa_signal_events/ThermalAcceleration/TurnOn                                             : PASS     (scarab)
  basicPhysics/smokeTests/frozenMomentum/thermal_timestep_ratio                                                    : PASS     (scarab)
  basicPhysics/smokeTests/highSubsonic/2d-dns-nozzle/pback-89                                                      : PASS     (scarab)
  basicPhysics/smokeTests/htcXRA/isothermal                                                                        : PASS     (scarab)
  basicPhysics/smokeTests/multiphase/droplet                                                                       : PASS     (scarab)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/PressVelDNSHT                                          : PASS     (scarab)
  basicPhysics/smokeTests/synthetic_turbulence/spectrum                                                            : PASS     (scarab)
  basicPhysics/thermalBC/turb_coupled/channel_standardwalls                                                        : PASS     (scarab)
  basicPhysics/timeSpaceVarying/inletFlowTurb                                                                      : PASS     (scarab)
  prRegressionTests/pr29196/3D                                                                                     : PASS     (scarab)
  wind_tunnelVars/aeroTemplate                                                                                     : PASS     (scarab)
  wind_tunnelVars/greenhouseTemplate                                                                               : PASS     (scarab)
  basicPhysics/2dAirfoils/8                                                                                        : PASS     (com)
  basicPhysics/autostop/disable_enable/disable_first_monitor                                                       : PASS     (com)
  basicPhysics/autostop/disable_enable/disable_second_monitor                                                      : PASS     (com)
  basicPhysics/autostop/disable_enable/enable_both                                                                 : PASS     (com)
  basicPhysics/autostop/percent/x-vel/simple_stab_window_5000                                                      : PASS     (com)
  basicPhysics/autostop/schemes/therm_max_t                                                                        : PASS     (com)
  basicPhysics/autostop/value/density/cube_stop_0.005                                                              : PASS     (com)
  basicPhysics/autostop/value/density/cube_two_monitors_no_stop                                                    : PASS     (com)
  basicPhysics/autostop/vehicle_definition/cube_lift_drag                                                          : PASS     (com)
  basicPhysics/autostop/vehicle_definition/forcemag_z-torque_pitch                                                 : PASS     (com)
  basicPhysics/bodyForce/pr17210/GravityChannel/BodyForce/OnlySpatiallyVarying                                     : PASS     (com)
  basicPhysics/bodyForce/pr17210/GravityChannel/Gravity                                                            : PASS     (com)
  basicPhysics/bodyForce/time_space_varying_b_force_by_region                                                      : PASS     (com)
  basicPhysics/smokeTests/APM/low_subsonic/mach_0.0                                                                : PASS     (com)
  basicPhysics/smokeTests/autostop/cool/four_mons                                                                  : PASS     (com)
  basicPhysics/smokeTests/autostop/schemes/f1_lift_short_flow_pass                                                 : PASS     (com)
  basicPhysics/smokeTests/autostop/surface/plate_x-force_other_csys                                                : PASS     (com)
  basicPhysics/smokeTests/autostop/value_percent/ke_conf_ptot_conf                                                 : PASS     (com)
  basicPhysics/smokeTests/autostop/value_percent/xvel_csys_vmag_flowpass                                           : PASS     (com)
  basicPhysics/smokeTests/compression                                                                              : PASS     (com)
  basicPhysics/smokeTests/couette                                                                                  : PASS     (com)
  basicPhysics/smokeTests/defrost/2d_inclined_osc                                                                  : PASS     (com)
  basicPhysics/smokeTests/defrost/2d_sliding                                                                       : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/FullCkpt/ScheduledEarly                                                : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/SetMaxVel                                                              : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/ThermalAcceleration/TurnOff                                            : PASS     (com)
  basicPhysics/smokeTests/frozenMomentum/base_seed_2d_channel                                                      : PASS     (com)
  basicPhysics/smokeTests/highSubsonic/2d-dns-nozzle/pback-8811                                                    : PASS     (com)
  basicPhysics/smokeTests/highSubsonic/2d-dns-nozzle/pback-8859                                                    : PASS     (com)
  basicPhysics/smokeTests/highSubsonic/total_temperature_SPS                                                       : PASS     (com)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flow_inlet/no_damping                                            : PASS     (com)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flux_inlet/damping                                               : PASS     (com)
  basicPhysics/smokeTests/mass_flow_and_flux/mass_flux_outlet/damping                                              : PASS     (com)
  basicPhysics/smokeTests/measTests/averagedMeasurement/autostop/cube_stop_0.005                                   : PASS     (com)
  basicPhysics/smokeTests/measTests/averagedMeasurement/measavg_bysim                                              : PASS     (com)
  basicPhysics/smokeTests/non_air_fluid/ethyl_30                                                                   : PASS     (com)
  basicPhysics/smokeTests/non_air_fluid/fc77                                                                       : PASS     (com)
  basicPhysics/smokeTests/porous_media/3pm_closeSpace_nonAxisAligned                                               : PASS     (com)
  basicPhysics/smokeTests/powercool/cac/singleHX_turbulence_Xnot_aligned_CAC_Sandwich_YesSuthCorr_with_heat_reject : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/basic_seed                                                           : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/error_tests/high_subsonic                                            : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/error_tests/mach_number_set_by_user                                  : PASS     (com)
  basicPhysics/smokeTests/seeding/rotate_seed_velocity                                                             : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/p_inlet_fixed_dir                                        : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/p_v_inlet                                                : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/trans_v_from_turb                                        : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/p_outlet_prescr_dir_fixed                               : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/pt_outlet                                               : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/globalRefFrame                                                               : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/base_test                                                : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/omega_1.0                                         : PASS     (com)
  basicPhysics/smokeTests/thermalBCs/q_channel                                                                     : PASS     (com)
  basicPhysics/smokeTests/transonic/total_temperature_SPS                                                          : PASS     (com)
  basicPhysics/smokeTests/userDefinedSeeding/seed                                                                  : PASS     (com)
  basicPhysics/smokeTests/velocityWallBC/2D                                                                        : PASS     (com)
  basicPhysics/thermalBC/DNS_uncoupled/channel_standardwalls                                                       : PASS     (com)
  basicPhysics/thermalBC/turb_coupled/channel_slidingwalls                                                         : PASS     (com)
  basicPhysics/timeSpaceVarying/generalInlineFanTime                                                               : PASS     (com)
  basicPhysics/timeSpaceVarying/porousTempTime                                                                     : PASS     (com)
  basicPhysics/timeSpaceVarying/porousVarHeatSpace                                                                 : PASS     (com)
  basicPhysics/timeSpaceVarying/wallSliding                                                                        : PASS     (com)
  disc/small/2dOffsetVR                                                                                            : PASS     (com)
  disc/small/excludeGrid/excludeGridVR1                                                                            : PASS     (com)
  disc/small/pr18565                                                                                               : PASS     (com)
  disc/small/pr23705/plates3WithSmallGap                                                                           : PASS     (com)
  multiOffset/errorChecking                                                                                        : PASS     (com)
  slidingMesh/2D/active/active_20v_slrf                                                                            : PASS     (com)
  slidingMesh/2D/isothermal/isothermal_05v_mlrf                                                                    : PASS     (com)
  vor/radialBlankPtsOnRotAxisMLRF                                                                                  : PASS     (com)
  vor/radialBlankWithBoreMLRF                                                                                      : PASS     (com)
  vor/radialBlankWithBoreOverlapVRSLRF                                                                             : PASS     (com)
  basicPhysics/APM/high_subsonic/mach_0.3                                                                          : PASS     (anabatic)
  basicPhysics/autostop/cool/multipass                                                                             : PASS     (anabatic)
  basicPhysics/autostop/percent/x-vel/simple_monoton_subwindow                                                     : PASS     (anabatic)
  basicPhysics/autostop/schemes/auto_drag                                                                          : PASS     (anabatic)
  basicPhysics/autostop/surface/two_plates/exclude_plate_two                                                       : PASS     (anabatic)
  basicPhysics/autostop/surface/two_plates/plate_one                                                               : PASS     (anabatic)
  basicPhysics/autostop/vehicle_definition/cube_front_rear                                                         : PASS     (anabatic)
  basicPhysics/bodyForce/body_force_by_region                                                                      : PASS     (anabatic)
  basicPhysics/bodyForce/pr17210/GravityChannel/BodyForce/SpaceTimeVarying                                         : PASS     (anabatic)
  basicPhysics/smokeTests/2dTurbcyc                                                                                : PASS     (anabatic)
  basicPhysics/smokeTests/APM/curvedAPM                                                                            : PASS     (anabatic)
  basicPhysics/smokeTests/APM/low_subsonic/mach_0.3                                                                : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/classic/cube_stop_0.005                                                         : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/classic/cube_stop_0.005_subwin_6000                                             : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/mixed_monitor_types/surface_fluid_probe                                         : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/percent/density/simple_symm_monitor                                             : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_baseline_2per_1stddev                                      : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_two_monitors                                               : PASS     (anabatic)
  basicPhysics/smokeTests/autostop/surface/comp_by_face                                                            : PASS     (anabatic)
  basicPhysics/smokeTests/defrost/compressible                                                                     : PASS     (anabatic)
  basicPhysics/smokeTests/denso/invalid_encrypted_box                                                              : PASS     (anabatic)
  basicPhysics/smokeTests/exa_signal_events/Exit                                                                   : PASS     (anabatic)
  basicPhysics/smokeTests/forceDev/slidingMeshPM/2D                                                                : PASS     (anabatic)
  basicPhysics/smokeTests/non_air_fluid/ethyl_50                                                                   : PASS     (anabatic)
  basicPhysics/smokeTests/non_air_fluid/water                                                                      : PASS     (anabatic)
  basicPhysics/smokeTests/porous_media/curvedpm_prscrbd_temp                                                       : PASS     (anabatic)
  basicPhysics/smokeTests/powercool/cac/singleHX_turbulence_Xnot_aligned_CAC_Bilinear_YesSuthCorr_with_entry_temp  : PASS     (anabatic)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/stationary                                        : PASS     (anabatic)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/TurbVel                                                : PASS     (anabatic)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/VelDNS                                                 : PASS     (anabatic)
  basicPhysics/smokeTests/thermalBCs/water/0degLB                                                                  : PASS     (anabatic)
  basicPhysics/smokeTests/transonic/back_p_0.3                                                                     : PASS     (anabatic)
  basicPhysics/smokeTests/transonic/back_p_0.73                                                                    : PASS     (anabatic)
  basicPhysics/smokeTests/userDefinedSeeding/bseed_include                                                         : PASS     (anabatic)
  basicPhysics/smokeTests/variableCoupling/var_ptherm_coupling/steady_import_delay                                 : PASS     (anabatic)
  basicPhysics/synthetic_turbulence/TIModified/TI10Percent                                                         : PASS     (anabatic)
  basicPhysics/thermalBC/turb_coupled/2d-concentric-rotating-cyls                                                  : PASS     (anabatic)
  basicPhysics/thermalBC/turb_uncoupled/channel_selectablewalls                                                    : PASS     (anabatic)
  basicPhysics/timeSpaceVarying/inletFluxTime                                                                      : PASS     (anabatic)
  basicPhysics/timeSpaceVarying/porousAdiabaticTurb                                                                : PASS     (anabatic)
  basicPhysics/timeSpaceVarying/porousTurb                                                                         : PASS     (anabatic)
  basicPhysics/timeSpaceVarying/wallStandard                                                                       : PASS     (anabatic)
  disc/big/pr21890                                                                                                 : PASS     (anabatic)
  slidingMesh/compositeMeas/moveLRF                                                                                : PASS     (anabatic)
  slidingMesh/compositeMeas/statLRF                                                                                : PASS     (anabatic)
  slidingMesh/multiRF                                                                                              : PASS     (anabatic)
  vor/radialBlankPtsOnRotAxisSLRF                                                                                  : PASS     (anabatic)
  vor/radialBlankWithBoreOverlapVRMLRF                                                                             : PASS     (anabatic)
  basicPhysics/APM/high_subsonic/mach_0.0                                                                          : PASS     (com)
  basicPhysics/autostop/disable_enable/enable_first_monitor                                                        : PASS     (com)
  basicPhysics/autostop/percent/x-vel/simple_stab_window_4000_sub_2500                                             : PASS     (com)
  basicPhysics/autostop/schemes/condenser_temp_fine                                                                : PASS     (com)
  basicPhysics/autostop/value/density/cube_no_stop_0.001                                                           : PASS     (com)
  basicPhysics/autostop/value/pressure/simple_baseline_dont_stop                                                   : PASS     (com)
  basicPhysics/autostop/vehicle_definition/drag_x-torque_roll                                                      : PASS     (com)
  basicPhysics/autostop/vehicle_definition/drag_yaw_roll                                                           : PASS     (com)
  basicPhysics/bodyForce/pr17210/GravityChannel/BodyForce/ConstantBodyForce                                        : PASS     (com)
  basicPhysics/smokeTests/APM/low_subsonic/mach_0.3_refl_damp                                                      : PASS     (com)
  basicPhysics/smokeTests/autostop/classic/cube_stop_10k                                                           : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/density/2d_baseline                                                     : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/density/simple_baseline                                                 : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/pressure/simple_baseline                                                : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/x-vel/pseudo_monoton_stab_window_1000_sub_500                           : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_stab_window_4000_sub_2000                                  : PASS     (com)
  basicPhysics/smokeTests/autostop/probes/cube_two_probes                                                          : PASS     (com)
  basicPhysics/smokeTests/autostop/value/y-vel/cube_two_monitors                                                   : PASS     (com)
  basicPhysics/smokeTests/autostop/value_percent/q_char_time_conf_pt_deficit_conf                                  : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/MMECkpt/ScheduledLate                                                  : PASS     (com)
  basicPhysics/smokeTests/htcXRA/adiabatic                                                                         : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/p_trans_v_from_turb                                      : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/v_inlet                                                  : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/p_v_outlet                                              : PASS     (com)
  basicPhysics/smokeTests/sim_timing_tests/autostop_greatest_end_meas_time                                         : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/nested_LRF/nested_align_not_colloc_lrf_noflow                                : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/external2                                         : PASS     (com)
  basicPhysics/smokeTests/transonic/back_p_0.92                                                                    : PASS     (com)
  basicPhysics/smokeTests/transonic/total_t_total_p_inflow                                                         : PASS     (com)
  basicPhysics/smokeTests/variableCoupling/var_ptherm_coupling/transient                                           : PASS     (com)
  basicPhysics/thermalBC/turb_uncoupled/channel_freeslipwalls                                                      : PASS     (com)
  basicPhysics/timeSpaceVarying/hxTurb                                                                             : PASS     (com)
  basicPhysics/timeSpaceVarying/outletFluxTime                                                                     : PASS     (com)
  basicPhysics/timeSpaceVarying/porousVarHeat                                                                      : PASS     (com)
  disc/small/excludeGrid/excludeGridVR2                                                                            : PASS     (com)
  disc/small/pr18603                                                                                               : PASS     (com)
  disc/small/pr23705/plates2WithSmallGap                                                                           : PASS     (com)
  disc/small/shareEdge/noVR                                                                                        : PASS     (com)
  slidingMesh/2D/active/active_20v_mlrf                                                                            : PASS     (com)
  slidingMesh/2D/isothermal/isothermal_20v_slrf                                                                    : PASS     (com)
  slidingMesh/2D/passive/passive_05v_slrf                                                                          : PASS     (com)
  slidingMesh/varVel                                                                                               : PASS     (com)
  vor/radialBlankPtsOnRotAxisNotAlignedMLRF                                                                        : PASS     (com)
  vor/radialBlankWithBoreNotAlignMLRF                                                                              : PASS     (com)
  basicPhysics/autostop/cool/upstream_no_heat_reject                                                               : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/density/simple_two_monitors                                             : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/x-vel/pseudo_monoton_stab_window_1000                                   : PASS     (com)
  basicPhysics/smokeTests/autostop/percent/x-vel/simple_monoton                                                    : PASS     (com)
  basicPhysics/smokeTests/autostop/porous/x-force                                                                  : PASS     (com)
  basicPhysics/smokeTests/autostop/surface/mass_flux_by_face                                                       : PASS     (com)
  basicPhysics/smokeTests/autostop/value/y-vel/chained_meas                                                        : PASS     (com)
  basicPhysics/smokeTests/denso/encrypted_box                                                                      : PASS     (com)
  basicPhysics/smokeTests/denso/original_box                                                                       : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/FullCkpt/ScheduledLate                                                 : PASS     (com)
  basicPhysics/smokeTests/exa_signal_events/TableRead                                                              : PASS     (com)
  basicPhysics/smokeTests/mbcParticle                                                                              : PASS     (com)
  basicPhysics/smokeTests/measTests/averagedMeasurement/measavg                                                    : PASS     (com)
  basicPhysics/smokeTests/nu_over_t                                                                                : PASS     (com)
  basicPhysics/smokeTests/porous_media/heat_exchanger_with_holes                                                   : PASS     (com)
  basicPhysics/smokeTests/probes                                                                                   : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/error_tests/diff_char_density_scaling                                : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/error_tests/diff_char_temp                                           : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/error_tests/max_expected_vel                                         : PASS     (com)
  basicPhysics/smokeTests/seeding/mks_seeding/mks_seed                                                             : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/mflux_inlet                                              : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/p_inlet_sampled_dir                                      : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/pt_inlet_sampled_dir                                     : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/p_outlet_free                                           : PASS     (com)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/p_outlet_prescr_dir_sampled                             : PASS     (com)
  basicPhysics/smokeTests/sim_timing_tests/greatest_end_meas_time                                                  : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/nested_LRF/double_nested_lrf_noflow                                          : PASS     (com)
  basicPhysics/smokeTests/slidingMesh/rotational_dynamics/noflow/external_torque                                   : PASS     (com)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/TurbPressVelHT                                         : PASS     (com)
  basicPhysics/smokeTests/synthetic_turbulence/differentBCs/VelDNSHT                                               : PASS     (com)
  basicPhysics/smokeTests/thermalBCs/t_channel                                                                     : PASS     (com)
  basicPhysics/smokeTests/userDefinedSeeding/bseed_exclude                                                         : PASS     (com)
  basicPhysics/smokeTests/userDefinedSeeding/seed_exclude_region                                                   : PASS     (com)
  basicPhysics/thermalBC/turb_uncoupled/channel_slidingwalls                                                       : PASS     (com)
  basicPhysics/timeSpaceVarying/inletStaticPressureTime                                                            : PASS     (com)
  basicPhysics/timeSpaceVarying/outletPressureSpace                                                                : PASS     (com)
  basicPhysics/timeSpaceVarying/porousTempSpace                                                                    : PASS     (com)
  basicPhysics/timeSpaceVarying/temperatureTurbSpace                                                               : PASS     (com)
  basicPhysics/timeSpaceVarying/wallRotating                                                                       : PASS     (com)
  disc/small/offsetVR_do_not_visualize                                                                             : PASS     (com)
  disc/small/overlappedBoxes                                                                                       : PASS     (com)
  disc/small/pr14733                                                                                               : PASS     (com)
  disc/small/pr22037                                                                                               : PASS     (com)
  disc/small/pr6341/test5                                                                                          : PASS     (com)
  disc/small/shareEdge/4VRs                                                                                        : PASS     (com)
  slidingMesh/2D/passive/passive_20v_slrf                                                                          : PASS     (com)

  bit-for-bit                                                                                                      : UNTESTED (apoapsis)
  basicPhysics/smokeTests/seeding/seed_from_sample/outlets/input                                                   : UNTESTED (substrate)
  basicPhysics/timeSpaceVarying/bogus/outletStaticPressureDirSpace                                                 : UNTESTED (com)
  basicPhysics/timeSpaceVarying/bogus/outletStaticPressureDirTime                                                  : UNTESTED (com)
  basicPhysics/timeSpaceVarying/tableInlineFanSpace                                                                : UNTESTED (anabatic)
  basicPhysics/smokeTests/seeding/seed_from_sample/inlets/input                                                    : UNTESTED (com)
