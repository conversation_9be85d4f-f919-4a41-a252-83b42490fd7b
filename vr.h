/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

/*--------------------------------------------------------------------------*
 * Variable Resolution 
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_VR_H
#define _SIMENG_VR_H
#include "common_sp.h"
#include "group.h"
#include "fset.h"
#include "ublk.h"
#include "sim.h"
#include "advect.h"
#include "shob_groups.h"
/*--------------------------------------------------------------------------*
 * VR_CONFIG
 *--------------------------------------------------------------------------*/

using cVR_STATES = tBITSET<N_MOVING_STATES>;

#if !DEVICE_COMPILATION_MODE
#define ccDO_VR_STATES(state, vr_states) \
  cVR_STATES ___(m) = (vr_states); \
  for(int state = ___(m).find_first_set(); state != -1; ___(m).reset(state), state = ___(m).find_first_set())

#else
#define ccDO_VR_STATES(state, vr_states) \
  cVR_STATES ___(m) = (vr_states); \
  for(int state = __ffs(___(m).get())-1; state != -1; ___(m).reset(state), state = __ffs(___(m).get())-1)

#endif

/* There is one instance of a VR_CONFIG structure for each actual
 * geometric configuration of coarse block lying at the edge of a VR 
 * interface.  
*/
typedef struct sVR_CONFIG {
  /* FINE_MASK describes the geometry of the VR interface around the coarse
   * interface ublk. A 1 indicates that there are abutting fine ublks for
   * that state. */
  STP_LATVEC_MASK fine_mask;
  /* This describes the states that take part in the explode operation */
  cVR_STATES           explode_states[N_VOXELS_8]; 
  /* This describes the states that take part in the coalesce operation */
  cVR_STATES           coalesce_states[N_VOXELS_8]; // N_VOXELS, but it can't change for GPU code

  __HOST__ explicit sVR_CONFIG(STP_LATVEC_MASK fine_mask_) : fine_mask(fine_mask_) 
  {
    ccDOTIMES(voxel, N_VOXELS_8) {
      explode_states[voxel] = cVR_STATES();
      coalesce_states[voxel] = cVR_STATES();
    }
  }

  __HOST__ bool operator == (const sVR_CONFIG& other)
  {
    return fine_mask == other.fine_mask;
  }

} *VR_CONFIG;

const std::deque<sVR_CONFIG>& get_vr_configs();
size_t get_vr_config_idx(const sVR_CONFIG& config);


/*--------------------------------------------------------------------------*
 * VR_INTERFACE_DATA
 *--------------------------------------------------------------------------*/
template<size_t N_VOXELS>
class tVR_INTERFACE_DATA 
{
public:
  enum {
    N_CONFIGS = N_VOXELS/N_VOXELS_8 
  };

  sVR_CONFIG* m_vr_config[N_CONFIGS]; 

  tVR_INTERFACE_DATA()
  {
    ccDOTIMES(i,N_CONFIGS) {
      m_vr_config[i] = nullptr;
    }
  }

  tVR_INTERFACE_DATA (VR_CONFIG vr_config)
  {
    m_vr_config[0] = vr_config;
  }

  sVR_CONFIG* vr_config()
  {
   return m_vr_config[0];
  }

  sVR_CONFIG** all_vr_configs()
  {
    return &m_vr_config[0];
  }

  __HOST__DEVICE__ 
    sVR_CONFIG* vr_config(int child_ublk)
  {
    return m_vr_config[child_ublk];
  }

};

template<size_t N_VOXELS> struct sVR_UBLK_PTR_TRAITS;

template <> struct sVR_UBLK_PTR_TRAITS<N_VOXELS_8>
{
  __HOST__DEVICE__ static void * get_ptr(void * ptr) { return ptr; }
  static void * set_ptr(void * ptr) { return ptr; }
  __HOST__DEVICE__ static uint8_t get_offset(void * ptr) { return 0; }
  static void * set_offset(void * ptr, uintptr_t offset) { return ptr; }
};

#if BUILD_GPU
template <> struct sVR_UBLK_PTR_TRAITS<N_VOXELS_64>
{
  static constexpr int reserved_bits = 3;
  static constexpr uintptr_t offset_mask = (uintptr_t) (((uintptr_t) 1 << reserved_bits)-1);
  static constexpr uintptr_t ptr_mask = (uintptr_t) ~offset_mask;

  __HOST__DEVICE__
  static void * get_ptr(void * ptr) { return scalar_mask_pointer(ptr, ptr_mask); }
  __HOST__DEVICE__
  static uint8_t get_offset(void * ptr) { return (uintptr_t) scalar_mask_pointer(ptr, offset_mask); }
  static void * set_offset(void * ptr, uintptr_t offset) { return scalar_or_pointer(ptr, (uintptr_t) (offset & offset_mask)); }
};
#endif

template<typename sUBLK_TYPE>
class tVR_UBLK_PTR
{
public:

private:
  using sTRAITS = sVR_UBLK_PTR_TRAITS<sUBLK_TYPE::N_VOXELS>;
  sUBLK_TYPE * m_ublk;
public:

  __HOST__DEVICE__
  tVR_UBLK_PTR() : m_ublk(nullptr) {}

  __HOST__DEVICE__
  explicit tVR_UBLK_PTR(sUBLK_TYPE * ptr) : m_ublk(ptr) {}

  __HOST__DEVICE__
  tVR_UBLK_PTR& operator = (std::nullptr_t) {
    m_ublk = nullptr;
    return *this;
  }

  __HOST__DEVICE__
  sUBLK_TYPE * ublk()
  {
    return (sUBLK_TYPE*) sTRAITS::get_ptr(m_ublk);
  }

  __HOST__DEVICE__
  const sUBLK_TYPE * ublk() const
  {
    return (sUBLK_TYPE*) sTRAITS::get_ptr(m_ublk);
  }

  __HOST__DEVICE__
  operator bool () const
  {
    return (bool) ublk();
  }

  __HOST__DEVICE__
  operator bool ()
  {
    return (bool) ublk();
  }

  void set_ublk(sUBLK_TYPE* ublk)
  {
    m_ublk = (sUBLK_TYPE*) sTRAITS::set_ptr(ublk);
  }

  __HOST__DEVICE__
  uint8_t offset()
  {
#if BUILD_GPU
    return sTRAITS::get_offset(m_ublk);
#else
    return 0;
#endif
  }

#if BUILD_GPU
  void set_mblk(sUBLK_TYPE *ptr, uint8_t offset)
  {
    m_ublk = (sUBLK_TYPE*) sTRAITS::set_offset(ptr, offset);
  }
#endif
};

template<typename UBLK_TYPE_TAG>
class tVR_COARSE_INTERFACE_DATA: public tVR_INTERFACE_DATA<UBLK_TYPE_TAG::N_VOXELS> 
{
public:
  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using VOXEL_MASK = typename sUBLK_TYPE::VOXEL_MASK;
  using sVR_UBLK_PTR = tVR_UBLK_PTR<sUBLK_TYPE>;
  using VR_FINE_UBLK_ARRAY = sVR_UBLK_PTR [UBLK_TYPE_TAG::N_VOXELS];
private:
  VR_FINE_UBLK_ARRAY m_vr_fine_ublks;
#if BUILD_GPU  
  tBITSET<UBLK_TYPE_TAG::N_VOXELS> m_explode_on_demand;
#endif  
public:
  __HOST__DEVICE__
  sVR_UBLK_PTR vr_fine_ublk(asINT32 voxel) 
  {
    return m_vr_fine_ublks[voxel];
  }

 /* tVR_COARSE_INTERFACE_DATA(VR_CONFIG vr_config) : tVR_INTERFACE_DATA<sUBLK_TYPE::N_VOXELS>(vr_config){
    ccDOTIMES(i, UBLK_TYPE_TAG::N_VOXELS)
      m_vr_fine_ublks[i] = nullptr;
  }*/
  
  VOID init() {
    ccDOTIMES(i, UBLK_TYPE_TAG::N_VOXELS) {
      m_vr_fine_ublks[i] = nullptr;
    }
#if BUILD_GPU    
    m_explode_on_demand.reset_all();
#endif    
  }

  VOID init(sVR_CONFIG* vr_config) {
    this->m_vr_config[0] = vr_config;
    init();
  }

#if BUILD_GPU
  VOID set_explode_on_demand(int child_ublk) {
    //m_explode_on_demand |= (VOXEL_MASK(0x255) << (N_VOXELS_8 * child_ublk));
    m_explode_on_demand |= (VOXEL_MASK(0b11111111) << (N_VOXELS_8 * child_ublk));
  }

  __HOST__DEVICE__ VOXEL_MASK explode_on_demand_mask() const {
    return m_explode_on_demand;
  }
  
  VOID init(sVR_CONFIG* vr_config, int child_ublk_idx) {
    this->m_vr_config[child_ublk_idx] = vr_config;
  }

  VOID set_vr_fine_ublk(sUBLK_TYPE * fine_mblk, uint8_t fine_mblk_offset, int fine_ublk_idx)
  {
    this->m_vr_fine_ublks[fine_ublk_idx].set_mblk(fine_mblk, fine_mblk_offset);
  }

  sVR_UBLK_PTR* all_vr_fine_ublks() {
    return m_vr_fine_ublks;
  }

#endif

  VOID add_vr_fine_ublk(UBLK vrblk, asINT32 vr_fine_ublk_index, sINT8 n_dims, UBLK coarse_ublk,
                        VOXEL_MASK_8 fluid_voxel_mask) {
    asINT32 coarse_voxel = vr_fine_ublk_index;
    vrblk->fluid_like_voxel_mask = coarse_ublk->fluid_like_voxel_mask.test(coarse_voxel) ? VOXEL_MASK_8{0xFF} : VOXEL_MASK_8{0x00};
    vrblk->basic_fluid_voxel_mask = fluid_voxel_mask;
    if (m_vr_fine_ublks[vr_fine_ublk_index]) {
      msg_internal_error("Multiple VR fine ublks (IDs %d and %d) added to bulk coarse VR interface ublk %d"
                         " at index %d",
                         m_vr_fine_ublks[vr_fine_ublk_index].ublk()->id(), vrblk->id(),
                         coarse_ublk->id(), vr_fine_ublk_index);
    }

    m_vr_fine_ublks[vr_fine_ublk_index].set_ublk(vrblk);
  }

  VOID propagate_smart_seed_mask_to_vr_fine_ublks(sUBLK_TYPE* coarse_ublk) {
    asINT32 num_vr_fine = sUBLK_TYPE::N_VOXELS;
    static_assert( sUBLK_TYPE::N_VOXELS == N_VOXELS_8, "not for mblks");
    ccDOTIMES(i, num_vr_fine) {
      if (m_vr_fine_ublks[i]) {
        asINT32 coarse_voxel = i;
        m_vr_fine_ublks[i].ublk()->voxel_smart_seed_mask = coarse_ublk->voxel_smart_seed_mask.test(coarse_voxel) ? VOXEL_MASK_8{0xFF} : VOXEL_MASK_8{0x00};
      }
    }
  }
};

template<typename UBLK_TYPE_TAG>
class tVR_FINE_INTERFACE_DATA: public tVR_INTERFACE_DATA<UBLK_TYPE_TAG::N_VOXELS>
{
  EXTRACT_UBLK_TRAITS
  using sUBLK_TYPE = tUBLK<UBLK_TYPE_TAG>;
  using sVR_UBLK_PTR = tVR_UBLK_PTR<sUBLK_TYPE>;

private:
  sVR_UBLK_PTR m_vr_coarse_ublk[N_UBLKS];
public:
  // signifies whether post advect scale factor
  // be set to 1 on even time steps during voxel advection.
  typename sUBLK_TYPE::VOXEL_MASK m_even_s2v_weights_mask[N_CONNECT_MASK_BITS];

  tVR_FINE_INTERFACE_DATA(VR_CONFIG vr_config) : tVR_INTERFACE_DATA<sUBLK_TYPE::N_VOXELS>(vr_config)
  {
    m_vr_coarse_ublk[0] = nullptr;
    ccDOTIMES(i, N_CONNECT_MASK_BITS) {
      m_even_s2v_weights_mask[i].reset_all();
    }
  }

  tVR_FINE_INTERFACE_DATA() {
    ccDOTIMES(i, N_UBLKS) {
      m_vr_coarse_ublk[i] = nullptr;
    }
    ccDOTIMES(i, N_CONNECT_MASK_BITS) {
      m_even_s2v_weights_mask[i].reset_all();
    }    
  };
  
  sVR_UBLK_PTR vr_coarse_ublk(int child_ublk_idx) 
  {
    return m_vr_coarse_ublk[child_ublk_idx];
  }

  sVR_UBLK_PTR vr_coarse_ublk() 
  {
    static_assert( sUBLK_TYPE::N_VOXELS == 8, "Cannot be used with a mblk" );
    return vr_coarse_ublk(0);
  }

  VOID init (sUBLK_TYPE* coarse_ublk) 
  {
    this->m_vr_config[0] = coarse_ublk->vr_data()->vr_config();
    m_vr_coarse_ublk[0] = sVR_UBLK_PTR(coarse_ublk);
  }

#if BUILD_GPU
  VOID init (sUBLK_TYPE * coarse_ublk, uint8_t coarse_mblk_offset, int coarse_ublk_idx )
  {
    this->m_vr_config[coarse_ublk_idx] = coarse_ublk->vr_data()->vr_config();
    m_vr_coarse_ublk[coarse_ublk_idx].set_mblk(coarse_ublk, coarse_mblk_offset);
  }

  sVR_UBLK_PTR* all_vr_coarse_ublks() { return m_vr_coarse_ublk; }
#endif

};

typedef tVR_INTERFACE_DATA<N_VOXELS_8> sVR_INTERFACE_DATA;
typedef tVR_FINE_INTERFACE_DATA<UBLK_SDFLOAT_TYPE_TAG> sVR_FINE_INTERFACE_DATA;
typedef tVR_COARSE_INTERFACE_DATA<UBLK_SDFLOAT_TYPE_TAG> sVR_COARSE_INTERFACE_DATA;

/*--------------------------------------------------------------------------*
 * Initialization
 *--------------------------------------------------------------------------*/
VR_CONFIG find_vr_config(STP_LATVEC_MASK fine_mask);
VOID assign_scale_interface_and_vr_topology();

/*--------------------------------------------------------------------------*
 * Operations
 *--------------------------------------------------------------------------*/
template<typename UBLK_TYPE>
__DEVICE__
static bool vr_fine_ublk_is_invalid(const UBLK_TYPE* fine_ublk, sINT8 child_ublk_idx) {
  return (fine_ublk == NULL) || (fine_ublk->is_solid(child_ublk_idx));
}

VOID do_dynblk_explode_init(SCALE coarse_scale);

asINT32 vr_fine_ublk_index_within_coarse_parent(UBLK vr_fine_ublk, UBLK coarse_ublk,
                                                bool report_error = true);
VOID reset_post_advect_scale_factors_for_diagonal_coalesced_states(UBLK ublk, STP_GEOM_VARIABLE (*pas_factors)[ubFLOAT::N_VOXELS]);


template<typename UBLK_TYPE>
__DEVICE__
VOID execute_explode_rule_init(UBLK_TYPE* coarse_ublk, SOLVER_INDEX_MASK seed_solver_index_mask);

VOID execute_explode_mme(UBLK coarse_ublk, SOLVER_INDEX_MASK curr_solver_index_mask,
                         ACTIVE_SOLVER_MASK solver_mask);

template <BOOLEAN is_T_lb_solver_on, BOOLEAN is_UDS_lb_solver_on>
VOID execute_explode_and_mirror_rule(UBLK coarse_ublk, SOLVER_INDEX_MASK prior_solver_index_mask,
                                     ACTIVE_SOLVER_MASK solver_mask);

VOID explode_conduction_solid_temp(UBLK coarse_ublk, const asINT32 coarse_voxel,
                                   UBLK vrfine_ublk, const asINT32 prior_coarse_conduction_index);

VOID maybe_explode_conduction_voxel(UBLK coarse_ublk, const asINT32 coarse_voxel,
                                    UBLK vrfine_ublk, const ACTIVE_SOLVER_MASK active_solver_mask,
                                    const SOLVER_INDEX_MASK prior_coarse_solver_index_mask);

template <BOOLEAN ADVECT_TEMP, BOOLEAN ADVECT_UDS>
INLINE
VOID explode_VR_coarse_on_demand(UBLK src_vrblk, BOOLEAN do_explode,
                                 SOLVER_INDEX_MASK prior_solver_index_mask,
                                 ACTIVE_SOLVER_MASK active_solver_mask) {
  if (do_explode && src_vrblk->is_vr_fine()
      && !src_vrblk->is_mirror() && !src_vrblk->is_exploded()
      && src_vrblk->is_timestep_even()
      && active_solver_mask != 0
      ) {
    sVR_FINE_INTERFACE_DATA *vr_fine_data = src_vrblk->vr_fine_data();
    UBLK coarse_ublk = vr_fine_data->vr_coarse_ublk().ublk();
    // explode and mirrored to mirror ublks
    execute_explode_and_mirror_rule<ADVECT_TEMP, ADVECT_UDS>(coarse_ublk, prior_solver_index_mask, active_solver_mask);

#if ENABLE_CONSISTENCY_CHECKS
    if (!src_vrblk->is_exploded()) {
      sVR_COARSE_INTERFACE_DATA *vr_coarse_data = coarse_ublk->vr_coarse_data();
      // solid coarse voxels are not exploded
      ccDOTIMES(coarse_voxel, ubFLOAT::N_VOXELS) {
        if (!coarse_ublk->fluid_like_voxel_mask.test(coarse_voxel)) {
          continue;
        }

        if (src_vrblk == vr_coarse_data->vr_fine_ublk(coarse_voxel).ublk()) {
          // CONDUCTION-CHECK: Added additional checks below - conduction UBLKs only
          // need to explode if conduction is active; fluid UBLKs only need to
          // explode if fluid solvers are active (we only check LB for now since
          // we are expecting all fluid solvers to go together)
          ACTIVE_SOLVER_MASK last_active_solver_mask =  g_timescale.m_last_active_solver_masks[coarse_ublk->scale()];
          if (src_vrblk->is_conduction_solid() && (last_active_solver_mask & CONDUCTION_PDE_ACTIVE)) {
            msg_print("Failed to set is_exploded for conduction vrblk %d", src_vrblk->id());
            msg_print("failed to set is_exploded for vrblk %d coarse_ublk %d coarse voxel %d", src_vrblk->id(), coarse_ublk->id(),coarse_voxel );
          } else if (!src_vrblk->is_conduction_solid()) {
            ACTIVE_SOLVER_MASK used_active_solver_mask = active_solver_mask | last_active_solver_mask;
            if ((used_active_solver_mask & LB_ACTIVE)
                /*|| (used_active_solver_mask & KE_PDE_ACTIVE)*/)
              msg_print("Failed to set is_exploded for fluid vrblk %d", src_vrblk->id() );
              msg_print("failed to set is_exploded for vrblk %d coarse_ublk %d coarse voxel %d", src_vrblk->id(), coarse_ublk->id(),coarse_voxel );
          }
        }
      }
    }
#endif
  }
}

template <BOOLEAN DO_EXPLODE>
VOID advect_vr_fine_ublks_internal(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask,
                                   SOLVER_INDEX_MASK prior_solver_index_mask,
                                   UBLK_GROUP_TYPE group_type,
                                   BOOLEAN is_timestep_even);

VOID gather_fine_neighbor_info_on_vr_fine_ublks(SCALE scale, ACTIVE_SOLVER_MASK active_solver_mask,
                                                SOLVER_INDEX_MASK prior_solver_index_mask,
                                                UBLK_GROUP_TYPE group_type);

//CONDUCTION-TODO: We should be passing the UBLKs and not locations to be generic. But this leads to simsizes error.
//So this is a temporary fix
inline asINT32 vr_fine_ublk_to_coarse_voxel(const asINT32 vr_fine_location[3],
                                     const asINT32 vr_coarse_location[3],
                                     const asINT32 vr_coarse_voxel_size) {
  asINT32 coarse_voxel = 0;

  ccDOTIMES (axis, 3) {
    if (((vr_fine_location[axis] - vr_coarse_location[axis]) / vr_coarse_voxel_size) == 1)
      coarse_voxel += (1 << (2 - axis));
  }
  return coarse_voxel;
}

#endif /* _SIMENG_VR_H */
