#include <stdio.h>
#include "implicit_shell_solver.h"
#include "sim.h"
#include "particle_sim.h"

#if !GPU_COMPILER && !BUILD_GPU
inline namespace SIMULATOR_NAMESPACE {
Mat g_shell_conduction_coeff_mat;
Vec g_shell_conduction_rhs_vec;
Vec g_shell_conduction_rhs_bc_vec;
Vec g_shell_conduction_solution_vec;
Vec g_shell_conduction_rho_cp_vec;
Vec g_shell_conduction_source_vec;
Vec g_shell_conduction_rhs_bc_temp_vec;
KSP g_ksp;
bool g_implicit_shell_solver_any_cell_bad;
}

bool g_implicit_solver_active = false;
pthread_mutex_t g_implicit_solver_mutex;
pthread_cond_t g_implicit_solver_cond;

bool g_implicit_solver_setup_active = false;
bool g_implicit_solver_setup_complete = false;
pthread_mutex_t g_implicit_solver_setup_mutex;
pthread_cond_t g_implicit_solver_setup_cond;

static inline PetscInt implicit_solver_state_index(SURFEL surfel) {
  if (surfel->is_even() && surfel->clone_surfel()->is_conduction_shell()){
    return surfel->clone_surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
  } 
  else { //checked that surfel is conduction shell when call this method, no need to do it again
    return surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
  } 
}

static inline SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA implicit_solver_data(SURFEL surfel) {
  if (surfel->is_even() && surfel->clone_surfel()->is_conduction_shell()){
    return surfel->clone_surfel()->shell_conduction_implicit_solver_data();
  } 
  else { //checked that surfel is conduction shell when call this method, no need to do it again
    return surfel->shell_conduction_implicit_solver_data();
  } 
}

static inline SURFEL_SHELL_CONDUCTION_DATA surfel_shell_conduction_data(SURFEL surfel) {
  if(surfel->is_even() && surfel->clone_surfel()->is_conduction_shell()){
      return surfel->clone_surfel()->shell_conduction_data();
  } 
  else { //checked that surfel is conduction shell when call this method, no need to do it again
    return surfel->shell_conduction_data();
  } 
}


// Happening in the comm thread now
VOID setup_implicit_shell_solver() {

  if (!g_implicit_solver_setup_complete) {
    pthread_mutex_lock(&g_implicit_solver_setup_mutex);

    // Set up the mapping array for the matrix indices 
    int num_states = set_state_indices_on_sps();

    if (g_shell_implicit_solver_verbosity > 3) {
      std::cout << "SP" << my_proc_id;
      std::cout << ", num_states: " << num_states << std::endl;
    }

    // Get the contributions of the gradients to the matrix
    assemble_surfel_gradient_coefficients();

    // Set up the linear system
    create_linear_system(num_states);

    bool save_system_as_txt = false;
    if (g_shell_implicit_solver_verbosity > 3) {
      save_system_as_txt = true;
    }

    if (save_system_as_txt) {
      write_linear_system_to_txt();
    }

    g_implicit_solver_setup_active = false;
    g_implicit_solver_setup_complete = true;
    pthread_cond_signal(&g_implicit_solver_setup_cond);
    pthread_mutex_unlock(&g_implicit_solver_setup_mutex);
  }
}

int set_state_indices_on_sps() {
  int num_states = 0;
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      num_states += set_state_indices_on_sps_internal(surfel);
    }
    
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        num_states += set_state_indices_on_sps_internal(surfel);
      }
    }

    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      //Skipping assembly for these guys for now
    }

    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      num_states += set_state_indices_on_sps_internal(wsurfel_conduction);
    }

    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      num_states += set_state_indices_on_sps_internal(contact_surfel);
    }
  }
  return num_states;
}

int set_state_indices_on_sps_internal(SURFEL surfel) {
  if (!surfel->is_conduction_shell()) {
    return 0;
  }
  if (surfel->is_even() || surfel->is_mirror()) {
    return 0;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    return 0;
  }

  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();
  shell_implicit_solver_data->implicit_shell_state_index = shell_implicit_solver_data->implicit_shell_state_index + sim.implicit_shell_solver_state_index_offset[my_proc_id];

  return surfel->shell_conduction_data()->num_layers();
}

VOID assemble_surfel_gradient_coefficients() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      assemble_gradient_coefficients(surfel);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        assemble_gradient_coefficients(surfel);
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      //Skipping assembly for these guys for now
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      assemble_gradient_coefficients(wsurfel_conduction);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      assemble_gradient_coefficients(contact_surfel);
    }
    DO_SURFEL_RECV_GROUPS_OF_SCALE(surfel_recv_group, scale) {
      for (const auto& quantum : surfel_recv_group->quantums()) {
        SURFEL surfel = quantum.m_surfel;
        assemble_gradient_coefficients(surfel);
      }
    }
  }
}

VOID assemble_gradient_coefficients(SURFEL surfel) {
  if (!surfel->is_conduction_shell()) {
    return;
  }
  if (surfel->is_even() || surfel->is_mirror()) {
    return;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    return;
  }

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  shell_conduction_data->assemble_internal_edge_gradient_coefficients(surfel);
  shell_conduction_data->assemble_vertex_nbr_gradient_coefficients(surfel);
  shell_conduction_data->assemble_boundary_edge_gradient_coefficients(surfel);
}

VOID create_linear_system(int num_states) {

  initialize_linear_system(num_states);
  preallocate_matrix();
  assemble_linear_system();
  finalize_assembly(true);
  create_solver_objects();

  if (g_implicit_shell_solver_any_cell_bad && g_shell_implicit_solver_guard_secondary_gradients) {
    msg_warn("Implicit shell solver: Turning off secondary gradients because bad cells were detected.");
  }

}

VOID initialize_linear_system(int num_states) {
  PETSC_COMM_WORLD = eMPI_csp_comm;

  // Get the contributions of the flux to the matrix
  PetscMPIInt size;
  const char * help = "Implicit shell solver.\n\n"; //nullptr;
  PetscVFPrintf = MyPetscVFPrintf;
  check(PetscInitialize(nullptr, nullptr, nullptr, help)); // filename and help are optional
  check( MPI_Comm_size(eMPI_csp_comm, &size) );

  // Set up vectors for the system
  check( VecCreate(eMPI_csp_comm, &g_shell_conduction_solution_vec)); // Create an empty vector object
  check( PetscObjectSetName((PetscObject)g_shell_conduction_solution_vec, "Solution") ); // Not strictly necessary, but nice to have control over the name
  check( VecSetType(g_shell_conduction_solution_vec, VECSTANDARD) ); // seq on one process and mpi on multiple
  check( VecSetSizes(g_shell_conduction_solution_vec, num_states, sim.num_implicit_shell_solver_states) ); // Tells Petsc how big x is
  check( VecDuplicate(g_shell_conduction_solution_vec, &g_shell_conduction_rhs_vec) ); // Create b so that it's the same type as x. This way we don't need to repeat all the code above
  check( VecDuplicate(g_shell_conduction_solution_vec, &g_shell_conduction_rhs_bc_vec) );
  check( VecDuplicate(g_shell_conduction_solution_vec, &g_shell_conduction_source_vec) );
  check( VecDuplicate(g_shell_conduction_solution_vec, &g_shell_conduction_rho_cp_vec) );
  check( VecDuplicate(g_shell_conduction_solution_vec, &g_shell_conduction_rhs_bc_temp_vec) );

  // Set up system matrix
  check( MatCreate(eMPI_csp_comm, &g_shell_conduction_coeff_mat) );
  check( MatSetType(g_shell_conduction_coeff_mat, MATMPIAIJ) );
  check( MatSetSizes(g_shell_conduction_coeff_mat, num_states, num_states, sim.num_implicit_shell_solver_states, sim.num_implicit_shell_solver_states) );
  check( MatSetUp(g_shell_conduction_coeff_mat) ); // Sets up the internal matrix data structures for later use. 
                                                   // Need this here so we can access ownership range in the preallocation stage.
                                                   // The documentation said users rarely need to call it, but the code didn't work without it.
}

VOID preallocate_matrix() {

  PetscInt first_local_row_global_index;
  PetscInt last_local_row_global_index; // actually 1 more than this, but the name was getting too long
  check( MatGetOwnershipRange(g_shell_conduction_coeff_mat, &first_local_row_global_index, &last_local_row_global_index) );

  PetscInt num_states = last_local_row_global_index - first_local_row_global_index;

  g_implicit_shell_solver_any_cell_bad = false; // should only be initialized here this one time

  std::vector<PetscInt> num_nonzeros_in_diagonal_block(num_states);
  std::vector<PetscInt> num_nonzeros_in_offdiagonal_block(num_states);

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      preallocate_matrix_internal(surfel, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      determine_bad_cells(surfel);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        preallocate_matrix_internal(surfel, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
        determine_bad_cells(surfel);
      }
    }
    DO_SLRF_SURFEL_PAIRS_OF_SCALE(slrf_surfel_pair, scale) {
      //Skipping assembly for these guys for now
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      preallocate_matrix_internal(wsurfel_conduction, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      determine_bad_cells(wsurfel_conduction);
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      preallocate_matrix_internal(contact_surfel, first_local_row_global_index, last_local_row_global_index, num_nonzeros_in_diagonal_block, num_nonzeros_in_offdiagonal_block);
      determine_bad_cells(contact_surfel);
    }
  }

  check( MatMPIAIJSetPreallocation(g_shell_conduction_coeff_mat, 0, num_nonzeros_in_diagonal_block.data(), 0, num_nonzeros_in_offdiagonal_block.data()) );
}

VOID preallocate_matrix_internal(SURFEL surfel, 
                                 PetscInt first_local_row_global_index, 
                                 PetscInt last_local_row_global_index, 
                                 std::vector<PetscInt> &num_nonzeros_in_diagonal_block, 
                                 std::vector<PetscInt> &num_nonzeros_in_offdiagonal_block) {

  if (!surfel->is_conduction_shell()) {
    return;
  }
  if (surfel->is_even() || surfel->is_mirror()) {
    return;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    return;
  }

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();

  // Now each layer will have his own set of neighbors I guess
  std::vector<std::unordered_set<PetscInt>> diagonal_neighbors(shell_conduction_data->num_layers());
  std::vector<std::unordered_set<PetscInt>> offdiagonal_neighbors(shell_conduction_data->num_layers());

  int root_state_index = surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index;
  DO_INTERNAL_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR edge_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);

    SURFEL nbr_surfel = edge_nbr->surfel();
    SURFEL_SHELL_CONDUCTION_DATA nbr_shell_conduction_data = nbr_surfel->shell_conduction_data();
    ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
      int nbr_state_index = nbr_surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index + nth_layer;
      if (nbr_state_index >= first_local_row_global_index && nbr_state_index < last_local_row_global_index) {
        diagonal_neighbors[nth_layer].insert(nbr_state_index); // diagonal block
      } else {
        offdiagonal_neighbors[nth_layer].insert(nbr_state_index); // offdiagonal block
      }
      DO_STENCIL_NEIGHBORS(shell_conduction_data, nth_nbr, is_boundary_nbr) {
        if (is_boundary_nbr) {
          continue;
        }
        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_edge = g_shell_conduction_edges.get_nbr_stencil(nth_nbr);
        int state_index = nbr_edge->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index + nth_layer;
        if (state_index >= first_local_row_global_index && state_index < last_local_row_global_index) {
          diagonal_neighbors[nth_layer].insert(state_index); // diagonal block
        } else {
          offdiagonal_neighbors[nth_layer].insert(state_index); // offdiagonal block
        }
      }
      DO_STENCIL_NEIGHBORS(nbr_shell_conduction_data, nth_nbr, is_boundary_nbr) {
        if (is_boundary_nbr) {
          continue;
        }
        SHELL_CONDUCTION_STENCIL_NEIGHBOR nbr_edge = g_shell_conduction_edges.get_nbr_stencil(nth_nbr);
        int nbr_nbr_state_index = nbr_edge->surfel()->shell_conduction_implicit_solver_data()->implicit_shell_state_index + nth_layer;
        if (nbr_nbr_state_index >= first_local_row_global_index && nbr_nbr_state_index < last_local_row_global_index) {
          diagonal_neighbors[nth_layer].insert(nbr_nbr_state_index); // diagonal block
        } else {
          offdiagonal_neighbors[nth_layer].insert(nbr_nbr_state_index); // offdiagonal block
        }
      }
    }
  }

  DO_BOUNDARY_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
      // Edge intersection boundaries might include neighbors that haven't been preallocated yet
      std::vector<int> edge_intersection_nbr_state_index = bnd_nbr->implicit_solver_edge_intersection_nbr_index();
      for (PetscInt edge_intersection_nbr_index : edge_intersection_nbr_state_index) {
        if (edge_intersection_nbr_index >= first_local_row_global_index && edge_intersection_nbr_index < last_local_row_global_index) {
          diagonal_neighbors[nth_layer].insert(edge_intersection_nbr_index); // diagonal block
        } else {
          offdiagonal_neighbors[nth_layer].insert(edge_intersection_nbr_index); // offdiagonal block
        }
      }
    } 
  }

  // This assumes that the matrix storage is surfel_id + surfel_num_layers
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    // Get the index for this dof on this proc. We need to subtract out the offset because the nonzeros vectors only
    // have size equal to the number of dofs on the processor so we don't want to offset the index of those vectors.
    int state_index = surfel->shell_conduction_implicit_solver_data()->implicit_shell_state_index - sim.implicit_shell_solver_state_index_offset[my_proc_id] + nth_layer;
    num_nonzeros_in_diagonal_block[state_index] = diagonal_neighbors[nth_layer].size() + 1; // This overallocates to be extra conservative
    num_nonzeros_in_offdiagonal_block[state_index] = offdiagonal_neighbors[nth_layer].size();
  }
}

VOID determine_bad_cells(SURFEL surfel) {

  if (!surfel->is_conduction_shell()) {
    return;
  }
  if (surfel->is_even() || surfel->is_mirror()) {
    return;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    return;
  }

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();

  DO_INTERNAL_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR edge_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    edge_nbr->implicit_solver_determine_bad_cells(surfel);
  }

}

VOID assemble_linear_system() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      assemble_linear_system_internal(surfel);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      assemble_linear_system_internal(wsurfel_conduction);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        assemble_linear_system_internal(surfel);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      assemble_linear_system_internal(contact_surfel);
    }
  }

  check( VecAssemblyBegin(g_shell_conduction_solution_vec) );
  check( VecAssemblyEnd(g_shell_conduction_solution_vec) );
}

VOID assemble_linear_system_internal(SURFEL surfel) {
  if (!surfel->is_conduction_shell()) {
    return;
  }
  if (surfel->is_even() || surfel->is_mirror()) {
    return;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    // Should never go in here b/c we already check !is_conduction_shell() above and that is also checked here. Keep
    // around for a while though just in case.
    return;
  }

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();

  DO_INTERNAL_EDGE_STENCILS(shell_conduction_data, nth_edge) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR edge_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_edge);
    edge_nbr->compute_implicit_solver_edge_fluxes(surfel);
  }

  DO_BOUNDARY_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    bnd_nbr->implicit_solver_assemble_rhs(surfel);
  }

  // Assemble initial solution and make sure diagonal of matrix has values
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    PetscInt global_row_idx = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;
    const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
    SHELL_LAYER_DATA surfel_nth_layer = shell_conduction_data->layer(nth_layer);
    PetscReal temp_init = surfel_nth_layer->temp(prior_index);
    check( VecSetValues(g_shell_conduction_solution_vec, 1, &global_row_idx, &temp_init, INSERT_VALUES) );
    //NOTE: Might not need this anymore b/c we're going to enforce it manually
    PetscReal zero = 0.0;
    check( MatSetValues(g_shell_conduction_coeff_mat, 1, &global_row_idx, 1, &global_row_idx, &zero, INSERT_VALUES) );
  }
}

VOID update_linear_system(SURFEL surfel, std::vector<sdFLOAT> sum_fluxes_and_sources_in_layer) {
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());

  update_system_rhs(surfel, prior_index, sum_fluxes_and_sources_in_layer);
  if (!sim.constant_implicit_shell_solver_matrix) {
    update_system_flux_matrix_and_bcs(surfel);
  }
}

VOID update_system_rhs(SURFEL surfel, const asINT32 prior_index, std::vector<sdFLOAT> sum_fluxes_and_sources_in_layer) {
  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel_shell_conduction_data(surfel);
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) { 
    SHELL_LAYER_DATA surfel_nth_layer = shell_conduction_data->layer(nth_layer);
    PetscInt global_idx = implicit_solver_state_index(surfel) + nth_layer;

    asINT32 root_scale_diff = -scale_to_log2_voxel_size(surfel->scale());
    sdFLOAT root_scale_factor = vr_length_scale_factor(root_scale_diff);
    // Don't need to multiply by dt because it is included in the formulation of the quasi-1d solver already.
    //NOTE on scaling: the quasi-1D returns the source term at the scale of the root surfel so we want to scale that
    //to the finest scale. This requires a multiplication by the root_scale_factor. The heat capacity is stored on the 
    //shell at the root surfels's scale too, so that also needs to be scaled, but the scaled heat capacity is in the
    //denominator of the source term. So these two scale factors cancel. But the quasi-1D also includes the
    //timestep in the source term and this timestep is evaluated on the root surfel scale. So we need to multiply by
    //the scale factor again. Multiplying by the conduction timestep below is necessary for cases with different
    //flow and conduction timesteps.
    sdFLOAT heat_capacity = surfel_nth_layer->heat_capacity[prior_index];
    PetscReal layer_normal_flux_and_source = sum_fluxes_and_sources_in_layer[nth_layer] / heat_capacity; 
    layer_normal_flux_and_source = layer_normal_flux_and_source / root_scale_factor; 
    layer_normal_flux_and_source += surfel_nth_layer->accumulated_dT; // this includes the accumulated dT from the wsurfels if any

    if (!sim.time_coupling_info.is_conservative_across_realms() && 
        surfel->is_conduction_interface() &&
        g_timescale.is_conduction_supercycled()) {
      //During a frozen phase, sum_fluxes_and_sources_in_layer provides the net energy received in the normal direction
      //for the full conduction timestep so the rhs computed above does not need an accumulated_dT correction
      //However, accumulated_dT is not updated during the frozen phase. Thus, it needs to be subtracted to
      //"undo" the correction to retrieve the correct value later when using layer->temp() method. NOTE: This is
      //necessary to be consistent with the explicit solver.
      layer_normal_flux_and_source -= surfel_nth_layer->accumulated_dT;
    }

    if (implicit_solver_data(surfel)->cell_is_bad && g_shell_implicit_solver_use_protectors) {
      // Don't put the heat source onto the RHS for these bad guys.
      layer_normal_flux_and_source = 0.0;
    }

    surfel_nth_layer->layer_matrix_data.source_needs_to_be_updated = true;
    surfel_nth_layer->layer_matrix_data.source_vals = layer_normal_flux_and_source;
  }
}

VOID update_system_flux_matrix_and_bcs(SURFEL surfel) {
  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();

  DO_INTERNAL_EDGE_STENCILS(shell_conduction_data, nth_edge) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR edge_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_edge);
    edge_nbr->compute_implicit_solver_edge_fluxes(surfel);
  }

  DO_BOUNDARY_EDGE_STENCILS(shell_conduction_data, nth_stencil) {
    SHELL_CONDUCTION_STENCIL_NEIGHBOR bnd_nbr = g_shell_conduction_edges.get_nbr_stencil(nth_stencil);
    bnd_nbr->implicit_solver_assemble_rhs(surfel);
  }
}

VOID finalize_assembly(bool first_solve) {
  if (!first_solve && !sim.constant_implicit_shell_solver_matrix) {
    // Hasn't been assembled yet for first solve.
    check( MatAssemblyBegin(g_shell_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatAssemblyEnd(g_shell_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatShift(g_shell_conduction_coeff_mat, -1.0) ); // Subtract one to the diagonal of the system matrix (Y = Y + aI). Has to be done after final assembly.
  }

  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      finalize_assembly_internal(surfel, first_solve);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      finalize_assembly_internal(wsurfel_conduction, first_solve);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        finalize_assembly_internal(surfel, first_solve);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      finalize_assembly_internal(contact_surfel, first_solve);
    }
  }

  if (first_solve || !sim.constant_implicit_shell_solver_matrix) {
    check( MatAssemblyBegin(g_shell_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatAssemblyEnd(g_shell_conduction_coeff_mat, MAT_FINAL_ASSEMBLY) );
    check( MatShift(g_shell_conduction_coeff_mat, 1.0) ); // Add one to the diagonal of the system matrix (Y = Y + aI). Has to be done after final assembly.
  }

  // Assemble source vector
  check( VecAssemblyBegin(g_shell_conduction_source_vec) );
  check( VecAssemblyEnd(g_shell_conduction_source_vec) );

  if (first_solve || !sim.constant_implicit_shell_solver_matrix) {
    // Assemble BCs
    check( VecAssemblyBegin(g_shell_conduction_rhs_bc_vec) );
    check( VecAssemblyEnd(g_shell_conduction_rhs_bc_vec) );
  }

  // Assemble RHS
  check( VecCopy(g_shell_conduction_solution_vec, g_shell_conduction_rhs_vec) ); // Copies solution_vec into rhs_vec 
  check( VecAXPY(g_shell_conduction_rhs_vec, 1.0, g_shell_conduction_rhs_bc_vec) ); // Computes y = alpha x + y
  check( VecAXPY(g_shell_conduction_rhs_vec, 1.0, g_shell_conduction_source_vec) );
  check( VecAssemblyBegin(g_shell_conduction_rhs_vec) );
  check( VecAssemblyEnd(g_shell_conduction_rhs_vec) );
}

VOID finalize_assembly_internal(SURFEL surfel, bool first_solve) {
  if (!surfel->is_conduction_shell()) {
    return;
  }
  if (surfel->is_even()) {
    return;
  }
  if (surfel->is_conduction_open_shell_boundary()) {
    // Should never go in here b/c we already check !is_conduction_shell() above and that is also checked here. Keep
    // around for a while though just in case.
    return;
  }

  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel->shell_conduction_data();
  SURFEL_SHELL_CONDUCTION_IMPLICIT_SOLVER_DATA shell_implicit_solver_data = surfel->shell_conduction_implicit_solver_data();
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA surfel_nth_layer = shell_conduction_data->layer(nth_layer);
    PetscInt global_row_idx = shell_implicit_solver_data->implicit_shell_state_index + nth_layer;

    if (first_solve || 
        (surfel_nth_layer->layer_matrix_data.system_row_needs_to_be_updated && 
         !sim.constant_implicit_shell_solver_matrix)) {
      std::vector<PetscInt> column_indices;
      std::vector<PetscReal> column_vals;
      for (const auto &m : surfel_nth_layer->layer_matrix_data.row_idx_val) {
        column_indices.push_back(m.first);
        column_vals.push_back(m.second);
      }
      check( MatSetValues(g_shell_conduction_coeff_mat, 1, &global_row_idx, 
                          column_indices.size(),
                          column_indices.data(), 
                          column_vals.data(),
                          INSERT_VALUES) );

      surfel_nth_layer->layer_matrix_data.system_row_needs_to_be_updated = false;
      surfel_nth_layer->layer_matrix_data.row_idx_val.clear(); 
    }

    if (surfel_nth_layer->layer_matrix_data.source_needs_to_be_updated) {
      PetscReal source_vals = surfel_nth_layer->layer_matrix_data.source_vals;
      check( VecSetValues(g_shell_conduction_source_vec, 
                          1,
                          &global_row_idx,
                          &source_vals,
                          INSERT_VALUES) );
      surfel_nth_layer->layer_matrix_data.source_needs_to_be_updated = false;
      surfel_nth_layer->layer_matrix_data.source_vals = 0.0;
    }

    if (first_solve || 
        (surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated && 
         !sim.constant_implicit_shell_solver_matrix)) {
      PetscReal bc_vals = surfel_nth_layer->layer_matrix_data.bc_vals;
      check( VecSetValues(g_shell_conduction_rhs_bc_vec, 
                          1,
                          &global_row_idx,
                          &bc_vals,
                          INSERT_VALUES) );
      surfel_nth_layer->layer_matrix_data.bc_needs_to_be_updated = false;
      surfel_nth_layer->layer_matrix_data.bc_vals = 0.0;
    }
  }
}

VOID create_solver_objects() {
  check( KSPCreate(eMPI_csp_comm, &g_ksp) ); // Just creates a KSP context and sets a bunch of default solver parameters.
 
  // Set operators (e.g. preconditioners)
  check( KSPSetOperators(g_ksp, g_shell_conduction_coeff_mat, g_shell_conduction_coeff_mat) );

  // Set some optional arguments
  PC pc;
  check( KSPGetPC(g_ksp, &pc) ); // grab the preconditioner context to set preconditioner options on it if we want to
  check( PCSetType(pc, PCGAMG) ); // AMG preconditioner
  check( PCGAMGSetNSmooths(pc, 0) );
  check( KSPSetType(g_ksp, KSPGMRES) ); // Set the solver type
  PetscReal rtol = g_shell_implicit_solver_relative_tolerance; //1.0e-09;
  check( KSPSetTolerances(g_ksp, rtol, PETSC_DEFAULT, PETSC_DEFAULT, PETSC_DEFAULT) );
  check( KSPGMRESSetRestart(g_ksp, 60) );
  check( KSPSetFromOptions(g_ksp) ); // Override the previous options with options specified in an options file

  check( KSPSetInitialGuessNonzero(g_ksp, PETSC_TRUE) );

  check( KSPMonitorSet(g_ksp, MyKSPMonitor, NULL, NULL) );

  if (g_shell_implicit_solver_verbosity > 0) {
    check( KSPView(g_ksp, PETSC_VIEWER_STDOUT_WORLD) );
  }
}

// Happening in the comm thread now
VOID implicit_shell_solver_evolve() {
  if (g_implicit_solver_active && g_implicit_solver_setup_complete) {
    pthread_mutex_lock(&g_implicit_solver_mutex);

    finalize_assembly(false); // Not the first solve
    solve_linear_system();
    implicit_shell_solver_update_surfel_temperature(); // Try to put the solution from Petsc into the shell surfel temperature

    g_implicit_solver_active = false;
    pthread_cond_signal(&g_implicit_solver_cond);
    pthread_mutex_unlock(&g_implicit_solver_mutex);
  }
}

VOID solve_linear_system() {
  // For diagnostics get the system size
  PetscInt num_rows, num_cols;
  check( MatGetSize(g_shell_conduction_coeff_mat, &num_rows, &num_cols) );

  // Solve matrix system
  PetscInt iterations;
  KSPConvergedReason converged;
  PetscReal rnorm;
  if (g_shell_implicit_solver_verbosity > 1) {
    int residual_monitor_freq = 100;
    if (g_shell_implicit_solver_verbosity > 2) {
      residual_monitor_freq = 1;
    }
    if (g_timescale.m_time % residual_monitor_freq == 0) {
      check( PetscPrintf(eMPI_csp_comm, "TS %ld\n", g_timescale.m_time) );
    }
  }
  bool write_system_to_txt = false;
  //if (g_shell_implicit_solver_verbosity > 3) {
  if (write_system_to_txt) {
    write_linear_system_to_txt();
  }
  WITH_TIMER(SP_IMPLICIT_SOLVER_TIMER) {
    check( KSPSetInitialGuessNonzero(g_ksp, PETSC_TRUE) );

    check( KSPSolve(g_ksp, g_shell_conduction_rhs_vec, g_shell_conduction_solution_vec) );

    check( KSPGetConvergedReason(g_ksp, &converged) );
    check( KSPGetIterationNumber(g_ksp, &iterations) );
    check( KSPGetResidualNorm(g_ksp, &rnorm) );
  }
  timer_accum_counters(SP_IMPLICIT_SOLVER_TIMER, 0, iterations);
  timer_accum_counters(SP_IMPLICIT_SOLVER_TIMER, 1, num_rows); // square matrix, so nrows = ncols

  if (converged < 0) {
    check( PetscPrintf(eMPI_csp_comm, "KSPConvergedReason: %s\n", KSPConvergedReasons[converged]) );
    msg_error("TS %ld: Implicit shell solver did not converge!", g_timescale.m_time);
  }

}

// Happens in the compute thread
VOID start_and_wait_for_implicit_shell_solver() {
  pthread_mutex_lock(&g_implicit_solver_mutex);
  g_implicit_solver_active = true; // being protected by the lock
  while (g_implicit_solver_active)
  {
    pthread_cond_wait(&g_implicit_solver_cond, &g_implicit_solver_mutex); // guarantees that the mutex will be unlocked after you enter and then locks it again after it returns
  }
  pthread_mutex_unlock(&g_implicit_solver_mutex);
}

// Happens in the compute thread
VOID start_and_wait_for_implicit_shell_solver_setup() {
  pthread_mutex_lock(&g_implicit_solver_setup_mutex);
  g_implicit_solver_setup_active = true; // being protected by the lock
  while (g_implicit_solver_setup_active)
  {
    pthread_cond_wait(&g_implicit_solver_setup_cond, &g_implicit_solver_setup_mutex); // guarantees that the mutex will be unlocked after you enter and then locks it again after it returns
  }
  pthread_mutex_unlock(&g_implicit_solver_setup_mutex);
}

VOID implicit_shell_solver_update_surfel_temperature() {

  bool write_solution_to_txt = false;
  if (g_shell_implicit_solver_verbosity > 4) {
    write_solution_to_txt = true;
  }
  if (write_solution_to_txt) {
    PetscViewer sol_viewer;
    std::string solution_file = "solution" + std::to_string(g_timescale.m_time) + ".txt";
    check( PetscViewerASCIIOpen(eMPI_csp_comm, solution_file.c_str(), &sol_viewer) );
    check( PetscViewerPushFormat(sol_viewer, PETSC_VIEWER_ASCII_DENSE) );
    check( VecView(g_shell_conduction_solution_vec, sol_viewer) );
    check( PetscViewerPopFormat(sol_viewer) );
    check( PetscViewerDestroy(&sol_viewer) );
  }

  // Loop over the surfels and put the Petsc solution into the shell temperature
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_DYN_SURFELS_CONDUCTION_OF_SCALE(surfel, scale) {
      implicit_shell_solver_update_surfel_temperature_internal(surfel);
    }
    DO_WSURFELS_CONDUCTION_OF_SCALE(wsurfel_conduction, scale) {
      implicit_shell_solver_update_surfel_temperature_internal(wsurfel_conduction);
    }
    DO_MLRF_SURFEL_GROUPS_OF_SCALE(group, scale) {
      ccDOTIMES(nth_surfel, group->n_quantums()) {
        SURFEL surfel = group->quantum(nth_surfel)->mlrf_surfel();
        implicit_shell_solver_update_surfel_temperature_internal(surfel);
      }
    }
    DO_CONTACT_SURFELS_OF_SCALE(contact_surfel, scale) {
      implicit_shell_solver_update_surfel_temperature_internal(contact_surfel);
    }
  }
}

VOID implicit_shell_solver_update_surfel_temperature_internal(SURFEL surfel) {
  if (!surfel->is_conduction_shell()) {
    return;
  }
  const asINT32 prior_index = g_timescale.m_conduction_pde_tm.prior_timestep_index(surfel->scale());
  const asINT32 current_index = prior_index ^ 1;
  SURFEL_SHELL_CONDUCTION_DATA shell_conduction_data = surfel_shell_conduction_data(surfel);
  ccDOTIMES(nth_layer, shell_conduction_data->num_layers()) {
    SHELL_LAYER_DATA surfel_nth_layer = shell_conduction_data->layer(nth_layer);
    PetscInt global_idx = implicit_solver_state_index(surfel) + nth_layer;
    PetscReal temp_new; // temperature at timestep n+1. 

    // Get this surfel's temperature, regardless of whether or not it's a bad surfel
    check( VecGetValues(g_shell_conduction_solution_vec, 1, &global_idx, &temp_new) );
    surfel_nth_layer->set_temp(current_index, temp_new);

  }
}

VOID write_linear_system_to_txt() {
  //PetscViewer T_init_viewer;
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "T_init.txt", &T_init_viewer) );
  //check( PetscViewerPushFormat(T_init_viewer, PETSC_VIEWER_ASCII_DENSE) );
  ////check( PetscViewerPushFormat(T_init_viewer, PETSC_VIEWER_ASCII_COMMON) );
  //check( VecView(g_shell_conduction_solution_vec, T_init_viewer) );
  //check( PetscViewerPopFormat(T_init_viewer) );
  //check( PetscViewerDestroy(&T_init_viewer) );

  //PetscViewer rhs_viewer;
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "rhs.txt", &rhs_viewer) );
  //check( PetscViewerPushFormat(rhs_viewer, PETSC_VIEWER_ASCII_DENSE) );
  ////check( PetscViewerPushFormat(rhs_viewer, PETSC_VIEWER_ASCII_COMMON) );
  //check( VecView(g_shell_conduction_rhs_vec, rhs_viewer) );
  //check( PetscViewerPopFormat(rhs_viewer) );
  //check( PetscViewerDestroy(&rhs_viewer) );

  //PetscViewer source_viewer;
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "source.txt", &source_viewer) );
  //check( PetscViewerPushFormat(source_viewer, PETSC_VIEWER_ASCII_DENSE) );
  //check( VecView(g_shell_conduction_source_vec, source_viewer) );
  //check( PetscViewerPopFormat(source_viewer) );
  //check( PetscViewerDestroy(&source_viewer) );

  //PetscViewer rhs_bc_viewer;
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "rhs_bc.txt", &rhs_bc_viewer) );
  //check( PetscViewerPushFormat(rhs_bc_viewer, PETSC_VIEWER_ASCII_DENSE) );
  ////check( PetscViewerPushFormat(rhs_bc_viewer, PETSC_VIEWER_ASCII_COMMON) );
  //check( VecView(g_shell_conduction_rhs_bc_vec, rhs_bc_viewer) );
  //check( PetscViewerPopFormat(rhs_bc_viewer) );
  //check( PetscViewerDestroy(&rhs_bc_viewer) );
  
  PetscViewer viewer;
  check( PetscViewerASCIIOpen(eMPI_csp_comm, "Amat.txt", &viewer) );
  //check( PetscViewerASCIIOpen(eMPI_csp_comm, "flux_mat.txt", &viewer) );
  check( PetscViewerPushFormat(viewer, PETSC_VIEWER_ASCII_DENSE) );
  //check( PetscViewerPushFormat(viewer, PETSC_VIEWER_ASCII_COMMON) );
  check (MatView(g_shell_conduction_coeff_mat, viewer) );
  check( PetscViewerPopFormat(viewer) );
  check( PetscViewerDestroy(&viewer) );
}

VOID write_linear_system_to_bin() {
  PetscViewer T_init_viewer;
  std::string BinName;
  BinName = fmt::format("T_init_{}.bin", g_timescale.m_time);
  check( PetscViewerBinaryOpen(eMPI_csp_comm, BinName.c_str(), FILE_MODE_WRITE, &T_init_viewer) );
  check( VecView(g_shell_conduction_solution_vec, T_init_viewer) );
  check( PetscViewerDestroy(&T_init_viewer) );
  

  PetscViewer rhs_viewer;
  BinName = fmt::format("rhs_{}.bin", g_timescale.m_time);
  check( PetscViewerBinaryOpen(eMPI_csp_comm, BinName.c_str(), FILE_MODE_WRITE, &rhs_viewer) );
  check( VecView(g_shell_conduction_rhs_vec, rhs_viewer) );
  check( PetscViewerDestroy(&rhs_viewer) );

  PetscViewer rhs_bc_viewer;
  BinName = fmt::format("rhs_bc_{}.bin", g_timescale.m_time);
  check( PetscViewerBinaryOpen(eMPI_csp_comm, BinName.c_str(), FILE_MODE_WRITE, &rhs_bc_viewer) );
  check( VecView(g_shell_conduction_rhs_bc_vec, rhs_bc_viewer) );
  check( PetscViewerDestroy(&rhs_bc_viewer) );

  PetscViewer viewer;
  BinName = fmt::format("Amat_{}.bin", g_timescale.m_time);
  check( PetscViewerBinaryOpen(eMPI_csp_comm, BinName.c_str(), FILE_MODE_WRITE, &viewer) );
  check( MatView(g_shell_conduction_coeff_mat, viewer) );
  check( PetscViewerDestroy(&viewer) );
}

VOID implicit_shell_solver_destroy_solver_objects() {
  check( KSPDestroy(&g_ksp) );
  check( VecDestroy(&g_shell_conduction_rhs_vec) );
  check( VecDestroy(&g_shell_conduction_rhs_bc_vec) );
  check( VecDestroy(&g_shell_conduction_solution_vec) );
  check( VecDestroy(&g_shell_conduction_rho_cp_vec) );
  //check( VecDestroy(&g_shell_conduction_source_vec) );
  check( MatDestroy(&g_shell_conduction_coeff_mat) );
  ////check( PetscFinalize() );
}

PetscErrorCode MyKSPMonitor(KSP ksp, PetscInt it, PetscReal rnorm, void *ctx) {

  PetscFunctionBeginUser;

  int iteration_print_freq = 1;
  if (it > 20) {
    iteration_print_freq = 10;
  }

  if (g_shell_implicit_solver_verbosity > 1) {
    int residual_monitor_freq = 100;
    if (g_shell_implicit_solver_verbosity > 2) {
     residual_monitor_freq = 1;
    }
    if (g_timescale.m_time % residual_monitor_freq == 0) {
      if (it % iteration_print_freq == 0) {
        check( PetscPrintf(eMPI_csp_comm, "    iteration %" PetscInt_FMT " KSP Residual norm %14.12e\n", it, (double)rnorm) );
      }
    }
  }

  PetscFunctionReturn(0);
}
 
PetscErrorCode MyPetscVFPrintf(FILE *fd, const char format[], va_list Argp) {
  PetscFunctionBegin;

  static FILE *petsc_error_file = NULL;
  if (petsc_error_file == NULL) {
    petsc_error_file = fopen(".exa_implicit_shell_solver_status", "w");
  }

  char   buff[1024];
  size_t length;
  check( PetscVSNPrintf(buff, 1024, format, &length, Argp) );
  fprintf(petsc_error_file, "%s", buff);
  fflush(petsc_error_file);
  PetscFunctionReturn(0);
}

void drain_implicit_solver_simerrs() {
  int count = 50;
  while (g_implicit_shell_solver_simerrs.size() && count > 0) {
    count--;
    auto err = g_implicit_shell_solver_simerrs.back();
    g_implicit_shell_solver_simerrs.pop_back();
    sdFLOAT centroid[3];
    switch (err.implicit_solver_error_type) {
      case SP_EER_IMPLICIT_SHELL_SOLVER_ANTI_DIFFUSION:
      case SP_EER_IMPLICIT_SHELL_SOLVER_NONORTHOGONALITY:
      case SP_EER_IMPLICIT_SHELL_SOLVER_BAD_AREA_RATIO:
        centroid[0] = err.root_centroid_x;
        centroid[1] = err.root_centroid_y;
        centroid[2] = err.root_centroid_z;
        simerr_report_error_code(err.implicit_solver_error_type, err.root_surfel_scale, centroid,
                                 err.root_surfel_id,
                                 err.root_surfel_area,
                                 err.nbr_surfel_id,
                                 err.nbr_surfel_area,
                                 err.nbr_centroid_x,
                                 err.nbr_centroid_y,
                                 err.nbr_centroid_z,
                                 err.error_data,
                                 err.str.c_str());
        break;
      case SP_EER_IMPLICIT_SHELL_SOLVER_TINY_SURFEL:
        centroid[0] = err.root_centroid_x;
        centroid[1] = err.root_centroid_y;
        centroid[2] = err.root_centroid_z;
        simerr_report_error_code(err.implicit_solver_error_type, err.root_surfel_scale, centroid,
                                 err.root_surfel_id,
                                 err.root_surfel_area,
                                 err.str.c_str());
        break;
      default:
        break;
    }
  }
}

#endif
