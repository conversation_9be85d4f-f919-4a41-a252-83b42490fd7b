/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Communication for inline fan
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_FAN_COMM_H
#define _SIMENG_FAN_COMM_H


#include "common_sp.h"
#include "timescale.h"
#include "lattice.h"
#include "comm_groups.h"
#include "strand_global.h"
#include "strands.h"
#include "fan.h"

typedef struct sFAN_COMM_DESCRIPTOR {
  sINT16 physics_descriptor_index;			// index in table
  sINT32 max_voxel_delta_t_this_sp;
  dFLOAT n_finest_voxels_this_sp;

  dFLOAT this_sp_buffer[FANS::N_FAN_DATA];

  /* MPI related */
  MPI_Request send_request;
  MPI_Request recv_request;
} *FAN_COMM_DESCRIPTOR;

class cFAN_COMM {
  pthread_mutex_t m_fan_reduction_mutex;
  bool m_post_req_flag;
  BASETIME m_timestep;
  pthread_cond_t m_fan_reduction_cv;

  void process_requests_impl();

public:

  // processed by the comm thread
  void process_requests() {
    if (n_global_fan_descs > 0) {
      pthread_mutex_lock(&m_fan_reduction_mutex);
      {
        if (m_post_req_flag) {
          process_requests_impl();
          m_post_req_flag = false;
          pthread_cond_signal(&m_fan_reduction_cv);
        }
      }
      pthread_mutex_unlock(&m_fan_reduction_mutex);
    }
  }

  void request_and_wait_fan_data() {  
    pthread_mutex_lock(&m_fan_reduction_mutex);
    {
      m_timestep = g_timescale.m_time;
      m_post_req_flag = true;

      LOG_MSG("RECV_DEPEND").printf("Request fan_send_req timestep %d", m_timestep);

      do {
        pthread_cond_wait(&m_fan_reduction_cv, &m_fan_reduction_mutex);
      } while(m_post_req_flag);
    }
    pthread_mutex_unlock(&m_fan_reduction_mutex);
  }
    
  void init() {
    pthread_mutex_init(&m_fan_reduction_mutex,NULL);
    pthread_cond_init(&m_fan_reduction_cv,NULL);
    m_post_req_flag = false;
  }

};

VOID initialize_global_fan_descriptors();
VOID initialize_local_fan_descriptors();
VOID exchange_fan_info();
// Need to pass requested send (or recv) timestep since these functions are called by comm thread, 
// which is not synced to the compute thread, thus g_timescale.m_time cannot be used directly.
VOID do_fan_remote_comm_send(BASETIME requested_timestep);
VOID do_fan_remote_comm_recv(BASETIME requested_timestep);
VOID do_fan_remote_comm_post_initial_send();
VOID do_fan_remote_comm_post_initial_recv();
VOID wait_for_fan_remote_comm_initial_recvs_complete();
VOID wait_for_fan_remote_comm_recvs_complete();
VOID cancel_pending_fan_recvs();
#endif
