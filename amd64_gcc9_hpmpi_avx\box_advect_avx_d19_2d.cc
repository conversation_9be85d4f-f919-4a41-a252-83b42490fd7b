#include "../common_sp.h"
#include "../gather_advect.h"
#include "../box_advect_gen_headers.h"
template <BOX_ADVECT_TEMPLATE_PARAMETERS>
static INLINE 
 VOID d19_avx_collect_advect_states_2D(COLLECT_ADVECT_STATES_PARAMS) { 

const asINT32 prev_lb_index = lb_index_from_mask(prior_solver_index_mask);
const asINT32 prev_t_index  = t_index_from_mask(prior_solver_index_mask);
const asINT32 prev_uds_index  = uds_index_from_mask(prior_solver_index_mask);
constexpr asINT32 voxor = 0;
//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,0}; 
    { //SELF
      sUBLK*  src_ublk = pde_advect_ublk;
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 0;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 1;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 2;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 3;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b00010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00000001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b01000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[0] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[0];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 1;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b00000001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 10;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 12;
          constexpr auINT8 src_voxel_mask = 0b00000101;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[1] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[1];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 0;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b01000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 11;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 13;
          constexpr auINT8 src_voxel_mask = 0b01010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 6, 1, 4);
          constexpr auINT8 dst_voxel_mask = 0b00000101;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 2, 5, 0, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[2] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[2];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 3;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00000001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 14;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 17;
          constexpr auINT8 src_voxel_mask = 0b00010001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[3] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[3];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 2;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b01000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b00000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 15;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
        {
          constexpr asINT32 latvec = 16;
          constexpr auINT8 src_voxel_mask = 0b01000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 6, 3, 2, 1, 2);
          constexpr auINT8 dst_voxel_mask = 0b00010001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 4, 5, 4, 3, 0, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[6] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[6];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 7;
          constexpr auINT8 src_voxel_mask = 0b00000100;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00010000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[7] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[7];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 6;
          constexpr auINT8 src_voxel_mask = 0b00010000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 4, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b00000100;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 2, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[8] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[8];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 9;
          constexpr auINT8 src_voxel_mask = 0b00000001;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          constexpr auINT8 dst_voxel_mask = 0b01000000;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,-1,0}; 
  BOOLEAN is_src_ublk_valid = advect_nbrs[9] != nullptr;
    if (is_src_ublk_valid) {
      sUBLK*  src_ublk = advect_nbrs[9];
      BOOLEAN src_tagged_ublk_has_two_copies = src_ublk->has_two_copies();
      if (src_tagged_ublk_has_two_copies){
        explode_VR_coarse_on_demand<ADVECT_TEMP, ADVECT_UDS>(src_ublk, is_timestep_even, prior_solver_index_mask, active_solver_mask);
      }
        {
          constexpr asINT32 latvec = 8;
          constexpr auINT8 src_voxel_mask = 0b01000000;
          const     __m256i src_perm_ind = PERM_IND_PS(7, 6, 5, 4, 3, 2, 1, 6);
          constexpr auINT8 dst_voxel_mask = 0b00000001;
          const     __m256i dst_perm_ind = PERM_IND_PS(7, 0, 5, 4, 3, 2, 1, 0);
          APPEND_V2V_VECTORIZED
        }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,-1,0}; 

}

