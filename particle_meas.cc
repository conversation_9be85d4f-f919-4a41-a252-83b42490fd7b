/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 

#include "common_sp.h"
#include "surfel_advect_sp.h"
#include "surfel_dyn_sp.h"
#include "sim.h"
#include "particle_sim.h"
#include "particle_solver_data.h"
#include "vr.h"
#include "advect.h"
#include "voxel_dyn_sp.h"
#include "parse_shob_descs.h"
#include "lattice.h"
#include "mirror.h"
#include "sp_timers.h"
#include "film_solver_stencils.h"
#include "trajectory_window.h"
#include "trajectory_meas.h"
#include "particle_sim_info.h"
#include "bsurfel_dyn_sp.h"
#include "bsurfel_util.h"

BOOLEAN has_visited_a_window(PARCEL_STATE parcel, auINT32 window_index) {
  return parcel->visited_trajectory_windows & 0x1 << window_index;
}

BOOLEAN parcel_is_measurable(PARCEL_STATE parcel, auINT32 window_index) {
  return parcel->parcel_is_measurable_for_trajectory_window & 0x1 << window_index;
}

VOID make_parcel_measurable(PARCEL_STATE parcel, auINT32 window_index) {
  parcel->parcel_is_measurable_for_trajectory_window |= 0x1 << window_index;
}

VOID make_parcel_unmeasurable(PARCEL_STATE parcel, auINT32 window_index) {
  parcel->parcel_is_measurable_for_trajectory_window &= ~(0x1 << window_index);
}

void mark_as_having_visited_window(PARCEL_STATE parcel, auINT32 window_index) {
  parcel->visited_trajectory_windows |= 0x1 << window_index;
}

VOID defenestrate(PARCEL_STATE parcel, auINT32 window_index) {
  parcel->visited_trajectory_windows &= ~(0x1 << window_index);
  make_parcel_unmeasurable(parcel, window_index);
  parcel->first_measured_ts = -1;
  parcel->order_in_measured_ts = -1;
}

BOOLEAN first_time_in_a_window(PARCEL_STATE parcel, auINT32 window_index) {
  if ( (parcel->visited_trajectory_windows & 0x1 << window_index) == 0) {
    mark_as_having_visited_window(parcel, window_index);
    return TRUE;
  }
  return FALSE;
}

BOOLEAN compute_eligibility (PARCEL_STATE parcel, auINT32 window_index, sPARTICLE_VAR particle_fraction) {
  //Randomly select whether this particle is measurable
  sPARTICLE_VAR random_sample = (g_random_particle_properties->uniform() + 1.0) / 2.0;
  sPARTICLE_VAR parcel_fraction = particle_fraction;// / parcel->num_particles;
  if ( random_sample < parcel_fraction ) {
    make_parcel_measurable(parcel, window_index);
    return TRUE;
  }
  return FALSE;
}


VOID transform_position_to_global_csys(sPARTICLE_VAR global_position[3],
                                       sPARTICLE_VAR local_position[3],
                                       LRF_PHYSICS_DESCRIPTOR lrf) {
  if(lrf == NULL) {
    vcopy(global_position, local_position);
  } else {
    sPARTICLE_VAR radius[3];
    vsub(radius, local_position, lrf->point);
    rotate_vector(radius, radius, lrf->local_to_containing_rotation_matrix);
    vinc(radius, lrf->point);
    if(lrf->containing_lrf_index == -1) {
      vcopy(global_position, radius);
    } else {
      LRF_PHYSICS_DESCRIPTOR containing_lrf = &(sim.lrf_physics_descs[lrf->containing_lrf_index]);
      transform_position_to_global_csys(global_position, radius, containing_lrf);
    }
  }
}

VOID transform_velocity_to_global_csys(sPARTICLE_VAR global_velocity[3],
                                       sPARTICLE_VAR local_velocity[3],
                                       sPARTICLE_VAR local_position[3],
                                       LRF_PHYSICS_DESCRIPTOR lrf,
                                       asINT32 scale) {
  if(lrf == NULL) {
    vcopy(global_velocity, local_velocity);
  } else {
    sPARTICLE_VAR radius[3];
    vsub(radius, local_position, lrf->point);
    vcross(global_velocity, lrf->omega_total, radius);
    vinc(global_velocity, lrf->linear_vel);
    vinc(global_velocity, local_velocity);
    rotate_vector(global_velocity, global_velocity, lrf->local_to_global_rotation_matrix);
  }
}

VOID sPARTICLE_SIM::maybe_send_final_vertex(UBLK ublk, 
                                            PARCEL_STATE parcel, 
                                            BOOLEAN already_had_a_hitpoint) {
  //This function was added to force the CP to create state updates for particles that are being destroyed
  //unexpectedly (e.g. lost in a split voxel mess, or can't find their ublk) and for particles that died in expected
  //ways (e.g. hit a wall) but in windows with no hitpoints being recorded.

  //The second scenario is needed becasue the CP uses hitpoints to triger the creation of state updates for particles
  //that have died cleanly.  A vertex with event type EVENT_EXITED_WINDOW triggers the CP to create a state update indicating
  //a particle has left a measurement window and this tells PowerVIZ to stop extrapolating the trace indefinitly.

  if (g_no_trajectory_measurement)  return;

  uINT64 window_mask = ublk->p_data()->s.window_mask;
  ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
    if( window_mask & 0x1 << trajectory_window_index) {
      TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
      if(trajectory_window->is_active()) {
        if(parcel_is_measurable(parcel, trajectory_window_index) ) {
          asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
          sPARTICLE_VAR transformed_velocity[N_SPACE_DIMS];
          sPARTICLE_VAR transformed_position[N_SPACE_DIMS];

          transform_velocity_to_global_csys(transformed_velocity,
                                            parcel->v[this_timestep_index],
                                            parcel->x[this_timestep_index],
                                            parcel->lrf(),
                                            ublk->scale());

          transform_position_to_global_csys(transformed_position,
                                            parcel->x[this_timestep_index],
                                            parcel->lrf());

          if(trajectory_window->hitpoint_options() == HIT_POINTS_NONE ||
             !already_had_a_hitpoint) {
            trajectory_window->record_vertex(parcel,
                                             transformed_position,
                                             transformed_velocity,
                                             EVENT_EXITED_WINDOW);
          }
        }
      }
    }
  }
}

VOID sPARTICLE_SIM::interrupt_trace_due_to_a_discontinuity(UBLK ublk,
                                            PARCEL_STATE parent_parcel,
                                            PARTICLE_EVENT_TYPE event_type,
                                            std::vector<PARCEL_STATE> &child_parcels,
                                            std::vector<PARTICLE_EVENT_TYPE> &child_source_flags) {
  if (g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if( window_mask & 0x1 << trajectory_window_index) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active() && trajectory_window->include_vertices()) {
          
          if(parcel_is_measurable(parent_parcel, trajectory_window_index) ) { //eligibility is determined by the upstream parcel
            
            asINT32 this_state_index = parent_parcel->relative_timestep_to_state_index(0);
            sPARTICLE_VAR transformed_velocity[N_SPACE_DIMS];
            sPARTICLE_VAR transformed_position[N_SPACE_DIMS];
            transform_velocity_to_global_csys(transformed_velocity,
                                              parent_parcel->v[this_state_index],
                                              parent_parcel->x[this_state_index],
                                              parent_parcel->lrf(),
                                              ublk->scale());
            vmul(transformed_velocity, one_over_lattice_time_correction());
            transform_position_to_global_csys(transformed_position,
                                              parent_parcel->x[this_state_index],
                                              parent_parcel->lrf());

            //Assign the child parcels new IDs so they proceeds as a new trace.
            ccDOTIMES(nth_child, child_parcels.size()) {
              make_parcel_measurable(child_parcels[nth_child], trajectory_window_index);
              assign_parcel_a_remapable_id(child_parcels[nth_child], trajectory_window_index);
              trajectory_window->record_startpoint(child_parcels[nth_child], event_type, parent_parcel);
              //PowerVIZ does not draw a trace from the last vertex of the parent to
              //the first vertex of the child, so we need the child to duplicate the
              //last vertex of the parent as it's first vertex.
              if(event_type == EVENT_BREAKUP) 
                //Only do this for a breakup event and not a
                //discontinuity due to a periodic wrap.  For the
                //later, we can't store a vertex until the parcel has
                //been repositioned.
                trajectory_window->record_vertex(child_parcels[nth_child],
                                                 transformed_position,
                                                 transformed_velocity,
                                                 EVENT_VERTEX);
            }

            //Record the final vertex for the parent parcel.
            trajectory_window->record_vertex(parent_parcel,
                                             transformed_position,
                                             transformed_velocity,
                                             EVENT_VERTEX);
            
            //End the upstream trace but record its connection to the child traces.

            STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS];
            vcopy(surface_normal, transformed_velocity);
            vunitize(surface_normal);

            trajectory_window->record_hitpoint_with_children(parent_parcel,
                                                             transformed_position,
                                                             1,
                                                             -1, //surface_index
                                                             transformed_velocity,
                                                             transformed_velocity, //rebound velocity discarded for breakup events
                                                             surface_normal, //surface_normal discarded on for breakup events
                                                             event_type,
                                                             child_parcels,
                                                             child_source_flags);
          }
        }
      }
    }
  }
}


VOID sPARTICLE_SIM::interrupt_trace_for_a_breakup( UBLK ublk, 
                                                              PARCEL_STATE parcel,
                                                              std::vector<PARCEL_STATE> &child_parcels) {
  //Assume parcel could appear in the child parcel vector because the
  //breakup model doesn't necessecarily create a new parcel when it
  //modifiess the parcel properties after a breakup event (the parcel
  //can breakup into two or more new parcels but generally the
  //diameter of the existing parcel is reduced and the particles per
  //parcel is modified).
  sPARCEL_STATE parent_parcel(*parcel); //make a temp copy of the parcel to account for the aboce
  std::vector<PARTICLE_EVENT_TYPE> child_source_flags(child_parcels.size(), EVENT_BREAKUP);
  interrupt_trace_due_to_a_discontinuity(ublk, &parent_parcel, EVENT_BREAKUP, child_parcels, child_source_flags);
  parent_parcel.dynamics_type = ABOUT_TO_BE_DELETED; //keep the destructor happy
}

VOID sPARTICLE_SIM::interrupt_trace_for_parcel_crossing_a_periodic_bc(
                                                                      UBLK ublk, 
                                                                      PARCEL_STATE parcel, 
                                                                      sPARTICLE_VAR old_position[N_SPACE_DIMS]) { 
  //This function was added to force the CP to create a new trace for
  //a pariticle that crosses a peridoic BC so that PowerVIZ knows not
  //to display a segment for the discontious part of the trajectory
  
  //The caller needs to have already updated the parcel's location with
  //the new position across the periodic bc before calling this
  //function.  

  sPARCEL_STATE parent_parcel(*parcel); //make a temp copy of the parcel for the old location
  asINT32 this_state_index = parent_parcel.relative_timestep_to_state_index(0);
  vcopy(parent_parcel.x[this_state_index], old_position);

  std::vector<PARCEL_STATE> child_parcels(1, parcel);
  std::vector<PARTICLE_EVENT_TYPE> child_source_flags(1, EVENT_ENTERED_WINDOW);

  interrupt_trace_due_to_a_discontinuity(ublk, &parent_parcel, EVENT_EXITED_WINDOW, child_parcels, child_source_flags); 
}



VOID sPARTICLE_SIM::measure_trajectory_vertex(UBLK ublk,
                                              PARCEL_STATE parcel,
                                              PARTICLE_EVENT_TYPE event_type,
                                              BOOLEAN force_measurement) {
  if (g_no_trajectory_measurement)
    return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
    TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];

    if(!trajectory_window->include_vertices()) //added for PR42837
      continue;

    if( window_mask & 0x1 << trajectory_window_index) {
      if(trajectory_window->is_active(g_timescale.time_flow(), event_type)) {
        //If this happens to be the first time the parcel has entered this window,
        //determine if it's eligible for measurement and update the visited window flags.
        if(first_time_in_a_window(parcel, trajectory_window_index)) {
          if(compute_eligibility(parcel, trajectory_window_index, trajectory_window->fraction_eligible_for_measurement())) {
            assign_parcel_a_remapable_id(parcel, trajectory_window_index);
            //The particle emitters may call this function with event_type equal to EVENT_EMITTER in which case,
            //this is the first time this particle is in this window because it was just created and not because
            //it crossed a window boundary (EVENT_ENTERED_WINDOW is used for the later).  If this is the case, set the event
            //type to EVENT_EMITTER instead of EVENT_ENTERED_WINDOW so that the CP may flag the particle source in the PRM file appropriately.

#if 1
            if(PARCEL_IS_INTERESTING(parcel)) {
              msg_print("Parcel %d:%d (measurable %d, visited windows %d) is having a trace startpoint measured when in ublk %d (trajectory mask %d) at time %ld.",
                        parcel->originating_sp,
                        parcel->id,
                        (int)parcel->parcel_is_measurable_for_trajectory_window,
                        (int)parcel->visited_trajectory_windows,
                        ublk->id(),
                        (int)ublk->p_data()->s.window_mask,
                        g_timescale.m_time);
            }
#endif



            trajectory_window->record_startpoint( parcel, event_type == EVENT_EMITTED ? EVENT_EMITTED : EVENT_ENTERED_WINDOW, NULL); //write startpoint with no parent since this particle just entered.
          } else
            continue; //stop doing anything else for this window if the parcel isn't measurable
        }

        //If it's not this parcels first time in the window, stop doing anything if the parcel was marked unmeasurable at a previous time.
        if(!parcel_is_measurable(parcel, trajectory_window_index) )
          continue; //stop doing anything else for this parcel in this window.


        //Check if this vertex should be decimated.
        asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
        if(!force_measurement) { //Never decimate if force_measurement is true.
          if(trajectory_window->use_dynamic_decimation()) { //Then use a dynamic decimation rate.
            //Check if the new position is derivable from the last position and
            //velocity within some user defined tolerance.
            sPARTICLE_VAR projected_measurement_position[N_SPACE_DIMS];
            sPARTICLE_VAR time_since_last_measurement = g_timescale.time_flow() - parcel->last_measured_t;
            vscale(projected_measurement_position, time_since_last_measurement, parcel->last_measured_v);
            vinc(projected_measurement_position, parcel->last_measured_x);

            sPARTICLE_VAR measurement_error[N_SPACE_DIMS];
            vsub(measurement_error, projected_measurement_position, parcel->x[this_timestep_index]);
            sPARTICLE_VAR error_sqrd = vdot(measurement_error, measurement_error);

            //if the projected position is within the tolerance, don't do anything.
            if (error_sqrd < (trajectory_window->dynamic_decimation_tolerance_sqr())
                && time_since_last_measurement < TRAJECTORY_MAX_TIMESTEPS_BETWEEN_VERTICES)
              continue; //Stop further processing for this window.
          } else { //Then use a constant decimation rate:
            if (g_static_trajectory_meas_decimation_rate !=-1) {
              asINT32 time_since_last_measured = g_timescale.time_flow() - parcel->last_measured_t;
              if( time_since_last_measured % trajectory_window->static_decimation_rate() != 0 )
                continue; //Stop further processing for this window.
            }
          }
        }
        //If not, Update what the last measured position and velocity were.
        vcopy(parcel->last_measured_x, parcel->x[this_timestep_index]);
        vcopy(parcel->last_measured_v, parcel->v[this_timestep_index]);
        parcel->last_measured_t = g_timescale.time_flow();

        sPARTICLE_VAR transformed_velocity[N_SPACE_DIMS];
        sPARTICLE_VAR transformed_position[N_SPACE_DIMS];

        transform_velocity_to_global_csys(transformed_velocity,
                                          parcel->v[this_timestep_index],
                                          parcel->x[this_timestep_index],
                                          parcel->lrf(),
                                          ublk->scale());

        transform_position_to_global_csys(transformed_position,
                                          parcel->x[this_timestep_index],
                                          parcel->lrf());

        vmul(transformed_velocity, one_over_lattice_time_correction());

#if 1
            if(PARCEL_IS_INTERESTING(parcel)) {
              msg_print("Parcel %d:%d (measurable %d, visited windows %d) is having a vertex measured when in ublk %d (trajectory mask %d) at time %ld.",
                        parcel->originating_sp,
                        parcel->id,
                        (int)parcel->parcel_is_measurable_for_trajectory_window,
                        (int)parcel->visited_trajectory_windows,
                        ublk->id(),
                        (int)ublk->p_data()->s.window_mask,
                        g_timescale.m_time);
            }
#endif

        trajectory_window->record_vertex(parcel,
                                         transformed_position,
                                         transformed_velocity,
                                         event_type == EVENT_EMITTED ? EVENT_EMITTED : EVENT_VERTEX);
      }
    } else { //Else, the particle is not currently in the this window.
      //But check if the particle was in this window previously and, if it's measurable, create a final vertex with event type EVENT_EXITED_WINDOW.
      TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
      if(has_visited_a_window(parcel, trajectory_window_index)) {
        if(parcel_is_measurable(parcel, trajectory_window_index)) {
          asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);
          sPARTICLE_VAR transformed_velocity[N_SPACE_DIMS];
          sPARTICLE_VAR transformed_position[N_SPACE_DIMS];

          transform_velocity_to_global_csys(transformed_velocity,
                                            parcel->v[this_timestep_index],
                                            parcel->x[this_timestep_index],
                                            parcel->lrf(),
                                            ublk->scale());

          transform_position_to_global_csys(transformed_position,
                                            parcel->x[this_timestep_index],
                                            parcel->lrf());

          vmul(transformed_velocity, one_over_lattice_time_correction());


#if 1
            if(PARCEL_IS_INTERESTING(parcel)) {
              msg_print("Parcel %d:%d (measurable %d, visited windows %d) has left the trajectory window after going to ublk %d (trajectory mask %d) at time %ld.",
                        parcel->originating_sp,
                        parcel->id,
                        (int)parcel->parcel_is_measurable_for_trajectory_window,
                        (int)parcel->visited_trajectory_windows,
                        ublk->id(),
                        (int)ublk->p_data()->s.window_mask,
                        g_timescale.m_time);
            }
#endif

          trajectory_window->record_vertex(parcel, transformed_position, transformed_velocity, EVENT_EXITED_WINDOW);
          //Clear the flag that indicates this particle has been in the window before.
          defenestrate(parcel, trajectory_window_index);
        }
      }
    }
  }
}

VOID sPARTICLE_SIM::measure_trajectory_hitpoint(UBLK ublk,
                                                PARCEL_STATE parcel,
                                                sPARTICLE_VAR fractional_time_of_impact,
                                                asINT32 surface_index,
                                                sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                                sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                                STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                                PARTICLE_EVENT_TYPE event_type) {
  if(g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if( window_mask & 0x1 << trajectory_window_index) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active()) {
          //If this happens to be the first time the parcel has entered this window,
          //determine if it's eligible for measurement and update the visited window flags.
          if(first_time_in_a_window(parcel, trajectory_window_index)) {
            if(compute_eligibility(parcel,
                                   trajectory_window_index,
                                   trajectory_window->fraction_eligible_for_measurement())) {
              assign_parcel_a_remapable_id(parcel, trajectory_window_index);
              //Write a parentless startpoint since this particle just entered.
              trajectory_window->record_startpoint(parcel, EVENT_ENTERED_WINDOW, NULL);
            }
          }

          switch(trajectory_window->hitpoint_options()) {
          case HIT_POINTS_NONE:
            break;
          case HIT_POINTS_ONLY_ELIGIBLE:
            if (!parcel_is_measurable(parcel, trajectory_window_index))
              break;
          case HIT_POINTS_ALL_HITS:
            {
              sPARTICLE_VAR transformed_position[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_impact_vel[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_rebound_vel[N_SPACE_DIMS];
              asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);

              transform_velocity_to_global_csys(transformed_impact_vel,
                                                impact_velocity,
                                                parcel->x[this_timestep_index],
                                                parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_impact_vel, one_over_lattice_time_correction());
              transform_velocity_to_global_csys(transformed_rebound_vel,
                                                rebound_velocity,
                                                parcel->x[this_timestep_index],
                                                parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_rebound_vel, one_over_lattice_time_correction());
              transform_position_to_global_csys(transformed_position,
                                                parcel->x[this_timestep_index],
                                                parcel->lrf());


              if(!parcel_is_measurable(parcel, trajectory_window_index)) {
                //Need to assign a remapable id if the particle is not elligible but is being measured anyways due to the hitpoint options
                assign_parcel_a_remapable_id(parcel, trajectory_window_index);
                //trajectory_window->record_startpoint(parcel, EVENT_ENTERED_WINDOW, NULL);
                trajectory_window->record_startpoint(parcel, event_type, NULL); // This was changed from the above for PR49893.
                mark_as_having_visited_window(parcel, trajectory_window_index);
              }

              if(trajectory_window->include_vertices()) { //added for PR42837
                trajectory_window->record_vertex(parcel,
                                                 transformed_position,
                                                 transformed_impact_vel,
                                                 EVENT_VERTEX);
              }

              trajectory_window->record_hitpoint(parcel,
                                                 transformed_position,
                                                 fractional_time_of_impact,
                                                 surface_index,
                                                 transformed_impact_vel,
                                                 transformed_rebound_vel,
                                                 surface_normal,
                                                 event_type);
            }
            break;
          default:
            break;
          }

        }
      }
    }
  }
}

//The following is similar to measuring a normal hitpoint except that the particle will continue in the simulation after
//this and therefor must be reasigned a new id. A startpoint must be generated with the new id and the parent
//needs to be specified using the parcel's origional ID. Also the hitpoint must be created with the child parcel
//specified. Lastly (see PR40490), a vertex measurement must be forced for the child with the same coordinate as the
//parent's hitpoint to prevent gaps in the trace when visualized in PowerVIZ.
VOID sPARTICLE_SIM::measure_trajectory_screen_passthrough(UBLK ublk,
                                                          PARCEL_STATE parcel,
                                                          sPARTICLE_VAR fractional_time_of_impact,
                                                          asINT32 surface_index,
                                                          sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                                          sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                                          sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                                          STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                                          PARTICLE_EVENT_TYPE event_type,
                                                          dFLOAT override_measurable_fraction) {
  if (g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if( window_mask & 0x1 << trajectory_window_index) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active()) {

          dFLOAT measured_fraction = trajectory_window->fraction_eligible_for_measurement();
          if(override_measurable_fraction > 0) {
            measured_fraction = override_measurable_fraction;
            if(!parcel_is_measurable(parcel, trajectory_window_index)) {
              if(!first_time_in_a_window(parcel, trajectory_window_index)) {
                //If the parcel is already in the window but wasnt chosen for measure, and we want to override the measured fraction:
                // then mark the parcel as not having visited the window so its eligibility will be recomputed below.
                defenestrate(parcel, trajectory_window_index);
              }
            }
          }

          //If this happens to be the first time the parcel has entered this window,
          //determine if it's eligible for measurement and update the visited window flags.
          if(first_time_in_a_window(parcel, trajectory_window_index)) {
            if(compute_eligibility(parcel, trajectory_window_index, measured_fraction)) {
              assign_parcel_a_remapable_id(parcel, trajectory_window_index);
              trajectory_window->record_startpoint( parcel, EVENT_ENTERED_WINDOW, NULL); //write a parentless startpoint since this particle just entered.
            }
          }

          switch(trajectory_window->hitpoint_options()) {
          case HIT_POINTS_NONE:
            break;
          case HIT_POINTS_ONLY_ELIGIBLE:
            if (!parcel_is_measurable(parcel, trajectory_window_index))
              break;
          case HIT_POINTS_ALL_HITS:
            {
              sPARTICLE_VAR transformed_position[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_impact_vel[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_rebound_vel[N_SPACE_DIMS];
              asINT32 this_timestep_index = parcel->relative_timestep_to_state_index(0);

              transform_velocity_to_global_csys(transformed_impact_vel,
                                                impact_velocity,
                                                parcel->x[this_timestep_index],
                                                parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_impact_vel, one_over_lattice_time_correction());
              transform_velocity_to_global_csys(transformed_rebound_vel,
                                                rebound_velocity,
                                                parcel->x[this_timestep_index],
                                                parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_rebound_vel, one_over_lattice_time_correction());
              transform_position_to_global_csys(transformed_position,
                                                impact_position,//parcel->x[this_timestep_index],
                                                parcel->lrf());

              sPARCEL_STATE parent_parcel; //Make a temporary copy of the parcel to be used as the "parent".
              memcpy(&parent_parcel, parcel, sizeof(sPARCEL_STATE));

              //If the parent is measurable, then the original parcel needs to be included in the children list to connect to its downstream trace.
              //Also the child parcel (the origional parcel object) needs a new ID to continue under.
              if(parcel_is_measurable(&parent_parcel, trajectory_window_index)) {
                std::vector<PARCEL_STATE> child_parcels;
                std::vector<PARTICLE_EVENT_TYPE> child_source_flags;

                assign_parcel_a_remapable_id(parcel, trajectory_window_index);
                child_parcels.push_back(parcel);
                child_source_flags.push_back(event_type);
                //Record a startpoint for the "child" parcel.
                trajectory_window->record_startpoint(parcel, event_type, &parent_parcel);
                //Record a first vertex for the child parcel.
                if(trajectory_window->include_vertices()) {
                  trajectory_window->record_vertex(parcel,
                                                   transformed_position,
                                                   transformed_impact_vel,
                                                   EVENT_VERTEX);

                  //record a final vertex for the parent parcel
                  trajectory_window->record_vertex(&parent_parcel,
                                                   transformed_position,
                                                   transformed_impact_vel,
                                                   EVENT_VERTEX);
                }
                //Record the hitpoint including the children references
                trajectory_window->record_hitpoint_with_children(&parent_parcel,
                                                                 transformed_position,
                                                                 fractional_time_of_impact,
                                                                 surface_index,
                                                                 transformed_impact_vel,
                                                                 transformed_rebound_vel,
                                                                 surface_normal,
                                                                 event_type,
                                                                 child_parcels,
                                                                 child_source_flags);


              } else { //Then measuring a hitpoint for a parcel that would not otherwise be measurable;  there is no need to record the child or any final vertices.

                //Need to assign a remapable id if the particle is not elligible but is being measured anyways due to the hitpoint options
                assign_parcel_a_remapable_id(&parent_parcel, trajectory_window_index);
                //trajectory_window->record_startpoint(&parent_parcel, EVENT_ENTERED_WINDOW, NULL);
                trajectory_window->record_startpoint(&parent_parcel, event_type, NULL); // This was changed from the above for PR49893.
                mark_as_having_visited_window(&parent_parcel, trajectory_window_index);
                trajectory_window->record_hitpoint(&parent_parcel,
                                                   transformed_position,
                                                   fractional_time_of_impact,
                                                   surface_index,
                                                   transformed_impact_vel,
                                                   transformed_rebound_vel,
                                                   surface_normal,
                                                   event_type);
              }
              parent_parcel.dynamics_type = ABOUT_TO_BE_DELETED; //this indicates to the destructor that the destruction is on purpose.
            }
            break;
          default:
            break;
          }
        }
      }
    }
  }
}

VOID sPARTICLE_SIM::measure_trajectory_reflection(UBLK ublk,
                                                  PARCEL_STATE parcel,
                                                  sPARTICLE_VAR fractional_time_of_impact,
                                                  asINT32 surface_index,
                                                  sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                                  sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                                  sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                                  STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                                  PARTICLE_EVENT_TYPE event_type,
                                                  dFLOAT override_measurable_fraction) {
  //If a solid particle reflects, A hitpoint is needed but the trace must continue. The reflected particle is recorded
  //as the child of itself so that powerviz can continue the trace through a reflection event.  This is identical to
  //the procedure needed for recording hitpoints on screens so measure_trajectory_screen_passthrough is used here
  //internally.
  measure_trajectory_screen_passthrough(ublk,
                                        parcel,
                                        fractional_time_of_impact,
                                        surface_index,
                                        impact_position,
                                        impact_velocity,
                                        rebound_velocity,
                                        surface_normal,
                                        event_type,
                                        override_measurable_fraction);
}

VOID sPARTICLE_SIM::measure_trajectory_splash(UBLK ublk,
                                              PARCEL_STATE parent_parcel,
                                              sPARTICLE_VAR fractional_time_of_impact,
                                              asINT32 surface_index,
                                              sPARTICLE_VAR impact_position[N_SPACE_DIMS],
                                              sPARTICLE_VAR impact_velocity[N_SPACE_DIMS],
                                              sPARTICLE_VAR rebound_velocity[N_SPACE_DIMS],
                                              STP_GEOM_VARIABLE surface_normal[N_SPACE_DIMS],
                                              PARTICLE_EVENT_TYPE event_type,
                                              std::vector<PARCEL_STATE> &child_parcels,
                                              std::vector<PARTICLE_EVENT_TYPE> &child_source_flags,
                                              dFLOAT override_measurable_fraction) {
  if(g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if( window_mask & 0x1 << trajectory_window_index) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active()) {

          dFLOAT measured_fraction = trajectory_window->fraction_eligible_for_measurement();
          if(override_measurable_fraction > 0) {
            measured_fraction = override_measurable_fraction;
            if(!parcel_is_measurable(parent_parcel, trajectory_window_index)) {
              if(!first_time_in_a_window(parent_parcel, trajectory_window_index)) {
                //If the parcel is already in the window but wasnt chosen for measure, and we want to override the measured fraction:
                // then mark the parcel as not having visited the window so its eligibility will be recomputed below.
                defenestrate(parent_parcel, trajectory_window_index);
              }
            }
          }
         

          //If this happens to be the first time the parent parcel has entered this window,
          //determine if it's eligible for measurement and update the visited window flags.
          if(first_time_in_a_window(parent_parcel, trajectory_window_index)) {
            if(compute_eligibility(parent_parcel, trajectory_window_index, measured_fraction)) {
              assign_parcel_a_remapable_id(parent_parcel, trajectory_window_index);
              trajectory_window->record_startpoint( parent_parcel, EVENT_ENTERED_WINDOW, NULL); //write a parentless startpoint since this particle just entered.
            } else {
              //If the parent isn't measureable, none of the children should be.  So mark them as having entered this window and set their eligibility flag.
              ccDOTIMES(child_index, child_parcels.size()) {
                make_parcel_unmeasurable(child_parcels[child_index], trajectory_window_index);
                mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              }
              //must continue processing for this window even if the parent isn't measureable because a hit point might be needed depending on the hit point options.
            }
          }

          //Depending if the parent parcel is measurable, ensure each child's eligibiltiy and window visitation history is set appropriately.
          if(!parcel_is_measurable(parent_parcel, trajectory_window_index)) {
            ccDOTIMES(child_index, child_parcels.size()) {
              //Mark all children as unmeasurable since the parent was ineligibile.
              mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              make_parcel_unmeasurable(child_parcels[child_index], trajectory_window_index);
            }
          } else {
            ccDOTIMES(child_index, child_parcels.size()) {
              mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              //Mark all children as measurable since the parent was eligibile.
              make_parcel_measurable(child_parcels[child_index], trajectory_window_index);
              assign_parcel_a_remapable_id(child_parcels[child_index], trajectory_window_index);
              //Write a single-parent startpoint for this child.
              //(This will record the parent info about this splashed child)
              trajectory_window->record_startpoint(child_parcels[child_index], child_source_flags[child_index], parent_parcel);
            }
          }


          switch(trajectory_window->hitpoint_options()) {
          case HIT_POINTS_NONE:
            break;
          case HIT_POINTS_ONLY_ELIGIBLE:
            if ( !parcel_is_measurable(parent_parcel, trajectory_window_index))
              break;
          case HIT_POINTS_ALL_HITS:
            {

              sPARTICLE_VAR transformed_position[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_impact_vel[N_SPACE_DIMS];
              sPARTICLE_VAR transformed_rebound_vel[N_SPACE_DIMS];
              asINT32 this_timestep_index = parent_parcel->relative_timestep_to_state_index(1);

              transform_velocity_to_global_csys(transformed_impact_vel,
                                                impact_velocity,
                                                impact_position,//parent_parcel->x[this_timestep_index],
                                                parent_parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_impact_vel, one_over_lattice_time_correction());

              transform_velocity_to_global_csys(transformed_rebound_vel,
                                                rebound_velocity,
                                                impact_position, //parent_parcel->x[this_timestep_index],
                                                parent_parcel->lrf(),
                                                ublk->scale());
              vmul(transformed_rebound_vel, one_over_lattice_time_correction());

              transform_position_to_global_csys(transformed_position,
                                                impact_position,// parent_parcel->x[this_timestep_index],
                                                parent_parcel->lrf());

              //If the parcel is measurable, send the full inheritance information along with the hitpoint data.
              if(parcel_is_measurable(parent_parcel, trajectory_window_index)) {

                if(trajectory_window->include_vertices()) {
                  //Write the last vertex for the parent.
                  trajectory_window->record_vertex(parent_parcel,
                                                   transformed_position,
                                                   transformed_impact_vel,
                                                   EVENT_VERTEX);


                  //write the first vertex for each child:
                  ccDOTIMES(child_index, child_parcels.size()) {
                    trajectory_window->record_vertex(child_parcels[child_index],
                                                     transformed_position,
                                                     transformed_impact_vel,
                                                     EVENT_VERTEX);
                  }
                }
                //Write a hitpoint and record the info about children.
                //(This hitpoint adds the child info for the parent while the startpoint recorded the parent info for the child)
                trajectory_window->record_hitpoint_with_children(parent_parcel,
                                                                 transformed_position,
                                                                 fractional_time_of_impact,
                                                                 surface_index,
                                                                 transformed_impact_vel,
                                                                 transformed_rebound_vel,
                                                                 surface_normal,
                                                                 event_type,
                                                                 child_parcels,
                                                                 child_source_flags); //child source flags are redundant here because start points were already created with the correct flags
              } else {
                //If not, just send the hitpoint info without recording the child.
                //But a remapable ID needs to be assigned and a startpoint needs to be recorded.
                assign_parcel_a_remapable_id(parent_parcel, trajectory_window_index);
                //trajectory_window->record_startpoint( parent_parcel, EVENT_ENTERED_WINDOW, NULL);
                trajectory_window->record_startpoint(parent_parcel, event_type, NULL); // This was changed from the above for PR49893.
                mark_as_having_visited_window(parent_parcel, trajectory_window_index);

                trajectory_window->record_hitpoint(parent_parcel,
                                                   transformed_position,
                                                   fractional_time_of_impact,
                                                   surface_index,
                                                   transformed_impact_vel,
                                                   transformed_rebound_vel,
                                                   surface_normal,
                                                   event_type);
              }
            }
            break;
          default:
            break;
          }
        }
      }
    }
  }
}

VOID sPARTICLE_SIM::measure_trajectory_split_event(UBLK ublk,
                                                   PARCEL_STATE parent_parcel,
                                                   PARTICLE_EVENT_TYPE event_type,
                                                   std::vector<PARCEL_STATE> &child_parcels,
                                                   std::vector<PARTICLE_EVENT_TYPE> &child_source_flags) {
  if(g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if((window_mask >> trajectory_window_index) & 0x1) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active()) {
          //If this happens to be the first time the parcel has entered this window,
          //determine if it's eligible for measurement and update the visited window flags.
          if(first_time_in_a_window(parent_parcel, trajectory_window_index)) {
            if(compute_eligibility(parent_parcel, trajectory_window_index, trajectory_window->fraction_eligible_for_measurement())) {
              assign_parcel_a_remapable_id(parent_parcel, trajectory_window_index);
              trajectory_window->record_startpoint( parent_parcel, EVENT_ENTERED_WINDOW, NULL); //write a parentless startpoint since this particle just entered (NULL for no know neighbor) .
            } else {
              //If the parent isn't measureable, none of the children should be.  So mark them as having entered this window and set their eligibility flag.
              ccDOTIMES(child_index, child_parcels.size()) {
                make_parcel_unmeasurable(child_parcels[child_index], trajectory_window_index);
                mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              }
              continue; //Stop doing anything for this window if the parent isn't measurable.
            }
          }

          //If its not the parent parcel's first time in the window, it may be measurable.  Set the child parcel visitation history appropriatly
          if(!parcel_is_measurable(parent_parcel, trajectory_window_index)) {
            //mark all children unmeasurable
            ccDOTIMES(child_index, child_parcels.size()) {
              mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              make_parcel_unmeasurable(child_parcels[child_index], trajectory_window_index);
            }
          } else {
            //Since the parent is measurable, all of the children need to be marked measurable and have their appropriate flags set
            ccDOTIMES(child_index, child_parcels.size()) {
              mark_as_having_visited_window(child_parcels[child_index], trajectory_window_index);
              make_parcel_measurable(child_parcels[child_index], trajectory_window_index);
              assign_parcel_a_remapable_id(child_parcels[child_index], trajectory_window_index); //assume this is the firs time any measurements have been attempted for the child.
              trajectory_window->record_startpoint(child_parcels[child_index], child_source_flags[child_index], parent_parcel); //Write a single parent startpoint for this child.
            }
            trajectory_window->record_multiple_children(parent_parcel, event_type, child_parcels, child_source_flags); //Child source flags are redundant here because startpoints were created above. And furthermore, this routine is only called due to the splitting of the agglomeration routine (and not splash or breakup) so all the sources should be MERGE.
          }

        }
      }
    }
  }
}

VOID sPARTICLE_SIM::measure_trajectory_merge_event(UBLK ublk,
                                                   PARCEL_STATE child_parcel,
                                                   PARTICLE_EVENT_TYPE event_type,
                                                   std::vector<PARCEL_STATE> &parent_parcels) {
  if(g_no_trajectory_measurement) return;
  uINT64 window_mask = ublk->p_data()->s.window_mask;
  if(window_mask !=0) {
    ccDOTIMES(trajectory_window_index, g_trajectory_windows.size()) {
      if((window_mask >> trajectory_window_index) & 0x1) {
        TRAJECTORY_WINDOW trajectory_window = g_trajectory_windows[trajectory_window_index];
        if(trajectory_window->is_active()) {


          //Scan each parent and see if any are in this window for the first time (also make a filtered vector of the the measurable ones)
          std::vector<PARCEL_STATE> measurable_parent_parcels;
          measurable_parent_parcels.reserve(parent_parcels.size());
          ccDOTIMES(parent_index, parent_parcels.size()) {

            if(first_time_in_a_window(parent_parcels[parent_index], trajectory_window_index)) {
              if(compute_eligibility(parent_parcels[parent_index], trajectory_window_index, trajectory_window->fraction_eligible_for_measurement())) {
                assign_parcel_a_remapable_id(parent_parcels[parent_index], trajectory_window_index);
                trajectory_window->record_startpoint(parent_parcels[parent_index], EVENT_ENTERED_WINDOW, NULL); //Write a startpoint for this parent if it's its first time being measured.
              }
            }

            if(parcel_is_measurable(parent_parcels[parent_index], trajectory_window_index))
              measurable_parent_parcels.push_back(parent_parcels[parent_index]);
          }

          if (measurable_parent_parcels.size() == 0) {
            //If none of the parents were measurable, don't measure the child.
            make_parcel_unmeasurable(child_parcel, trajectory_window_index);
            mark_as_having_visited_window(child_parcel, trajectory_window_index);
            continue;
          } else {
            make_parcel_measurable(child_parcel, trajectory_window_index); //If any one parent was measurable, make the child measurable.
            mark_as_having_visited_window(child_parcel, trajectory_window_index);
            assign_parcel_a_remapable_id(child_parcel, trajectory_window_index);
          }

          //Create a startpoint for the merged child and send the list of ID's of the parents that were measurable.
          trajectory_window->record_multiple_parents(child_parcel, event_type, measurable_parent_parcels); //no vector of states are needed for the parents becasue this function is only used for the agglomeration algorithm so all parents are killed with state MERGE}
          if(trajectory_window->include_vertices()) {
            //Measure a first vertex for the child parcel becasue PowerVIZ expects every parcel in the cPARTICLE_TABLE to
            //have at least one vertex (or hitpoint I assume) and there is no guarantee this parcel will produce one later (PR42835).
            asINT32 this_timestep_index = child_parcel->relative_timestep_to_state_index(0);
            sPARTICLE_VAR transformed_velocity[N_SPACE_DIMS];
            sPARTICLE_VAR transformed_position[N_SPACE_DIMS];
            transform_velocity_to_global_csys(transformed_velocity,
                                              child_parcel->v[this_timestep_index],
                                              child_parcel->x[this_timestep_index],
                                              child_parcel->lrf(),
                                              ublk->scale());
            transform_position_to_global_csys(transformed_position,
                                              child_parcel->x[this_timestep_index],
                                              child_parcel->lrf());
            trajectory_window->record_vertex(child_parcel, transformed_position, transformed_velocity, EVENT_VERTEX);
          }
        }
      }
    }
  }
}
