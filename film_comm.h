#ifndef FILM_COMM_H_
#define FILM_COMM_H_
#include "common_sp.h"
#include "particle_solver_data.h"
#include "surfel.h"
#include "surfel_vertices.h"
#include <vector>
#include "comm_groups.h"
#include "simulator_namespace.h"
#include "particle_sim_info.h"

//Define some vectors that are needed for for sufel p_data blocks.
//Vector of references to other surfels in a surfel's stencil:

//This structure is used at initialization to complete surfel neighborhood lists that span SP interfaces.
typedef struct sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT {
  SHOB_ID id;
  sINT8 scale;
  sINT32 source_sp;
  auINT32 even_odd;
  STP_GEOM_VARIABLE centroid[3];
  STP_GEOM_VARIABLE normal[3];
  STP_GEOM_VARIABLE area;
  sINT16 n_vertices;
  BOOLEAN is_backside;
  SHOB_ID clone_surfel_id;
  sINT32 face_index;
  SHOB_ID opposite_surfel_id;
  sINT32 surface_material_id;

  VOID print() {
    printf("id %d scale %d source_sp %d centroid %g %g %g normal %g %g %g area %g n_vert %d is_backside %d.\n",
           id,scale,source_sp,
           centroid[0],
           centroid[1],
           centroid[2],
           normal[0],
           normal[1],
           normal[2],
           area,n_vertices,is_backside);
  }
  
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT() {
    id = -1;
    scale = 0;
    source_sp = my_proc_id;
    even_odd = 0;
    vzero(centroid);
    vzero(normal);
    area = 0;
    n_vertices = 0;
    is_backside = FALSE;
    clone_surfel_id = INVALID_SHOB_ID;
    opposite_surfel_id = INVALID_SHOB_ID;
    face_index = -1;
    surface_material_id = -1;
  }

   //Constructor to set values obtained from a surfel.
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT(sSURFEL* surfel) {
    id = surfel->id();
    scale = surfel->scale();
    source_sp = surfel->m_home_sp;
    even_odd = surfel->even_odd_mask();
    vcopy(centroid, surfel->centroid);
    vcopy(normal, surfel->normal);
    area = surfel->area;
    n_vertices = surfel->p_data()->n_vertices;
    is_backside = surfel->is_backside();
    face_index = surfel->m_face_index;
    clone_surfel_id = (surfel->clone_surfel() == nullptr) ? INVALID_SHOB_ID : surfel->clone_surfel()->id();
    opposite_surfel_id = (surfel->m_opposite_surfel == nullptr) ? INVALID_SHOB_ID : surfel->m_opposite_surfel->id();
    surface_material_id = surfel->p_data()->s.surface_material_id;
  }
#else
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT(sSURFEL* surfel) {
    id = surfel->id();
    scale = surfel->scale();
    source_sp = surfel->m_home_sp;
    even_odd = surfel->m_even_odd;
    vcopy(centroid, surfel->centroid);
    vcopy(normal, surfel->normal);
    area = surfel->area;
    n_vertices = surfel->stencil()->n_vertices;
    is_backside = surfel->is_backside();
    face_index = surfel->m_face_index;
    clone_surfel_id = (surfel->clone_surfel() == nullptr) ? INVALID_SHOB_ID : surfel->clone_surfel()->id();
    opposite_surfel_id = (surfel->m_opposite_surfel == nullptr) ? INVALID_SHOB_ID : surfel->m_opposite_surfel->id();
  }
#endif

  //Constructor to set the values from some other sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT.
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT(sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT* init) {
    this->id = init->id;
    this->scale = init->scale;
    this->source_sp = init->source_sp;
    this->even_odd = init->even_odd;
    vcopy(this->centroid, init->centroid);
    vcopy(this->normal, init->normal);
    this->area = init->area;
    this->n_vertices = init->n_vertices;
    this->is_backside = init->is_backside;
    this->face_index = init->face_index;
    this->clone_surfel_id = init->clone_surfel_id;
    this->opposite_surfel_id = init->opposite_surfel_id;
    this->surface_material_id = init->surface_material_id;
  }

  VOID copy_to_surfel(sSURFEL* surfel);

} *FILM_ONLY_GHOST_SURFEL_INIT_ELEMENT;

typedef std::vector<sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT> sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT_VECTOR;

typedef struct sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT {
  

  DGF_VERTEX_INDEX global_index;
 
  //default constructor
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT() {
    global_index = 0;
  }

  //Construct from another sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT element
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT(sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT* init) {
    global_index = init->global_index;
  }

  //Construct given a local vertex id.
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT(DGF_VERTEX_INDEX local_id);
   
#if 0
  struct compare : public std::unary_function<sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT, bool>
  {
    explicit compare(const sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT &baseline) : baseline(baseline) {}
    bool operator() (const sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT &arg)
    { 
      return (arg.global_index == baseline.global_index); 
    }
    sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT baseline;
  };
#endif
  
}* FILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT;

typedef std::vector<sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT> sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_VECTOR;


typedef struct sFO_SURFEL_INIT_DATA_COLLECTION : sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT
{
  sFO_SURFEL_INIT_DATA_COLLECTION(FILM_ONLY_GHOST_SURFEL_INIT_ELEMENT init) : sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT(init) {
  }
  //Carry the vertex info with each surfel init element so that unused vertices are culled
  //automatically along with the redundant surfels that used them.
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_VECTOR vertices;

  VOID print(){
    sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT::print();
    ccDOTIMES(vertex, vertices.size()){
      printf("vertex %d is %d\n", vertex, vertices[vertex].global_index);
    }
  }
}* FO_SURFEL_INIT_DATA_COLLECTION;


typedef class sFILM_GROUP {

public:
  //These variables are used to reconcile, during initialization, surfel neighborhoods which span SP domains.
  //Film-only ghost surfels and the corresponding comm send and receive groups are created using the following.
  std::list<sFO_SURFEL_INIT_DATA_COLLECTION> culled_surfel_list;
  asINT32 outbound_surfel_count;
  asINT32 outbound_surfel_vertex_count;
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT_VECTOR outbound_surfel_data;
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_VECTOR outbound_surfel_vertex_data;
  MPI_Request outbound_surfel_count_send_request;
  MPI_Request outbound_surfel_data_send_request;
  MPI_Request outbound_surfel_vertex_data_send_request;

  asINT32 inbound_surfel_count;
  asINT32 inbound_surfel_vertex_count;
  sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT_VECTOR inbound_surfel_data;
  sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_VECTOR inbound_surfel_vertex_data;
  MPI_Request inbound_surfel_count_recv_request;
  MPI_Request inbound_surfel_data_recv_request;
  MPI_Request inbound_surfel_vertex_data_recv_request;

  // This count represents the quantums transferred from the SURFEL_RECV_GROUP
  // via copy_quantums.
  asINT32 m_n_initial_quantums;
  asINT32 n_initial_quantums() {
    return m_n_initial_quantums;
  }
  sFILM_GROUP() {
    outbound_surfel_count_send_request = MPI_REQUEST_NULL;
    outbound_surfel_data_send_request = MPI_REQUEST_NULL;
    outbound_surfel_vertex_data_send_request = MPI_REQUEST_NULL;
    inbound_surfel_count_recv_request = MPI_REQUEST_NULL;
    inbound_surfel_data_recv_request = MPI_REQUEST_NULL;
    inbound_surfel_vertex_data_recv_request = MPI_REQUEST_NULL;
    outbound_surfel_count = 0;
    outbound_surfel_vertex_count = 0;
    inbound_surfel_count = 0;
    inbound_surfel_vertex_count = 0;
    m_n_initial_quantums = 0;
  }
  size_t inbound_surfel_data_size() {
    return inbound_surfel_count*sizeof(sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT);
  }
  size_t inbound_surfel_vertex_data_size() {
    return inbound_surfel_vertex_count*sizeof(sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT);
  }
  size_t outbound_surfel_data_size() {
    return outbound_surfel_count*sizeof(sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT);
  }
  size_t outbound_surfel_vertex_data_size() {
    return outbound_surfel_vertex_count*sizeof(sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT);
  }
  VOID fill_outbound_surfels(sFILM_STENCIL_SURFEL_VECTOR &nearby_surfel_references) {
    outbound_surfel_count = nearby_surfel_references.size();
    outbound_surfel_data.clear();
    outbound_surfel_data.reserve(outbound_surfel_count);
    ccDOTIMES(nth_reference, outbound_surfel_count) {
#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES
      sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT data(nearby_surfel_references[nth_reference]);//= &group->outbound_surfel_data[nth_reference];
#else
      sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT data(nearby_surfel_references[nth_reference].get_surfel());//= &group->outbound_surfel_data[nth_reference];
#endif
      outbound_surfel_data.push_back(data);
    }
  }

  VOID fill_outbound_surfels() {
    outbound_surfel_count = culled_surfel_list.size();
    outbound_surfel_data.clear();
    outbound_surfel_data.reserve(outbound_surfel_count);

    std::list<sFO_SURFEL_INIT_DATA_COLLECTION>::iterator i = culled_surfel_list.begin();
    while(i != culled_surfel_list.end()) {
      sFILM_ONLY_GHOST_SURFEL_INIT_ELEMENT element(*i);
      outbound_surfel_data.push_back(element);
      i = culled_surfel_list.erase(i);
    }
  }

#ifdef CONDUCTION_USE_OLD_STENCIL_CONSTRUCTION_FOR_PARTICLES 
  VOID fill_outbound_vertices(sFILM_STENCIL_SURFEL_VECTOR &nearby_surfel_references) {
    //Allocate and fill a send buffer of vertex IDs.
    outbound_surfel_vertex_data.clear();
    ccDOTIMES(nth_surfel, outbound_surfel_count) {
      SURFEL surfel = nearby_surfel_references[nth_surfel];
      asINT32 n_vertices = surfel->p_data()->n_vertices;
      asINT32 surfel_first_vertex_index = surfel->p_data()->first_vertex_index;
      ccDOTIMES(nth_surfel_vertex, n_vertices) {
        DGF_VERTEX_INDEX local_vertex_id = surfel_first_vertex_index + nth_surfel_vertex;
        outbound_surfel_vertex_data.push_back(sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT(local_vertex_id));
      }
    }
    outbound_surfel_vertex_count = outbound_surfel_vertex_data.size();
  }
#else
  VOID fill_outbound_vertices(sFILM_STENCIL_SURFEL_VECTOR &nearby_surfel_references) {
    //Allocate and fill a send buffer of vertex IDs.
    outbound_surfel_vertex_data.clear();
    ccDOTIMES(nth_surfel, outbound_surfel_count) {
      SURFEL surfel = nearby_surfel_references[nth_surfel].get_surfel();
      asINT32 n_vertices = surfel->stencil()->n_vertices;
      asINT32 surfel_first_vertex_index = surfel->stencil()->first_vertex_index;
      ccDOTIMES(nth_surfel_vertex, n_vertices) {
        DGF_VERTEX_INDEX local_vertex_id = surfel_first_vertex_index + nth_surfel_vertex;
        outbound_surfel_vertex_data.push_back(sFILM_ONLY_GHOST_SURFEL_VERTEX_DATA_ELEMENT(local_vertex_id));
      }
    }
    outbound_surfel_vertex_count = outbound_surfel_vertex_data.size();
  }
#endif

  VOID allocate_inbound_vertices() {

    asINT32 num_total_vertices = 0;
    ccDOTIMES(nth_element, inbound_surfel_count) {
      num_total_vertices += inbound_surfel_data[nth_element].n_vertices;
    }
    inbound_surfel_vertex_count = num_total_vertices;
    inbound_surfel_vertex_data.resize(num_total_vertices);
  }
} *FILM_GROUP;

typedef class sSURFEL_FILM_SEND_GROUP : public sSURFEL_SEND_GROUP, public sFILM_GROUP {

public:

  sSURFEL_FILM_SEND_GROUP() { }


  sSURFEL_FILM_SEND_GROUP(SCALE scale, cNEIGHBOR_SP  dest_sp) {

    m_scale = scale;
    m_solver_timestep_index_mask = 1;
    m_dest_sp = dest_sp;
    m_send_msg.init(my_proc_id, dest_sp.rank());
    int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL_FILM>(scale);
    m_send_msg.settag(tag);
    m_send_type = FILM_SEND_TYPE;
  }

  sSURFEL_FILM_SEND_GROUP(const sSURFEL_FILM_SEND_GROUP&) = delete;

  VOID copy_quantums(sSURFEL_RECV_GROUP *rgroup) {
    for(auto& quantum: rgroup->quantums()) {
      m_quantums.push_back(quantum);
    }
    m_n_initial_quantums = m_quantums.size();
  }

  VOID clear_ghost_surfel_film_accumulators() {
    for(auto& quantum: m_quantums) {
      quantum.m_surfel->p_data()->s.clear_film_accumulators();
    }
  }

  VOID clear_ghost_surfel_meas_accumulators() {
    for(auto& quantum: m_quantums) {
      quantum.m_surfel->p_data()->s.clear_reentrainment_meas_accumulators();
      quantum.m_surfel->p_data()->s.clear_meas_accumulators();
    }
  }

  VOID allocate_film_send_buffer() {
    size_t allocated_sendsize = 0;
    for(auto& quantum: m_quantums) {
      if (quantum.m_surfel->is_inlet_or_outlet()){
        allocated_sendsize += sizeof(sSURFEL_SOLID_PARCEL_COMM_ELEMENT);
      } else {
        allocated_sendsize += sizeof(sSURFEL_FILM_COMM_ELEMENT);
      }
    }

    m_send_msg.allocateBuffer(allocated_sendsize);
    m_sendsize = allocated_sendsize;
  }

  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask) final;

} *SURFEL_FILM_SEND_GROUP;

struct SURFEL_FILM_SEND_GROUP_ORDER {
  BOOLEAN operator() (const SURFEL_FILM_SEND_GROUP a, const SURFEL_FILM_SEND_GROUP b) const
  {
    return (a->m_dest_sp < b->m_dest_sp);
  }
};

typedef struct sSURFEL_FILM_SEND_FSET :
  public tSP_FSET < SURFEL_FILM_SEND_GROUP, SURFEL_FILM_SEND_GROUP_ORDER > {

  public:
  SURFEL_FILM_SEND_GROUP find_send_group(asINT32 scale, cNEIGHBOR_SP dest_sp) {
    static sSURFEL_FILM_SEND_GROUP signature;
    signature.m_scale = scale;
    signature.m_dest_sp = dest_sp;
    return (this->find_group(&signature));
  }

  SURFEL_FILM_SEND_GROUP create_group (asINT32 scale, cNEIGHBOR_SP dest_sp) {

    SURFEL_FILM_SEND_GROUP group = find_send_group(scale, dest_sp);
    if (NULL == group) {
      if (sim.is_particle_model) {
        group = xnew sSURFEL_FILM_SEND_GROUP(scale, dest_sp);
      } else {
        msg_internal_error("Film only sets only needed in particle modeling");
      }
      add_group(group);
    }
    return group;
  }
} *SURFEL_FILM_SEND_FSET;

extern sSURFEL_FILM_SEND_FSET g_surfel_film_send_fset;
#define DO_SURFEL_FILM_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_FILM_SEND_FSET, g_surfel_film_send_fset, group_var, scale)

#define DO_SURFEL_FILM_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sSURFEL_FILM_SEND_FSET, g_surfel_film_send_fset, group_var)

typedef class sSURFEL_FILM_RECV_GROUP : public sSURFEL_RECV_GROUP, public sFILM_GROUP
{
public:

  std::set<SHOB_ID> m_set_of_surfel_ids;

  sSURFEL_FILM_RECV_GROUP():sSURFEL_RECV_GROUP(), sFILM_GROUP() {
    m_set_of_surfel_ids.clear();
  }

  VOID copy_quantums(sSURFEL_SEND_GROUP *sgroup) {
    for (auto& quantum: sgroup->quantums()) {
      m_quantums.push_back(quantum);
    }
    m_n_initial_quantums = m_quantums.size();
  }

  VOID allocate_film_recv_buffer() {
    size_t allocated_sendsize = 0;
    for (auto& quantum: m_quantums) {
      if (quantum.m_surfel->is_inlet_or_outlet()){
        allocated_sendsize += sizeof(sSURFEL_SOLID_PARCEL_COMM_ELEMENT);
      } else {
        allocated_sendsize += sizeof(sSURFEL_FILM_COMM_ELEMENT);
      }
    }

    m_recv_msg.allocateBuffer(allocated_sendsize);
    m_recvsize = allocated_sendsize;
  }

  virtual VOID post_recv() final {
    m_recv_msg.set_nelems(m_recvsize);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) final {
    sdFLOAT *recv_buffer = m_recv_msg.buffer();
    for (auto& quantum: m_quantums) {
      SURFEL surfel = quantum.m_surfel;
      if(sim.is_film_solver && !surfel->is_inlet_or_outlet()){
        surfel->accumulate_film_data(recv_buffer);
      }else{
        surfel->accumulate_solid_parcel_data(recv_buffer);
      }
    }
    set_unpacked();
  }
} *SURFEL_FILM_RECV_GROUP;

struct SURFEL_FILM_RECV_GROUP_ORDER {
  BOOLEAN operator() (const SURFEL_FILM_RECV_GROUP a, const SURFEL_FILM_RECV_GROUP b) const
  {
    return (a->m_source_sp < b->m_source_sp);
  }
};

typedef struct sSURFEL_FILM_RECV_FSET :
        public tSP_FSET < SURFEL_FILM_RECV_GROUP, SURFEL_FILM_RECV_GROUP_ORDER>
{
  public:
  SURFEL_FILM_RECV_GROUP create_group (asINT32 scale, cNEIGHBOR_SP source_sp) {
    static sSURFEL_FILM_RECV_GROUP signature;
    signature.m_scale = scale;
    signature.m_source_sp = source_sp;

    SURFEL_FILM_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      if (sim.is_particle_model) {
        group = xnew sSURFEL_FILM_RECV_GROUP;
      } else {
        msg_internal_error("Film only sets only needed in particle modeling");
      }
      group->m_scale = scale;
      group->m_solver_timestep_index_mask = 1;
      group->m_source_sp = source_sp;
      group->m_recv_msg.init(group->source_rank(), my_proc_id);
      int tag = make_mpi_shob_tag<eMPI_SHOB_TAG::SURFEL_FILM>(scale);
      group->m_recv_msg.settag(tag);
      group->m_recv_type = FILM_RECV_TYPE;
      add_group(group);
    }
    return group;
  }
} *SURFEL_FILM_RECV_FSET;

extern sSURFEL_FILM_RECV_FSET g_surfel_film_recv_fset;

VOID append_connectivity_with_additional_vertices(FO_SURFEL_INIT_DATA_COLLECTION data,
                                                  SURFEL film_only_ghost_surfel);
VOID create_surfel_film_send_and_receive_groups();
VOID init_surfel_film_send_and_recv_groups();

#define DO_SURFEL_FILM_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sSURFEL_FILM_RECV_FSET, g_surfel_film_recv_fset, group_var, scale)

#define DO_SURFEL_FILM_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sSURFEL_FILM_RECV_FSET, g_surfel_film_recv_fset, group_var)


#endif
