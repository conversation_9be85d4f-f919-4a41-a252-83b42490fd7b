/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Diagnostic support for Boltzmann distributions
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_BOLTZ_DIAGS_H
#define _SIMENG_BOLTZ_DIAGS_H

#define SIM_ENABLE_BOLTZ_DIAGS 1

#if SIM_ENABLE_BOLTZ_DIAGS

VOID sim_inc_n_neg_or_states(asINT32 n);

VOID sim_inc_n_neg_eq_states(asINT32 n);

VOID sim_inc_n_overflow_or_states(asINT32 n);

VOID sim_inc_n_overflow_eq_states(asINT32 n);

VOID sim_inc_n_voxels_with_neg_or_state(VOID);

VOID sim_inc_n_voxels_with_neg_eq_state(VOID);

VOID sim_inc_n_voxels_with_overflow_or_state(VOID);

VOID sim_inc_n_voxels_with_overflow_eq_state(VOID);

VOID sim_inc_n_cleanup_rules_executed(asINT32 n_rules_executed);

VOID sim_inc_n_voxels_reverted_to_incremental_drive(VOID);

VOID sim_reset_fluid_dyn_diagnostics(VOID);

VOID sim_report_fluid_dyn_diagnostics(asINT32 avg_over_n_timesteps);

#endif

#endif
