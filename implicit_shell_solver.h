#pragma once

#if !GPU_COMPILER && !BUILD_GPU
#if !BUILD_FOR_SIMSIZES

#include "petscksp.h"
#include "petscvec.h"
#include "petscmat.h"
#include "petscpc.h"
#include "petscerror.h"
#include "petscviewer.h"
#include "simulator_namespace.h"
#include "ublk_process_control.h"
#include "surfel_dyn_sp.h" // Needed for MLRF_SURFEL_GROUPS_OF_SCALE
#include "shob_groups.h" // Needed for SLRF_SURFEL_PAIRS_OF_SCALE
#include "comm_groups.h" // Needed for SURFEL_RECV_GROUPS_OF_SCALE
#include <filesystem>

inline namespace SIMULATOR_NAMESPACE {
extern Mat g_shell_conduction_coeff_mat;
extern Vec g_shell_conduction_rhs_vec;
extern Vec g_shell_conduction_rhs_bc_vec;
extern Vec g_shell_conduction_solution_vec;
extern Vec g_shell_conduction_rho_cp_vec;
extern Vec g_shell_conduction_source_vec;
extern Vec g_shell_conduction_rhs_bc_temp_vec;
extern KSP g_ksp;
extern bool g_implicit_shell_solver_any_cell_bad;
}

// NOTE: These guys can probably be put into a class. Maybe follow the way Dalon did it for his radiation stuff in
// radiation_comm.cc
extern bool g_implicit_solver_active;
extern pthread_mutex_t g_implicit_solver_mutex;
extern pthread_cond_t g_implicit_solver_cond;

extern bool g_implicit_solver_setup_active;
extern bool g_implicit_solver_setup_complete;
extern pthread_mutex_t g_implicit_solver_setup_mutex;
extern pthread_cond_t g_implicit_solver_setup_cond;

//These macros are necessary because the PetscCall function tries to return
//an int, but since our functions are pretty much just all void the compiler
//gets upset.
static void check_throw(PetscErrorCode err, size_t lineno) {
  // These pointers do not need to be freed
  const char * text = nullptr;
  char * specific = nullptr;
  PetscErrorMessage(err, &text, &specific); 
  std::string msg(text);
  msg += "\n";
  msg += specific;
  msg += "\n";
  msg += "line ";
  msg += std::to_string(lineno);
  throw std::runtime_error(msg);
}

#define check(...) \
  do { \
    PetscErrorCode ierr =  __VA_ARGS__; \
    if (ierr != 0) check_throw(ierr, __LINE__); \
  } while(0)

VOID setup_implicit_shell_solver();
int set_state_indices_on_sps();
int set_state_indices_on_sps_internal(SURFEL surfel);
VOID preallocate_matrix();
VOID preallocate_matrix_internal(SURFEL surfel, 
                                 PetscInt first_local_row_global_index, 
                                 PetscInt last_local_row_global_index, 
                                 std::vector<PetscInt> &num_nonzeros_in_diagonal_block, 
                                 std::vector<PetscInt> &num_nonzeros_in_offdiagonal_block);
VOID determine_bad_cells(SURFEL surfel);
VOID assemble_surfel_gradient_coefficients();
VOID assemble_gradient_coefficients(SURFEL surfel);
VOID create_linear_system(int num_states);
VOID initialize_linear_system(int num_states);
VOID assemble_linear_system();
VOID assemble_linear_system_internal(SURFEL surfel);
VOID finalize_assembly(bool first_solve);
VOID finalize_assembly_internal(SURFEL surfel, bool first_solve);
VOID create_solver_objects();
VOID update_linear_system(SURFEL surfel, std::vector<sdFLOAT> sum_fluxes_and_sources_in_layer);
VOID update_system_rhs(SURFEL surfel, const asINT32 prior_index, std::vector<sdFLOAT> sum_fluxes_and_sources_in_layer);
VOID update_system_flux_matrix_and_bcs(SURFEL surfel);
VOID implicit_shell_solver_evolve();
VOID solve_linear_system();
VOID write_linear_system_to_txt();
VOID write_linear_system_to_bin();
VOID implicit_shell_solver_destroy_solver_objects();
VOID start_and_wait_for_implicit_shell_solver();
VOID start_and_wait_for_implicit_shell_solver_setup();
VOID implicit_shell_solver_update_surfel_temperature();
VOID implicit_shell_solver_update_surfel_temperature_internal(SURFEL surfel);

PetscErrorCode MyKSPMonitor(KSP ksp, PetscInt it, PetscReal rnorm, void *ctx);
PetscErrorCode MyPetscVFPrintf(FILE *fd, const char format[], va_list Argp);

void drain_implicit_solver_simerrs();

#endif
#endif
