#include "gpu_host_include.h"

sHMBLK* find_mblk_in_group(MBLK_GROUP group, STP_SHOB_ID debug_ublk_id) {
  auto h_ublk = group->shob_ptrs();
  while(h_ublk && (h_ublk->id() != debug_ublk_id)) { h_ublk = h_ublk->m_next; }
  return  (h_ublk && h_ublk->id() == debug_ublk_id)? h_ublk : nullptr;
}

INLINE VOID validate_state_index(int state_index) {
  if(!(state_index == 0 || state_index == -1)) {
    msg_internal_error("Invalid state_index provided - should be 0 (current) or -1 (prev)");
  }
}

VOID print_mblk(const sHMBLK* h_ublk,
                     int child_ublk_index,
                     int state_index,
                     const char* msg,
                     std::ostream& os) {
  assert(h_ublk);
  validate_state_index(state_index);
  int child_ublk_offset = child_ublk_index * N_VOXELS_8;
  sMBLK* d_ublk_copy = GPU::copy_ublk_from_d2h(h_ublk);
  ccDOTIMES(v, N_VOXELS_8) {
    tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,
                          msg,
                          v + child_ublk_offset, g_timescale.m_time,
                          state_index); 
    d_ublk_copy->print_ublk_content(os, opts);
  }
}

VOID print_all_mblks_in_group(MBLK_GROUP group,
                                   int state_index,
                                   const char* msg) {

  auto h_ublk = group->shob_ptrs();
  validate_state_index(state_index);  
  while(h_ublk) {
    sMBLK* d_ublk_copy = GPU::copy_ublk_from_d2h(h_ublk);
    for (int child_ublk_index = 0; child_ublk_index < 8; child_ublk_index++) {
      int child_ublk_offset = child_ublk_index * N_VOXELS_8;
      ccDOTIMES(v, N_VOXELS_8) {
        tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,
                              msg,
                              v + child_ublk_offset, g_timescale.m_time,
                              state_index); 
        d_ublk_copy->print_ublk_content(std::cout, opts);
      }
    }
    h_ublk = h_ublk->m_next;
  }
}

VOID print_select_mblks_in_group(MBLK_GROUP group,
                                      int state_index,
                                      const char* msg,
                                      std::vector<STP_SHOB_ID> print_ids) {

  auto h_ublk = group->shob_ptrs();
  validate_state_index(state_index);  
  while(h_ublk) {
    bool found = std::find(print_ids.begin(),
                           print_ids.end(),
                           h_ublk->id()) != print_ids.end();
    if (found) {
      sMBLK* d_ublk_copy = GPU::copy_ublk_from_d2h(h_ublk);
      for (int child_ublk_index = 0; child_ublk_index < 8; child_ublk_index++) {
        int child_ublk_offset = child_ublk_index * N_VOXELS_8;
        ccDOTIMES(v, N_VOXELS_8) {
          tUBLK_PRINT_OPTS opts(tUBLK_PRINT_OPTS::ALL_DATA_PRINT_MASK,
                                msg,
                                v + child_ublk_offset, g_timescale.m_time,
                                state_index); 
          d_ublk_copy->print_ublk_content(std::cout, opts);
        }
      }
    }
    h_ublk = h_ublk->m_next;
  }
}
