/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*
 * Transient boundary seeding data
 *
 * Mohit Jain, Dassault Systemes Simulia Corp.
 *  * Created Friday Oct 25, 2019
 *--------------------------------------------------------------------------*/
#include "common_sp.h"
#include "async_events.h"
#include "thread_run.h"

// contains all paramaters that control seeding
typedef struct sSEED_CONTROL {
  static std::vector<std::string> fluid_seed_var_names;
  static std::vector<std::string> boundary_seed_var_names;
  cBOOLEAN              seed_via_dimless_properties;        // pressure and vel
  cBOOLEAN              seed_via_mks;
  sINT8                 seed_sim_was_incompressible;
  sINT16                n_smart_seed_vars;
  sINT8                 n_fluid_seed_var_specs;
  sINT8                 n_boundary_seed_var_specs;
  DGF_SEED_VAR_TYPE     *smart_seed_var_types;
  cBOOLEAN              *is_var_seeded;
  asINT32               *active_seed_var_index; //Index in active seed variables
  dFLOAT                smart_seed_char_density;
  dFLOAT                smart_seed_char_vel;
  dFLOAT                smart_seed_char_temp;
  dFLOAT                smart_seed_lattice_gas_const;
  dFLOAT                smart_seed_mks_vel_scaling;
  //UDS
  sINT16                n_smart_seed_uds_vars;
  sINT16                *smart_seed_uds_var_indices;
  asINT32               seed_rotate_vel_csys_index;
  asINT32               seed_rotate_vel_axis;
  sdFLOAT               seed_rotate_vel_angle;
 
  VOID init(cDGF_SMART_SEED_CONTROL &seed_control);
  VOID scale_seed_vars_via_dimless_properties(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data);
  VOID scale_seed_vars_via_mks_properties(cDGF_SEED_FROM_MEAS_DATA &seed_from_meas_data);
  __HOST__DEVICE__ dFLOAT scale_seed_pressure(dFLOAT seed_pressure);
  __HOST__DEVICE__ dFLOAT compute_seed_velocity_scaling(dFLOAT pressure_seed, dFLOAT temp_seed,
                                                        dFLOAT density_seed, BOOLEAN based_on_vel_only);

  __HOST__ static VOID copy_to_device(GPU::sDEV_PTR& d_ptr,
                                      size_t n_bytes,
                                      const sSEED_CONTROL* h_ptr);

  VOID print(std::ostream &stream) const {
    stream << "sSEED_CONTROL" << std::endl;
    stream << "  seed_via_dimless_properties: " << (int)seed_via_dimless_properties << std::endl;
    stream << "  seed_via_mks: " << (int)seed_via_mks << std::endl;
    stream << "  seed_sim_was_incompressible: " << (int)seed_sim_was_incompressible << std::endl;
    stream << "  n_smart_seed_vars: " << n_smart_seed_vars << std::endl;
    stream << "  n_fluid_seed_var_specs: " << n_fluid_seed_var_specs << std::endl;
    stream << "  n_boundary_seed_var_specs: " << n_boundary_seed_var_specs << std::endl;
    stream << "  smart_seed_char_density: " << smart_seed_char_density << std::endl;
    stream << "  smart_seed_char_vel: " << smart_seed_char_vel << std::endl;
    stream << "  smart_seed_char_temp: " << smart_seed_char_temp << std::endl;
    stream << "  smart_seed_lattice_gas_const: " << smart_seed_lattice_gas_const << std::endl;
    stream << "  smart_seed_mks_vel_scaling: " << smart_seed_mks_vel_scaling << std::endl;
    stream << "  seed_rotate_vel_csys_index: " << seed_rotate_vel_csys_index << std::endl;
    stream << "  seed_rotate_vel_axis: " << seed_rotate_vel_axis << std::endl;
    stream << "  seed_rotate_vel_angle: " << seed_rotate_vel_angle << std::endl;
    for (int n = 0; n < n_smart_seed_vars; n++) {
      stream << "  " << fluid_seed_var_names[smart_seed_var_types[n]] << ": " << smart_seed_var_types[n] << 
                " (is_var_seeded: " << (is_var_seeded[n] ? "true" : "false") << ")" << 
                " active_seed_var_index: " << active_seed_var_index[n] << std::endl;
    }
  }

} *SEED_CONTROL;

typedef struct sSP_TRANSIENT_BOUNDARY_SEEDING {
  asINT32       m_first_unpack;
  asINT32       m_interp_data_index;
  sFLOAT        *m_seed_data;
  sFLOAT        *m_recv_buffer;
  sFLOAT        m_interp_weight;
  sTBS_PARAMS   m_params;
  cExaMsg<sFLOAT> m_tbs_msg;
  cBOOLEAN      m_ready_to_recv;
  cBOOLEAN      m_recvd_data;
  sSP_TRANSIENT_BOUNDARY_SEEDING() {
    m_first_unpack  = 0;
//    m_recv_request  = MPI_REQUEST_NULL;
    m_ready_to_recv = TRUE;
    m_recvd_data    = FALSE;
    m_interp_weight = 0;
    m_seed_data     = nullptr;
    m_recv_buffer   = nullptr;
    m_tbs_msg.init(eMPI_sp_cp_rank(), my_proc_id);
  }

  VOID   post_recv(asINT32 imeas);
  VOID   unpack();
  VOID   calculate_interpolation_index();
  VOID   allocate_buffers();
  VOID   set_var_mask(cDGF_TBS &tbs);
  VOID   set_params(cDGF_SMART_SEED_CONTROL &seed_control);
  VOID   update_tbs_var_count(cDGF_TBS &tbs);
  VOID	 scale_seed_vars_via_mks_properties(asINT32 var, sFLOAT &var_value, sSEED_CONTROL &seed_control);
  VOID	 scale_seed_vars_via_dimless_properties(asINT32 var, sFLOAT &var_value, sSEED_CONTROL &seed_control);
  VOID	 scale_seed_buffer_via_mks_properties(sSEED_CONTROL &seed_control);
  VOID	 scale_seed_buffer_via_dimless_properties(sSEED_CONTROL &seed_control);
  sFLOAT interpolate(asINT32 tbs_data_offset);
  asINT32 meas_data_period();
  asINT32 meas_start_time();
} *SP_TRANSIENT_BOUNDARY_SEEDING;

VOID add_transient_boundary_seed_data_comm_events();
VOID recv_initial_transient_boundary_seed_data();
