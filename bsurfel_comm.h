/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*/

#ifndef _SIMENG_BSURFEL_COMM_H
#define _SIMENG_BSURFEL_COMM_H

#include "common_sp.h"
#include "fset.h"
#include "comm_groups.h"
#include "bsurfel.h"
#include "strand_mgr.h"

void request_ublk_ib_bf_sends(SCALE scale);
void request_ublk_ib_bf_bcast_sends(SCALE scale, BOOLEAN do_near_surface);
void post_bsurfel_sends();
asINT32 create_bsurfel_send_groups();
asINT32 create_ublk_ib_bf_send_groups();

/*--------------------------------------------------------------------------*
 * sBSURFEL_COMM_GROUP (generic bsurfel comm group)
 *--------------------------------------------------------------------------*/
typedef class sBSURFEL_SEND_GROUP : public tSP_SEND_GROUP<cExaMsg<sdFLOAT>>
{
private:
  std::vector<unsigned char>  m_send_buffer;
  std::vector<sBSURFEL*>      m_bsurfels_to_send;
  asINT32 m_n_send_bytes;
  MPI_Request m_send_request;
  MPI_Request m_second_send_request;

public:

  sBSURFEL_SEND_GROUP(cNEIGHBOR_SP dest_sp, cSTP_SCALE scale)
  {
    m_dest_sp      = dest_sp;
    m_scale        = scale;
    m_n_send_bytes = 0;
    m_send_request = MPI_REQUEST_NULL;
    m_second_send_request = MPI_REQUEST_NULL;
    m_send_type = BSURFEL_SEND_TYPE;
  }

  virtual void send(ACTIVE_SOLVER_MASK active_solver_mask);

  void add_bsurfel(sBSURFEL * bsurfel)
  {
    m_bsurfels_to_send.push_back(bsurfel);
  }

} *BSURFEL_SEND_GROUP;

/*--------------------------------------------------------------------------*
 * BSURFEL_COMM_FSET
 *--------------------------------------------------------------------------*/

struct BSURFEL_SEND_GROUP_ORDER {
  BOOLEAN operator()(BSURFEL_SEND_GROUP a, BSURFEL_SEND_GROUP b) const
  {
    return a->m_dest_sp < b->m_dest_sp;
  }
};

typedef struct sBSURFEL_COMM_SEND_FSET :
  public tSP_FSET < BSURFEL_SEND_GROUP, BSURFEL_SEND_GROUP_ORDER > {

private:

  bool m_ready_to_send;

public:


  sBSURFEL_COMM_SEND_FSET() : m_ready_to_send(FALSE) {};

  BSURFEL_SEND_GROUP find_or_create_group(cNEIGHBOR_SP dest_sp)
  {
    sBSURFEL_SEND_GROUP signature(dest_sp, sim.num_scales - 1);

    BSURFEL_SEND_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sBSURFEL_SEND_GROUP(dest_sp, sim.num_scales - 1);
      add_group(group);
    }
    return group;
  }

  BSURFEL_SEND_GROUP find_comm_group(cNEIGHBOR_SP dest_sp)
  {
    sBSURFEL_SEND_GROUP signature(dest_sp, sim.num_scales - 1);

    BSURFEL_SEND_GROUP group = find_group(&signature);
    return group;
  }

} *BSURFEL_COMM_SEND_FSET;

extern sBSURFEL_COMM_SEND_FSET g_bsurfel_comm_send_fset;

#define DO_BSURFEL_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sBSURFEL_COMM_SEND_FSET, g_bsurfel_comm_send_fset, group_var)

#define DO_BSURFEL_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sBSURFEL_COMM_SEND_FSET, g_bsurfel_comm_send_fset, group_var, scale)

/*--------------------------------------------------------------------------*
 * sBSURFEL_COMM_RECV_GROUP
 *--------------------------------------------------------------------------*/
typedef class sBSURFEL_RECV_GROUP : public tSP_RECV_GROUP<cExaMsg<sdFLOAT>>
{

private:
  static std::unique_ptr<STP_PROC[]> m_total_nsps_for_scale;
  static std::unique_ptr<STP_PROC[]> m_current_unpacking_nsp_for_scale;
  int m_order_id;
  std::vector<unsigned char>  m_recv_buffer;
  bool m_count_received;
  MPI_Request m_recv_request;
  MPI_Request m_second_recv_request;

public:

  sBSURFEL_RECV_GROUP(cNEIGHBOR_SP source_sp, cSTP_SCALE scale)
  {
    m_source_sp = source_sp;
    m_scale = scale;
    m_recv_request        = MPI_REQUEST_NULL;
    m_second_recv_request = MPI_REQUEST_NULL;
    m_count_received = false;
    m_recv_type = BSURFEL_RECV_TYPE;
  }

  void init()
  {
    cassert(m_total_nsps_for_scale);
    cassert(m_current_unpacking_nsp_for_scale);
    m_order_id = m_total_nsps_for_scale[m_scale];
    m_total_nsps_for_scale[m_scale]++;
  }

  static void init_static_arrays(SCALE n_scales)
  {
    m_total_nsps_for_scale = std::unique_ptr<STP_PROC[]>(new STP_PROC[n_scales]);
    m_current_unpacking_nsp_for_scale = std::unique_ptr<STP_PROC[]>(new STP_PROC[n_scales]);
    for (SCALE s = 0; s < n_scales; s++) {
      m_total_nsps_for_scale[s] = 0;
      m_current_unpacking_nsp_for_scale[s] = 0;
    }
  }

  virtual VOID post_recv() override;
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask) override;
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) override;
  virtual bool is_recv_request_null() override;
  virtual bool is_recv_ready() override;
  virtual bool is_ready_to_be_unpacked() override;
  virtual void cancel_pending_recv() override
  {
    if (m_recv_request != MPI_REQUEST_NULL) {
      MPI_Cancel(&m_recv_request);
    }
    if (m_second_recv_request != MPI_REQUEST_NULL) {
      MPI_Cancel(&m_second_recv_request);
    }
  }


} *BSURFEL_RECV_GROUP;

/*--------------------------------------------------------------------------*
 * BSURFEL_COMM_RECV_FSET
 *--------------------------------------------------------------------------*/

typedef struct sBSURFEL_RECV_GROUP_ORDER {
  BOOLEAN operator()(BSURFEL_RECV_GROUP a, BSURFEL_RECV_GROUP b) const
  {
    return a->m_source_sp < b->m_source_sp;
  }
} *BSURFEL_RECV_GROUP_ORDER;

typedef struct sBSURFEL_COMM_RECV_FSET :
  public tSP_FSET < BSURFEL_RECV_GROUP, sBSURFEL_RECV_GROUP_ORDER> {
public:
  BSURFEL_RECV_GROUP find_or_create_group(cNEIGHBOR_SP source_sp)
  {
    sBSURFEL_RECV_GROUP signature(source_sp, FINEST_SCALE);
    BSURFEL_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sBSURFEL_RECV_GROUP(source_sp, sim.num_scales - 1);
      add_group(group);
    }
    return group;
  }

  BSURFEL_RECV_GROUP find_comm_group(cNEIGHBOR_SP source_sp)
  {
    sBSURFEL_RECV_GROUP signature(source_sp, FINEST_SCALE);
    BSURFEL_RECV_GROUP group = find_group(&signature);
    return group;
  }
} *BSURFEL_COMM_RECV_FSET;

extern sBSURFEL_COMM_RECV_FSET g_bsurfel_comm_recv_fset;

#define DO_BSURFEL_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sBSURFEL_COMM_RECV_FSET, g_bsurfel_comm_recv_fset, group_var)

#define DO_BSURFEL_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sBSURFEL_COMM_RECV_FSET, g_bsurfel_comm_recv_fset, group_var, scale)

void do_preliminary_bsurfel_comm();

/*--------------------------------------------------------------------------*
 * UBLK_DYNAMIC_MME_RECV_GROUP
 *--------------------------------------------------------------------------*/

typedef class sUBLK_DYNAMIC_MME_RECV_GROUP : public sUBLK_MME_RECV_GROUP
{

public:

  sUBLK_DYNAMIC_MME_RECV_GROUP(sUBLK_RECV_GROUP* template_group) : sUBLK_MME_RECV_GROUP(template_group) {}

  VOID allocate_recv_buffer()
  {

    m_tpde_recvsize = 0;
    asINT32 per_ghostblk_recvsize = 1;
    sUBLK::add_mme_send_size(sim.init_solver_mask, per_ghostblk_recvsize);
    m_recvsize += per_ghostblk_recvsize * m_quantums.size();
    m_recvsize++; // Add 1 for the total count at the beginning of the message
    m_recvsize++; // for timestep index storage
    LOG_MSG("STRAND_SWITCHING").printf("allocating recv buffer for dynamic mme ublks scale %d size %d to SP %d", m_scale, m_recvsize, source_rank());
    m_recv_msg.allocateBuffer(m_recvsize);
  }

  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) {}


} *UBLK_DYNAMIC_MME_RECV_GROUP;

typedef class sUBLK_DYNAMIC_MME_SEND_GROUP : public sUBLK_MME_SEND_GROUP
{

public:

  sUBLK_DYNAMIC_MME_SEND_GROUP(sUBLK_SEND_GROUP* template_group) : sUBLK_MME_SEND_GROUP(template_group) {}

  virtual VOID allocate_send_buffer()
  {

    m_tpde_sendsize = 0;
    asINT32 per_quantum_sendsize = 1;
    sUBLK::add_mme_send_size(sim.init_solver_mask, per_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_sendsize++; // for the total count
    m_sendsize++; // for timestep index storage
    LOG_MSG("STRAND_SWITCHING").printf("allocating send buffer for dynamic mme ublks scale %d size %d to SP %d", m_scale, m_sendsize, dest_rank());
    m_send_msg.allocateBuffer(m_sendsize);
  }

  VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  VOID send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) {}

} *UBLK_DYNAMIC_MME_SEND_GROUP;

typedef class sUBLK_IB_BF_SEND_GROUP : public sUBLK_SEND_GROUP
{

public:

  sUBLK_IB_BF_SEND_GROUP(sUBLK_RECV_GROUP *ublk_recv_group)
  {
    m_scale = ublk_recv_group->scale();
    m_quantums = ublk_recv_group->m_quantums;
    m_is_near_surface = ublk_recv_group->m_is_near_surface;
    m_is_mme_data = false;
    m_dest_sp = ublk_recv_group->m_source_sp;
    m_send_msg.init(my_proc_id, dest_rank());
    m_send_type = m_is_near_surface ? NEARBLK_IB_BF_SEND_TYPE : FARBLK_IB_BF_SEND_TYPE;
  }

  VOID allocate_send_buffer()
  {
    asINT32 per_quantum_sendsize = 1;
    sBSURFEL_BODY_FORCE_8::add_send_size(per_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_sendsize++; // for the total count
    LOG_MSG("STRAND_SWITCHING").printf("allocating send buffer for ib_bf send group near %d scale %d size %d to SP %d", m_is_near_surface, m_scale, m_sendsize, dest_rank());

    m_send_msg.allocateBuffer(m_sendsize);
  }

  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask,SOLVER_INDEX_MASK solver_ts_index_mask) { send(active_solver_mask); }
} *UBLK_IB_BF_SEND_GROUP;

typedef class sUBLK_IB_BF_RECV_GROUP : public sUBLK_RECV_GROUP
{

private:
  static std::unique_ptr<STP_PROC[]> m_total_nsps_for_scale_and_type;
  static std::unique_ptr<STP_PROC[]> m_current_unpacking_nsp_for_scale_and_type;
  int m_order_id;
  int m_index;

public:

  sUBLK_IB_BF_RECV_GROUP(sUBLK_SEND_GROUP *ublk_send_group)
  {
    m_scale = ublk_send_group->scale();
    m_quantums = ublk_send_group->m_quantums;
    m_is_near_surface = ublk_send_group->m_is_near_surface;
    m_is_mme_data = false;
    m_source_sp = ublk_send_group->m_dest_sp;
    m_recv_msg.init(source_rank(), my_proc_id);
    m_recv_type = m_is_near_surface ? NEARBLK_IB_BF_RECV_TYPE : FARBLK_IB_BF_RECV_TYPE;
  }

  void init()
  {
    cassert(m_total_nsps_for_scale_and_type);
    cassert(m_current_unpacking_nsp_for_scale_and_type);
    m_index = 2 * m_scale + (m_is_near_surface ? 0 : 1);
    m_order_id = m_total_nsps_for_scale_and_type[m_index];
    m_total_nsps_for_scale_and_type[m_index]++;
  }


  RECV_TYPE type()
  {
    return m_recv_type;
  }

  VOID allocate_recv_buffer()
  {
    asINT32 per_quantum_recvsize = 1;
    sBSURFEL_BODY_FORCE_8::add_send_size(per_quantum_recvsize);
    m_recvsize += per_quantum_recvsize * m_quantums.size();
    m_recvsize++; // for the total count
    LOG_MSG("STRAND_SWITCHING").printf("allocating recv buffer for ib_bf recv group near %d scale %d size %d from SP %d", m_is_near_surface, m_scale, m_recvsize, source_rank());

    m_recv_msg.allocateBuffer(m_recvsize);
  }

  virtual VOID post_recv()
  {
    int tag = 0;
    if (m_is_near_surface) {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_IB_BF>(m_scale);
    }
    else {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_IB_BF>(m_scale);
    }
    LOG_MSG("STRAND_SWITCHING").printf("posting ublk ib_bf recv: source %d near %d, scale %d tag %x, size %d", source_rank(), m_is_near_surface, m_scale, tag,  m_recvsize);
    m_recv_msg.set_nelems(m_recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask);
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask);
  virtual bool is_ready_to_be_unpacked();

  // this class handles both nearblks and farblks, which must be tracked separately
  static void init_static_arrays(SCALE n_scales)
  {
    m_total_nsps_for_scale_and_type = std::unique_ptr<STP_PROC[]>(new STP_PROC[n_scales * 2]);
    m_current_unpacking_nsp_for_scale_and_type = std::unique_ptr<STP_PROC[]>(new STP_PROC[n_scales * 2]);
    for (SCALE s = 0; s < n_scales * 2; s++) {
      m_total_nsps_for_scale_and_type[s] = 0;
      m_current_unpacking_nsp_for_scale_and_type[s] = 0;
    }
  }



} *UBLK_IB_BF_RECV_GROUP;

/*--------------------------------------------------------------------------*
 * sUBLK_IB_BF_RECV_FSET
 *--------------------------------------------------------------------------*/

// The ublk_ib_bf groups handle both nearblk and farblks, so we have
// to use m_recv_type to differentiate between the two.
typedef struct sUBLK_IB_BF_RECV_ORDER {
  BOOLEAN operator()(UBLK_IB_BF_RECV_GROUP a, UBLK_IB_BF_RECV_GROUP b) const
  {
    if (a->m_recv_type == b->m_recv_type) {
      if (a->m_scale == b->m_scale) {
        return a->m_source_sp < b->m_source_sp;
      }
      else {
        return a->m_scale < b->m_scale;
      }
    }
    else {
      return a->m_recv_type < b->m_recv_type;
    }
  }
} *UBLK_IB_BF_RECV_ORDER;

typedef struct sUBLK_IB_BF_RECV_FSET :
  public tSP_FSET < UBLK_IB_BF_RECV_GROUP, sUBLK_IB_BF_RECV_ORDER> {
public:
  UBLK_IB_BF_RECV_GROUP find_or_create_group(sUBLK_SEND_GROUP * send_group)
  {
    sUBLK_IB_BF_RECV_GROUP signature(send_group);
    UBLK_IB_BF_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sUBLK_IB_BF_RECV_GROUP(send_group);
      add_group(group);
    }
    return group;
  }

  UBLK_IB_BF_RECV_GROUP find_comm_group(sUBLK_SEND_GROUP * send_group)
  {
    sUBLK_IB_BF_RECV_GROUP signature(send_group);
    UBLK_IB_BF_RECV_GROUP group = find_group(&signature);
    return group;
  }
} *UBLK_IB_BF_RECV_FSET;

extern sUBLK_IB_BF_RECV_FSET g_ublk_ib_bf_recv_fset;

#define DO_UBLK_IB_BF_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sUBLK_IB_BF_RECV_FSET, g_ublk_ib_bf_recv_fset, group_var)

#define DO_UBLK_IB_BF_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_IB_BF_RECV_FSET, g_ublk_ib_bf_recv_fset, group_var, scale)

/*--------------------------------------------------------------------------*
 * sUBLK_IB_BF_SEND_FSET
 *--------------------------------------------------------------------------*/

// The ublk_ib_bf groups handle both nearblk and farblks, so we have
// to use m_send_type to differentiate between the two.
typedef struct sUBLK_IB_BF_SEND_ORDER {
  BOOLEAN operator()(UBLK_IB_BF_SEND_GROUP a, UBLK_IB_BF_SEND_GROUP b) const
  {
    if (a->m_send_type == b->m_send_type) {
      if (a->m_scale == b->m_scale) {
        return a->m_dest_sp < b->m_dest_sp;
      }
      else {
        return a->m_scale < b->m_scale;
      }
    }
    else {
      return a->m_send_type < b->m_send_type;
    }
  }
} *UBLK_IB_BF_SEND_ORDER;

typedef struct sUBLK_IB_BF_SEND_FSET :
  public tSP_FSET < UBLK_IB_BF_SEND_GROUP, sUBLK_IB_BF_SEND_ORDER> {
public:
  UBLK_IB_BF_SEND_GROUP find_or_create_group(sUBLK_RECV_GROUP * recv_group)
  {
    sUBLK_IB_BF_SEND_GROUP signature(recv_group);
    UBLK_IB_BF_SEND_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sUBLK_IB_BF_SEND_GROUP(recv_group);
      add_group(group);
    }
    return group;
  }

  UBLK_IB_BF_SEND_GROUP find_comm_group(sUBLK_RECV_GROUP * recv_group)
  {
    sUBLK_IB_BF_SEND_GROUP signature(recv_group);
    UBLK_IB_BF_SEND_GROUP group = find_group(&signature);
    return group;
  }
} *UBLK_IB_BF_SEND_FSET;

extern sUBLK_IB_BF_SEND_FSET g_ublk_ib_bf_send_fset;

#define DO_UBLK_IB_BF_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sUBLK_IB_BF_SEND_FSET, g_ublk_ib_bf_send_fset, group_var)

#define DO_UBLK_IB_BF_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_IB_BF_SEND_FSET, g_ublk_ib_bf_send_fset, group_var, scale)

// BCAST groups

typedef class sUBLK_IB_BF_BCAST_SEND_GROUP : public sUBLK_SEND_GROUP
{

public:

  sUBLK_IB_BF_BCAST_SEND_GROUP(sUBLK_SEND_GROUP *ublk_send_group)
  {
    m_scale = ublk_send_group->scale();
    m_quantums = ublk_send_group->m_quantums;
    m_is_near_surface = ublk_send_group->m_is_near_surface;
    m_is_mme_data = false;
    m_dest_sp = ublk_send_group->m_dest_sp;
    m_send_type = m_is_near_surface ? NEARBLK_IB_BF_BCAST_SEND_TYPE : FARBLK_IB_BF_BCAST_SEND_TYPE;
    m_send_msg.init(my_proc_id, dest_rank());
  }

  VOID allocate_send_buffer()
  {
    asINT32 per_quantum_sendsize = 1;
    sBSURFEL_BODY_FORCE_8::add_send_size(per_quantum_sendsize);
    m_sendsize += per_quantum_sendsize * m_quantums.size();
    m_sendsize++; // for the total count
    LOG_MSG("STRAND_SWITCHING").printf("allocating send buffer for ib_bf_bcast send group scale %d near %d size %d to SP %d", m_scale, m_is_near_surface, m_sendsize, dest_rank());

    m_send_msg.allocateBuffer(m_sendsize);
  }

  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask);
  virtual VOID send(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask) { send(active_solver_mask); }
} *UBLK_IB_BF_BCAST_SEND_GROUP;

typedef class sUBLK_IB_BF_BCAST_RECV_GROUP : public sUBLK_RECV_GROUP
{

public:

  sUBLK_IB_BF_BCAST_RECV_GROUP(sUBLK_RECV_GROUP *ublk_recv_group)
  {
    m_scale = ublk_recv_group->scale();
    m_quantums = ublk_recv_group->m_quantums;
    m_is_near_surface = ublk_recv_group->m_is_near_surface;
    m_is_mme_data = false;
    m_source_sp = ublk_recv_group->m_source_sp;
    m_recv_msg.init(source_rank(), my_proc_id);
    m_recv_type = m_is_near_surface ? NEARBLK_IB_BF_BCAST_RECV_TYPE : FARBLK_IB_BF_BCAST_RECV_TYPE;
  }

  RECV_TYPE type()
  {
    return m_recv_type;
  }

  VOID allocate_recv_buffer()
  {
    asINT32 per_quantum_recvsize = 1;
    sBSURFEL_BODY_FORCE_8::add_send_size(per_quantum_recvsize);
    m_recvsize += per_quantum_recvsize * m_quantums.size();
    m_recvsize++; // for the total count
    LOG_MSG("STRAND_SWITCHING").printf("allocating recv buffer for ib_bf_bcast recv group scale %d near %d size %d from SP %d", m_scale, m_is_near_surface, m_recvsize, source_rank());

    m_recv_msg.allocateBuffer(m_recvsize);
    m_recv_msg.set_nelems(m_recvsize);
  }

  virtual VOID post_recv()
  {
    int tag = 0;
    if (m_is_near_surface) {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::NEARBLK_IB_BF_BCAST>(m_scale);
    }
    else {
      tag = make_mpi_shob_tag<eMPI_SHOB_TAG::FARBLK_IB_BF_BCAST>(m_scale);
    }
    LOG_MSG("STRAND_SWITCHING").printf("posting ublk ib_bf_bcast recv: source %d near %d scale %d tag %x, size %d", source_rank(), m_is_near_surface, m_scale, tag,  m_recvsize);
    m_recv_msg.settag(tag);
    g_exa_sp_cp_comm.irecv(m_recv_msg);
  }

  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask);
  virtual VOID unpack(ACTIVE_SOLVER_MASK active_solver_mask, SOLVER_INDEX_MASK solver_ts_index_mask);

} *UBLK_IB_BF_BCAST_RECV_GROUP;

// bcast fsets

typedef struct sUBLK_IB_BF_BCAST_SEND_ORDER {
  BOOLEAN operator()(UBLK_IB_BF_BCAST_SEND_GROUP a, UBLK_IB_BF_BCAST_SEND_GROUP b) const
  {
    if (a->m_send_type == b->m_send_type) {
      if (a->m_scale == b->m_scale) {
        return a->m_dest_sp < b->m_dest_sp;
      }
      else {
        return a->m_scale < b->m_scale;
      }
    }
    else {
      return a->m_send_type < b->m_send_type;
    }
  }
} *UBLK_IB_BF_BCAST_SEND_ORDER;

typedef struct sUBLK_IB_BF_BCAST_SEND_FSET :
  public tSP_FSET < UBLK_IB_BF_BCAST_SEND_GROUP, sUBLK_IB_BF_BCAST_SEND_ORDER> {
public:
  UBLK_IB_BF_BCAST_SEND_GROUP find_or_create_group(sUBLK_SEND_GROUP * send_group)
  {
    sUBLK_IB_BF_BCAST_SEND_GROUP signature(send_group);
    UBLK_IB_BF_BCAST_SEND_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sUBLK_IB_BF_BCAST_SEND_GROUP(send_group);
      add_group(group);
    }
    return group;
  }

  UBLK_IB_BF_BCAST_SEND_GROUP find_comm_group(sUBLK_SEND_GROUP * send_group)
  {
    sUBLK_IB_BF_BCAST_SEND_GROUP signature(send_group);
    UBLK_IB_BF_BCAST_SEND_GROUP group = find_group(&signature);
    return group;
  }
} *UBLK_IB_BF_BCAST_SEND_FSET;

extern sUBLK_IB_BF_BCAST_SEND_FSET g_ublk_ib_bf_bcast_send_fset;

#define DO_UBLK_IB_BF_BCAST_SEND_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sUBLK_IB_BF_BCAST_SEND_FSET, g_ublk_ib_bf_bcast_send_fset, group_var)

#define DO_UBLK_IB_BF_BCAST_SEND_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_IB_BF_BCAST_SEND_FSET, g_ublk_ib_bf_bcast_send_fset, group_var, scale)

typedef struct sUBLK_IB_BF_BCAST_RECV_ORDER {
  BOOLEAN operator()(UBLK_IB_BF_BCAST_RECV_GROUP a, UBLK_IB_BF_BCAST_RECV_GROUP b) const
  {
    if (a->m_recv_type == b->m_recv_type) {
      if (a->m_scale == b->m_scale) {
        return a->m_source_sp < b->m_source_sp;
      }
      else {
        return a->m_scale < b->m_scale;
      }
    }
    else {
      return a->m_recv_type < b->m_recv_type;
    }
  }
} *UBLK_IB_BF_BCAST_RECV_ORDER;

typedef struct sUBLK_IB_BF_BCAST_RECV_FSET :
  public tSP_FSET < UBLK_IB_BF_BCAST_RECV_GROUP, sUBLK_IB_BF_BCAST_RECV_ORDER> {
public:
  UBLK_IB_BF_BCAST_RECV_GROUP find_or_create_group(sUBLK_RECV_GROUP * recv_group)
  {
    sUBLK_IB_BF_BCAST_RECV_GROUP signature(recv_group);
    UBLK_IB_BF_BCAST_RECV_GROUP group = find_group(&signature);
    if (NULL == group) {
      group = new sUBLK_IB_BF_BCAST_RECV_GROUP(recv_group);
      add_group(group);
    }
    return group;
  }

  UBLK_IB_BF_BCAST_RECV_GROUP find_comm_group(sUBLK_RECV_GROUP * recv_group)
  {
    sUBLK_IB_BF_BCAST_RECV_GROUP signature(recv_group);
    UBLK_IB_BF_BCAST_RECV_GROUP group = find_group(&signature);
    return group;
  }

} *UBLK_IB_BF_BCAST_RECV_FSET;

extern sUBLK_IB_BF_BCAST_RECV_FSET g_ublk_ib_bf_bcast_recv_fset;

#define DO_UBLK_IB_BF_BCAST_RECV_GROUPS(group_var) \
  FSET_FOREACH_GROUP(sUBLK_IB_BF_BCAST_RECV_FSET, g_ublk_ib_bf_bcast_recv_fset, group_var)

#define DO_UBLK_IB_BF_BCAST_RECV_GROUPS_OF_SCALE(group_var, scale) \
  FSET_FOREACH_GROUP_OF_SCALE(sUBLK_IB_BF_BCAST_RECV_FSET, g_ublk_ib_bf_bcast_recv_fset, group_var, scale)

#endif
