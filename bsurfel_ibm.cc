/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2015, 1993-2014 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */
/*--------------------------------------------------------------------------*/
#include "bsurfel.h"

/*VOID sBSURFEL::compute_body_force()
{
  const bool is_slip = sim.is_turb_model;
  // apply the specified BC type (free-slip or no-slip)
  // normal body force is  2.0 * (body velocity - interpolated normal fluid velocity)
  if (is_slip) { // for frictionless walls, keep only the normal velocity component
    dFLOAT fluid_norm_vel_mag, bsurfel_norm_vel_mag;
    fluid_norm_vel_mag = vdot(m_sampled_fluid_velocity, m_normal);
    bsurfel_norm_vel_mag = vdot(m_vel, m_normal);
    ccDOTIMES(axis, 3) {
      m_computed_body_force[axis] = m_normal[axis] * (2.0f * (bsurfel_norm_vel_mag - fluid_norm_vel_mag));
    }
  }
  else {
    ccDOTIMES(axis, 3) {
      m_computed_body_force[axis] = 2.0f * (m_vel[axis] - m_sampled_fluid_velocity[axis]);
    }
  }

  }*/
