/* ~~~CO<PERSON><PERSON><PERSON><PERSON><PERSON>~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2022, 1993-2021 Dassault Systemes Simulia Corp.         ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Simulia Corp.                                                         ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/* ~~~COPYWRITE~~~+ boxcomment("simeng.copyright", "09") */ 
/********
 *** PowerFLOW Simulator Simulation Process ***
 ***  ***
 *** Copyright (C) 2022, 1993-2021 Dassault Systemes Simulia Corp. ***
 *** All Rights Reserved. ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;  ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239; ***
 ***        6,089,744; 7,558,714 ***
 *** UK FR DE Pat 0 538 415 ***
 ***  ***
 *** This computer program is the property of Dassault Systemes Simulia Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes  ***
 *** Simulia Corp. ***
 ********
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "09") */ 

#include "surfel_dyn_sp.h"
#include "conduction_contact_averaging.h"

// Averaged contact involves entire faces that can span multiple groups. Therefore, we centralize averaging operations
// in a dedicate class, reached through a global variable.
sTHERMAL_AVERAGED_CONTACTS g_thermal_averaged_contacts;

//----------------------------------------------------------------------------
// sCONDUCTION_CONTACT_AVERAGING_ITEM methods
//----------------------------------------------------------------------------
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::maybe_create_dangling_accumulation(size_t idx_avg_local) {
  //If this SP owns only surfels associated with only one face of the two being in contact, no accumulators exist for
  //the other face. However, an accumulator is needed to do the global reduction and compute the averaged heat flux, so
  //it is created directly here.
  for (size_t i=0; i<2; i++) {
    if (m_accumulated[i] == nullptr) {
      m_accumulated[i] = new sCONTACT_ACCUMULATION(i==0, idx_avg_local);
      m_accumulated[i]->set_dangling();
      m_area[i] = 0.0;
    }
  }
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::add_area(bool is_primary, sdFLOAT area) {
  size_t idx = (is_primary) ? 0 : 1;
  m_area[idx] += area;
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::compute_contact_area() {
  // msg_print("ac after %d %g %g", global_idx(), m_area[0], m_area[1]);
  if (m_area[0] <= 0.0 || m_area[1] <= 0.0) {
    //If only insulators are part of one of the sides of the averaged contact, no conduction surfels are created and
    //therefore no area is computed. Issue a warning to let the user know
    int my_csp_rank;
    MPI_Comm_rank(m_mpi_communicator, &my_csp_rank);
    if (my_csp_rank==0) msg_warn("Invalid averaged contact %d, probably involving insulators", global_idx());
    //tags it as invalid so no accumulations or average heat flux calculation will be done
    m_is_invalid = true; 
    m_contact_area = 0.0;
    m_heat_flux[0] = 0.0;
    m_heat_flux[1] = 0.0;
    return;
  }
  dFLOAT max_area, min_area;
  if (m_area[0] >= m_area[1]) {
    max_area = m_area[0];
    min_area = m_area[1];
  } else {
    max_area = m_area[1];
    min_area = m_area[0];
  }
  dFLOAT area_ratio = max_area/min_area;
  
  //Based on the area ratio between faces:
  if (area_ratio < 1.15) {  //maybe this could be a configuration parameter?
    //use average area as contact area between parts
    m_contact_area = 0.5 * (max_area + min_area);
  } else { 
    //use min area, typical with LRF where the large area is spinning so the net heat flux is kind of distributed
    //through all the face but heat transfer would occur at the small area
    m_contact_area = min_area;
  }
}

/** @brief Computes the averaged heat flux across each surface involved in averaged contact */
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::compute_averaged_heat_flux() {
  dFLOAT thickness_avg[2], temp_avg[2], conductivity_avg[2];
  for (size_t i=0; i<2; i++) {
    sCONTACT_ACCUMULATION *accumulated_data = m_accumulated[i];
    sdFLOAT total_volume = accumulated_data->volume_accum;
    thickness_avg[i] = total_volume / m_area[i];
    temp_avg[i] = accumulated_data->temp_accum / total_volume;
    conductivity_avg[i] = accumulated_data->conductivity_accum / total_volume;
    //resets accumulator if dangling, since it is not owned by any surfel group
    if (accumulated_data->is_dangling()) accumulated_data->reset();
  }
  //Retrieves the contact resistance associated with the contact descriptor. Even if it time varying, its value
  //should have been updated during last dynamics phase so no need to invoke update here.
  sCONDUCTION_CONTACT_SURFEL_PARAMETERS *param = (sCONDUCTION_CONTACT_SURFEL_PARAMETERS *)m_pd->parameters();
  sdFLOAT contact_resistance = param->contact_resistance.value / g_density_scale_factor;
  /**
  Once average values are known, we can compute the net heat transfer between the parts. In general, we can define the
  following scenario between the two sampling volume centroids on each side, x0 and x1
  \code
   [x0] <--0.5*thickness_avg[0]-->  |       contact_resistance_layer       |  <--0.5*thickness_avg[1]--> [x1]
            conductivity_avg[0]     |  layer_thickness/contact_resistance  |      conductivity_avg[0]
  \endcode

  We define the following equalities to simplify the notation:
  \f{align}{
  R_{ct} &= \mathrm{contact\_resistance} = \mathrm{contact\_layer\_thickness} / \mathrm{contact\_layer\_conductivity} \\
  L_0 &= 0.5*\mathrm{thickness\_avg[0]}, \\
  L_1 &= 0.5*\mathrm{thickness\_avg[1]}, \\
  k_0 &= \mathrm{conductivity\_avg[0]}, \\
  k_1 &= \mathrm{conductivity\_avg[1]}.
  \f}
  
  The heat flux across an interface \f$q''\f$ can be computed as
  \f{align}{
  q'' = \mathrm{conductivity} * \nabla T 
      = \mathrm{conductivity} * (\Delta T / \mathrm{dist}) 
      = \Delta T / (\mathrm{dist} / \mathrm{conductivity}).
  \f}
  
  The contact resistance layer does not hold any heat, so the heat flux on the interfaces between each sampled
  region and the contact resistance layer should be equal. Identifing \f$s0\f$ and \f$s1\f$ as the left and right interface
  surfaces, respectively, we establish the following equalities looking at the conductivities at each side of this
  surfaces:
  \f{align}{
  q'' = (\mathrm{temp\_avg[0]} - T_{s0}) / (L_0/k_0)  
      = (T_{s0} - T_{s1})/ R_{ct} = (T_{s1} - \mathrm{temp\_avg[1]}) / (L_1/k_1),
  \f}
  which leads to the following expression in terms only of the sampled averaged temperatures:
  \f{align}{
   q'' = (\mathrm{temp\_avg[0]} - \mathrm{temp\_avg[1]}) / (L_0/k_0 + R_{ct} + L_1/k_1)
       = (\mathrm{temp\_avg[0]} - \mathrm{temp\_avg[1]}) / \mathrm{term},
  \f}
  where \f$\mathrm{term} = L_0/k_0 + R_{ct} + L_1/k_1 = (L_0 * k_1 + L_1 * k_0) / (k_0 * k_1) + R_{ct}\f$.
  */
  dFLOAT term = 0.5 * (thickness_avg[0] * conductivity_avg[1] + thickness_avg[1] * conductivity_avg[0])
                / (conductivity_avg[0] * conductivity_avg[1]);
  if (contact_resistance > 0.0) {
    term += contact_resistance;
  }
  /**
  We can then compute the heat transfer exchanged between the two sampled domains as
  \f{align}{
  \mathrm{heat\_transfer} = q'' * \mathrm{area} 
                          = (\mathrm{temp\_avg[0]} - \mathrm{temp\_avg[1]}) / \mathrm{term} * \mathrm{area}.
  \f}
  */
  dFLOAT heat_transfer = (temp_avg[0] - temp_avg[1]) / term * m_contact_area;
  //Finally, we distribute the heat exchanged uniformly across the area of each face. Note also that the convention for
  //each domain is positive : heat flux into the domain, so flips the sign for the primary
  //(averagers with zero areas marked as invalid during initialization and should not reach here)
  m_heat_flux[0] = -heat_transfer / m_area[0];
  m_heat_flux[1] = heat_transfer / m_area[1];
  //Done with averaging, mark the averaging as completed
  set_averaging_completed();
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::create_communicator(asINT32 new_comm_size, int *new_comm_ranks) {
  MPI_Group_incl(eMPI_csp_group, new_comm_size, new_comm_ranks, &m_mpi_comm_group);
  MPI_Comm_create(eMPI_csp_comm, m_mpi_comm_group, &m_mpi_communicator);
}

bool sCONDUCTION_CONTACT_AVERAGING_ITEM::is_request_received() {
  int flag = 0;
  MPI_Status status;
  MPI_Test(&m_reduction_request, &flag, &status);
  return (flag);
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::complete_mpi_request() {
  complete_mpi_request_while_processing_cp_messages(&m_reduction_request, MPI_SLEEP_LONG);
}

//Global reduction of coarsest scale involved in each averaged contact
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::begin_reduce_averaged_coarsest_scale() {
  MPI_Iallreduce(MPI_IN_PLACE, &m_averaged_coarsest_scale, 1, eMPI_csINT16, MPI_MIN, m_mpi_communicator, &m_reduction_request);
}

//Global reduction of contact areas, done during initialization
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::begin_reduce_averaged_area() {
  // msg_print("ac before %d %g %g", global_idx(), m_area[0], m_area[1]);
  MPI_Iallreduce(MPI_IN_PLACE, &m_area, 2, eMPI_dFLOAT, MPI_SUM, m_mpi_communicator, &m_reduction_request);
}

//Global reduction of accumulated quantities needed to compute the averaged heat flux
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::begin_reduce_accumulations() {
  int j=0;
  ccDOTIMES(i,2) {
    m_reduction_buffer[j++] = m_accumulated[i]->temp_accum;
    m_reduction_buffer[j++] = m_accumulated[i]->conductivity_accum;
    m_reduction_buffer[j++] = m_accumulated[i]->volume_accum;
  }
  MPI_Iallreduce(MPI_IN_PLACE, &m_reduction_buffer, 6, eMPI_dFLOAT, MPI_SUM, m_mpi_communicator, &m_reduction_request);
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::complete_reduce_accumulations() {
  //Comm thread checks that data was received before invoking this method, no need to wait here
  int j=0;
  ccDOTIMES(i,2) {
    m_accumulated[i]->temp_accum = m_reduction_buffer[j++];
    m_accumulated[i]->conductivity_accum = m_reduction_buffer[j++];
    m_accumulated[i]->volume_accum = m_reduction_buffer[j++];
  }
}

VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::load_ckpt(cLGI_CKPT_AVERAGED_CONTACT_ITEM &item) { 
  m_heat_flux[0] = item.averaged_heat_flux_primary;
  m_heat_flux[1] = item.averaged_heat_flux_secondary;
}
VOID sCONDUCTION_CONTACT_AVERAGING_ITEM::write_ckpt() {
  cLGI_CKPT_AVERAGED_CONTACT_ITEM item;
  item.global_averaged_contact_index = m_global_idx;
  item.averaged_heat_flux_primary = m_heat_flux[0];
  item.averaged_heat_flux_secondary = m_heat_flux[1];
  write_ckpt_lgi(item);
}

//----------------------------------------------------------------------------
// sTHERMAL_AVERAGED_CONTACTS methods
//----------------------------------------------------------------------------
VOID sTHERMAL_AVERAGED_CONTACTS::init_count(bool is_full_checkpoint_restore) {
  //During seeding, no need to keep track of a count since all surfels are traversed sequentially. Thus, set count to
  //zero in this first pass. Additionally, enables the active flag if it is not a full checkpoint restore and there are
  //thermal averaged contacts in this sp
  m_count = 0;
  m_is_active = (!is_full_checkpoint_restore && m_is_enabled_in_this_sp);
  if (m_is_active) {
    for (auto &averaging : m_averaged_contacts) {
      averaging.set_averaging();
    }
  }
}

VOID sTHERMAL_AVERAGED_CONTACTS::reset_count() {
  //Invoked by time update strand
  m_count = 0;
  for (auto &averaging : m_averaged_contacts) {
    //checks if accumulation is needed, which also updates internally the averaging state, and if so, adds to the count
    if (averaging.is_accumulation_needed()) { 
      m_count += averaging.num_accumulators();
    }
  }
  m_is_active = (m_count>0);
}

VOID sTHERMAL_AVERAGED_CONTACTS::decrease_count() { 
  m_count--; 
  if (m_count == 0) {
    m_reduction_active_flag.store(true, std::memory_order_release);
  }
}

//After seeding, clears all accumulated values since it will re-start with the same timestep
VOID sTHERMAL_AVERAGED_CONTACTS::reset_accumulations() {
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_CONTACT_SURFELS_GROUPS_OF_SCALE(contact_surfel_group, scale) {
      for (auto& accumulation : contact_surfel_group->m_averaged_accumulation) {
        accumulation.reset();
      }
    }
  }
}

inline VOID sTHERMAL_AVERAGED_CONTACTS::do_local_reductions() {
  // Loops through all contact groups to do a local reduction, in case that multiple groups contribute to the same averaging
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_CONTACT_SURFELS_GROUPS_OF_SCALE(contact_surfel_group, scale) {
      for (size_t idx_accum = 0; idx_accum < contact_surfel_group->m_averaged_accumulation.size(); idx_accum++) {
        sCONTACT_ACCUMULATION* accumulation = &contact_surfel_group->m_averaged_accumulation[idx_accum];
        sCONTACT_ACCUMULATION::sAVERAGER_INDEX_AND_FACE avg = accumulation->averager_index_and_face();
        //If in the accumulation phase, condense all accumulated data in groups into a single value. 
        //Accumulating stage should have been set when contact groups were traversed to compute phase A of dynamics, we
        //just need to read it here
        if (m_averaged_contacts[avg.idx].is_averaging()) {
          sCONTACT_ACCUMULATION* avg_accumulation = (avg.is_primary)
                                                    ? m_averaged_contacts[avg.idx].accumulation_primary()
                                                    : m_averaged_contacts[avg.idx].accumulation_secondary();
          if (accumulation != avg_accumulation) {
            avg_accumulation->add(accumulation);
          }
        }
      }
    }
  }
}

// Multiprocessor support
VOID sTHERMAL_AVERAGED_CONTACTS::create_contact_averaging_communicators() {
  if(total_csps <= 0 || !sim.is_conduction_sp) {
    return;
  }
  int my_csp_rank;
  cassert(eMPI_csp_comm != MPI_COMM_NULL);
  MPI_Comm_rank(eMPI_csp_comm, &my_csp_rank);

  // We need to know whether this CSP is a participant in a particular averaging. 

  std::vector<size_t> this_csp_participates_in_ac(size_global(), 0);
  int *csp_is_participant = new int[total_csps]();
  int *new_comm_ranks = new int[total_csps]();
  for (size_t i = 0; i < m_averaged_contacts.size(); i++) {
    this_csp_participates_in_ac[m_averaged_contacts[i].global_idx()] = i+1; //1-based, 0 implies not participating
  }
  ccDOTIMES(idx, size_global()) {
    int this_csp_participates = (this_csp_participates_in_ac[idx] == 0) ? 0 : 1;
    MPI_Allgather(&this_csp_participates,1,MPI_INT, csp_is_participant,1,MPI_INT,eMPI_csp_comm);
    asINT32 new_comm_size = 0;
    ccDOTIMES(csp_rank, total_csps) {
      if(csp_is_participant[csp_rank] == 1) {
        new_comm_ranks[new_comm_size] = csp_rank;
        new_comm_size++;
      }
    }
    if (new_comm_size == 0) {
      //Surfels in the faces involved in this contact were not created or tagged as averaged contact, probably because
      //both sides involved in this averaged contact only contain insulators. Throw a warning to let the user know
      if (my_csp_rank==0) msg_warn("No surfels tagged for averaged contact %d", idx);
    } else if (this_csp_participates) {
      //Creates the communicator
      m_averaged_contacts[this_csp_participates_in_ac[idx]-1].create_communicator(new_comm_size, new_comm_ranks);
    } else {
      //All CSPs need to be involved since are part of the eMPI_csp_comm. Since this csp does not participate in this
      //contact, no contact item is associated. Thus, "creates" it locally and should end up with a null communicator
      MPI_Group mpi_comm_group;
      MPI_Comm mpi_communicator;
      MPI_Group_incl(eMPI_csp_group, new_comm_size, new_comm_ranks, &mpi_comm_group);
      MPI_Comm_create(eMPI_csp_comm, mpi_comm_group, &mpi_communicator);
      cassert(mpi_communicator == MPI_COMM_NULL);
    }
  }
  delete[] csp_is_participant;
  delete[] new_comm_ranks;
}

VOID sTHERMAL_AVERAGED_CONTACTS::complete_initialization() {
  for(size_t idx=0;  idx < m_averaged_contacts.size(); idx++) {
    auto &ac = m_averaged_contacts[idx];
    //create accumulators if none present for one of the two faces
    ac.maybe_create_dangling_accumulation(idx);
    //post reductions to get the correct averaged coarsest scale
    ac.begin_reduce_averaged_coarsest_scale();
  }
  //Enforces the completion of all reductions before moving forward
  for(auto &ac : m_averaged_contacts) {
    ac.complete_mpi_request();
  }
}

VOID sTHERMAL_AVERAGED_CONTACTS::seed_averaged_contacts() {
  //Total contributing area of this SP accumulated as surfels are traversed during seeding, no need to reduce locally.
  //Does here a global reduction to determine the total area, enforcing the completion of all reductions before moving
  //forward.
  //DFG-TODO: Will make more sense that discretizer computes this area, so simulator just reads it from the LGI.
  for(auto &ac : m_averaged_contacts) {
    ac.begin_reduce_averaged_area();
  }
  for (auto &ac : m_averaged_contacts) {
    ac.complete_mpi_request();
    ac.compute_contact_area();
  }
  //If averaging is active, average heat flux accumulations require a local reduction followed by a global reduction before it can compute the averaged 
  if (m_is_active) {
    do_local_reductions();
    for (auto &ac : m_averaged_contacts) {
      if (ac.is_invalid()) continue;
      ac.begin_reduce_accumulations();
    }
    for (auto &ac : m_averaged_contacts) {
      if (ac.is_invalid()) continue;
      ac.complete_mpi_request();
      ac.complete_reduce_accumulations();
      ac.compute_averaged_heat_flux();
    }
  }
}

// Called by comm thread
VOID sTHERMAL_AVERAGED_CONTACTS::post_reductions() {
  if((m_recvs_left > 0) || !m_reduction_active_flag.load(std::memory_order_acquire)) {
    return;
  }
  // Loops through all contact groups to do a local reduction, in case that multiple groups contribute to the same
  // averaging. 
  // Note: This task is more appropriate for the compute thread, but that will require some strand with a dependency to all
  //       sampling strands, which is not present now. Since only float operation done in the local reduction is addition,
  //       placed here for simplicity
  do_local_reductions();
  // Initiates global reduction across SPs, resetting the recv and reduction counters 
  m_recvs_left = 0;
  m_next_reduction = 0;
  for (auto &ac : m_averaged_contacts) {
    if (ac.is_averaging()) {
      ac.begin_reduce_accumulations();
      m_recvs_left++;
    }
  }
}

// Called by comm thread
VOID sTHERMAL_AVERAGED_CONTACTS::complete_reductions() {
  if(m_recvs_left == 0) {
    return;
  }
  //otherwise, checks if all averaged contacts have been received
  for (size_t i = m_next_reduction; i < m_averaged_contacts.size(); i++) {
    auto &ac = m_averaged_contacts[i];
    if (ac.is_averaging()) {
      if (ac.is_request_received()) {
        ac.complete_reduce_accumulations();
        m_recvs_left--;
      } else {
        return;
      }
    }
    m_next_reduction++;
  }
  //if reached here, all are have been received and processed, return control to the compute thread
  m_reduction_active_flag.store(false, std::memory_order_release);
}

// Called by time_update_strand
VOID sTHERMAL_AVERAGED_CONTACTS::compute_averages() {
  // Wait for comm thread to mark the reduction completed
  while(m_reduction_active_flag.load(std::memory_order_acquire)) {
    sp_thread_sleep(MPI_SLEEP_LONG);
  }
#if ENABLE_CONSISTENCY_CHECKS
  // If for any reason the counter was not reduced to zero, it implies that some surfel groups were not active in this
  // time step, but were needed for averaging. This will result in the m_reduction_active_flag never set to true, so no
  // local/global reduction is conducted by the comm thread, so averages would be erroneously computed here. Throw an
  // error to catch this issue.
  if (m_count > 0) {
    msg_internal_error("Averaging active but misses processing %d surfel groups", m_count);
  }
#endif
  //Compute averages for those contacts accumulating in this time-step
  for (auto &averaging : m_averaged_contacts) {
    if (averaging.is_averaging()) {
      averaging.compute_averaged_heat_flux();
    }
  }
}

// Checkpointing
// DFG-TODO: Include conduction averaged contact in the checkpointing data streams, so data is properly saved/read
VOID sTHERMAL_AVERAGED_CONTACTS::write_ckpt_data() {
  //writes the header to provide the number of averaged contacts tracked locally
  cLGI_CKPT_AVERAGED_CONTACT_HEADER header;
  header.tag.id = LGI_CKPT_AVERAGED_CONTACTS_TAG;
  header.tag.length = 0; //variable length
  header.num_averaged_contacts = m_averaged_contacts.size();
  write_ckpt_lgi_head(header);
  //Sends info for each of the averaged contact tracked locally
  for (auto &averaging : m_averaged_contacts) {
    averaging.write_ckpt();
  }
}

VOID sTHERMAL_AVERAGED_CONTACTS::read_ckpt_data() {
  //Invoked during initialization if averaging is enabled, but before m_averaged_contacts are filled. 
  //Thus, loads ckpt items into a local vector to be cleared later when loaded into the averaging objects
  cLGI_CKPT_AVERAGED_CONTACT_HEADER header;
  read_lgi_head(header);
  m_ckpt_items.resize(header.num_averaged_contacts);
  for (auto &item : m_ckpt_items) {
    read_lgi(item);
  }
}

VOID sTHERMAL_AVERAGED_CONTACTS::load_ckpt_data() {
  for (auto &averaging : m_averaged_contacts) {
    averaging.load_ckpt(m_ckpt_items[averaging.global_idx()]);
  }
  //Not needed anymore, clears the vector
  std::vector<cLGI_CKPT_AVERAGED_CONTACT_ITEM>().swap(m_ckpt_items);
}
