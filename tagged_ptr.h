#ifndef TAGGED_PTR_H
#define TAGGED_PTR_H

#include <iostream>
#include <array>
#include <cassert>
#include <type_traits>


/*==============================================================================
 * @struct tTAGGED_PTR
 * Generic implementation of a tagged pointer, i.e, a pointer whose trailing bits
 * are hijacked to encode other attributes. How the trailing bits are used is defined
 * by a PTR_TRAITS struct.
 *
 * PTR_TRAITS must define the following public types -
 * 
 * a) ATTRIBUTES - an enum to represent the attributes that will be encoded in the pointer
 * b) PTR - type of raw pointer being tagged
 * c) ALIGNMENT - expected minimum alignment of raw pointer to make sure it can encode
 *                said ATTRIBUTES
 *
 * PTR_TRAITS must define the following constexpr static host device function
 * 
 * num_bits_for_attrib - constexpr static function that returns number of bits per attribute
 *============================================================================*/
template<typename PTR_TRAITS>
class tTAGGED_PTR {

public:
  
  using ATTRIBUTE = typename PTR_TRAITS::ATTRIBUTES;
  using UNDERLYING_PTR = typename PTR_TRAITS::PTR;
  constexpr static auto ALIGNMENT = PTR_TRAITS::ALIGNMENT;

  __HOST__DEVICE__ tTAGGED_PTR(): m_ptr(nullptr){}
  
  __HOST__DEVICE__ tTAGGED_PTR(UNDERLYING_PTR ptr):
    m_ptr(ptr)
  {   

    check_ptr_alignment(ptr);
    
    //Check if pointer alignment can accomodate requested number of attribute bits
    static_assert((0x1 << n_reserved_bits_before_attribute<ATTRIBUTE::N>()) <= ALIGNMENT,
                  "Width of bits reserved for encoding attributes exceeds that "
                  "allowed by alignment constraint");
  }

  //mask representing portion of pointer bits, reserved for encoding attributes
  __HOST__DEVICE__ constexpr static uintptr_t reserved_mask() { return ALIGNMENT - 1; }

  __HOST__DEVICE__ static void check_ptr_alignment(UNDERLYING_PTR ptr) {
    if((((uintptr_t) ptr) & reserved_mask()) != 0) {
      HOST_MSG_ERROR_OR_DEVICE_ASSERT("Input pointer %lu is not %lu byte aligned.\n",
                                      (uintptr_t) ptr, ALIGNMENT);
    }
  } 
  
  // Encode attribute in tagged ptr
  template<ATTRIBUTE attribute>
  __HOST__DEVICE__ void set(unsigned value) {
    constexpr uintptr_t max_value = attribute_max_value<attribute>();
    cassert(value <= max_value);
    constexpr size_t n_bits_before_attribute = n_reserved_bits_before_attribute<attribute>();
    constexpr uintptr_t attribute_mask = max_value << n_bits_before_attribute;
    m_ptr = pointer_or_mask(m_ptr, (uintptr_t) (attribute_mask & (uintptr_t) (value << n_bits_before_attribute)));
  };

  // Decode attribute in tagged ptr
  template<ATTRIBUTE attribute>
  __HOST__DEVICE__ unsigned get() const {
    constexpr auto mask = attribute_mask<attribute>();
    return (((uintptr_t) pointer_and_mask(m_ptr, mask)) >> n_reserved_bits_before_attribute<attribute>());
  };

  __HOST__DEVICE__ UNDERLYING_PTR ptr() const {
    return pointer_and_mask(m_ptr, ~reserved_mask());
  };  

  //Set's internal pointer without modifying reserved bits
  __HOST__DEVICE__ void set_ptr(UNDERLYING_PTR ptr) {
    check_ptr_alignment(ptr);
    m_ptr = pointer_or_mask(ptr, (uintptr_t) pointer_and_mask(m_ptr, reserved_mask()));
  }

  //Get m_ptr in its entirity
  __HOST__DEVICE__ UNDERLYING_PTR masked_ptr() const {
    return m_ptr;
  }

  //Set m_ptr in its entirity
  __HOST__DEVICE__ void set_masked_ptr(UNDERLYING_PTR ptr) {
    m_ptr = ptr;
  }

  __HOST__DEVICE__ tTAGGED_PTR& operator=(std::nullptr_t) {
    m_ptr = pointer_and_mask(m_ptr, reserved_mask());
    return *this;
  }

  __HOST__DEVICE__ bool operator==(std::nullptr_t) {
    return m_ptr == nullptr;
  }

  __HOST__DEVICE__ BOOLEAN operator==(const tTAGGED_PTR& other) {
    return m_ptr == other.m_ptr;
  }

  __HOST__DEVICE__ bool is_null() const {
    return ptr() == nullptr;
  }

  __HOST__DEVICE__ void clear() {
    m_ptr = nullptr;
  }
  
protected:

  __HOST__DEVICE__ constexpr static auto pointer_and_mask(UNDERLYING_PTR p, uintptr_t mask) {
    return ((UNDERLYING_PTR)((uintptr_t)(p) & (mask)));
  }

  __HOST__DEVICE__ constexpr static auto pointer_or_mask(UNDERLYING_PTR p, uintptr_t mask) {
    return ((UNDERLYING_PTR)((uintptr_t)(p) | (mask)));
  }

  //Number of attributes ecnoded in pointer
  __HOST__DEVICE__ constexpr static size_t n_attributes() { return (size_t) ATTRIBUTE::N; }

  //Width of bits occupied before attribute
  template<ATTRIBUTE attribute>
  __HOST__DEVICE__ constexpr static size_t n_reserved_bits_before_attribute() {
    size_t n_bits = 0;
    for (int i = 0; i < (int) attribute; i++) {
      n_bits += PTR_TRAITS::num_bits_for_attrib(ATTRIBUTE(i));
    }
    return n_bits;
  }

  //Max value of attribute based on user specified width of bits
  template<ATTRIBUTE attribute>
  __HOST__DEVICE__ constexpr static uintptr_t attribute_max_value() {
    return (uintptr_t(1) << PTR_TRAITS::num_bits_for_attrib(attribute)) - 1;
  }

  //64 bit wide mask that contains only the high bits that represent the attribute
  template<ATTRIBUTE attribute>
  __HOST__DEVICE__ constexpr static uintptr_t attribute_mask() {
    constexpr uintptr_t max_value = attribute_max_value<attribute>();
    constexpr size_t n_bits_before_attribute = n_reserved_bits_before_attribute<attribute>();
    constexpr uintptr_t attribute_mask = max_value << n_bits_before_attribute;
    return attribute_mask;
  }

  UNDERLYING_PTR m_ptr;
};


#endif //TAGGED_PTR_H
