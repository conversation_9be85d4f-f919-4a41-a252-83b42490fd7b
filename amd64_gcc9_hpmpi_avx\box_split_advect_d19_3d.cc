#include "../common_sp.h"
#include "../box_advect_gen_headers.h"
#include "../gather_advect.h"

template <SPLIT_ADVECT_TEMPLATE_PARAMETERS>
VOID d19_split_advect_3D(SPLIT_ADVECT_STATES_PARAMS) { 

  VOXEL_STATE (*src_states)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_t)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_mc)[ubFLOAT::N_VOXELS] = NULL;
  VOXEL_STATE (*src_states_uds[MAX_N_USER_DEFINED_SCALARS])[ubFLOAT::N_VOXELS] = {NULL};
//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    { //SELF
      {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,2,3};
          constexpr asINT32 dest_voxels[] = {4,5,6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 1;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {4,5,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,4,5};
          constexpr asINT32 dest_voxels[] = {2,3,6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 3;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {2,3,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 4;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,2,4,6};
          constexpr asINT32 dest_voxels[] = {1,3,5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 5;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {1,3,5,7};
          constexpr asINT32 dest_voxels[] = {0,2,4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 1;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,2,3};
          constexpr asINT32 dest_voxels[] = {4,5,6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 0;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {4,5,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 3;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,1,4,5};
          constexpr asINT32 dest_voxels[] = {2,3,6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 2;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {2,3,6,7};
          constexpr asINT32 dest_voxels[] = {0,1,4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 5;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {0,2,4,6};
          constexpr asINT32 dest_voxels[] = {1,3,5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 4;
          constexpr asINT32 n_src_voxels = 4;
          constexpr asINT32 src_voxels[] = {1,3,5,7};
          constexpr asINT32 dest_voxels[] = {0,2,4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
      {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 7;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,3};
          constexpr asINT32 dest_voxels[] = {4,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 6;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,5};
          constexpr asINT32 dest_voxels[] = {2,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 9;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,1};
          constexpr asINT32 dest_voxels[] = {6,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,-1,0}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 8;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {6,7};
          constexpr asINT32 dest_voxels[] = {0,1};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,-1,0}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 11;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {4,6};
          constexpr asINT32 dest_voxels[] = {1,3};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 10;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,3};
          constexpr asINT32 dest_voxels[] = {4,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {-1,0,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 13;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {5,7};
          constexpr asINT32 dest_voxels[] = {0,2};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {-1,0,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {1,0,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 12;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,2};
          constexpr asINT32 dest_voxels[] = {5,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {1,0,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 15;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {2,6};
          constexpr asINT32 dest_voxels[] = {1,5};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 14;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {1,5};
          constexpr asINT32 dest_voxels[] = {2,6};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,-1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,1,1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 17;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {0,4};
          constexpr asINT32 dest_voxels[] = {3,7};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,1,1}; 

//================================================================================
  {
  constexpr sINT16 offsets[3] = {0,-1,-1}; 
  src_states = src_states_t = src_states_mc = NULL;
  if (ADVECT_UDS) {ccDOTIMES(nth_uds, sim.n_user_defined_scalars) {src_states_uds[nth_uds] = NULL;}}
  TAGGED_UBLK src_tagged_ublk = box_access.neighbor_ublk(offsets);
  BOOLEAN is_src_ublk_valid = is_ublk_valid_for_split_advect(src_tagged_ublk, is_timestep_even);
  BOOLEAN is_src_ublk_split = src_tagged_ublk.is_ublk_split();
    if (is_src_ublk_valid) {
      {
          constexpr asINT32 latvec = 16;
          constexpr asINT32 n_src_voxels = 2;
          constexpr asINT32 src_voxels[] = {3,7};
          constexpr asINT32 dest_voxels[] = {0,4};
          if(is_src_ublk_split) {
            APPEND_STATES_FOR_SPLIT_NEIGHBOR_UBLK_ALONG_LATVEC
          } else {
            FILL_PREV_STATES_FOR_NEIGHBOR_UBLK 
            APPEND_STATES_FOR_NEIGHBOR_UBLK_ALONG_LATVEC 
          }
          PUSH_STATES_IF_NEIGHBOR_SWAP_ADVECTS_AND_NOT_SPLIT
      }
    }//is_src_ublk_valid
  }//  constexpr sINT16 offsets[3] = {0,-1,-1}; 

split_advect_factor->reset();
}

