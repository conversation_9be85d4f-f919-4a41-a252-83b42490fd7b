
#include "gtest/gtest.h"
#include "../neighbor_sp.h"


TEST(NeighborSp,Invalid)
{
  cNEIGHBOR_SP nsp(9);

  auto n_states = N_STATES;

  ASSERT_EQ(nsp.nsp(),9);
  ASSERT_EQ(nsp.rank(),STP_PROC_INVALID);
  ASSERT_FALSE(nsp.is_valid());
};

TEST(NeighborSp, Map)
{
  cNEIGHBOR_SP_MAP nsp_map;

  nsp_map.init_rank_map(5);
  
  nsp_map.add_nsp( 4 );
  nsp_map.add_nsp( 1 );
  nsp_map.add_nsp( 3 );
  nsp_map.add_nsp( 2 );

  std::vector<STP_PROC> nsp_ans = { 1 , 3 , 2 , 0 };
  std::vector<STP_PROC> rank_ans = {1 , 2 , 3 , 4 };

  int i=0;
  for(auto nsp : nsp_map) {
    EXPECT_EQ(nsp.nsp(), nsp_ans[i]) << "Index: " << i;
    EXPECT_EQ(nsp.rank(), rank_ans[i]) << "Index: " << i;
    EXPECT_TRUE(nsp.is_valid()) << "Index: " << i;
    i++;
  }
}



