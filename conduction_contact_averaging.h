/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2002 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
 */
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * David Fernandez-Gutierrez, Dassault Systemes
 * Created October 27, 2023
 *--------------------------------------------------------------------------*/

#ifndef _SIMENG_CONDUCTION_AVERAGING_H
#define _SIMENG_CONDUCTION_AVERAGING_H

#include <atomic>
#include SP_H
#include "shob_groups.h"
#include "phys_type_map.h"

// Averaged contact involves two steps: accumulation & averaging. Accumulation done on a group basis per face, while
// averaging done on a global sense. Each step has its own data structure.

/** @brief Class that centralizes data and methods for averaging needed for averaged contact */
class sCONDUCTION_CONTACT_AVERAGING_ITEM {
 public: 
  sCONDUCTION_CONTACT_AVERAGING_ITEM(size_t global_index, asINT32 thermal_pd_index) : m_global_idx(global_index) { 
    m_is_invalid = false;
    m_is_averaging = false;
    m_accumulated[0] = nullptr;
    m_accumulated[1] = nullptr;
    m_averaged_coarsest_scale = sim.num_scales-1; //initialized with the finest scale
    m_pd = &(sim.thermal_surface_physics_descs[thermal_pd_index]);
    m_num_accumulators = 0;
  }
  ~sCONDUCTION_CONTACT_AVERAGING_ITEM() {
    if ((m_accumulated[0] != nullptr) && m_accumulated[0]->is_dangling()) delete m_accumulated[0];
    if ((m_accumulated[1] != nullptr) && m_accumulated[1]->is_dangling()) delete m_accumulated[1];
  }

  //As we allocate accumulations in the contact groups, they also report to the averaging data structure so it stores
  //the pointer to the first one
  VOID maybe_load_accumulation(bool is_primary, sCONTACT_ACCUMULATION *contact_accumulation, SCALE scale) {
    size_t idx = (is_primary) ? 0 : 1;
    if (m_accumulated[idx] == nullptr) {
      m_accumulated[idx] = contact_accumulation;
      m_area[idx] = 0.0;
    }
    if (scale < m_averaged_coarsest_scale) {
      m_averaged_coarsest_scale = scale; 
    }
    m_num_accumulators++;
  }

  VOID maybe_create_dangling_accumulation(size_t idx_avg_local);
  
  //During seeding, when we traverse for the first time the surfels, accumulates the area
  //(maybe there is a simpler way of extracting the face total area?)
  VOID add_area(bool is_primary, sdFLOAT area);

  //Once total area is known, computes contact area based on the area ratio between parts
  VOID compute_contact_area();

  sCONTACT_ACCUMULATION* accumulation_primary() {return m_accumulated[0]; }
  sCONTACT_ACCUMULATION* accumulation_secondary() {return m_accumulated[1]; }
  
  //Return the heat flux needed by each surfel to determine its BC
  sdFLOAT heat_flux_primary() { return m_heat_flux[0]; }
  sdFLOAT heat_flux_secondary() { return m_heat_flux[1]; }

  uINT32 num_accumulators() { return m_num_accumulators; }

  uINT32 global_idx() { return m_global_idx; }
  
  //Averaging state methods
  VOID set_averaging() { m_is_averaging = true; }
  VOID set_averaging_completed() { m_is_averaging = false; }
  bool is_invalid() { return m_is_invalid; }
  bool is_averaging() { return m_is_averaging; }
  
  //When the time update strand resets the accumulations count at the end of the timestep, it checks for each averager
  //if accumulations are needed. We take advantage of this check to update also the state internally.
  bool is_accumulation_needed() { 
    if (is_invalid()) {
      return false;
    } else if (is_averaging()) {
      return true;
    } else if (g_timescale.coarsest_active_scale() <= m_averaged_coarsest_scale) {
      //Accumulation only done during time steps when the coarser scale associated with this face is active
      //and it is not in averaging phase. If so, sets the stage flag to true
      set_averaging();
      return true;
    } 
    return false;
  }
  
  //Once all values are accumulated, do the averaging and compute heat flux
  VOID compute_averaged_heat_flux();
  
  // Parallel processing
  VOID create_communicator(asINT32 new_comm_size, int *new_comm_ranks);
  bool is_request_received();
  VOID complete_mpi_request();
  VOID begin_reduce_averaged_coarsest_scale();
  VOID begin_reduce_averaged_area();
  VOID begin_reduce_accumulations();
  VOID complete_reduce_accumulations();

  //For checkpointing 
  VOID load_ckpt(cLGI_CKPT_AVERAGED_CONTACT_ITEM &item);
  VOID write_ckpt();

 private:
  uINT32 m_global_idx;      //global averaged contact index shared across SPs
  PHYSICS_DESCRIPTOR m_pd;  //thermal physics descriptor providing (possible time-varying) contact resistance
  SCALE m_averaged_coarsest_scale;
  uINT32 m_num_accumulators;//total number of accumulators contributing to this averaged contact
  
  bool m_is_invalid;
  dFLOAT m_area[2];
  dFLOAT m_contact_area;   //reference area used to compute heat exchange between faces
  
  //Pointer to one of the accumulated groups, where all accumulations are centralized
  sCONTACT_ACCUMULATION* m_accumulated[2];

  bool m_is_averaging;   //averaging only done when the coarsest scale is active
  sdFLOAT m_heat_flux[2];   //total heat flux exchanged between faces

  // Parallel processing support
  MPI_Group m_mpi_comm_group;       // For creating communicator; may not be needed permanently
  MPI_Comm m_mpi_communicator;
  dFLOAT m_reduction_buffer[6];    // In-place reduction buffer for the MPI_Iallreduce that sums up the per-CSP contributions
  MPI_Request m_reduction_request;  // For the MPI_Iallreduce
};

/** @brief Main class that centralizes the operations to do reductions and the averaged heat flux */
class sTHERMAL_AVERAGED_CONTACTS {
 public:
  
  sTHERMAL_AVERAGED_CONTACTS() : m_is_enabled(false), 
                                 m_is_enabled_in_this_sp(false), 
                                 m_is_active(false),
                                 m_reduction_active_flag(false),
                                 m_count(0),
                                 m_recvs_left(0),
                                 m_next_reduction(0) {};
  
  sCONDUCTION_CONTACT_AVERAGING_ITEM& operator [] (const size_t i) { return m_averaged_contacts[i]; }
  
  VOID init(const uINT32 size) { 
    m_physics_descriptors.reserve(size);
    m_is_enabled = (size > 0);
  }

  VOID clear_unused() {
    std::vector<asINT32>().swap(m_physics_descriptors);
    std::map<uINT16, uINT32>().swap(m_face_id_to_global_index_map);
  }
  
  VOID add_physics_descriptor(asINT32 pd_index) { 
    m_physics_descriptors.push_back(pd_index);
  }
  
  VOID add_face(asINT32 face_id, asINT32 avg_contact_idx) {
    if (m_face_id_to_global_index_map.find(face_id) != m_face_id_to_global_index_map.end()) {
        msg_internal_error("Face %d involved in more than one averaged contact", face_id);
      } else {
        m_face_id_to_global_index_map.insert({face_id, avg_contact_idx});
      }
  }
  
  VOID add_contact(size_t global_index) { 
    asINT32 thermal_pd_index = m_physics_descriptors[global_index];
    m_averaged_contacts.emplace_back(global_index, thermal_pd_index); 
    m_is_enabled_in_this_sp = true;
  }

  size_t size_global() { return m_physics_descriptors.size(); } //total number of averaged contacts
  size_t size_local() { return m_averaged_contacts.size(); }  //local number of averaged contacts processed by this SP

  bool is_face_averaged(uINT16 face_index) {
    return (m_face_id_to_global_index_map.find(face_index) != m_face_id_to_global_index_map.end());
  }
  
  uINT32 get_global_index(uINT16 face_index) {
    auto it_global_contact_index = m_face_id_to_global_index_map.find(face_index);
    if (it_global_contact_index == m_face_id_to_global_index_map.end()) {
      msg_internal_error("Face %d tagged for averaging but not involved in averaged contact", face_index);
      return 0;
    } else {
      return it_global_contact_index->second;
    }
  }

  bool is_enabled() { return m_is_enabled; }
  bool is_enabled_in_sp() { return m_is_enabled_in_this_sp; }
  bool is_active() { return m_is_active; }
  
  VOID init_count(bool is_full_checkpoint_restore);
  VOID reset_count();
  VOID decrease_count();
  
  VOID reset_accumulations();
  inline VOID do_local_reductions();

  // Multiprocessor support
  VOID create_contact_averaging_communicators();
  VOID complete_initialization();
  VOID seed_averaged_contacts();

  VOID post_reductions();
  VOID complete_reductions();
  VOID compute_averages();
  
  //Checkpointing
  VOID write_ckpt_data();
  VOID read_ckpt_data();
  VOID load_ckpt_data();
  
 private:
  //Main data structure owning the averagers active in this SP
  std::vector<sCONDUCTION_CONTACT_AVERAGING_ITEM> m_averaged_contacts;
  
  //Data structures needed during initialization
  std::vector<asINT32> m_physics_descriptors;
  std::map<uINT16, uINT32> m_face_id_to_global_index_map;
  std::vector<cLGI_CKPT_AVERAGED_CONTACT_ITEM> m_ckpt_items;

  bool m_is_enabled;  //at least one averaged contact is selected in CDI (all SPs contribute to checkpoint)
  bool m_is_enabled_in_this_sp;  //at least one averaged contact is defined in this SP
  bool m_is_active;   //at least one average contact doing accumulation/averaging in this time-step

  uINT32 m_count; //number of pending groups to accumulate in a given time-step
  
  // Flag for csync compute and comm threads concerning reduction, with the following sequence:
  // 1. initialized as false, 
  // 3. checked by the comm thread to know when to post the reductions
  // 2. set by the compute thread when all surfel groups doing accumulations have been processed
  // 4. cleared by the comm thread when global reduction is completed
  // 5. checked by the compute thread during the time-update to wait till all reductions are done before computing
  //    averaged values
  std::atomic<bool> m_reduction_active_flag;

  uINT32 m_next_reduction;
  uINT32 m_recvs_left;
};

// Global variable that centralizes operations used for contact averaging
extern sTHERMAL_AVERAGED_CONTACTS g_thermal_averaged_contacts;

#endif//_SIMENG_CONDUCTION_DATA_H
