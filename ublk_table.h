/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// David Hall, Exa Corporation                      Created Tue, Mar 3, 2009
//----------------------------------------------------------------------------
#ifndef _SIMENG_UBLK_TABLE_H_
#define _SIMENG_UBLK_TABLE_H_

#include "common_sp.h"
#include "ublk.h"
#include VMEM_VECTOR_H

//----------------------------------------------------------------------------
// COORDINATE_ORDER : order elements with given axis changing most quickly
//----------------------------------------------------------------------------
struct COORD_ORDER
{
  COORD_ORDER(int axis) 
  {
    m_3rd_axis = (axis+0)%3;
    m_1st_axis = (axis+1)%3;
    m_2nd_axis = (axis+2)%3;
  }

  template <class UBLK>
  BOOLEAN operator()(UBLK a, UBLK b) 
  {
    if(a->scale() != b->scale()) 
      return (a->scale() < b->scale() );
      
    if(a->location(m_1st_axis) != b->location(m_1st_axis)) 
      return (a->location(m_1st_axis) < b->location(m_1st_axis));

    if(a->location(m_2nd_axis) != b->location(m_2nd_axis)) 
      return (a->location(m_2nd_axis) < b->location(m_2nd_axis));

    return a->location(m_3rd_axis) < b->location(m_3rd_axis);
  }

  asINT32 m_1st_axis;
  asINT32 m_2nd_axis;
  asINT32 m_3rd_axis;
};

//----------------------------------------------------------------------------
// UBLK_TABLE
//----------------------------------------------------------------------------

typedef struct sUBLK_TABLE 
{  
private:
  typedef VMEM_VECTOR< UBLK >::iterator ITERATOR;

  VOID    sort_by_id(); // used to restore ID order after coordinate sort for adv spans

public:
  // resolve_neighbor_ublk_pointers converts neighbor ublk IDs provided in the DGF file 
  // for split ublks into ublk pointers.
  VOID    assign_ublks_to_boxes();
  VOID    update_split_ublk_instance(REALM realm);
  VOID    update_split_advect_factors();
  VOID    append_split_neighbor_info();
  VOID    mark_same_split_instance_neighbors();
  VOID    mark_ublks_presence_in_boxes();
  VOID    complete_ublk_space_varying_attribute_initialization();
  VOID    resolve_neighbor_ublk_pointers();
  VOID    assign_neighbors_and_advection_spans();
  VOID    sort_by_coordinate(asINT32 axis);
  UBLK    ublk_from_id(SHOB_ID id);
  UBLK    ublk(SHOB_ID index);
  SHOB_ID n_ublks() {return m_ublks.size(); }
  const std::vector<UBLK>& ublks() const { return m_ublks; }
  VOID    reserve(SHOB_ID n_ublks) { m_ublks.reserve(n_ublks);}
  VOID    trim()                   { m_ublks.shrink_to_fit();          }

  template <class UBLK>
  VOID add(UBLK ublk) {

    sINT64 id = ublk->id();
    // The ublks must be added to the table in ID order to allow for binary search
    // in ublk_from_id.
    if (id < last_added_ublk_id) {
      msg_internal_error("Ublks not added to ublk table in ID order, ID %ld: last ID %ld", id, last_added_ublk_id );
    }
    last_added_ublk_id = id;
    m_ublks.push_back(ublk);
  }
  
  sINT64  last_added_ublk_id;
  cBOOLEAN dsm_ready; 
  auINT32 n_multipliers;
  sFLOAT *multipliers;
  VOID maybe_allocate_dsm(asINT32 n_dsms) { 
    if(multipliers == NULL){ 
      n_multipliers = n_dsms;
      multipliers = new sFLOAT [n_dsms]; 
    }
  }
  VOID set_dynamics_scalar_multipliers();

protected:
  std::vector<UBLK> m_ublks;
}* UBLK_TABLE;

extern sUBLK_TABLE g_ublk_table[STP_N_REALMS];

inline UBLK ublk_from_id(SHOB_ID id, REALM realm = STP_FLOW_REALM) { return g_ublk_table[realm].ublk_from_id(id); }


VOID print_voxel_states(const char *print_s,
                        int ublk_id,
                        int voxel_id,
                        int timestep,
                        int set=0,
                        std::ostream& os = std::cout);
  
#endif /* _SIMENG_UBLK_TABLE_H_ */
