/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
//----------------------------------------------------------------------------
// Sam Watson, Exa Corporation                      Created Wed, Jan 23, 2013
//----------------------------------------------------------------------------

#ifndef _SIMENG_COMM_COMPRESSION_H_
#define _SIMENG_COMM_COMPRESSION_H_

#include "common_sp.h"
#include "shob.h"
#include THASH_H

// This looks like it should be a typedef, but a MPI_Datatype isn't really a type

#if BUILD_DOUBLE_PRECISION
#define  eMPI_VOXEL_STATE eMPI_dFLOAT
#else
#define  eMPI_VOXEL_STATE eMPI_sFLOAT
#endif


// Neighbor Mask Index. Used for indexing arrays of  neighbor masks and things grouped by
// neighbor mask. 

typedef STP_UBLK_ID NEIGHBOR_MASK_INDEX;

// Comm State Selection Recipe
// The recipe elements are counts of successive voxel-states to be either
// included or excluded, in alternation. It always begins with an include
// count, which may be 0. Since there is no need for a terminating exclude
// count, the number of elements is always odd.

class cCOMM_COMPRESSION_INFO
{

 public:

  STP_UBLK_ID m_num_masks;


  STP_UBLK_NEIGHBOR_MASK *m_masks;     // num_masks   - the array of masks provided by the decomposer
  std::vector<asINT32> *m_recipes;     // num_masks   - per-mask voxel-state-selection recipe
  asINT32 *m_counts;                   // num_masks   - the number of include-counts in a recipe
  asINT32 *m_sendsize;                 // num_masks   - the sum of the include-counts in a recipe; i.e. the number of voxel-states to be sent

  VOID build_recipes();
  VOID fill_by_recipe(sdFLOAT* &out, VOXEL_STATE *in, NEIGHBOR_MASK_INDEX nmi);
  VOID expand_by_recipe(sdFLOAT* &in, VOXEL_STATE *out, NEIGHBOR_MASK_INDEX nmi);
  NEIGHBOR_MASK_INDEX no_mask_nmi() { return (NEIGHBOR_MASK_INDEX) m_num_masks; } // Indexes last location in m_sendsize, which contains 0
  BOOLEAN is_no_mask_nmi(NEIGHBOR_MASK_INDEX nmi) { return (nmi == m_num_masks); }
};

extern cCOMM_COMPRESSION_INFO g_comm_compression_info;

#endif // _SIMENG_COMM_COMPRESSION_H_
