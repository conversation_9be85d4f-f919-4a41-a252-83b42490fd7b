/* ~~~COP<PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2024, 1993-2023 Dassault Systemes Americas Corp.        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744; 7,558,714                                           ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Dassault Systemes Americas Corp. ***
 *** and contains its confidential trade secrets.  Use, examination, copying, ***
 *** transfer and disclosure to others, in whole or in part, are prohibited ***
 *** except with the express prior written consent of Dassault Systemes    ***
 *** Americas Corp.                                                        ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
/*--------------------------------------------------------------------------*
 * Ublk box infrastructure
 *--------------------------------------------------------------------------*/
//----------------------------------------------------------------------------
// Vinit Gupta, Exa Corporation                      Created Fri, Dec 14, 2012
//----------------------------------------------------------------------------
//
#include "box_advect.h"
#include "comm_groups.h"

template<>
TAGGED_UBLK* sUBLK_BOX::assign_ublk(UBLK ublk) {
  asINT32 x_index = ublk->m_box_access.m_box_indices[0];
  asINT32 y_index = ublk->m_box_access.m_box_indices[1];
  asINT32 z_index = ublk->m_box_access.m_box_indices[2];
  asINT32 index   = m_size[2] * (m_size[1] * x_index + y_index) + z_index;
  //if (index >= m_ublks.size())
  //  m_ublks.resize(index+1);
  m_ublks[index].set_ublk(ublk);
  return(&m_ublks[index]);
}

//Allow vectorized advection only if this is an AVX build and simulation
//uses single precision


sUBLK_BOX_FSET g_ublk_box_fset;

// Currently not called
VOID allocate_ublks_in_boxes() {
  DO_UBLK_BOXES(ublk_box) {
    ublk_box->allocate_ublks();
  }
}

// Add an extra empty layer along all axes
// Allocate fill_info array
VOID adjust_size_and_location_of_boxes() {
  DO_UBLK_BOXES(ublk_box) {
    asINT32 ublk_size = ublk_box->ublk_size();
    // Add one Layer to the left
    ublk_box->m_location[0] -= ublk_size;
    ublk_box->m_location[1] -= ublk_size;
    ublk_box->m_location[2] -= ublk_size;

    // Add one Layer to the right. Extra shift because the left-bottom corner
    // is recorded in m_size while parsing ublks.
    ublk_box->m_size[0] -= (ublk_box->m_location[0] - 2*ublk_size);
    ublk_box->m_size[1] -= (ublk_box->m_location[1] - 2*ublk_size);
    ublk_box->m_size[2] -= (ublk_box->m_location[2] - 2*ublk_size);
    
    // If the end is a VR interface, we do not need an extra layer
    // Should code for that.
    ublk_box->m_size[0] = (ublk_box->m_size[0] >> (sim_num_scales() - ublk_box->m_scale));
    ublk_box->m_size[1] = (ublk_box->m_size[1] >> (sim_num_scales() - ublk_box->m_scale));
    ublk_box->m_size[2] = (ublk_box->m_size[2] >> (sim_num_scales() - ublk_box->m_scale));

    if (sim.num_dims == 2) {
      ublk_box->m_size[2]     = 1;
      ublk_box->m_location[2] = 0;
    }

    // The filled bit array is allocated since the m_fill_info in the parent box is NULL
    ublk_box->m_fill_info = new sUBLK_BOX_FILL_INFO_FOR_DIVISION(ublk_box->m_size, NULL);
  }
}

// returns all the ublk boxes that contain a location along with the indices
// in the box
template <typename UBLK_BOX_TYPE>
static asINT32 overlapping_ublk_boxes_after_division(STP_COORD ublk_loc[3],
                                                     STP_SCALE scale,
                                                     REALM realm,
                                                     sINT8 lrf_index,
                                                     uINT8 isolated_domain,
                                                     UBLK_BOX_TYPE overlap_boxes[8],
                                                     uINT16 overlap_indices[24]) {
  asINT32 n_overlapping_boxes = 0;
  UBLK_BOX ublk_box = g_ublk_box_fset.find_next_ublk_box(scale, realm, lrf_index,
                                                         isolated_domain, -1);
  while (ublk_box) {
    if ((ublk_loc[0] >= ublk_box->m_location[0]) &&
        (ublk_loc[1] >= ublk_box->m_location[1]) &&
        (ublk_loc[2] >= ublk_box->m_location[2])) {
      uINT16 indices[3];
      ublk_box->get_indices(ublk_loc, indices);
      if ((indices[0] < ublk_box->m_size[0]) && 
          (indices[1] < ublk_box->m_size[1]) &&
          (indices[2] < ublk_box->m_size[2])) {
        overlap_boxes[n_overlapping_boxes] = ublk_box;
        overlap_indices[n_overlapping_boxes*3    ] = indices[0];
        overlap_indices[n_overlapping_boxes*3 + 1] = indices[1];
        overlap_indices[n_overlapping_boxes*3 + 2] = indices[2];
        n_overlapping_boxes++;
      }
    }
    ublk_box = g_ublk_box_fset.find_next_ublk_box(ublk_box->m_scale,
                                                  ublk_box->m_realm,
                                                  ublk_box->m_lrf_index,
                                                  ublk_box->m_isolated_domain,
                                                  ublk_box->m_box_index);
  }
  return n_overlapping_boxes;
}

#if BUILD_GPU

static asINT32 overlapping_ublk_boxes_after_division(STP_COORD ublk_loc[3],
                                                     STP_SCALE scale,
                                                     sINT8 lrf_index,
                                                     uINT8 isolated_domain,
                                                     sHMBLK::UBLK_BOX overlap_boxes[8],
                                                     uINT16 overlap_indices[24]) {
  asINT32 n_overlapping_boxes = 0;
  for (auto ublk_box: g_mega_boxes) {
    if ((ublk_loc[0] >= ublk_box->m_location[0]) &&
        (ublk_loc[1] >= ublk_box->m_location[1]) &&
        (ublk_loc[2] >= ublk_box->m_location[2])) {
      uINT16 indices[3];
      ublk_box->get_indices(ublk_loc, indices);
      if ((indices[0] < ublk_box->m_size[0]) && 
          (indices[1] < ublk_box->m_size[1]) &&
          (indices[2] < ublk_box->m_size[2])) {
        overlap_boxes[n_overlapping_boxes] = ublk_box;
        overlap_indices[n_overlapping_boxes*3    ] = indices[0];
        overlap_indices[n_overlapping_boxes*3 + 1] = indices[1];
        overlap_indices[n_overlapping_boxes*3 + 2] = indices[2];
        n_overlapping_boxes++;
      }
    }
  }
  return n_overlapping_boxes;
}
#endif

// Assign ublk on the fringe to overlapping tagged_ublk in the adjacent ublk
// box
template <typename UBLK_TYPE, typename UBLK_BOX_TYPE>
static inline 
VOID assign_fringe_ublk(BOOLEAN check_along_axes[3], asINT32 n_overlapping_boxes,
                        UBLK_BOX_TYPE overlap_boxes[8], uINT16 overlap_indices[24],
                        UBLK_TYPE ublk, UBLK_BOX_TYPE ublk_box)
{
  ccDOTIMES(i, n_overlapping_boxes) {
    uINT16 *ov_indices = overlap_indices + 3 * i;
    // create overlap only if the tagged ublk is on the fringe
    // else it is the original tagged ublk
    if ((!check_along_axes[0] || 
         (ov_indices[0] == 0) ||
         (ov_indices[0] == overlap_boxes[i]->m_size[0] - 1)) &&
        (!check_along_axes[1] || 
         (ov_indices[1] == 0) ||
         (ov_indices[1] == overlap_boxes[i]->m_size[1] - 1)) &&
        (!check_along_axes[2] || 
         (ov_indices[2] == 0) ||
         (ov_indices[2] == overlap_boxes[i]->m_size[2] - 1))) {

      auto *tagged_ublk_on_boundary = 
        overlap_boxes[i]->tagged_ublk(ov_indices);

      // If some other box copied this ublk, we do not want to copy it twice
      if (tagged_ublk_on_boundary->is_ublk())
        continue;

      if (ublk->is_split()) {
        cassert(ublk->m_box_access.tagged_ublk()->is_ublk_split());
        tagged_ublk_on_boundary->set_ublk_as_split(ublk->m_box_access.tagged_ublk()->split_ublks());
      } else {
        tagged_ublk_on_boundary->set_ublk_and_type(ublk);
      }
      tagged_ublk_on_boundary->set_ublk_as_last();
    }
  }
}

// First, copies ublk_location to opp_location and then shifts for subset of axes
// based on boolean check_along_axes
static inline VOID opposite_simv_location(BOOLEAN translate_along_axis[3],
                                          STP_COORD ublk_location[3],
                                          STP_COORD opp_location[3],
                                          sINT32 ublk_size,
                                          asINT32 dir[3]) {
  STP_COORD *simv_size = sim.control_record.simv_size;

  opp_location[0] = ublk_location[0];
  opp_location[1] = ublk_location[1];
  opp_location[2] = ublk_location[2];

  // add_or_subtract will be  1 when dir = 0
  // add_or_subtract will be -1 when dir = 1;
  if (translate_along_axis[0]) {
    sINT32 add_or_subtract = (1 - 2 * dir[0]);
    if (dir[0] != 0 && dir[0] != 1) {
      msg_internal_error("dir along X axis %d is outside [0 1].\n", dir[0]);
    }
    opp_location[0] = (ublk_location[0] + add_or_subtract * simv_size[0]);
  }

  if (translate_along_axis[1]) {
    sINT32 add_or_subtract = (1 - 2 * dir[1]);
    if (dir[1] != 0 && dir[1] != 1) {
      msg_internal_error("dir along Y axis %d is outside [0 1].\n", dir[1]);
    }
    opp_location[1] = (ublk_location[1] + add_or_subtract * simv_size[1]);
  }

  if (translate_along_axis[2]) {
    sINT32 add_or_subtract = (1 - 2 * dir[2]);
    if (dir[2] != 0 && dir[2] != 1) {
      msg_internal_error("dir along Z axis %d is outside [0 1].\n", dir[2]);
    }
    opp_location[2] = (ublk_location[2] + add_or_subtract * simv_size[2]);
  }
}

// Penultimate ublks (index =1 and index = m_size - 2) on the boundary of a
// ublk_box are visited and overlapping ublk boxes at these sites are found.
// These ublks are tagged in the overlapping ublk boxes with appropriate
// indices. The tagged ublks should always lie on the boundary of the
// overlapping ublk boxes. If a ublk box lies on the simvol boundary,
// overlapping ublk boxes are found for the ublks on the simvol boundary.
//
VOID create_overlap_on_boxes_boundary() {

  DO_UBLK_BOXES(ublk_box) {
    STP_COORD *simv_size = sim.control_record.simv_size;
    STP_COORD ublk_size = scale_to_voxel_size(ublk_box->m_scale) * 2;
    BOOLEAN   box_abuts_simvol_boundary[3] = {FALSE, FALSE, FALSE};
    ccDOTIMES (axis, sim.num_dims) {
      STP_COORD min_location = ublk_box->m_location[axis];
      STP_COORD max_location = ublk_box->m_location[axis] +
                               ublk_box->m_size[axis] * ublk_size;
      if (((min_location + ublk_size) == 0) ||
          ((max_location - ublk_size) == simv_size[axis])) {
        box_abuts_simvol_boundary[axis] = TRUE;
      }
    }

    ccDOTIMES (axis, sim.num_dims) {
      asINT32 c0, c1, c2;
      uINT32 box_start[3] = {0, 0, 0};
      uINT32 box_end[3] = {1, 1, 1};
      if (sim.num_dims == 2) {
        c0 = axis; c1 = axis ^ 1; c2 = 2;
        box_end[c0] = ublk_box->m_size[c0];
        box_end[c1] = ublk_box->m_size[c1];
        box_start[2] = 0; box_end[2] = 1;
      } else if (sim.num_dims == 3) {
        c0 = axis; c1 = csys_dir1[axis]; c2 = csys_dir2[axis];
        box_end[c0] = ublk_box->m_size[c0];
        box_end[c1] = ublk_box->m_size[c1];
        box_end[c2] = ublk_box->m_size[c2];
      } else {
        c0 = STP_NO_AXIS;
        c1 = STP_NO_AXIS;
        c2 = STP_NO_AXIS;
        msg_internal_error("Dimensions not equal to 2 or 3");
      }

      // Overlap should only be verified along c0 axis
      BOOLEAN check_along_axes[3] = {FALSE, FALSE, FALSE};
      check_along_axes[c0] = TRUE;

      // To create overlap along axis. 
      // tagged ublk at index m_size[c0]-1 in the current ublk box contains
      // ublk at index 1 along c0 in the adjacent ublk_box and tagged ublk
      // at index 0 along c0 in the adjacent ublk_box contains ublk at
      // m_size[c0] - 2 in the current ublk box.
      //
      uINT16 indices[3] = {0, 0, 0};
      ccDOTIMES (dir0, 2) { // Two end points along c0
        indices[c0] = (ublk_box->m_size[c0] - 3 ) * dir0 + 1;
        for (indices[c1] = box_start[c1]; indices[c1] < box_end[c1]; indices[c1]++) {
          for (indices[c2] = box_start[c2]; indices[c2] < box_end[c2]; indices[c2]++) {

            TAGGED_UBLK *tagged_ublk = ublk_box->tagged_ublk(indices);
            // Since the extents of the loop for indices c1 and c2 run from 0 
            // to m_size, the last tagged ublks should be ignored.
            if (!tagged_ublk->is_ublk() && !tagged_ublk->is_ublk_last())
              continue;

            if (tagged_ublk->is_ublk_split()) {
              sUBLK_VECTOR *split_ublks = tagged_ublk->split_ublks();
              tagged_ublk = &(split_ublks->m_tagged_ublks[0]);
            }
            if (tagged_ublk->is_ublk_scale_interface()) {
              msg_internal_error("Do not handle scale interface at a boundary");
            }
            UBLK ublk = tagged_ublk->ublk();

            UBLK_BOX overlap_boxes[8];
            uINT16 overlap_indices[24];
            STP_COORD ublk_location[3];
            ublk_location[0] = ublk->location(0);
            ublk_location[1] = ublk->location(1);
            ublk_location[2] = ublk->location(2);

            // returns all the overlapping boxes (including the current ublk
            // box) at the ublk location along with indices in the corresponding
            // overlapping boxes
            asINT32 n_overlapping_boxes =
              overlapping_ublk_boxes_after_division(ublk_location, ublk_box->m_scale,
                                                    ublk_box->m_realm, ublk_box->m_lrf_index,
                                                    ublk_box->m_isolated_domain,
                                                    // outputs
                                                    overlap_boxes, overlap_indices);

            // since the current ublk box is also returned
            if (n_overlapping_boxes > 1) {
              assign_fringe_ublk(check_along_axes, n_overlapping_boxes,
                                 overlap_boxes, overlap_indices, ublk, ublk_box);
            }
             
            // only boxes that abut the simvol boundary along axis c0
            if (box_abuts_simvol_boundary[c0]) {

              // Periodic along axis

              if (ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(c0)) ||
                  ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(c0))) {

                STP_COORD opp_location_along_axis[3];
                asINT32 opp_dir[3] = {-1, -1, -1};
                opp_dir[c0] = dir0;
                opposite_simv_location(check_along_axes, ublk_location,
                                       opp_location_along_axis, ublk_size, opp_dir);

                // find overlapping boxes for opposite location
                n_overlapping_boxes =
                  overlapping_ublk_boxes_after_division(opp_location_along_axis,
                                                        ublk_box->m_scale,
                                                        ublk_box->m_realm,
                                                        ublk_box->m_lrf_index,
                                                        ublk_box->m_isolated_domain,
                                                        // outputs
                                                        overlap_boxes, overlap_indices);
                assign_fringe_ublk(check_along_axes, n_overlapping_boxes,
                                   overlap_boxes, overlap_indices, ublk, ublk_box);
              }
            }
          }
        }
      }

      check_along_axes[c1] = TRUE;
      // For corner ublks: 
      // (1, 1, k), (I-2, 1  , k), (1, J-2, k  ), (I-2, J-2, k  )
      // (1, j, 1), (I-2, j  , 1), (1, j  , K-2), (I-2, j  , K-2)
      // (i, 1, 1), (i  , J-2, 1), (i, 1  , K-2), (i  , J-2, K-2)

      // In 2D, one pass accounts for all four corners, so the second pass is
      // omitted
      if ((box_abuts_simvol_boundary[c0] && box_abuts_simvol_boundary[c1]) &&
          ((sim.num_dims == 3) || ((sim.num_dims == 2) && (c0 == 0)))) {


        ccDOTIMES (dir0, 2) {
          indices[c0] = (ublk_box->m_size[c0] - 3 ) * dir0 + 1;
          ccDOTIMES (dir1, 2) {
            indices[c1] = (ublk_box->m_size[c1] - 3 ) * dir1 + 1;
            for (indices[c2] = box_start[c2]; indices[c2] < box_end[c2]; indices[c2]++) {
              // Periodic along axis
              TAGGED_UBLK *tagged_ublk = ublk_box->tagged_ublk(indices);
              if (!tagged_ublk->is_ublk())
                continue;
              if (tagged_ublk->is_ublk_split()) {
                sUBLK_VECTOR *split_ublks = tagged_ublk->split_ublks();
                tagged_ublk = &(split_ublks->m_tagged_ublks[0]);
              }
              UBLK ublk = tagged_ublk->ublk();

              STP_COORD ublk_location[3];
              ublk_location[0] = ublk->location(0);
              ublk_location[1] = ublk->location(1);
              ublk_location[2] = ublk->location(2);
  
  
              if ((ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(c0)) ||
                   ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(c0))) &&
                  (ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(c1)) ||
                   ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(c1))))
              {
                STP_COORD opp_location_along_plane[3];
                asINT32 opp_dir[3] = {-1, -1, -1};
                opp_dir[c0] = dir0;
                opp_dir[c1] = dir1;
                opposite_simv_location(check_along_axes, ublk_location,
                                       opp_location_along_plane, ublk_size, opp_dir);

                UBLK_BOX overlap_boxes[8];
                uINT16 overlap_indices[24];
                asINT32 n_overlapping_boxes =
                  overlapping_ublk_boxes_after_division(opp_location_along_plane,
                                                        ublk_box->m_scale,
                                                        ublk_box->m_realm,
                                                        ublk_box->m_lrf_index,
                                                        ublk_box->m_isolated_domain,
                                                        overlap_boxes, overlap_indices);
                assign_fringe_ublk(check_along_axes, n_overlapping_boxes,
                                   overlap_boxes, overlap_indices, ublk, ublk_box);
              }
            }
          }
        }
      }
    }

    // Checking if eight corners in three dimensions lie on the simvol
    // boundary
    if ((sim.num_dims == 3) && 
        box_abuts_simvol_boundary[0] && 
        box_abuts_simvol_boundary[1] &&
        box_abuts_simvol_boundary[2]) {

      BOOLEAN check_along_axes[3] = {TRUE, TRUE, TRUE};
      uINT16 indices[3] = {0, 0, 0};
      ccDOTIMES (dir0, 2) {
        indices[0] = (ublk_box->m_size[0] - 3 ) * dir0 + 1;
        ccDOTIMES (dir1, 2) {
          indices[1] = (ublk_box->m_size[1] - 3 ) * dir1 + 1;
          ccDOTIMES (dir2, 2) {
            indices[2] = (ublk_box->m_size[2] - 3 ) * dir2 + 1;
            TAGGED_UBLK *tagged_ublk = ublk_box->tagged_ublk(indices);
            if (!tagged_ublk->is_ublk())
              continue;
            if (tagged_ublk->is_ublk_split()) {
              sUBLK_VECTOR *split_ublks = tagged_ublk->split_ublks();
              tagged_ublk = &(split_ublks->m_tagged_ublks[0]);
            }
            UBLK ublk = tagged_ublk->ublk();

            STP_COORD ublk_location[3];
            ublk_location[0] = ublk->location(0);
            ublk_location[1] = ublk->location(1);
            ublk_location[2] = ublk->location(2);

            if ((ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(0)) ||
                 ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(0))) &&
                (ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(1)) ||
                 ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(1))) &&
                (ublk->does_ublk_abut_simvol(stp_axis_to_pos_face(2)) ||
                 ublk->does_ublk_abut_simvol(stp_axis_to_neg_face(2))))
            {
              STP_COORD opp_location[3];
              asINT32 opp_dir[3] = {-1, -1, -1};
              opp_dir[0] = dir0;
              opp_dir[1] = dir1;
              opp_dir[2] = dir2;
              opposite_simv_location(check_along_axes, ublk_location, opp_location,
                                     ublk_size, opp_dir);

              UBLK_BOX overlap_boxes[8];
              uINT16 overlap_indices[24];
              asINT32 n_overlapping_boxes =
                overlapping_ublk_boxes_after_division(opp_location, ublk_box->m_scale,
                                                      ublk_box->m_realm,
                                                      ublk_box->m_lrf_index,
                                                      ublk_box->m_isolated_domain,
                                                      overlap_boxes, overlap_indices);
              assign_fringe_ublk(check_along_axes, n_overlapping_boxes,
                                 overlap_boxes, overlap_indices, ublk, ublk_box);
            }
          }
        }
      }
    }
  }
}

#if !BUILD_D39_LATTICE // XDU: Span advect for D39 to be implemented

#endif

// Currently Not Called
UBLK_BOX find_ublk_box_after_division(STP_COORD ublk_loc[3], 
                                      STP_SCALE scale, REALM realm, sINT8 lrf_index,
                                      uINT8     isolated_domain) {

  UBLK_BOX ublk_box = g_ublk_box_fset.find_next_ublk_box(scale, realm, lrf_index,
                                                         isolated_domain, -1);
  while (ublk_box) {
    if ((ublk_loc[0] >= ublk_box->m_location[0]) &&
        (ublk_loc[1] >= ublk_box->m_location[1]) &&
        (ublk_loc[2] >= ublk_box->m_location[2])) {
      uINT16 indices[3];
      ublk_box->get_indices(ublk_loc, indices);
      if ((indices[0] < ublk_box->m_size[0]) && 
          (indices[1] < ublk_box->m_size[1]) &&
          (indices[2] < ublk_box->m_size[2])) {
        return ublk_box;
      }
    }
    ublk_box = g_ublk_box_fset.find_next_ublk_box(ublk_box->m_scale,
                                                  ublk_box->m_realm,
                                                  ublk_box->m_lrf_index,
                                                  ublk_box->m_isolated_domain,
                                                  ublk_box->m_box_index);
  }
  msg_internal_error("Did not find a matching ublk box at location %d %d %d", ublk_loc[0],
                                ublk_loc[1], ublk_loc[2]);
  return NULL;
}

/*template<>
VOID tUBLK_BOX::allocate_ublks() {
  uINT32 n_ublks = m_size[0] * m_size[1] * m_size[2];
  m_offsets[0] = m_size[1] * m_size[2];
  m_offsets[1] = m_size[2];
  m_offsets[2] = 1;
  m_ublks.reserve(n_ublks);
  m_ublks.resize(n_ublks);
}*/

template<>
VOID sUBLK_BOX::divide_ublk_box() {
  if (m_fill_info->is_division_required(m_size)) {

    asINT32 split_dir = -1, split_location = -1;
    // split_location is in box coordinates i.e between 1 and m_size.
    // m_start_indices should be added to obtain the split_location in a global
    // sense.
    find_min_empty_planes(split_dir, split_location);
    if (split_location == 1) {
      msg_internal_error("no ublks found at first location along axis %d\n", split_dir);
    }
    //printf("For box %d with scale %d: axis %d location %d \n", m_box_index, scale,
    //       split_dir, split_location);
    if (split_dir != -1) {
      split_ublk_box(split_dir, split_location);
    } else {
      //split_dir = -1;
      find_largest_occupancy_splitting_plane(split_dir, split_location);
      if (split_dir > -1) {
        split_ublk_box((split_dir >> 1), split_location);
      } else {
        msg_internal_error("Ublk Box %d of size [%d %d %d] was not split",
                           m_box_index, m_size[0], m_size[1], m_size[2]);
      }
    }
  } else {
    allocate_ublks();
    //printf("There is no need to divide this box dims %d %d %d filled %d\n", m_size[0],
    //       m_size[1], m_size[2], m_fill_info->m_n_filled_slots);
    //printf("location %d %d %d \n", m_location[0], m_location[1], m_location[2]);
    // free fill_info memory 
    // first box contains the filled bit array and should not be deleted
    // until the division of boxes for a scale is done
    if (m_box_index != 0) {
      //delete((void *) m_fill_info, __FILE__, __LINE__);
      ::delete m_fill_info;
    }
  }
}

// remove empty layers on the boundary of a box perpendicular to the split_axis
// between start_indices and end_indices (not included) and update
// start_indices and end_indices.
template<>
VOID sUBLK_BOX::remove_empty_layers(asINT32 split_axis, uINT16 start_indices[3],
                                    uINT16 end_indices[3]) {
  uINT16 indices[3] = {0};
  if (sim.num_dims == 3) {
    asINT32 c0 = split_axis, c1 = csys_dir1[c0], c2 = csys_dir2[c0];
    uINT16 new_start_index = 0;
    for (indices[c1] = start_indices[c1]; indices[c1] < end_indices[c1]; indices[c1]++) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        for (indices[c2] = start_indices[c2]; indices[c2] < end_indices[c2]; indices[c2]++) {
          if (m_fill_info->is_ublk_present_in_slot(indices)) {
            new_start_index = indices[c1];
            break;
          }
        }
        if (new_start_index > 0)
          break;
      }
      if (new_start_index > 0)
        break;
    }
    if (new_start_index > 0) // The whole box is empty
      start_indices[c1] = new_start_index;

    uINT16 new_end_index = 0;
    for (indices[c1] =  end_indices[c1]-1; indices[c1] >start_indices[c1]-1; indices[c1]--) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        for (indices[c2] = start_indices[c2]; indices[c2] < end_indices[c2]; indices[c2]++) {
          if (m_fill_info->is_ublk_present_in_slot(indices)) {
            new_end_index = indices[c1] + 1;
            break;
          }
        }
        if (new_end_index > 0) 
          break;
      }
      if (new_end_index > 0) 
        break;
    }
    if (new_end_index > 0) 
      end_indices[c1] = new_end_index;

    new_start_index = 0;
    for (indices[c2] = start_indices[c2]; indices[c2] < end_indices[c2]; indices[c2]++) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        for (indices[c1] = start_indices[c1]; indices[c1] < end_indices[c1]; indices[c1]++) {
          if (m_fill_info->is_ublk_present_in_slot(indices)) {
            new_start_index = indices[c2];
            break;
          }
        }
        if (new_start_index > 0)
          break;
      }
      if (new_start_index > 0)
        break;
    }
    if (new_start_index > 0) // The whole box is empty
      start_indices[c2] = new_start_index;

    new_end_index = 0;
    for (indices[c2] =  end_indices[c2]-1; indices[c2] >start_indices[c2]-1; indices[c2]--) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        for (indices[c1] = start_indices[c1]; indices[c1] < end_indices[c1]; indices[c1]++) {
          if (m_fill_info->is_ublk_present_in_slot(indices)) {
            new_end_index = indices[c2] + 1;
            break;
          }
        }
        if (new_end_index > 0) 
          break;
      }
      if (new_end_index > 0) 
        break;
    }
    if (new_end_index > 0) 
      end_indices[c2] = new_end_index;

  } else {
    asINT32 c0 = split_axis, c1 = c0^1, c2 = 2;
    uINT16 new_start_index = 0;
    // Perhaps can read 8 bytes at a time for the inner most dimension?
    for (indices[c1] = start_indices[c1]; indices[c1] < end_indices[c1]; indices[c1]++) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        if (m_fill_info->is_ublk_present_in_slot(indices)) {
          new_start_index = indices[c1];
          break;
        }
      }
      if (new_start_index > 0)
        break;
    }
    if (new_start_index > 0)
      start_indices[c1] = new_start_index;

    uINT16 new_end_index = 0;
    for (indices[c1] = end_indices[c1]-1; indices[c1] > start_indices[c1]-1; indices[c1]--) {
      for (indices[c0] = start_indices[c0]; indices[c0] < end_indices[c0]; indices[c0]++) {
        if (m_fill_info->is_ublk_present_in_slot(indices)) {
          new_end_index = indices[c1] + 1;
          break;
        }
      }
      if (new_end_index > 0)
        break;
    }
    if (new_end_index > 0)
      end_indices[c1] = new_end_index;

    start_indices[2] = 1;
    end_indices[2]   = 0;
  }
}

// fills m_n_filled_slots_per_plane using the filled bitmap which represents
// number of slots filled in a plane
VOID sUBLK_BOX_FILL_INFO_FOR_DIVISION::
     adjust_filled_ublks_per_plane(uINT16 start_index[3], uINT16 end_index[3],
                                   STP_COORD box_size[3], BOOLEAN reset) {

  if (reset) {
    m_n_filled_slots = 0;
    m_n_filled_slots_per_plane[0].resize(box_size[0]);
    m_n_filled_slots_per_plane[1].resize(box_size[1]);
    m_n_filled_slots_per_plane[2].resize(box_size[2]);
    uINT16 max_box_index = MAX(box_size[0], box_size[1]);
    max_box_index = MAX(max_box_index , box_size[2]);
    ccDOTIMES(index, max_box_index) {
      if (index < box_size[0]) {
        m_n_filled_slots_per_plane[0][index] = 0;
      }
      if (index < box_size[1]) {
        m_n_filled_slots_per_plane[1][index] = 0;
      }
      if (index < box_size[2]) {
        m_n_filled_slots_per_plane[2][index] = 0;
      }
    }
  }
  uINT16 indices[3] = {0, 0, 0}, new_box_indices[3] = {1, 1, 1};

  // Perhaps can read 8 bytes at a time for the inner most dimension?
  // The loop are arranged such that the inner most dimension is the inner
  // most loop
  // When we start using paged_bit_map, we could walk the indices
  // differently to obtain better cache behavior
  if (sim.num_dims == 2) {
    new_box_indices[2] = 0;
    for (indices[0] = start_index[0];
         indices[0] < end_index[0]; indices[0]++, new_box_indices[0]++) {
      for (indices[1] = start_index[1], new_box_indices[1] = 1;
           indices[1] < end_index[1]; indices[1]++, new_box_indices[1]++) {
        if (is_ublk_present_in_slot(indices)) {
          increment_ublks_filled_count(new_box_indices);
        }
      }
    }
  } else {
    for (indices[0] = start_index[0];
         indices[0] < end_index[0]; indices[0]++, new_box_indices[0]++) {
      for (indices[1] = start_index[1], new_box_indices[1] = 1;
           indices[1] < end_index[1]; indices[1]++, new_box_indices[1]++) {
        for (indices[2] = start_index[2], new_box_indices[2] = 1;
             indices[2] < end_index[2]; indices[2]++, new_box_indices[2]++) {
          if (is_ublk_present_in_slot(indices)) {
            increment_ublks_filled_count(new_box_indices);
          }
        }
      }
    }
  }
}

// split index is in local box coordinates and should be displaced by
// m_start_indices to obain the global split inde
template<>
VOID sUBLK_BOX::split_ublk_box(asINT32 split_dir, asINT32 split_index) {

  asINT32 new_box_index = 
    g_ublk_box_fset.find_availble_box_index(m_scale, m_realm, m_lrf_index, m_isolated_domain);
  UBLK_BOX ublk_box = 
    g_ublk_box_fset.create_ublk_box(m_scale, m_realm, m_lrf_index, m_isolated_domain,
                                    new_box_index);

  ublk_box->m_location[0] = m_location[0];
  ublk_box->m_location[1] = m_location[1];
  ublk_box->m_location[2] = m_location[2];
  ublk_box->m_size[0]     = m_size[0];
  ublk_box->m_size[1]     = m_size[1];
  ublk_box->m_size[2]     = m_size[2];


  // m_start_indices are based on common filled bit array and represent the
  // indices of left, bottom corner of a box
  uINT16 start_indices[3];
  // indices are increased by 1, since the extra overlap layer should not be
  // included to make any assessment of further splitting of the boxes
  start_indices[0] = m_start_indices[0] + 1;
  start_indices[1] = m_start_indices[1] + 1;
  start_indices[2] = m_start_indices[2] + 1;

  // Useful region is bounded by
  // indices < end indices && indices >= start_indices
  uINT16 end_indices[3];
  end_indices[0] = m_start_indices[0] + m_size[0] - 1;
  end_indices[1] = m_start_indices[1] + m_size[1] - 1;
  end_indices[2] = m_start_indices[2] + m_size[2] - 1;

  // Peel off empty layers in the split direction
  start_indices[split_dir] = split_index;
  uINT16 index;
  // based on local index
  for (index = split_index; index < m_size[split_dir]-1; index++) {
    if (!m_fill_info->is_plane_empty(split_dir, index)) {
      start_indices[split_dir] = index;
      break;
    }
  }
  end_indices[split_dir] = m_size[split_dir] - 1;
  for (index = m_size[split_dir] - 2; index > split_index-1; index--) {
    if (!m_fill_info->is_plane_empty(split_dir, index)) {
      end_indices[split_dir] = index + 1;
      break;
    }
  }
  asINT32 ublk_cube_size = this->ublk_size();

  // pass in global indices since fill_bit_array is common to all boxes of a
  // particular scale and remove empty layers uses the common filled bit array
  // to make that assessment.
  start_indices[split_dir] += m_start_indices[split_dir];
  end_indices[split_dir]   += m_start_indices[split_dir];

  // Peel off empty layers normal to the split direction
  remove_empty_layers(split_dir, start_indices, end_indices);

  ublk_box->m_location[0] += (start_indices[0] - m_start_indices[0] - 1) * ublk_cube_size;
  ublk_box->m_location[1] += (start_indices[1] - m_start_indices[1] - 1) * ublk_cube_size;
  ublk_box->m_location[2] += (start_indices[2] - m_start_indices[2] - 1) * ublk_cube_size;
  ublk_box->m_size[0] = end_indices[0] - start_indices[0] + 2;
  ublk_box->m_size[1] = end_indices[1] - start_indices[1] + 2;
  ublk_box->m_size[2] = end_indices[2] - start_indices[2] + 2;

  ublk_box->m_start_indices[0] = start_indices[0] - 1;
  ublk_box->m_start_indices[1] = start_indices[1] - 1;
  ublk_box->m_start_indices[2] = start_indices[2] - 1;
  ublk_box->m_fill_info = new sUBLK_BOX_FILL_INFO_FOR_DIVISION(ublk_box->m_size,
                                                                m_fill_info);
  ublk_box->m_fill_info->adjust_filled_ublks_per_plane(start_indices, end_indices,
                                                       ublk_box->m_size, FALSE);

  /*printf("Created ublk_box %d scale %d size %d %d %d\n",ublk_box->m_box_index,
         ublk_box->m_scale, ublk_box->m_size[0], ublk_box->m_size[1],
         ublk_box->m_size[2] );
  printf("location %d %d %d start %d %d %d\n", ublk_box->m_location[0],
         ublk_box->m_location[1], ublk_box->m_location[2],
         ublk_box->m_start_indices[0],
         ublk_box->m_start_indices[1], ublk_box->m_start_indices[2]);*/

  // Peel off empty layers in the split direction
  start_indices[0] = m_start_indices[0] + 1;
  start_indices[1] = m_start_indices[1] + 1;
  start_indices[2] = m_start_indices[2] + 1;

  end_indices[0] = m_start_indices[0] + m_size[0] - 1;
  end_indices[1] = m_start_indices[1] + m_size[1] - 1;
  end_indices[2] = m_start_indices[2] + m_size[2] - 1;

  start_indices[split_dir] = 1;
  for (index = 1; index < split_index-1; index++) {
    if (!m_fill_info->is_plane_empty(split_dir, index)) {
      start_indices[split_dir] = index;
      break;
    }
  }

  end_indices[split_dir] = split_index + 1;
  for (index = split_index-1; index > 0; index--) {
    if (!m_fill_info->is_plane_empty(split_dir, index)) {
      end_indices[split_dir] = index + 1;
      break;
    }
  }
  // Peel off empty layers normal to the split direction
  // pass in global indices since fill_bit_array is common to all boxes of a
  // particular scale
  start_indices[split_dir] += m_start_indices[split_dir];
  end_indices[split_dir]   += m_start_indices[split_dir];
  remove_empty_layers(split_dir, start_indices, end_indices);

  m_location[0] += (start_indices[0] - m_start_indices[0] - 1) * ublk_cube_size;
  m_location[1] += (start_indices[1] - m_start_indices[1] - 1) * ublk_cube_size;
  m_location[2] += (start_indices[2] - m_start_indices[2] - 1) * ublk_cube_size;

  uINT16 new_m_size[3];
  new_m_size[0] = end_indices[0] - start_indices[0] + 2;
  new_m_size[1] = end_indices[1] - start_indices[1] + 2;
  new_m_size[2] = end_indices[2] - start_indices[2] + 2;

  m_size[0] = new_m_size[0]; m_size[1] = new_m_size[1]; m_size[2] = new_m_size[2];

  m_start_indices[0] = start_indices[0] - 1;
  m_start_indices[1] = start_indices[1] - 1;
  m_start_indices[2] = start_indices[2] - 1;

  m_fill_info->adjust_filled_ublks_per_plane(start_indices, end_indices, m_size, TRUE);
  /*printf("Shrunk  ublk_box %d scale %d size %d %d %d\n", m_box_index, scale,
         m_size[0], m_size[1], m_size[2]);
  printf("location %d %d %d start %d %d %d\n", m_location[0], m_location[1], m_location[2],
         m_start_indices[0], m_start_indices[1], m_start_indices[2]);*/
  divide_ublk_box();
}

template<>
VOID sUBLK_BOX::find_largest_occupancy_splitting_plane(asINT32 &split_dir,
                                                       asINT32 &split_location) {

  uINT16 indices[3] = {0};
  dFLOAT max_occupancy = 0.0;
  // not used any more since it causes left bias of distribution
  dFLOAT mean_occupancy[3] = {0.0, 0.0, 0.0};
  dFLOAT occupancy_ratio =  m_fill_info->m_n_filled_slots /
                            ((dFLOAT)m_fill_info->n_ublk_slots(m_size));
  ccDOTIMES (a, sim.num_dims) {
    asINT32 n_filled_slots_from_left  = 0;
    asINT32 n_filled_slots_from_right = 0;
    asINT32 n_total_slots  = 0;
    asINT32 n_slots_in_plane = (m_size[csys_dir1[a]] - 2) * (m_size[csys_dir2[a]] - 2); 
    if (sim.num_dims == 2) {
      n_slots_in_plane = (m_size[a^1] - 2); 
    }
    if (m_size[a] < m_fill_info->MIN_BOX_DIMENSION) {
      break;
    }
    // The first and last layers are overlap layers and should not be counted
    // towards occupancy
    for (indices[a] = 1; indices[a] < m_size[a]-1; indices[a]++) {
      n_total_slots  += n_slots_in_plane;
      n_filled_slots_from_left  += m_fill_info->m_n_filled_slots_per_plane[a][indices[a]];

      dFLOAT occupancy = n_filled_slots_from_left/((dFLOAT) n_total_slots);
      // Count occupancy only beyond minimum box size
      if (indices[a] >= m_fill_info->MIN_BOX_DIMENSION) {
        mean_occupancy[a] += occupancy;
        if (occupancy >= max_occupancy) {
          max_occupancy = occupancy;
          split_dir = a << 1;
          split_location = indices[a] + 1;
        }
      }

      n_filled_slots_from_right += m_fill_info->m_n_filled_slots_per_plane[a][m_size[a] - indices[a] - 1];
      occupancy = n_filled_slots_from_right/((dFLOAT) n_total_slots);
      if (indices[a] >= m_fill_info->MIN_BOX_DIMENSION) {
        if (occupancy >= max_occupancy) {
          max_occupancy = occupancy;
          split_dir = (a << 1) + 1;
          split_location = m_size[a] - indices[a] - 1;
        }
      }
    }
    mean_occupancy[a] /= ((dFLOAT)(m_size[a] - m_fill_info->MIN_BOX_DIMENSION));
    //printf("occupancy mean %e left biased mean %e max %e max_dir %d\n",
    //       occupancy_ratio, mean_occupancy[a], max_occupancy, (split_dir >> 1));
  }

  if (!m_fill_info->is_maximum_occupancy_sensible((max_occupancy/occupancy_ratio))) {
    // Bisect the box
    split_location = m_size[split_dir >> 1] >> 1;
  }
}

template<>
VOID sUBLK_BOX::find_min_empty_planes(asINT32 &axis, asINT32 &location) {

  uINT16 indices[3] = {0, 0, 0};
  ccDOTIMES (a, sim.num_dims) {
    // Since there is a one layer overhang on the boundary, so 
    // to get any memory saving, there should be atleast 3 consecutive empty layers
    for (indices[a] = 1; indices[a] < m_size[a]-4; indices[a]++) {
      if (m_fill_info->is_plane_empty(a, indices[a]    ) &&
          m_fill_info->is_plane_empty(a, indices[a] + 1) &&
          m_fill_info->is_plane_empty(a, indices[a] + 2)) {
        location = indices[a];
        axis = a;
        return;
      }
    }
  }
}

VOID divide_ublk_boxes() 
{
  ccDOTIMES(realm, STP_N_REALMS) {
    g_ublk_table[realm].mark_ublks_presence_in_boxes();
  }
  DO_SCALES_FINE_TO_COARSE(scale) {
    DO_UBLK_BOXES_OF_SCALE(ublk_box, scale) {
      // printf("Attempting to divide BOX %d scale %d realm %d\n",ublk_box->m_box_index, ublk_box->m_scale, ublk_box->m_realm);
      ublk_box->divide_ublk_box();
    }
    /*DO_UBLK_BOXES_OF_SCALE(ublk_box, scale) {
      asINT32 ublk_size = scale_to_voxel_size(scale) * 2;
      printf("BOX %d min %d %d %d\n", ublk_box->m_box_index,
             ublk_box->m_location[0], ublk_box->m_location[1],
             ublk_box->m_location[2]);
      printf("BOX %d max %d %d %d\n", ublk_box->m_box_index,
             ublk_box->m_location[0] + ublk_box->m_size[0] * ublk_size,
             ublk_box->m_location[1] + ublk_box->m_size[1] * ublk_size,
             ublk_box->m_location[2] + ublk_box->m_size[2] * ublk_size);
      // should delete the filled bit array from the first box
    }*/
  }
}

UBLK_BOX sUBLK_BOX_FSET::find_next_ublk_box(asINT32 scale, REALM realm, asINT32 lrf_index,
                                            asINT32 isolated_domain, asINT32 box_index)
{
  sUBLK_BOX signature; 

  signature.m_scale           = scale;
  signature.m_realm           = realm;
  signature.m_lrf_index       = lrf_index;
  signature.m_isolated_domain = isolated_domain;
  signature.m_box_index       = box_index + 1;
  
  UBLK_BOX box = find_group( &signature );
  return box;
}

asINT32 sUBLK_BOX_FSET::find_availble_box_index(asINT32 scale, REALM realm, asINT32 lrf_index, asINT32 isolated_domain)
{
  sUBLK_BOX signature; 

  signature.m_scale           = scale;
  signature.m_realm           = realm;
  signature.m_lrf_index       = lrf_index;
  signature.m_isolated_domain = isolated_domain;
  signature.m_box_index       = 0;
  
  UBLK_BOX box = find_group( &signature );
  signature.m_box_index++;
  while (box) {
    box = find_group( &signature );
    signature.m_box_index++;
  }
  return (signature.m_box_index-1);
}

UBLK_BOX sUBLK_BOX_FSET::create_ublk_box(asINT32 scale, REALM realm, asINT32 lrf_index, asINT32 isolated_domain,
                                         asINT32 box_index) {

  // Create a group with matching sort criteria
  static sUBLK_BOX signature; 

  signature.m_scale           = scale;
  signature.m_realm           = realm;
  signature.m_lrf_index       = lrf_index;
  signature.m_isolated_domain = isolated_domain;
  signature.m_box_index       = box_index;

  // Get a group of this type if it exists. If not, add a new one to the fset.
  UBLK_BOX box = find_group( &signature );

  if(box == NULL) {
    box                    = xnew sUBLK_BOX;
    box->m_scale           = scale;
    box->m_realm           = realm;
    box->m_lrf_index       = lrf_index;
    box->m_isolated_domain = isolated_domain;
    box->m_box_index       = box_index;
    box->init();
    add_group(box);
  }

  return box;
}

static VOID diagonal_state_to_cartesian_states(STP_STATE_INDEX latvec_0,
                                               STP_STATE_INDEX &latvec_1,
                                               STP_STATE_INDEX &latvec_2) 
{
  if (latvec_0 < N_CARTESIAN_SPEED1_LATVECS) {
    return;
  }
  if (state_vx(latvec_0) * state_vy(latvec_0) != 0) {
    if (state_vx(latvec_0)>0) 
      latvec_1 = 0;
    else if (state_vx(latvec_0) < 0) 
      latvec_1 = 1;
    if (state_vy(latvec_0)>0) 
      latvec_2 = 2;
    else if (state_vy(latvec_0) < 0) 
      latvec_2 = 3;
  }
  else if (state_vy(latvec_0) * state_vz(latvec_0) != 0) {
    if (state_vy(latvec_0)>0) 
      latvec_1 = 2;
    else if (state_vy(latvec_0) < 0) 
      latvec_1 = 3;
    if (state_vz(latvec_0)>0) 
      latvec_2 = 4;
    else if (state_vz(latvec_0) < 0) 
      latvec_2 = 5;
  } 
  else if (state_vz(latvec_0) * state_vx(latvec_0) != 0) {
    if (state_vz(latvec_0)>0) 
      latvec_1 = 4;
    else if (state_vz(latvec_0) < 0) 
      latvec_1 = 5;
    if (state_vx(latvec_0)>0) 
      latvec_2 = 0;
    else if (state_vx(latvec_0) < 0) 
      latvec_2 = 1;
  }
}

asINT32 tagged_neighbor_from_split_ublk_site(TAGGED_UBLK tagged_neighbor,
                                             uINT8 advect_from_split_mask,
                                             asINT32 voxel, asINT32 latvec,
                                             const sSPLIT_ADVECT_INFO *split_advect_info,
                                             asINT32 dest_ublk_instance,
                                             TAGGED_SPLIT_FACTOR split_factors) {
  asINT32 nInstances = 0;
#if DEBUG
  if (tagged_neighbor.is_ublk_split())
#endif
  {
    if ((advect_from_split_mask >> voxel) & 1) {
      //sift through the split advect factors
      asINT32 last_advect_factor = 0;
      asINT32 first_advect_factor = split_advect_info->m_tot_advect_scale_factors[voxel];
      if (voxel == ubFLOAT::N_VOXELS -1) {
        last_advect_factor = split_advect_info->m_total_advect_scale_factors;
      } else {
        last_advect_factor = split_advect_info->m_tot_advect_scale_factors[voxel+1];
      }
      BOOLEAN is_neighbor_found = false;
      for (asINT32 n = first_advect_factor; n < last_advect_factor; n++) {
        const sADVECT_SCALE_FACTOR_INFO *adv_factor_info = 
                        &(split_advect_info->m_advect_scale_factor_info[n]);
        asINT32 ublk_instance = adv_factor_info->src_ublk_instance;
        // state_parity is used for neighbor, because split advect
        // factor points from source to dest and ublk is the destination
        if (latvec == state_parity(adv_factor_info->latvec)) {
          sUBLK_VECTOR *split_ublks = tagged_neighbor.split_ublks();
          is_neighbor_found = true;
#if DEBUG && 0
          if (split_ublks->num_split_ublks() <= ublk_instance) {
            msg_internal_error("split instance %d is greater than number of splits %d at location %d %d %d along latvec %d voxel %d",
                               ublk_instance, split_ublks->num_split_ublks(),
                               split_ublks->m_tagged_ublks[0].ublk()->location(0),
                               split_ublks->m_tagged_ublks[0].ublk()->location(1),
                               split_ublks->m_tagged_ublks[0].ublk()->location(2), latvec, voxel);
          }
#endif
          split_factors[nInstances].tagged_neighbor = split_ublks->m_tagged_ublks[ublk_instance];
          split_factors[nInstances].neighbor_split_factor = adv_factor_info->advect_scale_factor;
          nInstances++;
        }
      }
      // If the latvec is not registered in the split advect factors, then
      // neighboring ublks must have the same ublk instance index
      if (!is_neighbor_found) {
        sUBLK_VECTOR  *split_ublks = tagged_neighbor.split_ublks();
#if DEBUG && 0
        if (split_ublks->num_split_ublks() <= dest_ublk_instance) {
          msg_internal_error("split instance %d is greater than number of splits %d  at location %d %d %d, along latvec %d voxel %d",
                             dest_ublk_instance, split_ublks->num_split_ublks(),
                             split_ublks->m_tagged_ublks[0].ublk()->location(0),
                             split_ublks->m_tagged_ublks[0].ublk()->location(1),
                             split_ublks->m_tagged_ublks[0].ublk()->location(2), latvec, voxel);

        }
#endif
        split_factors[nInstances].tagged_neighbor = split_ublks->m_tagged_ublks[dest_ublk_instance];
        split_factors[nInstances].neighbor_split_factor = 1.0;
        nInstances++;
      }
    } else {
      sUBLK_VECTOR  *split_ublks = tagged_neighbor.split_ublks();
      cassert(dest_ublk_instance < split_ublks->num_split_ublks());
      split_factors[nInstances].tagged_neighbor = split_ublks->m_tagged_ublks[dest_ublk_instance];
      split_factors[nInstances].neighbor_split_factor = 1.0;
      nInstances++;
    }
#if DEBUG
  } else if (tagged_neighbor.is_ublk()) {
    msg_internal_error("ublk should be split");
    // This may not be required, we can take care of this in the calling
    // routine.
    split_factors[nInstances].tagged_neighbor = tagged_neighbor;
    split_factors[nInstances].neighbor_split_factor = 1.0;
    nInstances++;
#endif
  }
  return nInstances;
}

template<>
VOID sUBLK_BOX::get_location(uINT16 indices[3], STP_COORD ublk_location[3]) const {
  if ( indices[0] >= m_size[0] ) {
    msg_internal_error("X box index %d ublk index %d", m_size[0], indices[0]);
  }
  if ( indices[1] >= m_size[1] ) {
    msg_internal_error("Y box index %d ublk index %d", m_size[1], indices[1]);
  }
  if ( indices[2] >= m_size[2] ) {
    msg_internal_error("Z box index %d ublk index %d", m_size[2], indices[2]);
  }
  ublk_location[0] = (indices[0] << (sim_num_scales() - m_scale)) + m_location[0];
  ublk_location[1] = (indices[1] << (sim_num_scales() - m_scale)) + m_location[1];
  ublk_location[2] = (indices[2] << (sim_num_scales() - m_scale)) + m_location[2];
}


enum class Verbose
{
  On,
  Off
};


static void print_ublk(std::ostream& os, UBLK ublk, Verbose v = Verbose::On)
{
  if ( v == Verbose::Off ) {
    os << "UBLK " << ublk->id() << '\n';
    return;
  }

  os << "UBLK " << ublk->id() << '\n';
  os << "scale " << ublk->scale() << '\n';
  os << "location " << ublk->location(0) << ' ' << ublk->location(1) << ' ' << ublk->location(2) << '\n';
  os << "is_vr_coarse " << (int) ublk->is_vr_coarse() << '\n';
  os << "is_vr_fine " << (int) ublk->is_vr_fine() << '\n';
  os << "is_vr_coarse_with_fine_children " << (int) ublk->is_vr_coarse_with_fine_children() << '\n';
  os << "vr_topology mask 1 " << ublk->vr_topology().face_abuts_different_scale_mask() << '\n';
  os << "vr_topology mask 2 " << ublk->vr_topology().face_abuts_finer_scale_mask() << '\n';
  os << "m_are_any_neighbors_different_scale " << (int) ublk->m_are_any_neighbors_different_scale.get() << '\n';
  os << "m_are_neighbors_split: " << (int) ublk->m_are_any_neighbors_split.get() << '\n';
  os << "fluid_connect_masks:\n";
  for(int i=0; i<8; i++) {
    os << ublk->m_fluid_connect_masks[i].get() << '\n';
  }
  os << "path_connect_masks:\n";
  for(int i=0; i<8; i++) {
    os << ublk->m_path_connect_masks[i].get() << '\n';
  }
  os << "neighbor_2_diff_scale_mask:\n";
  for(int l=0; l<6; l++) {
    os << (int) ublk->neighbor_2_diff_scale_mask[l].get() << '\n';
  }
  if ( ublk->is_vr_coarse() ) {
    auto * vr_coarse_data = ublk->vr_coarse_data();
    os << "fine ublks\n";
    for(int l=0; l<8; l++) {
      if ( UBLK u = vr_coarse_data->vr_fine_ublk(l).ublk() ) {
        os << l << " UBLK " << u->id() << '\n';
      }
      else {
        os << l << " NULL\n";
      }
    }
    if ( VR_CONFIG vr_config = vr_coarse_data->vr_config() ) {
      os << "vr config fine mask" << (int) vr_config->fine_mask << '\n';
    }
  }
  if ( ublk->is_vr_fine() ) {
    auto * vr_fine_data = ublk->vr_fine_data();
    if ( UBLK cu = vr_fine_data->vr_coarse_ublk().ublk() ) {
      os << "coarse UBLK " << vr_fine_data->vr_coarse_ublk().ublk()->id() << '\n';
    }
    os << "m_even_s2v_weights_masks\n";
    for(int l=0; l<N_CONNECT_MASK_BITS; l++) {
      os << l << ' ' << (int) vr_fine_data->m_even_s2v_weights_mask[l].get() << '\n';
    }
    if ( VR_CONFIG vr_config = vr_fine_data->vr_config() ) {
      os << "vr config fine mask" << (int) vr_config->fine_mask << '\n';
    }
  }
}

static void print_split_ublk(std::ostream& os, sUBLK_VECTOR* splits, Verbose v = Verbose::On)
{
  os << "SPLIT " << splits->num_split_ublks() << '\n';
  TAGGED_UBLK *t = splits->tagged_ublks();
  for(int m=0; m< splits->num_split_ublks(); m++) {
    print_ublk(os, t->ublk(), v);
    t++;
  }

}

template<>
void sUBLK_BOX::print_contents(std::ostream& os) const
{
#define PRINTLN(name) os << #name << ' ' << name << '\n'
#define PRINTLN_N(name,N) os << #name;\
  for(int i=0; i<N; i++) {\
    os << ' ' << name[i];\
  }

  os << "====\n";
  PRINTLN(m_box_index);
  PRINTLN(m_scale);
  PRINTLN_N(m_location,3);
  PRINTLN_N(m_size,3);
  os << "====\n";

  for(uINT16 i=0; i<m_size[0]; i++) {
    for(uINT16 j=0; j<m_size[1]; j++) {
      for(uINT16 k=0; k<m_size[2]; k++) {
        uINT16 indices[3] = {i,j,k};
        STP_COORD ublk_location[3];
        TAGGED_UBLK t_ublk = stagged_ublk(indices);

        if ( t_ublk.is_null() ) {
          continue;
        }
        else if ( t_ublk.is_ublk_scale_interface() ) {
          get_location(indices,ublk_location);
          os << "SI ";
          PRINTLN_N(ublk_location,3);
          sSCALE_BOX_INTERFACE * si = t_ublk.interface();

          for(int l=0; l<8; l++) {
            if (si->m_fine_ublks[l].is_null() ) {
              os << l << " NULL\n";
            }
            else if (si->m_fine_ublks[l].is_ublk_split()) {
              os << l << " ";
              print_split_ublk(os, si->m_fine_ublks[l].split_ublks(), Verbose::Off);
            }
            else if ( si->m_fine_ublks[l].is_ublk_scale_interface()) {
              os << l << " SI\n";
            }
            else {
              os << l << " ";
              print_ublk(os, si->m_fine_ublks[l].ublk(), Verbose::Off);
            }
          }
        }
        else if ( t_ublk.is_ublk_split() ) {
          print_split_ublk(os, t_ublk.split_ublks());
        }
        else if ( t_ublk.is_ublk() ) {
          print_ublk(os, t_ublk.ublk());
        }
      }
    }
  }
  os << ">>>>\n";
#undef PRINTLN
}

#if !BUILD_GPU
void print_all_ublk_box_contents(std::ostream& os)
{
  DO_UBLK_BOXES(ublk_box) {
    ublk_box->print_contents(os);
  }
}
#endif
