/* ~~~CO<PERSON><PERSON><PERSON><PERSON>E~~~+ boxcomment("simeng.copyright", "78") */ 
/*****************************************************************************
 *** PowerFLOW Simulator Simulation Process                                ***
 ***                                                                       ***
 *** Copyright (C) 2012, 1993-2011 Exa Corporation.                        ***
 *** All Rights Reserved.                                                  ***
 *** US Pat 5,377,129; 5,432,718; 5,594,671; 5,606,517;                    ***
 ***        5,640,335; 5,848,260; 5,910,902; 5,953,239;                    ***
 ***        6,089,744                                                      ***
 *** UK FR DE Pat 0 538 415                                                ***
 ***                                                                       ***
 *** This computer program is the property of Exa Corporation and contains ***
 *** its confidential trade secrets.  Use, examination, copying, transfer  ***
 *** and disclosure to others, in whole or in part, are prohibited except  ***
 *** with the express prior written consent of Exa Corporation.            ***
 ***                                                                       ***
 *** RESTRICTED RIGHTS LEGEND                                              ***
 ***                                                                       ***
 *** Use, duplication, or disclosure is subject to restrictions stated in  ***
 *** Contract No. DABT63-93-C-0010 with Exa Corporation.                   ***
 *****************************************************************************
*/
/* ~~~COPYWRITE~~~- boxcomment("simeng.copyright", "78") */ 
#ifndef SP_SHOB_MANAGER_H_
#define SP_SHOB_MANAGER_H_

#include "common_sp.h"
#include "surfel_dyn_sp.h"

//----------------------------------------------------------------------------
// Parsing DGF ublk descriptors
//----------------------------------------------------------------------------
VOID parse_ublk_desc(STP_REALM realm,
                     DGF_UBLK_BASE_DESC descriptor,
                     uINT8 ublk_decomp_flags,
                     STP_PROC home_sp, 
                     std::vector<cDGF_GHOST_INFO> &ghost_info,
                     LGI_STREAM istream);
//----------------------------------------------------------------------------
// Parsing DGF surfel descriptors
//----------------------------------------------------------------------------
VOID parse_surfel_desc(DGF_SURFEL_DESC surfel_record, 
                       STP_REALM realm, 
                       STP_PROC home_sp, 
                       RP_PROC rp, 
                       RP_PROC backside_rp, 
                       uINT16 ghost_flags, 
                       std::vector<cDGF_SURFEL_PROC_GHOST> &ghost_sps);

VOID parse_bsurfel_desc(DGF_BSURFEL_DESC bsurfel_desc, DGF_BSURFEL_GEOM bsurfel_geom);

VOID compute_scalar_connect_masks(UBLK ublk);

VOID sanity_check_paired_contact_surfels();
VOID add_dangling_flow_wsurfels_to_groups();
#endif




